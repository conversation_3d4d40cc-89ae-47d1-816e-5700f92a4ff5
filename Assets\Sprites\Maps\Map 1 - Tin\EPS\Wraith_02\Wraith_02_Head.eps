%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Head.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 260 260
%%HiResBoundingBox: 0 0 260 260
%%CropBox: 0 0 260 260
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 1 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -260 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 260 li
260 260 li
260 0 li
cp
clp
242 147 mo
231.948 125.46 230.331 92.8706 224 81 cv
216 66 213 31 104 24 cv
83.0063 22.6518 97 66 83 77 cv
31.27 117.645 41.3335 172.031 58.9971 209.485 cv
61.1968 205.748 66.374 197.027 67.0518 196.896 cv
67.6128 196.789 74.7744 201.59 77.3227 204.28 cv
79.8003 200.618 85.8921 198.62 88.3774 202.278 cv
86.9492 174.703 90.7354 193.638 118 198 cv
139.359 201.418 140.999 204.673 185.317 207.297 cv
188 190 198.304 207.645 198.274 208.745 cv
198.615 208.756 200.129 207.688 202.147 206.621 cv
206.214 204.468 212.326 202.316 215 209 cv
223 209 256 177 242 147 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
.425849 .581689 .653254 .557549 cmyk
f
224.76 196.471 mo
225.941 191.68 226.992 186.84 228 182 cv
229 177 240 172 236 158 cv
220.785 104.745 222.565 83.1791 205.934 59.7963 cv
211.908 66.1881 214.682 72.5236 216.956 77.7192 cv
217.834 79.7251 218.664 81.6191 219.588 83.353 cv
222.108 88.0781 224.01 97.7178 226.023 107.923 cv
228.691 121.453 231.717 136.788 237.469 149.114 cv
243.249 161.5 239.424 173.811 235.196 181.956 cv
232.14 187.847 228.314 192.775 224.76 196.471 cv
.314244 .311894 .33814 .100404 cmyk
f
59.6856 198.633 mo
45.272 163.256 40.3872 116.84 86.0889 80.9317 cv
94.0459 74.6797 94.8306 62.3227 95.5889 50.3726 cv
95.9893 44.0678 96.4028 37.5483 97.937 33.3247 cv
99.2111 29.818 100.723 28.9651 102.97 28.9651 cv
103.199 28.9651 103.435 28.9739 103.68 28.9897 cv
106.931 29.1985 110.079 29.4313 113.137 29.6875 cv
118.104 35.9699 120.336 42.7449 117 49 cv
107 68 83 91 91 116 cv
106 100 121 80 144 79 cv
156 79 161 82 171 90 cv
172 91 172 110 171 112 cv
169.759 112.677 168.55 113.328 167.373 113.959 cv
157.988 107.936 142.588 97.8549 133.365 97.8549 cv
131.368 97.8549 129.66 98.3277 128.366 99.417 cv
104.161 119.787 83.4927 156.748 83.5041 176.376 cv
83.5058 179.357 83.9844 181.938 85 184 cv
85.1157 184.235 85.2794 184.476 85.4871 184.721 cv
83.5257 186.819 83.0413 190.156 83.1079 195.421 cv
81.0698 195.662 79.0258 196.429 77.2251 197.584 cv
76.1274 196.734 75.0717 195.976 74.3066 195.441 cv
70.5006 192.781 68.7958 191.88 67.1386 191.88 cv
66.7977 191.88 66.4587 191.919 66.104 191.987 cv
64.2734 192.341 63.5323 192.483 59.6856 198.633 cv
.49543 .611963 .672236 .68423 cmyk
f
209.023 199.498 mo
210.236 199.498 211.465 199.672 212.679 200.083 cv
214 200.531 215.612 201.376 217.078 202.967 cv
221.436 200.289 229.604 192.733 235.196 181.956 cv
239.424 173.811 243.249 161.5 237.469 149.114 cv
231.717 136.788 228.691 121.453 226.023 107.923 cv
224.01 97.7178 222.108 88.0781 219.588 83.353 cv
218.664 81.6191 217.834 79.7251 216.956 77.7192 cv
209.973 61.7661 198.283 35.0654 103.68 28.9897 cv
101.034 28.8189 99.3408 29.461 97.937 33.3247 cv
96.4028 37.5483 95.9893 44.0678 95.5889 50.3726 cv
94.8306 62.3227 94.0459 74.6797 86.0889 80.9317 cv
40.3872 116.84 45.272 163.256 59.6856 198.633 cv
63.5323 192.483 64.2734 192.341 66.104 191.987 cv
68.1831 191.584 69.7178 192.234 74.3066 195.441 cv
75.0717 195.976 76.1274 196.734 77.2251 197.584 cv
79.0258 196.429 81.0698 195.662 83.1079 195.421 cv
83.0278 189.096 83.7432 185.553 86.8598 183.598 cv
90.5176 181.306 94.1099 183.277 98.271 185.562 cv
102.59 187.932 109.117 191.515 118.79 193.063 cv
125.2 194.089 129.856 195.097 134.359 196.071 cv
144.333 198.23 153.779 200.276 181.435 202.048 cv
183.704 195.323 187.894 194.538 190.447 194.77 cv
194.189 195.107 197.827 198.478 200.267 201.962 cv
202.696 200.722 205.805 199.498 209.023 199.498 cv
193.276 208.612 mo
193.276 208.618 li
193.276 208.612 li
58.998 214.485 mo
58.7691 214.48 li
56.9175 214.396 55.2651 213.294 54.4746 211.618 cv
39.2378 179.31 22.2817 118.349 79.9111 73.0683 cv
84.3496 69.581 85.0185 59.0396 85.6089 49.7393 cv
86.0742 42.4077 86.5137 35.4829 88.5381 29.9101 cv
92.0341 20.2881 99.0625 18.6699 104.32 19.0102 cv
148.073 21.8198 179.132 29.2393 199.272 41.6919 cv
216.862 52.5683 222.432 65.29 226.117 73.709 cv
226.95 75.6113 227.669 77.2549 228.412 78.647 cv
231.632 84.6841 233.579 94.5566 235.834 105.988 cv
238.393 118.959 241.293 133.661 246.531 144.886 cv
253.186 159.145 251.223 175.834 241.004 191.878 cv
234.079 202.752 222.459 214 215 214 cv
212.955 214 211.117 212.755 210.357 210.857 cv
210.071 210.143 209.73 209.643 209.467 209.554 cv
208.726 209.298 206.891 209.559 202.39 212.235 cv
200.865 213.141 199.748 213.794 198.113 213.742 cv
195.858 213.67 194 212.115 193.444 210.041 cv
192.952 208.981 191.76 207.224 190.672 205.988 cv
190.533 206.531 190.39 207.213 190.259 208.063 cv
189.864 210.611 187.595 212.449 185.021 212.288 cv
153.338 210.412 143.091 208.193 132.243 205.845 cv
127.871 204.898 123.351 203.92 117.21 202.937 cv
105.825 201.115 98.084 196.866 93.459 194.327 cv
93.3477 194.267 93.2344 194.204 93.1197 194.141 cv
93.1065 196.914 93.291 200.477 93.3706 202.02 cv
93.4868 204.26 92.0967 206.303 89.9702 207.018 cv
87.94 207.704 85.7129 207.012 84.418 205.332 cv
83.7212 205.344 82.2036 205.989 81.4638 207.082 cv
80.6148 208.338 79.2417 209.14 77.731 209.264 cv
76.2276 209.383 74.7348 208.819 73.6928 207.719 cv
72.627 206.594 70.3681 204.881 68.4311 203.533 cv
67.3472 205.255 65.7222 207.916 63.3062 212.021 cv
62.4053 213.553 60.7627 214.485 58.998 214.485 cv
.757687 .679133 .626856 .856168 cmyk
f
195 104 mo
185.992 95.3179 177 112 169 115 cv
158 108 135.996 92.9951 128.366 99.417 cv
100.486 122.88 77.2968 168.356 85 184 cv
87.3501 188.772 109.39 195.734 121.546 199.137 cv
140 232 180 223 193.676 202.531 cv
193.934 202.353 215.151 186.566 216 185 cv
225.535 167.396 219.162 127.289 195 104 cv
.325261 .332006 .500359 .152377 cmyk
f
195.044 104.042 mo
195.042 104.041 195.041 104.039 195.039 104.038 cv
195.041 104.039 195.042 104.041 195.044 104.042 cv
.49543 .611963 .672236 .68423 cmyk
f
157.221 220.883 mo
143.887 220.883 130.295 214.717 121.546 199.137 cv
121.546 199.137 li
109.39 195.734 87.3501 188.772 85 184 cv
83.9844 181.938 83.5058 179.357 83.5041 176.376 cv
83.4927 156.748 104.161 119.787 128.366 99.417 cv
129.66 98.3277 131.368 97.8549 133.365 97.8549 cv
143.144 97.8549 159.866 109.187 169 115 cv
175.378 112.608 182.385 101.522 189.529 101.522 cv
191.346 101.522 193.174 102.24 195 104 cv
195.013 104.013 195.026 104.025 195.039 104.038 cv
195.041 104.039 195.042 104.041 195.044 104.042 cv
198.485 107.365 201.553 111.036 204.276 114.928 cv
197.759 126.134 189.254 136.117 176.436 136.117 cv
175.641 136.117 174.829 136.079 174 136 cv
161 135 149 121 140 120 cv
123 120 115 146 107 163 cv
99 179 128 178 131 184 cv
138.495 197.117 147.088 210.233 157.395 220.883 cv
157.337 220.883 157.279 220.883 157.221 220.883 cv
.4477 .420539 .564233 .309117 cmyk
f
211.857 182.133 mo
209.012 184.814 193.115 196.755 190.827 198.422 cv
190.312 198.779 189.867 199.231 189.519 199.754 cv
182.84 209.748 168.95 216.342 155.757 215.86 cv
146.857 215.516 134.453 211.91 125.906 196.688 cv
125.256 195.531 124.172 194.68 122.894 194.321 cv
103.822 188.983 91.5029 183.818 89.3872 181.583 cv
83.6572 168.995 104.708 125.862 131.584 103.243 cv
131.675 103.166 133.901 101.438 143.664 105.927 cv
150.546 109.092 158.199 114.009 163.787 117.599 cv
164.68 118.172 165.526 118.716 166.315 119.218 cv
167.641 120.062 169.285 120.232 170.756 119.682 cv
174.334 118.34 177.57 115.395 180.7 112.547 cv
188.205 105.717 189.631 105.769 191.53 107.6 cv
214.417 129.66 219.473 167.071 211.857 182.133 cv
cp
198.47 100.4 mo
189.106 91.3745 179.632 99.9985 173.97 105.151 cv
172.338 106.636 170.668 108.155 169.208 109.195 cv
169.192 109.185 li
151.202 97.6279 134.294 87.8921 125.146 95.5913 cv
97.5928 118.78 71.0005 166.889 80.5142 186.209 cv
81.5317 188.275 84.2007 193.7 118.251 203.401 cv
126.74 217.302 139.847 225.252 155.371 225.853 cv
155.986 225.876 156.602 225.888 157.218 225.888 cv
173.037 225.888 188.848 218.098 197.313 206.067 cv
201.275 203.177 218.854 190.229 220.396 187.382 cv
231.233 167.375 224.016 125.022 198.47 100.4 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
69.3599 142.805 mo
69.0488 147.489 66.3398 151.123 63.3091 150.922 cv
60.2783 150.721 58.0732 146.761 58.3838 142.076 cv
58.6943 137.392 61.4033 133.758 64.4341 133.959 cv
67.4653 134.16 69.6704 138.121 69.3599 142.805 cv
cp
f
79.4292 126.982 mo
79.1748 130.814 76.9585 133.788 74.4785 133.623 cv
71.9985 133.458 70.1943 130.218 70.4487 126.386 cv
70.7031 122.554 72.9194 119.58 75.3994 119.745 cv
77.8789 119.909 79.6831 123.149 79.4292 126.982 cv
cp
f
77.7441 152.38 mo
77.5464 155.361 75.8228 157.674 73.894 157.546 cv
71.9653 157.418 70.562 154.898 70.7598 151.917 cv
70.9575 148.937 72.6812 146.624 74.6099 146.751 cv
76.5386 146.879 77.9419 149.399 77.7441 152.38 cv
cp
f
171.59 118.091 mo
171.487 118.09 li
168.727 118.035 166.533 115.751 166.589 112.991 cv
167.174 83.9702 160.848 63.7778 147.25 51.2593 cv
135.206 40.1719 118.673 35.8984 102.685 31.7656 cv
100.129 31.104 li
97.4565 30.4092 95.853 27.6797 96.5474 25.0068 cv
97.2417 22.334 99.9727 20.7319 102.645 21.4253 cv
105.188 22.084 li
122.369 26.5249 140.135 31.1172 154.023 43.9023 cv
169.852 58.4746 177.232 81.1396 176.587 113.192 cv
176.532 115.918 174.305 118.091 171.59 118.091 cv
cp
f
142.97 54.2861 mo
141.372 54.2861 139.802 53.522 138.834 52.1025 cv
137.279 49.8208 137.867 46.7104 140.148 45.1548 cv
146.458 40.8521 154.859 39.7441 162.067 42.2632 cv
164.675 43.1738 166.05 46.0259 165.139 48.6328 cv
164.229 51.2388 161.378 52.6143 158.769 51.7036 cv
154.497 50.2109 149.521 50.8672 145.782 53.4165 cv
144.92 54.0044 143.94 54.2861 142.97 54.2861 cv
cp
f
157.036 75.7109 mo
155.522 75.7109 154.026 75.0264 153.043 73.7256 cv
151.378 71.5229 151.814 68.3872 154.017 66.7217 cv
160.109 62.1162 168.445 60.5986 175.769 62.7637 cv
178.417 63.5459 179.929 66.3271 179.146 68.9756 cv
178.364 71.6235 175.583 73.1372 172.935 72.3535 cv
168.596 71.0698 163.656 71.9692 160.047 74.6992 cv
159.145 75.3813 158.085 75.7109 157.036 75.7109 cv
cp
f
179.412 99.2949 mo
178.624 99.2949 177.824 99.1084 177.079 98.7144 cv
173.08 96.5996 168.06 96.5054 163.981 98.4658 cv
161.494 99.6636 158.505 98.6157 157.309 96.127 cv
156.112 93.6387 157.159 90.6509 159.647 89.4541 cv
166.529 86.1431 175.001 86.3042 181.753 89.8735 cv
184.194 91.1641 185.127 94.189 183.837 96.6304 cv
182.939 98.3267 181.206 99.2949 179.412 99.2949 cv
cp
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Head.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+4:Q8=o$_s8!m1"(YRQ.q;_i2B!h1d$k0m,mR:j(LiZd-_=kQe\N&imR:G.d7r$(]Yh`JRN
%m`d[Dhu*KKpAb*MNOsHKMS$]Cp\</1^A#BKro1o+pHESGqu"(X0AaAAq7F,^?[Vb<l0YO\n%SbZc0<R#hVA%ig$dlQcYe4'imk*=
%J%@YSHM79oi%p-1kMP.0I.cLemCUurhVXQ%8&Y1,qXC]&Khju<pcHmhDh%['8ABQBr]P?)lDA`#5P`S(RG.l8XUEhZK"kjJ986fd
%bHZ6So_2-Gni&f/;7u_LO6Y!iq2tOTf("7+'/eCi.f<1ZpG(Y\TsTcd^e\;UI^J*I98*lmr,`l"Nt!#HQJaPO-hsO)iZ-O5@nT#C
%I/b!Inh/_qm>l;"3W@*tn^6-5:K0jRQ+?skk2DX<>0Hikm,4X&3r!M5c)-k3%eO9_^#%\&>Y2n7r_UQE'b;An@5JgA./7>3_)Zn\
%$pDiUa!ldGq6QpUB2cjr#&bUW4ht8]C^D0k0u7AplGmTm?1a+[aMF^/'t9W@^iskFLDKZ[e@QFQUT':"L@/!;8mD_U]@+=W6/'N3
%*jg73iLE(8SV0L@,YASE+3r@rC]QS+&!u@@Vl=YfFSmH\k$_S8Cg\BbHOY<,IpBFG6>d*:-oY6Ape/^`d"LRe1].PCg9H\BV`Bf6
%G42KP0r_:^Joat?LX4tPC.ck.p]'1g07Nrlg\:0.]mokObs0T'%mM\Z*IdhD&'t"]YQ91Y_s_NX%tD5P^\HWkmp<]jI<%-8Jm[F8
%i.`3NfcQ=H/U[s1oHqr*SJ9DH)*uFZO8k3[[_Leii?mD,DWg1I5@YWp&N@KC?lfEX[f4pXkkik$g@apkro*OVGQ7C(L:Q5Ls6dkl
%5J2=9rWP"ufE\l'3_04>s.Kr*=.okkpmbd.Dk=`kKH5=?)\N)f.fN'^*h;Q8+()bWdgpBrf8F;!]Qbe4lL=6?044^.^kedR6N4rJ
%Y?nm=eXMooTsn9(kBZpGgM^E2Jf]r5hn=>*ghI`1qC,5<h6V=?J+4T4le>LuiACN&VE+V0:VV8To\#jD+=$R-aNskTim$AorHmhn
%'A+&Vh8EN.4u]]Ib73eYS4[SthGa-$9X30+<F>u6FiE&23O%),]mKSPQZ%?J`2<Y]QA`!'^O>kIS&'5_\7tknpKO-$]7*CciRQi#
%J+6!Tn&''>5FM;U]D]i)Z0f`(Dbb4XiuEH)s#e.NGQU),e_B!)?cPCpDc"DsIup!?_Jar#(-_6Y%9EE%d80RVmC,<0oc<u#Dr88,
%mLMO2o=MJm++=1Yc.d_sEV&haRhn$]`uFm@%rT,]O3tRhN&gHQ/cQZGL[P6#5YplTJ<o0o^`Tf_prnf6H1>\j@(4Hgro")(B_e"p
%p>Z&BJ,-1S\Va0trU4)UJ,f3_XQFG=IeM+f$NHd-_q^:8Deoeq);7gSpDi\k2uJG1jUZff0]Bc&d'Im0lF-C-dQ(D[mrVUgZh.&l
%s$Js\N"?2&<:Gsr"muopX1H]fm.!N-]EJ/IkfBY@Y3YZ@O"XhQr<'LSeNTc25C`@mc*MWCUjdXerrYFnK3RAZ-,9lG)+_>LgcS%*
%"'W^)]QjJB#PArO^`QsM?N+rN+[5\:oVVpOp$DXlJEZaebr=O/roj."+/DcMs(?a$#N.%QM-G9sa%jre]mBMOGL-9X0/&sT*8c2=
%mD%HSh:oEB^-F'`1eTl.ES>5#2O_@=f3+>P`d_MXej-(@S,5k7`SNe7q>C']5JMX>o%VcKjlGM)UUNd(-Pu"VYl]AkXpTV(-?(b\
%!7.E+T?6j5Il!GueJe2h*/c)Llu476Hh[O$o2dSoq<)\R^NeC;_fOqqS"ICQ<V6MC/]>K`MGB0N6ZLGj^feu(qP[Qm0D$afW@tlC
%jJ]%D(1uKr;MAIbj8*qK:+L,Y&meYX[.!8:Q2?/g%_n*Cf;-#ahH4<?W`qRS91&$ojF>-dau6rDI.It]ROa0T^!HWI:X;:FHi!<*
%5B&+27/u]:)c/h<IehgCpFfW>o?6)`HKr[M0ZQo:+X74dNV;)$ia7]ejPPQ:4j2/ISihH)>47q`^Acl2cb=Z2iqX+Y.hs=n$4F?F
%CT5Z;.)#,"p`6q4haE*b!KI+$.s.I3r.^!4it(-:"Z7+[_3_9beC!;8O!"><j%'+_?GftR<lS4(P,/j@)$[0=WDdM9TLSkZJ]\SI
%+eIWTM98tMC:QU?K0hedUZ.sM.,6M(4arVNPQHPg<qpS%ZU*hT%eP0?7jo`5B.+QEGNg_Ar.YiiL[P3r&KX02QV$h?Z@3N:'ttXb
%Cf!0X"+23L-[m/us$nQ:;4tobr;UTp6Jm"_;I$qgmu.0Mr<rTW:[h`ZnD@&-)<APkccL+t.B<D!5O[qtDY7K=?52\bH2?k)oYkRZ
%Z_,kdc[GW(Bs"lur:Ll.dCY4c0cWS_?%^S]&^&4$gmu(K%MXC'b<Ga]Z`h)/3V]@65rnhSi2FFpTe2DR*XJTs(-7[I3/1AAKQFX2
%GHuRim#scJTgLieb/\XbhflH9,HU&n.m)3`Y-a(+m@J%R?>.#u>,bA"B;$'nl2*^rn'#R'AdI!\JF/^3I^RUt*2":pWLV.;\Z>4G
%2*b*.^.W<>H9KhX4kDb4)L*^`[(-qH6Yj@U*^=CpG&4("Bm8BU&$ML21R-aBmtZAWkC,G$K0KLk&f&RJE.i6qh!&"Q>CTqWS>[Fq
%N_hd$_d.mmW5&esiKhG+Kdi`,S>i#2Y^.rm.jl`8qo3Z,ZMaT+3F=D*]/pN,-3O+3mUW"aWDp)l?$`t4f^CW0aj/M`>CYeeIu,'s
%S+4?1d-o+a-!trmQa[U/6n,A:kd\O*M-5:mJp[pKJ[cHcX##uuE@q.s`47XCY<q$F5CV\*E3Q[`?jUN:&&uAOG9Q-9hK9;Ek_PSS
%4rMs5aCu)'$TOEHdhoW\nu;e_Z,!8j9-lY[-f.7=$$fE(SqR"IFtms3$b[s/U^5a(K-5gEkb.ie_3'g,iE+X=69C`2\'h?W.7FRc
%Ce4s?*1&pELBqK-L\?*cL"QoaSZ!O-!*_O/.Lf"H!j<cf.LI+CbQKCLoT(*QKJB5H[Y%T=f+H/V$8Bg?0\6uNn-PI=M%D)Fi5!7[
%i":'_M+;01m-O6el3LF@?cZtD'O]9X(pkLr/.!RMr^<:3,:in$3lJ=CW:1Ns`1c]/)#3,]#@R&:\+-+VYm/1F#i%NB:\VZX*O%aJ
%>'t2Rm]kfmn>g[k3q.qkNuSirkX('6L/S/>/MKtj#TTiXFqC*nO3el0;[6nK"ZI,."!5"t&_"]j46(:q$fQZb.nGi-.OprhKiUbh
%0f.0Zk(m;ME"ZRRDu^Ail8<t!Pg((fOi#Sk;aZ0f>TJ*<UOo&qZRF:*KS!p2!iH7PaQF1bn[WbIe#1ssj)q9cNE7%%0j\.sZ=$\K
%>3W>iMQA.29<aFe%:*fCnB0-Y_eV`nMlmfI@BrXMNTg3UCI?^m=;.7V6D_(DecDrTW0,ZHOljF+r:A^ebfcO:I%Or`H*/sJ2:&V(
%3Bkn3+(8bqL1tgNAs:H?P?C&jN&I-P!ph360*s%dP&uSep5pWl7M:%Pff#$VO&A/4r%Rmo9cZe]BV[@ni*i8g!^Cb%EL]TR3IKg(
%j?HV:`bU&3+'I[KJ/[GQ96sFV>bK)aD^cbZF]kBW\gd"KV;G@I7Q_(I/'!"[BjICOq;:@^b%4^hXe_HTrV$#HeE<[++5PN`:.b:P
%5hE0V8'u\XgO#OI!!MMZbb%+KMlh@D38n`g!?=AV-[8!Rh4[n16X=Tt`hukF6"WY@V#fp1nfBZSW%m?dDBMMXeilFh24I*LGogXe
%>Y&F:Ks=Cqie<UcplgM:H(u^X8`I6IEU3->0sIlZV3pMCMn)4b,\dt^-@fY`IoJ.MOMM$"U`)A`f\g1m%Q86uBSPdsT(?cm\=:FC
%9Li]m6PPbRfWUt=&kajQ,T_JEUHmt8PDOst"0hTB]9$2574CWin,iHLK>So_9-s;(7>NJCcR!-T=oP!\9N_4G)ADdqIn'5OjG75a
%@f1??9a-Ou5(6JP?pWg@#LS)jN\3h"-XjC0iIp)gN[[/l=;33Y87tf!a7#G9/hd317*$sSG[h\`g;A*ult7IIKjGe3NAu6KD_<<_
%M)\Zc@<@`I67Ho[a,`V&`@hunRu*f'KS_,:-S/sI,ts-Y,\C'i1.A]@73@2=1KB,1"H*dA7Zn$Kio>Rs8Ut>GMm-eUM$nMS*]>c8
%Pa5j/=X_3+;h;02Y^g5?U-^Uin,`[/M9Hs4?EB1X]gGk^83Pa__Sl`d*s`Z]ZtR4,@[0s8RT-@M;m4WVkJrV'rN8U5<N_@^=NTEk
%%7+_5ZJA2e0^n*K41U/];N6l1g"#T[Aro.(cf=#_)DCnb>Q`QsonI;KNeH,]LpL(a=VbILJE=1`&@&X!a;RHOjV;qq5nue-'2r%)
%]oXGU.uT*['hqgb<O#MWdNOd25e7RuLG6:R7@/Vi=q!R9Su972HT'b+JppMr21DH\%u\<;^9Jab;1_mZC;O@jT#Vg'M%)UEJ[9t&
%!cbG+;j1=B4rY;"MTU#P^7sD`$I</[6ek3lHPBI0a?K/GVhl.I2<V:o=!pkLNau$u260C%Tk[EfCZ2HJKaoR*%hu=;(:$fA]ZBe)
%0fP:nR`CH4W%>gr=2]&D%-L!Gg^RPJXQjO;V^L!;])KjW^oiBaXp$gcq-;I6F`o:>97`T?G0ZOl6$=[E;*UV^;0J59'T*>!fXKj2
%TKPA[mFdHfGJgd\kB*B'3mr;EA<`"N\I3EL_k9a!-$^%cnJ?FY6FNaS61ImSg7UdAK&T/%Y990dSpV[)-nI7BJ8Yb(0m@g/l=\YU
%"kenGrs-]Sq9-pg);TaUQ>>qjr#Vj>RM&,Rnkf/5C>=4egJU2HW:C*L%b9l?$_>FfU^&rQok0r(RS,!F-p)V)4Gj=9$TWLbd;tt%
%SBFKo4tOs"+YSC[i*LkY3q%]g<+>oBO4[#>k>5TQ8s2D2!c2U7J37cRM;Uc3,;"+QIV#ZP%O@8Rr1kF9q;1%TD8Z%,k$1'-Zfs)K
%d*.&g%p4K4mYAfCA:-%m5pVY&2iCVB8l>ScNn'nPT3+.*)UDr%3K%29DT*+k7uS*_N"^E*h60Y*'.,Km+NJ#qR>l,7k>_G+0FiC?
%al[8**O=ts4AX6tj0$;=qObS%i9`I.AA#;"QkRtW,L1ldm`\?WbrY7-CAWtiJ#$@nZN&s'ZUs2;GF.ZnZ$2At+*K/Fff9MU]Bl#)
%4%?X81[Pp=bA>5_5/FoEBm8bNBs]sjm>o/l4(qD`4$r[.J:0+pKC4Dd265TX.aM5mW]B[NVhG=*=''\q=2mNOm\B!l(BY(jF2<n.
%I&odA"n4Q7%9L=.eZ)h$3i?*d;]!N;gp/<U>jb/?<_!G]gm-+?>r:!Yj:ZI,fs4@Y7?[#uW'->6F[qJG.'?`pf7Lqg7#*@.k^aR@
%##VOSKRFu@m:EeE;gpt\pQW6?:h)A#18LpI:/e]ZHo3mA8QpbY^kaLjn[c(C]ES+'->o!"P>j:Mo?8fZ-dmi>%\"cL!Nij?BEnB&
%.5mPm(/6R-jQI5e9Vc-`ObYal.%9EmV!ADl!BW-k8KUL9AM'6c%;:+(<M?jW0'hO_df5_9aFu2cW$dDWK.S:0edG]"ZKTu$Sc#-d
%TW=]-PRH/d82"U7%P#&sQ?Y8u>"tr%[*E"`1(9[D(W3Y'f?kf#AQ)*dg.^CX<>/MNpjJkO8#oAphuF]U#n&DZc8;ge*0*O0At6u@
%Ig8Zk_\($lM?FSm?un0O1XQ>F"Kh99\cW0"JI*Tj_?,Ts#>Ojo>3d1_])[`q2uj@%.V]48F"\Y4"9JU'Erjhkno!^,A;q[SK^?9.
%Y.?90j\U"RbRT<6>fi;%dl?kcIcPCF'4&mH34feXPTo9r,?rKU&m7C\:NGDF$dMd[[K1rfW)b%LqjL!$*?oS[S']_9F<LO'#nBQS
%,HX6t_mWd*%l+)-Naq6H,$SV)IU_K!3SkE(YK?22?o5sdhK%0K24g^d>*\-JbRg;Y-qt*!=KN8ffuc9FU6I?:]2W='BP2ltkh"MC
%]r)%pkcfX]bi"*kH6C!*W%e^9n@c&YK>A%d_%)3r=$[(qEYWce!RO,^.-LYUY.0Y`KcCN:6/2GMc=.S*k;&P8I/u97.rj>F)pD-2
%Al+,mE&Y]1"D=(<%+ONhQp:l@GSZd@=T-b;Vj9Pi5EkIFp-LJ1hlm)5gsEJGeuNqsXB4oj>,D?Y?<u(c9),?M%5iTBD(jK5H*ON=
%.pLR^SX]5n?3=s?C/lq=oFa\^.iMIV%&C-C]ii\5<qBoL5CPFO(__:/\W4][<`m:L%8W''BEhM?6>![l=&ptKQi^ibCNll8T[mg[
%\P'0F(+q=GW_]*!S&BVGWtf0iMca_\(@+,Qoa3U^J4pen!`#0h_^8+hq)[A2%a+o2.^'be9r1U]9P%V:LI+;4,-?_F78,jZCpHd7
%9E/h/L"D7R54sPjYRDMW)Xsb_gmY+*U_'WD'*eq]k)9A]3])Fu<Uld-J?/dn;EhXGAaKT+i,#%$=i$b69<PhdRpumSnNhEk4MXPl
%%QX.,/j>$e^fe5q%g/7XX\1V_ASnSOk?+?dKaq52!;=WPPZB6f\g4(:<[p[DNC"C9=V:d=g)O67c=D>_O?EY^'*[GVZrmO=q2&,3
%/Cog`fg%%?7F4e]Q:H]]F^^(*TDVa`?4,6GE\aiRnB%Y2DYD=4dod'5R#>6dW8LNhBPLscD:n)4nU?c9j.4GKm<1$RT#A*X1gRc;
%Qo;2N()^C18Of-D!>kI/JCM^_9aTpk#iPY42G"AY_9LBHIYlrs`MjVJ;Fi]@D!rP5:UKp)-q1IZLl0n`:F'SJcW.*L#I2&;+HA:a
%4CthL(,6XOYt[ZLiRtY$CqWT)9p5A!_D]nMbZ[:[a=[D:Ic2WBK@u`j<&I_OA7\0b"=p:55`N:Tm\[8m+Xo6>(tb^%m-UGt*Bjb9
%g4Hc9(?PJTrBH57^PV\LRcbdg';'sQ4\e=^I->*i=uu]AA1WK%ckGn"EnbU)DH>^Tij!&&MX_WK"lYrP0\-*1pc\P;c;)?bm)Xe^
%=>\`Um(H&$=5lAt[O3_TC2fm\fatdcUFMs0;0E=^F1P-ODn>G`J5q&]0WDs!?e!VTLgptB0"AtD3/_G.W!-4I[lr)oX4=H[UdSDd
%m)SUr2:&!:lartg_O;J1?<07lqF57l9:","A?E0q3LOMlXRjslB8q:Bn$AR2koj7-O7m"6pV@/#"@\jY=X*/J$3NR[oLr0]5seUS
%(9\p3B@A5&f"TqUpOtq90(b.^-Q'iYK8,F6A#j^r5ET)]@&"%*==%dmUG\XhE;E7Kp/s(9.W*LXU`q^_RcFs;*GY3rJX]D[(+K/t
%ZM8,W_&^W1G8,BDbA[]lcTqRLN2="tP2_eURn.@Z]XF-]n2I]V_?q2Z*9*5cR[YS6+k)G.YRJp#rW7'JUq22D^*mhqlDg(K0kV3M
%a;3#(F]$RM-#O+?\3iE6:G%<+l%Ho?_#kuJB@n`c4'9K9D@;/@A&a'BDe5`"+35;WP:FPN$o^gu-]pEV>I'!715-AJ!r:kRA@KRa
%)t:sY]PpD\.`6,"\1B!iTC.?H?1^5a*cs0/f#>DSm,Zah>0gkH4O8paGrgok=KL]T@De'5N$r9%8Y:G!C$#*D"+"=0Ma%?1rWU(9
%e(LC<lJkksf$H%@\.`"5B-1@k]OhnC-ANkOImlM>5nKu_,aPG2P7fKnAl"qA:hK`!+l<T]:EISuE\a8WUI?"0YeS.uP"?1cA9I+3
%-Q\,HSk5!]3(,-c'D@tBo:8k?;4Y5L;*68h6X`/mS2?CpSB7*qL`\S7fZ_a(`9#=`q_*$o&s?3h#8B<FH!AeU;WMq6jE^"NI6!,N
%LJK;q_K1W10\Sdr+lQ,e,0mMXM,->e7F&h$?CPR#f#&3m3Pml(FOFL6EiY=X,%sLR<n%UA\JVH5APf=-QY3ASdUKZp'4$o`KokYW
%k7<gSXm(b<X*jId4:G`2oHktQc27k^Lp$a&iDXEJ?$)KX;7'V.D&448(Z@P%>bh-5Z@s;XnR_;U]%jWQe-ftS8A^%C%s%"_g.?"d
%PI^lHo3OK4??51`Phe`?GHi1=a,Z>][(iAfGUJiiMY7cU/D[*Hh-4B5g&<0;k.JI'\A[uRQbf+)ha!fWgUuF`Sia!Hf+sYn?j:km
%r?qT$30iHYcVl9N;HIF_A&;I)n,8huG\Ahhlj/Di$X[3q:YtU&Fn+Q6=2)>j^j(F]U`R2t!ODm[<CM[X.qI7/X:Js]F=E*4d49["
%@SOH_eh*KZ1iTMu`>\1n*-Vq83b,aeB\#&8[2\8iBX&d09:9d+@kLf,Y(=&a,;(O,L=iNK!r9FGE"-%'@4GIiQXDNp,hG%0(1FqH
%b6FL#=!_ORF9uB,X'M(h+>.qDTZ/!eq;e`578_OI12n$#]b8h]'#)5$plF9i_gkl!/ApAn$6-Y&rece?&H"o>PaXA_e.o.[6_E"&
%+"Q:&qB^aF6G%(`;'at)/q@eR7'OebF6i*VNrV?G:Y-'cKuMtuA2QH*HNt0dWrJS10&bVn[X/Mo!Slf[D:c*BrT_XsB<PZiF7X`%
%h-bir4mFMld3SO7JP[*`M]<!*3PtQsK_/rt":aQt#4]Nl1C*RiS0M3N:K`5\7gS!-LbB%/Af0M_OABp6D8ieI//),/YV]la(p!s/
%(Pl9-KWIolAE[&&":@Nt$:FD/EEEp=:sHMV9J%XmSNq(9,j]%SDG[D8E<a>um!<8G'o4A1&l2=P5s^0XO%Nm$OT^P'&fOkC.!"Zu
%!`j<<Pm`H*?:8YS\"\I3Mpc30QIBM*N"e(ObXRrh>FBr0bqSd>W6p&1e&uQ\)(rN]CVk#lcgEl7ifUeK1;OpR74c=:A31FEhFpf8
%k1MSE$eD9U"DBG_A"`9o7?M*-;pAA,SfCp&+Mj8U`Vlom"jh'0.$rmd4\UUAfc`EcHREk&Wf`@V/53.&WH-:nTn!-bFs,cjRk)fq
%RRA;6799sq'Lt@p^<k:69uPm'H3XpK]VA:kG2A:!0m'LgBVa$mMH_Kj-AJfeI"=mdaPl&&e;t,EBHKQbk)iVj1hq:kC8%_hD?CA\
%8lOr:)kfmn;ZZ&rhPpc;ALE$BBA77`.cNk6Y9;sZ12qf#a9F9X+_]HtB%"ObfLbEt)%s&4(Kr<T@#F"tE[1d[b^ku"?ki<o+1mi$
%CE\j\K&<]J,UFgtMVob/#NMa$-@&^5<>;C`+d.JA&g33("8V+`Fe'7KaZX.:9`.)3/!Cu-m!:sa$bCt]3$D)W"BL;8-NbPS&<A*d
%=)j2;i6Ce9,\Mn$";?K@#'4(Sq<oR_H_,rEPa<0*3koTXgWkbg*k^PPq*H6mUjtAF*V)Wc1(!G#=[Khr8ESr&MnC1)*0N=t/a0M;
%\;VOL\A/G&5Rt4+=MP?(Erb8@#!#!nkCCW&/92)WS,st0mn<GkH(c4;j)S1r[SR$Z'3#B?'juTYq.k^$N&#Yf9b$a4<fM(r@6^5h
%q_OW>4G!EC+3.dn4eMkg[S:<9'?gQJ=0Mf2-]AZB8:b.lIQ1c"m4leE2.XFpkT<hTr_W$L+^.fhQLM+=Q,=Sp3\jnd1!/6!"d0dA
%i=7*/_oPh<b]O.fSQ/6&!MjJ?9-\9&%!X!mi+[iiXF_%I"X6o%N";[)Re741FBJdH6o1%5Gu"ls_o-1W^<+TsL-FP%AEm*'?gE+R
%m#&9*\/Im?F?/qu2XhGQ8PRl\MH*YV`m]TXJk&&+(08J%FbsurWMg_J^;N@1"@Z;T.!o^]a+*e-,>pJc;FnE'<*3`r6"Scd=\=DO
%=KS!m&$SV4AIZ3[[<H`Qj'R0&nluYaF5s``r;l9jgN59).+SGHloakC?G5^?HGN[D>7GU)(N%&&3#o,(5b:%0Q@T>N:=or->rrEi
%.p1->J3G2^O^oZi5_:W_Pe.G@%ik+l.BUPa3<`77qo'\=fl?k]&pDE(N?A&*=)T4,mYdCGSf/E6!O(rC_:o2uPFD,WQL?=qpg!qe
%:BHThP?a;^4e\P49_&c#6QbY?4drW\4`GjmRPNr<Bc9K)M=X7kZU^Y,HT)n_]q#!M"ft',1J8@E"bt4.!6)@9E`s3AE!T@P%:3;+
%]+:62ZMNVpp'T<Z9E4YTf2L_:g-+tC%$JU1BFK*&Ya3UErPOoD@EH#C"%d_P<["WZ$`l-h<?+8_P1i?:M,X%GbKtXA*2nET6gNee
%*R7PI3D3)L,,"U__Qeib1eG!Si@#_Ld^:(&OJE!f5,9W]+`r8Z*!GAF&kE'?p<0FR>7W-c"Z/H%h`aFYbLkZ,X!IgcT1bZ3A0b/6
%mIg&.0;rmdFo:#]i?2L7r2"0+WsE/4:nGm,/.\n'lGYo9s7Ef"&q0]KDuF%I``8,c3nSO--uV>?F;8mVKp7llafD^2_W+B7JS,>6
%>6jBZPn+5i'VtR[.%pln9jt4Io6%cR.';8@/cVAWS6#O+5j=h<(W^?M['#qCpt5Z1$8pEHc7)!Nq2UV5c68Zu\E7+rk4QN"$.^@D
%igfUf$3+<9mS+jO3rs[0p4aLA0$&ABn'*#'qdAk)e9AD=p3dq_^0efgC#OW8;V#7H1AN`@@/9,fEE_aB\hg-[Y_B!j$4NQ*'IXQ$
%"^brnG$:jOf-[W@'*T[=>%tLLf]-t'g2Rk3^LMLMQG/ARnk&@mX?@TblW@]5hr_]2&$VQk/2*=?;,TGIT(G%>j.+IND3?n3dQ(Jt
%a1HoPU\bkQMr]mC_5!=6U@_/_AF,!ggIMQp:\b:cdu(@7Xnnu:"ePCZYf8!YC\lBCInNI:l(0S*0t>[$QVoOu,8marU.`!Jo3q<r
%9_,E"n"G?I8:>0L7GO8kGe(654t-:%Kq6hF.4]=Y]2mdu\bU'I9eO:E(,egoQjrqdrl0Fpe0$W'fsYLC;bE7N(,E.UZ&_I>97FI+
%L&FMSZ8[##@)bD"K-VVZ]2u(nX2.`Wldk#jHID&7_qn]oIt5TS%93?e4n97D!TW\$jQVX<]onj4Bm>qb7?0OW1=Jjm.H2p\J,/T$
%YL47`[r![Djs3*Gf3I(?-RnC;-#D=\mnW/]S,IQH)(bIH2W^tiE!.=Pc_E]+SOO?;-/MU^I]Uat(AB3Ek/LLofWr/6@E[6V#WmBi
%qts>l$=**c/X-#^2RAH6"S0^'1bfJN%PuR1g<r9lr0Qgaq][pSn"m.]j4[kCI*HOD>JpeY@`ZJRi"AVS&fRBdd+CH/&ts-(q5>6G
%,X0?s(jiG`pN+9dTAo2j(4!PFa1YqZan33`U`UncK<r`UI#i?l%7$W,E[[aOZ71!dD%;qK=I"--%%ff!Y8FmKhhAUiSe5N)op7Q5
%9pt"Gka/R2frMV29=)[Y5QV0Ur/bRPj2uJ>=FSGgjqg-V,gUb$=]"\s<f;Sl$4M(i"#o4\i(2OZc]JJRM1o/'l1@ugcDnr/Kila7
%$!=!2U,dS:\-V_1QN+OHq\[P4o'J&\5"hf1h^S:\S;+@BH9L@WRCM)28o)ghE5L]m'/uG7._^$o_:n6dU^XOU7Q#l4Y!o9p!@fZh
%Vkr$&0%1`J?qfS.K8$-t0Q&KGUMJQK2EAK0=h_=2d"cOXGQ:EV&pT/01W5e,inP&?q?mWh>LDD&eFuDMRLjCQBFX;*@\V+HVtTY%
%htP<ShWKf&3U_i25[IaO7,*4$-G-&pdS_[AMO$"D,0GWR:7?V8C=KF0#me3AoTk_TR@6YMTi"RJWdoPM82TL=3!`:=Y`lppbrZ7T
%`P"1X\JW[jda9EM@FS])YS=[G>QMu/2!Otl66k_R97O>``+G-J3/+u0C1RBNjV37abEZ$tF-7BXr%%tS3Z.!YXrAqA)D/A0BOHh5
%g*3gl17A$'`pDN$GO:QH9UNBhS+o0hTAS0U%tsRf<-_SUr]ac2q_DM`UPW=)'&q_kpS:nq`SSaT"&1&K8?)CZOl4D9Q;3=>Xa6hn
%S-2g[):bd/Q>,\F`CZchEOL_j38qui*6fPI[a$u]5Dabd]%tpi+rR5456r`e,aM%<1P14FZh0.qd;Y89L4`aGd,q]5PbNS6SL"T3
%,[\0QYYh;Ij]XU7QUP3'K!u**ft8SsdL.MN,'BLPfAKKJPuP#h$XUbV`tPU:bT)As=%6@BjX5fe#LQB8-1@eL2)M;^>\5kHcb.(,
%[#BZ9>V)Q_V+"VUqq_4bZ^^DXeq.Hd[<;C,-19Z#7uD5\MJjc/TJseO[16ri(%9m;iEMD>DgF:mGtNZLi]r*#qhAF>_asVNfDR97
%ohJ(SjYDKLc/cC9l9aCOqg[.+O<d!(?_s,P>I+9kBX6`^C/X*%4=,=&Uo7qZP9Wskdj+!h7j^lcW6G3X,^N[jGoQXCAW=bmOVW(u
%N`$r[95o!H]m,]g3.P*%::unN/^^O>,e_$Ak+BWBZ;,9sG3%;8D6Y=LmDrrg\>FS1S+Bi#pgp]`7j:;^ZPP]D\JplL$Ffp'rZ/!.
%D"jR%&)*G$koI<.7_+W*enHOFhDljb3@S=//!'<8UcAUT?M1Uh\+HNcVNcIdoTRYZR@hdQf(INo3g4p=eFq2G#BS[9;A@rOI/fb[
%pW`BG]1MC*NW8fh>f<LPM]n^&dYB"ABSh:8Jj5Y4adO?.;?W+RGr?OQ9,4g)(Sn#DVh@@)1?]u#)bCtO2;W25#C00,j%]rT[?5KH
%TH#<"j27#dmA(C6.PUZ+*^@6!8n]l7:tnPZ1f==qcpE_5lEAY<.5cV2m@'p(k34`]Df))[h[j'OV69<?lf;LO8]:=#M(OmJ6&kc.
%$@M%uJu^??<Yob''UWi>jQe$C$[LYHf4a<d_rN>]S'6[,iODSP-Y`Q*Wc:d1-E)9K3M'1J?/uWp&]_D:]g(n[.1;X/`B!&;;=O5p
%_pj%NcYo$H/^jW\:];P5XZ?(3Ie(5Y,V(Z71q:n='#8&s_"1<q1?11+2$TCM-3J(R"D+K26GUcrNsH8!Ua'+7N[<$(9+>R)@ce6]
%[T`3U*M0LU$$J</V$LDpqq_5MTj;P_H.q:#\X0Me?`<M[DZ(20;D'bO8E)q[hlrGl:h+-5P\FVe$0K0YFcgq<4N5a%q[sFV*l!nE
%4VA:jT%9k$jc7n"d#qY32t(A2@'I\<]6R.5R-in>D$&$pd_W2HkELVR?$n6_i(,f:hS!2Ie$tR\F!lh-fX^ZY)Gf#G48X33CH$V6
%hGg)N,Q$nX?(^6E]m=h!_V2XeU$))SH=_l["[*Agl?U_Dr:#aEXF3+raCUSu'i/E,&_m=A4-)^A`HqrRpRL`t\W.QEDLDpB2>Ts`
%FM0F5I,hQLj8I-aM^l3liLS>Z"6iJ/p`FZ0F83&M15EiNbkJt;n,9$XS@o'q(/hQ$FITQ_ka<*U8TGjYG`:WLT2Y>dn4%RmFciQu
%lBWuMc/ebd?1oJ9XNKUfA;9.VaT^>aWX*ec!L#V@AII=$2ptQ>1GuAT\#TS#MK%WSQi\Y5";InqZW$;S[2?Boge:"i1[DeLX?YFP
%Q]#'$2.Q4Z:Z*fm4%h=UG%.#\Lit?^;n!\S2&R(j3n8L/G%[MG3ghdlC8/=[n#iep]lBB>!bE^Lf0YmgR`n?mmM;j4IJEO;KTrR1
%CSX4!#m0_SWll>]Qg%/GXVMDR]pNcC(&@1@MW+6f$1K%6eY63\[k$Q8<f(B6^,!/dB(dXP*qj3Rg('\fCrqW.nGPDuen(,XE)'ZJ
%1S6Rp0V<A5E(TY<P`$Co\8/</?&Nel@67+cUTrtss,fN[?6s]^g]T[I6gQm1ZT@5kT,Sc:g]6J[Q'1:Dr6m,"1-bOA"lF48-au%6
%cKRnWELRljf=.QI`0`X7\;cc^]F@lI-ejLllr>k`@]OA$Hb*BrFZqs3P.M_DQfTO\-/WL\S:K3aPYh3/lN1-UTQPcHq]L*Mnf?D.
%Hg-*"226CpFI5%'#FI(G\DqIc'U7Gi.5B;V&0JoFR!8&2RL\pS!]1eLmL?n"6gm2gE?!_gGTS!-fj0@D9\fmsd_3cW5!F*.h&(Q9
%o!XTTh1iK%Y9\$jhG*e'jDC&XnXfAif5>p+*t#+I3k&JZp/C/J,8)V$WnB$`Z`VV<-!tV'$_i(<8\+UF#oC^>oEk,Hma/AiMkO[(
%*F!+namgH7RDi7g.7S0>g3ZC;<I#sufeTbaIu(BK^T=^Z&Rei:o5(Jm=f*b)XE9IBgT6"^/X4P7QjTS*S7b^&92CcB0eQ-Y@<K.<
%4fss0"'oG1=aG6HC*+n"[\s$cHqF1^":DgVr?@+Ek:l63Q5XS?Mhi'`(M&Gs#_=+#YUZ#.$1+DcNI[6I.=E=R^uAJMq(>T:_XJ7W
%XqTL@kfl1[\Wg_;iFOc>\LYY%HaE__(Vb23=4t18pCO7Hfl"Ad=VFIbb3pI`IDuH"Ej]b>H<8^Jlh/@<=fkQOH>Te(;@>QB<i1h7
%0Cr4&VO="JZEc=GJk#M<]6Lr!9.d]u(d/4^)OMX9"e@50QsQ8^=iN9?W<+^?m,COO!YIcp+@/sOO@hm>(^oCM8$QBBHF.J5\m^1*
%!uq_4U<&IJ(E*"Z3aG7UQs7CTClg[(7oU2"SDVl"\6&Rp@HE4)_UdN<)KEGaI/%C%niJrkj-f]eR;pW!14M'"rRnD9\UEEj5#4B-
%fjCl]^YA99knT,&nPg*9Ferm"gZ!YE\&R=tm:WKmY[5XVD#ShpPq(@%rVmkAZ-:a=\+"*mcS"gJbVLqWdV%nBE]-BiN&oIU0DSp3
%G#&QI"a$.iHtfS^<>0+P)C8D.S9'E<c\U89EG%;Lbq*Rq?7kSNR#9a4F:oRb@dk*!YGcn=gmpuqe]ct#'uG^&++&rd=nhBi3.`\/
%<7Qpu?)"4iXpJ@.T,?\^&?PXs"qH+,5Q99#Is`[T?b`P0n9UV*^<t*_*;+jk95Y(pA7HU/C&SUentB2O1d?e.%8dCpJr:>qK,;Vb
%RJBU!95B"@+=@%8-9X6PAO0uDn`OZTl"5_\2>4#c:\.Ga@J;<TNXM>#/O-Z*W)g5IR0Gs[MjC\Qo2_ok:sF]/1=T"K?&XPR5h*Ht
%A1!$Lc^(%tb_kOrcod;VX&7-#_pkUhj_SX4i%1>f2i\l<h,5J4ZD,fc0#sN!NjO<6!dgpZ6fSe9/TZ='SBS]BV+mf83n5u^bB(nV
%**GGF8^.iHPCj\Hr64Zm:k6sXq.i_25:<kJV#5>:ll62[VUD@t-+TO`hOPn,"_\,uNbdB%la(7*>FgA=k(0*b9jaR4ba5pno@Yb&
%f[`LRr2VhpTglWkD&K34SM,7TeZNC"pdCNG*0gP*B?FQdel+nm3LfPm?7<m#,ofkmX-'d:3#T!S9u:>XlM<W1SdARI"^#Ec1_8U@
%=IQI`)R5,@W_7D<3dWd@1XWm2N@np75'&W08m(>Na-E\SDfj>^RCP`k27^H&05]LTcLI@Y\>E6:no9Z[(ja!jX!$g$G)t-=U^kD#
%>V"PmD_\)X.k:QKntZ3'C0Y;m]Xtf1?r4;qPr6]qMhP#mb^O0KD154$Z+2=l'J3bZ[YZ"gS8Ik4N_Kn4J"Y$^:l1IA>7Zm"+"FIW
%V<J!nlbhgaj)lTE_rd!'.<8K_gF7P'`'*u3#HVL+Zt5`\jh*%8<^;Y27G17l\teJ*'A_EpqQ-1W<K<T7B>`>^`0'B0Q-7sJ==@r2
%h7]['/"Pb">\;g?bZc=<A=G><Ngo7NQA."5=\0E$0JOb`kEsp7265"'?=.GA4-5ER8:E+udNVDV>A-OI7@qA=m-M\8B/OE>+Ve.#
%&FQa6EL.SJgXOeteR:_+ahdE8')cL;*R20XC.9SVMUdtU,gHG>L0,7$:$>!mhl$\f43,`X_WNnT,1_\r\>clg^TPLmrjA7"s,Js_
%Vbe0_,NqS)*7-#:_,RZ]iTOd!=HhsRKA9-TDFQXM<m-W>#%TFbP(I<\fH=6`'+[cGHcd#RCD&qb9f?0FWGEeXNgCcVq'IG"D6'.g
%XlkD7mi'LEb/O%.G$U_hql,pjB_G/8)+hbg/J9D_BD$1a$fedshOUb"gG@SWTCn3"i\sGDUVC<iCWgWYCnf*$T_$#QH8M\dZ5b2/
%Z`Rcm=gfm]]k,3e2=<[`h't0FI.[i*n,>C(-]@`Z7k=Ak(/FPLL^O7D8t)Vg`.c-69T`:qXMl/nEL?g:&nN5kq51?!I"fEb'fr>@
%]!^*m9lE2"jsn0J?pNHC4fJ+OEg&n/+I=rWj!FaYno+ejGi8IXS(q\)OunZR,K-1H._=h@mPID'4l9rnM0B_m+QsL[E$CO]q+4!`
%?Jgp?AsdQoMOIGppS+bW4:rtrQZ8e)(3QKg30?6;LA7l_2`4s=SXYtrS@cF^\P8L=%uZh,UWKqY+G*DpMgi?AR"S[3_.r>ZS5+'n
%4Ak0TR)-B'H>@WGn9uHQ>$o:imWcOpi<%`j*%1'sm'r">*RV1EPPU,e'l"`1r,uR^F\Q,Sp[Z&q0)+eF'FQ)63,Z<4h/Ok].+USd
%AVM&pL`J?7L&%R#m;n[QCLkO*RBksk=lW.+ZBWUC=DHB.\<ejaSLNm3acAl90?g^aXNeWe>o$SK(AY3^bP!D1(.$rh=g@Bg"!,Qr
%0.lEH"',LA;mk^;\Ze!cfD4__T&qPpjt!%c/"YlRT&qPpjt!%c/"YlRT&qPpjt!%c/"YlRT&qPpk)4Y9`RB4RdsT-`I"%&^nMFVC
%1gfc0pY]a\4*[3@&N*BpRP&+gR$p0;bo0Zi7Fl#>Fj.;%V0,KE2D`?B;)X<<;5B1Rg.Y^KCYQY[.-cOg@hmdY,&a2LZnqUhlVjH&
%Mq$KQ-E`7g6_iRZJh3APXeD34^<6kLgHl4?1Z&nHGD'^Kqhs54WqUu-rLQH^4*p.Q>rr0:Z!-sXV/L_::gR\B%siegjgi-n[?%H4
%_[A4^*[hCao5BH8cRp?0F5.ibgE4)(";<G,nebCm^[OJsCZ&oh=aSG45@7d+oH:5Qe?HWop(lkPmCKcNqua9\i'[6K/.E,#!C8f/
%jL_$?2P4%3\1-`+c%^]ljNWJK^:73emYN]uptkR,6rmG6ZGnI5s3CH4^XdYUf>stoBWnh'PHMK#kXA/!kHNCZQ;G!L%BJ^P?Z_(H
%@?pUWnRHVQS<I%ak?DHA$d66M,e@VXDP",hAN/"Qn##X8Jk'>_4#FPhEb*@#*k:O,^V9_7H%aj?TuOGg>PVg,ja3g=i2%d0m/6Mt
%WM],"k'UpsrKOTdX%5o+.%c4X]*Hk6rdqbC7eU+TpB<nAmH:%rb2hE@F5(IGQ^94?1YD-ISG$]"(W,g(3g\/F&V59dm:[P&.)Gcl
%K56@Q3*h8/2YZT%_)jR\Df63Dq[:ra\n--H7@I)/HYC>t=Wg%tLrF0/V`baS<$X9rLWa++Y1a$"CU[Y_Mnp;rH+aUo`'Zb/IppZ4
%rJXX_Ke,5+/H<C8+(&g]>3"3Q^O2$5Lb&HIZ5E5dD/.EZnbt)e`@H?+$=R`fA('6.PQ("4G#u'@KJurMZ.SRg5?ct#YfZFLbjrJ=
%rk"B8(%fTsQ#!#?I87mh`Vpr2c0+cS)\f9abk\utqVDCZ]9iG>F+49t9g.CmYeIs46i#US0l&f-#p8Z!G<"sGi*_u2-_0M<\:3?G
%LFF2?:-NpoQ4/8"igHt+iNSEJ2q$D*oA+nO_uBTd9C"T@eY1!0Zbs!8Dtrhh%BDQl55+E0X4cApS0S#!aRjQhR=5=/:<<(F%Q]"K
%T>!ammpOXR2[l4fmZM?fomp/9&aM5b*Hi+G\43XIkk24(c3jdPKOS:2fqH4\niM[$-*DZQDI4UCkBiiP=SF-$I>`KFdI1ibr.Kg&
%4!)GoGuM/O`cf0Nd&S,Xr"_deQag8J>jU=7-0BYZb10D!Kp$I/Oo![%Im?"f2e(TMNXmd>j#U)r1X"cqN\Bt2^jf\d)I2Bf@N4`l
%DP5X:\3P's/Ag@LPQQlkbYg@.K<M$jf(Xgc6p3epbF?At!\^k[s"$if9*iT?P7s6@M!s4E50@kB%]e#Z\=pXP'J1OW[Lj4s3!q0Z
%chOfa:"fEtH5/SK8[&H,h!n;qU,(;2@RbH\q-:ABd_/23]ctLnfIcj?"0@Q,2s4-e+4.USB5>1epqG'-=n9GErC+jrC>?AAk-k*4
%1Ys#PH:[2a@sSXh$-r3s(W3c;!dgt;]JcW7n(Z-Q2kO66,lG?paHcu/mk07T`?,:1$7RF9Nd'0p%N8QHgMQG<lY,c`?fshnT3E&f
%ecXYQV2?A_4bZAU`IdQ(K/Jc]Qi>bA0J'B"UhNppYl\hAe&O;q0/>DaIeLuRpI/[?]$H`UT0IBdTRtLY&T$V$-\Q715iQRPr:[%,
%hu*E.b;\ls^A7_k-Z_W@q(IdNrU^Wm\&NnZ^AI?QJ,/oHp[;J%Hh7$[pOE&!:H.jJ)ukQ2mcK&Ci7EI)r9-6?`QJut^]$%gl[Qfj
%rsH+th5$O4h#,&S\XRNq'>OOcj$ul`:+lKiXoGE7#+1+7DOqK_kM+Kl\4WY04^HP@s*B;,rs\(diWWFDGbkTU1uO]/s,K'\[0N%4
%-.P6_md]/7:;>l>DgqWnfVZ:Va^a^@K-%h+kKYu8Rqi@0V])Y^Hg"AHe#A3jqV@h<IZ*44gfA?t&k_$1H'L!^4PeRLg4BLbHcBs!
%gK7Y+"p[<J((_n'DfUgiofTocs%_$+_0QT.dr^Y[*Tgo0@b9gAcar@t^1fXD1<P(#[(f%tGMC%n]%"'#HI+SQ?>f:nPjO"agQqR<
%p)76%faRYKDjp+':gMd5h/6^rgQclG0(j%L*cu)lDna-%ro`h)-EG^q]R>4)Daq%hQKHA`QgE1LoE$V@D;XcT44sT)4*!Qhe#.MB
%hNGL;Nr;X_>L,2'(l-ul2rjE9'q&!l50)\"TN0nHVS7o$kgre[2>X97GP\3Q?(!l#9t/jsMr.PmkfYT<HEPXc4jnGdXZiTK>A#o/
%qfWZ$qQ`bhSnmuC>6EW#.;YK6[i)X,[cp(b2DOL-7c-$CJ?R#!D*$JKf(-966!lN)lFBCA1_"9(`LR$b;mY&G!?^rF';M.7oM*dW
%k>^=s9m\k2i!eN2QpG(;1$Z;=?T7B,D#jfVgN:-r\S_Y"U+\J(W95(Idr`8l^ui)iEf4OuD:=-OCgtn8NP69L@+M<qm9KD?OBgVe
%oq3rCG1:Sm^1d@]IeEK`Y=-G7_/q,85"P;S?U46Ornm99rX@$?LX^U/5,rk=Xd(2049*$r8Eu@1`nfIJ"PO-rKF>9&W3AV'+VE@i
%085+RNa!$NaS)p`N,QK=SoY@FY'hcL0=m/SH[)I0c,#'f?ASsI9b)l<.lc^DeDNVZ_]*K,/HAS(2bZnr(IA7!$6D[2^C?l_mVSrM
%QLplgpbi^>;YAP0K<s3caq@8VO^Dr]%aPq]-G62$^2j[T40#LiQ/rp[Kp&u.ctj!0F(Ojg/q%j>,h)g7:"He,OU`pY7*o4JlM3#!
%;2GA1Yu1\\O2iJN/$'\h!I>`S%'&bY:(A?p>pOdp(r&M+-`[_S_UkJRnJ,>mM6<!<'Q3Neme]5XrPtg$N]a0m'qegtp@l$%U1_&u
%0JTbi:21/Y4i/+eV>LTuF:hI1'1nhQeGdTm*j!ZJY^m/@--/-io&(>ejVm@ep.@'*#7WcG"G+<uYKJ<iQC(4Ve>5e'[t=C/24nut
%JcBt4Z$Y63*j7ci()stW*;%02$0k)L`Wj#t/i)8[U+<.O7^&"n>OYLGY5?;YicC3Q!+"KecgU(jI7IKEkr#>(cc@T7*dIE?keRia
%#'r\,M6%fGGD$7J#VhR>7-rc%CN-Q<=Fif*OAA*kh16.s>-mH65`bD1km@&`(8j9PIKgs\TYlmbcoH&)G?Io.8Z;]9kZq0Ni*dh%
%g?qZ]F$1eE4k7MY'.lTlDWID+$:9s`F")5T_S?P!oE.9!8HNTS&<nm69ni@;`H04,Ha'\9m45+=#-nXu*"#+M+=b=HJpitc$+$SK
%bfm-)>dRmS+eh4GEun8hRKG)hkF(c((m0?WYK$5XC..=%@iS936i.R9Ia=4rS#eD)EXj@3mN+nEn98Fl5gN2?-nthM-,tT]UI&ed
%i^-=-T?NFBY0_N]4e6'4k"<b$]kER_R$8H$o@aEd:RG$$3\kL?/#-lI(1VGm%YIf-%n6J7n5MmZ@DtU;(-Q83rkTI\SdatlQb_YF
%?utVr@D1So'me5^F\c$$DG<Hn]Bb9?ZI9[;3\b6>(Hfd,Qnk3?5g5h13Y:8WOYr8/_i=7dk8p'b[E?VN;O<4F&M2!0/>j>t(iX`c
%".[T.N:?W=DX2PjF_=^P'L'_]Meo7pL;YI._bj7m"8'g3&KE@k6M-^3";*:@&O[GhQ`*nVc-SSYYEQEY1,0k1XMV4&+[(Q&LKMd3
%3ME#ap%^E]33&"XM&cf](u"_WJ,5\S?5..463NiYLpU,WWj[WJ`99%mA)r*DdAcqE\V3=XGm^@W4Qu:/C37lY!Fk/^A6,mfR$CeC
%0K?$qP=$.-<ZCB*TD)@(DG6?Non;!g8"G0S(D:-jRFhVF+!;mBlVJONKC,H^3\`H,gnWP#n>%^?kTHVnd:+Li1pMr(d-0=VZX('b
%;r=>\+sE9!Kige-CWmkr.P5b__@'+FN+3W.bZ`+thD'qKLekFcYqhfuhf2Bf)dUIjf2-ud*rgmMs3aiY+bg-t.Za7!!jNnn\9U05
%JDM8^;gLX7C9mn1$aO69='-AN,ranr`/3`G@:D_*bj62`'bfh>-#VmD<C%JdBL-)g)H<(pMiRS/#A$PD#Uq1LQVdi6.X9<^Q3M</
%j)@kl!<d?f_E&E@ng_l$^gR?_O%!r6`8P`1r%R=fKb"qqot3q49uFm:\Z11sqf(FNVjq4Zr'?+)q<5PK/[BP0b![U]"k"nWAXpD"
%&g7g'?Y.kS2)<Q*gK-WV+V3hBH$MBS"qSYJqZLYQ?AN\o=W_&M-<:ssm[70e&M:k%&'D]#[iI8-j*.mW<E4e8FN\r`.2-c!Pm:;&
%;,uks_3@Sd%?G)MMR!o0$*%N;@SNTnjcXnq&O"/Y9UVd@#oS_\0hl%_6&:>h?rdGhUMC<=%jWog(Tnpd(s1%]pbNMZ3_rFDC-[W7
%KSqI7aqO&elrh=M%]a]X3t<W$[pT6RM($[Thc5;KZ6.0,ii?aN*_"#gEaK$I9nX+?+%-s)[AG)YFaFr>[44&L?t>C!]*OhoX.pPK
%ZC"Eg6R&oIkRKF/BeFZa8lYCFMFc9DQTc,8Pp\oCEctp::'=8:Cl=&fl]S[r[tTPcgV;M50cc4Kf-!/2%eJ:%FVbk5MZCV^l4q+$
%W/!'DQ'XPSkOKn>X/5&m!BOg0@li`nPD@9Y7*8!lV.gBd3l8KgDo)P%iGAZ]9MD)A<-AUJ9/[P4_YGYK2;598"*m/q/R4:oB(qSj
%4RKP>l%k#o,d[;^V.3=^^N`4>nHgf.e@?Vt)0kDS;P_Xf3q)hJ4LpTHi#8+eeZ22A[9?]eM7!A`!U+?K_2"M])&U?JANWNpAg6V_
%-K:0Y>\aUF?$C7rn\^(Z;`me<qW@MeVZi#H!T?mkfSn-,)`j)0=H[GF"[jBBJEOGPWCYEb_X8RZ;lqb:q0V4;3@:FUK-eMKfUr8`
%W'f`b)*kT(oP180<e3,`b`h&QFjT&HQ!!O;AR^Cjpfom#Hgj]V=!&)K[@N#o`Lj?H[1*DSUM(8/)sQ+\Q0W0t)N,G+e>;#([U\`_
%H!Q7d(bQi?oGamQX<2=Bhp=A[KS5^Z(J>Wc6"Ba6"5,+Or#H5A8SS7E,mk4eAU.o$-D0U^@9t+SdfS?RgY^Z+J6eJ!e3a;qCS_iS
%=j]QKb7+cBg-)eob36b:X$o\djP?$A7fB)-VNY_ECESND&V9CM+_FLS)lP[E*jpa&;ER'8"sL,bfVk1=fHs*V&@.K4X!t]HcNU!i
%4\gOPaMX#@-C/`m*Oe#`hV#?mN6e_iTo,"(FG%^+Ymq.EC^gP2pl2A3XS-teM/QM$'hK'&8+I*&60hJr"*Lpuo[LGjX*fX&,AUVe
%U1IiX4Yr3g=f\7+MLG(Q4[kj@r%Mk3?#/r>@#.q4MEP^iR3Les-HT2_S4-pEeu:fN1CeU%UT+c<K1aCj%7_/3i2U3Fes->?X$+Y^
%+?3sV@P!I/]p$APf]:$V(O[,9>3I0@=7<a4A':-o:%oRRa\L-0X!2PXU?dM((.%2P%n.afA-u@id_-O/d#/%>L5BG,HU<qS:mg4u
%qI,3;N6eP=k[=.B=Pqn9?,`o!\@>[O_^S2LCs?63GdEmVP9$9CY(Q1i&JlCc9<?`7;"g;JqBcka*Jc1Zk9\N6/(+Yd)VK0JU=dG[
%N(+A0N@3H2(7M"f1^9G[hsA)HT5?,7Y6P\\-5_]^/a3qt<VaM>SR^dC8-]q)=B@MqI2TJdc;k3*&<#M'bec[l:USD^oj+T:c!rUj
%1nfgB3*3)Q]&^DVJ6KOC>2uFf$<Jrh$9emP#1l[ol?o^5.<*\UenP=03*u1>%n^Gng5O=iLko2kZ/#&F7rrR6i?7/#'1#PB@Up8I
%p\HS#he]\eTo+\\#7_4C,%b>mYm?JXJB`<+be!`A=B0OHZV*T#1$AlpUqe2kXj+kaip:bu4K6GQd':SC!]7gnq8unpGW&adfa!7p
%q.op7.5bpKCe;0CLUglg/p#gj+UVk9F&1n+lqF)*L#8ilno1D4Lc$HUG0/2.Hq6@PAsXe=J=l?%E+YIArIu26P_btK=VHFYA9(lE
%gXR:&4YL1<2q,L`\^N<&+k]gNO-$alFP=LB-K4`n%?O<560;?sOWBhFA@.5sH!.p8kehLMZfpk3l[s5EQj#fpr61a(F()Wfb0*@Z
%Tk`,c6c:OFKlk)m>!i\"8.AiU(r5eh^KT]TjDg!I`Y3J01mpMA1-W@4^8(_\P!F/O<`kp/`R*Jka>Sg0A8rUK_^s9fcFVXA[U=8+
%XsT=AL@(7CXQ]:sL=elJ7;"O_d!J[F)=TX`KSa\&d207ATZsOGa];?8BWSIs*>%'a2=^-WkLE<?>SD,'>Vg%112%pRF[BU@/S[/6
%2,^eTM%G^l_J$]AKUV7X`2:JfPh87A@j&f[bH-ViXU*tsd26\FJlYiq$jmC>9I-JfW_At"763X]2TZfO@I1&9fg5PT!I\R]J\bcI
%"(QaM\Z)Y`4qBJZ\Y6Z*m?jE%W*FRg/u)FGo/Li`.4,8mgn:IGj,!T@bEFrTCC9H/7?'tDIUF??Iq!]oC/@k3O(1cC\_9]q08r'r
%I5n6uLWWo_!u0@d(kQG>bml6R4F.KBdmm&01KP2;!YCPoa#D8"otZiq^&dcZT[6d5@B2;j*61HH5)*(K<5C.s3N]98U1i%e2'b[q
%<u2b0(^9C30u6;C4G8>uZ3$/#?AsM.[6SOl%Zq+%<=jHm>QT^9Ceb#*g-:.U!oosebCn3,j[ODo;2:p<'Y/P]Obb?86PAI.aiAnZ
%UWljfE!J/7.FHJ[5MBfdojL7oMhiVT'l+Ru$;*j*<lO#dq8T3Gf[fV4;&8=t]&gCdbCp?X)_o\^K(?I/n7l5CYd2Jn+ZCkD+^)4[
%8\)<Tnr6S&D1+qk$PK%0PrCrrF/U-dhWm]6lgJ?9l%g.eLjDX8?W_$M7J5`LkiFCDldN("5np=rG>6#hCVq3&P+!'*;C\n3h#_-X
%&tq#C-6ds#=D?cr6Fs:!h#ahK]@W0]]h)Gh@'^kD7tauo(a=LbS!e:u<LYpEca)jCT4C$D*3s&;EI%rsY7D<-=*cBsL1`c%/Y6(/
%T4i:*m"q9YkhtBobm3?n30NL22@&NOV3<[/6pD@*YbrT`NF4OdjHNW3W6TA1NOn[E'\n[:KWO4/5Sb2lb4#ld$Gm%Y?Y)?\=Ki%2
%/aW)H0U\rb>!6MY+Xd1uAE=Z\I@V@/aO?Y=37PHq.W;KLbOCWGfYPefk_GPp9NYYd5*V:RfEJq9rQtIoTnC=%Cr9!`%A"e,I#Q]*
%ia=N)M?b'l9O6:7G(&K+<HlZhapHZ(g]65,XGboIni/hG'Ch#T=hS>19gJA&`9EL$4'A$p<r8\]fr3V*PFUhOPi6W%@@Bu;\=$L:
%2t_$MQHd+fXiX/,RfUn<#7JXFYF83q>98bkDnHNVl,cAK2ra85@2G[nmM@XK$F(K]"<ne.4]:+',Ll1q(cH*I4Adnn)G"q,K/;!(
%`+DK(-XhD8QgrI<;.bu+$d/l(?jZT;D>SW-B=<ARgf?hb,0+F3[fp3$CtQ'2?*gM*B_;s_)ps",&*;Bmg0@gRL-;b<"AP]F.rQfd
%_LiMbY'09>3'\LUEAm0@V=ap)'$O3)`':9u<caQa$ldU;Yeu#>1*r?K\79Kpl_'pY3=`ulAUJN>.Y)j8U*cIC);aL5rb'#%;#h?o
%(Mo8)LMU88j$6p3h%sCf1:6(XfI;c5*X53cUo?\5eqW]-%ip[a!$Y"2A(X4^=0[8ic_+1<#j_]*<&'Rj*B*,#XGHr3]A[FNCdE7Q
%8.@K@+sGnPma:A[\50(7-GVZHW@3`056fs,-D$\ul49Shk#7d2Sla&U,;juDpeeg%rXWmqrVr6g#TnqG>1C^8RtV'&R1>?L3K9fc
%$hB)hnrjT_KjJ^cZW`/O,=qlk1W&%F3C\U"1r.0j7a;_O&N=-(Oa.7J&o&gt%h]Zn.V7lN*pR(g%YQJ#RmZ\3ef;42LmJ]Q'Pu(_
%ljR8&X,?abd[8Z%.mfDf8u3eNnXp2+`KN_hT'Y94,+t,/9CPt5J>-a?1kBGAV:QNCEPPY6"#X=1(miO12])S4[H*i)dnCL@>q-&B
%+gVm/&c-9pMXoaf_.k4lWdW<7*aL.J09ZJQ+aM0tGu;s$e0hs="DtJD\b9/]7S1Z[_=.3BqE1?%Y"=DB$]5OO*A%JLa[;$AdiPr[
%6k<'/0P#40]=j2LB@huc&0-=\Z#:hBH`+TLg21Jrq(=!sm5]>''A\>ZD;t+OGV<8jg,Rqo=%ESU#-ors['VU&$1XaTGosnJ]=/]Q
%'&aDQ)PX3SXta=7]gY]cqFr].(eH3E)^OPu^+Ef-UP>QKPN^4,!t8FN,;'KN.mGVkE,RUU2N8r^'cSqIXGc'l@PNC\Ft2G_N5il2
%H8RUBdGU;<0qT8S:NR#dTN1.5aZ,Fs=c<,7M.gS\Q;]\\]T%#8nE+D;$gPM7N6uL&ESR=Dm_r#!RKN8W-aB<^&>Yd=`6["(I#F^Q
%NY:!:J.G03iTusJU3\MM.9IZ;Hid)'o@W.K52)Z0P&6(ff)Yrh-0[:oZ6^:'=%$udR58@TYYS>B;1jNMJ[bjR'-b&7n7W03Nbopt
%"?N*l_r5KcN$4V`Qm;],#bk'M"Lkfb)md]`a5+n9gLQlI?4kr%;c<-qCVqP.]rA9kX$2U1?UZKPq=;B9V9[^$=fckI.o40K,rZk8
%XPj6+Q8C<`)g#L,U^jqCS0Y.q1s$&q__kSkm2"K??0k4h#$G3Uif(%(/_KRI\Ajts%r3b77f:9KVdO02o\;k0A5NcCWTGHQ7lnpi
%>4Tp2%A]F%St8p\Quk"*m`4.X+<`'I1&ZF<TN`Li"HC?nL`fBk$R3p4bTatK&.Z<`6S99*Y(&0/'%,#n)tHRm8laF;>+BUF$%BZ+
%(?Ae0A]g$!?2pkMFuom3(Z*CGJaOH]p/Jof:B"7A\1pIDM(mG"O=Kf<9`u(ocrjF-Vi8^7'_9)CGc\>RPm!#s_`['2]qo+tEL=)\
%h8CMTK*,k>BO6Xni3P/;Pr?+nQOcuNW%8('IAO9Lp&O%,Pu2/S^'\n69QGl>)ZmW0c+bDu@G8_A^1Np3$u?2D4aR5m7!Sj72/,YO
%_^N/5B(Qe^Qpj99:^/8DVDT(+XI7#h(sZ9>2NJ?cVX.!mAa!E1)[pce<;Litf[rn0L<:)^GYWfP?%c(q+=]:%a#lpC.6+TcPhqZ;
%aQc^`+*Bc*&#sZKX&(".Gh>sG%P'.<9q]IM?cA*Ii>X,c24`j_`^8u0XbJ'Wld-&#R6a7U_I.9IcGr6l/poKhRDM3mWO],EH\#oj
%X+m6X+LQLlV?%W\Zjb_6c8'1`/[,^7]<an6at=Ak(t<;\.*LT=oOc!^m."/O2rZeT/YZnkGL.qcb7`KbfE:Up39[Gt`EMY`"i^Wu
%W*1Up$645CN,8-qXen&W5#Ne.N]\oA3XKs<W2e,#:dTZ3FPX/62"?56/(p:UX>R'mZ/HHe`Ya=nP`X`:3QNgI;L`.W4eAdg7+D0q
%2RJRRL;MEVLN2n9LXoEAV%k2SLE>X>-"<egmG0+d$dt$,._U*rWmZ2I3q4BP8@8s(J:+WSa-DHT_M]U(EsRt476j6s%/0lHM.HTU
%CCmeTVb6-$Mq0CXa$*u/bp.JZT(_"I'4e).La`Kj$bBE67MA?IKCgDXE['p674+doS.rJ'qLm7+/>>Rq3Q:39/b;t=,>U^?n.GrG
%!+Rq>1mR"?<@0jpRt27!-ooSE.U?lH7/&_I9gWR(*^!Njq)$J&)[V;!kdAd$,k/gT=VL:18i9:h2CZ,>kke?D-)s7iA5AuPDK2&"
%9bY*,,NA&<,#SuR]JP^i0MKn"("!G:b[\s"qQ4]j`D`T@?>6rVd=PK,!l\*^K<?tt+(G?\b!\\<;%o)-Xh<Z@$7@t;HLPAtK0c^W
%]1K);cV8\ZB9c6pJkqA$=CC!R71dO8c\\Oe/Tu?B.i&:$9[:F:+a^lM/MmH/H;H1C$MN#W9]]h`6X]rNR`D,^],U1)@Y:MkY30RN
%b&j@\l"J\=N5mf>)pt_/CeGfBOb6c(-oMf$M6#/\pp/8=Y3CdA<<tbRDVEIZ;,25ANE;,hB(/)ELb8nB7cfIYgFejs'`+[S-rnqQ
%^_TT/Hh>+c"/-%^9I(fUSLX8kK1%H>3iJ=.HmbDT#^t0ccBtE*H;;s]]:.tIWF!eD>FmV52/7l9pi;+g.$/i/p-e02Sohf?4gjrj
%#_<_]!?^MXCj$TfU/#[9Brd6Anl>(9M:+=G8IU$TG7TYX?I?)_)CR'_G$QHsaipj?0i=PoM_uRb:g#]e/X0$(7AImAr-e4[+i9^p
%Y0et#E6u2uLR*#rdFe`W26*%)>Mk^%LC4bA;F>6(F.nHFCKY^UZoCK%b1UF/!3t4okaLQbg!gG#LI78,20\-@$<HPQ!Cu=+=0O1?
%:<Ze?^)_e$2sWlqRroq.3M%^EQ)O*c*=X9_A&=sA_A!F3n5+?rlVXpo;M/)d,+HQ<2%Ma;$:cj2">Mie/X%?6%8\Z,Ph(1>N+i`[
%M@;<'@F-SQ(rQ+NT`]L05-eV5kS.XIWj/Mq(c`9InrD]7)iK:"C/9eE,i?g;CJ['^R:obCf`?+E9Q-Xu]#;M/b,TuKVAdD%YcaC!
%+(]bbN':882EF=F,I%h7(*-q7eD)7"*(-5NrI_i.8:s30Z#;GK/El/MYY5II2W%0"l`&*VY1#aJ%(=P4A:j"\QA<U35q,0dO9m*<
%Ai=&D^*QH,1,W`JpFq&IJaq@U#U4)5U?>56?GmEk$_sAB(^pIF_gpd7C^'q&4BVrD^2/ou)rc9fXCoIpj5TeIKoD"K$^N"6$.DQK
%\8%LROmo+?.V;s?lDhD5f$<URJ2kJ-SY;)IILVKJ.<C(A,*FaB%1c2LAmGh$d8qo#Q8#Jj0hr^jWCGL=Bu<?g=9[bT-'nd]oJ-q_
%jn1&c1&@n=e1CGk%uhV;#GE)"Ppg`1&U&=5b<-[gTBo6>X<C:*AdRfN;h1:)-u,CABaXTSlka7!k`hTFcm*;/;^sToP7g#LRZW,_
%`QV/JqWFEh+Q*;0,KS]GSj3gajR)Q,RCB^*Qq6po,*F8!Chm>9Mbn/sgm_t8Bp8tJ;gpf$Vgt(&(R8=(<!/9#lHoq`JYBp7iIGTp
%U_"BsE/705,MRqI@bJ8iZ8^0HUM(U=([2k?T6/;td:@!6!l_Dr:f<QTp&RY(CCDHbcO8^Xd7b:')Ef5G1E5;!QkHhr?TQXBe80SV
%]\&#(ZtbOp$"R`rHNh;WNE&;9?SXGS"i^!=9PKB8E>Y.[p8VL<DAMkY#@K^-qoPZUVj%E!DhRgK8ah;fj"laJJ<KVZP#odumk\&7
%oIcAUY>+qLP&G@eM^/[@^2+45X`H7XkiM0T#&bm?bq$[J"])PK#=m]c4mPYs5T%@dNF]n[F5q3c4bg3]8.F\P2qNoS3';es>6^=U
%kV=-J!=P_dAIP5fZ-3TAVB'$da$)BkiG3\jVi2COF05k?C#i_lRg;HE:^.8Z7$&FCH'tOElO;S0_fRl!&O]m0*&Ao)ZZM9bIiCp'
%P-'H&c%&[.0le)KfnTrcK.@MKQ!uQK`\f"_To$MrI:Rlu.VS42&>M"$J-8X(`cD_$9%GfgK-2#>-Ob+f*$cdf/OT"2%<.84U\#r_
%ZZ-B`LK@mh;O:K)$;WRAYgCdGS*W*DG(@2:4pG72JRbZ)?]T;1_TXbSAIOOH)U0mH-Rf7XU3L7t#Fd'-/"1^^$gc3j=nPL.j2I(<
%.\;i(Z(<oU'GDi*EK%rt\*.\4Dhuf#&o?\UHj2T'j8`"@JmSieJ,$*<"(@[$Xl1Wa\1Y%IL(@G[^1GST_SS$>V(fpu%7"F1RDhZ#
%=h4r36>jnrB:-"RcBriKGX]IO3POS@17:Nt8(#f6Mb$,J*bOk+GrSr;MWR2M6lgDCj4P"#J5-1^%s%(?H9q?4W,Itq%V2!1,&>dm
%=r,FRU5ld^0N\i[aXfh/5QYL.$4r^gL.ff.)'&5A%sS0uLP5O\Gf/HP:,9I)PemUa>Ai^n(?$X/R"QV^X9[a1Mc,)ten*gJ,ifAq
%L=\-<akKR!]X/T/BTeU'0o6H,qXe_b\h:8*A8W0K?b/XN\Jiru?9(&DF(Ud^>s%5lEU;=4JQ_"K\t-H(PS?sMjR\&K>jVq5ed433
%9ff=g?51<1lpa:@8fK3j]:,qW*)9?8^t]RQ:s=6G7b!C%4q6-^L@d4.-JN=)Ni"R5P$KMNH0U>C`Z?t]d-[:pTX#XIWsr?CCQ3<I
%^!>;1n@1,C1@fV1VW'%[O!^8mLdYipJ.55$:95du,g*6'Y'H!b*kAFMa1%;H;d:*\['@\V=?(3Z["U@(('5qpbf4Ae``m\JM4sG)
%TVqs*+i2J#J>WF.,dUE;c9GB!\qMd!PbQ9"U71`%(8oj_,d*k^8B=?KlIkgN..1=l-OaKudDDkR1c1D#YZ<OZ/<EMAIL>?oe[
%EirRia(.F:Epc%ko7?Ob*"S4m&6Z[_W(2q\L++2JAPqW0<(\I9pl1qOMbGpCM&)=)#$4D,d5jctiHZ]/c"]Ln["8QW?fYpM1,;%W
%&GBg1;F?9T.d,WdRMFEj7Ik-e,-o\G/4shGb7K'(n7t\8;coUR4u@1<1*o#2SYOA5k29,TZCm%p-n_lIS6sn7Q8NCG?dTi-Zui<9
%;)X>Y`ABMVPP7#p31,:q*!Vcual7.cB0`F0>tg%(5U8e8I2Tk-1l@D-2Z0;p+pIMhA]HIL=>lf_MI^OGea212e;tE^N<[R*eTHh$
%6q4"^&42Wj1+8b@$$)g=_S:@I^.^2geBIo)0a_PO:SYP*Fs1IPLZ*\4k,Ci%?7o^aY(YUSVmJG]]*(U(k;eG@T<^G5YIdTU!:qmG
%G-(1.-MRd4/gM_EAm$VG^WDB4_*i^B;[_+fFHQB0&0Fg;@dYW4hse*rTLdC\p1#`Z-T6NAX1bR[5?(:'k`Y*)WQfJRFMZd0F,DJ^
%!sjuGi>75BTUok96-3bRC<>AW7(JojS\kRLVN.KZ"T;-lJ.-WEfo\"$[i.LSH/&"gknIn=:$&IY17M.3a1,]\3(e2dBl4n#S)dja
%ITafPD&u3ljc[Of6/snSqH[!s@0R*'j-t_^f]J>n1s4i^<=4nbe`bkVr!72Q>2_/41O'OER=328Cr*h8?:g>G+D5>KE#"/HU2L/u
%oBKi$[Eh]PkXQHI6^Qj6m!'+T)b,%[+<@?KVr:pAMTI-Tq5o;-n=7&-d+uQQbA+?i7;o/$(3,GrfSL4@XaiFbFMb;Rj8e/bEDFto
%h^1#:U2O6=c-p7DE6i^Si@C.W73dZc_M:[-oqDXTM[R.0Kk"!j^o$A)AB1a#o;!s9p5,X'P=:G5O]Iq&!kThr6j$DtmISG_]\uM,
%c!dV$.G/hdo%"CE@GOd#QUS2W+g?h;$)?BVPObEJ,&&1n#a2tKHSP;PDQb9>6!/u$.,I("P(@qgcR2@6jV0)C?;#VcFfn<EDC,Qb
%Q:9hr-RI,D4K>hgP8k:#Vj6It3l(Zq+uEbP#K]i4H#\ZTQi$,HQ$nt#)[fFl-KF8_JG=e]Hrio2.c;IN)1.!K9^ln'o4BhN:-$uU
%QWQ+hmHZ?pKI([Rk#,1!+D$I4P?j9fGnUrG7B-*)V8^iMB[MXZc<./Z0-&Td7!3Jb5%M3E&X8BEo#.4,II^!K>9'1_QFLV]R5-'F
%RN0nGpl:Q"?_oEFhUFcY2W3Dkks8Z7;(Y6-2>XWC:&UjOY/eM]ks8Z7d5:h#<Vj#c:&Rkc'V2dO#(/^D2UV1S^.P#dhQ),_mFfq!
%BmS.(-CEo'Ft\9soVVW81b"*C_dQk%M;Eh.mY3q_Fj3QaZf]u;.l^WWQ,F*lq"1:Wk$dKrF@9r?8)@uMCC8Ok"u91Q5)_n;e;+bd
%+JSZgFhZ3$6!)Uc5]K1uDl[R<fKaFE&t1E/HlPP.jVTme<QVF)H_Qe>p/!j3%EZ9PURl8n;:"^^<Fnr)]P1Q.f:/eCe#Ap5<sO0&
%1hKLfqZA3lA.<D)`&5-UZY=a42bcr6kY9Oipkrk@`&5-UZY=a42beXZe_E"K3RW#u`-",kZY=a42bcA78=!!A"rnN1[>A\k;9u-(
%63#cPM)NE90'K!`$%jDSqiRjMZ6cj`8CEVYG%`U]88:=oBlS5!rO7(7hf_:^OVAL&%QETE%QE[`7N_:odqGcFH/!FMS,T;L5qhS7
%*(V+\@RNrCB>*d^#JE2PM4JkFU9U8?dVu/W(M,[\:6uN[lP1QcbmT;K&sSI.E9AN;>oY#GJh03l`c,T#3gg)VEJ_sf:);G-1C?%,
%RWOUR1[Oi21hE&LN"UL`NKdfa]s+GNi"Aa!=SZ8U-;r>5EG\Fnmj5e,G8.m+(.P!>cYD3bXV=j$mPJ7/m#9_@[?Y\,I+n!h\l^N.
%f/!`_7Hj&C8Jd<8/I0"FY4ZV"Y&(DPVISq'U0H)W.V3o>jAI_YCc,kkeB,jWE1PRN8"JL!iDWOTd76tjSM(sfA=ZfSQ-cY5?CgkZ
%#U1G1g]_-*7O>4RP;,-)\G`f%%+.#)*%c%hR5qbNQs@PO&H1i1q's1KaaQpgTL@;q'%"Y@fGD-3Z*\?qKtcg4BQ;MCLt=dc8!P24
%dG\?S67ln[dF%'fg6X7aPJ-R*fMtGd$b4Bg`ORdt]B[BUpVUGRm!qKZ@?]3PPtb/9V6Hm"SuX)!l%hU-Cpt]OKgj,K72SbZ0Pdj[
%\m']q?5eQ1M5"CLV'4<UDk/.3Tuj.p2Q*Pm4V7[&c;&D5YXcnb7Fuf[>DcZ"]oTpR/D_F?WC7mbjB5An-]/n81JL)BkFZ;IXWo>K
%qUJ5<VJWT=af50!$tSLN,T@W]?Nb776WF;0@o;ke&M]M``=a9P_2VbrL65#a2-d)rej/iOW`AhL3N!Y<eKVOnnD,V4a3qJ8TIK_n
%0)+RnCIqgJfhttG9;HgKT_K."aRD<YkY`LRJ%5J-JXmA(iPjeU387Qq]NUV*g10V2He]2RVMYJn.$uWK'O23@PF=B9!,HsP5SpL9
%e2NBIa>qLH:@VQ<,\SmV0BBF8Gg3<-%`_=gK%&7dPIPuRGIT=V82t#:Eui`QO/raids)Lcb>puQ<=rrd,Ca2."8gX5l6ic`X4[kX
%KA\K_BBHk:@?JD-drT<-=q_#`!s3^aWrIIa+J*NE.n4R1bu*aRl]^\`*bCT.153r/$CEg""N%B&7r[6;3,NY`85Jm8ft]B6M/q.W
%Tc(0gOlofojIjHNRgch&"!pCH`Af:q0<RmIR['Ar&\bVdr&<A/J?ET<CXKgMfWe5HHXR?HL/'\X:d&.2ftoh(pI('7e3Rin):h-+
%p1/Sd9Q]U5@'aO![RoS[Hqt=E1eC\AcG_9FH#c^GObB,M/2>f,BV-r$hmPMjYT+`%fj!i1=[[]m!mNZiBnaX=V3*/dFEonn+8.h7
%NEliK_lgth*N?5A,;q[t=$>9B8K_]bfqi<ph9Y:V&k9MSG=D<P>sQ9j!'MR>jC":"2j5caVc-[\+r'keW1dXA-+G//(/8k&P#Aa@
%$b^dmBWR"gfkZc9I<qa'@4Fru,ZfmeH*)2!4f<(Q+?#N5-`SU7n;5faMD'mSBbp_F(\'7tMMo8k"k?GEX3-*b,c`:T$F6C/,(CuN
%Neg5MCflTZ/DfLQQ$9Uea'bMNb"@(nDg'P*`q_M7-3h/"S.Q!"FE'3<T!X6lW<ANqdttdmG]%j`G>_q>(4Hg2RVtf%qAKW%dDa^6
%*.'FQiZQM_bYQX1R4hsbbV?-Kq9!u)Y_H9P9ip0GHg>+G,b,6p1WD;L]JZ0;]9AFD:?&4MKcqcC@S@1G&.AV,N*<Rr;J,bfN2%cP
%k\Fror[HT-B%-8'76KXdn(LA+P%4TU13.qUW6mVdQar]pL+I!nM'c@igr4Vo`1m6qam$/!gZZ*-j]X7lW*,A3BN"W2'Fa3nHMmmc
%LZ4BV>CLOR@hsTYehH=Z(iENacCgM*Em2Z`QBnP$Y(`ftIu8JjMae(hS/PrE=fhYP[cTTWE7<h%)+h2pn-=pF[a$:U/W%cPkl^R\
%lCP@=Ee=u+!(oldnW?/dUs7o<_r`4T22a6*fFZ./>hG3K^2?Lh65.0F01#kV\0YVfBXED"p'Rjb""#6AEs4$?b>PKl$Z$_^<g1J"
%CoM6NAb5':70JU,LT;AYWH00.:Z:\m[Lm#M=(+ih,cWa/ng/6I'Y`q52,.G.3O&m&`I2]_b+XWT5<`-"UsnYqaUZ1J^Wome',0;(
%7$sJ3FbHO8&Y`KGg_?#@)7$U:AO.[UJ@\4@35a5WK8_jbYpg;RK7#34LD;q%iHNAr@XSNuDP5M6mMpjhfRR2mFt117ZSDIW\IR?'
%HBW;09_;%B:n4MP!BP6.Ui?QHj\,qGpl+'(6qIur%'KbY5_cre9jU+14MkT\_E0dX3tmfe=XP>TAoa0s?:&l'2?AJo3eio.3WuXH
%::fo3(:L%3+n.nKKj@oHZ]'Q03@Qqp^F+2#%E??n:'i$m=!\&<D[Dg-M84o]2+m&:D/dX3cYl[N7[bibTF=*:L<2P:r_k1t-_Wq7
%.m7sN#[FV`B]Tr<`,cj'i`mmlL=^Au'[K0S1;hN!27iEj0ZgE*&9^pmV3;*tm!:*9Vgtc?E:t4D?C\-oOZ$c:T0"j<G`>H])4c_V
%?"\T*eYnl_=&f_+12_NiF,dmod72.?A^cL<^mh2+IG"a>^:`t_1VIR*=^g>[m=O1G$%,34$Ld3-Pn%ils5mCI;<W@^+1L-6hL'Bl
%pYE=Yo(-M_hnOF17]=?D^OQ&k8cSdnhRn.-qSbBPnHY-2blJDS^]'GR:?$0hNMH(15NBNX2uNBQ5l9#)](sIm5Q$_Wq$'%TlX)od
%&tEgJq1ekb3\UEl15;RA1,SRN'fgQb2t_9q@[B&(GVcr*1\cM&3@6(oPiDu/6j*i2grg2VNW9/MTRTDD!E4PqKRVo6\9!/uN;?Ha
%!=6gP1gScF_VHl:;tgrrd,.MB$RQHi5m<+$"V5Vr2E&jQ["a)K\[Z7'+#=Li#(AOEXVRLj[L?XPFN4D9Gcrp7S[g``#6]<3Th$J*
%i_`36\hqiM+E&d^m)@`'Q/H(FFHCP/TMsQ`C,,HR/`t=Y.B\?LM8_T?QTgEFPDi^SH/E?`mg@[J*ak_+[_U4e0&toReW&Np1.[f4
%QRf!hN+)EnTHH;W,SMA>4!N,/4Ap%cng'&I-X?dmj@,aUk+<\A=<G:7`F7/X!MfR';$]^LCT'[VRBl?;c0:J.Jr;S"X%c8&oV[`K
%/*_g)K`j'R7<!Td11?lK._T?K&0@Uto0gWoL"?aD:P">;>HcL]Uk9MQ*-GG3[c'KVajuEb\QjbR`./V=-FVF">!b!9X:OMCP&4*L
%1fmu50p8pQU(g3'Zl=Cq@HrFQ'5f7PQV+B^b/5Km&@)!U00rb%X:$)S>J),2XGXgg9^^N"e(f3>[/ljFHEoKtGj5Z6qCq;N]SIgM
%X,q[_R,2s.$%']\12\cXpT+p[KcmWr2%]D82b,:=6jk1:A]:sR8]4MLLGc`@;dL?$,tu6N+BGga]K.e&1W3.s0"9&]Wd$4?7Go1j
%TaEN?.Sir3'chS!PYtNP$l(/9;8VY4`BJ3meR6*LHEuXrXdXe0A0I90JPE`Un5SctDC1UZk<8VHn4X0/@IRIBNL:@+q'D/Zp(X55
%MHX\l>S,<,=?oB]]6BqR$)qQ`/+S)(gCKZbS`$TF7&,hm,$M,aU1].J!St(8KS]h!k(e$-Y0heg`0;QN00bD)2*^FR@gG4n\Pr9o
%8NnCDhMab&;>(':OH6Z*aC0pgApWc3kn<RqoZfIqJ<$%'iuf$5PfNkC3>2M[,MWT3YM2XSl-#5=M#17qqD-q+:AZ^)$"'Xc=a\%Z
%FBOGh3oTo2b`gUC0FXggOpiM.<m!;hRb`-qX=["e81t8/.?]pngDi#o<oUK-:R+\3q<3+p1NOl7.hShCrFWP$d:hX;m^,-@JG,5o
%Kj;<n0pdQ-YUPrr>^)pc"RB/JjD#r$V*K>fkcmg'-&/uuoJ:L:m"<mUd4uj1T?h;0Y1e4_HYlG['6nTDYDqV[dqbJ6Lp@RINuesC
%E/P9;ejW:DQ`>/e_F2UJ+83>Y2I./@&pPOmgOU0)9M4@g>,'J'ISZ5(+Tk,F'K6Uik`ADmUG.hS(dp3>.FVLeL\YmY)J+pSR`u.E
%X.m!_dDEH%k_*:4DER6RMQkM%"]\GN'1%Ui@cDn^+AVJ$>XMra$mc0d=+*GK6-p*?RM-kT6(D8ad$@4c3'bKhoC4-+)h]'=L<)<)
%T6*CJ59LJakdHd9'D5>f0ZbVK/1I7;p=e`oVIk.]WefO)H'W`d'!K6r)fAmOBl;E8q%L?u5M0T99i&pICbMIc4;=om.re_^bd9uP
%Ah1DQ?5pS)A`AqSR>@-iQ5io3gh0CV*Wqg+g*hJ^D@De@mK>tgn+CMb1cZ290h2s6Y+J6pZ[9,0N[(cPW<]kDZI;;SPN[ntE7NrU
%7>3N5'-A@/Wpq8r2T9p\&'IB%/90B/_uO#fk6'DFKGDUW-UdF/pFto!jXJ"7PEN,S,3bM2dPGg&;ICU>MDdLs8sc+\d4RB?NfiOl
%.pn[cZmLB^hCqa5;?D4idX=0r9hD:]MGpW`CZ,/]$V/+VH_DaN8$Vrj#""X(_]Iu0R5Zp9$Fs(ZKb8C6)QsJ>cKIac"'UYOLp'DK
%9e"FG7)uiEpd#k<Lh7"^=cn%<M5]1(W-3_[b!sQA'=@qiF5e8sKWq[VD$:^@Wh`T<U@sg'-Ir3!PEIART89(A95Y`?.R]U%o5)Rb
%[k/eXMOsN@C\G%PX%=H/E2YPXm/A/;'!VSUqnA#Xa2BbrQ=`GdkA'Q>dc>NWL,M6@>J0OW+&@%-Bch</0^+[V^c&g-0rI`CMDD=o
%NJa27&&g`u,*f9/a3nmPNassrf4ji!>Pl?<R$^;P&EjK6q;6@2!'Nj@/M2*l0D#t#ZDDq`8B\)Do-TZdJ6/Q^*>`8];D5m>ZRf;f
%G%>&u/DjMm`.c:11"JBD1boo=#J-q!M:jch"a`"e_F&PTHCel-U+(rL!JsZR1U?d!-.r2^rS?/2?C-@u1m]I`&.rDp_AJP$A$2Ys
%KI_ckVj[pY7)oI<$pd9JLG5t>$cMp@npK.n%2:3cFFlq:Z,ctcEX#eSfqjF@HNEM4H9*goO$6@mP=V*M56O,@)0Lbf@^-sWNeQX;
%Lt%H1J%?<=.&Iqe#qS\\J[14L;S7gh]'^kIgAfLUQh<gk>\Unkn:0ASJaS(l*W+92P%_Jd!PQ9hpLl5t#S`2.Z->_T@Tcb3qRL87
%:.0-%]%'hA1[,n*Gj&(gEFV.Of'eF_P2np(UZ`=/![18Z.!huVCJJ#JO\e>h[Bc>b`gN"Q.D%9[i8>!:MnDQq5'g)S?o4>0..:Ku
%Gd]LP<0PHQo!1ZRLf-pcMGhtnkX">0-n&PFVL$-pZ=d6p2+%?R=ts=/>&J$?Hc9.:kNgM4L+&.[EhlBmcr@0-Y"qU:5WL,k1le7L
%4_j4K)>dT1!?Gg:,$M$;&'Bp(gQmi=@cZTfocV_6O.MlZR%>K7Z4PNDRq+sqlt$^N'oRdBW:DM$(.<[h6&@/AR]K&A'1-Tmj@U*2
%(og"h<J7Q#b?r_lW<D;(O,_m)Z,R/djHGdp`C`"PZOt6u0u#3H>%/>)fm+\Mo+S;q]pV:T=H(;+XO:*4ip'[tQDK!J75=W0_9d#J
%0gL;&dm_6SUDZ<gAF0s1347?C`s[F"&gM'=*'N!DXF7u77BfM"9^6gUl#nK$ClREVAHLm'pP;qgo^%m@mmD291D!h$%)d/UF;B^'
%cpo<;-$&@U&193:M!nSp(?6_^p6(=l:;s%?gd/U<jNB0#4CMMg>0]b^:UOp`Go?#>9Oi6OY<3t0fONA%Q$])sFYV:Lbt&7bKZG"C
%&ju@1h0/!]]Zi?h@<LR#-RBOTqOhRK.8V2+<2Wka&;pno+B);C)DTKeQ"37s&;-U";,fh0C6"Z6)Y?JI2NhbO9_<IVk>E7;FiTtP
%+tT17WZt<#)Ka%50a,-53gDnq4[Tg=h.E@a5a@K?Z=:1aG#gW*MYF&o#K7mdJTt@g%\9,M=fS\EZhW9o*$PM`[c^<<&0SQEpQM;4
%:7f"PV\?BP5Mi[]HkjmCa`oO,&&p$.VS"g1&]@Mn_pI_KLCQuMe:UqM2LF'rJhW9`/r-PuV++JR*1>cg6!k*-oW-W(j\gA!'jn9c
%--E9ag)5/5Y[-BHWeQEd4\$7&,pPnuMs6-Vii$+pR[@<TW0GuJ*QXL3HGm%i\jd9JQ;,Y2AX18.-Q^uHAQ;_SmFbtTp/"%#VAZ6h
%et9:HREd:C&@:p2_?n3PF$bsFlm[l'MCHlJ6RIgC2/)OsLgE.K0hm6T#L<^IU99a5YR78!3F`dmYhR/;U=q,n>bMR!hoJJ3R,trr
%9^<:0Y#Ze_83:o7B3-aEI'0N;.h3ESbs[Yt,r`GfPH>Ik\2Gl_7mSklNHjLN<m:W@Ncj*W:-q5Ge<ZE\;e*m`<6Cp>k\1[U-R=%!
%G0hG=Pfams(fa`0n50eW-PekE?qKncqYZW3nNf<G-h:dcX&;&O44_hAI+a8Q8s&;UT2d%J9AJ8kbP5h>PR-j0,gHt_lnFqc7+3XN
%"m)/Jl>[eQ*$)O-<1B$HC\"a!C`Ouf)m2QI8`Kt"4B&/%fW./jS7E*;bdH#E&QHIem&W<00K;Bl2OSH[o\/pLc;8kD2'nX)]H?"I
%,2WrXS%5"A?8j<4h9HEB4B_S]BV5KrSo&sNIH/PLVF0AK=c([;BPQqo1;t7M?kUH#7$)@i]uYfLVQmk:?9H?A:F;_])5GniPYGD8
%Y,:ckfls?eF-,_mA"npZ!(D*o/T(3N4eGgn(&<?KQk1Mc,\em8clJY@$WdlM48F'XM\"&M0dMb40WZq$>@`YPL5A&UN'DSE#*YXL
%LPmTu(lSdCZ8&scGng*`3WmS6h2/5N3"5<<J^HUJg&1ESi&+)'Z4LSA&6^Te&[.mJgtmlbLg;@_57nJT'=Fd`;a&l*/(nGE`l!L[
%Y)TTEYVNN$KJ'h`dH,7%;>^^,P&d@dW#hKbE)E5'+p9+AM?Sl6(nW,f$XMd"?mU"*6C66maKI%Vb\P>`j9.\C!V$c9(Mhe;8`=.`
%n\G4fh*G"3PX+#>UC2Q!HF@+8*^2Tmjsj4K^7%N;@f%d:+p$CoLn]V;0pMV&5!CtbP;*B`1.;'gIXA8XDI"Q"OG(@0r<$+F&W9o9
%BYJr__1iE^4elV@R&VjLa(#r&/P$5)#0Ail@u)b@.FD.!,/c0BFN`A6X@*X;ks>Oe"!@8RCI0>'F&alj+-&^gPFEi1-Oi%`VQ5N;
%<9"^&df([A<B[FEH9R`T64Nj?3),?DnBn1iQ<u<lH;%[BqLkfMdk"T>[LipA09,),-^sgl2LBj+KFnm9q;rSB`q=AU5t=%hdZs$E
%"Ar;Q&Ta=q-e:Wr2UKCAFSa2tN=?R3$dm$N_]'3!GG%#,3Mb7SBjd:kb!_/^,:3UJ`_`tZ`[Ft;6\(sM7*^"(C':tC<"uMrj`T)I
%[?5U*MSthfEJ-JUEIndBd3t:$`hO4>i8e`D\=jNm/r]544FN9F0Xd*,.;Jljeb0o'(e!Bi=X;ZLS(b!<[cF2t:p9*e5Hu8'_cEfB
%IkGlR$M:TK4GJ-+%m%2Y6+N_B?SR_;VAG)^,,0hXZ,rg60g(J^OiAXrTj(VAeF<)%"EeLZ1'EOmSC_n#oF-(?&dJLP<QC#d=33jA
%$:H/[a9d/`%PDm8XO725+5&aJn6cYPe$U%?UmVW]FtK5<9_7P^-V1Z2"'J#]eC>dD^*%7DG=7YS6C/8o86]9*LZEh)4WqhLJ/_)\
%D,l$0RQ,L&K>+RUdjLipEk[];G1%Nn>R<8jE($aIj"0mIlQLJ*-V4>a)4+In-t\<7Iq9#k(aL]R1Kq/-K(-g\!OU6`ca1E$BFcRg
%VGT<N(:ZGs0ahX"5:%E`1qF$;aaf\_?icsOe@6i][1:$RUf_+8liEAi<=PWImD)GcM'1Y!-21/oqh]fiKsY<!lU?Y5UKi^J-UIo)
%J;h>i<kc*V?d=Aa1r.T6SudO"LZYhGp[[F=io:_k:F]r+38im\'G*4,^jJ4IEpBl=L^gs::#;K^7dW@4X0Y7bKm!&m!thFb><E,o
%K9g6(&&,Os@E<6"/il+m9^bdl(K%[P$"FeY"P/9`6\0p8pi4XO)kOuUT=RQ#f7VBBDp[kT:+@K1B]ML=jY:b]k98D5WF@(3+^a^2
%TYh*K1u=4KQmUGG<2(X=&CIQjg_Q3B\a:Nb]l"6eN)%]_aJbm`?OqI@&9L@!B4A%T7,fZriBg5`KJ&Dq.XFoU0Gj8pkOct^0o!rl
%6Vu,TLi1SYmcET88]9(?3V,bUT,LpeA.[MOirKR:8(/<<7$eY$eO8jA*@=fI,)0nT2.@I'J]=BQ\E/CWCp`lO3dC/%0bCA^P1>rE
%ceJ+KoM_NcW@B=mY7Sg%-UdJd0N$(<EMAIL>+leC_kDOJXX/%X0S+icL4EF&^MsB%7H_`q333pD!;^E#-sGNJkG4#sLrK<?H34
%T^q.T2U9dk<>roW_r;Qa1di`()@isP8;Sq^).?b>3I:/q-:Nn'E-'6@9S<-01@$hf1KleYYZuh+@A#q?,("E)6m8;[:F8\5AgW3?
%/fLU&k^u7lYk0%Cb%YQ*+aOZ.KXL<+Z.Xk1_/,+^E$@&CKAM80H.kTI9H,15lH;Tg<dBg8:@N<1[na$D7.d^BA8c+R'BFAWqG_BC
%VCGUfF;66O0Lr8bV?F#naP<8UU,E5CH(f:=LFe.C&A?TP\fd`N4PJ:2CrUe"pA9`5C6[:72^+4'Z@CGk*?/pO':,Kf923A07T$@6
%W3YLq;7_,idgH>h6=8o)4W_&_I2V[tgT5LVG'*4Q1,32aa^]KgS`an9kNX#jTG)u1M+40nL&P)fBm=Ta:8W++i1Q$Mf"8NlV8atu
%<Og0SAM8G)F20hm-D_8V*J[t\4u$FJ(iTrRkK^*54UMunq`ZVc'n)/P-)0_J&^_g@73dYF=@GNs%$r<oksC_",e@SOU>h((+S+s\
%0oW%BNT[aL>D@h9AQ+sMK8PK.!DQ4$n;aG,:Ks+>P[O$3DQ]>!^+DPh5h0>pr5eZ4;hiI0lL8K*`c,\!go<!s%bF2=\A]@2915Rd
%F9K\u&`uig5_ebPVT5La77@t=WoDSu$!1R?Ob4K@&&.@hLQQ(V@'Id9T#H**TjCRgVE>?70H%2f>TC(&[5;S?chmMU9U]7F@gGr6
%/_s).O+qU/XhO73<GW,MeV>Zp+\@WKc3<<;[>@t9$Isr<R\#(EorD&+:jPH<fLh["s'd1Ho!RPComj8"-O`a7VhP_;oDHNX`WRg2
%7q?`0nX_HUcV2`QK8I9m=?eW)2:@-V\D'=((r?qP#?;tlA[SE5S7^.jhL+q]nS]H:Ajo>IK7ngg*[#>,lFAlqD)K/Gco8W]bmeue
%'6lnglBNNeM2b2qF<I=$gZ>9WXKE>R5!gf`9glqVdc_i5a<snrptVB?Rb)G^1UU#BbE<B/jB=5?`8!?Cm+N!r]n_Cm9KdLFGDIu]
%N0(cB]E[G.Js&*BDk%p1H`A#2_(g%GRYou<Eq;]cM7/,PY23#.OmA*;M-qTA&NcSJ&P)2G]c@lujA#AghuZ;GquM*bbSirm2<+56
%dGS-))N5a&6C:*ZK/lSk-OfOr?GZl!W#@gIEMP;>?t5eg!0KRi73eiS846]\Kj\h5P^`6i)QUaj#RNZTUj"40l4&ke:7a0:H)Zn7
%D7%E:km6H#=6fknQbcn5#?S$dQs04p_q)LL4$r#>J^SSK0g`0emK\957&"g/Q4sZMF%ann[3-6u^a%&.C?,b'.5iLLVU7b9T\\o^
%E8?.0A^lA(G,.XbMAIqX&>q%W"pt\H_`;5.*K)+/gpnNcIpMstH;ZUQOhtqH#0=@6D50(tN1jD89Gbm;S-B*'p*k;Gc#]i"=3RdV
%%S2?]MMrqsCX$t_2FXnp!$c0^5>?rb`=2<:FI(u&-9ifSe7_Be(c;YfNZ8&&\@n'*,9d8]<QR.YhNG[fY]oL0;';qDdKU!'aLbiK
%?>ab/0WAN.\0LB!>'Ql&+(#G^ZRQ;qZB<_rP(?K]`]8@d):?rK&8%YWBeLN%-0c'cos%5$5U*b6<%OG)'#@<IVEo)S$0GT@O9?U\
%m5MCPNM*,f)S>#0HpYd2$Z:>?H1\no(30S@Me-elH#^t`L?WQ6'j)QJV]?pPa(f,U0G@A4j2#216$j)1Q^oY=>L,:sDB-I/$E:bC
%&s#CId*(G2]\5p02c0MG>P47ObFl$BFf@s%I81')(1N`D.2gol_s1u!#dnbCV+rWLD*>\dh++2C'AHV-l6k%96kg-Zf++7GK/j6l
%3AnC&lT$P(ZOjaf/36(r'I(2:DXp@3I3`"Uh-PmlV2?cK^A3HgZQR@!(99iqdhI\UU?HY2GsbB:fB)60*3)C77#YAD?,s!c@qf?b
%C:s*Q7\%-@09_\SiTqjC/R`QB0/'m!$UdJ@.Np":A^lYu(2o;@K<?H?QFZ"ooI<8=s.#mgBa9=_2=K;iE;n"%S#"u=C5KNN;S8CR
%/`B]3:9$i5B;&"^-96;&ju'SuJ6*q$0S]95@SXIPSe"l]$CK9JV,$WbX#6)s8BiBi_4udYZ%aN?NSd*9-;g4+%]k,E+h:7]$/GXg
%S+(A:o?TD"Bs?7MiK''1RWZt6ps3+BaDh8"KNtK'qabmE'2]<ZS4CSLMRLCH3<gIY)&nFV-%1t&b_q;hYM2Ufj9u0QF,+GN.9@It
%LPU4bYS6OeF:?1QP7sZF.MN3Q+_l;I29R5<_TtO.'U;p]?8T<RR0sDG40E`:+GP/ubHW-VODod\TbFI4*[8dp6UVA-[ai0$0FPE8
%,9h8g*mYT-!5>F#%H$I5-WdmH]BI$[B:rBeVfiI)5p<PS6bW^(32Ykr5SaXc\O'f$*]1,IgT=.tQ=QuX=L<jZJUb?!8V:W<qHkXD
%7?C+IE2f-]*>8nT;;8c*&p5HW4:.O$%^<8gZ_,Yd,YXKW!\B%G>9lhG)=+1C%Dei3mFAS;;6V12*1VEdOZ[VgQ';<o(5<UGR1Uu*
%Uli-V'hL`J_6/ZA-]#rKb_0N!2B;fd"4"B53aW%d@LS\DFhO\X>e14G)eY'?OOcf.<0)VIB<o%4/ZM3@HdIf+S:1fGUoFbf?RZ%M
%Z32rJ=9X8O33MQ:[T(#<Z%bdsF[_mRm]D]<^gnIs:e32+=(6%b3ibr%4t7CLZQ)Pc<N8AY!'5tORi7<S7D,sFhdJ*9-/aT:W'**B
%a]M(PBNqg6?,@.-/A'F)WQF(s"5lp`EA?.49Hp6.%FM854qqu$Um?W+ilC':LTICO4UrC#UA'G>%dU[*Xg1N7KRQZ`Ep67Ha_B+*
%(&>^qY<"[R$gOe=5p&'4<RSd1'f'a[VMFpY[)`AFP&[9SkWYCsSlEiH@b.dC3m4ep>n2#2\5UbpIRr]++UTUtb97T6E=QEi'6@f^
%QEE@Y#-CEm&Jn\^IQ9Si*_6JuZpY<<\+HW7N5dB1JZooi_6GCX99G=Dd=FE,N.n2X0Ufn.AG)`(_3r+Ha3BFe2"8')+WVoC8XK\s
%8k8AM<FgY(F)hN2cBOX@P<!7U4VD$VlZh5SnkBU.jn(DFg.]A[$T!t@,$F@h@*K:?%C%;qRj/Mh6r`>mb*EPhFRPa>b=4?:7]o(Y
%CSiO8OmWb\UNh9qK-I]LUgm-7MoD&jYuhb+<Gh13]4sLIJdBF1T\d3k(,,,lb_ep,+\-PK\`TN*OM>b;Qm"K])L;f7<.ipFfpL8k
%9SW_:OnDi^clZNQQm&aiNh>F-:_+K9?c4jDbaL<&)RCa%`]O9=ZF.\u!-f2HGDU/f)G3@pFK8nPp8\D(C/0g-!>'CG28"H6%Z4Lj
%&4]%)j8im17aake!-(Z#*F]59mWIPu"f%-$g_>R-0q,IK/;@@ia9A:5:h-6/nFn-$pp";0kgUH=b%sD5BYsjhV.s6-C(O7%'4-c#
%PVnC3Gc;i;3t.LA+@tJ>=;WD'M)+RmJfd5G):,A4a,thRQaA4NhDD#X\aK1geF@kq-TM>n)`/k!!tT6S`*8*T5Sl-snK8J6=Gl4<
%9IZ,-cJt%=_0/Bni[rCW6AHUa"@)`>_*;Js">mAk-6=A2Zpba1'u;f2jjH+IS.Gla5LrM;9IqUs(_MBq_0=q-6%RdDY*b+UN.DGX
%7ZsIsFcG[V"<FcT=+p=7>_%W^bF[Z[A:(X*=7LD0A?k9@<bH5/dSNWWKc%A[F;h&6gE`U"oWgpX0_.h.n$&6*eL1f#WV,0N_IeqP
%7ChEt)%7*'![n'O1Y2ZsP-[PDQrB_*A1DS!;B\A-b*I2fJr-4t/iH@3ol0cSQmI?=L^7V9_dMg63/@ZIEui$i7D"N]mc!-VSS2s%
%go2C<+HAGN]tb/4&G[SkX2jS3$44a5RQHo$HCTfI>N;MGlW@A7rF+0UTbau8cs[88qig3W@Oo"+_Jsl'M:(,V8O8MR(P]7n;[kX?
%Z-eEnqsQga<1tOp(o9&`hk=6fScrdijugXI2Gtb:ZtfQ$c%O.M-HVEXX96E(`f[3*m%$al4Qo^)fd$W^9`Yd-U?C4h5_t@D6+6n(
%-RH*!OdB1$DlKQ""K/7[j=)"o+'#5fB0;'?Gq=6$iqHW41%m'ndu09EJ/Qo$:=11V;RCHcPrK_^>oYlgQI*FHH#3m.!GlhEo*9!J
%Y3Fjm0R>S-Gl=RP"8,n\KW0.)K/M138dq72?o:Y5?!ANPIK4CbQP-/egD7rmO0/eOX2D+N8M>pE<%b%L*h+GFG'K2cZSDtH_PYHB
%.@L"!d!\oI9b1'5Ya#N&mc'80H;9U]9Klq%4Q,hp-[.CD`Y1"gYU5'dUUC0un90/AR';O13Sg/MJCLKi6^&b/B6tVM)%G#5l]IH8
%@lE:2+i\:@`#LIE/A5aocSW3e/PmHI8!#kHaTU0^KE[[H3u`LERW/mAf[d;36oX]t*Nr)PH"9B+>i>JD7[TjGFeD:hD"(X<nZ^3O
%VQlL_?q#<).iN1]4FI\,<G*Y_9op40+@m0!QT)U*5k7fG%3F65gse;-%hmp+ZpC]B76s,bW?n?[cH^;d7u@fo1;(OH_#Ap-+?.bF
%aXR?CS/!@$j!F$2pqkrnh(1TQ@RBEnYRt.(_'3=[C'=udTI3Mth+@.=m;>8@hP^^r+OtL]]iIl?7OQc:d\HIRGsbk/$s5?Ac)F8&
%8+M'slsgBsRHS.\E`%(3+M_!@P%`J*h[4ED/4>'[dFGH4`O)SYcdN*7]gG0sjU^KbVAss-M%$+I>7^!3VPR7_NO&1VrWj-g-q#3!
%a#OEtZhJJ;P%JVX6p#DjDaWZ9<gF_;G6N2@1SOmHe2[*!0*pH(+c]-uTW((V^alOt+s0Ym2DYLSo?"p34aGaI1,Xs5V?OKDlBpa9
%acM'^=UVPnOQa[ii5D;7<V0[-<QRq'lLMA<W5f!9^a\JAiqt5+[mkJ6!<P[]hJa1Ib=&EWjZIj,8Cdt)nd472`)9Ui-_^oUbOGG^
%+m5P@\`U/_:ae+=3g:sdHnlJG)%-NDQc9(\M1fLIQ3B<Z)-YD.V<@GW/*mGA!d[/m>tAp9?Q[PCHC3g\"@"6\!=Y<RF63K3Q[n8;
%<fB!YZ3Q32L>33,E?*R^R(H`=5Cu;ja[UM2P'\isYJ+++>4U/0mZK=j6k-D0>@CR$`pM^3[#W\'8Pd$/EVih9XH3Rb?AM_c5T%Rf
%>V%PiUSMnGa)XM438h3#.)<&T[*Wrm6R\pcBHaac+-T#s6L8qNVm$7fE$Wk^%X11UVr?3C`/J\Ur<;M\QrV;54<3?>-8P]<>)fb6
%`h=PR2:Vn@'0g,qbuPgc8euBU2!thlMh]Kd/crb7DKt]M>dH3_Cb@rPmd6^R^jk[=M9'rl7D\Vik[frcW8?d[1CYO+F!Y^_=##E=
%+M=P7%]7(m\WMQ+O]?q.d:P4TgJB&!_-T]I`rC-*?mM/ccZP\m&:HpLNt]QuXtJ!*,X>EGSnF59M,m-0>FnaETQ`g*p<tI4'6Q.+
%Le!/sD0f83b9m>P!I>'SlqfSsfLj<$lPb!]?Dn?Y#tfsQjX):nepna=<DW7?dH)pJ/Z""td:$i`#a1k7^)>;klus?QbRcRVp!_<:
%!8q7,(q8M?5B7ElL98%<9-T;@^50B=/J="h8@n8e<'%XY.UCbN@of:>c[:K(9j\ma8FaHNVRA=ma(jWZCT^+Y9_ULPS"-c^F+UK%
%?oK*n9-dM:2plWL[9C\^EbNaaH"dWlr+JFZdP0kf[J5=ofIfHA/>M0M_L`W#Ua$J0P[RtZ'HBDo4$kohg6L)'U8R-Q;aX8U9l]l=
%;[)Aa9bWg(;Q:jFTq0(35rG@$pP&F.UiP>r)AOWb#dH[2M(/^,Y7^iX,-@9>Lu.Z45SJ;F:s1rS6"JTES<H(&C(_)WABLJT;1lsu
%m.W5EVa0\W_Lo4N@#7l5%dFON0l"i8WP=BGOW:`\.P\,k=p[>9bdliKRs0mS-"@e6SA#*N.#G9dLkF/uJZL4C\7M,'\bV/8:eb.q
%,^/!@S=CILP"7a]#sR+]Hfs1+F,N<R&91rQ9f=/h`c/6GDAIRH1&=5.Q:Hr+&RtO@Nj-F>)97d$VRck`Nj,H3dQePmd<SB$8LXE\
%A>iX.hQg)HB;igjp&#8K8CE*tEZdDrUJVnhk_ceYaVqi>SOF%-\eQdB#E'P^ONVXqW!PL\&ZV$q79F41\k1,^ok=[dg1+g6bnZbs
%]a'3]kU9pV%K"^FQttneU)>X1;$Qc2Aa5rcB^f7/'.cne`IM59rWU!k8p&tnPP`nL8bV`c^cp$"BfgOWC(aLAl(sH\c4]]hUIk!t
%640:e##]c&0*B:5N(k#EEDT:B&`IG5*:g?mk#I!W_/6N9CrV73K-G#[ecBs=OMtnEXhr&\TL=1m1>K?;q#_H"jYgKY50jl#9B/ph
%pb?7-3+3O7+'EaRo"_E6J\UssiQ^RRLBA7#o>Z5:(e0R9;a4A6->pGhTp,R]Yi-<+G)%)1^C:Sn-C_ZN3-n9B!'RUCG_C-NbS6a'
%"XrudH.CPa0aZ(6&ZWb:-0nqA7Be@4R=@^TM<-mEjCuF`CHVfKK*j*n/2%7imD@0tp)YE`8L\M&S=*]bXWjfG1H8r'NY63Ydq#C=
%rI)(/8m1^hZuL^)I<kR>&n)JGm"r+NN2O=FK:PLIUM53(?s_K?gd<&sT_0RDA0*'0=`flDA>c?Q&LrmEi6\dMhBOiZVHE!i;_Uio
%oQWe,Ji9-.'m6Q:1=u\i6-[6h4Aq\m*_LblBD3hj6fG<g3Pg^i_VfrE_.s(a[0`4Xl?d0FYia=lgUIC-<F0&*IEn8]Dam11RMWjL
%S=m>^'k%tZJr+__8+6f.`21F[B^66QP/QsiLW$trfN_o68tT@R%h.A.NrF]>+X1MSFW30So?`)H['"kS@tp0uW_u9d5=r99)`lpU
%eIM-Qo#IrF%ZU0?_n\lQ25b,k0I;f;KXa,NGg<X5-^@??RLN5KC_ETln_6qMD^,@I$8Et`VJ4JkBNF1F=k&+t/AfI21le0*bm26g
%[fRR!DX`Vgr!DY>^m`;I3,`E/4NJr.X4>eJ[bu=.AQ4=o"2F0+%eAg-bW1V;[CMY26+flbp_F4&#sDo<`O&5JY;+o1*Xu4,"&j!8
%1tjaKRHaI^:"t5DPp@YsjV3'*+okn;8)Agi9U/grmlFhqX6=JB1r3>*NiXeb+rST:7^a>\Pi7rBY7V@(o9m!360meBi4=$?K3\uX
%T"tupU,Rl4ZZlS+O-7Qd*)r\h&CXY><H[gGFF\$mNA2cm%QL\f(t"?Yg]O^nOm>8SbuJ5F=tk2_Nm"LV^^2'8<K:bYlg3k5gVbQ)
%b=dRWN'$(%<cp_q)@s=p;GHFrN"*fjNMjQ:H%><J%-XD]1:u=Yfpid)<8/^o:H3F\@`h!@a(<E,p9Aa(6_hM>*A!0$/6LD@ls(C+
%PW`3K?6#0eZLpi>K\$P%"(j"JTg`1WL(9NgK+2#NJ.-.$;S30@0[P[uaoo(#So4`rnqLP6[ALB!%b`BjH_Z`7J@u6ugT)AF53TR]
%aXZ?8A[1P*GWiVh%P_=J'[A4=U1YH'NdJ-Pe1>@0%TlM*7f"1063XZ4["rH_/V-+6!6di=RW,e>%'Y)oMEI8[T0iim2.=RqlB%k_
%'>@\rGU6Z@D_eDJQghD%LeSd*l?%dsECq0kMR<&h4A?!\=\k\H^L\$^U_'pp*_il7WFn0`<,a2+Sf"/1CLM3t(P*(?f?Jad4+-Rg
%#pD]i#,^qg3]#Hb.6a:mk.jdFdUde.pf)!)'KQ\A#S&F^FIs-o?moC-AGUt@BPJD?7"IoXC45)m*#P\=PB;R?W^kIEC3Us8%Kf1A
%Y*_j:mkW9&$"6+*7%=<0\5LDsE7!=IiW+'h;J?iX%?iUOfiG5(Hfi6Qm@^1m8$]Es7#VG4:9BVsT/,iN`?_PjDkTJ^kbP%prZV2M
%1VaBI+cM!"%Q+W'&9eJ6m$L>I)>hph'b$%`^9!*%U50+3FL:#c"&]I</IlY4=5k8(h'cie0RnBWA/dudqXDrI4XN#:Vj:(K!Ao%!
%W"gL7KEJNZh)]KZqMBX(W5-8rQOu*?467@XM<Gu*dV/EqXVq[tJieUGBh!oN!<O(qQSB.jdin=EOBPXX3O3K]6q^2K-AF:b8Bp+n
%>&L+codsY63]24*5-nDs*B_.^5)BQjQr+lbLuIkrE[V+bNAP_B[Ms-rNGs9JAZ/."O)Pm+O.8)t^[i9d,]D8<m#-Zf81#uJ3r8PF
%"HQ]Y>!S\PS&XGC=>kF@:I3%=QGaMPoL5_.3U-_&P#uF__i$M'LfnAHknYns*=/SJX!`WJ>g85-0T!0T`qLiDVNJ[]c/e0;_(]bA
%0I@ADXeNWLg-_D.7Hu0G@PV;V3k4#O<`k,_;'f'@/%")c,-H?#"q,NaO8tchJGW?N/0;#]\bfQhkS$9)@[hPdgH'".&7.mh_5J&6
%HQTM*PMje#$CT/_LPCYAF[`cUaW+N+OWEjTO`n(LG6ecu03RY'Z=)L9mAs=?66^ia8G_HtkQk?C,)Hf@+b-g?gZX_]W`1L2%4MIh
%`N6YtZfpFuJq-l<R;:mE5hQRX?eu2R)n*T&l1m#;C0N0@"R'>DX0nG[6TnH#_2+Uo^U0;skWnY!(^e)uDU'dPUO9u-'&hDUEc<d>
%2_(LL/.nY4!%o]EgsErR1E!B2'1[f('nEhq4"AoOS6@Ju:*Vp0LLRQ4!_iPZ;uA=uVrT_6feVCSCcSVfD@'<l1gYn?"/?3&Q"(A5
%e/5['NZ.!tbb@7r(iU)+:0!M>_rf(55kH5X-7P,BC7Oe'2&^ZV2c'`XR\CRSSf@$PCjm)X^]\jrd]Jb7!8/k5iO]:-^,'^GCRjf4
%*j3YSZ]8fN83h810=607l#PWO+u\'J$Lo@iSZa7^DWiU9r2fVj1q(5@51oij[\;[T_gk852DD;2[tg82>)nMtIS>Is>D&qi+&as=
%6CuH%2-]2=P%/l]OC0YAQnb1"`,\S!$h9pNUDtj4]80H*9)udR-uLldWe-Y!a@9oL1K%5@Vk8P^/NJq&[tEP"$,u>d;k/Q;Nn%l&
%K,OaZ:W&40p+"T?"`G:"*7Qr-#c>CLYMR]FPDOdSE^CFMOC=$F"N->n@OhssQk=#uC>UD5`u@7CVt8k+q,9XG\8d30c*`tIV)"_g
%LqP]uAhp'WD=GJ[M/c7gE[t&?3&B;ME4TME:as(Yh2;c7ktRG=#[KA@Jtda70"A.Ll@J1n]B0cQnFkja;`h#nj&MP+%1lBh"Y.kb
%LmU4Eiu*<-;1XtWC8;Rq6L%Q.f_asX7oo\%i(B?4Q6%UPKBU\UVOCK?&B)aWMXQc@AcZluAX73=b,WO&[fIBVJ@)4Wb`Sbj0-rjD
%0M\pFjch1OAL6t8;lPYr9=/TaSo&OIo,#YTe!hpE##+iZ*Q/1/Tks=UE]4OE\S'B_+lCb?(-HM$I5CLUMt3<QCj!0&FF]Q9Es3hC
%8DpoJ:aOi":D#`52bd5rW:!_=am;s;q+h_-'Dg?Zkh-=U-'>-NK9[*C<d.st$P4=EfL<S%oQ+&<"-XiI"pfX@\WdH$'\n$F^,Q;0
%$.a,H-jibgmZCM1Rn,\AE9n0)b10HB]c-/a&Z+1fo?W;W5@)0qb*4&;F9M[,[n,,V:;oK"Q:EZD]1th?lC]?Q<e48,QjYT>!.%o6
%Dun"0Yq<A=CpoRCp,oM2?sj8bUY*QGW]ib)'ttm'Mhqb5#?LhW;0MfcXn;ZSX@g^B'q'RPh$dSU#+PZ1!658n")1#P=jGp3^^nR;
%=n)]M<b/dZ?*SqE7e['dE4d@RYX-le<m7fsQuGG2JD``aSu!>2TR'(Y7&F1t>^Jts"!C^n"eMYW0rChOH5Bqp9b!8'PAgUe;pqtP
%Le5Y%!#9mqo@5&P]&9/.d/g;\U9mRiS#ItI4a)!Y;ZPf&B.XLrB:u`M>Xkn^"/*+Vdu+7WU5OBbeD:4[06FLc_TuZlL)0FDl;;2L
%6)E_Q*i1_CL>?Yp`:^NiP*^qq?Y)YhodHA*6k)=fGT85>_fT:l`G/*%_tupe=W/kXP)*4X(k,1q>;[8Idu<J4P).Zm&)su/U7-iD
%c;K]VlN%IqX2A=2U/sSdFuMgb?r^@H&36IOHkFl.T9lY[OAb]9q0b"8`_#;hpF8O[Q0A<=-##]r$tu_O"="(MP:_A0.h3#MhBEPO
%(phWMr"q2<pNXk\^nH`)WY<bboW-KritE_`q2?sU'.Xh]c_-.EO#[?4K>O'R@h^*\UYlLd/Dam_h"6mAi-C'`ep"0'aY_h3CMu3E
%Vc!op'XcR8*T0i_-B7[E(8XH1]OW`Id0%[45`JI]O6E_=M/=pMG@'s<X=d-bQhl$l3,&[lqG[]U0>f)G:r)bmI<n;maB+<KBZAUY
%_dr=P0tKDF##mAp890H995trl;WoRhDNE^:Y5;3!"fJeo_T3-FA3"/<h@9kDFKcK1#0PWK-!aV6`'U8M7j':uA/L]>4?(0B`<']9
%_F,oI4fH1R*o;L,4h9cKJYkfb?kh+\,_^nlF\\5"pXFgo[fG#\.sMB*\l$?)on5/L.h71KQa&nH3NN&-OQSaVXsjK.,oEh*Am6N,
%"q9.!NtGOr5,LcH8/=T6\.O-Le\^G,<f@%A2d3JM6:`7s-WVeKKKgeU@(*j\*j,Q\-U9$$=M5@G#U&*GEh:]UF.2X9,^?*,8+^@b
%)Zk+OJ7=ON,UGX;:lg^KU(SuV!+q.9j;r,mXPBV>oPtf)Ft<'Ub<($s[fS#Mbbc,BSa>DHD_W#d@s03le;M_2Ak\'4p?^e+-..M@
%?."jG$4:K,&Od9:VD<ABdBURT4n1BMRbkG#M3=V=^"a2:9frR#hr]6GVY&H\((?Z%A6F#9[^cfkbi@q0'b8tL&L^Z\%9Xh9`4hBs
%(.*BkF^Um*3P&eSq[!5+lWY:)_IdD9$ujq^4QU',?!ipL:t0(gWckgT<+Rs+Yk[^Fei6Z*T3)_o!d-Ln\NcO=f&3ee\uC<1n"puS
%J92b_pol)EYi[1h@GXZRR-*)66?ENG-$2Wo<6`OL-#<0<curY_e.En:T/)E8fM=76.[CT>f^_YEZ&50!f[,eH#0j?+4]CB;Eh:_D
%\ZO"#fcjeFMOdSF*A!psGV9oQe9!qo>1<"V]<9,gZ37`GER>g!o90Qm(+Yc/<(s$//T-ZPr%a]+[C0EXT8Ieg&X#ANR8MZ_"/2*<
%W1o_>/od?jR"?8tBbmtGjUiJ:c'j/T>W)e?I)qLO\KN#bAul&.D$UcIko/OeQY%\VJ;Sk"NEaFlCR?"W8Wo$>)hUW>:_-!Z8#eQn
%3N3D6lqZ);i\Y6&oB2)21*\sd?'BNK^s0-Z4relCa`0B*@ZmGj;%7EM)A9uQj1NIhlE/@gQ>VI=!g8[N7M&l`TCSA_=W6mcg90QK
%lRJ;M$P$tb=bFXYiI+K-,G^X-:[UY*Jn0%RG1>eD834]<'jm#7#:GQ`^ld9V^01MS,8&IZa)BpmY)"E'7l=#MB[onQQ.dLe@LUn]
%@*tTGB=MG9ZNf_S$RXp1Ihb,F#r!kNQ-E_CL^IS+*c.<benrKl=Mr]joC-Na7?m;4@LB4-nDlPT'2O3"UDe<Nq[T>8UajFnTo1b%
%\QbK_(mZRG6.a29D"YM&F@4r`ADtfJ#c&(e\IVA"Bbe]LFIt-LP!`Hh'TVg893=!p+cVj)=!6Kk/+5o(4OK#l$[a!-2(aa+=u.A:
%e>E:L7=(Vo0el_5ZseNbG[M333]M:^b_:Flh%UI$DH@`1b)pt&,\MaSj8*j4[u+[k["i$-b%;&hKO$06:;+SB6W29_+<1FLm#b'j
%4EeV`TJ>Vi(j].tM5Kn<!lQqDE@C,E>^@es9j!<Q2XooM7\.+\<A8P(Ul:sZ'\_]&OWJL"1fgsbbE-Z5L]joEL*ei^8si/,!LDOc
%\L[%!]LO2M\cSEF-`bPBkBa4uNQ)8B"_(&jH>(B5TY2JudLnMRmtg[Z'\dD(Os>rKSVlLFXB5Tl<2aLlJAWR"q%b%G8C9;>K`X4t
%4%OI,FYGj4]i,FO//5fU8hACONR99dPjhQ:)PoLf&@;E=0j2\Hn^T+V(A+Ue-iB;'.C$9o=fEebA'G3&H_FA-%Z-;0!WCM.]nqf-
%OkOsegOplh&gB>.&QMj2q^BGL3Dt.hVrTFi\l(q4@2(teQnL!CiB\q/8go&.n/ZTAURY1IFtG0r"9J>F4PU2dB??[:LJaI>%hB89
%(5)tuX%mrMOuB^NXG/K=m?p<*+_Pa8_lgeCWBG">mKUa`Ze?5COQ8DV%f.klR*.O@h9IEP]PaR1%7Vu+6;Rc[/J5BEjK&\B*LE'^
%DQ/:;e!`a0jX:h3%Fir4>D=te?W&ea8M\tl"=S7jo%\:Q4?$o8L(%r[OuSf+9kD*q5lD\5l6D&GOP:WsPSDZ23Q5#S@>IH-of5'(
%;Dat7%!7@tp&])I8]bM*-l&@0r%sb&4I3=l\"+GN>/=B2/>N]##OM[Cbq<4S\'7o'!32aKY72BLS@>"86(`r]W&4g<$;Li-%]X>!
%AjA,B'3WiGGh$@r<Wp<C'cK,2@Z_?q'!iC122/g[1sA*1OVG3YL?K6'igN>Rk^`8+ngLsFYW0#<)q91hbA#g/<jE_G*K!llS96_Z
%n'HO4."^m`<8:]qs-OXrd8.Z^qTtC-O=,trc".oV*(*GFdmUV=ECZ6//WNVPFJ-aP[-ENC]mL8HSC0qek'1FLA-:c8FbCt/nI:jR
%XKYU&,DKVgb\YqO&b0&#:PBIC``d8-4B_g;K3efCnj@SY87#in&$9]4&9pVq(GuKRN;Q1(KVE$MP"\f1$JiX[4c!FjH'2-Flet-t
%q>i%>2<D+iS^Q]@0hI*9Ut(W1X;IU,>!b_S:S#<o:sd@*7VIB>6985Un,lPp&q66`Z4\WtBfN6O+J_tpb6AL%/[Mlr-bK&E#SN'>
%X+c4`fNcgmg.qSG"F1_D$>m-*7XGDM(1=^>\B]7SPVi"5`K.jFXG:9/7.YVYW4>cN6=^Eg<616oC`OoH2mY,"C-e'oR=n35o0Q=I
%1jQ)<=\9;-Zg,?SfEi0+1F\BQ-.B.ihC9b"8Yl[C8-_8<[i2j\2MNrp+M7GW1bEed->P'jO&[U(2@=JH39W661g;!,hU_dB)CGP)
%C&C.g#%g6J%9=Z&c'=NZ*Ya!TNO&q$BZ<!tUrHIITI9*2q1WC:k7i$WT?M"K6&iYgU._P[9KiJqP:[GXECd?g7\"9#%!'Q^)RrTH
%)Q,^*#e%'^YR%jHkSf3"7"'6:35?RILVVrF+<RrR3$FR6(Hib!JG]%cP'2i4D:EB^h,YPHa].(e$]oT`5f33P]`Cj-io2lC!jelp
%`5Ja%+jmDW=[C$V@^Qgh_Yf7hC7poH-L3WoYrZg=Q7frb#\\40Gn_fO/mpR*RkbY7@gIf+kcMoYDG%@]RP&kY2?H=hU*<h<U=6=Z
%b!i*)"9B8b<)RHEcu4Xs_LWd9Qs?M.A\]NkMdk0sf![fY0'G*!)aR)3E_Anf'<bq5od?EB&Q+F!EUA"';.9CeUr+lC_eQBC<^Keq
%ZZ<ft"uM;Kj\[BrO2oeaNK.1rUMLu]7H:c/,!W)KI"J*T3<;@,os:Ln[F)>2N7Jo^Cc8O:^C"YkJi2g1Q;Nl%)^<!1BN('7/\l-d
%*e=aFBp!!?7NI@@[ApX?8K?:7jqO8[,p?M\BfrE.,CU@3CpVhYGuAu[TLFQQ##*RNTa\6\9=P'imA1\\H8?he#p9]bnJN$Dc_uL'
%#aU4F=Bq44d[cV-ic9it6Ja4@22Or4Y,HS:CS6BtLo.E.D6@>80Oc?nHr2rDNGQOF'j54U?"k;%E:WXYTAm5>(/du?PNI*!SHibC
%Dp4PI$RnD0do4Id"t0SoRN\9F<6A)hBt.W',gOR$nj51O0pP=?).bb7`4>)*3*:V0T5De;%N?mL43JgXp:It9f0^nf?hXor7Cn\+
%D^oor&JhtIFU7>pqE<XojMY<*"?:%S?/&k'Cu6M4UpWUIAK!H[Ydh;mBe\pX^l#J7n;?X,`/L$<2rhbt_QK0XcM[;1!6(:[<:O!R
%(r(&-+0CF%nqV"E0G5kj%WDqrT<N1:B9:c>("jUh3VXIn+j#QWFZf-$#uNXQHDi5qSpGtCnR-0CfFIh5Wsa9EFmmN_Xa"s)$!Kia
%E.DMC<8D&-*m3CZc'Cs6lPQAG-B0e9N0oF;9mFiVicBboTZ9;:;ogpAU+eJ/8IkjE.In.[eOj:-?+YW%IhiL<$?i=Mn'Y_dR[Vm,
%=R"8sbKnP!$"6C=9Vj\t^5XT"jU,T??jp=31TYL(m8NIPY]=7'K7I=%FS_3u\8gQ^Dm-l--_BR<UDLQGYUik!l!^&<A;F-.')$$7
%kDEm"pQC<1NlPZ?0.i0_%e>LOY*QAXV\%66jY:<,?K"0_@e6N**h%Aq!>*cD0L9HT$)/GoRG%E':gVU+1e9t:_f[17Q\#Yt1g%RK
%BG)AkNpp@<?Jph;SK+Ll1fgZs2<[T;lbYEBW9fIafDi<:2:)Q=n,dSYRA+q@.WX-4EU"(,&,'jd`1J74equ`@fi8PnQjR\kl;69e
%,1jJ-?ubCn[3J>=KQAWa)NCQ9KYsjIJi&1?kgKr9E-S4]7Thq#_GkmE0_Ei"A>lE/X%1U-)8o0/L\62m9F_%c"Vnb2&\Zm\i(gcb
%\@N.j13kj:Eo_!tj:-,!?+@e9.6.BiBSL?J'm]3@Q6sSSGV^`E\/:\,e,1Hn`!M$',kuK)4e^*F!0L@q+j$F=paIR.IcfkGHl9)+
%4)T(/IPI22D,iY(h4"o,&X$#Ar9lNFIL4eGh!g->75,CR>D8K<De\08[5qD#R#&3W:MpRg7[O6MeGU7C12E?3Gl5WXUEYnXc,K)/
%3"<B,]%%ZEH6+6[j)YSa;9!D9NYPAfm)#CTCV&TZdFaOU2i=bb"0:IJ=9ic@MR[<2_HIY0M&&EqWBC^#8QjG)XU!)S`?(S%*j*dF
%111l,1@AUcY%&L-<*tB,aADS]j[YG"%\>D/bMt$QE&*1%4\$%.iOEY$3aN-s-KkXF;?pr-T.hmC%%=r_=f<,T^`U-rkg[0"n,rte
%FF,d[Pkq8V%=jJ6j.od/h9qUh<[/B.Hoa9)9!;W4K:H6^p?U]ge!o?6=FR%\OqNL^Jl>k>DCu/Di;!XLh*r]eOc]//!J1kRN_1!d
%Bk"0X"s%rU*>K1=/NS!8RXcHWR!I>1p"Gi)7RtE+&I_F#N/[mu/PR"G$LC"#Kr(AHSRN20WZQo##'dK,XG"/?V68c\am.WA*ZU,C
%$tV,sjodM%#C)=IaiW2DiL<7L:qRc!lPT_A@5nCH$j1f0,dV$o-4HQL&Vq8NH'7A9U&hNH"A<AI!QYCs2:l9r@la8dl],*+Om#*s
%DFC:R:^fl^Q:Dm>&"R3)ZnZ$e\Hd@Y\BGoC&Wp%f"t2F]i;RC%Pr(@V\jh'+[?PJao'1%mmg-lRFaof#&bN/)#T41-+p(<?^A2gc
%AM\).kc'/@"q]Hbh!%L#i"ZjK[5t>Z1;1KZ1G$\#ZA-FkK4KmPZB$^#0G^(q<"+aYkRMcU%;"f*YSg@J)Lop3q5qX\j+]u^\^ZY]
%6jrJL"u'(0C2-5.NRQLdPafIa22#'8/6U7%Z%QZD3OdQ'fR&8^dm5_G)_rdT0>#3^-%hh_TlgLNN=%QWHJ40qi:U(s&I'L,@qHr-
%-i.OthF%3,W%]fL_"/&hPX*nA!^#Q+#o9E'iGBha_LKgF`2J(ch7s>+;t-+ts)G'#iL=O16i%_(L9mrk^C_mIboGso9#i'mMP03F
%5B'UCTPq0Gf6q%%Z/f=b4VS,[,`cJ\!^n]/+0uZV6Vd;15+0`o'\3aR.SVZD<c/3='C6UbRrfjo[?48+@nR*X8L*ScSn?1bag6rV
%L%n9E3uWaleoe<p1VFu>aC&^C!0F,aKu=ml:dH:KXFh4L_@r1^&^cmFglBaj*!A0Cm)kl5'e#MeoOqp>?sii9G"_rlKl.!%D;^n+
%p2V+P<Z'sQ'jW9HdR83cR'\\^eiY>D2K3[A]PL#CK0`aL1e*=S?rsD*`":`="p]OT!n9Ur3@5kGjc1&..A=@b9=8jc-n@$)LrB(c
%Lo3QZ^dHd13Ln&A')Yd]e=)T?)acf\,>jYMI[r4eB(.KBFK1V(afH,V1b(H8CZl24]3-H\m?rP\[q#IpI7BtLTtr!]P-Aa.>L!(5
%O=k4`"kh;EG&A'R6S4)U7XG_e7[A[A\!o'?RMsK[Yaqdo1[h83cM2>&16>!_F1&#9c.!3>O&KSo-=Ilde5A,?Wf0I?XMD&W_t5X9
%nA`EiN#sWVW9=JgZolePMaUJS#055&\eIKg%<U"8dL9-C0HhstBN2ta\ll6L<QJ<?3:Ua&kp(8k;/Ph0_K'<n:u;R61W!+a,o7Gn
%G-md$<Lm]I\.Oi#@:GT)3r`DIClCZ\IC":I!tWn<HqB4u=E8?TSpa(oiH=@jAmdlmJ0jKZT?</$AnVk&&S$?,WD"J3cEQQe^t<6p
%St*:N0K569C3*]*d4N,)$Vni$i)+?S,:^VS/<J/#SoKg^KHkPm53*KLS1:2F.[qnSGFJZA=Jf!WP(q\#,kV4!Z3biDo\_e84(GKA
%"4jPO,"d[,d;),)bQcGCKJ/d#U"L$WR2*&h$BK%3qid0JLSc.R2iOnG'm7btY_uLql9c:*Fb1>t5^T_[?,D@>J\m#:]n:KD,$_Hh
%G&8@;515).:Z<@q8kJUrY<a7B"I)jU<jnrf$9ipepA&g/T>XqCaprenWEk_9@q*Gd:SY9CKqDC.N&b)"0_l1_UPXfQSUr]JHf;X$
%nY.mr<^t+8luR5.N+I2_6ielh5QCh(0EkB6.0S45F3J?<6"clh'V(ih\uCLA3P"9e;knq>([+A'N3H=nqZaOZT^`IE%b3<@ei6'V
%@<ma\i2]ur>kt3*!R(nD;I^Ne&*2(S\,ag+#UqaB@Ef#i5KD`OKW@A.*/@bW!=qK^VPL0rI`A`"MGWuMCfHJ8.="RMDhnQ<WV`2Z
%0R="dlYY$?bCqmu%U-/dpYp\,*jercI1DsYaV%f@d#FFF+[iUCs/6/M3U/q1_f[#O6S>(XH$0kf!dU0(O`C4QC@D[@MhkQ2+DZ@U
%FV$Big]8mpM%S$*c#@<i2qt7hdh#g.:t"FiW)8eqB4+gV@a*3J4:Z_0<$O:7StcOdq$`$'<?O[($Y$6:esY37LC\oFXXb;GCUBcE
%_i^h/Y[_$-rOg\(EuDqs8W@`=Z6M"Kbb^U>`?gQp`7"_s)e$l\.SB9`7QD3d<Fh1$9@#R1Ll-q`9EO)bE]9j/&^7'Wj9FQ]_:rq0
%0aQS1JYKtS.[d3U.VurK*2#PN?T5aW>Z]$aGX*bdS2?;"1RG4=R?uo4=lVHB#K/GpnMkCnHpW6kk:?hVafB5@/;IIj%/]uTIAC&6
%%0N@CSO6gb)2e25lEGCV@Qj8PD8qb60\(d6e<asGB@InkF?U?I'/Uo?dhhq2]KY+m'Pk,?;EkNJ9Z1J_`R-='j,lnib5_82iKbdC
%NK/N4rp;T,!'=sP&6<NWe[..^UP+H&M^:a>lA2$*4]-,0JUFs7ED]Zj*fn,go@/V2<$Z_Akd&0L0c2bP&lf/%l=;"KPRcYk6EoMG
%3L*eq5kmhk4Iq5$@.jXbKF!0m!*g!uP7Xc4?HU&i)/P@D>>NTJ'(UXBbt9ah0H"0u<ltYPbFT.GTd52:9l(MC%@XlQ5bS4gF.ph0
%%HENC&:mADA(./R,u;l]Yo</K3m>4$/4GT**c8N>`>"#qfO=rM.Z3W1keesqZGl&arS-a>ZQoW&fA\lTa:3g^lPn2QX$\b"'J7$$
%<Eta#JKqlR=Ll:N!V/:fN>lWsK-?WSb]n:@384qZNG2@h;CGs^:9<U\1Gij'7`'AU1-%W<rM,Bi03U%Hg*-Pkf:]__H709N!``Nb
%)%j?@iXYIuWS1,-'LA:fAO%3o#3-&6KD`/!7[_4C`caBP4,Z1gTu42Y_bLe4cN/F7OQ':FXBY%HM^U1&27DkHg%Mn!Xg8S9!`'U,
%&?N`a'%QR6RB_b38*Qij%YN@!piVd6P%^.+UqdNWJls?@G;C#3!""UFB=!:HNts4VN"kl6H;?):SNbNKp]OZ#d=:.kqLMF):Oga;
%]M;$0,ro8SVm/ufPZNBI6[S$Obn;QXoSJ%Z'*RI6L?LZnd'2iBE&hB73Zu4!SAi<`84^-U<7$8Xa(9$b>o'jt?\;Ye:+_+ZRYe1(
%/#"hF.4C>q\@uc[3dmn*1#6C94AfM:YKa"\#oH4f-Y*@>@2kJs#h%Q3VeEpZH+JcA&/S@rp5(M_Pp*1._SR-<jkAo-)A:1;[2Hg+
%;#e/Dp1R'Oq)4<[k^_2M]iJgo,rt`^*XlK%k`D%B0firM&AXofm"ZFl)eTBEA:>1%.nXleiHp;L21RaA<tZ+J6!'$,XSI:>Al(Dh
%5\4q?8-n`<4i:a`9k17,`j7b]8Er/P;M4(qB[E`o2ZT5,;)O(k;:V'hD^#r"P"+*4hICK+cp`N1_OlHuR?Fn-AH]_B?mdd@#7h6i
%29GcpfTW,j>,AJ/$TuI_BS\TIS0lk(26:U*&-HCD)1OqHmLdSO'<MmWROWIrk[@MnRG'^,a)KjA`=XLFVM$suMuXhCAqFgC)&bii
%W`m5J<Zb+HbjSElf:W4[4s7IulU34gqG_pUTS@'G0rq6ie+2>WbV#Wj#r28Fm.BXN,2/#s[;)5A7RhDGZ_ZQt0IS4(9osbXcfqgO
%0PH7;l4[i^a8T8mWs4Ln+pM.G;&;94\`"5"4t#aK:2`'%%cpGTr;OM4cDS?i*b#%aR^Bb:e.FD_)Z7Y^Qngc8bGjFPIuD4=`mmDt
%<-7.@#L73@pl`l&b_BM"H;&'M1IH,g10/!,F3&2m,>Zd&gp0OhEPcE,Fj'ib6q)]-W/`86ou%]eYpTC0LA\+ba]1d7P#\g!8d)#c
%AX'e0!1Z7/bNZAk>,[ia)VNAPCKpm,8[1=l8B"B"'QJ[/+\0qR:W?3DFJP#6!>&*f8obKHL_(3:W!5(r/n]T9<RZV7*0d:F4GS4X
%*qX=8Vc=LhmYklU!iQ*P+u$S:ZJkb\:.(#IJH-S:e*P4f&CF;!mL9S)Lq`BlC3j_4YQBUl/dJSu:eZ(-+#%YLMASkD2)NmieQIl?
%:C^ea?(WIh)N$6`fR-]]3^nC..%.>>=J/oQbS?<H/;\G`9j?KA)%Hj"k,@aip$^6aHNU6]5]/(s,5C\ak6s'&cNc03>(RHI;IQR[
%`9ej#DP8j)WmP;]j"u8n2'6nIbiXtXr8,OPPk![PU[&G=Ed?Llg08#l<fJnoU=G<N*Zt'0Vr/k1k8Nt$A%!_]3_$j7_kSL\fi2]Y
%`@1<"r[/)1k<t:WrD-4L?js1]<M!rF"diFPdnsDn)GOk$ME8Tg-dVf+rRh*J6;N[Eiq)Ia.7%'Wdq:WdVrB:<0@dj8;!2-rHfF9G
%h2db4`RD.o=g6!k6m*^bLksp7dCOpD<69,T#4HSAWtU$NfM&d6%otqpBoKlmG+&jIPDA(sp=p,6mti4BCdQ]8;m),r_6F&qint<^
%EtInA&fcE47!,BS?*36,dj:PC77<"E0gk&(h2H$pC;^RsB-3::5`Od556W(8/##'$\.`QJ=]1!1W!J\oU!u=P6F&Ya8]n:G;%qeX
%&s,.A7+C!h-<cRT!-UUGL`mobmjr0DV@_96afAgkG=2rSeIYS7a+n=7,#Q1PM5K^?'ITXtd>O:i'qdL[Tdl5U3[\Abi&3,G=pO=T
%A'*ID.p!Rn2%qM.!o(i!#B)B()Rl(N9P?!@(%l4VK]^<l[4GXOE:APK_3:oO%dN?_"(\)s\2"Gu@s,/Q9/+[pLe,HV_&PW^feEp;
%;eS<BoFO4g&SGD2JJ?'&0>t[V=Q$UqJ.g26&uTp3<9Xb&KnEXB/uG@=1/VW+0OM>))`L&H:h,9sC#ma;VbM)h6?!SmR0kX>gI2,s
%^OM>CFf.]qLk6<PJj[Io%q=*pCEdN4O>,3)nY'*RAOXgBU+^*3ILn5AN]iIF@^86&;.a?P0`jNe"[M\n;;ZnD%:Yj[OgdYL6n`ij
%,;L[5h3QjJA9G85aAp4?.M%?7(p0B61;oY!-OVM:Lo<77<<]2[0YO0?T-?Qu#6>T`/NCVcCk_[I67chaMTh0HlO$Kpbqq5!.7\;k
%7V#_]G!J%nYk)Xn$5"#T+Us*hk`):4.4^2;il+S@_<LrdBneUDVXm8P2as8]9nA=B.QTP_1I[(XCG(qio'"c9KYl;+c=ER4=r.4@
%-_`r2d>68]0:``c@78F"A3,8R:C3+o(1K26Vq0XudiPSCW7e-E\r9q#H720W&OlD@<;ogI$-8AEQ*&U$feE^p;6=^cp*19)",puH
%4Koa37[4JtLHtNIdX8>B]I-iBC"r_u?Nl.]X$XcgOqpaPHi-cJ)^!&uX'q"F\H3kZL?&rOg'J\-R*lgX,t,^kY@7FaF-#u5E"D%!
%#(]dbIP4c"`DBo(#(7jCDIbHJOiJK'(%EJP#SV3^?S/JAoT3eZ,;j'MCZT'*.^:Bm9UHC0W<^p\%K_<(C30d)8AQ;B+Wttm.2oK)
%$S6d>pj6Jk6V\'*m&!i3oe@=ToYDa96Ei6`/6M(<+lGMI&]hp*?JuZ?2,#$D#(A`WfuZ'_j_EjuVT:,(FI*d_T[#pm:`q`/qkqd4
%1Q0Q]YEY609R[2+8.`R!'ep<[UJPNQd?sIq0F?&j-k%Mg^]JKVJq0/mG[^[EQ)k/jcYRc`9g&Q'k1)jQ:gj'`3.7YGRsqGZ/sD$E
%i/A>_6'uO.::CcNrBOLkPH\^E2O",E)=P\OLF$%3\_HDNLQ3DsN+'Ho:<ub*;,h"Z&VH]FI4lg!P3@%KG]7*;N:IXG7M$65n$FeZ
%%DEQn2PVJa2%!hJHI^1*&WV2^8kg\-jUNo<qAZItG$e`;KUmZo)FpLaBS(9K(-!"@FLF`KNXAq2lLb]OFd:SR/fQf0W7RKY9LK0@
%;3PjaQCtn,Jk,IY>-P0!=_Em%)^[9J80h4%J9[hllZl92D[LB*99#jh0GSQg#4@#3PoaoF$GmaO"mIMt]QVZ??Cti#/iI_IaQkAh
%4%,TSEORhH=UD1)nWn'JU*3M_BaWXp[4bVj0>U8nn:JtS/#9DOZ%rlBb@<E*M*AAZ[>#`g,7?4lZB=42^ccJt$UdHi]ju\^d0pmA
%=[__B[nOj.+hss#P"qKY(o8Lg>#/JE3Vp[`U0aj>;5.]&AEPq-;kmEYRtj<s7"G[,:-?Bh*qVQYRS@oqPCH0(Z/^?Ci-M@3MA+"N
%FJg3!UdbbdB88\Y_h#Zl;+-=mG#6),JGU\uBRH.8A/mH:L:RT&([ra4@72&358pksP*ks,"fBp2]uBJSFYg:c+Gp=VVGtnV><KP$
%U4>([W+!5@^SW2N6JAW![Q%j+frQ8iD9U^,Ekbj5b$)Oa*HnD=_.!tZBS((\Km>f6chrV)?.gTE/N5WG"!f?B/T\t>J6,@D',!$g
%bWG&`:QA`$&n&sW>3kAjFJP<5b8L[_Aqk*$kd+hjQ]f1]:S-fqT"ek=Yqg)='@@uC4"FV1FAG5YE]11?%6'SJ'VtZs#>_5%4BQYD
%931^?!d0<U%8&i!:&8>9o0)=PcctUO9n!3#$d$RiOQ[UoU=[g#Gsdaj,%o3:S\<FO60>&DEpD]Qa+q9,K1rT_(Yoc=PGOog'4DOY
%>E!IjnZ`PZoP.>r,0hT4DWk,)5Tpc4*5W8kBIfmU9pK[jCX>rSTYQO[?gLs.Yo)aM=WTDS^Wb><7.8j\pBJ5qZJ%sJG;CRZI?cp'
%;__QtZO1FLEY"V.>gsue1rVa52EQjuJ'O4LciBJ7baM!1*D.D.^Te9'',-ft&N.KiUWULO[Rdg54I@iT9ENV5lks3a&!5rk$p$LT
%`WKA%@a`LM@^op:`911[]%-m/;'fjFG?p8)2;J,M7q%o#B:dBqRZF#u,*BUai:4nDUEt7$%LB,dPfRaEZ,Crn.pj5HRNr*HZ1!`C
%0F!lG&'FlIPE)<%duf\e`.eo20O2OIk)gp7'7LS>2&/)d4k`i],>)ukBJ0l_d3L0G//WAJACI2o%#!>\rLqQ2H@eZ\1D;Xj@:U*i
%FLn_@*AG2;+dYl?!,DX63"qGB"=2VIVkNa7g8Mg?@q*I@oSn)PK^hGuoC,/!)nS#>=`lS?Mk;1NO@QWZdgH`F?fCS*-g76/aPlj6
%`5sM6II$D?Cj6WrYMrXsF]LeFj!:Ye@FJb."S*]mUQ]O)p2!?sk7\ksD\hD.;RHIu?fIOZ6L#lU95,$/c&njYL_ZmL=RJ\='nfKs
%Z'D2pXN)PFT*MjY[gas*W'LDa'0dL\R\8Kt$E"O[^3"I^b0UK`6XNVT#/:V1hJ%)9g`TTfFVGbI0p7]k>`NARs8LK>oV6p4kG,6[
%f6dPsg#p+f?iT]ZobVi@hL>A!ZhsWX],RSqEUfsMRn``DrVOjag%E>M,:4KaoV:LHq5<ZEF%V8alMI4Qj;n3XNu\2_WupPTrQjT/
%s'06p+/Pai*;uN0l],:)^\I()b=%g-9ro8V?Q)0t/&!Du7;A"pqWS34iOi1]J58-#H&fVdcOC?LGKYIfY?>P^PNoZLnFCm%;H"_q
%6h7jEKb%*Q<@%o6JlD?,VF,ipRHU\-8n$^ne3`.N`>L7t<m,!r1fBQsY<bCU[87R^EXrs1l'qn1ZNsWs-0/cL<>cDiSD#`plBWiX
%3U(?'2CCtGKbu,Po!ij5Bk"'a\(JOC4`i.`'8+`dECn+1fFWM_6DUTDpLPL_iBnr4:@l$g5l!oVl)>9$=bp?WlqEB!Ip7^CQU#")
%'.oQ-)fn=Xe%&5Zp->17XtUqknl3M`M2UH[nU#R4M7SWkL89I`'[_V*!7g]gn[)K]I44AKeDDp$H3BWg[>('iW>Z.!Mr:DL*24!#
%q+o;`_nrZm\s-;b@\,K<9o2akRY_Eqj)7B.HF=kSQ@nGmLbc6!ZG]-`lm#G5roBB6c_N^'OboK4@VY+XE_)V>V&-M86P"P[X9\Ic
%0\rX]cZ*dI63I9*',PMh$SbHrU%PYV[F3n+**_A;U6F@Jfe`)-02sR3e)\3$_$NlM&=3X4.l$<M1oZ3^7'cPk%jga+/Pb2C5!"P;
%Tdb;V2#"*7;%H7d10WpnnTIi.H?KaQ\+#V<-,h2&YXk@!M43!hk@g6::3gh^HLPC8@%pU1AR_GbLeOlq[-JRGE[H^m([[R@JP.L-
%oT6*..WX)2%F0jVUcfF&34o_UBDEO".lQ''dBo=/(*uS6)+BU#%oq&qj8cMQ`SHis""RNKiFEA?MDVk0Nqe_0TO':K$g&MHCPc\e
%M7l@B2G!TO'00h""RAl4%[uV_4Tp=Xda,m_DFl57J[+4-4cjM_VQBU:+;VZHlql%dYJN,Z^aRqS\<IBM_;V>C9Foi:Uk[^t+Y&V&
%Dg%l-UqiUd<'M_8ND11dFg!qfdVkLmYu[W0n59jTl&pD0`10,0[<2mO)':N7SL9`WQqIX<<+-u"hZfFu^j@D?MEnDZFS#nqI5u'\
%)rH;\FNLeO,GK*Kd:[Tl89lXVQ\s!RCqXNn+u]#6F3PFG<^g5c8!d[RS'[p*3(/*B\3qO'MPOe**(%J:'&@%-$R8I$(^G_Mab[A^
%R3$^C6_I]`Wf8=SE?Pt-+Nkco%J2EWmeFA[-H1&6LFBW2G)50mRE1IJ6IZ6n]T+T%&L0f6p-A&(VY&U;eRr?S0iZQ5L"PH0-_cZA
%oT3SU6;PRCD&drM*]QNMLn<Bd9&qa`kL^)!X7G5ooU=.8&U%2LHH`,rfi,]AKmeJtU:V8gppH7$+P\Q$LC/ORndG^U&L.(O1iRd3
%3sY$'$#HCIR)UbsU82?-G\^KedmPmM?Jc[(-RB_U/m\IDpb8)VKq[RMG'5ArS-cBEX;R#6ZH*D#ne[)QH0rH5:s""2GFq35KG!b%
%M(3*CW2(CF9)dh9S?$D2i14Gj'^B4<9jJW6<loof&S)n2SjfuLKhhGa,*S+'XbX)ZSV<<Ii?PYL5M?2AlC?cpZ1U]`*kQff=A(5j
%.I:)C^0.1\lrIX`p/I>aDuga-[r)Ob'If1+>=E!A[>-%%Om.S*_h<hg.'iNOk)<`pB:>G']A%/Nd'MSFlcB[X`'Eh.i%GC)@bp5*
%08tB(=3(cA-Xn`PY<4eond[)6Aklr$YZ*#0'?a]-JmKK%cJ;TY`$6dgWE9\"/L>M0"IWqu[2&+*G2<Ws6k/h`9e^+2B/i'''LL;l
%_G^2_M02r*j[4rV0R@D(Y#&-2#q#@7TmAuj!c"IJJ.ig6(aEVHJh.f:i3L)M7E(CV7F,#Fg#U4FmflE+,ZgrX6p2gPb<2q",];n!
%['\gugpi]Fne4c/U`(md5K?&&7!-oV#s`0fgZ%:YG_mK'.@8=`5q:kFda;[`1J48a-^Do-J@%GR`ApP%,U-+pEA0\n<?2q2SjZ?=
%p1ffi>@pr6AsTtYHgDPXYVC)358KA3L#O@kB7Z"VbYY?T(qT&WWT#Ak)p?%<?%P"d"'TOWb>n$e@&Yre9INGVj76:&CeC9^lN3l'
%b*m&U?*YCD)dIMZ53IWr10U83UpOTO8/MWUph\CBIK#bcpPM9NB`7^=$gZdcQ(5A7pb-e+lPQ"iYRr&4U64!&n7<ab:6>4-/Aa\B
%>GZ@noKYMp6kpA=ZnX6Jj-A]Z^%r-qYmb;rY+E`8'nW[ZX0?``1Q^WuMhK@rMje/P50F3m#4h2?`Ba$$N^]VA:,e18]oU--TOKr0
%NF4\Z7"sM\2LL@17D_mZbJGSTiD:f-W.(Ru7)qPJ#G2Y^1FLLQV$Bs6*b'\@88+P\JSrs6[$&W&dLuehP>oquF^PR^'V_q]``[K+
%7JoVqV5*B(U[:,r>7\!;O[p[+8E.mV*a4jtfB'1]UQgZV*=GmNet,=!V9jD!K2q2>g:PO^d[;`<9',H$d0m4+Hh0316\a6Fh9@Pf
%6F,R&f11h(>fK2oYC)6:<EMAIL><&q<MD\hKUl<BY8bGfhBGrHf.]Q6Y(X""E00q_k0!In(SC9eBC=aXe[5f_H;es;"W6b0(
%:U8`TFHKnC&1a+nE=<11,%;?rNb.!g7(Y+`Y7pO9@5R&UUrJ9!bkIFZJjbPOE0.O2V$2)PbE6X.B?a>1^9[*!>UesRZ4p.M@^7`8
%fP%8GA`8G0*Woo@o'$nMR:UA?N%T-C;tNN1J[-nl>RDjH]0mp\M/On)X4`H>F@uXV--7TIktoAbhP\L$16Da%RRXt7NMVYHWMjJ\
%5@6:+QE;7tK()BG*B6PH6cS)T@ps5RioM:ZA/&D6FUMZd,f%MO)EAVWAR1N$q1Yq]:/Ku:?.)gI8pa.oi(5;JC_CVcatcTBU+AbM
%B&J5s9kL848FdfQdgThiaI6_R&;pTSni=g&:LX\c4*-\+7fc`RKp,#l"2MGfCWa\66I'*T>)3l7kFp3QFLEgkJLje<3JC6@pl^S9
%PoL,J,CP<m3N(]skGb2sEc2_>&a2A`E>#XH./7p9!cZ[X5'ZE)4[BV&h>)+ec!9m9`Xl]hP-E7q/E`5K`JZVJ"L$T)-:abB<MsO>
%Q.O"Ld/sh/@pg#"E/p8CPKY5W:ou:JgaY^$r^JH+Dk,\L.'k.VD\UJhO\,b1\e(W!Y<%TaP'eW+YCS>64Y!#<O[+#%*-HN?)%DO<
%[SR;lOZ+9#</@OD@mY>!Q,9sOVR2qlPJhHCI'9(Z)S#<XfU1l&E9!6E$B\9QlnIpS@Cjr<AKdgmh?`;hN^T$8SI6dMHX1tprKZWO
%.8]e91Lc]O:4`sY':F"7\?uPSa$NgQf6qj`l'M_sLKf(Aai27n$tnK'hDJ=Nj\j%6kF[hdK:"B'&Ka4N/['rob>mq%=4*'L4]Qi:
%)jM!-<cc7W+Z'og2+nHp3hr(jkg41+OD=TB(Ljb08_Go%."1'2;5J_Rht#V]iD$#(%7U%PW=Pj=Z1jWHRD]er<p$u6fE<Ct?i4jT
%6uF_h/1FS.>9WcJouYPt$CbkCQ^X!kg0E9r7"E>6*&WlK4Lq^17u/]+>914.Ae>bgS[j#>6R>p%]a7%:imQ9::&L[W+C5pgaX0o&
%%s#+c#;V[N'L8uqWr6ISF1MoWTuh7.E++r.`Ya;;@>_:<Z[Rok'3;B;?:e)GO:7]NXXq(nB/5<@RZNBV_F%+r=E:@M"NcM@qS'7>
%Vh]iZ_^elrb:5I8A&3FDCb_a^BLs7!:\I&F"T`7I1V%b5n"YkJ&QanRJDI*@VH2[\'06^Q@qq?0;>H^q0q@k?nHCb6Nb8NU+W-Wg
%)"hK)bG$Hu7Zgm%"06!=LqX7\$oE<sNsO_N15hNJjRBB->[Sf9GE@[.]S"=e"CDn!fdB&VBIH5"Q7e:==qP:L'p_9&U:L?-Qmjft
%hd,G?9JORsZk4Njf9dj-Z;SiWaTeIQoUnsmSr?_q#u_=09!Blo0dQHDq7X5"Tf,&J%;%m!\7uOZ"A^qS"dYdZ<fXWoM^f2QH*E0,
%N=aE2/:&XO<V$CZ#iFWX4VTob9F!rFMTf(Wr5&fGM632'EFb]](cX%8ANMShZI4mNMM^hVOo:qs,oTT(e%ODMM=+V8%q:l@Usk6/
%dW]B]KNR]&g\WV2YaHMWo`gN1igXsKe!!J[@mHdA>gWgB;.eDep0sFnPq`Q#3>9(S\9)bG,F.m-Z(t;5!>t?Ob$Kl@'c3`U$EG1j
%1"FJ&MEF7P>=J34KK/`K45?9aXMY(*_D`oU`H8q\Ag1<?Oc'NY*2ffYeNotpkiCk>;5`Y'U-og*0o%Rn0o;6KLsGqGjFPEY<Q:iO
%"1o7YC<?7BaDUi+2scWt/T])2=B,tX6YS;Qb$hMeM8<,XaC:lhS/,O6R3]['$'B"o1XW6LP'&Ve2VVi1Pq1Qhl@6&(O&8_VWU7i6
%I$@7Z6M)$35]4]\2kK7C?=1CI>[()Hg)\!=%$>4e+BNi%TIWrb*%?VnkntYD7Jq]nZ_H-j<52(n+=m761F&TL&=-M#ag[6P4/s,"
%RO!IRmjmMp(=j;dpR^20Ku?Ur-^ohfEXGbHOlrt(e5s&`F*;:\Jo)?BOW@1QZ%ruhHPlCN7IK;U8,UOD_(t0Z=aDtG.22C?hqm5f
%rpK@>rpo2Ia2^g80W"SPIeo8cI`MKNSc%N7s7($B]"D4XqWUfT`Vr'tfB%L1opaFfhS-_^^OL6KIXcWiF*@;q#QEtoUOW.+p!q=X
%O2oEDmk2A#GCT/VT9"H82#R:>md+AOo5?:c?Jf&DE/S]-0>G63+9+n3FoT@'(]9?ojIQ2$59KjIQV458J(e`2Qa<`:g?SRYfD"/d
%du:$-\CE>7D>3XmT$4Y[Q[el*I`6-ME*Ldo1RZ"\R)`QXHO<!=R)0BgS&4@'rqc5en%Ls^g4+>sQ`jK7Kc4fD1:Q0=kJd8q(UrjN
%?8#gdiqBqC1X5.no<*SX\\*F(@WEEiJ%feM]>bNQhq`_Pmcrfor8PVp,JX*5Z<69oRF'4GQhQj@kKe/;Bf^1Ch&e;R0%Ok47dI1]
%QpmhKnAN%$H[G:`]8Q^`DV<Su%-KqRD,$%NieJn*GObC_eX@9`%C\3cnDUq5]Y"1!q>JuR<ZSudnOdEbgq`mc#6&$XH1Aoc0o]o&
%mpH'rm*+S9LUZF>]8O;-qqJ^t)`*q#l_o5WqsNnMins1L^AIWpI)X;-h#'*;m[IMF"ID'n?*VY"KWX-uPM>ElDVndWYIcEZBG<#!
%I!>Yknr&Db8"Uf$\Xf566'^3,.I;sBQO^%N5DCZ^k3d&J&$LG/HhmB.bjC*>\&-:.d,fF:QWq9n&%^O^QTODOs'8B=d,k7XFh..-
%FLbD8fc+5b34o6.DVZR]?_@5Ti\)*L?[d@qqY'e.gHi<0qMr4QXQ7*GGB/Cnq=ag\0/!6EGt-gsV<#kF[l(@-Y22>9;Z2TD^c&$>
%Ili#Op?gtpbnn^PDR\`i(!#_7)NOT=M_&km*7d5HYIF9bXh,W9cb45R]QnjDs.7uLa*7K+4L`#:jkd+a<PH()Is6$YpWL=#GC+CN
%ZY!\gs(#p)pA4TUgto]W5PqDTNLt$&IOh-UhS-UKF*<$&jn[T[)M@Hio52b<MUWOO1HiLd]"S!mn%X9WU#$?2ITSO!Bm8M"J($7<
%(B!UVF8+eb^sI:bRkB$Jnp`j9*riWW.K4+@l>U&eMd9ELrOU6UYL[Vj`q]N"jj!bg3ht'J5M\$-e;*Klg%/MW4YP),YaQ0mr0-]6
%-C^(d$1.B2m$l;lEF+Z2irCU$Kg.e74IA'[)XXb@oB3Vq%WQi:=L/Oac#[A)I^f&[%A3Lg(>73jh<"UEf,+0s*6NX$1;5SXq+&*i
%49%3F?/GV\m.+;+bMdHr:F5dG/A3-HhmU(h^U^lOdfll7?:m<F0A=V)O&%fo6^5oX,tIbuPg=EWfm8lY?87pT[g0(iP2-Z>3Oui:
%qVpr&ikJOh6OnA\0-P8O^KQaLI!>;\o%f`L6,RPVq<Q5)ZSfJ04k)8p8uUF#3UBIP`gZl$OlIQKRCmflpYE<f=1<Z_nOW&?)KK4q
%43toQ!qU_0Bk;&m@h-`Kn"W%YJ9]^HGuhP+]iXu5p?-mC+\Uio?1:";JmGbn]7)IEg&:f6h7l8rhUCijDq7JUqnf.1KNS7+\c"V%
%62U6Blo@>Ik:;M>-f:NOgg"*4H#[tH^.t#Br0(t<[6Lb^*.7\^*rfbTo6W+0C4;`)VI"'tkMepd6*!!"mK^d[Zf5*SbmV*p1AgXr
%r8R;7G?8G?(Y9H^pV/`cDD.V%GD,u94o3WGr;#<"j$2.O4_V@SLN(kUg\'7Jh$POo?Dr)*XK\\k&!-UM@2-2mj%_#cni-#!C:haJ
%<pdl/?hH:EbBo*?YsP)>T3K'>1+sY[kKV.WUc4WA=6h&RY-tg5B33u_rU?=:Z["n+cgFgT;/*SC.Cg2Bq1e/kXg'NO]5::(lLO)s
%CdhV)U.:jUk&sIDnC2PaPIe<<=`Jf6cHkBR^O&c#_*"^-n[><[<uej(CVZ(.e[D?[if0LQ#p91!O8m*:[Fp"5PJ"M8ST`!M?GF4e
%^\l9TWEI]PS\Ptddd7r)l#ru$^c-JHf5'5CDRNQiip>guH5-PZq55.I$NOd#DMLcISP[\Y?W&/lR`XoSq-oC0;-%POY$[,T,g*hi
%U:fEYJ`e[8'P._Yj5.bLXP':qH$Tt*-r93Q+Vs2.289m+_4JT>]Egf:'kc;"=HrED'T98/1AgXr]Z=1hrso[!ZK`"h3d5pJYpecg
%.2HL>AG1_tm<^D,WkI'XX8Z",;9iNH/BXF'm)uO20tfF@(fRbU00b&(Ci_9()a&%=K\'W[p(*[&Il?;MNaeJ&GZ"6iTE"cR?SEhN
%f3>I5m+PpMYo"LCI^T_gp.9e>\&qf;%V=e<f04qmPlkAtYlk<GG2R-@^:"i*m?`#)jWL_>/91'/2YFl9%UABCS#[Q_[21-j*OsgS
%X8PD,[FlesobRJ[]=&t8QJc\(amPDrn8O@NrYj>)2-R6fLMM"A7N5te];*"_)Gqj[LK+A3;ql.\@gdAQrT.B>Y'tkCp;>KHE]2iV
%-?Q1<Zso.MDdHe`\\*4%<oldre9;^ApV0gafeBhif)FQ4>i.`'<,fXL3T/?`L`dku&MhUdVfTYMffh$J?ZYZ/!1;n9il(q`k/jT>
%*L4Vdq9OXSnp1%LGI7:_c^)>4s8TFe+-&NZNm%,QOH2\Tfeq1@A_$GM4".\pM9fI-BRF`Mpt&7'+#/_(9Bsn4o=:qb?b=V.52(.d
%qaoAJfAGDu?1dXD]mGc8rJg_kRuH8.ZeJ,*Yeo&ekNDato4CgoXbiT!k3uUmd)I'b(+f_@k4%.g2s0%BHQUfmX$:Nb=.S/QhLBm#
%EPI8$ma^XuED1UQ?HL_9b[.uFT*Ti776Hb1k_T#T^"o/cTL5U6_>h,srnee8P4.?/h,Un1nGaR-^Baf-h`@#i?'9j?p[:8+KE$-[
%rP'mHOp<tVcW3Z$#G:IF>"j+prc@T)*dk[,TC58X#]#LPQRec:2d1.cOSs-<F`mT[LSo<<q580"QNkLn"MD9SFq9),27[UY<-S0#
%c+CG$-au$ml>s:d2*C`Sete$8XSgo1RmgRcHR^tDml9djop8_*aU<EZ<mU!+B8@QZA-WLq_*W=LO?G=tQYI[j+f0NeM6[@Bfoo@h
%\sZ7aQ-]GOGB%Wofch_KI'u/UQ[cUFln=^7<u:V'h-I\mm.Tl6k#oIm_1&g%f$RNc1XH'do(q2@F0u]@NZhSDlP,X"e%)tlrmsd>
%g!k2gAt!(3Z:9154E@L-.DHE_j&DH>INJADlM5W?kKCblpWL=#[tkKoX:LAAZ8IbP`BD"M5Jk)(pa=gC7%[Cf?\E:_?p<:Yo+CE,
%<c^KKNN!s-0*fD<_=2J!]K@pJKAkp"GW?UI52!88Aem,6W"k'S0*fD</o.*b_okOe?FIlh?V[-;c5;YW<97ht/P%N)B]>ddg7;<l
%?WnfD:)Fs7'gjs2:!ffI#r]&#%_E3/Ym..8f>/fbm)&j?@]OFi-RjPMit6pUs#saphZ*TAnl['ciGmX]L:6E/irP!N2]g!A'%7=T
%Np@SPcG,8ZW<r%<gNYtkH<CMI+0i!SoVDZdd^G2#kJ.#mbAY^5hgU&';Y,qN;Y79#nlK47*O>6[7O6X`FS6H',%%OP<6C3/FgT`D
%iAo2o0o*O3qXrKdmWJEhc.e/'\d`j"Y?/$jICo*cj#R)`l2^jTh0k^6^om(K9.cHXd*Q*hL?=]sfnWAepQr'bKq`:nD7t-WY*mMC
%([Jr2XPpUbF6rLUl)^#D[O=bMS&?5=G)J-CY[\ZgQ4J.t4k'VW_2bcj3mHO91"m#>A#u*9_2be,RdRoGq1B^J@)pp-SN5=W%X*do
%Z"&lMG(6qM[d'NX]`e<#r'q6FfhPh/]<h,+Z`E!??ET.(QJ[+06Iu+$df$qn^Uud\c<@`OE)O;4?='bV3o4P`Xi\+T/&6GE3I?,C
%X1O;ip[7&'I#X<\T`)=]G.ti#[5Wo_CG;u1hHg/!\UAs1Fo:CA]=^K9K]`HaO?N.>TV2HKcT`a;cO0d:=X-NYY^)h1g=ENSgoD/9
%SaLFJ#kYSf0(HZd]=hPB,&srSK#acDH@--;d&/Ysr:/V!2t*o8)nhk5_&%lUZ:_4TF\BS!"?kG8WV5A8]FK\E24F&.$M[d^RCrq%
%=u2Qe&,06`L#!:5)Gg"WcXlhm*""@CTlC#TZ0YkTs7n9Min_d8`R@\36,r[7O]6gg/Uj2[ks:Zj/R<8MbrKHYgUSm@D=4(A*ONA0
%2RbhdDM?;Zd,nYd2KstYg,FFt"`D7ES_F<jDT,'ce@C2mG>RH4lJ+[rqa_/E^Edr>eR\ACSNHNu5bVC@M;;7trn$Q)lTa3rXa0CG
%qV4`jk?_<mL)!&I!K^ae]$AG$qE680\+dBF#CO_tY/DC@8o/Kf08FiO2ujiN0h,M%ii3\prmu(kcHcIffo_A6^H4Nl]`6qt='7cF
%hZ%Q&`_*B#[WV"=eregYHaIfWqf7[F70d`aDpaKLDQZ-*0C]q9KA&===upoOE0_k)Mmr(t]tDlQR_CmK>9sPk[,0A"qu5>M\b]B2
%&H)<O9Q?ZZ_]N]1IOfS9Mf#'@hJa5'27hV]?YjM$/)k-s^:S`5G(-^i:i80U.'S3M1ffr_h4&V&I::hG%Wp^jMS"*+?^KC5<qug!
%jgI(UX?bjW\6Am`8osaVG$@o)-YNmT4J,5nqSabq2Q,j\Z9SAR"(<NqW_`PA8olN.40";>nups,hH[.F$m,FZLBSb.p*1iTa$2Qi
%Xp<AB+&K?AW(P"#$LE*%R-Hscg'aV<+;=_;"8Pjj_-RL<0@#7NOnI^Xr6>%jEQYo.48W&4@_[oY.iuDh5N`]P2hN#clZfP"_fP<%
%)fpfsXi:87\MbCJn@Q@ECO"C%\&bSpmOmtC]tRTK^gO?PaHm[P]A>fdqsWRqjo+Yo/t]Pp*$3-@An\gU33IO/G5ZDcZOa\I33Lqi
%_rJ8u%!^sm`K/N%%r^M-^Q&,(5s>u%gf:'2e*t6QfoJoiMS2+a0<56M!Kh'1&;a^K_!D;GDY>f,n5IqdgZDG?kJ0LbaQ\2GqTC9/
%hEV!\c/,/Q52<B5J%Df/QL7eh;XDAtol&$WhnCJ4Bs0D+X4t8D]Pk-"Rn^OHJa]F%I!kdL\8C)r]uS2R?6QcF4@mK^90gVoFB59@
%9]pVLVgR-6hYoYBR<W_VFWVd*]u[gl#>PnP-.&IeprO)-?\G1b&,'VuDie=)LitH5l$9%)A/"Cc/_YQ&qB`gBFOn0PF)grTbI]@[
%5B^n!_pJh?nbq-"<u:nAf)E2,H<\#+KD=u]G2)()^Nt*FreJ0t,HpBp4$"@frAeKi[\(Ua+.1_FS['oW)B?UD%cI]kna>c+m@,tu
%'R&ui^Gs]_a,+Ll06M_c%`hrn5%I^KQ)"qfO/I/V6f(#A\iOLas5!9m#dO:a+dSa6"<UbLl"?Xgg:OP<H1TaI+YbP^l0I6`hY_ie
%Kct2kQ2\bAT.Y'EJ,/>)Iet/^iICdrL[/krYK5sNqu&bZe/`u:e"=.FYJ1&$:Yosf?[)-(qChTCVp]lurb/j=5e>?0$TSg@]A?ee
%5EbcK*.;s!^HoZ^=$P(3qObr<s1-Lj5>"G\3Vku?f,_P255WkPIeo8gk-BJ:I>^V?Q22ukhN"a)cTVQ-k?"M3GXTL>.^WH:(gj,j
%Kp\0WGu$j&b1.2dcFJ_9oM>qgYk7%DE?Fd2;NB6b/o=m08(+WL;AWdJbJ\cm3fsohX5YpX&B_&fX8m[M^FHK$/V[=eeUI-41>MS<
%kq,ce[e(?FiuDFVQl?XdX6aRgj0[$dl#E-)X#^Wt9V6"sQ*7;2rC,Em'.oe::3i7J_`G`oRnTr=</?q<Wt:Y!Q8,86ZUi8aS>QP9
%oi3SHg9t(6-G3$mle84nG0;Z'=M-9H6=3dj8<tLoWh8k<GM/Jt]Zf<]:+cWnZQ-Z^(ZobQaZb4JofjJGp:/i"F;_gfiq)d5GLq>S
%]cW!cT7'Gci9TJI^%1$jA_,&hCHY\Da0<"6QTpeDF8SP'A&snKS4ID]K?pCcaZS]c$p>mSH;;XJaE^G?e^@B]d<D4*RPDfU[/MPa
%XL%PU:Jb>_W@ZqkAYLbjR:]h)Z6VU!a\"`q#Ba2H+DY>i*8Ta\Um##%gtJf8H4]p.`dW"ZFLV1O9I$^`.I'>;RndLgRo%!XTTjG$
%d]93]Ol;R%],hd_s4Mb.?HnYn3q1sUr^4@-+?uAchCnA^fpFLQklCp$4LUjjWn<UCGEDub,_l>JEa9m(n'jAJ2!&>-%3(%U@e;k[
%m@BTb\?&f[;q6*9S\M[<?Wu0-pV<-hb<&qoh(AE@m94PIf:F3e3oBV3c!G7&crkHuE#eSE0$*3(f,_Vel/glU4P0K"gm45R6T-b'
%7p(hTnJ`HrK%&#KX[bs1OK^>ePL6&h`eSd4d`3GSa.Fr)PY-Zs4gW9OF=)@*%1&MOe42YXI/_O_YLqj]P%AZlf!"?ug<(l[/(X2!
%F.D8f="(G,figo?V,Ao",@LdW3u[J0Oa$#YSbl,pg:,i7h#m2De+;EfHhTqB2re7MSp)"A$`e``DGU2=0to,MB)N(3cfrp.B)hAg
%o_u(qrZD.)?gSARrNAH`VuN]2p]jZCk:j$,L0!?i-T1V9q80cnmmj/:5"t-_BiE3&L2S15F"!b_H-nrBc^T1Ta#WCBMX)88H5+=W
%EI[\`M8H!5n?_e0'[MN'kB;:4RX+>IKBN6)EJ)lHEo0e72OB[,nF!,Do:91@+?[irMDWgD41;TMZ.P*^CL?2ZWtfU$[=!Z#]Zg][
%es9ldKWUdOqA:<:8$RG,dJAC#m<u6\i`Ed`iZIspqe):*ms`oe36^j8_L(nIV9(mlhoh$1Ue+X[D3S[;3hb$cBWeQ(DV`>HCSo3Z
%B-'!UaI0\%H"ts!]^lsKT!Q.$8&PCeWiHV'6EJK.[C!LRWDXBG#KgoF0!jq-]"NO<Bl-AX6%4"1XP,X(l`IHMn52t3p,^\I<E_P8
%QFq5"fuCp!hUqWFN:WnDMDI_;95f2i13"Z<;;G^kJ&Y_U=[s9pIFZs&p1rkED)ckgN/+86_I[(aPO2jOC6R3)Y5,>q]pIf^>#Ut4
%3N6#>YSZ&SmqpN,mHJh=s1:e_03nUMS#dpi2*5MRkSUD-G&7`2pXI-`H%(sCs$"05eoc"tgR0ua=hZpuE*+YfH9;?BT:<aEQGrfK
%K[Y5^_T@cDiGA6&*8dH5&)>HroIJ-GX'T5\(%4RMK#3O8mLc,2.kg%kMKA!0O5G'Am0!B)gW__,YVJqPo:OVuTDNb):Vq6Hq74!-
%n2VJ6G1coZHl!0;m_/kqYn#b6c;lCLD!(ril8aJo;/'>^c,^:mDH*&@'>Doj/\f2CQO6O[e<ESf3ueJ4Mm&K/%oZTM@i?7jYA+0;
%aS<\1PV28^++s9>jt"p>KCM(T@i"sm)*56TPn/(lC\,'+c$l7f(2cg$']utr`7'VbMkfRS3euJ543&B8]QfXs_8PuR*X]$ke3<>l
%P@lTjX*Jn@n!T]5K[2RK%r?Xm1U!9Cqi.(_iRH`=VJdL]D!WK[#/-G(EZcoVdEa(T$.&%\.5W$'^uK%jHctGY.?3c@`XO)T(4.CW
%Q/6/bDENH)RYTs[)$P94giZp1qj;e9rmDTYqR^M$>m9fUid>Gdhd,h,DNOU9e%>MFMjs>uF$1]tI7U7hgg'b;gsAFBrs[X<htB0X
%%X3AlZf>NJ$I<jKAiY'd0E+*)A!:g1<h=Y%%a)KrQX]?*1f%Bu[]LOd0N2q(T8rbSC['l>mao^5/G^uOoDI-2EG=#j2ZfJFQ\T,h
%T#`sa7,-RFqtQo:9LXa8#hQ=fA=]<7\f29#7WlE^QAR9MqmOpi78rg?9`37gOai[dpf<N,$Mcbes5!S29:*a\J1Df]B3R6c8m4@4
%cr@"%G87c;Dg^MuH$^S1V&LZK.lXq%?5gQ,VheXN2Ocn"fDcH6ZVr7%_n!V/g#lU>*5!7Lq)[tQ2th(1&YYfHACVc1Q\(WdON8$:
%o(E\Y-LC8:kNi!XYE74jMfgia='kV:^.5LNF(X_RIU(PrGlFe_h-MY5K2G;pr`-;F'mD3oMdQ,Gq4,QRR#<V5jp:?I@dQg%=U["G
%^c&09]e6IofYMF\\Rdae4*\[R`[pW#_fQeY-EKtU(5se53@[uOO2,Ohm)Vg@T:'U4L@a]6%D<+L-\n\rYR6H('\uIUW9G!@,Xo[2
%*!OY?KKK=Jl]ci=,Ff]bqVh$qA)e!Cj(<BoQEh=[Pss)9LK2VWfDQ<OCQ2M^dkL:Y/HR6%2tUI10lhG>Sr?@i'$,(:lFjJF<W`Em
%Fa[k/FMsY^4h`gDqLAhHg2Q</1M_c&UMY@2mJQG)]&3)4ml`C8KK0n'i/Jq_G@'7\*:(lBF6(C#Mpp^u$Fu7>F2k=_[tUTY(,oX"
%MW:&klGubh4*41h]%J1Wl8]9ZYPS&0>S.tAlh,C[!LdI!O,G%5CR)+K*``:%;Kkn`",(C=f>bcA[OIR7PpHKS2nR>##je.hG<Vqj
%IF".=mo>bDC"IS%n@O&FM[/74POkVLpAU2%p[EY-UaPT"I=<BLpu!s,Xl)7I5MdB9^PIF5)`@UD`:'`(G:nY(h'D;==b94#@Lc9F
%j!9<[I/b74pX4<\H?n['qD4N_fTKs5_+?hNaum-mAj2Ia;kmMDT'^dJ"pD,Piod68dgpPhc^(1Z9_,O&&b'.eJU&NEI#Iq`'[jbo
%AO@tt>5te[pQOpDW$15)a8:ihnGbVB/cD5Q?Ocq*J)F9&5I-p<3+t7Rp5Us'Y1r[s+7[!20(&=\R[EdIH96Pk_gBuSI+N:acc>s7
%pJ]a:c9aNqiE1H^:XC$CT1@;L5>p>_5L"#6`E0jFB+*Op?%^`'*p6#?-:%n[(CM*\q0dKcB6#!Mf\'\sKPP&>emV,0%hW&[MD"MK
%O]?FdDq2B\_RtqVr;6!SC=NJd"!58ah"Fb!DLgfSchLqH='F`kF\CV5eAVh`mu0+<#s&:/H!>'F1%KQ%r]W0sg"5i%LMo_'[gTr9
%%\djs_Bh?j*OFsM4sMZXrS':._dC9?$X7b>iVTcae4n\[%Z.]\K\cACOoR!j!2'D9Oh`rZ1>e+0DalHh5*)<56R_[]I*7WkQ>fgF
%<^-&@dml"f5+X<r5o>:+@&1ltD2#1oDQu&@_oBS"e]HBh-"LcVc,f(NlAEa%^2D+Q2j5M:Lp)`+CJ$n+qMHL[.d;t`cUMT.b,s(/
%E]71dr7m"o_!S"gZ(Be>VZhABN]4$2_epOO/IZ1T-]Y7eNA-9LX&g5@$!1P&\.Nn#KR(efXe=9U,9Z3[A&3630s=)!cRKJlCkO$:
%P?OLr@E5V6=M),^7SnOGd5Y\=6eD7po7nD$pe(=g&YA(HD+9E<B?jrC5Ac)FE6A`42!;h!+2N@Zn%cGSp1O7&;8p(mm"6<U`GbgL
%pVG=)4?hfm,5nbCi.jZ;^lslekD\Q$OI*n4A\iaJKW/=n#lpPV1,W!9@U[tJ%0pim%#-<@r,2AhW*r11pFqtQSirN)C2`nS#gd+$
%V<^c>fH\Cb%"K*[-lm^<,%r%V(p*idiKeTXZ(FMhI+_1]^H2[M&^fIETCjYoI5<C*jE)BIZ`0e3cLtSV%.DiD&uLBY&I1OuIkf8o
%j@?o'67-:6j5EhBSui#Qa,OWjXs!4rD^FNp<#fqr'<Y]4g\7&nh;rs,D?&]\LUXnp*h-Jo*R1?`@LdRI=$3HAFA_Zk/RrJB1OC\5
%m>Ll*epSpC3*.)eM'j+W:lUMPg,#bH<,:A)dRq<ml<<fVcJ?*FoY&`Oma;&UH-lRbMk$qDoprP7^5(oW7E8F$1c\Y?=E2Xb8bi15
%C90bn*V*oLn\B(G\/!>.r#o)pp#=cbdBir5B\ierbPh;t"qG+$S"hu^61g!8NRKo=Ec+%RYCGqLI)iL0lI2;PDkHt((#I6S%rTnW
%gY0$$:C,7DRsggO)>WbQ-Qo/'jDe,urjk^)g&H^ZC*3RA)u0.loXhSR)AHgG^?9L!i^>R9,G0/D,p]:\K5$OP8_F<.L(c:Qlf>]!
%pP41ik<nf's*k"24N;g=;7kbDK6N4bIeWRb(4BDBi:;LRPnN0M[[$pMb>u><5<1P5!?#0HK_ga!-6>23N9HlMBTOQ(B)Pf&W2IDm
%s1WI8klc>?/E?]p=mjou@`=C8epi*oj2V_753)Rsci0LSMuW6Ys+h#H^]"bY6ES<^55`<:If>74If$2'JroZelu4?dBXEG,"4m2D
%9Rbu?rRjE*5=\QBh"^[%0C_8KUlQ\bcq$VIib+mn\^$iGN6C'Ar*Orm[J7^JrN#JOfa_BhmdK%LLAufkPeUdOWuMib)emLte2*&[
%0e`]"pQYl.p)$DupP1niIph(3oP`XAK,K%*X8`)N4>1`^1X9hnU<[rclqTT_)0!=]qVQZIhc2[7b0NS*`G7LN_CV%mDaltN^]2^p
%,hFN:s7Gc/o?1M=c$)DPe[Ia3k<Jq4[m0o\om[?E[Fa+`2oaX^]UB;*pSW6PVEF)ZV]:4p]R#,<Wcp\qK]VAL@S+ES&J!XMino,l
%r#ammNdm<@SQ=fA>4V?<QcV&&bKO3400blmk0*Ka3TiQ9YE!ue6$C!<_Y?@48#6R.HMMN]MPFm#btjh4S6%dR!PoTJC%Go]4?)@=
%O'U?.MNt;hLA]<k5Jfq@1Ok*Mmns;krq(H'p"@O,bP-8WP[845DVlG%g2F\Zr/^>AJC=iPh2iBD3q6K6JHl@_j16;8SqlhPXRCGg
%A)6C^o+A6$DascuIh<?OrE-:FX0h*U4[;c&[QA,1SU\&FU6/pl2/UIA)UDWhN?5.3UC?GTpcldLb<XA27i-]-_jX@SZ\Mll?LrO,
%,@A^94iZhEp)$V9hgAl1+5rtUj13f"l!4#i7Xhp%K7Z\/bW5[ul/ai)@<d(-!`-FeR^+qds32A"s)WZ(6L/_[?kKJCiUH?j5$)8Z
%L&p^[G]7`MU&^XHJ8[2f5\IL1Q$'(_N5mt5b(e(;G_FajPfP,gq/@41AmOuQnDF!%q1gtT\U;rmkV`_IOo9CZV5K,<pA9kppC&F1
%n)!*_V::O%KOpIgZ)i</)?35%W8T.q/uCKN/rc33FF\')jSe`A5Sa1XT5?+?Z]8nQ>C.TUdJrR*F:M:O"FB>MQ(;CQThm@Q/*g5N
%ofBeCPN@(JeM*;TYH)l&s"X3g$GL8=^0AdWL3f98I?.B9;][Tak1u6Q%QV^9-c8>Wep7#HS+2!(]pDgQ8h9K-GaEP81;->q(DG-8
%MXs^-oB'QX#CC9Q=5rDjItXr5*T#@)K/t/-'C$c'UU5ls^9L?[E?#+sT.O3tHJW.k3?@IkEM%_Brf=9WkFg/*#M*_j,_5XYl?nkZ
%qg?%;OiVqCSl:((Y]E+gq;"mi&3K@Pd=7[7a's[2.3XRQ_LahP_?`3[KtbReE$3Qs_i^OtgH&M+L)$r$hgAl-5PI)_>35D!;5l6M
%oT)#EV&\S+q/H[EO8*GoaGSS!Y?N)3?WfYdl,rQ@]t'^Vf.aKnE6/[RhdLN=*W>j^T_rbe8sBJ9AR)Q$,<<Ere]6LDkLGu'p%*5g
%p1#1p,\DQ3;5j'1(WQIe4pi)=&@-[$#.0t2*6?IaATg]Cna^2&HN'Dcc^+e0k'43)59'XC=Xb+$BTnEkldN5^@9T>tk$^cb**U\7
%rn(g7J['h.J[A_eH<uU2kPqKNO9pGWTl"uEqmVl\09bgI_R;kCioBh^jfq9kZg4!m*WJY)W!9=:!j=ZB1ZAGqX#=\-npKo(,BDRi
%qKVqCaYHa2g12LW[5Xl\#[bMX?[E.R4e3shhQLC@g6)FHHtKDMq%d3$ReCZTrt.F$ji^g:m][YiaM=$\g<[sLhT,3tKDg+gJ*aDq
%@o_RZGD?,0^'X;oNgu&O=?+T:)Tq#9[Ff`ZOIM]VO$>N>Mk7;lDVXO%k^W"Rb+D,2R9B?d07N(#6Ii`'B;tW7/N:8.c\HD<p\ND]
%Z[U5U,0B@]@\^;[ICmjST#iDQKi3(D,I\d8Gl0lgL+RoN5FkS`E&3"Ss8;\Tc?4i8q8`(kqtR(Ks0M/3s(R3oNIQ""J(@?kKlGB8
%p>GZj,eauVIQ-'RcWSOu_>(<oVQTu8-b.^u:CmC\QEqPQZK("qrd7)tRP"Y+NnOD1LB#s$%=`$Yp%X#I21M8QlhaGoQX.-TT$@"2
%q>FUhV:Fr2qc=FZOZRnAMlYt=11lScKZ-?DOi0DT5@9RVh&an_CArDes0M/3rm_-7%KAp6o7ugq^6glC,Q-o[^W$QZ51dRFq/:c[
%m;IIH^]+Y2b5^mR^\icl)umDIdc2>h:]9[is7"3]qX&3gf`/^=PORgRT>pd.rnh)_k-n.4TIT6^bob*=[,>\a9I'34YaL%:@,CZe
%C0HEXGr=OZ]C14En_:hUq"*s'q5]"\GQ4;mro*PErb75Is5U^uT0Y[=h/@f7li2,lq2b+]rkk@_(76&BA=^8s%[9oeR\a?5'@6^Z
%75NObDHn$fo5&=[6DWrM9Lc6K+A[(O*V:`f)0lo5*?fh*-sIV*:*(R[Kt.VM*;EBjd)oEMh1pO^S`a#S`%/k.5RJK>;fsRDXleUD
%.NnTi%O[CN*FYB!A8d+`NgZ7`KEkdV]em=R?r2.BQ*BU?e,b:*=jj2].]BXji2"(Z45dBB?/sfaIAC=5:0<'f4[Pgg&rU<K(_A8M
%K_u0t>j(YX.-0rX,D!d'B=ACFQEZ3dXs6?uORZ/qK^F!d&/[!."T_jJ!1=kd&6bDs-WPU:+j0U[J9&ZPpD\XS/&=t[=NGlC!":Y?
%f'?BX`DK-]^hr'^2UJn^m1DTb<Z-!'(Ko`*[q^P]'iQZ83Pog6OYn\gAOCjs,64LS+q(J08Ll2OYCq,868reZ=YFJnED<H&5S]iu
%Gqo&V7OV2/7`dNG8!l`'FM'k<?/;>:mrh`nO\C.Cogc+*i&#VrRehiVddD/[SbZqVk4'FuZ&!$a=E[D"3!kN2X!5h<#f5&#=^(]:
%+WB=c.BDqGZE5F80Pc:`VoT=9ZEi9B$9$p$0$<f/C_!g;fRV<!r\-o0!fh*Ka(?N'<N]ER.q%P*iU?.[F+d[bOi</[5\PF-GL:.F
%3N?b5o7elUWTUo"NR\#=nS8N"S9BqoMA.cIQt]GO:#OA3Ja?b]c%VX7-B[:-@2IMD+*!<?iuT^!&Y`a'K_f_/cF2scrIPTC1,5L)
%U0-hOT(Uu'5%Oa<gcam8Lc[doMZL[?2D#7R;=IJ@kDQMt=<M]r9U0b6D`\lb>@D_jf7L-ufF.X+()A52**,cpb+>YXF+p(5K1u5`
%DTA:Nkk`R-'#O2n'YM8jetJioWd'::qjP?BEG%"_jb_umSU_.reTtc&Ber,oB\-h%(4(KHkZ@[acdah#Y*pNm.T]^[+XD?3^;M8g
%=GA&um4I:E3k_q((ru:!OW=![QK2hhpc5[cA`t7R7gb0`-m>UO&JfY+Z\U_dH;`]saIl=mWhEG#]Jr=1G^s9b/%nra6,!a&R(Un.
%>UA_L-7Q2N/lNim_=s"pAlIdSUGnqp8)=ijpKWTr#so@%8P"^H$@+`u`Y,C;rQ_A)np1p9j'>\pHHIA]UnfE;p0ojqW3)QFn"il;
%Bb&<$Z</.=4875%53*G=AIH3Gc;@4p^.\_^f6GU=ZqI>+o*C8,hlmB7c!=1W$&]9I6C1K;Ukgpb]:(j#hYrLWL.?L7haV&)ed/7V
%qG>tBGQ/ZIAp"1@.c,M4jj<pH&p]FM.!M_u%"RFGQujS^-*2`$o)^Pi(0X*AbL%-tDUUd@rCka>EoQg7*Rr'[GE`Yt-@M1?/A_"<
%gp#rA+DtJL'*Y;e#/a!f)K3&"X'65jj^L[aO"eO=5^=td;gr%]'.6UQ<O"C_$qK;Gq6F(1JE]if)K,%F_X&GjT/'Ck:Z!25mp4er
%p`>LC^h0Oe9tfgp?5V-P$i+6BOJNs0];b,d$Js)81gD?`Qi%84r)&es]eGi`7Eh[so$D]c,/qf!Z:OAebTiAQ<q*=E'UW;HoXbjH
%a@.QH;f`/f/P=_"[RceefM$X<fF?+rNr[`Gf`qKpE>lVT>DO7`+2QYpflQ653l7`I'O*?>$cA>.PX,Rr:53LQ)/LBQkF.@7Zc?jp
%V]#`<?0e)b.0R7U(W'>4@qA=AX0.\=kX90H&_c@oWJmtpruCF4)F`&$/WqlqKH<)5djGFgG&0EYFr>1m./f0En/ImMfP-bGs30$f
%/63kp$&ZaKlK<,`&iBY\9PeQ56ZBV+&g5H2M=B40W^UX-+sq%FVp5!:khf)'bbef<5,N%tG>UbgLn24W\0jHPdsFpj%86N\Se/sk
%s5qreB2$JVR3CcPp$9d4[gM*ABY&#B<NKW`_]a(Ac)m+kk>l)Gc+Am??I/$'62QKb.V9f!]TEs9Y-WV[-95DqU(]9ec;_jOV\)0h
%;RWXbpYnf8\,L-lDI04B*gG^7VBp7EZ_(2m_p2O;8bd@#2*>9Vr2S1`Y*D)cV)(Uh$&m7M!!FigRXDkMY2ol`M;6F;CC3m^]%_fH
%W:%u]O%kq>YuHM%O:6Z.>'<"i9=HUE(qaWY5AC>VV39gpa5:g(Y8sFA,C][#]<Ce"01KCLqP>BFe9Cj)Wk\NpU%*X/fUmp[O9?Kk
%@&LC.nJq^-[(D4&BkHGF$YQXa7=$8<6419cG$U*&/Q2>ohCo4G8etNS"5!8:4X8Ns[_eWCEAt6uZ8*W&_Rfkc?eh"`2?XK=@p_kF
%M[(aCXt#Z?=$Hj:VA:*a`k?nEXH)8j5(=6%[Q;@M"<kn-,pQp)#"Tl5?RFkthoDLJZ=O*a8Zsr6o.AN0<q?C!6slj-gc,dq,b%m8
%691;?45:F*mIU\/UaMnU,=`qTf?LS$\hlg!M[A36I9G_2R`MWDk8RF_UCit^?=jY.<3aDr553F2O9DdXa?q$OTNkdl.;.?d9Z2N9
%6rD)6+hln@JL]pMB`sZJ*VoJ&VDNc2pHEe$Rs3A'mKip2B92WY#1a&"q,o,3co;rpJ7q6rC$2=NBk32J0b=Xk;BB6pMK\':hCM.1
%q@mZ(&RHi"OW9bp/Q>6B*$9`*r?/<Z%_"&mX<`c.$<s2U]atN;WAB6p#Dhq\C92Z8UA/(/,nPb[0r8iA)jA,X'8.qImT=pBQD-i"
%C6]rFgGr73nAe:+'+kjFj_E@oOhV$n-!huDSJ]V6O9R/3qm&;)1W/nKlHgQ<//dmqr8%8o,FEIQC=jPO0db;YQ&rQST[+h$kRj!@
%B6jb/pe&(5BenR+AG#<2.p0FU^1Y&)Ja/7e2L/:RJePXN+N-V6NctPlS!p&0WR:HB]HUD,_i3VA=QRiu`RmE:mRA,Tr&Sl6iI]p1
%mnZ51+N27;@tl$14YFC$[MObm&lD,qEs]nLj<,/\10KW&f0qbEB[\DCP#*)n:)nb$nsk8c%&Jo/0f#g92`^;D3c[]?.;'+'KKS3`
%R,rJT<78CO0Wjn*eO-qgd6IRm=u2*d=^%ZBU#)<Z@J'NsHX7)CK7W0sOH[WSa;8;egB/7?g:4Bl#gYuFO>k+u0T$d=#0OulDQ/L4
%F4_bMRnF/;N'b,.*hgT6)@R44en#jMKt*C!/24+Qguh:S]E9%a,gTP1k?Ma-YMS^&9p4l\!1bF5?f;hu`H)>i?5$$/.g_'7k5+R;
%mf]O_pgJDR>%-8?>kYMRZ0Z"KUQA_lGF+GNQl@Lc#&#/"a):CtlW72rUT(LjJ*cejGQL]-TB_;.]su7irMtWVE?Cbe;YK!r\iS.f
%9%@R7mD(NTlR/Uq`B9JWep5Llh%S\"nt7V'2Q6LNK-[&`5&.RPcmFBcg"/h<7Fo)X_pkY&E7oVA18H4ue_i5[R+$Hc&Tqr+P+lk3
%,Hal68%6j8=li2th+TrJ;G`R`TIP?Rl.rIJb%fBlh``H>hfgBf;it@PdJ^de/()I.p;o8AO9Xc/d;K.0+$kKf_$d0bQ9cPVlZRq>
%-3f60iE>th`oSX%Bm[Qs2+:ZJ1VB&"'orUk^*tdXD,FX,/"uW,Z-6MYepCB\`[OFAlT!Ch8isjr06-oMC>UaO\eWQb@e>0abOj'0
%r1Lj'bsGT]aTtP(EO1!&@HTph2j:Zl"#+,bI67"JGf`9FC*Mf4g0ooqW%GC\#WW%>XXiso*GJTDGcgdt<;HM`#lSc7JhSU<dR)*N
%=Hdp54"<*r:U^-Gdj.Z`hOpXr$-R:MATBK>QJMOVD5gbc)''Tc1.!k6:(c`kSgW4F9,qGPXlK\_>qT7W&[G0J0'A=M,5ICCG+=^I
%7e)@SYLU?Q.H*_F)o@@T+m9a!TiE7P.]s0US2GjWQVM<HiQ8rL)%i3g0>mCS(*Srl^d_:A\B8Mr/gYUP.6WhKD2!=gL,S;9Jqp3(
%KLCjGU.A@G(C8UQ@kk]eI&:>bgM&q<VheL99]Qs.I@a1i^%1clr4K5:s4<O%S+Iu?$h:c5*-u']0aZJ)'h]1?1PCm<6HR3eq"pmc
%28!lA@0?d/XbX*JbD19^d%@mB%<#5mnAk&Ff<,_\=`)3h2jc4E@0j*6'k'2prURbt;VZY3hA@kea[jQ&TWsM^C7+e=U!t8IRhsdA
%.P4RXRBYWDa:T2AG:f0ZXpU?,,aMo!J.l/n2M.dXOd7$tBj-L3BK)_t(u/k#G]XoO?fGgd<?klTA45=u<?[QqkT"5K+pN#?edVt?
%!*bUp-2<Q"Wh1TdM0s$4nDGqS<2NkJ:A!Re.t];Xf##>oAQ!J^fVs5]]_@f2^E5W?3OMl%11Z8&lA,8.aXkY%AI/+aKlKEkdMjrF
%e,:gK9oZ2u!r1B7<@mn\#(+JS4.KB3UukhLVS!ep[I"I.,"GrN8Pl4*27FgP>GN;nKLM]M,HCq,e:4+_5t]^mU-(1>#A'4$EWq?!
%(8k+e=V2^3j?Z5&E(N$(NuuXWUS$?Y?jV"djP?%$CV<kYdTcU#'.2[ScgXD$mc9msg'u4!a54<ti0'QmTJQAf_.MDT#S4*N@HH9T
%j>U2`Nr`?pH$2:E/!Md.)^O:DE"-!1XGh"A`;a"h9'@XV--c]=GZWQ/11W.l)<lA_W>r>Bg@mZ#0J)P&^l47D5%RAL08`aC?,4@d
%XsXS+_PI"Vi5kE9KgM&!KoUpo%QUQB#A^ARCrB2nfZFTjg;R&&=d(@)1gjWp!siu0A-3p[Y"b3jEq<cA=\+!$WBQA%>].f)711(E
%*-0hb3$>OWhD7l%_paakFD,BPeXVi?%#BVrj%LA#nd]W'Zt!'@:c0dce%H;>Z/dWlZFSlR*7cXfg]^44=@,4dBhcR4L=1E6Z*bQ5
%Y0IET!*<0_P9_JTb*kej`\tegElG@Yd5q=IJSE`Ah^)XGOXussD1@@Oa125#epRIQg#QX%fh)$LHhahtSD7<U4n:(I7+f":>;s#Y
%RoUu2eCuT3A-P[fjZ^bIRMXsY,?g'BV1cqICKYk]$Z2bO)WM:d?C8np(<&tO8U0*?9/r3N?;"r8IT>*a"9:&*Du(:Fg8-V\"#SO=
%n'SPZOoSLherCfcC,mg<b=RZQN6Vh+]-B`#I"nqMcjlDdF]%;peY$kJQ3Q,'Cr#t4^bLX[Q"$AVI1,KL9V]cE=(3.D+dSla7>cL@
%L<LBj>W8Lep5K2?$`'.Xo#/^/f7f&]@.tj*Bt;P;N?hR,=X#cgi7`>d'k$&L@=B!$U>G2!h0ZOp^JNSbK9U$=7Nr%j?ffA,KF66V
%1$[R,C8V#dfZUd/ER!/2liX:ifQ7C]gKM-MD8f+#4:i4`MB8E8X$&TR%YFjEK?1Di=OL+&$*71Zj>&$U&SdBoOgL,k1OC*4m#W4u
%;]A&*Q"o=e4"iN1JEP_glE5PD;TQZGmbh,NA]S#s#9W<l<\7k2<R"6b,WM^G-LO@C6R7Vh)`4%m#am)5K\Z-($HPa'+[6=r]9&(>
%[sp*Y:EL3`LU?u#`nWiC+9rPX\9o%I9#t6j#6ea=M@q/XjP*n_dJPRC=XYQ!q8[^bRYTT\do3NIJJFDc8o17eLOqs2B<q\IZe_<J
%n"@$0K^lX9>)-d&^[&teInu004u`qS[:bj\S?h5[!XM&s@ti`515(-US9"LDOc%LB:3R(*65"ioq]D"F[Q<F(NKH'M=XE\LXZ+C#
%;ed>P)f&?%BHk=69oZXlj]0\9DZ?LC$\ieqQ/uHOP<"VTjf=PdI\YU6V5D^'pf^-cK.nJjB0CU*Y6tY>=f>#/McMIIGGfS%fnTT)
%"\_`&B55<2!\H`5\W(i`a6$dYie"`XcZ4CQLN:C;-ZIO9^"sk*L+U[TCr#)bOM-CAHS8g1\Cu6EnN:Mod`RTFT<6M5F@G5Ij+<Zm
%eu1?u+u8Q/W-$oY[F_=GbM+)e*W*Xm68h,*nR62YhgNPhWt13QbT"EC=U';]TTL[*8*EXTX5KOrhCCha5m4hV@\;=HmMm66X0]di
%62D#sqh9pDgfW_0;$1Lc-&gc%<-;68&]7#C?K&c6&4>eV*1Uro6;D0eXnGtdkR>N\DbqKU<"Z',]?pm"KZRHZ1o_a;&Gh!GVBNVt
%Qj<\(?b6m,;FsbCdHX(l)1?G6_[s\3=E6eCddh5'@,/*MCGr0;2MdCh6>@%$N<`L#\Q,^5P'UPW5%iWrB9pH>W&6-nF<MgW=/.h_
%9b0b&\p$A7P!Z8W)Fe=ad2')t^I)@:XgSQG:tW47-cadE4GX\#S2L$59If#01?N+eS>-0F%DmSCa9gF*U9'16gb7%9H6:BlV=We$
%3H9i>agpF1<=Bo3q0FcF:maj%<-o;8j2qXjcD\_uh2*"GJ>$/WLqBO%h]014(O8gACID'd/-$LhMUWj%n\G&'f>\+%C-mIhi7aPJ
%5ho^di>6g]97+=.p<!m5<[Wi-L1_ko^\-\*.j4;oB/<(5?ZVe1>`72r$8?/W-sW<C0(qU_@d2$SN+[Ynikg]M08(X<]pPZ)<H,K,
%,b,HY:&>6&K]sM8+lJm0FWsi57J6u!S,q7r;_@>;WB^37e"GiEc1fS%=_Q89^m;Cg2!29UlPsacl5ZUpqUFnnS;+ntm"P8P65BZn
%.Yq52Ir+:1Q9.M/V#V/q@A8,M)I1tLg8*?N?``*h?Ghc2N2%?_W<$%2!B7V\4]:a'-PEHolBr6i5sPrICkJ/_TsQ9,<=;SCEVOAt
%)YH(1`#Y;+ES?.@Nrn`LL+-V[C/Dmb?X=.^,!:&-FuV2gA'7,>#`jZA'iUc0B/A8^?c!tud?a_2o:p$X'Zu@()O7so-ml_Ukh%+@
%lNI].ZaQeojNi3',-&a!6W8b:iE$7*&\ssH(CX1d!h,E)W^$`,X.[\(%Iek]&1jB2(j>K.lNdN<%R=V,N8KPB=m\sV1%pe3_l^Z/
%V5_N$f+D7gn.K2<YnXBST0GQ2h9V?La4gsb;30R`[27kca<0sB%^<DaNc1uK<VDdc(6cENhYsJZlq'Ifg$?r)Qm4Wsm'@B"/WOMo
%T!72S1%HmPE`SnlQYnP5WR[1*CH;hk'tpi7N+aLY"GMSdLNL:(1^;,L)?q=WW=BI&(IMDo0lH.070TeonZ3dAZVt8idZ+dc@(q`M
%X#>Zn?[.)nl;!tVc(,W9)bn\j5\G,dY"\Kpj]'1]SSe6177T2!MQ#$o*/1[Y]G0@O[)6nUA\Prf(b1Y1)NCc]78ZGh`oRSUUA[,4
%]m],D<8PKOcq-VXjTDA/A^B:KK`XaiPstnE]ka7?g"h2hKV^!UeOCF5g.;8F&g9I"+q1eHLe$S9l,cJIQOMD`ns`2>b]9io+TPu4
%35>sL[o=Z(^4Nn.QoiIficq/Q**66k8i_i(D@!&ufFV.6#?DgV<.[g".4f@hBKbD<dGh-gfIj@CG2W'@YStBW^;E*0-nQl+8pbTb
%\1TcdT@>*96g'=[)hFirKV`!_/m.LE1OuB[56Z0Pq;SfISB\^d/aNhD:tQh\hH;mK?]Me/EOGqC`(ECqrlGM]DDI\hS*!5RIPA`d
%en3`?0cEi015;I_>pr#X8\&@bD8-Yt''qRO_-6D+\cI36)Y?7##]F@fD)'eCIOe!7bbIBgPtfk'!(uF1]JYG?1J&lj+1V+^Dh(0e
%liB.o%=FgdTlt`Zd>B(%K368u#k7i*mrh`KU!1ZR>Sh4u<2(M[kpmJ'_&;o&hW>?9O6u5?)/rruV5&(T!IP1qq5a*Y/1<Eb\qHQk
%GbOu&$JN\=p7O:Z,+NgHBZdVW*_?=aUG`^p*PDNkQngogCKp[G5Dh<*lD%=H[SR+,M\'Pb]ZnUY+b\YIXc,Rl>t3d`^jGq0p.WKN
%'2"qL,!M%]"LcfYk+R"NK/9--%s"V`M:$^Ba/.qd0K5THK!qu*BPpsNP]@45e3Jdhf=k6Ca5UZS(-4Zs#,*0te2afBUtgc$9#au.
%R`548(:'XC1)T3\&Pkjri!>Ii)Ek[%\m-)LLC13:^cl-d'?6E>4HY;Y]3:L5;n?n*6FauHBpE+cn?ZePEQYoCfJaEJUmiNX@(r/0
%`N.d?R/WrI[h<MS&/ks8o+&),JXpo#'R>D[9Ku#8QAm?C_aVsD\/Qob35`0CkX*?bGtr5oGu/cUkp9g"g.nU#)r(&OKZ8J"=#cQ+
%P.V;c;#0Z>j'c"fjrHpt3`&9;'QRcN+X`KjZZ)G./m2P=i':DR#9_CXA'AG?E<dV+hh@F_++Edk1:P;C/L>N6OKXB2;(2LMKf9o>
%V]mU0fHSiR2ZbtSdpg0=DO'8S\1.4C4Lc@^CDq*gdu(FB0SmDrP:Gb.!EYN11<=^@Tnl$8[mse"3!>[-1_]3$"j&CE8&B6sT37Jp
%F4BeJ5sF5T3.uDM%73BSq:#M-Aq,&?9O__D-fH=D_a,0rGRLpIas*Lgp4s<^^HkGAUof(pM;]11^YMgZr-G[]b\"lCZ<[q?^MlX6
%bcOfoT7=e"n`E_=YgV6_X#N?a/.]AE!fprC"pduD]@W!S;\VKDH.@\Kqdu;f<hSZ'C`UG'o87H"5TP'ZGn,QhEcjAT7`G1<(EnhI
%hYN<NqA.[u=/0Z7l.3[[[5a*BE<LW<.Vrm(M)bVQ9oUXI[2s,n]pYK()KkBcOp;au)JWo)A,Cs9:Wl4>Y=6,R%"FWn8\KFffS0^+
%,B(E'1"j2J0*]Ua,S+'7SJ.Be."&\:X3>5o:s2k]8/*ZoCW3Et)4aWW$*ZIlr05+JX_V^5k$kZCJ[rk;JF#uGQV]Yl[s1<J/,=Jk
%orC5P&;0T[C_!/%/m>pBVD7lN0!leH@QAd;bDmpu"\6hSH3+@#VF?eW?B?@_G#O-sEO7L][7+Lj''1U4T,gX?65#9N&I9eeDP$6_
%/&Eh7*;2d"phf3mQ(!uUU'@\l:.ISNPa_>#>>#3t:4_rGmol$]N<mH2-9N=]'1)uCB9\'="@>u;"H):JNQ"WJhAcf1S#I>^af;FW
%5Z7u\`n"=>(qj$#!_Jpa8O]uffCO'oD$*oR:G3rGWu3FaH\T6Z5@&-5LS<1=1eXG6CWf[eoFbDSE*=4O(A;*(!d"@+HOp,k$X&sU
%:)4bJ^Q<XcMi.\"O08IX-q`:NmkCIE9YJUF[U6(eG?qN>WsL_F:+`Y#a0#uNS+dpM1>,7DJl#FB(/cG`p7huIh>'KVK[loMKh*MA
%:WQlN\Hi)G8p&$8lEa%6[U>CliQl8tBX\H*C?@urMm(s2j"K(V,qRGnpTo+c:HcQ^/;"j!f4S,bQm:V1k^bhVFh:<>!MS&@/`?C@
%C,d3n<sLNM78\^me4:*-8lb8$:hZ\^-IlW_mW$bIe9.uqK+)I]/*K>s#NMh<Zo%(E[$+@Q<'hWK"U.@Gl9tHb^GE56`Ph%DG#"^p
%0+`hoJp,[6VX!B(gGN'q%M_3+D`s>)MIF6!$ec%#q0kbkbB!rh&]_E>'rXo.`i2tg3+/j#<P9.CD<g[,.bO/0$;2)*!q4G@H?](J
%o`.E(*Ok02O0b(8=B8MZ0br?3-I5C[(?No,VuUfF+om+m<8%hOY+'f)'sL^X3%iHa=fTE9q&UiF@k<Iul\TK,%+hV!_?.qHO6T%u
%?*7@TiKY&@/K@9/"#?GmgG<<V>jH][F]YX2_OF,R,?_Z3@97h>Mg/\oBfTe)_X;)7m##I?od`aq;A:tnh*`DRCfInaX'&F`\o%ms
%S-r^O3q&JQ;CW$(LW.7oR76X)j+f#dj+4`nGHGM_!kjqZXk?[GJkLl-0;+:p6mYrE:HpO#oa\6ld2;$'09FB+#[gn0D)[cjqJOTA
%H!&6iS<tM+Rpl20@0oM&OR2np%IZ)K7(\6hY1R9lE45U!npLVlL;KqXmp_ISVJ]BTNB,X>L<Z@@n-X=/6SK0aP%D&N,bO2<'G;9&
%10eM3).A3I*^@O3<P8B$[^/3(.P_bMO9BP`nf7=2&;koATu&Np^J5JZaML91_=U.e&TPeTAs%*DG&p2,!$Uj(ksV9#d\FU2E?$<?
%@&g)t(^O@gm4^;K)FF^n0-dEp$0:E//ES8#.$,K1Eo=Si.J*Zt&'3'#og`jpl!Mc?84Aar0<4MOMk13g!P]p1@KmAc8A9ML6153R
%Q-/1W_d^j8!E"A@*Y'MU?*$20M5$E]i%T2Z+PBG%7&,#o<S]$sdptO$Q9%s%*l')Q4FXo%5Y1]A!.2F5BIPBpc%;Gj]Vl4EFlZO.
%50lRWl2]jO's<3E/&qCk8a5tV>Y/Mj*`SWL[:qPq7H'SAB9a>W6Uu//XacGqJAY/0G>!M3T7kp[r3p7&\&tgh,43;X[4Vd6<(]iS
%aYJ,*<_i6SX-?$U>bWC>#.`"fHYcY3$ps%:<f1fAUWh0!D2=B6%bk8oBpW_[\q!t*p6IJu2I,uJEJn_9-4+ZP*B1bVY<CoW'RoM*
%l5^`DcXmqt&L9lbn%j[5f-M`aV)=U$:u<Ma&#uaB`YS-nhJ+?E"NZ!ZH,+KU&HG<\0AmO8*R[>@LoCkJX*!7DnIc/]`tgE32"b0+
%k/aW8bRnm4lcQ>@%Ia^V21fTlCoV6*:u-(5bTNdt,7o#i3=DXt$b<gWabhEC+&&O)aCrCl#_j7$$CK[?GpZ/m1,T0(6(r'2A)O%Y
%$]SjDDga5,j.*p)THVG5&57g2p!gEA.7tQo>K%Z,]0C-EB:`5bHPuHDX.79\H&:\%r.]XFZM-jo^Vii(+(U^Q+G<*>Rc?'U'lCK9
%bl&7'?D:@oEXZUQ"(q_?<XcUB!lsp1cNbsAZLQ.(6<UNH<U,>/IB&drC]b;RLji30*RF4!.\Uai%cpa;5nbpF_h0!U=hKtD)LtFm
%El+q8Z`/qW?rJgFBiOh''j%/1ZZ&.OT9]@SPi6gO*DOtmgo0,\/]#qimZXMl,fr-3$3#f$P"8;9$du7odg+hdci`$>o)q##\;1kZ
%L?`m_Ot(N(&h^;f(+Kq+a%sRLl<4A(mAf!YU+R)*[hZ_Hn]B`?e7k5XF`eC5/"prAKBtG5:ZdAJ9etNRfJ3O9-_a`\'(ol&k&'`^
%(W5d?C8rC->82js["B,7m^2MWLg!R3BBHok#FX^1[[nZJJi'Lf8DU$(\4/_#Impb<\eBLe!MM:a7"W%e9P?5#KssBi3f*/e`UQE6
%>g`W4Z8B?FFXmnTbsZ([(@4k\(FGu;#S;(mW]XCJ#1qgO,KL0MilL5e/4:3l^+G2f$Eu.;J8eY\CiaS2\Ap*TWL#h]8&?OhRSJe\
%JdRAoWNs0Br(s>"A4@W0NhhQX^;E73gP,3/epS/]VtR9LE`SMY^5B*MdV@sbR7cSON#h0f1+Q;31]fLZ;cpI9MIlgf7W0tYrQ:Co
%&R#5o]ecfa$3aEnP)/[050b11T"]jM#N\BKClTc!EcA(;L8XPGO2'RFkE?tSH,\p=Qd`+QNFZ,/<eijFN0l[N@3GNpaVC)@C*L&j
%;2c)KDoko_`'>E!%8p4pTsCBRMUIb=psH?g\'ZUo(jc$ae6._JZmZ]mL_8*e7iVr,6K*R2X-"p7Bi+``UAnO;K8NL\=gCn:%%p07
%hhO6X-D;P!GnF9k0,fiO6XjME8$%9,0&gW4*JCV-E(?V7VMls5UFh'D\B+tS%uA(J"Vq*/=/m9lKd6YabRk!nNn]$cGt?8=J]s64
%_1.8_GiYF7]JbVU,D^A1Zi[%B78hGiOV*Hk<6^kb7>81cBu?MA(m1m_]B)HQ9?7g;*aYktIN.nTiM(R`>lMM:gOK70Dh%buhu<2]
%rpDkSXT+Xeprd%Bp)j=s2o#?q^\mNS^]!lT\"E[!gY[DUMnf)`Du]@nmsb42^\^`e`GZF4rs<35p?Q)cmT75TJ%jl#5PMcSs#Z]3
%?Z-9B`[f0GH2H4Toj?ddPi)KGp%9C@XSMVj7=4tL*I#lb^\Ba;HoCukY<MsGYCH.>5=.@&IkC4uefqjBf,hi>YXC\1H<WRUZ-Oi=
%1Ua(,6WCe0Ws%(-j!^XQ8$2qO=A9r:eY5m@O\eK_<->BQP*\X#;]:"<NpfuW!&$AFW[Oo9)j(rm&df5:c5:Ht99MkR1UlY?S=s\a
%;V@8cY[liq`R6hL7![NU9nKHKBraucL-ia9lYLlqh6jil5`Dr9h]p]$#'WH]PrrUU3o^K6=ZO?V:oU2K.+;nh"H\2U`1FJ''MN'l
%7+m'jA;-7ZJ2>BX,7)<7PKF<TAu0W97NC8"U]rLT,BBVqi/J-6;D"Q5ck32jgn9HFY<n;3Q2=^]e;-;?5ge\=mB._!K;,qE\O,)W
%A0b\1YWROu`?@<r(sBRjmq1o"ifQZ[[uHJ\h_Ne$mLW/%;-%Z"mRe\jMR7JlI#DAC\8tMB_Gop1"T+)t2o(aB-$h!Ei[]ap<gXkT
%%Nt+BjJ#2_$?uhSR>X/IVouOSZlR5DDaLVH<-1gs'Of7*4)5+iV30-R$Z,7&@2L5,lEFK:3Z;P/lpn6mDptHBmZ#,Oq1H*iM=c&u
%c7/X!kLjVY*]XL;DY#(Qjh;sml[pn\M@l`K#/:(lf<8B.kO)i-!Y%39JZ,h60rapOEq\RGq3mEi%'V@^$)(%68^>8*BD4IS=>hCI
%;("ZmYLWut@GTJ2'-%tQ1'E_PKE7?AP^CquC>(V7oG-)(%VoKSQLGp"7tk`r-LF7WU%h.`c!<3*X\VTQ0'V(%2T.Tf"r.';^aM0=
%DAS[7W?aug/^rr557"aNH*mgYJ-e=!KT'.:Xcr;qW_??XkPBj)@\"\F$qU&5(!r0B8d\)=QP;'i@-BN]jD?K%&Ce#Ce<2hLG'G2Z
%Lg<nQkEeacWa+cl(,=Psb$E"lK`KcA.Y<n3DplrXi-gDbja<=JNb(#PP^nQ+=8423>u]sZlbMmBSp`9K*DF6<6=GpZh(iR5VF-5=
%8Qtlr6upXQfe%1@rUMV`S*5S<llI$Md_m'-hkQ6RWRl(P-L9hsEr+V2R:mgc,hhc:(L:61^JH#TMq=?OflI+s)cgK%X54650.NJE
%%Y=qA@2*c1Mbc[V[1("81.bTSX9Z$:"K<%:l-0_hS&i3\Q(U]\\TugRUP)o._g?ZEYC,r.jcbWFb?S?GIgYfIV`CC<=PD-&DMPQ?
%$%3/+RbMk7n6]9'+A3YYcq.XY'F2]"03bA`*D6CgE].no98?gZ%5XLf((q;&'T.(;+qJj:6!M@<U,,5'-5`13T&#FKJ7;$V/_(7K
%(Kkj-"bWdPQXTu-2(qm0WF^e[K;l;@GO:*'TRtj>5?53#iM2Qt7As@!gsDa\Be7gEccHd`.cX06Z4uc>5ToD"A??4R@<Z(Q(<K=N
%BXp7%Lh'Waga,/"-"gpCi#HbFDS(=L9B_e/ZTFPuW0UBASF%nc4:u*>e1C`%SL_b<(Ro#,?Bh^=FKKF^YL<GsFa;r_?hoP;D^afP
%hS\1OZpGrE%a3k1(\;E@($GE]/50[=*Sja1T*Ma!Y@o*/>#O0%4<4=qcLhJtaCKic9uO+=^'\@>*9;uBmFb8dlXcno>kY;3Hn*B6
%foFo)9WR1M/n\Dq]KEJ;l^Ga_*2rohQ,)+JFO[_N8ECOA_=(T_4Ol@,L)ZM(aDc[RO_>9pE#-D>E_K'E(hufiYYpGg*WU*pP8%IG
%'58"afT`$SZ5&\2h7@uW>T\0C_,.`L^m2Y8F]bY0'1E*/gOl?5,l[37c8g6m#qmPBZMgFG7G34&q#KEl69J[90DY'+Tb(E]P!jrL
%5aB]R1&t2@TgCl=*g`L09-TG$eU^0md5(+2GO@2lgd*X0ee1YX.j4*G5#&"NE[l)*+f_QRI`+ph_5A`,QcN>E&71+NF7>_J<R=+C
%[HmmHfE!UR.tCPN,\biQA%NI6QB6!8ff/$l7O8=oX9uQ$F7!M65hV-[e"SqhAkXTNmYdHo>Omu)?&h`p7Zm-A+Z>U0!2W6T_.[He
%06W45(Jku]-;BIBIM7h=%CI_%cdcmt#BP*-eeVp%":WG/&R)DLYK)c'n&2)q@rjO9B/1:kWr[t+!?\3Z0"Ip,o/)u.+5f&c[\5C_
%^ct*@7%uP_&@-;fW%.j5jEnjDNZR9!F'keCf"WJOqOSdiNO)@D14ta^>R.$mkG@PbM'1rRRJ;<?[b@OYbP*();'/dq1ZGCD/1]NR
%/Xne*@1Sk?Br:]^7%dHF192)3GE:f76+sr7QJeGP4$)LkXn/2$W_Qn!l=Tgb5mgT)QF&V_2O)iLXj+.T1=[BlX,lS/<WRQT$ndi9
%gE4ghA>._Q!Lmc\6/^/nd7u0M&e,mU#X^'M'+MQc7_1JP+K!e.*r90<(*GX1%0_d>_:Tg`0[<M)X1HR(`ADFD4K$q&26e*`91(si
%i5M]?F%GeB;&ZVcPQOm#;[#;<0BpORUQo+>2#u&s9+B->`8P!p+uON2CZ8#qT@'WRdr!Z\d0L)N?$Bb-GQqpcT^>)V':ZX580I""
%.tPthRU\OEE'PPE>V7B]plb"V*CZ-),@51J0)s?O`Dmd7/Z9)l!Vt3[>l'b).Ktp1Ue4Je/#%?R<gE&-PfU*Q.IF<9RTkUh953$\
%F.kW5,d1u;U7+UVWsJUILd`-/+D`8=<@b6k),t^S)^:#V\]+&7N;Bds3m<6rmPHf1$Io_q#b)Y'%b%N+ASDWNkbU[PFi-+?c%<aq
%%E>fE6+PCeoF\<C(uoXkX(`_f\(<LSR%Gm\$N#/L"^Q32Hge&%#;S*a(%*,=k$Tm>:D(7hbPVhMkL&chX!KH(a@C6&d,McT?\e(_
%h;>(^:-1L`\`[RCRTW+jA%fApo&k31CHU]p9.6Jgn\m>2I^6!J+s9j!5"5fqlt'-6hgrtK05H0C^\,Bk\:!GR86#^f3)Lu+e=H5q
%jAe.EjGD.n@&P]'>G5V2SPF2[<A$`Y)rpms6:UFMD>01j.ojGQ9!T[H10Z3?TM$&gopu&#,]V+5SR'Af2V<E*>f8,b)T%D5^ogIb
%Cc!$D3]'7+EEKJ$Z2-'0HKEj%So(57XUh:,jZdHJl!M%u+)uJ&;F!%o"#`kR_"&FbS\?fLEceREY$#iA_D(+mW@qq$Ds.eqC6#Sn
%<S&AM7J&)C42G_CTRNlC&Bo\FA^&GD!!rR/LOfH_AFh-+n(HIW+h?%/7KI.J/e;Bc!TidMoDoqAi\h*afIcZ6CfJ9VfPSJMZs152
%SAScKOc6U\SqSasi?fnfY<GR_Re\65Dogm*0;SuAR``:!\YLS(%5Hc$q^+gu&rIM'_/(tkgf]!'Jh,^_a^f(s]+J,-3`8^S1qgtQ
%k-G'HoF\AS6\=Zm1U[ouf$RVhB?L8]ckIGK:*\IJp5:*@mulF&mLVMuT[J7U/DReGeG"[K[m#^!MXW'/K;eMjE+``q_G`/g&B-bM
%R0s<+M=+i5_'9Z',%c+PZUNWUI7,5s&YHD+QYJ"F>g\j-eFdHo>:[@J*OUE:W4*(L(FNe*]t9aC+AW%"B6ItdOPrOpcn:(dZC9hU
%ca3`</X0rNc;WS:5)##jd&\('<cjhohJX-tjan%1Ngo+AO;f(in_<V4i5_YT6"Yr*iY@Uh>b:FdN:DXf/r85%DbJ4s)F>'W6Mc"V
%!]SSfWm$p*V7YEW^r/gc-EDa[1;NP7b_Qn#f>GeK,d7k8VpUo":07+A\"XKZEqp"lgpdGL0gcYcHJRPGII<Z)PuL[s/oOnlL?1=J
%0C&"L^WgWH@+;M5,^5Hoc]IiI%kr\d0P]C(.LjYh"h04"4)-15K1&&\S6b$^c'_#70ee6u)_F7+gr+nu_o%M(EM?@lHd`7[R1QeD
%9,Zn%XmBPs+q5B`,@.!FEH'PPpBF)p0e4!V(460Ja1>QT&0;<El,LU,`0$YJ_$urLA(BOn*=>27XK+m#mOK!]>h408:/M7@ADlUh
%ok??5Ah+-ScCs-("0@EgK5kfZ&p")qVb!d+*go_fL=E:o;!lp^IS.(uD.s/\P[dW,+XiO[fgfq7mL-!YE`Ruq.ln12'aJ;c':N"s
%(gT*+?`0*8<%#D,9%4:/<*-&J6m)/`I[s2f#lH`'4E]iF0-6t%MD+i2m^J*C2f(dHlB!TO_V9hpKY&p6"$b<XPo!TZbJ95QaYX+>
%bt,76X8qi&U*jQ`/7>LR*o3%t9"aiU7]JR6f*jCe(4iJ@h*@\gBL\(IgU4)\a/HFJni[Xof]_>06:!&m6j5`W>3L'6T*T+1;-"s<
%3F`ED>%61WP_,8%U[B%n?(=N#_AH6](_P<3T?YKp%tg/MO+E8><Q"H.M]%o#_Y)BBRrO!X*n'!o270!0Kj;@7?^N>3/a=J3X&m0'
%6[8<_n.UJjU_Rq5.%"i>`AXS1=>an)_imN<*heK?dT.h4*e]1[9?9k.mofNPEW:a(b@GC]k(obs1dm+ph(#T`_=V+h"^iY2m3JeV
%n<\"1UMp2FP"/=.VSX4Z3g/W7fQB#\%YU9Pc2&[);F3#ipRedgUD?l,P$74#;-(<05OuJp9dBtY8OQ;f,2C!r@%&_^BfQo0,3L\`
%XT\G6h*nag8"im6kUs;i^8ZhB[*b9of9aR&<BaVDI4rnD,i<-(go3\UN!hp#G90ddi=YT06iG[DohYZdTf/=I9"93SK430)j0&80
%UQMQuY!^Vmf4qoG(tP)&X<A`p]>4:i(a8?cGNr[k<CK?lqDB-/,TqnISio"PYX?+:E)tU$1-jLP9s>XWgZ@0qGc)1!Wgf$"\U-VF
%h:WkQ?8(k`-Dssd0W0GVY<A70ZD0rU%Sc]87Uf'1<>D*jdWJrOS2G7QmC#Ao)*3bcA!XLMUWq4$.Z5RF2'Tm]I0,Xn)rlB9C^3r*
%$$AT[PZTG8B4QE.gnj:nN@-7faDJH1WoAjgm!PQl?.`V`;+'DC2@5sc)U_7rPXsSUh0?f6DFJq'?K!OaT!.tl]'^VIg&f,2eCc#l
%e8st=OW?-sb$9_K>NI=-=p*d)@(p0SV'\5r>8.To]q29n<+`=CZ=/ee5Gjn63fPEul[%/X5gJZg)BZFTZQ[2J,E:'ZKK7278N9HV
%.LLkhm%)/EVoqbF??.3d0Tg*iIOeZ.@qtT+Rn.L$0(t/`!Fsm:5jd^Xdfu"E#&gD&dYh1>+o)qfc*unm1[H22pXg:17G[2q_6ORa
%`uU3d/(lQM#/`,bO%L=lXN8M4]RhPo:upjrWnlM2E+QI:9lVN!*gW5$X;@CAZ7_6&R[oaCj"i]d'[SrLcC7t%Oe:(X0uAg^SiA$%
%]4&;,T6(WKO.p8A.-pUp2_jZEAk$>g>"YtON/aYCc+51;'Zg@J:b9H##!$C%FTCgO_PqB2+2L9T$3r<gnf,u2LQl7.qC?735sc\"
%>M6t^,&etV.BH#&G;0[IFIVo?@Wh\*[cc=r6lNr!*e2N00`2.GH!5!)-%TRo1D41M@75mJ><PWuUr$tZC:faVGJFmXi&38#MHh&M
%fuE,&K'%"$391S=?78PVAJ,HjJInk#2A=Eih2\X:Kf,FC*D&;86&It:3Lj>1Hq,-%5B?bT+!l,AS-Y9el'N;nMdrL!`tm3FW6qM'
%;%GG)B)S7<;h'Hf`LFT#Le#J!^:cs\Tul5cb@<<p)F^\)(5ZN:gX)n`WE`;950SOsrkDEPM/0#323sKXLRB[KG3k*2LZfbZ.`?)'
%ac,F&.elq\euq*`.gtdJ\KG'5.d0"_D31In=D_s+)nHmhI`i7tT,#md[@/:"==IB3a>-HBK.-C.$N[Nq(Gnc/')ijgDmY\/-Y#gZ
%!JITeeVO4%REZ[LH-@K`"F5RDUiuf?W_QnE:JP_=W>.1J?a=g-iYi=q)="f2obG@o_8h<%UB\@A#9imuG;HJ(&.KDlUr!UN9eV*^
%b6CTqZS0dZ:IiiF%-m`$%1D*j@Vs%.63YA>+G<GUHPUCMn*2,V:Hj:M7\Vs3Y^A`$^Md13p9bOP9I1orA6F$e+?uc!*1OmE@`^kV
%qSC6O_n7KMg6&TIqcKHAUQq^oBe%TP)Ec*i&5K9:0doS^CF_($`/*Y)P3p5M+tEN'78W.Z&:[;<k3UR35ZgQ^qh&&_63ck6R_$%M
%86N'#fst7K4p(Z,nne>bps%_AN=3O)Qer]Q`CD<//ta6%jq0:"6%;Lqk=6+W'/GA`pMJt.>7idbE:O"B0U[D)\1:e9f^&gEo87>j
%CJS<eoKbKbP&q);R#%b_h/-B`3>e*0LsNiM$aIgH89l0U2Fg/il0&g;@lJ,nY1ml-:At1j<_=R6b7ls:!WAURqigU)J&%b$]\`PL
%=R'Yto]0,fkZDl70JG$\(P>#LZkp*s11\i?BED$t^6hO"@T^_S.&]r#67uD[^b*&FRp7b;bm"rtQ5O6XM6$]2,uUGc0L^5o$5)<r
%3o`"%nBONRF9O14l7/X^2inJG9P$TL<[>DuBUbl^6oRh)<6^r!\/1DP2*#4$_OW0TG,D<fn?shZM++pu1$HakYk%-QG#F-6-&TWm
%/5"%6m#7Y`J@ANK`q1\C$?)jhUIK:I;,SN[JX`ud&2L<TT$gZq[G,)%e#/m-kn\2ME<%NUF&=#>=KIXWDMhPjiU.D+#`tbpFVpC7
%!I%U1dD=b@W$jOSfIF7G0&*):>>87,phYXIkRe^>E2`VaPi7'[=G^tqb2"_,Dbptklp_cqbqKF?E3:D=9Q9JZ3\qe[L>01KpJ^EM
%Y?$tcO9d[&'dX%g,9(L1o1cM3Z.i=]gG6.S\>A>,j1&J0/8!M1=gR/*dKhAWgoflRAfs9OJSM6L)^W,JAkfc$&N*?o]=H:)o/6Um
%U^qA%<7bl<=#-\_DfHG;!4Q1-4;Z!9^ulT]P*AB#_^pC%Q#\+oF[nYU3_%j39Z4F,XgV]h`uaXmb2T"^00%^>h[<on*B9qT`cHBE
%"kt!<52Aj`5O)dpU=b44"U9)n.,q_jI2UFajrLkR5i^Gkp]O&^go)ho_i6P0m&Q0[PK6oQ"k8-H7/]3+dK9P%a""/j4@3G>#cngh
%)<pQ.DF^\%>.!8OP&s?>M%PFK>UVnuhR+@0r4AM$1ipQ)$nWAI40+L$bM0'd'2UZF3L16gr)2>6Ft86kSk=FC2rc_j,BHQ-%2Xl/
%E:sY'2PN';cqj$S<+<GXSEjQi=R/V5,.B^<E]lb@,H@M[k7k-4:VJN2;(7FYPV8qc>b[K4_0dd7PfI>P&it.@TSbp*7J\Lckai.l
%bC,9]!!h:AI8Miuc'8eh305V4iPaAk>Jd*7pkuA3lHfr=HG?D=I((r:RS2m94C!\tD/RVg@ZV]+1GgD8"$X^d'L@q6K5^"'SPD6f
%G2eZ2#0Xd#(rRG'\Si@PG5G^rjU'IOSeI'e7XW1U&P1`W:L6pOlmY([2lN[`9Eth:R;T8ihKMjfA)]doIOBAO,.FgL8LG:]%Z?-b
%XCiY>Rcm8Lq"5sT;*SJL0VpeXVoJTi7Tq!aqIggQG(//^LAR-[>D<78L)dlJ)5Yk4@b=>`qK=f*EOCaUaA0AkF?5).%LOL^bp4Nd
%gB6ETJtC=XCYfh'$rL1?)#r:FA0#t=k+J0c;h(C(Hj/:aDu[PFWX+;^kCMs^JK.!@p[/g3_@DB52YpULT,X*D[$!34O7[?k7?![q
%<p;qc?G7uf2bUbJmAbBsE4PmHa2KWZ!>t*!qq51n>KUgX.'jDj9A+f85@&#>=3F1pV>5/R!!j7F4).hCMlUXe<TrYK2>f!mK@8/p
%S2\"/Lesis3`q^=\CsM43;(8/_It1dff_:5e3?JVgg7e_8p;"rZufTl<SKX(_V-BRAFrg*A4>,C[1-)81Rn2%eOFCD&'JX!7e5qU
%n9H&kd=>@r1JsBNlk*?Mf")e2hSs,485FeRU6pO*a_meofuH4]s%V;$9s%hiMI8\8lZ+Sr6#JY:8FSNa]tuSY&'GZ5?iEFnndm@C
%ZHo!G\R%u-+J:aKpoR0k^!0DEbC>s']UIOqL3-c`4\32c0bCGPI]8a$=X$i5N-O(bPFC"aEZ@s`+/'Eq\bN94LXVqnfGX6l7,9bX
%L]!C9=hqMEp^/JNO3iM6#X%f=[(puBR>aj+$7L+808&l^]]U;1>*K!'(q'=BU<Wq8f'Ab/#atKWJ#-h\@^-h=U(:?"DeYEp5HYd:
%:K@u,N\6uNWmM3N2<f/=[gWTBKs)//\>`%USd_V(H>pKBfUcWb&^JUKMr-!.5M>e^^m7"Lr>6YiSk2=<P2E^%(.QQ$l_Yha!GHE.
%!R`O%O)ULH"r/Fe9"3n%!7_3kZ>'#Wr4,X(M<(j%IbtS=fn2)9Z^T'VblpIn+0WjO<-OJD'-JGM=YZ6(&OIBj)Rd8c\)HJgYIf\[
%(C3O=n9?kmkrrk5+48J%m>g[=38lNl9`2q@2>YcmN=mk75Ddo=_j:7b=H+6_cbBE4qWBt3qi?0GGl8E\9&tJVqY:)uOa^1Il(-0K
%RP?''qZ5u=:4Jl[<g>U22dc`TWb?%q1Ca:mPftEd/p<6*Xco=WS?Q\kC[C+m%1QC%"tR:"=r=F/2aR=Re:,:8h2UYr_pme^!nOYV
%,cV'4Fri,O;]$MA)SWuqD;)MCDUs`Gg_Gf7??himX!iWm@EUNGZ]`%#L]nNm9dBdV\'-?8WLI]?F")+q.`?*6TM:IK?n_`bb5<TV
%HWNqEX:e7<CeI4GJdI1P:\ZJp9Yk)^OngG7`-2adfB&NIG`H_^M;/\br;>H-o1I;@fDO3=<68r.r[p^V&0=ZOGG#)!s8*Xa=9@"h
%4/@`Mc?'I;gbV,G/)5j0A\^W^$nlagiNH0U-$cJIB0`srKLt5JZH(=7na_#&3&5W%JjH&.r&jjpHTAQHN:qM\'n-0a+l0@_RfcI<
%/^I`I)EAP4X_&',c]baJ9duh&]<Y=e4-c&1g="3pWp1_Ip5`,6V;=6S@*.Nl=YZ'ZYs]>-ZRph'%>#o#Ul@Fp'A*kkg4cI0C]hke
%`C0bk;t_Jg&u@QA]q>'p>Jq.OHdR$0_sFKl@FW?*>E96R#I1.e0`nVt;D1BSlm&mSW&N&QR_i\Mjr7d']Nf`r&#Yg_Bdn2o<g3KX
%;I/_l<,ZF:luli-W:oGKU=NZGH6dpkgOZXS#:u-J@3s(q;2^V>N/H+Xa\du&PQO>l;DGm"5&bSN0e:&0>'6[S]%riU"5?U:nH=#"
%jii9`<IZ*8CZ@/gY7[o!Oa9UE0)+R!T%l5/=`8B?U5!@;C2R^-A\h'I+;l+-aFR4#4UASo"jNT`56e(91WQ+#b7&lZ*BBKMY-&<;
%hP>G7&lrs9JIQ"/7uGBVH$:PXE56(B]_bNC"1>@o[PLI1-^C*4"[jU*X^U#k=E@-*^(,4%cTkj:+3IN+3Dll3iWm.cp,qo6=b-qe
%M*9:`?:Q\.`&FK4<u8i9&lqB5OH7^DK]?OI=>\7;f.PdqrWWO8q68uhej3reLVqs\Q3m&Yg2F11$BQ`8_PW<N(+;&00nljT]0*]Y
%$)?W)OQE<,D%Vq'le?%YNOCLL+$eMl+Q8WT6"JZ6,`pq",ZVf?[)!g!?#24Nkh(lC`sBk84@cGPr7'BBObX0r"5&kQ3F*jkQZ)pe
%2d)]/#iQ?Ci>f.u/t[=-<!QJ_EH0**;a2\,DOUXU#X7Sc`Z%#iW`F53@L6MZhL3P?''^dX.q17[/&?iNW2b"gaq[o5eBPd#Tj)[d
%oSj-j!-4`^*D3PQdfn*1P0ARcG_MiY#/WsM!\6]erV6_5M6J,5Qt[Hl8#i;g@p!s/PIbRk&_r2,EWn]NbkG8Zf-!_[65aM;p]X+c
%_Z^2B-*ATCoa=kpmb?1.-[+R32K@)r,XkjY.6f4MGenN(*Po4@CjD'n[h*1PSNOCh75DK2Kd2D)e3n+f;/QnDd1o+M$M%;)\XK4b
%F(VrnQ_kfB0]gJ9Y9FouCBV$2g`K5IX-Ni5bMY9p1Jk(Zqe:hi"fQU<=so.AJ(\RGLB[9$!$EkicA#P\/Y]O)ln3GlFfc)s^&m5d
%&JsEfHA,)TNReX.]oLd7la_S$J.8UgC#fc$lCEjsE`JI^'oWOf)$;kOr:>R-75sd@\$7&"f#94aGBEgqC@&ApL0RDdPb/9^0''uf
%rMj1*%otE=+?p[EbdSKKahPd]q_A(VorC]YcZk;C-Jg?1r\pM>);DhdR1%IL/%G]bng3ogK[jk(!\-ZJ;`DoOR(s`YSK$4:i.ImW
%RN'`Vk+b_25`D)br+O.S)l'Br@8k^5=,b*YCjnA!F*9t53;.C-*HJD$]%`Yd0AoR2[DSm8[<J"l<+%*0mU"V-_qS$?d]m`8?,l".
%XiDJa$pP6/cUa$2hDitXl1!W@nqjQNUR/IR_F.""UHL/hUl<dRj*G:W]/cU.AE#?&8aeOJ0h1V'Bn]/q)tn>Oa#d,mATEA]Z\Vi=
%/G#Ue+<RCg;CaoE_Nn4fcE;!`)XsRFV!ff/TrC9A4mS39M4EYIbk`sQUW2p?FcV+#=?Of1]o>h3qLT#Ke+M4TX+Ie)kZ%^n';\EE
%,3E0:r6oh:joSnY'GY%FWVL^'`gCiKD!"<c[a5:V3>YZf<37=$Eg40c2Suk,-rt11\,i:tf9QjuLagTi^Ti"56E\g>3R2$>PZo]F
%AVk/>Wj>Zo8!Qq*IZegjH<-Jr&;Hr^DatbBL6h3=-'6FkdUBHC^pH1V6V1o(+Pi>A3#Zqof<_bd!13@5_?"Hked6H7-pi>iMn@(=
%&.DRPTa=+[-<4UP[tLdY^];]moAWPeeC7C[<i@.!,WL<@2'J^i[=e@'0ajT<.sH%@$kbJRRaj`ID'C;\r"FJI&2>t#YH%F1iQTK*
%VHF\@fE5d=f/5B]glRuV_;s$LSA+srN+clN$3/@*h*-l-rMc9S/QjY6fntsJgscR4Rq]2oKVV^1lBngIbakB(==,.1@ZP+4bLKIJ
%*Qr+t'bolnf[.D]&RLE6?]ETOCL5!pp():mV!7.OTJE+mNm8MW#4SQ/cWVn5^o&N=1]"9g^fTM^L>:#gb#Fe'KN1ZGVRYY4FQ6Dn
%lK8aC47.N+l*C0GA3$:srb"b?r?S<4SQkB&11Yb(U\$d)<\Q*CCfnQdOg6\"#bTp6W2*!!3W3R+W>3kriM@HF=c%8EPUL$/Be"<b
%4C_0a-s3@d:?I=hQ>P/gLZD6-gF1cDV5gE+$F+<?.&m`q28psbHFCro+Wtf;bA?#:&:+-XU)[=s+Z$ZFD5g0^C[/O!)_'"\Bg>Q"
%nr[tC>D^*erY%Rge_Cf:rSh!2ej-=pg4\8Br9jOY?&5T`;-oBm/k9Sb%oi27KF+'M&:WHUGd!g!)_g'qUR@[!@mF-C9@P+)M+ba0
%#Q9Zj@S=F%;%HP1Va&%0p7,t'ErdWj@FT^X-'j[5qjHr]A9;n(C<S8DPr6%I4=s=PRFu'qgtPLB1p?j`Z7u?^g-NTo[oLU1:n%7L
%]=M7"[Gu_QE1:\'LjE58PIm7VUHcuV'K8-gA9YIJ;bD2+:5]m"4:8gsVjfd31/DAE`*paP9FMCQEHL11)Gr'-_l0,V*omagn^J%?
%>Zo)<9hGBk9+X)AL:=]V=&a0,BQBX-%h)m*HH`':I39o>6(7>6RbL_#LmI>.G56t6ob/((=UdX*/>H"(GO`DRI&ouuTZ=,/`XpLe
%3Vr$0PUgnH!m2HOD5la5OpUFsXKJEerL(p/8X;)s)ksNZ\a=1+EnTG,Z\o&oH7$D<>ANu1#,!OQq\9=6\)f(+@5D\0or^`d]4WoP
%75%^V'9)4V\;)ATgu?NHf"_L/i!rpfX%s9GSgn'a3lC;JWbpTVdkuYOCq>Lgju4H%gB5p4QHWW&'75Tm(^2bq,V^/<!B%/.OLl#W
%&iaXS1PM"!d$=$_SG:L$l("9"\.H6T;S^,N/=A$$&Iimgl^FQ#FWq&qoK<CT;gj6qMnB4H/pTqTo\-5#bLOjorC.uAK-K&*6I%&"
%$.Y=I_P'`_eS%V_E@p#ZRRkIA#GCe&OLl7VOUDQC$.a5njr`EGP<9j1^>]ct@7>b=o@b-ocdd6A-42/4116`A$37HMU0GX78j2uA
%Z71.YjaNWQXR`6m;q:Dr*TL:UUi!5?m79Is!>pGsR5>6oh8#H@8bX*3Z'GodJ#J@]]"FMF6XAp)N!@Wh8'bOS^XVI-WP)2E%gK0]
%Y=a%\7jpe2Kjg8NZiF=!grhF;P8iP0*Em*[&iftkH2PqU6G0u'<73K*fqp3o^@maZ4+55npim$MjI6-R>*7)I95S)cN3`T)VeTln
%^3KgCOOn_:bKZbXqIP/Qr^uW-cCal3J.%b3^lJP=OS2V+7`<',fkl>QS*$s=!]/9>^@-E=7Y"D=Ja6DTaak;%*6XY$.0*1/PC%s$
%]T!NEBo`j(n;mXG1TCkpI:%4)M]k`JAe/E.Ve]b$.#Q>'?F/IqaZPuN&V_u$01#`Th4EVpMYa&>]+r:^=J86=M86)!<>Oe\`XqS<
%$n__g9M6)hDme8&-0Sl:mGE%Z6<Ji?k:%lWL?\RXB=I%?-<An@Ni^jW16UZi#10DE*&.,cef=bknI7%^N,.7)+nQDMk+?,[Q9.F5
%d4&:EE!f;`MB?_nNKAL[6j7sqecY[Q1N9SiF4\`4FbSc'N4GhK;3e>\&K20i(V#m+;ai_42do*Unm*DF\7KI,DH_5ML?7(h["jiq
%#'aoIKq4oA[Ik`X!k=cA<4R'gkY%KQHa1o<lb`=)d#D[iYpfpr]Le%6^N:SlL4gE'%cdXc<NRP5@JEPQflf&ZAYqYW?.ZH=qkGrV
%J$%qEj&e_6BA&)/W[dB!@cluM$H_MT!4`D#;%;Der#.3qF^pcg`*q35OSU/Cn2ksWVk-sY[t.e3b'Rh7<7j?I5tX"1h[qb4>YMmU
%FbqFL4$$N1H$d((nR5<GfD$@0:h33hHDrM._A+nI+FNX%dfBZJ&Sa%XrWp^7MqC_pYZ@!/O9t<;dOe?S;D#/g[^s;MB?G<PGW)(E
%X?-Ib+rA*R4[<(ln&Vut.2IdbpCTDU7;<<e=Md2g!2c^sD28Zdg6n$'4Hl"(U$P>T0L#82.76+HN+AM8DjuCu@C_/Y6D/)efZg;i
%R"Qaii>oJb7K+X&!pCZ74m\CFEeJu4gc,F+(<%i9^?0ZIK'(/(6Vrr\[d,ZV7/.U?T\`0Y[V-nYe"YD/ojCddlnhn.Y<&iF691Go
%,k!Xk,SpX2(+eLX4)&H.fhIdpQXc:DAob,SR6nbK3UMlN[#cdk^bq;&b+B>=)JPO2#YCb&)R5\P,M;;JAibsO.,%GGN?4`uZJQUD
%^b-9$<\kFT+(g-;WK6Dj]7]^Ri]dZjoE*Hb7UORLhQ3Rq)K+H3CE8tkQ7g^/_9@7T8@((!!Llu%!GSCL_#:g/`]u/doXOal1%:<,
%"UD"qVCj=ED2[JS'VPhoCacbf$\Xm$s8BH\N$N-;+I+5g#0>:!6@*I+<\LNEqd\hK'1d]gEbQH.UTm-f7h4KjRdc*=H@K+,m.F[6
%_uE=:r+4+8gH1p"\C>EspcnCgrZq:J7dK.C;%Z57*O47p=l$4(a<4ONEEQQZ:i@mZgX3B^KsGM:kA&*["D;U76]h!fR_t,Y(5OXc
%\k+lt@?O=e*dtoPO+DdF<#_OG#7!j9hs;&r##4)ObE6L7&E'5&prZgimk#49;CI0grB#aYg;lLVDd0gOoo$nM1R,WJm4SXe8hl<)
%h1@mY?&MMgPWA!:'P&:3Kc?2bjBpP^8oRG1+DKO,O5428#W(+9Kg^V$/RJ\cJY'4!ll(u2B8c0fW/k(D$O#MF&U4ndnoMF:?4Y*g
%Pi>Hc%SO`^&@*G6,E1ibH/qrlqD<or7.TA]Ks2&CAjF0\Qk_j*4N_6nr!IS(L%*[0Wg-s$'T]@uNG]tOgm`o,i-g_Kck<,KQo?YY
%jO,h7S+?(,c5'2038"ES"a%1XQn=+se:NDIX#9adO3&5!)Wr[Es$I<*!@#>/h=D;ZWI2\'^@gb!Lu-F9^Z#fa.JBrJ,..NoCjYee
%4-&Fbm&Y3[dcN5lCgq+6:)9So3lFHnqU]`N>>]&1nn?7VD8B*5`a1G<k=EQ41mg\327JM6%U#.F$OHIe-?iZ^MUF+955C5iq2+u8
%7##?CEk)P8nL<OrV/%E>+BmR6`q'I,*JA/rS]PG_)`%OY`sQ1`p!GQjgf9@8OI3ftG.)6!Gi!Xtjl-F\?"1QHopUl;Fa6@U@s?Ga
%]]-T;)<#k?1Ib+,&S3Z2]%#X4(\.=ffL)OW)r2gCh^qum/5`goc9[!fhP6n`UGZ.iGt)A(Gk9RL:DD5O4HH/4Ra(3O.CC9JYG,.$
%X>@C3QpQMtZPQUTd-auB91qob[@&G*Zjk`tQZPokf/6#O\RlY9Z$pG$2h58oDL5>n-o_PtU*RV2IDZU)bD^>4).BS.>9@>j9F;/Q
%YQR/%[C`M\Kft+beH!R\G-$T7,oFU\Fu^(K#?P>?6/,+c7P,2+_MTp#?QMr:m#s&,2>*f!,)=M2@U*E,YT/83#AZ8Y$8[V$rI/S)
%1$C.)kGVH1S7cH_MtTO?WJV[9?7U$4EW\2T_=gqeJh7^92jH>F&FTnuLC,g+q,,4!cO'OMkDUkp#OF4,dX%FKUm4;`/^$>o$]?$l
%nQWJg!Td;kQ#X'8*+;@MCrL\U^*sq%SiK7ojXl.&a$*_^"](AL'r<\rN?J^3Dip"cNAKV?ohc@n/8sQ4I!&]NrN5Rd_;8%F5A_-2
%9"hqFTn<f3E/L]W\#rBHU\,L4c+3W.TWS>u>%"gWNmFp/._,1?'FH+`8Tp3XP"EHt-fP-,4.nS9qXkX(eeO_h6.=:^/db^uY3p@S
%XHN,4;5c$pQ$0c_eJ8]%1:6:oRAQfh2!-A3#.R+'.NfCCrJCQqG2mQ(7m_6m"d[06Wg8!F+.F?_DDZR+jDA/eT&D:P8URs"&'d%T
%\/:Rb"X=GmdV!P)"[/@YiZRfs=@d=M$MQ7Z?2VS:NSXh0*)!k#gPpGZpc@HOk,&?!i9mS2e"^.X;d`AEl1*u^6NjV#*g&HSn\$cH
%mlM)u,'eAeH;`k!KXJaV.1AuqJ285Ic#\l,M*=+2=;44ANnNX$4N:P6Wf6nD8>a<S=&MQMlre1IQ5Ul\2^C+[!lg2IH]Lu*;[s(4
%<Cnt98%f&ZBj%kJ8K)?XmCsa;\E)LXLHSWZ.t*C_*n2T\X,EVhgL"t;TQB5=%H8e5$]:tj=-1>EU'Up,9nmt,Sc>NFSC)3Y*7NSH
%m^KL\U>mf!)F-A0Fl<It[Z2^J:4'U;[qS07@!#D!7[@]]<%:cqI$cc<(90!_'<2]r$nCO!4Yfrd)KocAbeQkS.fBFSRgEA8Xl?.C
%jPlu/I,B_.hP&q,#X?dbIl+Zh1Y%MRs6%biJ*P\TBQDnPKprQI13*_o6JaTSW-6Boe&att>Jf&\Qu>!'8P_I)p1keCg@_uq4[S+"
%_/@lS"I[--7F<rnMRt6ofi?5um.qu9$L34JK&?7KHkB%Bb:Glr`E4Oq4j"q[MBF&VXI_;4l-]\m2(VV`p"K/\<!3NdP[`_n`.LQ9
%49gV.L#/'>k@Z(O+>[6W#q;g=ens<cDET3KE-6CHb,^DPWJdndlUX[@?b=IO"d_)^I?U%h.%ESgeE4Cg&;g_O]NSQcd2he$IkGE"
%eeD]IgTC)]a#hYOX0*m!`Q*+3ZUj>halDd1%^CP(hhI6L,)$0Z>e?<pm#o[5p#?0[;Wp4`rD7L669&jYFdURN[6s;af<C3T,)7It
%mJ=[s_qp*b\G!Tr/0TeorpIpM1CGt-*JN;k5=`C8@;o,Sqq9",iE>S\S*TX5Fg3('($<O:bRj##*b=qdQOsnD_g,\Mc:aCnS/6L7
%Pd?RG6NrZAM==j-eUhkN]pQa,h$L6Y@0Yt"YO-WR0g]T`NtuW:TH:%-7GOcAJUELI&QgZf(J?W3H'um0Q)3)`U3udm-7Zqd8O/8:
%,<;f9K9Rnu]"QFZ%K'-hOeD:^'C+kW)#X#^Y`i4#Fd6Z4a=sTH=i]K'EQGJ3KUn]\4>BCbO`SGba`ek9m__@)jYS\(GlZ3KP2ga@
%nGGSX\O>qHQ=]l8T%YW<O%PYn6=0CA,L*1Pau,L-.7I06MDGLE3A8jtNM^TW'4E7AQgRaX7<6mOfP+b)@IfaZ+lu+iJTA;^G6BYQ
%Z35@-QgdN+H9[`.Hja^LiFn[c6mIBC8\+5lbii#k3095a'981Fm:qEZP;RW+Zhrb,HYbr)ia*AP+`hpL#bJ[h)"u,C;XVl&mC0FS
%1l]=],CI-+[(GodMqj=;#&k(8m2l5*T/u$P6lLsl!,A#gKq'`1.!H7,)\qTQ)sZEp=VkEPQio%0%%@dH^rK-*em5\>emkXBgd3_L
%+d\a;7(8QkK1m-KY\p0-.6RAgmKc6:FtA+O+SokaY2,PKD0=!tYXaF[LQ4Y1XkoU;GmenY0Of*6NaanHL`+BbeeJjg?7?!amuBcE
%ZS%9*C$1aJF7\GDdLIF1?@(LPj(E3l&Y)7a5@g!Jo,#GM@[\adr3!*N-mqmD#&#rtGSgT\>0^.-*D#7fKn!:"-1HsR8PV3[=Jsb1
%7=K5^'+0Snrq$\Ill#A8BAON7IOD$d\OfCF`iq,jhZ!IfT1IMh;Al/k0oha:2H]!<i\>AdGuM7tdaHVVQJs8`'_-;S5UZp"atl[m
%/1Y#5rfG7O7,*]M.JQ11_`OqY_/T<gjq6ofYNSt:pCLPfhbZZu_n"j]9m=Q9djuhk]7f6LK#g'!=mc1c=:ZK#/'FhVQ_3UgC;rmU
%"Wh[oP!IS(hu4XH2(.7HQkhK@j5F6Q[]_6kR$L_ohJ`skTgqm*]rZI[.9&"gc-$$m[jk+uTr,=jclmf0.HKd4aL2Q.)5QOMl7@ms
%9!/77fpf%=Bkoj%@)V(lc?tJs/5nu?kb$t4_fi#jSpYc[>NHBYk*reXRMWh_WOB*>$425%f&*KOK\25Y&/*p(NofL4<KW$>K"h."
%Gp;hf&M#^_L#r$k!/qjJ!nOEj>!3EAoSX)CJra"paN<?[1V5d0/49>_)/,"XI@#_k%J^J_np"^'PYBj@'M^5S@;E6>#Sak?@bX<_
%NgaJ&F+3]bg^$.7Xc9msp=<0;!_hIQLS.+6X+R!^<MStJTI&b,hL_')WU0@r/.&irO@_4'ieLZK:Eb'j9??Ke@4er&J4Nm%!?*G(
%2GcN-]gO2Ojh);!6Vng+R,`&o;2Tf=EXjJ-Fr,hm;j#F#:UIXorOH,V5l%q\1%)sX$>Xnm6^I"IJ>,].Gi*t"9fE3edKl/qG$I18
%M:*Ifa/V3YSktJ`cL6Rl($bAq)0`B4.BNs_B&h/V4dK-/"`j>SN0)Wcn7Ik,MZ3.:c-tg&k@%B?^>3Bcon1_Z&I`35X\hRke2V7%
%+\aSBR)-q1gOgSZ\psuOo"''KJOh'q$_c26]fG0u:tX!m!QprQDt(^9dOqLfDJ%<E(!6L%hOElcOsS_2^CnGQgsGI.BI(Pf5Rmok
%C04k,AO1nlPlO8PJ,*j'r'\J%6\(j)5g;&9cJ-s!qeiK<[as9h_mV&51*Br(h'%7f@T9^5#+9iiGH#uo^pPULPof>E+;5CH)<>=+
%6^)]dkJN>(p9.m`7iQcP,ZdY!AL80<_RnmM1I`4tB4q,4#ZeI%nYU<(ERbrHQb6nOR1o`tagit^@flhXD)(A$s.5_YrBNFkLSVK[
%R^t)L[r\m(XN0DOPnZ2WLc&`SM0`aof%9ZL1KqtKpG_t_,,_uj)A==rQD4`E\/b+tdjKr/)U0e>QVVrZkl%cZ^UM:6n6NZ"MC7L@
%.&'+j$b4?^!l%/pj\N1EhbH(HAJhsg?B!bp)PDjb'[f39Xf[*aG35Mc4C(E4=tbe(k6OHhfUhK),V!,dT0^M8%O,0u^f>VVd&?RJ
%N7i=s]@:_+B<Wp3,7b].[?2.D2S+Xf\$dIhdU78&KA[RuT"uGY8DHgf##C_j-^aWPSl9gDWp02fBg_\e&rG(.DC!KrqA<TiAVi'A
%r";pRc8#`^7cPsW^2\=:e"0oq`)#RpNpUnAr9-fQ/;*W:nhpnuU#"L63;cN*!NLd1Hu@uu(.<]MiuE_Xn3TuKcKQED`L99D8o5oC
%8!$^T:knZh[Uej4M'CP,boG\W@P]jZ)i&X7Rq\MH<-RW;V<oPWS_?1aM/Q&8J<!aa`uT$&M_C:6Gt/]7$ONDu0aIJ!iA5_&1FM$B
%.QRGb1IdtR(p](,2f_(.^3<92&9G6-%W,_7`pYTGpbnrW9fn&IBXFWeB1DdcLreH>U>]^"JQ0B&]7OnDWSO/5`AO?Ce5jl2Dp2kU
%:]7ImYPIA%Y$p[hs84hAo>cTOM=,!gOM*6f+Q!a_-0mdNnt8%K76V-29X0JlBU3aJIWdVi!U*LC+[QcaoP,OjP3BEbRE!&?0KRJk
%`1B8A<Qe@F?5TU`8[4dDTitht1a;="f+5KQniels4G"Z@7Op.=ITh^"GYQ#/AG'6X8^)r]QAfRG<.EXgHOJF80X>m">LB3b*o0VL
%+&4^.m.qmO9tM#Ppd/NpWKTBUBl*SN,JP,^Rc!ZTn%-ENiYdK-F'o\HcQ?6+O+*jsDJ$ccThYg@kr:3^Yi1I7kCB&RI]`'Up]/k7
%cjYHTA%jD84A]2-Dm.TO7$@1@:COhUGsjX;bRjjB&t/M/1OJV1gbA$*_DmSX[4/u6D_Zj:g9b@0->UX86H!A110X;eg.[T%D983>
%1fm\>m['HMZ$SIt3i+"HWge_s)j-B#./mc:F<YO_IG%q_g6$BW(8&I&-@beS5PU*<q#Z6reEDb<ln>Pj]%?O7)MGE!g9-N!<)SqP
%%1MaYkZO&_XI3GQ&Fn?Lj0lomdDR(R,9`jt<m:`Sp$@WSC6Y(TV0XQ/*#),tFo??0O:gAJrPs;8*g/g25H]@f@EUP@Fsqcs!@UiR
%qel4>_TljPBh)+*Z&N>5#X)lQ+P0ls$DoCA>#pJc(amH)fj"@ET"BbF4hITp]7q0cBWOe2/65l%aOIQSN#2M[C>`GGo!lTW1@j2.
%*k#mFL]*$PK_G1Ni],Pg\nM)QP^`8LZ#^4-OZJ]3(g8k7KLXh,oO)?LlKk-0)Tl**9>YjrdVP$:Ge%gbAKD_M47I79](*F+S!ZZC
%`TIMEE.K%(TJDsT$t@(2LTS&_Qg?3J&Ls:?A9Tb&G'<N"m`,5Y<a>X3HfEG1g:jIF#INWE'^ok?j@&?^MD8pX'IdNKqjU/A+&2Z.
%R.d[>$t`"F(<Pkh1;5nNbEm3=mi_"a,LA&u"(/1epmNNgq;Wk%_&)K.+]mK/$^:cUHjJ5BF\&ZOlf*uYTifo#BNI[N,-7JURU:)G
%*AP?uO8Psc>H'KK=LtPKI\:fXpR-JaS\3T1;I#SY[u%t84q<[#cLVNBFV;NLc1Gf*g$lGUDF'cE.:?EVA>[7H66:T6J$09W#.X13
%V38-=`YP<^_B^A1"s.2$Tg?<?X(`9@*(J5+N,4I4q_E*i98kP4fi9^dbLD??7^pdm..X`&K\(]DTS*a?98&pbL<dC<?ZIV2JYsgc
%28Atc*'oih/.h!A[jq(,X/;Nal:&s;O)`eCq1Zb'`22(#ISHCuMb,VW'^EIl=*3eL3HU=O$S3'LV2N$O8/X)@a7:MK/?QeLBn=#d
%[Md'us*X52%_s?\lrO\eDiF#BhsYec0UdCdmqfZZ4Q*q*[$oB.a;1/XUg:ZXB_pCsP(T[r[a,R;CUXJp4,ghX<*CUR8[7*A>qh.6
%(re.^0c7k^bHBR15=VTGm8V7RIp5<N0&%/W$q+n^CcC!V?reotqC&,f*ZB[4DY(nK=OA$r-@FAQq.\_@1/iJ5VHmJYST:>W8@cu0
%SsD[Cg"Ro4*7rGLp,(!f2FuKsN7kmj2&LJ<_?dn2#Oouqn$@>SRnb5V9Vndu4JG4/Cj^&kZP2e$]TnH'n6=BHl07SO;aiY;:%2C<
%,!4/4h*'Z\a25q1i2mtp3i,XC$iB^NrL!PJ94q!QLk&?1S[B=c[9Yr!s$)X7^Z<PfBb1+3;`3*NrF<*WE:&u5IqH[TdU(BDIjl@c
%(r^mpHXh#mIV2a\kiW*D5?UF/hA]?h3[RDWbHq^4.LKN8ag\^h9s."ea\9R32i(Ak#gh0)cH2"bE+=S&:&dUD-p<M),.RU=Q5!B#
%ZREr1VLF01aD!1L$fE]2$r("Zhqerg%=8R=_UR*"*:AMs]tOGNNGt7'ThPh"dp"[9bfIjt)3)$O=3=bkT*Xr4'H.TX>%S(Tied=u
%i'u5#4h3P\G`6>XITY2%A3<3Kn;$bXB?W!khq80_Vt#F74lh)p#!]URboRQG.FE`Cd,\QNY4O+.ce/t;/F)G.B7Hu0KX0)n:D'u1
%d=X*/%IuHbPX8@-"3Q-jb!aFt0os+CH`+V*IK?ZfhY?WI(ejR0#o=Gt%,a=3r:a(iV>Xp@9G%L6)=4[k8)e;@Ng')^-[]IY=8XcZ
%#ftYA)G:uAl9QbX&)$JKLl8iKT4A]%@)4L+AldU<VmKHVd4Eksr*(KH3T[Zg!+l='.rN[;$<!aq.])dQN]2u6[JcUX#(dG+-1plP
%V4_oAmGkjM;UQAW^%H2U>%Ab/dF+#(>[p"rY1\rJ6OI8Z5]n.4;-%H#oMekne(KIk<Y#nIpcE_g8Xp@q>I7Tb%n8!A_Iu\J[`C9O
%9lRA`&+.]BCEE3.Pr0al`=nas_`4NcQ#>Nh*.a7U[#;"`_hnf+.Pk_4i\;:F@o@.cq<+@:dJObh*M+^G7j2&QK4uc>^P%/_arSHQ
%Va-Nq9\15'93#8MSp)*FNDPD&p@3fJlja\569NmJcfZr'fCQ_g<d[rTY8Os<9J-8+Tof/`Fmt$eCp:3,`[j_Vr$BTKG)Ko>A<bdk
%1_@6,0$jr=dKI??QZVaJ;UnT%?&IK#+I'i-kKM&i-o!5mU??['WJ7=%%p^X=XC-"A])i]=6/7N"Y,pQuT5#@0]ds#YEJ1T:HcjuF
%(h7:/hSk>Se(Tje-1A&AY*P4;PFAXf0[L3.&-)`_\[R&>Fufp@W'8]?GE@8>1*G$uEZNe.;X%!55)#qdQ,XlbR>@BjI:6n.r78Bl
%BDh;9p^gkrWZ,&C^r,]/YeQ'mjN[F[L%luK;eIP8>;X-XAD8alJ@0RR*45ZCG(%aRghMpu[.#I^K6@_Y69PVq:/X8%0qX8/`fWlm
%N+;Jl^Wc[K&eXRLU#cP+7\/ti*lp,r$bc6go+?;I$j+"aFN20Wj*?:$m4j?3kRjlE[hqDj!e`!l7l?ms#$C(=/ZP^#7LPXGSEtnA
%e04a0NVCtffTdH?NZVJ^*QBRoC!>3i*eD-)*+>c-mHPocO:!!DK6>Q:IF>XgIQqeM]l)rdA/$u*8:K'>M.OT*=iU<C7+Mck'6e4C
%aC<6=*'*BS<p>[ZmuT@ae;9cA,h\h<,s?%f]>0VS`L0Ib(.Fut^^S?6q;tK(]18c@Q":ru=C@EX0c?Cg4q_N==:FQ(LA5O<#pAah
%>!sRCe=#ljP7gG&m)6-7Mk>1_bL"le-UrcZ7Rb`c'i0H88bO&8\4u'1(epkWc."T$=Ig-0rtZh!.g[Hc?2gs2'AA`jN.,P9M([H`
%Z?;%M/#+45TkQ)sACPIBMfl7)CAr1p*U=jn^I+0-8Zr@gCT?@;SF7^3n]FEiG:<9rb]M+k+mfM`aK6Ons5*:N'GSDWS+c^!+\@Zj
%*Z[$f@;%BT&!8R"NV"/6Ef6cpB-9%7VXaL#=;^l-Y4K"R-!Y.h0uBM,NPF-Y5PcG!=HA-9Y?X5+!:0]E5H['cBtm(%01fD0hQ1#b
%/BTdL=d63%I'WJSk*9cE>O8J6d$+E77l1Uq$'IP&HAQtt\9t5?C?;j+"9e[trbdunmNhHn49?ANquqXUZi&J`2-RBdQ4ki(=%]^7
%Y,t1?:#RsF@1k?*l'P!,=:3\@*S@R\[K\0cWs=atbKV1^jV8qUYgaZ0)?nG>MM<BMU;@!lDG#8Q;(C1k+oj(D.^iIsR@BQ8D2LfX
%O[-:XjbP_;7`cF3WAK/1kY:t7AsN#p;pN=&-kJWl3I2(hWo(L8KVR+70obg)Z)^qc4#RWG*lf3U?k=oK49=G>RKtK16<l`/2Zg>g
%YIkP85n6*K<(R#pPKVFD":`[4r",9iP1a>`;TAR-'MB*_rA7*haQP7,6e[_Pr\s^eZI^*;0'rcA*]!@^Lkg/];eR^dX-ZD#21&UL
%_aYCWe\,\cC1g3s-N@?@YeY:&@e`;+4opsc'n,G!@#k#'ZX+RWACQ5Di?@4p1'A``W'j7)1l/LZ4`@7adD)dB'=8Zh-QZoT)7&Kc
%:fZXB5j"lf5R@mL[OjXU%q&No&_8Kt6&@c.nB,Ws2A#6+&p,rT\=KlOHPQ4h9ir*227)Ai;NLN;Ee7Gj\\=H*\jMIAippER-'UpS
%/*;AKGSQ67!b`u`b\c-.0M)on.lXF(DZ,_Bl6&`cAGYl@WPJ^oP=F^^ET2S:,8h5Rhr0$Wei^8FPCsKeq$PfeRcLI/787Ir!@1fd
%J@"E5"Aet]q5;NV#]0"_VV%caEgu%Ek>qXB[/D:]=,#*`-p?(9^eW.T6aYSVBd/aoCPJ?7MMWVTL'stYXb0K9.u1XJWX$$_C3uY4
%GU%aMAmD5fU*bg!di-63I7K6b.:h-I_qdQd^t?T=3@k[9h$`k)iY)/[q?78OSGt\C+T;O?de=$CC.L&ZGPROZ&[#r)j)'\'k"E*7
%[I$c^9a<P7dUb,8P@;rHcme?^[2MqLo<]*"8dda?7*H3i]Kh>?mVhgSiE/q-TKpFhJj[#7NeQm.P7$0DeQS2*O6]mNh28"Y?_,&$
%b%dLh:iW#A(Cfc#d=\R@TEqLO">NUcUQj%GHBVfuWi7`Ef\*FF$^F6h)`NP+k\lTM$)7=A$b`H)N;d"Ml:qR2nAN.dH'^.*dR^*h
%gt'-pLi/+J%4$(`qk+8s.JCM%SKl:c8K"o"W?L>cT9ql$O)WXXbu1[\:eGfsTF"Jh#V"$'K@"/L]mE%o,+"4,0sTD5AY<#O^7sPb
%>9Oue]T</@d3bq^la^1pn-,<U5_du+G/3=1`fl>uJ.5l5O'KX:0]Le5V9$LV#nmAiUl^So0lh,X"=PnSYV2beNmQON=S%#"dtUNd
%Tk#;%:95=QcqaVK*B#W;e(.'!#$X[%0jOUS^sh/Drmm6E$#NC2<\Ttn#(L;U7ho?SLd_RXdO#gOB'18*<)*f`#H8LcUk33f"LX?3
%Jof\%&.O\q7$&D'bSoh)_e16e)NE&n7&EV:ho?upY+@b6K:[ip"F@/4Y4QfO-?!/sm`F<3k]c!(d`\O47urpORYSEg7X-6RUt</U
%1G<\G6Fs="UR4\oW^bSUBq4ldSP+*!WH?qGHlsDkAV"gh3,BU-=)gGXL^/6ZY'*;%IBc2fW'-#:6;9WL0D;gr>g@`C"3Q%M5NU&X
%a53G_\:dhH>Y#u5)Z,Jd=522TUSC1oP2`?UGo>&S/`;k#8l\5'Ft\'<d$OKn1i!5W9.3.4C<5!n&[`s%2`Xst=%5"GA\hm]o1X5n
%2jtV/$2IC@@Q?'<Q"cq[)C\q/6Gb;>ASY7444(PbGRW:I;Z$prIY+7^jNTMB8_[Qn_la#D!I5..l+Y+B60H[ihm32jd`CW-;i'q6
%lh-meKcC%KhFRt"m18O"rKrf@Hf?h,3OO2EnC]mjc^&8lW5mHV+NN,D56[m:NiF[<AF:e$)>/^'IMp:J,PD]518?mMBZGq4[`ee:
%N;\G!&YY4+Tgg"/C;+JDoHf_nnLaLS8BQ)IZ6ee<Iuco-@oI-V(6QHY,.uMO)Ns>`3-^?CH,e&(:Eka0JWIf]P[ZI)R1f^=.0'qg
%&JG9BX<%L`?jJ7$\h<'8r_naq(`Fg3lqhj:D*O_n>P-ETL-."lJWg0H.AB]3PbOCLbkhupa]>SOnpD9?>!(N"<;T6m"otqq^R&8n
%Gjl1&WLs<uecYIR5=o`;,Z1?<EVR\PEtc#g2n'BqBPRq/Ob@.'0+Ng3>H=h'N)0*Hi*_m+Gg?1%9oSAWTg(qlkBrPo<d`TQBhtLM
%`Uq&/XA7)2TcCj%OOD73&q^NYcr2\%$Egf3L]#KU@M2>sj<+Rq;GY<H[7[&'QG*fMZF<f`OoAQ,,Hu`*ZM/de#Y$s?`cc9Z5lBDI
%ihc/s?GR7;,p.UC4qJa3IS]Y_7-Qnm^\Ek.'5-XC811CHH.*k?SJ<7:m.V`.@u)4Shcp>-rF'DBh$<6o@-r8p(,F6/K@0IQaq:N0
%+e!4ae%.spPUkG&p_m9Qk,?r19rW.#+ROk;RsP@39pck?ViddZn9HPY1oeS`Vo!O5enM/2%mC3]`*kG(52N]7Oh!3Z+5MtU]19j7
%:Q">ko;$W>bgCFJJ51T2XCDQV0b:6eaE%YN!T?2BilIF5Sp3>H47;YUU1<`TC1/n4jRhu6Jn8VL<Co.G,Smp)jmGN>T=WVW$1F!(
%q+-3';"of$E\=h.;Vb2j"S2nFcYM^f*OC[NK?Jhte.QC@EefkD#Jdl9.978=YcQXXS-@QuJ1,aW@A9`W.0Q$-e1OSYj!s$'5$).I
%6im@#<^u:aTXr?m&9YtcD[g`;gHW(Ns*3(k0D>_'jU]u8MB"Sm@M)>r29d-d3a$QuCU1)[kU;d'@qM^_VTa-tEK<d5W.uUj]@!6_
%BY>on@9K&PUrP)?pTd#dl)pNX>_T$G&rSmIV4[N=/oO1WO:7(aNGp#,/8n;idk,?f>jc%5[J.:[[n7g,$gkL;89gu4aAdhU>A&p7
%*-`0u;M(.o\'UB]??YP0&6U'7P&t-$d)/a8D#Q"Wl2A!LS8`6rj3ppk:H!m7i$+mYe1ol7RHYYm:!N(*,$Upae9nS++QSus'=jfZ
%DuR>so!)6D,pAu\Z#bZ&<Ye:Uf_hXLpV#<dX3PtVKrIT.&0]Y;=K"X`dGDE:^A\#K;@S5n;d!OmW7+)qAXrPi4]Gl5hBO@X4P5nR
%%?2&U3.Q(ZP9\!rS.M4^?`Q%>B-eUc%*Bo+^\J6,Vq@T4>IW,p"&nSk>A]]=ANIB#0_XVkg9$(%&nYs`R)[S7U_(]nD3?p<B.,^r
%2NK/3[d)Oo5';5nl_nOGVS"$(L(YHH4sO*'M8_0#nP[Qs>^m.3'Y#:KejNIp>\i_QJQ.'i4L87QD(.)WmYH.lPEO7/'e2DsQr?Ec
%!8P67a<SN,`Cn[l]`d0Q>uD:J9;"4e2E/c]Dm?PdE*@aKOc.Fbfi^Ll/!ti!?YSc_*L\tXc_n[2CnukKTfk/7:6(>SE8H?=&HWGS
%Cn'_R\M&05D?]8kodQlQ/&d<n@c<E^_.k.RA,#WU,<G8R[?TjNnSTAC>gL@lgr/77g5Ws,pdi*u(A/&/$\)nDh6Z/,B]gDoG?;qZ
%rZTmU\RhoSW5$8oR*su&kDi;S_ORJBP"Jua?,Sc\T%`)hd%mB4IRNT-Q:*H]c)/e?!$8hFO0<#e.FQG`)+Qa\1og^_-Z2rYE'q#m
%WH_rT[kdT,C5o09lt&Us;,YNGY*G3=q99MIf*-Ll!?lkd^jb6>)3;0ODN?On%`nAbRR3Q_AU#)PF[fV"j#D:Xg[6J"Nt</F9;pOH
%ORFqokj%gkCt+GKVk+WC8mD67Tr9IH5n14tA>FelN4"[jZT>6rN>]JNJ]*bZpD_GZ&iQY"jWem3^N(AO2O#oSNChIo_rKl<J"GXW
%?gX]]>L)g0/_M2?mlpHd&)Y#d=J52p.\\qZI'9>@7rhkaf?ak&S<(YAZu0#QM8_Sl1t)'rmd^tNVpI\G^1<!DJ*``V7QQlB\Q)OB
%l*X^!$?e7>s-fEY0Ma*gXqU!=:s3#*RHdIS-5U^%]76=laR%)6b)UsiWp`S!WAq]7%]6AB5"?HfL,fJmXHpnY(]#6!MZ6\cQSR)W
%O#jZ#;kA`H.auQuJ9BTU-mCpf$,!@UJ4*MQTm:>1)s/W]=$eT'm$B_-n[pJXRd1s;[UV]D(`bW'.'VE(oQ6I9RE=qN`o9)N==UsX
%U+3us#75c$$s*Z?dQpRcTV0@S6q&6cIgM(1.P2#g&e)<":t:<!J!eY+@Nn4#h=V/o"RGIBX\ke(o.;U1\Wo66N)`NWNluS)gaW23
%h\Y@H>7(+O;FRD)MbnPOBg<T[bT^7K75414-6?R5V8hd1A;+$b5kTZjYD:@L,q@a\gLnbQX#L3NlHe!cM#-il+VjpZ/4SS)W[\Q6
%:-[:uGJF2WVJ#ir3knEk,dG/85=ka`Bj]j,bK%XfoQ$&k/3tLSq[KSV@8jC6);VJ(Zs0MQ7L8b0It%kZC2spA;f?PD]p;hU38=-S
%)CSrsI[[V'dj,PDhf4sL0Im4_KBI&8dhY+:dukhZA7o'CgS7tBWu\[+=&kNmkZTm/T,g5`qsObe78]YFY#HH8F'ZVq>qf031IMH]
%.#Io@d^9GS@b&EBgOlC*dUCnWq.Ia2\dHui]j!i%GB?"NP,0e'99i1*_6or?`_iGT3Z-Pkl(d5BALK<@ee3`KUG>^N=>isU4UjJt
%p0EPim3I;L,,gGf0Eu2\X.C^$Wc9Mb/-2,n<Y.eP=$gC)RccF/PCQAM1D"`='Gq&oO\)C3V:4*'hBF,n_&IjlqZsqs[/\_gHmB\;
%2oMtJgb"8t/u]`pJ"mu.N59C!btMOG;<fJ"Op=0tT3MFU*Mo14Y7gB,BmLEZ"i*%H-:UE<$Xk%__Gfg1(_iDeM/'TF*P7:"%mbl,
%[38P#h-P/6T9$9qg]D.-&t8C,$g^C'+dgnJ_L_H.RE@?4WbnAY&_8Df3FY(>@C5$QG]%Nk<mjH[/J#)AhF>*_>@:^q^k>QJoIr2n
%g!VkN&XGFFBGW'!`[OO9!^1eWHS&OJ_E=EC<)4)\bC#ZUg=3*4.'PCs0WN$C0,AE%Ye0G%,\-Uu2,d#LdGbJ._bT4ug,V8IRnkoR
%.t/u[+'u">^_X?].$p&Ee)*pQaBAG.>.*DS46VRB/=-9a8=!?T*S0n=<)Z]"s.Y3T7TODXA@fg]l1'=O4c'X]D^hQlM8K]o;%/V!
%IPaN&h7B#+9nM(.,mV6K(P3It"A+3$h0oAhd;Y%=dYAn<n\:WX-(ma;32QOhMYR&!b9$82ShZgDo9d=hXFQCnJ0NsVTUMP+/M3Ib
%4HB$G4;>)pN`?J''UB<$@D)U0g8!g:e&Mg3/`1#jY$N!99goO6)aANHaJR855Fu?ab9nFZcGm?qOZR>lTE*#R"j/],\rb0,nT&h_
%k3IYK&Am5K!$=IKo?X/BfSbP2><kt'<CB5kBI^[8#Y8cpbQ0]$%G>n_5*5$l)!Ubt#f>LA\(h`]c$nXFM')iiiCNbF+%g;0KOO7u
%r7f0q6.(_)o\clNdm-&&8ul?Tg%Ou`h&<`!:5,/T49@p.G`^MiN]W2\VsMn2\Q-L+e<YpYg_\1?g]L^3QEYe$PT07mW%e?3ST].@
%(DcuqD1Wk16;Yni-_pR.&Gmiq([Q<'JB)Yj6+J/"cCP:r/JhmeWf/#&K,FDt?X)IF%jkKI'&f9c:0em:BgI6,QR7;i,Bps9@8q:S
%:dC>GWD9#JC!gFR,7("Po9GUID8hJTdDpi*U+k!u;7HD9KGeMhjp]SJ,$n^ncm(@RCD0pVh_/X;&J"VZrIlAKAg%(R@`\6]m5r:M
%Jhf*hFJRTUC[F`8)b]^ur!=KCj;m=o2,CTh&.HM;iJ9piRm?BFiMd.<Y>LAd&?=V'D$K?$Qh44YYn>)6.0*4<!L3j`,i4-K)qC"#
%EtDn>G--Ob&=$H)mS'KrN43n"S59g80ln7]!P%"6'JZG#atr0+b!Wd1Pu\Xr7%-;g4/,>H[up?Mg3'khc8cZ&4i.Z**nm57rR\9[
%_iaZ!r>:i>i#uhF,lG27#W'<=$H2;?3-!dNeVDX[AFr:rU\YM9F#O'mch2e+hRNH&=8`#@:tX)4l!e(3AfGGJVISp("G<]@>F=i.
%<po!IELif#8u72Rj`L*A,/:OEFF1`>;5-iX/)D,(EJY0pobRg%?be?f^?IADloog`JAhl7rBpfuc?L+"`SMhLqCm?=ZZ%7+c`f"#
%.VaKF!;C!\k)"JE2jrj2B(Qo`f;4E?N9Ed`#lB?9qqg>FHbL_V_Q`R>0To8LIu8q0%<+rs@dG,@oaMfid\W^7bedVs>EV9/&f/=r
%3jfQ6Fq+6TU:l\*gK'8[N;Z.,"j$Cs`-,c$36sC"Cm1mY$)"?[j8!h"G)('-[N"/c*GOVq'IB_.;".JGb%!Q6kd%A?-9jn2:cP1"
%Gsh\H6h&:$W*T@?0EO,+Nh#)cg1*62164DA7n'EG.qCKG&$%gROhRoG>htr>j&2i)[^`"g!a<cp`1a]=B0]QU[QT=eM.+6XEcWFt
%,5oMDCp_>*#L0":f3=2Q:cGWj*R\?Iaab'2I*C`Q[PpqJbmm;Gj`uu.B4ef_'0WK[l:Ce>TX8$Q\ZK!l5gq[>>u!HRa&m?4*Z:Af
%ed/AD`>sG!\"W99XcAA=R'J<-:be4pDEPdPC9!F!e+F2!fSa!d@B;p%ZL1,&9\d#,isr.?M"Imb,SaInZ+XnTed*j2e<8_KZ(mH,
%E*38'oa%7I#[)!3b"gZaDYK=E>/*I,O0%*k1hE=d4^'_ED.sWS\1kNrGO.n/dB>76:'P.LMb%>lHrap.#HS!aMS8A;/_+rQ08;ag
%\oabGZpe=<&89tE]B"uWL_lPo?<-N(U^RpgZMH/\PXHi"TO&"F!6/;EI/C("lO!D)%Z6kI\]=Q0Xpa$Hk-1>Y<bHeM"ufjqFkSeZ
%bI1M[3eibs)*P_9iW+g]_2[)%jTb#nWZn\mNDpPN_iF-@<SAIRf)u-&34tF,6(^%SpWadR])k/Y\.0'j,:[%79l`)]=Qeb2;*Wo'
%dV,$>l?ClaJM]P<me-YI51!cqm]iFUQ"/P$J`GR1\0YQGH7oUkfU%4mRfE=#nIFr$p6CbZ3BY+\qjNK8/AD8(XrP8kXQHVBY@0A'
%WOgI@-R/p<B<9)=ZCJ,O4lp0sSII\JOkSB%qQF3J%+'dEn&Gc$_IrZe/gPnt$;is]/lD1bVljfF/%Nm?LcMKe*nr"5I3*%QC)7M.
%d35G2%MbZ6Se]&-?)psR\"g@G:T.:S5%-37"eaR6cKl1/--;KppDqd)lOo&<[]8Valu-Pt!;^AMKkW-?'qMN>JahLuR*9$In7JFL
%NBcPEMX<C)6$KUbGF^s6eE+0NA,-NYcH$)%`]H"5Rar)7YJ;iA`lI2,jY&((kW'X1&Vm-$09+>P3T?fR$\8DpKEV@"mc>]>mLdM:
%XR[)=9MHtn4lauK53./0h9Ao[*Iia=7-#/k(b+@,$7/d:#;l[O06G@RkH2'K'>"PBRa8SmolUIs`bf[mQKg!6"m\.7QlDo"bMP6R
%bdt=-&2Oif+@+oMMF2K690C*9TL\-MIK'i?6=lWmMpJ6*A'FPI"L.Ll)IA!GnodWpYE1@J?@f/[r+8G#N73fngZ0K_Dq77t;G\B[
%>A`NRh6E8r*GWice0%!B@g'qF-dW.K;^Ze*nY/U0)ijE4icb/HU`^mtqK*5cIHn@\@/pa5F[,VK$#tp!n(\K!kDYX#0VQ)m(QfpY
%Zl8F'H]CoHaZlOS`"1fP5EMg2XrpmL$[4XH2DtHdkt3EGn3/\pE3@:-B[g1dEj<!fIntq>/m]nUiBdn.pC%bn#FluA+<@F"\oO8d
%^A&D8;H<HBU57R,6]8RD)+(c[bD<0(7PTbj6>44[i+"s6:je5S#D3g\;FE4K@@I:WU`af)1^jD9rH:5"Lm]<.PjK\ag2.,";V)U8
%HR+RgNBQgKOF:+u2[nGs_Pcp7LaR$nfN+M:L^0t;[lFGT'-]r(;p\(>"JX,V@PsdC#r.47^oLhgR#u-EZRTuo`jAE=.+7fEa+Y<h
%NOhL97]9o_6"M3ud2Ymp[[QHIi]t@d?7^oJJZ3me&:V%^CJBLMoIUD8qT!"DQQM[[LBrB]A*!bebARMOhgU:jB&_X;^%FDZ7:c>D
%KgFTMn:MJ3/AC/J1)u?p1k`_hmDt[uj-H:9Y+-*k%<^Cl;,XOeg8e3HlLt.2O>bFW(LK1@;Z[mq!`U''1@GFJp+V)ZV<RmsSOZt^
%>Ub0SoN7N71!'Ymfn1EAL@ceR1RRhrMd$D9Q)G6>8E<p(<SZ66e$XO]h%VAe!id(d0r%nVI"t7[5FVis7,b*V":=,eH+M.EG?%hd
%\mrU`e.r"TOj8!dD*(gN6q3`m&jPOh+(-+'6a3kGqr\/*8)SMpANUW7]SG>%2tY(],JqQ$UFGaST\3D%RpVSJ[W)YlI&!`m`Z_;L
%&_$M:64a&;<JpJ4Y0b2^^h%&>PYflfo[X+C-;iu28cnOJ6g=QLhQ@`+AP40=(5\hVW\Ot57m;r,Bj8%6<#.M>HF6j<@n6RrW\[mH
%mtrRJ.R0j1]E$FU\3+UR5Mm^cdZ?th8O'G$Ub6Kgg6`gV2NLodTt`Bh#FrQqRF0b1/N]VI8+\L]W<G>p\@k"-dB<Os.m6/TX@CVt
%41jj!a..98//::tYL%WV0\-$:V(ec!XD+,%!d5F3lZK0T0#173\KSmO:316MH'LO7+2u`UL.a<)5BAWW83*@VJ>K*L-j=XM%baZW
%/jWP6<-=Fdea/"q/6nM8+(J6GJ<RcPJrFmm'tn4p.iY:XC>d:c&H<TpImtR3o5R7)&]\5NP"BNqg:L0nkf&p9qT"9\4@Ze-l4N5*
%$8+(h]*qK4C,%W5*`7V'>`,Z0bWa0/QYeU;M)5mo5q'rlGlY([8'U+-O0gkr7`[9IN?`WmjV)KDRK.D(nnH3Y\(T4Y!H,CNeU][H
%B3=>cNgY[YU/KX+h;U4AK$7mjL<AM,k9QjP3ErLb/*cGoemjVP@.$M10QVao;)-0qf=LO^bpqBl:U%)6^MYdJQcqnY?7@,s)Ge@3
%'nHYVlhiJa)ei7TQUZ.S@&29.mou*HC2h/9=JYm(J+PYtc^U`GBdB'&f,,/^>Y^"J%516Ro<@_K0]IBo>&Q'f`BD(7d6Qm>ob&.*
%)q!YHb7jp,;6'[^I\3Z_BK_IkXMtRM2OQ'cfaFJ]F\2+onQp[V"V-[m%Gq,/7f.`(*TSF\Qece6W"5B@-:+(h$buk#EJ)OOY8O-(
%7PSA7a\*/Kj@0HJ3-=i<.=r#bLIumO`$@*i0D9\X)jdN604gJYUc=SKid9_8-7E%<:GmcXM2s"O4,=u\E5n<UjX"os8qdN9!i-Gj
%1[BC&T=TEp%$_oZC,_[`aFI#]"8sMf=RXMBlEHOpVeIE:MT4@u)kFK`p=0-5LGWe.em6b9_hHfeHd=*2i9N.09d<YI4i5QgUFmAE
%XDk"7J^r(cR]h=t[[Ybf#ALj6<B\5?2&,]1@N6a6Gh?cm4[$9Xa]dSS+8pTPDOTQ\Oc_f&Ec#u9kud[M>\c@8\9+roor(697e+;+
%HCgo*ZNBP5)&/J&QO3Rm96aoMbsoM/2-'9f@MV6gMnh_@$Y?LiXlsG&c9r!n2sgf$m6j"2PY]@r.5`@scA.a-&UKsFTCV&tY:.r2
%"7HGj)BJ<fWRZ1F80,K<"%_LB/o^UHL[bI[h="%@*XOt6q-_sh9k@E0euDOOq(T\f^,sdfcQbkhnk;7e^\A3RksZH$fOl$h\0\R-
%Z=d<nGb?Ie,)UYPlSr8qJ`5XOn&Y=/PeSD%,,g5^&2U!iY5jVF`[3g;XuS`lAsb<>O>II;,'0+CosFIqS1j6a5;6+QZWQ[0YAlT$
%5DJt[AQML-#(<+u4l)7\>s*6)$pmiGbkr=9GL>T7XXqG7l<pq9C?/69f!CO*F0.$/TBN(Ze0,UZY09?)[o^LlU5I04rYZ\5[0\r\
%eu!#ES*OIJo"iq3Yf)unm=J&QN%q0;o;o,-?\WBj>5,N47eaJ)8^$;oZGu3ij.nQo,*hKY86RMm+LXhDql?E\X&3ds9@aM;Z#<gi
%_?`l$a+t:le`P7JoUu;I(c&e#)*G0tY>hdVbc$%3]4)Lrh<2eqkonI]d67'q)-n+7EsA-?N(>0HMN_1!OITdf<&^ZBA!.QCfBnno
%L";<"AqNE@$#)A61=dFA]Au)`/dSf8:M0;s3>%`Ta:M1ahItX2NA=tG*O'#R`gmfj:`iLoTI7?M<FG4<,qf5^e;TV:+6+!P8JrW#
%d6NC/&0AtY@n$rR'EOt/a`m[e)#c!2FX5erdG$sPU<F%>G_p)Vc2S8a/Fi21;?RUM:7c6H4aNBs]7[FH`H%UIj96l\s.Je5ZUC^a
%@_`Pmh*HFO\TfAKlaBI%ONpk)H6(TK6m^l7`g^%g>tS8:V6ZTdpf'^07"C<soAns4FcB5>PsI`q(CabY@kca]QgK"DJg">FKYT:I
%mBM5\+e00m[*q#fp6j>r*?4f.9^#Pp@osgD3a@_L1'-0]n2uZ9nYmpQq5h4DZuOH1(b5#V$@hTbE!6KfVZYSLesO"$MU@%DC7Ze!
%SsCUgal[WR73Id`5\jCTko+^"0IgJ,fC"q-*=rbcD&caMKAH7+hrsJgd8Rn]4JQ\<9.[Y`D@jL&Ed:R'\^7<SZV<O+fu'ndRV'mi
%p%;aE<^+?J+UoV!M+-RDrF"Q`=-04?b7UNriMn+\29TL*-tKtJ!X!J>g8#dQU#Hc8\JYGloIK$5"==emFLuR60G9bO=ObD8;!%-'
%_kRM6].):9NLK&'946&W#nP@B1<I&^m!G/%`%!'@N92<B)4VFXlOjT#3,ptc@[Npb5F?f'T5_)Eh^WQ,]S6G6i\VtXH7,34nKYD#
%T6[k>L*bK[>oOV!C,8BsIIHZE<-'tZ)-YU6WlXQQ@k^QW)`&Pp^@Racoh?K%CJ/Q^Hci@iSFf'31j@%+AD+Vl@(b$^P!$X2KVId4
%%6]oH+H0P#cHp:W_sX;PC,"D,[4/FR!;=ud13G@g/;6/2&b.tALHquLp2R>'l-*ZY]Thr/>0A%'qqehKP.=/%Eo/rfcg#]k9pID;
%H!X/lO\YjG/nYE$oeC<+O'"ZccbCahD,Ckj$@G[HOT:Qf%G<1j0k>2T;Bg:obmV07O3HXilVs><P'k%(B$V`.*!*/[qaY1dg<m^r
%mmnXf:]]R4CIWBH%^saR:]eb#N(YT+SlEiH+ti)1i9\8J=-:D[,1Za%b?.Mo4kE*0OgcdG80mOr:E`828qJ]mlp&Z$;2o(HO]RN@
%96<V;B`tdN2OPpk#;EDpYD'(6aMQ3m"*>Z:o:Jfk]oHefq6u6k.?Ird"kb:4@G>j7kMLJ(cZ\,4N@ZU:?iA1ciO8&Z&tYC60_+C!
%*7aNupuf'*UPTHk`\EopQUtP4*g29Cg)>eV"cQCp-Kl$U+U>`-pBogCn<`[%q7W#Kq"=t`BO&`Jkl,L4GN*p2-1@tJ[uJ&ZEha9V
%XWtGn._<`#eC6I)CioAd>)6a>\.F=>qp7P.L8FXG4eYh/G<3'HCT3XE2`_!>R[t]_cp6R"3o/Ht15&MO'fh$c"\*iYQJ/kl(qXqu
%Z]7*g_:dX-,T`6F#&AcNn*Fp76rrE,'LZ-<]Jl/B3)!rH]tm[-;HHQuLe^d\%no&3W#E[tg.j7)>S-F5mDb"/>dKU^!kblj649C[
%ZQus_W-S@6FZTW-YE<':g<cmqqQTlG0C^[[h<,n)'!Ib!'J*'5EF;ik20eRQY;5"36kh+(8Z/\!";\k'gc;XR=>^dunXUbA7@^0=
%BM7$EnuX0k(ia6$cA*?%4\73DA\R;@/21,aiHujVfNKg.HHjXsJm:dhABI)/Gg9g)Aq`3PHQf,A?6ds\$lq#FrIh@1dTL<`UCNQ4
%j];$pC]7g*_I8XhN'l]e#]"\>WbqOrS=!p+;hi$Lbr:P/']nmG2WKkMN&u1m)ssqK1K`]oO:5EOasD#naD$;ALc?XK]!Y9h.ujA=
%78(WHUi(,+^5=bm'ucTlf1N<6qqh8W5rCgG'CnR>]e/bjD)nG"o]W(Lf[\nlO1krt`o7"lCL<#BVJiW?lN5*)H4QWUD_t/=iGV;c
%nr&V7af;.8/YLT,Bab_mB-l9N:GAPM$Z<^.=?$)/)Y_P@VV(5\.-(_J;Ib)6ELV"OQH@SG8.skp4<3C_YXDJbmk0=k1QZYl)j9J.
%!LUKh6Rq6KJ:F<[%-)ncQ,puiPq5D!Ja$Kk%3@<W9,EN+1OWVhSf$W&FRrVD-iSDdRhY!iNfTYhTo]H8H(s3tl>]oq9#<7$k]Y=0
%&nliu-*8M8d"F5eJF(S/d\IHdRuaNN'H(N$AM!K[/YCSI50R3-f(8%)V.LP'N.Nk2P]LbnUeAuWZ<FpZ`nQjK8)X9QZ`ds^`3D:U
%j+WCKg;hTIIub>@UHu+66NDD+aN!T7VQ:FL3*`kGf_!OS4:V2r;D^muN2-HOTjFrjl`Tk2A-$o*RcR)GlF#+l/_R3<AZ2`QSb9mk
%Ar]PZn]4k3^>58)J+;9i/$fhn/MXFQ=pcY5oA'rYWt>Ti]MXP$m'bIH;L_`/LT"I=gS#\24]p+6*@B'n:h4@*AjPX5MW1V`p7ith
%,>seGqN^BZOd\R6e#aZSM3[BHdF$g;E;1=PnsB$Zl.8$iaK'JuE3f44H$_O#l9cm\2t50bj3Cl'NFqZD,GgQFFHG6?U(;kT<Jrb$
%2Ym<G'Jk("Y3cA]b/f[._`eTP_Pe.o,)J=35DUNPJJ[]?0Yt8QJHCX,a_7QXLKfU,?1RBtPNDekoCdSY?BB_POF6%U<Z2e]i?LhP
%!EQR;]t&U1V?K)]"d7(UVpC'4]!6J]WdJB431+Z<4Ea-D2K,Ntp8sN_,7skdD40J^rZ[0H1hm$K.0dY5\U/RH^/W9X?VK;':'IpV
%EX%G+)G0q*>YE'0/^;VW8ruMMgcBfcOVI8BCqG$caq`bd+X^[ESW]1RC?+s3.FAksiP_@qREfIh.4(WU`SmuuI+A7mIh"$&3\l6*
%CeTplQ"o"J==i&!][2TE_.)ON:(K&;WU[4t5@u3Kc8>5oF?7`>AKE%[L:*RpK&NM4r&b[+i=UmT&gPE@Zlr6JJ1HN]RW9TT4'bi9
%d(#tWT;"@d[CE9B<]J+&jFg+VHuC4n=JX`L'<^o!;#G"fc)Y'a?i):jT9HDQUj!.D'X;i5SI'=i/o-oAh;jL(iPl??kV[[7Y<rqM
%URCAh-Q%SYO[NAs$[eBfIe0^gL9KWO[E^7f*r2\KY)uNuKnHC\U6ZRoQR`7*_BBcE:DH+fP)hhg!d)h2)b:Fa*b]qdfhM`t<(FMH
%0]\]!;F?g2'0lu8h)i'166^.5\sXC%l>#pl3%t7Ofbg!-Z':9@!IKV['h!C)"!s,<EU0<iIanY0VV34^*9ZZ=SsFRDKk]^?,D2D-
%!/hZ,52Op#0aO%t$=SG&AWIXb,;WR3oN8)hHNq"o-W^Zi88<b=jat$3COY7T&UqYeDZmOZ&)D-i*'+[Y9X,lDeFt<c`g15<!<6s9
%(iDt*EEL2^cQ'<*+7NkeRsXB&CO1MJ;CW&uF?+4J\5t[<'K=MEO'GCP$pIo&[4Mr&eYiH&:na*Z,X3(/8-np]Z[MIe?I@8YU%a;*
%X?dB/U0p@6<mKYYe#rruFIB<4[V#2c5>r/2=/[kg2s)0$B+n7j=k3jh+`%A696'qc6CErg&6_h(O`@B8__oSOYt%&VQ)bVliZV3Z
%oDJ6K.4seX-6(^%L"E?mE.[S.0Tsb77Ws4&BZNDAGN2#Rq7<>QI:p=;DPSZIQ4JLC<1AS&03j`o,S[m(;$0rea?MrMY<>9-A(.34
%\G`"-fpOCd/*CRr3$s<Rh5u]^\]0jRrYu5O3>;)unR0c"%d1oL8:'2mr8VFB+90uiF!<bc%AT@_\N6H,!V<XXHJ&t+b#t?Yqk/Le
%W[<K4(I(uq1\j=\o+tu-d@XsA5n[l?Z-?QO0c(5:?F?j_$mo%CeYI8<B*nN./,(3oN^USUdOoTPhcTF!XY;;3BFuISafZnZWS2@u
%'nrU&54XPdZC\CKo2,FDqh:#A=UldgMN?,1^HU#co$*Bp2@n0!puP.FI-o27FS55Fl/4T,+!\8=NkRGahDstfR*FBfZlSK2[&B7,
%F\D.3V@=Gd5i=JffoS:A1usSeTuD;cVU'S2kPC!4W>sjZA8PpNB.?Y<KNdT0AKHEcD_N":6Y:$X.nIqe4^,,>qa2JiPl)S,C_T<J
%*d@OFJRi*7>@".C@!1)c%b:kmeup%k1+%[$Q`TnjaG>$%b983i`LpNG1kiOSJ<ZQ`/f+&fqn6BAeK(kf=2Wd[)'O:TeQlf,@VA7Z
%7SeC5b8<9t<cieps0!"3L5Wfn!3k$]9+);ciXrU3[3VC[M&Yo.p<h@b3&@if$sZBA9:d'Jj&+d#DIf66$CL1OV@+8E3!@s&Amp@-
%AhNR%O\_g[7DO\mHbFG9>6kpNeVH&_ZH3'sn>=oKk_AqO_O\m&cedONH=h(INTXeI'sJrWq2:Y@5@T_WeKL+i.O[.$0eFQEW!q^#
%(<VJg8bV,gA+^hZ-K+GmAVTc!8[s<+8L]rLBj7!dHbC?ff;*@]Vn4ZMW$M$E^^o/gm"qn\BQ@m/+G;KSBj-jdN[aXmp!9?0h2BCT
%WIC]i8M=QB<C\OUNbZCOS6mR>HFF&1EM!6W!bt7u4l-?YUI!folpbK1-^uoT*a8b?A@gCo2'cn2r%p]:/!#?uD=n=m:i]D`*fdqq
%VQUW7_oSZ'W3&aRDPpR7X5oM#Q*c4R'MoT@3>$o'o20-\j4@<Ol/!]'_,5-b?^>h]7)7#\E!2=&aZEG6hG#6](`F+k?6tD'O-n/3
%c_:ILd2g3ahVtJWe[V7(mPfB)N#HF+,.nP^>i#%9N+L+&DGB1+K><VQel'lV=4'JJCJF%(]IH6k$ZJ@sfT"DB=nEkf9CG$nQ0d.\
%kqlqGD@>Jkk4`(j3L71C*]pbrD,cQ7f'l8kn+%X:r88G6!$0>Edm\GT%+!SS^AbVsWS-([kflMT1h;-3QcpSAnm8SUWS4gL<f2A8
%MXt[&lt(/DHqnr._k7>icH8m,4l]]i%gU4=dRes:Y$_r&`"'nSm./'E<CICR;CYHkp)hIr7Z'bh5.J)G,hHt?lIiiX\0_N1^;Ncq
%_?ccs`[+b?rM@_Ti,BY0\gLb-D:/ejG./n0X8HU/ed6e"3sfim_(CQBONqt5TH-@`a,E>!c?H*8GT`RH^2X#&CV[rj<JY=9lYasc
%(4MOm1+O:t;a%q`(">cY)\:!8:MO!Ss!>+?d,oI_IMjU,2nodI8%oY*"X]uTA?P^gF:(/eWsRcSs.#TH5-$uIdsTe(3N$\:<J*,u
%=LUUA?<Hp'POjepEk:B1H57"m>+I.H,2Sr\(h3HQo`lP10^c!q6h1^Gq,D]GYGRPcD$3^0UC%`cM99fnG-Hf*:jIn3M=1Kt3m?+(
%0)6(/,gN4hh5Jaod(]g809_dI[L;nd%f!2BS=4gT#9pb]0*&D?Q4YuW!3,Ad]/l:)OFcu[#NZN5/OP#$c]c-d*f0_Ge6GdKc#p*t
%8W_=f?HYa+'jlmkkk82_)Nr_Vm<gEMF'Bhu6h5]ueg3A5.aUf-0EC`9'SiMWPbHkp\YO&eSd?cDZTn.Us)e3CQV5uMJnp^f36emU
%VH]F&HBuBIPqtQfKu%^bbI6llF]3X&Wt[Il'24[M<bK'd&8j(?=K^d><`gZ/@Fc+Is!?_\#iG:TeX)ZAq$bW\jp%n42AY``=]%$B
%;PT=<,[+.Bc%rQ("VjF:?.mMp-od;$h.D_2]?P,JPrF.7&-8W*.H34T0#a)]FF"J#ju12ij(E%geYHfJjiE8$l:b<gm#@oE/'1I/
%dRNA8YLET`DV@ejL--b=K%,+aZ*sQSaPU;6S^C^g__Mt#U&om8TgU$r[NdhRYB,]Pm<0aA^sLVdOC5F>3m$;gpR#b!D?UDofR#/`
%AOOT,-o_$qM!.7$Nhema.NQnk/asaM%8Ao%:(9(;W]!pXHDe%^LF)orMO9KMDr<aCM0]iM/.nZ<GGX'7au=@Y4Hj&\#;#"VpJHc)
%@@_XLddpF_N[%YaPZra39d2V9j^qL:!maBfFt*-ZI+FgBU;8S!ZLTpE\7B;>X3bq'7Y6s]+-"2^I#H\FLW;4?E)hbP<CHc0@INdQ
%(-BquZEF!T.eQcldM!Sc-nRMSq+A1u-CI@@Gm8YSGMj4^M$UO?]U/akYd?qQMUTYJ+K"mc:B^b_]V%k+FIa6;Jsc>P2`&g!)6'J$
%gZuleR7u.M0pE<HE,6cNZ4WZFgeIG9Yf*KIqoejSGXk$6g'gE'*-7I>NFWR'EeZMXj_NBW2R?!d&XSG6E_,%TC1?NC=0AdVHuTRP
%LqagI.^S_=jHF<P<gAY6M:t+&BZ9[u^:ed,Bi5ih!NYI1_:kms%;u,dJZ"r2$.ngi2@2iU*]L-4pV:2l#ds)N[gh^KSl[I9jf)IK
%NkhK7='W.$Fn-B^6YUs(Enq<&9\9UQFe,ZNo0b+QnNS\[Yu7cHgc2[oL7S@9m2S`<m(f-o'8_q<,HMd8isa*Y(ls-$DBpTed(nh6
%V^\U=]8NIEPtLCgIoCKVT=:U3(G8,Oe'r/:Fp_(9iqePNF4MY="\bZV(7-nB8(:^K:/IZU-0+qHra7%L$n$bORjj528lmA7Mk"4`
%LIQ!'8J5[$4J%dCh&c_ua:=nB'*g3SaC[#OZ4A3D[a?XlE->M!ilauqj$`G@0;3at&dtdbj=Cj4`\lJ90$dD4Q0ZKi0VRHXkLukL
%[IArb\fBg)lGs1Gb1trJ(:c?]JNV.kZ-nhCZL2i3G3H,Qb)XP_4Sa9G2US(Sae*V,73)$#\hR*:D"?$]EpO=[4_%3$-\Df^#-MR>
%Q/P3sD13bS!BI@AO?Y+Dr7O7u[(I>sW2+:6A-*G/7b)XZWc;f`_6Gg=r29lLFpM*7QU-CpQQ)[XP.F-+h[pt+q*Zm?eQ"Gk&-m#\
%&m?Lo9b<Gq+_i++XUsX%cc>WYFF'or%dA.*:iKE>=#IYlVAHa.jhFnH1\+pA=4s&-?54Hr)L/c+oj');*`uE/YW#<T)7?F+Z,)u2
%D_,8(IOdo2U+V*hAapMb,F,NJ'm*Ell:^SrF%Yee,_/%8O+Gei\-O2UZqR4=8E++sF`i0\FmU<TVi4&mq;Msg;6Qf?[51p&R8=[E
%F_#$eKZZW?Dt_C@NkrN)gaW:Vj8%Pc)Hst?L".V$147NN/4KM&A?mn<71k<h6tZ?n(T2%?:sDrPNo\d8hEqMDUu!uY$u9kSB1`:6
%UWqE`&V2F:/'c5MoR$a[2i&qI1uQ-9!B%b?@(+dJAQi#f+0r<X?U8U/'S:Bq3gLe`l[%AHHST2Dp/Y6ma(`?R(oi[R6Fs'C*,1JL
%i=Y]c311`#`-(W&&m<^bf#T'6J^LR7n6&a3q+f)u+"rIPIem.Gp9/uEor%iU^\jRNpM^*02h1jOs7u]lqd5tFo%3p4kPt8Ur87PM
%5Q9JgqY,L:fDk;(s8(pYnE7Raq(Md:TE"W2s7,"Hc/8Jt?[ht>J,B,XrNca>5Q.X4pL!sg+#M!prUSRTs0$q8J,d1!hu8V-rUYNj
%TDmAjq"*D@l6f$njYrKTrPsEDIeteFFn7is#Q;.3T]+i-+9.8hgOCs:2u`>Sr9OCYD#Be&qX$NGol'lb5=%4#s*WmW?6Au)C$eL+
%aJo?!AHSd:_>RTuH;`S19P\c,^WCA$UCK_CN.PqjUel-^khZr2"Y:haCZM73CTO:i@a*\aMXejC3W(tt/o]`!US8a<*c0:;gH+!?
%2W;;j%jkE@UNk5C)H&VIan_[TW0q=KS'`gbPiU#I+qhrM"@gug/Z5%U5U15nKQ7TC]XuVD61f%58>%,eN5O=>p]FT,SZ"=l7]@"H
%bsqm8cQjB9=]Jrp&4W[=,@Z#4jf>I:0*s]o8/iQ/c%tX!0*m9Uk\a*PgJk]LqS:U;]]\s\fE+X3imecFnUY>g'R<D'"$Mlj"&M#"
%j5A`*pTOF0-)dhA`LRm3<dtnAN0K57FQFe(LlL@a7Z=S%_$Gh&)Bsc0EOI,uftn4C(eAf+$GBkoYm<8l'dnI#+Vdo%lJVhZ,:h&)
%BX_M]"FPsjZBZo3,2-I(!Q,#X[C136CtJ*,Ns?L'UPhVE^;HS[:8T4ae>b\i:s?.2?9()edDHe%F-k7X7pZgYT\VdCN,*5o03`F-
%F?5T=.No]=];cPr@Aq^^@Ngjm<;RPLg^]8SXM<m&T\keaX082,P^j^3O)l>FYcH346b$]g5V`ha00M5>%(-R)$![=D`JATK_G52i
%BGg&`H]Qhh(?8$OXW=8/)Y95qS4q'H)(E>`<HHBVKD"`D9e(TCnOIXP?1CKph6#f2CC:/7Y#-7*>bgJ\W.R5<mKd_--6rk_^;=qZ
%].F(Dlk7aLnLWY6dXGs0"DW,uc^_>b#]kLbXuPCaNgCY(/_(_u&je.7*\<?`^.n;rOC!O+S9*2ks.p^!E6_1;i<k.9cC47RI>T2%
%<!(pS)C>+?dFCN\2Nd@7*G!4,.-cG,"G-SV)=fZW#tcrVbge`hl4^o1,iS-[T:rtQ<)Z@=VWgF/"6n?@[lb!s'l2odU^D14AqVm*
%(H7:Pa/-;_6]b2#SPKDsqG$,ph5M%-_M'=74pRR[0Uc.OYf/NgaaRTZo,b2_Gdc$f79f>U6r^-)"#pFkO3E[CV2HrEeOe<rEIgu*
%=Km%,Ki?BL4]BhsN*3&:0b`s#-i,^el6AV<[*!Fa_qL`A6=M\s&hFUse7]&SWN$$4f=`nhiG%mrBrZR$;u(&ANN@UZ8+lM(&WZmP
%3M54!Z&u#Mp-H#KO*kd/*rj1Y5*q?in7/7eF+U?XEbrT6Tp1oBQ*?I#P]jrKahi2[a"Frb-X%X4gd1f_,,g3m68*)Bq[u<>r7!#k
%._)XX5Dr]o=`GSWD'tE^6rb1sOKWCG4/j/TQa>C(7s8u00^0cEJtBVe$lDARf\&*1Hn79V`$]dc.Q88eg(!mES<I1P9i.*XiI2#b
%$X.l>'E$:T`RV8fPXotobL!XG3J,+KiXc]]Sj#K,Db)Ud`Cr:5R$OScHYEi&7!qq;3)P],Qj'Br&_33[0u?^bPYk"M8u[RC_i(+c
%VP>M3Q=-Dr0?IGE5K4`J?,J-'8`TfpEHYETf]G47%CD`b?^-r/""YoMqRq&HkBahQ14\6],hr]_\ZF"$p^Ti#LP.S37tI"TR%7Fa
%[>Ael6b!3:@TR'"jlCDe<m/XS:SfNAXe<#Nhh!812;>;;JrO`SW;2bCS-N_!?f"c1Q&u3',gnLe&H:/n2t#oC!qe)!T>dR^5>`/,
%D%eS,%GcO<]U%=7[oK=h^_HO#\TC.lJ8J'Jgp_f/AKuguGP]Y[V:W=o+]@Y+2W+J!?RpS3EDRd+I`,.bT_WtVmELmGqt5*Z0f-:g
%C\6d%AY07;P.(]L-&D`s>Xq]mrj=UXqX\NSmQct\?r=MloQ?9;eVif_HBP2a`,oj^+4TGncNU,n:"97)A]<ZGp44GYo`0=N?bnEM
%,[CW]'M]5]f-=BqN-59AA7UijoNX^Jd+FAnpkQ%hW"I:WdkTW1@\@WOd&D20K?<unbS#(+(W^Jc$\4Ahjc;mnH"2M2]4i.MZHa&R
%Y\VG]\6mKFAlaH%11qHe4IucBIt!<bBVNbHE1a'EOUWDO5ONl*:LrtQ>nP0?V\j!6*Iehn*V=>?:LV4YI8'D8A&#ZNUF`0uI@O%(
%?4BEKZtM`;]cECJmYW,j#oGnX*iUm$H`j*=3Q5,J(5SYZEj4T-'mh=ER9]\9+%Mp!3m;?u64j`O&Z1UcP2N"t27aZM0T,?/p8)\a
%$r:g@n[L08.Cs38g$KK03c=8tK43MI1K]r<pK"'%P`mYFG[J`po47+fLRB+,a7C!+6X7`"6K)TeJV1<+1)8ebA!=OhjmsU*4,IVi
%<-?"P8i\u)N[kLc3(eh2rZFq6+*Rnc3cmfU<_:OPI)8QjjTo$8cY7jXGKD(969qA9,:"=r0f_*A?D\ne`Z^G8^&H3ZH.!,uhEekC
%'#[1Q`,\2/3r*:Zg:A!/`&#?4drbMQr&(0I=`[7*7LbJ9P'cH]/QO8=#4jX5_fQcme<A9"QhhC<E-4lP\%bAgT>uh)o$(D`&biG^
%K(PsOe#W!,E^9%>K.4sS:$q"L,$35smD@T3h`2mLQQhnk\o<7ejq3RkOs;9iSgalloA8*ZRaI<A<V!*L3><e"Q5\0P4VH^(cu%GL
%&!5BT0u4X/PsaEU6EEVJ;W);m"lM$H.8(IU=EsV3V5T?]">l?JbB@m.L-R.#K$e?;-eI1[]nP;gJb(hUfeO;cG.X5Abc3aQB?V6A
%`6M[LH/D='nT+(%,3#r`92Ak*HMZ`V;_O+Tb/5`fN(?9UAu#]p[Oio$P!kRDK9\Q;lq%g^fB]Yo1gi)I*cc<DPL.O[-i<!#8DRA?
%+#FNt%,2'"d80fLI#A$f&j:BsHDk-t7iOfi6I2TO-Pm:_>]/FC\-`8VXo84[59.T/=b$9c'_[TS5PX-bP!CnMk'f(Oj;h=Q6q9M6
%Ms\:75jB4W<MXq=d@,@9ouM`-0$W4?e3S\%X7QH?)jS`8\LdaC6:'h^mij@[02Z9r5$ZO\$3<_JB@qO2%eWLM&s=HqT2!R*9F\"o
%*bh\<Jn-+dQ1h2@8ch:0(5_%0JU41q(r0c2V'u#:=/QFcpLJp!?.n/#,G:'lSI!H6\@C$9?<jM>rj=$4PX[bW.^N*:]?N?+L<n$:
%$'3!Daac+1BZNLrr!kD$!@t-/B)Y0*Br_VS"tWm^E-62\&::&#hH'PqVP-=+o@<oA`*(:OmV%?)WT@5$46d2@HaePJ<Q'E8*sNQh
%@Sb?.,$7RfLZGFjG2hF/^J[HC>)*Q2?NQkdBSU4ifE#k)TrIl'e.YFk\s+D4*$dNHjEZ1BSL^<no=r$6!(.@\M?"L1gemMYdQSf;
%:HH59N6Il`qp-ib:R\5r]6=*4EcW\kITHsDr^).SbIR'tn9T7sIWL1e3XJR<`bGYVeiZfh.-on2e8:(Phm7(/mshfW%VA1DgL@W=
%I#18o%fkLd)+`$Oln8AcVi\)NHUrGW,#$fLQ*O[,aj8=MSo=bVq(:QF>K0!PP[`Zup%E\g<<H-'Ur^[s&YUO*<`)i$QnS]i#')kP
%kdM?1betuZ&]`H,b:bY%8F/Jj/@-H+9c7j$NrZglU/j*`.1R8o`=n,UYPG,1`HQ9#'uA@:5ug7gQ3KU\TA2,?Rk,H!c/oUqS$Eng
%QP#9O[tl2b,p_D\O;EHMTV:jZ]RmpuZi_HGX6f&rAW`'R>W4.MO&p/XF=IO%gUCOA*/C+*K^%5U%[qlp[,oV$>_#$mRBJS34eZKK
%)C6".nX&=D\@C34?V)TBXTebWS+2?2:7rC",(pkF.^3Yh?0h(p#[%i/p)LhRk9mOeBKfVbN+"r=nSVX^EL`,HMQnT@G;'B9Db:H[
%r)V@.qPgc[`h/tB-0-G4e.pMGD]-2g#=:Hho!ah(3!+_.SpA19hY@2)k#"\<)uWIq%0^Y$;@!(kG2Z(*@C=-B#:\?9frW.N"Pc?i
%%K7"5O@i?J)BG#k24\CJQ[eu+lP:#>2N%-hRF/Y%fkKlSm6[6;75KJ74g"[<bEP_$o'(QqX:hAQ6<T!<;tMibr<Gi"g;e_HhGN;C
%<hut2(hIenPK$JL#N^[+>pTVAkN!GNJWhbRZ<,m%Q)ZuAW`'RrYPM91=%$<%D!"]1"2@9OPu,4V9sMOGUj\p]=+Nff\OL!#e-F<6
%(-=$4O@?+,Wn+<]2VcrD./<)JTMhdVb[t;amE$iH_^"C*N>?gOF!5<J8q,=p%WgfDi6_4,M\PK)1rd?BYB`[BQ?N'JCk^YVmYL.>
%6WUG%(]A2'TNJX)"HRc$SCMdGJ@kIk((N&E[1=E0Wm][<]9BVM&rg&@8]h1L#t<R'(,mP_>I+]Qre&'?;;t<<HI+b'<:e+gf8tBT
%]b^M#;[3D`!j07/iA;E*BtS-G5tNZaU@!ERU3]"^Ubq`MIKi-J]bo>@^]0kVs+?Nud07sUUB^TOlOo>^/*J"q8>$A8>O%SoU&(@O
%Pq6Gho@5VnL;&`boII8,(L^-,p"3CaZF+'.&,%+@1.)qVa#WLeW$ePGR[gKSMsF2uH?ZB\QdCM#!/%2@6Zi4G,_+rNTE(68U,SHs
%3Zl_*.KIR`D^OY)hAMSY:(/7#.71pRBs5RrX<6sFK]ChI!Xg#egO;ci"ASVQ3$Vf(45?1j_g&>_D22oLK0a$#*d>1QRk8OFITBOk
%G2!FmZ/.!/cNI\_Rj3t%!0i$3ggh+rf?r*6[$3cL@Mtnk&k%fDC4Y#6L476L^U'jsFnLkrZ<Tndn[.R;M"V-]!t$JUQXG/;QOZ35
%R"/s#fP6+6AW\&ZgK1bP')MjEPdq'+1Y-/0$@n>_ek[S*O)Rp[?@):YggA!\-.6(A^WEd>R^/8KJ1J/roD=_1@(pW9ApPEea<"b'
%)M5:cm9cf]p(m]g7>S(K)l%'(WKRN#*a^bJds3"Scb7Db8tn,Qi]X\VkmEU2NaG9N'1N@]JfPSTEXAsQ?]2#L.hWkCh=,#XJE.tG
%*A:^T@86n4*/$+RVnlFF^`j.QKW3od9RF50UU-H'-p4FF'<0$6q>'0W_=\(K]IH79+$.@,ER@BFO=ndUW/@p0NXP]^LQmt]9X:sD
%0f!o+%s<uBh"<a=7?ThdO1?]g`t`aS4FRY`(`)db^:Zp^0-$L^i#6-)%'K,=..**hR1NY9akb9qBpmaL-ma]8Mgnin4tJ0/'.;Qh
%hVlPrS(XFYYNfagV*X[0Hl[53_g&JJQG&reqLN,Rh"_gL&U>c21tQ:)IAj$)-(Q)/\)CT5==1pU\4t/e5skl](hYP0WRWrPb<&ME
%(*$<%;`'Yt5rFW@[5H[9m@f/mK8[[%/5b:>7T3f[.WRq2&GV!R2llN?2(q`^>r=?G(gs2,m]Xd^R8?1"dpcD3[c4h]NT`-*ZjnjM
%K%LZk\8*.a>Mr\6oPG:!W/Wn@%Yk]jabN)R@/U0@m!`V_!10WmM&_^Om35?a'%FmT<Rk$d&A*UK.""f<F`nRFL,OmH8iqRdgeXp8
%acY,K4HG@`HA8E5_eF@RhL`>]gs`$ImK^&/WmG%GkUs4Nj''k$OIX5U_,`M;p"0WKp/*Q8V?fHi=`lr;jHY8He+O"_6l@)Eo-he[
%?rWYo$-d^$G0+LW)>f5u?Qog6dojipKG2A9ZU_1=^$-@PN9Y8l8arQlW0EMe,L6$fj2]>njjo]?^n2645pOGo/%'+G0%$'*"+LY;
%H\BbnWtI/'>E^e&s#gLLQ>\S1=>YlBo!h.rh-@3`SQ'?0\XE@(FmJU7_/dS;W>"2!,+,*;J="W[fh=SKp<chmQ4k>`-gmK_V&Z?i
%_n1dZ6+D'g(E;u^FSd,U!\(;kI[2:^a\_Cq\@Qh>$>#-P1_rZp92P*rlrQ&d6I_PchJ'Jk8HQXnKk5Qqa7/H[i[mq(U)`9Z;GA5%
%-?_XUD9oQdfs\_1+sobF:[/7EDdq7%NCRPa25cKI30L?'$i1j5k%'7O5hGL18,A]J*W3dB]3AZ6P=*XeeHuK/f]8s'6<D$XnMbc<
%ro_sZdGN'SBmu[SF@>l^oo+?RQ3RdJ\9-*&p*<IjWK,NU,`j82cjq_Encg-r#bbELWV]:&=E2R82f2ZV+D0Q9g;aK"n-2@@@0@I,
%@X1<t,*#53l&$[*@FaL7j;gS6mMgGEfVnF%8dih8m3B_tjp#/FA?>6>HOtZ&;T#XLHQ>3N&SW@CU"4iEf]p\62=cLBN3(M#7Aqq^
%i@n81,&R?b>>7,KIR!CJ=8<#M/u2gua_PXB>iAH<LT2%epA77T(3h_\*#Y7r@h/,NH*@TEGOU8>p1+kAhgb[<i:_p5fDcG<]((%&
%K?.rkdg4k^A@_P0R!Hd%*2Ie7h]!<@d#E.Ak'[%]3b4<E=eHg8GI]ZDa#U0XD]j'O:4$i?dF1X,_An+4J&%A%JeCQ8,A<T9ks#<k
%Ot/iIa9aUJjN%`h\jta*?c&I.#mNV[kU]/IGP*VQq6,q-;%FGP#.0XeXq_W!<!-srJH5t<26tH;\ITMkOjcO;B/Y;sbOb!aP0cg!
%7<sD,SQXrd3(J9tl;/+_\H6G`Um"N+%CHHSBL%/;g&nO0bD8>5TJo5k`(3g)\V\=i^*7Tgbd%j?\r'Z=^uS=iEB9C1BAm!@Wo\WH
%DJ\#L+ap$md)_.t\WQ[M,X0HGJfSdGo!jn29eNqp787_-lI&tA)Q/o>)%jl5dA%]jF/As:M2JWOZm%N9j:BX@j8(;\JjWX<4A5Y2
%fk]Do6],UV.;HM3Bd>7UA3\T(YkT'B4]W,)Se:1sR[?O."TgSBeaT^kZ';`k#R1ijJlW?A-ui$(TSkd#qL:qNl'E<G<l:+_!ui]E
%V04g3MfKhn95j>G@%-aI8rZQ1IEA^#]@Ff&)^"M[g3Y-eO#[RojL^4XrA$f`[7Vj(mW3]Rc9cl]_VTfj\h'n9%Gc6hNgAfjR?-@9
%@D7(lJa=;b%UsVik94=dSA,+iMl!\(aoe;mplhQ)RU2[9+I2/$(DInB]A9Oj8cHj0pN4A"OL]$%3'LN&ROs]CCe*$BS@RATKU2$?
%#g>HI?sZDYb%e`.9$enEKg3@]r>8OJ77,G.l4>9CLN!g6r[cUCd&YE6nEbK$Gr*bYRP8IVNf?03>KlgC;3%21]-ua+OJ:M3jHUc?
%BW*Zl83mSB_+#50pC\^[6Au_sU=pgL>;b4I#R\2L>pV.`BsJnC5gg9\Joq;3jbOtC,0j&/L<sr#rW`\K#m:b)llr<)(JC0G9)IS&
%-^WO-*b:7lHsY8$JWWjlPlH'1;1,es/SU-_`fKrGEN#HKQJ_tH7^$JAKmb&CQqEF)/eOe?7()'qiWb!s_@.EM*7Q%gZ2mX[d+n*j
%WT!:.oat(J\/$af0u/>Zg\f(#j>:s.g0nmCT?6u)HQHrP0pNFKH'n=FiWT0#6bgARW8Pj).Oj@uMF3.#+p.]&gWt*BD0<L*Tr9&o
%NL+;RBY!ZQC.ah/g.[_Z>$RoE7N-=I(?gr".[aL)02X]6,cJ)a`TmS)'dZ,So9Wf^R+VO.43+'i`("6N9X`;>@4`B-]hX,AHuq02
%,Se/G@iMb=Wad.t8mnP-ie'L=iX>\DZ1t.=i=ppU`?Pr]QU_A:T`n89CXLhXC<.f4FrWs$N>(o97MEgCe[m!<.%`e]qNMqbkJgGE
%7?bY\^P+[(F]8?k[!cAlaVR><EMAIL>.D*[L,''Si+BOtfq[O`XPK(7[=[=ehF-^j^te(_kTH)CI-JZ2mf]rjmi&:P1Q[q`+m,
%)l((c"`U@3qN)k7^/<*Y73c#Jo/!mV!u\Cmj/YE1d`V7Lb1(8kcaG<80CRFTIeEs1ad)6qa*<Co<f;u0D_CrMNa_@XL?PsHG;)8g
%+]NJ/q(M9FTjVX6\5V&8-Ip_M)u)<GT1e"Jk['$S2X/L1"56OtI1+UjLk";T0aD<f8=m$i+@U)RHs#2$j1LBQY9Jj*(AtR)`V6bC
%Gdq?m@VTOd@*t#g6)N>3JZH#hPXnMV?%j&K66N[7=ZnG"kAB#6&lgMgqiYt1"Am>3ik(VNWJ\:S'_f.ij[cJaFK7o\>U0VYI:,_%
%84"P:BG'#TK2(Q#@0=lDT)E`?6Z+V&]oO33iOA<mC(Y*0JqN6]G5;"_PD=r>XJ2P<71g"jKSgjrm;X,T=cs!gI`^%G+^/VIhS:[d
%X`mN8F;?&\.!7hK!;@kLTrCC#1OOOW)jH@mH\0)h9`;3!XqV&A3C60Z<XfO:<oBUN`uk)("DWr[7nqSI[I2#:+jr2[5UO1mH_O`!
%i98'j5Ia>M3"[9=]'X#[s#'257<C3ekQGTc'oS`JX[Y(SX!"N"AGqfKpf1R%eak/`'bV+\`dDWG^hMoI)9<C8;o=N1j\HW,((h"'
%I4'sBb<N0?nUESLUOO,E;*bW_&#(g'SbG@)b%Mour'1/soCq<9pODlXlaQn'iUlo3cl2q6(=E`$OM6qJA2.'j9@Vgij&HuCI=tOn
%]o!&_BM[^sC[d%pB;MQq@k8]'i:IlJbJL%,4h:_d6:dkd-Of;%6(K7FVTaXF./.a"McfhM:=U4=3_[I3FjLUQ&Z!03Pu4siR]qMu
%At:+1+]79=84"#k\me=LI@.flT'K!UR4q^A]-DB:*4B%$di#Fa8QL+"!#=AjP_&dFi!R6B*Io8=h9j5U:!-*&J>iRVHdDBtQ4rPV
%%bNrhGTM:EAN7I-n2_ne:8M;]JnKgV[%B3"$ZJT-nM9e#n/M>Sm]UFKYqLZAmQ4J")^24j?RBr2]Ga4(e/g_ANW=`+,SKE`W!t]\
%KZ(Ke)_dCr[.Fe*rBae)(@HA73Q"SFfD9SY(#a4o')g$AX`>sWD&Z"4\U'QFrb-dJL@FmC,Z\!G0QbM]/`p!)mk`fG*-)8>MtLV:
%W2(118-tAd",t3tT"9#b^V2bp;qrr&N6(!YFfLbCJ<afCVNHF$L4'L#\R10ndDgo&Vi<jKE2r--]rFJB;3CeiVo)J9!4t4p=Y8+X
%_ROB?d&V$0)@Vu_qWF+n^<(t$j'X8)m=b*.hllD_>l(ce5[S`Sq4o&e*a3i$M.ZC@#MD>5e+LH^Nf"sa.eFj97A<Yi(_i-hX-Uug
%DTFCq`sVHMLrgJ'D7LQgU]%80d=$MDf0PY9Pp4:6WC"2>EW#I!d8AbU-[d2a)(V`%buL6sN>4^1RQ4l7[E96?kq3j,1tAtXPUPE$
%dMVo0a^X2Tji)cl2;ZGjD`_-@gl%25R[FH^&)H$G(J]7M"&RD&C?&W=pWjU]mP1H2VaU:*E1%75'<-KlKR;hJJFYsD.Vhf\:MFXJ
%m@+?M[^$M[$pa7GhOXl"W!Yb3A\]/]6hp,nRtEi[=]qB[plZ])jG-hpIf+@Y3@g!Q*-J<#=^#/pD#"KBaCd\m7apk32IDfCQ;WCn
%&g,JiJ)&TRb6M7[[?C\HgsZc77`kaT)/10K3f%)hPUapccWSi%@d*q?X!A/o$FBC/G`Z7UV*Y4uJ$p:!/+^?uI7[k$hjB\=KO5_]
%LN2gqgZSpi_A%g<V.!nmZ<#Y]pK_@[IF+.i<YdncoRiO=^8./lQ8Fj14T`8=X`R4clQ(fT<k>)s=CsubZm^7''P$F`m4LAYl^_nD
%&T6R#&3tfSHabf0oks5=9UHYGc1$W@r]OgcRMIOdS/]0@m;7G9efc/CJ\df)q)M3fCptOl`8JBeqoBg(W8Y!D9)J2e^P9G2&rZqE
%"-8Os#2tYG+DLI0'LBsW`<`3Rk<$:b!C=eL9R/IK#K1%Ee;d;apATF3$dflBb1]k&$%hu.Zht'CSD$_I.:A[=;p3ppdIS(@q[=L[
%#.K)8rn.jp<]fZ,QE;O*5_BShr9p]YE>f=;`T/'W``;)<E:8RHf!*J1\Pf@k]erbBiOJuc9HqdS"?P3tG1tl#`O#8/G'@f'V0A](
%%HC;uP2t>SjD0%EMD?#"V3.[(%SBT]=>nK_9iX#^-<caQl`fL^pFG&#L4qBF!)nSLJ_9I]BRGgT8Tu:-h01ttlGMZ?+R4h)?H[@>
%^p3+h%'W]%60/A$9]OlH#1:mrXm:PMKPH4;h'e"j'.^Qo6=e(Wp8o'],T;0cKNF9#?BpPiB[KKZ,D)_0qWB9s:Q#7Z_-4kG<E%GY
%C82U\8-O<'8(_6_c`2p>lQ(8'^c99c_&?nCRq_>tLAceRd7F+]r1[?M>oZ"*3h2@FJU1FZ$%H/&n0'D]]Nij!@cJh7(48e11AUfF
%4+p#N4-pd"_q?C2U_&ZT9lI;,e;_:%-F.FZ>,PPuet[Jl:L(,+\b.j"bL",Dfr0?Y'Ia-s%B0msbj,j'Q&qZ$cU]B\afWp6]3_:l
%,J7*]+Ds5/L(4En^kAs`Mi,,P+)$)l,4T2?DObm@2CI)3!_(k&_r_s1UCs9de#)0+pq`gMei:RGI/E>H*I*l;#:3@XLaVQ*]F$rj
%Ri3\ad_egc/7$WTic"3LiPTJ,!Bbs58/e7kiZfj2LN2HSiC7HtK[J[;Mgm-ghfirSOp-Y8EQNLWm6Z44*g_C8Mm02^I!\91dW%?f
%!iZ4@6`j?;)XDGFUFm]`&1S#"&#i'5s/FP2fX_8nnO7DpFHh3T2+UrMd1]9/Lh7S5KflDN'7U<P92X5E>_FP?eBiV"_J<;`l%?mY
%*NhSo#JG\c@OP'*Vk:"*A5N\kk\0Laa4b8*@j7mjUdCIrMQ4ML"LEYjE/'7+Vcc(ikKOQ.(h`VWXSQ\JJba0PJdf#eN2q/D66/W\
%27=$:J?D;l[s3KV$$5aU9</ju_9QbrXG"$!b`2r[K5Cjs4%9VHYqZc@Bd&rLNGb]U*-Qbb._/L(@9;3bm&b%=+oYA%iJ&rC48.)b
%[A5/>6J2*VHJg"d3EWjqWU=TG95.:i&^@h$7rLO:7O@pA0j$<b[gBJLEN]EY-4^H3$$%kl6Q(prbRiD<786#Y:h!??CN6At@n0+A
%%`Klo,(GH?p>#0om*@>6%D0q)>T\(6@I%EmilFgJ0K*iK/u<LVcg0X)A#gq"0`7p.dZ'5+5CIaRs"g^@om`DC#=*P6&h6@8jE,k6
%L0>h[ph1ac&cT"EPEfVX2jBqOMc;Q47_u`WdYR;nM+a_Mo2rRrK'f*A#[ii1ob#7lO#.8134e?aJ@kHt4PacmqLBna64&)NaW&3g
%+a-(07H8KRiQkYIKI-Y`\.3_,5gHl2Q5YOSN_>1:/Daof38WRfBr"c\mZ"F+=2Am?/Of$<7l\>h!)1Y<-%SsU682#_9"t*<\1"os
%0m;ImVNM<LO!AYmP[Gkue<6G>@kBU%9ZSFml`(Q+pFQS/-L$t.<n8Lo/W%L32`!<!NH7L.%<hE^Oo+GSs*"2+&)T%='9k@c,[;bt
%3MFj+"%rE*bRHoR^@AAr=Sq6&h3`/2?Hd9GqIhgY!Jl2:bcR+qn]@#i_$i:W"FTZuG@mbfD@I:Cer1=TfF1ol(DoG11fAB=]l$`A
%#u=OuFfQ@(\XkBiNU,JB$(J<TY62/MY-gVcY+a=.lC\h3T<ZR+[/@X;g"_\`f\EVl7Vl@Ikc9B)GBj^>Pa:=gBR?<)r&:JrK(4\L
%K1KhQ'h5;AU>NH%1MB!:4[rM3Jl12hc6K0Hn0`[3cMZUWUJCMFFb:^ZrEj)Bb(Ub/.R?cor>$j$^q@?Z4]hbDAUO!bFco>&k7EU-
%AP/a7+_nE:l*3i=OEqOL@9f8,[9>k%FukU:,"!#.G!`40^?T'ckgAXn23NZJ3i[eUF^Xa=9h;rs2/`)>oS_5=]9GiL_Se>f+,:`I
%3mZhq`j`)3g2W@JCL-L9g)^(c>&8T%Du3J#Q)!<6C!Usm>Fd?qO3p]@N,r;)ZJVU9dXEb:g4@sW-3Ro$*pVlf[$NIErCT:T<M9MO
%]DTR!EKt!!d_C]uC8T#K27:0nA(>1D;<O16m8A'(/KWk2I_Bcf59!4lC_(']>n,>3iP#3!k]qZ9G;IR3lHK3t*?,;IZ]V3f)P<3O
%7R1J*[_3_R-6ET1i-hSU+q$\ORY9a<a2_`KC@8t%q"88%*Q:eDGn2Z_JHB2mXQ'K'L3-:=A[L15^9kT#6D>Lj>M&KKOKi:eQfW@=
%H]$-*Q-m9mH,+^'5k@Z=(7)M3DY'GB99f24;1_G6]ipVHL,p(+@J8%eYMp!?;\]Co0X%f3>Rd0iJkdF:M*(c[f7ejpjW(DBX4[?:
%3-:J]>Yq[VBDKT4(_F8cRQ3(Qg\a]>m^F$!,&lm0f\5'SYo:5O>-TXQgY32HJ*L=fc<bmeC]&:n^N]'=#:.tDBWV0mhuEE.r6_i5
%i3&2!=1R>fOV:H;+u8N8U1d`^4,'=Q">.DOF(^\I#RKF.aV;oQ2&eVfV)H]A<Hfue`@_EB*R;6^P4![oqq/i6-EbOg?kq?]G2BK%
%[<:p?^SK+RVt>enMk8YOF\1[sDUrS!nWCG9,UQ^FUbu4[/qSfn24`hohZ@jL/[:3I]UEkr:"1jlVm+hG?eCsr9PWV\#o]F8_*a&i
%MQIH`ThGiU\^Y+8>Lc^XlC(\E/^uApAf*e'S7"Q`;DSF/[jPgL=D%(+,jgr'B%$:FUL[OtYFW*!%a*[J=B3[]/@I8thuSG<Na1X+
%5aWg'CeG"F>4Q'\W;GLJGj^F`4p9`+%Kr/5AT)N*[;L`!dBrHs=PuT&@+N3eUT8'2T_)Q*`)Pfo-7'oV(*%ab`!oAueM>,glhB$a
%3*`4T6?g3C=FER]:S%lu8gAXsZ-FZHaI6\2Ypm+<^q"QhaphB;Ef!c)0RACCe>R6>TdA-9f:qS(X97+WZ)<KS#L]$K&g$EXVNAKQ
%KIoI2?fgEY'\>s>59X7c+oU8!8K[c>b0@h/o\5AbPM=-aa:l:/Nr7g1EbeUR3)Z%E"1Cro,h_JVRT7bJJWAcFB`6L:K0%?p!SI0D
%T525M9UueEX_<cZmu8]1<$Ph)7eJSY0K$C(_Nrq$k_Q\W$?ot("dck(o3n1+@Mj"R\i,jnGm'jL-@qV?ZB4iq.<'dS-rsAh"D<<r
%j<T\b6XRiSpq.0skUI0C3W(7'ip?O@HW.tHRTB6.e6XP/%;H]kmP&era++X]2#pjF=ig?Ce5(emZ@M(*cfBr'>2o2t,AI1*#(6d0
%pWU#1<lN63nge%!,[D[TEPj],@ES5$)oj.;]``nJa%7IubiM_2&O.$2KXo_].O4[+GlT\Q9g?Em56QFOMA<-rWBPo2GJ"iJSMKlM
%Uej[1Zu9o4MAV4G-jn&J[V!r5/WI,NU>u2gHuCJ>RVR0@)`)<a,3o$lYaqG7jql;DGXuS339Les1ZMS4=7Tp^_DR:=-n>sic$2'[
%*+G=6.*Am&Je;ll7^n@b'd0DJHhYhi_707'7(Cm"512si,bL-'alZMVS]l1LH;g3MNCeZ-p:Eh.mNTWdE$L1Ue[ZpKD'Y+?6*@.'
%'.8]PJoQ_XXkkr=R?Q]E$iW-<#MZarO&ancFe,<`2/IY.Sn"ttHR"f.p]XfRk@\]h*s%A1*q^$kiP3n=_"h;,_uD.sr:?<%n%a.&
%DJ1gD9OHc:I&'^WESiS%YI5H;!5sN1#IbY#*O6QAe.r.QS'#^5l3RCKiCB!@H=@&[iU*Gp-h3m%CdhM6Af<spENK&UIR-*K*R`:1
%&j<50K7M(P"M]QCW3?F<En2Jf\ZC=+c%c3DdQOnJ_`2",W?%L5e@>agmS@7=(F&66Y^942/`FasI42HUikaAdX#9]52["GmT7W&Y
%?W:)^s6bU@i^_bK#4YLFA6L2$K)'Bth3]'8a<TBfhOE0]ObT1i2`_S6hu4D;^^4PpJ^_5Ui$Zfa1,e*ZW'aZM!'(NOJ=0X6f*jru
%X'Xob((i1B.(3J"hg@k(]6lQlDndkP3_A&p2W9.R[o;Ws8)35%Kl1,'0/mN/OH1*F0Er!;[U,fF=oVl\PILU**SoEF`ZSDK/<\hc
%5$?Y1(I_t!;q=[%m9@bsSHW3L(?#BC]-aL)'El'p>*QR:f5ErgD[M>1F1FQ\1.uYI_%.5,&CQtHX<"h0V%LS%L@EE^RCS?;)E/8?
%UC$0-3u5g8cec8l=]SeZUA(u=oH1Nf$/^7HmO$i%ni1_9TTNcF5pC!aQEP9VMWth=WCKI$[3YD'rq<td(is<O>'i38J4A?MOq>%A
%?9;6&F67k/8S;&^7"c80-n+5\(Q@2[&kfEf97aPmh)Y=nMPFE+MQ<M6LR4Nl#*MpZB^9r]mmA98/_#3:+3CqLQ!#PT0@ggWJZ06\
%Gk'<dYuO$JeI]XGJeL??II$!$(#qj@cE#,<b%QG9^_W2,%qM6E"ALZq?4E-hE"?E8#9f(;4?[F`H#j&+l6C'H6n$oj,$g;L.DB8(
%OhX3f5ljNZ^.u*bl-#2c)r24LJ8/6!fQ(L!mKqO"ho^.HB9=m93s&">\_Vq9H.S/:Ss3+S@Qu*2iR2pUi),m[i?!,Q30#fd=XWar
%@VVm0R?EN%hY%*e;^?"(Yj;FCK,U:'fHqJ-_?<I2>;OZ1=Cc^aKg`!TM4m$Y+:BjRjM[TU,\lUc1"`%1=)as?-f1qXP3gU)rI]^T
%8q;][H1(u_l,$0P-0'<T865hX`^D9u9F`$UE=Tqh04_;MO]n/\eP%0<rooq"8G1''eSRsT>_H9<G--ceU.K/TNIn8T;m&!H1Ab%K
%-^;!efSsCJJIBG!%m[Ko12'fAWRD/rl3,ljC:61)CFjK8>0:$J47]AmJmChu9dI*h]"6FGR=q<G$aaX_Pt8F#/L)lH35e]l?,8nr
%$#;q[9`;7"L0EI;9QngC787%r+#$W\V,!nH*7hW1$a*&&A3i+lL!l<GRouptohu1<M62$[1Zk5]j:gI@+&7>+$4`dsL:PB'PDd'S
%CS]lCBXFL;.fht0o`._h.dL\793N0PeX/tkIdtCEaAe*1)C4GW"l!<m?8o*_FsaMjAl><OdHe=K!oTtDSekpp+mFko<bc%Z:Hb"C
%r+(1kk2Ba]GYo-n<7=0bb,$HXj$7R\(4[ZkMn3pcA`miV"F_>jX9Q!P>q67[E-EKPVfjZeG80VIM.Tu*F=_oGdsNeKLI2Q'X=N@V
%_9$'9Jm/'l]U>MafjO_3G+_Yr[fS,VN/0$CL!H]&UNhbdjlCDr70frRW#j9e#tP4bSDi,YF<e1".("n03SD$o;t\qWYr/G9iG-pN
%4">J3>?ol97$6n;>QdM:$8+p0&u-.)k];'<39I=h=I<l!b)b"B)Iofs^qi-uZ9%'F7kkh_P>ff+RX4B>R#pRG;_>9m3C1`e,>M'1
%WL/Q8J\hkcbUt1t(WQkp0!m<k2."W76MQn[V.3CV)9Hu"a)#pBs$T1?EoOkXQSi`Q1/Jt>kcUI6_hkP<A5:dfPFH"Y$LVSQ,U*<E
%H."'K0n*[1"UOFNHa5qo*<7AMfG>r;!7WQR$Y:B>Uu7>F"G\m:M_,3"PASA[Q8EZVCe_SCN-4>gQF@!SM6REg6Z5B!H9$g<4Fb2E
%pPfqo8J7ND6i9r#j*tdr-ffAhIXq1qiZ*dg\i(N2$m`d54N/TCERJJWIkR<LRQUqmKVp`lO;9)!eW^'Nf;].C`C-V'$l(mE,S-oG
%!?)XTA=/P,,A18=R]=t^bq6_;0/-(j>7&gJ"4,A^%?LdaTVc`s'pSZpf'2p0><9`-ZRh-iQ_#t#=[)m=SUt%X?AuV>0@'@joF`XH
%L:E*S9==QCOU4bgEf1_!<:gfq)snEeD+[<\BqqBQkdP?s"?IQn:)T1^WL4lg'7;Q=(UQJpR$k42E3Mj=9/^@K`o;4a^#9#&6uGn]
%1qB,U;[;<VV\-qk/1[)c:Pd/gQ7e+G773L\D]t0fn9?&u;Yeul0bell5_9W\gpo7i'$QA'-4^mRS2;1DM#',HI<1C6%NM?2J-^Vm
%,o!7#d4VkV!tJiMb=k<"0KW*:M#De:6A+kNE$r#RFMfB&r;?\[P>VaLR=F(3-6ZC,F6sLYrbrdK[jjp"q'_rS@L)@!kY#EL1HinQ
%_NM#=7SsfW*jlQ4IW#Wk>-r#o52p`!W]&-Q3NV&U#eCVg*Z3V)r>RgGJEErb'NI3;9(Fl*H-.04BpH##ds)-8Jj'T<;k'[CSZ7@R
%S\JS3^>[p=U)([r%,)KlAR6<NqE!SHd?J=-AuR?XXOh7lP%k-U-Pln-jr>]UH$gh/W=PA\acdWI1b;l@P"MGP-'%N#ZOUM'JJ0F*
%&i7:SIpkp>RKj-pSN[A>cLY@/8G]PB5,>`kb*)t6?Nm"6.u'WJ!-kj`U=uMR:MiZbJrY?%Y3MOC>@OrD-RGT_N:D:+"U)U(qj-HJ
%,EQ5q@'e]Y5B1p?QkI%SbRCR6&DRRYrC!l)Or7+iP8bq!g#8&&0Wn6fOXTgP.Z(i&$qb?,,0J,nGpDc(^kbXr0Fs"j*k[>&&*YBB
%X:ENd!Pm5(7lN,RbpK5!2&srj2uP#?9on?X@T(A6!"M)Dns5).PKc&fq&^^nr<0I9&sDS',r$&:Vg)6JM1@U%$&ILsYir^Qi9M"<
%no_@Do;\D7!.-aH\L2sM8^'l-9W=\H>80'@@0E85!2qhnfN6k&`@f,*Fr,.12nY-_;-&4QD$WUo=F!mf`hQp?)9/goCKGK<Xia7]
%;hGZaK7Z*eeWb3e.90<M>%dR$^=TGsCG<+;"ubf)BnY!/!F2??mGUO'.^>ms1GmVr[=gj2#/&X9k@@RX$:.E"c)i2GRm7lEe#@H[
%,JeFKra@,`[5o(5"/<.I'/d/9T'RB[isGX\+,.QL71NeK!\W@)f7kNS%(Ma(`?i!%0'>p>*ds-7?s+LB+8#]Ve0V7uKmTOjTN#GL
%&9H[%:nn#XkDYbel'm;(>RqT<-URqL9^.N>j<Bp7]kQ:7p$D>(FY_TA&B^GQ;;<E=ToeeI?Nt^1I?NS3;GVIF+2JBjae3*WZ(=g&
%SoE'+U0W\U\mBn"b"LU`m!@CfQjf,SA)u``De3Ntls5MW/b,(l=YPFVW*,"<7e]VA6H#3M0fW=!<YZ;(`)]j.ia25>_fu9CJG:Ej
%ZLN]>.PEuRq\<3!*UlE!Og<\u-&3@PM;SS<m#oAQ?XOmV#-4*RiPjS1G&7h#'O6f`6mn@U6FfAb'_>*:[2DOPH?O%j#2>jl#F7+d
%$1=o&\8e%.W@5Q,L^_pN!#8uWZc:)(6"uu1]1FO-Zd]>a1@jV&EKRR@]aL*;O9gt^7^iZ`,Pn9M9W,LH[6>h-!C18P$8AFN&srep
%5dr;^C_r6#%>3FeAMS,!`+UkeSdfW5r"f=u.)A"kMAMi)[G_M]Zf4Wpm)96rLflMGBJ)4E,PM5\HB:LT/fn_J1'>B!W_?bg,F>N7
%]h)8'UZ,5S6iaL$-p<b2Be"Dq`D&78".?M`5`'bG[fY-sZk>^0'#4jLTH>;G/"jk.!FMqB78bpH)'6(`=Q;VSU!T7e,[rDNNu^L@
%_B8I-FWe/s''hf'&MXtrBWC?GWrDN?BhF34F35]*S'n<YDcP,1@3H#EJKema4Lno\PhGk0JdT=bE>,FjRZRa%Ti4rZRg[<sH9S3$
%SDZT%:1<`.HfGo:,V^C.2\8i9h/Q3p&`KA'N,XSHaU7HrAYBpB3*9l$nf'AF$]]>b^l]Rk@&?_]":d$U>co3"<@eWo(:h7:Mh"Vp
%lDS;e!.<G`FN^2OHr]Bp/^..f0Jfa2UJmY8FD/=/Ad-ij+W`_Sh]l!biq0@TK*s%^[uZ*k=ST=c&nor0>kCKeL*OR!4fNL$Z:uuU
%E%;>dW_aCN(^<X#^rT'^iUP'Fe(I?Him0"dP,9rLE8k;W3>dDuJh&8]Z8XBs)`5eX"qmUp!Z#eUl6RkCPu!$,7^OsDj<BhaMAZe0
%HIt%>!HuR(>]#?5g6)T7?N`Z3/><Er:X%X@3.b[%s6H/f5\&0:m`8KTGCaO^<ECd'c\BOcT]T8XG6'$LLs.&b+^u'9k*.MYW4RTG
%TbNWGYnC!ti[DNC9PYsrNLc"[+F$gl<_1pf@"p97mt,j#&([Htr1qVb!DL>J"EV+<*/fAMEWe``YUrc[V&:n;qj$,k$!/BEML2g_
%RaXe9s0TC.,dLfiYpSc[NQqQS*,qIh!l>\(?q]58fgZ3mKh"$`R>O8GPE(;MNW]I+Yo#(7WfF:YdiRJ'j1.u^?uPt",h.7M.4lKp
%)`ckeZ,A.*"]X-LOuG?;LKe@K;#t+[;0KMl&1mo-]M=QH;P/Y?Oq`7Ckm*.Bn-[gs3CTaK`$UP,R)la:o(S=mO&"n*%]Sk#n;Q`l
%!+VJ&@5=aGJ;B%j_\go)OAoT8$dNWM`.M*AHisM]C=IgA#SXS*)>S6fTGXHo+"jg3'6m1tB^-AP?um]@@<Y%'LGbhq<73_tCl\3C
%_`%bK)bR-ZYqA!<Cd@rcN36#,]d/hX8W$MZ""Y)h)0+to`H:Hsj._170P/r2=Z&#<>`=lRp7>-Z8tC1.5IklP1$[-Qr-ZBu*6<9d
%WZeN-@Q:nV*3!e+H]=GVLFHtp.h<N?`+^TfQW"ki=7G^Qs&Ghkn,_O#5t1hc0mEgolf:)i@7,S2GuMJZXSdH?/`u=,'F[W@?\R.K
%F":WAYoN6k*l\<oON[tlE%rhji^&!Z4>X6;obDD0=KE*,mOXr<h=Cl6d+,_4<@`TY(j#h[erNt)^bdW1mme?E`-\,SP%0!M)487*
%.3lB*4op[_djeJd42>UK?:-AS3u?cCftH6-W6HUXUg-]u4&.r%#Ib)_/VI0BK]Rn%iBe[gh)?`6SLHA2RZT;,5'N;)6r&&;RLMuq
%'Zd,<3TGGeYZqdDI4#-3Kfrd5_1B=4!_2a3#T0#]XGG11pG/uTk,K^PTt>p$RCGs!#m`R-]eNp_or81Zp^s<rC`kH$]:QBE*MsOB
%Bo9?.n&B$#)2GORIhJib*);O?)a<;8dQsj+&<XNUIOZW;cLj_&>hR'He-nqX!H4G"S7&8F%uAl\)AZLjeJ"$a8,sH4\/Mm"Op7!X
%g0[FJ(Gl-B;'RTaZ!E$<9TU<W6QN>g+YZql$YW!e,D#KL=*d!!2;E?E$pp:O.;WPqKfo7f8g#u.n>o%>64_Qp4*'g2/Btt*J[%m*
%*mQWYDfFQ*<RQP$bqWHQlhZ_fJ]1c-Q5d#e@bRl@\'UDeWB!T"cs3BqGVmE!/$<SG"C*UPNEm#-K7Sr?j>,><@=0'gBI0`(UIFp'
%W^`$3d[Rs3K\AR<%7-3!BB:P-a:a5[2fhn5&G^/Z05SU)6hhIp9`bUPX[cCn6SbrWd%OiAfs?A%[46C#G`.cl5^3=R`r^Kd33)+-
%aon1W5`P5%IS>Fi!:79<_-J][30LEQ=6?LK43aF[LVkbW[A_L,94*!qhMrLr:9IbU9Qgr2Y;U,%?l!2>[Y32$@HQdE;gBD8Sd>1/
%/k%j"^$#HZlq=oPot;UC'p=hA9ZN=n"9qeMFS>n8L<nLS^jg;gXDaD85M$o&2&/(ZH'o=b!@s(YoWo,`K39%a>m<rq<C'LI,Dt>p
%[8;Q,Y'bVH5@PofXMZqAH!mVF&?I_eGpt"<PNW=+JdGGOn%=(FgAEDIllm$XT\:_m5mkVJ+'rUqRjiRcO1?^""9J]<;us4b`"ZUq
%+]T-W7sYIq7j5AW3R'Te$)Z]d0@hM&lY;8t*O(_725qt$RT)9*O:`R`V_i+ADIW[oh7Xd#Ziu^@07M?1Q/!PPUTe03aLN[k]^*3@
%7.=4Yo3qgo>-G<?5!%KX_..5G8)ci#.jjnhBnAa]=[U@eQOk^#hOuM3Z`XN"ZJeaf4G2=d,gtO!W!uL3MCFUFN25dm0JLHT=6F5G
%+;CB-_RsD*+O^<<?C`DlBEX/b1jX'ZJq&W4BRLX.BdXRb1cra9mg\/KHt1TC%9`=<KF`%Ka1@CPdm0r8*;"jf^Z'J`W67q.kf4Ke
%nXTj+$<>H.L+hS@8s]\tpVk;5PSI#?Hc2N5\qSIUg-kW\5Z/gDB`8.dVhIRB1YkU>,l;((g5JAYV/UkR(`1):Ta=-N7^H,G%Dc&C
%&Ll$uJ8QV5'0r`DR,G%GWr_Q+k>oGd@S=PJ0`m<b0[1R=0Ha7J3ei3H'!PA)mHCV-1fq)%c'&GBSHI)?Q8J5Oo_L=r>P?oB=L1%8
%d#'$"k)+^(%Zo8PC6'U=Fs$M/qZ5C`SN5b<74WWCQT:?Gb@R(K@"'!c3k<^%Z<5kg$.O`=mW+T95k_:U`/@>rT.JHUi6`@1Wq7hS
%Yh3?p1;MIRDs?ec^glU'dN:)"pF?+mNn2aM^/7Fk=Wn#@+NlUtFk/-N;h.Hd@&B!@ODnh\Odei:50tOZ.'LP0foAuCMhEBtCdlR@
%)HeKfkbX+5***',T6^MX(#TH[2$@IBg;l8\clq[KP#dGkIbm_jW=h2h$0<.CX?pR;-A\<3J3EIrT33GGLt2iL5QZ7SMA1nu$)"'s
%\a_hgN`8)k<-ViG3`)n]e+>h`TeJ#%>Io>J%+e$SW)YO7-@d"&K!BF>H@0g%U(=<aoU[R&H(kDuMqu[52gp-FGShh^"0uND)m5n-
%iF2+6qE1qI&m%Cp:s^^<Sfcs.e\,gRXe6Sd"7AtgU[Ic:roOR=W$(L_]`b9s8NMr[!K"Fq=O@omMJ7#?!-LVK;n.;BZjoX>V9MVj
%nLQ5^n,dSU"D+OXXTYJP#4>E>fEqnTF+^8B"@qC,,fXh-hjA_/;*rbc=r(<Q=^&Hn,Ri6_i71@O/;'[1-Bh=\5c:S()*VM?.[jZD
%;T;k<<3JVDl@g""_jEHAF9$/-+fjB@r_ks?.KZXV"tQ@.Fr""*/Ya,*;s[6Q3<g9i1(AeN:Y1?MHA-]q;pkhLr_c)p'P`BtOfU>/
%hSE*"YRI\:#1YB&:bC;"nnA@G@.cZfX(nXi`/3\[9A1hm"Z/7?YlHOSC<69t0>KqS&=c^c87ThI!^_abYTdJOOsH,<l.k!C:jYU7
%:^dsFk$c(_gDbrH0J2DLXg>RV9RA1'Rl,RIfsBO?a=mbBoLC]7La:.pg,4u=%1G:(8D'hJ1Csnco3Oba2@Rjlr]8M//ZDf[3t'p7
%_]XQ]NO84r#rH#cLe&]E#UIEXP>K%cc)smpB<p\.[7'`bAEtl.JjVjj.'XD@#t$h]PR5D=#r"l`oS'!0]`<lS`%=,<-JLUr`>Ma3
%=62kD(oKLdQjqABK(9F49d6<>A/^#Qrf=aGfLoQ:C_*_tN4'/e($nKRiW?rCc[30Dg/1pG].%-^d#EkHC6\Ln_/e<FH3$]t_LZ)E
%:`2,4lSN?7PU)%iBNbo(Th`+KV48rB1E#'=kq/-Dgp<*0e=5%Ddr3hWcL=6D;s1hT8E:DCMm(Or%S^d-YsHpgpnc[X1CM$GcTJ0Z
%,p'VZoi7i3hgM3^UD6[K`1E);Ou<M**[Y!k<^:)NUf84JeOuj0I'srM\q&$/9:r1IkE<o#@3$VWEc8k4oLspZ&"Aeo:i+ip<_-U9
%lmVuki<_bN2#Sb;1Zqdp]t]@5?:06??05T290tF8_p!cgBqZ>DDk"%UUkPdA[<n;t#7kSV1-i@1aLbXCb_h)t=i@:S!OH5E>!aLZ
%R.skZb/-lO(P(-tjq^sB<.(g91g7nN\Ms#R9>Ta]O%TIZ$r=^>&=H<+;_M5nG4Y?#?[37sKS2"=I*U:%PtTI!.QAemQEI*'O6B4S
%n1>p:JI=k:g0,#d2*u.*UEu8@:b;IK=;p:cN-@!:\@b?NjkQga5_IlL(NX$]'*4f^!SCAkc8(O4'l)BTKG#2>eX@U]URRUapf4Lk
%:rt'5SF;BGq1Dgho2oHb2_/42CK-<kf41ZoM=Fn!>est*7o7VE!J>ZXiQ+-j?Du)A"lHMbmBuSj`L*MLqF^OiR?N`jcqURI*<V:A
%+jlmYY2pu[6eSdAM##=H%`3&N1o5=Fg]R.MGFA9,$2W.n.!+[$&ihaq*B4RN_.nmc\YU0Wfk;$4%eKf6_*L1^-\I2b9uu^^,.rHF
%gE^pP=?gCYlmV<!_bC&aC#0'(=VG`K.M'7Ug^e`W'MHUH%onL(hl:4A]%H7BXdP6'9jGegEJ3tskcp7,QFl^^ffK9B"Lo`sBmhc)
%=IbdnZCGCfFSB@T;D>(!Ao.HLF'<SBB"M.JE&\#lb!'Ut[.KG_m]cMK9>AmrYXg82*-.+8OGF.QXa4e.&"(.d._mI;HZU_&cB;%J
%Z(CbdSD?0fTeW70]P2'Z$9BD!L3(@*9=f4h+t*+Le0+B?"7&Ni\aLfWCSTc;fQMT/?"Z4`."XYN;,0ZE(oN%B(C#[SnuN/-:8_YX
%$C935G"WhmHn(Y$Q1k9V)9HV\!\^FhP?)3^9/#;7g;C_EIj`.gHpJeh9,:Hs;&/l)%Yf#C/4sp`EEu4.Zorrc6;rA,VA4>cXe7U3
%p])lu8M5Z/^GG+IWg-Nlf,T+rU*rmt!53Z<32X,f5qroZ#9(Vj6>_n=VeLiJYNc,$7_"<'=>$r`Q_)/cF$p*?^02b5jS!)_3>raB
%AWjBooI=OW-K:cA\h^oR;F*k'@^<]^IP$08<VF!.R4j]FZp$l101r5FWZ@+t91De[QQUEt@[t848/n8O`%fLnAjl-WSNY>SMn*=M
%g;k+3P]$h"'.j64:Dst_ORlZ<Y?)p8Ajfgme2WI6+.2BIa5iu[8M;A.*%aI;=%QiFmUHPr%@[;8)X+n?>bh1%'iq?1'dag-7!p2>
%#2?2sK0_*`g64[$Uh!OL`ti7iZ4/kJ0%EH];X.[LNK"`AF]o6o5b/JMa@IE_?DK.6R))$cTHT-t*8jak7j\"WB7_dM;7ILtOk:pX
%<7@C:I<W$X79Oh0-Y[!6nZ#aSA]p*i^!3+\9/9KAJ@c"JDgI@@P11+4,@)YX7*m&i&=me/Pl?1KeWGp2:(Q%@@17Z]aO3GSUO%+E
%7ahH5-'1U&-4B;Ndl.861d.(i%>kBW'0<%!+F+2jUSd;28gpt/_qKsa%@c]dCn=sXa(OTp8-Np0idr?TCk%F4&H@_Ol-sRo+`7WK
%cq)N:lOEL4LpC)?4)kub@/^P=1C$Md178UU/H%%uc!-DleH4+gBg@.R'Ss`XLO:TrMDXj2kU(,Os%0XSc0d+Z1Li6X7MX&q&UG+5
%qOYuDh__a?A@l1U]h0GriXR0d4Pl&C^C9^W3Og/Q.8=Un4QjZGZZ0K=-le;PDkVAlgoD^/-[6qo)@dleg_*oTWQM<"^`6**k_U-C
%.ks9s6`VF6pU#HCb4??l2@:U>g0eY/Ikfbp@-BlrRh17s],aQU.kX-rUEY@H>hMMuGumr*A9-V2?ko63D0H!rD;Z<YUcTMU]S'!)
%;RR-<HqCA1E$U3CcTqiKjA]QpL0J??-'_!BFG<=3bsiH+#cHMjOe7/j<6[9I"[9RqD5's`m!0eK0?lIGOIBZ&]J(JP$QTc2Di,1&
%$CHf"7AuDk-d$1lcL\gbI>PYYNb!*3_5rS]+>3j"9F8@FX4p%<+!=%i9J=Ij!nt!?kqC$h>_4e64B'.3$&R=\;.6\3&k(OVl?S8E
%M=aOs2+n3!&k$;YW%9Btn(L+SEcb(H9@/^#&TNqY!KgOZN/?UN'9,EPh<Ut(7a,V)i`MKH.O2Ltq_#,].;@)M$tF"^KX%Bu?kP:S
%%%[*[XNL`^00rW&g&=7U6P!^Iqj.+8MQN1u.5]=IokDn^^!Nj7p**l>"6>Z(>cG[uf7gC-bV7j0V/Zs'Ke6n,7Okr:*X9Ya]3ONQ
%/.Q[F344Vn3T*?3r#OmkfZ6%hIA1<U+V^%dch@]kfdfTaLEqrW[R<>ZCfTWhKIdGblln*lg&mY4dRcVhc2JTNVI;qF$#c%l1:S;Z
%.tUCi+<jN*[Ur/L.K3$nlU_^g;^[onk*24/$mh6Ff,1urWm@R<K\+Q"^c_[cQ#gp,#^WTm:*,[20:C9nF]K&?-2U_?]o6M;^_&E9
%7'\U`!$iV^EE2(!B.Pt':QRY42L&eJf<I>Jc9Pmah'P54`lhNMks9B4^A\Zf>l%m)';Y/H@n!mdZ]k(h#EW(".bFomACZ\D3q3>N
%r'c6R++WMo)H(OSCMjAc9!=1s?KC0+V].!9cC34[_+X;!=P9)`*9?f"GB&JbKK>H+>LfMc+[Xdk@*#+NO<%UnpO]9,6G=?.W$A;P
%k/AWiHM.2tl/KB8pDUkG$CZrVe#:@V>@YBPcVDggK\OEXn`mCH^l:l\:<aWt\ulR\$RYIudLng7;i-EXk<j$RVDJL\DY.*?c)E<*
%RWK.)qC]lF)D9i)_27DO1RMd\1Tnd&hCU%<.Q5WF#bEMf1_+'q/7B,S?KTHH8\(#M5+7h;n![2VRmYhmYI$f6#t_%IV!)[8pRa9b
%K1>ncoQi9JI"e2U8DDrte\UZ\/]"U]73@Q6`9SgTBE=s/\K(;-"jg.4\oPjoW'ZAV)HTgF!TEL@<cBuTJt^K2A070M6>Y\EH,m5a
%!(-+AQTk<GqOTRdFY[-NbcWB%-!KgBUn0ui6.h-L"#*Op.m/)b*DUFS8aO@3F:b,(X]d9Wr9(`oFE;FOE+j#)7(@inCEeH%A7En_
%0-P=_-/Uh[#k9Ts`6g52m[XVJ?0T;!c3?G'?*ta*,T^+1hDdFM5gNDu(s!?PR:'0_]sEQg*^nc9&XXgKfjX$b\=Ug1$_*Ou(?BF*
%SBMNF\K"5hV=*BlQ[6!F2^)@&0j/%FAHFNm=FM"2*5=g)@Ws\4][5uljkBDSnSb7R[cI=biP\c*.eTR=,*0D=`(]edB<#T\66;U-
%o^^@=@;g7GL)-Z7=*dS(O1_@K[G<1qdM).;9*"PlL<MNi9Ns55<tqPb>E/r!O^hS,B)DJBP_"[G9K`c@Ks?Q>.modOm08q]R#b#6
%acp29]b7j3_qcT-7V+?+7Jpi//Fu3q4-#oZ\.speNu*"#Uk3/;'AOa6]AP,Y8`X^e"A?!Z4?SJt]P*XC;OgqlT#P(+9I``@,&^sH
%(-+THZN:u!a$[8tIQ9T.]D(<In8`VI,B=d$&Kta^G"s\a0hFS"\?KVb_;D62"IDFJ/6LM)0[Ki)HT$$+I*!q_<%^UQ=Aki]_NW+P
%$$n)3Ya>/*W/gf4,f:s=0lk`fPYdiU#\;3aYkJJZ<j$Gd:H4aBa#uh'IRJ2Z:sQJcV?uLXkU0D-N7uBc,''gE?#JmEMhDj:U.fM\
%dL\pUU6,(P3$5f@(R"q*(H?MrcCp[lX>=+s"^]EW??c[!,C<<5kB3Fi^<^!/CTM%he-uEBTV5p.qZj=*8F+b@[?N6)qs6_7d:iLj
%/Og*$27ZP57Cs3^b6.rlNe0"O$jR@P%4f7=aded$C!lK63MDJT3g/h(UN(<Mk5>tsn'*2#HDlloc!_bl#j_cD%1iUj"WE,_TDnF[
%PN&!=hfsXq$>RQc!HT^D28pGc,$GPb)bj:_+e8KDj=d]E-1ddOGk>K'L1hn]FlHoOK)JaHd(6(Ifb*JkqT'EoH*e?`BDBhaBC'Vc
%?Vo-OVLg[l9F7(EMj_VKmuhT$-Kq=$>IS%q/o?>fj/g23<D/^4$kM=/P!+E*6HSc@jad9i==.(Pl6m?S6&jqp6Ds(".N4Q>8er[7
%ccIaV==RDR]rl*h,`Pf0CfM_Dn3b*9^QIPVj\@MM=a^mMcT6TNLP+8us#;HscJ5l#N((9s/?1"sPm5.O%auMO\pEfnF/J.dMY=5k
%eLMIcXZjP^-9=d:Ob7A1j<iq'btt[l4'H4u&iYq]]sP'BRbA81k2)H^69B<gc`o"<&R)>bhoq304NbXq^8#1)-NT]>-#o1DAW^6F
%U`>;"XG!)X1C<"'7!\FqLJau;C7]2:lb6YMk+.d5)GHunO[r"fR8h"X,ZG1tM@=r[e<^Y9(E='r:^gPo-39BZ\@Sf`E['\2F@jg0
%Ml^.1dN5;WZ*cP\T/'Vh@\td*@:gj?<e@#O6:(T`aX!r-`=P6R]n;k9oAE1E[YSDAF]fIjoCh*&b$#%*/"Zq]2QHnH1k:.o\/3Mt
%MZ:E_)D&!?QrKN?2_gJ#DAD#!`UGW\D9/7joKt)nKYkX;Da@@niVm8Glc9[ZYQD@dK\h][iQIrn:XqLW]OkhEYXf4F:6ufG.(p/T
%q^qX-C,M*>D"M2_o`mTilZ1i7/ZBWnLTs)aF3_AJ.?93S3o7u_pGU[JX^oH4"=p:CCk^f7NJ5c#,2DFqX\P(4S#DMejp8e6?@$KI
%Gdg*LILH+%+U&1)nu>9T3PsM+,R1%)Yt9Hl'tC+u"mCiHYnRWL4U(Em[XPQH]mtA37Gj9KEEJ^Qh.kW[M@P&'pC,iq@G;"33tPXK
%Zdr4<LFG*)/59%6DQA"rR90/1PT.r,P,V-SBb7.:*n`t&2+:Js\lunfcjn`j&Dnr[nj]Sc=)8UOfijZR?OSP>.daA"VF?lcV\Kb7
%C/p&E/?mM,@r(a6,E^^1;i[Se)VuVk):iOt-!g_U$mi2aV0QKS2f$+X604[Tnfh8BS2B'Sn>;jU$)Gk0T:P&,?;tL)1BWpk)Ik1@
%i&mB9KFt`=A4iO#N.Yi8*\^6p\8J*/`5)_'Smb]Y@"2MQPf`eLqb:P2X%oiNef;W8!-Ei%c6XAJGU6ehi?7PrR7XUa05!,sqLpqu
%,ZrKt%_s"sPlu,-?Ps>.SA&DI`%<O%;4W0o9<,u;?:r-P-Dqn*^2=IM#V0#%"@-ST$I!Rp"gk?_Q@,;&pRjc\mkgJj>qs60Mp+gg
%&CL(8Z>9b+E#f@Td&fOVp*s.X@E]R`Juf_1JofN2c"_$[5'KkN%'e\J5ZWK[_dF:-;A1LI->75p?5lqYL&%XjfqbMDDXe+I1*Tq,
%%qYL[BhC4[.=u,sC9!L\Vh'sE/'P!rp*QXG'0.0g3qY'DRDf4`"(1>M`Ks"n40kfX4Qc2bRbJ>oAoo]iLP'B4`p/I)ZmA.h!MX)"
%VjPt>p9lt-$gQ8A"N%u+$?"ZQ!d0rBTqd$.`#:C_,$f9R)]#25I&?)!DHMbNSB=R(7P!-sPPQnVAu"Ibl&EVA)V:r)+!@/<6Me@a
%fl]s1:3i3=8ZH06>1rS4R@oblpX6trfoP-EjGZo>F_iH!m?\S?`FtVHRI1CsFEIHRM>=<qfr'i@5'LqUeEsEu+NLk1?;2)J\!.!=
%F*A]%[35=["g76UOG0c!+_[7:9SAesIi3P;MSEIV#U?5eVd5R45'h=,8sRju!hRW]$cNjN!(XC*9YFUp-C;<RWB![Z%Z`nJn`qAX
%5"]fd>XIV*$Lm[8XPT[#=M@j6l4u.G-eY'$'JfVB["(Qi&E8OQ)G0PC-l<@\8pLTP,:jho%Ake2?t9`[:pgT2aqc1/H8tgj;P>`N
%iMHbmXu:f(3$jqQlUbSlG@D;7ESB:B6F#al@(]t#[UPhZ\=F4:b_K2591hp(fqOF/=h-ZA0(h0Up-CTnFk:&tCgblKZUHr11AI8F
%m@F=qZXO%$C`IRX,8hnDkV*UM$/#-_!6=)L-ZoNUq,OO",8QQd80V=//?Rac2C=F1(**5TSDq^iFGeh8*#b#1@#a(OTZaDj_\(I`
%-4Ua1.5g1E[^jd).sPVs,4iS2[LsAcq#"U#,=IbHeJIYE\htaS)e-a[JDeE0B&R]QJEK3P9mGT>Zm]82cSG4M>s_67VaQJ'0L#?M
%i3.2P+Y)Ye@4ERO31Js?7`V%OaBj</'_)5ZT+:*Q]d5jFO#YujiPV^q3@)Qio=dUgT@X>?VtSKIPIP6L.eLr>*?'-h#B65ofPA?5
%=`/npO^r.bq;65ZT)Z)f18tbjj7KqW>[8j.VH?VaACY/:9d7OD$k3G[bC8LEiUj@p'1=?.)_EcA6q6Z3,ong3ED\JjnO3JF-I.C*
%1HKi%W*u8!c%*YDOO20lMA?Z8ocP$1SBjsPWk;pW`lucJ<)A15<,;U+;F>\;[%1=O6l>8M%SV[$`qAVLn^ZXaNCu<)E,\[=la4,Q
%*\VW&O"R@a+L&]ca'r%OXFS<1F%Gn5+j7UWDB/)3=p(]IJK`U,@&7e$Pj6Kj+h7AOMQk,/$UE$j1=-,k+Kk\RP2]FAjcft.@h'W)
%!''3t#XgB#=TfF[5VGBd1sPt68/9%6?$tj!)t](,EA-OFS9<^p+..dqPpUX<V/n^<5n=2jVZese!Pqus\YEW+bT0?iTt^1\;jI0A
%[(W%Ebor2NVXIt\$iKu+[]uJci3\0]AArYK.NCD!h,))BBF,F_^drNLK;FUlKg7:h0%`KGGc0&s#SKt#q+/B-^jX!OSVOPf^FXBN
%Lp=;1.^-2r4JO]Y<-<dr[j!A?/YQ91H-$Oi)^aQ0*2A]AQ+,Q-P^$M$PHsY"&;(%;d[2T;6NG:?/;Wld&hV1YZ2"I<qJpJ#WWk?$
%Oc0I;0.^/Bf@ikGfLiCF[PUV/mE.(_AUKQdEUDldRGRnXkDq:rFE.!D`SKu9cL2j)FoC"\e^&-Y5.eE$o&;/OGCDZ'I!1!hEHf<5
%Ie!@,ab>!KAT_s\T).O-o?1e@GGsT+^NsXt;#^73TDe$=mG7a=N6mVYj@#<gN[VR=,&At!>UZo"Xp9Z&,!;D[3a;LC:\(P\RI0Op
%jnZmUo<YL*dsc:\q;bn/PP/;T&*qQsms]HOKKW_YHY`Ru%!l\@iqh[%T=DeN14[.<,L59J[Jo1sn';6E4*]7lIHE'UoRGH,rbKLP
%GY!UN%fc'kmdT9brp\Z<GYp8P*O.#N]7B.I6k=rB,rV<qWG\8D-=BBk'YHp^4F0<8S:5Ll<#,i-</?-(B!H)s6+P#icqj).lb_2Q
%Di3D!o3H<]p$EMI;LUM=k0tbM8u7H,#]i^GT9$&BaD.%^"8KC>MT20o'9Ttq%'!Nf?fbQfD<G(u)kA2+NHhtFSlZS,<R7elBng=r
%"iIZ02fl+Ve_o44-R,7f8oBdC*R8\S#'5*2&lF;17f8c!hD%Z8,c-GQjut"+b@pF&GhCFD9PipO(A8LF"cAAZq!@$%=Pntt)R`B8
%4htWtQ59"p%ch5CoTgjq`^irZX/\S/"9\JR,_40b<f'0XDX'p=!q]l_*LdPm%\/%0AJ77aT#B1``fa96T#jeS3rF]oJ7M8jfGbG4
%)F8$JD+6SrfK]aXCm@N[B"E\pjmfi"H4S,N;0"Na!_W0J>dAtW)CCW`8N[nDAoKC0b]o!^IZW-<)bFc"0-MXRDD+Yl5fat*k8qEX
%3*Cr4]4re"MRY0Zs%5k`&ssac+eQs\AV7dnC@NWO)'TUF=c4[2*[(=dd;G]m6#)@0a33C6I'NsiE`p9pW%jLNe;Pc+]M:mYYX5@P
%K&LAUgR-Ls=d\!$ip+t'mA"=r3#U`(h.gEj.W&YRT<*XqDJaN(10r0Di<6kASi(C`VAKro4FiWE,ZV<AG,0h"1Tkl*(0+@n_U"gT
%pMa%4'cLr^eTG@p'216oE.W^hfqLlecoUHG:H!:d]dR<oL)lfRTiZrq+CgNO;$r:,?HF5G:8XGjS@E!Zg^=TrlC&W;eirU+'4i7Y
%&4qh$+g\UbW.c:Oo2'gLWW%+`=V,m.9W>lA&0TYJ"t&oIbK.R9#M,rsHTHP*4`9m<:A6G)bk6PR=6_A8P`edHlAuU='/QtJ,r/@b
%9CU+&&-'^=(LJ=@>B!^X%i`*6j_1QD4P3Su_1.B23$d!7"[4KM3&lf699rPV0F#tb/Gq*UJfpBW;DB!=+[XaZq>qd1"JEcW32LZ8
%!8CJgmsT0;'pdCh7sV6t_/cL.\)=fo8[O\9^i)Iu3?)mQC@gT$Ane,$U8q>EG9JJgefieZ(RI(YenOZc^h$#fhK(P=V\X8=/<u5"
%h5,_C4#ka<TI8T"bc6=##BMV[9ZsbUm"Q;&kadZ`^fgO7S_"9#:&PPJh*]YfPABidJ=<tS7qD6;L1+=t8^lpCI4L?k%!_*8dRBR:
%fer']/b=bn$unh<-Z3<K2o_`eQjg',ntF,YBEKf2NbgSW7R*Q,Zk)HR$8$d/6%Z`c4o1Q+>Fm-0QCM?FpLDlL@X!:j`Vc,l"H6*T
%6^@Wm5-W_A]to(u`I^aPWd#8HX]>Xug-:GPDVa8PI35]07=koX&_OM:p_hW%X58eIZ51!EEjO4G,S0*]&[L,qMg53)%MBqW^%Um*
%Gagl:ZH4#b<()Kqor'.^nD/#](&IUP^.m2tR,p>F,o3,%@?%hE3H^58Qm!q5-(BRR3XUD_&s<UN'=jC`_@QqD_+;Wo_O=Z@iS>M+
%.Cs+.'>m\u'^W[(Wj;8f-"dj1::Xdf,rE#V@bgdjS/^Xs7k1.2/ZkR-a,<n,jf\6'UYH]V;2.V['aE.lB57]QF?<geg.[f"-QZk6
%cm9)YP;#bfkkHFb+c'oJ,b=WiQ,NR33<glT"q,PK,.kjKWMgL!,4%9$$a^&Af]&.h_6WqELgcCBK-p6lh5J_ZdVt]W>!Q"9,`,;n
%D(f.eh4d1C7]I,S-6P[n`<2\X8:j._BuGL**O:&l7Pjs9on7AM5U%ln0eUSTiH"CcS`#cI\s%"Mj=OXS0aYu<0]YeN@-_fIlgGMO
%Z:0URPch<k",CusNNZmZ9>"Ek9hjA=/a\S%4Djb"E[^e0+![mn+>&H'Db3*M\p?bb*+EQ*eU!(PX=B@1f5Nkj8_cZ/5ib\8Qkj>f
%jPa3mOF\_Bi.=g0S]"7m-_7H_&25%pV%bcV"@Y*noT]@ENgkelJ(HY\7+?"/a+]T?$1]DR!qqOSD*b_Yd%?n_?1+j!V@)k7B2"1>
%7O:jPET_T@(7cL`6ZU:c-L+,\6/>;.6KM7#*L.-o4*EbBMW*3QjD_9n8VpF[$=aYmR\kI6RX92FUq9+;B)*s6q<M16oQ[#C<]eL9
%W3VSCLQ6Q_*uC.9`J[[]I6&>E<Nq;=;+Ecj%\hU4(0FLCQ85Wl=f[f#TBLO!3fq%l_8f#-T'1nA6luZE/8l;W"B%!U?;g_UIh.\s
%;6_9D*KIt&_<GdQP@c#VZL?RX#e"@HM/(V+e3OWZN8(ktfP1q+`b\+]j3e/aH&YU_>9fq[eO5XtgaE_J=9[A%=uLFnXgYR$TSuRQ
%e]<jDp99DG^qNU(X@.OuWe+VWLPeDReaeZ`eq,[l7[Vuu(H;jp!#7mIUgD(XMKo3,RBR/r<@!,Xfij0Z$(Ukj6,I0"gB1C^gtF#S
%+02"r$GMJpCq\b#3^\hi^EaSaQE1ECjEp.gY$2P+\)sKEbTb8>du9-d=@tDl"4&tfa)bBQ,F3.lHVMF,@.fi^gD]&<U_`WOkXlE>
%kigmbP^ZJ2E1nU/ZL'3n>aWIiTt>;inW'LD:jo8P'Su!*>7W%`feH$l3KDB0',D_>e&.Po?]782$^H?f+e,8eY;iEe2Q5uS)MV[$
%0^fc<_hi780%,ieGh0d$;UnVNA&f<-L(d@T+t$NmqGukn-8(H=I6D/F7rBBXFQ8`N2a*169Ka"ao"$J,PB=OANJBE'2OmL##"W$A
%'t&O&</$G(**=TtODkKR1nAYhBM>lmqqT\0k1Cetad^`"</KVq`L&1g06q%A]j;#Y"OXd,lc%9)quO`OQ\soXYH"l>m;&h=eEJhU
%a9Bi%oca&`\Wk_T(J`;n924Y"*n@qi00mg9A?T._*oOo;M?9-4mt=bF,tC4h]\ET-)KcAM0uPC/iK/\<_I6c?;eFM#BF67rZ^7MV
%+'@e/<S0Y%R?bB_2u]VNTeDT32a)X9a%Z7(?-#qp'9o7g,#'jm2[e`]hIH_pN[\2&\k*-<`Y0\bBT^A/2'8`6Dj.Z23Dqtlo\Q0T
%+0RHbhm4C$``%75'Ze3<N!?QahqQLk7QC*ATq5.KAU@=qosRX3/!TCgG=+VqC'J<++"44(r=lK">i61b=mMr9#dINb7?D&tOB8,h
%03CP1#D.j$fh.NeS5*U^PZnYmmLH$/j<#nJ,>dK$[<%C;U:>uqbp')l3qCL8+PsoGS0r/+)^&Y5ZcmQH2gC2+'Tm)/-`2[3fKZug
%)@Oa;DX)J$;JE!=b5nX'."T[V7ET@[rA#tVP%ah6i"ol^W:E2[^f2H)?e3utb3TpIUm7l`\m.(sG,oC4';W\WaJI-KG;4Fjn4[&$
%V9&7YS?<4-*`"DQPo9>KW:!rP9LITKaG>CBF9GTn*<S1&"B@Q"MrtA+:=]2+W:9'6+K@D4R>BPk\H-_s`kkE7?-B<nBqZ0fBKL2Z
%A<V^]de]XmTes]fTcJcpecACa?qjbIX$\_-)BPH\b"b@0:B!W6%JjXE^f8=)=A=Jp=UqIs5,56#Yh#5o+"Y=rKTn#,'6Kk2bgOk<
%#\C39I><EbO!Jc+P/Pgj5tNYB(J>$:UM7N3:*1b;FXgh5F5UmJOQ;&>"l@YCOf*bBi],&d60n\+oY=>6e8%*g$Rs+c6h];),r([D
%O^]3<7i'PiK2\bJ3*f'o,26[>,&9a0_1HE%YfhTk@9lUL$]M`"[8Yc,Sk9%K5_:Cc^F2Ml@aDH4C'FeiJN"$k=O7X'Mt<DB,;`E_
%965JOUB&==$Rq0qI2d?k!JRM@AH'8&!9"WhP<j@VR'K>m"G^GJ<,,kK,'-^CMk_p9XdWs7d%.._njp,,aYj/th-1P`%gXqfUo*s4
%nD6'?dIQVC,jH\`'?"e[Jr^L]AeK<SHW\J&C!qW,io?E$#c2Q[XK!KlL^`UJ/'`JnTQKRE;\]MTg7#=!LrH.f,BBoDT!5/74;^!L
%'"+ZIrdb]AB!mS;IpPG6LV_M(ar/54,V:>.+JKt;E(G7CUR%hpi@[L@Np6oef&kc35b?f\oTgbq-dasrY,KpV)6"RP`TMl)>!eo.
%<pshQRF%L!/r&ZSc_u60.DaV9ZNsR[l,sRfe^6p%K'=NVU.k7o'T8Hogr,WCr^7KImGqq2??6QC;97J5h3ocUjl1+NVJ*BT`E+_D
%6E5RQ`?cp:jG)TlP4'YOZLEDpUOm).?E_/AOkJ9Y=&7>1]Ko#PNN_`(XpSgG8l8A-Ki9')HYR[],c_$oTZBMRCMMiT/IGcET`hH!
%,*JORQC\b4kgc`](bp_\8[]s?ck%`KQu!C-D_;(O1kpo^eiGU\d>E[6Sc24g^sr[/:TpEM=Fd8K4KcQ*D+;uL6s9@ufaO(n-KF,?
%V3*DEi^!d5,TtNah'lZ]X6!#C#&%(=:i\15om80mbRdaI,EGBNojuE,U0-Wnb0$`"L\*@ijMri[e,ItNq'_YlO?8ui?9c,OIe2Xi
%m#/3J(Ph>2-PouWSsOJ?qS@QG@+/BDN(4$D.LSM"[-g(KK$'B$gi;SBgZu28_lrM]Y7D_Gh0o+cnEF0E!T*A2gO"DsIsC^eI!kt[
%r0Z%Y0h3Q\7j_ZM4-2dJS_!(#1a*qZK)b_pI^/i.5CI#_<T6[scC7/aZ*)``r_\>j7/g<MZa<]6jBMX=&(uHHI?==*p<Tb]4@j.V
%q,RZJ\f.30;Gcu,mAF\RLiWNQ&k7F_Tp"Vum_J/*P<3QA3//7"je3d(O^OAM)gfW)pZ'B_Vl;RC(l[H?U6W&ue0M#kF0)"\KsVZJ
%-B-YDYMX,*ml9uh4LtDIf-Yaig5*LfQCc4!ke8ZkEMd_"B>(N%ERY0cVHt'c_l:-*amH"*/m0I?-L]5WA4TQX/oh/SL8B^Dd0j`R
%H>VlAeSA[`3:VG]\c"A8B"b$4+qQ+LU=c."O";hg@M2IWOa:GQnu0egcf+k`;t")KdAON5l[<W,kQrV>^#b;#9N#g<PO84#[4.k/
%AY-g<*N.b$QD75SD,ps;ddi2$$guluC.XedW7$ijKY=TJFjqbaF1B!FeGF;IqD?`7h6e9[]cQ^<6P;UK0B?@J@;G:Hf(Uq7PNH$'
%hf7r[.33i*C9n7%&d3j2\[&UKT6cGsLQf,ZUD@j@]tNc2mu?;ie#3Gr"/3k[fOEF<9QH\>*Q2;3@4&H3s#L#N%*PD#kFMuWbdM.e
%SJjn"9+gNO53:-urlK@K!WHGB#_tYDjSPq-1]%#u?\ahX`IoZL2E:5I7>M8N\1"g=EU">@0%^ZMQVT]9J8jcSR-c424FnqUnBq[M
%#$#8+"W%jGHNX062&G$8@5&7s:^`CSM"7Lp.)8h5QoA,t6u^3(,/^TlJ197AOGQAufGgEN((Uc/rmSfeI=a8pLgX*,02hllCidos
%342n*:!srY@2E)9XG`a**jpb:L.Aa_l45*a!'kZ45j-$X^q_[L\V?l7RD]B7Mp=eFdD9M!:K%QVDZNq.K;B5hRdS>67CsMIeklKd
%qWRP#a,`Mh!7`M!S9d(HP>Eb&&kEKVn<3.keqZc'-ls(QEPhABR:M0OB9<[bn)E_:]Utj)7]iCr*Yb+k7;qFs#Sp_.V.\#D^s(P_
%04EcbpX`trIKQMd9;N!>K.;/>L1A6On(*X<X9<khDU&Ajo[X?!?3RR"TF?5>_hlc*"FXXJbo7G0Mp5_AVL^+-;dhrt%dOl,<61Tj
%R<I7<'r';0.'X>]A(>%`RaX!2Mh-/AVF!QM&`=oY`9^j$[Q(h&crBn)BD7L%le+Jg9\BLI!F*=i]7Wcp`0f+=lKAKb.QK`AGAT(>
%@>k7K*YrZO87Nc_BY=VlL/Jf=BS.thkq;eG0dJ?eO,^T06RQGc<f_]G2A2lmP2RijXJO:/!DZdU"^C.lPg%FC1+h!:a.T@E4d770
%7%-tt.iC=D[-*+t4Ig7:?.H[2)Z@27L29Ga0c:a2'?kMM?tN[*(5.gEQXG%m^S8]QY$nRgc1`]`^0S+F%5aI3E)KcP7>I@?McOZO
%Rt+5618`m6M5Fa6j-YB.XraBV>#aQSi5&^r%UFFdlHOK',b0'1M?@!V&kT[2;!h"$nV"G<U_iF5XH?XP>6UNg'^FZf4rMBXTA4m!
%T6un:g39UK/!A8DX\O#CaU\\PLJRQ/_M*$\EHo+t+%Q&,L&qD+MW4q7Y(34j]5<+sUYr+iC'Y(lR"LZS=Z4'0Vi#?.HefO?]dncm
%id>%=XVn%Kenpi?VO>ENOM1t"X/!&3[Nd?4Bb)5'kTW:qD#ch6S"aZX&]_H+/H\i`nCltX-8?<qr6d\Fapa:^==LWm+'lcO6ZS\f
%nqDX]P:2r;3j.kkbs:.i=,,9,S5+3`8V%9nSA0/l#KT88^jaL&fOgEXgl\oETO5&5)C#59)CFlHUB$Z#J]Gb(@C&9flOuZl@20dR
%Zq]bOs!]Qj7F:FOpF_V=Z!r0M`CBNj<DO=#>38TG>$R*'D:_I+Z1WaB,!\d?4*.XR'a<=DR@-Zo4-6nR2a^?lN"1lNiJr=CDcDgU
%9%/(9:D8'u:87i6@d)#a14ZT&!)042i7XtF/g',@W^4[#H0hc3JJ=6>Zh"tsTEFO,_"2dME:?AO#&TgY+AOd"W?'NTNj`b*Z$+O5
%gH/gLn_(@gD4eX*I+63))oa<h`j-Q!9QQ^6FW+<i>K:V+pZ+&aoQ8F.qR2>*>;0kG"!-j:332"Y1Z(!f$]`em$;B3.PVnbAZfJ^f
%DRXAGkkAcHk%QPDKoDifhSqP$rI,rI**TS[Vi-'WpBLB(<l_Y>1Z2`;e'\kPRq,PEgjVbY#_Z[IDQC2RMVCG+ds/K>ZP&'8cQ";"
%gRfAC8EoCgS5]ib"0#pHQqsld'E\/A0%6E)3>0@49kWVnSBfdsDJ*ujI\+gCSXD4tkbQNoPWt'0=de\jq&q7'a^,5jlZ4S0Lr3lu
%7tmmfAX7Ts=Y#L8,RJY"B2+c9e3%QdY&*V98%l6mkrtU@lJ)GT"sgCU=GpT"PB\&P/)DgKLLh6(]3'qd^<Aq/p=&m!X-BEp>I@e*
%XqQh1E#arfM(V?"\8/U+%$D:NVkF+.Fs,`*]Oj8f!dhrG?C2'f^iBM28@8PT:VjbEV*Na8!E\Y[h.H=K,`[Je(eDY)oRcOW<l2N1
%6!H><Z!>0.Gm-d/423D#^%SC>)K`E)7lGe>gu94DS#p)89.us)gA="M?fbsKPcfKtR)hcrG/Kon!^FJk&7P(Y&WDrq@pc,*,p&Gj
%M)oVN%@t9lJVW6rX\h>"Yj[$'^'1NJhJ=Y9$f8H'T_g5&X)^3)2okcJRp)_^e<78lpX@G$r</pF)[-?p!&!]J1L#8bCmp+pNc,$G
%O#meqmi8ieT#)^rQVHG$0jF_UT`OU%Xn"TV&>?&ilN[Ic(:5Ic_)6e5DO^.Ca+;sKm:\a2FCOIlD71Hr(%\l?!DtQRS;s=aEiOD+
%P/>uko)I,ceb")f-62&=Y.M8e%Gn%);IA1,o+JaP[saC+Kk]:W)fU-A)u/&(l,^r.l`)I0V\J8i?WMC(c\]2@IJ1_sbK&YBfbGgF
%.4D4U-(Vne);:BN@an[8<$-^[3F<of9?$&4h(<lCQlC"nCD/nC\<f79`MgUri'qkd(mAQB2]%]6AN):*Y1_)n7kK&V<'$Vdk8e_q
%B$Z',X$nRpA=TR:?EUQ)Q?;Z$()arH.^Ho_AWaUq/3WeaM^_pG+b+p/"dA>OcDgJOUo3Z^X]MBT/9#Ft7RhXY%qiuU!h5lCCCYQ=
%WS^Y_P$\9\%)nXas2;O39g)\aX5.`,iP]VD.F+a)5'klSlLsP6RRO;]@hW,j+.s(:bO.3g8G$3R8qmYBm):cPngM:((4.-RP)ur_
%PL6mEmfYSEEFsAF&i5OErWK[?(FS!s`n>C;#rk<mYQut9=e!]D]`EC;N5f#2*WV4)NLCpuS-H:,UPFa:6amqG;]C(mYo0`i$Tc)Z
%Ah7?=qKA?J[QGA_D/@5F(ojCX;]eTE-4=KFj/ed#(pPpP<>LH2+Q)Opgt%7Y)hO'EWInB+1$n6JNiC_80se-PKP[(#B&h<lm+j@u
%MT^V'@_:='K+an8R>kjlRuL8r0f.5m9/GfjiV]W,Uc\s=195EZ.5]a4";e(n"urt5D?"E!VZahE%OZ<C^p"DcUXlLcNAI=TZs'`\
%"\R;H:.F7r:eKJIhZEGI-"%QZ3^icEbd0j_$]Sr>AurB>lp]OX)5DaE33B0c*'LB,"Y$kh<C=%*SSZ=!AdE-PD)N^j^]]TDYfUK>
%j5"=;)$s"kG8%(7s"Fr+Aohe^Yj:9sfl]H!/,(W>akD`\brCnh"1T;:$Y(&h_e,DV\O8-9c(^0NMTKJS(ecX@R2NL_.mXE\U86\f
%p0k7YTifb\(A#&gon2a&=6j9N)Aa\U'sJX:B5faIG9$Ki3`r@M\!q7l/ei&CZY38^;R)dh?W`7`*2"1$kIkSF2/[LfRNKgI`=$XT
%1J@,5nmjW'k-on?]_6K9HEj_^Y]g/dYSrFIU*=0Q=hh@]Wd`1(SSciVUuQ(AV@g&B=]u$k!Psb+mHEmj_P[EhA:fML"=',%%8]]R
%jObFH<Q@HU7tpTq^'jn%,Z@mS0g-^jd^:+P$L#NC(]%c\;C]trWFr!3L@HhcrV\V-$+J+fo60D@"`Z@G*&f^rdB<-Hp&\3'@JK%(
%9ki??C2H\]E<92`'>kR/Z(E&iC;P5BrS_7&FP)FDD]_Ou7'MncCml\,hMn!+2VXd;+(<SV!h?>(`gmXq9G^Q;ASc:e(I7f9^bJd.
%<]HR-5r,f!,BY3,2OcXRn&DJT'/Wb.LOIWB_:i__*8oD^3T48O)4WU%;M"\`qI$?Wp&..Cm$iIXGP_PH8XBkP^"_a7Z^X!#>HHT3
%l4PBF-&e33&t$31qmRG&`AF0.7(UJo9bPiii(V:)#i%)NHQcr&]:U<H^3+Dc$aX0.*mW'^``0%9)J^9*B,ZBX<*sd?7E^Rr/6?W?
%CuS+sj#YS)Uf9GDB$sPK^kX<r`_`L[9>I3.(H*TRB@<_s:8?cVgo#NYEGiS:g`6)GOjshT46;lqpNV+N-1"]Nf<9+ZSN9F2Q>Ygo
%ltGdHUsa*<EDF\sfJl7><elZo'DJ6'`*un`ZoHZ#[^C)b\4ad*$TdaAR7&;JUjD,0Sl`)A3r8ftbtQ?,ZuX\[mqCus-'$_%*dKfN
%[l?b82^_NNYJBi;PL1d+cF^0?Re,4b7qb(S2rN8E2t3LjpAMq#Cn)cbg`Q?[e/)?b^f7eL*0E7e8&@f(TK?R-C$)s#+lY.b.Y??W
%$_d+2JeGD%n"_EE8+5QC)S/[Sj(bt8mO$a1baCn?'f^.WKrSI3N)No0Z8`.&P0CnGcop^]>agpf>ldD%jY3[O[\)X&=/iP'DUj&%
%3kNhHO4K;<D*g?6Z.nVYcrf=>2Ib8A]N@L'3R$;Z17Jm2CbXcI+[58VeUd-X^bM\%mkDQA`l]QnbKaprg_,VSM,<7(Fi1,S)I<)\
%[4sa*)q('"EWp6Bq]_WIc&/._Ys$J$%Ue0ai5R+jG0<b6$c0m_PfRtti7Q084k%5=UC\bsFA?94g3Vh$'Zqje1/i&G\0=@OmMoGX
%oJV8Dk<RMRcY(7,e=Y4K19g.+eF>.u1M\VJ3X44OD8>if1g\cpT:1lcn#amuCF%krJRbR?e]@7%Gm;2'gHlZRUMO;8GrAL.NA6]g
%f7FNg.]#R$MnA<*!-bS:"7HG4V'Zai(q_^0#f^McG_9RR5FAmj2ks[s1iM;Kg5sA5B]^^W@p=-5E6<^;5/24`Cl;#q/n(*%Nb!P>
%R`&CZUE/8<@2Dgh/Q4-(J@,L^SS;Rj[uu<U"J8%DIX&,o0bkY7F`ep;CFIfK_GEp@du$!l)R[s'6R*OB3pnpuTC#DGZXsD)KtFWt
%\qTbn3o"GJS4g9#0rr09^#;@W)7rQM^!#%I0Pn,6*Ue'[OFLk(l_P)23F3muq)UG"W>O+K]^gqWf^G,E+g'nSnh&=JQjT6^jEM[^
%K9*I,@`a_9%s>"I&sB$3?2Hh$D?!:SYp#jhI'[B",;n:oY<q'Sf\kNT2>,pGjR$pC0&Q6HH?))m^l*2SHk^'@4iWgJ+#dAaT!#XH
%@T#4M*9Io1W9ilhZbpbkd<(7`_!/qri-%:#Fa2T0jI/!bkh%OXj>G*d`U]A?dR<=+gp\KL=JK)_UhKDPXq9=B2A/h0PQ$]S<7F3Q
%,?pFga`iSP(i!<5Vn-LgiI,r;am-c+(V5Wl(TYf&8n8Cf:;"ZTY=GoU8uB#-S'k.s53BDj_)>K*JTlb*>cR<!DlNe-o:[GqogO`^
%CRK%=Z\lu=q,j9W3H]dq\j<lO</;0tDI5=<laC\':c:6XWKZhKaYUGb,8bY9`u(XJE?"k8h8X[VaaN!`&\(l%`n_Kc\(c^RQM13s
%"@HhXiF^76dsYK[2.uducD7:k'D1-.H7I`YCq4^)Y-(@D'(+Wi$)guRA>ou'F9;(#fJhb^%hJ.ioc)5e__\^HdB,_KCM^0s0VNE/
%L2?ZPGMC<jD;.-7):;7<j<U;>Q9akOKoEWl<Zu21h<g=AB=mIT$9$TOh9fs=Vf(8I@qmd9HnuZVA%NhoU`/sO31Kb_1F(<nE<;Y2
%md[)Fb(nY"ZPs=ln91s/lL]NLDK/(8ltSihpo_o`3.@TQ_9T0^1ICX)W7rY<e&N+c[L*[$(G[(:(&GHU2k'^WlHuCE/XrUJnrB6-
%RP^f,k?oRKisD6GIrj;*eC3/UR'hlQG12RM?#Q<<(X5:PR@,"5lEq'b%\R-C+,dZ6o=3&qm=QB1YJr[FH7$hk`1/o%4/X9eEnE:h
%.H:QFh.=&#p[%c(/:(&*6;DPXMAApFZj@dmcI?PS<CcU'?e<&*24A3PL;%EdV!r)dq+#V#g++I@q2X!O`NF-rWI;YV%Ii6I%H-+9
%%H%QE2rHdL2dh!a[gqh2ht@Sm?JV1<>HcW'oo3h@\\94ecee=(i!J!o\!/,h2o53jDh<[$H]?GGbq0T9MKuP=V&[+]!Bf?;#(5n9
%#&rc%#&G7;8%/,f*4rocgq1:CE9I)+Haq]^*4`eW[3Ce$>i1)#?/GYM?/GXRE.(*Gmp4fNmTn^(@OGF<JF?e$R'USkqUWKRleCi9
%]"[]W44cDjm"F[Dqe,*gk*C&SqM%[I-X>Prk\TS]L-/Htf/L2"PHJhU+1])dgq1:CE9mA/H.H6X!:F.M!$8bd!T(H&Z!'6@2h38\
%hjiq,D)u]tk%!"%JB+g2JG63bkf(7$>6fATDnCehqW:HbDTgZ.p4V`NCUS;4!uOgA-X>QU3IB@:J:#m,Nl/-f)b4r3qM#tsFT`@0
%=hF3glPgJ%m9STLmTn_;dd5WZ%J\fQbq824orWj.f=0SRRLjP^a0'@XIPBA4cA%nU>>O(`#>_nEEIZcT^1"#0?/N.p*4rrDSD(Th
%!Sk<$F`?O?[X$M1hZ3(Mhfu(@]>!eLhmpJk!8Y3"Fb/M9lPkO>FS,sBN8Y+i>Bn1lRGI;[od;L0O0g[Pn<RSNPZDBn!hOh>a'+lW
%T@V-jI\`7F"Q+!+%,>W0%,Yi3[Y:Y\B)=X>arJr+od=fJ\tWbRndM,>b"R4DQ[5HmfaUd0b06X6[5-KV[p5-mUfPLnX!8J1Dl8jT
%-0/A_F,\^'*?8d)Q?#;B@lhrL<^uR'Xc/dh7p6e/=ANUA"@(>W/EMOc7_hr+)9BF:XUH!7?7TODq,+VleB/ckHB=F+=rGh!YE_D=
%:M==*E^D:/T%IgiG29UCO0g\6BQUB#DWnZ4Afg-<Q'l8Qf?+r?e]R5*Q"af?%m?bS3I4r<1<4WVor'2aS!=XjRA0sa/$nUO<O?SS
%D$!LIAM>6RXUKr%YEcBYiEe%@SJ7i1a'+n+WMs'hh&Pd=LEjIc2<%^g1Y8Ae6r;uTXc+:H`5^p"==^`D[10k%b04?MfANH*Afg$9
%Q'nO;S4&@#>+)-GV:l`t0:]gRBk7_0BOr.rr5%ttXq"4*-:GEXA/0:KO!#K<H1OO^14*X#bVCu:%93WsQ'lu8pM"oL"DQbu>-H/l
%amtLgNHRdg0N4%1Tq7Au[jm]Tm9<Mq#WQO2MXB7XFmVl-oP7!adOjjP^dQ"9!1\:X&$'>=9jKhZliLYH)H&5_E7omu0!8?Xd'YYK
%E73%UGfle3Ufcu35l)+he"3p&rK]"EBkIS@Q5aG(9'BZPHZ?ns44>uZ$/"_!,`Mo7nXNEM:/+);dkeTA3/jZdg,1BnA:-a.HBq-S
%RGZ#PU-X4UC>*?6b/f^oPr#FZ,?VNUM.Lj))s'c@Qk+:al3""dpR6hWVD.$a#Ad;`'I@5h^=;S9cpt#">H28I0MkD=Hf=s4d_PBq
%H?*"KXef?fDPen1.!nI,(fcthd(-$C).;7`AnJ^AAuZJ`#\^^+Wl[XSV9l$-WD'c']^'1ZMhRhg>H3GMFE"a%:^]9J>f[QeD!7D]
%\.EW-c%tCFdFrd*9MsYMegoHZW/d&jDNlrr/r.k7f673h$n5R$QXT"o7V8K.<cM]o'ATuOiaA"Y`EhFHb@Fa&E*?0l+$H]#$<!s>
%B>d02p7[kDA9<^ITpu]:KrPTBnH:a!B4ee,B;A?C#Lgp3b;,)tQus$5F'6JURjem@8O%(:PjS^F'Jm]`#>;(.;p5qKk9o37'/GE6
%@%hDfA'kL&]hD;;0ZeUj<@sLT]7YMe#O5p1&#WBKg3()&23m=,%j\-Tc*4@;WQ``qYj5k4E7W2@4E\>rjbgo12n_n]YcHrCRi7uG
%"`#$&R;1`cJ>R(L\#!4\,DdV2AbWG(AhCS+QZ9m(foa=SRhh6g7V`ogRA'^$4?CsDoJ`M]Z!JdOFPCfu19HQp2=4YGSW6F5BnONR
%aBn,R:Wt8>)eVr:KjrlbXcfQOD<gkHQI?8&3u>D04$N).Pe+>X'](\3L2N8%riFk/Qn!7+B[ElAUN9lgRD;N2^k<6<eWRsMe=!qJ
%T!!F[e[!idX/R?^F'qMf7(LHZS.:SIZ2)%D>cf.;iMtT91%cQ9K<$W6D?r4[D;17,VmnQ*8E5$1'Rer9,Z8JESLIVfCo\`CpdDZ]
%(W-I;.Pg?'geb-hh[:9p9dV6+#ZFI/fs<WK>ipg>E#^s--a.6F(s'tfZ3L?6DR7qOK*?f:YA)!1ZGkX8H@T5'km?fbdsA4#EV>i5
%6kBT:p6t^R:menmh6ctf9bP-bCYWXdaB!CFGFC,^Xj]+P80JL\S>HD%3pVNE2)LddH'f%q7'u&]eT@O.6]m7bm0\#`@WH;i-i8gE
%den'liLc"XjNhtp]K-2-WPWDTXrHY+<F"';p$0u%dquj^KP]Pj&sX))_F"qT6YNe)L3_<mX$-,*e2fXML/l"7GsoFgJXabAn7a5M
%[:;;])8UR15RR*`DHBaS$Y6>#[7T^#0.l-QCU[D@gprA!0?<$,X_B;0qb"-K`3[tAIHG=8?)@k:Fd\ij/eb9pDr*,TZ_nj]Y*,\@
%fPWlXVL=F#[4@pQRp"04E&9`HAl#[:m^r(c9]u*ZYBG"`:=J'HHard<BQiq%O]O)Fk]AqI5t\K4oE9'FJd4K0VL<XbZb0*a5!&J8
%C>K)FiABqh)0_Ao`9]Ij-_%NECNN@e9(md0+i8Wm9nJeM`iS'A+_Dm%/V\5@f=HqU`dBZ@hDl5=Nd[ebF;((DF4"51k4o^VF)Wr,
%jAs_Wfu?(NH"Q5"/_;#4GsaSQ@2nOf^aJI'.!;i@N@N-fYdqL(>S\nfA)7<^P2Pg'O`&=rD'[r+\!85OMNDhdcM:qie(-SCQR2)=
%P*j(1gZ07-b"*3&jf),B,^B,;j_]Z-T=XMM,W\h^lpO(i:U2Q2JOZO'p5p6Mg?6sLO/pf#1$rgPlu5,,dqf07pjfBT(DI;"/o;OP
%L`Pks[Xnonj(&=]^l9(>_O""X5k<F\AKcVQnb?_4X:MpY#a4q"pP,JkR)c\-d;N6)8*g#g*?Im_2jls&8\Nf%@_^Z9G90>?g\*^)
%[l9s1\fG;_2g18h+%q%(G"%i.+\P%)2Kk1Yp"PRfF#5P)Gt*U050_Pk/DbJ2WJ(2';ffCn`Vid%\7Uge`iH)jVi!/=gTJ\%olL$(
%jjXgqd21#'[-P++giW(>Nt#!.csu.&?L51H:BiilD8Yn6(epK3%r!rr;c?Y,mc;mI5f=mpBKJ]:rNI>cE5%eO-M*bkO*]hcrt3I6
%A"Ju*E9ab=DB1j)3fV(V_04K-U`(I?M*YW(+IE#RUmsMY4/%Da4WEQrI(RF:cg?^3/qe@M,BmZ0!F.#]8jOZUg&_mnqRDCupd<LH
%M6/FJ9ljmC2BR)pI*)V<1a^Dt0iD=,oD82\]iX>eXdae0)n@Gf'uKJ)ILLeVE!;Bf"5p]!EC1@TF)UYL*YgosgH:;#!02E\"E'J3
%*M52#9@dRMHWtI?\F$s=<@9sB]]<\D:8-r:jTun>0,:"3:SI&;*u2*"I.T<+?UrZ/.!fL'!'Wa;QYP%kU%o_`G0YV.7uf@&/gnK^
%nb=Z_qkLEtc2)%4;^^OWJYR..8N`G&`EIX%%QgR9/*.&7LOnDfBbD-dB\QhDFE[_oh!C7Ob*JNfrO/P7n(>VpC;<L!ol)#ufli/R
%)9J`s2L*B=U%9UIY$a<4_X8IXP;;LmEAiG.L"KW@o0(A77bD#-C__[",%p4YiqqS_><fBD:JPK.#GT8NqcTS+#K&@pWWhsYCr\:b
%m!1\X.Y\j$oOQf^\",QiY="n\iPNZ_na?3`1!R`u^%.6R(1Z#t;Y6#]fek,p"U!IB]/MeYB'?07F5X"(6^QAE$uYok_?Y@DF+%Nm
%@Cg-#G&<d!CX'Ht,Bf)2@4(#i?]M%^V6o#RcbpBLI2PYW:pqY9FHP4X*4`U^?hS&s<5=leis]e.8:Ec\qdg$r*lc1BLZ>@JR6>b(
%R="*lUTW!1];_4B/,a-r.Sn&F2;peSCg"%'P/5?H6[.spG%8k(7,;tJ%V+39TWZm@PqX^Jq]r%A$of.u^XJcQGb<:akt5mP\>L_+
%7=u'.+L;:C9^:tt9QA&#lNFXjG1Z7Eg"g+(a.2K+ff&RpD47ncan2<48+QbQ/=^E%"ith`PmO'F:[:jqC:D+8;Q%_@i>o`mVm!(C
%KY"uAW.@PPEEjk!F#IuTP98bnDecK+SF8)"fG)NKe^g/0J-A!B\7-2rV2'T2?mkgc@-?b"MQ,cNq(J2l!O>Q+WPoWll,d_jY7jhq
%[c?a\&f17aC4"iF282#c4s']J8;E6oFr#M?phNf;#2/FUo%'-k(4Z_<Mk<#NMVieeRuEDt*pJJD-9nKq:eZoN8TB3\[^e$g1t>#%
%(j2h%,%(91lV1[SaM'8TG#K2G2Ka"pq7fE?l*Vbf&R)A/#Og<18.i5]4c,eA]&ugI$*hFu":P5lf/o.Q6i&D0@&IB'ZcW4AA3\cF
%>*"l$Inlm/4FJ0W^UAGQ_Ym/cX)gW^kfY_NPGS"9=3Gd0GYjljDSW/*H`,guI+,^$+&:&n`%TiKmB?>rG6:%:iLJ;FXkk?3s6R0!
%IC$nkm=(VhDYRPh=jS,[O#,f2h"fk&=nZpkdU-n$):Q^mGJ.-t]2A(h.'1baCTiHA`njj?&*,+-<U2s4juHRQ)RVT9I]E"0>W5Hf
%rA?emqD:D/=Z-4_At>6iF)l^c^X^'b^UAISSCE1!1h>>[<dN<"nS(0dU7ZoU2`"n@*9MNWh2nb=H(IOSYhcMKg^X0l1L^9jbS7+V
%\#7,+O/usBYPg!*XaZcXhtq<a2_iLij_cs@GJ.-t]2A(h-lhVTQqd/5,XE&_8#N<0"$FT:YCG?!VXmaPCtb,lcY<@nOEu#Kapd.>
%90[;if?I6&>LM<0q39\m(/hOao02'nf'@m?]R)1OFWHQ_FBZiNCmq+@\#8Bd<:ek-pj(>25\(Lnc0:'kfY"91mi"&%:ZYkRH/Ec%
%\'4R+N;FAHme`@Br]A?\mii.2m%73$([HP2id<cCaG-0N%<4m9^mN[)]/Oe\9Y:,"ouu<X`6W/o'p3ef'oZ:tn!`D<>bghRNPQk`
%m,u)t$+>gVmQl20S/u0eoBn?J0.h'Rg%]X46nN/BYN+Ep4B6SOeU?$]Z!"LaV/G0jk?irAI'm-R-2Du@IGZgCO#-3.bdd+K:AsW8
%HbYIiHa8,rNEiR^Ok]62^\Q"/rPS*\H2$b+rQB8-hq?geL]8rn5J6UEmHrH_X`m#!n`-tQqhr(UTA?#cG'3>5kEessT"TRI7sQX-
%hYH*lIe2&orEk'IoKo2Ba%u^$:JYotmcS59f2@js@;QR^OJZ"Zi4O?uPDccJO3[1^0)m3CDG"Z)Y:e-S*hT'1?/Bu.Mn@.[s)RXp
%QMF>IIm<b@G'N??_f,QlrNQ:FrU+;W:EG".rRpQQJ1R=BpWM<\O7+]>maiX(4Absh[4sW32a@,M?TuGQ$buT6lM:;V^\[<30$`p8
%noo#cqrIIWMo0mcc_#W*puq),m.6.I_lrL">hm$k9j\t:mA%"gX38iXcCTK)H*35-H6%cb%cQpLro)g;IlL[45(<.k_ZM>tr^$P;
%+.RS?Ac2Jkm/@rR&FkBKDP,fKffmS7:Agj#kSM:cC0Q(7q4Fbgs8;eQl"cSn&)#:\H2ak6Fio*:q<aq1cE=lD4?,Ge^UsD[S`'2u
%b;fHiT3pF2SR!6Whd9XCj#Rpa&$l]BjSb#.>?M\j=Jrs:N;D8TLH:^Ol*m3Shf;VDh\5fbj(5Oa]1fGOJKK>^F)uMQn`18=3S8hf
%mVD82Y`j?Kp40lkq;8&Qe#;VK`!=oO*0M8+PIg[g(&P`BpuK*8"s(r+CXt-lrb+MYpigmHR!O5;l_EPIa.Jla!rTGFrdr+J-;J?K
%9jSG-T6'o8TA4X]7pf2IZ/,!L62k'u2>)1j=8)'lDgYgiCJCH#qat2?SsNB+n'8Lr)ufTsh`SJThX'd`#:1H9qqSr\Qe-YXY;4Z>
%f&uqZb8t%cg``If<Pke?Xc;5?g[!nsYHBM/HbqlEEc_,u)gi:/n?!5(Y+B!-hOND/*<,orlK\)(lRDYT0@q4FKWTIT4dW:N5Q/m&
%gZtccUHcN+=uRcBYaui$2[;N5ph_&_qTM0qrn6?/Zh#ibiFhVM4hEhK?Zg_ejS@p9Mk48Z_o"PXTAA><kaaA*ebqSEI]0Ma9L"#B
%ILsiI@h6m<T<>lM&R?KmYl9(:e*+]I0tC(iNr%)uG?Y#P*:]2JQ+DFilbEDp=b!d#dJSM"eh>2BG>7D(IXQKkKf9.amcmktO72OT
%^Z;'52hQ7SDsooQ6g;^=VZ,V!]/[I0rbA!!LUZ-TI.m3tlmmO<*<+5:euAJe`UDD&2F%#7%XVi[f8"i:]Z#":75hglXC^17I`3hU
%(AcXh]1ZBrn6NKkhS3=m';NZVn6.##puLEAL[D9`O2$&Aip"G,P@qYAftI_R`m)IY?ARHH^Zc(bW3;.1V;VFJ/k[25H9n]"I&Oko
%cD5um\@Nq2%"ok-d$fm-ro(?Tb1lQ=55b+)q=K0f^]*QWeFDSgdG[=hdiG%XAGu?<\O<Y$INem'G5"3Aeli"'<3$hbIbRlRE9jKu
%[c?-AitlfsrH_]A592GQ%bp*S?UsQjXX/G6gABT\q$p"6"!R1G-i22jkKKe.Vk+j(]X[N>h\'3d?e^g[GhKcDSi*-O%`Nss<<Rr1
%pD-03`-E,eY\lKnLFptJ;W>jKHR8:MoN6UH>OAVCs8-FFpEKH_EP0!U9DS#Ap=(43A6JXtfe\i[UY$Jr^T7dPmD^ZGX8KTPL:dFj
%"o#IA(4oFV_<sF(Z<oi'6!)Zfh:t4`-U)L4Zcq1(])DT6Hf3#-l`84p^L1dSGXT6R7o]8!*DsE5GochQ`;(HL*8+e4_u0U2SF:/V
%Pu[LJg+NVgC%\MC9qmgl!"6S'L#2]#<l&";[9;,9PKSl,kX/cq*]1P5G,<0)T?FFqHZ.Ai]dbp)W6>1bZ\tISiSs!$:S.K,-V"&S
%?@7ZV@Ct$9?_!]4dCh^1@u`n5)`pR\9D?j..^$KDUO@09rS+3-qLLSV=3rP"\/q$nb(l&iO1dpf@DT'b`AY1l;;rA<.G(q%%>id;
%(*3;NoZdtpFaJfM@K"HsrcE/Z>UmWE;8f`rlU+OcYJT7`aEYIJI<Aj\b\cr)R!7HYNqf/eH*;J_I[AX#Z;nq<b-19ZDP$h+p?qeP
%*nS1'g8f1[DRt-Q_gSWucZXMRH=[TXFnt,P\\6sG>Z1U9Q/f&ds)@M3^\dg*fbV<6]g'-Dpt[=`Bs>$WgOj;-T%mXZrg=dBc3?5R
%Y92Ra]i91Zd@Y2Ocd'k>j:rJTkNfrn^Y+#/o<\&cgUlL'rH#00hmMIQ-d)8JPN?INhS8j`mQrE?[E;8uDa(nKZo[Shr6LiNpVu7*
%%khiRG+EYlA$2-6B7EVbh!;2(irXLNFF/4QEVE/`r:Kt'm,b.sk2tL?lVf>bE1=DOH[,(YMm$,]kpdFu;O[8UH0Cu#5&;=>n`?+8
%*trd&CX9b5gAH25rHN5Y-nXkqRE&[)4h:OOO&FtQ^"Qe&ouKTAqq&LF^#1;cs6fUF_'\ue4ikK"[/M8s;,VA$/`c0L3GlD,^j:$E
%pZ4jIr9W_2M$[:3jGVd)5AuZK>hn.R-+^!eQPA,14nI@Wo=I`5kKiSknl'c0mmtLmh$3f1q:4:om`5>:2s4OT5(!!.h!Ub+F+2o=
%J)$d5NUfh8Y=(hV0S\Y^`G<25A46k4]^DMC:85I4?F3bgnsXFX+$Y5Kak>/9e*il;L]6,M2tn[N_!CX+=gup/LQA$6h-Y1YJ%3/c
%o+r<qX55toY_$LH)2"t<&mg%-O3[*A+5^B%iVgBHh<k)\RR1N3Vk0F7T05),^cV!j>-[N[Ie:G]9,=/R-[>#Cr;HJas&&dop<sQO
%r93^LnLaS3Zhgk4hS7C_mD7][O,*bms!.K!;ZD'pkPCt(8Cg^^22F<+\XYh]>+Dhc3CuIge7%1(7F#!1c]:`eoj;`X]p;<orOCq&
%^$4t6lMRQ&Q1+$"okKJYest<@YCEb)'E8((L\65kO5e`?b<P;[g$sjg:^Moe2sUB-(;CqmlA:f&fk\2tdgr]]IrM%1RU9@,?M_P2
%+"MfKcL1_&DbH-EYFf-\gX6HE(S?*,g[GTAd8"k;\)[AGFoT,SqIY.Z^$J7#h1LT&:q<\-aF7+!+(;KZs'<eehKT0Ir9WRf'\`*%
%0d7^3?!Z7C?a+AhJ+gR/GjiEBakqMo:8L];nnZA)kKu:9PU.,j`"ue=/2*O/ng_,M7n:K>hNrP5ld8M/5CE+Pp!8,Ss8/.T/l#dL
%V&Z?[^=Ds!%GG%dp;Q.l`Mh@3e)RNFEP;5CdU'BVnC(0^l41R2s*=(2]s\GWaI[G@.d\Fn[MKi_p_c83M^qN5YiW)Wr#lrFP&ubl
%5H`7teo/04qi/;1YDip5fWO>cOt&YseI"%5'pCPj[jc+mNKk5G%L#qq(aO+Yg4u`UT$^IsLsN8gN/<P-RtUcih9Y'BZBcr0\\Ip=
%?T[nnT<:>iH/JT_HXdCu3=WqN'9AA""'jl-Xphgr1'4tgQh<T<6c((#]X(0ceg^@M:NP1$E:ZM:?Z*_t^&G8>cdo2Eio.#G/=7B%
%=[L%KCHEOXcJj\A(1nFt+(_D*[R57Gok>!9Jf`/;=hWBDZAMM10qR]uLY:'>kI%.*1Lf6NS1=@eXMq'J)oBj$]glRqFA#0f/s?]M
%8gR&d=.."t$FG.c$s]-7he"bVjfPq=n#%>q)RoNWb]+^am6YM1EY>O=><?di"4g54M'fW8]3sBFh04:7(<:R/DCZ3<Tp=V*;'QCm
%,pP4A#kP9`224XAWTj8_?i2b(\GP[:[bm#j1h#7&JT?qKfL`)@a[:m'A/A3N<Us0i)f'(l617CE]-_C0ZhK^F'DB9/Q2i41nr(j`
%bG#C".qA-:F=n4j<NJBsDNVY?IrbImm5KFCa7##ZU7VdB=YSfil_a\6:>Lp-0hc@9YiQsRC7UAfgoH!e%nKaq>K0Nda"L"=+[u?:
%ZI_;?%"[ua\@]DfY]08.OVgNLqFm"FfX1CE>>Y8G\(>7OLL77"3.,gkpZ8a!VlSS^ZAVN9If"rF6JqDm[FRWhs7ZBdou52SqlAK^
%"qUl-rim&6"mG*6<OTPbo&7RVn,N(IlFG;c]^sm=55_6^)c$EX]c[1:qW_[@i5++FrS?RJcgNrFa?/kIh4<eO5>3fhdp&^4cG-m%
%H?+4LUJG"mO*rV[G<\&^hYP!*T)J'fB6V]$Bo5BJTDc[WZhWP5$Q^#F=<fgdfTZ@1\("D:[l5*Vdn<cOk?hOXH)Y\e8&*B,qs>?t
%g#^JB[DMoS)S+.]`u"Kb6u-,'U%@=%kG7n@S`7Du;dQUW/qo2HV,kUZN:^J*,5gPl_Z0VQbARdZm-?CbV4HeqTAAC2^BWHM/"NF7
%qq7Ac$06bFfAXp!e/r/UX;-';lLVKmPb7pRO0?CtIKOcaB-PLtc&@'eOr&0'*oJ\fp)br[8A+dlN4V:L>3t*;Vuu(+jo;4?I;s9&
%q%k]l`UlZ-M["oSCtYFeDR$o&m<OeJA)%H]o[QSll)WF38_pWD_Y/A#HbYFSE_$)Yh;rt7IAu?Ls6-kQ,d9^$^3tHSIds#.4MJ;\
%no*WUq!g"e&^o!Dn#juC[,l-#oCnSQ(LR(3G%SsZ^D8?8SbRN[_#(IMcS+]fR7d*8cTZURV4)]-UVA6P_Z+`QccaOnCPe=&FF,;g
%>;+BDoAd$nr:oCpo((rBipQ*ur$>>ioAaa[$PE1-oP?,Lr8isJm13N]<k<3cjmV/0n<0P+*e$I[GB<Z/?a^TV5Bq%jmr(C]Ib7<f
%G,KgAWc\.1rpopVcYs28XX^d9j'R^j>[Q9YdDtN6=/LE/b5BR5C+O\1<B34ec:EQnhB0>bBcZp1:CeP=h0f5"m.6.Qa1"j:5IH3V
%hjgd$o@p=XrH<4)Fnoj7?(X:WdVe83T',@kP:'jf1lhFsX]9H;eNJ2(q/A1Rh%("a5/4iqHuepsh+l4WmN6mZEfPb\^E(VfmQ\;u
%^VFpDNXl%2Okr#Mqs\6n]5s(@!\\pZC^U0]LW9GR#/PdN4;BC1iS!>co;f]s2>2!7A3M0gG@!I9+91r[DN7#j&O0Ma]=ei`lY#4>
%Aisi^I/;$Ug[d(:TAQiKhtFn\05SnVp>ouY2tkPHm=&!SO7+]m?/5D;ipc8l5M[E1p>kfDm'Fm9bBoEfI#1M_-/55EFE[82ZY,=h
%2nel+%,_?/671H[3.^(-"DfU_Jp;k#6e+oYh>$k3VhK>0'a\g7rPd#BU8[hb@Bot1Tb3A=pup_j(O[oGkRX:jXJ>K:RK_)UIe'Q&
%pJ9C,qM$)//dGbHB):[Ts78JRr:.DYD>O"d9fd3$n(ukBIe0$>hEQs@%U2TuYA^&YL?S#kT87)nrjp,Zro35:Za6]./tP8AH8_cc
%%Jf%Vs4cM[e`G8tYA],2*japPo#anT@3Nr[HMmP,)qHqMqfgp]rT`n/g%gu?'Q/s).6dBnB,?=6Y%>(ir;<nYhd/IV38+i;i!24(
%=3oLdrp<2tgI.R%NJ5Bi:K+H5Vd$9mg%PHTpRTLL/4l?2-fWn`:3b7Ei.Uh1&5HQKK<W5:R5n?`R8r(i(a5H<rAJ+DG%T_S<*^h$
%m?:ZV:$+NDe>Q]?D@RLT+]g)^^3aL..ViDJ4PQ7.;gYh60E9i*gp+DWr6+<$:S(@Bm.H<2mbmAL@(l/-gb7O?5"r7eTS_Xn8kb-1
%+GIQf9`!(ia_1&:2kr@g62mu`_O9<[bp_^=gR*5;8U_Sc0c?K1a8E'I=FF\o?&T9R:Nbq0F]WCp\0BMq^0g@K:0o%7_D't\EITq8
%R$E/F1&`,(r]!`@JftX(DBS\GOt)t.n6;2`<;k<a"To=T*/5^CA'b9eQ:^t:*h%[#nsE'&3S,[Gp0H%/+/6g+T%PFHDpr"GCk"3]
%7.Tl0qPr!`X>d[9"RG/YgR2@>-HBVPLG*jPV.Iu/#FV3N8^CJmIjYK-g#i%)[0GqKY]t>Rs2Q?&WAYfRjc$*,lnsN3Yn&fP2i.7_
%7eFAQc:b;VpHYn1+'"+a(OLVo*EYn!AW@MOf/Na1PHh'+8O3hg+MKi@%]9@*FET^.reC,j+ugkQ2@`2rEQ<g:-_'#f1EtCgb_26T
%_`a+K/@H3*jE;P27g%76OQ2h*4,IU3laY_K+3\br/WbN55!uC-O:IV86i5_cXG,e87t$BC)gInSQbK[G`ipOUF(UWT^pJfC6H<W<
%i+Mu-'(n)Qcg57"SQB2GLfWL!KcK4&j;[Jql)>i+HA6sNe92i87]+VlT**mN7:jg%.P/.LOIo"XpStn?:n@U;p5mX.l&=e>7t#fs
%@V,(bV>A,+0*,XFGOmGSC4G_+pt(b4IO@Nkp&=5+4:4pWNBE8?+OSj^nq?IVZdLY?jZa"9'jcpNq'-j8AS3!f-AdcOT*,!/2\Y6]
%SON!FQ=q8e_q^rp`!\j1Jcp/uHT7!;#7aSlrLO<"<R`"NY'*tHLJ^A;J@kp>,mJP@3=N+,6hq_9Ap2&CZ`g&C6:2'fY,=M],GDkZ
%^i;Q)#Q;b^DTEGd;<EMAIL>[=5BIiC/E1fug9<r(gng8C5o:gd"&sN+ASQ9oJF8[HLH#=Y(*;Cmlo2
%i[m*Q]luAL2OTrpmXZ-Rl,8]V1T+2O1iLf%\16(.!P#6f*ZgYDYfi<Vr1o@"rt7:*nH5i>I-U0po3Vu_IX6HmJAC(7TApsgZi9fE
%^7Tk`)1\HF:icseNWs@u1U]fhg%A[0_2/#H^Tf2.f'crrs(Hg]N;7'gKD3*iSW1BWMD3ZH!f]Sq[XVGq[iKIP5hkFs8p"bXla&aY
%(Zi!nT.FiX4.VNpmdpbM_&4$`?@Xhf7+ZU8SQFaY-B$jRpKI3Y=\3J8gKAHD5:u["hfiBGOROn?EL?W-M?i3"YPi6Xp==XV\suo9
%jo=L^4-"f$ebUb837i=/4*^=KIm':dF4oC/([-?PEjPe7oT`1)H$Uic"ibakmLP'9.ZJZGY0]`.60%p,.g1A'op-!`J[4[Rs#oJh
%VUo$gIW.G(gQm5hJq>p]o^gkW%B\31j$kYI($O,E?d?6k..j6\]ulP`G,BCdXnl*7`I?O8LQ78-qUKNYEHK<aAc7MW?-`gh69j-\
%aCMjHn#k#n;O^C`.5RBe\,<quY)bBq.uHZ%s%c8#2LGu2WWf"EJho8$Js,k&h1(nlCC"Che+lpk)U8C!:;%hqb4qa9/1S;>&?ZQ=
%dJNU^jF:a-9CffqPPW?,P;g)gM0sg4pV`Dra&FFW0l?<JJ^ebqSZVY'>:n1$'W57"bPq<WXg&Oa59o@+RqE6M]B!2]f0/SCNL@e_
%C65^`E3TQVFg!hLm*hT/f#-Tpc0`09@C&2Z&LY6kJH<[h2qRk]Xp07^Ot\[cGGXo">a@/;4o4i19>^`O@."5G3DKa\qE;F622S"j
%P9\g4SPW&WK?[r6pdqR>P:X:%KW@`MZL?GrY,_J3R7ATJFm72K45%n@X/g:S732=.P<^MOTo%Z?4NX;"o*)`laC"=a;A;NC)7@o^
%gmJWFbPp-khZWh/e%?1Tk^9Gara)(ABtn$bh:"4MXOFbq#*?Q'(ln"/l(X`U2"t`'d@:%+C<U][*iuR/^7f=\rpT(d"b`I\)j>]l
%ak?=<rMK:]$d!#d9-c19HFm('ELHgJI:n_QRShNeG9]3://J+E,?MGsiLb%3B^g-$ErQQ;?aLVa-tdg,BRcKP$O(NuN6+o"r$`Aq
%7/F_dB@g$Xm'`<W<Y0'_f7FJ*>=1H`h@/Tuc`_O]s*6"\Iif1U]*!OBqRNMQ/]?<=Vg[l@2A]M%=Ea:t=;i&oXNAfjagIkeGr\<s
%b1)Je]OrOdbMAP(V_]*6N&O2hD#8NgB@5>6Na07K6:^!m.I\)V)+?1)S;fR9Mj!_A(SelsZ^SW,qP(<2O29,b90BP-?Zig;1KL)j
%g'gHjUElEG$jBMr:_9a,(LfL>FgB&3.6@JKOIqM:bm&K^F`W&6FXK#Aet#$d8sq'QlOZ(Z4kKB-WWaQ(]P$<776"0[jUZ:ke:58/
%Iglg7CT#;DlegFN;t6bLaA:ZLg5BM2VsYh`0E^/eOoB-DKGmZ]A_uZI<(sIk(qji&S3m3Oo!oU!O61:aNO=)a%%&e<SCV6?k:_/=
%%n>Wh4ap,D]cilmIo;@Ogt1D[MJqemO5K#.XA.0(g7)tK<dTD=WU'c5FT1hQrFJ3FBVI]p?Zjbd0Om/VZrN>$K#[QF$JKD>b*)Hj
%>ZQ@'X/O:Al'20N+d(UHe)/Rs5P/E7+@7>h\MW0*HG9C,hm<'Tr(Na7A9`q_Y>L8@+S,eGi;G/([7Psn:`sb5OlT6SUpIn1"@bUj
%&9SV.WlYWV\<e_L@QG1,/l5D'$o,D[QS;G_q_kC(f4=f%8eY2T;)54JG!Y@J)43#r2J;AK#UB6*251#,.#HQc.VN#!T6Dpk>u^^#
%(]tN",JQbY.%-Ik&@%Arkama^Dn]KiW)91G1*o9$VY*Ep*%o`H[Q]>7mKH<k8^3gqP%^fo)g@,n#TZ?KYj&8;X5g/QXU@a9jo"oM
%C6dl$$RL6_6IABPIj4P%Y->1&bl=7f/0.?*hp<JWG%dd4f4j.k%@u$aE302@'<j^oqSgb$SVb;D(uQ=MkRCb/C9Z?*-j!mcFZGpr
%cf4rDan=cU)P,UQEj=tjf?<t4[JY)3J'$[_N;>`BC>#&lQf+u[:tK5j7INuI#)4@bl[2>H:7I-)`=J^=m5b*`1H0_b!2';*ZLs.=
%14-DO-&i=P&'X:aja([#f@R-j8fllP5Um:i]H8gF:9Na!hf29#fV.KNgc#R]@CtS]:qXEqT2,g/;O,!CV'@RKpX[N*B3X[!Ha#Gj
%lNEEr?D7/_$0Zp)INtF?_u,3[CV@i,1h=sq/cS/"043tS``j/A\(^?GfQ6EDYM.6X>cqXSa2<E(]n1$(^>u[1R`L=M3gp8M:ksGc
%dUBaBA=d*Xs+W<o>l=FiMAJ4h?M34K>o,r>>\rkkO4VLcHI)"?du_3USmBU!kW4^p0ABe@aN_t[PYi?_g%d4kMcAY93nj\G;75U]
%B"nX/-h_"Wolip?VkBkLpeUkdYJpR:gJ2FYrg-C#0]G%jk)o`afPmBBm8okYD'8673LQLj*+t(JGZK"M7tn"H$,+6ALiKgr5gNY2
%aG*p8L8*4t0Dj#GWr<7+R9d'XGYW1I*WI<+oUsINI@91`NDMH2[3knlbc6<gE6p\#]r/>(kmOo!J3*FkEj'm'9Xq4WT0;.V`"D8s
%GRM0ti_.5@g%dTe>*o[8Ze+G4:-/B69krh=Ou4(lAtrqr2HY9FP[j#b:X5cHZ?!hiJsBb&WR')8bLg9t2nqEip9$<Y9p#h!19jZt
%][hEE[Y[ka<f2Tb5s75b7!gAth_@/rX42WP"m&_G-7D)i\#I^*K^e'o`okOn-D%DZa.B5?h.9K9Xe#6W3SL]2C<.+#(MC(rl;2BK
%SmUK:UqcU$WlBM(f3!9dH&VtdrjaE<^"Hteg&;PK+oKK1J3um;?e]U1(7RGiLPbOZ3L6;i,E@0(;a%S]'arn[g2nM]HBW^q]e/3,
%r@=0%kKO=*nnF$6IF\QJih=iY_(t;Re'gta`0rJB(>_F;p^M09,jr;eSljFgbTmETGI1ASN^LKdB8W<.T(K6DVG"6hTHgWf[ZBA-
%XjBkfDJgK/[HD*;0@fGs\,fY^<#G52N"g$?`o;oJA+#2<6NMK3&t-g]?o-m(lX$1U9S;\\b$M*PB`[C-a$=P69KtL.#!*l;[N';`
%]S"-rY0Q8Q*_SGsA3OgHVnK*d5CB*G5>0S+S6WdR_QEfYOR$(Wah6$?%;[IC\ZI9GB;mK5(<ELh@d9,(RY&!E-&-iUQ;`?0fUagE
%T%:E1KFcq\/>NY)_fRJUJc+J`B#<9"E'%@$rKH+1#G$j$jRSt[%u_kS9maG#m3n]'e;f2LrQi)7,0`9i0OH+LlH;Gr6t&m.l[]2i
%)7ddo"+dh9k!\L*YT)T=XWTbuT=oIop$#$W"3%HcVrP!-Ui*LScDBBmn]&)JNE9u1EkK6YVugu7p!F,!qG6@2O5&K^92p1t[.S9^
%Qqa"Ef/[,MO3?%WT'd8X_RpnU-Ya*uWK_@6==4!lH<LX6Tob,5fAi_>/@TPSPSA,CQ9^%>Y:$SShbL_-]QGIbF]MAui`*-Ua:m5e
%1T;7]?j^?SrlHo/3`/lA/n:Tpee2m8[_$TuSk.>GH<p@XCGWCVd&2<f-h1,9mI&Lj`bAejP%3bU.G\RPmaPXXp+h88,daJ&Eb*LV
%;E"M\@#<0_I30=`fqL]rs-,U6(SFZ+l<j-l]^h@:B9L/a_Q_!s5EkH[+,MAY+GXcB-:QXDae`i'ns%3QXH=/??l>VI:e>5r2Zm0_
%(.MrS1W*UIGu!gB^E.dnDR1id=&hC,iU2@HU&8L_9MRJZM7?=<F-UfLo=;cQ6A(qZ%Q8b(67"./,))qH^?XncHl<MkDbBHap:W\O
%f09VA^a.p`cj>GeS.on+Q_RZ`Omci/9'lYF$%&'3bFTrZ[!-f9AaE=a!<g/9e%P"4?!i."b,dtd[/JZT!acgb`:%=h\$9;BP64nM
%&EN_ngE/(4eebQL_5$<:hI@#J?:gHs)XL<,ESFF0\Dus),i!#fmk44!C]kaI@K<8OG#+^p.Y&P:8!1hGg^R[L]1dPj]a-<tecle;
%Y^)rTM'l*ZF`2)YS4m[Ggmf7o1fD;%cBej872L5AFu+.lB`CO^G*A:.(dH1/q-^.pSFd@.@]aN5Y3$eonXh.AFX5,ac*fT\\n)*%
%bcNGmWX[6bqb3-Mj<&(jb21#kfUO4:^`XtF(s-sY3XIiO4KN-@Kb^ZX-`c5k+_^IYUj@lui]f;fjXnSdPGL5ICF3/e1Pna0EL(2V
%Q3#^^%;ZN:.JKIu15%nb<kT3Ld0Euj-&2"\aauKVPg"k\e]A0qVd\!WQGF'?kgjZp4tp-^!oQcTQ`9"7C1J4MrRm;6U*&Uq\6p:<
%a)HaTJP1>Z`^go)2u&?U7X-/,9tcfTkG%fVWB7['=<UET4KOT:V[k9h3m!3VYV7X"9Ch+67.,k<Om#)YA-MmdN3+/4kULPW^Mc&d
%OtMlp<0r\#1?Ds1A--Fc'p#c+Nm$?&FA$s4,STF,dj\f(Aa")"-(0DXdoA)B]:U3kfIJM*oN"Q8_cfRJWG3GsF$\8(q$/L2*_]5a
%%FH,I3MAj>=S=6A[qt,5+9sf?.ZcZ&nh&Pd?=i@Z:S_pU(QBc$\]5pC'6Jr*]L7J-CZ5"=php-+\ARfB).Is%F/u8?-/k"fm>W]D
%jN3dJ@1Oc7I>#UK,dp<i82s:S;i6]Mh##PM7<W`Sk+G"eNJJ3f^i+$e#9J>ljA[_1:SUU$*;#b9n6U`r[c$e_DArHSH6M*,YBaVp
%&.p#C(Rb$9WlUgD(N:+#WT>3gFSXuh>fL>W284n(%nB'=^r=hq7#O!-`<FK&:9un4GL-WC.2`-8Y'(K(]PebW=GWrF%s\g+Ri;*-
%[fkn<\?."ieNG7JJ(lhe)>-VZ,_/)5!C`uob]67Z(k;o'aZ3cI%;dBI#P%\F/",b-UFuU;mW/UI+buX]RCE7?"Rk!m"G1nLEjQ6b
%NO9bOCt`s>m,tJEOjM3K`+cP\-SDg!?\>=E`,%M,/EK^3GorA[]XdK]L'J?e`n/==%CmaRk`-4P%G^4Fn'D9_dCZS/KJjX@?p#o1
%OGH^2Skq@*Q@Fh^.C/bF5lTY-pgffm#it:WT59O"kTS0jVZ96Mp@O.,Zsq%g5j0`61;Ws;qEa4n.C"WZ9C?79jm!YC=@Hm"Ik19c
%*T67%o-![>7TSVW;T4=M!(CmMM#f<cD:YC"J:Vbr.Y=S4eWh,6di2$OT=FS$-0fSK'N(P5G-4E4WbHkPAu_99[1p\IB/MV`*O6SS
%YnN>^pc409q`n`gj*]^#2<1^^6CF)5`-J*1&"hEaA/XF-`Dkb=G7+pr?J^._Y%^[uG"/(k\lGV,m&n-Vq)Dhj1g<$tYn1sqqFJgG
%XX#]%PBfk+G)tP]=,C0KfHhBcgYA0=6[A?WCnBPDC8g@N5M5:LoE/YL2r"Lsel4?!\D*:`k.,>O^@[7#E\(1;["&mMc$TBr@P+Mk
%Y@ma)OCUc1>c_X?$(YM./asa"m^N:nU38)0d:]rKc&Qb3PSF`6W.a?nAoXI<YF!hQ_!1.9Qe'\b9#*hJrr!1Ek3^]9c(l/f&+2oX
%.u0O$)+EtY$AhqD#/KtQIV4a0mB5:iMPiVa@j/bcK$.[g21#<,WVPP5A@%]lI#L1ZFR(*JCO^MsI0t?Q1X9HQK!e@8IXrrD^QM,"
%J+1ulQ=$8tC6M;1H`AS)lBd`)ogOY@@l$#:Kk7l'>-];$raa#/?`L>RIiQZL91.T=Z@Nk"j<S95I1Q,2W1$>ChT&=P-,q$&M+*KQ
%o>02l[6sT5"jZ9nRFc,o^?>AV9*Of#pRAP1/I`4FbI;dn]Z"'_m?Xb[UtGH7"YtJ@cfSD;m*[#TZg&&@<?96b[C6NG#chY.%p6]_
%@k8g9;I7HYA/4]A`q<Hi)N.S^I_ZL7REZu-U#YkVcV++%>&EfYVf=U5&nog'['+?-6*#`U@kcO2H.'Bp.tZ<5%ZKTU)*V$/V_[t]
%*NB)t-YA7RAKZO<T@,/)&)%q;U^ET[2`/a$8$QG`d#L%_,BmCA*OuRYL'AJDm#8&pl4+BHXGpMcl#+Dt7e8_i56,)f";AJ5l`'5\
%CmZ[f)f3A-*3Ajq8##LI@COcCmoK<)#Vn$(Y?KSTr:bVe:Kf;opGk*a4;=2oaXiTYa]`I1q7c-f1oOVqQV-DA2hhR\E0]7;]6&k<
%7"ugc(YVT(1`CMsr75*VmO)9V2FkC-IY&JEr6RmV5(/rCd7Qc@@MUX&&;dao._gkVd'O(*:0S?_)#Vf>896]P\qJ*QT&'U0-_0T/
%SlR3GU)ufL-_S4_%jP*7Q!h0?$RJgXjJ]%Hdd)dE?COr^c*og%KB`S>WP6?gP5.,&<VXQT*D<u!<g61MPQ('2Ae9'Arq'Em?H"JS
%OtN/Zi[NkJe0ZP7d]N/':#71#m,9?`,A_HX"l3!,e3aVQTXXK%C,i;7(DtK=_ERQ@Q*!_ak\o!]I<dis$_;5-HT`oo,r_B/\1mGL
%kDB=ae%2*i_U2?jgf[0>5P;T`#'r;OC&qNWN+2@DBkQJdZB-fQVU$VqLdGT)AK6"eZH`2W?Tt<PThaY47U0!_7hD;uTCHAP;*LBM
%oYpZcdP!!1Q)JqAYPN-s[]AB%j,#rB/`@C5'Z1J=X21bfWgk`n.R$N9-!`WL8fB(5Ia-9<29I.bm-D)=3+B1'(K\2KOhpD2DWQ#o
%WFTqqDVcLf8<LYqo$?!nek\[)PT@S%B(c_X7.'Zg3p_8'He%YEFd4ElL=9Dg7Gpr6L9!<^n`k2f-!oIdFrGmpC.u4jMmA2mB7IK/
%W=lD\@Y&bm*@]+9s4ILtA3QOP_R\MeH-o*p`3J=7_PnDm]<*:nN_CC"!k4%tZNP0qc`bVW7bJ;GHj6BeT&/2mk.PT*dYAaE1io)[
%rA*pb(\==Q3H%")&Legk+=IhFfku+d(`BqrZZc.<1B'>W,]?&u_'o')Lm[?+HJok%2+o&]R2MurCWF>hA['@0*_AdUMrk19F2[1O
%]oX/Pdss$)mh&NM1O`K)EA@3Va"sbgl?2Z24^Y)QN7WdX/=GOG!rFl^[jDLQ7F(js$"nR/;L$;u:JLP?Yod[Eb<16ImoAU<8[!Qs
%$?@47/?,M(Y73\U>h3"eIl0Cp?#&+NLn^H22?@jJG.r[>Z-V]+0n/.Ujms!@bVWq2628\;YIO`eE/J-U![ebAO:+i,V/OsfY$d!`
%R#m/a+qH'#rcoK%%UlJulWD@j?]?EDn8&PQ9M<PXV49EGpfPQKJ26T\@39FO1,E^g'2TeihD`+a&;Uk`^hLV-L*q[GV3FQ<>KSg&
%i6%o$TA8iRRI>^HQOAk!R^Q1h&lK8=%!JCi"oWb$Y5B-TZ=L*iSPA5t.;R!)cqgS)Vg?;XE&Zt7`;nqB"j7td3SnH)e;Ugh:;;s9
%l.RO?/UM<$\i[,a(d;uKFZ5E7E5WK05)JfQ7S6$gc,9$gVb=W\Z$gitd>Ds2nrsVW?c9>g_6)ip)[J*h=4s':@mu]O09p&14:I/R
%D]b>+6IkL\`%-l"ZqljOo[#%9DdF+6*,/?FH!KI(Si>92:Yqp+:Db\sTqX.3P01`1Kig=$oH1X.(Fqo=KE;Y1QU9/AT?H@a!#IS!
%`U=TDHfQIf>a"scrRs/0E&of9CcjR/YIs(^qtc;.S+f)/69ou:5h0AN,0sUXNSfcH!)tB,b]b+;)qT]GiXPnp/h+%\g7rO@HI>W<
%OrNl%"hLj-C*ZE;Y]ogsOeQ4NoYH`*WA&S$1WZ9%0$S0K[!WE2&sFmrEb,;#GtlHSr\C!Lg"q+3;IZlfmko]%a'DE`Hu(&)nFWqU
%%;T0q>P+Vb8UK:,Fa'77fA.;4R5dar6uui)La7DRC)o!'4d1^WgCC_%^BH`fY77RJ3oc.dk^ZeEo9&84`m$<]i@SuX?#hc?.pa%L
%G/2#9)l)`KSjFLHPT'l$WRKL4$$JLN+*<.dClm*fD#fPGAeSgoG:UR*,/UO:,MYi#-\3=HL115/%]?[0ilc>\I5%Gb=APY=NA;Cl
%8HW9no\u-FY(N,*&XgHUjngo.](\1/%X$LKcVu%c#u4?':==%kj!LQ`^;gVPZ">4</'jhE82Nr,QZ"7t\@/)B\gP&,7+p[jaGYGj
%F'eX=T&0RWDgP+R*gKShB_/rJ'puXZ8)Q6`DX"4S\HTkh3$B2ng#E81'=f?]DK$LSNoPjsjOUC8VDXuU=Sl\?^6TbZq@FBSa``%L
%+$,DPg:Mt%PBU`ns&$'<EMAIL>]rn90Xl`Y]G5Ue`__B-T0JXH0jUHdW'h##^Q-A2nldZ<ri?9EbH%<k\LUg+_)Wk
%S,E4:Pcs09V;O_I$bI9e+JQT!A8a`l1qumq7YmdDYel=EYZE8eI6G,CKk7lSGH4.)h30I<<%pRu(Mp(U8b\qZb<o#$b,.)GcSfpq
%?3=Ur!L&'nH;+Z9e:5$"IL!<cm$]Eb]13f=MS-CWq!4lV2[/a;W@)mSnJ:6.D[W^/Mu@RarSf3bc>I\u(=UrKlgHELqetib?dm:R
%VC$\9qU%7/<T`e7r[40jI]$8Z`Q9.7.45N"QQoEHr$A?h*Hh4BLW0WRs4$#H0*qU?/u@6;:@H\I+n9H!N(3VBTTW5@REj&<P,Jo7
%ee0(91Rm!5`bZH3pT\*P[T+]Co#kB4j`*6)fX%F)?[r#c2S2.NB!^6#0q4^;Oq`hNFQK^DJ+,Do\_+KZBAnJ[NINjj?8I3E2`:BN
%U1=qB6'bDZ+(GO1ep@:8_$>aJr0dQJjU:irM#<\!Q-_,^=eVB*:V@nMfcu!dPo@Dh%o*L-*f_c$cQR[V7IX*SWb!4M\/5-`@kZ#R
%p1AWk_^Fmn%Sq`#Ba!p$6E1;Ne$jl=5*e=U,R<Wgr3M60qU-J50.254^d"idi5XS1q3159"kk5AIcpbtkV_oIpla/E]ED`dKN#Lm
%T.f$3q*`#"NAnR0i1:U"%f#F=9m:HjB=sW8i.&e^&Zu^p@Yc?DnAMHk0pg&SAM.P@%Pf+XpAV81^^esN8LdD';T&8(`21_m6sr-<
%H?>SQJZi&JlRsGq'Oc']M9nD6U5ao*[P`$EC-c0QhFSq%L\:dcePU^Hp?>!6C^Hhl.dujPIt&SC-;'K#eSc3%jj)1a'9>r`nb`<f
%4cp4V"[\@#5Pf]tUnZ=/g:hrRB+lrH7q*D'HXgEDPCu5s%b'D\1RZq3K"c[0"efS9"&tJf85c^JpLeP5n6%%Q$NdU]?-:qo]PZ9t
%l`\+2:c*NngU4,&Bi,.N\"#qd/bOZl5Pe&6rA;_D8C]@i*-qa5rld8rkUD/%buGl1G^&];UD;4MY>7s2Vq.FGTHTsE0MQ"'hK4hV
%WqqU-@]:)MmEuWa%/BijRN#l!m+R6hB=f);n\+Fk7(F:#'s&b0^PFZFA[]<EMAIL>-;Q'maGuI[09C`Fjb*?/SpPIM^C-Eqpf/!
%h*$.Rh*KRcPatD_\nK[\I)WiCdOZ-q0<+?.GlW/N'R\t/,raT'(AhmoD"X[nbVmbq2;cJ_`V)h+ccaOm+HnZEDeIC.=1Z+&]8IiI
%fi,4&bhgoTbNsRIJ"$=AT?A2k9d41]?EKXKNBAodeud3@n!]DB["f$$a_Q;\bO#,[XMi8o=j744F0KA-1P^9>'U(OdDp.9VZqrL7
%[NZr;Cr(GAM4cqA:-Mn>c"[(/B$OAj@clIjlG]9sXK:QYD\k@!3]HNi]3C?>>d[4MF0-CRR-XRR?u@_19lk+EBe_h']kR)eHNYL`
%q;n;!S)7te(\:-aoU<Coqms?<Yd0[PrFj;8??Wd>WL_/!kJ>1lo<RB%R_O190D!c<jXfPAB4nKP&nQta@C9=0j+jMNPItHX^<OoX
%Kdh44(7m*fU1Y'&K*@0RkAXTk_8V<YlKEmQZ1[.D,rr]C>;C+c_30;mcRboZ>fBV*QNbS2XL>>i\#kDHOb&?qi\QMS]l&;.FQ]n#
%0U1`OP9rsW\#s$hX/_?'A[[VrA@9q=2QL,n*Eug5DVOHW6U&fdO,?rEo,aqfmqf)aO3l5l]Dm8aVX=d+CTbrnXJPVI@]dAo"Dd$R
%ij1gP>n62$]!>F"e]ul0(tIg#:TjI!b?,STag#tuO,n+Cb3dR<n0UX`cWbR/@m\.1RqQ]bD<f=AQRuBGR;L%[l`fLG\Be4LZpi`_
%G8%6hrP>T&-.&[G3^N)<D-/END:=QjcF2G=rAWlc%a.!op4p9.NG%DSb^A#Fgdp20k9bRhO5;.L@JqN7Y)RAB`JEiW0)K-;O-]1N
%c.d(2FjpA\82m10+4Zk2YW'%\q]3N@R.b<=^O<i%$aM'h%p%@rHYa==_D:hde101q&(?6G@J<7X:UP#jhI.a_I5DM-m>FruDr:@q
%D23Pgp,RMQ^k5!f^%rbC>3Nqf-nYZn^/FD]W\;b(7sj6#3Snr>Z=>:GH5uNSrnXiM`pekGdh.S,Sc5>Zi071ik_sm=>A#u1H>o1d
%7lhTI]&!2QdcidhKiQ5T9/)NMRj_HFNl7UR$7%c7QWu"/?+4_k^FT2Hg:40^C#AS[8@DeG968K<M)K;jf@N\.h)D;dO5"T&[^r<e
%e<g%j-VW>VhDT?)m)tFdEB/8bGA!H_pN6!&m+sAPT04gYkgdR"bBH`1BBa_?+3`)Qk=s2`mIJ0Ke$3_,4J-3(n(Cn4nXS:uI((H8
%>UFs`%JuI;hXi-NnQ'rTX7u.lb'SIWI+g[N:>7tUU+u?:DPA2E*;%3un[l!5Y;dIRI4auCpO0]]bJ'tUIdGgKSf#3inQbc\p?p-H
%)m=2uJ$J*Oqcfo$c/0s.F8f<N0ah(jHoU&r^[DAk^/YReGIb1J-+N%=:X-a\!7XjSHr](8N7/XT1ORY4AT#I^L+-r><i.`H[@(nA
%:tl$eAoI689Yss+c4s_3Fk<#*GF1/?>fn&@AWoc1OfPq#S$,DRj!"sD(L<nN>VZ'Ja?@'3=fsufbM61@lJPsIqLY%:f*@o^5FQt"
%:-]l(g1'tmBqu$#G(mbjiIPjo,=KDS>pqg]/Ad%o[M9"-[Z.NMD&^kYF`0^V/R-/013k*&!u70To8OUWbNkH?[l]/M^HQ[9-5!1L
%NViRae=AU4d[0fDQea<H,ce(s"s[CHXnX$JU>/`9YEs-Pe&PipY-aI50"4`9ab6<L772:AU_ru.Xd'a$Ee@h$:"WZ#[?_kheW57j
%/7/^1KC6ik/Ju]6bi$3QZba\uGSQ`*+t9Vgfo!+I84[>KTiB1>#rPVaV,4p4E.kY?VB'9=<\YReISRc1EA&F%-nhamfC8#Lik'V4
%7\]=b.rs5ZpEK&X>;<E5Lj[^A/Ztk2<;/7r=*3W#mn[LoJ`pjSZJYM+am![7DZ+lc=d?"rSW#LM]3]X0=J1fd`8WQ@Lt/%Tk#d*s
%?,b',YL>JgH(Mt'rPqdJ4_bMfT8q26EqS[06ETY;M;jCq-f,J=CLX^>@6m*A^N9"g3*1tale?8"3UC?^.GMbCT9:^KRoH*@pSV?P
%j*5>3;6Hf!p?3cm:u&3qo>M\F:YbQ^N2-?^ab0_*201^D2Da13G*=cg?'25P;r4g*f,ZK-ID+m1ZX4d*3le2<A1gkf:NI',F>Pq#
%cUN1uUDV7r?*;M$o=:43J#_1E_6r=k$IOFT;lDXpYD$t3&1opnc^SD)l)dsu+*:J$Ss+$5WN717O`q<$0Vu?*mOjK'c_"bbCMoE4
%P=2_3BMQ).B-Lm#o>m]s?!KudhmGo=h;(3kP,e<#R8>4&`c.2UbKa>f8l$I-DVVKto=$BblML9l>;O#(G;U\Q[+$S*o*1M[%6]m?
%hAN7IpUC'uI,@g'\+fL+At^7_fUMPMi4`EFX121>ZfT0T:`?h%7Q'D21e-5.\*<=gXNe\)1"^lRa(ikA,;a7oD\9EX*_4DUTfrA-
%FsZEuIB";0%@c`4ji5_T&#<EMAIL>]7;"6M4&#3jLpZVFWacEFQB(tigR8]^$S]U@M3%j#N28$cedld"I4gW%cqmje/hOaU_h<9bI
%dPJW`D^J750ATY#ebn^QWs5c$]3tB@=f[lqQ]7F2-?LO!>K-LsI%/4Fk.AC]9Y]b]XSgq%(jeGO6@SZk>d2\<\*%V3A'9('3k)@p
%]CQ`;eiUKk[FPfC=0(.X^K'[(h;.t"Ejs"@<:oGpp:7X`ju\"548S<qHH`rbD`t]:6@f^Re]:Zu0M@/gLOANj2lsA.G/NPu`fGA)
%c]9Puqtk'<=dmBYD%R[f"7t)g2I3L6_M]p$WAT=SPJpf6Ja'(3mH$Ru0:r"&C6XcQ<Vrj^D5H7_FS!'.beiV514.f]K=U)P*hC?g
%p%eNFb7k^%Vs7B[*ULtD(LSF[YO(.!$TYr7*-\1:'us:TX(qV3A$trr$a@jAnduVOgA-b?D<CeLFj0m>>/.]2m)F0(<0&O4B>9UP
%WD!lLF3N0Q*jS#+b44.YIl"s`G89.`'._GeUrOPdgFi4R"5Hr^cAN$SQTq'Chp[M_r[egL2W>B`,"2je6[VZ<7["#O3J0Qq_4'S;
%)>Gla/#t"_;bW]0_B0u1QF_SD_qA@?-JS&P^?DN&+:15Jj3i:]nU5XJPMZ=H@YTi8N+LjS;"O1VL5YN#,BT>WP7>5kkkNDFf*34;
%6rpu#nINVRa+#00r,LUFfeDb5rb(#0DU@YJElYQQ`JpIQZQoK[9TV_!IXr.I!S6q%$7dS#5n\H<'X1#j7Il8$'T&%4""1p7a9RH$
%3R6]uO/RXZ)j(h5+sI)hEB=\0S=FI,(Ap,j<Idl;d.XAoAP?[ao.+>9rrEZ!s7cgHE$cYWa<nt.31ffDK#0hV-i+"'iCt:&lglYa
%WLR=X):/jg0_qA;&,NVqaX9Y,No`u!*]V9W,BsWX']..iOpHPXYTm1'WA<_<5YkP&*j?D-1Un2Nq^"Nb#qW0r_[NWjq&*F9<;`aG
%6Pkb[rIQ5^P(3Bs@f<A8'1&,OQN`(!8Wo^4aY$QGcZ$0tP2Q@[9k][eMCPU8*]-J?Vp\%;!FEs$?^:kkO^Wh@^e.c'o,RJ_(r=NI
%N.`_(WaB0OdLE"iJsIf/bfZ%_'u'm"9o9!/aLHSOK5E%s:6C'p.ZaIXKc",OWJk3H6.]Kan!A9%19m'9"US^be^'.5!9k(t=Th71
%!!a4+&gYR]B4>:hSu#*%IL\:ACU"57#NQ@/nI2VJa1t`01i_55'k!G#prKHbG3$#2)&D1-bg:7E=$@eRYoh$VoJ?L%!c/%Oko'X\
%%oKs+]cht#F\%Et2!EhHVdcZu<<QE-L4M,P!i>JjDh(/OoLkiX/@&8F<>7^M"@8\k$Qr'',k,,KAK1p0,+DG,66J^W`smlYpRK#1
%]e^8bP?i4M;,Aonb(5FA1@@I0A`.PhQs\j70%;q,L/`5C?55?Jj?^29%USW4.Z+VaNY0(7F!C&XD@jcpr/,O?;83Gr!F(8+.;8VB
%'j)4K!doO/";mkQ=5u2OA2jMQUC[&'gh&S)WWKJ7OALZdWPmYL>,*CDBX;B>THu/e^Ha=R7Y-GJ;Yj5g;"\6&.LR0*?ff(F=O7qO
%iD<7q:^"(*=G9p(2M.N02dh<5cu'9Ebl;l!s.,&Jaach,=[fPmQK.qDJfI.)AiNA4-HRo]+uNpr"qr1"YVqa`)%1gn3"kK'6?"C+
%\#@.k(>T!AEmu&MBVV4;!j2bP6Rr40E+D"=$kQ+R#?2pq#lY($8Z6_RC,?*I'Uh34M/p3_pA&8sQ;ELUE`A@fnH1-f0E;MJPTUUU
%#=dg#UAKlnM_%XfJ0N,_B/0OhPp>eg&cX:L'^!$[8+Zns+Y043#\Xt*#(IR1Tg]P]=`<7jM_(iR",I^p,(d2K/!EH,FO:44Lm*qN
%&-9T`o;YB8mP;?WE2K97e:3!YkBIl`.&56k+_$$1-tmTC>sBtj&qCM"HC.;/Z]Gl9+g?ou@Z4;!+0_\D$k&VS+paZn-dWue?(pA3
%hLHsGLf,rqfte[9$s4-!Ld[aqG[(qGW1qA.&'">bJ/RAq'b*bXi;cE$LT!u]=u]>,8LbW7\l=FJ)N_ds`E5EWeOUNc_9i=bEK;Z3
%9$?2%JL8l6:<&Y&p+*,DWaQ,s\2K^KjSU3>)#^AJ'1`.?"of!gbS'jUQH5#bHX]ucYRPd$JlZh1(bHpZn/:-h"WXF*oL."Q"6J%5
%@b?eK;8?*:r>!BEISiRaW!WO4&B+Wr']NB5D%NHd"$o+#-W*I7i6s[<"@m8DArn9W!"]?bT88%`e\Rr;F@gTG$8H+R(-E2Fo<T9d
%;!]rC$R]FLP/pS[+,6U5%2oYK%=Np]Eg/L,eOM2RZN=\_#?4P8`$_Y+12J?-O^fQGMT"tfBO%i$%'3#iP4929^]r2;aujfU.e$/J
%&qIO"8JHH]0uOG>NH64E'gjF&:6^n5NXCf#BW.jB,mANdf*22Q80UPU#2kJ/ZicgTD3<5#d(-FF'kifH;2:qKb5a*B<iV:^5c88B
%Bq`S#kcaB1d:/8rr8C`bF&W=1KUe>s<J*g^_>o6#P0Bp_A.[FXZ%->/$B[.OA!=>O*/hW7RfLAWF9P.nM6A$e6.1T5R`[,DAd+Tc
%/-m^/F,VG@Ng^uK"jQ"O=f#Vb.^K2$"dR.:,HhHS"4#WH$'[L/N'J,K"`mKO4-965W/<iL"><[35dWe,L[gVXe4\n78hnW.&b6d)
%EYRTM-lR2),k/do@?Drg\cS9r$Z3O!bHqj;'^PuA/8-U`#\[XL<MD'5l:E`B&;)mSb0k/IeU#lV9P<?r8iaC/ci@oKE0uLuJPhNm
%]2VA!EoJ7VRt1C9b]$NT$k$JX*MX%4<E7a$,@/*6RH7D])`VAI%g)9,Lg51b![UZe<oYth!4GofbePK0>JM+)!8&T(T87fA"m&MT
%WDIklB*<jRWKj^O^VOR)]S9`PM;":IJPsXZ*V'Pj?j$_IT[K7DampeJLm"bO/gL55JUjLTN=1h<^c7K=EgeU/i'J^n>qAKG6.@du
%i?'>DD328(E546LC$\d7,tf?dL^$gSM8g`E_Y><iAh&\BZilM^P$j*Ia75Lj!45`<dp/X,*.!hE`R`I]XkG%j/.%Oo(ea"X)GAsE
%T$P2YS-!NQFP8![B2&^:7R)+00HJ4&q#Uh'&[d:QK^nlWf$\cE3/K)<Segi=$"r1("d$Qj\pY'<ZZtA`&OVW_KshO7La&-qRWKb9
%U2c:Q-&/+2&/9Xm9md,=<(sJ4=j_X)"q?`L_<eh1!FnY?0QD\U(kEf7#Z6E?1%9c*5cVV[+G6DV+.ss3isBH%`!Yo=U*[%'^eoXW
%n0]jkm3M9,pqn:c8]Go(>+u!^7U-7.Rp^T/hul:ia<;fB2F\gE`\^YB^^(Wr!.,U5@MOksVPtV0>be%?D.B.e.d42RoX5[a&31&Q
%L(Ofn1^PH>"$D-n(`cHbfeB-:?p7:D)>%6K7^,U+K.KaU(%h17.`e$nWIt-NG"*W`=Au3j#@T;K:E(&@$CKl^%)l\;Co`Y-Tu2Y2
%J4biV%.Lt4K@qk2:'C'%;DkB7"#Rp2,1n#I2WPOfTu\3nVVoN1*aLRH!?RG9BOFAMO9J5oi_VB;*2/8"%RpaA5&'Eb+[lA>.=fSb
%%H(\T&]OoTOsk.`b_bM=,"_"#BKF+0J1cEAQ`5:PFH;OEOmE_J6<.P?!q:Dqn$'/2_#kfo,!duC6n'5pp-E.$^ghr4.Y).3Ntg<H
%b2@<U/$kb;a3/!9Jd7`Wa^f:X#XVRnAWQru7KfGu,ZU<?@OM@"N-hk1/Z=q;0H'!Y>nFRa's&=k%LMS0&r:`8L-ee?dM,GUBo\,h
%"lgQ,*Ir&`$A)j]DO_,o+9`lgRn7(sE6hrZ93.:VHru>"Qs`d/Yl[K\rKL9QAck2b8qOCr,!HSn`)+\4"M,rnMcmj"]Vt+L&&an^
%>nA]BVo%O,D'IU5fZS%_!hCP'7,S53ljL+"io4\!->W)jWJ,m`WP!s984nMJ9VB?>BS$qYP)MM#8?N>1Jbt6)@4^LIO9,Cdr!>_`
%XOO;M@'RYB!"BS2:A>fjJ8?E[M\0KP)a3f$>.32`iucP7$HW+m0Vk^scrH:YAU8$*e`##l6jsqq-Xe_8_DT5=MC_*:+C\n!5T9W8
%3u3h'5@G9E_E/No+UJ>&FP]MifM=DW8UM]`IdW:FZ\0>-abWeK<0\0<89GLi8e<!c"W"FP5_DH-,,u$<)TuW?M:BsQ'sT\5'-sqg
%gchca,6gM!W?-!a5<*+-Ufmkl(pjD#YsK's6%`eM;b7\=Jg<Zu"@+H]3(5YbO>"+Y;:$O)pi;c6L?1Ls&bppP&QY&$3'g!rWKgnm
%WZc]X)BsD?$tZM2OuLUK0lW8\2R%l2IP;XCO?o6:8jQ]F9/9QOgAos4`2`?j(@Wr7gZ`4pdfA;_>dmBPik=2dI_S!kJ"<_[s(*>b
%97,SuM1dV*+;RZXB2!tkpjqpq8.!q,Ta7H0+r+]t0?n&^jhO)X67fMb8EeDZ6,6GR)^\Zl-Gft_VfK(T4")[YHj#tU!7OLf'1f]f
%+VoIA1&([Nf0iN\k-a33XGE7U+aahmTbAXo(aLW+/s2rr_\+X4Ef_iF4(U:BMggiNJQo.qdicOPJ72#/UZ$qR7;<_#4=MnNp*/tm
%MerS+rZA5O&HPNM494+ZhkHBL#\u9O6e$u><NHk]OpnQ*EWg1ZldSCu;::U<Q?m7'N:g`g$,jD8.8,aXZj?"!h11`K[(n`D0N*_F
%7`$e+$9F,?^e%jJ_U:8XiWQqdUicbjeM*Ak@<isF)$:eT@TlF!%N5]Z6@l'R3!kj>35bkJ&IX=g"]Rt2&e@>l#T*s#q\>(C`-fA^
%E-.Q\o)O?a<9M&["AD8#hodkOapcu69M#d#Ao@agC-$liKHNQGQTW0[MEj.u)'+JM*S+[g4d5$4N1ra]&Wn+@D:j`bAeA.hOO=-;
%-t%D#WuQ[aq^*L%!!Kr^e5=%3W_<[!</gU*3!trk'7fGbpBeE4+?LkG!:YRm(WM'!`Q_kP#9Emt"8o8l!=Hbg75*_AoEYHq0h-"J
%kG6[fnmFH%`>iZdIT2#X&\_hC'Z;,*"2!Z.AqU2TV>S@I,?.K;[1G>-nmPj``#1fB7n]8G-q#)WW_bB:b<H-]@dt+A1,aubGY9Mm
%,#?5\%19:R,rViWTgPP#:.u.BX@AkQ^j\kP%Et+p=",75Ud]#1:BMl:UEksFj_%KF-&$i'\g``CU?jTS6/0>MdF&dOg;odb[NPpJ
%GY;&`lTH/>A+9DH?t0@=&!-sh7>B8&Vh2WS;0ACiH;2Rsn4XHDeAn;68C!.l19k$4+Uk@.g^qAbG_!=G8!OOa\"lKLXb^Ypp8UGS
%L+NZHd>eC3$5,%K<>eI[Ll+"PV)@RN0X<t15lAdP:rP+*^EL@BN)r)T'#>p9=@qNTZ":ck<W[/YA3)Pc$q"^Y4=cRA&2d,PA^jJ3
%7R#1//=d>jU_%[c&j)0284L?fK-k1n1e5Eh+KFCF2SbN>3Y3U$kQ@!&^`aoKTfrKE,OSCY,;k!MQLY"g*NFr/:H8SQJ0IMb<$]M0
%(kO&gd8!jK&heY=js<PO#ga/S*f@QQ&H7:Or-(Dm28rXDV#*@nUEjlgNa!)T`9nff4Z"nsq*<jQ']9CW'))UUT>a/];YD;IVP$C.
%7pV40Bo9dT7N%t]8BsJ&!jI3o+E_AV'iMYojVU[+HK<jQ;Mf99O?us#r#TldK![cD70rjbEfg-2V[FsW(%#7(+@U@YDcQpb8Q`#:
%aY!Pr(699X2u"%%Kqt'q`SY-%)65JA7=6R+?-Pq[9?e)GO+Is<`c?g.D[<+iN!Eh1leugi@WY;I=m]Ap2?Fe]YuJh[]OB0C6u4"H
%Q`S8,MaVn8H4,`>_EMMl*#>o,C-\KpS<;2aW,\Rs($IV.MJHh$U-K<;OEq6La&st4TC))s1](]."pl%7>_Jt[B\!6AE#>/m^pnV?
%6k>)A]N"qd!c)-m3?Sr+Dk2;U+.s&E"H1J:-%aN5XSQG/)9n#&gdR/)ZrR]s.Z#BpCo:_c&8T8mb>HfTn14^c2mds_at1tUJW/b6
%6l6b_A-gLH]1T7Y+>VM?fuH2Phoij.]Sie:'o'44fWec?+D?G)Bb7_1U24Kh:#l_;2ITJa9nG_,7%6$"Nos`3Fqb3o.hgf9:Pd@P
%'5!@R;YEGtXK%5\LBI%c$7QLKP6ipQ%4u#TO_$Xg`rf#!&!<b\O)cte!T_1^Ri![lB5qY4$_C<!l1#87-P3]FjU>9)-$g:.qVt!R
%CrFi'7$+AjU:,W,#nRD;j?O/?KhTB(huch!-\Wc$bYU,UWuY>^,UfX<Bp-Al$:k^q0&;U6/A04sOZ29DE[8Ye'-sY[irfuTh8/sp
%#c;Q:==PURii+4R01.?n#T2DYNU9hP_N\;)kh/"O6Z==3NY"?C@8T6(RNM)7l'"@^kuC*4NlmM!"06jeRJ`*\-$_q!2/P]6961g[
%YCV4[:qQ4GMN$b%D^7jn0r[se6O\u#F:,`M"-:.;%2sQE!^hi3Nq!jEUp[L_+41?kJ:q,.\lJiQ,=0^#+C=S)ohb.BPF0*UjA*?0
%B(R&HhKU&kPGnMXa9Ff:^f.p7[:KJ]k=;6m9S>GXZ$AX9_eK96:=6G5-dW1oN6W.r6k;6K]+`PFgh@oc%'Msjm>0u_)3hl#-g.cZ
%6[&m!)^:$V.KNT%l\-gC2rlC)#QfR&oFu5ij"jR17Xj8U)4oV[6(l0C,ZPP8OS3Nj+VF-N6($H1!TFsgEd/r4O.pqW=/7AiXouoO
%,$p!8n7!-O_.4_mY!1c"W?Dmd10CLj8.D+97n%Yd-_O&H,6C7&U&Y42fLOLf#U(.^:^tV-=EDMf>/b54:/;@3aXmo6>7bVo)PJq;
%7L8Z<3dZu_0.M'*TQ?K(j,#J#DheKOm4]LA&@!RAj%)$H!Ah5(3YO3>qD/=a.&m6IWeS6qRh*S)8E$Lf0R+(@i:3clINq#bfAqqs
%VH9c_`J=1Ncn4HukU7-c\IL,Hk_QHshQ4Ld%<!TX\L_1p+*p22A[l0'6r.W-n@V3Ll0SKn8M3HV)SkI6!i:M0*k4A]+1k+K2-T9R
%'KQu0jYJ+;Ca#Vc]]>p9?7b(YT1Y$P-=iYmi%ZncGX3&-blA/)jRS-/1C/6e()7KS=7G9Ti(3BC(=@nH$7bAN&HmbgjMi8A89>5s
%8>u(+b.*:=DENdeZUA+Q`h>.qFUi>TE`gjn==kX9G^Lut?\n1&Z+Q=JY&C3SI<'VfkPM.Hjp'_1gfSbI1VD^1g3!<S0Qe-=`"E0"
%_n'Qh*c/:.ot[pP0E8s+*4-f,F-Eb;rqOP6!mYG/ZN~>
%AI9_PrivateDataEnd
