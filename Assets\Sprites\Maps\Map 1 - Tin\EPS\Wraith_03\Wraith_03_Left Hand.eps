%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Left Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 9 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
35.999 44.9668 mo
35.0898 44 33.0898 42 32.2393 41.2695 cv
31.4961 41.0215 30.7051 40.9004 29.8818 40.8555 cv
25.7646 40.6309 20.8477 42.3066 17.1123 39.4854 cv
16.5498 38.7051 16.1172 37.8486 15.8828 36.9697 cv
14.7559 32.7383 16.9268 25.5732 19.4746 22.4609 cv
21.2383 20.3096 24.6221 18.1436 28.6943 17.4814 cv
29.5098 17.3486 30.3516 17.2764 31.2139 17.2744 cv
37.3945 17.6221 42.4512 21.0293 45.3223 25.7988 cv
45.8008 26.5938 46.2178 27.4268 46.5713 28.2881 cv
49.0898 35 49.0898 44 43.0898 50 cv
39.0898 53 36.0898 49 36 44.9678 cv
35.999 44.9668 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.525017 .718013 0 0 cmyk
f
29.0449 35.834 mo
29.4111 35.834 29.7813 35.8428 30.1543 35.8633 cv
31.5049 35.9365 32.7051 36.1533 33.8223 36.5264 cv
34.4365 36.7314 35.0059 37.0547 35.4971 37.4766 cv
36.5947 38.4189 38.7783 40.624 39.6416 41.541 cv
40.4541 42.4053 40.9316 43.5293 40.9922 44.71 cv
43.9688 40.2441 43.4551 34.2686 41.917 30.1172 cv
41.665 29.5117 41.3691 28.9268 41.0381 28.3779 cv
38.8691 24.7744 35.2471 22.5566 31.0811 22.2754 cv
30.5527 22.2842 30.0215 22.3311 29.498 22.416 cv
26.6904 22.873 24.3838 24.3594 23.3418 25.6309 cv
21.6895 27.6484 20.0508 33.1904 20.7148 35.6826 cv
20.7676 35.8457 li
21.8447 36.2578 23.4355 36.1777 25.7041 36.0029 cv
26.7881 35.9189 27.8994 35.834 29.0449 35.834 cv
cp
41.4395 46 mo
41.4492 46 li
41.4395 46 li
cp
40.3594 56.0498 mo
39.1338 56.0498 37.9209 55.7744 36.7725 55.2158 cv
33.9189 53.8281 31.8652 50.8096 31.2158 47.2178 cv
30.7549 46.7529 30.2646 46.2695 29.8447 45.8643 cv
29.6094 45.8477 li
28.7246 45.7998 27.6289 45.8848 26.4717 45.9736 cv
22.9961 46.2383 18.2451 46.6064 14.0996 43.4756 cv
13.7002 43.1738 13.3486 42.8145 13.0566 42.4092 cv
12.1113 41.0977 11.4365 39.7012 11.0518 38.2578 cv
9.46484 32.2988 12.1777 23.4805 15.6055 19.2939 cv
18.4199 15.8613 23.0117 13.3398 27.8916 12.5459 cv
28.9834 12.3682 30.0977 12.2773 31.2021 12.2744 cv
31.3096 12.2734 31.3975 12.2773 31.4951 12.2822 cv
38.9658 12.7021 45.7363 16.791 49.6064 23.2197 cv
50.2109 24.2246 50.7461 25.291 51.1973 26.3896 cv
51.2529 26.5313 li
54.2041 34.3975 54.5391 45.6221 46.625 53.5352 cv
46.458 53.7031 46.2793 53.8584 46.0898 54 cv
44.2793 55.3584 42.3018 56.0498 40.3594 56.0498 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Left Hand.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-#^bH4jGo$G!q_+[(;HK-1$;(Vb:qmJ@te^NoP/X66\DrUT?FB@"?kp@RM/S%%;Sq!.MMe(7YcHhp8-c-r`N
%5C`M"c!RW60:tH*2u(\WVtmCJ(GDE<^3TV@l+=!:m>)UoU+cQ/>PJt5J3O^\IeD/k5l10FGOM1P:cCT;K@A.qs1d_TJUHiOp:fGq
%g%CNe#Oc]7rQ8FEnFRBDos::/X5ErQ?N'>b\0#-"rtddpF"ON2&#FFk,Q86Hb?,/Mhj9()Xn^^MiQDD>h8\&NN59_>VpuEQ8Sm]o
%5Okm_rV]OIeaY"1kL!=fWFO196s#O7ln^aD!^,k.\#4;gc=>2#DpCk"3]hStH[NDJHb-^$A&9<snSUHp,!>6uTWTPegrJ9V]@S47
%dXjY(U;sFH[-B[DgB[(jajic5qKTm/K]-.KB5s=hLQSFLaXPb8A)!ZRMI0'Y^6rqn;Ah-MI<;(C+CcqRZ)&/>h9^QB!Pd:WMi@qa
%"oDp+n9[t&:g+0-L$q%]&&"hC?2WYGrp';$?[Vb=mF-84hRri#qW?b7KbrRKT7?HsIf[_$<]ZHe_Cr+<ci<M,]9hu22-u9?i5^%+
%]&L_ZnI8C1V/JY$Y6'..8b$&S&[X[jptp=4q\/rKY2WI@G(0811U[njJ`<h1L0k2]K&8D8PTNdNBlC\5o?F>6Du]4ZrI'Wcr58,-
%hn4@5mQ#.X&CP]UhrXuic2Z.G@0=u2:Pson^NrdN\q\053tM@Z"8kM-*A$4GT3j_\bE&k+kOYTY5CY2Ri:-HrY@%3WYiB.ZPU+1S
%IJ<=.gY]YmnIE.ReT@C]T=t'sq+Gsdn`AMl:PgMoUHnjG-iW)hgHRlO#H[::bO9DAIXK\9rpJt,]t/RerH%%$?5iXY2pYk2_jfO]
%2f\:KM(j'9?FL'tj`1$<T5Zir%<tYjZq6^.W>KEe3jeU<1\aUN@_[0bif>a!s7hFH0_.4?@;p_rp;PX9^X[;RpjZ9;FdrAPr?M8p
%[t"/?q.kBZ+20f7CuU]IqIdR_[VoK%I<tJl-fJ;-k7MmSCZ(d;hu$?#48qO\K.=V47UCfT?iBm3I,W%[XSD=\TC_O*=20.Y*W5Ks
%hnJpSS"IFo_je]-IX(Yac+cBU\pNuDq%`r#MLPGtWuq=>o^8>\Nr5f@If`(CIffNKH@e?bBGpfPPq%=Qr:K=<:WH=PrUe&f5C-,K
%^](_*n"0LBPM5.K5-";55(E:oc!+;q?XNck?iCf>6#DCf*RB5>/Ylrk2o4s(^u&Mh02e'Bq!l=Vs3<mUg/4g'g3ZBR@'.)l=b6%i
%5P=K#Hln_!%)f+&T:u_K+7*'OUMniGM^3QSpq,b2V^nq9k4P@=^O5V^[a3JfU]\AZq=O@/\'q.%I/EpG^'oWuj)u`a$K)dm\n'SY
%7K3Hn`f<02pU^\')&U.J0M=(u]\*kbI:2p%K:7,_mS=s94b*G'\">iF^@E(S<:K@Xcf5#\6'o*XLNMnTo]=74Ish.cRm4T2G'/30
%h7%mFqM."J?@IW(L1urqVW+u9P&)t')6^@pdu==JDQJC]D`@EW\r47M&G,_se\AbBq;U.1KB;1U!i*4q@j`)W%C#t`D6=4p,URIf
%E<+=[6/TfZ$\(%$$.Tsh#onjK[-V`X4am4q`NtoWJ,T>Tn")a[YMm%_p@U(Fo;_YGP8<.cE().pNM3jp@c4cZ%Xl^*r"jYoqpsrd
%7WR#.+:BuE:T;2aYAnj"5GOLOIL32.@bUSKn&g'p6\f#>[qjFJ/oN9r6.N(Xpa)VWC%B#J2eAIbh0f/&A=<m]jh9"<qCbW1E4^Je
%0CLodE2*C%i\fpB=24b&`Ok:aV"s+k,X(g3.%'%ll&_$[VFGq";=Uh[&+WdE?dJ<7P:)D5`N/%b=han4[njbf!TNp9Tdpj'P6q]-
%!c+d^FG5:L'n1n?cl]!ubZ*d1kh>Mbrdfj1j"KCZTEKVkO$@Q2Uu*6.If/CO+$]V:+$[mgarqk+S=lCGgd&D%%Z2p5^mLAqV3FTK
%6YZ&Q5ii:57-.Zkb0As:jVCL3:dCeZ!D9/NC.;E%`D<8WV&\b4b,Lu3gd&DeNf%bJOjW']"=m>AC7.\fC@Q@Y7jt9YDE$skh0.Cu
%O$1>&Q4CK2DnYmT5P9*4l*gnAn:tt=oHurHn7'bD6TaqXiGbT5JoX'1*,`#$*>Eu3:uX2M60ZAgdidY#e75fdbtnT=3DhdYDrbgh
%2c0;L[ocDnjA2EWirAge.KR`Q8,j+E)YC+&mgcB>;BMlfF!%BJ;\-9sdQ?[m6$fLneD;b15=HuW8u>188NSdYrH+,&<C$SF-iBL.
%gd&D54";:DTTZ83Jh'_;Q%#U^TY[>*T?l\i1TW/*5EoA%qDXWX-cJH(^QekM:]IkA@3&P3)/;CQs,YMJ\(dS]Q2Y=Z>rkL>X/A/l
%hr8_c?,CeI_AcPpLBOB(,6Y=W@s4SdlnH:9hViFSZfXICk<EO;_'NJ/*>[Ol]CiC;IIb@$It0t0)gho7"&%jf/Ak2lN?2W7"K]+&
%fV#HFcg`@=,aWl[EltAWRUG0m[QMBH\OeKDfk$dAGfU3D&i(3a@)TU3B6iK>bcdK*W3bui!1X=EL&?`$cJqFfL3O,IPY=38\)Vt=
%=W8HD"_nHB,*_.D*X':()pIRJk4M+ulb)u>B416=k4T)8q?FZcZWhr;+(9f^gd[YXS66\'R+^4.?qq*R-Hp$j'6=Adh;QkoV)^%[
%>=Y223&_JpciFslSd'c.jT',ZQ8j0X%Cl063;>)pmKadO\`)dlUU,,J@C#KA\rF2Xf&+'.gRB/7ng@g]LRWbAi/fC,bU$A-!P&@'
%KsU7NjT9#[J)D'Oo5A%*j3Bm)hJ]Xq$g`Aur;h1$m![sU-eSA&f`)Br<riemJGTQ/`"#P&)dkBJ@'oD(0&E;R(!`6VB+CLsro!W)
%S4I:;Y9U\cLNBK4M3VKO`#dj]*Lp_#^a)^k]FV5=>=3IWL(c7!fP>_<elrur1Q:'#7^']HihsRM8T4k=(g.+@i!)[H"ClkQZb4/r
%B@np]2ShMcHjf-;).j%a%<bd-3NB=41@-q2&Rk.=F:12#%'t23IG%PO4^)6siWhk(fXicm_EL0$EH-pBR/[AkTYO`]P7lG7J<f*1
%/aWKn*%J`JFk14b@p<3X-V9.qJeTlWBn=!<J]W?<#ld\Kj`7XN#@]_rku*t7C8FIP%R?$/(&^NN6&N/2\A23c\Zp1@!_WbfKT=T-
%P]F"VV3mnh7?iD/j>$p-])`ulj%/4S`ER=e@-k;V8<H8"S?*^,*,pArs%p9@'#/7dh;7#(iHbQFiGdO;h"nOoB0-Xtg1'N!qTa`,
%2Xe?*YjYu'9fqg\^AFR/#HQbUh$<AZ$[WB7JaBsncdAhjWul_3HqR)I"2''Mg;>Z3d#I3EoGi>aD[\u8\jU^ChDqYJ*>Mq0>'4Vq
%+us8@[W23\]=[7KFOrLHTou>&qc_)4QLS%O@h8Oteh9(q_4<QWLLR3>hd9_.m%7Dulp%4A=t,)p(>iPo'V7]M4Ge)LlCk25?::q=
%MP@,.YQ.6tllq$%C&-Yj+@`(B.u9<UZ:#$DDOI1>Oqor)L:8A,3E$%oM/7kQ.t=,h*(9`M"VB0J;uf(<%T3"K-=sE3(f=_RUI'bY
%i!lJ!Sk(jeol-TIZAP#@pT:<GWVT$j(bcn/_,N^2[>PbP<O4s4>,cRaku&!!'i/%2<KG\D)cK=*iI_1>1^rJfV>/<8?T5B2<_lHg
%"8.im:_53Pr>Q)O('RO"^b.+pEDla'q9pN[+;A5Sh?oi:GTNkOQm];'-XTlkTcC@IGTJ?nj%#gHnqe.Hj9LYE$JH2%+i*&h_o8@`
%U]4FqXE7uma])AsLie:R3l^)#USA5.ZQ%=\:NWBQWQm+$)]W_AO$]<]d9.QG=t6Q88WGhI1Ki)cj%^+I4n@[\,A!;S5!Ac7n/KF*
%HiVG^i%0?2+UDlO5jN36&FLQmmlLqL%#"fRJ]H_AfUE&Y%i+sW,+f0g2[a&4.Ho##hCRFkO_tfoTLKUT-5"Nk[tVVpK%^d_L1"W1
%(`^XEL7.fo`%DY2L5:Q71Jk"W]ejm=/)cPE!]h5JfXNn\Ol=0nm><FpV\0jg1.l)`^n<&pNB]@&e<7\<oI,E1?(FBp7Uk3+-ds,f
%)Pc\^m4R)H-aHZeTBK%4]`Pk*eolu>(D[L'a0:)mHcC6RZu9&O4;(0LY_.2hbe&Xa7l7Nr4,EVBMikZ1k']WZ$25'PH/;7F<:Xqd
%rM2L^p=*.$HFO)V5-kAj<.`fDZFT:c>t:9>_^\66KB6:sY+55P'l7<!>M8F/7''"Q=/nk@<2E0b$L%XbISYMLKc64*E1%-oVOq9W
%lkJ"7L6`'+#AQAj*f+B@L-nbL-)SXbA?[gQWdeVkIDFu#g%6aK7+PfXVW!CP6+4M6BZ.BS/Z\<`!tp;:Zehg13&BtLba]P41]&u5
%J)X+A38cM'`*.s#5Bt7K3e/.%T<QN\&/&s(-KW>OZM&jRs5Rd97J(s-:B]X@]cMa;*Ke9CoQPBhAPXSohVd?PGr"k?@e$kR@>]Gf
%"]l6H`HU9L6Bh0t$43d`WZDscWhVp$)%R6NR62?mXA1<A%P\TLJg!0Mh?"[Y\e&$U.N[kP@Wm^RjlU$_?'jX6_"Fa.TPG^>eKs[K
%jm(b=!\+HrokNMI:2As)<hZ#=<T@Z>`T?B9Fd#6jWO@*_)]p!qd86UQ0f:E$=pibV?;If"5Y\8S_=W^gNI5L':K(V(11+2sAP#tU
%Ok1GO`-Io!o9L]mP83VOFj`1.jM3d6Vp*P'MrHB7O.#gf\u8-3k1"Upb#("GLt>k#f%J=/1R$>mm(Q0m1%RGgb+YE(4F*;TW>991
%Ot$trN?C-`g;"%_2aq.Q'21^--(#M6_6.fD^O'WCmjZU)e<kGPg6D8R=YOM%PGCI'hE532kINMO9$@""XI'AnF\*4M5Zfd-980Jt
%8kK]#91:KK^S7/-7A1A@:XhSd(FGBO@!31AlmQ\+2=>'C>%O&+=<<W]CJbQnooHE:Ft:Ga%DBnU7(B"j[bV(sC;O46JM,)ra@`cp
%-&G&P(%0.L0*7`&Gf@V66n:L#qEtZ)W_i391n5pjV%0k^=IlJ1a"o/EXB#ManrAqb<j0e**61Xm@]La.*Aa6*O`n/LL$P%jiL'o>
%.#QXG!3l;W.:B(JIuY>g@8g"DmYfMj;Q!Eo%-s&pk"RgCJ%*_4$L0P03p/H-KZCaO1Z-.ZDmN5EB=S(CXS43ugDW%if"cGD^lDa'
%JpABlO=IAJ?uHd=2%Xm>^q*qh>?:j/!VZ8p`<qEqDQi5Q2>_>6'KRu$)GTp=ZUhDs>-64fQQDH,o`u#]oHD+WMg]o[:sV&bV3Q@D
%Vqu`TTT<;][JGYL-#P"$Al=4XrH4q0Sk7m(OK)N/Ph_%CDXK/Sj/aYIbA!jo2ru_#e>V[*>hYq%WKaCCWL1S]'QG_2kjF4@c=M;V
%rL>6HrM1o)S1UbNX7ED#I+t0;bHl#1DL"*kZ&rpD50^"RHlD\B'Uhgjq=K5,"K,7icCW"c@qX*FB&gO<P=_F7=TCDmE1L[^2*[Wf
%b>W$QN%&qi9IC6fSXI)Pm!RKV/dk#JXE!Qar]-)QGNOJ!"Zo:ZD,IgPUX.(%#Cjs04s%@WS=.4=cCf)GDg4Wf88h'hP/0T-gCA,$
%#4LZQ3kr9B@KTFObF?FYOiPoMDe/at''7)BJ"M].TB,11Re_NhSr>mL3BXYs^R9J=\0HamGH>pU(CWZ32:M"Jre^i4GQV*h_jn-!
%RcY9,S?#"\$=a8"lD=//YMeGQKre?o>0Q_g6JffMDWg_,/rMGT@ts;YFjDj<"_p`#H!d\(b@N-5DtKhd@1Eg&3BRbp+dRh'8^W6f
%21b+Zc-<'<;\om8nV/E23h;[-1(u%+8f^`CSq1A^<o32%&r2]bpfsVOhC&'I_)22P=N.qY7H.;69ZuFGiI*Ks^Jh!ah&5JUg"@gc
%4$\TeS5:RQfF\n[R1=o)i!9o1i'^@Oh(!B`2mH[0`BH_6.'Yq?`@6ptBipaWF\/]-A6fa^d]asqPsB-fAj(a"%ZH\?7qP%jf`AMs
%Ua"*B2Hc.P_\)q^dV38%&A&S.QCJ[SfpRHF#Y$Pd`oBS[;r0+pq3\M9:JT,bEBU9OERN:AS.BE:3gt+dF`c-]Ti^@`<#;4=s&;X(
%(EYa6Q5Y),3:g`PSmOJJeulZg)ESXLMT#o"OCU_A%a.#>MV3[)AgTG.n2*[ubqEY^/67K67nae8S@k:PjZV&QFHUG'2uQ.Lho`q-
%h`[W/l`TL(]bac)b+#UO#nE$-h!cE;N>r7Ikt$'7GBSS[`F<1=hMDftF\@Sh)P>iJ0\+F=gP`#^+iOYgF?nNk;>[cE@HuI^^V:G[
%5J.q5mmm%bl#HMm*I=ZrAK*D[/dFQ;k4S&<YF^5log^02@31(bl3_U<J=Zu9!AN.?Tr)J\!mV&/r(3QYh2W"/4H_`pkhojIg//??
%><+\(fO3kF*>sisN.W,gpDb*Uh8Z-C*>s$*gsSJ#"H^iXeh]"'ADQH#3!$d8E)ID`4O/al0gP%:312!VCSkS_`RA%#d2A<-ZK;gg
%kE-po3?B%m0O\A*f_*I)=5`db@^jEnQ-FJ]YJaP"SJ&N&fVmdQQus%r,_jF"&sZ%"ON\WD*Zu,j6q\*o93mohB#DgeWD4q`0ZfQ5
%n6%)*h5lVF5AV$5K3sG3,+o:u:[nqXKr9)"WjB2r;eFX;(k]%^Ba%_2;)S3QH;(>!>L>:*l/W*i-GZ;VPGp(T&NV.OYRCR40Ia02
%)/cAU-SjnbH9`QN:+FM5=fC\eUsC1tWihj@,Qa%i8q@P!W$ZcG5te;>@#qO9L#,*0%=%'2i(sU`':":cNASf7\:k!TYUI<(#6`XF
%7PHZHTu!dlM$n/aB[f)+bai/QToJ6$oQ;ul<K_1N:_a,Gb_@TTD=IQMRB)>nRPb`/'NN"@&iNf#:uq_$pl#C`LA*Y?KUi\ra@"$>
%-#V-eksq<"ZKN#,2?s'cQO7I?"h7W!<'=CI:S\AkoaMfeR/IB7Io"FB?^+q0r?sYcCf#NalSDcf!W+Phe2kM@K5jj*b7?,+MW2-c
%<8j^,PEOiuUN)BG%Ha,'0oPPC_'=5Yp!6Jh3K1klYsud*-a\jhA.9I]'gG'Q),^T41t"A73.X'CSn'rgRQ0;4d4WMY61KJ4Hd=T'
%>ioig7Z.;l!fiRtLn(06E<<+U522J[KB#H%SaV1kP;3$b64?oY/e\+I.DT>NlN/C0,Cn)6_44rcIK\#C&*>u@$eD1bSM=!2dSttL
%2(N0c.]0Z!(0FW)T^TaEXA&VQl+!7#+/\jl)TbEeEIs>-6cS60o,W&EEH>g3'HD\?;sAH8O]Z&S/qlm`LIsfD#Q*VU$0A).Y_crT
%7%E'19+V(X2OuQX'LW&G']QW>1)Qt3@*!dli$o(2;$i=p5iH/KV?:I/TGe@/QND(BK4p>[2&R7h`8C]3,1m+S%^:f+"#f';"U55h
%?km?:9O;iB%@E)[LnS.C\gB5b'L^[Z8$292)[iNc@`T0=24WOVbZ88Wg0ibkZ'VL7Z'Le<aW#UOVYo_ZlUBB2:d;qF#/^C3[BK_`
%X%SHC]`E2<HuJ^(keg:gg!d]"r4=&,C>5[[+mj#A+YD]`h0"_fc;Lq]^hQ<"$N(SnYUTK!eD@n%hjQ\/GXYF":J6KfeA$;/M(#&7
%9\=XQ1VA%eIru)*M-CF0/Jf]`j+K'2+lUbSD1n0IXLUkqY]cR>Cfcp#o5I5\8Q#=UjGQm\N6)Xq9CNfuDXJs_mjfGV^[<8!,X.Ig
%hYVjBa6,a2;[GNsYJq+Om+!%GT)5VNDl:U/Ae64A(Y:?6q$15C/,?oBK\sm7c4W=,>7/p"`r`+JUsl]nC"a"CHD2P=o*b^Yn>DkT
%N,b35<"/;q-_Xdg1<YQ@><>smbVRrjbMh;N-spRX@^<dgRH0E#.QM>+PgcY"KEF(ABkI[!2+%H60=,3^d:F-[$T5X7&,]ktDl:Th
%[JQ(_5XNa8L7=+o%p(BdfFtC55S>+>NcFGp)M(E[kI=tt;S/&o$FJ#Z*^a)s7M5q/Jn>d]\@Tab2\N*E\kSD+[6!@uL0C<Oc^]_@
%8SpbZ,9gujUDB",MMDRRC)rde=?-8FcG$]=ceNdR-f/gg/[isI-!N<>&YCu#XPN=eJ+,sYal@I?e,hLVEZP0D2?$=GRaAta+!;Oc
%Ucc0,W>G=,ro;o4F7f"o^\*)m)HRK`r>;mfHu?H&01"\LqBpH[E_EXl!\cQ%H*.M]<EMAIL>=Ikh:J-dVpfLpcsf\o/U/1
%:ua^RrL!05\]4@ha]PF^)"qII+KGrs'hsW0M$[->iecjnaE<0s*$`"J7rjja_Z9LX"kF/>fPEg\TuItS4WrJ][QT8P7p1?.^pHL@
%LZ@@G0:)<^+s'CUZ$pdDHU[k#T<\=]e2XT(1JE%K<M!WIHW\s6R.Llh3R]I$Y]`h'A&OY$E,F$\jKrZ]<Z8+kp3<+#G$G4;mhjoA
%V6AoW6C5*XK7"[^AS\fp&nWfem$GR?(-qaWe,m<,$Ft(Rcp\OV5-(h+*Zh,=Z`:#[8e3I?<'Ph2/F$ic9WSHtYUFoug0C:?rOLch
%*rLR-Q_/\Fg'8>`b^?kif;`fsj-ou=qVT/el:UdgD:^MPdKmll6\oQsN[`A`!21b^kfs`A-@&_"GKeI,V`qK:@O/LFGTVXj4t-:)
%Kq6hF/h:j^]2i5\YAcglD)0a>=D"$K>Khj#:k.K^05?Re_e`O%%l,25(fk[X(,E.UZ&_I>9:d\BN$Vs5d94m5#4mBf#ZQ+F2e[38
%TIW/if4Sa#/-M`<.%)?QFF)rN\(@[iG623&Qlc'cRXfDQ9Ch^EMI+EL(P5ION]k]Ddn6LJPU/b>4j9nAG(&Ku4c%_FZB2-iIRgp:
%''S(.hRbdIh#)B3)&0HI\!\=CJKot+@%fddSOO?;V/P_@b.M;*J)Y?`n8F)/2tGgZ/R,ER6Nl>!$[eg$fqn;4QUuT<S(U:V7_a*n
%9oK1B_oss%[WG=ss/!?G5/_'q]sMp(>l)^!cg4];7>t%iL.iQi/s-8d_#Pf=a>X,l'M1ijn0LgpUM4@;/qJ]qZM<HMZ5G;l)i!*\
%q!i&sNdF3^X8FM>oV87f;V\fii6JTd,ZUoagh_l19\*n)\\nkep]U/9`Rm'H/Bme9N$+V9?e=XW&3s8gojb(Ip*q];dd44nOAN%;
%HTWMp<i.+\1+"*d.f*PB+$amDCZN;U6\+8(O=UDH.ld)pO3H'qO_.9cN@i=^YK.l@=OGk0=JT?!TV*H\#q7E=!.b@PB.s-2*(7$G
%0;ZMEiciPY%]A9eP$Up]KbLp7>\Pcn%8OE\B&\Q/:j!siTn9]*a`s!8cR_OCKbCOWW*KF=R13OQ2N8DabWg'>-ZSCe_U:pk2]%(n
%=r_iZ2"&4?2[Me=JI59ro(U!(-\5od3^_+/,0p/VUq`XCZP=KE7t%!uX9nF.L=WTScR.@>F_W!_omK'nk^C%p79<+LP#p73d.Ufo
%5$k*L*m'I'^YVM6]M*jg9S"1]'Zc^Kfe(LX3NpSN;)BXBr?"G%3U0brC$h]b<o]2@89(gJj44EV[+&J\:lRbi$;7'C3%];7ZflN^
%GRi!s#88C$!1/;i/<UI?E48`I%%sYk+r%VVVi4;J0'NO3AP\tqK4//R(]hCe6ESX5a/PRefW-SJMd5HZ?Af5<4/K(ed`R9,#r@ok
%970MAQ\m'<a$BuHbl=RKH(X9;24E_DVdSu""n'\s%Q-,[89Vjf"1GZ3.D6&B>8^ds6P``k6Emlj5d;AQ2kA&nCokg%-B2iAE1i%N
%Q0kMFCo=aQ.m8f%ZjQrK&t1_::_lVNo9@32olh"i-tN&pW:WQ7,)]j^`33SE7Q%4),V]aeM`qsbRd`t7[VhL5'K=FA,XE=#.*X*m
%(O8ETW,#Q*<e34Lo.nE18oYHPK%"2C2)ph@,us<sJep/nnV!qQ1P_\!'isbJ'1Y&63(T6`mNq>#_DRF%`)W5UA3]YiM9]Mij*<,s
%#u7]_2uA&J^[LOO7BsNgmI:cY1RAVMir2)aUg6!u^6*)\h@T;.3iEWl]PfY;,fpckiZ4.l!Lf7O9lV;[;_8L?5l4O@5k'h&JkWEl
%P3!L',iYQ`P\eF1DrZTON)W0S0@aQo,V>T7)q#!(N8i9Q$'!nYU)G<j?_])9(=Bqf$YR9V1.JKGUf"N-*;H:q22l;h&H@6KG2kWe
%0?Y8rG9SX((h%m5DNnFO6W"+V:)H!>GpbM6$Zmc%W?ork&%lqQqiekX7/Lt>AC:@S]<Nr2A?m3nm0(JE)'ujX^NG+b/GA1bm79=F
%(HZZJ)tiB6f!]r!jEa=24PTh4l@X9B?[G:<;VIM;RmeslPKhLBW?HOEbKh^WRRu3OnlM8lCerp9QbLF'Wu7[8KDs0oFI'AdB_K.i
%D-*1+:ubH>H8*CQ,Xk"YpIJ'RbVjtMh+_WkCAcCZj!$BE>7C<IPCE>9KCcsAXEP_190g#$ocT_SIb]@u">OC&i(7bYnfEH'MOg>W
%6C;[V#UU&,-H``YEc%Hh-:ij>BV%rs-m37(_E:Q!U:2I7H2L3rXs?ZROt?]-Yr"#^P-MAC-&\X33EU<fO[s@8Q^a+8oB1;K1qbeC
%YH`AB9Z$NBU<BF`'n1*s"uDTgZG'n-Qt:j1">t'q!*T_tHcj;aK2CT\PK^\;aQFdG-,/Q[24D)G0_n=Z-SsaQW3*=a,NZ1"2/9__
%Om9GTbcrHT[`_##36',JNc1fL?l4%$N*^=Y`7iBR@%=%3dPT"f4(.oL<r5QSGtk0s/5*sP=M#D_>$hheq>QZPh(.O^bpDQ+gqC#(
%HBZf)XtS^<hQsM3%HlXr1ER2M!di]^aGP0mH,c"OX>n2C+iJ6*iXIt3@?2)gEWB42Rsi&`hqtodD(e\3quc]aV>VpD7X'(BkLbo[
%16$A4XKskHh>@_'L)Y:O1lInb;5T??Ku&*6VXEfWmHbFc4Q;Ug97UFR,X-KAe;QQHm<5CQ7sdg2^.a^I,[pM2OutAcF7QBF>S#jY
%-elahjmV=e>teTh?hV+e`OgJAg;2P"gF;1#ErW:JR*0i>`m,TF_:Z6H"k_isqf]Mc2g]_[A#K8440n>CfC`:\hQ%H:.]gn.PPQH7
%oYM*'`4?I@FW[tHGB`*qmE936/u.L,GUcpYf`&)-Gn`RqhG\s;O6C4kcLi5,5!>^kkVn[]N<6iafg,<#TeEENGDA-J9b*uF<TAS]
%bq>Xao(9/rg#ZPMkInW0T<l#]FW6#nNdGo8$-skWNs%R7nY+:.H!l2,HCcC-0t/]_c/cJjH"$4N-D0@b$P&;(JDDi8E/,#ZBl&52
%hgh\%@?W%PBc;l5&`52JE%8_e9480tS.\YW].2C@<NO0L`C54iKK-=;jh2CK'&Jl7gPtG*4FssQm;+^/+!a:jq49uJ>ms.H)3Z:T
%i5h#6VO9ZniRq/(]9*u=9pnl)\e6^EdUFem.B1FZ[-;b=.b9B)+$`@0!R;X[5["HqICe0DH*bUJIEA>9_L]-S-6-hjrjoFFGeJ>'
%r.f!oA@kEG]i)k(NuTF70?e:$m_lgrX4;C_S7#!%<.aSjWO?\FW=.n-Kfem?ZO-4PpCilbN&rS'<8ao&LC*0J<p5Un5_%_*[#=!%
%bI`j"A6pR(RbS$_Q&#0/+k5r>D)H[6eA_se`e7>7oo^Qdbd"0jJ#2)XD><1.cs3Jc)i<>!?p:+:Q<]*%\Kd*g=D;=*Qds)'`aZmn
%p*pskZ.0'E:D.6AelnV?O.G?L8&Jn`8UFTSm4]pGob&R8^<=*tV&PAV>k#nf\oQ.[hLF)>G5&?!muF`/pU_Zch8p]+R]T[j@\tC-
%+G:%UDrGgr*TfYn&"C^_q]BlHo`q:1_qSffmiBr>+5NR%mDDl34aqK-H.tjG;Cs=4mn)D[q;./cF8KNds%7A0j_"=L\lo%2o8;t_
%[F0u,A9AnsAgtai+$3'3=6(f"D3`(e8L7A^-aVJ]YM"D]Z5Ps2Z5O<`=nhN(X6Nib/DI7JNZ?fTK%quCc@qWXQ\LM_Ma<#O'Xu[L
%N""IMAnqg5S;JSj_Q%!@FYBN`OYUZub0P],-]4BC:fJ&Q:NSD*Vp*jC0_P]D%CXfHL_OYAA;d)dJTP0E?\Yeu$=S2l!oMlFN-Ajr
%S$+@_T#0/E5Pk\0]RuGAH1hg;hNkl*j,-"7rge?P=4t18pCO7Hg%0>SbNjE`G%HS<eKS'+Ff<op2<.>NoJ/4^XOa=b]tR6OWJ-l\
%.oSnQ5ogeY9J\ad3bWdFfa+3X(N6d!WC>I-@.YiiC1)W-'m-n^?sQTTRUo1aOXf9Un+nqQ*[-aY&k"SYWAKhhlE/q*80,0bpBc7L
%%0IRY4eg#[JJT4!]#o9/#u,%u(E6lpoFVgl.Hbo_9=iW%HWYQ%\Zdr8H,US,o/1nPB_`[?RdnULrT/Ehj`FA]_QZ)3IGlkD:Hn's
%?`eh8meE6&jMB2Y6-?\S"T$admlS9ArhOHs>HN\RHF@;Sc\n`$Z#8mbF25dN=3L!JV`hc+bVM$#^hcT7Xm=0`(hGGeI&6%Z1Pa:+
%kVd5OdSpCM-IfO^8ue=?k6t^h&[NY@eoHM7=]3WGX*YY[U3FYK"G`ll4@T,tWa??^-W4G31O,D9Fc*%<HgGPb#%$.rE<KE>W2^_t
%\jT9;#kKn\atQVEEdA!lqu[$+^Z6L:>K]5P/mM[RGX(?[:0*nIWc,8M3(JB>_igL@%G;T=+3i]:=["YMLunriX<@pZ;K.a"HXOUH
%dnQ19!ke13dh!Kn`i7)&[;#T\4_]N#:h-+9r%oM')Ktcd2S4c>*!;Y)H<cdX)XO<bC,X=@XersT72q^)a$jk$dMHX2+MD(`\FSY5
%[Ms9B.ND>0S(%%IPGrQb!]Q4)m_?VUI=9lHijMQ>8&"h=JN_P>+cRT_AW@:lPM+Qe[&J.NVcj:>H&d0=,HKueWB#QFEps.&T,HK*
%$8=M(J*)FRF&I?Xb"l*Wc;@td.X`f<*H^>uauKN.J9GserIo%O(U2HeY,.3>EdH)A[TCu)\a\oak03W;F4rs?F@"_<9QrO)nq=#K
%a"m.@17pNU"0Be4^C@9BKG8Jciu<<<+Es)i:bf%@dRN8-`+Cq7LD?lUo3ckkBUKQBSOq9mLuVuZ_JX,,-@VmGc">8#Zg"'<mWCi@
%%\m"KL//++*V?j^2/TPW4-Q(:FAS1ebCn!i8qZ`sG[%^u9nB50MYlq^ci_k/c?68L>H1%=7c[J#p3DQF/ZUh1IjGoUhpe*e^a!V'
%b53iehRSGG:bqp92>=8J?=$+7P@p)`FkahEn'AFf0up/F^ctY-IAk(VfW>D#X:LRtajTe]M=oiuWq!Z_e"LSMh3>?HqHSi'p749P
%[n0f4WEmf=!bqngGdAe[WDRjRSSb*JqlJ,T.:;2X=a;oQrC@p=c<Rp(?&<,tn?!QYS`H_SB&1nZ>9hJTjm0TC]6N[OjQ4A;I1\7s
%U1Vq9E%@8ST59M7.\ds1FJVEp3k*IrNf-#Rle'l0Mk&=r=bKaba[*GOm`&OICi**TgI6ku8L=\Sa%Q'"Km#<=7]I>sa=:4lqN*ZA
%9H.GHLc(k>7=V'J2!c2PM[?$9P_rR!L>\r[%fT^hh>CJ;$iV/^oM2)4?:N;"CcS._%"bHf5Jkggpol*m@gBQ4@bco_-EWRYI1=8*
%p*Bmg.'Ok6j;[X),dsU5EP3\2FPUK&Fd1$,1?EbeK/;E'Uo\EU1U?\dYk25<\aEnS'A]\!4pAE@V=-dXDc4bL,,S"q0YtK(^RJFE
%cV=*#AA-ikfj=NVDsS6(8Q$N+)7*XRIcFiM>t+jc(O"3$=[e\=0Zf]!-/6_NAbkdH1Oc<Cs,#u@OTFIB(LVXbhiTp%]H9@A_=tBW
%WeX&K#T-Xk1bBq),i3!fUmbdFk"(E"$%OO'()!*TqPqM9.""pdP7"ErCTZ.Zee;i%!mJ6'RJcb.q+I^B,`s%eA^)ih5MP;i5q\0N
%EbRXC(sdCEX%.t/5ui$SUYKRB3.&Bg3"k[2'LC(p3oECSJ+Jk](qdR/7EmOHAe[,!Y4"e^h0C2@@P6\1@4gb<dr6LR/.D&Qc#.:1
%8insbX#YBLqf8/]TcY%)Y=Fdn.6n$0%Mo"h@pp!f&I#5?.s!bjD=U)WY@.Y$_X">O/]7I60PPqQ+1pP67u%IcIrh-]2m_O"<d$mS
%?;Bc5#$.[B,kKcMRFO07n_VIdm^q8dlV]9e:uf(MEcoF!A>!'%^m5jIe0B.dlkJTK*k'C+euXu[CLkSVZXo,W=C`kM`N[jll#8:_
%eiHa\Bp0Wsnm7\pH!$Pk%D"*rA1d@^9pY0H7P'/0DmNp/RKbg=b?eB#D$I'G18\g0(NNr$m'$$D$_HJgT&pEPjpRdC$_HJgT&pEP
%jpRdC$_HJgT&pEPjpRdC$_HJgT'!A-^>N_>i+]p`Bg05M06ip=Ga^:1RX=N\V]@nE]ndT*0hiIi$r"neWb.6kfck4;pc<$Ag=!R0
%#b)]AUF7)#fdP9</8_79WO6SNDA762X0E$"7ZfW,-JB@:L@m"BP""[',SSeKSY8pM3'e>)o%$X^C#<J*bAEr+d$\eZeN6)T\,D<A
%"KM3FlM[;CYWI0qOBjpN-a?kNL8Ag8h,%Eg\.<n:OQF$f0l%k8Q%p/dGW\e<M!!S+X.NZ_2WqS7::<B38I6_MhW-DmJ:?I.(tQLO
%,.%!5hgW;RD!=5fh5Fb/C[E_#*SY^Tn+[-"&&J/2a&j6$a9$s:+N\glB5Bc6b&LA2H(#3B7*(klhgKLDm+`7c;XqH^JV^D56=c'[
%0`IX7l$o]&T$PKXX_=oGNK^NV>3JCJgZPfhc'D26isi'Qia6e_b@-\27e'u0&tG.f%Hgr*c3qh/lm0/T7W+g&h.8OmD""7kj+9d3
%&3D_\*-,T5?QQ`CS\Mo=6@"7;7\Ri/.9lUYVWE0$(#Yo8\*X%d*iZ%*(?U!Zg&7;D;l`G_mjXl^l+72N-iS_P%n?7u6MCXMi9f]u
%h9+g8OE9/l(71@<DDTr5f:;PL=;SjHNB3EP/eNY]2=5&u4".rB&;+bU[Q%QI7lL,/4i'3=#B+MlhYRc4\@Q6rj$fD6LO3^Q<EUG2
%Q\`URrhhmnj&@9[qYSHE<A)u.aD87lArFJD5C'dJ$cGEIEIVEC@f:sX[Y67mmk+RQ/K\>7r)2RA)F&_2+,MLs0<uGefPPBM>Oh\D
%dD[V`nHr]@(h;"NpQPUEk&sh5nUri%`3Itb+$Wrdh6>#k^<H/^8H-5ba;+?h.@"\@dX=m4>fH[a/^<J?gD^j+]Zg2cc2-9#YIg/!
%GB869>tmK>iNU&4iI1OZ'esJai!CLpJTj*?hV_e0i$Y&\^pq,A%YlJPr(]G#o9Kf7M;jpUhVk]#YW("a,kKiALVs,i/PGCi=k04c
%HaCVnmr*INm1$*X=$H9EQ&F>Qhk).m6-/\?&Xe(KRnh&s_[\_U&"gR)W:+-D@5eUb]'b_S=S75GT+UBFAfZ%.e@7uNcfk&,bK^Iu
%qh/Ec+PEI(rpi.,p*HkiM/HZEguj$Vel_YmidH`4T@*+C'`<W=V`Ps?kI`6J<F`XK([0BYH@CdIT1drkBeFRIbS#!3/EL??gHnIl
%QUq'JV*CUmVAlf.0?8haW,Wh,n;1^r2ofS,/F]8J5(fn`<,BH8d1$fE&/RltLIF1-MK44ZRT[eqG49X]_tniOSYudlEHtK->2Ws-
%i1G_fD@$>H?uj-T#4_XR`()g?b$e<H$9-HNR%e0Q`&<3V)+S,SmMTAt\1)csX]Np!PluG!b8ih/+t@).->#Y)Qd97//)?-EfILuI
%MVW<V4M^f,GB6Mjnc>l9IC@_ML%"p:S0]idgRk<)UrJspCp'6<4l`Y!TSI'l5,S'NOPjA>GVP/h0!Mg"`+rk/o"OKrH1Qmc8Aa8"
%N,2)TGBXo]0E*^T;+O:3FhGmEG0nk[h/7ieY+rftI@V#TIqhu2>7YP:PU_-mpCnNG(CRt?("Pbo?2A[O^p(>#P,;[`a9oHMNuuhM
%5KhQPqT1N,h.N.@kWWEEI/_&%JI4ST7^/:VT+J$a4c-@no$u(D]`.$I(U)`0Gl>Z1:Zd>_o5>[7qY<)B]>hTI^A[KS5Q10;]6DTY
%^4-"DLHg:fQRhm`Q2f6AGBL]4n@QFtIe4Z[i8oishgLRcqHra@^B3dU=6]!_=9&'AmcnT_`p\BLpsG:[P=tq&V>mR/#+1+7Y3q,,
%o6'=N>boi8mM9r]^KtEf^VDk/GQAQi?E!r.%JV*a^EX4omD3@;8N"kFHbB*QdgnJ3X5?&D[csrDp/l[mTEjuR\[0OfkF09;W<TEp
%*kG<j2cVS'Ii-\lY>H:.gZf5K6HP]X0@H\S]5=[F/QgrEBC&+!NhlnrE7!Wf!J,:g-C<poceX<OGFIq+eoPMoVL,uD0IPTk]0r!3
%ekJs4:"'-/;>/bArADa1gEnQ#V4LE1K>&5mEfG:=C^3(0\%FZ(Qfq>nB#"Y"dr`?ZlI*-*Frb)HY,$ttWFs]PTL71XpDrDh5EG7k
%=g^HTmHZ9N1QqFB1g^I#A=r:b(MG77qfWM+_>^(aOo'El51Kc-QJB/qlt7g&H7mUc3;`ff)38.'aIMhn4W_?<Xnpm5ohG'I^SZ<0
%d76?LcsO3Eb4HE_\m=K$r3iUk?Zp/5F,<*GOf%_modOb83l@B<\"bq1QX(O&>bZ/n)VC?K@G>n:cEP9r9b7*+M,DSFqPNKWHp*d4
%e)B#iQFBR!qQbrTcR&FbBjc!6mL2(FR^MPlpn0:0)"h..DjgkjVu!i9g;D;ABD?Yj%/R8d;9pQb\nU')$RfE;VX&BNFZ\LpIC\T4
%4dYB%VX$)@2RcDICGaLVRi_35';M-loZ_mgZVDfakIP#UjQ^ok#CSV:URk-N7FQb\S&^S=,=3^_VFqNm6)&#e%&,-?s->V7B4-HY
%Us;X*;8qA"<gH/@r;4,NWCn]YGB]SjBI!Ef/mS.F_@$6p6sr[%'GcEaDET6hgWM?.^&]a/`\kQCp8P"'kW1ooiLM/O#88#%,pJ.(
%3>=Z[i1(0QK*M\lDn)LK!'D]:0eii<#SAmBGSl;:3eZe/\"uDqdYA=mgI0+RePgN!c=.kF9-cYgI^RE[4Bienn<Q98c7OS7W59TT
%Q>;Qt9'5P,ZQUU?Q,-P6o[32m:]ncr=l0i]3j>h#6$J$^kI1jg,mnb78e$#0*fkEFY\p6@k](8kW4qj_3IIWEOmMURr"`S0X?&`U
%?\^,M=*."\\jl*d\B\0fn,LW*+.iTpnsI9nJ9%dHhd7UaT'8GU`4RofW@cKi#]'@7nfpuY78/DR,^ju;qb(FEYW@uc"tGFq+R:H!
%K4>=SCSVd!/&bU32!e<X"TFRt/t_?3@NtJCQR],p$K;lQI+$CHro:Q.mVU9r]_\""D"nogckGjnW$`#l)sAodaE6m/Y+4Ji]hZ.V
%_+,Z[mNWC'cTujYFFD1n^S?GT*GlCaf9cb*,HIJ`i8*^l#=lEDkRhYYZ@5'r&L'=^`))U0Z$Rk#_8V:A"U\O%5(0?@"!_]jB%&hl
%*oWa>'C;bN:3dQP$plF0LEi.YF5V$*ZDc'4;??F:];X#k_aNA7mS,!K4X?iZl/Jn'(*X5IE_g*I+j,JKPe%E7i.X/e!T/q`JM\&H
%8f75u6@>]j\`KqVfN4[<("<9e=U#lVKd$m9'k1Uf^<<O0=]AjBk#GhB>-PCG;':u]O/Z@.!>^pk9_W[g83kp2j*D7oU3,<JNi/SE
%I>Ttuk=OZ-esRI;#Eguh%jVe_]eU:pB.#R$R"=E5A&==)+Z=Rb+f:u0k"%U9kWR+\:9ePTV.A7c1s&'X8rVVPm/r:p9IUJmR5Oc)
%dBt-U0fgtC6*%YR,6'KEW[!)!ih9G3*]#AIi/B?c#56_Ci,NkC3VE9R?>ueL4m9epV^cet-6^gnF[SpVl&67G(c0MH*op!gREbsY
%B$X^&LiD(hRRj[A`qcTT&'^qSj^`A1bJ]#=(?>Ua;[ct@35"nb:1d<lG^OqKFhHhIZ`6q2+6JSP2Ua6FiY0Sq-n+ZfH=kP1%Mj:4
%H;,N6L_lEaU'p?]e1.;,BHGjY8R$;)Qk(0;@9E)^*^c#V/8!_.:f!,JHl<D5<QF+;%Kg_[m<d2EIEl:T$FN;GJkdd`>,irMgHp'm
%FIM)AEkg8jeGQE'_P"+q"]ASHFEgY?3pE"3Z#WVqX4K\^8h>3(S5c-&To1q3%<d9Jq^Q&6-Hhd&_X+%m:;FILM(jf\m"*b!&<hom
%W"$PYG1%%p(`EeMd;17Vd[)M,&5i=<0pEfL=N,DrFRQ!X/mOO<n'MB&&524RX<R9ok)UmT(:n#7"XbtC0afUGX^j0TQpjN`VcqeV
%R8)u8\>K4i/n<UN.)7$fImTTRkp]pN6NV_%a*K.bfFU1#]H'p9D[s3)4Wdk8=b*7N=Y\;S/<-ot)ff6D`,s3W0dD0[p5FoeM0,s7
%[r\OK+H1EL+>FlG.SQX,8,UefZDBF3'U7h)W.>Wt4D6cK"1Zm$\>[gU!k8kOSI.)s($8r_0ER3R+u_^FE&f7o)"a03#7IJVhBfS,
%iF2IS<C#Mk\8Rj8:,,?>TYns#FC2s%=h?D"U6D\\l'7Yl:0o*#&;[cu1@1VORHTh$/"hf^L\0634IfN+#@c,0!];qj6oq-F&$UKr
%.h"9/9WYLT(1F9LAROBLNg[!=]@rFJ.`6;<jh/HpM)hNWK;sd,Wq>4WasEp>a"hBufa6O=<O+-[-;j_^ah6;_SmrA09[iTdN5ii(
%-EsZr7)VkTX+J=<[>([d&t-]Hf8s*njQ:FZR0Z`^-`/4JTL;8d72J8O$2$IT/E77r<P$m)LQe<6YQ3WMPim\fWkg[J&*.3i<O>2U
%,%>)n08%\"_?e3n+q2Ipdh%>H4Ud3OPo=$aZ:M!$KM;0gd0qp9bU9G7OkL"]#9'*hQb4+\Wqd])Uh[S9+,]C,';9"u40`BF!iBV=
%`P[[X8R%506Go&ThsL3+%S"l^O<$`Eq(%`AMi6R`!9$WE$K+S,6^Y=)9-*,8GnT2gdWDeq*A`X/*HR:`_rQ7jAY]+MZj&]7iE_s4
%i;i*2[XfMIVU'<53sDW1)[d><r"SlDD-3ii4<X83Y/YFgqo[elH`^g>@UT8YD#R&k(f\d6Sl&C5645erY")Fq?Fm6Ql1;:-42(_k
%DWE1T#3T"ZlHrV6<YeA<D*Vh93h'LGi?i=E7PT*):XXl`8@tCKGbdIN)F@HU*Lq!EE+/b\:D:=Rq0g4r!TR!AGgmWij7C]R%cfI4
%j;LSj;26d^3A,2!;KSP8H\g?S/'N9fX#quS,]mY&`#&Op1k6Ecke&JLW4%Ohc#hMdYi_$m;OkE1)SE'_$QcTIbcheRBnp1/T'>>$
%@q)FGZV\4-rk_moJA=7(('-X^^AU[7Y'-\ILquJblA]@:k,t*'Q&((Od\0;u_2?J5ji6m$SHD;OLadGNPS"]gV@PGDSV"`9dGU7b
%##JR!&4'-YG:$bhRD'O#'k03Ud+u20]r_^qA[t>p,QPPVH9n#8/TeGVO?ff$6d)3_5&@u;`+2-i4dg-95>#BGR0L/*ZH*RhpeDQ5
%p11CYC.%AcCT&`%_o$!ribF%(MTL`mE^U$R4"7G'_HZh@&:2db"PAYqlptMn8[m)eMo4qQD9L3!$k!rQ.#C>NQn0?FY`QiT:saie
%aI:1bY6UWnMA9?X1uWd,h_5YL:/s&G2iC!uXfJ_LB,E>.k/AfR2R_Bs'1nFr)N(TJ2c%n$[(4<iUK5gN#*0.HL_[&EUB[5;E_^#*
%4B9O,1e(uGg@I5_?tV^(g7U@&eD/bs<.PCZ)MVa-3KUZ&ZeQ"A@hp9._MH]'8Xt?+DJX:>LLHm1/s:SS^'c[(>aa5dY7[>ljP73o
%&go0??AN,(P`GQ96ukep?o9@gRun)l8AK#.8/AQ[1^OYG)=FUKLA9$g]UX]dG-?_pc&L7Rbc6Y\Rl**oU6NUIgf8qb].#kqn]>E)
%pq@PaZ&uW9H>NdrT8B@^1(i)gW02=1'"te!IGgl_4a/',>s-8C";0:2U2=b@3P%dlYaPC=mL+M_A!)%"OH;97?p&<uT9ue((U!-F
%.kaCRYdh`bb2Ma2YQ]0X6AU1p$qFhD(*\%(AR;G+;Cuc-X21J9?*(iP8e';scSYjTdUgoH04dtiMYU0>@F-pBL8n28CSaXSMak-U
%,P](EP"1!u12GBOFQQ"[bYjD][f8IYF0c8[)<[@mRN`=gZ`)dH_^'];eI%![-5IX^2#N9!NRc#m6s9XMhmkQ/o#"s;fsT*X"gQ%>
%,IEO6O'K%M)l&:se!9I!-3HTT)S6:%%/9^S[pJQ?[)fQa$mkLHq@unCfY6"$Ek;g&`C+"o\<TDqc3ia+b/;l"5dLh_;:"@_88AfQ
%85AQ8V=XMEG[/HF8b)mHiT9%B0Fdjb1[ZY'5qfMj#!9rf[,e]s3>d*L^@L`jI?elOAVrg[WFd1pCI.0kIsFc*nO"6<S#1h^[IbY)
%0Y>G?'j^F:J6gNWl+goYh[A$@jXr7qK\lKl4d5$FF;L1.PHmO*Wb[BG=Z-ZPIa>BU(0Zh=\l^Y:lUQE3a:l\U&3Z"#l7-I&HZihF
%GThK*s03l$l6*453eN?a]r@-2%M?hB35gSTVQHD3YpXA(lqUe[,]O:J/)+1Rl/`>YLUN,dD@3'2Up`);F.rpRj'\T7Co]jk=)F/'
%VFJs8@i%3L]P2-?hs&FeSKt]lih!\mcnK"W836$ha9GhtC-$.+BUKJV*^tb0IME/Hf`?q(M16tZJW@c9Nes')(Gf8-a;^j.f^]0I
%W:1W<(A=.e9O1Vc3n]C4ISajD0n@0o=-9hApko7NbZFt"_Zg@cqN,_r=^QOtBZ+pYH&^N43>YQ'nE*tFX&A,#kWDd;%.(Mj8R9@K
%pMH6EF#;J_g8g)LR0``+6ot=co&S>oW!4<`P0,SgQ\o,eI0BCc<':bW]>b((Y#@%Xd>484<`F>9*4Vl,QDS8s,Z"Ud-DulBRM5"<
%.p[Q*ijh@KYNj#+D.#AaM5Ye1X)t=m>KChg;[l1CW6&dFNjPrA1:T%Gi!EkEM^WiV#KpW],0db;#ErnRfR+Y_'jdp`oIdWV&7a@_
%!^sGG!\jku6RA$)T..sCm&Zok7m/;Ap#[J29`g(qaSV-]s.i/SY>4!cA:daST8A'+djjY#Lh%;68+nIa/C5)qOP$Xl_i+)F8M.0K
%jl)pbcEhW9Dh42^).#41TKr_BEtdN_\gUrtRH6bQ;X?cVC2&lPDt2m-V[V>VO=h:o4JsZIJ;E>a4hSGZOTa7ma0=#)06@g!P3Tcc
%dV=^*a_T;I>PQdI@<[;O<28lMLjRa.4iAU'-]k0(@!0$aaQ+X6@F._:rlD@m`*%e9>H>==@,6%1,M@C,[`H9s4-#=,Z2QFF!4OmC
%jfYUADk`h7f+U?iFrWMQ`R*Z;'FnFk-HNC_<`BOSJ,ie9Yp_iak&Ard47gh8_+0t*asD$Qd:I/)e6tutAOMB(-B;M15r]e!Ajj]h
%WA7>AiM7E8jaAPe)iFu)a%(\30FjB,*Cplt%s#G-GTO+t%ouoekD*J$<LL7@c5]2HprUV"0sLO93<G>@3H9#:E>;L1>fE&g,?qC)
%RCgiW1#\P)^*nCX.I"&EGbt;2q#s+jMr!$?SLb[A7sOeGYtr<SA9nq#2!C@=Y^Pm7b-e;<Qs5ghXpqlA^7A@B]6WLXI!@$u)4@dV
%a4-T/l</ql.4]SI2F";:8N!hO[B1c9d46W_j,Cl4TCQqWZ\38^gEJ5=H^e6'P<Lo-U'2#BP*930S]%s!)Md0o)ktcEM[XOBh.sn\
%7Ut4,A2k<52'N;o2XGac\hQ^Rhg7(odY`=EAf*CejqJ&5VPB!mN%.W?pinIPUXc*_,h3Zr'hlCUn/L*Toh`qM"!b@YLlt2eeL#/A
%_B.C7B/-u0;k;;#,P!aepR3/GXfJk?Zp"qG@&"!r8@)CS:99-S-R<!bhIU\W3]uG=fG.'F#:LjbUKWJ'<qWTW/8H(\U6f;D"&EH"
%1*(ATi.T]J6PXV]LagOBjQ9DAp#8D74NfLmc"m7'J7YGT.aB^",WQCg\r+OZ]QC_<]JsJ9a4[[q(^iJ8P,LrNKa+Xgj!>a-*")d8
%P"Nd!Mp/>5!@aU+V-4J`$$<.U-=JrOd,A(?!Od3idJ2@GmM:m&CsYU;_A1k$fl(i%;BJS_Fgb1F)@M!&]$F)52j;`56<DF03-@?e
%PW_KNroWaGIeCT#?2a3k274"0'^<!'Q&dj3%1k<9,*-a_8$<`i&M<&kmnjJ+i.bjO&;<T:!"`dr5=opT"K?'bO_p&I.gR>:_R^r_
%'%A2V:pM,f/h0iCe.Fgm]t>?k?.Lrii%M2mEZYiG,H*$CSDDj9^]IOOouj-cYq&k.,W1iZ8_i;k'ASHmMl&Vi];\1`-QAB0X]PQl
%ds,O-f2>8F0,il$Y8.@X\4eS/SKi&,!eb@23DHB=.O_o.$mn>R.@qdVf)uiM`H?d_7eTjZUu=GI:(1JV#:`(2P)8b(U*HU4Hl34D
%AuB\:ZXf#XODfa.o9Juo4C)3U%RV$H,7Ze=$jr2R(q6D+Ca4cXSh+9jjCEI])tV@X'D!>+"rCe,6kK)fI6G!mc9h,.9X<Anlm='j
%$&@9Rjalo-[Z%S5]Xb2,%`Afe@jYl'AUG0N74[snK^j5(2+/b-+fmOh;sdh"^]8_>SDa(0@O<gm<%p7%b[#U#`6^s_8I[#oR4ea]
%\Ca8U*^j%X>%HUKC?TIP7oh,uAi*U+`F6\o!;Yt\_BTnWNC3ARC`oLh9;Q,^00Z#l(m%%abeoWa,U]l5okb>hd(h62CuBOWl*0*j
%d0LQ<JJB#sgDo7`7h+:4Th70IlpFQX5r0WS2a`CnXc<)k7:@/^YWi.1Nlo8m1G)#f?kQDB5"PGOSoEhsb=-fc6)u9c'[*W]"G0:p
%CM79P/;13_hS_3_a95nCY3;Sa#uLbD$03I%G4je@'O2[56>QpL3f/X0>mD671@Hqh!h'ZgSRr.FD+QWtQ1^3MPu"m23pF=%[W/4@
%ilBW);&4IVA%,Nf*nS/a<bV*dKdTK`Yns]JnPWG[N$h893ojG]"e7/Y@YQf)_DFe(Eks_,<ZPn;'0M%cZ:*G!\0\c>QXUE0\=1AS
%>pij\iO<fd2&dXg>25)3NKm;UcKse`M=AW](i!!o8PG.dS=?nHS!P$a2&eY@WVeL8?C9Q0]1#"P9IsZLoo)2G4p201M*hk@QrFki
%Sq3:uLdN:)G0>VF1f.HW@^78RT8>JeP<P83Wq3VXjWY?8J1iIp]thdT*J:s&;5*B#$jnW"Ri;Fb2[o&PKYsAYX86dFN&hV\W9#.%
%j>!)/,_2PoY%g]LObYCHK3e"LD6#,m(9`L=bZ/s^_&hVjB;N4s7>;$Pe,n%1(eMPV@o=goDNc^qU_6O-#R($&B2e0u^q2+lbfG2I
%j9V17$jAQmG&<[t6_M<'NTGQ43hI8A&=Rq="F,an"BK<dP2M*mGV./'.@MuLJ4Q@B9MA/r.#;"^M9RcFZOUgn*JR;AR=`W/]>ulI
%jSl.NnJ8C7Rii-),@L#2!dV?_UQ.W>C^,rGP#P=u@^@Ms.jL/+Vo1gcHkmf'j\T$P6o+;:VlUn>>S<JjMt+N=+ZmP"9Tj0PbI*=`
%ehpV?7hXFI)0.KbMC=.ogt&$WKfWK6B[AMgJ[A<r>:9+GeS\Cs5Kc<,oCbl\2XkAt\4)p!etcMX*E72pM8GK_.!dSGeJqGP_r9k)
%L?I4LY=*<IEGSfHd3S.Q>F:a'Z4jJJi*10I=UU.qg'nFK#@LB9:n/7[lj=W`k\j["DuP$X:oG@Z>,69RlkiQ!S&(eOI]%;DYS&e_
%KG?#jQpIf2X:qkt(.H4[K0YZ_hA&&l/ko5n@TP^aXh7'OrJieCOL9h[e&$l[o%&TI<#DGH,-$D>kN!puW#"dV-Dh1!NUN+g]HU?i
%4W'abODq"!#CSkO=R>'?`$XG3Oe=^fafH[/U)A]4Jr`;Ts._FWg"2eC\_j.S$IuDBR""T3hQs,nGJXJcW[2+H6-k/NQ)N(u'$MKs
%#eZ[`4&;!\-m^5EmZB\AoGEL";.NEVH=>!E("C;<d2BTM_(i`m%5pp,3-(9U:XE_3`%t#GZTG"AXdeN\kFbBp%G-6qMERRY\mQcA
%0GV8:HBJEfQo/X\^.YG[C!S-f()D>eP[:(&`(9SYHL2K'M:aYY>;RPLNZoYN_9,CQ)$e&)I8CO#YfAm"*2)0r#?0D,?1:J0onjO'
%RM@6B@NKWVanuth?r/1"16.fZ"E*<9O&e7)$.+;@K,G)L[Fe2_7=/pp'0WCD)A%:2FM\+Nc7FQ4G'Zn?F`4OsK3b`1OC3)5mKqS,
%Q#<8r\C-"E;,OR$VI_51P8XN"pXVG:*T,[l^D%V<olWE)>>_72bt:L<k%$TWRG<U)<G#J#Y`BdBb!`M6cq.KEL;B41I^c1%:/rJ0
%XLi16-V$2Eff!.sVO51)fc[jCj;O:2[VWQ`C/O2aStK%o])Z%dXmaX>mgb!tO>E#T0QjDB2\$J>3:!5<;Y$ApH6VuU:HE!Kme!$J
%PBJh4HW%PES[LXlc>1o<3Uu2>;c+\5YjI:qf0/AM4hJ&n?jM;uSH0hr/o5\Hp7b<$[V;e%ddgE*N/>tA4\6'1[<E.?c<eo)=rJe=
%-[&cC!m/Zfg+cHs/;lA-$rC(b\Lj%@p-kas)Z5$Uas"WkH7mT":iOdn!f*(`'[bS<feodsO'CM,Lkc?O,uR*GY]HLpgM9r5;7&;O
%-d(+TX8oNY_W[HL0%n__S;0Iq=#G3_iX/`RMucH3KNGB-lV>Z*X1fU*qD[_.kaQ]=ffAJb]1Q&sMN.lRbqWYS.q&d>Cj7?jU%P?d
%>,afh?]iUmbF/[,9naf0*.jOs_3W1S`!.V+'h[dT)of5h7R`%Lo.]AD'9$AtJ^As0,Tk^D?uu`3@b"T1HAigN%!bA5K8mi8CpbW[
%7$;KNUbsP@c:g'sNU-X*(8a%<-!fM=AS"lW6rOQSBirt\Y)b?O91GO'HQ+!#iK;au(lJkZ5AG'J6&m,6R/TBYAu*89X4XnLC)1p^
%6WbZ>3h,T7ch19"5*tN-0a?Cr'J_sr$d1p!Pb=879#q,I"Z%)OB\sFL(mu;LXEtf!^qf_<F9E2a51+EuF>)VZMhP#)4q%VrWaW\_
%9K%M'F.Oq96=7/lTagNU3n?)r]5d@fMF,<P`<AnsrIem^!*lp+\O^sBHkit?(j4A6@Vb.<H9`J0SBfJO!l'K3YEV&`!"m<ja+agH
%$aVGi`9=6;.n^_Gknke.el8F1e?iX.C9.JWUQWe%4aNNOY4DFRSeun+,&.mAVh[_PKnIa3Sk9^.R0Yl9Me*q=P#$6J,1[;PGI%Eq
%P:=:@9bb"]p,%L#3;/99F`^5N0^'+Vf-ut&q+6Da,gAlqaC8b[:&XZY@h<S[L(WM7=APTF;tBY\bfj9rInV,73t]RIBXY<$nO4i`
%>OG3BRl!`kjXIs8V,KWPUPh_t;lYDi@J+;fOhf?%m;P<I6;e'tD)fJ1kE5-uI'IKm<8!c)[8F;jc\qE)JPS>hj(L2*3e?;C(+?.S
%./Nuhj>D[SrTAn(k@g`M5)l.qJXML:4\Q1:U+`)%"DTj03aV0jFBUOkVZa<4P.cJ+UW+c,Ub<USMh9r5"f=,q^c2`s@!bnX$o\Rj
%ZidNRhYlM5O&O1'M)RcuT;;4tmdZ]85sS"g6HL`hV>$B.G^:/u^=O]\6ch9tP!tN;11)qo_q$gc8+^hFAqRQ+lfOJkMh7j1jb"ds
%[%;CcE$,SJ^.'m:_RXZrOrhMC%]"jMq4$\<G8qYifI[3J8TYg[c@CUHSh^LH`)u.*9@JnSj^TAOL[[/`lr-@]a#R3)c89RHU.p:b
%814Ro5pFC1_RrQ@[<2[knmOSL`s<kA=cSm_oN=5rRD03,&]rV@oOblei>B*5ip=Pd^qg`ki(C6lJ)_F[O3ki&Ld!T0GSRT/8;L+\
%3C!WnDr\Ba"srT'pd#H"gCjcPGn(H"S!G5mJd+2(Y>KT[PhA##LVp0s"0"qp%>?/K86G*>^.id@#!9V+1EQ_%NPMeuP-7Be.q*%X
%NA:^XBoQ^@Z]c)U78^]Ql%;\DbS<jbe-5PYl`Bq0\FJQK7MX7kW`W&e!/cUHLcECanQYXof/LY37L:to&jCpf-#s!a+2KUaJNnP5
%V1GjFXOckS)I/W]8&L+`HJ^Jj+CX4GZVm0#L'F;*g<AP$K.L;^,U'YX^ba2;'PC*%-+kG\JK/$;G*u$uTqggcks_Bj$4jIeTKJ_q
%1.CdW&u(DtV=A@-Da:3$M[)9:!PP.R"OoA'-3phM=-9n+*\E:_,>lM:;])TkVIUI:'Hhr^#R(K/*Q'2dM[U*kqZjcbO$ge_)8'DI
%1FF&H&5(o5nLZ]m+cGiR9ME;7.b4^l]L=87HL%m+=qYi[8Af51nDnGa0O@ol5r;/*]9ktl+FSPq)dSf_@59bLb:d.q]Nbp2!3[B5
%W&ZY-OKP-qjZ%GMR)#eC$f0*6,YAEBDXr'>B!-<3$1!6_W@"091bXaFCj)Qr<Mb\>Z<b^VD%<Hl"(6#'C+*[W7jDcg=J#fa$q'lY
%>*J>29h/-M/'4rYZoiKN(h4)FB'=E_H;N:UL"1p\YdMK\M-f@].X9a8,T1m>A5i#__afna.7>Ad35C9G4ssAE6k\V41tTB0)in6*
%W8h+)hC1&bA_L8I/M%l#EnWjKFpij8>\<$'GiLA<a2CgLF\pS=Z]JR-IDF;38!;OS,3PSt,2GR%F%Xj@.T&q:BT(@+I1fUY3@&,\
%B_?1fA/'6jQ4o!-ZtSDt,T<,n/QaNkIi!a`"`rNjU-?ah9fZ9,O^g-h!BG@qWVJgU5`Q)kEJU^Q$e-EB1;f?;K!M=r3%Cbf<;]3^
%Y!o3*[R:MG'S@n^LM36RWpi+`a(@3%5?^>UVD%Ks58b?28I52M-)mS+$C14#<6^<dM^!F'HkG%GBbmO(TOeBm=890Li[LtSF!$j[
%*1;s1^Wt5</ou!2$sCqF3><953R4gDG0?=2poe?@$<fJjU*uL"]jdG"XNC@/E#_P;.1QGRU1EdPPV\u^M(Y[CC*8Z`iP(7Y0,hgN
%#TJ4iB9@djm'>XfB.P"K#=UDV\n^qW2epD@iC8H2Wp2W75U)t$0aOIF"rWZOOp&<mIc90K(13pT>A@dMZP\BPS#=Zi=1]#"_o$H.
%(%YD"CG-L/I94;ROAf=&r`oT#eao<MN_`fJ`7NfmB7P8p.m$dV-PLl[+W$:S!OF<loA=(Is'VS8cDcN_#eJ*)15HI?Ftr`TpM]i]
%3DcMa62RpRJeOWhT[hoLk9$]M5'0A;rR>GiGe2gNW&_h^ZiL<NRd^\P-;`$q\"Mfg1puC_Atn),JBhST%9(;F07s2$HpEEJOR1%*
%ZWdk]OM'uWdWC@(KVie@<gh42c=>Wb<?.jFr%p>BLJ@=l0u#:$Rl6>&k&O%&KV7iS5lE=7dkHCXTCGXmEGT\f-lV2eXXXKGdV<mm
%97@"&?EJ16W#7](Zcp/E7CHT2'sFE)+HWoc8@XOE@aR+9T6^)ai7d?B/G,\Fn=`Kq!-Z#2ZONZk>EB=nps\^b8&]!Oh>GhiE-TZ5
%@d%^gZt6YuKVGs*-]9t*EU6("J6'IUCc^P-:3(mSpGf)8Z>YqnERD6IK'$s=1#0)UFSH1hA^O&C^@3:D?G3;Rcf2h0=lGa4"0cLN
%/_T[Zh`PV2P^R$*pLFl2CS[6X[D;3%0[TmND>S^d,]EI@?#uBJ8db:JLV.at;HS,Wa(:BL<!]Sb7)J0^e>.KUW^n%a4b=q5a^,C)
%U>,!)9Ft7!,)S;iRUi)JmISF48ial4Wn5[*8"FFYXdb/5P\&6ej@K[M(6>K_e0G:<jCopOTFNZ*WsF]Ph*H)2AmS^d`T9:1<+V\5
%lc-fjMTPWR'-KKt,i;VM]F&n><Q<nR)&%DPf";l[].#*HjV"WVY'm^SPH\SX_H_u`(o2!<C9e4f3,c[NV'n3n0!mT!'.k,Bfbo^U
%m"OJ'RA_a`_M9[D/qO]K;k>u@TMJnYD2Y/F`=`t=4&`#07[(_.'AmdBnr*-Oe!p2(dg!Hh2*L0H-E<n5^-l<d]r?K<h=\9[2*L0H
%-CUgH^IV]i]rD$L9$s']B##5TAJ8?Fj,<Q/l+Qp`e"/.nS#-F=C4]T+C\Q8D9lK;=9p7nG3?71HP3=JGibnNY;qJ\#2/B?h\q5Q7
%;G#(tN__;8AKI0:5tAjOHK;2?[&,OjC*6I*jMOMf98to^![=(/;s4&E+*:LG%2;=nosTatNXZ[m,b`oNc,W+L=H6fg<gETOZR8r@
%CCdg?hN5qh5O,dI+8,P?>ug%Jc^UfNGeL*A17d^`FEPqh9k3<"]0C8/8(Q$F9jFDc4e&;RIFGR!M;C(KNk^si9jFDc4e&;RIFGRd
%=NsRhSg-rg9jFPo4e&;RIFGQ5Kk/jnDnqK$?(8>l+8'H=*qV[uRqQkJmXcKpQnTA?]cWZB,[3TnJ0Z.L@ttg5Lns1^Kbd0kd!qNt
%rbWGS'hL]rjD]acjD^lc9Cu)B1l\AiT!qEA[cq_!*q[<5`e!40+@ak6IF\dUEmVGiP1Y&h/K3@]-k1r(Io3X1k]@,n\R#jLd_'Ni
%-UIQA%<l-!eAW$*.**#(CCH!LTT_Ai)jfc)iad%718A5uWCch43Q#a,:K*!.asb(rdRX[&d0klD"tn;&JX,]c@Nn7c&_GU@r*C"0
%F%6hdCLIoLpT1e4fLg-Pkt^9XaNhji='PKtc_p/JV2N=@Hp*ZY?%frKQ#s95h/_G#kZZ<0l,k>gCHAn1-Fu'/VGa9M5qWoDbZ8en
%:7\J6#M3\GH.;*''0c*M&q0C_e+pks7Fla<>m]>elIC``M'*EtbZtc\CfKC44;kJXJD3lSc4GEQajK+FPF6K=O3'8\%+4O`aB&Dn
%9_MtR"XTmLQ[gtp`kKg]NMUJR.J-=^7uNj37_J/73iW=fL^rTW(2?DKMiKFIe\4^#Sa;nk_lc%8?GKjT/^Bmfh,QDr\/uKU?j@r<
%%%F'DX_a/8/;6M-n6@!u8Oio(A\0PA.4e(l0>_B@LKSsMdbOuKp!PNN9,%R$/.J#mI-W&_#u*$@[lII5k_T9FF*<]kKKWd..D!fG
%g/@"+k^EC-A+B7h8hKn.+jZ<44&P).S8%g+49$Q^D]Mu#dB?lN12VqT9hsN"A0<Z5,h^6UruuD,)QG9C)iLFDMMRro/!frA%rV#[
%15nV!XPlJT[K`1=<is5'bW3(MXqo,:K(!"`5*j32"&705FLao6>YF>hcBcTS>p@5q#/uq-6AF[67$m`Ur582/$1*>T%PJTq_V)pI
%kLHlndN4%Ug#J+V0NB)07_s1tUu6joSCFo;!*J8C!kMd9TY>+[8'Yh_Ef/bK-:*8uINb%0`G9)1E&`$P':c4;S&FEX\AO"t6,EX9
%TBhkNIUm#gR`:P1>dbQNX,9G8*r1.E(tc#e=$Lut>KpK1)?c,c5;VKA%>to1R%@N'eX@Tt)%aT!;uPku#4ZN1?SR_MDN7UZ=eB5k
%p*c]3QRQH?<5?G'-4kpN3J-:G_.ehn6[GL,_$63f:R%p*%F(8WM*/P9-3kh4hdjOI+"/S\/]L@RH#ZP^eZ@QUNprl>j1jKC!d%.P
%=c;^-_2`pVh$[^X2fuX1L?e;Tc5,7[]I/#)U(1(2cEJ&sZmY0<A"m2j"e.q"ZqW#Gja2T@Vb*n'E\:DGc'*KPO!Mq3@>Vgt7f.S<
%qmOIhL?83GaPLYJas-D/&SSn`9IiNV/WE0?U!(n6q69$"DX:!")0RUWm7;Z%)R8Oh\E_J%9`dU*c7V8Lk^GusOtbn_\1>]DklMJe
%#O!&a,mUd+]9uothb@CMMbrV=TSA3KQ/IPX=u=J58kRcG0D+7TTs5e:AZn\*p[1@p_c3#u8?N_Tnk/.eHVW0,6#8)J:/k.Mi"NBK
%'jh_0dk(Kl0&gHr'Gl>`$EBdh=-X(N8QJW3'P'Y=7/fr%*>+G%fWZ->=M2i5Km0oUa'bMNb"@(nDg'P*`q_M7-3h/"S.Q!"FE'3<
%T!X6lW<ANqdttdmG]%j`G>_q>(4Hg2RVtf%qAKW%dDa^6*.'FQiZQM_bYQX1R4hsbbV?-Kq9!u)Y_H9P9ip0GXn=AN'$c3I)<2W6
%hO;]Yh8r52-ZRYb_@R@\0d[)4#R1;Q7P3<t.5QAn`mKuCoX2(HJ!KCRZY=[O+s-<mGO;2P8[<k;)7dO;eEEFCb?RhH6P_MH7$F\o
%mc(oH@SqWtjRh.!D04P'EikXFe1=4*1b!fT$3kTr]mbNm6h/4f/O-bd0akcgCDc\h$o]bAB@+gP3G.@k91r8Mf0WHK5=N5p7AC#D
%:5q#^/Csi8>Orh<\7osNN$R+Hp]JN^>A"YfQ:0ocoDs8>F\g^/\NpN&!2ZNCpdcQBdUr%/@<3'eRRJ*%CkuU(/o4*6?ah9oTo!04
%(Eil;glaAn1tk5LH\GJB!L&Y1\H3M0ALU]q"Z?f?X%D;L[T#08Z?^NX,(dhQ_po4=e@?+RVsnjqgPE,bX"YnD&lkD(q%>Y5$=EMV
%R\haRSCdtNi]]=jj\Wn;T-(+LdcpGtA-ZP`?g"JCM$`Z$,0WdU3l4bW#gk4^DMlT1%,"eXZCmBe5[C-[S7-/g5t7ElfG"Xd6,&WU
%_[7I#n2o]IYs*hK2c/dVGE0KoCqqWG4-a-Wfbf5<9F#E>]h,^S-MjS\.*bf9!$+''d^Xn5E[HG^Hq*QOU8/&tL=^u=+N*&n-Ej'S
%*oX@iiLQM=SV]qCX:l.:1V(YJ0;6"$RfGcH*Q-#R*J.?_-f+N*$X;'U&9sr6_QL#_=[k`SS.lssh\3W"#34\GVZ[QrWtL&Y[ffD'
%6srE?)Q!&X2E9g*kI2j7,L$rl:^/%X_do>Vs/?[KP>EI,Pb'm6"KpAkZuQLYi]3!$E@qqq_eV4KMJ&Y:RGhiL&m]f=Qt4cP#eR%r
%dRf'JG.eTX;nu@Z3-u*]0@&.s8=QoXc^gJ.]L8ZhN(ujfY-U=PC=Gq@X/ZAP)7Mfp\O>>FBHuR0ZK];.i(_[Q^2*j/?f$$kRGP?P
%XKZ]>G!U&4"?rS*Km31R63k'iJ+jo)..i/0:YSr0YJ398rU/E)rUT3bGL,0\(@(Gc+()ZedXV%]f<:g\HN*S;r6]"uF[1W0n,B&j
%PIN,mjf&+FHi2LqJtN#WBRdQb=$O=na+&86?c/4ZpFGH.K!Xi*?dW5So>49U@lQM#6R1KjaN8M;WOo]u?/hg@p#t6ci.!/:@q%2`
%3Gq)W7%cf]^$IH?#V]'XqH)/c0GUNVkU6@[483qpWBUOuJ=p^De!9kSh#64mF[+7[rQRa9?kT^Z<02o<n/qsFA)mJYCC$;iMea[7
%0ba(ji.lC)C>g%Eb:o5IG8pU8pH#)(R@?Q_5`ga7MH'-l*d'lH9-^PCo!WS`?G+Nf#p.Yc#:[+l3i*:`(3>Pd@dXYdi_&F)=Ni"8
%l/QG3ag`(OpT8&uY&MYN!@BjA$iGD;1*/&Nh&-,;K7s&M\Uka;(hSg5$DLHo1*PYc,9ksg,LluO:Qd,IdNnl`?5,G/romjCp$k[R
%*#:[T0Zm0,Kn,DtL0A,T.2sAXS5'AA\9PEng;HrV^Zt$tU0`(A8FY&4"LiS%',LF>koKAMn//R)-TW;#]Oo65$:G$RHCaIJ*Q6=c
%W70J,%0qbb[V#0VjVegW#..JgZ)&et&sDcqI6^;bCm,d5\a$2TOfW+*V,/Y7h;E>`;\ru3E(_@e<,-8M*[]!^LrC`e$-FH)?1<[+
%iXHK1<<U<SEkfVak/Nmo%IbprPJ(tV;dY@/LM9QE73o]OL"nK&oWA9ffK1FCEJ'+XB8%`b"GqQ,JY56?3C6Ri=pX@G/!`Uu,*o5j
%XPkb%o#bVs@3T:bZ'ntYeVW7V+c`"6\ReN8`Ik-?!q4bK<J[nZH'0q_kf?DnPcBmZW&T'_&mk#Yp3@UY#4n,-P:`DCeHCOQ$aGrj
%Csr9;VeQHV1SN;]n&\(MB2[XnK3(Z_b_2)'08W*(V\"hae3ZOaJr"$Y!]l4p]aHto83PF+Olm,/I_\A;"D9#,)MLisd;l?#4X8Gb
%kam2G9I(H15FeKlN=I3XVC]i8q-au<q-boo6g/g<S\jhhnf\V8AdiF6Z5:T7I#MW)%SN$Cdp"ItQ^D-QGKrJXFNj(i#FXMJ6k7;Z
%R2AaBiD/lgh+j,-?^=AOgqTT4QgsKfg4'u9pd3f^iX9@mb=X>6$.%49T!S]R@5&^cA-`dT;QbT&m'[:3D+V$#H9iBDOeb+qk*Tkk
%&uV;4;`Z5eUZ]bFba$P97'&TGX5047UQq%Se+QH:g/G9nEqQl\>S<pMVI1`kh2*`tXCKqFX-)V1!pOULA!uOQ%!@?hBB#lC#Hc[S
%AO+g2T@X]][J+_8g6f5onE-6J)p*f3f/Oc_AKbS*4XS&((*U]!Mjb\><U%GUM3t>-m!0aj\hN7ln3B=L""u!hPfQ.t7&OhumZ^Hi
%"%6c92\A@>O@k>(0TjQr6o9iOdp&,nq/AeAAWA%)bm@[VU:!&0_R[)<KO41d4R8GnYgN.J<Dk=\6&fJcid`3q:@\'`6Xf3f,m[jZ
%I7%X5cjj!C'-[oPS6iAFQ:R/u5fU,$nKk.RKG_7Sa>\[BF@%B?+:ik`[[&icFf0;Jj:X/4'MrFhATW#m1HeoA7.9t7bIh):=(:8#
%\#oH_2sQ:u$Jk4?D):p`-Y6\oQd>dTMUp1?\?'pe>,<s[Q!>?5N8D6'EQY:T`6**nN[e6#1T,S\J]oKY'L4E4L.X4FJg0"EH8]u9
%*!siVUC%m*:Sb8K4kg[k&_O_F"'[S7l(a#*F@CPdC2pJP_AS8g&!1)IG`a&:@Q`\+AKl/\3,KdIJ?A#+Ja?RWMWDJbU3G:;R.[-C
%'O^]-ad6+loZT2WRC>[8Y`F*!)kALU92C@WQQ("d8G()j0?ds1'1#'*R&=+:!Bq+[4rr[\,h,c3WE-2[n:&Z:UZ(:Id#uPHf_nbO
%+\V'.O7T*iS!c*egaSB&Lq-]Kf*$D4F<gM\/]LK&f;/DC'PJDZj'488TT*DK1BmNSa?>5FeEp+4]0IQOUZJ2+g4AHJnIHL(=P0O[
%!fM2$;CW8UHcd*;l[Z$-aV;`j7*hq\2i^WJ,"qr]2J(h$(6LpP:V5)E]r]4g&dUZ@Wm!N9Iu'I[/4&d>0;IYanI1JTR1R3FRXC#M
%H!Q#qNN_phYC-#`SPD65"7?s0>r!#EB%Jp&ZmeUE`YGCKEO4VtfOnKcZ?>G7qG#:uN(2niD8'n'T9os!ZcE.KI3Bg7gMRInkNKUU
%V/GaUSg%=GPYVa&R8n([A?lj;WjJM\Jdc#O7G)1;AP9k"9T4tmqoX](&G\0&>/u@e#,CcT=*68QgLd*d:F8ta#Z!b4;[lS\-*]KR
%0retX(-eStgE3B/3ucboZ>bofP2lon@]W<AUU"OV[8`?/`K9(Y1Z=M\\DW8gLe-_@-8o:`G^i>W:E-?c!l^!kYXg)V=^XVBT^*`4
%eBi%cTRBF.7u$ccpF1G%.5U=GTI21`%M;nreq:cCnD",.g+i%lGJY6:F@.R07EI)WQE`c6(\(MW]d-6\h'MTBL.fo?I8D_qnH"?!
%27M_$[2+K(nu7mkh,0am5+]%fUBUd,0V.;g9^EuC,n3<gnVqqae]"f$4A\a%6f_g8\fsa$'eAjW/kcd"pIELQ`bVS5*DQ+k19kF+
%J:lXE/o*$tIG":oCJBhm9co*<OSS$0ISJ%`o#g0:KHpT@$*id50pJ``P<P3cYI,(@le4`pC\:k`YbOk?Z?VarS5cUAAF/g9mQ6BZ
%PDk/?"SLb$=]$_sfrO4l`55D?H04E.=Fb3RLa:H<XGA;QR,#9pPpW"uWYT<K0OO>c^F!5"$-8k`>Bdi>24h-Dm_6Uj<f3T@N&X"]
%pT&,27>@M'SWu0.8%]CEB-OVH?u0CWTo4:d[qFC#,jf(pMTMKkY$%r<:^*Fa(KGf&blEVFCQBrqj7@<LGYVWs`2L$WY'0-%[;Rop
%@G7Sb-M9HfF?>_"Z:<5n14aG$HUo\r3d+/[o-$/;1E]s3%*3G]kU_Z_O@(6L+VOL8M3AcYfQYUuZZ-WpHc\\t:;s((lp3ebd?e/2
%n5S^WP3]dUn._p(ifhb"cSXL]bEg2m'm>s$3TVm"WQlhoZ5Kou%PojE0>rs#enVh^<&DkNLLG<LUlu@J*IXr&,efq'M5a6gTn6C2
%e.;K"@R*;X)0@.Gd*KYg-XIKU&;h)F-A/2lU1kR'21?bG3hLJmNO-4^R9$%c1c_TH+$F)[5tf--n-\J\Ej5;>MGW[5(30a]^2ecp
%%*-ZE.YUmM*#7pAYt/=HSd)*n:6*LK<OEa/ptKmN6kr87T$P`;TVCa35:I>ql,#p&S!Z)1nR*n]/"/2Uo#3:G(o*lgjlKnePo-OP
%0_B[mTrY#9W^3^Kb8Q[P?j8Bhb9<o`WDV/,LM"%t8r!SEqGln%Eq+HN&RVj]--C?Cm7_g'Y[-BHD@L5gHB'2"F$pL&bbd&CLneqZ
%C1;)>9;Tkpm?Cj)8IlcK7FrrXRq9^p/!&+<l.A@m/!$]$Ucj(lJfRf?'&sq.2TDA`Vhn.u%hl;="j@X[_bU;!r*W</<)?86/mW&J
%-rP[#Ijrc<"X0a7:aYZOdh-^K`29;;lBsG/;)idSG_[^@$GN[)]-V*#`X9r\h:,-a'lYd+,S<SZl9*A#?=s"<7PRtM@%,r2"UQ,g
%ZR\#,.MdnWdpXL9'G8HC2pQ<g%.pQ0RJ&*`_+F[FQBpQ;+0l`K191G]Rb752P!(H[eKQbUPr2f#U9`k=UV_t^3p9-<9>p;BnQ0>C
%#M,P(OXJ=Sji`j`@_$f?2VYHCEFq9*B2o\R)>CRa10tKT&uIcKM).P+4-\#n7FTEE"ict+l?,,*3%Al)Lc30FYAn&0ff2t(Cq-Vt
%:)YK&jth.cLV;>]cq3AAJEa4Z)$H'T0R$Qh*#O@Ui^q.\agF#eKX7[AaI89\^UtZF_SU.D^!5?W(GCsdW_@&+'0KIr6tj@9R@\8E
%9RW\WV^>]E"'LH1C"L;W7Fr5th#NWeN[B@RMq,-b88MJMQdgq!hL1h*2/s_WhBHdqQ.!WNSgF7$:?_nE>qdUaBF:d'Jo#.>^T@6A
%0Il_p2Du^4+=%;[R-@A:l+e%d_iDGOou2^KN#7_N2D!GrdgatS]cghqC]);O%jjG$Y:c]Z0Gn:Hb=:hVj`/ZYd4)modiHUmP_VPQ
%H-0g/24Ise(HkJjj=!D;'8>#CCW$T;Nh1H2'I&kMeW3,;a[E\hB!5f^[%^8a@6AZ%cRYcK,HQj&0W)2EM0.V/6tAO!s3KPkoi?AY
%Bu9TKr#^/n_#E8lc@'D4fk%eC"+#O[mabc"&1A<*Z->^9`NlTEoj>`06makC>IM];O?2D>%ipo_hcqC9I=bKtN\hF"UJiKlb!TNh
%5ic+R5S4@`dmRZ=CKpnAjGlOiZ8s'&2._qr/btpL_Tu.GI"^t-)E1&^pm=@[1OsUl&60>h&GOW:_V^3AD&8q%:QDQf8lUSLVNJTu
%I2>A8!,X,P;8\5-8q(k(*upclQipn:UAbh@]pD3tY*(P26JqA5N[p6J7BU/X?bc;B_hRcS;):L3<LDP>1!GOk,R$SgA/rfJ7$lMi
%kUJ.r@[Jg[j_T?GU'Q^"Tn8,PpNs>+Tb>;SWYa,[a!'!T9bQ<H-Cd9K<CBHZnHq!bs-/\H*nG[$^KNN^%K(T59oN]RdAVhd?SEdP
%9OCWmKW)'&/6Eh!&I_>dNTIi$ReVt^pNgHX1F@qLbr&1RlfDH)Nd(]'/(+5"U9[lFk-,D(=V!gN5SL'DnKVBt2Gc@a6mKk`Da`gG
%JaPAUOC"QTJR,!#'.o*%Ql*IQ6cfr[UL]o(!Kq%+3T'A#&_K]Q;7u%Uf2:Fl7b38C&mm!()>71O#tDk,;(@]f8$C9lPID:cL.]t*
%0Y7/Po\CJ'gd_"5W;V?KgIon%&</JVO^tA+C:ArOP)lJ+]quVDQ1\N'[TYQ`F/d&WTT@@r0s&:)e%#nuaqYrpMNLRi]gr8@&N*gS
%9dZ^_iGl5`1aZ'+h805eL7a5KW*;o[+^h,)]WOo&B@p^W3#J$]3aU=K8gZ#d+<ig8?D3/8i)3Xr.QtT=OhF*mGK0,e\J?ui1m?n>
%Y(a3DBjRA!#ZC'd5SfIoFnJ:j0*p>**RU[mct.q6d[!r$+Z\Fm%g__Vi._k0>F"*%-I*XMTJ*D.oLZ!_S?J`RSTG,I=$fUs*7:eA
%3/`4+HH7,c8Q%f[`[-p4MHSrg?PrqH#"d#6N#""9+T/KZ!,]*qo.4i!g4RP3Bbl_n"n#,ubefe7:C.:0NNRk=18(Hp=?n8W26I6p
%X[DS-.Fo=<HU-[s<C80+4)iAT,)E^!ah;3EIc;G]+g'DLH;S/&.+]ZkM9pbb+J\F^Q#CQ[QZHRp%Iga<-`\BLTfHrU48EnnnSsDp
%-^CuP*:Pou"iT.s@(dR43H`t/`.m*.-LX_?ZhC!;e_-\l_RfTGJV[6l/Y7TH6-D%M4(#WH@E<6"/il+m9^bdlLF?E*KJAGh!pd_A
%WBe=4I)"k;%8UH;:L,6"b=Q@D2sBr:-^>9)1iabZEh@Tgk:+t=WF@(3+^a^"Ur*NO1u=4KQmUGG<2(X=&CIQjg_Q3B[I#6B^"s[E
%*1`ZKO\n\K^7=g2+m=e!cbs/YLr<9n_M+AI$!eem<:li4?nUGicfpjG?jsL\%m`L>+F8quFt^=Z9d61k_3s3XrR0=B,_Ob;mLN^j
%4.=0N,eC)8XX8A'kV09a&M[hfW"I5J:G`P=_C[/%j]5S-XFiTARff@q03O:fr4H<!69dB>R]:t6rsFmiG=!"Y!u'4$'5#YJ7s[)X
%:Q:nb!,:^Rj`'sg.Sf/_5">dICK:n\BhUbAH;@CF%VJ)0gpkG[PMhYc?M+o^!\+]jiRW:cFd;*/lEGM&2%:-iKSIAp.oKgaH:HOf
%I#8tmJ4WUJ"5Fg$)q-(h^!+'Id>V+$*%[GLEu:k&MoZsq&lFH5pQLFHQ'k2eK8YKK)Z&A'4=iY:JT=q_7>TfQ(h#"D?u3:1<%qOO
%24TIrQ_68\8tNIf,h%L(UA?WB]iAAaQrj&CKNfhT@Np,#7^BKZcjB8)@stAd0iJf;&%[X_4d[JG9iHYp:Lrn<:/Ms_p,qOYc65j>
%,Xa;.\1WU$.uuT2$(J1@Wr9Z+V&H0fcqT=;ZL"3kA]Mf[iIkcVrDHS!JPjoI*ia&P6^031`UlItY0be2?]TuEF^HKor\c94UE(@;
%PtXl=8b/6S;s1\qo=6nP!U'Yp9;59s9VHqRE#S-aVH:r$]5pg23U9u,r_d6kM(2bAK!H3,coGJS7$CN)XH#Y$EM"(%!XuRCpg[r:
%fcGhTU4&Z#Q\mstL(_mVjbW,_N1uG&7'*+*A;3a@lu\rE*YMa7+&R#HHOtJsrYT6HLrOMje1JR)K<cq"QpF0Zjaa5)1jDMiS>WJ]
%DFq(B6T<Ui:u@S2%\RPkCOTmne0W"D$$Ea:@UhC*=`.6+G_\qeGMHYeOX22L1-1"tM$S/M<?OYR&^t7<l_5R2'30&<U4b$OK?-?;
%ZNoC\`l#l;CLFBhgn2Q3d\0uDoqjh^H8$5C86d.@0"!NM+O>9'1RL#2(<6u\>$JS+(@DXje[#%cK2o06aAsCc(l'@k,!$O)K@M;T
%"Y*IrG%l7,QIW0:V*X>PNF=>W>5jG:Ls">4;q;&f^76l:rL=ci*7"-XZ^;E-'H`Ib\]9':F?D?J?OeFV#%E#<nC0u>qYb:ZmFDgi
%#Wems@4nUgjQA@Xrp]'>B9.Q.GW.-NipJ'+bjYUP92NsRR;8i0%Yh^^%;B_,bFH?m$;mu8&P[(P\tfE??/@1eF_5d(q"55.O_c3c
%p6K;$\NrD^)T1b#[*lU1Mk-hqR9Z&*=<c@K+X#peO_<:#+R)$W9O[1ZMnVQ;WF^bTe`8S10,'SgMslVrK7$I;UEhgR/ko:7gHod!
%Lcs.od+?f0asD'K1+X#rj=Fc\F#)a6IBg9!!ggRa4q:hJLl+D^c\#B<Los3pIPr)alVrMa6X9sjaETCOl$1&VDs\iU5_uGfdhcX(
%@gt?Y2%C08Ug,a^l@D@A,uk6EQ:C+O&#?Hk>_/!`6V_r_!i"N_=m"cOZ_*QkH.tfX/=;H:NcMNKODl)M7=0"(b)!$>IlD3"Mm=Qq
%XrAc@V]-j+46M%ofL[>la(7i9+>UD?I9$_bd0i(RI%Y)c@$P2"Z>UXE`!+l.NWRfqfMb7&3,_<5k,imuZ3tZ_JngsOcnLtG`nI[G
%1;oa\)?/7,:SK4:it<fb[VLD`S)-&\6d$=87F$"[>fg\FTO;-$B@Ans)LTNZ3p(s7R%VNV19oD(=)LYV)Zh^F(AlR6(g,!):ISnf
%?=EA&E_;=GP_N>9JEb,Un;>sNWB)4icrlWMe*&@ig^AJ\Npd.g=s.[4n;P!fVWm1XV[&D8[YHrmDPMneHLp[G+?QWuJ0l4L%5&M8
%ASms3LR\rNXTi2\.[Tkgd8Zh]&Q0_*1'T"e4%dJT3@#4'A7->:(6`/*V)q9,1"1S=>La!_C94[%%_sG[/AC&^LQZ9\S89?M&@kp*
%[U'cJ/7Vf=``Rfb_0O`HP'!c`2@hSuFYY$ieePRT:itC$ZFfdgj6l%1/?Fruq^V+q1^(KCDU#5AP^NYq:VTn:HF++W>#:QS[+X&a
%OD#f7WLK'e*B:.'`e.)\=&eJGP0hb^I,02tW\q&@V5\o!/OMqb+FWXM=ZVq6:mbafCUftbnhl'P/S<B:8u3*Xh/eKMP3=^L'(>N:
%`fqsUOSIrC&S==l)_>$H5,ThDP=<m^,k?\]DQh74,rek\(5Ng<QF`)Xj2TMT'5G2N(.Oh,e1[#O0&c.C/hLLM,;1^\k6j[IhC9Zm
%X(Bk@GTkodB*V^4O=bOmMs45tE;qpi?dfJjkA/n_(Xr11dqt8"Q`;@sqF0p`HFJQ/@6(;2Tq$fl8Xe_q@@Pm("p/+fGek6N7u01e
%If"Ms&m)l=Aghe*G?q>=6ptc2aUK:S"H>8a@;Gi`3\uW@CiWqVX%^W@!`%qp5hLaFis#IAo9q"aI51[4dQt2LRKidPb9)"lJ-I/u
%Wl[Gf?.q[\`#Im-0qb8CE-%$?a'ltW,'-?5,RO'O,/ie*P;,'"!T`#Z>&qd9XkQ4]]J.ulp_Z$rA+[kLd23UU"S<85]]P[ER8Dko
%TQ,i]Y!ju^?H13CL#j=V3sQ>7]YrpaO!.%R_1p<n<<NhpA]u<l"qfU)?ndt%>`fV\\j_Zm$NpIe<2,+cFn:eI?-HSr@$NOp/=]YN
%"n/)qg#F75!WX4hF%%2ZK^i<@Yu)%#=H`&PVS5[.e8m1p6dib7?M:0\1J.NuPbfQp,m:_W93[F5LAIk9?4VlROrBnp:-/^5fok#a
%kme?'8o4Tp6FeZ\V:^b";il`Ad\QSWg!D-LOA$/0^4o1M33PrsJ7]0f$foM;()bQE>L&*O+iIbi7D0AQjXb"1Z;6sM!29MW>=G*d
%<EMAIL>"G;]?70lG"+pP&;_2C*Pgr^C4,-3q@a)"L8"tNSOKqPU`!@CqN)0N$45Z/S'ols]@he9)7dRd`,a.ReFUcL24
%LFs9=Mu/d,>DCEQ$_NIe7b'%[P$aKSck:k(ipWb!,A%PVm+Z_d$WRKmU*==_c9O,h8HWgF[^/UD,)0Zq\iqth*-D><8LuJ@gQG-=
%'WN=F("&o\:7]m4csHWP*;kok<3!u)K2_FAYgd)E=1)$dM2fm.Bh0kI.4$t`>,riSf^DffB5Bk^of;4<Vkcq233PXQmb74Chh)\I
%^e$;APQoqkPK/kZR6o+9<a8]dQG(J8/fT!CqV2T1SKL<$"^j?d@Ns)@N-A^uN7?:`B>L5U#qFqdCWnHP8'1qUTSJhh_;^b%'q!Bn
%,r-%_68^_<`Fp\5Z8Oe+B90;/o`Ra*f[$Xj'HoFl=Q-n1&XinfG<D_>Uc/V/TL.lKSuRZ!0P+#I^tG.(np*"R%%p+bCM"Tj]1BZX
%+g%9KVF$bD\9).k&mlpM4U.uJ,-`:,Kjo*Kif8i[geQPnJVEN_ds0QEk/>>@BQ_QV,pq%\!al`2O*4?q[Y/b($$`]lo.(TW6G9Dd
%?"$g:LKG)g=T[)72oFY:I>qD"-og99/jItcEt7aWc4r,\B3mrd!JG3,Rkl"I)N3j!i\If'lN$d6CE,,u2TG9n>SQHhA,_D=AmV^=
%%'[`faf[?O5auSlYoWld7F>CM)iKI!O;-O9"Y9IMK3:(0a,C"S]$D:$^a9:T/h2[P+Canl1CQ)(,Y!<o--ZoS"%i6unZ[Vma#\)F
%7*-9?=t1<*/KNo(d!VcHd9HBm&Q/I[@]\eC(n8Za6CMT*!H4fL>P2_B;2l5qOB%N&bRo*!+Y5\`R$AdbiR1gF-Q.arn=P<)5OTK9
%X4,+OI]f$E.Lb/hUaiqCpA<A0HO_D`#!En^-&VkLD-b(aMDIL60d#p.'(B"\jejXYOp'(jOb1Dm8Km(FbnEF!*`rP0qR"%&AF'FU
%Z!\n",-&"=(0)*cDI(H53&d?="uq++RDbIp;@G6'#+^A7`\W5K[g?=p(E"%l)iTb%6a,eoe[YrHA/26Qmk;3O&I"La_hZ>,AjNTZ
%l1W)*c0qaZfQ*d]R*5pmF2dB#/nqac8e'uqMs+U3Q'C-62+OZD(7\:g_$,M:,*Rq*`JlYj,"_u7!X<8>"5kJ<M2!^^6qbU#ii8e@
%KWLCim4\YV77gd$QGi(IoV"!4":YqTba,0CfElL.#KZ$gJ+&;=)^jFYh6/ofhWD@I5EnGRESu1p*ss1^8`EcXYfA4=*9V#'@&E70
%,'ImaP7"F5D;"T;9s9:TbcX9f.DY:GT)4DIj9c8JdK<%!k2oGHP"`JASW0(QE-iG23`1>*3tN<1kf=r;.8A98kgkDW!?,<a*b1f]
%W=]PB[=6816KAE9AB?LU!TJ1\9[k]g:*htCoI6>I&cp+OJ-T0'o5&Ch7)kXf>60JBP1ckV9<>q<<75r@;q2lY'Fj8eWmg2g*_">$
%+aK+-pDUseqi^pB`CEA4N,!I4BFlI==uhX9S;l#Nc5^q!B].CQ[KMTj[Q1GbK/@&hFDqp24e$6"IakZSHEJ:*<'/mORVEWbL<^:5
%R\lIT/J3Is!BI)O$.G\e$YAiuf7_5qkuWY9Qc(s8(Xjmq9p0`_]uc.7R3ih\e)^4eb^<+J^+.!s4/asfaojDBECX(nVoWg(+0f2p
%n9OYa3uTO-M&/`cPJYC<>C_*G]0/_Z=N8COmS+<Zl=827aV!F:4V#;e#&1[AaNCS=RrG#3j0]G-%N%$ghDD0D)-p>[W(rj-a<$1K
%!p_#H!mB5e%6[\83/B,%lW=7`S,&T"=!cff18CBmE.)jR9Minn:aC-@poT9qf^Z.Z/nPc!&`1ik`gR?4?\<^UR0i4<8;psgU]nWY
%RpJ9%H)[VE&)FhPe2SW_'B-1&6)lG(5sE\q-)>O`c>L*\MGG'E]Ykl,rdl472q'G%.KV/k'd_Gk-0rSG_W4qW/36(+6L8<g&R=P9
%,%:+Y"UfCE-Vlk\8b&qMDF2mf<=sgKWoa/)XdgSo:9=Jk8*`&'L:WNbY\4K8;j#q_jSOhe`tUotdDo5JU2U?'J?("CdB:=#($7DV
%L]Z\5X'm0/Bi0APQOSOnLPQkk+3_o%3VJmT)+9Ck*n2K09;srkAa-8n&L7KVLd7&_CCi[Bn)O0hc/)!IG2re_L<TUt/30g"$LRn,
%6im"\ETg8,&AF&hS<I>H;gYa!='BZ65,'*7AdU<UPDOF.o!F$[C)!(1$ZLOE1!d%d+ToWf(052nd>\aRM*[c7keY-9P*n/Wj_\L(
%NsD8,A'g^`q,7?4NT`b4"Gl9Ti@'MKM-*?lH1p$TX:*&0'>V;<V5gt"6kdGWnGM&bkFm".k-:4K-o2f'PU9.Q1)@;5$Ng6m`W@iN
%KP(RV"Zg-2>0tKikOW%2lijKmH9pRs88qX;+3i>^=QSlP5:4bY%$uoojp;H_JEOjON49k54,D1:g^KZlSC]?[]3l48Jjf"J7X-_P
%HS9-;^0"<;o#pFgOto9j%C'>G@9/i:mGQC!Nn'If:VC4Wqj47_O9DRiLfjdE(s$u//2-+[UiF5kE^H'Oi3e@h^p$56cul3Q.&hf3
%+geBoP<rPAm!(4NYQI+0,gq>UG<j'DiRVh`N,`k9"m_kL<Q_O.*9&mm1BJ:pNNdfMW?G.seHTLs5A-;,<ZJ25(2u5OG6a*r_e*-i
%!FJ#L]m*MZ=da[rQQK6%p/"oM^NIdhjLg_O@/<jpZ:Q`Lmr[]["qdiF&i[h2QpP$hhU4tCe)9NH8A`Eg._sJG1RbgjW0"/iYGAPb
%J%?LOP"b'%3mD&g^7j0UM.9.Z(M<j`TW8FGZUj@LX#%jL0<&a6]:o<&knL^c3Z:l6UVbKg]IT6.PCW3(]nBF0.N9:B-0=$,%D3!#
%<!U&=V*fcRM\9N85Ru;D-[NVmEYcocdR:,!0K(3[8Pj9]$tt"]43[k>L`*o&jlkP%E%j>3>B/XE\HP@<<o(P;O[Wqj`8-@f$-#+R
%h02dISR"pS0($5)X&-6;5""e)02?,Tpg6Ef>8l=-[IKA:f-5$sHQ7]5/m5/66.R`oRh!XgOt*J9kW%3uX^k`u1hY"EZipS]1a-os
%$p$[+^0h2S92H#>BXjY<+9j:Z8Al]`(mqf98'p4T&5K$EV)3qjWB&MY:_Qf,>eSfi!BO60KI);9.S*WSkO:i89k`X:@=9H$gin<=
%`CV@BCsZ@[MF#am"G!dKCZ[1BG<?biKUi.nnr@l)fsN9l,Yt;+"@O`-Z6r&i@.FBrrIV:QT_ATJ6/"]1`$uK,FbdXI2._Ei?UgDR
%%ba46Qrcn^KH:`Vh"'=H4%IAT1!F0'hZ7AS`*#LHa_A=Oee8YN1+go/r'`oMVQD?]]C%(BcAY%\U`S\%TC)'[F)JP!7A+m]3%q6X
%Rh/G,3*C;]MnP;kI3C8eUW3*80mp&IlnEFuYp6iZmQdHj/?4Ra:k`t4h@a$*MEV=aC0$S.lkoU;Wj,elJ(#L&Zs^!UL^rBW<e_j1
%U9-eg7^#BrO[HBk"j?M_iW_F(EniNeK$I;JlD.Dl##.BBM)i)[$mA)e$uNMbJlB8L!$Mt9b*YW8^3(%M)Z#_4"q4(f_*)"*3p8aG
%4P.LSMi2F,@+V<+6#X4F*VdG0IW]XmrO`6::U>9!c5j0Y";PWrl(bMiKKSg=;LGd3Jd6jXPhtBB!4!PR'*WV#e.P2<Ga]J)I'/HS
%Q(UZegT2TgnKI4k8/e.DJ6asA0q*!\E:GG,&YHWCp[(lX^]f(q%9S;0ZUFT:\5Y2sNu0`M=G=[`<aE="YcHQr?bDHhB(W(?!=IV-
%A%rojUW:-K+"S-?\)#P1aoj<+jV85JLE0"%9ai*jS%Pm`H;)N_4gEhQLN@+P'VR,_..bZ.e_"^n$-]mkWl+6NlX0ocYUr;3In(oD
%jZRhKBu)H<4Tp1c$7b>"Q\4jPiF`jO%h[KjP\ti<R9lts,-+^"3+Xt-&oH4KA>hTgIC1h&=n5e(EJ"RG]_b!O0T%.L`7b4R;r5I/
%cD-9E[(d5-05O#D+jMslH4i%i&!Mf#3_Bdce'mLd(!'EpB^E"u#gIlKeiCV^b`W;r?dXCs%B!;sh("FYp:t07F7>PeMTa:4Bi!aT
%5/q%[>W*fSfTAK;*3^o;/V(==!)laCmu/"44SiY/QN4S=l`7bG<>4<8W9@=S@X*G;oG4DM/;$PK&^TeL*eB`h[.k#G4T\$7GlI>s
%(mkSuC[=IRPH48*!Be6HT+EPA`,);gK1:)*>#:@4eZ\Usi?X>]<70u,;Jl,^%X<K2K=la4nZ/f[UV4o+YYnTJ1"n-O#p]s'N[FkR
%H.MAYEX>jSM/&,H_UKi[>DhZ^A3a$9-3_CG9pumCMOYuNRLk]P(n#oodIX[>^prZ1THhE[Hm8ZAJpJQDk.)8QS!ba%1<T?+[5G<C
%\/eQe'k,e2mlIS'4Ltf>%\l_R?He'F?7%"H$O+_,Shl=7>,@#+(iOh`"tZ0nWO5\Dm2=$io_[R4m6J<VP*3@rMS2RnJ?GUkn%a78
%b,r$p*O(N'oJ\e!i2g0R<gIPm#&AmSD9&#!r,UdS,,/[5Z^)kM>s/3nQO\5Db<au@(rBML#16foDkQSs&p]"[C).Cj\n\mX/foWT
%&<3VC`e;qE#Bge+g'\)YJ?*VN3Y5S<Q.VuM$m)5)191e%JHoVK,MMuTH[=1"E=D\ga`*&UB=ucp:.Rq9!>e,'8NkA(iOGf]'R1l6
%clIoC4]GchQb+i=Zf6XQ]4CO?B22`+3=,>g9Y&b=\'AW"<-YQuL%1T8M]0m3$PSf27gmk*EtV`d?AY-?\mU!&/7guOBZnlO_,rhP
%TZ%D^O<u<n>6"j0XluHP5b$d/7dqX@#R4FIC+JA<X^\Uq.es(sS'\CQlCm3iY,dhh17hef]5E/NUW_X.o;,":]J:g[``_?37ghi3
%U,M:eoe^6Un*ql,+R;LTW#!kqkT^PP9E_4!QTMt_]*GFYSQY_FF<FTtL:jJqBur*g]B+uQi\g9R_ch9k?OMYAUm[oOGA)e2g6bg7
%Xlo42'sM:5J^41Z8SjMUeEV98_31+Uh:trFR3[,gKSlmc`Mm,G3\Xo"qgIi#`H*AZ&.ohJk4@o_A<W>SF<6hP5@I3OnI=DTWBjDF
%1qY7s[(Ob67\kR.Ho[DT'1/FmA]PDd(87]"Ka2X+W:ip5*Tj:A7G"QuNC5`7(sTU#0#bKH6q8MU"X+Fs$*`>[+V2IVCYO^KckT-K
%;=HJ;dL]qn[JK9Y'.:/>MS^CUb!H[p-9+&o65G'n\-Eh6ZlZl6#-"A]QH&a4n<;)s0DL5T#"C](/;QUI.1ELl,/%UT;p/2q0LkoE
%*h%p0-HsiaL5-:1.7CO&\63'nHOt:oPRYM-iA$F2(np0B:J^VMcrcoD3["[Ri(9VCLtE7KaJO.H^_u>`p:[1%$gV(fe5Yk#=7@T3
%G;r6ji>]7@3:mOF$pLO++asV$>_$2$9XHC,bX4^3<AEUV[c%cL/`iH=b1n0uVlORf7]YiZ`5m(;oKI\8[7iW+Jjk&"&Y(SN3Z\mN
%rKZG?$LA^Gk14B.hT?bqr*7EAOfBJ]/ES$7CZ9j,0gnRhXC9cu&W)TC?>O`Um@=M(ZTtpK]]qG8`FZ8inh(u9Ne]3C/(3WKW73im
%"5T,BBp?Mn37E[dB8Gdf0$YICW!9:ML*^*LN2eRg[)BuY(HnXX_!TD-lI!F)DJCjP&J;.>_&:ro>l7%U,pm(V?^<a_^'h$H::m5m
%U'+M1\I1i2qn*=QkfP"K2N%n9X'f'm@"WLo[2@:=KKd>_m$tP-cuL8AOEQ4Q.>%7W@a>h+Td:C.>F?!2'+sPfDV:15[alWagjp[:
%Ar5sf+u3K1fHFhB](t&_X0@:GqIYl^c"#*4Zrf^/STI6uAj<`O+aI%d<[&J\$>bW'FeM=Qa8H&?XY)B\aN9"=)0Wd-)0g&P"rjK'
%_ec;hW=T]8O.iZ+DOqC3<*9G^Eh<6X>&p>>_3\X["N.c1ZY[kV%AP5dXjc:fA@cYX6;^i/7LoN);FJ$gLXfuk?I^B_%&#0OF00Ub
%ZACQK=LObiH>3u0AF0MeqE\'X0Rfq\25o1`h=>;@Xdj88<IQhI_h*G:"ff^"#V^#;>?ba"EXcX.*`<EVLgm9^amrm3i_L]n'QB6M
%a`KBmGha3s42q`Z_3C7a7AArL4!L.9KfCg'SXOXNjXNOhb#A)<_1_7c>fI\K4,8TlZKA7K=pblRh8B]'?UD>o2j0!A282KVaE$J0
%N/3u"8gQ^&I6YtnYac64s3,=n3;tVi;7@!nX6K#Fjc5Ye@\Os.@DLHeC>-_X9SedCZYI`c\\@,AT,f1Zb8"WJk7Fn'bMGR5W:ME-
%ModS\Q,XG%0F<UU9s^j-VDGN?)V9pI<T`PdTLVXMAAdf1G@X%F]Cm(S+fStZRa%K0!;t<MUThF(oguu(-8Gf`%M"(>Yh?(d[+f-N
%?_s:>";ta=+tCD_,e_jQ!uPVQ)S=_tM<OKLcg'bBpeCb;&.i_aTgEg;Ao4M@W!Hk9NM)\5W+d250jA*l'<9iF'K47\[LSh1QmG^(
%Y>t=kK4fL/,q214/W6:c?^qKV/@p.83n(8-KH@e>GhplplGRq4em\_Le6mh7F:]XnNlQBu+(S=nV6_(S"cJIg<=SZ93qL`65T.)[
%AZ;L2$iV80[(W:_<At'N16LQX-%oDdS4T-FgOZX[EnkM^7KXe#AJT'W.FCf1G<(VGXCjO>$@#\[^!K`Y@e(/6>I8%tgsl6jZe"b\
%Mi7[8rac?):.Zkgr2JIG6q-OsZtIJS[&4)R>>X2cIEnS"()pI`m0+NgUs<3G8Ke^k/Nm'i2XOF2:WQ8ZPV]?:<D,+EP]$If`P@-j
%[hO/^+JL+o'lU<s@`dmfb[^aK,7(me\:f=VMJHT4KYme`D_[SV6'[%KcbUF<TJs\j:'F)FM:atsl>[K:LC0&)K1NPAMPXBA*@7FZ
%4(8+k;=^W#=Ar?XKSZc2_#+u4>1TMeLt2&E->hAR#YuI?jOu)f$5%=[-4)qu$QUkX(biCpp5mOb;U(fR2&cr4Tm7?a\6mcVkchp*
%7!C@?!VF1MT.nD_?b3Wd+GWsR=gfLf&>5MdldC:s!!D/hTR%^i820*%/IQs(XHF<::hW:pldcdYQu7"Q:^+lY+q>Q6X_5TmMh27*
%PR.U:,k"m9JH2]_(._uIFj%1:KK'!a'j]aKq@dpbqPnc02;?V'kr&i,cnct"]8Dttr!Q?;f/m=kd]NuGM%Ui5at$)r<4\LOZI!)o
%2`:g^:3+pk&:4V-)pDmc$8_a/qm*jO:bB8ga,]O-;#!@)CFT04(1:fADlt.(Fr41nIHsa6P@E1hQ9nh`.k#[gR.OBZe5MFVfgO?J
%",=k$dPX]'-<mRk`'(bOKV#$f&gW<ijbDaG`3rZu5s;>pXt5EC?18tC)^25,R,BOgpp(t!!3eKn(*n20P[J]66jct[,tsFG.`<,h
%Xnm1n$32M4^Rj=1K4/"q%X"s1oN,0:?rW:9<6]s5.Rl*i:c6oM(p`/N!9l'J&Vi([4jK*3j*-Qn9EcEiLoEEII6t\@]fePVJsXgY
%Ots:OdIRdHR.?b7YW4,5#rP0aE^'rGOMKj=p<5+,f`[mZ3,<rO)O=(G]t!d%"*#ct5Yk78rS@Q-eS`f??Xcl97)hk8ZP#FZr$Kq7
%_Yf5H>$ond;fLo":Wht.ZuN(KLQ7RaYbY';JG:m)1'Rnes5')4Ul0W>Lk95T/LsLAT,+3&6Y/SAF+GTL(q::#nNmRG7GR8J>fV!%
%$D?>,[Sds1!J`4=N7cl.iL17b@`:SmS%>C'98?W*'^uAr7@4Js;jQTYR'KiP$sf66"gg?KR-):jE3U2q>q+-BW#CVHRZjP/:p/*C
%P5cQqi7'?o[FGUj6EG'hn9@)>%H!&l+NA!Z:f5O'IPp1H0=>R&5thU)CL#O.8VmHM[b>5E<e-6Fd4(4^;$Uu.?S8a*H+4S6_"isj
%m%b1j8CWJ29/Y[hM%Z1-n<f!3!8@gAM)&,86pCcE\?VR%1tA+.]J'ZKW:COKRF3Yf\g'Hp7l-;@+%`D$aBVT!'ib7?`BA5P[R4)5
%dS+sIb\k$-psA,n4m6+$P^-li$Hu7gnqOUj$cXn_MgF[-kX75(6u.-mEQjU0_tpP"aY<sQ1n9ArL_E^T46Ym1@J1c;RLW'\>*MFs
%fn3W!#`L)OK&gik]gl"ICLdeWc$6KE3_8"1#!D&DoZml"D3YA@X+5pU&9D;f8KJ6_5*.f[d.8131@9LCJ(XEG%81+c1IMF,m;cP(
%0po,I9,8pj1C-uBgF]E\)\#nsH:;USWi%L$M+iK3S?pum9gidd=;]lB6'QTBN,([>NY["E'.R,UOmk'n-%/JYl<PRE"LA`P,7LU7
%ABL(CqO_F_Y9N6;OkieN7gA<-)$gu#LF-h_-/;VAcTH&Xa\%3JQd_P*E@<h#l1Eb3jcXGBY@PUI.+SkD;)>j$N+-N_+:YI;GdUi/
%@UN=??%k7[#Q=S[6E\97Ag.tRBO-N8A1PDgk'F/sQc5ks*E7^QY@hI5p;fKI-6fIgk$+1^/I+tc%Cn<Rf['*sNicnf5UA$%K0hc_
%`N%])O%qK3JYk=.$.*c'EJ-uk'O3ml5`j!fj.\D)%?)Q(`ik@f,B9Z;,0D.\*0Hc@5r?2P[?ij)=nhhSA&$6DO$VAC*\_PSm&1GM
%?;km7::cggkWr1#)m+ht1$,ntEjp@qX1+q[ToOY6,aT*r=3[Xj_;6`&q*Jq"o7qIRb``28[YV"Nih5eT-\N(0bLRL(+rEU$4$PfA
%FWi"Nio,cu03/<*O"A)/fX/@r!,a"neV(t]iJapq-<!Dl9t+fd:SO-.,d1YcfUlR!T2]I5@m@:(lSf1uj8+6?4fAZ@.isu#@1%et
%@DEq#RrHqqaGX0<elMD3CIL[Vn6KK1XRbl?3$f56JfLO(G,+pEU(sIRa:"]"1o%Tc=l^Ec.$$[GaGXm-^,^:&hkt#Dc4gP[&hpj,
%9L3A-a>n@7/O,Cc>JLr5oRBM91X.)fQkiaGi^GBd78Z1pM?jT;*YC7]MoJpV,/2R)!*Ma:TU7Gg<j8r>JZ3m%'@8d"G#Z\>I!j`_
%&ZT_M)DK<rH%[GUK6ZGQF;;9BA#-Je?lI2T(qp9f>.0rIM4o["TERV@!nu<PT1jLrL?Cg3c5eY3W7V_WkNrG/Br'cceENt):mRQS
%(9)-.d5fE3Fn_nDl53&06-gsdbm>UXibE/.BEa\FhrGX]?6p%TDR,K\OV@mK]1$aAfWf^_B!cX`3^HMASZ/gc=>_%W'/Bp5P-FT*
%OO6=?6)pb-E%'QTmHH11f;p5fRX$q/ab<m&#_]-QdrYSf7>gXf4RS?+C[_Z7E-uhjkg8U*FCois*ZS91Hu"sKUidTN=W/^IUY%Fr
%Fm1&_!<cl;QM9C><A6#g<HCq1/aH&=CXKFc;(T_U%7^%XVi2T.+jC,cWH3A!mhI(Tm?k<g97eHXEg.cJWrD`qMUupFo%[$_>F]i4
%LtKQqJ/g:N=Br_6<%O81p6+ArkN6D;UOqi9<*T\1ntC?=&Oap-H%d;OAQCrV6qgbq-5`gFjl&hceEp^&PR2-O'M8lrKCbIR"jTg/
%;-S3&5n9F8^5.Lhc\ZYgLH37WecG,\7FC+;/#D$1jO,U_2*fk.;%#aoWW:LfBmJd*8r,7)Br4nU6D<'tD-aAW81XH]A^d(nP7:-7
%&n`N:4X\0hEu&K2i0H0UF$+:h"-s8YkK$XF0f4[@c4H\FW0YXV[ZUqa7!bh.d6WqinMF^J;ado-BOa\--At9kplsa8\W#USS0bo:
%f]^imegb,PA^EMP00j5pfdaYj?F9j-)VY/DOR^YBb@qR\M26aeCTsbqFMR17ap)>[,h"c9788eD?%ulY0Uu.:Td(j!8V&-AH\#9t
%#Y[P%giF*";0uu4*hb0gC"A"K3.E7#@bZ*b6S#i9X`q:Fk5Ag:gB#ZpB[t%$B4LQDm5N96kcQSX]f#_KM#eOn1>Jl?/VsXV_:[+>
%f(mBAe:]bT</ViB6tt'.ploCrRTrIcY1V7aCT-3VB"B_;,NC)>!-bRj'T%lh^a*BCPu8VW%,53&_`!R3f-5VmS9QmAEoZc='!NGL
%3Z49VZ<K`f,%H+9D4?3o.r%)o+E0.-+5%3_Q75B\9d)!.'0#V-H9LF_;SB2f4([\)UkmTdbEd6BT:<kH).&D*94G/8JjO/X*B?9*
%"Y2l)/'Ag(#mL_#@*uhe80ph%AZ<dDW0!k7[!I@d;CsL?4>aOkg6/#neq5/;%%10)#nsSSU,B?Lq#:Zk.WuN[B"O&r&B$-8FG"O<
%m:[n^kQ?\aK5k$)`L)MEmgr?;U[j#X'S*-D-0NrB:dAo1?$b"P-lk&LQN60%U?ZI_<&(\C,Q_T.JYfUb,V3P**]$HdA^A"h7fJQC
%&$1mgGTLc\Fsh)7@r+GSo>,*DkV+R=;T9fZ:e5F;jQLR0COBj)3bH(/'@J]i!]JL%I96p%L7B3a6r[>?.?,A_I"[Kc$"IYg2sd?[
%QOof+m'65;AVoReFN'lrBi[NZ^^Qr1X.W-M!H4/R'6A.aZ%YZ;C^:GibXWS]:]q]74LGI'ZZRsTh(EdY6Y<&V]1!]IpP;c+nPPC/
%=`do19'D0SN^pm5qn,J3`oq<jCl[=P*Iu72h+.p8GUl%rp#*La9rd44DP75G;r<Fg=n+hQ>Cj)3MQ+R0,s$_T'2d:F0*tA(EO'%r
%hLI1`J0PWPiWSj$=Qj#*C>Cg[-5=K3$o-+5O^6b,Cmd.dns4(pMNUCs[e*"d@3q.r7f<Vp/53"<e/"_7$uLY"585f^/E=m'8$!KF
%W-,1YIt1hO=26_GPh+o]NdrH_ab8qT5]J$C'[bponHPc*psVfi@;(DG?8J^:Wl/N2IbP3C)B6ho/s%.KX*Yd+[0gR"4N<8uBsgCD
%=6K>nMON]2r'],^7f,iRB<)oP21F4p@PrTLd"DR<e1iM\-tP;)Hn!F>,Wf1[f=jHN)4]/"bp86/'OtlXCLr\hWjU)#1eC>,\;&FV
%<-_"3.LasA[<qJ=p5hOt$Ju8ZWaA+2A@7bC#D1Z&1S",M8<+?K*#.Bg.'fFA#nmk$%99[C0K5dh.CmCY&F4q\=&<WEQ"XH#nWMHg
%$\K$c@B\Eh`J.d=9%<[2FGcR@Ls7ETct'c:30rOR_^g1pdm@5/60V.5j!AdgR'F=Z?W&JO#RsL(0g?PK$lj&:GUc?89<G#KM?T/&
%/&078d1F>t(6)mkh:*)#U,1b9q$DFlCBeh3:_DJc()aI9nP3@bSIBo%/%c\_:>3C\fEF]Gkebq#Yi"d@pspT9k%Z3X3HOQ8*_U#?
%-4qQ+,t]`;Y^(f=h;q:nC"EDeS.13SFC-5(]TfrE(Tn-hXjhOVT@Z.Q^-&3K\,+DFNPkO)<Fh@Z>ag>.:uQSLHZ8k8D1b!(`"&oj
%mXWNtcaX<R:;!OsH+-M(](+9;7FC\U$fm=0A"1AZi%J>@f]0&[B4bhrUD$DL3i[i8/@2,:*jbH7-J)q></Oeu&>_:!Pi65m6k!tL
%>BUXO,P1t!#25G2`X*62Ypi4i$;aK[T;\km&u4^F"]YB*BP:GN40<gq-KEJ&`'2.4QTec*Kq:sD93P^Q]qP/URK?fu@+G@ud"O<o
%qUX.[of`O02b#q9p]X9f5Icc`DERg`e^rp$1OX!0JsQJ'jiT2=Cm0S^;$p+95-='Sd7jCCnds@AR*=NcV(pLd?;]5&;O6kd,2/>F
%L5an<4@heT*`^$D1ClJGK)-B48<#Jm[%f<2422fYCKcGsBo$0,DW]nUY^qK4.l[`-_B[VDhQ-M]I-Z2.LFEdoeZ*Tq3n!B+YY6>m
%46,,hs"-qS["0jB5[6pY.%ZlbgiZMbqVC=eI@D;JVb">*,*6nieH6\T6JJh-'XtKk2/a43*4f\JaH@t1S-lPk1[-[Rn7n[?9d(bX
%1rBtH?>?NDQB/@+eXec1rNn*nlDa+7On<5inWM1+4WlqP"$ho5Kl:HjY;Qp$'.s<gY![t<*fctbq:Z=(6X5!TmdOaZ%KIcC+^9"V
%?MhF48b"=fbfN%%L*ljU^W6CpJKU<d52C6T.'k]p,Z[<(Ep+`sb"7>[3VkU?L+D')S(s-5b[kg!eP8I*V?;!pA5kGd]3><N"E$,9
%GXUR9QjM9E_G&=cZuE0O*NTK>`*7mB2hhtj/:f,4rX?7kCRWq$r6.p2,*Ri-`U2VD%CflYeJTZZSfuYUF+]l/j0a!L$E,@riNMc/
%?#o:FCe1In7G#5(eT"6`K==`Igp:9p/IKEa$R*"/Sd*uGXD"6WKB@bpO,cMe<p1D=0S>aO]3(Nf>R.afQf/u@P^Qs8;5B;+B2%:T
%GfgbkN1kDjd=ZL"Y$/T#QU660\cIK6M&OeYN(Y4Ek3mF.l@fH3LMdqiFfdU8^]>)(SrBnL^1h<aT]>5q.99*N[&'eQLd`Cm:/5$A
%/(iJsV\b'c](jG?VKa&GT4s"TK]/?WPSUm#j$3c1*s&@qW2)6`;XQb7&-BsNM8C`:N7F8f!tV`5+p,AFV[cT<d,Pfr%pQ5D4&b#"
%W0e`[_9%kF;4t1j`M-7]rF`[Fe?c2K/-8iEK5fBAD\eh-:fjOD2-_Y7IJ_]MK5lgC$msPqJ4A]%%h^*I);@&sZY0DtQ\/j4,N(.I
%LEM^f5S&T[QAjFK$+)Pl&]5eg&q*5++I8'rl&6heR=MO(3_XoPkJ"gE,?B%9HI05B&Lek;"4E:md:V6&k?&C7N0n>I!?Mt.o7hBU
%,h8ssb)J9g5?^H$:!KE8&`6;7,o_iV3k*[aL18K?^J'[oHIaR!$j[[^!MiiTr&K\@R5J1?<5C3iM!QPXF=&4$MMUCuS^i"R?Jt7>
%(1fd76Y)uYZ:-S&7i67`Mp,W[VFDkR#J7L#1A(Y"N$S[f^L_uo<b%83_]kUUC<'kZL%f]l(onE0[3Eo9j_J&B9jlBf6EC6S/E_Ue
%6F-$Lg?NeH4i3!=TU>0WR?P"Eh;[pe_,&G)3We/M"e,ZYD+6PTqC7[7'9\M5Z]U:^rA$D%+oWT`QkhjCH&$3.TPP*h*UE8!$WH`f
%2A-V\^Lf?4e38G$hup;2pe-bL3*"&hc*W]YI198q=]U<m'e%b[*9ebA`ur<XT]p#PEE+$PDmQq)I:>sVjC(VC'F-QCO(<J:6naoL
%ZP90%:A$,$REq`EedaYh9emtpD@$%Gll"6@F<\`4O\a&.9*j'>1[L<sB$;18Z]*Fgpe%bP)M?)]FgIl"]cph)?thY%OJiD<`0ufb
%5pCKRK+sHb'9spWd?XggNmP6T"?(Gib+35].%4uEBP=JT?i7-O9EHr&mM<ap`&I#KjYg)?0[4#U\l->?M<Yj1BkbemGqRH\i8bAm
%fo!EHK=JX/N"QB3Y#\2P+hB*2%erp*W/5>29L,>p*@\9e6?gV=C?\,_<al7FM=F:P:PCcZLU(K33/_,hpoOUs2(+;@L4$X'U*fL0
%3b.O$i*hH*<%Cb&=X@0a<,Sehcl$A<ZV1AQ1Bh9+aT,rLMFKBhK1Wd&@G)4#Re_^]?-n64ZD3QLgam8r>#*>%p*/LTN.YlSRkR:"
%)UAeqSZ5[Gn#/_$pi"D4OCpCk[AlX>e.'9U;e3-[@2qnh;D`6iN(<NA^p*3ll!XqXNJg(>58me*kQe5s4OY%'6rOUCa4+S0'LL[d
%FOA"S$rrZK+Y2,W<lPc`=g/K<aB;A"nS3%-1NUZ8D=IeHEb\cSh[)"n_Gm0S(E&rj[TnnoSRm,W)3FcF@TWXls/mD\NBsUnZ[:%$
%&(HG`8W*[QEc:`EI79I\Fm,l<?pL02-0hG$b)Nf@BG8W&@t8?o/0Uk8Yj'l[fOdpNS@@Q52jD<sCL/44/\dkb:<GEO;/MI@.`<G\
%ONr#UEK?ZD,BJT'j;+8U.\OI)RjpaK>6;37VGcK<L>$,_o`@.Z$$qs1i&_.T@^%0]euVan8r1:1CK'$4#11hP0:H4@fb(3O'sl3n
%O)8A'PEH0+VOok-N36J%Fj3aeMcFKV;\bdEJe^a?Ha%a#_M97b_Le]!K6D#S>dJ0gD'EcPdt>n@iZ,$l8b4#W6(_`lPZ.-R[]%LZ
%5(:oQq/iR-`*3\n%X_!M?'E]b@`DhPZa;$4T2n(53Ek>pq$naHM29J`Ji(u(iOnCEU,@^,B#b2DF)*dpYr`\pJ<iDBJ,hQrJuP4R
%!pd-Pk-VXkZVW(X:=+=C<(+Zp>\gaH=[AqnoV464=K@4*K3N*;pan-/eM7H^3(/^64N2()eLV*2r6hGBAd)#1Ym]\!EbGkd!,"TN
%'V*p^jqETDj)@`0<?P%s_l`EYLS/cgdaA4^A(h#RW-VQ(W^l*6]VW_/_ue97I82,V4Trk![V2Ju_OG*A/_RjbnNU+d>-!TGLX=ac
%M6.i_7SN.[,$BW7f7T8,FWmdD3mYSbQVcj-]*3A/4Nu*l3.2Z%pnk8a6nM8t286je#T?5$!#<et#\TuG\22O%mF1Q=Lot;;Bl\@I
%.*CI(0WYU9B'<qMLHR;j&Xs)&d0%gEVSeNGO<ON*YuXiib\Xo?#JdUq'oeV=g<c_U85ijIZ*0jMhWj45N8gk5`jfU5Od?^3;G2o,
%P%PNhjT('?@EM.k9(LSG3>J\sA<7:rB,h4G7Di"MJaWoU)p='Dk#)>&K5ch.D7J+5"Rc1Ud9n;dbYr$e[btg/:`u./S)8.dX5n/D
%5V!231ln:cQ`X#NQf:tIj#=%E`&'.;3,T%M6@EA,/-a)8X)'qX6`qD`rVi*H;D$+IJ^KG%TeNJT7=5\ifK'YM*dIKua--5%e:QZ/
%QV4)de/Uu#4qeA=1a9<U(]8`r52:5qCUNA(57?I%`+6s.d4X__O\%CPs7[Va/P??g*<AP*gIoloG1K\:XOJAmBk^I7#n^)P989=o
%&NuP.U>@'qq/@).U8GNX++m_YW>!aZ8FWq4AW,e^0PV"AWXRJ/aZpss*Q$ce@)qk[.^psZnFo+k]F76Flih\@&HjB\\/]rE+]^D3
%P_m(/fcJ2B^-W?$a9uT</'4<uA9\D_.3EL\HSuFgOp(>5Yu+=TB^.JXKa-J\Ked"R)1T#K#Xh9'Rhi[eH%BRbeVJG#@GS%r.<:sO
%B\N\AVC:6<()t%g;/@%<T4VMt/WLq;hfbod,oKOi(>k6BDBKX#hgi^M*ND,qB^m+AigSp;SFIW9aq_o'OOjYRge^Z4%)J`P`j9a%
%fldX=9X?L>64"$@@Qbg#Ti"H>'#'j#aU-,Rmr`Gnd=T3](("B["Pm))6ZIntW9Rj9Lq&]aGCVn1eTMk/#jON("54;F(g!p\jL=e9
%b:Gh2[c:rO=kq+-*M33LG)@\o%cB^/'Xoi*h-rTR6tpYDZ"5$%[_KLkSV=9^Gd>,??km"3W0XV.);@9&P[<)&0PJ^k?T3fgM.Sb8
%$4L>dQppRp2fiC7OMLX92Hq$iBVD0D9JI'VpV_[8nK\"V:N2"&r?o=Vn+jo^oK3#Ql]_NLP1-gdRd5VpGm.k>["iPO/A\VY+6[?#
%6!taMLp$=P9tNRsQS\?3!s5RA=W@j*KN($$"l8Ef?FI;lARV)!-0R5Ee1sRtRHR8L,!(XKJg,JklcWn:j%&s<dnQ.S'*VG8dYt2l
%J:Y(U6:T<=[TQai+RM=2MA@1oLO2PrEFG,A*uMn#)6sSl/<#-X/re_\+e_"r>7p31otMG^O/>=4DZ`R_%Tk=4CP<O0P(UO0c4IFC
%%S>?_7cu<1!r2B]r=IG%j3.Lpb\t@,7;32hr/T`5%A8aTPsg9uckU%NCOY0B):T)r:g"bS&jj6(c`l&FdhqD(na+OZOuc?me6k9_
%)K[Iu%0._c</kTf<-;fuRtq6!U_1&;n$'9%kb>k)iQjg!9Zbu'1'1f!YS3J1K*MVERUgkHlq()pXjYjm"^Y\*1rPk5:6Y#%&lpmr
%L]Od3N5)#4pBY>cM,oHf/JU>ToIqfH9Pp?QA%:s1iWp53;o0GI7K<o21VpK]$k4@o<NTX`X7mtrkBF-$9@/5H+->=!FWqPnI4;p:
%:d[%?QrVYEC&)XfAf&kFKU@.3FF&*`&a`&u=u@p;,9sa_=jlqN(_i/%-:m"^kBPZe(pq2,oa/"@@t1WG<<L5G&HaS_W!aWUh!cil
%4t#aK:2`'%%cpGTr;OtA:7&Y.*b#%aR^Bb:e.FD_)Z7YbQngc8bGjFPIuD4=`mmDt<-7.@#L73@plaA6=:N*8oU+.$Aqo5WAZX*8
%k)e>dAtGmK\7LrZie#W5l^.]NM2DG::`WCKl@C\6@nl_?%Ja-_P,jIL-*(R!PmC&Pb:%Q@!'#G;QfDXmZqrQL1pWV*f!kd7P%&Qa
%Oc#cC.H1C>6B7d-^R@M]d#gWr#=(]9/BL2j,!ng/ZPq3'\X`>,<D=.$EDVt`rZ'MN]@46kT2o)?\hg5F$_RP4MSSF2aG0Gq3Xmsl
%"p/=/:U;R85t!ps].[Tk6M-8(:t&(DJ/(>&E&i.hL17`0HB3V];oCX6YG&4eXj1YiHZ073l2!/Tekh=!^uh[Qf.ku@6ck:`_?/gK
%A?M:^?qHjjDVB?E=#_%mR%qA?f=s]%lj]bf!P,>VLuA.T?8KBI59IflBF.Qj8qj+]&@\")\3Xaa<27JD`b>DfC-LcpREZg:pq9bP
%;!*DRN@s,_amm#F[(!ubX;Y`=7B>nM4?r*?:Vf]Bc6_Zl`b\@CFc:^MLIXuCZDl?<MHT#*rD+1Ac\WPNqO\fK_&Q]IW]\hk$8Af*
%VPC_e24Df''m50Y/tW(JqQ^!sKUs:i`7NU<;M).8VU'3R:;pGW?E/jWT[(4np;Gu]\t:@rMPk*hZ=&k`Ld4GN&.uULUNHahW0622
%%,U*2X\Jl)@l)qu5?Iq`V(i0Ig1`Gn9HJehg2f2t^B]L0ZO'e+9Y!2f"NLi`MM!tjcU#WF8T4Yn&CI1<GI<lP9UBkT'G2c\`!ho<
%FYfa\Wt@8kT7'72!B#bprYTb,Y))6,j%c8pB1WsbU*<][Ki/#4#g)Au-7Qtf6qqnR8O2PL&Q*U>R:o1C!S@Ee,)1c(]X+CWR6jm!
%,d2eGh[r"B<'s($*$:n$LdVE1-CVND;I#006n_U?=+K.`L8mdFl^Wd)JD7<dBH=&CN$WCXXB]2VekE0T$Z%3"*k]V=C><9-0VPbE
%=4EiK$^r0HeZsF._8-er#>_M232j"o%$6/iiQ<XocIL="0#9Y_+X.LK"`sHlAB-M49VM-PcpR^:7%ACb#>u-5^CVcn@"I&b!(oAs
%8fZT>;l)#6(/Ao;\sS@=a[DCG_g::AD;53g5`HcgViabB:KuZoL#=:e1)41\[mL0M^OM>CFiQt<Lk6<PJj[Io%q=*pCEdN4OKd4S
%nY'*RAOXgBU+^*2ILn5AN]iIF@^86&;.a?P0`jNe"[M\n;;ZnL%:WSpOgdYL6n`ij,;L[eDG0D5Z9$]+jJl[0PpaLLN)n7V)<+AL
%MhMp%7+<0WWWLUhR,1Z[:Q_<KK)c:k(*$al[_dD5+dOrA7:o*5FbQcsAt#.!PbU/p,I4F?4.mPrf_IBrKntW;O=!:doZ#8+P*JCV
%nR<i1i,jIm1ddd2ds319S"`Z?VEdY\'HLH*RA*'g24)JoH1pT/6=K1&B<j15Xc!21'2bITBZ+WAQVIkB0VW2!17c4:VZb'rK:'pO
%WAXG6Zj3W>C,RQU/p!A\*X^:n7$0VSQ2afk!ck,*ASZF6G0E6J<3)LGr<NSc!4/+:&$/7&;<>^4+],k@1nX+^mn$PT)L$_a=$bg0
%W_d^0U]hG-m_>!Q`^d<6C09M?Y/&)Z+aHJWG(M%nVDO$/#uNG4CKS@FfOOk+\-I&!K>=KlIOA2o`DBo(#(7jCDIbHJOo$0F(%EJP
%#SV3^?]EZWH,qi=OW$$7[IUS%Pun_G-7fG^e,sG>#6@.O2S'$&UgO]2&<JuG'Fe3%"dVA/r&lap.45=-G13LUqO@s>qI#G-+kW1k
%(9I,+&F^^(&]hp*?K)`@2,#$D#(GDNfrJ].b14St9T`+/kUnMH5b4h.Tg3S>p5^<[B,.$d?7@6>RJ[I5OW]1".UjU?8"n',UG:fl
%@1f)^:`*"WJHb$7"9C)cn\>.U.p3;_T%WHMRsG5.b,^A*TYU%IEVr?l2Z6b>>UBmh_&4VHK.u+<S8JmnI\<dF8Q[e]Rn<X^bkGZ%
%_\0&*ghh2b6UqZ2`ircrVe7E[-n6shLqul^^6]EK8b>&64LhV.7eqm4,DZZVGM3mh#2]bGRo'dl)MPF5]kZ[&#ff(?V5=pREX.q.
%rJaeu]<<q._Tp(LNO-uQ1dO-6$QP$S3a3k67tn$Tom2Fc]#nf9Q]7LSe8*<=-6e-1W6$qk92OJQ_'=8=/QgWLX><G#%?hV5,a'X#
%5ePLGot2ZT2[).PV8cGDQjU?D"*[#U8rkq3"lYHcJoh`J?9;h0Y>;2o(RmC5jE6cE*?qYo\6E0tX9@,%GY>M5d?(Ak1^3="g(u;p
%QXD+GGen$e'iN]8=@<C\jfo`P7%[]>>!ih\a?iR^Ca?P:E6h_a!g->]03M[p1da`)(=E$;/^s69a=lh7Usl1Y7O^p3eq9</NBZm0
%W0_7(Piru"ff@@:f]X(3k+RkJ;.,!d'9DXH4$CoNAlB_uia7j`lo6+$\6`p&ie0$Ah=:@HW=]6Ug#8"/E1l\s'[\+t*Rlbci;S9.
%)W,*<)+T[=Te8Y""aime(susFO2e8u,c:NNJLU:eD_X@Xh+XmqLggCD.2=\Y(/HT7-m25/C09oTYGoU,OS13!/P]Gc2X>]gS$VgO
%gb0J&ZIUZpLKuA]YSb;/RU@'e@3,sQZ[Un/=7ur*bM/NT!>:Z\QFUL/^al83$&KKnk$@VV#KNuW$*`P<X`\^l3mp^!AW;?j1W)(M
%o\$OF9?H,?Y9)qQS\Jb<Yqg)='@@uC4"FV1E)/fUEji2i%6'SJ'Vt[+#@B&m\[1EVQE9@]"R6O3)jGe"Re4RPkBkT**h1DAS,=RS
%0-i2D6)%$9MI&q+jBIcm'fg=>md,p>%5KjoPuI-q5<mr*)J!$*\EK6GQn/ESQjB08fOAr\O,n/AVH9NZ&ogBdE^R=a!Xb(bi"2c9
%79^omD5r6k?%S*Z#6f!MqI8&3MoAL/amu)Zr+3hO,C7tM^,O[RP1<kg]pg`Bl`9(MRu6HfRoN9(OF,@0jJ!\EU+$RmZ.WQmqK%u#
%KEfN)@b*ANkYV,3q2KMNSX@djM5VTc)-%`lCPu.bbEEgm_B]][a^,"6r<.D+_4\]PANZ'^-mmc?,)/1V8UPCjY%o%c&lisqFP$3L
%<5ZtqIh_#oETZM%UA<FZ/e[F'$m(JR4^,-]iIJ.I9o85fRS0a&[IU`AU.Jg5S,7YQ"ESP8qOm3T[-.d13M$!@6u48+$c>EW@NJ+(
%2rPS3>Z63[etYA44ea4"LA(0\'MX`'bCU=EO),]@Ne(#20AlLa<)<#)IV,=O3R"S!>FjVST+2]FLQG.U2h3ad.@c5cG6/EDK@<YC
%6#BTZlnL6TF8AA])o?tHU3V!X^l;V<h5n1+NQ"\U)T\[VXQ]lon?6eVl<!2C#KYltaMc%J#(>h:(i4F2"0^]d&p75@D;uh?k%W:T
%Es2%W1o$7O%ea^2Uuqh#cYjT[9iA@ucnFo`"!R?#n>.rJcK<3rDT%F^*%^it`X9R3LtG<u>Lr`bS?@LVh7!hGCT'N!<YI\<G+,)[
%j9hdu+MMr>Q-.G\R6t>B5mG(5P29npYhU@$89GUbfeJ\R*aF^[S,`9RO8@7"Is1J;IspM;5P`:5\'KPPp%k"nhS&mQ%js:pB5ar;
%qTH30T3pEOs7AOYY"gUq?6:otB[Dg'qq7%dd4Zmkk-JuTr1eK.LHfhWT?k6Js7O.H?iN9&h]/Jfh]D`gs6G\OO'd;.a"c!(65-E>
%pe(^FBZ5`N<2;FoU`\Kg&'eB*&,>p#5EF31olER>qYg2mnX>.iSlEc'KZECE8POoc%*@rZ"s9_(W_*hK"K1N69o&]j1XTh_PetAf
%W.i0&MDH0@<m,!r1fBQsY=X4@CS</FE=W;:FA;o))8[,"$$htA<JEmHVm]9u\g9sY:-'?4k/9eELf"d9?e"Kl9e9<?(-D<%LJGfs
%@Sf*I/r;QJ%H1q/,#+?808$3W&"1L!c)8tljWAcEVaemB&A+=^R2F'G2M`'65[krO/XM;5F$^Jf\PBI(5b44D0HX!Z<5XQL6ZVkp
%4h[+K*@$Cal!QM?F:\bORbf<]<i`kD7@f82qa%,MOQfYYLl%Y%0o/s8-RImdKp]u3Ef(nBa/8*!@M+HBg,Lu:8qgq*%[P/mMN[XK
%4`DqWe)_0Q@OG`DM-S\'VDeG7QC?-q4]IQ#R9j?;k^dbFRE23@[&$,#Hkrdgp?XKhVhWN;CsO4*!/ub%gZq07PX]=LN4i9'S"F1@
%g`SZ"D37!Q:A.$9;EIV0b8?c-e-g7YaBaX"f:bbnF<Qg\hg-]-cY7CqNq:Xi'Fmjbe"jNe4can[EgP0G%udueMUJhH34ssCS1,m>
%Tnae:U*XPX1S(:L#+b>EZLm(o4MaFb*6Q2j<=kR#8SH^1KQ/>sdYIP>lbK>3$@56CDAB_Iq$(J#B@7iY>T@(*R'V1#_%iGUOD*?A
%9LFZb@n+jUg-_.t0KHqM:iWt@nA%Y^BK-mI=HOpha:TNB*n9MpFMZ`WGV/GZ19n;:h$+1X@3S4$^;;oFMM#Me5S$.'Sb-tXTNfi9
%G[RU4BFeVmQ76VK15JA;2`h^qc)WOX,V[A!"to#SFDs2h+Obs$%]NkqI>CL+hj^/b2C\.Y,h7s4Ch&[JC#*P8Nq=9&>ccK>(q`g9
%@_1W,0JR2O>AYA_;oZk9CZgPN2Wg0R+]Xnchm^_(2=R2oR3E2Y0-.`X9HRn!A)j%4JhB',E^]Oo.Jg-k'.GS'ku*XHLoiW'6,Z5V
%H*4]P`2[JYo]T2SBG5^A$&<1I"d_b*;N[2;R6rCT8YURU@5.8JNH%4>R#G]OPNf?=3<[LdYM*)F*=%_b"^17AC7po%FiW+I1b##s
%4[ciWZ9oU-b=%)l&Y_H3jE58>Nu0n"Zj1VY1o8gDY<EV`N1l-k/]8tH3>$[uZ\KO$(O@R6CZF?*p:qQ/7.fR>;'M>m4N1("=d9M9
%VUpL0NNfa(dUO>XaIWIq0'i\i'A^WP`ik@9#2sG;4B2)SQ=hmrM%J'9V1I*Zh`46R:1,;h^bg3X(+"qhi2p_0:(tH'H@9S=Ss4%F
%]6*jYiko.J(P\+)g"ELgE$i6[Z">fsb9AU,aBoq4kHtPq6RCnehgGis&Os]/-`WOWTTX=`./Ad*EiuFeJp3OF;Q,e920EIu=X=<5
%7Z_bgGS\P!(-:R#M+g79[3!%[FV[jjLQBN<J$]Fbe9Qc>`7?<pHgL<7@P,"GV6F0Tq.>HbZ<NFtfi)%"_$([]DnR`i;5?UGD\\+O
%e/je.8#@*F%ML&;Tubg.RPT[]T4ra<lu'.(6dL(bXX?=Q'*H&UK>/ZCM05GB^G,Y@>TkbJSqt)4^%1)XiuLtIc(*"(@&Zt?-C2>9
%"MHo)S@^M3`$6dgWE9\"/L>M0"IWqu[2&+*G2<Ws6k/h`9e^+2B/i'''LL;l_G^2_M01A%nt@3LbT9rN.o[7%Jr+n(<-O.(5Y[I[
%E$D%nA#*pFO<nBY>h1jGW&PErkh.FERiE=1h-e?Wj=4/]W/&<<RHIS,`(aO!Q<(YV)u!60I_pr#<9'h?k;Z"aLi0af!=3O_>F_2r
%0@9kWjjI9^84B%;3nhf)7d5!TUlnjlOI#(=3(Ks,6punU%OUDKWWQbcPK6>O?>ZutbNp_$bVGMrO8U1*)Zs13@hH7L-r,A;(<EGo
%%(3*iU!"L?.\;g;0a:,8'"eMjct]m(=2E(1V9[#H:lt1dnQ4:X8tJ33!i0n[RVE@=Z5WKW>]i_(kCo`f>+RjPZsn&H(^S=[CsqNq
%rg_I3MF%tirV1#XFn(;\:>BU^(H[Ot%*V`(L_+FNaQc[uZB,SEaEp>ki?4nt;E7'i+J'IFM?tlg(mp0M8oj<*I0Q0C"uJSHcX_P5
%6!3n^fK.t/aRQ<"6K_[2RUc1Xb6U-5o'VFT5kA;gLd5%4DB&n-^CWJXMWpWfK&*47'n5c,n0RoG#t'7s$_*rF6]olmqNW*7*<XD'
%Hqq2qS31_T*`,^YL]tpETlg\G\kVY:8rhr.-OO<1WM299^]k#XU9i7e<HcGPr'k_'U[0>&m5lt*&$`'q80VP7im!T,>*_S.nQ1lN
%b!h>N"Y0XicQ^V3BY3-;.eP@[h;puf9UBLjo(L,fL"O)PH(9^76YsU,Aetgug$liDUW-AqERE&^h]bFM%$dFlq7.1sAGTE5->]!4
%&U?.2^.B,8UBO1WeOf.;=bQ2hLe$;*(?q<0=suc*>,8#R7g4*OD0QulfcKc2]<+[Al.(Dg_'FkI:m_h,])Zo2ERu)q5'1-ZkSM*L
%aE'q4'c!,)/\;1C(h)>FD[/lBn/00-D*hm[;M`&mlqCY5lG0\>;&!KF1!nQdWT@^MLd)p_A")uQ($(df3%RN83Me2$U?b'IQUrf`
%PK[;VP2O9fQ(6B@`Z!Z2!*r0:8m+3>0p)./npsJF!MlSiY=N0&@k8?0D<9r]B$mpWj'<4&$*=<mW+O+1S9$6_/QKG'"H&/tlb%+$
%XHiuK=[aRu@_&Ru1*F?9Q-9PLgp0TLWa9Bm.31P87$(-[X\'J+PV($eFC>@pR+Pq]3eepK_3egg]0b(*j$>4'r5e%HP<FaS_!K9F
%(*<]fPo!1:<=T#lG-!.-?k[Q-S'W9Ud%,qU1[&X\hud,^0QIMm,4HlrX"$+D0LkmIT6!c>dEb`[O`>:D]a48%XuHaA9GICc('1p[
%+s/r@LV?(cbh`)9:<d&9:<!iV@$7b?4cK!Slf,mPq[0O^&<im9Z`;J2L<AiAqs8k<Q8((NBfO()Rn74S=f>Ae<,NEp0n1D`bVq.m
%Fd1RA`A\-:V+;na663umc`>?cZ6\=!b%opn6B=_dE$Pu8)Fn)i6q.8'ROsbU:?2l)0bn/4r0U;$H)7b4LFK@'jWngc(>io*L'DNX
%%N?(p$?"WS7j2nfSK.nu02.^9V(?0Lc5Y`Tfo\hrTQ@q;G->ZlWkU;02?aF0C.@Q1eOe+2CEg*&*k1i.i`O8on%.s_/fbV!j)@Yp
%/\0N$M;\m@^q7SnUKi[Yq"teGAIpY0/E5]&U?2&%I9b=*F;=c,d8RFX[S[?.D`(V/T0cHg;mZ+hVDF)YN=Odq#cX+3GTfPgR$9b_
%MUD`*N%4KVqj-uZ$#^/VCad0H8UW:XLqUiVbTs&VZng6u_^5pdrSiTj+EEAX@nEX4gBtdkW_\Ah:tT=-^!q_"dH8<S,Q^*rk,=,q
%kK/^S3'q.qf]p830oMtQlcdU`ROZ]ep*L22LeCC-2t;OOK6Us<-(@H54il=)*lqa+;?4rcWV2P?c07POKdO^V^kGKTR:?M_Kdjn8
%c2'HK9N]92H6l9c6S`c+\$G@XS]N-HBgO.I$P,Cg@`b>'&gSj=d.`g]3^$1'*aZsW>unL-*t-A@@>c;Z7n`E'H*R\G/-n3cS&rWj
%I33=mMRZhP#OF>p2RO/VR*^]"(fS&BMTY+PLuG-gLbGEpF?D'm%)L"Q`i(if=Vb"o0T+A;*`t#[6M+QNA>%V^HpeN9OBKNg,DV&.
%i;sr1^Oo45jrI-E,l"B!bru/s7me//Z#kiXd#&t#Wj"%K&Cgr'`Cu[kq!Oe60u^d:UQ$)k]>cB.Od/i$9f<Qr64`A2lcFB/Otc"e
%U]fF]&jp-SP2C%*)/@WaemJ\mO/qui7IXi?>%%/_C?9\eYc-Q#OU%q#eHk^3`1glW<]+6WNpnXGejGR0bB,EpY/jk5a=jV+S(O1/
%(e=:6Ne+OB=geEB-S$cOTpQ*m(tfFG=/#^A1>Rj/SES6EoH8=f7QS6X-%1R29o/K!\@q?/$_,h3=)S(q/\W[#1Hije4U*08bnZ3<
%'P2Hi?n]OLWf05/baIZ`_53>O*N>i1LaAUl#f)8:;t`PrW44Mj;crFkMlC%F<ob.HgFB3f+iM4sf\tCtA/u/i'Y[-n/#E]O1_[Eg
%N*K%9j#C&8Wt$%B7jg6fLcS6P&LG#eNB*e;Le55n7!.RP-4`f<W\Pn9*n[;;;bP.)7!@$t\S6ikCme5Qa/Y:6)+p*L;@\=B9+7E/
%6l$H\j"uElAWdJ:,XeeXc:"k%8Cl<2fPFbt<j$+?WW0f;5=RqMV7%munPLcY$di0l!4aB`hfu"WG^]7jEc"BjBQjS<0LVX0Jm@E2
%JZ_]&EMOEUVItRX(@_XXb_g!C;uJ=WJun%"b`%3!6<J!)-Jp_5m&4A%BT`M8^TWm^>?G7.hg9M_'bW/gS7iu3b9]sg7%V<+W@r$u
%kNpWA#2:ie,!2;q@M'iZp+Y`&MqkGjcM3_2Jn7[hZLhfi;C:\l^Uq5V_jg7uS+E[F^NcGo%T?R2ji9U^h:MRChT8!2qOdf]S+DQc
%eW54P0D>.f^Q/?efl)q$GQ%Ass8LREl-9%se]F;!ht'X9*<#MJQPW``+('s-^,c5Qh9FX-T,mlX2#P=^2^`h(S:5U0nbUM`'t4H=
%s75V[2tJ-U^M^daqMbIEVq5;RpY"fU0<a8=]cQe?jn7M[G4[G[N\=Z9q=ZK@H?/ONJ+=m]k!-#oDr*\+Qia_+m24<Sej&'>/:[3Y
%TE!p1l`X&Ip[=a2q<JKrU.2b`N;L`o-h6SY3IJHr;g@XSY#G,Mm\\puQ:ZIHG5g[.DDo]A%JP31hjhHqq.df`[,'';NHj?*@X.o>
%>eRcIrTVSK?dH0b^pW^8p@m_*RkNV_XZ'm2hd(%YY%Y=$"u>8\pJ9t1PIG2>H#r]ZhORrCDW-Cqg@&cs=4a)$SDTi#G0uT^^?'Nf
%3ks<h%WAc3j["9<0c'QNrR6s;]m!f6q*-WLs';CK^J&bCVt\GO)=_pSr>tQco;foVj"95G>4(U&I<tk[c0<or]fGobX7Odl1YO[Z
%h9G-1!A9SGrqc!+aj'LJ%9&ZhGC3b(?!i&/rV$/$gNE<_UL;p=r<)h]?_)UjoZ9CagFH8V*E9S%.X+_Nrnco;0N>Rl9]D_fm95e0
%@$E`dEr2cVIJN#9>_S*n?(\kpk]6R=oFAIf6'j[sE_fl*)d:N?ci/66n`a+&YHP]d(2g/hi>H;jgEs2oqG)?CqTbu&r%Ra`Gqmu;
%ndOBu%GKRkS(GD9qs4%aNG"I5]JeZt]Y:n#pXi.3%r-'qPQLjOjPAS"k9j?GI!g,7/N7T-DQgORp=DCem9A_Wjc+]%_aen)p"4WZ
%FoCEgDpN=8j#0$Y]Dd/KgQT;3)eA3B?_<n']/Y*-lMBf5c,nM%IC]BASV]^OqZMEI\$*!=c9>ono-_/=i!"t,'TRHbI<#5J?bcF%
%EQ.O8T^_?f('Bf0gXY@C7('[UpZBY_e$DhG:UX!]<q?Dhs+dI;\)@#L*kO**"GXX/giOs_j8ZcA4oT*3;Z+iNom4Ai)Z+EDaI?eH
%h*/):)<27Zk>sU_gPc\.s*2HV<$/haI<M3Xd.;g3J\CddD!&W^s.Xm7T>5nobu<):50V3TD]E/;g@LX_fD[Eb]_*58[_Fp_cWbJ[
%4&jNabTUkl6eq-YGg$_ACC]S8Yc0#>)Ep)Eq8o,tS$+HjDRN7$8\L*"k;RlM3IfQ8Ed/RrV@reMQJN._I(S1\hk%OWo,4[kPE^7!
%?aT",+!5V>LFAc^`l<eQ1;195^A-fZZa9\b@&Vu_5c.9G-@9Y[gb=LN*7I,p3rT]2A8C^U6PBBfPLo!d[]i%SgoZ&!>b\N1T3n+)
%n[Lb3c"t;;4IM.s/ViZp*nd)V^"72:rp'@>qTg+PdlgYtSei>-0X!WN?I(&@E(f?AJU.38oUP(U9DT'Aee]9Q7Si2rJ'YZ!]=d&t
%3!siX2XL=[fFTV0jKeZtfHN%6Q-8U(aM3McrZ><]s5%(`Vt0=>EI`X*DD45gqWd/,lKmNZZ\u0=D0jk5TBtjArF1D+i5=`%s!PFI
%hdc1J/'lt=SZq.]KIol+rr[eCKf&PCL1Bme,bHZPGq-#BYKl$SI;s:RG'S7Ro@oJ4TB)3rNpr[9H[klrJ%gEips1Od*#e8<+l%FQ
%Ye^b@AD"c\almV0R)d[;W4usNc<^"N=-QASg[EjerT8$fp0up_qNfXt-].T&_,0f8=P/q]I,DD^heq2'KDO]!lcUjlHJE=<Hb!Q8
%Dm$R^6eAr>_j`Ft:>\:"PLm"aDpJBoj^"EOG;ZE1Hu>Jn55mX8`;A_A]D^$8hd]"Fl=&HL-7#^:23"[&X"DB\o^pu$]C1P>\@"Uq
%j'>CFir&*.A4&i(l,gp/Dm-VQVCtYf2(?#naF->hgpT0dUtr2NEcU]pagW4d1B`H%P;aFL$Yd/P.=J`]7f:I$Mi2H2mh\(:/5E@n
%0/m24T7<pS49K!"([VcjZ53l%l#V_MfeStn@_R*(OXQ^-@G``X(=9H:(Q*80]L#I*'a"uO.!oD`hf?&pb0tEf7<r9J%^@G^XSH^0
%%A$*AasO!is03uu6Cp8e:9c3O-M(2CRqEUsidUs5.%:_*hQqo'/BY\!e;%.j^#<n!p0up_qK%Wb3IOZ--sJ%i<,V[/l(rZ_0f>Ho
%ncQDA0)+pNKBI*/n2fpSdmMo83ZITd8$U5L4=g?H?i9fa^LB<jcZ`c*fXJiP@PN0Y83bE0;#4[srp5lSIMg5OJZjJ-?RlAq[UBIP
%I-n`$%XdY8n?lW<)3fc`FqE:!RB<%-+['eP"'>@RY'(B-%`6qNWu\_(F5^l78*KObpp^!78N&-95O+D[;@^L[2>ZfX<"ISo_\*rl
%dGZ#"3]#G0jUKUn1?h#@$:nFUmRe]#_:7B=_ti380)fg0o'PU%aEcRMZ[@5pqWuDsX2f!UhMR!_qX!;*=.:llQF=EZlK="Xnij5I
%oRTj:(0HFr'$@ASp%E1<6@&UL%S.uQ,a@bWP@%W\b+AV6,a@,h[j1oEE(<*Qs)poms*Z[%*rl6fiua:F:6*Nc1lr56I+^I+k]mmm
%r$No(]8=ki<+u3O"S$E"c4;Xgs(SuKp-'^HirRnYG4t,"^B`rLDDFDY0>G5M47r%"^O(o8rIb#aQ]0i;B(mR]^]lmJ?aa5;^[1IH
%D_P;mjfs*fRY0Lkke(=6O+f@FRR>m!I<`uFGA@\&GB<OEaa!:3`"1nAHiH!q]K*b&lLO:RMo>0shoLi]^A'6#af"?YpqXR+AGR<%
%r=8haKE([5B6h]#]*%!-I65UWs6mf(CQgjTIh2P9pJ1$N$00tD4nsO,60XLo^qkH);3F':I.u-r[HE:;K?s;Mo0)kr5?Rp?+FTi2
%q;o5tB<SJA`liV7jc&$m`NB^*[q?KnfAF5jMAPH*rp")PG]?=US/Z'bpBNcY+1?3E:H'JkheKh\WF5lZJ+r*KU$llif'VUHcMR>6
%V:Fo1N#nh?1HJXZ%Ml@`ZB/pL\XnA?lqfD0?m[t&aUVpgk@l7V4lqkl^H"BfqIE-RB:k-<q#&IiTD\B[qVpl(P;d-X5Il5aEqQA<
%qd;YC"m!r5CZa<hpg5!-9hX63]sDRnS50(Hr:8[g@jM@u]hN1N@l)B5cIibK9Z#4Q@oIh&MT2nnD,9`TDlJ!Xp?T'6+d9S7!2Is?
%TBo>*gciP]s6]+qdXBIFk>-9qG!;eC4q!k4_0iWAjs/Z6S`YCf)cJm:?4M^VKAkp"Gkp8rGVHcM$nhk'b<PL_P*n`I05?2LqochF
%ZH!mI_=7"=L6p_Q8'`R.B5\2LT;qJSUb,-`/Z&/n1toXpc^u63`*O9#4r-M1g0bRu!!;j7CgmcK_nLatCtXRGfYfrWU@%u]ptVlo
%4BnV<Mt<E&S=Dh/_`Bo?Hi1,7qr;od@QHW34&WEYrdikMG7SWPY4q.1_">8NQp+nb&*QXkoVGh6diSundAL?B]Y=C-:o*6aA`[hq
%SN:<Cmu9m-jO;d@'T.;:(_=:pX7WmW*N^o7fMHFM)t!CmfR*%/rT0AacYhr)]K(>POX36"49'Z!it"ulpj72YGq&pn[PK#QFmsK2
%je/:f1pm51d(X-0fq$@LL!Mu/s"q1*=?@5%[S:4hYe*8*f13;Y&mq2HMn?<M4.B"!17`@/?Z-;2e92'Y`ZAdpj^?,R69<AT[pudX
%@)pnTLP$UWD`-@KG39BZN.W-\hfe-ia#M^+%X*doZX_d\C^O*emXF<ImlC#ODm>,$hHg-[o]G5<RrWT%`k.\hklr*]PC_;-0/p]:
%Hu\`Nlo/k.rnk>oX4&XM_4C;3RbQBcV`3ctViTMODV55mh4MtG2BBY=X`lY50Ai:YMfA3ulgl_(..Y"EcHNlWgPba[g&<c3)VW,E
%^3k!M\OB]\B"OPSL2&%of39l_p$QG^4Fs-5]N%`LpZV\To]QK"@%TX.U^gKu)cZ$Zc%=!V0m\3QCs^3Xr;>g8RkNW(%/0R_NIoI]
%rFD*k^$g]/NsU-<92k55-$^L>V=-Vt58ZcPA@%mmQJMJ'K"eZ]k3RB7f9RlPHbbtp?3+E&VXq\2pt?;Jn`3C62hM?!.q?4R=Q?$L
%2?)`d3mU#uMr1@YHI1dAa[j%njgO]kK/Rr#jpp,#PL\eRL]@ZsDEk";d,t%?3^4$iD?C6^6(?W:2Rd+Y%a*T+.<?Ri[-F=0*RqWP
%3d4E`%dMh*<p0JJqYKlWhjeMn)1/R1^$s?5S(fiXIFmZQo?ue[:$7;*cTSaP$l`0<"!GMLFjuBWoM;7kglodD38e9fE$_(uY/Cet
%pO&@[pB-M^)hZh_:j8_`qS.K!EH:oB:QC*MrUn:sq9?DK]#bZ_p?(+f/XA51\#XSZZ(V#Y\ORV%o2Mm?Ds6+Ba]nE3iPb)rO`Tja
%g@rY[g!sAVf$_B=L-pC^]Qi2;YEu0[2.'WrEBWbB>/K*&m-;B7o@9_.r<k1cro1QBi]=of5M4Na2HSQ-o^l[X(jE%RY%G'bf';Y7
%2d`/HSo(%LmH+WWp)faCk'FX8k$?\I626iMSWjT1:hLU)Ir."Q05YpQ,3&lNEq2HXQgZgm1p#fAk/1!@%PW/lQMC*QF-[Du\%0P.
%r\4Yp9]0$Fp?*pMTci\,Li`<?ZEq`eJNmSM^]*!>f2lT,1r9!OK>;dEm='S[h$6r<r`ER[qo2SG6IF6nM@MDhpHF[7Poi,&U$0#j
%.";elhYS\`fidh=iXJ$f]-#aq%1i8A[TrjIm-s^e;"=PU=IWN9n@P1-Q@3ud53^W<idT54Z1s2\F%Ep4_fiZTrkF?:/QVW,c2#Uc
%QY2f1/j---n`'K/%k$P$g1:bTiuUE9qu/Qm@ESKH3,[E)_rJ7>Er6Sq+Lgsu_qV^$Hh;r=)kWi.q+tmH?\P)4_\A`gIXKXB4NS9,
%]Lm--b?AiQ"\a3!0k^fV=VeRZ^dL;F"#/-s_#'.^ro]uiN:Qs2j$1#C7l5T#N,e<YY(%D*Iet-O5YmNa?_35E2W3m`,qd$u4MUit
%M=!6kG[:I/F+sDap_e(`]L,I9,NHaa!6KJj5m#qjP`1Z9A_P7JIfE3dLY_(ZJ&3<AJ'[gN!FNqRaI[a.K:ZA!*OugH`;Sqq+]CT5
%8')FsS%ZQuIS4hU-Rhip)mpiYn>Y6<-SJ:\V\b07g1Oca^%oqKRu_+(H&>AUE4!G?F*$ok`S#`Y[6NM_]rTuO5s-e6f5KMOSc8.N
%ZB:LHa;l:]g!_rns7S:1R6NC3Vf^(1\^TIJ<cKP6r_IH-Y24W<V38\:T(rml37rSYa+qcNHS9P+9>cOT]%p!,#Lg^\]>Ge1^UUd5
%BK1M1Gs-g_I[gtmO#2G!/;!MX?i7N[TE"cQ[1u&<=sh\\^Uq5V_rudtC0i=eQY1e3^KU[!a$9FVfBA"in^DD0^UV!#Vq:T#5Pq]I
%qSeMFjGlG$k/T]4GPhEOUYjOM\X,IEf),6;jd#u47Y;Is,*I/`\O:ommqXr[m"Bh?J>(<'V>p5'`aAa/B\sf+UYd-kTE"5;?B<E]
%p;\9TkXFmL^9#&fUY:l\)00XcQ_7t5_k>'LZLf&rLtH$gQ4";jbLAQd$Ot=,,,U^0VJpY"4d=>]/_!//0Dua^T[ifmj[a7EB^8h`
%P(p*(H;qqW@q*.o]oilZbMk=#i5UGj$4nf(/?pt4]ZdAKU]6ReUV#(idDP0"E6#N(X>)[!\@p+h2DfSe7RV/I^T=U'/DQWlT_\B2
%f2dt3.dPGgVW+Rb5Q5C,lO\\e1:8#u@:F['!n$#k5,P3`aQ[8WSea<^QaL@<RpM*Y)5QdSXDP=fl",molS8s].nhRjgKsPO^ABOP
%'7q%[]q$,Fpi7@fcNeO%\]m<fo(c'gpouu"9ROAZ^\m1H:/BbQrpfhs)L?o(3F.9`a$G%9]lR]7@l%:aeakMk,rjnlF8SP'A&slu
%:*_^j_<98BAC;mHfQp.FoUNock]n-%(35\fGFs#H0ucnopbK=[Zh0)M:`@MV`]&%.`g4!UZ_r68=\i)hR5u1WX[1OPH9&Qh=R"l^
%h5HcKp<nT?`u+^$Z(.+8r`>s/Vf@S`TA4K)-Sj(D87`2XAN_[+(lbsmL0$_/H<a]u;<C`fIrr.@h:dY0^glQhJYVnr&Ua5\#4Hed
%_AJVuF\K6?/tLQ9(M9a^)J_^Y4K`_kpc^8_?S8)MBT6e/=pQ"Q_CImi3-^FC?lUB%m6',_R)G*u@q0*`)tCL[o\`k5rLQg_3&6,c
%G[dU]<VZd.E>$:m,bF>]QsQRQ')cB3HuR2(q2!UgYmbLr8%d3/,sG^Y4^nL\/$rK*n3.Y_d`+SI'c:6,37^Vrke31,$r20LQp5g=
%*ka]Ih6_rTj?kHskk7-KOcSl*(h@eP[ksCsXK2o*jBD47V4*Gd>i+pnfDjmO5O(a'rRoS2s!?"\pVQXDq_Tp/OkglC8]7#61\@7&
%I;fl\&nA=9Wj9mEqfMJF_2-)7p.DMu,dRA+N18[;nOIL@%RtGspG7/&W(`[1SWCccY2VE[FhFkFF'/=eE4iq#06i!2F0BUlI;iL3
%O*q>Oq8OjX9Iqhq?k/I=33b3s`"Kg5?I"fX4A-Q2i+JI6EK'$N^=>Y-(rVZY!hEd5^'(8K@^LGI;V-!;^#N8N?fLPMrVJZEYm2Y"
%M9C9r'cp\b`timcDs2F4P47c!L\-augsE(qW25i#gb?F5ZtH\SIB6rA'/Gr'XF+ef*P5KWi#2DRGQ^&ubdoni!d84(c*]*S</?Pn
%Vi;A@Hkru(2!oU:l]@_CLElXTAMqWHC#`6J/fhPdDN6V%[`C#E%Hcep+m9aZ(%Ouk\/8^)/'h\sO]%ATO-Oh"P$u":V%oO*E=I1!
%6T=_ho#k(pJL&>i"UGW12Mf;tLdO)q>*VGjErlFi]&Vsqcr1WR745AJ<up'JM!Xop0n&[f5C!#H\gmU&!;kCea)]F,!'V-<%Jn_h
%</]+MQ%ePan;H?l<i'qa:AQr&NWfo%KIK5s)0ECm6ln":[LJ5BlQB6iX$j%:0Srnqh1D=^`2jt,=+_!Vef&**j-X:\X@=ludj<Te
%Ll4c@*(T$5j7VFV3SL>chG7--3is*S;F@^&j-X<h,X`?CO*@'4[rFEUbp!COq]1ogIF_K8&*X(,GT<!aLM]094j'3j%kb/4TdrIJ
%U!Q4?01]._hi]]&H4>7k#<dl`f/hdXbT#Yd')Z/bL,,+3Om3Z]^^234dP?J,d^("sm?+K:03*_hdcN%A/pD!Ap/,qA6#9i0b7K9+
%#Y9U#BfVRP&bDHlVumtZa!_I!bLk\<XP:Q4P40JqR,$D+BLtVZYn,^U.ZWOE'Lpd4Z2[pZ3sYV/(-_c4k@X5#b^2;Z?)(V>Y>+G*
%+_VsOR1\?Kb]8LV<(/1c.`AUahLWQRju^)5?IUYNQB55))*a$N6s;W-SlP%F)e\jE^=7O%?j.\4\8doYKcsNN0)F"k2uQ%Nn/qgY
%BQG6LabTWgYljRDBmT:(4aKZ>QSU0C\HktQNQ:qMon>$8rC=?Mo4UY;EOl%\B1'X9.GVrJc8+5XIJA6\o8n.oQ62SU01r^n+XhR+
%A*cq%:!J:r0kF%a/6@6RQR7n8]V!s0+dQ`&bX\k]iR*[>eHk.a3D@Ok23C2M.<Re#6VC%oR=\];5o]u!,+XaBZ3*ZC@s1$]feEXM
%l(#@@h%]j<p>0Y!ZfK1;]/QC,kSjWp75aX`J?HhF/I64rK99un<s#9jbn0.a%>mMthVPdQOo)2Z^O!gqZ\J]U!slm0rTCY6*[9XK
%@YL]AZSV,M#=e\aDW9i:r'SOifD9FB%punADu`s-1=!=C5*`L"\=om"J*\Y0oA98Z%J0C<oTsHS^ouPKrci/P:*1['dG&L6CgR-3
%#YTQfr*DA[S;X`>F3S-WYO`fY@rWY]W9;rGdgpXOR.c2$E,Nh]Qf&bLppBYQq\$)odu''+1^dAkpD1J5o3@mMaOcKVlin\:Ym9(O
%`NhNA`T6#c22df#/<u-#o^L8<PP6aI?e,X%?29Xcq\!'J"&(_Z9BofdMsDp;E*IO():%=R*jL>P$U!u6S]6ZrX-9_U,>1mo#$&_h
%n?GE:+@7-AQu<^ooBID:H>o'?%TDo&$E4kf1EhiC;N-\O'd%B/1q+1i#TV$XVt@S<iS6-9&$N?Misa5n)>6#pZ';rCXrN=*q^W6?
%nOLO$nN&.i#(CoW5fq%cqCf]&3BJcf7ODdTA08.!48?sIYo41(YNll!@_b^5p%YdHJj>*Mo>beffJ@;uM5+p7KIs>%Z0o*6p3TTZ
%(h!o/2L)"L%6P$LBpF-pr>bWQpu:r@Y!Oc%gmiAbeSX7nX2PMbWWE49lGubh4*41hMD[WM.Psh+p!YG$+ZS')a4E0QB_7$c/p>8,
%9%X`:'`beoh2BAT@9W>6irY8'jDFk#,m&3J]?QWIm%1[KT@<JIB?J8U.a5B/l^N4qbru'&"7asaq;NiMlU\a8G^neM-ghpU)S*fl
%T0[;tf:i05fn&pV#ZuSR`]o.O9pPaujm]+9Utm>40M+aX6)n@`UelS[hZIe')K/q8Dd5_\-8X_!mkZP,E0?9W1=%P4R89Zqe>UrT
%l0,2T,G]di'0eY)++C6=HoCF%PPctM"T4=/;09J1(Ai/$UjrBeL0";\ra3)%(Y;_c6ccTopLc+ps7JKYqA[b02":P:';+"YQMpHD
%K6H9iD`DfV\p[rT&Wa,Cm%X4DI[\?$p5c]3QTVpi]YQQ*`j^?>T6NScS`D>dZPAM.a8L,:l$W5nrqhb@cb9<5ZY^Ib!HXhaFJnh;
%20`CP:5Uq963+PuaJd6'_im-FirI-F12G/dSnhfXemV,0+!S5A'OBjt,HGA"A(N-uE]er%guCVRQWG[jQud*^*l/-;p/T[]JE2p?
%ph?X->39(49Aag?`=/RIb?9t#o+L5'XZa\Fj$ia`d"#b9cVGUr+iX'D!P^TiSh!mOE(M]ha"4lncV3Mu^]45Vro#Nh`eCNUbOBp]
%+4O#GF@a?P,j`,2#*Js:2?9BOb)sJ!XtnmB!suq#kUST1XeX[`n1=b)ff'Poc$\L_6.c#hSh+/5(hA_3(uhY/(3&]sF+EF0CcmYQ
%SSZ!#KM6622Y("OmVM'k^=r/&bp:Cq`7OhI6HV:0AUV:e_k-4=jEWXMF&[%:*gXFc8nY6X4&FmiDFT+u]3c?7q2Wd&l)3?"\m$#m
%e5%&c,qc;Gj6ED!E.gU2&8m.A)dsfZF"?JIC]pOHG:\!a3TbJ$``9_u2Sp'.8Zg9tY[c<qOQTkX,:L<_BUgi/g^$F%r55EC583gS
%n7<iWaNm<m.<;%@LOK3.&^J9G43Z*B(OJ>tqdA93ETc[ap&UnFF8+;l4/.UU!'f0B0C/0!)4rM/:V.-d3g9Y-)."%C#9fl<+ljs>
%?Kk#A)T%`VDs@>l`0%$el4OKRY%\0^-nuReJapA:K;4/A;;=NH^9`],-]ctcIK9L,jd+Wn,p;Od@m:/_$R(ap^&\g&O)U%Y!L&PV
%3VnOoJ:K;<M<Hn]i%H;.qtXlQ#^P=OMB([@*jTi$XLYS.b4(ES.UcMJT0U=TrJPqj\BX\9/X!F7nSkF1cdi)dj$keE<tH4,Vo1]O
%]T%s`TZAe3mG#hRk&S/G[f?8s_rnZ2*h-Jo*R-G0*:0b?6)NYg7FCG.N1ihhm'2MY0CE>amlN8C\`1Cu]XSeI:lY2cg,#bH<,:A)
%dRq<ml<<f"cJ;];oZbk_k0a3MH-lRZMjsdWl8rjLI1O_.m8P=Qk?na,`B!Z/i?&0%YmSa"aJX$s#YI\0kgfCgK8&X1\\Y%T"2`Hm
%"ce\frPpl%$=\s9F#<&3Ld"\/EB7'E%.U%corl&iml8lmICZ]Qm?9b`L!ODk-TC=Ohf(1lHhi?QI(7kMfpn08cZ0%1C.o[-ocLB<
%Pa22c#bqSJi0/`lpldcbk;4U4G'qWr,i-Nt1,8?6MdA_:-![[4*g<nfBK-T&[SG-2j8QEhrd%+ajPOnUSB&,M5.(d$3-_b8iU?dN
%`M0!V+h7I.9;MbO]hqE&319?frPbImW3!=$>D2j67`GX/C6D`(&r@Dm7OL@]@Yi[WIm&K;Ni*mj8:b?bJ#tE+1MibHmk8%l^0g>?
%LlXa[r7s%*L)Q!MpPZdR"j4UX5C\SpO^EP.\%ht)YC?*/hu3Y0XRFGsKee3K9:.Wm[RVA!(Co&(r9a<$=RkRoa8^T)s$K<Q(pa.2
%N',$35&bS65PkVN]m]L%r!T[HYPuN@2#]VliOcl)S'O"1ImYC%?Sf8nNf&UO1hHdR)91)AR";%08qK3Q%&MDLA'a-V)#$8mlaQRq
%`ZG&d^UX`ue/op-Jbr?!W:OS\kU)h1M&=aIXWMf&jGU&7QquU\R.Jq@VDinJF"r.Dp?5$E2d]a?jBADOTDm+(J+K_UqVddUrO!16
%q+lM=lX0Y\nUCpA[?%]Jd7K;-Dg=2Z8FI)Z/kgO'j-[+Q_0koRB>"+]-S.J(aHD4%SB,cXSh9s?(WQIm5&.p0U=5"p@8#,mKrE!b
%nM2nekN+)?h0f$9Pk3%sEaKi4?%VEjs+TumL6"qGJG#[ck.0Kt\]ujtYM^^>-28\!Cqbjrf;9PJfk!=M=P9.@je%V61OKmuXp%Dc
%US@qorp[SEqW)5Ija4Xf8fqoVDVlG%g@)d1r6Ok,^_[A*?&g6\fq3qAf`3)O>oF8.pXSjhrdsf+070c^STd7AQKba'Iu^Y_;.P>h
%2q4YbFnOTRWW>l=fZ0`.mA4LW=@,_dnuTjs(2K\cWpWudcc?&'5,q@#BCM\:;e^Z5k2XW-cGQ!"fDO4qaB.tQO!4DE4U0EgpKnd;
%BDAFAGV3<C*[;<6a:J#V=>5Su3QFA!]KEu8H?m"%0>!13'"\Q4qsV9Rrh'(_!8XOd!fQ!,"ZSWW[a,]AO(!RNRi^@R9*$ju!T.G+
%7@inQZ7a[sOgSoIhG^s,O3i1d$2g6rrjWA+ZS$N5p`KD(r)500H%U2*eBr+G!$_=!^.V66MsU0Yna78i*oFZO\*aYtM7>cR*,C9+
%ZgiO"O.oX=VD"h</_27nR77F;p.sNu0Ad%oilf*j<m#%qgT%aVWVheUkPq=]UuH8ja/J>=HOiE,Pc_#kW@jh>A5ANC/QTbk>/%/r
%*<'SQ-IfJs+Gfl3XXH'fbDbk9X9+m^]R6/l:L6-D/8Ldb'6./fm(L`cs-`R%ejHmoMEF)ZAYGiRn'^rZ4Wc3"8"$QM06cli=[!J5
%NrChN]lPM;hUXQ:3[$'gZFh2T>%m2!E\%kr*!ZN6B1pg`?M@RfgO!blk<C'gkLb.Igq:'#eoYpPYG,+=X,PisoBJ^fo@81_p"<d:
%K#$uV2U(j^]MD#sH1i8:%F1FcMHdrL3j6C+3o>Y*`cF+A5,#lV?F8HR7%]2KYdWG=@JE(]2^MYhoZC%10NRh,3P2+-jUM$*#[<Ab
%.X)i8<F3.SoQ4YQ#'=Fq,(4c#h^]2UG6$a18:?94q>'5=2?Dp;>?%=Bpb(Tq`<FBB'sBfmIHo%VT:b`.O*q"ka:V:Jo)0BEs0I1h
%c!MZ*m9&5l/FQU"BCI\R=MrJ.O7CWhhgb2Z19tK(f_V5!s7<<+M[Hi5`eY0M9oC!g"_qrs4,W.HH@GR#S$`C=%L*'aqC'_^dJ,rD
%9'(*e4K]W?Z^]je[G6.tFLMpl--?spk?jIo)R7PiA,?#r5Jp!b)Ebp\deTIHN[ipEe6i$!n!>8>gjo5`rbSm!'S`A-iN%AbqTnY"
%%/.)q@,K.QU?VUp)t]FAGi3(1S^4e8M/!"0n3=f<!<B"Ii"!@BNWMm'1InO[lf3,C_1')D5<E]U=OeIdO3`U]o7%E_ID$jQUE(,1
%1RWu<GZ='b/?g9V1e0\-CbQVfBnJk0lLWF'gg?:1M/4uSnh7`LSR2_(o,=3shM\eski+]HapWF=DpSH`5OtL=EkI[i..)Mp?0FJs
%A/(?lV<h/NDC-?JrInb6nsBBoq1M4S<S(m0pC)R4G>gX(Mt__TpqQTdnR#qmFo7_5q=>ZG2o@dcYQqq5OSE:Xqos*+5@lir703?>
%R_I3II0s*'lVFkDlMM%5=TDsNaoD#/\kipl?QR7(s!bkci^\`QRCSBus$p,]-OJXXd7O,-/,.#hq=[s?HWPT/21,AneHAM*c<e]R
%C:V*<g#F#[0f-W&5R`/5r)oR6<k%8f=O$lgiICAB_nSsnlgXqq@f@(ud2*Io5\D&EjI:2(**uF_+<rmbR"]q,-h'(E57i<bIdQ%T
%qH(H0_]O@+qqpgkpVUq+s8(Wds5ad+q<d)!(&mr7s+>gt^A%6ks8)co^VB[E+9$[B)#hk]Z%)bs?iTt@J+I%Hn9Y3@?Cj9_nVc<'
%mY?jYq^om]s39I$k#Z/\r$NhCs0j)!GM`7eJ+n`Us6`+DOoB?l_S?$_d/kj*l7DM-5lHJA=n\[S3SHS-7<>X&DsJ+Wm6',B''@Xh
%<!8Bs/5O@97)>eb;9giUblXXHY\K3(V#fp.KGeG5_]f>7<$S(,2Vj.F!M//lW\_Zq6jcU!_i3s`>0S[JQkaUe!Jd/ch&lj]D&YT]
%bf!0u4Qp8>V9so)Ag2r`rG@4]**F?ZF@$.:eia\N^B'Bam@+#<'c#2;_?uOn3tNK]RhCWj)dim`Y!pDbbE,Af0&7ERgbkUAeh$.7
%C`f#q@?]eXB*(qr]>$'pf_-7(InUu4&UFn<bQdQQQ!bPE6WJRF"Fdcd:_78qD\tacQ!bsh.V'UYc#[Rb=FH0BVu_]_i[bjQH)g3$
%"q%+U%I^s(k'[6?6\BRD0#A8-O8lgp_ZCbYnQh.$5TdlfMmTjn,YXc^g#L!"`m$?o-#H;X8bEAFgDhK'=aX?hThO;Y]1(!(!-Ds2
%fXcXj]Lr;'d0,9jm+$$^p[Ji2_LF\sHfg`t(W3**7s8.q(-GHHRi$C98H?:J]e:+`+5mdo'P8=i/XE6@?PW^CqhVik^EDiG&dbm&
%#]bgTTTCnOmD!!`j"tWuk^3@8aZp5eD3CM$qa2V4kPl\9<1hE'^a_R"N-O3)Ab^V!aVnTG,)[,@JC*k9cD&iSM&[?JRl>Fl8j*tW
%@l;&a'.3i.VqpAJ>kc#6At%4B"$#q;M]#!*d3]qZ`AtdeXJ\QJ0@[MP&k[hUQj07u"</Y;`J0+9Ul<Bk"GgNt'=kk@:*o!*#iY2C
%T0V3%i@T`A3?_k\U6#rjO3kTU:c2\XM96he)X+m#IG=QoDhSW;.6i%$B.odW=\B?0CK1De5hU5ODQ]BU<BS@P+6Rh&8N8r#"WBX.
%^)"DufjL<8YVmu+MKbGlY?8&pmf6sFU,[ES0!!PpD5PR*2H\M4,P=L;MCBGAarfr[HH+921F-5e$>\<tn&kJ>O-%$`OONohE5%k!
%g+;kNh&dY/^iD0WPcrWskJYAp)*<2T?X`I?,t.DD@:fFC-'>*#:BI,FZEm]&Klo%%%ZrUii7oJYY@\776cd;Y<;XaB_G4O&''nO4
%ojK.o+2XJ!,)78RM'+eKNo7V6OuuJRjtSD4`<CrXT'p0G<hFj(Q$guT^sI%eQ'e-E6;n4s;!l583nRFk]ALf,P3/uR+bTZZhmm+V
%pQ='n<]mIs#NIr.pbE_k>iQ]emG'!eL-(+._/EG^HCO<&#mm#rn7@g!%[D3HnjJt#rW29m+h[!AUTeKKKN)!#'_ghI5=`:fS\Vgo
%pKgTT1d@kVdnoI5B,L]5)4:&fio)prP7B"85K9q7=J.>0h56s1qO+@[jJ6#>lmqW!*@4[+nRn9-&mWc(%K#SpiYLVtN">qP,#6r4
%moX2,\i&_M2PYnkTo+JQnm&W#r4Kln:*9S?\MXEs7j])KZB)ki[aB>P[h<9`C&U'sF6)Stj7p=2q\e;Ob6Nk>n"nNJbsBo<2:,Jf
%`T;/a$CCZ!;!WF6_>ALD0:-ojnh\AW7pWFn!pEC(KC61kU\.STb*uCAOkaB58*\l4bJV2J)]EmU:"Dtb74q2X(QC.,2^rGa4SI?d
%c%Nc)i<JJ?%2$P=AKOUm\^Crg11t0H"DdmFc]O"KHu;euM?eZ<(r"e'$pp>&;ed@bY^P/oCGa$Xl_*^G7R=%>9Y4S/#^2OeKX8D5
%b*WrF=mHB#6>FgPD3J$V@JZfg@%h_rLb=S)CQXkW//XDu2/;Xf7(XK$/^5ocD%rl)1VQ.n!:#J;bIeZsi=RsZJV7F(Sl.e.1R/g-
%Kg-U/2NARa:Mg0/GqY+p"j0&!pp!UT^d>0`Zl-UcrL3h)6?q!p:=EP<U50d$Nl4QKT'/S(b#X`gn8DFO8,%[%7XIZS4@thT0gi`n
%lIV@GDA>@dq)d7lTJNTVgVY1U*R.M-7F/*U#b-#S]tBrkbC^_+Y?N^`%KP?VE#rZ6rjA3UGg-lB*!DsSqe62;ad)7.pq=0M1otFN
%Y>a,U]Y[j9gs576e^FMB-#;IDel9DZA$_K$oN0PbOlT_c<cER"0.!#nf/3;PX'A<I?nAj\("X.FNp*1B*:u5C[n*.h.[d.-n@KW6
%2q8!?$gDt.QrVL5[at)a7R.k^dQ'nZ`kN>Z^&f01iqGZ\pJkPl`@/sFHj>D9gir]1Wk\N`U%*X/fUmmZO9?bOd?IZr^164STeG-*
%R?8:=S->CD7=$e`1=,.#[:!&<35k6e9e&Q#P34*m;)s+f@LWY:XL)tedX&2S,,iAj!c24a.L$`.q.nVVk2/<o-4d7oXThHcGHK<]
%``t9FNZ@AbX^+OAg2[fZdlV<G`[\$Z`)s)Ad%6p`PO]dPS4@RC"46QF,[u/ARndQ*G_'"lYL>?RNWZ[o2m<4;@&XSs(baAM/CUBM
%]O$F1-YoZ&h&O&,91/?J()+9KqQ`flok\%2o5VK+,I>$=@H0pHJ2Wn`!s#Eu/DYN',(Q6N(2sLD\khgpVG+UUM3mWq'W%lBl5O,$
%iC#jS%,S2WZ"An9e%Lh)'_@?r)uY?Z2*$`gI"Q/:-AnEq6DJ32.R>G>:-1+79I"<mWA,'dUcaW*;7%Q<if*NPI!(N/ilHlg`K-5a
%KhH[pfbW_G%Q`l_!mq10Q*cZmKWSc:,mZIJl=d@`5`hTk=n)tjn\6>A+:;C`3b`#H^[7!e_A8c_hj1gBU",4'2'ctKF=5a;os&JK
%U&Z+:7]m5*+g;`@HY(UE@*8_:nj,g!PgfT84(C(qouGPunI\-SK_HLR^DfBO#j)J3``NBYYqq5s=[%V$9*Sme'Vi1IQKl%9!u$'Y
%S9LFGG7GA$iusjs'+TpjTXnWrc)JYc3.X_T77-C%7f[U/1Gh3OqAt%Y\Z-]_*:Fn,VP>a+`RmE:mRA)Spc<H2^#Mf*hTpdq]]5MW
%U'!)ZB6W58;1V*q9F`eJ9/ngW#GGOja^PCb0%^Js2B?fb$+9<a`:<$Os"G(qZ<p96/+[$-_ub4;m`%)FZ\DdQ@;.'5]fR+T-[Fbi
%5p1)HGu"%Z/k>Q[Im:o/$s?c`XZY*4fb6JA4hYM4?42pKJ`_`LPJm(66#X!:#;6"_)f'tCNFj>PM"b<l[Dj%-pretGbBDYX_^='m
%>[fa&PCtP7`s=bMe9SM99>"L7$K9D(`POsJ$Gr?KIGTM`?BXKP[kl8l$*W2t*7hD9".WYo%0_#O`H-<B:/<S.Y9Xgj)XG9_]b?q"
%+$skVA?<$6EO,M&=EZI6;+Rjq]?<`7/=Ub*mTh^`K-8fmr<fTL1c/7tcVE@b-_RIT`LE;nF`.4o@?7o/CtemPIY=;8?I9K1a*&#1
%ME3ktlGYlP8(Mt1/_q)4NUiISf:LLmdjC:&R'g.2BTk`mlt`Fb;(,BJm4`Z4#$a&nQ4.J!CGkpi#R.%$"sW?deg*]AE0Od9Ts/99
%BMM&5OZ5Tb'9CKSeC4r*.X^MqZD2@TXu7m(m=V7lG9t=/Oeq:+55Nkd:H#OT^6+:/)7V'jNSrhA85SZ;PK]>\PeQ."4$jkoK/6(O
%I?d#)pO&m(Zp!<LRK=e:H8f5hP#OObbkn9UW_;:oYP+7.HDFYT0*=!e.Wlsta]s%lK.!&?$"$n/*5A9&G>+?@`BGMmd*7Yu2?-@Y
%=\+H(R:(k.DKbIlAAp\`U#Ubi!t8`aY]!%G%kjM<gkn`@+F\'\/U!nte/#S!>dplW$[1TW\ON^sLhutf=QNh,?>u]"Hu-nJ:!-i\
%h\W6uHa9:XE6@;oDLQKM,ps,k\34\Li(hL6J$udbm_TE!^C'N.EBdnC8,?W`%i*U3em@>QK3>;Y7Cug/pbZ!K@*?::6D8eK3BJjk
%X<n46=&\K*PS.:j]chb_&lK']LK9,56%95F;s<RKe0><<Yru7b/0j/;n7?\_fHR->%fi#aK4JUl1RA^q;CObl0l;VkZ?do!KI''i
%\NkW+kU[?IG.`hnZ&_oGaF"e1RZ.fQIWQUs"IC7a]5X5'e^)G0o=hj9?fr>+^2sTIHu9aUT%Hk5_KC:GP/QVo@3hIAEq)?mI^rDe
%.j`%<$nBFN=QSF^5ehK/\q6SMBot!M?%]P_g5^/k0O9FZ#D_o.YatT=i<P^t,KCQSgkB]S@d6"JZ&W)S<LlM)$)/*N8kt\<WLHR`
%n0H\^@RGlP)fEToTP*0uYn["(Gmtq1gqcnT4eJJmbrM3+L.M8+);NA-Gk?4(Qh(hkbJ>(t/&klgE`L31-GnZJU&[s=%Jl*?3"M;G
%i5W@n#Q9+GD;cm*\;6r(dk(tUqP!W*:mHAD*VhPtPU%o4ek&r;XFL$>_^?S)[Z^8SVO13id#_cq_]2X1OQZE@SPW#H(c#:Dr@BKo
%]iQq=IuQ!:#ppaH]mI;>jD.X,H$/;pOHbb+:^!g=q*kEiO]HE[WrHTEWQ.*;,1>>1eU_`*;#UaT'$6S?PXgJe^jC8c=.kALm;Bl5
%>J>BVA#%H=>VPml[Dt+:LXFH9$a9aq>k2dTJ@45b0^5]f_$Q<E[\pf9E*Q*"A3/HSb4is5\4PUFVR=OCA+ShjPn+!tb8LsncAb;B
%AJO2\JBBEUKtlu5nPm<r/@2t43Q!4K^/,!I,kR;;&nh%;FM;mU+nGtdUuS05F_bVsqd.;K$dY=r0EY<P<"lfukN*^oeO12n+WTYQ
%OI:)UT<4K;%)H<(VCF"cQ:$[24;cA]Tt)?9N31=:"X#o7<?k8bY!]PTf&7N4R&_PqX3TE$^uXXuK"*c(iB1A;#<OFL(4s#i"V*aL
%`smh,1+VBJj@GX-Q'cE0BPECTI'DUuM0KBF`u45>,jt_'8NVZtYS>1$l:gr40Sq*:)3iDE-KhU>8`hZ!n'\@]Y#;O$U%BBC#"dgp
%*EVD-<<1BDj<U-b'f,=,@Udc/icM3VTY,'-U3?Sq!n4;P^Iu4k,?]i2n$]ufA#FPbS=8&jR#Q<=?*][d`V/fI8m4llf*upom4T&0
%mSFdD#f0;$HsU+BLYA!/P"rOu18O%5mPIt3:.bgIDe3'qWXD'!RDsK(\/3/F)[g+%fH`RoA4nnI^.(G=`r8XnOoU-V9)9A!=qP=4
%k`fRG6Ek6Y@6bQJ2+J27dl8*>jWqichG\pEqr!j/Tr]9A6gE[B.$3b0?oT<&"7@OQckN4>:fmRRSrn4"VpX7<^Il-?M5hp`S?bA1
%+`kXIf,DsT3"c3-8SuC"M+ai=:6]LpJpJ:*m`U$mBI):$a:bID`ur<2gZ.0_R%M@"W4+fXl<Z&]Ae\*n"[%]%OteAh'rnI')0?(b
%M,E`%:c%R6jr7RB2.P[Q&$0)'`N^QO?b*gc761mj_A2h'<n%*Lna87t$P%fAr#Uu7cU*h]i_Emh\gH#X&bZq&Ys"9$&Id49JRl,P
%*i:iKE')U)(QQ.5b2La!\nbY;%2g#!*l+SIX,K&3q;<M!US`R'9ij22m7D7tR8)IrN/'q@;)`/H78Col;'P(egO0=:iiS"6.$u)C
%I]h!JH-$DL^qWLb5A/1LK_BC(HR5)t$M$K]"QZn7.Rmsb>79S,r0Fp"&\YcMgFM>?&%R+cVkQ`b.k''ba#u`oMqH<e8!#AmLG$;n
%dXDbIlT2FKdE6piO^J,p+CBS:AhjbcCD2G$[_+jU.1c_PCVA>lnSa:`3pI-GcLJ[ih#\qm,^Fb6+-YY54d6:&?Amh^qSl&:FKT:L
%XeR>U2(:dI&`McI-H.=m<RD1ls25Fp(tN\-QA3ABf:3o#KJgrBnpE,\SW%%P$t%3T0d<3qT"W7hKnQNFW6Bu%`C9Q>3D-qqY5nVs
%d+!qk..--6Sg2aLgPGTJC^sl">Yt1S;=,lVAt7@'i23](]g(oo>FZ)RJEPXJ]nW2,04D#ng)AVumR[H/00>2C[RPbo'hp48PYA>Y
%7T;dQI2UG+i83\jjlVG;O'K*e_/T!9g&sRXLFo3k.]f%rBKSrUbSpmoZ4)&dO-GV6;m;;/R;-o#48HNjIeJ>PADHJXNX^0ZUB7Dh
%I>Mc+OFDi!I\C4)2jV"%g]/79M4b9@SX,9!BM(,I'e[]<4^0u]#P\KTjLSK-Wr3\[\cX_/)jJC#=okYADDZqK!8SDNNN.dRKYAQ?
%>#,qS1fk4h_!GhQi`dm(MLamsgf*jY`=;teritOC-3QM]C+01i,$K^5H%Y<]&QV5dPQr^nQ3TeAW>'HT=<6/!A8uHp=O\7TG'Y8S
%crUXp;4\Mg9e1BOf\*1]12+U"$W3_^iL$8P:AP;PjK1b$]Ne9`ag#si%jFFLB:V3_3#7BA_O-P+!?YrjD<RHYcEIKj/h9:OkPk?p
%fDFnUl$U$7:>EZkT.B>:`R$E2if!Vb_Gi96=*YJ9j2qYi.O:IkkYB?P!Y!W%h9JUJ^'-!t0(PU`fAM-_!n"9&P^&kd\$lK.\2I)A
%".MB6P>-jF"?+a%OG-pd'P^k`\Z6C`7u$1L3PY7Kq2Kp;G64nW^IEWkdW7!Ho-hC>N-QmXh,Q*$N`a6J3K<6,n,gtU0jrRLJtPbC
%$kB*deEI2(&6=Qh-\rQk\d6c31^9`%?!JaT'qJlb`60$K%5ZPO/WfgeO#RS`5+gXu[ja1mI^jRJTH0pb^U;l3']>5m+A_[0gOCha
%k_a[p^1/=E!DWs>/?M.k3=j]-A.V6\^k>N$A)$M;MmB`E%#NV*E,!lNQ\QMs)+QXH;^8]C!q&rm;t#;c$Kr,fR1V2D!#T/ae4fY]
%.H"24:E?./D'k_Bm=WF#ctm6U/]dQ4i4`ci1</J]<U>1?8=,#!fS/(b;7'eKR1NBWZ#H@F$?YTaZX.4J#Y<McD-7\!%l!"36?t:l
%UQUrX`<9kqk1Cn&Dui<lUpTj`O?`?9Lo@2uNQgK-<;>"0,m(UJp4>TH]RZlhLQsV[!4N<X%/7$r"a2>lX(+%u"_J>i@*J`5UN4hS
%J]oMDTs:3q,4?iimS]+tmio9ci$H@X@OYV:4&+`$I]bBF;R=`I*JCg:j,qD)nbs$&[i^WKX4pYJQj=Ik]c#]+g&q%1),$!#&\SqR
%0V&7A/Ausp,9!s;,h+:LTSU&sWdZmnDDBqGeWTWAL;`ctbpS<Ji^%NsBJ[V$+D@q33uS,#9m(^%XrIGNmo&#5\@/P@nW@,o"to;t
%RL\HfqlZ`k&Fqn@`%%`JI5V8t`56Pi$!MQ#RFnks/*jG#Z")NafmZ98#n=9[Nm(6EOT*+BM\utPE:d_UJ4jUT7.P"JW[=7kI1,*3
%"[2rZ.%CN=QWmRs`U8T\YmTj/PReG>aUS)E-u_EXBg`M4>5M:DD@^h3/1i$KO?O5t"e2bbL^u:NQn>WB11s-R7\4\>JZAbsaN>,%
%^0&['+U!3^4XWMP;O)DMn-Oo>..<)SKtTdqq'G,V1A_P**N+Y-ZscpRLe<c.3sA8Bp:^$KRtj;]X,-/c2*54E$!<2JTr[XjJU7HF
%QM0<MBjKAb\OQXTc4s\E!_]R#9?dW(ds\FiWkT4(<k`fq-YI^a>F6F/JZq;/hV?,bX>Q_l2?T\j9!?u6RDIM"m1&)!5OnTe'^$'p
%)<EMAIL>"7T/C*`7">u?R*C9F!%J0%qPloDm<PA-$@9<'>?qJq\3*C$Pq85*b^2k_TrMQ!h&Gg225+%"rCpc\G6`aH#2u8
%D%pUU)Yf&R0=B7j's.bY?&>DjAnAT&\%?A_AQBT.\kq=S8ee']I8u2q;,?4hrW&>b[ZaYWBFuj;Q"+*[M1e\($Ip)d#r#hh9=RQP
%(K/lgm:B[PE'%(roJ+4'+#:tldQ^6=@P+sC[Ddgh`0]kRqQh8tFG'E<eNnp*FV+\WLUUF>gB8\/IOh0)8PeW1c@GZ]YAGIOO;'"*
%5d*V(S=g#\<g4g"d(AV[R8:$l3Rlr^#gMOX>\`[:D!u.BOFeZmDHTl%EX>lA*EH2;L*gDF912gpr3@2nJq-e_a=`Qp,<@7k@uY4=
%pH)cDgSZfOWV%D(&D8Z;&8M]C1Qh$E@>9b>oKsW"b8GFlQ"NTc83P^tR)1C.SHWG!6s=.=,Q')pTfOT+C@$;("4F<oiuR-Q-<Rnf
%QoLF/Co47=0\AYA=CqR846_JKO>`EPN00I`%1IbCKW-AR;l\6](q.s[(IE#^o1%`I((]^2+\&X%<16^q<!%`m^5l@JK?K,<_2nb!
%/mgCUFu<jq@5K3pL7"/pkcW"&HnWf4b,PRFj16Eo&D$4L#]tA7o9a"B!D(uV+VrcOT7!fcm4hR5aeFe=?OQ1p;?1/6D`"P?'tKoP
%OfF,Ql3fSudmMX0X`D()U9/Gu%rIT2Ga03A'P/Fq&8MYt=3c(!2j_e+p"8M06OAI(',+F\%Y84$c,BHo6*a/UO`2;`B9qZJ*-Kh*
%5+p=`[[uhE#RN)Z,DY@tp4oQc,aMJX&l,_h*0ej1[nrQn/:n$<,=Vt;4lBe&L.Z!$7BI$("!/Tn=AM*%B#]imM.R.V"];P=UNK0t
%$1W7sSdEP+/$5q`M@JRDkh!E7[33Z['d*d!`^W4B2FRYRQa^Q]838Z6[r3TQI9qXO:2bQM!:%);c#\D<8D8FkDU7D/Z$$`M\DXJ"
%Rn\37Y?d.!!E@o:=KE8cVZMldmk9B39e4Ui^nRf*XgoL]#)#rRFm$WHDb,>efT0mAa)nk+<Bj5^*bS\F^$/.p?(-*4goubN6>Na^
%fGJF2`YV#8j[bM0p,PEd+ReK:[2FskMlW)gc4L-\9J*_#7s%4t!$a8p-*H]@=4SEeb7s4F'mY-8gRBZFJT(U-nO0`7m@9`ASMeXD
%_=/D&;jVUNA=t2Yj;YVUL,=hMlsVJ-\uH2]cn^1lOC+.F.@+:HO<S/A]#p@ZZng[T79:)D<Am''RaQj7)SmS`l0KVkA1Qfb>,Fp`
%CQQqq:.P=bc,rnT#$@4^%W#T3=Me*:((shJ@/RthJqVX$_qR)i-`gM\K;!H$)A*6De,&+aUp5`(U4aQh6#8?"6RGs$<T%s=729/c
%^mDitE)7e`=S9H"&?Wb@r)@Ql!)Ld#]pi`>Auq6c)>K6iF@LK,akT/u!(Njag7euW>Z,S'@6.W9&XZ*5JH=k0n$dd*5X3L]-Ymkt
%@E5i)41h>is3k)Q^q`$j>6`)RAUW=:Fht]d9bJtF7l[mq:7kus#h.&q%VH07Ojkof77DNIL!'s4ehts/(P%*Ge[FoU_,eWDmQj(\
%ZD92EPZ3`?#\FC`D,q%jjVB#2SW(aH*$*8<OcXCT&sharqaip2CQIuCH7a6Zl?m/SKhGQJO*Yf$k\Ih(o`:c:VCeJ9=@<G-1Ji0n
%%45o/NV$%)/`7cZXd\!3`N<9NT9knIK1t34_pC)=d"e8(``7q1C"W)m-_i5lKJW\/5K*&_LH8:!R%8DU0WS\$$oQI?0FDk/g=\J;
%<C^]WY.7$i&:\';>7Y;Te=FCDp%nc$`r1c3Qfen\BudaA'hgoAEGE$=JME&_<K;ra1=o=$Lg7g1.prsO/.#EM*Q>au)?<V`11FW#
%k]I79Xi)uZ.uA'bX<u8!9GZa:PC)1&6id3cWO*^)?-Pt\W<1XcUTt\L%Qt,o'^$G2.OE-.4Hj&H=It-1cKur0^^Tk6/ju5g;<ojI
%@LMGN_Ju(QHXR"hHU_h=CL(;teX`$t/\IKRDS'HL0#]R\?WYJcNRarZ'p1i;*@m"Qg"_U5@@.n.-u'6%Y])8!'^:1Idc#=QG\@@s
%VXqNcksaGXjho)!S#fsUj+k8Zj+.>oB34dQ[XmKl@CB?Ih2/?P3:j\d^9=kQmMi0Mk\b#t,5aU4;-u'[P'M4!HiQ)JdNZ+hEYTta
%M!T`CGWY3t8.'u[po,XRO$&Qqje-QU[uJi*?2YZ:^4C(C+Kb<\1!+c@[jmI$!DmJFb]r(i0k")pYgCT)SXOql)cK:<CqqXg39)-<
%"t]?TY*'/h/;b>4.5DYL0EcCS!H<H(KL.KRU)ubr/(cb7/?AsqKGU'f`t]3$1R/U^dG5e*]7:G*&lDb<$;;;W;5+9"5W+Iql_>Ii
%L/;C$!)[;1'p`[JT"nOl-!AUW,=`5K[Bb_J1G?a6%CV@\BeS^X>D479OTi?riHKr2JoASGa[]e?nWln%<DAHf63#>3-3mgF-k^$W
%2mI62#K/).CQD_pC$#D@FL<5&Z\k-uZr5^-U=2g.;Nn3j1QN/a*5=*(K=%J9"LR.%TI!>hF'l$(oa"]f/Xg<0Y_Vq-WHh!VApaaA
%4Lu?rS,[&2$dT?hB3fiphoSd?%6LW7!#[8LMnS!hnf0?VdH'/p"Z@Ga-]'lC]=_-]AJ8c*DQ%^P@s&D'#LEZ.h9'H'BU?#dJ4h`[
%e0*A2)6RYI@N\'FDPd,/f_iRuD8Xc3,BRq%8$I+Z&pp<[+Hp]D=[Pb/X<t6)S;nOrOOq2YmSN\L%9taj!m`pm!4=URI]S]6-:GL!
%EAX$5>XCc*RHY*Z/LEG\nCZT4-"$ksI7/@rF-(hsQOU$P!1\X+O#.G`8ik`@;sl-m%B.*Hioet7(,>qJoj033J;i8Yn)G?TOLSEq
%]RjNci#2ljWnJUq2]N)ZM!=@66MGE8ka'\qKC$M:hQC&lE'0E>,Cfm*QucC;R,4`'P9gF/:;C$MoZn?V&!^=39FHQ]huZ.+"@/G.
%2X?;uI"^ulA#!n);Ns-h[Yd56GCS9iaujN/'%^^A'3KRE;t:RGM>7VXS7V.b>ee3O2'9+s2%X6>Y;o/0%A6&aH`ZcNd9`PGDfQ8+
%HJ.!<L_,OYlkW#;8QU.;>OJOCP_"Ln?t$(b%HI=b2auC(-F=&'K>L7RdVT!48`W+CS#b_FM:\&2as)b1Ea2mOg=Cq(KGi9/H\t'T
%>1_:f:rUsueS3eLo4MXL'P6JKE;pqAQE><Ll_#+T&1nsi"hpa("OdN.mi.E4?XjPpHF2p@!%J/\gPKRu8E7S_i><2W#l-0p,2ptm
%C9_G8qZ[cnQ.IMDD--*7'm2+/!JQ'B-\nmEVchg+<Db/4+a\^l%(+J#5FE;p2p$0>@*8s,S;Ck:Gs9omB_8i+f4S:0*E5=4Q#UdL
%906h7iD(?d&k>WR$2i''\*"kED<Dn$"%!pV5pTN/FBg'+`ZMjlk1u9jNJ;PaCK?1//3GpUXr^]+8dDWWV`<OhNCu#Udq*6D+fSK\
%&=/,'_h'0g==0l'#XWs=nd*B82B#+fJt-d)Na-H.IoN]3gXOfG"=4HWiNbApk2\"rZUdD0p%$/iRkUQ3_mgka3EbTrA9kf^Tk]ID
%4S*o^\0XdckNRObUUj57oeN0#Z8&3ETD1GlXVuaY&%'d]K,m;0e,:hU[)b<=V@jiR$I-USg<giJZj1WJi6s3`"DiBs>SjU3b""^2
%T?9ZmXX0\h@f8UPSI'&]*m;STMM]eT4Z@VqahWOk4pE(qGt""`c:GPK2j./Q$VZ=BT)_@)I3W1i^QU/t?.H=(a[Nn+9sc<BdaX%\
%K9UM@*!%X(Pch7M%C\m_7\aX+E!<57,hDbtB]<pq=gW*IRmQ(d^8mAHX_CUD5O#pq`h4MVEL)PP,8rh25e[bL%cc/qHHYJi%@8$n
%flXHoRDiHciBpJS@64a%bM\CIGtf/TPci"(JDToL^Jbgn.Bn=87A\]GEA<sc7+A+:4+#dK*n.H)+Bs_n$3!;f_b8j3")Kl@^G$H=
%%N=D-iijL]gI+LZFtbdh&rM2B6;h"#O%@hKN<A60oLm7>S/u":f)@eop!ZA"[b9L:o=t:6^\IITs8EPDq+lM?K1XYoj8](I?iAsP
%rp'6mqbP!&iR`SXlOUc0n2J,]rNC_D=+C#5GCTYI5CCWLr[/-Db<Pp\G<c#n^\n'!K/ijX_s`*>GCTR82Kula$':+,,ld0$^FgW.
%76XB?j*slire#e[s7*j;+9+dM*VB/7r2Y<p7fN(!huDO$IfEc(L2cGX=QI5u7=HJa%A9b@@Ar(;U%feJdU\ti,6GiF\#B+F<sc+O
%5Tc\#&)\#N@oJ35[4"k`,#hu)<OLONpUK.F.?2$ENo.DO7+.cth@1)>fnd.j6k$I8^`:7oIn;E[J94c(M_e?";HV@E%81kNFd&tJ
%7?k:;O!l:[!=R9lI#!*UcDK4F?)u/P&e>S(%gtp]q7o`SGmN/&E<N#;'OaEqc(,5=VUP78C7&UZ6MCClF4#4h'c7;uTplDZ;B+Q8
%o'NsQ&0Okb(B_*5n3d9=#XJ[mO_j&5=h<<4$t:\r">h/t":CsliW_jLGSOPYXPD,WV2G-U&k//M[C_/i%Z9N]jU,-Eb)M'(e3&@D
%73OaI(sBRjD^WA8(_m).&0ki0"f@OC%e6pUl3PnGP(XZ+BajeQ_&bMB-mpq[)G@<LaDli4`RM(\+S_aTa0<bp;khS=KRJb`'PU[@
%"pii"S\pJOD\"c<aGB:Pm`&5M9MX!,(:UV^>%5lr,!94L)A2;i!=soI_J\,mB\Fb<Z!'E$ZtLb4j"P*Ed$=`j5OZCUR8aMVALi#d
%Gf"-3o-47s^*D;'+i2H]&Z4^qcGDL^^Zndj]D(h%2!?n(U;l?6UuE33folg'"%J/%^q-$_KJLdu3>2Xhh8a6%e4<n<Fm;B'3*A5s
%V@t#!Go2,EaC.=;LKSc&fM,*;VN\a2r'1U)+Ym6<GJkO;Xppl-(NG5)#mc1GLa9':qNEC%OtQ"Q&N41G&n+&A=@-L8#>4@b$*VT;
%P1_G9`<^Q]+'T3ZY(-g?jb/Rt!VfH9%`Ygi<X.17`?hW$AY])2E@NOC_3-OOb`fdTXB1KdY[3W7>N"J9[Hoi[A<*B*Sd5XdUme`K
%6q-9cJORfVMfKVFLdhAdZ\jhL\2U8N<l"UJ(6Itb5fqL2V1UcFI+bUddY0GUk+M$*j]S5N?HC6rE!FtT<jtcU]]kNP<0A"V$6_Ff
%YO=_@_aSSC8)Ap%0>KLkUJ%%Xj7RckEA$q\%1#np\1R+?X,Ad]7Q:RSr.#:/c%-i/1';?9Jl(D,BkQM'+FY)o9-HioWYQ>p('hYX
%@Mm)>al\e[E%M03Z`%O\XI7WA)uHERF^cK,q-dnL@=k!c2/%p0k$aU8&pDA*n)\V;jnk[o-Q-9V\[q'5](uB`nU/&N,6T!tHHm41
%66AOck-gd&HjGuj&/TFdQt/8k6XdP'cC9g3Z\bHhg'[410$ABs7.T3PgueuhYl"0#`](m)+10^*,bEhPafo+X!=_^s#2'1M6>791
%Q+@[Q6Lj:qFd603lntNG0_m&=9I!'`&+Ad7*<7\1:bO:*'fT*jAK$L"K]hQTFEJ=5_.:*ukYk1t\;^mtqE/ur-#,Bl8JNgXdd9fC
%dcW+MR:>e"%L+LT>&`5%K,YUn'u)$:=e](pM0$,7./$&TFGiBbbR,$jU1Q7,aEu!e]q]@c]6\TJMtC7\&@*5(2r.>l$p$]2H4/)W
%a_V!i*&8E*-,0Kumh-!1`OU27>>]bhd#u,;KhuPVFd#0L(C7qAq>qKjGmcf1`2j6Y&n=Cko=Z>ERhdNp(G!29>jjtkYk"W,%iT;*
%KjE!6A\:.bc5b"+K"o\Q@e'uTPt\U<k6bD3_tGKG#.nWMG6G9P+N"6NNM8SO_q>`n-&s7epH)KV`WZmg3E))mN+4%@@WMZNJtg[g
%=N^Wmjl_uQCI3)C&:cE0@mcuj":Ka]\aJ$)/;\k"!p6"9j<uqrfm9Shn`K-!K0ZsSnKrh^2s?8g+\a3_rajg3<9g(9^I*^"9Kb!m
%ad,=9TrS9T#bknaPOI::Aj]'eo[3g&_$W^o]MDI@@m6AArFQG/;';=97i9(S7+S)PbPoA6-Fp3-kTS_bFu6k9"0@qOE".ba*$s%U
%4LS6u@gV645hM:U?^TG[6e,HWFNs!k*$OR+Q(4`+X@i5)qV?A6WUOGtJ::da:`KS"mt9JA.dt1@2a&#F%,jVb9:QBk$k"CVNZ`ca
%coDWE/&9J&X7XTk]i\L3&J-WE,=[LS8J+;T8?lW('Id>4fdrZ8>dtjb0Y@lIWXkN&,YS3TZh":Sk12HCm;94C.@Nf7HB$DXKQ8M5
%'=V$ojleuo$qK:0$RY0AV>Y58ESMlHrDusO$6cI5\XV<eOU0msbegVbCTC10])9Zb+<C-[UMCjM7I*)JfZ2[pfa<)aN(>b4-PD!Q
%]?@9N/DiURMGWg?$7s"\D0C#S/Z`$.Oq2r@>JJHASAY7\DXYUAW4>W0US8j/V4bEC.Qb(JgX.hcbMZiT'JZTeJ5L1P>co?(mO?YN
%#O$?HfhXne0W\%aIDmm]]6%iX6"I7T4Si>"f;t_5I.n=\\IIR-KtGFbL[`m+MD59E,26OJ.7Tg_*bsWPWj.UBGii%P5rgL%s02Ws
%m?dHTj0l5q2.l@ffp3PFq453[qR=lZ+P1Gh9OOL?WGBt"ejqC>ohqqSYs2L<g+Wg?Qc$`gmh/Y;QI8C`mBkZR^p2,r>TKlE_3\sK
%e,&$u<6Y\9AYnKqH:i21U6sOOi.bJ8F@1_cfl1=JJ1N!hTn%uEX#FHeRGedSI;2<afaU4-^:5bJ:Ie8j(SSiHdG3",)19Ms\.OWO
%.M>N7OGg3*=,dVZ):As+_mnK2GSl#.OVp4(KH'@O)H![K6hY-Nn<)2$cp\,c^qFY4id_lf`oEPsb\o2<eVrdmAu:W>?,IMG1CPBu
%'+Y1K9WeuIr/@e[2"Kc,UNLgJn%GW&*`2'P9F,j'-hCn'Khi;ne0Z5lBZom879S67D`7ru*Sgh""1`baJST.:*T?3u)?-9a^s&t"
%.!Ma/(BcZ!j^a?PMbhpS;eSie6G?u$d$%II$FnO4Hq"[1M#NT7AU"G4T>""'h`![%T.Zc3cV]sHVC^OQfqIi0+#G?L2mVL8Rm;3k
%hP*4=KN$r@Wa(K<[NcmRWMf,YJB3k8J#,b>84duX9kLHU@Xp(l*%6R.LR#g:>+JsA.Q/E2Uq):]l8=52Xaj]L"PHm4I!',nNk/W)
%12W\^DS:(-O(Xqm7.Z&-<,lei78_-4a#`5H:o/\S\d&WH=eZmdJKZpHP^FHVm%S:H>XI'P];4hdDWh\pC6#Sn<S&AM72$T"7PL;6
%f2AV!"S5'B,%d`7!&;m[3Xc/dd)Nq%3Et/spfjK&==W1>EC2W&:_pf<+2BTXDNiTT]1"oDNCoS]@^cZ)OJ7%5rUZ8kfk_Uo*G\AR
%X2AaLGH(LM85nEHS.P1E*)?-8n._T?%?Bj127[U\q'J2Co2[;kb=Ne7BON7["gjVri%!+s)9Yi/0ADLj.UD=6CPHR:5)]+UJcn@d
%gQ,?P(WT48ouniXHiOeP.2Jk1d>55IW8BPDeh=QSj:9Q>NgT7PADuARo@tUd5)UkIlVeE-aU9"@Sh`.]3LG;P@\1'QKnP(HG;7-G
%]4&5b1!:69b?TY-%k-KjicF0-&o/tR6GA^ON6noPG`1<[ObDttKuf*]a7#L^62n6W9B9!i;aC/H*?%/8)Gqpbb:@cNb)hOQ68Hf3
%KI#J<jZUhc\8rcL.gCN8]$1i<O"*!0l((b5S)f?d$Q06G)YR(S2j%$8WFCe<iY@Uh>b7Z(&]U;^^#)@-1`Bm:%:Ms5+amq9J=@]`
%]."LCb(N0(><u=fln5XiH#MnlW?FOA?IkD[TkSW2OOI2miN9&3Lg5C&D.+1Q?(>A]>e]WFkD6.pM-<J.V<7?7j_n6][M\SGqgP$h
%X:b+q3uGTOjC_$SOp@AkoO-0HF:Q3;luab/[G4TE2)K/NDf^)Mj#1Ncq8U2Y'$mr[^<tmO(CCW_4)W&p8s/R:;)QOSRPL0acF_RE
%a&5>E0LSFB6F5YOk'$2&N1aY2AK65M:3F1)5`C'DeX^K\jtFE\K,QegB5D=Na'n5>_eUg+,%Y$no2rnASQFbaG(<X+n&=s62\7$u
%"CZg%P^[74Xc'U1/#Z"M\U`a>,Vj"q/AhU$8Bn`[S[E)H/i]SS%64:=DiROlKO>%+AKSH#\hBk7c'j=mMN3=:`Bi1M-rjbQ\Sqni
%:+,k9:@,$_>;Nbk2[uo?<k^#JV&];c!@*W&l7g5sUNBT-jg]k[(Q7QN,@.<:[M0'eDuBu\i&hJHFFR7pf6&_K7uELETFsks(do'Z
%]iY;QgJM$[qdE!njr=6FU;UI!XUDe1FaC2.@^7Qe<CaIAZA6X0@-h12b(,35Q$gor8t^*@>4ut;L>:hU0rsURNc#SC"^36X)D`2Q
%.kY7n4SV/+Y(>_o;[H/8K1ZOsU:U*"3Ec,c0IO\H+<u$Z0Kn)uL*j0ErA<W506#$#3n0rpp9UubBZj#@_\$Jofs?5[b&AY33J;%[
%<-o^TEE^PNUYs&%(Yb<d@+DJA$r7Z7H%[`C86`&iGSCcc/0R)T]k-S\h>W=SQc[fDbn3CpkU]Y#aA^oZK!:M+e,n8XCq#&b>JOU9
%nf3>c0W1M6`_D.CCiq#J`N\Zil*65:?R06ne[/^MZsM&;1UX&HC4#/OP?P&9;-%@$;D-oIg_YCnX?ko8iFZO=Xj#%VM6n7D6jiWO
%[aK-<%WZ5."f:nu<P'5]Hq-/MpD.4-Qg9Q@%mj6gp-UFBQ2+"caca/4M:*4WJl81ca=9;M5MtNn9+JrSdFR-KC2*G5$>7b`Xf_T)
%d7@1sf-+jrCM@&g$u8ONemqj)hQX'WK5M_R"[*H+F\r`u1`74u=rk(7HYtfZ$7pt6A8DM6Y>@V_fM6#gk&qQtW<AgX8]PXEdQU.-
%"0WO:1m:ihe#j0=kHSPACtY_]8:[k10TjYgMQ>Z)O#AQ5ZNb<Gh0-kU_^diI!A.*S=dhfkJ\7dJ6s-6\Yd1bnmT7>M5dd1[2^n-&
%#8l0/=VHYhYI\3'"j7"h+EuL<4RjFP8CXOF>Kep=Lj[o'5r>T+F<[%a$!U2%&#=^)d11*6.pQM*<OehMfA1eIg&f,2eCc#le8st=
%P9#a.D[8Zf(tH@*f,>2b#4j-^/Tk6sU%a1GYLfd$Q.aV1cRCt/d5R)a#niO0YGL_C4-Y`ZCOFtGc&D>s3RS1]9A$#p(e?5uVnb^\
%Y]h!YT.Vq-H&75O.n=i.,TG,XT<9n+q>!`1\1EubAPrs#0M;M5I`2j%!UaP<B!C%DP?nZg`M@YK2Xd@bL,HH2gga^`'m6F8K#NF:
%*\-/iSRc<sMf2End%kV[dU9oZ,=B:q"]6J;aO=^"$>g7'B=;2u@]5+L@@Kj3HJ`-KJ.*[Mm'J%9e!U?@]"hd5c%T7ig%-`_""jh<
%cqF$(`$ss)%GgFNSC1ii#.E^]2qa.9?#i_WAANF?V:QO'`mTTR>nUS'A:T!O<:I*u,-B[ZjLe`#V"`Tf8#Rj=6g_r'aDXJE;,W:f
%![^*(_[[:\d4?o^=K&)$>Cj4#,M^7KQe`@E6,`&Xm.0^08!$8/f\?#lC(Gr8+J5R>2HLOY@f),&QqA2MMlT$u[1\2JYjAtP\Zu=/
%=9l<dJPbFU=NNHR:D#EJRo!MGQUh7),,B7Aj15I^XD6B]4D/k"\k]Z<B63\$25D("S&<@UmV"X?oR8E\l]72j1/7sDZ#iZuXsJe:
%%U],,!eEns_=f=i)Rlj#;O:S*b?2FAbD:CR6niLs;-mom3#I3c\f;C$,#rr1h-!CpSc&N];QcrU+(i=0:#o9(k2`ILgl?3DHp0>7
%^l[<!LYFnV<JUu`;XH::7RoZ8B)S/c]s?SaRL>iiUOpeOh?GDYJ-?G"K48tt)0sC9W1OsX_fat%3[3O"h3>_S0>^23*&ciG7nA1T
%h-8eSbsEYq4p)'r+f]PZS$I<L.aL5WAIU9=RIh!INih&h)uHKQV,eG&S!Rq3TT7"oOcm<DBmmt)9(9+Er9,P'WcM@gV;u8FW2gI<
%(;cla%V"BUJs0s:7VcEL`ErGbOUO$X/)R;s]6C?9GXSLGQjb6):Yo0Xm0MV2QeZsGR"7`$3ps5"5sN,&=#S]u!lbeEmnT3$/ct+s
%e;8g#`'rPd[ePW?B4(856ou$%ac5D-4^(E8Gn'nd/S:.nS5pM8hX!keChPUP-DS9#!+VG]EE<b%Gm&fR;NNp9oPa4X0Rg<6]T?aR
%B:u,>XrPl+B;<KhpCZ>%Q=p)-`,CAQe@5LKIJL"%;Ri9.]7>'$!X]dn9GIFSRi1*]*iAm(\V]g86[pD<XEr:qHH-1L$J^muCi"9I
%f'74-+9cOVhoQI<%UUL;Jh/*,VJ:FCN3q2E>j`b!s8!nW-A*N(E<Oo<,*F/*MO9gC8OJ<WkC(;#o)3p_@IR-Z`ra*F7)rq8mT8MX
%s+Ec,GeLT6Z..=h;iV.P7E::tA^VgeN3B$d8pb`^[LCi2!h(IIB%]L\dSfg&j9k0N\<oo9*0<SH'IkuV7O-I818@MgOVOOn6n39c
%VFV[mXVS2ZK#0[C<+p>5%1jm<V:RYf%?[#IO_H<8:%fpL:m`N>C&0!7&IW_cTE<np%[Fp,ic`Xi'uN;>ha<4fiYOreb/ltsJujDm
%W9@oP_ubt@A1E8V-p!"uXZA/QXpVbW)cG9Y5YP@AOohlri,+mgEZB2$miK6hD-m;(38W3:jOC)P3t(\8koITa%I;HEkKp_FLql_i
%SD'2P&"K@(U[k>&<^>'Ki2rq+O[`i]cl.<F"^lp:hN[+j34X"Y&O*tf:f?"$3A;>j>:MO?UC6'#fE3tBdD^3-2pbG2k:34.":+F1
%B;X_>fSGD=9PR#0)A93cSCo,af,]*C8@+rnO#P/f<_!2]O@C6o@Pjr^^QA.C$,Sh=-lK&N?<A1U7@W%QBsdXm+[0Uk.N8ElF65QW
%#=?"5O*H>?LIl[$H(0>_5o`MCe[6<s3gnH$X14!L]u:um+O<K]5;rN4ZQpjEqC%7%+^$uk5`^A5[>%k&,7rb"caI<5#O+S.0!sG*
%G9b-HhZ1<B4)dk\NT\Xk/1]B08G:09bqGVa+[8Ue870'[M<Nlo:.e>Rb'$S.TGrkpkOSuaGI(.PMM<)2iFA(_;.:/@MkghjQZ[t!
%Ki!*bA]3_l<EQ;HKYSoH.\JD)89aO7N\``3j6$Pc`Sq5("/,f5@ZG2P10kZ*ls)Jj_ab+o#V#3($5RPuY7s\rE=.=_(qE-0.cGbl
%2)0_b;JGs2@AA)dR:?l*\-d$1&WglD=q`SLm3GYRq5ohF@,K@3&MG,lI/=(;U#Jr*H:Yg3LQd=Z8!n4?1Ds9H5H>nc4Du!R]*&[>
%PH/+_Gm?SLI?YG*K[AOr;,;I!b!OCn4/=*5dHpU/Y"mXm<>+m/L$FF#'Z0Rb[mMt8$2[/6jL_6s+>'K9QcQ#ZHJ4aBA\pC7-UVmh
%qfi?#[Bl]O9D@aog@9,-0"7.)__MF97hCe,jC`29+!t9bH;9smK[%e3I)ST#Lq8eB]VJfb;gN\+g5Z3&%M1fd>mhD9994f@%uTs?
%_(2SD2.1MeZ1)*:&a<Cn,<cZk,Q0#,^YZmR9$\g(:8p8%VoJVOFZ7#FjfZ(0E7t%u$O>HGh^g'Yo,PFb)5Yk4;V6o;qK=f*NG9=4
%Oe$`tJC(?bkbV+m3A/Fpi:\;k5`+8`(EGI2(:B+V64k(7RGDBoHJg,Wm8E5q'p=)r43iG-hb[;Uk,e1"^\YXnnCgP&4kmpc2"nr]
%SMiXH1eSFGEIZEO1Fck\4ebOb;Y01BRUQW.g/[Uol>l^$Nf.->+qiS(p9V6f[ZoP9;)EhEZqK)%Ekbjoa0Dt`.@IQr#!KS$V0</(
%j\up-o^gsoF]%l%ZO@:88%Uc7?D1\g"4F^N,>*6aG*3;0I5.u\g'E+Mc].fu5hKs-@!8BY$Jd:>nMrK0YQNFN]Eop[T8Fp6\ur5R
%TW4HhK"huV?j(u6:HPoEj<o<*>rDc0JsqOj9Oj.!!]7;F6cRf=bHMf'N;B#J9S'$N[nr5kNME1pfNU0R!%QflnBD^lXQC`"ToKEf
%3<k'_XP*PSVI8iiLAo*pjInu0qZ[3r77DTfbt)MRUQ+0B1sk%uFgV(E$,X0Nm9rP0dD%L)/mShMqM*jgH7)>g/UtdphD+J:I1F:@
%Q1,Ah@20>H7dBXYL;V<#rFU_]A+R2BIYp1jdjn)]'iY2,FKud:@*Hg@3^Q6K\aVCG!TgKOAT3*hl+1ZWMY(7&A(:4QfHHgDIbrQi
%ZI"8u4h"Is=H+I9*pikXD,Sk@Z_U8Y=lHgk+MQm>WuAZJ>1oZISsmf`i*Yt7iKq,I1S,#T#C:@$HJ"fe^MmfQ>Po8K>R@j?gAtM*
%mJRHT1X@>N*6E,g649RNJT]ES`W3A)mf7&;f13&=>_=d<b?9)k<)`gYW+nGE/lWS(U[Io*2]-'9c5b,E0#KO_$o,K20)JK$Km<_F
%Ua8YWl00M$&VFM=[.Y9j]_HVN0t&.S7^_`12Z`/,C=XKSd6[/[n4>UuV^iG\b#jZ&1fC*%9BT)[29N.7s!1X?#5YYJs,6k*&(:Ht
%^12Qg(B[:j0>'LJIr(q\r\i1DThdS+=0HFNq0PqW!FNAT\e,29F<#iXIPaY^@Mf-I``m)_<rclCIR%?iCR>mp]XUF2gHc^[l8Jb@
%B@MAT&_W0.7omM3=_DfT85p$Fm5tsRL\<XmO*h%%.M?-;oQ4`%*75VaKnjr`<g:n9)b$F5iS?$&JimWYM*NcqN7OQKC[dV=#Z=ZY
%om1%;6Od*EZ9ICIE:.!hM1D/l7nL_(U1qFH_rFfW'F:+Y^Rh4k;lk,t4ksD2F5gUCA'.ZOIc5<@[E!P-kHeoPCH/?Z)?Jf9":X5i
%om-j8i:t(5#fI@S^=XT/]J(iA-re)7#h9q#6BY?_%ki&[n&"p1oi.ZP.`2od(#6,?8+K(1I9jM6?9XBC?(PH"Di:6#s(sFP[Z&TT
%0>54U^ac,fS&IR"hM%(mPpgQ.c-V2#Eu9=sXG'bK&RF:ZPoAGW?/<]G1/9UYaju-MrE[Q.ML+T5q_u'u4=`ma,F59;ohi[U-\!o/
%P;_^9k@]Y2>E-o;fP]H'$uiAU37FQ(NhbsVoR>a\9bC#$-[OOt&$*1^,c/(]'E-Kl"'UXce,'uu^!!*U`'j["'uhgUTH7RA&r$.9
%==>h(T'sR2k"o5R*IP$3^8b/a>6*4.=W9%ae=[\QC,.kL=p4/oH94qMZj?1+"H%8No$lY-K2J%_.62-_Hkr'*jPC2Pai7LTZ*4;R
%ak9lIO$8&*eGpFdMiH.BEAXtQ,O8p)J:TXDX]G$e]d.6WF*&Y!mcLUPGB;VPa:Z*7nt@U:%qrUZZ\>Slm/s%Y@*%!*G3odT)?lpN
%bVJ4uC><ag=n,_(dC`lo/%D4e"u*V57:aZU]93^b@'`8Xh8CSW)l/f7\JG='-\J&b;ioj9<irnY:1&eP#+CD5Q271:3PkX!(5N+c
%(>3cl1GNo+Fdh'7ku+]0QlG8\U*jojX1M8?YH":&U/29W\<nf_%8W&#:$cdpn:2$r^4n7a&,V=DUd&4%76OL(<@!8mYR\$*OY8^g
%0l`OmQS;j*h\Wu\GYt536nP[XI?r5nqf;S%cCJ15R6b@HY%5*I8H<NSVL]3M!-b]aFJH0!@`JbtHb;kL<W(A!bE7Pb_Z@X?'KKGD
%44+,!;h1C:3Y`7D+V#3N8sK=\44M)hZAFKu(&7!Mi?EF!F>G*8Y]\/]C2-2bgi3\lQ36h%VnC!?'nrI)K:9lG4MY"bN\\@R2@]9p
%))s'ebH@W,8dW33EhImu9!E0]"Z2>IP31VS#u+-",nq+s.H9[>6U%E5>tN<J<9*(2<A\k-.nl8ZAU?mC,>uWP_ZXD^cRQYpX$Ik+
%fXX*HI%n]3\q%R\bLK"d%^dc<6op`3$o.Eg'*m5,P+j@3-;%Qalq(:\%UP=@L:H(K#-1K?7\`Ld-^i_G8:u3082#%s@ZmN<E'qin
%PUffk'QhVOpYjQM6_ebX[1KW`[RMD0ml.-4<QbCOe^Qf:#>,^;SnP,o66#rM2S;bcUWmYA7dr!o&k\](\'u--Q%diX6MF=.g47=F
%!'E*t,.q0aYe.J]P\;h'fp_67JOWp_?oM%YaUY35SXXg;^pC(22Z`r2!?=a+8L1<s7UER/+R5)I>O;(Ir6*UBD1fKI0VqWq-N&X\
%(UNeHHC[3$eM3K*o\@U%X'lZEp":#=IJge5RY#C+cZfaTn]pOq^B"uTU\ftWO"Qlqg_D5.*"%*XaDo'S_p:2!gH\qujmT_f-?)Io
%k6CW'pdW^Ibme7bo?epT+@aTB;c\s%?mM*0mJaOr0brleh&>]A>G?/N,VdMgKH;,$=a0sc)RUug"6FBd>bT!)o[;P'gMqI"W^Zbp
%fh-@-(ojgu*\XS/*,'9]*A+pDVNoTG_/\R8EQhO_N-&JP-*72ji/ptR,MZ=*YO$KmLM*;+*Kt6^\u8ABmn%8J8o,I?Za^7.BVWJ\
%LXgL&b%@%X[9iDiB(]hOnJ0!P!YqRj66]9,,<n.(C?``klTOQqX,.hXa3=B'jN;DEkYWV!Z$7S:Nc@-GZ+aUte+C%:\LM:7P'e?h
%/5`(o'aEV+?2TsL50P0A;5&U]G*Hk\&=nq9@$4uQl]#4&b./WtUlH4La")hID1l]8Al]js&Lr*Dd8$PoU4Yor>qP/'T;8M-VV/U6
%j]H-<r#uk'$gZ`pX,I)_8d9GmkbORNGpf5E2IKTk[RZiQAmf1_>7O?=^LO"#8S8\Zh3Ks,$`i$rSs:%a1.oV?=(nN=Zr8n[hP!,(
%f#K*j,^cWW%8@+%18uJ@^Elk8a(1sA]/s6T:WX*Ce380OPZ7cfUqd#l&DPGrB&2=a40T+BX1Lsp5r]2'E/WFUH2;7carLBeF1+^*
%6IUk-QoSC(d59$U(44sJqmqLd,#P=b*DA*1`=Y3:T<Qj0$Ukk`J)HZ23u;AtR[Ga1/c:VP(mX/W[VD!%5u`OA,+u/F3NQ`W4E9O[
%G1<<.bM4-(4@r>Fqh]SK&M7l7(?`<'Vj[Z\</3A`R[Di@OX<iA(3Lg]Pfl(Lqg7%W_L7c>6(2Z1ZW<!7R85\S>ds_oZA^(^AB<S3
%!G>F"hW\.@d8nELNgJ+P>C!5U8,2/Ps)ch3rJIm#:^V^28Yc<7Vt5mPl?4pNGl[nk+$qKFQE4/q,^[SaYR!bom++HK9d5R=e;^l:
%hQ$r9fbhq*//t9N_Sk01gkis<R<ajFASURBI]-r5#.SCoY8`^<nt2\NKkf>>&=X^`WXU+a`OU<'ao*A*;EY%HKH]q&%c6Rp=EjDY
%"%Bma%^RbKFA70"F!HC,^*8Y<(\n?IfoFEXdqrh+6E916d-k%;DlX(O"kP*^.Yo7^34ha/iK_iApmA.*3C4[3FU&?s;79O3^[?N_
%f-ngO#$EbhDEWS1S>04H&sift*j[r2X<=r^-\gV22TaFbIoMh!B3U@A!@ItXe9urqQB?@fRIIUpFE>$.bUmOo27KEp/5FDl'mkmm
%H^fkEk-_rsj^-0aX_TsC,MbnfWP<B*Ips[`:FL+Ec&.s]emi_Z(V/mJf&H.?>lYm-G.'\88p-h&].M5t<Le\JJ^#3WR4#:4nh=Ts
%-LN*<]#_)nM*W1Pm0!t@1PEeI4!\gOP38=!!"bG=E5E'&EuSRgEC4D[S6ki=+cK:E<X[@]Embl)'.ud+iZb?f9D,i=q9"]n61>DU
%<YtnBN'9e(`V[sOYqCHW*j=QB"&U^e%+XK@Ug3!;h1>8@B]VXn0=DY`7-olHURI&L^#eB$kGp)7Bkpcp5ioSr,<U:`o[KMhXgNLS
%"&Lt4b]@cSH<Zuh*]C>o<ps4^S"@m7jSH8MI_G$H\Ao4%X^$Ui"Z@b5GAtXR[Y##b'sE0dlQk7U:\&f.9\08.Z0<`9Rbrm*/Y_Bj
%CFtscLlu:pWI6VMpld'@KgIZ&,`o&;0_qNpUh9rZBq['5#JcldH8D+U'+l$IpI.W<^(leO10&SZf7l@hUO[trlWi?\RMQ(,:Ct9D
%Gmalm_I`3.8kp/7QaoF1>Eq_"#!G(Ki!6fmCa`7I.gUnkJ_*.s;M],YNkkY/OonPX6cdf88_hX'H%LHM:Codj:FpUn!Zq,o97/Xr
%ppZSM"iu/"*4;4`TTmQKIH&EfAZ*]?@oK4qr$9s[4`H=.RFnV3VQa"TnS?u*UiCCs&@OY#@h>Zl*"bj&=W-%.LrEO]=T5!PDX2>I
%)>%>t]gjr1c8g4M1Adn;c\_%S3+8/)COhV+$#aj'1IVP&+h0PCba4gg?B-J]-1C%-+o.50VW2Uo&E9O_8#il'(kZYR'N/<8aeG>4
%A7`Eg"#Ed[F]OCSq_kj9TeOb!'OT<N<&NUF,NKaRB=V.Z;SQ%"NY$W?35>$b!#P-$Im9g"[d,N!^7gV0DA1g]1kug">tG"l^<^d;
%f2TH;NQ7=.>8%TO[?k>sJ5Qga"CF;Y%Go)1jKpuM(qrG'LTK@21?a%s0OhMjg8JmEH[p1_kmgO-;7tWuf^<4P#%T6B,5M;W4"gX:
%^YN5URZ@4g$!J$5@>,a;-IShN<1A]171M0W/^Jr]M$![iD<5%a=nHpc/I>hlY+CuY!5qmFqFTM7h_DAK^#?8$R]3eb*(!f$='M]f
%Wc5(%!gU(MbJ(CP.3S6sY3)?cqj'<1WmN9MG]3pW,0FBhVUu/ITh8;"VeA5DO"fZL4$Up>WmtAu0K!@Cl$67i/!JcK)S9C+h*Irr
%En9]sLp#aL.UqV.%4k\+EaF'8!:Aa.+VDBL9@t-^T=/(E=("d]5Fah0L;*#2+Ab!AY7&rX&-8:-`hj^eo;OMBQO\\jO:Xe7JNU`I
%Wg.iOL>OhO]a&iV!ro_f*iKq*&NZBW?7X`RggUklT4K5[W@HZMj>!(RIj@X3*s3oG$b<mJ;4-A%/ZX`GB!nFQ@M4%f"[2FAk>ag0
%md(+Ves_3uj<kbsn"/\+;oHsRAOl1m`;E0'Xs6)J0D@Wcr!4[521G!29N:Iqcm#\Hn/XmVb!`MLA\X^Pk<$Nd'Kr1\d2-ejk$bD@
%jWG5j-)Eu%Di6gf\N>"f?UYE.;<Z&TF73?OU)*>hE(pk)Q/,rtg(q:_KqcY:,3L)CaMAE\R)^3iBW@@*4=HtF+<Sj)@lh8&,2k#.
%So:G6@2'.8ZcT_%gEbhcW-D5O.u$=>lk(ff7t1iTDRNmrRa<tolISI+JuYd:@B5=uePGiWLVV!*QpoWY*:HPIS4[8"56<8u4iWcn
%mpci,k/Cbe2Subg=++E*-+a5\81<j_>UUsULZI8\Q/nY>We+g[E$n?h2).=@h3VXM->%:sW#7q<2HcGpgX3#HBZ:t4B][Qs,`T1S
%-%mO@1Q>N+nkO@pd?a',3f^0fg.k;#+?2WuAA:(gJ6aK'/G,`VH[41R0X?afp?(bYGm):69-)8VbDa02&\Qdg4Z(s.;qLkhV3&ek
%meJ$Nb4#RN8gu$q]GTpS^j!T%?[>t*[kTSbAj&+^>[e5oP">-hZk*74cj#tRj)%JWn3j&tejV$r25r.30L4ba&Cb1s:/]%6*gBCY
%6m)e2a<`<'[#.s/X<NjV6PKH@<q"WT`a3SK1Am3?^gG*/^2\oQ.B@n"?og[DB&2Fmr+<<Z0+\^a)'&`Mcn(?'6u_5X^f__FeTDAR
%TTZ$Bd:uK,Z$dVf]M^T29@8!=+RMfY2l?71(r9r'$0M;D8N%$&oEdlL#0&-UC*WBFi'8G;'cZt#@cMgW].[3(Q*JF*b$1(9?\18(
%bf2Ejd83hs%cX+EVdFQCq\P`@G50A;?V8k6PgsVO\Jd;&TPR>(A[.;ZhOo]urZ1%(Jm4Ce1RnD!)_;;(#!8l-<]<gGf\JZ&!.N!H
%+Gl0DmZIktn-oN\g_"cCLfni3TjA5M&0dc/76<%4dm;',E0!hiUZTcumJ=0qj->NkKjgUpOG/9`0IskuH*kbFq%`2%GEL'neOm%M
%MT;#7n89I]nlQ7aK(h_!^e;=gj$HKn4gVG>K%%3k#mMcHGf0WR/Ji#WZ6?V`)(S5sYYiI%9nTlc9E"mp(C>n*GH7<B858J%Nim0s
%*>d,n=(!q`\boJf<+r)[DLXS*bR*CNRg30O\_^$tbL-lk/:pDAZ^:Ys*;W.I&BLV$+-7>Hi$`\WY8*";C/=jdE):T`dbq`=_^4pV
%Z"7s>].P9rBpTd2Hpd4ImCj.)4..%'9`[@?K\WqV.gREQdTaE)eHN!VkZ63f?(SH)5gQ^P<tod?qOJ4U/:1n#^mc;8!,D;;mg.1W
%$Tk1m^JYV5Crk`/M!j]0$_41ZDf8+47I#iJU@T!/HP+r9_7Q\KJ^:OU>f'9Y(FGYuVMXWleJ9qh0L9rsr*n,s2@r>hN-OsDOH1+(
%9g"JEqdP[$*4hA<&=$<oX<q0p:Ba-_W'Gh*,(\C:&q._+>$;8W1Jo?1)n9bZ,omZ^Fc?u$Y*l_#N:g*oDc7k+]C#?9E"q:X2@t=)
%e!065Y/@3)(h&Vj>OQ&;9F;0<qoeD/\+]S5jU->?o1&lsJ(iS?,t]kW)srMPE!KHQ?:!=?Jjq='gVSh"11)(sq!=1s*!e3M06*F#
%I^=rPND791#RV[)DMAM)Bf,"Z--)[^89k.L$Kh^=.>[Ff=%*TZYE_(U\Ug4;?s+M05]\KmHe:gH18mQ8In5gpq'kB4W1?Fm7MS8n
%m-&r#kH'bM<3`h^qZJQ7lAYM?>K\#Zmju7Rl/4A7Nd067o@i6%6st<&(dXCq'XiGa=a.[qj^gmF9SCCs282L;+uJFD?npP'lKjYB
%E?DnU0(8eMn>=%Y:l&JhMVmLh<bj9liAl]1He3C3H*fb&a4jksE)9UZh=(&Fk4n"pPA<r^Ta:&jaj(<YJ8J*#!S2Q^F&:">Dt,Kr
%&8J<IR\$Bs;8=).,EY6`'G5OYTea/@g[3_FdY0q/pn7@mR[1Pc'Kdui#'>NP;*\nNl)YJY>EmsrC4HU6,Ze/WBe6<"\_!983Hp!`
%mPN&?\C<[Z>umEhCu;pFY-Gm`:#U)F_%8+1LE`P3_ats\+s2_sj<geo.,Lom-#k$?$V%JOXk@;>>%0t@_rdg#CuF`\Qd_]n*2["l
%a`jr%H(*E?,TJ8IQNm&0#@^d\G@tr8+d*"`_HPs<*/8B+^_r2`)q#Rkki2c"7<?2O5g48cN4!+FeGW&d;rX$'WP`cMXB-fbJ>h9h
%l^1#)hKsnrQuYHb'^sbo+*I2#7R_;@onj[[[:_iC;-\/1ZGW96O.F"fo2;?6W@12f^)QU_nZakTcDT`dk,m.D>4H/_1Wj.)=4[1;
%9lmMcD?e4Nln8$<fl.9iHR[4t/t>GQp'0D[N?OXBo&fX[OO'n\6lCYtHAs5I_M>H!c4m92U_9[@.h!om1d1CD4Z#/`1OVL77kU'U
%Z/'_G>GtLFks]mF_iX?aE8!BQ>RKF;HLDpbDP9^/4!)eA6^.PLA&"_Z6"="foEQ`]3J@ten)Dtd[:9uA%QXTqmgXuV[E4\QbBZa,
%iB`]86hrQIK?Gel>Ue=mR[P?tGdSsb-Udk<\b_hHH%91m,YUL0TK_>mGJ.`2V(Ata.B`Kp1'haC7N3h/AA&jhoan!>hDoIgk4\1X
%h]Gc/jKp]TD\*F#c0u,Q0+4L_ZPhg(ciYCu1E3/T.B&Q`D;SIN7p?)*3ZNielZ+\%'msL3>PC(L3[dJ-A^+=aQ"s#PK.<kFGt0"E
%,_Yc(EQHgA;"AdZ/ngN1F5FWVF";\#GR+(feMZHappc?#<X$ApH+Z;n_,=NI?YTl./[AJ)V&(d_]iZ^b`J(8H=]I/!W-%m0g)1H^
%gJ],)ZtSh<q\'VLc"Q*<.;[TPNZ.UC*6,DTc!^o=FS65HXppD'VH)a,[/8nHW#$`g:'qYs^5ik4j`X'sn<m-u$0Y`Mq&)F8,(@R"
%<aU%g0u5rA(P*.*7-V[9OXCH\=!'ea$<0l]_Jj_,i1+X;-F%0,Ju`RPff5m!A>67ETsWQECXF49og!pSi\k?WjE$!8i,WPE=K9cl
%b@Ds8:Zf]KJpdCM-7Zqd8O/8:,<;f9K8!)4]"QF&)u+25OaqP2$+4\K%"<FFlspktZ\bF'1mlmYG]'tbnJ#<"%"!#JSR-V).7g+o
%F2T9EG9C>^_a5$u/27`^J-O0T.Cki[W/F+6%FBI9SYpW<hP7CQ1I*6eJG=r#Y$B?N.X4H7ZRACAATJ4%;'Y?kZ,hd+PF"pd.BapU
%>!^di@1Qf;QatQjKd=TcIn('&/`^d*QgdN+H9[`.Hja^LiLnX5'*s3;W`rtW'f.mF,n;2"jFm#fI(S++lXW_]h!Db"o,\kETFp,e
%G)5Qk/C0c/G(t?TBdrd(cgI^;fFX%YZOu'O!SZ@TT#KZ-LkP^=I^DV[p/?`oK<;!rHIEBJ=3DN'>suLu7H2$PlJcCn>C8fjq^%G7
%o:[#rQmpFoWtsB08bM$XMLfIgH)]*qWXM<3,Hkc&k)Fq(BR,Y#6BR3IbF\f'5;DbHc.:]"Qbjd]RN=Ko3OF*V(?I./4i3"<,'0hl
%/[pY%;Ekkq@Y3&ZKeV>sqPJ^8ecU2?Y,VXa5CP3O&9b<qi-d6b#Es+ni8cfm"-[ft(WTZfROQ;TY,g)$-Y3N\P:Nb[Gck/o<T5#i
%jJ@Q[=Ws.*CQmh$Z\GQ"kDkGt@LEj,M5L:C%U0ApVCC!_$sUK1e"thFV%t;00DEKcrQ7n]gL!eqTfk,jRi/*MicRjUk3Y*G.X*^%
%mj;9]:)6/eU588I!$P8aGFk5>@Ths`Rkn;!"_$hcJj0d>\j-q5L%si;R7F>`H\JZ'(*:F,Q90V2Fs,*])Xef_djuhk]7a^-J]Kru
%e*[,+=:ZUBkq!n]?%0);W#76G'R7^H[h)o3/i<pqK/%CAM]'Y,0^Ip^gK0\F9ZsHf)hpJq]XfJSHC3+#VU1<P5$cjFIB0[8($V*I
%TL8M>6ccr?b`o(=.PSd"IbHTP0$$\qeH0b>p%J5K*fP>KaI+\YRUm8"@AA9bhB'>+G_B-Oar?/bI+*74o2:r.eQ2B:+TZQ"qO=lW
%'F1D^E0#>QU]8qb72ioJqXT'qM9IV@XON%?QJ.s,nns^`5>A+7nnq`<#SMOUPi-CP&Xd]PQY2h"Ln?:%Xg.?_nu1*d<X#L7@c2o5
%g^Fn^ididKKb>cJY!"?D8'S=$3Ig`,g^%9_egAA3#T_9j!0_Wm+iMI=<X1eFW^bfs!.<*)]EKm0;q^Wo=H]JG1-dnBIu5=[RD$`h
%M?j:1crAa'd$A]F!SCO['V.gLbm''GZ^8b/]:>5K-i'Lmfg3%C@8`!^&9?;m;m+qF3`L])-oBBd/ZsAKmZn,T5-B9lqf'<e=rq+n
%+sf`G<1O6;:@%Z+F@38_NFD'7Xra`6l-ID<DS5B#ER";"dC#^OW:fjZDPXKWcN6hgQVa8`JYIM8''\7cRflU\pMXjh"lVW[WhT4Y
%D>)L%S7Vm+E9JuK5D2#$QNj8-Kb2e9>S%*($Gg01&6\"8nYuSO8d?.,p>rtj_aQg.Oa/fdYo@WNb461@(6DT6)$@i(YOi)n2S07Q
%=N,4U1o#YmD30SohF$BT!3UILWQZd*mNj7ff6lhV8\+0pa:[,)+lqWrK=n6j/VT[3c'mYeoa!]MBU@%?o&129iEjBLL%>X_*Dq('
%>OZVf-39$"G:o)(L;?-]9?__Pht+':$T)@6lH6BM5%FAm[;YkCNQK:n!Fig([S5@AU0\.SoW^Td7qoMU#PQD8&UC]%a?:k>K'p?%
%hN%@!BbSE+]u;L`VcS.[kkVNXhqI-uql*!bME7)TfIL]A^f@i)pbbAf^QD4fG*MeN"*B^U[]:mOcQsGSKo4Go9oRX7WeM6TVFi-k
%UNLXlj,RO4*6N31.*P8DbQb`I%RS(>!NsjO#SeD4n5`gc:60pG3m/0,aA>G!#!u40=;X'bR8S-[6*^2+_'%B@&`R6*SUpE-%;>7>
%:[(H.692J,&h4P]'lC0^S;>*,1cH%?fNnanT`S:JM>W7m7+8J[*#aN).r1,_m7J#9Jlf\*UOBm#HefiPq3D8JT>5b`H#!G&JR$u?
%A9hCq"K2Y[o!u/F=V(Vr(Hi&7M`OEqft/>,(Ma@H.g=A;+RI#'UjHd,fqi60/t47ee_$O`>ZF1S4!<B+]nDk(n(UfcWjnmA?)B]K
%Gmk>CVFq$F9$f#)GIOGSI$k\KcS1'AKlt3f'ZL.MQ%[KAh8/5@eYA;V?Sc3Q3J=m/,320t&+$MZ@i@ou*YaBq.<2,$kV%=Q7++WO
%`]"U3VlOE!7HW>mp0ngI1KteDqpVF2Cf,GTG%tL]TO34sh=$uN^Z%-=U]0RZoDNq=+!n"lJ,[jpr5t^EV#+R3&GRBVAC)n1m#i3#
%g[.,E8o/+g+O":0<Ni\LR%F`p@o[JOL'WLf;5YA$P=3I!bnLBELYhPA_<`&$$[i#K2?mPpl<$fL:b<5b1a;$sjqkR^Jkp-7G_LCq
%qU0Winl9.P@l4E/P.L*K\BTe#QAfXI<.2nT!?rH8]s/;,+J"\Z;:l1T<YZ\B/1>31V*-d!Xi1XD(8O^.n:n1&=(lg&R+H_8f6HEq
%ne(?;7%?W</sO\+_q0@4+E(l/s.r<le*5Yd4P^2[_%=m+G-11LZ<_#<6i@(f#2q8Z0$tSAch7W7ZB@AS(j%GR#U]P/\"R&t9@jq8
%Hgl"@&8GJUi@o<%[Kn,8`s@93;i]+J=Z_WO&[.modA4eoJNS2VbN6s0A&"8(qsMa4Rl-.JBkc)cW3>M2=3ClCU<9-K]P`A__t#)&
%%Y[NCF",%@o(i-0jZ<t5$WB#d^eUQI5!8E(>0h&(&G<Y5I>AmnW(Hgi&T.\IO=hta9d0Qq7(a3u>c(+SL=kN3,_VE!97DO2[jkko
%K\m-I+<tm**`=`u`H$-m:\GS[igmB)bkEK<VGtEnWO<hQ>:.2oV=u]p=t<.hXXPF0U_K7CH)]BI_UN%JI;(7JURM,fVCb,:<e4PR
%?V'lD:jRJV"'H26fj-&Z29\#b@D4#1@YGPGW4jg8DP6B7i9sA#5s#LleF)%V)R<Qe&,?k*#j(SdjuDP&^1]^b8mhJS=L!S-P),gM
%%p.$d$BQ4)"(>&<IhC+*ZG]r[Z99kc#!%a"$eLg/O\ho3KXn/Q*40#<OU<82ipR.*E%,Z3.\R-g+:OE%Y^u3,-WsGu8'!7$P<>"B
%Pd@)[Eh++FGHmI1ORG,7NL_[IK^YQs%o0E&RM\f<]Pl2dL%HQ8N+2ga2#2PWniLjFX26QX*U[(Y7&\KH]?bSYMF+DC*Me'uKR2mM
%Z"e)=_I6/Vo(`oJjdUFP*7\Ms#sb'OLQJR]3$I]>lf,g`:h3dBJT`IV/HX/7F9<tR;oNQVH1t/BjtuC*Fp*MdpP[JYmkamQa^1nE
%#:$jk0KVP#9GBhbEI]jOY@djCH(c(1l%^d%_-=Ns$d-C_!TCP,-9@*OKKOW(J$0-S#2&GUM*5f'/2=.I9G@PDi";+R)O.j\21u$q
%DfVk(KVZI5IP9aTho,jaO^/-S1JEf'.WcOKK`kVcO<e*VL*LocLBp-?%[El<=NR^B"Q#Hgf3IqG3eB,d/-Sme)T&US8*l\U@b$["
%j5=G/ot!Js#IX4s^8jj("(A3qajjhCkn+ZBg^_lph@15>A899#Pt2cGiQLr=+s34&dYSG4%*@upKe*.Mbl%sC+<BRD]D,Jf?N,Dn
%GCn6'r>1LN=71#L2;qldW$#UWb_I_+gCu#HW^s;F2keSLCCl@-(.)>18ktSf-Wt?4/dOlp%,ho<(kc@bAQ<UHs4Ma_SP?EN3q--i
%fpaDHH%8pnXp!fPd&*-=G,PNVb$kB^nbq4g@-1g.#"]E*()\Og&scn[hu3BlCcHU`rYV=a/p5>^J%J*Y0&%/WX=^<RA@oZ!"1*<i
%cpntEp<XXeFdkUu_MYc51Z>ZPaA;\nOdXAh3UQ/?Ug4lS0UCJkj(TC<(;5\aec8:jltMhUD32!p)7+\gC,<"m(u[F9!5;UZON=]a
%9d^Q;VUF!KT%e+0:4r7#47UV7>KI]k^I)k"T&-V.<?rCo4P,n?%eO7TcT`N=>rXhG^_k+U:?du?_EP[@)>Hp$J>GWK+Q('j$EUQl
%A#6W7k<75i)EJ72\N9+d?[f#M3j4#a(([SHdc8ZTSD.Oc%GBbpg!Q]pbX]E+#']>kG1H>9:5I?Z#BDec+\TiY8UQr7JMN_3(Dr]\
%KnP%K!6IpON]2bliX!o_>1KYep1b"LqBt8gl!\\31.K?&8_D]rlbB<GK=o9>/0YJ[0D,`H>qKF@&U"V1%PYcLeEIt@Kl"H9s'1P&
%Ql;YmEMo`C'd@g=0n,(]PegIKEhu#dPaFC-_+^OGQV0t0$AjFs^l>:$HZF+Jo,'+_iGC[P#Y,GErp\e>N7m=j/%aa.1nLqNL\s*S
%i&c.[fn7-e,o,tNRf#P*Q2ZI8M+44t7>Pr,p9tr7-pe0UfN.t7_3`(Eq&<L0+Y)s^AO%YTU?1O)8)U5WdpEd'!'2<Br:jD'-@2nL
%$[FA*9Y:7tKqBif[%H]:.H\E2UI]Rk8:6E:E>"s'_FLJO`?1:oJ;e?CZ)"Y#*b=d8_#B"a+YKd4I?@O>K;H==ZcZ%!>?;8'ZWKll
%DjC+\Ph-fUL^fk")Ei!f8=>pHF9T]4kJl-=qH`Y/#Y)Upd+9Y3_RZp<d)qiK;UQAW^%H2U>%Ab/;0e@DXKh5CfC\".UE=N/nFB24
%'%U<,c2-'HhI-"irZ/T!?fI%0$97D'b(EPC;_XZ(QGT+c8JAHlcO]EhWG<RKiK[Ce?H!SJ1;c)KT9X<Pf,T@)"LK!:MNm$+JMVFp
%B[/X[K/#G7n\?INgp9/4hh%rIbbT?U3h%SrQk&shl"pL].<cI6ScVna1L4q;DH1fhHJW@`2F8F5g;akqYrb?iM(dG-npLH-EBWd&
%CW0pTqjOd*ajf<g*X]Hm=pNs\ap'B.j\:b5'7WPo&]\dOCk7OG3s;OZ?nTq9OPU6l:6R<E7B2e5WJ]P`rYWU:kKM&i-o!5mU??['
%W.q4$j\"4'RiaO7MEsNM`;PdY/I#P(5/Z4lrmNjDfXO(Uln+1$Zt]!g7'F?])?u6qN$W9C=3IQ98PW>F)"<5Z#_2okgtP%O3urBb
%WXf+<1rr,$<*qoTBdXaYp6/JPdo>9o%BMD[0[;rEr;$dOL[cnTqNgZ`#NLHL:9&1GRfj8B+2\",RZ]Ve5A'7T5ucai'J8UU;6oT0
%(dN9gm*[,29jM,1"h33e6'ql:h)R_,od$J59)4^iHm4#j[VIn>ei1Mb`a<?X,XBc.b\Sb4b#i-&l)niYeM)-iB-!+?U7FPHf\=dk
%$fd3/%5Kkm[D`0&=dT61OPQ*sKB%R0E!%Y5Pnqp!(8HT-E_^dE$0pG)oJPK,;drgKTK@TR1,BiL<2MB[OuI'OIErLKC>Q*W@K`^X
%EA:Q#S_kfSHg^V1>OAgYh^5,.EG;N>^r3P;eLY5/=<_Mb]SX6X(1].1BoX2,=0(t?hfl[9e>^k0O!G[C)C:+>.9(jG`<mJlWbQ>o
%X^k%qc8U6YF,rZ\FN_)FaMS[?&ot0212jiPclB9WZV>Hc'UG-icibb#6mB(hRIZN)&kNl*`3?;#+*R8!^t9n[7YDaO?$Mf;4AEO8
%g1s7;2O0J61mt@#;q"iYF]&qOCQ)XI@3XTpJ*Z)LCf25PPqaSrA.Qc4&[7[uh>jlbr0\gt2SI'j=K=9.0]e>f^jEUZP'0F#LW#Y0
%<.fG_bXk(+CG0!$D(,4hc#h4l+mfM`aK6P9cL-^RV4\A@CM\n0!48LncW";"FNe,M9"B\rP97)uP?R[.il;,h@9=8SA\<Hr(.Q$P
%lTFQ-$!/$HN$.,[DsW;KI?0"cn48kXbLNp'5W<*K4K^a`BtoJTJ*:Ksj3?j8^nP;,6emcGi"Ne21p:F?(QAIqrCZd1+42J^-V+<8
%BSN<V:lEL%$D^^/CI:A604LQAJu&N)G#CEh`#!7*Xbh`P8\Q.q-+0)RYZKqD'-,ZHn`j.S;-%Y\>*ssODER(/27c`%cKW-u<OXhV
%@>9))/`CG36H!%CO>[Y=ct88hB,BLI/R]8?j>0Mdrj-5UV/dS+!grp9[TOH8S!WbaMFQXUA,1UQr'5+)0Q%IK?)7ia5]:Bq2lj#M
%W6r_s#pA<)[hQEm&^WYW:i!M,7XD)e[:u/e?8M"h]Wp"*(W&SJ4oq"u88*PUhlRuf)Ze[U"a*V`GE64uM'MsD9Wh-+$0e#lOVoU9
%W<l1Ip-,9>UP?.Y^)?.iW!`U=&1pKPH\0CJY<+dMq?R'`3!5W]%t/c%Yg6ias+^?uo7J!-C?jEPi#kA<ch*+u)NtV"0s"SNC##mS
%ER.Z_OqWfLF0f0l$"VP<$/qSd\sWmb_^/3(RCPTdHtr(pgNrKF<q-K"NNs4Y;o9#tlPlcPo,k'.]ksR04r^Q&g"k4G((BNF^=m1j
%6rZ0F5SYk,`VH=))'_F=OJET`5_!3>e^>:fVRV&9S7#B3XtQZNFUOd,.Y5m8+$lGlpD%bb9=tVP-NQ';KaUdJ0M)onCPh)H]Ch'r
%;25kMH-sZ!"`N@UUI<3L2,VJMA5jt+g\jB1GFNmCp"q3ii8lJ"0,U>4ZCask1k5Rh'Yu/eLNsQLZTh:cWA@BQ)r.GB[OJK:B"7U2
%'u18qIgIa$"ZSbT=X*u026RM;DDS)F9G2]FV*BU"8>:%4O=JdA(fe+e88;B?E)Y9[JDKrh!-QPr%X)1UCV2TA(dZVGj/AA4VHl,d
%&&\I,9gCI)G_el\)]._Qi"CE5q?78OSGs@N&:_N[oW:Ap#B(N;!j[bV!ZN26@&@6VCL(]0#=e:h22osp0WpUJe-OFc%`[ff,5bt-
%MbY`uD$nSu&MjP302Nk)qjT`m[Z9kJ6ub_K6`27O$^/^mUN;MpQY>BkqI<nh=535F;n,D%/\7FRAf`$.OSB0k)M95QFISY09ef9)
%r/ip0k,>l:l_Y.(-VPa2Ctp!GH%f`'SL`qQ*71AjUVGBWRb*&r4E7T&j&g?FP%Z&K26Ss5RqBCPEpa$21F`%48308*D4NFqdo.L4
%-14q<eNAgiD;.D=G+VaF8kB\j6;_TN7u)*)]h::b/f0@>`+InsJJ.r`'KiKZKYtKPC(:7DS98L/=6g1,@.PG\:j^%h'OAV$Oq@f#
%@5S9Taf10s*)87lb3=,<mg&h_@@o@,jG<M3fXJFul&7%uV+7<$S\b?VK/T5,LF!%:YfUY)oTe>B8nQag#&)X-8^+b+&H)"5f1Lf"
%[*Da&X%BXN[\bB%(sP<u5;].1$EpLr&]t3E?lYe;J#n9k1@Q7rmt-[j7L<fQNs4^T&*$`G6#r2g*..N<9!ssgL,3okQ$$LW,X.$b
%-[",6TP(FJWp*-.745JkH"SnW:4$'QWPIDeQes6()(XAs&%sHOn6WWP#k>R_1LnUt^Q>V!cR@pRS;Nc!m'UOFF^FCM:a2$p+`1)K
%*ah`,%U4k?<S5V*idj9)Y1ji)pVdb6U\6*2#rVOOZXe"O_<&o84HS>WKXU?7j,^n")D;u1Y$B%`AcRQim,.ds,&cb_0OSB!/hb,f
%0f\9];Uon\A6BF!Ao=Osed0RAnohgcdC!@sbqC=h&'bpbPqB1n)E`B+W8WaVdEKC!aG1,^V@j]oH?3SiO[ApZ&dC?E2>CZ\6TZE!
%XW<8,LKFB]ikd&[>"mc]UQVuph_D\C`mIM:_fiVp0ga6tD!8?Es4F,r<9.#Do=*?;^Yltgn&RTT+M.N\E8geJ-gB6!;FkgXOT[N5
%'1eNc$:M.XY6DoihWb_8-7PXcC(#u!,\Gh8,9&NnU:nuBUs"_^`1\1r^7(_>GEl!X=N8Es"K;8I%L"^3T)edF7%$k[]9kR+\;lT$
%.7LrKn#\DkbuYK!_Y>,5(=B?[+%@AfJpfV^E;(Xj7^Km#;_e^+:s!6C&?M].6nh#&$o6^;:V`$8.FrVbp[h69EaWQGA[XtZ6+$TD
%VJad[cYAbO.$*G'IV4ce)EFiH8dQ.,,Le#D]k/4X`Wp5FE'$tb-Zp:,a9qlPH<oOH(Q8UJUWEO^_I4i#\W)hATa&H/NYPNnMn&.R
%O1#b)_HYZQ_XZ)=E'UX723q0fZjo;#37qdNT(C>u';N-Yl4:_f=\m[OXn]3,e"0&'MXcIE=Il%C690R(+?4(2Nrc'Ras/nl1V2;[
%FSt$H!*m+dS/`O!kNIW9kGVAf+L]&0d-dP=li.uV%J5%T@WJ[bW$`G:]eP-2aGqm4YOMtKP4>5Vi'-7n*N"-YAko(BTC6.Za#VmP
%@i1U_icjWM5%6T(52t8a7guBW+lRrR"a0cUBh7'8lR63/I"iClcq]50pl*?-DSSV!g"rT+A4!hi"cOhW7p-^c2mE*^31K:?K.9]=
%cX7X$d0KT!8R^TLMI6nDj.S\]UDj$>,49#tpuk9e.NLE'i8a+rV5/b&rTM;gh`NC&I8W2rSgVL`AWDci#'B]WK:+7t8#b,4;B5/j
%h.CuZ="Heb-f3/+Nk>4C[c"Y4[I%QSa9aOO:WK.D2C72CPF1E/P.MB=rMt8SORNb3Ppa5L\+':6(^]:a[9k+2+?>$bke1BJ1I-kb
%3d9&aFAl&`-!NKS-6ORZQm9Y*2CpA&5A("?2B^/67$qM54Z3\S&)5VaRI@Zf,=?B3!X6EIs%m"`4<Tb;g)'\<+GWthi8;1^e!Krk
%lggN\\j[)Kj\A3BGtiA#Le`,!bmT=ALRHtZP`[],;Is?M\j_F6YOHoMeiKJ+j\Jg^hNb%D/.LjS2$0rSrGbPUjV6W&/=i,0jM<MJ
%`1M$p.maCG>\7b(fqQRYL8Acj)\aiZc.[1;aiI?c=lr3"j[I$:#!q9!6HQ@9`XND?M,iL`%`rX?d8=&6Y>@ZR#L/GH=g=PRWupIr
%_qanLV]FrR>B:[SJf4;Gfc[dq?$>f$=WIVDP*s%8e^W@eJ:B[?UV7ZPqSX*C!8ZV"!s1f8-)+3iMT8?#Uq&[5%dBPW]N^!45K138
%SH8DIW\^5obBgN)&TV^mKR2C6p<LpCgL!`-eY(`DkM%=\ViA"B3:ZHi!X\S&e/KHF9(%F'`]K9:1&iRjLfZPb32aMlCLk^_4>%M9
%AL)_#Y7Q2@mHM0(>9,H&e@rKDG-TF((c:sJkM!%lTFtjQYF%f@&2^dNCm$g;B.,^r2NB)4[d)Oo5./-85I$S%=(K%P(eSTeqi&]]
%rL3=g]]:g](Fj)8<E$\bS6HX]h/LWHlNK':dIYS"4gq2t`3CQL]hV0`[oKC%:h*6870%;u7O*rsG82c*n+IB9Qd:$Pf[r,@p=n@n
%B>):(DSTub'I+D?4h@u+2p-[KmT/!%4#BMuZqlHciNmNM(LZP\-V$\PRONW;W\?qK96Km^Qb-D0U'sO^f?1^)N9DW)Nn>;H(,A's
%:Hq%;j%b=90ZgG)]V9o&H7gQ&-6=hlXp`j3$X'kF+HUIVnAiYQ\TH/XX!8<'^N$+JoWPfcbUp/u%]DV1Fs*oc4*Pd]'BV.-OQ<i0
%:=]m7M,lF"L&YF#*tbhNHm5Glfu3V1pns^EY@=4Td`flPLP"rVKQ@Nl\@(/1%G?H%MMH@43ItX.D%JB2k-?C"Tkl0;lWQ<:RGT"#
%H<lRpUjsLr-RmX/;&PE`Q\@me@(u*V)!V)u,0DSX<V&TM&PEeN/ZUtUfKld_Q]R1!-JrBnd+'_Vfr,k!:-C,ePa3<-peL1u@8J39
%',m\`L;]Ibef8+'^D\nW3<G$b,q<opGu_`De?"$#9"]Bi1H+<T,sj`tM"6Ub?=F%\PX]r_gFT5jQRFEYik(77o#!Di&/Jr,Y)6nQ
%_TGH@%\sB0YE,$H(SaN--t>&B0om64FM5H$]U8V]3mGp9CiSNs]sHE`k>EfH,31?+nob:>d>eFuT6'T2k4p!)B:V"HL0Dl#c]i&L
%!S:/$hYlL,5_jpWU[7dMg[@cRWAq]7%]6AB5"?HfL,fJmXHpnY(A_sqMZ7P&(QiU^UE2#\Pc(4UlD1q@FkmKT,!`^G]WAr"6s..O
%^lWFOjsmqLo(bSk1<8W.2qn6>?pX="N"f7K#i>@>G)Dm:-E^o,_n-bDU%bLHMo+Ekg!@o\#JD/#*U4aJDa=t5A1J#_@\(>s.!l_*
%'Ll3GO^L^UJLZo"UjI_O/g`pGfNl6W9X*C1hAm=lW'Edimu%$-'prkq;d7:R3H=I!L4*F#P=imum3[]um,P7?\CQoLUp8.QaHdOV
%#[Eg%GlWD*$Nl>d`<q7*:CN^e8lWGB[a1C,<rS_H\fNB3&Gbcf67PNXaC5iN-WM9sS:7Ksmm'W7R@a)<]uf\OL#6:3,3mKKq[ut4
%5!H^3*0Qp?BS:*7@<Elt)HKM+qfRI5,:JnuQ7[Zl$XCUsepr6i%L&fJCT.JiZRpY.*mG6eSY,'2pb[gq->Kg(KNSle+$=35RC;=9
%2\e*OV4+"\RbZ^S>4&goI&j9C7G"i=r9O\moA9XA2a_QGC3!.F(!kP#JpY+;,i3^;LCu"lrP[-l$M7fX4Ci-UWDFedMCMLOFVUo\
%W"d6@n"IU?Ap!hg,0[t.[t;I87tnq'TS#@I)KD;4/Wfe6Q8l==b")c(BV&7O)KtW:].>u8,_EAVGoVDj5W]0++#B2G<*Ca&FNm$:
%q_=3-MAT:AC^)k+W6"gCln&C\_+%s5Bk6IM2K#.XiXNZsI=trIgAh$T5(I4E)m\_s\/^(:/u]ak`Pfp=+0*.p"/@T$..nL)H*2Ic
%>e=6Qa:ZnpFWqka9i'c5(\!,<f;?(XN"Ek['SNCXcS6h);%clLRA\%W,Q'pZ?=_L8Nb\%VROe8_P]_B^+CD1O:=MjF/Y*#Ei\Q$!
%Q*M;*D@&QSF?*`^Fc(D2bhtH-3L,DibZcM;Wgu,1e(rlhA!P-F*&cU9U$%n<IuVI1O3l)o6G#K#0SYAral8bK:$$A&*`(_Z3-?I=
%OE]rmeMrg""W#)k7L?Tp`M1TkFu@#'YPAK2c.!DC[!2Z:Yg[iKFq.<[PtAS;48HR)bX"CC/Sk_i/gF5j^RVNjZhQ8_C<+_GckVUE
%/H,O//?eh1XQpD-DnKs&R44r`Fh>G6P3F$gVT/r0ocuS3<Boqgipaf_'S@Z%_Qkm'lW27EiWBiVBZ0u87m'--?=GOa%a4hQI\Pd<
%Dr-7hLa/!TB*9@ip7j(rk=<2o@.[rK!M!NGkpdYQL6/$X,2oc1RNU67/Xoaqo=R*uZD"F]J16!qH)MsK`p4_M4aN,BIB4"8nhio#
%m$6P_<93OKY`\8POA2:H^HKR8[nf8?CfmY;,'VVb5QQn#eEaoIOY'k[k+d13/1"oBI*rN@8<J9$e]g=:&(6C*qBqr6KnsL&os_Rt
%_I;_$Ou`/mF$nqK8)j]>IZBP>cP7k4r!3EgO3JJYT*Q&P+-)R-J@h(W.,]De#NXbprj<<57R/bjRKdY&:g+#abdNkq.q?7uGNr/;
%RDC+5Udtad#fdt*a&D]rrnJu3,XI#WQ\5t&AUb6&m.%Ql7!G3dQ5m`<e#okEc,-*K:$TsJKe;.)):dW_DN[\G_/<lfGT?GpeEO9,
%H6U;7>bU4&J`HDp0SkjfA;1_WggNC6d_.`[285Tl3["0bjPmBKJfRU`i.s(m\HKp6WMrOi'0Rpn9QC9$"CSsl&tuG;Yp9*Il7tht
%7\l@A--T3_3G6MTm@7Hj&i`4A/0JVAc[NX!iC>lgf8VFo7N-_g-AH?oDVr:)+::n6VO"`><lqamV7D#?332r^O*O6@7*QAs/le_b
%:u$stR%$A/"lZEegM]rN#8!om(2X9B9pbKNXg&J[1FW<$9*(:^U]bU%ik#'JD9?P[g)0ONq$jG.G:(cto[oH>M&%RM6s;M2?(R7T
%\1%JB0'\Z82-)J\GI@,79WC^2i2@l#U:Q>c^B?s#])=p!Jg.KjR).9N/2.$Ef\,43bjP179M%55&2a\*rnM(]M^L"dZX;b2FmBh\
%<I0X*)g<%6W;a\QSB_*+\c-7.NOp9V`BSeRL&J[['al7;R7QrJJ`^SsZ5`rTk=%&?@ft;7]+[a>.4%*rCS&km'd3#ck5;cJQP+_-
%q+*141"I[4CguoO(r:GKhfOYi0pU70&`S]1G9>#P):+QS<ZbXE!:Gt5jG@6kWJU^QWXGH_0cpG+eJh5SaA\KV,SgF"6)YYa7ee.L
%8AU+2FH(7:`S"ZIkM9X#58cBYp+]kd9))2e:o+3.`E_ECP-'4mmRgJ\5,3Sn#"]#ulb!&VIJbm5*IgmM4)2itXk-+P4d/-_]Z3!d
%j/GDEK4BSTZU"sYlfY%#ZOOo+q07?/QJEW-8rh&;".["EV*+IPL0I-sl;;he]]"b9+VP8K7&0W@U+:N!J;u7dCdm1FItJ>(Gj^CQ
%7u)'eaaCK9KL_Cr7W\I2ao`k@0=<*#GA#=%_CZ`:E^P2,-`m"RLZ4ntiV>ZIgJ_2[Re=.a3O,U5b9NZ[mPnkq[D1R-KS'Ur!k.)D
%1#dlu#^g7Ik]uMhUZ(GlcBMiCifd0W:@u'aW$C[1:pDF_g_TYmnTBl+M=,E4WBWj%2BEa(FS;W&C?s;:9"GI[G:%'\(nIriP>F!!
%3,Uh[H4jinOq`b*EJ:2B@kirMADN++R(-jkci^<U,5nPqM%2i%4i,%Rk%]n&U.$[0/KR"7_$u_W5[KK_MMRA5k,uY?12c)?`G2L[
%\E]3!S"$;a\TP"mf![CA:L':CX#p%9JiYl2,,Hb,"QL,//<.s_NULfn+X4E2HeBKFMIOB*o@i-SV'G*"/Qs4j"2:+GM)j@E.Q)Rd
%XQ%]RP]\6*>7\M)2:j#^ml"Vu!)QSXE2(r"<Fe6Y+2^Clj+$]'?ds4l)q#^Lo@<,bn&XG,l`3c"`u*XuCG4l%aVNk:Su]Uue-g04
%N[YcHLJ\eC*GM=4(a!p;pTDs4E;TnG:r_IZ`")XNfn^sF0n;(uaQ]K&K\VC+(IaN-NBGcP\Rj9OGE=X"k.LO2\lKr53,:YW]+;99
%[sKZ>GNB[_.nUOjpDn-C(m!OS&j[[J%Us/OpJo-n[&_k]7^\Y7TapF)1/=+g_7+UShpX27(@=Y+r>^!3Q8B+2Alg7"S?Wj9bAt=/
%pQU`l22]*S7;?&1l+_Oq0L-`Z^)nj.$(k3BC@:Tpl=&K/r#5+Mpk1K4P04i(R%nupZ!B]RkAb[IFs2ta*J+d\XtW:i@TI'cE*P-,
%UU@j4W)_nB@5:4ST\;WUlk5R0ad7)C;&N)f'tqTc<S`l;%4qeba^_J-XbGTdq8rhupE%+\(9>FAI`K(NI7(hN,92jOO;K(4%YlX/
%\%6)F$ma->;m7,-d*-0,8"_q8)<T9?\D4Cf,m>/L5]K:6O2ekT@O'jaHo,^bE9NX)+f*c$Q]mN@7i_!(b#_+f24J=RN(\g#OS`I.
%2cIQ]6,1CdlrG]-a@<lN-!0772$K="FNVJ`Xg>GK]:`E\SBmDal[dg;:+b!tH4/;D/ho4Wj76cR[F^c7B8Dt2:Cu,_'OBUZ'?O95
%RH]QTJT3E^2Y@3gEpOY7]IO!ob[/1;cs"FP?EW>EFFiogWb26>7foUCNjIL$+L5q:6HX=`9-:_c\TN,m:clV@_8AfmfZ/du>:7cP
%lA7tg*pSmtL)Z0BGiEoC.&7t1C\hdNVHb(4lP9hO<#e0d^+UY>)u@h`d<pnt3l,4CTX]dt\Y.K'2ql`E>>FaQ6CWZPIL%7q;.L=0
%/,'>r(E"7dRf4i\W^9mA.Xl'^8bbQTa!dgIf")Mf)u>n9IS?DR"\JrUHRjsRL6:?u)GNX."DUYp#j9#:?[N4!AKmT_Pao;p/aah"
%!2N_$7TAb1MWspHO"\g'+A:=N:k[5ZCoVo#H#fa^c%&8]OD9G]k8OI(0NP(rKp_+E7i=r/Bt(nq\u%0A8/a9&HfB`&ql5Tm(q'a)
%&*(bC:Wb5mXZ2d]%p!F_CB,ub8n?NFW!#c/=B)BAZSE]d0U2cRShVD=T'1BSglsNF',?D3[EK4Q<T>nWF"5sA</+1F=9oS$drh^Z
%IVB:f.j:.\&].K:5U$&?n^j5;#'&(Oo>A%hr?&diU7^W>W<1,_"e:n9!,3,iqXu^m0ULWiD9k;:nLcC)>4@6n`7h'6Jo2@[nL2IY
%7Kh&!#_h["OYc5V!bj'Q*j2$lN8`g3%6)TeWE,?Lb>=mmf,jB.,[7/@b1/kj_T+8W2q2Eq%9Dk)KV-n=rf,3uY#;Fp.N?sfG<cd1
%,TfQr4f8_?B!(LgB%H9!DAffS7ti'O,1!g1`JmSR>s#._XgUne!EG)CQk;S(^-a0iT@42g7,b*V"Gr[+.:WF?kjGIX9U5%5n18_$
%^Ls@K>]JN@PuNQn$$)#`Zt[^gFFR#KEr5#(+(RY]Uh0$hK^l&h]L$!'0s4&t'2=VX!#V.k$Cp6h\Wh*^,9a5uh#SE!UFJ14NVbFs
%&,mYNMY6/8d)K-.\;X+E&rITthKiSP,RSrm8Kb`f.(siTgGNKDCe^ga4@41gs"'S!,?*<^XZMf%cA5dY<mTFGfsL-&S3l\%.4V!Z
%!QZQpRNK"R7tUcF8,f>;o"G8*^s\A!]G^EbY"%),?("[+:=fO0mnndk#I'L7)^hA8GtoqfYUSBC'LC@L$FI:j"8'iQ,`S!n=D0uC
%(>41QeEBut4[sr9aq[n;\V?t6/4:a`d%IAB?E\-5E?k`\M8cjG3PIr0Ilq;b*ib91lHS88beVQ[9IU&&dRjM*QI;LX.5mrR[M+p*
%"uO@s*T^cg]A[1@V3>:L;(2LMs7Rd*CU?sdCT]W12DI,0Q`D;/\R)NfR)VIAVoT-P.C.?1.3phSm:*q@0?2er5:2o`0cm_Ml4N5*
%5/sGW`$:B3U<B^fj($kr99IJXfgcpJ5D?4/0p-'83X2p:E<*M[#OTfC=r\8%A=1KYOZS`sM70nY3@FX0jKmV-Bp8TV!flQ]Rs%B"
%K/KXsj$-$p1\UZ,]>\Db9WQNb<fF8U(LM#,Ric!3_?q9K4Z,!p-Q^SfF+A+)Z)rc'Q+uFs1b:%`@i^$S@ZiC4MoP-^7ef!8N,L0I
%&_>S0A+_asW!Y/:;SJ`u=3U,;Q5$H@`QVG[r="2J2Z:s_Uqg[iQo*D3'09AnL%l*181B@a$-DL9Gr)PMcuJ!,(.fn"S#[iUMW@76
%pt>56^NI%ddIf:[=/l--Ee;h$iPchgjX>I!FRBAR+KLOso%Pa8Lu`LDrkE%O.ms%@r\o2bLnHhTHP/HlAJQit%j6KEE3=H1+=/e#
%&W9=hH@QcK'_$rH=5PdLL@.DT]l7En)8R-d3;1b;eQR'>.D$!5`M%Abh]&IO6.+tBWY$Ml^3iSIM04M.cGV:fM0j=d@[70gSS$L!
%;u>)+CM'OC^f.l05,4F#Vno0B4L!\3CXliDk>'Js*#4I@q_f@4nI/bj*l44<Xh1HQLMtTe-%D[4JZnC(orq&iQU9)BUFmAEXDk"7
%J^r(cR]h<9a\L7][.0_hoaH&o:s_^jEL^_#@B5[fo\@/-COH`mBDX<(2dPsOaN,E#\McOXFKG?dY-f6W>WGpB*Ek4U%u/I&*4;CR
%E7`1(^eDL:i6PTD'AI7[6F+8'U5'Dg#eE#!-H]I>HUVYf7pB%A;Ll;,2i,iPl?!Qu'Co'o%7uEJiT4<t*kqphhH](cXshl2"7Pr.
%cc68Q?#$fRda)P*'gYNf7?gSs=JVud2//$Mm,DE7s4HR&IK=F2GVg9R"Sjah!kpbF,0tNROU4q)jk?iO81u.m\RqI"&0[G(@iUfC
%-QX[.X,(5VE;V\'+bAVDYBo>Y0"h/hNX2Xn&B%8@_^jM$a4Q&XdkqAD`XL`idQ#1'b[&ZKe)%QS^812Mrc(pmMtN,()OJqQA!gV$
%G,=hKd._8,diCI*`C$!<?mLH[204:T7A&JJZIHk60Up<a,ELhuUktXX>uCJM#W>n.E(<d2a&oCV]ct0(<TN=jK+hsZRb@lc+D]F]
%bKX$@>CbjRf7gHIRQdbsRcG#i8o<`o?R8f;^o5YjmL(Y!dCns8dLsNJ,WNJWS?snBRkrYpOgJ.e5\uQV*Hhmg&]!\4!JWp;fSjr2
%NMSNH76;np;7D?F*0H<?ZN-UWHfm\h/QF@0""5:17WF8g1,07:"R1TT4ZmG]11F^>A6U<48CGTNh]LlQFD$]cP4lDH8QK)$[pL$g
%73D3_<sg*>mZWH#IKdpr[7d+3L'PVT>:l!O;VKL=&A7ZVObuZoWHQl'fJ.op@o%tFYkP+:d$*:".^7J(aGpTB8'Ir1^dtigk)An4
%*=Zn`6$RSD4MVR`g9=Z/?\%+=;Gp$an>N^"S='D:lb*L!YhjL-17%]Z<[e*,9SsT'DtGM(g4&Pt./^L32NKIE!?8=dm1n"tW#.5,
%E!PBhoiB*C8o]"\(t,r%7JJ2-m]07/69mEQ/&j3</E'4S9mh=<,827d7j$g:k=/%\ad#=^N#)q2?=SM1Nm%,ZBYf4M'LI=63)9lh
%4[qCWN<'X3`BI4HW.FZ/pE#90)hS`V_s:/m-@d$\.&h7q]H!ug0XdX6CZOd4)MRLrn.<0g4$Yt=^co0GoHI7n(3EXL4=Tg-*:`AF
%b5b/@DN6C[TKj-k0qrQ=Q!V(,&p/AiWs3Fr>VE_VA"VH1'0RX8`A>*&\&>!kN/6?P^%W?Cc&$fL)q.Q'p0bhY[:eXuq@%pUZ@foR
%L?XmhMp-nPah[QS1\GA-9sc0JFi:.hH&NGkN428a5ci\XR:.5s?Z(T`K<upH,=iFf9=HE!#GZj,E#=NR!_`.q5!4^q^RJ+QgYGi>
%HK.)u#nP@B1<I&^m!G/%`$uL0$tdN+3\/k6-*Ub*7O:>/F&Xj=gll6$P7@CiH&L`@$o!5a<2/i<*Pc90r!4n"B:`tO-HW<j%1Ht6
%)5N2)GBc*IB`KUKdB]obE]`^_1J=eg1EMSIT".K/)U9Sh-9OVnZ=;5a!<M61[iEiFP`[.?plshWp&Ib;.tdCU=8-'#h^B`?GQ`1(
%D&MeTD9DA-iCdNV;ml=]Xt9R:/B5i`>&,2bbF[!Qf!2*UK@Vf6g:fLXOCSiaS)!aufX(Dp7T1TA3<h&^/4Zo70eAZt',Q7bODqO*
%U\=:<$[Q<.%f+.irjoX[d;>3F$[ct+YK5R*UdXQ7[2f0JH"7t_SYm,"9p!0UK*`0EB,C\RFnUm;2RT<r\@Ei@<'gbE-a"?3Yr^N(
%_aEZf`M[C<&RkqlPqn&T_!4)?!>$:n+9O&j%a`iaiag\_9iN'[RuT,<hXhLU_LbQBo#FOfmtI^[Z,_Yfk;p"==7iO_5eCEfGqsLB
%PABlE6FjP8h/SNiI(oW.3pY<ie&f@)`(_sS)DBiGA-tP3Q2LMVa-0df&KG'+%U_CT7_%j^Q\;9u#XW*9&k=fA#K=R6j3a01SW7U;
%k1O=o=.b8DTt7[%,""GgMYr#Q#s%e2]2s5WLVJfB9J7\9^-0=e)L&(l]@NPB6lelRH#Hu2=^^Xc4L2T8coSJEV?6'cX^[jOr7L4r
%kJ8gpfcMi"pqh.:T)cdOHfgnr.[W;'=m)McMVRD"r!t^-G&R"qWmdN`+@@!R=2>gKU;1Fqc;H_?"Cq1$fk+LJ,=H_L83ABj%FtT'
%hV$`#&YmM!ZtNq()p<@D-8Y8MVulV9@VH\)mYm<'3$s&(U1`'A]SceCYog('3:YhgV*!$^&R+,2T-^`o\CpN'fBi^R^.*.2BLAt`
%lGN1f]YqEQh="H'/Q[qa)6jb4^rDXJ&n\l#I$^!nW*!k.>Jk\Xgmf'fde[G':is[#I`)t@P"q`'AQ))5`@oAi6ec`I[N)B0V7=b$
%4H0MVqO@)9c-fcH"+&5Jk^m&/b[ej[96e`lZ=X9arUrP\-9rg"Nsm$_+oR(t1X]EPr:FKC@#XU7qMmH5YoVf-a[a@EE^td?]P[Ia
%fmP'>f^P9%mjgn1VGO)3kLb_X?8M$%3Gc(Gaub'upDHMVY!h@a3-OF@imGjc_:Pf3RTX@KHj^XM`41K1V;A_1'ks<Pko1MsFGdK]
%p[2Z::i"^ESG-YV04nE[CO2"(qPZVr27PXlfE.;BGehb](WI!72qQW'!FpVmLJP-uFf2G`_;cV5O]=u+6pRa:c4bBkXPD1PSE&ae
%'%do")d%NRg$sT<4]kO78WE9!`,3do72`PqOX0EBdQGNP@>V"O?<mp4LSTei2C+a<7KDR'kHH!^c$U*[[deD^<KYCh`cMZj?@X19
%e%G`n3G&D\/f'FZqS!e01!V*/k382P57`5W/4)hG*\f]($bl4#_#r3IY`+g"?PoQm6"a65"h'`(R`n&2dZad<4%K%.7R1cRVA)j;
%Qk>[qe8mgS[;@1I4Bg(#YEk_bLc5[d3)bj9oDkU;3K)5^Fa>j&[kHX"_"V\Y?^jiKIcU_=:H@.Ld9_<^(%(-]ATh:b(Yg1.#Wi`E
%Nd<?ZXWTM%i^lr:1b_g"d2X#ZOoh/2rj<bMGGSpqlZBhQ3%/&)@cgE`/VB&X8pbC_:X0JpRQ3,]`?,HEn@)4FT]@bT]6%UJLO$8G
%6DJ;X'@_Cg,h^\'K5BBe1E5k8=pmn"=[Bcj3o7D7aurkSXW8b6OAKJ'P@d,EK7`*^de<I:/Y\t+*X`\cHDG/7e5WIR-<J..O0OA&
%@kGRVCBQm]a;)cL6lmZ:<>AMqV#;c]%u4?4<J9N>*ZAbJWGi/\1kYN?'#@Uk6>lOR4pW[>7@PEhr-hV&)$u%53ruuL3omsCK>mTo
%ULF$iYedLt66Oj?l9mCF"n)GR8t1Crm,:uS;O;e0-aMObHet?rF$t^Z'!)8Y-Thj9JWDFU04_/4:.jisoj`m)Q3'WH>]T[G\>C4%
%^<ld?7aQ#l7u6KSA8TCtMG(Q^S_P-7pkNP+,iA?+7$H/Y]_sj"is&lj*aYF0[ES<?e/dmT+N$fV_(ArS[pcG9@/H8PB4*%,[=`5@
%$?c_InR+cbOFE-V0d.ouYu&p8ZAm?+Wo^Ap#q,_Fc#e[MV'%F>N)Kk>'N7'?qaE'/#`O>SCeTp,Q"p-BL;c2Nc]&:J@-/Y*G$r0c
%j5Vs\-@7DTf\oYp<hNEk*lZ&\!:k>&l(DlL=b>[L0tgl_<mj+dR5P1_i)%Gq`%@2eakH!B3[s=t6guOPa!NBUA$'^/:WWp,Y5-b=
%ELX4(\P4GVj[#U8d<H0e33+HkhNmeB`7B=a]Bj@T)mVtc*54&Q0c3pOMnJ#g%MIt_qIaF&aN>i$q;&'Q+6qu^q_Vgt_@+SOff:sR
%EnuYM8%pG!c0XU]iLoJ05SZrWAT1l")P/8GqLn0u++),M?c)n*l+%8!?=r^N@9l)8kYWAbR_Y##lAHRtUur+,ZL-MRmM++.Fc_HR
%4i@oR"VUTSTZ'kC`_mF-8%g:'eMH0PJJ-)Ye:?8<f`/fP'=L>Ki@\bMn[+g?IagcS[0n_V&/JX.k-`GFKC*&G#l>i!-Pt9,1L7=.
%/_rS2SN+/dTZB5QV08eI_B%;onV@H8I*qQ+2q#s^h+/SE:;e2P,i`j/n+t7r:\D[b&PFN!@8de-Fm<;$K9j)\+#!R&X%S+3BGun/
%=h(45WFG&_<R-HRE]$3c78Q!2;qh?Ubs)*hL'jbZg<5OG3YI&CkA27=S6&m8@d(Ma,\p5p:J2]D`D'\"[@V;8/A4Efif=pe@(^Bh
%_Pjpl98HV*WLREaDmkLr9WXe:$#N;!^boMl(jZST+V_,T$^9ZTWb(qWe"&p-Y#<c-LIa:!O*kPs9:C[R1W,7"8#psR#s(;l.+VW?
%g%?eh+TK[T"9:gOl])>GSc(,:&^lkdaTp1+Wm*8.;0FElnOc1YA=8PZ=^T,ja>464OjUYq1FtVOAQ7b2cWcG:iFl;n,@@T.-&4]4
%_H4s7I7-2bS(j'd5A!:2"/"Qp5NhS,O'Of%;;O?nFi=X>O+@%/LNhrQLam%c11ENDEiLIA&/RQ4U,2U2GOlV^RutHj%?a7lLk!h[
%\rMZQ==EmCAL1ch6U("u,q,?2/4!%CQXFh(kp5Z%<q_Lf?el'18kAW,ah;=g'$aHn3J<8Ve223ket@&-<Y('74>[%d>O/&N<T'lE
%rp5)^TTS>?;KpAKElX#D]EH4*hV:ig2)^qGE#jRlO]uEfh]"B?;Tc'%j)uqia*Ks%C1WmjP)&._('uC]3Yat(H+DdFa;=).Mt:aZ
%TCnGpob"#^pGjR=d+'P@1O8;/5qR3(.Z\QF#D9i(A<o]jK&eK0UZDeGr#p<n>oS\_(-%@:I`Xi1YA!XLaW=`c4]ifAJT0H/QSBYh
%k.tfYMel#o!>9$6Ih%B&]bli,FsR,Ohr;00K\GhfJo+MO?;+G<gK5>e6WFhESTTa^*M"$1nHjj\Ws,Lkk6Lna$JX6QnTPKD,,8pf
%!Q%Yq6?f.>"LK@miuKf(F%W7SaI\4))`54gWUVMJ(LG2k.)6u=-.:*d4dti:V.M&'IalSH0[n(_"UWZ"mp7['77`0h)Rl2,Zf&!G
%2ii`q$D/m%c$\\L.K1;amqUge\!G&*m\rL<KIMC*ZK`u:gYHD]jFdjJ9o$)],;Y258<apIL.TPInXJ@#YR.hP]RPHDHO1LM4&H)'
%)QpQ^dkYDj!d@+g)*6SJ&H]a#$2=NWS3?DM!_mQ,%T$1CK1(`,2Y=;g2;8QOD=h;CC9!mT8^`M6Hq?'=Y[^o,*9UBpPTD(D'2=&[
%cN7iP8j2#7CnlQdKH#n#@cXm(6(PXQplI`[Tn[#mVk5:aC>k:?Y60$,%,@]cHPkA[jef6CemW$r"Eu7fNc(arW2o!Da-)UOn<O.@
%+s\ID:jbI/B)LA?,Ma;L301b?n!BEfgEQc3:4jdYo^AB)D&(M*4,-5DJ6*'FnfrJT_NH5fbM9RNjVG$"N<Jq--2<qp'AQsHH<.s7
%0oFjS57-1oWDQ8$3Q3+[jK#[&XDQKS3D7m)ob5mh,"QkbTQaLAN^.'s+Zk8K1>8uZ!c3U-e88W!aS^LH/;H!tYZ!(%7eN^rj$%2T
%<BfKaml\L?Eh@T(,aTPi*n1uQ2f@E<e2X+JXK7J*Tn&o9bTL&5cR[RC?2LLRB$l[5a!O](e.F6,Jbr367l.*\,A_K@+Z6RVdVhEl
%,^,f>S:!#J$Y%0;8p'klp8o&U<^ecg?=ZOe2^?9eG/9(^Gh&8O0<<q5XLA=;PCu+RJgE+0Vpg\YV+HtGIjoFX:M9iZ<;2'H\ZJg4
%+Kh?FD8BEgUbZ<Z_TVk9Y[;I01-_Qj]g+(F0O7Oo'VYqBOXh&]9LWXuclMRO*hThIGYH@Zrj\rsX(7MufdPn:"5I45l.Fd9JF;rg
%OX5q;-.QPVDE9SgN/Hef?XBgle)!O=TZe.CS5,bjAf9Sss.#TH5-(;oVHl,4r2+:RFb;O?Z.io>.EfGJEo&N@0$2k3i^#u5;rq#t
%qm5S!!S2$jJ)G;HikB^c>0U*\af!kQqDP226$Cq05?+>>3$-k5C_6XQ'2Wt2Bo29[/NV,*h+D>P#Lt(0e@m&Z/?K'RHaF/:M0Z-J
%Oh\+&^@N8XHpEr!O^3Mt(MXsB,AR3.A.WEUk>F\#K5!7Aqk+oQ\7Ok7m<K?>_ZQ%9Kd1o<C9D(FK&CUH"p==bD,tm5d\GRaR;PQA
%hCnH%"O1e?N1Jid'FFKXM^m(G`\9e^,)RVsfa`ld];/-8-_/Q.roeM(FT%YM4.&:g_'gV#H=Rq<VH]F&HAl-0cLh#S#!_9AfnT''
%h:._lF=D#rJr,FeB=r9sDCr48>g72gYY#tSX=&\8KKKIt]D`4#/hr7T)1=5l)fV;'nI#q2PQg2jYu/Rg>dUC!.MhH"g.tsZEdTrG
%E6ZUhE\gqN*uc_Kba3ne-p:9<Y=XC,:f13gi?ol9N^p=T-8k*77j_IV$gdZVmZ",G98Fb!/c3VZmBj;XekNa__2tR:Og(cC*,Asf
%YjD),T`_(tMe#qe,#hMaJ?\JsNo,,9*HA9(NRR9<,_F$Bme"(B(p/Th13iI;WuVp,pfOYsUm2O_KW$M[5Ya^h,#Q,5^d!^8\'kD>
%&QGQE3*"HRqUgjn]kf5>[T?27N5BpK3'[/6O$bB3T(ka^TIF$"au=@YO)[UJ*TUZ7]C04XBCdmNQbA:nBUTO77ER2f<.F?B]XZau
%917_6aP&D/9KVp,Ih"PQ(QcF=F'Q#X^_;'BL.Lf+>Rj6_&AH0Np4MBCp;_g;O-b[;3UC%#?+2cAaL^NcW74aK92BB*UU9AAjWihi
%Q"1qEj;IGEhLS`j-R.6HNbaN=aNGOU;1;SAmg#Hl7@L*f"OXfd.Ii=7At\#kU_^^`Y9,9*10)62:eHhdWK!Fk7X/B]2pp['&u3nC
%O7-`>*RTV<&QD6H^W0KjLK?LR)m8b>l3Ee]V2'k"7N+92DdlKDp5(;$fP,>FC_h%H,;*PN>`!h`m9[r7F/,&"Y7/JF(LGZcN(4;8
%/l1t;l?VngI%_8UT7!=A*DH3KRfL#oK_qUWG]jV%5o5Eh4lC0G]85Vbb>NWN1@(].mB3s6M<a.VG_a`DR!s$\?[_f6FIAnW*dH;m
%f`2Ur:)2rr;,M1"NZ4dL/\us&,HsP-.?5F9drcjS;LO3QZr-K+)4"&P?Fb>+3Dn:MhE4PJkjB'4%TiaXhVZO11#o]0Mi/CGrFtrH
%&)Wt6l\jc4,O7-R>tZL0->j"T_JkB`H;Id#qo[PULd,!u.Dn`Nnn*cL9gPAk<+JsH$]WU*OsE57al?7Oebq4!CYU(TC>o5j#M#nu
%It=DNa<FL\:eGk90m[deWU/[P?:+Gr#e71sOg?/M):n#Xm?XY$>e+HrR*b?=F0/3F>51`3?&nJ%F_%7KBC9^bM`XRlA3@Auhl]'9
%NHY3GHfE<`i(r!UKgR"I0>(.kX^t1b0=k5Qe"qJmDN#PMS,@A[*;dWqcg[lD@X$?><AL/(87`5h8pBkO@1LA("c"(n!1MX=C3]o!
%/d<Ec^"l)f;3,B3=Z()+?RR)Bp\7UJId/(U]jCsaAmSFg4X*3ob9-Oo<:n=N>IM+D<QL1X&0>>dU'Y:thKWl=M=Yso=4,@kqurWt
%lBlWm)'RWUlbRl2%?S:b?"L+78t#Z='),.d?ZiOZNaN=/^7o,9Yj$^=,m8Q>%]"\o$Dm%h"W%fS!)C\fDrq<ne5_V#?@2qL87cUP
%<Zi`=7'ln1Vr1rV-kKmgNm?M"0\$mHHA!2.Hn6LsAL/<LWA&2J0@2Q,QG[5'!]n-"<WbZ>>t&@U/=c%IaC#=><D'K.*g3b:^nK[K
%DU$OghLuk__30N&.4MR/e\nTNi(_&RPIefqLmQ'52T?<:4dC[&Q9l>AC0_s[3VYpYr_//6A$kd;3[1N>l\[L(00S1mg^U':a!=Oh
%mL*$>`@uS)=]hB7bLQrJaI7eMeD&d-3#&3!WN01Rot#/:ACeigorY$M#H/5%U$Lo7pCr<+m1BIaKO-HC[09MP`RYIf<[H[n8)BD8
%-0Sb4a6Jl`<m'aSQc-L#r7-;q]0H8`J,QSNr*TKIn,N5+s7Ef#o,iI2rl#uZfC/aus7sG0p4*#)DuSMKs7(U<mE`QIn%\n?n,MnW
%rr2A9s$-M;T76^u5Q9K2r9qf>J+[1JprB$,<<N0lTDmunTE"Zss7l?'pE7dDr;C""fg#MKrpX"hrV(g?Zi:Z<0>FahVr.L>p<.r:
%Y+'q?+7rq>K8,$QTDo_fQiCO3DuB";peUq\N;VkRqt`7$r;2!=5EJV<O,s5-lQ^m(XhDdl=`kjlCg_C+<']Ji@bk4VfQ)r*<]@6[
%LM-FY/('J'bu(6_LD^IF_9(P4(mS<(*G#ppp-)H7GLk'%OR<&;Nf4<di-I(bp7t0/m+`UYnMo,oYe7;a>_<5J;EE&86+F4TR:q)8
%dB+0Kk0&`.'$#;$7VD7LO?QsiL.7j3,a<Y3j5&c^U-+'B+AkWn1*1'Qi=B1MFeUT<0hS8]*5\N@1.fJN2PV+P10!3:A)cN#?Y+q:
%8N8;][)"KRN2TMAJe[=Z^e:eARf\p^+.,*OpUhO]>9F5N]^duhjTYgaaD4rskb4dkJ;NBA:.Lp*la(`5%,!4`M-rSn1K#htd$<T;
%&KAWM</^f)oI.'u5W]W'Jl$Hq,[K*A%Pek,Fl(0q7+BD!;^=0;O#*&O'XW2]5hakf9F)'d?Z0_m>f)gqZr<!p,o"tbG0R7>.<^k:
%Lp[MI:-3lZT>E5+,/hZ)I;5FB@>DL9G2f6O0`kqp%=@._!=7E@"4=Wup@GR)=%7$JriKt`@+nVWJ:4HJ?"+t>2cTYlCtoMZ&?NY/
%#8l'$<aS.6W>8_UlT"DAS)Q@DPIP1b!3^#3IHe"^AuKjf8_E$8H]2D\i[a@`fA[YS6/_Y6mb'2.@XKW<[A-]-A%mP[ZS3hTA>K-p
%`uX<g_0U@]-4S>?+3h38bpS55N90\I$NWi%;%Y0`NLlo>%`0E17j*\+6@rV01H]<WgNN9BFORB5$3mI0:M/o+W82#IF3H6;)g-Z"
%XuPCaNgCY(/QEYti56kZ;Q)t/,@m4obqW&&2!"aTf+hL$YldB6/2`/4"KFY$MS+)(@RqZ-JG1XXZug(W&__>f89TPS'%o"&rRo>G
%cq"<=K]G,RCZsfAA.#Pj3Yg"$ncN;a:O!JDTtW3[;b8L:ksgaV(#j<O1t=V?a+r`p6/!<8ZV(6+2iLbWWOP97/mLml5SYt9"nF9D
%1b+gL69@LRllePI%P)-gC.G>\NZA2q'fj#U(ZhuS92ddOFKofdDfoOiLEK"1$UFbrD+8ZRDT=BX)^[,m@c>j`A%:CJcBr`h&MqQ.
%I%m7NY!/QiJ#+]O5HOh*fMjt&9I*^a#,nMH*i^^.HefVC.-=!7]&gPRj/MtPU65+S3NB"WH[#DfK'2_E4uToAW-po7#8HUf@oN-s
%#LY<N-0,O#PL?:A+_f,GL9X5_3ssJ,&,&lQ"`IsHjDhg1@hi9_>QTcB%b`<.j*n)/!/;m[c$a%!AXf<<A4d$<"4Ws>e5H\$d7`'<
%AN"?S=f^edpM)$:WuGE9qe_fI47m=9ogDKS77E3#=XFYi]@##gaJRj?isG9Q"imKJGD%6CAb=.]IM5[l<k.UQE)B9sI"G_LML)%k
%PXi61B@0J!FfLuMdPau4cQM7.e^9Ka+_@n2i3MQ]HD.:qPm7(RA)'epi^>dN@:i8%W`mi%,9fUa;eHC.l$+JlNU)LoTk$/$ciK"8
%3?3"DZhofg7mYb2$:N=PcQ"<T$V9<0jP+W1TPb`B9^;^-dXM+#]-b/F:0e`n-n5p5+Z`D1/mYV&F]ITgn3pL<AKPM3_ZaYdS,<i#
%!9]9YOCL:r>aO!HZK;8"]HK"&.!,0%m9nA:#5;pl>1=PJS0MdL@$*7_((6ahadaJ%KLARZK@k53%5$`W!qcgt^iGTE=%-8R\hGX&
%&&86Q/)'-f3pcsJ\6$nN'7p7$F(4=H"a&]##t]k8p+13MD+4f=rbqDaGPef/k7n4*dcZq)"OMO.XrY("A',hh\o8_Fh#=G'T>QnA
%,hu#K,.Nja%V1KK"&1VY9U#I?CWcWU4/SL7^+Rj/@M>buoH[5KM<W_F5H)hfT*n2fS#QL1"H*Y,H1s..p8>DPAks6\P-.]>,"@'9
%T/fhWIMsU]UK*o\#L3gSeA%n*arI(D5CH"lNo>0?PX.o3!F;,]`YP!41!Gf($Q&<gKoSSr6XB\4.P?mheo"XqVQ=ilg;U%Mc*-NQ
%k)AR1.s$GbcJrW3ro_mXM^I.?J;LG-(,:64pr@gOl\RG/*^rKnH9]@^>^+k6;:NFu`$<[+R&uDerVr7IC`5b@:GTJBH;r6GQS]@D
%Y<2YeOEp.=?[77i3*>>Qo`FKOX!h\MZV2n]6T'e@]LM7Y!,$'>MgQLt*Zi+%.,r&q_XnF4&f`aA7Q1D[7M3AN^@X#)8Ka.A%!u2,
%;fpKQY?,pNgUci]f_4!WHL^7VC6Y'*P%>0mbB[hd_\G/Tgi,<G&Z!o!j"_5S8!=9"'Egt(6C_oD1F0Y]*e\>W&T,FSETg<GNi1p[
%Jom\7f.\Rl.8';1bO_+pg/Me]j_Va\b_0#WTLApri&9ROpI+EaNKABe`oPXNGaXG2(l(A&o_Obtdm?]!\r5nnhgn:?hUA8OerOH#
%TbLsNV5CtZNZZm6ejYWrleN2`;+'E'jBfEEM\XBp_>L,W9a:$dA2M?'5/eRrldqIa(iq;"54b5ebU&2iCB8.2aYQ!ImrC[bOL&ZR
%g=/k-V6?W.[=u#4*'?VG01@dbFQio[cf:;GOV.:\0p&"$VT4CK:+&*t-E)!a`3)K7fK#g`FJKk:Ug7jNaYI@2NkEah?7UfDUP2eX
%5Gc3HdRfo];H>EIYjh-DiFITt\uih9SgaE:&dTdp6R[Q5a`Y(/0kItJo/GIX1)7uqmV5n.KukTehj?2K<h`SaF[&./Y-co;A^9MS
%:OD?B$R/B]S8dbmEQ"5P.#X%Ckg5Ya-:HRr"Z>81,@J&5#8#UQ!k6_GY_4i@1(m3S\E'36*^"eV:Wdts<i,eJa1*+?Qme9/#*?XP
%(gntno+T_Cit8d$'!c1Se0?[@OV*r7f*JNaIt56#pbCmD-!iLq&)W>_rd=NoJ")`,18-Q!PHcC:\.VnAUEdQiI^p*t%i(*+M@Z:X
%S.3MEO"=DrdbLBbS]<q'8hXSX;>D2.ic/ITZk`FCUdb;<Wm&J<;n@7d[H0sA)2Q`+:sU]5\4c.FTF/qp>mLm\bHQQW\KJp'L<$K*
%_Cu7)YXfI@%+I)P_>ld+-+!Y+Vme]m.(gr!@RQWp_<DtK`D-Pu)S*@[_!4nLA`)Rr3O_A&J^#eJVIR5l'Sb7"3G7FMKZ@N]AAB$_
%1h<9tI[;TnPn1hgnK7=(*GXsaRH'Z[!*:Q"#dO,<h6j@1,X/UN5k\IcW1pWl\HLR8i+DbN>)p"q,6e.TG8p=^71@6g/HZF:3?ZY5
%P5#`(HreXB.ZS:ZR)_1gL]Xpn)t(EGI'TLfSf(stYYAfRIKfCt_RPqqdM45..*fA4#;g"$OC?bSLX(W$bS:.4K"1tk/F<NVV+5QV
%..,-e&'W*57ePTFj3NjW9.BVI@bBlt`&Vr-q[>jj?J#+T*au*EiiXg3Xa-WZ;:`cDW7r$UbfAo5^Al`H$%uBfXde,bBJ:22S1/\l
%#80?7O#FFq*6K?'Y&Y&$@33,g;oc7cU8b_W%;@^r3&KF_/TLDb`hIF['H>B![l:%\)N`Ys796Y,L]e@1\ubVV/7eup$WJ45iOB_4
%-<:FB&sP!d>sVrg+k[+j#d4Xqlph'4%B<5&2P`<?S/ud_pR7/#<dY'@n@9FQ?XgKF%)?QF0iEaS`Mi^q0TqpM,:kcb_i=cr\9*X:
%Z\/Ht'Je,;BINcsY95M6m/RFk/3E]L=X6Dsfp*jul%O+<Ps\sSp.LM-d$$g4>5pQqrpeC%57_`7rkJpL3'09sB&jjV'!Z<$2LJAI
%S)*VISVFQkN_AM.Lnm56gt0WV]X/fbke^G15C*XGg^/0T/(XMf/9YaW9,L,bDr]ZVWT140+tCU06nq2ch9>$>E)Er9Q[YrNUN*Vm
%,@AMf(qW1'Z.W=cY;HA)SdbGL.:aF2h32KCEKBhr#0ip)'d.PO8`?H<O?-bp4p8>Vd2n4X38(eLP/;$&5f&DT_)V6lo]q#5oSEG2
%n`-f\D=!U=rUf=i&%4AKU[s)^QGsiW3t#Wb(QAEd?*i77!(F,k8j9q,$$^/W53&eXAY;OKj8O\+0C<7/[K*3%%T^saH0m3h'*7/4
%_:hdnaPKl_5]fbMi>"=t3"=)jWtmVf1oX4Z<5.[&4^=*mg`eK]Wh.(Gg%#4nf/$.+_7HG5R:2,''Lh1'J_>)d4>Ko:ANR]<WM">Y
%Y+05"ChuR9jSHo&YpGB%%']au7<<]kID!tE%:Uq8986&RQp6b7*r9+;5&??jSWCd[jbBok@(Udu[R+jfG=;,pftL`tqsZgWJ#JoG
%&O_\k*B;DT>n<TG4e;kR[Z_XAZpeB3Opm]6paG3;K+tPqnKarD*/#O%e`,`50jCsl/,fVha\>8Mag>l[2?RU)SnN\0]b^M#"Lrh6
%@QQ!Q.%DXE'oggep>g"6m8^CtlrCl;lfQmP#^utE\%NX0a1qY>fDg8_kK*nUMF1/NF?;#tgOe:EN!.9GZDESXA[JL43@cZh=U<V`
%B&>ijeUi6]QLQ.-7.nt3?^=p&GXXA#=tf4BAXFP%YcjdmU^Cd;ZSjD<6S[lBDd8\s"DYRSGj7:W[sq*BBANb":h:;d"0&6j!M-1K
%qKqJOjBOEITX)^s!DFh_Gmq16iu0^m<eTMfj'%BB0&"'4p]Qaf"o>73lusT2Fs"_F7;Y+k;]Z>]kf)RW[t^kZ>alhc(&7a()),R6
%f0YprDMXH=/c\]LnoL!l(nK&P%R:7sPI_1F&.OjH]$=S5&0&d=0:D-Mm]OX\eOK\BbhH\k6hS!J=bJ3!9O.]Adu']91BEhAYf.P9
%TTbN*.t%:3qZXTr\HlLten/+ni)57j)%"EuXngkXhH*^,&O2RPG9;$nhZT6Ne?-GO"IRu;q>JDS0]0C846(2!,C5#;BLm6kn:UH6
%!W]8,Z.3u,oZZo(ptZ7`MhZMu\T/Jd09sTnVEW['/23Et?h%Xrg[WHd78Ha@V=2G=k%X8s@'dT$fS[BeEr#I@iD$J*a@VCQ?FIMa
%S-B`gfXQ!n#%,u475j?9VM<DNmFdM\c>N?M>l?X/%%3`+%NAZ6TZ4#VPl%.AbT7S&Ug?Bo'WU//T6lMkH*8la'VG6`pg)#`_*$45
%\oQEkN"nq\ZomKJp`0b:.>.u=OC6./P4_OFjn@OsEU;Xc+?PlK7_lHu9:6>sZC&4q#Lc"J%?QgEBaHH*%"$XSb-7%cF#W"89XcWq
%'P&FNb!NF(Q1C(oR#.U)UP57_48^Qhbl3PHn-A\WCT>M'81,GpH^8b?LZjJ6+_-oK+5o,7P7D6I.Q*/@(trA!)_K;b')l^Ui1S*q
%e2@U?s$!CNr9q+ZHVEk-q80s:ike@9j:t/3FOBjd\1n&j6$fjd044>A7+5-L,DH5"HKkE<%Z(G3"(a$,Pt`d#ldLYB"%MT!_^L<]
%>>KFe"18MAbE'AsJd+`-7KDOW3O+frpp1+S5;9qALSb8pM&M]$0hEN5ncULJb(aXkpCo@#/g2AFQG+Z2?mKrN2Mm>a4+QML:T<[%
%Ok$5^Ru0CU#,r!H]d:<[Da@Z?mcdS5pVZ8+G5!I`F;J,8ENR/R8^?;g@&k7.H>J:`qs\g,QS&u:X8<2JY6;5([Qbt`1-SIr;3k"o
%W&Ut)68P4"?*YA.>Pj5b?Qog6dojipKG2A9ZU_1q.ZG8`N9Y8l8arQl..]k$1sbo#j2]>njjo]?Qo_#G\PD,r.%245,\kQOJNj=.
%D*mIM<@%[#>E^e&VZ<H(bI8@)/=OL\`>!L&]s[[#<5QVrge<0O\oi;$@(G=.e-WA]qQos57MJ&2B(CMZB8>O<21+^f\>[^9=pE)d
%hc5%1_QC/YfV#^Hg\3*289rbTZRffbL2oHZJa_XT&1]YM)9*lb2JRhq:U=EU8=^<@LHTYrE1?);#+3'5YY:lZ_m?8![lF&=Eg0Wt
%20ltBYle+.o.5C"D(qEc&uQ]AKZ,mTC@6nOZE9SE`s<Xk1%5%FFjl0Yd'6;[YY^"(CWR'8iQU+0JDteGC*[Wq$e&d3l;J+F<U'"9
%Y(IDcpeK5oagPo4T2fu$UD9QY@jAa9HL5\4^%N^JLq"U/_id9FN%0^XYaPuP$p;H@$oV0Yh8:YC#fZp@)o=!+0`m-\5"8WgggQ.M
%";uRc7(:Ts@hM8BU53&V[<WF2'Ym#%PEEo0oJh(=%,)bcN+[%dPta`"DqlCeT>&g*rY9c#49H10!6Hgp#86+KR0dZi%?DB0Vs0+1
%!j\O5<$H0d[fugWnOFWrJ0;;N8A,p2FRLa<]U@QeWYD47Mf+lb&Y'cuaP^#8F7b`T#9.&m#>YV8:I_V^rV+&6DSN5krQdIQNQ:o3
%6l(]CqH\cjX?ajM*V)#lkoJc-16ujq"/=q*q_C3CO7cYK*n$ABmN0uYK"l02k;O>K7Lp`c$$-4mr&<`qHi,nd,A<T9ks+Z$8ZlS_
%*T%n]!CuX-!AaSi!+8G+#!X`_kVWoX<W=hBrt%O35?Qh^OPH1-lS]8L(kmNON@_DRE_Tsi?;Y35M*2JCZu+Pq8LGcAZaf53&C(_k
%']qq>CE6$/e-F?.810l4)eos1d(gjtJCYR@!?EV'hET$r9.rn9:?e":Vf-/<!>5s5JmntP!jtc'W`>s:3g(^hiL,(npgqH_nqfq/
%%m+pISZsJ"')rrjLRIXCeUgT?o!f@L7kV;j787_-d'jY=[!S[:aWa.jirZ0U`*rl4.Nrp_5rR!)$-%&EU5&!?!7hQe8Y=,\on-n<
%#nI*?E0:&42.![I!gY4>+6dcIHjh!#.gRoV#kV@2bTb43+b"Xcn?+7GPdeXe!sh1YWs3U/\0hf+73!TO^S81/9+==jiR+3iN>1eY
%4.M-r\IjTqDt,)\>4jgFN=k-Na!+),Kpc*`+7EV)4k?&oWfiU.mimcle+bm7<XC)"I86uq=^^\ufGhuW9*fR:3U-tY7@Q.&.8"nr
%)?B)ddccdYQp0Z@E#pdS_YF::glBA%fKA6r:dk5tdV;M46q[Q[6moU?kY(ISOpRYh6@drS;Yjj>/UeZ]D7F1NQ3D'%j%I\Qm/TIG
%G7):p%=k>"*AGX9Xb4#04<=K=\O,8m*2<k;817YB*Ps)[&.SbU/#cjDbV7YbZg7SQbreL0_PdJpG"E=,)4MNMU)Z8b$7Ou$OJ:M3
%jM=8B%(_1T,k).qNHU3IV9dLTN#0=i0X(O<K\U)D$G3TL2(sr$Z6>8odLqKI)5O51%FLqK`&Yuc#gSeMJlFMkRlLP^LoJnC;W^bh
%5Q($3;S0i!#R%J*]VZ7G%SN'Sr[Y$HHF>'CB8adLO/4bs&Dhnpi!-2NY@n`+I?Vs5MIj\eKc-i_3eFdP7LCqj=E75bKZOEP4bC%H
%q-.ltD<a-R1pWs?0C^as4tW*-hY<D,#$/)k2s/F3cY3&8el6+1if*O$0^gs=$2?XfIufZ?Ea\PXo5fdV#N&k,COZ01P(]AHDd"P%
%lG\oa"!p`tGiPfY:WfQZ7#=rRR(3BX5+ra21Q3A6Q$co4E4AF]S,*o($W'h;BH#0'_Dp'[5fLONXW5mIBM=E-(-3nsEJk;i:OtZV
%F=W\G'uAF1)nVjJa2#p=*`<pi\]KK>r64A8P+)##,[=eT[s/%MrUM?K9,[iRDCrHU=Uc:YMT=X[<9"+8)>=C'F4IlG.;a\"j7$DR
%L^'k3FgW/tSULrN3n."-IEh-e]1ot@%i1TIb"FjV6P!)g'oKoQFie;3F22Cb.^A&teEJ_W#OE&2?r1)eCRhms`!-A#phQM^b1!#C
%iI9('(t<!_JHDdDf8!GSNH1M5X(Z%ECjlStC(RLej/YE1o'49^b1(8k)7JQGHHA^dPi)9<p#O8-*FgiI.!&I)+H+?[0]0:jS+--f
%\t]9u#bdD;aa]/F%0]\ra.4DC33']1h8%+f8-G<V6/V"_Sa;gd'W8XYL$Bbq^[J"d"#Em*Qnf+AOY<To!3hoOo/[(KN[CuFphN+5
%pU(,`l7kY7^k&9N"$uGkrV.SX1qPT+]rjq`[#`&7$nVmA5\d4&B?8B_(IA^fUY]43#J7A"EV/8&R@hkZ/ii.u=n3:Y7q1#F><UDQ
%S#3o"9A2p',ACi-poL&p';Fq=M8c(bF4bNB(m3[q%fYs%jM_XL(+;.e4t7TE/f:(qM]L6ak`rX7L,IVA+*ca;31-Hdbnak]U*H[L
%$Q5Vi6XhlA#8g`KmOX$7[+5jGeXtKKV?7kqI!]6?P`B7#K#-5qOstPo$<hM(\r,7mUo^4Ra!qE[nG*@>*.*^7Xs7];XLrDu1hT'#
%]i'_FVK!W2-buI2j!9/^HtDrG,9c]#g=95U4J5H2*`TBM.bSP^A%VBGO;0GG`H^:@\8nV4mn7W#%N/cjHB/OqXB[^e3Pmn-!&n$N
%V[?geL^(S@,E)'O?RfF5^Q-F7+9q[D;&PMAqd2d'1lt`:kPrO!2>PYuQel6Nnm<r<*rjMij8WU4rT,W,D^7Gb;f)a(TLf?oYVMeH
%.kKtL6Xi=&68VTff)_>fC7jF]V5Wj;69kg,b@?bi$]eckkRJ^A+cTJb4UC?75sbAtfY6SUL+%9Ah+\nkl*VWW,oRdM@ZEK_O-g4$
%hkS=B>6';[#2o4#&9X/7EB#3eMH*rejb9i]G]98?8mdu?=/ig,g\."L1'BS%&Sn"LQ.H+7iVfY_m$.[C1HPS&]Xjktd@-:R7Ru]>
%EpX=-$$BV\X=3$F9D7V9i;aB2mUR1R7OG2lEb5#8qcP/L!,"\A02m?1^Me2.0!"Y*!eG%Q4*E-l0UeKN=l\$X!lFZh0ldEX.O1'X
%QU>V#ZB(WO:ID"[qZZa'rV]&aC)65\mkM`+=/,Dr5+PP>pm`US@mBQ]Tc`/u*J;.TeV'Q)Tu^5FO'W34?U/(DQ#7b-moIhtlgO@b
%1L`YqTR3r8$k\cS,*JqE"rN7nheMF)94(utWD1pJ&QucH:=Wb]R%3omPQuMj:<K9#9R!7jIp+i)5W!4ccRk[PJJY,d@#Kp/a#G*:
%_ZD(beYn/`>g4jZ]>b-9>laS6eps`O$s9Lg'eIum%X&J;].F8a9Xi<@SPC-kGK'BR*\nd?H"MnFoX>(YT?lcu[t1;4P%6$6+G3+W
%;7cm?I**ZJ,\RVBO?6.V8r.5<aUdoY\X'e^IKhmu^;$VuJX!o'8dV*E*QkKJ[c`U0X@u^/96&6Z;>k@3g5q`8-dRR81JSNtQ-7n3
%>qu\"Wb@l!eu!&?65kJ@#$D?S]"C#LTuK0>m;5AZNI0&M:,=[,i1ZUYc-UUI,>c)PZ'VrsV].:uWJ=<Eqe)kOLF%=H!?LthMATd<
%mB-Nm`<:nNkh()r^PFA-[^'49n28<*[O2)5WkqP;))/XO#fO9,b?tJDG\:T9P*6LO0hZs$C?P*,DeG<@U<=s`Y?>%94Dag%=ib5>
%Ldok_Yr5sUT9`B.?P=O.Rt'5:9IR]^XdpHIi?!FR0N&CbW<F^IYaP^2c"9;1dL>i>AaLt5/o'99iW*.bZh4Jn0]H=3dQ>4*=rDVb
%8X9V==:-SR`iR*)r.<^t=:_.qk:0]R.1gVpe&W;9Xg4M^9CU&g./R2>(e,2;m-2eg!Blr(=Xjf*"F*49l'A:"3tUe]F@eKtbR/]1
%=nGt`#'K?W(E<`gq]_\d=@YW*BaTDd:n*N")UsDhSs!SM%7.f`d'7cYQloV][!3O04gN?+2F.f<&<O*2?qo(!+/`PU>*.5cjhEYL
%8CDIUiMepHqst.ZLV:/0N!%Z6(J@)YE(DOA2#85I+D+tj"B"+KSR&L6-0d,TJu`_i_W`mpn%VrlS_!R7d]Sa-&Bs%%jkomq"I=NL
%99HCt3jjI!IJLSm"g/^@iV&>Z59".bHN&hndB`G."K1Hi49"tSqD]2q3M',&Q6Dq'\G&?_CZnHJj[u3Jp!usQK]bf4`0m2^-[PrO
%C_4itYJchd!pCKOr<25a.5;fegjN;sNfB&Q/i?1W`-2AU]X;?D'$99=Z,s)GA6?2_/OUR(F9X;DLNEb\N/%XGW_b/VRsl$0c\Wh0
%%V2F5Ne^>WGT7\i>uc)rI\H'*)>ZBj?C0^d8MBnKJ6dJ4@*h;Me[cd29;klng5TQd2Q;:$:!&S[6YJ[D$:14XdNQU\89M+.a'tQP
%-A2ssdT?BgeB+T,!81a3j7]L@l<L_oN!cNHc1<s4%Qf#<A[GQXbo6dTea*f^P_!bM^!@Vj'1tb>l179>5kChd)5(H:UfgM.kV;%7
%.g/&S-&S$>\kChLBDj\^oNJ6=`nON^dlWm5(;nfPpS8@_QVDD'SCEP72.j/[R\"N#8+Bcl\6uR^lYSPGTnUP@jpclr!,k4SKFRO!
%"A^=j@#h^LBoT@GK[PF6EJ4)O'Z<_DaH/(_=^]*KX@7j!Y.[atG`d5+0K1UG:E*[niaf'`Gi`@TNqlP/d+,lqBTXorYUPAcc7ga*
%^:RPRT&n<bVV2+2$%"A0Z8j6h;!.EDI2qD^`YO&>#7;PbEsgh;Ud@rsbnM6c_)[H1<sglKF!HYC```4F62SB9]:]ibHtfkWXP7<0
%o.khXnVXQN!do@5LPPq;o[hTCA,basd9*"K(j/_MP>G,<GXstiq[WfqCdEhrlcYD_kgYE[.bm4QEt9cI]V2bZHq7L1rY!ja!$B!F
%R$'oRh)?RkQ?@HY=*2sIiTcVPd-IYHitH^d]4r;'TYhl"nf64Uiu]L;4VtER%pA0)p*Q./=9ee)`7'rM%HgFs7iH%8BWV&V_;Pi*
%#/jm$hB6K`mcK0`P'X.(iSjiAM6=&P"=.i[]q$(r:9\dX.hTIVXYseSOS((-5fK[UpXfa*Y6<Qb:Q$/A3JX0_Q=Q=2n%#XUAn>mk
%FlTCQ`=jlV'#.pX`oJ[JPB_bP?Y9U-U6:/*S!E86n[jFUP-U&DJlX1DlR=P\g%Ps;=uFo5:csnfQ)msYT[9#J8+Sd+d0MOq+g<,I
%RXcNR`255o<Cs;&4%bV@=GN9$r%WBKF2uV25JQtRloo2WARi)`I;Dh%R;!V[k)IN[oJQVnSa:0*X!SuDr2JW&KoI':Hul4cq)m^:
%*L/Sa_Y\O`1&B(ac>VK7a;PAF:U[\;J(V$J7NDIfe>$5,WG8;hKR*0b&`!\\&"(5Z@u3b\g-Yr#2"MBt8/2UTJ]KDB+H$%Po5rk5
%Gme9u-'[WVLqF<hleoW/)Q0&5J'M_:W]Z6KafsRV!BFX54V(PDRr+Xf2Y=eg)n[Wh^e-'DNFuoOURH"[RTc/>Alt_WH_,"s6jP]p
%CZlAK/N$FB!7*>FE]C)+#!IR7X49]pj]2u%<GWqKO@LG\!lY2K6[P2]8\3-IX@H)k&rk@Y2FYkWiuF1dK3eFpjSla.^E*=N_tOQ(
%"D(;OJ^hcB_+n[Nm!t8u*-9"/Kl=V>4P%5,>rO)m5&V*-<mGR/)$%u:@rAEU4GN`I0`qmoOl*b1S_<824,outR]unqdI;:*JJ"\G
%,;gjslR!>b=//aV\SfO)bOo[cCL$n*9>G^qSaIiM8FEGEIpWnB89B7!0!f4OqKYTfr@Y>%go`b/CgCoAY(W3eGn4q[QK$oIn337/
%6NlE_"UC1BmOO6"01<Qn1H3^1I!UaS(^6\?M4@(.+5p#nnsoO$'1\&KH-Ap3CE%?@PI&*m+cqH<+NM((_aS*Lq^C&gojA-Z5\frQ
%LMPXrQY\IQ&fuu;;6(F*O&`K:N9VV#=@t,Y"Ke=L4.ShXOeIUR4!E.-L%d7@NIVj'Dm@[UJBU/G;liSE<K15MnH85VqjWjA2$.qp
%C$+G0UK?K$>g2uO[9VI/T8-sr@qdmuN*Y!8^[7pQ4%Fope"1D9[g8.Pd7tU=F'\gOhl)=YIOt!BJ_F'M"R]q6F`VTQb"21UrE*'R
%L>ZnDLBlih`<+VO/hX/n)Y<<9jgN2g.2&V6ljV(-0JY]g)NXA-^KrqDT+/*/C(H!;],I^F_L)/udI[>UmoF&SX\u4QZ&ugX)jM=l
%nMt;n"<c.$mIa"*q+:X!>7sIBnR6JOP-Z/NPC]bu)lfP0CcK\K-^%3;=sILLi0Q+_GX8SXh-.Q*X^^tVB8=N9M>"9p@mHg\@O0r?
%?K\eqpcMImh92,iS9N,KLB(h"Z)\-DhuN/p.j%B&BrNWBDS)@8TC4I\k`&[6L7k7>7TVQkWgul#hq_<'gV?W>s)k>F'Qgi)Bu;'o
%Opp3O0ClZh:<14!&Y&V,$do[A"r53rrZrrdCQ)X5\3oD@&O0<+2bGTdjj)_+(jkIPb"fF)jr-HinaBaFdfE#e;&X6Mq"MNc35>2=
%&%HDng.T]K<PsYWjpjMpC5dUh]e=XuX?n@UK<ID*7R[l<j*NR$,&0"eCT.bBjY/5Y`@_EB*KKD"8:^'*:fl_$8DJCpA5]XhC`tn/
%%_IpHmfU>#bsWK?%.5c*b.+@[2g==0(BI>DUSbX*j)\?;C!DX(MkHe59kQJR!._W1Tn$bR>tqlI->FBu;cml409V\MP:*P0d.jCD
%*_I]RJ!5;&<C;N;i1sh2R=V*[!b"^:IZ4B0F,^;OWD?n@4ZRl11<V$fBtoTaP+Jne.[dUM_".%X."B-C/^Fo:Z&SP[&!4/@JG]3D
%iD1;(TcN2T`5$g1mY'bpaR=2%*=dBGb22&3;F<A?oVRn/s#h3Bi9T4k,&s!6Q3B*H:!%Rg]LUc583(lSf8../ic[PoN3UJ#4grrT
%)7(YEj?n=1Ld[gs'4d@t+7D8"=Q?)G8o0AZ%Ib#J4F,/7/>t8JnrAfg)J$N%>#Nd:M]X(E[2m6+Sf6T187u*uGd:tLZ=h+&5fmBp
%k#q5W5'+\D<Je^*l1_naprM3[Aie0!NMdnu"-7)W-[aqoq9GPlg8e1m\.6W2Xcnn.0\^AD7IuC[cta\bZZXKb%^[,O7\3lB$5e^n
%:(4M.BTH6c]h.:i"qsm'cs3L5J>pYm&Y>)HDN@dL[;@$#+kBn39fau+:^l#m.[8,oOf[$>9+o)u$.Mq-6/nf=_#4_;=]qfhL-7dq
%![n>"kf\5'9*A97\mc^'=EiB"n%Uf(%&ms+Iu""o("(88f,[=h4jeWUC&h;SB4Qtei9dUlg):$]*FkXbX<h0`16V@WB@)EaIEtU!
%G#c2n-SN=J]eHFHH8KI()5^*R\1@-QeO8\%laf#QZk?\V"2)?$Ej7/XF3L&[,ijp<oE11/69dBI$JMRL`kkSs]GcKp1A/8T:eJ@S
%2`*a8:?27/$$jtO[V!r5/TmL*SC='hmsJ]0\*=q*)`&J@77RafZCRY9jql;DGXuS339Les1ZMS4,\3-:aTN2m#%KG1N>dRSVA+pY
%m&XTJ>E[C9,?P,j.F[/P"8HUis-s?2/'pf_k_8*hYq+LT._X,qN_+G^`JmH>[.\j*i%@nEX\YgpF%;U%Z/W\-9QE@gm_&cun9&^U
%`ZiKo,X5N&a2'rqNJ0MrBi!<&"CsB+_JnNT9C6rWK3s^/G^ai*d/+F%GjA4VEt\JKed'L03ZA!m5k+5P5/.-U,uhk5^P&9d^'hdc
%Ort`f.p2bMLEj<8*ZsT]ak))Cd<N(<:5&6N6Jj)&ed,/$7oC)/#&IhoLHtRp\R/]QX6!OR&N-3u9+c6'@J35Vl\jsu#p)4)^eXAL
%$#:6i9-Ns9M7BX;M!0;ITV,7K^`eKdb9.[m>6fOt5h>lJG&ebS4q,%N[EJ6(9mq\>n93Q$9n(ATRW:Ur)\Kh]?-3#V+S\uP'd?S=
%%;Ip\L$9i+K)+rmP8TO@<&0%S6.?F#3Sc;4o@mX4)*!2.I7AN!&,^g0-=[^\M)p=?"-DQY!sEZQs-b*eE7M"0L-pX;17ITs4D9k\
%^m)+gL+gqm$]7]A2Sb^&HrmBMRbX1P"S`UHftQ73Ja=QW:1+JK/::)</Xmm-*[/H*3:#eRp,<2Z0I*l/e0cb.em/r[nP*.F@&BcI
%":X]4F5?+)_?inZ9iB;;"D$MDGF8i=Cgs&_?7p?AM^7LRT492!)#QX-!^XS"`S,k^hARR$2_f=B0ln?QcL6\D4[M3NN^[AFKP`@P
%G$X[G%Fm2K&QrW%.Ms!uNr5[QasjS`)GdC]!$t]n(p1.6l_EW)O,"(3o8g44SD?D_S\*QpZ*:t@8/b?^Qb%ea47XUK`9:tKK[5&'
%*K_1o;5"A&)0NI1Ge9C98Cj1R=CqW5L2isX6*P3h7LkQ!i+]YE-W7&F)"0^+8eG&>"<!m-]t:ff>8g'683cVj>9obj8/MB?&[;@!
%6MT1WM[m2a&7?m7F\Q'&:?TjT';t0++\Rp&0uoMV2"EaBU#F*lQhG</7`(pL%QgjMOu&+J4r7+X/2K3P@nG>3BV.bY,.6]_W!gC9
%C31?82G'YHP[m0O5cutHA9%fq^l]8Z4=1'DP7<,I4=k^>%QPDF'(*P2a`Z9TN3GbS^!&^n3+E(84MU#@2"<OLB0V"6I8d;#*1'#l
%52G5_1hJ6Rm3[WAQ<';kH^QJEe$VjpognB6X=]9LOhrCjZT;3:(9o=L(lB?0j;MJ<Q83N\$OMMe*ULfEk(iPB/u-L\\QMDM#pj]q
%,DnYE)4j"B[0K()(>D0%C1%0i[5@amb*flr&8#gH3!IOVKV"SP$!`e,EYcr$<XEM,1B_K$^WJ3>;IaS_>m]T3]%8Al$X4n#7Zo3!
%_jZo.Km@Hm@.st[$Ho9`/>$b1NtJ=ID0qilT-n!8h_9NRm0"%CUFn.J>b5LIdcBmV</p6@NHM_)+_<Jc9D+"7G4p&?]-WZOAuMV*
%g+RRP':IYnS4,ko,@J-q9\=QpU*cR<M)$M`X*A]L\q^-)e9R`cN(@#<gd[Emd?F@%UGp^9375l2,f;)G#I@7$EnS>i%Q7sG!/`l[
%fu4\$,C`$"LQ^1seha[b'&:UDZ_FT+I)e"7g(JQ>FDTNHA**A%#EjTW2-FkonZ\LtmR5#)=cLm_c\C><i#u!sIuOI[5(d[F8W3r4
%Sq.gEVJ07370e5N;NG2F'ds5.457$Zl.1TKK3]3ISj95.RIN/3nbJUhL>1/f]Th?Z>28m!6PSck!>0RK@51:kj/)75;+!ZOiS*fF
%QF,eYm/"bh%)u<q^r8FTgJ!Jhk!9&]:,+M=O49b0$!HJ_lHWuo0ILB2Jh!L(MI'e5_iC9-6<2VE="^g5VJY<4:hQSY*0X5T*VW+-
%AsCdZIo;A)S2^Z4nd1\EG->ln)80pA'9>ru_M2@WB&)Z&U!acE6tAA$rZs)75K7t7_Ksn9fUu%G+Gi/*Kqka[UJqSgQ7]?_@AbIT
%40uI=Q:fdlL2,@qPm_Rg]`Th)Vmb1g:fLYuN%JDr`cDme)1MN3%_[@sU'j#VnbgT15E>8`mF^;ocp:`[2]E5gcMnIL.;`tBZb"+j
%qji'YL\6#5N,[g4FA-dlf+re7N.#=mh[KrE"%Hr._>ZNYIj(QCaT,$B!\mBbaXb3(.&gMkQ+<_GT8O+V?Xj6#pM$6_QjkSK%V&&?
%FX`*m[Qjb>4RFCB5$fc9R-RAdA>t%)G`mXs<;Age<)3fa56[`;]#>Cp#s7uV5Et(+Re3i'Y#dLP[>_MKYt?pCBoS(SaV.eSEKW)O
%8uYr_'drXs*`I#b]#6);$CP,rFI!M37PYao>t<e]K-TP`'I/OmDPR@PkdeTU>#;TpTUnEkA$9uD:!mod``r86Prp+\Yoi+@#G-i\
%&B<DPml]4:,dZOsPIKar*`doEX$=4gGO?Mmi%,&[aQE4:@267>(Y*)1is?0)J*!D:&_4jKiLCgQXFogcTfnu4IEO=5^E5pb;D&XX
%Ia,s0N?.`'o>a"D4C2NW7mJa'4$oB5#L>$pF7m[WLb]2d1L;:S9&Kr3<"_eACXnIV1l`[&eK%+<D>WFpa^Ht@UTrn"m;D=\r:]1S
%Y\(QpWk?L*W:>H$LO,?#<N\O/Geht#InWVd=JrEo2VQs#rY+Vl4%CA9/a2dC;BX%1rupa'YFA&-kng5Pb<#W>[9<V%pRFGFhqC7O
%9TF@F[dpD),Y(j'NK"@)Fp"NcDDQ9+mE%Dn8s">BA*tW\(\nm08ST:`F]G(_:f<R$0j3rea6&#ZDY#-9m+95iJ#g&C$_Y2R#i5/f
%4>LGfA/=YF[T/4*?+n8d&!Km4om$HQ"13CPpJ.nH&X(bAn]'p9T(I;/q[e!lRi23*Sag3dT4m!qR6b"[gd"qKLEGOYX2M6!hp[m'
%P.:A/Ur;s>&MjrQbnH:]P2.?ZO4VBD[[!74]eZa8L(_3#br]6,0JFA_/4^F3r-"?Sml04>%M6apd\[''X@%e&!VRon>l4OT1=7c8
%X>.R?R9W(HW"n'bF!WNckn2Ra)oh!SF^Mj8fXq,fc$bU:-c4mA+bp0(1A`Y#AfBu&$;0$HYd5;r,I7D-*j^!_^/BuH4fa%]?(QR^
%K`/Wr;)*US2II1i/6-JlGXQm9!@R,"J,]K#He0T;MCV?lIVpt?LSJS4=>AB,\9gVgKXXWb6PTmS%AGJ/*dsM9/1^;lfpmFlAekGr
%R,<@&YI1i(+sO?+;'f'POq;oiM<!Q5hnC1JB3b$XLcA.4FYpK@)Q1]B`lV>L-/i3<&d(jh1:*nQ\Roic#"/8r@']d67VJM^"1[D;
%Z%j!hE3\YQ1Gk)>V^5<<DW@OOC,5EKDcL0P5thp9XYMBB!N/jIM6c3Lbeu_31QK<:(_u\-G%@>%1te'q,JB\aDu*>p`V>*ZP@O\1
%Du;E/&Qf6B9rU<3.KET61T6OG5oiRij9!EP-*C9Gl0_RC$]kc\P'`W!@&Pkf9&@KMkg/p0OaP_$&tiW)D^6GA\J4:&lRjljCltFI
%Jm4?fH^YX-a[]GPi#F!bgs,I_MLW]HP)4V&LnQLn"r0K0-1m-7o4F)-D?1>LQO?VVH-)O>\D4MX1AJfS#MM-'Xi.<#\SLk).sTt+
%ZX<o7;"&Y0d='K)OKP?Q%FNQ$'MWZ_/^rntpR7Eg`Z$a!WE8.JQsKjU(o,U+G,tg2`"BRX[1u-*-%[(8ZqD<9IXuWL.]cr$%klkt
%XtB2M0$1usm=JIha:/2IbWf7[B%E8Yfr<*BPpYTPYa"ZKE,SalNT8Yb701Cq_)jH'%[JtA*Y)p7)V@bffpnnIl]"&4)V8tPU3Ek5
%775gPAp!?HHpljc6U_U-a;*Xo1B9s8_ZYTa/qLJg!SoRIZX&>sD]%PG.?eHr-)MHaeSKKJkI:4nG6m=3;7u*\c4$>l;uL7[M\.K@
%qZ[ZT0UJ8!N7OpMoPJ^*)b\nT`.0On-I^/WA$BG8-7KQ'Sa39gGWTr1qM>Y+aZ7=72L#=lZpR*8GrW56k/"D;6Eh*<$DX7I+Lt8O
%[!47^PLVTaC5.B"@B^)Sr$O$0=u2[:lp^!?",L1VPMEd+3>O^[P)G9o#u-&IpSn#0_\#n[1$+q2#jS:o.c!27O]0]^G/5q1%,/.P
%>VR5@_)*UV.0Lm79s=t?7M8CM9NBKn>X0(Vj"bbg$I7+7$`g2X)o$'L&bASk,7+H4bIApPM"mJLN,V<]aU7HrBqcEG3*9l$&iXVR
%B>H[8qauh<`'=IIaV@>Z2j1V:+A$2KlO%!W39N#SJ.*_oFn-j[=OIY1.PSuF6D85cN)9i(bc8J:e0a+Hn^<l=LJ8?>Z"*Yo&p5Wu
%hqBN#i;cQ"#-G0"D(d2s0QU.*dD9ciGd8\W2l16EU]mYUF:(nJ,'Kg,&gf:f&o`(X^%!I[.hT!aaooWdPD<_LH!"Ga#%<DI8dZi]
%j!UB,9XD`i6n".I;;Ap,#pd4$e58+A@:Dc"j<=nF'JgN?l&fQ.,>8G;^nXUYE-Mtj?N`Z3/><E22s0P<><t$8J,a+6d*PonpCTNg
%HDh.O\n\L!-:Y.\(:f0)/"GB;1@(<V/'M:.:AKKo\dQ"h8cb=s7q-!]ShUNZN;?f'A8Ff8f'CL'8NR(9MSm'!?H%P,<"OLVa122U
%o.\#9O<<nbJQQpq6SL2g<=(At/qWu:E(6Hmk615/!a8>D[JR8C0ep#%pcBAPQ/L_a'3s^?JC-2gZ"0_n/g"nOTN?AI__Y+$.!Vp?
%&VRj95il+l8QIjck"iV+GO)dnDI@D\M'UT!crH/6cUc7]deA&6I"RMLO@7c0JO;=mJ@+^*SLt.8MfT0,'?WbCcb_)%MS2mO#D;jA
%$J9!G9;JhGj]QQ[nSK<BqoOsj-hPbuG6<+jT5+GH1h4F^%+#g:P&=*Z4cS_,#c]QMK!SYknXrl)*s%d"N&m%9@=m&b,p+`V'YDZc
%&#V$p'1h3tTW+CpK?]tJL$"'(%SK9LW2+@q=b`2nl#<k5e>Q1m2BV7:.nbFis7Jk584kNhs%U19UTsp3MLpesFhqTLG/-XL#FHCX
%gLo'`$udssdP:KTVt5Pd]9JqX1,^"mBKJH9n-'&;*P-!A.utNBc4!3X>MrgtVW?]R<;.c?"[&NAbfct96!*RFU&S+_:#SIX"bVa6
%+CagG`g"SfEl&$sRDOsM8*&i#LA0(DO[u5p=RYo_L`JUd!goF-GUa56!XM>/KEX?GYq@2umug0feu$0pq)E\*Xul$*C!9G4$/%MM
%)q/I(-mJqd^ddY16(PeJ/OSqr*[\[DTD*?ho!?HR!F2$JP*<Ae+%A5('f>WTi+U"lL0R&,(Z+Z7*MP?,VN;J4p#I[a'Pf%7]3m!#
%qqYYsnjJ@STo`+,pQXUm`-k0Y1OA-K!5UrXFptURNar\UEf<2D9/1G+k5s.R`VabbPC_W.Sf^VAJQY&<m)TDU?q=J--pB<>bJX`Y
%h'L,W?n_T%*.uZt>"Cp*1$OYuF"^THR)G%.'kIL3qTc5kBs\@K$I>]/[US]KS`+*!k"o`X/!&?&pq5iIc?0nBJr%Xm\=/pUOLcF2
%M(We=*[Us]j>V(Eoc.A:eJD6mAg9Q;Vg$-FTlEU):)$YJ\B9,LRJ'_?3!KgWFk"J:f4VOMM#>a55qqXG,UM.-`dIOh:9#2bQoe%D
%4?R;eWt7ZIW;`mN5>ro%(lTA,n:?o9!=NJZ&:=[l1,)X7oG*@@F33,TS@;BfRNUFbNo<l=bC,!a%tiGL8>KQ)C6*@4Y\/,Z:ijRN
%Tt*^lnYD-4TX^lX&29'qUZ#FH`XGU#P#GWk%HG@1G?2R310Se'VZ=&p95jUjOB?q064l./OjD0I/<M4a#8`n5UBG+VgEalP3Xt':
%R5P?o>dp;,#n4o]4bF4J)p"Ts(a=V->T1^e+M7^:@f^sR\5UY+PR068J/[=)r0Rc[!LS!mK"G9>E$QG4-hA$kV&>MNp@(^C/]Q)i
%94*!qhMrLrc7j"T0\JTg^=l=T!4)rX2t[Fh$I[.c4/_+]>^I[YZNGSb"B-s7J-bU3QhQn'I'==egl;$9kVs8^-N79[n/FQ$!toa.
%1d^c>N#G+i(t;O#HX>h;KU*W$rG2TV^aoo@/qXcY$EF5*l6?isg=rA,NO\QLrV;87\5S._2XrQ_`fF6OUd'p&>Ec:-,8<ha-@P%D
%P@.G:+*!15>pOrk=kf`%8AU\\,BXp;R1&55Uc8tm&4B=u^do):JFEgW&:l(:"IPsO*-U=*88oj,8"Xh,^*bnDQEI@m!n)aje17Gc
%&<e7K976LT=cT9oi&2o)\iY*\:Kub9^ICGJi"##17q':@4"rsGf5Vi;pdP=/hBp,#[9ifro&sJ:#<816Oa,7LM@'5X9otBPf$JH]
%,m;DaGA,1;X;ce8=`LGD5,<c.5)t9'X_3:FHYF%:A7QrkcA$m!DR__lUB\f$Nd29bRq'MBY7kiU-7NU2M+][ODB"1WM20CQX@&eS
%9Q[,Z=tRHIHoIH^-'4%#4KDO.]Q]JJXh]@X<C)F81t5B`7tS>=UkF,C8_3[Y=eMim"at(Z<*lMF=VBNt2e.B.Z2uLoO%>om>PO`B
%7$],a;Bm#lrfiLGB46qo9K1BRao47fD+:>kElR_g([3O""QI5c8?=K?.O7(+3Q9t=DkL/C9Q4$_eH#<&@k.TV@H-&Z!I!$Jbo?cr
%Y%Re]%(qgTX\*!ckX06V)msP)P6hYg',`R*=jk[/S_'2OgbSUV_O?=B[%bX7\S*+>""2+g;0s9S[I_3.\K@Ird?t(G3*X3<^W`rh
%YoukB!VG#2Y:Fgc)ldTcW"6f$jXQUJm'<T$GdPTYqqO2aV@DWdV)_,_BS5PY!E5T'0rOgIhM$naq;H".Dq#*(>ZYk=7c?f_#m;a\
%=JjX/dq]:EYVcjSRd&@0PG#L<8i0RJ?AG&W*B:EJ>-_%d#Sm$2TLL.K5DKsRk,2Ga7g8ZU:(74A[[(u0&p.YiOm?2XW*S>0B=RdO
%#)sN$89.U[?j-Ru,7j@575BeH)PM@Pa[g&>Okp^98I9FtHX\"pFonZ4`'ea@=LG$jI2YI]=;T!D<@L<Bi))bn6ENR$BbF6p82-@4
%[iR3Yi98(Y35+Lp9R4L+cc*?!iD#s%*BXapQN:5Fc4&=OqFCu!EqFW2S&rB8:/H^J@@eS#CmD\N-#_jX'FqW?&aXV;=+>8pa%eqU
%@r_lP-'NFYa9D4kV8\<U=\qeDHN9uXH<5gdVbHE,`*:rsIPRLK.2-#1Y(WGVos'cI.VRhUcOX4\+`hO_'S^!%4;$Q!BmmTW#8d&h
%qF/T=PXC``Y7(_koaU\0U_fIc;6\\'o`B6($DpQ9*KZ.*(P%`ijO0]F'#<A&(K/<`A*,2`"EEAD,n)7^6rB.M#mdUV(p!]s@d$eV
%VpYs2.JBY9*6[^B]kIa_HTEsQegoPQ'4$>=$/4H?;5&'f,EeXUKT`E79W!HZ8AReI_<<LZ+%FHHWdG`%"ZT]:45fVY&NhKeO//=e
%>!Sea&.9W@A>G+P3#CKr.L8d!9;JiS/KmNf"Y)K08YSmMa4:HJg'\l)V=Y'jB`cb"Dpedpe&9R2Aom4e*m31H$m1^Ojg0TMOC8Eb
%l&R<g&D</bW*KK3h8#]DKnOj]-3Kt23R/oV8V#f<aKIC:e^Wb3['S["57E4$I260Tq(_?FaaE?,[9^nY,l4'q:r#gT5K&CMg%.B*
%2ZT`A1+kO3'JQ+/JXA.01J=-d]T<,7j@^!lmE>`!>UQ]=#s_Dtg.%bNB3Z<\2,%1?3TZ*AKAhHJ5lMpY\_)aL%0.t>"YJ"GcW:<t
%Ga;sKAY?577ktks#i9XZ_dp-L!ipKC5brm@Qc7?#nYs>kNuO2ki@`eKd.e`Yk.@l:2&f.#_un44$QLb1"ZJ-TN=q]%n:j_WTtKEW
%KuBeD[,.Im$f2iJn3kAskIioI%'_T@^-oTFJ>9)W5f\X:I_[fqZS4W,b`h<iifIq^?PUWJ].XkHYX0Q0V.X=qcqHgI_Z[$8'bHcW
%.<l&$jUt&Fq@LYTP,JbU>d3jgl>nRWf+[Hmg!4XXHDY=!4QN`gWM_LL@'r<&6f8YD-9M=mU8Hh$D<BJcVY?E"li<Sr&rmRGPCiFU
%10-b'_Z>]Vn.M_SEIS:GWD'^HR55'YCC)a]\<"_Cd[6F;K8*n;VlBpEMI`.cQl1eS\[$/faosIW%_5#.*gQ*n8Y\!Ube8QT>p!mC
%jL>?Nd;Wj#!@"-[Vd?Sg/EZ2d^@Hr<O9o:l`+3(WGqiFH=5]UU;7[\.=>0MK#(/qbP+H(P7V=<_ZAM:1>&rjhclu3lL;#V'`SSGm
%7FERpR@6Hu7Qk+FR,sk]PWJl/b1KQ<ZFW@;]?i\%a]>MM@Pta-c\p[XY=qd7-:=?mMn&S8=`AFb@Q?@t?DVF:N_u-QVF<'m9bjg=
%DqdcYdLQA2H&gk)4LSQ5R6q@r?\>f=d_e+WR]>U&iR;C66@qQ`Xj2(u1l?iPH^_O3S#?a,)Uq:$B3oSo_/[@VO5'hI??_RqH?<2>
%B1`l[&)Ck^M<HgE*I52@l&XgPeg]aYA>!OjoFhlJj8on/6%#>#"qNN3ZeXss_FMl17aMUlqU*c#7rb.CB(@lSo2NM;9t]GcgroY.
%np48#Z)#VD:o2h<X\*1.VE*J32=ls>$ugNGfZcsSa#t<QA65?>LE^9*S^Zu\)/6c?mT?!II:YFXd-9sk)`F)5'[(=lM:W%dg`bdl
%B`gAX3,8bR0hBZ0Lpm,@94+&r\bCnGEQE,6'(Gr>g(2h**\r9S<IdEk_tU(q/1EtBZ"1S,Q3Km%#pYWW3Nn*oPO"2:)9s*G/a64I
%<XUbmU*EQ21D%4Jgil<XRcsJQ[[iq8f9qX[pn4c?j-VAm#nKf?&F[]H-LVR?ia``SIEpS!F^U/!*,$3+cn^cDZK"d267W7b5=JGW
%=r8+SG<j>87bNK:ZC'#W<IsU:d*<M7<0@WFlWSn8m/_SMReg5DXcNd)'^BF-b9q[VF@7($mP6T_95#NEp&rteHK+Pu[W_a84\#K-
%<(U=(S2nprB<khue0jpF*;-<aT[sIY\IQ:!!]4(VJMfqSdeJgQLk+90.!=_^m'L?`[N'?'/6<L(d:=7+*uV%#af2o/qU.[U-Qm`#
%]rjZ`_E]9eZog`oR$:PP<')'qf,I9b>_:JmM/`a:!A=(X/Kn;dBfU&Z%O2S_KIkG('CX+4P?c%rBMb/]<!D0C7`A0a]U&Ao+epem
%8Yj<sNJu<D1s=Rm]Y'Xp!2?"4&iq);o?%@*5*dm^61\GN".bd:92(HV<iTIV9JULBpd?/4[T=KW,R6Xac=T(<,YUcd9?E-#[R'Af
%O,Tma_eog]XrPss*]uED,B@''>6b?c;O2@I?neUdT+:@=:.HW,H"V^^BMl8WlB)p:buQWd,kKh$4YOshogVL\gCYMbbImVpFLZRN
%9\q'5.X^/[A)Q(lm9cTgLj!m.PS8U6ciWp'H`%i*d%UTcN]Y**bS5?L$!DQ[F&_VMNMZpt,nQm$aGJNFhHeXn:.u0Ei%XXc&Fs.(
%ScU2+@-FP'3joZ1Qh4_R7!Wcja("Uu7pVa\09HUuUr3F6!ndJfM'2eK4)'U9.O&a;4&AMqK^0*>c\Sl.2ETk_M)YsmR$e%&$COSC
%!lfVQR`o!#37S)WnnC@=#S`(@ZP2o3La\G)K-?_gao3[@au$`6m*CL89S$&pJdK,I3dk)m1A1qY&kJ#&?pmPX+O#r53q#P$?\Wj<
%ACJR*X]pKuKoF/Idj8mJ^!_#Jc\bnuH^2PC"Ks(J`;rAY%O=gq:/tn+"QjZl+U'bkBI+L*@VGGU^ca9B,NZIHmBf`X-$-K1N4l,^
%#OBOX(7)C*mrgBJbd[p1N/A1hE;Fd&E$6LjPll4ommUlX[lY1K:uBPQ8k=.aEd.n[84V?"mY]()%'V"4UZebc*KJMc^/pdVW&u8_
%j5)k\Ato]PK[Yd>e1FGbfT5/5dg&(<C(m-nAC$IT^aWDsXTP[PiJ@SDh?=AU(aWdVPl'<_UIc'_jIs*,G]TG]I5rOM7<Tr/M]=3W
%$keX363?/@c+@B3*9J(E1VJTh"Zg/pi@i1`*0TXpLt$D/Gi:KKUf81IQ3:p8'@>$'Vp[ZC5$@B^8.[$C6'da]rUfAX/tEY=O!>h6
%mgp;g"V@)5c^9_tAs-c*o@`Gb]0b[pMW.aX!%K3rE]t-qi,1>!1Q80l'#p@P!L'1]@Hb'lVZH*b^bM/uV^L0s5fH)$jIj`r.!DsK
%9HeCIGe3#$P3CGt-1+.8nqY:6f5[X=kWe.P<<F0u<G>]$#nri^OhJ_t@[/.f0%W)rCrqR;1/8,`F5WAB8Q_nKA/>>-3"_r_"J.`J
%,Nr^B+<b#181a<C=mk4'MQP$KSrb7LPZT.`\(UqMSoPOQWFl/EEu]O'@S,d.jV*9i+gWAk,S+?,QUL+[_FP82e"A'[:edKc6BY8R
%Q<9AV.;7N!W2eRfa?NRs#4<YUXIR0p&9-oL-]aC#08\.^F]KX+QndrF4PQ6'!(TlU&fIE@"+Uhc'<+BBp(u*R#mR0H-EkX,3]8Df
%%*d4&F178W-.'Zl/G8Y.=e2`L&`NRF830$ThnOjNpCH%NbCftl.)k$d.=DC\-CJg<<)fM16`M51WFp1GL(rm_R+jos?06u87l<=&
%7q:8>SV0:)U-e<?6JbjkE05VGISscu;XD89C[<Y1^mMIF,*1;CG'"&H$^ss#q.@^CNi5"+(T/n7X8>C\(I&Ao?3@:tK4Z??)AW06
%-0EAkfEk`3pm#&-?67k"!VO.c$]9n*XR#d,]O2@J-uB'"F%5.uhYGNe`g"L4IhH*-^<NY+9M^6Fep$C2UZ#at#rCFPi_3l3p<81D
%%<f2pXce@J#WP_9NlHXo?V:qo?KB=)&m$q;jhI^[.FJ<@bYBjEF7g+dj_9cb$"DHP/`Un-^40dp)\@NZ"O.S<+ZUZBLH2@9=6l(r
%fTd`!$8N9uMAB@q$0a%V74phW"d3XpW;!Y+jPUiWQ*IZANHFuK(#2fKcqn@`0+"'Y6<KD<GKdW%+ns2R5BqPRPd;]r7Rg#^"F<U)
%%kBh/eWYM?.2qro,TNJ.C/fCd^)f0UJ76lJ&5$TN%/.eG$.=@'dukknBPTJnRAATc6jVnrM*XYBKV%33WlJd!cpsW)1(Cc-Tj*[T
%Zue4mEJ>p;a#/^/=0acFNAo3?PpQ\jL3b1K<b:`9,#Hcmb`9Ia3R_*Y8"g:]R65Fs1R!D!6k9Nc'IX$'3Q.jhh02e(l@YqH8Y[UI
%_]kR$'6k51.<@Loc1)bD>n)].H9JiElb4Y#WW(Sfei8DDXWmUG[YgdF'd.+_ml=9/Ete-"KL@JcU@!qXda5ipQBc?N5PD`U4%D:m
%R53W5L5&+Y)%;p+I]BhcFtOD)%$ooS=lbt$0%p4f+c8HY1h>n?K8$RgY:Dr/B$_u*$hD\Z4rIeZ<nH2g&N)*?^:V>&O2g_R;9s_3
%4X"eq(+Z#'i6(aV=rYCDN+D=-pDoTR=S^Ggg1_NJT7f7%[#d$lNi`:Wj/rM[8#BCN\'_s'83AHq:lR1OW]BgsqQS6$M_!atF\ka-
%>hh8(bhh)7h)!"qq8bu4aj(6g;i:r7KBuaFY0C@M"1F5LbJa3sHn^hD,blVXU=bJlAV\YR&J7+E:nIiap-8s,OC<dH(fMb9a+6W4
%ZF;3+o'X9nLD]2:5N-,S*;:#8q2'r4KQ:t5IV7>2!_BI>26gbmR4EemCNu28ElK<F3f,6u\JR'>OfaG8]p:MKhV-/p+U/`BFHt.F
%RR!r1>ehJ2VtIq$4C^g!8[PooWoWcQot8;%E3C3GPdHe(1>mc#bqf!]<F3ITZkeHD4OO'M4I^cGY)0tsYm*p%7$-1a"M6s43/LaF
%=:)eJ/XrkNdIKC[UZa-2*Q`U)ff3X=;\6ShS':;_4utHS"eZFZ%3t'@Llmog)MXWOr:;oAM@U!ik5/jWOdjf:XT\f3qp:2&ZTLc4
%Y\Q1dVuf=GqaI6WHEZ6tk!A8$cBm&u3\rc"SbVDN>f*9IX]rA!4%?fe5-`'s_MLRXANr$))1*@\(F],h.Tgc4A/M$T3,O&/Hm:3>
%g9c,E[dDLTaIo&gS@<5>PE=`II!9+^TWM%2r,Oe?5<5$deSC*n'ZbkUac/\TE'Y3JUq=KqK]bA+,m-dD/"kKo%?@6iWhZ\2"<Dr)
%\F);=oN^KQ0ZR=E&Xgs+J>.gOBt&S`PY#M*RW?EB+YfsOf4;:&0^\49qEn(\mN#laN2kufM#5=[KonclN9oGX9RiKb@L0fM+E8cm
%AuIt,43e$WD?@_%9P+#G[2m-/4YK%>M%J;*W1<3Qk&)9.,>G8jdFc/pa+R1h-F6>(.*p)2Vik<MjsMY[[Qiga;$gWQU--!d@?db'
%U8mJ(noj,_G?3.@0pPp1+sDqVU4NoH2)+**AFS'3]Q?]UJ:oKg_rP&(O0@W^[;@3.kEL'[b\?M+'qM0amQ6<.0f&Tifm8!WlAEu,
%:nnX3jUrd/5EEKu'N=p[b%a8k,<\mK%1B\(_B5u&.[mbii$`@]*8FC\M8lhNJ4Th[.#`3JFGh&k\3&;M8u%!O9%3>XP)%/]_CMT&
%?Uh%)\J'XD8-OlId-'LBLe9kd5K:DO!:c+/OH@'d4!HqWn?l6dRRnGb5EujjQ5J_&[Q\(CAB@>#NX!pi%)&$Lr;gPoB'F?m/nK8>
%o8UTVhPr8:6bSMU;B-;;k]f#jb:t@Rj3.]&rtKU-7:$<jV9:Er0iC%HiD/S'kVP<0M)qon*$I)*Vjs4chJJee^P.'L-j9p/nj\g0
%LCVo'-q:6sPYD31WPFqQPb<rG`qmt7cr2XaH-/'-faF0K;"Q>,3=a**E]5RS>gX^uBZn2n3,I?bGmAr%je29Xr3/f<)=&fPH*LB)
%=6;qlC3&?k;L.V<Q'qnclK"(ue59.42;72G7\$.mZ6:C*<C#W);Wg.cA/UZ7<QY,La)RZuhHFf#fubK*8`J/^HRR&7V4hOZ`n'FJ
%7O#8+5WO_%Bm>EKA17-T;dn"XdQi7aV\d>jm*-e$9=/?QM\nFq81E6FMDk]t9$g]K4T`_Q^%fcac4!gLBT/YGJb,Ol&Ot\:P:;2*
%0HR1_QHkjeMsEh^6l_/)k:(R3bZ_j[J\5oaS@E4CX%]]Lef;W8!-Eo'c8up`GU6eh@4sY3"t%0D]U[AglR1h.P$lDZ]K$*j0M)R'
%*i'%YeMB[B_pk>&=lp:\,TqJ+eJQ+qM/7*_7@FNHBHjNki=Q'XFXo5n]bWlNKua_P=FO$Q,kOj+WS#tE:r#@Yh$6EmZc`O\Ifcd*
%+VQouLY6"qau9`F]MSi8/O0I%6+_>;>I3t4la4\DqeZFG-+KWcm#iYP6-1rLbIB@;QIt$+L&(HFB9SeXZc+T]Q#Xr?3AU8je!+Mu
%qE.E=.Rg4K:'2^Sg,#dm*9a1mQ)W*:atiWA)d,dl"_ItQ4sq+A]rH6F0V1)cJDi<:(rT7lBZS:*LCoX\;iZXKS(/=JF,]a]ca?$#
%'q8-?6+r_E!:&)uml-G:QG>gWqipI33&sL)CCYX-6M-&C,J`nH$ZP1h<,O`8Xfr?0lFVR31YXb"OZ7R)Fm=VhW>gfgH/c/NZ9%)U
%7O4sb.;<AV$CV9)fT;JB5YQQr-N=u$m*/iR25/W:hnH:-$)?GjRARhu5tAt&:oRp\$>5tl5"MW*HHT%9mMa%&0hh:a$[D5.G'2-e
%3U>:/=G1$d0;mZ/H[M`"L*OQ(k^V9N%V,c922=e!3?BJQiqZ&$W:l8#9<N,DkCqn&:4OYE#,Y]B+J,g]`8`.X0Tqd?@h;Q9%$$V[
%5SS?>G'+Ch2C?R?firR<R."$?A5!Y5G%WF]7)'.QX)m>1+l:[@6a?b]f,GP86qV_.1M6m);&!&<L-bbK!E,Be:qSgmS/rD0OWi'f
%KlGHA!`p=+-Cb]B1R:RA?$9ZlS7`78/EuXRV=oO%)5h/>J[h6_SBQagC9*c(-F:gg=lAj9]@>`2m=W6LSe`>9ap*$k4WDpY-SbCa
%i)i)D-=cpBgM@jP3=@alFT`AoRXi(Y$]$>DGcu"81;?:/_2(NGUJ5(i\Urk+?&"kp#>N?:^aVGj#JEfN4-IS3JZ0S9orHX9&=DD^
%lU_`pMW9p>ntB_uYK'LCP0!Q>\ZNe=6[aDPK.eCB\l&Y):>Hh7EuST,ig>rHT::PPh#X)P_&0IgF.n-n3)e!.W4T[o&9O?I8C9i,
%>fj;'$Jpnd;lh=>cOUQKpo`"!)]RK/2o.=RbblBt7A9q!amQ#Y>&"Q;6(`HamLSr8<I\`oNB;uD.\]8UCtKp\(B8V)`gC#8/2tGs
%82<$lr(f\H4oaVI`hYZBNmf.UD-Ct`GVi=`'-D#?@O5T=dLsKkS?q+7'LZC8E]cXcV+U_nMIu>kah\4F=4hfk/")V1eITg!M=Y/C
%kY3YCd*,E"R/o`L&.]8s.2k)=eq7)7j*Z#B<[F04(o8@pjOe+#DK/1'[K(TP+Ztb.\h$J(=W#@B%SV[$`qAUEj/^/LRoH(ZJqJ*2
%8o7qKcnW"6<*qmuR[t#M5ZU.e\-_B;9Db([jP.q84/UR/W^pNRFX/rp[g!I?Lnt+EZUAhPZB?G_NWC)]*lnIA4l2"=f(o%sZkArk
%HPAPl@"SZ79H$;(:s8sN!;!_(qJ.9T%"!0g+nog"1T&+(TPs$WL@g.)2*A3%WM;#8S)LMk]#2@rM'.$n0m?U`PIY2&2q5<C?T=/C
%P-ho/-^nSe`^uGI#qbZD&)>0NVO,^D]Ho'D\#?7cQJGBVf4YL,%)>DT=bAG91]qEoS@UdVO_!su%"cr+JcsS75*0_7E$+RJT9h\<
%bBiRo?'e]+Puh/glC?7s.c[N+`g7O$@-2e@4Y8T%:IP@D3@JgV_^Y+<.)pY3g_S-Ve7T'CTTgc^A)uah_c(26/fL34jCP$AEhR9$
%/'1m)qSH]1Hl[87&aBhl^8Wb3/9^.0<>hto3\J+Qbj:D_c3JfCHM[%.n8s0Q`W#Ypflq)T0+RHA+$DjT^YXI<ja(l=bu8?GQtt0r
%9=2(Qa1q[2n(H,13$!Eh1fs3Oq%ZNjSfZ_Cm'"^`Zbu%trn6hh@BTY-=/RGG?:n;^Wp8jC^:h1h](u!19CmQMf$A`Ls75gYjpLLZ
%SueEIMaMjJIrTe?^V6PFIPF(T5(E&!KKW_Yj7t"a#!J@>IC=XkB5[I(%,`6-KQHp<c2[Rp]CgaAFrhYZ5P(FnqR?:(s.6J[mJ>X/
%]Dqd^[laDRhu3'Y?I[2-7kDf'9/M]Sd0!fTM+qX0<4>^`P=[='9(KABiQ3YtF8F"dZff"D3u+K_PZb(d]cc_tl7=ABc.M:2\*q!*
%juYVi$@RGM4pr?q;r)ha:b#au7$pVhIGUp>'<@Z@8f>DlgT,#3<K.eGWJd^j\:r-3K;H!imgDi/h']KInSi\4Y9pEPSm*.DO//Cp
%-B4@uSbW:EC2e!S'9UY&8o?AnO=]SXA55q;"5n1Q3"(1idff"G!X<_<JcD"S-fSQ)s"gpR1Kld!&1DdH0g9qiLYR)?p=I,Q&_1H]
%eIrJZ5]4$%hXUfk(b*U=W_/eC#mTt.a[';sX2NsIL1IQA'u5Gj5]tFsDD3+7nal61M637(G8o&t!u[!7`I"=>?nrM>q',`Mk\8BD
%%AM*\2_nbC-.(;%@e"'AKeUp"7Q4ukc,ZZV5:*k/njq4F-'t]@^4uSSK8Eh=#Pa46FrhMsB2BV]iC4GYp.LKPGtNr1Z3<S_BKZ"h
%L>?GVEp^fn$>nHAkq'mA?TDe5h2G.N-R2_h"iVaG=HRK'R,R6id*?qn%1"$5%44KTM,"0d//gTu)179"CQL%_F1?g8biCE7j%1As
%;c+(,OD:Sa.tZDW#;ngP9C=(blBJUA,A7-<&M.F8X6GdKBbLl(ZO0_pfb0]H(UHRjJPeN(qB0ZRVAKro4N)2jjq_!n^7PZ@`jIG2
%dqC)V$*CKjiBW)$G=?HPW3Jd'LA61B3l)7=1Wm"KE]N-tPoUim01o?L@6!3NbUc0]A!+mO';?0Z7:aS,lLcU(O!ObV`/H&W:LO8U
%CYp[J;q]2(&8(u7Mcj7<e3X\cGmL"DO^PO=o\M=!$;3X7AC;>$T,N_P?#c`:#)4o>"s(5*G%8(<nR5.QdV0gWB*:'@ZOk-8Gat-'
%9,KK`6>,h%h#G8_Ke#Hb*(T1l>F7h,GP%gP9?K$dD]oe7,b`ePeV/aec9knY]MbTS(eBRkW,K8[aJ697@kJ1@=]=Pm^`X3i)(VIW
%Cc]@C.YuLpak#Dc2Q2Op-dOXtQrl.d<HVmScD2WU)k[_1&9s9.(QFjc"uD`p,B_Q%oNfOqT`,AVf"JT+3H[n_ihje&XW'b$F%RHK
%#^%uF/ep5"8c"O2#!MM-@TFd331,VDa0[s]`bq`aa$B*($j=jZ!IHT3$5Sj%*D3nuUe>>;7pcT?HYgP$1O9\o0ZoQUe)>\hd\B27
%4CqG?<;4*&X&%mE4Xhs%%nZH4SMZd>`[4k_#&rZkO^ZT@jQ&=r\pli<O0fG_f*X)0X,o^Y2X/U*\N`j2.'eN#[?'IlgL]OQ+3gE,
%BnBJC^.*E:M%Ar\f&!&`XMX9Wd\\MDdUoOS_Eg$B3Ta7`\5hDkECQ*jMZWV57UXiZPauJRl)NU!q=Ji&SLu92r94=u/sS`ifMT;1
%BI&u8$!rA7]CY#VqJ[9Feki3;*9eJAO,@%j%%pCt"ShZ=^.mkAJ^.h;]mNVdb;lGESWF1%Rq>)g?qRkVTElnXQ6mlJYj4$cGg?p*
%oKc'i->C?Ii]W57PTM(o\RlZt,\*^K@%Zeg.SJZ%E[DC]N#U=ej+%6X&!qA&Bc9H][S^6iB)8%EM0nN\&mJ8E0cFVZoN#KfD2>W4
%7`h.]Es\SKJoEH:HYK4)*[8:&Yl-sZ@`jla%uIgIkG6R8[&F2+@ZHhG79hhs]651K:@BBgEfgZu,tN\2A^Mr1G!?!f%$T,85"(EJ
%a'KIs5</hQJAJ1G;J4]ij90[j"t^[k:,c;W1@aTE%sXB_#mPCIO`R$&=Wj3p#q)D_W[F$(#82T=7s4*p9KejdNLA^iBt=PIef)o6
%:?5:c1BA?`6o"bYAc@+]FP0FNTG\`nJR`#(2SP@LXCnsZ]r>]b\t!rao<d&'ae>(WIuIm[boHtUWOM-IRn"aDU!!kJXI"GO2:/f@
%f9;?8/L1kFOD,F+a]UNI3gPR*A*S2U-`.-%PIN(@S-/u,0\U=gPu?eDQ)867KUss8]L0);$Pk=8?4e3]-*g?\4V\ka0R2Lu@)$s2
%Xu&(%Z-H-]8OIVYj!-]g\Gdq&,0-87h0XU6[\69;<KjDPAZ8&Q%T17q\!i[0-U->MZ&;'<4H<\j)KM6A9!"1p0bQL<H!%<s8T$+_
%)X+=Ih*oMZJB2*D!i3jSj<Sa"A6B-lEGsh[#V]i2H^"5OED[3k2Wq+`a=WXjeT24pfSUGH;df=%?;\"/6.J%'jcXN,Z<'#ULCuC9
%b-FdZ+X!,*AjJt4m7=7:SrFD&daZC3l>R0G%+/[N4D?,t-a^"D>7A@A/:^LS6:)<JeNgT`0>;QE`6?n.?E]FqXWK,YafA!=2%aeC
%(N'qC1tmqL(+bnI`mJt<(mL0)[Mg$(hB(rQ&+9>AaA0e`5ZNp8P][6dOs"d.,>$`"'rb</b^P,lJ2-S.&os/2HAISAimPciKQV76
%,)PC&ee1:rE3fAY%/-.E"&oW_g5nl3:*eapn?L2Mp4D59SS4Q>I'^oNe6<Y9jNk:NnU\oDk`qWu)7,kCRtfHd:`iW1Tc8<]VHI0&
%E[*h,!4@h6@MEeRQMOf)+UW[g1#o'tan!G*W-ajUrM$IQ8-?rS[^/ph06a/!:8JoL*[Sn<X@N5"k0i8*,]1mN$#tC\ZO.(p7T`L*
%UQBP(-DC16Y/7T18ee/HJ:5RaPo>Bc<\Wu33l?elL)0'9\am[WLB^=i;bVhG#dMFt%g>(HA)9g@9FRY\+=iNE??>^o9/hmQA(0I_
%`mc7',`ar5:5qOWW,QEKAtp?"BRmgQY/pkL?B_B\U&e.3Ji$3"BWX+g)q8P2(-@<<fH>7WS'oAWM-^5s7MR55k&-^hMYWL#l:-*5
%oQ3!XC<>k7-)gKe@Q*#maV\LG&E89$\GBUbd*%<lE%t=H82+/'8%Al7lte0&[XhTF2%/,*pn(J^!2#C&YcFjg,=n2^)fe3]TJUoA
%@u*cWs'.s+M:3F+[5heFSW'GMi\c(OP50\%3M9!R&Fms_Z;W^"mGM^BA*(L#c_#2"%5U/6)'Ao4s&%OIJMEm:(<V((q8B@Eaqgmm
%dM&&=^r4Z(,k<d\O&PBqLKuc-8mJ4LgLFmVAMJe:TOM+%eF=5+$3ekbV-Z$PFLgM[Uqhq.m&LCT--0UJWT/q5A?eJC0^o_IqJ`Vn
%h[E!2>6t'F>VJ,m#bhG27-FkmY_61L2@OjGU@po/LKb<hAC";V<9/-&_k]tPRb[(0:F`&*rU0QaSEfb%)]9u[H`0fa@c8Jp6I]4Y
%!$$-g@h)9-"'tBcYUP>)?qs<-mJk+LaMZcNBD!F(MeP-;H%o42Ae-8D,>pbG>9m6Jb*QhQQ?;9(!IcbH"-j*n\J*L5P$21=>DUsL
%4c>,TVTAu[SL>rHe`n('":6Y9EbY-lS\1pX"D@57:^<#oBqGk$E]gn2$O6t*U/alI6:A]Q@)rm.!D[_>otDdLHdTJ=<:Lg:o,J`5
%FP6sDIun.[W]9*fG"MM?#u]q=`%+$`YRgO^/KB!RD't?sBJPr%[3*1>#U9TlluEiZ>>)c'o]c:a-@6L7>f3=lXKmI#7h3NMD*.C5
%@),a]!Kjgi6Ls+s9i[0(#-;jNed]&-OXOa<foXmSW;EB$k)38/P_GFs*=CpkP/rVU&l6gW,m#=X@`$^\7k2%GBKCR'FH9E.o:5tG
%CiZ$:60EjVdH):?JS*n$M6gVG441=t8Y*?Gm1rT[NJ?f1*L4QB13O@,7D+ddEGYLj,r<B25im1B0p+>hnR+Yj.c(@=%EiKY?=7Ai
%;!h6+Bhp$H7G"#'!&50!k7N'D"a$0Y0'Z/`+Vo/f#i^lG_$GZBo4<siI4;t:_,*17GoLp'VKH4er3<T&3<8"PVB[7+VRR%-Nfuaf
%7rQnqo!P70fhWO:-<J;AjAhU2Gm"@&Z[u0Np?I#jO>/'[<$)X"e4glcIbgAZ]gN%W4L$Hkc>.klJ4MneG?9*0c1;Bd`*+X/9Dq8(
%OruN]UTQTF[=F@HgRY>uTXYj95s`HKdJhO5Xbr](>&]P@e*A:eb1[,2aY`LoJ=]U3BCI*5-?YJNWF7Io9W')0kWh!8#;KRBC.V5<
%ggt0P;*A>qD2ujLjOl[/rm8I_-IqYAYU')\WjO`0A`KfOg-MB4'$#I+HCpVNK^NY9TVdgWN'LiC+Fhrn6CmE6jiH^\*R0fqQp*9f
%0]fZDh!C6g*YEk*;2&7:3SG=k/j=9\VeS7=ko2Au6uc&oW]FSU-6\fe]F'K)LRMFe#o\3:"=.DAj?"4s=i_57!C<km_)*p*3gAoP
%b(:n:%V4TU]:l:BH,^*>;b5ls#Ip:-SIHId!N8h*b5FmhnT'VWJmF*/La=5lk+6(gY!]?5jCN5l4i:(G-"=0%@'\rE]fAF28BTWO
%RK>:=d>Gq6RJoec^sr[oV,r1;2lo9<BGK-!.(7B2g'Lo+C3Q3[?YrQ-)BT291t:#/3YIU+8GdP,GZE3#L/:8BBNJT>n\&#=Vb"u>
%5SZ=XM0,DMJ/aVpFC/Tjl.U\p`gqM5!]KFqh;tDB/jF5tHuX=Tn`'IWGQ$(>jaMDsEO'L'H#>3G5R;)Vq$=aYE<lOBGoI<t_g27]
%6T?]HEcH8E^AIQfoPF9LdFHk#]?_OoLM5=QD_:pUr9g+Kq=sA`ISB?pQm7fi,L]0eL+)$DBrs!](%Pd&1/l<%r_u%/DB;g;9I%iP
%TTY_(MOfPf8]WU$*7RcZ<q=(Vem8/[DsaA"J,HXQ13Xp!RNp4u:P\LThMDd)anF31WTTM!Bm11Z+Y;JA&tM%m"4%>cq#Me%iO6<[
%nte68V4;=2n(;qNSnpVk[>Db.eEoZS2i'p4XF-'$'=s9IHYl*K%5N=mg=SKqhN'&=:N^N+(TiZbq@D"1KBn4f<SUE@aB?"'V(6\V
%-WX4`Hc+FhnRVNrWllNJA(i(38n:I9Q'gV@T&2GEh#2>AD0i7BT&."9p5\hCouZo+<L`fM-AauU`siuPr%SJ8QWtY[\ObO[S"\>D
%Woe8Ga4C-c\GB1VB"kHC*I>Ro<c@f/(FCRY2_!F_Dll_b[2JkQ2H-\me%U=u=.cMHbS$A?jUiD0\9gk67$KAmd#n`-fWDP!95S.:
%CGY[,D;+a)Ykh>LAj.[ahEBc(7#*%FHnXPc%Uj/@NqGQ#g'k-e9al/]g@l+qor4sNT.^T_mmc;-0nK"'B6Dk.74P_/VRdOqX)$+s
%\!pEj8hG9/#Z\%F(,I&*P3ZB=+T=,ZAod1t5J'-$@<jO?TF"JigQ>9eo#YYkE'Ds($8K<nP955Tr.!?1G(9AAX/XZ-I=>PRiT.CU
%:2Or!n4_4Z>%S3&l<t!.2Ps)g`_['tG#lI6R6ann6s'PP\U\)7a:,%>/Ofof^Lo1-jhlo9At]taM\+uJX&=HeXl(g6%J36InQVga
%'bmkom!$N=,#FUFhBslr'YO7>4c$t+1WG5Y<7"E@!?"RRcW0a*J)R1E;Zq`<8dttYB)r#!V4bMT>R8oQUH\%o+b2jg@g/17)of+.
%Qq<8+]$Aubcmj\1qS"G1jt5L`F-)=\1R[[R^e)-W[an=XDYiSg5*$e<Z2gn'!qbS4070BlbU$rJ;,&X@=<8Mu;']94&V>WkVSC`)
%;9NU7c%YWGm9%#n7^%fj<At^ZP@Cii=TfU$KYCH!q>tunQKJkf*f'[)6(Hg4C;s/N!qW)]<'+\;]3>Xa@^*?#k"JV"E+p\Yp*tU'
%"<)9LLE3.>#l;1pP;b%'(RnsGVLeKeVS[ut*7h16;dmHe(JsJ(AL\fe$QA4!\q^qU5rGQMi^O$*WL=/+lk7+%f`\W',Y$uf<u.6>
%5>pki;RW19_p4Jun/nn-CV<rnPX17Bh*X\&Jl'RcP.n!9`.h+raEX)beY"Pt0nbd,d+_C>i[SeqIk-;[pN^94[00hp=t"_($>4MG
%a(-E'g*IW.BZ4-^>o@%t!tYmG?Bi[Z/@Z(r-L`DT:mSI#Cu"M\19aGdcO&8e4P3KA;>Wi]*`q1U/]fjs_dmP7H-QR5(g#CB0N-sE
%'Z^4*n#Y#6GN^_,T<$?%m35PITYV=tPIgUA[aqnNQoZ`UmnLTt<S@&'B,s^c"NrM%+'^s"?+$WPPJDftbWGHaMcF]([?,?t[3uMq
%3P@u3W=Pc[.3tL(L_/f>q_)`\QI("dMaCS%_5DR.W.^f:PODkZK_E<`opR-_ca/pOdUa9@(;hNr9Ar2tA/lYo^rt+gJY>?EW"CSK
%,o9r[okl9!1@[PHC'Y(l3!:`4Z`J]-:=Z0JcZQ"d`1dfhQLK$fD9Y&r[;JoC2pEn6L?(Ko>ofc\@p3;.WqOd@LFa'[<!irqCf@,f
%4s>r-W`o\%-K&C"_jJAI0P3-'aUi6lCr&`CXa'(K*ZDma'=qp<NL>!A_T=.bg5[<okd03icoQ^-WK)=M7iX?E$M%"/0H[V9f$';/
%4*"@%oTN=,0`hJ>_o06f'EE2!n8@fBfW<G]ncVfk=9Zl19bgjoJ)<u$,3\a8GSeK,=><%j@\\7pdM#&F/T[h4WLHprgGK]eD>b_u
%]f;jo1C"k);4s2F)$.C.J[BRU$oqTL10D,B48*FGW$(1KdW?Q$Bjra6BiO"a$k5^B"_!f!fZ/Ai%lV[Oq'a;5(j,+X5NA%6;FH,^
%c<p?s(,2'Tcemj%_641Tg,,cp=4JGcBiS@ug9P#3g-lZnmXue&&%3LeI0T$,WfK;%N%M^.iaMo<*2c0[QRdfmA(Sdb6^^;f])-5j
%>=O\i%*`?+j*;De2-`'2h35EX[5PZYbVfjNCFiJljNMkX>d[&n]3MhZ=q"CSALWqS1!XjNT1C/]'Q!6[,%)=0KC`A!@r:BC383RA
%\`1#dUfSerE^pl<ph8]*X#:?Cm@`3Jgoaha"JY4M_o-uH1IE"2gpi'%8te8,8qr)cjRPndYWV&1mE*V8=9b]b3ubCbB,fXOA?QK@
%c/J%IA(:(FpZf<@L]sADpPo+/Q&9*$KTu/_$6eEu3Vl`!Wa;Z9*`(@EQ%1-r:A1aq;kBG5<p]B;Y%$'G$^pG#>+cp7+Lcp_obD6s
%/7K6Qj'XpL-#"01OYr&n'/L%r6>HBhSp[)loFJ"RCX[FS;,&#8,YB6gdM-m]XrrAX"S@+>JAH"g^ui;\G]jD[.>N6qQ\^03")F^d
%K#=Ko=fP@B[8k"B]2H+@I1_=2+Ka]+P-GJ0i,bKT:bR+@()FP6Qd@ju+N#&^f6OUPY,^V%FOmn<X$3CpE=MbV/#_s)$co1,554PJ
%=cJKoZsh3eMPoYL(W5[+"[7R@&<Yqh$:`1KZ7e#<76E8X'=Hcj)d;9j^dE+tef^Ufbe"t]Es3Il^"GaA#G/40^jU>,IFSn\?urDN
%\>aW;U_YZ\I/5;(LOc'rWG$_t,jc*'S?r&ICg%?$GZku0]^@W7\T>h6^4A(1;d4Le(bU@7d"$>#<k4f&LeuUpjb!P\$ec9ii2XSi
%gFBl.1$S!?UB-Y,`mDQVX;tl0H&L@+W0dWe7oA'3C>PJV0K4me5NPI:4`8Dq%=B)679rT>=4PulYO/'0l36&<Kjtmq[auG%]o3]=
%ClR`$Qn2TuetLPR<2%%)m^/JtFi3e:OXladel+B&'3\'q;&)n*(>btNZo;ID74jb:%PtH*5gku+c0O(fE&03fYj&P&03t)@Xi@*b
%TH1O?h)r\L1XaPPFm`PZbIVKCGtBc'Cmh#>gq`4rG'R]OJl3M^=:T>1:UJct2:g6>/l%<K,C!'SNc7majK;`Z:Ek0#0r9[MRR4%j
%;qb%=PHRi0qLR1CD0j8oa$q9u4NiL9S-K3U6a=*?mKc_52q?Oip1.__p8%[8SrIRYC]u_/_jQ5m)d&,E:gbpuG.G3[3_-MIY#a4D
%-ccO+6ccPp$nDT>:0G\8JX*pY`quGDi"bQu+7pffOPm+\BPc6]i+=h>b`5,$VK1Rt^f:&;Kq$KB>:qf7Mhj_9+iLIXlMT`Q#QaL.
%2"nr^+CMb1d/99IC*6GY4WJ;UZH_%&g)-roCrSeW'Qel3buhcWp!#C)Dg<t4^9JRQbYZh+S$?7_!Lh9c.jm2DXKV"@'?(EsVgQZ^
%H$HV<3--noW$Pu/SbD$F+)QUsff093[WZ1u2@^L55P<"*3eL+K!<N@'AJD@abf2N3c,S_)0$5eNNJ"'qS=4DG<#8H(OQN"^3*6Ls
%!R2/qW#dErd>"IYZ_![""H,:1b`Mlm,Lgm?ZYVJL-jd"VQfeFHC&oi[E>Bt!D(]`-HIEK\'I+Wmqu@578BM="^h,A\g4/ahbNk,7
%^_s6)<1Hu";lbFX;c,D?leBQom.1YMMTsf&9:K=uJQ4Vg)UC<OcU_`$6T2dGr=@pB24J*]LKtZcaf[][[rU>GR5RgUb%C!3X9%+A
%bD!51$!<]7&@k1_`+Dkig5=$bbSX#-hjG?A(b:/gAPp<_AFV?V_,.4I%"ik2/Gha.U.=NX1E$KE1DMS_NGGaV)Ag'47Uor%`_naj
%EM-./Rmj=qd7;qonqG%,^&aZG\DE]YX"i6Ng5!@>nH(9)bV\H\\[!-#p"SCR53@"@k@2&N_-RBU`G-spM2IP6ApnILUj`_VBOffH
%e:rPkgSPUfTNAG+%*A[(NFWJFF;4h^D:=3q":N`c9!28A4j+s@OH@[0%mj,D,55i2X$ZAsDaM47=@HC"<\7)X_8d^[Y."K'nS(=>
%K,(4l"06_`AWHSFo(i5oB.)J62pC$U@g_3E)&#c;29?K;b\OnC\H(/^(g_MX;qcE&=rTWXV(J3$-5fmHOWZVF#-3U#K=6Q]PhG82
%-W7XkJ5!p.@#Sm&9ToZ>j!MQP]@3-;=Qpb./CoX!\FY9PklO4/g@ghQ!@luK$gXZ?>@DC*"NaJ=7WK9?=hRMEgl8F]'/Wb.Mg\Mq
%_9h+Vh^i/NhlQ=Kg'g`G<iJ@_A:;hi978u8.f6*Hp>(*^<mi8rF71Fg'=LgeQ#*^RA#j[8$n)Y^g*)$8)mFg]JJ4X>3[36fkUMFf
%2K1_;1)/M>`I":#%*X0\Re"_=atLtbWHrf.[Pk-p2:WQ/crG50JmDA`]$IZnd98Gef!=#JknqFJd;;LT[d%J$UL)tL'J![,dXKfH
%d^-/TR5ga+I:F01&*#e6Z\c!hS%2\kC*M+7CGFp./+75$6q4)^MeOa.UF\Jf9.cL5#,/6ODFl>6eL&RV3>NQH#_U[$Z#Sc0e4M9h
%KgpuUEJ_(]c2qX8R(f6d?C@%6A@\tB_A1kV2GP@mEohUAbdgidU?Ebck$mV#,E?OM&&Ijgh;T=k?_'jlP('l$_CumJIGP:%jca06
%Zk;S=)eT>_<kO##<O'jtDIqj-ilh&:(VI4nig%*DWnU.+OBtIHGLA$I?(h/5d>F@TRR4r"``*)K?Pc)i=+ZTIc#0@-&4\D8Y;'RY
%8E4Hm&FM%(*R'pKEYUQQ,DB"."rX<q08R_ALlc."SqZT9B/`SnEi_eL?m:Q;3ir9:5P[?_G!E'3Y=W@CEqWAfaOaRN#:4Pn*=XiN
%B;ggB6<6CV13\esUnl_HBcJ*8`/>XaFIhu8=&U%8%L&qOXA!XP(2!I#9jq$m7l<J:=+"aPU[)f!Rq7?cp!W4'g_FB4=(&Ao/%f[/
%FLqXBpQ9@CkLqLX=YBn2qbp:ngG.2>R>/Ep=D8I9"ddN.qEX8X(-Y53DW)V=!!Xt))09HDg,j3dBQ4M-rI'*+9Let`*A.M2c_6;1
%k<]-(-H+:Jha(&g%Dh0L<*iYBbGj/LjB]QA^31c,0sJmO8@h=+oL#@$]IdY<2+ma,c*Mu429&U1ZpnCa<07rU/^CM#"^JP]'g*GJ
%Q'stB@dqq_+;ga)iXM4<j^?g-DFT%bd(FouBuAD>202`t-8ZNu2k2Y]N.0_PQmS;;=i(Z0*2tt&1l4)g7m"LY_@EC:W`%dg*+L\L
%R5Tf1Pqn_"n44)#I!Drn;&(%WFhX!B"1@^C$:?PI9oIRZnsJNf-lQ7";(VQ<2AipNc^#Gj"*\aqFFtpsk&C&`jW$d[)mq.hY;_h>
%.V"6KTk>7T!CNV&c?Pr(Jc%=kXHttids?iRj6NYq;_Y)tHK>=*Y[8s=5[)F_ndX-,W!]M)A9f14K3)8Mct#]&LU'rW$->\EK^9Un
%qkq<+%?VRRlRan0WTdl='lL%n#2b_b<ZeapDhiDGHmS$pf&R]of2s"7_:VB33&NYfd59H"VlS*uQrM?VL\eq&l:X5/lu4LC<1T`m
%=<4p]DWc'pLR7i_%nmqH3qe1<H.=Z::oD-!RL#4pI3Fc<2;3p56)211KrE7uQ@K%)oN1$M0S8=g[L/(AT-]Y0W,[6peNF9Hn&#S\
%Wt4l.a?!A8=OkmBOYsY:GYkhZ:@CZqQ:BUC.kOuJqc;+M<EQ#-/%?H*Ro551\>]NLc&7`9f,#lnXC\i:eg`HDl=7N=kD])OeIBZD
%X$TA<\p.L`XDQdXTWDma32>R%]*bVki$+2Si&MdPPZl%Cj>46(nPGDGH5gj,p2@t,(u!_]`&(,FIADIgZd*NqjacT%Y40Zg^;d.3
%17Y$32UQLG.bNG&`p0Q</Y,%0+p%AB/PDoGU(oZ;NdVX+jmW9#hm=1'eLdk?'q/7"]n4%QhX1eYo7\g+Q`Afo41m>CYAm_rl`qgu
%]<aiHh0r0D.4jo\8;u$c3f\fOp-a)2WkbXh)SAa<7[Q-0@5546Tl[)>YK&Z(s6:K\.Z"gH[;sl_b)m35VC&Sf'ZG#Qf.(oh4qV"H
%9]N-"C&i2<*2jNR2)*[n<HUceIU%#""ZCd6iFT&DnM64%fW(%ZXfg(DLG5kX7;5m^I69Z/=&W&BB^HcR;bq5$0.*6s\>-rp<6jC6
%TrYR0<HcoT0Y6%%=r3*NUmd]'=F.*gb5mi#[BXC[:lS[X>oR=AYa/MDPd3R*9['ATg,[\\.>Pir;qStA?+)o+CYq)0hJo8$Eg!2;
%LO:CWS.Z>lBBlkVO,i9gI[pFUe7c+!,:PZ&Hl?c!Oi=6n>"MV@@'s=MlW\J#Y&D^)\@tD@iVF>G[d..>CY-&Z2I,,J>e7X$*d5**
%9@l,'f$a4C\dd9#kVc]'U>'j>Mf.qY(>[^;/W)9cp)6l][oi0sA8ep?2I,.6gNJ"'DSu-!]!^j.eGX:LqA.nAlW\HMY&D^)]=tZ`
%7iR2jlW[?^m%s#PF;qB$d;?D-7CV[\(>X`2>BCgeDp`F6^.#NiqA.nAl^NGuf=1H^Fa]0c_t>UmD:c8\2I,.6gMhQ[d^JJrOOgRQ
%6X%+9%;FSiD+GVKDaW5tI2eK*.?s"M[,Lj>U^bqbTd<Aef=)o?\@rDd_t@k;gTGH^g2MHZdY@)BO>^D%L:#q92P^f6[ioV#^9nKP
%l^JTH[!blL],oCtle@LZ>i1:CG't=X[d2\02I,-o[,Lj>/9sS3okd5=Y&BK`CFm:iFF>+V[d2\02I,-o[,Lj>Ui"_m+`4a'%F^'t
%gG!KV^9nKPe%q,(>i-=qOBpau[,Lj>U^bqb+`4j*7CRGgMf-H*/\5fN\*,_ThkJkEI5>j,qGohN?/MC2E.*X^g\!/0%G/)Q2I,-5
%@+]LJUMu!khU'C^Y+W7X\(8^/]2-hHaAcDYAHZ7QAH^c9b/i)ZJSe"bCLmY(d`Mj0d`PsL0=<P>0"!Hh0=<QP+f^fA?`.(q<S^W(
%;Dk6u>,io.i20PPoOUtd8h75-*jShD)U5"ABuf%M9r:XeN5:8aN9Rgub!8/Fd)rH[0hVa%Xc.c\Xc.d7=rG.X>++S4Zn_]#Bk7ES
%Bk=$nd)nbcXd+IM]@=u2!ACi9"s0tZD,Vsk`d*ZsAS8,=F;nC$<-^h]'A\CD#!K6@at1-+_,jg.H8Mp.AheC1.oVLMf#lJu,&:`u
%Cpc$V8S)(n+`2"+<fa:^ArVjc>h)5RZaS/ZQ!#t@dt,(>X\@Jd'tC**_14)E1)RU!<lV:t9<ClL.8%e7>+*pj<38hU#E.SMKo^D:
%=DMKJ=qmM5Wk?p*TQs249JnV1E(_][F`C^P=tA'^>>s@c<i2;q74Zh^bbfG>[5)o4fYFu'1'T[@)2A?EfUq6i+In"X86:De_N+O+
%`1q$[rWo(=Sm"33e4aCAeU^MEbrrN11qZk@Zl`DPfB2.)q+%;O4!TVOVB2@eH:AV-ULJY[I;<R5!A!P2Yh`OQI$nRB25X$gQ?Go/
%U0oF#:;#u3lIjW^=Z[;`!b0\0;D4ead=b=KY(KauCM+Qida$?e5El4q`Oah0Ub<=]f$<>\ZJ_UN&^Xa"R&MPYK;n:X$9.?Gc7gPP
%^13?`%TqaOTphJhce=eWfgW\c,TWsl[;naV2[la"2+]LmgGi+gW)&kh[aNHSOs;>)B*&fc"ClU)Z-9@LURVW+o-%DrkCHU5]7?cK
%$-YBQ!NVG@_8mGrT>p5_O@L\a><9rP@A&pZm_-f!8H[#bl,hK?BeB"XF>KJnLU),'LX?tQLiffaAdULU\sXJ_\s^j"3+gNT_juO^
%91kmM2q'M;%e-Qh;,;1`VJk]MUo,:NSe[^%]7)`$-Q#t53U(N,@&9>)a+:1sc,uHI]X=hBB6.LWYI&hFE[KH#:`ANf%`$#E!(4:*
%PN)e6Q)7a(2-?I.XITr1MEtI:'eZdcVPsWt23ipH_s4$?`L:s>4;>dt'U=l@:6De0B>d;Ke-:-Q#$*0cC-4"ZUei!8e'/$=16-@Q
%FV;CMZ5SX=-9T^(IC1q2\jn6WTrPk=%N^O;->5G)J438T,]83ffW<,l%ENj`TR:12o.$eEEb]Cmo4/?\c*e/WIFMttoeN);1`(Q_
%BbtW@A\CdX*/ss)?D[pB]hH8?XI$1s;-;tXQdH4HHB,M8&^2b]'?]r&SVb5cR]<4@Q>4*lUc4jA`P^])2<dRMb6^OJ7inS2>#;4;
%`VD[Z-0]KU?!fMg7T<LUAUI7^4YC!:e^:7d(,"'2a(D>cK?8G\?HL>cf39'"0%5gf`P/$M,u$DSPj\]gk\IEX\\QIYnT$"ReD1lQ
%H.1sr\lFpgZYg4,<M)^T6FU-YcAIIS!h1Tg$87@=E@==SB'uOB\mI4q`lS]tVNPJJK,mf(jf4'qoLm?WdH5Mn\2oecVdFFSL:kD_
%\E\,3L4D)6[U&-<U?h+IWEdcIW%Y6:7&g^[CWb07CGc54Y:*JoH.=j4X3@dUYb><]4r7$^RnQRp#ZFI/=gBut+k@0*iAc!9='j@I
%0p$fNhro,?[U.E$/D)m<PT%Hl2h[G"CqZ\r4F9fg]t,;$YA&t%lGnV1[*OLojYcG*c*qALkfqkC$dG3I's6UnZDHG>9p:>Hnke@'
%*U)&,2nZ17g%6/>fq:l(/ao^:'C?`r<+KfH0\[7fQYNb1^K;l6&!&XH<O(U?B,taLeKWXG'ip68hb^MVii6?`Y>/4]D>R%!aSHWK
%jq'H0,)MR.1g.S%?FAU(8\N8'j.&TXWh8H:,2cL01H>hLc"+5*Zu]Ou(fq(j12ga9RUTDBA_j=[@^10q,HBiACH#?jgq&G@0?<'-
%X_B:%qb#5j`/W90IHLF3G*ssjlS;c)=XI1g?G0\MAku]LY*,\@fPY##VL@&Jf<0c:1p\Hoi+RFCbatK<gm@1oW6Xc652+Ci3DFkh
%mGTeOK\=>27p`EaU"/[8#&1*.VCSnD$8-RCReDA<T&kN$n`o&!Wd,6`MUSqBB&$R]&@5loSht4afBABH<S7K>6A50cRflZDNEO"L
%6Hg8cgBq#.X_X^dk,VYYc:Z#Zq+OS`\qq-eS0[a(.ml1d"E\'lC.r)WcBG?7c=rm'C/,-rfZYkgd0aZjEs8bKF/@C/3`i=X=hpL&
%R^ihYZ^"Ec'51"l`BKGZH&IY->Ou#i6=X9$f*N;]G\X@%I[Z#"+TeOR58EKh!F$G@_Qu@)9!&4LT;B1i8Fq6FhMO_!'I0aDkOM3q
%EYE+s9"U-@G\`pMq`M#^NFLVrRTSKoMGLVl7,nBRl#`=6O:f"TFD<%V*UQdB"Y%%&apM$tN>88+VbB+DjJhNXTdT`sl=Rl6%uY3/
%P4uD3K/5^8eZGbA>ANUs=^S\SK.6.:1C,G/l\u.HJnXnUB'i<@>3nAjhLMcMG*!&Ill[$LQMNGj'r`sA9_iXAll]6L[ilc)Bc4T$
%WYkitHs][MZ?Z8sU]U?"Ve0M4P?jlQC)PV3nnWV0om0UMk$_InGu4IpZ@,*4&)5MKbYY\1[0g`]\AHDnWAKkW\LA>!0&FQ4-b']Q
%3Dl5GboW_7Co=0&)^@^O;c!Q.=*W,9Pd9mh?0^?q2B>cZ3UuFWNDoH(AA/rSZu!OXcPkS"\14m)M,JtLf^bgf,@$'kAM,*lr,P[n
%*Rdc^,'R,.\H[!Hf_4*<N,p.uS$:[aK*^DFN^kA#Q-;]g7,#n["ocm@7:PTK>LQha;k"Rb(Husa+K[5&CSZH^bj2I2HEYSU=uZCt
%BY9qL=Wn3Wm`,ku%9F*t4ogn,)`K%YRX:N%FEBluDt>7.'!U0G.^2o&AA$JY9S4*bh:mOf`jPP!<1S8&n1l7>[L-%/rD8&6ih_7U
%n1%Dn&$&$oDHOSH_q.bn3f9N^.=dbHqe<G&QI,Pg[@L7SdZLBMWllascE$K'3q$-[aiqV[;-1U@"&%P@P.6rAd9=0ha+`!+Q*S9k
%@:6TGRR@;\R]PU.h&X:sS"6iQf5e4(hcadgN&@b7"eLH;%nAC<[6kqU865Df_,V6[SN!gaK'=P=MgLsgb.+t%faMa*Z+1qAH^l[H
%\@BW5.Z3mfcIQ4=pYCA%T3/o[V_Zs.dGrpY/C:bViMh7U!r5IBYm_%fDF\!O:]lWIbZjDeWDiLOjI%JMN4]r[Sp(t!]Z03$^VWq$
%`C2(ArtqY&F.?)]T^B.6Z_Lo_Nlj']N_2BTknr#[e,L.haA\VP'>kX87EVs;Ff$/!@523/cB0JJAc2&HH?raebN8BG&;Fa68ug7G
%,j]W8=KELQ\QE5$A`ct-W0Up&[9"WgS=97/KH:ZcioS((C0G5q`E3V\F/r0ikb,oEFQSk&R[VB_0i(M'&/8>LQZ:,TAqQd=pG?Jl
%cSTdUl.-lpZpR)aY+$nLB!Cu#lt\=`23u-HHVWP54qFG>]n,::hRb<W1k^M*)TX]`ZP,+5'i2"BXA*l)nN,@ZA3reWljh],QUe0Y
%[Y)`pU0L4]!U_M/Bd!Y91MM^]??r-&X"r1BLUZrE167*4!)oH&B!`f4(&gH-<.]qd10L!NJTFP7KQZ12\Q3je]>;F_/iKl@c9qjf
%d'CVZX4Ru)#`&k3Z<%'MNrH,IHENS63g2Wj2Q@n-fE+f.H&_ZC@S1iY]tI(U!oIp5n&qcO.hh9]h4JlhmN[jbOZ.&>%Oj-me%DdE
%QTc(u,9>WgcrXA:k"j7RUPt9*eK]h$@jj61^3WJ7d?!s1j&FW!5F&gAO2U<=Mbg=O$brk1_FGAa@Bjr\LpV;E]DB:CNqD%f,-&jM
%koIB6(0?ChSg9p4EAJFeh'Bq%Uk2aq5BT/K.LtuQmp,I"IZ?Jho[CUJ1r!)cX.FfAVXC.*bBfI]>J6ZeIO<$X9g],6b+jgAa6^Xh
%[o:9il!"'D5IoK0%=c[<8YmBsZ5>,Y]RDppIQ^`fTV&gTS<8<'gMY>%ZgC'9Cc:(fYC549Y@O1-]g;ZT1L@tis,EX^^U>`mI_/=B
%:0,-9B"bUI&;Oj3P()f+O:dVJ<eu4IZ\l&&R?R5ClH5We5BZXh*"f\Yru.N;"7;;sWqt=&la1K!PP(F@/aHPgD=uI?W<Y_'8D>(e
%qO!<H[l@?uZ2&Mu'B[V>4cesmaVs=-GpEi7J),<C2g"^dW.,hCdLChO_c4r,X',8+ebbX#<^4if=2%o;?-KUhE47=i4)<DO3sot@
%7U`i3nK]%/<"#-[qXl9u\q%hCn$A(5e$M5k1UAQ&5>UBB7(cm!Wh]$PGNm/ekup6(>$!$#m:oDL7:V+)2#Fr-X_"\s.4lV4bL23b
%ok?GDY1p)ulK6a0=$JPp8fALOVhg!k`V/qg?>-pqf>#HcWh/d>P5MWO?Fe;Rdu=2'1[[-AH2dlNpBSfl>Q(lgEdf``ee_Pr@9ofT
%rE9QLZ^Q5b4nl#K]RD?=-1US+s!lSU[P#O2[D&]1qfRn@;dJ:H<4Aoj&f-I4pRo3>X<&$cZ.nhs>Em^_ZKfZY1XWdOW&qgKk:9?V
%\tm&EF^8(pZFUa>M:N5FGMTWrY?'se;VeM-,]irAj1bPPHOY#\5JZXa6*fiXs(m^c?]87%g]$VRs7D5qhgXbRk2"KrH/I$"GIr>>
%bbpU"lnH`mn""bWE<tJ$A:J^rkih0ooNQTapY_SQI/!9sk%9*cSt6rtO8$t\Nt/)N?bV#4HLgP%n(uds$d.8<rPA;(oCRCe?=$mm
%I/;[\?Cr+dcd_nj4ZSl&pu:;[(3b6"r31\N`TbMD=s*X<r85j".IA1h[JSu+jq%(Dmdp1u:S)uYpAEnM@$&^,s7YIMl.,%oc!9^m
%%i'RV^\b\Ck%F_dgIloQn[k]S(\@ZVr;=>(a#?6[V[)[\>Wgp4*acUjrbD2TdJ;1VqlAPn="!mG[pJdiNo*#\3ZLo0]K3Y%lL\1#
%rH)r=gUj]%]Qpm]pm\&Sn+EGom2ek;oacd=J$AGkbP:bhkiS^+kjX4#/mb'?[*Q%:NF.XRrF5`,[`&dOs2k3=093fI^1J8VOSsQu
%k9$>B;rt(B[nj_!3deVf\9iCHFo$KW55$hVf`'.!%i8;;LY];+rL#gO;m:\;A`+s)mZ0#3&q;]5JI<@g\Y5(+.a?GGSOe7hUjU]+
%H)pHj=dND@fXr8L^H4YE3np:n]!lnWha."E[s-KH4>qOOG;oOTWE(l+b8Toeq)>qBQ-)[(^2LtsFWYPI)f<r:c<.E%]Dn)nE9H+b
%!28]RH*?Zf7l^M/N"k2gh=+Va1R_FJI5Z@?jF?c\'`[*Tqk<WFNZ+/LQL>@ThcW51o=Os:HZur8HoM)WRU5se[Ci"m0;eg-4m35J
%pttCeF6R^jZc?8l`aRK(ci%X*q7,e_=ebbnp%e1NZ3OJF[gRU_Q12@cCZ9R0pA4.<]>K*3-bTEHk8/V/a!ZuW++/#e]Jqu95J#P:
%raUF;>l8;"-/\^h]YfW2p%CJfZ;p"ahgTTcFt#TNldNba0qj-nj>]<[nI`WVnT`Dnrakt>cYec'^OF;N*Zn[[*nRU,3'V\.H?7j4
%ZXC:iqruMYc#)p^:K<bM]R-T^B2h*F:d9d!jKV3FVjDJ.E^])GpX[(i53mUkXR"\Th9bCNoABNEn*#SJSlcuqcbo[!5C_8AbecG+
%i.:]^3<)/8o;b&<]fG![!OY":I.cjglJcRhC4.n*a84fX'02:Pjtl[;jR.W3_!V5(cM@I.@^e-BrqssWrn[P7rqtjRj7DqFTBm(J
%h*/NfGCu1SmciM;FEm`or8dTB=/WP8i4IKf$1.'Rk@O/)ren=uokq3bg#0MKYG'c*G7V1fdIVK@DpOt1Y8XD%p#Y/r4+?7kfl+G'
%G3UU<@SAb3]c/5W8OE6*peZUhS4N`[FS#G>#m9-W/+HKrYJ9tp4444W5k"^ISUC:Cq60hT3Hb8PIC/mts)P`8T?U_m,+%Nhh^:0u
%Eu4ap2p_%"<NXsr[=3!TeV5K5l1PD:O&t3kAjfku06`d\]6=!%f?rWr>i\Q/hOP>OD3Rj$6G*I\qTu;+9[eVoc]qCGYPd3.d?Sfd
%;ni"3]5=,c5KFi0!hr^-$mOP4&H-q#Z]Ki_;k2Pq]u6[AVVZ*VBA6qp9"3N8^\Hf7Ij:]V=6o7=?cmS:mAWDFa>iXNZ=imBO()mh
%rGklQ[2Z;cY4D$Y2Ve,s;/gFmCa:F*7-r4l-@>L6p9oFq%]<t'`J5)Ml+Gsk?X2L$V6^BlnA!:f0#,HTqWWh4+\,N,]0)g0eakis
%du!Z':)qqrA]+HsS2d]c5F#7A6e_!&+8`\9rHmGgcTaTRiImDHbB3XV[`Pr10sD)1;sWsp=aCTPRkq7-PLKIm,sl,o+""Z@AnB!B
%0BLfH+#fa[X7E`j(unE`iYB`HgV"YX[67#@pK"]:3=:C'qDg=d].[Ki\GDsBmEH7:DjcXN2/NDE`GV*_pqWt45(jtsg/BAm/\!CX
%=E@[\j_b4Go%;8e7Uiq.Jr]eY^%5=>]"Q+;0`U:KDth:*Ub_i\7BH/g+,UDS(>'*@GQ%'PX7Q2Rgbupfr;$fiLd+FspGZ9g5GVA0
%n>bNfiR=j&oBS%^Y0PVm\GXmJ=5d!3FS0O+qcY7/f\Og:Pr[@GjniD*F,sWL+!(%sSt<!n?8UFebi>2MI/\5;FZaU4ppSXe^uahV
%du.-.o+.uVDN8W.T>/#<nnN*^/IMCJdIGK#Q$pUi>]EX@Yq>>/iZ!1=buQj>Dou54q"ILBc/[d\`S$H5bL:mRHKr,'c;-(<:B.ua
%Crn'rkr+UU\miG#'m#:6r'u\%fU/^NnG7(&Jbq0<j1$9#Sm5Nkn#rnUc'LAm@5$MES'ARslVf>bn8c(LHF3]qJ#BSE=/i-aqH=su
%'O=[&m+M]amf(DYQ`2sj_&%;iS"J!.T?aR2UrC;2oB`NQPD_[(,UNGg=5+[m*bi!gIuTrS/P"L+<u72bS)f(`5o.V:omK6KXW-jB
%,X;07h0=?q\8$]1j6NaU!7=QhkD'i]WUo]EoZC]J54d%ulD@"a]"%TX;t0Cr3TnDDcf^LeX03uD8S0`a&7$=thfD$]#Mbh64h\RY
%+`;Ljd[Zr<kb#<I=/ihG1+Z;Y53bK'TuBYgH"9PU4o!l&rS0JZnOo=X']&C?g<?0QHfsJRErP_,-$JLuT:XqV=FfT!HTpmS-bGFG
%*,\N^R6R,]s8#aun>J8V=01>4X,Oli-ka87NrSn(^3SnpP7KjE#'#Pa_(U<.oD(WN,Q>a&B-7'Iam$"?#P.Dunb6DJ]Z\XciQqPE
%U]:5+V+]sqo&ba4Ct'J*OCrb:cC=El2"c%;m#FlFp><N0[Jq1PT?!S*7X'YW6B<0U*M2%?Gl#$3@HDQnQs;&YZC?bC!qMtUQJ<ur
%EY&0KQ<a9#ah<#Lh.NMST8g245L*2J*4sP:TqD?Js#j.^qub13jp1$5U7;07s3(u_N^&b`@b>frU[BY@q#.n/eE;=R/%,CmXOP"r
%cg!L&^1Q3u_XsHVIPfC=s/L!1cVDe=&*:6AX+K>p+*p/deJW51)n[3?:Zq]Jk3ci02L=&#ik3l2h2$eFs!.EdaF+;_derPg^>Y&A
%E6!=g-WOj#b@<r3*Zar52g`"#l17shik>9LBE.VrR+dHQb-*+@T1A(_&>p\g,<kr0B6iG=Q<1V24sE&ciI)\i&"`]6oXg052?3Io
%npg]=X3'TV^QucZj'-s#C="%"iN(FsIAtd.dk&aZEPD;4ddGk:pus0@EkD)/IieV>fCk=7K;X)Y9=7<om=g90IYNp3jC49)9LFnt
%E-YfX;D6iNmI03"9C(?8BtpnhFi#_iFq<V\Iq?;=QR*;pGJ0gHSdUs%)>\?Uk?Rc%^RG%n.XUhgmaUG)\=V"kNeHCZhiG8ta(]uW
%+sg\_3dLb\V%YVBqamLujb8,M$gjd#qUiH`q8-GRkF(<lk0oE20q\:`54N'M7g"khk"X=X#kmh\,fNJd0$_)U]VteKhDD^`H!Jk]
%])#2!Cm_*H_9^tA15eCHU?h_"6Tit8/ndr'.i)LF4/T/j%eal\L$P;ukc%!i*"Y\Sa,.,BO7o&**.92k?R.P&F0W20p"nb?a>$d0
%OOE(kI[H$"BRf[A)qqL^'u!<I,9FFAjp.AN:-(mG\B^oG`]!kLoik@/nQd2Bf1S#.WF#5B3^&p9'J>*Pq+9u9rdS:p%#+=jbno:-
%"]$5,NA>F-h_0lT_g/`g$]^_=i%6j`>E%3M(T$M,]qt^LI58%<Ipi>NGPmWZ$<#CR,Fco8kD0\!s6B5mDfOL/+tAERnQM$pCtdnc
%(/=eM`S&Z7maLtJ]8quT^:rHP2G:*DQMBgK[oP$iqPH>hXb634`USkmrV=^MZYU$7$NnIrdE#rCRHP##L#;!ITC$Q'>jad<3reci
%d?eo]?<k4S<5+2h&QZd=L"Zi^nThi_*?@nYQe:!=p",(Dfn+0gUmJ?DpYu&?il3%`deLrGkp']POBV,0H#[o1qrHseQ9\01s2F/a
%CU#R1UtmR:5/31`bZr8JX*IQu&'aou++AZ`Om+rZOJ(D*ChO0)l,-U"P@a4(F-eY)GLra*0+PtErth;_W[N,+>TR2<c]>i1h`4K@
%ldGX0Y^'&@Cf!p20+P9gSr<M_i=`U`rUp!*n^eR1DW]A#o&0CL>BA]9/5:S-mo8-2Rl\hRZKqJ>]Gl70s7U(!hT>$,dIYi)ktU;K
%M;?\CP&4)]3BI3&kk\HHV?d.sdJi5!`S%o'bWYPa"h>UVZF(l%qW)6Qmn9b"bt^Z=HY]li8TR)L?[n@(-&/[N:Hbi':Hj5IF#eI>
%97E,M@.,0iSf\t:fjDHQ>aMX8M=WT=J+C_GQXASHri#I"qt6j'Qh?\`^N4O?Us&V[Mt`kDr0q'mqUmrNs8KRaY@THN2*'R0IeUGb
%hVAoUS;0"@hA:[2'C!j=c+?cdH?&Y+pVH[IrnI8KGOIc((/DE+:Zit\EY$?YE:&&PiVnb)pV+<qs0FeZ7GF;eqeE_,:s)W7^FtUX
%p?XuuJ$V@UbW**/,*2W6+qCO6qq\[Jm5gMcS*H-:B$])tI.6G<LP*jDhp6G7a(YHqA(>F]IksmfgNM@uR-"m.[Qj]Pk[o:mgp[13
%#Oc!Zl]!'iTDe9=puij,[s>Q6i_uNhP#lSISAuF)2]$+bR?^tjWtl`#a4,"pi1oUBgTrr+i])t)XI=q1frtF[qou#\ICOVd?T2L_
%Jq-\+*WP[XkL4\4r:Kf2Q#HW\K/4Ko#eF8!/jKBJlb,LWc?8g?qK1F@Heq?TgqL0T[U*O[O`s!7p?_c!fp7="^1_#;js>>&qX&U3
%0;&*Yl1llqXj6G/K-.'a^1qocokC`s5H%P4iahA5;f3[-=$Hg]^9nq2G58H\cbHtLSBUT*g4C.6qr%DIn7(7ZrqOmim?X%Mbf+E`
%puLH*]Cc`EZgQWcQa?H6#HXjlg%rbdYE$=Wc-pbl5+unPJG`LI*/a?/CmX$bmWY9`f:+<FIEru>X)n-/5P4Zhmp">f7"Y=7mWqqT
%c/8D06^[uLs4q_4hHm\G`c'P"o3flXp!sp_QN[&rIV(rdhj<0PPqRF7YW91"p+*U8eh?i!T$6*X=5bfJf_Cg3/DAFa?R;W#gq%p@
%QB1#CI=?0<\FhkKNs=l4Isl<ZG'%eda0G)]FEG(<34SK@2q,=kB2tce%c7,A:Z),gl,h1O^Y+Mb[Z\Ea)K9tD4Gue;W,siaUPJ_'
%RD4"8m.0;N-i:g?H?2Di[p'!.8ufTq`I9UMlMl6OIT'>8pATC<6512Yr#PLS;/Vt3IT^#>(?8c:OmJ7;/A3Q#_=HYJS6j>?n*'.R
%6S-_.[Mles;2;J'3(q_Id::D;3lU_Kfjtc[.2(Jee__]N+%m'(0>ul>m<h(pEZPm0mT?`*ej$^^%I)h1HO3X(c'P)uoAcsR@f;ch
%]/3iAZbO'cjiOX:qV2B[O"u4X2S3,1iHMHf9AamVKN9Fcb=QcGqVt'fr`60qgVVYARW$L^kOScdk=T'O5(@R90,2Z*m&s=2.sak]
%h!nIDLQee;;LMt\bQ$C_c0)qC4MUmnS"m%M?WIeHrTlICcf0lK>95m[!hn3-])DEDGLc:`-uf*4H>;lrGnZm5I#m]q++KQ>pb-RP
%q0YfkVkJm(j0(n^FH8oFUT\]9r:#a9GB_"(`qiZCWI(b+qi't#I.4oNVXUeHgj`OiB7Cnb>hXK$id*dQIg"N`CRq4"H@7&/]bi0[
%NTU3rorkW<jpM(>lLXbXPcqjsduRmc^B$Fl[AY;KDT;Za?Ygsr2Yjt3NH`rhs,EK^?J:uhml,`G,jjj.o[4M'V)S3s2!_4=df6do
%>F2okDOgVt)u(f.=59j)qW*^3H25I,CV-1uAhM]aO0;<n4?^G\'A#JFd0L?:*M6WM\2rJ2(4H*2gF3)./r3;QqgRa'(&7A!r8R@Q
%^[p]5B%2nA-rtlQppt1tc-b(.q\rJ2IIQ[<R^rFFhie\h]l_jc^\C?aB52(m7u+K:pp&&%QaH;k33V4<EDR_U;7,cQ4+HWFFMCE)
%r88#ujf(4'*0-'J3K!Li5Dg4?ph,B^gh3fSqgN>OCq1a>%-RB.O/Kb\ICn>"IhYno\@agb;]i4c<Q4)Nr/[QnBD\/C`cV1;b+p+a
%R=@-(aa:/3l=kt/Y/@%N44#I9l7_\]ICZOkfY-mmSc4VNJ!YE:MIY7"in1[+9H&-D4ajd7<0\jBYPi][P'c\jA-.Bfcp6<2E='M>
%@/_p2Ts6^:]%2)(+'.g";P?1#332>>Ah]i@;<-^bNH*Cu0"^9_`NH^44bl2liqbJnSCNp!'MKn#[sq0ek2XI7l'AaP=IfLX/P^l2
%ScR9@<?JY5Zgu0XNHs39bNbS+bG"J"jd0Ue5C+rJc\jHjGf9KRQ5=Ifr_0u\=3k)lDD2".omWLrYd5sq+dsKY5<ARL=P`Fpl?rUX
%Gjha9_q&nd?9\5VC?;jMbA1>NdB,p1_km(pHHe6DN7OE;s4.FOO`T>.fAGR6^M(O8WXXDuI'+PC?<oQ>0<=p!dE2B&?e+[XoAd(T
%im&^0LG.h=lh(+5Mu1hpqihGCo&iMEfCprlk2Pt2%pr>JY'Y7Y.Jq[7mucf)jK:8!Y>-.\Q?,$sI.Y[3+8kE#?$1:&jeSuIdmMg1
%bPo(&lG#!Dhai_#b?fIb,C/a&#b>T=8X>+cHMM+OKl!$^IdW4Wdc;bON&rLC0;.?`F5a=6=ms,KhRPg65/2Qgeh`Y9"/@aq)Ue,R
%$_%U:]R!O9A91h?T/g<seQji!T42$nm$$b)K0FI(\TDjt5,U%c%n\&)FDErVKs:<4VWkN::W5WcAa=X`PDdVJ(O+A+7dY/@f47>g
%`u"JW\Fhntm?O1iN"1*cUqWiQL$r9_bDG`Hh>J/],X9sFs).T!eU32'GJRj?<jLm&dN(]nNc0s>@Hnp,rS9\_l0`c.)ZMm&H%D2L
%MehpYPHS=uioL!#KJHmcA!7Z=9DH?N8k<6kh8<56]ZZ5&]?X`.oP><.mp&KVmT9/\D<5<%nMpZMn(r=CY/Du30jRgUIW9fmpX&Ln
%2^*J/jD]D;`PpPrF8]@ofU(A<(4H4G%_r.Y^:PIuX-r]!WPY>YmZ*(+T\cZ>D$V2kIQ#@SiorG3V7d5k\aT&GGE&!6B;j;IGY"^R
%ao;//bO_&0X#u6*?<"tqecNr(5$-4Sb>r5]jR.EgdIm+[S$Sm543PQ1rUM!bao%6;BYuDX*d=QpqaDQqO8R2F-92Um[a`I[q<5'/
%YC5Yfed2;=:%N7bNoaOfZ:8ulC@AeRShI9uX,!iQY%AjU`VFMAV<`Ht+hd$pH&XdIHXqT1(H5^gQf1oQ4F2DKh:HjE_"gQ))lHiR
%n_eVPSitg+@K1_hHle,3nRiB(rSce7T=qZ0-JZ$Tj_aZahgPtc4ZYgRJ\lUnf#U(b['89KDXICfCqdGd\566#Y$^<&<`'RDA&$\s
%EHX!8rnP%N%sM;Fjg"-T:G:nKk5;C"7\75\YHq<^HY2BRqbc$2[pnZ(24U#U]H;Uung?")\:49cmC(nWDg(fg>s/-p^0&r,?0c%"
%+Lt,oZW<uk<p!Nu$G'+YD*R0#/Zr.Fe02d^/UQY&SY'SSnXWOrr0:fcO:VEi$;o9Q?a]?3Q+m"9pp9k1c'n\*rjueOFPfa(loUQ#
%qkL4ZD@`nK3hN!m]0AF6?[]14lfKKe@ILm#:]KhPk<K!-s8B^EGMiDp2sH[D55N7KY9#q*ihM#&Yl!O#J,]&?5Id?cci,kqr1AK;
%q#B-FdpIUsg&KscG;VIBlfu2[ebk1KkA98</6U7YgWMV?CnE`"J,KO"3N^doqpp2+h,UL$M?H3&!shd[q+E9GY)Be9OZiX=onssZ
%"-juK<WbY?dKUgEEnORRfAdtL4Z"0>Pu8<$QqlkR.LY)."tn+GZoshE:a4AA-BVkj.WCMrpLo<iWtjO3#3/Z$h>6ZD_MJ1F\h.T\
%X7'O?\fKg8=Y)94\?Hh^nUN/+=#7@l#LRB'fHdO>f`a;E<6J*5R+HTfY$?8%4;M4oX'Tn+7HIKEfmSS_2hdmDUXR[(h:BrT4#R?b
%d2$Nc+oIH^KjK^5pf?^?"L@RQp/)tSSTTS"pil_ALlJF1+WaI`ZA6PV*K9!#(ZjrPClZOM#7^O<F^lY[YH?*O8[Fkr1-M]rXA9&A
%@gb8-,I*ol'U,:l;qjePl;[T20?_1RMXZ">3TmI2eiOlsgkp<\%68>bq&B9;rRW]=BOo!69K/YTrN6l!4Z/cq4%9>#[Bko%;0+-]
%6W^CX*gEjp\&nms1/X_U:K$;0^]F_";Q>C=VN^aN$)R<$QHPJr7>Z;L/=+B>R3-cN27*k5/Gl:hTlg'\ZM%!tO^i1+4<`uU[Xdk%
%-pO\=@Taf7m='MD#ND]acY(8jkVAOt3FfTN'fh#hJhr^PkBY'h*WPut?)KK31NYV`\s@MRT3QA)G1#E!Ji6%0[[iLMSY!_cm!TXN
%,/3timENkFXKZP?[1Dihr2FbC&0$b7X.3lAB40&N;fPI,[[NY\X?]P66oL"0iQNN4n=F9OHlAn2k?@-p7kp7V^cT/p.p$3=<+>JN
%+]Bbt1D7*UAC9d!W&&XQ;3bB=o[0nS7=S`"s!!DFb"W^]pa-E5jGG)0"q0?P70t0.^>LREFbA?f>m"N]2iiX0-TNm52=!RDh0X!E
%FD$Qep*0<jDn%aOaOr`nd3CU,DU:]e/6s?+FK'Q&6p!nl=TC+`":H4?!iqAU&@V)*:"Ws@4lOKW4bU.P:^C.9=!udofS]0WgigOr
%?NpgW>:^B9=dTh\nR<]0CQ0]$9-?su/n9qRW;_Vb,<:L?ibXeIeVG:*+-2PPLnLRf&>;q35_SI>3>'qA:."J2<$L$Sp5,0uA0U$P
%@t[CA%ilio3#IL2q^N94Tt*t[_bN9j[.#N=1m41n-'_ts)VM2(p$9ac2#"-in_Od/&=ZG$-4ml=VF(clS;S67b8"L74ICrX,a9Sj
%ONS5O$^i9K^=JG!(H`#`3,T^mMi1=KLt(p/0HQIK_4hbu#2>kT1H;.<dbm<Y#6lqBoG\Vs[fnYoDWOM1`4XW4nG@AL6c_:s(a>m/
%d1BTJ.u&:%_\uG/eAdU;,R]@D,f^hGS'oSY]@<m9((#XTo#>)$=,=S!Zj##:Iu6-8CHu9C4EM$fU6n8`0%LqZ1fR3sUP0.1N:uA3
%@<>TGUlf+iU62c9:8><>SrJ;PIqFY(=78U9T.UtE'f'[N2V(0(Yu>+keQ't5C`2V@@ok].N5OZEA=Upq>N:UdXFQ:M=,nnlB8\l2
%)%,>jam+c/:!R'S@3YZ<Q&-`#>EdS$+&/HQJM9Mmb%aO(ZJ1Ub2E4XgFUjiY*ace6n^`VqU!M@Lc#T5+Bg"F_9nuBY@j_-*+L!S)
%Rea&<H*qh><.;>5o,$(o%cf2bZU2qek/="$0]t2W]=B,1,3G3MdPU\Vl_Aa2>4UfUIo%sDB&g"IQjd,UFZh0V4I5BN_?pL^`E/kh
%QH2t4!?4[`Y@fDsk'C[>>$OA:HRU\T%*9f2R8:B%AZ%FKQE/csYZf<]\(*Y72Q^f/l`p:Zrf1"#lre,(:mkFDSP[tm=P=89hK=se
%cM3^S]$7,Kf#p)e7a]R$$5,8C9PW:+M>,\T/+I),`ViM8%Uu69\uSqLS^)1jS"pQtgI<?3gI94$lrI[Y[X4dpU0YiP6hYKfm'.pu
%lIfM9X=*^R3ngeKp-cp7$rmlYqf*j$%^UJ'3]r_:dD:%WUEc:>f8_+dRQ=',rQrsG:O<SmO#rBpZJ0P-pAMGKDP)-D[(,QdrCOoJ
%W=I";1[e4#c:4s^EXiOkZZDBF4jJtZ/-$WUX`%0DF?Gh4"l0!`.s?q+?%qh%k\>6uN,(O`@]o*3h/F<Ve[=.K%QW?scRlcjr3>5+
%ChA$<Sa.ut6)TidY<D!$q`93S4\hiEoC/'n`Y2Jp&0"EBDJOL^fmq1#bg_fI9mX;=)U_<plRnMo??'H8][:N#k:QH]N;(#OmWehn
%SG-.PNC]O5%ZjB/\0f,K0M\Ju73bs-Wss)K@Q8B5N%&hjQ3B/\Q+)+>@)]jQ;F<30eb0&=NMu'4g*MZ"g3d/J1``qal-MW&Tek)&
%Mf(R<$$nA/#kTieZncg4Z=A.15%(,?TZ.ZL--^NX;hS:bk9=\T'M)WLH7r,C)[luf2ViBlfVZQ.@Ab^+R6<@'1Zr")/tCE&$sXtp
%201Wn_9S%rF-8Vr*!5SJ-t-GX#"_"]GUn0L>j:jTNGZQTV3XPhFBDk2m#sCIi=`gt;lGK8[4jW.*Q!)R#+Jkk+U4+OK:%KFXS<k-
%kJ_!ZZBC0##VFHQ9Mn\=on-5$/HX@.C')]t""p3UfdcfFE<?7#/"H+:VMg(F=UhCH`c'"9p3nogM#13&HM1FjDUq$dUojX>ODG%j
%Run(HU8qju\/;+F6a:,3*,"_mTKCqd#"+E-4d[<D`kd,[PR/DZ*#!?=18drF*HaaFN$B)&^ZQ.26AO[=&pCm^lYn8%l@o\%Cb\&2
%]@??70U*d<6Ga\("i_I[fZZ`oZ<\P_U=W\obcndFA!>JKDTj%UH"A!K'Ph*i;P_``X(2_$8a.Z1UN/>RS\QT;aZGlg9P6`?b2neH
%c:3Z%C2.'&W_gd/eTAXf;7hgAX<%SV9$D&W/Rs\%YfCK!6B3W<gK*+`=InK$mm;opVs@N6<4JG3K[1]5E,MqI8D3Tj9qlDEk7U:_
%rKJS(9Ji^a:[;P4&ITnoNm0]JWb+RHfkt/nC@-!t[?;0#[/EV[e"NbgRBBL*h=RF*%:s@0p,Ne6n's3J/Wh0#/h^@sG\UfBI2_Kr
%Q`XA$7s7aJYB"fDW96K?-(:mcQ?7#@pkcX^QnS"c+)C$YS2$Yn#!-M8PDf;GC6,.uD8Xn(36'P#,/"g3MlC[Y>3oUBQP.]:>t\XS
%D^Z>GRi0kCAkq2h[X!dA3R"RXEaA<lbM.Cr?1CZA[7\;?*`[Liqk6V!3mOa-%0rn%T<1q\?Y6(%@pCtDP]k&JH`oh0[8YVjY&h9!
%<R36`\baM_>;bb>K`s@DGs88dhF4cEELMSj9g7692%*0K^H#Za`\$Ah,dc'=D:IjZC%7`2/naXe75k'MFntb"gR(.YVIjI242]9h
%m7D41lI^`!U(]C;h`ICV+k.gL*Xf4:qX_Eu[oTM>]DLk_kcmM3fIIO%ZeeT!eG>b?/_3_N.%rkC/t<#*S%9*(4)-qB[4gR6.5gB!
%_i]7Tp`:kbL=]D02"Huk`DM%]09/ef;0a_T06LYYY+2KbGb7P:-bO6/RZthQBsOkMm8Go;EmnLe?(#T)rpl">*lkTRUNrE4B+\b$
%cqJ1\W*&ofom'=aR*s9p.!iAq`FaNZ/_Q0?^F[o_mH#sm7>^8d09/u39#nJJ*Tj?Z>-_rklaePRlgsBqg]V(4ERVKse#f=S%;d>@
%b')O7j6<,b-<YOI/(N=4]ju*N&j2M/3ls5PMbtmATZMfU:os9oLV8mB".Q&F#][mSC1oR=N+[HoGe3QHh]X8jG4SD=m@0qHdN:8L
%D;"5L^$So"qQ'YTs%MrXgkX-Rrik=ST:Yeij0lb;BdXf6eMqjuMnU[q^\kAE1JdI0D[#OUo@G[J6pA%VqbZZoLQOgrG3%,hc#U;M
%S1gK(rqc7%PdZ1*g9'8ZIYZ6B2:)0FIJDU^0\h*5[-QaQ@$TPrdJCmp!]F;f[%2b;d-;]2?"q;VE%@&L@_;-SG=4Je\89a[Ep,NQ
%pf]Gm)h!iP,*ZTT^21iYMk$q[+WdD"U7#In,>"KWPra,OVMN"MEC9l2_`;b_csC)nnRm^-X]G:mb`el>,f!/*0R4%c<KG0]s26EL
%B`UReNLP@5HfCd&\dJ?&G/<MuHNm_[(=!J0]Z*t&e500bCBN4f1GV,iP+"t(AjX2\m`<qWVuAj2kBaj[,d<;Q,JLKslVq0PCYY5C
%b^^/R=+&@J>h)ud+eVtFdl12@Qfpb\Vd;28Yc)%]H]/(M*Rk@Gd-#(\hV5r5jbRN/K]3bK-Fr+omPH)U_oWYG81U0N*#irF>BI^T
%r;kTq=I`"?2*b*[>!+jZ3^WMHE<29!i>PuRSZu=@Ts:Icmnbtq9`s:YQ_#TEF4fbA3BL;&>Ho/F#OE%PH@_A#G^mCGO`t=$eeu^-
%]eC`B/mkKZ53bm`XJX<dd%"L7$4V`[pm+eG&<'<EJi:)RWbd@J<pN+'CQ:e]!FmA:OuP;a<)[Yj(_WS?Shj38[!hSffYqr"4#:t-
%`&iq!/8`,;QZbo"+_>NRh)[geP:T:JNB,NN$s;MBoDO?pnE<.K;o)Ia%662>=FB-;`<5s:TFo@\oqS\B5bgfXkS,/L8NK54R#>"_
%o:SjW&IeF^X*bF^o.DcE.k'24%Fe;(G7hCs&L^kfN+u'i>fIA5g;iJJWsgQZcEG6.<F%W8R5]2G%>RuJ_N/H.5Z7!)jQ^3%Oc-)V
%:9N"RJq,Co>=N9_H&jffiTjcB)L,W9g06e?M&XbiGr&W>YKeW&$!&M%*SR&-UVEL*#uLmbYs.9N.r7!^O.8t')XGR(_UqI)[lj1g
%6&3X_m%rQ/D8(2l=nO^RBuWT;:m1#N?>X\)6RcQ7o>.%%\^Un=psD:bY?hWX21<#af&d3Q5,nNnrT8r%AS;7/4aNFb7o`j<dPPjJ
%*:2&V?[e&qaFHXKSk^mN``W7H&ksTpF^u/+5>>S1"l+_iEnQCA9s')B*X+C$MO!3[,8YYu/fpR[7?YfP-Lp;1gVT*F$TfVu%3KCK
%M"?ra%.qZA1&+$?[&1c"VZer_2G\m*.=dbPaI06:s"*WZH=OI*>nRC[8<TKl2ZH&jrmZ3XM_<=K*f!L7]1C\9WT9g]ri+5L"eRF%
%&]f"<@/<$7j#ZWo9Om"[3U94@qNJ$d)5L:$12)S]"D64103<5lH)+b@%HB1Je-%Z1-Q27*20+M`p3A]:iI+ESr)JW6f14#D"*TpR
%:^ba<[P\Gt12)rrRtKb3?>5d[:hXEoo"s@J1Tee0-1HtOI]LhV<!>o<RQX]NQQa:U;.m2BpG+"Yj70jS=Q#3)!8^Gd[CFV>V`+mS
%$L=%@kGbuoL=O4+k,OGoTZC"5>eFckL4VO?TB_>sbL-N9N^-UEC*o-d`re'ANh>=fbT/dIIPhor/"8B[62#%X*7^\BRfPEF+*iQL
%Z;jADC4PSWLp7L<)-qs<kpRSg3-VW"gJ'155K/-Ih)nO=2Q/TmDkI[[J$c"@]qM/tP64\a-D"HSDCGnjnO&^+c<%;n&cF-ZQ!T\I
%7'eX*X!s:V(.G<QLe:B21X]_f=%1)W9_*Y/Z,$KV@TB_-P4l\&K6\om%:P!I=pS%oUahbLrFl756.:(W&4U(kI+&1ccr:"VW?O58
%e<(AU6hf?d3!e?ip`kMKWZ4;E$4e`E'cFhfd!127@;8n'gi;^2gbfnZnP\s^7t?5N)*[g?f!,Gb/15=TD\`=2Iji$iKLii@.i<T"
%%s(!cE80BIapeCoqY+jiOJT;YWJ2]E(af(B3[Oi<p<5&`.YnPFdCOEi+XV;p>U(,sZdG4O.5;9:mfpU9peDt.kUWl'neQ5q#HsKV
%)RLR!q#]fLp^`UD*j0YO%E?YV4!BMqB":;&?hqd\5osdl+V9-'EZ"QH%U#p'+0>Kq(L8@n\Z[SA-/8L&kn4$^Nl(6bn6Q(;W/#pc
%(aA^Y'@n>"M=;7[m`lSoM.C/pW=R)H[hcQ0Og%9U6`a_@2VA@b7ebdN.dkQ6bl^7U7"hd(&3%o"6\jQM>_Ajgdki@P+#qj`Hq2QP
%4ZN.mc0'>%U<q.JIu;6l,A]QF022+m=5hn<'X23Q^@%E*kd"[e\;]':P.["%ZZuaQi6:/tLaH3hR2o+ted6Dl%uB.[r)3Z>/XZ_2
%rh\n9prO`MQoF!4lJ-4ej$XD([tUbY]]V];Dl_$7q)J8r"OM"3A5Uc%^7T,M(aKbi4Qf+N%^^%>ktgF3c5qs?:8&/n,N[u2M\,gT
%fso6-)df17QS=fYK.h%3A#+"M-">0tIZcORKDlBWn[lQ\;!&_aY"qas$.FZdV;NHIb-KB6-,3)0S1m7/*^B;iBeDt+UH-KGVN+dS
%]5.R_N6=YDD:!_`)%Z7"9Z5O(Vgmc3#_/<%CRNFsPN;bE$m2hOYEJffL6"Eoc-ZE&5/)bnFDH^B"Ig"J\9B@o7;IXla`]hom!ttO
%HG5tnAE'V;P'Koi7d^5Z\Yo=e^Ad@Z#fX/`<@'_A7cCHL'`/.t!(hqZescb(BeH-Ji-f(A8*>f_=U4pfh=r+L[%:mV/*(>-f.BnM
%&RR>tF]\?P$M5TV0tF8JAi29]<gM-%4,T%7!@`)BcZChZ\@oEBa&VPuG(pV3[pEW`=<Oaae_<cPiBbT)=RqH7rcE'%L%TRf,dWod
%!@!o#`r;gZeipX3T+(T%b%=_9Ou"PUdNNRH5la"q8VOi-,iu%sM4:8^4^ab&:R:nrp\hoKo:Z2jNDOak_-1XJBo76tGY68#I<`C:
%WX9>fVI]`\=:f\(q9W;A6*;I[/!siM:NWC%86F/fbN*LFD-)#j;c*p+kgH&V([!="1gX3JBn1kZcCK/jjo:dW*X+`Js-`NhQFm9P
%3hAD<+l;NGN:GQg3hXaG<d[_5hi\9*Cg&8&,EV]/[eV:4Pk()##+khP2AVI_gUE6@e4gSV;KBt2BWW$h%d$N!XPQY-d%@fnhS"Pi
%gm1M_d97o"5>A"ba!d+,H,OHGQ=&<eFlL&M;+Ku[neGg`GA%b;FlOiIB\Dea4o&ekr1+cSh.lfI(d-Rj/Id-ga\Tbr:QE<9FQ.rj
%*0O8IgL$'4:5!m=M&&8:m>&;:edbQ1:=F+8#J/6Qlu&0Y.V0c,Pg;(^K:b<H/GGjAoHJG,je>$pR&M-,Dk-?DW+=_<W`3uh=nHrG
%<ZQ6[BmfRlYc>(HheNM-?ZQf3Y.AkUNtR:sgtjMeSen@E'CXE.8=j%!*Ek'sSXnD`5?uRclC")!,\X4_oD@62$;`TA4V-\$4_JS9
%Z3Scla:MO6HmIaU=Oak"Rn"#T`QCZWg7LdKMbc(Xc2_#m<Af1W\brepBHH\$]FDEOc5l,,-Mp^CUCAo)^_Sr@V(6(!Rg,ljN/ai<
%#BJ/:id2hOaTA/2Y+d7HOdVZ[<%5fRRi^uH&$MfG<K!ghmi*iA0Vlp@k*j#aT(3")l%D:8H5c@9a`p1#(Y+kRos1[r=RjE3DPaBP
%9cD8P&,(PuFZ4O<1#?@YJmIcbDkck!HmCKaU'TJ6g:ujA<`Q3@8qGf:Wc3gh_1sN:dZC8@]S%)4,'?GA],W4U`^-5,#)2/d`uBX&
%@.P7/k]Mc(]L:$YLlK(9LCXBq]o%Uu!P\X_aVX&j0:%IiQ,?P_W5BhEZ:FiK=qR^DpRggqf`3M@?6m<"D1=l_WqYkI^I;g5>UmYD
%^cgSjTTeoaHG`>ebn@b-&qPT]E[Q>=^IdG&HnuAZ8*e[b<KJicgq3i-8-UY4ZXLYOcr+M_')dODR7P+f*7RAb>t=EcHGrP?5o2#+
%;g03nod.):dL]n.TfrP^r2(NA7foB:$K92^`nnb2pEt6#hIlmrRa*.pKkr$q7?3?@p3$7"'b"bT93h7];6%&fhj7+rc]H^uX==TU
%Gn/k5@O5Y*9'ucj^l,IWXIiG"Kb3uY?Z3W'l)pWX]b27!["K2dNXNXq=nJh7VFnEEoh9H&=OKVkS"e436:^[u)L1&-5[RVg?^,B?
%b2VV=Ir[X3\&'S,',0MqK6]4<0Z0:"<'(]=lfm*8V@Ht)h"8u1*gTFf'QCG]/nr]f*n3l0[S@#g/H$/>FNLtUMsX>2Y$A#/4b;Wd
%A8F@<;9EK.=l2q'b*>]>.i5"l(sR?[kgPIb^t*-_j7_;_I+3iP/#(49F<gX.Ysnf2FVS.(/Fk-?f.=%a__N-A#AE*Q`@L,VM^:gI
%D%kn6P$Fq3j)TDem)rYV5JlmD?K[-5*mfff-"&["a,i$=(>`RA1%D3tS]@L65Q>]FHYS5io9">B)A"d4?q,^;9<48aq%XiJAZ*5)
%$H@:t00IK/$Pc<<S]NV-/S9G'%Gk`G):pX1m&&,kX+K&2[Z$j3jD8jjm3aTIcNGd8\RLMpj^$;SN!J?PoVV+.YPm[j5</NJqC/^m
%7'ThA?BgBfiOY'kAC6R?Rd!k@\8>TIT'K&?(9l:Bkp_Mhm^*0I8*T<q\Z5C,*F=[h]@Bl\BgOV8NB.O@%!'JKX[>oi(fB9\Tgd%n
%e#V?:qn-0"cI<+=O3E7$%o`GQj*5DF]]/Wd1F%2l#!YBAef^$QlZ5Z^Bp;%FAUR.CLq>n,`uC#_`i(r3,)h9<Rd5k?`!q0'1S@#B
%]dZ?Io'ohb0)h(MfG!Ol7:3g7q*!+bg<hFBRRp3ir6h@sPMb/q!ku6BQ!&W?p>f#N'@EUA2>WpXoT,dpl54jilHSM[U&i8DpZ7o=
%>HV#OV.\]Kf#MGf<gkQ)SC<.gmT-\_(jH@q'aKOs[E>3o'h.0M"tpDl,q$eoCj,chM+b/WVW@r7G@hqu^W^K.9Q'NB>".[eqiEMP
%Ws?j\],+h,o?=h6p'+lb$Uot0-l8u@R%"ocg.Ng(9Ur>)?I4q>]rlBhOZ-Dp8+Z$A*=WgB5@[SQC0\3C,CsCTo+Io^7Mr*>&H\V0
%7BYbICmtG5+`;Ibb<)mRfMR'Dr4TnAFA^LF>9(Bs=/X!QKgP@kp-jYFJ(L24`",+2eZ'@;D0K+\e$aQ(jcA.o_e.tGD]3#r@Cd#5
%Pf'glhH]2$Bdg\'ec']H6<@!SF1`[S\osBekidgc,+1V0L'[K%jg^!s4_gP\q&lt66nkS7P6m>H)\jidI3<M`p"YV)7,MN1Y57@U
%USr"a<&N'NDsl+Xl%2+lHRrlG[IV#Y2*U51(\edRGi;%$@Z;MV+"=/j3DLkAO-#^%rYl!D])eq3g#Ut&rlA66ki'pq7rRG8=dXL2
%]&\Q_?*8.[/*O3KIL_qF;j`?iK^(pQdQ4\V6I3-##<&qH)l2^a"-UY+H'ZQ;rp]P+&_<:X%9-?JdQ<2R+ep0h6R46VH+tae*cCV@
%lLF);NA>f@&qbR3)9[_-Eenb1(/bA81>a`&@cQ%Hm2n7[/(GSC5u<g>b?1sYW53Srh>3j$>-m1'L%\+X,c]OBk?Hqcqo-=2NHmU`
%jQuT:YY17OT,55LaDV7>2nd8e/TIqt%EA>RJS!.bLT#KAnAia,%$!3Q_.XCQ?5_T+3J.6mD]O3Ga.K:9oh?3,CV*$Qg,CjU;KNM=
%^o4c"2WVP6ZT[(/*X[enY5M<!?RFMi>04"g``E7G9R4jO*ZlW>Y^RB3?md=H<c_KdI?u.;P1,#t:jQCipVAt=jcXgrU'MWEQUW%D
%6,=mBbfD.4FlN@k5?17?H!dm9K&02]0VW^q#r;QVNuMeRNj:&pE9Og(qTA\oh%(T.rJc;EoYn:>SCDc&E_Zo\d-1U=Wh*_\&>\Fe
%RcRq?C9b3=RQ!ta=HZ+:JY)S'&;^,X607^YMQbo8i\(U&IN^-"bMm-Dqe?p"mcRpuroO;R]UKef@<hb:9XJdWhP:Q#G)3l``*[>p
%U#W6EFs-\2)\h5Z&^aWld#qnHg?>#$??i2pN_&72e\25pI>s)t].BuP52IYbH#/`;ePC($Fmo3erCiPIFfVG!i@t4E7iA8IU7/PC
%^4+cui$Q;=bb[Pf=nT/WicDPXQ2sW_\I<\Ga&-!SQU1H':1b%8C0Pl'Vp"+i>tX"k%M5:!"<!Zk*dc\u2SQd@>cYs2%k.r&lppuX
%ZsF-(,;fn'()VVXmCQafQ9pSGBs]UEi;kifeb@]`f=]ZOeIdHn>qki#CWqLaAYO<5rrnEG:lG10&00hDf6)lC!bBt36>.H+7XsoS
%q-7j,C3a*C=CU?S!qTq2F?h9Uc>D#rm\WEY)=4KrP>egKD4<l[-K7a7`;B7np.Zm"ITT+8pL<'M5rmo'$1if^<];`,$j5,9j&9n+
%27e<^m^O(%>]3D1B-3[kY)3D#rF'fCrfL#>UMH.F;15?h2o<g=G>dLc'Mp"XZZAi7Zr[Sm)t)$(RaS[?A%se_1S11TMN.NIlCSOW
%30EoMmsFWS]:ffGQN!OMN)7hZ+o<%-?kaWRQA=Ab%.t6fb-*lXI2dm_e+n&5r2FFYGeQ#3U\]$-cQA'BrGaKT>Lh_P5#"1`qYM^V
%C2n]Jhm)1=]!nHnT:>nP%hEuH%M.8Bn]U1?mY9V^b3aEMn`$q5:=];-471Ol:\tuV\Wb4O40A#,EC`/$*Zd]&U&QSFNT8"VApN9*
%k3Hk7?cM[ZV#PIR_tsFp[fqYA!arP7B7F+bnM$:h%K@)ilqXN%YPrU\hk.1QPH&`ti%mt;b822tltP?jq7kA)"8XkQ%5>N%$U0_Q
%*ps!j2lLse\3)tB8&IuCpn-e6?Xs*Ac;1=Z/FS?ss"#l[l$k-9md.E%p_2[UpU^ag"tG>P)-tORpDmuISU$e/jNikXg>/rU0DQWC
%IlPuCrtih(1=6&R4d!Jfq-"Tm\[FZlo9O(F2pl!\2i#5#:>`GVo:@U"%L:^2C1pju3foa-d7=PjruRBrPFBmBJ"OTs^VeRMr8VE<
%QaP'Zs7;^3k.V-6:H8-K?][1]=GQSL01+SqX8MFpf_`ZED^PHqCY"QqYf?9PO?`QYLS<f'C=$--(E?bYpaG.XE4Pd=Y(M(#4&7R<
%X3^[trg4[>Sl5N7@Kt*UY"uiDqXbHDDqOPT!<qk#q1^i6I,;O(>:,X$#Q](_]9JFds)?Ke&oca4q.disg*$-j?*^*c_<=kbjjQKa
%Zd,d:Hr/NmSbXa\Hg9$<qc]acV?-R<Z-<5["#a>9N8Bn(55ks*rTpnr!3($dFIIOu'#2Eb<8;u29DcD?AN\b\CmG%Z\U#1no6>/J
%?@L,qjmRiF:V3P['`2us^_aaIIT>mhrA[U9*tEl%\fM]6n\r4U?OA6#2R`e/5N#:3]htl>Gs0kem';*[^)gl]h.gQ<BK(N=k;@Zo
%O[Hm>!Ftk6]?1#In)4HKN6Mc"pkQhu#1lp!D!u>+p%MhCS+`J-s6-X/]hu037WTR)E2YTTqYMpMj"t"I(4D@N^L_V6I1TrcGbtoB
%j[nm-d9D$E7OQ4UGcpp:K9qJZ0Ul6<IIk/.,l(e"Nht-h7`diM;gYGJrV?=DnkDT'RfE;oheieq)"jDHkr0Lfhi^@fADd.Y8NKhc
%YLWY?afaj%!.R*Nc&n7[7X>20,Uipj!i;V^1LLV%bhPXY6'"T0ii+k@3J`ib6r9W7PTV8CG2/AF)A1I["s!Vpma?'^K!Km_!*6Y>
%G*dTGr!a1[J,_sdJk(tLp<CkOU.=3giGGW\P+j5]dPj^lmrpnLYo]?r3-<2dBO7FjU8S7U@3o51,\bUQ9nT7CJ4`q)7$=JSm?(,J
%>*6mGo\13d;M%Es=]$^2)%mcaZq"Xq$8_<.U]>7'a[8o^/cncl6rq6k'KmG:8Z&g]72.%U-GYY,:g3tqd^U&!I?XlYQfL:2a.ZB@
%Xr*;;D'bW@1:*.!',VWUTffElU5lLkLZM*!PF2Fl?iaCG"I)_OhSV]f:_E)g=9cjoUkb;@$<WrF;A=!V>2Vr.d7P9W'X4_WNoIDi
%>2W7^i'9$VXbbN^8fgH2osQuOhp;et@6"Z2+jC*tRLqXj9l%ER,Z2KSThm"+`.J.k8IVh77,T5_MB']-o,KCB"AFfM4Z$g][fpA)
%!''61kmi8s\Ut!KNblSf*t,#_ir'A[9"Y1!e7%rRn-'ZpJ,_D'q_&3F'!R\;Mo\2/6)#4W$9ZQX,`i!QScW=k%k&3dJ1a4jBa%d=
%'G.Q=K)hZO&2%2N:F7KQ&!*D%A-'nWKI-t&>HKEk8:2n`nTNrC!pC.C=+HD<F\r]`:+N%f:=D-]NC8W5,&!P`.;VqFk@gqH0S[O3
%B#Yr-^s&#cC0CWl"0PKMl8C5&LsS9(+V]LZELJtoRYOI"(gms+i$Il]HU9//ZFD)3BmthpBWWjoq,YE@RS*Wg"WB2U&TU]t.1*A?
%]7DG`=$i#T/j\`L-%Y#4QN/[/ECgfT]+Y>aJ5*/%-[?7,S;ri@3$A8::2<LCi4L>;Pu?OuDl<qhK@`Ei":YhWOW:P@Z,l0K>8I,W
%,8N]h$"8jI#Q]-QXFP7]j!'Y]oKY]`Jn?5?@'>8*#QZdQ'MM75Yg7oO,0Ag5_35l4:9^&&kTPKKY`seT_]\>FW4sGTKb]5&OO;T+
%8edGVi?;0?dS8&NaKShsUHtO'KZ?Ng+Qsu!`$#X-!S#+mTOgZN^QgQ*Y/j-8pRs/hEOWA#Xm(^/i=D[m8d-335uI`cAjbaspaB=?
%aF\5^2#nZDTE.=f\r=5D6I\7C5t)>&.4ril\1-HhqMZ2"(fA__bMFr`V:[K3#XYFq,U+m.70r-]Ht#@5\1L6IZA8`5C7Ra]*$\<!
%r#c2A3,7uP!t+Nk+'5+seN=5N82=@+>/67:\Kl>-jq43W0Zi5<5ciN$TFao47"N\C,)QI;bol4Ymh85DKV6cu,[)_>I]S[NXf+0Y
%F'OmI:<rrS^ognDO9Kkn$F,)=>(fA:Mios5%`P]^,+`4\5iKd'4UJ/0L2MR\W.9G\ip[f(6#`P9Xb_T@UUB4P*X&El"<5Bn(d93^
%>U;Bee3X`t$'\K/$G9tg#`"p.l@H"@-j1aL$%g7^6Tg?M$6Wh-4^[.>d1*J*E\)?SE/qblQ5E5T(S!Cp6'6hU2]*fj4!&H&1kOpO
%"O44d*g]uhduaam"993:04FV9:s9JmQnTR&pQPc.+n%iG!%ge^"!J`tE=o10pJ;"VO`'S4C)'nA2AUhdYZN1WVAudnGVfV=75VsU
%>DS%;8C1#GN`l^][4D<P!^gZ>PEt'ujFbk_/V%+N=fkLgc46fY`*6*&"61#J,"N;6br6fF\u.-mLp=uDr?H8KW^UqRG"r=VLb:.i
%-nQo8>ERLi67JAMi_hm2i$&q@!Yll^_*u#PhpBIO\?j!F2H#Z@oL/he%&Y<Y7pMPiC;(0H0H<WmmL#\eHN5*a[@iDjn&["bI7J?K
%8+#c[C+'1=A9[V=?i6D<rmko`rE!%IVZ\\0_@m![qjTpY)84S"_PC[oV@E,Qbk']\md.j7'Q*jJL(;\Dlqc*>o4%_1.RB'^%:Ja,
%M#[G*rrM6TRZ%~>
%AI9_PrivateDataEnd
