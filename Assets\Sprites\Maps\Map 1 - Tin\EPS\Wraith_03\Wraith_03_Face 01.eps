%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Face 01.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 160 128
%%HiResBoundingBox: 0 0 160 128
%%CropBox: 0 0 160 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 5 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
160 128 li
160 0 li
cp
clp
112.669 92.9863 mo
111.614 92.9863 110.613 92.9392 109.706 92.8501 cv
104.444 89.7895 100.751 82.6255 100.751 74.2759 cv
100.751 69.124 102.157 64.4209 104.472 60.8565 cv
108.061 59.0291 113.369 58.0046 118.161 58.0046 cv
120.631 58.0046 122.965 58.2771 124.854 58.8526 cv
128.049 62.5498 130.078 68.0869 130.078 74.2759 cv
130.078 81.479 127.33 87.7973 123.196 91.3638 cv
119.998 92.5015 116.055 92.9863 112.669 92.9863 cv
118.162 63.0049 mo
114.553 63.0049 110.723 63.6787 107.971 64.7627 cv
106.532 67.4736 105.751 70.7978 105.751 74.2759 cv
105.751 74.5591 105.761 74.8403 105.771 75.1196 cv
110.897 74.6782 117.391 74.2798 125.073 74.1255 cv
125.046 70.0088 123.938 66.1572 121.982 63.3057 cv
120.866 63.1084 119.557 63.0049 118.162 63.0049 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
level3{
gsave
clp
[-16.3803 8.73617 -8.73617 -16.3803 118.428 74.6103 ]ct
/0 
<<
/ShadingType 3 
/ColorSpace /0 /CSA get_res
/Coords [0 0 0 0 0 1 ]
/Domain [0 1 ]
/Extend[ true true]
/Function
<<
/Domain[0 1 ] 
/FunctionType 3
/Functions [
<<
/Domain[0 1 ] 
/Range[0 1 0 1 0 1 0 1 ] 
/FunctionType 0
/Order 1 
/DataSource <~=f;<#=f;<#>,VE$>H%T&>c@]'>cIc(?)mr*?E4&+?`X5-@B9G/@]]V1A$,e3AZu.7B<_F:BX.U<C9mm?
CU='AD7'?DDmfWGE45fIEOc&LF1D8NFh.PQGImhTGeF(WHG0@ZHbTO\IDGm`J&20cJ\qHfK#@WhKZ*ok
L;s8oLWBGqM9#YsMol#"NQV;%O3@S(ONmh+P0O%-Pg9=0Q-]L2Qd>^4R*bm6RF2'8S'q?;SC@N=T%*f@
T@NuBU=TAFUY#PHV:bhKVqM+NW7q:PWndXTXPNpWY293ZYi,Q^ZJtob[H%;f\)mYj\`Wqm]BK:q^?Yb!
~>
/BitsPerSample 8 
/Encode [0 63 ]
/Decode [0 1 0 1 0 1 0 1 ]
/Size [64 ]
>>
]
/Bounds []
/Encode [0 1 ]
>>
>>/Gradient add_res /0 /Gradient get_res clonedict shfill grestore
}if
np
105.771 75.1196 mo
105.761 74.8403 105.751 74.5591 105.751 74.2759 cv
105.751 70.7978 106.532 67.4736 107.971 64.7627 cv
110.723 63.6787 114.553 63.0049 118.162 63.0049 cv
119.557 63.0049 120.866 63.1084 121.982 63.3057 cv
123.938 66.1572 125.046 70.0088 125.073 74.1255 cv
117.391 74.2798 110.897 74.6782 105.771 75.1196 cv
level3{
gsave
clp
[-16.3803 8.73617 -8.73617 -16.3803 118.428 74.6103 ]ct
/1 
<<
/ShadingType 3 
/ColorSpace /0 /CSA get_res
/Coords [0 0 0 0 0 1 ]
/Domain [0 1 ]
/Extend[ true true]
/Function
<<
/Domain[0 1 ] 
/FunctionType 3
/Functions [
<<
/Domain[0 1 ] 
/Range[0 1 0 1 0 1 0 1 ] 
/FunctionType 0
/Order 1 
/DataSource <~IEDNiIEDNiIEMTjI`h]kI`h]kJ'7lmJBRunJ]n)oJ^"/pK$=8qK?aGsK['PtL!K`!L<fi"L<oo#LX6#$
LsZ2&Lsc8'M:)A(MUMP*Mpq_,<EMAIL>\"/Nn+11O4F:2OOjI4Ok9X6PM#p9PhH*;Q.l9=QJ;H?R,%`B
RGIoDRbe#ESDO;HS`'PKT&TeNTB#tPU#l=TU?;LVUZ_[XV<S$\VsFB`WU0ZcX7$#gXRQ8jYOVYnYk.nq
Zh=A![J9e&\,-.*\buL.]Dhj2^Ae05_#FB8_uBi@`r?>HaSu_PbPr4YcMnacdJk6le,LTtf)I$'f`*B/
~>
/BitsPerSample 8 
/Encode [0 63 ]
/Decode [0 1 0 1 0 1 0 1 ]
/Size [64 ]
>>
]
/Bounds []
/Encode [0 1 ]
>>
>>/Gradient add_res /1 /Gradient get_res clonedict shfill grestore
}if
np
111.361 87.957 mo
111.782 87.9766 112.219 87.9863 112.669 87.9863 cv
112.67 87.9863 li
115.465 87.9863 118.348 87.6045 120.598 86.9497 cv
123.38 84.1021 125.078 79.3477 125.078 74.2759 cv
125.078 70.1016 123.963 66.1924 121.982 63.3052 cv
120.866 63.1094 119.556 63.0044 118.16 63.0044 cv
114.552 63.0044 110.722 63.6782 107.97 64.7627 cv
106.532 67.4731 105.751 70.7974 105.751 74.2759 cv
105.751 80.0737 107.971 85.4053 111.361 87.957 cv
cp
112.67 97.9863 mo
112.669 97.9863 li
111.46 97.9863 110.299 97.9326 109.218 97.8262 cv
108.503 97.7559 107.813 97.5327 107.192 97.1724 cv
100.242 93.1294 95.751 84.1421 95.751 74.2759 cv
95.751 68.3623 97.3584 62.6294 100.278 58.1333 cv
100.757 57.397 101.42 56.7993 102.203 56.4009 cv
106.38 54.2739 112.345 53.0044 118.16 53.0044 cv
121.249 53.0044 123.991 53.3628 126.312 54.0693 cv
127.214 54.3447 128.021 54.8696 128.638 55.5835 cv
132.73 60.3208 135.078 67.1338 135.078 74.2759 cv
135.078 82.6919 131.857 90.4951 126.463 95.1494 cv
125.994 95.5537 125.455 95.8672 124.872 96.0747 cv
120.195 97.7378 115.22 97.9863 112.67 97.9863 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
53.2656 90.1284 mo
53.2637 90.1284 li
45.0918 90.1284 40.6182 88.7368 39.1572 87.4356 cv
35.1943 83.9043 32.9219 78.8232 32.9219 73.4946 cv
32.9219 68.6665 34.7724 64.0971 38.0045 60.6749 cv
34.9889 63.8689 33.1783 68.0619 32.9492 72.5322 cv
36.1401 72.1233 39.9005 71.8869 43.9511 71.8869 cv
52.1089 71.8869 61.4432 72.8461 69.668 75.2876 cv
69.7226 74.6997 69.7568 74.104 69.7568 73.4946 cv
69.7568 82.397 63.542 89.2412 60.9092 89.7622 cv
58.2422 90.0054 55.6719 90.1284 53.2656 90.1284 cv
66.5463 63.0085 mo
66.3187 62.6728 66.0794 62.3438 65.8291 62.0225 cv
64.7246 61.6013 63.4791 61.2102 62.1275 60.8548 cv
63.4791 61.2099 64.7246 61.6007 65.8291 62.0215 cv
66.0795 62.3432 66.3186 62.6724 66.5463 63.0085 cv
level3{
gsave
clp
[-25.5534 13.1043 -13.1043 -25.5534 59.022 75.0471 ]ct
/0 /Gradient get_res clonedict shfill grestore
}if
np
40.0674 58.7964 mo
35.5713 62.3105 32.9219 67.7173 32.9219 73.4946 cv
32.9219 78.8232 35.1943 83.9043 39.1572 87.4356 cv
40.6182 88.7368 45.0918 90.1284 53.2637 90.1284 cv
53.2656 90.1284 li
55.6719 90.1284 58.2422 90.0054 60.9092 89.7622 cv
63.542 89.2412 69.7568 82.397 69.7568 73.4946 cv
69.7568 69.3179 68.3691 65.2857 65.8291 62.0215 cv
60.5176 59.998 51.9473 58.668 43.9775 58.668 cv
42.5713 58.668 41.2607 58.7109 40.0674 58.7964 cv
53.2656 100.128 mo
53.2637 100.128 li
43.3828 100.128 36.3985 98.3696 32.5049 94.9019 cv
26.415 89.4746 22.9219 81.6724 22.9219 73.4946 cv
22.9219 63.9912 27.5996 55.1353 35.4346 49.8047 cv
36.1152 49.3413 36.9004 49.0532 37.7197 48.9663 cv
39.5869 48.7681 41.6924 48.668 43.9775 48.668 cv
51.5098 48.668 63.0752 49.877 70.8789 53.2837 cv
71.5508 53.5771 72.1494 54.0146 72.6338 54.5654 cv
77.2275 59.7915 79.7568 66.5142 79.7568 73.4946 cv
79.7568 87.0093 70.5244 98.4619 62.2695 99.6724 cv
62.0068 99.7036 li
58.9727 99.9854 56.0313 100.128 53.2656 100.128 cv
.757687 .679133 .626856 .856168 cmyk
f
69.668 75.2876 mo
61.4432 72.8461 52.1089 71.8869 43.9511 71.8869 cv
39.9005 71.8869 36.1401 72.1233 32.9492 72.5322 cv
33.1783 68.0619 34.9889 63.8689 38.0045 60.6749 cv
38.6399 60.0021 39.3287 59.3738 40.0674 58.7964 cv
41.2607 58.7109 42.5713 58.668 43.9775 58.668 cv
50.29 58.668 56.9792 59.5024 62.1275 60.8548 cv
63.4791 61.2102 64.7246 61.6013 65.8291 62.0225 cv
66.0794 62.3438 66.3187 62.6728 66.5463 63.0085 cv
68.6292 66.0819 69.7568 69.7296 69.7568 73.4946 cv
69.7568 73.4946 li
69.7568 73.4946 li
69.7568 74.104 69.7226 74.6997 69.668 75.2876 cv
level3{
gsave
clp
[-25.5534 13.1043 -13.1043 -25.5534 59.022 75.0471 ]ct
/1 /Gradient get_res clonedict shfill grestore
}if
np
69.7568 73.4946 mo
69.7568 73.4946 li
69.7568 73.4946 li
69.7568 73.4946 li
69.7568 73.4946 mo
69.7568 69.7296 68.6292 66.0819 66.5463 63.0085 cv
68.63 66.0804 69.7568 69.7289 69.7568 73.4946 cv
62.1275 60.8548 mo
56.9792 59.5024 50.29 58.668 43.9775 58.668 cv
42.5713 58.668 41.2607 58.7109 40.0674 58.7964 cv
39.3287 59.3738 38.6399 60.0021 38.0045 60.6749 cv
38.6398 60.0021 39.3287 59.3733 40.0674 58.7959 cv
41.2607 58.7099 42.5703 58.667 43.9766 58.667 cv
50.2898 58.667 56.9791 59.5014 62.1275 60.8548 cv
.781125 .709987 .614252 .890623 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
[/Gradient [/0 /1 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Face 01.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`du/k2D$R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:HkhONHK%<(9#4fDa,b]=KE(c,=3nX0cSO`Ccb=c!r:R[/^4#V>q7#7QhRn7-p=gZrS\BUMqW_]hrs6AbHM.0u
%042HhbLQ.EdqA[bh`:TkIs(YJjn%4cDdPYUG?(dd&`ZsbjkkFnhgu4)B;4"]IejCB^OH1M*HVDKVKPa"qu-PdgB@)T$ebE/5@iA=
%L*+(OZ1]\lou#6Sr70,B'!HcaPBuo:J)Yp1Fo5V97*W-H$9sd/I>mqg-nSp\n@(%[^KU0>V8AQ,I_1TC[hsbd=APtD5a0jOYC>J:
%^&%Vu\?"Xc6I)@.'N<&@H\egMHe;tU3!=K[(N3+4OIaF`<Ss=-p2kKIFSGQPmIkXA.E7$*`8]Tt\jUes]0KXnp:-0s@s>+DCo%;`
%4@G69EG<sEkdM)To9-/0[e90DY6i+UHI(S+<=U6HK9K66S]8*(9bkF3!dlf_#c0:_,5^RdbsB?MY%(*R@_8?0l2uL?itiXDQ4*E2
%mc!o3k+T-pYR99/`#j;"$+foLJ,Ijg5JHpdh:njr?G(R^ch,K":CAhe?U+SQ.K9I8foN=!PTRaqYKkJonFX>Zms$]rNMk^TG<`[u
%`ddLf;SJuG6!(5kIGIKb>ac834Rr4(G\m6jrZ&*#<T81Nq9(S&&blCZLag6FK7(LR)F2FUWKX]uhS/gJe^M#-p?g-P`o$g6o2jh^
%I/<7kD=a-&cE<b'"Fj33l_C\.l*2V4?,Lu;!cn_/IT7'[1B\6gP%P#HGlHkBci<RrX8i"nl.=8=$5Y\7IJ<%Dru^TN%pac.$2IZ[
%]mThQ2m6T$K?b7,Fe5J&^%qO#96bc1rV#>O@L:L#d,4E45<OAka62oAMnOE3*R<)oYF<qtrVX9/G>Gu'NnhcM,JX$9q<NNTo3:Kn
%JFsE@FIcecHec'c-_*].*a[D&D2i.3lGO&!lWS;IWV[u\Y@"qjn])HLJ,I54(pBHfE43N2qr6qXfP82;nLpp>\kR[crfOA#MnaW5
%r3c7SLTaH;RolJurn4K[m@2^7TAI5t)>G!IH%[d=)VF2q2h.(LNdoFETSi3POd@6.=$M0:?[2)Z.t7E[kPVsceLt9Y#J^36[r9"X
%k,5E_nE089+!/.Fo.SFnmU!PTI`9(7U>Pt6lA,BR^&+^p,QFb)T7B;FT)a@kT&<BF>)*(/8eo-cIera/VrN`GIer&n+2+T6?iSmP
%GL-9\8SrM`+4^4+T0I\sjt3[rYHNEF0E2CZTXMbnNorXZ(=FtFS)F$$i:!lE(TGP1r:ja<IqPEem3pJ$m6.7dYZBWGXMB&ETDJSC
%Hloj@%)f+&T:u_K+7*'OUMniGM^3QSpj^\R:KLfQc0ISYJ,/4GCnIbV8-aV>o',J<Dl62*q"F^mHl3-t`pHBK'u2VeFNM%<MZ*jf
%N#d3CmWT3,1GF>t@$Y-sH*\[NqnVj)#;u2Hh4HsDHN*d,Da/YlI-!$0W8R]^5*#eb#@6>T*n%=Be!D^pr:01'DuTCgg"#W[GMiDa
%l6"lqH2/==)5^QbR>TI*9<ut;A\?<_9JcYm]<MPi^"qINl7Vf(6-SNh=9%jRjbn%a%@33E$]NpdNLa=M2oH'u[CId^OdNr7_uVu]
%#(7lZ0Ds@.-<+d=-ZCW"d'CQSo^qO_(XHDOs7Q-D^3fj`]nY^mgXj2bc)^+c:0NI+_%iL\2hp=`M1Lk^47khFnmCT\ls$*+)p(rU
%K.P6^4f&M#]Z0(#resn/qB`eWLhYbq^FT$^%ip*Bh6'=n]=bDi"BDpSi0T,NV9/aph3;c&Fn>86OD-<eQM3T7lb>Oc_<p[0^V&`/
%_MS90Mq=VP>lFN2([ms%OQMnIOn@.iTjWnMVF*f_RMtS$8&`4a4igu]Hp,[!:S'[t'X*P#BDMDmh`h#8"c2>+L9=L;9I_UO$EU'j
%dhN)$<qmQD6@Xdq1K0tdTObt?nIIaH*IeYC!q"U7@f1N;,K9BOp#ARBq"W8:rV07X:_*:ljTOZRjT`kEEDV#h"idWS.&Blm*D1rK
%#:`4l-S";";f3X@.^rN[K*&'#%8\76<&5G,.Xi;$0',oj:ep[WjT`kEF&[MhNK!j$9!Ar)Um@toWYRgSG(amNkB><lgMM/jpXg.l
%D!@V9p""ISp/=3MRso^'#Cd#78:M8:$nhgU3j_7=*"GIg-Re/qc/BG]d6[<C!$r]8&JTTiRg'H0U`Q!?BSJ"Pc\]g?GU*7OEW_$q
%^;Y,5-[u2/&HCmbW<=c5+8nD\CXBC7]fW<A8:@<5c$cBo:-I)j9(/GQ#O/)Y:j]N^rtM.P.[_V(-LK<Yo[?)5;uuq^T($fUEWbFs
%m&5.WJP)Yg$a*$7<;N"hK*I+FIQjBBcEOVDD^`(oo`%a*S.lJ;ri[%2s7C7,(=o0:StYj&r-a7l\D7WXW:VH9n'X3_?Tn@-p_Kc3
%kRtVa&`1%G3$]=U*$L\,(pam8@ttZ9nIC=YSMg/05Q<0J$n_S@kcji"goZ>Hl]X!9qZACDgYbF))-B5GAJoC&D=es'-"+\L_?P^J
%HB'rS-Fr1NOAen#eH?g-[cIg]b>!T9bV&c$_F%)6QB8("#:%M_4NeDa@Xcjd8"G>e"lMfD/*&q5G9/uK2oK3fUL`@-]cI,Wc._r;
%/5Ti,&tpS;nK7gThI'@p0Wujm=m*JZ4p-^]0XjR)ei3(5SD=7Jq$7oZk;L16iQT/P_T^l6!-;=O2IS3og6OP!m1-&>/^7FLffJ+T
%_7>\AL(;",n2(D-0*bl@\&?C0Dns=&^qjr?G=UTDa:1q#)kFCk$Bq$!dqu`,\,Ot7gtpi%Q9PC[45BIt#)>m'@kPT,$cjWM0:[_<
%-l4:Arr]I?SUU`g+*[\bp3qYM?XWoomLRC>@IGEg5JcbP_#A8V\-d2*"Pbp9-['HLg]36m!Mb>ZE:FFOY&Ms&3-4T]nH[ZaiC#`E
%HQD)43h0+`:94jC+SGeRmbfq6!@E'nk'_b[g)A\e1Rs!#_-Y%NZJNHSSRL<60R=bZ&3g?,8V/`X^rSRh#<+?Z-Q).OS`LSU5/WB]
%YmKR/j]a_EcS-k'CTt-+bYa(bQ=7OZL^8@RV-DT/B:-X_m)tt=m*Pl]'gEg]_;9>)(A@89Nf.C0_#-,n"U3HYS<U6)!iueHDINR;
%iD9OjWO#(+);F.+5"SAR&u9b$:(-]R$V^!K8,,%9Q-7:,*/NieV+YN#Wg+b33WM$X=7d0."=`2birW`,ji5GG$7m27&NWtQ;9H^I
%QXYPA'N#4WP04[Rm07rON$F<<(N794J[*gI,=N+(E3aMKEkqLe^S,`u,_"EQ]=kn/_t=/l_VqqU\FjigcZUAr[Dh#"oUQ56D;KW3
%@Gen-Rs*YBIJ6%>%T^@3]+<\>(\SiM"5.hgStfF]=$QKFpm..p#IsTb[>/8EU&WDNkW/VLhACtOFG]@e]Q70s3\%l?[-H3P7<6U_
%D<(CCG'D8fl)o"o6R<PHpU-++/`X$(`ub)rX[@2PK0+'8&&h?[^V@K<fKZ\tfRH;aZr.)i/\]+h.6NH%GS9,"e3d.H]nfoZ'Lc":
%@0)OsfL5p)ddk.]6&\2d<Y-L4A;M!ggbM5[,V<h1$u\R6F/B3i'%mU,<rP/Y3JdN%$6Z9sVuV,V*MW'!9Zg`D0quO.7thL=^`,g!
%4HXYTlJY&qA/-h_mTWBl;tZ%_0OQf=JuO@CC)8C*Wb-gF[8Q2Md\Rp!.\=)CWZJ3f2l;b4_?U5[BGenV9(ABN^M[fDXIbmW#O3Ze
%THIF*qDT,(/./%"JR)6jj.uO.nu"p@5UaG/]bNYTn2s[(0McF,:VNi`6TJ\rn2s[ea,`Upj:a,naU]9j'XT=)6@crYLPnTJ8,oim
%=R!odP,YYo&a9Q/FH"%%7Rn=;AiHNBT'/^,;Oq)'2E9E`+,)UEU9d#l[8^,OP8n]rB!S)Oa.48rHK<2A8'3Y1I!YGLiAZe3q#HqG
%^fgTB6P.i(J_&HL+P]$chffp#(^^N-"-:?bYVur;+"Ht96p;:XDAL+G;UCn%]NM``,2FNg6&d529d6*aD@;"i"goMH%Dg8A0fY=j
%$o@EhLg;3B%1ruMB:r'9H>2^Y<l,ph"`uRtY]4[B,/]+egD*ik:*hYXA<b/IJO*)k)LlY+W$R-VktqfB\iP[iNPrE5:Sp8V2+H=F
%gKh+o:LpBU51,oFHNeZ3XmHn[0.\+.NaVrcpPeL.C#?/)GU/=!@0c>ZR<K/LO(`'nG7a3d(*e)@c23<?'(.(*o":GkW8udQqakuG
%m&@,&ol(57HtE\^W!'F;@WU-M]8eQ[LK0KK#KsNp>rhA+.bNb@[^+_=Lffp+Y>gc`W(N7M("*>Or13t"$8j;3i%c1g9g?O9fI<iL
%%4r*6%b,_]4V5f`%#:F!92(8OaC&S,<<(1`qLHh%ZKY@uLoeQ:9Z%T*Jo#jJd>2^0>?9RJ#:'XTB"_FAE+[m"QoQsGB(fkHs2:8b
%E55s-Lp[d%IdiGuF9"2(5@KsC+=,m.:!/Y)AF0J-s2WORMWjj9Sd<1^HT_FU4!KNfkjR^Zb+;.g]tqU+nmpZ]`8YR-_\DnV$a(Tp
%MXSF"KI64r'G=MJ<';kP<(D^'1*.L&13kXd=IiQa*+C0!"[dC%]EL8;FWn+5<'8[*`9\>-bK\"H].V4KJa6C<5hA=ZX%V:ubLOEZ
%"B5pnlIEmqSCYj1X?nkXX2`>[Mp'WQlR%O_;Jl(H2EitkUS1/,@qno'[1oR7]UiM"JXTP0KBX>Y)Yqn,T;K?0AA5Dpb*&p3,Hih(
%M=We"jt06c-S+1(l_JA;aanLK:RS!.(<'WM*]*DUF]"3Ec)L/jQ(r#m&[/Z%Xg=M=B-sYefR91cA*.nXPs[]/H1EY3;D$H@,ul!o
%)F81I[=K'IDMl<,-CBC89/&$KK3dShIeV2ehG^(1W%mb*Zmk>.Z=)$)-:rb,]m7ECcZO")Pa:i"=YU_glB3E#JZr[:Q3phrPa!A$
%Q\o*!IRQ+9MF&[_Su@(Q02*d(_<`GafhpB5CY[0f[)t"4Yrj9Df:j6gl5sTRm8enL)g[d5Li>j]D7^*peV(JL!aV#mOd0Nk8eR&*
%.bp0"?3EA*nVW0KM,o(%onaA2<2&6PBfJn_8f_UFYW>d@O(V@j=KMqKjW,dOXBqH330'2c`E#L;4(^N4,402"$J-j]__Vb[;&$4m
%!FYS9;SZ&rru<\X_ks)gh%un^V,!jh):g)kbaWRerbe9F("@*?Fj=o9$'0A(B>09?hI`Cic>j)e=RT7s[l&-]XF]agJ/u=,"nOdb
%+]V^t^tpOXCEVm\J9AaZ[]T[<"75JjM\Qdlh-SG-CA-UK.!/q&24EmZAs$\pZs'<V/j1c7l4FrCksL39(Am`?Tq6,N9.T\h:;-<1
%5p*SEC@r+"9&)u&c(kK;qWg]>4d3a0+B>o=.(KlegtZ80``Z0qQIAShDTZB%W);+2\[4f);^kYe;_a%D.H4NCdG5>`SBLP6qDh?o
%qFO\13*T@%=63^&ppN3UQ=nnAh"#1_@jBghI%!j-q)%Bc.5[Y^oB?@8#YqE[SNVmPa3L3kc,P%X-Bg\LZN+qei&]8ECOS<WQD_t+
%),om\R8+UW3\tu*f_Mg5>o&.t=Q@sKrGs/-m`Ycu$[.]?g7iP)8"Z&*%fVj?HouZ83&?2XSO).nhXH9VOPZ.Z,_M#8[iO7'%Go9,
%FaeNd`!2l(Q8aZ<,*/S8De/at''7)Bs$%G=50;,@2=gpZ4W/b#Ed27pIP^dXECUGdmo\n5/f9>EC8Tlsr"TMFnHQ:ZLH0-!29dN8
%3*1mB'ZLL"eP#4>?GX\,$<aRh[$^=XKY3H#h9PE8>S^h2`XLJ<lCMXW$IkG$o=eB/Qci9IhrmXS_AjU*F*JUj6S&W.P+i=UCBE0>
%S"!!WV_/mPiXJ]CF?249A0t&4PrbSf49NVFXM!4(,n;BOn@;(']he'qK5(>*Z&3g<Mo;UKR?kem_u"$qI]#kL\hhe3Za3SPGCU6U
%32'),YTk^?1E?b1_%@kB^knZ(\lI[IDdpC@MLCBK:h"c\Mc1hsd]bD7l]PG:aLWMFUgZbk.tQ=Wb^'C"*>g<]O3E3_Z3+np84B$b
%D6b?+L*ZgFUsmI)+Ef':/NG>1Z82Zj&WC1RNQ7+@Vn?6jo.amQT:D8Nj*FU)ihWD`3?QlTF>W0RlKG1C6a+]KW%L>Xs/D=0/j=IJ
%/N*17ETYN+4MPjrX^-3X20LD#'T3Y"+j#Ha*L;%['se41btMs;iG"AtRpX=F=f`#LNgMTO3-dB*b'Tr+kp4lJDu,<B^6OW8^O&5>
%em6b.HS5jrEYmSc]b#.-eE)Gb\8L@uW(TXt"2;)T74oAQqXI=6d&pSFjOa1nNeFBLmbD+s]acJLE2<ZZhMp6m>n0fJ*rc,/!<(m4
%OEs-k:J6blBBmi2h(@BCiH@6FiA[rI5CN5&l`35WkmA(HlqpiOP6LS8O:QlXV?f%B!uGo?!9[,Z2=5R7ki%ZR0gZ*M6e.3;E88UP
%+iadK-g8WbFCZBXaLLE[JF,6)NTnNgj5Q@7MBIF[#.0tQYp8R(B>FoLSV*-+OPk+J'T2uqO2/jW!Eq9"o-g3e&rY&NS<5\$a?5Ll
%REb9CLk,j7$c]>^B"J,R581Xt<i^&F'72%F9'hehe\jol&&G?2fHr=HAsB5f.YD9_=S2Z^&1(M;(g-(?gbKgT`_U_T3JL,&/;2>)
%)<tgVAGBT.TGm_p1_g$ki-pQ-3%mD%6Vs>meX82)7`6-s2+fCOif'nbGL^2$c.RuO3DCkki%P0A'pq[#;3'!@;1iEcpkMF\S]u+@
%,rh#Q:XoRDQuZ)=`lg\[=!`8)R8A%f9Hb!m4BDlCa?qi$o+"r5FTd&E]dr^bU1h>(*sIET5a\K,S7n]qp;SZuk_)A*(-WkN(,CK8
%i<p*$ND*Sjo0K/&O_!kgHO+IN\"p4&\Z1g'_hP06i+4fG!Gd>&O(>CSP%`E;=Ar0*,>$Nm2*dE3f\nV),T,Cu8.]r6GT[M?,F,c1
%c_MY*Em(1Y'\F8l-X8."8JL)h!E&N#SNi5hJCZYmS7)%@./uFr#C]O%61gr1N3bhC)6D/'I0AoSBMe"'gCM;$<@gA9\13Br`_hGZ
%%SfWJ2iYgC!]HE]1<1r_-O5^n&_90:_;m9[#KI`'H_/SBcB'o"'NFu=@:i%B371:W=Q9PDBR>p4E,"<fG=B+d'*lJYIdK;Q]Ps:F
%"W8lr1^c9H"sS)BNlDC$ZMA<L`XIkL!Hc$dk"Nee:CAmZTt<kfs.fXI26I=Om#M--hZn4W?2G4!k<54(#VB/JNci?0D+[_E9%U/'
%q5Kb82HsQl[=c1$n$+YD2l>VU[XFP*05;aK_8Q^MD@[VO-dR"l_8jfo&Q<WO##D)M3CI>1RCa.[YobFr73(Z\K-lhT0#K$%TbDEd
%o.NB0`7T1OaA<EW-rYr^[`MpM-A*K=3J"4Z6q&CeOo^8lm@+TU8V(V=\6fTU@18kr:=Nf^J.mPCW/4n$CcfEZ"qT@:!u)Rpd"#,h
%m4`h9"Z0UBFs2TdAa5`.>Z22N(EBj"")]ZWeqkPCd+!#\B/<bS\S?bB_i.p)L;[8-k3R!TM4!V0X0*7AHU7TE58sn.-e6'@=Ka>7
%9(W>39bP.mPFrFZ*G&%HqXEs&+KB:R/qp[57>8+)j*'$73]KdS:&q=05q3?X?3-P4T0hr$HiC/#'7a0V2#jq9-pmm%pRd6SMSXt:
%B0O56Qi1!`TTH\6+P1X2Z8,2M64sq19\W$=\\sqp_usl-M8aXL.5_WkP`lfXT1$C1jij)#OhLsj6077#Er3T1&bYH2;Foe7RI@g2
%W,[2+<&^)H;tau(d*H%3J`OAf>5i\[>\F=-!cr>jZ]oste[CY0.nTN,guBsEA%AF4?$n2V6ibb2nkl7!m":k)7la-M#&<'l4\ZgS
%/uPZci_`K<\a3jm'eQn<Z4<77Znn#c]pDR0b`]nTJ`/B++-TZEfZ5M><A=dbC)k[I%f0pp"Q!Ktam,YE0%LhRHnQQ9_ogk3NRMY:
%,GBX@S2Uj8([E0G>[4N]>+^?k49QYag;>`KOTNgJ`Ls#M,I8(tRB\l/ZIk-^%L<3^/0o4XOgT'P;@Rc<-o#IZaAiYV7&un>%9\kJ
%Jh0D[g+5`G,UjE<(Xgeo2XR_fqCS7dCBpKBo+k*6iLX7iSWujVDTX.eD2MK.h`q+uNrYBakP2"[CAd-RrdAPZRn,E1M'5&J3-[u>
%E/ddLmeQh%^\-dRj6(pXs+3,2+F<o>BM8-UE5Q"6b#S(0lpBg"@(?GKDMn!i(D:u2D7g+MgFM_-gi(QIrB@%9>TWBc)F*A*^0I>N
%H[biHjhGkrmn&\I9Tq3ta(dn@)tGJI(dScJW,0Bccgd.`#g#(rEXRrh.-$'`o(*3i*G2P/J8.H21LPPp[KgVq#&fR.rn)d9M96=K
%_m*Q"m/]X<Q;D)ikYh,3pbP-Eq:hlg>4#sin.)K]_ts)2ZHk5h*EI6Y<U=,\T#es..lH;>U_V!ceJbPH>Z^:^3'lrA$J=5j]H'XP
%a/+GACJP323^Cj=E/Q$d?K0WkKiosWp2d/9H:&Behkm'),0#"2>rb#080MVG<F_77=GEc'r2<`Ic%qci%K'X3qPLX15`PV]]sg-!
%"#V\qqr"gl.-?rnCuqj#itB?hrTEjgKh^*_mPES"@rS7VNi.UCfn^YIU2'cr:Hm-:+g=Z2Y\*?kUH_P&BW&g?EZafq$E\_Qo_/_T
%YQ%j.hWT-b]Kff^KI5GaYb//D2G:KG%Juju`4o[/+>no,[RM<K4MEMNjAmjC@NT)%PBrNW=hkkI`;ZO<Ub*#;"$[?qoUKgGia>&R
%H@E$Cc<sc\7QF=$8NuD2`*mSf=)[O1_Yu5Pnfp!mj7&Drl&?CjP++*-Tnj\8Sn4da9QMXUMWT5P;hOK6bXm*lYIS3f=U4kbE8Mtg
%[r&d%F/ZVEmsm2BBK'ic5I^bb&7rXDIfqfl.dTdbfT2;CbMrN>h;$rG%ejl%]aJ&Q?:TbV^]bPL9NAAkk<CV`qBRu*O%,(Wm7`rc
%#;V(&Bddi_TNM1dGDUa(c-1NKlfbkNRgYkAX^[2*ThGl_3!`b5#ulKW9PBN+!gG*rO@J2%S:F)XcCeDD$TDVda,aae?D'1b\'a^*
%e#",ZMD/--=chi;94Og.F2,M]@i7k<=>dVA'[<&Id@t]=(Wu4RjSOh>C\h2$l_bUd2P7aeg!:dkXH`"j=RYTV>a2)<@q-XT=MslX
%7R!nI7uCkO5*f4@_)=N.k+L;HK11Lpb7Pk"![q)gjII?[Vs>POR&1d+QG[$k6OL)m[hGu:_*gRT6O"3%=O7$L%J\7Up/<r,@"2NU
%K>kguP^=EG_c\;!StZQ>DE#)R>-o3<E;@_r%4@k77)2+mCU95p,h\emN+r\G>`2Fj!n8tE^f"te.F=H3i':)tdY!7MO"bH("J`@a
%oT3bAKlLuCK&-SEUcd`,KaCKUcOb5RQnq.#qr(+r7H%i/3dcta5)WMVicP^4@@a#b[$g4/5(;eoIbrKLFKpblP2%@eY+_NU^J90h
%9RAmb+*2nEOTp*!!-st63DM#b2%d,uS8E=RQ1L_`:-SLn7%u^,=jq*PUd\>ENWD[jY8-*K#'@6%?r9a[6%0/6Tdr)SFultnFXq\H
%c2@k\,oH]LK%MW;%B"0S;QUA#X.1@J\s]IT9N*S."\N?%TV[9L@$VW1^VE1<d8J#aS2T$#/L$*XkS%9\%!k"3XpWV"Kou`uQZc#L
%:C3AF,h0k##]%ZaV[IdBkJK0GkS:"'n$<2ccT)Im28Mh,_JTIa[e;/`$/8Z%?[b?X=%#BZ[Ilei*d'E4a]WG]bB,(jCoVHIM*7-k
%W(q0,[q%!PMe07'!kf582N(BI8LX1p`>J8l&]lG3pE6F,7Y`1G@+iK%@/38LWo53X%?-3/(^c`$99cl&R8jq[dB+Z[);$@^-JB8]
%7'ih-=n#IC3#hHEaWO2q&\!A@@_O]ZP]A'Mm-90=1"YrKWt$q@U%f@'m`lJ93@m-\PCW'In#4)\OF+oGRFPh4o*1&s@-$?n.`r&m
%YVnH;IWrQqm,gaA]fb]P&P"XD%oVEd4a*W,3Q&RnBS,(Po@,h"&QcV`2%CnR'h@7(O^nK]Pc.;r+L"cr%cfnP<9$75O$nRnYoo4k
%(Nq(`U?[tZaI@,.LAm-26b"f"@uqrfSgGE)#SE$Td[1?#iG,)*,naR[.R4cs_!qeF_#Z;\YYq8o[0muLUR*isCDmcaJ!tQ@3j13[
%)Nf4f/a=0]I`?8i?$J_6#X_?(3IQ0#2NnMj-aK]tdA[YhEh*#j_<")^EGK+4rM[?5/Tt[1*?"5Ia8[qfFFAW(!l%n)b8]2uG%NCN
%jDm3Ecf;*Dp[$#pNmr<^qbP`ImEE?=NguoP0iL#h>g+RbUG5\7nnOJ_=Y_gtZo-pJ1sO[E]VR90Mgnb`7^E<tSb_M$D6;4PI9#"(
%1f^6hV(D:INdF?TJf=*?SF2e^rqsBte@sH-T<H7'CjNN,ni8&qnneWpKcsUf4ReYeE..%<2HTgdqk+7)`\,&jf^.]_#WEGZ4l:oH
%hgc83*1T`fCEqrp5WMN"$E)96S,,uIdf+rGRV#!,9n;g0OX+4(Q?ji]!g[;0=K3ZWBi@V>:!7R]j=DO<gdj%V,GjPKmB1/LD4Y'D
%o/<"imkmM"9rTZ@i1(3f0'd%rb7l\S!m:mbgnk(Y'JfEIMeNBh`WmlZGu'RK8#/b)c\H9mF5p^"Q/58J`0!l#@iIq\@uY6QFN5WV
%EMo1696>!:L!F[sQP1@r9Xhhll@'VafG9=OQT)]>8W0EZNgFt*oI6p9R=+Ro9Z^G'ME.qb1![/?+S>7'/*jfK`q>focEk'.ATRMk
%VbLE'^q%*jAa^P__+eN*,!6#%\8dTFmo].>-+?urMO?hfgM4\HG'LkF-eRM\YVnH;IWrQADjT3bT(_pamc@+==&P%Gmaf^h,:-n>
%kIt)3DofQd)s.Pflmufc4*.,i)qtaQp%hD=fk9'm,?B*..F4=\_[&e\nqa4^Hb.u!:@jM>P-fV),elpfnfoH+>i+Z)=*qJ!`dLUj
%PP*)Uc"4JR^3mZ]GI`WUp[GPB\%B+J`eW[T?+VeWpEY`h462!uDlF,Ka*77EEn>4&lupEEq\=9bhqbL%Z[BGf0CDeARsQOU6[%^J
%*@cng+*Qp5BP6c$aA:NVduI[&a]%_/[N;^P?aK>PKN>a`!-o[6cd\KN4]$NP8?(!T,eY-#I,hPamb^B=<4VE,DrdB-8=us&s0.tu
%54dn#@<&Ws]l;;EJ#PVEDn#$j$?$pYl7NaA8TGjYG`:WLT2Y?s!k%Y_8b->.=o=`1G@C'ZiYP9PaXn;K,.R]c>nl6-,@JsPr$8uW
%;kLoR9mkDH'O`6#>nl3rKHli#'KA*!TGVlQXDb.AMfFZ5r$VhjB)To"/)hhe.Ef1>dhd61D^!ifDGEAdMLb:]M3tjkMFK?J8&i?,
%+gk$#J""9]VaP_R1MWC7URlTS@%sL:=t!<IP<!@+m:'(s&-"hDO<amt2b8M,&d8&Q\&n2QP@aTR9<3aKD/W*-fS]TLB>s@dnHAUt
%dq[!JPKnKNN!g^f82ahBgG7TAI;)`_mGL"@QHZ91hlfsqh5jKjcA;pQk0moUk#:[q/s@,r=O*k\>sf)8d']t%+/a\h^jBc3a_@&b
%+aqGE9'A+B$/-j>o9hqDh8S,`p-Pa<)rQ\^X$Gqr8(a5><U9WV_7/1gak<>t:>7Z>\#I2k34gcQ#T7\R)Rm!"aM]a4NDGMPMA,>V
%1`g,,%G%-sb$;'V3hIHTak_u3bOUhiZI7H8:;H0A8go+SF`C&5(aZ`NQ*5>lTR"3MWmU5%B=m%AB+=<R7tA4845Mi!Z)5H58%q7p
%'4Th8lY8K)K4H&^Q07j*k(`.38DT$0c[=S2WZOI:5C)"=Vf$kZk[O+kO1_;m;C^Oe^?tL)+(+1(bM<)^k4[!CnfN"\r;5Xlh4\V\
%(IQ=03k!71N8m-GP(p0];iKE[Mt`B,Wq&nsZXmILrt&a!IU6SC,/UYRkLindZV4N1=m6nd[TNdE>pu6O0pO@#*I/EQ#S/A%Y@SOg
%[VsFA(`#+H8Pk+@_4p4$7dVF3eFo-!#DGne2cZNL,Y4NjAp@+>[8C>u%@.oKWuDX"fIk5\5nO1_\kKgH@=&a+nfe4N^E"0[b0DqQ
%=ZANtZ[I;r&+4ZI.@9Vl+l'a_I9"9%H[boToe<PSFfXsS:78HOl\LkYo,R,>(;6_:Xs!"+Y?bq`QfdLCAf=gS;:Tu@S]4P84#9p7
%N%BQA0\Db)G%PjCne/5?5/Al.DV7pGYVX,Z;*`XH"=YhaD#(oYT_((aR-#Vn^kLY<D9+h+-m'^jC'7FK-rnc$^!2[#DY*90qQ:_@
%3#Hs0OWsr]1@TFOnT5G5c@RZ,a;jCg.LK,"mqS22WVuOpo"oEUC[UbN0>?Raph8%F4$5C\RJH^&q'+#HU<80uF`RAr@=4Yoe?cnn
%s*qZ@CC6406a$YBj'iS`@p?V7r7:Y,^AVlU*nKZ1MTn@=U/JtAXoG!q2O:mWDAPM(n_IcJOa..C9DWI1?d?Y>Mc&Xf73A$[Hr`PB
%=i6T'.Sr!r`hcO1e]^$dq!sB2%2lX@1Yg3;K(`R-].au5474@R4N65-g0[_UMRYa1)QT:BgOu_.2#Z':B=fkenfE]6aDsUIc%sjq
%C2-5*Y>J,NGp8L%6a5Pl(c_::s7kJTrpasdl:Sqj0o'tYi\X_bUQreYl2qoig_=SG`]3s2rXR3DTijW;5DWn\;$`A;M\OeGqd#_0
%\tg)!X[eb5i+Ol@9ieDklUUA7'?#VDqpaQ%q5DiJX-^'D7;Us#clCTr](F?W025NSC#3S^'ojfufOQroC?q2":m1nDobtW2(Y?+O
%R#=i#O:'[2Y&Be'NB7rdJXgi^'lN@'gX[1MD6c1meV=P!WC@;1"OSW=I-@*E^.tM!V:.KU1/Q/8bgRP74i^.=&QFsH(\mpPnE*Pt
%dHUH_-\S+Ug07I(\fu<X!c(a!;jm*I)u!CY&`<_tP:PuQ>YDh^Xkn7RI2nU&K,1f,U41CA?`,YlcG,aq(2BWQlBYkie+o.S<:)`4
%JPVO+`).C[Ff,(R?p,g3R'R[fcjL#fX+Z+_@5X3+)CHaf]1Fu`=C%O(dka;5U]B^SRcZmU+%m8j;^43j6Zm*$F&&9#pIo\4',kj-
%V/eJA8m(>Na-E]>HQD"3bhSqFRTs2H9ZC*OS1hq;b)L0D4De0TO!fZ'Yf:ggCE;;1*C;J)Qccb_>>/a6]i"_Y\S1u>f),Xl=^t:b
%Ii7q)7FZpPg!?b1G89fA1?tDmRtRou-?aW`[`s^j2D#(2JMQOY-!%m#4\015W4Y`gV?]Yn&!p@Wf8Z:1AQ.lk[%5Y.6JSp9h"eNU
%g9OH?Eo%L,/"jnO7G17l\teJ*'A_DUIF_-g.S%e,Zf14?ilH9)b%:!5/E;>#buj3U8*)MlQ=etlGpY1On*SfI@i$@C0D=glR+W'/
%X3Q9#\YT(4CQ):3h2%pY/^i.ZKm%QCj=+?ScJD[aX@CGYU`rGA_YnU9mS[P1S(cRK3XeP73S6K$U)ttoUKKNRc0.PrZ+AA=`&V5Z
%&F2Za$(WL@)mu>Ik;ZX=p`-22`0rC`2mmhuk<eOtho]*kmoZ?/$?bkoJmN!2!Gu9W9GRF:N]tI<22NA9aNYFZAs>)c[2eFH5R+PJ
%.<n=5%=ob]L^,934lqOd22S!l-6'(^<47o<a."*>q'IG"D6'.gXlkD7mh3og&!NUK<mC:d\BQOP+.@VA>"0,Z?&F:Ap0IXQgqn;R
%WcoiVp%HW'dV!u<\+iOXg??kMp"JsU&"Dc<$L#.,M]U+m\h&V.`b[0YG*X`'IrCbF99&gE9Xflf&7<*Nj1G>ObfSlCpsBG_[tRaD
%1u^j;\<<_')0$hi(/lL/*,@9!>22dO>`#bJfR2.L/\XR1P7!j[B\cj-XerS!=E>3bf&b*9[o^[Y9'%CVgd!K-G(KSE/rpN`EI<mA
%j8$Q:^=-1I6L5#YU5L)S:1M5qo:<m`G\<]UJkj\o-URWuT2`beodqV-L)DqFW$uk;0?WS*'7gd;))hn<;.l>P'<,JMLu2rj;tF#C
%\Tq?&\]``ogQW$CY_[QfRVYUt+p`=qkgpa]PAlF.=H-USdj]0u-]39\1KYH"DiJWPhX?(glc](U*=k+j>X81Uj(,ik*LF%:,?je]
%;Eln?6B*4XhepZ>Y)/)2?J]n`jka:0@NgCCa%_o$4"_f]`S(R*bRgNjZ(kp/0Yd7aI4hj<N@\3W;q@HjCOF&E9A(kNV,g<GHhjtu
%RBfSWr)f3X_CWmJg,s2uAW\9SOWSAECp,U'&L;gp($jF-d'6jLdZSK(?o=$#<Em2!$Ys7%SNgKN1gf`/?gomZn2+MA1gf`/?gomZ
%n2+MA1gf`/?gomZn2+MA1gf`/TC)4R:\+`co*%q]kr!q+dsT!\I)4:eK;U`si+_UJe3K"-\r^K2CA(I]CL#+"=d-hV1#(9FehonK
%bSV9ffdP-8OZ$Z_1Mo^cD$+jSW#K'&<H'mS]SSDkcL<co:P"f_;K!qN\1[1XNIo1WZ=oQ5L2'K\/h:%A$RLJOHrI!#&ZWD/Dr`6A
%^%YIopFLpPd7]8]g*&G4e]Kg%IG=(Bnfku"cjPLXci&i+qq.o2]HG,PJ[.Nts+XYSliOIQa8L"`rP;-!0_+]\O'r4a$P11[#+cTu
%[HIJ]oCZZf&&RU$%GP*&Dh7?/Y'YN95;7EihKM3(ddTL;&@;O2^ABp#o4_3bS$L>gD"oAEhRrmVMb@XESg]e[mk#&<A<dlRCYXO3
%n!m2R99&[n7TO@pH`_.TFOeo.03['nLQubcKW6&2=@F&+U,uTiX2HrS+.JJPSA]/%h\pQB=RK`Jd60:+o8N&h]8:-H2iSmq4<fF1
%U"SOh,?t0<^g6F,p&,]L,Kr%Q25X+$asStsh^bf/0/^:OkI:1V21;qfk>TRla-<,u7:LR=q'8Yk4fCR7(YjHnZL8F;0+\2'5O[r>
%-MN7bgKq4r]NfHG_03TUZrb/V&>jBq\U8RW#i)Ip8+YA!$/UFS>Z8#KkqYB,\SppChRPXkH\:hEDlC\+/]/M\dYp6B6R/bV!YN"3
%/7.m3m@bAp,+6j9?Oj,Wd3_+b]DXKh%+b3qb@Ocu*r:ln$[WZT-^:ngABB+8q<r1rpep.=)Saesq.on!>k*0'`Lt7h;hNqapLf=8
%?K$fD#\1ahPP2__oQN7']:t94]WiZ@`K'undJE9c9oesZQM3i=hjm0<TMJ=^>l0cRF%YREpA\f?4tkgMTLe0UcM)2,pF^^H^#Zqt
%q]"a6lM1pFoaME"bQ"`$=j$hApj`mf_dW9D#Km67iEQW-of[LY9\J?Jo"gg)AGAe3cZJ[eLP:DpOk(kIn,>Bgp?up&>0k<j%Q3+4
%*I`Ed_u"@j=P^OYTm*\Fbl7V@*:XP+VPf?noF';n6DbiJd-5oPoIWCX#GVd@@GoXXQX%D-&%)1l=J%>6Ya_<*20++7YE)t4?6IlJ
%'`.sR%L)Qmha+V&EU?D@q\%jOhtse#\h=Za>NG[?IUAl.-T!sAnMi_q:"cNbgBG2SYAV_Y>9*THibh>/Z$TuHR/:GD(JER"9$4\4
%l<c%>!Tc#kLin2-5.j.?lUZS&/;q,#50ISis1Ih=>ih_i)t&Q#T\,"/5]St<_VnCW",tUN;Du":YG3,hpOhp&4[>"MS&3VU0=(ZY
%/517\#fm(2':(S5n)#9Pb&L8gX3t(ZTt\;V/L)XO&JWDJ$7g;Gpp.h4g&#U!MOpsrfE5=FpaI"V)='%4,J(F5/mD]I?6mQtOlK@+
%Q@%]oejm?AF"3@aBO1&4[9bJ5hrj/RH/[Z[/EJ$4mbD>oh0eE(DiFc\,kL[`]n_1BkBb"'@GH!V]i?:`)q1#FD<K'2:TR&!ra')F
%97m1fQU16&07smkPa#Io]51p]NNLUq2Yc5l)Z8(93Do:D<lMQ@^ii;Kig5!fTi#s;i-Sd=:#s]u?ag"DM^p2i)O?GcJ6W'K0hq>I
%9MMOZ4>YB5Q<B(4'5#2G:Ri#qi0.aUS,)[_Tj$<:p_kPfrVXq+?bUsYqV@(]Isgft:5D(5k4,4=Im^tdr4D"Dn%SbL0;#t*p\aFE
%a,b]EoB1V6P(-d5+$FdK#@%JJqoe"ihr,)PY.4>ap\:rl"[Dr9d/J'NY'tQeO8nRL^4pS(mT5Gk4_Zmdo[bVl=7%l\/ECnq7jO_1
%;g71ES\B3TW!iUt)B8YZc:Pt_Y]0)(%3T?"%iP?eETh-J0%3FmJHjoi]iUbRaJiPFmO>A7GP/;_g9UuHJ8`c)D>lTJ'W+V_=Io35
%V2DmaMF%*VT_<hoim?I]Dm0jHhUHh5/lg\DCE79.!q/9^2puh"/SKch*j5qWPhS.-7$i5f0%@YbFAK-46eaHRj)p,;ftQ)b]%"'(
%HD5>H%^:nth_sl*;<H[mEJZ=@CmlF;c%)-r]H^uLi;'tG]n*jbQ[1Ol53SK5q__?>[S=6s(t3XS?)1+VS#-E2Rf9qf0X(M%I(.4t
%odOfd?g4&BXOj<E1?^%]F<A1]953.iHftt5s87o4ccbs[*h^"'m\ODX;q]`CM*Ar<&'3"?r'Ej7Pc+!?p]Y?]pV*VFU+c@#eT6R2
%lM6)3W^U!dY=R.03lg6h<=Sf19;A(OoMLZ+SZf(iC&?I!cG[F;`JM=qd\u=AIIl%*[Tj0oVW2fR/[fMBd$IIcH7mT(9aBVBZ##m>
%f=nZ*D0jQq9oOBNd5Oc%Rqm"V^u<eEF_AJpIG)Q;k7W3l1rq]-[nY<mN\3</[l\3KQVEEb0%&9FqfZ71!S]M<YC!G,!MAT!9g@pR
%7,Kh,Y7\"LD7WuIR:b4GTJ)cOoj"50"rs"Q.WIQ%rAl3*[rX^rbCL],>bWm?rk%&MIe4Qk1-mU"MO4ne4i:nD4d#cR]TGD\1]/D=
%?D<K1m^BO/,\lN"k7pQo;TZG\l]aXRiKnNB\ec)^kU.VW>OnVp0[t+.+VEC6QC77P=MZer!S4YS7HHtinR2ss'Y-5kd*O9T6+k=5
%n8SWp>`]jd88*7c%BTfl8b%[p.I]Z[TLlu;8G(LW<6r60(#"g7F;Wm^6:RuHRWEE`qbUE>kooVuD8tPfilHe^o\"G+!H^gHp7S]r
%K="H1G6aQG+aL4!^=oEENh:tC5d&6*Q8$a<KoHAL.ni+@ALK$*TK?1h_#*YHUhb[,,;h`I6^'h/S]Ak2,Gg</O'gqVkji97+\MMj
%<%5PU9/=mPEhC3k^=n#8B=@j8'ZcEW'W(qrCYf!h)F=!hpCSb7V9qkOJZ_LFj"HZ>JVl'h.XWK*e!o&u)U!c`R2e+]6+d>'7p-lQ
%A"['O]tdr'`q#d2a-n.?QgAkj7,JUjWL(1qC&M'D^TcjaN]-AKcRj<YHe.6-a4t.Af8,'NGUGd?BunplGM,E^HXCmV'\DPkdWGp]
%$INu<R94hHdM&;>48/Ij*OP9KM]f^Q2!>f["k<_D?3`VQil+0DBJYeD"8\#hi>EenP*CdY4)#-V<ZEVdkS!IN45CnoEMGC\h?!6S
%s-0*_?Y+6"UZc>%;@,B(\HJH*LNAeNFe4g)TSMAmFUfH>o$2Ds.[fTG+mi#@Ind9]EO_-X$7cBZ-Hr=9Ka7c6`6f+jIO#5qTg2Gk
%W&GIN3u-'nDsbBTo!GTn()Y+Jk\qpa=b*F^$OcZ6<[Gta&i2>I$?OUHXbDQ4M9L6=h.dG(TU]Z+Y@Bs%g:eKWK;]BY"-7Fa%1V]'
%]_O]d&oY)$grML;[(1N+OhgFNT`@5;WM.6=4I6luL5ZJE_2A[U'n%i-ijhDE=nZrp<LYKbbP%Z%(1L0NoZh4o?p%jiH0E>N_68g#
%%9\Y;GrZ::jZO=RVnl:P$%s$(]it&q@JL.@KR#>WcX$[6UP$gjhhUlV4&tK1+4`STTt</gOm]@s@`hGYPj$&QJR7ggB4B#*/c[VQ
%k3>3bdmhnVJdjZa:RuiG0%DF!Mb@>&*^m4T-a9+Mn7'2lXUfA+K/1=(las:Z/_?OnSPo5:G:^%fDAa(ZBF[NuR1IY#MahZi^^JP*
%!YIi5^dJ`>BO@K7.5aKaaM(F'U<CK&i33R+P)&ZHJfJ5SlfNkHEn':$@,RtE(+48BOCMt]KDt+V(4/oW/IE)3;BaId4fR-NZ4j"^
%r/Qp:`C?cfBB9m5L#jbFiprb[MlP/r`rq3`H]hOF(1L<R_8G3,qFhB)bZSee"+8s,&\Bc#`mbZ6+!#J*hH`[*;.O,H]RGBu7'&am
%!"QH$T'4,>=E'=59>Qh+A>+X3g..'p"OY.;Y!\t2+3eufMG/GZ5)=ko65O)GTs$2#4K5^7*=FM[Gqk=^E%u,SMg<>o3[-c<d`0FG
%;Lc_EmB:ULdp>BB(4#fkdela6nVS.rn>e\&6OC\ci8NhcQi_STYYFBXd&P=l*%f6hM-aV)N0W1[lOkSB@togXq(o?i>Z=CmK9`s;
%92u.(Un\*Ml"En%+tYk8QQDACXWaL56siCmUo<3;dodU)XcC:fPm.f8PmH\TLrl\O%g.S!?p#48nI^Q"1%E/S^D&k65h5HYWAP+q
%]4\,F$Lbl\gRN;A8b**,?KS$[,.>SHN@>O3C_]LEb`m,<$;#n9[!e3Js4#+XQY^"0?%b>9a6)LB`#:`LPO9*__0I3P+_?(Pop]&l
%TXe]ENCC2[@m)4BoA4Z7A<#KsQ;gj%QI@GK'rF[K5uet*9gi!X#7"0[0f;(Z;(Kdapa2r&?&:FqlBb0+bsq=FM_J_2SgS&*0#f`!
%I0Cc<IY(];/<6kj<'?>a/7,i@`*suNLZ?]KR-Hdg25pU&1(l4N#V&WKn$-LCIlOOXea>Deo\>$%#Uc?/++.-!`lWZP(Q5Jk/UA*C
%,KkKQPVs+C5^j8Q0\2\I5h,10:t2Mp!/$D&;XGZ=_N0^hYfU?<=Ug5;2P<.Yi2!pE]dQb&pF%4clWs?`>Z[L&29X[02*2C(4gQ^]
%DQHLbEnhEKVJQ9Ma2`9UP$We-+?I_RWRP3Y'JBLnB<##5?_SUYZrLGt.%e\H+Kq*.Rg&#C@>sH<HV>J-pCetpeX*-jnEC5Iik,R&
%5_lfXr%KjRSl=;k:bC@&QjDlQ\CYE3=q0NnTsntuL9'-3rdNR:-.<G6"%[%q=%^N+EdmP;P2[-YaJj5DU,)5dD5BdqVfgkd%<un<
%5J]3:YGtZbY%kAM?kR5W;?P;!r.5G<=Yhah\]]5\*m@%+j\3T;jdF[2Wk.WVCu^9Yqkn\-d.PZP.o$HhLB>>ff!$$aQSrq.Vm.N!
%8.(/6F90^-EDr!oqh3cmbbeQPcLY9%BfRN61P7!n&D3WV`2H&dD&GUH:%W1tZW;W7LWktAlug\5<L$\UOC_mUV>*_G*..0K-W_B'
%9=:=Eb/O)khV&/:hGLWcpkeY1=3>Zn1Q=g3-L+KI/DDn'`Or?A*"8/D?2=k.AC`CmSo<g4-pg6oTo5b+\=bR)OFca4'`O*sV^+-M
%iY$qNK!N$F$'W^-`A"J,dqOC$^?tB+A\]qA7Hu#sn<ME0g8nt]ZfQoE9'm['(1m,Hqpcl;"7km%A>B-$'%=rZ^bq3BL5<WhWXuKa
%K+1d!C6)Oc<@Jt1-A)k2$Z&d9<c!sE+pcjnM:E<sb3+B'P9)*eB&mAj!Y\5<NK.3_DXVCGDT6<kaA>;s,eCg&q,iE+/@L:b*rJFW
%6rnXtkepQ(fSZI2h]qrOM4f!kP(C2I'RFM"NE&0[J<;]B<me4Xjdql2U9)mA>fFF&R\$7u<IPdmP--Ze>VJRIBZ2#Mi(T8S!gD]E
%;U2!k)XGnE[i#E4qbblJ[6^YOeqTq,<,gAA*:(auXOWp(D@deo&2=qiUu"]&$uBroK8Vo%$S?I_goL0TLp?'a`27m@k[Z((^/^:T
%;:5&',`A=;b_IC*4#-u?60[6b>j+TX?Gf1:TKSu?E%&qLDuDB.DoUu%1j#sK7BCEFXKH#5D-YktZ/HCn,duj<'"lmliQ7*U51@@B
%]nhT+e0W[],:NF_,^kl5YRX'd[.BIE`>.V>+-#Wt$W69M;+;dd^r@%)CCAD79cAZ/gokm.pj3s<bj0h5S-]D[ED-/HFB-%oD<G#H
%3RWJt4k<UCDdS5NW3[4*%)cU7,."Kba\se_>()`PEV!9A%Sc.r"jU+_W%N@u+eY,RUXjZ=K>LX4Q\#a#[_Xh#SN:+.<=2tfZF(Ue
%#EG-5qLar6UGel'0-Xkk@P#"ekL#fhAN7aja$e8gM&;XLrDk!enXHX]-r_q+nA(aV?c6c^2ARIa'!V2WSQV:$*imGdF`o3i/I!5h
%KbSdmU:&Yk<LfV_N$gJ#&3ACp7=]![mk;?HJnLiISpaupRD71ROAa!i`KlbCFdO[+l8$D^*27CQ3;6An`m+^U?208&/e:HUMc`>e
%C6pKBc\KI!@Wk>+&,6Ga%qgW2cqg$K7u7LtOYnlG8LC>]^Ko+#f^n[!Im<>$S3&<$'Tu0Jo*AJ,6Dse*CD)XLg&Te_*Fu=L(YOAm
%9h)2IR[43EJj)j52N.Q@\3;<bM-1tn`m=">1`*%$TY-eZ'rk;?4%.;u=\/:6VDYa-MU^"lJcT+@nuG3N803QMBfo6o6'L!.$ff,4
%kr\a&/I>A]Bnb(T>R^!oNGNZl8/B5h50Pd!mAu4/@1-[kibBZoCY2tH`emMEB8A^X?$(6g!50bfSH5>r7@4ns'7F:n%GJI0Rqsr>
%MT-RHfE'r*;\T1+G@'Y^_)2i!bVb$-+bN84;("aid%XRi=X-AYPR-e-,)l5r8>sW?88Lor98O+"49fh"[*6+Kfnr&O&<4Ml-YPr&
%a.TZ@cU;7-U<;!-<i#>>@Zu`O*6,^MS/oOT*7/?<)AjGP3&&p=Pafhliq#D[Q5dt^e;hY]LrtE1k;$5+3fcYkR:9W5cOEM33f^^g
%]0)LV\Yd_[<Rf)Xi!1UWnL'VMY6IEQn2Q_5SuKD&r5.YBd"eC]H)TLU5!X+YMBRdFh!D8>_P?;021;hg201'p+oj>XFaVZ.=:\[$
%l5L;\%17M`+1>ndbAg3Dj^VDh9,65KQID:;.#LJg<^Uo'F`ih_NDT[O&//;:a[8$i-7]XAk/B<I<"$Y,it/3Z=p:UD_'OUK_r_XU
%Wr3Te.52emYtXpd=`.b_'m2M$8Wg&4+,OYD3$^fHQ[J]Y4kb:+!d>b#S.bcor3Mqp;'/7Q]8/GP?a43oVP&U\AlTgWP]'61JSctL
%(M.TXV-ABVZs:]AZ4R0J+t0(Q#d;fJ!@u>E(NP^K+-KVsS!88X-f6mpf[bh"Yor!c<I"[71l(ATjh6["Yl6h@SW7rgMl;tApP"l5
%>?"C)MdG8W[lt85'1AN4Fk9%:]/K"!-E0"V/>WEKdk*g30+ePDDBeXANg1s:.\tg&eL^de^3^mBesj$8%Q<']F@:rXLEQ`/]pU<n
%<OI)codm_Hn4C(Ka<<'g*iGoTnG4h)I\jNJC3Tpcf]%`Vq2%"b_k,MKLCOeXg3c^LM'Ij0CNH-Y><42X9(XoZ%!r>ud0MWT6;C+;
%M<YKsh5Djm6c%S\7*%DQ$=n^-W!r,[U*+iFqc1Z-L]r.0g;6$<%$R[5XOVCS(imJh7TPP#h:Ka\Lciq=K0tX!W:K(#^'l+:JfW(j
%NIHZic\1s[ksJ$6LL%\Gh%.a`(ckdc`]!0odGR%%_oIR]+q>sKUNiFtcd[[9A(Oeb?$43CWgn$F^^]f#`4FUG/!&`.3t]f[Pgqjm
%h?6g(6C^eRZ'2Tk@C8n2R>n4`29C\]5oC_V*2`OrLRX\XHrJo<C"_:rgmXc*\JYr1E3PFooW%.$[cPmroZ#VsQ(D5kGU)W_)g3qS
%j*i/hW=Z*B5p3IAf-Ah*3C<]$Md_fMbS)kOW.?e'3&i]79l+E@GnS-;HPmp-(1ehl[]li@!g\Aod'g-5Eb67-a_aJ2/97gMEEa"J
%aG)j9d5@EYKN91W[h6=*oMBF2Xj*^8C"NM,7F4Gk^%->U/Nc!9l"]rFMZ_Jf)/$ji=B[pS+i,/K6N"0iD`)01_ls`d$p,GR,#aZh
%LcYeg]FGj[OQ/T"=iMgJVZNe505BKLQJl[ej*q7a'*V3PI_dAQH-"Q:4:^GKds^2o.762BL7Q1`k[BQ@9q!OgBPeS>n^-5s#U#:0
%3egpNFqkBUnI-ntMSMu[9,05hdp[k=a-m:<]c3!O`tid+JCItZ/eSf"i#^-lKDUo0?imF9Q&=!ln70gnDW83pV#Cq:C-KV9j*@Kf
%0iIU%O!"%q+9(KhGQ4kGL%(p0Va[_8]NJouR->0-Pg_U<]c`AL+[:75>3H,K1ol<Y1m.ul3$\>mRU[U670AKbA)pl\&9/B7'q(D]
%-D?0WocS6OTi.bJi1]T#m6Km@^,Z=GC!De;J9Vqt7D\-QcHZD*Yc=X/-B3jfYp(HmWDlLL((Zkl<]jMAjiQ@R,m$-\YQSm`![sMW
%6PHtq>'bVE(^GGr3)IM(JoU#pH%Q\O>YE@V1<icafqi.3))=kT*21+oW$j=%'<F*tYM(?5kL"E]pbFj+&X&JSPSY&T6%.b95gVDN
%Q#!!<A!DXo'E"H*XO#qkigOs+\tBm[f[4/)Qor<s4mcYs8bDL_!CJ1]]A))-GBTOE#Ji.))#GY3\9I6(WSM_5RdX^KeXg801!r]T
%ga4WaK_Mc`UlFEZFbOo[DBKlS5t``gRh.5VRJ.k`[sP/!.M3/!XWa-Jk8FF,fN[D>YMU$MM34Tc\Ns]minGZ32JPct0T")&0u1TD
%2WF9MZLZHg$hm[hB@AiG>-TfW_7D$),8LC&B;Z@`_.[X)W`N[4)%Bn%*V4RHq*`8R$sI3`Sr=mSOgV-+PDFqtDE281a4j.?"`KDj
%\=a>5^_kni(rndSk["ZtW]=H/H4gUS!C=:76MtY:`4%-%j!]KW$2AI=*mU?k+fYd2R"Fus_C+i+1QPfoihHrd-/#.tFr_\:>`rD\
%.pHI+Hq!ZSSiK.b!;gk,(H"a>&r91'59[>5#EPb?lIZk]6_Mn"4<3cMa;C(cO%Bu2KHTP:6Z?g6Z8&D]J)%/R\kUaImhN*e4!bV+
%[Pj)Md$krc^$>4!r`-<lRkHEW;<j+RON!*D](0Ao=`Vt7cPeFoCe59rZp>M*60aO>`_!K$8<]S%3F%aog2=_Yfu:Z>CQQNc/C*H?
%fn'[JNGGWKpor?/B#JVhHjY%/:X;^f:DUoGRqb]-EhqaNUa=p2e3L%ei9<hpO%K)_m!d_A$5-fW&_!pr%(c*T8C0mIM?q(p\fgPP
%bUc(#!o^@f2WUT`iqaIT,n^)LZlu(6WDllgYJhq,Ol-Il@6s;b<i54#eFek0D7="OE8fX.&Epi:?r&'gmc7a*LDsaQR0Z/3#H8o.
%d6o)X"J'[8F39;$9HbMEMT9I9<CsE>4j7E=dT[(P(l(+%h+]FU4?/uLLuuZI$9fDm>%r&II/+nMLrr%,fU"XgC(C=$>o@3Cm].I#
%a<YCH\08(le,1-d'rGh!kA/ZE`5%,Se?^O<6^/t&>*G6T5E*Hp@7F&?N*-9O$>!USC0EtjbaM+>No?LNB_1FACs($;>__*([_X*\
%:Sj,]<EMAIL>*r%&.2eGg)AIP(F@::$Gq$JZM$]r`SEjpn0*l\dBaHM2+6*mBGT5cidO2pJhXT&\_\:>gliYD(N&s
%d:tY(\.6cFP8R=f+UmMAmX-S8iPJ*;Q736<Q($GgYns?^ZYK8<##^r"D]5j28'A\"i[hD_h#co8Z9(\nVX-XsK0cX%LpA!1:N&-R
%`u]^VH6\G[disYrE<k=:]I]29LckaqJqM?P=KL%j=C=5kK;sXbXMA7GaN-i#gM2'+cQe-1jiGmpXcm/5J?%<3%OLa/V<`l$TSY%t
%=NSPk@]a(0L^ggA1<(.6U4i0Aa?au`P55j(lLA]P-)0gO[%W2T2&)Vt5)b]75.%3(.'4%X4nL]]0+gRZB^[i6?'&rOVtB*dWClKm
%ker70,Lnj="7jE?+'0XE#qq/=boobm'`eok0\d=h,a.sFX[1%BmZc#q(9LZ:Nd\k<P=P,64mfor-HK(:&Q9$_(3H`JN@'fr#3eY9
%j[@<EMAIL>;5=@A&kF,J?Q>NVuZ7\5F]i$Ael!QJr[Be$r]X%[hk2?P=u;0&2_.:$cG7/&_I9gWR(*^!Njq)$J&)[V;!
%kdAd$,k/gT=VL:18i9:h2CZ,>k^06"'%J*oZDZ)c[l@("V?u(Q&TMt.&Ldud?5gmE(aeL"MJ.`-k-=(LrDknFi_J`i='Db.Zlmr$
%5k,Q[TW:cKa-ZjZ1/X0Re3kR$/$F3)_F[OR*kW?6?kBEYDA"JRF&S[o=ln+_i)?Ba(79;X&HoDgF/#X\MqoE?MFE(!P;@/Ra<%O,
%$Osn:*`S&TJpfL.;gFe\&D4b,k&KV[mY:V8fO1\^lhT7l17`)Z3ncG=j)N?)7eE]9gJWfTj<.JMMG8e"ibC=Z^+Aog2*GrZ$DD+g
%9cH,Rb)+EPn]ET*$pM`:dD[6[W'%=rgBCN66JqD<V(14'fOAQCNuiOi+>eY>M+!#r;h_r*O=8+O#IT_Xmpsl\Tl58*)3]0?k<tlJ
%X-qh.lG`+sjXG7WULqT(05$Z0!m-47:R&T]8JgaHSZN,a#d!4upcs%3Za6kPFb6qo>A]UjYA.]DPeQBR@d'l+HXLshMOrAH5Z'PH
%lT,VKAnKSRM>CacXSH7Sb-K-+TldRn)El[0n"Wqo&`2#YM2m.=KAI>&[%sIQc8NbH;8mq<qQ<4K>aQq%)Hqca$VfD_bF4?``@3iF
%c9>_ALs6])f-GR^VInaK*1FO?\K_nlY\IeZ3'&G"JlPrt@_Q%=m0M4V=XQPnX:r_$EMqSlU:OEJ1luC2$&`*r;_Vb"6fA?R8&`nK
%%:]9nC_gC/4QpT2*6I+M`7tX,9SOZJ5e3gi>4:,uVDU%W1<c,@6+<4h%-%kO%br6'aG'kn;j\/t`hM$ojA97*4,P+r/-]Ra4Y\.D
%Ho!.P60W?#U1nIT#KX/(]nnB+&e?KCWraW:C(O1Y\n5u2Pj)I/qGc/&kSObi`(%OEFHH;qF,L<E$us;Z&,J%+%0g+W^<KW`TT^J7
%1,7//HnToNV_\"W9Fm+;fLDd#$)1X+*\+&jV$p?j-AX(;fmlBcQ8$.re=^C/_tXB>`a>GbLp6Q/Shm'e&P#qSN.UV/0F1Ja"--ls
%)$H>uA0DZ`Z:,+;M"f<%Se9_:Mr.GI1,^U7=GlGm9JZk1WbomI_MdESR!$6DX6(%!4"o%biXk9pL-?7F2,&IqbR"K08Bs1Q4?-K2
%FYP".?aX!=_RN:[3,u=&)I98<%22TK'p'B">ScPNb<0m3f;J8Zkc5IrZfjJ61jUPiKPpE]U<q.Qf\'CYo4X55Z83)tL+p0--*Wk4
%!G6)@W-u+P.GVtK?^.[f;f[IUO/T`eWQ`FZ9(X6?7KQ$p1IH$ET6D3+J."&i+EtkP!^3':\*msAhW"BA,2(_-8<uNIX`]IR6E/XY
%Dkbp\;(=+mq<]`(!a6LJ<-js1%7C],7k9N$L'DRO"/6Bj6==KE+YGp=C_u)hA`KN-hF+Q,U2>9aUi-NddP2h6PGuq9'Z[XI8QA+1
%#8eVf0oYH`08)P[1KF']oRmXsCkV+_9%Xo-ktEI$Zh`/A@NVJrW\HO\OJS6/'au0A<XBptaj=9,'ZV^XV'uVg@;#_sX+Fom=jX&e
%JG&F"-^T.L;BABA&_d71]ao:IcTDO99%jqN?7@D<cWn<8]28(32Xq_Y^<)WkOt;^jZ8!?-cH/&?Qa8Ie]U`.`Y7+bP4Oko'*.amX
%X<W8u@0lTbOpMrMNPh%R(rgS=23cGE>UR,Cn-HmK),O)&bA_D)/<o^i0n:If1+uauG'k_\qq*l]YMio^10QZ_\LMQqOc'rk+r!=0
%)(5n+6$<t)AX]X$1N8Nge)<DY&95g4b`ndHdQu1VD'4g/[MG`!"%EU;aB]?i+"dO'Y!;tgMTto8f8t61-N6L,OTEJ&TX@-RN693B
%&ib.UcUBZ==jP"sJmd6&0[Rp_OHA,%%)E`:^bL.pd\EJZ`\BFm?A0Cj?".Y!4P<!:6:^ceZK!do!UcEP(iX<pNhqnd4jmUB1WsU!
%0ec@<1qT-GU_kY?ig!(WCmnb7B]>j@YWl?)eMk=((I'[TGYV;YHK#'(9UnAd0cl81U:7hM+\"C_YaL^E)<<3."$lejIOP^'iNk-G
%KPb2+L7ciN]#6cN5%N4@Q8o$R$ceX"_Ak?g!0,B<(*=E)kpdqb\KqKP@EtT,A)\rr'aYE3pl_u,A.f=l78HQ0+pChgR(WU`iFJ'"
%RKnrdFjr"&B1$nG3Xo1RaLMiF"m$u%^0*3OOCc"\XBkF2=ZdX[3p*$N?p-=mZOir5$'[_(5]H!*/id9LE6-p$i!k&R'(4*(%>195
%amamY$5\Y>\Z$*BKV)da&r'5f*f/u28cd@EQ\F%SO>B-??GhFDD``[CdV9&^8Q+9],XMLqe&0l&]#5)B\QN$`kn<+>C/>.[jlf+W
%R:1\hm@5]IZ"(fWArU$YG4$PO;F#AgcP&'T[51KY-e([El)^V);%.*D)[32jD5<E`C;+G(3(4\bF%]J"1$^h<;)?5g1gjr5d`9k+
%Cp.P6R`\l,G,;"rWADO2HdFT:[qjQIb'knC2KXYT29&A_3bMC-j1Ze\aP/#1MPhFXRUf#6&t-]>2Dk(eZ'leu.(?(aju(k;jLRYu
%hbs/CEEO^=`Zf>0iOG$!(=KQo#c-B6)lLXoDrE]'L;L-HU(3]I"?YoOSd%0;bYAa1s%F0l(:ol3MC)D]C=5UVS7_5R\;JEP+X9:R
%B@A*#\9#'J0T;\o$`>2rcqJ>n>06]S(jms'o0*f[=Y=I3\S?s@P5u!2\arM>#LWit4@kK"AG'9%*^Q)0VL0)B7@h=n.@$^g2hH'%
%1<m$L>#;(5>U_[$lProYkE._^Wr`1H$6h0POf/#saS8FqfV^/i%4+oNS9L`C<a8uGaeRgQaA,C7.(oX56h%Y'Y:U>^@<J1=d(JJ]
%PjJ0fN\5C@K1fdsJgSX6\BrB<IU09aetg-37<;;Pd]!jJfduD'e(eJ.hVX"H?j]MG(gmt&MmoCM!MrP50ca9sOVA,rAH[A--q6\Y
%n;B>c'Z=LJ*PXRDfml77P-NP;Z[]Sa6WOIO)D"fef.d=>)3p(F#ZWY$TR<%NaGo`us8K:u1;2=<1]*f`.%JsQNUFO7KpW`!LAS*?
%V]&if\VTXb&pB&#dc0cb&f@aBk:Ce(b2O\\mQNW5bem]=Z,/Sji#<*8OH^$<AZ;'s-7eot'D6KC/Q>6e7HTe"Hq:Z'\Y$Na_r&aj
%,!?=fO1c6tfE;D\p9$UZ:bD429:G1E2Ts(g"a<>Y.B]HU+@+M2?PkPLHD\@Z-(h$5-%@]ajaIDU%MNZG3gP((oKVF>1>IFk8&gqP
%C&ihGD:b,O;lh-!Q*[pACdrOgj-H\(.)k;G\T'TFP';cYALZNDGoS].9+iHPNSaa$G=e?[)(bQAD\A(?RnH)q.$&.D.7fV&UO-A=
%,Im8YKu6='i(9;Ip*DAt"oG<E,u"GlS=Ms_<?)X0^(L.aX,d;55"B%T+'jC-?'5;oj$fJT(u<ksFI9q;L3GX--BmZU[gn#-F64*&
%G71<#X3Dt*]qmnq`'KDn-V=BG!p4%B$-.4P",+G03DGigS%O]<^24H=GKYs(T'`2g="#MU7M>BJ&!?GJ71F\](O"+3a.s]*:"*t$
%N`Z8C8R&"W'2-(fM\o_EXYd3bG#*^H#,lFokE@3;_jB_Y_c0Q4YjXd:XWs"G\t_5;Me&SL?kk%%7_biJj[3*<dQm.2,_8P\@dk&e
%?:SIVYS%[J]QqjL"8-Q(:R4i".8EpWQM*B)>a'![9FRXHMoHu;8t`Y<<,JN[3@H4M/("NCJGED<BZ06IP+sr,iY![mpXu;kq^br1
%(7nQ#`m:%QnHgT=Y*.pI^dG1Yp'/j1i7k^&c"Pm,3S>gI=>b<iX"Blq-$'d_;H1[ZAW'WR?')sL',E2hWVLY0`R1)ed>5uk=KH'e
%*7r>$F`A0\A@Z^mP`V:'`1[Eank*h)*1\OE\7jQ`1*ncG*7gMPNg#Q3UbA4oe3BB[i8(Et6!lNI/Of4VqQf?[rM/V[a2P(C6!lOt
%2#[AAqQf?[rHD1tcJ1;[^7[-Kk^".SUea_ReN]MrCh_]^4m5fXb]6q"QbI9^;u^O"'Z/#O0X$^/Da=tZ_]P>Yjs@05F)B>?*3>;q
%@Kpk#H;Q*^Yk/Ge,.>lDD`+:IL$;S>RAqSU7\X($`!I+hd<qBDhe2t^X6ODl3$PQ8<A\l8TK",%bnb4]%\gg\%%FQ&jO]nbd7Rbm
%W#bG`A7#%.cR&Fdk7?mY1+.S.1bH`59i[PE<6qGBK>)%<1T/n(JJ12PL:1gU&maeU1d1&lVBnt/VJ1/M`O'LA&maeU1d1&lVEMt7
%DOU8=BtA$<17d^`1d1&lVB%04bGKOeNXY4,`$P8YB@HLGs'a_OS)\N*oMEGY_3"$^aAaJ=&m>D`ksuaT[$X*(24lM.3')]kgH>(G
%]-+%C$Ap-b)tYG3)tdI1O*[U91fa1\D*"prJ(+%_(\TQn,S(,7U*f65?MK_:hOU"OcF[r@fZl`r3)oa4ZCO:Z98LgbM@bm3KgRm-
%b"$6Wo9,k$NfZ[qQ69:>34&m\b^nHW6t[/\MsHND"e/.Z\jsj[G5HZU1<`Tb#sOMA9!F.Y=O]3IM5CW]HnQ9L.;FmQ[\i0\P$j_?
%MI9`uUJSO7[;E][MCV;C)D=R'7ddh@au=`4D0IL9/=,GaR0;e!o_#A_MoFtI"5cJtH8\K!2Ha85Z1Tm"X_BPU1;+!7WL.>,R^7/%
%AE>n_:@$3#:2c`nW@+%rUF2PV(Ci('c7A6RkZ1]e:AdcgOUu!iK!8^Z'%c'`QlQ=bIbJt"kaP>-(,qf.SD/BaY$kmtIQ'a1$F"-J
%+&a^=&bW^<[';C[Ke@9pD;.XUD`Tm^giP=2FjJN6-&>!33#Ant93PG>FSW*5AI@[4n%9T_ePYo>m]p\LmL8<^\hV8(Yh0(?+OOWM
%,dhN$P>l$@/?46`l:=]3,*4>0D2D"r;6kK[7aj?@g+/<R(1=;3L7qbE.%"X]aE6QK-](Q2E[eTEWG4mu"@/?\P8kb4&bATAFe7'J
%+r<]:j6n(J[tdjrdFSePQIOTE,)>>TdneWl-^f8GaGPn)ob*O(>O6*Fl96HqR5lD37`P3>JK*!5<C,DB'R`hH$mf)^`.rSVUR.0Q
%,a)UG(M^03d!W07(&hXeWJ,E$8?NPPpWE!11p.d2ME[cX4o45C;HIhG<#4[1.:+\V^-i\k4O5=be48F=Y.mS:-cC.PYrEKD2V'jk
%QK=G+)#VW"G=74cikR5TV@+XQ(PLtIVG.--)W).R"Y0cL:#e3R:@j`Lr"_-r)GK`1\SN^$1M_`_Z.#9%F>_eu0&OSf/rU,M%[d#r
%a7SpI.mqiDSqQ.0ePKOO6lPX;hqPu_IW`6ua_TdKp-R7OGYJ.*3A58PUi=-AgmY:p"Mr00!+)/[5Mn/NB3Y=lOdpQ8PkrMu,eLER
%)'usi+`j@Z-4Vk`0J(K]*&e:$>?i8u'E&G"Y`*t(>]jpt`b\</q3<cs.'`Oo")e*'8Ps@[:.iZeCXT4pjed3S05VX-rUpquCKenb
%FDhS2m$$X>(^%@CdnM]!O4SH]n.+D?dA%=sO;`1p[?.%E=e?TGh)[4A/D?b6;L'bL'8/JB;H8p%Z-$sUglm%0@HIkh$oTuskDS0*
%/^?.'`s5*-<kD0DOWTNIo8R<b+`_I7,YAPPSkdC_4jS(W=+\]$CN45^l7:D@9om4mO*5=P#T@]+l.JK!1O>4;&T>]q;>s*&O>&^\
%cNl0aR&/Zaj9kj'l3^gM6GERZ1lSR?<s7-LBl6aFaqf>t(Cpq6'lb^M9iAZK(YTh*_Serb[Fg[E8se:R\u#aB@sN*hR^)sdLs<*t
%Z?VdLnIKaS0@Qf.#Wu0b%5k_s9Xm["HGZF"/".dTGEV9JLig[l[HPNf?1'>XUV/hqqh.dHl774fMr$D<\+q^4k[ie53+/J=Z_?rc
%2)J&SYdBrroS(,&gQT13)Ul(_K4*/U;ps+G5i@JO^4tY"PDKhUEP>h0MIt.]!>B(*;74\NcD-"L`1h1]lGdbELWP]u/e$#Bf9l-[
%Cu4MR:'*G4URi6UQiMps0j$[p#RE/"d4RtXl16ThSm8"nXl]>YCj2:A_eU(YO<;2P-e1kQ"[pMS"cf9Gk<*)5HI<_;TZJIJAGpH`
%mhHInqGS`=caIbegQn1A[</0U;*q$LiQG+QTY'O_$ZmAG5F-6`_*sAe!)?_8T[.,XC!eto?=(`./([m,WWKaIU>#Ppil*Jf/Z<Z(
%PTaSBr_M:;kKq(?MfWMVd5Ji`0\4u>UDUEb3iAhcd(?<)da#oC8dGDF/IfWg$;$F>M\cnnPE=<GaoGp=,TC]298`PV-(,$5"?X7%
%)5,rSN"M`1%YE@aXATOqN24Z4W0f,/\b[n^BinM4&tcJ+ZDj/7Rq!6H1aX9HDG[4UI6^#N[gUnZN3-E?3<>8@g*Fb+^aW.UZDZ81
%I0&KK-C>PB_'pjh,#8.8=C%+cYHK<8#Wr#H?R8B>J6*=kDS"F3N]K[dDR"1[<"<K\cFT>GSO<JC)Kk$J;aUsKl@d0S8.Z7$XdgJ6
%n,S4#+_Q`++U#E.;K'0g%$"`uDR%rqoSUo5q"&,ZB]JAP_X0&)2t]FUVIBbna=34j'nhi.+U$a[H8cE^7E@6s%cdX.p/X"q"c`!W
%NsuHbjS\I?9T!DPJ4Q-N<K;D4h6ku?&5)4)<efiM]K/+<^(4H#P1GE('299dD,&;2&h<"=i6_IpfKD)/(FM0gJ>c_<(_")(@-T*@
%,?!#>S]A1T.Ao>D7<'5[XselW!*L6fo??u*SIhBG&p^>GbaBYo`MT;n;rA;7C!;HUTf<rs_nf/3'7It27uN_tdZ_KmZee%$74"9q
%)PjY(V9#/.VV8r@CY3uR[['D:PQcL'bhV2(3q[$*13.Q/obgi+Asut)2<`5nohj$]qW:0b)5ifq-aj(pRND*4'<q<'`gb3EY$GUm
%N>o85HF"md)'(0=UJD[eaF4*#hbZe9*uoa`^:JUgp$qEnhu;l,:Z&j4rq<`rO3dk$iq8K6_r&+UrUeY3YHQrH*kSHT,,B\^(VQBX
%?26!@j`fVB^]#&IoWLOWb>l.Mg!r=dZb-&P^@]$E6+7JsD:eT@rYIUKr8]VfUVeiDo%f1n4Qi3FS#b0fNXB*Rmem4d6@g*iP<$d*
%"*,Re"Ua[/LM98/JKiIUU>CF"5WqFunH[j[^O&TO.>Rs,AHSC(lj)M;(XK)XM,*8Q-B\OrLom>%7)XZ^3=m,Kk>,8K^l2N'1Vo-?
%LdqP3S?l6(cU_8pYlqYu!#WB#);WTn*.e9E4If6DOsfdu,9gD3Bf2!]Q#$*QD<=I[YU2D%\TQ7JD)eqjH__EuB7L[t6Kc9r*m%%s
%7uKsYXg#HV23W6<CK:s"d7IR#k>R`;E67Cbn1PY)bl>j$e/61ZHSeP1ST9l"&[rbml#%QA2L'M17%'5k!BBYEcjB;>%hl%*?^fuY
%kfJ?PI^1@)Ur5.?,/[+-pmC2c8moKSX52;QV@*0"8,j=s_M^a4Qf]L%]ec(\d!&+NQoB]=W:`:#nS$l=6gdkq,4O1LF;O0aL*fE]
%et-_<:i*EpUMhd:TF`iN`L;W825un'N$mon3f7I;e0*"9=*9I4/V>I*;??.hT50h*+N&k:$K7gZCTI"[8L?hV506Go(-1]48*h"0
%L`i`B0Kt]t2<H_ShL'0jB8N5HNB<a.6BjZ-HQ"O]$-"IUSfstT*D"'(0p.D69Z0(E6t%60lr+>rrSc!5MDERG8:OdI/8>g&<8Csl
%Ds<Kf7Ul`<0b]Anll*'&%/10Me?$hFF.t-*9O/6Ta&]-*N2=[\PYjk>?7MdNPrqFQa`E^9H>g(7gL)n+YDf&hq.-NQ+?We_V7^H)
%mQWg64,B@4V=&V)4p!j1!_4*?;K?7oKQMZ7,bdGUBS<)q)CcOk1c#oo;=*^VkWqQ#RGNf`?h!ak-<_cV.k/joA;Z2/CPCsiTk6Ra
%ljI5G_3"KO*49R(+;$>>8_2&qUKGbOHc[*kcVs6GIV4fSM24*#:hu</5T\ur/0f1;U8446H;V8lquXQQ%P",]B%K#`"@"2Q5Xs&;
%JGKOqK%K=3P&1!#A.%>q'qN'tGJDW+bBUl?l?3qM0G@_m@ZaE1^-;.a2%U1Kl'N_`Lo?SZ%D6;^>o7?MOr_sY_K^q`jln\D9XC!D
%mG+8\.5;o-:OZoYKRP1Uos%lkeiXkQSCF+<B+iR_P.csX[l*<20F]oD1QguuFI?MWbD'8]7&L_iGCps[6s!ir4%):H8'O52boSG=
%ZMs[4QKY?^Qa>;`Uc!]^kPX%)/j-hel%g_q+nOnP&I,]M0kO_@oPgd%-SA*2>kMOO$XqM<0dhMW'"T*.1!-4Z%\b71?/p.,F:&m.
%3hs3A#XX;G66(]9AB#ad@8**GZ96?d1X[[m,ggF'kZS_'eVP'eX[OW0MKe)`f2RE_[P+SB>+#Y21_UN-'EN32^ZQLB94oY=pq#`0
%3X^O_R*qK9E<tRrEeUqiP/IAVQCX'i8_kN-a>HB"$2gS46MUY-i'Xu]]U>][S\l_>J?bo#3nl0[URR_:dAeC:fB6<!j\!/u.rVq7
%c)8*L=6*HHL4=-2\MlZ#"1,[o(@Y/-Nip<3`A>smNL?p0l?Hn4mZiW-<b3IUQ<SB/d;kj>h0TQ@5&$blP.<;HP.;5?iP*pM\^TV1
%@)a0daLHUANQpnEVDsUOhPD:GR`S;H4\VS#g;?pKK,#-H=A4*mm&J'`o[jl<)*]YcOgDu?!tbWO4eE6qf+M+0pRMl/bML.<#F21e
%_bWn=`/:@'Y"?sP4\C9Re198:>U`s)6:AH@Qodd3?`ncb-I\Ch.XJupeOg/;M+IC*(cNb7SgmLCHEDbmF"SFA-gubT<$'$HV]#hD
%8e69=(M2IIUabA"M%iG'eS;`Dco\!A\IW@.Ag4r9of&^4dh!P_HJo*G'I0%q\7\7u.3tX*O.t>3&h+bO%o(O`7*g^>a:Q5*5.),-
%XK.L^M29ka[[:/%>YRhf0Rb3lAhCQ5FK,mTqTlJs"5gPC'InF_S_!.=2-,o+nDR2Tf%L!h`$AduK(j[2233T,GV%*m#gUu]>GTC,
%&e&W)S[Wk*X$?#odV0eB^G+l7M82$]XtchK;2R-33/rfb%1gEAlQVUr;od=)HB)Mm.bAD+F^+a]<1'%A%M7h3&c(=-CB-Lfmk$,6
%HS>"*ag.ajC.QOL@e1q1Q%2r4'ce*$peic^0!d*5$?frhCb4IF5/`/rV_Qa`2Dd0+SHla+PDRsN!sO$,OhsJ1$@8:YLnZ>TeH(M(
%<AUrU+XPe?5lKNlY[3WLK/Ao?:Cn+JFi"u\4_(XkRY32G'W7B+QUJjt?OT4g6AI8"#n+]K;t>=i=:Grm:3;(V19nn%)fc-g#MGr.
%DL2EO;gLJ[]T9d&fonsp@6XlX8JuZ,fOD*,"HJ3!*f/=+H%3C=c7#P/=pZ`PAq@7H'iM]#YIa-J=W6TYM%;FJMD".GRN[Y$-p4TA
%2(,K50N=-0X_d<`nkATDP[,+`&GUkA/FR"V+5&&V0H,ddjE%LWNihDoC/Pon02'e0;2V>TO`,ANH4[VOEY^B9;n_F/P%U,<26-]s
%jMl:ECUr]I^/nTUEdVa#0LUK92b3FS@Q'KQ7[G>F#uFBQ<?I6p>]$))To=9!V'oX$-6cWof,B;_?(.(u_eeD0C1SPuLTbmY@D&1B
%,U=P3(H9Hn@bJq2/QX25Q6OI-?amfPA.(^LRFYn;9KBrMekN;(iJ^[B85k6<MRW@<l'b..*rXa&qc0`fS2G?]-"aD[%-;$<nSu3K
%AQp`A%)qS7`*k^dKd]"rM-U?r/'A9*j<M6!bFU"o++$jYr`jTjH6%Vfn79,/g?WDV:_[&Z;umu2QNP9L!a?g;V58.=>OSYAX&05Q
%2YAi)hnc,CTfeMrMADE_N'lAf@L:Mg>FM&mdA`T9l!e$cNiPZ_@6B,])PIJ"YeNl%`5'7(F0:pjS%.*D0(Ns20"km.lD]fXm%T95
%>1\pBkJ`4S=LAd6N-9HCnTJ3H4D(b#_kbC'GF;0J2UR]4L^eHnOYM)(L?u6IgJt/GBsp26WtQYIKNRU);cP(4"Vj^)Fa=E1`1j&V
%i)^Zr)D]R1,>!W=-3c^)U*tUiPSc+m1t<C:gISF9'@GG-S4d^heQpM.'%N_u=`sN'&VCQs,)1VL=Jq=7]k:>u@^ih.;kW]H$TqlX
%G.(F#XGZ/=Gc-Ef&d.Y\3*gZI>\9t8eX4;4VTU-_5YC`tIPq<D?,ub4WK9jk2WEQ2XP&X>@mO>HN00>XL?ZMk-/C"'&%edY:pl%O
%f4,7q/_*c/@c^bDYj;EIOE"P9=cH%5U>@me_nAr//.g@8@Q0U2f5liER`-[PY*3O3b2\9E17)ur3#g=f'EO^KEqj'$IA'+MKo9"G
%Esl>)AN"J)j@W!ifWZs=7cE,R9?pjhPf?;8*njkE<(/"ufkVDjTMgmhh_;GmrY.LiFAdCOR"[2$SkpGV$4,VrHO?O"@`icV.MG=7
%)C5)NM-_Y;0hm6TJ`iD\W%0#L*<fBfS?[sg\_G+c7'Th_FbSU&3?p=\@$^8YAm2BgF0QpG5h]6$EOPNqbNCX<\`s7FoO]XFfT;S.
%_=:L0:gBJ",GCcd84L73G)Kt*<uoJk=*Y*IqBr_Y*;B;k`Mf#(%?T,YSkkX<H.f/4Onl#YiIVZ,,nD(4B)L.E:EimbIUA,M1-7\(
%#+^T5=iMpuSg5&oeN:.kLAcuce$2-<Wh@VH\&FR$HC0pNjf(hA+nF?\FNuA)<`Uk-c6QDnb9=l&JZD)D)8:T[)lq*#TrMbCF]EG0
%2utb+WKTL3`L_]W18b4JGHK%2LjXcaZh!1m+G:M+1@@i=[E0S^<UE*'PgoWmrU:R<DM.1jk&.nXRA<Yj3jY:5dGcN?!sZj=D"D0%
%)auqJko(S`6EEqo-Isf\VRYprQpbpN&L:Y`Sn12sdlP4=?L77Ne!-efq^D.R@2#i//uJ1jd<Be,-]m\H0k!IK:dl1297-PIbpS-@
%U&VU,$%W+#;6[u4GNqj,Y.u*%o^/S14u?ojO9Dt"\\=GKa;4"L&URX?:H!VTl.L?)0r9T?(c,;Q@^deIMggIW$0IZD=Os0.%-l#7
%7^K5Mr:7PbBp'Z1+nD!K[$FuB?D)<1]OR]J\P/rN,UuP9b,%"C2-ZgjD))IYTo4D,)-n4jAOM5,STR`(oA1f<q[mcmGtAZH4?@'7
%7G2JHs(CfFH7M.=1uWc`rXZXr@"3,qkJ"=+m'>I2!PQ9hGAAlL#S`3&f]Jp-@Tcb3qCc?(,*SM]/R.=X8=f:0LQ9N@Dm#5-4op5U
%[:4\2'ZR&-`'W(c!a*PbK@"1P4S!:1,kG413I-=NVh"OJhhs@0LnsMpK6V#/lg>mq,YB3IV9t"3'0Y)&6k>n0>B?(.%UVpTI:`:.
%N$8;V:l(j#'/>aLkWV0i2Noj%`\Ks\-[pCqNG`#JAWVSZjo$3$+q]`<a_8D;mD4Mdk<5SPJs`Pl\,0[&(G//bL^(s\Qt(q)Y:7k!
%=sCm=?ZaKEqQ*'s)!O?j'*I^tChl?`LiZQLVOrl9Wfrnt]I-E5IAX'$PZ6ZX@ab6),um+0pNh:rCkN`%q/Dc>c/>?o%kYP(9(\Lb
%T_&Of?)Li01i+BRQsL]SHVZKg+l2SZc4FS]&EfNuQl+Gu]`]AG#YJ0mpCTf)YscfE+hk6'K!9eQ:8Qu@^1_>UE*.ocQ5SP8d(KP;
%BuhI25"Pjg6rt^='HJn_GVOr9,rS673Z.uEboh&@j$P&qQ(k!tbpfik^+YJI$WSId9SbLkZ&03RLdRq:+rcSLTa)r`/&Da\W,Z6f
%pZ:YO2h\`$lq((O#HP;"TYhljB?2:=:>+-7%CIK(9nRad0DI'`_),u9C@LCaLT<Z^S,6\tLNfY;)]k4o3qsF7R;.&1#V%$5\/q,5
%8tX#]BGkMXU(Y%k"[WY_%o6$2Q7>8;8Af^YQU]<*a!>XY0Muu*W-OY%<2MoKEf@Xt&';YaY)HA,JEJ/(LE>lBiRMs#Ai+3K_i\44
%B-Ga:3o:OFg@^"J+sZC<OXa]L[d-#L8TCHI$jWR[XkH-_^<[i3jB',+-%NER^Ca214,?10=Nfrm:g#ec8Llme&0.j+ROgFm<Om)b
%qiLK442R2WJ@ph6/9_f7`2UL,"p,3rQLbtG=untEA(:S5BX-`'i&%eGPpb$3*%;9NS^*UV1YJB5&-s1["hS;193JKL:dAc#'?"(0
%i)N[V0a@?%b."C*:m-"[QH_f/Dip30a2\(r5KlGtZ*Etr`2lh=7ALTj!@'j!i=RTtgl*oL_JgFOBmWWV6,Z-3f[tE=je1Vn&oUPq
%A^rcuE?5IC\$*Sn4'H[iY[`Iu],aW&T,_J14e\p_7Ic(SoihM1V_D=;#6faGA_;4@GY>o8f77.KGYX[`TdUCJ\5!??)@6PtKo&`Z
%K[':@cg)OA`*f4K8.(]IcLu2'm0-67,BRV[6B("inTN5WXD(SO7)hU.hTJKG'd@>()\Xr#Y0!Y28ZX&/=Wt:,/2(en8R-VU22!tN
%4\m8sP,<Uekq,6K'eQ#6^"s?XWrRe\0f]oY81pk)62eT(_ulX\1]+s)DD*&_JegZE(1$95D@N('4O&L7^\@W%Ysh1fZ/+4X^smQb
%N?Ll8=i"<:oPW&b@[/0IaQ?>EQLUS\*,:.2:>!al[Q[duGJ5s?6?QY@)RK6b]6o9\CihO7"\&G+,$FAf."2CJWnA`CU:ZKYCsnpo
%4#m;he7He]mR:-Gc4PJb$9He?)M6G*I]E(4U:qVmJm,^)Ogc^S-cs'e,H_2L*ZW&!o5s:Jc/[;^nVh3$,*W?BLK@NP`"r!(Yc[[7
%4no#@U;dDI2.eeE8\BD<FW7(nXdh@Q/j\YS:n\\f4h;r6&5hjC'.#+aP<oHi%F[3,_'+&*HB86jZo![P]a$:/>55ncCr<5[[RP$l
%&Xb7;DaK[ND4sLt%1u&%AYOLO]LX`[n8elE:9;E&oWX9bDeG^VjEJW?<,Z9D.TRU4'e+J`8$8>Y"Ke<;gmak/2?rPocoIU`QGQ4L
%?KuK8&CFQ#D<_`l@m*fgI76dd8o()h&EdTW5ASH&NAn`]bL66`VIBY&W)l)\g%'nH?+u3\K`YMQYB.H"%pSA69`"^)<k:a^3d<-B
%QnX`b0tX=.F1::QFcHOM$,VurUX5Aps"`Lhgc8'3Q?.64TZhK.]=Rs;`[VX%\B4qc079I7^cI8uV%?cMLL/t(0jk,$E"2<WcDn05
%$5Z,-4teQ(I?4j`U?fr6d3BMt,Dh4Y77#!'QR:DM6C0h\\n4.#iemFPo'ZH2bbu)4kI.?oL4ohC;j]7kMMTFV9hT3iNi4p5>F"!,
%?u^s>Y*Yfq/O(7@eRI,-bkS/1L<C4pE,W\7E,)`7W\3fOQU^GR`>Tso7&4@8"N_8gk"_pAL@?Caa/%<U72@5MimpO)=(2ArK,A53
%^?^c6OlP$.@$S8eMtNl5]qFXbGolTc11lZY3+cH2C!o8<n)e7:Bm)(g+[i`HF>GN()oAXeomI(G@V_(-5$TfP8Rb)0/gpt*9tUg4
%W(-.%oF*:O?_pAIdM!P:<oULa(D@CGY9g*J#D@Lc@;u''21.BRk6GTR-Whf],,94?pPe?]Ar=;B9^E5,qS(pfOWO/lh+l\++Zp<X
%:aNt`<boh'Bbp[+br<ris46^X<i![i8LTtb!TKE<Sr5DV<YHG]IFB.`gn9uoJO^EK?5XrUfZ8/+#9pal?!cg?l>#kT,K(eZ6q$An
%boUnXI'PS`ccp1#H0lA/;u8>\/GZM<Xp%0Xb`*jnXpm\FH\e'+dR+h!LDWt5#jJD3CHu2Q.#Abt)<V8#T'Ad:E"$$@VW,(=gcQ-p
%U2?U-X*9e$j>pgR+rJF)`/a"k`#8G]DheKKH..0]\,[*UIt4*qZ\B&tNVNEf1cX'c#,?sb&=/FJ@*[3jKH]4+9=TK`<MQHFc4n8:
%Cs6Z7+>QKuBOoWqM24g)&F;!.da&t*@s,9_+ea[]oF;$m]cj]*aV/8N0,=&dNAaon*T8""C-<]u13e`Ni2W%S;sXF#fX_l17u1D/
%:g-UQ7S%^Jr<0?g8@4E#P!VHFDCPF`(;up`G_GlXc$[>W`MR2'1jXrc[!NesY'[CNN-]I"NqCP"n`"*ric54Q5XD)P3+n2X@o6cm
%481Q/YD]#@:TDD\;OD=Q!'*!OD%qbk0f4fYl!h2ddss^"^-p;EFUg;(MRdlR!U2:(OV)/6XdQ?IQ:Nn:?j&(TV[5)^QpI=$cB>iW
%ihtk%)@\,)ZUOuEOTXMV[oZIM7"h5>.bU]H>8"F^p9#Fn$\YoQg&U(LGo'0'b:QI"-<4jmGGO%a$a93bj,63H<^d$`2UWLK1,-l)
%3(4?<,FVdF_G!o]mDYj"6p2`i5.5#280iDDMC!=7,1t#Q1rq6q_>HrMP/(S)rJ%b\&[^%i@s8oC:P`,#_2SsOcQ5(!6EVGOZ-7'u
%/l#QhYe5t#!pbL1Zm-j\=D_jqAn(%95$4m:B[<C9Z?'T$lP[b!bl_'M+k?<eic/W;H<K$l<uWUc#0NQeX1n4>)>(>0/i@@LY6cB"
%6;1<:-%e9u=C$mkTpDj[eB'#1XU"X4qb0t)@N.s=rFB)dLr)ISg-N&e0INPJa%"["4f@CW#-hCVU(`b`+i:kS(7ul"B;-^/m6Q[[
%<Ep^;bmu#BJg<9WX:fqV;^`uNU<cEl%6`NRQ`!JqW"LZn0lejg*O!JBV84M.Gi^N93-hK1-7rN?fh5RR_VRag??4@.LlAuH6:=kt
%X;UOp]KsgQ\rIn;k)'u(Wt=7hF-=87*>$NJ\o07!o2hgD,R#i$9@WWmil1P-$*V3d;FNo4$Y>JXago;IYIP)F]Fsad0sluQF3=IT
%q-YMOJ2tkNlEouj=u2"ke3&m54F.8OCIl=nr&o.6&uF:B_!5!hP.Ljecn0dP#nP&arYalV9c!V8qBMegAfW];rc=Ainfe)D6EX:O
%I3cG3M("0=c8/rq79ea5*!;5=N/^6fP.jL#k/B`Ef52jEEWuTd\O3abPcJ#tLPU4aYRBtUF:?1QP7sZF.MN3Q+_l;I29ReL_QQ8c
%'U;p]?8T<RR0sDG40E`:+GP/ubHW-VODod\TbFI4*[8dp6UVA-[ai0$0FPE8,9h8g*mYT-!5>F#%H$I5-WdmH]BI$[B:rBeVfiI)
%5p<PS6bW^(32Ykr5SaXc\O'f$*]1,IgT=.tQ"6lW=L<jZJUb?!8V:W<qHkXD7?C+IE2f/3*"reS;;8c*&p5HW4:.O$%^<8cZ_,Yd
%,YXKW!\B%G>9lhG)=+1C%Dei3mH(XI;6V12*1VEdOZ[ViQ';<o(8_kgR1Uu*Um\]^'hL`J_6/ZA-]#rKb_0N!2B;fd"4"B53aW%d
%@LS\DFhO,H>e14G)eY'?OOcf.<0)VIB<o%4/ZM3@HdIf+S:1lIUoFbf?RZ%MZ32rJ=9X8O33MQ:[T(#h=@8mJ\g!2$pXVoY@'U8J
%.&7XQ.l"LASQ-Fh*u0a7=cOcB.TN/g!1h'c9asV:,2V"^n$!S-&o^6-e/k-2A.4V*g=C(QQS:f9b:[pcC,.d6"LsXt>YMRPPK5.O
%6Sp<f:Bd7LWMQ9$pougR+mZ+,O+RY7kl"qh_g8J#X)&CfTqBeq>WPeUEZ2;c7D?"`X%psmK)4bRcj*<;Q1T$%KacbEjWV>C/[Bnj
%A/m$-\kaZ6-^?H@Qo1tTY&Z(?QU>%:mMPT,+&Y-cL]Rq!E_+/;S3n.^_V?$.V/+D/5q<RI`!+b[?]L23#Eh@`/J3WS/NkR;U[^re
%+KU93E%Hmo;C#diFs,*P@j1+DN97@:=Zn3#E+Z>kGr$X$%=ISg8r#uV&uIsKdLeTA'j4qeSD8\%1Ss4)ABl&XO$qUD3aR#mI$6p$
%3IOVU2Nr+E_TX:>8Ci+r=E?l>6S&S`k"*E2&_4F_ZK2/rh)hP)Z;Jl'OfQjoRV>AfUcS^p.,H'5TL`o+ku\Vg@PR"H/67IN<SP.&
%Y$)@@+9JhOW-<-6`QS,sZf4i$aAH2kY5.6#j=d#RAk:4[L)pF&e_i>u2C:9se)A5VA:<#0oEWu-Ar(8^j(GnNe:Yd=(U;5t?qhMH
%NS0J#@pX[DfihlK!'H.5]>QTC%Ab3s]%%0hp8\D(C/0g-!>'CG28"H6%Z4Lj&4]%)j8im1B$s80!-(Z#*F]59mWIPu"f%-$g_>R-
%0q,IK/;@@ia9A:5:h-6/nFn-$p^>&CdA\iYQ.^gId=qb[9%CB8eKCS)-bLS&-u51pn5;VUFr2n`6'/s[YqSp.&nTuc"[@Ln1S.[G
%O!FU.054E']OaW6Eo$-WWT3^m:3$bh2J>\u#O^<WLpn(2J1b:pi^"jJYnYAW=]-LOSAuiXKC+gg`*8ZXKFU/L#_)D[JUbbn$#!ea
%9KY^BBo=OB.Y?KfQD8G;3$A]Lr^&Dt0sRMk?pkDa"lG;P"pJlX\md9I0EsEP)bGcjf/9RL&Tl)D>S5h"EWI?k/pi<`OmUDF>fG1Z
%MoD.B=e2hZ84,,"(LlNadqN(8Cm1)%d*!3P_osV[]u+TF<22!(V:L8,#b+70(ZgMnB.u`<#bV;/bZkCg9?n.Z@8;ZFOK$/u8VIFQ
%.9kM7#UYFl]%AQje\gW=A!RB=+sNC+:P5nci#iMkc#s$B([X+U\;.\'GG;dqF/OI8K/hl+pB!Pn6KF?KXhJ2(<Cf*pdG2Q3fAf9c
%hARaV>3FVFlOTIk%CMR/Kaq%3fq1D$'jJcp(1*dAO;Q6eT+iQ=G%!EN2:^XY0Q)$-qYk[]1oeob7>t-5VXMm6$',-E^>M,n4uZ@a
%(pn&VPg+m&&LPV_?`X@JkJipg?0n+SG!+te3nYb[P"brfY+k+4i]r<Y1I8JO$n!@-<KWVMJT%N`m/j2c3TMV-clZ8cdq:e#Re];@
%*6%A(Lb\TLqpUIaMLZSpkYBMJ8lE$:*gJ](WEl#Ska'T2WHi=e*!'ks=Ruc3`3/@_-nos*f6I7mAHVU14+sSV\:ZZh/kQPC*FLa!
%0jS!-rdFoh:mVfl8_Y$'ifC5KOfQs7%S8=1G>p2l=!!hVnTK5B'sfYi1r?-3m4"J!R*^7]KM?YeB<[X-@.*$7Y'Nj*iClFE_*$ZI
%kSJG":rX/%P-?9eOe]>bq=&QSP*iJ4O#c$c,_4CbTL4nf^1Vd4Jf,$GFR,'S=^sAK5U"-_+iG?SBIpi2:sUo.YUiVA3(K&n'$1:-
%6\GW/Yr`nNW1%];=kM<1#"1EA3M!?G_KfZ]4'ge@l:./6Q?Aikk\T`m%Ip[_?di9(-En],T,!>C)kObJpSp"_fPQ]e_]VJXKU4X2
%Hp[C%9+;rRFh+Jk6lm\l@;1?n=,MTWU/O]Uj'8/2DN,@&0P#Eb4b3J75<s+`?]nCl=?%g'XN*ubNU__E\%5^aK#E>pc!Vd+d"=d(
%kg^"Uk!2_6Ge0*u>MIteWdUol"G9[sq=RoO72VRts%/:&CZGqI,r&fT)E+K;]q<F@'sWfKLh<BBWQIZg%Y=[^9hM-cK7>DqD6OQT
%b"#ON657nCr]VtS"SWB<23^=mQ^Mm\2((/8(2Z459:2#CFZ.Cj=8=(3#bhl]+<T2XI*UThnOo=aOV$p$$+p_8qcR'B=PX_I!R?K&
%lr2rkUV7ju(POgr_1JQX?idI<k)+OS4kRsmABZD7lh'7P#KbID17K';qPn%s?R8&DL1Ms<ej,rK,CgG[5$--aKiKETBr]A2`h4@`
%49<Kq2i7.("PapCM$=4sd<ic.&2+^%P*GS)\KP)lN6<T!HP9%PD4cV2KT4sd\O4Y0l#P$Y/YQ!Q2KU?ASG6VGCb$8(pb.U1?kNdl
%&1e32`\OooR7klh^K/tp=p=PM`k.>jY.d)gX7`^>fa3+$U&u5b)<]3W(bPj8M6.V73Fm=FSIN+I2WHd#>;19;,(9ZOg67NcAgI+W
%e6[R.VR5ENgYF/eJ;>+Q_m[F`1l^8+UM1aL09_uR`T$SE;->%K<4rjL"Fr1fo=S13b"#%`Jrf#H/9/I@&Q+'eM.iUK9SZGB"joYM
%>sBZi\qJ`+?nPjmIc<no#p&GaL/5^hfn^O+iqa&66SYX+@<jiND_,K'.^U+&q2A6>UhZn=`[6/[/=Wb$=HtGqE&?tm)N0?p\1Ris
%Hu=9#W*HYaqeRlLWp]8JZG:tURYf+2.7M)FWT[oW@&VrHbVK\P;KF.%N)q`f\0/G\P>8!ke[^fqO-*FmV9rm3ESF;5?,9i=BuU*o
%1LMJrcHgX%_%;#^YP/^NPccXa#UDZ:'qT'#e;74=P$;6jf:[=V!VXtkF;&dRU52<nVep]\.*Et=jT0Z6XmB"F1tYFC^Zue_'4.UI
%GR6ut!9FtB`n#Y[W)ZXcK$i[2ROt@m<7,,j8dLTQg:eHOp?[!l#L!`8HM,J/]`[2#F;VLH`'88ML5)hAJP.2Fd(Y=J)(cJ[HUta:
%VGoUgQmuCqF^nIGCsRK`hCW*)bXh9=Lo.U;]#SRG,AXGNfIZbV#b,));KIGk@:2#7QqJ])<4C4oYdIZV]Q.]2Q?d0P#lJ@&k%GTR
%Jm*Q<%W%K)(7*?e4^gVb;UU\`7ER;<E=%Yu_]9nj8F9#`%+/!pWi;[=W2@(NZ`+"JWhp(q#Urc8Y0bJQLS1*_b59'P8lE4g!f`)$
%8qbii[6ta^W)j9*AWbQf=[__'18"q8"/t<%m3&]M;pIt+9Jm-4<&/#\LoA/N"nqfsA#DT\$,V>%LL>s=.Lt_RHSK9X#-#@r0Td?^
%K.8UZPZeAV,-8?:]pj5+O<nD(adg'Ic7"OULkq8DFFgM@,.M2fU:L)XJl#0G=!VtF;4O-oK9Abgc3Z\hh0\>?Ec[.(nIT\H;GB%m
%U;h/Xk&(r]-SbHa?C"*@o+>I,=@_"Pieh59FAq"H6:Y-W'ks<JkNKQ)[NlQ8&i.t<-u[cnp+oZX\U+7P59ssZ=ud-7_U5Fn7s%1(
%LLcBNPKkA-@bhBWkFKR$Eld@PCX`KXV[1gEX!.t@Jo23BE.&TjN:n+BE,u2\FPd*c,]o8*MhZZG,0Vq:/("bBOsks0[^(#%I5H2O
%M$Xq6gL@t$:]H%'DC%@5J]7TQiZe8F#@O09n$JWI@PnHAddkgV@qQ*njO'duA0D*e!Wsmb_j:C72o[0On2R?mMMUaY0b4"a)tPOF
%Ol3ZN75j0nej5ET5jJE^B#nF<h,`Lo=&:))FW'W%V>PlPp2V+V#bPadn>1-(@XK7PC7_X:M3"aLG'RIs\V7eC+5tK.;hkGh\F[^h
%2&JfDIoAR6.OK&FWM,O_>&_Y8)XIqqP^])=5"mk[?#fM(QiVMB]5\AEcBp%I>maEjRIdG`gL:e_E?eG<h+Q>8%#SFsMDG3l:_Lru
%m`<k/?e0N/q?@[6e/9nEjmBq2./"]+Z:QM$p,FkA=Be3pX(H=nC)ePRXJa0:GP_2OAX'L][qOe+'DctJK+_T,8aM;A!R.ISM1_?h
%'r,Jm-"('3L>5.dCS:>GBOi/2&Qs4?(1a>@d1IjiXl.RYA+("u!E6(@L+/K9'mdTg!C7Z"Z&6+m*1?/Ea.VSQ0om$8SVPXb`7]Qt
%`DF'\7S^04KePJo<Ua/lDf#NPUAE^a/$+$gbr.0TEg>6XI@mPe^me3KYNEgn![m1`;FO[lQ!bf-fb$=J1N@.!(YX0<La;Ngkohc8
%&N87nM:*bQjT>U0KVLaT=GVdd9\fo4n2ReJ,-@nQklnsr(0.?.eXrYkljY!)rb;[`!VtbW?&(mrRpf^$YaT(/=-sUk)F>E>!hT"b
%gI^K>Y\"R!+Ie%`8c6"2e(_roUd@`^N+n_;0:EV99,9B..#Tc7!KEl;Ab1ImeZD),MRW@M!r`>q5[_iX/Y"aU<M!`cn'u/gBNoF;
%W\Q'@Z@UnU=pK=p&`t'&RC^)!#sl3t9_]A1Q3f]e(r`jS&$I_EqAp)J1':HjWAN<bP*tS/_(FDmGa0O#F6+*_P9L!477Z3codlA4
%C%")j)lr1@E\tU8\W3aBfoQWn#UA)a0kL.A`+qb/c$X+T8^P%p!k9j'mU2B6]OrpO\*+9q_(=G.iM.[h!!h%*,/eTRGZA$3"s"g)
%;.GOkY6,:$-q:Q[$LRprhop3c]&kKI:G!DG8B5:B(Z5?*_CA'_ol^!#W,:N2o^S+'&ED&\X&Y^PON%ATL/a@_S7BHV:\-%)804Ec
%&-I6W^8rr2>;g-5$.X4@kS)73\sRLLluAG6YC1;Zcd8W[>R(Ys2%(0WS3%N6.V4YVWYahD\88hk2;r\or.%qmH>(]lVkJaJGd'Jl
%*KVF=9T$:Z$jKn_6^`H_`Uf)I8)=dcZI[N+a?a%LAoCZ>!O+MV@=-Nl`<T[M.p6FG]e"?_!3\nZ`?FA:OhuBT9_=:M`c7%TNg`CR
%e,elLb"1FM<N&16N>>ACW$Te\)M"4]9G9%&J,sg(=HP\On8%5QX<n@$;]X;LA&<H5)6^toj@tB:n.NGN%^AS]6_3Yl&>uliVat%<
%j(TuNc$BG^q(7f_,))uGnS!M#/Np_VVh-]rp;@4O>76?f]*NLd^2EZAf\qbO9F)dc1bd2;f9s`-/b/'f1'`9FQ7h2:^o((_U<mV!
%;fHDqU&0d>!CQ['-IX>q6rV9j+UTbe;<nT#jM4UZRA;pg;*Bf><Poc4E#:9tF2_CAH'aAJ8-fSi+Q=#A_3I>.L4e'c'aLB^P"MM-
%D4%+BaL5jVgG:3PBEo9+4-Gk_,%=5bFD?L\rfhC5/gb9qGRFsY7jfmJo69.@RfQjb$XjI&G,SOTK=FhsAB.)ac.Jq[X^GOqoOU0B
%'1GT^Tq`&-+-9BVBQ8!t\.\NYkXI!?K_`Mq+PSS:dANV.kV8d95.,K!$D>5!6ELg(+WQS<mFHhEcqj"T70OIX]8W\fgaTGb:IOYj
%Q:AbNDUi^i'AOIQ9J]$5$kToK^PnRo>7R'VK[d#2b)pPY+^/'l2_gQ-7;>S!e[UT>f=-Qule(ELqTN]BFRTEkp`q%C@n(T',)5tF
%ae9F$2q#pPgMXZh28]1"QD#L&4i1@G5B>>ns3>,1OM"`6Z7dC7+dp!qkigb_&NjZVC&rV1D]4PW@+jUH49cf:>7t#5d>(PtF4:H+
%-*blHLDOs-&[Taodk+gp3tP3t<_j,s\=+:8@1m:2NUJNH9I#,CS&s3UK5',j?q_ag>"+'"["l0dMptBn`+6S5F`;aF=CIbD7?p7I
%XO(u(MR`A*)(a."5ltj<'CJr<Z?X.hjmHn=U/)&CLi(g*DsO(X5^4I>"J\itm<upE/V6>_-tKbA*uZIMeeR#H-$*n)7<Z0C7G?!!
%h]YBu]O\97b(=d-[BinB$]4N#,3bT;THe-TLa5%GLA];FCnjAgW)B`c2P_&>(:39mO.q^4"Thk5A?M&Y"(a:TIX*a=gp.AI:="^K
%;VP(m,X<G==fpLB+6oi2%VFcaqk^^]6jJ;sa&!%rF)tsHR,o8V98$NI`PKZ<h5H"$YXFEk!k>%^E'k/:c!n>c9cP$;<rhAdmAOP/
%EGDap4,rm^*fA(n#r)%)VYFTt:<@9JZXp`0eT+hRh%HacCcmFU"/?3&Q"(A5e<m_Rc5Pd_bb@7r(iU)+:0!M>_req15kHM`-7P,B
%C0^8<2&^[t)lSB</Q%EocOG%c2EqOg@Y-[!kuPr,JF&Q!n6LYQh\iq_2(j(_%p*>e=i[oa,Ts[T(L"S,F?+7b&Y!Q`M>34pcIWpG
%2t`G.ITH>pRbM41+)LtFgX'p;@DJXU)]2Z*gI'.)/]Ygu5:49uX[1LEO/\NY+jXcNR]UUYaY>t@8?eC1bS\YL@_%f<L%nL-;2uEU
%?,S4P-%KA9'K;Im<P_@AA0Wt7)6'X[duQeUQCQ$NgI^kTKOT/m1!W*`a(dK$5n*hhVdW)(H^3l[K""Ea%Vct'"B/^7=)Vf^ai(qd
%3M?a7a>?=bJmh2rZ'>PJbQp%K[I4dVj,K\]e+rL&r2Ch^gpf\)k1\&5;OL>n`*OBK1RV(g2L&]>`4.-n\J6Q0S=+473*e73W0CVh
%mR7B,oVP5YKW\Mp6.*G,(L1'aotY[H?1S@cG^JtlWL_RrnLCZ[#)K4o!YsoA6qeV^nHqM%W'Esf2:;gI+n_iRm!X$=UFUjME2C`U
%9+Mg96$-de922F(#i]D>`HOo[Z[b$!1/#(YAQ@g$gAm3;5haC4ejr`6Q]gfX(aiJ4E_7&8Z4_JWWDpjt-!IckcaLB`q28jel-0t]
%"/c">%cX%B:q$0u\XSD4gp:_@ORHmZ$_Fi#^)?d;7<pIC[^t0#3l"=X\H7p\,k+u`W"Nt$Vhgq+S#.WI<;3GZAG.J.r.g/\$%6\?
%o^"k38L,eB@-,=ieQnQa!^oNiFqJ["rL=VS^a'2@!CG[Q/u=.L"o"U@09/>:K!h*UMS^%3HXt[e-;k?h*.%mPE]"S)YD,)17&b@H
%r3N-Y:Y2hJZ=F4Q>lfKO/^h&.9/.!Yj\\_*Y)<m`3iL--<F?OcjoS3hJ0#Lf>ei(%CeO.=Rk:J*4caA:(`5U2.!BUFWfPM8Ks-=#
%@U]E&JX&H.Pb#O1CHXomWtMaT74'-B2ac2n!@IjO^i;[55T5_l`q`gSpeFs</)HrGMG2X(.i'hpke<\sN]0?G2X">jME)FU'94IS
%&3NM?ZW%#-<6m+WaE6a6C[VOK!X($b^]X6Wk1'$'n)A,kWBnsL&oO@T8m6BQ1$SHLTRs@k^07=<(YfZM3ae4SP]j$J1UUefa3JR=
%$:(-BXCWRK%.lHFbNK[S!;,trGqksqFE*?^gH2(tKhg2spfT/`d":+Z4mMAfW7:[pUU2AeYa[4u\@u!?o("L!X/r7t*i63"W5/]I
%O(5#%Qjufu=H//aGi&ci9;<e(&eW:r_lno6Ws43&>4Gj#'%+;K6+f*B<:_f[=mGNg4hq)+FW)f%oEm"_m\YdSX=?l1,"GUg07mC#
%l`q6*;JLYD^J>'O\B#ZT?=i?(Z95q$,Yb5+!KC*<@&0H1Z:-HX`Mimd>9kC&@lAY'+%+':*cIGR34'8rFX$[8I[,5+I%g`sTCF4=
%@`>))),r?P3JV!`:fnBGN$@>3(479V`I$s>>McXl4O7utqJJ/ARAP=Nm4AS&;JLcSU)*X>"'hk^Uq+4XU9o`8QRl_pHGK>NW0)f0
%&\YTdYRRJfcE!P$eR`IT;I9qT-S8auTBZFHA[l7Z$Fp)K098X?q2,afc.H3#QqT[q7TYAfKPKWN8^9#/.?<GB'RGYHgG?@"l[BR6
%_5V:J0JW_?=j@kh[mS"i*Gi]PJT<Yk8Jd[&Yn/AB&Vu;u)()43ccn.TZ+q6g0[U5@GA8hVLUZ2N0.sh6T]o91(^2cE8Kjd4SPpj7
%46jY]>C^Li'tf3%h'h7PH9rOa'rO?VbLiM_SCRRQaE([&f+`h(&rbGPZ7V4P!sWSL`sAdI+4Hr_,R^=Vgl/sPC>n`&.n5'\)m.aa
%+f(3u'.]A`_B0F;0O*Hi%p&e?'-Is"XBpa4#*<e63DX@h3R)eW'"h*',Pj0l>68n:J7=ON,UGX;:lg^KU(SuV!+q.9j;r,mXPBV>
%oQ$>TFt<'Ub<('t[fS#Mbbc,BSa>DHD_W#d@s/X\e;M_2Ak\'4p?^e+-..M@?."jG$4:K,&Od9ZVD<ABdBURT4n(<LRbkG#M3>1M
%^"a2:9frR#hr]6GVY&H\((?Z%@Tdf7[^cfkbk((k'b8tJ&L^Z\%9Xh9`4hBs(.*BkF^h$,27dAQq[!5+lWY9n_IdD9$ujq^]]<QV
%?!ipL:t0(gWckgT<+Rs+Yk[jJf!n[T?W[r/!d-M9\NcO=f&3ee\tt$-n"puSJ92b_pol)EYi[1h@GXZRR-)r26?ENG-$2Wo<6`OL
%-#<0<d<8hbe.En:T/)E8fM=76.[CT>f^_YEZ&50!g<c"J#0eg!4]CB;EaI2Y\ZO"#fcjeFMOdSF*A!psGV9oQe9!qo>1<"V]<9,g
%Z37`GETdo/q8J%mM\XH(.ONQS(,s<8I[SFQ>2(]g:IR@D#g$H"bUi.DJP]$.<)LoL/ob)*R"?8tBbmtGjV]%Bc'j/T>W)e?I)q4G
%\KN#bAul&.D$UcIko/OeQY%\VJ;Sk"NEaFlCR?"W8Wo$>)hUW>:_-!Z8#eQn3N7XEG,K(.EE][9q/aQ)R1UNnY":``i9&Zh*tGu]
%j>`]P0hKcFW!51a%1-LdEF%XCoi>.49=I8/!Qdkb,7(KA:\fUU=W6mcg90QKlRJ;M$P$tb=bFXYiI+K-,G^X-0CD7_Jn0%RG1>eD
%834],'jk=L%Se'JJ0V=5IC0(17O"i=O5Iel>nKf.NbY##dAig,/%%rT`>GgD_4j/nc>^gQAe!=0(/2a@s!i=k'4=j'.\"<i&/E'4
%4kM[OXP8gQZ%fB_k2FpLMC@Gq`"Z>8iPfe/-Ct?#7Q'O%pDlXP86(`f65O?*F18Hr0e?0CK<C=Qg$4!,k_?fIahjSt&4e*TEZ^[#
%dOL?"kWW4",_rgY.3.UPQEXti6^f_]Y!Bp`<o/b/GbPob(AL!9C0DC4[:VgSWD<Q#MY'.g@q)NIBu:!Nn\?KEF`?]GQj``b\g8P"
%h6&SBPU#c*8^7P1`qARGDAC2_Bb/$:PhRp?&U(Qu3V="P%7rhnJoCk"ZU[3InCS2rJ]=3A@d$[o-(<-6$P:cXa.]H\;sBu,2.8(7
%ftaA(R!%]^<Pd0>O>;EZ;.uF47XE#'cre[&/47.n1D7>m)OfGl.:FDL#$?&,jK>^so,c.ikmDNaS?+bM?gFt32B!rR'Sn&Dl(K8q
%JbF[p92"2>]Hc.Z=_t9E8WGY!F=NF]ZXOLP;5'lO!)AFdj]cub,=U5B'aimolla]Le]I%!oM.P/YYb,G.'Z>/1e+f.:r5`-CR",4
%6dPl?`+1Ig`E0*J>0s>4SFBb6Qc.ILAYl,'N&;Q5m"J1P3@$q[#P:sXoHrEF7V^[4D%i3<98MXW79!:cn$<_3k=F^FT9?ZClUhbo
%KinC9@_T(VKKH/8.&#uR_fsDNcZ2JYfsRcg&-V&]o5Gq1S0db64j;'MH?b75Zli$r=EVd(Q*dOF`G-e7et+=T*c_JJ1$o=<RG+FE
%lu%Cp33.H>%8!-)j.Hp(L:OAff^4Qj`TpPs\])Xd>6ntoZmDF^L0R^ZVW6+qa=WYgAQmPVX.4l1Z[piA@*=X4n4Up$2+],6PQ3#T
%8tLB)M!*nOdZD9sB+jBPg/J8-'@b?jX$1!+%_8,R6:k$+R8[oJ'8D>8;P0\9,Xpa-`'C5VA1u<LQBY&YM,Njm_bY=ka^[>(I+tpR
%Wth^9`'VV>GL0ZHdd48>Ge:Y("[FrPqm@U-TC\At,3T[P"guoh;8MLIeNoH0`XGD8F!?O2*3Zp^abjIcX$T0D8h?,,8RhApZ)/)"
%Jqgi!,a+nqck&?,>?oXY%39Ee8tO#1mhU[ZH@oDiE&l2?a\&uDTL7,,M6"qAkT/AqRRPS/?]'2Gp`C.O2#:dn<?GBW$hAnSTt.8r
%O^k<rDjmU?5OO?UU;cXOcOMeBPZD(pWQ3Ue_^WiP@ua(!9/Zh=>Xp1T+5_T?`!S#k3arr7_p!jP*YGSEqG]HEBWU=8kG'>24m7#D
%/jEpVLu_"Erm9@W$%Z1-Ei;:^B&l1^+VZM,P%:"J<9PZHlp:8icBG&M>7Yr#e5&UIXXTSgl'+@jO%D+>+K^sJ@`8J(dLnT]HKki<
%Jq@$h1I;``'%4UjKqFH!ORUFkPEerf9>bK:"sKgH>W-h?DOImY45$_@7?A?AY8.`t@WOtRC-1tc'BE0[.(%6B)snf)=b,`@iO[aj
%:t!\q'L)f^Z6"lX'@#DXU?4r,"d(56;'ATXZZVpkgs!A%W!-$ZA.gKqbR6kie)I?9A1:(Qbc1b<@jafLbbR@5Q;#JnH)lo%-']NR
%,G"<dhJ2CPfGMW\K'_WPdBq8/=KJ?X5?=J>g*k%iiLnftdVO'MH=&%qBj:dWV"+>d(mkam2ctp8ZrU'C4Xg(2)eTi(d#;nq8W>AH
%5VLc"lO1lKcS=X@5FL!%Jf91X7$gq?R=#(m-<hhZifIUWNB#T&)!%'F1iT,o1er=2&Su+G?jOAMkSf3"7"'6:35FqoVnh>f+<RrR
%\0.'`(Hib!JG]%cO`l`3h5]-J]<+*oPGu-U(*N-JJ;!7)HNT^9`PpY"$d>K^',._3Kg`BOAH`fIMq(FA#kKF<We"IhR]PO[`Tk):
%>Ijf)+J?[[k#&A0\U`5ADota%NZf6KTXQ,W\1-)dCV&RYfa&9<Mi)?8Mb_/^-l7m>'*MF):e@W<5ee2j#R>f,?ufDiPlZqJ/W*Ek
%=PNfT]:c?XD$2;iaXnM69tZVqeXqBO7RS]"`O4_87YUB1P7GLW%'.'R=p\2cc-$!n(!ncqPXf"%4mlDG2EGRfcE__S'og[WMgV_R
%nLG;2cO'B'XIkLGQU];%1KnSlZJ!02r$n?G$eD7d=CtFQCl%jaTM8"$ZiI:XIfqMdVa[%G(j.7He>h*B-?3"#Ro=!`Q@s2dV=CMT
%NqcTkYg$qWj@/GZJJK!7)),(*L,-hb0B!BD[?kJblckM2-\+0)`+MnW4eiW:+BTP^@W>go89[sPM9@'n"aR;Ee0HNk\t\11Xaq5l
%,FZJU\$_)&_2+ATo0qmX2mX*c<FhfFG,910_lJPor"u>c[#fmfSM\A[kWCP/HXjdc>"B5DR\!u;1-c^:edCqHQ;X#P8])8N,]Qk6
%QPZA>Ni=QJb:L9)--eql_SPdVq!?Q?jKP=%\?d_AB#!As\stf!ppsOO0@HDuHW.0WMQkH]Y>\iDfC/736VbkU8q;!FfVL`s`qE+N
%8m4lP=JfNu!fO\6QR:1a"fs[&%'Sm/7*Km)F#,FR([P>)mE@euG>N[48/6(9SadYSn^4AX.Y"rb#pZ.\m0*1Ap'KIXHNo<I>*7VA
%R=h,O'p>)3;<ROgRh0`&WJ?A*jf'';*I#L]L[Dla\@Sob=ng4cd#(=@S=k"(#mXRE8(Bb7kFkSPd;5crZ&Tu"D:0iMc%q2ngk*$%
%,X+`^!I>0]0nkQ"+pqgYORs6[S0C0p>HK/?e$TR^p1Me"XC-6Uoe&tYTrElsMNofB^AJ=$R1P[k^f5VImCP4Z]Ni:S",ro]Bnl;_
%WI=&[$<D&'H4]QRPYQ^I+)ft[p=lXObDBm.dc^if!A)EZ?$b-M%2klSSqMMqe%)3@oY'</WoGh%_b3X4We7sYW2laGjX!=%/U7aB
%\Q=$;K\/DeJqmqZEW?782,')MCP$4i@:j\34-\@($k:Vr_/S5S_kg`C$Op-U%%;i>X<YAZpD<VUqb@W"+<%:\it?I0fl^_k8lTuR
%\a09"RMoiA?$Lq1PY=q:2&3[fUcA%ZcNj>*4lT8"E,.lbl0PDT1+\-fB6>V?CQWDh.^1mSakDk';8P=.A;t3LA]!EV6p[8.7eCDL
%-TJcg<jO>!B47f'EuOmh=YeK*f@;,1bm`SXS\ob=HmT)K*eOgW`f+5@*cNK3!aK#d%[fF!760tI*V+rppb(@\acmbF82N]Bk)-hF
%HV9R^=1jrbI9m=VqItD@!TF/H0GAiFjdo:\B7NOSLlg]E8,SU8$pSh*CVC3Y\=GRh,JTVhjL2(ZK^MP>lj1_3CArFd!e(I2Cu4Z;
%`^MQpW*=tDTPnQ'>dZUp&"&0^#Z#=H-Uq2>9@WnXDIqBUZgmgYSOQ$p&Y"^5_f6T6`H8.:9(Y55WorOL1Ju/-l&HKT;=)/,j(l6e
%f[8?5:,e*6HNU>/Q<4B1V2Dcq]>@_cLkum6DQ,c.\4KiN5-5(7"iVN'K-%/A1GW>B0c>2?1tT8JDQ,;,4_,u<_"+6k+U?>\_WC<P
%.l\D6m@L$WkAX\6IN:(q!Y]YX#cSIpUb<RO49j'flR^Nn%H%cQ0QcS`Dg6g8,e?,V@7bmre-81Yd;`'cQ>UD0<N?^i5/*PW[\R!/
%YsD,<$N"u9Q0D$52Cq)A$:tQA1VkLlFuRHm$@"`9;R]LpBdTY2It/XEP+rYD<LOc*00ogaAk,8mG)K*9UaDbT.:sX0?G1NZ][9S2
%6Ni%*-"E]8@nV_KV#pP?Eabq[=-m"lWp0iJX9%0/2CkK(9W;g;<jMSqZNg`q(./s5X>p<@c2et37WL8mD>7_j";EKobBcXn>F5md
%,/\pP%*:%<Y6bkUCBbu;7@ke$N+bI-+2QAC7fY:F@Eiq^pfPgiKlonen4`JIFZ1LB67<&6+j6Y*]SFe;-JB:ke(b$f.U;*Fl\J*3
%=Tam-CN+d1Le=,)79q.PRp&EB)L6ICXmZ\;SooON*YJ%G<<sHR0HpQhZ+g$T(1f:;[-4K@?Nq(p;;CD07,Sdlc*]U5o.n"ph(pY*
%K!L-P8:tH2B!RIOj=h@;`DfNj9'?b.E=HLE!O*G7$lXW=!r8t<,3+DH34[P!a[?O*lNT%D/S]#hS;k_D[$7&qUc!g-"Y<g%"gN_k
%h)Vmnl.C?9&(<g!h]"<uBeR*;i?a,KTV6K2]ZgdA$[UNcEo?Uj-4qRM-"CO3+"-nrEo'1]6)ZIhHGK+^fp"bT*!/4QjTu^"lJZS*
%,/SiSC;d'!!o:Wu@S?3W,6)Y_OPu[C.7<qMY(j9a%Y$!*ZC<l&99bH&L_W5)bC8HN3^DS7Dm0&VS79UXP'SMdDFttln-"PR0L'.u
%7YdN!=*ucq9e!.aE)`9:8?CSXne]q7Uc7O+N,'Z1ON3XuWfq&I-U#_A23MSiWseH_@!=F3O0._[8S<?<^6$1ZF9!F\%gn.N,GB]W
%lrXKA,)%Bo:ElMb"1TAFD`J)>m+@Rbdje0Ng$$i1=M_`H@I9Eb@LCpCLp`(Xf%J5`W*3Vu_c1`SJ4gR+/R[#TQs'#cT\I9(*Up?L
%GmsstIF/lX%Y@ugQ4cQ,;]$MDV$^$4g#SNN7$Ti3A=%u;Q7^Y8M4grg$mH>ERj+F2E<6Y^bN*UX^I[iu"]B,2(2jWWG`>*ck4s/>
%fUK^p&CIIXSeeY)YgTK*=KeDSaR3Z.!46AG`nZ^Xbk)X0AqgMRHqD2\+`'%-%mde"#o@,B,MTb3fF50XRN(W_`#0EteS[<SMDR2^
%(LT=)X(L3cNL]OrRUmVfB]a&0M41C_KHI,5@SO^!Pc1Elg/8fLkZNIFgF^ml'Z(e*K.qa2\5:Q`>%r8Z.i*u=*__Tn`H3P^^)Q:1
%LSYlg0O[:KXO4,.-$6J$,H?j3U2],Bi6l9#P,$aj$\[Wk[%P,H@V1:m5YE^b7."g$+KIQHoPcT/q>J79!d+EI4kps.<r6M'TEcO:
%'D[e,#I*:Gn3-Z+/Apf#=3\O21]^8!;*U<bCX(3QRNofPf2>a<8tBD>[>D05Sif#Oa^K'8NRcUm7&!n74VsWQBu$Lt,Y(gi6!,8#
%mC_F"3seF>%"62K3&su(HW;t(K^Q8OC9^I/a<_;'a%5J!Eb#jU0'U2r%!KTt9a7\%V*pOZJYUho\=!f!V[6]e]gB%M;DTh8T+rF%
%`:bQ)%h;;uF)Dh#]di<9TjS\;daY0E2X:JrN.CV0n[aAa5lu`D:O<?6ejR#[ea2!Xf"@mcS"IVZ=I#'35@/q*bm5_qjK35qT+ubO
%U@%R::d9fG)O)9;RSpaEV>J2i_k(IS9+!ncGSkM/^gZdS9L][-T=Y5?W;"/SdGmZ2%^!s8&_Q9tEYr;1.RX:hIm<u7E%hGo2^nm\
%ATQ(0EC"62;A>Xoot%,YLcN#sC/_Hj:)E+aBoK8s&,QR?e=c>9?6#tV'!-)/(rLTUe_mK2(0p[39``HFTMf89bR`d](q9RkJd<eb
%[pdN/&jOFqRY7_,S/d;gi;jHTl_^JS#Ca\"#Du@NY\[:"`[Q1+PAc6\SuMUW9JCf!OR$c28BZ]Gb77^]IJ;`NYTC/P)1<!g[E0%c
%4j]1UfHEYg$@f5a-TUHY<5WjMJO;&`c?XR@liMI"SWRpg12?;Q@>fqFU*+6!JTVM\NnaXbS;@j'9$M<fU,Ui)iR8?Db>?XJlE!jI
%_.R.X'k5D6dl\+e?;r.-':tp'V"m]>,?5jRp^:Q5W7IBiG,Gk$A$%7"1Ma@k9uE*j]WbmQ6iF[JbB"Q?<Cr0ibH`?G-*3r/'ZDBE
%.`BL2%_'<>P>me-YK_);>cYpg*Ce@7&r;F"V+^sJ'66$t'+rHb#il3K)St0IX"an8q2(i^9IcI.a=;#Bd2\fE0OHcRhca+'h=?(s
%'KBeJ6"_fSSMDk;WNsN^=:^4%?K,t[')7.V`m.]qMN_,pH5MEB3=Pf`/UaEuQjj.m)4a-'<#LSg,3lCT,[rq+o%f<PnR8C#_l5;U
%7aJ"nNp(j;)CB,cBW!NYrHs%'kq(D#dA;:-`*%o<OGfl>eGCJSBU&Pc5GhYN&2lQNLA86ETEFP^X[$!f5uc=]Ul5p1VCn/C9rfLb
%1A+&K70N2?.`=bMFh9jBZcl>qVGWK6AY[Eae&>_E(e@UDXK.P95YsLp_Zdl:kN>f^=TIFLIIMJ!MLsLhoFN2P8cirlr(KGYAp>a<
%HY'KGNbs2u*fuI"b\U=C3JGTE'!P7ti*,hj+:'8"jD/5"TPDj'D$C=V^mtjE^>:XEBFb]2PVb9MH<'M;bnR2jn#XF<>KKe(3:"u#
%$Wp)"\;;dqJ>o9q/rW!=_CSn25LR=6:,P["TQ2Wu,Dk2@.)*CrNO:fHF%F^<4&EmRaW!5"4+`_JN"mJ=$f&7cQJNL28^E?.kCBo+
%&DWXbAL`BH(?WP]4to@!Q=qOk2_1(*PgSCSf_2$CK:sk0^iTmoX.Y7-&1p`/`L%6W.k0ldIpB#@1enY4H!1EZ+`9!:?oQ/FlX?)^
%Hsgd2bHS=_P8QDDq`4!W;=kjDLC>VLA\bmrI^k3^i`p!:5+6><He1:d@4C\VJIfRH]I$K-?Tg0QH2K)([)6>!RN9.\&ks"`EeY8F
%_Eor7jS`uY_A",DnaUG(0]pbL7@0EO*`2<^"g?rVqO*u6$7ksKHqiMiM9"k)qH;E!$)/]CC\]g=$&S3[ctmA+h6:%6AVE=pc[(2Y
%4P13Nk6[T#I24GJlq7Qd=GO4B(_-kOG)-L:e+((eD5k^q2W[TYCVZo\OBaThQ"EI,(,#h[ncIF0bt?$;oKT7C/>"9sXSSb2<@n4R
%C&<RBTJ:8hZ4]IWHf,Si3$Jn-O::-c.`FS*L:K'M'gPk<"/T6@]g5"PH).kmfGWt(2qT0GM(mfkOouf0>,^$[/\324J&e*'C`+7V
%Z>Y`J=V_/0kO89kVXMNm!Bce-KYr\q)Jo0h_kS,8*UPA6#a_27`cBZ.^0*7j)Zl\<3%cJ0GaG"1Z0eDCed:BiSS]WpS.)C5E_81P
%<8\\q9ZX.Ne"3N'ba^U($N`mECg5e*DlqATJ^)'(K5R*?Z[lZj^+Ne@1h-b$%+$lRr2A83UuGRSYr`?+`e8#]2cZ@pf>/c7C_.-[
%@]^Mt5u$bZapo)e&n>)pFr':M<ih_C3jH_Eh-\,5-mkgE24BI(*$f-JCX!_86IMl;_.$I6:L_W<?K[ZJ")0qA]8u8G.[Kp/I_28-
%AlpdgW,:#Sh<)p6U8nSTq=*V?ls:miNH2FA>e!6mE[S2hl,,2',i;=X]\h'*ZG/),L\9pGBS"5'>1KYXN"7*:=AVj23cMXg=872=
%g9$q&DB9e?SW*@BSH565Hdb*or7No@Hc)d,@SScTOVoiaGT`;N_6O"ULII3'Kp&G!`]F[#h-f^u[g0,UJi2dIWBnc;_9pD-R;pFo
%[l)Dm9rE!S.^d5!Vd\.2"/HPE$!*`\U+FdrA!k<*n37i^ZXtIa'u`dGam(>fR#72,mLfM=r,==0bLU@#AiaX:2n,o/"UHEnl2W%D
%5L!*E"Wi=:7>H=LLQ\;TK`hm9Lr*;XK6Th<O;MReTQtT=ldMl&-:Hme/C",RXNPgcDd,H.Z:K(M9Zn5;"X"M1d:JCKmq6b]C7G_9
%aI&Q2=!iQUIW0I;_GGsM)_[n^QY$7KJ-b+Sn4F,.FP$.+"1P.`EAUuh@kCoUeJS#<&E_F6A`Beb(dil!=(SOE[!.7f),#Yeb#QMf
%0mUQt3!\6GnSYOL0M!ogI7LGiLSi`D8502,>F?)>pIDl.J,t-`Lf)M3UgTDo98t*402Z_Ac=j.GNua2.!%QKGbgeJ<5C2gGYEkm+
%#F?B8^@.9GH*:n"M02rYc7k/Whs5Q:e/P!2Q\_2*cngX6R[E@Wq-";iQ4RS*W@8Rga=I.[/0pf#2'2BS4ZJ$R\;!t.Wl5]\B&QER
%FX_)_SL#cN#ZFtjrt"a3qbIM]5miFOYG7lr@EhN=iKTji[uA""'1Y?ZHU7"Ta,b=,:S&:scWXnr-AN.`N0#2,H:(3g$DpZHa#P$M
%b:?ef!,T\f\eT_!!hr:s%$n9LCf$K]"CJS"DX`)c%,#5+ZK@QqFgMF7'dsb$G6"4eOI)-g!.5o`6\XrC2:%XBl*Ml`;M9iD._k7\
%7#P(jD?<$!OK8Lu'Z^/,kbqc!c.U..5blYg1.4N!YiH.f"r\):e_*YpNa7I0n4o-se/9I)k)M4EP,nbi"4BRG#mkli8OG7eW1HRe
%"\ZQSHB>([LnZmn6q\)J$78J(F\ejD)#]7sXSf7%]b(]ROf[+o&OQT.@DU>P;!/]hhUYf>_N_0j=E,/0:2W4>PF9t2`Pc.N@mR;s
%58+eh5AmN5n\Tm1-Zflj>]"IW?RH[5$VeL'<LB>XUD7'dH-S`E,$#'5n-R3sBb6XulTq5"p^r<AlREF@+T?j6:0,14[p8uuGa=US
%[L9!*=5Y[#jP"`2'n)7%.U.Z#_H#@#"nI3_3t+k/T8&rG]JU&49uLQl^jL$T!apKW+Jn@`N@keH.3JbKLts,jXLZnMe;>DXWB&s*
%ZSK^]R&NJ\b-Lsg@_`t1+?dYRJI8-qaO"B!Hr(0#kU3+=!V75J\?I2rKp7C"oF+3":lO4rgt-(Z["ZS$`T/LELoD5nSm*tgRhuqL
%/S'[92cs&`Uua;DToP`h5u@jf(3V8h_Sg8Eq)gtpBkWCJOHf&<;-hF'1Nb-:De_aP>DmYi3=)k\CQ]#:'s/WRKTt\[Tqs%cLZma:
%9IdmCrg7DS32NMD7SYm2SK^dZNc$:,\=bWHLQ$"$QP]5ZfscXOej7Sl?g:f2!N[ZC6Eedme%@?)'"D@<cSf;pJItZaOC\VNS)%"Y
%ma5_Z<Ys8%<.&!6/m1=OU8NekHTiqZWPAYt8r:^AlLO;/K:]#B2\l3%$GlgU2*i9cNEW+"LP8*gH8TD#fh^IgXIM-8!3r_Z_UDMY
%ipKAG-M#uB.5-[A@fOu_`p[/`O'+j@Lfj$I9jCA+%'L;<@^t$oIdOD\qFbc<jR<A)<Or$&Y+gfI^3C(_:.3mY@%\b=4.[t^7CEaS
%iaBM>YN,28C%NW-Af,=FA#Ur_-qI$6&B(OQe6iafKQ:mN$-GfT*;Ug$G<RGoZ4[NrIqJJ&@)u8A%)D5,="!Z$)+aD9,3_Fu1Dq`G
%k><D\4!l26M^c<W/`^Z63X?2m]K`]'bF"QqOjf^BT5;k[?>P-Q:UriDi0<UN5LRk-5#8gh:,4U#))rg+!:s1.7#6mjPm'We`m;OH
%49M92TMP[t`!$DJ>0>iU!N]A.)?`1/ff:t`#iq7P'>6Y^iE,%6Oa_-CZ-'UN)B+2`:@na!\nO.[+:e?8=^@5qBHf?ur`bfZag?gL
%=:MgqlE#c$*q"tq^%P4I:g-^q@YPJIT145^,;>;EFcL]c@F'o''9bls'<:N.0b+Z,'l5e',47;q@ki;[b3]e$HQl'V-_f]4/u/7^
%X#tQ4@lB"2M,bI,U$$*@Em,:Y'N]Cb8@FNc=iot%&3Y0$fD$s;3B*ngR`A)i#hr4?D4$W^]YT1ioZgXEY+&R,Op2jP0apSu<U>6\
%UKQo.:L%fFDK\[4VoCD*E9.;lJ=MlGE<$?_)k.#q.ce6cd)97_<h1j$EW!oD/.js3>\e>g[N&EVXJ><ODMOr0<@)>dgp+7?=:?k!
%9"aYFcU9:ZYcY0d%PGq725H'm\%8u%*la=rQlt'QE_52\[IaVHUTRq/^s$JHN0GlfpVC(2j7SL@e.G9M9f%`+P$G*o0B3t!i_<9'
%e[R$T1Ff\OSAVVZ/'?+(+X*Sq"EPq3i?rhqSfc4S3i'a0,h:Oe5ULH`e-<ku@q<12pC1"F#*uU5+,/Z=iLqZo76ir5@6hX,!s:Bo
%b9A^%)@&W8K&-n1P4C<$c);_D=\VI]qoJn<m$lj@GdP&M=@j(C!Z4icBSnC5?33thRRYf2goXp^OXD=>=%$8_ZJ'-GpSKi]m3T;M
%/f80%?\/d"<*EZaLfZFL2I+*]*pdP<`1='t'=a@;2r=]_.O?Zfa0^@u8uiU<0q-=ukY2SJ?,mRcbtD7+kRr!5R7tdB#u>/fCT?U)
%AOi)M_51L%dd]AJblhKeejLjZ6]?4i]'SCIKF[*[@GMi>/=e1[q>Ci<>/kSqc##"7e].sF+XT)qrD5?8M[@`(U/O04Rtiot9B]\Q
%?C$12Qd?J\Q<))9,Y9J+jp^%u)nd/#DH\NfhX>oB\6Ci3ZjLg#/MSt_$@4E=2IWi/kS>6^gX&Zu7YN,IN:Q%aDP[LN,4WJm9EC'[
%6>G4R#m\$E*L.-Bgpm!qY%8B,Te;Mi6"B]5M@E&IY&/T*YT"jg]i9;:@tN95aU:!AdhOgBTJdqeTXi054]4[H*9:S8_eA`+/GTS]
%OD2\8C"1M'oF^[6lX2b.U"L)V6ejJt4FG`rWKrht1T#jt+PN$m!\F3Tnf39JLM+eT+DXhJ\iE1fG>+4qgaZR\3l!#@jQu*5ld;\d
%Rt(GdbZ!NFe(I.\YqoW1d^L)V*gqA^A<4S.@F^eZfr[,u=H@7ZCP,'>`8EQJrS@hBi3N#Ag&Gd6&_(h^oC7[i:gt2pMjVIGOsFt_
%0>b8s!b\W*HOP3g8PT`^jZt;`,UJ`[,MjKp12%[1Tse:AmrPG!i0!u^6qq`i=[fe\]sU$E0YS#?QV;8sCB@Bn'9V\c,hN@"U%Erj
%W5.(r@`IU'NOUp#q*[XBn3hgH]/7*%FYt(qT-?.S8$c=H$P;6'iUYL%cpJ>*_sNSCMcU>`f)7>Un0eKI`N^]RXFf0G79%O\VTIs+
%j?mTM-efX-fp#%R#<JI6/BF<VB`Fc5igU-=l6YC^BbHr\Oepli'ra2qmX'p.KMrGeQ);!kq]e(+#C!du)dLrjK)hu"IQHgP6+b5R
%:+&;Sl0'U^TbT><^)hn8IN'QGe*(2M6)p%3-oA5>-7(gb*+Lo,n`(_aUS%PPkd1<ccG+l"MgoZ;s8)$R0AjEhqpcc`DJmbV>Uf?4
%B7E+[\kW4hIp?9M>b8Vm@e.p>+!pZf+1n:T:dWB=hu*7>n)t:3jS1s3I7sV+IuV5BHdKJe8k6WZqEP#3*PEN/q!=&OU<SB=<fQX9
%"q]1>%n3EfD3@IT^-s-6[n7_2nZ@6]UYkA"MMCg$nR(<6$Z/#*Np@"4f7T6T$DA?#h`VB39HdE$+S5q7ePhnHrBY%icV>mBM5O2Y
%M9clK1cVV'a-LG8-lHBUrh@t1;)I;S%O1/-"PPYb/'8Lq1pO%hM%39tR8S0#PfB582MfV7O^J`q,ZqdZh,0VCWq%`bn8f,2MSmB*
%FEUn%E?#q7cPf3?.X>A*;%"I$O`QseV$G.-nk)];R*?as0sNMhP5fA1.5$1i7OD:k'0#',,,TFPAF4q//R;dKR,k6$',-/^lLB4I
%.u?;V&jt*`Ef0''&jS=f8Na+UGoqo)&bYmN:6-OHV:H1k3E9Q#ZNs;ubTR<JPQ0\X4Ud?Fe"su,'(`JMCeBp!d7bN2O1uDUle-UB
%=:V4O,Kdr"aq>AuY`DIpr5gNJT1OC.+qlcG`73-9jcD6[9.ssOLE?4A=:acO@Cf5DTB"Rq5m.0)'8O$JKbu4td!?OUX`QKda&]]9
%FHYB&HfgmcL!$68R_4tA35MR1icpQs&u2TsPA9;J,^[<!:_KDLiq+8SUQ[)GR\H]OF3f.RooS5eUQll;D`8E-B@h*I$Vrc];HuO!
%6h3W<e?igK8&G`rji(fq_2ESjM,H?uM34e\19m"hY%%at9GXH8m?cFU9.L_KOK.(cEn0ajcAKoC4VYFd]FHZ/q&G#;2VB5#[$d;9
%&uEX-U;sWr.d:A^J6AT*DG/r>Pq:=%15<%X9l?%;5,:??f8JmF\OZ-migF=dDc+(T&jF3$NZ*>"FXRq/c1HN-Jj;B]k@-]cUm(;`
%=E2\/a>PSJf)0(jMr&>p.-R+$@2_^FP\gh6b(]*<E)]*"#AkGY_f#;^Q,pZu4&Kfli6Oup*u!>I-B$?af,bpr2pAd6X3iYHWJJK7
%ah;(kc60RL3X2I?Nu-En'2*2d,m-N70G2'<P=o]L4VKV?+P:!]H3K^#c<A6-2dlhpEpVkCkaPKB=e-p<?Ad1-,-qhbDq]i"NI:;%
%F%(pcd2(rnV-)+loV`E5d9[/4O[u8l.@DrH:p?-OpR&j+j@L<NJ.OFQd?Z-.@kVm/PfmFAZ%P.J,/FYmgelQd0>m(8YBst]4BHlc
%E_Bu!31cFm$u$qSI6bm>fE**KEg18KjM#;"YDln;-70Tt(!Ts=9ce=!qA<iU3E@g*<*\%p_$B@Y1F.h@ao.u&/q\D+SRAcR+5T\p
%$#LViB@Wfu4;s>F"jC9)#H0#]Mt)a5)^u34h(j5Rn>@h`@.4dPU9@/kV^K6#OIi#Y7d>M<lp<6q)H4FUdt4,;JoUGZeo3c$fjK!f
%o#J\(_*fma-*OY]>KPhtGg0aCBFL1q?8.MV31S<1%p"HXcBH!>'Ik>dLlY;10ge2PjbQb((UrI1<LX0\/CUFl-PL:\b)!>q%NLHN
%lU^OsR[l!Ob*Nd0KN)_MI&6*ZUauutOWja<%h,E.$Km8J:VukYjibSGC;EPO[fZ0G&d;gT+qq8[ZLi_V0P''e>;2gCM(IO6''lJZ
%,e8t009dJ(7'&b<Xd:nX%!t1er-[/QAt+OHok%E(IQ/YYJe&'88K\Z34p?FEr!U&%?MHVpHa.qZ_4ek1TWFVCRJ"?nU"i<mH]\S[
%Gn`5^@h=Je<Y/37*].(nkW0#*d7.n%gn3rG)2s@ilL13Ma9^+2HDW-EW9],5nd,uq=9dkS6U7#%Zm8_O.c(:AqbX6Z&A*I]-o'XJ
%7PDbPfsW"r`]go6Dg"ut,\GH1<Gj]T1=EeNOpB*/WWZ00M>&dOWtL8Y\f`T,GS`=,V5X<F',ddh9@_@.O9?0OEdn-%g>o\%3@/"+
%#@tuRZW<0F3/_mcRJ2ZgkK$4@ZQLABcYmf`;Ak=I'LNj6cu2nh`jR7WPIeH]AH.tK<Krn_0$>=55n#XUY2[JGEhuU&"RRb%;2lH.
%<=M<^4X=#i-%083N8,_('0)Z8AhgH?QN9aMM(t\HiJlm$XP&h#He&?kkEIjS*kGl>n2IlBR!d0e94bImGt;YgjH)&,QLWY(dJNi%
%Vsb@S-IDVZcN`^p':#)tebCe@(@)g)X0<0Zg7j_[V=8%>+&7plMCcV17U)^0K_.Ml1IMrk>-p"<'B>A=g:d=k2R*[(J?GKc!#:*i
%n>id9_D@;6=,VaW]b!'AoR3#;F:@6d(Q3k,]t=o-XubAu%eP2nFoiE-3GAW.8L#__k-<l<oICQ/!ac^$8I:.rWC?J14X<&mOu`<-
%E1&_3hC:[]U@8^%PX@e''FJrOhNsKIa%K2B`(j3!CY"%G%Oa_XUQ)>@OGimnj@Vqq9h*l]?FCna;'V2k`fS-.,<`CbM6>mB,bpT^
%*$#Df"P0/5R\eAS]n9>M=k^L2L!lAAeOnrjO3n??H[G?D'17N,K&+[((.,=cK@`VQY8lSsSQENoL*@Kb58Ea1.;HrrZ!pG[/1Dh7
%A5[i>/t,6;OG^'%g;U?'h+J<?]F-:=2s7PEL>'%+9l5mtmaTt[2!VD1kXfr%PqLLc#I/kP'$$Y!,9h:3jr!-]1YR9@^/!$.K[GnE
%Or>i#0]-)X$PG22^d6fePSW13N$I)1cC1R@I:_*"\5Lh-ALX?%`+3ANZ-cImbJFj@49ie^jjPa$1LkE,N%T-C;tNN1J[-nl>RDjH
%]0mp\M/On)X4`Fh\toD<P2qk5F=:WAmo/<MRE,G#9qu$W`_siZ.Os/pcWAAcAa@CK+M8=j#L62kOE!8C=BJe-GW?$D)9(3Qh1A`\
%$#m2-7aAtn=VkX7IUOap'@aO'Qh4:kdU#g4Gg_EkRtP"21.E]sPf;k'$p65Kaini88`#(Sc!Y^KXD,\,=;:OmcUfI1&mKn>RB%l&
%$G\,ES33ek!7't6;guP0j@hJ*6):/8%t/':ft+s(Z;b8n'L"tD9/9OJE[Y=SoGKjUS9C["Vhu\Yh@+]BkWN3k'""&TpiS+hE7I??
%GjGrhKcZVnoB2X4\`fTC+j.SG@@ql`kEFN:M`(5D?F_4+8"jQqVeHR)f>[L;!t?ek?kh(/MO-5bbTaAiXXpI,.R[bF7P'pF>i+`o
%ZQkBH'[B=58d'i/Lf'aFSOI*]^6=f5^__\GPtp7.+?D?>]"ElY#"CFR-j?0_)ieDX`74Q:TfVa:@G;;k>#3=5i]&L'f1lbagises
%-@-E^\7L[ejO?4P%")BK0"";SaQT0R_KiI4OlSgkJr/i#f,dn@ANFW7'L($*f]B,sTL79uB&,Rem=E3\OHn[Fhjn"5-:5c3Q<md9
%YqZp0)>Jc,N>4pn'X@0'-fiDmB$=Aqa3R/slXBY"Wa4j=qQZfnC(G-U&u+5tY'<0bVBVmLks8$V'N5nj\ucQB/O/_kN-6p)H_qOS
%&"XV<l=]fBmp-U`'Y;pnKV+J)?n)b8O85/79Ta""Xt,9%+HlT?%i7Om(,Nsn;C/adK'u^3%#psbaejEa#q;XoKl'BaGhgP@',f,[
%)`r%maPsGSJK&)7;*id$S&[?cU9X&Z(jZa[Q7d+8#H\7$b/d15O_T6)Z_]gH_1\WbRRL-A-e"R6KkLffL5UTjS!5\K&0)'VK_:i-
%F/55.MG<b9R];*Qk`g!sfbHT48W;)N9WT!NH8<I&6cC?B:4/c(/%]qMR+OR7Ou9kQEg&[&SG0l96`Q35j8nj_&*-k)E)nl\Ms'q#
%g=R-[[<N26D+\VZ-#PE1'6,b(G"EF6cT)TT8T,/I5XXd\<o=gHObo#oDs"AWWY/_SW>.S#>1SDk].h>lZY0)i+H@SNLtP9.%h$8Y
%2BmSTR0_&!R/BME<H4IU!]5sm1=Tdt'"O\N6+0d%=[XF5$fHo116_4`6#U4iGp,Wr0`lNX=V;o/V7`:6+F8__e>_&`nS0=Xb2Gnu
%[`'MH`1A9if2aZVVC/L^%*0Jkm=C2RaTf/gne9Qrnb-?f`Si!O<mdoZin9/WcQoT,^-E^t>F_gN,0-#6?t`f-\LN_K=<TT-2,c5i
%m*cpVB4\Dc#KBM>>-^IL&\]s)+7u^("%m+8(HXV(SP/04luJ)-HLXZ)%aCISlb.:bhk_)HZQl[1!"rL`;_D^/P%f7O8@4g9)?D8/
%lrq53N2]epK7K=Fd>NTL-YGJ<8Y6Pe3`QcB&W`jVKnrTG@2c#Ig0kdr^!*V"1ShK[<.@5jKE&3oXkCcV*,m?SP@@Qd(JJNiE]"`F
%QuL&bW_->51n'G"DE\YTRQ_ofFAWuu8@&kkGU\I4bTBP#R+t\Q9I`0/92VRPN:2ViVVcFC%dDY%VlYqZ,p>%.-533q#()XQ4GAVC
%-CVth#d_Z!)1G/5>jIo:?7h;@X#[E;[^rPFdC)sQK3`tcK$$]&=75(C<9b6n\'P+q9,>839Q0Fsl=K)2P3UE^#]=%+7%4=ib!Q8:
%RC_EL0apb%#Y?W-&#>U.boWbY?,?_IaY0&"#t9NOe65S\D$8Nh`>HrI$&!e8.EAp%^.?$5ln0b5?A^_i4PZ8rTF-K;LN-7"f#l:k
%V==u&@CSaL33C+C7RZWPdZYHW>J$Hr0+S3/S0DM.d;YmTeB`VgIJ&@Sk>sV*=+>\h6KoJe[/S70J+U@Vq;8iKJ,Jq's6Vc.(Se#Q
%s6*hVn"Y@Qs6I#K*rk[)obmf.X7Of0oBWH@2rD@4T0>:3l/+*HY/W]?(^#/,rl)L=ZaW.\qY=h"M`5QLm^>.SlMTZ7S$OV_&WZkP
%hlgq[5LP&;I/ERSs5QnY^\I9(e!j]N^UneTk4-X\^]!N_?ToK;\GGga?i9TXigp)09:j@W>b0cbIAAS$CiS2aVH^0P^8"j1inm!#
%`PpG9SUBk_5J?[?)eMNuFu:03PP7l4gC86!G/'sEfL]>Gn@.4J_7M:_q!.5DcX5LEQjF]!pN4,0Ispq=L=p7PT5P\=^@B/2re7k.
%GPV-bS!1UYD./!2j'D'@c+>oA]RO+'cJH:IfQ(6EE+]SiaH>q`c5.jHc[PKJm/Oa\]/ncb&b!*ae`<m95J9(l3IU7g/JENTmJ_3V
%Z0<7bC;%fCq!MTBnCd]:-bjur]WC^SACUUj7f@e(kj2+6S(BFTmp>lSjq$WVO7C4o_@a30ebT(6qV@XiZ[[=Co(?)IVd!c?BgTG$
%c_CSI-?9"Wfpe1plSfc[17R_,^:_%kr;-9Wq9?A6nrZDX]#jgSn1VO;qsrs7K.=?3Wa%T`EX,V4TZ[5He+mFNEZ:Ca6'^34._!5B
%GL5FE:Qt\Hc,mI&(VdRGIs]Q(p5/#SFLd#VGL5HCZn=;ES6qldp$C\eY>/D2J+G5H<\M<Gcuuil@c-jLoXsge:"/oHkWA?EZ@V'S
%]U'SE\$n<"Is(J:o+N%g-GatE^8lO-EUiJ%Qg,[Bi-$OQr7f?*[r9SgY($3Am.OY$hJh7VDdi%:GKAOJb[J[6qg+$Ym'c5bpY>*2
%b:hbEh0d7[Gf"\'pH5o4H-8.fdVlla4+2<mo&eKqe_?RWX"F3hs!KlALYVS0rq^P??XK@PciiN-Fu@)D&iV6ZoCVn=qTZd(o@;lY
%k:K!YgP[Wf-uHGoa_ciKH2I$SQRA\DT:VWjS'\;"#G1o*9nR?"n)$9O?iC??IG_R!:l*0lH2i3gc\A:kT,m4iIbQ^Qh)pfYc!n;5
%@[Aji+0NqnDN;1D+b'F\L@M6,I)l*M4@u70"76/:mhsd%L\Ld:q3X?srSF!@VXo2&QXeo4^]q4003bUX?Zp\[2`tr,H#0(]k2:H^
%So'i^gbOYphtuo1+mRVudM$"e:Z+*XDLV/?2s3,,?(TkgREUk@)_]KTrbuoWW4h<^3'L%_pd6UG+*.1#\Y6c$Hh6DV>0/-@m;0ON
%cZ;<7l6m->5)5)V%uY"FFa\].b;X'Hs0KO<^]/jYS)R09hGUjl%Ym$@J*M.R+ca[qioPn?r&6",ceiSJK4qc!rTq'_A\C:B38*;2
%I8ZNeL5gI6))YV.f`sXF4-b,qhgGCQou6Tr2a"!bW.mDf2KiJ7Hcj!m;``j&bbYF5qWI'&cQSVH)=X^\2Y`6&cMda6F.%2s_4PZ\
%m&"XPpPZ:"=RsRFpOBG:@XKYlh//:(&%VX'[+tYo]i\(liHaD_"b5<8H@GP;ajnqp`M[>bB;aMVYkpZ<ePZ4_#5>ogcgUj/Fu@%T
%ifdE7[l$6Ua(YiaR)8h-!'L^(mY_OplH)R\>:/r@?CP;m^]"#gjRhg0m-<`B-XT*[-c[7d5=bJIoXFgIs1\#q>=:J.h!7>;pM=8j
%)=#>JL!,\-h1dVEV]eoecOcVYjDh@OQh7cE[6+91h_sl@^0gObWS(&[mph!DlH-97/X-"EDuIE+[LEK7b;\%>>9`^Lfrk$h_cGW!
%Dg@,@G;k:9B.TReIjQPH?_?5Gs)lMs3h,PP<ZChh3]2<_G<C`VcgQ&SSK5JcK]pt(\-5Ps>4$&C=4^MXNo0*c]?>EU&UeV6K9jNh
%Rb#IRe_/'!N/#fFMppQiD[B*ERdOogDSqV6Jh"9IEC<@0>tsGAigZ-M2b[ii$o]PP2Mr9uE`Z39Eat('Qff/$gVVi$G-"i:>b,5>
%](/)c=<i%p^hb[X+VJ?>'XZ?P'T',,1/Gl:nUr,a2Jp@G?]6S0CJQPsjn*hPU)YWe*b&"?`,`TRAj)EWk7ERB^qLdt;>s_igRoHI
%O][iB9O[s2;4`J(GP[h=;790\WOt.81)@k/!+Je`*Q#L&$\qiKI?g`AYpat:h_sl@^0gQr)N/Y4S75@(9]i>0qX;m+o+Y\L2XTT2
%E/e/kjjOF[gD&UhfSQM]\FFZ!ihMNCQQp`Upk?KXpWn?.GUf?S^r!<oMm:61oMgp$Sf=?pa%$$\.:`<1PaMV\q[gJB%X`-2&fg]#
%JZg*hg6:H1^O@se#BB,RY3e#L4/5/hi+`M"p0h4n+KQT:[0'JlE4+lGeQ.aXk<&0On(X"A=Fu%P-2XJXJ;7q4iRb"LY0htM3*`5r
%27dpBZ9-2JU(LtbJ(ta"\?t(W7.u84^\*BR^R\[.iSrqQcZbKB\8Q$Ur9Et.QfhG2f;cIu$2*uEaj%"I@8JLlGI>uI'+<oM>ik2P
%U%g4D'F,nJ>tsYPf7\kKreVYW:_$#Eh]!rX]RH).rrYe?]4#VJrfc9#Hli=TNlM+rAGeUis%gX!*.==sQec?u++j9nDkbYN.TH1=
%:ipmpT=_)Lplc$Dh_q#I@Mo-oE4bIb7e4P$[dWSLd/e\,cD03+HsC$-IJn?QiP0m:(n^X0q:RA<_(3at8TK@kG+u`f\bL[-iP",V
%Yl)[So=Qpc^I-Co_]0^*A\'aS\DR2QQuDHE*7F9;n:?bmhS4mh`d@IZD*3)bIe2+Og!(:hjiP6!*sm>b=ZhjCd1Po"H[Ej+4(S1.
%r4`L:mt]3ZQN).6^k;N-Q4NuPKE'OMGOYVRr4h]ketE_r_>elOs8IZ!,C+flqdcHIH1:XtWF]kk%7(5kn%n16nG.O;r4,,i4TFuq
%GAkf6?!iSXnS65b]D%4h*O]n4/hX<AH2eR3cN!gmc"QV+PQ3[%bjb$2G,@H>jU@c!6peZ=H=p-n#$1`kc'LfTh)_[mc1db&mJBPW
%fD?FK8t0I8Y`KkVA:%l2g[oML"#JW(@-k^'Ftf+#b[f&K9I>).-\$SdG'<Q#O:ol')%Z95T<CKTlhU4mg!(m_c<c!@rT<2+;8;h@
%I^TX*SIO/oZ]"@g2VOXd[QAhKgP._Zr6P3#]:6i5%c2N$cuR0=RU;Vl+l\lBV4GW'r(7Z_A!Pc>jK[T?nau5#B;\*DH1M&+U\d:/
%SZoYQK"q<>f0cA2TF&o9m-a7g9T=H92R.o1BWoZN\-G+?rRa'+H8)uO$nhk'bJE]=_5Hd1GW?UI%Yg="i7TD4M\odM$H)S(KAgC&
%2:.!7j[0G0]+^6s:Robf(m$p]jlo?@OIfZKm0@@4H`s&+41'jX0K=G2L@DRd-FNmWloWk<(8-D1#FjAO%gT=qI0rBss&\a$<Uk=B
%8.ug&2h*eIJ(06rGT?JQ@G1CqnUSQ"GT:q'B'oO!\Xi11hSrjid/eg_Gp+ki^qLl:QO`4P]/lfR(n@<r^H-XgrQV$,c#_(Nhgdb1
%aNEBZOl4?ZZYq6+q>)feH7N%\DnA)7>NW/`g+#&8n*@7Qh!=g[0AiAWcu"#Kp3IrVIVO*+pH6J<U(6\em-aIE#52nH5,.B$l"nL.
%6T\spqkp-9cS2XdREZ,3J+ESQCltuG-t7%_`"ld*LU+(DDSma63nkEoB+9&CS[;rhhjhI<'Z@!<%X*docihM-Nq%dERO<$oB[=JS
%Z"4CM43$,l\kh_=+7'gL43##%be]<ZY[\[>(jfl8\2i4VVY&[Bbbs?>kf_gl&RE)^r;Gm)a^7!gk,s\f26#%62RXt8Xm9;2JE-Ib
%jR(W=hOOPZG@t!T>?$29m<:FsX6WQ"c>QH"ZfTuJ%8aZ$SR61oKXY.RC3![q3>iXlicj#;>OkYPGOM)jFhbC%laaA#B+-W(M:I"L
%k5'+V[)1(22j4^$9Q%#B>e'+Uc/O!M5Ab\HSj)Sk!VYd*%6D9gI@NkLY`^oCcki/TO,mfCp^l)NO(J+^e%7PG]Xo$IpF.$bT+Ce?
%-E2=K%1+*ioOO;cg(-jI3Z(KHIo:_LL=41U5CY5rq'cJqD2HKio8p.-;qUhp0DE*\^,A^KLCs$`+l;8$RGr,h%t%EtG^$C35=oYg
%c22USJ%ui79DS4QbMAAEG,`Z%j*(`Fm\%fF[(.Qbc#"!m%bf]e9];YhmGE#7(u!aL'S:rq[qfhu_^6Cg\c#Ih5&P&a6(L*9EsHjU
%T["S-\S&"hrR^`8lT`-EflAnB?C-#tT7Jo0D%c`KcfO>dcY],r%mut9DUfR^W'1GOL>D0o)\_G-8/\obO"54ca#r9P4@*5/*DZ%u
%0X'pM+Cd>7Z\tOF8>Z!/rSlhtoIlgV$0h<PPLK,mDuXsD.J3#P[hi7"C"dH+kAFcqq'U6E2U:Kb>bg3'_0_?SkjQEGfm=t,f(6<<
%O6O!O]DAV*K=;8EqW`@r[V&r#:[;f1]"[0MhHR@?]>*"KJc"T19f`(e=r@1VGHo,0Jp2^KX8-V8Yk@8FaX/b1<pO&9C.#)QgNRSM
%4?5RZ3j%/]%gqYf1l]cQG+[!j[(V(\1C#ri)k`nM0#mqFBHCL6mYjTS%IE0:J'K_)D/s;lb?`AJT:R'2npE"Yku[%!,91WCO8\ud
%%YU7cYs.R$<LQ9@fFJPVmf-g1Q[&[__J"Gt\$*!=c9>ono-XcoocVue!(FL1WYYQ<^omEkW?-_eV]c`pA>8O@+J1,JGg\q+EskhD
%+==er:X6;Qi:"W(H8'M,Zo&dDo"T"OrdWV[mGFSrrT1:B\)uUm2hs!^n/-Q$XZ=>uT#5(.'t<=eFuP3DG0g,:'q\^3m`CsiS$+Hj
%2O.Qm;X`Ybq9:!Vc6'I;*$3-`ITZ=BESSEqp\f*eZ4D`2ER3_@-h'B=kIZMYEW>ds#Q$DC718[[f\jIN4WfG.5+VZIW2"U#-fTE:
%i-q!"hVJX-ne>T\%OeY7F<?$iGT6rrU@3[-*BF)E6[p!8rHlP6DE3bkp_4S*hEP<G2LbVu4+`!S,*Tc();T/Ff6KtnK%#!oqcsli
%D`7-u"MX%+naZ-*ic]U&SifK^Mrt)'nR@+R^@s#n+4&*:PNi#ET9KkubM3&M5plT&m<JcIOq3X;p\k@[])Xp5-.%MGdmld&IMRs,
%-a5OVIs:gVlKD#ASmF?j6T3n%]1#M%TRKWh&=p5T^-$c6S+AQ\adDZ6S\j?!K5]o7jGSr&s4h@=][_=0]?PtQ?7)i4]Xp6'j-+!u
%0C+`^3ZPK(F3OXNBc]'[?tN4!Pl5X#_',o>:nIWXhOIiarGkOWKfqQi+.R;SpK!=cPLK53m!P+R%lndEQ)"qfO,%n66f*93EH0?E
%rnd6m#dO:e+dO3a"<U_KkpO7@GOI`qp!mh.L;^GmUsFO7IJ&BC$U`FV;#2ikrd91&rl^@PoXh<S&-([fhd1bOI6gW3lYh7G@s@R'
%+90D&IJ1pHJ+W1&s)giHVuLcjHRc/lnOLbS"k&A7/`];Fms9&3r^bntEqJ#kr<<9iT7'B4l[Oo/rd3#+3D)fV4o.DCf,_OGTDe,;
%5Q9&gg6-qgI>^V?a7qFNhMs3ScTVQ-qj4/2GXTL>1:19j1%^g3FWmV:0^jj#?FlTBd4+gO7h<"(&&,dMA)'JQ02mn+Xm;aaC[FY2
%[T;Z9[PfJpqp?qY+M96h#AP5bp9=\kZ2V&Ib-^jtVqR42;hj%5NaF0.O5[I]*N/\<`nZC6?=m7X)Q,;7U">X]<Lng_diY?-q5(9s
%*rT*m`g<\"7'lAmapmYJK5WNQ)H%_fln:^K=r?:[WTbsCHeFpe"V,$%,tm"dE\,9SNGn(8`bOn`b8cGlAK5enR+t0?^$;MCpK][)
%">;R@\!(;%?]LoUq-Yg6\(Tq?56'jTg)a=B=Y@TFJ,,#Ods%..]Y9eghOf0jfAQ7?OC!8%gmIaoIBBF4q&%Zuo(Z^rS`;qJ8IADm
%30D_C#G4ZPP=\-f01/N,L7Zlh#<?_-]Gs(7)1D6^V\s!g7*@6)b;r:gkQL_OMd47\AXR4)>3=MiYp;Kua\"`q#BUZ3XF8B7F9f)"
%aXoJij7&<TKKdE.O,eq^`rF1<i\XD-0='5ASB+)W@;Mc9.H@q6Q_SD[iK[6O*Z(/KP^1dH+5O'>[e@NnnCW#3i+P8u"<Te0_)n*&
%87PmeehNFRDOtrRK3XcuQ.-35Jh];&42+M6S"%jHDP2":8dcep<d4e((^I7l&W@)k\"..N#Eq+[@Vj7^,4iBdo\`k5rLQg_3&6,c
%G[dU]<VZcW1GG2J-_BY`QsQRQ')dM/:0]PR5,.';QbfegV:(AM5%*D9o[pt+$E_n;@,6O8ZsF`S^c6ZuN]#5uHLKo9K@A[AjptdS
%a)93j2snF#q9Y!`\igRAj<M9#*gH#9DK.5b=BQY2agLDN8i7\R\@g_fY5cP'J(0O.qQlsCr_&jAmtL.gh/1OSfo>Nn7%B)g0AhUM
%[\e7o&)DJ9;O))?h5@%J%7\hk5I<h^@jJYaA_coIMkA%TNIt(Jj8I2n(Fmh0C?OX5Q)cm!<6+;s["Ph/%EJ?MnUefJ55-N3ed(BG
%ER,d6k8U<U&[Aj4.56MqRpKSu33i*<(,+jnpDfG!#C_tq(rp<MjBFc;D4/gSTMnP$Q_<t;N)hT&l?Y(oG<Cf\9DgrfIt*5%p>EkA
%EL@nNKnn7FZ,J;)Im_6iP(ZmQP$V%M)d;nUQ#ZhMb'H3bcX_%]=NA]7A2*m_q:=+?m^YYcgl;7Q49?NKAmI%9,%,W7fA,:"/4bKY
%G]V"e@H/bnfJ]1A4hbJu;M2k!I8%=D3B3eGEVH;>QcI@uZVG<kF!j?'FF%ok36"C@][FK!N:ACf/4L^12&S,?G.a!P]F:VOSHc?-
%GmHbrp/M;Y/c7k'^3]mt6]qm&nMB0docZoL+cA@Kpqbat#sC`=<9$AIiMpDK6"S)lNp1%dqSCY\i)SHS#$B@,7[@fo#NAZ*f:\/;
%9=5&=='<$o'F'm$rRg\ljZ0=4Lp4ZL,dm.F6XgpT\HPAB08HtFNR-o5<opAMXE6.a=HhQmTf^2o*@Fp@%\_GlAI,t-N/Q3l->'jM
%g&l?baGoe)>\(KWa2Y\&EGV#B).OP?I1pU64ag#u'pcnfF3=.FDG0#0mX38'#E8O#PaFrcGm4,-%PS)rd<sT$SC++7g$$7@(,(QH
%#SH).pfP0OeW%;>@j'a(AYB?+mKQDCE"Xa9o4>0(@4$^aQ[r/?Vuf^*iKKQNA<EI=X=50K(TULMSkGQRR(M.IZ(=g7\3Xq:Y05`\
%<<T*`m'IbVq1b7JjsoZ37`R7+K/G9jDJom5m9gGrp^SlLDJ_/9,bk"b#OcaLLD2W\7bQY's&aq9#Prc_56&pk7cZKlFH(1?]f*FE
%KqNi.Ak%0s1WpqN:_eD[I2=)u<<j"(f3kNp1>ac-QB55).H:G#,1=o-nZ5@3o3h7l^=7O%BT]WCd-+ldUG1?U3-1fI4aPR!A.&o'
%OG>aWZ(68]cQ>:m&"TR&ceY@OFND-5QBb^.ViSjXII6kt(ZG@gg['3mUZK#@IbqE)@]:&*4pfLih?&-#IIZojG5[`1^!;PaX`4Wh
%h1cm^Rs(9i#8(DP]g^+En#\I8gu//%o(t-t&NE:ac/I'1L"-Ig.QL2E:c4A$c7*m!H[Y+Pb*didp-B\p8J:90m#rG\)B#mQ7,0ud
%8E\cnl)#RrF72kOhhI9.923(Fa=fjZs4Mac?HoY68o/B1s+(gcm6MB^rBQF^K\^=/"pCoB[muFX>O3!MR8jI1._,Jkc[*Mnhp$M3
%ePmnp9Lu17aPaW+4(gcea(*)G=1%t-pVY$(PNt#+6o(o^eU/i\nSgioV>etEnER^WOW3HEFnBH.7W?+YDL];UI&H]N]BF)iI5\;/
%96g93f4\/CfQHmJ(Po6D*&dV(1l!#sD9<HaT%.B%?c"E!;t#gh-!]Qn-!1A!gA1Z\`%,*TG[.>F=h+#`\*uDHQa;)\q>1$3^4b+1
%Q+5S,AT]\aai-JZp2\+e9@`#<4a`[=PP6aI?e,X[_"&eVp*I's#+.6R]@FN"(Y;gVi3pqdc4W3``"'d<nArLh)ET:I:;.L'/4DQ+
%<2#>f1Q)[XX#rR"h[0eTeXme,#`4fdVW)G-_F#`aR4_jBNt`fa7E#KO"j\cR49A?032LP'K^4A\Ie[qe@cImBj#-2b*5kbK,.hkE
%HL7rZ2(@?'&\j&#>q__&Jo>Q>.!`P#IhfuE[DVQGA:g+4Nal,Mb'pQ5&:gtM"8`/iLe[4*\M8\q.HC=a>T3=[g0ruSgKqa_epOXt
%/[,-.'/N+FK;":5Nn1gKiL9$(iqgL+Png4;G8:,I\iJeOX4tKg<f7nN<5j!oGBMuEbT48A-/Iq^[<`jJfiS.A$8)UEp%A6o`W/^f
%Jbrosp0&d/Lt`8e>jCanA:QA9qV8TjKPelnHtJ4#h9BiD,m(^1ft0GGl[>rc+$Z_Y?dl0]lJYh=pdYk5.jsLiVsO($q<Hr;pdn9:
%2ks\OW5T!KKA$[86i5o_JbW4JXC%prpE=ucjhGgrI-`1_B?:?OOqDbsC)A<I']A<O^oNgAX4PNF=4&[3L0Qp\2(`BYOtT=:?FMn+
%5*Z=NUKT%B26O7d_/l1BiG!c@M,#kdI5=RfCO%/_J#TiMit]&U0;4L,jY%QVq:#3?;BbqRojuX7nuUmM_BnulmUo1Bjk48Ks$*.N
%eGbNqV\]isM6">Lbe<<8IEY3ohBA6ZHp8(aH@NZ"52tP7l4(Jm2nnEaIcC<*G*91.Y,uYGHe_EK4'g`l:&k$EPkW601YZO3'engY
%L!J1So75G("C@Q#+b3fKA5q[:iCR==irI-F%\"_K'2u=L6Q1H2i'L@aR?rr8P'Lb*SP)ZU^>.a5LR2)C>f%NN#Y*a\YHi<Q,ThER
%Vk%..RHpkWqOU>cbV8Z#i(t]_ZEgXB[ItF^NsFX96N^ZlH@GAQ#q7&+@edq8jK;^rE(LRHa"4flY?eO-pFkFj0>E5\(UiT[_tRG"
%W11@C*>3?B$+oXf,QUq^!'g^T,GB*^A\MG?YR/:!g]92+IgP5nKl=N_L*^)OCbq].M4eY`*)DmG+A>90WJ)MP$=NtV6EW37@JqnN
%)GRZs1Ef\$+-(^d'XK;>&,WpihqUngr$B'0DQYGmp$=1]iMD^_aOgdC;7SQ1=RJX/[<Y,`i-*SoBTI'*]c`BnnR@bImH2gP_EauI
%ic8&3&J,CW1asM)_Bg2=L2\C(fEg)S8R"KaHQsYu\7<?rc7uNSR@.(NP1U&,kgCG62StJ_@D1h:)aX9g7,sL<j@6Y$c!NYZpgF5r
%#p2(BKD^&=OWsOfF4g3t)rZ+QAVW:7o4%GaE$*:[0^bJ0_*273Gk]hq4/.UU!2&Y2HT`E6j5hJ5g3rJT:^6E"AAWJ#1uF)$,i1'(
%d`i?#*e%%?OV^NbAu\KT9KT*p2ZSIR%2cjD5.K+H,DpgbbVu^gRQP>'q#LcJPZo'HQ[l;1N;YL3=pUgDoF6VLH%a@S'efs*aO*N;
%@(Kmj!3m>]n>hB=^AP),+n(n'O\9bf6ZsIRCG+]*b4(G)<5Q!rhf8/OoIo>E0DVGN@j.&Aq!i=%>0kb"SE!!mY/@n(hb)*kW&S=C
%-X8ks\$%I&X3:2$gA]9B%PN&)3B"Pd4.9lT3S7K^JkF"UMk\g;)+0UZful/-^:5ifI@l?Fc4pRrik`TIJndCgB[<Ue;ltIC81O]O
%X)#tP3d7d!dFV.bX%32Vnt?#N(-)D7eT]_"q])?rg.O;tHk+^m=6J?\KRXK1`[Wh&+eQah&,bRI>OhQL^VgJV?CVtI1i5K;R^YQ5
%ErS.[JV+g$jCk\DLd"\/EB;-drPm4]qU_=u4"fir52=iSp;tWfiT3a\EhiX))m5+??9TMlmn`Xs2J;*gZQ+LO)O&39rTE2jA]6,'
%_Pi"kpgE#EJ*M4_Zpo&Db;-`#Uc?cG`5ninH3jn30t^27FA0j"UZ"U7ffD,Ha8NBKrcgt_ih'TX3KJ/$]PA<gEU[]T_Ol:8@aUNb
%#VJ@:==HO205CkDrD9S=R1Bde78UlR6N*6?`Wkq?+IL^h1e<fOZSV.5q-WIF"/@iG?r)>b4F^Yg)>eqpmU+Op]jL5:LhE?"rS9EI
%V8hNp^A18uT_uG%o+.c7q;#,es5>oOqu42lr!5X?2t5*Ora3KaHqiF>c!9NV5B+,]oD@k"&JVorI/Nl2p_Ot8bE-j[,U3iic$O?2
%ld*<(ZF@%:O2'XWr9eM&Vt%rHLUVd0m>K6Y5>[1,pPa!T9?"e<)RBG:$tEu[beU(P8qK3Q)+pb"gRcAK@,1e[\$,28N'6rYs2Zeq
%@$#s:gkD]u*J*E$S@Q!,kIY(#gZNU19UI'$Hm]W1@&L(0R-!o!01N^p^\udt,hFMOs62cGnGVhTmbMg\n=:*foq26_]AL8Jrni<+
%mC>-emK^\c\%J.=V"ab2(T'<OED/@m_0GWNB>"-':0;u$+"Tuc-,Teo[eR<R)?2L?e@^1E8Z*=!Od:C.i+;IQnM2neqrK3Sh0f#>
%.-J8jpXLRn8W]K]5;N3ks&#,$$,ASUXomFYk(h2_n<0a^I$#L/?cL<*;iYkdZCY.!CsB4f?]P.(<iYMiCI-B1XXtCZqtjsU#;6`D
%hrEfsHFD)0I</q6^\u7CTAc8i0,@5B?VXPi3q6K6J-U=c"pE.qofS2J@"$A=*pZSa>ote2YC8J-B@qO^$`=IB2PDHLYG:j_V&Ct'
%GFatArDS%k)S%DmVCd(q`o@hMUW9D&."ch+ro_p[)9<]n-kc6%K]_=<+O7lkJ_0hRq8c(tEK;:?bWQ0[fXDq?s"f%%@ltP\M9nG<
%OmgW>Y[A(sEuk;YnF?R'l/gk24R=h<9'e=om(`M?pqQGk-*.<@&6K'N<]E4S[]_3e?\SULDL-9Rk:(p.,nN#gV8'4ab#[)i`b(aO
%.=4kPIX-VGoEiPs@Jt0`N#'"cjmYM-jn[@F4;8f8*#P+IJB5Ghn'Y.Q,5ZXU]R,Bsa*7"8>Bd\1-0"o;ENHB(?B5%'5)NB<R_G0?
%N4<t/=P3Vpqr-WUJPggj8)&o@8(^kU[1DA3oO%T]-BO+s9c^"7;sZX]b,&och+I>pqI!*ba":?0=B[<C`E.fo*.)U0VZdqQke"6s
%e;u=>]'lm2Cs)_IBq@J5m#Z!clXZB=b<!u+HSPM4g3Zp4fh*dcpir-ZMB9K)58P!Za,9Ick2YT6)%uYq/C27qUOS$#\!"V_\n4bd
%Uh)l_W3`.2ZIh+%#pkUa!;L>&f!QRn=R4^gCn0ZqNrKu_.@9A_[6:akPN-m&]m=.%A"WLr'8aq7mlApFLKt]!Sf\jC!O;5[*<#Kq
%3u/c?Q4fq%-\cWS$7"b^(]#'EiF)J7%S(ilD@7&))HOZ-If#8dJ*Cm7<DK_;PmV"is-RT2PY69hIQ<(`7sm1"<\323\o-Qll,pjO
%kfScOqXAgFY@0h\iK>A.^Vf&Y3VlNG5l#M[8sBJ9AK8$9+Z[3pe]6@@f;g^6lK@?CpL>:q,Skn8;5j'Q8&kQ;+,//SKIgJb!Nh+o
%#P'ZqR@Vd??[drHc[Yi.o+P9HnR1[40`L6<.2EY3PDug>AQn.K/Ppe8gJKMWf(66*gT2\TFtb&#f!@WfZgg8t8*t<`H!E;^Agb?C
%C:X-ql"h_`99^s`cT`h=2.E$q2>M:j?iHgD"kEudQHj[oWU!h.lE>TLpVRTRgjj]8IW)NN'S\MK$"*)=kb$gl)!l#K_7k]r7+;$9
%Cf"?!Olbc'3hL?N6du%rn=S[':qj$]i!qglNW`$)1Ip'?[,/j`%YO_+T"fL%m]ie^paa73L2U8ele[bfd1-Q)R7ot.4K<Q_*]E)6
%k(q(H%E>+iD6Sh#]c@!;rhB<VA=K!P^\R);IOnHXCNu.k0%R@N-tfWoE!I4KB)RPs7?Z]=`("J@bNV@f@4($?[8G,=S]\``W,CYS
%(G<Uk^\j_-:Ume,V60%ZnG.2UMsJQrS)X6^SpoPkhu(FfO#-DfrlM;?U7:j$iL\Zg_4O7hHn<YCC:NH!Z^dGB0=too8d@nPYb9%/
%0)B95B0j?l:AtLE?eZ%_Yl3O#bkB@#rgTMsrgp[`j8YCpr'XuEO#RFfFlbf52IBhQf6>3S<jVOm?;7(\cKqqiY*S\&Qp&.q())OO
%')-eKr8(#HDYNb-RfAqrJ+HUV5>KSCn:,"(5D==RdDRJ967rZ,X-*Z@VI,VD@5!'eZ]MBc"TF/P#NAO^?i,nprnVLAr:.B@rilF]
%h=j_&rPR*&hL5EZhr>INp[A+@T0D^l5Q/@rJ,esZJ,&Ou"bYe6r*T<DBE%nBhgbZ%5Q3p`^r)AfCOG`o5JDJ.g`Jf\r#bq1rU7r]
%q9M_;s)7B*GeNoSs7#%ZJ,F6:8:R2.nFo-\/MP"5elh':JGKd`1Y5!>Ed="..%7E&)]-N]HF>aXbQh&E!@B$nSrfiN+r/M#1_:n:
%^h=kGQ(r/0!'>H4_5T2V@?CXVWY&']ONtb^J;s]gA*+VY8:aX,W`(?_27rM>)@8KNHtDjK!hBPt)/U<TG8VQl6?pkre4ZPA)pCPS
%\,4nc6\-fQdg6b2=a/c=rWYJ#[;5^;^4qO03+3!5ob,RKF\43ND3sG#/JR94A]U]CQYuHsmMaA1CR4e[kk9SL&iNn<eH\ErN/*QC
%Cr[K"C5j?`&UFn<bW>60<FD:1Q(K'V\kU7,lH&PlL??oI;:2A)\UuieTs<q?CICO4E:,!sijF-9S`RGq%_Ar<_`OcpH1.\DTg>0s
%']B@O?t<aV955lF3?nO!^`n\Pp:!RC+AA=\X$'=>_lkISg6T0_;DeNUkd\l'6CGfZKb;JgD/h.lCDK!HE[HF@Qg.B2/4JY-,5sFh
%ajhK<-N(me/sS<e&VHq9Q`ONj,C#qDGe2Jghdb=sktt!c'%@p]V]Z*pe?ZIT_a6V2?fJ'%0?CSf7)H#7Jt+#C-tLPBHRma-e$s8[
%g#bO-KPcmE%f8U="_ds,[d1Vp#[$19O:ff#Y7,o!0*J$B;+6VdM-3)WGs?N#C0Zr1S5*tYct+[!g^&ni!+YP7(RU_9Nrb-F56I9e
%;D31Ncr2Q/(sT.:lm%?$k7VB84oGsXP[fc*(i*_5VG='C(['L1I1i")!<j)\lGblO1Qg96HVP/\A`U<WknAk75.lS'li=,1L:MM.
%7);O,aRdAP(b-*^W6O\gg:Efc9-\a5CqrU;\SL&KiR16('Toe('l\:pI9,h!RYaq]K1u5`DTA.Jk[]5K&=0LgDfEtneiJ>)Q4RZp
%0O\=n@On'R9O`:A3,'aJK2K$C<gkp&OL3_af:ZY+oDm/"=8l=JeMDY[2+,5\Nj+h?orbALnH_:DiKD[`Q>f$XhV'OQ0jW+c,[e0M
%BZeqAnT//L_i.lJ\0#/L(*uBA*arXZ>UPZ3:/<@p#n%N,<)%".]GH@E)@pd9Suc#"#'h$U0cANUj=Xl=0F8OM`F\>P9)(T'2$ZP[
%5C*u/!&kA'Hd=!`S1)UWaF]Kf(DQFb5/e/:55idb$MJca6c321@X8@e9dt0M[25TP6Zq5OIP6aSR_s0!%.7VSkC/fU>@(N;nZ_cE
%Fjs<L09ZF[24cTglk1ec<PmbR2lp/qc1]P<70e)!B:G]Q2Meu64E'*UXXje)q*T>5DN0b#Wjgt/^&-t`2ldTo=7P.j.>R&X./DCM
%PdAnC#/FaR-G?=%\;o(7",c<'4"VK=Mg;aF'D4.]9?@<K6O.c)og&o7>l7aaYU%gVEl4INJ@J&D=&Eq*D(ppuXmp"=Z+;gW;Dt;&
%8!r9YTKJRC.DK1t1VL.mBXK]!$qDW]q6EM!N,0Gm2g?i]p5nEtErb7GGK8h>0(3G#O%3kk1XtK_?M,kjSN&JW.:;EB18b.X=1,4.
%'tg.d3(urW0DQLHpnKIpH=;u"EKF#$gfdKRUqBi!(<IS_H)+j9`NTZ&!])B[I^[*/\L?QF1r_[.W[JimAcO_&p,7UDS<"]lE,XD7
%)g&6i=b+_!=WbK651t#<iOX\:[4/OpTGN>9pF1_+k1l28!8MH62WpNfZ%X[\F/i*!.M1&I`tX-]V$l6choj00(04]i^_G&CmLe1(
%-G^$\M82IDZ4U@VNRo@'o-K&]HAl$T!mtmG%;Bu'DPJoaT(!2<"7WYF1.Nt(eME*uLd49\^jMiJ_jO,Ul5%qdPdga>GX)j85alm1
%iO(GX8!=ZB;!U)X`ih`/VGGuX_i/t:diRtPL`@"e`(K![kX:NT<g5]8Dck.PpOke$q)?p+?`'@T7Ec<^S,2YdLg,T&\s`;7g!aNd
%mI7A]eC!&/;Y8aZN3ISOdECjd4ZmCBZ(-[hDufAsK(Crpp93!la6U>#N6Jj3VW7C!#d?b@FuB2(>_rJsVfqYmQ-IIPZ'WQ!b^NGQ
%%23"<>57B^L'NYsK<"QBeB2(>>A$\Ni582J6,9e'anK$$>#(0OaSc8%Oa)oEDA,'mY?=%XqN/uR[Vin2Bt@"=27;dqG*[#S2DKI-
%RQbEgUi@ZNP;I1Tqb:h+!`IV^^]3*F=*`1`UG2`99h!"aEc?Ltp"+[@2#c[!rF)W,8Ka6qeg[_Mq2UT@>V=I%R?8:=S4`mm8<3sS
%k4tOW(@5ddrOR>k:Da3i-*N6j3d%s@B/:2:D>ub5j%a9UA7\/*Km*^Q^>r7TnWppjMKCR%Fa"m3<?N1cR"Xm2-)rOE,3]0W-*/W+
%p.Q\BD5i>X#ssf991Hp1SIP?]Fd\Kgo$)Fok;E;\Y<X;O96f7r2KO%CM7uIC&]]H+Y+&Mm5WP)=3uBW.m<"+qE&lcCMk_GXaQr!"
%>$@8I!.Yk;-?M3>AXW7-Jei1]C(+(,O2a6p0"j,1;T0CP.k+-c5n0"313KkLH2s21F!4D5/4p!O'/*WnLpO/ol5Qu&30Eq@KD-mD
%SCuoBFRM!lG=LDJrW#Wk4NdU3WY%e3W`;B\BG`3b'8fOH0AXH8'/?dheYnZ%#`bX1],h.],@;uW`9!6;EM<ZZOa/m[3beiOej-*]
%#hYU+K#L@98@X>Td$>^Y"XSmag*7+>jO\7UiZ3p9Inc.m'Y;5)d),4"gCk7M9`*SM'K'>sMZk7P/DiHerU@j.=;)8Z7-/17I$)/H
%U*X+o,+TRqOcTbrVu((M`b(P&cYa@&#^4,u3%b*uGN4I;?q--*%D)]qVBe42d`V2%n6)J&9cui(>rQA'K!1DcOXB[f6UgGV1(LYV
%IGrh!abV;+<A=bSi6m,e$ai(Q$".XcL5)TqhZMW1bZ4<ij*2-dLpisY(&_(VDR;.T:"tO5HCqlX"mLOK(DDiIH6Djin-7TQ/Ge4J
%S#Q@FZFnH(JSUu8=c?b?"Zt\iVF/WSTf/L43<#HG+4-,:Q2P0$=@p$8`n=(Qgl6$"HV?b7Bi;@jV,KtSK#4n;IB(DGS1:7_qKoS]
%TFQRQ[7ZKI/X6e9j.7=!!j;dL_*ITEj<X9.j:/Apm=C0ZXe&VsKPNCPGsa]J;:HLoL_Xn`l^$hG0nrE.]8$koQ]Ni.8#W\f&:O3a
%;rQqu4%aXRKnX+RK_tV^q]3c9-$_\#44DuKnE=`.@3sY_^pV@mTcgP<GV3&ISm]uR8LSC4eqZJ9-90+3X>B('Z,45RaRVE%fua"Y
%+h>b@/gD5g2cLh,cjk4m?k<HFY9.3GNeYP'rSHdcn-#D951LC;RSe3Fs(^MQ\Bf:u8D/Ldl0:C7d&StCFqAJIf2#/lMg8SL=m,I`
%B$Z>&7X69MW9Y5^_:/>bAkZ@;S_'U$b<AsQ0=K`:*MuR8Nqr<YacMgq<g2FpTk2LgLo>)nP8&F4+6Nu"fgZ@ol,Y&8!U+.T&XJ$;
%S-<!Q;buk"ohnh[(<5_Dns$HnjJg3uoQ22RCRHQ>fdGZJ_jLqg'E(>gnI[#pk,gs_;I>r9;d?L`65p;bP=R<S/-"tG4H<+hSo7\)
%i=scZhFj#ha+JZ0Ph:(,4QP&,'(T/D14'm@mnoo#WuRhBEMuC(G/Djs'$'H=m5q1<[j$2a71ZsI;NKt4aYjN@nKei"OiGZ64bToH
%V^6H\i:d4==U8i#OjCq'V[p8rdKPH!g0oo1W%GDGXo[.V/+S94\R(_LXnde&nGNUaa2n_+T[_pfb+`"%Rb^i?$%&tRX]3FIP`b,5
%"[cjFS8M,j*&JnHgRkT"Q&A=q,,9tOO7Nr67AOitc-mWcg>oi-=fhcp]=]b?csWDr?-X#t$KOk*=jCG5D:ILK+;qloqZbH+W$d6/
%8&@l,,=<Z.9t=S#f$rdt(%mBOW2Q&:#+oHp>]S9UO3VXeZ,#\)&L>9eb,'cH<(`3^9O$JIN#`6*@7cSs:6;).3njZgT]:`BO+")V
%8*4Z5Bf8#6qU'F>2fa;fhM'EPO+(E#TD/'SJ":@Kq9g8lnTDH'oYcJn'!C7FQGc$-KopaLai.3VrGnkU:kh[,E)9\DoJL^ci5ODq
%YqOe,?AMml"<NRV\TWtJcY>P]]tne.TX$u[7bCI`^CuWL.;h=*:CR!o%m\fgK;5]3>j=$gLU*91Cp4AH9?YgZ-7nrF6a7e%X:<cj
%<R<O@Mca`2NoN!MoPt10Yp1h`)UbE%f?aZRs($(!-fNHmPaX4E[2c7[YQ?SSn)^R2ktQht/.CItXrA&^!OefHH#R76on*NaZn38O
%#BqkhgDs$_^0`7f9;Wc3\)GN+i7+u0pCN:u3W/d5',8(%$sh9qF4:^<%C2F2%Osie"*"F7:e^3m$IJ8:/1sYVX10C"e<j7k0_lRP
%>p]>;4sW"h"`M\UD)@rB!G:E6YE;FUU_Q1-oq1Rpolri+`2*hdWrqD3J"OTd7'`ZqKJ?joE-KR/>_+*![_!-pDu!q<YEu&ZSCm4h
%g7sj7Fr:23I0#XALJ-Ju)6h\U"X^pj00!AQaH&u4'SJ0h3LS-8KC\uG2bk!qjCSp9ms/rXap3NuAW6sGk9_FGQL[1hat+5"l;knA
%Da4r,CD3\VK/7n\jYNj?lU+!'.4fIUr%;_1A3\.sGoc9tddaQF3GS,mmraGjE(]d'1Q^gsRRg.808`aC?#U,/3(Fs;kI@4hXdFeN
%JO5VrPu6\G*!rXu#K(gH1_Bf0Y+;X98YqNL*jR+olB8tWk8HUQ.jl;2R!!5N"I#;s$bmn]iT0jJ7'<]T[uqha5\o/*3$>OWhG]&u
%#,`[r^=HEo2a&V6J63XP:1o&#Gm?<$g+hrIV*J']B=^$T8M#54-7JO^7W_eq0%@fPqS??6Z)+/d*Z,)Qil<'@k5eSUMdV[2A0t<t
%`(-:67onVk\D<gjCRCi/!&ao-Y#W50]RhA2Lplc\^PZOA\Z&-K5(:Fdo!+DF,^lp\r3F&FQ=-m8mLH[8(all'UF6Wrph?"6h-WK5
%JC_**J6JKK),g&mS7sN.SWja2J$3G>W@kQGP%fk6LjS+WYJ.7aAqEao99!`oVpa1<"]C^N+sI<n>ZfkT0CZ@nb(S+ja+D=0>oP=4
%:mJi*]oPXnp/k>qVV]?22X[4J7m`ro:t?-1TkW>&;'=K@^i2W+#$cB)?mnDnTs&]_<D*QA"H8)`kjZ9N(^Iof3N?#RfN3+7S?0XV
%V0.3h,6;WKRpm"s;WVlBF^\_ogr$W:<B/hR8Tm4d$8q(`g3+&C@,iVCIO;Ouh?o=^!,<EMAIL>$=&'L4se\_+(E_],Ljqa
%#dK_9[c3a\n_".JYFsn-ML!P0@4TK7W&`iYgkm-QbIj]b_-uh+Qk5\Z#YS?Y4+YrIRTuaYfMeH`p./N1r-0?h6ZtRcD$XQmWXUWJ
%]Nef,4?>G?qnT"hJJ%5L9M3%AOL(1t_5)oE`?f'D<R"7M':`!5'6b\]+VI8o%@U!qKM3(+6>lTM"luHOOC&@W?-('Zqc+Ntk9*<7
%#eNo/gPER>ScHGHEVVdio)lq?*#+o;;h5OF"g9/<U[_RRFp0%7c(Eu9A0,dg9M`Wj#$+R+.Bs>@Q\%YBB<q\IZ_Bf'ha(s@#ig%P
%[1:U,Ib0](s'?gTIW-'1$Rd3e0V#H1"^4KK3:mHTB9Nb_h\<!DITnXYdPTP=(=;"ZjmDGbR1-@N(O/[hYGFPljWpW,n=WLTop%k-
%i_8Ehn8VBj@MA=4kQSGFdYAitFe"`Y@Io+3NJ$>nOrWkag)+rfMVU(1/^J_^&bfGShW4&h6;W"O<$p&,jWJr[*$sII[fKlEJoWhL
%'0a$#eX1#a,Mq=_3JM?C\M##S^/AV?UeV"NIF[Z^EGsLh9Y^kff7rWL`*7QpF2c\\"@-WuY(aFQD9(&-XjJ547FR,),uZ@Z&J^FB
%?Uf&aYW$lV\@H)',F[tIOPKbr2F1./@8!OH5^M0N1sQi(\*Kc!j#]tpD02.sFbJ%>ZTHsaf6cV++4s-(]rp9qB,<]\&@>Z//T)'G
%Dc#'s<".p/17+%@5f52dPTd<\Z4J&Gp/:W\SZi`SQ-0N)rWXt83*Bppr>$`k"#@q:PhV'F!NO(U1kGl2h%i#1We_UIEC!:sbo+>c
%!&4<(JbYO'Os3<Z]ua(->O4&*C#5/Gd=f<U:P3X?rBdk"K"'JW\m_ZCUZhTpMP9.J:jT,Eh'uIG[OYKc>8S-`L8CFN,_Z"dTfC-"
%AeGq:m=*"Re-Qh$I?Gu4XEe[8n'G-l+`A)6,_p4s3OV]@6)7'1XKdh!;SQ6p'#pFmNfd]rF$l9b,PMX_OQ!LX`,k>_ki$jN.\6To
%mZJ6-3@XeXIB+2G80$p2r'=a@R?5"m$1DkF"@3-_-muIe:<\4i%+WKdgbj6Ok/cr"aEpfl,9!q2m/c"B88SRZp0dk:qr&ph#g@BK
%MrAFP1m<rT/EAR[:ucfA>FdI>)4liKr773EXFRs-!Neqt>9BC->`72r$8B9L3Yefh.MmG!/7Rk67eA:B!P3)%`2jp"<UIc4\59(d
%+p_h7=j[ZLTSkM;@o7/3;fB?'rf1N=`60$K%5V!NW8I;^dlM:L"0;g)&)F,4/1t0hB[DdgR,?VXF2W$1!9C+CFo+rh\a62"Qu?*J
%,(L=.a9r^'B05:mqNC.UFOA>CKYg:U!h[1T>Dj9J7-l,-2!`9F_0TQU(;Zrs'_WV@\1\PQ5,@$=:P<][:sY)^0`o-%gRr.F-uB<9
%Q,?Nj*.FB6VL<lfE<(OBA<P3YoI/X8_iul>2lK($0/(a"+Zsr+G$lM7N%L_0X,oF<X:\C(>A)rEB3?beP4_4IjoP&UN4>a:h2Ksa
%jJ>7!_$'a'Yt'IK",;CuBm#9JFdMS*co"r?6egi<KjS\"6=J(?1IK!kJ>R+?.Sp0$C+/ZX+Al"l+hB^G6WRS1&H^N'5X$YVh*Q4(
%.7goLqhZHdD@.C2oltm(_<%faM^0k)4,GKHo)eZtT5-a6Y?>Pf%`6qXnOV`%GWGrnm>'`V8p-C^-A'3IYD0O$oRQmV`fash/"a!$
%3FJJ5\8Q@Tj-h-b<^m'X]]Du_]=E$AiD-M@ZsPU7[Z>)c6*q78JjVn`.:rWI;qKmR#RG+D_ZP59b8+F*=a!+l'D.smI](W[_SDur
%)-ZMdR>loj+lkRuLfI>sqJ6MqM1t%\'=@2#1U1X1rGdcm<E6>D(ROL_1)am_*1<?i+T7#C<&klni98@3!LM52M'Q5<FIWr`Ead`c
%^aDARL%><0EXOBUM-&\t'k#XiI*g3R,WFp[Ttk:X&S1U<IB$n'#<EE^QB5/]L4S&MJ3>5GI)n_nArE1kbSeimOlq*cL!uUj-5&GI
%SEiEG8@cigc:K"YTK0tFpC&uSH/HJf-7dPtGXsiRbeA^#a/L49;\#fe6m]oRX9_Hap:^0URn"&a'sV%+Ad>D%!NV>FFS-Y0$\Hg$
%!3!702bWJB@(c4>i6]$ob=5kG#^WJ"'Kid5c(Ge/TjNnjI.5i^c=UDC(3YI4+DeNb2la,9QfT!BZPa;h<dLA:aUGn$*h#(mBs]lV
%R,Z&%5-\S4.6>c[geOOKBd@9X9e:-G&JYrF2tt-lnk`KipolX>J%Qr;SaEl)CnLR',iLJ=rWH4Z?&P@.OP.o`@p"Fq:qBhKgjDNQ
%Z,'N8!1ge-^1*?W[F6'0XVeoL/7g@8GlQ<<)ngHd-j"ic\Lr!r9dL.pS<\_,!b^$3Ga^j9ZatI"g<h*L3\&1Y*"rYIr"WCAFgc!,
%[^q-]`-<GZ:!d2N[Kj9?JRd3T#gHKfa91[f&3g2QGi>K#(rI1dI+]R?htp8A2lMd8A_I1t_7Bo)f#-7Dltq?9><eBMLnlD@7VBM`
%qU[V;,+8,(,r-"L`t;_jSc+XqkhYpibU;%q*9qIjK]_kg0(g$7YOGX0UnA!_>F#u">XI">LK2s?<Aqm,[uG0so>>1-dnQ%mB6?Lc
%/2Y?beL^-4J?,]Ab$ao^aDPm`m384`-%k>rAEe,CQl[J@5L^:LSBDgjPpa_meN`6n=!sk!$VY2!3#%KU)9)qH$Wa2eb%.KVD^>la
%2koGin#iF?RYdia2J&n;bJ@ctTtt.8]!?/g+L%&)Qm/XdRL+r&]>8c_+\Y>P8<d_u?ikoT5Slb)b&]g'Ku)fe3^AL`CHJAh<%aF9
%Ts7D`$@A_]iYlRQJP(^$)%X?JP'0>oqZ+U*4Ml81-KTpO@MURtZV/l9+ug.bm/[K7HAZuA1%##emTkX&2TI1P=6B2nWhkAf%Ja10
%+4C]">BllNF=O!dLFlE;E$Kl84=rjDKWVNaeHV.13jYae+k2F0H]#*?0Sm,j!J0"GeOL[[:UeO$ku:"VKpp*eda')`X9&+P*t+6;
%U/1^5nsO(b4%Gk7O:S9HNia]+#spsOl,[9nLAg)10o;aWSqN:Y%0pHda'54]Lp<f9g*9&mr=*l2%Ln]En;:Bg@L#"^(U0KWP0Jr^
%A\2Wa5[G!:lSVkD'OWr@6hF&K(0YEh`Dp^c8&6ddca=n=m1CHHD8o%1IPsUq[$MU*@nTD!iQV#?"K0QJ,]S.rgB:?n>qULVB-D!\
%iV!G6MY#@.666rUojKF-l#j[_2<kBBH0\kC>+D)i3.eh.'Xfm$6n8:rVFIuU]%J"KB/EV4/fabAjFP:6L'!<_]%hD-+"&Y$CKj3?
%.C.M9%e]*I@2gMGnk=gKK?_U7@`XUi'_u:3'CZ^tIG_1cUChQeQ1>c(@">6<f&VY4a:nWT_o`KM4Q!`Up1Q(e:bPA.m58]uWji2K
%!PHAldB9itOAf.@CRDAZiAVUO0'T+L]58IMDr<l"dO^]jLUHL4W]#54d=Pq^>>0s^62(C/<_#/1#BLj-?=JfoVq6m;/a@"A'9[#3
%NA.]FMsQY0Qn-$(r#GDu<;:;C93&A]#j[Z*K$$VA=JE\\[g0(jMCYB3#m9OghQu_Yo+]+#4R==M+uGOe#q3n^@hFsJ9'@E0`&:OA
%A<KqqQ%@j-4:U2uoIs/O'S3'?f3_^U'f_eWb&%aKBpklC1@<VNQ1@'?hiXPVkRK\BbPOtP]r1h_U'q*.;K:e!=OgT8^]YYn$?H`I
%)]irCT-;'8CNXd7K$f\Hm$=gR:HXEgmk@KS(jhP@D84X*n*2jRqF',YEbZE3AJDb.b;R[.TNlaf2%)`JO]k;@E7ml^GNUo",HNL,
%Kc../7#_&OOF,@#Z7IFu4=->uD>!"Z?1#ue*::+AM]GIdgatSdP)WGX')d#=LkgBB&KY2\L$fePOGB)94\&RN4mKj(U9F)>eH^b.
%S$mk,@fRA.M".g(RA6M?E2lBRW)9Pr*hq"R'?r#9B&)FFje:u_'=c[K1s?^,+fL$5eKS08G(&)d-qc$5&!.3F_`hTY9'ggV*/.N9
%bE*\dr<8rMVkBTO_btW!`pK3fj::_S<PA'G2sq;\Z[P(\kI=l-;gJr&E&mu+OB%EsUYN73!MR^L<K2l`1=o=$LcrVg"1M6pbeQj9
%./&r!!(A%OXoZ4RJY8917NdOU2GcNbjLL]#U)\LM\fkmNiuj5XC,,rdq[6iZbj0"!Z(/I8/hIt`6)54X)keT)a.PP[qdB0hB"2`$
%',Fs[H;k\U'V$/Nm_Z$Hrcg"77stIh%n=!?`.khRoi#/PH&:%fRu-78928\h/5u(]eND5]4^&=TGM=,dN)Qu3UA<<B1bhU"e/\'b
%\<eGY2g/0bn@C261]q,43U^LB;Gk6.DQIp(UX5tYTf8_q/iP]"etZ>uhFk]9Cap,A*"GYkfb?mOqr5-M,ql^C;7OC*cPLDmcJ'+:
%7e,hAGQVhcG0in'7;K1RZ<M@7C]?kc'F)\K;g3K$$gV+eiJO-C/Y&sBN-sNu[<7S^qR`-U4fMYN7q3$PT4.=!%uCV!ML^CmWs)!>
%m>(=R&n?PTEF^$I>Q$1!<W\pr'JiosA1ScE$0$!IB@M]&-o;T(Kk&2Q701PmK`t[24j\R9Y))kDWLVldm,jC7!()bPPS6X@ff4id
%DD!<(W_(GZl2#(Kc$\Hk'87$UTI,5;4d!^r,T-d6,=`5K[B^@`!n4[&P)\0:d_DA\ObtPo?<#m-$24b?!P]p&M#kO6ag\Hfd/*lm
%V7"8>'8j^Fk"2Z/_Z@$b_:g-#BUUr7Dk3R)@TF(>0Mbbu(o#JQoNhpGPbJD=&o/K%h*rN_F<iWGkPKdqP![jVQ+R#"A%"#c(4sl=
%5hnXsp]6(!<slrmB'E+jG9VXTo)l(6fA<"7V3lsNrYZhG7<5[-gJ+8lq5;=D-;.k?Z#c),F44*6X5(iEdSARol]<5`-a=&3qt[ae
%dlc#1AK_)@BZR/=N\^brMl\^g),\("o)jh^*J@EQOQMMI>s]?/g8-TGrOTA!ZaZ/j[itLCf$t<=X'&iBebftdOOmgcn3c/Y2K71H
%$t*TR'dPGD5;FDAFbukR[,qu6l?`j/9sp81fKrou;lp1]!4e.=%sST4YN2u<!$iG$$3EB=6pQS!Vr4n=ime+[?nLnilsgD@!Le<f
%(3;`1DPsE-"q`*rec;lr=;/1K_PX>V#h9@h_@0oM1P4!PKeU+"oP#]M0Y+[/Xg:I]K;m?A!?AVgSBFgGo\S\t/288i!nP5]J&W>X
%,"X@CVM.??E.RJ!Jel$V,e4kGa`+9"7b]8d-ui("'mhM#riehUGllGF;LG'AJY``7,nJCnR,>-o=n[<boh4(>o-V(K"!JYa@5S7)
%V<AD%R/8&$c-RJkl3t<c4.BM""IKV0XTli+r6LiI&(u2>+JC>gQn!6rpcd5&EOGldOaoA=XAmq?<'oD;6gmr:#>Mk`%hEuJAFWQX
%Mqr!<1HFDpRQ7=+!.K(d`BMg8b+qaI?/^7"5:cW).)YM(3gujc$(C^a>Crg\h'Yqc#WkZ#"FnB2P"7'+OG7nI5$%+lCpt)(XEcd]
%'f^rd+(iB&%-oI,eH/._[K1i.fMl1XJ@?Q,4&j]eXTfI@&t^/X_OY:Y>?=7eckEtaLWr(GNU`3XTQ$)gG7(e&JAR^f(8/^3XW,[O
%1R"ufnYR4(6(!*;LTn.<q.8>qj/.W9:>crN^-)^HBKOh%*"^Ps%O0LO,t5j/!c7eNMe_*CrS2)MPV%RkG@P`eS:7U^P2%<i1VrU9
%m!=gd=Un(,N)I.55St^ibHP@\%$jJa>5J@^.]PU5PQo`>?VS0F7kTR.+fa2aD/*i^JeEfgE*8ZHF)Vl(a#EP662Ii]_?@rAUk1Tp
%=Hrg*J*q@(J3m.'*oW,#M-H4uf$9Bi3d0=,aCEcW'k(I9/O#)r1X^%$-7`#o4I<<?aEkFiAWmm\E4a%8'+TEF,?F,K1B^dk;DUX4
%J0B'TFlt`LO+>[8Q&fG0]qFO9et;:.Pb9@aYJERn0l_N=f*rdN%j94q4X>EDTi0\dBij4^Z7BC"KK]EqR,K/^ed<a*<4])DP*:fM
%"ZoN%-t&484q&C+`-+OUQ>TbE-Y!L4KE/O0.%1utqJ%F8W8/K@6r]gldCsa?$AB#eY"#S$VEkf0=S1Ja@o*bp_pjqh""<.t^(5C;
%`31md\PU/X*5:XW0gO'2Z+)%0^e,D_[EB&sf.sk2:gR)?1a=1__34,AA,2[i0M0%rPUJ$3Nkdso!"6_)dt+('mQ_iAZXP%MMOY"P
%6_qUehK]-<2L3'%/W)\%J9kBj4ki@o.!DgPCD)MT"@?!i4[gW3PK"c0W`d^j7-5kL*I)f`Jb;XZldJ;m[b9L:gY`"NDnc+nT76_;
%^]*Jg8_3pbs8DobIf.7$qr*V9p%rsjrV^s/^O-"8msb'"GCTAIgHY]%%mPNaq>N/Sps"m"q"EMcs63<D"EFA4%6sd1n,E+.g"cE"
%,sX5W8cJ\3^Fmk4ijuAnarf-rIo*B35Q)Dcq-S7!5qi8thuE?eq:e+rrSnuLpnuFL:AqXjrcKg`.rjr?!Z+nm2!04>V9(or*"T_m
%7&>ZppMj7HYXd3:TN73OK/Th=VQQjdJ_Cj4$A5=XeR$L?-`&YCR(p_Dn.sqNB0ql;]YA8NJt@Z_6sb3K\,I\qUp$2bH5C4mg8,MW
%j>ALAH.<X)NF&\,e0HXZV6(*P(^^SAXL%0aFk>pL/$qD_D$;Nr&`s0WV;reoa,2)QA`X1J68j^Y=&Zr-`0Zj_OM/E!(@`b)'c7;u
%TplB4AK0RKo'NsQ-HK+"H\$5u"A_DsQ4W*cWhF867c[1l^mi"K@g$hR0`hNj?/2*/+0WO3Pa2,KVUTQ,6)*T64Ead=b!An\Tj/a6
%>Ifk3^#<?oEQAV5XtgOQ]*7nB2jI?.QrJ),F.M8"n[&R5[3&Ij_A#kqGQZohL"m)[H^LhHQ=,3Ij0lEp^E+O)Lp%Y]=^b5#3In>P
%Oc(CmX4)T4p;*:I@gM<f&/k"TaPE5@R7V6Nlo___L6,OV$/MHjf\@M:@)s(+K,q+:1rrYHO.ObUVF=/6)i$0>S$j"XrB(0pAPrgW
%hN#pM7h.kRg\_=B\^(P%IJD'(`<aq6"2jl6a&dtVII<]d'55a4%Rc<$M'2s9Ik,kd#ukX\?l_uU6CFc)p/^1gZ[4e$Ri9CAWEpKi
%7j)]tZeF+@gt?"W?rNG\K8HZj=6uf]I5=H%Tm`iC('EeIHq3O#j4ZEL+6!b6$:[=&ZntV3Oeb:"RL-6O*FV=*'4X:iGG;@$"/U0l
%K\))H*fc1_VSc?H$+D6c%L0(d#s77%N*HCcPO]]76l/Ge@CgGMF%"\0;E\:ho^HCk(!]dMr!d@q%%1rbbI/0Q#\r%2C<7Ga4$4)h
%`%J#9IS_dVklqnXgFp]TPT^qGYg"Xa*oPnNT1cus^_W$X!'&8GGB(_:&0Y,pam?Z>k+M$*j]ToX0AIRA^[XX!O'CEqAa?5:Y'jHK
%?`99QF5c1^*WM6\_<VKN[2p2kd_m'-hkQ6RWQ/p"mmZ#tG5Bb.R:mg))6Wl;#XLBSDW&D@Yh07KKa4EEdDH7_7HRJR;PpJU@UsHi
%g`V>KbaiQ6#n#9>&3#N)(;cMO4ci+JUeZi!o]Cq"9nWhY9$eiigcXqd;8OrRiOun2=2+WD50<Sobgf^CmH4G`iq[u@dRN>kgYjtp
%6LR:5k-gd&HjGY"Aq17M.85=5.G\tLbMdYOlkP^Y*8T.GBWXXi$4BF^A-bW:_P5e;$edAM.<b`a2;*T(cYgd6->kkFI\Pe"%^HK%
%L!hZB.Wm0U(l*C)]#lTT(=7^af&^0V&.YA)<;/c>]pTVd!.QGUXS7U^TgC?[=KFSH7n5tDgE>>dP9k=MR?Z>;aBBB._X>-+m3Fcm
%U;]`^%&YRST[-Wt",=L`efh,Nat:-jNkrDd>61_L86!b]ER+7n69e`,Hut8Hjie&G$)2#gbqAfi`no+N\m<!1PXW/:0*k\ilC73>
%Nsn'8hKfRVMTKZ8],WRDjrqK+$0NHISnqC=h,Y5^#IL;1BHbl/;Vbjrblt<Aq>_?hGmcf1`2j6YVWMb;Y4e5.!bUQhA>NA_eMW.!
%Aj)CGUp4<I1$H]11ThQJ&&LmJjT1MP^r.BChZ0?&eRBcO?d&YfUC8#rP#P5a)(cTEE.CthU-6<0fbBo-N[0'hm9lb/#<[O^"%lIQ
%ED3/oG[XO&P/p[+b'M\$f8%N78]5c`J1H,%ecD`S9b\HZC_)ioP5'g'!$a%Qd,E?5c't`?R^u0(*u3c]!qB(5S\.t?PeMkVZ@l8C
%!S)I@+btZU@&YH78(d)l,D'Tsp>e=0Qs&i"-<.[c5^BR8Oq-s0PhMc7CJ0r'.sOMqp"]hEVVk`(CJd?A)2cbVGFhc"&)l*FUZ$nI
%@+EJPCLA.C.$7QmZ0_l;9.77lZ*fqR?PMLU6BMq'Nl)uQW2V?)a_;#\^'PtIG9Q<6=Xp(OLEdM%;<7VAjf,o1E10Jtb7J"HCiM)3
%1@-]/;R_P@"LZBe=+Hp,<b[?$Ha:(F,ES:)`!B4'aUror=Ks=oJ>q[I(=$GCgh-]tYJZ1]6^!o3k%V>KIXp2.g8<hh$N("e9dEoE
%T@Ch6[RW/oHc8mC*ot4[+/VtWY9e8d?5k"FNnX]!Y?OGo!3_AsSsQl],Q\AXB&Uh2[FHU(?%-6?OH+Xjd5?s7U(NaOk&8r&fa<)a
%N(>bT3t_S'l7f7PbBJuB+r!/S!_`#ZRiS%nqU91FpAj(8e?>(6I@,N(mG4FMPh6VNc*CL[Wk5::@h2D2BiZX..CLEuRjh=+1n^QE
%d#:@u=gr91i)$0qKRqMB'i;E@:[?TiK;`7g\b3Qe/jQ7]6NM;&-"@n6)%(SM_[k7=lY+ibN/;A+d1qe\.H@*+?E>V3=(h$Hc1Z[.
%Qm=7r"[(Nml,VLlAHrW1nI=.eRFlKIk4X>TPYOJ1^,6qE,r?4%1Td$!:tJ@Nm_o+o1i!3]e)L7p:#72_/hEia^tT1?1%-A-W$]L6
%_-1+Hid9&KiM0As#X\c>^4,<!`EhPl(j`I#j\LL`8%1UDhLju!QBM$-@HF*G9itE(bEi%aao32IjM#/c)E-/GW")i/(<rC4T&M/o
%?'l4^/ttYa4ckT'Pts.9&QCe7@MkH$S1Apil:5IM0GA?6dkeO@T8)^;RK;oHAq_ieL^*9q1D.YoJS[IJ1^$Ql0TOZl,/n/kO+:N"
%3-El4,qXf:p:<^1o/!t`d`Ba\#K6Q$Hc&7Pih#@2O9sn6nROflVXQ$dQeKC<&;eYD,Lb?=k;=02DYIF`)nq6B^S*aAcoT%kfeWIk
%AY>H&PWRHS$R1(L26k0%`M`"d+VZ7Emnh5i^h/[K;o"Zajun<qR#"i2nIAefI@4KXYBXHI/t_qm;jBU/na6`MFsiPN)=:<J:ND3`
%.=#]!b:ZimX"JW[1jW:aD93O.#)K?LrfnsA&LH*3%>d^P=bdQ]b9b*++-%Vl!ZrurEG3R"($ZrCWRh^LEUW=a#hbl@ee\fjhGG)*
%<i-&nG3Y?.]04mq,<E;DmGcpLMP?3GNci:nTh5@1F:K,nZ:$\Q"%-mp./CApZ&>bfEsX_1%D0i[Z-<M+YQD_#]6?`WM^C7n4(^B`
%)+^a0&BkA>MO9t&!5q9\lS&n.6Q6CJCT4]Wb!U2\;?[5ol*3@(N<YWdYL3YpN/?;"[D?aCO(4ptk-EWV_BZFd?:=fUj_u!i`b!1o
%Y6.e+_G*]*CuS`P<J+YAn:hoXoO@<s1c:Q7*O8,,AJ0RKZ-K7>$09##i7nJ%i%1f8H_kD;N+FDmQYh:OHZiJHZ_H]DW_)u%#H2<h
%9X\:<EMAIL>`?$s'K"2^B1a\2qbD,kAUXnprgHk6=4`H&<4A6OYbV7@71A\>=U:>VDZnUMD%GY)(E^k4+rt*.3:RMRs#DqkX
%NklqS[j#F0K!RlrB`!<HhBG-R=+J5X8tqDCV%=mD/2Aj;[T7Xh#pfq6HCM]nOZ<R&.D<?W%3SFIjk&kGpmFKTA17os-($FJ!f)k2
%ZQ?ra'pNp3:G_DY#gJ5]R@Y\2(p3Y_EGT8Q#V!egnh/ui#"$%Y]G),l/e9^)`"oP<Pi2+#[ac45,^om768\u=d#^[QA=DYGo6#\T
%+]l9)aR.eZ\?i1`frC*t:nAI.^l@LXWlt`CUGI[qY3H=373t@_ne5&+44"Ws9TdNM*H'OheN6VYWM\0MhQm%g/"\BtMr]8TgT4+O
%'XWK#/+"'8!kf!KFUDOY#G\`%Eu=U\Y:>tBH?KY#>X]j1D9f;&gucg)T,<q]i_1T^fjucM*$0:YN"_I#hj[Bkm)H(>]S;VLPjdEi
%$JBDV3ZA?u=`FF_>Phh3'sg4]C^$p0F/msuhqpDk?/<%HF7AGQ*RrFrZR.sc%E$V.cR^DB1dPtQ82K2hPbW1)42=4sZ(08]9;l14
%,83?He:Z3<YQ7L.Fb)3g7Y&%"jAVl(%?J^[+H%!G;1!JBT@0]GCKeP5b-@q'&<t;>lm"GVpP!^q=X3I6kW$[c*0`$LBO3RiPRFDI
%lZA;NlGRmBM4-`#C>l&+FiDK^V6Z=1?gNE24Mj&dKLjR7=7KkZ_!Q6K!-q8)jifX3_meFW]U:]tSd'YT5:Wi%\sIsm-'E!13+td]
%oj@rZIh6h9Z2LmO<:^QJk[>)XngSIE$TpSm$o-ENJZ:K]5b]rC7_hT9m4.BUWpQ&kB*&(PgdSo..LnWK^)i_/@*@-oM;CHsPSF:7
%cBpp40J/cc<q,a2e6R^G'1urT(fXr]ZaTV'3E_$Xh;U/NOh,:pSn]t^PM!BoQu_e'i/i_9\4I.sB42t<#boee/5"DXZ<+5_A/D'2
%g+UHAXN``,jcQ;<cF+PDQtBafYW3+f(AW6//ttm@WDn*R&l(!O3`Wk&m=4*o$[%fXej.IlX^ej)OL/4,J);qC`4%It.c(_J-)tdE
%U#b]L&0\T2)E/DVYgnjn(W*>l:TnN_4IhAB="!DelXe$*L<s!P>#[ffRabfLK<dgfnoFRN_0s_?To4NFm4$gaN.[EuTpNm0p<1)e
%TTclU:s0>hBI(L7;Q*dqoEo)[3^\8[3[a02I6ha=`EJ_E2pV=Hhdf=6WC1HZ"$nAK*`.qq[OV?5qQge!ToI-AWZ^.Lg:`0UY\;L*
%fDW,(n@3$'<nOg8\X(Z0_6CqDAdo%IgB$.M7``3rSba13F\rb9Me-S+CN\G'm())**I;;<9#c%9!Q#mr_H@W7?qtoS`_qMg3p@KC
%4WI:!GP6gZ<Wi.SP>a"m>TK7L2j6]@f#Kq+0QDuBW+aJr:q@/2lE#OUC8W(-g[GQ2e3M1jD3h&V+:nc^2'TmaI=fS5:B8!IJB_[8
%q!]Z3!t;4dZG$b8;K]su"."^nqQF#R`-<IobC-t+`D!%]aJWm<[?ZL=;BAFUCqX^K@<4q#]G7X>2[laFK,1ks/Y18Q$k$aDfWR_9
%W9;fY-9FJSQ+7Bu\&q))[0FR1_0jA!QJ-aZrSZ00H`Us[RD"L"b43"2A#u*\"r!:.YGL_C[V-\:"Lap0bqp!_NANuZ'$_ma##\+L
%.4>niY\+j^gAnSXcK8\r<fZ\;82d2:5$_Z5o(/&9:e[#^SqosN1Ja&/MBN<.'Z&-MK=B=toO@#8<#_Fck)-KPZDQ+H`dQ69e=GG'
%Q3g*AHRDtDZ%VZ,;H(r<E)b7n'aF=!o8lY&KHa3Y;,*@.%$k.Y@"=]n7/7J$]'k/JMZLnqV-p4?eEBRAS1T<20TUhW(,SImGomUj
%pBWAIHK(02m=sbHJ'pW!4aIE3+a0$n8=krjU>U"X=A#bMN/aYCc+50$eWKsu<\2))#!$C%FTCgO@]XbD&SfGgiBV!?INppcF"P59
%=-"sS,QoU@ii7K8Ml$\IJMtPVW<BSqGf"=G>7dhOZHKk.H]Uccf\?B!C(Gr8+J5R>2HR1%M_1>h(bps7<6nGsWGifEg2:KY*Uq!.
%\.&k",+$;A[W-PLT_[8o!mA<hpHik'#TKS0"!L]&CaPa[ZQ3>L$>V]/L'N',#s7M$24Rq+Ni$kbmlT\Srb*I!A=Npg@d06s?-="q
%3dg4`keH"mT2HM;Z[Of.r?ZRUVCs1E)Wun4U:eidE%h@PF?(\&7B1IZ]<df6!2&i1mrCk!q(MojAYr'7(LK8&5hqB9TR8pZ.$3(*
%-Jm_RNb&r72:c;!qSimAe=6,H%e[tr]43IL<fIGiV$g8L.4TiO%`3j-Fi"T-QQK$g/#p)!.B,\]f%*PWfRJ!N9gT=k0qW]D(+<(F
%Qo[CVH8>?WS!a!9,H:[>REZ[L\`ZgACN+jMUj%@B>.4esp.7><X^j2HTD/H7,]l<`4)$_5+(eVDK"u>S1cQ)t3Kcq0jEi\]Wt5[T
%_PG$YU+<j"[!&4P0T9JErS^;iN55\?I&#!UhQ\uu&YT891(ZdLp-"<k_KY7ic%CY/?j7O+lF=b&"o\?_?.iUqRUV#Rl\$lm5pM6a
%S2tX4mUh>Z\mdTC:HdiGMfp@"IHO_(f1RX9o^*ue@sa$,R1W2p$o!=U6_Ij)g<>BcdnM+B)eF?7oXMI<l9#+^B*pD,T769YFN.*o
%C-;h/\0l%VjN<m7[^4BAO8B4P2p8Q+1HGi0a`.;?oC,uU!1lrk]o1GS[Z1I0-0S-EDiD;aV&:/(\GJ1)N3*ATGrU)"h@S%^.<&@s
%Yd)jf>-#OQJeY:LID\BPnILGO>8S>Ja&GbDg54?$K5nGWq"gn4+Z:2P.?>^h((lZAiE!n@e4giJ>Wtpq?d%X$7>J]L<?;MN]*jL'
%34WC\fDZ)3g@9KmULU:(g#[i+Ws9XqmZn!m(0sK;#)Ud"h5W&ZSO5XULlLuA<3fG?nc00b6b&@R#&$Usi/90)R*8$VClN4UZ$c\Y
%Q2hVb8;0gT'N*DoCUK8C&/=PXVa?`nBn/0O.D<HHCl$"dRS8p%DmD;&L;G4`8^e3$N+NatU@P's4\)Z0KA4):Wu)%]r#S1H(V^ej
%8PU&T'4gc/6LVji81_iVQqV)+f%Nk%0NrU,KO#=7Kf>g1OiYA=k:CYA@Mci712#\kA6.SNI\#6-!6B/*='Lken)E^ccl8j+UkANb
%d<]\h5Qi^6T%'VWOJ3G3(fcQ+ZN?!o#!4;,Us[gE["bt7!)lBo6s3iLP7(H_6.rKo&URQ!+>Jha<,BKB(W&+]4>"1m7<VZicr(;8
%.\c=Mj)>lNfIanmN(00@[A$tZ0D?VuO$m8tB_Of]"Z:Ne8QKqs&0MM2m@biO,Pm,DRn40Z5(Slj]$?#K&QZ9L?.1%B5^^u]eQ,1`
%-hA>c\o++F8Yj';Gt;JTS#6Pu5rEThVn,o]JhhCn_7I)Cb0An*!0H@f6j_Ru`Npg9dJ>8L!$PK/4MZu+E!WmF/_:\mD^;RAMH(ao
%$Y?rsFs>aYGt08#ZI+(*NbQ#n*$D'b[ZfW.HE#_k:a*TV2a[B;Th[u\06L10,]f:srDAL3Z-:7Z:GB;JNTsCug`R+LS(*E1PK6oQ
%FT=M),M8d=Y7m-B3EBPpB`k'68PGBTPDjVX$+H6@*T1-3O=M*uTGmS9$=#8Nj5.2:EBG]Ye3[p4L5ulh3]<S@\\^#jp1eIHMToC$
%&TMut'$f*P#TZ'[B_el@UNd]B@hPTI$Z"_f("-sK6K!0Yg.Cc_oN#(U/Behi#PI4r:R57Hcs8,j8AF5@Rt@,CEH''i;ffYmNOHuD
%A\(c^#p>Z`=2U-8==/PJM?t#WK[AOr_-qUMb!OCn4)?mrU=jIG>b//$GnBQQL$FF#;PicC:PWQO_tSfK87tSJ6#`g?!tQ4-@G,t\
%R##4g2mt)$EPPMcK:(BG;S&^rFZSo7=G]ZV\Qg,?0o.W;L_M#IQ?N.6dcF>>SeI-W?<R&\B-W5DPFoL`>hDu$G])5q!LaKp2N-gf
%@'LDci^%R5-5a*`?DXG2M]UW2Lif_2+?l7Y8%JEf,Tb>;>B9(pRNm5q]K]YFj81O^VepD+q-aX9gp`k+-('U3oQk&4<i=$Ph[u)d
%B*79p/Q;C8]HQ>pe4fL2.,c/4A7TYk&Us)L&\@23,#2"Ya=S*WnXht5XJ=Jord<G7je"%K!W@1%$iYQA<P^+3SVIb^^QCnIkH:d%
%JK98[7:5@D4b-6W.H>lTIreT-@&PP\8Bn2NC5XOVC0/)YMp(4C3Yk!k/rHgi"0S9QMVD$H_ai*`HI+9Us2k4YWNBGXDb;?.k(9q/
%<I/lY/tCKPA.*i*ej\90cS@=**UW"%G_LH7%S5X*O!s,(g.=1^ou4kdBI!0&4ZDdrZ*LEPeL7lX#DF3N?/*uSAcN0-e>%Bs-DR2j
%-!Rs`O):PiR[/Q?'Aj?,rd<FfTocVtK?s-t,N(,OR4'6hHNY+WWQc:^%ahef#L\e:e5bt6A@KqO(kV8JRf.0b``D+#T[Jf+#LsAk
%96Ma@fbo;BPC_Wp`8A9\;mbldMZ-!Dne__!I@`jJonH'8>(l-0)o$N/TXQrHB_bF;KND+,_iX;\IqRJ@\eN`g$,%MT:6l/hrf">"
%@G]i:*0*B9i?P_H.NnZ?$&?El:tQZVAjdZ-=\&69lG-/u/B([e!I098`ZQ,i@;(5b@WLGj+U+[R[[4i+Fe82b,Z:E;_DY6LXg(5S
%_>V$G/S(O]30I'Q'oAns0+U@R&WE\`MNEolb35$(IOhF<%\S-Jd0ldN4UUGQW=0+4Ks)0Jf%$Q6:EHM@G]:9@fUcWb&^JUKMr.F!
%/&ITp4(-7-^$buBk*[3dAs^kJGk=on%:KZ1Hh.YbXbuEh>UI0)+k<YV!33#Jd/%12qp0trJp>cc^[DAZm(o+-=j:Nfk(4brVrP&n
%eQkl="@u'VQ9s$K95#OS_tId2/\$)rl`QuZ"i=+g]Y'_aFC/e6+48J%m>UO#38r>OM18s87R)s`.,iVM=fb(1[d;=L2/8dKqrtlZ
%+7^QA^\/].q-OOYO^EB^r4d*EIsi:#Q-^mlMj&Hu,D+a(JdZbgB'9F!LT)I-<^"pY7j_aQS5r5Q_C*AKJ.i\Mn%q74=TcR,UqmI_
%Z0e1m=7r&5rA/1Q@=[dco\fUGP<F)-?$]d6R'cHMfO"^SF$#+pSh:X`IC-9hXE%B!#'o09*j--Po-C.BZo>XEKih06g-d-H[NPrK
%p&/ncQQ&5j$Lnr9$#-?sl9s4$HlLAEX!SF_'E@9R^&\p[p"#gX:B2hs9YhgsdD]S9<::mrYKJrrY_k0s-s+0&R=!c8X#Q_Oo39+0
%<S%K^LEg14&%L4QOsfM1"XfU$ou.E?ie:N$5bT*&6)a32&<a-c.D4/1L;2"sQ0>'en-fA4c(mKj\?!Zh9C(QsaOK;X7^YhXoBa6Z
%jkQ.Vg>f#2p]XcRFMd1m-d91Vdb7>^,H#9NG;+g"k@(t&m(RppD.9EK;M.'=X*Zk8[!G[s[O-t#mo!aojK+jDB?B8s=V:.^Ys]>-
%,?QMp&,aOs:>2XDInl_o>4"Yd?NABZ)B%I<B5se9$!`Lr"4R-un$GM7&Lm(TWukA\'aUQuY+BrU5aN$tHAW<i],^*)>QbUA@@XV)
%l62HC;4(.8L%99Fq)k0Z%]-#O_+RMr7A[S%"/qg:g;r$C#0dG(mN*jPZj>5gold"PBTZTE_TDo%?&OmT7V1k]jJ^&f=Tf_N(gdVE
%e6Hk7$8AAY:Sg2&cU&KMN2jiD*LCg/W8PaC=T\b%IbLpdOa9UE0)+R!52A,%=mtLcbE=)r;WL2JYSkf4aOFmR%ZIb*KL155A&m<2
%1B;2)'#C1rLr@f*,Cu*''gf^*1iHL5$I1$'!K?G;eL<9BXs?DJ&3BCM-72%%duuXt6<jnL??l`>c+>*HWiTBF9:W^+o*I5edqeV4
%Kr',4ni1km9Eu1kT@p+D(D7(%`(Re(?uB*GL/cI`Xq]QiC8f`V`r8jq%OR*&_[LqcV+p;p;"/uGCVEitjmHR'#\^q=m^GU*iX2c;
%W$3[,g:5f)ng1ms-I22K&giR+ImB=Q";%kA9<<'(,rF=>gjWo^-<Ci!94AI#[jtVLJts;>%;`6f\;Lfn)]cPqmhfJIO!AnB<ic"p
%Z'Q[G2hhEff-taO7X!(NmLhK<=e1EUi=H5$#i\L,fH'88RV'0)k*l-4.<XAJTLSEZl^3mce:56gE=29&"83e6I%3@398"b88oR]1
%<RpLco=G1JK)Qqr-\Z<g"_Cb-n-+mUrG<t![S7Rb9Hj`RZO%;"R^u*>/?i$jiQkFdVj1OZ/nUQlbE8f<lkn<Ep7s=q4b=T79!71S
%@6W!EP7:Y%(!9)Edmk,JV[Ee.Md/uLl^(3ipMFX,'-$U^'8O"],XiS!-gIGU--)U1,N.K:_.((_iiJ*1P,hD)Z\-Kh7&6Ni!$ch8
%T,sqq"`A"8a6Lar$tZYA8-hjue:,2tVd0ZR)eWphFT:!#RD)`S#@Q(?p!9[i;B9.)h'$TsV!m'g_kd.:e^TG[C[MI@V9Xg"lACJA
%_IIe`H`1^s^^r$s*ZB#1?ERA^<8M<ei$K,)XgoJlBFrf]Z@*HdZ@f-&kG%43$K0T;URQf6`W5]]VBcf)]91]-N<Rdg@SSaVFm!,/
%`_Q+PaOQNBnnZ,"KtNeThpq&Z_5`O;aNm:*F#RnZX$1l>4N63iXXU2QpPbFmfkDA&rs^Lb;8-L&CD9K/mKsXR%Y[*%Y\MGPF3Vrf
%O;9#G0hoIec3Wjn^chm.Bt?teMRM7pbg@fX+@^Hj\f3D!7rg'9mVb16'^$UZ2V`@s7-/YO_jj6Lk/bbHGQD_$[4'5lf=]"#Tn/bV
%GPC*?*8Z]nOUg62&`"F].P/*MbN]TCg1R3N$ZDPLV/up.KU#?dR6-rpdY3OBZj,$27oE?GBVhisPo]>Un19gBA&=GdX#G)`]dKco
%Wk+@*V;\78j=p(32'&Es=lr/TLO=%P;mQ@;9oLT!@;7#4&lgg[K6&2bBY8MJZVJ'"aAIDS=mn\[r-")A71pMsLH2E5nVJ62:@NCM
%Bp2Ds$<jP$4:aB:-rB:<KCFaNrk]\J!Q.H/4IX?"8*P0td2D0M5b9BKbs3L=j]heN;b)`HHX%_IAKtI'-&<9?7UF&LMssVU6jMt3
%cMH)mqBTA.JA7XIcI(G(P!h[=ZW*DaPu/eVBcRE='G^5,9PU/Z2X'//Cl^Q,4@jLZD)j<;%\]*3-9sUDD/+8<"@nQZ)??*Q+Ziu'
%TdJ,]h#M"*l=3d`,XhE,eJjrBZt*''Q@DQ+\do5=\2seB8p2VZp'5s(%)8.F!M`*t,+;k#\A&9Ti2Jn]2ebG]-NP0(c"`r`2A$T>
%rlH9/M.R"BK^(p(9uU_N/7u-lcEOK^BV;jY26VYSV7AR;a%V8"[$$/ni0R2#Q[rn4AnF\:$U_@-P)K:$77r6XZrnLQ$;42Ga*+QH
%c3H]O=XG7JHaN"9F?^4X/>Hq%KOt2mYktmh57sP3W+h2u9YlS<g[1-#X"+fVot0T>(sCtCKD9o8ZNeG9U-uC2'\IFB+Vp\Z!&[j9
%=]N(X#cj8-=)l8RW5br#eW1r&E8!OIF^2\S/-hEVY<6OkYBN$7\YW%K6^/AeT_CXWJnW7KY(Cj.(Ak'X]Sj7)b,.oPl-ptK]h];J
%Z8%`3=>rPQ1,Y:;K[6rcTsDl2gX+1('gh_`>.28m+kn19'APJQ2GpHRBfCI)NM:ljLMTt\X(RpW*s*X![`mkc!0MhHK)t*UH$-W5
%%C.CMkbuqr+jBQnJYY?J8'&qu`8E9X=h9<%'F$C_I_WDDMDYqq\2GL35n#+h]1F?f/nbLJ->Qo&[39#j&2)AgiOA@i_QS<DpDRUG
%bTMfj_6$4X@[bMl5^40l_kUP(Fc6mMQ?\*-[Q"Pj5Jh"*S8C.K72cYh;<JCS=@VT?B>W8`dEUpU0t3rYf%cS^YW+@M?0j9YmVO9\
%Rb.NkQ$WO:[=a-hDl]1BfV\ZFXtJ=6m5U^lga8L*2*J(rj;&&@B)/u]GDa+nd4%lcHR[L!3pj:._bndoO+#@:%_Bjt=XA>1nf0i#
%-F6%,q1us3&#4:Lb&&$5j/F&^3*FAp9hGBk9+X)AL:=]&Z)Y;WFD3]o,=gT\(o(F":maG.Mq1-W(.Kk7,3_BdohrDLiC85c3'=5B
%BU<<&?Lf`*GF";4ko98>(#Kk^r341;D/MebKn^f4"dCg$r2"_D^ut0$Dq\r*;^%B52nu3QGrf3cV@g&!h;T$<"'p4"^UdWbpSJ.f
%E"8<!iW/YY4Jb2WArWnK[;6;8c+"J$>Z"0KWiP,s9<\Me.@l5i844m!d\BSL)kJNcK/*Z'TVSf_3hK^[pr'&1883MlEj^]iph"i2
%l7SfuaLp"d)JK&JhPTek,>/*Kq>b"SWg`R%Td243V0`FoF@L2Glf:IPL!Y5[;O+G>A;<hnhO,?rIYHhmem.,GRMQ(,:Ct9DGmalm
%_I`3.g8Eh$e]eZVRNl@\:*:eBWj8<prN%+]l3(DpNCb:r`A7Sms%3bdmCSIl'b`W7#n)e#p2rO7]Vdrud)cW>i%#<-)-.H)X'YZ'
%flq12enUq0;C4aY13[tE42[+RQ'`oV<hK8jgkpQKfW=Y@YZ'BJCRcTEIW3F@[PInn&?=>e&PM(c);Sq]`EGH-rh25Fs&c5O4tNW+
%W!:U/8'bOS^XVI-WP-_R%h)\`j\JQpB+ig9EoFqMDU'#*99X^D9S"gA7s>6\LVu&UroUg(0/#JOd4N?\gGFW6f7(#=V`.`r:M4&^
%$DGa;Oj7@?M\eLVi_g2S<=KDV2btP/ner$O\QKVE^JIL^&3X*b?hdWO$[AVbhfN5.pCS;urt`4@E-KbN<YQ>&^UWb03K789C*t'(
%12fm/L/<4G=;!nfKHKCiC@F,fmSe8pB+sBXbBQu(F?<eGe4cjqW,=A;(7L5Y@q(EGM?aZ24gqM>25%t#.&&puNk>W'r-!FCCHCa=
%jr$U-'NXEp@BO/f3`r%F;[Dgk%(&,!VBlTE\(^2.I->+'rmr_.@0s*.LSt3k$9l>[:P?>(Yp+LB&e<!:6*`r(I^_VH13(,"kur96
%.V6LBal-Ft&eK?r=7]4mIENKOe``674?/!<&S84o;euU*0M>B\VeA5DO"fZL4$RrfgiQVR9L@\q4ccUj-&p9]NEsol"e@-c=hbCj
%\r,sVM4n97Y\;^p)jY+AclObV$;'AIfGV=*g3O?<;<usK484EBm"li]2^>L6f1uqYS-d34*"__@>%7N[JM7rS@Yd+-#<5_k1@N-M
%RqAsp/=G\,2JkQQ0rQ<R'O<b&<EMAIL>]Oh+I4idDbbi&<\I'.0`UX(34UrUG0[)>?2Dmc*D2@R)GR[U.;ri]]trJ]g-Y]
%dNYWoHI.8F*B\`P^j%:CdVhZ^@f,QVA6EO"`<4i<UfOYg]7b5qn*8_.(C8uW9GB7MrG4NrSjun6U)td/)</b/VKj"78iatGG"Cj/
%khB+.*JZ%n+<5.K$6E2*S:dn)!WjP^_DOJA=*F%qGK!K3,c=GP,NgIIaMAE\R)^)[1f^nIVk9f++<Sj)@lh8&,2k#-C[Oe4a9?)W
%o9=OT,h*)OH<;m)>/]UY'i)Lk@>R:[7rEOdOqM%l0m!.0LLs+p%$JnbiXj!rc]gN*KL1;rbg]!VP/"Hk0t'JpME+M$(PaTr#jAY:
%[D`M-F0$aC<lD&!A%5?1Ae)qpN@N%=!Rq:rG]tK/%5fk)H(C:3r?c,sW2LS#_)=%8G<9!LPo&?sa_9e^M'S(fb9_fe;)=26nF@'4
%QdM+f@QKOK.,%EhN?C2G4(Q)Rl@<.e!\BZ8mc@T<_2&P+Z8_4^c6JRV*pVqiR+mS6U_17jZ)X>[5#o<!M3=].(%j9Dr/bN%p]<@M
%e,[oh1b#q!\'c,7"d^93he<K]:^@`)0J9VcC.T[rat?\Gbdhk"+N/s/mmdpE/s<Sk-']bl#0>R)JpL,,aYd)or=o)[#UGN\d58#j
%eV15b9_K2qq!"m:0.W!p?7TU^\$WrS@m47Nm1QI$9Li(0]32@DBRW6Kd"uJ,Am.YoTYYmh8c'ppnc8ML%]endY!L>21i37B(#-c7
%_A*Y_OFX>#9I<=-&C'la4c`q([@;g_c9jK!Gk;;056f-,>T9BO#7$-"WC@WA##4)KbE:p4#]NUNI,qEopY$e9XZHr7[,t8[KD=X)
%D:.iG^W9Gmr5!jY9@`^C<!,G#Jj:M7@2c,=#b(dB7c/H(CLP2[@bU`7_"c.RfF=.jA>)T/DqU.C^'k>M4_KSYb;CE!O6H"^qHsIS
%^4B[^1BK32b0tN$]cN7Y2K_[?%SO`^&@*G6,7IMg6;H(6:NI?`_]"F?_`j2+$O*;5<d]dM[*+!q($ZJ@Trl44/&<,P*a8ml)Bgmn
%Y>qVq*GC:,I*<NgZQkp#`iYd</mk(q(a[Vi&I3Ed*1/;bER]RD%FNBXcOssK1h[3/eGg$_&d1U1X1+ZB858J%FpFAjOEJn>f1sAo
%q./0P6>r'2pLhD3jQuMZf[@"Z,)g)ILJATbCndb5o$ZF8Tl6_8Jc%@egU?#gFCVKdN,tfS%N8$8eXG73G_aL1k_3d>j<f(T7#KDF
%-U.?`f(m12+X4f[2\qdV!Ut=UEmrI5+HTu(DtP#=^P?E+pPr;(X/X:KedD0f<qsP6O.L@NY&Xr$_up]BP6M/]pDIDMGQC:#frl`"
%+C[]ULIW%4G"5i@o0,rg7X/8)Ks4)>L_?(^>^e("7=;g1p4+U_7!HHPY+B@eH58IZm<IIN"l1a&@^H>t/^LASGsgFN2<6!9F;)tF
%iD.YafI=3N.^,tDH1=d4B7upSalJjg]uG*DQ6QMa/Uc/]U.!EfB\OTV0l^OO(r>:%=Y.ShWPRBjiB5kD<<l#;?=F#q2_YX@Tpt@5
%BL^WbjOR8RJk9Ao&!k"$[s"*<Nj+9m59$9jdk9s\6jZ>&UKu0_g;-dV=3u,STMZ*8[]j5!b_J\%55URs^I<Q"Tul9H`ONo7@6"IE
%2etA,rasVmc?i3E+kk6cLYQN/3);@GR-fG-;AD4Q]N4$FjT]P4_sOX@"GDC$n>t]gTFO!\+WRRAo3KGR:citRbSaCh%bN@<+*67N
%L#etcX_$05"K>B+Fa%nB)._WFW/QB%iWqX*ALl'lNgl3P3QDE"]VVi$[3X(6+7?R*RL)],KZ;+-'Lao(1sR;\YBNR^UV)TXnBl6$
%pQra\#(&c^Ic2c*Ol'BF[1GmS6+dKI5](CB'!9L,H6F(h*7Ru=0t9B63Bl>8KGm7P8?J=k#AGfY"DiE/%PW!;G2j>$f2MSTd#;-Y
%J>T6D>a41b+Z%K_FK2@oL!&KICo?M&K,rcqS2on`RAT'uW:cAc(P@!iemQqZ,F#DOCC`*BK]G`5/<+4@/bdW'3T"@b1j>DLD&hi4
%i@ZWJI(!r737lgAihZ''_I,(%Y3;OoLQe)tM0piiP+.uYST`NXQ6bfHOW[f+@b,A%(G.?dj(<@2"#_!sol,6<S&9=T0_sk92c#to
%gm)iSpg+l_k8f@eOhA:C4fNI!_HPs<*/:Xa^_r2`:-HV<6m!L)/=Q0Q"CdZ1-EL3Y/(AuC,ZE]e4/p,<p8h>/C4N8=5jOe(QJQtA
%)OPP1TG6iu`#j-nc:16B,P?p4oX0$5%oHSPn$9j&WfN^b#1Jg9;2Vsknjo:,5E:eq3H.0]LH#cI(%-Se4e`5b>YTs!@@dFl2k895
%"ur@D0))5fVi\9PdL>EX#pJ%B]KI0#/dpdcmF*2M/3m]HVX9sI77r7EB*FeS<*`$<<$t]`<7GW.j=,CgEdhWsI[jAWhZ-'BKJ'W4
%M51sL71Vc"cbjJc:iRq;+6^DQ)VedCmdc0_#u1D?a*pI@UR%d]A<^D!EoI/k/rhO!I'_IId!C:lD^^jP9s0.Jc<J^!&#/t?b#M4F
%U!P"uXq^5G9hg]u4B`Fn_aOD^VO1Z.4O@*f8m$+1(T:+hpg*@NMdSSLB)*T&`^6Bd_$?dk'nq7i6^Z;q$#F?Y,+bI8c]>%2Eju`C
%hB3lP1rDIA]I^X)9'<)n!.)T8V\pWRf4*$YD"[c]M%!@)!.cm[iGBSFVCoG0OstR(!u+^nE6=WS^+.a#E!B<?S+DS(g.;S5dGg7o
%_k?)Xr*7SuXXVDqQWr\^a#hYOX0*m!`Q)t/ZRQfHalDd1%^CPX6DB32hBIUUiZ,R2]iZ_U2<r5WC,gu@V]Y2pJPJD,V`N%8dFtSo
%>$/e4pf.[UnV(8d%\EB:g'pA$%SOm:Zele62gAB0UTjXUkGT1C1a`GrBE=Iu;!qqblgU<RYopapk'I(ecc11aAG()3&JMgpGnC<Z
%99^Kf(O;1*GP*\$W'D*:-k(<$$dRH!@M3I"-Feqf):@2F,;RYJN)86mUU="oP;dV-]aRch5X%^5#VMI`Mi"[+qb1NI,H2:R)54&5
%#'*\!*KkpkY=<P5b6A?H+EVt&(E<-B5EV<i0qn:lMS]%2KO)eG=_1bL.SNHCeo>)"Er/Gjh&M@.%MEI9*3i'**CuRpYY[_J-k@6X
%F9#I:*1i=_3<<XoC,c`,c):0_:;M9VU@]5l8ur$RUT,g\-kKAIN`]mjP$lGpUoXcYTYseE&=;M,?-94X'2fBb9%ZLur+4>?=!8h4
%p<smF%(;8,H!\?0OmDgJ7dr"2iIiH@.jbfmJQ92!mODgjg.E>UW5o978GRA86eBGK4Ls-nn]!RIW;:S=$D."RaWYp"jC$If91uf[
%GW)2>@M#uQ+#_!b)5*OkVci)(*&2tb3%t$iQ[\4]Dm7bk6uVocjD%g"\VhlgW(=C";&;17f]!jeh@H"_Rh*KRQ^JJg]5!Hk=&^8Z
%Tbs.dOdCBTTZYXFY\p0-.6RAgc3QioFtCB5&H0PGV_oFM=8X"i`@fQ:bW5qg[!Hm2jXoOV_MC&nEtG[W6HiR*Z`]\RoL^9'G]k'3
%Cm"BU?Ce-#_9J$J907f4C`@WVRtRQ>#D/.QoZ),ec!MH'M.rh,o=L#`UR[Ph"/+JWiaETfEB.*WG9E$7(B.`rQGGf@,WVEs+m[mn
%UE[I_NX:D-;h'%IXTfT+?[(Y#@I&,X\s/DSe7+[3j^-WT?hfIga*B-Z`%l;em'X)#K>_8kIe0FW-hjqRf9;DP&<\I$D%%Ot.cel2
%;ckS$?*sTmaj_ktCEEn4FLpNRL>i4WrqUBm&M(n.iN&u4#@!.Z:]odQfmu7Udp7^lS&HXkQ]U=3&=EbV]=jgXlf#`lBDFONnrm?I
%c$64]^dF!nL%qZiW'MOJ.Y5RGR#r@!Z]:'PI:Tn-[&eS#_`:tVEdBM2A`[E#W*n>;Q2SS/.[!,NGn8Es1OSjZjBa=A'fgnGUJVA[
%d>GLXpYo3]BU2TcY4$2heKeeA6tqri</-D/Tm:tRQJrJt]Vsp$6tni!4/R9P9S[@5qk]AS3+;d1g)l3skd6M"gf*G9?d!O7qC:^P
%VoTO>?\hg`&Lf9=5DQH[/-;c]e/F,TqS@le)P.R\=]:XqB6JR?=bc_11=-qdH^71(NYW;dnZ3Qe(Q#bUI``H;KVRC\5bZ@M[Ts$p
%W&`Ekk8/'PNUVX?D:C_I[iQJ'@.I&UKG[kElCd<=eYmg+TuB$D\"F=#!T?5%,pB_u65[1I*L"Y4.:fVt$,'UEln51M!*pL2KY`\d
%o)QbifgB;U?-\oFf03mB\qB%G'iBk&$b>Pu.G+!>:ja`,Rd#YEf/u9A8d$Oa7m=q<\0#-2[kM?lh?+I_#0]j.kLXfCYUb]@l#k%s
%_j)YKr]&AdURY=IXuX6A\lPcU+(X6M=O_KO^hheoUW:g*Ys@^udnI@I@Zqplrq+@q;%W._TdjeaDR2"pi7)<@"5#M>QG*lL@P812
%(:k&dR["8t_iHRj9L0s/**"XB&?0bZ=,Pi>6ck68@5G)V25.[92p4iWU_\cW\B#L9-$c<mhddmrO\fNT%`5gq($'1`9Knb*<5)7c
%d;0As6PEs%6A,G:%o(2X<s0)s?-("4Q5D'p0lYlhCZZDQY=Ie269BR<Oq3W3%egV(,Fh7Pl\NEI]OcaNaXm@Xb9WZf6m[&6=YKc$
%*qjr3AkYs*3L8<1$pV2o.3GTF3I)SO*8G5NPW^;WQ-`#ClSEmPQ:lF0hqUZ]'URH^@O/a4<%rsZnbbOeK%Qhq]L$miPfJNM%ibAQ
%:&]j=g?Zo\>"qqG\Em)Q!j8g\a0-+Rq3>LM\"cVe)+#''5WDNJS\AjGMF*2#N7JQ?L)!%P8gQ<h2=%"IY8ZNoaA9>pbXml>#/'=3
%6Go#Ra1NfK<-S\c;fs7o]%oVHB3@PB)%(GnO;&]N7Lm&6(*-i!!k(TQ#W>h=A6N#f/kFmLJ-Kq=8l%/0_VMM?O\hGWWb@%`i^>%%
%Tt-:>_rG/*&Lqb]P!>\!`*#FccmWF.=_iC7\X-tF]^dWqmJ@XOistn5\,73HecX*"$_RmgiZ9>fgBo8m=)``:X_l75#d?MV6-'Kg
%,R%cJITTmW.28lu8-adOmh-\79V9/fP,i>a9baRACf!j/.\b!&&FWjF_Te>>D5oZtgetQje"+WD3f.e(T`K#8Ypj$lr9WRhKM[)O
%.J,/2n-k"`J4C59K6KDH%onZ_:4LNIOlm:I[Yp4`EBQQNB1sNVa+NBu1*pi1%K1ir;k9U2=5t5*e^IMSU[pB<A1r,R0XJ/QZ9N3+
%aVKSCC8WFkE)1-lQ#YsER<X__W;h1Cq3Y\KLTiuAq6U%qKgX$SpVS*rn3Lm4+PRI[,r*p#ds7%?fZ^3YR)LN^Tc=kXIL\&j<P`Ae
%+r"o"UK<!Mb-#TY0JG``Nm@uCcNl9F$+2JflVbcDb(OM\n=U@B?Sapf/JNS1,K)+NbP$_:$\M<M(u_chJdk<-G9Pc(_^%lE2GC.-
%je+#\*8/$1'epi%P3>asO00kLK:N/L1sg-7-m*9-:hi;]j9ff2bB\eber4n!VmFB!c]F34LF2"#$H:?_E]8C12[]+f,5d5NJ4^$p
%l("\7h1dLBSHWn;gU4c)O_;o3L\_$RF^ufKQ?fa+Y+FG]Nb#(;Y8D@8+*\+4_:qehcD<%LR6>dSFpPLY&G@D?/W2=R.7OkJRrW>j
%_qK+@&FgZR),5Dc&]b%d?Og_'_d#g^]fgGfAdtM@=rL**lKc%&eM9IiW%pXWiAO4j/5JV":oKU=Qbn\.0@o:ohf0cGR)6D.ckG."
%r@RK@p"]6Fd90PN&$\J*2+c[J_JL!5VF([p8o0i9!$Ya,+"gJ<*D>a=&e[O2@b%5M6#NdH=:dH.'84sBWeZh_5uF.XkEGjqN-M8e
%+7oFkaEUtg\uon*m$*@26Kop':hp6VB/pJ99M2!M4,Rd4/PI`cW*u/<?7RX)fOU^.@`uP!hpk<lce@*BpUmM\6T)J+"hTL,k9/!(
%A6H"<>fbC?Jhs"r/A$JJ0p)_.0F1T=CKkPp:2rDW9=,q.a,cYKS>5ADH+B3SoYWCm$^Wsi8pl-Lm%=?hEB4XH<VC&K%4+Z[,pD%7
%ctng8Gdq^`Bc[Rl8jeuX$dYl"'2qH%pbRL0>TfY]qdC(s5o:l*aQ'#)?G^m2-fchuP<>"BPc&euG@>u$iebFY=1fG5>t>7Y6$jY5
%iX-['-=WRUB[&kiOV57X+5A]T4\QC^SN-":@2R2upFL#&eE@51X'@iB1MR.Dh\r113bGH3Ru_MjPFK3ii\,].?IRkJYl]M%e/?"3
%\_<!0m]nH/bR2;j$8I*T.24?8H75[)8:d9\VN=+?4=\0HBT43*:^YjJ%n_VlcgWIGjMdE]c%R<_R4*aNirH&k?hXP8o0P/Ae%6hp
%,Rn5G/LV8o7:gmK]U@t;C(^%+.K3Wc8e&#b*>3_W86B>#6's&'f"`Le"Z.WOpM's;.#3oUnGfih-+fISa*#-p6Vh$&pC06jCLj(B
%L*n#\nhU^]c4U/8r.!VS7%@I]8PoV(M[=-!%,Te.S:,Poc&/o>A7H>2Q[/HM@Y@n*Y(FbuVFGql\rD_>Jh8_?`9TW4:dPB[-,NGA
%"("N*E[hfTV^:pl^\N:"6'YD#&Efp!]L+M(2sMHS#;sPqWM#jW01::d9<ZnDRQQ'j\CGFpVutdZdBUmVcol8!0J=!)=qBX9A\<t?
%bHm(RjJkd!(AK[rRpeGLcW=fQ_Q5"'mHV=Y\T,=]q]5,37[)._ZLts%J:#npC.8FO>@;nP]6`ou[#EN\=0&#S0UcP^7ao00V(538
%Aa2`!gWeCTUFCocP-Te#AsYqfD;B+T<5k\KH/rn%BZt&B!gqNT'UEPofOJ<40A,+6Bb-1h<k%nb.]d;T0bZZbLA\FI(n%A9054Kh
%+RIO\J?dnl+&C(QQ0`@@Ym*b1Li?>EU,GQpPZ>Zg?G.89]N/a"hj=N0B'&cL68m^q/M?gc8lroT`-HkSd@1\[c;)#.nQ%EFiKm#R
%k&G^Zg/O2B8FTk9,!4/4h)u9GO'FY)!cTs9P>:A+r5(M/BKp)njjG;[ORHTp"kK+&;daECcg1U*Uui2uEYYam^ALr)F^G)M/08'n
%V8o3"jXAhug>ah4?QJdCKhM>t0t:/sZE(_4F+WDK,ZQg5aO2e/&mqMQ!phJ5e#g\lL]ig`,lDsRro+41f,?GG-aFK,J>Rf?*g@^8
%G:F7fgttS/=0;Lkq@3!>Vm2%/JA9Si*-JZ:4sTD4p)IInWN"?,g^De]HiCPoi<\CKroMSMKB.elN5p%:XHGq-\Pf*1Wb-,/5Gleh
%&98LY#`V.pi?N"\EnHsg*8<Tq0FpD7Ng=f)O&.t:iP"b!Zci.\G/<(4B,`c%OT0&5I,iL/HVfs&]LSX:T>L$gk6:$jN@kti`nB&&
%C,DV8GL.!np==jLM%0#7[$3lkYq7@+67ER"2oe#^&+,qe)IkOA3qO@UIOta;4]oQ)7RiKYI3SLUV#j9daY"DW^gt+2SKmY^)TPEq
%P7p,8m481'"9`7O4,kPG-1rC5`s;<>BH#m)lIHbSKfW?*bF"\tG4mAQ"])I,V'JU6S7#H,bUfOB^d4^Bo9P!^Ni>WHE-u\DI''"/
%PK,pB!j73jhRO>b%\eBAVc.>tXUQA[B1p]_UFV:hDZ'/GqrW+IG34f6"L+Bn'_Z-S0">J@C`'?Y4]&ZJn'IVs`#0uDYlYEI[IQ5$
%RRr<;:N4`[GdAi5Jl_(1VZ"VE6h]K(2'QA/mVoFn"`,+9#(G\?PU)o-A<e(!i=KZ.:Ck]Qa($f8a!;_9jI^d9O63nolS(+D'$I$]
%XgUO''WteP$&GM7;(Qr!<%6(6]b._ReN)i=VXa==Fj.qgG[YJkNqgT;q=:k!^0K"]lrrp^dCs5Z=u*M\Rtj>u0@mru^9YStDL%GJ
%eAP-s0&kN#OtDdCC57n029Z5-!R_p<0LAAnZM0r!#Ku(`hA_'%@\.Z'jCFLB'fj\53g`mae%gU=P\n#-@8im;'_F]H&';\oJd[(>
%EK'Uon=R]4`[4DG)LDj%/8a+Q374#,oA`ln4*6%SAQ8OPV^i\ppqeK`5_*81m2qggNWK?`&%+"!KCfP7!L\+nlbIt48q2^JHkSBA
%db)Gs'9VmE05M)MT,ZD6N8Hf9+%9\>\l!8V4<-S>rD>BtDn^_&fGBeXhpO&\GM+Ctp-T8H]+C540cgA*V'(frhho>J%)Oh<n'1r'
%W$e8Y+luW8o835GQ%sC3X`'"ON5*U98T;aT1NTL..:\33Vn95ro#t@WGh+V@&\:sH@;eYK/;uBV2osdq!@)I*+]bMe(dT&L0F,W%
%TA]uVkUJ0aV_(@GRH9t*KZ^)i$N?"IeWe^O*9`D)S'2;\.R@^kR,e]!#.h-<!/Rr1]Nos(#ZRMX@^$>'^J>dBgs64KJctetj("Ms
%(WNHb9Iq,hbqdkZ-*h-uS%)[45o)hRk/^t\YtDe(I5b4oS`ko#P&2h\pJM>ui(4c6VB]lNBTrjk+aH,@r5j?[BU,a+N._)?Q2*JC
%N0fZPCC;s(/-^:*-:uQe?XSneSg.];SLG(0.?S/W:(4bGpaaOm.l\W?jMZk9e#:Xe8]B0>9;9+S"E%-u#tC,X8i&>a2-)(n_*;E4
%.gZmb[es>GQXR5Sps4pKd3X-)[SAPqAJCpc1p,k6!_SlP@NDOMRQm0[a*Q[Wg`]8$Ut5^D';3Emc=[cEGi^3E4-]Z4B"I-qOS/c@
%UloUdL"8?BQ@_GJXKa>]J<qu9WPPF,@g8u2au-T>M1j8oUgF^r>:a&C"+j0A,E17#^r"1UZd.%6P]3I=`<[ZjqT=in`qT7[jMm<5
%q37pm%=i<2G^[-jA>K/8XoHbm#034`97THr#!gt*Fl4:R?Snrs.i40Gs8>R`HQUXk((qmD;RAd=RTr$Q+LUcD>loV,:k+gKs"sEe
%>TjL9Wr@U[rl)a7,i=UIP1iRo<Oq)l#Bk,M\eS/.'jS'\-6\!\O*Tr/mhE!I3j;<.D%0CQTcR0:\D!#UOd8Fe["0WW(=,,&R%Thn
%&gi/o_DEG4cX*e)Q0V6YJ<XgagFS<-btImk7AGdkKjOHrc86KLRNL>0VZD*BZaj4M4i3.Q`^Km$jQKP+(B*[G,94+'MeSubMI!d4
%fp0,.*lf4(rH:M`:9@;OG+/ah)SD(hb`)ViZ\#b9jh'bmPG^bD8!mPPef\YYYK;h+QBfZ$LiA8?9G!X#]+-.CY.be;go(S\\_i^K
%r\ZuQA?4S7iDNGOi)sJ(ih9]I'H#$El>>IG:p\d'0l1B/#nA.Y("LV)TJ?!"MMDA$PD'5YpENJhZ;l!sOa4m;S+YK++JTuO#S.h\
%+[Z6]N_?1P7tV(jWI*L)*rWu+5!c5HG2dLRR/)\lW\XUjCtdW59)5f9ks9ZHO9NOJYckL.J"St.5hre3><.`88OkRggDOe4=&BLP
%KLKl\ZX_q$e>%GRS7#B3Xq.D.C^Zh3L\lCg(+0TcL)aY4G_nrLp`tBe>$(eDCV_8j=KL:#,X8bKAg"6784m'1dH?orU\[J%/N![*
%M+TZOUI+M*mK[o:S(WION(/lO=3+_7,uklB787Ir!@0+G:H/mV-A&/0U&7i%@K\U!G#N3U@_'a&n+/&[)qt9(Lr+kpff16l6suqT
%eKNX`L,>#a_DNQ5)9,&WID^b<WRena8sMd#?I]/58(:S&ci.]]N9`/-qeHl>Adotug1_NaYsPh`VON,RJ?]DO"pkh18:KY%r=^g"
%\"mB8d*2<(Ta1*H4@rm)OK--dG/0H:AP@b-Rg7oS,.2EmMbRBA/>>E>UN@B4lXUj_"*@.^<NQI'6#:-^qVO1#gfLW,%o=I^qjT`m
%3#660LPV?%LMkPF]\F5K'td8A[4$kQf<L-]VRGGRVf7d(>')])brg/)?`mrN[MjcBTQ4OOR^pBcV=R46,akEBC<UM'lgR4e;SpGu
%@3PJOjCN5Nd%,b(F">2mYI1@l2.-J]*,O+a/fgK>P%Z&K26Ss5Rj*=a8KHnV$M(JmXUXLE'4i-IIdMP:6S8aX<YJF<Go_'6J:0e`
%7V_(JjJ,84#dtImF;T5W],TfYEk+__+u]>X$l%r-XseP;FpM.lL0^DPY3XG^Y1=86_<*nBU%X-[.(V&'QP5F2J5u4dN[8)O[4Ou_
%5[/mJWHN/mjP';503:VKYHT[TCQ?fsQrrs0eY!A,D3YO*<R;mdbB^l>gZop?Mdh3kXc^O1[ZXm*\FpFR$<EMAIL>'&&eCXC)G@
%)Uug&UC+3*Q4^dG0Yoa;eZ*mTQANB\:@TqRWG.Ti(@kRHDYk!>iT2Ho,_n%Z#!:*!)5ULEj6IF(doEu3DN_N>H@k]84V)SVW6sh+
%/)<l1PIE.JpLN_*oReLOTU4OaAf3,P1_3umT':`UiSX8g5ce+5%Q]MBT?P8ZMGmAn&t=_g;;R;UFj'n)cF]MU"J6`C:-K.lLXT8U
%cO8``*hg3:OaCFd;)E$Kifm9'&%W8F[NAc"I*Mi9ID7ruWY%>drY3=_a7@SKf>J*5E/X\L5=FR,4W"B>N5=UefJs-XjALI/9)O%H
%6\W7e^Z6)E`66H7i'7b9>(GDZR9JQ.3EP-?7Pk'V_.3cA9QTV\RMg>V/]0H'57VOu()im/6MhH0#!"(hpg[8j4%M*+5BYi$^L6LX
%(g9Vi%&8umAD],Q!Cf,o,d0Kqh.MZXF=s42'i>I2>ESbZO],9*?XL!d_M&rdOE],=FSg"8_Yjp>dP$I'OQ9@bB`ap?KDB>p]pa7P
%m1O>[-?sVY-0/eK34uFcbc!0Op;9^e)mP/hQPn>16T[bb[:L-C&_Kl5N8D'm_%,lm'J1>QJo3eK$JWP<l,Rpg'MKt@Xa^j1/B+Kf
%[#5V>I?4,+7)Ld.V].K:V;[4&T0sO@!Zt.Y-p0j3P;tn4Q3%W6d',(ENHtYT]M#N4$b<0bN0+30"6AbXAI$]X".Hei:CY/GlQqSS
%1k/fq;FI;D.7G[oCdMF5eSDUeQQquhgQ?58WQP9,<p9;+lOj`B@6EVqqG)G6q_482!6N<3:Jg&to[7lDOk`EjU9,_X8)ZF(-A_hV
%rMm,MGK$J+!-#0c(d-tuM^Co9j4#0&]lUo2<@be"m'N\<]jgnJ*EK(NUJMFC(^NO^:QGOXq[,&.94g#iNCd@R"p=pi=?7f7Jlq4"
%Ku)rN<VdVBl.a!*gKMims/iaBfK_+Cq`\KmURiSPHZ+kYRcV`29B.g7@9PG#`oF*qH"Vo3hNO@k.,F(`(]X"H`7b&@.TZ)OoH#*Z
%SJ<7:m,oUJeFA]@5cfRKI^M#6mXoYHY]`2t$Q3WU_-hYMb/IKc*l?%m2XM\%b*?dNr5l#K`#IA,9r[ZhI=>ShRX57^GW%V\In=)-
%o.gE#/OjrL>KkXMK=:IkqS=OjW-$\9J<-bcQ]_i5pS/h:J+]c8qFU4'pH0m2>NY.Z@6ga*2O)=p/XV;'[6CbMD5R&T`)>*4ipH[2
%\BA5m$]^-BgMVatS3ENCYRI\=ODa\^9QDT(h2/H:dCglRo>m,CKL-bccQU;!,`gK)M3=k!#f[JKT%D8U!EqT.,h@"'DRYD+$2u7p
%aho)Y/i8:,0YYd5lteqO4c(lU<%(2A0`/([XciLM.kiaN`Zn:k9`&`\O_hkh7/MSn)WWTA*rN'?D>J:fEa=-:9s;WhK-;#X<*n&/
%0qsk;=&4]/jtO.=OYS\(<iTu[9Xb:inJ,BfK=)2rB)'g`X`l'@/5%dfo[oQ8mq'7>dp4s;\-bml,o1dq9>BK4\@`QMi`S$@F3#+C
%QFE].BIuYnlSI28h7HYo+j8[)\\-"]THL0_6HQ@9`XND?M,iL`%`rVo)MPlkcqP[h4Og1TKkWjaZhe+51sA[tIbJd:\2BWC.ADa`
%A/j:7m')7+#5fd0A"g1QNU&qAmGk_p2;a?M,mo$Y,NOU1IIPjZI&r`X8`T_bk7#8h:ai#Fo8g0E<G+SqV!<oZ?C6&G$P@Dr#'@b!
%5M@nP1N%\*YuH>u4'>V:,jI4;fc2C=I8D'&^6O#^K2'JDd)^HE!7&iRP=C@03%6hb^K,,\c:U2O)3dk6Ie+<6:U*&G[r/5k#+!t%
%\(WGm\cIH'^AM6J[1H<SN9qA/W#UDdG(r^H-i=D7k1f%'K[3[^]<>!N?`-Ic9p;HuQPMrF%:l(C/#U<p(s.$EAL9%ZEMB=\"Za%=
%@_:TfCY&r)l_EX2h0Nm1F,=Bsb^5nFI,.hsi^8\c<>5JLbo;X)'&6L"1qhX:cs>KS,pBMrH=K1aNHE`UD!V@rerMP+Oa/uLHar[&
%\all,bBG'T6NM;iE>YpN7GN_14'GC>NZ>.;&3`Fr_,69Ykd`?jZYi2LNfL]9GGuG,:FmKj2b5N2]^;;>Hpl#G4ItZ3nV[<3la`C5
%Rs;SN].R?@*o*c$!.f6uJA7W0RS3DA\2lr2ngdeaO+5GtA8W9(Ic,bQ.CK=m7GYML;k#J=l%YuH=)8\X%ZMi>bmoBNW^ng-,520/
%M3u.l'Ao"R](N^a'ba<(AmWdX1fR#obJ)0#HaobQr(uq1XsPO["/u_2`kbmqN2EDFEEt(B1\7(\=Kh%%%k4.XY12F'[C1=]/TLZ1
%e:@A-6Y!($DCEEOW1)TfK52G3YNWDVb1K"ba-X#h2>Jk7KW]3t`CA[nFm<*AVmP!H&W4/?&Ca8JKLY3[/_h0fhQ2b'=(B6'7j.=I
%mJY&rWeAWr=nr>/qQHF0N",SmM,5i+UAX&GQWSeZV,:P[^?$nahid@]qX.jSr:%r]KEW9dX!4!baEL6oA!L1S'PTPGq,iS"H(g2A
%LrDJk1t)'rm.+TU;sH1u?a:n#IiqM?8EWl$(E1RNead;X<mcp3m4Z"q"nS82SiUk?%(2V%Sghu"!j6NGmf%BUm+_I47bR>#7J.,M
%Xt'u9Z@cklYP/XKWF='Q%'/Kpi._02Zf5?Zp24Uj*-8u;Z)9\n>udbgCB3\/)c/qQF;&n%K)ph5H:.:ccPbjp\dJ!e&,Tpue#5"<
%V)-^`[#\^l63hmF-M7%,d8MfoM*CWhE"'`GQ;BPG,Q#$@qaOn8PWRA\d^i@:-n$hX;%^ln1^1lsQ'R+D$&6Y<W2`575>:;P0bJ</
%g`7nUEV!b@e.k"EfV6#X1B1^5'EtnOcuF'bgaW23hj7M&9$!>4'Z"HAmHTNYjsHk.bUg&bU^0H:f[S%G;)Yj:'[$:YWEH+D?OrW#
%8lWGB[a1C,<rO1!eXoKR+nFNWm)gir\.Utk<<WHDl),I!%N&TroNHa,c7eTpL#6:3,3mKKq[ut45!H^3**%fH7[Wlg<`d/8@C7r'
%?PdB7JoYRAfb+S&^dW'K%JUb&:k1%.Q<Gt%g[Z`Md0tTUq.\-7:Qn=[I)fB!_Q4#n&"^-+R^VF:2\e)K8$C#oJEFgg)6J);-=0\P
%d\:O1:k!QjXj=a(6kr.99#"WI1Ua1=hC]Y%[sbA;THs4E9@kZ?McFi)?H5uq"a*2qdJh!SfZ]*te:35\'-LHCo%@!aADN3HU+/mT
%JX.,j"k.N2&MrW&OSQ2JphWesJn3TVdqV[r%UT#b`O'e"_,7*_`KKMH!SYr$kD?`aS4Y5'^a)Vc839nYWSCuE]J9VRJB`3mW*Z=9
%pbM!d4F&t,_-81u3FE$YI3.?[L\ALZ^gD:uh%4PKj)W'FWWp>t.)uU'YhQr1(r4l.H3m,]9D\Tkb`F'HmH[/&C7:a4/M@+4WA_#J
%EJ4tT@e,K35MMjX6qLA"AKk?Y>t4UEWkHKLXW>sKm57u=Gq84#/PnaT.&7-]5h=@js"t2u6f+.>ql\'u-)tgSJ.^?]hDmQVi])Zi
%k[(c.P6K3JFY+-.h;1W5#[:\A$6*AP%m>kGqJ.pJI"Z&@&_[E2JC,Q5)e(V#pKG.sKm>ceWLb;CQME03[2GkTTtpN=0M9-?0,AE%
%YcI;JJ'CcNR]3n%)ss@lW(*&\c=aeARSPfQ.t1,&+()(?^_X?].$p&Ee"9FgaB<n[>.*DS47KoF/=-9_8=!?T*S0n=A5a+J[gLcC
%#M8>Ej%6+pXI:1P\CZr.+P$WS[7qWI=GgRLh"@d($L:a;'ST85g'o>5k\uuDH,OtP5'c5,'(?PDa4MYC&j5KM5Vh^AKLFIRb(?nY
%2;.utE6josYN^/Z,""rC#T/G@GGkfLLg`Qe4jNZa:cQ#c:V[Z>';@:Af0<SgoGh<9=<Y391IgTpQ/UCqkjZDj4`"2+kGtN5^]"@`
%\U)9*!g,Q[BV;:uR5h`-[e((SRh+Oji:X!?OUqCu\S/I"#HE]4!<D'*L#*q$[,?/L>+&Nub3OBl!JsO6\V`3_Qh]t$pct^KA#/!4
%5S+/""G6XGS_&>Kp5'mmo&3.8Tra=a4\!,7W+WmurXuf[,BRu2!^RGLU#8>pp@<Rd)RbA6]%tTg&KjuIK7&C5%c_>3qK.5C.UaSg
%!a#AI^tcp.*d65[hpN-c9n0Z!W%e?3ST].@(S9,cEPmjn\R=8;):dUI@sL&Jm\<-rU;m7'Q-Iel_.X:t<[;X3h?X<GZBWRP,+6@g
%&#OI;b&E=nVll&&[%KYDV$"OD-W=tP(ti-qA'!/aU=>$k(-O!)Jk?(u?YPAD<nu=>I4V33,>BC\bP;c_W.!fecatB-g;5+S"EHp#
%;q/.G05(%l%f,3I!R/SLr3!u"mFCSc=;40^aiMsEQd7.leugj=_:rs.rhMCBNq/!p-X'cT8hiSrM+*f$5jeg7W<q<4;32"oWo"S/
%#4,'NoM_ls&]lgX!)^oa[8N*_CG0Qcn^uiqN97;9Ying2Tap;]9Vua8\1Sd7,?2U#ba_.0i(i)33fpFZ.Vc%IHa_XSGpZN;4`Jnn
%$2pUQaSJR&]%\=i*/hlZiL6`9p/dLX-1"?62_:$Wa1.:Oi#uhF,lG3r8f(9(+D*ju`-(A^p3X!jC?)H`(tF-2Q*^:ITBh!ChRNH+
%=8_#m.-uk6Vc^\^.Hp'T`Sr8,TqRKB#OLHoj6j"!F%Rn?6J$iPT<G4AnJgZh_Ip$q?\(162o`O(FP&6V=((Y$5n_@"<A[1Wqs\00
%1EA9ZXRb?9]_k:f4X?ZPh,\2Wh\!I@:gs^&8DQ:q8]/s#'?Lg7AF:iU6I"_H@:*)*C:G?k,66B$1iaDq*Og;Y@H28j\=IQ;r_k@I
%B`JJY`K)<Lc^cRl3Z*PZiXVeq&<2?sE.gi#7%1/ka<,6?E3]q9=<P]^i9Vtc!ge;FNY03(0?uYdc]bA\K,pXZ5[TAR4,c8,h8!E7
%7k$!e%oH?6jrP6Pl'FGOJ-4qhW!j#'F<qG6d$\0^.r)W[J,<X%[VkJgjYWF3g`i0,ZIhq$#kp+Lh#ZkKqK):?eX!SuSOK:]\$q87
%=sIi;/FeP.\2a*h#`H^2Vm$^hlYf45@TTIoT)l'7a$I&1rUgpN*)u\F+&!TR9FSdu&-ug$Gc<_pD]0'\bkd&>.l)p..X%Q,F[mHd
%IgD:iM_DOCrk]0I!r,K3Q]k18fRqdYHXp[8=X1/V(O,d#h1iX*'/dk`UD"WUAJ-Ji'$#u%(iH@:"4QoQ.+h0jQsm'c"g[PaFA1Js
%fAfW-._mV3P0aB=6BpH9*hc#\>4Pn1m%=qJ>a>A84b=C\EUXD)b">+6DYK=E>/*I,O0%*k1hE=d4^'_ED.sVhYsro?mb!^*FU<Ng
%gQ`2aPS]J%deI">nJtB`GJL?5bC82D&d*U9H#<':dZO+L(4BKP!Vp'Y-$SE:]\)TDZAPi%^9Wc3\_g:XM.@ft==.WC!dYKt\)10l
%f+[d2*#-*'k8rrI\h8(lR_s$W>+P1();KIbeS4%\UO.FVjkMoM69L1)8k@G>DT%Pa(KcGi'o<rFUllQ:G&6oUddD)P":.iDiK_=k
%qR"qcGOB.u(%<A"UB?2)LrpN&P8oQEeiC9Pe@0X`1`GMhqAm"F+H<Q<GC'=5+)!BIpK5f1at\uZPUb,L45>uQ*kg/NJjR0k`7"SP
%VhfP1=0o)hC,7ju.dUa,<2oh-AD!.l/5,pnAHfi%?(`Lg_i<=a#p!Z[`U'n#YLu)#D0DHZ*(#eAenb,c?6B['LVN<4$:2OAq&h:8
%Ib+p(4l\@rnp..9CL\m*be])qWn/<H-1lUlcE>\M,k@Zs:dH'N5kd8hOC5b&X&*\(CO_sQX2o_+A;CRX[D/9c,q;QN0B_bu1)_7$
%,Ym'nWdD%dU#c7sH-@##[=BIW$N8T/HN974X;9[Z*R@d\%<YNmhY_!$Nl\C/m&htUeK)hG+E9jlTJ55rRF/C%5^rrlq(k2e[k*##
%5t92ZLlOG7NI,Am%a>S-NLL1nqB]>O.<b.@,UDdia8;t/'bX27.Gm;Kdbm<R8(HEh-%VW0>14?]R7ib-;(/t95H$$@I%aIS5&[""
%Q.Ue(S^4n`lK^NN)qq9fqFY\B3mK%KL1so,m'HE^')i:oo?[dFi1$l/7+\od1Z0fb2IO]ApB'AaL2D5g^6Zp%1!d0=/jSe]]E53H
%'M$;g]ULMb?@f.ps(k1XFIsRd\&gpHhlMNrjj]U9F9o+kXh/'p&Qdb88Ib7.&i'7Z_UXus\R@%@\_rV[L)Z0Bo#0qf;+NuBf(A2J
%1A]LAh*TE5Jck5lk9U7T6f5EsRXtmJAKDVEMo_"g=q,_O4\$nqo"Vni$Uah'9(K`+<"m#eT29Qjrct^3cOREfdfJo)ZOZ/Um#73s
%iZ>/U/8\q)b)ZiCY!AtZ!@,7<5LK4V7oco]5T2kDE:,:I`M[B$aQDDCh0'fBgdQksFH*bgW7kese+4SP<=t<3S.^]$4:Wd]``EjC
%"LfT0%!:1H&J5qj+sA)=CcWRcQ@k(32J]dMd"aH=oN2I-2$0*,ig8T^_ruh`Ro&l6Sdb.bbegt9c6a`:WO\RtQQeqW;s7u-4IZ==
%A$K3=m`mL$GkLA?1K*T]PFF>)AYMMR[[&qN^'7?mY#MoVRE]SfQV1>;b2$gP?]"rB\nWCEJ:X\ETFcR[phNT-K9B%mhgI*/s5D]Z
%qK[c*!lrFeS0>Xe5nbu5r<ren0""O:%1MWfQP").^+b:D]C]7Qc/Lg6.`:d3G&gC3[Hf#s]KdABVhZd87)n^?9^'CmL=FIr)6)uQ
%mJ.9X8Zkle:7;jKW"E,R=m"OJeK.2ek;8`ZUU)W3;Qc8!4*mu%_O0Bd8_h/)egk^!hX4#X+)VrceZKH/8E@1gX15EKVec%#IdFmB
%\6CFP9$F6"!T,%5k#BI:-Ti,q+?tP*Fd;XfWjL!7m[3JpFiD+.UpF<GgWl&a.j;tk0%.`Y#ET('6a3kGqtgR>8)SMpANUW7]W):A
%ckdM@5RDD(.InJbA/T96AQ%7%8WSP;/p%>3*/poMj>QG\#X.s4<%6Cj]0[H<H/LG58P1CGE-gi$.?c1P/L>uCR%a.9<H$KJ\[$og
%</X-WJbR#d3thMM;lV/H5K9ZRF@Pn;Z$OEMrqC:FPL-pN$9hu#EXA*KE*578*?@<606lnHPC?p(86rJ<L3#5pk3:#>Tuc3oL3dJ(
%[R\LN.,>Iub]@NM1,67i,3ZUFJbDR]4Q<tUNMLR"$O\Nl`$5(sN>ha&^#o[MXY6((0m-p`7]M%ED2%Y/>q:d7n5Dqs#]l"B;=kr+
%RBhV5UR562E+rqBB5XR\dG3/B!1&r!U-S?/UP#ugPuIrEa&!f<D)Jra!=P7UIDU57.@=DK"<$Yd.XK>SB*+m&C>d:c&H=L0\^*)J
%VPr/"EMIj%,WCAG3:nEBM_XtuNsY".RDJSb4T_1mfD0G$S9P/f9eJnMLhejKq5gquho\.*aS[K'22[5I)q"aIpm)/8X*`4]m%qZu
%0g^V_C`Ku1.A(98dfdTX;$`tRKXI^T!o2]KX:DmWQW1Q[;NLhtl-.fGn*Cg^@54lNj&mE(8EWS%]fTGu=-KiiLLj8@_,@oVnI.2Y
%W"p,CS9:qaJ'3HN7ZuQK=GL7mR,gp&U2d)q&\c2InP)Fp_YQ<CXPQD`<PbM/b1E-TMYV\DP+jh:Ql1ghTAbjfVZ1q*'KD'TPNEcn
%6ANa`&AR0-aWq91Se=LtccV^dUW#C50M*Y<S*M@U,WDI:c^#sWXeW!*Y*&HYC4Cjfd5=[DEJI7Kb$G@EqZ-rCWoLYfFpf96q&@!H
%Y?A_KY;(H+W(2jR\s'TN_.-b_d/p8s6QcW(.pb#&M2Yo^,*S?7@"fPR4*ToRK4@-6ELoh;P>4dq,PYa:a*nE]Yh:85ba,8kNAgON
%WD-3r3hC'j$+Ln$9-FhU_AR"G<c7`\I>I>;5jIQBf?R=laGh+6R+ZIo2r/,fJ5/mmr?WLeccrl_Ya':-BdA^r`nGh[S\03IrlZCh
%#d^LQf<jsbTm#8K2$su/K9WY_<f-JT6e@=/>a2S39Z)^EB/n.;_51\?BR\Z[cDu8G4)-2-UW#2f2"7a"b'SGP?^%l]#QO+HQ721$
%pet^6&#ig'*EtSUr=IRa%PWQ'_:>krr3[\3Qtbi*s/E=+mMZrWR-Bt53%EmDD)E9+e\A)XLD.!"]"%qjb#p->7;51\)j^1(:kmo>
%]&-S3\qfh_q'i.pUr<)uE\9\>?[s'Uan<<@:u`%u)[U#%^r*!\gElf7p0WqZ;m_AOaUa;O0-b8\p?IdeA!$_rdtL0mE5A<k$?_LB
%?`(W>MpANt2Bn?5U[d[C%(DCUl/Fi0YCd1;@%J/E[`Tduc@Q7N$r3"7"G,MP4*!No_X!9@LnGXj6-P!C]V=8LYmIU.f,UG$1OKg,
%UC](6G7T*bm!UDI$TkU"0NJpp=RTpaEB3t^7ds(!Y*@d7qE>C9\g,8-8GU>>0Tr/Og#N.JLd&RnAZ:<I+Z::XNbIKNG8=*)cF'7*
%2*]\@?Q,9pYRXXS>i2l;j3L`a7%0mJ2Lu+Sg9f(EEuiJ*;m13^4]p1fZ:PNo?rGai+Bd<_!M`-5]#R5n]%;@&>2I@(V`0X!eh?'@
%>YdZ6>"Jb"75B#LTq&/dltpCWRNX*Q:%Kol`mU=_+Y"cf-q*5LZ4]O%Q`Q)5YbA7"Z,FtjJ/ek(Br$;`1,06?"mH0S*dR\UY)Jb#
%*n_=.f]c/[iZI2TFD$]c;B5PIO%EAW>dn&Y($l=na"J,n8k=T(N&:i7NYTN\QuRJt/EMPL2A@nLfaFO_PW'igB6W`n*<RCZ6T2p?
%Kk@p=!A2uq[K:5W/og\_Ve2_E8^%5Z:<N@1NWpsAJacF"PNU_8&MRCRI+M_UiNLHl'@)"c!iT"]R=QDS?3<a%m^`K<9DlCE.Cu;U
%r]RFGp#oCFOS.ND&?iP$2NKIE!W32=!/XuqWp:4k@*79:1)GusA_hM9(t,r%-2;%1Le>\=)/ih9Ft$u/=3>F?j&6h`&-4ig+OrV6
%8fCI-<"`Y&BE7WBK/W80CqkT<$k#<+!GJc:NNiKL0u>BU7lt6i.G/9*%j<f5GjiJi[]p@+?l-Xr$Y`Q!6WCsR?B7o^%K>;.1i447
%pkB.R"Z:LYLB*D&!;R1eV:M$bKSn0/mV8=@-(aQ#H_N%n.*]?^AlBtuUheJ"qp7L?YMCc:3f*Z6ZuKd$%sD1E0R*7Z:Pgth--NN*
%=`hX<+F9CK4s0\]-h8#)R!+q)LKQ!EU6A?P/G0Qf`=q#3dNDGL=Ca&>G6Y3r"\HkC*$JJuX?'fhZTL^76H34S0HaGs9q3(*')q!i
%msn,3b1d2b7*!aCb/qJ5?m9&W,DTUO`F=eChH&K>64K7%7M5p*2nl&VnF5cmPDn8@^6cr6<aCqZ"g:VO2ZnH0MHk.h'8JF[!mP8G
%\L6,<-QDc]nl@OR5a77#o[QDhX5c]`EB`u1dkuue\i)5k%>/b"pPo_R2%;f$GdOR<<0>pT\"?f4h3d^D>L@)$/#ps8*[s&,4Vauu
%R.NX/^c;!=Qh3+GUrWm,-$b7D#Y!>qKCY#RO\pP%N6(#kfZLj<]^N8-@$@P#oJJL+CEMf`iXGp3Eeco^[J"dJm-2`)`K@(%dolrQ
%g\8\sP0?44PPa,IHWIo!BW?_o7^\Vf*50Mb9pY>_\K,m9O&1hicbD$pNTE_?Pb,X-V3)<qjb@Rc`u)*@(Fbj&[/.&ua?2)e:8OsC
%alb/]&qss8!a8UtO2s5eUEN%0,DfAd"c+P/)9oEIfKpg?B_G]K\<N078Yo*(jE5bk36?5I71K\[GRZnm*@Ja2f0m$o[jf[]2/DU[
%%h!d4=+W*(@C!NWeMnmQOEmC#U+?)>&^[9#A=cI8d.(#Y^5IG12RbbG*21^AT20+5L1r^E[u.[EOC6N@=XabfgFGnA^E7S\fl[sj
%R='@2oeU*29BB&H(/eR$Jl*DU`)-ui+Ur^X7pEQI.LWMQ>pb!mGt8jRCMP1N'JNE^R(VdLKH(Von-\XLn<`[:l(ped[mrDIj-EYR
%f=DWeW!!!apXW?2"kN[u,9&.Z<nl.IWO8p1Z-I*5Cimeji0WQl2]"f.@64$VJ77P&F&+2!bam$1)u,=eH4W4?s..:ih&Cd5l?$Yp
%a:Li0eKpOF*5TG9INfTH]"WC22<,#c_86."&s(1^K=*t8GGW6k+oMmJ&8Ch.hH6r@0U\@'7MVR,WN.?K6mF,;#GrN*<ZTR4lZ@/%
%i.?aiCH#"92gTVT2VTQF$_7!u,dmlTH2[AT3ruOgO^'6ed&Wr<Y^@rSnLd`64'#)!<?lZV/;--iX-"=1pKk$YT;?'4.\P-\>Jg^_
%h1"->Z+a<t'OC7"DfLJ,dO(,fg"[/C>.HEJcutt@!e]fU.5(3L:Let1&JGD/]V+UT%%2o(aCW:`VX1XBk<(),<l!L/018VbCP;YP
%T`ZX;TC?tm3_O>4K.8A<b,sqjf)49GKYo,YR3H2e_A_:>r4))WO\oq>bg?*L+[Ii:,$dCQB)M1fEQ?@5UKuGZP(S,dP+aC'\HIr[
%3K6:$OY_Tm>O+,Pd[8?bZs[,?bcTa3I2-IH(F*hY@Mq!@T=Z0gZ_iNO,Ng)!_T]HOBne'LGk=j_]P;Dl7pcEJA+H%a28p*T2qQT&
%n\MS$H4Oq%DqN_l/4]4)a&ceO8g=Zk<LUgTFA/dkct,\[(,s;KX;bGi.^PoC0!>KfW\CD,hOM+#\LArE[QK]UqZF%1EE629.]+#K
%_YkAAc+"qML_PBMb;giSG7F?1C&D5'pQ85#Zq;)YdD0k!505o7(r<&,A\p[h(S`@tGm)qc(<VJ`*r]b?HgPj$grp)4`KC0_5WoV#
%84iTm(u5Z_;$&>R[A;>`%[rKV%$9:*\rJTK@e2<sJ0Mmi\BLe@rEcU;c\>Q_d<aYs[FD-RZ;3Vl?:Rt@!7Cei'g95J`nQjK8)X9Q
%Z`hV$7C*dQ4QU6p7sk1\<g8UK_a*;[W]X,i3@LJ,4t,a1^m#C>"!F3-,l'Bf'[=b,cRXe,Q92JA9$]g>#_&3Vb2uXq>3_B=CfZBJ
%e@&2tIWME4bcrT<5)Xr2fKtc!`G_*G9WUmp`F;Zl;E3n^n&?Ul)f_7i)e<QU#KsNje=RK:iDoRe,A\QcXm"=G42R+VA8@B@J`q`Q
%Yo%n>N+0"S,#sI>M$a=5W>R$?e`q_-26l^&J.F)!S.6cWM3';H=!55=Ga+:kF]<q7_H#G6%^dp8_K2K''kuI&E!^A<6UYGZ:T6Au
%[sLLbZq^Fk2"!t>j,H\1[@I;()'*al\\!^g)+MI9d0Vpf1kkW9GZ!%KADG0iQ-a,N3d\B%R+*FSIjE59Xlo(CZn)J*"4JCgKVtM#
%0QaX](ldMSp1(eHI`/+;P.Us]R^hGN\St8%&q*W$@0Gu+EJ-m1RB18\:phi[-auR4DEQMC2+><<-E@l,qfB]qNGRr&?Cl(7bI9n)
%%Lr,a"M#?&p>VB--STTnY@Uq6(hrrJTQ7T%>DJ7^P2]p/6UjI.#)%a?0c(<Xqs]Kt35'Bj]0[d5kgI?d3g>f?o_'d:7n1WK-l%RB
%'S/!cR#V'b=/FXESaTPZ"P8S>6"f=(:(K&;YlTla(,G&X@N25MVl%qR<0(MJ*fVRl7HjeMQ=R:3q[h62\1at0\h\+n6;-TG!*2fS
%OfY*+WKTYq@L,/On(=GTD629CE8Yr0Y5-pBd]l?R9da1GZq'Y]nK'Yq^gHsf`1N6qq4r3@r=lM[T:Q@0KJ#[5?rW7AnqJ)F8>+Su
%oskQ(n;V%W/"_@1c3-J/Bh<;>2kppj@%PTf+ZggS:9jH42[-d\rLBtckb16V/*^[W=4H^jJQj7%TD[>75!I(nQYF)AUh5\0mH,)K
%-NcN3,@(s"E_UuGMb;;3`,OJEA(rFX3&p59@Yjs)4=eF(Ig>D:#[lHiS"ngR_Wha#bm]i66P3'!'Dim<1LXJj-Z;$BMPoPe@$21c
%JHlh:%Unlr;$%;I7p,R%W"AYm>YZp$F_8-q=P@TUXt@_c34W@gYH+4eH+@<tdA!m1op<>Tq;WFYB&.>ma,^n=`tZWT4J]UGF66DL
%YsZGBMFlHbjNQK'*443QWK3Cj]p+N=oO&St;H^N)$)qB!Ga3@8f!_GGa9X7cBPL`ocDj\?3AMTGdH.d1e`Cab&:r+"f$AW<2(+.8
%)c![C7JsOB&Mi/&7:,<U1r[m),Xl&PK?T,%p/!h-=pNiMHc2JWdau`%0sjSfVne4.8sB<\_*0T$eCRK,S1;MAZ$YnQ(00&/J9p_s
%WQ(oN'lqVc;3R",E'(G,Yt;@u+=fq4>h1($FqAU$XNQ2)HI$Q0Btj#6@,IAMEjlA>WR/Zl)n8\<g;H#177ef+F(HW>:WYd77(nAa
%!%]njp>\d3VqS)l3SU3%D9iK&Fl^_'V<$!H7D`+a;K\gfDp.9d3(IRJdK?-$+2?+]iubmT%\p[Z)XE7mU)u\$&r1[E:P^K5I?N.p
%8VX!PX",)74]e@6q*B5r(M0(u^)Ro=80X$:[^eIL`aYXoX-T$(_:86>]EH3[`.$!sqf!&nT5)e,CLuC*n^X`qfEPNt.)5KB9#@!r
%/Vg+'.W%4nS;8TZ3NGYJk5(Y'lIK/mX2%^d_"N5*TTS?*kM9trTK2PcIl$eFm\[%Wa#^_j_8jVKZ_QuKR<K['[Ck=5V<qCRVa[_(
%?QDY[9H,hbe,_:#2WQWSbj\?&%kZ)521=%S51`;hmTc\G"^R*c3g7#oNdGnr&fI]EQA<:!HC8Fl.nIqe4^+H8_]Js%<jJ<t`C<]]
%k&JEp)+`m)c/(G5U[*",lN9aXf8Y4s'1/6@=RC5t8dbY4B17:[MkTFV>CD4:*fi<6#drF5"l$%(]pZ$X\$7\TQr6u\A\:@0Y_PD\
%B]L#[!nH*%U&MgDFuEV'h#lTj^QoWX%)jG.KZRSW`/SuR=O5rRS/Kun;0[^$JNPf]a5b#e]CVo!!Z8YpKo@Q&DPY]J%&RY!!hYYO
%<:!fj54m[sV7R#TYZp.2'586;[II;8RqdkT)sY$AG:mAiH[F6-RD$#>NSa@C.]E9.k_P;$qD!WpBaU9hDA!u,_l/6MU,%\*=XYX9
%-4//[2`i6m'6&5BrrC<0KF?"pW.>Khf3=Z(F(7'3Q\_1"V?d31QN[5:%a(C<>(J$0-3kqLLd=qn`[4D:gKV#?!'0aZ9NFm\1F5V=
%&VD_cjr&>A$_HDV.Z)TK;UUCK_(LZerWL]#r5NdcF#Cgok(s\K0*%;/Y_7;25hi.42o8AF$@I(Dd-C\!-iu>l>h"X$MBY!-Q-`q@
%AkPGWATDlS]g?!NV`_&V-+mfO9F$^hL[CH'^K\tX[1u3C2p$"KJiE5uhCHT,7DMQ20I-gcL=/]H<\]T<@q\gMGa0BPF2VTQ=OQ8?
%RJZ]:gu;_-X[.@>ep;@dnqE?pB4udmTNjG>-c)9*2+hR,V\fPsI'o8lOK"*q)IEASpLGL]7>N)>/Pmu=,;^$;%bV/qQdLgFN-C"m
%e88W!aS^KVQ9uNuL3L*bs-Vg3o!hI6Iut9;i>^,4TS=d7T6<YHUC7\0=sc1GgmHSNTR3cAO*..HUSl>/bIQY9)QJ5G>[km=":^)V
%`N-sD[()tqO(qLF(M?aG;3a8DaMW!OM;8Caa9kCV60stDM(V#Sc>#d&&]7NS9an5]bf<&-8aM%aglu"6o3p02TNWoJf-I%#+KGKF
%K^j/hiM5^D/4oV0-&#kpNGu(J_V*_!AjZP@Y+N.B<9-95Z;+4mD^XMGh;"aK%uF0X[1h)>cW%+U;(psdF9N/M;Z[k@96^ouGdRkj
%NAu_UTahfdQ-9jk)eH[/&9?Z_E0;.N\=DZ8<V9^>HV*;]Nm'-Y)=ljTiVCQ=H<m$dB2I`J:X6"B1NTNh'`L*QLO<,iM7!)_B6$c#
%m-4dhi+'7BqGNW-l\G'A3H@^Jejhh.BQ^>fMKjVc@Lf64X?Z4F$0/`L4XBK8P>AU,YlgHZ&S.6apHoZFM;uFca;+f,0EIH*R$RM"
%=R3eWgMj58FYa%EM@KBmmA-#?Ih2Nb-8A`4,k?^^>B#S2Wd@VY4r-C%C)nlNC*3a1BL9<lQ[!7$c%^]iPX>S_%/lk_:BH#j+QBlG
%(%)`8EujQUr0ftU7l[-oB%;JSh-S9I6W,Gjq,W(/[RA-XpBj?8Y]&)S7I6S1582!Z<,OcGRd?1=nNR[^k[si>?&j%*RWLWY5hs1(
%-(T?m)L&&_QtgpbVlH9Oq)JjQCq&dnG2:Z6!`UU":a:'\;h2\t&NPpE$fq/@:I+IfAj)jV+7^8%ms9L&Qe6I[mt^Z^`!o7HA+;b4
%/QlIF(E8)++jlb$X*F5LTCrq"QQ4mW'HC1XZU`-nk-`.M)uWs?$\YEli\f6]>hMQDpYb]Gp8O?t.G#]O5<,mE>^kH7/?n0HX#JmX
%0hQ'7X_,qXIRn<gXIFAHJg!ph!K(6&37bqV@+jr&On`fDP$sc9:f2cD_S2@VII@p%`4aGR::Ju?#kmW!g(4mU<^]Fq4RO8SD#d)A
%JfaV,93N4\8VjC/#inBA-PH(V,h5b&%,DDh"]4JOdnYWr6VX;Yj0;/.bJqD-?h;Z=aF[GG-8&Jkb!\CL?^?Keh'43R;>E\!pglQb
%@gBQs_<u+2I1t1gk@O*k".Eg)CQWX10@*chF<=6o@t?H\!mb*%Q>'m`WGh\\m$q@kLqVr\A8,c!>kSp>?AZkA80#KZb\fYSao:kh
%oj\YbF4e,&]#B[dN3$fD7\u^!>'aXf*Mm>&.ISG3W[.0A,L:ndGR%RFS;:Rb_M^.!`/IQlcI9k9+K"mm:B^b_]V%_'FIa6;Jsc>P
%2`&g!##^B:\(A)(17>0$@ONNniRC?BAKs;lP-X`TYf*KIqnr"CF@RlT,:IcTJ'U$94X-e1[r7YAl5Q3qV2'k"7N+92DdlKDp5(8#
%ghCbJOM^h8LeF-V=!F81]9dR'go.C31PTLXhk68)&E=oQ6rFpC!NYI1+nP+0XU4d`$]Ssp\>J/l$,dgL%L\ekffd+T>q"k/J@?!T
%,+_je9BE-WP`EPUG#qqV7!8&;]L-E3C5!=XHmWY45bY=/<FVPA+HQL^c=*KBb)IB"8.SpV!HCZ86_>ff*LP;u,&pM/KY`^N!J22T
%G;*@6Gp>=H"NhS;4JI:`]1\qZi'cjUIlDR1=b:>m^r64/=soZ-!i-aeV?FcgOnBU/:C1#Dd<1Aj>0/".Hn6Wn;5B@p^(GiKF'OH`
%6KQ<'3LbU_:Jd0RUV/';r/`1u.u*V><-ED6%(^1t#b@DDJE]?WCRq(@l%r?*LhlnEF[\P='uY^gaDM$YA'/H>V0YYWJoG7&UM]8d
%lnr%Y1;5[m#_9b-F008d>51J[Eg/5V,J.j8fu]L1Ec#c$+m@jMcE95t1NNA-\,NDU(fcUVLsJ_4PZV]=P<T5r)KWKs:XtH@GKV<t
%SKPuP0\nNI.[epp2+Ejik@3,EQ`Hrdr(*1WEaF/"B.nbCe47]OkV"^dE:<0@dLE\q_ZaM([>5,URd\AS5a4N#ea$Yj@HKu[CdgbU
%CAB)=NR$.RgD8JgWfX$0#/\=s:.5i-&T)>/EF'^;rQ]]c&1P1=GH#'^JX7Q/\14Y^MV`mJX4'Me4&dXbnmJu.$nI+hX+DQu"-;0_
%H7q0>Yc?2D)k'_q/a@Up,a#Ok"X3P9hOl9V%3mWHHj016ASbNR*S'Egc2OOq`=uE[_O.?uZa4q@fn@g0M)HT8O(Hhkr>F+m3-3+U
%rdpZnbMb1$!?Ik0Wr^UJ[/_Mi3Rs7S_%J4*0tu9rH,@j76TUJ*C5-p(0%A5TQ7T+48WhuM]#mq;#Ed,eKQW=f6BV00:ml;b8$JnS
%E6qff"W',98\Jo<)="HEr>H<"`b9E$0?a<[W&W8Tc8VCi-]9%r8oPkOi>'6U4#WnfFU/!-j];2Y.G=$I>2d8^NeOWJ?.\D^fb5'5
%SCtMHl8T54,DIE#:qER#"KDC*T[uk>D#kC@93`j:OiX9*Z:<]6?:Ac7%hq@kf*r>Z?_k@6E_3q@h;A0tJ,9+fs5F"UVuQ=[J+NDa
%q#*GVmlu2FrJTS4TATS<s7l'_pqM:X&-)<H^]!'ipuOI7m5I9.k,87TS)=2,J,S!Vs7"c-r6t-5n&op)pRcs4gFn'-!.Y%GJ,&!G
%Du]VD^\`uqH+h,arnc!#rnmC\^\_raq>2iE9;oT&bjlj*rNFu%rrouOroJXVJ,Trs%c2^2?]:WWM[@/gfDjhprok<r:]L(_&&8)!
%5Q?5SVZ-H_rp0F%?V,#DnjQS"81Y].XZ\),lKa:LCM@8$0s4U6^d]?9jcci87uB,nNUC)o=MTIT)Yp>eRd);r(pit6@Q^u?c:!V?
%9SL#*-clsM),]Oe>6d7E-CgFjAqF$/AN27n.35jJ*?BXjM`bnCIknT+6!)TmE*X9B3#7.oeag#9C;'_.J1m>54dVK`I3$BbQj/`+
%6lJq\e@c6H$]0!Mh3&CZQRHQ6<@3$dGkIi1;Mu\IbXI#C1%F?We,QDY,j`;hD/bFm,H=\T+@S:D`6E,,GthP'%m9:_185#m(.c:V
%YHGBn%m0rkaD4rski&>lI`UR5h$Ni%qCKJl(q1eF&I]4h-H\a`\l.</"KC?N3_$XI@"cF#"!Vc`iq*b$0a^!ef:+hi7H>gLG*;t[
%nDaqVOFh'T+%[DS"h;cDL]7MPN7_^@kbF=*D;pjX>\dP&_fhm/8\/k+&bY9'R(gJ!@2EPgioMY$E)=c_6XDc7Em`>Q!V;T'YX`"4
%J=t^^&<(C@5;-5\A^ol$hmssL%(iMRYg4fnP%#Ss2e;e'D;5V[&41k@%kt0(XM0;K;D#C4eoB[aG#d8$#e0.4i72YY00M5>%(-R)
%$![(p43UC:=4f#pH!3HEn@lTVlirAf]uJ"e>K2%:.jpOd7n1YiH.<o+k?pHcga3b#QM@5cQ>R?WRSg6:Y:To'Ysq0PJ2MWuHH!+E
%g_%JeF`e<NZS3Bd[N4<7H7qgM<%/Z[!^)j;"<g;5[>\aYfd1%$mPiu;-_/l[;M1a@.\d%<OeNkQ7PSX"<mh[omf\.\7p<o"Xnse=
%)OlP8UW.8,5h0LAPIKsY%"<:)Gb8`85QMj1*<;0@OmLX>AB1L3$%6`F7O%=L,f_&MNdF,"WL%[3i$7q#B1dW-o"\'r-*<P(UhOXp
%Cp$uGTN/6tIRm]`Us2d.NV(P9_cV&OMCUdl:Cs0+R%=A)#6Z;%QJb)nkT+P$nqh?g0oJpk>4$M"&@deBGrN8^T<7`^d?ZCC0&WjL
%%,_](*U8')QS,j_Z3G-c0%([c/7)fE6*JMuNU;[-V\V"Nm&s4W-1'nA@^g,U%Y%UXPHh*3N0m/?6june).\E#$LqHs`W1ak<GEq2
%ePl:e73h,fE`>j7CNo^VL?J.Y4uQB_Uj[3acQaO$]#W]jL1r7`Dc!'j0$OYngXAhQWW'-cYGC"@UY!:t)!Q5@a""[Ia9"A]9Mp(g
%(7GSSWG)i31cGro7R]p<i.&3D;<0ho7</I_7T?eC=n\k-ZEq.46!:+&F(>NRfEk6PaF,$S!#HsP^rrVFj'tZK,!Ec+;7USg_7PPQ
%OQRn^F$s#?L&5]m(U`n\48?drW$`KUH%`eR(1!D2VqS^*D*sW1-cgP'l==>@SN6IDZ?Jm+$Laf?5h!tP_;[^?11=ci6`O=)`drpX
%m1aSO%ULQ[)Y+DqT"1(:-_o:bJq`d0,]>jG(jcs=%BD`>NVb*Dfj/l4S*P#Md(\+Z;.<^3g.;H:^Xl4,A[8g;<sBk+c+"FFc;U-(
%mMe]&e[`H8;rCXtB'0pqD5*Nt&?^QG$cJ-:3I+kreZrea=LT30Xj!KRKqaN.S^s#jYRrK'2=D0Ko)L?Z[R^cWQjeX&Zp0XkmV3;c
%lC#/%LP8"l$<P#`P$V3Y=X`U''k/)c^t4IPH)j\e!->?29.'iEf]L,tI0o,aq%Jo"(n1?KlWpt2Z1WU9msa#p,sQ;uOZp7]-%P.L
%agF'+<qjYd"\aA_s46D[A'-tJD?Xqj>tPb8ZGf(-!<0A`:&7ek6Ih35a)i!Df,`A*s3=U&_Cq%Li5B^<g%4/\5CP5p"ZKE`:`^7-
%$f$nljJ_'TH@,n6^lWZ'8eaj?j$]Z2JFqs>:qL>FBM.@moN[QDUf$F76P3l>d*82s?)LW2>rZEUA3eb5]!4\9Hido*ahK],&f-WG
%ln=g)MIg$<j3*cG/H\r&3C<,m<6"(PG/CJcqSC=E@@Zq6)Q<]3)/mnjRtk%(/#l$;91JH4qqfp<)*E.B"L@hP?GUcNI256l=@:Ms
%VEDtVT$?L!7gtYWUSEK9/f<[bFGqEg"OZNF4Vub"9/l[h/'(HhL`Ygbph*Z*d[9#F'+;`T-TOEAMS5539Wii,O+Yfs@9+$XMKl@`
%0R\V;":%Z)D8%$L,(,#kl!'mG5U,k<p9mepkb]dnDn=-IepRe@[a/Q=B;^*W;-qC:Q9Dl6kG`_^Fb.HS%Hlbs)P'0sA0R^l1o\8V
%f&oGgibYrm+mJ+[+CmQjBH'on\5[W!FiV#W*maH.XS"jB'IS7=JpeZNUe9Z#B+$n3-F@dr/J;Ukg5+55'=k4#X+t7[:<eZ,.e7!f
%!pSD(^BC#Bc*6XWEj8\OVmRk9*Rs%H5Mg9Ei<M9ed(ZC;k;BemE?WiS/\rA`7*\?kV7X3=Tr\Ba^ElSYUss%W\e9ZGl1Lg?U2KuB
%H/EU95+,%\T="[.ioOo+fpC!M6^_giQu#a&e4pL!\"^W[KseD)F$qi<75gEQi<1aW,8rFO8VB6*%&E("O2j-6P<cfll5V:Y.o?@%
%g^Z3;UV#ZEWjFrXZH*k!n_6nRD:]&=`P$tQPWkqZe`&I=Db+$BQqIN'3>pe7DQC"'-VAig"[\UggbAlG7j?'dVJJ21re<L=VIJ4:
%jDIREX7#VN0kV,R"6ZGb%u?RUaENt^cKN#=Hg/?S,#>tDE9a9>KU?.$M[UiB#8C-BC>6VXJjbmt@F^b`HP4.%<N[SnFm(NpfQtW`
%]hLgfT"Sp*>8ci#>d+tSgSU>FOmiECi%kS)9>Y_#l_3jid>!<*RMPQ%<A#>O\D07m\FS/YHen4%b'QdoCtA'h)MS.Ond#1Q/IB:o
%\=\^+H,l1-d1Kmsj\?p\<1V#F-&EZ$juMjUAAL<nk3bq.YfBM8(\Gs$Dou&-NU>+ok]`.)rJ.=,)]qi>AqbbkHB)G"XZFrfb;&G5
%]!Bc@eEk-'i7@TA%->KQL<$K*_Cu7iZUbdCba?5`!/Ql(U/8Yj[lReC]I3;[7%(U%.7X@D5fh[a_9*@'JUWJLp!UV;E>3-*nYj[;
%h*MH9Cn."OnpSC8Y]2F+$mbmI%*=.1i_[`V'Z\.AaCB@k[A-a>P'm=3-eAX/pM%=KXT\Hjg'SC?WW?(UZ^0G'd0r73669jZeJAGZ
%Jq5"D`.11CD'9Bcin.-Ahr9U@.[j+$@E$GhI]\DmjkjL<pQCI,-mS'Y\!k[.r<hltV/\.7V)qTK;4M^H%_A,W]lNUOoA3srV\i><
%J.^!G.TKbkCbMRU81MD$4ZW70QR=@n_mSONOdeumpUanekV#>fbIPqT%/"rj"#4_SYdo!Djkj$s^u(S.WY&gU4bL=?!RH/(2r45C
%=u(ZuWjZ'SQ=qrf%6OWZd[Pg;m*GZ=PDbYi!*nV,;BDD5>,#7nV/-t/P.uicnUsiY:#E888nfbq2Er@EkL&%"d8UQG/N%B/aSI(n
%8/MYfEA:l/SkZ>i(gq\F^eCgBN6h[C,n"3^OP5t!0-;?K\7n//n,dOrBJ-)?@%8!...4e2US8L\_,5=Xhh^)l0`[u^Q]W-J'r1>`
%_anZ'O&4Vr%6GPC5;(b9fKqoV(h\:g%"O)LcZfVEWFuN%n;HWu^#,q^gTE))9.u7QW>s-"hgPlP*-uWj.C/p4T_ZVu>Q-b'"o&0U
%.,aIaKUS$=<(OqXg4g-'\,5Zmi'mW2]nsq&$'E^DeA?eg6k*Tk;OU6O=8KQ46Iph%PniW_oNYp\H3qJ[k\[t:2g&A`^$JI=\:_!5
%H[4TVIRVj:Hd>11:Q(@-1j4_1@^ENP?:tDf4;m_!;ogouK28(a_EB*F1Z,fNl05OF^qHFcLXa/E+9mcVg1(^b\%I,3L<)7UP3GrR
%&0aQB&eqoFO3,/*c854<5CWHOM[*H*QX$R+Cpl1OnD.4q;nluMnE?=krW=R#9i^cKI7o[*;a)F;&ZQmfSkpl0UJL8e_CY/SE<j*h
%Fi>c(gKHFoUcp1;AFCuk+?Cmbn-GOHM%lu\hSA[KBi:K?W-m4+HFP.d\4)(kWh.(Gd9_OU;X60Y#4%HGKoS1aL"d]#flJ5tr]jK:
%Nf:Y&=2Ud`=nc^ZfUQ28o#DSO9".'BJV,VsU,b?F5+ZG]#;I%#Q9urUe-/CG>016On&#nif(j!<K-+FqOBsO+njV1<\]rnAe]S:X
%oB'BOnb>JZ"cFLLeEt$PMKq@8](KAf;qoD'#hl#>_eU,B]s3a-LLAJr"eDIYN.Xsf4BgMn4*CDd_M\AD7WQu*82;b9WQi3rY:!MH
%@B[X6pXI"ZUHpEOX=Ycq@(`6(K!Cf.Ra4J$M!V/)_^M+)NSnFlFPD^^^&@h8Gl?MZGog66XZ04H^1LXpZ=&A"]d"C$PX/hQ*D=S<
%])3\n8qehi/[Wa9JQoMcrQ6\O5o>FCAI?.&[q>/fA0eMpFo[o4=>Xt,q^Q>PWdr"2g9cj(37E?:aqQ&PNdH;S?AIb<O%K=%p$Pu#
%-M.MJ5N`J:;$A(6H8@Wa$lGqEEaf$48KAkimI@^+%%J'kq"D35@fWf;Pjeu)_$]g7H._Qs!P&a$F[^./>Z8!bTKF]5oRgAce7^/8
%,-*6\=)/#a8FMhCB?<[URj3t%$-IerggCh.B=*m*d"0.-_$A-p%?r!Hc<W"5)p6M\RHo&4eL'RsILn%$F;[lT5,7AQGGC6TFZGKt
%5Ns=23HhB0M`5*1-fYg3.#P2=p1mtbEtQ&sXNe*d!,8K'1(oas>P#TO]s"F7,(?UDDZhW4[d9`cltI'i8oE#Rro=NU:Y_Y3h>qmi
%/8%::PWX'-UMed-!8nP7[^p@0T#!qUG:1`i)Wi4+Wa,T2QUIu]&tRDb0;8?e+,f$:?Ujf((lt=t6\V.;!c6Fh)dC]k6n\LG2n[fY
%+?@P@a30`lR+&hP#9.lie)7POYfI#3_pfgl.aJmS[PLJZJ4RBMEV((t5MmG=\/ms*PTl@gkRaf8V9*4beh!Yt38<V&`hC&sLEsj:
%I_!2=N#g,p*Mh&pn?-+1/%.'+;IaU%h_b`F4F-j:,T&R*anhm4EdM`&*q8YLO<*Xu4Ham/V+c^5C\*eZh9SH?,46bA&uKh2[40eH
%%ai=^9Up2"%WC"LbI]YOi@/YIUIogR)%>Qi=_:(5V/q/5LY8;^"TAi6YND9IQhU#*T'r;7:?W7sE%YZPPMmbAVWHj2Tn=qUo)r6:
%MAXk*jdi;]$P%K)5f#uP#6'$IiVDA8\VKn*6-$f1ikdUt)u3p:#G-:<MB#J:"MS=.c0XP-?=)5ge\mCc[R9d*F%ADY*[kO'X)(o.
%>b=K-)),(rD@f)c)RV(EmLcnC[J]7C-tlErGm*'q:+R\3rf&5.re:`>Ict?T4rQkMg"='6-kAYI]\'`aH\rYLQ]Ma^9A_S90TnLb
%S&@al'u4nYVj"n$N3")^k+[p.60^dr_ETR/%+J;>Sf[QQ5HO9\GHVWJGG/Rbf8>lLj;`%J[^o>=a4CL'F96H[l)`U'2e=5Ho1D:6
%C&9+j0&u>@*bG-]N/I3D+uJ>*fo*9Q52h=+?QofKdojipKG2A9ZU_1=^$-@PN9Y8l8MKBIBQs2-chCZ(a30"pes1D$&)Pb5d#rVV
%B>i1lil3Mjpr_4io=IMc<[@c9qSZ8%hc%G;[i&s4R`&Co=+Y%1Y1T^G.cKAtDC.S8h7Pk4n8uI+F/]i2%:\_!B7Q>qVV+Z"E;&3<
%C!5,poau4lkE(qU+PH"`PqPG5Fk.A`@UtGPpF$bIH.DDj,\\ibE^.FjXn,L?>>1it8A'qNT\qoaY@+/$#f"7ucSSn!_uu83@4DfO
%(nNlr]Z$jWe43`><0IR"M&kO+pH6W+gGTmC>)C^fHG\[2=>a1?;;#!pl!jjVKU?u_GWO)08fZT]"rlfF/p6XkBC9X<";fUhOg+cp
%VU=\YVcX;,dbuKX#:K<.4%4WHgSXdp<m_N(PeT@>o#mr_N]ar*hH1@KGVpHKjN+9o#l5lp?M-9h`"J#Fr=nUP3C!f8L]nu>=9CjJ
%^gXX[&JbOo(h;&-WEQX@+W<B+K0V*iF:*n*RjX>B`5)1:*_>EC4UuLdA^L.5A3=o8rAe33&UA^7Ielr0qG-C"f_$N<+Hfr4&#:GO
%1DA?\)]gf@:=C#1/Va^hWL=H6qmsbp$:a$a4IuO=Coc5>.L3IU'QDo&mTikN@3[;oB*>,MF('$fN&^uslQ8Xd7-VsT@dEXSIhCIb
%;rRR[E2X_:pGIaXZB8%N?=^VC.Ejp8Bb&QQ`:%4f#84s<A?b`<FN>Nh]g+^dK)c*Ph;]JTn@&QbEMpLYalm.Y3FRpjKKlj,76sKi
%o+<h)U@]OPT-RJ>PiE(t>3*a53.cH>5_^s&JQ8gnYDaM_!iga<b^V%e'L,^X)UN=D\^n0QU^c@.`'CUtQ<?/WVI[o>rb5GH2M![-
%o@apM&m@l,-kA#-1J9h>k=@7A*D2FC#LMC"-^tA\C(V`<^`rDA2&ia8MC.4rGElL!VOZ'`gKj2::1%mM=c")'7BI6%8(MCENo\8]
%Gj]m^nJ8_[#qJ>gfW"3(N%MT&pu`ndN.`jb(49;Oa.80,El!G[C4]*/J<R47Q&=7ng,?baOV#%=0S]jU*erT`&>PM4(o6=\M0[Wa
%5smN*ac>Dm^@CPEiBb6o/s7<O`iGCp_"'ne,<ZNpO.dA<"[V8gJY&p!=[ZC8@$=S"n#@!nH#"*MJ.NF%C*6$'Hh%/B`'MiO.r%^A
%e53LjT%k498Du2/_jOVk'6-#)(/G"/<IY)[l;ZFQO%#48$8](JhVQ*&HU-Q^P,]cRjL^4XeMP#.)lnXTkWYorl7<LIWl!WZ.D37q
%^i\Ib:p`gcSt`$#gRnm<UboZY-)Q_PB#;Ch:#Z#$/8iL;Bl'C3!%9%?B5'5[]=^fG80p=M"\%<;ndCTeSBhNkElB;c\S(h"52(m_
%/Q`diSW5?`F125W/_qieR!X@W]FnY;eUW:;T[!=]Z:"[4erN[^;Bur.Wt&PNE!Q\geCaBVe`Jo;m<\eX1!^/p+;>W2^IFfbD<?1s
%.*'W)>nh<P85\e-Zp@J)FAF"_0M@"WG)G]67W*ns07lX%7A-\WpB0`QU,fo4BiL9*Gm':r@-$T\5t9:t;T.7"B,E^/6BFfOI5=B$
%(GX$\=j$Xsm=udToH1Y<mP,!EOEaLr5DZKk<=\i.[qaDUNmV:5BPdTKC1CKJ\,R5[%9/<nd4Pe'-CkebbLAc`4GQf$q!loe_75O^
%M#t^k%LV.[#dD5lQoZ%u'hL'!Gs4P?\/$af2?4l*>UF^U>B1WadH94XIlBX/]'$>1f*49/Un<Ef^D83DpDEc*N'N)(Nd\qFS3t=A
%>!E2ca1%\eqV1:[dmA%=*a3:'o$J(\_,h9?P"hG!f7S$DH>MDPo3/q(0R?9QD4L_riGap0(8O-1W^'QU!n@$=lk_Sa:be%l(2Y6Q
%)H1.OA[<Ke#i"X4B+H*?asBnRE:ql'mO5n+0XJ%I(oQDH5]pNoOq=o)hU5:hVlJP3&W$/(UI6YI"=UmiBQ%N@B\Y'YUD_(2'D8'H
%jKV^=n4,c65/V7Js!<@Ic]+jjMC+1BILBpIlDQsCH2GHrP:q[C_sjd)?K,Z=HB>G9:`O)M91GtLKAppO&e!!SU=3\3%bRs]"bYgG
%=l[H^+Z>u[\i/A8?(M7;%/4fQ`1RTe2[Y(@6=%%RI%r7_<meuhg&)7reNfqBDg_60]"Iko]/"M5:PS7]LL-RQI=1Jke<;E;YrZY4
%@B:68Oi<k09$^]-9c)a*\q:#U#bdD;aa]/F%0]\rgRTNW33']1h8%r1PmB':K=]Z#;5E&-UsJmOO)L`g#Oq2/.H\Fi'K)cS!2RsX
%;f_ql-H:<NK-a$/P2>V]lW-@S*!N=8_dZJ#L7idKIXDDVbq>!0jPrUae,l_0d)&TbUin57L=Z3M)n3=F<D,CLOF(B-*-WC9pho5,
%[#7RP)Yj6@K&oP)\oauCIU"-)-#\ZH30MXEklMH^obhR4oK=J)f^gA#lF%OlhT$-*n6>Nmjt-:8E:E_&FNgXjC^pqi8/(H[ITo6r
%;"4Xk4c\P\8^buC06p_E6q^7q)jo`ip!1[9c9dVIBJ$>:Qr?IW9a<dmq"DK]-lpA%"G=8$kj$f?.;6s<l7-[QPHof>*31L_^um:G
%F6ko%\rN_39@8_Ubo)U7Y7urAO)Z1p-XbGM*2f7>nlfKSs,qQ5Q(V8H-V+]H=9On*23P4MH=#a.qofoT.^%Rlq3+Y_M2YJF^34V6
%@HG?Da:[S)"1Z115?.]!>-O'kA.a:c>NMp^o9bID@\>rDb(t/rIhn<s5Z[\frYP0mj@Ar?]uU!F^K<HYrlSI*s*Z'hm4G-;,WllS
%%]4.>$YEgY4P(0?M>"LpLoi1B)'k6ReNUfV:/PB^n>62q4@].Pn0iI@mJrC]d\tk+K6hf>.*e@D]\4l)\XUcEnBn)aJqTA&FI7k-
%)f1pS,"R3E.\ge\1gphtbdmrF8^o^>Oq>U4Y1So(.DRUUGqK:I++h$qh&Y=[a'i`hE%3*`hT77N#GAs2Nu?S##7-Qb3WKbq;-cjb
%C/WIs*MlEN[3p3j_l:*4h@%8bICa"BhuUig;blLR_C?d3L2XGk2'NEI7eH4+p#kiA2YZOC>+[#NLuuE]/I-s-MLAp'2-s!$+D_Q0
%fl`!M5*8A]$;?X>ePF1oWYWmfUSVUJTU_DEV<$Q8Q.RnJUj]@@-WC[B-OJY^cTK],LMsAV6*P+TSuZKmb9)f[X+e#9+>LK#KqVu\
%M<.X:^R(R^&IWU`h2Cr:Lo@-#FQ-LD?n=?eY78:-ZLl[IT>G4)B1*N=W`+$D#)pk)ABG!HZ7"a1<i3JP!I@p/ooQi;`h$DlIDH:h
%=E$\tp-Y>[<q;"D$N5tH>g4jZ]>b-9>l`H%epsa:do*2j"q.kuK:TrrC#kqOl..l%dqPTUXuN''6cMJDcR^sPr;%Q5b;il.7W;<,
%:T?_l10>e?[4=LSW%8Wn!>WH\UZbe\;%58U-,(s46`\tf?mje)LO't3q$Z5]5m+cb>=D/1AQJsrknA'^=Af$6WnNP!X=)>LR5::6
%X'F&CmZKk,;bc$%Q8Eq&dMVnu&AC,GH#TEHNHasIgQ'i(pS+FQAnD+[6T'c0;#*KaO=j[Qh!OA*fuD2MTPRegm[p1.+-uCj"`tmB
%.:Q7)S_$ilSd(ca.E8do+f]),b">Ja!4f.WHP0/M.f@e]!Fta4!F*1K3QU6OQ1[^D`klW-Oro;03W/Er)<IqJY&i*p5-qW1GhDR)
%ZB/F*4@@Kb[45^_0)ls!=i:R;jr\fTs'@Q#ajs2si,B2k";iQmf[KteIE^mtHC.O;1kRr(=hBQf$fr*LE<$3Dg%p<%'(IQ$l()0P
%Xc,CBY5@"@'isg`=c#fH"h@"**kJtCNW:Y)J[eo&`iUS(XKl.#Zc]*@^a49./:N37LC>^EBr7(?7qFuI!&'-YI9WFPmm+7hdO""n
%1+V^bA^PRq(fTI4GJS9(n*FR*feY&[:gi\Im0maCgNtn,T\saC?W?Ra.9k6-GRIO=A=Mt#Ji_I++CB]9RQ/=`E>iH$"c;&5r_'RF
%kp<^jFZn.OHA+"\n#Z`Q_"GL\P0bHo1(,@dIYT5*fk66d$@KRo.Ma_LmXgX(GX*7g7.'1pDU6(!mX:"Ud\c^8;@q&ZS`Sc&ji!uG
%@O^l56`9W*g0#^9msDI&+-TsJ)pgRcY/Q*=X%CZbcj>g06./Bh*g]&#=XnYg_,uGF$K0bO.BT;5[tS_OnKJ^[3q@8gas>t?:V`1m
%'VO:eEE%26^1%6;%&j%03O1)bH[+=o=S.W5NZSsJA:p[KI+J\<L9aKE,2#(/2HA.lO^ZXp[%u*Q<a"4=F9aiE)1I^BZ!"X?e;crY
%<t^!WCNoI&G<B^,_m^AZX6P_k%nY%VI2t$(9Cf&".4dod7g9/l'N2%`f=4KI?<c-C@X'f1JkL6lP<)&OP=k\!35,6d"jWpT=/BCm
%)U0$e"A,6^k:#jrMZL-">/m[E_i@B*EATI$;!)5YpCVT$fs<0'`Hk24+G*"dAm\]9*:"J.&XD]1P`C(<k<m4l=l%mR4]FJ4P%JDe
%=BFfm.tgGZM;mWSmLja,=as-Z9h$IZhd*qCLb/-'OQYIIpS8@_QVDD'SCigp2.j/[St9r'8+Ban?a-'dHCKn*B]26C8,[=jq"E>f
%>7L1M"AL2A4j*;\&?QOgRSZ%!161j1]n3cY'H&qKa9!$p+tY;4G3tjon07+-&-FQP3c0o/*AcC(B0:"U0g@-"\isuHEF&KMP7VN$
%@8sshG-qfBbFo"CeOJihmUTWm"2mnCPW?F*?]O^`@uJ+0NP_"q+ARK&`ZZ3OP",PY*MZeUNG#E3kc^Prh\<Hd/%5nn+f`TtS"+-I
%/q'4U!SiOmEqg1sk_K`]7$c>[.b&uYrX/FZJ1c[;MA4Mb<$W_qkl>JuJKTb#kT:5eq;o--5fi@*!&mn<(Y-$l)[_-T!B7O&/k*q@
%1E-#g1eL@Y_/DF\]U!]04uaP:iTcVOd"UR"$IhmtaAkm1j:_HRQKPMr=paDN(:)F#=mB1i9N`!1L@Q+VZ04d1E8C_;UiD-M`i?3X
%gh*bIdN@M;,ie1GS5;^R:CQ\kCU8uZB062d)l6S0X^2nL4"l^VO_hE[4",0=D+DG#B]2*4b!N@81U!]F%@*C"c>Q@]bEV`HWRpj)
%M8Jq`fu4UWfTLW3APDe022N-dcED;KO&-<b))PaSJF*&MZBElki7<'BN)=&KZL9d0e6FZnS@DW27;9-!+cJcYo/N!)#f-q#%8j\X
%\@X2KCfOB6A'%o:#Tu[I_uQ`jhV^@;/+f14l`>XXO?D4mfVUV@dl*31)jO(\1]DZj>[hO"V!]=S9"8sd=$ZfRX#p85k8F#4W5J]J
%hHtQIk\kZ+^N)\m&OLI7+jWOm'K\5+VutttdCD)*<AS23bZjiKd<uG$Yk(t+;Ls#Q?;s?/ob#7lO0mLSb2MtI5j*?'T'eH]DZNS,
%KG+5'OZ1ifIZdVd#jPj-qCFYDP5e-P$$CG?R$K9\kd,A9ip7ti:&;Ka:!j\TML4A$R`VKT>;T!7#?=5L/Du%nH*38FpmBZ)E$:2`
%DQ+[d"1eLZEX9n<(bq/_`E;kBGVJ5[nS0R4P"`qAY^I@>$u-TN0u,M8<u&'I*l2\b0M]C9'f?AYF#^^"]DCL-rH3mF48ei.mhab#
%APIB"<+jcq-T#"M5Y8`/gb;.T)t+]+[;SglN<tZl42d;IEuT3*'-nq[[oQ[EcVf.2#DRcth=8_Uo[hngd4<+N"b\B<0J+u1P\?lT
%7!d[b6F/YH^MXbgYOm/fD+tS"j:\[;*iF@A1Qji"le`A$`46$dmu1Oad;nmNNk"tk+n)\H?$VC]]%YM0#rD&@*on_$`Pp6u3k+p'
%6pjB&.gcb10dn1!W:`LLq`b"f'T)MI0tLg.N63Jf*[u's^NRnMj,'PRmTns$!+*_$s!7J>/l^gQ7tR0\]eR%6lI`'W/-25G*e$'u
%Jd=>l2DZiAIB[/HNbSaHA!QC\6]?^VKp8^i$L4qGBnR'9ReX-$;H6hDs)QoAS41\,/2Sk"F)6RMdO&3hI;C!QpH-n(G,7Ul&LOrK
%q)H=VoeN].blA;Sb8$"\m.6=?GV!^Z^NEZar3d]bll6H]9m[Z8W<12ZBI=8/\ZUG8Di\_](<sD65]%taJf(GA\iDccAYf0fIQXDQ
%%4(B4%hHZ[MWM@95bH[Io>#JaDL9lmMF;1iWr"pCOl/]Nr#(Rb3e"b_Q9Q>aj-`&^`d=i`_L)/udI[?rG8&:=m8g4:m+=keR>jfJ
%ChC.oMu_h>D0nF/9\pD4!NL>o_h/o0l`&%Efe.-r5qX4+2H2p>g!Kc(4csRGeJ]#%"i#Ff`s[b6l.5^53HDUT%XUaRj"3?jQdf2#
%Q8E"e>c'['kF'KV?G8P[]Mf"t";k@$>t(+\#AB0)q,)`:9>+MmH\OQrhI6-))mA615t1[rGQb</c0No1lAElF_3?ueEU(EWiGt_'
%K6EY*!2FrOD&2;XI1ifa%n>^DA?<99Q^ljW(r7D8S'3*%I]UP,f'p`$@G0$:A4u,lb"fF)jr-HinaBI>o)VE0;&X5"qtE<>35>,;
%&%HCIo@G2DO.9nK'/<^IooEcifDC^i`%$ua2oqOaVGZT[Pt\HQTOuqK,pN-tCE1MqYmWliLSK/!:5h?bI;In+U\ueKX1PF[Me<Os
%iBC@Dle5#lb"`08XRpI-T-fh5\QPuta$]:?L\*V7Qc<1M7DN/m5f.%5!9WuFfa"2+VVGSno`qg+8n#!h^JJC2R8fKT8`LI6GYEsV
%r_<*[9FG%?V8ip\m]-cG#uZ1J"LJs?RpuEufalZ2Y6CN4St%rBDWNTe^Pk%6Sg<*WbS_7rf((mNJ&>\@cV@uTNVNScOL0/>keAr1
%NG'H_'4$;/7,8)l_WqlTpEB$t!=b=&1^=RClt3179e-bHie(YEQkaOcOZ:QVb6"-5<bfQPjJA^r)B0G3I>Po0]P/7a:j\8j[7&$9
%8IV"eY<F.m-dMouV:!>ic7RV'Y/<;1;f$3!C[##f)m"W"e[A[!4[LE/G[!UMM6rlj5<rKG63urJShg=23n%!DP<HgJGpD1`S6Y1<
%@3jH^DR!n[gEHOP=%OLe(R>R&H/g>ElG2HUC5Mlb*;RUFAFt7H'o2+:%+mTY5snhbfBcO=%Z6q24<eG(QpDa+MZBh@l>nU?8p*0@
%:g26f-f:C-+hO/(fOV>S1`\8-\NRfqgLF5VD4=iuTG^ps%a75$_r<d<0lHKlZ<`<p9+o,2&GTX_R/(GFE.>c=<`[ZYH[qK:#5uuN
%"?\_;fh2HK)nKK"+BWj>Y0XjAFUY6`Q%/7'9-p^#?$@->okXXuaUqWe+Yh;P58E)!!h'QYbnH.,nZ.ugSS\9')>G@gh_W`a>88g^
%"*G62pk;:fe=pAUcS/pVN>on^,C6Y$[:9e1j`7O1ICBpbP`&gnhlU30%tNfRkm&>>KRR`p'sq*YeOM=YN`W![;l5##J?dqt)k%j,
%Vf@1<KOSHGRRrXKcq&n1:4"n5o9Do(Wc-C*bckf>=qQC;CaUX'q1Wki*g0\PcM>B!bW(n%#oR;R1'(oVV9\`b?>:HhR8PHUZa55q
%N`a^?2VOQ:$_0!>ffpbW2ZMPJ3.WkKb^n7.MI"?d;sAGMFA,k:R)P%VJ@,#sN8Q>:&%7*OLr.]Q,KrUFkRo*oao-=mf+g-Y3(AX'
%D]OGR8b03IaF@$2OtcuJ8\I`3ZEDYj#b*O'36+qoSTaT4*Z!`1g$?jP^_;%D?_)k"X7_&8[>ouk0+e&(bi`'c]7d2OqNilM_0=;/
%>=IW<I)^rK]&X.BLW9W[;u&IF!fkbGK=7"2qDi[$IupLc+EaQoC)t-maa=AHn1g-Z$KOaZoho/o=SJeis,Hi\,5nSRd?>2*=94V&
%$%=2O;[K]%]h+mtK;AWY?!ViEiqH>8!0$NCQ!2#7Kj4[j`d\CY\I`HsK&)&O:Jak)9mq\>n93Q$9n(AF;+^FZYS$DXl:U3rKofU7
%U[?,,iD89?=sD1Bq/Hu%Ql2'E<&-eS9Pj>l6VI.m'm\lL5N<V@J]s]6]upY0`B!<IV\E4?;/$S(+ZKro5VT\:Y8MqjpY7N)$"1Oc
%Cqo;X=)c=7FMjI/[fo3u8/kqFANo[.l6V1pbOZ+t[VT;cc@Bo?mf^Y'_8<['dW,WAlk)QhJ?t:(CjffTX<0oBX/SnGG5Od@`MnP?
%)F]a8lY8d-IYtZGh'i[]Vhl;(2%4"nf#]+:CrYXrbS1]/k&T70:8F.[A+0SS#t.R0Q&HJrG\Mh]133cJN!b`8lTi41'=$:Dl6SM-
%Kts_%g5CTs9"Q.QTfNB;,&7Tl"U!f,Oe3=Q3@0t^j!c$,U?$&"UH!CGFnC(9nIVt`a13k`8:Cn-GI-==95l^Z,:`4]GHbO<5B:"b
%dFTQJTD?4-S/A/$TaP*CGIZ"E]:&:^esRaf9/PQ/SS3B^Cn%5_lDNgoDFI<r=T'E"JM*02W\+Q4PrH3%61*+K8.%WYM%A8`jNAjJ
%9k?"5TY_pi"A47W-.oq1J/q>82//U1N&%LuEOq1"HEFeKKmQZh_8u4AKSCjMh.kaa&S;+dod/t;c:H5?l5^)$.kLoL--9[sZqho=
%O]qE8e-05-27e`WS#"F58i._:+P.M_1-'qIi(+/(*YSV5aTfR_*]basO`$o=X"G<OJ?7Rs@D=P6)A2Tss'XlG&W#rSb]t='(XMT.
%0nS_>9<R9;B!qFROfX8VV\SMq@jX[B['GWSg;ojgZ[H?VTY?H?Osq2mY2%+`E:k)sGf_ZS4"-8-;l.]Cl9[gG<<p,jG*(-/[fu'u
%3TMb3"?NWrT+OJ^^kcIGG_Xfkle6t7=\Ec/jUHPX/^7ml1O$;Nbh=[h$JZX9Pn[JJb$6q7B.L$9>0]gX)O0HN?UeO=bY=i_J_XA:
%F>5fa>9qE^Oss<`MjG57!]<HOqh.8=aMN4ijcf*.2?Gb@D9k#urT%bc/&lMASNt&%e>7kfb:EE!Bl`tfW^``)Vn$#OGXL*(;LOYV
%cG"1+[q)iXUCsVj)e`19$77O?0dQN*h[%m&/edr<XJj6/2PNkPYp8)[TWTG&*F[i)M@o*1Lta?K1:!6gZT$DI375lb)-3Fje36>O
%QT^;f?ur1MTZH/Uh-AnmngD@q-s8-64[CSZ-+K.0WC1#"acRl52.X.TdL#k1<aMtL+:6+dPP(dphj:<76!`N.+)ncg#G2I?+2L=?
%;#cIT/3X/==d'[7^-)QUd`om4U5BHW*2FW?itWsG+M6C5Ol@/*0U6WZ:cZMPg.Gt)`\cOV/.B"c+1N`8A$ri:F+u*k]`KDI4MtWi
%7+DR,khW0\+d]QZHjDL5_=ibNhS*MS[Q4FAk,C'Ea:Y.KN.I`@)7;tLc`i'RoM3`Xk.Mu_6\PnH0tGo'*>Tk&SZm-[X`3AW$_q$<
%19"]=O&89)aXA$MX?EVp7/TVB6?1urONT":^SE:\6&?d']k2s3n&\uL2XE_I"q("A(p,qf6tAA$rZs)75K%h5_Ksn9fUu%G+GkCR
%WDab<;E3b/kY&cf:BkuSF$2(>Y^$Z-YRN*!iZQ;C?rt9@HOU!E[?\neng/09=$`J;k8u_Vra\G6+PZFt`;a0aj+$LL?TH+t;VfTP
%_iF>\_EOg@c$38'BN1,eDaV8UbQp.V8B4-U)m6IB<#PSY@(mQm=N1Wa<"/YNOajXq<E&c,'Ef\%_F7<pE%+tjG=CXp`dBBo9RXnd
%b3o^NhLjLs1>jiYb"[q)p7K%Urd?or-7D&a/,GY!9$(VCI^h825',)'^?F\C"J;<<:`@mD9!X*jrM?Ir#I=[rR.q4md[aj]QEg-;
%<lmfeXcY*+j9[C:3(XuBV:7&kfjsp%*`I#"]#6&:$CP,rFI!M37MjOWf?lPFi6_98`<_:l?g_;iYgn`,4:HO\Z_j<Zb%%Vs7b%Ua
%?K>'/JNm>[j+2ZC@=EBMme$aAi&gWNch/s5m.gA:/\7I-eR7>#cgKte]8%@c97\q*q)q5M`@]JRGI+E./?;C!?H,R8(/)m?+ge&`
%#hTPPq]:1/0Uf`u7WD=8!3H+EN(bXp,l?Bmj.8IVO0Q\LTU5Qo+jHs<B,CETHK?VER0/`mY@3;$##-;,KTOG^p$Dhnl:8H"5DX42
%p<-Hu_`k=BNTKD7Dee\/r:]1SOCnHs7$Uj,)Ds_*HrCQ>\'!66)%RQgo8h"-)XB5Gdmfh]k9!jAh_t=eBgJB3e+s,1KjF=nZcG0*
%@*60aj53n<'nEJbHj1oYbk1fripjS%Vg_0sWeq]6)&DTD84hTKigAG`Fb!a!lslJ1\<gl6ia4*"bsS8/MHN@&q3`J7i?-,&K<]=c
%,a%HF`Y*d%\!.8n]uLDlHTRm'i=8WdJUb&/;EXC44^NAhPS0cOkjeQo#&*])n"'H-fmD<t98XruoR7i;rJ*Q!R#X&EVOMg#jN)Gf
%kQW3UJETY$a':?R<EF%1L0Y=jW^`WlpUB%%?gdI*bc3:g>G<Dh+_n3O4#5[OiK*)$3s+Y()4\'X\XtQ<a`Rnr,g7fC#jq\dIUY!(
%Or7.jF(c/:;p3Qt`Pb#$-d$fb8n':]1F9J'&Rjh,\<5AU@)PKq+S(Ts@Di9sg&_JBf"n:WMW(XrRrWk!dKq?j%I70nVOrNP*:dL9
%,a2+?`\FnhJ2`$u2(_?;+9kur%kq0=+df97+u>?;Jk+4R&/_1,^_+3d-@+3Bq77F;Gb)&(S6cH;imagB+g5dD\"p2pl;Bnk[j^3O
%%Kjf+!!_P^c1VlNUtCiHB6PerUZ<8a>2CYZ5hIt-a0eV3Wn<8#:8]id]<\SF_BlAeFW`)NP,bXoEKXQU3FuKn5m]+tOF'rp4fthD
%92QYPg(+?7mGDf7*"3aEPO"Aq4@'=P^T8?BEpusn,B[LDd\2^fX+1n97]hQr]nd4o6?Lr*>-S_?+;9N4*Ku!hdLN\*9gt\U)X6nt
%Iaa?EdcH+6R%t)[`]mCl"3]U^L1tEK.Y'6l.k$Wi:)llue,mG87@NLp4csbDARm0]ncbI)!4kASV4kbrr*0^I9(HLq![LJ'N<mHH
%V]r6Xh$F:N/Si[bXoq?7<f5fbq65Dob0/Gn9V-q4a2XH<lpJ;"9JS"u.,$IdbTluf@U=m>.H[dI67D:JbF@#5EG.@SoeeB]H:tP%
%euk8G/[IeJI?kbe"I(H+X3l@ENt>p<k;bg$\WTp6,?n#'Jm7:^L[g+g:OYkQ\L+FTH/ZkI%EObWLA_YiG@E(G7VR_3%4^Tg%4T&Z
%FeCQl08m`+EN@\,9:<^FJ8EmTOL[Xo,kFeH[?7kneEk]/dkqm!37*o[:'+&k9N>,kU`:qo:eG"JJ<I/D=_$Z(-T"X5\k+AUZd]Vi
%12+n1E?;HMH^>O_Ta2767Q8Z58+)k80qE=2K!M'Ta"DpN1b<H>W?pJg7\D3,RI**aHfID`1hAFYV8-In>L/$_H[b!Uah^(ZhbQ:q
%1W9aC5`K;-7^*$M2`U=nP.1cQSn.q*hZh-7<7Bt4+mf:Y1-Joa6I]K+-:uf/DgYUfQr4s#IUFm_S<L[0<^A"ui)G\BK46irk+j:_
%V^4#IN.A?fBJ+iSn&9P!qBehZE)>=YXg#`nZ@tV[`[;@EOn73[aLCXd[<FXnQuM4R/jbui+>a6,n'd&l:6:\OPNJ*@>bkBbhCP8m
%P^:qq#)XgGZf$_W:e"r!#pKLnMArH.QAd(O*@b'Cf=A\">`[XhnJtl4dh^M1Yr@-ZGU;LIn3[Nb3NKZ$I4gr"E^YS57nQgBZ7Z;e
%[,bVK'$,<-PBtKOJ99T[]e/S'!I<H;AD1LC+Iups:ZH46k<*8eTQ<Um26$dE%i^pS;$A:mQ)g-$PVp_leOW53R8)Vh7UVI*l%f\@
%#t-n!XM<pd?%Tu'2H;@L#,b9`D(PRm[h:rnad0&SOWP;##Z()gbfr]CG,4%F+W)Z5U):ep=Mb@+N!'t=.r721Xb7+#8sp59NA5u)
%%h"kZ6'sun(0?:3(7>V&Z\F%l:`p7Y,589E!_BM>l6r,1BkN+?V&0L^o?%,*T^TC$KYio2XAb'<]Du.1]/(jpjXA"+NObO&.u:(o
%+5Z>fC98h*B.s*,LDn"'X![/q`05m?afWY>"['>`aNPn-(3(n*;a0/6h1aJ`$.7m/N#<J"MeA!c!g>a5.N'29*i0u49-EUE&1`du
%`InNp?qpd/T@@j4!:2g=0Gj&m4t4QG%\RkM:8KHa9o>.mIS_$3kL;L%HT-M=e&Y%a]5r>JKZmnfejF,A"9(9u=r8n'ZR^\sXT[nZ
%W2%7VKdSoD&W/ijK8`2Bp(@PFZ"Rp9<K+-Ve#r5sH7n7fm#=5la>D/Q$8CEfM&H2a,V+Od/7\ba;)YbrEAg[]d]Kgj`E35:.:@h4
%'>d0eca$C\'RO!H#F##R&ub&b@(=O!Gp_<m]IecN*uoRmK[-HEEAsOlKj"Rj9N*jnKE-GCKBT(llq(.]K1tsW]jToe,IipeJ<,4'
%PEe0?,U<At@qn240IV7Nr.i6=B`b5</8OO.?u0lhH(d8BTYi+Q7240/mTFeK1"0R\?BhBb.^gDaKM-h);?$frCBUIM1]N^_=;Ee[
%f27V1f.JIX.2<-NA9A<%b-K!m0Jik_b"8%;cU9`WaLn#-PGM_WWC+`%q1^O^(67=Q6*<=\3&sp-;qj'F-"`#Up0$1C1)S/)[.3GC
%jBKtGHhbDV.RI)/&G1Glk[o+Tnhl_[WoQ4G7UFLdc7.PaIKY,6@Ph2SSUm;?_9/f<G48TA5\^M];gXlZ!.eNI=FeBO^'#gt99qB=
%%eWQUj!\r4QYn4^7P1nGXkbsJ/O1.-fEj*E5=TVoPI(b++*!Z%?Mcj\kj,R=JruV(BdAVe]-!+',ZW4DG6YA`HZlfMEA"o_Z^ViO
%TZs:#8(Ws2MMiO4\QqI<6+fK5//M6LDaJR'7)i\Y+!]?q^c=K6XXk]-l!lS(`PcgU1s(pkSkP%[?OIFg'tXe'SW*@ro_I8LG0&B/
%TEcPEJSpX>-1Z>t?:Is\kUr9.JKo"L7#o)-ljZ;.;<5a4F0AWhWPm+K.`)DLK@fG2PdSCsDeE7\Q1V6RMM#mc6Qo_f4CT8io7kRN
%U3)KQ'9:-b\tAO@;^40qb!t?XP<o9ni'juD1T7\Wd(+0T!Y4M&7enDW%]O!CklV?Kacd&Z%;uEGlm)S]3:Q5L%4bgQ#Htetf"a/U
%+BD5ER$;??!pMkNo_`ZmE*)V%(Jt$4#pf*.@kC!F_$H)g<),(-Q*MVq>JW-F==Gpr8)n%%V!W<EC8jOg5Y[#D3*"PZ,DESQ-oHnr
%Y)Pj:Oc+a;eGtE["iopIe+%rRGtU-FE%pK2n'OROjqK@-P,5@7K^fd#+dQT^UsZjkWfcsZHIGq#SL[!>#UYFV(62k$$+#4FSB7#M
%Re,:Fq3Ei:dL+'0P8Ii^XX@:=6'Q[^d,@>eJ'71]KQ1N6St"BGQGV=GqH*`Y!di=":moO>aIpIqH^X!t<a?6b51U@&,O(j\R&:c;
%RR-MVB"pa:7J>GApsLs3or-ipM-3G_PMJUbX7]?l1Q.oMVnbSbU^P8/1>HMIa/(/R4<)s4`o4?E']eaYmda3K%^.C9EKg'4a^?N+
%-2q0Zn/FQ$.K2&(-B0.2[k+>O$o9aUirPK,!a3^l+T'hG(n"No6+m]H,ZY)AcpnYg"p>Vi;!'8MhHu=G8d\3O:`3'U:_8p>q0>RT
%</[9F'Nhf/[kD?a5AjB2#_t\NBp5J<#t)-38%B'U<U^Xhj!6T3&-0d;'auFNQnl`(5m25d81fD;;)<*Pe#\-=?q>I@`S(<2?7imp
%6XnieSP1NW?4[5`NBMT=h8T`]m]PB%e!BNWM]_V%B'p5VIM=i%C^1/8mlITR_7l%j)ljXG=K,@M;]Qn8i>Kk]Fp3EAKCC;dL.i/>
%2C#RKF,>N\\9B:\.\Kq2STJ"WWWO;65,<YR3GR<#1t]/a-9O%?P,;j/i$kfgS=`X\cB-?C`YG%SS-`LK?LB-hdg-#QB_:":%fQY"
%]_35b'U\*Wfi]uB2g[L*'6@t]ga`3V_$E7C34(GY*P<19nl^&Ir6!&)5V+uLTt1M-f(`]2:mS&35#f2%(F"4e1g(7j+V:X.#m>/W
%a"L3<Iqc3mM(D;-8I_=[N8EVp'SM7THN$X/4[PU"i#bDskBr4%aP:&$99a1F;B9bF-/AjP?N(:8!LX6h$d]frIH+F&-m5rD4Md;5
%5d(Vajq*(0MT-cmJ[Z8ZVhJODkX03UajRW>_?5r_LTt*jUf$T,.kfq%bM'QJedO[Y2i@kLWY\&^$Pn/V*p9?QFl5XS#M,3^bQ7_n
%[fQl`BT<84@ObXaP$`ri4i-O!9\m'qa@"6o(\pI4+D]P+i(5<"%GEgqF4sGa<Hsf@Quk#VC>`'(DlW?$b"/FTW=LAIHPmK;[BABY
%NImOC'C1XPYY8Q)TX:#T!fYeN+r19#R]n:Y5e&L:%c%h*LR,9;E0!H)-"Ca_b^^Tl_VPB2MHU!95LfgG<!Y/8e0#<2CG"]pK*JD-
%FBqO#Y)EUh3;a*I*1Th/A]#:F=[<n+OU(R'!h"bo7`Yd%P^27&8]2K;!6#At=_DT1C+#Y](F6"jMH+L3maE![`Or6EQXJ!!$CnP$
%W_c9U#a?GE*_R0Ra'1J*pok)8n$uOL:GD:GfPrg^l1:B9i0;!7ZY#^V/tqt";6DqU/77).,+P;7g48dIF:.]JU:A.C'FoMG!pF3n
%TAXW?+NRlFJ'[2TQ,'D1O:K34!,I?14)G`BMOE*[J>j36l,*+k9'a9L\"m0nkK%&1KGPV0!g&05=A&W/1iOE!_chSkS/WT)!S@HL
%LsX@:I=8'17=h4_\Rg5i+\Tc]6UG@4".keM68L$QAhjYb!p_ZH'S_YTWG$#I!L^WW9>fFdKqQp'PH,.VbY^21aFU[`&4LIZeqfb7
%Pln&e3\;Htr0jN].'4:$;s[6Q3MtJHK5([*HTG(FegnK/:I;W(U&9V!^^2hH@U$-117:U_3<^M/M2gp+#+%4!/,1rg$S^<RY*C;5
%R:GBAUsH>R.:WN$,[:<d-OWn,a0bn_PMflLBl%c^YQ\io!bNiP$CI!:[1+*j:TtKeM!W#Q)b@mnP>GeOm<t.i:R01Y_P]C)0%W!R
%pgDH]Zac**#`S&0HD1sCQC4Oa=@-*9a`qgB4om#7=lFF,9-BAt_+h#7;f,M@[0kqFGd@n_-Kgu>(T.*QG$h"FJ)[L4)&<h43uCqQ
%n5^r%-t38Eq-!VS(n>'KJJ*r[iId>=W$NT2&i0cC$2+hF%/A6Q69Ar`/?T*,oE5bBn":C+E93`o`?HVX;dODb'ouOMn..6UM_-&e
%@*jU.K!-J)PP*m;RZWmjI0DYE\[\$b^#hK%8it2Fp>XE8R;IqfrD+jm,sF.fguD!K$eS69&8%)<U\2/:2'#:#`&">q%`VfBE!91o
%-t/JODt`g8auermS=%f0Q$"L**YcDK,<V)4_Q=OXM3tqrnP-7"':<9h%EajRO)[b89G-VKo-@R5)"d[)PcO7:EUDFNWlF;b!(+_Y
%"KPZ584\kR4bBeN$!EG;H6H-uE&(2;B8Y#\.`B0AF^XH.$ReQl"?=u:8P1"TV?O4k!oq'F"q.6"<,&=JJ8OFNCF8eB)35C3bgg8c
%-K80NPHl+D)32O4[49[?@-u=BmaAeiRDTP.#rFnp+/*mQ\M>Z^$-?X]<%Z>"i1pJdRPCQ8W0LpQTiqG*j#FNHaJ2/ZO=,%861,1E
%BnFeo;WAB,:PoCT<UcMS&Q@"i*>\8=9j`0sS2hofe'#kA,[NL]S?ITUdKV%tZiG/:^iuG/M?Q<lD,UnCXfUNHN&G-c"jRr6TF4&2
%(P?._(J:GBQ(!;]7P.po=l'1bEehJ&AF)Pp=^7Z#?"etHX]c@U8p#-RMr-h($rG"lD3s]aE:jIbi9%Jd1cHpk-#8a*;,^u%8Hp]A
%_Z^I[I5m=;0^.#-N/u1mm=gK=>3u,$V7i<=eg]'I7pjA%D(`;8ADOUPgEFol,S9@Z/PjFlBIKc,5%7Q<`>N<K)t\Em*Z3VBZcH2W
%clsbCjbF>2:Ei15?g#)W\F^#H'_?or:2$&VP)\$8iBSuF,<13Dk[aHsC_Lp&K*Y7\Z8tQO9\PO>%Qf9sqCpEb<jE<UY),)aPo!q`
%glM2#<\+RK5H@\(qbCI3^Cbb;gG^l0&Y$:lqsk=so:ii'Up8R;Me)9u66O'JZBEcDjc8FY1N\p]*]hu&Ofak9e-&].Inj!"WP`5q
%lrM&Q^mmr_[8HLlKh'5M_OD(GoSGSopDu[+`E;.K.,oehBS10_+VM:M"_`]M*\M$S=jg-@d[KulSD>m^8XGO$)7;jU>Vo@l11W@W
%['ZX*>+[7;.\\.[9/A7*aaM4o>plkE1YY[L]k]*S4_q<b\^o=k-$m'D5WjX2m3.XoQD,'ETah1T!APd_h(0e3c(l6!9T101TbW^'
%T-?:uehAfK^gL>h'588ca^VPQWWMM*_2tge8OtI9>aH')C_&Y*RejWe]K2mJ<&]'e&og=7m4hQeI<I,6MG"FBp/U._6TT\GF2UUa
%.nYd?9QQj]R]MjaJOq>-jf2(KPmB=kY/1uY##!3M;fb?J)S`A5E1G[D99!7D0.irh'rb5"F#>[IR]M/$)3G8J<qr>9Bir\O+SR@3
%?%U;<"D(aW,/Z>EVSo^Yb'9AJ,SfYY,]6D61Ec[-Z8]r`0q)]87^uhj6mD?A=e9+JhOc/6;)`!ZZ-l$4@(n"FMD#J8(<A*P6nhJo
%Zt*'U]&-I0)G1<HE(;l#\aN)r)947*-Y9?t(3Yeu7&*](0i-pj\DMR@V$^+/OE%TO=N%qo$'9G4datW^Rq]#r6eN1eJpV0#+PWQ/
%eOP?1C;N^YC'$2TSMF"\EnNe):VQ9W3TYLXIL(6@:*RObn=c_S,5o/O_3\k5YSl_g(b&D5,Whq!mb3>PZL"(qP0QZn.nQn_DoVCf
%gF@IbJnQ.l)B!<p2K)d`5Wr@!ksRcJ1k?fYn"etG>#`]>.6Th-T4_X6l\%)XKK?+\E21MU-5&k&=VCt+K(KM2(J,-+*?&o?7&"aC
%$A*J`P!:q(?:fl.T9DAgSS6(l:QNK:_@(:1L;$&*G`raV,RRrVHR+4<-gme4gR-s0.GfVjJgO`@=T?TBp0b3OMq=3g1mgR;%\+pS
%),,egVL"M"l7E>_C&uoV8O1%3&;Z2BR.IGaRk8n><"h1Bs(LFCi_2'+aYB63DIX&26uO8Ehqbh*0-1]N.&UX.="-e79UJ-"4@I(a
%IMq56U.q7UB\=j`>=004)F%Y?UF7)f<%tjL<%Vbm.($#SU*I-8'61#rX!2&.:G[1d7L1Tb!-&WgEf]:s4XhM+nm!pN@CL8SCbV+6
%aN.[T4Or6?Sd7[Zh2Y2?aEJ@1'g\:4@2"K.8-0W^"4scbYDRUk`#c(bF,1HlSIHQF`$XIe>8Rt\MK#KGQ5n1P&U"Th]m1TUfQ<P(
%,Jc?'"d!>;("a*n]HQ@"Nek9d"G_a7e0Zq^^n+&4Wb#(g&b8ER\4DKlP,ooT8SA@"'L=/A9<9s!"o4%PJ>gcb1X8CP"9o49Fo\9k
%##X["!!\gBMmg,iGjtmS?qUeI9MQ#%^DKQsS7I]D)A0eY#skjg;MCO`&g49"`p%c+MG6CQ0PNib>"BP05YDIL47\bNO&>247/9@"
%OkFe\jP7@YlMQZ)\BN#JibeAb_61TI7BkNC:++(]7a<1V?V6t;*ebJ$drH%1:$-stVQiGN1ejmhl^,ClAUZ['_=koK4RbV5CDbs$
%WS":TR,!?TlJ\$V'=s!&MqZAMar^ro&?f'=AJJ9&=%.f,RP'RM.r>]mVQdXX3M$qA]fal0.nHm5/%Wn``K$eI&j-Hc">"jR?jA'0
%&;li&cdiAJRQa&/-c@2>GrXFe("(kR!]=U^!%$3LbH:DeVPg6eV3UD>en>S+\F![:jkt0F=ZNFV%\a&l"JrjI]a"9k9i4Xu-^m*-
%KM4j[=]/1=jWni8"t"7\h[B"9a[-+(ONFS_iIsO)n\(,VB$#J,R,4RD@Y0P1\<:g>+fbSb6-W%J(o7Gp`1o#h"s_Fm"KF[P:n$_h
%BQ79MYX&P&eD2GgdDkc\C>hIb3aZ5<_M'p>3oMp0Ih*.ZT=C)N9\#pe/]]&&r**KqW_#XuP[g6K#?L+&7755^\KnXOM0-dnUDf^$
%l?"gT>T35K_0/LSf(@m>dUr2e8t65uC)#BO'Td'1=O*[A^lD0`HVRI2EP<=[W,koYRhq!do]krYd`#/A!F]LcqMsJZ)mQ29U#/WZ
%o&XJ*JeUK*-CT`lNpX^=M"'JiN01()r1HP((-\Ff"K'GcP0IS/E8fo#%Z$/is/3tCWJ*a%:'h%6W,E!2dBHU-KL2Ota^Al6c8Z1;
%HFG0:.gg>@RX,>V3]E)@r)!MuI04NP&l3ZTf=b6=e"W:1"t,&-.1^ciPRqpt:f7DWq[].,JHHJ:Hs-6;@/Cj^r)ZD=_KPs\$$M;&
%nutV2h?;4m$325tQ?q'_`)=C9ADr*?!"T"pPuO-d%$s2eJt&nT''`/YllC>[aYrRLZe%e[E6(35dRRMK?aD*@8k-uH'BjECHFDfh
%.!cLe9IjO--QIYfMR(*O#F8]B2B`91!?6<Mr3OIP5FG[VEHQLTR0=(7XX#]tA,qbuTfu%nPbU9+F3,8+au9i;2guT-inQ15ekTb6
%-I884[n`G)VT_1U8N``/]jY`YJek=B\`8n>B7a_q[RMVG7'4GpE(H/0fjXa!31n;)$QH2<1_>3Y84nC<Luj_]/9U8O?0[WN]d]-l
%'r+2u1_Y,V8b8ubamg==ZT]sIf'C06>5'=P!el+^ja59jA#iFTm+#,KgKq_79^k4.<6s3M/2e["bHj,Mmu=28BOOGSV'hQ+H-_D3
%Ag5%4+"?60A8(I?T&ChK;p?4G>;81*nlqK[3?C/<*7EP8]"]%l#;e,Fb#sf_N1G?%3OEK)K.s\U+:@pTU8tH1&J6lu!nl.TP917g
%mL#e56Yc!ZJg.X4ZLWhW0(#s5iJ_`.AF5L=d'+e:as[28:?(d4,c_$Bj?boY!\B;BOld3QXO,_[ha<0oS$W1NDfM`,gb8!2[X'U0
%KU:4T*U/Mmdg-Pl#<esm;G;\'g_]T6nIlbER.+Yu4j0pBZf#3KJ1Wo'PW1mjh%ltp,,t+#Fbq*BRECO[2eR)<L]/89Nce+U_dqD-
%/>ajP+iaWV0mYpd-h!'5i&J`ec;/U=@@Zd01@mXA[h.ZVPgD62]lQkf7:G>AV'\d:21Cl@2:DY=:?O#=\elU/&ZnSt;T-RbX6mme
%+G1%`FLBDFRXd!r=MU&E>1e.0"3t!qWeRpX5UBcMXi3/`VQ+G4JXJ$pfKUQ<b>9Ib4FoqLT*nf=-ZJV#],f_D51GuGXB9"W@`gG1
%_4:\T8"&<Y@U[03;gS.8c.6^YZ8akd/Au0SaOQ]GEY^n3I`_f^R_rThnL0Z\*#=+2TqNDPVAfL>;@7.Ua8FGZV]5i#*;5Bd_gXrN
%Od!3rZ>1P+U@FZWM%/h"N@Ls4+WUGYkV"Go(?WHMA.E`>3j0kX-PN+6Q7l[rLY=>jcAi46XA<s.JT-4I<p$'-1LZ?^qj`JV1_mC.
%GuBTr'JOB?Q$agE:FBl1]FF1MWA>e-B-l[l/7DMue:d9!p'B:X.E=*-"6V&pAYSii7YChH:bB3id:jLHM?_F5nUb;tF)Xc5Pfg(X
%<`V)!,dgXCFB*D/iJ3<6IR?RGOZSsk:@$)cq5=BooO'B=m@R1s8S7BiC<!!hN\b5*Q"7;P@+a/So#*h"[<I9aPB7p=qDVlaT"t`]
%l9i;@GbYQiU/O2hh1SIa$l&s&d=mp9&>j;VWmg&gM=$D\h!=MuS4I3#4Xc?;Eq"K%8(nCJUhnrF+JBI"P?K:..'Lf<5Y4^nZUIN;
%m>l:DL]jqC;4cSC0WNW.'Q`Ut%k0.G`-'E0F@`5D30B:j15UUD3\)E8BgV=<PIbj$]"eVV+GS8JK-[,6!3/,`E)'8se82gO,;CPP
%-,Drbj^X.\46T-MOK"/$1uJS'KOCaEO<jjI;N>3%5?MK.3U)ZrW!7t[^5K9]?B\A7CN7?B9-"FJq%E'5=p8Nd@#QOnCB8C4,/%$W
%2(2B=0ShP[DZY2$h(#L(-hGMH[6]!<<t?_b&W67,FUNeaO7+1U\6_/L0XEF[%E-e7Zq(Qs`gMpr'84trg>FecK0,u'N7OiZ#=sMA
%q<2j"&=75$`L?*Gb(>g0a6bW#,ZVb?+;P5mNa18B;B,`3E4hrSYAKAhbN/[LaTP8H'[WS\-$?OKZ#iMe`6#[:7ZrD)W"Y'L#bf@F
%C2Ps'DY"eEk3Dad'I4Z_QW4BPSuph2;eF"W8MEdej-%uX6'Gi="g8cl;]skFW]*#n$]nGO"97(pQm\$.@G/MTG'-7*%,AX\ni7rM
%:I#cP#t4\FUumPg:24*O(&+#>.608qnOTXLIHj_ZE0r\2A;`E^P,JT>*79Qe["C7^Nc-5*o1"QDQT/9WYdFg`5PY9s0=83WRl1IB
%35KkGX+ql.1^lNMq"l+Q%e==CM1du>]XU[3n.&lj@EqV24m@tK_(Y-H_DpYO-6ZQe!?j+,huk8N3Eq0*%Z-0;XKs7F=T`#.f(&+V
%jJ\3NO?E;#k[K3*E#[XPXp0hHBY9OF(,4*S.RZH;fq#%oeTfXX*[63H-8MhL6VsOZV_#BtaO+a3mDUNifRPK/,TiV209/?4SAB/[
%$^(oqYbYSG(n]G+76#RS*Y]F:)"J-b+DtY>o]#M\^8,4s^nl]NC1E5[S+2;&=F=l">XfiHk(5_i;?.+A,WCtCILcc2m[X[E46a_G
%^o3eVdIQM,=Mpa:a']H2G)@`*.5D63^1,=+)b#jR!_@9jd_2119D[FgkqS4secF239PanpY=VHuA1S$*G?bA"8o(D7rdiV*4@`jT
%HpJDQ=%:Kd<^YR-A0`IiOVGVg9-mSqmF/k4RKco[kWhp7)`CK?Uf@WdE`?g&`rbin@IG@BBgku_[&@)PN9-CfgQ+03&]WG(LBC\f
%GT)Jn6DQPu07Z!;oV<3'Od.l'TL$8[MLa,d#2O4mnh&p:C:#1iL/1R^l'bo6YY#H.3;396%4csu;n2)1BLVf+&DmYLCp_hOXcb[@
%Ua6Qk&tb?Rr`S_A>sOD=0QSM=JC>d/SL^WcBOX=qdK),nH1[ku?uPZ=FDMonp3fA&2B'R<VE5"d*?9Q'd!Sp5fdDdj-E=NpnRIL5
%eI\L=/,FDuGV\>]$h!N>oP;Aq3m._T^!'&NH]44D6^e9E_#Mj6%O?P;C(TNtE]?[Id4]#bW!7n=790i)o<RA5SH*H4%8=H5J-D;9
%6^WfmGr^3[Z4oNU<\jbeMuBmU[,HhGeQ\7n/9=o?JXeI^+a\U4!:N5^^C2P6J27=QJ="7aEaBUaSV!A0?GPh3lf!^*fRh[QdkNWa
%D$#NZ*M3Mg4DK<h?KafX:JsHpC4g^dN;_hZJh.A\=Atbmfu(d"Hfkc*Ob]@p$=,_>oaKp9)8Mt1XAiD7ZH>XQZ=k"pH#UC6hdGf'
%(inr]Y/?X46+U*h:""k>_9W+&oaKb590//(a&/-Y@1b><4;"q,]_>&"#/r;<`!qgj=4S"K=Hk.B6.aDka'AR[aV+[26"ES\kQT8:
%Qt2*787o=Y8_ocQY?$rl6A?^k:IlJHN`0E.-Z/GXY=EA^,mhMsP>5aheE<12QmkE;/(Gp3S[(*UFW3Zc,.fQG&H*JEDR9%g%L&a=
%=gY`T5fS$a5>/2lHQcD$plpjPLIIEd&H$[P-$\D(F@`5R%nCbi7s)u+7X"f+O=C<umV,!%M%EF.\XTl8YGC;/`0:bPr=#<"Gl,d<
%3I=/iNoGtEj=HNC;oB1;OF7Lu(h8`Qc4AmQ^gq->.m:koI7p@nNqIZ2CWGCV0<:#Gc7JYYmmN(6KFpgK+KqW1rPm!tiDGR^n60Q@
%>%%,^[,?QF3bWclK+-;7KdFlEaOB;qLH!Fm-0*qG$n,A4jb-qU;`%)8X^P[IO83UFXGSc18X'8cB@%K+6[?J1/jNZV!#N(VdN2jh
%(p#\=Qn3h;f;io6=3FZ`V@)Dt$kg&c;JOtSKa,*DMYI6.-L'c^*a];B0Q)`r4DDmBWOJ@M[*6<f(!.<53WdOpX[s.7<XTI'9=iM`
%Y`]Z5&[3E"a8dMLhSbhalgJ7q"jG",K1L5[E[PQ!$+&!%9uuBo$W)o]Y.ksT@aMI#]%XJccBoCA@b!At1Le'f[,IFL8Ju+)g^8CH
%A;=[`-@j<Ea`@\r+3ts!]og:C?=rI*k"CAQfF4t^6HkEOI7"`QaRA"p)bW^T,pbrR<_RC..48CPE'pI%>+>d!+OZe2@*1'I_5S5"
%/_C2=9CdJIBh?ac\BKSEb[DTtV=JR@p+Hg+1VBnBZf3u9G:d`t*`\`'kb_nV\Q"`Zn#>i>D4$+]6"VAd,amSK5He`DSu9r2BPSX0
%^rP^r?Fu<Ik[r%aq=rq.2983qo8QjZNce,d:c"/i,\,f[(RB1k<\i5=&oT)/qbEiMYh54Z=(T%]kd]lRAQF;VG<W8$l05\S_peS7
%(]36^BA]7C^GrlMIe<QBs*F^9PY2ej6pV6DiW;[F9TC-Uk[QR[o=S^7He@THUSGAWhpS^<FT;!sk%9*c5!:ut*q,^A+!'M?^OLBP
%nfOuUEN\SuIdc%7MlW=C^UmJ&Vmq3<PPpiGf/HddrpInBBsfp<chf@j:\NDLFO`&-a3Ef9SY)P['R2aTjBSA`F1oonDuIQ>hOsuJ
%dpMkirnm<3G5#m(+Ieq`%[isbN^!E6q60giZ+oYBm\AU1s3'b(IURGMqW&G*ZmcJHOb._^k)a,5'C3k5>o4m<Xc^lHP.":[^=FSm
%\YWh15It/:h"uApi[?3`(),D*ggfoNGLUosb1/FqX1nB6NhtfZ;*CPn7ZT>T=.c:F_sN&o-r&^t]WT*bX^*V!^dm-0C!9F9'%rJk
%T\)SCctR5VU<\k-dH%:p-b92]Y-m,#OTfmeCQ$uJ"-U[QfcDM`X^'B6==ZlcU!5oA&WN8:kGM7V-ZX+0X?rO*=GV1f1r4>>!`O66
%+Tm$eXT:S$i.&a$`^*4Y&1I,I$&Ho6f0T;:'X;M\hZLJa_pZCCWShA]n<O;-N01@f_b;[N-KpicJ5`.;Wh!alIDn0KCt&N*YZOYu
%#O<%Erim7I=m9aFqB=1K32on(_C@FbjbnPG<9ZPCQn/Xa'Y[E2*#-3qb"6VN&S(,PDh_JP=<?)M:j\!FN0((d&5]*Vk`Y&Wf._^`
%Y0_kVZG.4["!Zc>Tpm-fergtDR]9]rB1rA">c8DD\taiFMF"`+X?rsnQBYL;*3sYYYG<EUi6l)[<YSK*TU=I=M`7jK>7f9/CAM8V
%*O9ijE"<O?+U4NF`kKB\25lcJ2-_:2;4j6G.$3&:W5l!$@\eN)!l[(?E0E9a6'ik"Q9)WEOp#_L=Jk@aj!QA*-85;A.VS3)V(+<.
%/7sSqJ^8j:M4::i`X->bbb2LX.oHWVi1sWD8K>`QP;Q!?b'Hk_0q^kVfmsf%N%HkB1<!G6#qeh>=`.OmYmV53M%SrK;SllPZP,Og
%<=?psOV\n3<?S"M(e&,]i7.)$VN77`$(%%t1cU2%ct_,q9BnH6p7VW#IDaqN=^--LM&m)scpq+9(N2,9h)hE35m\MR3QV\?T,NYN
%>]D<Y^gRG0dfX7)U)?r!@<bo&UI]X:bQucrgE7C(0JOeETHUS;)Rukhs3pKTi8P\,;N>h]MHa2EKL9u<REgc:N#FLDOjsVi2fIBn
%8/X$FQ-.*3=IeP,cddV4QMeZU;D^e32&3h*#SJ:W@V+K9-3IMN!r^trZ?>jJMrN_0_/kSB(1!6?AY*\)6n;sWq[L=GNH,#2pkuFu
%(cK>]H6[F[o("EYMBI%t]'d]%c%pM2;CF<P*]YsnXF<p.`sL_`BM_ta`->p[J!I^TOB!_8H1jW!POqDh'-q`g$CKbBbQG[o/V->-
%>fgJe'#-eWJ!$Q@F1/3$*-`-o.H6o2%)YV/)GZ'`*Q1fA"$T^`.BDlhpo>3Np7@U-B\@>qMC5QjBPn`X<+@Zh(HrNO#&-=sSs.,3
%r=D0@eqm`aCB:4<'PC\3kX1AKH<)H"39OaK:L7D!+6L$i2dB*3$c^d$O!p12U0;o7qQQ+d(H,:a7TEYEl*#/mF3ID0iDt=JaE3t9
%^]JBVFKJDh`kp/(&oU6&]h,u'iFNnb:IcOIWC4"$-h@+"*G2sgQQc%i4OGO%fL;&_TN'tsh+&]!"_Rg`U*[$N5O@-DXXQs,P#3kO
%i"nklB0IBXTUjT2aJ&`6Z;Kli"4+>G&sC\'+S1,$U+f9)CS)qaj##_(nKq*gGtj/K)91<G6rL6N]rTT02cb3.N3D$@?5NoZ0HfeR
%_]iJ,2Gpano8-E1Et!nk*0$&Il'pd(CQpT-H7L/3Q=s^2Agl0&*\Xk,8e_:>cFjO2)El;N0"h-.GR%!pW]WZW.(#f!<i=An=.F&Z
%"OeFSB?74s7TBeP"^O"ikE8pi-'6(]Vf\'fILK[&.+WQ.N0c-S>2n`I4p.U28Z@g(d#DA-&5Z:NBL,44.'aNna@Np7*q1S/&e+?%
%LA\O"+`$*4[j[pdckGuHK+iejO(o!d!(-F?>R=_FM=s6:2,'cK&2?ag!\i\06?$(W++u9$W>mbZ+j-tM3kgi`0-K#.gsi,/\Ye3Q
%3,RbcE$uL`O5\Y*?4LN>/"pQP=-#ln#@A?TV-Yr+U'C9B_Y%Rfq%cj5,$#W$:lK"MLLUWn0M7GC/P$Dn:?"PgObr8:\?*"K<(]Qb
%1KDi(7%I`:@.U%-\Cn>+iGgH+aKC3h`:>5b!h:(&Q3$)2#o9Bb@5eCjZ[#3*W(=">8=l@3cJ\,-,ff6[H;KbhdG#eEgKE#\_Q?9S
%ab#;C*kSeXNTFZ:P/E^1-Uh'-FtrQNj'.fpaK8$)'6am9H?f]TIm".3*_2;*h1gVpSVW,TMMDC[L05WsM4`5?OM+G%A%`4qN`Nro
%8'F7O[\F"l'U[bM.uMn-W-ZRaK[#qZ$H)$M7?PIsKd`8pb@Pj!&4E=Y6+)j%3!,YH:1gL9KA]C<F>hO1;2tB`lF=emfH(43.@`q"
%i#p)S%,898hms`Ebd3^QCI0$u\jIW)a\7f%72*LTmAs)9PsLL!R\Q<EEn6d53FQIHXbMQM[tc-TKD0fAa;/T`3&Du..Z0'RG-L"q
%8TUb)A;,'(QEbJ>At<d(GRs$^4EAB&N@)`_79"#c(gE@T>O%;"JQ'6Hb[Q1bp0]?RCi'lec`8-M$9#<f/A4lC8nZpgpb.o.!>u#,
%a<usc1f_"qMK&/1A%'cnPH]AEO'0!K)"D`WI6M#:C*T*\JCQ\O`5l>pReg5-@HF:3N>9(ee;YW/H^=j5U9*8U",Q;k-rnL7M:L0U
%l\>6'ai4\f8/s%6ljMt(c@^s#Q39c)N6W(O(4oXb%!(!@FhnkdmG_n/PUK(j_=f>D4#IG`XOt1Ti"/Gj)7K+4TNlbAJ:U"L^"9"$
%SWd,>'Tsg)#")UoAJ51CotaFdeGg,Pp_Lp2Z0H<[@d5uD,d.#$fPnR%TF:oeA.$7a3JsR465<cts,;LO]aS4ZeGr05km8.^+u_h^
%2cG[t.X"SPfK^5bDcLMIW^KI[LmT_+09Nl/oQnX0'$+45/HIlJfpk;?MEjL;?p0%-c#2iT%+3t]$oB[4qFB2U$L9(DpaF?>iDPkS
%MFRpG#.X^O&X$;I.XJ;^78^ia,B.a6f31S-ckXgD)UL/E%3G0Za!K_Ra&-%$oZ$M!Tf?UY1)X)#Kkki\PPTia)Gh_a#^L%$lI:f4
%48(M<WH7!Pk77`p<j1D4!!/O](^7LDTF#_V+j,5DaZ<rQ'+ud7PBL?R@jVtpOiSiLO&HCV_lf@OMll_sWiD#EC\?;\a0[;c.GbaG
%OtSXl%4Gq\0Jmkf1?.t"B;?9H-?Z-;%2LHHfcokhJQ<ge.e=D7(P)1B^e/W5H:J2a',#<tLd7Are<s]:BG`P-,jC"S(F'Sm<6nUD
%<9SEr<a:u!E[P(j8Ed.(\"P:LZCH7G@A:F(fZh9i?/">_.SRLkmn+J)\PS>a/chJR8K$A%'IP1/q`cbSo'C5APgf6YZ@tG!i=(Fg
%9HGNjWf/06U5m;[8rcsF1/6@5YPoKb08=@pnpjdo;LqYoBlB2l>h:cgaP8W+R^a#:Y#a-u-:l]CF"V;L\jblT)3;"uMTGCN[Ef3"
%)Ga,]%jUrl9H!]h-`S>]@*"]BSEKt4^.'5\?3,q.9GYqi*::*)!P:(RX?'hLHm\IY"P"`Jd(-LQ]t1HFHC#r[8Vsl#7m_)K1JMUK
%84%#+X."3j]sS,>TMNVS-.t'\/.s#kX3-GkL`24<g32%#1BXL.%aakR.O7Bae"Z`(?m9$]Mn0lg;+M;P:pMg\mSbC;VH)UAeO!H[
%$KaC4S/W7iSe_^V>OgORI+QApa7?VM7U<_Ree7+,#P5/s/fO%d0$#'(3:b#]6&q.V2K7Amj(dL#3Rqj3Fq#n`N<\a[&PD&<Lru2P
%l('LYNce]]A7g*?0H,f3OiWufEMl+177ns6%BF59>[VBa8VsQLd[jVcbJr,U0bc1aF6pFOO=Qr/d@1>q=;9-COZTiO,>>,]-Oq%2
%=oeg:-;kuHlS`Q]RHD/Dq=DnSc!c^)_IPE`_\$71]p/_UF$66q'3"17",/PY+bEr^=;VodB2VMH_Catq)2,SfkZUC01)ah$]IrSr
%0NL)Kf(.#(7-Z'HaS]"S?L20TS(WJe'6SND8GugZ7kCLr[`$ZN?45["lWdLTn"kP6[G+PY(hD(9+UXSK9gJC_O0qT+_PGKI=VV(6
%XCXgqVI1_6%k(Bhqf=+E@R<s?>2i<Nk=fkFVuq=^,7Z)""9sfg'\(+i19g,$ShQfqa(bgakT1(\n.#t5&?YQA/@9;^OfDiY-3jcj
%3BH$<l@#Nj'=bA/]&ea?5@k_#O<g-Es.F\\U=4=.&63j0V_mp71M\]+Ufb@a4-PcgI]Eg`G#TXefu^@Np/KP%mabdnB)u*%9jVRI
%3Vf.LX@\\1P^Whd&5Q7M'/K>X-:AWD"D'ikCn"0W15e3ClB.MJWVqds`b0Tb(PO[$Y!c&OKgQqi1psnO,`2\#V9%`pPA&rIEDrB6
%Kl(E>Ca'Mq`aiA1>.ruI,GsP7PQVFjdt,o<-hQO85Rq9`Bdfn%9W5=mj\`,<"JimE\rG84$6D-.#$%_6Y!TI3HI5L7Ts&W@R]=VH
%;rZhN8gD3>_ugc<1Mmb-IpFL0)2I=470[F,1@'IlpcTp*Af,'P".kHd8]nm57RYDd,V.6"CBsShmc:DaTP=H'/fuB^q(]MAMmLTb
%([!0Xl`Z=$pUdbiPPs^dF)*M#Q?403N_?0XflCiTYTq-d4TGF1*1&k"3'01YI<p$^[eo:PlKdclB+Lkpgp#,]>5C&.h)gtqn_LhT
%R-#Ph7j]I@9W[&]$Gh!kM`4LgHrptYMgecX0n/DZ5$*dpEi(#C9ie7>ZHW5:,17)"A!YG-a\p4cJ*srB^Y[[ArVuLk;.\%V$L11#
%C#%iHL\<03,T&a+IL8q7VeFEG'LkC^\qq&j)0Q7V(EVC8*Q9bg<gQ=beV<[6e_94K[bf>+Nh<o:,[rIu4,tj<N>h2V`h7cK(GafQ
%)qSiB$&r_;oGq0)k9n;GI;S,QWt_Q:WF&4W'o5\Ogb0,Y=guu$f=EYY?)rmt@H6a"Er=G;-'I=KdCZ/$)3TTP$D##/@7fO!FDG_[
%X%dS>D<a3khI#(';cfIaQ:?]raQJe);"R7'(,$"$$ZqNB[5P;?-MMSk^16L-frRC1[@&ZO`H+flb3D]N"CdN<(N`%!orYVnXZ!GV
%_t>$SmuM&QWN48pTi"u7V@OhoDq8Z#Qm"95&4tNV[E5O/TqB6q*F%tCC%\I+c$WJ-IeS6A+rq<l!Ep]11*!`7mTAACe].?i\@uJ=
%rB@;HFg\LGKu*8j3H6=`fE^lQ%\'kn=tSM`Hi5XFa7hX(qUB82o73c[%<!um!D9oJV%2Q>'6kj"T)Q(ndh+66c?#uALIcAuMe7+#
%=JXI]_05KcISsPPacAXera4LnG(9>,X/XZ-I"#GQiFGe?$MJ'NS>5C5l!@3*XPCCG$O&-rZQW>CWslrM-Ee^`]u`(U&_;o=>(BO"
%,=<qpqT*-q_"rE=@Tu%?Z.pa,6Y&2F\^d\p@'UQ,)_?V,pl7Q_c**dk+mSMU'+$t=2EXZ24B(b#T*?VLB3ZY(),(+cdM5TpWUIA-
%LGfOB#uUj[m';dR]83lAXnLUC06&TImE[<o)fZ/AcHj$'gYg';<Y$F3&\lU1*>)Cb\@FM>-,@14\Bl4+-,=W&]2Gk(GI+EZQ,#>,
%mdj8q%ma>,MGjRtlKPqi7&a9]OIW'U6ooqMi=RZa`"[a9_rUP(A;SKB3L^E5L"e="#I9W7nl<.7!0a/+oN;/+2oL+pcL`,*MO.L5
%_c*Y?MrafUTO]UM.S(U[gH0"X]iu0<P1-o1bo*2E<]_JjWu.oa^kb'7*"`&Nd+6`-=iFWgZ,rGMRdjd-.MNB/6+qZ:$5&KW_CjT?
%!p=4T`gOF+fa/RiR+$??-3[oDVF"a8$-#8!aKqL1N49s(>A2B4.+g,Oi78DX<OT.3J?E8Hh+"9c`0f7AionH+X5mMYh_ekR_e93Q
%g_HkI#]1]YRq`Ca%OE^0ecqVNdnR&h@gGtaD&Y&WHN=alFmI(>7maUV1d"?feJ,SO^n_28kYf/DSWQsWKj*UU/*]Y9V+DU`^9N6+
%8m>!1iSg_&BsRA*Pj$/!2agA\c'+jbRR!Hc!a.=d"PH=C92OEC?W#CUCV8<CA8;oHh(GS-%5=1?i<34HDOAg'3_`q=Xa:^kh5^*U
%nZ8+8!OcKmI;n9j2;Ou.:BD$o>^,C:M\U0=[?,?4[4$dOEWSh\o8PZoSV#i-@q(Rl[/[L&H3qil?50F!%XUssH[6?S`_Z6fl]fbF
%>d[n%KGJRY,#(*-_qAMoMucZ(U#PG/b'6"Z$SrJ.-ruk;ZK*e)VLH"N%2jDhRi2M7",ouJi*"XHDQ!&(LY',R=(5#44A50)Y?NfT
%mS^OBeP.fWF$o46aKCP:ELp35S@Ukj_6/Xu^*ElADMlf\4-&>PIES"VM=Fj40I/A#%/eO5),_!R/XZ?W.c.`@N+$7IG.nnjA`d9k
%lss%7Bh+[-ZrM&[at5Mf[o8iV'Pu-jLibVi1MA*]!j9R[LCLSciTqd<TIj%FM%O.B.n8r$XT4_-$2;Ei@FIR!<%!L3_@"rXl-uDD
%rWce27>T^upb%Y<TjkJ&@\\1nMC/a+/TmsKP:nG*m$\D[\pc!,]i_/?1HQU_;P:Fg)6(!`J],-+$oqQK.bM<:Y*H&5,D$Uu,1EN1
%W2GKdOADVU=(:BL-o)!8R342ZH@SMVApjDfa&I4;G%*\r/IeM3$lV9Yfa"m8[064$>s@WAW_a3'PT'>""tiTMg5;XW[ju1)`5J^I
%d"M1mgYpdtC`QjTZ$6UmiSLKO!(N@9A>hl-@d#j2KA7-p*0Sc6G3`?gJrH?A/cuilZT+uDZCgRte-2n\&_1<u?V"%(WVRoBNH^:\
%X$j_GFW7s3ckXup@XFT_GqSe(MHH79mQFPQ"Eud`\DFVC2KhiJPE*p&MHan[;2oOQ*L63Y,oq28[43RW"s'W3En*W*&7*O#,jnm.
%)&,nVneM]bL#G;FYjC?0MF&Jj4Q[jSahs7>"#QO="5'0N"0Q9W'Sk/rn;`YdC3e5tn_>)%JdA7N6%4VV7C!UoFF>cq&9@QiG#a?s
%=*g8^K!f&6+AoA,5-)P"K]Z\W];!4sXF"S'ds]jVe8]Ne2P@^sA1_DO(rbMP2B?t(1^TmNA8Pd)J!t"f-aJc)VXQVmkoX'1f;@kE
%"Z/:NO9)QmdL^UQ/g,c,#dg9S^j%p`E&b$ISt,q0g>SWb$Dc`X#jAKYWbj`m'J)l\"k'+PI+@Z7D-C;,`"<P]EWTOGU6)L&<a<E#
%RC9?YD7R18Gl\MsfY9$oHgRhJ&OC&>4/1qKL)0NV*O^P0:UP^VC@1W6r[Ou=M39c5(ejK7NG1-6;-;a,\.Bhu5-/-e3S3t9871G/
%8Xu^gLYpGs7fj4?AQse;:12oVE-r=`;nq!Pn0`U`1'%#JbOedhXhisumr2:me<?>1'l?,4K\`CGoiir^B`[S[Xuiu@=2LhSBHcn@
%l/Uu^:1Lq[%f!q#E9([N$hoJ9@^$mlm%#A$m\^di-qaC%6,7jUdM)<3lG0+J3kY,?kj'%[_().<Tjr#IEKio9)eT7C`AFe\MULWd
%Zg&gTF/-kWV,"&YEE+?%i$IMS>uZI.eN'S3m@nk,?Jo=#"CtpZHYic'b?2[:egQQ;V<4gD*PBmqL&.OTAN;.7L=OsE="N[h;mW>7
%>"h4['pl.YZXmfI2Mb3H[=CFD1UFh]Z7cr]k81VcIUWfFctaZY]""'[NCBna3,l"2Fc;#dA=O%egq/r?IFjGL&nK5!AWkJ]"7LHd
%jdFp48lJJq3<\A)/G=1F[BQH*:.gZ]/?'gL4/iq*/,.JUd'bP1'E44j0:7]jML2JRduUQ\CXYWtG&4NX7X.rEB6G=A=:N5HEk!nB
%l:j/3W)%SkiX"h!i75mQcW8&+24R5tZ%D@fB+""e_kpJD?KY]3WRYaQBOU^egYf*ucA!&D1S1V0T`[B'EK>n-$F#-Xg'7YS!>IZH
%)3*0ma1*%I,I1g1R&Sp94)jJ3Z,>+**&SNOJPF&`M\*q19\oN&)\<7qX3imVf)pTk<Whr'"hT>UVU`RULS<F\USs0_[@PQe`kd;D
%Tk.P+c886nheFs-Rcblm_.FZ;6WIkZA%a3FW&EZaSCBZ1Tsq':L`;#6HHVlEn5Mboj_.Og8uN.[j24&kSKMZ*VE]3koOd-Z62!;r
%j!r;?/&bPqE<G6/2X^*M2GT<D;9!?.:7>cM&)mFk?'b%`RW80Z8YJGC)M/XP6OdkcOD4NlE(U+Hk&<"E1?\q9ek=8fd5W=B6E9/5
%>_#dJ?tV=!![UKnFUO;#(4iV+kr?hH_:X[A\Bkeg2IiT^k?]ufSKSo]K&)kfAb@aa$=)rI<"2Fr[^-jl3^[A_)+Spc"8!O7B/AuG
%C,:m+KE`9o$!-JD4:YrT*%-0e<,iW=\cg`)b=1-p*!_sQ"L/0kVHkg-AVe.a`)'i*+OHrn;0lqFB/?,=#*YJDe-qk3PsE0o-jreo
%iF/sBENuMu9]C&slj)O.cJ$0N1epALC3+2\iu[QH`XP_oBWNc7][YJFS06>(AheFf#YB6+H=>3WD[+`'h![t4eX5#6`eU<cnM8bB
%R?Hm8gf]W3pheq'cZNNJ>-+nDm"N7%XMuNXLe-cPerR76d>u\H2:sf5[3=MR[OD>1gacB.B$(4+K4N5#kTU(-gSSsV"UiidH:`E`
%5q0X&A$=jHMS#*RL",.s$N2"k;c:"]4Zm3%V&`SEW5iNcqKo;B.e^*HB>(U*nQBDgqm4lfY4N10HKnH5Ynn`,SU805>-D<c5Vs76
%\"8_H[W,,ZH_fTP^u8d$E<-XX_IlBgRRfR)ip`p`FL2^\hVNl[#[g8?XICP"?B(EW::`OK3l45@876(i;!r_NeTBbM<fYGCiN%>>
%(Y2=0[_gn6aWs5`)*rm3qj.JJCIueI]-5X*!H7[+,mB4?IC"L"IC$hlC==&7>,MG07JgKBQeHa.FS8G`mCW%C/>PoGf%ur_9XpHN
%/R*$0CiocA!TYEY4!Yqi"7P,^i$?crO:eH=_pCtC]n%sfQQb=r2"-,0's)kEP4D>`c7_k<WI$TC>oR(SP%osAi4D$L_60glgh5$?
%Ub.L->Lo9'"2_Q+\kOU'<o"0Fl4(C_@VlNRg5gk9]a>^LM)/hp2c$42@9f^X2/s,a0K0s-9k8KW/Z-0kj^#!L"CB,g1$,i-g9Q@;
%e8jeCgqZs+[j@<"]ns("Nm5T.>gteC2GUt&N=q.aTt/,s0<rpEg#+=l_\[FI.jKj@<Z>i7$BDfZdjD\3\PWu\/m#4*HSor?'#uR6
%O4YK*,;,\[rQ,bi.ki#Q1`C)g]SdE%fq47@KMlaKm(h#+fC5Xe[e7T+Cn*h>\N'.)W%UX95ZP;,*$HQ=&k:RV9aaHAA=a_I+b(A0
%&bLNX#`W0t8s2&YPcC*1H7=>T=*5^>X49PHlZ@>ec:jJ'^b@^1bMkC832Lmd'LO$1Pdu#S\pKuR2SF.4._q.d](G9ln^IEsldXVd
%\8&](SR&8K8,XQ;=!=.aZ)?I+5a$>GhM=o(,lg#JGNR+]$k=3bF+=7/G4]Tqq@hMY2"NdcYe..aa^nSAm=Qj*<JZDKT3p2tC9l91
%O/l!]_hW\TqG=eu/`I6[<d]\3m6TOq#d!n/gt4$5n?D![lU;0ZrY3.j946Mr`-Xti:8?`.\9/;2#=S$gn2YN'$hq_AlN^K:YKWPS
%C\LjWd,H%4jS/V@;3^p`4'W[^SjO6rno]<_'-^kq%BrFPJFO`r?<>"uWTb#c:1-.j4U>+'Cblg/F,G1e1V=lkKbX;3Nis*T4g*OI
%9SGronE*ON=u<Ur\<EMAIL>@E!R!lCDB)u_o9TAJSP/CVeYeC?^@H:H8oJk]s3dr@6d7&uWj_Ac5eE0=S<&5GZl>*Z<@c$bhF
%</EJh7lu?oKUkBueYg8q0INck;/H_^.oig>?o^C2o&6<9UFXnelhhl6McP:0@9-&odulTu4D(uCRRS'1'T7F81Xg14ZXsEt^u8kU
%%q:^?gDKA*<6crYguqaHEkUFg,VjQ.(4)7N#LEq6iJQ6tSA7?Fc?cLC0C'pIPcpih:J$sLqj!M4_LAb;'507)b;XPoU+<$DOmR;8
%(qeR+OsEE35#eqA6:fBrY1iUSNr@Gc8V]iqk5CU6Pgjf[]b5HpdD=Gr>LeMhaQqeI/mlai]et'jmM_#D,:Qj])MdIP5FRB2:=E4p
%6]<LmP!a`Y&bVulG^;?X+mMhM!UPj%!S?p'WP_AYOsd%7=<791M7e-P+c4\++AJZ#^jQngg):u#OsJF^MaGAEek8779_p8Z.4N)Q
%03]ab'-Dq4"T-#)pFIT&%l]`Sf(F*Xl<*.WePE(Cf&1Q-H),jobu.?*f(*9BHhI0tk5Kl?>.0/eK4/,enco\*?q9gc,cgV"'Lg_2
%h,8S7R5OrJhVE6ns)<1714E4/Lg1b"CQ!>:Rh1hW%12;Fgc)bK(NBQeYMg>#e1PX6T!;daLQ^Hi+ca?3Bf5>$cou=lW``Nl,9=uh
%lO$G!*-f'(.m.j\YAn6N'F/<R:@sXJ_,taFn<VNBmY)^<n.l<YQgH'WN[lgWNdVpSC3Y9mDoR]ekS@4.$;%<R?:!4s54/hVnqA^*
%Q`f)_41m8AYAmYpqm%Z4gM7u"DBD;<$B)MZ-cR$JSH-JqHD3H(F^Wg<"5]n%,0YhicktO!ciqt(pZ#N/J,P<3S]&WX[rU)a.Y&%n
%VC#q7BStbgY''%%H6SAoT(8m.@K7M9m%>"p2:1TueTHU>2Qs]o",B).n?u>qGT";EXAu4^Q6.s^cK3TPBJ&PIO+iAVC2/VDD(u9L
%hJiW&k?hT)([C7Y(=cU.<ArZqOA0S`=;S#lUn=%Wf_*sK[f`Rp>0!A@5sK78FmC/sc)]5f#@@Y?EFSX41X7E[V&X?AZ+>&eppeM-
%#C#Y67P&k?'LIOp1m/W6N3n.!i/F\b;TK('6T38?%r/K(Z]R)8'Ts:*r$.+qn[4OUr5`WdDV)A]2I547gMhS!DSu-!\o$'.kglnT
%d;?D-7CV[\[WpHSl7QB"U>'j>Mf-H*/\2DC\*18+hkF>e^.#MSI3\U4WV7;Ef=0E&V,VBAkVc]'U>'j>Mf-H*/\8@U)e!60+'d]t
%gMhS!DSu-!\o$'.n_p:]Y]bGif$^X*]"UVBiVF>GL?i&cgTGJBD7I;K\)I0!h6uWRF;qB$d;?D-7CV[\2Ei[pDp`F3^.#NuqA.nA
%lW\HMY&D^*]=p_GG(#9lmJ7Uc[d./))m4,,DRdF"-"./A\S*p,hkJqGI>]=YMRpMj]A`T%m=sq2[+ha%d;HJ.7D%OT(@A!b>I5?P
%Dp`I4^.#[$qDLMgY&@8jde7gplY&4_hkJqGI>`:9qA/J,lW^_8Y&D^*\A#p.:PcNp7D%OT(*snmm4RYC:"Qjf2dG77h/Ie#G2)[A
%g'uZCBJs,QU=j^<)]oinK4m2Kh\Q/e?/MC4E.*Xf_t>V8D:c8\euir=D7I;K\*a#-h1k6"F;qB$EClNAY&D^)\@tD@iVF>G[d..>
%)m4,,D7I=!'k,6_/&PZ+Tp8J[5cY92Xd*&U5#m)!;fSY!+`OEGiS9M6=rEbI3j=%3CTS"r>?[G.2mb>&_3\=C"d`C1`KsbrYn5=*
%N3<nsbc5QT5Ns+b'eOitHZ2n6e+I9Zl:NS5(ndmoksr'pA]A^+Xc.i>Xc1,P(!P-FTp9'^CdT_Z`cICT<a$@B='?IC\$RoDYI$&1
%)Q^;$"E%pb#[_:WU>+&;E;#P+XUNCb9<AUUi2'ID_;eP)D/*Y5\/_;^6S$'^=DDEWar=NPkVfe'g_3>_bbEhrI&3+m8#'g1(.202
%1As16X\:r(W`DU*;(Ju?<2u4W@pr"\Z[,l`12B!JGuYNCc.n%SB%9.<#O44S11Re9i0IDu#@sg!iLsIjAb:;YeB3`6U:"g>Q!$gU
%dE4$&?r]=jn>t5t[8s0.7Fd66Q.K1WDVe8Ae'H5=gsX@;?/1^Vf#fP`Xfq.r/(79!hh`8@>+*p2'((V6(H1b7Xc1,L<lT*15A%Bt
%X+MRq$'MFsR=u[X_i[Gf*<'oKAl#V$#sAr);g%RW5;84ZT>G@i8GeP"H>QDPP(+aE5s%H-#^iE\Y3HnM,:6Nk[Vna^6=?^^8\06^
%?8&WU^P6hZe^CT,9E]/8^km8_=?Lq^WS3c9(D*]LPBmgpb=usCiYnX#<BE!eST^mN6Ylo/_0kVPd$G<pCM2oaR;g^%&83p[9^,0J
%i7.L1LT>Sf1JBN*n(8bB@r1Y)&5>nR9fpfZ]9oI7d9F=o[=AB]eF?[5'$U/gN%,aa-]M<aBkL>eO].N$&o.h.<MpJSltQad9[]:=
%lLIaV;QT9hWcBFLda_n5j\/Q-#/X$9?3tarWkKe2`6`*mc=BJCbD1cUNaF.]=-h=(lIG-2ZET+YG=74KPmN+)=*6;6o[BJ&l1(dC
%a\'K+K6eK^&un\UKf\$`7_0V[X)97o%K]UX9@f1G5:nuJ,\qW(Durfh@nl,3`s!j&9"dFj*7>*'ePQO`[A]GD\WGc#9jjlh8U<Cm
%`gOC?+>G-oY$HduEPe-/9!@63RO>U[B?7n;8rKaeKpG:Z*<EMAIL>^--Ntu#[56m;OR#_b:,81\>dLM12o),A5ehkmb*a<Sar)[.
%a$Fi(8[FBI3:o6<OPsXkK6EVEBtLJbdQN/P@OBXL_,F:V,Y@1>@+,c5M&TkU%I5`?"0,ZS.:3o<T2UlLVT)Z@:1aaem.bN*8Z#eD
%b@FUe0)C*]]'9^)HC9^3N=8k$9FpiCR>UUe)!"gs:XLq!]hF0b/*D`Q)M,PQHpXf=-EB+"TW22lD5l.1b^B]_IK)"sAej!T;`eE@
%fo_=odk)tFNUln9l]SW_=<H7o5DTY^]hGNl[\ni&ZoHbBZZ)<j(`PYu.rDMp*ceG`jeiF=KqlhuJO2("FK;&`8"8dl@#CW_gRX85
%=Y&@sUl`1.65"/S@A$g.M*L8`&6"94)E#+VG'%n$,TSj4!H`Qr>YVID7?tD&lKPN`>YRWt?V)Qc/TuG.Fd/p>NY@_CX'+WWaLI[)
%hW%ZWRBGuI""q/i57hc\h6oCN:,8M-OP9B?,^Egr,SFsEmjV="Ct'"q]VA^8kER,+=-6pX_1?VafigWKD%\k^%tpo+h=r:CUPYt"
%\-p@)PI^:R[YTM,"+?rHh)K^T9<b'2BIG0]d1:nq%33'Y41ol6bIuV*^"9VZ"Slcj2/VZQd!8?TDY%_6,^AqWBq^q.[\D=-R<7\S
%:=I'IlBf/a%UP]Ti*CJh:7p1_##-9[Gul;_W#mEcIF:KlUJ_P5lG:dFU7KduS9=V=@_lW93qUXs9sEq5//E"c:p5c9atK_q99i8m
%IM3>L^EUgZDA-oN=;sShp&2\=._CsQ;Qh:h32TQZlIS&B&TB7>R%+WSB!PN#`]5[aN2m,FOLK789rT_+[dM'\YunX]JQ],2UcEGl
%1/bk,S'/17X^(3C8TGIT;G6\NpJ^N9h8%EL]#1RB/;h,i^J)_?\fo_P4fQ&=F.'Jn0@qh1%Lf\>Q=+*H?Heud\_NP78*\Ce"ePDN
%m[U/V[JFjF2*lEUjcPldAS?9Mej8TId/b6#JfER:VJS05ZTLN'?6/rC26-dEF.,#.N8)MhA9MeCP@<ifN@omI8da2e6Hlo>-($<$
%9`'5M@YWOf;7OKNDNr#"<EMAIL>^e1[VfD88"LblH7K"-a0%mrQOm)%ULO9@%EKDZi-Dc_5K/n.7ZI9Yg,,=Zd,sV46p.P6>lnj
%lW_VF?3<(]4hti*g;Q6R9mMYVUg!@Z8O?lkqpuD^J0%L]Fj?9c(C^\l?p$?X#=fZ2_lGoM9'la7>G\tu8@+usDbZr>MNLd.1\E"4
%\HsN:A[.<gph695q`J5f#0!t"26jr:MCT05iWFGSnWb++&\,L<oL9BPg'M[uOJZg[X<8NC=@#!\<6>4b:E;(r!UP`FRg$)]/?U.d
%]Q\X0&O+FhXf(jHB^M1e^2SWb<S2G+&5Z78)irU_rF4r`7(LcekGe.ZmYY`al+k+Fdc#&BQ`*u]c4En=iA0%Ob#E:H3mM'F)#>+9
%`qdtWX1!8jH;Y@N$`7NSTC^7*<cQ5(^W*U[f=Qn_&)3i$&c0%<qdQLZD<hR?-sH7H#7b%piGF2]RS"X3;N#B.n'40gnUPL?-X,!1
%fcr>CMlA`2)^7XN;c!N-=*W2;['K"+h<JBa2'$f$3B%IkD4KfSSGHjidrGH#4r\J.i3(RI,[(Ys@B5TlNGp6L[CO)`jB%PNn!J)]
%27^=XcLptBa,]?V@%;:U?IkW.dZFOT4'1E0[Wun`nC%/9[,=.oI1b0/_J#D0"1>Z]W6Lmn4;sdPCQ8,@bImghWqY3eWFaP&H'#)$
%=s4UKm`-kt.P;hVICOjQ)]KDu_nAbm3aD+AD&,ND"L`Y]#EDE1MYA$F&uF7KhWBbTDS3GueU_oI06,EL]23Vh@L"fJ(CEpV]23Vh
%=+q>1=)X;lFfnmi75*=C#]Cm\-#7X-BR[q?c=q7`aAup)j[(Z.)P?#k?&`EeZu>e#FV%:rf_Pf=`7<@/g>Yi:/=`<=PSFZ="3%+b
%+=\L1\%='QmMHqSfca"uOddBi2H$k_CiG)CE/BJo/moqDLU_ksXQs;K].=jUDp!#&P=\djpGe_.AT<d[)*AV-F1q5?3*dA4EKS^M
%<;MNCY.'G[HN6Q#Z%dHtAZuD[O&u'3#La.n:[b@H^fN:OV*io,39h]B.bOB(<a.LXS[R-iSuLkhLjS[Oq4KIeCE-[V.t$nO/Lj?g
%a&Ei!#ti%LD=?;M%0SKTW#B_bTX<`3c"RUsrSQRn4N3[K0l/XO9QbN<cBL?0`IoBr8"D?)n\^[co[K+kbNTlU_QYRY8/9oN_@@B=
%`QFmDMtU^fAjHQYDE7F;GX\p]WEClAguGPC]W>=5:!N%8(s?#C[i9IH%U)QNTO-l`W@9qIo@.:UJnE3s'rMIP3Z,/DQX#M5V0L[8
%q[Q!I+\#2.Rj_]M*deSf0*\Mt1,$#t&Dm<UJ@a/Yf^A"?<7,(r*(8@CS4T!::f:Q/5!_hsNQLT<hC#oc.Wk=)mPrt`nu*#RG0lX`
%K^5R$^k7@t/YiAe-Cn%iGFk^X<JK/nphc^&k(3#^_5U4;PP#2=NB^5@PutsZPq:/C^mQ?1_RaZ+X];"^/umRbe)4tV<+Onjg:"Me
%'oT&GJ/G13HP^(&i6TPEch-e#=1Nt*7KnG\S%qpcf8*c]:ME%q[s(GH@)LRh^:\3n1;Vi9riupDO%=HVA?eSJdJR_8/a&HaRt5$.
%h,hj8`-FY2/YRT0BRGd+*`5<Hj'qfseK1J%c.i'`=<?aj(Nmhj6`kLQS40d7"^_d_iCB2VafE#6F^<4os6ls4X`PhBqTM*:%D%]9
%pVuJGaIgPu+BH'F(/(0RUBQ,gb8j0m+o.E;lZU%_B3cDMPjssrf?)OJk<D%,GO3%dj0,lI:\]B.`T=fW4QnN,d7!5*gKDO)@MO.>
%%K+?@hJ)oN%,+n8G-jr3+<KE\T!e"<pftF^5K2cTd=.Wjfpsulh^k+T`NNe/%Bj<53OqHU4>U<]Sc=\AA\ZVErI2@QRnd/%ZXLP,
%UC_K2$;l"*X+b*0G-$D=V`Lla1W,a,S?>]OA9N6cPM)GqfD$m;.%fe+l@.!a%V=srm,!\>;7#t3XgcSI*!EAeMr'QDAPPiZ@eY$&
%Qh%-$K^@JJO774]8(kji*jJ[-r&XT.J5:;7oY8\Rm&X!p@3/"b\mO4D[9NSl1LBlpooCfMIctZLlZY7g[rE+(\#N/0lrJKH).P]6
%E^oVIDcrX*1DomAm!\QeGae>%c"H08&tP`mY'-f`j4C3/0r]M$rO=gmqkr4O;W"Y9PT:^d;RIXb5-K!Bh<X6ifYq??eTK[HZ8sRM
%0TTM+qrYu1f0<:%=AlE)Y%*,OT[J(2;D/GakPKJf.G@!$(F@%3f=c3mmH5A]rFao$ORrGXB((eD"]tYDgW93@aRtn[;)TC$-)@0<
%odoNC2=95[goomds3uGY%jd>H0QalKIOu;A=j?-J0__&Ycoks`=An%Z5tY69G,#<(NSWBVp=dJqbOH^NT4R]>m:N(,Q2B^^eU43W
%F=\jo]+.B&AaT,.1Jo(Mqd6naI$Jks-.uk]]lKP4k8F$mBA@9a;u[/9muNWC=8d3paSFB-ot0V[J+52,r;>r+Y&<)Z4f7ro?%'3Z
%GJ`^2U!In2Y(/D!>fmo#[m/Of*BlNq\N9\TT%uPSk6(%B*o5W<YKj%YLXY;^Zf@gul1p\7?_*eda8W][G5oQnmb[s$r+:3M]l,PW
%Y&6H%mW'u,`:Yp7IrnK3V*2Q&3/khI+$DjThg]WlZG2^GIRX;2Mgq.<&)Q%=l&YFkq/Q$%Hfld&^,p[jEF9p;kKeX%EW5Uqo_`=(
%pVVc<5G$b\hLG-TJ,.nHIol-qe&.+>s$(u3htuH'?i8*"G>9c^mrshr?<hufqrm8Akf`qTmc1@3rRG+*qf7I1T,\j]VdEaN?%-pB
%/<>6@\+8pWgKiYug3rnDdJWb8dC490WJpf]n]i4m4GknCF#^aVkmooVi00'8k9o;r-tD9Rq6jY3T9Zk;GlOD[s8V*RX6sbfTb$D6
%kB['sq)hZk)LhSEX/!Sa]8+"2a&f1bkNSa;UQ"Ttr8qs<F`c8!]@ih@j"^eYLJ\9UjPBe)"\mupjsb78UHUX/O\DTsqMc0Q#>WEC
%4d!2l:QoqSAVA=oEVKA%o:rYjaj.Z7e,MnjfA>BS:1edXBB\dGs,^u<UU$s5*h7_d$`6palLK?Rp8\"7F5BH?<c^a"ot100oS5O*
%p&]2=cd-sOf,WcI-Y=V)J+&-LZC&1Zh:bA_B8$7s%0#3]k/YcB4)3-N>J/k@J($$*qJQ2ahX-Yhp[`mBl.phrbKJ<)F:-s^;-*2)
%s*O]_DI$"U[f"^3.IK=pgqKYPk@L:3EoPuXc^1Pr5(K)hh(6&.<H53X[8-L7hHli1I!=5hpOp$Uj8@mikPU1t_?V/&%PQ+&c];u;
%]DpY.%MsV_:W^(q2t*srCq]em0l9q`l*m9iS6]$RroWi,DbOEOHubaKn*gV@[r=IN1]Oo^ZY)3ea'ZUBB9/+V:QPBS?(@P"35)li
%q,oE,^&@TelK5O!hP[BsGMYL4]Ou=NJ'^F$cd?6`N_V7j?H%`G&+?oU++34Qqof<:R?(\RRK!cg^QS\i!jIpPcZ/rnSNk;9B&OaP
%pjr7!2#j,ar2NOgD[u[La0e!VI.cjglJcRfC4A%,a84fX'0;@Qjtl\fkjH<3J_^E73Tp9TLttOsq"$-Lqfhe$qt$$;O3cEbIBl*,
%l1X?FIi#N"G>IBKF,0MYi1Kr+kj"D9!rUk2I`^6sn'B28PPl;=Fo1!2pThV\]3dq;?gSnB%<Behjh*a#cFdnjQIhIYU[R[aY=?:K
%$J:3=N(IaC"5'&5ED5s-H+m;4!^:ZN"e:at-#g^dG,-EfF7Rr?rTP?f!BHfsJ,50hrL`nhs5"W5r7HG)qt#<*h8q>4Y<S^Ro_e.k
%)S!<cI^PYXq1GhN@uk@hO.E:<<6'*-"SKg9D\k3=/GrR2Sl*+>q(:4Gj25-_9@hsj'D$&nTa_.D6YuCg(!i;A#J6RCV;,DQ]3?I0
%l-?/Rmq$^t4I^/_3TM2NX`?*$H#KtO(GO1j7')l4dUCVsU<mtMHEUnOD3+??h.f1Rif!.$%>hf+Xg_AZ8`/@3V`NNVe%A3!Sf)&\
%jfO[=b:J/]2dY3Cl/.meh"gD4\CD$H!MiU+n':kYHhYkk>sDtWdkgF2LPCtX=iE&.\A!5]Adtr]s$"3.r#SMsk9K94*)Z,Gnq`H/
%O6b`M-dF5%EG19,%JOd[ik2#aPZH=qBtmMnm-h.PmsVN,s7kXX'93E;e*Z2M-J)kMY[q46>*#<)dX.Y@S9\tASF5Phi=]o$E0ACQ
%<G"AmP(*B)n^+hS.Y$?4Oair=YAdh3me-@Um5a1N>M]9j+:S81k=E70?bB9Vod;uXRj2b,;B=;'g'%D0h(joY%U$!:F+NAYMl4o;
%^+IX_E*FULDIT'r*9J\;Z.qR:A%uC>r/ImtYAk`0^No;&rUC8q%)1dHep=M18Hh<dC^6-8rq+[iH2VStS)7_)rP1MSO4*.gSJL[T
%l'3@(l>Ub<o]kZ<qMU94Zh6JaIWagRfQB,Y\XW,;/HLoYG;F8NG5ZRZrg2EoUY!OVL]*"+k@1SA%CZW7r69.EJ%EBUT!rtDf#or/
%o(;kE`MnC\#?M^#7lU=khq`d-[FaCRL&L/tpu^R.^HrC4Yc.RY(3Xlk%DD[OX.2B_jXb0ELS;`QnSnKem)d6.l+UIejkWGi-PpEn
%J),%OjHKC]S&]>NYs6`qR7/Y*F$sqgbf)>^rV+gWr=l\[^*2p9>[3igYHI-7QT)*V0Ag**_n4OdP:S6Nj`5A6B`IK!:1]]l;Go*#
%?69oO/h4fqpd"QI?_4]#`H_s-\80grHuAn1DZ)+<0".0\?QG\Br8hQ?nL)$(hE^bTTV_Tkh"5JEb20hC\D6<os.K[cCPUL8&KS>u
%bWh9gm)O8/p$q!U&GD-fYDsJkHAXmN]N@^qQUdrT.b!CFDt[6k,P'C(eR+)`T-)?52e"qB:QDTlL<N/Uaj`Y!H$"G[rV3"(F8kh(
%qYCB+h-Ep"IbpfKhP+ARnrCeq89OgN]3nd"2qc;n[PSefn"%tSJ^enbQSp-gr_AsYdqA->**pj^5/6/!qX^WJ2]EOjH2=,Yqom1M
%UDJ]T;f(TWb2_C7C&lP"o.\Dr*_g8UcD4#+*]sA];uPjLB2:_dTL<gY4lKcInmCS[r/^KB(S?-Ff-dK2p(-BIHhe1Zhn4lne&-\6
%+-6E9s-pT1hCNT*Yf.sB;$hNFp[>[RIgK&W2emPaHF<e8h5gM3nbSc*[MmsF((i^@,B`cl@^PWK0uV4I-o!_ZMH)SOOp'u55;0QK
%]&Xq1j"7h+k9]lf9m<4@@mA/Cgn3aYRBuoEa"[O?rd16Fr"!:\2a9"rOj*`AqIb7*)Z5[<8oEgY7bq-^nbYg^WR(T!=).M7XOP"q
%cYAKDY?CV60`?::O65-o?i=j+g$.2OU"u#P<D\)6A+/Ti3hpP8K@OK.l+DN&gtf'XZDDssc$N0JN@:\"+51oop)W"Sk'uRdL,Y]V
%45N%`ks@I<*[C7Jkchh4fqWC[T>-O#n)YA#pm7o!csGd$^S^pWQ9-J)<+9<tpgb%"b7c6W]ulT8M"(=t[41Pc4Sa-.]8qD(s7r;Z
%J,f')@cWe]dp]iXXh8j8BFk+;e`k>m<U7\>QKGS7?i=bSp[(-CkpO5(]D(K<*rih&p5s@B6GB"e>#)8<M2%;o$m/3_&H1\f7s)`A
%+-_=*;"mf`rS2d8rRa!f2u4^ogsY!GR-4>sl:&67OndZ99(+nZ1=ss.4P+YWqr;&0rQphCqPn^R16Gkgk7Qm1i\?,1GNDfd(NZB?
%$JEf'k:S?V=kjdM?DJAA2]!C2@mV)?2gh;P9Ckh3QKM2:pgRPr@%V[Fk0`*X$h^Ce3eDq>1Jr/2c,EiGL)5j,kaU]m)9Wi(\ZXLV
%T>a]uB0U`[aXdKfM7_&&)WJ;.F$Y'i?Es8thbh$V%ea-_EP:BQ!EoVY!MOUYQoN:Z(r*&f&Nh@gU"B?`;U@2\hIL<@H*A\>`UaIR
%S;VCBraa&.N*kg"XIgg\ETcMQmrUT.l5X!d16^i>g\bOp[\hL4]/mSO:c;X[?F!$^4fMX"?OCfEUY<uks#uD[(D=`V0>>=3Sj%$2
%A_dj2?PM=Oo5=:)Ngo"cD)S4pN7_n^V<q[(V>bOa18)3C0S%LgHjOZo.dI&.:$0E9NY$^?lL\nrES@<9.Zjr*RZPR'g>`tf:&`8R
%g!TW`e+jflO,gBW^>=8r9<1`<n_%,bX]QS(9m>)j#3F[!itlA*o:'r:fp8Tj'EII<+J1C16tgr^G#oc7[_I*-\bkm[r6#mH?d=(F
%EV\Tlpa0$1BF2]q@OLs4kOk]pF?$s?@V83_?OQ"s"l]#C/n5pj`V&?3NBUI@nWV/F8'63F[GJUgioX_EajKc4JZ::$bKhWh@Un,Z
%G-b0Cr.8f@F!WKBk!_R"2!=U]q"`l;V=/@kJo^1`DCG:V%q"^"QG#Z3?..6Y?([_%3&E;g?[hFSU=f)!o&sh<If/%"c?Z^$\(5bW
%c%C>:K6hilEM0`1SE+;lQal_QYKnk^^V4r41REPf#N+ClReueiC[t,FVsALfe8`3b?14uZh;Hc\[l:qn:/5_fSt@'!D^l8jo#94D
%F](Fa3re@+qH<-]#Cl_dIrbDFCu/.RSp;K2>R`:<=qMHsEjMD4-.Lmc7j>:R[99W13?RXl^T-mgLTu2%5HW[95(7Km3Ht6kIT4R"
%miO0R+!!Pu?bSL)\$'S(o/163o#^-_?etY=W:f6HoqrUmrKhH;F*2UNbPKMBpL!ssgL'pAffO<Ur`?[<dpfCoEk[,'rq@MI]5,>E
%%s?aA:]<7"e=Z?%GFs?Wip?L/KCb<*\+BXghLDOW/@Cd>k9Jo*c^$*@gA1Zar>.E]I=%agB6QP_D''f/h/njSUnRd;?6%AZfmUC1
%Aq/I#EG"r6*VXhJ*)=iS]5-*SALcC&BGMo*g%4ku+8k9?iLof*%s-Rh0q,l5=ERj0hoo$r[N2h!VRaU:m16rp\s_G4[r$Wf_7S=s
%ou<*ec[L+YHuu%74$;=`nL7<EdAo3=cJshPRu=Vlbc36e?&Oa*@qmrHnBpp2m9,uP.21lCgIRq7k)[.3IeqHI]>sMcK/Otj&DFR&
%?iK,6)UFnF4Q!@5X^?Hp`lB,*>qR'tTE"l_?64LW])-Js3c1-Fmj*!mhqB#RYL;=T4.eW^G5mRBbMimnBk*4d-$k%:m<YogTDIU4
%q(.f5cLCLZ+,NS'CO#UHl**B:kZ!,oA8@Z5DFg>!*sYKR[2^]=q6Lm`^UQ7nbk%miJf"+Wi;EB>@c"Bho[2.TQF7C,`GIj7jPoaB
%oam1jmbJ\GZhQup/?)%bqT&K:NkRNUdr[H2/dt1Qr7Cn;($Fbm?%.".hgK*1:g'H=cQ>3MoCP1qB_@AB\#Nb'K)>N7HIj1^htpui
%c%,]kDE2-DYM3pS\*V@bYigjhlO1*nSG?JUdil6ibO4`>Qb4hdcL$tsb&!.397BkgoCr*HqdBrW5O<#SJ+igj=]TV0p<d&-b0S0#
%Mo3E5cae?Y5k"N<MlX1ZRsR9Cqqah5QeRIO)p`"MSZ@1i<I3]`XhXZ'h*7L&gjJeL4iq+_$#H@;QUf*&Qo'^n2tK4Z\5/=YMtbLL
%Dn^S(59-gb-e*=O8HA%Ol?cLs\+?HqY8?@\IFW6(YP0\@@A1a!@D<fDiT]HjrO$,irr!eP/ih,']-\4C:ThV",sCaXgo`QR6Kqp\
%_>>-n3(nk01qb8"`[n2Y9OEuh2N]%)iN6#VD%cLreaW?c/.UR_f>B9)1"#.GN\.U;aVE]p*e(Gn0U:IA#s(+3hRY31^U_iej2EtS
%X%Q?^gq'Q4SP\u-o2fk*Rn4TKkBal7?+3QGAR`6@%+hICIIuLekfU7aSB\85p?Z`^5O`WpjONMd(H;"R1#0_H-H3Wl[rfq:Is$&s
%\_8^e(]$#fhbDjQ*rN55ld*=+]"7l@-h6lUIads@IrTn[KBGrOPhKB'+5cjrH@F!D*.+<LR_*[u6m*c4UV$BC?f`R4iJg!m:W`Jj
%oPegJ2(=m[d7A]X<DJDnh\#SMNk!tKT@,W?YEa+"dJ^ZfrqG:arr&>`D[Z4(:LfPd4-*`RcfaIe@/G;Q;tStNO6EMi"3%q#gjc4#
%/_80OTq>M:riqd[obW,R$-_is?mi]9<Q%,GfKTeh2KESs^Ur*B=2"=b\Ol4cmJ6I)oCjW\T&?N5[cf6Pakrjr?hKcb^N\H1j<'dp
%lq8;m(XcC?X*sR":SN.-`d;L5T(C3sY,<@KSUPj1I]k*$J+Sp:,'\<ZG4FSWGlO0'nZQc(r(":?55j=7/uU8hjo,"ol!]))e)S[9
%+9+p6!424gd(l-GX"X3+W_E<Jh0V##rM+if-%^F,Y&Sp\&Ll&Ec?bN])h7?UR2IYljVR9rIS[pKHh\u9\sMm`]q<[M-N3Fd2l)_:
%jS3[R3kjc*p5f5!>57$*)ZJ'2p?K!Q[jKFe/+TTWI_Yf*qV0l9a.<2W;5K2oS's1`!W,GgCqddd,jKBX^H"VSf8m)YoV3r.5LjWA
%?>Hin>+iA%0WE#$ZjNT)+^Vc6pN:SiG<WOc^N&o7,0sJ6m!=F,]=+8%e*aoIcShE\''8Y!5@0f>*rHmL@-eXC6!I,;62WYtJ#U1Z
%D@^;7q@84hEp'5*h<Di1<M+'Hk0k\eSGZnO:>4Q9p\^c='mHS=CsTc(5-5<-]d8jML6LfTIt.jsnR03jb?B9E:4]#qVa0"_!,C)b
%%nE6pHK:++]PPSfp!)7Yd!5HYbHnAo4X\G9`>;lD_L5;(9=I*77c*R6kI!=Z(]WK@5>QC0ZS&p7L*'\c3nRrN]&3J9q>O%L^"3V0
%XlmF3I.ZONb%pa;f8e^_>E:mZQeK(;2:"fK>#udF>9BY1c?8GXJ")f&U((2)2V?:E\ULo1Fm#F1XQESS-i9rm=3$ZPO2LsT4,8J1
%^7=r"e+54FmQZWt;^skn5<l`j^43CH(ROlGpo;ech4l2ufsfOWcZq]MmOIi'lJrOo3JXBQ8)N+r_672!<EFBFNlnpn4g^cmmB<Rb
%rqk^N%E3S,m$60[P4sul\F]>9b0k650;%UGTWPNOceVGgR+t6)ECU2p"a#-3:Ae*Nlhu%\44hNnkLVB?HKNXnfVm%*`@4"ADR2Wq
%=-*34D!ieE1FEI']'H3a;eYKfI<'aWON/t,QVO^,kN&K,_%Q+0l.1"d$=R]lQ#bWCVrXNDZ?8<kai3A`M`^2pUNSW1CGKrK`u"JW
%\G\J'm?O%eL^n[_UqZ+<L$r9_bDG`Hh>J/],X9sFs).T!eU32GGJRj?G-Yb#diCf/NZX;C=T"p*mf($qGJE1gptsKA$VkEC\2H!$
%q>/&VT6u-^;!*[ICsq<O$9pLR$*!6uHap).GBn1Lf@`fXT$9V#SP*\RrVE,#Xdgjt53G2r]6E+i/+Ukf9UF:n?_$#t4nSZt9o,B$
%3F#m=ENs]K>X-gtp*KHh7GkkU"*aiQhqa@#e]`@K<T2:>GK7U&XPW35*=*^pIQ#@SjQSY5A\8B*\aSK7GE!HaB3(qdnISEYPPt1=
%Qfg"@<dKp9HI3a7>775jpfpK;03aj^P.U>98(Z'_DpF@qmjsYMlBh7,8`keM6]8FtGeQQ$h(SEQI,nX82T^7.[\["EcUWb=II0ZL
%e4/)WCmoR+F=?VK:c57uX1e+>GeUfpXrb/8\XJ6E([&hUQEKP-LZFnNk;D8om#h.`?JKq9?\VM8o'#VsG@05^!TthED4FZ*`e3/5
%HM2%Ha8PW*np*V5`Lf;<p;`5dIeM@\)gC&"Upp"QIJYa:l-]<P%2?P:YsJB"V1m`#GAH[Is4r+RZKoSpkkg5#IonJ4`F+u_1UFd=
%b\U)$fj?sH-%ijDmlhD++)Z;pC+5FjbY#%D<;P+6Ii0nCTR<2Pq&cfEU4i[EIa=MO4hKT(rqmV,S"HRN#jo92*h_0PIO[5]mf_+m
%ds7sAQ2>2nPWup/\*fH5&1WbPkJ,'M^C.-`rnY*QpRJutFa?8E_8K6TB`4BY[QsE4U<Mf^Pl%ISjlKLh:J45cUQOGsdkC:U?RaR)
%4eBq@mO#DeN.65U=M"rhJHELdJFC?n9n0b?Q'WiG5A$eKp*\Y`_d?4>?-uu#T;)'kjhkLf4FO>TRX?`1(75%ah%Zcbg%6.@^Y(%e
%G*Rr!V,+Hb+\),?P*/$n1nT@lAX5,a)1S5#:(<AjYfUq(=,:+WAN\T?,o0\97GO'I*e+(a71f0D8e;K5JKScC"-=8.Com-lq%%cn
%RZ<n1.1-PY-ZiOWL$j<j#NcZQSkMg+O_I.GaKe&:>bp4>g>Th^`M]uW30ct2IXJ.LU-FT3$Q='YII'>!@sfba-0>142?P-EON$3o
%#"SA03irIQK[h<Zi5C2N%-<.fSS-m%Pt/=(U<@W.Mb^OUSE_Q8cZ%g>nC;c*H9.eL)EN#]#,*9q@a.&h58'm@MpVj'C86nV2P/Ye
%&`V2@2u&;\k7<L'W(Iuf!VIphQu(Ek-sh*pXrbpOGYE,(TPn$n2DY*^lHG\RX-kKe_cjtSA:QeNJ5\?=$X-7Z3Ee;JIH7%rQ%/b<
%*)*79c_m+N^?TULdkS_AGSf+ro59c,d-Ku3IUPSg'@pbS'0=_5)GkY.F9:hH%7o*r4pIb9!M5%>=u/<lYEsHDK+FBKd"a-Nnl&os
%4,m.<KLMc%pJ;0moj3s-PH[K4H&XA@aQAm4HVQH*%`U'sVB_dVpjoaE9jEfjCT6M[l:JF7r!cNSWF01<+6Dm=IZ4TlW<1CY`qAON
%R.NCK\L\IUL<"m%QAt'Q`cN7$qW2"\1P@Q+3=.VrmF"2/iem)sbP;9XMBZju+ceZILP=CX/?VBgq:42gR5nDYgSS^-jN73rk@>_o
%,$e81dk]4aSh$_uQ:c!O2+^r%S?duf(hLUQ7tTY^cPZI7:/T<Ee4"pt.&DK=FtZq"`58LlkZL6L:2X\iC)tEjgLF&mmLH!A\Qn?n
%@;cq[Kq8#U@C/sN_?f7m0MIW[Jl!C;3:A:g&E36bD\74MR,-.I&GCMC82u!["$=,,>sPqhYH<`.N,dTY,_,=npVn(2gZKcljMop`
%=pTbWlQ_?@2,BTQkD1f"&B0rCJO<:BooTQt(>cD)H`XM#*#ZMHE0^n>iMuY;KY%n%&Y>->:3bsF+@0n'-%\/c;h?`19E]F=nH<J2
%U^>mhlk'<L57q?M`t2.=9bP/Yd3JSL0esCSZnqn0'M90tE'60.,!3q=-pEDY7,,^F&[S!$7(dc'&4uJe6_k%o,EZtQAKj2mpPF:M
%Bd\cR5"1]*nrfrIE\('4Rl-ph>oAI\hA%9lqnWGFdqRA9U0$&jLd@;I&YjZ_6;3gQ'/>u0ZGZfj3FZhKPJl&(3YM=A-UA`mN?`F_
%d7FG&)I<lmK9A#m'ViWT>;U+"B/E:#gh65o5j!fM"%21Y^rF0lHte5#\(V7>hl^Gp=bd#FjO?4/U5O'2&FX146`QX0(p]]C_,-N[
%=@7@4g)4XlHI^@dJP\kn(_$?Md5R#m^j>-=d=u3A#],KUY2N1k;i&];>2tTsb/n2$Yi1;`^)T"hmL#RC;G9_?EYcZ/'Xtud0ei+C
%N03\rh5KJb"_4i)`f8Zk3bU'f9!#;/$G)3KUYT\O8dBug=<*okds5M?>g\rSYVGRgd=q+,Oln&Y+ZqP`?.$4)fh3kiUd)1@EC=YZ
%OuiQ:.?lh(VPm*<EMAIL>=5uY\]9:.csHI4SA);D#k0!d76#Nr/PE(P;uK`;qn2J,T<T/A^6erH0@%sMXb,!4&#]Hq)h
%aB?KP=HjT`>UF,C<s2=;gbN-'k2']u1f%.FhRr+aMWVfm]X8<7+`"?c-RF\kZ9;;l_;MftMH&=C=irX1@nH1_N$[(\mjqIs.qj2]
%,t=1D\R[;`.G)#pe:u"aFk[j9bIQT?\u4D^Q*#6n<e&6KYpu/)CJ>Nn'5Oid$<nHBgVMUU/ooM\hf;o0fi6jT=\$=W)d5Tp_WI7X
%9cGbU5$MtP?>QmV^M1kWNjMf1'oT3C)3/QtoHA#)-mW`C-lIZnW_CHVDq%ps$e]@;*q#R^p;BfkgP_Zr`840X)tq+>aOliQ.UlmP
%DFDcY<f^"2a3lk,po\pbKTkcVO^q-a8'/%Y3a2DpcN3uaa0ArogQ'Ikhfd`@o<+8mpO/r(Bc&k\!BG5`?0]G_a>Hc=U#Bg2Y&^8W
%GqkNe@,nQcNJ`XSVt.!@Hf'"ccLIa30KpF]X_s8@N<`/$[2l@gDSrWgG.Z!BVd&Z$A[e0o2Va_FD""NJ*QC8+1ii#lVn?tUkO54e
%WO-^fhD3f0,)ZP4j2oef%8T,+0R8D@>10`Kml"K>RL+5pjmpJC:5k]%H;4+G"lG%(^=Udr0=W7i4ViAhdUn[F,Z?M%aY5Z4PNb`f
%8Cls=K,g-nNnaYFb+,bKJIP'0m9V8EYqN'*ON^S0]g,L@._;O$a^Uf!+."`W#H^`^[#](f9XNEGRgr5GSkLX9IEWV3N*WRP(nS,G
%Qmanl0h"B"1L8*:@92'la;Ps#'%jGF9@V&f"+l5$\"!Z2kqHDqC!1U1c]%,43S09!-T)#/Q\\,U>g6KWF(=s7jGkjJ^cNN.Qku_f
%P)h-M$=>AdqYY#:g[I_4AJ#n&Q5:hD,`X?ZU6fR$oaneP-E"OE3)<AUaV>_'+IP?JRAdu[mid0e>\501a]OAG&j=JR-,ibH8>Xr_
%+:Y1pLjUr8ni<#<P0Xq'&<OWC$"%JZ3;AfiTF,N5VT`Z@B;:<n?WF4@IoYlJrKB_)lG!bL7Y%i`\NhFO=pBkBE@NXV48-I0p-&bn
%_l[T+jI"SPq;Gd&m+4t"i$$?"+]fk/$3esW2jDWJ;PF):%_4jgYWQ%)DKg1C%cpLBE8cm1L50c8)Eo0r;T)d1GU'N0=<eQMR>=PF
%l[qn'\nd]O[[2#Nd[J@=H"/2)XM]a$%BNWg<4Zt4b^hl*4XFN9%]?QFfL.lJ#8<BeCa`Jt>gh$Q-rF&?c0Ib4$Mh#s/pKKn\$;%F
%_]W$;GqG_$9G&<==KOs3Z)(sbMH_%#F564i8FcV"?&OaNQZIcJfn0UV/bTf+HQKF`FNriNiombAP,+>!-Iq*iSSTofU#ae0RAX<?
%9IM4AmAj3g4cZC?;W<qL?g2r!1c7>CQ0P*5?IYmHN31GjB7fTcH3U5m&gYfb-#4/iX_b4;UYY@D's$]B0'_h(c;"uE>\L*)F4K!-
%cE.dFO`q0(7:,+dm<jOBk4@=m7kJt+EIU:AO`[:6\@N$ff\0N)(IM[e(%,=brCcTW,.b*r8W<*IV1N56".R`_akOusDilT+R?^UO
%Mr&DuS$/Y=hPm>P8U/FR9!;Ve<!bq4=V\!r5dfg^'rplKCfGJ7lc,CAcM2/CPt7ltWqt=iXr!o!\O@oJT35X(L0^d_^nr:$B=5ak
%BJq\BZV[aF-bHEk_.iL"kNgp5jW'Y@QJ/I#>2;(g^(\EGm+o.lL>T$ahu8$CZY/%Bm-JCuR!Gkul&A"MHZ2Z#U?:QaV_^Ae\jY.[
%!B+55hDa`N8sO4:?ekuNG/tVL"las]>7-Bohc$SpqC*@0oZ,41F'rWZf0OLKXTuQ5ANVfG7\*sI;K!!$,K7gpROXUmrc58'W__p!
%dE+*mO2_$n%W'.M1Y!Q81TB>dGKj3Qd[3-k[e7(Wb,.aJ=q&+qdnS>!Sh\@IGZJK7YI!mQ_TWmd8^.VX97OZ>mth3HgA)ORn$]s_
%XA>`?gTgXBV4+U$guYl@0jX=gEGAM<34Gs+4;D*oQH`rG=(Pfg?6+ci5cc5K>$#IM2)Me9D'Vs2DL6If]sHE<lRleG3kMY`%.7DQ
%kH[l2RDs\1$3^nr=YLnSQ2]^GG7nUtEtej&mat&>%4?bt9MZ6r[smU(KZ)P]Sk8[)Za<=#F(VI)HbHEQrMj5^qB,^g'^rtgfM(B;
%$Qcd\kNR3G(>(S5Mag,mNdd'Y:R1<ShQ4T<e,.Ns6+`qB)Ffh0k;I&scu_@.r5#]aT,)=1]Ck<6nVm3Dh,@/($:8L:IREuQlBc8X
%qp'V7h!$NJIGV?sj&TQQh.+k/a(5<1>d1T+JO\#O+#[D=`_pb+O],`$nBH/F3_m2$f'%/<3LfeI1<(8]O]irnA&H#sa-qpVY%&Ur
%UN</t@TapIT%0)eGEJbY^ZTT)!j9F$EmE$<?k`'CM:3IqI8T.K;Do.qV\PT3KuU.5A"A,/Q(>:4TqKEM52eYE;-.%WI<W;2p6Rpp
%?X+Al&9#5$<S#Yr<Z>fVfVgS]4o1>18W[ON^!uJkeYoDF4]ae<Y8[eLj5#O[m+FF"Qh_Q/,pkEUKCos$oF"`Cenk\+f%9Q=Pf4r[
%&DjB&N=7B'R*$Hp!GD]q66Pn\g7.j1E+Cu'R3SE8`Ka#t\;`RELurYs"ZFfOjrDF]>fdaeY^M!d2DZ,HP'7A^Pd)!b:9T[S4`+H>
%,\abX7n[K2Q<e1*.FL9l:.s'O\*Tq=b/PF0\kgVs<R+Vr7l13m`1LA/V$>d`mmcBofXc20F+,c9M1a!<l)TkkO;t=cEud(b=JnK<
%+Xb!8e2mdX;n8RGm@#LDTt&Xmd)qe%d8[p6Lq+c@brXBSRl?(Tke?3ge0f)T4Yh18h9aFU6ON>n,;Mo]Dt'SD;H67jgNiP<60j^c
%8Tg"QeN41Aie6q%K\5eX64[#2.):QW),`"LEJEJ(gJOUUb@pZJ=)=O,UK_gI4LdsD6_Tm*<l=OeNDLi:n5!*\*k(2u@\?e!\2tYF
%;UQ'f\k`nd>Hj'jVC[IAKVJ1F]$Vu0bJ3YLZ!::1:3G^D*+Qm[eYM]0=t,P4q]WM](gl?uBq1Bf1kT$u(:l(Bgba;;2$`++6VEm>
%^3FF3rM1cX4K%8rBW\s/X2H>eV*K6<GD;(UL1V`");)n(Pd@Nsc#c'g3\>*J0@pJ>.uE'P`.J46/uQ"AH69Qj3cr(#n#FfpMb'Ng
%d2BUNip)7ra>E(457)A0&IS''IDfL+)!#H.ErE)a&(]70rbh\sM*Nc%!luEG9DP?_Y%;;c[oj47k^kJ&a$sUTTh\B2(C;tMs,]Xt
%a?q!$K\T@%f]N>(5obSp`hTD3_XNip1olM!j#/+uH:fe2SY\c<b)@\;k[`GbRIgkK,a8`[CcmRGQRijIACVOp+9YkH@[khuRTQ&W
%^oJ0b92+Ejf@YS=pESN3Na.ph_(_#tVk;4`d"su0hTqq>o9..ne(lY)G%3#t*1)0?ZYUe^bBDrW3G1&ET"]9:G9Ee<E7LJ$,VK&;
%<iukZWco>f`AI]QCQb$b\c"WgYGR'EQrn))Rt-]]FWMG]`*E1%*OkOP-5c_3`Q%5=jj!l3b%@TKBhR3LKj5M&b2pmaTuSYmPE6-R
%XAE4_4E@<!fqAWLRF1I/rCBqm'Va8jQ$Ol&%Z<B3XhC'a:l`TnG%-"-!XX*1L!\gK+0OI+!$SFN.Za"@#F*cW53Al*K:p:`"%'Y7
%Ee]0.K<O,I,F>55)B=<)WbNlEFN4J<cAo*fdEEUm%DS?iR$CC7oH8LgT7Q9'o$%.RWK4\Y1"e%O5;hrq@#2'!-5dhXalt2p(-$NC
%9&!&j'jnq0pt\&0"uZ`,`@#U<)1UL7NI!r\UF$5A"ACqg,+MODqVqgJ43gp5F:nI#!L6uU8jp!@gf@?%QS"(sq[f$]N+Sm)A?Lg*
%ISB<PUp7`;C1:2KTosT8AV57WcjaE<Lft'_6_l3!_rGJ3US)<`+G<Tq&)[%F-8ZHfDQ8Le"V%8\g!K]+2%Dr32?&E*N0#9?6fG#@
%LE[r["k=Q6%9(0UiJq\TiM!g/SX5es"p,cTbj`^rZ&Ut-?od`tO4s`Ojq#FtOV#VNI0Du6o3i-m&&,OJ7(KqFL[fdS3+V8)^8guA
%&e'tO%NQ2_Qp('+I20[)SS1B:GTeDUQWtaQH*G\6a&6@b)"6mE_=3(BoJor[KB+Une0hGdgGRT/Nog^Ff>N/bC$6F<pX*B`d=i7>
%Lh;s.bPn7RGm;>gbr6Gp^FD7&a8BmZD3uEHYOe*M9K2(IhOuCWma.CTO[D:En9Ymt2uYQN@Gp)QO[n+3hcrD[i@D*X"C((9PEBk@
%(m+FZGMG$6KIpXR@L[+]a[YbW])R4UnS"M1Sf[OnL_(=,o#=g\lL6k_fA'9IBpt3c:I!Gnpu+NRO5SZkDsHopb!Pjlr$`TBDSkPR
%qSI>=6kqo!B*!?Ki9QsP<0n>PlS;p:8b,V/cfQ*YJMW&l'htj^1HV6rXV9hb+$>.]n[\qi=u$?BrCbZQnWQE$3H)e/WcPX4N#Oq0
%g_QVVn^/"e^I=Cqo5XJn$)$&Fm%U:MHp?)+0k_o(Gg;.p3RG>HV*C6g4*reF3JBN@Ng,aR/jf'BBB(`TCj^M#>s^9L#@H,F`^np#
%9>m@rr?Q).#Q5aIins_n6^_l"]08(m,YT:,Q@aTk/'C9"Q2If4E5DaWHMfHth-?+@7W[(W9c^M0GMNJM(mg-fgS"LK1EPP$R#%q/
%:B/DE&H9&Sf/fE$UriX,*Bn*,+#Wlg%Nc3VS"]`,I!cCjkgg@c#W<orEUQ`iMq/;bPO3[hf`=g(DuG"5Obp.48`iHA)R]U[k+;!R
%qtd5^+I(@IW_.HaN5Af8;=#MI!$iR[=RQ9`dp,9s_>D/aO3SQHo?Hg_l,Es"V"HQsIR;W+q9:,&S&a.)lWYe81^2=L=\N_ucWL]N
%Z'AmBd>jsIMh%&&_.U?G@bb;Fpu<ju:-1h"Jbt0W2_5NlN9Y7,_f2=1@P%=El#q2f+bfT3<$\f<d/c"iS&Xupc.5(4R%Ffa#KgX[
%O-2:JR_$&F[06m+THk9GWfWIGFscX.de/--g-&U0BAAOU&"a@AODTZN@g^djZaqURjkqRJ_tE!Y:8fX1r0`iOU0p8Lr6'lHJdQNE
%0j2=<,C#q-2+#0##$`Kn4(u7S;j`GGcpF(B/\F&MZ)7&s!bqf_XE3gB.kCX8f!d4"Oo*J+mr$5t5cE^gE"M$BY/;*;mB-QF/8`?R
%Ab7O10[?P\;DA64DekiofqFY6e?S&i0&14c8\9EJBM.);_*L\2\JhU#@`-%;l&=/^ah.9p/r=f@?lujh&kfU_$._n9l.@_J,\!@U
%qDQO#Cq;Rb]PLG8m'Q>Kdn&oR/Fj_!3`9YEmLLOF<u$3$`_!Y.4uihn0Le]&haFbF(_k(hR$g^>OFoo`Watu71>[jAY9_Mia1V?Z
%\*)Hgb0XkVD>QS"+3Eqoq4U\%Z:4R/F#s*LZY$`YIAIaZRk>N%"bW6.;>%pHYD5a$>mR9a]?[.kqm,LSRnD_Af76kt'n<D1i-t&^
%\@Ch`SN]Xkno9#WGgS0B2'uX8E?rj^Zjn[SX3OIV^`P2mBVhW(PltsS@/a9_8:aje:ASFK_fNi;-$p3*L(*%$OcO&N'oO!>Gk8u[
%&6RVA<kD*0]_E1>m0n5TUoL<<-'s+I*6f_BL:8=-e[YcFd:Cid0Ns%d"1H:m/R>%Pfea740fH%8*@O$7Msl@3,n#Ae\Vchh7:p9]
%;l(2;DgHZ84M96O<%Xt>]Pk1L^lU>GR;;Z!HHHon8LOH*Q*00kPOXD&/p^a-^;7,CA&TMlG:QtLB[USF?.]C>eAL(pB>)8B"M<Ab
%gdL>sq+,*M6kQdJ[=9nj%9:;UV82LX-sYb[n,_XZ1aFLh0B#?j80(mq/dD`CEA6OB6")e2na8]PAFnK@d*F/dnIc"$+?&f)+$M[*
%rMPJ8')X]H,l+0>nAR-p^N.\DN]4G']'L#Y[oNWHmepc[82bFP@P8APM7,Vl1=\m=bhk4`-n^cnCuCn'T]foKZ/2]_7'hQMI#5Y!
%-ZUSa"94P&L\YO+RZH%!k>+B&_<0l;pkE>+1,KpS^D0r;Cf[:F:coBt\;>j,.'2__hb(*OdWs7EjDaDCLY:FFgKeNL1!WUf*<kH"
%*aV><Tn<^:bV-uma.mjL]JOO`5V,Xm&`H&7'(R6e`X2ORr;d'`1r9kAraqCpD9n:);_9c#[2c6iJlJ=/I7gU%9LHpMTE5%$Ca89/
%P\hS^I5TUQBO6F?li18t%:E@+M^6=HTP,*hlas-ZmRTLi>X"Krd>q"!4b:rl4gbJ+2^crcQI?Wr>Hb=l3m;_TLZ2>oj_SDC*-3ep
%'*L+MR7t"%JK*IJS(H0KbEJDZHG[Qt$(_4$<kp"53lZf@8^Gu0@#(RCq@-`9/`gG'e<)95lLi`DnKfqK*eu.TZ^Rs;-5GXb-K:9u
%dThan4Dj`S7=8M/Z_.3ih1]=fKbGu2bC0jEqKfD9!l9!o66IiM-A0o$XcC:SWj2^`nX8C%o@V$F+s+0!nH2?8e<4g^]FX^I0'+26
%hqMK#LmfC3%m`h'DDI9H@jEY=aZ*bh;7ScL8&KfT)3g35#`%TLog1JXCM(DA!]cTJ-Yr<(2@*llr?N,:mqQ2tRT1+QQF&]:A'eH(
%qWJg0ch]E"AM2Y1U#2pYC3S>dIV+eMaVD7V^be4gY[$7H0q+DP8]NP,Ll]q:KjR:9a,p6CfO):`,$RR%HR8'7P&V^OpX+UE>#J?V
%h's)Im7WZb?]qnV9+B@P7CiA?>(`-9FER%We]4a$0?0X?C+@ajTp`niSHaBE\`E82+Xo%&W,\QY,37&6<U[iU03=5k28[?Xo3h1-
%<<%"[YK_T!h#;&#7'FPe$g>&Wk[Wne3@1:Sb+bB2^,/[),9@p!7UrbZW_T`'eZ`;"IF3VU+V&;m<5d-:Kh+g"*m<*QT=P)N6Ck:C
%lSg_O1`AkGqMD+^;UYfaoYf4tboIPsWsblM!mIqtIq"t)-4R!&YA;KLP+&=Z*.;KWT8/=3N5tA03LR<\XdVoZf]Ui+BKfIc/*i_S
%KVn0<ZpYaU8[]?=Lfh(-U+6sIolbtSL4k_RA/mN)mCA'HZNI)d"GM&3T<,foCVO\'Ds=Wdg<915@?+D"R*Be-^*`$2G;8Io^9.i4
%M_catXPK85(\ZRD:3)a^@GSS@;nD\6b+WD'A@3E%X64\+/eifTUit7tCe7+uaf+AK9m;OmW>-!XTNajYjnNY]!=L>oG*'m/Y9hgu
%I=(n)^nc'Ldl(DMs#.#TM1Z24jN7@W#]p$>3Br<AJ>T>H.gXntYkk9lhARelgm/j:,A2AP!;2*26:t,cqKTZ.b^Io9Hl&g]lOKbo
%TK;7\6bmj3/G[Z?&US,qgJf"FfVb%qT"8fBl[504,)\hlK8Lb=!(jb!Teb:+&dM@]rEMte9d\o#Nug?>b/R.U'@$:6:7[Y&kY]+X
%P,<u!mu=L?62.t+D^aOtP?SZ8"F9u;8JUH/.e.b:]F8`I+F)]O:fYaemmcn]c_@1iX(RLM`hTjoUO0T5Kl12!E4mdcY[uC`cJ)?d
%LX_"B;gm(TZHC&*[uI;(BPTLsdt7i]N<f#*PC`WtpEU@%QXUD'<QENLL7TQ`TWgd//PCB(-odf,oUY8ep!g"cXW<6a^T*7S67+]\
%4])S9K796WQOL-=CYJIm)uGocNNF<P9)E2$f3Hg5]&R[VJW//rpfaB)dY^FM)t//I4Dqq/pTZGkkEMLm^(baiP4S:^\!Hil&Gr@!
%R(FZi't1rg?X7'mF3PkDSfR_`1CQWY9l3]9!n8a<#9cUa5Otq/YB=)?7At.1&6dg?.Vmqg?qSg8A-,bN'5bg;H"PO[l]DO3n#j?A
%r%F7N*j&sTA)jTj/Z(Yn:!\S,?<Ft#OKao^4KH"GjM"9L7`fA\:FNn/OS`FW]lM6,0N!K5YY1aIkb)IM->'&K^`2fmc^u[4bIk#]
%)o63bEN#hl6,F$?GKUq>EfR^Z4)jDnF^sN]`pSTeX8]u>)h%6m'dr[FZ(+aE!-GdpQE'-#3!=*/^LfKnUfsj:P3.`;#aT#KA>#h`
%-3PO,5#A5.=Rl&e9)Ksna!V"$Hs#/4G@0aS`Vo_siGTcf`4MEl*JV<lSV]6W_jcmaNuasm2=V?LDs28nGWV#ckm[Xfl\K5T1UrWY
%2/MnY-SO[3TMHj(s(GblYF`a%cWq=:GTZAfWB<+3qWa@#476UnZ(L:/#e,*u*Y-YkG*E#$(1H(/q"e^rJ@brFD_D")aJu0"(4Tuo
%B,Rq\Q-4,*H%%7/pG+/3gWAf]?Z!t=q/gmGZV0,_YQGE_>Qbq5kFR%Uh)E'[)dGa@D\jkI/(fp^_pUIp9TIVM#i1%pOLm^nCr]GP
%nMcFcjtAif9jhmdm%Ioq],BQ5_V7d=o'+bn$\+@s^'XWHr7D*Re8L#u0JE;tGBbc+QIQR6qZ&C\$br%Soqn^S^M+9QN'^EMFN-Qf
%.)j?>D`Ytq[<PUSHUlWj2+uf8.HKn@hPE]Smn[ZTARao%FYnCt#/2%@'6L];3ZhdK#3PPqi-K`6$Ee[!9u%!X4:4fc2kACMkMZ!2
%=>2iYk%<+-&rL=>>%MVYrLj$A_VJ0%;P[%p^du<.>S@e<Q%I88a)fS(o_m7D?Pui3:O8L>,QHitHG_k[8GdQ%l/M>DBLIKL5Ni`,
%W99Y7IEt2U7E;=fiHI@N]qh@mlOHSFAted;Y"sa9TuMlN94L#_0;j="Yo)"5pHlEc(5MKqlFGC$:K^W>o?517'gL[657Oe(!q^nc
%cuX;Rd(U.-SA+?-parK#I^^CP:AaEXh>b1;^0:/C++l4jEqs?T/,ZgcBqL$5bKtFfh?lPk]XZiM+$7Sl]kuk(4o[hE0D@AP'R78B
%:`L/Ljm)=5C&Q(`/o_g/48Ra'o(IA%T5XZ+2qEIVeP/4%)l8APMiN7HZu<>L+)0s'qDA/1@shi(r(0e3[/[>eGf>SrbN%'#1q[@S
%s.2FN2>3*d=_F2Io'nI#T-ZrRr=9(OKDj@ArkpeDrGI4rJP)X)N%Sbda,W"VbWP_)q/bK,%em$[97H'A5CD"Grs-]p)<h*.:ZO0-
%^,?,(Y&06$h\(qG-MpHFc!bRc]5:aNH-_g!bCR*m)QHHA%hc35)@:]]5PI`QVe3<lhktSdc[8>Ds7k94bM$Qhs8L7YF5Nc!SKIbd
%/nc2sem\OWK+t8r/-!45p:cC-]8=;URe>\tXJ+M\nu$a-:eH2?#44#WK#>bGIu8J0>],#>leiB*c;X(`<W2p*5;l#8Uf9R.0n\V;
%2^Vofe\+<F%3&W?:s_ClDg3L"rT87[19"MKaAM]D9,SW@&$,Dj%egF%Y:?tuKA<YRak"djd9!T>\jGnmc>Yaj:te]tq5S_h%I'@1
%X\gFo*;arjbl;8W2Hkio5*f>8^4qMO4)HN6lJ*G/c+OXIhdV'bm_@JPrIJ/j%lZ.GjaXeCSq__ZYM6"BQ[->n5IdfL7?+!Ch8]W3
%GjkE-_-`8`4Ipus#lN;!@-#9CqkAKfUqGC95</YYn)Vt;`G1moUYWmAqmYPMa3="4P6IH3l01C`oQ`_4]at:Sm`0L7a+qBa(\AK4
%)*mu1@Z8.r(!,)jggg<U0Bu:/Q\9k9?U9"5B^q)E3&ok.1ctR"?adsg\@aBh,\)>ImdTciaRo"d1=VX4ElX-qlH;![@/Q(XFaps\
%"VH1+VNt-;HuF4#rgSgUBsa.&Z4GsET'AUkrq[H,-DNfM^OJ=_)N>\q)?-NUG'l9@^6-o@K@Ym6Ykn_'%u%S=1#rEt/YG=s1iVj>
%kU[05.tITs#$EDLf'\]>PJ?$bq6,96-fbk!lHLob*a7Kt\#g,edN0G="m5CtQ=Uqa801']G_!R1aTIPkHUNmihP+s2bhG,ril<W*
%*pAri2b&lk,^IF^gI0%K8N[QI-kF3O,/-bc4:HkHi'<A,p5P2*m=U(0On,`La=#mujr!N5!-usZMEIPgE57>T5\L\K8g6N&?juFp
%RhO`\W\9Z:nc1m>irfn3;\U:57)8mjdn$/U$^:Xe80g6SDZm?[-dVjHX#,[[DQ3a>'[=@?;CBPbN0"LUJ!C0mPcZ0l<(DJY;h/IZ
%&=AKnZ1*6&L.2mWLLJ[O+M\#"Wre4Y"!ioVDF@#XrC0BP2pt'U^l3baK0j5]X-2JhDW<GLVE?]Wo:.Ec6&PJFs)M&NLCm*g;ie``
%BVbBTq\;R3@?`^Qo+XG(n;\A)NM9hT6Onkc-\1Jii$'h+80m+c*.na:R&h^kI;t)9J6JVDF#(B.=JUII$3LpF!t%;PV@ep[n,cEo
%5QZtX5tI0(Y](:P;btJ:ZRj/e]FIX=oC4r&+VZAWM86#7&T!.D%*8gE%l,2_&]%7f?8F=+J1lHWh&m+tMP::e?sH5C'T*:b9AmGq
%aknY0>Rp6l#rI^N-AXpEk<+>]+UI8)_#H9!![*O.%VR&Ee;YS0hr-POH3$-urW+6@X<8&74eQUJ5QO!56*^5_V,;%d`t_`Mhuk*h
%"@SG%L-Z&$O(&d73Q6nq5Y;H;%7uGmMnoW4`=$UmBMbAYE^;[$Tg+s<8D:=Q2:ir!%O474i$D3m"S.?[o\2H(e,cl]#Z8l35tHX8
%KYeQl(g9Qk1aQfHOTsLC.RXe;dMa]n#76Gr"Nt0Y-q1o_8j^N%#UR:&=NbV>Mc9n+MP;Ed$1=Zj8/DsJ:A"VN<I]c)?Vh9S$0d*$
%6l)[>*n*E>"d-WH"LQA-fg0^pl%Y=^:bZ2_BnkZaL_;I+%:RCU(rDHsc.!;[!_,OD0Z`Ek&'Sh*Z5e%+p(dfWrIgg__8BtfdU=<f
%Q7*1MGTgDug(\R!TM>!?-7Iq^RUS+.Qk)rj1o[07>3%Dk7uP#tcitjQS<f"G80%sFL<@jgd$M7hDAJ<XoT5CRIKuTF,>fl<;rEE4
%1)T7a,ud5s<0kR:L?N'BgCQ@^&PHOTa"[XU:6sosLreT^#+5XZ+XA,=^i8e&O=_V;Bhecq!u5i1C+tRks#tb3(-pB=W];RHOMTI>
%ibnRLK&h\,$Gckd5DTG:+Vto$La(0[6cSs>&O5g"6Wsa)`l>><eA);lA-RSROJd&lj!5&l4VB8.d[K;baK\=tf]'dWP"%_Bd2D'Q
%7+I">9"0JuJ2/AWWK2n#ei:D)>8.`1-Zb?uX`mrTkHm57Tq_HQ6WJY_;up1GCqTD/ffg`JIGQ5frPVKg<((!p,O!Q&ng'Ac9)`5`
%>99n0Rk8Rb)I3IN!pU2=/0qO'*f/%W^`[=[B1YKG!qm5BT`G4Tb+PU6Hl`nR%OG;1:4u+r8uO:k"W.U.'V<:(/Khg*.;3T9%7NO!
%(=E6nYmZa3SC9k4oe[)(906O.#06-6EgIL)i;nW%UIL8PTQajACBmFfU`l-\2"XOp+Q3HXNJe[+EZ`C)F]/\TL7h?e7sTlFj8qmm
%&s5Ff#VN;X1dW<X$+uQKTbtI+Pm''S:q-.$VuMhS-lr^MY(.5,K0r!FL"ff9g*io@O=!3";)HQ_TQ^b=46hss#PT_W"\g]A:B>SP
%TT,9j$<Q=n-hVskRi.<Fk#%11OYBNKWn-kRAbFDg5nG59&98a'U0`LVnq>GQBGh*k6qPm5%N1a'I2MNTM55+Sd3tcbg&nYf<WH/W
%AH,l@:b[&`.8PcF!M2RU<mQl"E0A0"C.+cQJPf7K$kf1t4<gaNI3]UC3uS2=Q=@(o&nDjg+]5R<UO%I6!%UA=CAA`9,DW,u#RIki
%kTE/Xge[tRo(a5&"/K5j!C3ZY^g0Lp"Wp8lqSN)'!"gM_##$M&>)e7RdqTNu[rJ4"JK/cj3#6MU_dsb*(4u%SSg"I8W+HA@JfclI
%D@WOT&u_+)UjH)$TVQ^lQf,U\6'9N;0NbR6*"JEeP_^HiK(UG><(WNud#A%$7#KTH+tD%AJTNPY&6*'mKLEa)1g+?GNYH/.TJ\P4
%VInal:n8JgANr00MmM\!0Yg4#[0VjZ"Gt0jN9ngrL^IZE8=:H@]plJRb6j1<U3EUb&@*;#BR-#/n8&n7M^'*Me`M:Q-5QGCc2Z0Z
%7moU)EC-f.!A:&Y-@Lak8Ja@I#a7HkSFfAT0/moZ!*gHaB]?^h7?\F]9h<G?.FHjKSYpD:)Z5HB?o1NM'(W7gXgOts=5NHI6Mh3L
%9Z!%pigR`PR`7.0aDrIVh5^^uh.m+jV0F[LRM.tPJ8]+LnS?Wnc_r#ORgC0-d)Y,p&i):tVV@_:s"):%)#nMeUTa<.b9=&u]P2DL
%G<(B3@!MYO\>92]LkW;K3^Ijo<HhZ:5T1EZ]7sJ%XY_Q4^G?XoSWYo_4GcQ!;!DS&*b->@Ji4(7cl<7$(he)3@_otBGn!(blerC`
%O\Z:jenX/!Nn/IE<#[lG)C3?$$r(L\85TpWT@Kp/$`kYfg$EY%`aAbg.^p_3fPZ9WeD0J1J7^T`.&)#+/iRIsD13Q*fCTN%ggkYt
%U2O\WTl3e7p8@00H*RAg6-r>e)pIGH+o^*.rW`O&5EG~>
%AI9_PrivateDataEnd
