%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Right Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 8 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
28.7778 48.002 mo
28.0898 51 26.0898 54 23.0898 53 cv
16.0898 48 14.0898 39 16.0781 30.5967 cv
16.249 29.8369 16.4585 29.082 16.6982 28.3496 cv
17.4175 26.1514 18.4136 24.1514 19.4746 22.8555 cv
21.5918 20.2725 26.041 17.6709 31.2129 17.6709 cv
32.3491 17.6709 33.4697 17.8027 34.543 18.0625 cv
39.8721 19.3545 42.2012 22.166 43.7422 24.0273 cv
44.1309 24.4932 li
44.5938 25.041 45.1309 25.7178 45.6699 26.4844 cv
48.8271 31.1777 48.1289 33.5488 45.7715 35.1143 cv
44.9854 35.6357 44.0146 36.0684 42.9424 36.4678 cv
37.0898 38 31.0898 41 28.7778 48.002 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.425849 .581689 .653254 .557549 cmyk
f
31.2129 22.6709 mo
27.4551 22.6709 24.4468 24.6768 23.3418 26.0254 cv
22.8418 26.6357 22.0811 27.9775 21.4502 29.9043 cv
21.2563 30.498 21.0898 31.1016 20.9561 31.6953 cv
19.8032 36.5674 20.1074 42.7676 23.8711 47.0205 cv
23.9043 46.8828 li
23.9395 46.7314 23.981 46.5811 24.0303 46.4326 cv
26.4492 39.1074 32.2979 34.1514 41.4141 31.6992 cv
41.8936 31.5156 42.29 31.3428 42.6006 31.1826 cv
42.4326 30.7822 42.1211 30.1719 41.5498 29.3174 cv
41.1709 28.7813 40.7539 28.2441 40.3115 27.7197 cv
39.9033 27.2305 li
38.5742 25.625 37.082 23.8232 33.3652 22.9219 cv
32.6777 22.7559 31.9531 22.6709 31.2129 22.6709 cv
cp
24.2368 58.1934 mo
23.3296 58.1934 22.4111 58.0439 21.5088 57.7432 cv
21.0356 57.5859 20.5894 57.3584 20.1836 57.0684 cv
12.0015 51.2246 8.56396 40.6396 11.2124 29.4453 cv
11.4028 28.5986 11.6538 27.6895 11.9458 26.7959 cv
12.873 23.9629 14.1724 21.4385 15.606 19.6875 cv
18.2603 16.4502 23.9385 12.6709 31.2129 12.6709 cv
32.7446 12.6709 34.2607 12.8496 35.7188 13.2031 cv
42.666 14.8867 45.8711 18.7588 47.5938 20.8389 cv
47.9697 21.29 li
48.5947 22.0283 49.2031 22.8164 49.7598 23.6084 cv
49.8184 23.6934 li
52.2871 27.3633 53.2061 30.5635 52.627 33.4766 cv
52.3047 35.0986 51.333 37.4229 48.5371 39.2793 cv
47.502 39.9668 46.2773 40.5615 44.6816 41.1543 cv
44.5264 41.2119 44.3691 41.2617 44.209 41.3037 cv
38.2764 42.8574 34.9902 45.3555 33.5879 49.3857 cv
32.7192 52.9023 30.7896 55.7168 28.2661 57.1348 cv
27.0146 57.8379 25.6387 58.1934 24.2368 58.1934 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Right Hand.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l4REA.)O!IfgD!sD.9ON;h^Sd)-qrU_/=5dEKbCdXk@.@E(1JX9FVc)72d1XAtTbsf'PZ]a.GN]NFQ:G.d7r$(]Yh`JRL
%md2s:hZ!NLmf37EN=YF=8Sme6pAf+uHMluRhtpcaIe!!`q_(%GHh#p]QZ#+dr;F5D0>IJZo^;CapYYu>k4Ij"Df:*qr:f^&o:+XM
%I/WotT5K[mT>1.[ps.j=q7!i:?GF.$p\rG/%l`UL,@^R&IJHt%_RH0>Hr'D2gE402dP"sVp])ms](,@[SUU.!s"iOGaA6*_hA:R'
%>q[)PGaDq-2t#i*V]Y`(TDU+or8pNBk5YA#r;c0Y^Yk!<CM=O?PhLPEpa25kYnm-%9>dr6r,/M'jauB-j6Ntd:%;!J*L@GPl(p@p
%p#ObgcSsb]`K.Y>2h1_"ghr-:nnEJ/pY6G4&U^E%!3(6g'1-K&^R?0jeOKl%mGkW(VNq<i),%.lpa'!>EL":<nOpj=QU2NJ$oo$7
%1</5l5%023k)TD)6`g4A;Xh;t@q1/#0>j0p;`ME'?5@"%bOEo?;eN]*)r2YBQQfJcC5N%abrM.TJ\OgZ9*)bqlM,8\5%Ub4V1>Zu
%]t5?HI[TU/G<KDZ:>^Xm_BmPBV2(ucB5/FC!?,D`QfO@.TlDZ+I$8sL0J;!:d#lhjL96(=QhO&p[tG9V)pAV9;fW:'i&TBt+2M#?
%[^"aEkOHgbIJ)5?[?Xd3b_klY?2UXu"Ln:-5Q'O&mpCM?iVi?YYMQtk-i2uaa+]kt044_;3I_#:!7i9Wn&'o>kEir1rnZlN]M<@a
%<Uf_"%rV1)5PkO\^85a6LY`11\s)-a1a?[Zipkp9D#4_"LRWOPpc^qBq&/Y;"*%8p%?+ei]</h+.f)=dP#hPYGCOl/r;FBY]?apT
%P.uA>jq"YfIsB(OW'HumVn'Tmo,i95YSOh>M%:JMrT6['$'s[I9(Za+n:t71\C\ng^3]UQ+5Zq!Qd<Elq-KH<iVeO=^A@k[)3k@)
%K_UJo?GCmfX1,8r5^U&1c6N"GGMg&\%F#i!^4#sMpsmk'oNSKn]BAX4s7-82flsX7_h%[C9lp0a:VV8Jm.m8\+tN>,PFqg4jkIOs
%p\31]hVjl2k^8$u##cGkCUo#hK>WPTJB*ms+iCDS@O5-1<aKbKIh(i<BCB4+rmfaF54PW-4NY,>li#T4YJ(mE[t^Bl^Ne#2l)3j.
%1`%54_hXRs[J`;ep@OQ*LV'HKq'Fnpo(JR%]/M\Rhr:7u=:&nsYJ5P\Hhhd]6(^FXhu,h^L'YQmkr\[WX1)4$j$u>qce<;erq\_O
%n8I4!qsFtZIf$a$NurfMnBJRpIX1POe_?ELr8A*5qLb:+s't]h+(L?/;p.DFio)9#&'u!f'VK%4+*]&3!8mVB2_Uea:$h-9=+C"]
%4a6ne^V0R7ia4n8hH\#Ih>QeL^A.3c4j<&,DLV<*rsF8&]u,4>cTcY-Uh.&SK$SMRcYrT2<943H^6nEY%.pV1Kt?Z#%r!$-Q1m<h
%n6Plhs*r,aJJgdEN!fP=]"m^LG#^KTQ9@E&P)"SfMtVP0iOUX[s7l0X]0G6P\_;fqi9.;9^O?*1)UqQts8RU&6g*%1SI$tPGQa1)
%))i)Rs+!1@6XG6:on1s,%,_XM9!A6(=V1=60R`Sd><W#U0-L8-I/D0tTAM`Ene\9,nGfTA@g>,K-LmWJd]h3lf<8Q[rr)FQr6),W
%2ok[MIJYZVJ+rBelS$W+0TD6U<1F_.P(fbDj)l&IR.67(`Z1rLFE;f.\_,U@0qU\Fq<)t*?[_f`Yai@r@.Y.IXQ1S-+]cC>buMSa
%@KJJ%#OpLfoUUp_pm])nW7Jac?sb1QAVW)+q>1]+?G(a_b<LLRiT7u.)X]-Ehu;soc`/WXBH[ii=*/E:k__G+#'P?2cps)<J"]B;
%Dgg;M,[3tP1,/m?PUcV-(tY,O$^t5aL[[>LVKG[a=)JC`3rD7S>a)=6H")mBW\3\S%gfYe/cR=Z;jQ[mjdT<odp1QWn%@eLIsMTV
%gTsu]I*\sK"9#i5Da=Q;q;lh(5.kDLbqH[Rl)l[F_&PC.RuD/?2;2Q$MLRWiOFIZ/odi<lH1L_BBYJCtq>,$e5QBj\M!%!UVGrXN
%.SSb-XJNe961K[/iB9#OIY\1Q"'Y^h<bOfop]D=Da'ZcC5RI-t1\e9KO\0rtk4,3rGl6cT_IIg@_9.!K.9T8oP+fc#Prb2M"_u!U
%%oeQc%e0\::K5X1>K[2m/JS&P*#/u15iE["lg^a7TbJpQ]>@?rh[N2UEhK$l3^Phm3CC>B\P9Qdi=D074o>G-M"(0O4\VTBPkA%L
%<;:1s]0Y[\%e+OmhtV8Ss,U,SUHjfOq#>0l69hsT;I$qiT8W\Tr<8h:(&Z0*rXB$q62kDB:YsL.W;a.$^9r04f8KU@.+H9.o]^br
%`G3,phq/^jD>Cq\Y.-._qMWqg>so&<G+u%?60[eCjlA&^f%kWWMSFp\T7$1^=5TYjr6_.0SGd;D<+)lUP"97"KMk/F*QfNF_^Sj-
%3S]?n=ZoX58YO\]Tc@b.j#kAS>FO:Yk&[2`SS9]XR`.71cST\r1g`Dje0qZ0d#@>`F.eqFjuGgSKQAHTEm+\oX`Prr.*$?XkAjdE
%.m.,IE2uYsV)Ba8*p]eslRd2f4;9@#g^8GU>&T2:O4p9JgSnK4D0OgR,:?S%V75=YTqD1l?OlM^%fqtaKlT-mTpuY&3`g4NY7$U/
%Q,Du$8r%GsD,:QSGbA8GZIl!-7.c0hQ.#nKn)\6E"Qa;]=:S/nTC8':j`7oFoh]$MTT/2:-G#F6"^BKQFmFYH51>_^]QTnC1LL/.
%C:nBZ$YM`>/Us%5jl%k2-!6"%@.qukLKbFur$hmG'UAi@/*'cE-l:oIr!LBQ13'KagoEYgjL]B8ZbKlSB_;nU^R03gk<e$J%Mrf7
%QeahCf/guj%3,a8Q)!.P\Wu`c"7<A/Vh@\hgGpY<7!o;d;DOhsr!6=XE23k)-uu*hgaIdtKDar+r+Np/O=0"flWg-KcD=`Z<iVB.
%Kf?FcK`mE/TYat;,E#iS^88^5#%&`-7k2=*cCIR[;Q7HAaL6uC"q!/;$?Z7,7lXo#k_SV6<VSUA3D]_\H335TJd4I?P9pkC"W-NM
%(`M1]:,)1YClk2NIRFX]$LibN[R1'"I=t?W6H>6SK&TSE_*,X\^o9[P$bH`fiC"Y(?bjH!5<8ds#JQ0:"c>/c5"4Dl!I20\cL-5]
%@FsLK@rm\O^P8rC@I(]\#/,%RE2*d]Sf2bYR'sdmE?MKd:=qhA%=ElLO;p>9U+!OFO;Mr[D2su9%b_f4"!._,P[6Y=KNV/\[jj4-
%&rE$-p+f/)qLX>"h1m"iTO"b[`MIDETSjL*Af_lU>iFthlPgRT%.?WQp*(2E!u9HGTLRe)nAY:Qa`a:oo8i(*c8V>/4ND\il-+`^
%"`pN`S16:`GV6fpOu[Whfu#U9B+<0)SM*$*@"l]bd5drmA'>CQ(Lp3^RL>DC_O(Qj#G=PT"%O:07cVg)0-`,[NoPPNn`I+%1tFe7
%NlLif@sirlV%KTP`K<kMZ;*m^,U;#V6jp5c"M?"Q1eQ1Fb1]@KbSs.?)._JqniTP?+Rqh^UGR*=c(L3=OF`1<3XGZ-OiDAf^#\[>
%H]Z"G5`OAZ$QiM^c[Zqo]d5h?A1g\g^9a&N93i=3Z#31GVt@S.e;/J1?0-<D3NW\=3<+E@UKj+=k"<5\hJ\JD8UXokqpC+'@@*cS
%!kNRr?AdUOhqiA7"scGMAcD]!>J//Xi!K-7&]aupI=aH>e/`k$1XtOKApe>n)g$hA;Jq$P7,F]Y&iGFA`=4*WbM#JM\RtF=:!Wo9
%P7+@KX>&3s=hKj%#TnnjPrEI;R3[es'Xo,J&d&L\<Jm2>\sQ.?5b]S;DZX$!o1'P5#8oX37]O;bQ44mIk[<udMIreNi]b[8MVC]b
%bHti(g-e50K_5[%0<sS)9&]jg2@<t9)OX,53rka7[gT0h;3XCH#qqr)3S6HWZ;b<5=bO:?rE`CaP3)9\@`%+`e<m0Lr&`'i+Bo84
%'B?Qs+"FMfnFiVD"q)=sg5$4aEcfut3XMR@2CJF%Z;'ji`M,m8766?!4l0i:]=dpOLlbh/n]#ot\f]dqp3md/;!?HDgPDUpq;FXQ
%SVX%P&k5BJ-Bo,jHi-E&;1,V[g.VXO,YUT<5@I0j-t,G1-S_rjO=A$m.f(8US>dtP+BX#!0p4pq&g%W`8-1&P>(eor8R/b_nZ\//
%TsQ**b<dA4QjSLPKE^^8'GZdfJJSk7:1WJeojY=omqoYI7I'"n'JA65GXK!.U.+Ag'W\<-etcd2RX16r2*Y,(Y'2`1WH*,\`Ce_T
%C^<opOKE-KK6]$r=ij*+PA6UMb,U2/2hU1ZINKB@eTf3niYo$ZXuBm\G9kZZ7@^J(aKcB#!\D[:K8?Wu7+NL?.aP>o$*#,*S!_>b
%l@foiSlmV7WbBP,WJ[N%PR`gT"O`Pp3eYnW.(U*\f4*r8ei^'O=L7f5&bKB1eG,Ra4tI'4r5Nn"6j[<[W!S*EGoh%:,W>9["ni:0
%$cKl!9hf(OqJ9F'-n<\3pkj*q.QiL^%riiQlqp`]+R@+TS0h8jest'\>b+:!3g*'seZ:AtLT3Y6YD$=d$>i]-+"1YU/S(VaH'.K2
%@W!Nf2"r#Y;-AVoY))%g(s[ml\Jr*s=k2%VG#,''Fo@S8JR&XL>mms=o"M+clg.Y[QNB/^CnET:JuhdRUOLp<UT>qf.2;%GZ#7OC
%5\kPEg9V[UDhi%np?=hhFenss8M:lWEu3j"rP2-PP<9,Cp]_e,d&h"X;#JhC2TX7IT_K)r/'+Q\-Q`UQ$<%BQi*-8#b`/0_qH:4C
%J[V>is+,3-rj$Q]7^l`njk@<]^PAh>VSnUm4B0&&)L%Oj[T(R*WdI*l6hS%@6G#!kl%/P@r>fRTk*!h?b)RK#O(*\56;ASFoGhNd
%kAh35%nlM!a@dWo\<l*EpA5sZeJLR*,Bf6h3E2BBP2q[e!1R.&+:fGX+uk_&8BWTR?^^5B!srNB5NrDg^,L:CgMjj$3EOPdCq>TB
%1ijes!uBjPqk<O));e(t&BSJ8c/M^);O%3\A+_67-^IN#`\SK7c=0kg?1GhK&`^eEj2;sNpF?s9KS^*sa>')uB&fB'3L,(8N.W0>
%Z8l)cLTENuc]b]a\Ee+=r[2YLG^G40N2O*&F+P<H`*&2?SXITr=f3!:D4U4s7p9IOq]F_?-B259?*]p=g.AT!fWC6HjYZbsZ>R7U
%(tF)]ZrTFEZPB8$"P6,=eY+LhdHYDR4q?',TJ\I+#[If=f"kGt\e9bKXEl\'4CdNSk010'-5t*5S`u\M)$c;6O.njG$o$Va"ZuGL
%0cLuE_"6nL>lCBuX)=JAT4T6?>p)XXbFV7G\KuZ.*?hM*=#,,Hd7@&s9>l[!b;sb\8[=PTp.QCT/NIR@\8'8o$PN%C*O_c;#@\Yj
%7(EL7p48aBQf5AIq#R>I;<kGPNRbP\OK(c1:$!Sie_hu*BasJ34g+_%ldT,-(?@!6\4I<.;TVJR,Ic0p81,$1CmN!D[,Nj6)=STA
%fO.T[1s%;4UJ'Z'Oc!:2*mXPUBW<GBXtMmBCsuoBI3A'G[9U-tT^;bmL"[`^b`;its(@cD(Ue:USfi1e[YR>?05S2@U1ZE.nq2+:
%Y("r!/nEES]q!n<W9DZ7in9Q12PGHE3kl/8(c[X1L3)[hUIDuAU5gq7R=5LD-8O3Bk<q+dAt<bR!>]Ve(1'7sBk29E*091)30R/<
%(7:JE/X3@O#mREFib4utU']fb>3G!h$Ok;_'1as5"Ve3ORd(3deluO*!@AJ!!9bn3e.//7?9k.h"9POa!il:^h9pep)Xhm'm(,*X
%\hA6g:ieD9%YV5pad/IGf^V@YjCY>(?1pG6]+Qi_,5YXj>L/BD=["rDEoAPWc;n;\!J.\X<ZnjJRRVWE,'5Q/d+0[g+T6?f%SA&m
%X[,7j[TeOu1&iDWA,AtE>@f+0@f)*Eh@hWIamK(81s"e(En1:LSu6U4Q/emg&=gH_9afHQXV<;=b1K7ZPV?@S<+M/*@t@_FlYQ"G
%dfOQ3=@!L1nA,Mr&E!T=(ktEQ:>!D!?jFqa#K+ujTECRl$9Pa&FO2*:3WPa%Y2hYf#6SoV2h6OefS.;T%/W@u^^\pg*-]1&1Xr'8
%e0$F"c*0FKhPDn.FZLTkOF]Ha`TAOK5ILBfj\O^YAG_"e&Bt,4/:\*e[&o&TCu$W-)8&2#WBcGEB=3:'5&+[sNVCZa`OnAfk4Nb,
%bIj^#F.V'#5`K-qlK+OB!bl=;_p"[&1cD*foD.*VhTY'$O+mmLK$Q"2nX.9&Mce3^#r2UlJXNR#RI0p;fh2FE5ag`h*A]XQF]rDF
%&hL;A9?`^edq6THZ/3oF"^1F@QJ/1SHI$3:Jk[d='*anUS`[bmM:4gua#hFP=eJ7G<MQq#e>\\3$nV*K-qIC7=V7HcWCMqB[O?:`
%T5D%+Hr8auIP;D#JO<H??58T,dkm7&Ji.f4J76]m;8?:h7W>FY5-u2h2'?&E-eGT(I:*iWm1VH0(c6$M24&]3beYPg#]H:>R#/U%
%BE$T'a)DhS9BYAE5dR>5$+lnTc1UECo5i.&!UCW=Sp?@E/7i%PKG;L$,q5lla?h4H66Jpj,AY2bX:GQA8"a^nJ/j>&Mh!]MZ"ffJ
%g2S5S80tkRg.BZ^k`DVnm-t%=T?<`uU&&"hk*6lL3VCP>^:@i:CV^R'Z&9&+4GjSB/SqKt47+D%cdH=9ZFR3qNb-=rpbn/0Mg+F7
%-<@o+]uq]n0/GiN_EJ]r>1oZ"Tc=jL]V!.iaT3No34d`qnOK]@e\Xlbai92)4_4MJ2I6E\BXS@I;*4'"jD=J!WjVLBp=CA(O[bY7
%9\.oA#(n20W@0MagNJ@SfBK_8;ZBnPL>%Pg/*I,H`V8^8DPB.Y]ids-@td]Z7at/:6<X]FLstX4.SC>96Bd(RD9BlT%bf@i?D,ga
%Zurc)k9o`u=hOc/]`6AAs2^gW-cDX^'7/\+-hGg8rWbMp0k?(-J7k#!Ng"[n$GR16/R)&<EMAIL>-iK&)h/"U<@*[JOq
%dCNiV>nXa^Y;*CIH!+'u':m4!fSptQ#uukg>#:^;FY\3jKi\K&nrn_u*>[t8Y1>r[COfI%7Fo7W4:=Y20[%)1JOWnHN\qq*dem`B
%L0r2N(?JZU--[7gjI#Zso4cc7Re'dibk<<'SZU#U4>UK=Y@]e1jO8LKmF4c>dFs)W%hlS`:3[ipEUmb!XC!@a"9m1/8bE;p=^bIG
%MYs#>T/9^JEXP)J$=UKl%u-]a(s<=+5+7G4dP_%oAt=L4-5^3WYhb>jGWs%51We<__$I$I4mS5ffN;mO4"tiVdAmCf3osM?n?^6r
%VhG7t,YQoilu=So*QUDL"_9<QhYKj%>=rfXYtafC>&qN)BEs2.1o*+L/$Uoi8T[=s)t1!>d;sbf3!N::_+J(![O?8ub,Ugb$>O+0
%k(:/ml&HCc"_uhd*lFlk6`tMt@A2V1P+OPs($ckkisE2K_R_J^5`iH1d_cT+%.HiRi`(Z%-+K/[*6^!;_a'X,M$/D-nL1\IA(_=d
%A2.CM,WM(J[Sg@r*M.KL_;?lVT*#Tb),KSXheKT@l[Gs_Zlbn&`ND>j`WRjDIJC&*7Ku>HZLjs=552GTqs=EtYJ\=$!_`QFYQ&]&
%kahQpQA[W-M^Rp\(=Ce?QNXKp"f9<a8aKe]%pTYKC*ne6C;?Wu9:jfM-Q\N*Io=@3_Vp[`nsl5o-g,.UL'+MqnXO.<""RLp3pDJk
%@]O.i,;B\UM<BP?-cp``Q$?7U/p7p>MYU)9:DtU?Ua9[EV%hq09fP%PoHSAO1\(GPD1R?cQ/)I0O%A+CosYXg>oIup3dG'B@f,-?
%$"(2\d1/VR#Sn]PM-e,G[O:V5LS`(`=N,()bhdZ569[nakF$MIMcajSnk]Gu.D^nqJL;8/Q*Lf*($kF,Q!7GY[UGoRZEXt(*V"MQ
%\_ERe-p#O!O2AdgVFtiZ,1Oi/5_8.ami4NF[TGtbBba'@7FbGd;-+@4,Jl=<T-7FkE>)\;YG98]lU\^Nk(=pQ./>"+>,%:H<\(.B
%TJE^IZ`PNdIRmU9[!*<j;15Mo=\noF.Yr:h\Z0=KJ],u23mtDn<gTpAnmTi0.uHt^Y>uOb8b!8^Bsl4OD61:S)L?70kltaM-X;/\
%T86D`Nqb`%Z7'iq7Q;_W[Cu$QPf#kHq.<,^XBYJjP'QVQ0<^9en$qrM>mA:VZmPJ071#>%\?7;B<*LNXNadDr)l?8*XGgKU.hldQ
%7Bpb#>c3-_*@TK`9B!C-8Ce[-[7%@(&ZskC01Up@3EaL`o,;\T_K-1!`>W`)Bf*b/<>N[--&-3f\dhZl@^lQ!0d4*QGqfWsm9DR-
%7)(3;$`-K%$XE)1g]_]>gB'8#:s16$()B>5q9$KUbkXk-?V:/%n*lCpMo1'J%4oD@Bn[#QAq3=k/-u4uAkGc8DX%R1Hf$c*;TK2i
%[XO(k[EtMRPUM^_0,MD^h*eKhfCS<C7umX6"&IMY2[YJgqoEMqP4oG3Z;gia/R60)^MNK_)?RFn6/&JS;>Sht\'NsEr9!/96FsuY
%bBq`=7\`QGpB%$>";X!/#]bUgAI7VKN!UELZOGiG/S>RY0fV\1d%7NE,E<>LAIr2[7a3X*AK[W-0%u1oKif\+6==NKQnVVQKJ!hF
%+p_>B6HINU(^Lh3!YgCO7H1O"-D?lJCRJ@&!'5\dlq#dN,i#%p"6'`F1"c-BYUU2cN[13h:kF9)$QkYPN$AWC[j3f7OAKKBmf]`O
%9Ignl]tK6UQu>CsCDNbkPUsGoC`"U5?C?d*j'A)0c"7gh+)81W@X=H%0Z.H\B2'4<i7WN+:-::U?RC#M\VN\AS8:^TfUF>Th)q0]
%E5<ICT)eUY;]k50<`.p[oMi.3\gN86&P2De+)udG@0&3S#g-^<;\,(cDKa)2_qp7*]5$E%Q\C8r0icNp2A?]Ffa^E_A96k#9Tpm#
%U?K`#MW3hi_&)Mr65-VHKq<i_&T9*4Oe)4k>RVXR-gGDfS)P%fUkteH%PLWb$99)rKGq.I$gg=Fc9.8IO%(]s7P0kTd;?\89PFi>
%E[+NiIOj>]c!/_kVX,RIibY*D>Xb/rp^1Ke+p/X;Lhu!9*eIuUD^I'+KUp%VPTeGSF.oSM%]2eb,"FuhaW*:.$q=[_pp"TP\J?A,
%-huLY9N#oZW!H/XG;,&W@?QB\=%.[d*?mL'.08G,2irNl<S(AnQ/"1Xb/BpI\2)_<_laet\Y)%:M%fG*:1/4CGVUDI"7\oKI20dn
%%,MtR7ridO8MF!iBJA$AS,Z4bZfb2Q9mLG]XRl=D\*_8(lbQ5sOY7E38IB-pg`^#M,a/5COsT24KOd8q[)`XGaW[&Sgk%/jO37g7
%JIAQp!p!`nJ-F043!V^o@?;]JEXmnq_BPo)a=0@eg:i4NS/9>qfIgf*C'u=a3'kM[<<3kKR9qb_@$JcKBPO1^[VCKU&,Y#_gRl=[
%k6^36q`#'9kQ)E#[;@B<Qsf@7]RhFX3+8%*5RIW%n\Y[&ClMS@X<I0E7K9SinV;/"%+1N\Z\4+YW>t%Ce</E>M+R+f.?N%u$[3F:
%*Cd3S@BM\JlE!VK#U!-b>oI.HB##@2!`($d@24`_/.eMD@=/pbd&`#LTZ\fU-(:6lc$O3`)^[a)nkE^[2=ppA,u^ESqc`HX?roH>
%`77*kTZ`bLZ[l1',rK8c.99GK),C@C#p0&H>$)g2eKm9@UnVUoq%sLQ&en3BU[4sg+91/XN(Dd'9-N\;;.5mg"I3[+HRTgF@('[Q
%5/8?kPVqZ=W4erF+$]//bB5i'aGkBOqZGO_[cqH0;60ppfX^,P4F5\1oR`,F[i4=20%u%+E&_1/J37u>/HZY'S?Ne8]64s]<j80Z
%!I[GG,0<6[JHT9H.(Apc%ik,rPZHfAS,t,,Hni]PG+:apKUoDMj2\n-l4##bIF7W;;r^2#i;&oiGl'aI;Ni0HnpMej^Io^58`4Tt
%P&$5^-PR8c8Nu-Ak_&8u7l-I3#L9Mr;hdCd:#i0"E?=@Up?8i-mq/?)Q^qK[J9AqBAk?I;^i6h"TX^:d:9.h:%RDSR_2IW"H[hG0
%X%t?VY<\)ZoM7,i4Z-!@h%S\c+DD77`c1)FF87B<5Ff8(QAkDkOG$\D(?4,]^c]M;`<!n*WEj&!3(6WN%*]U3JJopnZq_+[i@OYc
%Zde%c!a8qZ*,E&UP9e<iNmcl9?-?(Q8P6eV6^gmi65M&e5u:<(JL#2C^RY//FbB6g5kD>Wm6e7dN9r4'=sEJ0\[8n<7T+BX?7uh<
%6&j(>nT,(lK7));Q`BSZ,)#X'4IMgD*aIaTME=$%oV_[$D>`s#rEleK<KQK,fQ8bpDa0b6'm)BgPa4b[X0UU!j%^bKB*uje#O(Q?
%HNeU#;Z.Gsci[TQf^$SjcIWH#Q2@<;^N5C`Cqgf8]lWH?R>m?kQ'2eE)rOtpjr2)sjek<";UWZIA9/74g^DZF*dB6PRcHi+F.P&D
%hoigK.:d?E?r+Sp:nubKH&pms3J;;mlofss%X5e(Y=$,"Mg-d)4Rc:l)GFHaJ*%OuIm#YaZi*R1L/\Mgn.\uUi=hIX,m)<K$dIC#
%1H".HU`"HPTS,cj%HeIQ4AJ^7c3!]`$ua27f<In2.=R;bVh9@;M[]C8r:g'hQ?.lD`R6e>8k7!e5,WLm'0<!b1<2gE7n`&j4-4c1
%rk`rCgi4Tae8eZU^+"u!]hi2XkofFC*s$go&b_kt+'#JZZi1Wh\-.7Ts1J>8@_,2+KRo#sQ%j3L`T2tg`/]ZaijR'de\a5c_j\8o
%$Mkt>Tq)s=@m8lQf38'EkiW=8YQ%L$hWT-b]Q&#`fjpRWKQet`Q>A8pp-X8KT.ar@)T-Aq1VbH98Q-"bfZ2XbUn/ksS\i8CUGLRB
%4=)[k+8A-bOQ+qDG)r]?;p+FWiD7&6l&N[<?;5p"a:o(_s#<14'/]gNnCQacN>0'hRjVopQ?,KT4jPn48u=&s/Q[Gtn<gE-(f.&H
%/:?S*4c%]pmr:iqfO[\-[di4Fe9a.Omsm2rlITmq='&c;6AV4'Ifqfl.]c6)XWIFCR?oJ-:L)/(Z&l2#H0rJ3X\U1Clm;)k93!E[
%`W*;=lEf-e4rP$S.qd;K306/E9LEMe#K?6e>p]1g2D_P5XeXq1-WaW\>K%pD6CAZGE=fWJ&u^K'k8cB;%uNb'>@]q_"r`>ZEXBbB
%CXi;&EUTebqhmlaYKLph=B%&hQ^IE?`-$$B3JR!BDRfcre6!37e6Ouo<jU$OEt,/_YE[uZaEJA/fCWC:lfT-O2Qslsfl,"IO%!Eq
%H=sG@U+FP1)ZpC_!:`8!aN'Q[9<nD\8'N]3TLb[G.^Pf1's&/UG8(n\+pMr'kJ3Ch(S;oa3YDh0N7-skGb%is9a\/"[hGu:_*iA<
%JoM@c[4M?#!V\=9&*;PO!e$A0'>IK6F>s_BQ*bk[LGEk]_3NTGe_l,(CF3pY#uuB<MLr7ha[d:[9oNfo-!Z@)])kr8Le^UJBcO!-
%:;N=9V$@B$T4$GZ#Y*FOf55PUD6eP+KqS<=Dg27UE"[DMW&)A^+l_8d:\kG+2d=\g9@W])`0q70f$G>dq8GakWD7<jk<A@$mCgg^
%]5laC]7@OT\;?OOE(tLI4f"oXRM@Dc8jnmQ-_*$l$Il#X+:/=dr:ZG@BUP;["eedgP,a%]K1Ii^@Q4WgiinW`8Bj4'+N:`Hd^^4A
%;:<i>$XqOm`ip/gVHa/E4#H4#fg6qJSYa))$=^>42OqY?9!pF)Y-NsNNTEb^UcD#*@=Asn/Sto;.b>+FC5nN_*]1+@?>U9W,FQd3
%8RqJ>\@ZoT670q3r^hM3S`b2Sbl]-f)-%a/R/bJKo"N>+CGaEh>!fm6cCP_cX4UY0D4l`](+Bt?58&A>`R7>_[8i9PRZi9e3MUnB
%c8(A"V`6,I4O,jI*@Es0Q_W5$g")Rhj<p'c9"3:'73nZ#n-;7UO2Or<@Ra2`EgD0:,8s;idr:E/(YYB[MGgelXKs0\PN6Mg'/o,4
%V-N?+o<P<1=n#IC31H8q;L0Gh>,9FWru)kL\WSFcFhs!'lZg9HLRH%Vr5qP\V>TDYi#ir<^#U26\:u>qR`hcZqjBBFJsMNqMMidF
%U9nU7FhbQFG?4=+a]bq8V7)C+Vo8O\X@Vl@<:Pr9'D.$HVq+.n\DhWm-[Z;boM(D/3_r=D7iY']A)GW`EYqC*jLDs9U>@?`W>pXH
%VjSN[<F+g$H2b!n%$IFLpT#!lB1qq_&l#es;GCmGQ`!5iRM>EGCN;fmBR7TZXiP$%&XBoaN^RfD1iHkeZE+X`-.8TLd8B0rB*jSN
%ZkcHC%L;K<?A[8H]Hu;)K;:Gnp\_@b>1Ue)a,1k;Yq9!eZ5"6o#M2pnEO1/\"MZM=j[`oqHhLo;/m%'g:#U8$hRt*jT'brOog(,"
%0>/QraX@t2ffBT(bWCU@2(.K9FQAQ*Ji'Bue>$?Yns2K3m?EcPOa[qs47gp(g:EmP8k3ntRbF>Nr*YP39o>[/P4(S5q6%"%G;lLL
%XmnV_NJlH]Cbb!l9_pFg+"]4o8an+6+^#H];2QJO.F0@$CoN"%l%1a7ifl,f;[/cq@j>]?TZ5_\nfTS8BA?'%Zo1gb%@"a5H$i^L
%Tq-9l6Nnfd_;e$e;@2_:&SaBVK161jkut$*:Ao<=+Jn5PmB1/LD4Y'Do@E'g]+*/!;sF]S\5"*RT1!KKQ.R:[(t?'FW5*i%&R8$b
%[Q96)UVMP`,-R%e/ED@8Y0kIQLWNVD4.-[<_Ju"):X]/4<9'LA9MkHtF$-As]>u-f,E?aSH[ja@;\h:=Mf[%UUYtGkL8jp%TAM!o
%>GV3BTDV-J>'&uDrT&G=860>MBQ0ZY,_4&pJENIkA]AA5CCDi%9Es--$-H#DKRfBm*t]R"84U,L*D<!/QQ"41`QTLDD7/@44$6r4
%'C9`>8eJ^>pUfbi^/]&dH!95M\X'G$?`<M[CAebq;@YL/8E)q[hlrGl:h+-5P\FVe$0K0YFcgq<4N5a%]+PXk)S_P;4VA:jRb"Fu
%jbhUkd#oBH2m6gq@'IY;X*IH%R-ih<D$&$pd_W2GkELVR<I?CWi(,f:?G0VSe$tR\F!lh-=Ln*.)GaJq46q(#C,]r%c;^C>,5^eS
%?16n@[<ctl_VDdcU$))KH=_fY!Bgrcl?UG8]RNA!f".APj>'?!MPmcQM!]]9cD(qSnX]gC4XYVuY%*3jV`oj")L[bf*Nr-;hfs#-
%GePs0U<:sQn4`&fJb5dRr!tlS3IKLYRD\#`ju_OnGCHJSB.lQ`KiIFaSYiIpHKKlnP+5#D*i&MWkL;U14HWoI?)6_t\g<8A1\j4G
%Qb>p<X"sJrR3=RYEWL?q.`*2q^uS7>fg=lRNH>lh9[.ACmA^/a+rn--VSL;&!Q?PKm$,,-/]K@t2cC:Ibk`t+l]65Wjl4c_c.K\E
%'6jo4cB=-CSQI6oU0UF1eEmB+9ub,^:3G?MSQVnj:?G6IRcnXZ];r8':Y_e75ab4,\PT0!-?IV4gVp6rDuP.b+XQI;)C^bKK"ucB
%lLI?nAbqR*Z\8Ku0.%ZT7K0kS@sG_4!rRgf[V1uqm7b'U=^kNjYDd=\OZc=:LV6Qe[Y!ZF>;DocI(rFaF]kOnS.c_,N2MX^N2N0P
%>ge!<j^LU]mM]n8f-9$IfZoZ2BWQKuJ)jBD<sbEE2h@"V&Gsc%W/blRk6AsQ2h8/=V51XPs0RZ7bRa\>_7/1gamD["F*\Hngnk;I
%odQjf1$4KQDI]Oq:IE\!aYr/^HPMNqfPDMiT!%3ZSTg-cjDIK*AU-joalf*J-fFe5-'Z-e]YGBsBHH*S50+V=Hq$#c?@(O.NF<e]
%*@?g#!CdFtm^O/["f8[s9$DB.`.p&jjt/]uVSbNX!):JA4+m"ad(>?2gd\1bhM'!bG1PUS;n%=6FD(0QO&+S$-V!!U]^=`X[pa1M
%/'6P3B+mHgH(1U/]H_jZ[?KJFI=[Yja7eiOId5R77)DAa2>b?^[P^+Dj9pua6+$)$.2P2pi?B/%5%.^D*P,9t0pJcBA'6DJ3W?:I
%=aWZ0@anGX]pD\rQ$5!Q/b>G0LFB4nb:9P)&XIQ(caKjFA]h='9'=:#bppg%6Kqa2W?=7GGo$(_oJA!=j1oaM<i8=6dn9Eqcp>+)
%V.V0Np5q+FHcj[PLSQ"dT[6'R0;;!X%`@.2Wkl!PLe,@*6+.&V&.!jkX+Ep-^b4p0#Y58cdKcKN9TGs?mkTSB$r>jE30B2[hRnK*
%96p4B?7+]s]2J"*2uA*7pr1.\b1<]/LH&kjG1-K7RUDR]IL"!^1Cgp[UMW*B]/opqO&+?JC5o7E@r!rf>1f7J11b!DR('@Uc0VNe
%Q)+d'1r:Wm`S2tKC<:&,kYXUp^]Z)9k\U(2o[m-f18ak<'l\/.#<rP2L_.]6$)/>h<#SBJ=<JtOMY3XkUE+F,%25f[i*ba\7?.eh
%aM?EXkr;S[g^<IZK?Sm)U7s@'r2t4]^"RB8.k`Fi[cYlUM!JL/_af?Fhr1OPf?=KAN.]-6q3-s,c\Bulm0fERH1FhYrbDE3G/r41
%7kErA;ame<Q&\"GMs$Y')W0f.cW9@?]q2@Yr-f6C.b8J8Ih(+5k%_G4ppA(cQ[)'_&XY07ZZU[V)P\j0*GWfbI&@mpVhV2Q]$)),
%1f]+,#rO>pP,`->3Q%21`,*N)[@H2fQF'#_(?[;D78G^##nB`cG`)2r<50XF:8GmEB(5$j@7/M)29*/-]@U.)6'`1aC.>!O;,_h^
%"Kp-JN,j4M<r=0E#J]CCs(feB^(c@++&l![4jI'bBP)7EqETc]2[(/+0ofV0^I,>TBGM0(&#*2o'Z-[(+pI\u!HPY]^=1lcRU8+3
%8O&)(:X+2g&/n"8\XIlV).GsmI;89#I+2o`4U[S9_WG.I:TZXWeLX7g6'1E,jfI%GeS6R5F.o$223W/!j.RA&26Sm*M4qqqj^o45
%U,a,K:S[&e<&<Pg26&r&>M9hEMl-E3,DTd<e;0Qpkd6i&E@75#XWP<nSnc3j3!;t!*$73hZ`d;A;s?,7[1(RU*:l!W<mPb`Es1%S
%huu;/1V>^^?o31YbmE@lV"U+=T8o2-UVPS_qGa=;Qda'gEs/uQm@_I=CtiO7(G0$q;\7I6d8Qh)"^9,t\D&d2m4sT:?"D6qRaW$e
%Ur2)U@CjqeOsa5r3gP>@P^1;H).9kMr,lC1-)?p7).2TYJt,iX6,bMs8K3YQ&Y%T%**0/Dc\(JMTOh#OFXBiS+HDMZ$+m/KS+r\g
%1rl;rB@B!WSa4ehOCmQ0B>^X#Pn&+F\XN(Y#<e9=>BE]*lc].O`gu-0YK;c>8u3aSf-l]R#@'J#d_9Y_80(+`@#W17Z>K1K;n=u`
%nuj6%l<gX47CLFpgY3/AAW'ktUKk60(e)R5B@bl#f9@8EX0P"<'"2\If\ugGRoaZI=pY$.A_)W/XG#DUONdPtH'KpT&!0m80!?1s
%)`\osB4Oc"]3<o`l#S$9fr$f;/_d'QgXIk=9`_X'C:l+?:289!7;&+RhHoN]DTZ$@E`Sb@?U#/nXts-3F&uhEXHdo$RV&XCP,!&c
%F\=N/NBEDAb4mqkU(^`L\p.a4>pR8)LmX`CAk1@P3X9nYWAX'.11DL(Z.Ma.1j!W/*?>rA$J2Ph(Q4g6W*+dQAB@-I5OZTpC2@`c
%>ME/5!D+UC43("dUP3kJlUIllS]E9]=8J"<#>?(G?&1A3'1IDG(,Y@t7uI\Gj-5F'1PXhbedd.7M;A)FfaId"C\1Aq/m`kNMekDH
%9alWQ@R%`da7ukcd:3DAR:;\FE[IoS,CL$B@.klMcR?"`Nef#'R)J]_-a)&HK!(SZ[#S&;7]%.Y4#lh+9Zl'i2^;PQ$VTA7SElGm
%lOE6mhYm.H05],'S('3VM!8(g9X+!NBCd.`F]5t8=g,;][[/o<'>)4F8SY$h1$c7/2]am)AJEL:@2D(q<HM@RPDn0qZL'2IQN,es
%j4>4DYF&'")'HY>"ibc\)4?]-qsgErHuBCjH>(6NJ8ll0dk]oLi[q#uW]AX>NjQ;i!*kAdOP"s0="V-QiKFV><+t)kCTXJCSlg%K
%?res(mG/i5]2H12+ubV,R\Xp:!#Ko?"%>?AH,`QR1d9$L`p+.F:J_d3oI+>j,L2n,1lfL9$UkVKM@MTXb2<,m+pP5H8Pq:f3#P.Z
%WU"V&<bJ!0PaKWR;-8`;dkmRc*=G74<jS9aYL:_<bA,=.T"a!W9^%7#bj3Ye>7(p-W.ROR?7\de/YZW1G%FFH\4rPCnhlA6?$O%?
%\n--$%jnD3j(!9qi95+\m;J#Kok&AP`la<Tg$;6.`4u^;Nb!UN0s`ngY+Bucf6N^0B3gCK&8E14?*;J_PRJ7A+4r>9N-`F&R[q=n
%d_kdhbaLDpA;s8cF1/1?&rS;PZP]!32'),Hb?AQ$3c4Ft;;M\e/.ZOfYX+UEQE3N(XN@c]b(rKJpha'5>LPjXFP0;[]$5cU5fQc?
%q+<9@NW"!KIDsZ_`RB4RdsT-`I)XQ^`RB4RdsT-`I)XQ^`RB4RdsT-`I)XQ^`RB4>f2k)Z=$4`/4O*'@p_?Tr'i4E9:NHc4k-Tc=
%\J%gB25]V'Q@Z$g6e/8X2-XTc(15*u>?b:_'O`e-C_UHU3#b<?Lo&A'-+1/jb`/?7GW&U=h'hh@b!8&+hlo9J>-^]4SRg`k3&<Au
%*[f%)=e6P33UOH&?o]g'&3BDSTmB2,k-i%G8YCCtVk3?["M:Fa%b\*SYS4(&DclkE`sq-!LARVK#[*brMcor0W][p:_rH<R5W&Lq
%=T.RdZ8ou(EO,PZ6V9'&ng'9)"nW05`39H;(7DSnr:]aPAP3QNl9l(c>AJ/.Y1TmmHcJ?)HO0%$+(K#0+:AJ/JL'.OS?_ms.`Y7d
%k@ljP'-V;^5PsGDff:G&UuFJZ!te[IKub11@/N+Le,NV3*NtT*>L?bm)^cYK[Esht[a/BYRk:=K`uA(,`F!_Ab@-\27e'u0&tG.f
%%HgZ"c3qh/X<]i>7S]P[eR^,UCsT!Kj+'X1&3D^1*-,T5?Z*C>Q+oN_6@"7;7\Ri/.9lUYVWBn9(#Yo8\*SKgLK)`H$ZeM>m!_R[
%WDt4@Hcfu0qX-WAM8/!BUQKuVd!^HVGl)c![kbKQjP9Hr`T?'9gC1Of25e,+eqGf+UX%u/917]E9fnfa%SH4C`8I)YGFT/\,FeT(
%SD]s%K=<36msRM[Y"fkkG`cUfi>rnW<>LD;-"?D5s(N"_3#0PoIXaAZb#Oo2H0t=KCb]&P-a/kFJU$B;mOmj/XIiA4[ZDfuhX]RY
%o's--Y;Ld2n]TPZM7DPlOi=!6DGu2EMYCb?/mdLp<sW<R(mF[-TCd=d\%AAo-bQ].Nl@%YpsJs)L/!^&AX<'Zo&$!+Y+SQcWEf=b
%mW2"_.e`mUBg/_mhOmF$A^:$ef(UeAh7_maS+@T_B^5C%L\1r`pZ6m+`2r'N-[`^"T&Ho#JqIWdT5^q8j*K#h%T>:\SUBqh/K+O#
%`A:d__dO]sDksd4k.CL!L]-gKQI^,_W^DbiAqs`chf/V[%BD9d55+?.X4cD!cOp1"A9thE9YZ0R-Y3'^#G&Sa:Z!AGG;/<d)?rrB
%=33>$qEV+-M"MXl%]]nN>U.j5o_M[$k66Hc6Er2*lqh)<q&Mm#'%aic[kA?]o=7Y4XSWY#^.#c^kk?qAIR:s$)3<g#hNY.BZ(8*,
%o]cWD^W"UH-17'U(HXQfM7HuZEjaD6Tqn2OV#IdM+&EHrM-+tgj228S\;h9ubd'3tj,:Pcps1Io_,WZJ[c*81#)[8R$REd1+rmaS
%3C","/7il`pc@,;I6RF$e-"414=NBEn1kC@cbae5KRbU-3FR2bq#2/jUDt*3YUY'T)sRf)d5#`O)aU`fUTe4jN%[iZPeG]"X6Ae=
%Jj#dgNh:>(Q*DqT1SU)VkHacGmR;0_("k4YDRigi@"#Dg3N"Y.JC`g7>O1t!kC%g$Or+,b02`ihj3C4S:R_kXj/Fc+LL`S4VY<oK
%i*9'&0T:?^?u=GMa2[c4IG6dQjtl=cKYL;;Z6d?eh.=sGprf<u+XL^OnLU0^6"=\P\dcB`NWt"03[Y:6goC.V*`t$D<W$n+.5\%W
%(4=CHXGK'NoPk8SYdf:JVJc4J$Ma%]hP[?%OeGA>ZUZ@R0/>$)IE%*<r0\c2'n"Sgi,(Z-5cBuq&mkb&[m,<8p?1mO^&@HcmI-Z0
%R1j&UT0.Spdm)2lNB]M=s6eWkq>'Um+'u[2qtSS5J,8_32tGR[s1r2hF22MZlT[O]oGt[9El?M3s,;I+Dg;#[5l9#)](sIm5Q$_W
%q$+S'lX)c'oVCI(d]YV+=7%l\.-,LAN(?,s:#1N(FSaFl7:#E7>J0LPE(h`&J;'OqC&gO%hIQCC@b9-O>c*og"*M,oF0@X<af41q
%qDkcSGOq$=g9Lo7J8`bnD>n7P.85_90K\`r9,5dM.L,-]KA-3$'Skgto`d4Gj@u1Hku,L,D6;i)0Yr+^fSoGZQ59+IK-$X#&1G"`
%EfD^E4M+MkcE2GFq&J,Neic4a0IGNZ]1/-5f!h77Rp-,b7bM]um@U,s]=l0@]%"'soq64a]KF.h;RRU;T=2DaZ.J2;c_eSbI<4`R
%'WOg\>IULbVX7'g8sai^[iTY$p"t.PH2Z(L]1ln>:O2j49&K8OI9N'mK3kiZGL6fR.;dg_#Mm5mT:_8#H(@.L"8%o7STJ[\.bf9P
%d76G!%_3JO?`;&9o(5*5^ArKN]l32*k_jUYoj"8QH@Db6[HPd*'tJI6RUbmBWWmn)Uf#GqqPZo%SY&rTde9Xrm"p/FM\CNlUf#Gq
%qW=c.D7L@h[uO6?/@"TE#CUlU;9q,rk10S.H7[fTLSqko]Y4!205iT1\OPK3)VA;Q3ungCB!"-853U0oN-_YS2n&uOVu!j4[>1jl
%p@.I,)>*#B7QoAelWa8[["&4d]lebWF5;$76shSC;9q,rk192S!]INs/l0kgZb?%)05iT1\OPIu3&'Ytic_F#du[hjNqK%u4*K+p
%NP;pqhu)d0UL/k>$%hD>`-d[1)8:XbM>Y-MNVjZ]i=DpZ)LYTTCg/D_U:1)M=cq2[Q&N>%RM'9Yr"(JCm6u^DHA=mr9>2)UL,pqM
%,U2!d1,?QE.sSlf^qBB?W4_=T?L6X=,2Wk72"XC.&8uS;]Nr?;(Sh6G&fR?\6ZW9Q;S6FI$9SnEBMre(')g8jWoqs!$MT]AFQhU/
%6:RuHDWbC\p7\c[dR3(cg[rCA`Kgg+l*Ki+#LFJkfRq=e%<@5e\N+W`&<c5!i2^/;kkIcK!T]]N:C00)=P/G&]>Dqd;hh"Z"hJ3?
%#!<ugHb*7F2c9ab3&Kl!hmo@!?;e@*p@pLoMCqt2m@o8p6^?XU\g.A`,I]*abr<8\s2*>/mfGLiT>FETEimOB5=H,HdsZn#H#7Y$
%>G13mk4i`R8"cT7QjV_AHM+0/f>dAhFs_u'W$#DuXJ7<W[P:U]O^;Ft'OuE-A&k#\n<`KaqF'3R'<.6X<GDG5&API3PP-p(+4#/t
%hU\dKUQ>52%3Hf)HK(1moqWa@"5RN)RI5LNR8[@ca%9'2n55"EqoH_8F-)[f?(\CJcfj<dNg9t!CJ9APO]>4@nEb;EK-O\2oS>C=
%R6RA5#SF3O@OOg)fY!F!i*s[>!ei6MT73Z<"!_]jB%&hl*oTn/$+<h\Vd,+."sK8)h2t@W0lT*q=]FQUW;q8.>u^"F@3D2ql:iRG
%4X?gde&br19gn>cjIX3q6BhLl.<Qe#`sZmo"25jK!bj%oPVDAsL%nE^IEUCD?sS%]=&99HA/TLA'T,RL</?53q_#2f7T##1R9,*I
%BYigt7"IbG4Jm!R#FJ?51Yp[1,6;XgNU&m[Lsgjp3$*g/p*j>GSd1)g=_DX5*(\f=4HN+lF(HabcVA4'0a#`J`e5M16>Z/L6;0hO
%b`R.Qd!M'9SRU.(Q',^(d3&fO.5?/3[11KT0VmKHACf[/7b<Ag`ot'O#-IG7MYqA]<(@!u`D*NOh.Bdg_%C+I%.(<e_#<J)EU*:,
%^>MTuHI.Rm9iYkE9gYY^l\Lq6dh*le0kR'pgrPnN1@5H7bau@+&7N&13K[^cNVuer*c@<ZQ(>mj/cuV.=HFP*Ftn4qi:,;%fU5#C
%iVrN+k4+c'cDjVg]U:hsfhAj`M0Ce3]qERSka=jafN@'_kV7Vo,$nY$Lb?%e;6,qMiV(@,:3oEsM>=r!'o""FkqN=o`+%<:$2*AX
%f;HI#;0=EblnBR&d(0J\e)r:UU/5&.,3YghUoAZ<[4Po:7]6&",I`Sj:<h,-/T_?C;]s'@6&,\FUQbBX&1b48Z[+4AXMf^[_+F<f
%*4hNDg0b/1]rrW'A$V&m--SP6l3+c2O9iP@`#a1&!&9=4LY;`ECa\&dM4q34*mL"&4+54;#8+A)4,+A9'<=)'NUD3OgP%.5oKE$n
%&n2/QLfK,B`r,A&dR&./]\q_e3(Ca'XHb?V*OFscpf'm*1UB[O*TsfEd:l9i%0]kpnP;^I00R8n@S";<nsBq6&0$4ZS%!5Akfpq!
%V<^LL2@?[r+=edbKrHe=E^R!&S=$`&->)>bbarfP0p+@picZqS'nh^0(*ocP6%t.Pl%i\m:q!`cOFe*n))[MMJf@EsB`OJ\Fr+-s
%5QFk0bYoZ3YJNDD*(GHu?f]42*)=Pm(PD]bG[gbScb$"(1u.G(W!Xrc&(bZ>ase[Z+*;`FO7%qY,&9-79Q[R?+MBO/`ka/N&H^A2
%<JL0F,84@gFTlk!nrOo^Q\uMmn3B[Y:7qgSKh.\t[/oE.@YKBQSr#<?d3=PBTFB%[gG+mLITK`O<Fo9-\"$Ne1,0#6Gu-@1<L#0V
%9Y&*kE*Gf6-p-CWTLsAReg.8&/t!Lpd;pkUP\GB6OO:QTdff@ZKS]C0AlUNF]%0]#6R&j]i>N>eOaK7WIjW$/kD?00%4I@$E7%*N
%GsZA/4\PgmAsI7Wqr+@J(/.!G&`rlI]4s#pecWHbHkgMGUPKaim`V91M(f#%K*[+7ehdN(DARr"<i$#aN%C-,(EjIK3Z>g]PPJ`H
%,Z<BImQcDggEl;WM/'ap&ZJt&dY?bmnjKEZp.h^BM1FiYD/jc0?#hU=pHlc^Xl"!qQjuU6CQK9!S,""g\r2L+70):?oa9VMe3Y"\
%b#9>BF1DqEe^AQE!$*kS0cb=rai3=i,%[PGd^058*T:.k2rOc#n?da"-Do+!.Ot\&,o[4*@K.&9)D,'lJNP(I'X#GpZhqgnT%7)e
%[)G?:P1fbmdP7\j+$?<opk0u7l<KCK$p8Z:.FR?mSp,P`*o+h<E"1*nlIDZ1>-1Jc`7fak!-C-6i5JFQ%.n#sd\!<?1Qb]u'6-Zj
%Tf%M'Xuk:jpu90i.39gXIIm&fdfTB>JFKKpD#3>&FWJP;/BKc4!hsjD^jTJC<2A_A@J:>>WE,DXr4V[.*0\a;_%L5`CsW/ke?8C$
%%A;E%H8X/SX2(/kk!="\3,%f]8h@Ko1,6[GI%_"04a<ifX,i+6g<)bDim1\tg,]A.;7$UR8,Doujgs05`ZbU#FfIBdm?kLp?<8<\
%L.e!g]n72joui?8)]As=O:Y@S@OWNSBM9LY+PR'R^SAUo8I_?&6m7/T/C.#,j9pAibU^VRR\78gq\u"BYf_!KH:S$`U\'-r/"5[<
%\KQN%Rtm:ul$ZZ_[>;qT]R)EdW.1&MF>=;E%:kSE,4M-Q`9bTR"4`.[UXi7AMMI,3+N2e3)\nPOgZ4.r6?Z7922/O1^**'DgBk)%
%\_*%[`0,#5m2!=g4'S#?rYZJ,<)F9bDD50VemT_[XeDBmn&Us=C9Il5pkBMfOYl$D,i:9fZqaUV:cF;!:T^\&b%KolZKB+U=uP6J
%B-rciCGcf-Z#-(eVs*Lt?]8/[f,?bH(a<"f@ZMFHUrYQI8b,h0B@.QUF_]7WoRlQ0W+pt=TMfiH_bgRe\7uk@Flbm>C<poZ#``&D
%(^hQdmj)lW2S$*/75'CP(:m')eb^u;fQ(P4e$VYeZBK@aW^rYYk^"D878Oob6P7E\R=`t^FDjW$oHF=>@3Sk$T$ST>'P=k_IMh''
%rUY@#3`s'>ei,O"=#$a6:2\Y8E/7C)>B<jPhX%+YA]H<.B]o8!`5P42''lq;P^OmA^<&d1#GOiEIS'1kMR7rD7eYVk.!G,/]b*Y?
%j+4YLL$%SG2hT*)Dg!5tcdio#k5u#N'+Dn@P4XUqWdNd\cEZq^U^`5dXKrn<^'n4BB.JWPLrF=$AmljGVcq*JqC^=XiisCARaB"\
%S?(.9?1M5f5V68fXmo?pKr^Ro"YU'sK57DHFM:eU'Y%geCGg\S*)CkP#:6]GD0fid7)UXq=DmHNUH(8+GE?g)M4c:10f"[`HbGfb
%DmiiC:rUAi*J]B!&[Sar=G04D^rAH[AmKk1X=Cj5fqfh"(j(EH;W*[qen0>0EHJcIT%'Q'BNns_!Li!HI-%=D4<#mCliTVsr/H/Z
%PT$t`[\V]]_cSWjV`?Bk&;@I-^8LNU2C?f7_E2MC>T@H1@L^GY?*\S/*tn=+fd03Ri%)HFS/J1imkEd^&eXa[F(2A;9MS60\se8V
%kN*rgAm/7rGFGBW,b&Q2&[E1*:@"80,]#]P!4$<"aBV@kZ3%A%N)n=`0,)tA]pL]o(B(pcI8K>EZTr2`T@l#7/kgITGr[<(<3<`T
%LmVbYORDj0CM9CVWM,4R"1Wg@7uKKkg^he2J"j)t-F4LiqM-HHGP$qJ#uh.DL".&G%.-cKCibD'(<7S)bVF&&XG]5M$YFVBC*JLM
%fO!7NMG>g;Lcr+NFEUR_]dL4Y!M:#oQl$&AI:6=hg:V0iCc+)-`YuJA!T?UK'E:gO?L9#S2%fT,21pW,K1=9i-U]l>U/o`G,=Te?
%#k9;eN;H\=:m5aXGUr;;,rX+i+36@f1@&C].fsu`1^9G@TG840K>PJh'8`]H.T7^!OcBs0:!s9N)>bn(FqB.C5bAp0@'J<la&j9Y
%DCXt1cNB//DCD\c)^bG+.Mps3b@jo@g^frGMMZmI2^kY@5N99AEb19nRR2@$;'Qa*+.,URT3.\t)AL`;j&f3)U%VCS$_rbJ093'q
%TlSb0"3#O8L*5c$ZbqY-O.HI*o[;1;b`r<g!6!sJnP*M&/<EMAIL>#B3a"\0kO(";+<H&=KG&++O-lOsG%EXa`eO9[$
%7KGV;9Q1Ri&)goaX?`SL(ZSn$/J+D46_qS/'nkEIQN1J'Ro=j9p.lg.^j)fr1>rUP\WNF_eDR8e7BYKp,bdk<QNVS?o'r>oC\-'_
%gjr.<9(Xq0O1&7]]blmu,3$[i`L6t!JeA]c<O$mJ^+lSjQF0t&Pf7p6DYYa.EZs8^7LV,0TR:W$]NQ*TX=U,OaA%P/86.;o;Jn)S
%Ht/LO>9D;4JqX##jTQ4C*3,KppY(hHqYTK"g>[f=U0^aR(M6if;*B1VH?fkTqUFn-&;=lK>n_5F>.99SUuhfcPR=KeQN3Gd6mrJT
%Lr^YuAst][OD],>QN58,m^4EHDfRLOYh=o//OHO'$k^;:btP[KWm-I#B3Fn\:U1^*%TtOY38K:@f8Bt+/$0Od__WFN-;Q28:\8h_
%m>7BZkhtC$bm3?nfTYO)2@&NOV3<[/6pD>T\#1>gNF4OdjHNW3W6TA1NOn[E'\n[:KWP-I5SiR=b4#ld$Gm%Y?Y)?\=Ki%2/aW)H
%0U\rb>!6MY+X_YKAE=Z\I@V@/aO?Y=37PHq.W;KLbOCWGfYPefk_GPp9NYYd2O'H5gBG7<rQtIoTnC=%Cu%i%%3?bLI#Q]*ia=N)
%M?b'l9V'g"G(&H*<HlZhaq!#-g]3sAXGboIo%61>&oit+=hS>19gJA&`+bPQ4'A%+<r8\]fr3V*PO.KJPi8mf@@C!&\=$L:2t_$M
%QHcPVXiX>1RfUn<#7JXKYF83q>9<H)DnHKUl,cB*2ra,1DAT'&Xqrj`$F(K]"=!/Th,<&G,Ll1q(cH*J4Adnn)G"q,K/;!-`+DK(
%-_Z%&Qi>BI;.bu+$d/l(?jZT;D>UmnB=<ARgf?hb,0+F3[fp3$XP:&u?*gM*B_;s_)ps",2s&W@g0@gR\im8p"AP]F.rQfd_OV@'
%c?AZ^3'\MPERsN7V=ap)'$O3)`':9u<caQe$k(HU\&3qJ1*r?K\7)VZCT!j53B"g?V13N,.Y)j8U*^pml/(P[rb'#%;#h?o(G(`>
%LMU88j$6p3>o-h;1:6&BgF8)8/d=nsUo?\OeqW]-2][p4!$Y"2UYA4L=0[8ic_+1<#j_]*<&'Rj*S0JZXbd&4]A[FNCdD,18.F/7
%+sGnRma:A[\5'"4-GV[3W@3`05<@W`-D$\ul49Shk#7d2Sla&U,;juDpeeg%rXWmqrVr6g#[`Ir>L^gMRtV'&R1>?L3K:/m$hB*#
%nrnFM68,jB=Y3%8OX'qF)<!Zf*@!j")IR(n,NfDcLmtWOaLhX5#rRsK)i9I[PqmIb2g$cL#==cbboujTCQ@0S`6/E9$5R8u[Kj$e
%<CQjABh[lN'd6.CV,@r8\C1SiilRoD=8$M]&^\XS-$UpU5g_D0R_Z>\dd)h238gRNJJp-SN(_44)iOdU>-^qfBr26[/es#\OBII]
%#lQVH7<rmD@'u,FeNR\,O3SEVQcXu>OM#+u]Vt$"l4[M/K!s@3>l-&iUEnoii:hV1I3*SKf;-70"N?27MACi]j<;P)BbsuLU5(*(
%(c"*Sh;8"@Zg5Ol#`ca?fJX>[]i/96mC'=IHl&!jp)L]$M/L2h2K=!b]U(2pD/=-n/#38e"50%J>$;g$KQtne]FSG`h-1=cM6I;%
%%FJ-:f,-3WhBF=lrMGrRN$K.^%2*^uh\IrR;+"668Ta*Q!<t0b&XN`bQ(Jgp\@3A;S&UT@MN&J_ejXQqYo#_i4-f:@7UuBn]c%j2
%B^f3iR*U]b-e\d#djjtYA/r^JX@6uq6o6`>b:X_nhF8D7G]P^Y"a+2V`oo>$3:9X2pL:(!blQSGP??0?#h$s/ib*$O5"/<c7g\NX
%^l+uYE:us5;*C;b'W_`V5(PecH0fP`+)T@SaYq,DlMuLoPk5ktfb+\O/#"umbS`/:fV^5\WBik85[4Bd$'ALV\2=Fka0H6q!prbB
%iG_SFoHBXsQm;],#bk'M"Lkfb$a\"Ra48>EgLQlI?4kr%;H!$pBgXN]]rA9kX$2U9?UZKPq=;H;V9[^$=fckJ.o40U,rb5^XPj6+
%VDL"plZ?PRU^jqCS0Y.q1s$&q__kSkm2"K??0k4h#$G3Uif(%(/_KRI36.K\%r3b77f:9K]3o:FoYO#kA5NcCWTGHQ8(u6_>4Tp2
%%A]F%St8p\Quk"*m`3SH+<`'I1&ZF<TN`@e"HC?nL`fBk$R3p4bTatK&.Y1@>qR$Dc@7QO'%,#nlgdW>8laF;>+BUF$%BYp(?Ae0
%A]hGISc>Y8G2!3)(Z*CGJaOH]p/Jof:B"8,\1pIDM9seYOXfo=9`u(ocrjF-Vi8^7'_9)CGc\>RPm!#sbrk,<XefEdZ'hrHh8CMT
%SHEVXBO6Xni4UkEPr?+nQOcuNW%8('IBB9Dp&O%,Pu2/S^'_0"9QGl>)ZmW0c+bDu@9U[V^LeK^$u?2D4aR5m7!Sj72/,YO_^N/5
%B(Qe^Qpj99:^/8DVDT(+XI7#h(sZ9>2NJ?cVX.!mAa!E1)[pce<;Lisf[rn0L<:)^GYWfP?%c(q+=]:%a#lpC.6+TcPhqZ;aQujb
%+*B](&#sZKX&(".\D's5"tM;49q]IM?cA*Ii>X,c2'(f4`^K,2XbJuqo?[n+R6a7U_I.9IcJ(Z+/poKhRDM3mWO],EH\#ojX+m6X
%+LQLlV?%XG[1(h7N\YCu/[,^7X0Y3&at=Ca(t<;\.*LT=oOc!^m."/O2r`IJ/YZnkGL.qcdh:>jfE:Ul39[Gt`E_eb"i_!*W*1Up
%$LDqiN,8-qXen%l+"7mR8"LL,3sg'=W2e,#:dTZ/FBu*`2"?56.p86*X>R'mZ/HHe`Ya=nP`X`:3QNgI;L`.W4eAdg7+Cmi2RJRR
%L;LjFRrS#ML=T<@V&:JWLMl;9-"<egXkb>$$dt$,.D:!qWmZ8K3q4BP8@8s(J:+WSa-DHT_M]U(@gI]i76j6s%(??mM.HTUCCmeT
%B1h?1Mq0CXa$*sYk7P@>=*sCh$*mN'`0>lA"l1\+UPU7`6.uoq3Y?S-,*Pkr:(#c$IDY2&(=AktS7;--QME#/OXHkZGR9"4J2&!Z
%RS%P[.[-0kc+o0KPT4>^PV0C^U3do5-D<8$&+1rkI@BAONJ,PUoNGp"&pRp;X9nYQV&H3oS!7,ZoR3V/'%J*oZDZ)c[l@("V?u(Q
%&TMn,&LdudSf5[0(aeL"MJ.`-k.^!YrDknFigJ3uYI'h'BL/_&J`8-@6!'JuO$3RKAYLC/W/4('<hAhpKp.f&4a8]K5pt;*]1K);
%cV8\ZB9c6pJkqA$=C>I'71dO8pPGd8/Tu?B.i&:$9[:F*+a^lM/MmH?H;H1C$MN#W9]]h`6X]rNR`D,^],U1)@Y:MkY5;ubb&j@\
%l"JV;N5mf>)pt_/CeGfBOb6c(-oMgoM6#/\pp/8=Xm([@<<tbRC>.%V;,25ANE;,hB(/)ELb8nB7cfIYgFATY..g5,:o^g$J9NQH
%n'M6@%YQ0j1QdJIFK3\H%MH`?l_%9VnssT#,4nT&3b+>Ekrqrlm:rNmUj=aWD0a3mf;KR-il`C?U-S9XgCg?d>LE\%p<;ZO+Tthf
%#(3)UYh<@8M`S]-VQDaKb?)+#-VE:',VerASinbgHUZ7qBqVDpfQEbk,VucAa$dVH/EV=65\8X0[ST$<(QBJOn9<81LBqh`\OOV)
%_*RMs**Ucf6t[HLf;N08DLGS1DD"VI8.D];p4Xi2Y@YsIdGbk2.!qSk!lS[YUMH%)A5b:'*Y(nNe_H:F.p028"rs0I?GWYC4#(*E
%qK)l*holb-DrumekBNlZ<MI'+F>T(mNX:VM#u`Ng_bK(rbt#b$8e#8.MJO&8ei`/4.NHRg&B*?0[S2Du2F+gO:g3>@0W$]].Pf58
%LI&D:?ju'+KFZc\q8e9oUJmNlX2^mbA)pHoa?,8$Cb,d"X$&0]P'f&5X[23UB;0*WA-^2\0YN;qkn81j/#r^uRVEC0_n'H#I?`u!
%1AXu'gZTI`O2ON'=EKZ#;0.X$EXZs-oFLdR,7Oi_`P0SrZ_3L(_D3jmhL$]!Y,=&L\P<Zp1"\snO9qXc=<8ri"JlN,67.L:R+[!U
%qj&qPaE0em\qfe`%#hqj7hB+p'0TtppEGX%=;V5'_dF%K)?q2%A2=KFkK-%=o]7)BZm"9pa*L3k2bnft;93u\EPYO`5@L!0!*=#9
%/eVJ)+2t;h7U=h&Z*j0a4Wk5S:1rc8JR?=U=3@%@J;E9FA&Eq_M-1W8@eA1k.n^`2knke.el8F1d'R4*C9.L-9dQ,0HLrt:?01l2
%/M[+eNi6+QSJE,,'FmGgH7DeUAg6U./=(9483Y^pLfd$3gs$Ja9r7nH2,VjegX?l)j4H$-f$Pp,_4X<J?Dgb7j<l7"O>J0_,E%t`
%2YGBXNBt>d(e0V!@m,Aq:<D8b1G`rhr@sH$m6i3mT\7o-`##,sCq2LPCsj^HQDaY(Q;.H4N@l]o9W84?LXi.77KPr0\'4s$#Y?0n
%[,;L^Sk$3sn\qeR;.X[?f1+.G4tk>C"Q&C1O/'hHkQHcQ>G.cAU$Ml?OOk==p7E/<T;.f(qD;AB#%fd1oe7\.MnX;1'<'U^l?KND
%dqRAMSJ'#l9_JgJNuG'POM57=/ddKr)48hd"62'lJaHIR1>FGEdgEm:GkI7n4\dF:,i/goJ!/XnS&]js"T]od#pld>RGMSZi9=Am
%q-KQc%40om8esu6aF)YX%U8+,*7;sdR18"JXIEjW0++:cPrF`=d<aB*^k"1mq\mS0$g5Og8pUnU3fk9&qerVNh/RZB@CHXn-dcMX
%3<L$hH--sh&S%4V/.F[)P`L5D*5^<H[ndkn*9UQA37GsfMDc'(+JB2X#)OHa$M-#Ie^bVJad_O6*[?KNAD(Qectd>?BDsfN7O21G
%e!gB5KOa'iLpIU>!!cRJJ1JTNr/GJ_gd;a),PoI]j&OI[+X'<dj8]LV]T'rU)O.=9iWhb'Ceko3j>fb%Cg)SO$Q@q?]gh6`:hJ`)
%*"K0h#,ElV2ADa!+CUt=q_m,N)JfM"c$TpZ2Zh#t8\Q6[Y(W3S2TcjRV_oAubYD%E(.Ic9U^o@X0NU+$;A[uXc*Q])i5UZs)-&dH
%X("53![oBj+Q=6!`D=n9?J?$d)^X.\8b`^7PfNluIK[D"#6+*uQNk;bZ!,%HBQ.6d*t<.ol@$jGJV8_ebXu<()<ualBJYR-$o2r?
%P(_VR"4&Z6;4KB2PjjQ`#BG46hISI!L5r"+V&,8G-p5_FJb!cca:umO8;K<AQH$6S]E+Q-/0<m-#P<W=&Q%45R2j:'>t*>IH*f,o
%NC4e.:LUIKRZ^g0;Lenm+;>qX2%6CQ0@DlAmiV$'h@9kdBCLYlc^\Nj5q%Ep`jaaQL+L2;1,0)&W)sSKr>)40Bs#RqCIuSb+:YM_
%_NVI"_K]cQ"OSJDm:$\OJFr*bCiirlLWS=&/AH:`no?Ud"2&YrU?9ZR6+V6bQ/gN%@fj!V/Tf`kOXMZO]#j+ARCV#h-EIkqUmcC+
%dC^l^Ze:=i<0]@>b'#XL[5jbO%"Pl7WhGgQ*0$m7@so9"1CWMXC/).f1`];%XsC&Gddkm*@uN!QR\AInCI)Mr(02Bc_U]Xc-@25h
%WShr,P%<Y@P"ino%NeD#V%AL-iW'pdqOCY^&4r*jdp2J_D*2iEToO%?H`+A)P\FWiVm(-qaCu#rfI[5(E1)^7if0l**"t]uf0N7=
%c9d,R[/kIRlq(feLn&sgMKB53bUGuEX$ei0UFf6IpISUYjcGRdT[gC5O\soE>##pSd%Z4mO_ADT[pZ=Kr`opr(X..HM>Ab>2<A)N
%7#oB>"Q\?`U#lGk"$%DJa!%a702R]PdgT/=$Hf*giM_u5;Xk\l\JCTFfFfba;[fgn*27S9Wl"^**1PK/rb)HIR(gYjHt?i9-6_a)
%Pba$H.9+U);)Kl,08ei<n3plcV-0&>J<hAR>iF;#Lo7I<cY[@PEb%dcr&R?7\]hmddUb]Xj\T2qjZ(#Xfe%!fi4l!H-tqmFM5"f%
%onmI$Z6o$Y_Jq75V)Sl;M3>j4:sOSh-b>ZTX+<g!K_mNV]kA*),Ad-@SOEnF[`a\<*3j<F*u)iLlDiOPh5DJIKGtAdWN,8$!Jub-
%`?9g`).ZQ-9+p;Tq1X=i=F?NCCoNj$cXIU6Cn=+??gU2($kL8K<m4L%Y.\)Yp0A!:6V_?8pid%'<mPh&4$I4q&mojTS,qb\Y3]9L
%Pt^ZVL0R8A"O+/Nb^YfkrI]t'3i<an,Np?Cc6,<Hg;ViChS8#fk<P0$"ms?:$;PC=SQ:)=T7<f*ps:>3oi%@Aiou',U"_$!U^\G3
%eq@BI14abK4U!.(V4f2b1DAkt#':=gCmZ-,H4fd6jT@9nJRh9jR,)KWJ`L[uQ_m-O+Z,Ij[`W8QFWTS+WT#>Fl)2(33,FaZM>:^3
%hm@YK24$rK,6[\s!:"c"SVA"5G`QbUNamu?6I@3BCK2fSP#e?)?h,GOnS!Wo7*XX]Qt>t;0<tcYWd#ga!eHl46,HVA&rX11proe"
%!TuX:@A+cKJ.GqK"0gfSS8B,!fn)&J_OlR,3@ZA>kiT>aK#;Zl&jpjNV_+so"r+bI5$>fiNajA-!42giAC1_(G*2r\^W/?/QYi5)
%N/P,]&N%h")8eVl6&2X?;AP6;l3e[Xjjf8-r%'JeSe[8W3<5PGgQE;TrlpY`Og7b6rLPQLFH*ZU]GB-^#ppR`\o!p4QK/JsZ>0qK
%9hE18qiJ:I8.+[Cij^P&N=(5:S4FLqSsklS>nop5Yt'lY-J=<n:NUq'M$_$k<(-9=6'D`,]59`767Jfl=nPtPj/@#nZ?7rXLpAQM
%O'F,bhVqD?Q'u6YQoqK=(XrKJD\\rXZTC^<g<>aRUq#2mO/f:fCs?(,=tpmZB*-"%SE.:SRTBMKZ-(]<ggbqSgR:(7nSH@i7_-I;
%\SL)=kp.gJUG/%/3X.$;4Ib\f*6t%XE%+E(V^ls2NJSF1\87,C>5#gQX\;\1BAlRN.i1JXM()4?HI+%Co_+<?0uXbA/K7WcKLV0C
%)AD"J'(W*qjfLK7O'<RmDKY82?f%M\MWARFjfLK7O'?ha/loG&o]7ZnVSBl<?f$*-s0F.CB5X1K?dP.Kkdh^?V,+SWX+/pYZ__ec
%lTq.\_(<('46apmDsAFR-AA[E36W<fd8Nl[h=-2sY'Gk>em+\d":)!'P*ge?T3`5S4=pG`NehQEaBQo&9jr@u5c1R'LQSO$)K>f^
%6o:O5`qS.Nkc/qI.#uC7BbcUJ)^p_;6F4XpO,^*B*5eYT.etr7:UJR%*bpJC+dj-qe8pU5lM98IWm&SW2n'R_oqnbTFK0!&e3W!I
%)De;Dq102BZA,fu`AP6VZaob%Df..@U,TPNa'VNp>?mpr4e#XdqW?A#@l63Sp)IeX\]&\Pe[)gZTPn`8=<Ui(RMXo'TWYc67N_;>
%DE[3u>Ml1"EOcI'W+!PN8G7LCWfh0)/HV)WUl?*m9H]Le:KQsD)MA%D4S*JQ+K.g<MC"#soHf83oj"8QHK<XjWp"_Tlbk^[c)h5a
%Y`EcHGrUTl2.<-M^n2V=m(=TehX)>Cm0o566`%OGgDP)oKZIE%#Q\XG32Db2I'eVi00/=W#[%jP$%b$h6Yk&j*12^YF>e-E\1X1H
%n91d&o-cW:DOXF#L,mC)_->>Y#r^0=Mn0*Fd8Y[^cfJU[7ElFrf!P?]&km@6&^5$!'MA,$[LLk]ECK+ej2K*ba=UItq52E7m:rJM
%`R9,rP<`E,^0[(HYZT_%i&g.+cfWfD:"FLRfQrtLG&&ju1;+!7WL.>,R^7-OAE>VW:@#op:2c`nW?p0[UEuDL(Ci('`[gCJkZ1]e
%:AdcgOUu!iK!8^Z'%c'`QlQ=bIbH]7kaP&%4)Pt)3Oe`">f4\qrRpE1._qLpIS@#>7+C60d)O5\(HT46Zu%%A^1lZ<j/.qNO*dJ,
%.R>V[_mu.g>119^UW[kh02kChHfs4hX,`VbE0or4kJi&?WXEm<%u[o)#:)>=S#"W;Cm&NaKgcaG>TN'#7g*jD].MDZ/4sM-_IsjC
%=aMICf`KCjdP*LN'F"(Z%@PLhl6NV@9eJl43E=>fM#[g&G*4)'4^pT(]$>BN;WOS9IJh?"nb,dO/c(nZc]U=N;<75:C&/,ts!0C=
%*EdC0Y?mqYD2V[o:>L\9+>X6JfuP$1,&edlW[`fDMkVk^LnR3eQM;"MDY.s)N'pRFiC",5#F]R3Xhm<H3ONh/(<JFccX%JQQH!nH
%:!lIQ\4</r9Bll0QHB87,/joo`L!MiL?Gg0Qhh!c_td@?m"DkY0,CJ&fu=rne[!uY*eV5\j,W=W<?)iF_/t7(i.UTie?hdI=Q;1=
%\mVA-^8W2Ec=oI_MP$Ui6%`m#+dXe"CM(h13-MkHS??In`9Ec$_1b`P_>t>=l^2"-A7_^.`fk@H2B/ZsMF^)Uq&A'*fWS%?5C%*M
%c!H2E"HVe31Zu^dM\HYgSm-9dRNNFb&AW?crT>+(nRAP+0p8k\[Sf=mT4F5\.(uF3.:c1Q^r1dN&;J.aK&_U%koJBFoL/_MFY"c0
%-s)rYRPEP0H$go4%SA:d8V`d":f<N^N0BFqig@*Q=h3c\_O!/>s35^'TOV0K>'+ZkG3kFV?5NdUU!lRj<0,XZ[dXX_2:803[?DMM
%L:*,9/QqN\'AXQ[fIU4!D,PX/T+cl?9i=uTF0Wl&T%oO@-"5pB9+3`7[*JuhDsoZHY\YBufj!i/=[[]m!mNZiBnaX=V3*/dFEohl
%+8.h6NEliK_QLlR5#Pae7VoX^Y$/2BP<dKNZU<%c\t@B6,`I!&mY^R*3e2n2!P1dGah]P$D?a2+c_dSd5q)@;W1dWV-+G//(/8k&
%P#Aa@$U&`BBWR"gft/Q\qs;K(L8clsQ<Z$8jGhULoUN'5K%O1sS>`<$^aETt,JG^8VHNga?,Xpn.4t\r'i[P[Xst);P,t8E.*.9X
%M>Wq*3[,c=Z!hkK@IYnpXurIB4pLn;:fcj3HaAX%2pS>s0JVd"j,>s*U5Q@Ont\-":*m-OSPJg2_/n@o\WIfZ[.&_+XnecgV6>Hc
%'4#tp`N^d(+V*LZ_>G"lSG<45bT*BmNN\jB#A],aT6/VB^UFt*9>\4n3+H3'b$22tZSOTJj<Tb3RM]K8+ND?-!Pom$`2H*d+D%Fr
%h)JSYNP;iej%%eJ17Pl,%cSWrI+LifQQurpP`Z=n6]KB:4O!Jo2.T":8MnQci\'(<,Sp'N9MV3sgIAM$0@st(7a7Z\7[(]EV-lQ4
%fAdZ\3u!/qTCRVu*4#X8["^l=qf$UHCCGN7--*),@qhnLkZEXUkGO+P[qN.A`i2giON%,^D/5_1$TE:")^C3J&cR<chUWO_YU.[W
%21:"H<S`is7SZcH!'k74+?i[oR^?51G^):V=j<ZL0K)>)!>i2?R'0_WM9nMl_i'TW)K*Y6!UB%pR(^?djabHDb$9LTag@;-hZB:%
%Q.7/M.9e9kC>$6[FqI#Mio0/,Aa8%ToM]urTP_X"l;<o^&;dD]Jp[lp&qpjCC/*.n?qtNi=`GCNU2h[AjH#AP2<jc.HEr1-j0/&O
%_ScS%((o]+HgTdj@ta%]lSh8o:kn><CK]"U"JDnp#L5"\`#tfbBP`P/d3.>hq-aF;@/(a_[TFJ8YS/GXZoc=d(cL:f4.T!)fPHL2
%J"`DkNnu&:%MO7h%hrY+Aq5p.!biW?=?.$]&3ck%'2_Y\/D]VW*ft8X7?^eL&!-54T^Y_%s3AQY=Ome7UmYJ!]9glM4&#^KUlJkp
%Qk$s5UQa"#Y_arGI0_s_FCNkQA1o1$;K`BWmI,nf;hK(0Jl;`,b#+#3cs$RKINgHi7?,`EC^ZbRg@J5WVig,!+t*PT[:(gDN;qB6
%iCPG[2bMDd.0=-u$<fFn0(G=KX>h#RUZ"3Q%)k$MQcsF(nJY\:172V';[C"6jO-SO1j2"rR8^n#]/Y-HHS<\,dR]k5T[/Ucms'ge
%/K'P,S6]d@a47,V-<F1VqD)r:e8pV`g!N:/l/f#\>Ce`B*q\PG@.E?<2jro9S&u'Whp`/"c'#kT1o0Vu5$_kF\,C5o72^fnGPH1T
%mH*^oa7nJ>q_(0`ro(c<pMYb3I/3Y@DjU+bs6B"+kAO[UEta&mf\IMY+nb.cl00J]`nra*^]$%gotTJA^B3du2sKHP2uiACmcGJ`
%d]\-$bt>4L4j>qX?g'WBPKOf'%b&eGcW9,f3T&>m\Va\PW8R&VhTt*r$KRSAbYaB>O:r3S:iJ!J9KN:a'S65uo`e(nl4NlLDfFh;
%qDks]+9E+=%CgOl56s3_GY['jF@(I5OXukfC]tP!+r8tTJ_8U7J'o5:B&qs?$%C)dg`#tIS@)P17dah)9;"?VJAH;tdI#/e6Z>;P
%M\CtIE[>UG7&30pXTn@)Em7q'c!1QmejK`l[gkHq%Au;M*pJ]q1I-gJTm^B)No[$tj,Q_-Xg#HV23W6<CK:s"d7IR#lVj/LE67Cb
%n1X#Obl>j$e/1Y6HSeP1ST9l"&XOLMl"hE?2L)cq7%'66!SHt;cjB;>(DEm2+.D2nkfSEQIPN>TUr5.?,2GrGpmC2c879<RS))aE
%V@*0uU]63TiC+p+6ZWjphO.&>k\!NVQoB]qW:`:#EGOMn6gdkq,4O1LE#342L*fD2et0!*:i*EtUJEM_TF`iN`L;W825un'N$iBC
%3f7I9e0)6$Y%f'#>R"%4V#jg051_JG6+RAh'Z*M>elLmAP"^[6I$08X/99;EO4Z$j&4'ns@!j<1CQ)tF]_L1\cOrCo'6i7)Kd^f*
%eheF9&r]i33C?L/g6%'%@j2dLR##NYLr)K?fV]VnqSS49'P4"mOT%"F=k"^+WOfqbT$f'KN5cJW@O;Yefeo@l)"&:$W*-$&k!W5)
%R5uS^N3,p1(e^,E.AMdo]MqM&.X?])PNsXUp"j5OQbf[Xh\\N0oZ:-.5^9RG9=\\tmU&(V4,B@,V<3&!4p'N'!_4*?;0$.nKQMZ6
%,bdGUBS<)q)CcOk1c#oo;=*^VkWqQ#RGNg+?h![i-<_WR.k/joA;Z2/CPCsiTk6RaljI5G_3"KP*49Lf+V?G?8_2'$UKGbWHc[*k
%f2M)OIV+`RM23Nhd+p*sJR$:N=@VAU6qK2JoqHPbq#u*-*)o2DbcYrI$%>M-J;g"T!VI%B"OG]e+PH6qaV;\\.lr%qc[MebQZ7d6
%eEea#?m`Id`$131I!k'JCEOGudkOFK&5at>)L0PFRkWuj,Whe,K`2ZWb0t'bR78]Hg::igVo3t)4o(MV&pd3hl=7T^Xa/^/E`.*b
%Sk21o9(kTShWC1u_DKmZbXATqd:%WL9H/"0&tMoC4WF#q-FsgWh')W_3,8.QC\DEZOSR6"[7"*`4e+Yn-ERTe39S=OEoGt7:.I^?
%#ap%#M9re%Lb\4kVMe)I3cSRPk,K5ESd:f4N%kXoOjNI1P=Ys)EY'hMknq0J8uIaOU,.[rL`ge1-,`ZJ87i8S)TJ$+/^),G3?sM=
%7hX(dKT9'[>+K?cI(/<m?L_hoCRU-bdFJJ$;C`DHNFlYZJ0H+2nb69U@He1H#[^YM6YA^3##gs(+q?o/*j3kQ8<PqtqQf_d-)hi:
%(qGCB-\7n35U\i>GC8U4QT#$>9ZJj/+^ciMT[*8NkiemUHF0].hXu2\H+=Qk`6&I*>OuRCW@RNO]&E+kBC8K<b.qgHT`mOmXeRCd
%=n0_C8n2H`\N8Ra]mF>MPZ=--Q0";O<OCOo:3URXR<R@5J7QA3.`pHOMK%Hgg"^7],VW_g.V)fAOiCl)Cf;<X@.-u(/Bq_;O)?;l
%'lDppNe8&]c<*bfJF';UMfd\fiu)2T"-Kq.B`eqLCd%4tAChGp,j1^0^!#-IX9Y#G!5hT)^;c&+AZeVJ]Nn-\IFK`MkZfPWH9t"]
%gmB$MU0k'N?ma?k#V(/;4Om%1n;0^!knl?*Tq3`/[<es+f5iPa%4BJUQ;m:km\^s/=\9KW*#E2CbbQ]K:5KZSbggI@-I=iL)n?Dk
%0kMUn-FGe/5f4-RkBc(pXVW4N.C`qXc7($5/rua\"VN7'/U'q=#"6doJ&i2<_EQd+YZOsQV%OBJV/.lk#.U+d9O[ieoZUKZj60Gc
%ZLjc%;[,F&&Lg]OZtERKQe>J8!l9d^Pu22g%hY"],m`B%A^igHT&Hq2&gB;:PAcR1O%V16nTA0tQ&kohVLqY0JD5cDQV7Ojfl`<8
%]bR@lj,2<a\68Q&m'BGb=ZU\!rMKH_7\b!p(14P]:J?J!=l]R6^(?G,[Sm:l3dP\S;n(pV:Q\=Tb,:!$bUPM(178Gn<*I[u6&%&c
%,4%).[&%/#-H='rIH=1.LqF[:X^aXAK@+\3UH4g1D)"kk-PIpkKSQ5`WJ2giP#r`d(f`F<M]YiKXUiq=STX7@=ZFLiP2lon@]W<A
%UU"OV[8`?/`K9(Y1Z=L1]AS;bLe-_@-8o:`G^`8VG8mT6!l^!kYXg)VR:&D-T]70,eBi%aTRBFV7u$ccpF1G%.5U=GTIVId%M;nr
%eq1]BnD")-g+i%lGQ0mS?e-1LEM`JK_#Js=#.b%OFmMce8YYU,#P8)@h4qeo+AaVT`ASMAMe-&ilfXl$3t]T2l;h-NbbT;Bj.T7;
%`f9Z0=hg`%;,&us&k%u.)Tjg18F\1DinQ%dRcl@q*],p8/tjF"dc>tRa'-4>PdM85'pMcMgUUga.=%Mj$_mq:^(@lsHN`]1(F9M[
%U\To?ma[;0C\'hLWJi#S9>lZ8`Dioe<F(8!<:5g!+sps&2ln?QG(j$S"Q5pfe+Kg(L;pr/S2G`/*fXTa!a.!CatsE!jkAqpKHq"k
%;dr2U(s:7$Q9LP<YI,(kEAf8?[<hJ<m-(usfk;p+:@;-n1(F$BaZ5=E.3]oC6+sd"<`I7Kp97f,=BLG1cVeXH"NV@Ca>0m>M?Gp_
%jOR&.1uXCe>4b`^pklQY2'&,X5b@o+\GZtS]V%_X3Jgn?<'rYbEI8btX(&+UL4P__#N@F%nOh<#8]6Y.GV%#f?0*<6Sn2bAg8@d^
%P<U,tbsi]U'R2NA</`l,!L-"'*[-&4H[TkG!8&bUJsO:$PPre\2iLW;gXOEBIKu'SL[r[?-pCHS0Fi)`La>a@DJAr26jGXQdFH#t
%*#DC`Oa%"W1dY+ba8QEc&d+'ecD>/.DUMrH\S1#&>l)5V`2cYIm"37kf!V=8]]AT^X&bCMd2pUjB%Q^u'68di-;@jXkLifN'.qst
%`'?hKN57#!A=q7dLJGhCCh";_-]W:H@^2gAW^`-Q'gbT:RKWi!W6c&1AMp+mdgT>M2Y7A3pk]eU'ni=DM!:X"?SlUEM8mk0?fuVc
%[II:A]P/\/U+*$%o(8kHLFa$)^al-Xl,2@6'VX/r(A\A;.CgQO7VcXLQ.)aXO,A-X/<Up-Vo@oIam#1h$EGn>^CA4,66Kid^9e=M
%U?d%g)%,WhR>Z;N0l-=>/Si1(d>X3H(F/Q[N07u!CL\/H$l(o6]q0Q6YZ+C]W0M#%LWptJ!u^3@fP4B#<;"P!)=AF8ja]V2MVVAe
%Q02-:260TK]Lo2pJ]A;UAM0`m\/<\9;up5ZEhot-^.9K/b/LE<\ODBJREaK;>T'>-%,7dt'Z"D7jQolIBtSQIU!6S<<@-(b4bd>T
%'guN"@;9<<EMAIL>:b4gY"A-M&_W[Y[nm"4dii#b--@^U1[=]%>,Uda'SBr_M8r4"X@-X.9<`'"3D9O0']gBjVb-:Mt
%bg_2H^)P;'*WbG=78e]D6g&H4os2[%\r+&XJ-"M#":8:QZR\#g.M7PR__Q6H'GK/U2r8H&$rH4k\%V&P"S>K,BfS@7L9*k@/8B^,
%-LlIMZBo&+/L!ieXPnb.PpHq_'d8$8d?=p''Ygit?^VoVe,sk<:R878`KRZ=/\T&gD:l#pR83Z_'nWm4]kt-8YD,W*+AY_k$rP:[
%_`:(@XeK,uSk5q'a!&IMJZD)E)6(@t+03N7Tu+.p3`MV+?9l@`FaAhVg2k>,k-2X+CL%`BjU8rNb*)p[,ID!Fl"3Pk9cr=&[>.j_
%K)*Uei@QVO%AC8!]D:H/P=!gebV6ks`D;0l8s&;jp;bZAc?s@f1k(OO=<3-neh/RRgi3?A;4L;_$&@&h/BDWGlc\S4PJ$d'!c!lQ
%WkCjS`&6an.aOQ3G7$A9a_idZb%M,YF;NWYB#!$jM1nfhs0mgk5lsBI`Y&[8_3r#V"nAg\D[4#Od$t\8d\mVmq@^A.[chClfYbW%
%P=K+:l.L?lR*p`*R#ij<ZeL^7Tc^-f^qA$ba?#9t!+-cac/YD?0Ad.DP8#;N;:YlD[Gi+p)B-Z`SJ[(5A]"BZ&TKR'MqU;DZ[K3D
%9a5#g>kmL9kirF(,:cIOU3dMYmPY`tO$G3E8",;"d4#/6jf'n"+EFSdTEp8ABd0h/26Hp[ElT;E=WNS$RPN!tQ@)G&nFI>*+5qRO
%`XS%E5%Y?J$t.p47$U.H`4\(?n9:U>gP+kMP@JC2'%PsABr!,bn%pIcTFHT'C,cemaj9D8P@HBdP@,q/e:6JepY+i^<Dj#mkZcn.
%E$Z.%aH__Hldm1ofPo\'C*nb9$?LPVL2kUmUk!-?N.X-Fkjk/T?)8i6%.cosS?:+0'S2B!<%p&rER2'?<.[)2oj[dV32AUG8]#i&
%JCA/BMF^;r?6LZ_T2JS;_tp=pPA)8k^s@eb`+T>sNI#:6"]M/9oED'eM!@%GJu)lp_8,]k=PeD!M%==,f*=Y[0gV=JY$kdE0D?B,
%`$6[9M;C`6Q,O@jLP^7b4To0q9q4hRc[=-m5iJpYN@Zlu&YXOV]$la.H@:q\G)<@qZm[tE@dmh8,ti'5P'&M_8=Dj*I)i-S?n4Z7
%nX<B_7t"_`=Vn)/I$kO9fOGXD(_IRD;Mf3P3^F9OhH#:K.T,6CkjHXCpLjhB)qgFLp((2@Y<dq01ldYdP+AnXh&:$$5+2]KRU5Is
%lbPDo[;,3o;MV;<+.L;\R"tF/EuQ0b.m!G?,8k6BOU*E\C:G=S9M-DEj(K<E9VN02K`sdP5>P'O\!AL2^du@g?.VQlYfA#/0JH@H
%]Vp+@9N7lS6un6VTSMUd;HLjf4i$ZO]gE;@`c*\=M\/[Pa`(eB_HFr:YKhKH<4b>U7:\el4]:AL_KL>t1at&!)<$+QV,_k5cM,]g
%K)>tcT`i_CjYrojEsFG`$YqbN&h5Ep.qrQB*J21o:m2&QZmn#[@NVlsE^tA;'TeZ$M<0b,Q8nP=`^#&?dW.IF78#!6J#jPR)_LbG
%$_2tFT^lV3Kd1/[]@.Y(E$kH\!WkZ_X'h8&BIO<)&.1sfCP#[S&A1Vm29*EQ;#+/B&5(@\gpEITi[n&Kn'nk>pkN!C.lOhBPaF>N
%[>8oK1ggI--p1u#RiCnZ>Sp3<4DjQ/.Yr=_%NBaJ?OePL:@\%R;HAbZ?Htn=EUsO]\"b7I%QfEtYhRRdrQ/Kth@mI]>81FH=2-U(
%E=0%kF&4Q(R>1[uM]3:`]E[_s_fNum%Rk@LL$OgSL64`/?'mSc8D&kCpH64L8IY<lMkk;Tk\U8'h-T$81.$ac0#Uj5'/t7Z@SN%9
%-8HY5oj/.iX:R\Xh3a"4E_T:T8n%r9bmM:(@<n9q%cZ:0`Xp/RoK7th8t,>DTXP1m=1!3!pA.$"Xo].e`8jVU,.XA@#PA1?&J'7a
%cg2<TfN^NA"\jU4#$pS%D@W.(4O''GJ+WW;Ysh1fZ/%Pa^sI9^N?Ll8=i"<:oPW&b@[/0IaQ?>EQLQ&1*,:.2:>!`Z>9Br!#@ADP
%Tt2m[NS46B>sC\@27f6VJti8POfWan'Ya_`eSGmgdG8G]2X+"sSK%WDl8%I?pS'-4kD2;l"eBF0%DcaP5Lk'U;-sfG6*8qPaP2p:
%PN6'C&_@(6:C^gn4De?"EL]'@Sl&3LKG(!;nB-WQfRf\,[M>F0#9R>OeCXa[a6^f&L^u/FDIYMuPnODG2<Z"a.eFpj8%1l#,0Kjp
%U-rR);TRdj@Ch1W3!9ab%s">5Q=^`'pE2q-$_J9*D)`9Sen>L?,1AF59o+t1/JQ=6+ekpL9N5qq(CB4,5DO4D`*,i&&'IN;Fp9un
%IV5`XoepE^@c]mWOg545jLSbld/&UC^"FiLA'ge`3eqdt;E,!19-(/C,5THW9jPVJXG:Q*Y@sn)Bao.tig;:oB5Q>,nRaah=^&Si
%<,>qVonnMS>Q,21b?$F3:k1(gFmdlA@;F\c6%?Pl9%R`H#>ZWZ'5!CiB%POXmcJo"c;=R&6.h?6'H0p6?NlsS#D_PS3bJs5auF%I
%X4purX>3Y,X%Z@`_A>6<XFk1a$B`kYkU#X1"%IeAf)U<gX=Du"E'^l"j76*q#K]dd\um"b4d&Z!P)+oo'SO\LBp,#>FP0$UCZZik
%Y%S=imlhf1L^MOSDO\4K=D1MhKaBQ6Lq"GO[$1d`Ll1f2<QRrA((]WkH3rlko"tK-/S<7gN1W.B\4iS+O%*poV`)`#l4WQ)D=iim
%X>."K6qTp=O=UuPm\C2##VTY59R9uDA7;2^DI&KG-"[]KLiN(F]A@cL3Aq'GX3BU,bU4Fr"]]K`j.HX@0oNuh1$__1P6UTjT:`rq
%fsNeo&CoL_c*ZtR#Ue*dq[QZGef\1F\7-G@`/*V^:_$NqGSR/RNS()l)44I>8sKUW-T$R[`+f$#-6apH928V##lAYZi:M)*pi&4=
%:X!9AYYoj:7cti]):S-"'R`!/fGfY>6c2hI4m7s=*OLeL4:u[3-,NnfCWat;(B*4:2pWQFf(!WYUr5De-lCnDig-7/M_isc'-%F/
%di6`jTU.Zi7hh\()'YO%aE0uY,.bEm?L#c"C@UU7`$3l&7-lLb*ccm\1,Xg4c"X#"m1qc^q_Yi7q638i"[bQ!N?ccP%7/EhLmZ'/
%6Pk0+(fST`l)VpA5[GccM<"?Mq*D`m\ks([6^O[Lph_05:W/i<>NkD<*T[pE%N1Q<7WK9Lj#Ius`4jmY@=_).F<:`?5X`R5T]a.m
%@740A5!KtqNchA<kb0V^.:bQ.ZZLK0&9$q^>X4+FdF/n%$=aN'juEn_)5LLoZKd!/`$JChp6J247h;E#3a@5tcpLQSLB=4,&6\/V
%$+$7j$@1AqRk0qeE/ntH*ft=Y]L@0de]N]IFoO.3h2AQ`7'7IW/Vb8i,Wk1<!8'jg>/t2GTcc,"'Ra!i@aPQG_L/"G^0m48;mL<[
%4i@NX]oE0G4kN\&4ZI@MHK;2QnZiOB\q4[iT[5B_1WJ.R-o/`3q[rhZ`D<3U<T=]@bdM;;S/^<CMF5Q(RRPMDZ!>Cb5bMMWU1_q;
%LDkQZgcFSJFI8`BLbc6d9NQ+1,T?e=Jen74KG[h8+AYF=8R>ir;fYKiL6REiUO_UM!Uu!$/#bQR6I8l#Ct%T"$/>o8f8^pV_HJ(!
%JFJN1$`<1M(8hD$.^k*odVbh6=*jK.`?77CU4=R0Bal[4(hIQSUPG?O9s"V.-7YAUimC`"*YD9lgGB*[Oul:TOuXd"lssKBj;*qe
%rhTiki%`]06X/(H>&(.D3tB%R6nH(1`eT^I&"IGZ<tMoEk-gitVbCTfhSrZKotbq-&X%7;2Du(%0m,5m/CkQCk>69R\[&j?N8s?G
%!A(=6lME($3&CcO/8ib6NKehnj\NWk?[9!eqH?^^R^Cc2KTKjjVQ$&J_&YM:Y/Z)j935*XjH@m@"+8pQ,cW`,NMW=t1+bj]i?3R9
%TNO=CP>IWFUMIB2e+,@(Z!5GH'9l*4_RntXT-VfSU*\[`Kc!ag\3f2E?\gBDP2'HM9(Y,<Tbo><4suIOoQ5FcSh@jQ9!gk%aL0N?
%]G`_p(,hl+.rSk7a\2RfC$?8U?K0BGp,;L4P'ao.AE9j*,-kBAc%om"#!"d/3eD19.pboQ6A56^8c"m!5WE-B^)[aIY=uDs$=9`u
%W9YhMMs9%1?A7L:MM-!cas:\aF,+U=@iG\u-/o)<BHJD[Pl'V=?mVt+3K6l#2"/"Z1Ppq;4H^XGH3bF)8A`sQ4`k9KV:jqFl;njd
%]*+YN/V(&3&'$F)N:WJBM`i-\#jJ!b#>KjNSE5eO-_NcHQ"mUcA=#1d"nBTXA.5DM/)6Y)^i>aHOiUeU2bCm!QjE5nNFY`.5QEfg
%LmFQO[;gPLQmW)"&YDG2@#R90+W@+BOanLIQIfBJ4;'.A*&LDfagL>$_onFXYcte#C?GS+R$Y<&q_83[-eJtkZ&7H_ZA^e3e0&4)
%%1EHIO=N(1;Rbl[Ck2TERnD3"X_rT.V2MmB%]j[TRY>ft\)88@,'uH5>6'1`HEINaJM<ZG1cFVb'2,Xeb1(&qQ?nQ[HUo(Ma31&2
%BI9_YKsFY\WIt8<C2)-Od81pmh<Q4]7oI6g^hMs34.A#&.G[;,<2hIb;CK"CU(j*%IL=L79UaI;9cFuS$^/WJg"-eW'?=*E):32.
%BRuZk_'25bH-9?Q,_R$$Wi1,n\=CN38G6cH&O822I&j4;<U_9RTW(f?Hcj=(Rp`I?[KlnJ9uK<i.!T_YMd+l/8qaSWm?l/_VdMrn
%WY"ff,/>Hs+JXrs&4h]=WZH(Ooii>MLicNrj=0<L3iNT_etEJ*eDbp'k*D^Ib/EJ<Jl+DKOlTT?.^T>OPPDbhiJ:XT48\^lej<@<
%Ni7cfK/8UJKqs:.2;?e1$k^C_93n0Y%$!@KmN`PaPASDe1()PCc&1^P!N/\-.;1A`/.R-MH7IJ*TYW('Iou!?Su^QpS&@e[k;4HN
%cBOK^#-Cmf0C8G5P;BY?^A8iAJQ#Gm4e,Rs"*\U.L`\p@9].067nSF%PKDiVV+`%E%4eTf*6[4#:oB:7"b#`mPYMdaJ-)qp;DAdH
%&B6ddG=H?3g9Y26M?FZ6_):AAj!3Z:AC*\:`*dRXNsa*E]Fl(fCS;XdMdj%-$!A2faKrohLjB,t3UB7::Po^R)6@f4N!N,[T0"(A
%@FCWuBi(LF"A`#d()SeSGMqg]C&'lZEsBu9d5A7jf3KZZjH*D;?W3ZC,[@o7,De(:oc=4d*0JkbMaF[/&ogE0F`+*nq7o>kc=sq-
%<-)O0.^Zd!RM$:u%KS8@pI@:n;HlIKec`WfN;43H<M<aLp9(X#ejD!EJ1L`dSJu#-o"N,FGSD-<'W7R=r](5Z$g3BbOu';8`rJF`
%\BU.q8WC]23,fa<T61/4_)75j+rfS;=Y:'STJ?I8Cbs,5.0HY+>I&%g'@8(1CTIbk7o=ApfZ'YZ1aGN!3bDs<C+u^NoU$3j;>452
%$eXO5a@-8P'LYV9E/%dHS\tdF\"q\#Ag$>E2]c!RqH1d4JLDD28P@$3\>=g_&=O+e-@-&L0-Z8q/&mHANNoJ,-DZJV*:R"/[T0L8
%&_/l""^+8s_\drk)T&etJ6<21'J>B!EKZG4"90hFnai@B]K1H\l0!@aX2<dh6`Q=8DSJsf^ZTR!;p_j:;mJCPd+1XuV)SR!&b?SF
%+p!b$=FSDW,"^]T*[qqBQP:Y#dQAXok^c1lUJDR$6cMkd'+8F3+ehS5A5Q>=Cu\\ONH;uD@=[rZbp:<IP%&qR1$B=!2kCm$\4u1E
%\]DQ'fO/M@)1#T?W6Ru]+ffQNG^mg=La]e")*UHs2[aX@:Ua+8COTo=`g.hY`FN50OXj2)ED!Q3;JJHM>=8uHYGCe#X#X(;ZJ^sN
%%k\u@(7AEl.rLln&5aX%&hl"\*<7`Q9/M;^-!EX4&m$@o,%RNdBnmk!-q`c6ZQrQ^V[nbi,%udg@(&0+`_M&:3']CRnU"WMgGn+4
%*e)q%p,:3eqOn>WL]cNBr(mZ]:D1-_bT<gHE1E]6K:/m_M:fL!:+EW2[:Rr32SC#D0tRc5V?!qeH1!2kOt/('V^IQCc-;jp,aiga
%3#aa*?um/(SZ':&*eZut3jOQ=9%3DQ\i&GLJAu*EA*d\M21e,pedpG-L`Bid/G[7G5^[GhM;#r*WFX&%5,FH;+s>PQnDGdBSh1G*
%a>afS/&6+%10YK=M&#fO9%m$ZWgZ;HU&k3iFZOrjUSsU$P346BYD!g'T<^\ppo"Ic;2suX%AG+:/*BcdZ]Wt\Ob7NX%@im=VSEeZ
%2MX;BO@JT*%ZUUc7rs[AO'4G<ca?--C54lQ;sg<td+JG#dhL9=-0"@u+:!$<@@FQ?ThPILRLm"jr9BC9P'BLY@X@.@8J5rs2d%jc
%dm,-oRbXKtR?%[1=%1d6!-JKMR/lA%mc^\6'H,3W,8SK*5*(/57g!<bnKJ9T;A_E/$]km;[jR"=bP7IR]h&8rI0r"l3T>Coa!$ht
%+:Ye$=d_-Z'+P^c]G<iX!JAD?Rg/OZUC5NPeU_cEfcuVQ?p-n[?l?S_TnfWc#Ea^W*G[o^1S<.dW[+*)B!fGZY-2_r$#3WKMM07Z
%^XWl+]DQF_MqJ56K_MgIZ"Co&fAKtXAcg]iM(oEj1dQA3e&qtA0=B<fToI5iN?e!J&IqGQUq0G<j:e(f65o%RlumHGpjaO*9C)]G
%0DGQg1]<Q>,/D&u;.;[pZ>PUS%)19:U(\e!`,5bu_1`p]&k9'*YV6<.o#0"J']7X4-<0AlM@^1o/a(`$jV2-F@f7Z;>-F&Ie<Rt&
%7<@+OMY)?ChcI`[VBD=a5,KeGKa.kb>W!seFq+GQ0Xnkb'IY&/%1?$,Mu*<5D%PJa7+>'4?s[HfZ.GMe+Bl/FTIsYV9%DTC94-eA
%+<cP-"G2#kKCCmJ+3-$Kp-qBU,:Dt=AF=)Aa^K?oE!J?I$F!<c6]_(qn6HafQimecg4KRAd[D]W&bTNf;ii`+9WZ^,V]@$]UW@7T
%JEs7rH%)"e)&S[I0O"X9DN"Qj%/O3t%i/32bbV3jD]==T4R`KDK&.cuT+Z*Y6milq5Xb&M7mXd]`(EqQUZ[AB>qdn'!:EQ4<OI6!
%RLln9c]9)e*_Mc7*bu(91:*ZAC#[&Y@ptk3+M`J@/-(l)G_TU/TJMn-"cZc556&%-?I10p(Gt!&"F'P3d9Nf/=jBe3@hKftE61(P
%*l)_$GYW(oLsmMoJEUgjc*/)+ESZ5W$RYBg8CX5IKJt-dpS.BHM`%:V5Mb`[.0Duf&0&%mAu04s%N\@1=FHV0e12<DItc#P8YG@&
%QmD`cJXjd"nkqYD)AdcP-Y/&9?AjU@$qitr?"Z3OOt)8'6L4J&.F]Z^NX'V447E>B+q<aT#;gfHDTtYp(`1e2YTA[)V$tPb+b!oV
%,>b)a#g6j#)Br0[D(mm<F(X*A`PJQb!Ja>.m\$gufSo,?i61;<(QZ]r/$mRantYL,*\%Qq\#u-TS>T7!9HT-u2@5-'SW5h]+<i[E
%il63X;htgu><;"V)I/gP8ba-*"]u[[7ME6?("*i`Q'>nY:H&>EP3MmLV\ooj2tZr+ED5W^if$,(FB.:F-:sbAFX4NZ.<Q)G<to%,
%SO,847jEn9<-[,oGD3>M&l9XBQdEkbAOD*PV#7j-JN)PAl@>4oF9k]\;IXGOaFROf6r+Y*cD&ui)FtS``hnD3$**B^!H$/3VfBK\
%0r%f7*Sgr#%Ru4n.h;J%2dpCPWZ/GnZ3]iU\=:ki_758rqef$&Z\tg2V-Q#l(!?6oBB\1D`PIKIs,1Q*9;?j82EnG/>(k.BO+X[Y
%.4[8'a;P8+dt,%t;FP^A*=#fLQ&I5Kk%pHP[Xc/S-=e_<K2,7$0A(jC'%FcR)QkG=Ld=Wo'!XtF"t!*]&TkN-KG1=j.?,KsC/-WD
%e-+;9B5]\.J54WO@0n*R$IT[C3W'Ff'3]ZgQsWE7pKaFhEDhW))mkp0@M&2^!MD!lRQ6;i*DZkr@:oSJ]V?K8G&>A^ab-Q>!Kg!O
%lj47]R(U4`+#A'IoU[0f8:(W.=Jb#Mm][M&VJM@jb6Sd'5s)"Xo1H!Icm-Dt>Mcu;k6P3GL69^E45c'\3/`SP3)(41H6RrFVC@t'
%^F0B<ZunfHf,_iPR5$+HeDm(WZTYsFc:p7,#S[d]a!K8H'-_0"-Yr-^Z$-hU0B?hme2SNnL+hiu4%u/am"fPZ4&G,HMlIs1<8egf
%\&=ScU/++1RMFp942lZ'C+NH4hscCH/QaM.U-gmYQ/RP%B^:b]d;.05jA;p^_=DuX\3UV8*-[C2T^rkk3b_TIKr1N4`#N$SL(L+C
%L,Rgl_("2e5_t)R11nq<DdA%,L41G;!CIK2E6hR^%_@]*cS[Dmiac7%f_+31d+bZj#<jiP?X9145OSU=e*NX6F%<;Y5t.5KqIo4H
%+ceaS'V?qPi(7<!V0D\iJF(;.6ie>6[6tT=SgMH#+)>@m,q'D]2QOo2I)n>sOp5S?@+PfiN2G>Z>l4q$`2>6*I63]YE5>ju!uBke
%/.oTQ/ldiP,=[Y+<[%!q<S&i6lm`tLMiSJ,N';>C5_2"8lrSgJo_:B<K;E&:[Mp,W)5%-P?I</)fV.rl_!kd<am4hZa2AD5'AJL:
%3-WA9&aE8E1=)IlNSPA6^l05+3p^1Y%Rs7tgJX:3GD),]hK)2Y(AtJh6dRcjTVBa&E_tUFr/>+R+X3D?d\b'Ql,D@+Z<B,<;c_p,
%d7-8nL:Yjk="C&1-,2kf2jhsYG<[Jf7ZkW\=<#&#b*tBCGuQMep/_=XKl+mo`3BMM:P)'u@1n\k7qk84H5[ft!ndnj/UQhI68N#,
%Fj;ZEZ_TpK=#;%5_^SV`[u)D_4n'<<*5'K?`G+M/ZmTj:T40Mh/t7t:lpr9.%TjIY(;O.DJ>oK]GK'u**dtAS97U:/oi9o4X9nB.
%<:m5:0tb:.HAb`7Q9cgaLduUkO2+Fo>5X)_*W[HV4F_ZJ$qu?!20P_da]8/P!$:+_:C%^[@Q%/o5pJK&/Z;5+l;p#Y36@mE<ODUd
%<1CRp"5[DP+AX,0r2T)0BQ#[8X;"]VMY7BU!_Gb8bEIJ>4R7/g\HK"e5W-V0@;6Fi/]I3;1*EP-P6,6_-I%H\7F%&b9aJlcN(cLs
%k]tm0?sIh)d#m;lhakPTTH4B?H)u_kAu<_'N:OYcD%1=iDF[\r79DJ;]+4-LO"S;>_^JHQMjY-pbJ]'PTjX;8F,!OcX7Ym"j/qdt
%J0d!*[AHQk4X6A_INR@P*Jdcg;DM<KYr=*5crG2K*E^We\Wulk"1GIlr[(,KS<`E]b*"?>_&QlogTaEiIQ?p:&Q(?V=\BC7Y+>Wr
%98>U]jWO"0%,idaK4lrs2c08tM*/R>22VYo>r>qg(Jd=%Ldol2A&@P^"$;APm9%1fi9D8l%ReoR-2+;AK*UYNbj#8"i'CK,Lpk%C
%SfY:6S:\JfA2qN;1YukH-R>MXJ;YT$U`RS/E88oe$9XIVkRPO]*i^m"bMAK/=`Hd9>r)801T)k&*!CUnVVGr/>N[e!.Ql>!_Ka=W
%71I;_Kn2kLUP8!P0aTI:Y=)*0SM399QEhQ8[,l#c?s,VETZ%D^O<u<n>6"j0XluHP5b$d/7dqX@#R4FIC+JA<X^\Uq.es(sS'\CQ
%lCm3iY,dhh17hef]5E/NUW_X.o;,":]J:g[`_kd+7ghi3U,M:eoe^6Un*ql,+R;LTW#!kqkT^PP9E_4!QTMt_]*GFYK3@t0F<FTt
%L:jJqC!&0h]B+uQi\g9R_ch9k?OMYAUlh?GGA%SdZn\IL>L1LA<5=sq"?Q-Y-F#uH;6/a)"A>20G%4;OB9MV<&Lt4)(8l<fkeMT%
%m.[d&(XJK\6:>Kp)UGTCP>.M*ds5I6r.HY-`]&EDV$9A^e*r+Md.0U!)3FuTo&WQB:C)fSQ6AU0R"V3b-Q^?[U"V:oGF5.O'O@bo
%2\P('?o7&(\J4Wf'-32H(5+rk.DUNcKffeLX_fQu5Z]4r7`!h58OJSVe`sVV:<EMAIL>,I2X$<d9UiuVH!dX(Bu)PiBg
%=rQQG_G<&l]\jUC(`;X:ZQKHlV_H2PM=d7A:G#ee^_l=ZH>"X_QnUlt)]h0cV%D&6i'q]4mQo3Z;DngPVoM'2@>(?c5<[D(6=YFY
%l%Ba9K-u@V+_!eu+Ih)e")":!f_:71/?F%5;,b#(T#%%ThVg#FL2YkIiQrs^1\d>LK_QE-DukD*1XS*$1(O\j<Q11IgP/)&[$5K<
%.!(<r*3Q='2HjDD,"A_6U\E_'X(BYo'.i.,Lu=40ea\G4*j6iE;IJBQ1Uh86nQ+3Ha6-W\MH'6SB/0G-=VLfsO!-j\AduqtLe*20
%nm\AhDsp*`Qj'JnjB963/1uQdPBZO2F?FA2>`hK_KM%f150tm<Pt!dHLbk$XFbZ9$iSrXDJ82<7@T^Ltbbtl485!cHJ'qgF$/FB2
%Xr\Slj/*#j&eWM@)P!Ka`5uA^?=G**o4=nRl[</pFk@F/&gabKb?ZOUg?3%O5Z\-rYZVCE?NnX4!?798YJl;]+nQqdC$3.2K9cVu
%KsVMP8eJH)&UQDB%P=B9g!"hWR#"jGFN]uoYr*?Ni=6Q92bSOR&<ZLG`===)f$2gl>(nORdPm)[Dk;Q@TZ&+<kb&mc2uBk>%`tJ<
%[1_/S;IE_QWXCtJ540d(BQ]HJ7SXGXc*7*,c+pYo)f2%9&$r+@V*[_'5)-@H]6QTi;e6u.aEdJPCWVDC"CI1_&.Iaabd&tG2_6Nm
%DOi*Z-e!45&rOO=0mX,`Os7aP4fgQ*nZ"KeA-cHAQts+)P"ilo`NnkfdSkK@->P!Ee1WYt&*%k+?A"[udk/n3e&pp@:ut4-AQUW?
%W^'QE"p%2QEGOm=;6I_dg-^_u(K'Nu2-E+ZT)8m_*s->C,+5UR$'/;cEL1YIFYKX@C6@_j_2cV!7j/@9E2P&=9">:j^%)`KCG=^/
%".-:4l-A8HWiS5H\>Fi\6oXX&l^*\ckD^`rF,d@75;M'j3[3R<^/E)!C>Vfs)_C0)m6>)")NI#jGgpR4j(`meBXso"\<uL#A/MO.
%W1*E72%q1pfrSA`<N[u8`^^7?eoHH^j#KLYf<i!u+6ki.d:0D>=J`8*&57]87anFgR-BECUrG8:<L%k+.Ho0B'A7=dkT-'fm&iLj
%=\.g[X_'Af!<E-VlmQ\R;LMI4Q@[7GNIujk$>fG$/aYHapfAml1dM<`VMIhu$qW+\nc4C^J]=kdI3O!MnY:Ck++BD<2$ARO=/aJN
%RorrF'.SUdCEmL>'5H5*:X#+Af7YW_,))"4Z`O>-.qT*QdVICHkhqlc<(VtoFjhN_qA8=H$]D//R*N'S5+r4"04$TFZT9gIV7G4Y
%&N*[`!ZcrMWT,_)i<L588Em3F>/;DY?<)WkYKhN;"],E+FBNAC_1Fn-%W--a8_"89]F:V2:'k>E&8+mQl#r`$MB$gZT[<a1H^_nn
%BhJ"qnj<oI,jST.M#q!a68(0\WgLFelPAm@2E*LCdB#4=fcjqAP^h@PHr(Hu2Ld-!"&Y2-2XMCQ4mct"o+oh76_C\5YK=dbB6uoL
%32n]85Z3C@?:tf/U(-u6d1^QL2!'\]IasV*5,FbK<&$)#O"H'W]b"$RM2F5o3O*+;(Ik\1d1M%;"C+8XB>l_^$,U,$co7@cWLul"
%^$"=3;p_"A/Z42pn3lUa=nT:]e3aD[*^QT,9uYP]ToKa_WgtjDX=3nn26<cN)+$_m!+:86F=lfX%)cn+'U,ukA;$ij.WTA@=G;RA
%!0[PYa+Y!>E"!T<;L?Js9;uOLj-gD]L<r&?>s,:T,BR&H/#'YCQiMG?j\H_)Vbc,P&!4]/`%5,T#meo&cuj'<$%N%q>YmSF"sfSt
%r(RRsTWF:kDbt+9VX)eJ$^bgR?VgpH)^5b2FKK2p*/fCT8!F#[*[L2WWdt/MB,!mI\ol3(GD%pUJe_SLM>@lqCAOrSnMT(N+R(cj
%2pa?,/\?n-FSlMC_<3me/^gd0mYMPsYf\Ig]2ZA0g9T(8B/_^P+AQt.Z4M?eH5k(>oEmDTHM]J2(f2fq\AqA3*DlMt-Yk1///DW,
%6#WnIF`nU$C4>JG>*EFJc'C\$?RmOMH'n5(<n('/db.%<hue_SZ-kL'#:<ZV`l($QDhO^Yd1R^]LujBMf>P*tK0_ZFGciTSBO1*M
%?5k&fH'mM@p%Yg6huKuB$l1+7"dED=6UV8^PpHj$,:^VJ6*:!/fa7^.28>a?-\GitA.Eac1A"Y9*ahs1-</6?`[XG3;G]7(+rIWF
%ciE;^&eTmR)a2K-+ldd-[nHrFg5)F`&UcpZ9"dAO;1b.\pl[D;@aXcUWgN@E6a/R!X9gaR&TU)C^6$.i4nd)oDqP,^B/j[T[i1Q+
%e%N9bQ32+V[6RMbMuD$(0%Fjf=C5N7>"$A'(:+W:i;<f/aMe"4hHHQ(##fL(<DUd&ZF"VNYD8.%QE[/\Fb'6B>VZKe>lWZY8#2&j
%`JB+XCb7G:-D)'KX$^G`204<oB,J,ma)NYaSg*Ur#d34!?Nj8FC0N-Gb^`'H1Ig`uODmo!>HqY1S5!;;Kr%-f6je/AU*<9cJLSc`
%1JCq&E/#u=Zi\s6)GK2M/\'GsMlmb-3`HPX6-Vu!817R&<69Vc4ZIQlE,E"7QOp2:,s5L08Ahpr0Sj]d4&hHN\mGnlc;X,!64d(I
%nJZ&K78dU1J?AM^TbZKq>',X46.CH)d?dc`>TRe%#]P6f'g!jt'G>Ot`DTG*A"j*<$U!S1._p3/A>5%5/('CX&oK8`.1J/4))"[8
%2l_6n?`(1R5^*>Zk%I4^iZed&#am1mZ(_S5[^\iq_03oB9q"X,LXBtJ![#"dUBc2tO>>-u>(PAK:>YIUA&1-:B2VO=(nG?[P\=p[
%/rj'II9>AA4ZTC4OG4EG'QIM1EsE^+/'Qi`?dk_5?&Ojo3I9&q*J)VV6]h4_Y4lU%DiJSLTG^Ok70dOrbCIVqT)MUGYg!f3@MR7O
%"_(6n/'29E`!90F#'ul9>DBhPn.3.FV-tiiAqnA,Vd7SH4pKL/Fia\RD$H\KB=gT4AtoBb&$KZ+;t]d>-'9WQQZJDgUPWXn*Xea4
%3f*?ERW/d-a3fJLCI/2?4q\q!]qg^'T"QXBQXEi\GD&94S4m(k<rW8=a&PMJ+gUtoj2ccL,Qj`Lq9XdHYCmjqpmHmH/Qlf7r+ak?
%2%Cqq.@m'bWa(]=ns-</$,tW7,&2l,Jl,39#Ge%N*Nup;=purTTW8V?h*F7i\WCU3Jsf0K,[/gZ#2.-PPIY"8qdo;sBEo7qq=C!l
%V*-RFMM[-=Wd3V;\*8Dl\072N&c5]P_6MOl$)kdR@o17%4B[pgimDJ[.aZ\"+9@q6+c^MUQ@]LC#RJkgF^2;.WI^0WanW7uBI")\
%:dfT)RNmuZ\e*-S<:(u,;q=*]cIad+i`"2]M$FIE*'<H'U">MKVh6TK.=)QUi'saj5tj;aMqkGklKiBPG,QW.)9HFmiDoIuO4g#!
%0jgmHCAtH?P'O@k^_W%i8/r?#pOk4eC>&IE?3DKDB'3esXY,:&6TeZEoltY!o#LkPM,!<:"lM9O;,)<5(67U9BJO^0dr^LKr?PRG
%:%BdRJGfcirCM-d48(IGY^Wod19UXYd=[kfZf4j8*/9!B&-hL;[L$U&L-1;MCi)$?^K+<%$/[2/^a]jjN`._',fh^?@5kdd+Bh(2
%K"MZ+80aS8Pt@!EM=M@&NP*>5n!c['oR0=ImS&0s.KPb1^_E#6JXf+%g%$@JZis.^8H6/P2:EPnLhUi4,$qYI?51?lXba3sk7LS?
%Z=p<s,N;@E$BS8`#DsB4@g,LE(79sa2?Bq4@n4LCaN)n,&Y\K5V#d>2R7Beg2Ma;p:KMP:3(?Me#c=;3`pcFLe[";d9+3['"YC"s
%&^\fEq..?P2`q<`F7+jX8&^d-3c?Z:9HtR5U/'%2LGID5[7Fj2O3V1`WI%SJ;o_/G#^St6D,(r4&Q_>074YSNq'M.nn1&-cEg*>Q
%M51trfe&B!_N)lf1npfscj=`ul:cW/a/[g+S<-SjQ@u5mL5Z>j$W8Q.JOAsfBMV.']k/QNd`Bn:81r,D73G!PR\g0H/5h^NiGjnm
%?%Z3,JE*tg7#+eJUGL@\p#*VPiEN(q,-B<2c-ei`+2tB[W`'Eo$Qp"E[Cd;_Xb(7QYF9OLDcr\F>Dnf4MIJ<kCbW.23b@B6#\b@,
%:S#t(po#%/D9&kT=;!/.P+r^g<V=+f;S8SGQ,e>GhVtIa7Dos2!T](4_Vu1LUgg$NZBl!W0fM>>)&6]6U>eKNO:TURN?E8-bR&S4
%Q!*eL&2@lbQsd/`P_Uc';k(a@-kk1'.?klo#3R`-$CmO<UUKN,J/gPLI1Zo[SeL.W%s*K9X97,BN2";W<_D%QjO,U_2*fk.;%#b:
%WW:LfBmJd*8r,7)Br"ah6D<'tD-aAW82L#eA^d(nP7:-7&n`N:4X\0hEu&K2i0GUEF*ZJ="tTl+c]P7l@V?7^SKU<k;(\27CaBaL
%M"FT;UOsg\j(ZFsVhek:d)MB99GWI_nL;@OEuE)03D4bSWR7a]XB!2*b+Nt*?@^JjZ;l/(>dXX+)VY/DOR^YBb@qU]M26aeF0MV$
%FMR17ap)>[,h"c9788eD?%ulY0Uu.:Td(j!8V&-AH\#9t#Y[P%giF*";0uu4*hb0gC".kI3.E68@bVEO6S#i9X`q:Fk5Ag:gB#Zp
%B[t%(B4LQDm5L"KkcR_#]f#_KM#eOn1u,)A/VsXV_V!4?f($g9e:]bT</ViB6tt'.pln8RRTrIcY1V7If20@6NHAZj8&e1[!:F)^
%-k[WZJ4[Zd/#>:9(q.?+L2@nBY=8:e3:U[`ji5?9-"&n"FYGF4A@IBU7E5>Rg,97g=4D7fK>>aQIq1np>,ERe1khmT:#+BRkk1Gn
%8bKM5*gEWGO<Rk,/mHaQI;U$gA9^CD/W%H(!=GYLFQE)D)6<rEXXn+<-5\#(K1-/4+I+).Q`2;^TgG'"e*7B/9!u0FnBfrGCJ/iV
%>7ZT6Z,jR]8=54X's4J&`;`Vp=0\OI2VW)ULEL10VAk3SCe$ia6Q]/&(lZXa.)nq;ItriK)jMd=V>s#</32&-JHBjJldDOD7-Te)
%\-+K='NIqmVi$V5,S;`2%P3U1+CR!gnsEQ8/UQQ\1@gc2H=2_S_"2fOYrc?'(h3rZRh&!;6AK!TQ88GAKgKOL+@h3>?K4\beM6B<
%*K[.<'!RM<n&&[D2WWY$-)/gm8SRET_+3-;RkTS/I)A]oHj'Os_B,1R=I+2,6!+!+S447C#g=F&ZjOt:'C[p51,0Q"'O,7habISR
%a*a\6$Up_.b#=4+4'MrJYhOl7@E;-lB=E[!lk+4C4?a,C0oM&*;@.d!_sn#BDkACkf,3=5c0!YqeH)%2eW$p9M73O3>S#[1d%i-D
%D?DIES,,lRb_d2Lh$TA]<(nlE/(#NaT89KOH:_eUNft3Yo*0$q!ZZcA(+5J>_*[;d<qF$S1SHd_@#0BjMX9?"ABV(=P.+sH:m<L\
%ZDB38%4e,W2!RjJ?suAJV)AAQAnWD/pn<5[AK"VS4CY]H7]g0;qZH2:4p/5*UpMJXF9FGj8g[Me!f@p/V2,o=N].di`h-!e$puhP
%p6o\A<$aFQpY0:Fe(@J<Fb971>i"/uW)Yh"jtTmn:9,u;UH6FC-u,=en.Sqh>M<F'4^60EXmsSH'<CZ(J0lgLUQ/b#74GPdi_+h\
%-/$ZI]_:@taP`R(BdR9<Tbp_/=t?)\;6=\1Th/E#_DRRtUWP[X=(/m&X5/se[>+lg<;:(@<;d`X+KN2/3tTnJRc12(6ZS[sih-jT
%6':i!8sk-:BrHp.Kt/A_8]`82LKGqb]n0.GW'os/MO1[S=r<33$@CUk.Q.DS<hdkUV,Q0j7Vl+fKdPQ>`j#q":TsOB:((bZ"f4:p
%Nn;X'@)ho[HqC`-+uD,>`Va7s12s<4ii,VJ/@CXq.i(\8XSa\(6phqmR5C4,G#M&(Mp@!.jTk;.`XLq4Td%"Q/2ChPb<'i0F>E?.
%YOGko4)+Gd@M]_eTaP4__#C:idu?^ibgf=9F6;,_4dO+]9d32599`PU@IjPY]??NFe#ikU3#`:0ke9I/GU[\i/mKdj>,go55HbSo
%I!J6tDtSbQ)i*t2WlQZ>\2FX<Tu-0spYb`OgBN!/M&fc^h#WprT5bU/S9RoOo5:$/FlTHVMPB43(W[U-`\rS=^gFU_YfBo@cHOXm
%7jjh"F]8YP=Ct(R4_ElN%'IuoW>)Ut+\HV".)X;cM&>$$\*PA(8*Bu"%(%aCN"[BB@oJK]&>5O;5>bXe,Y$7j$`Nc3dEnt&GFJ1W
%9u`m+M1(8H/q(J3$pC!iQ8HEWH:.)SQi^Ts@+G@ud"O<oqUX"Wof`O02b#q9p]X9f5Ilgo2kL!AlK`KMRD(S*_+OdOEb1RY[Rii@
%."s$WTB:6uBI8-\H&1a19]kimd[9;BY:/[N.8+pn&T(Gf6UpJY*[DmeO/h-3R>2a^6%'4VUrJiGg/46*S_jq=2(_1J1dj$&2tCo:
%fYBh+'c_iQi=YA]n($G%hK[r"@1#D4[;&s!N\n@#/5qDJc8^%HInN]BD4h7i&=8P/MPm<qGF]GGrp]_khkipA.2$O##UGsHog"82
%d.XCJ`GI2Ic!(>eLFu^kZ6W%%Vh2"t8@hJS]UC6>;ojbYc0tjk=5%DRjl7mc[:]t:5:oN_HKr(f,e]U3]VKUN%uAQm!74Y@iS,3s
%X3Cb7KM-irld-f=LSH7f^:$Dc&/X5X4$8po!s"0iaOXUYf7%XP;Z)D2F0^jbTh.Jn:GP2*+:(Gg:Uf*mb3TNJM%pVc*;YJ`Eg#Bo
%Ndf6>i<m'S-?nMeF.(fa[?JtNl$sct)-+r]DBn)+^s0k.O(_c$1P*,;QjNVT(=#p\KCsbOG`2(P`c,C?.Bl4rT4&tNVO<FaO6e\Q
%15XXgg`DL)J1IL$SPc-E&Q1bU[kJJ,]U%tf!>WR+r&MKg99($P%I%u0kdi^W3c9dsn5cQFHaYH+7=gh>+lnsCdt!pP(%'Ircs0E6
%d4+;,$9]U9Ag&M]/sbW\f"k)RA[mL(AKP$''Ii,$),n%-SoOu^A#S4)o\^^LX4FK7V,1QOD?(i&TSE(Fj-%HUq0SZdHD(B;Tc8_]
%Sa-_\E.C?c-R2<aDjoA[kVtUuMUg-,D5h8-@aW5oPLHE$$8sf`ks]$q0"s*S.Gfg@7p7&b@<a+YV8_lbEhEK*O+>_s<)OV@.?>Pn
%#_D'86t)?-`p.?dJVVoU&V8c46O0Rsk])KJLTOX]SLJK!<6Poii+0s^.+$VpA5T>Arj,j3lIj]:(4iL^5r`Y12iGpQW%1g3)Qo@,
%^A[93_77H\"p8X95biCN#DnS5N,9NJ=Yp/ubJCKU&b$Qj6]fACTEdh>9?(ba!88?BLgcGoM*<+%&BdSJFN+oC9L)^$*@<t9F&0%.
%L_Utf*jk?)6qRa(J2Ce^[#t+03S$S;,A*BkJ4G=:]O;fm8Mt75EaoRHNttSa;mWE<`-5-Qa[RMYcGo^@;!_'RQg#)JcWZ$a!<FDh
%J/%==5A*hOUIL05.emQTOWDE3c9qeA&^ajaUH1'W$f!n_U+@^.Ll=/][V.#b#X])3O]bAsd&;Wc?l.EkVB,`,0u]gU[k*WuC+M,X
%=G2l2m?gSSn@ic?A(@<NB#N@^I8*AF#u"MiLfM6Rj]i?i8Cj>Q>Fgef8,+bY'_B!]F-?7&>9$s)p^%jB8&3lX?o+LSN@5$<?Y'jY
%5Z0@L2Y(j^T-i_a`#jX>e"R(0muU6MPg(AU@pYa7@B(C*7Ns&Sf;qH#g*b5VS00]nIjsq1V[=<UR?fV(YMj4UbNeR9JWej5O^=c(
%$pq$$RU5[i=4I?J2LBf,#Nc^n*gY;CEH";98;8KWoRcln9//Kg<'TE,q.rZ3m=:t[,XZ&&L6+X9Sftd=-Xp="q1Ll=KM\3r_ER3u
%`iP:bb?/&+Y6;J40mIM?2bQ3\/+SB&QDO`aM0_uhX:YaR,\"eT8.d:5;0`cdD&L.V#^sl^?t76[gmM2oU<,lcL;jSo`=Hb),S3o<
%2B^QT^0,)%\"RPj@1H5`Pl`R4<4@BL94MSYF+S93-[3AL<oc+T`:ZQ/PW/ql[9?02d7n%g^m?N99k9Gt!YSN$8<KGU\m)UB9.,=s
%&i5>7N,OSu.`;?MPV&*:=o9J[D\YgF3Ea!LjPr6524<XZ).RRh%mra_>'ZR\.`"`PEaue;c60`gAHL5M.2[,!"u+M=1n#!p9_'J9
%bD*7ObWc6pJgO`a*K=muAmmLFEbK?t(D1:j3N.,5#udY!n[nkVWEM1NNb[X6s4/_'GsjGiX%Ll8h&uVT72BYO`h>dP7FS-VGXTjb
%%,3[6%mpV@>R\LEdDCTpIaC^11L77V2"b6q9J*,7+nXSU(Hj=o:k]2hZ=8e_ALsOE"WL<mS/$_kY6L=W17'!2(7u^#LSb7JSIdS%
%9EeU?OWeI0b6_>%=f-rn!QcE1L)HZU^X^6A(pr!U]*CLg5b5u_ZoER^pP/.iB4MldLH]JX#,DQ-U5o)LgfS?H2UNL!#.TuA'N43)
%S$!3ZD>*&4GtSWg6YWV[k9Ubso'<jEUeCk3eL!;N64C^4l"oN#pXi%s12#f<DSaGo!ns]\W?QGn.8Yq6.Rbt(W'*UJ09@EJTQ`s<
%^14B'#&>u%bpk$6KErR22T3jV!'O'OU?ND]*Kcdn1&Ip=fEScK.=#lGqN5j:kiu/Ff*hhp=I#aI<3]:J=CD?>#E.1[HjC?5/9ol1
%BX]>*261?@#.!A94kTZXcG?NF2#m(aoO13S3@/TTMu'C1j!S]9:P\d<][`\A^t4h)2(:[8AfUM$8m>3gBuRuWUBM-kmpIi<M"oaS
%841',cAN_s>'"@gFH""!2j_EaG,<EMAIL>&-)r60QiRG?sP\2S/)o_(+Mbh$!OO%MMRX`CT]QQ.d1u*I[%hXC\=oME(^WYhsfW%
%/X'S%6c9h-VcXDBRUs=#?eQ(,X8j_-G/=)A%ZQ#Ii%0\GJo_m^Hm%s#*3MlC$H1&+\.p(S&@?=IRX3n)9REE<<=d8Bok9kY[l!o8
%=Q#=QccH<bLB+mA(6,\+GdHb0-/3VsrWhaTCR*<1&6bPSZ./s4Ll!-=,cgEqH>o%B/jqR%k7K]7''c!"2o.,.LY3O_V_=eLh[m5P
%W$MeV`YV_^60$$,TYd*6iN1N;G:%XBI2Y9YdBsB/9s0Kp72^IWL/fD:$pDkqYieS8@Pct'H9dqFE&^`!15*Cber"45R;I0C!'&Q@
%U+ruD)qltcC!$fFp,1@G>J3.c;-]C9(cO$.nl!>0C.V9-nu+l;*T4.6VOXHV,_'Zq;rLnK#3iAaN$EnPBNWD&n3%q=@lb4<>`(da
%&?ZVX%C[7.^muPq)U,"?H&A]?(*(R"$3]sCoD?dTQ/%*![%b?-o*@Bug$4fI.8H-cI`$Is9_/sZj/0U>e2MQr@SiT-b1=INaHaZI
%hqujPWeK+[0UgBL'Ln@]LbGJ4>=Tmg'//6T/;Xm9/Jq62\Meb'c#H/71XRmB,=4J?_=,G1_bEY+<f8U'UEIiM9SQdlm@lccR7*hJ
%O+/6h=[Ld)#T2RY?@r`lZShnoV$9dk99V,'LmYbU1,Q+nkfM<fobJ.\LT:t&2:_*a(sFa2SX;l8JsVHL>>,ic;.C_GA[&=4"p.*T
%&.U&j4.`'C\RbhqJ+US+Bda=#0@3(B+=IQ3jmY&4Z'sXJ%.'7qp,KMR"?dkN0::Fo,]a<Vi?_m.I)*/Vfg$*@'$/\C7R>RtWS7gq
%)Lsmcb'`#!rb)fSLm0_S/UCFf"O.X]&Ke*l]N`KUj>cJQ[L622;7MH72Lqd$fIi+Hbebo8S3GE:m7oN318&Y>^*AKi5Ua@\!KFKJ
%<-(UXi]BuU29?dsi]DaW.uY2cY7o^$Tp@kZJk=:IBBOfOm1A'?7V+''&f0WJH9O#MUR#R=HoER>'9pm/lIbg)"bWL#'#8Su\uVKZ
%iXd\<e]!mfE@WL+!.UN=m5.QMe9P4%Zls<@Y$a30DsLhr=aX[pVphlV\<@dk4Spl"'N+KB^kW=IDIpVp`I>=Qdc+MAb<DA3Is]d.
%$XkOSA:Y9-._I**/PhrUgGjYH?J=?lB[)QE2i/l<AiW0b$t1<H7IkFW@po_+_"`.4>8ZsFTq]_$0b:5W`.HV*1]<8.E.o&*-bcH"
%pr)dr#Q;m4PQXjHe$XQAl\%ZO"_]g$LMk`q_6QD"k\!(F?lj-?Y2]Wo]06Uh,PIr9!j':B*"7UU<'*JO4m7,q[PlZI@@QgFkV_;J
%1.\R9kXr0*3g5n^,'eb*b@[U"BDN_!9pX*="]kTUN2i1oJ[Bhl7aN!\;-A_um6,EG-6//GHUn?\@?k;o(rKGnZ3h^*N.Fm0NS^<O
%Tfc5B&aTr!^Dh!MVF6ei"%e(S==r$<KbdsWK>6H_&CudM8_G&XF-Zt1r;"B^LuGscAcPWh2rV!81^=u6B6[AXW2"be&`FXM`E:ES
%h`#9SF[H..(<;,^&F\%'/@O@k`]@Ki]<dKmNftVD=AF\WSI0`HJ-d)nA(VGpSJh#G@R2,NKPg$<p=9RV'\Fu73TubX3?)'ii9LMh
%68NfS^]7Z(b`@WE8LglC)LJ^(F%hCEGZ2RqEZJE&ap%l$+P*"O(Y%LeU$OJ\.P[g"@V:65B#U4<XV6$ifDS\hJRW(\R%qb/^d2EV
%\CR1D+XF1i'eEtinKc-P*d)o@3frJ+ZMV.,Ugg<!jgPV4BSID2MV9s?heg[*,.a3SP7Pu7cgE*Rh'c@*`eeJA,^2":)EnR^6Lrq8
%aiF@M:AYEOG*oHsJJNLsY>r8N_"]$]J-?[1T`^W#5/H]/dVeIEG4XWgmUX9)*t7(Tg7.4:k0KnrgD`U&&Ta\_O,ST<_ZR06$BG(o
%3%SfL/X/pt2r8Q4lir(qfc7eD#hGflU]r\4"@jtPA]?H[gt"=Q1nDW<X4.n4S;'n]%$4(\,ZoccLpBiQN+!]A_Z+;jcPhpN<>6rd
%-*+(7?n,>L\Ouq<l-m>9:0XKIRd$W1h;#HY'd/_TWrn;[r%q"X(O-L3'HR$\$me20$4.[P&VZu*#P!ZW+Yepj5i_!Ieu)M*9o-eT
%=dmBPR$[8-hmRu;AWc3%R$KCU$:$!@UWXc!hN-qYe2SBQO>/oCY"T$3rT*>%^efaK)_[n^f4G%6J-b+Sn4F,.FP$-c"1P.`EAUuh
%@kCoUeJS#@&B</kC#Z4f(dil!=(SO5[!.7f),#Yeb#QMf0mUQt3!\6GjkZNe@$"iXqMnh\%Sa8gOI?C7[PB%Y+-1Zl!!E4J&>PmE
%8AQ\hVA_hW?D?Ep3hrAe4a&MS"/o&f0iuP7rohJmSP2Bg%k]fPrm3$ZkImV'-.ZYX35[.JrM\oL;/^[d?6XQq6-h5sCLX;RjD-n@
%>!e<FV5@4N,-U?_ZB0<+e9KFAo]-:ti#5MTW>8QcR>!F;eYT@pFI&l(,><+Jr'/Sim6;oh"=YHW^Q+HiKJb]:L/W+BhD4e&%3,Oo
%mKKjCSj(X%3-eOe4_p;dRiF`t1.$_OkRAQ7/!rek*9Ul%/gn(g!SEFWF=[>u"[nWq(eeYAfq:$E#ek'"h;J7'(p_C5ABcpll=^bL
%.o7W(mJo?S+\sE#!VeoJLC2kfC7[.cdV2WJU^.Pg<.<BBL_d*^g]2`q+^"uu.$"1'cr%D!R^8&:JOZ7XAVc/"@*'-U%5]7SX0\:k
%*4lb>iLM4pWA?q1boLAi-!:I\#Gcnf&eac\P(mKS;*C,U$C6'0oH7!?&OgbgLmB.r'hk'0lCLUf1AVTr=SZ;)H6X?.,$D$h,)-/:
%_h+Y+T[#?Z]WJGZKcsYVZ/RC?S_SP\-8V]BMi#9'`e%MoIjRj&Ib[uIie7[D:Zri^\_?&9^.p@I(7Kn,ZS4IA7k;.Ro:1Mj7'%*H
%i=h@pdi^>ueq6:"nKSTbel<b^62^aL*3=_qDS#lsnLQ21C)9X1Y/"7$aL1>C.f(D(<ON>-Kr_Y%'BB6QFr6`=4tV*7o[YDn2\D7Q
%!9:Uq'EIb%#:WYsC35:\8-'kn7-sUiB$I_1U/h[/;p>mrR[%#V`(VKR;Prgd&Hh1J"6gsU$WI[K7YV['i.Fo-6pD:V%2d\>`eb^]
%/#5T*U1Z/(K4<MNaaHla7pCdS<D9RdJ=)5AlFA3.]SUX6cpEJCH@o3t5["ZH)s[2I&eoI"?t%Z=,>?<^R*R"qP(ojT$q\"4&Co<)
%1K0W\oVQ4g[^BcRP_/rt^F#ok;:-<15Y-ag(g<B6Eel>SbbPlAmZ3cJLf8%LDrc"=e1&f[kien<K+g:EFNCuSEd8ghSpa6[C`VS1
%p8P3;&\r]:.eFX=30!g`.=iF-oP?ml',^8(!*NnWB!Zi!brn,T_<QW@NJap_fHmSR6M(shAC!d-58BQ9>U`P'>+M6*F3kTNlDA#T
%6:!THW$]RfX&3Y3q(Qdo<3IXn-i:`,P*M6N*?b&U:jBQ$<88^4_*l#X0BKV3;=)%9f(bddn1e]L*YGHDTRi"MNT!l(<"/L>k-$rB
%=MFn25Rq]D=I&4HEYVR_ou1FfG9Z$9"p5lkh!nJY0=$9c&I83`niBMVR79D=@tq6h5*i2]Mh8m##sk)-5Rliu5U`O"Q_99Obb6*[
%Ep&X\+Y0h5nn3]l-\abE_uS#!H?E+-PCZ+D/<VUg4-RL4Fe0<LhD6%#>YO)kDBW*!e3Rd+kMq\q9Oh,+7S[TRI'Q%^H*ID84QHtU
%JlB+-qFj,[pcS%<3Q]A'BAeRK!R+IS'-TOH<>HF2)*\Xqn-jueJ3jPn&/QGpBISie"u'E+C(TmYB#iQR,EQPE:%SJjL0oedBDSmt
%a"$7-BkRdt37/[ul)*E]$ZXQ)PgqVtM^Dmhl+`MRSd]sJK%5spXj27Vki'>pk%jGX%&Fh1-"[uKphA7P1'/\W?K[+L(\Z'$5;?jT
%0qnG=)ro<'>JXo-.cOd00G^EbXkXuV\0n$#F<XoVi%h=IZ-$SN2.f5.)-E_T'RGcd.*$.T6ZGS.Me).POHlnR&(+<WH&_9uO`Q/$
%Xa/,YKWlISfnO2Iar;mV7.72\laiL,->B\m'#T5f;T0-%2k%.GpVca%i+BIJG\RXc!EX0s%`hf;&I4sO]WkV$Y!UEL#>ORXC"DNX
%'??0Hc4[BL`46Y-CX\E2^cEBPl#nHn;H8mW^n"[+K#R3lXMbK"pe8%]$JQJWkhra/<^W>>A#igLk[?KW&/-<.8M1>)Z^/<jACT.&
%(75uaOHG\poKIQDFM&[cM`N'HZtY7l8s67JjCOjV:V8.AV&C-rH:C>HNcR0;H:>Ai-mV1pQsf@#,Q-Ugcf:eR</3=+Q_YN]%`eXQ
%QeJ)l@>mm(qOp2i`5$J<pnKM_-p/24Zj:=M2%0c#Iq!rJG?I>R;<)&_CX@D/?W%q7Zk;u+0rla[Bg5a.MLErX%$+FJ'.XB`=9)i'
%"\l8@_b2*Z8CNA5QFlg*.e4j\rJG9H;c]C4qU*g4XM#nRb6=ZBokVo_QV%oB#4[QtfS3=la-6JGT6S_JI0/#@F7VO[U/KDpG>&Bh
%V6i9,&YmI\PMYrmfQA_Gf"@fdNikZ<R`Ve8Omlmn\>G-I>BY*;)-m%!R?$J+CF0TCZR.F>*Fc[fdUc5K,]kGI'C44gA*HBIarc-&
%A/`XD27Ki$<?EG^LR?_eoL%$%0.&g=P>:Z'CnBAA2pf7"]IX5CY+[@l/Zk*n,Y9J+jp^%u)nd/#DON&Q543lI\6Ci31^e<N9eeA*
%$@4E=2IWi/kU%AngX&Zu7YN,IN:Q%aDP[LN,4Wbu$iu9p6>G4R#m\$G*L.-Bgpm!qY%8B,Te;Mi6"B]5N"&8KY&/T*YT"jg]i9;:
%@tN95aV-QIdhOgBTJdqeTXi054]4[H*9:S8_eA`+/GTS]OD2\8C"1M'oF^[6lX2\,U"L)V6ejJt4M5Tj<(kDe):QGJ&FIT2!LEZa
%H&f_6_m<o:OL6J`h4,[D4/PSsDAB<i*FKL[EcuQVp#oAmc$t*T`[Poil"m*ifGQ:Sl.ZUfO3BajZ:El(Y\Hmhm+*(JXN.a>[DAS/
%@W3:`rb)FEE7t)\m/Jp,#\l@?q=m?o-nu+I7Ef3^8Wl$k-fJ</J[<q&4U"!C,c?CjnfSW@OqPri&N<3<%9ge;W3+YnqAcL_G[s#p
%;:?LS<maC7)sbrRj6I7salEZ1-6S!u&a<%UJphYX8h[-&$Cj7+ApdA2LdsfL:II#M:DC=DCSRA!=/3%q.8g7n1lF6)!!3'6/l-nI
%*dW>VDN@J]]OK6s*fn-l7ksK_N[7P8(">-:U1t`-9f>Ka+$qbEg#c\gVF5_odII!D6/EkUq_.dl^1e^c?4su:-Ge@T=k\j;Od^pa
%IahIc3!BmuBkkh;4G*0ME1;:1i3J(U"CO+Wdp]-\g.e_h3W\557ouYuXcPR8ob:s+gE>aO7L.*G;MA(?Tog??.I"t9=D%0n5Q+/<
%qPSg,qW`RDp[=`&rp0:N05k0HEVJng\%b/>^V@:j;<er6J%E;GV#SoYhqX`-?bAR3o$Kcg9(&=$lAe'+KUAsWs63>9d3&A/nUJ:L
%Jc=:$>lOTGL%bIB3r7+Ks7sFtkk4M)YPtb"[(4Q%Wj2Ju!CX<SM]u,r2T_VaDU93F>9sgTFJGJkkSsKUd'.u.r7CM.Ksj+CNp@!)
%Y6Q@2'ga]%^O')X0SNJ+K$Y`%<DR<ioEUe@^,i#'-D![Y,X,.sdDBV*4LRp.7EP<Co]'*_7F<[\E$:Ci8YA5)E(*nrSgIL'+jn-*
%/Q8ZeM:.cTc5'l1@O,'3;%bF@CNl!iQE#=j*W4\[VrBBb.%MBF6sP`]iFa`1ROWSi(8;R.A0U1nT1]]ZJ)lld)DR_8fYteS`(9m:
%Kh-1i,W7F*EgaEl-PS;+lQg6Ve7n.So>)\J24U&D8?O#KVmhhfb=H)53`o,h?&/V[9lCY?X&\#3U/",TrBdDPOQfYYLl%Y%Yn45I
%:.i_R$8IbEj;^<6*1NfuM+&]PC#dj1-l&JE\?J*p<g9[ul[`P+QnFFJ'0@X99]X&S1:?4>DQ3G*g-ZUCR->ooL)qauP_iXk6!HDB
%^h:K0@*]>3I;8Nsa7_&P!.ZeDdIt-_"/QT5&'J90h2Tac'lG0Ua_d/'o1o!Gj\e07hTHb+i7&-SH'#WNUTQtJC?Ru0Hb]5M&tdY4
%oH!+iP:.E>U8LS(c[$Z*OQ8$hPo^c\2\i0%f5PsB:H;?O>XAsZjGB2$C!Ca[L0<`)(RLs'0BNQf9'&kr7]M']`(EA.NZfrdZ7(.K
%IX)sDZpI%hWQM=e=$>I!XQW@36]m;ElO\-'cS;q=P78.!Ru(iVU@agMoU,>K,s=p30Ip,XD(.TOOsM#A`iuO8P8iS=%quR>ojYL?
%mLd*tGUAj2>Ej#XK[2?7UIJ%`%Y`o8R3'^B&4e2I>R]h^F:oW3.h:88fnO(\R_K8?dG)nU"^U^L9Hsk&ZAjE.\M,$DNXEl"TT)<R
%fM*H^'&!"k-]1V*r&I@A@j9[[A8tfIg4LXKL)CeC2*(pp'p4k9\JIr`)8oQ[a2!r0n`&8J@]>?tA7.qC-K=c:E+ocoB4@_D"PZR>
%Y9;;\Z^.ld9c-0_*'6/)qPFsT<l^&]Q_JT$a=P9\S+[t6,G.*M>Z"s1Zitbt.9<e^4mI\f[$QA;,SI(I8uBfU'^R*m4m&t&nt_7b
%5_tedBM4QRZ'RHRb2oh\=@+#E,/FYmgelQd0>m(8YBst]4BHlcE_Bu!31cFm$u$qSI6bm>fE**KEg18KjM#;"YDln;-70Tt(!Ts=
%9ce=!qA;^53E@g*<*\%p_$B@Y1F.h@ao.u&/q\D/SRAcR+5T\p$#LViB@Wfu4;s>F"jC9)#H0#]Mt)a5)^u34h(j5Rn>@h`@.4dP
%U9@/kV^K6#OIi#Y7d>M<lp<6q>,HhZV?K%U"QS_=Xl4S(ZGGnUjc>6/K8EhM8mZ.C\!"Xsn=%Cdd24Em]O2q5EB'TB*ioj:S0raZ
%.9&bR&KdRB@YTD*aq:?.05enBW\t7A=f+ic:+"QAPn@Sm*&nj&ererp1cnk(PV4C>$)uL%pe'(>86Hor,)cAo4YWPS$-j2gT7l[<
%bEn'ne:O%(D?]0l,mq_274)P@Aa'@7@$,m'VI2lU&m;qK,h>h>89,f??77m/LffEV>;'d;)"r?Sq>%8,br-"olcll0r,>=<"<S'O
%P!C;DI1H7np_Rt)]^U1jp1!d=K0q6ZJ?j(TAC^iUKlSuSlpW,^j\askNJ0ncZ@\I$oq9u96J"ejM)JOAiVmBNc!Wrh:k.@*6Ul<T
%dka]A6sV/kPu!cLQXruWZj6r4LR.!.<Q1G8@UBhL#K/6f-;"\+OjT(A;RS0uP-GNMl^M0Z*/RhjCmCJaNf<UJYiJ)jPq#)L6fPf'
%j/PN,VCkD4/)J7)HRUF/iC$/8nP]%$*$dg<2r1)+KJp@m:/6Y4kQ.)ll)YVq,0hAQD<=!VoJpqiaXd]gbO\r.UJs],,/k)u'D,MZ
%J[+T6jJ6[s2VGiVFqnS#REMnVIN\n7"'>#&&\1df:4dX)aJd)e[AntTb8ufrSs$gm%#isfGS<'0eRl'k"YTtD3YT\iJqQ'8Iq?m%
%jOpjl5WYgEWV4[bX+A%%<QF0A?<A\PjiKk^.jg%)JZt*Z-B6mFcT-EW-o?M+O+4UB/j<.SM-Gu>@V;s+J[l+,&Y^iq=^;AA(@pDe
%H*>:5*+ZEV.S!#?`%sb[0qh#T"#DZ<8ZpmMcX7F0J-P6&R1)n8#ReBS>:2N83I\A,khNGA3Mu8ON1aZ'n%Mm42"0O."M3l?j#GE_
%=R6/?VK:/kG*>Bpps!<dQfqKr$jF?rCm#D@78f'/=*dDZ$m]o`3T2`7o"7tK5XjWT(+=rkegV<s2SDm:\K!7c;%Pf\'s1UD[W"JT
%3ZDq+AVXO/D^8CFb&/ug^@SZ'&;qH`,Vk*"hAkdRAP$uLr^nq<\]E1'?q"Jj=eo1-D:^jOV4ONs]3HbTku+W_hYnA*&A,D1O#&M$
%Lb798ls_+`)a8LpeG.0t>dX+F\&_Ak6fk44IP+%uRD,b)'/iM+Lch))hkZ1-d/p,<lD/U./40(D`$=^PMtG7S/X2rP/Q,N:UOkS8
%2EZuqCbPE+]<+[Al.$0HJk6TqTJ.T7Fog`-`a)*AqkEsK6,*t#6_6qcXJip^DqQ^5`!?GMGrni+JB+l6fd:gl/;<:<bKe%kU(/_I
%&Odii)Zu(ZQ<IT1NqhWh0W$:k?9t/c(5Klm,,[7d?Z[;:lS?SYE0@b)6kZ$b[Z[tcbudWJ'L5`_S5PX_AIRXJWdGp&=TGF8e18sL
%l<'@0MRN*I<O6AlqXH0DYCtJXF&Xr+*6pP[es7dl%K]'gnTA0I+BD,:S?/HIF0T!$72_1ofLk9/2/_(nI9!JXQ]quOF<\dQiDud+
%UQd@Q@WZ#_*gkSjAibYln%gd(@!77*BuUOc..AIRP>(Zq[K*Wi='9nbJ5oo8GuQd(SD1<GI*<qu9K&Su83qG@qTM9=lN/0L;MC63
%p_894a]MQbTn"lGBDA!<g5UQQ.Z,eOAt^#lhU<2e6(<^_?cO>j=&%b.a8XF!oYB7Q5NgP?7IOaq;$J?RqqT)05'^GH=E@:-at7(C
%'REf&R`80L?$m_Wd(hC@7PPA8G\JtU/2@U`b*'oL66t]3`.Ig)l)ft$40nWNiN*`m6r9fi1!9*fZ`#FsGEVM8pnH-1dKpm"7#/cZ
%QT(&S^kcS1V4\KI_f8A(Ql819<(C7DD#7X5lY/CO*/DN-g8Ucs)_Zu+M-^?EDOI723OR-B".pC,9<D)-14&,CE8KqP,^IbI?m<57
%[0P5hR1>\fKb$$cG.Hl`k`@Za=Z;CGHQ[;ZA>OM+G:/`;M9p7PV:V#<Cs9h:#(GK9@tdc474`ic$+VgI))E&unMAS`HC2EOeZO!/
%rRfLr[0O-;#uPUu=1fVA;\;s7oUr';MCFMph:;k2Q61>p7_>%P4k#;:(S2IDl=]fBmp-U`'Y;pnKV+J)?n)b8O85/79Ta""Xt,9%
%+HlT?%i7Om(,Nsn;C/adK'u^3%#psbaejEa#q;XoKl'BaGhgP@',f,[)`r%maPsGSJK&)7;*id$S&[?bU1N#I0_?J?/R@5O%pBM'
%Q'%>J,1ZB0B1mRoK*b0O1lJ0`:Sp(K$dfZW$l9!^2CW3r+ZL77#jXG8k=@C;'q=KR1fY",cmdkpZ7C)FP8L,&R92%'oONh*LPe`d
%S9V.(/%]qM(t_!aOu9kQEg&[&SG0l96`Q35j8nj_&*-k)E)nl\Ms'q#g=R-[[<N26D+\VZ-#PE1'6,e)G"EF6cT)TT8T,/I5XXd\
%<o=gHObo#oDs"AWWY/_SW>.S#>1SDk].h>lZY0)i+H@SNLtP9.%h$8Y2BmSTR0_&!R/BME<H4CS!]5sm1=Tdt'"O\N6+0d%=[XF5
%$fHo116_4`L#0`^F"Bd"0`lNX=V;o/V7`:6+?De!lI>.AGVtX<jm]&KgL:d_@aC4pCF^9;e!&?j#%Wdqp:s,dA:n&nq2&kJG^HZn
%ic("bX(^#hna+2gB+e6&?_EGK/^Dp7O^lRV0JorR>a<A`X:UlRR].,op1]J:Za*`B"(N]//Qnd7#[\G%%sh<O!@9MWM]J>Oc6`**
%G.C('^%`n%LZV<ep"m3lmnsO_fo]?S!/W;A.@2hRag=28,ausmN;sZ(G-,.*QDmk%K7K=Fd>NTL-YGD:8Y6Pe3`QcB&W`jVKnrTG
%@2c#Ig0kdr^!*V"1ShK[<.@5jKE&3oXkCcV*,m?SP@@Qd(JJNiE]"`FQuL&bW_->51n'G"DE\YTRQ_ofFAWuu8@&kkGU\I4bTBP#
%R+t\Q9I`0/92VRPN:2ViVVcF3%dDY%VlYqZ,p>%.-533q#()XQ4GAVC-CVth&S@;"1Am:H\^ibS]ilU_=*%cUD0B'mU2?b+#J9sP
%"dPA,Y2$udWRNLfDkR3mQ7RFDRGQlpe]Z+C-.S^F&_t/5M)GZ\Q%oRT13LX"@MkK(&X$>9+%S1<RQX@;]7^KrP$]t#&rHs'WO81B
%gBb*[MD:bq&d\NO;N>[(I?B!Ifj.QJ]G,@[H+5N*5SXiU%a.Rp<@^.DVoBt6L>`p$iA;4+(_O025u1\FDXM\d^H<)ZF,l?YB>]ie
%;8<+tmEP+g/oU/2DuOj+XE4qNRfDKI5Pa\jr,:H65CWDMs7WM:X/2khro@GQn"YA<rmt5d3W8@4lO3^h(RO=ZQa\ndG<^K5T0>@5
%VnZPFh(8SN]F=;tnsajt(N9?LkiYUVcgU;I*k"*/p@@MAh8Ki4#Y4pcl^ReAO7ZfR5C@[ks7fCH\Y&ildD((>FF*PVbsm1C]6^BW
%^;#&Ds6VVe-p!8j/Ha@C5Mt_II!]l/Vf2$*bQ\Nc24ZUgq<No`r,oKHnAC,4r33j`I/`H85JR'_gU"*^LSo1gHGs-fdC(BCqosUs
%SDSg9qsNV>1<(tbT0#6cSDGsgqY-K&qmS(&\'b'8l_^t'4RrF,h)Ff*/oJrf5.irIWt-h@FhX+Ih;!I)IsHPC*!`+5o9S/0nabsS
%c":n$hAV;T7*6$9m$k>j(VdPnpj_auD-b%u*jjhUjQJ"@G]J!EDr4j&H-suX>LNm#T$,Bjlee8jTD`?O!Lt+4A2_aCgqRdc+(gZP
%J,Bl,R)]+(jkn5+>i'jQCMpo!\3%=;]j%`rUZo4Ig6];;m`mKbmI'>QD_KF<]\LiXJ)fY)fl^&/H1Tf+gRLddqL(B7^&IHnmH+;X
%lgfm%_8=?M#OjC]\9N)efg1nX\]Ggj\_W^k^8Q):TZ[4CW,chEc5-6FlF46hD\("G?79kFX=OT^DARE*'g2,]b8k]8K.coUjTGfo
%mN5Pt9.rl'>;_h^dH-D+$de%e9k*ki\M#T7EcLd6[qDm_^]1PZFSi$*a@jWImL+7e=,<5^mpH+Si\-X21nI]>cJ3eOoD7V7h/b7"
%hl$it;[W</ZZg?F?Cou#nbh`s>Adbegg72CLp+;/'%b?9c-u<2Mf7(Ul`p':lh8Sm[hnf-\IEn5f71h^gWNghh25:q]8Q9)=(c`l
%r9JR92]"Dg2WjO*[O,mqTARcXc.i-%O.+u$agU?@,22)E]=U"7k8.KsT3\QRk\`<9f"A.VXe@Gto@p>#]79eYgZK=:%0>[JF`Wdj
%#G1oR9nR?"n(p3O?iCNDIFl!nU.6j9kJQWU?RqH%^UC'Nk>ek@Ff!rugPkf?rAJ5Ghqqbsgn1`*$N9nO%HC*0peI);n"nD&i`lPc
%)=c!:.3E90q&#a>DXYf`kr^'Njk)F#Yi8g\o5hp(Gr)DH%VmtPNbdbg[l9("qs`B'2s0%BHQUi.WBY<`9qC*GhLBm#EPKMe]N6cH
%966DK`(Rl:Dr]C0rU8&bT=)-J*ln4YJj79'q_@h^RiD3nMn;2Zcq<`&['Tb+^!-HMUn]n;q>=?8PWqS34?&b38,;3\Ze9JJi2SB^
%r?._rgY8b^mOg-kCP>"6OLO`mni$EH'rf&oB<NuGX]Z5mgt%e+^:ZL2j3-1E4nGM'R]VsphIL6"foI48%e>h*S%!V!I=$<`qkk0i
%e./K%#<'$^D!WH%YEtJS[O*`k^qBY1g;=;SLL$0X[HlFkKth2Kme7L&1d9Fnhr?-;<SObD(ZUV5^Mhg(/&^C0c0kUDNMO<B9R9-`
%p"tqe^U"ld0+ZC#!u--s_C0j'mFfb/`RU:k<u:&cFNMQfg"'$t_[=`+a3X5E:UW-qk@1OTf4f*e`HH;T?A'0&MR2]!E6L6kNn*"!
%TPGm\0<`VR0&M,%F`r4RI;s?.S$VSWo#YlAps7?_5Q/To+5SsUF,eh)L-'ehGFc?OT.Sm$E0BXCHM>YkS(dl2\/uSXELT)YKQ8\p
%o=h!.g\bp=?L7Mg?^&%5QZsT&o]c=bod_B\dU@=UX:.REF*Vo9/n4!YHQ.9Sp&3PSc\XHI-hRE)DWMIqQJ7jk>;;[eW(te[qkCIP
%el8e)D_B=9`0E!%o_EECf7XB)9t*!ZS(jDI\**,Ur8%%XpV\&qPF<G/CKc#&Z$X.g*(a=NCDn_[fQihQn_`p45J0j_mC+sp]*m2(
%=3U.+lc]1K^T<lm2;Qsm>bNP$Y0iDMG?P?$F*DuEk$@(\>Lb:MDD&+o0sI:"WY)lD@(,*=Ld]o&"mu467A?lObeBGtp,E[reMcdj
%`Ag8[lrtaX$SE?BrfTTFf"a02>qF-e*SQjXk7ERB`4d4#;>snn>G2u%8M&#B&;4)([b[!53IaW*-u1090<`WYN(*::!3mrQ%cOR3
%[hfd;ErM52;:\EQ?[1A?a.fZhMd=*_0/XO,r$B=VXI7;Hh"i,A9='[6266HA,Ik`OpN"$bo2\;)*Hkol^a\ti,<k??[Q/pUKjXWN
%YJ:&?A&<W,"Ui5N/]J]ed/iKEl[WQ=";lKdN:l4-Ae'4UA,'4fn?j!(E8YL:^38FMHbn<_H+-T[inDQgf.L`8%p\M;ec)hag-Jhf
%;8V%<Wu\_(5;ZH24[NLo_8![!9<r"5r`5BP?9%urfBD7u;*A0Z'h`MU7YbcdEFuL<at'1uA^Z'1'p(D_h3Kn+0SeOmrq5:9pK(ul
%I/P:&")_[:LY>'F(?tDPou3EDS*+QeDF?g>GB%g+McH,R;ql0Vs4tZemB]=e#@*]"0$TF_'(R%ddsQC$#@*_bj3m][kACf\Bg*eK
%*)L@@SD)OkC&F+n/@ZpsQiD5!bQ%HUq0Vr;5?n-GkgSV&Cek0/(rVbH'9-=!9u"GqU:Zukgf9'JpEk]Gh/rW._t^^:*ka8"bKBFa
%leLj%>t;N!A:8h'h<irc&-(ItkP3QaaNq<:#Ym(c^#T5FN.$2)c6"i\"uY,'fXYH$c\2C:bTUlO@^';OccpN@%Xtn]@1BR>o+lW%
%g"kcg\(;GjCKU%3]oF2TK_OPm>ckj/eJn=@^_^@r,IrkqLl^?G]a4m(AGR>E^\d3orK/9!%fY?kA\/3In%o$NnGiKi;`W>R3V\+(
%hX``%J+qFlbL.V\J:m`8V'L?Uq<k3#AB4(%#G:J$kBl^oI^FX0p1Slb&k[MGosP@`%i8;K\)2[[C0-aOqY#APE-tErRk&+Fn-<0]
%lW-Q!QZ(`nH[BaQHaBY72euO5UL.h3C08<phqbtK`20l>EjPS%o=eL[C%\"/TLW_hN*^gbfYKmtY`KkVA-WLq'9daA(:"`S\p'Df
%HSCXH3OPuYa[BM3lq_gA(H7s<;W>Z.b<p[QVq]PsEd<"SEW=_J`Ttifn'5gBokPZ9BA6f7D1t"Am(Y0Vhq^Lu96&u/U?/gY@9G=U
%MlYrhXgF]pD7=>Yq8FXq[&:he[i1b,$W/`UQ!*S=Z!X8tIs5nH4R@:,r9JQbfW`NpnG"A8'+%^p",upubU\a(ZFrd#3ApM!fZ"E`
%URa:bi>uempLDgBlG;t10YAA60*fD<'9bHo?4M^VKAkpU4hG6:&]9N1@G(R"0YHbX#'qu9I"^HiHX-+&`;/bY!U36WT(^lio\tBJ
%5"_[-Gm4/CA^^""_;S<IKEKj0I<qAj'5Qa7lX1BAJ/WSOCgtXQ*5Eo0qPJY'neXA/0t@,Lj438in'<VYojJj^`uSc*]Dac2OI14O
%EBG?umI9/GScS<X]D.MWnD:g;#6^ojKu!<mqg(M73hS-Cl^3/#l`\)$p?_J>pP0qMs!T!Y0.SQ&"h)u*.t;2"K"Xrm?Vr6BonlA<
%gh9n^CX!ijj.%'0q!5(iO.GH?[r:-9]ssupE-Lhu,tm+(49%C6j$07JnDDpTQ`1D[g<R@\@bCtqmDWdQ5.R;6pk4*Gq0H$hR^Uu%
%HfsFXaaAH?/e#l@DJM;R5-L@!)Hgg5U=?bGX:]3V.+8Yf`W9oO@)pne9qWKm(ji(-m(FF7]`:.6RQ2AS(d"e_mb=9sfJ*kZN*Y2+
%K,ru[a4M%e#&.cFB<,LB%f4jOd*L!ejNs7\e:kCo424"lqX*\3RTOD%h$5<<h/=aus3(;_qt'!PKc:@H[JT"GeZ,_2NRljQS9qXF
%SEHBJ[hlfj]D-mE/oJtd-ohglDej7R.nj/oK8@/E"7,+*p$\4$pQnn#Df3524*3`44oc&F,C]\33hZ*BA"rS-&9+3uU;R)%kK0]?
%*Lk*_>#E`4rcX8dJ%O_Rn!grDlSU=.2d7_UHlAd#a1qn-.`9RShq`_PmV9;[qe6alETedN`9LX;,^_r*PAQb'5;((D?mo<U)8Dtt
%Y>pe>Rt.\"PM,JFY;K82rEui!7">/?G5oCPnA6.PpE($k%[Qm^muf!R]j+Ju*Q8h*N*d/gos`T5[jdpjFLg5Ujjr7XK/U30gN(['
%%bf](Rlb;)gXG:caG^@@mNlOfLWHKDe)sci7NBpg>u9]bCt(bA4/d3*FmbpJ*RqW\e:@*_;Wh(TAoH20A,cTE\oj4hI?j5Mh;5mF
%e(r]G#Oe>3]``cOV%mB?+[dCP)7Ha7XA#;EP;7*(O"54ca#r9PlliOM(+8isR+IB$OKfg9o[P.g8qH)RrT<D3r6LuU$0h/qZh)B;
%+9$DJp%.SL%p*PJeCneEkAFiOl+OVt/*F5lfB?:XnFlDg]f>??g6;1BT@#g#Sb&SPK=;8%H1J7cgO@bt>@"#gbO`&?K"skAD&p"P
%^Km,*J#*=bO5ItlkK,mb_7&L8@b8!>k/m!"=(,I2gHGJR[Uuum?KUXe]/OBc(MaQCB(LWVq_q5ZFW&fEH'd(uVL@Dg-U+]CHY<A$
%H2VWG0@3m?CLbNp7%T=;jaXBhG>E(b2'm^'[XcKkWPk]A>k2ZO]$;Jro1q@XAcen,`:'T\\)P&qg?:3iqVCP``@B!fnP==1nA/Ks
%6LoF"PCqe>&FKGt7D"V\7/X%"Y'5o**e^9$&;grQd>pJQ09mXG[8?MT9_]]407Lt!S51!"]_f=IDZqBZgXs4!iVW4f54NnSW6r&6
%f:"]^#9U9,LUbf:gn13SZS;^"g\\40=8dE&XQo0?o)@D0McBohp?I?ZBn<n^%Fi#i5$hOOs8MBU=hDo:ESOuW[d0"7RYPJ++3-89
%Yj7A1RXI.XN]YR;GQ(nG858mK2X^V"\;qEd6a=I5'm`%Q0AN2upnX\;b7jEO8oPj`M=q=>kTp>TILt[S+%$8tFj7C44sbOc9]V8)
%ro^8qN:Ps+j$2/^*?+@`%GU]VlhYG^D?VT1?9DEk[f$!hHQnpP>-kiP^&-(O2J]eT`6/eg?7Nn6VpC1RjrN0&N/Zd3@dD<lA/r>e
%P`/CNA_P:KIeS'/LY_+[hd_b)(Dg<M&VA_p]K:Ju,U$4j:$Re3%,ptM3(imPDg)*aq)*k>pJRTS2ekc>iP+-B]bdVSdQ9j0mAi/^
%GJC`%gS4XVf60+nXY:dQT7(d'nK2Q6NuP41[G*g"+5^nD9lBO&G;n]7fLn-nWZJ^G__Ko<A+jj<H-H2;RI/HPqdQVB+fsZ7Nkcb*
%c>MLQ=]7n##LE=+1OXY@R'uTak==]cm"OCcB!ko9qI'/EC3MYumIRBZK80!T4g0VPb>/d\q2%T1O[Y@\pA(o*qtcX<YH8WSdk['"
%rpB*tn!@>tU?5SB?X3Nnnn.lfTDmo\hk;.ka"MqProWp3S6i\^s79%BdH<amM*(S!qk*lKh<+`[8&(i!Ej'd=XnUq<Q<O]l)_JFY
%'9TrnbpE32H!ebB=(h.E":T_O"9#G'\bu,m$gmhdFS)&ts7:`rdHfo#?iAr]5<3U6pfH"TGZ7o<Y<q[EkSj*[@p%s"^FHq:,9hkD
%U#eVZqC8m*`3]rsHQo=gY,6/[c99+BLK^X3OC.fpgdG@qQ6We[93^$%OYgmVe>$2o=XN;.d>;rV?59*3"1LsK=TSBO17213<aOle
%qpBo7E8.Fi[m]kmoG4ss&7<tT)g0qBAQKfhVe&=cD_Af)Y3IRn1Auphj""J@4o1)DNNZ7Me?SJcPT/0s#2`s+25ERWjkiEJ>'.q)
%;q"nEp1jCV>2Wt`[:F"+?7cPjOHshLgA5ROd]4@Gf4A4NXt[C;h=dk@o%eNCS.N?Wi6b:P"*h-l]rr77Ekf^[J+iP"*Rj_lA]^jn
%nAQ39T:`m1rkn.%0_GA2CUs^W0*ga,pMLj/S\EZRom@0^$'mfA[<)79ULVB0^77`/m"<W\o?!f%k?"@T,X]mlq$/ou^:[WqW\XG=
%ZVE<rR#)Scjd(%T54JqOb;,@ES$G_p,q\`TQS4#`ojfA@WIqi7:6;lE=cVo:XE8IjNa$k\9+O?r`n6>IL$k9,Q"e0N3CUX&@P9*O
%\X,]8bCEL1ECm@"[RGrFs4Mac?JUVd4)f)1rW=MWfRlQPqZ2a/U>%Aj%2>hrD0oflY2h(KUstK+6J'!@>`Zu)h0[IJ=_3I:_NlNZ
%E1d(%c:;D-<id-LDJtlk;q6*9S\M[<?Wu0-pV<-hb<&qoh(AE@m94PiqOGI[q6"@Ip1dfacCtD^(:*<n?-pnpXuh(Se'#W4H*6o#
%\Lf>.LMUo6+KS)bp^N6I_.i)aetXM)P-?PgPI!qQf8Zujh+Ji=i#R<UbY]i-$MDUmG>L=^nSCJSgp=c\RGm`J%K@,H**TXADUJ9&
%*Cj=:V..C^2].QF?(oULAuhOq52+#^4VDA"YG8qZ*-m<g[WB1m:&F`EJ+l3Ul1DW4db[-Ln'1VDQfm1Yr5,J31LB(M[56@rPPbc_
%cfrp.B)h/Pl2>$lrSRXo^XuolbCB38J,BhgL49cBGODAMd"YT]ga=uQ`;.9_.6ia)FW[K%]<nPK5/Xk5r_k5>F+CUYo=TpHs(J(=
%0_sa&9g+BngUKIpk!X>CWWsVY\+1r=%6)W#$<`:t57a@_GK()@<(PnF)`F%IrUB6O8FrBXg)rLned=96.qcoR(-?]2rA_+V<SkDm
%!>f(c&D$G2?g5&./;#0`m7bl^pkV%ijTCCk#Gc5*Mk(Nriq"VuJa^@'JgpE]LOC8"q41Z5Dn`qB\`_jrfl;^&l0@m-H+'O_3dm%6
%`Dq-J5+Yk49lA"Cisn)u>UN>7;'@.n)E^!-UVm.lW[i-'@Wfu"V:B-JQ(#3BB3V0s]?I_Tkk=_hk8t-<q:T>cC?(P"Wlio.kI4OZ
%Ff7a/Zuf!]jGbtA2BFdI`m4<d*$^DLEQ2[/l&F"_-I1\_P06tFT)l'fMm2QK^<YD96Pnh#fdgJn1bB9_cPi9h+%oM$XS-h^Ns4pa
%,]D5%lKlS@XkM*!flha]Rso'I^UsC#4XtsL$F%"GN3m@ie#q%Pk\';:*0TciM*(PcbOQbRg`a7(c<Y\WNDH.8WR\4TNa/I^icB29
%fs8+pO)S:+69)M1+e7qkK&\rBmX\T2`NLuYXEoR`=1f@*#RWX'\OV/cYC-,HboD&eZb0r-+?]Crh2F-!/(9_DB5d2/\_k?t,[@73
%T6jcQT>Qe(QQ:%jGHnC8mTUeUlQG'CCmDl^G(+[;ce02p(q'u6bm8u8.ohuRn<M:;q&NkunMD>5O`P_W^;FA_@.Eoqr*]rUGc%1N
%or9ohMMt>)6g:nJo.apndX:.2D*3mPb[THj6g(n]o&5UJD&nsh/j1(CYa$m_e9k)!cReq3cG6jQ_E*J,bM1Jd[_[HihSI/&[2t%Q
%A87`ZaN:cUNn(eSQM7J19Yr.rq?JoBs,#?%Vu=V0#Aa=LD>Mh7^cG)U6#UA8*!hI.oWsmnKP,Mi'H6BfhkP=A:s3pbPX]k0SdV@9
%UrAqIFJ\/gXr@o0U#mLY@R^BYE#aknGpuFs=n7onGgbJL$h#R,pD2pb/,AV%IrU+R\!u71G@0.$a*?n^=m8:7rB,JqfBjN'8Gf#f
%Hfj(S-dUbscT+'>QEBX]=(mn@(\m"9,<qZ>qmZ.Oo/S5r5$WuQgUY_snuuum&;HTn[b>sdl?fY0O#sp[2h+"u-Hl=G\l)eeFjQ+u
%<pA_N%./m:Ts_>Bp\+,GXdP%Vq[oD9C9gU\3o(7%2krDuPBT26[WLum9",O0c"C\KLCT:@PMF+&(&@SIIn/h>HTuoB1-#SY73po0
%X6*>PMh`7?.eO<X"n1:e?asN.^Q/<KN=f:$S(f#<%H$FILgBX2Rm*=nrt-T*Qcs@d$Cm#an)g(V-=>q!m)GVk3?lsom]l#3]+dqc
%66\q0b3,+!)XqJ(a7gld+f?%klY%':]^@A!O(LFk\TJ9E=8!5t5:^`_(X))O'U+H)ea.:p*D>MR(Hm.hf]53>`KrH'K_?]H7jYMn
%#+0F=QdiWR(Y;gVi3r(/17ZN.4CSP*(3nnK3fP-n<[V747[Bbg$*'(Wi+%]3a@dSHQu<:coBID:H3mVJ%TDo&$K7cphFus3I3Z]:
%8Q#!Y8&96S+cjFk3i1`P#j6r1IX%\0)eX+2c`kGMLo'H,G3)Qmg0p8AYIZqBILQ=h?8rB#hqfPRo'qJ6ZS4ko%a1&u$!IBjN`h>l
%qo*;8!5>U3m#-I?'<T<9NKH!S6%3X['^V6q_o0s2/Qh3o5M#e!f7*1F)'dAAcj"D&Bg!^p3j$hkYFgD;k).XJ]*c*1F*#@'<k!dX
%X;3a&W/>VmY<)5Qjpk[<G"DN*>"o:PXSV/rSD^"5p$m0a!?/YFQ\um=CR)+K*\.52WOit1!Q)5/a2S:QnB'G]@nQsiDK_L$&_K6Z
%msIm^qjo5YIo'RfVd831_sd$`0-5pn:<o2$g&";1h8B-OPFf:&-F.58555P$/%?)VO3\<ST1-Ql1H#/[Lu25.mp$?0\k1IY1:W'c
%`#PQk`^pL@q#3GGm\TIAp%$C.oHuC]NXp-,V$p,iEE))2`A-uITYo9XeqrYTX9g,II]O2I4jDma5c=Eoq)Q><5KauHs'oCM*Nuul
%rY,_(TDu3jPs;DN=7,n(oi9KQnuUmMR7m&L4O9>mcEeT%r1AGr;u8ebT0@>CDHuQLBC9'7mn5k(H&#YYa861XdJCo<kmJQh]ADJX
%IcGc=jq#+sb"csmb1R-Bg>dWKR/-[?fCSu/b'W39R;K;*$CLF$Tf][FWkn#HUK`X7#snX&=NIG`n&(>MPN)Y`c46<h>SC&E%RV07
%&Aa'tkV=e3=XD+GY<&"?R.kT]Iii!'Xh8&e[:OSNW*.9s%?Pu.)*dK[9$<a*DWuMn9?]*^c<*GrTOVQ(\$boH,EEZ"mm@2KI6Bf?
%pkBL/b6Rt)4Lse//R6_^f.dXdk;1S4l0gidZ[_ak\8brXc8'e)eVh#\4458a\!!u]^)KTEXOmN'$7JCGj1*6Z/,22tk!<@bH#".4
%eLe=?#tL7sjqEd]]&rbWAEaH<+G81\&9!9,J90nF"$E2aj'dW+AF"XhA8:G:Oo)[T'U=H%h3Mts-;"1[^<S[h3<3#T++;*?*gLh;
%4s"ZXNlnKO_f@\>#tH,24)hgrB(T06qdi'"^>lP"qI)=DmM6!^ob5AGnO@1\`mmQuTeYl@@jC?m#`+FX"hgLeJ5kNg"j]3L]HS1q
%PqRuR!*).\e[Z98$sSb"RnIQ#jVJMg;^RSFiTQ_[q"3NX!^@=3CkNW#fa&fkmPF%1mp@uiH+7F@3L]PKcUVprbW@NHSGgPn5ZDak
%*rB>8<7)368,M4U4eC!pI$&m1IIP.J%,f7sW^$h$)9qV;?4u^lF$\;f#ZD8KW<9pL8#Bmu98;!#BaEKj@P@q9aEc5>7h%PS\;.nJ
%oBalc;1$J>5_,`e;S0XGKKWf2`rKcrK[S7,+G/_fNmT=adZHIY+3Rdt!FI;idI5AsT1--]o?[7sjF&g7E\)okaNdYejuQ"RQf/s3
%<5Q!r5B1Xi3qJ;#Iai-aA5X1-*Y)39fc#B^gdjp&$3D=F<aqXehSu+XE;eeO^:t*F0%[^Nqp4^kI(nq8Z0A[Kd7j/W_9RoRA1A'p
%'YR8b(n1*@*m8g*i]cN%^(XG8.h9eAMi1F8ebF*W4ek&3[Iua,XTUbfXu67+\U4(0^3?%'4o0XX&,Mo3OV7^rIAcJ<mia_migBgC
%PCkaC%`TakI6]YjU%o2o1p2)]2cndlk;WA`<q)LGc4(gh)\A%,/H>b[QM^Dr$l$W>@ml=]5a/Ic2/%c[&_8[lhUfO9+hlu=2tc:X
%^:-o;p_>ubM'(NO8'n/.Hhi?QI"/J`ZT06PSn/!le<i@9lJGrM.4b;Q&4R*s^aB6areGssHqo=RCr`A5:/[TqAn'iM(;5BS9"5k3
%Ofh9D#EFNP0kK\3Hp6s/qs(A?j:D1[q`j6umXn=&Jh%>f/OK70^Ho-gkB.%_5PS,*5u+'('#8kIb#e<RLINI1Ue6PAs3\p?!(osd
%_6b8V6(7G5M]lOOs#'iThL<]Up(UWKqpu1m$7Bs2o)7[.g%XgG%.V_XSA4sShb[hQs$-BB<1s04J,a<6MYuJ7^]!QGJ+9RHrq^g]
%A:6O0(<Qau/Rd@`[6<dt^Mp^^lu14B,:IkUI/NkGr/6U-0k;:CaiHjIm!EWQo4L;JZF@%:YJ9"!r8KC/`V@nuLN!1kDa)narr[s'
%J&UVP3@Z(.d[13:B,F9O?j;YZ.gbq90pG<!O$/DN^psF7hn6E'1\TbYr<p5Vm\?=mDG(_=c?BDcRrINP#+Y.prjNum&MddT<:Vo]
%eht,G@$]!qcUL.Y^\Q:j,hFN:s7C5Zql=*%j``rhoe1aOc[Yel?iKu8O8iF'WdUtnEb<ODbH';]H@I%*_NZE&N[\=#n:D.Ana=Gg
%O?B/ILa<B!30EB94^7j=09#oUI+0lpe"kHMr('Jp?LT^qVKeMsbOb:gl#+4_&!V,VPPaXdlOmakgFnNcVuN]:@0!N\[*K^^@F^fR
%2IF9nc:[RpIbB)d1TsM5`j<j7?Epdna(G9tr#]@Dd^eWMP2:7U_19:M?=nWV1@aou?N+35YM6/52b0b=gFj!8,CC0o`I^[Vd&hP`
%pb!N$"2%8/!p74C*l_Qk^?9'gFf4iV8'KVdT8rmqCcu;S4"T$@R^B`1hE)MmWpf*&Lq9s[U60L;2JpRB)R!AHN?5.3UC?GZnUHM"
%Q[t[CO2C2K,;^KL#&t]u%(t`sc:>U;O*_;gBC7LI`kMDgpclVc'uk=bOqY%,7VodeR%+<YXo.b\!-d\hi<m6a129GJ1?@f:rPJ>Z
%lAl!gT2#YCJo_^cnF5-q=(FOM!UaiME-!,-RfP>p!T.EXq&PYLB.N:G]c#/peWdbFE^(V=55kBu&nP0p>R$s+Qb5-P+8YR+6S.EU
%X*iagkV`_IOo>9n92?.Xm.VOjmMSb@hn@(>mb*Vs7,/k9NjUG<I/H5)8(f@adhgK%b9)G)Blnl=s5a.Lnr:F5^AP$KV0piK8U>0E
%b4"OY_E%^[ZeNta.QbW+6_Rc-<n=D&l;.8$:R9Ta<l8$C^96<5rl<Z<a#-:O&G=j_/iD/%N:)%o!h8bnI%#7TVYoec3Z0:(ZHg)V
%4raMlg3Zp4fh-&#ejE.:.XLkBIIGeZZ/U.qq7?BBpE#gc,Ot*`5Jf0!*ea$kXr5^ql'"qdeIeX3VCj18182X+&-.3gA6nMDO0u^8
%a2WQ,oC7<`O4oCkgE'J'q3-n+iiD*>7dfknFWutDl92'Am'(Osi;%Glk?MN[I6.4cZgV"4E8C^pgbSh"]-r9VGG251*"J91k]!=6
%mC!SRVdTb1mC-F>r_NJeM,lXME`=NI(&_XfDCJ)$6[g#lf&Lp[rTlFKX1W?=dGpLG):4W`MtH9,r*7.\p:qtaq9XrnqJ>dg1]c]n
%>?%=Bpb$'qU+R^H=1nC>qt.N^1TPtRc=]B8!D)2SGS#QYhgT#3+5ruNHU)*L0>i=k98iuK)8+0gBg478`^e@)qoSFCnBLK4l[L\h
%O0__8.aTL],OT35(-,%reD<<2'[*3Cr9V`fCC,q?$j`[%;5!*InUH4QRcEa[>W:AK2nsiX\bS!pmcW[>aq^Q3?^/SLZ-DPC7t!;p
%TDkU+#&"<LQHj]!RcdrplE>TLpWF./DErUkIl:G!$:C;aKWk+&kWcp>1"b)aJpf<8M5L#_CsZCLi.4s.h9WS#M/!"8n3=c6TmU"D
%_&X:7%_PGLbT*1?Bt]SZ*<uE656'uRh.'IFnTT`f&\\R:(`bUSmBCQ"aY]7"UM>#5@7+pGNd1KT?n?WP#)g"AkG/'V^F4REfk+jX
%0>@4P?O0[')P3e^MtJ]."ZD)Om[RYQ1$]qV:aA'X95KjdYAP/f:GWm/ej_6<Ft2OrWsK#;IUR-mnp`coE4#Df<criaI)f0N].'?O
%fnVe0r1Eq=nR#qmFo7_5q3,^M08+]/[g0[<OSE:XqqZ5;5@li2Y[+J73IR_+Ue4b9b>0?mMkA"0Q?0c$:B%0;2^Ea\Rdi@Lqi;Wo
%B7FUiBBDArruYCg+"5A0,q%XZY2#AijqlH@n*Z&dX_.$OGVt$b\[0nn\6A;RA+o[e=A2*r8_iq%o6D[f\inB[CArDes0FpDr9FL!
%%f_S@jq$DkI5,_f7ekYTIY/73?*8\QoABN7QbWLls8HeQ.f\8<s4&brf_E5[k8s*]4S;djr7bTW\)7.MM>dH93!isKp1:o:n,N@2
%Dh%NP%'\F*m.mX]#(9aYP5El'UU/meQoBJ]RMOmY4rSD?oln1InB8TGnFQ*tp$9B3GQ4;Mr.g?JpQ%rMru^.sjp^h2?iTQ'^\P(r
%PQ,Wo_S?'0ciWC5Ae^"8$:+REf0&ftV*%%O%O`5qED9lscEk2a#a+B_0H%^thf&Q,WJDT!R!`;9+G:7IAP3-XaWn_<cXg5>^lN5`
%F,f7&*sMo`fH<GlEXVrD!(dT%SP!=k\TiW-aM*2B:#p21N-hY7%A#Xn@\s\XF><I:k[oI8'.M`)D^,Yo14M?YJh$(UD;<`W#<Wd0
%C\mp<;R;aN'3:c2O0J)V;e!"J"73h?6HCh'O@u.f"#OuKE5l%ebX]KY6Q_[H@2%a-7u5M8j.W7Tg[PJ-n4Vk2#RLNBG(`4r>nQJ.
%o(*,QFef)'@r(6(%7&7)?mdETA3N=6fI8Y3l:%A>k0-BIE>J):cVJljd$7TLhUlYGGsB/\K,3cK_@m3uR7>c3^kP+%f-:9j^`n]D
%[&kTIS_7/q,WBV<R4`2XE_WQ'5S[R+9$uEe)PKj](;%+hnHcI7OcD,fKt9oshWV$`i_KrS>l>7M_LF\sHffV=,6gfQNssN,2<GJf
%P6F(lq%#`[n9)Q==EImZEDF1r^/Z^WCVMD'<;-uK'n/HW[3QAsD%PbBYsBOCQJ5W[At6k0RGJm8@g]S:qMi+6Iu'oYr""(GqJ7J[
%<r!&4`:M8)_r'3Ajp5?s<5eW,bQ:,SqmhVc"!qtq=DZj@AL[:W#`[@7A+==nR%/>\!K%RG%s",g9+s;WP`$DKF5\J/I\.Tg$g[&<
%3.%lLO!U"r;)(d3I0hf0qN&Mq$:I?_#hLVO,1!OK4?f_E+l#6F'8S_69T&p<ZY<:XLk:t6I4_,WH%j'9Td(LIQ$'cY3uZLN@08tK
%]euFq!7nk^7+PPJ-66[De[r@Yc0@Tlr]%CI:;Zh3/C;2@U#6Bg\IK#"JQBG$KRLgsXaFb9M<0TRUn*#F7b%iKC"gdDK2q<M1FQB6
%4[:h"Ps<i@oo,NDAk9GS("T[shNda[*hdou+K+TYiI!Z![9;^'\j&6=JE:77.:9-pc\[Vj1NrJ2^V\t^8r2ag_TMee8g7'%Sch.j
%ARe9]<0kLMEJU^d!9_jC]p^h"G9J&l;=,T0$+kj^9!!roe;'=YI0aUH7?0QX'1&i/CQ=g]jDgn*7=Gl4idsK<cO[9Ka!^L@YG@)2
%d<,=,G$0[d.3LIDciqs+N2D55FgLtY4=Y%Tb7ueBO85@DAUY_H78DN&m6)<L.E@dsJE7]qR9t+DQ/UCA[Q"DtR+9r-?VO)4/;:9s
%Ukgpb]:(j#hYmt,L(N55[o$882*rdn5)O^Zm[k`[XA3//`Hs89*1@tuib9fbX0W4baUCj(&r%%MOd^AW+oO-NV8WLa1%."PPLRoX
%/L;cnn'OF,I!l<HL#UE6LLkLDfQ.$([j_8Q9;p`4C2ftdNAk("eZDE0Z5FZF_A!4Uji:NTP_7)E3.V4Bk10el*1:3qb?fmRnlOOY
%]YReYZ\LAeS:`imo\FNbhW=]pn*pZ;0>3]MQSf!L<_X$F(@oEc+B(?Sn0QTW]RonL\a">kn\4'sOQ2D5AEsEGSaZj>D3mcYWJZp!
%/'0!C__R;&`#[cE'++nT=9e_AKe7=Bb59\Y(%ZrS34d<6CaSLc$9'pd4i3UCgBYg*E#LKA(0M`qg-422jP`.l>?%)$9*T]Npi;u^
%#[u"QWHj`Ep0i$Fd29bBYP"K%'Vl)S[+dHl@!Cj%39tpmN+l^c[=5R/R&N4nZqXju10R5*^d@@m&QKOif-2[8=YQ5=f;QY9&FFY*
%[kt]*[o&NAchI^Z_6J)C/b>.4qOYY56m*Wi3-+jUS>jH*D?\"IX_m8n*Zc/&ZeaG*Gccqod:>[r\84p0nZ1$OR;b=qNrj^$C"hlY
%'TjWWO]X'f':fD,fhU5N\3nDZTcScD)I]D6-<XZr%&"npI2V$Fh`/kP2@o#1C=r6ICY-th)C]rL2+H62]BJALgg2&/T3-!M$oe63
%,JeYq-2Z;/i_bD6@Uot#:q6H\!ncSVFuB2(>RAnqNnVbD<5=-,KQE"*cKlo9HrZ\W/NiOcQtgl]1^TK!8%!A-\9-lO!C"/\)[%^-
%RXDkMY3!t.:=>YMnf6'5-?NYcZLDIXoQWr#inS4Y+c>]!a0W*o$aU2O&9,RVIaAAIQ'+lfIJro0jt"^'&\n@N]<Ce"01KCLj]b0l
%W:0RQ<.Fjj6KA+@Ys/\?+:0m__+n_;j#]`EEc`%1e',mk(X>L,-&_D/5\6P5NGHA5#"WsL\+YPB?rI^2huhU)#.R+,L1C+Q+4$J,
%ULfUR&O@/g'k$p%;h;M,)Q4U)B)^V]AFeZugs^dFS>T@jCs4SZNZ@AbXh@1Hg>Ka6+sG(K,pQp)"opMI>peYr^,_6`hZfb6'hH:<
%7Vs;)#J@i'dFSX]i]'aMFn=lGDm'U3(b^d6FGFII3F8FKU5sBHFTYqRBo$iqfj[9l;dYi)^RblCIm1RI<TU"Gc@ei/%H30r!`i4M
%kq'pZYZ-VZ%Y/SB_5E_Wm1q$T\cYfoZ9kL.!/EL.j>4[2rArBY18XeV](\9WDsjR9d3%Sm8BIVhhk1m=M;i06co;qu8l.p*c@=K<
%7b0O?+`pRq0u?a$mDr/'Yo*:Gn-,0-3V),sYt.@h'WRp/NfQH&IaCps'8S8C[N^fq!7m&p>FJ7o&jm],Ka1.s#<Mf0^-bcqYDEmJ
%6C!p9\B0^:iJe/\(8&S'QF31oMcqY;6"0T7U$@Ct2T(u>O<?#SZf[2D-#\ZK/_V$InfaGH+Uq=EpLSOa#tpmo*P19o-/j8Bn9=JZ
%!l7QJ``NBYYqq5sX`Vbi/fBU2;N+?^U9^qk1GHXHb.U?0q9G\.G7ti!=<qgBX<iG_OG)RmYm=+_dXVl:[6*4O#[fMW_c*?!JSe?J
%Wd:%P]Jh&)*,QR!rDW-%PVJ3%,7!!C)lpp6c[p8O0jlqs%`"%J*Z#k+Up]E5UMbQr5GKcJF)hg!Ask2?Yj6n3&@C\d1J+O)-RXp=
%-i^(VOI#iS@T\2(6nFEifPX@b3G[h)'UAr#-$iiXQ1Q[W9WL:RH>&WiqPV(uCBj&HQs*.o'o/5-)iasHp\4-%/P$?URdWR<&l6+e
%H[F]1:V3k9#`kfk+`E/I@2-,h(ug[=A_g6)2o`'nFuPbT:)JY+YrtF5*Hg5@G_rD=oYhQA]#DNQZO4R?C44ri$so+[X1'n04UJ=K
%AfTC-*<X+,S:60%5S_Tkb'>k\Uq`'hfP8D2nY:;['E(;>7@245[QdNQh\"%,`]"7H<H`R(ab[L$nmHt`_N>Z<+k+aP<ps^8XO<`3
%^5#HK<@s(e25BHlrmek+EM%;Q8D/Lel0:=5B62<;gHi%m?9^GM0BTf-ZHd6'l(7ZfPfG&1\o.&De0'Sq^(4AVQ.MaqbJ%#'/[jN8
%*MuS#L!/W%Os)DkXX:gU#]mRq+JQ#U9iCTmFkeJ]AK*3XV^%+pWgei8=:/e`(0#u@4\^*a.B</WTl2$;_!elJjJg3uoQ22RCRHQ>
%fdGZJ_jLqg'E(DinI[#pk,gs_;I>r9;h1Vk[';;EPN:/Qo$<?>,FOb(kM'!8TS0F']U'lY:\(Z:;e"4Pn;p`K8n^@c/.4*5$%PY(
%aA+Md%Y20Ig'M+j8fYU@g365WDT.b`HI0ouH8Y^r[TQ.Z0:?4)1i@L*+_8PT/YV3W56D=*+g>lT^(2lg52;#g(Ee0ALFm\E[aZ)'
%#$eX?a:!0e?"r<+js*dkq]i_]lcfgST[_pfb+`"%G-O+Y2.98rF954ji5WAfkZM<P[hi:[R2k_ooElPY;Q3p&59lq^hiIXH,<=0W
%MkBV57U(4r@gltW%i"AN_;[`pN&h-SSAQ*LjbqD%>Y\Er&0A$Pg,CE+]SLl$pi%LS'^uU71+.7Xbd.rS3C1@%*`9sG0B($iq%s^1
%KJ0+l^_S)YI_\EPDn*r(A1f/jUf)LaA(pL&gb:e<N<PMNo/tE<F;F.8NYdKclsgbj1>dbOAtoI-+'f*j'Q$"d]5X5'e^)G0o=hd7
%?fr>+^2sTIHu9aUT%Hk5_KC:GOr.l-0To3[\bH_c^Kdd>5[unSk`Fmp?Se%PhK;)5g+N'O$s4^M7CK^LNPNkT43"tMF;^aW+HKV:
%[NjZ_;ROR*.Qqfg&]<G7(_]j"$/&q$e877%JdkI9)f3FVdP>s/#n)46:i$CmBu?3<(%_pi,aLL.ltZlXV[f#%7p:3qUd6pu>sfPp
%o42eJ/@K?Y-oG;iC,oC7^hP+k[p0Y'[>>[!?]"nE<B'+\$2!db3=g#/X+!<8&oA"H&MstcNi$"t:A!Re.mm>)?$,/Z*)Mg2+GZiX
%)Bt*W^E5W?3OP-J:GTm/ogA5,XCdiPWXppP]KDmTfJ+)5TD,Z):J#)*#F#-[laW4p"R2>\V,'u&e?I5f!@Dglk`I)S$DfRJ>(I>a
%s2c`gO#LD'C%'..^c;#EVm%b8X=43Z^r5:$64B<n!_$(*G9b8^o$'QI*Qm"mbWSJ`_'Y<c7R+L<^_6!Qah&u(ep=[<Upp%$-VQkO
%iJiF#Ai_<GfksX#5PE)k!Z*Vo9f=#]2[?C2+OGIN6s)H.)YpV3l?EM0R3aTo@k2%o*ACLAVq&l2fTq:#d-=7j5:J.GQb^4O[c2!3
%jF5SJYcg'%p9j@3Lp/QWnc?sb+&+VTMg/PBX!YDCU%e%E):?h@c"$pS!")^a'ca<X*!t%+KAep4RMu<8fZMEB^_0SG'hJ^T*?rd?
%BaoU=W1*(S3bo7V4,P-'<`OT.\NUOI<Yd4h=W!TAk>:<N!HCA:A\86i/`VaIG*rglD6fVbWFd?G_XMO^TWgA9H.NG.':&]&=e4g6
%kQ<D^bb/e?2`XC;:$+(X`"Mp?88B]`1mHjLk=H:Nm^P;2W$7ic?K<Ag8X2($d:-RJ>Wk4u=1l]j]`YJNR!((Y?.qs5BgT1T\,B;H
%42m\NVLAE!fm)ZnT:odZ>B=qjUpU>Ph/IRG;r7j"GGpH]qCkr!#bc+%))?gjST5f_8BhN^V\+L;U6H5bHS+PYFeR'E9OfEp@k78_
%YomY19u<Yp[Y.TDlb3EL%&C\!,QLO/]]ie]CR<Jfi-b1[U+8mg#"o/"+g$(_n5[Y7Z!V'CI@T!&J_ABW9Z7EOm@YJgem0'qEa9,Z
%W%8B;T!1LZLfQiJn\h3!M\2\q5VEtG\.[g(GpGn&MA5qY$CN[c\aF`4-rlB0\4@pb,VGq9RHgcV$L2%n-S>)gU60!,,>5s%*F.>O
%U&o0/,g9'I<n(?Ehmc_qS/Fap?ieU5NFWG)g4UmF.A8YE';OD)Tk<.KLqSbZ("\D(cT\RZ"Vo'9OKIX8Z5)U\@*'D_i)\>-a>iWG
%4UVaP6^8lOm1GYk'R'VZeMXm4M(iSu@s]K',D)]:JKt>(4A9VeVJm],&\P@>Eek"RUB>Yj^+cP6"82^A-!t4nKF\PuP,'O]S$#p^
%4UKC_Z\c;VnIQ!Ol3>M1/OCeA&g=`3QNHa*HH;/!q(C`<d-Y`[oQ-+^N*Urf0K8Uc$%NAujnh?4s6Md5-)A+/'c95>nCGf'c/2F.
%.tS4r]=k5mQKB@"ch(1=""PeQPh60L[=Uh`B<q\I1SR5Qha(s@#ig%P[1:U,Ib0]hs'?gTIW-'1lccP,(fS.`J^?,:Z=AUE9eqBO
%o*sWe7Iqm$@s5<]AR=s$p-0lkCirY>.UJ%n)Z%HE%s4sf9!-"&Z6fa2-6dgEA.7qUWosVqJ)Z5hL39)QV."*$bnlAuX-+IiUY6Q-
%15amDF=kBK#UA=hle.c?>Y#-`ksAr>6ms\)GKWkC/c-@b<PS`C@8dir]1UV@BPA^Jhm#1pQ#K%FMat1Ye<rM"I1*RLX'Y7UHc$UH
%ihGbL2Is!7kh\t"/$?<u6Te.JR'a0Zk\"]'(L>Fqi_@N/$Y;^pAD3@^*=D4o?=.TQf_3BdHQOLlJ##fc;gd41lDYu#EY4:Dadm]E
%JP2:pO7n^Ad0Y(>Tek6c%X7JhYPd_h)p,VA'c`_0U.5B=K[+CRF,59t_LA1f0K5<.I+/.bdk<*O_2VHN%d\_qrDPYVKW._;M$&-X
%(:)aI1t@$_'AW\WAtSu^2$R&i2]BL6J8U:87TIB$C6'/66L3lfK5eOn"9>d3?&3Eu,UmJY05)eQhckQh\n2B#UsOd^VJ._:R\OMZ
%l>2+:g(7;:9<oY@(q008H7G>#Q)eELPq9'i%#\<,dS:Sk6@HgL*9C<Z3D8^fFJtUZb7G"T$P@e?b680F5Y_*4N$;F`n8f/gJ]S5a
%A6#D,EHdCj4d5`MifPqWCiM!S8T!Z^,9pIH$,N%GF7'k.G.f8mLWF5!2fY0)rKk5u$lrT@oj[on0U5>Q<SiE'\2&ZShq-MT4#$gE
%0N9P=ET_5s*4]>kGu*^@j^G#lA4LAB!:^ls#@&2q]Z^Z*i6.T$c*]#Rr'h3"aCR]3`q>GL$0*8JZ]L^7qJqN0OS;MC2>>_$02?hr
%`G6Lu(pM>a_UVrR@W22+dQ*<N>OVW7EEt$45*==&(XqA1SSl7B`]^"L/(X\c(\'@!p%I\13g$H+,l8fmc2_Z=#[\'8';YWe,>Nl[
%cZhTpRo6SO8GV+-ZQNb,CV.k(H[,c[T'B7`s'3Ff(%oFkA#S2`-hMcd"nLKpH*G".K/cbPrN:%q69(OA[Aca$1UqP85U6gN941#N
%C"K5ds%pR<8]W:P/pJShPs*X'>+Ml;#U-WBbsY0O!O#+?lKn"K-CXMu7H5LYGJ'0>\6X-j]ui$/P*lcZ)#2:4F@cb!?+:oBL`BpJ
%3,q<1/0(UU!o5mVBG$QL+s:$,Zt3Su5JW(j.bN!jNCmMT'GKgo,5.CsE!'\VXjamLrlh8aU7+P117*bmiE'6L#iNu91IK!kJ>R[N
%.Sp0$.O[*I\j\9VL?[uj%9\6b6jQn9!;WSKF;s:190p-tlmIG,]*X/&eDp4k#VIoX8MRGOhaoBo]YM5KkF.KOD&1:$UTjH$4;.H7
%52624p;?_bFTcL1'>\.`GJi0"omf2Mqro4:?'9*k0IRgT4puAfieU(k#mn"i.aR*+LT97VV-=@PC:0l9RN\q$'o<EkBI+]&Kn`-q
%m=(D4C@>g=">nZf+e*ln_P4\)EG]!`ij5U*Kn2lY2@M(OR#Qfi+lkR?`%%`JIE"0h>$,Z!60fS6-BOa#=be;a*BQC,p(N<YBW00p
%A9KGRoG:8$j_3k1UG)3B>Y7@0P(\VT35Z>L@9gR_-U7ulo0C0;$S/[=4cqs`#ouo-fnKYRW[SWATS,-GaR!3E&85_R)BB)VbK-`T
%9QG\LAVq$@EY4&Z,29<48^`fbkBqH,e"-C`&q$sSlTtl-fMarr,"%rp3Q3]HF`+IX]J#9]p'.\(r/u(;R=,=&N[lkiOE+1:+Y#dT
%mM%2Qg%WW>DB9Xp.Up#1bmmj*!`gOkkit6?('LJiUt0p5Za;>(q4o&4*'./Kfa',4QMLk!M7hCJ>#>t0"aS9-YO1C%YAjr^6RiYU
%a3u9sL5cTAiH3cVcB5?Z/,CVD2S-H4<u7[[k)@*=]Z_R#>Rf:IX12`XfLX0?RonBn,:^Yt<+W,lTp,QH@;F6iGbZG&G2$K7:]4=W
%n<c^qJh!7m6r4[+F6BY[X?nZj?*+WT5,.Z$i%'/)c<^T[YtC;8ktUs]6+AGMPd(eXrHI/dY1c41Psr6db#saV1oIOWSP`2CD>lf5
%N2tR-*A,.)UN11o0p1tI?Qj1<R/eJ?\qHQkGThc8csB1R#.G7j5fts_>'#u5nj*$r`37l_(2GgH0k!37AUC6Plpi5TTKYu5Baar7
%FV+\WLUUF>gB8\/Ia`gafdL+Ln&t/id$=qLi8t=lcta487jAENW@sZphH^Z;I"FN&U&HB-BKjc&;KI%iHXFsJ'GOtsbEATa9?:<%
%Yj0rOb2ugm[W*'D^MF+J+Q1c&cU&+`6mj9jXH?n2^Oq*uiN3Lm3^UcK#f,1`i!%Q(bGi[=@=!kHrD:P:U.@;E4DDUkfs-kW;j[`c
%ZQL6F8?3//fZtJ?6?Q,5eC_5C#G`:HFntHc(0;/#?3O#m9.2\s#Rpu"k'EBu#je>m;]KEqN+S$]oPQ);S0:R[dE&SidD6[.eNap;
%DT[#c153R)_`C&=O`NTm<2O]8":1"c&?i;CQ]otdFJ/-;k%huSJi>u/UD`"l`1/s,G"!b>K^rRf;H>(L(tTYATs/DF5bQS]R)]p$
%%RB$f$aJ(@puM$bgLI1J'#HN3IP%>8>P&1C&Imc@Y,K-EMc;@b#iIsD%oG9par9QVMJGmu(0,C.HEhHc;&03;Z*l>/E;R6C0T>jM
%].>;6]WYC\gRPdB=P-TIR94qj89ThR&rpJtbd./;UE0q-a9PZ_7t7Vt+=&:=7h3]sljlmO8M"`Gi?V(6[JUq]_-U/F";KH/0gb)B
%<(lZ+:l,UaJF4"2i"6C!(OUW\@bG!K.u8=IXOh-VK!<'40@;RA=_OY>'j%Y+*#2kcIBNfWDQa%+BJVOJo!9bPTY8!WosU)166gjS
%AqJLp!KoFUokac/j"<::!G3Lsd4\EP.KM%Q666rUojLKKl#j[_2<ne73gD/#>+D)i3.eiY'Xfm$6r*iAVFIuUHIs-n4+pP-T'^qb
%;g55\KHXj:giTP<-S:Cc/Y_*D-.snF]m=SK7L:6&C^%i`S!WZa:jk?%jHG!e(+M?=F1^.b?LE6:SKe=CU$ithghneHSb<o%E1M+:
%b1`W$fL!9E^3?bhHW.m0&.[VMSqqQdS[4O7=jr<M%)9V<F\*B$hJL`/Hik2=OCkbf4fmOG"jO(PG.t2P!!8<tOZ)F;+@T7:l7-/\
%dE7FB(0sD?b3!>,;jre9,A;b%SR#cuCrKZ4=LHTT6B*"\=e$"f(O!X+o_1V(WPK4UP-U1"-jkpJQpd#iNsN\.[;CS%DD;P-8s7PW
%5U`)P+)=AVEAPY^*!g#CW>dV7WiTYIOl0q:#PaGc#8H,0iUrOmBEbR%VfGuEGZs7oh8?`8cEZgUpl>8o7X*M,>U(dFfVe]%U'q*.
%;K:e!=OiMBYl?0c(pFPD*rW^UOQ82cfOpnO3a?>8%EZn5l0A::*)QAkN$nd,YHkn0f;dcbr?d@9c,3<D&b_#d+\Xi`4!76^P*uQJ
%W(8M99"=49Hpr6lop%#ldSQ>DMakVdVbHF><MD!]FXnW6*VJM\$@c(a8%WWjJ1O\G!8+@.U>mo[80oh7J+o.OaA4t9dnF_[b+s#=
%0YjM9W^dNR+o<2H;LLfD[:p!s5/p2Ma8"3$bY_5Z3)q1dbZYbN&;=00m@iOj"PgVTQ`bs7i3LL;-pgn15TPfa(=LX0ldJTu=],QO
%J7Q_?p)uLp@aB7rh'(#-=I,080,TD"Jp)8H9@_E-[qea$42g#X'mf:cga01:r;rr1-d_f!lU]Z&"@.lT>jr)"]ShT%2C?QWF$s$'
%,+m*P;^qi[NH4K\EN<VE^e.ViXE)4a73N\S!PNB%TTrVkC"J^ZlqWc-/gcKskSVm4W!*d]8Q@6WLJ:j@2Gs^[eHYQ%.m?;=L0I-%
%P@L(25e3JZ`%YH*\d8`WJ3Bn?2\PLZXh4(;ZThb(a&1`E.Z0AU)6mVQFo>9@G?uK$n7H.?m?5H#'['I'>r>D7<c>8/->/S0NYTO9
%LuqXNd_mP&<CfECEGhon!OAZPiH8?-lNNZ4cMJrXP_oOX]#DZMa9W?JWY#4Y@m#j'qlIq(+Fb>A?.itDFXP%4.)tDllL,>6V]U^*
%Zjj`hB&M"A5=/H>#a@qT[o)o#DPm$0QAa=)7V!<skj>H!+=R_^D:Y^m8qPH\01YOlo_V^P;qbIE)n<<ddTV,:PIrhsX7BrJ_<s*q
%Ic2G)@8[\h[tl+EoPJ<WUlQ0,)c5JN3$U/I9Xo"[Q!AiK;9Xi\DWQRQJIn.mmnk/r\.[1_M^7)`?NF4HZEQTsQsOGi**5Q+c;[PX
%%X)na/c[%#SB-OT(AJZ40TMnrc!ol-)Qo-E(`>Z"+eoq,Aa'igl\R1T-!AS?,=`5K[Bb^4_"2"Te6iY%RJ)gC[8eVQ&`q2o.f><G
%hV]@B>Ommn5^\Z@k:P5_(4k2:[=k\,'T"QgRom.T"(IOR[7;js1iqTIE/#!]=[c"]fM_BNLe]bRMjS\&\<7^Uei8,jNBg&18-""N
%05-3k9I^6ImeRm60O$@qCL@[2r*b?17od&3/rUY1OltIXs,6_&L@.nbE)rXbkZ92[805S&:LSAkGLKrH5T+IiIcN)bq+PI[3]D8J
%\&tgh,43;X[4Vd6F:oMtciu/Zb%[G\Qa2^'9/P_Z+P3$4Rmknrc&AT-(F^M:75c?tk%t2T=lZa*`KbW2VQiY3H$&bG1?l5^l4s\[
%eYJU>o1OU"?9<3O$GWouWs1iIg!#&k6#r3?]J(KEo!r)8j;hZH6@F.X>cOo:`YS-nhJ+?E"NZ!ZH,+LZPEX1%P@)Cq>1k_E&4.lO
%T2cT/DB`O^r#GUdDGi16GV9hR5XoXh&+L)Q8DL9I?9teBE".Ip<UC?t)\.N=`:S6VU&-cWF=S<V8G6J4DkDko!ut+5p'BTl/`p<(
%!\8))GpZ/m8=b:=0^,,`"s,8%d$PRUBu't%c3[\HDR!>WK]450cPda>S!9Yc6*\s7:4rPO7?i)7XcI3gc`Y(3S",Y.BMQcif]Of4
%J)^;NDQ<Mo?)iGe%b7d%,:nq#G\3??-)/(@Vr$JO&fdE`dif98fPNK**p8#:XE`R#DNtptX)8d3(EnU@%Duj(.]cV(78uV0E#mB/
%SqA@r>Il8A/7-J=F^8]c'5jY7KA$SW2_Mk4\%2gE\/MK<NVd/`XR):J%(uc2^SAiTeS\k;1]mu[`C&H7;,YGp;DQ'qEC1@[WG"R9
%2VRdT!0mW?mDLeuUi?tsYQ9SFKRB.sO`5#G[2+YNZYrZK;gGJHhKIH;:f5iM)Gg]1HuammbaWaSMF&mB2""tKS]:HH]MQ+0VE4rc
%AjG<K1lF?kc.9:9oXRNEkBKo7Wh@%DE<o3!B[Pd6)\5B%NF$LnMd"FogGq?9+@r7gLh'G+>U.&(+2"Q7h:1nQL''&^"*\6sT:CA6
%*akQV6XmZ'F1_MmQXj!k?tl92W`C#LQ5`sR_q,B^;RQ3)FAu4bYUB[XK2o#</_l.,QclZG<G_H=Urk_fGS(!dHSAT9,V%[;/<Z)d
%^&0VF-;8pu8Lg*s^.FUOmAcc8/Mcm)BBt\iKdBna:CkU3?2"ZXJD.VU6A6oa.)*ttX.>`]9Vq;b`X]8+j)G!h=p..ob*Y=7'[-JU
%NS\((#a6br,I@TOX>@$&$U^2?JC6WHO%;a]U4h\Wet:chTOr2T_*^REEL3081iAOnP<O`Og2/YO]ps6l_i60UCP4kPN0rQHm0WeS
%\Y@MP9q.YXe190s8k&iF2"b;0+=4H@qQb4TlHl/%n=t';\'ZUq(m%sZ3X^Y(&^Y&fTP)Zd<G`(s&,NFuXu)@C44^Y"^LtQZ+r#6e
%N("s05S5[G:uiV9Y78Jn=Jb0L71[Fh4mU)HN`ZB6?,Y8G4,D)p@mh`K.:W^/[mdM(F\%-h'N(5Ci!W-kQ"h]6LB(F1PGSFDBB!eO
%&*[4OO=`oH"TKXniQ4/+/.Y57VN;H?p,p.gRS"Rn_]4-228p'&AkbT(O?c?/Wt,fag8&_=S;;V#R=`o*CR;6]NHtJA;7oOSs'PfN
%^\c\;J,]2Wqpg2lqsVb7rh'5.YC?#R^\c43roUunlFUF&eM"];jnF8rbMW<i(Ou8Phu2@ihu2]PrV^Ye/:O'brf7oVGMi>\msc>d
%pu`TFqfBOLlT]$'kNbhCanSEtY<W'Qo'p)JZ]ot7&*h2b:pB)3pb1@AJ'i6@IJt&_qB)>hJ+Mt:J+oGirjNsmSGKu9q9Ni=hhc+N
%kUKgSl??YbINN!4ZHjr>1Ua(,6cAl3X7?\KTqGF&E/9&Z9HC9)U"U6T$A5>C?B^7fj[s7R*BLq0(>qV/6C">@G@W1Cd4M%'!Os5h
%Xa`ntl"_WGhSgA4Ft&82Gn!Di??P]db>L"E!gJ<+ZO$X?d232shlO0Kol\mOne4B5r;B<$E#5"(BF[MM&W!`5KQXZc>_!Ml$RYq2
%0/5U5H[hP$nL#q((^h<<AB(8t,=T*![Nc'd;4"9+!(`7WiGKTb3C8f>>:&%69U;h>#m1uOe3IhNngItuJfAeF0#]?*Q2=^]e8QKq
%U?M0\G#o=!5uHI3>Tn!!1(pl)=J!@!U6$B_N+H>F\%0=g"Vq-qn-DZ>@'2<@+eG!'*J&N;Efk!]GKI<#p3*[p_c6$2"T+)s2nTMs
%^E+O)Lp%Y]=^bA'3In>P0uf?!](jBI0CjO[((jjUOV-K*gl8;#R@GJ&)/a'r+gX"DL*k?P"ZCSN!/1J$m]bH+F>OPe?*RQi-P0SD
%Eb2gkk*NN@Z.+%(c6=NCJ9<q;(Hc_Oa[^EA*s!ea*Pnlh$$K@.0KhAaT"k-s<N#]KI>C[l6,tts2)Zg/'u*#ZSm28r(b*<%.l^6=
%ThKN!cGY9HpMnn723O;(SN>5Y5ho73L\:dn[[/"7*=Sm^SPbq.G#Ql(l(kL:5EN`c#c*dpeVCk9#jH0R?<BXQ7hddM,[i.gaS/HZ
%9H3F?@7-Ou@mH/:,8c+8Q`RoDVGr^'UR(/3pkNg('1NYF![d8u'/FS3ZqebbYj%7db2>=n\>7rTC_Wk?Xo`oo1=Z`0.o6.6g*&gq
%S%HmK$R#Y\lo2=_NhRPc8G&<H&rf;RKI/jt,%g^@\Vi42,65/T77^G3S+Lb.GTX$ZHj?%ko$,6R![?^J3115s:=9D0#TH#!]m+`T
%rFTr`,e*j-N18V%X<]CQHm(N.YO<5drY#-%p*R?o"D?H*GlpdBnbPEF31"si#$);RD(2C??(?a0@WL(\7gX8hDW"n,T^1Sp/u5&c
%_a+2jk-F9c.\1M6r?E.#"C`4egr\Q>ODlcREOeaUekGC\r+p*#pRib[r33J`1dTEF)RRJ(o<:kW$+j`&p[Yl.o'N'9:GRAMF*6%5
%.Eqgak'XmQBnO!*W96k%-1lW,e(g6&KF_KgdgU7_-\Y,gr%3+4V,kG$I,ke8:sRK4#h*@qY"g6@..6\[4P7UVD&M4@$OXrX6Slch
%gG'RDmh!irL/3)L_%++S)?s-:0#`F(>$e)/;TjqB9Mo$8]66Z'&SdsRpu>YNL[e55aJ(L22fu!0)N/aUF)j[[\dC;N=]1S)"N$`q
%f"+*YG5!:tOmJ<i(+\W-aftEJ8ph1?/Qiu"3/?:@gMYp,;K=9#obiU$3@2u(hEpF]NSdaF#T,VgU.>ilB!hBh[`[5a\lQ;@=,4(P
%cfG<RX$M0_+U("\DnQ<f`FQE->n382rK:Lg2L^L:2i%c`FB_UiSggcV/VI;#Wgc\V\QGB^(Vl]c>A2emfdrQGNA'C.O%o=,<fSh6
%HGs0\K6Uhd9A,=T:8V#W%!=Sjbe_I0D@Yb/Opf(D+f:VXU=?3^SlI\?KJ#W`=`#C"\=o`#)%OXlg&p$[W`^r<P5MGiK9^1Vn,SYM
%C<$FHL.Hq&a=nngZ<+n@k,0gr^JnMa?'&(uJ1H,e=FCXI-o*4'*\>P^nLls)r3fa]%[Vod5?+K#_l0%8P'ngu,<kjbruru\M'TlD
%%#cU.'kIB>i'Y,]bfa-Ce<7ltCiuCJR2/No9tk\0RH4p1!EQn@J9P/t8Vd%S8o5^(RrDg&mp4.K6sEgFX*L&Q=qhNSL>m:DSL7Fa
%6b'PjTEMP&XI;.I&j==`<\KMN<S1D?\HT&M:NCq^Lnck-U^juV!AJ3L#_?9)EZ.90DaI"k>mS&;Q3uj-jTpQP'_spiR0p.,m^aM.
%010uH_\2,6/&O!'-?ZAfi8K9eNCE0,<VkhFhQ/<U#RD93&L0^:,Y.?d@`>>q;S&='=ZDVUE'G7KGTBse+-P-<0EDI*c4=l`>MB/E
%h#dTVGnZ$8Y;^S)W04^nJ/Dn%=N]$DHl.=dZPQ<df=:okj1%ep-t?M8MR:X,k':\<7O#WE?l"d)>$GiAe`F,h@d0iC)I;B*/K.30
%DZ5qrSZT+$cA4;U]9*@=W-k#`M<2?>;@s8-?\'MJW6L?_Y_D^A8P229X`"(un8<ZAJ&^m3TPB_D'!9Ea=ph:AXKl=H:nq_?QGLQ2
%+9GRCJM'ok1n^QEAB3qcBSRLk!tI2'65Tu&?NGkmqMb'<q&luqD'&YYMi1O@\tRKt85$r\N&)19,)>'G&#P1#+>G4K*Ca*u$E&i<
%)L/Nea;j+FigF$4"m#a7g/J+!p.P7eEa.3?cr#0X[\\^j6gsAIYeQK!5fm(@*hDfEWGBt"ejqC>ohqqSYs7U#<P2+cmrLC4Qa"'*
%PZ@j(-]g"<WWH9:k3?&C%DHA"R+;ciV.rI:.P!+NdOiF$<XmR4d#2`h6Q&,3\,9,QJ1N!hU"K1?hP=._L.b@&Z6:[aY>I^UMq^Sn
%FF^*t#W5jDGOp%`++eZE7P)-L8=[W!o@D'b7L"jPdL?d%^*AAf@FU@_cs\9VE2S&?H@6X9f4r<"1!L&2QKE_Q3lSJUCtL)8=V*R+
%_73N%L_Q>H4p#m:@pI;5k.$8'@DLD6@Ed%46%s<ls+Jb3I.*3Jnpj%AA5]pa4ILi<PWhP*;D7W'!1YFS+4]U]Ir:$/G'#CB$:jMj
%=6L,P[#T*I[L@Z8n7ZDOjc9#nK#jQ+9s!l"n]A@g$B9m<AkDQW<Gi/:R;"i0#Y@GaWUtE?/t]kZ$,6DL#PQ's1]Ma#&qCI3Q0_=k
%;YcP2E[PifO=*%Uo9R&^d'?0/74tI[`@,4Xng'fQPhufjEorG\.ojGQ9!TZu@1\,/FGX6'&>st:OuR*Vc7WZC)f/Z2HAGSd22$<=
%]=TCU2Ji0Rd,Z3i_.$t:_VkuT&D$Q"[S94J5F>'.kE]-_L$Wr`,L-d!$>?;`^gNsqEW?;5<\=b\7p[gZB$DXObVng.OiB<:ACq4t
%06rX(hF/(rM&e=?Wske'<s[qRN\Y4E3rh"_iAqAMps@#I`EI)[`U]*q7<,S3]Q`CjCc%e9I[**I@nbZ)Wss61HlO)j4P_>H6At+t
%i;8nZ+bPRfBA((/CNZ$3kq879L:$TdapA)eaPfN?##B#3;]4@6>6J+V&U5;]I&iJ;ld4bC"RP%tW!n$VBTS0a.$p9;YGPJQobVO@
%,pq;N_k8ao#pNWETm5qtMdn*WH=dk<#!ddW,u?\(<C/Unmi0'C95Y!F6hDcc:A82&E,UJVDN[kh`>$;O;kgAq05)1aA]PaW_>HY7
%o=SWe,TKdi\N7U$6[5,3]IZiT[KrfUdItDdg`epP=+J5X8tqF!m5H_nY(%6pC2ZdF.V]"C:i@$"/l'P3Ip-ci%JY0n@r/GhCXlND
%X`mm66O@'&\\K@6e#Z&Q!mXa$D*IX&F!*:i`[:Wb'1QQj.Q#?5SPtQ<jL^K])$p$Vnl"D3P>cG,Z_'[&nHqho/l-"^"65A7Rd(lV
%d:eV11o-UbRIg*%RlA#XB$C>G27jR4$WKtY<?EZ`4>.1p)p\UcR1Yn-jnE]UOlS>7Pn9)$O/X_4_S1pa9TdNM*H'OheN6VYWM\0M
%hQm%g/)MpJde3An[oqIi-tiiq2jsRPBlEPB;j\Vbg5+$_fT3_GBF<tV.E$6BM1i;t]=r9S1Q-Wj?j;I#>'s]An;%GULPHm/j$]`b
%pSOmX`<eX?[?P[fV:IVl.aihj3mnb3'aHG%[9lC"1$`j-GQ42uG0uIrgs`Fo>gPFCVO)L'6YGK?RTein=?>h74;O3S5j1>]E*;@(
%3/&C2U#&nsBoOE<0AqsaTE=Zmc*.4d@Wd=``^YKhJE0WS11ub$_ZXL@c+:W[[:bj$.7O2=o*W2\a/1=QG(*A$+1W9#0;SU.XAXr>
%3.,HGAQ'82QTtrke3!FdS`HP,n_I,]c/:KtRTrpqHrDMqaUj#1-]TRY#6Y,PdraDGI+^Gt\Qpb:L?Pq5fk,R*QJ7HL`%9V!n5K9_
%H!T%8$lg^Y9WJ5RRuqI!=93<oJJ^ec/7>LRL@$]h!etZH^oLV9kF*P@gEKHoVAi&?L<KqMB=i>P['6u?nq_F%Ui10Tp2U1gguI`U
%/'oPGb_`X"+jCm'LjrVe4f.)us/<QL;)*JKTfL=0:#XJbYNBBSLNW2*a@KSo9[=m/ibZX-e!-=XCdH-03gNA+LoqMC:,C;^&U"i@
%j&u*@Q#PgrT)DRSI8%Xp/WUkTE43I4`Y8NUL<X.OZH1?:QmgB`l5G,=E+0-_I,WDRrL^Ou,2Hh92&qD8F#/U8OL/4,Ig6"k+DLol
%nlmgm0Ne\V$_Vs/_?!%qSt<qrKemOf,gF<4X74T5)Dr$Qj6>*N4"KrD(8%QX*E(WZs-hGHO,L;1;.mFfO[]Z^N*1eSM8INUpmgWm
%,GEbK;62at[?\gr/%UXV09/B"g*#O',FlVtCEo-OmtJIX;6bnRn'.8mjI7]W4-_4RE?Qalc3%4C[OV?5qQk%XCQ3-jg18neiU.Fi
%CL/8k'>^=oftF]obr42%\gSEHR*r&Z+^hptqrRgMN:Qd2LF..@&K>R<a&6Q,>Fmu:*3b(U,:aYd5m@UA]FhRS6IhNCGN#)bi2LTT
%#%S5\Pgu?'6/_D[T4W,$-/6rNC?SbT2_gU=707L#KS`<[WON;[NeYu)kaXJFi.fs&LJ7[UiK6[3(2]A7;->"pW=]^Xl-h!9rW]UM
%"k[WjlNq]dnd]hn@<I=mo)a-8\mj\'b>Uq`7*$&%M/P.m9/`*^1hqhog*YKOBh0o=V*r2Olc'SrGI/Te@\1hQF]GpO7FffqSPEBS
%/.%^r;F3Oec4ek3K<3:>#ea5E@]qX3ILP@3k#uLBHBtuf*qXcl<]-l#SecTHfAj`/WFd%fbZH(E\I9Rd/q.%<fQo&)F$j%gdKk7o
%fV@4=81ehZCiXS\!T`:>o]\-[0J?)+Eoat$qc:5:`mj)42j*%(>W?un"H:*ZJP*[dLk5WJ,T::;<87Qf3X[s-gY5u@_gK;fj<"+E
%<n*[($5TcnH)M!(FcmKq65_Grj=M=\(mQp[8Bd0hi4adkOdNg\)3rA/#O=0M0L2o<L[8H=8]uFrWPnkuh$e#IeG)^h<o%.<J:e0k
%KpVMMqgbW[7d)/pH=BLYG!gJc5Ns<!*jqJ\PKiTha?:[H>qDi((WOJCL1X^(@>\TGH5'$AYR(R$h4)9H7=)/nI"[qD5WFiCOP`Ue
%(>#i2T;>s$$%Z'M,icX-;hM2;S(Zl$"LOPP%*fQU6,`'\n*PCd+H_D_\**51*X:Le5#5s2Y%2I.`(6a<7a0l'e[&4K.bmLj[Tf/D
%#<_8(WsoJj,+#/K4t]VC1d@I+@+eWU?78PVD/qMRJInk#2A=Eih2\X:Kf,FC*QaI+6&It:3Lj@/Ni$kbmjI:JrD@T*`ebfeQB$gK
%[6,d"I-p7SkT/]`I1#X`[XM!>;G/]W/o`!:Pud;DIa3QS%RqS20#to78<o3]GLdBrYl+Op;QcrU+(i=0D+PHY?TWMMmDoLg4>SAc
%?YnMA\@lBTKij<lR?#!9H^B&bIa+Qm=`AT0"js>I60E6+K+o'9C3.#?b,lN`I`i7tT,H1sFV6!CF^470>jO\lUgbK<DD(&`>+eD`
%@d<SJlcJ@O,Cn,UU8k];.@W8no$!k4f&-V#8acZIfXaeeZ#A>nC1WNAr5K6Il=`9k.B2X)elN9\"<ST.O+ee.3QqrHbhW]m.KkBX
%0WD:/=SRm6jKcVM`9^.(G^h`:@E*-F)g-W(NjTI[#Se2?JIjTEmoc_(^<9;i*ZT8)1XJZl`uUI!ki_^-ap&e`^_d.F9qAWM,!^TP
%SN*8I6nsT+W%X)lopU(Ng6$1aO"ScEd7VmH1_[rI0p=`Y[)rA)$/2ePiTmqfF;)diVG+1\*d8>bSOYr4/HqQK:ObmJ/E8)g[2suF
%I0OHOk/njlFuF[EE?>5\ApLh77f6j(6B)Agl'L9G9rFUYZ<]^WKZt`L>_;qhDYn[9=IP@<Lq@RMa4Q9RR>6t2k`(,\UVL0g_T$1H
%BWVb7#Z@o#oSnR+iZA_'[kKdtNiAHgZkKLR#30_7nbK*O6>SD*;BA>Y/KuD'L0K3h@lJ*-EVY!<pfbC]000M*Wq'8Tj5^IYIdH.p
%UqHCCE2Xe\pHCX(\saB&jB0+Z=E1g#5sYc/#(o<XcYh:!6%9NoKV#/'h^md\I4pm+kE6D``"=15E!pPEglbN'pN6U:?m_#W$;ahK
%'flLsS>=u>(:"d/_EFMjk]/Zm$)3<`SjR=M`&*?F;i,Fu2Z\ZM,gf?8%tT[?&bi$jUM`6(Wc4jCWh&B(L4P$8,DbdI(P=(X/'e_%
%FnJBoGu1!L@6Go8frqfl)Zbmu*>i3d"^U'rWG<b%-7q_ZJ_1.Y*OK+FEcp8&I\#3,&BJk(X0'LC:[rTDkRH!&dRATFBK_,G)GLge
%]H&1CabCJ!!&a;E``>@P_;M>lW0fiZ$E_ttTt.MWC<H+Q;ZTr?;4diD1A7g1!*fI\efVQ(d09%L,0k/&MsRH]p)"a+E.Sq127I3Z
%1EF%J/pSZMP?LiW,?epQE[D6QY<1P=fkAR"03Sr_\H$p5^E<$Ad3V3Z6<Z&L;J8mI9;8\@7Te(#p08LfISGVog-CNL!5Y3$%Mdb>
%\+UKl3[51#c9#qX&2f826=C6<%B(GJ(GYCO<,N1i"CUDc,")_NTmi<',[):$&Ki@BLBlJZ%h9a,V8dQ``&D08.oq([/sPaZg5U1V
%=54'-417WD4)dk\NT\Xk/1]AE8bU9:btf]db0SKIdT8)E,&8!m(<;4HUnVTY4I5fQq[!INPC=cG_[Z2+Yt<b"0o;u#;?sF6*T8&^
%^J=PZ1WLuH<BNTUP7i6%9aO>)iHE(cKIP[\\T2ZnWgPC<;]6_a)Y^B<:%F8tTT+o!\Zp(7iZrl\Rk(Gi]Y.K*!JQ^Sr4s?.-]7o9
%K4%)=,,gLghMfoGNQa*`;I"p`E:sY'2PE"EZ`4!-.P^m&8Th7UXS!;]*4n<,$f2&u:>L4e^Z:.jg_<>F>4"VSHo\^%b&(_$Tn?JL
%84mL!%mjr[.LM^eNEe?Pk9::V43Bn8`]Iur`hM.)Y?[X(Ki2.s`;NLuWjFq[mEc9'A&$udFgj1E"IImM4C!\tAa=&?,)Q]afU`Mj
%!?^;A`73(udOj:YdqQk?:0aM8(CM*8/7T68";*(LI[50S@FF77(d'24kV_?M4rQ`:Sc%QFa8pW[&'mu)0gO1g_F18:Te;hNRWqZ!
%h[*UlrN60r;_fBc7#QkCXCiY>Rd!>QpbgrkJYSH]_*QD04uM1=%WEq\5(2,hQ^qKlE56n.Z]noOcI(s?;-KM7KtW;CDg)<OW0SS<
%fuON=FGH<A#)*]9ECiV([g1m4c=EW,f"<T-(o"A]0`JDjaZB&Z,[[;j=+?g,Hpp:XDu]eL?k<70Fl(LXrBUo:Wibudc.E%(=7ima
%^;-sH/4EOXrCR%OW/+mG=1XK^=4bq9;h3E$HH0'Qb?oBsgu=Y<Uo=7YXgftE2LH3QJ3gf3r?q?_V03h?b=U]iq/XRWl8!FcbXF6P
%5Q=G[(M++AbKuo([%$[H($[)*+H8HIU-);i9sVU70.%H()kkUG\T&\u#i"_MbcZ%hTt@.:'<^'Oa]+>l-CBa@.[Fo7]"YV(WbU6C
%Ij`"I#_(Pn<VIuum9U2bcbW)D=\pnO,%*lJ!Lh`4+ibR^bIWh-XH/0H$8*X:KrSqUd87:.E\ng[ELHn3kcI:N*tWT1_hl4^`BZ*o
%0@tY(;mbld8sGeLj<:ri\Sm!^.4X=E#D"'m2qn&/9*;lP;+JfZRNT83g)buj?TL>@]1\JW=\%\8WHYX9W$*MID9BIpT0qG4&jAW4
%X9-7W76$eIKu2,s[j=dk<')h/7mCR"(18Mfp^/JNO3l,g"CjV(/[/PT0)Q,U5e1ZW)4Y's4n""TTC?oc0l$Scm#Xt]5At9E=_NEd
%9$ph]@PrmV?Y=d?+@Y(]'/8Pa^J@l@nY'8Soi.\&dB"[?!ooRi_8"oN_Cp%qAipr1&+ne'orpU$F<d<Dqj-$<,!Qt>esOCnlt:j=
%Gp\TBTLT<jKi.I"\nMF[!8<odJ2E2M,?uRU6(*DfXLnP5WIN:k@?0NsKrQ>:ES^0^DD9-QS2m+i>_[#H(h3D^?1su'M_:S$$#.LZ
%8:#RgD8]i7mag6LRhrj-._8t0*7_pdh#sPNW*+U<7gHF`_C+;q5I3-8+-4t9O!?MS\9.]u<hg(-p5`Z?C7mfg_gg&uj[%W#fQW:8
%s'e6Mr6J1]s1^J?:MM,f'D`s]5eXIcjmPLOKasK>Tc8O>VM[?8oh7`&J@Iu)ZqQo)51ptWV^u$.Ki$=C(=s150S<YI4"/!Hr?bqP
%N7r1eTXupAaJcZo).Y0mOf00lg3Fh-%g9Q3O*h%%.M?-;['L4?E`Y(%H/W0QXqIT&JB[,5p>/@M(KlmSP_3,nO9A[0O^;OC]a@rT
%d]`A<OG4[V:1Sdq\)H,SC(^P/nW.S-7_<G>Z[1ra\L2nA6Di\EkG\:mCL).ffC`s_bj@%&Un_2n_WC^mj1L9@=Z*rO**%-@J8g_e
%%^^Sr"!qpahe*($#uk9em41+-"%fGG<tMAn%YQ+.r"*3"@buh3`TjEj03*`Sj2N=\,k;N"@jnb*8UWd_9O45t)"benZi@J#F'HXt
%O-a23(LS!+:#KkY0bnDFM=P7)\2KS=_rUE"/]:PEkk_H`eDO(JV5gu2H;ph-HYe1u1*_jf*nDFF]K@])71U(KX2E5N$:^cKcol*<
%N]97>Lf*P+<4Df,aXJ/Hi*>FgHP2QVV)U0B?hdB#`"Knjhfe\1lE(+reApZTO$0@L9cmgIpF_5I+Vt05<Z"\7+?dY@,mn_8cN('2
%[liQ'e'+MmPWJSEPPq:T?1.enDeEnkDN6SlKPG]A'iA-;l`POq:hI)UZ1u;TaoTX`Mj"Q=Aa6,QW5\:0>jc"s-IP.;ej>7J'jse=
%hFdTD0<5;mW*8PAWc7(D;8JCfZ:VjIS"E`^H)=o2ZcbMCO\^%t/*l#]G8YEhDi/oj$e>cH(BA*'Y7fRP#_`=YVhlAT4`]+%+AF)H
%)?-!aEfc48'gf^*1iHL5$LT:G!K?G;&QppYXs=,[fIW//2_iG<Y?;O;$nVU.jVe!GT7Z^<Z>.W9EV_`?:S;u<og="^"S8r8L`c7)
%flQ=40lg2]">g^q8%d@;M#1WU)cF5I]2E1CWhEb:&5NVQ`FKGLAkB^[i/\m@i5;"eO`\-\fK2FW-C;bgXf*UNVM_1RC#]8Y04/ae
%@cB#d/n)Vqq$1Gu9`Q?+[D_q8=[JnN>CqG7N(46ApLY!$O"6WiJts;>%;`6R\;Lfn)]cQ<&S>Fk]Z5B+=s_=-01R')$jT$9.<<!h
%G3V9>\]O,96jSQ-:]LnPrFL2OK.FQN8',"75:E@GS#caA\W/[A:&Xh0*N(#u3FM;qb1kaYZUC14X99%[:fZnC1;@$O%J1GS3%8)f
%beUbqJhMWokhh%+Ctj%4bp7m+PaF.uWMuqO_?)5V>b`t^ni<i=8ocZ81gZ2A7:XBB_IQeT[V#_#,*cER!Q'5V'dam*'Io?0p((M,
%j?Hm7i1K+G!nFh\._0Ij';$*\%n<$J,\'E'K$K.q%uLs*E*\WL#n@HRO<3^^H83sl"$@upfgR/H&S]&:1=A8Ym=@m_m3PCHX"/BM
%P[/qN<M-k"*Z?`oC"G\m`n;72KGjY;e+]kF'TPU\s1B="<<OeHW?,aar9&kL-F)PH>><T_k$&s<8lo)R(0sdg/I8tH%ZbCBJG0`X
%%q[*Gja)VTjUL8"9dU"NHu5NRQQ+Qd'iU:F7_Nm7Fof7eC31-Y58_2h`KWcbH8kE[\$9<^;9Z"m0MBWX)S=7`+;[rpj_)HF$[FI2
%^T,o9K2iqUOe7P4k$&:a\CK4Fj$WaXgR?ROSFI50iXJ@-06p?Rm^(qWFjZFuAodD60;6_6gnZgFcS[Z9?p"7b(F)W3No81llpL9-
%VCS)SbQ-dh[jgY3f2G_R>X;:9_kj@HYF(2g!').h3BNsU]Hf5UH%[1XR9=b8D]6#3")>Q[]2s'-#'\MEh2C$-GWWK*3ll)+FE8;!
%Yi$QUX1oWfT91$D]Q'g6d_Wb>jV)!&lN#ME*do-?%R-e!S3lZ0Po]>Un19g"@tKp$X#G+&QPp`FTb?+7rClWFl45l`Hrs*uW,.cF
%;/<`g]5Y#gC%2qZ\>%iZAc((u1Og^!=4Pa$,W2Fu-M3r:_'D6Weor9=IX=ME+pj7J_jo93pr'6eR,BfLD/%H3Z-,2!qf$&%!nFG-
%#k.!:T=En`&>g0W)PLY2l#m*T)<h2fSg`)5Z&Zu;TW4o)%8i@b&AkjfY2u[$6SCbpY:ZtBPnBRHr#9uaTa97Oq*mA^:baMefeU;J
%k3"?0Ah[^$#I%e?4*ZPqo2qe0^)_&uTe5"D8g%%E*/DY%"4Q[Ve1OEURfM]][%WrDp@YMgd#l6Qg#WPM21]r9#$a`n5-W#VjFr7_
%<P>(46q\k,AGN]fWr'R3YIcU[VdtQ\C4EK9cL?/u*3aH?+ene?0D@E3i2M1dDUNnD:'*?/Raj`ID'C;\r"F6c;+_Xp=&mc-!05-\
%VHF\@fE5d=f/5B]glRuV_;s$L*5Q.S$+i$tUu<EjV;(/FV#BGG)QFR%'4Lp(CVA4&>+9645Qo5Kjfjca1Eq:o/=),OQ(YJ/POqhc
%T,'MSd59[tgrG.=fcgNbkk<QF.,KP1"Xdbt[mulacA?N)drlFY0iBf!Uhu8t/HTNJ79\Sds7Kp@ieJAdbnY1uea@hhnJm8DNgJ+P
%>C!5U8,2/Ps-]_,lAq.jJ5Z07+_gS4SBc=rWS>N+jTPYJI32Y/Zt+.U$1CnBD6Z1\fr]m!RRA(YYS>hln'crVC^ao%er<LX9F[hl
%gkis<R<ajFASURBIOM[AK:PB<6h2V'D3UFJWbU6n'5F9jWXX5-1S'!a_9)X!+ED);Cu,4))43>1hhaIKrGe`pZ7)->W7(#Ubk)X$
%Uq4W;1<57=W4B.MDP7u&!KEu60<V]l%E&UV9es!$mO,_.]1F?fepo=Y5:iLf]t-pj-^M=oKO=HshJ8?e0nV#qk'JEs(KC$;%KsY2
%\25&qR,'J`&6n-/ML(AIJ'T4q14.<T)YF/,#/[>9#"pKlmIi?pCWYf&FOca_dOK$M96j[1R'S:9(^]pp'*lctJ!\<k8@9?]9RL'P
%HirE0s1YPW2-;s.-OdK+AjoEilS5r>Md5sceu%ZS#Z#@)CkJULl9_$W/^/GRFRN_sZak]@o0cVN*ele4c^-Z'i6@9Q%.Z)e-\.8;
%+Zu@M+c._mGeQ`U!+?08Va]mLjeM#hD?rsse'2nQ_AMs56XfhQ$]/acdrl_9Ll8&5/_0%<jX4rq"'mB[=nLCCAb<[s?+fVW+7%?6
%!:,P]$uLGqA#M;p,3;-Y&b6"GP=-oKdXAC.BNKQ0;PT[qc7l#*8^[,DIC778?ON.b(6@tKl-_B`e_OE@2`1ShF3$H[#H5,n.ICP"
%CLm+@7BX781G2*L<tl=,lDhA7&tGTq28^B5n^ROY?!c$hK+1TkY5oks-fP%:[X%aDVZ8ockpfT"hhGAfU5kVD[:JPHMM]PMmA#m(
%X;&qu=d$mi;ec@ZE7C[r$70D[/);R,Teh!NSWa^!(IIjb8<pn)OrZ;XGiRD14'88.K2h/Ai"DIp"053/^KGi?8Li&(+WWPUQaoF1
%8pbT3Cd?XG!^Fb/AL!oF=\\YHPu)-f$?7ZS&_R+bEWBjh$b9Fq6#J7OA1_Hc_d3!_KViUT91A6>@!&m3?KMqEqamp4>"o.#AMRYc
%pi6S@YVQ./<2h608N(,j]V8a[2QOJ>lr83,RSea6D67uDAFdFS!\ju2CQ=4In>qT:1W3`S+"[Fg)n1;JfF7V\SDN9FEU#PScX1;!
%hUq$PZ+Ubt/DW9#l>r?^`f<lQ%LPRBF1)'QRbW[.$?XC:7OXPm+`U5shu#+F91t=FASj.+fqp3o^@maZ45ESN+7?p<'LJ@U,+SbO
%9jR7iMG#[?DL.)<#$cA*>iXco2iC#f(FmN=U5p&P:])]X"[(;lO6NNI_fSYi^Vt((gtafB'nopas8'IuE>l[m('n0c_K]jj1PFIT
%_3/c;@A2k3O&1J<0`jB&)'K/\*Wh\'b+RMZ_E419O-YaKSrIu6\gH"<7IaO"CUUk-WrY*/71js2&M#NO2Tb>4Y3DnR-8@#p4Y-0'
%0L8Me--?9PRR1unB;"?(0J-/;^XZp,OYG8+!dX6=E[rrgK(:9=5]Kf+IT!Xf-!$a!>]rar-<mK0KX*kHg=mS!+4[qI:oRjpq=LV.
%GPR_^X8/[G^?(B:C37T6*h5QZLpG[Hku,P`0RZT&MUhn)#ZD5>o5iO09m;<,e;PjDKrP5U^61s!4N9!GVLqVYjfI?o&REA"<Q*6;
%)d'K6m.0:nDh.P#aVLlhk"F>E2%/,a&RPQD:Aj)+\Ms.l:BZ6[)0oMtIY%j`9JABdJ='-_&Gqf(cFSiD6em5T@%*fFn$AN;HZQul
%=3^mI[fUeF@]:S/'O?SI)<ANl(5jU>//s^B<J;H!K)$>[Zl4?1+BN#qYpc`nYOS>`2g/=u*&aiDI1qgsLG.kRT6afYkUC'r:cq`]
%";Lo\&D?NDk34ro"7/^r+a#e=VQM.,j";*S68M[ZC]*&1_l;MC@$tD.i1%7?R#,:%bC2Aj?n`X1(e%6H-Meo=dlbEQ1P68)U4SU>
%T&:B6(=FGq#1%b\UsrL]3;4cO;Ea<e"4D+lZ7EP"?5Nj[+V[B5'0-<:>7QBi_E@5d5uY0ji1Yr2)8O?T<#*PnA,t@.Y.9fQPRGP<
%jsfo18]VKYE=XRR5h55+<e]"p8RVEA;N1At!!AJL9$fY=He9blKN'V$h`(a.(<%i9^?0ZIK'(!W[f$dd\^Y,*M=;\N5\47A[V-nY
%e"YEZIJS'-[QlA&<Q?t%-mpg+jP<:jjIuXSJn9=R-VY%-0gBs>QXft@Aob,S=Tkm\D3CCN[\JO]Glb(1N)oqJ8CC&@>XCso2:I?b
%0CN@o)/Z?*VW4&$U?tA)kQ0B`qn5j^eq3r1Tn^"BB3b`%%_Laqbd?@l6:U'77k\n,7:4IKhXmZd)K+H3p_.>BQ7c1UXR>m-c-#MZ
%CeCZ1Um2\tpZGQq"1d@<j6F79JO.`=K+l6Np/!>t,Ys=7-MJb\$(*X-`e=0VLC"j?+/>sUL*5IplL"`t4f7/um]-3a&GjWSP2M!u
%RRZ3m<s\-5+oF_Arbtp,:FhC;hCE14?YP'UO84/.[X/^=q[J(R%_\>8gMQO;>&RP3DeatPFMr\J<'`BV>ikMVqE6I3/#(%b6,hCC
%B_>t//L0;+5=X,7(6)s])['pZ:faV#,2fTZ,b2([Z>%+L:Dip_0NO<8)kk$%;C!jE6LPC\C^=r4W!),n77G3thO9u5Ef"kZR8PRU
%/p&69S36bEgL%^Obd_+ip%F^RS>MZCWbt*Fo*cP:fb?aDhp:L*O#"Hq-9j7".*)L..J#cIK-_\b?tU4#%jpoid>-Lu#2k8A"-WG8
%mLEh<.ld&."QS8LGS*cD[N"N)J./+Pi(hZ09>ko`Z67pIN9I3#XkNG^&]Q=;M[I?cOWb)9UL7.#&G'S%!V7hkBf6F4=02U@TQk8r
%Hte6B*_Se)n@hF6BSfT6bSq@=Ep:8oQ-Bu4^,Ab@38)4p%d7`>0\^(*N-d<N)dj@RhDAmP^"!&m[KusHf]oU%(K+3/MSn'D)d2nT
%IiYak`('hTGWA_O/T+@62OId?*?pjKn-Hm5Q?MJ&!PkR"Ci%Z]ETe>j6Qj.'q6hk7$Jj@8"lgT%\:Y!LBXQuG1#=)P&phA<88`IW
%7AM@!EoWAPBUtd*019>qk/LS'!0?S9DHaV_!XZ:,<j-KWq)%-CGI4+$79uLm<^>!9_A@*<oSaH-Q"'e>-BQTI#D<B:^*>0F0C36B
%.%uSHQu0Ya$5bk"/o[]]]_!g-?Xm`-eD):k@"*6t^[Wu:o4p:On.XU]+1QWoM<e=&D"^FB6$U*8@^H>t/^LASGp@7!)Y0"W\qqYB
%R,a)7>tQ])b,Zpk'7f;U!b):d$KPhQW[=CpY?7-WQG.)i;'K]nZu%J\QoM9bN$*.8`8/#fL7K,TM*178`s'TC.%C)[&a#)Z6a4b6
%S*Q`JYo$o5O]ro:(/,&CH*@Fc]!Y$qPq22EbT@9jr+TeBQ70QtDR@T4E8Md7]<@mp-:+CBLi$S>(7?h:-@GMY(Ck^8="pouZJ#ah
%ICb:l:_#eM_qL*F_TX3L^Rm)<#(8r8*;'Ymo7W2>`-8,[,);]?8t<-@5YZI,r=t<Bd-r*HQ$aJ!+f@PXK@OR_$98r<7n@RDW+(R5
%*@Ni.HHll>729%MZ;9u,UX"\Q]!VqZo/Tdi`HlNW*V$EMkB4g>M88]+0nLfl.;ZIs''GM`:R%1\V)&]%6T)t;Z4WcbhZ8Z2iDF6K
%]T)gXeT#m/c\4:ep5VK;BrcJVlLh:1>"J;H+C50R\i)K!M:Q;WB;>61;-/l--/+!>j7AbT#d-<&L":E=G*(jL8\hb^iZU^6AiF%I
%I]\raBLV)Pd.B0B:&rkL)2Vm1BTB9&$C/`pfl#7L')6YAETX@sAX\psJC(tj/6$$lcl,S=H\<#T@rpd&6?8n+QH$51Q@$lLS3+M@
%GFIT0>j@p>/h9YD[]c3:N-("Om",fR6`b.7K7Y.>!c\eI==b^V\\]P)7uKcUkgR-D!3i8'Zcug``/KY1btSZ"&/;3+N8OSQR!&KW
%hd1[$+C^PQ3.nk1MXRCXI,BJl(<O1Hd??$r-*/D!b[L:t"J"ufE-I8'M*=,_0eO(G0rkU:equ>JqjC!P`$nbc8*0)?Y>E^K;7hD1
%0nh0lpBWW1jio@7g19IS+LKV'Cbo:U#(>mlcpJM6Jk%IKQf:21qRXsFNIi*-7>&<ra^'GBPqrZjVkfJf2!#J?TXs\uVp^<GAY&jt
%2k895"os_N0)&T'E!f0`.F$d'S07=(j+W08hCdo,\sX$^\l0&p:^>sN8ju@+&$s9o6Igch6?Qak;$`;]7qN*)_sU4U&+kuWgc%KA
%/"m?Q<Li]E]]`eFpO,$g*@`[qn.WM0Ns<&n+Df(CqGV(u5BcC;N-3pVKg^E0P*&]<53,uh>Gu_hZ<!9P[tM2t_3Ju_GtWJB6'P_D
%\a*sl*aY33B;K"FCMJGpiU+[<"IFodVjUiJa?78@:D0Ot/l80Zn@Lm/(Qhm)B)*T&`^6Bd_$?dk'nq7i6^Y%J*Y"4M'=@K30blu@
%3)uQ]6lT]??I&WAZ454Gr`+>Hnc3*1"3R3eC2)Kb:eW&8eJuBTi@%?$Q\29<i#D"7]"\7`[M[th2.M&?Y"dj`Wh_Q9*J(S]&C.>q
%+_QQrakQd)TN,/o'Q0O]V4Ta(JUUmI9fXXdXs9E9_(\P(DbkP\'R-3@YD,m1,(p*Y>e=%M==H0L,>;3>%?nD?KMZYX$HMP5%Pq:q
%G)6*bM$.EInZ*A4lO-2lq\B\%g@sa6KH*&M].;J;7l/WZo')u/o+Io@_Vh6B+sQss,J;1TCi]cP)24E-r`,>X!L+beG<BF/FdE[R
%]Yf",#Z46Z?CVgWP)sn^1$gD?MGY5YXqmP6mDU@%lqVL7%5V,LHB$!6?SOl][u]+759fBDqGP7Kar8@a#TpnEdWdJ[2IiA9bQP"g
%q)V?:0>H70L"I[XW:=UJB=2Q[n7al<o\KfOWLq7tJdI^fjToT17bGg66<@'Ykp'/pPesN^!+\%+SfD1:QQAp0&np>GYt4F+8=9:Y
%cpTle9LKp%3)+GkFuG<S9!Yuqn;RVsq0&@`.<porAaKX,NE?5&7ho/R\6W,(cE1EC3M=Vg=fi9YTNL8CfZD=5]9HBZ*3Dld=YE'b
%k:_c*ZKE]j,fK\$7hC;)jDEb?n42Pc5F2t0$Z"OM?q;%H#l8J?DN3IPbin-)#mh>,_$DLICCFJrC\$OupVgqBfKbM-<s<c'mT_<h
%\tlcrD_VXYL\E1Bmb:kQ_D_,27egp-BQrWR(:bGU%,`,NkBlj!Id6L<<`9XEl1Z?&b8SqXX,)@eMSsq*eA]Zf[ePT]pIcjNZJHGn
%QmpFoWtsB08bM$XMLfIgH)]*qWXM<3,Hkc&k)D+S_=)A6.$CiFq(mQb-YC)pg$l`8ha#I41,M(7b]Nlt4+\j^..7a;bRp7qQq\<)
%`j24Z`<<)?$A$(+j3[0eA3H#UkGhR%oAeHN%Ug1.#2](tiQ1>=i$96>+epl_^GsLOdnDdaG?q=?3@R$:5eCaL_fNe;XqL1g+tkPG
%b6fhRJ%/'j\lhDO[rY&.IW:&J`C*XSH&&L+(R1ORo?OT54#pTh9i>^\P'sn8p?tfU7&&^(Pu1tKAVJa%^(u&+i&:3#Rl?UZTn-?6
%aCHHI,`;@D"qWO)o#NVn<5>ffFbrC2;mW-@_W>AR5r#!;K&QLXbTg.0HNgUQ(*5$-\Tq]+7C*)A?=%E'=0n:Tm1PSQL$_,Na_pRF
%a!+.]C19%)^nAkHFqHU<D3f;DMS=f>WcY4++<QQ)F'@?ENFab#B)B_kMeQ>enV2+p%-?`qZn@CsKebYP>M5YF<X$#So,[o*148bC
%aLtR:/5i$)WmN3uaQTYK$>?6b3I6\7Vs?Fc@G%Yi:ZY7+QtDaTA_96lc.dl['T_bC71F/X9(Pd31!N@"\l;ne080PE%I7r`r_RE#
%-O:#tVpPCWL3&j\.d5%dOXDp!&@#+X;HR[`:,KGr,"FVhej,2@0.mA65']c9APHGcSD!!AB,T^,b<Tk0S%CdtFgRP:O(ZGI$(8:@
%.+H3<iM]Nr;!;qWD&3Z14*f(lrp1*(&Ur_DRm]UfF?lT&9\un,&0hSIE-f5uNOf?lbWA>a(:;RcA\W.n8[=Du!9kar.Xmarp>Wq<
%+p\0YMma",Dos@35hAC9@A"%8c3k>IB.oChFu_4/k7Ekj!-?rg,YHJQ6EW@`b-ek&cU*^"KjoA&Yg3,eqiXc@5-B9lqf'<e=rq+n
%;LJ`t3XqIIf'fg,L1"($_:$/oUoT8$;>Tm(VAi#cX*S$/\15'jW6ZPrhT=uXZn<>cNB;4R=B[^#.([GMOm[Y+8J">jnBU/n&FNJj
%.V_3*Aj>s@"3`_:$t.h?=o*Q&'74FYRTp+Qc,p4Kj[Y_IVP"P(NiJE\Lf?F>CUs1j8g`UC/;BXg3f,"<#p5J"*2F@3<u2_V`?h]%
%]X:muAO*ts]7]T2US=8qONGPKLJ!7$>jGitR8JE-&h:T]I-I=r\a0[E&_[.9jPjj*]W5<iKpuu)Rc=,8#X(XjQd8Fti=Xl(?G_,E
%o*T%L;>?6^N%cTFdHmG\nI1&$As\nXikOYbIhl_;jOBH)M1*D5@r'umYo8gia/-R;!+MA%LIAfgqWMF;ArkA\l&N/XMo[>"Z,adR
%JF0:C#.NXkHeJV-8#(<_o(4Dm%6\&VoR!)*-[of%5,un>?n$mM(X\#=KcD8$?V)Sn43DZ0+]JIFKHn(qO;%HiBCAQ<2G'(tcr[4.
%h.5!t!Er(&'$q"PS%H>k9sm5Q<G[2%^;&Y9B(8l$EWV%D'BH%<YklfJ#S/"n3-L=YU/5;6UG\"q@>0i?;Am_m9rMq"WmSQ0T'`7k
%-Ts3gGXi61a)_6`,#1B?PobN3].RW,6pNhdAJr$iBWT`R;KUP.CtkG:)qB*qNJp4GiG^H78`(dYK;ZuF'?n(-\!/KX.[.6E6]+WE
%D(IH2)[S=54pDRl!l&"192(I-b:">KN@ZUG)eUPm'$Z$Cr.pujq[M<C$MiPF4GD/8Qei.+pc7^#*[Ak_#pqo[E08I`>hai9Y^W*p
%A3F!m-RgJ,oT,hX9quBu8FgZ85*YT4?'UZB_-tIBA_Gju%B@P!724DWiXJ*re'58GCT9I=gsUA/ktZ(:S&Dc^Bg>0>Ctapf5H"<4
%@?f&V1Bj2+!m`\L;8:+$]Hhk/mr"!nFcT-iJ'Eq7k:hqW+p9^9\/o&84/S-nj$@ZuRgM*#L\\cq8L0;Oo+QUJR]`(smC-dRHa/K@
%a"8)uorG>JfZ'H8m>4tN'3[qDjW+38Hrg6rs8)AVJ"FbP)>iS]s2Fj4^7f,4qdC+6c%%r:"G'hk6g[G+6$?_DR`7NGZ&6Ds^kJ/J
%SW-kS3gF32:kTj^/t7CqWp4Ge0ebhNNcP?0B+1!Vaf'mo=I0`FBbRKM`^D&/<Gg=u]8_D<C4phbGhE"h]E%hC0uDs."WNWSGKI4*
%5"r7m<5YO.gC@DU>s#pEg@9)UO+MY!5:M%@fhd4m^@`+-27M**$^D=":@@V+Z/m3hr+<$GIa)fDlONfLB,Di<Ti(_M^Kh<+qi0$J
%')itq171faZgnMDY39s;\9[JnqVLY\6GCKB>TH528m7Mu*#Vhm]oe=c-[S!2k[H[dC)`#=Tj>g;kDGZjGW9RXoj'LrBF<7l#U@+(
%#_Hg?Nl@Ef*DZqQO$Um$d:7ttrnM&+5m;7Qi0%%tR'X&E3:/c'qt<b%M:a"$JPfQ]co>Xi+T&IX#dm$p9'6$cAi0>.p\EZI*1A1,
%F%)iO::$R"qfK%)<AXuVgTHW"<)SqP%3jRsUM#FP3UB*b*JB6,9*LOf>:gjglu=cn6E4YJ,X8lrSZsGZ\0_LM+&icIR"M-D]$=[.
%%l3-KWrH2VTBI1*Zk7f3>f?$)9nXNZ?Xj(-/'+li8L)'I1n(eHWRoeB11Gp.1_sKVW."p!(*P91`%I"4AH2"uI<j/NH8sE\HV1MX
%QuNVURO$'cL#n(`4U]P[nSbB2*^h(A_dG#?^MW:F46-;6'Yt6*r%W8_F&_su^KjIhq;rg6RS+L"DbMJVS9?A1^i#YPiPOmHDaGAb
%<d79SG'DANORm_-ELgY^)<FK.=Jm+a]<mE/,ZT5Pi!("M+UT]o#fg<]P!qijh7mK%H&b_SN$irddqEAaTBpI^mj^HiYMBkQ7\pJ1
%&Nf1X7i^5S'@X$4jAEt%O\qp5dHj(s-I51,^D!NY.h0Dg_&]D0OO02](QF;AXQU\#rG%"WFNjMp'Hc;<-9(bf-iVC-/TdfE/_6%F
%gYkMM$uq26,29^(@s075Sg2T7NM8jlhbc.k!]uG[U6J.H</.O))GSGK#fP3Gqlc96-'%.\rr>]Nh3\96kTI_4(EWlQ4m.(qON\*W
%fM"N+SIKK^lP?jaSH?fr:F8+2U3/qqd4k.ndf;=uThkh]`YP<V_F,WQ"s.2$Tcqo_0j!<fTU\ibl4Meg-)asB8i3+aA66V./5mdB
%)VmkQ,Jf\aPa-?"3:aG)M=?_Td+QQs00+0/ib?M^c#8%q7o1Lsn1Eb!@D$>tPkPl!]r3sZ1%'?O^C^?0/\P3VC\%CS;)]=%N2(+>
%cf^.F>qUp#EH?]M!nj#_0-sN9bP8>)X2mf>RWN"i^I,FI`Y8B"mJ@Bd,TZ8R%e232lf6mkGY^#^I/O;YhY/(1hp?+-#9d;ZISTdR
%VDr:ZR2f,"\a=G`bM!E"G8PU:l%m((.9tNMf`e-!KjibG(kY]PZL"TnqdY<%\C<*kr2j\WA_S[??)=c9]?)+8MgN:e8*NcqOF!n;
%bo^#,k*uu=iNp6A5O:I$Ah*8I['M9EG&(2&BXuAP-F2"5A8`]P2;!Fmp,(!f]E;tu`p@$F)N;7.McD$C!8jmQ5-*kYksWD/<4X!f
%F1[FXC!]%V-f189.iG,@GGku!e(`EMM?H9^RF0UmZ31&-h=LDTn&!0Y5VmUHl'+BR0C7^,p1*ep/fautq&Y,bE1OBS/%&YGDi<K(
%e60T0eS/B\Hae"6@Oqj&[J!7S5821:ZIcJ(?a?G&UR@0lAIS"VDsXLmr1!a?'<C0OE$.N6?p#_)A_I@2C^ONWFLi426QuOV1C6/"
%+A.J85QDMf3f/t$.Xf<eVLKd2PTE;<.V".qQB[$Xff7$eVLF01aD!1lU@FRr#'[J&I7MpIloDie;cBT8\/2IDpAT+j_I7242Jiqm
%BPVK?IZ1:k_,u%nhV*"V2Q:)]d#a;cC6:7YM1gr*NMp8aYa-@Yrn?kZDRZ^rG_G)8&+>M(QsJ"PZ3uR[isD3$aFCN)^2mqU4Xa!%
%U'70^ae-JRoI'!$1_O(F6B&7W^X[!a&NUKC[d$?n^*GVXhp>7.;1T6+"87QUqW5o]PP.b$:&G(>aLUj$B/,e;_%J@SMAa3X,5L](
%!_W@3/sp)X$oEiPGE0VnBH/Da9htK)['077&eL/0m51+cQILNq*Zs%,cnr[0eZCL1$6_b+bF"\tG4mA%eR14l<BYu]NblP9^mJ7o
%Z%8*,UF3u!;Pp<@?,?'Q\ad)=I[</$Je;rJo_$>&E,M`g[.7h,Pk5,nD`q<n()[BdZuk>Hf@Y;^CNa[N_0lR#aj4C6[cW0Gf#QqK
%B3#NWY4t)#V^HHgb&OM5PB?#WiFTT38iOgciQ\6q$!Nn_L\Bo\2%TTRar1kqncUR$N+;]ekluU]&L^49]>uHZ/AiW9=/Ui9XQ)#P
%hi:9-/+_(MgS=Qra?%Pg6=P3SU=D/8QGW;f/-59_iP^>G5aBbkf,J^6<Lnc6G8e:p5Q%mL-T#=WLtE>Iba^?$puUO^hKRS/c\hER
%A(QmC`SWclEdtJ,^@RW'8`dg`ePU)@k;3uplk[\YaCr!!;ajV;UP-K4N0)2/\4)X^5%`l=EK+G!F6;OoPaE1GdITEO<5[2#NNSL_
%](ocUUA,%<K")onYpHoJBqJi&$NFf[@;O@+Up7B_`mU`tM\DXUoC9?/=MdOH[>\jmjE+sc!FJr_!4EfE3`&k]dntW\8U-d!K3SQ+
%01Z:Xg'Pcor?R[-1fZM4""T?oN&)&+^\Ygu0_hJaIEVEaKCL,%6<,t%9nJ/\kHM^YVW?"rNu@hX9A6j3Lg)]$P@WF/0nA[Ynk6$Y
%*-ug-aHMqFC67Kj@2b(`g\&D6[pi_qT9>R;`%N7./go<JL,d*GbNti(Q`Du"XUZUdM,C1_Z7GLi>f>^?T(rZJ.YFt7UGE\*):'74
%DRB]S+D,'Um+CT<'VK_>7l?ms#$C(=/ZP^#7LPXGSEtnAeA5e]CV6jJ`3//dGPkQRAi*Pr[?.*XGQ\*>F+g*Q\#YQ,JLYbH%+[`-
%pIXD:qu+<2H\Hn@pmC'\V6R$&'?c,3Z]+QeM6%Ta-LTGeON!<X3HEbZXjZZdhgqLm:o?o]gdsEVe_WqJ<)ZK_4O].:QYh1BT$GQP
%O<[qU'\NC:f;9c':\Y7-!1$[*@q=HM3;7sB^ol66#s.A[FL)l)Z),a1Q^kFnIEWq'WJPfgbL"le-UrcZ7YS9H.\;Z2qLH86"E%-u
%#tC,X8i&>a2-)(n_8!tq<Y?bMD>CXn0#M;/nR!25U.b31Cn5%las]ePBNiRuF?)7m`B%,%1k.4@NqL27\O2O'8ZtWXiOg&U8MT*o
%(ugfZHc&\?CcBg#Q\Vpip`[+q#Umb7+%nmC4P&.#!s#:>Q(C;%W*l?)NDcpQn`5I1k+n;kTD5\,HPj2:)I^:-"r9\B^?[oK99$87
%Xr)`.<SuLBht\<@GO1GCH'bRE%=i<23;$mePgMc/3-;LCnRaZl?r8Z'+`>@^n;Q3WM4Gqs\S)EHmoQgPqhR]b3P-J3MIoCOj\p0p
%J;_RmCFj</MUpVW:Lg$5'gdL"IDH-CR^1a?o%gf.WP.pm-\+1mJXmdaDT*<$"kTi/8IcWnGg)r=@&JOGdt+p-DJP?N%J?Atdac&\
%Y6\hE;$:ae.!To$0qfmeJp=9TVR0tSV/dS+!r-D.CpVWl,u1#Z[j_l<L1t'T7#?)P)#^\_f(6XK'52pQe80#'=cY83PAq7$-oq,<
%\jaA\o*D3R&CX0gq$bk74(,1=O7UbqI_-^j-U^%8.?/urBKs0&"*5\P4*XDAJj%2k-_:\2*s!+pds<gq4<Gb';DJ@NPfd*8.)jVf
%>t7`G8$gTK!8u%=;3m@CL;!#*le`?Up^)n.f<\cO'^DuBXkls;5<Udm(cJ\(":#N,4OqaPU+lt9f!*)5ZLC,tnLIC?Z;hU]m\,LD
%7D`gbJW+n/+ZsFc6A>IC*LB<R#ZlWaRo/#5[e8ikXPso#)C;epmL1o:5rVV?k!&iu9V$_I;MYGfH0R\BJ<0qEVo&!<PitJ:;a;'L
%1WB0cUD^o89QfPKl@GP2.'@:s+P\NcP7u.ic1T$iYnMr^HS$N17o3jI9ecme`2dj)'$uGG-5WJ:@$2ifee6"nGO$(4r?4UU?M&O*
%e6ml]?`nI69iSDA)&8%dG!A5lfk*-N6G'/9jf<\<kiC=#T2;FBJ5D7B_'bb.Gn\"m5>_?Y1GJqMgo;g3`NV>>]cS\qERS7r;7LR+
%':d-$,-Y#;l4E>@6^l(AiL1@V$tCK<^9PsM[15e+W,d^KX*T<nMV;r5M^!\_@U+\.fO*[P=i1N6FrEkFX:Xd1l$=HV?u,n-6,I+<
%oDI>NoUg(/^*il2KkAV[$U'=G^m"CB!i[*NBbXdp1+'l'c%GMSeAemK[I(;=3iHJO/u,`,aY9_!k`ke@=q0/S3ue*O>mNPO7*H3i
%]Kh>?mVh[O+cRdN"5u>l@D%eaKiRi&cD`j$U5AL,O0M$M[`m5M4J]%oh9AtVO.As$M?[9ZJ0Jr<MO8!M0hU^.OZ,koW1"V\&C;/3
%_DG0-`m%p8qHN#libI?:IG:_4UVGBWRb*&r4E7T&j*2)Vd\rC+DJNQs6oRR6"qgpYI6_P1'`25_SKl:c:)UG'W=e33pF;i)8%<<g
%-MrNoNKC.OV94oQd20oF$BG\WcE;2LT?-+<d>@^QO#'V.?b'n\V(*GcoGu0.mf:s^BKfM`$,0g=*lq0)_@@8c_g$(Y0bCQ%mgg_B
%.[M,M10MbD7rh.f&GRK(IEmZ68%-HI=/bF"p18?j0mXcP/,h3U7#B=u0TP2mSldf.TUl't4)AAVVQH!!%(7o)3.V]+*=>2jl3.BN
%>Kfa]Q`/#`-u:Wbi"+*!X,$DC=G2d&&A^=QB'YKY=Bu!S+n71bQ(lq`5qV0&i8"#Lr!LQg)(.].@Ydb[:6'!A4//_$EMN^YK5O7&
%Z[0Is1-H.gbNQ3S]$b!6H*o'0"nuQr'(fDt2sKFJMHRqk2U+Um$7!3HHSe&O0DE]Co&_U$lfp9`\J1s9js2huaeHEeQg:"XOlBZk
%#D,a40`k7g;o7@ec`%-=5`a:JcH*[PQ7*ZZ7r_/"aTl+,HeT&`B>3P[jM';UI-(-!bMS-'l7*DM4%a"4.eIJ&3cK1_fZf<5aHED`
%Ztm%D7>&1"Y#8e5D([&I*oQTa&]_+-3)DuV(5AbF^IZJmS1F(ZURD.f?o!@[VE+Aibm_7<QJib$+,@9u$an>PTm[7SK,g*or%Y4F
%*FLDLNl6B<nLrPl$&04X2q+]NXo*"/)djZAKK*eGq\g[^S]cb:bP$ukl:49<<B5PU^&C#s\FJbP:-+2K^KVNBqjaHh.hCRD;'K`5
%ZJERD"5k\BI#ULrh*I4rNi5TK6;>uGE-RkjN+_5(01.a;`#^+.AlL,fm25BDQ<D!n-pk![W1uk]&Vkt,n!YJlI\%7^Mi#oDlf]aa
%;UeqN*`?*t9p$eQ,)*3KY[<<%8>_Fm&2iG$/m83GK[D@?d?omP1K6,t'*N)"!"fVO'*AOpf+[r>?&Ec1a?BM!(ca9,ciN$tllb#2
%6;tA'r9H2_/sJlc1k3_:MJ,+%.7snV'C(b;^]\pHN$,5CDF^`*Ub2`L=eX_GY=h"QK(35p/Ca_?,"#MSD1j0l1a!9NX?],_f0MlH
%)pqH^-#?*1ph;AkM"[mhnl$9ql2e]:/u*[:Bl'(;0<b?dm@Ep'IFX3.)a!G$<3M[<=GPGkfdN'e#R1>BG4;9FTR;I6(6TO8P1AP6
%niqV`8URtcQG"H@-Y)%;V9]E?hZe&%XjrC&hte1@%J5gsmk[Fp[fBpM2b&lMg!QYd2-s]p1-Up2go%f[_pn.sbXn&eoDOUH3+pu\
%%%/R(GNh7S@QQ)(in<cj%&r81iIG#6Fo>od8DT'e-t)FgL))Pk8qYOdN62a8i?=Y+Dpp0kTj5\@``"RH=W*)UYk_L0r:]qPJ:`+P
%bBPs,r#&(7;6c=3)V*lA5BgCmI>l@_ljt7%+[bA/Ad3XqCGDS.AU$H91&EnjMJo+$cb&@3nSFVgj+[T*rn:a,$O;l_L\PPAm3kRF
%hD<DT/bqBrG5c[?jY'$B6DM!F$jAY>]Vf-T>%(@XNBW1j/g\'?1TW%a`;i,KO$(NS..k@K*(+qdMY9]!=%S=G*C9-/=O/"ogIj(&
%>Z6T5*b,l]9gY">MUdD=/1Yp/B*!06+Nj-/4k^cGdG1O@dAMZ&1pG:@(`@Y[&L[p^rJ,'Co<=*_!qfcM*0q]ERoaJ'EkcU"IWJ5e
%aFS8";gD)Ya,!J+?0I`Y.gA&Qhm^VL8sPC(l.4i8H+q/e4]q$#[['/&d!Acf_Ql!h=>#U&3!X#E>glt="h4n4k9Umhn4Q"9B3a;N
%:k?ZFC^l_4]4?0hJVdrTO?"XG$c,U+hQ9Z]=lr3"j[I$:#!ie@7YHq/B8miMGSI4<;]LtX:.X&_fqFm%A9lNa8Je614Y.eDk2/MV
%btgFH$+K%P4p=pU4k\Pt/j">oVW[XW3>O)+=**+j[S5_[q.9n!2[)C<S@I[2:mXTSNKoRTP:d1:d#X_R%$O&PKS%I`O*.We"6q76
%B0>n)e>$l:OD?Yf;;6Q,GA_`^rd&%Q#5-5&h_p<P5rG[%'E>e%8dtRSP^:6":!)5<aERrOB!Q,qKQr1aJogp,[79;[`8d"HfgP0c
%?QoC_gXC60[Q7l*WdSZ`m:)h/0P'Onca?sb5pab-?87G9a%9^*`U.7RIti"8D=^lHB<.W[k>TFQC!ol-UeQ,#h,:3*5kZ(B<DUBl
%4;]`Zbf#2BDPa$GX^P*b&Q9.j>.\)`;]e0Km+,ZNMG'I$@c$FHjE"OZM@VT,<)Ebu2fCPD88qZ#de%E16$IU@P_?sfk`).XNH3TS
%D!V@rf'd,0U81:\G*WXN(!L0A?+iUhA46<:(kAJ!E@3"I8'B$%OgF1#I"[$&);CuKHLAu4XBS!%:jkqbmntm7T3+'^Die/Dpt.b.
%pkXql\Q_Xj`k-ATE]k3PE"e;!CY+8Ud">Q1$Rrf-(@]AdXgrQ"NiU'TOl*`JIfD(3Nm')]V@KPs@locQe=AZ^[0^oCDFc6s`L9na
%``)cH*JFGummlGF+?gB9fPKPo;H:2tO0<#e.FQG`)+T/G8A_ok:0b!jiC7E5WH_rTP8!S>C5o090UDs<.&g`^f?24Zr8n:9le9f=
%Ubm7X@Vm0OnQ9m9PdJ@0q7]4+W3`suiFQ!5&(*leb\.5eh,20M`L^C&1=hqBQSCg.\(G;fDVR;GXGBg/lL,"4>r`=.W-okboYNM(
%N"Ku/a7ZJfj-M!PG`qcLj*=YAk1$J["A6=O*]#_8O!R-[:3$<DkTttgUGBtba1%T#j'/fP7c/X\OWV;_Ks3i&F&bVL&G]Y3dajmf
%6a?-Lid<:HOh=N1B#J."2&b_*Is:]Bq'gHTRkNIRa<,bA[?8b2<plE>;G42nJlT1Q62RS8]RkD]i-\=j+%%VmTkCA]Kn*qe6LXnn
%HD&/@p/-#2qsF,(NDpm*A-^Ra?_8R%;o>+-(fngi_$g3#B>m*emA3L!d%_X6p6;JKX31'T%:J"8",[AGD?;J,0H^s.DeL1'ahq@P
%pIY^GL]'!Kkp\RFrX<08ZgATl"pVHm1802@MOl$C1*,Wmi>@Mn/>7$m7ar,)],gNe.!MVBUi`MR:f([:U*>]fBF9]0Q'$b?$&8'`
%W.L3!OmW\0\<mQ$r2f_u@Y4:Hp=?1h5m8a\9-s0/<+*;b9Pkf:ji"].qT$>=_C;:d._'pr&IbmM='5UR,`6!b"#GjO-RjY6'+\Fu
%,WjYq!981+!hlVr@Fos6(#"F]Bt#M3W;Jkripf1JKueaN&lN_(E@$!aWX8lfdn_k!*AG9nkuiL5@QX!eL#4#gg7eN$ZnHEYjhp+o
%boumcP"$;^)cg%WVI?khWa@]kQnl*`$)Ckb,*o/0rl$l3TF.m!((:KNe"9a,\81nScAk]LpA8_(/2:R2"Q0O2fm]eMHK%YQN7H<k
%1(JijZ!0\u*Ogl,<L'rT=uMV;*C9#gmfu]Y;4a5Z<17G.Ya7+\'TBub%ai)q8n(#iFKGVX=>crHX](oQdE3q:-Zp0(>Gr5?&n-P&
%'-LHCo!q`A'GK7/$>UUr(*eGAnjl#)MVi^fbeCL'iPVUQFHCRgdqV[r%\A*8@<EMAIL>(B^?-g%FK.]"ZsR"n]R81Rb.
%@geag1'DQN:&>,:^SS_*Q$9j$ip#<&WjObbGhr6\_ClDdJ+eF(f>;Yti%YeJ$hFbHkn"/O"a-9L)o6CoUG7:<!Xub)=1qB^!>dQQ
%LhZ`e1F$;<C(a6"qh5hd;r!F$5`B?2K(.:h3<k0a8tnp1'A6:P1;H+Rm6r@=mAT_rYIUsb7p4HQ!:mPSl4[JHC[$n"(a]IG!BKLJ
%RANB[m1V1j3`Y/E%DOl_@8tL"*:[(X7Wg9nYhe/*]*MVK>Rh]jo[(M.Uq]CZa@EHB/%\(bA#4too\`I4[<+86o6:mG4[m:?o3M3.
%1JXc`/HQk;i5QRk2H+X29$C]kA,(:+;bsXFiX)Eu:?)Y<$9[]V-LIi"jS,Z/#PA5.YU)QFN9"%8jb/V``K-uu4-%%-Y'A0"l0jC<
%H,\Z,YU+\Q)m<HLlpE[([gS-BRI7=pH@4XW:SNJBp%-HU%PKA%0hCa0d27R[X(I76-:.$R&d+dKnHc;'E1Vjq^-PXP2FB;@A!;"+
%n/SGqlKB'N8;-qTMYR"ubNRS#M[SJ@R`F0)YCM_,KH_QTP+!M44pFEJ35Re]%Y^,Drrc!rN%@?SGT*c8]_:PL_Xt*"E6HiMTsIgT
%B2]Cn&'@Hb[OSKPbu<u$I0'K;ClE-Q1nt8ej>*_36,D^keIL68iaiJ&hk2GUrEoZ>djIuJr3N=Sp*(^&(=:OIMQ_gFX1UVNiLZ/C
%@R-b-;?><"5Dsuu&1Ekh+N!'/5gTV&^0eBu,"R633)/c@.jRLN''e:9"*3qj!pmEiH2+CRi3l_o=G(1'Hhk@Tm2%@m?:o*In-I)s
%ko_),\AS)db!+XR>:#4R8DmV9"`kLWHe;nj^SN7!SRf4j#WA5sFd6k\rl!fN+1lT&Mg%]]of']h?B24[3jc]_\As-M:$1Wk'0uU4
%c`SICj7P\lVur:-'I=`_GiQW)mbJJXl/gFFZ'Q6uf6HOFYf"SQa$9!./qMD*2)$e.U8LG@8WKI,!6gc>O)2dp*U[Eg;&F!cO]p%s
%0LP6JSDe`p`(R05HA!VJ5g\)RrI-cEO"aGsq?u8B((gL*0k>WRq[OAF;0XiO[D_159!5QPc0_jU=lhm#bWErlC7]*YW@Gm&&N5P$
%2M'Wk_G_,VJ"e(3k+(2#c%*LlgbIUDg5+gX'S*.Tq<lt1>7baV3UumfU5\6;ortonq$jG.GP:m)*23kXH!h@A4Q^A%]+j-+mVu+%
%A/V"-H,)XAWsWK;D>O%Z</Km"OT#mO_Wph"D$c*@*oR<%&+*/Wr_qJ&iCNk!IZ,c`#9KW%5f0/*XE<uC"_.1@S@EJ8.S&bV1:jLt
%7eIjPj)"^Mch2e,hRNH&=8`"U:tSRNeA]<"XLChL"/]So/1S[[jq!mJYG`gfG94O[WKclTO52pj#PfPgh+i[M>a`>.E?]=[a1+Nm
%Xd6F_>YdN=nkFnr^T"#(75\B#_./lnj)J-s-KG=fNp/nPNujgm(]_iUpdF%n&qHIsNHcWhisdbf'F1=DoG@h%/X_`Q&cLo3@9e_s
%b<809Z,%eK@H297,b]nk'FRnZ!%$D55F--aA*hdCK@.c5[O`:a,@M`6JYri&kGj:7fYBkUAD9'faSU"LQZ>2W)Vc9gmEQJdf$B+3
%gE%SCg4_<H<p,3W8%'Apj;hsrNYJ=g.XH)qb%!Q:kT2Lqn3_:%Kd40l7hj>IR+[A.ec,guC<#$@k^=7Jk4o?M1B2`9p8<3>U].fP
%=QOgeoOZR=1u4]kUC+JL1rp-.&LKd/3oWt*dPE!s)V^k#]#:i2Dae_V<Mu1U!NgC_6*mNgobJble4hJ^LXs:a1*?`%+*5p&fUG@J
%dkd%D-!AWN^o(WhH<b[DOnOPqBta:^>I/&EpC6@>-_:*D`#=etm8DA8,I"HiGPiW?L;#__29=F2.GHTa'iTtL`ZqjfVCK+[?13;q
%]d/:L`bJHqlV9jg`(bSAm_Gj^-G'&:cimA('4cN-Y1<k1>1A_mij#9(B=KJnnp5sc6j/nlG9n;rEKa/o3asP2.8$_tK')<WO^$G;
%@;SZ$nAL%6]kX"K`t!62i^?C=1'gfb6lIT'McJau!+P"nZ'YEoV'lNV40K*B>rkl4g7kbYFL^f$?#iGeqhi/sGeL-:_g%Dg7"aLk
%"!GM"[<N/)p'Bl#VSe7FeP`H?TTA')kR!l.X=rkMcja\!]D62a`cks4j7QEMNFT2/MtOKEF6d?EnLT8O:nh9L<Q(+0XM^uo`/ZZP
%8^+>mHWOp*3.;MD8E923@L%>8Cr?t^(dOMu(O6l=+?[u708_):)L8E*Em(C'miZ=$b^&kCCoou"3HKNIFrtBPDtdA\m`I8G=-P2_
%mQ93e=5:?j9d2"Hb<[](s*fiMLNYQPb,@33a9@*B9ua/13tq[TrHm7P`hFI[Xe3H_d$!PM`pAS9N\f6qG$3,Bm%3c&]111,Th'%d
%2>pT0&-h@9htLW7?u`@g;0.dG:_2SuF3'/U`[=`U=`Tp_&T^pT4gn#Iq`N0,eLi-<U-m9ng_H\7:X#i_)3-60G-pu;$+5ZG-VT9)
%Ogfh1fd/+@OtZ"dUG<`0QR#>[WiCEO^S0`h+F20GVR=d;4(#8mA+'FlB!GNaZVd+XlkA[POrK!?iW'VO6c!nJQf%(gjYMuX,7dTf
%mo]=^BfVgL,\<?-j5Y1'8fD?Oga0iYEBG06dKgk^,<d$T&30/P[Lc@'gri8[g`rBU.UO@8e<KO[1G0.)6gf`Wh+[Lofl(je]2ab$
%ZB;=Wajp2Q9u5p!S[.a1l`@5,mdh(9k?UaRou(]dVj?Tu]`m^]Q^A\gEVZodg?VE,1W7L)-O<On]&F9,h\+7cZWPn2.8S_,N/JR^
%l*^;fMB-2uG9t%YlnuEWX_"F4Pj<QQ>p-e3WgUWSOTXEPa4Y<M&D9!X+_<+$%_XFpp:,6hX<kBs&j(kV$2J!O,FQrZ05mPU?tA;k
%Q)32`7sI.94+.f@OHDDK\qc5.oj"t&\-(i\Id[(GK"I`Fg^q^oQ?@eqMEu4o3H@Ed!H5@.#fTAaoVJTB`"/Oe58r!>r5sFSa[JZh
%]r"$K$tGq4rd9@YeKf=XK0Na6)s^6MD54fZ1L.QsV':L)]^Ij`rI'dB/m9A_$;Vk9#>9I7.7suR3D@M2f:g/h6l82@GftoC>t-Q*
%nj7$,,+r+4#D$IrOGgs#J>A!/E5OR9)G*6[n7Vq-A.elBIo+A]\>,"ZL:#\H@"ka4N$FM*N5hH6@E?/4UDnr)6K,h,1;oA3$Z%?=
%VFI_<G/&(u/gj&I'W7Ud?jT5h6cJ>c#Va.qi75#DbXf-S7uQ/e0eF%RMR9e8&P(BeC"/7P2ih+s"uNVb`Md%DB)8>aOKO75^.&B(
%`<1h`X2SF]ef$qjB:L'X($ttq>nd0!]A;CI\U\`+RLH+>3;,-0:^:PWAT-6[aLeh=Wq(kk`7h'6Jo2@[n^eY=a&jK(3pkX`1C;&Q
%4]>WH,MF(d_Lt,.Db/^>a%0dFDU3e<Bi>$qY/<hP!ZNuu5g=G>GCrnB(_JkgSU[F+W2l.5]ul*&oGs$H&]k7j>gFk:a5(V::tFq`
%VKP^39LW097%VA'`JmSR?+X_o0^'[?WD'9..5<&t3dc]RQp3)<*(8bb&-N="V2P^CU:[JS1/kbo_R%`,s.5@uElDlW<s!4I6*>3i
%2K$(F7)h)Yb3bf?I#nMgOI!^=&]Ul?n-XU6kD1OK!%43ZhZ52O$7PtB,0QgAP9($U?0DdF3#GZ#a_fgo&UNsHW)B]]GD.qu5D4th
%OqNWjSDJR"as7=B91^8?E[\VFe_;rkY&*X2bsYI%me!+tno9X_Bj8%6<#.M>HF6j<@n6Rrl8+qsmtrPt0>C0YGladHS2RaV*r2/c
%!lsgKoE.`VlLJP;NO]X4o5nOFqE*/K_"i=ViSXa<AOe&Ak[C&]$PY]MG6LF^NID&`>Nl1bAi5akaY#-O+:sIt^GTcu/+fDX9Xh`4
%20eE'C'Lb4F15k;_(eaf"b<4CE^Q48I#Fa!Gfl76<$0*/HA5'K"$^i_V22;M<W]M4PSOFs[i$3nSZ&>>TK.bl-m,MC%1cRN/FT@A
%.OkV&Wg4dfh*si:)=YcQBrDsFE](d"7aOOWNJ/=^fEgJhl6VYL%((p!LV4OG:]Hf$T#Z**Su>/PPP>u]JeO1FJmBuuh!87H$2tOc
%3ZIK;OY/IGIgTD>@Ns'OF:>BTk5jN+*ba8RBlR+2OC9IT8+&pj-ecj&!Top5dur$pZ_pY%gkZQC")lm>Q&aJ\qtSL"cA]I9c>N.i
%(=O`i*^aN/^iRqJ+3p2;eKh63@F\:DUO(qi$o8-e()<U7Ffd0DbC^*jP2+/]LL7NXj@eX0EP%GIIYoN5Ru0Iaol^79]o=_GJkMl'
%Y=5X^hmY-c>2tL.Yt4a0roe4qT3DYm7Y43QNA7-Ej]%&nn:HmVpf77WorPeD<b^bWE(Pt`PNU3`'!^5J(;F%ERT.\$G06DKX47+Q
%3PfI#nDXrDEg46K3qi`a^$Bnr^YJsH8H&h^s(dq)13Dt-+2K!Y,3VeYSkVq21lE-"#EV4]3*3b)O:iGMLd`XD4hqC`$M_Q5Wt3F.
%;n0I`hao1BOV&45s#B_A)a`6:V<fsr'Sk+&HuGP/"@]BOWFh7=8>!21`4*Fs)/%rpQc)lS'cp&V*F't&f?R=leE(IXW;OG0]BML(
%=bEWgUhDlphOA<2@4UM9dmtGnNOAXA3cX[D"Bfa&+bRWE(Y;sA]#06U,pe&9+G$`^PkD7QX@Mc&UFmAEXDk"7J^r(cR]h<9a\L7]
%[.0_hoaH&o:s_^jEL^_#@B5[fo\@/-COH`mB+Ze>2dPsOaN,E#\McOXFKG?dY-f6W>WGnL3j`Q7*t4k+3GLc/i3&2.JXLtT^n8#f
%-aqQAKNA,MMB5O:,3\/#QmcU@lkQ?3*GoaC:Ok2'fB"i0-dj07HpW9SW?E^15.mDDd,QNI?[6ZLXshl2"7Pqgh<(c&9<GYG9$o4k
%)JUuYU3uOL,$ce#8_^;dIEitMg4_otE5A<kYTV#B)&D_tR21'[.O()>ql0)ncZ#"QKX%"pk-/WnRECD5SB<$Sc@Q6#fi+*JRDA<K
%GBW+ri*NSmNX2Xn&B%8@_^iMYNj/o:VJ?[gN#B?\UiD2,RCf8uVnI$1Hsp+N/8@m/B!L4??K6)&IuG+Z9Z.5pO!+9_%=>Z2a1HT[
%nemC^W*D5@^osKYmorr[q8tYLClTEZ'MgC?<N]1DY'h(m]X6hZNhU&<:47IXHmbDI!/$`V;,\EDTL/;@1<[pB0O'(iDV'TH`0rk_
%-CX"YH4oOZ"KMbE24E)ZV,Y(YI7RO*Wtt>oh&FC+lcQRpF$170CF'/&(<P'7f20cC8<SiVCPJM72pJZO)G=uqKW(-6@"2WrOdR%!
%T*>#.eN<fdTMi`A5IU]7,VW7Rf6qO!S/s#J+Pe9'+-?(L@O'X&g*1N&P")&Crlgg^j:nYk3:qV@PH;:(DSJ"X[!pMc?R^YmOG9MW
%A'I<'@n#0(1N'Tk:?W)7L.6oZ83!'H)f'<H[0(R\^poS;&";`4#F&bCRL6-e\Kp.!;_q=3`*X>!8JrW#dI?f<_RjjZS3&;(7]V^@
%q?rJ)<D)OI]!J0@="DB)`q<X?-s5rL!uXc040''H6tW3[N'2KPG>WR'QDRdP;kL_<"RmK9"'e9:$7Z5g1#.>6CLo+>8]nX3&hp,&
%'oMKRqV>5L(6I(KBjNN;.:ifeb&65!;]1u7!BU(5f[#P?L6F-IKGZ?\^q@ZpTK"RYn"9hOMHU::g8r<(Jb9l=P]VdJEEqidV2%)3
%U:g5.AD7sP#7?7<UR!h>Mujo?DKe7D#%QbsTT''#AR$fW6t?]:U%,Q)j9&e^>%;jj.RjfWX*L;$Ge3(S9W#*(TJS1?L>'?G:lHtH
%2iJ)Gl*\,L6&3K?K/O2kq8$tGfPbs-6r++fnVjX8/[=Ms\O#j?n%HL2DbfA7k*U=\Z\M[RlI2>mlg$D2>4DaiKf"T",SldUpQ^<1
%?9sbF/4YM^A.N0KZTL^76H34S0HaGs9q3(*')q!)#fJ2kq%uD\kZGE%3B7oc9Otn/N>`ms0a`83q033'=Dg+1f.V\8mj:.Z43Ai/
%G-gXKZ4a3;J2*FLfOSr%YcUPLN8he?,`#lh$HAK\&s,C>fs#C[[n'iC!#)22-2Mp2S(7$RZoF\'[cs4pDh,&t4Zr;LiniYQW29ST
%YbPc8Vi'8i]D]qdlc+m=a=uo'Ks@SQ+9(uX/Ct>2eue/)gBY`tD`OVk.3h8^n0uI8lppR<b+O,XH@diNDj1iZ49A`T[uC'f2J%+f
%MKiDS'4k.Rp5uL/h@MqH3!<uu[?#rO:M2$?i2)C9dolrQg\8\sP0?44PPa2KHWIo!?u$pF7uI\sdAn8$oho*?^Z(:A&ZS_*q3M8u
%2fmE=(&4HW\B+SYrW8_i3Bi2absb7C%TLX1O-N5tES5Ue60,Uhp-IJ?a3QFRiEl5=a>T;%@duRD80q5cGiG1>SC*^56Z`c:`MXGV
%>:iT"AI=R/l%.c/^sl@*+NT57Zj&iCOfq!XkF/>-qDMo>-G1YW9u6slbE5#<Jp]4>23'i>L9&=n-+]hn:U+30aZPhL!2r[Nqa$o\
%gYD4Qga%usq9PtF?Hk<tr*)VF'M,IaDVJ3&MCd:-q=gYE-bF7?34Pt/0VpX:K\NYZ8U+F!+B4(BEE$T'J4$>"*_ga$0TOOC]1i(d
%o8ke.f@C4<-t/pG0L:k$KH0dPh(.8$r\E=d]p+?Jp'A]Pgmq((>'IgsF\qkq]lhk:J[N8aaMs+/eTNf@C@/'%XL"?;)dW"spglJ_
%_hAY/Qk''.?s%^#g_39`F/LPS>guE/)r6$o9MZ6?W.Mdo3o/Ht15&MOFUYNCk6M98U-VJ`+;h_7apuE+4@3S&O2RgahI%p3k?TNV
%Db.W1'LZ`t]s1h_@59Y-N@I18Tbg$\'S>n(@*14?EA?H`D'rc/\00eIg5Qd'NF(s_S$n1#![msuR@+(j?9_/nZOt1oa^q1U]fN/(
%bE)3,milnhA(@qk2.C4:6HB8@)QA:7ms12o/g[8hqhha<N2OV=8KfF_03/NN=+'1$>S/?_R([$/7hfd7N&\1MkJN;9f#LZnk%gR.
%!b3O>;@l<(:j*q89LK]X11DS/p0#7R*g8H3U]@k?+t/[5_YhL%!>EmWpc9XjV)C3r;/'*L<St(D5IR<iPN#EL,JkAU[f!Z%5*8]Y
%Gg+P/Pp&FJKd'eda(G8>RW6Bn@A@+/hXPs,cTj7-,q>7sYr/Ek;BXtX_!BQ4c)`baq\Cb#QWD(J'">%gNR^I;AoB`L>&N3q3(@2n
%&QW8a40TID[Z]3@.;,U-\Gj"gi+A%a$;eb%!D@3c+/WtZXm6&.rH\tr?uO]N.4e!Ypr?HH<K'qrNIj\giVuB3QDaVJ;6OP5W^%Zi
%8<qlo2T7#G!=Pn]WA-T,\BVR`.)WJUOp&K%0njUJ=J@:B=-pf&)9<q[%10k^&V.F83NP_E1Q$i]j6&b/,*OULko*U9!`AiK^r=GY
%-Q'[rMacZo^+VrON:R=dH*#a6s#g3RL^3.2!FEs>:fAA0mfV)"Y`+g"MUCga'.)!,>>&-ok+E#paoL+cUO,QW+>t<?$7I[RLs'&g
%3M4jU?B[f!Xlmr19?f*-)[0lE.7[?`E+V_f+oiXs)91EtmmimdgY#Xa&)p]]`pk6VCEH&nr=r@4NWr($OHlk51]ZLG]:6*$c[%75
%mg-0!&5RV8cNh3_/5V)&lcMLRBSY/Ekul(GIX.g#ZKejI.kTGU&nS"YiQ:oS:,>D[VYEL]1Oi%m>^"+pj64Xe[mXFO12&J8!'**T
%)j5#P`'H;"OI;+'5ck3cri@6'=LlK>:ab?GXQ\4F'5'1+RktL>+&k4&9%tRqD`dQWki,N,TLW`$8PQU=?ueuC>h]N58#3^ceC'F1
%'iuNmOEd"O=&i4NDoo#hP-AQ$,!7gRcq*1lKhHCaL5<i70.'[HjE?UJKB>#p'%=?@BoTso33MWOnB0sV'<QFPN6ftd8Mh&<pbbMH
%`;3?%8B.di*?\6-UF4g\AKu?>f7+pdrOqC]=gB2#N6J.N83Z!H3">5tk6i/CT9I]LF8^nh$ctF;,Sb=@l#YgPDC#&[8$"I5,l&.7
%O9_#kg=d!9b?B%/CBWW=U+MRVhiEn1I?D!'MLk8&mN,]++&pFVWG<u$/Lcci-'mJT-FpDF[&t,gXZmQ`BOT[WHj#QG%gsKHD#3[P
%]>UfR#Fg-di+'>)o;oo:+VIQJ6(@*I*D/n0=t(+GF\K%>8cKc45S4h8+4Z`$Y:A)nUCE-(ir4oE_-lCL:(JCc;oi;:A"r^<M]E:m
%Ba%+ol;:71c`-<5&T0Ii,riUSe(Y=M--ao0Bg\Nt%Ws<4':YE!mr7*Z!f_P1n>UEHNb[Do!BgE[]\@PQpEl&`7ETI.i@5?F`K/#c
%!)dN"pa^N-E7*LArg!sq]3)]82GT>YF<N]"N'Up__[SPJO?g"s5;P)c<]3OlmoF<(Y[T?Sdu[E40//TU'%aM@3lTMlh\=uUp2_:)
%4'hE4eB8t`RPhBC^b$,#:O_XV+28g$ki@d9OIA<Z\!p0tT+(@jNc\!'a>NWZ%:R]V"YneUn(;djM/#.sAO"3I6R3I=i85,SW?dG!
%FSlR24X$mJdGE)4M.CI<5j_21IpnrUgEO.HQa<k(HLMR:bS$$=O%W/.KZ/jU+)`]b(i0GP6J*B"3OpPTb](hb&ZV\>#ItKqf*`:a
%>>q722%$d*dFd@Q94%8gnVqXsDDrEcnLZtKMI[\1PIj#"k#6]59uI[%8_B:"(npT/1']CR9tXWUn$'Ak(kedF>e,X-O=GEKhmI9B
%'0L:m/c,er?*1O=iiN"KT,T8E,'CHo=JR/bfRM3?dEd*72(+.8)c![C7JsOB&Rs$dnQO8J.@/g6@IZhr163#\rh4LK":T1l4lXa6
%W1L>s&Q#%F:'#I@.8^.MDt]$]\"K<:j&@I^CR!@YO+<rS=".9k7&k5f#X6cp5XaS!@0^a=WuA70(*Bb*ABcD<cd`NZeWBesHIZu6
%Btj#6@,IA5k&qEdPBP+S^hB`B"Nk&8<3kMKL`4>A6N4]S6'14Nd\Df3rPh`JZAbfR;m>bY&<:qA3qDC$8ohP5n.d`f*t]%Gk'#hG
%O4fWe^o-tJcZsp9A!bJLPdaCt>qn\>,@GuOi>jrlU<6W2N#4frFOY-7$$#KV,s%53F>n\H[Me>Q@0)00dMQG_];+B'>.h.h#hd4@
%c\ZusiAgPkAe.YKrZ2?%R=%%Km4qc:N>5gN$H$_R71:O;Sm=o53>a*22kgNRa<r0W!8QRW,_(OH_FCj_#7FdZaIh:l%lCS>97*dd
%JEFd'?@q0PDsjLo)OjG^\;nDGa=X`nmu<`Z:-0b6o6)X$a*Ks%C1WmjP#TU=U'=)Z3Yat(3C&X[I$$V"37$1J=\niu+&TUj.7@K+
%g@UtXmeF:na!W)VG-^b,_#Q2hhnB*3$Bh_\O-V0gn:pu_!Emb!@\Y=io=3hI$mbG%AoF4+d<3$Qoq<2HL[HM0KHT>K<dS(^>?V<^
%6J%jI@Se]^@!h3/a0h-=.JI*=C15l*-G>eLL:=EC6>.iXQ!_N]U^BX.&t_E+Z!*emFFLIoYHXQ,(Ou/ZBO&39(*F%),A\`B5sQq5
%2r=(64brWKAn4\*i=/[Jo$hn*Nsi.1,MP%T_#0bqZVSaq*)_NfLDW_0bQU0Vo;Q];#l)bjYF"Z$,f:m(J5miB/@]KUUP:_`(.4IE
%mO$`";i3e0e@Snom&d,SI-`;pV,T&7RoM[bO9RelR#qt-3eWN-#pD`K(f.J7NZ0>"SO%%<>JMTLo()cg_<5DKn4!L'?o)#TO*+qi
%!dE2uH9f-F#Q\>""T71QZmWRgTZO=X5uQ0;mIX>DP)_!aT(UD;EYuc+ePnb3P,&mKpl]*X@7R``3R4f@g"Zr"6fAYj$h,^?L8<>Z
%?,/?:"?n'cBVN6Dp!p0!_W*ubD1igbZ(6?4TW",G!kf(a[/8aUjJ#[;XL%#cYN",A@^[9+1-)q:?D4Nb;]\uf'&HMO?%f"A%#WA$
%S`Pf;>B-HcP74AI[^k5f/Lh]-^s$M3"Hi[H`e81Ddg/$i7`BmZ#pT3j-?GG/q,lXjAIRuY?J-#0gu>!J+dXZbfP1.*]Fq"7D>Hip
%k"C\3R`Ho^JKnO(V/;)+l\dT>[^:[TR19fS]*7$bJOV%5[N)RdBJ6,$>8Zf;fr3ZB\<-Ob)HdP]KF^(WC@dLQHIUkmT/?Ns+7eH9
%T&B^s;*7$ub!:-F$kF!HP8cH!bX,B]>U5<78.m$/Q5Mf\3dJ[_VJG(ErDEPK1haE9>#i&=(>"f6d1!&EiiME%6>C&5-1Z!h:;lRQ
%TiNk)$Y">@JjabdV.Phli[oFJ'fIT6Z"b)]!]Eq6P8oN3PEM,]DMDqicj,Y4nApCTFKseWOBF,+pe.%0.-M,Qf0/)-DC_1VM!LsU
%)O3FN%%,sV[8_]$M\Z1OjG.iSQfIddVLi*0._'Y&,heWCEf-lMf78i^[>ofjen)aj$-`P>NtU.8eH=V+\A2KRi:k(Xo7Ir^eUP^.
%A[Ha+r%a?WDgiRqO*mj6EX$*XpuHN(2)EPs&B'M+3B;kB<o]ch54"]-;`;8!5oTONmBaNkA`#O/n!G>djR%63K7I"#b,^?m-RYcD
%C0Feji:.*ud<7(nV42k44g%a_8gYK<EaM@T8K88%SLjof:jIn3M=1Kt3m?)Z*33Q-)AJF8YYAgXkhf/d.QhlI,(F#+I-3?sd;$8o
%cpGOGU"j=>LmlCN$C]?0c6\Q:1X1Z(Dos5S>DE.(SgT(R4:q<mF=*WYRd4&\cTE(>jL%[i=^>'m?hi/o6PB1WSSoffDG+O,8D]Ea
%\jZk9LqU.cB;.?c`7b]Y-G-&RUs+K*]%J/h`M`%RUt]n5Jnp^f36_XiY+L6D-40Xrq%:b/$mlP7^15bhD(.`8_N6-GJr,FeB=r9s
%DCo7+Y3%B<=='#:erqje=^hcQlN(a*@MIu]5j&>1+Gl*.-]--)/U]`?$Md&`23h85iR'jV-a#'^^)\U](ko/Y+RVs]#$0TJ01ElE
%4\ZTnU!BRY0%LZ#Ie_%+]&RF:P1?@?S`RqKb@6#Chqc!9oP+[eUpaj\L)3g7W9oOddr%K+j=+BMDs@eP>1`j,]DZd6eF"O6AJsq>
%Y(:$=@iW8T$8_E;f/VT(7j;UujA)o5K$fJ>,_Lm&C9ihB?-pLG@nA0q3XJY\]"r0C#sS;)<FnV=d-X)%i%[Q2isho@RRA7pOg:=5
%Ku5,/BgNS-AL$Y_iV_K,O(0XSSiE_+V;'&52fcfsECGj-^<sdb"8D]I]ZfPMn)?dAG6D]DE_A?Ik[]pIFbpAd+'DgFO^EOsT>Jn,
%"XL>(*1&3.TQG\OMb*>0>Rj6_&AH0Np4MBCp1Nj[a$mr(*;2#"DQtE\al<sJ6/$mld1ZoRfVN/7.3C"jas?Qa,L:nDnIE4k3>')N
%Kbn5!M&GVWMrHQK.u<Hgi)>35$SU^A"'t'n]HAPMn]b%o_KQ\lMr2"qNtjF<Wo7D8,tR"E7a>Qi4>HgQ4[(DO.dd61IKnX@c"TSZ
%Dq2TmEcM\oEeZMXj_NBW2R?!d&XSG6E_,'*SqP9-=!O[c(WU,2-Ssa[B#g1X-e%L[S2rU/C\Nd]);@M>@D'O'd-/3T4J@Y7k:JhM
%Zm+Fl0=7F*l$dH4(<'l,2lgTc!#p$VRppW3G7i.OQDO0%A_'A<g0J^K'%P*6n5@B"#H>8:ENBC]KKu65e(%p!BSAJEk<+M7Ic5Lr
%ET<-t!="3_*VrHLm(f-o'8_o6G/?"7J7+,n'9$"1hFLBUJU5KucVdY.GF(j?[XA@0"f'9n_6a;P@Xf;uPKQ]%fMgG""LdBRobl70
%/@Kq5grfJaP"3Q=:g,XOb1Jq75?Nak_b$QmAr<*Q6(TI&aK!WoPT>3E&fJ4C7Vg[!"1?,h6Dl.?@!YYo>5&h(HMCi8q*QU]F[_*>
%0<L6MaV2=?nei!XA\B=$5r^X$dC,8mX#41nAq-2pnA01J::4:#6Sr)/ng-_HJn6f`\<9;[j_^1Ni0Ji(mb3M2*Z(19=5dSf!=O[7
%5;0]I@]a38N:3L%&GE*71!2Ono$[ncljqQ.]4=amal%b#):(aM;%GTXDt>2^%`io!L\qI;jNeI&+71p#3)n>7?S[2[pT6q:DQ4Va
%-5N,5k'R$)#Ujj%<UZ8?LRArY994nUeac4Z)ATsBDHjKZ<`R5$2%UhhYbDG5RbidtIGhpsoU,%;q9eH'N]Eoi'g]Z9EF%,e#"W*0
%E@Ck+8steH_d-gYd-2_%A>FUKJ]CrqhQ*^X=s2H:-`N&.eFTINatON_e85(qN^-!<"ebG*=&=rW`hlb_;'pc\Zi:06=)]i?'SnJD
%7qZaLR-G"_*?WTuiaWiTT5]Cn((p#f>K5[:Hu]MEe7@=roO'4U/O0Ug#h>':Te&pH($q?D\WL8e6:-2&>fQA@*QN4)99us5,KXni
%V3i?\9OoEMH]3T9$Q5??@16T!;b=L5UOl,NpscoF:QQ&Zgohk5BT+iUf1,sCG[5<,-4+Xkk(pg8PBb'<#8LQ"9]@j!Z\nf([RTaR
%es>5E1mm]F*/`4Md+,5`HJLQ-_K1O\((('mF$7%c'gn\\86qB4HB4^=+m='B>CPV!\5'b-,)b0t)Hik#0T$S^7e]K%,N\U%Qc(C=
%r5F0_iu`dIiW&^<s7O/,[m0fPhuDrEs7bFNo_an;rguFQh>d6Lrq6<fdpN'7^]!3mrp-3Un3?sBo,iG\s8VStJ,!CNj"LH)=$Q]V
%a1qNK^\rci\Ng#2J,c!&rJS\jduT"VqnMrjs8DO!J,>R]p<WbGJ,"s%q1l`_a\RfZb:bFHjgY9$rUO<glfK;h#Q:"hT]+i-a8a-R
%jhI2P)h7`<rl[Mh)uoSL^AcY)s7Yp\%,'/1r5eoP54UY\g5UKB<<j/[#"iZ`qmXT<Uq1\_U",/#-G+hg3]tS5?-/*@C5s4g4-HkA
%S,SgFkLNa*)9%uV%i+,r[<!U(KtG-Omu.Tpi-I(bp7t0/m+dTc?^]_DYe7;a>d#&D3mA:'0HN$tr\C7,'Qd?7LpY3#!CuBr%hhDF
%d\k:@cj6qq#IFLkhG#%:atm-i*,*D<j)HsirkOe$-Q7%=UK!)5(0%-*-]7="gtZ'A8K"OIK/)odQERU2]HljZ+Dl(V2^>>$]HBX'
%02.1jD/--AJZ0<.cg*m%"]>tM]&G2'n-k[.YA%R@LQZP>;9[P`Ld"bgB.H8J'jVZ@^dKk!]GW.7-0Rs@PDG4W81I)Z'7(AH<fj-i
%,(`X2[No/3`""O>89NbrAgM`-P8%te+(m&(.;8DDJ[D[VR6VqWJ%a+M`Xp^P4J8;,Q;Ch%>;sInV:\ALE"scFJC/)/n*p4YMmE]Z
%E`J_M-t_*TYFM/nk[BF#GtVi',?k.7g5,<N?sI4=H.e'*UG`Y_ed^#7fN"buIB(h'@Ngjm<;RPLg^_rLHsbb9(tW1s?U8b4(0h^B
%3:R?Fi\q^<\7L^$Y@dZ[l*5MGW-,mIf^t(O_3diC*oZuY$-a"a^2NHp]>7s(.D<-a<*j(KP*M5&fcqa'pUk7$q/`b]*k+_[&+`;4
%/R@m<@GS'IoP4g88Zko&-X_q*US878,'$0rMJY\3@-KI0B=-D;JAWuOmFn7AO:(J:,6T/[r3_JrNnG^dU[AZL'doO)nPY"7V(/6k
%H'T!L.T=bZ6tAbn2]j3ldZfhkAEuF<BNceC@&Ykc+].i7;rk(X'I,&0_#%*+":F)Nl56IU&p%&<6mK!IF=h)T%mhKQp_/gcO:Q?Y
%(6fC**J'PSi$7q#B1`rtcO3)tQ*b"hD?Tc4+\m5I;k9\3IRm]`Us5V)DhK`\(orZqEJ9/jXs4RP$CH:kq*uo8`=cEh,3?]o7m8X;
%CM$FW8#tI+*<PsM3U\:2jg13n.b`=i`W[o&!9+QCaE\p7+M+.ASJ6q_X^CgidC<rfW+`t7'a![LZOn@:R:Le2d8+1P*tb6E<L1NE
%f)B1o&sG&4@#5_n7PdRC<>FN,mYq5]F#Re]Ia_6kG8?1>5F8-kqL8dks8/>4htYk$6D]Xp#?5Y-iEfFGe33e^1e\Pg9pql#Bj)&_
%(URcnH\;n@rKXUFI2$kB>78Q"il<[_^pjL[`uVN_,(c7@'u1C"VE#JjMB8XSMNeGbUo6LNWMd&]5f.(]-a6^pq?rdaHEUqdCW9h^
%Sm[TFk[Zs1'/?Vr7[*mX<70a3C&Ia.Ei+Xgn7GX;2!3`476cTLEXTWb`/Cb!O<@e+NV`jnQkf:+6YRF#/n<M1j9U8QX$K'RU8JN'
%^iQNN,7d,`Mja-&p+FHbiOq&O$g3!H(+780&B#rUOs-hY,@X&&oba0N(J:Z]r54GaTs%/pFMKOdM0FcbSpu\^$G(sl+HjbCS)`k<
%4L*.R3#ZJFdsN/@@WM<X4asS?e%+nG.g+"TW!c7Sc;U-(mMi*Ye@CVcGSYPWLY9dgJ_ELNq&ruDc52<\a)^a\/urPpc)6^BPZIQE
%qHHpCP9t475d1hQ&iGFoq)B7!Zs_O<[e^S0#/(Nk-sS@FJFKI9HUGt:Kg>SD=28j>?<9Qu:j+NC6k@'7?7XalXZGhMVRT^OV%\F]
%Cia(K^'/W:r!>rL55+m%lWpt2Z1WU9msa#p,sSRm.$-X%ZmC7bo?F[%;c\t2f0tW[[sr2ck:pNi$+H),#VMs1"5..V.6qa!9p>Q=
%iGa"6BshH^M<X=M4Xr@`)G&1HSq7X)[8hZ`Riaj2OA7`7"&L$OM[R#UOeW6p?B;9dDkL+4&r#b+&LSLI"Jd[]V2I3p78k[3T]Zph
%Jju_h:IUL.5W=2hN&husmD:;>`nLN"?0]EFRILTNAS$18Qm=4./O6qF0)oQd`^MXHHe5V59NgNa:Gf:eJ)%6i+"MIBA!o-3Eun*Z
%ofp42>;Xai&NhSD7D_!@J)'e4Sk/J2;+.4`pJX2jFdILl7300i83<:2,U7UnU]2F$_NLsnKM:eJNK@sHbd;%L&\T3N%<>*+QtRdR
%r0>BoATfRN'=:B@`,(ik:a\7(HRnebJhst676V2J%u4:^>U\UJ0#I5m8eN+#]gap_%n7/F_7oOn$!_0Cn[L08.Cs69f;lNbD;BCi
%D#8#QU>g9529JQPag-]rjZGCr-A04[%Hlbs)P'0sA0IXc1o\8V6%D@jibYrm+mJ+[+CmQjBH'onW7,d14<7K=UiDWf[Ji0Y96%88
%/(iII/#kH(Ejjm>AjS"<cm%.KM&@E*R`N1:&J>VtR%0T[02muCit+c-?N2>(]P*%Jmi]6^G;o`"a)V6(psb@;1ndlXC8IfAK8q&*
%jOQb\:b0!XDq#=Y,Is0/hI;2E'4Cg&1`;BQ:I]D1l;R%8a*L=grBoXo`\mR(/n'.Z!o/[ReQB+1SLneGmO!Y0TbQ:i\"]`W^=]FD
%0?k`9Gq$%uUg&)WBkf2C,_&CY$.AG%fAi=f8YBCqF:O_kPpcZ@'l*&bcZYAF?1(?2Mi;X9nj][)XW6nl(1JllPQFoZ-Z+;TAa*Y2
%$VsB[U,(F8cqna7gVp^<JP]6&U`p)7>^+AW'mc&HcJNSRb>^##2d3>8i%:rjI:pcl'J%#qW9aUqJLdk2&Me"ghl"0[j9;6T?VWTR
%dPsQe0U,!q7[.q*$nAEeD$t*qDGm3W=EUKf:GQ%Gaq[8TdX]G1.#m5LR"WL$4F=G2b'Ms7r<J-3b-6l0)ql2q!afGf\X:Z<>Rufa
%huX$6BNin10_UV@QeU*tY$?HaPdBDjPkUJgfrRDc1CF$2b9-XdZm7a=d*?f.ZPYa+XGMtQH&hUUa#Ki,S>gU+.WA>,?&pO=kL/.V
%8Y&FhX;:O?XO1:hs'-rX*Vs';7K5*Bbf5E:0MWc6cooFRoi<*uRH590BjFkUDeaH)C/Fm$BV9T&aROoAnLKt\%"]<I'#b^joh;VE
%F><m\a3V3.e`fE\4cpO*b01;i3<Cs;g55%)',MGl=IlJ'm$j7=Fm5U5^h/o9e"PSa$:AX"S29_a_W.k?111M:)R=Z$It1>;nY%pS
%rkh&3"#VdfJ0jL]Y"l,3@dhclCKjA$>VoDNAPu)gDrJ$gh;-Hes&.%F2Km"=JG*j_5H9M?RAFJF!@&K1R(>.fHreXB.ZU"J0\^?N
%7-h)9mYek.45/Y]&U&\/9k_[u"ndPc\<iB,5ZP0>cFID>(A>Zh_le=sSINi&.0.-hjpq3*Bce%PCG=/Ki->LF002ej8JBWuf8`'c
%DD%d@mttPK^CX*lR;RR]rE_IAUo_TK[d#,4J9CmoH(l3Sf#KSFgD'/$Z:jrjYEhj)]W,i'm++FSZ"W%r5jm54Uh5,M(H/fpq;[W>
%`qZ@3!\I9PUf+W/Elm3,Z['E]iMLO=dC1A=$huE'65n1N[o]<')N`Ys796Y,L]e@1.Z,\k';eU`5cuIFrUs]NR91aPW=HONN!^11
%;.Lc]D1BWG_gVaHh4XUD1`'%00M[NR\CXiDMH5*08(A4L-$F(C!0$Umis!GJ`Mi^q0TqpM,8<(J?a@A(G[.gKY_@`5.r,!Dm/Cb>
%9@GN3273^Z<J)e0HRof<'0X@`dL5)W.L3<7[pO_,LRoJB-uT&EB`@PLQ9Pn"!WKkC$91,\jnC2j3cZR6jKr%GAp=/!R@L](4?_h#
%LnoKN>*jW[9+1<J/rUP=UR(sQ4lL9i`B$5?`@cpl$,]<tD7j(3Fkl)\,nKOUR%qOdRo4kD^K%\:I]@4ZEO19B?cZ"[]0o#kfip3Q
%SHsmn1?<AOODt7)T02_(Hb:FSBHDl/!25J!]qKe.eA:L7?%B1KHRo`$ai7V*Up1@0"Z);G_cWX?#IN)`pqQn]=<%/!?U+M#`Ik:P
%,(4!6<+J#M\e!\hKei=Z^8t*np)cO3CoWXomoojNXX[Tlas'eJeiZ7B:.J[F`rHn1;<&-ed0]l^XqWhK("NZAQL[B9XqCf1F7K^J
%^raK.?tT=pE.!#$<2V&m?hLNBY)'T(g!$G\.JstK<B)S<:/bu5/nn\VQm;VbG;oZ1k<=-$Wl30iDsC.bM&osE\mNS!k.*>Io#DSO
%dceV7.8CB#F6hkG(4$O5+_rN!Ib"ZnXpT!IZEEa)0C+!BqV\8Q5,;7Q4+%WcQ'S<'Y$O<3a&,de55`arLE:dM(.%Sk:HSH=7ltSQ
%epD4%e95j7K!sk\r^qo<Dp0IYr['(%A`H?ScSu2!euQdgfW!>4(KUG=1m4>g7ZqK=88SRVGdZV%m'YL/-GW&>K3"(:6ZQ;K.iL&X
%].'`TCfR_Hm&]C!.%YjHi+F0SgP]Kgr;0iHrSK\^*aBhi39jp8ZRu[?=[DLI7KUQGCoLoY)85mqc:SkXOSNXRI07j=5[Nfi11#Wb
%W*['nbAqA6Y=M,L.s;t=RirOVSAVpqJB9RG!h<k<e)I)5UE9?QPpo*8kB_ZVSjRVsa!?/s9u$a/2PE'kME2j+M(5[CQekE]'a"Xi
%X.1$BA5\c7E-jaN0`u&W35.PY`>IMW\R!'"O9FmCS!WJc+:`K8`cFRc4'Y]*9PL3#5duKI;-]S4eJ6T#8i'kpHETk&91FNc*-kch
%TEO?=-DN7UUSSr\%\tFo)3c9_-p_sG]36=8PbDG>fC(8j9jN/m^rj;q`m^s6lIRg!.8uSKkd=l4ge>L@'l<D0%-V54q[-^1"8`67
%F=M>D]On6>[hJfhQ*$ZN-B`^[o=nYGYGqLFIOpWL<bTM*ZAY0WH\_Zc)`5;o4idB<*Lc45O@P1!Lr,q_QK<bmrW3(t#dMp&,P2C.
%$GSZRG&E[;b$DZCQ\<\qO%=@>`)CEcilC&fj-LAWUf-/`WATB`T3MG6elNepd8r^gs'@XsfbGVia\g*:]u-?E(-'sVF.EO(h1.pP
%kS^7P!8*n1puq'V0?Vh+ZcA1'q<"otoa#<F.RLZf3lLB)d7RkdUq1.<F(//Ed\.SZ&R*FXNJg(Q"S5Fpl,m9r10,[be+/lqicg`@
%!H0$o)$u$m:U7_!WSH><#H.:S=!0TU@pG1?aJDRdmUK<[\>P%t)Af4QEIDgUPLAVhMsFq?7!JEIf*AWHf!kMo;&5>-,"jEH%#4:_
%+"cilgFj9$kK_CE!rn11=*%*5bPQOC^b*Pj%iqA`c@q3g144c0[!Wi9I9FHIK:r`t[F*F8]^sWh_*KA173h%6q"I(.kC6j8gd?GP
%T]=rSET*a-nd;.*\nHHmg_^''@D1?YQ`k[[5&f&jLgbh!?Fmn=6fJ.;5T,JcA^cP"qD=qi\?gl!15ph;iB&eDmLcnC[J]5m.-O9;
%M!NSgbDE(V_k/6ZrdbB!5;Y6J9@U16NenKcncULJb(aXkpCo?X'J>_=5='p+K0)Gi`'pL7^iSqAeFfo:l+tVg?/Qs,)D`1\ERn>j
%=.+D]eBe)t\qAhP8eg*8Q=4#K=rriJ\38L07_'RE@GcO?Ll0KX9mKYq_I&pof_!_4&k&MXkrd,Z.2Wf_2FM5uo<M"i"(uBkdIp3Y
%iHmV$3Il'83-Y&:jh7W1)Fh.=oA4lZ)CjuK`ea6bf(ebj@(CEV*UouF;F"(S>H-^f6Kpt!=Zl2OJ?qrU*PJHu^i4'Ek4-4L:@rDC
%)N,rI<\]:KD/X8T-'`Fh1,kDhTl+/-.p24a/p0L^5ce8`%.Fn`1n2Slc<`MB?aJ?SaRPgRk?8)3U;OY\,CKm-cMD"&hL]Y&,@L'r
%V_4m`P1F.lk9CQ552H`G@T,TP@i7""N4abC@eM;hW>!/;YfAg72bmh4ab;F6#BUC#ShAmZe1\sA?7FFc!?]5i"k)gYQ9X+^(eqrE
%Lsq?0_W%%e2-6Qr[VAPZ,B+pH]^s1p9d`7ma#9p57f&TI*W3dB]3>8f<Nem/p:i;@<i8.#:frtm0BkMW_2n%!rLIbok4`"I7l4t)
%h&[O2jjS"#,O`#fJO79aYo8o2Eb&-HjI-8D%6VQAI'+AaAX$3u@t@/iej'd&,MkUR=;9P!N)<*@C<c=(BLEi(0[^\)jMIqAS&h?)
%%`02U*XIuS4Utd(K=3?LceQ;dRu@L-\31*^YN_6?Yh)U-(OmTk>?VO_aK763f'mC6)-Nlk[0m:S<`FZCV![L!!9bO'aQ!R<`3pS`
%=2T8$df\5VRG>ES'P#1WgZ<2RVa`X$3*!ur[,Ab)(u7nS#\-Y"Z1\(i^C"knX)S#EEMsh;pFV1PpjB$tKa*V$7/T'h4QL2ll(*^W
%A1RruKKWkOojr6d*p^-t4es[ch*%o<"b-UbE,jTIRuACXOqZs`6MPI1o9+]g7ckq/S-9l-0s6Tg5t^j<9Skbb"-,WC%$2Wk6XWpm
%R%go\SD@KLbFMGUJ*2=<NGDoOc#TqR(aNh5-k%A:A=7S(CC3/nMAifnlk,4DT%C,1P0cg!7<nn!bS!^h3RZ)SLN6aE)S&<j`L(-u
%Z32(JK`74SCS3F9c$SO%77`@ojciLXp1X;[aE98]9&/V[3f6`9X4'$KbE9.#9KlJSnr;IeKG>[/jusfRYpD=As.%#_Omlr4JksS\
%\<G<70">jh:$"I`[h@9!?c?gmoa8^Kd%d-?F-,o9U^e\A;--SL6$aU9XDrZ3=rNc#mq^McSinD_#e7`?o)#BUWhg<34Ho_?O2Kse
%U^[Z_bVK;`<thorZ9X6'>rB9q8hB)+EIn2oTV,9e3D/(j#O!?AP5\fc>Pb(KU=JbocG2mVGGuV.\F*a+*)=2dPCRAth]Od7?1t+l
%P^WoLEdYNP[J(4lMYrjJ:dVmS3*&c6j$ZuqPXP7+]i&Ko]iX;7Vt'0f!:tX5b]B!14[hs6[K/Ea8u"aW@euJH_Kj3>Md[8"<]_'i
%A+Poi5S<Mk9<1Y>"d$_MI9_$P]'ZBfM'Wfph\B&MMFeq3chML-Zk50"YUim<I9?0u,9W8OV6C%p0!qNbPgt;:'qigf2?s1sPQm.F
%KiTHE/ZT@i&o\*]nU*g,?<XNjf>4fZrTDEf7qfc<!B<".A8O-['1=j(p?^e-/KmQn]?gaF@F-l_'Pr/\jrHnb&gOkPN40<DoMg0Q
%\fVItG0r\F]JQ)!L-,N/9/&-4VHj'%6]X&5&acO`$tEpB#ZAdGDrq>l,,S6S9:<Ojqr*;X)#"LN+qVsFnXB*"5at9CEl``Y<20_V
%Pif;@A)kU-)k6]Mr`]La:c3Pf].!$8FA>dd;Ze?8*(I?ra5R;TOhqMHQ5nBqiN`JXO'q6Xr0H,I0ZF\K_6nNENr]aGf]G-,-)Z;/
%D9gDuGJj&qcXY5&NI=,XY>qP=k5=qhhr>rh**@X/r1)cn_7*M9RbrS.lYiia9aer=G^HuPOs`W0nPmCRA^b8botUZm*ZG0tlJDYk
%+rEI\.\-W/iGaoE2YAY.DkO*Ycnq9-`.#73$D:kdQ(0ZhOW'4Y1"el@"ZpVLU$!l/LQ??7P,KS7H>I)8Pi*BLM%\I[ih_8mF^f=`
%96;''?E'Vs<Gq=47/Q(C,*k%ePhfLGI=S>1\Dh\[;'_GSC18QM-B>4.G=,1WT4'X5s,o]JiKgD0MC+/l!kO*'WM9ONZJ-1=RsM?2
%/2X'5`7$^=+DG2V36YWoN)hY<:X)ib(:giIe&?FEIYB6g'\CSJ-S>q7!C8[D\q],(C!ak=5Nb9?lEGK-!8'TaAZO'7aqAV-NGOm)
%ceqh5AB=J(YPtWHWunYu;n)F1fT(1>jh9BgmsK\4Eb2S;0c:O]31b[P*YeR%f4&[8gV9gOb>$1X1uq@$?fGce_:8=$VaqD[WioVc
%n7lDrQb$t[]afoDp:e@WWr9<V[o!#*q"MITLW%CN25\S#U_#3$LP;DE33QhrjCi:<gi2(a8:Y2o%364o&LfHL,l7%"OD/lP/0l<E
%8u-('/jg"6+XhW\StS;r[uFTsU8io_?W-<cONK8ipho5,[#7RPqCjQ6/4X1"V-?+Rj#0!PC.T@9BI"$=(n]=lCdRbW,dH"n,R2<h
%/OlQ1Z%G:=(4#F;S-<7:*'>=.Nd2G"c+u[TC#[V/[k[#iS0^Dh_O#3q7nFWqqdh)eH<Rb=!D6ac]@K%k8"G?mVGuR04T@XN%8@'i
%Af(Q`YRO4FYs"f2LsdJj*"#kjKY!&qeYK:>5K>7rVHh=aW&aPk[BB6E<-d5oC!M6&HDM`NG`D#C4PAb;S-4q>YIoWmbC<-rg.L9W
%p$"1^1Q+o-cTkdF9@%BLL9+`<#/?$GVgA=^n6\?3I-598#M_4IIuZ4D+(,D^_4N3-*?lEs<YjZ7IpX5UIm,?WIi4GI5n*B=U,!p`
%pV2O,BHXARO8k.6C\"7t:VFgFjhFnW4oQ\5`rG3gqQ:U^2_\#5A2aU51gbf:=I:I\V+#fQ8.6mXK4@S^#Hj/nla'^CGCt,e"i0@W
%b*RfP@H?b@+sEc0&9.jGnt=nJ-/1?u_%>]\/n;^fQX7iFBh9[]N]Lj7QjD-tUWOuh!)6S5(:29tFcr\C3$]Z)<F)jUTrT+rP4Mp[
%qT=K%9&1ND[`;fl#]M7<eiq2*Y6U`;qsQ>F<t5ci4*:NI?DG*!N7+MT0fhp^P&#"*9W/%T@,60\=L(`3$l_=E\<CK;(n%VNaHb^'
%m:VF"!h9jN]M/8ar4^GS\uaKG#R$m4mo[a6+f]!Y(h\h0P6_HKK?)%FJ<5UU@U0JOdIhMW$sh!bQq6lXn/:*6Q>JJeGJ!U>i?mr-
%I6+'Zpc1q8`d['D<]PPSFUUT"V"S%V.pDq6VHYXbI0#2X<@SCt1Is'`@f8H=DFQ1u)0@O_P9HZ`>)6E/5ZZOp9rAM)bs(mfX/eF<
%c-jpo/7)>Sl3Ie8SbL6j`u5:Y1@0(n)iV;)Ki8M#=W;^*I<TEH.P05H,5X)iLZu#CM*RcLd@@8J?!D/$5"DVicPd4g*^[]T;p]-m
%VK&i(Gd2]0s*egoi6n7B!W'AXc6kp.H"c<-V5BmB*b>:s:=m5m*#3Q1<M9X8//!rr'd+_H0ios+0l@h&a%EHhM9-:uD7LQgh0oPi
%l+Xp2f7B1$0M2YCg`=P>*lQ@WeSqMCkW!4VW;\\TD+Me(PNP<W)5i:uaq`as/fB=!e[3TNFP[VX)#R7P@c5Vp`IT'fhT9PO4'HRX
%MiMhHR0`j-cn&(S1=6-a8A4u,Qacj))_\\:aOa%+rsY1gY_;H^mlO?%OUH($VR#nPn.`n)gAVX`12UT690N?d,WWd9,bRV,po,bG
%`onKGmIKi1+HL_IegEHq@3;i:LS/f#J,FIZ3@g!Q*-J<c=^#/pD#"KBaCd\m`h;9@<QI^:r]=f"L_&^o5ON:djb#/>g;s@8mbrp$
%,NSop>(Eo>)')*u'?ZPOYidQQ='iJ*@(3j'*;7M/!,++W/UZfR?V%;X4TDS]gm8aVn;US-9DA\$6T=9(7]`mQS-!]>1lQ6JegfGI
%^8jr=%pY:Lb2Oe)ILrj:QSd15P,5IW7t;DOQ#;G)]`Z[=8sbbUbH.q'Q:9,b@SDKfh$(i-_R5l7,Gtd#!3,@sG<:F\n+If_$-9`J
%i>5MlHchX2q&253DY3e7[gC4R8)c%<V`p-GI[l/GDF%a@PlK1ufee9_:tHBJcR\o-p>/Cq%U9N,/.#%f+gF;\k][kqR!jr+7"N[.
%G/XLfT^"0)_f?E0JeGJde%!*k?Fb8`j%i7Y8iGeEIWLg)kEciar^AA,^2iGenoecL'!b/\'Y%WELYG$[s(mKsX3iXX$$oB.JHfuJ
%j*tF4O42JH^f(T[1*fg!pO5YI%Ge\%>U`XFhAYsqnD&Ol-4sm:!hJZeR#+*c4.`iMZFb[J;;G$dF\1M?]/*$!D2$N%7NP3"#,CW;
%o!IddiRY;[<c"e10dFT1'PT#U+JCRs$DhhLoKir'02aVb96VN=8Q:-`nX$%5Yl4EYLJ<2A*#q:N<Hih>U$DRoqDqGaQ+B$eWPcJ>
%!r,jX^8,N"%<EMAIL>'W0g(q)_54#>B'`s`>TnVa8-5XpY9;B/.d<Y\U6uQ^"A)tncV`\o('A*"[>`k@Au]q&7PQBca`=!!&U)g[
%ZRV`hH>D@[SbTp2qZ#_:>42`SW+8,dFB"Gf^TM8KmF<1><'$OIQNBBgNsU;?+n-X-;7p/]?/k97l`Y#sf1Km<hR4VshBten/&jKX
%-\[AhCjM$$Ui$UD0p>npoSd9CMV#GY"cJXMDmQ\W/j(]VV1(?;.8plqDV>A;ePnH(>`Z@IBoT@GK[MksX@ZDHir%fVcYh<O?]oBT
%MO#"mGJ!QDq;=^$ahZ]AloK!Df,Z23'X'W8c7qO5&\:/C3kgInC^OX21Q9Jcn#k5mB/t'qWO@>%_B-q%.,*J+5ag*UpMl]n(gHp?
%*@UhDU5b\(+;L%tG)53VJ8E9)P$?D')e&@tcJn:"g@pT]"kjO)Q^/iY=gq%H^e*t4/&J<`02FdMNSB!t8.@Q@YO7XLYs&sXlD0do
%TqW\"OF!/b*sdJNJe#U8IlUC5JU5rnT[p)_/il0b2#Fd#Pjfi9&c#)h=\8Z9fW5tXbRK9t&RNYldbR8\boNrNR0UF!g,g"ITcH-p
%/ZQ\qMWeaDVoRM*r*pqMa$Q*g+\Q+Wh?7m"bL"fmBe2lVL@]"_?PmGNBWV&F_1;f-YRH?J@V^SraO@a]ToPq?)JQ]@^fI2qCHMVh
%)ej6=l@i*>EuHPZTocB7B'3ib^#T\6m-l&8o,sD9Z<>Q<L.]a"kI2bjA]f@[Y-qKd>LtD=e)1NkC^K"(.q?FcckQ6XV4kZL%X^Yh
%#>7!+eX^Q&.8@r?DQ#J]PSf%k-tV9Xs#6D4)h_D9]h2<THVa]naO.X1ANW:56XW7q2$$!.M+qDhX&<4E4%bV@=GN9$r%WBKF2uV2
%5JQtBloo3B)P^m0A"2K!9\V6ZDcA?cYOTs)A7mu.2$nim:Bd(;CukBMRlgT+=fne*GtK%ViVc9F(k%b"q2H=#N"CU$"!lVFr*dVm
%)g.OHe[a1$<AS23baTu>kWTKaN[&7s@u3b\g7<T,_'XUt(obBG,7p:)+%1*uAtJEJ,]<]XP(XJG*0E1sBH>mJ>"(@frgY9)=Rofj
%.cKe*a;GBMM$=6J@)B>YC-rO0]"sj;&$2#ThY>fQ'c]0cU01rKO)T\Z!>(FDP(;@3(o9JDh`M"N8@f3aG2O786k4cn)Nd"1?0uk&
%UR6)&#hpgeP"`qAOEQ3R:qO[e'tV\pP?D%LTXH*aVr-iuRYAaA,.RoiqVfNoFbbH.r:c!8(Xrc6RdL6VO]d=-ctM)5-2f:TL*6o]
%Gh72AI<2u;9R(sA-C1a'gRp?q<,'RYQ'R7FO&,2Z&Fd@I'BXZWY6:WaZ"C(f!92F1[hflI8i0Fe,/%klTlDqn<?CIl%(oik+`34h
%._,[uLY(csif)rYP)<N);WHO6kr`IB=gT1I]E.]aL:kRh-U&GliOkkr&n^"^4igK(:nb`0,eQobd1F.h7);/7[egE,edKmogWWC!
%+.jUL.n7@RCa/&)dRX'G^H?/Co>FKRHQMbk@,AoYs"+%F/l^gQ7tR2f`+'Asg-3Ze;E^BK2M\g#9UW,YQJWe*f.nZ#M\*:"_=U8E
%]*%T\7=)5'=OIIY6!\XpNA)PD'0gY,WXB4'?]H)J];U"5Kk;]CXojS8D*lD"d-^'?3?DpbgJT6g#_Ya<O2Ho_S`auIj4i.*m5W5u
%$$Q#fQZgD5jr-R/(4P$,_sAAfG!Q8NCQSIN,-d)J?2q4fW/&fM/F_AEg/KC%Lr:A"k2`J+Om6$EZ)S-OJA^Ij*fR-6bGo;(cmQ9A
%4K>01g]"]s>GFrZ3FQH?r&#PS3I\Y^Q9X-q\6\KQ+L"a22.(THs"/QR?fTtb9Y4;>2*3]/G\3^K"#)ndD2a#=c>@nkP"_G[J9g"<
%)CVnI&"AcESF-*=$s`JY;#U$!6Z^HE7(25DVAUfWji]\k:i&\3eo/fqHhcrgh%VT71tJ1'<8a#X_ic>TK?LVl'.ha"EQ3d.99MEX
%:)W[82OTM5<tQdp!SXSUDO)V$okI4hBqBdf_sF'U?H!khX^%q>5[L.#$<G%kVP"S.b5<(.b]Mh%E&l@$!Fp-_JAJt@k5m^c$_Kh'
%l@Z?51C(pGRt&Q%r>Hn_"R_@sAAjPZfM/Bb0lgAOIA&_Yoe'sV>=N[nT<@t`Y+=g-,a0X9[=A,O?QP`pH^4ol2.Gsl#65<#:,MX#
%Qn:F\VKKR*GseZ".<1!qfjl!962W=&@Q?a!L,+tB#2i.UE@ac%/dNaDNp7@7TRq8-Rs<7m^IuBmSH*tBOP&3spBeX_=`pQ2P[="3
%T"[WCa)\L%!Dj?&VRqI+k&Y>RC!DX(MkHb49kQJR!._WY^$GjJDH#u-M&OCu'Gd2;$et9@8S..T1d7mjLCspXhddhO[&g#d32r9P
%AY<(cl@U:R7G/(O=Q^UO7i58OF_PmiLh.>t5gk*!CF22A*NtVd6U>-4^Ou)9*7j]IMhuXB!:g45+DL-A!S.:g,Vd\3UaM<f=<m)p
%\.bH\,/+S=pC(`.\"`3u/gE5aK2A^bjXm:*)0UO5nYR+Z_jn(bLRk\-@]C[@mV?YG7pdH[]:W9H=Ch@OgBE@b)r)]n%&1ko7jRZd
%R38OeqKUW&`>TiqW&$?NV?XPrY#W"5p0jnCgqr7e1/S-K\d0mMp=&"+Ll5RFG&-HD+o[%7VPSqnH\O!J7_Atjr)IVuqG1c=>8:sk
%L\1IbS#;<p?013W5&WOe8KE%<M`fr;kK%l8,ZffM(JYW*!hN3_Zg?m'fj;!7RTBc>?h9)DX^_QXTZuCRo&TXJj=r#0#(GMo:;/ZD
%4P!n,C*8G<dr`CTjRrj`DN@dL[;A0K-IuEGBG?h$d@J7%E+7V`F_rP^%FR?^+"2]/L,,4mBrd@i17FK9MJh&K`$F-hFm."nfLl?J
%)nKK"+BWj>Y4'+aFUY6`Q%):]Z\mu/L?f>EYb'dpH8P\2B:4Il4A5WPGV9G<6C:.*lMOlGocPJns*P$!#h\mq[-Lc3Mu5hGKHYYM
%;BPRJf]Ft`R@\EY=@AjH+:sVE=i??2>`']a`r^cF(X'ZOa.-:*Wp",aIK1!Mkf9Q&6&[9K(65udeL9dKWS]"U;VKs@U%r,<)WsVJ
%!]%3dR5@JCN+<4W[(hUhG\*&Il5TC81E<bj7goI)SY1Am(X<&+88=8Hk?k3egU3QSjNAHqCIJ!Rd+lCm`Cl1X/5lmEH$34B_C`mn
%[Ml4KjHOBIrdijN2ufBR(E:^kqWNXoo<Bh!Hn)hTR>qSU%]LLiao86_4SX7cK"FQn0O+i[]K18Ba2)O&,OiCGc\/TUNEa@B(l'5T
%P5K.<+*XF,cC!Om=J[j3:D]f^2/L-KiuEERh^bZG`%T1U4<[NLeG0^'GjA4VEt\IBXUgq?FYs#/J`,G+I"()c-WJ(7^P&<E$3rLR
%8IuBD's)kl_\$.qir_<A,@:[R73f4Z5Wc-1*[Cp$"O.iNlJtL9K%>I3N;4%+^-).*&mJ6-P`Z.A[?#I&i)#8=p..D@`+*TaI%h%-
%@!RF-d$/^;6@j_V$A\]9MdDjJHX/)*WiWr>(r<cU"AFsrs*`PAKh2(PllK,PJqCk<$sI5]U"`U`noG;fTIrG4)\Kh]?-3#V)k\H@
%g<OM2e"NbYM(f>"a4]f3Ak4CLaU$7ef]\HPAj8JeMilor)UH2*I-`Z;]9Sm&dOCgJ[$8sPO<KVd%hQr:r+iXWDcrF)Yt=uu7^N']
%nWC#jPd0J-0@=\GMGB9l^_eF`Grt[3:#+k+Kl1,'0/[CX38&KWUM6t-/Y07bJi%h-&/X9+UH;];(aSF&&_Sf%PI2E\;09f:A_jpP
%5[9WA/C;8B#U_=Z2+]sc&<M_VhIWi^A0##fV3!)`QfB-/,-BoEK-)L8T_Me<;En&("<<WB1BYij"rT1dhaHh^-DZ:\=;8V.f4lrK
%G$X[G%Fm2K&QrW%.Ms!uNr5[QasjSP<rdq(;0'(";4P543I8F6GR1<Ec@$R#noNHoRiN;LJtkmU;2LXNK,$K=,;-puCCu6GHlk2i
%PSf0QTaP*CGIZ"E]:&:^esRabAcen8HVP9g>n3L<qW[g_>EKRJ<e$'PTF7TPWf)'7T_Q44\Ir*b8HD@HnKhBb"/SO*!PB!H.Qg&m
%fHQ4`[dOc8-e!348JuL3eS4E-dtN%\f=G`s\2U5uUq4Wc7RG"V`!!Gk1G]Xboi'LgSWTF^<4*;^-=p?f`i\;tiM;?Oqaq=\X%!A;
%!SBu'/gK$rBab"gC4"cUXhc3F,H(^)Pb@.n\]8eaI,uM`*-A".meL9-NCQ7bDU,KD(h&C0HC98eEQ/8PG^dn_B]=#"c@,oJqkdU%
%3A-)CWu8LP-KEj<"hP)85oY)a.+`AjZR("1gG`.DlKeJnhRb\fE?*Y`qg$EX+):5O=X1*Z^j&Ah*RgGhTO)h5M+1;[bmqd)bsk@m
%@A=dmBMbEelu1eRF5.uaZO.uQ!!R$aM.5)9XN5XALHgfCgCbqOOgr>PXcCUh9k*.u0k;ejb-cuG*@D2=H:#`T2KiS55rru1fW)_1
%%lsnGQIP1JJ^b"9;:KE)=P$[9)RPa:Z7MA=9t1Et/km/Gjc%`l0$IEM[L9HW'U;5!^b$c`;`M]nWDsR$n_=f/#g4(XbG'0a^'P>s
%6oPl=Fg4=M##gnc:8Wsiei:;F0A];tnS'H7%F<;#f<"hJ"Q[L%]^Ha0c56$XnBDIk5iONZV3PjLlZM&g(ih<GUh![9)QfBK5mmrM
%eNK#JWN7$N/STR$AD<p%9hafc(_`<p1!#\R^6,L/'dS'E,C>C1je\AEntkp<(?uh-dim$F1<#>sKu@h!nre;M.5K^J9n?fJbR;n(
%dYi`]-r@J(Uja9hcI;oWjDc?G>i>bD&bp@EZ5Ur5l<8`Qgmq!XR9`Hrb<eBMd;5kMNob23jgD'0aDf3dOEH6s00&dnjB=m.?cl=R
%-l@kSU]H2G\J:dZB+jtg/Td:rNURjCb/r\fMD"N]U3\r2-6O$'E=Kr(::H<!9T5tO\<r$YPSrZ8NNG38$s0c9fMTJ[g#CGG+UW=p
%iZ^)A:iJu=QPcRg6(cX'-8BEj_89Gu1@='XP/c-h$?.[piA>&a+6>eO=CS2";)NJ?>*tE9^#OHMZfJTh67-tIaA9(\i.arhTp'_L
%3CH;d::[Y1k%i!O:WZg%-M<N6.ci^'Za9NV&e^?@#7e;CJ^>&[ARI!R7PAM2'4$enSZnG?aN]m::]X`D,-[UcI1d=ULULQ?Yb&@;
%gm6Rn5ba9e60/FSRACI0=_I>'E(Da96^t-q+qllC+`CK2b_7g[HNA`i`XU^-!_%j/&e)Gcc0FC9cLC`8S)2b#FG"i.Y(ueQfZ([?
%fdF4J$##"e"6(;W'7+,4Ao;MAB,O#a>XV/U[J[^WVm-IMQuK^WV1<Qc\GKlJ^.Xo&=^o43MLdm0,S-oG!?)XTA=/P,,A18=9[sN4
%RTmhj@r$]N9V#AoeF*Lcd?kA"%CgqWaph?Ip7GNdhcjqRSDpYR(<>c7:RTG.*rqYAngb?!)siO0O-`[n$ulGR6FHU:OU81JV*Kr]
%'YOWBg+A`IP`?s:7RO<*Z[dWTd-9J1b)"88blj;+(/IEcJi;g*1G`&C%eaRZaefIWVW2soL'osP^.C%WjDn-m*JfWF7MmY^XC+X`
%+Y&OV\hL,u%El\7$[L6Sa.nGDXn&fVB`KA+@#eO\>adh_B#c'Y#E*UtRLM_Sr>`5Z\OK3s%T=K8"Ma[DMURZ#(L+WT@ttgRNc\.O
%Om8YMG>=G3ej3a)0LGYMEQ\D5J%pNpUm(pkk+DC;8P%D$>l2FD5F_rAXU5\65"b%CfYMnKqU4#,odJ8s9=AF\70tS@,aBp?qRnUK
%C!ir[pk[[!W7j4,EnG@J"6`2oHXLNBp2B;f!1L6#;cN!4/'6NGjXGNnV,b`':#dC)#l6579l6KUFK,u9G5d*hqhBG>M-<aMj1N_l
%o82G%YKI<-;l\VY=nGk#fa7f)R)D73f[]S1+cfEq54ouG;0c&LMhZqd60sR_-uk3,STQ&#,P-H3Hntpj(cVLF2%Mqn'l]Q/=]kn=
%8KYC[-_RZcP6A0u"eE=W#"oat4B`%gmF+sE8WuN/NdlWqkHs"WUGY"[.HSD`c\GOdn;G#!M;C:FkqrK"lWs,rLSoeS_2F&9M:h1$
%4\YM^^oBt<6nM,NTLNRLLGlq)0aNe-RAM8'qCj>&Fc)9K#K?BW'2bu290b[I"[1L=EY%C\=KDub=RQT%X`8Fb,M6g!dS>:$QG2T2
%Td39gA>g3+;H=$AfHJ>*+LmgTU<<K;6IAR_HRg8MY$;Sq;/hD*eWgE-Z^-+I?3?,"X*)-/i/t57I@\RI86B,pF?&:1NHe)N1Slk'
%JmrUp_-mfu#]0Q,]kPBuiYF+h.=SS7X;A$MGT1q'X8oPC!$b'OoqX?)[fUY$_q7t+m0^"A'uXUJPW-_6<bS!M=%^;2Q`DK6;m0A;
%p@Q)NXCQ*#Zl]K1lecE0!Y$'`KqHm:DNr!acJ4n_WMZU?$rT%Bc%\D0Wrc3*@ZiFS$G)Q9U,&93:R$Q8QqmPmQuS'gQ(jqLZm-5A
%[)JVqBpdIp'DN#$$73aa64RJH!\W@):CKa$)/u$B"[c5hYin0IP[Hct8g!a.aODN=<eWJQa_$),`r_EO$L'O%69XA*jMGmckg-$$
%UfV/D@[]\M5V@(+d[R[7?rGZ%6b<N2,$4KD.'A^DZ>.9;83j\Sl&uh],`,Uj]]=Y`%$EoqO?j196Xu^<ZuZV2W4H;c'$<jJbj9Z1
%!m0@>ZQEl<YdYOW2YO<($a&>9fs>&h;c'e7*KBJ+IY<MG-Sg++f]SQpU)Ro;eLBoD00+tMBYO6f<O2/(7Og&TRJR`.+idS+*n44A
%+j?`.Jf@[o9,^`XJTY?<XBqKR(N=<E((&a6-!A;K>VHNh\Np4Qq59/)bhCJr4#M;pg7fPlRPLH@m]?i+EL`Mt.):'UGbb?S`[5;W
%Ob\S;5W*_K<n=S+8tRPZ\25gYG+s2c8Au#q7PhVE(a%-a,@e<uR5k!N!0Ya6_4]4Li%;&[o4'`673m5t+?)r]9Nrr#-GMO3%<)%>
%(B<]V?lfluMb`>ii<ss(80"&2(r6ihD]nR9dC9H:SlGrfDklo3)JO72"2Y>49J9T<CrP[6;J[Z,`Mj!sNZ=mE@_%Yl;qto92ipTm
%"I,:/F":W=9Ih7h.fl_*$Mjddqc#h=pB&'2I>an5\2$0WX0BTn_EsqR_b%dQ6lnF?\:m06]o/7f_*]?U8Fe)aAOl2fooFkQWcVb:
%3FrfP9kdV=\#SW)(G7,Z5`rHkaq_gfbg)7^<!Z6WV7I%.N8(6tVlGP2(QcW/Hq^(j_0nA8_WE<@`\3co`-fbFM#&J>pLFh<0?uF2
%_BCZ\/%@[,KjM.G@&5.TX1Odi"cm3gQ(7T,ZF_CEE<?L#@Y,kr=`gG("Z.PT';gkgURFmYOGBB%[ql,0]IJ$G(9p1dV&DqLX9oA2
%D$"=/Qd2ZTRioXZkOs6Jl;F.$jf*=[!,SWeel_^UNtP!N.CeT$XpdgR-[SbSV@`2J>&Yu#7S6JkEu5*SR)$d6^u=h+L1>$G!_/qY
%:fT3bM2)8s?kB1@Y,"<<4FY]2TP;t2O9c"&Y_`0X07fp#Qo`56,MF"]Eflk&MAZf[Eo/Y<&LN4nZLm&Wbf?U2>3Hbg:`G<@_XeF^
%[iUPPREiQo_\[!mV!\t7ZU:i0doFRK\T+o3Md/WP'qO,S.>.`Qd[fsJnL$+i;<WcJ1,2g=b$5o3:G)c&S8Lg9-?(G+?WNDu6UW&o
%/ddn?)%Y;m+T/%\HN@[?@=l&F+-+<*/ieG*(tb.RK[L8F_>VMq7\<?:"Pp\DlGm&m+18]c,dLfi0i;h1_CLb/^`Or6c!un$I*lDg
%R6m]&)]e,W*np*o.KV@#FX0B%$3i!#@Sa;PnMPgM,Q)LC([;=nNB`Q";tbDji>IJT7/C5nKPqF-#a-ST/9&ZW$BV\:ZIAJsBF,Nl
%F[EVeoiW+X8[2?^I^#1MDfAW9Vhd0qYWN<],MuPDhbT7p3hZpAUq.MdX&?,%(t4EQP<#`J6qe".`i'AtT_^u.34n8`%pB/-&I@Ht
%;$YMn9[Gh4jMM.R@Z8<V\AY+9+I,TMVJ]6>2Kt<#FsdE7=>O,K-%//,19;0NhJBm(;5$m(5Lp!>Q3*cZ91IN(8hVmu/P34ICk-Dt
%+<tc0YaCFk0TsreB$.FHZYF.X\/6T-m6-Ztr1NYaod?)d/OgblSY'iqJciOn>rBQc7gpb@$qG&:i*KojjPa4krJ>7i_3hc/YW.6;
%i8I''X%5tD4i@MVMuXu0n-G?2a`VpH#/Q_ENU00rk:=i0L$>"D=Ru;<`"&AC!DLa']T%\$!<aZ(_@Bh08tU?)h^-TBXE/fRq)A`&
%Rhn3)]&sSJTrW<GX&aa+@R+t8erNt)^bdW1mmeEG^jD]OP%0!M)487*M2Yr1:Oj\FZjITG%^=Zkf?d1Cc@n3)G-?*\dq/sWUg-]e
%FIZgb!cAg;>6o//$-N]*_M(<CFl*QTa`JVf0\V2M*D>]L,@]niRTET#QjkVk'd^C))eXOO5V6&hf=mH"hnktgUYrqhSH;:OJV6RD
%@[8-#0AOah$:ru!a;&):!6?.4dPb9"5gZe"N<++(="TfXO!#0K",SJ\oqYUR0W"8DVJ%LRDN'=6EL9+e?*6;!-cY<YT5d3^TFiQ-
%aBWOG/TP-=4]2sQUI:8&K+^PeMe,$VfP1#4<J&m0&Ca:P?@\qGeVCu<:G=ej-T*.Y*sIlI1]Z'',:fFl>qeM94V/$K#>%Li)7;7R
%#YqB;$)EWpdc9;fiN<eqYFt`u$7Ougk>m14K*e-7W*JP;3b:ek^4W7"fI8M?M8mHDB'g&Q>H(5`+PFBk(bV(rrCC(<4CkQsDS.k7
%[7=L:9['XHC6$_!1`,C5*ffd8MJF+?!COgIh(h\5"thfsb?P8]j28)k.q'/1/\9P420(gGhXo?L`5bJ;cm80b3Jo,g"QA9jdnNt1
%k-o2Hg!C<0KkZ_N_KG1<Q,&Ii$"kbn*C:F"5`_!D)8t]A-"heu!go#Pn>pTY)kW?$2FE"/0qqcDEs91Y7r['Q&<<P:_oY"E>\OBg
%Tfr;d;2Z?p^%A-+kj><7kN&Wh_dmseq.c[j!+li:DG(6JP0he6.DpSqF^08;kd)/@0nQA@*C,:%U<?Km41q[,i3(egMW7OO:AfGq
%^i0",?*>kiiY5]&l*.0bFFZYO"[)<1-Qoe_!9n,@?j<qk5e@HEU/hGljMhfD"p>^A:rS]\n*`cJ,_^@H@#GakZ5"laIpNHgeV%KQ
%TjU_!.QVJineXgp^"Pn!fU9-AWe-1chh\5H6GM2[5g2rB,S2)STF-4',c1S#&L8J#7sYIq7nL!_jtuC3-('R.]i^m4Y,`koF[/3\
%jiu<__[E2iKN`*<MRncRM!j]eaO:naj0-jk(#0r=IM=]!YQGQ'GFeJZJ8Z]Pk/@Pg[9dQ]I<;^2"0?*[,Ta>,@R@hu-:h[:27+?E
%$,JC\*SU'<lY=d!)btOkF3EqE>-kUgZ,%'*@oH'HA7Qrkc\@!"DR__lUB\f$Nd29RRq'MBY7n*7P8#j*`$H>8[gg0g`'^mqMhna(
%M,'M1j2LbImj>c1>[6;l^%kH)7u(.'51"XR*-3sd1%BK6W65[]Mp-c*?9jFT5ZscfHJe4']T@5<?9FA.Adjc"<^>d6OncoHrKjN2
%A(d1K8W@+cN1Otq3"(XLB=`Bf8bU20Zl+Ss@1jX-@iUoNkWC,qoNA5[]&5%l-t\"IbMPRg+IM)g!36.e)s:6'oY\VX0`m<b0[1R=
%&?][H,n,0cJ?;FOl0,2)1fq)%c-lb-SPp@\C0Au\GGEcV+l'Bt0#F'6=8HOFc`7m"RpnBYm;XE%mK9)-5<rGI1C"h;[Xnh5A<%tT
%E`o7@kcohJ-[2"QdkP\@ojO6">id`&4_lM:@[#CklQ!a'2O%.**dje=7lS7>^t*.:l1(I_A*E^9+"fJG.OX<)\pDga$=W'Kcl86:
%Q>,b$0l%+MYVfrD#2un?.i1L_JOUp3:gSp>ZX%N`l*Ou]D8[_l6&e?!IL[k.bYPnPNXG90S/MGaCi/7'"R*q<,1j53isIn;_s'rF
%k?qd73ZBtCY^ulAOU(j/,+1m,7d+,W1=E.#U_%/m!L/fk2nk'uO_Q7/el!ip"3%8t:%-OJ$H$?:935L3*X/>ke1,R'-35u["Cp\Z
%p%[a*6m#IK)8dIVj[gm:@s;B?]]o%B_8AN`)3?`9M;]@V?gcO;UX+SG9W/lN#qDTD4?pb`d8)9/=%;/!3u#N04+3s?\Ul[gLjMfK
%!NK[n%cgq]i[ugoQ,S>a)VbE[SWMV@Y2;:B!toNdZdN;F$VMk[5[4U])<u8nJQ1"7IHh>I!MORPO;*H-#X"6f3)8cY;\(KQM#'Q`
%@d>11McYXd_>4AK)X2re:__/b;X+GL1_jh()'+frVAFcIETGQ@G7q]%e-dl#L,!``AqE!]p(F[R%7Sbl<WW?'#R2dZNYjgBdP?,W
%2oXWmT4?)_T1hTpgX?hRBt_Bs^FAD6VXg&lJtPpr`:mf-aOZdp7LD=g%7YOB;lH#J<V\J)$aBIKY#R>ZRG^f[>1-/+/YLJlL]rl[
%Z;<u7h@&fa'a,nL-.:H:Q4ODV!Yqe9Oq]YtSQd$EC+jO&1!!4g#u%\=8J`LC'0gE<G<r)hPdSaS5+(]iCbB68!b_A)R4(HiCl<HG
%,]j-@NY`?%.G,&^_WSRu+=$uF]A-b+g8ZIl.Vrb6)h&VljCsCG'qY!Y9/!C68qnWenXP767]$$E1eD6(BB"#]Z?;LR_',M&nQZC=
%KHZpiaopdZ"ILE@H=1E!0*'umED')hanAJ5EQ4:&<I)t)`[O\GB#Cs?5lMpY^"A$L%0.t>"YK-gY:IC0?DSNV)<`U'Oj(cJK(kl2
%0H9DX^oH3)&F!QTARo0K\0UB[,LR_TRgk$dqJ>l1:8;Dg8V22;VW@e!l@^*Yb)$`hPMltChElM.4U[*<]kP/GKbqB\SDf<f50/(n
%ON8^d(LNrn?,2nUCf&Y=I_[fqZOjeDb`bL1MHocLW_tqS>H2Fl7.O!sgsD5;#Wa"R=KPUh@>^l=)/RhrW6FoX0nLZJ(U1GXkqf<:
%=pl,V-Ib>eSbG#n9[?*dpTVcH:`6fs?rfg+#b1t;KI6b*dI`p4c(&"_o_p%3V]#Vql6:M:"@\&WXG1hO"'E-8766F0*Kh>gOWmi;
%QT9-VYa-C6K_pS<X4>Ls=>V92\>i`J.iu'N8=D9,rTKN#RntbgZ#Ri:Wq,?\"sf2J=>9IRSocF6*,/t6JW:_%VdNM\aJ[+Y(T\+Y
%H2=DA]HDs<VJ1@=CeNfRMrf<ic:JtNX'q%]U+^)nGE=])KYBe$ep9l(B,)45'=mah@aUN2_2siM/g^gi(J1BlNLBo:URnZ)bjnL?
%arQ",AaCNQ=^@_U?-%eTCt4ss<hl.N^A&`AaomuG1<fR.LmpVr"ZMJgeoS1W<OtZ](j.uRHBB'5A\o0U[XA:AIK0f=3JJdMgj5YL
%LDG9PZF8B5_`D:rRcl9\dq2?AJjVK!c?9UQ$AClpR_"fSW!m7\l6GFQYINNK\7)!GmX_(843;PLSX&-$&k2@YVp6Xj#ra9X_d'F'
%!!GuWV&?3L,5oB3"2+\&E"L@Fal$SSH'[2*7<eik[n5k*Z$oZ-*Q<GVR*"5>c.`4WCZ8&qjl2Z6GWdOL=An:2W)@I/VNNr!NPXTI
%c-/\p-EcFD3'qptFOs&%b?]D@m%$EK"L'0kBmhc)fUs[fLZp\+^ABZi.@AU!1H'`YWbLVbR-e>p,WIUbS]X-3hR%BsH!RZMO@qe]
%an#P4Zua;ON^=p2+@Cc*Spk!@p#PGhs#r)*a?)n)8M:[K4nDs!Q3Km%$'goU93/pK"]nKS%u>;H85$O9lZ3eU(.:["O_-j<C,BDG
%0gWlDjM<IFkMC?P`*P3O<Q6>o*iC7o0+8DQjB3.8SPI@;'J-3oK@GH,+sk\n/tcc$X-,AqU)$ppiVeE\><NBgr-(EL?H&\cpLH#/
%)-)6Kl<l#Abb?@!&D+;m[69&d_GYlMW'"`.d_F?VQm8&Y9'T1K;BnZu$*H;\MFN:%o]A0n1pp<udG56:)J@0<F0ls=$,Oa#+A"r-
%@R2ZS!10:rVWWb))S^$H#fuCg;KFZKC<P\T>'^o7<tfQ#A6g'Z)6"a&Z'_UG*V=^oaV$UOU?#pJ\W?rO19D(LbDVNuJ80l"l&q0.
%^a2P-Seu)_`-Gg1enO+pc*T=LCNfD:)_U95b0r,nf'P`uL7gBN'#=7;;=P#SY@[]JaJ_d4&nu>`,GVW-=1OnQ]8GU2494BTV^5:`
%YNs=:lop`>e=cW@Tn2XT`AOM`p;0Fe[)+*KY"eM$YeB>#,rlORE&R$m,"78RN\/H\AfLBFqoHiX`bt'AiMTGO_&0+6KO%%K'a?<h
%Q?)=_8V)UA,&i'.4WTgOkFDpnTLIgOW(3SD_^'+GE5gm]RWq]\SQ!$ni>`XHP9d-b9Z4n3FRh'+:uMNF)OYAF2t-F&iJC%89Zi4-
%+D:dSiSH$(#ILE0]Anq@#r>!kF@)97_Aq)>\^C$LUBH0j)A\+f-&O,eD=5:5:]fe'"!J#B;_X<GK-=.AFD`Chf^rTqU0pX"A$L=7
%\keii?R$;u8Vmh4"L.hV&jcFSm\Edj0o*7flp+L$-Fqtus,8@QZIt)RWth-//#^[C?D<P'F=:P]=/f`eU;X#HJ<it5qEpo@>QUJE
%+Gr#G@-AqRZMu(pM`MN-eH(g0JPh-,8qc%hS\E.T/;m"%M+([RGb^&d$,R+uk3:^#=l(daoPm4`<[$-7Wq+TV`f3\d>7d`J&[ags
%.Z*O!?:gm&",Oae`TF/BXGXe5:,A]]AdGNW!%eV]Xpj+7!gN7\Z?FqM"IIXC;j"&b:kBJ2+fT2jJrk8eKBZg`7;/A,67Bb9h_!N.
%X/<t[b(70rpS'IOmB2;uKDJ'X-m@F"aPeF],*AsFP0OVaK)t],E*foC&9bmh65g,$QH?(DU%0TgN`ROO7MXheVqqt^h;SPjWL@Tr
%$@Fg0D5's`m!6IF\hmp>!i+[?GgWT<7@>)?HBf7"!OL1LLo!:ZG8\Lk6K`X(<RGb0Z6L5lbQWqJ*J=R679eRpUA=o58=8X1`%01X
%$OD5:QJdj8:I(_1dh7gUPH7X7/.'aN-_R'/W)u[sVIg-#7"VB(#?ar(hj$VnbGB9;kAaJ>V%7R.0n@?HHcAHoR3BPKmp866'r%TT
%MITcjW/RH\r4eO?C6PaC0Ue;#Ak8p-QirBX/Vl0Q1=iXZ[?i#6-e\Ja`P[,#"\g0l`E(1!'H3<kVd#`fkXEa"dU(ub^P@2WOXjFZ
%3L8#J=(O:+RF8SL8<T-pST\P>P"Uc.9MDk;FcHT/'oL`eKt<-k3H.X\,cjp`Z&8W&S-W"@!`,EV&bP#A`tg"c2<`d-rD9b!WR"L.
%Q3:)p<F0RN#:r#LQ$e8/rok_K,C<6r8Z4m[,(n&^ZQc?D%q6ZJ6q`h"')kTWlU_^g;^W3)F3;[(L6=&'NSCP7Wbk6)Z#7S.^tf*\
%Q#g%5!\SJ9nu*g\Bq4%[ZN<FI:r*jrb?.dG*'qJ8,WMP'5iPWZ=%+S"N5`g7$%gQ8P?4W)*LgR$$oHPjD'1@hVCZAZV[(Joq>*I7
%E9W.?:6<KiN>:F/bYHkj8-NQT6;!k$a!<+0^g@:DHqEuu/%(SrKLf5pl+&X[K9K8jN-6gonse*fPB2UI:Qek:Pg8bk7i7C[5Dc1K
%;7U3$[8fqI&L)<k#)56D$e`or\J6rt'_9M17ibHL0J>&`e#V[/9GbHSF0?0,V4cLt/WGY7Cc(:tc0PQb5e'N>&[*81%bum4jOB-N
%XR#^*]O2XR-nPJ,3L.+AGM]g+0r6W&T,K/SAG90'R7)W3eos]Od;\?a#u1W:bWJ)6m#:7BB6R!W4(t5M&Nj(WBH5+k=[Elf?KB=I
%Os>nO=J>eijf`f$WdnmX&K^%JO4"Z(9o#R5[95ahh1,Jn-Bo)o(*j,1lKF8.%X.ke$$I>gia<Zp@Za(+644nZrM&uM"fS8*&PFFL
%nY?NQi"mTJ)Qd>2/q:*W];ZA\l7A@i"*q@E_%rG_bdms$pWQ:&I=H[kRT)?d/O0m2+U($2p]@Ie6k7[@fSkIc1f-bEGk/X2.+_j5
%P,!%@)nFIt<\5(frq"us-#1PUb%.k[PF5#!+AIMCSm3+Se4W=PhqBW=&.^iL<,6<_FWtp#dYU,X%=4OQb33h>ifTMmNFUuXoLu*<
%JLk6$$'nTE4.?Ki[<Al$0M)b[_p"<a4d.Z<2VAXQ+;,Yb>+c/Bj\UgeFWYtiVUR0a9rjRZ9GZ;jRD;HIQ=%UHNo)PYC9i)Xf;$Ns
%m7WV+Jc=>&ZB^.X>D9'+O1'p.p'M?edJ'01T!8TIDF1(aLE6hMUK]R^0U9B#P=bc+o<P6]:a^aiN-C_a6()@>A?PU,!O<HHF!WPl
%bEh/h=lbt$Y1`fg+H!n3+]H^=@U\W_lcRR$-^TY/K0sNobU4VQ/(Csc18762&CObAi^X+*SWcaV9W_TR)N7n*=S^GK=2KP?_jQ-u
%(a;9mX?U_scne?]NPiYt:^:U''^&MIVf>f%&IMI<aP5q9,Ni]Q<WMTt4B-[qQfGulHS6EH01Q8TS7/pGJs\^S3P@,c,l,OA_O_Kk
%!iHeI>R&lf?sQu&FhlS"9q=7N#WJ-n74t3C8X+!8WU+/;.jAH'jGY2D^cE(CmnGQR1)A&tC!:Q>`s6rQ_3=Tn"usO\p\>+C'B$BU
%kff)]g`hu(XfuNV@MD(JQ,U'Tfh=f:28%*a4k##d<$BCP/k-c=LfP5<?HWKDem;)]0t>#mCNL':"Cdq\!_0IA=t_u?]VWN6:e/=N
%M=:ZB]Z*PJ6JVWp.[=0cZ%)8S.c\UOncCH+qs;66hIum"6V%R<25(j;(6nL9-Pq_'LLA=e_b%cJ_b..(1=se%Y\8<RSDlnnZ^9b<
%,c28M47B'iGdeY.:s<.SUhC3.?kS9LB"oZAKes2m5Q0cA-H.dY^@?[+:NE_!%g$@Qp7W.j0drb"6+bN4'c=g@01Gm2$<Anm8r#o$
%<b$qtE`+8KFt>7SdYHC*[Ejk*=4W/8pddAqXHbZri&'kAHs]^t=/LR,QV7^Q2N+!DP4+QP'IN%@9j>)8gFd>tAE0c0]N%Ulpf.g_
%]95=/[R/++bN49PjB*a0YN/Y2O9IM))[W(4$`[l:\gppFn2r)-A72BE"n]C+_3$QTFg4r+TYpL5fDR&n/pA4^(@$h!I+A<44WM[p
%qo;:]FQU$.B.V2b8Nj^4X-En<c7YtY"EjCo;<CR"JJ@M/bcQ0p/#CdC\D0Jb#pXIE/X"nBgnGZ9(4&KoCgc6L0W^U!Q=!SF+l(lM
%nme0qEQaB"ahDlbG.I]6g8YjGNqC^#2R?$8..>>';+WB_o8Ak6gE1HlW!%']U-*i!_l-LXM(pqk*`=O'cb-Ti3(Z4PO@VneR8h!M
%SWt)`<#PW+YV.#[/q=.Y@[QB?^uHs@E-5A>D)-1qp2%Eo1GPd5,q2`.NK2Dabgr8NW@L$A5uf3Jl6R/,J$-mQMBRF6A[NZFOe/M3
%L'?@$iK#p.(!Tp`UTATWE(?m`PZ8,c)SeUHg^BcZ<#+T&?A#(S"d;R-ng9NTafWE!W>Jr2WXZKPKRQGqMGV[_odttl528BHESVem
%0\D`Q"ql5@@RgRb;RK7r?8H:_YXf4F:6ufG.(p.Y#[+=<[2MRh1A#&%o`q!YRW4`uoO0"Fj5;U,Lh"_8fs+Hq&W_%HP@<osd$Y:4
%s/"o^/%cS;U@V$GAn.NV.)MP#gZgUU\;<7Td2X;bR?sM7%GksLK&8mZ?^--F6$_"Vcl?K]/eWe6o%55'OpYICRK@S"f3!obJY%Hr
%cWe0;a^2cqJtd2*j#F,7j_hJB@7>scU3d)^iim8%k8ai2Q<EOSn\^T[4-h4mH+-ed[*1&BWQC,I9'$u7PaVeblK"(5e59.42;73.
%(-nFjoR'KC2"F,8$<P`q<S:PIhCiTPK8W?KX1;q5=j2O4P.3>k&&m0P*R]fq>H<MtM:A[X7o;,Qmgtip`P,_0lui;>o#j)+0R?;1
%6^*T_dDV-5&r`_kX<QJiJjq'MX<g(maX=bq29hNi9,*@?PWWn+9JOBigh&=<ci_8i2;YY;0%JsmOU;[*,M[R!>n+,@n<=g7,qCZ(
%h,U@S(MaLi=oc-/"t)Bh%D7iXq662*H'q7.1g$#t>JDj^P=TP6FTC9+OqM"^8?WG'c^9kc&@ZIP;4W0o9<,u;?:lK=2YT`u)$!Ba
%<@g66+L>r#k.0(FBXeVa@B&;]Ud.981lcL^MkP[S3k[4NSm@Y/pljVs(c%dhJ!fV'Y'e.&``lE(eH/-XcJ#t"[V#0BMqVZsrO)a4
%&mQI^]4ac1:bnAjN&u)r8N<*H0`R@tS(+h&/Ug!hN/KFP@-TVI.FFZ_#aSW9<%0Rs7iE6/<+]-n".cfu1A.&9=f/oE,?n_A_u_^s
%?g@>7jAJ>#?p1==P2S4L(rTNMj(l*@5qH6-`p/I)lBJ$W!7B3R]h4fG+tGRLA3mntMEj.g7DZ,'7F93j=QjFj'!VWgB19Doco3LD
%Iu$/f4TBN@9'#,6DE9XYaZc*AGJQ'S/N6CB,gOfI:;-F8`GD?r$_J/\WDajM7$9%PRT=U6TUDI,%>dX]`5EJFO5d8+N)3`$Ca\rD
%QPM0lBUB1V<&cAk\E>Nenm0)6M(]LlQ$UV*Ap<pRUE_?Ghqg/EM\2+gH$RiAX`JNJ%<0!cP-O_*APU-2Ll'5(g??>&%$YL'Q%0hq
%-!oU,kg:P'<DG1RGVV?6'N]5T*76Yuos+SfKOlL%i122JLW:O6piT[j'0pEi%u3q"'4nW?/\5E8pF7:@V?n["RS0@%01-+Dc4Jul
%*igfI]J9fu"-HJf_<qTpo6jf-"f<r*?t9`[:pgT2MJ6>2jaQq;$,4b&KlGKBFL;-[_C<;X@u3%nFXL5+H,dY=P&?jhC<G[Lgkp@]
%ic[e10csGqX_+kBbU/6[J]e/_C0H<c+(UR;G9'/q#'R+3>I5%gK:`J$mMUO0.lNL7`_i(DeK*'bJi*RU^D>a0*:Q?R3p'MO&r]pl
%-K@F!Gkk).[fmdF<ZM#?SDjosd*!-4,Io[)h70CjXY-rs$qFDqQ8h\LrA<TYP&9j2C0k&+aSaq:>7$WAI=BeENY#'je>7-Qe?'m^
%gCPSF"U$JB2[bDQ"$@*GBu4G&Tn*W^VNtS`mI!]s#,tCT(i^[e1rnOXF"8DsF0dm(;UU?,3PJ^>bje0rd[Qo>j_E#;X,J6"_T?J8
%;ce9[%[#`a*V\+I<W/m[2!uXWR>;25ZXLJe*JU`G8;sg`?s.B#Xc[.-iq_JPkJ"&pWh[%r*N.N&kN\4W`ViE.:OK6DMq+%(8kT`!
%V4RY*lbht9O!f`aC0"MGqMC0hK1\1<N8d/Ze:=YSlWK&.Z[ssA,P)T`AjBc28Ef.q714`d"^;TG:*\45$E3)<bkt^>"XSCb`>0RL
%22%==XNR>;?%p+Tj1p<RU0Olf!e715XNF8@,rd<.79]A4MjG5@k&r9;$RhC>V\9Y_YlYi`9gC(4g>(PNI_.mLjJ"M^.M<S-*=5dC
%ejudcbTDG;T;;hF]W_D.%+m!#5UNGonL?:upYXHH8smZS&laW:=Cr,#@%Q)Tb"P"4!;!^mqC@XWL-<0Dd!I\7))K7F@/%gTn#d0p
%_m;t!3m,m]*KJP3%L;GLK&EcLgek=0+DRIH2q5<CB5/[_-#%]h-X%8"1RqDrFD%ic3hFL)Nq?4Q6FLFD,*EY#>O?=,ibPn($O1"7
%\ZEVF8+],%02brWKn14QZ,:9)5m$1(:VsBPID@M,LD/r=W,72*,^,JVjcpTa-T\"m<Q7u-1A1TNP,f>BpHgE,)eLcm"#`.fDn3b7
%n[sN%A`)erSmYBe$!iJOHB=UNo((bu-!HCg`o[XhJ(R0"d'^]LA1S"7@Bh#jem:[oH3:ct9;@0rY5Da/`qL;jf3[=+l.o_Br8HaE
%0!7CN\GbI,q;%f!T6ts+os<tj:TlB7^\C^MSKEn7T&&4.gg9T??Tc)NHsn'`=WO$0V`q]ee)dpJ>?(1R0;"[`PWsWfB7G2QF*fi1
%,G]An3KZHOpi=.V.@`7a2VIbrOA:J,dg'N$^\gNL[4/FF]"1#=G]\CX[<C7WDD*+3CN%Y8X8Zi3RJXnJXrjtT%!]#-s4X-Tefp\j
%]_rLfGZP+=pOC1Cr4gg4r8G?b5^!Bn\uj;+.A-UcqU^5!fUi]=nA4'JrmMl/s26".rt-o!k-207Ls*)0:ud4#,rV<qWG\8E-Atu$
%Q/q8@KSM3/RF=s7QOE9\fVoDcV9ECKon']oW3^";-_+Lk;tT4An*!k'_G']O()sr-Wct'\.?uML,0VoeI6OgN6`Za\3AQ+\[TCkD
%ZZa&b;A\E7Ul&]JG;Zea&pr>G+3AE=_qfJ.!j8Pp>FX(MduE)ach?/<[;o?$F01rGOjI/U\&^1h.76ig3lU$9UC(:9ZC"m-ag<i0
%=Nl1_/saoE'Ci<%s-_NdWUaDB#aCkOM2&1n3`t9"@JGke;A=8AB(a*[3fog_d9E1b)'_S\9N?qM?s0JY=NbP_CBkin@Rq9(XT0Cn
%!igm]"6Uag]&_B1h<CgAc.rTsHDX[LWI\an=Luqm8miGE)C!%B4,5d7B,XB/2mub(-f%,=np&q!]n^.8.(NM*!@<(`/m6#<%26kA
%S6%=+\<d?CGUbX:OOf:.8SR!6_'_nU:dVWVa;p9'*l)_S=bT_bW[Y1g@HXcK^X?3FdTXH>b$l,9.+Vmu(2mZI[NNrB>!sP'!,jP7
%g21pUn;T0lGStmc:Y)1?0(a)dOXT9D2<.7NY>\:/XH71mTIYV-0m1"FSACjk&Qb;_]0amJE$ADMmT(!j=(r<Us'ukOG,oQr1mot.
%NGnq`+eCQ/;Pk9DdKirHEsjKr8diII>jJ-BXK!*@.Bg&6Mgl/6d+1r%j9/:h9+su]=6TjS(M8n[L]ZXX`/-"6H'1Zd=QUNVC,+Z@
%,"lQ"1'sNdU(gWn[LAs<?[DRi'l:P,X@'\j5eJIj36g0d#`4KgqcH263)[rRJtWs+0[<%je5Q>7,d<IukXe^QcnL(PaegjJOe5oK
%Sut%G'E\DLOSAl%9:!epE=]t)RN<bY+>7=01t8Q;`f,/4R$f8]b")gj_&N,uY#MnD7*9LkY.B=@W2B\<lm^-9^P,?$$eUf5e5k^`
%LV,+^I\]Si0p!16[EE?p+G_>Y+@\tX#[(*j$c!ks!B!o4:2Du,2AP_.O(@SG$Hq_oPK@mj$'ms/^'2h?9qn18"F^IF5nhL(j(`#o
%p9Z8<di6\7/*aa.'g_/ID;C,nRoAX[S5?b8=,XVXT2UY1+ZpbS\k<3dGI\Uk._N0V<='*Ek$f<uQ7$J7L)//"SW&-8=Y&Ds#$8W5
%;nk[kpX"$HjBDL1TI+Kl1.>[!QZ3jR8^lp#*)CgDaFLa;.Y[.OWPA;(cJIP)#$No+P3RlC1n&b?Wd*WO^^?pMGuW?-WIS;qWk*]8
%UQ;bmlIQ@pJ6"sf]GEa-1rjg#7Zf"(_RptKX(q"H&&+Dq;bg"?9?(&]s2,("S$b2B&$&fOBi>a]BsZ?a6aN@o^3,38omIBaU`'Ol
%j@3P/3oajXQa#+eeXOr]n"QS<'+b(a!Xb8d<]bI:Y92=]$tJc^Pho[^DpsJ/2H`D4MK5L_U`L8U!Z=6F=pa]aVgY&;2$J(SQ1a^)
%E`Yh+nL>JP/TtWYW5+X6.8tm5TgBEUb)W.d5XpumC$Ep7OA4/VX2R/%$Hi+)O^']$M2=-+$MIkO<E]1DOu#*(Y2k?1`]2mEIeI$l
%:$;)12*3Nf*2Ci"P1h`:)F5XW7_)BdRRnA@]iF9n/fINg)3M/iGS(sq:]rVHHVO5koDQZ%;q*m*e=`%]6sNA0l*Rq!,Us5C&X0PW
%.n=.%aK_"l=O:W;P`&5n>(Tjmkn7;R]:XTn#)Ta)K`s$g>!d<3c2]Gh)&:Tr?ISV*[_aU7Zs][jU9.f)1@aTE%s[AS-,C_l1uc9P
%%TskCDcoF\<)4p/%O;)XO6Vm%)_UlFg'M)bp@_([25!fP(bmIO!\i\lO#@7rqsT`sANQ*.][.,7JYQ3a?`@($bVl4T2/P_JBm2[8
%>CDkYMQQKjGVJCA@^0mTC'!H/M/e?BiKa'KOJ2sUQJm-FP.65<J]OBO0eL\Vaj[L_//ULYRkdU:PIN(@>g'/0kA92he5Rqmd>SAp
%AV,2_C<75B>3<OC,-N;HWVX,r+E-fni-A0=ZR`0&f/c.',n8DXMH>,,E;``+7#j?!=.ViDpK#Y)P5M$0#,,piW8kt$-<4/,\4k80
%?&:Z93-HT.S27:V[)'Z!8EBQC.Gl.[mNI1M]I$K:gmdCt?+Za2brTA`3NSKW?&4?$M2.+8"u!<J'#+Q"LqQSsA%r?p0=m0ijr?do
%'.pGc^eAWL;-9lll<P[#Kl52A=#c_*;;=[S%W>SpQ\B]Na9^BiWf=s:#oGLA18m7($edO#S)d-sOZDJp1N"oMgTpl@KeLNV@l;VM
%grk7X6-PkDjRij<BO9nb(n]k>V/4[M^o^nP;'=;k)hkB.AM]\&C-,W+b/e,-T/,K?4_u?+AD`/3<j\RQ4F(VIL_(d;-IstND#KJG
%;R&QVr;rktA)\)\9^d;t9jiu@ak<0Ui0$0/;e$2[ZF(1='0)!K_m3rn%o=]/Q9h7TYklJ:i6.M6lik4"3g1?.IgH=oA6#:a?cCO_
%](`S!lnp.rAJS\aRtfJ:;IiBVF@5V6R`0u\2Zcq\;!Vd=7KKR!eDJg_Y:$_kXPfDn2hnF%?&nQ8\q1;!+I-eVWFfIsU:+s7)eq\G
%E\?s^Do'Yo_h1P7TKs2:EHE7jYX"Tt*la`IEkH_%l7-%#mm2fIPSp]$q)G?^(hNsCDP9Fjk[nM%%`Xb]"ZRDGMiJfZK\5>KLEb0B
%hI'kEoe8*kk_/eYeuZE-`2+EnjLOMp4CdZ=G`p\W,#4+`)5qLf;eDdEKkQ"d79<@`F!..>5Hms&+N5Kpdr^Ju8n5HO\g>Tb2g(KJ
%2TT.oVDL&.U6iZKWhO%Qgl4W_cqc[$DP>ZTU9hC\&k^.V(0V4sB#AJVN$7nA@FT=&IK$tK@I'aXB=Jd.6fFl==Gk/]kh_sK+K%O]
%<3<1pMGY)ln/7pp'3o;j<?f?I]&4tdB"CTkI\MM`T8P*LEI,O8^8F_o9dc\bXR>&9]e%G+^et]AdOdl:K=O'd1Y`J3L;$=kh":LV
%._"j=].cK8-7DAmD3kIHaSZjsID*`,AfXgY3-CDpAB"]b9=.7NV4*HD\$sK6eJnm,$?m#[.`Y3-'W\u[4dJeKVta(:OJ].VXoXet
%M*iL\>=A^+ch%R)UlI&1Z<3f2cb(1uH@7bqR3!6)KA-5-abAq!",T_4\E'7@RPq[4+N]?]Y<pWCd"IA+W;CZ@KP0TPjcl91@$m0M
%:QtY_#*mQ/IeTflVlH>8Q!pC!b,J',=Y)[g;@"\h)gE0$r\QT['djr+n*r(H6NGY.BqbKt(_:n:S!\47Dl]IQe&4&,8i(h(0cZ>P
%1#=+ZR[RQ[=$6#-67tTEP@Z[LH%;0<<$:BXlYZ1UZh5jn17BQL%>N'VDKROCJeTdmEbY-l?-HU]SAFCSE=S,o*jj5t`Z@qDog/tS
%.J1jq9g]=t),^O+kQpI%c)KijTSge2;Vm&$IFULmq*R0^72;_jcRYslg]4<p?U^O6]agV%GA32$1tk\E$.39;9j0b\-Bs6e27-l_
%ee0[!5+OpiAC'b3$J$h0fnin<PQ*o&%fJ."0U6VXq('Ol/$@@$qpZa3AZ[bl5DMWMMopdCN=!0l8nNNc<P6Oa-*;fO\W8"Vj-%@0
%o<'=o!P=`'Fa?4@]JH+-:nPO0_KtAF&"D4VI*fEsGBDgB:tpb,hbL%SGG,8%MNQb9[p)B`2B&maWfm,uU[fh'b$QnSc>?p`A0uOE
%_22upObp._N<]ik<Uj+!iKbN4.O;l"iN1b4d*nSD/sm+oLiW0H;WK"p>RRL$c`Ynp"!JG%6kK(RTYUoYPkjmuO@B82%@/6X`P)dC
%.+:Qt9/;"M=j\4>E!k(9+EXKj%"iXg2ofaa`5;6kIo1:5W&D1Y7SAu!/WZJcJqK=k6)o:\6K\H&N%%?A3$&'@dtX/q2^VBT;*gM4
%$qLas]U<J?`kK(5K1h$AOe[`/o!Xbub*)sj@3'L0H*7C9Y2,,<_LP4QVQ:nLnS%rY>cXSH/F"4ih@>EKU7eS>W.T0to`lf8rE5lN
%$^3WnXXDd#/86P9#P!I;*6no6/\>1_(YiS8fhjn@?u=36%-6))')j#_,QU&bf(!]J[>rL&h(UFkFqCU[$4RVc@I/-r]E71_'5pln
%/!__2O;JnWT/DO'<Lpk:3HfO%;OV"i1p9hQMoErTXZIWRgn1N3\'E+C`^'6pMF(M0jDI[-IE>r7pSOms>NCecoO9DJ$Ku#?$-F$o
%+aC$J4/n/iA-p8+OG/>21bu[(o]h:%;S@Z]2L]*oFB(<DC87@%Ki9)OH>7R\,c_$oTZEApPA9*2(lp;?;QR'(,1<r8krGn6+57Jk
%,A(E[W?u9aQ4?U^4JhP2!-0!q`CSH=1SN3RQMZ=69)'6dD.>`B6KARkTP5.%X"\@ZdBjQBXg2(f_rS2Hpc(qu7/0)2aFHN_DSPTR
%YJA\/L=A3unr9F`:1!mj$7mt9<Tn*@dU@QlZ]3$f]!&EFFR;=Hj%b'Y!]KFqI?fkVq$=OKq9>,$2p\ZL+.dn(p@RA6jQ?!Oerf-l
%>b-<thkc:AeSpTmPp!A_#Q%aeJ1kF._&\k+\p8=>IJEU2o#]jCZeNfu*BnbLh8;<<H$f#+j:C]CQ&kB^PonI9,j*2/kf?_c!"9Qn
%la0]2U'Z+[,8Nke?Yhr$:LBS]PrR6Ko;6bJY:DLN7/UacL>bNa92!f-^@`T>OT"@s#H@als/N*Coi8QC!sX4c)P>TOphDq%rKO('
%:<EGb8f38GgP[=<F)V@j(S9DbK>]LWW%RgmYDpH!ctd.[3:VG]\c"A8V?hfO4G^!jMagF,TQ@3,d"ShCf-ZI1+A&N^VSD0@qsib@
%dp/C#7;F.IhH>.O]-N'0ieHG\c?`o)ii!6jHM+l%)c"'e:MrBkFibgf2L0M,,!u_7cm!,,BNU-O*5/R7C"0#5\Mj/A;suBukgNM#
%op-q]5b+@#@Y9h.Z:oMR5UO$4]s'D?e0`L_\A.(Rqm/B\1tQ?QY:U>Li?YJ5r/lY(9#A!,CtC>A9c:?qo,R2e3;*9\*.=8jZV"no
%Q:LKc_qc88/I=3I<E^#uYSYlfNnT02*0-LK]R>8X8"Z[4XLkpY]@XZ\k`A2pmtI:T+`,DO`VNn1D>[u]LWqC,^>Gi`:K)-.H=:c;
%2\JVQN.$T2l>X$ED:HF>B!:+9hX,Yq3;PIH_=KpjEZMTUodl;3k?8,\QV!Rd/]rPQ^H]UoL?-"@$W5-VB3CcD6s<4qm#O>adU3"$
%G<Z--?Yg"'5QBgAWR`m%r-i".P@qjf.1J0p&ZH=N_CB0Xeci2j\61C6"I5Z_^_3c&Cc?XtLp.(iH3(jTapb9/:*LIK!VYD,(Yept
%`,`1\$^!l6nu1(e^f;mE#-3;=M*(\Pl$`dLR'QZ#R7II<^#>r)nY"h-5><7-#Hj3N!/lGXR0r.cRsT%iWQemfP1Dm^Ag%PRFV2"-
%U\6EU0-M-`ok2g1SN6nHoAM!'i#'+3_pQ@q@YE?%B-TaD%%?M2<!(<K^m4%))ZaP,h3EPLFuO6>UBKBEdH@Ki++pj!W`&sYok[q]
%TiCA)B[0IF+s"Z!n5,%KJeAe:-VT#^8X?l9(>2sf0&DUWlVP8Q(0B<u!)8?qdUs/&g^s<_S`pCC)fPbiKRoKF?^Jb-%A7;aL#N45
%/U="D@n*o=[G[_]HQ-kJ"C:ZN$.W>cYa7U"mcIr;rZUM&l>5=]?nO>S*aI!A-YH@d5mRtd(&LtOg^b@kZ=>@,578CmqRVFq1dRSZ
%3H9t.H'tNr!P8JR&eSUHK>\&okf,qfm73Z$6`-tupTQGb$iC6GrEAY"11_#,>5+tj,=&3gg.:,/`PdKc$Ku_:][\-aJ]OPX)C-7o
%=^7^2n8IDb-Jpb:Ua>f@d6cMZ:cPLG5;jGedk8c3'J%a4kfNm`*e7WHo:fc=B#Na8>8-cF2h"ERLbtmr<\eVfC!R*rGrXPT\uL1B
%2>V=M%G?nL@koUD-CEt$^s'=2/I3Ri00sUEO6L&K7bscUdX85blJ"asg-C9>%O\jaTjc6TkgH1?g\3%&e#J@@pM?S'R,Br9i.J'8
%WLFY<GnP@@f=jW(?EAIZ*=E2:?&R]-QBO_"DCJPs;b0#Z/eI89q_)_qQI(#J7A2@%0_'CTKqI`[T3eLMp>)bAMBoRQZ^,=RAJh$*
%L<hHIah$as4C3T"UK]CW&nOihNpeO0!(36">M5*pVnT,R+IHOH<WZmd!2lA;[L)DFUT_V=&:0Z3Fj[X0SF0RiAZD53(.Uag*iQ6%
%dc/u@YhZ*=FMOT.";-):!1dtaeAaGcE@u=U5h#qLFubLQbQT6+1f&i]K4>9i8o;O;fC7,ub(C8'iA>0'*==0`Nb^^s1XL?^Q(`=#
%Vc,b#4ZjAOFF"256Wq3RbYHf&Q42Q2ZN?e,ZjI]r!&,<<mS0GVQlCUs`V3fI6::&P6sX49*:pO3ka;B!/k8@Wm=4j_P%1b+I[S/E
%opf#X'V8Lj:?u(gNkfM`9R*i8e%GA5d`0QPCii*s2Ki[8nEpM/f(EXrl'g#9d5B^aO62E/;GN'RYPMsB0>qQ]i(a]O)+=`@Q3i)@
%oE.511rJEb*)e`YfI;k%[PX'i&p/<r_?Y15]``TZmN2r<W6h23P8_B="sLCLg="B7m4-[>RWs0,$m@ghG.?oq=GMt63-2tsKu[;=
%".3e_e"KOsMIGi'D<.9Nk)c2&H!9*k&S)9(c"3R.I26HQ>]/\N-?[/Pg;rP5]3.[-A#V@ZcAZuE<GP";qfSC+`@r#^QK!-.j#m)5
%M$3X_@!bZc!F+t$mWYdnD*)CG`C?cI'lU]UAi7HmG>nn/-oUM%C:g.K*HXB&\#VD;J`uXJ@H,$"R3=c,Y+EL%V6DtA7YZZ^jN:(<
%YWq:"T"I&r`rJhHk6@I%faVh3E`RtEc/G3NA(:LRcg&'M&I[ei^QfnH;N^="OKh!Y6ARUK<e"))W`Nf#-^1st83ddqV`eh4<2Sed
%.sC"q=0_4bSP8eW/5YLF82DB6IDo@uMjVHC3*>]_8_Sl$AF3hTU0!eQK[fB:H1Fs-dbh01Y>!P>7l>9+O=5r<85n=2\:P-P'@<YN
%!/&mXJ=H.6j4P`_B,p>n>Ts68'A,dS*B8ERp8'5Bd^BLmC/=+QX"PZ:'Vq"!5VfdA(OXQ8'':JNg;8E%jUsJt/''Eaq/mSA[7=6G
%U_qj0AC\_:>!.FdZP?/Yklp]WH_4sGWuR*pf!JkT_%]\m>^"#VPm+mS>9T`Y=%=rBcAd7?JM#l4.@d==Vr,iUXpI3)f51ru3l&K,
%OZlj>>3='FJj8bUAH;/!$g=X33<hF_qLk>Aa%g@MMsmR9Z?m^YA5]C?IMdW7)!@F>=2Un\BYi0?_"6S]"5Z+pr8B#Bk%NHa*Z;j!
%YTFeQcI",fdg?\Eg:i"IJ8[`#U*!VR\EUF.++6iZqV>gM-$3V[i,,nCAVtcFfmp#g!DZ-^3B>X&3D!Xs%/GD;PA5F!PR/`,-.cF.
%9S5:S3LR)<Y'Gk1i7kt:begaS[10XZN"(/NNU"U;HG0a;+_i#eW%Y3`m,u=BYWdEpMWNmgeElFQG#M4:`Ci7GVm`5k7Z=pZKp(%r
%B$M2`qGO#4EI/)M9[!sUl*Ug>?//)D`a[G*4)gt+I>jG'A31UW\T]tckH<L`99M#>b7pf5$%1T<pLj1N.(*6sE_+2q>3t>hlY/qW
%]F#i-GYpn132nHL=.Z38&O(r:1.2_%lJaW\&!id5nd;4kmoZ\nVM?^ECc!oa-iJ"=o,sE=]q)9o_];$8rMEb-[O3FiE@1?CnSVm4
%B.L4?%;-SSCu@;06%Q_!f_+P'lQ".n]innZ>p(5^%ACu50WZD@\90+8"A\[M),GMli"_0`%h_G9,^=&Z)OpC0\/C8`3u3[@`dN=<
%gY@]#DFWZ&\_%$)+q@b/&@9f^o9VBY"N[([3#gJ%JE'Zto4&$'JSa`;iF9^OD8o`/`b=aM1D]hHTEEtd`WA'#DmpTsbB'qaNQjG,
%L8m(H`cb&\BK"nPSCAC\1tu"GMAq.#rKc47n8q$:k@cVI9BZdGgP9:=2F5Q'>_IH.4*!4u(CRNUdP.g2,GE^u]@hFZEFUnunSU6;
%pN9?QDDa&4AocRXo:rmYnq8jOXN&4WBF&:+i'R+`!/EJ.m@VYaPTVs0iFIrO(cF]te=(NTEFtGg<\*mS5cIr&a_9H58njd\>CbS;
%":W1=LF>B;GnrtsTb5pn`^]id??CUd0cIQSe!_c0dDOH`T\)/%%E80eZ5Mgh6fP<fB6n8*N$kjOAj#,0Aun*P"EQN<lMV7RY\Z5<
%Ae2L+Nfm[l@;b2@jYC\CJ<S5;P?7ps)j^qD)%pmJ0d(Ln?mqL5)7:3Cc'U5Q@(nEQE%tAp"37-hhoP4oZ@\cPdhikn\CgP.n3pFE
%@5Af<,r6:?)i\KL2B!)gFec4H:h-Jod9)*+h(7:0LfA2'knb+np]bDdi-B7p>)g?6Z$eS^'ed@Bc7HAqb*1@8X5=+Wn_2T+lG^bl
%_;5J,_eLanM2IP6B)U;h8c9L7d#Dm@;]?'-Cn"&4ZD:W,#3>m%NFWJFF;4hfD:=2LJq[nAUF&.iTU$/2YrD0QNe^Mm$L,TZ(]#@J
%VR1)JH:5cKCHK0Dd#ZL*oZu0c<1IAqcUMCR_cc'tqeNPPDODI+mF:A-ib2"FBkW[)?0_sd"fTGXnBj%\dPc!mg6!gD:>kb!l*u>(
%"MrcRXX4qs\iY:#)kH=`VRMV^'-^Q$0fDai)=PR>!LrIO[?%Q?2[VNI,E+]+\="tDai\Uj3+A4mim,."``>'g2aK4+/L+D[k5;MQ
%E\4EHHoD1f(%G_i<)PD,\D&qI\@$6MGZS_dVbNX[TRO.0f]l+X57f1=VP!S<IR.#_,m/M_2A-\kVU!6uRl7]9V>6cHjCa5NAo>E`
%D$E`iN@,a/DDFnL6o5iBPAqHIm[)nGOKsGncV[WT)*%GG=YEJ[B4l!i.TTK-Tal(B6Po*dqU0eX/M#DX>chSDM,V=%:3;t'WZ?5g
%dSV+H6PLJLB[V@bW7&;^0pt"b7=AYa%1hJ_jh$@j")d8`^o*#_0<Euof;uVTl_?-pD7c2#=<D&+d915!F>IiC7r*;oQ&`2-F'=UA
%`Yh+O3c&W&\/0!.(=PUF`OYEAXc7D;>p?.!k[q$B,U,8&=9hoHS8Fe1q#/@[R[A02E\m$01j-)9a6a@t4<<'Y>XRQ7P:;\s@!dRK
%BTs1n?+*;Kl"h=aP\(lt"E(>d"!=OPhbW%L`i6nC]-BP(qI1rsR-Jj\,;Md_8/N3P2!l9bR!ERJM"o/\MI?/D@VG\%TVP_s[o*rs
%U\F=\%:(?eEOFL,GE_G)k0,$[$66R<6<13T`[S"t@W<#),ajQlTmPADeR_dAoa-1FV9J5/mAKX!=/iP'DUj&%3kNhHO8=i`h1^)M
%@^k%<5a-DHfn_4j'a,n*GKl&/C2Zi,huFoam-)u.q?o9[2#&'q38I])j>-)Gp;noFW/sP-5$g0qC9#[(Dc`E>LCa")N2_$?=Mtl_
%=n*.h[KjD"!UT\SXSU^qb>Hgu_8lP>aE9#kfZ>uBh7uUW&12XD%b1eNn$VaI$KVsCE<S$uZ"V@P@r%.^-ID0#B_qW%bZKi#F#mP1
%[QTC,hW='NT-ctBl53o1ZiSp6:jH=M+/`^]aLcu_I#TCV0lY@d6Fo\%pHt['Hs=ZYC6\,O1sZBk[P=XWUF#\SW$3f3>MNMf#.S_C
%)L+2c.lDlc_r.XG5VPC.`>^AWqn]Yi]c$LcTQpWpdY=[[dEGIiRa>;U]]#(U7hTKO>R9(n;lr^Fjh;s>Vgp,!(A2>G3(_bV'OPo2
%>_'3b".a.9Qs6PB]`aDgOSaeL>8gGF6!NM2&pBsLIU?M3-FIR,RRS@)h@Dsu9/h`6^PUdlS`j_0%!hu!A\W>F-Jmm(VkJ,`e`OlQ
%$Ie;M-BcO(AQn;'`nl5SUE/cIT?Drd1YZ%cFbn62&h1iP0s@Ka9W.L).n3s^>J>fuRRbQ19IjR%A6P,UF^PVAg00@\6fL;Z[=V"A
%N[Au;qZ+,(1Z?<-8<)d'Q<B0rFLSpjFl-Y\Dp,Vj&%<d%L\#]bQM]6sgW<B>f@V)c`RmeDEGBCRUM6i#:KQ?_0r^[7%eGN)>W-cb
%g#4oFBKAR@i,n$tE''\M3P<Q`H"#EWULV%UOk2-/'Qk>L8/&rT\F2hKYsu2H8^Zb*>p3s(RjB1gdQ\A2M@giSZ;nH-I$[BYTuNpR
%WdAn%hYS[bX?OjLU$GhR@PprZ7+U;``/@=dH8J8@JW=T)PEr?DI;<NL->]Gg)gF:YcD;G/n>6P31`o>q?=0KVZBj103ig?7Rud7,
%Sl=g-V]1kY7tm&GFn`JP=LWEOK!Mp18Y(Mn9qK1,*5TF;36!@hMVI?-lo)jN8abch9O7ALN4UgODmp>-/FE.n$&6a:_p+JLVYdm?
%1M?S>:2Q6S"MM:$Sk:pYCT\[o3O@[Nl%&sZOaqc2QDH>>d0\%'A)Dqm4Zl"&2qs7Bl5#Ct'q/67HNes,ICh:GbnmtK?D_VphVj_3
%r>;H?E\:RD'VZ<tme0e;Up.Jc,;IJef1?ZmZmWG[eHAY'[_^#6A;'bj1b?Xi5BP"(m.32\p"qkVdNif6S6#(JUe9^cPc9N+#"(,X
%SSOEM]q:*b*)S)qR]$77>N\8BaAE%lSQf$*K];;p[nYeXO!f<k'dd5k1*9^8qRI>"Rf3l`dA.U2S#27EL[EL=<&<6G%S"l.T"rJD
%2jYPMH?rMJDCn5@h/joAf:/\h`GQieBBBDgH>S:q"/qR?a'u3f4Do4_q]JDPX"dD*fEJSJ`1/o%4/X9fEl]uFKN%YhQe`_<ZgXBf
%kW)KG3Q]Zn5e[jc:5K4G1NckmeWG7#fCeTl)Qh5",XWrulm"=daR2uPV'IMIZ!+?borY`fY&D:ME.'8)K^2i^Vd82;4*H$&mY'n1
%EMm+"Fq;2kh7`,YEMm+"MEBen<nn#5\)L/p^(kkYqGuCVlWXt8PhBhG?/NfCQdWS#MEBen<nqE@\)L/m^(kkYqBjiQ_N1j<`LUsr
%/+/)V>B-QHhmO%3)hqkS2efCMA:)q;i_I"t'mIOfXgli_DoJ8dI4O`=lW\A,?/LN$iVHU1$.lZG::n5W+'."&i_I"t'mJC)Xgf%I
%DoJ8dIEV#blW\8IY&Cik]>!BMg@[&,'<Z8aST'##HbpK>F1Ii\IEV#2lW`3YVO6[RDC8]HE]`7VQJ6-*\(j`g^?'OToe!Y:Y&CkA
%E.,psK^2i[Vd82g&$,#c'[*k3PO:K@PHJi7ST'##mF_(`\'qKu\Xd\fbO'uK>Met)W9[Z*-X>OSa&a:t7!*k#mBl1`DC8]HE]`7V
%QJ6-*\(j`g^?+I0lW[[j]=r,(<i:T*::S%*qOtO8j`\N6/[lCrDn2E^I4Oa<orY_;Y&D:MG'tn/K^2i^-X>QOShNT=hr]^+lW\?V
%>i1E#mJ9l=$.lZG::S$',Jo\Y<X)iX67ZQUK&.:"YuYJGqfMdn9Z86YN.?8WN*rQj1,:_'@-:FK[5%fTd`MiEUm8b!heQd3ghUI0
%heQf3KrA$aq`<9M*7[dV-Z_YdU1J65%%btk9RSpfHDHhZ%8b[!bbNk!1>?fX>a+QN<lX'u,8emJVj2g\G![^#N7T[&*,/se4OV@X
%GY0J2plSTH7"Z_dN.@h514gT^AVrRCPpu..<X/-_?7UX3][t:CG^e.Z-ab_M3"OHJm&lU>/bW?c<;D2JVRR:C)>(jR/$nQM%=Y+<
%R7F#fATgGm[5.>5GHK(O,J$pq^o^XL2lTJ$,KIYMXj#2QWg,TaCTBr_;;#biB@Y-E2,MbQ\eA88D7<6t.BO!_A?V97d-\4rG!4Vr
%A[[e3[-UmFF#5aS/$k^t?1Dle=1^pACLuX3FI`];I&rT!oCc@UH"Ls0;d,U"Ml0I6[5,@6S#^n;Q'nO;S4&@#>+)-GV:l`t0:]gR
%Bk7_0BOr.rr*b)<X+M(c$'D@n=bRml,D\ighY[t[baqs!P@l5JU$BFk1in`F535j<OS0]qou`ep/H<esdEY8s0N4#c6TlZ6[T0T^
%D;UeN7DMJV;f)1D]O,9=IZ<Si\u"3qQmap$TG-ef@J/tY9@qH']7="kL=FBp>VmU_MeglnFR@9VS?t(.T(49P.2I7POM4,e=Zb)N
%ab$"*7R1n&@@!mG%^rna-eu'b4i<M7rJjV#3+*]O$.\+<Gh:75bUrr,,AFT0c,pnAhiNnjeKhuPDrXO=TK[((fk>$@aH=r:1PH/p
%Ju(Po=D$/rURWbKo-%D2qEV(2>sM?6'P8?IJ5+1S@Uon!0=?-Qo[2q"Q>.9j%+GoS_J&s'C#JbI4h7Sael&\C2cGu)PeEhQN+u$/
%d^c6E).;:aAnJ^AAuZG_XJ"C(X+mWI8teg8;jhM.Gh:1g)&>Ne[o<b"8Tj3!Tio"jLC8BZJIS06*Z"lQ\f#K--sGB.S12TbbBcUH
%$^JHS!`$n+@[BGrSEi6:'12g0j"a;g'4b-IAeeCU4Mb;b,7[1G>MKrGZ:pQq!iIp+o$PA?U`dXFG"+S.C*/>T8OfUK*Qf8i;5i4,
%(f+P'Eb<>'E^gDDEA63BYq4oPLKQ!e1m_2_\s[SGTCgU`9^,m!8MO.ik28971I]dg!??DP-?0\MPg+M*g46>m.EWGR3Np#oV5M<k
%YYXCHh<ajoP_r5JY*Z,bdbOMH,iQ$Hf(Ce`3>qpA^!$o1HC9=;>q]]uU9Mj90;D+Woc.sM,FDOD-^<k,WenUpR]9rUQ=dhSg3@B$
%kD.#8CXJ-ON"^riN]^(m[%LAUMY"IR-0]c]0o\nW,:X]!ATCSU?$@pMl=cBIS]eChYut\m@'UiZ=1[tO-0rFMbI"$20l?,T8Q3nm
%p%q"tBa2VK$f%g(+*UUTh:k7'D]3=W[n79_enmcJQ+CWckZ5aO3AHXii$Je=_?]+9al=f7bhZ5oV`j'T=mYY.<0Cp7YTmnW>h*e`
%[&7022P7I;X-Ma/l%gQ?oGrC5X"H>J#WR`ZgQO7f\259uj*JERIGptI;Je[f.9@m5X!-^%7(Gq8X0c;?AVOK@)p6^jHN>@ualMLl
%@B)mXHhKpqbP^iDNajI.j>H<B".;(>NP9Vsen\HA"k/3\;I^QmL9P]K>Hd8tmp=IhY>066ReGDmfr`l5mt\d@2=79cGA*0'c(!un
%1T,%X7rtT]dbd)[PD9kCd)Kc\d&Ss>GZR8=l'$nU?q<[ihGm8tRPkkZaOmY'[jNNSAoP&j==;?(<u_.<=E9h9%Q!_A&F?8$X2R,:
%W)lu=Q1i_.9:&D_rMCiNl%g`DLQh4h`5W@WXM81M[G>Rn]ui1lS4:Drhrd*Gi/YUtj;dqOPL\BlSb!c9GdK^Co2]a1;],sRX?3g]
%lkXNM:UU+<m2!m%4-rh,jZ$MB2/VGm?R_G5GU'V<TD;H!Qg"Ng?)B!^bJ^F5S+k+Cm&<LEX5p5h[[ReoBclAam7QTXAsW'%*)4bk
%fi'_oqu6LAe%*00Wu^#[e$Sl+*j31'bV$Uonqs7PSW:`F8B^?V4b6h@i.2E:BqO5\/2*6FNt-W<RQ#e*3))JH`gu2t0fom4a_'r*
%)VrlG.XSs9LbR_4P@18l1$eR>L`']4\&+D:lS_WXo@<2Sk=ENQp6gri9k:QT7<<%-k>O4__0P/LgIe)K)sa$VR@WNJYcud()p6P7
%gK<VC5$@&<WKVIB<+34cc1UJdZ=ne-o,\Gp-1,Rll)),L7aeDa>=h.k>j']F"+VCf>O.edQFq!(@Ktk0P@nhb,>EELVadYLi8qS8
%[h7&9lGD;;k`fOe/,Hf*P&7`91brXOO3ICM5,\+">cfoABl@crL[-D'bXN/'&&>%U4$+Q0E3\ZO7M%Z8Qe;^$FH)J!A-+\jO9o8T
%ja`MlDD)BSm#+@m$YFh>>sj^dGIodZj24!joo+@S>!#p>([gd#$G4Cg6;$Of`Y87*I_52BU>337H+LZi?.&$^'=b<"9__`T9=?A!
%A-c?7iNf_@/?RfaCn.8G1!DF?N:UbT<b(Zsj%1QX0:\K;I4jrc>/rLQr%VMP]foGLH.[:?cDmMk[kFY.fT*[RL8%tW:#Mn,-[]`l
%8lLS/>C*R7i]S1CKQ/4Vc\^&VC%`t"k&+o@LR1(_2)Ps8#Tqc\;0G1*7ie,gVLuuQ-^7ZFEN9$Ek*n)$b7-H(f`nnqSk>PC@andI
%Cjf3@&Y?q60c,pErb,3T*OD;56ggP*F#1PdA%1Xp`]+cd\-\'h:CmOHXp"ucCaHD"&s:16[(mcP^.<'gBks/11U[%_F5^%#!U4Fr
%3_%OJAR>D.WqXXUWHH[NGp?C%ZU,Kmh2`^u)Qk1qI/k6'2f;0<D=275d1PttDt>7.MKuqF:#K'Od.(6@Z",G0]!hiUNSE=E:j(b2
%_+SZHlkHJf;kCcJnP[2;p_9aro2-Z(bXZU#n9%]INhU00$K*/iI]6B(&XqU^D<D=PFQ=iklLKX\o?m=adJK]DXNgTRN`.=aKRon:
%GY(#d3lP9M&hZ8D-S+9aVh]i^>%nTa`%nd5XQf-QDRnFL9>iD@48Wkfc/,^b/(B*9]^+NC7^DCm(G_.J4`'VP77664fj)@8P*Oid
%c6d,Uh;`;UD[Nb[?KQU)/W@!IaOr$mo0psUgVij"!OmMe".u++BFdLlGPAF/dnVGU?aW/5P3l?UL-9HF?,]VBLRu^olaB`)lugB*
%2-^uD0A\ja/>i4[VcWj0hI8%J8rEVQ/A($iUL6@ABD6"._$h@7@h'KDG3I@XNknHi:\Z&.ENW(G5>2lqc`XB+AZ^5:@DpI=-d6-!
%nB:@o[$6mRl)F$_q_FC8@9sDb>U4JC7rBs1Q[P8J'elNH3()FNP2B'[IMkJ5LN2kTiHT*@B$]:8k2*YtW,$M%DJCNi8jAc5b**Dn
%*=.BO.s>/QR+Ym2cFq_FlrfXQS*o'?9KNC,ot%+8-K^:`@[b8dUTr$)m5]mpq)jbAoEL>&H?*:JfFSNJCEUs!c;)Fp=$4/k@,#49
%*M8PO/LGS`amP7-F+;]:Bfh\U0?SDo+5k8XR<DJY30Ud+iKNG=4XrhdG#=+h'dV`Y^c&*oWHNs+_&oV,8e>uI-OXuRE,Lj]iOEf@
%YLT03/t:K-bJr)`RE0m^3juP-(!EG/J@Ju7q\3H$kg(/If0<D[9^Vo6W.NN.$b5ii2<rd2237(Np+H2cT2'7lql@a2-J6AVT+C2U
%J]Q-XOLZEfPaLuT>up8a*s"V.mId-EZ7`8O2IG(W/:Z[l6i(:jCg$].Qlsr2q6@?gW@n$]e>5Ka@s+a[R@cuTq"7l.5-h3sMk00#
%A6TjoR/_N_O^:,tYG01nPMWPpOmoI)4UIad\/F@&2[QabWF31(cZ?cEWAp^2M@jh,2*MhGJ,H)TeE5BJI^S[kajclZDo;;#<ptgR
%T1O$FD?pH[KJAoD]ljp@AN+2qVVVdWjC[j;r42g(NGdt'U03(mIZ*8me$&SXG6:%JiLJ;FXkW9\5J-;KheY4+h66G*%H#>i$[RI)
%EFuk849$6abC4>5qTJ7a@ldckSo!!4FqdR*N'c!s]bXN)F_-9jV`q.rF)IS5'b6fn-]q_SRZm&,laFRJBC.t]A@$%M[ELZ`>Nb)F
%ApgZPm(_u]eiB]7_.^ou2mi3lHTg*qVm!#4ZqiRn,]DU'O6l&SWg`*r,RrN,]$B-I:GrmK?^Emd+ktISSjuhfl.>nLm8cHsN:22(
%qR`,+hf?1Eom<uIDqe,;Gr#g)h23RWFiu$r&kU-ABO)S=e@T-,+*XXIoTYl6F%Q;BbeFe/qr:V_qQtO7Ib^Kso2B(%dbM"M.D'FJ
%UhW=Vqn1%QA$7Pfm2jG@>mI/FT9eg1HJ<RfDN:97do19"bpDoOVX&cR:WrJ(P[<!aSY+fiCG\2>G9u@!f7!*CgQ+/YS'STG+Qr\^
%R(_BK(-L,3-V)JN7aCkiP\`;qC.LCO!rB"\hEB3`krbT0Ou%S?M4^.!=$ZV@SP-4jXm__@r`l5t'Pfj(g9bW?-0uOsgk5=?1&$Ok
%G$CPVS'i!"a/=?XM:QfpDGK'7=.Z4WP3TdILtF8J9<jmu%Is%n_LB4&Xf%?[^?`LWRIeH:pW[@+/!*^0)pI1-,g2eET9CM_G9aTQ
%D1DGHJ,#p+]^tRYh0YLOo"MHb0+\(e0Rn?1_9L6TiN7<Rjl;,/GBXk4f0?l2qt015(XF`V?T`]4nB^Z-5.pKflatD?F7hcso'(il
%l&VJ7fjC?-gtp<%Zq;WjLhj');g0\9Rs.'-34o%Fm-aT<0CnJ,V"X$;o).Lpq:X<[oq-\QHhmGD0>>pks$cqU-i_O'DnUSorP,HV
%et9K;r9X:MUG&6eH?064bND+fqW@XlgWSop[>92('S!>Fk'I$h-i`]%Vtb/gG9:-S?iU-bTDmH]q;"jGiN7;GhgB`iG]<QXfk6'N
%cE>LmFE)[&c0aPLgUFrcb6,M-G]n4Pq5;uRVk77dl_>Q*j7(>;*1l$C5Q'Z%Zh<eI8)HMcP;;8BjKJKtM+:^gc`bX^^@hdD5PXI)
%q=We8ZRk.T0C"IEEVuoH`TNTfanl%`cTdo5Af:<(rHt1!^U[[Kb\*F&55ad6lLVp,_kZhXb>6^rDGRTte%b_Se]mL7e`"p3`=mT'
%VrIDYQ@Y[@GB&'VX5]%[$J`_$Y781K;e_eOgPPPS-ft(cO\[i@-L8Ch"Bu];iO@FjB2:-$+6#?eqet&6m@=?&169[$crW1+gJhr,
%Q$nYei;/p1EhdYQQZ$'4n%4WVc5UWId@@IRrVGO"p;;N4B*X*hkL[C2>)mS"aVcPChn%c"=NkI<[.`26'p8MVa+*U#IFic:'f?i_
%/`.Z2^ZA9jJ%'o=muI:ba7[3uo]a,ojQ5`R3<f3GX=Lg>J$8jrUW_cD*cJ_nnfb<br@<2>h=p^pA,Te`^-B<t!S5106J>Bb]W0N9
%pt`Np5.^X^l=.FS]^r>Y2h/Q]-]neoqUO-Ho-]mVB6P/IJ%rM)](trD9"so_Fb^]?l^`7GAZP)l^[psWa]%HOT3kp,)\WQ\:QEWa
%"!8]a2NJ)kk+>HLm*XYBq<),VZNk-h0$i]^h-<0nIX1[^k?#LhnBQf%[s'KOkK?hIs3@ehC?uhecek05`,kM%rNj\FCThbHf7u#\
%J)C)JqX@'MmZX$5rj2TpepeK+]Hgs_Y+P=^DngbPhlLiT5N%+cMmE=fe,$)*0CQ(5L4Iu`rT<h$h<iJM*7XdrY`AD$h+)K64^QIa
%J)5&ZLNh<VXjcP(epEb0SmIt9acDP$htb5P2*\\Ij''C`-XYJ6n'(D"n1TR+\1XSuVH8AWN9`]6nHuOTIWos^M/*C2bP'b=QYV([
%2#.Mc9B6="?!Q?mrV5OM]D!qL?<f^Dp7(C8Mm-\r?TdA&kT0AdY(20$b.(=f%Ueb2`%eA1j;Psl)LU[@$J:6:91$K\378/9\bPJ4
%IfHR`9b6I*n%Y)_s(D;r^CF!Z57SbTro66!iUK^pY<S1Co_S$s12q#Qs3"iAj_ml$H9pr-?"FJSSiUUJ^?n.66.abm[q5T_!Aj#F
%?%2Zno\aI9nS?_4=&-t`:r4%TM2+(<LL*,V2Ea%mgaM:'Q2g-8kXt]oVL7E8pS)raI$Qc,"/8g.$mOP4&H*7)=_P[`kp.:X9J9i"
%QDsUNk1T"(ra*(fhu*#*cS"WRo_n-$Hf^Ubh,j!C62&C*mZFef-J_1s29LAN'l_oe=#O.UGEiV]rOWF)D1aDCV3HSfH/<)HH>%r6
%Y5c)IH\tS2k9O].[oISBa(KV3P9E`)/t[NDV=ij\p"CHaiGWj,l)NohnAc4*HT6"UMj4bG#<6_;Vu9RqeTYcC'JQE7DdG8W:3HUg
%!:(>Fn>WL@[nLSDY.)H<([KG'_V3d\H3rq1M[+`p?U&e>l,6mCRig-ks7Y-bcb?p!rlsKDJ(ofWJ".>4@U@\>p#*1*F!S?+VgZcd
%k^-h&8:KeeqHh.kf4MBcNj+#fFAY9l-L_ARAJNf!ZWm5:'&p0_-m:.O's?3O<[^Sq!]oG3eu9fIgY\`8^RsBYJZispE$;<tq:T+-
%cC[a@pa(eWepG(`.em"']Fl+NpTLdGl\E#fIY2q?/anMQ8XK1/([lQ^r@;)p]VKq&HZoASO#OODNpgudX09/CLZt\:G;J%+$LqqV
%2lBa>0OL/RrfZ"TI`j&1moqgOaitV<^\bp(GL*_emEOJ?h8LI">#0dY/__=O>PIpTalQm_Z0#kArVXdTS#;V<Xs!R^/aD$bfp050
%iq@a_=R7u9ms]_/IWoj[>d7Z0MYUO.IXe)^?_/f=>OpXiT5H<hE`&n8o^_jrRnKg2Y/\63e@7FYhn=MbIBi+&3u`7K$pOPmC>`ER
%D&aA5_o%tul\jM1o&cioY@'"J]f%h<5$jjj=b5>Oc0bIf^Kc/_Z:\2tW0Yl5O30g+Q1AA\5`IG34T1YGlWSZ7<R^*NF6gkflUPZ/
%\'7&3m-O8eT7<(<?gH89)QQ@dg=X1%:%&6UfmC1e:CfA*^[4<&`hE'b6q9hbA[[\uBBQYhp&jXNg$RrA&b+8Mlc1>[nWD.8DZ@::
%^::\dgIcQ`cRpZP'mo)lX66s]K$U(Fo&Ze;p>;!mmCNi&Zh(6PQe[Z%)fEqC?Tri1>hnb=8ooI:U4:sm^Q%eZ8?YEMHU!8GTuCf]
%H"9>O$,#]#ISQWO@pZNCh"$nNjrZd5p[Qqcp!Y"S@Yas#LY$_t/AUTD]jA1ua+k1s?eo".1V^[eq"Y:,^f>:oDHO`]RuG0`'F?G-
%j%!bU^CP69^3K,?@X%_K'2>1*kYmPgm`llA-K(F"QWEIbNun;c=%a=$g;eI*5Q0X=TDqdfrT9;NrUo.2:ZG[MnRqN=TAR?&cgBRR
%rf?sVhu=r"M,O@sfB<"*eO\7iFZ"t9]XaOqrE>_qml<s)0%==cSOLh*q9P*pme'q-DnVH0Y5Plk^:ie!PNr&nFd;F]3q[6IT@BC^
%J$4@*dhD`]o8!!iooJ+`J'1(B6eV!bnk]XJ\2W^P5Sh*V=6bX-qWVe-;UZ/];L`YXeXBiNZX3LFoH,T4[a+=@S8V&]/tY8>)RlOZ
%.P<p]Y.*@+3hr84#&hQVL[4qbpXRObUjC^Tb.7fmh4Sdks,m8YE^.EQ[Jme(2n\YdmeGK.j!h@qlXN=H4M/27]=VJ*55b&"$1Zl%
%nWSItn=3+A>3+:u^X&Zr8gm=bn;&a3^fau'J`TmL=c\T!5J44T]_1cleK7!Ml2'SXbQ%P,:D]q4r*0`\'Ar)%Ns>!RJ,AU'mUB/]
%]BO=d[CBQAHYG.fHhchq5BV`gXT(EXBs6oP(UBso]1T\+YO.$u'n1o3gYthh/o3O!&V!=UVBjtBm>l&SV=iTGfm[Eann[9;*O]`D
%[A8ZjCUHK&CW3*o%lS"d^Tk(RaJ%Ol*.oO)>Pi@Sg,"%)#P%VTbjCYOo6O^9,2joZ\ZB?LBC7F%.SMN,I_,7Bk'd-qm#<HqjYR[,
%9KZ4%$uh3P\ME(eap?q:?Io/7%ge:*o$/T,=Jsr,TB<A'iT?'T^>+CrHMq=[Sul.h`PYnm=YMf*Z@n#ueoj):SAc-`/^,us50?^2
%D27PnlI-mP"[/:VZ?iWgA/1j@@m&?t&$r$\c>-)3B#MF&3*#TT=Gomr2h[[(HB6&kl'@FV>p^H%PY.)QY;1sr'km?Q(q;3M^<LI6
%b$/ZYi27ab)RoNWb]+^am6YM1EY>O=><?di"4g54M'fW8]3sBFh04:7(<:R/DCZ3<Tp=V*;'QCm,pP4A#kP9`224XAWTj8_?i2b(
%\GP[:[bm#j1h#7&JT?qKfL`)@a[:m'A/A3N<Us0i)f'(l5nBd@?'D_Sfm??^$2\+R97RX)H#W&iU'oa,A+Ds./e^EKX?O:7OhP1P
%Er<612gj/2&&rnB:PE^&9_eUul$nUiG9_SGe(P1Gq8](1VdgM:C>e+SP1BMsEKo>M92\GS;?*!L[Eh'+T_pH\0:uKO\GE$mN1rjk
%-HH9ImD3iOWrM;Fak?@Gj7O4D&'f&WIs1TtQ+K>*n]BO(+-B^iS%bWJ3[jIQS3MLDEIIT<.^t$2_uPsMCrB9R\p7kP/MA0[J+:Rk
%hjn%92qj:9IfI7]^QrZQko[,'K>0OZ\m?JgGBS1SHhlpt]?VMhcF^QpBDUBo;p>".QKbsOcRuCBhd0RR;\m/Y?d&K*%??X]rpT/c
%J,&$ip=[2Sd4"Um?VEV@0:ol86#hoBP*:M]q9OOd?$1R:IIT)+FEM[(q8MB\l6^lMqjb1R^4Dn_?9_aHlsHWPl^W6BEVF7Ujs:p,
%5Q6(+MXTCNri8-BjgsGLW/+bpA\?5Xs32q.2r>>KpD5Kt/]b9%a+Te&Sj.^JGOslQf`#4A'`Hq8c9t5H-g\DS?[emO&F3r1d-F`)
%^TXL3m#QXF%HdC(Y`h9b;>0Hjlsl6lOA#<JIpo\`I/-n(eR)e@ouYfppJ0,ZNHY-Ok'_Dmq5;PFC5_>;^(q.d?e_m$]\HtE:"jYA
%%EGsp2X/DOC=8HP8X$_ol\4a%o]C$+0BA/iS(l-ZY5;,A49!WC,=u:$I!"uXpAEp$W'%f'h=C$rQMBFBIhC,TDf22;]kGUBo]I;L
%Y:dQ[l0Z]^++GkCJ33;oVL`r([su!!>@f;3q`Oc+HS<pniue5*BbLEC_gc!)IQW`pWJ<E6'!q6D*B-4^>0N&2h]XYVmk/L]cH]cp
%AMQLLL9qMCDbn^1'$jqQGh`5U[:lE6g/e1bIK_AbMW<YSUik9J++1D[\Ga6ZF+/W;nas_*]!jd"CJgptVg"J'0)l9FJCgf1lTP6T
%W5k*[]/0:cIHSb[mp):\q,iVF0=<:-VqDr%&Ae62[l^_0rqu$>rNkfS4[8hm`Sn_(VpD!h6JZQT<l4[Wea7fI3P2kM[`=u04o=#P
%kWPO#l.P+;@sg+nqWWu>F849KIX69h2!<\%*Su3nGB<QCm+Jt@RNpWQrLr9l#8qtPSDE%MH6[likHZ*Kr';O?Y@%"^:IfWl+DD!c
%ph\Rsr3[&r"2G@ts)rW:<m\ajb9tW>#&i)F_r[$VKO!D[q07>cY=H2b#1Z3@E;Ak_9$FH1MFX-*XM+,fc`_945JMJm\!DKl#N*;#
%2)!^NUWHlt4&`Wc9cZI2]m+^?qo-P5j,:\-*Id_Wo'YknbH&fJP8/J%9C*tZUjdQ3rPdHr8#)R1qMu.>*a_*\Eqnj-m@!/&)V#YO
%W>-QrFE\PQlfauOGDF;7j2^OnRe+A7Zd8K\`%XsTRA]`Qa`!d.4^C/ICOWW&oY8O[oWNcCjhL`kdC\BuqN'VR3c@OWhVK9%qqGJk
%G8R:s[T;LNDk>6DjmABVM3_iS]#OgMip,[TZ`B->Sj'o82QAkKhkQhDgYq,AjjV,f2aSLF&O2kCneC[maE%k64aPN<oC]ouh)A-E
%4?PSbs'2Gop!KAKLOT-eip`urZL^5*rokMCXr^pim;.1>=4cV:ce%Uar<FFuUm`9YKp'=a$BJ.5@K=b*DIsj1gV.d$?DkF9k,Q3;
%;LI*BmLC=P+QcP=k(is'o(qn$T:^Q1K>5+6r6)b7m_6HkXM(BDL9YU;%j.?j+mg"/lqtjK[4F%(4`XY@ZV03>BB@nHX<)5\0Cu5P
%S)$%"<BAi$?fkGXgGLPNjGU5=e[*0#0a@B3hW:kJ0DdO(/FM]N=4HXgf"h2QdETB"KeMZ3YPuoscK3NmrM&1d]CMH*/A@c5^\3F@
%$IbF>E^'ui[]8S*!+4A)-`?rjc_%@)&;arqjMAfJlR\EAN-lC:kQDcIP#GTU00fHorVhW>jQ#n-asV[Y,JIp$V-S3obGNG:T0.FG
%G(+H.m$BZs@=<3FGBS+L2qe0748]=r:Z&oIT4L+Yorn2EPG`V35.nD:HM6o0qq]6Yh:fhL-i8DjMuUQ(mqblC<u1daWqJZ9aVj8,
%2OFn?L'cq2@0gMlH%,^/aTbEUB(k)-^V'Hn:)UU=DKSj$>ILP("$PF3l(7rQcPWO]IDE+Q:;kI*Egh@J^3ocr%X;de<OLHY&FqpO
%5k0FKckL\bit;@)kR/)c']]l7aSYkMiH>7:*F8'3ILl<;&_n!$'*k:W3+Eumc/d2iqM=e`1SOT1nMu]uXUh`SaO>Gr5[C<)`K+K2
%p(uS>>h>gAb%R4.*InQoi_*h4HOTDRcuXq:UUk&@\UrAhYG/-*#k`iGOLk6Qb-IP>7kY+g%8JVq3fD=Z0/kQFFQ0f8Sp1ENJtc8T
%q]Od@2#(D3,^O6^dO@OK=A$UT$B/;3UOS>"(<D>Fp(d\nG(`)$JBg^Oi1N.1T>Ms8IfbS#NEsB3:PtE*+,LnF5J;UrH[8ri_,Q2b
%s1mNYAFV`)!isZ%:uR^ANP6"NjZOkMqb*OM*H/BaXDILsd;Fk#\t8[Akg$$RpXnFma8PH=qBXf'j:/fqi).Llmk[5>_dJ*ToDAiV
%aU-2MG'1?75ZQ\0R@mJIbn6Mr/p[<7.Hhk82a[CnJXu'C]&W.=HnMU]h=1+*Q*O%FQ@l0/d\$-kSUXt)q)O/*?f^k.[.W-+TRmMk
%#VpE+O)"mr9>"AP#c0U^&>Li4.Y@%drF?hlLcG))r[8`&fD$"nVc66t!"%_/g;J#U=ZRbjBkjdq+&dnMd$U\!j]a=J<+=FJM`/>o
%el_H#qq9XalSTUlXQJs-\uO3+/!VH"W@2H:Uj@#\P[4I=[-i0`>m`k?:F>[s3hH;.pJR+IDC;K=ViRAJAj#dccf-T$n2KheZlf8W
%r\U27LHk2sT]gX"\IB^b3pf('C4LrCk>:Kl"FlP@?^/*Rqp3hLg"dBQ^-Sgkh@HNjp[iG;juH_?OiWBM5Q(O5+IXS&[_JdS4ds:V
%D@Y/`q<U7'W-*6\>;E+iiFk0]MZpg2i"B(bB/;li>OYk'om$(T/c*_j\5(]FmIn::(4>oGJTn[rp_Sn!N;Y?I,CF%fnCa1]h9^dZ
%cEaqPr;3sRdiV`#HV,7rlS)OL4Sct2h@2@"oFL.spn(CNc`]Gq!Q*&B!MSO)^>nd'1e+WG^]3^.NU!*IZrc;p!FS0Fbo2"Hlu;EC
%Dn31r%+b:t>Rud:^op;-_4/Ag)#E3E[QBP!+qM?o^^Sa0Xe$[>.!;[gH*TXc26cIkp]N$tdu[l97@*L8hp17s_]VBn7.E@[)9RV]
%Z>5QDAii+=Tb0k53I@XIG`9K$pP-;lc?Ps0MAl3gUsqK^/(IEIpf7uCb%M2U5()VppI3cO,,Tk<I`kOCF$5YOSqopKr7/*sM2[4$
%T6L(Xq[MuSs(%E7I.l#`IH6.)qr8HqI".o8^,NN^S8NjSg=j.H;X\IH\N:1;]=Ft?gk)[e_tus-H6D^+HHq"!c8fS)j#>8UHf!!W
%AK$W06$gWBq>_N#5BT9uB"6Xa`gcjogHD'EZ9RC^2n]0A!S@DHchWB%%JUc._GW>J'AsnfpnWPUq@^fCD@dn_g^WCdSI.LPHRbIs
%&juMS+29u?6>A7qm:VXU:LZbS)!ON!>Wjj(1LOSEG5@D6?CBSIe@g9[G$+FG:WLD23r)3<!^J,g`dOWP3/;h*(Baq8rq?88:_;IC
%W)[,pHcseSm>ldq8PhW2c0$7@3o5%*hP2D[RP.c1s#(>!"543.&>UE"]#T!gFK%N/Renq$(p#itC<Yfo*UTH^^(,7hqX<F#!WjXQ
%NS1l'A?0u*SsRHq&'8GpPt7@P8u#4!g0ahRIqOoMRpOPcKAi6lf'Dr,O!e=:n]s-Me9SWD\UPEb5I;6W;"!m,s*85Y.MTt%4tjKn
%j\MlN,LS4\\pTfq,-_#T[Y;\4rG8`)E'$U'GPdMKI/.h[j?G;OkjR+jPhgmTSGWe@g>\f]rb:5e55U,:c><>k81aFmf%4H^!m.dC
%b^DM5<0aFfeTgOT8<:U.DhldXH1X0eeBuS7`nl$?D&mo5=[_Y2%[fSo-u^iP)<S*R2(k/iIuI(gp)@hGkuZHLD.<orT&62RG+EL=
%0YLuX/^Sr'd6C64-rsC\V(W#"U?Y);cWP,7oj3O4^IS&4E80L<<+asQYI3eQ#%cE`ql5m+F2%c,a0YZs*'go>ahh\Fr3cldX.mi"
%K[%udkY=XZW$cPuke?,[SV2kagOVRra(^=GT[23ZJo1^ubCom[a%&Z&MFsV<IVrWgeAn(eMr5;tQ]f\4"o2oglI307"I]<hL)$UM
%)u]^CkjiD=^XM^g,0IK5k4Epa&p%DA)&8^T_d/nWUT[CmJD.!1*2OY[E[s5;I^q\akB(YRbIsjHFYDJ!S1sJR3ImA./qQQ*h6Pc/
%$O'iuhNg3ng<++SC-$Ju,)E#>qXfhja9,8g%n(@RQX/VlY32Den@^mkj_+Ep0=h7")3&*9%K`C=#23F,3Wb/g*Ye,9V'eGa(^'(4
%MBu&Z)RpH;-`Z]F/p-^,YtB3A4pdGeFBru9lsdu?GhtJ=E4tJM2#3lG(!#XEj&r=&^$(j/?7)QT`J!4G0Ubgf724']>\,dp5*(%+
%_F[_iTh$1=!0LS<,F(rempNEZq4'PbIG4Mj8>(9Wd`W=4q3fh""`r?npXo=aG'Xo\5$j\li?d&OcSt8)X[1$*.&6`K2qqZBHJc<,
%Qr_M!G>G7b='d<Odj((j.AtL_rk]CNGI,gao7a_JWU:]D=Nh9Bl-48fBU+>/Fut"NJNON`Id6TaRGg:BdE?$MB+.^_fV5JPl[osA
%TXCu5=jEUOEW=t=p#F(M-Yi7Bok#o6D,qlmoClGe.`J/:lMH98bLUdL"RU^DFcH(&l9=Fb8G>[93T";4VbPjtq?PFGp,W&gB5T*(
%&:FKOaj_CrQT;)M/nlS]HXKTuDfuNeHMuW6<QV$-%WLMQNV3V$`j;SRnTUMO2KkU2Abm@"Gq`O:*D#U:cHdlk?`D14P1#V]aFi<G
%h`KnFruIX>j>k&$Z;2nJB*,Y4Fh\O7SRMN7Ubuhu)\"Ocb*IONaH*h&4QA"+l*oC9ce*=+l(?\SNEi&dCtIKmqd.+F.6mB,3_cE\
%+!"&jV,3M+mErgprK;H<Xo'-`r35_W-Rh6L%/&,"=,q\S_ThpR7XTSYhU?mrc^BO/]A_Gl]L^"?]mk%qfn*J`O)Pu53a:J0id"'0
%RS*$&_E4dK^"Ms8$dF[EkZIf>,N%7KV(qi(qY=LkJV`_J;_.OUF?6uS^[X7D$s:Q)kL=3QB`>2q["%4KILbFPh("2OQ/275p8Mgo
%hVlS6n[(u_5&W_Rf'Ngq's2A%fl(2tqL>`om"s!'I<ET`p"0-B6>_aRb:F0=KUf3F2>b^5=WHhKUVUg"Hb9/R4WLL.;<N4'm^!_G
%fPn[tkmTD#<e'S_Q!l3IG;QTuY,8qTR`H`[Qd$G<ofRR7?VA@`UG#M<L@T05QYTdrOkk"",iWoP1fGr@,g!_T=!fQJ'_/XqN_$sn
%h+.0Ag'_R<rZ&1)6"LPR8ZJ#5"P?Qt2@G6DMT94tgu6BbhZ9FD,g8I2,C*,JXXO)*G0-dZDD.n<LT*)J)ik;Z2)V-"V6p>;PVJTj
%59.on87S?"qJ==)R&BpH<]utV'aZE(Xb^oa=ZI4`q3\B4LLWT%q3C/q6W(k*L(IW[Q>aKWFF&a;Kb*I+,@-3_TQ4D%5hOde#6LXo
%Z1b!,JLep1OrgKWW*>d]jGM^%<f)(e%?H$/RbO?bpjamJ@T0_q&'/A&if(U_]$<jj>"ce'7n9ML0i%\"6#V=WB$U022cW^RUph`<
%Au;_Lf$4]!968bNhDq&q3ENJ5TaB?rrlgT-1q^T[MTG+7Fla%orE`p_%*f!l],rj"DNL`c$QjL4EhHt:Fl8ct*=310*;X8%D\%Lp
%jb#J#CD:%]`K`K>pRF5V9*^E8\4bi.8*lN5-Z9D"Yt`sjW"2+@FPk`fj+c,sQr8`a(m.oaq$(20,?BMm$Tmh.'XU'JO]9Mk=1l4s
%,saLTFpJP<DRAp^(^/3II7'IB5FeE85e@@[i`R![k)+B5dHGd8%3#.63^]UNMcR]YTJfUk)"8(!YAGi81W;%S+j-A&!)tdr-D>Q*
%J[,I&#\q<;2NESC*R&D'(W%a'_9W@,<LGq6YrDOpn+EcZ.,V4n!\'rZP,m'J5q\T1H+?O'8-NOtokoFX_iORs2gd`/8QFUtD/CpY
%UPVXi/p.f\qaMH)i^:pf(q4.%L5c+d.O?o,TS[k@B0FDOAJ1-]DH^Ouq*l2o8`XJF*[Krg3MROmIETn$WU!]:@[/EJ1_cVQ4+g%-
%6<R2`1;iqG:Qh6I",6OL-K;3M%!"4H38S`J7[cBhT;;Lq,>_SZq6Vg$9YP)gYq1kI2TsYmS+>[%Gb5C(PgOC3,"KVFs%J9lOY](>
%8\(%YYg*F-h#<EMAIL>@E1<2kb?Y>nUU[%mFbCn))/.bc$l@`-_Xp7r5[OGkgf@,j"%J&hdXU8b.IGXDV,!BN[R-+P^j!*:V"
%S..iGI>uK7]shq_\8iUQ>=2b\S::2)L*uumIP[#/p+mc%:A8C`[N!\pbIm`tT4J0a?+(t(b<(?EO3@prU^95EJ\1'7,C/J]=)t.,
%;7s6./YNY.[\e;[Rbpd_okM()Ll8)g5SJ+"n`H5agUM%j8gc1&mgTDLPi@>7QfWl$)p5Ia\nT?c2$^iNBU-caX[<V#lD;92>$7O]
%Fa3!6HY!Y"F<:A`d"t^*9-KVuW_unkLr5sGAS_0X8LPr!*BQ!p'*/?N*eA$FrL:DH'gMbWX-H3,h#Y]6EYEXs#=2a.W<"@FAo`WX
%4G3+F'j"odS'=Pm1cYVg<0_8Xj\>s<S/pM+Z^Ef(.XX)q0h7QJdY$WRGqi9JMLQ#<Oa>n8'N=I3oVVms*=OY<_BbnW0KJDG_?/mQ
%<b]0[Y2Hqe&&4\]bCBdSa7'W'6maNLWIDGq<C`5T;N_hFXp@6@$2sB]RleGfEc_ol.D[G"(mU'AAE['P;D)/^U%Y0WC,2F_g@A7n
%m)MJbY>d?G5aAEC/lY_e\86j))E3<]U<u;P1c//8@lW?T?k>SU!5cgZLJN:?P:?rj2cYO.JgOC4B^!a>2$$+[WV@&l)$*5=L;JX;
%B1>?E#[2/n4ZjYF%JR8L(!`<WST$$]l9kW)N"KCc,Ll5k##$E4EUFSB1qt;C7RX&Tp_=3C`.p(?5IbY%!ch1iS,%Io3@*i/^8pN3
%+q,!-R5uAhg?dL.mAh?dk_D3qZPMPO:6_eH7a>\sm[-U/#)54=?ncsL+Ej/'TL&2c1$Aem<HhHN0!fhrkplKF/IbuZlSKQ^2`c"H
%&lHVg<nVt5,8UHmYZXnU`&3arGg9re<Mt*eI@\)c1hk1Im\)DpUsLSEO[>VHn9Ypu2ubY%@Gn3qOgEdKCFZ!?n49V*`YQ/VA7S"c
%#:07H%unJ_fHTH$k-->)>QirZ4!.Z1cfTiAU-WFO#[qbhDsW_s&&2%`<@q4CVD>].d;r<?cPlG^"imfh;st6Y(HRuI1N0@;_W&K2
%_W0KT(&iLL!qr_2f0*VG?02Q%QOt-.P1O`"4NTnqSDE&'BQCMP?,J)?,&'eq5U/6-Y7%"`-$(3V3E."25?T6aGn]VH9\f=q%eNS:
%>DP_+kEorU/Z;pA%!(5NJ8Wg^MNX9^buftE+II--S.j7_d71K!P07Wf/2l:;RLkLD;jSn!n:0L31(.cCYEUep`Om8)6:2Y9FfR)3
%GJ7sL.:YC%^U]9^[,nS@o8V(^:WW_GdnO:#p]n44/2UY3mfEqG,Y6IhS%7g)8LiGdTK$fLLmPl72IMJ0\JSlsZsO9^6Tbm9pql(`
%pO-!![h6=mg&,c?r-oF>@l44d+TE/7j2n8<\f^%o$OB7(Q?ZK8),\De7\MICBRI_JU8uE;V8QQMSo\EUMLCl*@[0".s'N=7q\$XB
%2@i,<:!]h=;oqLS.k$:gNt$W8_b'19!-F`hW$;!R)XXfO1qqQUs+>4E:a.J\=UlmfcH\;('K(lRR2;;@4A16IF;@fuqB`1n'K\@6
%9g<.O_*1\`n(s+P-mn<"V*CYm+1*hgm(,'9M.):GAejc=.L\7.0#mJ9-u!la(/e6SqaE9,m;Kgn\S5keC:8:u@X^*!mm`Z0HAih:
%Z*^A0a*2KQgLr(Z*ht"BWpPY,TsQp)S'SMoq6TM\NO-oc\k"_GeM.,>/Qc;9He<X:c>^JkFW.2.7/Y_^"'EC(39unAeUe-K,rqJ_
%OqHKsOW#BO3Wh!upG6JEdN_:O/Z@$mh@!-R8'G[P<^:)DKR(l1fB)5r)7AT1DQuDrI8W^%$3N.KB+.,$h/X>Gnb'.[S#)Bp)&^eW
%_B>2%QAQ7[I,A#/3]WF2.s7q<g5XQb8CDD5-C.\uDkJ4![p4pCC@PVSicN3+DlJro-P+<0Dg.DLAWnoLlNHHba@uL^L+$L`^08_N
%/'F5:3%nL#5!,c,;B!Xt)FpsT=>ppDg\oQ`FfXLtAt3llR@U+"0k40$qSn\@`#q^56"W0"Je&>_8i5.fP)3e(\K2@u4XQd;F1]p3
%Ob_6/DVNFGH:rF2QHq*n_-</uEMnF2?e-e&[M*LJlDDHb<P`(OqPAEMN7UFnl7N3;`DL9qj1VHRk"!kXkVZaKWJeOC&9)!s,$[HS
%nP1/P'!DW=Hh!a^;Dj28h;UunhbbCt/m"UgGaI;/d+)AcR^scC<J/E([ZLcr)Xm<V\H_5U%ncO<WeU/=8)Hflrea-9=M1h."]08^
%.9c+"&od@iY1OW=ejrf_lq8u237XWhHjt!]Xst*?/@6aI>l^WP0RUYOft!q(6m0YW!\).d<N?4ik<8WI#Zbt":buHUP4+O<P0I%,
%W7s8bSjOQ*%54^s;^KgG>1t)iRCgM.3@^Wk&=n(^n\*3a\g7lDN&N$50s+ug[H61N'n74QX`RNO.AL$i@hU7gB6!)elReDGaOQ=;
%-8q8cqD*+7MST1XrP@Po:_f"U!p^pn\E$0S#"0sN8^XB$n=iIL%\[p$",=fUQuh$oANAZ"8V8j4F0A7#EJefk6cKdqpUq[')[/5b
%MMTMBIfiH/0?OH*4<*f\\%9XX;i@$d*q?^S72X:_\X.qZ"'$eY,6=D2oI!))HB+$]PD9pm=gT"6\gL-QFL%K_]2pGU/E)^D&QnQk
%DWbrr-V8]QInRdf@s.)--qGS!@U=S&8-[U3ZXM3biE[=^9Cr>+1Qd3WfIG"[U5o/RFKI4@bI@Nl:7q25Vj6%49LFW@LUpP$Ya]r`
%&IF-!$Fhn,)#kHF5BQPG][6_f1n$gc"5-5fdla*Bm#01/&?O4a8t>b8@T73P5F5/GbE*K[]IF:eGlm;1E\:uDdJV6+TJ+;\,lN+4
%<=!T*k'EK@eTA4oms:WZUa<B=Ea/S[c(],r&lm'pWFt+#Q]`tIfG)dF&GfQRT2s34Lr`YB0Y1`!odGasm6kT1aDQ];hBjSoE<i6P
%5qpKLan04$TYY?i]ZL`A/%P!HRZO?Yg]^sO!LGW[)Ri<s1L?l/9pWND)nKRLai2S&;m%+@7kK8O94WE(#Z9"2I(2.o<0SoVLfZ3"
%'/>gIHo_:&U20)!pNl_q68n,GNYRPshO5%)4BNhZi1^Q*&/KPT_/#A!?bJ3uA\0A\)<&_`r*W)0BA%t5Xs=fuWJ"O5"?SB*p@n+K
%[d+o1jTBf?'Fi\hjoIl/UkL`Fqb/1\[jHr6f[!o`nf/QVo=T`_BFOC!3=qrN:8I^2qXt1(!a2RbUk4GGm=D*:AYEST-t>7IZ,O3d
%6n3/r0B5C];QVd%AlVZ2ZZkFmR_2@tYam$\j*h!O6*;&7)0qcnHmJiN!I@Z=aD,Wfp5-%]Bg5&#Ij*]"W=MT*NH!]DGB9^YWao=6
%-=PYn-p>3gY<\Em-7qX5St4FTj<u&)CKL2!e_t!8hQ<U&D^L]_/<s)*D3.C#D]PA-e;+/R$5F`9:9S],Y.e.,Ng0m>LeND=2lZfT
%3aNa-Y(ZaWb\4X%K5[DqCCnP9[@ECU).h2i=g%lTibT.gogVBV1b)8(#chCEk'@Si\A8Yk#.02[-\[@K4qZ,m`Fi&!4fOD&o^Nlg
%T*('+4*nhD1F\`D?b4&;m,eEXqUGXDAdW##OO$`N@k!%>o]lSNgS'AE!*^P6c2Db3!opQ_nkS=GY#`dn^$Tm!YLmX'1l3jU:(p7U
%Xf+W)\8o"m#EW&qb+^C,6gO+PQ\L0VP@PXk]=)Y;nC@`iE^H"7jt<SS`jH>0>Kr^>;0_oI9puaG/Om)#LtBptm%'0jh8ehJdGbSW
%"`#XVk(Sfkcq,i'43X0ED9CA2WuS/&9i]BA=(VD_QA_\+CJpTn6FLDKJQ0j7jeeM:=K/&>3'as)9ZqE/Rk>($DS27XCTf&:<.#Xt
%?XQts-lJL+JY.oP,@]t*/pV]q&Rk*t3TgY<!t\GFf*]BpeZ8\XY80D!'=N#O/[Dk#).:-d>2YR<>J31k9]hQr'L.7ckI=!a/#Z-$
%h@OWHRkm8#jbR8+R?e24#J1@(:9I7]>'hhC6kY=^"pJq5!(pELTebC+P$:l>I46K=L;Q=-8,""BkgE1*PA_O]CLpa3TTc!$,u#->
%[G=%o\uk!,m5)$,_MREn)F9tpU8/.730clU66:I21->d1AF9Z+Wb.IHrQ-?td>`o_cSiI/`5Z4T\%C^9gq$bLg.1)[]34S`[C_NU
%e:d+@IF9;pcb[h,NJh\Q2&jKuD?GL1/\.4i_!>%1P6gb1C(4,Wa36VqQm!D;Aqu,JbiI$Ui4,0%GHAicVn1#aiCFsJe`]7NA5bW*
%'>5W&cr^$laUfQ,O$<NU`b)Auj)]0sdUj85^J/L*m^V-r!2Gtl?RpNLeO?4$9/OC(4CLJL2t6oekG!76I3u&E-0K^8>P7npY^Otj
%h#/j#U25I%?`UuDm@&RWgcP;7CbH3O26t&5&>60<Sd09=rI*QIGeK.'7M4q/*15;lKpK,b`aDEDBk.LMi\K)H2tGUuQe0n*'L,hA
%h>FI>D&u\4XRX-SJ;PeB]]l^>i<o75#S&pR`*(?aOS^1i)C;9bmngSJOS^0HT"F'+Qmb9(fV4Ks1m,,d&7\U.TJh%mm8"NrA`!/^
%lbdO.$!:&/h4to&:@fDBEe:#64()a5F`ZYm`n&DS.TEj'\@7?Z3mN!,.=hL\?!p=UI=)TXYhWTlCe[eZrb7NBN?&Rr@Sno-<jrA9
%M5N#9X7*i92Z8sDIeROUU>R7EY$X]4h'n.d:VGHYM0;bQHs:RE>G&ZMp5;_dh<j?pXV]1Z_)'^`cYr0??IMZQ(GFtpoW'pX-1^VP
%rgQLDhs&[7ERL14jTP;LEhnj`?_Z8Q]l$&[W#tO!V[af_+T._BPQX:jO&@\Gm1A`[&>OIX.ES4GR<QJ\AXk^A)#l*Pll%-X@8l9D
%Y7N]bZUDP'7kD:pV[6!O\,Gp]%mej[$:-YQ#(j'jE)OpAie9U`s3N_%l,^#lVVo=u6U.WM?F,:mEs_g_`+1\[8u`S.V?h^1e6?:'
%\?K0,cZjYqcCgeYr%lKOr\41O:k>Yh"!@A"^cuD8`,+DLAjCtRA?e*9_j*B@I\NiGdcus#nABgM%-=:1n]JY4lP4Q'K?L6?d8*Fc
%#B?Vf%i9*jhbD$Y].`f_L46_oh9_Jt?1d'9`'rp)ItrNRZ]TbrSm,J#?kI,Ws*ucU:;KSbL."9earVlGFtcds]-paqp>DO;%N>+Z
%CW6/th^@-G"Mt,#Bk+;SI\J,,9>DtqM(,R&R67U=5(-tN:6kkW!B](*GWmpQn5]5[#9>2I;=ATAEh6h4(HPIQcQ=]O,f88;';8q+
%Tf23(RWcIeUB0o=ftiZH41&X.Wn(u*g)%(/Yec)_<9V,gFSS8-J<_HbWe<g<q<FZ:dJ&^->TSVOdC@fL@Z"una]I5rKGMD6Ab5%T
%7[nca]'p/7Og"L^<2WT[@F7GS4L0tN6d+HsQCc21U7-1TkHb8tIl?BjT9Z0J5sTD/F,<ti0c^LKrcf6I/E%!XG(R`1FiL?HEgdCI
%WXfM`OO"X#+6,a'=8febZ',o'T2b34Sif!f;up#&f29i-)^,[eRQI9^WUu#L)BAB99G:]Pk5LWQQ!27*%&FBjc>?sW!WO7$e)R5t
%mG)CTCgRQogeC7pk4>9N_.jV]K9c6C@C/sN_WW#A#fA>R-O?QbK(A>=";/p!&>]S*@!5&`5hKH$^'KKiEaWH[V$eE7p"EU5(T]G4
%Oo3*Uge0#JSeD^lE[)kX/;1jfne"?Xnk,9_B:gjc@)\F>!Bi(\X?JC2fYYg-_B,ms#Y3J7+e0iGP]51-Fi)sncO!D)D'uu[=cab-
%`=bK(Kb;-<%jllgep)eQ">U,K1:@L?lKed2P%P%L_57KFNG'")_#T=K#[/,J&IN(V7(=;.&<Sj3,bdJX6Gu'I*?oCSR>=9$.mg&U
%gEr%8ClLj6I?]RnceK[M_bN9j[.%e(e<af9-(S\*)YmDYlGpGdGh]af`()2269M"<<YL:4C"XcC<R+&*lfAI.I+t*GnM$1PVOQFn
%=BNWYZ1>SX&Y"A"8ULsc?c9cLV[Pu)G;L/GdCd:.2-LEsl!)EjN.jiUHZ/<jaF)<P@&Q1[oF0O1QSF<"<h$qo>;m!7CG0(Om,#Jc
%Z"^aX8@5_)+l,aD%Cp)N?mlHTXJ*9XXUV[*]kVd>@A'L11`/7gU1<Dj!;5!:V@BjM%'VL)>D?ul9HuA!CQ17k.Q`jA_M4sXqlJ[/
%\grl';p;jej=H8=.'@lCA"`-0)^FJ?LJ`?A'TB!uR5!bAMkI?-/@!4[4Qn];)sp[99A1;C]jbnm*).n5j.+E?!KD))N_PL[M``^3
%$K883lAA;0c?=5C,5p3*M'HacPLSrHR6oRY@>ZLueWaNR[jgc^E2KpV+#Gi;^8.amXVX\pTCLAec=a&P7<a5<D%B4!RID:q\]8N3
%Hd.tL7k;O%YJ%?35KHT@i7/Gq8',biS=GnBk'T(#2cg,X`;FL4]U@Nt`Ktj0TCM&hVctura+KY2)"q'iFYPFM4I4OK_?pL^`E/j!
%/WlH;"#Q;h?dDpHDi;$"C2jD/+/NN*dN`D+AMW7//Sh3AYoMTnL=NSJ\?51]pW^k3>99i[jDS4sfaV^k(FhdfYo*mQ)`-7"g-/:'
%ehR3m['"H6NqKW4TIi2_CFDa+4CdX#`_3tj(7^9f><LkoZfT"ng8aIr`R\l!#.@rPS$O]A,]>b4ZNe$!3Z?Ya0k\ldjDlDj\@qXh
%P@)Jh?M#e'p:!_s@IJ)iXF1ka+uK"sV8t[@b*UT9Z$[H1HqX9UKN%6SO^_!/8'/($3Sl]i[\LV(2'%TXldKk+F?Z7=qkW>L`D+D;
%:Ds"(V+Zi>Y:aDNCujJscCSH_G0]]Ra*BA'd;rodX^d=$kX>9t'l\(T?1E>Pjsgf57b<!faLDF[-LP(1(Cdi^?!Xtrk)e%DptW/Z
%If4#kZchmY?0@Z8]A%/_h$[#(l97s,8<5S'RJ63:rmDIJD@md_[Vfl*lFDGee'$<?Sn2NIoGTJY_!<V>qd+8UjLha-WpMD5%ZL.W
%Q>(\3*BERiZ/$dE2E=#X2<08sF1h^E3>@H`IA/R>IL2aW2'Ad?BcB[].I,IOV1KHZPY1\k4B:,A1tAL5)etonenOa-9W%'IeObNS
%nKj`c[GU4-LF5Y(7hpUSar.=??)anCT!`d1(tYlE73N"`]OZmRo@u`E%?1FG2MQPh0ddnK#So>imPM.d%b#tL]pn`^Sh]UmkJD5s
%UkW:8P-LCBX*G(rDW%+Jgm?2-n.%r&mJcCJpdEV9iN)'4L[Qm59E%S1[g8!@b6sJ]Qp<0,ZCQCWl61Hs%!NYGYTKUY-=:GBJQ-A7
%An+\=r!<+*`E>)SQ]-V(/^^>0;2SQB>H&=/#,L\."I:\F>X$3p^#\c2$j=)DE74JERVmP[C/@225>ua9n[#K2_QPB=]t^SSErKJl
%A+^piA0X@nN*IJ<S'M/Tko_#1M+r?TC])Qh]#4!A@)t(0#@Di,6*O9HAV8_Ga+R^>ln/uOqQ8+[er^Gb54V5J6JL2E&pClsMf@m&
%f*W>&\RN(8E%0c%[O4MVKqpXO$]Hr@Z'gHiE4+?@7(]jgRGR+#NXt"pEb!N7bB%H;$G$"B*gK@<<A_<6GL:RjBoHqR"&tACH!VLb
%9Vum)=QOMC`5_ad>qs#5LPeoQh(+nFa.p![;d%dso9Hi;&t6e7U;J%U2$l9,F0+43f#E*0n9_'2icVW,-2N>55"YP9_'J1ZgW?i$
%'qn4-b_(<6bA#k181>KN.E1*eoeqeb;Cfe.P'0j3TsSXFcdT;J5qG0cP.Q\O8AAb',GR%nWD6ZcBmg]<g'XJLD"+3*c^%'a$(U2F
%OL'eUpKXpiUH2)O@[.tEC&N+E[o5[X+.>W.q.ab9kN>],n`gf8doVruJ%dg#j6Q"\cr:@DI)95eg*Bu!1`TeTYP>t<Uo-@c0cb-Q
%Mk*64:a0*'@nB@1%&JPd1BX)ofZDmk2K+^/LRG\LY)D.UH1b@a<4jGKXci=Bqiu?@^9&$&VR%aQ_guRMB.Hq/LN1)k(t=PQk\&IJ
%qkunT[9_=tW,q>K<R4H.q>/_VT/Z,a$JI\oO=0'lj$PXB)S9u;rl1j>o='>#k'rjOnq2.!f-J!ZX]kb<ogt&`6uP!_n4du_O/gC4
%q,kW'\PHtQGC4"j8c*S]mY@8)i.,L\bd3@BZbHsWLmA9_2/)emIJm3K<6;O+eYUW)Hb?Uh+?mg4RGb#d4LJA*PI==TBCIg%G3s,B
%fT!#47CW5@)"^MdI^9iNe/Q)^8?pT)VMh:b^K`>`lbe%R^N9%j"7c&7?Dn]?p=/85P>1e+]PB8EQ3QDf\>k&47r3q@7`F:UFL7#&
%b@;ml2&I,JDo_BW*+:0&c(l_@MPW+3lo2kI.BWQtU3BV0/f2.UN,@;IZVo'B3Es8\:gc?IMhCZ7dWDYSgIXH9O=\T6U9;SS@R&cD
%Q@X9P>eV;ZdE:se$EYM'b%fJ(':H8Y-<\r?/(Il8]OZ!A&j1qD-H.f"ds`^LV9+=/>HDqXgPqE<!1GRG)reRMl(2<R0V6ZEib?qe
%*J]a3gWa6m\jCB1Q)mJe!]`u=:Z":1IRoacjmmLK_Tf]MEr(K!eSim2O+nZ5kC``YQ%Ah,!ntgNVkm.94Zo?'!G%u#"Ia:l!^gpt
%k\)_Uj3JV@oe5T!rC=^hoN.,\-sD"l3=^LWW&<3,lX.*?XuAF946W?4g_qR80TAS8@\`PaHnQlBs)8;"7qF7)JPNVr;%4&^Wl&ql
%e7X,MJC*u2FjE*R!?tQdrq[H,,,7A@J,_KH*`!XO&+eE!)h/jf-.PBO)?4I2\3idRn8QtQZlRNt,a>*[V)aYA!^)uDnKPR&3JQZu
%eA;M3^;]Rc:CD,-oC*j9+;p$5!Hh4J?]m]]I#/%f?ojb/gB%he:(sl1(]DLleK!<BI\FiLr6&`2Hl;ne2p8uONqWe\nA*9S["fnU
%mU`6'P+j4r<pk\$f^ga.ISo98_Fk;ffun4Ne<iGuigCU1:CnE="bUb,O?ZQgd&]X1<lQAY!YoHZ3>IX,!GDs%nWHG:-JHAAP1Guk
%YS?_[<mh)g+I"uF0rTpD;%r4FGZ]OY;%rC13KQ&b.fg(nU('sBd+[8WKS-*`]Kh''E0>uG(3+%T?$R'FE!=<]*gR&2E0EhEN$t:k
%E%I!YM:fVm.U$0JL]X"sbBSOJJ5nj$UaVKjMW^[end$d@WH3Tp(sH0F$,r"#g<*"rrK:)`3JkRS,YU?I0E=VjC5Y=`4Ds/(M,X5g
%MQBF-!8O4NbYueS#tUZ7"Offghuo]Z<;R"(*Y+Q';1N)JU6S-,;-S>@o7EDrA^GD3+-IFS4>htl,ddqF5=T2bGLd*>ZhX/#M;V,4
%@h0:fVEY"F?OgU0*e=uL&_R6(Q5%60$^qkl*\QA(fFhGN&qdF)Kd^mSK.eVa`euDa";^'NE;>OnA:l(OYoX7ZN<Z-[bTa8u!l`#'
%6$X;NicV@;@P!50#YcQP'#>JUMRY;P-jWMq3tcsK%(C7$iZ-%''.$[r@_`B0-;%f*-t"C&p*J8B,?+6]2!?d8n/dT[?$WXc7RA\/
%B3Ec2=I)mR%]q:=246fM,H?:P@;lmYYmC2WRNsRM<D`mUP>5d[!Qc628`FFj)`GbV>8`eN,9eW?*@#i0YsB@uOn%A/8DFsT_iZd!
%)%u5m#(^#^RA=WHXA!$,fq&5\UgQ/JD$">VPMe_i.El<l5gE2):XB+`G8398#\$5;/./DBd@`3<;K`P<Nog@1#$`c=1CiQG=u,SL
%;N2*P/?seAU;03:AqGb3FXe)]!&Y'&en!\P:MMXPXWj`aO(U-ucm5._OkN0\8;XG!JeSQ7'u;\^We#b.n=\JLBEM!"Pib?Ln6QY?
%=qe-^Z3&6RlcP<V@UrhJgk+Tq(dnW"<A]j_RMLOr#;4GVUhU6G$V@CY'$hdl+U<i=J4E.m:B`h51lo[X$Z;8A:0rLH<G_lq;3^>\
%(`rQHeAP8eDPB]oD.,t)Flb`!MP,,]:)Y\d+Dj\J.07kZhaVLL'3.O&mM!C?aH38qGR8p=5nDle-N_o)KaPm-U02G+(GY6+M6KC(
%(b`B_P%$:9KB3?;,:G6uS`%Jun(^/:4!<fW6"?GXQdmp8V2ic?*2RF>_gk3l/t<`&FTmIF6aJ`b/bbV^Ydea)86N_<#.Z9b)'f6b
%`Sa?j<E)5h6u#&=<Z3e^<=p"r%(dM0`?IfJK%Q07OZ6\=rNJ+l>sSokL0"71`s\=\+pC[n!fp@q^os.=/6!lhC-EQ,?%F")o(iEc
%!D;"$TdEqU<9m&-i[,'u;#A*J`i6'A6a8*`?LJ%;&5eg?,XZ<P_4.H^Nom9p&"p(m;PXeQ$]0MR+TQ6Y:/UA'\c@fa7QSp%XUluO
%l=)<D%1G:')g`3\e6j[:Yf9ZBJgnJt(.;>3lR84M//+=g$"N(!Q1(BJ-r:e[==F::<EMAIL>"OFnt9f.c/EG/f0m-9-80Uem'
%i.+C8`6_+4,[Lb=m=<#d.m=:se0UD]h2+[T7$6S]^!Wi<8i1\aZ07!6[8l-EeJX!#>c[7_I]M2kW;ZCm)@@r;IN8@[0E/q&IsGi-
%orhV"Mn(#(^_+'1rCs'?N';;eKm[<cipt24;Pb3-h=BE2KKb<RNe?422S7A[I_,=o[@g:X%ZoK\&qBlGIfQ^VKST~>
%AI9_PrivateDataEnd
