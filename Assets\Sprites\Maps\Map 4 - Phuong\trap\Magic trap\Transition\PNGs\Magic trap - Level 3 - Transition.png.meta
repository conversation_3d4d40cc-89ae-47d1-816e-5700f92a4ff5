fileFormatVersion: 2
guid: 73901d8e96d1bf54ea57e4058fd14a74
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8067852083207462320
    second: Magic trap - Level 3 - Transition_0
  - first:
      213: -42250242830766871
    second: Magic trap - Level 3 - Transition_1
  - first:
      213: 8684421163432032611
    second: Magic trap - Level 3 - Transition_2
  - first:
      213: 6191777273678738131
    second: Magic trap - Level 3 - Transition_3
  - first:
      213: -3006693014068010905
    second: Magic trap - Level 3 - Transition_4
  - first:
      213: 238427036489761192
    second: Magic trap - Level 3 - Transition_5
  - first:
      213: -4154056798289962193
    second: Magic trap - Level 3 - Transition_6
  - first:
      213: -3263690852968630115
    second: Magic trap - Level 3 - Transition_7
  - first:
      213: 5146981457692976200
    second: Magic trap - Level 3 - Transition_8
  - first:
      213: 1606774903127579281
    second: Magic trap - Level 3 - Transition_9
  - first:
      213: -3787051616378959569
    second: Magic trap - Level 3 - Transition_10
  - first:
      213: 8555148802478523405
    second: Magic trap - Level 3 - Transition_11
  - first:
      213: -3055517437336076048
    second: Magic trap - Level 3 - Transition_12
  - first:
      213: -2820048926900316837
    second: Magic trap - Level 3 - Transition_13
  - first:
      213: 1944682793374078349
    second: Magic trap - Level 3 - Transition_14
  - first:
      213: 5906752738558245384
    second: Magic trap - Level 3 - Transition_15
  - first:
      213: 5535844115376953354
    second: Magic trap - Level 3 - Transition_16
  - first:
      213: 204852787630125858
    second: Magic trap - Level 3 - Transition_17
  - first:
      213: -6088622325289463889
    second: Magic trap - Level 3 - Transition_18
  - first:
      213: 8156879220863598354
    second: Magic trap - Level 3 - Transition_19
  - first:
      213: 1657104447131516310
    second: Magic trap - Level 3 - Transition_20
  - first:
      213: 397285741487635731
    second: Magic trap - Level 3 - Transition_21
  - first:
      213: -127830081770913488
    second: Magic trap - Level 3 - Transition_22
  - first:
      213: -1897561537019545955
    second: Magic trap - Level 3 - Transition_23
  - first:
      213: -308496617139519821
    second: Magic trap - Level 3 - Transition_24
  - first:
      213: 4811800457326273517
    second: Magic trap - Level 3 - Transition_25
  - first:
      213: 4638708919402392595
    second: Magic trap - Level 3 - Transition_26
  - first:
      213: 5880991863791203499
    second: Magic trap - Level 3 - Transition_27
  - first:
      213: -6517815915920624525
    second: Magic trap - Level 3 - Transition_28
  - first:
      213: 2650854678557707196
    second: Magic trap - Level 3 - Transition_29
  - first:
      213: 7485402792518524191
    second: Magic trap - Level 3 - Transition_30
  - first:
      213: -7596277698534339695
    second: Magic trap - Level 3 - Transition_31
  - first:
      213: 3855319895826921255
    second: Magic trap - Level 3 - Transition_32
  - first:
      213: -7512523163993683296
    second: Magic trap - Level 3 - Transition_33
  - first:
      213: 1222340828990863294
    second: Magic trap - Level 3 - Transition_34
  - first:
      213: -5088891799063561050
    second: Magic trap - Level 3 - Transition_35
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_0
      rect:
        serializedVersion: 2
        x: 802
        y: 0
        width: 30
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0522600964b390090800000000000000
      internalID: -8067852083207462320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_1
      rect:
        serializedVersion: 2
        x: 905
        y: 20
        width: 14
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ece564c0a5e96ff0800000000000000
      internalID: -42250242830766871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_2
      rect:
        serializedVersion: 2
        x: 1004
        y: 23
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 369304e3903458870800000000000000
      internalID: 8684421163432032611
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_3
      rect:
        serializedVersion: 2
        x: 1090
        y: 0
        width: 31
        height: 36
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d235fdff9c9de550800000000000000
      internalID: 6191777273678738131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_4
      rect:
        serializedVersion: 2
        x: 1186
        y: 4
        width: 31
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 760e3d342a41646d0800000000000000
      internalID: -3006693014068010905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_5
      rect:
        serializedVersion: 2
        x: 322
        y: 0
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a142de50201f4300800000000000000
      internalID: 238427036489761192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_6
      rect:
        serializedVersion: 2
        x: 418
        y: 0
        width: 30
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f2b5121e953d956c0800000000000000
      internalID: -4154056798289962193
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_7
      rect:
        serializedVersion: 2
        x: 514
        y: 3
        width: 30
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d98fc14067a05b2d0800000000000000
      internalID: -3263690852968630115
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_8
      rect:
        serializedVersion: 2
        x: 610
        y: 4
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 844ef4bad50cd6740800000000000000
      internalID: 5146981457692976200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_9
      rect:
        serializedVersion: 2
        x: 706
        y: 0
        width: 30
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19a9ee6e2496c4610800000000000000
      internalID: 1606774903127579281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_10
      rect:
        serializedVersion: 2
        x: 898
        y: 0
        width: 30
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f29c9ab9e90b17bc0800000000000000
      internalID: -3787051616378959569
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_11
      rect:
        serializedVersion: 2
        x: 994
        y: 3
        width: 30
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d04475c328ef9b670800000000000000
      internalID: 8555148802478523405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_12
      rect:
        serializedVersion: 2
        x: 1290
        y: 14
        width: 22
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f0429e541f9895d0800000000000000
      internalID: -3055517437336076048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_13
      rect:
        serializedVersion: 2
        x: 1475
        y: 0
        width: 19
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5592e6317c2dd8d0800000000000000
      internalID: -2820048926900316837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_14
      rect:
        serializedVersion: 2
        x: 1574
        y: 0
        width: 13
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8550eb72b6ecfa10800000000000000
      internalID: 1944682793374078349
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_15
      rect:
        serializedVersion: 2
        x: 226
        y: 0
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 80a74d5025009f150800000000000000
      internalID: 5906752738558245384
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_16
      rect:
        serializedVersion: 2
        x: 1201
        y: 0
        width: 15
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a08dafc84e443dc40800000000000000
      internalID: 5535844115376953354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_17
      rect:
        serializedVersion: 2
        x: 1290
        y: 0
        width: 23
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22331f56688c7d200800000000000000
      internalID: 204852787630125858
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_18
      rect:
        serializedVersion: 2
        x: 1379
        y: 0
        width: 19
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fafcb99014ed08ba0800000000000000
      internalID: -6088622325289463889
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_19
      rect:
        serializedVersion: 2
        x: 1396
        y: 0
        width: 13
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21ff6533f6e033170800000000000000
      internalID: 8156879220863598354
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_20
      rect:
        serializedVersion: 2
        x: 1471
        y: 0
        width: 6
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69d7ef607b73ff610800000000000000
      internalID: 1657104447131516310
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_21
      rect:
        serializedVersion: 2
        x: 1492
        y: 0
        width: 13
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31db2b7e641738500800000000000000
      internalID: 397285741487635731
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_22
      rect:
        serializedVersion: 2
        x: 1567
        y: 0
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0318f68c73bd93ef0800000000000000
      internalID: -127830081770913488
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_23
      rect:
        serializedVersion: 2
        x: 1585
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d967186f5d18aa5e0800000000000000
      internalID: -1897561537019545955
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_24
      rect:
        serializedVersion: 2
        x: 1663
        y: 1
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3be3df016fff7bbf0800000000000000
      internalID: -308496617139519821
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_25
      rect:
        serializedVersion: 2
        x: 1667
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de3aefc9503f6c240800000000000000
      internalID: 4811800457326273517
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_26
      rect:
        serializedVersion: 2
        x: 1681
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31c6a6d6f21006040800000000000000
      internalID: 4638708919402392595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_27
      rect:
        serializedVersion: 2
        x: 1759
        y: 5
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba4bc38f0fa7d9150800000000000000
      internalID: 5880991863791203499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_28
      rect:
        serializedVersion: 2
        x: 1760
        y: 0
        width: 19
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 374f92415f01c85a0800000000000000
      internalID: -6517815915920624525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_29
      rect:
        serializedVersion: 2
        x: 1777
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbb4d9a094ab9c420800000000000000
      internalID: 2650854678557707196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_30
      rect:
        serializedVersion: 2
        x: 1855
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f1d98c03e2e71e760800000000000000
      internalID: 7485402792518524191
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_31
      rect:
        serializedVersion: 2
        x: 1859
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19b83a31ea9949690800000000000000
      internalID: -7596277698534339695
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_32
      rect:
        serializedVersion: 2
        x: 1873
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7277246d409d08530800000000000000
      internalID: 3855319895826921255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_33
      rect:
        serializedVersion: 2
        x: 1951
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0aa43434cf72eb790800000000000000
      internalID: -7512523163993683296
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_34
      rect:
        serializedVersion: 2
        x: 1955
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb7f47af180a6f010800000000000000
      internalID: 1222340828990863294
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3 - Transition_35
      rect:
        serializedVersion: 2
        x: 1969
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6acfa60fedf9069b0800000000000000
      internalID: -5088891799063561050
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Magic trap - Level 3 - Transition_0: -8067852083207462320
      Magic trap - Level 3 - Transition_1: -42250242830766871
      Magic trap - Level 3 - Transition_10: -3787051616378959569
      Magic trap - Level 3 - Transition_11: 8555148802478523405
      Magic trap - Level 3 - Transition_12: -3055517437336076048
      Magic trap - Level 3 - Transition_13: -2820048926900316837
      Magic trap - Level 3 - Transition_14: 1944682793374078349
      Magic trap - Level 3 - Transition_15: 5906752738558245384
      Magic trap - Level 3 - Transition_16: 5535844115376953354
      Magic trap - Level 3 - Transition_17: 204852787630125858
      Magic trap - Level 3 - Transition_18: -6088622325289463889
      Magic trap - Level 3 - Transition_19: 8156879220863598354
      Magic trap - Level 3 - Transition_2: 8684421163432032611
      Magic trap - Level 3 - Transition_20: 1657104447131516310
      Magic trap - Level 3 - Transition_21: 397285741487635731
      Magic trap - Level 3 - Transition_22: -127830081770913488
      Magic trap - Level 3 - Transition_23: -1897561537019545955
      Magic trap - Level 3 - Transition_24: -308496617139519821
      Magic trap - Level 3 - Transition_25: 4811800457326273517
      Magic trap - Level 3 - Transition_26: 4638708919402392595
      Magic trap - Level 3 - Transition_27: 5880991863791203499
      Magic trap - Level 3 - Transition_28: -6517815915920624525
      Magic trap - Level 3 - Transition_29: 2650854678557707196
      Magic trap - Level 3 - Transition_3: 6191777273678738131
      Magic trap - Level 3 - Transition_30: 7485402792518524191
      Magic trap - Level 3 - Transition_31: -7596277698534339695
      Magic trap - Level 3 - Transition_32: 3855319895826921255
      Magic trap - Level 3 - Transition_33: -7512523163993683296
      Magic trap - Level 3 - Transition_34: 1222340828990863294
      Magic trap - Level 3 - Transition_35: -5088891799063561050
      Magic trap - Level 3 - Transition_4: -3006693014068010905
      Magic trap - Level 3 - Transition_5: 238427036489761192
      Magic trap - Level 3 - Transition_6: -4154056798289962193
      Magic trap - Level 3 - Transition_7: -3263690852968630115
      Magic trap - Level 3 - Transition_8: 5146981457692976200
      Magic trap - Level 3 - Transition_9: 1606774903127579281
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
