fileFormatVersion: 2
guid: c1372f6fb230b6f4a9995fc8de9ab4c5
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 78
      width: 96
      height: 64
    spriteID: 3b4e831d7fc795546be870b20a903737
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 78}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 78
      width: 96
      height: 64
    spriteID: acba2b86268dbe843b6459175769620d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 78}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 4
      width: 96
      height: 65
    spriteID: f69a96c8f6b8b4741ac39e73b60b910d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 4}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 4
      width: 96
      height: 65
    spriteID: 6e10c5f985f66af4990ad45b175b1e7e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 4}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 4
      width: 96
      height: 65
    spriteID: 378c626822217d9449571c877bb34f59
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 151
      width: 96
      height: 64
    spriteID: d17231607a7c1204082a59496a35b2d2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 151}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 151
      width: 96
      height: 64
    spriteID: 8e780b486ddb4bf4d936ec92c6ef7751
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 151}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: 0.030303031}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 96
      height: 66
    spriteID: 0d866a7647245694b971bdf94c3448c1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 78
      width: 96
      height: 65
    spriteID: 1d7f2a7b440a63e418ae39bd8ae0c15d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 78}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 78
      width: 96
      height: 65
    spriteID: 3c0d28644fd130040aa8c587bdc36a34
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 78}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 151
      width: 96
      height: 64
    spriteID: 87cfb3caf28b5b94caa0dcfdcc1be63a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 151}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 151
      width: 96
      height: 64
    spriteID: 65854d9865afbde499aa7d2dd1623f3e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 151}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 223
      width: 96
      height: 64
    spriteID: 17750184f00a38942a6f072fb27ab96d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 223}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 223
      width: 96
      height: 64
    spriteID: 4dc5af66fd69a05499c1813a567ff41e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 223}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 223
      width: 96
      height: 64
    spriteID: 5070415c4464fe44eb11e0faa1684c35
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 223}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 223
      width: 96
      height: 64
    spriteID: 345e76467ee74c44cb8801779186e23a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 223}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 295
      width: 96
      height: 64
    spriteID: 5eef730d6031fb544b8638391db219be
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 295}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 295
      width: 96
      height: 64
    spriteID: 1ea5b40ef5a360449ab9a76480cff639
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 295}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 295
      width: 96
      height: 64
    spriteID: ced43f81661a4e144912d65304c9c82a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 295}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 295
      width: 96
      height: 64
    spriteID: 0e37d6c305af3094b9c25c48c6879245
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 295}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 367
      width: 96
      height: 64
    spriteID: 95bea9decd004bf439f1c09f0ee84d65
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 367}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2263166078
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Magic trap - Level 3 - Transition
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 3b4e831d7fc795546be870b20a903737
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: acba2b86268dbe843b6459175769620d
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: f69a96c8f6b8b4741ac39e73b60b910d
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 6e10c5f985f66af4990ad45b175b1e7e
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 378c626822217d9449571c877bb34f59
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: d17231607a7c1204082a59496a35b2d2
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 8e780b486ddb4bf4d936ec92c6ef7751
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -2
        width: 96
        height: 66
      spriteId: 0d866a7647245694b971bdf94c3448c1
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 1d7f2a7b440a63e418ae39bd8ae0c15d
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 3c0d28644fd130040aa8c587bdc36a34
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 87cfb3caf28b5b94caa0dcfdcc1be63a
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 65854d9865afbde499aa7d2dd1623f3e
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 17750184f00a38942a6f072fb27ab96d
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 4dc5af66fd69a05499c1813a567ff41e
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5070415c4464fe44eb11e0faa1684c35
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 345e76467ee74c44cb8801779186e23a
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5eef730d6031fb544b8638391db219be
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 1ea5b40ef5a360449ab9a76480cff639
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: ced43f81661a4e144912d65304c9c82a
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 0e37d6c305af3094b9c25c48c6879245
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 95bea9decd004bf439f1c09f0ee84d65
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings: []
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 96, y: 64}
  previousTextureSize: {x: 512, y: 512}
