fileFormatVersion: 2
guid: 41784d66542ed8c43b3fc05c409e105d
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 32
      height: 32
    spriteID: eeafe737d66e3684abda4c2b2e07e8a0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 44
      width: 32
      height: 32
    spriteID: adbe3808a9abfbc47a86dbd785e2aaa4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 44}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 84
      width: 32
      height: 32
    spriteID: 7337c06e94c3bd041a7da231d421fc56
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 84}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 124
      width: 32
      height: 32
    spriteID: a16e70021f72ee54ba3b9986ae7c0999
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 124}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 32
      height: 32
    spriteID: 23dd8cc8dfb96b44196539da3dadcd24
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 32
      height: 32
    spriteID: 302ac5f19cc58a748b71bed2746fb953
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 4
      width: 32
      height: 32
    spriteID: 2a4c9009feedebd439984d40f01a7c0b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 4}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 44
      width: 32
      height: 32
    spriteID: f646fe652405c0843b729f1547a6e929
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 44}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 84
      width: 32
      height: 32
    spriteID: 49459eb8ac83f81458e9b78469fe0995
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 84}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 124
      width: 32
      height: 32
    spriteID: 2a5b79ec521efed41b480984d6c8ce53
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 124}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 164
      width: 32
      height: 32
    spriteID: d6ff845f3bf542946afa30bf2d5d3225
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 164}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 204
      width: 32
      height: 32
    spriteID: acc8b133d284d3945992f55ce416a231
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 204}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 4
      width: 32
      height: 32
    spriteID: 2a335430ba5707f4590e809f468954c0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 4}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 44
      width: 32
      height: 32
    spriteID: 75413cb2916c30146a5b06e24663b012
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 44}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 84
      width: 32
      height: 32
    spriteID: 2fb678fe8ae89004f939cf0da9b5b7c3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 84}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 124
      width: 32
      height: 32
    spriteID: b043f0332a2a51d4cbfd9df35f998b70
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 124}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 164
      width: 32
      height: 32
    spriteID: c6a8072fca203d142bbe2ab073a53ba3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 164}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 204
      width: 32
      height: 32
    spriteID: 91bb3a111873a0d4a9530625cba54b93
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 204}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 4
      width: 32
      height: 32
    spriteID: abfc2b7658ec4e04c80fb2c936037a1d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 4}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 44
      width: 32
      height: 32
    spriteID: f3179204013cd0349846147a9f059bcf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 44}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 84
      width: 32
      height: 32
    spriteID: b6f876ee427bb784f9a6b2e303427439
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 84}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 124
      width: 32
      height: 32
    spriteID: 26d589d6147e0744493f97e069092b34
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 124}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 164
      width: 32
      height: 32
    spriteID: a61707ce4ea8ec64bbe2ca697afce5c3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 164}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 124
      y: 204
      width: 32
      height: 32
    spriteID: ef013f329d7ef8944b97e178c426691f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 124, y: 204}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 164
      y: 4
      width: 32
      height: 32
    spriteID: 0475aa7e1d68db947a32d5c55732551a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 164, y: 4}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2920222514
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Magic trap - Level 1
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: eeafe737d66e3684abda4c2b2e07e8a0
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: adbe3808a9abfbc47a86dbd785e2aaa4
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 7337c06e94c3bd041a7da231d421fc56
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: a16e70021f72ee54ba3b9986ae7c0999
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 23dd8cc8dfb96b44196539da3dadcd24
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 302ac5f19cc58a748b71bed2746fb953
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 2a4c9009feedebd439984d40f01a7c0b
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: f646fe652405c0843b729f1547a6e929
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 49459eb8ac83f81458e9b78469fe0995
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 2a5b79ec521efed41b480984d6c8ce53
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: d6ff845f3bf542946afa30bf2d5d3225
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: acc8b133d284d3945992f55ce416a231
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 2a335430ba5707f4590e809f468954c0
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 75413cb2916c30146a5b06e24663b012
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 2fb678fe8ae89004f939cf0da9b5b7c3
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: b043f0332a2a51d4cbfd9df35f998b70
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: c6a8072fca203d142bbe2ab073a53ba3
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 91bb3a111873a0d4a9530625cba54b93
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: abfc2b7658ec4e04c80fb2c936037a1d
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: f3179204013cd0349846147a9f059bcf
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: b6f876ee427bb784f9a6b2e303427439
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 26d589d6147e0744493f97e069092b34
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: a61707ce4ea8ec64bbe2ca697afce5c3
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: ef013f329d7ef8944b97e178c426691f
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 0475aa7e1d68db947a32d5c55732551a
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings: []
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 32, y: 32}
  previousTextureSize: {x: 256, y: 256}
