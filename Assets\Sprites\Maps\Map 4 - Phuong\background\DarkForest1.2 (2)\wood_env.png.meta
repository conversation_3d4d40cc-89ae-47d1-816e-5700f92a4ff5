fileFormatVersion: 2
guid: c9652bc687b929248aaf6ab39d9fe5a8
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 6167489346621658444
    second: wood_env_0
  - first:
      213: 5140568443003981999
    second: wood_env_1
  - first:
      213: -6083278840270124132
    second: wood_env_2
  - first:
      213: 3201945345161946748
    second: wood_env_3
  - first:
      213: 6440472967513911866
    second: wood_env_4
  - first:
      213: 8028095939034081481
    second: wood_env_5
  - first:
      213: -3508637457953159829
    second: wood_env_6
  - first:
      213: -1479253815536356558
    second: wood_env_7
  - first:
      213: -1253231910567423540
    second: wood_env_8
  - first:
      213: -5370035713757906848
    second: wood_env_9
  - first:
      213: 8015764328856465413
    second: wood_env_10
  - first:
      213: -3764376893969583160
    second: wood_env_11
  - first:
      213: 2795924691096803258
    second: wood_env_12
  - first:
      213: -752450913240699383
    second: wood_env_13
  - first:
      213: -5279076477566529037
    second: wood_env_14
  - first:
      213: 1204312477820097470
    second: wood_env_15
  - first:
      213: -5957924393774764236
    second: wood_env_16
  - first:
      213: -6688109351046928356
    second: wood_env_17
  - first:
      213: 2788164425645588259
    second: wood_env_18
  - first:
      213: 6704687663840560512
    second: wood_env_19
  - first:
      213: 1141072665469655593
    second: wood_env_20
  - first:
      213: -2446962255448878096
    second: wood_env_21
  - first:
      213: -91517009842893696
    second: wood_env_22
  - first:
      213: 8666703567919033044
    second: wood_env_23
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: wood_env_0
      rect:
        serializedVersion: 2
        x: 0
        y: 383
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c45d36d42e2579550800000000000000
      internalID: 6167489346621658444
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_1
      rect:
        serializedVersion: 2
        x: 15
        y: 383
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa0d5d453c7f65740800000000000000
      internalID: 5140568443003981999
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_2
      rect:
        serializedVersion: 2
        x: 40
        y: 401
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9b8c08102ad39ba0800000000000000
      internalID: -6083278840270124132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_3
      rect:
        serializedVersion: 2
        x: 63
        y: 390
        width: 130
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c76e798a3589f6c20800000000000000
      internalID: 3201945345161946748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_4
      rect:
        serializedVersion: 2
        x: 191
        y: 401
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a323c267a08216950800000000000000
      internalID: 6440472967513911866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_5
      rect:
        serializedVersion: 2
        x: 213
        y: 383
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c8e1ecc8b6896f60800000000000000
      internalID: 8028095939034081481
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_6
      rect:
        serializedVersion: 2
        x: 239
        y: 383
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b65821152d0de4fc0800000000000000
      internalID: -3508637457953159829
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_7
      rect:
        serializedVersion: 2
        x: 0
        y: 287
        width: 256
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23370500e72a87be0800000000000000
      internalID: -1479253815536356558
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_8
      rect:
        serializedVersion: 2
        x: 0
        y: 239
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc1d17d9730ab9ee0800000000000000
      internalID: -1253231910567423540
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_9
      rect:
        serializedVersion: 2
        x: 15
        y: 239
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0687c4258fcc975b0800000000000000
      internalID: -5370035713757906848
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_10
      rect:
        serializedVersion: 2
        x: 40
        y: 257
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 500bcb01037bd3f60800000000000000
      internalID: 8015764328856465413
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_11
      rect:
        serializedVersion: 2
        x: 63
        y: 246
        width: 130
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c33142d82f32cbc0800000000000000
      internalID: -3764376893969583160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_12
      rect:
        serializedVersion: 2
        x: 191
        y: 257
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab7919f61be1dc620800000000000000
      internalID: 2795924691096803258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_13
      rect:
        serializedVersion: 2
        x: 213
        y: 239
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 90eae5d81e1ce85f0800000000000000
      internalID: -752450913240699383
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_14
      rect:
        serializedVersion: 2
        x: 239
        y: 239
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3f1d684d9e3fcb6b0800000000000000
      internalID: -5279076477566529037
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_15
      rect:
        serializedVersion: 2
        x: 0
        y: 143
        width: 256
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb3990f51d396b010800000000000000
      internalID: 1204312477820097470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_16
      rect:
        serializedVersion: 2
        x: 0
        y: 95
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43fab6e8453315da0800000000000000
      internalID: -5957924393774764236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_17
      rect:
        serializedVersion: 2
        x: 15
        y: 95
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c1821e7faff0f23a0800000000000000
      internalID: -6688109351046928356
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_18
      rect:
        serializedVersion: 2
        x: 40
        y: 113
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32f7cf5d5cc81b620800000000000000
      internalID: 2788164425645588259
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_19
      rect:
        serializedVersion: 2
        x: 63
        y: 102
        width: 130
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 085092548e5db0d50800000000000000
      internalID: 6704687663840560512
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_20
      rect:
        serializedVersion: 2
        x: 191
        y: 113
        width: 25
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92a1a382b87e5df00800000000000000
      internalID: 1141072665469655593
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_21
      rect:
        serializedVersion: 2
        x: 213
        y: 95
        width: 28
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f35e211bc4aa0ed0800000000000000
      internalID: -2446962255448878096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_22
      rect:
        serializedVersion: 2
        x: 239
        y: 95
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08c8861b3cddabef0800000000000000
      internalID: -91517009842893696
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: wood_env_23
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 256
        height: 97
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4daac631af0564870800000000000000
      internalID: 8666703567919033044
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      wood_env_0: 6167489346621658444
      wood_env_1: 5140568443003981999
      wood_env_10: 8015764328856465413
      wood_env_11: -3764376893969583160
      wood_env_12: 2795924691096803258
      wood_env_13: -752450913240699383
      wood_env_14: -5279076477566529037
      wood_env_15: 1204312477820097470
      wood_env_16: -5957924393774764236
      wood_env_17: -6688109351046928356
      wood_env_18: 2788164425645588259
      wood_env_19: 6704687663840560512
      wood_env_2: -6083278840270124132
      wood_env_20: 1141072665469655593
      wood_env_21: -2446962255448878096
      wood_env_22: -91517009842893696
      wood_env_23: 8666703567919033044
      wood_env_3: 3201945345161946748
      wood_env_4: 6440472967513911866
      wood_env_5: 8028095939034081481
      wood_env_6: -3508637457953159829
      wood_env_7: -1479253815536356558
      wood_env_8: -1253231910567423540
      wood_env_9: -5370035713757906848
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
