fileFormatVersion: 2
guid: a251bee25e9f05b4bb0c5f4ef3c91677
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -3654092879298017055
    second: Massacre Sprite Sheet_0
  - first:
      213: -5930399418827851710
    second: Massacre Sprite Sheet_1
  - first:
      213: -8877710511268033976
    second: Massacre Sprite Sheet_2
  - first:
      213: -3770891993432478066
    second: Massacre Sprite Sheet_3
  - first:
      213: 223601641109954747
    second: Massacre Sprite Sheet_4
  - first:
      213: -6178777790217940784
    second: Massacre Sprite Sheet_5
  - first:
      213: 3689661317885422911
    second: Massacre Sprite Sheet_6
  - first:
      213: -6627930396510656599
    second: Massacre Sprite Sheet_7
  - first:
      213: 3982084767623643326
    second: Massacre Sprite Sheet_8
  - first:
      213: 2746795343777381479
    second: Massacre Sprite Sheet_9
  - first:
      213: 5126986851326471789
    second: Massacre Sprite Sheet_10
  - first:
      213: 8121491975477602359
    second: Massacre Sprite Sheet_11
  - first:
      213: -1720916946182320554
    second: Massacre Sprite Sheet_12
  - first:
      213: -5477728807717674718
    second: Massacre Sprite Sheet_13
  - first:
      213: -7671681470263449926
    second: Massacre Sprite Sheet_14
  - first:
      213: -6261581981287752801
    second: Massacre Sprite Sheet_15
  - first:
      213: -2972512950988516112
    second: Massacre Sprite Sheet_16
  - first:
      213: 7539928288152776706
    second: Massacre Sprite Sheet_17
  - first:
      213: 2146652400394011460
    second: Massacre Sprite Sheet_18
  - first:
      213: -5089728296608670636
    second: Massacre Sprite Sheet_19
  - first:
      213: 5784851191490471300
    second: Massacre Sprite Sheet_20
  - first:
      213: -1362048152855599222
    second: Massacre Sprite Sheet_21
  - first:
      213: 3341017572516919905
    second: Massacre Sprite Sheet_22
  - first:
      213: -798820401790103510
    second: Massacre Sprite Sheet_23
  - first:
      213: -2301842224496724682
    second: Massacre Sprite Sheet_24
  - first:
      213: 5628419984870177855
    second: Massacre Sprite Sheet_25
  - first:
      213: -1839552090562136010
    second: Massacre Sprite Sheet_26
  - first:
      213: 5413173377692829648
    second: Massacre Sprite Sheet_27
  - first:
      213: -8833037255080670181
    second: Massacre Sprite Sheet_28
  - first:
      213: -2410622831306416790
    second: Massacre Sprite Sheet_29
  - first:
      213: -8487885751300724080
    second: Massacre Sprite Sheet_30
  - first:
      213: -8774634776832783232
    second: Massacre Sprite Sheet_31
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Massacre Sprite Sheet_0
      rect:
        serializedVersion: 2
        x: 2
        y: 192
        width: 61
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e84821f2ed0a4dc0800000000000000
      internalID: -3654092879298017055
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_1
      rect:
        serializedVersion: 2
        x: 65
        y: 191
        width: 63
        height: 27
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2483590362df2bda0800000000000000
      internalID: -5930399418827851710
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_2
      rect:
        serializedVersion: 2
        x: 129
        y: 191
        width: 63
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84a0e1d73590cc480800000000000000
      internalID: -8877710511268033976
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_3
      rect:
        serializedVersion: 2
        x: 193
        y: 191
        width: 63
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8ea71506b91babc0800000000000000
      internalID: -3770891993432478066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_4
      rect:
        serializedVersion: 2
        x: 257
        y: 191
        width: 63
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb0ad8502846a1300800000000000000
      internalID: 223601641109954747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_5
      rect:
        serializedVersion: 2
        x: 321
        y: 191
        width: 63
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d09cfb1652904aa0800000000000000
      internalID: -6178777790217940784
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_6
      rect:
        serializedVersion: 2
        x: 5
        y: 127
        width: 59
        height: 25
      alignment: 2
      pivot: {x: 0.5, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3d6f028b6f443330800000000000000
      internalID: 3689661317885422911
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_7
      rect:
        serializedVersion: 2
        x: 68
        y: 129
        width: 60
        height: 33
      alignment: 2
      pivot: {x: 0.5, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a7e7ffab6cd404a0800000000000000
      internalID: -6627930396510656599
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_8
      rect:
        serializedVersion: 2
        x: 129
        y: 133
        width: 64
        height: 37
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb083b78ff4334730800000000000000
      internalID: 3982084767623643326
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_9
      rect:
        serializedVersion: 2
        x: 195
        y: 132
        width: 57
        height: 28
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76c3c80efc39e1620800000000000000
      internalID: 2746795343777381479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_10
      rect:
        serializedVersion: 2
        x: 261
        y: 127
        width: 57
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6ea9699067b62740800000000000000
      internalID: 5126986851326471789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_11
      rect:
        serializedVersion: 2
        x: 5
        y: 63
        width: 52
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73827841ce555b070800000000000000
      internalID: 8121491975477602359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_12
      rect:
        serializedVersion: 2
        x: 71
        y: 63
        width: 50
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65e4adeb7231e18e0800000000000000
      internalID: -1720916946182320554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_13
      rect:
        serializedVersion: 2
        x: 133
        y: 63
        width: 52
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2216010fba23bf3b0800000000000000
      internalID: -5477728807717674718
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_14
      rect:
        serializedVersion: 2
        x: 193
        y: 63
        width: 62
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab6200f5956b88590800000000000000
      internalID: -7671681470263449926
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_15
      rect:
        serializedVersion: 2
        x: 1
        y: 0
        width: 62
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f9b5b510d546a19a0800000000000000
      internalID: -6261581981287752801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_16
      rect:
        serializedVersion: 2
        x: 26
        y: 21
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f4d41a58338fb6d0800000000000000
      internalID: -2972512950988516112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_17
      rect:
        serializedVersion: 2
        x: 65
        y: 0
        width: 62
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 200f34f64d433a860800000000000000
      internalID: 7539928288152776706
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_18
      rect:
        serializedVersion: 2
        x: 90
        y: 28
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44389b51cf07acd10800000000000000
      internalID: 2146652400394011460
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_19
      rect:
        serializedVersion: 2
        x: 129
        y: 0
        width: 58
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45cd26da417ad59b0800000000000000
      internalID: -5089728296608670636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_20
      rect:
        serializedVersion: 2
        x: 193
        y: 7
        width: 24
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 485e84b528be74050800000000000000
      internalID: 5784851191490471300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_21
      rect:
        serializedVersion: 2
        x: 206
        y: 21
        width: 45
        height: 24
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a87a012eb68091de0800000000000000
      internalID: -1362048152855599222
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_22
      rect:
        serializedVersion: 2
        x: 216
        y: 0
        width: 39
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16a753ed7cdad5e20800000000000000
      internalID: 3341017572516919905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_23
      rect:
        serializedVersion: 2
        x: 257
        y: 4
        width: 24
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2057ffa3150ae4f0800000000000000
      internalID: -798820401790103510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_24
      rect:
        serializedVersion: 2
        x: 270
        y: 22
        width: 45
        height: 25
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 635b0e741b63e00e0800000000000000
      internalID: -2301842224496724682
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_25
      rect:
        serializedVersion: 2
        x: 321
        y: 0
        width: 56
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3c75e4d72a2c1e40800000000000000
      internalID: 5628419984870177855
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_26
      rect:
        serializedVersion: 2
        x: 182
        y: 6
        width: 9
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6387738be199876e0800000000000000
      internalID: -1839552090562136010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_27
      rect:
        serializedVersion: 2
        x: 206
        y: 0
        width: 17
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d78bfcb1847f1b40800000000000000
      internalID: 5413173377692829648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_28
      rect:
        serializedVersion: 2
        x: 270
        y: 0
        width: 37
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1c002b7a6fba6580800000000000000
      internalID: -8833037255080670181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_29
      rect:
        serializedVersion: 2
        x: 344
        y: 0
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6dd8849e4fbb8ed0800000000000000
      internalID: -2410622831306416790
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_30
      rect:
        serializedVersion: 2
        x: 385
        y: 0
        width: 29
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09aee9341e8f43a80800000000000000
      internalID: -8487885751300724080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Massacre Sprite Sheet_31
      rect:
        serializedVersion: 2
        x: 408
        y: 0
        width: 27
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08cc715192c3a3680800000000000000
      internalID: -8774634776832783232
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 71346361e7f63644b9bf179f31d0041f
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Massacre Sprite Sheet_0: -3654092879298017055
      Massacre Sprite Sheet_1: -5930399418827851710
      Massacre Sprite Sheet_10: 5126986851326471789
      Massacre Sprite Sheet_11: 8121491975477602359
      Massacre Sprite Sheet_12: -1720916946182320554
      Massacre Sprite Sheet_13: -5477728807717674718
      Massacre Sprite Sheet_14: -7671681470263449926
      Massacre Sprite Sheet_15: -6261581981287752801
      Massacre Sprite Sheet_16: -2972512950988516112
      Massacre Sprite Sheet_17: 7539928288152776706
      Massacre Sprite Sheet_18: 2146652400394011460
      Massacre Sprite Sheet_19: -5089728296608670636
      Massacre Sprite Sheet_2: -8877710511268033976
      Massacre Sprite Sheet_20: 5784851191490471300
      Massacre Sprite Sheet_21: -1362048152855599222
      Massacre Sprite Sheet_22: 3341017572516919905
      Massacre Sprite Sheet_23: -798820401790103510
      Massacre Sprite Sheet_24: -2301842224496724682
      Massacre Sprite Sheet_25: 5628419984870177855
      Massacre Sprite Sheet_26: -1839552090562136010
      Massacre Sprite Sheet_27: 5413173377692829648
      Massacre Sprite Sheet_28: -8833037255080670181
      Massacre Sprite Sheet_29: -2410622831306416790
      Massacre Sprite Sheet_3: -3770891993432478066
      Massacre Sprite Sheet_30: -8487885751300724080
      Massacre Sprite Sheet_31: -8774634776832783232
      Massacre Sprite Sheet_4: 223601641109954747
      Massacre Sprite Sheet_5: -6178777790217940784
      Massacre Sprite Sheet_6: 3689661317885422911
      Massacre Sprite Sheet_7: -6627930396510656599
      Massacre Sprite Sheet_8: 3982084767623643326
      Massacre Sprite Sheet_9: 2746795343777381479
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
