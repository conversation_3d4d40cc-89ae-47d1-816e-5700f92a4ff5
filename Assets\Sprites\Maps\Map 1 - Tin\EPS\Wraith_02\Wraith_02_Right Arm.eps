%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Right Arm.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 3 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
31.9229 52.7373 mo
31.0024 52.7373 30.0718 52.6494 29.1558 52.4775 cv
22.9224 51.3047 20.061 46.0254 19.3535 43.0918 cv
19.1973 42.4443 19.1719 41.7725 19.2788 41.1152 cv
19.3535 40.6572 21.1431 29.7969 24.439 20.7354 cv
26.0962 16.1748 30.1851 11.2637 35.2451 11.2637 cv
35.5137 11.2637 35.7822 11.2783 36.0479 11.3057 cv
42.084 11.9453 44.7861 18.4385 44.7861 24.5889 cv
44.7861 25.1514 44.7695 38.4541 43.29 46.293 cv
43.0938 47.3359 42.5703 48.2891 41.7959 49.0146 cv
41.3896 49.3955 37.6582 52.7373 31.9233 52.7373 cv
31.9229 52.7373 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.0318608 .520439 .874327 .00128176 cmyk
f
35.2451 16.2637 mo
32.8599 16.2637 30.2183 19.4717 29.1382 22.4434 cv
26.0054 31.0557 24.2852 41.4805 24.2134 41.9199 cv
24.2256 41.9668 25.415 46.6855 30.0801 47.5635 cv
30.6934 47.6787 31.314 47.7373 31.9229 47.7373 cv
35.7725 47.7373 38.2734 45.4619 38.3779 45.3652 cv
39.4912 39.459 39.7861 29.1924 39.7861 24.5889 cv
39.7861 21.4297 38.6504 16.6094 35.5205 16.2773 cv
35.4395 16.2695 35.3428 16.2637 35.2451 16.2637 cv
cp
31.9233 57.7373 mo
31.9229 57.7373 li
30.6968 57.7373 29.4556 57.6211 28.2339 57.3916 cv
20.5806 55.9521 15.8188 49.7607 14.4927 44.2637 cv
14.1792 42.9629 14.1289 41.6338 14.3438 40.3125 cv
14.4219 39.833 16.2905 28.5098 19.7402 19.0264 cv
22.0483 12.6748 27.7383 6.26367 35.2451 6.26367 cv
35.6777 6.26367 36.1201 6.28711 36.5596 6.33203 cv
44.4766 7.1709 49.7861 14.5068 49.7861 24.5889 cv
49.7861 26.0469 49.7422 39.0693 48.2031 47.2207 cv
47.8086 49.3184 46.7744 51.2012 45.2148 52.6631 cv
44.3115 53.5098 39.4229 57.7373 31.9233 57.7373 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Right Arm.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9ON;h^Sd)]/q<:%WJRj$OfS2Yt.@E(1J^'KMoB@Xrbd6*)iZd-_=kQe\N&imR:G.d7r$(]Yh`JRL
%md2s:hZ!NLmf37EN=YF=8IXk'pMf^L'_hCMpV-C:?N&mF\+`+1IeVj62_X';q"af204',)mHNHmGON<FiQ^W5cemUWjSA9'0@+Qc
%oCDS?GCR1a^4-(@hr$;G+6VP<^3]bKp>,U)cTg3LViHT'\+JWdI1)[!$Li86fAHT&%?Q&7o"t^r$.M]:Spp7"s"h+jjJOV@mgK,X
%>q[(EnLhl9DqrV3:I"D/5P\0hq9=ucc2[U%qZ5:<J&)dUf?u/H;JPq\i\."4N4$Gs?Q+C#ib(o<%i>.c)]t+M)TA$#1G1'MV3g0]
%ersT:52Ubf(I.ICh#-Q#Ek=@2aJ\RUgiH7j8+;eT!2Sc9QOm?Dr6alhUHUa6C`gp!Qs(!lA1YFNi.kQ/`Dun6`=E[o*suF5(ih&"
%A;n>bI)?@UboOhRLKOAYV;Puba3Fm-+,2e2[rBt44r.UZa7.W?dpTcN)mtc[9+`[l28t+7jr>qE^`TE0V#VT3FaQ)hT/'F+;S_.S
%hH^Z??DdFheKYReoI8uL49?a)HAME:VTrTXck1W7,bI1nNGG6J0?H-`o7CHS"%h<afK8lgZCM\M9DCia_4&/H8reHL4Qi0l:[lbl
%I4RA&H$loE?TjE@D5@u;Zf4&o(B7?4K''fI&&3AbqcD[(GlR]ZX*8L^M*?4[ET-<SbEm^'c@:?R!.,:"pYh/lkEir1rnZlN]MEFb
%;t0+.+5Chq5PkO\^84WAJDLG*\<Gp_1a?[ZipjeRf_.9%4l!uF`9mL#k@\U`-fV#5)jnZ2XfXfG.f)>OP'6g$GCOl/r;FBY]?apT
%OhZ8=jq"YfIsB(OW5+e<?[I>'c2Z.GJ[NjW:OL5;l8MM=_ej[_')`JG"T1n6*pi4S+()_ZdgpBrf?<@7]R2A;lL=ej044[-^l:s<
%s+L0*mp<bVs/WW45!Xn6Ra+k)PBYosHk$#n+231%S/Bg6%eN\kpq,_XJ,f'AYh=6(^VcK9iD"Ci\"A-Dk1T>j^I2DnV3.DKiT8h/
%Hh[9ac/.EU@JT^)>7B(>CtNb1)[PU?S&"b,g*4eQe;KT"'j>n%.Ju3qf:Ta+p%qg/J,$s[Eq:R>,/!YghnT#"Yhg:9)ZS_if;A#>
%hpPeTq%"QR^=E5[nCuDf-78/ProBEup>t[#Vg!&u%_fo3*apS3"igQ<h='I/iudnPW8bERaNG.R[m,9G+*dGo.t7E[Vu4$tgRl7[
%%fZ74n)&QfLEF;0h7L^g^45q?h*8ZeH1pOjb8^H@%K5r)2rFYUkhSJC*;R44IuM@aGCXV)-ho;ZbOC!hP#qa4k/ZAlHp1<1VpA`K
%i<T+ar6'S:q;K<-Hrf!,`@&(mpFfm$GkW7[o/ATmqr`H_Io+m.DTD?-[Y;'sm3_UT%f#c;r^3^V#<tD>nD>,I=ZZt5>C-nF*3>G"
%3\0]&qPX>m+eK!W#Qle2prDS"pBURqh<8):p``:9!rcPKjO4RiFa<l)Q`o<F!ScdP+4>_4^OH1Uq>4l(5J@$q!?['4X`:rc?j6$.
%,[E0CStc5c/Rq:WIe`Sgq@!ej%kpY[rj^VmpHC;0ks8$,s6qZas1NgWc*s<03kk.iBiOU%=eE_8GQqI5Q6+JZe+;1p?iC!&o0</r
%Wu!_8pYUDr0/nCTmbR1^d)dudP0R$s2Hu?(VD$tP(1is)+5)W2;!IB0r_dsF[guq3It%"8mp<d*p<?u*H+C`S/^i:n7=%>&J7"eH
%JmX?@ge8^'o4ZK`1B,,I$t_i6#(?Wo+pfh_>Y39!IsCYGoug_Aq:cE5+1Bej>^?ACB5`&qIDJJ_EA4nsPN0YZgbCO&g2i$5C0(<A
%]o%7'bAJObC2/ia)7ZSDiRc\k^>3/Cb$)Q5f+g%-HhqsJ^>P^&0;R>[l#)hH(7Ci\edhV@:qJR[?a6*+9lDZA]E4M`b$fSY1V`k:
%EtI,u^&$o=:*lFohn==D.`fjf^O,Y4,n"F93?1UdI2NCS\7n!7jIiL"?@,ZLbL(t?>EH%$q>&-ra,`LYI65X(J,B6@2m5J/rulK\
%Jn.bf!EWY\'q,?O(]Fr[6W`K@BJ;68pA\WV0%>R3n5+<+6$rsF@#md003]O_UrRBp5Pg/UO8^N>L:7esN3=KQ9.<[m$s93$Vp`(?
%NVNMUPp;M\4Mf>WQ*^D<Ee/6=Kmk@=`Bsr.\7,/Yr$Z,)"Ub:2nD3#8K@6_=kkgI%:ejj4:bGR^dqs:'%4,NqhZ7%-4FT[OPNY)>
%r@1VnHoLiRTQDXgpccPjr'&4%O&SlL5`KFOs4PPK^2NV>\9n\A(]Co@(]7R1i4n(,^S-05aF>)"PMbS&<<$*MhV)o'lG`b9.+H</
%o[ip'Mq*2j5Hul5qso6c>&dpElRuG:G7dP[3/F`K#,SpVP`r_h>BfBR./k9RIIb?i?[[QIn4.t[EqZuY;1Z,A>9]b<&5!B`F=;o`
%%&\%NkAVAXAbSUG-\Z_2LN9&QO6[e#DeX&Y=O2P!G,DZPBbf[^4k%Qed!GAl;PU>=67]@jpOQB%RF`"p&?_8ugG'NBC8F9X5WP-/
%3LAK@>N,6iJ>P)MQeJ,)HE=#jY.$)An5?K)F"?a8ep9D?IaekpfM:h^DS,3U?9,i6.)#<1&,k#(rGli^IgE8).[7j0#j$HDe[9G0
%rjkod;*!DQTl_]<gBOKNJJYo:/W2t';a_]e;`S,koj3YZ:/HuENTC.Oq=hRC^0E;"[iPlM#4sY+b!MFmWF1duoPRs=he.8!SZeTn
%rlhSG&iEuRj+T"Vhj2#L^@LbHD^AMB;IK1FEJI$tZhu9Vq%T+sE@^*OB.+SV>5T+kF(O?;Kt(36r5a*@?tXu*'%#teN.3SN6I(:t
%lPqP(&ML&119Pb]di(1]E%4AU0lef+<<kQpJPQT=8p55M]SZCrnt]eY$2G2"@c0cnH;W`o&\([B5mI/462Ao[,9]k"YHDYpFukt-
%V-0cqMA/8,Oq\Ng/qF??&^2d9mJ3%TZQ&g';)qMT[W_(<)O*ht?:0`Q'K/dT#uk<r+$[UK@n&t)#HP_F,Er9F@%\lTXs\JQ'/4,5
%+]*-oJVU5@,No7DO3@g]g#GTP&ZbuIOO\M&^n?`BBL`nA&kGjsM)`%9=9Q-ULh5OAi@CFS6fSt:nAgS"^u0c^j$A6*%>mpn\9S(G
%mX_]+@"1/CptFBVA&jYh=6jgIi":TZ3@K.!Kar#1VZ\PT@%K=i3)h9l+>a0BTfFCE%a6=.DC?b74E?o9VMgX]`o2Fa_?6W9O<e77
%<&gueXcc(]AtZKS&\Gd8,f&p.!$(4PC\VitRks7PKr:?:L'C_h3qE,:"ZsV7;5UZ*"k,^?:(=.acR&MKF6R55:Ug#;lV?%NY;BQq
%]Xi2r_5`*pdUc,f=\ZPp?ld?-]%H<,Q(tC7f8QjK>q<.sA=_p&dIf6FL9?H>-9A8_8o%k:"'u(+,HZMAc$uMakIHqIQT<+^5083D
%>\BL@SnCQ7I&8nf_T^F:e*Q)<-CJ!aZ[.cPD)A!kfLl9P&l9JmknI^r,>%$W,k_YPD'jNLV0nC"@IR-_3g++%87T`V_a#4FIca2]
%30RZhpr"u0hcHJjCOZ^6=ldeW)WSLW4fDmD:h.5GP^?&/(9"KU9"V"n]*d&DBAm.XG\9%=$e.09qg:oE/.8SFFS!3'*oE1X]QEnC
%&%f\-8cK([hf\]BHLH<0a-.I,!'QSBo>bdSQ2ArBmV*l0"6fT7EQG+@F=h.(bK\l<e>bFr%-aTFJGhmmUW*p(LA]"",jEM+8*IWu
%o*?qo""f]EWJmo4ek'#WOKo.A&htnTeAO.7*4I>64anX\(,i0,1S&VBQ`,I'9d8EsY\`H_.Y\HhGef29K?#P_4Nu0gjp`$L:jp9'
%SkTp.PB`DXaR"#)K!G[Z'i\LKab>tt"#+O*a?4Q"OYCqY2LfT\;@B6JKr>Vj5[SPNGU-k"`;[(KEFo!WUs(..hiij&Mk<gUU0d?8
%-pN>(0e?(YT8pXb^sU'?NsZaUBUn9/3fqG1j[eY^N&E_en@Qgt`CCZ$,"341/1U>f4k&'F`SZRZ\O@3NCnjpUfIi`n/-MZnX(/UN
%8!!5cKd8\k8'<0:NY5&7,u41/Obfb,`Bn5o&[[]T&OlVN--`0;#n``HbXt4!#a_LRK+N%"W_e4RnU\YM&g9ho4A[1-OK<..er1tr
%iRYrb7V7G(P0>QeKfEQl&9\X$E%Fjc81"OI5[ZR6R01rsePX&)kirbZLYGY8p`SMtiF7Z[m<;C.Z79"@MKN_"<LWXZf^ZK/eL769
%ePR&3g"WhV8BjE.S*:@D]prU\(GSXaEUkNi)W'9YP%XW:>l7R]-%Cef^tQ<TODWg9:jWmr[Yc3N67`MK]G;#%[MBHXK8?S@_N#tA
%JgbMG.LW3l;Ik#`Z#H_;.1<"&QE!bB6rFUf=5rET7[Kk5;@m,lTdSOb['sJijuYuXHNd7+GH1GB.*Uurqf*/nLt/.F$teXIbi+`;
%ciY"B.sN]Y#J2iNM8e\bENu:;9F'N,b.XhE*9KK(NJ+W.;5Jm2,L:D!=M\*\5)XH(&d@Sl-:LtA\o\sm#)0c,rlS.&OCJ5T+e*0h
%)[4ECCi9u.1E!$7m)Y..1@/sK$l?j$5*X1Fd)F)+Y(gqB6G9JdY8dr:=pXCsOAhFj<BXJt[fX/UaJiS1XLT*WLZ0BJe!V.d=/eid
%aRG7=.ujbdZX_UYbm8e\9Y5CA#<Jn1S_$G&\VQ]d&4c1ZDSe'KKCF`4^^Cgg2=1^-T3BbL)S-Xt*s-rr\MWs>!s(o#WF6$Z9>HOt
%ZZB24X0sV.G'*LD:p/&M<j'^2p.M_sHai#/:_h?A30KM]b-tF3mS_`(lIeUk0;rlh?FO,%<9>Dgd.sb-".dtI1)WR7Z,>^$/?iL&
%ahs.1".eIW:?(nSBq>]X#t+mio$c'5+2jrn"%[dW:9lj7$F<]pr)")7;sOJFJKm\g-ZU&<\OH*?F$4OdTrd0"rF!nE#1%'(Y'2_c
%l<0WO\+,&^%chuP,!9j01k$_1H\i.TR1-]1S>oSHniq8u*p*:;dL"EJ-3MFom&INFIQ0;rS@E$u\Duq)K`[buOT>\I-?IFJHrE&'
%o$;r.d"=kI7k<kd<G1K?Ecr@F*A5qC"RZM5^YpBsV6.b-TEZ?T(*mm4Ao_V:Ba7\nBDhcSmBVrS++KbS)RmAkA2fL]P@X]t>9V28
%C%0qihfRU-Wr8N'YCKki4%ArNmYscL6[)(rh9g%g*2_OXHXb\@\ebG7K<ZA;-Od+[ok%6M`l_B-R9)O'1g%<"dQ=up%O,m77=W#g
%@)/6>G+@`*2)<EMAIL>)`qDbsIf/5`Xok4d"lI9DZ(X6<NERAG5GcOe*ndL_J%!Fm:H>A1'n7R5CpJEn!QueXF)c,j$=4
%Ce6u)`&KN.EXa;\"$(3O_ekn6"%p,!e7F1LX)X=H<K2$fb!kBl_-'I[)VbKc>ks<0V7#55aB(-:P9cZoP*),EDQ,j_:*al4/;XgL
%`FV:ji$V.%_5+AMP$',%QcN_^'sii]SFK+s<)4]D?[*%4Tug(+5iOA2\SQ%HgB4Sc[7,1?*s[u.ca6"kCuIWB60oY/&W*l$fQ*0E
%cYi+1#OMRurN)c!aJM=N3P09-dA=7hX!uM46M7'qr]6-Li'b-nY-$H=('-(X3l9BYqC\k<aBHLM'<O5tF`IHefYC'\GQDraOj2j(
%4H_M\9KrDZ?t(1;:ps7IR&C(UUOf"n-AWm[SX7eW0K9S:+OLl=:rGD.W[H.sDh6RF=A6L2@&Qn!4+NoL!J)F+#b[UoW;`--9E-;R
%]\a=,le8l6^=\]<c,hY$&CtkJ>B3L!`"Vs/]:huA3VVIu9#OP`S9Ks/4[VhPq=$MgKsN<i>CG0/"PTSRJ_9C+)XV['d)Bff>H?,%
%oZngC.%-%$"pGIjDBV3^r.d7lSMuCAQ'M-$SMTDQ:W0mm\,1g*+I>15=UW`(T#tbINiOp&E;H:c/Porb;QOCZiuj1V_Jnm?O[Z"8
%"^[m0B63g=(6G</$A'sa%qb$V!"YaB0H@rR_@@%[4(ng[(m\J3*PfftfYK_DKDn]o!Us?6-/T"h(k@7K!?@WK/hG:Z3#DDmDST<4
%\-"XVTa&c7#'o0FL@Y_s/qk1Ae+k\K%+Oeu2F"JCWSL?%J*MJ0gE5d9@.q?D!F3..D(htllX!_ti;L&W"_t]]WXY3^;(ET#]Ou(7
%f(F<nY*UYCehs"fo?(AACA_gH0A#h/84+mG<pgBlYnarTcdu4hAgKZj1cuTgHtKpLa!t+l`*s0OO;V^9<j"c$[d.!q027f8AX7+U
%XPBK"Bj%u>Yf%;00jCYRRh_F2H:t:RABg$7C6o_%4e)*O2R.d+(h`tGRKp_W:(R.]hGFd+!];I2)Pp0):8LWq@'PJR6*_]:MBG>]
%4_ef=9rUecCo`9AMt0()K9<AH*3pLPWefH!VNmqgXMGPT$u>!a!ld8XJn%gNU!8+h!Qe5'\V=\_"1-p%T>4UGTNOpj\`B5i%4:U2
%Gk4&58d#Vao?T$QL:b6(\g@piRPh3?3"9G9Y#jdgT4<Qb=O.!@b\NGD.GMEZUd.s18Qh$>(,B#*!a44s=W>XsI5m<O!c8SSj>aML
%"LRu]<T>X]NW[s".bR8;:4ed%LFdRVL]="E<q]i,e`Us.YMAWhWE4!2br*sQ$\k%,?#6It;/Bo\@:O0,krA3^Q4`/Jc%YP./5)SQ
%[rF61]ZZ[#YlY[Y>qS]<RJ?k"p8NSlarX7`jgYTSr@Biu%KO>idD^QkheL-0(/@au2GS)W+mVq2q$JN=ZYbMUg/1WkB;<dQXfVi5
%^\/e11pT5TZITdG(aQ@\7?QQ^[QA<=:6/,4qTm#:e6^@&5ps`*rm#uO-CTS?S#'(A<qp3:7,7trUm43)\?'SGZ32="1eAP:VJPVd
%TIh2P"^5b/>??`'Ie*K1`V\Q2q]F)SO8Oi16MqlPTf5/W1-[@]IrI.Y6[%Z"GS[jW3-dH`n/LoMZ3A5BNLkak9idWrr`f:Ar^^\"
%i`UHc5lrSUm5t$u-*\uj*^e\GQT[2cd>;[qh=sj4:o,SP"gpAH-\F?`G\=fA5=q6l@BhKNC-TYA#A=oYA.P3LZZ+CqX1[1.BZ/Hd
%fE5"qXag%SpskgG;3(<TdX5ItNcd!*T2pB82:!SlC/;.dfp\GT1GdVXC6c+p5-=S4:;-92Xo1ce0YtBZK_IbE-\%H'qLudO:_5dR
%<.C!k'pch>aPY\C8X$P8\+"g>&=*]PA,$@bZ-8m"UWM.<?1Uj&ef!+RJgj_,g[/[6k;/VlPFR%:N0$ZdZQ?*%NMRu4"(R*)]h74[
%;k?Db5FX3Q>"EU&ZG[d&*lg%kJa@GS5R(`_XfnP1V=Z)IgE`u),rb`2m=;EmL(oDf-'m[?M/LCVj#8MVoUNZ]*!<2.@&5MK>El]K
%AQ?ql"Z<SSF$X)rF@V2B!]BCBO5X#q+]g^uYYagSaj1juMY.IFnV#X`@GR;j+NRb)l/;k&#5FudE@SlN'&6&h%d'(Y@A$<Q7"R]'
%GSJi5Z0V[BZ5>57&tI*`>:D2J$.#OHi,9Dpc[c=lN2LggD`'Veot8j3g(2O$i`)LE@fdG33IKIu,6K/_=aJM/T6jc;rUtcufAY^"
%JYdk4YQ&]&kahQpQ&@N,M^Rp\(=Ce?QNXKp"f9<a8aKe]%pTYKC*ne6C;?Wu9:jfM-Q\N*Io=@3_Vp[`nsl5o-g,.TL'-d\nXO.<
%""RLp3pDJk@]O.i,;B\UM<BP?-cp``Q$?7U/p7p>MYU)9:DtU?Ua9[EV%hq09fP%PoHSAO1\(GP*J&khQ.#b&O%A+CosYVq>oIup
%3dG'B@f,-5$,>ubU(`JD&8Xls'>94nCes'>%ni$@Z&7/1RD&8IKm\pMc82#u(U;b1jIlht;M#Xk"&CO=.qKS4.b<_7.^ue;D8Sc.
%ARcl/46$(-EjmoS;08(!*efAX9UFT=7AuV=JHF6Lh`6&kD6]!Odk6NdMlQ0=U[B>]7tbYW5=;l`j!MKVhFEnop5sYEbm-h-;"?o4
%]gOq2X`dU/5\8eFBA6))r/e4Rkj0q1U\`V?ZCgi7<"SNZE`^Z&!f<bCFJO8!ln7%"jMRM^<tq!G?L9LMPN!PFdr(ZLgXpR[2>#V>
%dK@\n:UpGC57seK*:\;(APq`mN,VI8C\tg&;%=*gk*m@lZ#8OD9BO>8^:Z)4]]7/$G8V5Nd$iIZ(F<I4i4>rP:i1pQ3f<UhCmZhD
%YqgXEY>D<7(WEu)F.EMqFeDjs/VW6Q+^&GOf+Q?>7^R?V]GrMHjCB\qc>1YC$I:U!(2LjBV9oiY<EUGOQPcl6lT.^NMr>:"`e<D6
%kJ\Xh[r^5Q'E+p6/X.k2/9&#_EYlY?D[Y()6S4r.[BM&oc;t4q@a\>0qGPmGp^R8NHZ9lVX+=jW>?YHf[jb\s1(/\`Lm_.>L7>ck
%e_^%WSK+p:Sul?DGV-;#/L?:Mqo]/I9oTE:q0IWu*U"*NBAi%.TqS\-]T9.+-[Y;I,"qU$e&*KEqoa0OTcns@*2MtJ(8YBOIJ`re
%bAV9I0;KQ2ZFi\5AMfi$IkMAU6=>oXJKRH"?^?cpc'Y.)4j+s33-+/S*eSN%$gfAn0]EAs>6aU$@FP]Q>R'b5m\6gc<et;g.g2/m
%N!Y.05j/8h.tWl5.>/,cMLe$D-pV3S?6g#=@M5[Y\!Eus"eZOMc&RIJ9Yq6r2'C1p(kZ,?%''GLj[eA;$7.*VYjbL]bW(bNED&V#
%$OK7^rD(-XcEqU8dG'SXNVQUSY\$bd9Z_;nc%_?ik*=W`14Cb&DmL=9rXUm?mp^U2NA/7]2ePi%:>jfR-VccZG@)\AK=Qe,R%.t_
%in0_LlJ4RlL2asIeXlj/V.gA1?)oAo/:+>L$Jgn5WsBtqd^Qa$[BKY"9a'Q5S>h:c:cCIHLLKX>mTh^@d?fQD<A@M!q)/ou+P@5m
%]OZ*FR7remUn[(q!K,n#8&6!F)PN#?]0bu%Tnp/lMTF*?&MAfueJ!p!\kR/P"k(=c<ei>)#mr[4S:hfdJICj4kfu;RMN:4Y:IZq#
%e^86L_+\PM?#h/h$%=A^>g6L'TVh'$E8k]'NEeoBB_W=\]L<#W&/!U2KffL_>2DqV\-P0QSL?k"<Tc\AKcNjLgdR6Vi0-0?1)Z%f
%^@>^b13+s71/@>NFdSucUeq_o`:%(R<Pp'm/$=a2of>K]E)HbUd:a[&>cY$6i[VKOba"%B7r`+G",:)7k]/t5YS/DF8Y!T];"8']
%A-_+VK_d0YOVk_krf6(+:m5:]:s`UQ$TA$U"D5ud0UqX]_H?!q%<(!h1"V&E>U*T(,a/qh-FlLX84Y'.IO.Y.QO1c]r+M3/jHDqd
%#IjqCFs!%_A-3WMJHa-2[K$]&JT20MSAQI>.-)?Z3JBmsToi9(,kQKk+#kp<UN'PaMUWD6TQ#L?TrJ/Fcn^*qn:FLaYG%qHIb:Y4
%N^)lC'5W.&o:ICls6Sc*XIoet@+VidN*q6_@.O_kT\tK#_>l"r/Ar&HCpH39fs-:U2\c5.9#VmprA$cV$I9>+2Q(S_K2>'Mb9SFF
%];.\i__'YPUi[Ha.YkS&+KDataWl=8%<lN>NLBldj9[r.OG$Uf6F&k0C527ee3gP`6A$nk+I-VPE]3BcJT$qNbfD`2q:*arI$RHS
%C@#`LDh;9bWXr56Bn.gK^<d(u+t9K?]2#W2WlfRTYYFWNB@Xla=Ka@)<@8M]j36+Wk`_Zq8f_J=%K#qHIUkk37U6@8=,RF/bU9>_
%4#fmTO8u==PP"r78YN4O:IFLih>$cs*kXV%"4#]sD..oZqnY02bohnOJ-e<`I">?Pqla>qe`0\_>g#RjYQPHV.i+5E:;(4H!eV0[
%!b'^+_UAkCF#'E7aAsRSLc1?fb8o^<^>a\ghODqcS=`^[`LU?EnsCGq%k;"cm+(m9?[MGMP39Pmq<P#"Tc*@rc4[d<TJ`]?O(S4%
%P(_^=&G.7;\6I'48_+O^MU!IhEA7%$:=>mTbLH^X=/RK7SsH6ne<;kQoJ1dc=hJsb;;B@:Y>W.sA21D90j.no>ROA5b:0>h0aWUX
%Q.t69Fh,3#b`^L(Q`sPa`a/G=QbT+1Vq(3lDe"kc#!c\.F+^lJ(@*'F>RjEHobg<;X0;qNm9O+jiMtl;@[,@p1/".3Vcni;Mrd25
%'H1.&LZrcDqT2lTEYSVW0,0F(gn[N)AIV/oE0[TfW.cKC@.5=6.1DmX']C/_Vis@Y\?K=8IG9'5GqG]b=o@t:>$8l=Q<>dOGl(b"
%>U1Pn\nS@7#Cf%!bHKsh(((<BU?+%.j'P4\l[=%65I!>\`i0bur;G+%qFgJNbJ3_"_&?B@Wp='A]qE%:E7.b5h%Waj(+$0,q?W-D
%K`mtr0Q.u)[o_u4gaPee@o,K&pmT'+5N_9Z]hhnC03\?:f/To+Mh0;<V_4(](hm89RIRTac4t)s+e2J[lLQSRSX`&*kA\\Z:sJ<W
%`tGIF5/]JEq%E\?%,@==p3$N2"RHRT\m:El#i;%<q"DP\,H1'M-g^^k"FRt?a3qg,5GbHtjCRmpI_kU,;rb7=cZEejV-j[,JJB"c
%;(5<c^^@g&WpR/k'<Y&5OXCK.BXH3,?*VH1"L[8>@"65dFtA%^S)fdmk?*'laOd]<JA9?Y^E6iDLKIk6XeV\6PW\2O9#ircA(i2L
%>blGJKT<MDWCQ\9S/99f7tO5_LILHsZH1Z+27AVWnrE8Qi27;u%pJX'jU+-95@KOEi7IUnfDh/(Y0oIATOMl:i@h$:E^hfna$_k+
%>NWig@@?1bc/(ekI?FBQ:5cV:+7Y)$XaO%<mgD4\s&)nSk]X)@EHCa)A=HhWG:[QrCbZn&6iE#E^8O>4%'sfq!2l&.9eBmb+(HCV
%HC"JN$Ocimc=Ck_Pe`6+i!2h?MKc7+)cqZiPh3%TTCer.'8O97Rf3;L.2o/aEI-[nC')E6YK>64.=*:!:X\_`kDC7+Sn4da9N/l6
%Y5\"N0jgb3So3:bprl<U&?iXQECWpZAP1U`n_ThdHT1ijJ2:<mf58Z3=5X?NLn7+d"?MMllq4pn*rT3/[k-rCgs(h`OK`;An%tBm
%:F#5m-1:@t<@Mn+s0PJbZe3Jc)A1%@B@=^XO?(NY6YT0u`U[BI9C.Shbe0:NNfIn)\ahVi<GW6lP_gOs#D#MnJP'U'hEbZCTLaaA
%7HU@_TTT!tQXK0n7buB&pO8"+^KBRe\fCI%74$FO&sId/\.6e:a7)7;/N3Z`)U"h9]$$u+C:6-1Y$k>iPqm^gq6"2nRXQ(8qKYPl
%c.?=Rlo/L5a.cq\H=sG@U+FP1)ZpBd!:`8!aN'S18?r)Y8'N]3TLb[G.^Pf1's&/UG8(n\+pMr'kJ34c(S;oa3YDh0N7-skGb%is
%6OL)m[hGu:_*iA<JoMCd[4M?#!V\=9&*;PO!e$A0'>IK6F>s_BQ1TCFLGEk]_3NTGe_l,(B-qLU#uuB=MLr7ha[d:[9oNfo-!Z@)
%])kr8Le^UJBj@MmDS_^YV$@B$T4$GZ#Y*FOf55PUD6eP+KqS<]Dg27UE"[DMW&)A^+l_8d:\kG+2d=\g9@W])`0q70f$G>dq8Gak
%WD;j@kJ$DO*PKc8]5laC]7@Q*\;@ZoE(tLI4f"oXRM@Dc8jnmQ-_*$l$Il"]5nX`Rq<9I[d5!SA$::MX,ZOmD"HdBC`GcBY`*k$I
%Od^J.5`0o*UiS8`USOY\(;d#dN+"/W9t:=iGA,G%Z\:lsDjLB;.>Z4mfl.:B/(McB]#lH'23oblOQIfCLA=sXZue"GWE_AaW&7an
%H-sDGHaQ:QNFXeg-]Qfkj6kVI$(3Jgq`"'$GG!dC1_593B2j.YA,\]"aSR`">bd-^d1YHpFn_\6>K,IAD?ETY[7I:lplXLX0:#@j
%XfZoGe=6Y@mhsZVF-^P,35nI_lZ'@ilo<tG\K&j8b-oSX,sR66;nCf9/g;10LHc%oI3b+N(,LPoQa?=B*78_gR"g5u^X2,M;O!o(
%@%ASMRj;@QSZkth.HO_lSsMlObC[21`7_dKQdQ6jcm!V(pt+G#b*>r2Ws`7P>fRM\3m8Rui`ATQ.HMq4"UEFOm3V5"_DO0Le6HTC
%g!@=I'"!UN;HtcH'W"6&mJG`<^9a<t9\+f,0=>s#3$n"M@n2DpUl'(9R\2UX4LGZ6`"haq3T42(Ui1Q:f6Ku?3oWDV>l07^Oi'`d
%-J/2J'D5*l:ZX?b3R-WpXQi.0bPQe>@uqs!^L*h)4Bo,kO_D&^O_ReS\3)'`e"ipTfomXY6.7(@Bh'LCM5\YsFsMB<Uj3pVO_'Nn
%4-:;$N/gdX3_OF6TtP]6I5laYo1lrai%<?U+!eP9I-.'?e?Vhe3-OB8NGYpFPO6G)-b-%"Mjs<B(\hRH01R3R40o_bEk*-UBaV?H
%o'RNtnSEEAWXmgOJ*@ua9LcRSc.6SWBAPWoY;n%=Tdu2r&u7MkV-`i#Q+-C^Dl8hIMtE4[hjL+^dan*@=#uOlda\!9i!4>YCJkl&
%P^N4rb'2,'^Um))CQ8AiC0\]SB8[q*A($,Uq.PV9:4b]u%EY%]Lf+A<8U333B1iuH8/J)$';pLLRPO(P(`Q$c"?#%ROp?803TEjA
%Ts^#'D4hjqaQ(1$$14N%)CdH:$Q_ODP"KC@M,;qu))P4kaj<A6F6]sX!\$!Vh<p`/fB2<T2K*H(^=s3)2PmSGKBqT,qana";Mcug
%MF!7!QO;Ml*Pp8?A#kMI4pjun.e<6EQ\@QkMeb.jj'hMDhG.ZNNGr,Lq3*Xb'%Jrh-kk)n`>m4'r[r5JdYMDg_qq#/dZfG[o\uD3
%mjRu-MdbnaiTI[Art]>Ka<uHLVq.C7Da4'T"-Soe*u\Bt0+$Pl%H)LZ;46qli#76k/<^+C9nK\".O!_]_+006/CKY4H,l(\m%jnC
%:,-eqEEfPi_[]Jpirlt6J5QNRpAOE.^MfIn?Cplc8+,JSq\O"[\m)qfNg._G*g1bEo/H"^*n8i1'4ol7:4bLr?sP$!4AW4=iNC+O
%YfCZn=-(:P\u.D8H+B^C!jj6)l-6gX2AkfNj_g[=%VE?JCn&4]R<3E7i]6,YFr+LY3&o=qY'4fNb;?Atn_?^q3Hf;RQGsU3ps-TZ
%mb5pW]ebp$m)ZoQ?e8rdHEUZ2WN\;L:?ZtOQX8@=F@"6*P'Yui47D'hT=$>M*P%,lVrF[l4_";<B'3"sXa!VBnt$N/3S,uQfM`kZ
%8r`h5)dTX@!T3Zgn9D]'qP)-]mdO!8aas)\CA\USPrX6F>FECg<ouL&Sp102IEl/NGRLM)8%MDt'D0KTcQkN3"?]0DU!K]`/pJVQ
%Uq0+GI<A0u^Lm%batA%':f_/u8MWYRLu\X(G#a2WN.B$@r3D%EY8c^S[[ET9inIH6:.7-_.85;V;Z_hV`n;ebo3.)WA8\7e`0p:*
%-Is:+hTNm?Q^Y1)JLBq&U7ESJ'0EHo@RRhoLoK,<L74'XF.MU/(V@"M;k;.#9Jt5R\9D"UJ_mn0.tKI>c>0+I8PS?C9t-$\Ys/*_
%!('</cS\TCXTX4NDUmj.Wlt#\f2`7ag0RH4fS]TLB>s@dnHAUtdq[8]hqOE:/$$Uo&mq']+u=S1<"F>^>&=Mj$+=A6-2;q[C?r/t
%>rX2agmLs-9pe_e'=ak62Tj!t$J[MkS?>;lTs1(6XjPemMZ:bi1p7NQ9l:$'gEH9]Vcb&3]$ChG95X`N]1PY9=8lnK".:8/Kn=6P
%iKN#c<9Nm!R^c'$k3H5L:L_NgC#$^#>Mb+7fop9l$2,he&$aYCmQFjX[5sp$43?<=L<K.Np6+l%APU&,,t]S6QZ[j0,1&BdSUHS]
%9_j]b]Y3sQ[i3.H@QI2Q[0+Vg!o.cS7LDb+Mj0Un=GRJt6HiYY>tp*"+9@piD0H3Ac(T>'Q+2fk6bEefea0HV#GuA@&a@U%D',C]
%NGa]h,C/%aVf'E-nlM!`/ak$6E:PnYWH^r0[VIh8q>14D^Wu5u\c($grJ6,bP%s]Kc"q0;:WXECfn*np4&R\[9#="P,(7>@U-!J!
%oO94O?,r767WSFI:1j#2H&>I'V=n4n8QFWhCpqQ'p)g$1`\4Bo7RUA0rB/i7HBp#'2Ck=Ca#9t@[gT$R+f9kT0]uTgldF7XI#JH^
%9j!r=9i!;$j$4F6;O3oFN7peEW9l#u%ptKK1k@tH7DFX&aZ0cVK;Ae,l67DUgBH7S-pf$^(9@ii;$+A.Ij6'^9M-C_)R)2eG.u-6
%Ot]"PS*NI#2L=s);D.(a6[5#X;sm8_le_"._o48M``9Hbof^5BF1.Pj:t>cuN:K&$DP(pbX^#_tU#+>mhg0^V7TO;([9d(<Z.-Y9
%N0Ph:j3,*#NG3!;]rWbZ\=aEt_(OS^fhtHq1IdE,U';r8;*ej<&#RsUC6gU!8#h)t>J'Z-YX!k<5"H78#c,mF.`MNMa\f,USK@0e
%ZYl.<`;g-1O!o$Zi'djaY"mT2.cscN*ue`#oFVgl.Hbo_/+1r:HWYQ%\Zdr8^.)?\'-BCV[/:D0c$3j787l*`T$3gc>^nZ5:2XDu
%N6d]KHM1G+s)3+/9mXdQ?9D6+>$^CHo<ro$q5s0GIJ_]44fm8A'q9\[7&>c`>Q6nmCb0Y8gb+!.j0Qok,P)>fQLrkA^7:0[(8D?s
%k"EEeNSRN#<<6B;%:>R(5h1N+QUXPH1Zt:eb;3TcADCA6E&1/@V,rS%(T*q9)4^ncV\hNbq^\[R&U,U#@l6(;FG6rml,OiT>h7_X
%/<1jr#uri-;O=>]]k[]$kU/h[4Tm;7Ob&-lo8ipZjW@ie_q-Ok-bLM`Wc,8M3(JB>_igL@%G;T=?^e_sZ[?F%&Bt`[=[Nn?Uu3F#
%p>?*p[q<A'gsVWu3f$?lMFgUgH<Cdt7Js]bHb()(6]HM/nC)d7C=kZWQK,jE?-?$G3NCU\8>\\7l_<LBf"BOJ.TCJ_RLD';FVj)3
%)A"d0249;p`j=c='XOMo)Dk6bk%[jOj"N?j5ri0''f_,Kr/DqVA%0sDR]P_[S',e!Rj7;B^VHFU`6c!s:!4Lu3[_8;;e]1pZqe";
%eFFUN1e1TmEDgi\e`>Wl5!Lj93>>kCHN'?H_Ttg@/L`ni.m$7P[Aiet9M$k7Rs4$$DunZYNT8a$bPEbn^fI?UW-P&6:=C"m1%XLE
%bC$bA/@K\`3WEhh>H!Z:Ip7Zl:?P9A:poUk#!PoH'1ZVaeX?![Jb>7h@a8S5Tbe&V<s]*H_eLH61epMVG*?lIZ+<+0VItLH8HID1
%1sLY45*dP^VFGC]L[*;-Qd20spIo\4;km!SOjBO,T+Y0ck],^!ar;An9P/1i5u"L8lUr1@Y&h6&2m+TpY'aKL4GFnrr@1A8]\OfT
%&?1::,1juGcWa*-k).<rMYsE&Yo4G312h)L2].c2H=`(Vfj\A(-*s4HF.6N8Ebqo$!_#c7\nto3qm1-Gc*:t+n.1ad+"FN.G3_FL
%lU0c6Z8=^0@XYc!C:nHFFP,B23CW@NBfi0;Kt(cfideFQPI>:+]::e`e-`/EZ7%f6;0N_FD5)CS>+]q9^<2SEnne*tOZ7BP\T4+M
%&dr9QUq>58A#5A/Q-675RX&]0R;>gAa)Mc`f[A-c,:i"&#pr-[ll7fCU1Vq9_!=XqBa9e5)p+/[<a);uEsqC!\B%UYNh*jcjK(hq
%,,?DU%`80e2ssRRCRa&3XuZt^1-*uM?D8Mn[%gb_;U+Dn<jB6aLVu2\g(<R:X9kFGqAZi8@HTUR9_]U*<5b4["?3`3nCNrXc+fRN
%i8:e/mhG'HAH]]fQ35jZ+IFYP,92,j%r2e@G*&1B`m#oEdV:ejf749A?W-BP!ZU3XB]=S7Ceo?9H;,UI]#dG-.$\X[b42ci1Z--H
%XDN)9ZLtl7oNfAF?FdT&d&u]`Da3pC(<sQb/L-+>Y!p21qlKd6DIGXde[q%<H@'7Nl*J+/>Pog>D=i%rH>WJ;LJ;k."a")'7?;$q
%?'`ARA%%Y=43NrO^UMG^[Dr.mVHZInLp(-9n_JU`k%+"2r+H8kgVTp2)K?p.>Y3DON4=soM^ag(NjY6!/M7\:=/%5k2W+.,Mm#tP
%Ud?FD>.<'dX6H`6em&BG'b!W[[g2T/<,Rl?%HOfAC"rj,739`>&J'8]EK_?$:NEA<HA'oa<(.Tdb+6P.ZfaE0,Os&G&HnZ!"&=O?
%2a=9t_<Y<'e-n-N7T!+LqHfE:SL4Ibjt^fgetUGH%4>&o_@XK4m&&pGZubiERWFh)0=NADqI(!-H8O<@!^mUb@*u.o$^5Of-s2I7
%RF<U&P:)68%5V*&?>kg@);ah78aFRpfGtJ^3a=rIS)\%mR2qX!Z6NQHH;Zs^YX4<oRGVo+XKID\pKl^!hrW^#QXV+k)TPR=X#hp[
%"3&t![`f;X*jMJPV_GmXY^J.1ArS?E,?*0`/Sf`j7@(5^$qm^k%eoO*XYXOZ2YjU+mtFo1LUDE[TZ\NPefm?0AD(:#`$.bM2Cm@\
%BqStHm%l#aB#&.(A#ctb,o>.Pr]5OW`RB4RdsT-`I)XQ^`RB4RdsT-`I)XQ^`RB4RdsT-`I)XQ^`RB4RdsSF(YBZM>Gabg\Ra82-
%/"YlRT&qPpjpjMuBa7FT-_^f[9[e\H"&lO]W`e@s[`Fmg^5gmT[^<1d_Igg?.*>#!2N"jR9:KWgC2FoW)b&TPC93$!;<1H9M0=Wg
%@=8KiA<D1"ai&=AB4quA:67oN]JWZ0g36]9Ea'q8FQT&Zoo;knD1@\;5kWu0^#-.%G"42+Z>56R,ZZGQYk2e9)p4'IpH^GDZ97jt
%ZhCY(H$DW0`rfn(B`8IGqUl$_F,p.XAG_"GP[NM49ch1ZkSG:r@1>=N12G53mH<fYQ6(_0N>f77f%pJaZ!I+tNdkI1&DVIo`mX?A
%((dhT8\Y'^A+OD7Ngm?"dqne,6JgLIpVjPGF&/#l`HRUQ,-o>uOYq$mpik#]s0]j.^.).?7=2.RS93<368)Nr5)5D@[Y1Y$DcfFE
%s.VS[%]dN,MBA=Oi1.`#38YWq?<HW,-db(5Eq>7W0"ZsPHT-p+(H`mofV.![O<\!/o"\Q"r:U")as5+g$8n\TfubZu/`/"Z!&$4?
%A`p7@82Yh)0]uBXm\c;?>")o3VgK8Wmg`<hp^:t()VK<Dh%p?J\"@kf/!G<Ic>Baf>[FSCc!\.jF5!['>mSn<l<HNb6j2r.[%hp5
%TEO^M%'D&5icH%Zg"a(2"Rk+c]Y-UUmk7Z#lB^Cg(MQ>Zm\(4oAUt+m+r7BXTCu*?;j#;fpQrdt>dXm"f5A@I(5"Jnnp2/hLkgH=
%m_YR7qAH,G$X%N</cWL9+(&g]>3"3Q^O2$5Lb$1U=U]WC2Ds^#G^l%CiYB1PKePED0kp+R8Ukr*].;T[_Ab#b=R>gD+0G!Lf]79a
%ApI`/s4p9-$MrhJb-b%05,Vqo@fMLTk4AH:%>n+kk'Z&uIIDd>h+RaZ\\E]uVOm6q=Pr%U?iG7<bZYL$_?YJL*=f\?3*7O&$0O?'
%Y4p`10P,moaTlb@P2S&k4O_hB*#91\7Tu9l^:WiQfY>14Bg!iE3n/CeG51b/%JSc:Ye"o;A+*a7Rf33a*:XP+VPgK9oIJR96DbiJ
%d-5oPoIWCX#GVd@@GoXXQX%G.&%)1l=J%>6Ya_<*20++7YE)t4?6IlJ'`.sR%L)Qmha+V&EU?D@q\%jOhtse#\h=Za>NG[?IUAl.
%-T!sAnMn8.S$H$O[L780?JV:;[lF5p`8-U=@e[kn1&&jhY*QXNPa$6GeA#&\"3P&`&aSk*I!C2\f9$-,=Vc4&I$(T9rq":ai$t?5
%gMfZ)"3!1:"d7nS&J!Ee*+4G3QEn%AHl_UY^)q_Ml2bX)h@uOsGaSa1B48AU69l<RS?TXlI/a]H;3$QT=I$V;%J9n%BGih8NM'Cn
%;-:)M`g7uhb21b$X6Ae?Jj#dgNh:>(Q*DqT1SUA^kHacGmR;0_'fdncDRigi@"#Do3N"Y.JC`g7>O1t!pO.M4Or+,b02`ihj3C@W
%:R_kXj/k&/LLe+_VY<oKi*9'&0oUH_?uOSOf>dIDIG;='jtl=cKYL;;Z6d?e]??jmnV9De6;#D'j'=RJK>uL+FrUA=3Z@qFf/Z^t
%k>oR2otY`BWT&\[:SCX.Z/Buc?qo_0V*DEh%#ZVaBc<:5XFOOToJ')5MbHjFT05!XI?@ZhmPAInkKfY1W8P!o"Io5b$)(Pj8ULu3
%hu<9(gRl61p%H;(\%dF`A57qDs5idGTDjE@f(a1(pYUDr00asciPWk5D]_4?r_Ho#kskYpj+$RNh&LAOr;'3M++8pZGe)i!f71ei
%rGVJu?\.pKRrn7cRt(2\GOl8kin(qir9-_iae^(toO##X!4.d9bqgR>r%rO4Y0uQ%cPCZ>^YWG;5JoL\GQAQi?E!r.%JV6e^EX4o
%mD3@;8N"kFHbB*Qdk<`SRpSAb[cq[YnlU7iTEjuN\[0OFbr(3S;[ldj*H\:?fi5p5rGGGYIu<74gfA?t&k_$1H'L!^4PSFJg4BLb
%HcBs!Vm3V%%1[]3>!Sb9^<&7DeFW$!rB!aJ"RA(T:")6hm4]pB(V</'HIbedoM*d8Q;RA1VYsGXH+-LQUJ!b>V?_N*i8lEEOg6"s
%r3YS!J\=O>H8YjhkaSIH;lD<cRr2#39\YhSPqM`GRN?QaqJX*c]`.*afd<cNVn@G+V/Ji/4tY$G5qgoh]BBE%9&%)96$KR5kBH!9
%?DgTl!50\&-QW)Pb-mmWoJ$ij6Z0p((Fh2<4FN7100lSPpJ$&LIF56tSnmsqcgUL%/\3]c7%/!hF"DcEeY"S-2!VV@+8&1Rq.]LN
%D;RClRFo!D374'u)YI@1Dr)DbQ:kZ@FF9NcMf"q)oOS]2SnmsmdfK5i/DI&>;r:d82Eg9I-:U/MkaSH#c*b&;i,EG^\hi8s^2.9.
%F,dnJ)W[FRgSSZq7i.]SgDr*6b9f\AfBDOtI=#-_!S]M<TCuO!"$Y,!Rsrk.NP9,<5"39L[d!>sbr"1P#CY:DW95(I1Z6'M;i08>
%mS?2jFZ\3-^)Yd&cSbQnl+d&Ej%$is+VP"-V24pid5-.Nd1c>*b8^750Z+u1k;E+7in:b@Q:1-<qm%EF9.Cg'pG=2C7ee97GhS&_
%/+abnJM$[q=;^>5ooOuG0?_T7#-?_df)Y-jVkWaVlhI]>GDb/q9PEXTL`kR8;#%PPF"Tum*[ESTiJ,nbn>(<H`"[G>7*qaf@3k]I
%+]Qe$/J[!&5'.Y!-:12%Ssj_>(Agp2,3^9AX)5l8`T"sl\+J?tD?4iL(&"<+>_BC[13Wtl%3^#"$pNCAO,fic)F1IIqN`h`JVB'h
%\gpH;^L\fC;4p!OC;B`:Gbf,/FX&b/O$f*sops.Fi?P1WA<.hm\*3BneKIQ[3`IQBU.[8h20,>+dsSM!r\&*fnL9PEO"!a+.+c'/
%rDNjH1YkQgR+FeT[0=,_@?&$&IKV.3LoKm#UeboDIr-_I<bHI*5R$ZjA0#6,E/]?gNV!:!V&7C.L)k&]^gFD!jV*&89Vc"[;Pj%j
%62\Tg:HI4FIlt3m^$Ss`=!)LA%::=iHK(IuoqWa@"5RN)RI5LNPuCq_f1AbBn55"EqoH_8F-)sn?/Mp5n*$Em7p?,\25W[ca==,0
%GkX8__%A=)H:46ZX<5f5KF#m00pJJ%m+t<LE3\D)JO5]b:I!=aJIshEZfrJqO75s0$2.@7V`]ic"sK8)_i[U=3H-s%=]FQUW;q8.
%>u^"F@3V>smS,!K4X?gde&>Z-/OURrjIX3q6Bhdt.<QfN^^G/S"$Reu!bj%oPVDAsL%nE^F3@d7Ycg1V/#WUUZ4&c6$:P^Q.`B2U
%I@!q?Z`(gdbcATcZs[YmU-KoD*`KP:""Yk`RI9AXOasmD`q:Hh7-VKs*)Aphq\3stc]c;:X>;eU%O>oZ*_7RGG@`0fcVA4'0a#`J
%`e5M16>Z/N6;0h?b`R.Qd!M'ASRU.39$+BPBTe(:PSh+*g'8HjQr4td12HA0UME44@ru#eK3!7.7K./n<(@!u`C$gE4_@gq_&6[Q
%%.(<e^uOWdF6_ps^#2X#HI.Fi:K?Ur9gYYfl\Lq6dhsGm0kR'p4NNqX17\e<bau@+&E1!Y23D:aNUp*3*h#a0b/rX@Q\bqZ/BA,K
%V\m&`E-U[NSBRXbnG)itlZgTqB2tiD50P%*Co1Ek`$_"k;,HBWoZX$A*%^SGoU8#J&MGgM6l4RDW)ZI7coeV;PIB[10crBV_Q`)E
%4bb)7=Nn?:Tq37sq(rpJWfG&T*<tRAg@q7iqOGN2'P`Om"J&MJZrBi$[Y=(dkVU%ajF4A]WVK]-KgB-m$Db0okjY<]FODlE@ca1l
%<j.4EQ!!N032o**6PjfE)sdQspJo.L9U7F+K\8jcSUbo#&n)KBfE8<!+=7]d:`Fq;mA)*j0f0[$UY/Q7Ub>m7+JSSW@OON!Z&7ko
%kh\h9?+;(WhkL]++dUK/=@MFhbTBV1/Tf%M$Vk#e@hr=n>01710oM'J:URR717QeNED?<L?,j8':k1tUs+E6/dSme%L'7E(O7c<N
%Yp"A%GrqmRhAg?1H9S]NZN*K'Z=9P0=;kZq2rqQgM<UC9@Rg@@lkpTS'BrmNDWant5oAj"6"2kn<1$47NqfFUAP6eE.4ET1;$/6s
%GgLPu#&pX'E)EG4"E5[(3Yc,p.a5fG@0@F.7;dLkiGqWi0^(0D%i/"7]M!!6_o(nrWdqt`E8NROS77ZZ6&1g&ke;j)Z?Ba#6ml1B
%dOR+bS@h0$+V8KtA_94)1=7E&=$RQF&*^?EGrW&5&&b:@"DVm^Lic0j+(5$o<Z#N<R93r2/](U#b.t[!*A^jYGI8_s<JKUWbB\dj
%'6@!8#?;F7<U%?9PY4[ZO(HguZ4jqYWao1?9qqLGP'OAG4i\a?RAT-R)3'P/9jh<oLkq[2<X"DVCCO8S,Vk8oXs(%eaj&f>1D-KF
%:J=Jt5`(GQM_9V('C'r2=iMNnX)t[0%OXEK?ie-$.*i.U<J,:s+3;F\X([D47E!8f?O!?$KFs:f6l:ljVBHLnHPdI).Q,"LA<Bj'
%#at1_UD[mRQqpaM,..mD%Q$.Z/pT*B<:`818C`$Q4quV6-UQ'uG%/]k"AI-XMh`8;P-u@>Kn_&2^YA95*K6fG+?PAho/A.A(Dk&K
%!5b/h'u607LG3S1QTE:Png);WV<MRm3bB4=3p%KILWTK_b!u'#BaoGN_RhdG_?&$BD$*!r9V:KIFp_2A2ARXVq'k_hg9=T[GsLRF
%>`I]SpQbb@mBr,ALkPjTZhYQ:@o,2#GXb9p#qb5f\K4TcGj2Y7UsILNn+[)L\WNNB)OsmZX%-7!>$3P9[/m6-k\P^aLRY@_(rb9A
%4@p&q,40CtifWduCC!pGGAt"\_hJ%f4AG&9k4]Ug"cPeLiDFCCN6c8Y3fqfmP%u<H6lijhkIA%$8C;k&mNH1@XYAr6Hf\s%-D<5J
%-,pMKTE3!06g&;(5da>YC8S=<JMi5,PN(qOd,7oh=R_D`@YU`S69?06n4Y32*tq;TSV>5*T<Go8%1'VABG'_Ql.^-@lY:&NM\o.;
%V;>=_A+#5"?&:NX/fhOq)m%,^+)X3ucoFpUKU>(B9,V_8A,7;MfdS?I*=Yb8C&l4!$5!DPH"Ra:?g*DV?6Q2p!o8:Zhq6)%?'^Du
%6NZ0bV&-LAd6_Kh%1/9Q4gZc_kem+p78*pHh35rMrM(X8PA,P_+H$X?JaZP]AkrUMW&E-H[>(\Z4?Q:A+g)U5W0j[=-:^H8ZBGp%
%.D1ql"ed;,:4UU1_i=jHQ?4&d[lE1"fs][pc`iM)MNUMPK?<\h%LZEU%0\oiNN@u<p3P)BDcj7Z8J=9XndQ6ulVo/4FgELge@HR!
%I"hYHCL4*:ARinH1Qb(.X.d%\IIf9Y7@U@U0PVn>C[eiALfi&^4_hd#,3K(Ua]#%+7:S1*WBG/U%X.Q@WXW]q9$OII7hqu\WaRm<
%Plk0i4j2Y(3+a=C/<!-5PJe?kjMhB?I2E`'kT[Q=n%0-@bE[oYpDVH$:<n9g,&s?Ol\.kF60XaI73m(f$<Df-]c"B-MZg\IM.Ao!
%6Bg>(R,Qou@>Ng"cg,</@/#O\hqV=>`N7e'\63=Z.8!0Jc!;W?Z^\gq*M7_QKC=.!(E9>HXOfp<p6eJJ-50f.Lam-%-..W$dK_rS
%flIt(fYsVE7M(-1-ed,eR\Q?/#+N0\m(Vt]D(IHG#82&((lZKeknDNGq80P1A*)fC#8[&9=#mpA)7H3`<X:DkMrBK9_EoMD[ENFZ
%?)klg@l65&To0XF=/hn)jLL"q>.TYrj/!.k=#"%h4q--Lf2-VTI'$?sE7)bXC'5V<?Lm:j8nO(:N6](lLfoV?h"I@1F)I<&+QFFT
%5uT%<A/=k4qCmc`CpcU'MM[#l%LrF^An/6R#a(F'c$/U"]);&"<HbLN90mAKkQic9^`sYtc,Q^AluZf$WQB\;Z,SBt1rK:fb$-ND
%2Ho1p#DSE;)uU$k2/HW98\&,^\RM9cF54n]$>5'6:ZZ$O9VC'mhl,`/E29mt2'f)CajU!483AM:QR)BWTCDDjAs`f$L)G3"LnGPe
%'8)idEa&%geJsN2044N6e%Wu8dkX?8fi0bgOL63Qg:t9IiV9)UOA41ik$!(n*LPYn[#2#3=hlZR_fT:mkjO5kK6!<G"[[/*baqCI
%@Ccihb=Pj1C=T^Z`%7C3FMAVq6OU"e%:(M%_"Aqe>3(P$EJU4+'6P$HRD$*c5.lukU]8u05]r/h=Im:Id,*9n2M[*!E?*uU/CW1q
%]%@\)@!3p#-Bblnm?MOikk&nMfL+C^B/^"@HW&T9n2X@!M/b5s(Uhkec)\:p<`>c,XKd[(&Df6$naCS_eP6`^'62?'1M@U\BI"U@
%6=^0MC,MP>;RB,S&<A$PBYTFXRd>RCVSp66fKdJe+NHmnXG`LK#ilHB(i4JL<`F;P$4H`),c32o9V#ug3^5#NaV_g%m&S4\62\\&
%I:fR3_-qru@spnYWN7a:%l@b+TtEk^R%0)5>^1<BUK+;b3-:`@-3j2!TVSaNM#4X(P<#(I$LLh^ND'J(?m6V@@X7NZ:fIOX@nGd9
%g.TC<N'7;k33&R*b$F$0'\B<$JnW*I%P'"kH4F3/7nZc:<tZn_&<g&52pI3P^m2(:!V+s<cr]mKX#@1%a$`ljL/kfa_(B1XXf7&L
%B[gQM,Y)$>(E)!F"n2XL_*sSNKS*jA!g.)$N.X^05\rUoOUA9l&:,-t!f/RI:'%*C5Co1'j.R+/KJ/SO7Wqg0LSLFT\U*k0LaI[?
%''+J_a+)O4buZ93#;X+EdEf`qg1_;p@77DZLa2d3CDLRj8k<c!`Sj8]`hXC&f(*cW(?\i3jgRLuKa]UR1!h%po!sVHC0_OXZHR>4
%2_"GD=gbV.\[QhSNDkRKI7P.^&tW$e2aBcPW#f:%lUV*Cmpk#7L_mXYH:`6d=D>03p#NOEjXklGe$(o"'WP#S9uO_\_]E9MRM3b?
%.^1ti!bCj7TeP[W,(JZUC#Z3\\ueug!jM+)f9'm_8R(7`ZQ2P+KNN^@,(SF;R;uA/Rl=3jZKhghA35ekZ=<$;H3sn\%hn#*H7Q"M
%B0^u!Slh5LBQH0"M4JqUi8CBT#N_+.P3B20#O8OiP;8k#582)AV]Zt=?o'"fPZ4'Lc>_3k](/$!gl4aUXQ-'@()VhLn.$]N_?[VY
%ANZ#UYQq3\m.8+KRB"J74_Kcu-i@LcjVZWuSK*4%e!rBWe@:AcDm&*Cn<#<T_9Ca!LR@R)=C7:VPa$:B"h`:^d/8KhK>h_E_bLsV
%Sg-@FN7]N;?nsm&#9*5.?._>aIHB0_GKd23$.fC:!8gPQMqu&:3'OtgnVg.E]jp>%5kQ\Hl')oE=dpL6qkT\6W7k:9SEL]PiGlkG
%RSS4=R9@BcokfhWc+VB(a[r+o!RUrR$ctuDRSI<r*fWO*]F@a&a[LqCipO3_(S`_Q(G_KfYo42W+rb\g;Vd]beF1'f2JL,m;)=.L
%899Z@1XjaiAk&%5@=5_Sn^2)KAs-m:b6fS($2=sHqg'Z'[XjRr<^b%Ra(c9s"_!I.fWuE#Z`-]5oauHgd<eRujjqu9>[!bS4g7cd
%9r-GmD^X,QK+jENG42^r5TQG92XH<to`Gm@bY]t15H=Q\F-\Zl)puj5_gs0j-(0e'@rT<NTV2]Q%`FTCHua9XJ9pa[-D"cdhW?]p
%B>?9"!#=Z5Cm(<DCgG$?5[HM-(LEUe[,YuT.?&s%#"d=?WBu6gL@Gle*KGgR,"\V@fL9CRZ%*P7TMhltlian!0p=:32X#+]M#c;B
%iFmUT6$E]-%6!Do+lNdrdHig%:T4jO^cG]!.#eBp#2SGhSsr=:&#$&d;k,n?=3;R/21P;o;8;r+%rQjps-Qsq+,Y/DeB5h$Ka*@$
%%Zs7ANMG)5nq\(PPC(eG0=[]Y/OZ:p,QPb#iG[-a,=*dt?t:3$k6cfsS$9':]EC(u23b0IM2M_+13E^1FY@Y<6C7Ai+o"]GNnalX
%b6TR)2J75W#-`VX7K"uU9^IWO+\>D);A-H`cigi;aeC4[p22>ld)h"gGUjOpd52o5^pV\?l\ibBi2ahJ)D2\^9BXA8Mu_g?E__g4
%V@(Hu'naVi=c2AJrA?l0H#]B_[?5mf5&!!;XqS^>aao>2]O[bLW9=NP'41rq[b7Wbq^`V[,ikHQpO$P#*`!U(Ur1O_`JDEl(5'Kb
%+i`n`&)0XLLF3HH1-+%DhT!47lbkmkNkr]e2\F!A;dW>f[^4>.(R(ro*4q&aDSk?jEHr\>QRsG!*,hu1U]YT=fpE(L=rH4NGgX[H
%%q3-,>7FGdE!U4gd8W3Z(bib^+t%X6VO0r6,SeN4&p"lH(,<0n6W/P.FQSJ^"tm=rQNrY<T,(S>[i.;s`f\f/N>W2V7<'0L(dL8H
%Q1,&KA)gD!)B6b2n65(Dc\p*s3X0-A.eE.?*6F&=7=CXn1c00p.2t8'7QN)FF#:`;F^PBmkVgI@mSQ@G@]32L!W16-=O7H^d`i?_
%B58a+78Xml$1(I'LSOEZp.Pik4da/%X:Gq-JL?t5luE[[D9scS2T;uf!.j'koGUod&e!YKU,VVd));6FV^o?W5sgf$>QX^-O^1+p
%1^-t$[LaAZSPJ2'E)2+F+dr(l,QUUFU/!KI\$dP$Zc`-bh9)YMA=`@!&_jfo;Xn74_^Go+WNk3Y5*,#:74Yb@o"l':cI`M-XE^]p
%:WASE2]_Iji<%;j1bh?:FC?)`%_5u;,ccq^aU+QV2-H[@(>P'Ylq=Sf=^952N38?rf^RY*`gVktCFsEnr5QmW?t]3*_F400;%0k3
%js29D3mLH'o$'f:Um_aaSlG]VF>(l+a5*I4+XRsZrHcJV>9AG"NPqNH+AAdC,1"'K,/bW9%_H:>/fH.@Ikb(=dEb3"nk#HG<RpnK
%S8o2JI_Za8jBc5*b)Sbns/=:f3sGj)hTZL\mMcB(<5RThr`+BV0h.^j0[t-).e4EN";3uH=][JYN6&mNo"hJRXL;,o"H1mL4Q\-,
%V-o@(R'Y5s`$3*O")<C#?At]<ZSAGE2Rgep%oS7_/-LQQXg>##!8u[u;.en'$?CenEe]/b!kUA8^EHE^g;\0IAt;^+C^<`uHNH;t
%Glk$(Bh@g3.(g=-oG#Q;76mYF"t$4E2&+!1RDe5)c<5o\Nt0()lqG6,kZq^<%!)AIqKRqNfM)p3aq6=P:4YN5KMfc4.Q2q"4@q"O
%33h/$hN\\ol;Q[aFZt]oWcG&=:a2<PfMg?2.kV-&TZ)._`K2,!)^`ZaS`SSF6L,PlL*MQ@cjVq&5t)L)?uG;Ua$Le!Ho,N)_E_;(
%\ZHNlJSI-EL7`$"[<_YH!?69j%,<&P12G;iYRW.pZUA6E/egGce7t!lbMhs_B#A]VnQ2GDi]&hJU1b0`cn3Jb'%r,o"7Rm%kcBKW
%>4%%J`WIon=t%Z'*-R$pD"P3=VDnN=;CGM)4>"uB$$H`UWm0Hu6@YkSh67MKnUqW-fpG(,267Iri>+l%(kY)<OPU^4a=^1bIpuJB
%SuoXneGYCc[#h\M_UHAWEUq\9el3K_'dBZ5*p;T1@]1(0h'T!,dZ'kJ]m3&HdX!T.nKHj^[1<%`I'M>EQ&&/OM]tH.#a8C[5X[C:
%pB2RpAH#)7&@/KP9':Y]egj_?5%=B5,C%P+p>-)hA>tlN.0VjB\lYUq'1jm1oHWjG,47Q^-DQfq'Y$[0K[F9\U.(2)`TW"-o2SU3
%:IJjb&dHAV@lDsrQ/K_`-9XYaJJ6?X3R43F?#8=bpGrOR6><qN4BLnF1slIG$&>MD[Et*_nA_P4'C]:"2,&?T.CZPFqb66#W^2SS
%:SZNZEL;PV'=pUWip8';;@oqB1lu]29[X.\d0tBGA'Y2PX`,u51SkIcBt7QtJlo>\`Db!5XtlCe@fd(]6q,*m'9U)4]].<G-TqKV
%n7")(6*M-:8ZZUO9mNIGPtqL6:Q8+O@,@;?&BIVmM5K\mG'C/@('P=EcG2RdHb@cY1]UU':b=.(p=6NDj^h=W@/o)Pa._P*)t'&%
%K;H;bkA)TjFJ;q&B\/c5a#0&"dAtE+9-*_gK34Ni2AJ90X48p!ZI#5Gl%r>=#X.Kkg<dr=:3<q(=,n_An3Jsg/dAjUjbYSD/sofV
%qPGVg<S9"?(/*t?oYA'@TaBdR,U,pQ.eJ^Q6nutJW0NTp-_$\5IFFD*R&"A@:=MkIRcuu7?Hc/KShfIdC5/8REe^PAOF5^S7-X$/
%FMXP3Zh7]3JJF^V+6Y=nFr'eG:2mXUX,YQu<UGa%dPj<\%"5O-%uee?dBd!p-n6Tf>pf]qbcKMQ;4fCt2'36'=Z3fBPYUp@UW4;N
%?1c8__OGkSd4nI:d(9@7Q?3hgf`p+eWgJiOhj'"Q$POga6YioITo%N>=`[HI3(54;fk7AdZ.["XRcH*Se-qk&6^(S/GN@`j<pbhH
%8*>P,YU\s6bZCjZ*J+&GlU9s+FX#L!SgelKW9tB@%4%BP_P1,K'F`[a:j>=m;6dK+K35l4S&L^O%m$0e7l!%,0fO\9P9G_k:bF5^
%epB<6Bap#Y$o@$Scn-R]_tX>-"*[:"Z=1T5'P>:`Z)I=43><>$NoC9_Mh0Mq:Q1)ThonZ-:cb,7BNcA.!HK_bDk]n9`5Ba?Wdor'
%)T:+/Y*iN:AU9]%5#q;73_%"3Yn8Z**FsWKg`h=i6_#n0K>,Q#K0`SDD_oopki@N'9FQ#$hasI+WQ]dndmIUR[a?O7K'3u9LWV&3
%.79A^#pK^g2IOnG,tp!J2/$.e0ReS(n^lt1,&D\%k=k)G`"<amUS)uO$j'DGJE*#5"p*jK=U9_qClq%'ia$Z-B>euRiqF.@N/`]&
%Q6Q24dnBePC7B7+n=o\-AmI+UWk$(!cKk+Q35dhJToT>*%?k-JF+OE:Ou&+-%o*mP>muP:="RI=0WrkZ:)pC87b=VR_aN!V"lr-!
%QN_^-1(R[PodN$YqPm!6/G`*PN?/E3Tc6.[.!.6WG'4YE4;oK;lqBgK+\_<9$1'!;J=2iSC7jm-$MSQ,(M2prPal..,AOD\.PpXZ
%&j8dSOarS5bYXf*kFKU8huX<3aPF+m5S'dRmIQa6\%)F)8G73NOsSIkCL,qX:hmGoRqO]oPR>T4IQL3#J<iY@Q!U=e"-LLdd0_+L
%Tu#LBJ7sV3&/a,?LeH&=gP,E3fsmud2plE9BH*Q[ko0\rFNheQA862f`N_!k;O()P!Ck.G$mYYq$fTFZ9GMso4Wsn`)rZ+1'"159
%3n"DbCjpN>fSA^uC5bmoA8*?:"iEii<C05`1,KdN`UO:/Bo7I](n/86WaH]^(A);G?p21"ae^&VPQqh>K_@W%mt9HV1QR-<P4jQl
%=3=/(1KX)</sLae:&Ka/DfR.^,R\.sC^c*$F#&d>jm:1qD^X,\/&N/,:US=M8)S&DlU]r!(rC]\AAX%Aj)?;-L9V^h%H[1?f88oT
%4G'eA#*b>81)ueb$`,ss%%rE]bR9`6*FHt04uNo0CO"L0N#%FpXpU\5jC1QI#XshO"q#uNO<'%#RAO`b%,Bp]F?#<n6sR#;1F;7k
%FHA;.)b;3:XbH#aJ5O0<EYl[ILC8Wb.hKL]@Pe#'2'a6:M8'XNA:X2"B^LlmUG?k?"A[gnEtJjRehciK+;hO7%!;%[,U>9"6gt6g
%E/\+uoI),oE=<>4=()>3QWP16:H"<=d*[92lo5GtJ@fDW#$`*uA#U_\O*"I?bVC,K%#iXRbq<TU.0b^)G\,;.RjOLQRWte>m*F6c
%ohBDc`K,s-Ss*0ZSi?`b'.4?2N5sqek\[MALs_1pD!!!j#!#<:!)Pn3?O?mbpoI*j@@LB$i?pIl0!UIl%pAS>A^3QX_D,Z!0ZOS2
%5\%0h`CC,8\dD"qDTRHBR'S3dR!;5J7D]-e^?"W9R7'D4dF6O%83mq2B(&!13*BdLk!:7\*Hb$8fb!"j:'27BEpa$U62:ebDjF',
%A=9SECY([P(=,IENk)&A=:\-J/4:pP!bDoM&=be99?n=k*:ih7GQT%CK_mQM_r,0&1;"`o_?Jt(DX'S)i?t%\KHX<r#NC#e;LnDj
%A`:$Bj:%-)f.c1*gJ21iZl4O0P*C,EM%DrKoPc_"0!W<)DAJOF\jUhS)Undo3BC#YAsa]H46Ob+m+=%D=lU:/S\%FW'N!WHo7Y=m
%m0jZZ#rPUT3icq#'Z8Vj7R=%H)_#@1R]kEb:/M52g_/.6b^,:=e;UXr%G2PQoJ*a#gM<7<VD;59>q'K5lARI;*]2-gm@*^@Z6'IT
%%:*CXNH#WFcIl1d\1^E/noi-Pi[KYD-CIbf6tUFh%8D7GX<r`Kb,oB\H&X^'\ZScL\!4R)>SbJhZ,g-O3,[faKf&04!\(Y'`bFt_
%>Bl0bU%%B@VupZU_9Vn-VZDf'ZVs9;^JIA1KeDMPU<-E0Re4uCVqLqmY+fr-a@O\Cg%<?6mT_=k$tEtu_Pp'_1m;.J<\j\-#+l:c
%]T5I/QA_2PY$*$iA3^3:m^uBfJb4L5&!Gra=hPWM8!%;:.3A_);<Mmt8gUJ3NM'O79J^LA(6g6P=%WtM3_*3/GpJEpC-[9UJd>%W
%jCs&u1-Um5p1[,s6\uL,kM<)*("X6jnn@eBZI@5Q8oHu<&:CYMCJ2TE=InV=1iGB0,s-RHUWZp)i)1GuTS[\;0!oYh?e=Y12/2Pe
%d2Wk-FK.`+2N)n#1cJR$pG_%jR"A3U#+.g#ib^)V5cGE&9S_.`,XoRu)8TW9MNJK/r1_I\"nC5Va"P_?2P:S<A8HD=m,)^1O;5Wl
%#0TurFc:i=7\sQ*6Bqs7.%_dlEhiQL^NhBE9J<j(bl260$>27mUV!^Q+_HIaiI3<>WCGKG/n4^\K\[fLZoIuq"@fHT3K"oMZ8d]E
%]2-]Po.X6hXRJt3p]a)<,UEM(=XrRKabp)67"(V)$e5i2OniN!+&_K8mO.J\n@*J38C)n]@kS!52M"/p]rTID<(hW&P32V?c#W>r
%_;.q/MCWBC#`pq&(PYVVSnW+/a_,=&akt<1\Xrnn_ZUpUNhp%brD36>%!NiIOYLIW)F,Ejg\%E-'PdtKjfGR>Ro,`s\7M["8oaZ@
%DB'.UjBZ5D=ir[?*_4K:P/IVlj#;Mb>uIS/"q*WTRtThSk)rOtb2ZZ*$Bh48kh_[(#]@:Yi@n+bGS4VVIE@C`_7hC*8WuC^B4m<F
%'o0`%DZi8ql=7W;cV[g.7i1)NQfJ/5GS+R-7X)9`*N&>(iE_J$aX"InXb5R$S@QSbh83mLC9n48DlHMJEKK55M%TfjJ2u;)_QfZm
%!9)7D:=Ee3-84DRDd*@hZXq-qcZ[[o/!Q8e,Dl96LIc^`U'<i?$bL&*@o<fPVWkO"a/a^]Ub,L<M5B*n7?"C3<gqVA]-kE_"&ubs
%o?!0.iCe@=@B(b*fQSG.ee(J^h,,/Y7C#c6Y_j)#UYf"6ntE*.Bd!*TP/&>i1&-TCY9Pbf=G_n`hE5J7JU4fO-VGlL'ITqf97%\%
%Y"?'>-3d>57H9LX-.#jYWjYii*#+U7Q0<f\_#1;Y1h(V5a\eOQEJYBrqs71FI?puS$Vr9"j6'*dpk4iY=%W!5i#u,=H\5s)n82D$
%Ai-)R3S>gI=>b<iRk:1a-$'d_;H1[ZAW'WR?')sL',E2hWVLY4`R1)ed>5uk=KH'e*7r>$F`A0\A@Z^mP`V:'`1[Eank*h)*1\OE
%\7jQ`1*ncG*7gMPNg#Q3V(\=pe3BB[i8(Et6!lNI/Of4fqQf?[rM/V[a2P(C6!lOt2#[AQqQf?[rQh>TS[iSAHpM*tdIf?18tVR/
%X+)tng![HGHI/Q:R,tc"-[j\?VZ+t".>3u(@GWDhhLZs>L-IM;btD<Jk1ZU]3*7Jl`!k`%oV,1E@HekU6u8VghJ,Mq$Ic$[10%%4
%N't)'L_D0ZUA6Wg^<cdE=4PbbEC=,OX(^kP5]K1)RP!BD*(5MB(cGr*afd^OU6WId:cm_Iah7,<SnmsqcgTFbAPW6;Bj-JIS#WX?
%WLcgc#CY&XAkoW.""/D*%;jS427h;DBmS,b9hUr=:"/>$MeUqa27h;DBmS,b9m_oNgbeCYdWEsVFZS'ZBmS,b9flBHQ;-rT*#[86
%M/2`\c_fqms4t8C2TOo3kb3_;K-AqFOf:sY-+!qKdZHG2BJG'/C-Gp;EHDE`[s@,nG!Sre'GO1M2s<mE2sRqC+8&1RBWC9AgN6no
%rhepG0C30g80/7M6qu<H^$mBS]fRm(STih_Z(6HoE2`CFANPN>QOoSN'd4^E$\rg:P`OC7k9WW(*$L5l/4$M[E+f\AR00d8M9[GC
%(=sig$9"6>FH;S@m.U63AXK6O&ptsaP[G0<Yc)?q&ks*Cq-G[#;Uc\+D,&4B-,DE^'Z%Et7\OqMC>49@'N^Oe1gZ.-N8/K^Q#HMH
%g?r"Q==h_K1C;Q"kj)MG(Q6fq#JPhnok^)"CpLLHA*[^#>LR+4AU5$N;DHL62.u7)ai\gHS^s?%S_cKf;bsRD7o)%60-"5-SQFI/
%d&j<SSbSTY+sFn["_#A>-*G(J0foZNrNtr#d5RU9/T*V;3OeaM>f4\qrRi%`._qLpIS@#>7+CN8d)O5\(V78aZu%1E^?O]lERW$c
%f0)o"PopjiiEXSn/SSY@dH/!oQRS_o^%6-o<Cb:A\BD>"\MKnjlGah<_dX;GJ4,0$PLR1/9i?tIYc%VpX!9dlLd8+Z[j=(h7EOg`
%(es$ECTg.;>Ca7j)MVj\U0sRh+k)itSKp+eaee2ZVlCUt&HH7b:3)#l8'E>Lf6L+qMHgk/O2#rkhB#:g73=>H=]/l[MB&3D9g<(L
%T4I)d,:bDCeOrl=D6^.`WV_bcAEJCf)]@[C"E$Up<<[AP;=m9h0pH@j&g-,LNF#A7O[t?e>c>Ki5kE7#<VV>0V\[S.,.U24h(-C`
%d^X1f.JeiQpZRfV8R90c;H]YcUj0_Kp_$TJn3!'$;BLI>\Gdt/SIjE3`SsiVgK<LK>*`CHA+2F(g^tR(M?CaEQnFI7>S*KjRPkHP
%COAW;(T\>#2iZk=3R(W#nQXKgBJ@[ajKbV,c)_cma%gi1e(&8u\9ns6\1fC'3+1`e*Ri2mX:/+XGS6%[<^V"0&S]<2Iq&]cqpjrr
%-a.s!faCZ0i%b1Ck.J34Ohq.JF).p^'*KN^!dJR^s*V^,?>T9b7WR));<fOpPO-^=AX)gBL!5A\QoNFq_QZ!iE83-,D/jtp:\<Pt
%^bYJ;En.^o)7(oWk?$*mTZ#iZ%(Q*8,t5A`3%6:0Y">[_Q(-J>^9hVP2qq8]Y%]J'dCh9eYtq*@@1;@S:,g^"4^QIf_E3GF7#ssi
%6#+U]ej=u^A:82dFR-SIZY5!"8`cq!:D!(R8QAL1`@AKFE_kt_KUF2=1"auhT.bBF[Q)U;*$6$N>O=a[7"-fkbr:r&Kugg$OXN53
%Grd5np,p6P>mWF,Y/RplWNT8G2E_[S4kd/4+DA^HV.UItcfdq57_Bac7JS065f2^b4X7W#@$Z*tP:Q9<;ci0,'`&KATj`$h\0AEu
%9lkrM;RY3c]m*puXGBg.CQdt2G,;l\.SP48=,T4eUNF%0Ve_/42O5^<W[SnVM:h0P08l02(B72InM4s=MnjB;ePN$Ab(Q60X!^":
%ZD(JGIj_deLe)`+=Go?ohW<FUGI5uB@%S"Q8pt8hG1$3,q)0<A#/+iC&I2!OEAgdf["cT""I?LFM7tZgDL4-_;Ic.2ILp=aDb=40
%)@/IQ`Rdp+HSQ2m+O1eI:XRCS04;Z@0rih#b'A*5L04(B=5EXulX@(ES-ss*`\jX+24HP:PES7,ii&c/+XR1u9FM[J$j&bS>-nZ8
%B)4WKUF4[WCMPs0.GJ-?e*u'[4QM:dg.Ct#A-%fJEe.`!d5d8S\Xp0_/8-eq-<WRg`I!<WUAgD!l*Kd3kdKU7=tSR/=d79H;Y[O3
%%-[,*r;n!pifE2*IW1EZ(4_V"0^/*FDHaEmTU/`#bfVf/QE#J<KrB0EMKj-!7LNt$)J'1WXT-bXjuKefe)K.#8[OKH#E8>ac7"E!
%M9`*b"8&b]T!U:oK+\4c8kT9VO[@Klc/Q^[js4IpM_`*#&BP,]fTK^XDFl0A'>Y/363_,5-"@'k["Dc[$EKBn-6js\"Y(kB;QbHV
%C`6>TPJjA8WGJSGoB+[o"HA7nWuI;&F!&b)iU)6*&S%kC@89'5W\t1S-#cHj^?hgV9j`E2YmG$]I;k(dTnnms5$$:2e*H>A%`47@
%c;t;c'AukYR%*MdKVQg)U0DHh'U;$'ZErt15pm7p\ji5M_pf6MN&mZ;.2#%$Hn4B8'g.$*GKAHHI0dZ,T[_`a*.qbRJ6VA`._T;t
%1&h*)%beF=PAiTWoM%JFS>hkV3cYS'E<Te49F*rZN8@8s8J/6:Y60&8L:n<r?9oeu_IJ584$!=&'9cm.-QnRaEg>Zk6?hfT!JHUs
%L6Z1."L!h\]'mNP[\WR>n3k`Z?!EN!N.ae`.6+]L-[&8p`l/6Z#p:^!,mH_T$(1RG^k`[^9'<HrE6;n@Dc*#oROa8g$p`Buah0>,
%;^[5GTg[`c4`dJ+>d5_kjV55CEuMCE>UYVef5NDXcU;+q_pRI$](BMeafMr_G#?AXP6PafH7:VIJ>r6]p)<X2S?8WM2J#1H4#=?j
%ND;VSSW'(EB77eMW0nu6<=Sd[DLL#UNP7F#7`O\(dtt3BS#-F=f`Z-*]^[=)]k^\P@H0iEK&/$bHq$U5n*&5c%W^J,k#[dEk^/(,
%\)1NKIWKo:h7*FToB.2Z]R9`[W5k7>J+[tVs8K4Y^4$"CjYo-g#ER]c!>/5Erfc26ibn[RU[m_8j1kRPr:[<ZqpS9Nq8plGr0+bg
%#!V\F?U2(5S8D'n%K.r%?MMWR$qH(aB]NeVEq5`=lIfF]/oak[G'*1cn,ii79MFRs`sp+H#S\\sQQ;qD!="EHs,)+)V?6DrrPW&^
%kPk<<oqro6&<mW;]!<E2]3d[umO(&h^R7V.9Hpos$5*pY*ooS&Cn*_(%088*EQnVNAg/9*r,Bqb/Nb7p)9*<QNWW\Y?>"@AF)qTs
%#kDq$O\24`UQ1.PBr'9EhH$f4Mmo*)_8D`*+Q_0?VmPF4@d]@^baec4>R?r7Mg6R$I2jgc)7RKR^=>p6's:\&^dKPeJY,_D`ks+'
%)oKF$O>-\f=&<BD_sGc96$17@-:HX47&7G?UtXbg8J5T0\cgs4MaAf85<V]E5(74'_kJL\7et0#&<IOV:l>`<A`I9hdj]hO(N4Iu
%S!Y\h:EBC8e-do:$!s'Yi:bH,_EpIcHH3ZA4@ZI9M2k(6Y8MAQ!YL!m?DkD+#;J%qlEp8$_gu9\XVlP.\H[bD!A2krCoL"6KOd2J
%+4*k1gS>$fY.CWC,\n8c.?k0Q2u$Xqe--^:genD2Q!.CWLIj6E@e!#rK!NI#(Hc09GXDU:'nPnCgoiH1\aeft"$B6KA8g8ne/+kO
%@G91T&P4ZW+nlC"^$@orp5Xu?ggD>o=Wn+[6"6I$+Qs>SN_kss(..l@MIfYKLo'AHCNrLM]J:BuR#%.qXCg(/F^hjnLneQfDOD/<
%nR1X>^cStk'k/^oStq;p3q?/tV5iSo.ScS1K\(R/IGP1/_0fbbABQ?)2$)[X67h5i9tLAo<:(fG-Bu+)4o#BQ9F;bJn4(NZR1RgB
%-'=#W'VZ1s>%jj>&CJ^(5Y&)kGG,6JWFpN-npJ:.&"%%$i2i_BUQ?oK>-8U7B7u]Sh6SbfM$-3.LNCtJd=F^(PU0Hc5E'&/5O9\+
%#X(to;tunT4sV?/N'XRCp&]N$O0OYb@>U.ZHL"#a'(ZM'c8hhRNgX25+;=[;a8m/='*IMEg^.;jgI\^8bE=<1qc@n91>_t)q_-t9
%?`;^^I)/Ku=c#R.iVE>oPL_uQ%&RNt$mD?Gau/[Wh0*5./P1+V0*pSeP*Dm!*&Gi?ij<dQPYR)2k]u_TF!^H/&Z^ukB"-pMB]Z&X
%ZosV'p6=ot>eR6oQ\9(W.@>rsGG<7`lW?(+C*Yj:5^G?PYtT6cL:TbE1NCq2K@]D:1*GnTcYJB?g3Yi,D+n+HG]V06NT@t*CRgE@
%Z4JdP*YVt$M\'CL784;ZWc[7;7*JXQp:Lqp>o7VqGT`^7JJXMD9&q*uU=1uKc]h0J"%6c92\A@>O@k>(0TjQr6o9iOdp&,nq/AeA
%AWA%)bm@[VU:!&0_R[)<KO41d4R8GnYgN.J<Dk=\6&fJcid`3q:@\'`6Xf3f,m[jZI7%X5cjj!C'-[oPS6iAFQ:R/u5fU,$nKk.R
%KG_7Sa>\[BF@%B?+:ik`[[&icoh=O&aX(=G.A4u[`ouW`ApUhaLu.fMQ?g"RYJeR&DHl^HDU]Es'>!/kD):p`-Y6\oQd>dTMUp1?
%\?'pe>,<s[Q!>>JN8D6'EQY:T`6**nN[e6#1T*<qJ]oKY'L4E4L.X4FJg0"EH8]u9*!siVUC%mj:Sb8K4kg[k&_O_F"'[S7l(a#*
%F@CPdC2pJP_AS8g&!1)IG`a&:@Q`\+AKl/\3,KdIJ?A#+Ja?RWMWDJbU3G:;R.[-C'O^]-ak'XWoZT2WRC>[8Y`F*!)kALU92C@W
%QQ("d8G()j0?ds1'1#'*R&=+:!Bq+[4rr[\,h,c3WE-2[n:&Z:UZ(:Id#uPHf_nbO+\V'.O7T*iS!c*egaSB&Lq-]Kf*$D4F<gM\
%/]LK&f;/DC'PJDZj'488TT*DK1BmNSa?>5FeEp+4]0IQOUZJ2+g4AHJnIHL(=P0O[!fM2$;CW8UHcd*;l[Z$-aV;`j7*hq\2i^WJ
%,"qr]2J(h$(6LpP:OArrHWc?Y,S5>_<1.lPrt$oA=G#IZ?Ui:MiY`e11Eh?k2>J#%o""rk)^"5p?MWlIB0,:a&%Gs`FNHpYRpFX5
%e#"N^)-<=t`Qa-m@\7e*b1<P%kr1aq1)cGD[/%;8I:=["bT[8toS&,!Dn@lWStl%GQFaaGHAZ7d;Ect6AQH']PK?G4X3ulc$Sm+/
%(L`Y5Q7W8%1,1\QmjfL>6KHT4CE4?4)2uiC?.I'8Cn$!-4dek"+Zook:GkPfPf2j;`2K[R=o=?oCk&,WmVH"/bJHQ699LZYLp9!L
%N67_Hf2>>['gmjWc%J`aj+8q=+<mLHR+R&qir(<O5&IA*$5=jK_'5:LA:Y1MK<Bjn;F@q,JG(ET*?!^(T;V$IUs-%bJtAcs3^deh
%=:YJR_K-5UC<*tOh>RB?I9CK!a-Gf!!WND=)!E$.eZgd0-],8N*mb9GGE2r\JO$JB(>E(N/='l?YFg0-mmJFfW`s>*1R$,SN.sBN
%N#V)>Z[PD)URH)q,`!k:23_[BOQ'8f`OT'S1soTl4D8gN>s^n$Ur`^-O0sA[.;LLJ.O_K$[WBJK;t<%^(IeoTHm*TopB]DA01m.A
%7e7S\h5_L@f&hU";Aho0QA?2OMQ']TWk&I!W8&Hu782!+Dcfa-ljC"0#f&_VVX%C.%?5e>3,AA<4W;3L"gV*fP\Cc!b-fXi#tZ$`
%Vo4J40pJG'/U]$W?Y_*`j(qU]C\?mWg!OlqZI)d5SD:4fA/asbPB.Si;a`qfJpMF#XJhGuls[G6Z*4pBSu(4o#`pVdO_%^['a\nI
%aK:q:Bu1`T[HONGn.fm;CH=;;J3<Y4EVgp1GX7=:EY>]\WJ5FOiqPLq=2e35$j/4G&&_n*ig$H$PDC6;nQ;)W]#mNJ4j2QbZqmGF
%-@\2rRZ0?4..D&aW#0Y6""9#-4[K.HpA3]l!O,R5">,>&-i9IBD]#;V[],Tbr=111%cs+\;1,$1?l\/I&PJL_h9tqDL^n;,U8rir
%3%gfJ,4Ps8BS<6NNr9^P,R,%SSP.:<gn_hoEm_n+\Fb>6Lf^-pf`dEaXD:DNH-,*G<imW#UHP2_bc^8s-fbV]9U`\9ca0T'-<dhq
%M1LZu)1u""a?HBR%\8Kdfu>_I:)rMo`*uLa</i.,.YO2S1^aRu;5#(Bb%a0dVAZV$D!2[En.RI4.L8Jf&^s1$^1c4i&riN?^<Z1P
%C?)GaGgf9<78m$*jQTNo%Tt!1J6,.:dYGMK-pk2n/G'[U;K>'(N7Q;".]6;:*Yn.:=<oa8:Q*\qPLD6Z'inf[IiFD8KL!]RHtaN$
%7+^sX1)/0Y1DfS'@b0PZ>LnD0U)>0n01P0A(a[duf>]Fp(b0hKH9LrJ@B#iE:b0k(&">ot"u=?_Ygo]%W9Tou1YakOb5m0C't^_U
%.aP*RCK@2uH'M>j"-4P4b?RNeE&"6QVujJ>j@Ec8I??u=Q&A]WF,L`t17P`T\M?^:(q*Gr."]^Lak<_rdWk#p6^t-XW_0'MHOR_3
%.Yu#"_pl^&F7qH>=lg6)<k^gYTicb=#a0p*L!`1@jgfKS`)3?8:&Y4ACBl#[8PnR-3MBC$PnG$:_:18RXJ,uDgR(?-H\I\7P[Weq
%RAg:pHoIF,4T`pZMPL;fLX+rHl=H+(FVSu:!<^!&#SOT,AlirW<?`./L1TEo-ml53DnOo+)5,KaDL9o*#j@o7dW0_M$sAQ^=k!F7
%:#bu%A1tt5>""ZS=i1E<.SCgH.RO'OU*g[,.=PWr^G7l7W!DZWSh+AOMC3,Y>'brWgTY#k17e3H.KjUFHeW3O?O`236(XO`)5<WA
%L3&)_>"-,t4Hi`-N^Jc#"':+i1K&]s5$!lL6\T0jFf796]RYZJlLYR5[HEX8b[H(5f"*Jcaqo`%PpZe@7qg!ke'4+`Rm+\,C(ISH
%"S7uS_cp7()a\I!GOrf>-AAJSR:1[pMk:=cPT\G]m>n5bSGCZVB`&u'YrW:fX^,2/\DmWaU,\PH&e:rY=ch;nesFpF-[GI."On],
%<-sP/M/1Jg<1YuEmM'aQP20M>PgL2<kUs0;c$m"^'+:QZs)/PaJcp`pN?o@OK/7r7$K>JAh[Y&(TfF?PUer(cod+^<CsgWaZ%n,)
%-]c8Td^/Oa0r5>30d0[XB"'4M69e.VJ9dhMO`_Lr!PUZMR`@R\?bR>h-7MP&U9"YfCW/0j2)L?J4#%)IbCoa?,3!.-(:AFfBDZ?g
%Rgd/Y\F@hPdFAh07TPo'7JA(=gheBq++[HjN\hF"UJh@Lb#;Z#5ic+R5S4@`dmRZ=CKpnAjGlOiZ8s'&2._qr/btp,i9)O35/R&'
%N>j&jI*<]s(r3dGLan5oLjNu\iU91\[L:L#-caeC-*"jadn!8Nhh=oQ5T:&-e8QUdP+URN-c]gS-G`f=W;h_3hJ8lj=%O8TTl55V
%^m.I/,$-OgY?uX\@DdB:W0nd*.a7;M(reuPOoh@D1(NF`U<@=EFHr-t1!CHiEje.^;$9A":r1)ca$&1G:^Q,deVi[Ij,iQe-40-4
%"&>KQ.O$1hG_MNlInE:4&*p!^:Ss+J"$[l&';HFm2!Udr(JKZ,dhe64+faib$^QNa"9[+G@sRO"-9e6M?69Sa_ut(o\V>r\^"dDM
%&W`T,-$gY!<#/-F*%'[%oalcc1iY"954_3Q",4WZ1pY?p7YL1MkY=JTl1IUdfHn*adA#i\M8>*)PUs'o9%M)q+DD%Bo,XGAJZTV$
%`G84o*VFhtA6l,XnRY$,@?4EY?pn6V8gUe5kQF%.H!:kuW'lUQUUr0ThkM?RD.HG"g-8]I]`oJ]d5XH.8Tr7RG&+$.q.L\rCO3fm
%X9[4YeZ-HX9-(/:I;Bsc@349Xc>@f&Y3=eFN+AmO73\[dW8W&=1+@V[O/$9^0o8Nd()@05r&d,.h--fe!@&':F^5qL_]G#[_8&Ih
%nY5/G1/XG@&]lbJJf^)-93iF9oDm3-p's5J(rOI;/OhR2-GtsO$YLX3]K5Kf;XO<G(6K2Oogk?$#i#"mdA4;!AV:06Q<*Ar3T!=;
%#kBM(L(ZaUQ/@BEc5jir0<F:-8Z8_\YG@GSF9i@X6;848dAkt]Mg`Zma;s52;F"R.-'gWM=lRq<)$9mD8^$X_(+l("r39u:D7%se
%/8uN]K?BBl'TSC]mS7E=_O9ad#m^ZnY(Bq7TVX2A5U[a4XpJR>6guQSeeeL95il3M6RYMfE78DCLpops]i3s?iZDbUXk;9P;.A*)
%f-8Y"d!u]NU[X))Df3L[F(%$;n?fkVX!$Dq3Ge&qHo\/#35sX98m+"]GW?9<`6Bedhi="m3p[\m_equ.pEt_nHV`kjCf,Rg?2`>>
%`$rnGcU#C?AK(8p05a;tnf"`j%aQTQ3u#Y&'Wsg?(e+RXF^a9++CR'Th"1Gu-Sa8O/s=*ETX4S9FF/S&ap9"+\I`7r9\eu[Lc%o,
%R)jNnf6][EYX/JPG@MhoaYc)D.#A[,1b/^<L#ll`4,@s])abI;e+4h?.V[&WKAIdT?.%Z"f]rX%\-bL2&<eoDMr]JI*n1_E76L'"
%4gRqC@<:rM'Jp,j)g&?3\N+I=nMF%er6au2`YK^8`-1m"!(5Yk1km:'AHd!0dCVi&M,fRl+e(u]=N5i`EMsIe3FOQ[D1/Vu*dhf5
%K^-9^2.lIOG5VjJeECYI$B"g57'bYU;>^nt<3o:97<bBgfq^hiG&dSYWQUODh2A9mSKmtN'm-T]2$Bg3rD`)G7=@1d"grC2,&^8/
%:Qg(T7pHG#4?8(So<`M&`c::JGXaP"&P@_2_^d68@LIKOfN*B[*diK0;.B^`*4$U5+?mH^\rG*r<_aVcf:[1%W6gID*o.H+La`"]
%M%Z)A8Ki_EL?TWQi"/"%4ii1p>+.m9hLcYR/UV#D[c(1>>GF'GLeQEn2^(eb[SS6uL5;T#1/Y_b?6kmOr#ESU'5K<74Y.i2gDT2X
%q44cSe_?IjMNnEP7EQ[qOm@`N60fmRpL^>%NBqntkT%kk9Aq-a06OcWM!-@M2K]=qYo]qD^7qGBV)?UoLh!aWT=&7N`gbq?ASM*@
%;mCmNe12T?D#$G_/mlTi63479f=>7LLTP]+#6"'M.p\nj*B]T\9UJEBQsEYR\XW3$3_'57"CXrt;.rXsIhco@*ZV&@l'smr.-$ij
%Y&8`gZ-#HLXti;r$X`*;YR[C!.7@4WTpN8b$lB!L>Qao7Z`7i&_?J?$O3:0b+!a41kdke&oLMI!80T6Y;%PM#V+GjAct*`EY*k?L
%\=u-B]_cp^,:Z+A\S^Vt@A-!?'IM%s,3!W.do%*s,:_!f<[B[N=?N7GlQrKJb7=hS[?rg914JMQiA-*J4;tIZSD#O'W_:"B[F%'R
%Z,Xit&LeS<6*`n3\W\M)+hWWq1$NgXObJXl\91ZcQ'aku+NDf]ms=#&jOb:dX>5iL0qWQd(KA'sO+SKI`[h`=a/hoc9cD+EIXVKb
%B%Q#Z6<%_n2q,X=+e26,mlOVd>D$bbiJMVG&gO,l5ri%ai`2X<2.DoLB4+pA.TICOT$S=_&uXj+R#%\g/NIB*+RI8XK@A/GiPXV:
%5;A'N_+O50)4c,eB2$?%;tT0Z@W*C?%h2<ooUnG:G0i.%n3kVhPmDG6XXQK4=nIJ.h`Q8`>K=?YOV7C2TjWNYLg,IW0$[^+9lMfY
%9lERDK3m[C*`7`>AVb#/,L[qZM!4DQH)WU#X2V9'&;t'4&[Af%HH6Qdaj_.k2lDr'[9MukmF1.%jMMV@((:3u2i^#42?ZV?+^tdW
%%pefJ@SA?uUou,K!I:*+,`^b$k6LgSl:+?a$sf=!ijXWs4:uq7D5"/6GEbb]3b2>:(s,q!Nosrk&c6$ULBT;UdsW4G!"frqJ=["P
%L(R^Nq#"qb37b&5UPHDlVNC=UcI1e\6bKgnDuh,^7SdA2.?$.:R--FoA<:iZaJoIX&XQH?fNT8j+$G_*l>Qnn64Z*@*=@UK6Y)UK
%-d7$G.cbFcD6dP1_E#chH9f>Yn.tE-<@];kf)<LjFYCmt&@l\OZlEtBOR/M7!FR:;BH<EcKn@D$;Y4*DLd`ud#kNTcq1;V(:!(+`
%oFMgPpGrXdpL$c7p#*J'l&iI7`QKmPkmFGAJjR@oc5:?;U<!mimRR%Z(I*bJ</jPH0\``2Ec--V-k7h=BGNVW`H!9&!J,iNMOmQ6
%*,=sXF6f1qdU@_O,Kdr.1L7OcO_b<=#[6Rj'4P?(K00m>-$b3d:WY%C)H%JCN<5t%#J_j-YGCH>#XN*'Z;%+',]GF'>s!KL#]#!u
%!Q$Y`/srN&>FZa.Wn8B\8@sis?Ko_T(PD(UM?95_Uc#Ym@$J'=NZN411pn6TRAaBEMap^&H:1;PCss5_8F@l@9BM=(Y`jWPOBYD1
%q3N-JJ]CI]%;f)iBW88VmPk3=&@)"^)]qZn4`I]\>X@F[RG3$oS2-(4H5g>ted(AP7nD(5g=SO3`72nRYZOuRSjUn:kK<5D1QK'd
%#I(F!Wpd",j3SA0ZFDou2H'5XPX#+IIHAj3k\EIiC=6^d'1/FIR&4an#']"0\f))G/R;5QP=n5H&+F`9Of6jL2O9*oag;HgKnK$*
%K4a@U:-rAaNNV5d:<V6?`,7;f:/EEo#k+@SI$-l>M3`Cq(Lg(;iX1N\INcQZ890@%.adE9Kjq87qOBt1d*qB)H+F-6.a\;0,M,iF
%nS6i]>1eKHYIoQ$-SP/9Unp^DH\kBgf\=Kj9CDTWOcd+FLra<K2'\.#)s$@YlNje-Y';\7$5)\j-g>F#!TAFNqfY)l^G=Ul.<qgq
%U9:+&0;`qdGSQO..MBm,.$LGuc5j?=NGTfrQ&$68Tm_T];sp.<K9`qJjtoE(e%G!]bp4K2o13cgkTOW@+UMB5pX#="P\-*]X']F/
%mLjJ*[fF8l4<2E>1X9bS/G=>`,G]s(*Ae6)F-]%2S:Cee=/sA+P$Qd0'Z:sROYNN'X`/?A!QSWg7NunFh]rO"@NrbV2NfdT!s.K<
%+^/p/e]-u"@\+G'7!oIbK/*5`KP6;O707dk>$c9pnjo_KF3lm8-.ek-%525R_SYl(X.9?K@:%r6m)KY_S7"_J`%$@oa>2lh;1j[A
%2)#lk6E\Cc8DU#\ZNUD\D^nW%\%;CWPqM2O3j?aEBGSmmh1BiIM!XYnDZKiul+&"""i>Med+G9%9e=D0.oW&d=mA!_m3-3%+"quh
%TUDVT(WoVfU_3`6WhiG16plCPG-?Zh*D$]9!iMRkltji2VfW.M;l-o&9;1,VM,J00qC+s&0POF32/R"@/4_)mB4""O:E3F^AO*SU
%T`oIK"H*\&j=P"5Oq;*,X.nGWic5ej+k`bf7f^qeo<"b4=2C(;K;M+DmjT'>DM#^EfcgQp2%]r@UYs+Z/8f$W.M:5QZsu"lT7RfW
%W^>'6M?&hkJW)Wj5p.V:X*VE1eSf0'+jrqfOf+k!l_HQq=beUD;N(F;Qs5<j.MJG8$:`Ps8!75EWmDB/:?(`>L*L:Bn*JeQ=:8+8
%32.U3&'dEq(6SmUeneuc0LQ>m/p2SW1-"P!\<C$"9XZ72a=("T1b5:2"e7WQV4bNsYrMM%l`!jEK*J7;rF)\CHEM"`C_@\\T@8m,
%3`AXk)RNO6^9HLq:<qLDqW`.K#>RjUoPe,j%,A8T+asPG14bX"*%;?.:F;,MPqn&\26==5E\)R(6_-.''_JXP;E@+"!Xc_`8B!ue
%6Q#:1g_m)fCX7Su.hYh""kT,KNfTD0P!KU1&VJ%S4Xr.\nOd=7XG(D./;hhQ,\1b6+O;*=+mKAmk-,q14Xi^:B!B-l1EnXbI-u)I
%LIlCsVEopa&Nj&.=^]=@gjudfV<.;Zc5Am.7+]XD??_NZP=&M6I8,DRPAod'N$Q,.ep@g-F$sJ)/.0AY8AlM\e[_6VjT>nH4/_VS
%;9ar[X3g.#BhD'p3s<>IhB2-X88S[t=;1151$2Og<eYj"gUk>*=q@k\!jVs-GA`)QbPN3ai_a[9;4N*<q<&aY0:XP)8C"l&*Wl\u
%i&4(`-8cRfiP9#8IF\Gk#M1qILhr%1B=TG?K?!g*Z-=8pV@9]KCrrh7:I(CcXg&oI*_"I^@N15[dZ>1!lBmk8Wkkd*dpkiI7,;Jb
%049ur+r;!4;%PI-^_qlhF:of`glc9)R";&[h0<&<k[PVl"eoOd-7osjiKaso6"iI3R-6."]ne(cX;EHK29)LKRueuNE55\Wg/bm*
%6r`3"(M"7l%:]NIC'_um"(ihb;ReP!`^rXo%/d$]a3T+PoBPsfUn?s!XU9\>%B2t&]DXp9r1:et:-m21:!)D16X(AsQJr>"7Ee7a
%L]fi+@ehHOMBmcEGbe`O>uHK'9Cm\\Ta)@LO$6;-%iIG09e;ZgLPREsO[W8=Y`3G/1sQOXKE9AZ23Sjj98Wa>`1U&uh0<O.i&78Z
%jrsn;@"Y_HAE\/AUH0bgLTJ?-iVb&=+JRf"BD<%khCQ`J4P`2(XSJF;)-`+X'U*M[7]n_A`[T.j8Z;a(C_8gh]TU])XQE&2b(<Q,
%4h-$H=_)PMY./HU6UZb29#/6dFTB)8/C>$kP\:Ak8mIW\Lldn-V&b:uU+!"!c&u0lS3AcAM4c"9K%]]K(cqa.iW.Y:a7"H(DW?7o
%GkcD/g=nT2lA%-N,7])Sn3\?e5"XPp0mfug_JtUu%VE9n-Xui$32t7ces9Tkf^rpW`U#'rQibM2jLMJH9!5C;S=VuT1cKn^OC[(!
%iG@"CK?[h@FJAX2HOq)qkf=r;.8A98kgkE"!?."ZO0Ob*e-H8\>!HOR+``6-ZK)hf!:dU>,\e-BVii&2qNO`5M1B,8^k8]OH+#[D
%,3)5@XT671a_Xu<-!&r..VUu\WG2ogM?`]Ce`l]DO.p5-9:hpR]bY(9IEMQ]ihI]*7Q%bU1l.;/Xdhm-c:2Ob7h3*V1iR29RfZ([
%gDoAR5oR"D3]IJ**mHQK5AJk:4kC0PWZCM89soll6Y?X+9iFa;QAE9tJ/m(8KPAlCKeiJ!CI7+In>eq)9B$tWMs6MI-:tijhIOU,
%9EU9YBl\PmAj.P`?PR"u"4S3)AHJ72\>-*r;e.kOO'"(HpcSDp*=\8'`/>nB8R^\Y/O2M4h4>l=/E:58pEg1hodT+OjG<9X*f",m
%K/DA0A7a>Z9fPt*nQH2Q#7MMDD]7+]N@mm4<!U\\A<0,6Jb9R_JS"2nL*!@,*(1RNFfYVkc$W:,X-.ECRF)K\\A#Nd-E(W!-k\Q[
%r)Q0Im.+N4=Ni+r,.sN_NAVZH^'=>31DACf8;psgU^=o]RpJ9$H)[VE%rGQhC)i@kM/ZSN+OuaO+J7lI'%/aPB=F?T`<Qeh?=FFQ
%J&)YWS*?h]'`f)qMA#7FP'(8^@IUC:(7cR&+a,Zo#VPaWOYD*hJqZ53P:)siV"iMa2kau)<.nY@C,s%8/#NZJ'C.mId<^ebTroXF
%/=OK=<2B75q7\'H0a5ruoJ'@V-p0a+i2T@?Zto/LKt.X.U-SP<C06:$>1DHmBnP@#+ej_^#P0]LNkORC#190I#B^@%;QA#3=o'Ut
%":7@.+qWhq*/)Ykq`'$rZSrMk*BA\[+`fju9+J;?Jigac;1O(0*.$*NK^YWHa!=d6e/g1!Q.B3&O/N;QR=^CnA7D+$4RF8Z)F\8e
%!aK)Tbk%lGaM>$]L"RA8FCPGm@aO9fHEaWRA0t#nH.s1cj+/T9'_E_lrfhq;,<F3P!?ui.3""YV,$'kI*^)G-l[j-P"E89gWN,>!
%d=e`8G^f"@o2*#'o2nX6PaMt$8eWR9)%52X"TeTq@f_rb_D;><!hHS)/SO4noD,R)p4?<G]qFmJ,e0nYO611?b]7lY+-YmgL.fNH
%o)OM<^iNs87[stA"%6chDMHCqNp@J)>qh*W6)+'`,J4o9]be(X?`YD&q<<'H8JrYFL=i`40IolXG490Ka6LjDVd?X<r@:iJ8-2dE
%`%1p3%-0N'QC%/>d^\4F3?_$8n6/3o?s"UVkW709PZ0rU&6d\H8YIa[G!)-jfRY,(&nsUd4.tQ]E,.@@`jiPXK(Vsa.V2^'%e1L%
%)1`-s7T_k7ck*1FC4idJT/4Z&.h5T+$TMB0G6a*r_e*-i!FJ#L]QdDY=da[rQQK6%p/"oM^NIdhj?/^%@/<^lZ:Q`Lmr[WY"qdiF
%&i[h2QpP$hhU4t#e)9NH8A`Eg._sJG1RbgjW0"/iYG=#8J%?4GP"b'%3mD&g^7j0!M.9.Z(M<j`TW8FG1J-k"X!>_<:T8-V]:o<&
%knL^c3Z:l6UVbJ<]do?/PCW3(]nBF0-lX(@-0=$,%D3!#<!U&=V*fcRP7hA@5Ru;D-[NYnEYcocdQjhr0K(3[8Pj9]$tt"]4,j>S
%L`*o&l0(;2E%j>3>B/RC\HtX@<o(P;O[Wqj`8-@f$-#+Rh02dISR"pS0($5)X&-6;5""e90+MTipg6Ef>8l=-[IKA:f-5%.HQ7]5
%:0FPV6.R`oRh!XgOt*K$kW%3uX^kZs1hY"EZipS]1a-pS(j(@5I)-;/Q_5+[d;V6W5n$Z>ObcHK0JIJRNhF91+e2*j8ne^];fet<
%TdH_83.&!2!d(H>#tkOQ<KOB1cJaPORaB4S_Yd#(\aKTZMN^YcfV#W?'n`Gd#lnP!f$%8bms$U\#s/6fjW3_2Z>3Ca8KLP_$&DP9
%AP\,\_;kdnqj=WeT_ATJ6/"]1`$uK,FbdXI2._Ei?UgDR%ba46Qrcn^KH:`fh"'=H4%IAT0oTX<hZ7AS`*#LH_.gJGee8YN1/60O
%r'`oMVQD?]]C%(BcAY%\U`S\%TC(UNF)JS"7A+m]3%h0WRh/G,3*C;]MnP;kI3DD0UW3*80mm+ofO<cu@n1W>h17m_=B$#LTF0lG
%]Ht!3'R^TLe>t':fdZVN<+EIbre<W_Bu*t5&08X8W!l1<79bOXNF%go,)9X`$C9k-`=6k/jg]*U"J)FreOcec/2ROd,j+%]1kZ_5
%0sop%$;TjB"%"8TPVJ)NIHr*$2=rEH$lG-UK6t$^FjPOnGclT((+7jG^oe4JK&1Ak3q.^BrT`AfqfrEST4[Q!SNC:<#qFBqdRRe[
%$$kUZV"eID"]#1%.D<Wc!G"./-496%W$R=Wn2)j0q,eU-.RB3T[TQ$Wj$VB`OYf>h!4uja@PlnAiSmp8,!L,emb=`=JID0l)mBU?
%B8Q,SEN*FF(bocqYR65JXL`S#?ru$s?[Rh%B(Ve7!=IV-A%rojUW:-K+"S-?\)#D-aoF$Gn;7F^_[`O##)3YP9jUm@4XOckT%IpC
%_RVI7MGTW@P^2E(C@&Br"D6EpeRA38FYJCkfTr8U^E\u]nt%r6[,@:Y*eM*l"Vl0GQ\4jPiF`jO%h[KjPVt\M9J=stOk$HLS?a&'
%M)OX51=Vlo^02DN/GUmO\A<j4]_b!O0T%.L`7b42;r5I/cD-9E[(d5-05O#D+c\G,H4i%i&!Mf#3_Bdce'mLd(!'EpB\?na&Xi`!
%XE0+FQmEJn]V5Em)c!Yq\lB^fm"*3Mk29el.8Dr8V*0[Aq\4s^E7p2??q1Q4E58L3[/f(;"AD8W]e`\koB;Z\>QWt9YHRddB2*n8
%US;,?Mr=h7cs*<'YRE"t6p.b"GQ_X<dc)peo`u+#iq_%j@ULBrY-AY::ThhF#4MmgImU@N&[Rn:%3"2NBL,'j=230<_atVDWM7q8
%UtY/E*Us&C#C-CHj&]NA7XLV5@&:)r;m]Vn&kEp->b2'\k?59XasCH?#-'sI#ud&_D(14KOTA%-QkUCd1hg8Q.rpq,C-k`4@:TEX
%8'fZH"66CcJW:F'pdP>a"SG#fc!r>q2`"GTA<cQ5C27KeE'('T.`/KBhfW$+H#sY\*(H@M>g.jD?7%"H$O+_,Shl=7>,@#+(iOh`
%"tZ0nWARZom2=$io_[RDm6J<VP*3@rMS2RnJ?GUkn%a7Hb,r$p*O(N'oJ\e!i2g0R<gIQa%-@g?gQ#jfnB6[?LkqMqbZ^1&FnGlV
%>rpcW/dH[J?jL\u)ao<]]Rdpi8`Tm^W_l0?l)O9R\p+QC5W#4T(_^3\*8",IBZCJJ!E\1"FWe9X.^;c$(d(@0AQBW*":r@!8$qr3
%p@P;#iY;%H-,UnGSafa]3[#Z+#@%\:-M9@<L?"lF;rEd#5^+=ToMBt(?1,.;cAE:8m?5jES2k^HjWN491?Dj>fK,Xu;;o%q(<1,*
%/8@.f/dt_%*\C>EN_+3CH6m=Cl\,'7Z]t.0U+K/.#Ku,C"tb!`L,:V6h?#AJBLNGC!nn@72hE,s6SQSb;d1CMD&&PR;99'YhLpXN
%<=]fdFBu"ZQ1=LPfQ@;/*aKP:QnLl8kERdL1QEBo4^'g[(:@!EXJ[KlI?.I'!a.Hc7(&\R5Ta\?Ag/p'\D.)hfKEV2*mDLIT`_Qg
%0]YrN85kNTh^rKR'!IEV):lYtpf]Yq-B/FV\jMB8V2[K(g3R(sV?5>M-d\:=15RS\Lftg[-<S^GZQ"6@30RlKN)6deYW0%K5R6H;
%B"sBI^3IqV&0aFCds->4R;C:cT?\[&i$W@*1;3d:.Q&U]SVB'+LR'=!c\N8hPV6Z)I6pI][1!qI^a'W%54LlJ%+Uq@]#,N<UZbl=
%]TEJT!M_YVbb^\NRHTH[YTO&m:&u,8.HG2&BPgsn!4jAR1^`)o8K*1sX&ctuF5hjr:f4_I=]m-s`WSf0<lj8r)>E&uP1G8pcldIQ
%daC,T&:Uo&i!$k,`kZ8CRKYF%4,#tD9Vg:!I;7FB#0'2@ejD5j`.T!hg@4S4.><Qn#<.MjE=[Y=Pd3TB5<T4V%3lR8q!!VV',p18
%9.-M8)E]Iu$5"5o!SPt?*B40"`4f;P?t$alLmlJWndPLcoF>MJ1jn=a%>R-`S\`?',K.n.JICtjR#/99OEjAfXX^1ffeshEV58PQ
%6^_/Sk&=<P\'aL;(,9j#)SHZR=Y579SmW/&6j./HW/aBdn9?6>PQL_OR^mRqNOQ2_3'PqL<2?Na3/dT&cC4OcGmk:J2euOh778=F
%P8>/YI=!>na"1%=+OBmY@rWA;S%OZWT]9HRiefSh,#mhIomq!PX>J)Y7@\)1X>@e:$Q%t9"B:$('>kHcAk#df6s='[qi`)M8Ds2R
%EqSX((cO;hPDG#qdGPH#+\r"\ob%omR4h2R>jhbFXM_3>Oskkr>4+]hf01j?"hHH[JB_Q=r@1nb&(mQ1I4[q\$)%m79fqNT*OHdl
%0;P.H</R?^O4^d1E+fQ3b\$!)_aMGSU0G':Nifk5%X4R:]34,TKSYSR/9XQeY\q0uegbJZOhOsDIIe.p">(CO6jMP0^Y+ZbF@[HQ
%Wg1Y[PQKTK<aFoloPm)]5c]ge20*`1Ckgb!D[)dBgL0(6I$cLn/JUSRoJ)T\g<K5_Sgs&67*`,G>"_V4+S]scKjZl!B9nNS^0t`4
%E9f>@316ogPu&0\Ls\QiQP:$Lk[CleN*!ED,=CZ%^g*o\Q#%X>/XC7OOe18o0j-R=VLG3fIMfgunf$"oR;R;YS5rCoKIT2)0>iBm
%;$EuB0aKIJN*BLVNH,J?d2l0o\;4UlWlag?oAi[iq@IV0'ZkGS:3bP4NOt,gW.QNn;2[ig%<b,t572C<JibjS<m"Emm$3@q?%WI@
%)7YV_:3R0Y=(TWZa#+SW+M*CG>/,`43H$/TRqmD*plXgdf+ATRoT`$t<r7=agL*#`D#YC-cuAnk^nYcd(g#2@6d$K%a2LG2-GtA9
%6.N?'WL4;@cXt^rX`Aj30g7WhYa-f`(sa@7\Yd&ope]".NY[ga`%$`mJ7'!(2k">Y_+[@0,;lVAWN+1k:I1&-SB-H>61;mJ@X%2g
%c9bFLC,MJH%061$@7OaQQ0OQb[gZdWC)e2r;dcq5DeZ]&`4no)Ta<Kq0L\rm@7Y^LPm9"e#W-_4mh=+/N??^"q='mOW?ED>\3bo3
%g<A8KR8=L6?5l0dR8D?jG[e,"\0b1c'e*shR;Es(>qo!NP[:EY70h02VFja>Y+$1mdD%XW>Zc+@^iZGZo*IR)Hh"\HS^iSe.a>p:
%LoA,k($Hc.9HB2a$pVh35Y>ZHdL$a5or8qsI<F\I.tBBoV9;b0%i5(/FD-#"9r`(:iaj7OG=i.IJ2i`J9=G"7<TL[E!0%1Jg=%V8
%85l7LOrn]]-X"c98egd#&pg7N<5moE>.81k[^iC5Me9*Ubn_0!VYnUIie*Cj[eW;$)i0C,ZY@aImqGQ*T!tB,)>]jlGtg3*3nk$'
%^e(*-!g5kqn61.:&oH:!Ln9]tVC?.WpT'Tjp*nhqVi$$3I;CA)m"L7Y86!7<b)%FF].fBOLlNVE,2a+13@h7`8Iu@6KYGQ58d/3%
%mQCe^S:]T#CH3+DKdU@(aK!9TU`M!Fp';l(C+>NU%<[nk;XK/5B19q7Y$k%9aq,c.!W_grUha,2@N\^pUR8Do-R1`r:m#Oo^a_ar
%"dgP84YCidJ7HCIQJ+\a>=pc"*nP3^0SJRcm,E"b+/-RZ?P2,5`!"Eh.t>Da4+6DFH@Qu9-"bhf9L+,GL9=/O:&k]Qj+?QI1a'Uc
%kAOP^#A'1%G_8=510qAl=MT$Rqgp:Wh@O\ZTPH\@k#`<k32-gHn0!#&;@Nb*4ek7de#ciW]d(Gr&?P's:Xl!R;q"g]Mm(g2#"c9q
%\:L5#C"ZM)UuPg4$9mACC6<[FE)2_^J5ooQg836Be<Ye041haH!-+1.QA*sId+_q\UK=)hd+aoO`IM^N`BABZmsr7k3a#,7B<_G(
%$[QmeW3aC7;Y++Rd[ClfDRL8=qH?2,bJU-ZZD*q:Pg1VM"s5N]Ng6bU3\]5r2(%bMI3\A8NKt\Y6nC8*^F$Ck)@YnF`cG]_7-fF0
%n_+7Gc,0ZsWQ1@u!!e&)@B^m%/"d$W)^81aW[qS7*)Rln#\qo9b>nU6WVQ5b4rFdh,]fo2Q(bt8oDkYL1neXg1fc>_Ob0UT(;Z(I
%LB^:fObbuUg$!F)$lQg,]O`SNcpeGoM<G'<<U/uAM%[IK`g;pH'oSYr:rS[>*.lF$@83[TMNI^4n:^u\n$-MCHHQN^4Mt=f]@F,r
%RdS/&[iC3&W<IU*>3q:[E2e#E`qg=+dNUGQZC$p@!TuD77OI7fo[`lW2OK(XYBj)IQ_f[7I6D\B["K`RWWU2+iK>oDi;3134X+1m
%.n140@r%K@1AS9o>obBsWgO5?3Q&Y/30%JtoFu;S7rTL#r@3>L;qjoQA-O$ZS2"JkLP0G(fSUCGj_X4F/hA#P+]?*#&/AV/$qsfq
%RpjHKJMVCVU(u#re6j1*Do'Da>]cf-e"LM1$>N>!6:/4DVb6r8lG9H(J8;2#]S'DC.,j]B65mQQLI$mBfV,%0ebs7(F.R-u'S;\b
%N7U%q/Wp_K#&L]f#d"(Ke"Tbd$_e-dLq]BoiUUq<7Y((LXk62gUl_Mg/!aqe*toqS>Mq4H<ro`C+;8=f?AX+6ODn\i:MHafc$3Y0
%]M[c8qD+lV!P0L@0h4$_&g']D8'"q4M%s"jZc,(J&%4i+DUM4)3K.jf(&6/;*BiUjJWtFldgF;oG4LBs(^n><4-P!U`TT]GVF53H
%G&b`hkem8"kfGO_Kf#<UU5/nMR60Wr=CLoppmM[emokrDajG`ImKeau*eNHkF1k(BH>@%!#0hCu/36>V>R!=QnaqkLLM`=_'WZb=
%/.cl3?V):H+[1uK0[TD2h(n>FKR_YI/IbWe1G_E'3:1aXnhHN7Y=fWZC,6,q452?e1)g[)I!A(qS>rJ_/;[&O\t#PN+&,.=n5pO`
%e3T=Cdu8'-3fXu'=;*/do9&Mrmj3BUoB]a+\cf>P\g%W`k%)Vt[JCtY40Bdm#a*q=)+Ont-Q*1)bD.E\GpV6J`QpY_BZWl!k#]Hl
%WGs=H:[.N,<ooK\Ofr<7:+4['(/Zm#(Q;g<3Znm6l$0UAf4&,j!aUMikRRIbab<uX(!K&q-IKC?2oo$KREY>-gFnI\76@FRae-o'
%..*.J:eX8V;9^&H\4m3%_%!q3MqHAH#`c$!9gC!W)hUI@jN4FW&<BRB<e7,+":<Fs%<7%oZP6Y076lsSXlH-59FZq+8]Irn5pLBJ
%LEakceJs^Ae:hRZVeRl)R^2AWF2iZs&YFZX8N^D=k1ENQ%_\b#3$!)q8^W0h"uf]p$"D%s?K?"s<bH>D[mM/:a[e$/#d9[mH-G+r
%NVPnX=7_2iQ->Eq#I]Gi6/nR,^*eRG;TmW=pECG?1-7]^C3:[F*TH=@YK*u%P=HDH88MI:/HE8BLhafe[r=`:7$FUFR\j;rl6jtO
%D3@sT#4,-ikYJ37gp*JSKCt];PB$Q9MB:7MQkb\)jtHp+KeQmF[S8aI2WRH2@;DohppHh?9)).9"]fnnEh&;L-UEbi%D1#9#LNlV
%($((q72jS/X\<aD99c?ICZIpmHJ]^RU$kpcE+]L\;@/`G#H"0$$KN2<c^#ZpTe0XZ8,DZeekC-U,GrEpLj7=iHVkJQZm*?gT1;:D
%bG)8mN/0.[.6ppr*[kVoN"mm\=C<U"fEAPkNZAjU+Wk&M7>.\qPm31fA0)o9fc].]5$c.0iYX-3+de"i)8d8!=3Hn.0/:t<'XStg
%7mK._jHBn2h=)CtbdesQ*YhnRl+F\00o^8pME3ne*m.Fqefp@g4?A6pVs[Cs9H=3a,Np#![Q.cl7pHL_'s58,jI87U_5jB*a\A'6
%-B_hgA[b4u$9L07d>4*k5UXXpW\a=Y*iinJEC*-G=;%JP)DW4E01`LX":iC4U-0O9p6q9-80^p.,.TBX(4d#3C75XjYXhX*KuZ>S
%F:DQM!05A8&fk-rN6Y,ce:Ri2Kl;q`MmQ5h1eRosHka1^WD5SZ/V$)[f(*-p[0ic7]kYh!^L_gcCb8gk.Z22IZba^hk`h2s,,BKL
%5BT_>ihKtY[N37D@7d\V8<-S9<Rq+59(@4f<$(ocH&JH#'G4Od"cstk$]@Z&OG\`)b$HiNa5$S@B2jsuMi%m.5X%"82hK+R1+29o
%<+a[u6c>c(@=l=q;]`*;9l1ZHThJR8VGMT[)4\aO.r]*8O4pnO!HR,"p-ge`Gts<N50Oo,Z4:9O1PusO>:F-9O\<.pe,X2U6nJg-
%Wrbu8V<*hD.NusAVNFn>$AE'o[<9<O+jJr3PYGqV9K":"8t;//pRDf@buphcK3O?]cK/b=)6aNG5=_mQLnQiiG(p8K7C?Z$Z'A!"
%,19,4NXh3dNMd?jSKa615T<p*10Gkp`MX,/c"//]jXfq=VadjjYZ;>D.>-JCHN?aFb?!S=F4CVJC2!?W6cM@R/Z21e-R&n4c,$-+
%dfHM%.N=<aOA+h*(,\*YFW$>W_/NG/L5qL#,R'@Lligim+YX'/F3dE'7I?clHA&`:VGS9ni;Aj&MJYR/%^#@-[GKk_ReN#2C^34\
%U/_k>SW2#Y[,3]tTY!B&pX\gu-3e/Udr;?OZm6FJ$"H>D=iVc-:_V\B;([7P&Z%pSj&7!>BQ>Q(\meSk?UG:#1t%@D*Z6Wa!4[(j
%Th'QY"""U1XH`"+@Fu&G)n'`S]GBa1iP_LrQ5^p,Q@SY$f>4upOPM+D(E**;CYL*;?;\m7%0AI5s,dbaBUP#/dhJNC2fW=<U=XIX
%,h:XqGYk8d5`05L\SOj8oXSs8P2QRY[3QM<"t5_%d#ldUB<W!_['H26RUD5=%MgYoJ5h,R?`'9nKF1j#:9-;Z.FmF`_*`:eX=`,I
%DETD"`$4ef+^lQQ=1"j5'E:9_?H%pkgMF@H*eKa_Qu3J<ZQ0K$%=LM3@k]P$TboT5r#HtrDH*2=R0!p7ZK+,P#7APsX@nUX&>_`@
%i=`r=:fKSPSgm3qP!m*e3j:GaJqV*:b%#&*[-Wpab/dpfl%(_?!RKf.`pSA:@>leZDE\n3$QXuD>0?VM'agq"JfGd@HAsT&<l_<9
%F[2D88\de6^CQNZfoU9-Q&&%R-)IFC"Wq15Cr.$XnI7RZnJ)2j$%$K;@9MSM"^UXGEuJ%T,;=c6dOHZ/:Vb^qbJ[L&;J`$=-Q<,>
%*:o9s/eilV.8MOIm;*do_JU1$L*8?MTA:\$YXcVTnG"EQa"08H81!ms&'#pQ^3uTK?"7sq24J*b<<lne<B&>-,i/liE'8Ga5a/4X
%\.,g\D!F):0e6R$F=U0g:bf@[X[IpuIj1m2kTuqE3_i_Wc5X!a$ZH4M>+AtA"Ugk/=O$`?b_4smJft>E/$b)&OtF#;9AYhk5Z+hc
%aHjR(1ou@NdAT8qJ;";pPhH94S"Yg\i.h@\QYB\Bn;,Vh(q#C3m16E/q@)"GOOBTQdLjjE/"'64#q0[ZPTj:945O@E(k4U20FZ41
%g2!RN:mK\7gi&Y_9k-P9e-XR`EHQ2pUK6>%S&\XY33BX1O6R^WTW!-2^e\M>Ch\B9oP?C[[Djkh9ru@<!h-?$NB2f*'W'$.LbAqc
%P[m8lnCipn+G4`=UmZm5LLNrZC9;:b8%#ccKdLM*#hMlkO"0US?\JO8XEIC2e/f69;;muF;tXoSK<4bZmQaTqBniD>%*<JjLkA$A
%#"@*!.U`C1V4f@S(ZpHo-RG+h*+GJ#pBjMeU(XRY.ULOABONTh.I;2HX4RuB=G](GPYQ3C)mNO7(7K&@)TOV'4MBaP3%^q['U8$]
%3F5)9JJm2]o-MpRMU9`B'_@Lhb.t8oLo3"nZIDJbY#lg'Zgt`@&.NVQA^X[MfhF&</ijXUQ63nT)`*SnL5Op7ZH/F30(7<_d)j)T
%]u#,Ym\J[.MHs&3Ke@`n!rJaA:H&bD0iok+cC&1op/i6f2i/qs/Q#uFL<U)WmOhib9Y2(EibU<]UAGQXj/'uE\8\"CCSeHqs1CN\
%n`:oo][[P4D]HbeX!4VBiS#Q9KF50jgk4?/C`D^Y,^*iiFpDOfI`>AYEoKM.cb"$\e;^@I.Z0Oi?6FGS(u#n9"+T:p_B*@G4#95S
%*h@8%ekm_5@$r$8pR%M,1:Tf[V,G5mL+'H$UC9(+-=Up-hQi@?*2*W$1!moS1/<WRN(lUd6\'37r'YD0Or-pF/tgpj774D5h.bHP
%1`;(G-MAlh](Ggh1\>3E=OI=Nkmb%=@g[IkJlr<q6Pn/ZlWZaReGBr]g_f],i"mf4ro?7\gj&PIX09^'BDU?G"YTm-bEZ@ZfHq%F
%U(j2PI@AWqU7(Yej<:P`0q#pP94OuS]V;@*V(C^W7'o[6$lQTWG`[U24K=!gAfbsm"SFcKO;_ndBLcKCG()N;f<bqqdLa66h8mL1
%?ipcG<cAJ9KL`*g]N:.1GiO3"LFEdoeZ*Tu3n!B+YY6>m46,,hs"-qS["0jB5[6pY.%ZlbgiZMbqSVKKI@D;JVb">*,*6nieH6\d
%6JJ7r'XtKk2/a43*4f\JaH@t1S-lPk,O$uBn7n[?9d(bX1rBtH?>6H;QB/@+eXec0rNn*nlDa+7On<5inWM1+4WlqP"$fXJKl:Hh
%Y;Qp$'.s<gY![t<*fcs7q:Z=(6X5!TmdOaZ%KIcC+^9"V?MhF48b"=fbfN%$L*ljU5KNnFJfkm:52C6T.'k]p,Z[<(Ep+`sb"7>[
%3VkU?L+D&>S(s-5b[kg!eP8I*V?;!pA5kGd]8_>J!]M&W4JHg-bQR33@4(2B=u]Tc%b:_/i[r$2)oILEVSEgeItis,RV"H!5I/b:
%a<Y59EI0Z?"0S#/F[h4Z7nE=JgqlaLnQNM6"]Q2JnE9S8/ie)32Pef]U?g[OlF8.k_-<l_mb+bI('S0AKofMR:BPK4<jYZg6$#>s
%8#N"M/+ec/R"^od]3(Nf=9ge8Qf/u@P^Qs8;5B;+B2%:TGfgbkN1kF@d=ZL"Y$/T#QU660\cIK6JJuraN(Y4Ek3mF.l@fH3LMdqi
%FfmYc_#Y2)Sr>A"^1h<aT]>5q.99*N[&'eQLd`DX:/7;,/(iJsV\b'c](jG?VKa&G+)-G)K]/?WPSUm#bCL"F4p"Wk;+YIIUca*V
%+9[k&&qs>S(osi+#9IJI70Idl%NV>lTZ8Kn*k#CgG,Dq";)(E@K9ITkUHi<^P=;;Lqp0:kWFo;1=9PYh#N;`bhCLQ8Trq(gCUdEN
%qY$Zh#NHRe)%<T,!KGA**[F3q1:D&pB$g_q0*f[H7_i4F%RD;VJ0#-@//bVt"))HQ,)/[\,l3=15V+"ndMYOT1'(h.FdVr+c99"T
%,?B%7HI05B&Lek;"4E:md72t[k?&C7N0n>I!?Mt.o7hBU,h8gob)J9g5?^H$:!KE8&`6;7,o_iV3ZIBH6Es&:?`$<r4_kd!"pBm@
%!)fn:rgZF1N#F9oWo03E7!98g3YN*M77;4!Np=8N06$XoMmB"W+YqIgfqOo$,7HSk7:n7h6_b:*K3__LR<ejL`Y='Fhm,LrX0KaU
%@1c6e[::u>_Y^nFN)be)RLs2moJJ5^-89-C+]\V:Q?,>C+^'$7D07o5*oY"Y:sAZgbXpO3DK0pCi$W2O*<C)RK$At?2PM19I?dBW
%!D1D#fuV3jI[Zf$&:Y6@9T't]]L+S':c=*ENd5o%KrKBC)i9C??aCY*C7daME.Z^Vr2%JaS18(EAm3?=^4r]I/??[,"A3hn7s^$?
%0aP$.BR8>B>ZD$mg[+<M+.,tXH!!rT`PE#*,8m'Qd0o*W/B-h7;uMYNk-HLj[0)F]P73;5)aaN,H\V/>SVV9&j:h+:&jg`h#lCEq
%)7)g'/7WfG^(Tdm`o++Eh(7eaYK.M8=IK^",jIZ=Ysp!<OGFqC+@W*q7-.!.Zt06H,P3PmJW1\^EbAPZMC'9*)B(-Y(I+uAP<_g8
%gONBFph$)1gkMgDL,hpr<slGo;;D:#/WH/UcO%/lSD4,u>ERNF&D%_X;;r_NeS6Nf6oTh9!FMi,23CUn#tS%+,=QcJk\f'O/Y*0i
%9$AD%0to*rW=T=(:ps%-BCUl_^JuDak%+#%OH8Qqe7>.XB8jMA4:DgmC?9.bWrsr4b)Q'43eQd:Q3fg'VI7V")8Jq<d:c!_0ZfY,
%//uH,1]J#^$^u$#Fu6LqRt]*`bFuJo?::<Gje1>p-;9Q!`q'7JVeXc+40"E!r`c];j:TXAXX[1(FbB2D'NqM/(l&a3'MY&Hit4F)
%0PoJt4U`LHEA7aZ7gg+_qEZV`cZI,MdF<fb0t!B$L!Y#G?1b?X6PUqAaGMhnQ*H![(2QARE`NlL]Gb&OaBWab>C;%,*1@YmG='N4
%0GS9C7>@`]/LP(/B:0fY"=/Ja(hS<Ds&KXLA'euJllPf7"*Gepdc(7mg`*t?T93]0*VuiT(m$(PM)sq"Eau3hg-g##(uSRI;b3lD
%Cq;MY[b6ilk:J^<%Ag>`S<lcgbHL)\P;3Sl<1Ds)$8DeoUpHV)gg__jaIJq7\HO@Y$>?WcVM`aVVEc`!W>)n=U%T<p]g.ToK&.e:
%pl<29(p(i0262st&gD;eg(63L!VA5-Ml'QhG!p(W`U'A:@iO)"Uu]@ckss#.UT7Hb>r*2]@TAln:rPnp@"f3>*\:Wan0(>qYi'6a
%T\Z%Bf;tEH)p"J/oO.9S3!ZK^deq.$d"`J>jcmEC/aB*_O.We-4rro9nQ(:J"(+uAf;/&+QuUd--uDQ7V\K_PNYXTJ^4X78,3![A
%+:\!M3%oTjB[*69d4k_"gft>!fprII5g(5\5QDct_:20dJT]WcF';hq=f<#<-YU22.O&=sY.<99W1_TCqGA.UXOY5&_(@NXI)Ck0
%[7l00%M<2QSmjSPC6f$)IVD.LZN@)TfELjK3A^pmJ?p@b$I3Kjo7=j+EO[@S.M*uJiDt3=6dWCnC$L8A1$sN9<';cOeY@+VhU:I(
%@Y%&74sq#&*WkDK>;XcKiQ\Z\Q>GHlp`HRBX]7i_6YL>B`)[Cj,:7R>OfZDalG6&P\e*D2SO_V"9;lp'h#t>T*bK%qSA'q#HpcO1
%U6[4K)IHkm"H=XMJ-opuKX4Q4gmRBNp1a:Y7+X2Y1q>Y^'P6bO(fl>-1\<Mb_k&7+Ls:V9kQ,B]_r4-$a:S?&==^CoB"$OS"5m;I
%MT3jYD.ph+Ua[t`f[iqaDtW[+7WDF+A)+HXa@ch\W?juQaeK45FF_7uQu\@IP5M^b:5*Hu)5P0u1QDU4U1#L7_"WN;NTih6F"%1$
%6+BDR2V`$U!dB*dBJ>WBjsdSC>AuD(-kOVSc0mUC<H9$"d#%lPbp2GrAZJblV)VK+p^QNi1!NWZNgg*B:uBncMa;@R.U*G,+]f/k
%IXb!4W>9*`^h?4#:`*2e,//joC`S@7:V;(EYp;M,[9tuejkAo929-<"cU>YRN<nqD"hu._%lYNJ=tR>8O+K>)0c>"O[#=d*A.jmm
%5Q6)@AI2e?,@H=WHOW$OcIr\[Po<%J/PXA9+_'_Gaj@CUU*il8e6><`+0Iq8e5Wot@kc(gF^c7($!oO$Cb]3(7ZDseQ,1$m)$gAA
%6TCSIm!*tSjd$2arm:sHVkGN+]g*)OJtO@hQc%,p`+k$n19agagZ-r;'D-g.]O5?XP0>@KL6mFUnc@@JB*1FJ8K_@R)[05oVJ\1d
%BE7I*.._T!pnKu?+B$Y?[-1N3'/WXh7XVEMo55jIOlNN5gFckg7H#T%fDqe+@_4>j0FP4'EV`B/[QE"jaahOApl6V?"/5Md"uOor
%:sC.PMsinGf-G:mM!kUMXtWn,]M<G+L0FF]#UV\jSt,u/[R5"D)O7998m%GolhS=)NEW'=nD10>:/FF[W+4S?((>r+8-%*rkcTLY
%MF4a9<>T.7`3#iF"8m/=[aR3);!2O1d"]'H\:WNZ^TE8gB(:__SPI)Ji_F+LnEe3";af#S!$<tl\<R*\(Am&78rIbM/c04irU!nV
%/;1"<P6F,QX72QG[6/_HCub:fH>//PTJ\f[h'AF7RcOf(0SG-f(<OVQN.i]G!or=oCgkP_Ko.K-`B9gM'+8-Ech'"W_%g#C5OgW0
%^aG_O5Nl`dV^.(]S?Cu&==5B:.mUG74`nSR$'l>&6pL8L!qTeeF<)`Bf_&CW*3h.;&7?O(ia=_kUtQtAkX!8LZF-Ya$epoL6*C<e
%Q9fQ96s#fld@%W]'Zg_f?T'F*51BP!DR+Y].'W!gAlQ/@%&E8(14mNMNQOirC%$UO1uX6Qh*WrR$`b!=`#-M:OAu9h@18B@D=e#A
%$0@<'N*p:ss4q(01aU"d)b:T`^hTQL/%"N'*;gffL9P938BTd+S[0jQi6OX]6O_k31BVkW^BoZ1V)d2t4i=$55TT5BM$V_+/&lV\
%r4Z#aW#Gf2Z8aH^KQriT@Bo3s1"HBcfput*Fdrl>_/`.)kZ$IZ!)1J4)T//FlHGVS&ISY3,A^TQZ+!ctU;1C"cU#$6ak=Hb!ad=Z
%':Q1Y#6sC\A:@ME8Q'Q1db'0USmt)D`1&5OPO%-F<]15>!O:47^RuqJ#\AdJ<f&P*'fufq0It^JD4T<h\F-Q[#S[qN_Al9>!"KVt
%_6rD6%3]FcX^ElaO!c[EpSd\!d"+"qOPRs"+r*b!._f7c6SlrR<ReHkq/17c(X^cdR!a.%H?LqRm7+Wi2,7C$,Is"@ddUZ^'U%o3
%83ZJ)FP:(=[)7:a$+.KXJ$&!9"3(QX""^CL$QZb4n-OQ9PBL`>\!mVXE[N)\s'Piie(upB0RkuTg>4OKLmShbIUiRT)'4Uq<,h3=
%`F2X(C8#/f]((d_B/^ZTalWS86a.n#-8h`i+ZNXC.l32GjL1qJVs-$U=f&PdiA?KSAq3jT,/Ga.8!J^R@LV:!'E1KhIb'>9WhNV;
%.Tp%'!`RK%b$>&K:7pt9FP7Wbe4_6Lm,k`6XoP/k=<1eFihoY2\A3`YUZg&P@jhXE;Fo^GLaX5h3J^a)#`2th!ua+bZTbLkBgOWd
%b0iDD_n:@-qR6(G.Z6]=`jC*o;uf5s(gR@!n:"?7TuPbNJ;i`,G%EFbkU1$;#)4TqhfoGb]f$7#!CR3[K3)M1VRr]9(q;FlN2,n]
%)Je$kXN%IlLG5$s:D7@M`>Aqu^++2pU:i@IbZH>G:p*.IMTGsd`&ZbQ/arBA#D3_3e9C!MGA*GYJ^cM!\!7O;r-6/$"rQjmK,=bD
%5mg)\0\%$"p(A+Lcl!;Ckcob[)F0"FO1AG+o1)T*mpm?ZG]:4@onP`A4RX$P/[6q/`Yh7nl:4!uO*\]Bn.N!I%=3a`>8'pU,@Bcq
%d1V%I.Q+&3'R=9iPD-qrU;iDrV^Ftg#1nqd:UR\6bUf:KW%MY@VR&ZZ*FXhhkPpBfD#YrX-V+:.q/qpd+KW-<1RsV,nWpGHC>^M=
%F"`>4nar7;a2t[Ikuk&<f]3SfO:a696uV:&?,8@tj>\N>G<)qY$,C_#VH`Qq.j1iK`aDD#bm&mD6h"*&mKW.S`/ngo+`!Yu=%A2,
%=FieYE!P;]#^Im+%_r;n3`P=G<qD:7P"qJj,"7<TU%57%7^QhdiWe>I&'6;o7WI4UJr\P"d@>X?#rMK0.76L&"A>moC7l.^=e4@o
%jk,9c$1B,G8=^nJC@3J`/]g$R4!5!NOH>QJQ8BjcM!CDh<NtOGU'HNiLa\_P6aQuYW*n%A;tj7KPL5HT`QJ2fcl:A/(!13]'/:2?
%)\sEq"*L3?jDBmA.h(+"M%t5cmGuiN2jmg.>>,GP1`#_@qD2PHoAd8_(,&?@GXD/_ajh.3olYieZHsk%R>'>!3a0oWR--"g,\!TX
%%\JjLQj/*jZlTp-L`_2(Z``ja$KD(%ia<KPE8(O]M.]tJ;nI"KF9UO<6*=K0;pct\W3U!%03koERiifOZ,>KVnnHM#VRK#<\;?SH
%"W5SQU&^S<5=hcb;kluR5mdn6+_%nc0l*pL&[c(=QthZVd&bHs,%`=PQSNDBL_qhd*_6er=eL?p1GZ<S!?[O-+SpqnDnrje0i%)V
%>9P;e(HmB_)&1839C?T$3ncp]Wa^"6#K0T#<mN?8i_>AqKbcF3e/PKh!n)+VhGD6gOeAY*!"6CS*+<<b_L*CJQ8CEeAk:'eKrD.d
%R[LaOUGk%TZE6PWTH"*eq)(`tLl0cF#!trS`mE.uPs]Z`@O@AuFFVLk6_%''GpB5VU/$iJ5Y!aJ*LtKEU-lpXQcfb^o^#dK@I#%K
%Z;5VFMZD<A;5)TT`+e"n:'o]0U1YE#S\U&Z"NW>+0q6utC[TSa%osLrCfGdf`\(CW'V_L],'uL's/Ed:j@Y9Of';noR"M!/<]:cj
%&IQAU7Q!IQ=H-LZb(kg@TR&J051pJ'PH:92mJsMqK#5u:U#I+b$RYF-IOVI[O(/.>Yf?-:JWEr$nq^E8":\U0Bah0XU11Xa0RrK#
%a8-7T#BG'A^oCYSQ`I&oW%T3E^(V);1)T5rj,V0;G`aS2M)a.kQIg,&a>EaU)Dd>[o!)/Z=;3#-9F0Y-")9Lp!CAe(\ccT??k3,H
%O?9'K%9\MkQ9R2'4Yc"@-O*t%e#S]/Qjb8:@$,OSfU3-`XRiM)n=I`-jC/J"E!s2pV@Gop59GsQA/_dp20(`VjHC%abDQg+k95$t
%&=g')dqANF`TMeRb"5&L56FD$#[j6]QjE4ZW3X3PbkQINKP)DA"r'[;J[b\G5YG6B0nB'l<eGp82ESpU*e?HFC8)Lm0`4P551>Qe
%5?9e28OGja`eiOp9K*EY>DS2dTWG:f=>UVZ8cPoOpLMpo'!-iCD0=Ds7h6-M0OK1)B.2"Q.P\hs_,$i?QOt'+oK#K)\?KU8q2%Zf
%L^DUUg),SLh[292;%`O@Ks;K.C?>-pa#t$2VA%?t#FA<dNE%%3Y;%!5cG1ohUX4'=5riNb*/ZO5>9mECKrehXHAJ0(b%lBkNLUs;
%CX$?9,)#?^aYlgc:u5Tr,C3uS=P(g!1J*3.P8FKLOt(W?][<hR]I0j@"ZO&i``$B7$Fk*YG<jGGO#R+CiJl*6F;8h&4(r*,:U<e4
%RlGlXFd@h(,0mU+>FUYS"P1tF9=I:9OK+I#"n'mo(G(<-?mT-(_.TJW;V9Q&6jK(?Yo;%Y+'[Q)6P%A&KpX"989.;[T!e1o,,;;I
%ZG=[tL5_?JUA)c6VW7?2?doZZVbJt+;Nb:e-SOL"IH!su0@dQ\d5*8XF.C.Id4<Aa6QmVI^]\^+*oSIWH6+0QVE1Np^Eh@^EBj_I
%^!m8*$"IMWdglmc,6k$O_]OTk5iJCbU.%=/W*qs,r=]8i^<7]UNZ&-i?%^uAplb2%U5c)nNEJtI9`)p5;s^"1@X,8kR93#l`<$lT
%.VO\k*aSnF5ak0lYhZ?f;irFNm8l\[S?gjberQ[dB'gnT?S31,X^?\j]2Y5,0o/mf_)@h#4J(nPrksGllk?mpS"&jI&s\WG\RSgY
%/#Cc&M&8tLSb/d,a0&nYYi[76F`\'RdS,C,NJ&Z8__kHdfs7AGal(SpcZD:q?Sd78R5L+`kRcdFPqIEp-_WmfRom.S)bVOc;Z?HD
%-du0=XLX<:X83Za3lJr@V&tRAI50^VSofkQB(OM!^Obg,jCKc2F:DO'CMAL8+\>Ro1M>XkhWWK4F44)Kol9`d_qh=YVHJ_5DMj:m
%;UC>YYR,q67Q:/<fS49i2(Bj`B9Xc&F=Sr9&;a0m@0fuB'iNMX8iY0Tkbrl*i5VTHFtL%&%!@[`%iW>mAo'/qGAZYmKA@YQmA87C
%)&'Bk8tN8dR'*r%"gnBA#L,sqlB*9ZiZK&,*Cd(oB?<q_J4qJ*9r!FQV2q#$=9la#$-f&t)WI>cjl(VlUfpqCba9.mJS7jQ%']('
%aE%ao*M+kCJZM9pl/i[7gbDO_F/s7clN!-IP-^pqY",B.DZ8[k0M[H\:M1?ca3JXd8De%KG\$6iP!'-WKN2e[A@L,l@QglZY727A
%&qerkpI?GFK?MgNAcJZ"7o7CnaN#Z;6]"q_/n,Uf8<UUl57-MA$CnLFm4%62,rmiiQNs3tP*B&aN4:mRC!\mF6>aF8d-"4g_'AiF
%M43Q\ZALP$h,p9Z(gi&[9-uTJ2$#.rM+Dgl''th2:jPEE<8d*t1#m?O7Q_U+GTrl-GbW!5>oVV$\f-RI:QZR:UKO\_"UOV$E-^^^
%o[!^$E-l-Tj;!<]olpm'4Gn\<EMAIL>@&lnP-BsK`#q%0"BaYjBN2C6fCM2oV8$h>Vng4.L'nP'*/ooeg@1^VIiaO4tEMUWUs
%pS5_8_C:;n8lJsFit7g-KA5bI#+%3s+98I!T+3qWcu*lXdg6Rm3k@ppBSF[(YGWLqhZc[@1ce`VO=I%e$B=Bh8P)]1a"j-6s63>U
%d6IU$d9$A6]mp(Ko3\u1IsLY^Mn=1O^]",arVa^kNDdO$ps\?L-N0S9rT<8/qVq/[PE-LU;0)^7;g7jo-,b3(rD.q6NrJN<NrSk(
%&+jf?F8>,d(Y\URl0(&rs6'EnUY![B^A>`&dH3b/X2a^s"q]1>08`Sgg*D<"\j9[_Dj<HCduAgJUModG6c8cVnrMli$Z1/e*S1s2
%?4TYC.YMD)IeU)9@1'!6"JJ>)Wh.X\kRTE^I<A".9K\;<8:79oUP703H#/m<MN[Kekf:(HMP=8AiBef\P<aI1iJOpo4ADn,6_]33
%>GbBU&u@<1SLqeB`(7*DUEiq_fBt%]/62K]48?@A:;pXN:c^ZjM7=NE_TuDA1fa+\/4;(;a@+<f5*m<=s2Z]R2.AHOZ&=I0M37^S
%$^'E]88Mh2jYCgc:+0R4f1FO7W7000k(?6sC,dqfO^(%u:N%OVQ]]4JFKh8Z\e#1@Rbf<]<i`kD7@f82qh7e++Od1<&/.'(@j,Fr
%S<TC.'OiEhaZ4ZL3&XKt&rT<)e&JXA;(=siEFF.jXXI>uecWs50O5_s-?`:QR)ju0A89;[gf!a3["g+d1"/ch%6[Mt.208aK!odb
%J^8r@^n)RDqpb*qNpTu*!!$NgU@!(H#"g,I*gXK?\f78O/)3I5P1q1-k+1jmb,'9M]U(?4_5S71nfW-&7U:cre^0"@p3u>$,X/-F
%kr[0\-W)i[78K-0T(P52+j""Z.m5TCDCS9)Y2SjcSoU^(\:bn=aV-7'e!fM@%C=D10.om.?Ha'VPff[nN)TtCM3Wa;*CErSA5W2t
%rUN!gBn_-[;O(HTY'Rgu=j`YELE[Oif-09-T4;iZ-6"5!2A=Q67-QG$kV;Iu8pZjE?rj59gJW9(,tg(bN+#kO-9'!X+4;4[lc!t^
%g`q%qnOt^C[j^&:$(kWM7ua*J*=BbO1HhCd+HT@p\JW[Fkp/AF<ZJIOZ45!A21HL^U:6U4$G,A"Qpq]*AK(`<Ea_!g*#4S"5oYU/
%Ya[gE-*m!a:)'03piDV`a$dA@al9\q[0K4u%5TWfC30kj.O$TQE\EfI1Pi0ANe&Y>j2JFt`)7OqahWuf9Z6DSi6iNgcbrKh#e$([
%?9tGAB.[WRRkTFH3HfF2oM$e2XH!rC01G,'O]dLB2sioL7Qu+#\Y@$BBFFIr;QOOFHIN7VBJ0OU80h)qPY@K4.+h+cHd#o,j@L<N
%J.OFQd?Z-.@kVm/PfmFAZ%P.j7"Q4c\YH*S?A?uN?h`!EGcp`Ojd+%!EBGfd):D!1qLF[ZYm!7!j=&IuaF2I#?5pZU9M@3r.[mgX
%Rlob"oJ)@IEi`U2WOT*jK+HZ<B1NZ_PP[k,>R'^<42GN/5.m:i'A>@]cDjHsGr-^l$CJH0%o?&D(ZQCJ2GtBF]4LJ.iD3UJ_;HV+
%6sl2`:/??%+?_f;NRS!XfS*FlZqLL=9ac-5#fb9X>Jp-0A:rR3b9%B>"r"O$Pe>;eDC(&oiAHZRUG5jdH,2oJic%)b4]i^S3D]M>
%;lGX/,!J%b`=)d4PTrN:?egfc<G`MaZV6]PSP6,a.jN1d3H-g,X<Y]jBQg]..:5h\&lPk)n<L#[OKpin6l,SgHXK+0':^GY4pg+V
%QS0"fW<Fr/h$V@b9,*KDMG2*_b0gYM^`r[,9u2c4-*hp!8?AT[OQ8S\]him>&?u^6[U.OT1$n^0oCHC7Rr''iesgM>q;I\X#X0+'
%-%SUgq],NfnLrr1H/\?_m)ACX#DZL>!G1*2afG`5$KYl/fo&8FbG2k`)[hdQA-F\&lTqkRKW^L]'5b+b_YqU%S&'oZU&Mb4L5bU1
%Ve;GbLq6>`.\AE"0$Bo8BFteF%kYm<WeraO`4dX!%u=LV9p?K6,,5raUhj7s-"A$%ei14>3"iR]fd\qM*#\"s@*+&^.TM)!LW"P-
%a&Ru79jNjH<kO>0pJOt>_MO4Nj/)&(3(SXWDRr%5#]4TdS=C3Fd/u/cdTJ(k7@Ze-g<=n5l#Yp]P$&?XQKP_:8#_G87#E,t-Kqq=
%"(U)La@Y2oD6eZ7lmg0%17)R5rB^oN#-Qt+,''MVS-841OAUuTC/k^1QTZTo4=,Dc)&TnWn0W*>X3G+a$=3ofFXEC\"UT*PrlU[(
%ag5VcJ94Oh;sg8O<Wdi(X,b<b]Wb@)bEI^G<_Nu0"(<(>9cC^kT6'j8;.p$5+9-.c?$rB0'=Sn[`Qhp5"*528,",WlZFUaa/EF\T
%nm7GI365d6<0!%]LhDI@@R>u2#%h>WP$GV#T>;k?!=dE+1DuiP&/K^0[SD&OEr9[7d(*X`F%kJ()*u;.hL2XGB\pq;#^"V]`c7[G
%Z.K@^9]r2`lm@^jn=.IQ0@?so(^bXnfH_a_MPM$<YOmq?(eEfIF2DMNjE[fuJ;V32/5Qf_X]$XpD0hgTE]IHPU*+WB.U'.gCuKn2
%F>hi4b7;(=hajnlPhflWIHP2-+VlpJ8Rr6$]K+D-bED)#r0:fWEfm,,_2?%^ZUhD:g9,Y(8j,goGI^O2e"p6I]_sR2+a.dB*bSs'
%&6lBNfu69K2LP#jW:?.r\7k&jDip_aL<<;Gr*5*t14<<1->]!4&U?.2^.B,8UBO1WeOf.;=bQ2hLe$;*(?q<0=suc*>,8#R7g4*O
%D0Qulfj<l6G$C4ad]40n"d:3l6"*5NliPD9N4Y*`pI4h!Jpnl%L-1gP=AaYFhQg@IL_0h$nog\5!0:PKZ;'Pa=UNPXQCWj_73,Kr
%,)S]\2?k'=/[`5B*:_'Z?qaK_]n/AQ/.RTc78@QS^?@USen(";i?WK2La4tMD'`gPS$A8s.=eQH3Me8Iar&4s<;Ad+Z2deOW)oe"
%e?LP>'lE'qX(Kbbp#B<h?4!_9kGW"631P%@X=[Gb*!D.Xip46p6*-@T3*AZpk@2!'M_ZEiYa*E=C>?*fqPmk90.@o(ksUV-_Q?F5
%7O_Q+`TY/I4>=%^b]F7bhLfC._<_M3du5)P;;ar.-^j<mC]S-\Y-RgN!3<bOnu-R/34EBlq3XmtR;>1tOb3!`oU1EYece7!V$\HF
%n0nEGP-M$M6NL\mcgWsXZlAs+<>8U(bsF&b]r*AUJi48G^Q(__Y+!E:O8Yb"k^g9+J'P$]Mr)PmU's^.p:5t?I.FjnZ0%Y9Q!;2f
%..jV+1l\4"](eI8TRgY_N+*^Nn^;$4=_&;JPpM]"Kh0GFL^!C0dp!i(G%C-&_HA<cM4dZ]@[-%UB2Mfpmj.!Pn3s$@V%Yg$L^oE>
%/oMr/JJ$-B8jOiqL?"X.0f=DRW/eMgf_34If$e`(3"Cl8[8TEp2dQt5'>4`jgbMACEb_-c#!O\6QWg4:AG".diOmj+8FiEp_*iIM
%CCdG[1*&7V$6O"PluLWJclm0KZYghnp-@V>a@bp4mnPJU';=H*9<TqXfU.NS%/doQ`X8JGMHKZO'67Xq1M/2tj(G+Joe:g)Wa4j=
%qQZfnC(G-U&u+5tY'<0aVBVmLks8$V'N5nj\ucQB/O/_kN-6p)H_qOS00CtheBdJchRb,I.<Mhg#X9^0_,MWP*qM+MQm0r#>ZVE)
%5U?#\+"i/d/S9qfV+PPS"Q-7D)&bkNP>1dL'2h;h$epgMn@4s_-8M/?2g40dOi:b0"#n4NU4TI&2ih[O7*Dkp@H^!^>.VA'+1**-
%.jHRt7&oW?cBe/h"r!:(BGO1IT1j/u(SW?8)(l+GCf8Cm6Z=SM&_:mOc]MhV.Q5g-BrW)7Ti8[jAQS4l-8A++19k#-kfFN3%Mb<Q
%3Up5/=*Ep%0X,nK-#7[,jX,C,3:DPQL/]6HaTL\I*ltZ1i2g`A(XMc&[';.@C@N:JgQ^@?9&*f@-fSZ1m?/qKT5u32PMRFrJ;2MB
%XM?On,81rihTTV8<$fC0;Cd'$[B'b`G@?YcB$^#[5o`1&&[RN<*YsJ<Cde121(ft!0_pjhX5Yi1"DAhcAZ3Sr-#u@'Jo%L)ZA:hH
%(VgbAAL?BJ$b_BGk#dR#@Kc$9ZQqq>8pNAK5]hT!e>_&`nS0=Xb2Gnu[`'MH`1A9if2aZVVC/L^%*0Jkm=C2RaTf/gne9Qrnb-?f
%`Si!O<mdoZin9/WcQoT,^-E^t>F_gN,0-#6?t`f-\LN_K=<TT-2,c5im*cdRB4\Dc#KBM>>-^IL&\]s)+7u^("%m+8(HXV(SP/04
%luJ)-HLXZ)%aCISlb.:bhk_)HZQl[1!"rL`;_D^/P%f7O82Qed)?D8/lrq53/5rT)"p$GkU)+!":<mgSP!1%TFK-Mb,9BY6$PB/n
%_DFqp[DOVoHC8!"B1QsAW;_G]#QJ:h>I0H738d^0-H3'R/st'\jD$Gj1#\)O<0aUIBJ^a#gjC=21jqcVkb9utOCfX_nPTqGR6R-&
%0Y+7,QrJ<<Q_I/*);c+\9u#eE*R_9*:0A[>8j[&:9dWIm%/)5,GR>*e9JhgZ,0VO#AG@GoEijC0HF6/HY3*N3g[)4d7GCH5%sIn+
%$7[R6?+GlS;Q3lVhEh=c/Qqkg1VLWjX-])e9<(@k,Ii:J&oA9B.Luu1Aa5:#`@ru/,:'[Q5EKJX1jbVTG6npn-,3u&,SUj-;JS/c
%[Lm%?'k9Lm,S9s'V&\C0qBGmpZb*/tGU_W?o5J&3JLVf5*1!)jW`F;g:5qcJ%Dsd'_eCG50d:?CK:]Hkh;%CRIsE5?jr>O;c\<ZU
%U43'qgRI*X?/F@DhYcX5=Qpg&2#io7roCQGnUL.*ro9@FnSc1R]NVoCkth;tr]^21jkmfKc09.b?XN<[^UQD@^V%6?]DLOhs66kb
%J,8L(e]mY,O-]n(?%2D5ro3<0+8pS$q"`b%fu#`2hd0X&^:Uo6(?+IaIXQ9%rZhC%reKqppcnbAGOB,DI.T(!/tLksBsZ2ocE?#<
%X7%]KhgbMs1Q'K;<m(KMh9U:k1>cDCror27'sg3DD,"ZfAH'/L*e3#5.K$DZ$9r?F$D7@pld)cKqtBAls5Bq^^0WN1C:(+bgj\je
%([(O"gqLCG]o;K>gZOUC1oE:V]Dhcko)A$6a_eEaq7<Ql)Ge"qGPB@oX,EJ=M\!"Or9ApX@\%FK68uA\hI#-35KCU<l+H,$J%WG*
%=L6["Xgc;Z(6S1*'DpqYmDZ9s0)jb?AR!b?S-SGj2*!^T+.h9D`S5T<CL/hSgt$SaLZ`^LmeV.jpLetYFTCUE/"+Y'"8D7'pkP9u
%`?.=_1o>9-J,-RZ1RC$BhO<3f0(.$QDsp_Cb\4SiU\<g:]=[ZmJ%If>mb>qV'ef<&oA=f'Dg%Zb>i%Se`AZlk[XMRYDQggeI=$7S
%m^de?;6[:qi;rl"0.u*$I<JHKG&B&?<Go:CYV3"]3iI*!(olA\]<api]__$_jkGDC[hm3B$7j:cQeM[LD?UP6k]-o6m\%c%G<*K*
%Qhq$=jjHfMj]5)fDfiPMn,fu-Fd(I+II<#O^Kb#lq<cl$g[BW.#MhT/47Z:TP5>=;aV?#^g\]U;\G48/)3&9oOu#&Sf"A`-HM?2f
%of$UegQVd=mHiZ`\!L_d?T^Qbh3OO3*4OO839'6UZG\VA=S"A_hW!:smX"4^pUYeEk3^.dHZ$_Bht>msh"L<)Hai!7q<m8?h::37
%0E:n)mENVpRZ2UjPHB\rlfWJ4Hh6t1m%`]%I^6*OXbc[qqsNmsg%iURk@[k3%(_qQ[l(?>;JR])o_eFJp?l;&s7U_lH:%]/@GMp"
%h1;L(:",b>n@Nnjh3YkY6f0b=]',:"s7u/1r7?h&poc9L_g0c`G$V*AlS`7d>/*cLj7g$L2ts69N']c/FMmlO+21Zbc[.,5]Ju7n
%C.>l-:Wc1rCd,idYhM7GD`Z4bGr^&-"0?__rqr;V@,;<,[)8?WTAuA#`9kfXbs"6Kc]'s[h;3oEI-T$^A5d,bQhTBD?ehi)..>^0
%psP\o+7[eJr7cI=H2mcJb@g2oLW=C!Q/I_.oR>#4gZF7ik2Pf)(UT?P.IF4OBJ4Ek53XI*;*I41*9@].50qJclVTik`GB_R*k5rX
%G!F(2jS&<4Mn=n*s'"@l3TP%iKY.coC.5/lh:_,;GAC4^%C`b,Y:m*)f(eh0:sXM<Tk5h;YW%sfC\HdYNe@7?o&f>qGMh7`h>V--
%,IN/=aFT?<4<&VRbj-ROQ*!W:3A@YOO7r&EiaGpM=mc(Y?K#hUTDQ<U/<*GJ_=onrG1AM;qSbF)p[;=0rsEaj:UBr5=X32"$^4m/
%@f<dsp@%>PmE<DRk5Y[XY:rS@I!^$XF)gm7`RVs'3aM=H:UYDqrij7\nF?*sr0)!Bn$s:9q/ol1HEl:rM\dN@Xl=0/*usmsMIX%Q
%0BekHm)s29Mp&Te?(INn>[Q:As5r[[]mOkU&cR"HkYJt`kD/eks'-fBT]&Wf_Hl>Bf4Ra#/"ED^U#(4(_PXb+4RP^o;;<LZ4/&1f
%hn8EVm5BGu[26H(e^n=amj6i2$@R5MDZ,I7]QBO32_0NChHXAmXc(iBg>7RU?%%4r_fOh]jpA^c]3BPKV<,._jAnt;)lhAo`kEkg
%[Pa>NbF=Er'li>n*e"$WgGRs+;q#SXlK7eP`U,ZI5C[nUmQE90o;(NggR;/LEU7#8!Cr;RgR?->2g4%(I/LH9rXWqsB)?F>o'V*P
%g(j4+rVH*anh7&@AkfaX!a"SW[qi@HgRXW3hq[$oYBa3[fiBtgZ^]/(<=J6WT5"K^!1</`*?n@(6:!\_8WbE:-F5AfM`m(g=g\^N
%IPD=p./Od:nq)c(T"h'tPVmE>HP_UGAr$*AkrH\V4L$Pp=HpSPr)Ta#HP28-+p=T>S8J"1;9iNH/BXF'm)s29,%FLJ5<pdBW^F;^
%h!IPLU7Auo=Hk\,fbY^=n$+:Ap0uXWqFcf:3IaW*-u1090=Ur$o[<)99[0TNr!9I1c+VnNn@Va5?AG*(5B)MYfn`EbMJ\C37H#&3
%rp-b;_3co`Do';pCQ#I;@WXm3ffENo-NV6D.V&E:PaMV\q\[%J%X`-4&fg]#JZg*hg0X9V?f!B!K=LW\`3g!,e,rc6E4(13Y'(B-
%?Bh[;i+`M"p(,EuArk<j>OZS"g6/O>K"oh^]2oKX__q;cCK(hrBZ1T(Y0l2HD4I$\Nq/'@7%*Zh<WsaDD7:gN/^32JQ[c]YqYest
%%q#@Xfmm:pd%RnpMFG+hH%#5Fo&faOPoGi^hd)c.s'c^T_pij6qgWg0IJYQn_#]nbJ4oJYJqSMAa>>!1c2j8Dqm\J3c+2R2@%%U_
%(C*p.-`=Cq<#U8I57?o#kN/?(B_d.M/UoGRs(BN)3;PHl0&+i?4pLOgf0lC#<R^F*T]g8@5'L##n\#7g?T4O*`%_0BWCJVckHaZ+
%m5d3'B\1Z"-X#>5Is:d5dDLJ.mQ!K(2tF:l14g1`DD/Q9$=htcTC^+IRmE%B-?cPZ_fK6O)fpg>Zl#m3_fPG)R"O;`I9=Cgrk([+
%F`qtSs6]Kur>O8!=0LZ)1#Y9@-!+k9HhBjW,4NPfnOW)C,N9sBG&Mt`%[hbNs6os_It)!"^4ZH0=5!#Xs*O88*rNd&lVn@:8IkaW
%^\9i#rnm\Hs0D8f*oPs?>:E,bI(h[W1"gRDcb\dknG+,Q^)npLX?B`oY(Y6[BYCN8p;=l"ea<)QT%_`VT>&!G)B>@_nE,V]4oE.I
%jLQ((h;t+bn(qB@gtjT^ccmBZnE:ZmP8[K+noo:r(3+'UWSD=(j1K<^?WoT?0Bc8:4en@8+a1)O*@t_+$e`?_p;Eb5WeKenPjVg'
%ar\c;:JPcmAhqb855Wgb`Pp)7Mj/JVg%#\]ros-_^KfW%?/0pQGObO9gUje]BDpNhqq3l07pBEI4Su>*lZ`*LePb0^L1n-";cm'o
%kMg7P\!?ZnWU6"r1r$P2NV8T]jAobhINaTgWA<f<4SER?oC"<WQ\YP-Q[esO&K=bR+!1GmCEgTPH[sLmKR)\L:U^"R5,ZE7#i""J
%I'hAb,M[pRk'lY8;`tt(%Yg=&iT4HlH"'YV_oomcE\P.RZ=fNJ/7Nj$.h(0>%Yg<Uej'b#Q]Ei5Cl^*\S2d.30d(nDb0s[$OBrpR
%`(-f640P<lZqT2E0K=G0L2[-^TMKJ>CgtX/FrbC.%8asFK2&H#LBH2I<o9-;;Z:.G,6iAlID!/(s8CX4ZTbJ.j1)u8D:]lM!ts(#
%4H9XlVB(+*f/)?UIht=I'Af4fnUSFIZ4NBe@UBZ09*b*cD&q*OJ,T&,hk%NqrVfWhjrXc&k[e>eYJA^MAEE9=8?hjQlmiE3^0X[9
%PT+K*BW[]?F.@;]%Y*=-l`p':lh8Sm[hnd\)t#=bY@PcPG3=@qF8SPX!WG0mq_FFf?/(d6=tKQDTtNDpbpW7l&"h5;041mi7FQG7
%HLH5d1RW/7eqHCm_ei-2f]O!u=X-RV;3Y6CM)@We(1l3.a#JT$+7'hkW!S]@*Fk.PR_?1?Zkm,j=>P_b*Fi_>h0lp,#<TrANj3@]
%F0K-olkk(Y`ukiYrbPSNeNlEH$2W?NNu\#%S[oAVBBT#%=22G^>SE#I^\crBXSXTGqgWm@H%5g`[;\LlUiq7#]NUSC][;%0PB3p.
%JFe/0p@dKIG8L;Jh8RY7)C=Q4qeaUFI"c9MD\/&_?sqg']DgW,qu3ndps"eBH%uk8cNg*dHfP]&6O._)InXsU(BVmg7[,l?o/o%@
%X^gt4="?m(h;>@Ik\kZMidGllHbJQ1ias\EO&'9Xp__YV:M'=sj1DpAhH_]lH9[C@0+RqkPut`cQ@r$>HEp7DmDgMu*=Sc[1ujo:
%n?m!dHP>nVAhlFhm^6X6]-$AsT'3AMbO.SS'OI.oiVl2Rfte=$o3JZ?]9d;jVka$=N:ADBWW!Z;r&3ZcK.aW;]%jldF3s'XF'B..
%gd.DkBQFDqDL:*m$CUFWc+O6smLWO/Du/5rgk^SD[gVKVMN'ZqEsHjUT["S-\Uus;OW%&F8a>*2am>!JGO/[e6FUhF]<BZ]YM7g`
%J%@^GoACY]\X/u8]sIhL.tm*g+N*nqSWsNRHVt&cmr9W`pm;:kM*8eBf](su%eKO:!&CumN4rK6<@P0MYMWTUScAMl,9bllGBSF\
%s22USZ@9O$s5@^PQ!2pZleJVD,J?&1B]XX8jVn)T%c1OKU).Iu>^Nb_i\S.N[<#Z]FRYUT/5KRfDqj`,H)k^;I<g:M8eBWg]rcq\
%rO8hJork_Fq!n6Ki]Oc,5M4Qb<pZ7jgH<$[i@=20FM!=Cf[fCi`65fAGPC7dr*o&egh_F9@/m*<X=iPE2t,2k_R(s9ketLVGOOn5
%0!F07nc`4?D/F)lJ]MnP[J\cOF8+1->>sN/BFC1iT1XM-`?i8WVs>>.5Q?rM@4#rn:],Z6F">NtWf@#KYM\0m>ca(s*rfc+"Wn0H
%,:&o%5<0"P#LVr]:pg+u9#R>/m!@i^l9QiP$_cc,'E.+sq,7%)?401Tc@l4[p#:2nc2!$4b.GDMqc5C[^Q%&"*kjj'k7aIHK,SUm
%GcdCOF7:n'F,2o[`VoVC2B-`,]=e_jGh&FqaK!PR0(6Qg^0"CN@'<nXQPR*ChE\dOn)Ms[rU8&bT=)i^*lf:5BY47'Y'F,mrGc)K
%&%hg#Y1\TJj1+[hGj!u'O48gnDr^i3NVdBT4qnsk<MAU)nA>.WO&%fo]fZl[Ws&.tO_DK@M-_sQlj%NJp+^[Y;qHEOk#8SK^dS1C
%l2R@Ob:"Ih5Q5`KhP&mlgp!?!\usAFmT.,$m)>amDp,?A^*fXhY;NqmgUl@UDmOVV9tW$.n"i?uI/8f3D;Lu"a2g5H+h$f$8B10m
%5freC<i2umkBDERI+W;mr]LIRG]C!2+!ISraLeb`:(hRnd%p[9aUjJ>KVN&gp1EK+OC(qBFt_tAH)"DN'COU,g3\DI-+uZff"QHO
%EVHNW].M`$G&<#Pd0e@mrRj,2(]Ubkrj^@%>J&.4q!lUPgZ0]rH?XH0LV74CYNh633ZPL)3G/<"T?#.tR+XZoZ7iX)*5X08i<]AO
%md0*'Rl-HFG4<_j37GpgM.o%)FaeEBaBf]IA6.HMU^s>`Vfmh_hEJ]c3bUMQh=GpuZ2?F%QChgn+'Aa2B:o4X]<?^0o@f'F0eF('
%legRUr-mma+_m%&rl)jK/oTnPo$a6/p,hoas7+8SY.qN\,,a&[p](.iGP(],J)ZL&qYmBWH+3U!h9k]T+7<-)V=hS#d;hm2FJFO*
%E[$Zj]t$O\,k#M/qWa#gK7KZ!I\;2fT?CUA\^8&T8k6WVqEP#3*PGcCqpcd;qNKt%Jq$dKMat1er$pdaNik[o4pIb%=/FMn7)\RE
%Zs8FN?_u>-R$N;KIoKWjY?Xf\ka[!U9+SE]4g`dIq.8>LX05E]e@$oF?#)sYYdIAc8/(UnkX7H;YDUZU5tS9VZ3'['ri3n/[-CuZ
%<;Y'maDdL6>"dAR/A25T35elUk!m;Y._FE]3%Zp!H@Jq#eQKCnqUBM5jd1H<rC,G[=/gH0\1N1^s3PnE_)Icc%B0odpK&LLXY8MN
%e8uJ?]itCIM4S))P,3#B'kkI4q3J1cYL]kib-?1s`eEgfmuL\am8ZC4qm&u03?`X8_5!Q+#4Q4bH=&>LjaNDArp8s#3i;:a[?p,5
%I.>2=kND<:s)<<L%)"n&R]aKYb<^I9]j!s$Vf.u5om@0`$[MPX=ZaZR1cg@bl-"pZ`]j0&3hK(aqD,LNOpjLOjZgh3o?4Rr>\3Q`
%B:WXn0b^\'at;p1IA.D)h6K^cq5,\VD+Vo5HeO7HojfA@WIs<=29=Ll/4[0%7u^8]_/X7oHKd`Zb8C-(WR0JlDja)]QW)Cl/9Y/k
%A>5ROl0bDGs4Mac?HnZ!3cJt%s.LbKfRoZLpB)J)Kl4flC-]#XD0oei.9[p#,rTF/B`7ggp"48ube-N8Ej<Q7%8N(ucAh:_GjO(D
%[F^H+Ecu2qr4Ll7F*kNpSuTNbr5D`=lYB/jmN2/WIX,RA]t4H_\m7s(f^F0WpYrfV[FqTt%6BCWbaHkea+/-$UKi$@5,O[%$h@Qk
%_fK$_8e9TWe\ZWSNSi[X':a5pD*:MN)[YgK<(@,'DLD'D?MaFAn+a5#N`*tqW+T/AK9"YgB*1K`T)D[8%%i#+r%#38Zt,TWU,d_%
%j(KsD<303>2k7'C]<4tD@q!iWdr_sVAU>]H^_pn<1Ip;q?gQS:6IUCoc/1Yq?TUF>S[<T:jEi]^e`D(#+ht>Jk9YK7\#&elS58]+
%N'=.8;k*`TGGk2U?1i^IBE)dto?[HU2uiD4kSjFteD@a.<_Yr^EUK#/HfBE]hUEeCBGB9XB=:F%>37VgqrU+s,$G]t/`lT:M3i^^
%PJ4!*cB55!;1'2SB@!"p/?/psB=H0g%6)W#$<`:t57a@_GK((u5NKPV2Jaupqr-@(Olea;ZpE?mLK:4E`n@W)`K#Yd(g6_cmp<,H
%$a4+IlZnt<oNSq/5M=bYTTAUTGneF,'Whm:gYNb04nob!:@XBms8=09Z$/G!LH*NHbee_uosMnYB77s`/#5a<[skEF[WFj6cV[mg
%l>GFu'5OJem:RL7bYn.Wo$mA\<cudI3U;$66k%E\c)udmLt+N#QB&DH+]W::ij?&0<ph`tcD0.K[iPA1r''1SQd)Oi;pM?l>6CMT
%?Er+U(891-4N]7$j'Wf:)gUI[b'<\p4at?fUU?pW4u8T\PA?^.D62^BR&`qeKOnN`eb4bUEn1)XZWG"gBNcOGT/AO[5*i!&=mbUF
%*t-eL8`$I)e_-'`>4lR<@b^FdD>LY'rU0*'pSe"h/&6V"-IJtV'>RnIA'iQU[5fMi;\Rd/k,7=Uc`F4Ml_L.9QsalL.)K$&@QAJ"
%!f51IiFg?n<I9DJH54;RNCi)FpCnV)Tlpt%dO]j$3G6kmm(),hkXJD$EQC9E[!:]Mo/o/-^AT%I7!3qG:V.KW)HLaGL3gT#I."Hu
%4UK^hjlbLO_rl""fjtDYo3Ep6\bd/`a#]$#3*W4uZ^o9e=3oP;i>B!7H:B#>%UGoVJL_eVj``ZkUT?(nO`T,igu4Y/Q2M<m@1(2&
%VP1j!W]"gRN)*,7l*?oP0ke>LI-!)"4HK0Z@c'46kI.SbiJqko*LC7=:Dl\n\1o#RHuiR<ioS/)*EmSo[2mG[gE$!dLIStpM<Se=
%$E_G"53n;oBCTf)j"P4nchb[-I(#_uAE38'H/3SrDqoH=k+CD*D]X^dFd+Y6hTSqZ&/MnA:otA%WR!"MgekdUlhI-=k@,m"m`tX7
%g18W@cXaQ6qts1"nGjVF*g2<\*9$G_>>IA[a6_ROQbIdohc1'Q.ZjB?kA)UDm[AWJK=n[_hVEB_h5'5#HULp2OA8"NTDNd.L*"eB
%B@rgO[cbbR\FXdeNf.I!4P(=:jkL$Rq")SQ-Je['4SO45b.\CQ@dVam].X]L<XrQ*.H_qrb>I0[B%=pBh63=JR&+'mk8AE=\?tRp
%)Zd(hb@;FD'05(/g@jCX`tu-q.QZod\;p5+lh%:-h2lk;Wq0`BSkk.oh>=Y/D+.Cp`L6M+"!dgo2t9O0r^4cadJE=g%puqBcMq)\
%aZ/%Tq,Gf%iJ+4%rI%b`]D'<HDO<)\UH`m2"FfIpp;]hDGPWIl(VS(jcfXnMKWrBBe\er6c<fL8S6_b&%\*A5WStkV6B"aeC9m,c
%YWI=*KLBcsb*SIQ)=I:,o@*RLYE74jMfgia='kV:^.7d"T'+2H:Ts*f,?V2Uh!N(d3qA0I^M4N;a]PPY^ZaXOE@:3`0#"D)fXgW"
%Z1]KN/-_J^@#NOH?5M3rm,5>?gs-NoGf1*3/F^`uF;d4fXA6JFN@[LY&lbQb^aBm8Jd?XLB"fQ.cDuG0keTtC3D@E4.\pP'c#i[G
%823G/<4p(FnD$@O,B,2TT$hOeKQG%)5J5F)NAEPTB@u5a`)?:q]+Po$U=TmU+O&l,LgT1b<&b?bp(i4B:H.p<KJ@Lj=ItIFck=E>
%b\CD903//Vn9'.k?PL"aZlVFh7M+dFEW-e-"aakK0QFI:O0GH<5Lo^uf7*1F)'dAAcj"D&Bta,qO3OrD\:0J9pi:A#HrhI-Ec014
%e`sS^Ph"at$rqk/?ZkG-bT4:Wm#^r*[0dAl=8i%(:'cmQp$$UY!LdI!cc+1s?3r^QYlZ&7\pH.L+AAhbC0\R%S8pVSYsb8QB"-G+
%J_fp`kB"5kpItDX]aF?omJ<t,IWpj&og$02EkZ_>hER1l*o=)moYc.[9-^q@i%q=6i&\50^,Q[R&cq;OeG'OtITPXGF#eX8-<[Ta
%YW7_mO#]`gN0=q_nGR^lh+QealfF\<k`98/i0m33$r6A5<9Ca11%XS$_pkX9M.qrj2B_co-[pN5J,#.$o@`\UL.Qi[2ZiuZ0ee[L
%Q#EolL-ka7^MITF:[A$\qgNO5))\3PlZdp<')Xu?Z_bG.lmDo8V]Y`arf?ED0-%><=*quUHe7Z^RZD^M+d_oeIVP8CronV&MMLr/
%2u'Q5pn!QB,O_ONL8M:Rc;lpU*qp>bbibTgdDnILhBTeZCn&CF^mo$Fh5O\4ei49i7@OE'J6XX9<9<ScP<+of!6:LalrcI&`0@CA
%ON-0JE1n_lbY(H,8^GG[iOM?5p^@f\+,sBF_;p,:0ES_3Q*hPRq<Tndh!()i3EP%bpRIS;@HbOJ<[?nA`-GR&O"p3M&cQ'L^pC6-
%B+Nm]id(?M?^rKfh7A;5HLX%.dH,c,@d3?rI'^Tf;n-gUX3NJJhpmXr_*tE';oGd`m(VqM"B_o0!#8n%KZ8"sq$%eZ-jK%ZUF6(*
%P^=dl"`hKnN[i`)-Q[3jW.i!E-VJ(`#5B&Q.C+,$8POEs3Pb5(2BG>d::B",6Dh1T+8NSGm#&s'^2D+Q2j5M:+(#gC=-nO8j=<"P
%aaQb*B-T6'jjrX(\JqYm`k'M;\D;0]?JY%9"aTp(037>Xh)r8Q(s(3&**:)uTbSGHT590**5[GL,OF%gm?jMM6*FoG+I0HH_/mmV
%Y73Cm5Qn>(NbD.%F`)jQ_]FFmk7S_"q*d^3RKCT"]&rHGJ,#GI:P:A?iK7UpN"ihKSAW'jfa-d"Q6#+$cAOh-_0n$+^pnIZBDV[+
%`OU?^K,mh.Dua8>_97-eTCM"erODa_a$U`[PE?/1Q-oLQj<a>&a[kJ2_UQJAaoLqLOf=c6&e!0kBaDa-0c6()n[n%<UP9ddgr!O6
%ZSQgrW4h:/+@&kCBjG'I_B(H*@fWkI6L!\Q&4,mna(W`>@Gp;3,Otg9^.]lJhjoBaM.l@ETCkRmqdrmi(f0mfSYKT;KrnF2GO/05
%e&)%$_9_@QbVR+QXuC*XR)QfNLT:#`%DXuA,U7L:J['GlbYtH"jFemc^H/jm0>kEFp$UQ5dsDR3meV9HH7a:se%Bb+1VI8C`puRG
%la2VE8^Hb.N41J.A%qCn0u^]_^eDDN(dh,ZNu-M+Wl%eOh\4F1^r,G@oCnffGL,K"Vto/Ds2X]'s,e'W!H?S5g+N7DU8.)$%!\3\
%X9#C^kPc\']7=Xt6Lu5gBj<BE;A(a2[DT^EZE?/GGVj8&31@<AeT1[q*!6F#g<J$8[ikN8J]?polS6>5hUfO90tu[M2tc:X^:-o;
%oG,*1M'(QP5CUM\M!X4:K(Hn)50i;LI!Cqd&5cs.\>fMr\k6,1MM3a!+9F_Gq!W[uGD@A@SF-*Ca%;.%btO(Dg7ajp:`)RdU2*XJ
%FaT4('/=(ajEruUO8J4GLZJ4-qu*/Y)#(kEG/4mumhR)Em9cMgoZK8uQQLUWPTZkWCC/q4C`$>P^d!3!.VGB^#kah=:l3HF)::Z%
%clc'"rq0V)4+$d=rVm8]Ee6FXQ8/C8.-VQfs0P+Q?hSu_YP1)r-_GDXmMC@=NQ(qriic41o$@@7hk4$20>I=`hu<1"J,QF_q&K`D
%hp/V2AHIjW0Q"VZ*d*QO3*^t'=6ImA'KnB=rr64#+!'TUa>u/@Ob`_N&):FQc[N]ADr:J"J&\nkb<Pl:j,^Me^AZg$pgQ&W%htc9
%>=Q*E;=UlQ`[jA<_cRfZ1[kEW2XK+,.$`X2HPZXs\Gu)+]se5&p>C`*Q!HA1OcR<V\JU%!o,WDG-ChW#(t?*D+*bQ]S#Z>O-cs\A
%l^Ama3R>\>h><rhnR$Z\$'K(hc@>]B^]!h4rqZ<'r0q'h^n:Z!Dh%H'mm$bh_5[.-2TqLCpFWR\"6obO]F.Z0`&OWA!Po.u`Jf"*
%mY'ZsHlu&pk$E#jc5-HJ/*$X4+4Xdtd:l&IYU*X0GaUZm]ZeU]H24F]2dX3'auPO)IJ(k4j$f'#n%LRV+9/+]K`StM'\'\Qcah=S
%S$0WV3H#Gt7=Np;))JHj3QADK3YH(/fpZEWIQ@C'YYm`tQ:g(Q((Btnk@N4W0,$"rj5nV[qRXq_I</q6^\c1C5J]8Z?VHjZ>/qm:
%?73J1hG*IeJ:P26SqlhPXRCGfA)6C^o+<ePlK=rB"C#e\'H*J!S>]$&lfu02<!/YXF(W_4g.Ti7\IP+;jB7Jo/_3CP<S^LVp#Ys5
%`R`3tc<qjB7G7MCc,Z/:S;/a"YPGm'Og!p-*b,?fp)$V9hgK)6TAZIOj10EVG)Srp,Cd3VUOpVZ1?\GqQ^7re9.pfWi+86@;f\3J
%BE-dh`rBOu<5NCc$%YOLAqe.?C\qA!r>5aK]A^TNh228I;?FSSLl4=OAS#/I2^<+eR(]D<59?L^SI,4_)!>,UY1=of%0-4=4T?8A
%H/@Ia4-^%_)'\sqI@$NL(>8.<inZD]4M6$%Dr(#pJq^^!=r_<\roNp)oT-:XKSUd)VDX])$89l,o4e6crUTN=^aQqpX)4\\f!2?'
%CS/f=kWcmE!NES3r%V%*R:>V.Lm#'/FXslrP<Rfdj`QL30UA$.X*9@!.JDUZVZdqQke"/H`SZ?8);Y$h"ZGIfq)%Q39_m@O'p0%\
%lb%T5Mt):k]nc9BXgE7f4lB%:d=ZZ-HbZ/5($D@9f?M>k,j_d=4qYPirY$j33>2g>l^C,1O?NtRhTg_M`a[h--BpJ"f%-EE=k@uU
%@uWqBfONr*s5j#?_+,N4I"jegB(IC@=7N?/[EX+`Y>->u?bPW%T,i)-#X"/__d$^7I6RL'ZgV"4E%E;'EQdu%SQB5&SaSmPj#J)1
%T?uOf0A:7dU//Z6=PN;Y1Ai6kRhDiDHKD))(b>I'SD@)'nqR'%KWW;^bh[HF<F<60kjce>U5rAV/CN_5o5b?cgB3cN@K&E!^:kH_
%pJ!pKnJ2if$,+MaZ)A&Pi>&O[mQPL5XSV`QTDdE':&g2B7%qbI?Q&kkpaB*G,-ke#nqLb%YL]D6fdZ=Oji@/Wq<pLo*Z<Wd?>TSd
%`rBaRMF@^&A7NRV9[b2@Bn>!-a,X%X1nUE=fk0XH3=Z=jogth>POX-SO.,+qq/bA:S)+#A;p1/e8TR*L?Vmp#4TD-<eqe0DD`I$?
%nc/A+Pu/!^cTUUc*M2D=Q\P>eTC;`!7#i3_T?!QuG'o\0A0lW201PsJQANGlp(H_=3DZE"()un3,^A7t<;6!$HtM*I1]1%Q8-"1b
%)*0iJL]d+/)Damb^$F^$LCn)3cTaL-pK5sjr12erJo;S!6go0;%L^=F9LCg&Wcg<B2WW0FH2Q?'.!kFgMn%<(U]7U6."<#cAnL/N
%^A=C-$!$R8Su%In]OZbAEoi#0nqTA?rB26[,fb7gNRIgFm6^Pm-KEb4W+F:MAXn$'iP<Vi*5e)Wr#a64*%cWH<F&roh^8Ed<#tN,
%00]ADj8\-5N;kq+YN+NqarbcTIO3e@^-28aKZ$poh=$)Fr\;TaJaJ'&b3:Ds+B?:1?@TQNVm2cRS?Is:s7u8gTcUYW4HA+o6\bW[
%mi28Ogo'34"0;5BKiu1Q,^dRrqtf"D9%rOc?1G>Qm/?[^-;Yn1p0&559DgPXO)K]j6o:%E4eFs2B+h9"o6dY%J*6=9pcZNE1-mS2
%rr\2T@sCLN9q7deIdrob@"a#nl_"4JGJF%*Du\sLru5o,s6IGuoj=/lnULG^4l5.ClJMU+YC?H-IeR%npAaU>,^amlQgt'is7\Ie
%rS>XQ@ss`bm.mLY#(9aYP5>L\d91EmbSrc?9agJ=T+&70h=n\`poAo=nX&/5qJZ??hu/'(lL-t9hENP'r-s+oRkr,Es6b*GrQTgT
%TDT5k'`QW)L)*TW:hQqB"8-S>oA%BQo&Nmo^geAC5]0gD\+=ES'ge6`&PPZur3mXG"eVMZ3PUZ,M1Q:&I$?2l?mh!H;`pCTOTRhJ
%&Q!bBm_chj!ZM8u=,m<3'KEt,l;S*pc,Bc1`9DQW=e#\ho/p(63SE0F\RQsu$K48e;_O,J2OHLe_X7=\_GD@JqK,%-<S!/`\.<9Y
%4;P>?qYN!H9ZIGi:p*=@Jb*EWC^-'T_Eu<sbE,Afb6'S?f5S`4ZckpDQObX5Y_dD',=LaWnPIYem<A^QpdM8,C2jJDAk+h6eeQRB
%.mK*!!]WIc#N?0d)h=M1_+ms3aF&[:_c2"t+0,pk;+CnJ+h5u<0>gS4";F=,#+\e7c2/)R)74%:[g5bP4T=e=%Ej@:MtjGF!(LCY
%T;ZF-8XMPK<l]upNLOXZ+Do#.APK0o5S]iuq(VQ+7OXJ07:33#4MiXQUa$"go.C498$an4M,1;RLc%c8Gnqm5WK0kipQ6I4\CHs:
%DIVU.#U%`pQ_Hh;,>@*eJe20=QDLur(':N5S@G]<0?CSf7)H#7Jt+#C-tLPBHRma-e$pgTK<QfO6=Bs[>Pgc5"fhYo[d18f#[$19
%O:ff#Y7,o!0:=&l.Z,cPbQ:,SqmhVcj%LYsBQE^!/rQ]A0[d<F?n?bD#F!X)0nAR2(r/h/H>*G9SoQR'BJ/*$/Vt2)#ck2$reU/4
%NI,K77#6_7''dZZ3SUF+oF!uRb/B)CLgmn.=[*n(Ef'iI,JU'HT0V3%@9O'N-F%8^aG>G7EfoOCY:s'$FCYKQZEEq=7jE=1CqrU;
%\`]dD]l:Dl/c9&t<_YWtSu['VKppPE9`8sKa.7nT@p,H*K]QVjG_9k86UbQcnI?6+'e?``SOKkI;4P!,Yed8A(U]Yho@[MO]8kAW
%m4+uFoIGEmkNWpL=3ZhGPq/qIB>#h]90>P%AH5*@eE`9NloDbe=m0AL5<4mWj^Or5?`V/lR>n=9#AH7t=M.'/;11o9jJD7-7%QG&
%WGj4)fbdUEQ"BJl*$T/\^puh<?HjDLqiHl\W:l@SKO)I5_qMT09RU"LhhSFJ"8MN9RgI7_mg.cs0$3-A+c?1C,fKq@4J!X)M9[LD
%SQF6.KDN-QT>N930g0(ZCB>8)'i]N7RPr[Fc0k>sW2)F)='3)tGOCgnIH2,'AIH3GcLe"S?Ub,uCHQ6Yfd0UP]nOGADcj>lS*d6_
%M[o4g^2B)Xg*#fuo"98F2tFDOZ7sS]7Uaiu#cXp%..5KpIu\WW3e;Dgn%G63B`<jO?VNnu>Ff=*[<g8`0U/dOEl90V6i(:&98`uM
%@bts*-E3W:>"VQfhP5_7q"g!`K$gC?&r.dDSrjuc*!!o/b!-^bqYc;-;n'c9Xi9SVUeBC"%N]&9K7NOQ/8NDS/*sY8F(K3J!5jp*
%7_\4h(e;]k7cOs78shc;q;WI=Of.Z.j7b1pB7,b1BOEXGjJFPFY(>Ab;ZiSEI'EptS;)1kW-(8,i;MH(QV4tEq&/8gUFt5G@58dZ
%QquJud<_f:jipE'8F<Y*,LleZoWSXU!])B[I^d00\L?QfC<iK_8P'<KkV<7ERJ-9W)k;s1!V(CUM=V4PE>lVT>?9<FYdR@$*-o&S
%E2Y\6Q43E&nDuiF&Ajs,;Y);hmDJ'kP;1'<k^0=,O@g&t[W@g&Ye,!#SFo$G`jDu88f@+2;L:lH>FPcE0-pZ-Xij15;)DX<Us)>b
%UNMQ"I"K^_F%N^]E#V71GC5&#i-\u]lgcg@_R!?q#UB\E<T60e<2'SI598<5NYeU/0*0M6Rg?3_&O)YbLk]*RBet2QqDT)AK^bq2
%S>O!V71hO(*/Trt4jf*/Q0jfA]phXikai@,W"DQ8T.DX+L7m;NY:i8u.YR!Rp2o!:3VkgbX;,k7[miu]>E'4Qmk/p34h$a_m3e3(
%0`qmchFMT,B?C.\RWqem(DFl[Z[*Y$T1/gM>hQ97hhs!N_l@nO3Yh.W]Q(&FDJ/[,)SYLB8eV+C;uK2+Ah@$I(Y?fdjBHS(E.AHP
%-(/,l2#2U$>YERSn2j3_]#f"=&!bLmj'YB>^A(^6fX`TuE#TnT??SLo9=HUE(qaTX5AC=sE?q)GqYKY>c.<D&f+GdtWOoA)U9@$p
%rqRL>./.$IJEj?<:2'PI55+05%O?VH>r8D.O7*q]9'D2AChDbI$YR@(>fi9A.AW&@DW,=1'9Tl82Q77!8tLt)S2sp0/2^n<!Oi%(
%n]XQf99H<7#cdBs!8A<\MMYLd)Q;Fbinp:salL9t\Rtmh&Sj$O2WcVIHB'!2Wi`sh&T&ZE,qm^d2-(sGk/bVGX9$\>Te;Q"*oEJX
%Z#9`knHpONI,dq61M9CHk\GhiE?(q=\p<s%"A<Ua_0AgnV`Q_fbOH2s*]Xji\o*O6eYOE];2Yb:9mp1,rE<&>)m711N6K`?KoK^S
%O]8SS^e2a1SOY(0fYq_/G6"H9\<,XG,c-'FM3i*G'W%lBl5O,$iC#jS%,S2WZ"An9e%Lh)'_@?r)uY?Z2*$`gI"Q2;;#JNM)Fq\A
%$+`rTg3Mpl)Hl)kNL<ThjUm6ABLe*$fja"MYMOLajm,>),@s.!;I2@I!h&m.?u(`0(+X)Di<WY]/PQXMi`2FSG^q2,*qtP9.#S$^
%;$'Un&lT,r\M58?0gXIOZhW:\Fu*T)Mcu:G%!&<CiK&]QeKZ>qb13Th,(I_(p<'+h_3PJ(=\+10ds]o?\XDRU"561-@`jl'^*WQd
%q*R?-0]$Tt*I':cd5fB#drW<;M..1nem_oDB4-=pPk.Y6q9G\.G<A9t8D]/-.\b$SEQI3@Ym=+_dU826]AoF^k,)a(G\Zk6q!O?!
%r^]!9XmJt1g2^MYrDW-%ZnYma38iXQ;mH)/+<>Bje!YL*ci]u^Kt!/L?1bg-[m.9m=guVs5q&+3Z5g]`$[+k`VGkbcTf/L43<#HG
%+%?<5p5=c9AY#ll=<N[/UG34\pg0C2jp=8<ad1PT;b?G0_R.I,Wl^)+,Gm'k4a=$+K4&FGl.2"aAV5c;V\cJNCI)tpd!QpUP'A$.
%kRI7Z^dXl]DQElUA#WSO)rb?E7BJ;!#l_,@h,57P`ir$X2j[WA(^XJ?NY-$mW:OoG92(&701HV6=tD3]X!$2sf&n'pi$EZ_H99U2
%PORjS`F1(I0u2IRg/*m_<'6KqkLB^!r^oLeX4,X\h?csJ&&BaEoY1--<+PeFZ0Z"KUQA_lGF+GN=>oDOJ<l;<`![8`Y?t_gNi'fG
%rRpF^n,tGX>utiW7b,'JX+`Xp5-Sbuebu.;krc.gj(FD>+P7BNGYZeIGqP/Zo9rmp-b^P]Z)XujWr](l(7?mgiTe0^\a4+.6W#72
%#44*e.On[]?^;+78S^3ZXU#*+qZ\80g=@N<hb_A,6=JEQd%$+I,&r-N-6?SEW2U_r<VY"k]3Dt5eY\%?qeVKDp(ui:cj&bj]&+<?
%,s9l)55@Ydc=Gk)kVn*SO!$`n;bf)PC/atdfBii\/.BfJ\b)ZpZfCUIk+qD?G1@9T@I"?j,tZ)9_7X\YCq:<%nnMM<X[e7[iFO,4
%:o,,C*2.<2:le69KWcsRNo*_$4/PZ[@`]3F-tZ#`RXZ[=/>U7OCad..j(A$,6<;mj&ir5O1Vcg&!rI0%qs`V0_l^Fj!TL##fO?dS
%95Tf,^iet#]O9'ek<qaeo+@t\qh+ORhRm61EVS1L_j[rjh,1-XHa9:X:s&t_1@i+E6"8@bVqY,s#<6Mk$3S@IWSELQJpnHopZ$_@
%8.k)*>P%JV>FSkV195hr@$_6:NtaA7'^Tk#YY^]jVK2s.doOpcgnRpI#4*;I+/h,O%nFK/@3@qSS/D-%aI4"T`4;*"8[0X9T4LDq
%'#'=N"'U@Wq>h`1;NU6)6R-Y$i?=K7,8nsrpZ$-Q'EH"5,hG0!\_ZI#&ls%cc@fc,F+t[&Do55Sd=Kf.Y(Jmr&gVf?:,BT[H!PmU
%\(CZG9$;@''&SK>gb'("Z([P<lhZJb1!iBu,bInRnM1#lWDj<[gmHCGTX)4^<W68/o]PU>'-]58!Y<><>e<J`B=/c?hI*o':g%`s
%Y6`mFmh_tWj)GemT:%Wm]+OI(e;OKhFlZH0)*&D&+?D6YjCDtm]#fU/:Qdji[L'W8Qi+#/`(4+L*7kbe@)5b-85$],Bl[HGV#Hsu
%*`ipprJgVn?K,][=Dtt^E>Om4+Npl]KYM;V!Lu#iK>-jekFm,EDQKmfd-i@9(60SP>XMnu4.M[c^J7ap9KcK"_j<s2WF;^m+^@uf
%q"MZR+K)[5=.2f;8,])(REWMf</$F[(\iYmQOa8`kPZq_9oZ2u!r1B7<?*^g)!i:)p5aTaR`MN[Dh`@N]*%]HO`r(T'kmhq,QGJR
%Oa=YP(6S<NX:C1fs*Y7/'$6Rd&-d1Eg0NOF!_$(*G;j!'DC554BN13*k+8@AR-*`hqUp8tQK[V&LHHG^XZ$V]H>?l,_BbZ=Bl=\*
%(XkC4c$"a1rNS;!'"I"7%qtEWF0NdlJuWCA#V5'b,G.tlcN#H$49lA@6!(]"8U/NVWjdU:I'i"uU'c9>kU2*2Yl"Xm.f2Mdbd76E
%VW2)lF4BGihs1h^iUbA'bWo0-U25YK7BpU%$_UT5p$gl!VW827-K.<-1'@rL2Utrb1T#1<35shQ;YA?FJM1hN`4$$2;5t@NH?L;i
%Rd<_AemKY/_8umFAq`r;EOP\J$cKa#%k#ul_9U2TKhR/5^6'M(is*F&R8rdKDL*4mNi`"Z@4C"(UbDm[@JDgGrJBSUi$dgnfEmI2
%1JGW$Leehd1Z*`dV;B=,]opTFm,',]K6*<8/IeQN^bA:l5OQ$B0;nr&((O0bUe4e]W*.cMG`]'f]PEn/G5sN&e#m8s9>6!&=bsNT
%[_IZ/a14c\C0j,62YkuoG*9Sl*e$^'X<lNbL:^saQO"OS.#g852;5E03Nl3M",+m+8Z1>K),g&m`&'eeMGcdb>?TOJl2m'!VD6sN
%mY0?S`e'Qa2@5E_fb.&VYE@2(Ys6]t,f!rrP5YX!Ft$/@GGH2l&qXC./-V[R%=n3'.(GQXJ4\(,*a+!5qqu:r:t?-1+a%d1'Zb\[
%+InOi!QPj3=:3GJkUSiC7c"`I7aJ+4e16WZHBH)oq\'Z!-S+^McNO-(E03bM8R+lQB;/@8(#6\DG@0e073fp78!\s*3k2Vh6j;B@
%8XQ-qXf/]i^N$Fm3&=OB)&D]mhFPYZ^fF:m;aP?j,t>R06I!/uP&G:Gf8P^85-T>:'utp-J*$7,($.:1$<Q#8>L<NQWOJa;_-q;E
%h]joD&XKd<G64hq27ZJ=YbsaIm?,&Ap%:R*g)gi()a[YtC;UE@0/lB3Nt0ZFqnf.jJJ%5L9M3%AOL(1t5nYkc$FhA`%Us>0PMhrd
%S%'CT&!8Q?DU<4R,%RDr&TZ'=.3K_7L\rQfmmB8Ch64a^56lah-1!r:(NVBNSfk:>dP/,kaG6E$kU,(A'c95>nDnPOIS8M"LgFBT
%p.Dm?_\DJQNc7Pl.k')8fKMV,MqH<e#JOe<LG$;ndXDbIlUGV0U6Y\[,JX5k5ed0S/ATi(@D[!+Q59fI"!fs/f\Igm^F;pgrO-D=
%UhT2`(')'aTMiK%O3%r;*SA$%GV+qkkSka0d(,l"[>q-FUNi]_*"bAPC,O.-7[/5r-[f)NXRiME%pNDbkt+QNZb.a5#@l9*.!Eq`
%[G:(X6p,*FH]sKu$0aHHR2HToMt#PCP^(A8#!`nW5rkI7#tJ/N6;>"MG^mcr&6?XOFYB?Jpc&cFN3c:;VQY<Z5OJtbS-@PqkLu#%
%LS/uI@BQQ_)P99d-S8"qfl?Yj2>W66BNjr&A;Z'rU*;A[b=E*Hp-u5=2cjgBjJom(P++\a-KlmBC^Kd!@*MqEj&pD8hGR834&(]O
%FI7Z&K[*Q'45@s@^%/K?CaA%I=)_u]M]NQ</-,AiZ3<Pj%1;udMS\(Mk42@OB\Fba1/)jp(8:of?L%Nh)chQoP.D82+E5`ch^gV7
%IL?hF!?'r-b%!l*!**Je)SlK*DMt#S<Q#?`\0/6Zk)Ngn!183%6@;FC9fo.rPMa*`fO9;+4dMVcKYYW(K5o5r(0UnSH%Y>7&6;,c
%PQr^nQ3TeAWFi_AY)[@#ZFI=s/8>Ve4'bA_op%j`OCui!/@#)gc9;DuR:sMiZTMq0g9BJ.Y7O(X>..F9:k6XolXX"jX`8QEI6Eej
%61IJ_F1+%DbiDpgXVd0\@1=R+P'G:N!o:$RR9JkQ2nDXSgsKE"/+-cad,DF/H80nGQqSjBAYV3C4jK!/KN1lECseeCO`.[4\VRkp
%DVkL1>o.c2f?FgOK]Z"eeg;H:q-cFR5N(8LOt5h5$ug&W'sJ=GV!B03\P+F$\/t3A-]Ch0cU)Z$lcV"`5i0,'eg/nM02?hr`G6Lu
%(l0nBKr_]*`SQD>/=f(8oIY:f\qg_E`EkjWAM9q"L"b=4*Pr\&<Y-ohF:kBo/7ARg\5&hLqGM\r?N<".<"#`ll7Y?\1oU5/f0d"r
%22cpnG0lm&V_Pro?0;L_apX</qUFms`T/e/H==OmO@9-s"HXEJORIA<A%=sK?aAu0$<(a[AA64;_Cu[5G6&;*F*Yk%.j2I.XVCIN
%9"6YsrfVRd4HVmq:sY)^0`o-%gRr/qB2/K2<d<m8<S;[^"'dYm6?L7@@Fu!V*X1?"$rb1@e=]Fi:DWCdKZ>_#NfBbJA"/.M$BKlC
%'iUc0#FtH<Me,/ZhIt)]_#+jrF9T8oY>ND7Mdc`EA97)L&\*@b+AuBK,(NqY2i=O\;[ddh6A8c`=X&AX0?ah.o)bB?*d5\6Tp6(J
%#i*Ip(KeRsFj,TA^apZ4lo>o5^MqZh4iJg9Ts1-t,&d2B`g1+qf5DnDXPbCaM0$df0/;iNM6m)i_Al4n6CE!2em=;*,(IPO42B^%
%=Pp!9@'+`!=A8F+Gl-YFYjX0JjEo$R0h8T43!_g;h&%3@-T'fu!\!%[LT97VV-=@PC:0l9Ra(t37A</A;O);b1dj*9ot//>kMFSl
%#\^N]"+/VJ(-]U0]K:+M?*3*2]e0^ug9G'_9s/=!/.*Q8Yp!.>h<?`(\^SE9UBt,uTJM/[PWq?i4Eg)\/DErK3h`6*_i`WO"O^X:
%9pOP]LIY_6$b2FU]VhOPW1%ZnCD=d>&ht#1W2(!M@lN59b$2@fH3/7qb(ki0j9C#]'KDb=1a7`*X^)4>]o<M4-)-N'4.5/]buc1.
%LD_hj(>4/e9p2)A4kmH_ktZ[@$,0QfFWgma6p2n_hbo__,Oc"qROIW_irph_/a&Ib_oUnn5WZ'hE.JInMF1?p9\h."!Y.1rEUs:^
%rZnr<j-h!mrWoT[0WOTQJU7HFQM0<MEEtIa2"q:6d(//Z7UW2I@B$fQk:46upse2X6n;WW[Z[tdf=6Oj+Vb:;j6<sdm*"h=NO'?2
%'iqn9s'u:?2[`Di4QIkr5FCFmic/JI^cuutL;5;NMNC;*B)+'F[bllJ$20kc?nHXP,li=^j_?9E6Z/:\2)<paIOe!7bbIBgPtb=(
%W!d.Eo@=aZ+W4OTotemCF<2BKAIm/@CHtn"e4sp3=qQEL&PEuL!O.EdjEmr9:hF99XpYEIWC0%@doUn;)>,gUo*</)\#\+m3*Li*
%8*/Bh@j9lq^I%T_kA<S&FU:!`nNmSPTY1bM%pBX955cM<IE3fIUd%3D!\!J&Mca3-(se1Lnr5aSD-BrmbActEk#G]?60mKH<q\F;
%aK=>V+<p&AS^A>ET)Q,\PB$2I''Q`ZNt[a#hhk<B*dW#n3:dIM+G]f_%&*JZO%]F?JaIk;$W9fSbq2SG;D',.WPb`Oh^Y+*"Y3r/
%faK(?JQ$2G$V7f?SWo)<7Q$,$*O*TOXUN2sOa*%;kNsGnZ,WFs'?MjCB%SM#5La\R-rP5R-aYi0kKJJ_^kro79P6XN'TlE6`iu'm
%@"kVU9U-On:1?T%[jmG_Lm+98nL@r+>XGcbjpMA4a;Q_&eY4(dha?+BWJJ9enlA(d$MA@H1if[3F>/Gd2:.&=$dO]:Z@p'S_c)!&
%&jJp^16kKY5\TN)G_J:[>_0g7*N65pYF,n/h?9O2ArOm"P=O9.aJ>>&hP&GEQ`Mi0E%%gB!ta\g1$140\-%i&mmAY53Bm9:^[3aT
%9?EogOnEK-4l2o'pkWT#QZGS!%U5`TiJ)mc3\;[@i`937%8/IZ0qI!^'sCNqSt[b=$t=)4m)h>,^u_k)\/n=QH!_5=MA$Y)'+TS*
%%F)ifCI,6]8oR\J=?a&BJ*3cN_[c5<Q,^*%AA:E$BITSMZQ-m^nC3iDdafI7Eg'DiYONBA1Lb0#+>I=.l#0H4+DL-aR2B7mVec#@
%6:k$A5Yn\RZ/\T"!'jj%($daTs,@ZNnIdu@PQb)k>B(D4QeLZT]'m$l1NC/2"JO)?MUVB;M?7HC07JlhdLM8X>M1S)NWG;&^%H4D
%W8fKH^6Z*ejQH]U%2b@L?l'uI$f:_6fqd6mg=<,@5io+'AQF_qE@$WQmk9B39e2?1^nRf*XgoL]#)#t*f`S?mpC54\(]1K)o9_-h
%Sa+6ebN!DsW&J"=[L6OA2rIQ6'10<rbH\nGAUH43?]GirA=jOR#P*=]=.@Tn\[Y9-ll7`6^sl7tE^j\@^W)('I`^R5f!VnUF0UFF
%5t0X:_tKsb9->4i><EMb8irq0\$MqL3uEF!EU&^i3=V4+N-k^Dg5opOS3<rb7p!+W"'S/r:\$$AZAtcJ(SljhXfh5,*3IG0D,H[^
%7(LJ&B1GmhO@1=,KV"`?A=m\W>Zo6Lfo*A%d/pV]5H%^Fb0p@VdbeOJX2,FWP-U2%JcatN-4qN!jr<0c3Bgk9`@rES9gU'+TdX)o
%?CWhfYtpn:6O7^$p^SI2X?mD93Y'!Tn6H*&!^FOfOVCXBs%9"#\=T>9D("%3nap^r]pc3(^1Z@co$\K4@4-,UYQ,N.p1oRA#]NAS
%Q&"U?aBR;H,mb!5\bG=FNJ/XM+i6DN%FsE(j4fcgh9sjRWKsKuQsKa?#`,JQD80*Tn*W-VB_G&AC6,Shlj@14H/IRsTe_e""ehkc
%/Yl"Km,2GDGA$T6.X>.(mC4=h$<bG5kiYs?.rKJ[+coKu0,GV;=Y1W\[UJJRE7l1R8W/VDA0[AD7/u)hiZUP\#S4)iU5$qg@:^aG
%h@_GL#O[\%NR.:c"%'%_>JTb`7gQa`:io\H->Z&/oMD;^"[#2HPpVr!PKgVX;9MVa5p^JOP@:Ve%m_4pWts.3W9_o-7TiUqH%6Tj
%\!>I"&pq>_g)YQ6l<"6X:%T6X_F-]%3esQsk`"7RVjfb9HrG`j.)Qef191f:%1g!u"YhW<2\?%43WP:\<Fk&:GAU#]b]6*ODajYG
%SVBtWG4-2K6mQOL<`2GmYH.sW$L)L=k]I79mDSSL,-ldZR3p6c9GZa:0r%3`+V(6&W]!ek9U+nH[#lhGXYr(aJ]:1k.E*X,aB'fN
%#3Di>#J6<]_f`#LYQ-K+UUh"VL.AAXn;8fnZ$k[ml5NPg0sWqe2U&S3TBb^e9;E?[9J!YYEnZC[R>S2Kk)%+C/U#*Of^;!Y;T5eT
%DTBCD9T2kKK`knid)Z`21:IaoFElUW_0Q,oU@Z*SksaHnnmQ%!:"HLrWsW$,V?&fN<S:I-#C.s>^St$0-mj\^J2SG\PoqbIINVUn
%D",\u6mO/bc<[uUeUkXH_?Y#^+t[8>,T!T)N6!VBNOQ6cJ>=SaL9YW@+*f'lb<b(orVfOPPZSq:PGCVgZ,uT--a*8Q&Q2H19nVD9
%`X<<6$'U7>Kk@As/GUhUVNAmOJ6UTE`^pdPH6(on\H'UCR*iu!A+9Oj&/QAff7uX?1_VM<0kA%ND.CZ(C>_C&U%sMS@Wo(K3=!/V
%,Stum7RA:+k=^@4j#B`^^f4#q0bCiYg0nLt222MN<CTenagULAoO;-i%;e:LjM4bG]D4V]@N>0Rq!d!Dd_DA\ObtPo?<#m-K_Yi0
%JDUtS0a!4BOq2Y$H=*>K[$K'=@a+6VU4@L%**<cQ>c^)/M5H[CjO"i;+D)?CV.lb:6ZecR?>?d(/"'7DNT7M&#_\G'"LR.%TV\G\
%<EMAIL>%OAdC>Mf&Y!bI36tk5Ko_U;EUb[&<lrhrs_fq!T%sD!A!A\aB(Q?BFGn(\<A9-!2fR3$*;UFn5Z+O(SAJ6"3
%DQ%\LOJ\5o3.No3kuLRN7(\\;"C*I\clgr.WNKp/@N\'FDPd,/f_iRuD8jo5F5C2-Bp[,rKh1QaJq6V,D0HF8Ts1!,$?A1FJ3dN=
%7^thEG;aDW,l^Bmg!#%@61Q8EY=sas?/TB;>&suVojt#h;Jd^7[YU:D<\"1q5jfl>".k/;.hD]f+p-0n!^0ZYDG#6So>`qRI^c#_
%<C6>_V2PCK]bb]?P`a_b@se@b&ioA`.gX]3Y+`)!WZ=EPq@mNQe)(`C];1r#dbfhMrWFU#%<+FpfHZnA1DYS9#*o=gpF4F!M#m/U
%>RQ2`).VTu;@AIuTo^$pi9-U1BL>.M]-3OMh/HNO-D`,1E,68TkjC*.`0hu@'%^^A'3KRE;t:RGM>7VXS7V.b>ee3O2'9+s2\9.q
%GSZi,!N;+Z*ilqAZk^0A99(/j00>XD&Y`-Wh,]0gEW5.=MMH"11QYEK/52>?@=4^4VE!=\KGFT,:fs^*Ml;<?TnXVtH4HBgYob%Z
%+N`W>*#H635\G,SXYdA`NM'[Cb:dI,pJ;T+UK+X?5KoabrDJm=G6!ddl!+_.#J)4KRa?K2oStC59GbK,DFfA,NAXPuDUX@T6+tog
%RjY_.`^QuE0iY??Aj#dViR5*;)N31QDbsk@H*%/)G1H"g;&A)KC-Wpbn]B`?e7k5XF]B+_;97U$2M:?Q`)#NL_G)fA.G-BX$>6)9
%Gb-iuf5@?Zde,WW<)"*\AW,OZQ?LWL_jr\,MPq9&:!A$4%^O=Afc<'aUf"[q3CV>8NIW_^P%p>*-i-:76>&pL^tiDr"Xg5*Ws:Y3
%`K1)o1<1?HYG;2&YVa]TFXmnTc.pAq,,%X"/tK+'6[e27&<rr+1m"jC3T3FI\QkGSW+*ZR,urLsetl'VX?j;*\-h$O':I@n+#@fO
%K]\=&k0Eeb9r]a_=7uK/Oca:*0+ta)amC&c%ZTd<7FXu^`+<FNh`pXR7U#37A7[6Uk;%2d=\ndu*7?$L*)%Y:A/1Qb4^thu2FDGk
%.lA>N6;[&oZud9l<'J1lH)r`gXX0\h@f8UP]nsuG&A@&\<Yt=PnHP&h#9HS20ael@d2_e!g(h,[m#'H\cmOAV1E7ZVfU\3V.UCT3
%aiVL-rA)cp0M,1t1Mi-cgC\X[U#h*YPpuJ?nioU=G[_69(Nn;6:I(+f>L0$lS(\P`+7riu>\PSeG-#-"J"MZc74@RTR>OHnJ0J=\
%q]K>h^GZpYJ7lG5^?FMmY\/A^m10TB7lOM]BciWa9a]u>PrYLZX[TIi2O3/K$BkU?6&D>9*u8iN%':\I4rr99'6aP(8C*0EX=/*9
%p^`Js:YlstGq_iHI?R+)k5j]M.WO)OF83l.'KaC)2$l]:![0"p5"6f5PK!WD,5NTr$;M,8?+tSCk4Rb'nLhBMJ,NIKjqkf/jkp(*
%fDjrN^Gj@4J,Ttnrla3toq29X2ui!khuE!!rp#8GrU:W<qX(Kco$;g]]>+@=qqq=\^\r#2`GZF4rrHX-mb?XsmGA$KrqV<+rmL97
%qX87gI(^sO)4sJ!jd1I&55gCeQLaeM62gEd$J,.Ls$chW008No0E:$=rkl)4YQ!\E?iT8ts6V0>VuEkgrmP'3GN.<V]&mfS%f)cH
%Lp1t:71U=LCe(=WW-iSdjVb!a<Nf]@+=LA09LXQ*[4#"d-<+.9W<V=;\lkgcp'r*MWbqj"FKhY:./rU-,qlhU'I!UGA%;1b8LAl1
%go>R=L8nMd900(]aj-r)dP\1`o!SOp^eZohfHqM(+lSo52SHim>$ZTHG_l-A=?p*4?-qDck$Zt(6>AHr3a-VX5ab@T-NMGER%BDQ
%5IR'V':0&b"caET@o/SUU>'V@&HkIO8>TT#fFGdS@!3>QO9'hVgUKqCXu6TOR*$6HX_JQ$!bo9V5nQ=fKcVBfpTmPm+l3EskjlB0
%<t`YA;;IpN+U^bR^S2&mcX9LfaDS5Qi^J:)^cnH6;1LUDaB<P7.:p"V4=rm&NRsF@`'?oS!JHY]c??GHD\=mm@"5HAL,+fmDAG)9
%Lp%Y]=^bA'3In>P0ub@G/)VS@^j;.3e$Or#,)D3Q0'$hB--(+&&_7B"ZY!BMd;:'i?djp8hjsiU"\`002oPQWNeUg`q\.B6-G%4I
%A)g6]@t2u.rAr.lQ<C.8S)s4^iIo>.T'cu,7gR]O@GhkF9[=0\kANP4eZ:rY+6+#JcuBO`NQm;O`@m=Z-Ps]+N!a'FFV>"51=6@t
%'XL2KCb1=3FtB*p]3rm]o7q.%Z7rUZ-hd[d[[/"c*(:-sEj+1kA7&f5;ZouHUWHap6q5@Rl9jG7f7l'7_CA/9!HHK!$iZj%:J6j'
%l.Q_-ABtls5f$Ph0FA:(gB@pj.fsuU12<407\G,)fQ&Zb"\dPt-Y29F^jjLJ0V0lb&R:)-QaBt]NTrHc*_$\l)2$'oq3td*K$8i>
%Z<Xl8_eEBLRp%R;dC(DH_Bc+X@3SRrq-IG@Zi-]TKe?Y#!,T!!O9`9Sl/uN#+)->u*<7L5]"<&<LU)!&l;BB!WQ[Z[JI*9&I4sFt
%9Y^*a@?4XK11_tQ=B578)bER1aN1o2F0&ttOZ1Ys+ZRpdc5UG+NS!XHa19^c2]te^j0k3$mk2o(n`(=nncLr.kh619L&/;3Y%$7`
%S^G?2T1.nJlFl-,M@-8=KAZbbVuh5@gFQUQ5[5q*KJgr!+:`)<pL#,Wq-dhJBnEE!1s%:-&%cA:YY/SQ043E`hu.9b,ibdX=',?m
%<EMAIL>%VOu<qB=p6DT`@'LTn:&#4cM#@?TS?%#$#&SW>l`n3]3/LEO6oWN_JO8>aaNduamI\=bG6Rpu%$3^WHSM.a;-
%1@,gOk2feU)])Y!Hj,/%;O:kW-pCq6Y#<H#,>:s=3.2H%VU9><:DU+M[80+)Tq[Wg6\ejBYuiqI-V$S$gE^!:e%4$O&G?74ecgr8
%].[$>A>`D6>b9nL^!c'j!#MDcKP3BoK%k2#i"F2E`@m'gQDg*JigfBfKRVS3Vbso/'Ypf.+otA*j^LA%A+/Vbh6(')as+N(?F]K+
%INd_j$9C$u/[k_^R*6>Mb9<(C?h,^tnLh(#:n2l&[hZmF!A3Od=pVO%<&^45F+H#mXtP;kPIKK!7*LPHX"\/(6*:LCVPWDlXRqC^
%3X>1Al^+n?BXGkrFueT?na_JtF!qQ\U&^,l@Z&:%/"%T/k6bD39^k+D<FAm&ogYFu;Lo5]a>]"M!"U#h=GDm4_"TomWQ#)P^c>>V
%JrF1`%:XA]iU9l_-S/hXlLOgFjY`_l.gWqU^B*IZeY%aCK7`L,dTdFbZ4k#lS&K,^BuouU<G<(gR34[G`$5in#2N-)UkpA4?OVlA
%TbV,=U>SDNOf?C_-eX[(,A_Lq&OA6JH^^:M!J8VRLduVA/PmLiJ>C*[1o3+mZO%FK=7D.MdBj?H9u4G0etR]a1)-@5ml[S$*l>$j
%8&G]r^r:4\HuNoa<\KO$<7k;>\HNQ<,ki,;[3u`qX+IMM>aZf^]s3aLFINGNbGO0F,>Nq?9*OtREeNEhLjU<:``(i=G/*:XBP.T(
%E`h=+?tIu%dVM)A9gIl(eA_PS-`p9!GP>]dJQNj&0IS/8\CHf)3Duq\'b.rZF#FD=1)K@+"5"g:a!leeN27H)TI7eD!H)so)sm86
%p]nm08@7s#W=(tVe.hs?EI5Z&NZR9!E$_*h+L41/g<UNRa)-FTJ&OjTcl8aKkG@PbM#`"L/0%Zt[b@O[b@`\oNd@g)B[1A?8Fgin
%[qt>GKfsBEUm^e57,U>lbH+Gih`[.jRZlS,c%fJ(4cYS,k<U]`*l)"9qK97**9Nl5HT0MGc#*:q(&^BLRHb9Ge]7>RCB/^d$ndi9
%&X,b&N,!]\\WR60ZVX`SXkO=Fq^I=4L@pBM)E)ZKFDLA3FKsR_qHXn=SYGIuH]"u]L2hsb?.EeX/uArIAh%],_rutKiKWk/"#![i
%R:@aF;&YK3Pf"jF+7"s?IRub)SlU$Ve,W#i/N#BV4*qe1&(1b,f5/=!Zd7jb[HTSbU_XLU]Y!UMrpU?I#(j^7T<lYOK[YXOZk@UF
%20UUUR?<*%UFe@uP3q4g,5<Qd1VuskC&+*5.BZhgMiJWdAtAXMQYuWe;?0ilWYC/D(tG+_Wbe#Q[#YYfBn)+/W\DdF-/m<M<2`-+
%1?2Ym&bT%\USoPX`1i0(&>kSJWg-B1]jYrm+^u:T>7;%aW#qM&_b@T!:A5IqqClH`eVoQZAq_j1F+@SWXdGEC%GPDN$Nd4b(tWLl
%oi*`r8&299N6<(C&>b5=!A,YM#\/=d#V(f!rM+L[Qg[)C=rpM=JkQMqO!f!&R*ql<?Ku*N#NY)[pnORbld'?'#T`?Fp20RWdJ^35
%ij>JejemXO5VpS3GH;Ss7/m*t@Z=HI:Y+&pBM:NT^1KpOP>uK-\C/fQ7PGPjnZ"b/J]jrECP&a>l%/eXq94>WWW\t^>U>lhSQ%f7
%XLaN?:ND2%Pe?DINr`Z)\X4i_UQ"T]PBcA,]5P<BjBnl!(/g;9iKDkD[2>Q0j9gDsotup13GWb\.8-"X5Z4P9=J]=k\AtqfCEd;A
%nlG+DjrJT\@V%U;i=:CFHJ@K52Asp??n,0J>?Xl<ZIc@A3'1(gTUG3K"$r5$#@LqMjg&9:jQ3+Oem(Pdm!rB)`\q26^^:K'HQ>Vh
%9B]I(%Q4\l4BYr5Qif@kR%7Zc+S^!u"p00c@puC-]j2\B/!DG2i(U?9+`'!./:NjJJ7C^ph[P^mh'*r]7O?8iZ.jq%BWbZPs#fF_
%a<4s3fhH!kYmRsO$Z'8hdaImLQNXrELD.@c-6-bMK0*V%5g(DSO(4`!PnLTs6tq]q@4!T5[$T#&lkKtmYmfabZL+Ted\Yfh8kcN=
%:iU\<6\<*56plQ+DAU>eQM&HMFpig'L!--4)UXi[p%nsFqB`V'g`_!Z&^u/q/DQ_'Sss9_B-B_6plkbH[VEln)0/2Y0`M^'4E$Vq
%#mZ=T@ZJ8L'Xo8uH%N=jbZUl,!tf&UT-H:W<)X);o"+c(`i\PC\$a'\fhi->'?Q:'2^sCWBt^?$i8[g]`/Sl"<Xi"Q67]@SE^uf3
%\`bIfe#Z&Q!mXa$D*IX&F!*:i#(U9rbn8Nf.Q#<P+bUgoanHr;1@WB:OI$#T'ljX2<G[eddY$$s\3*2/_M-mClhs:.UX=7qd1Fkc
%:q,lj!cE`tmA=!UBQbiojY/EI@X"Ldaa6D$9+:oJGZbG9'n^q*3Ri.N:O"B>+X38FYel)UckA3/Oiu>sDV6u!4&IN-VUSl-ZMY.^
%h^o%jjF3TQX:b+q3uGTOjC_$SD@u.F-?(\S,<CTg5PD+O.Afg>0%365Tl&)XDqsL!,2X6)=^EF,^2J+<b_7ga3c;ro8s/R:;)QOS
%RR1+!G]?BYE@b+c!CLt).l8ad?uBbpN5/n'.Yb'U'n/o#]8/_2HeR#@CU<.V]tAt93l>THC73+Nl*S?D/1Nk]>nb&\`FYTAi=pHc
%<tXo?om&L#R?^#@d"A>nB1&fU/7Ol;7:Iklq%P9#_]uJoaH2j5I625j0(DlOokQoFpnf#MWsDSPQ<`*5DMIQeWBWDDPTi-uneDC@
%K;)>)4mBgDpc=u,UH7r$;kdk8Vn20)7Qb=aC_O$V)'UbB;Xt)<(^QP,l`T6j^4FJp@^03QUe>jWQ=Ah?@g_0!cI9R)oge+d?2$*n
%UdQu?$8??eWqBg)Ljot[]<M7@,?47DIct2Q0G)_?G1(MZXr3rNT7Epsh*@\gA4?Q/n(LbG/8VNN&aRd^khDSm]j-)]pdu)2'PJ,>
%DPc&WLTZEPbf0.19/J"%QFgqJC?ne)9c/LT$mVLXpT$;WpgGj(_.+T4Fk&0/LHpc*7hi*JVVI\M$UPo_YtTI6FL?/?b=&6ufM,o&
%#-Lrc-P-LE&>&hum<q[JBe>Ac"^<\qgR)GXeZ_%B:DWbOKJ&ui>H)E5@D[jIh"$06[.]"+reklbajr`%&UeQD=f."cC:Yht@4O]m
%)dB?%NPGs"AWmNK8)XHlWMcY6;rJ-h*EdG,lmhoi!#NL8OI5!`Z'<VY%.$M^NbD8nrG,;h4YFka%8"_0MZicChc=A$3DkTO#Od3-
%cjS9B.^!<dpY&6$ZXjA%1m6g,.9(/.OR\<L$L]RbN%W]ub8u>85ja>1E\$aFI$5jrV\5g\/d/$+Gp$>HFp6f#Il9VVZk3PNag>'b
%h9.==7@T=LmLMc3Aa.u/IAb;s=Y:&925X?%doDqtm&L&XRn$".0-[JI.RN'oaj=GIlF>s/F2J#??o_aD@lqa3@?3A+XI+XNh1`h]
%1<iMX&ZR6n+!^VVJ_,g-R`R"E*6]jNU"dJ\OW?jlF@Ck>EirDI"ZL`U>mJ6I=T^V4DEDC;iMFqV?7r'ES#%Q5(?/`&7RYn):N3i8
%%:GM1YQ=(%!J)Zf?j@P&BtDVV-f\8VY?2i2#._TRn\dA,OJkqk[[:dY&G`\-JnS,uP>EnY'"2+*T$Z;Npf>&Eh\]BqL!+.Mh*&O^
%$YUYth'+/pF6l#O*JelW#XXgoe\8<?)A#+;o-K;ulCo2C5H+7DY>n:HZWAka],hdQ)b8B\),#WE[qd@]G9*"Q[b_A9M=ps1dKj-c
%IWJ#i0p#Su:4\8A@IH#g[K1"9S^#=jXW6<UO_dFTI(I>IjQAIai3HT#QUrp)_lO8Gp_?.1"Lsq4R"<H?8M=D@Kqrk`2W(5RL,HH2
%gga^`'m6F8K#NEO*\-,hSRc<sX#Cbe/fV=RO>bg@A14sk5Y<:;H+Jr,Trl.>="llo<!;nK<gD0300$?FJ4q1bm'It7_jNoN&1%K:
%O.9huTY0bXi,\;m,Pl`GP]Z6r`BD4u*%>*Qde940-C[l9Mm+S%Jfdr7L8Eu-'(t%G2Dj`_=B.uuW(4+[LWpBEn(o<kQ6D[EDE`Z+
%apPO$1EaFESOPmo7LPUY/.FZVk7#@1Omuf6e:9lK_J4aE(R&-J;eQ@JX7PM_4jRRMBaT36rgrLcp^Q<U[+=A3U0VT-OVg_@lpV$;
%><PWuKYhS/p>*m(/)\NlE#TW"8M88QCub(#61]_\LWdECpHm7;i!(Y)5`f!*Ru-=pDF`9WEp(\@mRZ\M-LA64f5PbI/"3UM]q:h0
%rD@Pkf/O?f`R6IQ\sCCTLMqUNNS[7]_=f=))n2s$;O:S*b?2FAbD=.I+rIeu-ndCp*/Hj>?&jb=Om9YLif'6:b5a3o.9G#f%l7VS
%[\SdR>W]!dZ)"c+b-^=KEkCdZHcNE!Yo7C5,PhU754rOdRR=coi9m$AVH&8#cK!ac.4Tkujq\JbeVRo!^;,*ZB1;L5\g;J)g=j+U
%(]."\#i46D`\ih(N$;hiWRLI?-_ind!('bCW^_Kec/t"b4S,M;27PnadQ;t@[Vcarm$i1B$3_)5G2if-FYpZPUT*RNelN9\"<ST.
%O,hY:3!dWm\XPljKj_+r-5C\8CO-S_>W,]QR=32Po5Z*n`h4<VgTWqgAupu:7&[ng$m\&5^6'P*dgG!W-T;PG3eA5_,c]l7b89gB
%qfZP'NG49<alG,t>X9Tkr1#ffY`>)!'hgl@@H=Crh>r/.FsQ.3)p@i![*j%U9itpMR'1*k33=sO4P5q'g!F>+9LaB$!QHnDA"OsD
%o*>W.V''jQl0mPK@/T)Zeq9'84//TiXrPl+B;<KhpCZ>%Q=p)-F:o.<)AdqQ0>BAA$Fc48n"R4CXlFqO$2RRY1Ob3`RZTlebVaF"
%XU(DA3-g&2\0+:m]<[-Mc#1m3*KC],@.!3J*!,R?OJH(t*(7iCW)th'?A!7.L\Uf5q[eYo8sI@.@#Kh3]AZe4ob\%M1a:pC7XQfF
%^D@mp+#Qu-<sA6N4&54ib%q"hLYD9En"XBlX4=*f&`/G+'mIiq8'hEW*C4DcEq&HYA8)0'<HQL.2iG2@R06LaE%W$+T=JJ^HbiER
%*i*=))NE5Y8-*+%Vm)Jk3$T/!%9%p]>H<H/L7iM3-Pa3t:'8E@9Bl*YA4%W>;.Q+fI$;5M0GGjjDOQ"[AAggj(8q5BSjT>n^:;uT
%?,\LZn9Y;c?2S0q^oTX[+I^Mk!8^T-/.UtpREBu\[G;,HD:JZCBN<CohN5<<!b0SuPSApZ!,p&NQ%P0$IJ<MZCAU=?a76=@-Ds&H
%gKC1.9^bLgooZ$B*#dEf74a:R'7'q0i"U;NPs89L!f@Hl=%,Q71`BSPf'&;G!#(=]VCXC;ab?dUn9KOO7;9[#^g\.Ao`oDMF<Q#6
%XK_%\(HJM,@Ik!u3)^1PbKR>9k^#.]b3l1[7=PgEc@&71,LP-!<m+i/Ge@]YYWB@Djd*o4UF#,Q+Xrc]jdldC%+Y#U=QG0p2RdIJ
%KGfSA>+ga,!5XUM`r9*uh.@=FKTV$]G.W$hJ;g?X\Jt8(Rojf0<6kfk:$_n83s%[=M\%1Nr9%]a8ttuU('-b-I!C0Yg-3Te;WBbT
%nVt7jm(b%.+C-DMKshrbcK&*fpO\`N#EEN.n_?,/=i'f*Z;:3mb>4Z;QDtC<$Ol;gFR\"%pLQ`#i-MdJ*m8rdr<.bbfr-Ml_[Z2+
%Yt<b"0o;u#;?sF6*KaQ7PTG>t3EBPpB`k'68V@EVPDjVX%C^s.5TSeQF%NqhV#]$G#+1Xp-Z`8YPNf@iA*9k[)a?ZBkh(-f)QC6Z
%]im5n`@25p@,r68GVK3S<YI96-'ac,)`F._OZB<'L5WuR3.$@$)mp6SBe$oKW\1t"$LtDuDJXCD]^<h(lOg^$ALH`/r,>SRo*B]M
%g(WL=D/-FXDC\YJpuS?La]7\[&.5(fAmH".>W'+UU7Uq!P_GWeG=Y6JU=o"<SCqoU1c+aEEoA&9KeWT,-9`;iBEgV.^Yt@k>8m(@
%jA+WAOA!4YZnW03KPt[&L>m`EnIB#CB1qKTmap^1j'a;f;%(E27L[HW`#V-3MNFI,h0t`5H0RD,SeF"D)t=Z8N$.C\Ie$CtJ3B`a
%kGIV`T^`#%,1Eqrn\4X/3:WY$^D"8cqHX1mVdqjd=$X+"Sq6ZMVB=pZf6g'mmBkriPibN/$La$=7FFTkb,9)qg%f1e)t"(P1fa+p
%2:'\9O\o;Q,56OV:nlbT\8Sb`JB12\Cd@3<KFS>8T&Yum$uQTm&!Y-uFO?Pd"Va=g<WpElp1UalSknn>Ft*+I=ija\IIm1$0*1bX
%R'Y("s6:,8#bI6WAf3ZqiJPA#`toSYM5?$N\3K68*#13l*`8aAWHn<Fm,'@`%VjV/@pL^:eZfC#E)H8WU\;%'ijP*+n52#n*Bcpm
%ao-7ZX^*M+M3m?t,m+^!GDK1X756U^kJ%JlU?piO)&ea"<c_T]6G4WLUiGQfPUB)58N&R$`PfW,,P9YP2ss$6@Cs4'fp1tToP!JM
%4ugr?lZ<bTT9`k?qC+'8X$Q"YpQ)=hE\W%k^EF0d-?%D*;t?,8l[k*]Z_1p).+?fnYQ.C7cB*%+`aq;pSd_9pqCEr)Kp'r#bVT.R
%`Js9)!sg.j:r!hiXQC`"ToKEf\Lg#L606-B"`pU>M#cVqYGU[X,!mn.m;!<\*,YW)V1ILcW2&H=9b:W*D%B"F0:a/[h5O%]>t=+<
%WHYX9d.1h^9mLsIF#jf1KTFIeX+4;-;I]jFY*i<mlo-.aF>f1!iMTAo!kbfQ?U4kR&\8j4@*FD"KcK(4_RCWkPe4Aj_>/61o+$qa
%-NAcSCAV#tOA9aZDe]Y_BYr,&NHq=e'c%2gG5mjd.I*@'Mr,n_#G$Tb49AY=o)S*@j9^cLL'7O.f*T4A9q(T_:d*0XgXX48,h%`l
%B'rn$<Y>/LLg,L9-LLa?O>G3.YT1c'gHl:Ck.u2h&B4^W2t$`\(=u'd9H8[?$-QIs!E<!SPGi4YIkF\8`1ubqI5`!R&'c0?C+dC]
%(CW$I]Il;!$R$lnJ:t:?.3q'iKJI!rCj0Z5_p+*HG88pe&<:ManSSEVIFP>_U8*@p*T=>D_OjqupeSqFoS8PI-!WK\&?AZ_/c,dX
%)E#m6rVjRqTCX2mJ*8u6VggFo7/ghtO8iQ+l"@Ddh?eV9BfrSJ5X!NZmtZ^!jiu/33-_@]#V>M@;1c;U"L&e_S['::V^u$.Kch1<
%[^_VoK,O#bgHc^[mPe!>5i-DrTXuq,l"\inlV&SuYb>+3c3%L)BQ(ffa>]dQ+p"JCS8DXi"GRMM(EV_t.T&7QDA3oS]os?eZ6H\*
%#I=g=UNIC;20<^YXgtGClh/,VL)R3iA:;UEiS6rRjAq,7>diagl)h6d48/f<AuFRjkNHA0e@P'SYNskHRG2#+8Op>fL!0;lO7Puh
%B@m(4E)bXN+qqd(%Wq>h#Il7s\DY:<W4odr=)K2dP#)Uk+EVfIUm-FarYSu,.`5WVSkAWQAU$Q*JpVH5,u8(.WBF#Ao5S=+ncU1c
%>PpV\DgOLRAW6>-knSXJ<[$9%*qfp3/r[0K6s35?[%YDX-D-(\!jMQJ^S"X21"Z+o-)IWM[!G\&[O-t#mo&:'e6Brq/@iDU.lg$/
%'Gmg:.6]cfPPLfW;+I,u2T2P;+fa;rjQ]2=37iGsScg?C.1T&?(V\?6Ys`Q*[pAs:H`N6+h9j/FNt#bW@SIqS"SXO(H6F(*Ki/aF
%apOr"=K4bt0)6B82>rj(qf%uaPa52P?YZm=<N>@JJElO'%2^=)d#:dYR@PcYJot/.*@N^I"F("L`A'W'C2oEQW(amh*47aiMp4Y1
%Dh;)kYndVQoBhu&DP]Jlr0^Oe?rS^99%ULlk,@37IiioNj"_OJF.,ER8A-9]Q[<g!DslbhXLG`Z>EU4W;rg;MDAsM_enj<GK_FZ\
%-no2u=M&_]@#Y691h)l0`bO:IjB;"W[F)Wq#qNNX^]lL(,=Q.f]K;;g4CGK`hVH*q`#GM='Y,"]LaRhIqCP6.id31u%ek3,5PYq*
%*fp0N_U<iSEj#Wj!PLqqh"aX&gf+k?h#9s5H6$ui%"CVoGBHuElr>pm9,*7,*#FgXU1"8?C=4I&2^ipdLG`E`l#QO&GD,C[5@81p
%4jc#b,_\r3(G/JM9:B>L0$c.Y_Pm9c>"'r=RtRTcqY"&YUYVe460HOqd**W\+'5a2"\CR\)VAF.E#0He2`aSVG)WZ4`t:F1X&])I
%fZTD4#K$SZlP)?b,<O9S>Ho+=J1CZ%0F%qW,t`[?*HP#SDK!X%;a2\,DO[^1"<Y(43CusDpFA:"2>4f^7`lrNq7:p='U"?P/<MAa
%do=lc..b&^dm/DFJWmqs_sAe`;M$QP*9c:7&o?<E!R2mATXd!i_GX<6$&&O`MRZ,a)WYsCfp1W-VWkf)Uad9EL39CWWQY!oi'u.Z
%lSq.sn$BlAcY3rH'Qu+T!1t_2+"nc+:NA=TX@"I[&a:Q)0uh_<9cRhN8;a-hR4*4"Yp\Ka/e::7%<9'!$s*!77\`Ld(Ra$'CggX*
%d`WU`R3X)j]k#5pFldLqJkErXE,"U2Z$+d-X/U*I7Nh6i2l?%DZR?6YD0WeH-rhO;gf0=kUsTQ])VugldAAfp^k',^j9VU-L0KNe
%U9ZK>/p7b*XUV7'"i*JM0#Gg)$Cf0WXWQ&'K!R>dCBG"H)cd^%kY3'kFZHVi!dJk<kU,5;0S$L!.1+XF2tH7CYor=oMd,OcGQ(lA
%NSW_kD+F#Lnh]7^LV0"V?e+K-_();np<n"4o<!aAACY<gN0!N'Wa@(,rgrWmb&frerGTK)U?FdV+'g]l\LLDu?UCA\Eh%*nn8lk!
%2NS]KR[IA6JNJHE2lI8'2k$m_DNFsfO$59"0k,%lduXko#+J=Q^>7Z^/&pQg/5@;T3BLJ+cFr"OLKs0Lh>sYb0AoPLbFT3.]6cD:
%BQ,=^h7^<=LUX$_UpFk`\iC1!=E'<OL*$ZSB;E+m2p\j/3]K+!I+%=q*S)!3Qq0ea'Y<j+auG+0Wp>ojn)j*jPb_fpBD@na=mMnR
%L/t8<8m7L7Qi=5,J;;ZsLrs=t(U^,5Z3qIAQ53eXN%7k9C*Ou:<Aj?"F$Y7glAlirST5<o;1?`jmmD!67eA*l:foj*=hTejm)W[g
%e+C%:\ZA`8,i=BH-uGW#KgBrR(]V]"+6JZ\W6i?i4%csiLsAO-YXl%9ogZ,#j]mluW`hd.itXlt2Suk,[AsbM#Sk%]BI?^rd6T"J
%cJDPT%t0_\1lFt$qV=Mu3N4u,_@1$-[I?S,^47443[s2WhT;[j%G2D^m1CY+'j0/BQ35V(DbMuCl"gEsgX6:B@>&*2"=YifAbrWj
%_$UW/JTg;_aue)XanjNJ"b]ZO]qGa`8L@GJq6u#lKY?#Ol.MVY5:&CU;Z#P.;cX38OTGcL6Yn[hR<ABDmuVX9+*Wq2K7HFqiRY(G
%WbW8&J`34TB<.7L1jp,32fc@8U\BOmB<_]eYchhU+;.-@4QRcRk2M33%A0jto1BAh_-ad%*f8-WB!u6Xn/F_K.`kt4eKjg?W!YW`
%J\lHhY`?![`1=Zq;6t$#%C&E]SkA_jo2[&=lmOCpQj<Qk"tiang;GDr/n56'lF,Fl9ikCIjSkaZUN#+eArpVonZ:7b@-1C`oe$$D
%6V,/T`u)V:'l[4(m[]AlQ>CY_a%NANXZG"9,CF[gs2<>sor#oP5UlWWO>Qf::?O^JeC2Mn_fsqNUemF$P*nG_P3e15@2"[Zfr]m!
%RRE:e:DTI(0hi<fZ8%`3=>rR'KSetq#"hrtY+f-D'J*0m-hB^9AK0tUZ5o)m6#EB1':$^L$n0bNn#+[1Gt<+t\U(^3r?7LZE>XqN
%7L'E0@+o"=RG\8`okE,.MirpR.a9$ObXr9dW@m`REn=NhVl&1tdOc^S$?ZdVr=ZM=!"uKng4\8Br(Wm*mmHI<142dH\f@Dj7OI`Y
%kdU*t_QWhRmkr7nQpDK^JpV"c%4?3>&<=]*_5PE@NY-lqP+<H>K+u2eO)ULMB3U@A!@ItXe9qFnkJbK<IO9Fod1('+C<S8DPr6%I
%]IZh%fbAdlKW9si5#IY]]V*E\R'R/5-GHVYnKG[1^[LT2S^[0:,MH\Gf][$4-FZNrYnaHFS"_P\d(s4))`4Bj([AA@@E,(8HQW-V
%boLno-hSe1B"H@b178jp2Bsrt_l0++U18rIG\W!ZXt8W/(*rr13.o!Q2nWp`+]]=:KJ!_[6Q[/^)"j7`@JW5A>([eQ".S%;3Ja!d
%9C;:+2sMd,5AC0ZAJtFhGo,p2bK%3d%=5[UD[ZZkRA6sg8\)Wg[fc"\dE5%&?cX.-[5TZpWi=Xt>AcWF^8fUr[WXM0<3BCe,/p3U
%S^a:fXgNLS"&Lt4@_]b[osQuZ4`"_iXPU?E2a*XM.=/Of?a%$jm]cn-)OKEbJ.7CP*?9+m$"CeN`G;-rd"@Lu2K,5P'Yl(S>t%BW
%]9'MhJa$?2jt<C0X&8b-T^aC*/..)GA4[nf+(-@5e>-^a-#'&'h'^"/1O:jJP7nNE6MFIefKKS_%N,B1r3bJnegTHQ#AYl0$-eBZ
%h'WKcXLn^iDU"*rl*X=%Qaf@*X>NMh#!G(Ki!29CCa;ta+p`rbJYtbC;M],YNmL0T`_N9dH7rd^e;B+<cXhBRI9ecC5cIO1FL$3:
%i[ZYOfqdWb?KMqEqjDc[C8-5V-3Nsu05$N?.`7<AQ$W"bA5i3;Y$'7dPrFDflAf4'MNG3IeZ6B_SYgTg@Lt@.>)HDhccYH86S-gZ
%_=A,EEVme.c(GTjkrlbc!HcW7o9\.62`>tWC9Q.0_n^EUeHp/7E&0ofKd!rd\]gBepYhJ@?1OR!8u6o+>gr4%^CTl*d\E>9`>E-T
%VNCZ.:DW?i+Y2U5*LM[MW3]Bt1B;%:"ja3Y'Z4N_6UUCg)i00[`ktqs(5:0io7Qli]q[Q:k#VR9@($/M9f.L05=B2'r*/MG_;.h,
%=EK*7l!N?B4\fM5W`Qh&\<;,Q+sO)];QL09"[E=/=-$11[kRqEm27&N%gfOfUWT7an\U+<WYlU()4<Y'3TkKYX:[nkh<bPcGHk3O
%ZB[ijI=U:A(En@eDD-!(o7\@p>1>d+'mcl$:SC0ZTLVodmf*^&49MH,$M_*am%OF'FL(-!i`gF6Ak&1&I%gZVMnY6GM/"*HHh<%;
%lZKO->p1TukZ)sKrK1@NN^0FmdC\7=pLX]EKWFpHfY6I-OD8m[(*8u4PDGsc^$/F-.9)-2p_9h8n.DN;B'8?[T:>6UJZ0Mcg5CXs
%gItTue+H%X-84R^;9o#D(su?2=$mDpZ@PHf:<lKkHipa2PCQ'jRP/+43Vge1h"V6#Bp#B8kMFRP4V7h^B&n*nq?\?r1Vq_."(L,_
%B)cnqff'L(dKT1AVlEc1p+ZEA-r]!=N7G`DXT0E@(q.qrS,-GA3pnsC;>OER,*Al441_pC!^[NrI#J)"pk]iN\u8Jnio)JiM8Q?I
%+n\:fi-4h7cTSH=&r9e?b'Rh7;ugKaBgK%Mq@p2u;Bc!ne0PBLGBC,A1[\]T#b+Df>iYRu@%qsKK,0#\JStsVRjN$_/#Gf"4[tg\
%.!e?CUG$(2c,8gDb!@A]91jt)hj0IZgmu'n\"SPY..h#e\MmTEW5\ZG>gidsAbT<6`d!ApS=0%<c9Xj\K-?O7O#ql+Wch6j#A%*J
%i+P)O(!:E)!98\bPqmqg0W<;Ej5Op>:a6DCYYka+%$bMR3;Q:`FY>HTccGLZIU.%AW/B:MG01(frD^OO#`L<l=`f):n\,.NW?cZa
%,EVKF@r."k1$1`4?6$Ce,;cHHe`Tq<Z7XF\K[O2EKlSnh:u6%g8M1@C.9OUaGLFu;'D%F>I6#ks@O;><ROJqePEi\f*N_qcAuq,R
%<rSU[dR^uT6:__Hdte/\mp^JYD5f+aj-Y^um6odo!SSjs]ho%$16K\Q-[jamZYkC-II+:!mgdVf:hF&!BVnsFoHfLT9lK<j+fLC'
%Fi=5].:f`IH[\Ml140+l;@3^_08@%Xn/ZK8QQB5cD%)B1=V"7EQe4[5Ua9ooA2GH5'JR!k$\Xm$s8<3i`\+\WW@Y;.5c-^VdUNVZ
%;T4mQ3ob8RJeN0=)UU$DKs6RW_Tt'tO(\.b]Y`=e]i'UbPns/DR*Tta]1C)%KG.$lkhRfB/i:>t>.>BiRp'6C;%>r&Wh:7B*4\1N
%QH*QZD94Z6V!(^`)H9=F+%JMu'M\1f)p`>PZnKg3c(X;$Ds\n1"G]Q,%=%LO6U(eSI8b!9rZ3%-U8:3B<`!T<DbnQ=(Foc1:h4t8
%Xm,T"--dfL9!N?Fd"2+9r&.I7KJNbApc7ZQrM?g".k.N'&4j5n'91L>"O2E\h)?BqEll'N*\4`Hkr2?-M]aajOq;60ZO4C4>OZeZ
%(>q1Tn3a>Z8UNpM#Liu/3:RF\j4k5k&VsS3]:iPl0#WQrmM,&m9MAO"$GnsC#\r#GV:tTV%c^u"@o6[e-Sf5+?kghB+(HAZ115Qb
%3mCmi11%@$XF]N=716c+&MeS^LK:N-FeC$MnZcueg;To_-<<$#E;O%7?uWE.7i]n/EacC))j15&b9_#(nBQ:-TP`@[s$,j=![?:Q
%Wok0r;u;G`l^E=`,!,]1bf1>="[[o`W6e)?4oO'Z>8#)<2\3@(F1n%sQDGT_=TjjbAhX,;3:s>OZ[I-<oBid?+EV+sFrBIF=n4Cg
%E,uD?dbLn^&pf,]1:bN_(6P>ubDf<3UNM%J^(?FcR2`2>!Bbf:\S:;c#pa1q\C\7%?o8>BplD*GV_KFCF0+jG79tt'B7qJPhO$Iq
%k&/TICA>7+#g:0pHWWGF3J\n9Am?D)^2ML)lMo;E^arA:";[PpJCp@4DVY"'nbMJ&"/a-Yh1Z/:gl_i::','h*#+b_]O#n=>(ae[
%B2lm`M=j_;_WfX%&sD:5%UD[YLrsN!AmRU"lAcDi.Zg:9W1&5P?_Pa_22&T0.$:kGg:!>i9Us]l7PMAb'oT>GC1oP"n<i#]Kc7eT
%Bj(LdK>J2+hW?kTHHkO&,'*!ZbQ:@E,nH<W=1@m:W,dSTK_^f4MEgK1(F;[-MAtr?7W8T6@9u7S9nGhd&DP'Z/M^ZS:&+(=/fY-M
%?,6gCCQZN@MfF_N_K#nh+4b=a$F=G'kHcJT&FJYD6gc`rS0ruc+MKpC'fk?f<oSeo#&:`%AQ:Pg6G\$DiV>7-6S<kEPk+W!jIk\S
%UGSa'_#_/Cs'n3)-#)Jn:Ib$a?<DmAN8M4SbYeD_!^L=%X;=CZ&MI@.??b1h#V=:A,BS2u&@PdpM=\lA-d'VidM[h[fQuNmg$m_G
%4('d-53hKs[rEM(3\Kn#V=:@uhGK(nD\Z&?]tW$#$WhQ_il/QQ>uD`Gcd;2?(I'J9.e9B'HSBMgIf!co[AT&?#k"-l#AL?D[UIo=
%*7cPXRY+M`=YKTt$C5_ZoJFrj!7nB.bWl]f3PKbf<$mrE]1kLf$O?kQg@W*QdrH7TJqrI*ig+"]'[Ral3dZMmG%5RT7m_6m"d[06
%Wg8!G$']K939Eu"]T#S?1XouiJ`n.kJ`9pELPp//?'8'D"F$esJf[YgE=hqJ/0m/7KkHW`O\"IqH5DNPVd$LP?F7Y0N/,Z;%G2dK
%>E\^YIQ1$1!0_=S]m1`2iN@hY`V7eg^@+;qMr`^5kXh%t'%R[KjtM0d!mZ#nglTOs6m!L)/=Q0Q))8/u'@nY<($^5pOstCnQ./Yk
%He'\U2*fZ/TQNpO9CG#125ZlT.)rm/akRIN4Vbqqe15:\f:EF76ug>^b78m!5'Hc`j\(R7VP4b6qU-[6!BO;IK&D#K.VT(nQO$[>
%MD'lp'Q>oQQT+7.0>6=d],<FS3gNi)KdO.b'U&/fR_`8hO-&`<^:bG;/rSe-\sX$^\gfs4=/PAV8ju@+&$s9o6Igd+6?Qak5t.?A
%7qN*)_sU3e):1l+ESn"VH?!/f<1NTD>b!X8n,%(X4&fGliFdK**t3$1L"4KArl<CHJ*>PR)!dhL/8WlF-Q+pu+%"gAgSf;>Z<#<Q
%OF;$q[&o<SPZuF;dL-S[]/N(EHJ7I99*nZ,G.XUua;!K[4K+k`nK[,_F+OaX\bldp"a,CmKS/1TBtakMULf`5iaOGQg7F9^<!:<h
%1W&E#9\4R1\>bnN\$(409LY&nc:#<24Er^#1I/T(Lk`l\^s>Uo+O"YdrhQ2j>.4u`\W=0'E-4,IdFRDel72SK3k"Hp5_^j[GflJA
%g+nG&#$"Qjo8^Uq/-tFhifLLaT]GM>>gOt&.13O;\'iSBmm+Xj\u(u3+4lY"WaMbPO1-Ju_u^/09+@T-72Dr"o\r<>(G"UE@jJHD
%WsWU$4<=AsmgKn4hOa!dUIAsRfUnR&?q(gE8n#?GY\Vn-e%T_36-,rr]n,aV\"Vo@.UP"gXY-5PC,7MOg*$l"$58PNN"KCXDRR).
%T32m:ig7O[%,g:$eQ$#nmP87'G;q5W+_lk^Mt8,g'(YHSL4)KZ0abhSeUhkN]pQa,h$L6Y@:nPi)Nflj@YE0I+0Cg>T=i[-nhNU9
%"8Nqq,Igts04r(5lnacl!'cmT"%E%V]1l7\?'9^VY=<P5b6G.`#i4A#M[fT*5EUJ<enfLF;@NQo7+fU4Jn>ZbTkQ3abN:3Qr-3,g
%02@6b3PnNE4e<?5d)\4VOlSTc0_]"LLiS#9*p%PF=eHb+P++I_\sU&MZC5^pWL\Y8W*In[BQIVdLl\JYbCn_R_)l+o@Pe9Xh[Wek
%&Tsb6WWM1c>e3Qge+\q/@;!7Vh%1o_R6+,@84)[(G6B\R;VpPa$%G@GpN%4g\$3k8#Wb+'JKPf3\i"Y40r%ZJi]oh!:'b4KpDQEN
%adP?&fmWDE4Z;Xm,JjoV&Dam!SomVA+_Ru0/`luufm5`<&N)]Eb=;8O#AA>EBbn6"7h";qcBinMkC2]XDm7dA6uVohaORR#EYhTW
%;3>b$VClbPWPpgE$N6.0Yk_*(I*@ggh,TC"\Yf4B$cTn\S%R6`"BC1#Mih)J%h!dm%t9R$6BR3IbF\f'5>h;pc'I07Qbjd]RN4En
%3OF*V(?I./4i3"8,'0hl/[pY%;Ekkq@Y3&ZKi$U>qPJ^8ecU2?Y%e,!5CP3O&9cG@i-c[[NkA[(:86,X1.n>;MrQ?Cc'2^ef@=W#
%e"9!BEhsK]ZN,m+\OsL:M7g4b0.Z,3IVk7<DUqYWmFY9u?X2396DCHo]Ydd&Me^E,42]Y<jE)h08ZB[Bp>t/c^H_H+M=t#1K]$OV
%R9"QLB^n,0*qT)p2jHHCQZj+G-RT9F,Z2Lh0n>@bO@Pl[_I?[4]#5^alK(jYS@tNqBX*(7@%4,$cXnrVn&JbNX-2^VDF[gs!2A/?
%4>3lq`2d$7nob>]!tUN>n?UdFq6A#hJISB=pp&p.ak!,O)UeR7qA/(kd\R/!Hn\%8BlT=n4NoiIcM!Ob(=TSSisfY5%$\@Odg@.3
%$"\.)Z*HUgXicr2$oB+nL[`h)+kKUDB$-W0WmN5+\Bb#3GmUr`5RsVS?/IpX_e'A!WKirERnTA<=IjQL@&8+$(GBN:"%er"g5]lR
%$Ibj*b'kcb!,bngZhB/`1HIfmJ3/JQs$`rjiYP>9QQIGJA:>\2V+.8beGaiXGH4..BhGLiGO+6TOPl>#"E.f8P2XV;cT')Z5@4Uu
%A)H/);6-Ic$<53e^^6F=)(="R"6$1S,#IKDMJX.q3,4I2chWJXi(!L0Df&BGMU2B`$8?b7*l<jeq.`c-2<l4ci-ULB/SM,Qb'r6f
%OsIH"!Kp%R;uCSM9af)iM?j:1crAa'd$A\Y!n^X\'V.sPbm''GZ^8aD]:>5K-i'Lmfg3%C@8_u3+*,n';m+qF3`PXKO?%?h&Cu,V
%DI4"`aN'R,E?FUM=N_ot=fTdJ7Ag0me,'##h@SsE,?R!/7!\H=,t`YNc?/*W7ZV?1mte+]lLP7KGB,$am<BC[MS=c7*"1iq0c2(9
%n]_l>"B"fPmqa$KiBA'XU@ucIp2`GXGo=Eu*'2<l+$uLM9*<W'6AQM\>eN8t.+;c8K]I:GZ3$.\#8[+.jh3[l76h`QGr8^/)fP5_
%nrbZ*`K=4<)$@i(YOi)n(1J/C0BV[F]ZmB'B,bA*L\L#_%h$VdiOUj#VBkO(V2$DZU7[#WOmVU)DN6X):8qMNKpusScf4`m#X(Xj
%Qd8Fti=Xl(?G_,Eo*X$fK#3;UA_<@q/3;Ut9]BOa"3iGQlc"A"_u;46N6/%L`tJ9.iQ'N`j^U.p-]^oOoDc/k-9[hEa*HlUl/g`#
%/*?q>ftB/VL#L9^?Kd\h88u$D8C[\6H%q.H2S1dH!i:LU+@$.gb3oaa1*\$8!SuRAaW:f^.d0YMmq9t>Q>>m']Qm*0%(DU6;C&1g
%"Q;<\oA;>OLcHC7'unF04=9_"R*a`ZjPTc7]0S]EEuBYEK&Zp7ZE&Ub+f+Z6`N4!5Qp,'a7=ZMs.bo3=GdgZei^8US#`?NW+BtZL
%>5>A3hiT-#9u)#%7?L>kA/art*/'/]!F/'^J-`/;KOD)<'URP`i]JbBaA7Q,=rjD,LP);/^"3N[(m)`*$3e8>N"A336PlJ8P#0*I
%-M7Mf?+=*B(C?@`TL^$oMj'5^K%<C8L$`@M#qGGh(#QroLpM]MTeXWsZJKRV\X-hBr8pNIFP^rS5%ht253:r_=H=qM_a#$XXZ_D^
%h?bJn0t=1F2QgO5"?8Ws6,Wp[PQ^*<rio_0)3h3:6jH)`&-"!?WedRW-+V3VM2b6/ajVOMfa[JK/nUE=)5#MYG@K>_G#IR"1lkAi
%gQF=b,:9kSKrt,1Xd=m;h"DTW;[DlOJiUWE#bSrFS*!)oUJfQJ:W?`AJg7Z/-QY)piH3`,nJI%GX!McLQl@usL\[XQ.;*>6Bmdgk
%4^IkooY&K\ZDY9I12]*<Wc*lu?jH;NDEjN-3MD;Z<^).CL!Ro6qHok$o?XK4aC,&GkJ*IXh`%o9E^))V!Zsg'#3CTT0$4.(V"5^s
%f^,IY=J[JsYHf%5T:JA&!1i1m?VVY(8e!"di5W]P<\Q3l4<<Sj<mO$9kQ:rJ0aR*.@l\&rpkLL@On#dD8)-DH-T+4q6<rmVE*bg-
%Zl%cDRR.HTm^ia2lX<[;UDbK0?TKT#7'##\>877_.7kS7:8l=8nA@ga6+J*2gl..h8%d4T;("04&Su_!,MsC9RU>Y,n%'aXiYmQ)
%Eu:-*cYmI6pV*DqD^N;M*#l\(YaeHhj`t[PP1)G<k&k"rI2dUSlLWBh.=OrMbjghZXK15bQQm$/m.J;iMu>mhnL*1JEB9F1^3f-K
%=B-)%^"5t<N"0T.JrsDM"@9F5a5fqENopZ[QU/f.a^[k,*]j<Q2)B?YT5)gQPg=14*_EtEC%->RdnFMaX9_E_))@4tCf#L>?A.$@
%:b?b&$7hSCk5B\r1[Y_j9*f]mea/V]50`c53Xm9Hg(e@X.V_kqU"pi;QKX_]5"+WX6sNnF(Tfr::_]nL.7a`DaJp\<FF,cQMhpp2
%^/>K<=G*?Y]?Xd/%l*'JWrH2fT=9NM')n9+Lt`//KFF]\0n?>?'\CiFOC1rj/_\Uj20Q3_(nmjd"a!_EDo&_J.*JWpbOu=_rWPn9
%+4!36>&\X\$$?\-c66:gA6Gq:_@Q=#f:X-tg'p/t4G]q)Y`*t=X[h`:pYXF&2raAJUiEP7PtV+".XgJ%`u-mkcVZ.]Q]0BZ!HI.A
%&8^?[*?bk,/(u9[!$E_s6-\G$i]>gV$7_<1YYGkX]=`t<Uf;`%@"QS,.B%h6*'6)J(81qU]73CQH&b]i`a_-"BoItta1.!)?&^62
%[<po]MJ1nfh/$rdcm7qQ<q8QC]S\E`j=*tmPPnqc1rdSrOhMJMHWM'h:IfIA1(c9fUAN0aW$#OQGED?q&U);Oj/p^VKYq!KjL;>f
%4.in+W\(ZH3!%qTrt9>*N!nVt=r2[Im\FZe"kmN!3LhK',mu/X@u4WaF`mX^JZ[?=6WhikGu'Y7s$"T/N)LNs6(/2)!Eq<W"IX)L
%MKbY9!8>(6N0!5dN'CI>\/FR90EuY,"]o"JB[Vb=H2'8r4Y)a<i5RVg-r)hU@T5[]'Em"&,7G;D(mfS,K*7DpaQK3=M,Comf'RsE
%&LKo+oPg+m&O1oFd4pI#&Wrl"'#Tj0j%Fur]E[/QhW30%MQ;HBOP]g3*.aY[;*e0N7e!,fTioi?]QIEKLd=nak8Q`Okc*0K^<Jh]
%e.1',N]OJ9?GY:UFQac)-Wj'(W^lKV"Nbln5GffS<Jh-EV[<>4fk(@_r:@>b3rk1\MI.[J^C%-)J%t#-TkO#i+8GKBpB1.!Y@]Ho
%8V-[`j5Sq3G'UEWF0<h9%#2pY%A@tK\RfK3VM7ldgmVLS>#H1Ib>K0Ed-@oEe^j[R6IiVGPXk_]8B+o>XbP1S/7BuN;a'9/i.A'M
%9`"pQ_-:Er.a=IsAgY1ApW/EjNfdJeq`3\G"X7gp$gdMpq8Yl=qNp2",$72ALg`N&=]BT1N)`;1h5aaI:tmIOa;]1#n#YirjOj2R
%bsK':Q)bKZdQ@M"oEN6::FGRuGUiqlpdNPuH$Hum1f^cRD`4#%Ok>56/G;24g,j:p"Fr4e+$-N"ndT[9;Z'YqPZ*?f6/IB)'V&gC
%:LUJ5?*F8eOd\Mrbk5u;W:oZ;MTJtB>h%O^4oS,77k%b#*e,UPI4.1Wddnj&cqaPZ=7r,f':(KNJJo"ta:aGe,/,V_^mRp;(Bd`U
%7IWnPONJhrHSD;7&g.0HRk["`dUru%9A(ZWT_&MYWJhF@/]ftT[Jjt$)oP4K?kF$2^XP+TZ!+lU,O??=*+215M<XC!_QhtSInh1-
%+-"XEfMf0J^r4M4j!Ud#)1o7Djp_Nh,&?os]-%9D<C2Rb@f6r3+`c3lXDQ]Q`n`n)a%fsX&W2?.]Jj/,ZCLq?/h@on-Xu(ss(30s
%kK,P@-n8-_,1,C(&U4%lA&kRb@M*(>bl1XqKHbL\Jo0$K%_aJPW8GtM$;h$M8-\Do"T"t>A:MbYl*!Zc!0"u<5COr3KcY'h6Cl<"
%cu-UmnC%Q*nWPMk+q4q`DmFFp^;f(j.;s6k,<NX!O4cdK_6u.%10Ds813It&'q3$Fe80Pj<<s1o?cJ:4ALC8fWs>Dt=N)/2G@V)8
%"eM+sZl1nl1jVL$@:^pfdTo3"PKu_Q.V(NuaU1MQRF'Q`Q@d7FN0!QcV;s;NGj@q8>1D\#d+42kSR^RU=0nZBa#rDeWcQ=D'%*4g
%bPp-"?E8CFECDHHYgZN1"78'>NZpEm<d:%09He(D=rM<;:3hf&k(i]oe2EB_nB;g=q""hfR1Ws3RmEj$?ZQ?Q&tBnMPo>rl+\8J*
%&'nCM`tWn=PNZ'0T6ib_k.?`?n4BrK)U[h+34?]s9e6>8<',sD:P1-^RBAcmD-(iiHJZ^;HjFl=W9&85mcYYLNEPl7=20,\chi1!
%f!@E55fHetV&$i;E6/!AH17BNCoFYohINQAnOnVElp>c7WR4qYh%$+`f*ud%*^bB$PW2CJp_BW?b,"=p%eRdiei@A;:-k:N;%l+%
%K2"9e%BNOO0fp5R?roUF5(rfA+'V($RIM0bY2_a!,D&X]Jdh>g_3ir8GA3et+,u#kj]:%r9S'5Z'Rc.*.gZEm@5S*o5VU_/^n[#Q
%*G]dI_?10&W:.Q#?U3inQadOb-`-I5Fs!"M<FfRC7rTe!k>0s=4Ucc[>i,m;s'U5AYfP?(N-"3%r"APOd?M6<rim$lORr,t:",i>
%(bOKkM?3(O$]tEDb2IOleecg=XK>O$"k+=$8_8`$hhm!PHqIg2$,=:1[@C(Z)l&JhAb%2LMRI0[;U\O[&u/4.Z=-cY-8DZGbe/TM
%/qZs4*NcRPcFD^BEs#P![\6?o:1%K6&Hr^e_5ON636<dRAPl`M0j;XJk>Wgnru,32?fSi-+`8ar5^3.Y:@&6ed9?%XjF_n4qjo&!
%oXnIX`!CCW_dot^3o<^;pY>3F[bL+f]hLe,j"a`U^r3V=euW9XQm-;M]SU!29ZU\adMt:6T?PaPrO_AGTh`hM?"B^0UZfCq6FFa"
%O#<K:V`Kgc!Ac9&B@]iiO)MDK='<?_^2DT:.`f3//;I<B@W%g3AoL3h$%N.L_NYU9@]#q\WK:Ya:5l\cr+B,=L-\Gt(a5eSEAd!*
%3`>2EnJW1Q:$j2/-)PG'e-soqSMUNM)C*m+R)lbuh^UKlb\lIU%]2[]iW7^n`O_>^OHS"uVDfoV%_^+J6n:EMprIm7KC7h29A1],
%*XZ\g!(uk:q*'HT>rc'O%M<?cQ**,1jSck1\b/>2g"l0Q($+b=3OYaa!814R;?PsSAHoKGBJQt+P;("9oP5([MlEam^u<_&&]*dK
%?t89`43o^s7%@h-`<[0\qUQJQSU]Z)ids\/jBJUT4TKX*`h1bOMR6_9lRtn9-Jkgn!\@OJUnMZFIGgOK08*E,Q!;7Y$$rW/H\k3:
%3EZr-75+HjVj7i58EIL=X!/,IMpuD#P'_]s$`ssI=SN=,\#*n!ML7@k96r7>+]mdJ#o;18nbQ9[93.Pu`[$<HDHc0g*D6JrPlo@h
%lJ!5QE:"]gp;g51L:uSt[=KTT2L?-C])Y?=$'RV!T4L@Yc^r0*Qg8T&^sfJbRmiFHH!0%%5s/12[f9of:h<*\CpV)][r`]G5];<e
%%]d`uKGhm"-\,$:oP&E*-(oO:,AOZO7XE6n\Am2b5E("1GAR$.L1?qhmD,PT$!IaJ*?UXf,pTNZ\gQmWR;5a!_Z!%:jO.d;,Mhs"
%1*\/&Oe@pfh]j3]&^pMh%[mP$/jtW+N8ceUFU)1OScHPn1U)"-gPmii4Hlo#>?F*Q5FBNU?'S;SK*\P#d2?:J-4*-[Q<6m?*Y@,1
%4`O&n.)cK^r4,t6K*3sp_M%CiX_t!<cEUD)=_8k6^q[mZ[oRY9?gQbcZ5P%&YO7?'*3O4QWiU]sL;%LLV>T&Er1H2<fRdNq3*4k9
%PAoi(=$RKp1S++d7l*Kd)0<)b8It0@7<)?AK+)FNg[N\qZV,@f0h#a0n;KQdfCQAZE(C'ER5sa`FugV8">nsM.EFQD,Y_CBdNB5g
%[qoYaXu=Alf\G\3c)L.1@4>&.8L,e):lGb%mmo[iCS",7gctWtdC!"i-)<\hZD]2_+Fka'%<2$RM;Y?T.0&q-(bluU[)=jSQJ7Aj
%I\_D3c$TgHTl<&C5YA6^LQ#/Qle'h(lm!uOj?Q@:#%o-J^0sC/FR.kab+1mlj*H7QRbC*apVm7!8oYlfi<QfrDq52[m6+q.`!S6%
%SSe[A!("bq0dn'aDf)0I6qZr3q%kTD3WC]/+T;I-Ui-Q1e^.$c:dDHD.:C&I&B`M'A?SY5I:+mM8AhB2L>Y+<N2cn>4e[-!7=q-U
%Z`(]i"4%Di1E8SbTL?*_Rkf1b8%<i=\RWONST6801>b'^+,Eg]+E:^>Mbl2GD><cQ4Hur0l2_^A)1'^"Xr0CPC'#)(7gRSN.n(nh
%5L(UL**(HtL)tbP8fK5<i9%8B.hlQ_!.f'+kVXAio[he2Gn$.V'+5aBNb,]$<^*pn\\/U2'_NEBFf^"!CSo&L(Koq*'oafT'kcFI
%OA"rJ"KHa#/)%F.\_Sc9Q_[6;0+h[:G]X.6ZHA<L1?F\GM0QnX'e&$j71DE77l]l$VnN7<pRuk<XXEmQnX>c4^Od9cFH,4hM9!D_
%<UqA=B8J8U<p`VE,(R*-'7tT7ac2IdOn;bC>ESTYABa8+A+VZ#XRs7EZQokY`j+;d9ic>4PS\_P4n##1'VH=ZXF(^t+Sah]YE]Ct
%A=^[CL9R`":W4)\$;,l(IqU14*`$ZFlfEpQV?[2G31nAS-1Dm0mm<,-7L<h'NWl%l+2Y4Slb]bDDe9I#.3Q_mqW5Zc4Ji^k0(W2`
%7GSVN%V6^*b]R&M;JE/p]O$iT1Ke=&]Z\Z?+lBq,PG[%C*mRNFH,(`q(u^n<;dO+3cb02GQdYNKgruTT'g3qd[;#*/dsa!X80hk>
%T;Yg%-mp)Y>=XpYT[OAA5`a9Ec:GikdD?JpI1iT683UFIm-QpFSGB;mZMTC(p:X)rna0+D>Y[s'X`RSJi,O\Qm@WD!9g9I9fQ[U%
%QJpmARds+!pO-SXXG[rFo73'LMT8IqE1hrm/VLLn(CH@1Es6*3).\2rKhH&S)E"P"#+:%3cn\k$kiF4YAYBk\.?%%G@8MRh_&oEj
%g&VTIU-DXAbeIgDHpXMqilqnC8@r2M"+j.4+^P73h:EJejBO24>IK#t;*R*^Wlt4mmIeef_MpcUG1%OUqq\,ghrdB';s+/q3TYJp
%2"YjNZL\'L@X5['(i)qnS_,Z"#q^:(T](;F@`D2ZlrTl#4(8_a/h<q8LN\XOC<J*b,I!fr(g2nrI1E*qf!S!D'$=a0QUC71+!!h*
%BGg!k=$+[Jo<h+'^7rGmD5&2C5)qnXK_0h&eUIp71`Lg!(8H*`PtL0A_:f5]!^joET^qK!!e7jUZ*+PCVgfL-#DNF\AilM'/DC\K
%lB[`cSRE&@<j(R\GfUJVRT22@7'Hu?8ZLBqGWp]jXW=8`eUGr5!J%9@mq(&r*IPbL-L:6Nb=DBk#MEsI-&Of-%W<4;eOU\pUp^l;
%BPWIWO\aY40+Ng.>H>C74J1m?TY[e(FNtb?[K@VTrt+4]cYIW"nJ*:tSj@A8oYW`V:/@T\%U^-pW74=o6$3Pm/MeNb:lBhEs8-Ro
%`94JZMDl7B'^T&1'L6KJ)`UW0\Z2G&[m!8(JY&3/qMZ#FH?t21hACh=\L5<ij(mG%/UBmIb83s""N/a`8["%ch<4_Qs7'g^CqUY\
%,k8S?*\Xdh'ef+DpEbREnW@fVb_!ET1H"OeK6\08G$RHC)ebMQ(jng7jQqVVk[F?ESIuUYR;?:/N2d<2)bBrDc=VG%J1FGPB<[?h
%6V%,O.kF_G._f\>O,)*+h;-^2\:Vm60B040nmXBW52J/bU$ZDm-h&kf2j]D?Y8#<rk=Y8l1.TLs%s7Y`@u4eXX?=Eo]/%LJXnR'$
%;CQ?enTOnTguUK5`'U6-h$aHIICuUg)4Y,s%?>#a9%mW827uY/7sHch,eQXMj<GZ@OEuq\]jM$B&p45#OYfGo"_4,cSh>HZVL@]"
%[n!Y/r[&j]35oW")n)$tT2,K-&J,g=GE6_bV&4)j]>0P7Z_Y;#$A(Bdit?#2VXG&OANg>5Ot$:Fhl8>\+><Y^>A`MC^I_^[ZV@X%
%JYV%Xdo:56r*gp!$Pp;pes-;Bp\@fb<j$8?9Xbf7aR^1=pV@I"XlWV?1uD<3YUp=G\TP!hf"OAWl)pHV;MCt?&sGHMV4[?lCu.4H
%63Jp@24S"?9',Gr%BZU#D1*IKo'>+HKhu>uoUK5%][@t%l\(Fnc5JVun7YkAX#R3I!i%P6mD,T\=<9S#153!*1:nXV9&m8BeQ4iX
%M6uEN.1BeCG@cEc,(8.DRlC:^Dhqo^KQ?a%$,AcS2P]E*FncX!hue"T>6tQ<kuEO+_oS1p;_G&7?[XrP0KGBZC"W?qR.ol%TRTkI
%``$eYQW@N]Y)`TR^$SI>T1tC&?WSf1TY@u"4!mn(Njdt-naCh7`,qq'Fgo8u&4DWC*E^3U9uA3o/Y"Mbo3\OU.VC%[QO:kN@_[0r
%nY\oRcVM%#&HgML>G'V%%Z5e1?sgXNh-(XFiJHq:O0S=Z.AW3Jf[XQl23aSSS')FN>P6Hn??NoO-6G*.[&GU4PG,o"jIEKEe$!C*
%MO0cDdR&Wrj3Wg@p=8;X^i#nJg5H>in$C@1Y1]qtB.5EPIM)0X(F>^0.U7Ab0rL$U!4]S7;+H4F'fSj:m3UgGT=qPNLOJ4#:3*+m
%obJWm"&A0+0lg-9X#eF^E!MTY34HZQete/;4qi[*f;?RrfQt_rUEuhofD#UV%a'#4p!NBCgs*PJPRH$BPk-LJ,Pm7R,P;8OKoD(r
%V]RS>Yp[Em=Q3?uC25!SHnH2m-6=i'XrGuC$e_oq+HUIUn0c>[3H`Z.Xi$0]hqCtWpFk3[X=_o((8tU"F.fV5R6+T[$$2O^,aI"k
%]7b;D4LMqrKCHYSI>N??AE2\^2MZOL"TR!CnQ$nl7;Lb[A/l)j7i'B#I,]GeLRY^[.E/'l8P[;_#R8<b)<b1c9#5Z;SBT,oNZc(h
%`2,fC?mf?3E2ef>g+p,Z-=FR^cudd:G(s)F=D,]>c<(RJdr"h.@p<D<2^,K_T\a7=d)?b>,jNCP;e[>m&_cO=#Xtg7*,=\cU*/!o
%=W"1am];H7>@YW*8"f@I9I4R]Y?KS+1b15QbQ!jR1!bUPYo%CLJ11H-Li.Bp*E>p=HFJdrq^0IjJ+L"[N-t0?1P?2UYH(`-l]>f,
%:U(2[2:U+'P>NMW[':K'R7@]1?175`4tM.TTB>91G/VKECJ,V7>\`E<KY4-c?4Xq'`^.uN3Bpie4HWI4F^k:dnoP@+;lqJO>f./"
%pQ*i,.CS62m.g=<qF5GU'akl/_=X_1cOn0r&^CC[X4=N6M>j!Pih)_6nt`lLO4lGDe"2;nWf,j@enl's,_0tMGZdB7M6M:no3gB0
%XAsAGN*s0J05I)LEeF'u!"566@mh1OM83[gJ(?G>X\2j@/;A?t(;oUgqKHFZ2[P!XfabYVe?,Sb,g\,N#mUo.SjL0DB.tmZT#m1T
%j(2S0^e/E>FjL1'7KIR88qa.J>K@+`K(X/2X#kEFJVs/!M>BFWQu8"bmGlW#3-Rso3;;MqWO=;'DKZVkas,?kfocW\;DPT#G#BM$
%#FF#kXaOdj.1?JW2_,$^4u$l8Pc3[,?.p>$$EF*O7D2+t=RG-DOh'\\;$<#k&g!UnJO"2NWeTC52$k"Dk$PLJN`iI))['7f'LcKg
%!!aCbLWe;>6h.LYN::7udnt>+B#&-n'OR1DG(FZGCdAA<gu+p$'Z$=9V1+tQ9,%duD<DROL6-"-?d79oC>+-oD4`*qQlH/%_XhJ+
%O"E?]Q9s`B<)[c:T%r-[>4%\(?I`'(Uh`OM5<B@Wn%Tr/8o.GnWTd0SFiO7ngtN8_l*^[WPX%ff:Ma=;5qc6_98-EL$7L[H6g()P
%:?a6M\+X\Gp#"`F1iBOF/eM\nJh&!QNMXL(AFMl+$0]BLWk1OXb?j3'[+%R6fL0egg=*9YkIXACXdqdtFutaR0Eu2\?tQR/X2_0(
%[KNO_Xq6[qXb=M+1='M;i/F&;)[LVr1`1udO[Z*DV:4-X]QQn+r8./\I^NkhZPRujbM5#Zl0jukL;kjB)VE$)*TIs0duK+U7UQ5D
%l8A3SG&a/0!\RBlZ-r?J<^p2)V-go@AcZ7pF)<u4NtXubLPG<jrYYoo-;(#T/p#87aoMH<[hk=:[A2Kg4m0sO+)Tid"ZdnHKbQfg
%fZt'DW"MejR8g1un@\u*<RBiX&_<oss1"cl8oZ<e2d*Uf3`J1\*b1\rG%-ac\9)OP:`Of<\b'\U*-hk6)Bt+ZPVH4+-k2'Q2$]W)
%j1mdKKN(_R0e7X<rn<<2M,7Tn?Ne@;i/Iob'QFdn7Oj.RoLp8We7`h*nmQ2#KL?!HQK1t-h%d[>L_$sp:LFQ7!gLl!D3\HmY`V`O
%j3`aEU\CE5*`''5pI2C$iqFSJ'&N&9gT:PN_0TD!$\BakmP/L']\m*lU4ZM>p!+ln02+e`84"RQM,BYL-<$!OW3;oU!72i$>\+Lg
%0st-W([r)kA))?r"V:+"7P]r:JjfAT<<_ClVLHW._&;;0230uN]aQ()WamO+hVfdeF\;f\p2QX)Mo*U)s!19H#gjO+lQ<;nHB'\m
%X:X1X2+Hfhb$]'9'(FRN,M<)?Ro73#m4.hU?3(58@4hULm]8:q-!,o66g3G8eIINFcc^R'/BiLa%g(_\%.F9sQN.+p81tU=C]Q\)
%6rZh3YU\r`*2]<i&7THu1?4cgI+VEp3C`j.rt,J8"G35gjpSXo"::/#VhlJ!_D2gE]sRt5+"=p`+?`'#AfqX1SdE_t!!sQVq7V[Z
%HYWCVR*=J#?rEeSr']P)i0,U:FanS"#S,5pZ+@SUmc&U)?eLW]Bo5*8-diY"(]g,O\6.jJNBaDPm2O9';5H@MT_V;AqnF%V4+1dn
%a"bVB?an:uM')d*_,`Ngi8C")K,j^.iq1COhA<&0Di`qIDGI8CfR:Kni_S>8G)Y5>nW1<'-)6,f%>$.%k\@pgaV!#85km+n;66/;
%@3,f.`^h>1dW42t'l#t))6S2&H&5flO$J[Q^5@71qrS;O_BQathpAVP%GrrL6]Tckmu/h4Sq8LFEVcc"F7%+kHPU<rq%#CLNcKuF
%-R^TIXVaUa>=BANJ^OqC;\QThU*1$i<51"u#3;Z)qPi'@&?.H7!)`(RrG%+26/oKJj'+)Qc*,^9hBmCJW<7A<qQ:g$%)I6t(GR*c
%)fpRCjX<EPR,Mj2BrkQd[V3?ZPA8Ri^IM1Y@N3F=M24^.rNXYQM<khWQ$O-o:?au5]F!p/bjP19>Zj%*&[_fUrnLMO$S*_=ZX74]
%2>%?7._Q&:`Z$(QiZEVZD>o=Bom]DJh#5kUrr*ibm,tb*V8>c2-H^]ea4=lUWZ?`D;HW@j^j>51]\_hC+FH>Jo3!@:`p-Jkc<OMe
%JoDoXN$l+\LJ9<@%n9>+A6IVF?(sb=-$:\FZ'#U"`QB&o\cPkRlJhrU8,85oUBa^4"/&X13>"*A"Q1%SRQ?R,n:m;lRY,Q8H@j4*
%<09KYHut?%Y0GW3Vrlg!.3P7A(k7Gd"XX)EfVm3ucseWtA(fEt6*jJ.aqlhsft(]3,h@26c3j^(O(uI_cb<P(Hue-n[QP.P!dS.E
%'D_0Dl4W@nL$_VB3;_lM9!dmkU1Yd:8TEK*_<ktr?LQTF<phA5b2X>-_rRaY_%Rhp.`(UCFJcet=NMd+kT,O#VP7,]h[fo'?=/@=
%*Pl7(0MZ<BVg="HA2HLmG'TZ%F'6nRY1Zqd\oG?Z[%T>YNn%Iqd+m)-i)hhE1PKt.@DUhV3>#)MIcEFrc.;GP?p?aE]Sd@.l6[:@
%j4nf`$qQ#d-erf,cDEmPHCj.+.lnk<Gu\CLa*'rpBtjpq/;cBq26-ppH7.Fi.B!.Jl.C4pAC4O)Yd5-mr1Vo#hE*rE,"AJ>b9Bn:
%-&rq2!)DO4K-X1N&1etW_6]Lu"g[R7l'Stpcc7&.<=%hr]h'm\m1?[!Y1<k1-u,`"n\eiHDR_4uo"'Kn7"d-M^Ih`^cV<2nfWFg@
%FE(hcF42?91\5;2Yd^Han=0lM]kWtJ-Ot?>@;r-%6$VHZU9P+j1Gmqj+@b3&HeBL5Pg"Yd`)QCu%4+odg7l'75N$s8Qi&$6<GKg*
%e\t&JW9Qo][P%VtC<f7!hCZMD(jB!Em8%lAFTm1%+,ZZMF-$,C?PfL]c,X_Y>+P1)H+<DBWmed=fXAcsg<6mHWeL3s5$NN%W>F!=
%*KOH5*M08Zm)U<?`-AOsS%HhQO5i#^J.)q6,VnR>jrjBWNreUhJ@^[T&8EGi?5+YF#.^i,iKd>*m_RIf`t#!UEX";oXLR!eOF<eD
%]Zm]<EMAIL>-4ps7j^=D:Im56l_mme%$9#Rg&?Y\IE2qFu]sZnuT(-Bm^S@gecOgt,8^HA8e!11ZT3.#LbBCUk
%mO95Rf@@Y_'WGTUB)_'%_?9Z(qu)m*./5>H`.c(sRN^s/;"JJ@46%'5\Q!U<6P4B=cTT]&oR<BBf5;B[H-mh76QA-PpK];bZFP.Z
%:i/^DJ"L\\aR=f$&2euQs&cU#FI$HEcEE;SBHgLL(>l=lT&_i;)uq1D3Q!Ziq?l@8$7u2M!%,$T/U$l%9]M1ZLI/!f3$]7^.6bNf
%DUdd[\mp!p,gOI5'V9Lck%S!lT\fg3q(l>05?[uKL5^S9,8bEYP0FmG"ioc%E=7>M]OVBn7HRo?KZ$#_H(S*<EMAIL>*a`%=X*1
%*_&)VlA6a0AZof_JPb^NU#]teTVp`=L/(tJgi_1K@2G*m?LM(-YE-V=n4P@`>C$\-EDA5%VU<TE2ce-Pr#*f(6Rt_">]dQk-*Er*
%b,4[2hZ!grTo)Rj7;*m`R-1"HJ7/'h=b5]jp@30OhTZ`?\%l-*Ie?,2Vq[64gXI?dDUu\I;G\BG>6=0]e(laFB!p;PVXpHElk@='
%bnUMR[noj+(;X7[abIYA7sHe/-[dgL:lmhg&XR4``p:3i#O%-NkhPnZ%!P&R6P.M&^5[MlO3L+XaG$]"ZI,6gPUQ+[BFOE"\B'/:
%&>noLQA[jGopY\-+KBU<Jo[f]3X$T2p*dg6d[GAXB`ijak0!l7=.iNc\-;?e^bG?X^n[lBK#"L&'&d*KieplhF.'u/.P7R$NtupO
%@/s0+XWY2uH&>1X(QKhY!jZrC$cOL'D98[3f@HX2D:&b/U]dZX7jB-!!+U-u;E-HqkpXkU8)A)B@"kd5Jg6/m4H2MeKq\:A4PfMZ
%-58Xq-]`LmAjL7Pg4]'*V<>8ki,i^%Ake=YYQR^<EMAIL>?r:WuA;$L)['C2p+^TgC,D:NMU%"/f!d38>LBb4#`f)e&)n
%OY4bN-\!QS,h-=^%1tQR*=m7F?S27\;r\G%70,r/HeAA_],(6gEfDMeXAZjE"&0!Yo5U:5"JG089A=LuB\Z62N#''7/?*gW[=HN]
%4asHKnH:@IMl0K8C*k+6j_(SFB*>oAig<"%%#\kcgKp__=*NdkKX@^<+k>PG3J!/.!UYWQXLl<O4bC`gS(>@%\^XX5Q1EF0FBMb"
%Z,jsT6(9!k-he$e2l^1sZ*XVEFE@d$,k=Xs.W'"ARG'eG!aI,X+2YU00iDFL]aWs3;)!K30JnS0;]uDZ6,5dN&`$_mQC7:a':u&'
%q"Ho8/9fmXCF(YB'BC&NC#^SmfXeh`8\+fk"!:E,lL_@>MM@Zu4lII]im/67U&[qF7G\,SBKB%!25eTnJl<NK*NT;HLS2@S9&o@G
%4nO6R.Ft-'65t,[i!Qb\8gnFfHL$/sP:6&T/V//iiUjk7Dq>YDB</U;j:PuXrC>At)O8aZ@bmk30I(nO=Zt)krqFrY3/ud;VJ(Kp
%"s?utBtW6$0,",:4mF]H#*.e$j9M!:e.j&mFfe-`4DgL.^<Y4/lgI]+K+"o\*JkHG@@gC2'P^6aKga8s^&m:gU3WlO)p."`$O[XO
%@ZgVu7Ld'umlA6Ao`\9&9PmlkO]6KZ!/PEZ@@.cZTV*u88q%?&*']VM5CR.C*f<HB24p#ZbeVQ[/1CY[,55";&lf\N9X,HUZsBW+
%1S?U[l6ma\fn>-h<DGlb7Ag.&r`ci3KZ5o"HHgtA[R#k3n`:t55=*L"QEa9D.1RPWYF[MKc+piSE59G[Hhtd9o:X8QHD3$C8`CC7
%3h1Etb$B,.eTuUp<H'CeRBcUe+R>q]eS$9bCe.')<A,*fGPC%Ofh77"@Y>1HeN!,XUj\ut//-s0;g_dBfkpEkb((E9Np;6Uj0D:4
%C/Ws0Iu\P]jSHL"`G8R,X"\c,Q,NpQakXS>>,kejYl][qVhRKWic,D`#H0Nq4!IY,r%/_^@RCG,lgX9WMgQsY2"$Yc_U!45]Mf,m
%YeSI*Mqa&\3k,Sj:?#/X1#HnPEjWh%1LlUA1]5\UoKR9"!b[U&d[Xeuk\rQti\rtC=bNbT6LoM)h$gVHHlP,B/^fLKk'Dmnfo9\9
%Mt4g$F[Qt[!UXro3o?SW=H+&%1mtI6fb:(NHb]]gl?G8&h#`@&r<@cOf.(3?B]$Z+9o^2*F=29.K#bI>Op0bFA<6oE(!db^6q/n?
%Oi9s(YTf&.5&k9%CDPRLI]/tW<_O\^G[mAhILGDE2P[jB+O-Ue&DnG-2F5`"4OlP>@!D5q"VU-YAA"o3#3MYY<4FXTG-IN6>ru-T
%UGSuK@V3Z\h6_j^bJ]IXq%=LFGNW.P36Gm3a%.0bK=-%b3k=d*$hB34AlJ<(C#7g>FR@pJ)A7;,/m28f`-R*1m7&WCeZ?!Odt-'$
%E147f9?U)Pr3Z!s=7ltXC1uh+aZ*$,GY#752a+XZa]dSUs7q&/fM,ZJ.125*WmkY$-QVf-8S]m_(rDu.9[r(*&LJl>Oe/?AZNBP5
%'bl=hQO3Rm94VL9A0Vf3RlYjt0ak$T8<QI0mo(W]7;G=b)q)hATFAYZq&&oR$8m"$Q%`9g2^B-`LSo2*H@.R]]K+#JDGfV;#N"iN
%2Hh[$8?#%iX>Jbm-M*b"-&\^lr,B4%6To-rodK$D\Tm+-+`RS!3iNU3L_')Zd$;Ue.+Q9oi!<cNf7r/=if@e'X;5"H8o-p\:2Q,o
%A3g=5)n7?2/7A7SZTDnck\sF3Mh`O/C3U"n-.Ksclo2u;K]f+G5+icL49J18,@4@;;SJam^/ad$FO0ZAX.!2XGI0<=>lGgT"c_bK
%,s^Y0,I/hS4l%L+]=@(q)N:I'>r)`\PLJ^hm4@q+&NA6VRYX'e:SUVM>&/uOB>J#_2';7"8qu9R]@Vo.,WY*g,a$ZVWJtP`6IhrE
%Q(+hlC]U!:>rTV5C\g_m,g!0&\"./\NHd6Ue7b<FfEat@]FRZYB@[h43;qgGBO+b!kLj[HBW\"Ze08$Vg`">CRe<EYZ1cQW']2WC
%Vm+';jU'q&b&6gM')KrTo[K);ACpVe+7tgKQKS<0OC+cm,V>FOL=.V6+h:)q59r);:jWm.&fW*-jSA8jXEfo7W[6]1+&f8N\8T,=
%.[iQdR2m1h-)BLUA?9FjPZZnZG(+U]aA1q,bD&?<TR&oRQ:=.(WHS"Ga;@0]LW&&$HCrml-.SQh%b@df@e63Ec,_mXIpEU5<IE:d
%6^fG8hK4qDg1_-Qg9=ZO?\%+=HjHeqP0r$hZ)TQ8p;](AL@q]2OKla=ZV+^PEi2<`Jj$Wd3,QET">p,30[2u5*NU"a#2"[k'\!O.
%Ea4ChbK=!No"Vc,'(AHT/sP:\`?6W6T/GK:@Upp!$WkVkdc47FR1KBje5^45\jsj$$8?&SD1jk6)?X/7k`-^DAAO(Q'LI=1351La
%XN--mV"6jiEUfU!4ObPObC'med`*t*coAZZ[CcZN=AT&'7(aMO_3fKuAE+0-qDB:UH6(iZWYPp3!O.X7(rF]Z>=4e)?0$9'ib>%*
%[Aun(.*W+(Tsh-*'I\PjSYuiF]hZ%IkjS_QA`H>'%s?VSIY6J*JlBNa4AXSQQIp;uZbbmsfgFLIrgOGh=`Z,Uc)6>pNXsa0RJZ;m
%###Pkon>5%Y8j!ADVS;7>^rtUD`6Ds97g;:ChgE!-rJ(Wed)\??(:V"G`_DJ2fja@fohC.A;Q>GgJsG)d(i$p$q:^m-$k=f*_k#=
%O&D50@]<VgnK$G;LM',HADD(q]e!2nQ3o=#E06RtedBj:"SS$+:8^?Nkml#:-HgClc1k=dMrHU8>I%+[^0[aMFaOKIen;.&g*Wf"
%XUc9"c8.j)^$fo['PROnm*W,B@rXSZm:V`P2qD.b,M$*&M^\O8b&5)R<4K6nTc4$Ki8hMgb-JQN+XiP>EhtSY9.8M#GpqR6R37+@
%NUfIYQU"$f<q(S3V@5,\hXpJSWocLfQ,OgJS$@jYf%S0]ZDT4:F`::!gM&5M;;:uf%?\k,EKg:f&URR9_]Z@K_;iYE''W@jf]A?F
%!'AgXFJJR&0,](WHB.$`JfIa$,`jVKK.Jk"(lC72.<0Nm>7Q+`3\m&FY#VHZ-2G8D6)&<-6RUY^reP#^V'.7)mO09EDunsUCIN<g
%Nh`p(=9IVh,Ih!A^e1Fq+Y9jD+?1=m"BFHc.$86EE^j:BHa\drTQekj-.n]TK!B]JFX)D6*:c6)73$Z+=EL]=92%cST-@RF.6LTk
%!(0%8qa.&_a%U\.'H3V45G,*)/^&8,D\Ztb'T'g[[WO>o*jSE;rTrPZgMnWde2oqbbS]<e\WdU4V6CrUW!kl.;-dTtZ`s/51s+]"
%3(EMgA^OS_LSCKr[RMB,#pY^g#sKO>E]!'_C5,M=r"ZkWH7BNsm1GB+\0mj"2=H2G.WC!]IHuc%J[rO:aY!cbCWRnh;KH2EZ,1C-
%I-A$%_.n5g7g^EQ0J3,K^p)+U992t)^N:NdTjN1^HTuTSrX2<'h&lJSe`G(gO<K5T<f^4SEs)S,0pZQ?#YLY<;QNc$_'SmG?^o97
%Eo17X;!VC9Z#A:0UY7OZp5S/t%Z?acDc?c6lEE,STC.Z(N=usV(F)u3FqQ-\%3dF+h,s2Ao[4fEQio"Fim3dQ)=22;l'li?enPnc
%M)P"mW*V#>g.U4Z`.#b.BfTt)cA7==$[)uTL2LS:;opFe9K&0<&Nh@_:)Yj$I^*Z:\`,\8c4sMS.EX1)?jT::bqI4q79b1(;%D0t
%<*\!iT#Li<r-aWXgn:0$%#'KiaD&RbVW+pMjue0,&VFJ<+p&FN&ai/C%[`?,,1d^#/@DGcWi<D,[&WS^o,jSL'"2sm;7Rp32;E'E
%ElgLB:hk6Vd.`eqJmo$TVL1Rsn],Fb*]o.0:0G-Jn+g8l`R%V`>QVssG`9!2O_+`6\(58)V(pl>BW8KER-TafQR<4fa>\;'&iOD/
%k2,!?&?%JG.]e4HZ_XobQ'<bnGQ/8\=2>Xr2Q#>M8muL0kD-%-X&a'R#jsWup],Xc:8Y6._;cVEM,hZN7!+#jjp>0Q.p$q@cJddg
%BBW)2oRYP*S"9JQg`C")Z<>Zo+%b7b`:ZS=[cNZ?f\]/da#GSC;)ZmC?=PM@YCS=s8>ea?)Zc<n08?A^.a@0&HlBaHPU`ukdTN`8
%c?DuVG6aMs=:nfP=:CecrF5E;3"O3WF5EM`kE0[L77bT1La(E']$eZL00gj\_#E[4B65NbXQm:1el#P42/#=FnpkQ=kU?!-Ll[Un
%"#Lm-SBK^Q)5#a=?:./QeFVh)V+.(tp1QEq9f\Bb",ODH;ge9l>i-&CQLs;;&bs*-lDp0TYq/Z/G_)b4m:0k6ItAD8U;:?tg6TG1
%(Li4TRQG/s(Z[YDCj,SF4tliVYs9&0Xt9nV[2iRtV.V#gCu)`\.&-%8`0HX*<f&`a[Sb9I8>fT8'.KNm2uG6I$Y]?oh_&c6cZ1#4
%=Emfi\,t1DHuCF=M26e!hN0K3n:@#9./h7RSu-NkG)&+mpL$M#G$$^k6'9YD4U%Mi.[m7fgYYtW7&^e[(2=-A.*\E`Br]3K@HH34
%0\Z74%UUF\\fn1@]HQMf<LtL)4P8qVWsJ$NT((ON6_fT%F^,!-Ak6SD*'F&\TXK1m+,$'4JgO$OT1/f.f2JRT^oO,hS-sVH'V>t:
%#nfqi3C5rd-LZL%m6WKM\:oJ8Lt'CW.^b^;9^._Ds$2kHH`u1f3[Y1U&#LN@M+g9J?k>/U'TFWJb4_=j7qu5GE69;;84u^]$eod`
%h@9%^.ROpG'TNaZQc7ZTa&KLUVcI\p5l=!r19($H"@9=s;7AbN"6$rK4ZkklUm?4Ekmh\oOC9IOCJ1GDhc+A?pj>a+26[ai&U816
%?@jS_LPZQ@OFE-T0d.p0B;)3W-&pA\:'jr,N>$fga)oi%4t0fAZ+0+HhVpcaT*#HL7L%5,N<SmQ<\8sEAt@S(6h.alEY5>WPjh4k
%A>t8q,&f3EeD\cBej6:`4c5,+!6F%4hu9`YZ@p6L@X5TEY'?ms9T7/L5Re_0=?sBMFs>U$>u`N(fV\nF_L$+ii\o?pbP7?n0]ebl
%9dCaKPhU5q<>cL>+-!jW0[;/b?eloU(o]BbPnaH8nqQ.$+@iS-F::XHpgRlB)^<XEo_NH\'_hm#kpe'RYVUjWoZ`fLBo_i1X83^j
%Ni1\oY>fh4Rf_D>4sq6R<>Ph<=@k/5\=uGm$$C[5or4"cPiK75"$5PS"f*Kc"DE/if(G434h;kD$Q*nZI1[Vr2W`:/gZFN5Q]Ni/
%K9^hE,DAZ`OMJU5/QN"Y*eDQJpOrBu/9p;CM.1<ITE=e-70DhP7<%6=='osTHjZ^,7%1ds.I@Z^06r!$Yn^_e<"^[6r.rNG-`!5D
%Uh8OeM:-f>gkcPC',3uPK'&>MRA+ggnVB/RGbaG_Dj6a)]?uB'`7\Wm/HMjA?(X"G.:2A(Y_::I;aN%nX\2lj2/()qh"/\WYe*:?
%KHA%A8e6\#<Fek^[2]kerj(IM49AJFGo.mo="<k;F&Y+Gia5h=W%Ni9*%.uD55:YLWmgQMG>h,F:Y8LlV?e0@&0cBsf6Y0,=D!>Q
%k\F(XH%H%iP'2OVT=H1UmZDZN61oWJ)?F\aI49E69na4U#3:Vl>>S*mE2jWt\*T^D1n1LF*9jba6o@_pY),.@9dC[uEV/4kIM1lm
%A)[B'$h5rBeu++Sr1h+B)k+XqN!f.$`0c0b5Uh0M[LYXM;Oce1Unj4)\I&u2pJiFHK-5(eJ42ckIMeBpA-LLDa7\"n5rckQFl^e)
%[H)M?i%(CPF$s88OPB;^=:Ih<I#<FOXN/@G<pQ8AAa9cU'dFtG!RGC!ES2#"b,7EPl\XZF&_Un4io98rF=(AYDLS@V)<oCS!_=Tb
%(h/94:W8\P1jdc[OtkUc*r,4=Pg0o=+<S5p*rIM!UuPBsR5=`[N3Atmn2&E:L6?mZ<YEt_I<%:s$;o9O3Enknh,QTtRW-:NoPL]@
%/uucfQhoD6H9n=K\#f"d#A>afi[/?Qp==,mpcM25\3>P+Oe9i-hA\!6EltHE@s9G?a*^*'H=ctg3BC3p@*kt"KFp`bcqVTjkX1:$
%Ce_,+[*SCXV,mDu9\r`cAk?Y#S0k1gelgN8c@$E9:tFpd)ZWl(ngQ8fjU+3L,.P'e#E'E,I#^:;ab+,Ad9E\N`.N3DXnk9lKPR;h
%SdA7O:K;aXA4/NNk"a<r_dj=e&GF63#3/l^Km7"5k12lQ[N>:L>Rbme<J2u1;9V)U0LIp*0m_gm#U@nu1kPNhrZAF>D>`2l5fi0C
%>(A:@O0DogK`J5CgpkBtopcf^1TDgcn:h?)F_^goNuP:P"e:kcQt/uKKo@K$8qs0t),/;t"Y5#6>_k))I.-_X>qBD\J6=,E/>R(A
%UPCcsMn,O!juo&D5A,>lo9nB;#1(=K7?rP*Hnh\e`sU8Dg7L!rYgIPAgD.pC<Z"W=;2tsHNOF(ZB6+5mJgiAI+/%:bE.PD1&I:\4
%hPtt))]C!;SMbs>E8K.P!FV5sr'2.FRJ9s:-FY?-,V+O6,&Lg_-@3i'=oTJ>NF"/::%pFY8NMF#QZ,J:Hmk[E]mc6Fk#S)/:QP\_
%Sd!W*P'Ymk8>F#;B1*Yt/n3>;.m]`<CAErW)ki!eCe('D-u.k,^p4gH'QJ*PB>J37Jc1?q?R;Yg3.CX!bMq`/H%it)/pZ)@96a)1
%F`AZQ@ShA7@[?W`g58\;2oT_Cc6^p9h,T>T3WZRa_%@shpB2/<[4NWi1Gh,slpbJ<5>j6Y5=u:P\L_IPndMZs*;<9Elq`>nCUR<;
%rM?fI&RK1Npa#Z,lK69L;P(sD#(\(-e6f/_<QR]T^YHbe'@^%!"Nf@1n2i2k2:@h(&bcI5=7>+NA9B7D^h24gh%mh\b4!r\Ei:1@
%35%X!'LrTMpW;?ce3e]]c7;"jpK>P79'f"^*qU6q.qcD&(8_-Y3aLG`aaBn>boleok;@%BdJH+3PbTlg1`0\FToWT&$LDtO3?Nl'
%4?9I17L*I_-1Pr1*3#H-;3MMZEC9(h>Y15gTdot[+\&M36"Q:+1J:E9p_4FYV'NNoWp2`UFNaii8O4F`<hU-C_:,68^Qr]HoTukl
%nK\1_7l;Z!jL2?#J@N>IVGRj6WA<XSWR$BF%rG1nLU<op=G7hTSPG58;unOj7A5^;.$pl)A.A>'WogQK8,;2A`ik8&<b6oCG7SY>
%"f>eq_o)':K*\O??d_YLB_*#4ARlV=XY2X,`Oh0''PC8(MHA*!:f$5A"+kbP5H!Y*fBVOiS`[n_`Apr)WsR4ia)8*RV(Cf\WHlN&
%\L_[fCE<Vc3aH&<]Kc4".^XE1=OUnj`ZbLB0jELC-_I-RQ"in4Ms@Tn5BNUY02]GY`=A%jg'BJdl!8h6,Di\MAFp-",gPKV$2e.i
%HN8#NNXARJBM7D>AAED+j1IXb"&W>i(4]^]M\@t1K/>Dngrq(O4]H%27$H\n^b%p'B[;mX+"iBI/J?GE?#qU`(BT@Z!_:>g8[H9N
%;'+Orb;CI-mMY[uK''L8`gZ8mR'#+bE(ehF4+@m#]@8mr+joA8Pi_Xm':S7uY4H;kfAt]&!oa\Y.>gBrd^ZX=_G\hrh!/olk3$7D
%Z]::;gp(Xm[M>H=@5pGX<U]d>!JA`C]J>QXS%JKS*ELj'=S7?oQa"LQI+JI"DdkKb1I1,,)t4?\3spO->@:bRM*cZRTVh:N$$SS(
%-a'UeB+5OpK#6+pc"o<b)VB>qF+b*$Ass?=].tN><"tmPpr]PHj(km$-9^[27n-`!)shh<k/K3XQ"B*h,(mW.6[Ch(hR&a`$tW$I
%"cI[OZ*sTT`8=l1*?\A7,f;I(TV;ntN=pF3r9,$LUqP&6cEu=R&J0<bV4VM?BV"?tK6LM!g<n<"QZ\$d>'Zj+&_3]haGCS$7+<kH
%n;g>ah.;.cnX/clS/NT.54Kt40*_l?]i,GI6/fL1P+S67E@dqA'1]Ms^4#?qUGi$\4=,=?&/T\6FN!GLH(!7lmc"muU!qGZC.gNr
%8;L#,VHH]6C'Db[/^)4oe)0r`M!qAmG9`.VPnmt@c[HH0?Cps0,S&dK4Pg!I.Z-IJ^4'<4r68[=o'Mn&bTQeO?_^OqB.[DoY`<!q
%Fqqj#CLrWR7$)3"f=2''nU(nAG(gmQ!Kp6[b:SU4&au!GpHq`1-;@EWJnSH/<;=Eo;j.UX;+N:Qhd/NZaAf,CPG+3PKbKH9dmeRk
%BK%[+_\AlpmSE:eclR1CNM&02s#k'AH[(kJGf*[u<rGiNX3;-[ak.hni_AJZi&brA$=B5\%cBlQY5?u!m+;qrAbp_RWcS&*n"+0*
%SaDPN]u#$4^i8m5&$bAFnOBmZ6!\*6_,IJ5KPgSa9X*;C?2W,V4P9f^ke]'`#J=p;/)klo[T'E@7HFekC3RWt92m:*_+Q/&/u6q,
%VTEJd7L0^^>$Z"-#qF0H5K>gditm1-)E1%p>CZ_d(IOf@NZFM9FeWhQa%f_`)\B$RI"LmUGA'KeTr?JrH!JkGhBc#=VulVH^KFq'
%lQ,6/KagoToG(/NGkX=h?^),CrX;ZUZE`dMDIrdFiXn"2Q4&p:odRC8KKn+G.DnTJnlCXBRSm,lr^+j`TtP,$r/)a`ES9C"g"$!;
%\2E\.3jio,bC_'UiK\g6l!^kWA`>b"FC-a^34)Y.nH:f0HH&#2GN;[6NXU!9/_/$ZNDsdKkJMt-%sms;/#n;17+::s>Z*m9i9JN,
%WFSkTH(/V4pdr6%462B`p<E)67><5m()@(g?=ot<J3%:?\Ru9XP8<\f^+jA%,l)-X3R3DFSmhE%;o&WC1<sKrZA+4a47n[/P3*Jl
%EejedZ?H9;nu_t&;RZO%$&lE54\[\mll*>['qDMj9DLc%EGSV1?NhHN?=^k&,_Rem+8'?toQo3!TXn/E8X_dRT&?iJ$-kB@%8$G)
%2.i<I9_=D3#Lf`R*S:3P=/7>6X'4?[jXX#b+'APPVp*57@9+9DBZOKp*5N\@V$(;,P4V#4"V,n>Um+^a`%7S>p9k'n<7Gj9#UIo"
%;"7j[C5F1#B2bi4Z1oQ3rT>;M"UAO'JJ_<?3kNJ&jE@s_U<P+G@iD><E)O:@:tJX^'"n-P>sP%/B=Iu50$8jIE-?%`"\YeqZ;NiL
%,4)"XKRs1)\3/)]0okE(Pq:-S=cprY'"<aNM9&(R_W!5uLhF[;nfkpf*`bVh27Gpej4G0hFV<Pmae*8Z(`7+#SBH)j,s'>M^@]&?
%BK&I3r[^(GIA%%P'dd'd=Q.+iC'7mSD$dpk<MpuUls,R)Y@_(lo>]=E3odSKWerhi"3rknJTW6(,_H"bE9Io+\kk`P`\4TfI#UH.
%]p1I\7"5P0#Ko=i:Z<<>YCF50?MWP5rnAOonFI1jci<gEs8B_2r\+<NO+7,Cs8M3ETD[[Jr;Q0a^]4&3s48@ml@8a!+9)"As8;co
%q>4NJs3(HBqnN+.s8!geq8^8Hl/t;/h;A8"mlebji@k3-hu*"O1r^VeqSV4jbocoQT6p@f^\"p`rqH9,?i87-s5hS@;KQ8p(&9Mt
%rR^GgrrT9kJ,CD\?iPb5L%c\h(ZX-I@NoagmD&ELs7Fe8YCH0:YQ"%#J+mTSVZ,aOrqH)K^JWR"e;LD3OOuHf=e!PWYs#()?!;Ps
%h^UOg;25cL7M-m`.J+T;](`"0!=s;(a@Fk5fLHguR(X"HiuYmT(1`-2.*SWbS`E2O!4+%3$<62+FT9MMT`g9:9?T2'0P%#$C_;?Y
%#pn;4<HM)iY3HVHM*ioRV[8=-jqf1hg5H2p!5p:VH\g_<e3TflK71\c7m@>==!Nh<>a]_dR$/A`=ptmL;0YYX"-r"QM]jN:*H!8P
%51q^_*uEY$^n>L_N&Mu];n=)?2)"/kMel=SZKpR=T?)B9%#P`i_A'2^\4XBLJ15N.<X!fZk]T2/j6?oA.JpH:TW/>ks%me/r[>Ds
%,o:.V8op1U)^CK(1VsiY&IE(ZLrcmWS$a)e*Q2A/h0GX-.A:&h2['pDI[inXPb?"SK4lJkc9Q'P<+5uXL?c(2n0PJ9GE]IgcF&[T
%.r8R]SO<fdpCJ14ETRbGMmtU%E*f^,WBk!J%!gCiUd^?;&'Wct_]F*F!.o4R5R=NtID)H9.d1mu]iDp+(ia[I&>,].b:Fq(L8(:`
%`m92`KJVJ7/k&DSZhb#s92TX?XQ.5Nm&I@$'5lJJ+KO20?Y4,'1A!Wc6eKV@&<np^Ddb$)(=sr?!q*PlGe0SXHtg:%UK(X\H)2u<
%Mm>i=g@'=<*:)cq5!3PCRNG3!d@<j2H;^,S_W65M&I+DgUCp>;*'Hed*?:!9*J%.*'+tFH?cR5XG$>sEn<HOfmF![Z%<s@,T0Vcr
%j-fN'db&SikcVGR9ZB^7NL$T%#4US,@]B!nk7Z4FlfKL(g7%S![d/*&XaqK8ih2)EXs6d9=u2DFHPJmSW<Bk04\D@I.-F08drUsi
%/dje)`/[^]:FZm87M>2<,Y(?gS;XhKKK,q=]Sm'P'[5asCquYZL2&/OSS9XDL,-A&N$7koZW=f]'!aMQB,*`L4,dFbjg`/r&DId#
%:^'P-RJ`gJ,BGDPBG0FE/k8&Z%VoWQGXn6@#DU$B:!S*AMrNG_%i9*9K44qK0E?(uU5pRQ!>,#\UH9n="fPd+JjcA4O,o;*j@k-b
%G'Xc5;OE=2k8!q8EUei;n.91,pqts\JjNr;9LMu,#H0omC$DXLe*jt)JDk5@7sV+^,?+D^FrFM5Y!hsTrt(%ep\?q$P@Lrn@./_Z
%Ii_ade+`VBatQO41_lJMV-+s@b^P;N7uLtLJ_DNO5)Ltci2q^m4n3#@>nCP!S3`I>4@0EO"`/74HP?6/>Pck8A4Zs9"7i(<ePfVU
%+Am9'=UgiqemCsol4P1MB3ts#f)<%ZOW@+"Yb%<]#kk<77aS^\]gs&+?C"hFiYB*@Q"sh=m=6KILinm[3Kbku_u1&Nf4lBOS`Nj?
%>tHH`'(0cXk_-5d.-$&iNEjBmOR]F;'VWF9a\$iPHC*u-E*mno+NE4o/5DES$>.jGUoU>`3"Rg-SJk"k:S1.ogpk)^)-W=>f`b3*
%*[5R/ZhofGUS)I+%3g)Q^GX=)c4aop_l!O?EFc@1<2HE_qsf+%R`T]%I:WDls$=..S'KKN9BdB`?)>mWHr2eHai!KC7.B9L`=J(<
%'UrBf3L5TDFkJ>sHTn[W)fg^h1PbrFXgR9D^1@jlKSrRLVbH895YLqDU6_s6(+=i)Nl6J;Vpp%qjd\f5^4e(F3CU-P:0Fg4YU[R$
%^]JDs[/"IgBb)no]j`Ra0FT)KM9LAekTK[WP,sN`iHJZu#Z:W2p$T<\>3b]OK*(.U>?_/B52TOj1Q\]P\Lq"8F8NF9J"Dbg@gX'F
%,#V#@#EiPh7f^-CRMPjINl0ZX9tME%:!?iYj\ajd%&@O4:;Me-[FK^Kc&]%98>hr[!VM,'$ZLCc-rD;a,2PZ"JQB0O'J]HhU<u\#
%*dZ@%0a1kDi#g>AKVIqUDu:h]%]BuVmaL^9?)0_mnj$:$`g3"^$(*$u2D3q$bL-GT%8#^6X)jDTcaR[m#*u?3K]b1r'n=Z8jr^XG
%N*r&0Jo_tt#5WguNOrkebla?s8nns!D\W5]msY-W6O.O&5W2ugF1CXqc_=>C#Et1dQ=I@0B(5ij6qYaZ&iR;)HISX`'mV,s?<?]@
%Qo:lprEu=aJFR.;ft@AmcdaJD=gLdO&49.^`k"We3sj3nm,n^Da[.)UXG%i=llo+FTd6<tdqc0*\1gPE%Ip9=#pt6$CG>*liCqWF
%;bjc",3ii)YX;jRCGYh+Vu'Df]p+F+Hhb4CVrl.O"V.jsS<rCfFG@/cKnR=kj9>ht!>8gu[ETt@b0/s7832T<)q9#GqFWh2Y)Y"O
%dZp\7_j\N?\Cf8Qf[liJWAOP$c'LUod)lqW8Y?kNhBeM54igKP#`4K$R@Ki`Y>UJuj"O#"Y5],N?8BFTG>MS/"YkSl0qB^qX<6=B
%Ti>K9AO0BbNZ["$O\+'3XSGG,7h(Y<ML0(+/TrOX!Tl$aPC`Z^(!X"_:T4S-qphQ=`E\nFhq08?.[MR%j]k&SnY@'rnYNXi:dg[c
%9<at(U6ELZX6=J%@I9WS*!sm5O\IE5,XG)L&N*YCOV2*K&d.6@n!e=Acq9&LKR=84f[Wi`bV9^X;/VWV/%j>oNt.8<o^-=K[VHk.
%V7hQr]O%GMe^?=b+%TP3R)6]bmjpnEZmDB=lrpieF#=@m\#lNuq;,N&mLju4"O>mqB?Se-Q?$'MIP!)tGm$5c)8h$*UZqrS@9;I#
%-Z%(i^JAM,W=-R:^RRj[j`+7uZEtVtHU*@D95e#;>DT4554%@@rCmXS<j.Q1PQ=^eorsq/LHl!-FnV,$DA@9Ao)u$<bLi%W+UVNU
%$%.^5k)qiLi-H5&9!i"M$dtb%Tdn#K&=_G+3*^`E7N<MNK!b9<H.f8`I.J'GY?*s`\"FuXp)%nqX-UEEPr6o#ct/r)2F23BN5O_[
%qOqA83+b=Z@@^><G8>k(ptTVt5flK<;a?29s'04C*WB?/7K4gJbf5Fe\lhP=%J`*'em%fEDj`4^Ut>&'Z\e%ed\-C-+Ab0>jS6R\
%p_?C<!^VnTfn96X&)SiFcngka'H1(ZOE$)A$1E_^(f=ll39:DG!U*10OIEPdd(npee]$%)s)mSj]`:H&K'>P0Yeq"j5''3,:O!il
%-Nh@b$t(7sIKu7Cd)e6ea/jcHAT`g_5r$D7EriErXq>.3eU$PfP**7bW)1XL21R0]erni0AGB50NkM\B?5Vn^#(BMX"0tPB!sX>g
%c]s^hQ"GDedBkBd:)l=Vf8_7El]?)[7Gs@MFEgi>IDDs#1aQKpS59@_;Ghb7A09&Vm<]X_b/>DIY'gi)L1\C=!+5qN#JaHu#7/J5
%j8r346fJmj'5#Z\p"d%9cV^0)-25#\f\dQT*QiC1ioGH;#-m9k`t:6,,g38>s/gR@)c>2,24/6lEQb2*s#26FZSKDM[^n?C?u-a+
%JEQ8A5mV3KD\;HTq#eE4IRC-Q(LcT2<kbQo,%EupXV20T2o!O?_oAEk[M/tmlaKrY</mWa**K!3+q[?KX!F^t9ULEE"1b!aoK+B%
%7$="m9bJj[$:Y9"fW(:)nChh+IK\T2OS[I@0!%#DS2O.k_7dR[iqm[oI=f5PDQOb+VnFHK)tk#AlBQuqYek_mEKd]u%@7Zn/#q`[
%/L6Ei8/D:/J5H"uDF%Y[`W6YK=`NqPfd&uKfpsF8ks]R&PXAjRp.\BDcjtm][/P'dr8XV*^E2A9r.92Bi7$pBhbOj4O\^#>:5eLV
%Z/`rJd&2W[;Ik0jh-Qc*V;CdkKY3HU@u$WS]ECRPKY*KaoDBjL],^Yn%"/d'P`;l(+K%B_<6UEon-_]in1CU_KAYtJd6CX9dJ-XR
%j9td?@$bRU55(;"7jed5>T*+APK5]bK28Xq_EAN5rlKItVLPR^Vla^gAkkO];Dg#;R3%:;>"^a@JthbnQ\sd>eK]TAKN_1`!k#qd
%PK.%$qT,"QoCQgU#H<[qI:QngC:4E!aPGMt'4:B3IHpA$!>IgtQ#_3<&dZ'k=o*71N#>1mZSU",pG$*BGLY8tY=qut'P4XU9?*+B
%#N^[+>nmJfkN!S"M3A0UU2&ikNc.^OlI85`X2!10'p4e6adhXf,&bl<>%lMD/[<.+UiW5>NI3QOk8Jp)1CkOl.]K`uRlYXf\h[@4
%UI#N]YqP\<.j*g)P!Klg^qXj;*)PtkSeenk\Ic4q5PkKr=1uhe7Z3f+'#MJX(OfuX6du+UOc(Wg<4<WG-+fLKOh+Zla?LmkdX"4p
%6#tJQPPrY#SM@DB\/J2t:2)u7(-TQBfl629=et-l;-Q-dY,':#6VsSk?JJdGAnt.MV169'[HSO(R$NBsB&F?+[X;#d(?aHU\9p#?
%4!_RSS>P50N)t/pjD/;n`/c=lIQiD4g"-MD#"#d^):ljCs-'PaHPb!qMP:sn:(Ve7KL%tt?#22E''Ll?8fd^RTW:d7/()G4i_>?r
%)h:th-1T//j$aeUY%A'UXL)SRM^/QX^-&2L6Rj!VLU@itP@[Sp&D$2BMsZU`HH[=0Q4STcmfB#+(=DLk;uE%F!=u,dnSVKe3Zl_*
%.KIR@Dl2TQfbp>\:(1MbWQA_]]bqWeI`,A$NphpB&QcD+s!j-EaFY/jX7E:J6%muTN+\(&::R/V(c]l8a.6oR9;C,t5IrAdqb&_Q
%`dY+V1-+/%-"gH_TEO?=VP5c3d;f]s"0'CJ#&T1I]"(G\/W^mncnAPfG88Z%/fk>'1,$gqg@`!p.uRe2c-dp-0k0Ai6]_q"9a=nX
%&J&sjP.nffs1j)W.7Il%K%Gshl*^Z'FQ#t`_BU&MEq\i#VYET-=VBPm^&+@.Y7W4qgB@:L)`6G:FT^cQma.0SL-1VDCdCF_H\O2\
%pD3fh-&=/(NR0J+.=CReg6jXUP.Pf^JX:\F/!WG69Z;4%^Z*;b\(c(0<`&9KQ7u*]Vnge3%2rHu>=:(C?5)1c$;d8Rn:Q@jlPfMG
%$uBmE\8tp[ml^Z/+Qs]?#!5mt[SXD,AiI7K>,Z=Z$sB3@4uTnm6'#*<;#5qX0f^kT8U#i#.9sh`T6l/_FYB$H<FdCq0k>tL*b(4s
%r9B'`MQ@i@_M@jH.&Nq_J)<;9',$7h8ufViWX)LKJi(Lh^'1NH8Rhm<CH7XddA;`"q)imQ0\2mfacY-jV_TNG6tKK"`7^LNE0XXo
%^KM<0=X,E%=(Fr-R_^Hn-'&g]m-W#tQn\Cp6GT5I)m-Xue!5?cbk/>cH;JK(\PTLqCmYWj^:YG9.9n)W6_Os/d:aD(rpiUgd%I\9
%@dl5S,td.lhcDi4=PVHQX/R2\4.pKP(CNBf/PE(a&KO3r)[>a>Xd>eg$)FNIWZI*5#2&0/qTEMB4?tMIA$rAHE*'J'.$qcCI?5%O
%.P'f+Y.:3CJSY)0[ntG=*AIWbGUK@$(Oo2V^A8']3;CiJPkbld32Rs3,bBO#9A<9;kFO#Tpe!$66>7TqM]fD`oT?Y_1]hpImjWZJ
%3HEZTFK+N"CQQ.>dTaRmF;ORLju5HgkP$*!=-OP&M5Io31-F/\QA=H>V?bl3.-G(j9]M9(9M:&S6+NF[kdhC-k;0,l-&>%:eTj^1
%A%s&I2C)tUos2bA")DZo62Mskn6MS$8VReH&C+Q$\_K,dL.793@.'Mf>XN?[f[F*_DiGhl".!.mfqC8FGC^2qi=S&<*pk4"AoYXg
%`U;E#\pV>5X0M`CY/rJ\s#j'%Q?P.Y=S*/?j`1ful+,eHF\rGH2)A/NWX3A1/>7+)Ff?&4%J4;\2'>P*dqU3O3.!D#QabZ6-03"9
%Zau[E1hX="nKnONcMh9WXIh+T/JYY40Mofq[&-1#04AYJGrq6B*i-&3gl7-&6E4MfX3,mjh@5]khoB\7Op$&a)U$?TNoA[?c!.:G
%75`4?Vj^lK/aNHj_ObQ=o@\kmM3J^Z]$X;UH5!@ACclIeMOZk"i(1#.07/Fl3Y;ElW,FAh8*6:6%"@0Zbn`S9]6Z@J[f;%mZo[k0
%NfI,-n5?EA&,=8;K&9/R1uHV/>,"=%kg%hZ0:uB/plD"n1+:)O+_F*(6&pP3MP.5C$A(Bb80o*fY-PW/:gO_r$Ui[P)F;T"_eL8<
%//msGN)<*0C9@&]dtfh-&QC6;IANc`*"$s]$ClD4^d6*(EI<\fT^<uIGh*(*gLM#n`pFrEGqGXF6G3ksJV5k1jHeC""`iqQJi2hJ
%a,6rsFR;C,O;4=Z&nUsO3<7iCmRJ9nJ0<9TjBdi(Bqdj&VpkPe42m"QQ2,28:/,fgiD^P5n4KO0%5>@b6o`2X4N5nGhuCgWp:EAg
%s!nS%j79nfOi:jqZS\3pUQ`6ra>5Bp4#NS.=FLMdLn=5*bNP$Nen9V]YhfZp+PGcn<UdImH@Bq^@e>2p6(BW!0Z'H*n3-ajlOaQN
%Je+7eK^DD.ddlc;&o`NZ(EgUK49,qoWZT]3QC5_Je\Bmb"oUsko5.`bMYj\pmFL6Qi"*akZ:C3\[qurX/%hUrX>5,JP%ZqcjcWA0
%CrsQOOO#KJ1jhd(=o2#^Ucs;57m>Tof[,O!JHe$+#IMKq%JWK9oDVn$L'3@4Jg6mCE;D=k7'2YQ<oQSMd8C+G<h77pfT)J]kH51s
%+(mOp`"]";HqJ.G'jGDtn^#=)(^48i/U4[>O1Q7bP_'9EeC;3bHn/W"rH6>]MA0KX*MA]tR"ONN+a%Ha9"KU<$rT4.g"ZI.!,I<O
%fq4&3l\Eb,$Q]FU@3bSRoQX)E6k<U55P4!?q%ts"<X<6-&a6coQpn@c!]J_#GZU7,h!g5d!XM%WWs3U/\0hg6,oe31^EQkXU5,K!
%n<D3@7L=:L*R<=*&D&q4IZ#QSe*=]aC2CKK];e86C!>)\pp^QSmqqce9"L1MYjWcr<KHZAp$EAaU&05MEb@\N=4J7(1V\YR^qARL
%TmYFgUq,4[ehuh[Fq%[]Wf,#Q0FO@">Tig;3AsMTH--5^M>2_*eR\(LV"Ttn-SfT,G3CAHX:TNeq9G(r<(mLaHcD-FdV+nK6H[^4
%l$/CJHStjjDbYp(=E9KYGJGW-!=(,@S,LB,M-pg7N%21Z+_=fa#ZH7@3!bbN"Eq5@@EsMgX7r6qnf4>06?1PEa:fh2+uN=]1`L:^
%7W&M_b2%b0kQ-<j;Qt]d+;bGD>%kL6(qF36?G+9f=p4c=\3FdL$G5@BGAJ2%a<Esn,>bbcSm^C1^O7Ag8eYjHSIt3>$,J*ZrQQ=5
%/7LY_Z'G-sN.X,[#;IFR'bgpc;g;%95Ps*5*;3_]h5G`QMMX;eA)kUM)tOZuIuE4h\jilrM^#kak=!?G&L`4n:f!TGQ8T;+1hP[M
%e=cL@QNT!JJu*PY$]=RoqDk/VP1%F@p_X"7Q8W9`j'2(/YbTHU1]?17*d']fR]HrXcHne:ci&SY!T1rEmroaNpu]XRk3J7d%c4mf
%f6m>aSUXk&2bb"Xnb+_hOt/oTnbgf6T2"`.*e5)>:<T>4W!V5^UIU)XCD@s!L\V!f;r?a1#-(92TWVY]Hat=(cnY%4:)Y4:LrT(S
%CuLS005`M<k:3]kQYtB;j<D*[I)]c_?l;LnP!WkM!\CknW)&@N%o&C)!*j:6<0^ZM!muJ-PXm*[!+f)ek&saW5mal-.B4^ioFDg6
%/6c-KL8)%qL=ptPQPH55[p4&WO<d:4kS@&WjQ6r%c0YtP:#6`4*enU0o+G*aoRg\%L#Xk&>mc=E3>hDR`)96+$d"YRY]$3B#/tko
%`ehr-C-_9J^[C')AK$Nt\=_)4$L3FkUB'G@?QWabGt2#cX^[AkfmF7'C2BV%DS5XT?J;r]XA&$4PK.k%6SIoZ53N_)oBbG,IADpD
%P@1I=SWUn@2jj&=lnWH/YNcT%-t.d$bp<<8RA^o:oA8OrNc8sog=h*T]9*CR>D(Y]+]0NQ)]9?f]t&jn+2GY[,s3HE9[Qm`'9IfN
%9]Q2PRZ6nc#"se"Y\NrK^Phi%$ZP`JL),\u7,nN(ibA8Z7?D5h1f1lgY9[!RYV(qOL-hQ?;oontEr(Jd7,b(7<T1bCXhQi-n_M@6
%TSpugHGGhoL!#NWFdZ9kI(@MM\"][X'Jtu.(4sm!#rNmg*jSo7\J!qd]>]Tu5'H.F56q1i&WZ.%73>B.^43G580,bak5aU\4EEX,
%OtV(=:&$D49:@*IO@2F]YV!co,r!MJo:6C^bX.nueD`Qk<.A?r)AdW^@3#&rD,<6QoRp9*PSRoPUI/gW:ffAW:!O+BF#6A%$U,X;
%24m$%Xm<Y*41\:d3=fY@A>34:#1q8mjLg>5!7K)V&Z9C>cP[P$"EhLD4bdfcq\@Z&..PL)$WZ`&?]=`m4BE`>o*fMl7%@<<H0o"T
%@HJaOa:[S)"Y>3_per5AE>*P-n5iVCTDj6n`;b3_7fP4iUOk;K^IZM'Eo-g?.AMHonj!+,k+@MQh1+]Rf!#+m_WpY5JK"Q\'C!,t
%8Dh3F@Ahg/=<dQgQ<.Ldi)WrZ:dSSU#;HdhR-OasB^@deBk?Q;LO_2]B?$lT()_ggYMc?<VHX$j#U59r]eV!\?_uI8'#?9`*c>I2
%8%2h5n/[\>@3LA?Ybs?-&S/UGQVgW\/QbAt=f5A5UJU"?Ob>,`Z!p?m<sa1sSa4F*R5!7#9PIeQa4;s&<(3m:MH*sn5_o*kjhY7\
%^j:V6M"=g+6"@8L-%`r]4Y!VJLZ^[&C?t"gi%3QZ.Dl?#nD$$hF<20uo&RsR)rkRjVuiJ$4C4?QI/L_'HsLp]Es)QlbWFBt^_*tm
%-_kJ.\2Bb+!3A.LNdt;E'GBa&;&beej(mk!DSUl=?\$bt;HrYKQ\pDs?"-%W-LY``3h_H+3[O8"]Yb:\%e!__6"%EAce5XVnje^H
%Meq99@\E=GTjn7_m8D0C-dU$N$Yfn%PP*uK;+3&\R9Ft<@km4`98p1#e^^9QF<);t`KNY]4+REBnifY+#D_N=*QlHD2qn&)78f1_
%=Wd@mHLCr=O?*tR;Ii#2l<n6\`2/$ETV!ff]>b-)>laS6eo9o*(pHWO4]S^(*+T[L].F8a9t/]ImA/"s]4-1$<1SqJY#utIr,s7\
%f1W^SNSp`LajSRh$5SIfj^c`#`*0R10bbcI6-HOMrlSjD7s\D-L/udL&?"=X(NCH@q)`8AbS\QYO3^VJ(D2GuX+$D\3FbMP\%-:\
%Jlk;aW0K/n<3+g%SS$"%85h+p>a@L!FJLYdUJSqF8!b"sH:5DEf%tRHDp1X?43bnNJ;<D=FrjrP3s+6mTD3Za5MLFn6U-5:m%<5p
%*rp%).?41j9T7mjd$rbCe%elbRsY$";TXl_Q'?XrE``"u8O/9Hn5ENqK#&N1<.=Z'TT45>egE<m&Ke@?Mc_icr%b%Kp!JbNqO4PZ
%`Pn.pOH'/niu17=Y>cYi[JB9Qbp#>:,%L9DSd>R&NjY&Qppc)$!fW?GCjH+35%ZL);@*Nbk*t#aOhE8@>s)LnhZ9j\WS;qI=>/6'
%Tn&#-gC8_!H-LD;G$)RBAg]4i(X`csf>DDL"'Se.0j>R4/J9sRfrk(?AN'J*Pe;/T59][JD_NgNoMDf''X\*-!jWrooO3Z(h#l']
%.[QA5e#\WubGOGp1T&$mh$qD5_6oc4,7qVSKE.k9]"`D0CAkR785/4jB_Y#]rgp,99tA7eY",bM?4tbm%fu>1bg`QgpMh'_Rt=&:
%PlK1u8K*.GQF80\T3M(Nik:UR*4R&7KI&HIUf)bSd._J^1&Dk68-5FXm#K%&m^mH"<LQ0U]t.]2DXY=V?\m,);t@(h9QdPrXQA@8
%jogs!$VTFj_P`^3;e).j]'he0r#4#_&cQE1l<8%f)6I":3IqD.g@;q]o#GH0aD$o#kjccn!9q1ce+dL3UXQ07[89pX>W5h/c?'4n
%fc*1eKCebXR58U]a>"H9_bce]i"7b:G&;rhO9Vt6_#Y\<]:r'Tc3d,Ug?.(naf;:==Hk$^9cPumJ0dhR%et_n*P\8HeSO&moTC08
%U%"DM4&D414"`GiiN[?dj(CQBm8d'=e_>ZH]%l*jHE"[04pYdT4Up#S)RrGM<liuhAp=frK?Kilq@%q&`Yj@lSLe0-=-2`SQ1[sj
%-5/.j6df'7F7dSOblC>><cDj*lmq4uK2PZ65B!.>&U)+7=]NftmEQ^T61sRTH^')"Am?]5`_SQ7eWeAjRfY`+C%MB?468)KVEg1I
%r_ap/+tca*SC(X)mTf(]AYRdKD`7$l&t7Z)7t>j>[^[]ELcfO#Ms2KF#3VAmY%M@d0d!6-]jcW5bcS?7=9^-Z@rLsn5sot8i`J,`
%>kKKa3AALGKOp/4V&?+NL<^D1<j*W`>"+/]#-,I,YfM`[?b=%CnY(lZPsMft`jOM?cT=6C8\B)K3QLup7-Js)!CaTp_PSI,=Ms?M
%qgR(!n77;L1"@$N:W,VGQ/n>)5`q3N^=:-Q"bSE+ed`BX_P31a'')C-Ga^'fJuguV24"e3>'&^@rJe5[]7Ta)@s:3hX@c16ag6/W
%\uj-UmDLNkWWqt[O\?,mq!?BbHGFMtRs$X>lN0O]7[O`d'I4`#O7N:&5S,C.0W_7"F-I;hoWUhNg8B,^A>b*%[`cOC_F;s-_.tJ(
%5#@.k/WC\$2^>VoFlg4?7a2tA5$8-hq&jLn-h4"&[1J8eU$-5'DWAf"9n,h\opEg@5@gfFe=.!Z?lAN6=_#iW23KoqqB@mD7q:.P
%'5le>\3]+-CjdE+o(6foW$A@@7N!gB_l?&Tf=1m>2DX.MeVg).k6tC76=F)8bg*$L<D7[[N*9bQNHL=PT\]9N0qjcVYk-[Kln[rk
%7i-#Cn^d@I!AZ'Pi.`iB4a'h!As"p,-c1nQ_s:;l5Rj#s<N8.8nl2'bAUG7GP`6k]JAipRh[c5m7BUQjGa.nO<2lBRJX<#tF876E
%&OI?:+%-\I#R3mHKi+sEI<g3;5)HA?;WLXDV*02'N;3"&T-Nd3Ak`o5($f@6ilFaH#W@12S4[/KT@_167]IdY0`3BYdKk";g;!0S
%s#]P3ofl61"!G8V#T]]/no9+gLI0N^E."[n`US_B9bMRKge`F-F\:1X"UXe_8n5CBB\KX<)IloZ;1W`=?EgY*T+F<"E+fR5Rg^1'
%-I#fOCKTEpRNr7ej>dik7*n--8;bFO(;lcNj9SES.n(c.mXUk5OIhfA^s0,Y:`DC`l\99a19aZ6\ad(S.l]i#'re#iBS?V9^#usb
%O,m\XXFL[uKN$-hY[Rj?@ERZ89R.(n"C"rc7kp90&[k[ZUmR66+JG.a!5$2kQ+kP_b0%9udum=a!e(5lXN3/nUGaf9KH#:uW*\9<
%h[]\biV`q.JjSX[@_p+_kCc;T%OgSfnSEG<i>qA*QaL`qXr@o,Y8>MCEIEdMc2`*D):-l/bN5*eM^P0M+VdMq9UO/,GU=7rO#^\T
%"JXl@p&lU2M*ZG_ACaI(1sSS&b%':+JX>sgXK@7RPklBVUjGZhP$aGYj;KMlEX)cAJ&YP*VWI]?b2rqSO2K<>BK1_;#JF+ko2jd9
%*2S?'.T/OKq\5>)&VA=X3/#4n?P1e8+o6Uk\V]k:eRN]l;)#"@jB,=VLj\46lcaQZ39jJh',tcPg&IfA;Ctl1+@h0TmC7u]D]dM5
%koMAXS'aFliHhul-#$t5_F"l)e7o6;,F@a,A<b;?fMb>q>0W_C30Qf7;5&gg]-,XXL%d7@%Hh$h]VRqN&i/'+@T@lV,&['$&%k8I
%j)uX>#WT7OlZ$%SX)gEV<d`YjY'lc%+^=K(g7:66=*,R'qd"kTUZ"Sh2qtTT&L<F1P;EF*])H9Ud19gdf\j.>[Acb(da9-(k1$>0
%OR?3HZ+:8_LU6(a*fR-4$WK@%[&A:sb`h/2cQ;OC1`MB7VsJ5T:<-`%&t\4:1M?Nk_:DWY5^9r":sBs9gOG-LpXco6l[0jnQWcNm
%#73M6OJMs^T9V<(!$Z8A?B6Y$-OjI&^[scg7!dL5#cZL;QcXl\?5pJJIVi33[i]-r*b:!,"l=W0`tO>`FF3u;*4\.D8)+_3R&AFg
%@*j$N$Lodqo@nm#bP=r(nYM>]/C0)7cP0o"R:"K]r[+QVjmVc=^nn#:mE%N/SI5*FHh`S%l%X^bIfmRtT5%A)3#ab1"e@nT$@.RZ
%MLlV1F#4iFcK7@1U9gZYK#1,?b<MlC!/"";3=+3jr(B6/Y;(W&i37%bX\Tm#(9ng\(#*cDgifK1l$(6/dkZYnr,fomGOJ35,aCb8
%XSq2:s*EZ%hQ&ccY)tRX4bHgY&l!j"C*94M1&CG6^6B)3$`1]LhX[HZ!Y4b0A;TE$1`N="g]j3^(q-7b$UR:;_gVcjV#-aO4uVYs
%0n,H!X/HesH$@A*d_67iP!158M!*`G_P<(I1r%9U[E6tkj/$i&7EfDN.%D2X]e`:_787PXPQ66ErLMpMcu)MEO@5)dF:!l$s$D#q
%/#d>j;oU1jnB`AI;Cb._W6t`EBDJfq)lF%m$;pcf!pnMU^cp?uO:m;%f]]<f,F)D:!Ccl#XBG1KqD(_;Lps^Vmm6Uc%VpE7"H-ck
%%sWVhXTR?c$OD[/97COsD<ZjBe9u!X4SqOZ*s1o-"]^!c'*dRZ/j_JeoVS%3s*O7fJ[]hZMo9(2pn?\`jC12!%Fjg#+W:Xp5(-#@
%D16dKA!6M3*#BS)BlI;4I/C2FLdI[g'3(5d+7D>$=lZ;L8ph=W*oMUc;oUqrJiZV7oE\<m_+-6?BSj%P[H[ONl2\P!Io?K&glH""
%:3_`\0r<bemf=_a<L[XA]6__e>#cdc407>3h)oahCRSrlT/Wo>U(1:s-(aUcFlYV[P3\ZZ`SiqT!qn:)K/'r#B@SiBTS$S:Ln(SY
%#(<-r!O2:V51b>$Qs(f_>1#9(]f3*qp_K]Ze8T*(drN7rjRrj`BocOOp"@,/6E7TcRe4Fd7PUEQ)p[Q-8@Ig?,m@_G$u-]@6.2[-
%_>T@d<*?14S-=qA&)h=Vq?e9aM4eV$<6PGtlL*C!rV)V<Tern:n*hE]FZ!F[Pe&(fX=(O@Y6SIqq2AJ7Kl1PRe]okq]GTtQAM*.m
%:m&,7?@uPeid5:jFfXOK4a@?gk_AmZh'?h.)Q#jI\?RUEP^KEQ<D&Y2Z'Hmr6kJ;LISl#/]&,$@OU",Q4FI5EN6mV;Yl^AZ`)o"a
%Na&9`;jMm!*(h)-I7)>Y8>0^NUO[q1OT]Mh+t]p#+e4Vl-,/E*.Gh(Ke8IHXb8*Bk;4A_<-R>>[L3%kg1%t4ir"3IN*7O/p^9!H?
%`Ua^r2FF_eQU[$'9N)iuq<=tWKNkb0D)G8rW%$3Pmg&@4DZ)R%/iKA`o[5\;kMrTgq,Dg61D>u3@hq:&!i5M&H\>rG.2IXWLFDMI
%<EMAIL>;6mL#B?jlR&)6]a<2I^[ae]-oZ09^ot`9,WuZ3Y#[X@`!(o*IGKW/#=51i0nK!'*8=XH7K__GjA4VZP*7-m15_*
%0cL%t61F@'H%'66-WJ(5^P&:o$]!Y/Q&oc[4l_I5\s*K/eFA)1ePWIOX.ka5@ZWLrOWI!U]7eBH60crjWe/eeNK!OlI>JJ/@4M=F
%g$?>7#*`40cJ6(:hW1:TiA"41TAYe;Q:KRgRPJbXW.+)`G]C<]]JN(OV!^teZWBFJ27K5K#@uZZ,#0^X`eRsrQ-q;:5?7psk"+:)
%)65Xf3<s-<I2+"OI\FB:olhSKh0j!!W^6DY2jBUX@2>r*pRTHgFJMeG8dSCj+0=^M"^0a^)V;b0#jPf^GB(IF3N"=YKP."i@J'WM
%3:HpAgqpfa9Dk*(Gm:;X0G"Z#ht'^K^`t)gL,[Lu$].W82BWhe5BT&1'2.'@q1`ckN\?MN&=hgP+D;WD^.Tc6HtWCF"jZ63Gj8[L
%lQ,#8*M1@K&5Uksr'f`s]r.tO0[nb0JAIdSY%uc=S=%)+B(2]Q"D$MFGFJuCCf6pO##ATH=jVpga%EHgjFP5P6<9%[,m]P#Q(X&.
%nB:m=8\#?%Rl:qk[0c)j3$s@Kbg6r!(>%i4g>j+jEX@:dA;u.cg@<=JY7s5^'p^9..31:?jD'aK1j+;SoR-,j+d2GXX"f"5^I3Im
%Q%J1.>AuE%,HC62GGnu_4u1rMdG#iNTCopDU+hV#O"RI9_hGq]Xe\Ilc5aYY'0nE7F$fSt3=!r&n`b>,>:1djXSrq.^_BBgW\3+%
%JDrLP8j_So8-qQDM6GPUjU>6-s1dp6CQi>KFY.o(4$rsUqjd-b38oU>k?>p")aQEGbV;Wk/_T\P%Rj6#Jhm#_W32G>52>%Ef,S;G
%I&t"#>!Sj#)+fY<A$;FRh`5aA`^FDm18'Lm-o*^&U5eQP-"%OdCYr%4X:WZ6pV[^/nI1^+>V%2=]i%J)\t>,3E,!5n^nWY@_EI2,
%E#`L5B2"n*KV0A8b1Kgqr"2bDqZs'W\eQF?K\6ND,W.VKYY>2+"A6c&[@1b\/2F=17_jQf<-_0U!T"Q,E[!<bWY'7S=!QE&Ng/kJ
%="Bsi]C\GiTO>2@i8Jt<q>jp6;MXY"@9i79kpjJgR7LTM1jM-t*f;8R&k#eqX\;R#2R$ij(;<Y?;-u9:&h+X%YEYp7OkHX(C^N.V
%C^$5IXjWHeVE-l6A6'Y]Z&:MZ,oKo%396Je8/=j%CG17g$+Nj^Cj"C]=QCC-PdReJOAARdm*]rh\j.o]XDW/h`cA=i@')mqM1c8l
%1?('B6S&u&'g1QCYXV(tLR;b1N=g6$adGtcR^uTF$\KnP;Bo+0%Vm%j8BgD%";RQ);#53P1M\kKIOG4WYn_2(`jkVoHie]e_[2EL
%N-T,=al%!4!$N,O2,`o\1g2V:8F'HXV0W,>43G-tq^!kSecR(5E6XX&0>88:)9!En=6:(ugYL#bDI>nM9Ou8hU_6kMP?Xu$^e]_J
%0?=hpdM3D(+j!AlZ)$A,j.iKeYu'S:BisW=5qNeL>\[/$%KpEZ!D)dk&6H#R!V$Hn3)j#iAj*_+32GJM6qYbk>2nF:aJUARD=u>^
%r,&[NY3H8?*HD^%>p75r:B!:o3MaViZl2Ro4r]G&S;0A'IVo<knen-P>:al^DN4-(M9<s#b_e%F3MLg^[jE44!-&Ig(/?eD%3mZH
%0e+Q!QhW&,`M-35?_&NuX'$t>W8Q'>EG9$@!o9oe#kU.l>a!mj."'D)oY+Iq\!A-Rf`^&I9#F1<A#h&o_*#]cbZ46_#%M%]l5<T#
%,GKkuRGL$975j=D$5mi8E_=<HZ,Y^V9eE'&G^VT>?=Xu]Pm'QplsYdLj=MC9HHA-,8,@%Fjp$]rK]L(&6S8G>TT@Z+j'qH1X(F?=
%24;YpHgTm+@ehV^7p(*^,-[UcI1d=U8%)cTYb&@;gm6RnQqDTT9s,%(ZP41t4<-r/ajdo%2HPfJ->_@i]O=Af(Wt=Y$@qS9BNpe3
%"H"*u$8AhVXB$u%EJEX0f^uo*U+<[cZlS/0@N5>?b<E1j$^4hUb(bP)o4u6"F*$A*b7MNohU0usV<.0JY_q^u&55(?[9/qKm_7AA
%mh-9b9*n1h(b0di801_L"#MD3&:l%\4l.'CU8I489lg]HeI)dak>hZWqnR80Acka0/tf(^6PYjaVq0i$<ne?an^]7LA+laq9P2id
%J4j,B6BuK]7=K6rkJp4D5_ce#/>66pC!Qo``G&5.FVMMo<T\(K4rWXQ2B$=iQ3@sbJPHHJ;+>).C*olqZ?S-.(Ta/[0dC1^_SI;<
%0%lKJ_/qf#hJ`K#_D`o5,,ALC#NDJU&]VsE/!s41LruKY-B'EC6PpYY+cgq;$42]?;LWfZkMcaO]'lTI\?,NPQlWe4*\>GoBf6e?
%olJXZjr,fl3)"l'&cCKX-r*.(?!Q+nQohZ'F*bEr$s0P7hNGeFR7^027VEkqDHT6dmh_(UPYha7ceH\iUhY.WmGZ\=:*&t-"c4Po
%,rA`ji%5:a=kVk,O#"DQA8?otPi0tX;G#!+;2QG"-nSU['d%G-#uqr-8IjMD@K9\pA(T';mGHPU!AnH&VPqpG<g'omb!YkV8un>-
%R`7_1&G0CMRbBm3ku8tQmJI.ZpC6j\'"+D#a-$ct[*%!TPJ/XP$E$k6]<^Y,?F(B&rgnKAJOG8f4hiU[?1HFB5%$E$Nu9QXjD'cU
%&o1>'(`s^_5Y+B,&V>!U!A*;=#Y*&@O@P!A"^K6n)PF1`LMcPfkc=8"Ja?H(BCC_odKL2C<^Kd_ZrC2@n-IM5:U&iKQ<Rh*"t,R3
%8gL"HGWrD7Jm\.u9D$dI9WnbA[GF>:EHk(m'"hnFRJb3?H^O(GJ5qfWLft^j709VT7uLF^_ZKj$/nl_j]flisCn2cmbS,7HD^Ebd
%$bS/(M1?X`/"q[h&hI&g(T_faY1>SDJIu.(6IJVY@[=am-XhmY5m6dOaQ`a84,!0jLi5mDj+PST0i?=o0RG;n\Y`UE1Jr+KB@W7Q
%R'W*/#[RUk^2%S5?,.[lQeOQff9*9s%d@rN=W-aq@V/5D8-QufU4U9&7aNqV=6sWuD,MQr03[^?r*X_O,,4.KY<?fg'gT2:?mLRt
%f%P"($9"0EdYV9fS.%PYAYm%2VRAhO1q?Kk`k!]-s&)W$']*QJT&-rFQ#$nap;=\rqKEdM5_B8[iFfQR=%_F\e+70bW[VZhS^\,D
%C."VW<IlS%0hIa:"l7?-d?mIHg<Eg3&cVuG*2D&h.-N,Ia@%=Y!M57LS(;`rp^KFp--,V?ad=&:s'L_"at^+/3)-B(+Y!IooF3&l
%R"2=o"98[*<u"f;_Ai<FRU:RjT9A!Y2(6s0?:lgiQ\,3!C.`eJjT%#0+s:9*70>:YT%i^*AICJ"E%>s?08ng>q&l`Hch]E,S*?+e
%)\<mQgM.K5#0ENIBsF8'0i('g\HPB#>q8/HjX"hkG-,T^AFOAFp&j\$p/Y_T`n%Q1ciR"bScp@+JsrP"BQW/$,=F(DOm,6O5.;4@
%h-_b5:3!I_a^Mi57\H\],Y]qCEf<k12`oRt5K!`:;;-nu1Kl!45o%#-1$'3R#F<[#j`u(OpNCD97rV$8AoAL06D<0W!fHfOM;$2-
%;CjmeH+MQq3=[lW4;6LS'l`1m2cES(nPJ^G""9+&bSh]:#GluTfhGXQb>Kj=VX39i\B*?[hLu@g)VQ%QkS@TS7,RWql!Se?en=&"
%!(]Ss!eX)qJ[[H:>uS-4?Z[^EC+5?\.j`3r(WWl@%A2P;IiT-F"/:%fM-M"6O:3sJp0n.td(UZc]g^s*`->;tKYEGkK;kcnM$sY7
%W%u']3M^K1l:k/#oV7=3$(*F+k,IuH#UpuYVmshQD]k3d!0ik+F":W;9Ih7h.fl_*$Mk7<\H5sI(\#E/>9l)DQ/NljKf/iC((./]
%:*TBUo%3u]/c8K3?.(onF)2u08Fe)aq*a[MooFkQWc[:TEloT)S'n<YDcP,1+heANJKema4Lno\PhGk=JdT=bE>,FjbXfV8d&8Fg
%blqWt4mu;s%WIn4Vm(/u^%%"-)DRjp3g)1Yl\9aALksA[AqWN^8lfnT/.9.)`PG/>OLS^T3=?nMmOL$7MmBr?k&CJ/g1#Y+3odbJ
%[/sCW+%^RpWhZ"_!a%#<#!iSiGZAFA/J@++P\MHuZRRXg[0`3X?<WQeYR*WYG&G&bU+XFL=.VGhNb<5O"0JitD=8dtZB8.*FCdEU
%4P>oZ2m]=7d=Tue@&*oo0o)I/KF*Tr"<8JdCcIEV6pbFC1*KL0$uk-5JN9*N.C*H:2EZH9fq<'10TZPD'!R2h="?R.YB&iC18:=:
%+rAAsTU<"b$5a"#)5XP?>]#?5g6)T7?N`Z3/><Er:X%X@3.b[%s6CW<5Z^VD^49kH%^i^3Y$.p!.jrMTO_`%GU6Ag@#pOI7@M19;
%.:4'E1(tCfFsnLI@jS"r`Bdr_R+=qn)NB\-+JDueQPCh6=;P7_GS^YujM>"6KV-b>.tHtK9JDgf_m('",mQ+,"4q*4Tra!,i-o5s
%UX,cXK'qISaP\WBO4rCl&m6opR.ra#_CLb/a<)c>!BQ9M1bDn41/j!Se4'[hKh"$`R>O8GQ'O^jOuc>=Q".'F=loFI5OB&331^NN
%"STj-84)-&>m6VbN5R7kIBQ_h2[EOhPpdS^Vi-+@gAsBA$]#S]&7-a*7B+VM-qT<0Wq:p4I<Ye$FA<E^/@!;)mN^UiakomS4?B0+
%BC5tK<GFAkA"'Z218gCfLl#QX(tAYK<+"&]c>GY\K3!PN"CrQ.l9JO+$!PCAI(3Q\U^gGH#FAQ"$!ahF1j+^cNnh?8?J(bZcsa!&
%+tDgrH8\9?R,OM9l4$1>D9'6G%'Td*?S]2^V=YD_^Ek=0PmoDV'P,.a$Pq3r"%KRW^e@lh5HBNNJ;\E1f%)XFc4;u)f(S1QYf3P.
%kP=q.00#'ZUnS>j71:QqL*O)XE3Cupnh<H`\FP`T%+.W.jV3'g'g-m-IoWHsI"8_7cTjGU=0]_bYDleb-#=-_.GW!n7".=1GcsSn
%6lJ5)P1NbYG4=8o?too*CO[05E<;:@@0\DSV,'4Pmuh<1f!2rIq)AR`Y+p0XdQE!j$!jYPf4kI;,6j[RWqBl!':@7U%':UsQiM&Q
%Sk>%1g"!kHVeR*k`UX=SKNG:-`P)GU2XE<.''?aoL<o;T%V^d1KUb<1-eAg*=HgSg6?3Im%5M@;+[rn;8>tr<8%:Kt*tm.`KUa&*
%3%2e,5o*HUL_r-A$KT<fSFN]][9PH$SF'kF\$5%200$c!$t(>&Lk&paRMG@<iEdj35\A%=\H30D!*<gm2D$0skZIJsJ@X:3(/3^X
%OG^>p`"_TbZ<$9FV=4m\Zk[pH7`3>-bb<B9=!"U[j7A#O32(H@L_E0e0".AfM'-pm;A8&"WB<iS-&-pdbBaZ&dHV+?"LA[_W0:7F
%!At?Y^kIAT!EWDJ37.Xsg=;hsW/_eL:g-tPlr2;V^V,88\868p0eWf-WYsKW,Hp`a3,?%)"P,*%riFa:f1&$1)E!o?=c(:TR\Wf(
%jPUtZ<Wa9E`\Pnc34E(<KC`-DOsW^"0HJ'-ZE>2s>E=Q@29RMRNC*#0(BkJYX+HcB[gU.?3]HPY:jFS$VVr(?<:&$%X^,]E(E:J-
%XFl8t@G%??Y_e;Sh<V$Y3M&l^qWdR8W_gWn9B<F"'MDsC(5Lgi;\kJjD-';`&2f,j@G2uWE8>hN,qpTS=Vu6/fW+:@bJM=C5.ADB
%<l3NWo<A:e'omV#P;:6lLt'8!&]J4J/(er,K"G'8E$S^,Y09gr/'X_4["i!4C/Lb6=+0<:oQJ`WF)ofo@Bt6QI'lgE!4)rXG>22a
%M_qb;L*Q4;,_Lg-Q,GG*@g#^`3e.Y-6ung+GPJU5"F&DQ<k(q50U5.P5JV44N/^BXR1IA<RO-Y5FbAeU6Tjlo-_P/+$.@-h^b!^H
%"RRrmnY'Dl:mBa;#T?G2W'OFdI/<hc`(Jgaj?NlT0OKA;G:Bd`68`3)hLlAHeMl>K$"fIu"'k4;&a[`q-<3>=L27qNW54t*J='EW
%O[+F>Jgpa9P*c:bO!&rY*,-_7*6%!C$SR\H/&>3_g5>Q[i3lhK=GHr?d@Er]/7&kf22sbF6/9H[U:XVgij:#WphTM3%:6l`g+^AR
%9MjSgh?H;o,De302F+J=9$>pX7s9-/eLZd"K@X*tkZ>Y)M/9MQQ=DP(]*Nb=[QMdF)RR]!;]YA?B(gSRg"Mb!_4aXZG_L)^?-@tR
%.;lXV5GJFVk^6XP@WMsGi-"&0@?Q43OLR]R\$dITQ[">;B-L8nU6B3PGk&K+]880kaMg$.eXQSLF`_:#mK:-=L"M'PN^]"!Jlm1I
%Tt(G$f(d3eTWCrnI;&E(?V%aZ0@?n@1BtoL/"lJ,L]k:Is.u_ROaROte-H8ukXijrZ`qMfVOQb@A:I";9Nap392(CdOkpG9LFW'>
%M5;Q/$G99.(%Es*)\&HA&57%DJCOYnNH6UY+gIm%g_^&]CRA]D!=2a`J.`(C9-@$R_N)?F9lj=h[X*6bU2tje/PDQ)DY3c\KNknA
%79DA99&D0;1oak!h5j_=<I5]W[G/Kpedp,nkfb4M.U'ZABJRVk@_"$W!VG#2YEM%BVJ2Y\Jbh(E?/u[85dmbj`/@>rT<*):8(pd7
%<I4-@=DTZsl/:9-De\a8^gh'QdN1#!pF7QOdCOYQ2i=OZJRE$hb;=DMnMioD0X*EF/d=n?#$3F/F9@f0iUHUVYifg=4E?RWA<*dJ
%),MOcW6kmlcX@!-q5G+%UCuNH($oAOeNN(@5R5D2&qbukIbm[>I2$Ys'?W>FZ3lR#Rikj"4C`53$-IZ3>M]_gFT[ecFQsQ3)@\2_
%DL@MH,AAMjKbl)9"3%8t:%-OJ$H$=d935L3*X/>ke1*;<-35u["Cp\Zom#q[6m#IK)8dIVjb^;\]+_O09qU;Q*X9d1J?0X?L@ZH6
%_>,ZQT?!4&p-j"RHGg0Nr/_pU[aRk>7TQ:'^`A;?jA'(4hcrI1^#4RECG17=A<7;^<WKPFaH=)::s9:@!)P$g2h^M#NRe,e\$T<-
%Es0!0_@+;I&<rkP[h=o4)RrT1@4NHWc4)(RWH7!S#_u1@)cI+s8rWV3QGp7c/1k0G&j#=tn7na8Q9eA)P=__iT[Ql%$m4"U'hp1Y
%.HALG616(Laf$$,cl0pJ;4%[L^tD?$PT'boa\`XP75.gEL#LXN8nmP'Ya]*?\BcmA[nikEOR;;dE*BGampD?%+]J20%7*g(28lt7
%Q,qOF'h/No@OmE*!iAdGf;nIV#T$nA/R5>kW\VCl*G\5.jg9Q'%R;QFG)OmJ7<mYrn.6<;-^?"+fHSQM^d@iu^e,eCAP,"m2DEX0
%Q$o!.OP#=C2-4GDhP:MjVMUqKY'#<L6Wr0FiIa^oPoQ>fZu&0qNM=]9LdHc+]4fe]68pU^GTgLfJuaH!pmef+enW+,]%gV)g8ZI,
%.a56W%6k8SLj]qD^f.r6[&a#N$Z!A;@7g)Q-_Z'QRU%m-OZd'M-*l,MfB+h@3$]1lSh#AZ0Z5b?R=Mr3!-@3o0+@5a"[H<?DWqH_
%g"o:NcF>XB.PFsl5NRaBCj<B$P^`sH.s<(="$d5h6&SshgBt0?2eb`c#5S[M,]dsVi4\58b`-dF4O9t^'JNDJL9"T;8ppJlU;r6a
%LN$L8+Pa!CAfp=mC,V;%L^.Ts%cF#j\:LKH-t/PQDtrr?auf#o85/;ClO>=+"q^rMGbX8uo3NF_Ef%fT\'i5m^m\JD:pi-/Jnnh-
%@=E*^-]KMg\`WQbV)Im'U$/6d;NbC5k(XY?kthf4eI!_KMmm4$>I>*D-D/OPU+YBhJu-ehT#qGk2)@Tm_T\=-)t7;c??6LD9FI)I
%JCOu5N.Fn/1>rO)=#Bk&Cbt6T1YuIAcPckDl7s0Vo_rU,L?]2V'RF2q[OH"4;OGU+]9i&*GZ[V#!J9L.]PFX/=g;("QJecX!O$ap
%&gD&0Os[MF0<E>nJB#S\V9,L<4YUTWN!49@i(98ec&oCB-+IE/$<Dl%I#=;;_<S7oVf2FS'0\`.I$%C68@GAbb,+3I'qCIG86=9L
%8+`Vdp^WZ;M;[[N(/eq$g.4\+/q"76`5U.QVe!GuahcM.dO8b'V?##B?SrX@@JUW<U"f[YWf.s/,-*4n[gW#9OKD\"`+jDcdM@>,
%?C;qX-K4^LnpM*/IkV7JmRsYCB#UfmNQO!@>0O@PAX6oC'hl&a"Hn36%-BR),P0]:!Q9-Gqj"S&Q6%q%do<6KUedI?#mh*tZoZqr
%cmTS@m:kihDPAnNCK;N3hg!*D#KI;N]PnAiIGI?SX![&ZW^E1Qj-'c%_Ug(e2_Y+q*6"X]DHRFUCp\a"#?h.ai#nVjPJP/l-KK>?
%&O'kD2hEXulc2\S*Q<GR(hu]=o<j5"RX!:_\_M*=Y=Aa19?R/C<JkYN<&+[]VE*J32=j\L$ugNG[(PV^Z\</uG3)0!G6T9'!71IM
%I.?9XjdDM@b:/0@G@+r4"d>Ba`o`=]26/`"B"M/uY`]ZVZSlUUZimp>2]m))$Zdln*\f)*(.TM[%rbaT/%1QF9],sei$-*&I"-%Z
%P!UGCr06.+`]5]`KZ_N;,qIs6;ALKEL3#g[9=ctGZ`lK*F@8n\:m:%U6[I?!6:q74:<U?:)MrkolFO9sf]&.ViIq<lPrrg`MC!6s
%$C91r60^]9.]g"9$YUF"(>3[UC7u@[gN!j*CC:4pl9Vr=Z4"-P1"@.AJWK)(-s+We,Sq$D[=gs&"8FDuoll88K=2&.[CX0^*ul<K
%][0qBVgSkGACR4/RR9l@?GQ7eTJt1/Vl?d6EA_^kJ]:iu?89s5>%+U9FTK0F\8?BHKbiUpZIlN(d7E;!XUtTErD76h#"EHt\1O%J
%"j>F!>VoSbL0$ho894!_X=kUdHig$Dg$<sM#Qoc/e(P?ng`/S9iLSTjFNI''i"Di':Q.(@%WJ[3lSNUIAm136YM%S2D7Grp.Sn5U
%gU74G)hQL+9*!k57`@%A]U&Ao+epem8Yj<sc&C)2U7mkqDal^Ln00De6KRaKJVDq#&f*IJaDuh9\h+`-desZW<(ST612_sD6llfG
%=2XST#=@Y"AsOrfJLQH'*ASk$D/cJRq8c*!4Z!sS>qN&e4EtrjQV^`Fr5="2kUD:8q>l35T^8QYHtqoE6pL-6n-(BN2a]Y>L!>of
%8^LT-%:"-Tet(8]>1cBYC6j/8Ru)s7>oJ!TA>o!KGE%l'>;pJWXs+eK1+=T&#!jp3_-QF&J//l-F5.?k@ALt3!SPmgR%sLEDDXMl
%<!'$UCJl^FHH2c)?'l[*^s(%hqHtW2WIQL;@?m:YF"<s&<?Gg#$FV3A(0?Q$pPC]u)l/'E.<.Y9!4KX3,)H9=STO-Tjq'J**GA!j
%,hF"!T+PX12ES0gXTbUgn\uR4,mp&6LB<cbPtL0j8t9VsB_aKX56M>3P1^0;<^[X)`IjkPYfZklfub(Q-J3Jl`b6,W(!8!<H4tGt
%mcUmrrda[&e9JXR6i+_A7'>j%2d.eOQYcLXTlmCIY)oZr1i_Wsk*2`a4*X/qcb2q[H\,=^W9+((Y\::eW\UV!0SaKK.Bj[V/&<&1
%6`W?PpU#HCb&a9RYu(cZWsrZ\R4]M].m$s;g;:AS_=UPS34`gW&B\s''7gN7_5nWdFbS,j[_#PX*X=e^8k=.apaLeh,a.gpmY]()
%%'V%5o+ZV<[_n43U+U30"dMTOEP*W]Ato]PKMufFW,@lPQW23e-3ZT7C(m-n@*b%&D>-,%ass(7,eRekP#=-DUIrb);XTmQ1b">Z
%-g0VE,?Y?;4:h:/)GW\DoF0>nF9*UW#9a+fd8m^_c!cWp0[kr9=.hnr&H6,i_'-plJ4L-I,p^_^.2<&V,sb`'"P!#W'bfi&?s5-*
%Le&)Br?+ajl9"bqeWl\(4HC^1lPK@']t:@uh[uZ&^cqVWon-O9L'OS(Nb&gn0h`P)s(_p]\&lc$UM78LL8:9NdFcft.+:aiQO25D
%Jt/E?q]cgdpQ\aHrD@C=/50L6*6iTKdg<^d=\IJ,I,tm#:C\7JdHASnU$Fp!9K*Ao>Q[AEYsZp!*%[ZqWt_OV\]tRc*;kn<8jnpa
%\0YYueg\,]`;gBC"B?i_r\r^Yk[gqo6FrH#+qKds%XtA/Wmp2\+A2bp43u*Pp40!;>XWGOm%7V.ER[6E/.!&i94W!%A6&huZp>,]
%P/rP-N.c]0Re7p4Wi;1TaQf"VV0""b/f(q<FXL6`P(/]e%GV&I=ZM7QNIXYASNJCD?6Z;VlDu+]DA[OUSo)\$J0V">M$.J@!+5o,
%EA]^up(u*R$%gQ8P?4V>*LgR$$oHPjD'V[Z[IJ:'H@!pXSZb82#a]uWklb^JhcLKYc0V5hKp3uXZ$/`2T.J#..AT@#"j/QqqOs2.
%78F@AdW9,?L[?)<-72t,Jt2c$FQ6mZd@<B8?Aa>79+NZ':Eo%k,Yk0j<P5c?8M;^(XK]d@[?)D76MH)/PX<)%nu*e5%d(_V2L>&o
%Ni%_Ac+QTN"?O11#1V1P^OhA6@WHLQ*#uI_/.o',gCE"po]4M\Of[mB+M;)+:FIdqZoOD]R!1a$VJ/Y43:i;".OBuq'mYEa6;l\e
%=[p<*q'J'Uh.[)BBQck31c6CBWT*32*l1d/NlHXoT-Xb787KkLM5qg^Q39D"b;;.PWdq-[p;0LMPa7DI3lhuSpRa!Z!s0%%:^/rg
%paJe/Nom;*@/$jj@+UMj%]45VYuT1<$Ri'Eb>GHD!%e@`"^`:LnW4;.WbG^0Ka"Q"q1BF;%?8_2B43G"jP:U+G\#+VW',ZmHfh0N
%^:h6l9hLD((*Kr!$Bl+)p]=>hZ@XpT#b-rq!!Xo`KdRD4]H/sSJ70T?Gucs.B!>WJ.m]=s7P2/IR>f]PLf!M@?5"LS-Nf!]=p^&G
%$`_6V-)clLd`,K=_L5BEZ)+cmO#'HI4g%@=ZBDn@CqP=jWAH>k-&aXrZ7:36'1"d;k1L>'8WFn@7nFH(1HBMbP7:@n,/JgU(Q*`J
%%]/AepTZ#^6Jcf2s7M[W9GZ;jRD;HIQ=#=siKF)9h:6'+6E`nGCYP1$FouMG*9MVX-aj@\W?5sl\aL7UAtn`S;a"V(o%\md0+U2]
%p-uN)BONB-V'hK)Z0,[m1SL>09H!:U6UN-j%#3!./SflQ`U$hTU<&Y\!EnKNeo%noI:7<75XVE?"i\@2`RgBK9b&_P4?+VB&bF*M
%X#=71`Q$WKh&LfdM2NGbCj.M1k<FuuY];60.j$onjcBh8iCn42AF5O#060X?Q&kpk+^#d+SqOk;BH^qbS6H"8`a-DR@>YeF)uY+M
%jC+a;.c^#;W4(J7a+nO'?9Aj,NXfeqT1cUm+_FMn:+o9q8Nu^TY\-CVG%rbJAWT%_f!Ps/FMQJ!9h)8ujS3+4(J.(fQ:.tuPKHWi
%[oRNXQgl%,[0KV)J..p+`A2"*juAG.7eV"W-gq'EE/8LOo+OkG^i9n4R3.j,74@?-AJMUTkU/8b4A1j^7-%Oh]A1gi4\4-q7%)-8
%84+BEM?8qb\Ljr?g<7S]"rB7@p!/,W!7i#??kC*%HJob"/KN,i3Wc0JKI8`=["0%L!.q#[KGFn2$uVA>dB)m?0'<&t`"GfcE"JVc
%'XVXB0ikBKM2DQgG7MEUFbVCYM\oKH7?Y3![MFdekK+T0:Q]cF\+)uK6F-J;#i7.g77cn2UY^#nL_Yo(dGR;L5`DKQdP/W;[%E\R
%'[I3b`qsI7Odf9$eq+=^5'3MT#">mABK)BY!^esE7A?]C=0Rj5:c^`8^)B?qPES&tZ"%b[P<Q?kWAZhK'[k^D'][RJ8hKq6aj/Q!
%pqEDqY=o+8/t!?B74E30pa5f+=SB.:gGWm&V9TXe)J:AY/M1B:N:+Q,-$oc3Kp(J_b5qF\YtV6*af/)Z$`YR5&dcAX<AZ,[Pq5IN
%Ss*A6YZ.h.H<o(Z8F!7efW%EfiJ30RINqG1<UL!2SCJF<q5aZHH8q;Zp5bDXeX=$%/RqYTj,=r]9NEYQ<]bZ,%-hT@UWf,l[Cd#h
%?bodVm>mjel2t:/U9fLb3LCRRDF-2(a2lJJS.1ps8Jp%%g6^]s[R7R8\)kXi&`T)s'pF&[h!K1c&24l0qi6^/d/pI_Q,HPVQ:7O2
%>jCGf.tP,naW[Ak^sS#0kiE="U[F9rb>?Yp\*]&m*FdP,,T[Fd,c$PrWo0)dap`Rd%"<NPQ4?J&OKaFJR%-fC!ZIhN/$b\4e6TY]
%Aa:(9'6B&90iDhINmXcKesi&+.lt:.73rdc,o"W(]GO-n`"/fJ<[9iV'@kL7J@d6-n[bfr=c6@AZIZkNb*O@lbGkOh%:Gh_D?V_p
%D\D't<eFtc$7;%8j\6j._l^cpW>Jr2WXZKPKQ]liMGV[_[4R2,528BHESVem0Z"1X"qk)uHDUi\")k3j$03s2)hLLnTQ?R?kX0'4
%^dQ8,O02LcH-&N/[Tn<t6aeW1m8AZTWBr9uN!84.1YZnd'M$(d*:GTJXUR=462nV5SR@/<JuEdZI5Vj1bDC29o<p\S%W8"9=>WO(
%9]i25AqP;3[,AF?c53<"5u:lF.5XQ5+pB])Q/";)YbV?P[&*h/d5@5a@A'N+f(6<*lsqbW?9uuJjF/*"<TS,+Cgfbf[\?t1DW4@c
%]JA8<60OU814GTgF2QEK2=Iu2Q^2nC3ONnfAVjqI6Y[NmWKThq;+F=meY'&\0JTq%2C-5M$Ac>lWkoK;QE"c',k%),gVH=T^"(oF
%U+Bon#t\BjRYRW]?Q+6r1U^<p2%h/IlD%'2%IXjq$u7_u&heJ,$mi2!LqGEf0icXI,YKsKj@'KIA>GIVCsAH$7O@9"KcYg+P@"/u
%1hR"D;)H9ho"]N)`!C_dRn5B3ciV2h2;Zb!9=;9IUdE#9#^;kX=2V*S4Cn_<8TNh/FB=H.?*$&BB`[E9)4]V=2Xj>UjMP;EjCK[S
%EkIpiE9e>&BTcap]/fq!RAe&dX89tXoo'L?0FQkU(<EFL5O]t!@g"G2ZG#?8Bi]qZNX)qtn-FQg]&f=B?43Fb6KEltp>;&8$,*Zc
%I&gN]"jGQ^L!fJXZJ<<h"P:JO$,*d&fj<d_2iJVa<05W36u^H*]pZ<@oXTn6@u1)l!O<>CmKU;UP+"q`>S8g[S;q'&o+pJ\11uf>
%p7'$^k;flV[8;%]3_bIn1du@d2;:3iBhnh0.T:k+8gi&ab1('-AXWC\NN>Of!b7e:HUq5lU8jafN9<$%142P@-CTrgj(l*@@6nEl
%1L_`E=a^=FUW%RAf)eBACei*(H8QE<e>p^b)dL+B4Df)F:W]2'7Somr%W0*moLIIHdCNa4EcW]hSJnV@+&(t3i0fRjS#/+Wk@e+u
%?J(?K2U^cBccXApP_U-=U^F9*<d(O:R@o6CM+Irj`#%*fEp[g8W3&+h'DqJrMj@g/b_8`7M$c<3q4LX)]G8^U9kkrI'(hJ'IV`;O
%M`<hR`STH,LTe"aHg]0b^ZNLs]\n+((1Zb%X+hKE#T$q?MR@S7@KoF^W(KfF9KNPN>7s([7&A&j-.b&]o0RF#-bpd?"&lB=&BUb$
%DJDq1Qc!<p;Z&GSQ3fejF\?CQ:][0eK[j:K__g8OBH[atQ\CGaE@F<PAS;+$S0>fa4]WjZ!(,HI"p9uc?GYb095bMul,t1dr!rh.
%2F0S_N=-T[Bmq8B(]'j;?F>=#eHV*AVZ]q24__!5:99'=N.Hm5h)qO`Pg.<p/`mq,gDRiHHs!5Rb[^m:X"nF$jmk[K<is6g#MCBR
%?&+QJJR*';(@B&]i+IYLGm7[#M@HAfERa)$==+It-A")UHbU?S=<-fu2*0l$[1\F#'K"P1g9#t7q:iJkhEaa/#rom?jIhhgDF_!W
%3\Y\d;SQC@Aq_+_Yb!Cci0qlO%n/RnYu]1a(1mJ`\ZLOGN'>"N_3Y^1Y10:en'nH_RG/8t(72,8ptRD?mLJ_K#c7opbse0VF)Iu=
%-%nd4pT;Q+f1\Ge-=Z(?/UOVX;)pQK;^ptKfc8m$3p(Z3Hg+7$giCrA]6Zr/.,e^d].&,P2)Rl911`N7YOm*?IqfMW@M8DD7"q5T
%'9O!@IlnBSP[[d5KWPhsB^kq,F5stLd[ItI3m`o9lX"F3)^o,HSrUldMcGuR8kT_JEm&plp#5O<M^O<]C0"MGqMFk&K1\1<N8d/Z
%e506.d_tsCl.<4s0/W873+bEa#tX:A3CO=.D["l28E>/A,c_98M+XNW7$o_EPi*i&>CaB9&7-2j-i,V2"riR)'-`6Q]l&$/j/^/L
%RoH(ZJqJ*28o6f*cs]*bB>/nS\+FbKj?Qb]b&L'Fo46V1dV^uUG>,+><0=s-lU\8.[g!J*R6LgU5>*9rAgRK\d.*cNoKXbEgc/+D
%$l,G/Pq\90-)_;TYfn4$_&,QfTU5gb!U=Yhp/`A:%"!0fU%N6K1M2GiOhSq?pYG4f@<a\E;FakN\%sd[h;R;J6kI"rQocekakX/N
%)sUY21Nb#8-#%]h-X)cDff5sOC!L2IpE-o)/PIgo-"KYL/8e==j&VWgWq[Ja_1D\)BeF2*e/l:7b_m0!KFHoQAe:Q"&riS;o->:`
%g^!c*GW6*/b7aQ.(`UZ"VLt5MU,.*pF+RT+['bQrq;a'u[#F"c<&A!HKCY<iS.a/#@a);BLuo47.7,saD(.DRjQ4:s9=,fX`o[X7
%lcZj+W>a8kHSQ@i^9C]O?Y_0c?n9(]];AoODHD:l.cYl7Na#lITBX$"I/`p3:D[(S?ekVZir&fG=4a)#?N'6Qm'EaN0/(&fElZ5\
%a(P/@(O%fcjql>sT3Rc(:f[h!iMfU*5/2JgItpYqqQ$OV$K[>@r7R^6hVR(LcUa["j?sc!Sg_6k64.@bpr8.<NcZI-SN]*hqW1ND
%X/X>9n^Zas`T-QrX0'j"EHh6>?WrZe?1e`_ceRu<G4UAM@)0[^r\k%p[2!\0nb,c6iB8'ohu45NnGh5AnV?kZ"5+\WlF&kIQ$iEm
%gL'ug@=LD8_afL^peQ*Yne6NJm3fQF22@_F_7Gs"Jf6$%beuA@;r'Lj9NJEg.`p:^$'/Ch18gaL0,NLBYu;eQ8t!Yul320g@A'7J
%;7D7[=5O_*q;sb"Q3r%*o6_;m]q/*G-V6q,MiS-bX1CJ:=5f:l:(3s-i03P.2!1m1M!W)k:u+IN&!kpE]Iu*Y[#\`Vio0<oH3%)I
%c`PH+L?XuX"4-\RRVV#f#3T/M-R,7f8oBbs0N/1mR9!nj=?T[J(AnT=$.p*u/BrT4%-SJ!Lkd#LcbtQ5Q2b;::n[SS1isbi_>;F`
%fjBDt,,i[AX!\t>!T##0T_GhWPgpTq7Jfi8$6^&kjD0s3^j&n:_cI8g@^?fbK1/%Jcuqih"8[<<k4DT)k&,ft4jtl,0&?u\!MLsU
%X$g";TE,s!e#uj!J.7`N``Cd#UGoXn[6,LS6gGn`0I0^-as5.PP"Sh0?c,8e3%#NZ%"C"Zc>1EuY7BE.,:RF"99<D(,fL.f=Wldc
%7_UC'iGc(0]lmCS@]bg=Z8V_T?]@\dS?4>STn6_g7]\p8/Bi'T*GB(;U(DQWerE;h+TYZa^uK"q,sGIa<k6So'guOYJ#O;]!dU>o
%H=2G+,6_?b`7Cs+/`mHG?&H/'[+QaR6a%3_L2:`bEV1eu@c<r\K%:E9,*V8#ed*Ym20#H=^_B.&RKj\T8?-QIqFD"1e[X1AGH.Y[
%7=!%%0E*]!6A)lo3!%O]Z3hr(i,jPYJJi#@T`DVCET5+NT&`aG_K$+'#s[ABM#>:;PDLYT.BJ&9)+_HT[e,^Ch]fUt%m1CFL`T<t
%p9!1KfuF^:B)pC(ZaO8?KcZ3E&3O0^j6nf1?"VI3P6)soEn)%paNlI`YHBjc$k-b[$p&@2FagiUiF,HA;D!tH1P1j5^+poS-Y?rs
%5pI\_"E3)q_,K;b&1PZI\`sqWOn=bW<IRJA#6-8ci`IMV3\dV9bQSM5UX)Dp$=b21cD&$e('Lo'(W8kd&1,=0LbAa\6j2"A)*5%e
%!8CJgmlci9_^d:=Mbb\G2()K9<VmOof//=BdLc0_-mrZhND49H\C\gG<kT)N[A(AC=V8"Ng"*jH]K*Ap,B6Z@Xql(95GpAJm?jY7
%F/`!r>r;O\G\\J'&fVh7`Aj#Rd;/*'(9l4K)$>Gr<kq-A>fC2a<PLmmpTWr2agLtO5Y^$eRF.38LJa?oSIW.+Po_5,i+k-66?SpM
%?[YdaDp_pUQ48VFHK#-gk*E8$/LJ@Y*@'CbX0S$^"dIdrWVa^<qFLLVI]^ruBGEoE[cl,:3PKPkMCbH7jo&:d<F^*!nAY<nZ6kqb
%j"'Boi<,Rog)J7k<@kYeW=$&?fkRj4&7n#Fr7W-.eb;A"O_Q/OO;iaVl\qI`+Tk53Dalh%7DDg4fbOA4#q0%.=maj1]REt<%:A%)
%.)!5EhGkX-fjkOl.a!kpZW"+1)*f9H$1R8YI@IAs"`uA^p%!r[=%Q*U\fql@h3]?8+=3L&4G!hd6A6a3?*W]mc<(q#J3N0'fd';%
%=hHm$jTijtc4"3'&<s7FfRr$\Z1OS5B+f2Z\RFTuns\8q]u*$%A8G4JOP,J1iY[*(>H532AsM"Lb)P=[UuR(IIrnUrSZS;.H*b/K
%Yf_ZWDReF.e3.YkoQ%eA;q*mJod13+\<_HhBl?oV:;n2-3K(Cf7!=P0gK!\dOE-Hi>Y>VC!er6fbO-`#LES0Bl3$,"G3hX@r0&p%
%$WrZ$8`CCWgZuX,X?d4`WoJ[W+;:AbnURO?<Y>i#:*\r33b3NS^L[kf:d(LW3fjUU4K;%0D7J@_C*Ru"g=n!_epp54@Dnt/$-830
%<YAZGh^>8D:*p3kTc9X,a0scFI&1",1#%ZDe?f\8ddDXn0H+S:-AQ&Jr%Xcl2\-U5QIN4=Rn"aD+Z>'VX]KZqC7oJ_XsYHN>=]]h
%gh7DNi[gQ]*D=>&_/jrTA<^+BEDucq#mb^Jg2,g[ZmKOa.@MubVbAf5\9Wo']Z.Z0,-N;u;!7m+1f@`N^d5d-U2"kJ%?%=Z,?rSm
%SSbm7k6fg*,u<U3F*-NsStp4:'jfFuM)qAC$to_E\9Xa*8TNe-[2ljlGNs27$IOqkXgb[R_o51-pf18,44;nO">b<tJsX#,]>4@;
%/3nNO5u&'%XXrd)9Und=FM'DSOU*E+HZ;mmD)Sl(+NQ-Ve2-81MB-A]R0$<%gK,*?.]I`JE_/3&fde%iCa<VY0efY]6QkFB1+ihM
%iQ>8J8bL"!4kg`hWKW%9_muFgZY6X98Vgq*PgHisDD;:KZN,+TQZ632#2I4PX^nOL`6?nng57Zj#Aq*/COD9V/O@N[9a';+\'kF'
%%D,KUkV\j?UfQ7'8l.u(oDOmsa6d<',<]SpVa_G)!MBd_6Xu*P;+M8/Srk;e9f0`JB"u,q5Q[ksKUcRQ4[`h@\.Z<>YRXE.,`Y"?
%>)1N3SGN1m=d[rr<!nRq5UIX6Oaos<QTh%L(\6"M1MJTBQc*@.!?"4<;JE:rA^\5hL"Sn9dWoF:k--SA??AN&&e3ZY]UEGPTk]Pe
%eH")[RYXhVl7M\<28Yh`3GE*2[P;=(G9JeO[:>U5[;`u,4el5MH!.R&(Y7FP2:6h#^I&55R>u#7m2esL"q-Sr3'**o=J/=u%qEn5
%R=sW/9psVY#>h-KAHqL"4q;<EL0;@*>NcVH\f"WGO;WR]^%h!A+=V2cCUOs$p,MoBJ1%S0Xjk8,+Bf*EorORlH/R,1A+JFI367Oh
%(F5U5Fh``2@o*@Z*86Y11fGolI%'7KC4?]r]I".?<"4pD>+/46>%pYhW6o>n44iWI+EPYod`>CE=TT8:MNYV,MAO<:g*nE"/[,AR
%L!Jf^b3bcQ05<YCQjpNI15=HM`<40.g^C`K?<1lsM^c"%;H$bVa$fY+]`:&-P+cVj]iU/m."KVL*M)+lbGZ$[drA*$'G[sc8nhNS
%*tF+T&uR\4lNc^>\a!]^L"-91Z^55\5HrW=WcSU\c`Gol\f#Aj:IAIn#B"ke`)UN=iOR/4!I4EAD3kIHLq6_;p[ojPQuu*Uim^[`
%-o7GKYsaL<?E3f\21(gFZ_)M7ftBPu2I<B7^L]RRQ)Ak.-tp?AV.i1`N9ARK_MiI#WrbF1aCJIP7jjaQd_7taV42T]V<.ht1l^+,
%]XJGg,T!2!"G7ghcp.`C>(]8*AAaL1TkH)m6:P\=4*1[[BBoW\PakKZQkZ,qR+sVSW.YZR]2)-]<UJROQtV.n8h">Vn.828+<1B^
%<EMAIL>$ADGW>G]8:.g*2Iq!`FMmnNlVt%3Q!8%J&TJC%SRd%_!9[*^lW4,=!VH#:u)kM:A+W$i^ag)ka4M]*..M26=osPF<
%gNjVIRGF6u;p.aiGdY+D1I6na)7d,"kUpJN"J0=#bnPFE5l5A*D=Q\]0$NfH&?XsG,"*\mNeWMXkX7q&pGDuFGpQ#O(`(F=*:9g"
%6&il=/ugmBRAeO.\H-_s$Lpa[3^hekhHW&01tk\E$/+BCRlh4I6(BoBY\R5MD_1u+d\l^"lI@$j\;!M#;qWblj(Q7GU2?fW=cRH4
%*"ZhR)c@F&C"7W$>NK7p1Ts"gN+46NJUp*_U'4B8XD:g(=_MO,TjVa_M;5TdnasK[LqlQnfQM^j4n60G$7e\l*D4uP*8k7hgRJd8
%(O/FT8"SEu`,UrQK@u+,9i(&ej_hPhEbmRCc`L.Z5@@G3`A/%/A6,5T^8@']&E&Z.`.7rdRZ+B)+@-#Z@F``/VP_987c:B%WF0F9
%V#YW\6'SEWPTgXCk?:,."+lcQRg)21-s*P1Ni^&&X"*]WfqUn!"ZSCW8r)'-@0[ZhbsphK4"Ha%&5HXp)2-9LQXS!UP'(ii8kk8o
%hsqR@pP`jNX(8S*8_EUtaWicIR2)O/f;nKD6,o/ad#SFIgpEd:f8WAO955jDC@bm8#rf1"7/DbK]>B1nDKh`uel$efp4;c[6"F(f
%?#e_:!MsM>X/[kH\>Ui,Lfcdn59^cIMk7/OF$/IHH&89$[\++Ej_4`YU$^/=m>aG3(h;Wc@gS<aP'%=3Gf8Ppk)2\\$0m,S$nXQ@
%-S2@D\XA"2]19^[jHfE<eaZW<8bOZ6le%0G%+CATLOiXgfIp:oCX\]uSIGgg\eRl(Od/LI7HL4GKW=;/j\j^[d7@ugkp&eeZCddl
%hX!]*aRFiB.Q$I]*,Q*p(S<\?;e_ed]<u:74!tM1?<&@V4!C%%HEe5`"fCo"UkX,))OXQu\.G6nZ<@RWJ<8m('hfjYX$`qZW%!"E
%X=>/rgC9\\B>HCs'I]LJJD*o%ZQWHlJ3$,rgn0h!>cof'l',Y*Ka]:TB!q7Nf:e0UjCN5l$c>at5snK63:(o_iJ5c;P&`1dVSGm(
%$sKrD[;^_n)FD4k]gCch(8,@VO/pWES'eSVZL5#$2T+I[anRi1TlCm%d1Hq!dI#PFi`/]+OH`1EJ9D*>)P=\=4V_(2#ZBljJ/sq@
%at"20i4d@VWb[C^1@F5D/R[',f@TgHR?*FkSDRX;]mYA$jRDMoZ_nUple<d'ZQmdh\N9[s^.VUFRbO?p+I+(TJ9[L'L((eTjTg!7
%qW.AHm,mg']?BNX7q[$Q5!+/gf(oC8q"3se5Ee=EpJa>js)icagQ7dIPG9@$705/>#]A*p]nlsS_hNu$n&+jWq:"j*NHnK[1Q=A4
%$DGMK@`(L!&QU'm!;f\uW`J\mrtpOX5MnXqrI=]W<Fm4>BJeVH^e\&1drm]VodAou^8g@\gqTS:;2Bp8Psf6pm_ISo_bF(h(g^1.
%GC6HNW3h<5fK2j@T23b)L>!>M0T$*TOj'Os'1'f%ekbKreecpbo\(j-W(hWZWV>Gpq9rH8i'hE+rn^e(-G(OVWcIiR,%nTn0!X$)
%VQhcIL/W$2POk]G/m0I?<Sj:AaH0/E4QHnd%8.:gUDMK.p"GWaf#HQdlMW"KI2Zk,h9mP\Lm&KLCf'[mQ3f2Sdn)/i0<@n9[E7GK
%DXTRXrp5Y/NO@I-CJ.&k36CG;IjAHcP&P7cg[tqi;gN<?3/?6M:;&kE8"YOiC_E24jcM[GE;,)f9>V*+Q'R(Km"MjH]f';f_f;-_
%oD:m8qD?]6gU/'Y^"p(aLEq>!?HBYs_UdMoXRE";`_Wp#9XAtm^J@@lL>)Fi_Vjp$,`\+NKG:33iFFOk?4/^:?9c]kq7SC#IHn5u
%Gp$(s%U7/#Q:\6#Xb6Dl,g>=,b'so94i9/^'BJFPLgmeUbNaaKO,Kh?p.(a`-gg#fI.n<4s2B_3pu>/aT5$NacgUUN3-eOuJrs`!
%PO#pK0!(dY`ZVDu\0,C9h%P23%,2PZ:-AYj]?(.FKJi8I$4$i9Ak:0]1$W0D^#ieY4\Q%]'P\esR?R\PX'U=W<cAIJ"+:f+r#'3t
%"Tt-tr=55d"H'>?C";3/_:sF-R'f_/N@`Cj9EjXHTn!?MA'YTD[97<?dMcfIXK3ra`1-u2Ya9t8&oo-tIo_!MWC$Z<`p*Qt_94\U
%4j50@G[5DL,.,Lc\NUHKd(BNeBb#/.^bjl4JW"\1CotARhXmYj;]@q*AH>c."R+$G?7)@FCaEc?;,&X@=;r;r;)V7F6K<U\mknD#
%r`NcQiOa=r(@NV_TU&bMb<.2_q@-X)/:rg"6Jne!r;`k+]Tpk6P2#;CKa3oGj]8FJj["m$@n(?[CWQrbK"VUPB7_a^l\'#V!n^+A
%#/9Wd`98:p^9,]2)]$Z:#/e@%3Du&$RgKNiE*0<rVF'DjO0m+(WIXmtBUil(*_t&bBhmj0/I(?KQjO#`'#7(,LtN:O,P7bs$]WjN
%",B=1_XYOhPV)AHYk;eFgW>[GN+q+CNfF+=P3_AEAk["X]mtim`ENdL+,Cof<eFpM.>t79Jc;bgb2h<sOL,/J2qgS.*<Cl#](b6"
%,@T:P)4k<V=peOQi0:WOY]]Y.8WRo.?*H3FW2LBs[jGqp)`PEp%.$o]I^AX`fnRpTm2P_F4R.Z;8Zjcc(0_H@k?R"p(fr_M0N,7j
%$r?.i`)<Aucs^(*X$URU`B(@l4f]BiRG'?->BF#C(#/F/@)K"'C%lG<WL.'XB&J<B#H8XX'mUF:E7_Ncf2eu!ZQ0D[k=plo5aIU%
%P$V(cG,LFH_H\53MtUN/a=*b4AJBie\:[T36lL`Nq:#&uX-CuF>I@e"KU-o71/5=g_t@pB7"6Xj-s$kUa/e.skf-%,TNZ>_B?:lB
%XQ+2&i9;Y7C7?+CJdLRI-"*$]E7h/L3oI,XoTe&DU>FmS?*p^odbP:R>DB_'AoTZ'm=FQf-nVUf&XM$W&++=L,!fIkJ^-/Be]&ho
%EuA0dU*eg+a^OhO(QA:?4;l9Ip+9Aq[2&.9_a?f$BL3\ne!PE).%G)lY3j<2J\DGn9"?8+E2k:rLI^?q:@A1.-k"[ff`@RS8]Mi3
%.ffE7X.V0K!2Eu^hUDp`\Ij'kHlFso1otPGW%4s$I@dSDg?3sCb>Al:p-6<MjB/LN?Uh:t4_N:YKr:6Ie%.Ch8)W/["(#gB+K,Rm
%ZM'hLf]\1oXmaJ-V*,?bXm9]Bl'g#8hDO)nO62E/;DjQ5o;\?.p-P9=*H&gBR8IJp@@7+_8ishp9f$$<qCu!&Nh,an@7qgo,!]f(
%076#HfLg\Wn,D;(NsZ?M1oWb\C(Sg><*OLP7D"GbM^I<3$!iA5[N3YA#4(bU+(oI.VskPHD?/3,VaI$O'uSV-S)g0rkE);'H!&si
%&S)*`F;\b:S&O%hQfEJ>Ub4W1)d+1nVhL?eQ>0ZFc$6^%[79f#?ak;_$pu1d.JC5bSEFcSO[K[pYe?rkJE6,fKA0\4@<:UR\Cg#H
%BaMCG192")^S;-_I'I\#<SIYqH-kq8Sl]be!n!d2_o.!#1IE"2>Wme)8mscB>$1KCEb2'YfH51&p>kI/liT)7SJ*uQZP.4hE`RtE
%l0uue0ku3dB-YP@`Ib0bpS7YJQ&9**K_4pt$R+O!=oD2@Wa6QSSkR^m,6:AfSa9JmVD:">Ze1NpIc75LFu%m/UHkC%5ah_qlRUOq
%=MuI+a2*Wl9%'Pp,&7ib)822&+a*c`at&aQ^K\*PRB@3`C2b/S.#B9AX12rTJddeUd&4#H_fd0\FC&`F]*.j"_n@`=Lk+Ri2G(:s
%LJPb#<2DjKX+78ETPh+4+qdC3]'BVm`]!I@Dg>C#\E#q[f^Z[\_XtKm$tC8aQbd!T<40rCZah+0oJ'j68'k%9_17kQ5V'88H%s)V
%dJ.6H)`A/&G`(Xh)tp?an.".Z&I)S__+6T<pLQs(idiPKW#_encnV[04;?CaN<6\AD>3)\/Pn%ZAkTsIn?p@<]!$_".[OXa3n=Pk
%e2lumQF+?>SUQmga!(+[0-F?tE,i@Xg$5VHdlb!VHlI<ZghFQ.kE%q+"O7u]F5`69>QENP!Uh#1ni3`_Tr@?o#6FT8ZmXSD21SW7
%%=%>Odb!qW""KuX6O.P9H'D=QRqDht@QHIg7HNT0oB%<P\^BO=V+muKnYSu>`g9qSF_Mc,?K\,u[eo3d(\lc%M-65.?CSb+1=l!0
%26%m2C&'5\LT#@ZTt[VrZC_e?_qOI>/!c7:;mRoFG#M4:`Ci7GdkWbH9k)UX/Q<gDR5nRriCh/P`SE+jS<ssLp"Frj/oIKqA'o'G
%%pZ;U)e5]^RHK1A\aS\BfJW95,T/DH9MmjB^rII=IqFandTVQ2%`p\+MnNA*4Y>o]=$^@"0<3)#-^@-gb+nTndjV(M;$<Lh(^6-!
%;bjMqHMVO0H=_ED!lr=2a9]!(i=),\?o&L0c/B-d[@:offq'/l`L56U`1)l`4^6C`NPF/`M%/gl4".?-&%bluG+DR<LfLKoU'p#a
%Zlq9ZSVEE:FCXhD%eelo:Uq;"'oT1)&kN0NG\SoN2sXaQ]$WJn3u3[@qL*hnf<H+sgl07`j^MG_dC?Fe+_G:+c<n6c8V"GkNO*Uf
%#hX&QC48!Y(s$h_HFVT]1n/JC[B[)^YSdhN;:#>]2IcbhX`(N`B?P@YQH<a/)ai0(D8tt3/>1[TZZ8rR`6r=#=X\.n^9h,4g4lLC
%Jf@4DJQbBMfU3hAWS.J0ZZ-TFD=,ElCg8O44tYU#\q-=<EMAIL>%bAa<4Q=V%>i*js=e!sO=IR>gL%D(NORe&]f;uugV9l0s&6u.T
%l<r4uXSZWd3@+rSF#]R4!JX1ENEWZ#1tF&]X]B7ZJPrn+K@.18PgVJA[K+!T#oL79%T.ZTo-fdm66i_fN/dNQ^$+02@l/-,LG`(t
%UPDjM+N:KhePN>c#Ff^N1Sf2`EMf'\_0?##CrR^VCrVm*cieDG_<Ut0j024Hj(]+:-B=6pG@b[R[cK8]S:H7:30RGH\EfIh@i;<&
%At0aL6njj>;+>EA?o)C'-CF8G$s\aV^qdZ4kr1@^9NEh]CDt*"*Z)G;&AI@C4`!UJU1C,&2P]dJN^i&h0?n0H/C[eeAi)+SC3?jb
%LIS(K4%m2i=N!-`1B`bcdqgmJkuoo-S<\sJ?pA:ig.pbGC7NOe"/gB\lS>kWA%Q.;"GUeO(AU?eb$_Q)[V`$n9X`<i[]-58"n/lo
%-"'$B1hdhl#6ZErg<nTILJJ!\aTN"##Y%O7ej(f1P%1]U<t,\:*#6n^q^1r*P"]T?qXfT&8_8-1/"doT:$<\(6)ci\VOide)MYT>
%\+eKf-epI8bhK,G'Z<k:E!.skEjTVqJZSq&=;]nA7T8$l8Za#$K&[!:4sl"7Qt&3>ZIOWuT_lX)Z-gZgA:",D3['*O*+QlC)=_=o
%'QCJ9WO<C>qF1JjCP6]j"#n[t1g.A4'1Zm0:#/r2?/GuZo-R"_jt`)3.?,uR,hLJ5NH[b:=:t#q9d9hBUOBJ$Z[Ho1Rqa`=D;(Wa
%oo&LZX=s865T&jWIb8l!"^+E`$uEVhdX>^dFaCLKA[9i48d:N8r906XbRN4>c+kd*nY@DX9SR8CLdhJZ1+kr1rR/@q;DtcT3GQ\q
%!scRpl_WS!ltgMI`G2D-e0,<EBS,&:?Zhu0bDN\Y=)6K&i_0d@3mhe:WHQe9Un^,)G>YH&Th5-(;Pc<\@k_mLNV_col5mr^au:!s
%"AX$Ej248c0<BHACKB;eohifr3%q/_f101kl7B>I\f-6N[!$dokrP7mLZR0*S0;FD_dqfG]kE^69FHgqkIS]G)eXqq7'++qj'J]Q
%,Y&ehk6eAeNRJ]Y?b';QF7*+IB"a8&:,_+Ah;OKO"+/%QnoR!^13j&c,>:S%S2fuC7V[rr@iqCUZuHT6CrP^b<u(UUiXRkA-,/.T
%q3f_KZ[?/\gJO!P*8)QFUbC)+k@hiYF&O7A04QJ`dB<qOn]-6EB$HpD.M,,=jf]Sk:1SCQg'MtUmLJC',.S%#\<0*XY<g/WD$sfm
%&gAThM`7>VeCgZP.`[tZ%%j75eRA'I'OIcam^lKb($Y'Wb<D@Bq76n&TYt/,7ft+,6mg'".2*mOJqLVG,-r0&BnHEk#k;e&Y7B#g
%^cnU2n'Jo0`l]QnAS8GID@&ge7&oeI]'`KqNNRV7D42Id%;AJ!\UrMq[YP0->4D^O[g.&f'<pjKFCPt>hJ(.VlNQdl5:(@+`\Qsm
%l9/Y8TMYssgcL;V"/5JCq&G*/$BND1RjV:5B[!#qCQI=!d-_lUp%RL7Yo*/sE4C"+5)1'5jQl^R9o(NJnN'Tk6Z>'lNRp%KVe/?<
%42?<Z`l^<4GDEQ:c5Af*cX9,1$X)\o3585EoME$=QqE?=bbIN?C!kSmk'GRoSL[UYS&(TD"MhUb>Q&b"5,6(9/CXL4[+.=!HAsoo
%?ru(OF?H!PW`9_$Mocou$omhUlpA372V"7L?/BRsl4hSjem]]f0i&F*+iW'#$5[)%_*DYe)!4k6?n9&<4llV$`KnM?Cs^bp8]!+0
%b3phh`M>Do=OY?$f(u(UAEcRglSmL)gkgKgbb->&HC9bF=>]P+LHBXDF/_&V7TGg^D_R;Q`\,.qD_h"hL.OJ^n2?U@q.Jhm06N)o
%nId:jf.jG+@SE][at#qi^K\F+[$jM.[gXcr-tHGBrH2KV52'1t-:?cVe?jg"802ggZ:Am%T)gT'6u"P#\'/<WVRu4q\>^5_Hs=<Z
%I5m;#*R=9IQ)"qg-;W`/@s<;7ALfop;fa`99j9ba]'=Hp8``Wd(f*8f^9eR"%`^b$_^RWt_i[%>G!6^`GSs<J_R'>6a*pl0Y=V23
%jto%i';[!oT1+pW7V@jZ=S13MLt^WR$)iqWs%$BEjrcY+<X!m1ZOg/N5<.%R3skAn'1u:I`jsH<XEq,iVc+dTn3$4be%QmJdO:O7
%l>B3aoCs0bre+!L[Z3;!iE3,M#>\?MUNoq([JD(9OgWEAe58Zsh0?"3pRF9.4PZT74=Xl=7f;LnIBh(?>?'5o)Nr<9G\lpVmc"L5
%hSM!nWC,H14(IFj%jSX2;]L?mbL*g=6i4NUQtC'ANsm^aedYr&3FKGm.m*p@O)\p0$n(8P:%VDR_,tdG6sX)NU*\O24VCREgTjei
%+?G$uFR0t9V1f/QrarkmW,CSX<hY@-jsp^2kk!)]2.,QrHPsD3^;DHGqCt`\j]JqoE^qXUhsI`W8S*+T7q/!6f1?ZmZmV<;eGp*8
%2W9(!C4uCB)D/sP2g!.um,L&ap"q_RdNEN2**2LtUe9^cPc9N+#$irGca?'0hG2!,B@P%.'/b<dFUloX*&TG&\M\Iu.)$aFgAjus
%4Q(Xg0_)H1q:M^ocE0'?qEq2OPG5O&Qp.)_[RX7pc"O^L(eO1@![m0g=E[[/l)>HC>)"N=iCKoM2,C?4bsF22bR#88D8Q/Ne4ORf
%1"K_P\$<M]OJCpf(oK`;!nf[ja3L+9<2<De[S:ba3.]Dfm!Nc5^2Ll_k<pKS,Y+VCdDSSFhuW"7SQ8X2,TlT"qg259`JKR":2D2)
%iZFRQ`E,]qo%g>=Wa4LK3pHJVdL"O7OA<="Kll&A/Yln.[ua@p^)=G)IG6pO>'9SBDcX!RlWZn->i.k/iVF><C"KhUNa:O/3d,oZ
%@Z28>`mjZ5%"'MKRk:ELY&>JSG'sb_K^2i5a0'>HpUe-)>=^,Hha0B_qA.Lnf=1S7@`*0OqA,;YHggeHdL"O7OA<="Kll&A/Yj&_
%D]tZjI1,;HlWZn->i.k/iVF><C"Kii@<Hu+l%JA,8>fbN69=*%(=EN@[h)?I^4gguqA/(qlW]1C?/It2iVF>L$.lYj-VU3r(ZGbQ
%[h)?F^4ht/`E_:e4Q6,PiX+5G&5TpPKm:K-/]8=*D]t]oI1,RulW]/m>i.k1mJ8_*XKHJ>/CX;%]'Bo']"WlC_Y#M!A(S1$:V4>p
%Gf',*`"T:l@F#U]Y&>JTE.*oi?aU+PdIf*0e#mU4SoB)cnV$44Lo4P969O6'(;^C0[h)<H^<I1hdm8d;>i0`'cs5Hp4Q6,PiX+5G
%&5TpPKll\X(=EN@[ua@s^4g[GodusQY&=O4?/NYWS*O9L-X>Oq3plbZdL"O7OA<="RD<daoo6g/])%(\`c)*p/\GSqb\m/hq@f9S
%7p6c2pKAHN:2+9MC=p=^V&jD`"?`NG&c>0MK*f9Dar%Y?ar%kEb05R<<b?fOC1WFjVYZL]+\g?>2L,F$C1W.6*(rHCb3T>2Bk><3
%Cg*6Pgb%,g4K4T#q!&.)o\4(k+J54OarKHL8I%ah)$,f*1'4&qap<HlPmQ].<^sl'=rG/;Zn`eqd)q#`=t9]#GH[:J!o@^($p6c>
%[Zam_@pBPmF@%JkdL!#aWdI,`'@I2h%!lBdQ.^<7KHFo>`mJI'RGH4IXircM9e/$FLo-FlE7W1u-C&:V69MB83I4r<1<4WFlVUAM
%2G5F)1.MeL<bCb.<OC!+2Lq^41)Q+d<X(q#f?2b=E3GOp-Uht,Yu^"#C8t<H2[>i3i<ZW1NB#M3N4!=[&QJR..td!O[5-ddSA8',
%RB_[*;NJ4@]5\djH`WJMVaDFlNK[P6UH,1)(V;>j^e\ZU%%ag]%LN,&dGX-Oi.Pi3Uan_h@7U;&imF!g5=1Uf1MUS#`'i`8-8IN"
%O3O<pX!L=6DUj&E:/7+),YZo0;(U]t(#7:pU'Rr[V.TU:C)2>PG,Ep[/U4nO1Ar-?V)1iW,qr3#(I0A)q-is_nY'Z0MHrKK>$^E)
%'J>gcqSb=-'q0;X!XJ,:Lo9#98;bC;#>/@__k`i;[q*<L4!UE-gb?oJ63GiE(C/a&1R;GV@(To!Bq/RpkCdSj2S<a:>%SQO]9+pS
%]11n@N,Qd-cYAVNjL9-+BMb<XC$hRa.6U0r;pH(,g^'5.<+;8$_k`eO(@r\TT%ZH0eQkF=c4jk1k(!o`R:9\8JhONW-+)'6qNblQ
%TafKae@g)+YgU>GFaA(.4ftPeK5RP)HRO21FVrYr5brHq2q(&Wl"3Dtl"6q-EGV<(L9$'/91kmD2cH,^#C+_BW2DU@e$i9al1'5c
%=eT!1a+oTD":Ne':3p.N39XtmEM-u^RrYACD_:5hD(\fa8ddU,B8Tt)V/`[*!)*e-0_EOB3DPZ=Bc0oUg%#8)m>Ug<A8Q<b\B$F.
%Q2quVe=)F><[S&"f!I6mThsK?$r/Q=7=81^LlXmQ#01ri)jE-'hNTfoW/ZGoAr:.;a#K#=#pmL.5UPeNIF8X11SoGo6H_ap206!t
%=uO>nd<'g;]:P+caF]+$XiI.FBR6(UHstlS<D,uED.&jWJ\'P1iKT3p];CpoVUF8mJYZSY):nF8PrT``2SI<?Nd@+RLSgDP\Y<W&
%RpJQ?E9TCBL.3l#+AO?6b>].1"Z06&\1N\d)+ebX/A$\V2f$g#\F^G(R2p:;\m[n(C)#iTD+i<d@Un\mrZis5lXd@^Tm=CoR8ufs
%+*K9c:<+]?[)'n2LgFf<:RA-jB:!=&Kqb4'<gBQ;:J@a':*\E[>i]Mi@=KZ$e3+UGGgl-qh5ZY5q`Wc8P5+p2DRCRW!u&cV:/Lun
%hAKBfmRL,am\Y<amho3M@Y_.aP!k4\*Dlq#WeTR/4lpFdq)gI9<g*4P,EC!pG5)AF?*9e*3/_Y.K.a(31`f3dHG1)AiLhGoD1E]W
%<sNQm&2\d->UAQuT,:Xt@2Iii\QXA2TSGFnS3.C\RWPlKDfiOK%hIVYFtsS7e"/:UI\LcQXW(oVBakjM'+LeX0`$X1Z@fL8oRJm(
%Bt[Sp5C-'$Cn'eX2=9.l0R-=(@IrlT,^T'.^XLCU>LCjR=`*EL:=I?QlM%rlJR4mria!"o9qU4c#$iFAGupD/'R6NHNPuC`JP%f1
%FT_sHMfmJCE4+XHMH)9M^Lpm?K&'73[93<_H;t\]IS[Yg2!K,M2^KX=7mrb*S:nL"eq_U_rJ>gMG,me!.as>t)Ki*0S+DGd3s)>%
%g$rJtX0lH/o,>GiZp4ui<mIbFoEE[pS_>?4Mmp>d[JXq0(<q[tPu5Q_fNk@3D<5YB.@Q`NP$4=#]_A2EF9cHFklG]XYq#IerN\,(
%lI"`DH"r!!k1sV&a1,Kg&iq_kP#d10l$547MWSs:T-%W([s*))UO-sKp>4QA[FKr``V2l/Z':s"n^n'qN)%h(3-tgQ]K8jbm.P(;
%Ti?D]n4V<E#mB'ug9$9I<4]iN.p[KWX^,5-Hhp`R%i>Zdk$)a:bRc[ErOrXo@r1K_>j8Y6P/9%_NSh(GmR4]tTBS>j6jDs^1foEO
%5"4KO*+tk1SD,<YXE0hCG+@#jMRJr)Q(nF>=m%h0=Qb"0K6==u@C1QEokiisBRq)^*Y=;6J%(Iq;r2C:C"Bq"P0b&tnTD\K@)p`4
%Sb($fG7@-f?p$An"D8Td^mdI:9-"-ghk[nV8FreeDdB)9MNM'6F6t6B\Ht)JA[d_ZphZR$q`M#^P@E8#Rb6SFMMKRbU@A9:FCJ_b
%A-=MCS^@^P7sc)T+C3SY=@!.1Z@WM[dO5?b4E\@h,dK'Eh(1DK_gDnZo%nY18B-F1FR/2i2Hr8oVD$``_%_'F)+5pQa,MIT]D]D6
%8>=m^^[j:/"1k5[5mD;6K+*!3l:\Q9dU3>`g>Og?6'eq7L2<BTGqk766B(MMS5t(.VW15oj=d$lUO=c_Vcq!+62h!n9t`_i7.[Y-
%<R.V:gUAi6G1cN&cjW?aY79F>`<4sU8>W<(a=XOHIdnHfF;sYcnXSf;)eP/ZD!n0t3&8%.;c<c1O79=>k[h6@]%=doAi^dQF5kg#
%)Q3Fm@(mN=[%+q*cR.F.GUR\OM-bg[f\3.d,<UcJI4cY/r6_;[4(b'\?1&bnF!Em;Z1GLh)<X?=Q8N\Q"qeY)7^WpUZE7n-_Fkt0
%d/nf2qBE3;V:d]g->IT\;O]=T66;,(q)V"+(U)7Tp\e([PrV``dpL[KP,kll+W<Q"O.!n5*A;;(oDu1'goqq&S*k-hkR`-O[s8O%
%!UTQh!M#D^%3IG=9@dR-HQ2#OJhp!*<1WeLn<-j'FpM+B.1]gQGbO&3p^jIno2-Z(bXZS]pc>H67aX"R"n3RR^Jq7O&']%Fgc6DC
%*SJUU\srmja5aKomuPJoY1>OAS2pp')VFokQc.p`;lmqci75B/e!"CK4N?Vo@(97+6j3YGG*af:SNGhCV_$e=DK_t,ge>uLYfRW[
%_b6SI=>2FlNC++N(s.LR2L*<;6-3$?i<i%+\q0ggKLskd^,@]uGD1"=o6'FAa+_@09hPkp#^F,*EP8Wn/MY$@c@04C+.UJe%n)I;
%pTm-6%YeWg<=@k<RE%':m!0Xp'giGsH89E@S#9$=l]>bZ].ie^]R)hF9_0u6YP]VmNNqh".Jh)jm$a,sJcT^1h4#Dg1NZTS3H.sO
%ThB/]#,hGBn4oS7>alSERkE8M]4P]u>4ZXKLn1;8R)/RVQfI>`;HiLcVhQnlb>;)jEA]"_Ok?cU\[bgL[Ukb(^_iBt#gHEr^Rpt^
%15$\ZrSlqVn^MBC4m0)oX`K7pfBAm`e4f"iIP`q(P9Ej<%KtUR'cSbCb\StTk?_mZp+/kd9m>qXVB#a'>2?-1&TIA69`B8i\$=_,
%g55WKVXm'adMpj-l,bg1?m6S$ej,fuNLc.[Y'>8`^uf0O%3Lei/LGRuamP7-F+;]:@65<"0B>0_%e-:Q9RA3RS4V<fi.L3,4Xu)-
%42*:(aug6l&R<BK2:[^`fD,LdW'h6<26noK"6ZQZ#fh?l>HPnmGM0p3==.*RB-IEnB]%g,<@jIP!oeg&lpH;oK0LlaSf?e+BQ=@!
%7MURl)rT/-f?g^b;`_)Ym2`CR5+UEapK*@C9sB\659A83,E\P+G:djeUr83f*pF4W"+Y]5S$-=Vq/u\Jb4mq\4CUj.9H$l/*ZbEh
%q!kVkGD<]mVd>]^%?B"N]8FS,0)U$s$Qn'P"iX5Z;.Q\b4,j2&l:A*!.IJHc:O:'agpX`3Km*:--ZLr`NG_Yngd$Y@$i`?@3tfLU
%qmN<rfDKGH)F(A%an0@3D&<13R(DnYD4#fiomVSkR1r`CL28)l:MhpkHF@uL].(WUp,eXh8u$SKrnP<A]:gfZc/)md(-hhfT3F84
%;shjJrRYg<H]#TH9E-p=I\"Mspg2NScl;V<1ZK5*q;:sMp2f!:?Z*@;e&gm!4fu2='Z)nk_U_J2mBr$*?<=6?qdd$jZ>#qp<K%l6
%:2Z]h3%)1pf$1tlkFtcDPKg,tf.u[!Cn6dMS+3W3XSo`\>N[8Tq6;)aDcl7,pNh3*%[GQRI;,<i->]<l301-[U/a(3o10e.qfNpk
%JY-a*9YXn\frI7V`d))P)h.0]d;JX;bkU%=X1sKop+:Kh?GGf?^!3;`eo;CaFt9(N[FR$&O?gs/FGasD_.29tg;j"G*M3t"X0Ept
%ep"N#F[P`IWOdZ(oIg)%T<?XA@4l81f6GI+)@NNJO`@"3EpIj<9&)RqO1GgW4$"t&hl_%PX^cGEg`)juec&Y`5>PPi6pdQhFF'Yt
%VQS,&]3:2KVt.!d]^NH#>A!bP`dNa_p\i9\ruG_?GEE&)p<T_#X8-Gi`lmlt)bu):dUm;[Mu0TqhEB-^I\)TrD.iiI&j?hsDFcsS
%$MpjTG7AVCXAO=:gQ=u4@6aKd9/&<u\-M\d_q_`p`nb.:nM%m%Sb@IY>#FV<S@kJt3(`:rlFuS?fIRt7Z'f92QcEHerHu"V%jh%"
%f6uO3kDqFe>KjVG?V^Rg0RLQm/:4[P:5rN'7jBt\[_MX[hu1I]hL4k\rr(#]H/I<2=,6f-Qp87\nFlS@nCDq.q"XC4rVa^kc-L:Q
%5.eE$`Uh8#(XLr%r1cB?&+=sdF$DfF<r^cY4h]2irUH(9qpX$K]DM,d[8Mi&imql5e6Rt'k1rfccE9>l]>(uUP<:plZp)484MQ,g
%IWfjZoD('-/t`'b`E-7*5KWs(jR:[;>AkU4qet5Em:Yj25J)43poE`,DQa%Nq>@j3hVNX-GBn8E(%L45iX`N<gl1.gjR7Qn']1M?
%mc1A2g].<=Zi@Tr^]2F5^%L6GgWqJ^&+?)Sqr7+mg!/45(O(XAh*&<YHi';H4Rh)qB3/0$TAQ2&rSmBQ+8tjj%dtIUiT^;&LL:#=
%D#=,)dC0VsUl=gKq.kJ!i_c$3o;f4pDu;o>%mIOPIe.*!Q=ZT]7=EQ:Nh=k1q#'\Ig&D$Iq%ps#/AiO7T5X?!=#\luq-s3fUG'oq
%^-"M;4?gNlr+qk\p,'rPrL9Edh;4].*?O*F>he:"($C?T#(H.B`VHX;MEboUi)W<qCZmi@Q+#p9:S_nh.DP>.I&c]l=nc2KfXAR+
%^U^m8]=rN1M!lDO^KL<=G7W?J4>qOO?f1d";lsJ-p,1i^gM[SDou]j3rmH[PhgKlmhIqP%k);1`QPY.Y+!1?l4!4qp^[:^AEeNIK
%c:A!8g'rPJ@R_#d[Eg1<M#>RZre"#7]?[1TWBoEZq>MN:/VfeK]DX7,GMG,@DZ@,A&-%&(05&VS.DU.??,8&jpZgYk]3O4Zn^[pk
%+!UU;^S7(HI@kF5edp?pqpW_0?qoE"Uo>j&<NXi;heh-6j86$_0"-,D]Dlnmqo9Y''"RM6&$E(,?XMZ[h-V0$rSm,-p5drWq<foF
%*EIiqGMbKKXf=P_m\AP&Vp_>)^V&8Y-5<\T*1&Sh5!o$]*Y8+k5G%l-j861S0>HIY&'phQSr^(d]m92Ho);)(Dh?s,h2fnMmU,V3
%Q`99LSR4V2fJA8S^GS32kN&mlc7Y`,]ugrC*knlXII#AG_l#6":LfF]ch;S:r`pN6P),C7IEpT^rUsBDZcUVmJ-WM9b4^$RgT,dO
%H%1N,"Oul)=*K_omr#o$TT1s`D6.[E5ES=ta`aQ\j1JY;hVEXg9rGm5mpA8*H?Jor5=,j0pD;T6r8cl3>7o=[^8]$,*U!+ST+5dZ
%>tC*Y?4"%qDEbhp'k2psp$D)Zf+<=rmCW*G\,O<Zne\s)a:CJF\AD\OleCIghS4;]EV&8EYAWJ=@E5mgGfC-=]BhI^!=/:_EM]`r
%JA',=a0]-6CChS&n<l']i*'Ak&eYDcO]H/?`F&Z7g\9cGH9`R$qiuR*5MKQFF`?X@EVSbe+91R8:O]TtrJ=dR[spW)J_Rd5T+9U,
%Iqu"5$YhnMPr7?ClDX+F[f,t)?":X@bPnCAB%16qIf"9[n7jKD)qNlt:<[oZ66?L'BKX[t?9Mqu*ZTL/Qh'.YIsjt3-%0W/ro.M`
%U3X3D/9<3D7'.XM7(fuOqBaFnYR9]nPB>G4]s`\Noj5a&4$D0PlW3n`huE]/`P%)A?Q=!fr)19+\ihpl*a,p7;YJ>1q^AmBfe9B-
%DVfVLF-V5Vfl4F8X5MhmfKrD,.G133:%mn.2X'H&YFGEekem^$EgCuT<O]_mFq6\QEd`(Ur>.#e+-3APp4s'*XWpG>4g0K&SiGWF
%M'H6+Cq)^FM=WmtVm<>:;u<DA5-9;,U.KI4"(2/^VU36,A/2ag>+9BB4h85XL-?=&k/"'a^o"J`ppp>!iPVW.>?,Kg:m_-MB,<f0
%_o9m(Sili7:5o&0]_E96_KMo6?_!]4dCh^0@u`n5)`pR\9D?j..^$KDUO@09rS+3-qLLSV=3rOg\/q$nb(l&iO1dpf@DT'b_kpY3
%-ufXYP\]!ML;[nXMiRabHKPOI]"Pt7Yl3]sJ%@Uh/sYC^.,n@toqA>B2rX6uAA$g6^,iG>k-k(%be%e=8,Q,n]N;aj^J!m"frF'Y
%jk"]h2pZr&HZsmcO)&*Nm8Z+S2dO+diP&?KB=kc9]eYB5]);,cgt(sV/ua>mjnS/r5@"-PmlnI82T'aJpLj4:^\_P'%CW^h4h.of
%amJc_T@pL3CiK"DqU+]Z96F;IrOE^:m/63B??'t/^)\cuS*@PWT:OlshAo\aT3K;4Xqe]7;9hCu/4Wq8rl+&E(L"AJ<IT7>oC;4E
%eS/+FJ"CE1)NX>N]Y(f-FiqCW\QT30rA?HOYJW8Y$fp9omG6%+h(s?Je,*4NQ_.TRe%:[mVc[!X2KO6>cGcD.^/eC5X&N%kC",Z7
%Y)>pJ0L&Cob:cZK-gJ.kSq#S'q=38:n_Et<>23sNO2!Z$Er3m!V8Uu3[CsTFO\$Mhf=^@]i$`sPme5n]-Mb,6a?i+urZ]`e;K/$U
%jQY>XD7f2NH?+3'jWjnVG'3Dp"=ub-`U2KRBB0R>GPAF3p[mg8,$V<b5.c,i2\g`>m#E_`FgqC2FDTiO^n70)h7_lur82I7rq5BZ
%B@"Qam\r&+a"@/./c+3MmtC=*<4),4O?BmpX'\#qU4i_VG>URP;;X)]g03;&RVh1/5CW(Cg!f?;n"+!DrnYURpEqN-g[r24[aca\
%O.K_7?ZIrPB6j:D(E.h)hE5&PJnI"ZK<$C5?@KqUcKp$&hd-0=rO\!DmJCsn6=\E@St+W/4Z6$M]+6Qsf_6FOqVZuPMbi^R]_3HH
%If%Ik.0"bnkm)]eFSXD\Q@/U5rRW^khrj1q_;G9RGQ,AW8,<mqrq3G?ogc'FXJb>u&9[d5Eq]-@EPfHQ,QYGPLGa8k3FCl45;'Lu
%J'#Cs=t[AYa!LKI9m<41k<H)<Hsa@^=)7`@k-Y)6qg4g(p]m0NC_(?3'Y"$Frn/@8N^&b`8iXc&;>1=$q#.^#:TW'l?JCa6IYa9c
%KDVb$r9BaDdMJe\f^Q4aG5p`M>C([BrUd,IQ<pN"puK'DL0s/'G-W("*T%rWqrdq02s\rXANq[!*`R66b%:H?p7oe/#b0B<monT;
%`Zr"RB/`V@d#N%)&c)4glJhgBJo1D[ZgT3YHiM&\rpJff.WoKg?VCA^O3T2J/@iP@^A%bBH/Sml=1Mm(3[4Rm_A`!rURtS:punX1
%TDpPDpFea:SA+Pe3D<4rX7&1i[($&%Mp)!U?EN[na4]&'G4fdM]km/rVk<n:*Ib9loC/Z[[dU_f4ltNpg-p=)0/Td"1ONh@>Zmm2
%f,U*459p>0P4"J+a0#0cqRLD(DsToACL"*]Ft9JV<:OCt<etIjR'c\J<7_$f.k?>o$EhN+8E.@c%l^1FUSi,DT3i`rVY$Ihl`q<5
%5JbTU.oSEKJ+6HGJpI.?XbWhKmD#tdVQc>lU,Z]COn*"4COFEm!>eU&!8$nLr<IgCG[un[Y0r8Gb!#Z:%atf`03\8D.brV0+%u*>
%bm#a@hE&i3\WN4)7/X<te)B7:T(lu-Q@\a,L3fCPFSAe)#15*fd2\7uCKi$Q(_mnJ\W(kO*W:'9=pd25p+=:`U>NggCoY)jSA&`$
%)O$t$699=YAFNJH8Q*4D,aMN)h:lDs]%m-?ZZ.C3X6mfsL9PIKoMqOD5&f50ZA-Fl\4pl.^]b2"3`RMqe8jl,\HTV6g+Y+=lZCl^
%*9P+AMA&F<,WM-Lb`F$m_b/Js,&sEUgV84-dk'PIkEjHNoHpZV]HIC=,k8.'<2Gb5&<"X_W4K"iXf+//cF/<G8u]ELc/73$9(E;c
%@!51n&h80GlH#\-gacn*6?Fgn5#n[J]P`kGhgTVDZM>HRlJmZu]P+4[?PZ?1iO8!#Mc4h_5ItRr"h+>=:T`)#MgI>NnH$bsI`j]C
%n+\1X-D[U?9D7*&T-7&1(O)4t(Da0&.2&C(;P+9dOA1)sgk+fU\\12=3W+4#`SmSu].]cs52G:;OBm81p=i)J(Aao3WO35YqmYb7
%Y:je]hr!L<0Rt!*nrH@9'qgsr2VIZ\abL\I^]2olX62t,na:<[qXBhlD)hLSm=+Q&l`6NBJHoi^pNMsr4hkq5+n*-hGB]q1r@l3<
%:N%,B49+=2lH.l$NBUnS4SH2\hgOZIH0bLEH1nj7SD?@*V]csiI.e(<cf0jq06g<`@u]R1?r-d^hbmP3gtl;K9J9]-Spu%UjJBH0
%*rS8Mm^I,mB9tPQ[L2nuC#h0b*ad_&'!6G9L5q=ET9J9HGDcFs:3jCQ\.gEeQWb7[1X^dGM=?jM$NKu60>B*[Z`hE&QZerbIs-<g
%r>KL%YB5[$mq6:-,`i[_>DFjr;h'UGZW643X3]\S;L\?:5J@?nq[M3"T8=un2`(/IQ+W2Oo)O8KZi>:G7,la(@bgXuf4+JL;3Dp2
%jVs]``Pps%?bFl(diNoh)Ap2nAUf$:`VQ%1k<\BnEA).V="i+"Y?mD>28duQg[j+cr?bjl*VEKXQePg?V4)W+UXqtu^_Zb@pA*P?
%`C!LJHuHXYho[P@j'Vd0Un?Q$?(\fDbF/4upQnqt?i0IX1WIIBM\Y/FHp'@Vrp9(EMu-5G'"uVt&)Mf8Afh!9Db?5_599ap1@sO0
%@\k.%?:iAl96AtaphMTK^3PLH]Xbee]/i$'8X7Emp`%XJ"MR#^gIaSX\`7Q+_/%FjpPl<Q4o>XnEYSDgDb023DEn!@<oX%0fB3#1
%k2Y]]]l_R^rG,9,gSae4f9^uA9>G=CZ0`V4>1DU/ir4m#<o:GClZ]MUs*UQ\U6f&K_pdt2e;<,^U"Q!F@=S:KnDW'FkKhI<^GpUg
%qt0\_>H%GDa\"P-]t2s:-YHMIch?#afs<@!L>Lp8St8)jd`4l<W1VpM27m5OPGac9o]Eg\_;W)jpU>oRcCSn-WB&E*_s^4CZ'4.M
%LU4VicYg]Tn6EDsK0Mo&pW^lFr)rLV*$hZ%$_Il,_;G$,56+q9pg4P-`<pp9G'X&aWq0iq4^9$G:WVpoQVY'1s*V2C@o.$\&!tU[
%YJff(R<YMFce8c!rpo4Fr6*jXpKQmkD=IK3>P#.Kfpho!]eacBTBc4Ah8Q^Y]0BO?IXUaaqnpaO>i)*$G'S4UDmFsJEJ^f.S(*ME
%DZ4VKluDKk\,PE_M^Ok;Y7I71Cd%Ap?)-*t3jg/bKC8`uAfDc?iVkN5FF:jZ963;.GkORqQPda$4l`p;5P`k$qkmYRJ&@<o63$7U
%7fWDLJ,Z@<5CQBj8,lmX6c\TX]t=0ZDgq;oqhLc3@C@ZcqY'/e<1^l&7'oABf.@3r(K\)<SR$YanauY-ZX2a%p`BJ5h[Y^M/tX26
%I4N8+)"HXC-r2Q>Kfo+nn`(F&52Qe8hgPCoX2Di]o.OZYh]2#DI-<N8/e/t.bKfkWq.IIW]m=^=['T;=%rT[rSo<H`gQ+P,8MrI;
%$RLSs'HX4^_uQH3S7W__RpS3OY>\a-@o_Fs.DG+\pP4ed&G)i/j$WiiH1U+jO3bQ9_;F$Qrr:e;f95DYYjYf(s"GciiuH,u-Gtq_
%$p!JZ)p[&B8N'tn0QZs=IC\N?9nD(t[lUM#-*2Ug0)+f0m)J`?aJ>tV$[+)ords`qkHM.BCt?tOi70&j_V*Ts^X\>@Dr(0nfD:\;
%eCiOR<\NUOL]4(cn_Y\5hSg;gofBbM*$[oM(,#/?Z(E4=]@$@6on'ak2PA(rCNEi-r.:-94#I=rI4jrkqVAIJGEL9[0_=%ZB.<&q
%e=UlZr[XtlK)iYsa]$_k:0Z'G7O6S<nn8B;5bBd2YR!QoOD@V^bU"XZfusU&HLgCenIdUkJS3NY/o>meA[ru[[S(>h)iXKG`!.dD
%S+X%qI-OFDq]HEAm;4YKJNgLe^./iBD3+]d7a'oPkG2*Jq]fM=ngXTWH^'d6gtYNcGC[b9%+Eck^p0/"`31M.SpU:<I/7ppk\0lg
%[Fb-engj/oEpsu"P't8g%058W"c?q"EfC6u_dhVD^OY4&Y4biPL]*JDmYLrX#=e6*`]DJYa^<uu&aL6F6?7#)9-Dtn+(oL7:[@6F
%FePCc[UO."(I!LW\##SBBUS')!?"_QQdD`C#iL!%k5q3-Fj6_&"06oXL.;n"l6l0HFRoM^bAt$4]cBCVE/r%2"t+A<\To>EA#JGZ
%bU['J0NnWUTB$s;#!ZdV7.1R^4D+p29B"BQJ^I\6o[i;CJZNg!D]U%WNmMPOi#QMP'N@U&3F7p@(Vk!.liu$h<ie9.8^n=Ikm`HS
%Vl*N:R-JY_&,?#;c[R6/_!9)t<:Iu(7c0<jdK(USLMNLN'lUPDXDIL[BYT6+?-iH-Fna>KHgFV(j8Nfs?ci!GnqQ"In1<VKpDLd%
%@Bb<<H2[idj93]cIW`2A5ZQ[ER@mJIbn6Ms/q/8k.F::Eh?]\\#4,=0cs:8hj`CCBfH"<@ISt>hPmj&V3W6L)n,.Vhk5,jYHBXLF
%e(.a<!"1:'+\9s'4Kf?9Dp`ss.?s6u6%h.?Y6_RkqT:XZ&[CWurF%U;5,22TSPpj6$H*UeBbSBJAEf2KQ`7NUHpejp5U2^5(%8Qo
%>)?C#Y1ie3>9h,A*+g#UXOh8FYaW1NWiA\72_%PW;j#PA8bqYO8A,5CBg(tT[M/d/?;0KCFhIJcca=nafhPQU]FUS5Aj")`^%c@K
%r!!!*SqhfG5>UJ.iD54@:is(ogkQG"SIYFX*2)6V2:Qq/6#Yi9(M+ZGrn6:prBi=%$a_t`rH((*]T7q(YNZ*,$(]`1`W'9H_FXg_
%%X1pIB62]ib^k-kT*'Ot>$#pZ-Zdu\NqT6'=Q!rq0#:0PC.8bZ$iJRl4l6*AMgdE2WY>)Qqnpa8"[;kI^jB?4qu]Dna8V7]&\>8j
%phJ$hIY^@XZg;Q!^O?MLrK^I\Qc-_FNjIitP@+:S_bfr!rs4)_ruK8"jX8A-J.V=t5k$gmYIH\7NM3E&T>,_=UO&dTSeZN:!&1(^
%Adr**G'rqspY7b;#4'/cXp9o/6pHf1K/\q3C$Or9[X27DOf)Lh^^Sa8>.m60T[^NdjN6AjeA/NIi<uFm:.'=2'GqH!^RW?hJO,I;
%"lUuQ)9dcJZYL\Ic$">X5pMWJEqW+nd27M\cOG)JS+43h'K,7W:9"!!T2`S4i-Rns.AJUoo&3gQh`#*W(,>b_q`R/dqg&;i^(?FL
%pmYi_'kj+G??c1qq[FV-s'h95FSF6iIH6F1LXR$@I"0&kHuJG^SF1o9g=glZ;BKb"\\ADg]6U>Qgk2af_oJl\4V7+3HHq!tc6[/j
%UL0P+p:QkXbVYnkKCk8coau/As3_3>R,N(t+)`39DNAbJaaTFDh!t@_"s="1]]LG\28I6t&sg,f?3'HA_'^8ic'pDYFT_.7bh^'>
%DGu+QISWE(8n(VhdIul6+K-X)d:"q9Ij\8&M3)UsbLJBN\H)?2A#J3ek)IKr7,9rsB?+"jqf5B+W08=A,QR&pd+C!.*p$5%q#\CJ
%P/"]C*YaFuH.8iHcb8H(K(`Eao-DCqSEB.!:*"8b]UVjZT?S0S6iT=ZHiVk2pE3!kX``BAO*%?d>+5=DN>SbdF#@QNS<!:rFC,86
%=T,jVTn!+Ug;`JWEjA^7')koNog/0%K-c\XX]3L*S+a>+W38l-@af,hF"fH"hKjT1s7$0W-N<gML,r-)-2/Kjo;D;V)TkG(jh%$V
%J;"'hguhC5YP^!fIn':b6C4lR:^D5HbdLoa=he-V+P\$6ZhqX2]tXD%..j.`6M/mq+jBA)5Fd*3Gs:5]L]22Jk<=[!XA3X/8W['>
%)6L*aW!/>c7@D2:b.0!#\uG3`P`VJh&RQ$G0Cn=&MQ!:UlA`LM:cg`/o!!r($XPRh:JmckkV14JY?P6]8\^6YmIh30kNS>a]kqTn
%lo_n2HZkrsml5`'jZUt!1sp4fd3PJ;Lf2iVBO5g)qL)B`H$bjOpX]\a!cGUiS]e*iiiUefd@fiQJ)?JGn",l]b44c-2@#mY@*(`Z
%LA\CaLT7*@onL`_bNR5Q\u7Q,^?OM8XkeX;?e^-<?cQoJ_SFdSEP-/lGfk^:?cPZ5"7f'cUV:VTp<q!O@Iq8`,d/MdV[N(?\E0&m
%m27_b9Gsoke_00T+$^W4'D.lV*tqN$AM`N$!tpFeg<HScjrO0h<ZZ[+=p<DnX^t)k16?k91"=t<76]mmHPlHNIB@kQY3(DB\*F4!
%/:SBk]^(m2hI"?Fg@_o8q:8SI)c>(`o+aPBH-rPIdNKSqOl$_d,$];NGGq*_^;0L%/sPhhF1pfjUnor+TfMVL:)NJJebKOt7b]?L
%W,#1NCG5h@)&,H.[i3mTM.`j9AM-sR`O/;K!^7!Vj`OjN2s_a.12,N*2/p7nh_b/+WYN_AYbP@"FM6q+c<M`p#=X<>q9YR,Imau@
%CKtF-XhKa@&].[MQ_=[*'`]Gg<^nR,0],0*6h<<b1?s8Xm?p64qP9H,'(RQTf*g_eT^KA`l0S$dBQUpEFiR.NU%fGl'!=OI;(/3W
%?pEE&Y'OHdTSQXoQhE)5,WB%#rb`,*9Zm0RVa#-Sjo'#gHbM1T/_,88",#7"mG>LQJPp4GD7?9A(!&03QX-d:bRC&1OK[C=Y8F..
%Y_>RVejh<LJ88M@Z^e_SidPD#qY><ZRG.">]bsN<S(1'_r4Q.g9(=HkqKcT#Z8J1+JL@+A\jh!Moc3U?UtZN-c*tc:[jaIEIj:DS
%m__9FF$$-#_<8QdVZ([Ek+WWiU_*^a"6T7MB"u]rf<;;WPru_J!*()'n9i#f4F<J8J&Nl(@)k9/VS5lo-dtN)JY`;-Z9QJ/86(+3
%8iKot%JH9?n!NA<41Z9MCP'rE?VpZI!kLjD57u4o@K+'<2/%(M%5R.eQ@GRKc]@_2i[kl/QeLqUm()f]0C@ms0!@%=g[7<)?,<Qj
%5P&&>9r-E[*DIEK:tL*^fk.jFah0i=rWQrK3V`9<'VA-TI-JC=V`kgRApl3s5/&B4jZ0s)9S;E^na'gnUuEgfrc%S]Jm:<p:`6)l
%><\Y<GtJgA9(+`+-`Yb2g%$Kuc(2Q3=T-d9msWP\)uWi&s._b#U-$ZM9)T9kNipDur67#"f!40kFkRe's4*8*HVW`#5`X9pEcA5K
%_2g?)>nci?034$uAHI"h"U\CV.TfcZ](]c#q,u8J_(i842@#hS-dHVemKXlak/aCCnt\arlDe$oIn%WZBKmOIm_doYe">pe=EtI#
%?4Q)2qB6[hb#oWXIk>($p$/[oD99?Mn34RTH7*JZ'uO97CLl#6%jnHBLiPt'\.T<aR?0AVZt@5Qdt!KJWN9JS)9,2Y#A(&3>_FQm
%CZCGZhmGV#%r'/gd"HfbCk%13*T]keO9tfP;(47.":#9o>:0rk1;aWQOB5n'0E2nS.eAtMI(EV3C6LYKajP1o%!'?d9OUUPH+pZ:
%!#fEBV#!kW1\'IlY8/$NW>raT?XqX(H!=_W,1[*J&,=;H\BLk</Nt-V1l,MD/oW5]\5hSNAU%APNofL[1?CaB)if"lb(._&_Z#Y)
%V@E`b5"++gp',oMm$-QbM90XDDN[V0j'!b'hA`$d)KBY9%\a'6@#I^HNmBAHBo>o=GBKJ8Lp[kRN)#C@2.`<S#(tb6I[W9lj31Ik
%9bO$=^Rpq*hH;otPaCNoe\-/b:K"'#fte!I_nOWb#&#u'[2#"Y"7+)IaSJ$sWYq2o]]UuGV>VN.3Yu,n1?:\l*NFX2]QM0EBO^1r
%76E^UA25+.\QpG*alcaj*'Q.?E$X[,etOdZKeK]4Oq#\Rj<L/&dpF+R]N!e@6K8:%Ah^&(k:f6-1q4H*=@6*mjb97HCC45cAdG\R
%bUT3Uh\*hsT6lBKK+H[L'EeKa&:Hp/6!p.uQs]aOY=6niI)HceWhWE5<"`k`ST"dc;&?'^-37tV>/9SGcJRAIRMUjtGWD0TUB+23
%l9[i'8ci]7'Htt^6#A/".ilQGYV(SGmZX+NUp;b".Uu8#([^ms-CO1$$N>_,#tj=Di$d\]K</%LBW(tFhtf$Y:;bCk]c0"<\>1VC
%NOH83f?o6t^/(8Qp9@K7V<AJq'TCeQ4+g"09pH>2)5)h087tq7lD[m1^RnODDmgEJ;A.X8Sc]UI\=E+ZCu9=*G3XD)T37ut(CgVq
%pmco!/Z('jR7OShn0^uTe:E3VIhVO@Ns;SR3h@D'Gpc<,ld+"_mkNHqL-BI0c_PngSVlDTB!Z=@OXXB0N:mRm6Q,:c/n1%Upb%f0
%*4`9u':1a/''YqDh2O^a:#eidcM'.KTLr34m8dpMf9WbJa>bIWA@b!a7>@iRj@Pub+YGFY5j^TlGK^1Rj[@c=\0D'J!Ha9@8pk%g
%!F,dmF>bs1p=;-_o\RIp^aI_g^,>OOdHP/tms*hb*]E'"lnII]lZ>)VPhh^#>j(2fGqCOSdmZ>tZE,jDqFmHb:7AtrdGUuaj+nd=
%_j+3dVA.?-@#LudN3*C].VC](E4)5&`M,\m%ab!A7'q6ua>W>;]MgNmkH+j&T>]r$Z*TrT+@B<*<&\Z.=>R`o#-:^B\TR,p$G?R]
%KiWp]:*$j/r[=lL)P'A_"*DCkhNEe-G%L5V$#s(3Z"._//.'9tXCK;JBHk4mY=HC8f`'^UZO/sJ`QcQ#lg=brC4$\;Pm7?*a?g?'
%GhqEh"umMaPtJ4K3\m0>L2_/M"=YkcFM]'i7p.p"MInYaXB#%92Je\G.0>:qC0V4uQf..X-=#qbpjD(r#ldkS<X45`"Z&a>;hu:`
%@j.FdkKB+G1fr%&oS6XHi^JA%2Fl+1d#!QaH]n>N:KBuLr*l0082_!mOs9ha")[HC=3=?bpnn*]Bh-6h,U7kX'q'0s+qV-uN`(u>
%)F.N8%%DZYaNGqG%3eUpX-<o+e-@U1/e#c0,(spB+;t\!<C.NT:]1P%TRd(dkgi+64qM_!"b8OCLo(;&;F"oo:;[tP7+YH:&>95X
%_[o3'b,#Ij_[$CJ2t.\'%Io2//R5gokCR>Ob.B\HJ3cL-YP9:+kS42o'D8kWo)gg6,[Z-k,]<Al#[YK*0YfoODuL)a<e7mYI5s;9
%kmVm<l4P?N%=+L*)?@.te$n%4JnX'YF\3H%6j&u/=,.2kd&>b4Q"UR/G?2OFkV>Ea3`gh9d[Nb"='R]?<Rn\+k)#t9jBo;OGVuVp
%ClmN;bR$m0(!B$WJ'1<a#'+:]V?4#,-ATs<0.O"to1o-CIF2`VR![RTA\qTD<F/"G\nL'S/GNuZ76![772`rY[hI1h5UGBW<aCY9
%GiQo%Qu3C1qWAn<LEWpJ`'_IO<Ap7!H:E$@[gJSK(!Ta2/fG<ij8cr7:p)qY9$()@<.14XjRQ<s<=QZUd.\:q^"#ij5Cd)(Cn@iC
%Z.95<&+L$eHY6@Ble)Zudl^^Na'a%XdqE$ABLl!(9?$EH%79"Fp23<_b16,&N']/Vk'.,E"Dd%>!S")Vp;]57Z6S]\o%5g<6@G,U
%gE&\4*Xlq-&NP>tM,mu89"E%do0i`E2I3(+E>dMieQ<4_'2Fd5!]4)f+a6CREG^b)LAP?lKHnIS22^=?T:)(b!&WPV;ON*T]Ge:G
%6Q!^@X">B8^2J`4@kloRHOAf?jg$7R[78??U]QqJ0!XJcK1]##S$'\2JVFg`8EJ[`>]mS&.JG2D:)c3f`NQ!F0ZKcQQ^JcR;hOT_
%a+6X+JNJ:.n!*idKWdj's!>bdXU$#Bq+j<U3Pn:M;_rM;bf:MB**l^A?/B1H)>>&(^'m9e*Ng/`<=U]/f&H@],99_L_t4D=eC"8F
%jdtmUq"iH%#l6A@WtJ,Q/TXsc./#h[!.kQsX_.a_EA">Mi,`S=7jc^K5u[\WWcgmh\/;&FW6dRef-HO21qmF*BVt8K"k(ed:FrQu
%X9PKQnglP57,Z'KTJG8M*;>h]g%.jD9OU.7B/&1bCEO;.'OjceSpDR`*Y_kdjh4^ASo-(^^GtoX6mgaI!Y=FjOF=+]g4P7XLleej
%0A"_[0bu!*9a+96^]A?7KZorsJok!&f\'))306k0/W%3Kqsr[>o>*&D%:=:;J@$)N0#]tknU`PrmHr1$Q2.X;rF#\,0Z>[@jb:+8
%<EMAIL>&=q-'+pdaQc[\^fM9p%UnSW,kYe%,VM>iE1b(#k0L/#gB+O=&r4b_'H%C>/Y>LqBP*<^I:&KToO0r1%@k>2TFXaZ,
%'!<G]1]fn_UuXj+$j,=Ie[nQdL%eI[.A$7k]G;-EgUi:fQ":sa8AQmC,F(m.f:=mld:tgaf)GG!rorZb@^^B+#&BbpqC*U'h*iGT
%o[R#Y=ak)<dK\k[Mpc6-V.WCF\qB&4XVpGZJn;P9>-`$Gr6C67DC)]7FK]9b91*&gZAoc8aUsW&%\:B?#=V),FJ_I-)5_>-KLLqn
%j(`J`g*-+8.EZM>)cpt"mRH``;?hB(iZ[T/E9$8Qjkb1AG.;9J*`Wp`Urtj=';I]MRAd\>8/XjeH/*I>Fj,la_9$1PXATjaO2:;!
%NCDKeio6J7;S_.!p5f,K?_j\24hAUp3./M&ZrAS<S'`jt4i1!9rFkJZTN2LKQeFnAB[q@2?d(AeB66tS#@KEb#C?$U"RR^BPaKG]
%%TNKeb=fZdf9(T@:Jo)+")SL6Q.2Mcnr4=]8_@8=e>0B*ECbIm@b1c;l2nm!D;i044FmcK=b@jmBYSA+5kB@KhZJIq$lGq2/Lg4N
%n5r8[bG=LuNjb:9LJu*KZGfe#Fc;VE&>[6.@SNC-]JtRgSqXlHo7_)(K-K=aZ#rGb=_hjlpTES8!d-#`PXrZ)eih[\oAi?qRHo0W
%A-h72LNX?!9I>1E5%8fQrJ:EojA\qjNY26UDa;tMBD2m(.h.@+dXugQ,Z4f>Ef1)0NNus;;1m)shVD=e1hZEY95lu33LE+mnONJJ
%cg@9TS@9[d1SDL3-c+o@S2ZlHqrFk3En&S_ij+>fX\'Xnk2&5C6*pYmag]0:%Vtk!!aWN.a5#?8$c5U*7D&!0&hH(X4PK4gnh<^]
%VFqb(4Fu>cC1nuO$ZYiaj?6BeY?O:2@2&56k_<bI4Z!fRHHUddj)KWr_K\-.G@Oi'Sm,0f^D1\[V0O't!/H^R4j7Rc[klJU@cX4(
%'a<5a27*YdK3JMh1S56W*:uI<oMV4,!,Q@+$1gZ](71n$-AL1iNB7]Ick&g:qW\ZuZ8u75VlF<a$Ro8FGd04/lVi0`V/(,FD@E0k
%`WhZV<aOm>m'F6oB6,k7/cN@&W!h#d8g%6e%i3F&GB"Bu_B#eVla(@u(N)-"oE,5?/*PI)fa/GNKa+:k-<j9/d,.T?Ad@^hhe-dm
%g`1I`3Y;MC[$aHCidM>3Mrn#_Gi+?h#G;B,8qF\5p#1+Oe#%,cUf6;B,1'l!HbH*[[[E@u"Ii<R<'G!s**48WK0bT`Qh01q,#U]V
%/]/VY-Vf=c(2l/5RTS1m@35+8dfjU<KcDT_Qao%"l?'CfrIIBTQp=4#9EpP\SF:bm\B8u'i\NodoB=Sb`%J>%Ea;Pb<>%$c\-[7&
%2X0P&rO1H#;<7o8#a5.0PBd-P7`*$1N.!`&O]mrN,in!g7X5<"OEk/"0/s1*RMd5;SsY?*]s[hJ%0Th7>>g%)](3efX:emGUm/+u
%"gQ%V'7_S%^b=_7@M>jhgp>-PXkAL"&P^h'Q'b;dNU\uYjX?CQOd*4J3-I$+C]JYdT3:QpahBJ&)so#0Nudl+U:O2WW.h6XWgePc
%<@-HNdkTUS5jI;sBa&UXB$s![D==h56B8q)]loYo=WK!3+5BID=Z=/jA#YFbD1bf,GcJP.:5prH,%2((mIVgil0?G,`)m=%Z1'#8
%^J"5AfC@!#i/63+`=0d;d-(Mc>6Ak=<Qci%KM`B;V"H'b_CkP$=`77r9;CM=#[fM@<a8EE&3$fqRfP-NE2O7Fai1"-WphJfYfE$V
%S2u'Ao1Pa]ZbnAdMFgD$I,Yl9B/aI"TO08N8l("?2T.,qnOUNi(k&sqrI1]=1-jCqBCe)g\A7$GDq7K=UiP-nd+^arfL=YlKo*I`
%3__;N\&&cOs2N`X*.Yu5<>&QY#[D;7W&2F/S-85`fsPl7a*JDTQK;]'RCT&@)V7.^DDcWAL>c]R)`Pg[>D-Ur&T402fJ=PU]?f)_
%]YtbMEn4:@c1)`qdn)#uHc2BmD+[fGWuS/&9pCQuGNJ<#QODK52CV?H-<X5TIj0L%\ETk&XOONU*2$MeVWE&e9pYl5DS26lCQBdg
%<4j/l?=6kp.Ogi=LA&WP&[lgJQTRC&LbO$*SFZdPr?/'8W9!_RX&o:7?S-g!`GEY&4aaKc%:HlC>JMQ,>J31k:$.s&j?NH\kHouH
%/"fR<cJWo;RPQS+F%!4'9M:(j#C?_:cE9h3=pHW$KG"U_"pH$8!0ji7Htg+\a1%qSqcZVa%Yo\BQg-U%o$U6uj7Uap=rA-:nE/nL
%8Q1=hDtd&4DX0-5k=htUE2#@t>(GXVUf[[Q:+6qd'?;/5)7T]l13X23Wa_1CrQ-p/d>`o^cSi1g7E-KSGIljIgo==_gF'eS&$t,8
%C/7,:;W5f#p5+^s49K30Wa0a7Xk>s7GAA=7g_'jKn@rma-]&DLA?3JH3+e`_L`+Vr1cm[mAL;8k3QK[/^1'6/4k3P%$V-p6?Bkan
%/tM4BR-4W-K=]2!$\mm'A(:#@C7DcnM9^ZN'K<@3]0<?IFN=@M";o^+\4WnY<u6^6-J&*8n<j1<gI)::SX$O)qi"l0$dQf0\*Edk
%_"LKp;>5i"OdR-T^/]_egGK"<oAJPMl!=W4f#S%r"$_'hn1p(Mi9P_+J_P<P2[WntAq4Tt<cD4=@LTgZR>-K)<Er".dRG/0o^]T'
%K)DN^URV9XZH^3XL,D"O).52\RQ"L_,M\sk'S+(r$rL&H0'%J=iEF'35pqn7$fkWUB?sl33eZT\"PBHA75U^E8RZ*q8"_bbVsR.>
%&k1)MYOoYI7PV-A[^pH7:cQ^<Ukf>9U?hkIe#f)ik;B^9%T^EOh:&]1XOqM^ZVD)LT[O'\WI0L3JflW=<@h@<=G\q0ihX!fKI%J&
%%(+Pp6@d#GS(lKN^ns$g[iG83dG[ec=&@]O*;0<,+T-NMh:YSKMXp2kK%o/YnrC$u]JAp62!8\g=<<Lt?urpW@d!YFmGrT`"tr<X
%q1Z7J>[Pg,2_"]DHF6\AeFf/K4ELLAU_8;uU=uQZmU(7<+7T*,%\N_cpa9]'0aru3`-b2F?m!3-Xa"jmd22,:pAqf]c5JLGmiB=>
%2$1*<M.5,,$Qm>j/DL(Kj??>fQmJ?Wrqc9q_sCY4_]2\ZL=oHQ.:tB`gop$.ps+=<]ItO%nA2Je?d#Z[ZNMAo"n9PT`9$J:Y#8eu
%63&oKD=-]JK@Pjn7_=:`$@_ZAY;.^)6L\bc"YP97]^Y4eI?fHe]`fKo"us,"'(YWKpgaoNYHY#B\)NPYHuVD"6SWIb][Pf&lQ(/0
%!gcjZ^orPn(;uX/"8Le$dF23.[,8^'(&6<"m07bmpn7rlLoa9Eqg7s8SW*ngj4Mls!%^I=^b=V=[Su*1#Xch>S0XWMKH.AcETa1h
%mJu%Km%=?nD67L/jDD\r8Kddmd;?fZ`)!m2,7-=U,;uB_,b:bG;o;7MX(:bP049S40ZU#%'<^^$pnXXO>P:`l60DAtJ&3]NIf2Tb
%L*E^X2%_kbT5(.hWG)mQ@l-GW4$0AQFpIC`0NDo"%GDW)1U+\'7?m+g-ctA5?lgbV>UY:/](qAMEV\Y$-l%L\M]DC,)+9SrTBt9,
%-+F4K/Xs<r/iU>D]^QD:iSDOJenNWV=LcJ37b4hPBNa];dPEn7W$[qm\F]cmT1sQ]ccV?R+M^(6RFjSkRneTC^H@ER)KQ+DHX>a\
%3p"me3DG5ueHZ:ukVN%uk;Xf`Wh1Y%Fu&uq6!!F;aT@6eaFP)BFct/p7Z[iAk"aS]()eTo5s[/[C#gRUmbde>YltTU:e1WBR1381
%JH(t2e2T(CHT+&OgXj:_GEZ(gCc6Mk=A>O)O>?jQN-<X/)*NTeAf)lW(oGe"TY1.-L4rMq8?"lgR,-.F@.s?k%Wck`#?#_F\c>.)
%?<[ed""VVc+F\;fpX0@7c\VWD\Y50r(<g0#Gql'eH,iUCZ]CN"@)\Es%doZl<\G'*CnYDBZU[Y-b`VVXR'U1^0itbg`SA`%*5hYk
%46DsT!Z82b(GPEqO"f:41h$$U_I9ObT>ERSZ&S9aB9*AX/<,K@-@pFm=<$i@##Iuq^f'3t?ndk`6IijS$a3,L-@6a?(24UI=OaW8
%@Bo__N$Z^&-C2kmNEFn_P-@X+gqhi7:-4cNE(R.XQY'>KR:WT0;6OBXe(fK(2m+S&fa5G*%r9`H4Ou,9Rom/D]!-Xk=MfgcXU,pW
%K,JL:koI$_Kh,T%n&GpmjR>.;)q9&`d7#>i=L*-+(q4P:@$-EaK5Feq(n?^"@;m=4>m$5Vq$]*JKo)CPSU49>iSA6)pq5pi+_^9=
%%H79+c4HBZ986P1bnuVGjO0-(-?=jr0Zt;jbJMt03EF=X%g!0hPa2M@VhB1$r=qR\p*D0*X9&T.)7M382D6=!R\R:-)%EFUa_5*L
%;knclY8Q.BOH5UtLL=XE[*pG)H3^4[lPge\`VHE/Gu*O\M%UCVAnm6S1#FN9`pu7M2LZM1<b"eX)N;@WDf6Ui8h8ql9$[MVj16(q
%)@mTt@P["2A!>FXR@'N3ps__,dW6([<E:HOg"DdFkq-M).&>CXTI%&rl`@@gU?`@EeSoH^eFZ`3VOlDl0TEF'WG0aCS4q[I*&hBA
%'.imF/bSjsfm0lTR/+G)2KMC1C%f;GJnCDfQd"u6@;=)b"*SaBa2@$M_m+)s0,H_AE'E=+o1h5_hMqgk$T@?+(@,F3Yi(htf#$n\
%T>d9I)j]teZ2leJ3*/gYZLu2j;RX(:QHqn1Ds7ii`\JF:%EhqSiKf\^?F*fF27M+!o$jd8[=qh5"^f_.V`0PlR4A`Kq.;<kXj(Zd
%C\os5>0#ERe%3[I44`F9%$0W)XMFmK_oH6B`eg5;(75J">;YKCgTnUbg</`E`R\l!#.@rTS$=PVK!QKZ[)$XF7g$@Nilp0Q9I=E`
%[1Bc1^/GO84MDZdl`!G"hVDRRq^bj(B^E@-^cK]^cKW+r%ASPk.6C$J_(6Q4g,*ga/=W6k@l%j]qTE3oZ'_p;NoR"cE8']CQBMq>
%*N(Q!kW>%^0m::CPbk^PXm_fD_nk]0B*I(91_en4ZcgoSat>00I!>DBRt`G>Ls928_85@3B]Ws[TM%d\X8\jU2QY(0\(_mE\p:J`
%E_Gl5RQJ2&Dqn@$8\;UZ>1UC&Mc9@ank%b7\WOik97MeRVd"r]:KMBj]UJA[n6W\h^mD%ndUPhqIZM*Cm!FDZOnD+;M7+ep4joqJ
%8uh7ZlH"9pq0&C7fd&dJ=,[M2`:dY.lBjLCeVR0Kq?7G<XT;nZS@$*XS0["Z*\pQt:W[..aHDet:Q^&\Z6@hVI$HJUAeIN9XA17T
%M(YcLV#<U0*I+(mN_2FAb8@@g>q)miNj3fm#huK.H!U]#]P%Z$o?9U1%(.&U%u,Fk&LS7]o+i\GiA<62LZum=5"l4D5ar7bF5]*a
%@Z<=NnOUUB<]iB(4)dt$G3<o8SAQMfdpLo6(R%!.Y$<N#\GjpbjEBO<Km,?kNYh*#(gQ%Zm<"o&#,qbjgi"21@0d[=Cp#,['8`iQ
%Nl[1K)>(TMZZLFE)KT=ZUdjU]-sN]UYn3K&`U:eM/77nY]T1Z8_<['u0]9Z3EUUq]](O@k!<lO%9^1ZYa$eoAT;#Eu^9Z20pkH2@
%eT^?l2*YM*ZMnfR54ds8K;?702!t:upp1oYLHZo5TOVXG]_A:U5c8?h#IZ(I%_CZaI5%>d>J^'@,8oiqAt]P7\$N1_jE=$/U#>g^
%(!:ALq+ra,;F:7t:CMSjEDK]rBkt4h1LoD&C9a&.G&&8K]uEa'%R2:<D,M<GF),\$EqRCV((7%*SDFm*\Lkj]nod6J"o/H'm[td;
%PmKoPa($SSm`2AcWI>a8I.$s+UiJGtZt`N2flj]uW5fO@7:j;DO&h^:'V.6b+#.PJIYde^;_du0>#\u0h!krr]_4U\FURS]df'a(
%`*;@i2SO7dpBa/[7hSFC^IZkPd9pGF=AG$^E]TSPm.P_1dMPB8F"tC)fh^+E.QB1E;l0&lQ't`D_6bfG32?YKm:s3llA<<%cAid3
%YlDcI3Y\[5A4fnEOdT5ho;nM$WnH<(Nb$=u)3pc954%+I:T!Rn(N$h:=QB;Ub260=GW+^-.;a[Z2%1j6DAQS\o@1-[bU5d&;nd<h
%*t)1dHn#E,NM2FID.\<q0QqL1U<SlLMBR;k(TW-8<*[+<;C0Z1;CTRJ`I=eWZZ"$SU$4*9jcI6IpY'ZLa`6+>3!NAm7;ep7Q-W@<
%:t_1`M^mHkOO`\SR&uB#d<WthQf.D5RPYm`C7g+2:tq%`d5mV-c0IBfbu^MgZ@`[#f>N.eX\/VgeY%RkX>_)JHDd1XBA)D&I'04;
%9S4<,*/ud<BfCe6I^!Ms%'X?^4SrqQqqGKdCZ4c]6-K#jmC+R!;PpiN0DRNTj,E@Fp,R$:GXnT_=";TmdRNeu6%/%bg6q$$)d/A_
%DS'm.oM!AUZPNsMCVA&5^=54jo7ao^R*YN-_eQp#GP$dqiVa%3:ba@=aMqVO:A0F4CGHtI#/ku#W]'2g-:P/6I,IpE/L/!hQqluP
%j'8^8)DB*=H9tPPEfqqU3g7NkK2(UIg,I9'<n6GTZ/<lpVVO0]<4n4DqE4o_dlEa:,j2#$F#DiMP(A=#=Ul6J7H,,=q6md)7gm3)
%j$\o.LAEC0$N!Js<L\lBelJ8R>t;#-F$9"(JW5T.O.0+#Y&1'7TRTTH3TE#)l?'KS-^FgIG!\./mV9k8@?8h2BHP5ueU>KS`t*@b
%=M1aI4M3!+=d_NI;T-ac*g5EVb0k<U^)>nt[u+)SNF^O_d_hE+*B0=Cr@X@da.`Vtb:b0\:38Y3gd2%_L@GR`6:D4a_04neA@=t2
%1\gPPnkK'YJH,*l0><R5n4'Z6pqjo!9`NF'Cpe\0G+<a677Wb'9(9YRT&#54n&+b2S`0TWA#PI2=J>mY<N]C\4M@d*dIgP2*T.)f
%[l;pPl>Qh2=jd':&)p<8J*MN54)Yn[)p\8.hWX&<Dpj+-_g*L%%YB0kk=<Zin,l3/$(RY[JHHVF/4D8"#X/MV6W6,B5S-1/'_ieK
%,"'3X/B\>>%6fjT6N^o)'a#/p^Vp:#6bUSBL:<q7eJE1LFd`<f^[PG7I%#\r^Y_TA/c4+4&T>QO_8@JT$4GaC9Sm\*R\XmD^-ZM0
%[Lr_^kZUPY^^6SHe0^1f):6=^,g0ps%*u7u"5"D&XnF&\RUCB5g7U*dW1,+nS=j!2Q/Q^$??upHBUfXM\?IJs,t3=m["8;rL.6\=
%:I=m`''FOt784IL#\k#EZIHGRGsh_s=IoSE,#/2G]t#RX`-IIIRa-M)!N/3k!TjP65nl,*"HEPc49r)F!h(\iYcoWRiY2V79l`'c
%0fMh205+ognG*i](FVmkiWV"D%U`[h;/N$6<pe'43=Cf"-o*?(irQW_8q85h&1Xp\-@s!95nMSLf2`Xm!U'W#i^CQ^8d'QF"t2RO
%0</id6sAtKK"9<cH%9<[$[;`o@!Mfn?,-KA5Xu=TMa^p&-BJY6c;hes60-Nr=p1-YE\--+:fP-&5`MLP>uTg`Sh:!?A,PL3%VQ>3
%cgmS+ocAdD,Rq/OUH!hdTLl@-aU-<>7%382,nL+g=H5S,,fd[Q+p"9_b6Gc&&,7Lm&;[AAkiq&E2#L%.es_/l!Y/*N6.ZI/S,p'r
%2K9E;\AJAkd^7Sl(.jcQ5fm/[5_(C<Z4m:LBNuZ[9a(se#\XQ1!C^DUTS^3o&SNJ9O\uo9W1o(JJIBg=T;-tY?k^e`Z6O>(8+t`)
%TqI*9U5016Zl(R'83fl\!)8+p+Ih=-!pLsA.)doR;^E9RT:cqO[&WmPQoAd>\-Ku;2ee-[D[O)WA,s`i),T6_eASXhARH0G;eP3'
%GRcStlR&87ne]7M4U</3?.4em?X&(7@5tABKBs%$JD`h/7fYb>PH'pI'VX2!M[P2WemI?*!?_K7$DTU35WeHR4I@`4VWR)%0T&=U
%!.jQlUc"_FigF!cHD50eUug[,U9a.P5r=ln:#qt-Mk[?hWZag2EY/>Q87(ts1c.%n/-+9m?Tf4A65LgI]4P1eGrX^q-rk1&!N6$6
%,&'M`&kRT]=X7[WOCH+.%^MV$E<aZ"3"02DXQoH^6=V:ad@,uKAtfb@Ck<IJ1ip/VTb\Y%<;3.FBki:5\.0'"$a5T1iX?&k))fl)
%0<.`'#GqSPL("ki:!a;j?//F!J?Kd*WhXL\WK"%qE.npMKS*b@7cc^%^]b0.Es`cp'Ert4)6=_=<p4"$'+HU(f#d8U\>na5Ps>;I
%^f;4&\ALQ2i8/;D[sIGf5;KM[UjuPVKd^j5(QE#_HnT.N"B\;6a@m'd[X:FqHL;eDn3+YXrp7OBZQHk$VOP>/$g`!"!5cPlaM.ZZDscS6~>
%AI9_PrivateDataEnd
