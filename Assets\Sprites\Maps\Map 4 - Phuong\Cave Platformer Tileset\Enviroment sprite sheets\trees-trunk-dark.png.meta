fileFormatVersion: 2
guid: 3a0e0e9389f595442bff65be2c3c7c67
AssetOrigin:
  serializedVersion: 1
  productId: 61672
  packageName: 2D Platfrom Tile Set - Cave
  packageVersion: 1.2
  assetPath: Assets/Cave Platformer Tileset/Enviroment sprite sheets/trees-trunk-dark.png
  uploadId: 154679
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: trees-trunk-dark_0
  - first:
      213: 21300002
    second: trees-trunk-dark_1
  - first:
      213: 21300004
    second: trees-trunk-dark_2
  - first:
      213: 21300006
    second: trees-trunk-dark_3
  - first:
      213: 21300008
    second: trees-trunk-dark_4
  - first:
      213: 21300010
    second: trees-trunk-dark_5
  - first:
      213: 21300012
    second: trees-trunk-dark_6
  - first:
      213: 21300014
    second: trees-trunk-dark_7
  - first:
      213: 21300016
    second: trees-trunk-dark_8
  - first:
      213: 21300018
    second: trees-trunk-dark_9
  - first:
      213: 21300020
    second: trees-trunk-dark_10
  - first:
      213: 21300022
    second: trees-trunk-dark_11
  - first:
      213: 21300024
    second: trees-trunk-dark_12
  - first:
      213: 21300026
    second: trees-trunk-dark_13
  - first:
      213: 21300028
    second: trees-trunk-dark_14
  - first:
      213: 21300030
    second: trees-trunk-dark_15
  - first:
      213: 21300032
    second: trees-trunk-dark_16
  - first:
      213: 21300034
    second: trees-trunk-dark_17
  - first:
      213: 21300036
    second: trees-trunk-dark_18
  - first:
      213: 21300038
    second: trees-trunk-dark_19
  - first:
      213: 21300040
    second: trees-trunk-dark_20
  - first:
      213: 21300042
    second: trees-trunk-dark_21
  - first:
      213: 21300044
    second: trees-trunk-dark_22
  - first:
      213: 21300046
    second: trees-trunk-dark_23
  - first:
      213: 21300048
    second: trees-trunk-dark_24
  - first:
      213: 21300050
    second: trees-trunk-dark_25
  - first:
      213: 21300052
    second: trees-trunk-dark_26
  - first:
      213: 21300054
    second: trees-trunk-dark_27
  - first:
      213: 21300056
    second: trees-trunk-dark_28
  - first:
      213: 21300058
    second: trees-trunk-dark_29
  - first:
      213: 21300060
    second: trees-trunk-dark_30
  - first:
      213: 21300062
    second: trees-trunk-dark_31
  - first:
      213: 21300064
    second: trees-trunk-dark_32
  - first:
      213: 21300066
    second: trees-trunk-dark_33
  - first:
      213: 21300068
    second: trees-trunk-dark_34
  - first:
      213: 21300070
    second: trees-trunk-dark_35
  - first:
      213: 21300072
    second: trees-trunk-dark_36
  - first:
      213: 21300074
    second: trees-trunk-dark_37
  - first:
      213: 21300076
    second: trees-trunk-dark_38
  - first:
      213: 21300078
    second: trees-trunk-dark_39
  - first:
      213: 21300080
    second: trees-trunk-dark_40
  - first:
      213: 21300082
    second: trees-trunk-dark_41
  - first:
      213: 21300084
    second: trees-trunk-dark_42
  - first:
      213: 21300086
    second: trees-trunk-dark_43
  - first:
      213: 21300088
    second: trees-trunk-dark_44
  - first:
      213: 21300090
    second: trees-trunk-dark_45
  - first:
      213: 21300092
    second: trees-trunk-dark_46
  - first:
      213: 21300094
    second: trees-trunk-dark_47
  - first:
      213: 21300096
    second: trees-trunk-dark_48
  - first:
      213: 21300098
    second: trees-trunk-dark_49
  - first:
      213: 21300100
    second: trees-trunk-dark_50
  - first:
      213: 21300102
    second: trees-trunk-dark_51
  - first:
      213: 21300104
    second: trees-trunk-dark_52
  - first:
      213: 21300106
    second: trees-trunk-dark_53
  - first:
      213: 21300108
    second: trees-trunk-dark_54
  - first:
      213: 21300110
    second: trees-trunk-dark_55
  - first:
      213: 21300112
    second: trees-trunk-dark_56
  - first:
      213: 21300114
    second: trees-trunk-dark_57
  - first:
      213: 21300116
    second: trees-trunk-dark_58
  - first:
      213: 21300118
    second: trees-trunk-dark_59
  - first:
      213: 21300120
    second: trees-trunk-dark_60
  - first:
      213: 21300122
    second: trees-trunk-dark_61
  - first:
      213: 21300124
    second: trees-trunk-dark_62
  - first:
      213: 21300126
    second: trees-trunk-dark_63
  - first:
      213: 21300128
    second: trees-trunk-dark_64
  - first:
      213: 21300130
    second: trees-trunk-dark_65
  - first:
      213: 21300132
    second: trees-trunk-dark_66
  - first:
      213: 21300134
    second: trees-trunk-dark_67
  - first:
      213: 21300136
    second: trees-trunk-dark_68
  - first:
      213: 21300138
    second: trees-trunk-dark_69
  - first:
      213: 21300140
    second: trees-trunk-dark_70
  - first:
      213: 21300142
    second: trees-trunk-dark_71
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 16
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: trees-trunk-dark_0
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_1
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_2
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_3
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_4
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_5
      rect:
        serializedVersion: 2
        x: 320
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_6
      rect:
        serializedVersion: 2
        x: 384
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_7
      rect:
        serializedVersion: 2
        x: 448
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_8
      rect:
        serializedVersion: 2
        x: 512
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_9
      rect:
        serializedVersion: 2
        x: 576
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_10
      rect:
        serializedVersion: 2
        x: 640
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_11
      rect:
        serializedVersion: 2
        x: 704
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_12
      rect:
        serializedVersion: 2
        x: 768
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_13
      rect:
        serializedVersion: 2
        x: 832
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_14
      rect:
        serializedVersion: 2
        x: 896
        y: 256
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_15
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_16
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_17
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_18
      rect:
        serializedVersion: 2
        x: 192
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_19
      rect:
        serializedVersion: 2
        x: 256
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64305410000000000800000000000000
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_20
      rect:
        serializedVersion: 2
        x: 320
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84305410000000000800000000000000
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_21
      rect:
        serializedVersion: 2
        x: 384
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4305410000000000800000000000000
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_22
      rect:
        serializedVersion: 2
        x: 448
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4305410000000000800000000000000
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_23
      rect:
        serializedVersion: 2
        x: 512
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4305410000000000800000000000000
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_24
      rect:
        serializedVersion: 2
        x: 576
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05305410000000000800000000000000
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_25
      rect:
        serializedVersion: 2
        x: 640
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25305410000000000800000000000000
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_26
      rect:
        serializedVersion: 2
        x: 704
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45305410000000000800000000000000
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_27
      rect:
        serializedVersion: 2
        x: 768
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65305410000000000800000000000000
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_28
      rect:
        serializedVersion: 2
        x: 832
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85305410000000000800000000000000
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_29
      rect:
        serializedVersion: 2
        x: 896
        y: 192
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a5305410000000000800000000000000
      internalID: 21300058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_30
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5305410000000000800000000000000
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_31
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5305410000000000800000000000000
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_32
      rect:
        serializedVersion: 2
        x: 128
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06305410000000000800000000000000
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_33
      rect:
        serializedVersion: 2
        x: 192
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26305410000000000800000000000000
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_34
      rect:
        serializedVersion: 2
        x: 256
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46305410000000000800000000000000
      internalID: 21300068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_35
      rect:
        serializedVersion: 2
        x: 320
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66305410000000000800000000000000
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_36
      rect:
        serializedVersion: 2
        x: 384
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86305410000000000800000000000000
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_37
      rect:
        serializedVersion: 2
        x: 448
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6305410000000000800000000000000
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_38
      rect:
        serializedVersion: 2
        x: 512
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6305410000000000800000000000000
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_39
      rect:
        serializedVersion: 2
        x: 576
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6305410000000000800000000000000
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_40
      rect:
        serializedVersion: 2
        x: 640
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07305410000000000800000000000000
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_41
      rect:
        serializedVersion: 2
        x: 704
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27305410000000000800000000000000
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_42
      rect:
        serializedVersion: 2
        x: 768
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47305410000000000800000000000000
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_43
      rect:
        serializedVersion: 2
        x: 832
        y: 128
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67305410000000000800000000000000
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_44
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87305410000000000800000000000000
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_45
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7305410000000000800000000000000
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_46
      rect:
        serializedVersion: 2
        x: 128
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7305410000000000800000000000000
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_47
      rect:
        serializedVersion: 2
        x: 192
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7305410000000000800000000000000
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_48
      rect:
        serializedVersion: 2
        x: 256
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08305410000000000800000000000000
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_49
      rect:
        serializedVersion: 2
        x: 320
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28305410000000000800000000000000
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_50
      rect:
        serializedVersion: 2
        x: 384
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48305410000000000800000000000000
      internalID: 21300100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_51
      rect:
        serializedVersion: 2
        x: 448
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68305410000000000800000000000000
      internalID: 21300102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_52
      rect:
        serializedVersion: 2
        x: 512
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88305410000000000800000000000000
      internalID: 21300104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_53
      rect:
        serializedVersion: 2
        x: 576
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8305410000000000800000000000000
      internalID: 21300106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_54
      rect:
        serializedVersion: 2
        x: 640
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8305410000000000800000000000000
      internalID: 21300108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_55
      rect:
        serializedVersion: 2
        x: 704
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8305410000000000800000000000000
      internalID: 21300110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_56
      rect:
        serializedVersion: 2
        x: 768
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09305410000000000800000000000000
      internalID: 21300112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_57
      rect:
        serializedVersion: 2
        x: 832
        y: 64
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29305410000000000800000000000000
      internalID: 21300114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_58
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49305410000000000800000000000000
      internalID: 21300116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_59
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69305410000000000800000000000000
      internalID: 21300118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_60
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89305410000000000800000000000000
      internalID: 21300120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_61
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9305410000000000800000000000000
      internalID: 21300122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_62
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9305410000000000800000000000000
      internalID: 21300124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_63
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e9305410000000000800000000000000
      internalID: 21300126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_64
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a305410000000000800000000000000
      internalID: 21300128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_65
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a305410000000000800000000000000
      internalID: 21300130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_66
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a305410000000000800000000000000
      internalID: 21300132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_67
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a305410000000000800000000000000
      internalID: 21300134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_68
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a305410000000000800000000000000
      internalID: 21300136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_69
      rect:
        serializedVersion: 2
        x: 704
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa305410000000000800000000000000
      internalID: 21300138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_70
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca305410000000000800000000000000
      internalID: 21300140
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: trees-trunk-dark_71
      rect:
        serializedVersion: 2
        x: 832
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea305410000000000800000000000000
      internalID: 21300142
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      trees-trunk-dark_0: 21300000
      trees-trunk-dark_1: 21300002
      trees-trunk-dark_10: 21300020
      trees-trunk-dark_11: 21300022
      trees-trunk-dark_12: 21300024
      trees-trunk-dark_13: 21300026
      trees-trunk-dark_14: 21300028
      trees-trunk-dark_15: 21300030
      trees-trunk-dark_16: 21300032
      trees-trunk-dark_17: 21300034
      trees-trunk-dark_18: 21300036
      trees-trunk-dark_19: 21300038
      trees-trunk-dark_2: 21300004
      trees-trunk-dark_20: 21300040
      trees-trunk-dark_21: 21300042
      trees-trunk-dark_22: 21300044
      trees-trunk-dark_23: 21300046
      trees-trunk-dark_24: 21300048
      trees-trunk-dark_25: 21300050
      trees-trunk-dark_26: 21300052
      trees-trunk-dark_27: 21300054
      trees-trunk-dark_28: 21300056
      trees-trunk-dark_29: 21300058
      trees-trunk-dark_3: 21300006
      trees-trunk-dark_30: 21300060
      trees-trunk-dark_31: 21300062
      trees-trunk-dark_32: 21300064
      trees-trunk-dark_33: 21300066
      trees-trunk-dark_34: 21300068
      trees-trunk-dark_35: 21300070
      trees-trunk-dark_36: 21300072
      trees-trunk-dark_37: 21300074
      trees-trunk-dark_38: 21300076
      trees-trunk-dark_39: 21300078
      trees-trunk-dark_4: 21300008
      trees-trunk-dark_40: 21300080
      trees-trunk-dark_41: 21300082
      trees-trunk-dark_42: 21300084
      trees-trunk-dark_43: 21300086
      trees-trunk-dark_44: 21300088
      trees-trunk-dark_45: 21300090
      trees-trunk-dark_46: 21300092
      trees-trunk-dark_47: 21300094
      trees-trunk-dark_48: 21300096
      trees-trunk-dark_49: 21300098
      trees-trunk-dark_5: 21300010
      trees-trunk-dark_50: 21300100
      trees-trunk-dark_51: 21300102
      trees-trunk-dark_52: 21300104
      trees-trunk-dark_53: 21300106
      trees-trunk-dark_54: 21300108
      trees-trunk-dark_55: 21300110
      trees-trunk-dark_56: 21300112
      trees-trunk-dark_57: 21300114
      trees-trunk-dark_58: 21300116
      trees-trunk-dark_59: 21300118
      trees-trunk-dark_6: 21300012
      trees-trunk-dark_60: 21300120
      trees-trunk-dark_61: 21300122
      trees-trunk-dark_62: 21300124
      trees-trunk-dark_63: 21300126
      trees-trunk-dark_64: 21300128
      trees-trunk-dark_65: 21300130
      trees-trunk-dark_66: 21300132
      trees-trunk-dark_67: 21300134
      trees-trunk-dark_68: 21300136
      trees-trunk-dark_69: 21300138
      trees-trunk-dark_7: 21300014
      trees-trunk-dark_70: 21300140
      trees-trunk-dark_71: 21300142
      trees-trunk-dark_8: 21300016
      trees-trunk-dark_9: 21300018
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
