fileFormatVersion: 2
guid: 6969f9782d2058c4a86aab3ffe205a8f
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2023767916532506085
    second: decorative_obj_0
  - first:
      213: 7668942431699141051
    second: decorative_obj_1
  - first:
      213: -3865615691702928371
    second: decorative_obj_2
  - first:
      213: -830858006723863817
    second: decorative_obj_3
  - first:
      213: -2376944820942771835
    second: decorative_obj_4
  - first:
      213: 7657681755328959116
    second: decorative_obj_5
  - first:
      213: 4660138730982355035
    second: decorative_obj_6
  - first:
      213: -9134449417693661896
    second: decorative_obj_7
  - first:
      213: 3505341954046943286
    second: decorative_obj_8
  - first:
      213: 1707672703365030134
    second: decorative_obj_9
  - first:
      213: 6005384315849007658
    second: decorative_obj_10
  - first:
      213: 7893509101633562092
    second: decorative_obj_11
  - first:
      213: -952751972215984741
    second: decorative_obj_12
  - first:
      213: 1112293333476690195
    second: decorative_obj_13
  - first:
      213: -2796373767048124268
    second: decorative_obj_14
  - first:
      213: -6011190912784093691
    second: decorative_obj_15
  - first:
      213: 8008310171013271857
    second: decorative_obj_16
  - first:
      213: -769490126978045028
    second: decorative_obj_17
  - first:
      213: 338643826697932801
    second: decorative_obj_18
  - first:
      213: 8574377597131556641
    second: decorative_obj_19
  - first:
      213: 2198691358015970307
    second: decorative_obj_20
  - first:
      213: -930307356115044387
    second: decorative_obj_21
  - first:
      213: -486363160235436888
    second: decorative_obj_22
  - first:
      213: -506208812085195198
    second: decorative_obj_23
  - first:
      213: -4166017319972552214
    second: decorative_obj_24
  - first:
      213: -5502039888936769233
    second: decorative_obj_25
  - first:
      213: -1403909340196071263
    second: decorative_obj_26
  - first:
      213: -6676090851940211670
    second: decorative_obj_27
  - first:
      213: -3165711372880999111
    second: decorative_obj_28
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: decorative_obj_0
      rect:
        serializedVersion: 2
        x: 15
        y: 431
        width: 226
        height: 209
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b126f336dc12ae3e0800000000000000
      internalID: -2023767916532506085
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_1
      rect:
        serializedVersion: 2
        x: 271
        y: 623
        width: 98
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb10954828e8d6a60800000000000000
      internalID: 7668942431699141051
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_2
      rect:
        serializedVersion: 2
        x: 399
        y: 623
        width: 98
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0cfed162039a5ac0800000000000000
      internalID: -3865615691702928371
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_3
      rect:
        serializedVersion: 2
        x: 615
        y: 575
        width: 18
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fa5f628b033874f0800000000000000
      internalID: -830858006723863817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_4
      rect:
        serializedVersion: 2
        x: 287
        y: 511
        width: 18
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58960096745630fd0800000000000000
      internalID: -2376944820942771835
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_5
      rect:
        serializedVersion: 2
        x: 335
        y: 511
        width: 18
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c825f5dfbfc854a60800000000000000
      internalID: 7657681755328959116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_6
      rect:
        serializedVersion: 2
        x: 415
        y: 511
        width: 18
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b503e304c732ca040800000000000000
      internalID: 4660138730982355035
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_7
      rect:
        serializedVersion: 2
        x: 463
        y: 511
        width: 18
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8357897a6aaeb3180800000000000000
      internalID: -9134449417693661896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_8
      rect:
        serializedVersion: 2
        x: 513
        y: 575
        width: 30
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 630ed336fe975a030800000000000000
      internalID: 3505341954046943286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_9
      rect:
        serializedVersion: 2
        x: 551
        y: 575
        width: 18
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f024bca64fd2b710800000000000000
      internalID: 1707672703365030134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_10
      rect:
        serializedVersion: 2
        x: 583
        y: 575
        width: 18
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a22e1e5a739675350800000000000000
      internalID: 6005384315849007658
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_11
      rect:
        serializedVersion: 2
        x: 271
        y: 479
        width: 98
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce56e22d4b06b8d60800000000000000
      internalID: 7893509101633562092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_12
      rect:
        serializedVersion: 2
        x: 399
        y: 479
        width: 98
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9d6301112527c2f0800000000000000
      internalID: -952751972215984741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_13
      rect:
        serializedVersion: 2
        x: 520
        y: 495
        width: 16
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 319c9e3c4e8af6f00800000000000000
      internalID: 1112293333476690195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_14
      rect:
        serializedVersion: 2
        x: 568
        y: 495
        width: 16
        height: 63
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49820ebefd84139d0800000000000000
      internalID: -2796373767048124268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_15
      rect:
        serializedVersion: 2
        x: 359
        y: 399
        width: 34
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50ea276a6b5f39ca0800000000000000
      internalID: -6011190912784093691
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_16
      rect:
        serializedVersion: 2
        x: 415
        y: 431
        width: 50
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 131d462bbab332f60800000000000000
      internalID: 8008310171013271857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_17
      rect:
        serializedVersion: 2
        x: 0
        y: 335
        width: 321
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9bad437ec83255f0800000000000000
      internalID: -769490126978045028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_18
      rect:
        serializedVersion: 2
        x: 440
        y: 290
        width: 176
        height: 93
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10095b324ca13b400800000000000000
      internalID: 338643826697932801
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_19
      rect:
        serializedVersion: 2
        x: 0
        y: 239
        width: 321
        height: 86
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 127081fdefe4ef670800000000000000
      internalID: 8574377597131556641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_20
      rect:
        serializedVersion: 2
        x: 335
        y: 191
        width: 18
        height: 87
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30c53451422538e10800000000000000
      internalID: 2198691358015970307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_21
      rect:
        serializedVersion: 2
        x: 95
        y: 191
        width: 66
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd774da7362e613f0800000000000000
      internalID: -930307356115044387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_22
      rect:
        serializedVersion: 2
        x: 175
        y: 191
        width: 66
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a4945a58471049f0800000000000000
      internalID: -486363160235436888
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_23
      rect:
        serializedVersion: 2
        x: 47
        y: 79
        width: 34
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24e4d7574c599f8f0800000000000000
      internalID: -506208812085195198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_24
      rect:
        serializedVersion: 2
        x: 86
        y: 95
        width: 148
        height: 40
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae149eae1555f26c0800000000000000
      internalID: -4166017319972552214
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_25
      rect:
        serializedVersion: 2
        x: 239
        y: 79
        width: 34
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f29e4ef3fd3d4a3b0800000000000000
      internalID: -5502039888936769233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_26
      rect:
        serializedVersion: 2
        x: 287
        y: 47
        width: 18
        height: 87
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a892c0b4ef448ce0800000000000000
      internalID: -1403909340196071263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_27
      rect:
        serializedVersion: 2
        x: 95
        y: 47
        width: 66
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a246ed9ddb2c953a0800000000000000
      internalID: -6676090851940211670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: decorative_obj_28
      rect:
        serializedVersion: 2
        x: 175
        y: 47
        width: 66
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 93d830e67422114d0800000000000000
      internalID: -3165711372880999111
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      decorative_obj_0: -2023767916532506085
      decorative_obj_1: 7668942431699141051
      decorative_obj_10: 6005384315849007658
      decorative_obj_11: 7893509101633562092
      decorative_obj_12: -952751972215984741
      decorative_obj_13: 1112293333476690195
      decorative_obj_14: -2796373767048124268
      decorative_obj_15: -6011190912784093691
      decorative_obj_16: 8008310171013271857
      decorative_obj_17: -769490126978045028
      decorative_obj_18: 338643826697932801
      decorative_obj_19: 8574377597131556641
      decorative_obj_2: -3865615691702928371
      decorative_obj_20: 2198691358015970307
      decorative_obj_21: -930307356115044387
      decorative_obj_22: -486363160235436888
      decorative_obj_23: -506208812085195198
      decorative_obj_24: -4166017319972552214
      decorative_obj_25: -5502039888936769233
      decorative_obj_26: -1403909340196071263
      decorative_obj_27: -6676090851940211670
      decorative_obj_28: -3165711372880999111
      decorative_obj_3: -830858006723863817
      decorative_obj_4: -2376944820942771835
      decorative_obj_5: 7657681755328959116
      decorative_obj_6: 4660138730982355035
      decorative_obj_7: -9134449417693661896
      decorative_obj_8: 3505341954046943286
      decorative_obj_9: 1707672703365030134
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
