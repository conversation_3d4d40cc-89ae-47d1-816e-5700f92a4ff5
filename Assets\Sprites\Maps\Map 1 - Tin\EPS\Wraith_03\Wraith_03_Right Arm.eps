%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Right Arm.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 3 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
31.9229 52.7373 mo
31.0024 52.7373 30.0718 52.6494 29.1558 52.4775 cv
22.9224 51.3047 8.70752 48.9336 8 46 cv
7.84375 45.3525 18.8931 35.6572 19 35 cv
19.0747 34.542 21.1431 29.7969 24.439 20.7354 cv
26.0962 16.1748 30.1851 11.2637 35.2451 11.2637 cv
35.5137 11.2637 35.7822 11.2783 36.0479 11.3057 cv
42.084 11.9453 44.7861 18.4385 44.7861 24.5889 cv
44.7861 25.1514 44.7695 38.4541 43.29 46.293 cv
43.0938 47.3359 42.5703 48.2891 41.7959 49.0146 cv
41.3896 49.3955 37.6582 52.7373 31.9233 52.7373 cv
31.9229 52.7373 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.651301 .62591 .384436 .281712 cmyk
f
14.0664 34.1895 mo
14.0659 34.1904 li
14.0664 34.1895 li
cp
23.9346 35.8066 mo
23.8989 36.0117 li
23.9346 35.8066 li
cp
16.2646 44.6738 mo
20.3735 45.7705 25.9927 46.8076 28.3706 47.2461 cv
30.0806 47.5635 li
30.6934 47.6787 31.314 47.7373 31.9229 47.7373 cv
35.7725 47.7373 38.2734 45.4619 38.3779 45.3652 cv
39.4912 39.459 39.7861 29.1924 39.7861 24.5889 cv
39.7861 21.4297 38.6504 16.6094 35.5205 16.2773 cv
35.4395 16.2695 35.3428 16.2637 35.2451 16.2637 cv
32.8599 16.2637 30.2183 19.4717 29.1382 22.4434 cv
27.0415 28.208 25.4604 32.1836 24.5161 34.5596 cv
24.2051 35.3418 23.9795 35.9004 23.854 36.2383 cv
23.4956 37.8926 22.6177 38.7158 17.5513 43.4668 cv
17.166 43.8281 16.7256 44.2412 16.2646 44.6738 cv
cp
31.9233 57.7373 mo
31.9229 57.7373 li
30.6968 57.7373 29.4556 57.6211 28.2339 57.3916 cv
26.5557 57.0801 li
10.3154 54.084 4.33643 52.1357 3.13916 47.1719 cv
2.37158 43.9912 3.48291 42.9492 10.7109 36.1719 cv
12.0327 34.9326 13.7544 33.3184 14.5859 32.4805 cv
14.7417 32.0771 14.9473 31.5596 15.2236 30.8643 cv
16.1479 28.54 17.6943 24.6514 19.7402 19.0264 cv
22.0483 12.6748 27.7383 6.26367 35.2451 6.26367 cv
35.6777 6.26367 36.1201 6.28711 36.5596 6.33203 cv
44.4766 7.1709 49.7861 14.5068 49.7861 24.5889 cv
49.7861 26.0469 49.7422 39.0693 48.2031 47.2207 cv
47.8086 49.3184 46.7744 51.2012 45.2148 52.6631 cv
44.3115 53.5098 39.4229 57.7373 31.9233 57.7373 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Right Arm.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-!`-H4iTVni^5@$,o&VoRe7`cPO?BrpBS)426(7h5f_Bhu3-5RqL'^rV#mGjdWa:rqPdNrR9A"5C<5sps%9g
%h`lVdo$(OEN\@"gq`";qiMZLPgD;N8hnFC5lKPp]Ep\Z"^S3C]k$.Tr<.I1H]mfY'=$ZWTo69Yr-Vub0HhuFas7b:qOKVs2hA?)5
%]3[8ji*DAN?b*3ZSpo\&IeJtMp%tB2X8i,3pEQd"s+2V5Y,,.nU"&L?`#h<[)/Fg'qpOu7'u*s&I(A1Eg\?)Q1&-e%Zs!MfM/E4V
%-f3CS^]0/&3rUDc4PEC^Ff`-/kXr.9I>3MEJ>[hcp2getH0e],c1u$KB*V4VO&+AF:D]@,9_^t+hMnF+KVuKke<OR*qq*uRpH$5n
%>0t;Wo[jnF<]u4eqg^g_\U%pn+&D[#0RTqq/1sl_0X<Mp3MkGDC`Cl'nZ++S2\V3J.cVh\O3Z2oA8L?g[ZhuE)k*roi"nJ(Z-csi
%@,L;lId^tl8h0qCn3';>U">O%Ma.-[?TnGLMf7LdIJWBNS(I[6h`UhDn=ogQo5C5k04:3a8r)t)puU'%q&c2Wp[T?.`iJVDr:o\M
%=5<R(^-ToNoI]:l<M6NW.@s0gJsIc*ru7#9J&7hq<P1rZc7aZm`\ml_:u#'nnEV3(n='WDnjD[fmB*j9^3TDDmD"(]^Q7oIIsCtX
%S!Qq.4m1'r@+sPJ4htFr5P/C:_"?Ou+#8fOeW]9+XNOU9oM=+qn1T'30T"tBIe!=h5JHudc_#[br2oI?_qTCMIe!HSSqempOq<-9
%h94u"J+NNU46SA"ds7sBp?(](0;3mUbI9-s&mLaCJc7;ur57!mIsh5*R+[sqhr.9-po6@Qh9Wi_FnbP*kK'p@2"HQCT"k8[2nu2,
%p'raH@A)V&9+>27[r;!Ik7a?#pC,b"c+;D_Q!b>1H,V61-#g+r]RB=j(U&>d`2@YK,j-?kmdK&Ipj+X()Qk"YO6XGse"<dmUq?-e
%n(p3WXo8"QX4,BA<p?c:p1nLAk4R?(2m701^;YRC@4StppA+:(+6Bul+7qXnik'-iAVfj+celMS?G+:8+$]T^s/peCbEq,&0E;%=
%S+")el0CRHH2Ql]LTfV5bO_dH(U'H<nLq*0[m.J#iBKd"r[I!NIqnd,i)K1n^`T3speu^/iU9sL!r1&:^OH0(S/'XO?[VI?pU'ka
%Is_1_fq1I-D6Dln>6"!]BCF]iL"6+oeM$p`?f<*VG*q%Sr!1tC>)%26r&tA`r>#>V<V_=j?VL7`%.pVAKt=D"LNF$Wb$1DOpb)MD
%s*i&`JK)HkN!fP5]"n9\IT8?'Q9DrQP*^^6MiN1rijs#6s7uK62rA$?>jXm>nB7.o?U&s#:$Td.s$,Gb0XruX<s#H'Sq3\1`m%HS
%5G2TSaRaVjHVg>c!IP!Uo]"2d%Ir9[VI)KbWMf4a"B*hBf3a%)l(d8hJ!!2(s3CI'%_s*,M-G9sa%fE;^3oIk[njd7du.j?a1'rS
%qkpKEGk:(ZBBWlnAk?Vb:7EiW7am^o3f>c<plFd])Q<:OF+:1$f\6WcTC6.cbF=hO^3qk@:0oP,HAaV'Efo2i/)]%AWo-BV!`p$?
%n2qW]SW(>Q=+co;Xb!+[Yt.4)%Th*-\*mWcHhmTkhgbOkq8;nESh"_ds5Go>Wm<Y=+V"]XFE6.RML-ZS:4)EU#L1D@Ti\L;IGSfd
%7njGu0E_'6.fAiUQaciJ>$:0Xn@Bg@I8BmGg_W(ppk<h%-Je([`Zc'qh:dEe&o*a[s!r7k[cu>ef.TCd\St[-qol8Dne;59],1M<
%Sl5qCq@>5cK*lYllgJ/mHd4sH?OfTS;dY923Gt8q)*)AF[+??6s*F;[Ig,E9/3c@Eo(Z(p3kh3Ilb@rHo]m;9T)TY=;F*(;Y^e7.
%Q3p>f)#.:nBBT^$+WAQuKl2FH%=dC[GUPZQ,!H-5YX\lmo@?D=md]V`O8e\>U]:5]UYMO_nr_'$ar/>2PhY2jp_!pm=ijK4WDkpC
%#(I+^N_iJMcK1qY4FU7IYuCr3<;IN+6n50J1tGY(1b)eRTcTntlC#d(PhY?!p]_WSrB3cW&uDCiW>)?J5DOE8Ebac\dr*oWGMW#G
%'>//GTZd#ZHU%4&5PLXrHZ04>#_ts#&sNeX#+[f^6U)P#7k,l7kJPR'(<b)kn](VU*n(^C?Mm"s+q@.,2Cm,_<Ig\TQ1ON8roS5'
%L&ZX\jlB29"NhkdT7<^NYWRC,s3Ri5c^OLoYj*D)Onq$2Oa9%\;#;ZSKDlsgQeFgoc(b/8KT`9ERWOq(;j5X*#:WIQo`'0QcPak7
%PhWO>ih6K)M$blF,,J0$B6k"**;]O-I(hVYb/40G\YZ$M6b.274;@Qp.Id*(Du1=$Tf&F8p*9M8#jlWD*I:QH:T!!FF+h8RqW%&s
%::JRrI((?5EM[YG0If%K0P`@S-Q:#8,E1Y'%_"K7mPB@l;q;dj!liVrn_L]Z%kH3Y?[VSQ=@BX7LCc5FKT=52gk=GG5mButMJIkS
%$4HMHDrOK3aX84/S4]df2D/gh'.fK>"J_Ajp)/O=S:E17;dg/*S;VWX-_A<TWYpjY$XX7VM==TfIu*PUIT16A`ummo`'\i4rjS?(
%naT_S`W.;O8M9$h!:7A7X<(bU:GfE'HQRY?(>iM;O0Ga)i%$e!"^(@.2E9!!KPA;kL!bRA]hJOi[(pLp>\:>tB40I7:7YUXZN@IU
%`-HrU,LHo?JAL/X&85A[^h.J@PNUeQM][J%hZE8,^p0"@Sdm\?CUY$*45ZpiGT1Q!Xh2-W\Uc>^!lfDnkr0%]R0(E6K#JLe9+YGi
%$U7H?:r`J>?@b3<#517Kquc<a#.qXcrl<h:OB9?V7o"Mbh1'tRhdCF=huF(nDZEQ!i8hBK(39OH$Qn<d*I;#MXN>GDi9B84Y2@*Q
%FGBNN_V92mV^e3=))abH!T)VJBm'SPnc[26!3<Ia[$PDb!FpbqL?E-]Kb6Da\/f#M3fG52$>bn/B5iB`Fg)Bo@$FG@ed"7Z.u\k1
%*<Taab_+*JE;lJ<<aaKdB6BT:$9KiWIKL/op7=pdYS5>:MauEd4Z1le"njEp5idO1lf'M_j"hYka*<.1Dm0QUX9CR4@8-l,)(s^]
%r]e;o!\@5^NHR+d`FXF[P\;@6nPYkmkSk>*grGKFs3t+&-S'3[VLt-dV+IXeMA57SeAtqC:UCb1GUnd^N!5Oe.q^<8FC7N#=H,g"
%#:j);#s(pO.2Y]1@mIC?n=_Rs1sT@='upKt?H\<B3L%G'b#-:^k6M"tV+W!%bP'ZWm0t&<-P.(([iNDhO5##io3eaRp>2_94P4H@
%hYR`Y%eeV`o.KCA&))01qJG,<i3'\rk?o=OY+,/*c!%^T_,n[dU]`fqh<1q(*5`9bGWnL@nQ55"0JoFqA9[.+_tq_Wn^XO;[rs<c
%@jYt1a)A5pHbN2G>dY1o\N7F-Kj8O6Il0:\cnB8WUSAd`M;rk0L8t%Iop*AmR?bVf,9!DD1>$=2*>V9+8Q<Tk.#4T>RYp;tj9hV0
%Wi?=4OHd^:,1r$Drs]?F,`8jY(eGuGU`eZ_)<rAZ@?Y/F/#&Ig750kF<iE7nfBLUIe3?h262a.Sd$*Z)L!OC0"q-5!'t-1RK_PT)
%5`4NWPXA=Z(CD@eLm`ABC.akn?_i2QZV_H2'gu*7=Q*.8$^@`X3]EBXjfhSOZkZU6=P^SDPnXrs.'PtS:ek)LhlQZs;u:2"eC6]Z
%5?S!jdDFDJT`A\%!*'?lU&lA`ePDDo/YWA7!@I9;i5pW7Tdk_@;@@d@<Abc=+Y/<1Tb9P<,YB2OBTZ&=cj6;;9`Z"2*'Q>e/D<2^
%O%%#[7i%FbKk6'27p)Eo$k>$Rb4%*s+\hSs8(s*jluK>Hd0BOtZq1n$ojOeqU3`ElW+.QG"\h2KoYAp#:C]NcVYT0!UJDE8EW"&d
%i-Y[QihmfKYiZG&^=EPJhGO'^_iVf53"R0V*3Ot.=lB0RZ#:fVRBUnA&GDEAhCR[2_VGdf-=IC!1bik\iCn>t#](uh%0:;OAgWJQ
%O_eKoM-iX:&oh]C=D0i"KrWKmA!aD4rcfiaUOe#VM\?mE/f`ZLa9$<1Ol7IY6oGD]H4nj)F(7Wj.D/65;V9RN8Yg,Ier>)5m1H9O
%g)er8P.c8f42poMmM+>)^`.0.\*/9LiD=_,$])B,CtDAXL;h+VEMO(+M"f3-HWYM\,MTk`GRq%rah)6C8$nJ!0+jQ6h5!@`mj?me
%\t`Y/D$@(t9/,H`9/&d\R!gZX]2%%%KW-r4P(YesXu5XiGop#%(+6SVmJ]JkWq:S5We<]=RY7]&5+q!2'GscI;4.KPaWPKt)4&08
%;4VT<H*D@_`j=o\=GCEIoYasC>-(-s/*!E$B1CGWCbCoGY7a35/bWQjY/h2clT:"oHn3pVlHVL7;4'-)h5Z5dj)2`XRB\QS583oA
%^*JI9*YUi@KimhsLJ4NHW0.#bbNo6Q!LIV#Sie-2*sha.Nb2?\cNir-&5LpE&]i]=8Xr5!7i,%r7&'#W*fcF3"4&/,?Q1#_aELa*
%"T_JJ(EOn[M>s<3^oF-oE?V2?3lI0Nn?A/!PL7A7`8]hh,=*^pU>t;d^]CDBU%P)<TcPLla1,Al/sb778R;m3^KLfP(8=r;nkN>j
%%UL<OK$[Au2P/s9<fUc,]%L@H?8c(oXr^\(=Tk")X66_WTs5[K2>S7jPCfm45TStZ"=ji/Ue2P!hg!\mVe5oboq`u+V%ljnFG1`\
%NPS5Wf9nR9=V<r%&O0DGD<".sS?mI_*3HXtr\F&3REqsmHGjSoI:oW$78o1j]sVRFh+U!g=^Ai2bN'a$Y7mlTl=nD)LT-JPZ1]TF
%nc7,E@KWY?X>"`p'IG]R'EhZmH(c;G.L5UXLR.M&!pr:5H)IH4=`9W1K-i<E$In:a"/^OM>8*[:N6fV'\KFn=,QS:+,nmXbfW([A
%'G7rVoMY30WqfI*3`5H9m/qfG65^/C-P=Xa_M21QDC2/ZFt&@2+c3]Ejj6p*-jsq%,.uaYX.2S24,Re-9ouD&b[hIOU)\uj1cnX!
%=L;#cNpQE_i2MN69[$/:nq3r<Ud1U\"`I,QGnO-B5l6>eWChP#P>]A7<?Np_8@`4&l:f*8S<PS53jRQre%$')B7!LcDJ9u[B`O3g
%GRY/`X'_%lG'a2OGd/5H"bl<W-uCGV.`KQ7/u`:9KELpll[5b+DDSG:INqL1bkjNo?+fYbqo!WC.mV("YG/_4Jb*I_Cf^Z(kg_/M
%f>.oqd^fYV<l%l?;:bfL)cj4d(%<,LIns:MJ10^VHN%!P&*H6P'8*F7A</X,1u"hV.]mmY@@:/Wd'-4d1<d/YVe6D1Cnk<?WRl=Y
%X`k'I\@#>;Iu%(`7T+.O,oG0>7r%L\']&qk\ogNbIH]kVrW4t&MXn3GG04f'%\l:KWa=#J/P;R+R7a'.3P7lpf.r9PI]2C6VAI_-
%GuE3hWj9OqU5HQ4n#F14c)J07bEh=W=!JP$kn`I2Hn*C=;4gUWq#m-^+':pM>/fam=e%QsjpZ,ccC6_["H&TNJ-,i(M.U#adlF`s
%AbOH;@V<o,_>r2U\]_n9Qd+d#0I4FP\%_'u',be&*iN>JPGH_EKmPoXXLrQBa)'nu_(3Qo)=6\uqKDLc2tVjsQ=M6Y>]dXGi218Y
%?Cn1g]5&QJ!&_'p8K8Lk/H,T<[Z<";?9IrUfspNCIQjd"+1U^[pp6:(6]:N)ag[*g@[MVb>$#g^0P"0>r=7kk85P+$OEE\0=\G`.
%5%2LB3i&T+<\U[_=tfIS5&OEBHM+/SI*X2$NQgg6DNEqc(kXYW\1UhV4O[9&`jD0.eI&fkQBM,7:T>)jn6hX$e%cr4=ZD`^>tA-4
%aBmQT9WC6ud@_1lGS6q'R9:dkERLi9!N"or3;-bAc-@_W8;W)I7@NFgE_d]Lo0878bUZmC:=[Xs/.NNR2cK6.AK'$gOku_$SYE^u
%KF#L&Fi&\M@6pLL5e%8`&7GT7oL.94!bZlold\l!;e1XrgCme]'%rV+KkXYUe>bpf"gKB`4-EbR`&;#S,?X0F=P8.6(8R39?u&%X
%ik/rg($RD'!?`PV<n78U/!4\j=n.]H.@?*&'JjPfIO%/>h=@.u3?.qX`#KWGPI_AS/Nq;Q^_WT<3<rPS7kGa-AZe:@!NQ#m[<-T^
%Z%HtoU%aO&Dg(4;Kd1O'b3Q(46c#X%SA5Y,2C.Okg]OUE9pfa/@RCZ%K,!#:;Pq]JA]Si/PbH+=X1;0c,>Co\\]0*I:DE`/>R^Yg
%!t)I(RXX(>EODp]$%&B(n>OM>s)Hl9O\hC)KElm;-^n9kp@[I:#bbP5PfU&ufc:Ath;#QSOEqpFCu@njPeA$D?b7;TIQdW+4+[7G
%]-6ir<1fHtQd=0TLUp]u+iE\fd)0d.^OH%Eq:A\t'[d^C6J/'90II7lBcnJtK1uqmH3Q"[HiVHa"DZZXps?o'WFLSk%Ygu(jl!Y6
%K0tY.G#$DHdHJY2fTC1"T`Nnb*#d:g@E6>=*>Cd>aUDiG"(OjPNR!+hn2gk&<?]3=N$oGDZlXd^3<HR"3p':%]d4GbJN4rj5*:+3
%))@]AOV%[/"r</'ccgP9DLX!VMl]t4Ca\pKN0W$ETrX$0.NX1E-a?BTR*6sUR8aC4XKh$k1*K8s'JZiAKt1bQ]SrKJUiHm-9N_Xn
%AMVc[lH&J47R8p,KkMq;WkeQImr2TXC58HCMlSXFG)_;4#]#Z*Yf.J[U[P5'UmeV)9llauNkf')*%EY1\tU1<1SP)s&F^3oi]<Rf
%L.ZD/K:](JoJ,I'dYsNHb-"A5*&=5Qb*bR?n9D)M#a'mWK'+/]`9IX%#^]Y:*CS;*G<Q?DAUlI3BfOL2U[3tE.*r9Y!\A^aE%PT'
%6c0]Y]Gd3Jd,osMXWtsC)>4WqG]$)[7rqZVabC7)6,:Z[VukNSs$)[Wl:@XP,@O;D-&a*XF4C7j*ajcLBTQVHO!?sC)<$-/KT&mi
%-9[QAeB';#?;Y*?^n5SFTI*NdHNcgfPBHjm\c>pkTQ,S:YFtgf"<I8+#mSe<Jht[VeRu*M_t=VG&#2Fm1Mf8m7ochlYi;Z2NZ1J'
%DE9a+$/RKW#79$jY/qZi#bN)!P"/XS*!MPD3#ioRFR49>gGhROYYe6e1PD/>7GpQYNFRq]Lnj?/`l"X0,Mj1&JkR-u&,<R,D"GMT
%c5'rt(sjhlBGlWI)*/Z`ONQ^lSJa4CIK\$n5+",Ddm#G&@"n"mdJoC3%+_p@:pN:ek^4Jr)0YC;!1ijlc6K](D*$5c'=B8`ALLij
%1?PKDC>3QUA^q:e0"f6IjPQ>bbLuStR_Pe`>Qbij(qDe?>b4(8E<dRELIOtR&K!WqDW_Rf'kZl3R)FHP";Gj[?OiN$I_#rW=GBSh
%aj%R'!>&bi)'_L71n[McNnYn5JXR"%m,nMSU(A')!(dA/*k#uJE$bagn_ejFn:iZR#!#PEJH8LS/taPCD6WC-Ri"7q!tJ.8J3X3;
%J[WJW<>X#4ak2:alBS>a136/UZs@Y#$4jYO?s4q4/4!4Pl(-(87HUTZjpa+n\Jc)rR@1Co_'`^;YHGHCAF0Qn;uCGSr1?pr.q$'%
%e0s8p0K2SuAl""B/9R1?rThA9qgk]SNOfhPXqhc#,*(D`kNLhT,s)./<WoD;:E]h0_Oj0^h0].'fb8m;oJHTTKIB1cS+iP2&rOMY
%SS(.(X-`cdVkB-]eKEIa;6hrPpd,T+r_2'9=LlA=Mo5a6f87IiALTJILQmu:V4U/i)!/u/%$A7,T0_T$SoX_BiXtkL*`>Pg6^dDV
%+/j1,*`P(`YZ(71r6Wbr]n,7TcW^<kC0YEV5lu+I%C;:Ki759!mjh!)S_W6ViFBEp:gJH"KL>gj9_)&W_!H]EUdmF:%0gS*:H_Di
%j"@7/(ueCg6FlGnQ5)!\G-VUFB='I\_nDC5^<DbYA:`\paWH^GEnJG^Vc>GX#AP7,c49\cL'6@?B$'>5b:_?^C4%ZUpqt83:6!_;
%pEQA^KUat02](d%Y$>5:-X*-n,Wl2Lj4_C-!g*Q0EQ+THFsF=-BUQ+fL9fHk,Rt(_bq(,[Fcj(L,Z5(oRj^"+Fi[Pk[7%'e3=T,S
%g4CkG7+s1^/L2bZ%i<[D3]?B0E;D/WI;9([\apS3]]#GdCn6ha[Vj535&H0[]AGjPi/[l%a635s"`!tr3rXPBr0p57TnspV5WF!Z
%H#SaA0CJZ7a1q<1ki:iUli2PoXrr&f0ka5Tgi1/;K$%*c:[CF0Q=.6,?uCkm>Se#t`,3-lV;!Nh(#3`S[J`:biERGnJJpc8F1tnP
%`d<131$-/kNUKH%YtU?9m)Nj,<ZCTZRM\=''<#`c'@632lNkTm7WJltj='&D!V5ghX;toWfmmg)NAFsh5dgo6HNnDLZMrL-Bo0tB
%a2>/h/F<I,4I/fRrTI/QMYJtlA,4lYEE_aBR.(Td4N%$hYd2S/koEfGVH)tjm@MD>Lat@Ag8#J*C"4-(1gF,KnMii%\-gmG0t\N"
%?cT!r/o+5fq)Ud`eo6Nnb[0;^083O[JG+&_"aWNMEo3aeM^$/g<a@^J4@gW.<E3On*a"/qIf--HeBNf;=:P1:;>R/s\Nl/NF1o%F
%p]EB+5PsBG%k-S.q/#/r<T=_32>u_2^P,D4Q0pH^Y%)FI&WWcD&D;j[;ke%.6;KelV'3':nuu`pR%llAI;#Ot89,gB640iC;"XJi
%40SZh<o3%`Xh6o*iIiQ4A?_]1bVi,&QjrqdrkuQYe0$W'fsX?kA3@R[XQ3D6A7?Wj+-V#:4B]H:9>ph)Z_T/]jo[gZW:ZTtd,JG!
%=*f=ucd#p$#Y$gO1.a[ooYb0VY?,Z2"N(-l4n9gT!TW[9jQVX<]onj<"JLVkH_+fGPtZ6J98_kXQrAY)qTMNW&?iWU+^#'JNc<l%
%d'ml)GB9^mq<rhi-tkPX1I&AN7hB7I6@T,!N@O9ehq4!TH->4H`805,h[]BujLkq*[U)IZ#f$T[pe4\n9BSMmPPQO_Z]@S7IdKgt
%(^DgOR;"'dl(D/X96Z5@Ls=.jC$XumoU?5\fr\_A(njV3R5bIW+ph5#!DI0gDZZ.OP)r/l9:UN%e'=Uk!Xgqtn]-]$1L?_4%(FXR
%j3skFpPLY+q,rgC7n[?g\9#F#SRWO/W`V-gfbD]!XO3b`M&4>($0:u@Z"s2=R-UXf5rP@MY1_,DJ?5"#a&qfs<EtcbfjeM7Tif5,
%GXalu9LjtO9KnbBoYEFMJEENUaFcp9!6Yrs=TU!PE9*Q2RA]?pc'Y@H>F*05O[BJ\U:1f?8AR\[5skhi;HV9G!Y+qN?M7;WLgY^>
%8qY+%n!&/>:;^3\]q1r*-Z1lQdTX!rS8e;J`!n)fl9rES"`(H.nKce[=c@)&-!Z@)])pJ;)o,?f)Ca%#J)ERfimNI5V2m<J/d.rW
%3:r;GgkB`c&q\sB'r3tQmtD'C!m_F#L6sbbSGV8:Imr=..lr'<qEBg>'B4-DX^Rcm9sg>Re38?HqgRDLqfK*$mZcKQYHFRlmCuDD
%]M*Z7Ornao]o`MER/hN#!]a")O>RWU>F]PO?Bt,VBMLbf5DV^Ke?c%$g.@4(`Z5/Cia+.TXUXK-WPP=%)M@r%Q9nYl>3p?CdHaj&
%*1Ha8-;$#n/d8L_,TBM;DP^MS5Z]s7.!.7@)4@@,dL2;^Sj5pR_n[fX!e,]S>,.pS;`S^!='J'.\^2_:()caf8mN1-Pm\lOAhNJ:
%pmr*N'hP?bK+QV$s%aI%50o""1smgK_;8f4gn/X-C2/?_m"F1!Ci/Q<H5!`r1;<[@/?Xe^?u,(Z@Qj!o3g6o&=I88G)VJ*K5SJJo
%gJONo:2e6TH5=\;(Bf%11i4#%/<Wil.>oqGmimolaR2>jl&?0A(;[;5?bu;mC/i)B@N$("I4u?liKks0h(l#ZDPC?,JhjQ-RSC.B
%nq^mTf<X>:!c^-\AWf?DiZ`7DVo=%!E%'g?Wn>i*04M*E$!/-Z%R]b:XPB?oNcJ6@O<t"a9AWO"CEWA-X"p`tIc_(PS\`Cfo2Lqf
%S7$2OpAOE.hq?84@XECSj!0Rr/g$n-rHmm%]5iXS,LHY-+m,bH:a<,fheF-%Ot2R24!McJ1au:ki]pnL=rbV7p8Q&_i#ddYR:8H-
%b:i3Q`'r3*foS/M`XVkaWJ5A!WlH5R>!,M;U_+df`MG:&)T1)n2+f..7TFsiU4M>@c@Q./N*rJQUM:T)n4,-I(tUSerr4hB`cYTp
%Q8KlT6sA(p_YfAK.(eu'U(E'L0QR$\:2D6nkY>Ye9;5?,Gi^pZ-\KU!p>a`FUAIf3@pA<@A32R#$''Kk8p#K1lN4h(m)p@&O`k=i
%G3![&lHeLlD>3D=oo@OH[eikN#OTaMp5YZ=ZnYK<Md4IRS"h]:6h4uXT33p+PI*4.Y;FMVT"LmQI>`SU1Ie\TrLNcK/8_N:m!Vmf
%Xu'.P`hb6Z@#fS*MLlc8Odtj-D>sIFS(,1%pn+b07;R:Y)"K[6rUOe1kCk'^ml^**J8fr&5rMgB\GMp*@`C%9PM#6`4U<t\4$DJS
%0].Bl9H9QsP\VN*g-@YlO:%o54Ic1E5goXcH(&>6DbYcQplQh-8X=ZK>6:+"BiqA]@qek6(/;tmYiI>[Ok`J[2^>0]cg@lLH>T6[
%A+MUHK^UcdQVFG0r8=(RAhNC[IEN<Zb6#inC>>ZdZkP:iP`'5[TuI<bR<_SjcUXd'I*[R`1iq\cns/',B#Kg.>@SUBb%$EGe9&_U
%eT%LS@nUo[Q\E_)N0b1thb:Mq0^=*)^"=*W[?ihf3(]%)Bo\O(_"9(Xp6g=!RS:h+QAR]-Ftq:L^**(=r@@3G=2sp&9?slSHY%9k
%-DsRC?bj4bB4^NMcG<4e4sE)lp:,+((%58qN$Yr8EJg/OLtc[`)g6JC*[fV%!3e@?qgNb(^MfIn?Cplc#O^\Hqc?[TmdAZPYp1Q+
%)X"h;B5bl'q&AH)2s`)/(-?T39H2^oKD#(8h:ZGqfcsOC`$s$!!5f(5I.E)l)1/sLm8!85p6)e1R3]O=_X\otH!K4hjB$]=*l`V'
%[ZgC,hp#`DR(ds^m!$!uGPCU6K=AJ%;oXUSra!,iQhMbbSNGnaZ#?TLYfCt]m;gfrp?ZtC:AYcLADc4Hla(uP="iu@=IXXbog<oe
%KcMY"FWYel5%nTsm`82XQd^t"7`eU!<43jQrElo&."kX6VZ:uadEg=4lTJ'lH:Y$_D^<"8"ao@:JaVD_?W=T(%WCEt*(Z^&`rE94
%>s@lAl!\S<bIql@H[i"YB5b9F2@=@BpZa@ESk3rD$$_Eg6_@<K^]Lq94A#*QDHZn+a`h1a_VuA*505Am*$Von]3Q-0%O5B0f_d#/
%!E5W--;Am:1_a@C^0MH2<.lK__A"si-(l*!<NL,>=Gq0WMu4t/EG#0mHRn)$]ID%5)q,UJ!cK79fksPK8;_rh4c,Q;(lCd<-:c(k
%OWh,,1Okk2Z_p38,;X)(18CF`-"*ID`EFZg;Nj+Te*UUEX:P=pJ<2#XS2WuiZk<N<FLTB_As6g&P"Vj0")VsM37s,dF_A\_HLV.5
%&(>:8G*C3tV2Pd(3Cl&8R+jCPM1')=X6nh*_3W<D"Q-C]D;D9k(X@jFGNR@d'BW6]-SCT"G?E.h^g0;;'@=.gcl]0:rkNkp(WgBW
%G9MCU]LqBE\I$t>&+c2UW85GICA'unN>pf8$*->V,tHUU@o_jo9#8!knB'@V\PCm;'"=jVHoS_i\Hj'o7iJkAd`B?[X,To6`JP>q
%<AYb+Wt!XP,g'Ao[5sp$43?<=L<K.Np9N-#@8=W(,t]S6QZ[:@2]*jOi0CKgje-n3s/OK6mUs/h*G5iMG=R1qP<7VY>Ht'D9Llf_
%#SU\(_Wtqtb_'FJ2O3J!5N[^c%nPN2\'KHY4/sAe\j(JpLSo$:r9.V\[ZA(t>#Hj,PJ6GT]cUVDp<27jQ0.e`c12s!Zi'^`(#>R!
%V&)M@l8lAt;N0X#G*7T`aA\&O.Z=%a8uhGWB!Z&TZi:SJ21AkC2'ToCmcW8(e[%qP[TVi73OW&o\QpVS$Sdo!%39^0K*Vu:DQdUg
%)7FJ.eef/_'hM5U<PbEp?:O<,A^pF<T*L'F'K9$bW$Fp*k[o'8RC,n"lj]7!Co\Ke.G-8o(-rN6l+0R&$t4Jt_X\lR^:t[*n&ip7
%?Wf?NKsQ2Vp(-S9#:Z#1llk/t/U^X='mkE.47t;1Z?<5:+\nH3kNdqifCL!Kaqh[j7BRcu:p4/?`g>iOAS)i`X\mVpI@\AU$Tg.F
%VMm'X3\]3RAp.d.d..gT&O+#`hJV"EMm,"RkYXUp^]Z)9k\U(2\tJ[PbF%E:<PX=Fokhc467<p$Lh[&jBdd!2>J8:D3c=QW'DejM
%"umHG7$/\r0/E$>Q18r0Ra-L;2TQm%,:X%!cL5M7FK,64Yk#YO@;G<%MD)^+C\VSiniJrkj-f`>bP<@*l(Z=S@<HVf_I7h`I+5L;
%>5u=mnMLKlK;$nKP]8D`oW^")GCF?*8`,!*=qBrlCrPP/(K%/@h:B@(J;n-CQJ7*hH[L%*B4RTbfoeO'f9`fub$rlr9$mlV%\HbJ
%D:\3QW<uU!W,ZO9<&JaB)883XAjaS?jU=)SUnIZfpF]-.muBI9%Sng(MY:+N^EW)@UrI.]n2@!+'MUgcf\%kc]Y);E#dTss<APhA
%M40kFk%bZq@@dbdoFS,!#6kZeaZod6Q@D#%\@RjmIa$cAX>nI)he6S@Nqu"q,rYuHZ8%A(R_M4rHtX9,bn$OO6PRD5i$&?u^j[)t
%RkACYGj>s=+ZPpL;j3J^e,#2RdKHhK`]c9TBg-8hm-5m5FqL3'"uJjC\1Tp$fLs]DCmqh:9nhVb2DuG5MqYb=0@V74HG'N%ji+WE
%2s+RK32Al!VsE(a;rc.,DBL=_ItcYRqR6(9D4>@%I.cG"H`@ZF)F64&*$73hZ`d;A;sCYa[1(RU*:l!W<mPb`Es1%Shuu;/1V>^^
%@!$_obR*7kV"U+=T<=!@UVPS_qGcT=kf1<$Es/uQmGnWO$eNf[@&s$-9-;49oUs0>YGe(7iRb'QRI`)o["TF_K">N\mW?2($!"Tt
%A`+555&jO+=Igmj)d#lB.HYHd2tTF&mOI1g@T\VcNQ0(rc#+.(.ot9b_=UFJ1+C""PGWU:a2cV]h:.6Tr(p[fc(%^FdleB#l\%_I
%7]DmeT+Y0ck],^!g0VgD-8W-pTI/9W$'VWZ#3X2"[W:Ds!`.PcpZ%+(#HVp9lWV?<</4;/aJH30o+k8.<c:t;8hQRIWAQnk8(JNA
%pKpD'EtPgQAG.fpmTi8p]Sq.acR*)d[YZ$]T5F1/N_Kn4J"Y$^:l1IA>7Zm"+"FIWV<J!nlbhgaj)lTE6eR^8.;c!T\IeN%4*SDV
%PI>:+]::e`e-`/EAl3_LU[BNkgI1f0=eBh8^<2SEnne*tOZ7BP\T4+M'$IfC>;K;@a\fUm\tth(7\7`Ff6_jfn^th%bDX(2,%c!d
%c)PjC<\C@8fc$Vc7Q7(+8rGNKDP8'k(O6A>Z*QA)3NH#`#cYn.K5c=rNP!]mg8;<eM[fiP9Fc6,QfppIm:b(1<&6Rt<NN@q+`S!Z
%2T30<CI0D+r^JJQfK"s-'0aGc'sV?Z_+h$Pr4*BNq6tA[r&`KnSO-VpN1_!?E^6*2"B39gKVOUu@83K:%WXAE\/FC%=tN8@)N_f%
%9+%7f?pikn2r]B[Ceo?9H;,UI]#dG-.$\X[b40'KB>03o=ki4RAF"MLl+;Uih8?$9\lEg"$?#H-hhPh$Um@-29U?r3Jpg^M2n(K4
%S*JhOHG)JJ^%>IbfI_Q8Uc/'*B"bYN`ks.7Mul$nm%R"VMD!(\Otk]`[[h!Qo&!Y`r)2SS'@X'-"2n7o_"d?4,BL/85CTtkmqq#_
%%un(10Wq3c>"2af]YtRV29/nEOVTkh!*>mG,W=QP<K"%WpnU5('jA)t>-$ni-e;N+R!&3[q<_2QQVt@c+ubV,RU%%c[`d]`>T@,3
%dWiJ[T'nL:Nt?YiWp$0I\^MC8p3`cX,i2ZPJtQ%a^kknejsLluG^W5BRUO4\-u)@[hi"eOo0f7i>fMc?>1At;iC)s5(ui#nh:-h;
%G&CVeF7/VmjYg[:5:g8MO!`UZJ-\I^/Gf%Z_Bdn\$<CY<k&cL8jG>'f_[P+Mf3>"SLA1RQdeY^_G/j]F%N!Lkju?L^B#eJam"gt@
%SCC=CXAIqJk-l9cX)$_S]pJKLGI_qLV-""s`c9JhlAeNZ5dC$6/Mk>D8*m2-.EjG.CnJnP=X-SjaHSPpb8MJ-aAOpS@B!8UiEj7"
%Ff)7h-9i:-]eIY#YVm"2FBN.r>3*_m/DI4a=9OK<B#V>=%<O.f]sOTi$s3sL9\#F^jMKX'?g#`O4O"[)bniLe(HfqY4O"[)bniLe
%(HfqY4O"[)bniLe(HfqY4O"[)c"Hp$_:*eJdsT!\I!UcZn2+MA1gf`/pY]a\4*[3@&N*BpRP&+gR$p0;bo0Zi7Fl#>Fj.;%V0,KE
%2D`?B;)X<<;5B1Rg.Y^KCYQY[.-cOg@hmdY,&a2LZnqUhlVjH&Mq$KQ-E`7g6_iRZJh3APXeD34^<6kLgHl4?1Z&nHGD'^Kq]!'P
%<:Be8q`TjFGP'<,]64HT@^Yb99AfHSTtJHc+7oUXbB&/CeipMl%kl#nGb?T!c+bE&5J)G^c>e?(Ck(r;&kosNa^\ERrPAXkXF[9<
%B'hUnrHs%HdY"d8;8`+Yg0:43[d2f,nINhbK(n)^Yr"S)"U+3XP4*kFfR-kfj//pL1`QFNO>lXCqr1N,\L5Oqid8iN&Qr\!a;VNq
%r&a)js&.l?T93,Xd9^T--XCl&d#43ucXEZ>/YQq")ckA*^ZZ/o_^b29ilC1,3%(sLcF:f`(RBF$8T`:;gcT)YbAO'-hG*%N"cp_I
%G%c%ZjN*Y%4E/n6Isq?Nnd)R]6B,\X[dqO6bP+UZ^e7C>g%k"u;GH%#bk]boq_;Z[<frd+;a(N:G7U]LqusPON9n00mL+]`gWqsn
%Q-$]_kI/tn/h^;]B!C*p3UPA$/qiI.FY9:l+p.LRg<i$+:kJEb#1jQ+EOlR>D!p&)Jp2)BhVKBfpD^,PFj'9oM_q1=pX"_sZ9X'q
%&W<"R:O=M0WCUXnpQrdt>dOg!f5A@I(5"Jnno>T`LkgH>rkb8Gq?a!7$X%N,>Q/tR5/,PAV8b/GIf:6M&m,!;A23;QgXMn*ir*#S
%Mc]]5'uJTW`h^<:-iMlGmB1-_$#co$A$P&YI^Hi$@?]`"RH8jZr-'NN/*N0q.b?k\qj`h[MZ#bCRa90d2^hRLRIbirot:`>G;&d\
%k5GRrRX2]c@=<dGqsqKP@b,S8'0b=Vmr6pm_8.nC:H6sWEIH.R%T>:\SUBqh/K+O#`A:f4_dO]sDksd4k.CX'L].*SQI^,_W^Dd?
%AqsaNhsgX")ch0cI-BB:(ULlk5=bX(OQl!R1Aq84SWN)k*H_-!5(/>chnc8/DAY?Uh'JDll3".P,M$MO3p\2lEK+4qdHb;/SJMS*
%$,sVDZ9s3Aj`_:'93h?,h7c:echBW*Z0k9'q\JukUYjZOp^%C*G<Co3GuM/O`cf0Nd&S,Xr"_deQag8J>jU=7-0BYZb/I8fKp$I/
%OntD:Im?"f2e(TMNXmd>j#U)r'?fBQNmI9!i'/lB#$:W!fS4aI)^r-</s%%`Mf?0?jT$M^Z]r`%i9P'3p&3f1;%HL`ntto`^rn:E
%57>rr&d#ASjFlV(iqmqUO)eKi_lE>DDQ#3B7E`c/D$7g5Nl$\01VPD[;t`CZYG.pF$2/n84+>=@<5uLCb_U-hT?Gee\eiJn=6-k*
%]B*@EJG%=Xk+'TT"'V&gCos$3htQq,94%D&5>-8mNB9)P>W3aN``lq<&'93I9^eYt_7?0,@Mj;p?uOSOf>dIDIG;='jqI'CKYQ+U
%fok:^mP8H^r+/^!OHQp8GaD.kTWpAch$-)K7tZ<f3[Y:6e>i;P*nW(o<W$n+.5\%W(4=FYXGK'No]m9C@;uGs9uHOa'l`I\5GkE/
%QDmIKP@l5RcS@#uIDUs<r/!32'n"Gg?o+E=+]QX3&mkb&X.R.PrQDZ'?GCjWqN"FDbR(Q;h`QA[o5Ei:NIQ/"qq\`2qW61Mn".:B
%rV4bNJ,]@!=7WOFs.dX&l@@(9GCOo9kWA^Yb8"*nq]>e1^Yl#>"km=ddac[HrpcW:e^s;Mhu2j1UF-KuQi?_Os&FCT0FKmHh%@cl
%ldT4M8>e,s/a@q;]uGeVJTF`Z!\>>(;?I1Cn3_q[7i]+oK?LWbC7kV%GjeY1+*-XJ^O$<(dp;q=Z1b7b&)c2`B:HZ]22/#.%9DXT
%FK+8;Wa_(h0W;ZlUGt*>eJkTCOuIMiUe;GHgKI*OYeYV3fSstn9+0J3TZ?@"!p0,M0%$Me]*n<#1u/hHkKl+]pNYM&::;g!M(\J&
%S"Ai5V?8BEm'f^dCQs+R9jFDc4e&;RI5BgC)Xg5M%X>^*H#I(DdA<GZJ.MLRl$a)lV/=]K3l\O48JgVhc_fqmLSk+>43[#?*oCh?
%C447Zf2qf\,jTSe^'o>052A^^cr/Tkm2iikrBJ%Ap[^.gWX%`>\U3SncW%@G>b[:!p]GOm*ktO63T]LP4FR<7ot>;/S*5cZ:%_bc
%O*^eu^M;/]kpt2(]?[\VD,l5&m*_iq&q/7S:$:j-;qes.L,6)Q7rb7V]/8f`7,PX7YUu3Y[cr%-@>eul*qX"WURk-N:"'.<H7[gJ
%_mOD7Djqg?AJ:i19Cts-^uehU)/qPmCY]lt6!lN)lFB=?1_"J#oki0bLSG7&nEl5RoOS]2SkJ]ue%T*(:r'M6fB]$oG/,L%Emp(q
%0$uUPf4tM*DOW91BVsH@4m:'97Qm+%RfNb+SP%\_Fh\F.ea6TPqJot64dYAjVZ6'CO!"<iKM2qNQQkRE7*k??6r2nD/T9^r^^P]`
%SX8Q8MS!`-/;`*VpN]2*QW,a.mQ)Z@7ee97\F)e>(&A5n^m9?s/.?[VH:UH4QXscV"'4oMCOgQp;c.hfFaViZ4%4#s-8b?e6kF9W
%."#:93>\Iq%h]f;E'nCAGZ)1`@LBaZ,%smD0G=>,#h=!7980gM%ir[!M3*k"Vreth`BI7e8:$*iC7B3f[Q$&$/Ud>`>6*JVKfu@c
%f,uZ0bh[h^6V*e!K<KI_UZ3_q#*<W+^*S_[?st:3mKun=D]+F)'^1RB)Xr2<*pHj:h+/"%j3V=5IHPTjpdjWng#L]Z/\4&IFbg[E
%%TSCik`SV]NSLFNSZd4os-`7=4p>uf0nlS,j]]..^IAbtdsZn+H"h@u>G19of(_o"8"cT7QjV_AqXgZYQcAT(FXDkQ<"L]Kekq_<
%`u$5V82%2KMQo9WfWcb/r$?\q51-jX7-^U$<Ed-P`%S4fUn\Csa)d0pgRe(_oeEr.n293u-f.Ek:DtuHcs:gWl/!4^3Q3k%STN\>
%O$[sE7j]"DKCf<1qMouQ7gJECg*0(k0b&Fu<^HDo]@T=ZNd*;*,MGh%rIDU,7!M1WTd1pnpJ+nEM\X8>'Y8sFqG)5>7bh\%VKW#'
%_EXkEfN24GngO>PKN*O57kR*j^u='[;4T)2!pH>%;4W>pM7g1NRnDgh84^AA`Es916l^+*T#(h$PR2C[FWnW;$ZYe@^)ooJBdaOW
%0^;AdpeJeLb?K=tY<;2&W!*C2$p#@872@!q`57+SQW4?uW5Qp(X28,K?u$p[5U(7j;n&1t/1B\Lmct^PJ@;Z$ocS!;l+gdK:I@MH
%'Q8HHPJirl1H0=a,]2rj80m\OALKgi:2213"4&:5KH[*WD"-FN9LYStOa+%UH^6gc@b'U5ag/TnP(f9f(qoOp#8g7(;B@kSC[`jL
%CI8kIFC#H0<d>3Erb:k$7fm,<6a%7gAm#&K).B*Fm'E%oj60lllM"`#i(b#5d"ca><Tn<p6DV6ZLmYKs)'q<uAtTr&aI/DNSI7^,
%T"-lniO"_)O^Q(do2FfnRS1'2al63bM$FVDa9aDq(q^])Gj>I%bb0paq"emoGft5B<MpNpbT2UXVH\en-^qqM;!%lPbb>[h/FZ!u
%RQpuf0/*u+(YY4S<D(N_4L2/S[cfJ$+#p@=YiHrjL*27Rbdog.SfIG!1`/3PNl?hq#36WTpRm$4SAeeKC5YC]SQ(EP<)9O&L$ZbA
%1r!P]I^.;"q,t7^6",$GUprPNn=jZ>dP1c3jdp!`YD&@/=R\"(WBQM5(NWUrli<[P<Q&cKQ8/&cfgeQ'J9MeB@2,-^"0Wdh-5#Q?
%)h<S.UaJ2X1`"lAfi%=803RK]U@uuD-p]\pMX/XQhFFnX\0=l/UCSOlQI(=f7HeO2+&=@<FN;6+:0rlh(0o'ea^X*]MiNTh[C!1o
%S(->8:&?GH`jF>oJ41OZKJ`_W;nO</"T0a1ohY^p+aCMB4/RPb*,JQ"5.#(2!(RF&@\Qq+)Ik'PjLW9dl'Pmg7fLWu)&F#$l!PfT
%k>G;G)!P^"@QGE,Qq3-qpn\4,95=e^.++AY"">^EcubQXE3&#<;5F9rE$`@^'k6_`beq5)";m1gDLBC]E_QBbkV%W#(/X\:9572=
%_sWqmJAV2&h_qC&@ZpXTLR7i#^5+AHG%8Z_-X-c&.l1b,]L!5S&8bccfKV5Kd3[OK5SkRSl*5MZ33`Yu`2*85C2dL8F@]OV@/VEQ
%(hi?*=CVpp_H7>k^q@,s1Kia^[3\gQL?3O#H%d[\F&8,,<>/50l+t3#92#eUA^lUC'>=dQA6357UAEu7H*\)Ga!+3FVWS7'NJfMc
%?sc3ULnA'U"cK-k%93oa15csO+gDm+c/JFR&Z.Tb9MK7eM2[+G/qV4Zl?hl#`ZG\kFL=1?-U"qH!Q'[AcNi?-K5n1=aMm/>BT!p#
%`5lt+#,P%I&We)&(lYl"+V8dC(_jA-28EU1O]8puRIHuH)/QUG:KBh,[12=01cT2%GqVcho0%t%eU!QG'\ATt6HsOo@a.XN9[@O5
%LE#QB[N)c1L+1llg[R!=N25&J4,=C<="8>/(WD:4_O9[(^mD!T0Q!)A3m-XV,fm$_l"0rWAf,#=jrT_Aao+WM7eNTl%=C4R]/X`G
%!RTeB"$]IQTBB0s$<0YV(+-oo.IK(IYZ:jO5j0e)NT"XHQ+rsb>01m)_>obPr0>\bW173WUSR[<(38\FO/NK"FB^3Bk@pK2Hiotq
%aA6aacE$6\UT7S#/==B0JCFR@2S\1f:"B`3@dZ"CU^&]<Fu^6+89q'iKW;1:38=5e_Yu!U-?O\-qhii(`h@qPW&8D2kG8V.aD!3I
%23J$8%UHkk>hi;SkWWGL&@8?:R.L5VH!65"kfdFk0a'CJ!&<ccH!:JLD%jUOl)[lmbou\S3-uZ&GZ?%*4CjkI@4=Ch%`]VQe&g.\
%`):GbTlT]%Kt"JkJThe[!e)>Aqiu2-m7_"%K8I1h)p<EK>qrN5&I64M6^N7R'q+a<NTV6A#E"TS;s7[>,;*Y'+(QKU"bj^d_'qqb
%TX[B5JtO[`9Z#jHI5/qr`,o1U]5RRn>8aZgEfkZ=!.:X8Ji5,_k\RdQJPs+8Q:2[>fRC5aaah$N1>/UUPf.Z!%<)beOWbq<SLVSE
%a^>5$L>&ER:fWC.LWL[WeH-[)P82j5L^6=j;IVM;\+7'PoVBM6YSh`=OhenuDPQ5-LYFoFQCIC^(,Aid&rCK[=[(DF+cEIrTEd]D
%23HEq%m'4q/%-X9_Y6U8@3TBUWaqc?(58>`=(@NA0OSG9McI-#Cq41*Osk*''N_PXSu/E!F-Rh:Xe]_A*Z#4s.pB2J%6.;ISA6Cq
%Wg'i'N5dodkAA5Tc@/a#6#BGt\[CCp!Ynrt%*,r"+D\At\Jk[l/4>GN&ZKu;3!5Z*f\iJ7_a4`:,kpSP(Lh3k19p(M,<FN"\SkIn
%:/l>K&BQ+E*V5t\jGlpMC/DK#,8YZgJCcK;/8.gm&f_67gfluOnXK&o3=R"q?4lLm)lp$,<>u`5CMYt!.>b1;Ws!?a;g>V.PL"@?
%I4sSh,B\k9g'Ghb'1q(l,-1lD-1/>jH>uCM^dfVrmQqW"Ga-S],-^T*gY+u8FHa^P1\[s+(]N:2,4g+'+_QDCGheHG0jU%t>h_8>
%?B0,8EK>7-_qUMd$gMP^&-K0SNlWb&7.O=#]N3pd>225@K0p<`=FkOY)QHH%CC$l8bG[:)b+N\go;1B_TP\WW<N"-o8sKGoNGP^d
%n:BUGO8be_eu6jWb_q:EV&7Bms*6HaAf2(6c7onf73Y35;8:Y:8c32$'adr7/3Ilbp_PY(X,TQ(Zik3ZF(a(cLW?F#CRn`<@Z*NB
%BH[O#\S,h<7MTR6/)s2q#)&!oFqLAbme[B1^#7RWO_5aQ9'3>L/#:\!IBb*[Frf_&M7UoN.u,OC(!\t-LTrXdAf#ojT$,$6efr\,
%SD#8^kOTke\<pQbj9'kJbJT`V)Y=\Tgef8I?=Zb=G*9s]a_t7%0_W/,jkil2.d'RM'SU'rbre>DK$\63nsY6lLGhCU9a@)%ks]c'
%G*C<']&mQ)]:]\`J^'q((Tn:k?"+QT^%!n*-I%8//grA#AVI-af+&D!C_kItSnSWcZm%ALZ![R`O8NaT-^)LnH5]T/<R>:Mm)&Pe
%6Mc`A@nQmR6!@]>"rBXR;^:$:,*KobL_$gc><D.[:Y:9PS[b+ZiYo7,[]uRUb8Fb"D&8c!'L;+YD.gZI6kLcc_*hY_<eE<6g*U#u
%NM<\t/p9L)e'u=Ne"'Q%8T7DB4`el@@E>+(FOUVZ%CE(aFOj"11*o7LM?p!U.M?L%!tq(9s&*;.*KPak'.pOCB!rnF2ah/[jU#*f
%i@u`3lnack\u7B,Z5)X%T\(N"!/Ih_,%h/N(C0T#Ym(Df%1\?i+m#T,^A'h;LujB^)RX'q/u!CFc+/d6V9\,JH)?rGfQ2\.\rJ$'
%gFc:aBW`ek<u!SJ&bpp6FSq!Waf@^)%6TXsQ"Ohb'u)I!#k[)SH6+P[N=SijVd]q+&N;eQW:9"2E(qE+:!Hp7TEmeV:*&4K-e8B$
%](uWLF+ZedeVC9\^iCD.;>D&0ku%\oOQEjH)N(7dPLYa7BjG"TE+9Tt'>t"i;=Y8.QH9J%RX$Ut-Ii1&q)Cbl]N.>^?#U)m`iqH,
%Lhcqm@LA'L8h:W>;jJBU=[a?kH;`hhbsrtH":e"bpE^p]8WPc:S\Q8e"K+XP'E;mH<Rcd9aY"ctlrU8kTID94Yuqfs(ZcU">$tRX
%_aR^uBBb`>!\G3D]PPMOK*]W_-Glg+;8C=0bD"`IfS="fdO+)4ilO,S)7K2]>qANk:0$-N&^@"qG^6J;$*:n&@Ab>VkT]:<eU:l&
%eJ?]!f:nV@n?#L(mK?Cc7bop6pT=sPLe2>V+A8]Ll?!Dqa\.<(TUUc8O^k5EZO5s*<cnec]'irqZLoP]Ob:o%*$#W#rF(K^DJs.0
%>ubau6p8_r-[_*E3-*)tTuZCH*5"I/OC.7SW#D6.!Od5^;1>7aiNO\Qk+RE[O#A)E9B&L:bM!r7_j4'?L_BY[*5eA"E&<0kLlbP5
%AYM6'8N5qWqbV5j((mj4NriHl*%JifO=1AVjF3Ba4CZ,JXL>@Z&&VqX12HQQ3JO'nPED,?@/"ea"<pW_io!Yn.]U.lMD)k;[U9=r
%GG?6h_D>VK;>O*edU<s]9(t^E$\f-,]<Mf,=YtA`nF//AT88-AoI(bocnffQqq1S,CKK=H[jib1)fPn,40^D]o?-+gn4pnX11g-=
%WAak.ZOj$5<pX@6ITZ!$C1a5U>>9.4=&=Xl)2Doijl'f`$/a7A79VVD2Rb"\I;T6W$QL]C-bX7UM/@Mp%__Bj7&;7I);^1]f7mRi
%`jfBsE%8e@6kG/jf_%dX@uaC#pL;dN(%Xi<0PV`T.1XWs7h_@0<M"m6nQs4*X$rFJW\#bPR"-L@2mhu<Tq'd?7WM]F-&WbD:#$M;
%4gJMB@W]kl3hb(ojD@<R2*I1.=)2kkI2_gp7M.J>B!8k<=:5X78@mNgbMiMEGfJF@/!e`s:e391[1NA>l<DM9ehCo.0>e8)T#d.,
%3oZ19$*Su9.c`Q#;jQsqc)C^.`J^10Q=Y<1fD(s=b<uA1S6rKlEZVN/'orHBl#^9WEQZXgZXKac(4$fC;3Rja%9a^j90@;U('9:;
%Q&]8hR9dc5;ETS([3DggkYfAn_K=G'_m(NiD"!+RnZ`8R/Kcq'TSnJV/%A*Uhr$\p5*u?9Co*j_(Ao"kT::(C-E04_Y*r.+l?HVa
%H]6JPc/V=mlA%U8qVD0eC*@sRdV2Q/+)_o5+D*Z&*p#5=?8'AE>0'LrUDA0l%F&W2$B6;3+O17BP3*Sf;UGIhgDDqP&4G<,F[bF=
%W(Eq]A3EIg<2c%\,C$/6GrlSKXD\Tb?WqO34s40$:f4GW]<a;D"fs3=QUt[r=0)[!C+>L3\Aa(I\.qdT9mO$g;i-$"2Cu[KYlKZY
%n^%EAV3YFp1r]Q'/Ip9LR:'8\8)@VV#hP5\RS5M;:j/fT9SUbh4@eV=#qF)UlNF8Jm&ThO_g-?p(qrZR<.s<Y'BMYO7o5iQ1(^h;
%:?C2BaVZ<IhZU^W/*leO]!s(-L+XlD*-S\+'IF>FDGPW37M7jH=hp-@amSj\k+,d*k!<qp0#kV^+RYfsH>9p[L$$"7A@:UQ9h]XB
%%_%;<"6$q0)/8,UF'Tq0L`=FT9[M51pa3q;<p"f9j6b]<Le<=f=DN_ig:>4R!VR9h_<X&!9O]9H?uD'n]D/gjToBtR)GHk]?OSW-
%Z&Q7ULogN]aMPX@d+L79XB'/]BWs+H_hc83o%C!q7kbi#L[*FB5XpBm9#l<cHp5q4+G3^_LF;+Q:1PsAgE`MeJQkn&_!s>2@DSG.
%&MC!a.kjH%3Q?'")S-_PQ"gf]LRuG9AjDGqL(HRR)t>M;_Dk.qIf_eRQ%u\Y>);I[,c4stORNQ=iQPeuMtgt<<_9_KQ<a_$"IGq9
%d)qYV#[\1WM.b+q$,A`1d7"KWU'0HgaA7WYlG95E\2q'PfF[7g&eBc0%PicLhID]O1a.Z9bn/[aP&/>llQ:=$T5Kj8dX+E!j%`1b
%O0&kd/G,gZDQin!Y(%1gd,rLZ:R!nM04_?IV62ou:R9dXaKpU/3*XhL$uO@FBokC:JoQsT3mo9$GRi>9M7-W-96RQn`>W9,22a9%
%::=QJDrVaO2En4M\/FfU/"_Eag!__jiKH\be>A[uQlP?M2egAm0/\i+2L'OGq.%jd0Y8ENMh6VL=N'jt)1ugT#SJs,LE_=(W@iuh
%"#GiHLHmicQ4:B<TXb!?A4]0R'5bf<I4A5skA,([73qX/?W`D:JX%ZG#'Km>6JLlE'O5lA2[$^1(t_anLHG<5p#3n-FXFt%b<`/L
%@l+cg!'t7'eXSB=%o^OS]Ag*L<`Kb`?4ZXpX7V^04iUpU8p:r,$kEiH"ep8sP(M$"UfQ4)-=8r--).[V#kk+?VV2-&G?78%Oo$WL
%&MhHG$>p.9g,/ha5_WnkbP@TL4u'8b2"g2An.=[fXOa0n_%3=1>+!]91h.:%O[9qQR$uT&&@5s]Rq"Ej*SNV:%%FOY"dbp,O_len
%rDVt@/Ri3K5o;(A^u.3VnoPI5*J:<C$m,!cWMJIjAlF"W7Nea-M1=+P/3^#TTrp_=AE%#n=Z`^p,W(&fj*_Y0G(ae$,hI.(CE!Y`
%CU>"?,d'KjjCAH0;$_fbXZIU4Fh$qgTOeH4Br0t#:@&.ah5hNDQ@[QJm@+;ri6a<!M`An]8[lJrW9heBKdjW1oO(Lr3MJ'[_pb(a
%c`tMtM;F'KfX-I/8_cV@0J?(0-XmV%\)<pc()C\n)\s8$^n>G93%Jt#'f*)BhmT!1r:<IQ:c\*R8F%PR0qbO)K0otD:?f>u-"u9E
%.orr1K32nk-XstFMMs7oJLD\T.rp_WR\]r$Y'N37H&8Ks*GoI5CCPuqKb*5)XUhP50M3i`<"D.;a\0QK/_[g29GaKDh^Q6a7B4d^
%0KS_IiCL?dqLEsufZ0859u;f46$!<q=[bSD1CX!\+,OP-7r&[AY</S%^I@H'Wad,*rT"R'B2<mCKF+q`9>m'HSd!q#;58snQpLQ\
%GW?@[Sg'@q8ko`K,CRGm0c&02aIH`3_Nt!IW0&I%1O'4Y@t69QCmmt8j=6@bTZt9/B9XBKG=iZU2po=a6'G+`Xp<8fHTk/G_+):M
%.XE]=,RM+<CXrW9QVn:ASk[?Z/Ge3P]gO`6OmHfSg+<dJZ"ZP6BLJ@2777K7Q'V\1%YdROTH]h5O*U(P5ee$DVRTR9i0BVo'eP5!
%.o8@I4Qn8Yq(X53TM/<q*&S<7gr_,gX`2]%TEd+S1Dg71Z(&[hp=tl[H<0hu+SaIMKE^M/=Tj<BoUKU[hI:[6]4+V[_;l$['=,Y/
%h*M3_V$GPFU0q>&b>_dp2$EXAXa(mI^:W?Q.5k;nl[$:RU.cr'Q&^%4a`q&H\IlNTbdQ#0+$#.BM.0DWPIOd*K.dW2DG<osW^>oP
%GLM:ANcLl#D7jm_0nU"B6$+j&2(DC<flk3]U<kh^6)i"M!Oc*6hkLG5*=,@^B-Iprl;XHq*$gD]ej6F6j?)&e7-L,3FN,T^krDTg
%m:rNmUj=aWD0a9of;KR-il`4:U-S9XgCpEeHdW(EG%^-W&-=@C"$Y(;f^(0U7@i@QdoIEajf@)Q';]VnOr4%1miE=:]qT/I1f..H
%m(,ppP*oI\A"l.i(2H)NTX&BS>:?'/Maiabq"s9?6\IDk>bY`$i1P8t%P?onU9b<7CK*#1\%X@)%i6QbUk[H.k<]ikf<XP5BQ8o)
%PdA\<!FiBhd5B$OZDe^$%u2L8C@90^'s-+,"-1Y5Y@(A]SX?T]I6.N'Dq9cl2sWl;F5/3gQ)O*c*=X9_A&=sA_A!F3n5+?rlVXpo
%;M/)d,+HQ<2%Ma;$:cj2">Mie/X%?6%8\Z,Ph(1>N+i`[M@;<'@F-SQ(rQ+NT`]L05-eV5kS.XIWj/Mq(c`9InrD]7)iK:"C/9eE
%,i?g;CJ['^R:obCf`?+E9Q-Xu]#;M/b,TuKVAdD%YcaC!+(]bbN':882EF=F,I%h7(*-q7eD)7"*(-5NrI_i.8:s30Z#;GK/El/M
%YY5II2W%0"l`&*V0%30t%(=P4A:j"\QA<U35q,0dO9m*<Ai=&D^*QH,1,W`JpFq&IJao)j#U4)5U?>56?GmEk$_sAB(^pIF_gpd7
%C^'q&4BVrD^2/ou)rc9fXCoIpj5TeIKoD"K$^N"6$.DQK\8%LROmo+?.V;s?lDhD5f$;J2J2kJ-SY;)IILVKJ.<C(A,*FaB%1c2L
%AmGh$d8qo#Q7sr?0hr^jWCGL=Bu<?g=9[bT-'nd]oJ-q_jn1&c1&@n=e1CGk%uhV;#GE)"Ppg`1&U&=5b<-[gTBo6>X<C:*AdRfN
%;h1:)-u,CABaXTSlka7!k`hTFcm*;/;^sToP7g#LRZW,_`QV/JqWFEh+Q*;0,KS]GSj3gajR)i4RCB^*Qq6po,*F8!Chm>9Mbn/s
%gm_t8Bp8tJ;gpf$Vgt(&(R8=(<!/9#lHoq`JYBp7iIGTpU_"BsE/705,MRqI@bJ8iZ8^0HUM(U=([2k?T6/;td:@!6!l_Dr:f<QT
%p&RY(CCDHbcO8^Xd7b:')Ef5G1E5;!QkHhr?TQXBe80SV]\&#(ZtbOp$"R`rHNh;WNE&;9?SXGS"i^!=9PKB8E>Y.[p8VL<DAMkY
%#@K^-qoPZUVj%E!DhRgK8ah;fj"laJJ<KVZP#odumk\&7oIcAUY>+qLP&G@eM^/[@^2+45X`H7XkiM0T#&bm?bq$[J"])PK#=m]c
%4mPYs5T%@dNF]n[F5q3c4bg3]8.F\P2qNoS3';es>6^=UkV=-J!=P_dAIP5fZ-3TAVB'$da$)BkiG3\jVi2COF05k?C#i_lRg;HE
%:^.8Z7$&FCH'tOElO;S0_fRl!&O]m0*&Ao)ZZM9bIiCp'P-'H&c%&[.0le)KfnTrcK.@MKQ!uQK`\f"_To$MrI:Rlu.VS42&>M"$
%J-8X(`cD_$9%GfgK-2#>-Ob+f*$cdf/OT"2%<.84U\#r_ZZ-B`LK@mh;O:K)$;WRAYgCdGS*W*DG(@2:4pG72JRbZ)?]T;1_TXbS
%AIOOH)U0mH-Rf7XU3L7t#Fd'-/"1^^$gc3j=nPL.j2I(<.\;i(Z(<oU'GDi*EK%rt\*.\4Dhuf#&o?\UHj2T'j8`"@JmSieJ,$*<
%"(@[$Xl1Wa\1Y%IL(@G[^1GST_SS$>V(fpu%7"F1RDhZ#=h4r36>jnrB:-"RcBriKGX]IO3POS@17:Nt8(#f6Mb$,J*bOk+GrSr;
%MWR2M6lgDCj4P"#J5-1^%s%(?H9q?4W,Itq%V2!1,&>dm=r,FRU5ld^0N\i[aXfh/5QYL.$4r^gL.ff.)'&5A%sS0uLP5O\Gf/HP
%:,9I)PemUa>Ai^n(?$X/R"QV^X9[a1Mc,)ten*gJ,ifAqL=\-<akKR!]X/T/BTeU'0o6H,qXe_b\h:8*A8W0K?b/XN\Jiru?9(&D
%F(Ud^>s%5lEU;=4JQ_"K\t-H(PS?sMjR\&K>jVq5ed4339ff=g?51<1lpa:@8fK3j]:,qW*)9?8^t]RQ:s=6G7b!C%4q6-^L@d4.
%-JN=)Ni"R5P$KMNH0U>C`Z?t]d-[:pTX#XIWsr?CCQ3<I^!>;1n@1,C1@fV1VW'%[O!^8mLdYipJ.55$:95du,g*6'Y'H!b*kAFM
%a1%;H;d:*\['@\V=?(3Z["U@(('5qpbf4Ae``m\JM4sG)TVqs*+i2J#J>WF.,dUE;c9G5r\qMd!PbQ9"U71`%(8oj_,d*k^8B=?K
%lIkgN..1=l-OaKudDDkR1c1D#YZ<OZ/<EMAIL>?oe[EirRia(.F:Epc%go7?Ob*"S4m&6Z[_W(2q\L++2JAPqW0<(\I9pl1qO
%MbGpCM&)=)#$4D,d5jctiHZ]/c"]Ln["8QW?fYpM1,;%W&GBg1;F?9T.d,WdRMFEj7Ik-e,-o\G/4shGb7K'(n7t\8;coUR4u@1<
%1*o#2SYOA5k29,TZCm%p-n_lIS6sn7Q8NCG?dTi-Zui<9;)X>Y`ABMVPP7#p31,:q*!Vcual7.cB0`F0>tg%(5U8e8I2Tk-1l@D-
%2Z0;p+pIMhA]HIL=>lf_MI^OGea212e;tE^N<[R*eTHh$6q4"^&42Wj1+8b@$$)g=_S:RO^.^2geBIo)0a_PO:SYP*Fs1IPLZ*\4
%k,Ci%?7o^aY(YUSVmJG]]*(U(k;eG@T<^G5YIdTU!:qmGG-(1.-MRd4/gM_EAm$VG^WDB4_*i^B;[_+fFHQB0&0Fg;@dYW4hse*r
%TLdC\p1#`Z-T6NAX1bR[5?(:'k`Y*)WQfJRFMZd0F,DJ^!sjuGi>75BTUok96-3bRC<>AW7(JojS\kRLVN.KZ"T;-lJ.-WEfo\"$
%[i.LSH/&"gknIn=:$&IY17M.3a1,]\3(e2dBl4n#S)djaITafPD&u3ljc[Of6/snSqH[!s@0R*'j-t_^f]J>n1s4i^<=4nbe`bkV
%r!72Q>2_/41O'OER=328Cr*h8?:g>G+D5>KE#"/HU2L/uoBKi$[Eh]PkXQHI6^Qj6m!'+T)b,%[+<@?KVr:pAMTI-Tq5o;-n=7&-
%d+uQQbA+?i7;o/$(3,GrfSL4@XaiFbFMb;Rj8e0-EDFtoh^1#:U2O6=c.t-b\7[nen.eR<U6.iliBsERH;T<e`J*-S_D*JE@+04%
%ZK'KMH.!HWqo$qOaWa4+81Vr#J_cMtU&h:KpA&5jhXDi'jtP>M'^WFBH0^9^YjNp"b9GW<&6Qo.K[K7f8bp_5OYdVr"A*!a]bpZ8
%\(?b0+KRt"'Q5$La[!PokE@3VEf-&\YGJpm3nGW]2\UflbG+OJP7m(2*`^so8IgX"dn4^J*FSkI&K3C9KB/uU]XU@ebC0)_b!+#"
%NJ48FPOW]@5^^F?^8n&T(%;d8N4m%`-MY$OGr$@_;bf6n-!&8rqeAbO@EBLC\J3RK8D3"QA/F<GhLpSU;//:#BQ8,@=u>6E1DXcZ
%$cfY1;-f]G%p?&?"=5=j4E9RNT=iV,(.W"p,q`AEVG%;U-AOH*^>gL"([9,?2t`6/%=#?sHI/!Q<(i@$M8!eP;u^OBlZ'1EHI/!Q
%1eiILOhPXX;ub[<U,0:'J1p-0-G#eg([4lIHhEGs4^2)K9b7*M"LHn7m\MS+hUFcY-AA[E36Wm!d9BGch=-2sY'Gk>em+\d"UD*(
%P*ge?T3`5S4=pG`NehQEaBQo&9jr@u5c1R'LQSO$)K>f^6o:O5cM-!Vkc/qI.#uC7D&&$N)^p_;6F4Y#O+XC8*5eYT.etr7:UJR%
%*bpJC+dj-qe8pI1lM8?/Wm&SW2bf5#H;i@*3n:S$lC`'a`lolTJ)E_UXJ'L"\3u[02GF_YVJ/U8]kJB*Ikbf0\3u[02GF_YVJ4A]
%R[^0;8"+=a\4ifH2GF_YVJ4'Xag,X;J0T]X2K=R;lIe!Wk^Q\ndH7so"[1?h@5&sR5;H51[Y,Cs8NP;>%P9"=8^4.@/WS,Ls#/4$
%gF`a]P(I/bi<F_PiC5tskbB25)GLr0DjsL<e))uFk_4GD"3JGHN2R'[X;ClsTQfQFZ1Dj;1q#2:)@i&3U24Jhkn51]]cmTTH/Xi[
%K(@;-m_%VZM^)b0O<k1`(_3Z6-Z`ZR0%&@jal9mB7`"4bPO;:gL>>%cV@7O1;1ak)OV2K)(D_-&>b\2!/)SOh7#IEXY,uF6]d0!-
%NZskB_C1Z:=`#+^Q/[\LS]6SMSWS9%[dm.b:YX\t(Q5-cH7"3S#^sB:l!gp/jhUW;[@U+APtfi\FDSWaFDI==V+bfZ>Unm(XlaGj
%3a)7gY1'uRW7.4`I-Sg"qWQ%uo7_gTXRce2'"V)nWtR]25pV,N]-+3-W5LQ<11gal\"C+"+o8("UW#qT1["HQPA=8<U>LrmY6(en
%m)KeJ8jp?F!H"osIA+h'9A=VfkaP>-(,qeCGhqt5<!d"G*R;VTe<1R_*F6C[I@3D_ffSYW?$4Z0J@fW%S3$haX)?.$5:e_/%S1[Y
%2G(#Tfd,*M$B$Cl\N80&NX4^gG$C\>=HomnK[DUdZh@"e]*4SfV."&'-jo*>)D[lZek'<aRTt_fF/k\V'*/[,lm,(,Hc1;0FIhU%
%V8u*QqtU'/ipEG(>4a[>SgAN&U<):Re+=;sr^g_Y3jRb>?bJj=g(q;hS@T6Q5\:KsZB6m@7GpWl<)jJg(.DRE&OVCU/Fal)h!!j1
%(l>%j_hB"F%5ctD>(qHnF)'Z=/WslPhn:dm/</s"R\Gl,EfWDpQIHY>/X6LN7#DchMD%h\%*s!O0(fhOL[2,VfElO<?7\j*Z]-if
%X(Ai<4U-DB`uWQ9W]2\kK'<>._$/dXWG.OrYf:kiFMUR8IFC09SC;lI'Yd(2Jd0^%67uK"f?B]BE9qZo3*jceM9@htLC9l/KEEPb
%f.ju:aN@=:N@3\pCc,0n'p455o(R;]Y[<s(Ie)4%R_97i#ThHEB$Q;R(+C4W4h'RR2*ioO+Fr(@qTh)/in7AI@O,UB*2S+i505GE
%;+=,1;okK-J;FA&+:O0L"NLt(dh^PjoL/_3%4i`r-s)rYRPEP0H%%&6%SA:daafjF:f<N^N0BFqigR6S=h1LPiCfZ[J%n,MkaL).
%(>Tp^*W%+n(WLuCk]B`4e`6=m/\FGq:\:SR*Mg]F@4`a<:Y)G`7(:GL2N'J,RoH!%pB2X8P>p)n>cb6#-hjDX8XOe?&q`o<pA&1l
%D2[uobOL.G^""a"7H=[ud!T>k<f!8M)MsVEQ`!dQ@F$abfTfi#lroq6Onq[-@acQAeY=.S"G0+5?$\EVrJ[<n@Dpo?=-Pqt"XK$:
%cniO3%VD\a1V#ieS%:p&U!Tu.%1T'__%jeoBUTm)Pd887kf!Wk@mup1?B\@Wh1bQWWA*k)>V5[1Q."dfS?=;mfN=`8W2=CH-XsD4
%^+<Tf=o:Wm`65M:/cs?6<'uYJ.MH]_XIeo0aGOSdr12n0+sPjJ@4h>[GaH#MmqAnc_*0$,Q28Y[m%X66o5-Zb%!pSd,7^+)ibYPU
%Bap&##qTqk'7F<Yh"G:HUrG5Cr#jWKhitM?2%Y#,Mm&g6p1,Ac6(BTqT;%`0?bh>_@oTT%Q1O3I%C,,dY.F.-lX@(ES-ss*`\jX+
%24HP:PES7,ii&c/+XR1u9FM[J$j&bS>-nZ8B)4WKUF2DlCMPs0.be6@e*u'[4QM:dg.Ct#A-%fJEe.`!d5d8S\Xp0_/8-eq-<WRg
%`I!<WUAgD!l*Kd3kdKU7=tSR/=d79H;Y[O3%-[,*r;n!pifE2*IW1EZ(4_V"0^/*FDHaEmTU/`#bfVf/QE#J<KrB0EMKj-!7LNt$
%)J'1WXT-bXjuKefe)K.#8[OKH#E8>ac7"E!M9`*b"8&b]T!U:oK+\4c8kT9VO[@Klc/Q^[js4IpM_`*#&BP,]fTK^XDFl0A'>Y/3
%63_,5-"@'k["Dc[$EKBn-6js\"Y(kB;QbHVC`6>TPJjA8WGJSGoB+[o"HA7nWuI;&F!&b)iU)6*&S%kC@89'5W\t1S-#cHj^?hgV
%9j`E2YmG$]I;k(dTnnms5??C3e*H>A%`47@c;t;c'AukYR%*MdKVQg)U0DHh'U;$'ZErt15pm7p\ji5M_pf6MN&mZ;.2#%$Hn4B8
%'g.$*GKAHHI0dZ,T[_`a*.qbRJ6VA`._T;t1&h*=%beF=PAiTWoM%JFS>hkV3cYS'E<Te49F*rZN8@8s8J/6:Y60&8L:n<r?9oeu
%_IJ584$!=&'9cm.-QnRaEg>Zk6?hfT!JHUsL6Z1."L!h\]'mNP[\WR>n3k`Z?!EN!N.ae`.6+]L-[&8p`l/6Z#p:^!,mH_T$(1RG
%^k`[^9'<HrE6;n@Dc*#oROa8g$p`BuaZMBY;^[5GTgW394`dJ+>d5_kjV55CEuMCE>UYVef5NDXcU;+q_pRHi](BMeafMr_G#?AX
%P6PafH7:VIJ>r6]p)<X2S?8WM2J#1H4#=?jM,$2OSW'(EB77eMW0nu6<=Sd[DLL#UM7u!t7`O\(dtt3BS#-F=f`Z-*]^[=)]k^\P
%@H0iEK&/$bHq$U5n*&5S%W^J,mPgA-kQcm8[]aRTIsl_Ah7%n1jjSGBT"f\*eDp-b^3I>es6qtN?U&u3r9LEYUW;ffJ-`.3rC(WP
%EEE.^;>K&Bpoj]$5Q*+Zrc4:,IWkCFs6Qo&L+iMn=4"&PBA7RCU;'%XKqDViE-,3:7_W)hPNR<lGB4M2`)4A:dCio&s%jiY`+4mK
%gjkoN5_4N6fjWSH@%[etYGaBf.Su2Q:](6;mdH`FIgRA"6.pdc/,MWBqo*XAI\IMPRu([l,[r:&+;(m:5n:iQ`qbhL&25=Ff1b[D
%-K<Fg&*L%E_DBW+@4Zut2uj0OF[ihr2i$=A+:fh6WT6W5lA.ut7Tp8^D<75"Gal5V4KOAL;JmLs8fLMWjrUM@g_h6RQ-_OrQq-_q
%a3e@ZE@WU/>Ku57iL8:!9Q\\uq"`EN4C#2'1&OZK$1n1;"Z;#hbT73-7&QPc6H!_27(SjXWXW:+,Z3^"9)t!b.!3?(e&)=Sj-QW&
%/:''ZPb0K6!@`!t[E(%#2EmuuSJ#%DnS39UM5c)Ue^8k8^$qbXi2*siDXU7G/G,L]46@"20krjIF?E`Oc&hf:L^:Zd3o*4cLnVpf
%VmU.V%MYCKc01Y?2(1j$BX)/Y922E4,id#CJ;ti,-8e>8eQ!V7=j%p[Ti=:h_<2`60$#a*Q9ohqeCpob9Tf\oMbimVPXWm/i"<T?
%)^<A'#`R*jIZ6A4NAk2`i)ud%HfL-a^(@+Oe"4R;8:n)R\S$`_>mHOsG>':B"qu"&$;jn2')PW16%=-#KVD/I&COod/;orb/6-mr
%_co1`Q-i^N7*GdC27pl/'>lt#*&[&&bFGMg#?+\,hQ[fq&7_^1,\9s&OdAZh<@,sfEUk(q]\ZlZo/9\[jt!2S><^`?#ma<B@O^d!
%V#(ZueILSN6E,=gPE,^iUqdQHYE[h0#pGjH&rC9t+W@&QKnd_R&^?7<F;2?6(OD1)HD\.qcPO7mJ;Mh`c:U\c$CN*4j]WgR7]73O
%4o7R582+S'3(N?S*CrRl=d&IK_^JRW_d5*Qd.<J`L#8<Z6R(O7(t9I809?G7(s@^TQ7-IE[pG:O0u$!BS:_14R+X>,1;QbC*0?:=
%0q^_IS`'1PSKs]=/=D+"Imp7gj5'12s"^28Kt<ho(X,:6PusgqT!-I&Bak3_?ofXV&0bkc>Q_1_D&:4GP%9@?JhWc?8U7nl1"0qb
%:-3ZtF@sbIc]auh/"n&A=FKO;]/>X=.joJ;m?n*aVdqIAl>;Nk,YWhb_+`q$'3uqea0Hji,LIqDdbUS:1=;@(l424f`)3iO>-g`B
%J?45aY9gbpn)`oD0kjA?brNNsOpb'hEB5;d6HD989SC566B#%`2*G3EdYS>F0ireUo0V)O@E_=$JV7nG?;*+'Ob7>(:=o9s.@id0
%Ls@Dd,(e/bPn6Go,+10ABGa[F[k&']iJ]V9*(_uV'=q45CNZA*F)0/0^#Ijmehn1NU[8GhUfJgo)U64=!C]8DipXF4_U9;o&AG=D
%L<bCD*MtY@6l^e<]j.6_OX+psN+M2492[\S0ld_cDZW/fKM[+_*r+*!/T3!$:?O=Q-"&8QT[*8RkiemUHF0].hXu/[H+=Qk`6'$:
%>OuQHe.qco]&DQ$B'rAhb.qgHT`mOmXeRCd=n0_C8n2H`\N4%7]mF>MPZ=--Q0";O<OCOo:A5e3R8;NbJ7NO+Q"9:c7C_gZD!j,?
%&stCD'f%D687SuQ[OI4]Y]m&O[Uuho7h:gIMY;#>ia>3/1DUWG+M4ZD,!@j'GfC9;7-#n=g-ERV>L2I7fsQJJaaGO)n$;[AlTsV]
%_!mPN0<l_cR;:5,Di[e(+8(LL3`eq?Se0:%2s--_ka*f,R)u]^Jr8D=:UO(%Hm7!e3lXlcW2GMOX^5f$rL=6c_o[8!-'_!*ql'cd
%QI<Ylk=(dt1MI3,;^-o-F0upi$1uO,7d^@^N1#L*8c;@4cnn/2\R,%s)o[VF$LV&ZHO>V.gS:]jJOIXMb<lrN"4;2L+#8MRYYT*>
%(@.ur<$J(1PV"65!EYJJ.?'mTSln^(*1\QIQN$Jlb.tJ<,)(oGG,I3E=cLrW&:?H:R<<]Q&2B#DK&D&a7a5H&)'2F,+bFbmq%d]8
%\cbA>?fs"Iq<F#&STNQjgb0)IA0S8&Y%u6q;j=#8.o)l!B#@!@calYYldW33S":iBNQQCtI,3@;0bl$IOTPa_6:Cg^h&8Q)d<eMZ
%h;/N&7C$_YT,Q-5X4Y),A0p/6Bgp&G)8=UYYfeT@Q@^-F(`UZmPn.jXnnF&jNX^sV7d#?*l==A4bs9+:9Vh.hSq<Q3)PUn;@WaqF
%<7QpYIRjdR5>ZiR2QW6Vi?6AkaV.(\=Ih-8A^?`ZW@$S]+iUEm_BmTL_&#HMOjG7FCr-8DrffO;2HBn`MA)OgO\@=3^kS@Ff0OSZ
%U"`m=qim\4%.Bt[;t_J7B%RV[:JsY&,.:UT#D$CD\Ha=dA)<jGKJSMML/nA6P$+G/@tbUMiiB5/8X@B-9B$$_Q_I.LTOpaT!t#QS
%DTrK'/IWBT;_-V6UHDJ&4Co<u%0XCQ3m2l;fLIhl)N7/F)u89iMdpOJTUJi7aNG3=7?h72<odIjQl\Z`O.8Xtf2QZLRcN.5@a-A&
%#IuQpelltfYjYTDRN,\(4q(:2o?EEqR[?h2Q-n\!8HP+i@b$6D-s)lcI4/T3CFMm-[>B^jlEph)8!g)anLtnBWENHGf\rmeG8RM2
%)$rk?lGZK'U0.,p=3D>8Kr'Urbh"GN!82r88Z"@J&Q;FY\TO)d9fSnI/^dU,^>^_n9tDYaO@MODmX>md4D0Z/I>M+.'LOkuEI8bp
%=4pW4$pua2.rgr:GTk5T6t4cafZLKd(\rl)ca%1Tg;dM6P<W,SZX1\F$9X_/WqJKB!>IuR*`W3\kN0MLSA80];/%IamFF@7heJCm
%Zq\5-l?=rHFst[INM6Sf!ojjdOBBAAm$9!]6ZhU*5e7dLOQ5WR=W!2BLIPrD,j[/t-S_j0n!n0*j!HDPP)DGmMG*EkK6jPm0>$H,
%WReI&f6QoF.9<Rh-_h]RBtR`</WZ;MlnE-ob$AUXSP?J%1L^G,An2<VL`uYseTTKBBUZ%6_s0W\-Qs6eqGc!9HpIoL;k;c3"gKJT
%+8D,?J;-`RkX^l1T8CB[]I8X['I?4B##4bZdl%hSr'^1"53Gf_B6Df^X[KR1E`O2EHGlJ/QA?^B("TAF@..55U"J`aj:nP?jT:J[
%Nh*:P$EKm].c..6Oi0LI%8Kuq*1'g.C,I@.\*7ph.ZffVT:ZQY7FuO.;C\KkKoL=90l-LC_-#&b1h.fmMd^oc7S.gHbJPq^0iY8`
%kk/%F=%i3cK33Db2(ZAT$tNV06WTs[USrAHc#BkN%LY\tX4WEniq8fc^1K)lPj9UKRI^,]]?FjA)U?@p*(9kjL`Cl29jah28)`IX
%`8&4r8t!rY.Ci?>*]@hmY#./;bEB25*i3l)Z%k5=*`\=nHhQ.s4/c+(`]$S.N'Ba,':i)D->)3EQmYjgT)sW>_4JNBTU>:T]#qrg
%U]q/);PKKb6;3T99].q.,O.mW9h3%L1M<e`E:11)%T2R-HsIn>9?tV;'q"*pWL;<fi*<?S,*6uuH(8`bbjr<'@]m;X1qab7&iSf_
%=)^9%//6^5_al1(,*EOVSDBnWkZ,YYm8[JEPQ$Cj/]YOgA2,QF<WckiZ0,%3HX:lq7:a+^CUdb?A]_h[Kc**td$"a$Lu4QZYKW";
%X?gJC:R89.QR>ADEqKoZ[UHr1b]N3tW[_[<mVVK)]H/CG9R1@cB8F3D.%R]%VNo[>N&+&UlZ.f$<8_L&D2.Ldj2Pi>EN&0UE*ZJ6
%1ep<[BBj"<Ha2b,PK`7kdp;8p#U3V`m]8N20F4AkPDhSeCMe(tB2s;?2'k,0GsArqL"g!]S%5"A?7%+#REhl\GdR7&d!u-P4kon%
%qF6TC9tEe!Z4O(cY)0QEXsreM!@CY`9a"$(X(PRsfX-YlW>E1Zq_TU6i]`757G,O/V2M<89D%&2dKq22?q_Eb'NEH`g!\`+=nBaC
%<9X@!CdMMp8/*U0e"&PKJLds[*IU$7M\"&M0dK&%=KX<S)mVnY6c:%spleDo9R:kg>tes@0ZHePNS&*jj.[Wi;hB:l4-k";%tj`L
%PgO;_2SXVmHCCYN;2@=]$E[mkL_j_E4n%#O6nT.Q+,Pl=M/oT??Hq&77@T7QGj+.Z=%3ur=;atE9P</pl+FM*J+ha#\'j;-'>?E`
%s1fQUp'4ZAmRulg0&)@,cjMCK[g%J95d;_qD(_8,Q=]`ms"]lf1:aC@Z;j)De/?SGW)0(5F,c.O#ptNOJQ>[/1_\E7;^O->6XPL3
%jBAfR&IRRI6t2j*>QLqeN^;m73Ze)SAWh<jq4,+7cVm)VK^Zku2agR=QpJA@J9?&I<>$ShpX89BhhdqA(SZ[\Nifia</7ZSOhS,L
%(IjMeD(%eL_hf4&IWQYR*1P';"Ci=Dr[3/A09'jT-"W0k^?,;6=!A67>fgZnIdOt+%HfmLAqHDM0XpphQkq>F$A\o.31+330!?_O
%<E^Tb0OJqFg%qd<&K'b!"VV",pM=V3-&?VB0;bA%6`Q?>,ePL!3.7[)Lm1m!'k5Vj.4%tWXS7$i[;:L7q"\lt(sV!6;%G7n?qU'Y
%^7b*);-g)&YV^/@(mnVJbBm#AnKH`Vp!++\9G$BVCR>sjbIn$!nH_`gUA#6fA[+YI@3N@j*eLVI-;LDt8%rYQTQ""h7[=qKLsWg;
%h.aqR]fo!i]0o^I=qhu30mGF-OsN"UaZ::@,g@HP^>hR*Ya(rWGg.\@UV8C@X:&#R^.a>=m'2E]N/0ir<$E9B?D.TOD_&[6Q)J\2
%FSFn]]/PDRNU056qZj11Q_;5=)J@,8a\LO=D@O!"+&.B69eZ8llbPDo[;,3o;MV;<+.N.;=GM*uEuQ0b.m!G?,FN:mOU*E\C:G=S
%9M1qpj(K<E9VN02K`sdP57^Od\!AL2^du@g?'e%,YfA#/0JH@H]Vp+@9N7lS6un7!64_2SV65bWH@Y0'H\W[1N8`<-/S6hR-GtsO
%$YLX3]K4p^UtYKm/0Z>(lB*Q'&@UmdU.K?ub7"WO/Zm_oEk]j@&EHp.%4-P5._cQiS3)Nm?WbM9P>GCB?V)b0kn"i;KUOGOU/eaD
%(Aj3dO?:=CUk$+:9.Y9$Zc&iX1'RdgP*Y\W/Qt2$pg_hSg1[bS=l;-D#F-[c/0$)Hgmu`XKeqDS&eG<e>m7iN5YG7aJ5ALG>SG%Z
%LYu01X>'qQJBG@$L/=%Vi0MVU&9!\oHcO/)`';I4>-Y@*U;X-1Y!o-"T`HB'7c=q0hqX/DkJD0Vi+[O6<^FYkF4f,ho&ZP(it?9+
%.V7-ii,KeFLpsS'H95;NlEFcS%D*WThObd"m6./DZUOD<FnJr@&Z7<e49l%BQ=\]m^3E;fOOo"gFEgQKhOu>-;7K@-@i>XVeqJrZ
%JV;;CIa@=!T#>$/\k!0pPKrl?d-V!3.O0%Kj?ege54NgB;lj\2`C.^5^>@%)LD;"H]GhM=9s2U;6qOI&U0/YL/M&PjgIYXs]etI'
%3^$7_UQVL%3U&+^ckS.(M7qaXM5pW0$SaXT\k"HPkX#0_>f6`:dkgm:(iCuJJq=b(oG1a!?>^4A6p/_H1V`\]V6NH[#-m"?+2HBM
%1lWYMFBQFa:%W>N@:GKH@`[\/,F7klBH1c?>>EhoXgUDq#C!m?#=<Q?Z5U(FH+SHWIVa*`<<Yhk'd?fLKID9S-E>`GbbD_1oPDsJ
%)ETqZdaVjNiU0-7SB_^pNX@i!X;Z]>R!u(.St`q=c@T61EKR^X_'d@<l6EG8<$!$rXn-Z#.a@#'@I]:k)BtCO%>S"cUr4&@N3KGZ
%'Ku3kPG6&$\2@kWVFeFIR<9ID)6KDg==uo&0MnGZOZg9%U(C2i-PIdU1Qt-0(CehNoYD\q=F-&2jg6k&&A8<'_HQ`MfPJI)@(&O?
%3"_Q26#T)S4Dgc_V@=,+ojskDX1UtWYOHT=[na$D7.d^BA8c+R'BFAWqG_BCVCGUfF;66O0Lr:8V?F#naP<8UU,E5CH-eRA6^GS\
%LgK@c>nGCb*c:0T2IemLqgCn+2+hYWRh4anffs8qNf?!8$-UcnV5@](USq6V<8$ht-saQEl%K1DTs&OP*Y2K@^.eD%D-"5;4$%U9
%R@#[lj>"9Dc?$JWF*3KEcj@RT7&/+r6N8h!1u()bYEj6QkFg%?f"8NlV8atu<Og0SAM8G)F20hm-D_8V*J[t\4u$FJ(iTrRa3L]j
%4UMunq`ZYd'n)/P-)0_J&^_g@73dYF=@GNs%$r<oksC_",e@SOU>h((+S+t!1!HR-NT[aL>D@h9AQ+sMK8PK.!DQ4$n;aG,:Ks+>
%P$i9bDQ]>!^+DPi5h0>pr64r898:V&l>U[[`c,\!go<!s%[TZR\A]@2915RdF9K\u&`uig5_ebPVT5La77@t=WoDSu$!1R?Ob4K`
%&&.@hLQUWJ^gNG<5E#316bT/X9mIfP?nu>V\2\)+C2)*]T'tfS0PP\^NZgfu[WF;Wg`+,2AbCHVX(#k+XZfuE%S:+tF=nsHX%g56
%<2`:Qde!r!X'_\qK@1DO_cGd(r"g"YQ*2L3X9XE,3FQn'3Al1DSEC,h0dg'P4sHbCO8"UtG\2iI*),].`;74,WKA7oqqY$$XW]e<
%FAO:#>ZPX^cY40niQX=-*rjg]DH<ROh+9#^i;qr(TUq&tf2`P)%"6SDdbLL^3Is[7U0\]]Rb5]'8I$HN]Nnk/bj&s5lQa=lfgJ6p
%/^5>WK7's]L<n+YV0VGD4&c#3\LdQ\7'6\\5g534c`R^CdX-e;_U:dfI>8Y<b6[@5a_O.J-7F]eh\kS^q4?k@8T5)DR\o)RL"nd%
%H_PANYH)l39B8-E=<\U(=i??`AAfA_@W.#*/pYoo*4Pt\'n:b*#IB-s?&eXWCc"-&W<T,Q\e(0hnR0YjnUr(Bg.Sr+&65L'0?t9O
%)JmW5*$@"-\S,O(",Vh,WY"MKNRH,o87QeP![47mSFd9p7(jbLbfLN15;*cB[Z*1J0HK*lj_?q$iA,Y9p>Ds9&QA#pDV2N:AOhP]
%2c*[Q2b$>]cM[mMP=W'@bE>1XA8WjmX;8aK(O1W/5g=\m.`J8qh_]Y8$ab7$"g33VVi5q@hN%6P>Og]a(ZKH=eNcEo@5qnY[:<'E
%Lcb>er3)$N6r0i&,uU+If)Rfi]knI=Q8(4SSEXLnP1+qH_7i=`VKEA+pklV)bQ6r^6'2aj@]c'$gm1Jh"CM?ap883OYa@_JMU%Bt
%_W%KSPSsu[%C8M)duaaj't77BF[UnUZRXY*d9@6n(\Y!Ph[>+F5hZ4s)0m#le&s=<%jk:m%sOi*7LQElk2V@cG#h$49fCE<>V\"U
%g,MM33?str%Dke<0($WQWC/lnUcu4u$qMXfP_pc.80Z^.^bj+a-GfUQGETaOVYu7XWY)Vf=CZOpJ5:u'I)1dQ--mVlfg;haEj<i*
%]e3&_`j!+8Yla6Do<]_fku_e=*bJmHA3O+s.qm8&kd#8@GO$-eh9?%(=<Z5h<T8"3#^TY/RTCFWqZb38I]L3TBGpHMTIr*9Z`aZY
%d6^N*2^O)<E*-D*#GIHdW78p8TreRp]),0#CLS5U;uL61L5<]4qV"6AF>,)90ZQZ7E':,\CBh%Jbb>c88g2'`k8'[8d:]1"hX*Uf
%.90Urn]_Rgb^q*!=KE-`:/`PfMk\Nhj:+9.?C.Q_E2XC$&2A2WFWI[*NMXH<e9L(,N&Z*^?R@@1_V%Re>/K,c?=%Zu(Pn$_<C0)S
%bH)TO6G,V0%_@ZC>NUeZe#O&=rc=7:VB$5nf=GlRVXmldgEZqU<D1j7MrM_JD@*M`E`,=m3=BD]1s*;G2QsY#"M>B;JhaWn&9H"m
%pA&)BY(/OW;!PFU\lpnHLt"PM)a&EP)J_m\f).[QAeLrnjM:%^R?G/bSBrj<YR(i\$iV%,2,+k`@eq=FK4Z,\%K:F75Op!!%773s
%PC4^g[#$2e2pO3h5Xeo9jr=J`_i0RCk*!GM''@/$Lt?Jj@r_bTaLO[FjHQ]L[tfcb@B^.00m!iDk`Z[S0e*kDkTO\dbr*[#Tu+!!
%itRb`MdA6QN0t%]b<)kM%NpLOM9E.G6]c8=%A=H9#Tb25QpX7N,.?%;?n_u!+sS:SM]U1[#QVT<6k/&=jsO6AR(MRt.VGDW32^p-
%hE`<ja$[QE!'$k=MEhp_j1.KdUE27s,hoFpI?e?Bk?,*HUk+$.lq)hAP58N691=q7#]Qc/[N#qiohjJW*YNT@2*;28Fq1a_-*s5l
%*@+=Dg&`gZEa'e]Yo2l*+:Z/_8aYWFTi&$:R2h`F%?Wbm?Bp`Ubnd/laI*Vk18L>IS/"U^Lr$6OH7+Nl=j2P0T/I\j6j(`DME5)J
%:LXa$)Ki`[>[4iH"rh35&<PI2$ri:>(MC%`PqB"(=F8DTfpmL"<hu`&6!l&B9X4dVl<"4@Uc>OBn[;E3d9&c3PI_s!62/r%F^VKb
%>puKapF@u-%%UB64s(MuW`b?-^FE6W,ljLShb=PMbTbos\*pO:WY08=7FWBKJRf1P#27XEB9t;?%*L8GI;1Msl3+V\=BnP.-'QCu
%<'rJmU/(r_3iSsK^%eilp`\t&<0)Y9Vs+(fWPa%bC9).`)kinQOBahF8obiW<^ja,hlBMtfRitbF3k.e8X8pe?A/gC[H/RG#;*63
%P@PX^%D#=^O!g2JKpXa!\?9j'H^@**2XVFS\K=^16&5RL^iDub7%?U]n&!itWMOCVJjCn"<5\slQsYl7PR!@;%kSBG`@X5&0Ve/1
%A0@t-C2b_]d8D6#D0@&J*b-AY$&n#NT34<+6?$P@_Kdp]1_"j#GY3`_D\Ap5L!gPq<>AYEb+.7<ZSSH>o46,a5`\=bbW9'SOaN?[
%=uh"*Kf5-6l,(Xi^.?h%$s7R=@RD[R`TB_0*>-i4S[.>Z$f*nBnZl$:M34jclC)EZ9Va`Q^aS%]m@[!e$0bP*n7"-7="IC+H<&_3
%_QI&QYoj+R"K6N^L;h@5)RJF#E6(m]I]iM0*98cC.qV>t]S7I0#h@ikB/V(o(=V6\ARp5?!V?WK3+dt_N8nQ].+Emhr"lW_]O4GO
%@#`j&?"72k2VD8D4TIU%U:\?%((I)`.D"%;<q`QuZYE6r)$(:kT/F939b*%c<"MPc`8gr3(hV2fMu[ijRN5iAP4!JQ@0NEeFkd[e
%OtiE4<%Nc.3lhJuq+tRq\+',9UA,,8=OF*&2O2sK1sq$[hFc>,lI@#O)CN0?X!hbN,OeSu=EYN8.m]96V6M^L1(n0V56X9$hD:a)
%ViN)$>VBek(qaEN<H?p_pSm3I*):M_H8]G/('j9]\qmg0B7MUD52njRi3=!>:K^R@$(T$\"bZ3*6H!)s-\MsmYR#^MBn:cTfEl5#
%_+ZsZ<,%7AW[`VV\sB+2DE&dJE;i0[M(u92(go`?3X+E#B*JNW'=qX*1,&nj(`Q&$?Iq'7EtNN8.U3SB?ZH/k$8-[[&!?8(Jm?:D
%m>JrPhcZMTj[_`nR29>pMUQ9EdVP,3)UQYCdDO0Y.D<W6N`;c)32P2Y$OE4j-5Yd$)eUimPR*ob%*Vr]^e;rc%:N"gQB8)a<CqlM
%=XdS/@0e3;P.K9sM:KN.6XuDf)BpAm/Ph#('AsuCONhqIhA>Q;::Q(k?[&6Xc#]e3."aM=_"Rmp'LLlE`3$a]O&\_1cI(F`9\8;m
%m+rX.'jBeSZKi$j?4F<0?%ibeJD$PWpD$oS'c(,O#ETeIc(>[qR<E"")`?1(p+UB,UAFk5+>g+FO[Y4,R\oQ#XU1$i1qqm1`]c=Z
%,8de&rKPQ[a:Tc@P$b9m`95..W*Sb\8p3>\;``SW!g67Y&$DYig#>8QN&*c/!XpG&ZW3-S=FkCiV!S5]W]B,h)UKR+Vpf9@MOO?-
%nf5X;22F!\-2Z/LO,AH*e`]cU+A,P=EZUK*)l[,53TudU^o;Tr#oit^F'/#=BuI1=i$^:*2V33PW+?)(>W[V<G.</^eLjH&SK2)q
%9efpX,6!#mE_r)8VFfMXb2SJb$'D:;Y7=,WM0DoOVH4h.,k6&(P81DZ9f0_aamCOFq3]NW<J4(j:hR"t$"32\Nim\'Ol8tt@_k1b
%oB7!71f21*09WOdKor.=!;Z,-H]jc#N/p!TRj<O6a.Pe`,JIf;*.f%;dWFf;Q!%TTiBSR+CE^V90PE#NBi*,2]!*_^SN'-$4VGj>
%ejG)oQE`LjHDm9P&6[Z:Ym*TTo/WAF9#>ja7QXo6qW^P;TU'_n8EamZ>7L,,j1hJ%Qq^sWGX!\sCGn;'%IR5/-Eu5I0nquU('lR3
%HMN6TV3W#^FR*sGj+ofkKYu4BWNHp=q*_-HON+gl]A4AS5%2P%&pjWU$\!W]9YVjR#dA8KPZX!5X;g1-PAbTMkQH_KZ(&R!E1mMp
%K[Hh8/Bi[:4RmM@`OE:f$!$k'70j*sQLjFMEf)P3Z2Tq.Xk1W5l:PO$U,?a^7=%1^mjhih;\72A+&eG__Ara[Y*tSC]*A:9R+l!l
%$C$USL4q%Q7K%X@2@/^k,&/NU0J>6DfP=5m&1uU^:mWjfV<Vl]V6BG[O:Xf'!^T#Q626t`O5lQ`HQs]fOV;sY1AA,\jKQ3G\-&65
%"k]`mTgsMspaunn:4`XDmD/j1C!@CgLjH:n.EIlP-J%G'dghP?;.MR:_"C^J4MOLC$k1<_=KR-B2b!cp#(<YK#7IS)B$sYF[uE[:
%*d@aH6#RD!:C4NgU([tI+JSUbUEEki@O3JdflKNd/f9pN!;@h+WnKWK9aD]Uo5f\WLXP:Qa'(g'b\ZK>RQ0iZ=P-Y*#k21>$c"##
%Sfq2Ok_s(d5r4k!c[W($(UOP5"c[^L_-MJ;1fs!OQ*V/%Qp_05>VaTm-SSd,SeG<_ic<-.+?CH^1Mk'N?FO>1JjoSC'"')+iQXp]
%]r8X@@SLRY0=SPdaoE#2`.i,_=_Se`K1FBO<aR1:onp`U?_Xhm&u^+"AdJHG+Qn$T]TeJ?`W[I-$!H8<f-6ph_\1?`f9m,BU^EiM
%d(4["$?ZZ[j2#!;%fF8-8/"&>!=bZU>I+nt`l7TeXG:7c.Dt^qLu2=Y#ZPIp_X\SBL.Kio)iRMhS67aSENgoq^u8t^*AeN@RrM^:
%I/2Rii_d6uAbOe^IT5.X6Y1iKehn,'o2:Htku6!Q-G`Nle!Xhe7&8s;g_d5($?8@V9.-9(K<'&<#p1Cm!)'*3B]?Io6Fqc^P1#6f
%l"7_%17b''1kQtJL0j<rN[\7>S.S;BXsuQ[,,l6Xc6Tm3Uom#PM?<.B;s5RnBLEGn$;]FIY#(E[!m1W;&mEtfChU&<P^h:Bn0YeE
%*Uj]+%KX^(8rTkQ3DccJkQRa)=Ys>T'<._DfTZpm6+)+sT^CG$1_5rsB(YATUO*6]_<<Rj`JK**((:XVFT[su(86-gG;EDJ)#',6
%hbC(lFtm".[-P`56J7t+%)#lZ315ogIh>ClBloEd`^eTbMm-@PE=b%=A>SDk=i8$m3\ZmkWi0g/j'ZZ1P'Q09S1X?\((g8gKZqgt
%kQJ<gJfWJr5qOX_iHi@m(o3rP&W;l^@,_e=6&.Ng30Dp[dMS^Vk#>8H9i+Q(ok%,+kYt7iQ4Qro+C1\3_[f$jTp>A+op)=nhZXo5
%-^=tB6#2<,##VQ:!6QLfRBq_+nROluFp:dd45)u2f;>(%4IKps&?03_Se+bOaU@J0$cYqmh@kPIBb/#mX&L(g*VNslPRUN?).=^L
%BEqOH?7E8pH:1kVbBl2:*$Itp0U"MF#FnX(B+Y_'Vs+7c:IK0FZp]!,(BpM$[]W0f\t_5F1Bo.fHCPXr[_):Z\`MrhJ`7j4]VM!4
%6:?glABip]p.f)hKtJst)Wr?`n?J"X_o*;1Np*@O",2c>W%-4\(!b3P95I0JqBTUM=]+%]A*dj,`cCbM9n,^^K!fVA3eX82)33p<
%[LUCt]dB:=%`Q!o/C-D9)s!+(ES%,q3_p?fP?(*h1^2\\k2?(kKGa)2[,L-S:?fIpKLk;T@hoONejYsAkZ=Jr0I0L"pM=F*T_iEl
%>S@c%0"/`L?(_*"^"l;&hueo7e\suNo+&Q"c/*.rGJn5/<2AWPO2fHXo#RRtn]=*)*Wu[FHA=-UaDQr2']<t-9,%usBjr(Q\MiSM
%Jg/$JZ\iS=LTH+PBn4b8G0Q,fBY(=_`NPq&I-NKB[nHN!([U6A?kGTK'&cLD;T8Dp@amMK'bVTkFm\b8Y&Xo"nNOgZdAIHZ-ue@/
%rG"$F*ri*dYZ+<OG#LaW'=?\`=#APMFt!;I0mo@)o"*iE0eie&`-1/^cCH[gKXqkjbVSE^A%0E&0]PX$%_J(/K,GB>[M3<P*MFaE
%pY:n/"i%/3&G-D]W2MLY%a*O[T2Fr<6tDKZ]'XKLHEjo9XB*U(Pgq`cZrp1gU$hRIe]K:Oa`#SH%4:`;mKY&T.$E3rCBKr"GnUEE
%O"*WJr^su=YRS-u37gF]8^PO@9Hpe+-nWM8:ST.6+Bu[UZ,b]G1]sUWk6t9C9.KR]70A-"S734UX#Mlf"-5am_.+N8/isT?4c-NB
%(lP),fo2G/7g`[D^oH>#!8cE29L\Wf1KIWLkaQEo!oti7YPLFa9n50tU0l0'i<<!'>GR.\$nB(]4L68W"D6ZAi\.\3#S9Seq9q]?
%?m9EiYnNs0Y0$tRn-DO66Eh"5Z^JB6)W>[L5t+sY^X<LEP:W_37B8XE[FlK[i;M19ncJC&YIY4'p,9h(8msTtBWj*9O>MVMSch1=
%=gsU;-"+tEW^/NL;j6un.AjS:!D/!4'H#:UBjNVHEgA7k6;SG+A-*Z>ccbGQj/Q=7NYHF%N<K.cl@Ha>k?9i`Au[u8.,V)Uf$Lk`
%G#W$PKOk8#la./mkT\52N&#O&0b].`R@*Iq'J'oDc7k,iPoa7A]_+E+Y[ds8el$4hB-]:^mt4iQ"7[j49'48Fp*N3.\r)N\"7')>
%\'j,U^c#6@hrJI&ZZ7'h=gB1gX:[q1*Fm7]bDFi,Lmn.`>*>P$o`T5>3o%P(#C4X'#]J*07"\XTJ\joKj8faj=[\)1Wq!+#GRQVu
%1nqu,'4(n`fMRT@0,31DY.g=i5]k7N@\6gXG`[s/X=h9l*e[ORB^>!Nku$3R1$IXD_]d$jnQ)\O+X)gM>R[SYof1!Hj.>C3i/p,e
%G9@+^:jlJPm^?rWMA*#WP0kYI3H[(<+P>^0j^.i>3hN*CiOKF6`bQ>@C,gDi9N9.U/Q=CK8T<@/-#1<_K)#GRPkECt/Pe_=A+<rZ
%(-<X+R[=e-!>l6PU5(K)D2RKq_2l5P(hBbJXcS1DMTt"Vj9&e.6GrMsLPcVRY#_jNG^<NZ\cH,u/rN*@QQh.Tb*_B1B8tpm/K?X+
%g8G,)e!-!/a/`$fX;`ac>#7kbO#-hm&XXnMmJm@U*j?i4B&V@gJQPV`ntnID--(]-Q8;j(f>sEiCZ-Re/KA5`AqiA&Tcm0F@nr/p
%T#];Z^map!3qHQ&Ysf!sX5hS193QXbF&)sPP3e#;.^g*.Vfi[9^jOu)pT_UPc^>>26tftsc`]-g9j6Q*H"7E_KIpKL2h4]##">67
%n>>-jnj<'gOTL:.j\2OI#`5FE+[ZrGKVI$6*lro+g+u<ER%nsA?)r]='&bNP2!9b,'_<pj*K^mKKL!I%^k+(j4:DIsD(?)]=!X&#
%_:pY86]c08lF1D$SBq`q$?maG$Ur"$i98Yt=o9`/*aDCeHLM[gUlJ2R)X$S+:Gr<fi"s6I)!t/++9lh`0Z]92oU<$\/R-',)!T4i
%BI8V(0LJ>L*^1^C&CKLqAoT-b6-tTh2@/o0a$@(AKVD7,4jdb+@sr+VKG6\(-\Y_I@RE*tS(atHB:tmLpPK;/XEPOZ;n5h5_4QP?
%i@KtCDqqAOcmQ=Zpm>ZHp_b2n!IbnBdC(#M%gf48`KVV%L-r>TP]("ZF,SP<Y,ZI\XfcRS,M03Jqt-<,1?m^'(@asaE5mk%GXMu7
%=->p"Y]$>*>IWF5.r0h0F"KaJWd9?)R"+1uM>s-e&0Z>*L1O^[,XO83Z-gt.G9V:U!n@ACWo#WKQbfuL]6F`F*k!>s8>?]h;Wjth
%FA$)>;%>8=9I"/?+f8JR3-QL<)Q]MIQ>e];Wkp%0&f]_8oU&)L[l9MO7gIMQWPOri7'qgUB4qfqnJA0kR#&k3=mFrA0!N2Rm><BM
%&m1jh/ei<mo.jF@.t[uW/10/UJn=J&%EdMY@RQh"<EMAIL>'L6F#ho034^/&EQ;GY=ANhqE7'%oMAP;1<>)V`d#E#mX
%-,)a&aEYg9LQhu.k%eV3_A3dKRmkaaO$Q:_@M)1RdLH#ArRaSpjADaWM/5^3YUsJ;KcWZ)pBq,2]#\,$-O7e+f_tAQi43D<Cn.(-
%Z_b+,6:cVjL9b!pGs9_VAKB"9H1jmHab^K1?]Lfk%-QcsZ8'N]N0XaY:5aagl+0^u7:D#i4To^q&`JHc<h%'Q<LYs'KWY=![G"L"
%-K<Nafle:uWBCrZ[1cs5#>Oa+pmmT"TOuAhbH'a3Ek3Prq:U!$$O0'8W%,h1^o/u+D4NQ,24-gZ829$HMOgaobD4?#5Cr#s^eI]P
%e((tdEGIE!(cP7r;d,n2M4P[QLhqMSikttDC=95KWrUg':Z8ni'>?8A)/ELF*$29*R^8#i.7c^G'/-_A$0cKe80TtDp_**AbFPgm
%,&ZZrQ2I`WrA;""E^&.T1Uj(:pqqVPh)6siVRM>H4'IZ&M1/-Q!F(2VBcg80+[VdFW\n#BqN>?9G*$)`=-t_66c=!sHOKA;cD)@2
%-G,(6)G:(H,UdOec)QA`cH&Ii>Vf8XRAV-Z*\BC7%C_PXOU9"pg1-Y,UlNu?2M2nJn5$r^_Y,J0-O"FVa;i\h3!cCsB!:(>DNfp,
%Q#]2)A<:M_W!(#"oO9<]@$hO>ltYE*U;n3;X/GRJ2u-B>5e(ff$I,uH[A>XP,)EojJn?d3VJYh@Cl*.+W,aCdfJk\A3cMZ>Q;B$D
%<o&ZDT'8t7U^188/:sUGEA"c*kj!``9Y9F#HLUXfF]1B&'B<(rM[^4pK*<aO6&:mh*oduaOAURnc>'-i/4/W%2;%fkbXY_7A`-1I
%V3W/Rpk.eWp6M.Hh%7NJ7#LGt&7tr_-=rK\#Kj'fEOPre%=kgP=fda_6n]kDjY0H8T&l1bi+45,6;sk@a\0!Yd@P2r;tCgM>18rm
%1'UGJQQ="+-)#/HVfF^.'tIu+Sg!"?SeaG,6%(Q]3K*H3Ci"7%(Nb1k3cbHMKh1.0oM?jfZqh8s+ZE+ocD^/4/]m?X](ljO!u(Od
%;WuedLlt;.]YVaI-*+X3P6Hc&+RC<1')FU=Y(9.U%KhBu`EZ"=<=8.W#@AI'h?7US'bl?L@e[sDa%`?N.QI,1,3<PhQ<k#CP_`6)
%K_]7Lae,_E9kDEX,.[gUo38#+=dNS?<m.8*Zs=Im4EXJ_CTG6#7*Gh%,QKt[FLZj2^t=t,6T/.@21$CrCbRPrk#(L)IT7J`_[9`_
%h8R;8ciYeJ_C.Kol.102**+@tfnrHgJ8U+E9*7oh%"RAil@Cc7_2Rup/F]498liSjTbWuu>?<@`]$Ypl'c_.J3,HsLSeZ;t$76`i
%-6RPp]P"2J!iV\n+Hbp>/_AhkiWKNfFFa\^:X#g&X5:hICf>t^gEbQ>c]kg'.Upir00f\JmsMN6].UT@8p%iuY:/d9l"4\R6B@\n
%fG&iqa]ZeRll##(0UL0;ciE:sbOT+-[HQTk419>ui<u3XV=:fnJ>!.[\U)cY@8NrrX[d)"MIAGJ$?:KrT]G3CdT9RK:H_<_'2aAm
%(`p^if.\54Cp=llp[Gm/HBq#nO<c$9)hq]5gK"afosE_hZ6Z/e_1QuLKW+$AK!WtebBe[@_!3aYOQ[EUK&'-5.9KT90L_##&75RJ
%lITSt!2S-BH@DIpL2i>9e@j#O?Y+M5;+[3Q2$4bt(r8a/eRXSGaGNmuUpil_*6)ge3'W.Vh`]gO'l[h2VWpSA$2l@Hp@q#^?9[CB
%b/B0+cSMM/b&\'/k+Jq*e/1Wqr@I_1!fQg*1Uk#.'6p,-XS(Ca=>EVFin@TH`21rb4[gsrHO%&[]1I'lM+)+j))#.:9([Z*Rnt=;
%o-8QsI:4'g_4=Xj.BCY?`hgBj3_ikqP!7Csilc;_(_grfHX@hV0(K`%$C\6-X>$&i>)]ToPmQ!b"d5Bai)1Q8$Rt]^EOTS"*S:6>
%J:^_S8mu67*3u(h@W.$TCHC+*'a*Q6lB3bN*F^0i^`sC*@5:MM=e;*kb$ooXFCCmYd-fCkRLa>Q`ElekV*NM'C+p!0:6Iq1\N6W\
%rcckE;mubY<\&D$Z+<fLj7S'^Hn.sE(mhKp76.FGeWAt,e?15'0dR[?EFi?",mFe1C6&s1!C!ALVaqRk*Olu8\^\LXiDtm;k;QSL
%P<qe`;(#BqeV$.68mo*C@ihcSCaVIpb#l=ir<sh-`c@[!<-+Mu_BaB$b"))89"+reNW@$<bt(HS,bZ8&F%%BU;ut2YpW,MM%U$>6
%1[4!T]BrYW6^N#jTPk!3k*\po0K[t0)r>$T(rttolDYSp'?umWXmOf\0h\5Olarl=`tT69E$C/`:/#6eTVH"NfmDXm3]+Nc;%b0f
%%skc20Ud4lP/#97*_kn$OY%D,j2J=f+Dl0;n<iCf5:[_^<?b8@!ZJ@0b_^`oGq=XD5jH9QcYIXG[.b13@FqtF/;;?m&/jE5bB_4M
%@(O')=eM_<+B7`E\#@Gc1gok<'s2JTT"_ES1'Qr`c>nCk&ZrlZ@<>-I;;P#'Xo[cH>4>rpW*mk?_Sa$QoJ+UkGD55nbY%)Z0W:G(
%.8/7WJ"l>CMPqafG!BOX)6f`D+B0m6R]tn=E+9VIo<b2;.h.qS:4&kXU#`k^drS`Y9<q"#Oqg'P=?BVQc1#$nMQ,ij1pj.?eTP5V
%JRo.0(#mR\o^dtK\J?]_M,[0CFY.KmXo'rBla=0.(8$J-:_Al*Ss`a8,6OX,Lo@&:DdDA2*&%OTjO,Sg5KbiB[[K&SQ+1W=$Mfc"
%X)^Qi3mCb1RbA;u5gqU/&MD=+Dug[S5oR4mMq0R>0Tm\!^GZ:U`1\60.$%>K6ul01dHjWY?K/>&h=KfgrA#T]#$mF;^qe4"UHTZD
%:!%B9l!fgM#>kqJZl0RqPiK?rh<M,FhNi&73WbJ,>Xf9224sFCHa>Qk$aj9b+4aFI:DOr&,-hE0K>RMW&4T2B>@60D?j+U<r9UB4
%KogD4LF^*Q=<3noZ2dC(e,!e[\;`mU'-=oB1V+d!9HDY?h4dn6M,nAT]S\FOItC+=;Oud,rQH1]h^k.Rli0uWTI.IUGZ6SV.3L*n
%!IM%f_HF>k04GAp>Kt'h'bsT_GDRcQb/9.tPY=1GEf=bcNUAla<fj=(08(t+N95"hO^hLf49f(90='.*k0OQcT9=mi;]q/o^uR[5
%"f53AkKnXcr(%<7!E1u+J![BORR<Mu/Zq@WMcS\mU3NCI&Jk.(,&19sNO<@dI&TmBZAS9>0k4[o?tmL[;t7XMTQP5?W0dtgppqaq
%<8F]6@$Do-#YE;Gn](7d.W-!A88/UN1mH`LHkCP0CcV/$6OcUQ$\E=#['N7dRIJi<c[j*+4(@DNYpGjagC"SA8B7F&do5M)/2^if
%;VAj\F-q+8mT%*Q0p"/BSl'0j'1R;de^.FG$7nQYaYORB8fF/RA1:q2.0`mtMr_%FA+LbdB^#kGJt/o';d\jqC,QtXA/'hG@HjI/
%UOi,j<(fmB:qS%,dogLj=\4W1o5lIF$aX:S#Z7:mkl=J:']iPDg`o8>i"ZUWq!9-NU".9Kbk+Xn33Z6SegRdolSM);1f2%ZZ"oTh
%dR2@_#fL7*n&8A/?s:pXk+(pFesF>+U1L0q=H#*VN9l6n)*&s/RKrI`kO^+X.n4:.0l7Si"A=R3-q.*8rU7',YNJI<\Y#%o39s0K
%Z%Z<\3F]_,\:crC==Bm0-,XrPc-LE""kg;^\.Af(6h>7ALn#d_$9cVUXEo,IR'[u00N`J?_j.U.gVmnU'@ra(PTFiLk:u?2S=/*+
%[d7m`*&t!I&Ih=''B>_rF,%6,"5*!uU;cG!9"Dp^V-=4?J@$Vs(5?'o!N+o*o#NDJ&7o%o9WO\;6F26s\mW^C<q&c)VDUpl)jC=K
%c:X[T:5J)q/CKWr/S>^ZGhQ+$//-OZOfLZTBF_SD+[Sat/X]OR,nl#hoMV<UK-WnGBuE97Lh&0c`g2i_(5YrZ.Dnr&)9HAK975L!
%dAn"`<Q%5HTQeahJC<ei<Hu-hC'?j[[/76Sj.Gq/dj!uh&I#Sn?r=c)JCQrN%E8fIVK3r>RN/NB%*=B733,A57nP=q!_Hkp!jSU6
%R"C('k9Q4bp<X@66<d^DX:61hr^rd$[dOue<dKC%mr1;3qoMI="/&$/Weme%`onGsDjj,A?!iJ9C\Stb"#Tjc\5aVS&e"8^Z(\_0
%o9!O]$#c92(-h5Cd\=_N.F/J\\Wg-#Z)Pdq:r=a,FCU>$`_/2jmT`71&g-"]8]1rdV)i'&[_p=DD&s:m7J]tCnS##c`oj*C?p3^r
%1+ke&[ts*6`r=?=2@4q9e8hKRi+D%7Z69o6;j([[6<*.qDX9[]HHq_MH<Uo(b1'3X&ZmLK/a8K`2igNc]HF>B`+$[gL!gSpEdI!X
%<e>p(Q8:i@$kh")U4o5$8s&Pc;NmAB+\T^eo`cS9>$Nt1BPs6G,s0e^#=X^k19VH+-=V$bCMGs$rtFHnlmT6cFrTflLfo8m#3!/I
%d889<#8BB,j0B_`0b_gh+en*gjTUbq!)Xn4cEhYdJDAA[oV_pj3b/jU!R(p?e^NNW@UpoZ"O*kJ94K>?=NmR'AsQ-6Sp@Aqr$@UL
%=<jC+4Pji>:=pE'fRiJPK@.'Q@9BV-N'C@:OP2,(dG^bMpsNu$b^*VDUhO':Co$UuYS``:3B(&sW3fT')EfMD#TICi`mBZ_fL_&U
%+[@^5A*XNG'$i;rMEdNXXF_@A)+9fl6(**%hBlGRk2MJ3$]R.!&/aJqO#1gL#W,;%C(Y8plX1"1?0E2]bX?l&M`AN?&Z<G)UECYb
%kdVb#ckehbO+@-$H'*90dG:hU*4r=6!0aCnOVT)0<e,kh_0Z9Q<5li464;#7^(V0die9e0!bDNM;iB0uF9qIcU8icN&t5o^AB!iH
%!F*,87j+JD$0G*hd(+\W\o5_b.#oGf@\C1S]=9<C12FJ^/_LCD-&[Xsl&/<S8gkL8q@a!6Qr$AR/O7&gg'@q4-t%'u`g$Rc==oD-
%lWDp@!5"o-J8GE<XiW.8!DU4i$.YYui^Q<bU;aP#%7Z'AViqV.cjZtgAm,L@M9X3A[M@.G2O$^;+?f-opGP'I(!a+1N"q?$C5[JC
%cLED!29uU(M6*>/p6S7p<\mopr3G"oCA]QS#n,=_@p!>?b%un/;P6`UG-h7QI=DAT32]368JlRu`mh$>asM+-CR\79P*Uc?%AiTn
%nrEKF(pM8"pZ+d1+b:CD^)8V)^P4MbC+nNU"LXS.3:"hN*%-7r.)]apA3H\#-6QD/Wa]sVDFetLEf%s_ecg?&T[Y5:dCQ(3-*;_6
%"s]SR?FR>"%DdjcW:NIB'K<?1RoO!ma`aM#/AnND=8Wo5KsZqnd[`589B\Jl@s__"Ufp;$)Od7,IPO/+37\H,<0gFJ>"QUH$RB^m
%I[:kF+c`NQ)Ts\DW?ohYB>VLU'DUqF%-4Xd,.Os111n;]S[t=P)>LdEGV@L9[!P`V$];18Ks+E_^XqrEX[>k-eOk@5k1HLnQH&Vm
%,"c$3PZXeko)ai^bEjR)Xp(md)[rq6^-p1i.<@9t>Sla1WU7noYBNfjGt8ZgoptUlL[icla=;\L1^PD#_X.L#i:[K3#($Q2*EnT]
%!L\S4E>=Y,GZ!YqDOV2[j/IfkU?/?ja;oKE2M^\8?b!\Oa'&/^bg;5;-_-iP<!`e3Ak1B7B'5j]Y0h'b'X<ZA8o<!)_p+!o(u0_P
%lm+bbkQ,u<Zn[8n'-[cn;ipVcTU[2j;-U.J7anaQW:jlOd&^W!>4jLB`hBAgI>X:BID5Ud4dckI[(oXe6g_KC"h\*VX94ET$Yb@g
%Xf6a6I0s"mN-Z')$^=D9E$'#SGkTmmYKMc8qU4R20**WIQ!J^g-4Lie'V9u+>1:$VRIn2R+faQWRDVZjA#b0oU8Y,h/EJ0GZn6P3
%)7`gnOG)7KWe-"<*c_5`oefF))Jaq<7q$"QIQNeGZj[Dl&A`>Abep8b!t,Q=E"&1g8$!rJ=8*O+f)&2P;]m!Z<T=^6UtKm!R7^G%
%`jtF8>FHID"'p/&\<KZa4>dkal_HN5]?^`Z2Eg%qO`5))+R/6m#;Ria[V'_2DJPAMhC>`hFNN7!,uMN>'kE;9dUg;SeO2]l6:UT]
%L*nsne5in,+(o#^4`_P<Bj!4@;qKPEl-0Xr-(Vr"5^Xm`J0'=Mho:U]D'!XD>QS/9LCT_7*(;mZ[2ngm\G.]3iQ7<(!(\q6[cWI[
%p3TGri`Ed\PC6aG:&8RQOTFGC5qit"]&fM[AnQWbXGDu!cq""uP70ZsoP;/L=1Fe[\KekVbN-+3SV6E*#OD`<W+u<U[fV!bghbj:
%LrMk;'<#,hR@nJ4iEWcO=:^dT[g5cI$n"MH!__&A)/a$:1pc`IR@Qf9-4u`*gV\"LE5cq"1J7:%6`T>4"uB,rC[$io:f6Sr?fPq5
%jOhl4OA7`mY'U8Oj-EdPDl&?$B=l#a'SqS]6>ibc%Ceu4d6=Z;blu'-*J5E8cndFb"u.!>Yr((C%W87!_`_NYqnVct80J5aR)7tT
%PUe^26g^J>#Np&2[=0,LK`Vp0!skk>25DlBQTPJshs2iC$Ou#)h(Ws9>&SY:"!kH@).?*be'nRea3em)XHma<93&#)Ma"kFS@54p
%Yd$Jh0+.T!nWi?5nMVp_dgAYQ__]+X/Ts(nJI6Wc!`HL)8SKNIgjahs"HoMmKiOQYX"QaD*bIo2"Eo\u*Mb$Ta!kUA[U-XjVS(;R
%0gc(rP9j_65\!HF7AU(_cZ3HnB31F%"KL_liN*(:/2f#PY"Yg1\5op3p9Ou/itb!"9WU5TNQqPG,Xuf0[VbeOd0(.:#KqE_^g!09
%5pF3s*Xp.A)6X>[BPRerL..[ZqM])Q/0D^V)+OZ^!Fa5qR.se/I>)f6XRJpUTQN1;;F2c9U`mpVc+JhWYpS0A1"4n[4=XUu%I8g@
%W`]Fg)f'DB["q>CRRLL,^7/:u2B$j`DQW@9`L_4%R2ut4XAoND&25:08?oo)Eu&=CCUE<)JmS]nfhpPh9LReR&^@^EX7J*[^tgdu
%b(?pRa@LqoMe98U0]Xo'RDi-!NCrCEG&[t&l2e+,9+2_gb6%LA`ZjGt76%j-NlaZsn*:03<\8'$7@]@\f<gd3^n`i@=9U6Yc3:J+
%ADK8Re`k_5KFcNt1#Ep*-YY6G`Si+qT:hm#"MDtd2@FibN,mkL<@/;),UHr4,9J<0ATHWpr'@88/UG5(%b[(3fc1&Tf&[2RVX!,W
%,U1*Tc"b1qI0/6?$"?QsQQlHLWfZ2/BdlFG)Qnh$X:B_1:pQQNdS#gb6&67Ae'!4"W]EE&AoFmM`11;Y_if<o%W4l[am]Fh?8+TU
%.7T)_l;r?gT1pc9*H4uHA4WfW&FpXi1Scto7#Yn+T9.*Z&?:q+fI&i,Qo1%>)64/8Q!eem#*5nPAk?W]7D=l"dW>I3;%F"j2'\fH
%7-n"\.3aHn.Sh7_/H`L'Tl2psD!]f>44>ZU1th*?cre,upgNQW"C__o/VpfkDq<UbD5^WHR_r*HV1;2PeLb1Wd[42/>bKieaW9lV
%'mU5mEIH4O,n>N4Xd0SeFAj"rn>-Ig'A;H6J4,Ya;Il,ImZoHTdYXWu`!dh&j-q6a$?6+XA]s.R(;EE0>6Ro?:)7aVq07le:$27o
%`n6cs_[t4p<h:)59!HaBgfUbR1W\urR/jU,$HCka!R10NNFmnLPC,s=Z6=$Ao++-uTEuEF7jHd!=kF.jS=1Da7Dp&]#M!]N@>Zm!
%Sekcr<NB`g;2q+9hjS+!cY2HU<Jd0D)h]<2aC7l\30^A<B\H?%O6B_t@G<B__\Bs,(P=Dj1q6B0%pE[mVCNF=VB/EkWPrs.KYADh
%U5,Q_W\@jJ<ij]MN$WSP?mD\5Z_DEue_nkgk)uLniX:G$e?km_'4PR,WBa*/1.:MG\KIq6bOfa0+SkuSYQAqG>s6s]K3%k]pRha4
%W"\ui('h))hXE]*MCc[2cr\K-J2@:"e58Gq)f*^F-CQ1!!7t+O6;!eWe$Y%;c(7$XDolF*.#ld.]L8JuIQ\L;P\Iq.g*B0fLd>,c
%SaF)L*]jZujpZ[Z/N>aj5^QKh<J9eK"DY28V8sql[Dti'"`e,eoIfCEG78Z6?qgj,=f"NN&:pV.VRW$'TVi*'1qc5^,>p<JAtKeZ
%gVie5,nCJ[5BC]Z?+h`QR\=+*X4#Lq.Q6Sq.u]#P@.Cg]%m^8N"]'IMmFF\8B%?8%7+T#K#Fh@(3EVQ0k!*1(50X?]VrXsr;4eSN
%r1`TMktjU$[i1JOpmP*m-DgUXN5M6!BhJdtX`t\([(p,@*DAp9&YHZ<BrLdCH-tY@CC[JjNY0q,eoDU;G:Gu]/Up"c!H9^r;`?-.
%en&pHBlQFOTi)SpT_5mqLkn(%enS-iJkd-G7qmpd<Y,'3\(%)@>@&;LAJ2(aL`"$SMUgXc=Vb9<V+h!7$D::,(X*ENcks7mc@Q<N
%34E>@-PAYaiL5G#5b&gL>N#Sn60kDDV$DIhZj2Iborr73O2`Vo99X:M"eM^jS4kV`#a"@hd7DH72p2\7Z@2K,J$:!$<Z-;qTs_uT
%><U>mR+NQMZ3l)b#:DOs6<(jFSq06]U!@q,$g[q?R%9@ZqD)gT9jrs9>,!-TI"?DO1lg>l.\)>-c:Z?#B;.?>Vr?)9?-?'mjQ`gD
%Peh!?=;>;hd*k)eHX9Yr2A;[V#Gh/hLEfeA6hh$uXf0g=flj8(J7G]3)Dc09ThWFeVS'-C?UOe)e]W"O`DoKhEQbX40.!]rQ5%:a
%cRQlV!=lTUP`o(2jKHgK.U9$q+<<qt3"h%)L8@QFh\1#hQ4jkSV$Q,di>TPeE2t]!h"cf$_+Z>HVoU?(_9j6q5VQP(^Y`3rakcN<
%<YRN-*^OEk>FdN&`Mp/_DcdO+BjTe=g_0C8qSpf`%#U6Mfm4Kj>WE5ir@NGn>*oa$UD]Ui@8n,%p`f"-FX1EZnFGCq-1PEC"iI;m
%QU]<"3KE<N-5n4E6tiJOq#,Sg\EPQqC=/l,(;s@3acMFU?.A`rPA$3\mm![RQ4Pr8!f$KZQ_4HIXK=`iWBjM4d\Mg7,,QuY$r*cI
%H8>.]]gW(pi@;c79m+'4#.)NP-`0b'i+8\,(=U61e/1_U)6Bm&JO!$mKYmu0Np#S)DHQtumsX\M)2a%!$hOuS#YG/;3W)Q;XCc]+
%'74;r4cXB-KcZ>;(X-\rOu\6<n.Ms'52]W<C`j!0$05l2US)AKe85Bs%D\MBA\R"Vrb)fSLm0_S/UCFf!mMF[&aug=\m*9Sj>cJQ
%[L622;7MH72Lqd$fIi+Hc,)#9*'_ldm7oN318&Y>^*AKi5Ua@\!KFKJ<-(UXi]BuU29?dsj#_jX.uY2cY7'-qTpFOQJk=:IBBOfO
%m1A'?An<HG&f0WJH9O#MUR#R=HoER>'9po%le(p*"bWL#'#8Sq\uVKZiXd\<e]!mfE@WL+!.S7Rm5.QMe9P4%Zls<@Y$a30DsLht
%=aXUna31]n\<@dk4Sq/*'N+KB^kW=IDIpVp`I>=Qdc+MAb<DA3Is]d.$XkLRA:Y9-._I**/RLXr[X%d!]sP[cd$buiDA#YVc#SOQ
%(rATnMrahSk/%fTJar8H[P6jk6:I6/@NJA7M$:*C8,AXGi!M"2TD4khk7`p*n+<rh;\DJi:=o&NXqMH0'qj7,*kGdd"Nq.#TV(l[
%JU+IC]$:sGmJ8A=cIeHn#d=eOEA/@F;XG#1FJn/Eg"K>jKl+u!T\"]mb94?-U+=KGkU^$IMW=rD0:S5e+6e8M2d&=?'Nk$$1TFU[
%#1E2O)*qcbAn<9?[.h?b>&ZkNmNEIdKiO"Y?jq*fa=;_E0Et.[1jXe/K^qZN7BeSts),&(RM'&C%3u6@@'rh7'j>]N%fVJM6eF.T
%,\>jOS[0jQi6U<T6VQBs1BVkW^BoZ1V)?op4,-6_J3)Cc&eQ`^=,c7Bq0fS]:cAMBAT:sG#k9N1_d_@pA#p?`ZTHo4l8TYZKB8>2
%d&Ff>!18jF25WN6eX8F9+r1<E7bG/`_qnRjMZTB%56p""-"d>'#^fpn:2%WW*Z1PcP5.t[-V;:b8n3&\G`<$W&9"R.9s0+A=P4f>
%#/j\$rdpTo,Es2q=s_1q<9u0__lQrr[<5r>iOZd],>_'-$#;@8!B<Wo"P9(u1k_Z)[=ZB!hOh`Vhn@;g5mPhc5^"7$M,Z&!V>PL&
%%F)+d<`.VKjh&d*>sqa-@e/L*l.Na<ZlA+Ae[TRXN9!`G8^iH':e8<i+F1_oj(3^KcjoBl-dVpUrO&Y,%2^0T%'ZD!/p-#l_lrS,
%S?uK^^"ke1PWKcQq`=m>?1t.@K__rgde8Dm8&Up2n09_sb4%YIVh/FY/Im[W<sp<Qd>`>i34m#b95Y8J2$@^C@o@rJ)1nWKYqk@,
%6Z-\.H(%LgQ;q[X$VnU@@g5IM.MAgLF[TB2#KAHY2;s$AjCc_.Xt#<oXHM+6+ts``UYI-qi:u8;NgHlq6HsW#aDPm"isH>iMU__s
%.Xmh_L^Q<N3Jn=FC(;OU7g]PB!"jpk,R<js#U(qHC-_CQEn7-g2,/nH6!9ZRF'+dt:>`L/>%Ni[cBsnNJ-nOi!i*p])rAp+1t4K(
%#`pc(c)s_E&cLYS^4`0ed5PAu9eX`m<WE`KjkuC@MWq;.4U,du&`[Q;L;mE*3ib"%_'.%!lc[4L6SX;sS$0t>a(XoM,r:ho$^@]R
%\RP,[-#jF)C#e#UiF%aVNr"c@rshc02isI4jL-fgK("bFbl@kFHk<'G,tPj#'p]b*gg6`7#+a.<N?1.uVZBbqq3\3o2LkAWpKfF=
%R1Co/\pWB_a7EWbh@f.[C7J]JXhNEYr2;P:"YUHLQ_7t.ZSaJf1%M^n*\APVVG^>BAs$0g5?UN>-Ybd-IsQ&:F)VSfnl1Mt^s'Z`
%Np9NnBKAdce.JRfGlAfKa7c,<H@chB?XH;p$,W.#5/Sg0'lOt:W@!XW3qn6Y'9=7"I\&YPSt<@4JkZ^u!Z<.K7.c4oeeLlR8rdsJ
%GaDL?3W[lnc#bK8?j/r``Y^7@VqI^sGmM-!ce[OX*Z23--=k@Wjm$Lk*%Oi6%dj<Z#9b`H_#a<r7#)-*eS:[1?.*Fc7&1)#1K8hR
%c%m.;7be,op*!QBe0p@SA'e)b,+>j!4+Tkn-"VmgMI=O@8e!U$,ce8@U`?uX2;t)O!N@kC7*Q``U*WqI?jEKa(_RJPcB<om$;isM
%]F'sP8+[p$%1u$MI#+(2&f"*/B^20]EA@;%]ib);!I>t/WZkTJF!?>?DSq($FeI#9QFs?)EePrH1U5plYasQqn0TrlEJo[iKZ8UM
%;1BoDBR\`;bMn3Z#>aP)4Ls2*TY=/T5SffA/#t?c=%=dP"S8.;Ld\<Ybi'4u*Rc,bL":lC&+LkUERn,284!1R6Cb-/"`CpP..Ji=
%EGQFdQ*.o,36ASo@Oh4"'32ILl\2CJE=:h020Kh,7Qq8ED0]n2Jt%S<<.Of"%L'ZGr]s0,*?/9.$;s+p%M?<9Lfnf$M5V.V_GYHn
%LLt+`'erXD^6E4+6,\:6p-<q[aT<2FR?=2^#l(&,!7=S1I08UhLiHinh?k\D^$$>maIjB^/\/(,l=^QfX,>#!Se+gT>!9?*Lc7%@
%(0\cj;/`\=$:r?KH:&];7=q6D!\E\AE.j)$$M/Or=OPP1R3=$2'r&:.BkHX.No&9EaL2lOJ8u3/kLUso+t?b`RKt@i)ad7k<Y!Nr
%Mj[Ore,;#M$unm6kDc!LME41p!Zg)pFDsNYM@LNU>V7\jfs>T'KroutahH94/co-N7"nsB&Z9n]3[Ja_Ln8J)F93\Y&g5@+@Q(ir
%f%d%L*ipumfr4\Wb[%N#-q$iC7J;+.r_n@SaH[C'XjuOd0aBj=X_oW^,8>e5b\D_lYSso=Pn+7X5kSm?I'TBW-WrHDg]:kk"bikT
%6H.cE(/3f$s&*/A+3"5[@?'0T!["b'j:SZN$(AAkdMQ::7)a,K@K"niO7ejt*m!:L!ipA;SQnSu6[eJAoUUfJOhj:QmWUcp`/AFT
%9Z4br2.">b+k,XFC<SB_b/[C[@Sr5SYTSa#&#d;a":4,AkRs,BJiWHj5jj"t1h7mJ=T6P6pV;lgS/?e09s7=WF#&j@K144B?tV5s
%Yg0"1^irIA+;[t$M-J'O0QdpFpj%@"YEgmn=!OV!76]Ct]=cbrFe3W/!l6>?5CTE#<ICB_V4;(,nhY5QJcOr4LkuZbKdmIh`f-OF
%7<f#'@6gLh(5N>0"LOE4!s;Z/A2OX;B!ZPpl4M)fY9nRH!lM4[nK\"boOF/-R5?`'B:S/p_K=7G\j7>4J[2j6@F7JZ-N1D/hihab
%9<]=T\(+Ul*Bi0q_g:^@StWNcW5K)h#!\+D?:J<JdH_eBi52''jX2C7+=:(FBk&,$Hn3ic7Rc*J'ZOVSWg2<`*;;jcRSH8o*F)),
%2cb4j^;asp3tnYOc9fk)"Qc"&E%%mQClrNS'sa"QlQLT>.^/AJ2KE\3Y;MB/L_."i-JL4+6$\(dO6E.B?sP^tc6NiW9O\j#8?1;C
%nOBk9o:EJK'\r&@)Gc?$.cAGYg]9Dc4lQGUKJtdsdT+A6m@]<L5-6kOCuY5Pf3';@Lq)$qDJ_D>&m.Za/_pn*6`Nc,'Y%Xg?a(AR
%JsCT>"eUWP87>_4&KP,Ba(e-YI;X1C%6]Xp$SarQOlV\@4_s9i6q1IqA:^/r$lLKs7.5c;9ZQHB^SiB?:7Gi4V'NTT%pm75qS\kt
%?E/!BU1[G9k;\6,UK<\LLI+<qJI1I64Mjn^oK5@,9m'!bIn?ZFj+'h]HDq@2'?8+8a6stC7LUh\LHc-`J\jcO7#QS=:r@g6qZd==
%I$[94*&S4\\cf\_n0R1)7%7%<)RGlqR.c^IVV!i@`UIP`1U3)cM?P]2<6u=`4M:llJ2E7a@C]UWVBSf&g96=@3+]MNXRAl#cItp3
%^K`J8>/(1\G,_ja@M#_VJo/#NGs'a*qM@:>fdC_jDf<>p85r2bk)O-VYEW'8,[rTuGL;m$*4q4W^m^Usf$Pf-82H["2%lM)%+eJ+
%B?Q;f-%ek*HDKSDrgF/3b,gFn7#)WJXE<.54UE.Oftt<XgfQU7ORi8jI'nppcC3KiZDrrtW'a6p;#6l!dMERgmmX.!Cc2f@ns@_:
%OM!+JM(YJPH7c7\Db6*kjNJLOZT!1faP=.%:;,Q`r,B,[OD,HhU0D)9Z4uq?,&G,(MpX+?5.7/S?B#:``#7[Y%GaVa-q*'I6^Roj
%D2$KHW=f\_#s'OX'Y&/IMd=LW(.2@QbCdh(QPCHukZus>hI&95<dUh<DT2g]YEB]<.]+_6<^CRfmQkoXMgD1TVDuZ9Ar"e!cWTfP
%)BdLd=js=C6g5iX2qGfe@R@CKU5$0j<-e8A/Q+/P:M*9.P<cK&HNdOF'BD.XK+?4nnNb["QumD,)"*b<n[VZ0Y'#7d<6<h@?.YiM
%%6+=d;4+9"RA[BJk$r&68>L/[GW[bUBZV<L"2_&q-*g`j0!QJLb1?iU-AfQUmea[0be[lVK<kO+U*HVuFQVTsZ(])e$l:XVGpGu<
%KO9CR9a/gGO7jF_Lm"8Hg<5+F1eC:j$V%clZ=VBD#"!1?I,F*`4i&B?R>IBp0XHXSFa@hO3ad=U68"\]!7h:KE$cdb#tV92:.+7L
%[8DL]a2Eq_ko4V\#kNn/k2D-u2g/nWZg)9^(-b6&+HL->VACAj^V]*3?_N5r+G\\r',RhhoWA"#Yi/Ff]S^2je6)s_NJja(VbNB"
%&]faSN)$fAfX!#hSe#p*^KHXW8^[N)^Vi&?K[`Z3QdR-=Rj1S<&M$b#Yh\sk.gW$q@Ok/i=nbF*\dn.Oh_r!GT`tO":&K+cBnCMl
%/"<EMAIL>^s%XgJE)>lB`HDf0,Ee@=7ePU!ja%T*OE&kpSro;$`l:4);g!OI4oppU4j;7jQ56(<+lX.AOp\(XPSD;ni]t%&+
%0@0WFIW_.Ze,Qqh3k7A"IDk3A5sJ!0B0UQ0^S$7IcbB5FO5A8+rlFX055UZFm=1,1rd3#+3D)fV4o.DCf,_OG^\I/VA"q5%-<?,r
%QNe#KM@W.S88XM]5JBQ.S@;*3nF<W0*i%WXkDG#eqsfa;RY<Yk<6+Zba6Q#_9ngq&amU8T+/Y."8e?:OVs5^*L]/DPd:Xhk_Sjp%
%Ku@V]1E)cab@A^P;O!-D8UjTQTdZ@L%gS5=:'0f#_A-VgH(%i8L-u9E[S3[3,t_ZB3ZH$"`(2S<;%bEeeF"_Z/62K]48?@A:;pXN
%:c^ZjM)^M)iF`TD9pN@EK;aJ7RE.D5VpY\/+*j]\7a,HU]9q6=(s2e9L_Q1@@NuO<Q\cZ&64q\W:9sRYSX-E4+&)6)_hj4;EqT2j
%]p1u+,DdT.?o^Yt&.,"uhB\[nX1Ss3SV[.>6)R57ku);QM4CS#Lfjj.-0>bmR)shuMB2uBVWguf&W(ee3&W'TKkMeCQH.m?P4`WS
%E%D7CjUl'5-Y8-k>hDQD2ZDd'Ra7cm<0<$*?=I[5JT=;LaJB][V*bc@(9@c7lnI7nL<jjUcQE8hN/9pVlTu#E7VFrD]iM_6T49D,
%+?IV]OZi;2p-Rk=4&+ST(nBO6$ZS;M?&;G1Th.+`+d0OrH74WrBlX!0o?g,D%Sb@"Uq(,$O*X?;QdJD9N45:6d^*^K]S38R.4HmC
%LEUr;-#-U>SYKTtSatdGo\d\N_*k7Bb/?hN&=HWlB\+tlE@-UlL\ApW^nhbQH:V%R'tNW*#%pA;dN49N*+"DfZ[@eL'q9$$B\H-R
%6]'6$7aY`"6^@9t3P_^mEOVe6^e0o+3#TV9OX<$.&KfFBeEb(q_;$8[/b)LiZ*u.;"ppo1,"IX,_E&BgK4]Ep%m_>/oQ)a[>LH)f
%@&u>$%qHWF.;So<Ldc5@3tue]X"]MY0Zl9X0'0rlE&]<idt9OgWF7`6Le;48S"1rc.IcLGeW5Q'A!=lGh.mO]oG_CIXOZMO]Mq:.
%]&[0O0kV&%D&i^W`lF!QkD?#nk)t9=<LAU"\&kI(n=GK)+qSQD*PI%u+(m08j-q`S/ie)q7.a2&qQ.sU.5+-S;NY+<XZ,W5`'uq9
%Y.::FWb;e4kYeKq'7/.7LM/=EQUiB"Od&P-6R^SYU3k*m0_2$!EBo>IN+bkJl*6R8"Noi0g/pZ^#EdpJ=md\QTU0)0a4J!].A!S'
%7(K/"6QAnF7#&&\U-9^\F_h)>(cfaG#BF0.V:W_j4qj-=To.ta`$Q!T&UfVELE*?2H7H(5j5>R`^_r;'o]6!K,*tSX`D3X?Pn.f1
%f7^SlYd8)m@igWNSc\%,liY`Nh8OWYYDj5#iGKMq<;9CXB/"Re&CFRUJ^]h?3CqIk?mBg01j1#X-&jA3X_GUH'>ZYSI;??aP\`%c
%FSTG+_dFZ)L6C0Afra=S-G8KGAi'_8+to>)?Q@AS[0Na"-0IkWI0u5O7`7eEWgFs&P&`r=q28HtY,]`uGF%JkW0<&qF_*sD,mTH"
%OEI@D(<oSi3p"PA9L>be*kkNp(/bp'O3!E4Zj-fbH%kk.egKF'm.+^s/[aN5YZl\SAr-Sp>EK6F;d"F_"W4O`]e,UKUdUMe'o@(_
%*g5;KVAIAb'-c#$aO\P'1PS&bBmr84DR3&)c[)q"-U/Ra"2P4p^EaAIFe=9#0`Hc2+YS57LqVHSo&*@)*Dk>3SBQ:eWDIIZQG#fl
%]fZXl].o,[,:eSL=:^iP-$5[\R:rA'(%+k@h@)kA%JBHi0P#iL3'G_)6I^.p;Yc!&FY&19TckQ&*a4^W<#'>'M1$dOGDKNi9p?K6
%,,5raUhj7s-"A$%ei14>3"iR]fd\qM*#\"s@*+&^.TM)!LW"P-k>dAW9jNjH<kO>0pJOt>_MO4Nj/)&(3(SXWDRht4#]4TdS=C3F
%d/u/cdTJ(k7@Ze-g<=n5l#Yp]P$&?XQKP_:8#_G87#E,t-Kqq="(U)La@Y2oD6eZ7lmg0%17)R5r?):`"'>#&&\1df:4dX)aJd)e
%[AntTb8ufrSs$fB#"J"n]EoS)C9naY_97A*cGZ)HORU9[=4V1[SiuB@UiX\IC(FUU)Yb7VF\G!^Kr$P.SiigU@WG!V#g<!Zdcc,Y
%SC@4i65+&2ph,5%nVKk5ghQa'4,\4ibZ3;O&8fh9l;ID_O:N_[HUS_/+JZ&:3C8$7Sf+8[#qajJR#1?*Ep[1]#+J^!1dG-gA:P"Q
%=SPnK3oLstnZ,)Fk?`t5A,8C4#W`1\mYf%Lo3PaZ5ZaLo*!^f)&3GH#l'sS^h4tVtIe24]-2J>_6\-luS"t^)Ojp-%Po^PY"3DO1
%::SN<4E$K+:f2In"j/!^FUA;`:%-7tNoQr`$=YB^U,D8P"pYj:ZdHcmG0!,M7WP?#lmALp4"jeKZ)EdU,%F1'c&j&NQ:0?p=-?G'
%7?3B3.fsM4,0a#!]AC@RodoLdj[/3*QYNAfp;cBNfY+tDS;ACVCr,OO(CMD>nA4Fc``J1?dEX9","%J$Q(JEZ\J-o3qH1!LCmL:t
%GquA58Jb*r"\;)48aTtiK^L.B8)=Cbl'l`t-q!B*Uo$%?"a=]uGT-8.*qU_iX\'DoeUpu?,`-#-Qk9qB!-=*=f,<s>FC-EZ(W'k,
%F%M?+'q"GA]*4$.@Gi8H#>,Bn(rh\T6>+P@Lq^1]9O'%3^p4YO'On@&d+E$2j^mD,599o:Re#\XjI*84/4LF9V()88lTeU>8M5^\
%bZuEUi5E*iSCMGq)Vn9k>tK'/=ZVd%OS>aFJP9STUo%"m87SW///8n:jl`[VdB`H*>nC<KXTRabB#ls%F"FCE<P0:SMN$Q\]P^$:
%Q390M73gu)\ZC]E+MY.GbR&%>Z:k%o?>2&A*B`RSOA3To8f=Cg/]qOE@0Rd?fOSSTL*Z6(QtA%a6In!R5qEB@aQFh44c(c+m*&HF
%*sH&;(JZ#O%KP[8X=:nl'65Fhclc65SVKBbk\OP_"'MWXEXJE_n0JsQ.QJ1s7f!OcF&'?pcVm8pEGl&p#kX^k3!n;4'`:!-!Oukg
%+$B7P^-W@Sh>)+ec!9m9`Xl]hP-E7qN:Y0Wil.A`$Er4rPG?L2Wm`eZb3S'am"#A,0eeK!35nO[8S4)fW)f3`DA=?MIj5`Q2_K#k
%PhD2<2[X2o80rjS*Q0APX3RHF,i;29CZ(+;O%Ld=,S1t77pXt>7KXm(/X1i^j:0-a'k1qUQr"fKV6K?B.BTN0,]Oe>heT-E#&ZO5
%)\]mlDVR!;+`_=g*<n\\9UAqZ/7eCu)r\H_O\#&9F2:7fYA4ZK^G6ff"UZ>d7dP0G8^JVr,-u/.\+W)H=NF,']'iBH*>>.,E(dAd
%\_*:+i=,Y"]>b2Q>`)YYr"F*^&2RoLio=[Gjn1<U>h-!!(&ce4;mck=;$g*<2-7G_6;nIpZh0of1Q!W@%c,\AWKnWN+\')2PWdKQ
%dK`R,.ePUpQM7NgD[Kcd.)O;%I5$qB]uPNE,c4,>1=qf/-4icIieNF7Ef1fF8Z<?W$GlJ::PQkcfOWIEe:a(6k/+PP!^,o5W/6Jr
%ac2iN6=b)glG/B6Ml7cabfsXd.a_Al-4E%n91tU1nUoDFG\(D3Rn0qMaP"u_aC)k_!6l^ep64acks@4\-H!g<KhY1-@gJ*K.Dkr+
%qDltt=N..`,u%B,13C[JZL!I>aWs>i67,_@%m?=iae@],,U^=QpQaPq4\Y2`?HJXA<k1dD$dO*-M]pV@9CdUYZ3TqO#WO?(ai/=L
%+)UA%\6;!"lo$.J[1YEcd##7L;nJi_b3,:NiM8Dg0;,"\4=b@Z_8o$aW&O<N(0-2FMU[M)==ri*oNdhpT\6Z!6kDkM=iBYOLPLTB
%!ZM`dK1ps\*DCo-po6<u4oq*B6Q'XDFG\f"MBLZRYUlL[eHKp29USKIm[uKn`OlbWN>M].`[hFYPu?I52A!Pg&*h[1a`o\+0]hf"
%"P[Au6XnB)heO_[C=ABt?j:/&X7j=tn77/T@/5Xoj`l-VW8_cY#HMOg#k@9"U1Y*>2.*#:n1Dr:6]km%F>,PSaBY"O0<>_SfUAcV
%8+pYWJ[;Xb[KC"e.kOntQj@%9faiC1U&cPqh2<;DkftLH+Ek:b3kb;7I>?rZM"TW7^$ei,S(Wp*DZGVG^+#C)4it&o"sdBs'o`2-
%ATk1`0?2MAN'T_QRHR)UV3<\UWME=G-(V%A^kg$0p-Q]r\1e@j=RE>NWF"^fj;HD#.]P]V`52K+iRVgU*=fRn^.C%0V@07X'=jQj
%>YI-;3)ufU%N8&3-T@ZR3_^\.-+^%73M]f68\>a*FQSW2%nuDk1+!_WfVKup9?-]oG11B?Bnu=sR8_\WWfdo28s>*0%YHo;%%b=(
%()+sQ1k]"COpo)N7':[X7'DnIf$tbmCg11GY%V%=s1tj&7R:U^[OZRl;(?\rd9C8a\KqX@Gc!EEIJ'mdD+Sk`G6BiY7&!D->ihZ1
%Y"[YkU_j<5B8!&rnH[LdMImAtr@VN1XQ5_lN.`BA=DFrbh_p)QW+$0KOp&Y:YE\'O=C7l]HZg2?&hc\DBHHTb,u=b5T#8(;05G.^
%m*^+_gG%D,4.K>\&L4%D0^2/ego]DXpm25;APNiCoOp3^-L0d/]>"-ZS+E\Mo;b*jGbK3h;d04EO+6f6YPGqY-i`_cs89@`S6q5X
%If,sjZ+lA!Fo:!,^HMSPp$4F5qcDsY2s8do-Z?_Ic[YfPkbipB]XSm[n\l@'S+A;ih421Wg!S,9_dC'%F)nb7ku[$aYMYjUEJnf.
%0E6KD1]J]uj6#+"chKa(o_OLG5!-IRjn66df4,l:]`-[3NrJ.hnV+#.hu:T%:G1Mg:VPo3r5[@`c.b`,;ns0X*Fl\2qWBr/jY-W3
%o3YaMrRK$Qrr(;-J%]8ke7_pIEBeaK,ei(gLMup6$BLarFUEWQh%[NV15W3[cN!gmc%_XZiI4ScS+G5j?Yu0i[eom/&apZ7%.@PZ
%X*9oF5J9)Qle*$h\2N3ornb;7o>UU)p#bW.]8J8]'b,B,5_^9SronfMo(ME/DpJ@>]<eQe/KF:UqcuqalMAu!o:+4L0%r(s\(pCI
%k80W5iN:\C4G`GY`WDdGIsU[dpWgrCs$oQG^MD?fQ\j#eoY,T&,Q@,=hdcY)Ie=Or]HnOpX'M:":TrZh3Bd8K2^_s^Fago57[ka3
%]D4QM^h"/1?_>"l\[d0q@4JVJNpk/llcZK8hk&_A].P1>'Wde:IfTN>X0XI?4uoQ^H_nl]K.;(AjUS<Gh`IANL+9Z`8K:^??)qub
%N,f.tDZ;Q(0Ai:YX)RU@lgqRj?#H:%hEdJik]-o6mRc_b6d,;efjE&X55jd-p%:8i_JSri49KG5gEcJuhau-ehq9iW+,7KS0.%W/
%T##ca6$ms*oD2O$Im$#inSAi.fB0]6f,#7l*damc5n.^u;S^&<]R0PLr8d.0O3_^YjlR#mmI_7Grd'@4IENV=]WnYaprI?b*dc5H
%Y.so*mFfa.gkI2(f72+fH]kjcK.Qupla,&a(FO--?!k:Y3P!kL0DBt%1NCKQTAZpQp@\+/f`t5jra](D]HD3X_D+ii0<X41lSi06
%Y'Ae9'Y_95@XumM]:.N0W0Xp]hL'ThHM6UfagMN3C4>+*s3NUd(B"0fA,#)W^sMh8Rgt-sr1E_ea8]sXMZ;A'?LP?tA)GNEH.+=Q
%gF%.nA,P<^r9EEhRh=ZC+)`!glGIhIcd%^gH9T\.YkO`?c$Xd)?^-W9'A2cUH"oCdLNtoRN<<unHiC>4gAb`>[sl6Dp6Wh^H#!n=
%L[*QI3Q*LjM#4jrNu=[0boo4N2Y@2Z_rp/05C%=!o@;sUc'HI6WOMEWr8nM;kE">$%ZfZ!<.[?Qdeb2SDjMF>gTrhg5,4BjP1%YL
%MtT:-j.hXo&ERuSf_D[QVH;C$(Vb?=G4n->%/'K>aL!DQAFD+()s2*qA,<)u`uj<.Cb8t28A]jto(r:?(,gu']@G6!92sL.o8Aea
%*nMd9R;LTD-^.E6jcFo+,J-mRp\N.d?X7#Ds*;M'\qb%Ao3u27k-_,2lT(dDDTQ-E0X#tdT(<q28Y6%0qI5*'kipmkml?_KQP]Q+
%8&9)sL4-fR)hKB.>^$,`>9q4dEnT7aq=g!*s$OushqO(^<$%VY%T)Z8%Ar;i?_(G-SR$VhG&_]:9b4l9P?f3:?YjZlI/*X-s(#4&
%HRq?pj`t(:'6]n3Y_=OmIfRj%OQO:0E3,XijKiDQ:Z]"[ebRt'02>9g0$g\<IXCYYPC.E+&cQurmn\H!chHlIs1(R_6c6T$KR;#\
%2OL20$sU5();T-8o6;/dp%Jm2\P""<MK2:<qd$h?hu%???L7PhhiaH,,k4&V(oj/9$emG)n,(])gMP\a:qqC!ICUXjO.5NO0/m_/
%c1RBHk^%GoG]UmAl2,+6o(r%Tc1t_U]W8C'Y%G(mc`L-`a+)-nGlR\Z\)5m/HVB6%4cEU\KV[/$VK$a,[H71ST"_gapDqWd[l.o6
%]W5aPS6qhXCrg9L4h3QNmIcd\oVLfT-KR-*\M'n5gD3;4[+j,1:+j7@q0'YtL;7bLnn+Y1+Y@mqV(XdILjs]!ENq+nrF3g9`Q?[+
%jeaR.o8bb\B)p0LU)bFTp-M&,I85nQ>:/r@bSO&7&nTNMm/+cS!kS*d6;^LM=")4b@c/CFAPXl?>Ai"jH$Tt*-kG[f+Vs13289U#
%_%VYofu+Tu58N?`aE+9*.2HL<A=gqZo.4BKr!sncA^_gXHa[mL7G1b!q@JRj2uIOA?L7PhJ$-%)Vp?e-A]nVUb,t\-]eiM>VIn:`
%IK8!Pj^(,fn@VI-?AG(rHAEM9B-TV*.!@V&B8'^fMgtd)=,a'Jq$nqaqmI>G/4ND=l0#SX8q1[,T:Iag%muaFn?iecCZW@`2Cu[q
%Y9!DlJZh3C^@pkOK5qOT%cZ2+ZaDSMU^+&Gi+a@ReIKIM6!-3f[0'KW/mN8d#^sDhs7X/YabD,:7pWDraoQas7N[)=lJWXu=G"FJ
%3aAFaB-t6c]HJ1j-N1tZ!JsJ(>tJQkR/1$:3<.ad`?-^-^AWEa3KO&\QHua@T7)!6om!U]>EM`hT:Od8C(gfjdWS\rrB.>]hT&Wf
%4qNV/!_[Mu6J9nrhL81$..<e\!=@k<K^ufro#p>hg%.FYKX)QjG2tjom`=S]:NnYph^@IQnc+(orkm.(a`6")#4`pn0Dd(74g.2+
%rrbc72t=GJ$B79<+T?6,q&tL_57P*FhIe*P4CJ'3c5n"apY\0f9ub6Hjk&+&LE?HlGE;9nIup;iOqG@Dm%,I=fO-*'bEe(dQQ=r[
%%0k"j4>tMiF7;7*SN"dOYu%<%F,9_,&!-P[DTs?lD@Ra:)=RNnpgLgZn))-K2s3,,?(TkgO]\4A)u5W3(SAYkq%Oh]+1Se89YWrl
%hnf>>nGdt$/8t?6G5sb8DeJn(s#p.,D/om]n%o&$hJ!u[@3#3[#QO6W-p<^u34MaB8s0FOn"*^kp=p4$nG+,Q^'C;!#Kr_YUsJWc
%^O5eLm+(dZGT!]nS?[FUf[XF-p?^euqO@4Tn`'Jm?epYF02;h(F2JFTra9C(_cG(Pkscrj>HN)r2/H[^n(rFPo\HXu]%qb[H/H?N
%[!KEXdA+'o7L'=S_*YS3(:$Xff/#+E*=Be-b[^r7ftgAU4O%E2VgS'u2eQc)T+!1=m$$rNTE"``PJ<\r?_#r"o&f<3-eE93Xrd7O
%+7*XPJDAgcbriTTs0g>3['\ZB452[;l/[?>0AkSJ<c[]\gOtkS2UhThS.-(N,V8=>2W\#7BH4)&p*>^tQAVSY^HO_"1<H!r0H/dI
%amOi,m1]EjhnC-Uh$6Ynra^4kk@88nZ`o9,bX["s5/KJ8C$s$U1"Cp8j^<<n(g[],QOc96#K+a$n8^4qqub7tR9@JtU/i,>^DmY8
%H`Vc>$ni]3GhJX4oR,YGp;VXTW@C\>g4JtP;JJEeUYU:qr7Rc<m"N:7@kiY7%5tHp+CO*C-FNn3loWk<Q@1`C:$be$gA\jEc`N)F
%ZUL=m35>:,an_\qXMa).`t-A-5E5soVShH(,;j9*+3<hsp[C*/HB/OA9[=)tkl^YPJA-+j?eX$brP@D+IJVL9lg&I!7;ADu<rN<Q
%WC'_chM;;15+R*hTubiHn^(LF8q5UOdFp@2%FnLid=,G`^#r_R?[hhEm"TDqS$U]c=n5M'o=sHKrXa4&^Lqb_V_YJp47a%i#>\u2
%%N=%e6eag7m<aSMh8sJ>q#$?M:Lf<A[6kOAgEa35]/u7"I<6DTn^Z=$\3"K9Zg"8;e+r/(oqij,D0N3om"$K[%X*dYUshGO4%=Q$
%G)S"o37otZ<muJ3GQ)!j37otZD.a?%N.W/2@)pnU43#OeVD+k0mUn`^SjQlY(5qDAh<"*$Dbg;C.5uHf]W4VpDQpB/)/*=MEW[<-
%(FO.IIe2^gs1LM+3o-MCghFO(\QrKjH?4OVqNrktQ>M0+rTX5hdilBSH<6=-o$,:d#T(7f52VNa,/8KnbcL]T]i`UOm6>*RThVWN
%b;\%>>GN!O[_%rDW+L'k^#d+:mqroe'DQ:b>P7W.T+8)$O)E;Fp?CU2l@>/,6&!&*/9lO^UW8/*#(8&?c_'(-q;U5L@)#Lj./82:
%:Gtepqeet"R%Ph=1t@%WP1RNHqYl]LK/&Tc2Z*;[g!*;Ak\Eh%cCTf=D4/8T#CO)F(%AVF3^8RRY7:"C:X0Z(1O.ia1<-qY2?$0N
%j)<ePoDA^!pg)%?#@6bhN3l5`DB$>PBQFDqm\%ff=`&&/#]S/Qp1"F7D/TBNo>Ar;2P:U$[(0IXEuR%%TZleq1A>cUWt+FM6(L*9
%EsHjUT[#]1Fmd#IY@%>*c,l9@Z1kib$i^XB\S[P_GC;).07@`eFFG)'giKO9YX.qB!&EnI8)9Ots$Y#F4aC:h"$YiJ=5jPG9'gM;
%c\Hdo56)S50pl5TF[mp`s6CI;-`;Iij@Dhn?_<j[hgG=)RnTMoDn`p;@S,$l]/k-T$d(]%HaIcV:ZWMnL(<((]L@;l:<_Q63ObT`
%I=Fq_?/=%D/KMK837M$UMmkkHH?i\$o@<LKfCRjn-/AI<Xq'5G?Z!s=^X0jU0><>rXqSJFK3c`?F+p_l:JWm*EOT-?C/u9_Xkpd=
%oB"*Yg"'lnDLPDis.O.CY.+:=hPKTSKV(9O8_F(QF`oM=D52B$BtH/:))nd`PQ*YXI(.4k@4c[\++ANZJB@Df`2?t5Z]&k9brtdl
%O#[b`!lt/E5?DP/"^sV\S@efbSR(@er!B.sp#!Kt^,l.a$-o._"P/3=pB),s5,_ZW`r`m02l:m4MOoQ!FHD?npOR^lr8:-S7F*PN
%q1G(,hEO$Hc@WeIDs5$irQs,3Gf]ojjnf!gLUVbcMYEIXg>&-Lhq]9\m)*?&8[mgnpNmC2`E1i`6e^?9[bEt>s&FGaZ+;<$c>UG.
%4PNhYiCU_D([!`,3&uK</ou7Wp\81MpW%7];r78hEQ^Nc*lgF-j.F*h\Xi11hT#NOUEAjWp_5=$LLPo5.eJ*.VYG9,j.hXo8Y1b$
%<J(*Nep;Zr@*2E,5s(a`ZI"""-K9"bJ;LuFhKS&QIh2KhPkXVas1.5RU>]$YN-kSHrS.,?g[>CLHAYDqnmljO!FP"=E=#D'q<`B#
%CqnZAo+Lhi/&]KKc\U?;*I@8n))%Q`8B-Ps'):=YA:-KidnZe3qgVL-ln35V'*am"Ha`)Dpe!_8p>#Nao]uYGaH]"<mS=o6RJL&G
%8tNiTm/>c1>H4G_8U5X<W"%F5:[Jo\H%GfP]g-\j55Xp;c#:nbHmE4I;fiA%UCFr*S!,KXcMhmcrUEqS?0sN\id$s%Ie3!moT)e2
%NP)>_mc\W6T$B(f\#4Mqr.18!V,R*2SHakRMF2[_IsQ0Vp%HQ\Pn`r]b^XTK_;8BZ5"n`+Ha30F)F!63m8fB6Qp)SF*=dAl`Poe^
%_teZ*lQO4;j+LN`U_.u^&Yu&sinqHHr7>#RXV_]"16Fd#h=];?7P>0Io0V]V4T"aGebWX%r1i.tNILXeIerHn(Y9JaoXb%aB0UQ0
%^\I>[gsKclr:ieNDZ='foR$FiHeD>3]oPB]DCLZb1h\$[,'he09CtaF^6YP?:?XVB)#ET'qR?J^rqc(LqrT:IrV#ITr*O[?H@5Fm
%5Q:FJ0:E:JAb@VreaE"OOF'(#"?lW!L`Toh?K2V0_opDI+ejIS=r1Wd"7uMj[bb?%R_mH$pCKh*[>Z/1lS&2R2ppp6PU!RfUTJ>!
%8/ep;.M6B(^)t%O%`6EK1NcOBb<AsW51X),=G^)7bFE<3qG/!skcV<]D9M,pE5s-9X>)XXL6pTDL;mrO8Eg"`F]E)5`q67jqU1FS
%*rT*m`g<ZLlF;hg`]nPc@dm*h<^S?)o_i>,7-gWFR$u=;\!Q+sOm)UP/Qpf9Q[pL=a1$/-IC47cAWFaq1(LlG]K^:UgT,eGpT[K+
%3?`X8_7cBZ"8HmV^!?q)\[fsKJ+ED2?dSMVg<4LSIeh/oS=[I,r8[Ip2"^e.F1MRJO+[)QHT+DhVf0eaH9R(kKfo>OCZkJR7`UT>
%I@NiCm"<W\o?!f%O0SABAEZ%5rugLLO)598b36I`3mALO-<bhc>LlsR6mnqFYtS_V;jN9KUh-,\m6f3GZBEP`_>%gUZM$#rdB2H-
%dE*t"DXD!g4U?IpYoatoh2Qp-1h&[E;(o6-AXph02o3u!9,Yc]SCGdT0t\-6fAkkrM_^@`Mar!WZ)rHD$p8]>*]R=6*l1@-C[dA0
%HS8l64P$br]qm?PefYc@XD$mq&@%:s*':l1bKH61kWW21g6EF?#-G82`&N"$NaV>i]'VW731.XH&7[67NWSa^EqJ9m3rPXGr&?Ha
%WH_mN@o`=-%CXa*!@46uc;tu94T7nD(QLIpa\_5XHN,ad_6&rudZ,WB_jHGLZ%8U67Ybm:VSR,6er-:<(gYZOejrU#]p!dO:4*#.
%5Bn=m]0-\\Zj1O16S&0"p,ojIO6l][A9L'oQX5D$,oo=o[H_U$2'4uk]\i1\s2U1Me;o(TZ_PkG9:k,2L$aloWC9m!2o^1WE@/[6
%I:k_V#=jpNE3*)2<OR0pT8rn0(f?4`cn`:pOm)M*ERPj_Y&S%5?kgY+5I6]1U.s1`b;cW3bCQ_SFX:5.g5h!KrN1=\j%H"kPlp?)
%o@g_;&;9Y7;jrN0grgCb6^;Y#A&='KISmbJ/)PDI"s!D@-=Rsi3Ke=R*49?M@t@B0$"&;4$Sbhu+K=\;(usOWggB(0q>L1P7'QtE
%G)=E?^a_IR?B6[U)Z(I7Srb513oqNuUaA%-l9.c,AaCl3oqXa7`Q_m5HhG8BA*$parRfZ;!mfNV_'@;m8h>-3=p43;pmN/WJd&+b
%*H+[k-`m+p@bYSMp%YC-5E5+FI@RS]jktf3X$W/5a1!#53jD+_Brqm-k4ITY0eOs:Q#S5"G]5g;`TSPC-6jQ^l"DAI)_YB/7Aj!e
%Wq$^UDX8nHQ`^$2lSIs8JFJY2K1Pp!3+RG.P&eWQ:*tEHindDn.4V56-aF(\*S`6Fm.ep^II'WlGkrMp.f7Kko@Ou%Em7\d4r&)I
%o%:QRMpQg9L!/NGZfL#aWjFD)8KMNkOhfA$&Z,hjA^KNS&:(o.g,:d:C'V!n?R90XK?;'#lkU#c+_L+$\JfPq[FQ`nkd=i(X@=lu
%dj<TeLl4c@>Y!fuj3?U.3G-n'rDYXa/49#cQQ_BWNTn0oni1qjR6KJEBfn#'Y?Bq:EPI,=Zd8ITp`UYc%-+8h5&B76mnq\QoIpDP
%,3k$sE@>pk`=%"Ol!$3'_(\4!64]gGjbPkNo1""r"FleqU!.(=Uj>HEE.S@fZj\+9o^%>uHR.q=93'r][(2()b8hM)I2!]>&6kLe
%15+R8!n&37=q9\B[@%T2$uESM;')ggZ+W>([t?ghju^40\[BQ+P).h%+2$'&*62V=N3^-,s02iR%eTKHI/]\_9kO3\%in75o<l]2
%01;I]'P9g^dkf/j@Sdf1=K<"RdX)3/2jj'l3dOZ@Hrr^EF_CV'-4stVb]m$^qtQ.,HN7Df^*Koa%-#Y/;g*OcLY13I?[D@A5KFT]
%A0;D"c=8s3i1</Z5TVs\&"^C'&*`k0L1MYIiT[--G[J(scpbJEkVdc/m:\L4\"ED2W)tO'qq/\e``o)H^Ft$Wd]VKu[F]\$\hss`
%\Q^+bDq]&C*^kf5A!1E6l@$=H1:b6ujlIRnBbl8@o\(9+m9!JM+KgmFPRLP*)<@=9s3KQ6okKduf4"+KoH3oUJFd^ul[FDWbXQ%E
%kl?=*Mt:IU-!DMJ9a3F#7/oGd>isaI<LojX\_1H.#%*7opK?D1lS@))N&g%ohV6'e[:UmLlpnRGoci_oXOH!eGVcAkA'(<<Lb7H[
%[aK"];tJ\1po+??s"$,!F%-(<aPYDAn(nR^\kLEL>7nt:gT('Kdc05?VFpeS=2DNC`YWN[c`a*.G@Lc)jlZ9W9>buM0k(s4c)/hL
%om_Y!`$a*Q\\ZQti`r!ZT5+q+r"uEl`o9=D:sWm^lAr/2Ie/4\E,Nh]Qf&bLppBYQq\$)odu''+1^dAkpD1J5o3@mMaOcKVli\P8
%Ym9(O`NhPqMT*>F(,*d3WYWA5iM1B?mpRi_X;T[%ng1R7-+#3-QuZSme@T$'f3.Iu[dMqr&j(?V%`U'haMW*<4!q%(^(u\Z76(u[
%ToQoY()_]0H$j)8,oFq.Ir+>b9e&.s`Q'I(@arYEp?Y=)6;gdq_GTgX6HCBj/9@E2mp^rk_b<;-dZ0;C30._)iuH!8p-QA((ET=&
%%]^_rj,]"'j)n<\%K,o8JWc'QoUgglEctQVN(hS2aDoF=>hLc2!s7t7I:?Ot\7r;k5;ktBk`Bf`(bPu=B'h9lZu(l.V):KgrKc&a
%mtho,be_X1D\Qp!oA>VRYs,dKNr8ZmEI*"6+W#0:cFDEi[W%J]iW]fkJUh*MjJU3B]&r@@p@5Nt%F\DWoEn>!NqN)8Fc?Ej@KOlp
%cBqoE46!LH+IBZd0c;*-X+"ki^sSZm3Bsf;T^HF$fb/klo9SQ\]&*0Tq=oRLo0)L;Xr`%9O+6[b]Q8,-FNWH)P4'IK0;%mTI03+e
%\+nl;W3+u=Zp-`9&:YGrld+)UO!/sRjhGjsI-Dt\Z"1(!Ndd$L82Z?i'FO@.Hf3^Qj5'%_SZCsMA;9$28>?Sgno7Xk\]$;pkFf`B
%W7Hf>NH7!#pfJnmHCB?]M,*ZqnoKOWI[2nori7b-O5uREYs3q/qNns5B]W%Us&JrFEIA]^UcdsfJ*%CA?dH'T^[pe^"91SMnYuJj
%fh\5#BK**fN\gF4g\3!9n[q4AmGn/p0^J\tmIUPmmp0l$?,,nm+7gl::ZW!ZB;(itE3)4j-\Ro65EDP"J#'_II[SkIMm%XkcPF)j
%]*>BW,Yj4T#snX&=NIG`5D1D,-H?.I1DAHJGcllP>DFA]IuN;P;fnFoNi\D8O'TYEM.lAr-:U0;Z0Hq_!KUUkDYko!2aHrUBDf-c
%.kPhF\u"jU0/:AkKB91*d!:K>[alL0jRFGlLX:Qa?dDLh^*1Jenc?Kb:[Yf:$^^MFFj-T'q5hQ`hgbW;s7kbrj2+ifAba!?(Nk@g
%<`,cN>=mIqh]W<1@KQT[^`3Z?j!sX02NUcEfTA["raRAN:4*H1L*[g>ZB\3JhU57/rt%tlc6*Pb1GD^;mC&1HmbDl6k;-p:N)6']
%]oRh?D1+J9H1fi*Q%seDQ>^Xf8cCHY/oIgE"ng8EAD!+ENh"EbBTC6;dDKu`>3UugTf[5*j^u0:h(K0,nf=@)dSIK#FLNrcWLl/Q
%MDmjP19Ngp#Y-k-%H0e/Yn>\E5IZ5NcUL/LEQ<[oS7>kECg;Ms@Nul?RagWX#Wmc(#'nK?2M1LXLrM:UP)FG,5CWIu1P$VS?T,bG
%a$pmpEh>@IIK-SbcGM4qbgDpsHTm&Y@,?HSkNl"<!MYHmpXf^4U5In0^@$W?olr[\o0rLipVBX\14U_s'5f:pq.%K=ik:M;a>JXM
%LYn<si/&[W1Y!ceVHqX^N_s7>,Qm(Ndg&AP4/06?_0fm.mc75lb:c,2VVKV2^''*P@dM,8BH`&Wd5N2"jq3X$RZkY)c-]&B!<de<
%9LQWW#CdN:hTnFPa,p&?XN3,fmNFY^_3ta9Y70mPUCsGXq2'`re%_8_K\KaMe06W*Iif/#le*2,F50;UmKs#*nW]E=:iXP_:Ukjn
%DVnfUGF.XO[JcHd*'W9QpXo+AltE8Ak?qqK`LMqLMk\g;)+0UZfjbn<?ei\LhfiOeF2`]uG]>HmTGen)ZtE?nWRed2Um6Ice[=N]
%3d:%adJ$E-]1;lsj[&o&/TLpNWpd<#p,tf6fq0/O1c\Y?=E2Xb7JIAoeQ@Of464l#@K`MQAFsVlI=l'9EGk8Zg44-!Dh4qMreEH;
%%Uuf7UH*6YoSEX/dlGuhL_%2&J*fUNa0BOIbFO'"TD\40Hp/3.\P[kSL3!s[b9mGUa.%LIbo-?H//o_+`oM,<s2/uEZ=4OV@FROF
%Hnt1Ur7(0F7A`'!nNDqs'IT?;)#:O+0-R#CL=erHUcdr;*"=9)b1=]1I,(@feR&j/+8GU-5NachK2:ld.h:Df6MUGm7^;1?FU0Ta
%peiP,:7p,N^jF`g[%Mr!r)?Y@5&Cns5Gt1fS#%p8IpSd6RY:Y*^Ck_`o%3a<(V8]2HO;<6r:Nq80fK#FKcIkos*A&@Fbl)Bj8@^d
%rgFa`s80dmT`X=_rL^hjXiTCIo=)L3jkp"4=24Po#[-'1[/sn@NKu88^tKiPOC'tPJ+:#2cOgjS2a76@>PoQ`l!qdq1m3uVpr/<L
%>j'F^_.@QBr9o-O[J66+p`IMJAikD7pOE.pB)&PX7/8?&\&qS<Y`ZpV5X!"e)?Veb/^/%`Zc>ll]g)3Ardf(hrP*Vr%qb1EDu1>o
%iB0Rn!r(ke'V`ls"JVB)jq_E&kOO&ZrK;*!lEd*.5C]eNHGJ]+(ZY8YO+,#srM:f2Y#RO_l/2L.f73CY5Q((2^[cq?s66_9oueF@
%rboWPp9?O!R5+FfY@[n$rF<JJ;kCoeVL/E\$\lWZG<bMKjS=0G9+pAnE9PR_s8EDdrsU""@8bKe7JsA9?S?Yl4B&o:biO-GWH:(*
%^'De@O3)hCc-bQ?OIbjT0`I^DUX"Po^cE1Z\`1rGc7bZj^sb;^&+E#:fmFYnY>%$sZcbBBB3NuWA$^VJrm.fH5e<7Ri,0;=prDQ/
%egE*a0AkgOch7.Gc+8=EA88mZ_XKe-2Z[8ZR.OS&,UctH^<>A-n'Z`:\Z_T[nJA:qH.c]`l+HU"1\U+3U]1a,Dep+*S_os98^$Gb
%]mNc]S="A0cG/(+dE&j.S$qB\NRoc57ZUR*;$Q^hpclgMb<X?\,0r^2q?Y?dQU.-s0*AER^%7Aik2RLm"VgYI:h/C4@JlbVUC-%l
%Gfb,f8-C/e]J$%3an%:Npue!1#ImD[%p.\#QU+PjQQ^#Ar>3KR;h%c`C;:.,7BgBX_o$9e>`nt%#.V;(_9Len!bbI5^<)1h3o=L[
%?)fh.CX\ZaVfG!CTg#"Rr0eJc>M-+0-1;9BLV>nN'[(dYi*8@/#A+htqr<*kSg"s'mlTOb1S$Y@H]N+$o=0C+\3F.:CK^Z4pj_jq
%Z;lR6+YT]MQTkg1e#;kPs5a.Lnr:F5^AP$KV0pk!Xa4cTb4"OY_`@gt\68_a:u@[MmidLMZB]P_[JH(NhNR&DMq'MIQER?5L]9C.
%hE%P<)&;AIZR!NIKU3S4pK.SWja$YYn"*5KfjbbZ&23@^Hge<2J#9o7ooQ;_G&0>"9j#eqGNjIhSrXWLUWh?7a"eq,Yus7'^Q/:L
%/CQTm$ubI#6(O*$$!H-1Ju6OshcM4i3"FNjSh4*sHJN(j7I&N8j$*M=pclYd*LD/.4g8Pi-_4?/X2D?iI6Oi0,Vi`?k>\&oFtO%_
%O(uR;A*88$qTlM:=DVq-,t)aroI$22@0E.i6=8b5*%[`/iCGWUD'>WL_[9N#n%URSTDG#iX`AaLW)OTaqT#,^;NC=&r&BA3a6h+,
%G-4hP\+f+Vas*R@=&pm#Vq:N]%3sU0QOI=%4^u,h)uU:A5l'X['dsC%b?2$J$d%?BY<MA<D9f6Zq)P)GfT/c,NCH=0b4f6[s(D*T
%<*LQPA]O@rApLT2TpOF]mH<TOpuqPB5C":ffeQlMrD1H5Gufek"kmKs`;NBY,)9l`g7--C;E6S+TDdGTmJ&[O#c.,jT]`*?Itj#Q
%j6ZS_h0>FFr;KX<NL*4X5;-A?Y4t-ZU-."_D,tR7lEg$\oH0>q%A(m/lgl-gk'<;JqCA3,rGk;d)c30IT1sKVR+PpqI'q9iru%,Z
%_7oUUm!C`gUONmqoTgbIi71g$o5/kag:oHAc`fq?JB\rm4%4r-mueSF8$'Dij)p+hAJEHT4)FXneTh"_iI?T#)Y;_?l\li"N\d)X
%Ed1RNDEMaVS]gX"Zb]K1j:)lEX3X<9;h%[D,dP\=b$TPd422?,ju#7o*M7Su%.WE$$b,59pR(UaHmk-tmj0kaqk%aB\kb+"a4<^3
%HZgn!bc;$T?@LiM[f#6:Im2h)nY<5OqX"#A!ALG:M!'9XiK"rX(Ooijf,8lTs5mA]Umq&ciL]f2_(R/]^)6'_2r+#N=j@Fl([P-s
%?g.Qi&f1-9dnN&djXfSBFe&A*T5P23Yl3['bk<,7r%J&rn9="Y*rhj7c(?E.qB[NJ$a/9OcfQ%l:@IV.0<JjTIs<i%'"P@#ZFF)t
%QT9D?,:k19M'VNbo_CV5=P]S3@l=8oL]>=8)#g9:H5Z^S=NmDS48NFGFKBEDYs?#P5U7GTVr[h/?.pjj.mAO!U&KW<POiSiCT`0_
%bMW@!=24XWrc^U)qSTN9h1oA:nE]!0/RICcqpdq-Vsess:]Jc@huET.hu3QS#R83HnNZZlTD>NprU$fgoC%0!^qe-Wk;@_YgY`#"
%nSl5Dq=DB"qd9A_R3hm$M;\EqLYn[^s5r5;rcc-U:M6JQR]#Xns#@B98qK*F/K:X<qjQ\3^6C"cnc0`k#R>D1_RQ.LH`I^hKSj.m
%K&sc-!%@UV4j*SS$qq@gnDU-YB`L5?KNQj:%#Z,#;2p+KgN4MA"r,]45eM+G!2RCB2H?YQfP3#[+<aM+_p.0t!1i;X)UHO^#1";G
%0iJ>g\sReXFL/g-M%jk%[h4>r=jj2].]BXjeAcp+G/.Tb];E=b6cF/;YQEp4KH3$J(<^;4M(L93r3?aAe@@\,"*Y2eS2JK;Zuu2[
%@L8MPk;kq_%N,d':"YIb^k#k2$DJ;[!+^8+6YKa;mBH0Q#V1E=`,QpFE5)G;\J"6sKn[`@R(o2L0C<9BTfD<C!AO_$)WPE(Bode]
%kfTRI.^"%]_CC33bjcJ-$&$A!h5FabY]g]:oa&>]%.Y&HD(>okB?]Z90XYmVbnQsjfW;/GeHi+Jdl&<1?IB*jo![c*22atl7>l8@
%5]>MU!<-XI7PaMUeK;%C8N4s^jYc]C.d]F*irZ\.o>jX"4:;2NRe0KW8:H4`S5TuNX2VBOp<,cfFe9>V<VQaNeriRk[6]mQ`!1pS
%=`/-]RrK>_AU4nG"^3<j^h7G4LnnE9i5g(i2A6+lquee7H0:IOn*MEBKN:O(`JI__BR)gdd;#1C6krF6hU#ggoaF<Y('BN7YO,_<
%g-#XZh#dbHBi6TYi6YCTAf/dHS%L&0iZ)AAf$b^\?Dqk)%?q,q0hju_\N"6VFHh5?Le*pN;#sNFG9Z@^a/PRQ^rj`:C!n@?GC5bn
%",'6^=qi`di2']>q;Nn5('kr65cUpOIG</*h?k2U;L\$o.<dG8X]Z%H(l)+TNj@j5h-?XcocR1_SD/$2iVZdnBE2Id(^f,->O,*_
%)-+>eKRLgsXaFd//5Hs-m0%5ZWn'UUVTjKH=OjQ!ogQKMW>YZW+7$I``m7X@,[BF<CY;kZb5)a3csms/cnb:*,U%M0Df$9dR.cT$
%,[e0MBZeqAnT/;Pld.rONYE7I;*n6S&JfY+q]q<%:/<@p#tHn.<)$q,]GH@E)@pfTOK;Ni#5KG'@kt'4j=4T)0_)C@/M9sD<l4SS
%XP!S;IIq-go#NnEpRY%K3)PJU57'o$5p;<PLKL;B$%D'&FO?b1Y3>&Qb=YH()8qto`CgBb?At`j20H8&]f[cY%a,U<meNFNAUY_H
%78DN&n%COU.E@e&JCPRaR9t+DQ!nT>EUXpuXPRRdC;Q\II"\\mUkgpb]:(j#hYrMIXf*e0o=%S![5C9#X$2Dfo^8=rh5j@Z?,_.D
%V];.TU?#D);!q@s)*k<qF<h8@XFhLtaS(_pR)@?]J\ILqS!HY2B<%8\'>O;*TJ!smUP*#3l*J<:8Zl^eL-5bAEJsAu`eT#X,3%+S
%nRcRA\i&_M2PYmpR,T6]r&Dlgo(*2M/cApW>9gq&A:fpsHPOOto!6YfK=;J*%r)#&#BStj]Ar\"bPN17it33^$oPSpbF32hGN.+;
%B-CXK!A^RHSH3J2cRX6)2sB?9mn8*63W>sM[U,?FL*Gd/G'UHdW^qEAOt9Km/FuCiJ3#i06D;1'bFmapYXL0:H(UN]BPp_r69)R#
%!,SMq4mWC9UIRR3Sk%eQ_e>PE0[H&Mob":mP<DDWXs4pd9sGuH]eFiE:Dng>kGH`kb$b+N3S.f#Yn$W%C)!HV`.[LtV@`)H>mP(k
%O-MFOXL_:6]S+e+&_be__=UBW^d@@m&QKOif-2[8&`RJ0UDBIE[+ej6ln[?c;>M<ji&<WffZC_;%'K(N<ZhGMJnC9mO#*'"!T6b/
%[>j4cl.Sd\2")d.CLr0[-7bgaeoBE&?\SL(KJju9C+#onYBNi3NX/-aV/T4P,"Q9,AQS>t;EhZ>">X]p;T1$,$^i1%2YHCZ&@W#*
%q7Adqp2l>o%NfIT*R1^KP35kUK[%,e?=(tFj[&d#j]9TU"+W<CSB"d<J#t%=e`4UO1?Y`Fa6fc:^a<-kX5Wg49$jfBh7QJd4=G,Q
%CQdG?rM51O\u_'J5K?a<dBQhI<Vj\BE[(;pS/!%%_b3S`f/6::h^[4j2&Q6dd[Y4G*6CPoFPo.2;5>joPq9B3ME9P48%4hUcd'rj
%bU*Ot>14!#!GNNF-^)4Cqb?[)K@G%^msj]lX1[YS;4)k+L)8!\f0?#+lb<uLs2U3!BrD,*+EA8mC%^LJ7_TN3U+MiPH1Ac)YR+C:
%V*bS=jI0;_)Seb:m]B257%'5$,!)V3f/?9](i+ZhIssM$;U85d.jKWq]IUek7AjUhQAPkr2j=#\k>=![E+A;RJ4[]598ee?Mi=5P
%a1?#$Y@0g<Lj$n`oL=hj#dZNH1)=X*ZtH>\UqF`,k>CU#!-GjTM&>,pe(Mq_;^M8YTgg",C:S4gr<pG>)g3oA'\hj^c/<WT7#OQq
%7&$iR@omeeLf2`36?c@8Q?g5GPfJNZYmM_^FF7NW=[OH7C_<n7Z;0QpM?%h)>JM6Zkrp^F\1(!`+CX9+>7a4P.tma'fMOVjcNo0t
%.t"GOil?00UH-<+U[CU$i-\8EUHIGl)?CO:Z\6?G0N<WV'V0DR\sPt<3)X65<(V@gTd\WW,B:./q800k?;C;W;>`s&:q2`kNfQH&
%IL+S0j+>6Bg.43?<bP,>"-e%qjqf']b)aTViZ3p9Inc.m'WT*9_G+oA#]EBJ[eMEQ[]J;D=Oa(2A1N9PWda_QiHY#$$)2GtCVIks
%nts++BOe:._Is_5+Q[]PG%Y])\c.,&OcPm8,eS%%rbh^s%j[,.)Kl?lN(J-:o#gSEn6)J&`P&Z]f?G2#i%*p%dq$16W9>64&M0)(
%,+"1#Z;DSsGuZDI'=]l$,h27,-\/b,)&f(bI1O'h,b[HNNI,Gfe;1Q'(M!K0],PUGQRNES2pPChr@UX])s;<T*]*FRk],Q5Uhgiq
%Dq+/Mm:Y!%F)hg!Ask2?Yj6n3&@C\d1J+O)-RXp=-i\sa^<EMAIL>"Q,OB);K.F[&k(e6!O(GWYFm2lR]:^j[nsjH8,/]4N
%97IK/.dA.D>"cH7Ueob&@I!)G/+?`(*"4YgKUa<Z7h0it]fg>^Vkk>JJFHYj(@m4T&4TN4N$3efCg"5Ha,MfiP24A/Q4l_SQ]bj3
%)E[5s>O`>)iTQ3I"JZ_?$dC.,GH1=]#;UNqT!."r2"r9.H<YAka`\)3G*t6&j>@[u--CKj_5+i/d+IWjoqQ__PPdePau$eM^)%Me
%IRbqN);chf=4uD@,U8UL3Z+M9,Vnlk`Ra?V=LFp'YtBLhqAqa"$ua`4S!m2sDZ=J1NjoU9g4"0/-r`7R/)9of@ljrS73.suojXN9
%,O<#)QL9Uu7X;*#CX6aGBp1tt9Nen%moD(<>3Sit$C*n=9V%%JQd(jC6X%oA9n"TZ!U7=LLq:#P8^TKHEN`2@[4O55,E#)4TUUd.
%=:/g.L1:t4o^qQP04hP*@GF$cV2>.>q(oC/\r?(O"^;:/2R?2nObPY0\0q-4i8Z%Q;!aF1ba-tI^>a8s65p;bZUc_I.0&YD4H_^=
%HbNcC"\RE._g=sQnTZbVRhD2CKnm=p=$l58@Tdpn8,uWR`\'dF]/\<8;ENiQ98V\Q<;V^?d]HVl6k>.m;Qo5TaYjN@nKf-3nA;]g
%]n_[apU#U6&,ltkP_Hoe*Vhn3GkuA0/mX#&W?&G"NS797j;N!n\frf[1[@%u;;ZahCA@cN4g-dt=BZ\.[cFY>m:(9=C;QPnkQI-U
%6&1DHDbuNtFiWb?PfWsO)SNufZ;`<_j+a46L%tVV+#-Y]qSq)K^!YCXf8\!h+"5e'K#JsQNB.6TS0KR!k-O<B_<&&S1_KJ\e\2(]
%#4Vr`%H53eYYl=ZO>&'%rY2Z8`WC/JUjH#+ajOf>&XS/(j7@d:+dIKVaLi3443l3o>Y963V=[<u[,q7rgC=C;YU%=JcZ*h]3fAZV
%*3V/,g$,"M8*7GQdVO"JoqUh\:UgPLhM'EPYC9fETD.17s16X!SY!b1+[3An6VhMH-K2PM@S%!8;&ID&P[&Tl^L&Hj_[AE#F#^*L
%Y@*WFf)bi]?"u]:-;3:Upl]FE#lLd4A)&"sQN8QD%d+R847`=o<I6j2ioW&g`c*lRI>OD4TWsM^l8BlB_@o<bL6\8[/Q4TrYi&*Y
%-cJ!;QfsUi<LF\<9:4`X66.UZMB-Eq[inJ+CP^KtPLnhE_P=k#?d;;L/I1SN=<[+,;@-mE[>FBsXY>:3VJaa>*o]9r:W/68eIVNT
%7,,RU"H@1&/9Q^]POI+[lB8+OlXT]F.6'3Oe3/JCC(C8mp]T[-<rGXS.H[QN1aJ8fR"MCMo(`?Z;!dbH!<'R&Kr(bg'i+i0)RG;'
%-6IlRUYPj2:JqjOFlgl0Y&/B5*]`eO"L]MVi,7W5pF0"$2o6cehN??>EU(;7@9XpY;Zss&%UQcJ4'/)2WpT4Dm*j3I*ug2sZGG=;
%e/m)/*C`9*0>J3+?1X.*"0D0-JOseI$Vm(BZT[]8K&^<$-aVKD:qf0f`_?N$h!>(/4`:g2q*7caX:\n&ggoKP$Lo!!AX;MpTfM=o
%2]rFQq+:7&E/'F^r*33P0+%TqYQU8ai*BnYaH1\5J)>?1lt_VBII2A%+tS-@H%?j3MFs*Bn[p8N9lpCM$_e^AHeajuU!G!3RtV9%
%6D[D!QJme%*!o@ppqB`+F$njn>8cr_6?n`07@]rTLQ3]C;lDQU#Zu&OJWErBP>.QaId@,UDt:ffom*dJZ="gh+e!?1a5sf3EBn,9
%]Vh6jL8Q7H^/e>C2a&V6J63XPc=DDKGm?<$H3&3+U=0NL0H9$#7I&*@+@2uNnl`.AEu4^t$2rdeV.*(bkrg^/P/NDCY0IET!2,#J
%'Jl^dei[[_[OE8n.h;"dVW]%NNPMo:g$b*-i(+WXmCVb(in/6g&tGEBO-iX*G*/>k0=HY.d^Rd?lug5%Z1gnENB<@o*Kh=X>,@`j
%-i*j1B!A(0mXkqIGp-W8+YQ85+m,mXlV;R'83t^lD\2Z[lu?N$bQODg7u_<kVpa0fkg.nRBh&eD10I3K!aiR(-LNrnNX6$.XppZu
%:k]+hqe?gMb6l%\k;\O(^Z0`]2H_GD#ORVa\1S-L^)@PXaO5V!#obFLBI39c+$Ck;#Yq.-<IX3J86!:DiKX9I$CR*Cpmu,MI7C_;
%-,h,^V91SkWj)p!@>qfOp\CPGAB,of`$QL0gJ4IEDYtd(:Zkd.>FjZ]<stN9.bb@>GSY3C@o-P`..Sg\2Y[o[hAr;%hF#CD\:eY;
%`b-Aq]n2,O6.WekH.e'q)K:B\m_Q$h!J&[(e$Isd36:P#RJ$nZ>)GCme`rT.VaT09TZ?as?H;ng+;sR-LT(_+XA61^Kq4R"UPD>E
%lE5PD;TQWZp&HEc?Y.;]b3'lRQo#!%&ci4l2sj1U:74i3NJrW`(f<5*2Jj$];3V?sjgu4gO4H[I'[.(2++ru_@\B?G^qUM*+cVDj
%R5[p'fILYQgN)@B:^M4WDOLokTIb>.3(^l]DHB60/pDZ=gu`%&p'O="Uk'8FCeALcTn>dMB<q\I1SR5Qha(s@#ig%P[1:U,Ib0]h
%s'?gTIW-'1CWnJ@3+^8@KT6(r*@pJo''%j`Vqc`*1k3q1G'=;oh_e60hYtQ+Loh/'?;nKPa#3LKOWN^kd?Ig);t<eudq=[H+#gSG
%AL8Wo^&=-Y//Zr`<gJNC0BVmP-ELA,PimZRl=:U@BBWOeoe@Y^Z_"kPa+]3=*2q)?#fT7-DE1>%AKM#I$CIJ+cI@LlXVRV-gd\rk
%@rho+iI\WWcZ4CQLN:C;-ZIO9^"sk*L+U[PCqt+/aK7hV]pCES<,IUj+O?5e-EZVoqUlec79;=Jpeild65`W4RJ(2;LB;gtf9_sW
%[QSZ]h?V0I+))D\'Hac:H:kDR/u%ke\MJ.QWUZOV`\okO_)anqWnAE\jRZ^Rf`sEuEN^lP2VCM0$3V+Eq\%S6+T2LurBeJ2DCfl)
%Vu\aB&j[B^4"Uif!H0W6s3qK._,j36M$"=!kjNF*RVKTkM=,m&ncn3\U.TqcYoA[1!C4+_:89;u#%:aJ>CU]a0G+</^31[6V7*6A
%d_*\?hcoG$^o&5Hc=fQ&j/9`'kAUe\K8@0+FDV:6ThNU]'gp/sU%D;?h'uLH[OYKc>8S-`L8CFn,_Z"dTfC-"Aoa3irEs91K$-hE
%@[:/gZit=!n'G-l+`Ase.P<-bjFhZ]WmTD-]>hRV8c;][8/QEV3#9a!=llb&OZ?hZ-H_KrcM[h#Y4n>fl23KO91n!e?(WY8?cLIO
%[\S4(Io)M+-GX2f2rQpFN$*dR;,1rTSOS`[[Tg:u];BK'!YnuWGpCqc%jZhj:#OSL*iSo%H8'ia[C%;^e:ALqg6a?t,t:JX6Pa^1
%:oJ@sjK2/o*uUZ(jS[SqA#t3nG6+g;YZkJ)oN[=+4:3bRA"F$/Dj?6o&W9aPK>QhVn,RPB(KQF,JtU:n9T':d\H+dF1W$49?F]2C
%9^c*#!cE"K86,;Ypgh)=Cm_1;<e8b-I)K_qo,4`eDGt9crG_.s5s%hOIVZR@&`Aoj+A_[0gOG+&AIS7KHt+=X!)<eB02NeKm@;4;
%LZlH_f-;!,fJ^fuQ*#aK.)bNT,6$`qORZC;G%"T(lp$_RB/of8oko!uJM*Xf)'NJmJ0D?ec%OE5!a`"ue.J^dom?tRBH2]hNgIVe
%))=>^\5sQFN:El5Q+ES=hM2At?+:mo74ZA9#uMEY$O8/Y5dq4_lWG"HJ7Q@@QMl@0-NNB<Fu*I"P*Zs'"DGm=fHVSZ!CK/K[(8X*
%\]/P%;,*o)l8jS*^Y5t]!-_b:)%,L01rVWEPef[:)]fU$O$t_+L?[uj%9\6b6jVFd0E?U]VV?HT;1q8E58clGm+[LO`u_GA7;uDG
%9,\K,P5R[Ug1_14pN)$KQC<L_m3o'L(lq)aL_"][^&>1L9c_&RogH$Om+q1)f,h0b/[4;_b$-SjZo^5Q6($IF*jm@PiYDSDi[oqc
%hp&!%%!QHoV#?b-!`;C\6_@8AF#u3H12Z]r`2;_Sd0L"kZGQckf$bBu_P4\)EG]!`iZl,h$s_Mk@8K\$D!W\[+ht9*<_;-31UajC
%_K)CX>5+[GJ`N%[5)VOHm-lR\:FJ8tTf0fNfr5,&?T:cFh9^pI?fha+P_QiKUX?$/E("IU`dnaW&nS#;&C+cEh4Mho\.O#gG$W7G
%M;@4sI45jiTS,-GLaoN4#dh:)%V-l[O]OW4jFYWY,/f6S1!CEdIaq/USkM<%\&.B)IM3%%=jD.5"JnLL-U?QeQ$+DA$&G#umu-30
%-nR%6Q^#D/jG#o\Y/kR5%ASeJ<.[g".9HW]3*pUr"4[;q[d2tB*O,MKXG/F/n%+'s!dg7,ib!192ENbi#iO(?ilaf!W@_PX\\RAX
%^Xr81hha`O?bEh1Z^]RVL#!me#i"D([;,'Om`CHp'bD*\\jud2"^a1r:LQBH-XK$)i]Q4?A&Z5JFu"ff.hIT"7T*jo/s`s'L^FI^
%S,6SFGpjs=I%054.]GdQR.)sggS]KN8U/IS5=(Q/=!FRNA@D*\(fhdt>b(K'bsujs)gl3-d+N2"<N=PDD^np&KKBc=)%-JLM6c2h
%X0ti:APoT<"hps(6(68(HcR)t1e.'A_Xt=<f4I!GJ4iDofTp"kiKM#Cog\+]6YplO2i7&k$Cd^%$u?GX':V2a*Pm0F-kE+<6Mpm5
%mQlb5>Kdr1NF;=:4<SKE<S>Q7nFl!E?%bN0iGDWh2F5Edh_ZH"kt.Gn)=X6s[69^no7Rb?[>9kWPT\k@;IghpG<IqW$2,KsqO7ne
%q)j^2rC);)I4Yh):k[(F98ZV"+`"'pWb^KARpuUP?[gZ:,/Ab.B0Qbp\IkBsN3Pi0Mu+O!a6fu;`[?mnP'uN\+onmfTK*\-/f/-P
%-q-AMEN:KcG*OG^2uDd^JI'&K(:6bJ5r]R@<Z/o]T/C#1WhW7Q)Z*iM6b9`VO*?DDRUGY8@,bEXZ^Kq`"rkW%0:gXf*Ne3dg3BTQ
%^8UU'6=L;pE+(SDXD)c1L2AfpKoc\fN*W`B_;iF3IDlg`8>@r*9-li:m"]h>4]G[;`#]qtZ%B0I%h%4sTo<TBb*\A_fWN;kS5A(9
%e9RC^01Kn/XP3ggkk$%\0u^X)OLQB!/@7E$q>A"p0iUffU5kB/CDf:lU.>!7BGd(W70O0*UjD!SJHIqb-9U9g=b@/V19N@&fp!5T
%;WF@C/-HAjD;jj(3H)^pZ<=[Y9O`9S'8;;W'3>hWJS#A9`;478Z8,$k;T;g&ZRgUhW5ldcnc>#qZ1VYB5tGZ<TDGl6_uB/TR)IG(
%*_pa>;X^gZ(2,L5CfMT4@Kbq6;A(*dlQDT4?BM.$pCoqc;+!TEZC.uX:3$>"?4[k;2>mK6'&rEo4Yr<+<a&^I(#,)/kh!E7[6QNf
%^B+P3c)QZMq.toiBItldN==5FNQ>>W/GkG.OuU/JD$$Zr/f26fZ]O83"1SdWQ#L+I+cd#fqCpTufCdkJW#J"`XYOHgMmICLNaV</
%Kl'O7O^#VqWDTe.*ju9+<7KS'X_d_@aVPcjZ0h.M/iT?da%=J7Cl>5N?&>ut@Ji(B;6(?s]9URE_<,<V0iVM?'_u:3'>t.HIVr5V
%d>5?C9)7fn0egDm;JU;BB2lXcLeN9>iT\q$[7>;CKmH$RhHn+nVM\"h*5Gc@&a`1PV`2)kVAVH4IY2Y*P--eSP_>!C!uK[QB!Ijh
%XCXn'[G_3k7T`IW/$cI@V3`!oLfgd^nn&lDb]mu%R#j_Hb3!>,;jre9,A;b%SR#cuCrKX^b>d/Q\<,)>ZF.GIZZ;;.!S4*^a!Oi>
%-#=l=+gD1S`nsH684^ua]PV_;_L@AN5iS5c7Kc%T[/R]tBe3_c32)*=jGNFI67,ih*8koYJu@S>"MP"fKS(HtTW8$W6n<4>5F:,"
%[%$(>h+W/QA#4S!^j"k;1V1J!jr.Y/0J`%I(1e'8P):b#e(KhlYl@<.(^P3OdD$V.8NoLpI23a$_hH+>-Ter:X8c*M;1UI6rhU2<
%;sW1eXU<q@Dj&N7Q0d!A@?9%U_K>C`_q@@f,W,rr;3>$QQ#PEmbDP`5l75m*Y\Mp46`:a*OF,@#X$g%El;g4oT28lI3,Z'+38/5e
%em%(<2sc\C*Il1#.Ue25]:[#/^@hgCY1KD&c1tdZ-'-mq/n#"QOF<,7p$MoKX%cg6nM*N>]JEB+<=L9k6]L_7UJqCZ7%okmRT,Kq
%\AqIi9@lHUE*&63Y+Q:k*K7$7daG)[dnWA8;@.W!'Tfehe;4fb^.6ltHqWa,#"),W(Popr6+]15V<[9'gGPmu4%+HpKq9^1(%*iX
%$ec%#\UF]:/n$[N!-L=&O`+61i@&ne9OQ(b6re.orc4Qa:r.>D(0EsMCB@O78$T8!#:KrDE"2#R$O#k8o%G:nJ`<(EcWs3[jj5`W
%7X6-eU55g-;nu(km&m^X.U]A:2LC"p7h@,g-D8F].6.UoWS@`jQT)g)X.-<n1XMo.&93FpXh4([/YoY"j>cED:dm+E4X2l/[B?.e
%lHW%u(>__9[p=%.MIN!2YH-fB`p,.!`GaDs5VQ*TY&&!r]J*$<;H/>QA$F>$%qm`-%]a\rh%mhQ!G8(t^5(rAhN[S>A"??FYC3GY
%>#;'s4J#R+=5(.S?8-h,T`@n_%!r"l4<c7@@"eGCW*\08S,J.6:^il+E_!/6Kc,OSi!)SQ`7@I3>*Bhd?'#tCB0?8e;322rh=*W!
%7*:jK_A2qGS&d%`3Z]V>M`bI4kDrACo>)WHTOOAp_omuT*YK,5'gW5P<m+V9=`-nVOg+qrXQ,8',T?;9^f>CUMFB7pO[[:L!KnQX
%492MpK2?5.>kDOIIKeKGOG26#KCD5!&TPeVAs%*DG&p2,!$R>T6kmq5!Y`RnR&GS8RYG8oE.,+2a:N(p)'Gr<TYck/f+[Ae]HeHM
%I,rA"X&aE8Hfi:,n8n-Y[CrAc4hi0U;80]A_X@d%%DDkX,q<@.&0E/>k_/TB5[S4WTrZ,!h(M.snTm4"+BH$THfPf61C)!r1R(/K
%.Ul$!8frfO1_[RPA/A<&UVn(kDmp;a.3Ak0_gOgD8YKr9j?2q+0$G*UZPqI`qnpprUFKN*HkqRcYY"*R?X-06kapJ6Y.]VZ'SRXq
%X>0lgkX[<CO!!p40VHPKSh%&@fK#b#i;mD5X5(iE2H&=,l]NAb-a@XkD=GuUSE>ZO.16Hl<aMIcXts!^d6tr=LqMM8nZB%kK6c4/
%UN>V2]=?9ASZN9^fF%ibR<73O)G3>)23AX7FPHkK]_7>(LViLri7^a$O2/)i"dai%8!mYO&Fm?Id6+eu&eaKQ9])]#8n)TK0C>lB
%!_.^q8!F6@#O,;4!,+e(U]UaGYj1&/o_dk_G96^#6[,Sak[R7'!Le<fjB*fT\'p--"qi2%gpL/oX:\])@8g/fK_4mcbTNdt,7o#i
%3=DX4#W_hi:';NoGUfsg3+Z\Q"@EVL"]"MbT#hTIj;GHC#5p?t^dFq6)Uh"r/\&UB3<@K]L*Ut10H%I\ZF3J";<$Nd.P0!'?abbH
%:P^:!bFg5eoHpKs-(5M=)tl;8hOh<,M486DA=%0V['K0P3b\Xl+oOQ)H&62bF>oO+6_%J+2EN*NUC=c:#05d%c\E5Gfm%*OTe'<5
%.WrZ(51NAI[K-[d!oBTS;Jd4I$$ATE4`9p42">$>4Qo(o`Ci3DG+4'.jFg`OAlBX7_'YLUbc's'.]u7AB&e?-5:cW-.)YKR,]hDE
%*,lF19/&5sqkWHs<?de_J9fq3"SisOT[qqr%k3*VZ/%<6E$L@#=Fjt\?74!Blo2hYBjik7X_6Oa_PZm-"ha6&h8,g<]GoCO69sOo
%_cNec/8sjS-krD;]t\@ffkH87bR9,oA@b^DAHijo7'lAFDmlh)7\?LBF1eK@.L=35`??f"TLFVtD*/M0.sRPNqt;"?REr3$EZdoJ
%E^=c(U>m-m@JhG%0'Q.'N<Y']Per!`_>"C5:;9j@EqYA6!UaNZ2gA\$"12HMTMauT)IT#EO_$pIQ/l43>Ao8mdOcRi8(hPu>mYPg
%NAtsh/.5=oL2T_bX95OsR=\RoCiaS2\AosPWL#h]gk6!Y*u3"jo?Eb(=l-=Hq;!5>chq)pDTS'r3AMQmf6rOTQ`c1g`g"2eip2Q1
%g:>>J<%?4*lk$IomUnPU7G^"k8o2Qu1p;8ECAi"(i+c>.3jaR5g@8k@34>c5K^853@kq7]6r1btK>`*MRj0&%_niH-jR_@?*e+\_
%R-8#q16SCmkiK0%E$O$NZ.5OD![D;:8D(..-Wa9YWbIZ\M\5esU.&LRVIF/i+=4H@mJoVt&Z%!Vn0@"DN(3?U+KdB_XLXpQ-L\C7
%7Jf!N.24u0],J6.:]fuc_kR/p)2!fCgfZBINGnkd]ZBh5`&rKc0`D'uF?+`r__GQFqP;ge(5@lfY'/8X/45j?b%iJJ5j;I1?qG2b
%^Jbgn$2B)b79)Q'iaNa.6UZ>`Y]D/Z#4dkI'a%T9j@o%b+s!8f1W&]jP\0b=Mdk!7b\sK!@6_up-X0O9+D]OR'EJMnUpTt0OfV7q
%#KVbcQ]NXU4hPt6k1uEV:D_")Ap=8Ns5nh0`NOadpV6b$J,K0c9X=X>rpO4oc0t]#07W`Ns7kn%Ie?dWqXIWOrq3IDpT)()bMRgD
%gV<d5mm#o]s!%aCjmW6;],uM!TE"n75obBgiHN'/]>+>,Rlaul"A$O&P(3H+?^HiR,+k_0_gbKIre#e[s20?655pJ#46Z:Npt,Q<
%N;T1&I/(>2rpE#N%,o_9^;te1Mu6=T)aN#FNPlt^e;k3-cD'N2,D,<]oFR9@m]&AOFf=T5M\cLB$\PELn?WmaC=e+gb<=Au*)SYQ
%PfDRRj%IoS+Hd5coH(udSZ1[=[tI(9!-8RXr[#[^YF/Vf:<l\D94:C^2([F+eP3Wp'iEg'TG71cjr`>G@jiUMZd,H"f3PH#D\EWn
%Ih)p#/%H&!HdNgl)b3Hd7T`0=c35WFC#K31GnAF<ZM2C_'7]">J[*!&6Y6`V1K&>lD**>W#S<uBMZMQu\/WZ[63o@)O(92rajS]d
%Tb:9A,G8%r,#BD!(H3c%a/dQ3AEA9+.5f&u+;A+QfBu6t3@LmhPUg:\..()8U#m6:.1Y#?[Ssi;mhXP'PV*=4L);pD/hj+1GDN.c
%.')mLP(XZ#Bfpob'$o5@G5G!c`Y.VkG2,pV1!@h9-3<WO__NadMEV7Vp+=9DUgXEhKG.H@npQrB&RGU&&`8A6NN)!+c7-W!iHEdG
%9.[YKK]d,s&aS1-5bW^b/*r!mT@2,k94WMrg=o$I\9_t]BkluF)PCGmfuPoV2.a+!*]]`ERG62=61,$;qB-3Z@YF!VJbT)ea01r^
%kO(]b!Y%39JZ,h60rapOEq\RGpjc4M%'XX/J5@R2iJ:s/BTA(kYg\VD1L_0%q/lUt/P['n[cujZi6M&%,ESZ+@8cMn]8>lI%!.:k
%$=u&^$\l`9\PR_<)r]LI7U&t`aX/,?RWlS4[.S-)dNr0\_M7D[+q,k:9*e#'JRZ5V_.uI^%nB)@e&;T7:ENdN#-NI_46;-S>QOUq
%Gn.j#jZS4]nD]USMm?4Y7af0[JgFo'R4!ItXB1KdY[3X][WFKgbC3A_js\sW6q!CfeMKrF.QZGp/7Q&[Pe5;=b6WW8<Y=m&pEoiD
%%.>E8E4uDS,u8`O%d$T2N`B@WAg`j(kW1D+j/_Xf]7]?,?7>/[X';H;V%'J??[*e%aT38>(N,8`ln7iU\fh2hg;8+;9s!m]4o..)
%HMn&$.<F:1Er+V2R5fYc!`sh9LDsNg5eg,;f>Ub6S$Xtt)"FrR>ZSNSgfdS6<,iI3+9kgGDh"71CDhrOAWj90=V&FO/c-RtUc1N>
%Ca)T8<IuQcjlU!<N"U<U%.E;!Hfb-sbU498Q+=KkT=aq!hsc$-@qgS,_<l2r/tj@ST'!JO+FFHQRq<eHQ4tY`Nl4arK&?CFZN<@U
%ZZHJi9:6+pM2>aMh)6:!Y_rnC1;52<s.CT)Dio9f4!qjo+_k>t`NX?E7E8I`Qn!5-QB'tRAa1bQX>n'2[>!'gZ""^Nf)9.<.U;ir
%A.^9u))lZ9AK$JL_fKid<4Yf0Kf6&+%0Bs6+DY8U#eTsH[7GI57Ead1maNb8D?=c3H:WjX,;$Fmp'iXJ]Nb;s-teBO3@2u(hEpF]
%%=T4<Ma.<e\gS3,6rYeiP;;ilY1;q)eY+PBbN4EcJ)qJ%^7h2u"4M8ud#,q]!P9=,)>YYo!Z:A=$NnAYGB@%dHlgXt^7p<XCIrZ4
%8Mh4h_rpjt(c6LG5#&ts#egXU7NCKH5]'\>Nkn>k\>sZ^0=60WgOhtb>[`sTXr&gQ\2/p2!%A8ja<@FJ/@pun_KW/U<QkT\"&K/E
%ePpIo)(cTEE.E>2BFjU:+bsY(8.'aZK9^1Vn,St7,q6np-IO"p7hGZ[Q$1#Bk,0iH^JnMQR^7SG>ko=Hc;P1@-o*4'*\>P^nLls)
%r3fb0(70bl^JL1B;3qUKj$Gj0nScbpKgM5!!rU$QL_<lg"s=7:Zl>EZ>^hhfM*/XU#9"1Be$Q#uknbK+m#?7#pX0-)_$W^o]MDI@
%@m6B$@<Db"-kK,-i6rCQ94TU]#VSR83V^m`.d/&2>';a8YQJ/.<l>6`Ao@;4fM7-[-'VU6@8Con@:"jY0tSq!Ra76Z6IHf'3DHtc
%d'dceh)F=mGd::6^Fi#)Ne,:G6#Sk#?>'#%*fP8sn03W6c377'>pdhIKAQfRc9lG"n8m#-j`5Q`eeX(L!<Ig.@Y@rK=e6kWMH/%(
%Cc,k3E$fYbIPcR,;LG!T\BJ'TYmYkuN#pn8\$j+jDh3Cg]ST$,c^VKG<(U?r5SLV./EQ(]4q(NPfaB"i,Gk?TZ#SPG#lRh0KF5CI
%Wd.q)^6&]Zf?D*/<GSX0lFM@D"6lc9)MR=T(6'S(2h0#Jc;rR"B1/1;hadk[<5-TA`:D_/l)m&"mGE;iFk,OIW-'02K'iZPeZE1k
%)pVmk)7Og"IFR&LY>N[Xe<6g!0Nig/O0f+q(X(`GGn_uieAeGq2jZ%OTK<O(A_jrbJ?tl"#[pi-BVJok[nF17+%-pWc(j0=ERcM\
%cF9_8b&%u..,7hYB'Ecnn7ecZAiksSL_NT%%lNic<KT;M+r-sEGX4?+ZN%2tR-g74A*%E9QV7j=[*hi>1V^8?_>[Ir!T]\DG]`Ws
%S[p`8ArY:AJqA3uS_,$U_:h.N\F+DrH'`K]Y`r/iDl;`X"ZA/7E-8Oa#%+Z!gee1rV.rI:.P!+NdOiF<Mdk'G_p#42hXJrFhd$R3
%91bDC0G1(+P?*t916D47+Xuf^;+ME+R\V3V3f48EW(ps;CG;JhMK'@'`/Rl@0b(uVE`'78WOb#^:,bQLj)U(:+W9r_1N\uj2r*Yh
%U0\prm'W;POjgDYASDY$bHVf2;R9f$XCacVoEO>Zc)@4KB)2ec22lN8_Z6DfnM$WTj;MINcP8a"EP6k/%e&u%7ome8$<<0ZiU65H
%%L,l.'Z!GUbjU'%]aTBiP]cqMN<Z)oIFl1@h^,kKF^:M\FLM)]:`3qY)<HnOie#7D+&/j,C@TEWH4#Xg8''9j$$`bu9`\=!Ra28:
%@No-da#e89oOUbdp!5e*T.Zc302n,9FGQu.PIct#VjpaGZK+U&d#=-s*W7]8bT[j%j^OIZ*I/8n4`0q1pQ>A(<Ph'pZ6NU0>GW);
%FGX6'&>st:OuR*Vc7W\m&eIS@oan1RCC'ZZG'?Z6Cf&uGrR/?R/m,2K7l]9)OB-:H4PM2-RNG5"b&^3+MF."tee)PM.KqZX4^]DR
%''0*%N3$8"XH'quFNp7IW0n[19@DJT$PuoriFT4QLj'1($JN=.+c7YJ.g0o97iAWh9GI\[Y/<MT%rEjkQ*[sC6ZQa9"U1F\[60,%
%#*d*1dKa`M-i:M!ZFO3#j4L;`\L_onn-I:hMsR(V*"EiSbW*4k/1jo][VULh$q"4p?Mot:2rnqPQdUOS9kA:!C_:?K7,W'do;tml
%Q]iib&M;5F-%YcQH(DXC5r+b">[FiZ[8a`e>>pAM('Q2UmUf!$(l)cqYfc@mR)uY*)9$IoOJ$P1e4q7BZCua)i**nVG*"dR%le%0
%&!M]o1X'p(N;bB(-m"unJB1b#Gq;>.a:.2QU6t*qF!9Kbd9XsNG_G'W`%4\\[YQpOQ"M4k/E1a3-B`*nJO3\PhnZaErSk7dGTf.`
%+`\d[nbjeGO?]kVe-:<5lAiB:ma8%cC.)`(Q<3K&PT^i+Kk-YF#Yi*[b.Gor*d8*IB#ObS9Xd%!qGDrY\KHQej!ORFj@0=pq&8g%
%^oKds6)BGj;H%n'q?3._-q1Z8Rrl@$87j^4/iO]cRKi'.SR91WOK71OM\F>f"hks'Al7nIZm0j7k]s-N5S#N+6)S1rY;:s&Y3H=3
%73t@_ne5'6E!L\uL+.0=]#-,sVKsV)-.S6Kb?L:I@HG,?@$05_F.5%]rb)OH2jsRP8_u$c*Pqq37VtQV0^Qhd<A_\%WQJe`80kRe
%g%C?ZS6=nl"#J-DUrE(f%Ze.sAlm$t3)M-q]d]sn=]&*'$Nr,#WAT^4MDS^sNc]te`<"2"m?\DebY#jY'sPR$WGWLaLiH5&\oV`>
%r2#a(:d\mjN(knq3%UVDG`>/R-`Ur+Gdb,`o.$9I%e(t-]sp8p"u`e#VuV-?e`gnKFdkh$EiDUR"XK]aV/CNg2?=[nZ`\4ZmF`e7
%9"07dBEd:X@q-5sMV=4DqoI%/I3J82B9c@_a$IH!TPRGhWj*pW3!WfgPC-=W^3`K%%,t5f?.XnqCmAi=<\$N?##%W1@0?ZUdraDG
%I+^Gt\Qpbp.ueYM%baf3]R<Hsgq\1Rpo47k]WUR,#*+p=VH!Z:bt,6KX8qc$ibfR@T6>m&^;1=C9EGEM3l+S&:<tdb']Tj'<Mg^+
%'Sag;@-h12b(,35PrrLr=p'2"e$>;J0GIk#@Y+YU_U5JbqrlE"d$V@G??(ha"Mo,&%9RIq.\R/Y:l/UK<;1i!-OW-Ijs50[A>H@H
%VWSM(&aY=jou!#`1Mf@u6TUN)mte,Ic?boBoE`'cm+K1^$KhQ/^`:4t?`Lf#aZ-EaOeZ;7L12*#%'1@*LZ-?!5EF/['PT(?%6mNG
%Z%Q>qnE8C%fBp=DGF<7Ve/Zm!#+]=_R&d*R;@p8l`4*"J.c(HT&61X1:i[6[^mo^s)E/DVYgnjn(W*>l:BPPZg]NBNSet*,^!+9P
%']k,d<RAK-[FjN6XO*')gqH<u?hA2X#*BRQ#^>bJQk$eP/Z7T##(*PU"?B\0O@(61/s\iSFpAoB#cI1K6HnY6IBi-)L^MX\W$So.
%5KPie&pGg&)WO.\b+/dC`GJZV%eXHZe3b0p:n(-_-/?2H_HF81\Ej.FBV[2MqkihH\MhW"_6CqDb&=kko7gQ$+*@)[l/kEf<CK=^
%Ho2c[lrkF=4F:s*?jA6<XCQ,P)'J;9-Cbfl0XnZu\6#Pq8NfBT-1,21^FAYuO\[bn`KGj6"YOnDQo)+D8:[k!0N$-'MQ>Z)&#JSW
%GVs9pL>$L"bVm*jg<e$>oOtQfKuu_%Ct4=IS+I!sGJ4+EEWJ(+I5`8[jW3[f6T\$:3Tt#O$KW#G"3;Td3%LTq7Lg((l,"2lN>a"5
%[Rh$il?<m+V*r2Olc'UL>7obM=83`PlDee@9s2)5Qsg")7'0[hKR/ZL-aEj%mL;J'&u4/>p0%RXa'cVl;+k1o6gk8f?LtS)8S.TF
%->V5n,(8L-RNQ*sE9DE3K_)A,P/,XQTfLLa+@d?DMq2h7-oV?.9ACsMd5^K``kjXL/AJN:=e_0+]K;:9BTl6DR%!sN-5'RL9B%]U
%#'?5tJI9/$Lk5VNZOAX/=ZkrfX0[npNo&o&FMtEG[Zc()MnYqRoMMj&E5=mf>cs9l9RZT2K#qR+2]L17]8m@(pD$5U`$/!D;(8k0
%3:$tV.#*Vp*lb4C3pHmH('0M2,`8-njE*AFBG$u7JEE!cbQ="Fh?R9q*aqHD:RmT.h4V9u=HOh>lVZB-Zeo:Ll?*^4"B=kp'o69I
%\\:Hp\"sPY\g1>,OoaiF/EU@s7,#73ad$F.VR2hR@%jkmmDo^WDa^L#:l<X%-9b)?Nl=#2EE-QI(H44EOJK*!U"mUtQe`@EJ]-jO
%\S][!QpdC[hO_JpHPh26q)kde\WUVBgLheOOpZQ`!;T[A;DFka>F']H1@GQpEZNp>#""JGF!]SN4@RWpT]"e7YO9)#Zidg(aup;H
%<T@]N3P4%.FfO5B1Qsm5H^>%#ma3n^>hb.5CI@P<kAPiL*6g?9"A,k?9SBS11%F4,hqgikV!VRePeE(2en_6X/`L=mhXkAIis$*S
%gKTmYRLBZo+Ik()`2H4Lh]d&0b_Th\Qi<4"C<_MO0QLAq`?h[X<Rk2L>(:,;Et#V8GIY=r=bTt;245&)K-R"7Z[U/m?<M5kbluRr
%5sa[RDhA`=^hRqK#K>sr1NHj\4nQ(TH=Pco3Ws;:h2I9j-Z"J(m4dRh7qbASZGb8WH.J2,+%H=).n!C4^ZfA%``/OT<6H1bTJPgQ
%^`?Xg#rhQP4(a1uM'KY&"+WEN?'*I`F\h<RL/ihd%Hg5Okrp)?jNH?'TWUU8=9t)t4,>c-#Yluk/BuDQjO&65Ou?fHNnA2NTKG$]
%7Q_ko?q^cfT-rMe&BnjBn)fh'#6u9c$oAUIEJNW`qY>QOZGFO;`QVk5Qk-?mO[OXnSN'_A9U#l2TY<,Ne1g=k5ZTrZElj=&Gs,>F
%3h.NJ1(h`31F&Dj).4NP9%[rn1FFNE^!?[gRH^tP&_)(/K\97b3W&Gmn2E%MnkeX<aM8fn;`@bfBst@qj,a'Cn2JOsrA-J@#XXLY
%_!.3CROEmE8[`ij>/Jj1^pjOG.@V2>74c^)+;X)X3Q>*QbrhLY5]9nhUVL0g_T$1HBWVb7#Z@o#oM(?rp]j@</IW,`@o?8W)/6:j
%\D0H!rq/X770Y'\is;kX7&8XX/%U$8"^`/[E??C=QLE^h%YKh2.C,uUqX#:NpT*l=J&7n&]\`PL=R'Yo.7]'cU1;0uZKMmBJQkW@
%g9VcH3tGI4!DO;7Zgu?bmV!pJ'gA-\`p%+gREAiT;j7M9R>jZ\FTbSBR+<^f,qR>4KhfN"iRQLo+c@Klh"+*k/sk#eEPbV"I08.6
%M.mZlV@qcsDZU?$8YV]O*s*=b:q$TX^occr.@`&/pInXC$mb`l6$*pX(cj?$cOPaXEuTXRe2E6&H:k'??YaL;!dIpl!$YS]FG2ZL
%Y*"7%ZlC@q@Mci712#\kA6.SNI\#3,&BJk(X0'LC:[rTDkRH!&dRATFBK_-joh?#kio5V4668*T*Cf-deZ+.oK>CPb;'M"><hVDI
%,RV<iA%X86Mf<^qWZum4i)!;)0KH?S`aet`Ri5Q<hZ,/X]?s'-Wc$Je6P7V.K)7;ZD&p3ZNMs/[.<a+2_nL16'(e`eFgoolf*rcS
%=qRirDI9rNZ/0.#\mIu8-:,1T![U3V4VnlZ+f!<LRN>lAifb.V+q-^jj,C6m<_^'V"<M"4@?TGahBjA?WCuZ6IJG3j*>I&q6#mWc
%JDlLgOQRfU#oip?=d"q]I?[0NO=VIp@%4S8nu^Dd:.BW+0,Qc)8iT?Xb''KG7_1B9f(cr:-/=&])K%Z]N^6^M4d56]^!uUO-\J71
%_qbBH3Y<CF[bH3Jd/pga/V_;$+*Trci,1HQ9@7^24FCsC2bHafCQ[*I/6GF'<Z%Y<+@qMm=@1_,BP\<T!l?3-h0m3.TE\3tGl1g0
%DFl9B21P]SV=DYIebish/B6>QC?ENs&0X(m?EH[H/oRp/9gE\*K0L3``[tL*5e#:^$slN%jhlh4k%S@dBtE7IYnkVk+l,U>KP?k^
%0VGF=DhUNj/<h+.*&VY]ZK-=bmS__0qB\A]'\kgf*j,.S\5Je7D3`ku<$Zp<WG-Afq]LOnW1mEr6mGcmm^]9o]H(Q,BB(#\&[Q0E
%q^Z<&S3>-@4L/4:a*qG*JA6q:7NIp`:Y9U\AAsjh]IgZ-GSoMiDEVi16o!X>gQS<ji'Y/0R+g4k\u#:H3ph1EM+c@8Z&fR81'V_g
%&<S0tB5+gs-=*3h:7LF"c-Ae+`CD//\s&c!'KKPsl/B0e3sK@\5b,o;/QBA94W7'X.*h8aYH33)JC,g6%f[9jCoq=m5+:_+\bJ&>
%O[2&\3A@9id\jtZ@o&4D;7lC5)dIQ8ftrL@Jd+r7]QDD>oQk&4<i=$Ph[u)dG6@8;/H[-$OVJfHH6Ju76Aam8,U7rrKCAshEXuH_
%k%t.<&>^+mnXht5XJ=rWr4-**rHVH7'l`'CrVK6<4TsoKo?P,IK,fL+Uo@M:%T'[0YP%DhL1OgCU,).4qDUo5%(\r.ksB,k^+B.?
%bGKpGFmiG!b?oBCJfJ#04nYcPm&dcLjYhK#9jgK;d=62O>\\%Ub=U]iq,5<7K\RU-P[dS7%/Teg_R_.BpfW=UV5)S2Q`j4=aT6,2
%q%;_F&oNjA:44k%0\Qe\G0c0F:dEZN048t"]*^])\rL^"BY\aOM7$,P0H'hRQ]0H;onE95<Ah=S^Q[Od#_%srC<n@)ae!Xk7\Em\
%1JsBNlk*?MVD`).3(N#7*_9'2:fi(2P2/GrC,P.ArU8ddG2iT+&e-6sM\&p1Onqnp"GY]tT7Wa,A!qqZ;uT/bo'f^Zp3@5nQ]h'M
%#\]5VId>EIDfaFUnt.67YGSa`+l/J[&(9mqV6mBMoZ#BB=X%PT1Z#"A]C\*(LK),\BSBGO//bEA!asVR.kLC`&LTW/a,TReVIfj8
%=c0DU!q6KON'T/[_pC`1E,soAO93R1gWSOQ3q%>!a]4Q)_AK]Z.X"?1\=Y^fKe,&hBWoE<;ToIUL'P>?9:HW<c)\hPPLY:\__iKd
%XI"3_F\;3-`[sQ0J&1H(?g8u_GVZ3=keZZO@=+1%7Q<3u/UjaVr+*aJ_(ClEIKml]B+e=,jL9!"Ki.I"\nMF[!8<odJ2E2M,?uRU
%6(*DfXLnP5WPhA-24d#KWX@`_GPo/o%'g+N%VIYciL1./(dD8lSa;&lP:&U^>-\j*SmF`$0c[X6[C>,W`NQn2mM?.![IAE"!U2d_
%efW;e61'^%8N>\,.hhs*=H3;>;PSn<kDs(0b-5IW-s!)IqQiEa(]@8RV#=);a8#E_#*d3$H%#U?hame7D5;ABT55FlWuk/6FSbkh
%@T@\0F<_:Pkr8`;r+M<;XqUo<``o@B#J_dHQ?OLnKpOBo-.60*]@_:'L9(2G'GQZ')[>MD9OG@L2TRomaZXI=Q*9dEfH8i%YWq[f
%.(MkKB`VicfF3=>"QY8lH+6J2cEeJ/7=4L!L5W-(D9B[G"2JU+7=#E6)b=bI.`?YH0VkCsWtSX.npNLl;)PmaU+)FG\S-;iXOGJ.
%!2S9sq!0rO=:DU?]@Z@p2HT$a(LtljY(#>Mrr;#kP4R?a^GR!2GE'[30SBNK^"rdm8hVeVYlq'[me2p8Lk-gN_^c#K*="$)@%ff<
%Qb(?/f=@dl]i5l@&A$d@*)t#nR<ND,PPTYHRCYMs8Db1Ici2[*c>=Go4^NGg?[u-I2h?a4_Rp7$XY*IOdgU(A0o*m6@ClO+^>:O,
%'g^'O]i.M%W\(=o'oT1#A,Em&\IA/^nGl370@*6cd0%A,l=GiK:B"e<-Z.?FkD'L$Wa1GG[Qh=6OTEaM*jR.ZM,oK9e,+jfZpcMX
%SEOfk5O@5VUf;Opn^_1P>)ZS4LD[k@bFr7`76mO\7ZF\Tkol?B;4("*#%10?jnV*1Ae(Z6eA:=:m&Na$]a%BIS[AK.6k,RsW[>&.
%V+%WVWj`(A7Z.7b+J:[u-r2Ei1)Q:UQ]e*Za(MZF8FB1`9/Y9%Tm".UF2k>H8"W#PC&j3m`CB+G<mD["*9utpDf&`K@rtIEa9JW1
%jQpoJDWK'uS<`b*(N)ld6uRs1BZk6[j5$.UI$N&B]c%'/N#c]cmJs=c5ak.n1XT<aqi*]gI]GAq2_8A@pCl0Z7X(YaXI49F&qU@)
%QH6\-R"T^:oTCEm63-Odn5I(*kt$2?V9\$@n)c!)!mTb&5?h7$+%#D'5U<#3aIYJcM348W,)4j%ku+]0mYBb>8EI#B8s-feL3e\C
%0neRrZ</s-^fO7a`;TrE&,<BkCF!3R*qkW@Sb2)\Xf*UNVM_1RC#bokMLA&p9\ea_,to.5s%ob0-#/X*<*J0C95(]e'I`2#\J:PN
%7/Gq9[\k7I7SEn'liHN`KBYj<BuH#DQM*:i#AUZ(T,N&:o'g$H5^8hDVe]fj>$rj]K-"tXJ/.336)d9:0=t`^l.#)PR;;'mV:9@G
%Q%7f1ehJPOWW.E9a/R)XGMbr:`$o&;c]UUm&X=Xt-#8sLg)XaR[qluH0/@KE\7CsIAQMLn"crib]24)rRkCA%6kD3L-;7NVl7/L3
%^e,3j-qo8@:+Nr>>g,%\f7?miA&khAZmU"LSe9aKRW9DR#kNIFR-ZgB!g1@FjVcti7K1a>"TCim-B`LBV(Ns_`$`QlHF/M2D$rp4
%PV3]H/iiVN*6.)+aKctl2hSr(T#,.Tam:3m4-(u;91F5qj!BnbA@ImVH*"1(btq5NGTSP44Z(_"]66%hcIN7Hrr'k/]kS=:TsP9T
%pDTlc"HT&;ObP]=ThNAY`&cW%AdLG92k:q^eCee6YshV-L>Bi:TrPLDQ)Ob.BZ*>a\R&]CH)(%bRS7\/k'Cm;+^i3f65mP:K;gKD
%<F#U.Xr?+!r!s!NFu(EFqs?jZrAGEZ4`YP#h<iM&MlEu9]W(;>rD7qir.g@MZh:AF>s2</'Z+1&DIpQ4Y_fIXm"t6Ura,8uAD>ut
%?:(&T#c%D/Qr"LQBC2IPVC5LL$KOkDo1=rUXr2\k\_.286<#a^$RTg_;!/&U+o"OU(!R2.!;"Sc7Nb1ED13BY_8]lh.EK!qgLA-(
%S:efF_`_Xf/'#2eqsBK']tc^;Fl<M:Bk5oGJT>t<jpDRcUo1g@\)Zt$FGk7,X[G+'TaD1qEQ.G-5l6L>\+&OJ<_<T%8!Qkr?*2#u
%U'hV`BoPG\*_@6IE*IIuZK'a\SqW>L9_JlZ'0^3FeB#uD<F&1sddY[,Fr9_QRLW3J/G#UeF@gZ7@`d'8/!+b).SO]47&$\NIW-p\
%%o*""j/c03686QCRIrqg%G?OY71orca#U30nVJ939Q6H7[O@Jb8s\Q8E"f&We?1J2n*&c7h!ne_8W!iqgp!Pq]7B+(F9.:6.pR#b
%C/N7[aeGdm6p+d!KOH*1b>l1.d#1`JCQ!?kPn>#ndcp+("nU)B3D?cXjJ:Zre<lGW;^BRp`_m1?<9FYA_4[!$*orl>3nf*iES2C1
%!a1)>SS^A@mJb*n,EpD"5e8Oa^_TBf2Fik7,#kEMgDI$=:_RN?DP4X0I?:O+9jU*[<7MqV&=@!+7@&ErW(,EtNL$0l)>I.96RfE=
%SipYM5Sbef!'.u@Z]J>!&+L-$L\GdIBg)f9"<27'C8&3s&JH#\$$3OIksEj#;1Bg'CHHilg[?]e^_uig%1P_"LnCSMjO.9Qn[IHj
%G%k8S_*@]f\'H;(H-OR!X;67p#Y$,NMliGDILoLfB0T3pXrQ"IG+Q58(HZA4mCXV9B[KcCIU_/+7#N(>`<#S'?bjH'W^E6m`S4rt
%5u$0YH_fokMM9jCdrlGn]@78;CbqZj=p3&sMR:)ds)eU(=Sr^4\@D[dWnP/8Z-\Yn0(*rje[SVWqK7He:A[h"*S7JCb5nI/B:^sV
%Dg+8"\YYlmL)I_kp*GF0"HgphWKq(5N")7q"OCs+<73%LS:7d%e;@t/n</dIXMh[^8gI)0^G8WsZ>p]9ZD1fq)EX?/at;@UkE,(m
%r!@*W7@U5T(1TVGY'WC0*mI'X/'c\ONU@=fCl-'&bV_G#n,h&C"3^m,TVtZ87TksL9N5a"&8"sP!(h<qO-%!1$9^8sVNu*CEDWTD
%nF)^B[/uG7"kP*^-Kf:S4oQ"%f9bH+ZI\+Ch(pgS^IGu'Y7Q0=LNp""QGj9<BG0)>")g/e"+bD:/e(95Ap91F`0Tc.TH9m?:4Ir?
%loEg8e-e5R#c$f,&3V[10(h$ZCPcqZdoBmq8ZSr'/`gP`@a:k'@jG#Z'_dHq<9"-^m_t]%O>-3AeW17:4o]qSQg:05:DhJ+)ob?C
%lS5q=!fX7+c.Wk[)1`8=456DWV7KL$711mm"l9(.pMB8jb<2aWBf=FocBlYSaKQP=5orM_":uS0KZ@"Q,]RB5caP;':r+"P::T7g
%;[fq2\05s.GTQ^s&B:Zh*"(."EmYfdYSl*K<m!_8<1drmYM?(K"TPD$k`fZNUZ>^nmjoRHdPpC[C;)D*1hVi_\gHM?$f2B#pI$MZ
%d=!0Rd+.![e>T6R6Z9O2aSDi.X4c4W#Z/'HnV@m3>'3_mG.6&j9asAH*/a\%"4Z)rPkM>L26qP[inX0B`hhn\8q+J=Wjp(^S7u5_
%[fSH9#'Y%-eL-.^:DL'a;F:se$JW4AVGdJefQ7iWc0n$V[Ki[F/Xa5,ZE?[aUOgFW4L$:KWf\TYgjO^8^ep;e.?pW^Y&p99GL#Es
%FA>S!>R9.e.:E$R89W&k.-IcCGHaO;LH8OIJP0Z_%.$/Dph&V>+EVC*oM2T<qRsf^$=U%s68n&dW<,<*=_/hOg(H\2e-S5@)O!Km
%Mg'h$dP5n`XjMJ5e#$/>jF/`)'C>%rJH/;j@\K"9'+nSkM9dL`s7S4G!85]JUK/$3PVO,\:OG0tlr4jOCoZ!KT-To3#Bn@d2QOJ>
%lr83,C/KY[D67.3:$jdNWtBuWM9o/=;Ku53pDIGFQ4K:2W/]Or(2O2'=AJ>TN=DP%Nh4#0I_E#6F&I@]<ZVIJ,HjjePCo>R3[bEQ
%c,jP21q=.;'^1_SN)2"c6fOPp^\L6QQ^8cTHnE>\gGFW6f7(#=V[loJ+7?p<'LJ@U,+S`gUFpdl`MFD0Wu;n6Dr*`)j>A"(EjI0Y
%I[T)n^X>3$o..Q_Dum9c3,>6+V#<#7,Ndlm[gDJ?R_tj-lq%0Q^@-%[HWIi6NQ7=.>8%TO[?k>sJ1(CY#ekV<)SD%AaD&s+.<nCF
%eJhKmh2W:@lrMB@@*-LV!R<^'CLW8OMXPFa@(V?1)N_'PMWb-NYIk,Ir-!FDCRQmj8^O42frWP!olpfiaoX_p6?)s;T]I%F/Q'<u
%eRf?bDl/^eIi%"*HS@7uUu2n$TNLi7A4fhmpt_](**KJ7Aa0Ug)*kCZgZX^Z>'C*!'o_#Bf"*e-kTsKY5K3=JGD&qj;mu2R'UhE(
%`U6He3_Q/=<5>DF4b/SIjY/b\GQ$<=<sVLER*o:lN7_aB@[XA8!]50gVi#NGn&'saik$+<ka)`0>'=pm]&=U$4e)X#K%GAV43u#E
%mcDGi?quVRPrn_p^?g\!:cBW&$eJ/c!LsJ9VAp0=-8k[eA'VfFd#frB#<5_kP-B`#2as/*%VDdHrbhu05fKt(DV`h:>1%?1Z&$iS
%Z4Vpm<S5^<oah[t.-3f)@)>^XOOQ^Y_A!N!=\Tl:/I\U363k9#8&"]0.S(\&nL<q'78O#L)5[SZk35g_:.g64)cE"Fpta?>ZHTh:
%&Kkm:[;q8a)hX"4>n's?ecicB1iAbliNAOdU%]C4-DTcBjc$p0`AA++7k"\2cn6af!e"9J&or3a#2b.R[\D-0%HlSQ)57o<6Z>a1
%d$<Esd2M$o(34$<'0-:071P&S_E@5dVQ&9<"WinF0n?%X>ZE2aOTCCO^/W5\7_t`]RuE979gXgngqu`-:bbqD7H<Sb!S>$'+?UmX
%bPHA/Q(TUAHe9bl7.jLW`Z[%A-t^m"295S,1oHMhm>jNT:QMb,_M=K\Q$IB%U5dO$k#J$TFih'W.hYI$%2[1+bX$N`;'ib$J>Q]l
%5MSh/6VYd#?MEP`^PS?u/8D%YYYA0\hKH7U$(A+g10XKp,"X$HEX<m\e4_)&]Y'0YA[J;BR#YZ+MRF1@UBbJ!m.WsiYffE]Mk9QX
%0@.3=7eEt3hRkp0O$K+YWM>krRF.?XJiCBBLPDH[]%.JfB;L^39()Kuc-#MZCe?-%\@`[DEkln7[9n5bICCpbdUkX%@9d7PeWQ6.
%!L(oaBMlS#3\:nP_Yj-Q#T:8,etfUi_>p##5Y-qb3(Xi`@dAureI,-nV.i>uL_[cfEX4nWKKl$XqfRUtf6a>&s#ht'k:rUt^ikj)
%'aZ7TcHs1+Y[(pk\$^0.O>jSalCo8Q)H9![+ujUgi%0r4lIqD::eB'2BJl`QfKP=C?7D?*2)h,["DM5;\XGn3"5_a+_?g^9C`;g,
%'8CrEr;3AF#lQnG:dds1eubUt&Dj"E,;3(/i1S0db$3?`ND#d7cBMesE1mC0kP/niapD\dEq+5%H2C6G<EE(![igcKI3ol3V$NOi
%I_'G[jQ)JR@m!e29@@aIj;1*kJgDSa?tU4#0=Tm,67fon#2k8A"-WG8mLH0+<cI%;#faD"n0/FMk]T>,*aOR6!So3Y@@<LcFqo[W
%KFu_<[7dpndi?BFYf<ESYYSfN6GY(;'gKW!g1A(t$?_fHaCW#!-!18o@uD+I:EsE>=:VH:=6`%DN8e@'\@%5fmnKm"T&B=EH%NlS
%-6@.lB?Q"^U23@)==olHTu&nO9nTlc9E"mp(C>n*GH7<B858J%QE=s%NJm#q.kho@HkZi$b+V-H`V/q:4)[\=2r6[Lda%N'_H<+I
%k[Y8n8t!Bbn)D^'U<`JsC!='(JqMF0^"R%f@qEd2^qn8I&5%#IL.g_6@a!gZG?[ImdOmIBq2X&%b'bEW__X5PcN8@h(in*R'ba-b
%b&FP(Fhqf.Wpq/sq+kKiT]]G9.uLnZrQY[;QG##"i6C)R!S$UUh[u04(\Y(ZHufihB$@bIU4(pt$e53QGC`nm?3i)C0*/=:OkmP;
%0B2TRYm3sb2nBA1Y.,bc8=]n/*]YW&6$U*8@^H>t/^LASGn]M=P(_;d!@9#3(EL)O=qHtNf$1jP<7N!4-r_<RHVlX$/\XQB1MR2>
%1Jo?1)mjH8l'OH6CQ)YAbpjK=rRB23R,B^2<=1U8UXQRMrW\'"B"cH3&G;L6Y=7O'LR87._Fbk#JZitYd88p3HO045!u-7=YqAY5
%?kbQR'oh,]Y/7i'[[RDZ=s18-'jG4_4IYcm-XA\C?Fk_#o[b:BB,BSHRtTl7'L&VS$l*C'B[16`NZm-&-&:8gJiHO38O7`J\ghaA
%H*,!kb72sC#Ks,\"GDC$nAFj/:kr<T&1Q`4;oh8NM+;8L'nZU3Sc6ZUc:ZO-Z(I!A'>1OTaVhj9CRLssPm)]\\6(DEYTmJ_ik4]h
%.1o[R3C`6'L*1<A=q^"`"](AL'r<\rNNr/p/[F?CX*;?gIX'Aq,B)U+I!&]NrH$plK>"#k^MXc^9"hsh=pnDV%cYI?F^7ldXj>Aa
%2A\S/l72A#7!gV/I"(f+_6p2$g^*>:A-ZhOY-qcn-lgee)k)CWc<2grcjH(Zd.B0B:&rkLL!2dmdlG-f.9%X\B=gl&;@<a5);=_s
%9[e"uJ:`R<_r?tj@2.',9KINR&p@c*k!qaYV$31)ZDs4;BaL#F#<:j@PIulJh)!PlKn8G/Ck:SN@u#t^M-Z9AN!+@'K7Y.>!c\eI
%==b^V3JD+1BTYjF5'e$(AuAk>c!IcqN/WP:N=FM_,[6H*rI-_"[fDV10>2WRADs\E,K9E*@(u(p%#a3&`TTmYM\=SR0BOs9=ouuo
%"Pf*Ya@%*l0J&[aFVup7#ntl>YncPQ-B]ftb*CIR3IB=-$oL<"DP2,G+\V8e68bX1&1GQ1\B3M<+?^IYNRTInd+=iu4[a;3]upn4
%W]kV&3os"o!iD\+%JnL!G6'a+Q%IqA8h+NZ$'tp!d#'K7?PIH:+dY.Rr%GbBFLrDI.6<br22*B`i=qIK;OYF-3000g%DKFmK.`)C
%D7Z,F0Y/`!6W^/PSk;FDKb(N0`'W".CdN8RKgA.uVFP5*:rD]pRMjIB/ofdQh0O'FW)T;ZP$FM6PjeKgg,i8=$K3@Yn"1#++beIl
%p&:l9jF?OKmhkA`0e@.l$RZfPKc`\=2;>'Qbd(9r!K__J.($+96Bk^">LEliU:W?NmX!UK1.qpT$?j%Y-HHnXIQXR$&Fh"+mW3se
%N4Jin3<aPVqLjLYokD,GVP@^g+6$`\'\].<2H0h$H8EG'+\^L5a1/+:gUFA7InW:me]pRq5s0BK__XQj/;T&"B#)_b*[&[CPl@JO
%>$ABFFLdI@#&n]E$IRGd@-T6:`]CQrDa9%/7X3(EQeF3ME!B<?S+DZU@OqfJe`)[s_k?)Xr`mf"XXOUdcaV>(Nd%.'='\a"MN@)E
%AlUNaPK2FA*F]$Op8*L?hBIkX;L7jWeVf]5dkR$>SQ,j$7\5WhW-8FQ/8n=,R*hY96gW,m,VZ!M?_su4?e@7&i+ZQ-h&tcWV;=K5
%f%\Pl7?oNpR9#YM0=@470^TDU-\[CJ5K!i2E];`XQd;aj"n?YBIKjc_m-:T)$[3IC_)n/'JIo4Wl,@q1-OG'B+2bgeU)8UCmV9Qu
%gKmQ_c0b^9d4hnH:6!=_?SOl][u]*4h+qAB6?sHCXFYbZn.AnXN>FBZ1\R6X9RrbZf7BZDq;!AI`%FVHNE7>bX(?ob1k]c8o@X?I
%g.rt:SMcn-KbT/(4qVrnMkJ`k;.plll`rNC'gLOo_i'^e:QD[.991G(#rH[_==!\P,g:[=.&%^>O[lr&5Sbc'VrVGbTJ!+ELTKXm
%H"s884Xuf+(YYB]!TG?(a>0I`<Uc#NVAP:YdC2JDCIB0kVQ,0V/=5d&cLa9\6.f<n5rXJ=?fk+I'Ts%)_]=::[Zn7H?c!nIlht'X
%n1C_]%t[PP9HXU1X)<pQ<QsZkPrOk%OV>T5o"E&iFg<>iDK]C"G)m)Zd"lWC4%+9FQ=jK$>KGJJ91uf[H+)ST@RLZdrN[#!g]?-b
%H(%0>`t8D5L\(<_rRt$fndW$rRcmIq%(TQm5jfg'2Cd-,f$q[`1:?mH>+o:Y))`RoJWH33Xh/>14kEXTDP7o7C6P"\N1e<iA23Y4
%p0jXjiPFu&7jsP*A,.p2kU1,[XkC!Aj\j_U6aK/#+GX"dcD%t*]LNen7q@Y1X6"ME;X0$(<D7@XV%BZ]=5VMgaa,)5c;h")k/X[%
%*PsFt"2+(i_NEFX_+?K[6UkcHIrUu)VO;JKn%)\^Er'iJ9s1k_@2;+J3EM&]+H`!+ragdgg9456e'/f2^)]9bnr)h&:e3G9QZq(s
%E)B>tIV7=0SX_=o-7Q>>c+_t[qtFkjO_B6p8-k6qRq[T.L)-ski\>AdGuM8KEHsM%H?-;&64u_7(BXr2$@1^#+Y8qEY"K2/Ug=NG
%bh;k2J//N+!k+MSn8'Ghr-muI]IK=%k&uF>%Nq2\b4G1-6bJl,,]B$24R`oM#;<-kT-&tB*S<&<M'#rNP&(SRW?L*4U2M3h.U?d#
%a9Q59S/+%T_o&RNj(r(*DD<`D52L7eb<WUb19KK>Q;ApKB.NkeDcO6Y@\=#63eX\c%(V[=0iR3C;N4-p_fb*[bY,Ir!cf^^l1tm1
%(cH.p:9E;Fg19IO_Wmp!"tRY;]Qd:@)bAD)drnFV;OB)e<q]00!H>sQS$bHpS'_O/!o,5Hs"@8m'u+F6^AJOf,4c0S.lAN)V=s)1
%]NZ]1:K)"f]NZ0gJcq].-$UA-QOIMMH,a!ApusQ\)Z/qTO'3P5<M3MB##<7Xbs7sT*XG8a&qNB9;f'&O_GPBUH-W%q!B7ks^9pNe
%.7+7r._t-$H3c=4jJt^PeYmg+JF>LM\"KR8.f4W599Re&#"U*::[u>*1bC,B-mAp^6W7h<5[2AW#[h:b;04]22B+%ccX%fZm;cNr
%TB(oRB*ZuTLIirk6c`7T9t>E`kto?s6KZA>6<NFL\U@$t+<OZK`dP@(@4dNoA>q`p'V,HP:@%Z+GXJD[NF;!Y&`qQ<PplV,37ElM
%)Ek5`^E^<jW6ZPrhT;0"[c:M`.Ja\"F"EEba'aS-`]'N,&k(,3]X)ktK_n`IMV?Ccg"1[lnhPdt3-:Oa5DD/&QNj8-Kb-%B\U&Pr
%;5VSP#g)DlAHa9C%P7,:bBn;bMh!Q,o4kL=2r<JHjX"83M^,DX1'WV/?Ki&f/As:d?d.=lGahQ-c8Na3%e%fG*u::S_KBR%9M*n.
%9+Ob>76^r9,25>8g`110SPm('$9-e0T#L6c&UKD_/t\]q_]u\/]nH7ik7t"V"bdJ4bHN]m=EM,qRDd,M#+Ah,er0R"L](AK)3\!#
%N[=B:_iLpJbK"?k:*,`'#MIpEarV2g9'E%2opIs8bG@;)Dk8.nMG*:j0bWO^KQ,_,p[;f0`#aot(!>"+4>uhZg!K2)nuqu@:)'A"
%EuBeIU;d@AAQt1%,,FcKainZ.R>s%U.Y(oX<HQtUo'07QO>emt6`:o(#am0-d*.!]q2ROgd0,]kE`&3f*gmI^gDts%B*)=,S:D/#
%49XWF\r^1QDBPj`/0U&_KfN@.M@L#HD0q9Y*FVbq^.0j_2Hb]XfG1t?I1q;YhY*8,@,?ULAa:P61*KWE#5cAr8/ss;,I)phAH3;l
%E936IU5Qrm6K-9>A6N,OHn2a_BGS8O/KX<V*SK`gq:`<**sV%njfKEa6FjYIONq(H"K2Y[o!u/F=V(W-(Hi&7i.[num+i2QM`$3_
%Plg2X&GG($U0JH(95M`(A][o29rc<872/l6nKopO%6%5B\#JdpED+ur9QquXQmBC?QNjh9``@:l1OaN=AO<>5HPCcA!^%^d*T339
%cf!X$k!R[oP1*boitDE$]3L"0'?s'-47(4Z`[9h_;&id%`!sJT^[Q*?1Gn30HpaTS\oMD)hKfadP?mf]giA$XIi>A>/\G"D4Y_S9
%65un_3sQjaf=6n<Y?0!Ck7rB?YeL,48,MoDL6d\aLI"2`K@l>Q[NVkJlMmAlk<W-X1ReSW(9=B[Q1_rjbZi-lTT_FmVfggUPbX@$
%E+>nbQ7t!WoT&AGB$Z!4kQ;+$0aR)Ca)]>bnq.GkOn(C$MqgY8-T+6'6G2Z4l+@#l.P\fFRXtu?h01$tk//H;7Q*r?538F;&WQ/6
%fSUN-:.b(+m"?jh$4h:q'Vt]USssbce.HWKK%.mWh@Q"M$LOu6o9pch)UCN552mTOLiXsY5L8N)iG62*O>dj35MQHB171i"7cikm
%CshJuE:_edot?>IA%"VOlQnbB*+mkM>%?mOm;V,q-$rrQk[6Rc9`,]F6TI5p69a_s[4nX2KNcu!3/eib.`07;0nQ(PP[ec@erJcW
%`+uSg[-054+:k(DE]Gr:fQSY+^:SI;AdtM@=rR&q\nO49>"/e:T*uXYlS_9t/5JV":oKU=Qbn\.T<6Gk^>jB8@gTtHTZqq5jP@58
%>M^KU0"XI::dIh(2j>>:Sge^BO>iGH&0`_ZQ&Sb93%0sBj!5n'<5kl(r+O\d)4OSR0B#:kOHMUPMR2VrZG2J5n!*c\EI[BhPY1^*
%EtOb*1(dP8I>@pR>![1G9aV1RV9l"Y7CC,NVA:E+94IuDea-'cD%XXj$LBPH6F))ldbDqb#Z?SLh3ERt_i/XJk"E!2<`n=9lH:sH
%#3tU\*^h&;e3]PV?/&7E[bC?ICk<Vspb?cYCK/uMT5UR0r,A`\\+!Q.-2r/Nm%=?hEB4XHeV,Ru%4+ZY,o6P(QY(l?a2<Vs9_1e'
%;]<O.?$K]+S!ZN?`SV84i3M,P<'h7YXaNbV_$7?KT4Cst*?hI#O7Gi=;6Gd?b$EY,Q$D;lm1lgj<;+"qc.3%.:9r$j8l!=;Dpb#-
%oHGT'Ys2DU[Iu@=ici8H=+t':45.$6M,9rpG+\";'noae4%T1u#kQJ!A(B4ZKYs86jRX\sauA_8g8Yoe9`XI;Ige?lN!nXJZ7\>"
%m%i>j$a`PMCcI\&U`2jU*%H\``msa\"B2!OMP/IPH+taJr]\3&P`lnf68fW\#ABBZ6:bTjXa'_h#%3o:M/eo3\FO,UW]!4i$d-7[
%+lTqLL,*JbJ3<`@J'SCs#MAPV$&k&`Q5Gku-4,5\n-D_h%Ed57RR.(u.HET^aMK?S0B;c``<[J08",rVC;a2IbNL@ml_omj>pG(K
%GKYaI5PYfa+K]2+'oKI(]9(Tk^Q-*'W_,PkRtbl,(85#4=0+Geo)/OS]h-O^fl8).=)-EX/=h':=*C\SE++"?bD&&P6\;@,5be7-
%he.bTDm<3'"gBR)W!*2Wpu_PCE/$=acQEE\4eH'eq4NLLX8+XME)8VuRl]iUEd@n!Qc#No\iIQ8J5j4&%jacd48:NmdAp/EGWZH)
%pQBA_nAIOuqn<kSO\YGIK_SWbp$qKp4=:h;^A\dEms@9<hsb@bK9-e7?SX8%*Gh'$.llehCr0SYXPFG68!$r@Q=t%.'Z[?/KqK`G
%iNOke0a+U2$W_-#+'5C%Q\t@Th\O>$/%3t>\qMF]`GV]rg_*krFb=CXi%]Il>dR*ZkCf60n+Aj7,JN3\/EQi#ENoQW[tb-1qV1pX
%+q+?BE>oLkTa&H>?c+Fp4!;PC,2_h=UEQRQ'g^o?GldB8lcA/>cR-n'Hq]g+&NnK(!(ZYc`*%"+SsagR"\nb\d$f6)]q$u[Yb>tn
%R;\5?JUT0D4?hI\E:O>9@iW7*>+ajlk45ADZ2,o)%KV30%`'PQc=iie@(X!Ws)0)=J'*o>j2t6dh8'hYZ08]rZ!VhTR;e0a]Oj;*
%;C;6:G#Y=$:P,:2qMeYNSZ`]m2#2\@%TLL^N/&Uk/DIt.9ue%Q<Gk7p8d(6u,lDsBV8Nuh!(r_83oSM"$tr;e-L.8r#b%`O.g0(>
%EO'?Xf>KV(NEj:D,T;03HRN@2I!r3&CW,fG,=5*uB;ZQ+p%JB+'&iV#KJpf@U6Ps1G,?*@6$:<]X?A:V0G"t%],0-(.CcGl;PXul
%O._?NbGJ]bgX\+I#/WHFneR6^4T+0OnJRPPr$QJmd24tX'8QKE_r*UPVTD,$O`D7h-@^l^bF]_8:(7ts&E@<p[i-M+T`YmCX+#$W
%A[+Q"L6bkr3qtrq@%SRg05W'5;t`t9:&4p!7,I_/1Dh\8?jde9>ZQQ%q\=gW/te/qAAJso?m']XSKm_`#00;\PG9,NnJ-Pt"p,4A
%S\^K*P(&Llj0P`3*9Nu-lVjlOLf"")hTRH2GtM:((qmm<c60bE>:;&lbcIJf^`fH"'B0HO;b<8B&fRBi2qh:U_\$ff0ER8f^-8q=
%<"<AbS<kZNImM(Y:J0*12@kDJDZ&l=qsJ+ANliC6O;k.D.6gEmF20C)@j?6#l@Hqi[1'lH:Z2_hj!6XBguMYtAXYB.TJ^Ft]\,:*
%EJ687]K`=&H,PJ.eo8[%GAt]`+</rA+q=I==-+`E\KP"Qb>KhnajfS63Qq#]JhLUHh9,Y:ch(n"$E4N-1s;X+Xerq>'s8Wf$&GM5
%;%.[f<2n)`4V>//eToR6^;ae0e1m_aj+4b9F+c2Yc'Mf9nju"54):s2F\%(@=\GS,J+IX#rKD6jSW@TcZJ45HW&4$;L:-A7F*">.
%>IN>tTa00YErd=LINt(:9tC$6V/"/KlLYSYa!fM!B2),eR9<B#4h$&<2`tAJWK^n90Nc/oJ-:9+pVT2'KJkXH1ZD]\Ig/CGRlgil
%ln*fDZl/>\7%Z[^%>3E;Z?MZ;=">]q,[XUc%/@VQ"MfOqW]P`4,BZ&ul;WEA(2-P%K;H;4dPELq*Q1`Gktg\1/-2cl`*eYa9LR0u
%Hsq@=r&V@%(&\'Ipb6-=WuDo=d,FaGiif++Fn>QA+hf,_lWosV79qWO[f$mVYh(Kq!lQLBhjmkV:_U(A>+HF&A+1gN+)63S/o9]L
%$M-ja"l?EDKNd7MIDde&57(/n4YrFnQ3rqfl81"1Ai4$1j'KdM>Nk$$pr(q7)N3O4q[G8=J@055\tnW`26J'r!5]rte^Y?]fu9sB
%Wk0iP$X)S]l@e_8_e81K]T4th/YsKONZ[#4*Pj4/$f77ud#Vbqo*RQBDc\$#"hDug0\?W$h^=&'me6h<mIe!GVP(;9)6@:T@b>X8
%<j7#]OYm6t`7Z%2Z5Bl(7pbmb7d9Y2n+oYT'/OVEfdPB!H7u?r/MYX_9rIBX:8;pH%*R+fob58RNct4J.lbK!%D,[F=gs^Yn-W:A
%m5*6#c:B5^O8qk>&n4MXPS>J[Ot>+>&N>XJ%-u(j880(hjE+DW9fel+<;JkmL';Hn3se30WS0-cK/H+G9u4Ta($m2>jP06DQ'sC=
%\+*f1e"TM*J8Ccch9'D%a_4kq)\bVohI[e&Q(C3fjZ_urpMu$8m?1feKf*!5V0VHU?c%o-S1Jc;LpPo0f,Pk&Ic-":qB=qdme@V+
%o(L!=gJS$%m&2M8Jaq,CK@*dXR+d<e(un*^2$ph4d+(;;E9Yu1i:T7;1%OuXk8@VNc8)oh$5BZ>er9CLkP0V<i/oVe<iTdCJ/s11
%-Zu=2m2*n@b7^.u2li&@McRN*W7sc`GUE.miK\0>Q8)d!T'H&VU<uM,<WlUR90US!jT>`c3NqDE#R()Mobk=$*aluS.tds>rdqqf
%WS3=Uhi7#dhFh4s<Om.%"a4q!^(o+['rQa?ae=<UQ?c<Id_JW>^pCNH[K\0c]*Fli1=a*IYBXGl+Glc9*X1.J^l_j<,gaVFR1T;&
%k;CED3a;gWp8c\T&S,`bf!f?)O&_8.J!hBXRF)Q"CpDMBh$72X2o7>*<FpT4)9G8oJ5CF]Blo7ZT_Spbe8-RnoTo5jNO*t\)#H:a
%f^?3Xa$_*)JP0\iZl$kF4tnof7TtRh:YL"Kf;;;srJr)BV:D#(]'$tA7S[P>5q,aH7m8aRV"N+T1^a:gL_ahF`G`]\%_2L$!J#r;
%@-bD5)`%X[PHE5:=?er5R)kIsPu9r$#co,q).,3I#tDKWZ(,Vm+Lkd*if[8<MA(sDF0f0X$"VPF$/m%2h+S'DA$'4GbX<"nIuQI,
%Xm4KPfYo4K2+i1%h&0W\>i.eH=oY-(c#"]%r7RtFTRDBq"iGS(Y-qI\iRZ\bJ1;IL3p0G%Aq[TK`al99*nUdWii"1.):0),iMd,q
%Qfe-RMU&Zh)1B#ua(]PPO9R$eF5TFPF>uGjBY5inOEEl]R]onj5q%2"g!%6:D33ka\^BT/;r.Di;PIW`85tJ?(_r(gOa`tT4HkH/
%J+S#\CTZ)Y8:)gt;,\N$!(k`\i9Y_&_,EQprol3n@PjZjM/?EI[i+H/8Ge@8q%?RliH,'NP[FY))5lWJZ*/Z17oYYmJX3/Ac9sF9
%_hM.Tf"k:M4,9'?o`ac!C(#1A)[E;l`H;<=?eEB4=X.%WpP;Z[/&\r@\fHS-"=\!'TQDHo[=S;b.Dbc>?a)fXr3u0Jp^;OBh\)$N
%TpnNFJ8A^n^kN4?Pc7a+?C,@&^hDSdL/GJi_0,;c<<I>h6Ljo1"@#=e;1nlQSf9"g3./A(M.H?>AaZ:QE:ZJ=a+crCj]G>i^2Z1u
%&>_!Fik/G1Tk5q2nEX"t-s=gF""oTioV1;M1O?"M"=R`>&>j/CMGdW-S4u=1Wi!fF4o3*]2H%E.c-)ia:m>Mm"MVF?f5uhr?Y`U$
%2.-J]/8Xr<QJJiMq:)k8p-s.&e8L`*Z1`aNR1fC(A=^V=1q)=CS:*%X0!X"@W9,U8dC+bOm9Zd',ZU:\M%En2UGKED<K8o%.@5]_
%`"P>;5RIH@C?X>7_I6e9otA.`+.<EMAIL>?f*It;GRPQo,sS\XQM<dTe_VQ#!bdKG+im08gaMD*8WSS9'j7_4`f.&\
%7W7>+9)jf(9;2u**6ULpA2RskH,1<Oj8Om,!Lg:5b"&]@K)\"+>+&2Pelc6c#ePul<,>F/TG2)7LM$,Bn`H.E?B2O)H8,DOo=^Ym
%!m`%]/d1EQPb8fW=Rb/?+oD\Y0&4V>A_)s5!g9(K?VVTe@i2K9164k/e31Y+;!,U<>dq*cQ)9GalU?XnP@\pW]+"WRo$,oGBWBnH
%iTrt##M]BA&K]%Wj`NKXbfc3bPbq7=AhM?-kW)5;9m][,p4K&-,!eSbU0^!US:#^#*Drtk:,gB!I>&_a7N-`6(65'QP?9,!nc?(-
%Ds+sub]c43h)C;$moAA.\N_B[3AEY'dTIH<a"?kkIu9FKrEskXiu)*)L6H+\atN;@pQ<S'DJ#]g"&du)*]n)PJJutF&CR"S-qnO'
%'p'kVOSPjk/tG.>e>UA*A-Qp<bA`H8O/#gCKj&_s0Kt2D5n-EhIpCON%TlgLrMf?rr^Cne'+-<(De@?bg@_0Z&6-LTnJ]d=^;^mS
%cDACmAc'L`oqNb0\t$Ok?N5]nDL=t-8Vb[[^I8M((]9Hqe9M$_:,uV_%K.9BloY9A=Iu]"`YoO_-[&0a6;-,MBR!m@QuQl#r@4%A
%8*j[4AOUb$d#J]FD-7$i"ST/@,O6jZT0m=uFfge?KJFG%jUaQ%7u*3M)FjSteP7pkHn><EMAIL>?O&mH=6MeL$l9BtVRLMG!
%Mc%-B8gSP1?u6&iJK\K3;#,9!JNr"ff[ki2e%^ek#DNF\AilM'/DC\KlB[`cSRE&@<j(R\GfUJVRT22@7'Hu?8ZLBqGWp]jXW=8`
%eUGr5!J%9@mq(&r*IPbL-L:6Nb=DBk#MEsI-&Of-%NcQ@eOU\pUp^l;BPWIWO\aY40+Ng.>H>C74J1mCTY[e(FNtb?[K@VTrt+4]
%mq[#BnJ*:tSj@A8oYW`V:/@T\?GJ7V;5oTh(Y'b[[*4%#6S89\s6m/]&t`mC'lPMc.G2+!.=g(I2K58?E`GX*DgblA"FAp.kqfJM
%eC,(IpVmr(T6io[1qpsSfV3$N\:*n37kdX8SFjCKepT`3nK4A=cIR3%:D1E8n*q"E=n**MIQq:D*?Q.(`$hSM2abLN4r?cjCY(_V
%Y^ogmN5=\W*1YjS&A;mUdj[#F*6AE)c`#"5]Rf-^iG>sS&%DZuEsBg4-T^gJ>%+\Q;`Uc'4WZpGGB[Phi!M(u]SOI[b)=LQpitCu
%L:3FSS\W;4hcqYF]i$u=Sh%c9AWE&q*pE7J`Y-O:=F#_3G%Hhr8+5tkUf,^TipH[2\BA68M1r+dFu'ino^T)9AT3;g2E7#!.qsM+
%eF5?W*T=g7ORO=%P)jSG6g?[dp4=%O9@O.+7FDUX(Q<Y-Ge<UYS+`['h&aBZpm[3fi=r=%D:hmlJ%c*)76kD=gc`Z&P[_/Em/fe$
%NKX"<.gY_0NE`&!R-#L%bBsgK,ZFDj^K"VB6/MAq\(]'fIZqA@At*+("%Ts:VlAIK3Fa<^$Pp<[es-;Bp\@g-<j$8?9Xbf7aR^.<
%n%gNi>K`XNBY:BB@9XYmF6cqZXaP\8dp4g7U^Bc\,mJYY90_UcfXu>nKa:p_CH/u\Q-.hn)d5UkgA3qujQt];Khu>uoGh0O][@t%
%l\(F"B8miKGVlJ\>oZmgJ^G>VG$n6>/.\="RDRTPRGABfV=EC6lFS)"6s=Zb'S\C2]<]7lOhU23c's0k2oMt?6FhEO"Q1B:Ro/e!
%3rFhKn,^);XTScYoI<8&@H?2..@8Q,Y@c'69FbWZ)E.UJ`kD3WkathVn^LdoX]:aMCGOhH[s`&\]BQTtl]1'Fe2R,La(4Jbd<6KW
%rd><DQk#CWmWKE`JdCq[")AaRC%r&*#U^&Dr7>\Cb#sSUV1Q",fP`r?r$g(CF6^1pikfg7el:DC5nPdbm*#.2>73eE]Wo0023o:6
%Kp:"&]=KSAL2um"F'6@mX+%\,ViIt0jJ/T=2O+Gc0U.qic5F5SrHdY#Gc>*u/QBeF%ddqC?Z!L_lo)F?Rk[/^5PlGhRb`9`9506(
%7a-3bP^IYTE:KXBS2uN-i9$%h*=:"@DOR0a-0-\N]s>\s^+lmVgo+bs`PcZ0C&KZe>SQ%pcLN=;_;mc)mCCr>2"B11_U.f*oQgOB
%Du"F\jL"e&+5t"$)=O:`HHs^ilrucd:jkqrn'WqbT,9OcDie2Epm=5CG_qGD)(r];\&+uM(/g1U@KCB+nT+!-E=KF>?+U(<kuIh7
%P2)Knp[$NnlkoQ#/`h3VRdJ2M1@8h-qB`!@AOXl!dAfp.dki5I`"q)RXM`tqrLFG8s7Z13nnjPLO&ssRic>b1;pWKcOjFfEH)m*J
%39VDd33Vh.lW"pBg+K08O5_KH*Rc,J**ZsH@nA-Rf<oZT/&-?5q/mi5'iG5t!W.LBA_bk&\c"2_o6l@jUJA3Q]Rn-a6iM)FEq#T:
%]2'=!&pnp1OmNAG0&m:ECp?:#DX9ELp//Ih>Q)6%&M"*A)LA#(89Jbb`p/@-X5j'(=RjQb:j>m8&i]5[/Sr.iQ+,-%s2t)'B1)Z#
%N41#sP^cq+2`o$>.]=o3>K-75''7c\q:T(DIe`%;_?Q"OeZ5co82TP.=G6&[l=&qn.$5>"fSgZkUBZj`pNMo6MkAFq\TsAZEUq0(
%dRGRM)S%OFDs<S>_m##<&@?r4a\/!K''ceuX<0g$cJ`E922-Qr3(!*5Z$IP3<d^,Ps'AU\#k#95!M_]CVY^/%_DjEiGlb`r[4HQq
%s(8D.hbAHCdbU3ZD&VbT:W$/^7<&Fq[L">p5e797XJ(/4=M:DRY\k?0._8\N0O3t22,6rX62NHg"jOM2U'uuKi3K"?CRUjG-*:_o
%_n-V@iV.$a]<SW1"Mg+8#<j0n<`9D>kSCCUK8(RG+^aRl(`$]/i(:rYZ7e"A,rV\_BRpJbJ2&Z&6IaX>jFf>*HF^`>*BLbs7=2"%
%rLXQn2osMBETo*G];0qHqf86,>8:g1]80Et&aJe#8H`NPRFu+4.1qMk95EhD5,3iH"pCin'`r0C\877kW9!/Jg)^;&WrC=5]gW<2
%mGgs(\`g:q"Hg2,*hCf35]HS&L;*gJ#o.d+?aSIG=ff[`"BZl_7:P-S#8t^?gXhf[n[#$Mn6pIg;0Tlh^hOGq?^DUQJT;?=5m_)W
%i1cq[_4:@uI[qNQ'F_]mk7_fhOCgL;c.VW$g4$eRT-[g2*!,iNo5-^q/NE9d6KY^U;fMf/gtaklm?f5,eQ(%jCIop=3tAC]^E5[4
%CfB`#kt\'t`qgJ+gC5Cl[KA[W5Y90I[]\/RQ`B2E67Z+@Pf'r'o?UO:F;;AkHae\)mr2\R2cr81#W!pGQ&r5i;IM@:p`PV#FCR>]
%;kW\m06'Z;[?2dod9NgI2!j.gGA3q^;[CZdkHP<4"kjQUR4IF"YpuS%!G\;Y"Jf$J@BbI0Eu>$=,Go_g_=!Tnj%pu+W&1Mr=rW7K
%:?dMjqqf2n(@D.h"eWRcpC0Fb4(aDEYtM&#Ftm^OpdODR,N6<J(dPHS%DA9;gaBR]M\L35[!f!?&JeR7=!*c3^_c!8S'LumKFue/
%+[!GfL-_MiP,P\0*&D4Fi#h7XU6;s.FF1L;>hS;8XW+l;LErXV,00i_q8!,4Jr)XC9%=?![fE'.\f<E@3n<432\]RI:*njGlW-W5
%C+%;:Ct%-6N"Q;\NXZ8O6dI-HSh5R>Ddcd9KQ2W"`X3BU6*[r^?rMClR)bDPl7!lgL%!GsS$=FT$EW]STGS_tYb7C.DW8i1`UK"]
%WM>.c/abkfljTJIW5.5PI&aZfijT!ZiN_YSCgfteEZ(jYo'7)Mnh#='>1%?^*ONfG/XHBb8@DUt*<]T`f=g^a<PUe#YA8>Jn>+Jq
%7SSpSh!7ORd&Oh;fqdS\TdU(9LkI'/$KK]J$BbNL)bOE:)FT1X/7L6/(<c[YIY)Nt-h"A!!T)<sVK"qa#kLnn0Krn0Qg><S0s4mT
%Gae&3#WYp36]q'P2aOBH)A'07lb$C8:<rm'0SLZ<B*>Df]_:Q7L$<0&i0LQdKeBM:etsVlT+*Z@*<<Ao7@`8<@fP15TKkEZ7=`IM
%[>)9R$;l!Q*F,;lJPkBO)6]6XR8<WE+C(KGHiV\C^q^K/MhE?[5Yi1.eIjdAb5)C!WFmK0(gEQTG\.L/2O/`OOabW+KY]m(T]`WV
%_X/"&!p%75U'Id($jBmSm=>+)MJj_1?_L[[&-ro0KP*Z&"HBbc?:F`S216rJB(-OC;P]$E7,j6bP::j$Is.(_%aboo]"DG=id;9C
%m)c^n7%`'EBaK*EJ5'Z]XRa9KT2aV>%HbO+dN)64r$Is4?]D7[5.&,cQt*A(gLfG5&-.Dl.i]jSn4\C!?MV\%oZH+K2l:V6O5lZM
%:94rXID4_#:YZ5!>e/2@+*Wi?7L>P&"Bde5h1B."=&;NiiQHmEZ!q0tGq94>,E3g<bP;]MW;UsR\DJ%a-$B<;"_pHq;q//205(%l
%(AW+OKoQm^pmkP)EkDU)B5F:2'P(p7_e55SCKI+^ZUhFFJ$<("PY9Lg6#il=`B`G>.`.PU8hUHG:"]H%L-0u_rO/@:OIIlgVcmRS
%;J/l-U=SKhCPR(!ik"dB>:g`;=mO8f-?]Kj"*aJ8XrNT5_0TK*0d81gF+Iuc-p``XiG9uXXB^+\BY@Z7C=C5<7oG1Q)p*SdLe+iM
%Z-KV2lml<kodKg0dXR:%'A*b]h<it,Z>e_GC(2Hd\Z8IA<`U>T^W).Hl5(.7H4?m62aRYd5Q&OTr(jN^In">2rR`Y"nJ`nCkHra8
%_FS&]b'Yb\en6P6*J;[9=RNB.H>%J.mrM89^)k_u//!DAor0-JY,ktr30?;B'3p^<-5MXIUF->Q/FAoZd3=m>3Yel$8I*jVjbtQm
%JZ97RE<4jiI)qN!K;j_9TN)dg&N$0ai"e8?3g!5-L6t&kK5`VmK#g;YO[9V,FBr<[S$O>7c+>?%5X$&?rY@d,9))2e;5FXcMn-ce
%AR#0;bt8^$?c\mQl]Ph@=Pqpr0*kNAWW<Voqe"7Gaap5>p:LM8jF);KjN$G_9;k2l[Q>qrm%9Unc*f!USs<,!`GKf%\#\?[]PC#?
%\YDPAO(2i5PG@[O:NU]X"N(btRJr4S-')`4G:7L!MH[uK6P)e5bm<2fK#`[USC#>QobFWkd@<DeniT5#ZD&XNZa1Kjj*".26gG(=
%<dTZjF&2O\A'rrS'\4Rb"b4_V6pO3;NV@r\>K[D*rTqc".7PS]RokK,F5(gl+:SCEE7=bFRg.j9H&jnck_T#p?'0sbgDt^<r?9Om
%.,Nb1XL^[G2pkr,3V=aja2XksMpAm84+"6Q+3JHa]5stc0fg%R(851dESS&-E"Ze',\[mH?jUfZT#GeO+R;dmNes8!GhdjMKol;a
%QNT\&%T1F1C0dM:74n0aSiH)Q%@8A+?FLK+Lp4k\XMiBSgr,20h-*$2(J!%52n^t#1QP!7HWte&caba_X-UkSic#,=#'['aKNDJ*
%)U%%p6K:--@cUh"Qi/Y8A-NGeVQ(LkPXtUfMnUhCJG8/2E^g\Ae=?33MiZdneWIX`hZ2B[*/dq3>CZfph,U;\5Lg(l*V;(6.!gp"
%4iWh9?''dt+1q>^FiJXdiPsoY:'\gIgD8gb0A"dS(?2guD#IA2?:Z;a]Q/RGGcILB,D1(Nj3<U$mOI6\Nm@h/1,qCZaIpkHK@l)H
%/mqHZe7H";:`E:)U*.Q4aCUnq[uC1R%(8VeFuX&p,ET`S`SJ6.f`rG>gDgU@h)P]1MH307I>:`6_5$1^6r5:uK3=ZCrKQd]j!+@M
%f^p=Q[,0!BWE)+QerK?K`g<h,NiI[q7pM]5]n[PF=!Da6@kHOig\H0-VgQ-_<s+dcUlP,:q5X`+&*UpgpLAlLi3U'-(CIomKW(M/
%c*%`:N-?]4;TW>W7EeUlp.1OspN_p-]3WM=7>mfKETdDYGHCkgk1Zq([2k8*IDW*1k6[%iC&lf`Z"B?d/BPVrZ]ECF,$+=Oo2g?9
%nf(b=!$[Ia_DE&)$K`iG/=q1gPM*#Q]6cB3n]/:0+]T0q&dsd8+5dVflLIm0Z+L8RBB0F7Sp)%6Bj+0&^B0Jl1HmFh/:U5Y7_chQ
%N4;<uAmGE@a6'4<$\<rCA.'PLk$GcT`'%RTT2iEgJuM>=feYMqc9om4d7pKailGEYCRSEL'`2ht\QV5].Cd$adhY,kS[DJML#fhs
%],'i#,B4ZmWMOo6DkT5;+_JSJHMRHXMHPWt_jPd`"c8h/:=ltn4`jTT'].C;s'REXZ0AM!9n^%=Gt.oYR(YsiTWsH(]Nh(Q1BkW?
%E7BkSbt-c7gMqA*)FVlq>$+o7Wl&XGJ3?Z`*FieaS"4%K&*h%r6#>4ZJZsD-on6]bbQXJP.,=;!:F>M*O%'H`ZDTA81@<e??aoM)
%]s.R.q`]\lDQ%"MU=mr'O\bJO\VLm/OOZTSg@GXtMYoCO6i7KJnm<>hBqE[lOf+s\1S4h=eW*I&%b4d]huge:AQVE*j%_(@i%NXY
%QpBU;PR8,6i-cU_7XcG(1_gH2\2?u)S1@fknL8E0PLs-:jj`tNi[uD`+H!QA7+X%e0o>J<d=b4Z[]X><p]S&Xg!ooQ3cqJURK8&*
%+SG8<"9\j8XNj3@B;R$sj]Uli]+#"Hn23XR7+%C"Z0/bR'OR-')]mae_FO+V&Pp\'LojdKE#u+QgsuWgS/RMmdoRht!iO89Ai9"A
%"Yh)t.<+E.Au&UaF?4]T37G00Tp)p?3CndRe#4K2D@TBPGR'Ln0QtK-QfQPX^Q\p8lES<_#eVi3%s3J3/cJpPit/jl!D%]ef`13J
%(A`;sN@AhJ;S]gF9a9SJEL^YET^FF*gN*m=ge0/V(;+',WHRO4JBd(:I]L?U"=m`8Vc,Zl-6]n\B<!Or"MLMgQpC3NMe\O3?DXu0
%GNM,C[&H#.e`!5_JW75Bg'r1-GCs1J"Hb&iXad*eWN3BVjf,fS[^$0KOcPKR2F0`,mMXet<5$RdBa8bEkR]"1j;H`Qg=V??[<j5%
%>UTe`_RabB;IN&.%-.mq0RlPG1l*>S+UA]H^d41sh-F$HRIV@3Su&UbpH7[kGMKWB,qo]?j;JtOj-4=qh-\@$h#9=MLK'?rI4Vn&
%@4,#M\;.ioZUC$+J.kY>cN"fNNYGU?WH+ur'S^l<X+8#0W:,ntpj7]9qEO6Zld&k1NOprb"^*)s'K%_\Fi1RX'`+,]5^_X3HJe"s
%l]&H]<F$sB'kT/u8(?Q;Q.!*Mh5)YV,C6b#4jT/fpXO%=NS\fTBRjh%+BO)-I,(_>$2qa_op9:na7:,="%u;PMTJ\#X&1`M>:,oE
%1NgM0Hdq$4!j#g#i^@aQkL9KcRaPIb&TiZ/J9[8k?>Jsu1-!Cm!X4-WAH/c/K<NXK?=B<!JtXlq2Ct@h.Q\LJ`&3N.N!^:r:`(T,
%-\O@tO!OR_?^[pLi_`C/B;@@?n2!!rML`4XrKMh#Psk^PCdnYH(FnYXBT8]Hb/\0RiLt+RP7VQX.=eZ&eft0gZ&OWI1Z4O8&7]3t
%\Hq_$&7^<'/C\m$K?3#PLJ2G=0Q&0VE&$LHr\<@ok]2NOW*<Ymp]QKb-b]ZD0"U%W4[-\3\.mK;=FF/1oi&eD1_U`bUnb/o(CPc*
%HgpIiV8Z=e_M4In*'#6XTBJ8X"F0)DT9anKRluBZi5U>CqV]W.XO<f2eG&L*URW44riU//*YupK$m%cIEPliQG;$+blHs.D^rqU6
%_k9!Z\;-IgW&>AP,#k`C/Jk9n6O="`nDb:BHmr<Bh(<8mTB"5lU_45IJ"8e>[h1oKE#Fn1`21+UIc%HlBT4TU`ZXY]pnP=_J)4]>
%c60s!3Q>1JiG^9bDg4OQ#ZIjYjRK=_)+J?83,hDmU_E(=oai"H)q!R[jb\KQLamoWg?0l,^-PJt)CrI610]U@#=@U$^]Lh,GW3Cn
%!f'>F#1*<bQ*<$Nn)43m7udkdPIi3Y@pNNb!cS/qa8tUa`sbQaUDU8Wih\<4,)lXfEG4ZR(>-u_W1k)/9Hk_V@%YVk$gFlRUUlC,
%c(VKUh]&US6.'.(eN>*bGP!Kk''kEc]W(E*k+\eR\2!Z"YanTr4'1gE1oaG_B#gq-["QfL`37#Q&f7Wnk?<!fb'0c88/r@s[<jhn
%\aanPnqT&6;rb#pgu4`EmG9uE')9$j=]`5Rq?fUENE@g5=^at$"0:nL1iEPaQCX37C#`L&l5=3`d*P:8EL\0@OD<l;4h%oB>2P&6
%RD)>\VCqf]"j%ofC9qDBa1,L)>0@9^iD<&lC@Tm]PEW@Ec`?cG!q$sA'SMm@$2dq!A7AbR,8A=j3dOm_$`cokP"+j->:=heCILl8
%d`E/q%)I)^)EAIb)EZ?h_P`V;Op^Bi_mp%9^)@:6*9/oc..k"On+J:O/J`T,e=p5LRVm,/H-Xa!79!$2\6SHqT!YhM'_Jn?4E76[
%3!!EX</3NQLNtH78n0cDlJ`k(KJ;q36Z6o5a#"N3Mld+.[\=kA[`DISTF8OS\1,-C@/-h04)-tCE)L;+LEhCcGR.b)eAj*+2ojR(
%B@8XZd#8j6JY_fJ0.M/p?dEuSWqfQ(BD]_Lbq/QMg)F9Ls,XF:`cS9pT_%(RfbKi:h'^7T\ENK)N2."j1GA5FVpFhd=mAR'DQ;AH
%&UAJi7[_o:ee5[BK#;2952=PM%)Jo]Y7oebEmgpX,-=^@)p^d'W_LYd2ckdf!]5,5DM0=eoGKDUP$?CQ44![?nR\Ald5:9r&WH-t
%+-r5=W%BE:HK)GJ?!0)KfkdEsXeL?q,$?t^\s-tflUM7jm@tmSk"UcCYesE$$0$c`DY'h?qToE0W@)u5Y4EY\oT^qmkQ!Z*?_b+V
%/X_u\dO[F&\OS8:,W++LHPA%E)^K"Uq<uY2H'#D5-0)6P;lmL=DngBo1k!k04#B8r8Ac%]=RVn3Z,2"XS9B8#:?W)/L-gWfH9p&H
%Y(4r!mHu!V0nA0lM7j)c5.(JbW]OK,XBiBTk!*.?O`MfI\fabJpo'7.1fGO0kp4T_dir2,@B\t2!5i1>&m.@DUUO`C<Foe/O)4k+
%lWn]W@u5\JQbl;S:5K_iHZ56ig4&PtB`,8HRYR\#==k9sYRS]'W#7;M\.7JaQ*kktIs!h!i/u;j.)@Qp5u\1TCV4f/aAU.rY+4=D
%a/:gN+Tdh*X3pujO-%<q@*6dL#]J9O#.0Q?*PEB5Je8)6KE-(4b`N@9a,$Oeo0C7[@\0P]e04_4I1JWl`\9U'34ut?GmhElTEG[Z
%GWBr"1%JAd`Z8geCH*O23d`V\/J@+H?lu@&KV`OoL+o:4aRbY7dlrh>=-X1jRMB:!D-mAn:]tPp;%luVSSlQri<`9&`fZE+lF'Ld
%#.pc:VhCmkT!"J5Z0@-iK@;ogLULRf2dbXPk#ceqD\$b,'A&Y?qs/A/9FldKKeuc4*#`@*gfGP%J$;gh2VfWOJl,$SC:;/2;98@[
%#RlMpS+pT?OROn^F,**8P)h-7&fj?KX!WUpJVco7N#[FAoF=#HR\7EZ;O$QXWE_UV*Sl=&B1JG%ghueu/665E$8@3ROdZ^0$IZKg
%p6-guVa*r?%aLT.jZ(@oI&jqLnX@!?fr.5E03#8=^4d?g1Y4O%PLS.6"GX>U%+.N44'Wao)F+UU[-[*\glA`>bS&sU>0CKqRYtPc
%V8#^W9mBEe.k=r;&J,@GIS6anS%BAd10H&-3lA[cU@"5$ni`BI+t2:r:ZQL'A/&kamq6R]nD`_-?a$>Am%[fg4<`@9<NNUZ4r0GI
%?(%dHRkh]QCO(`ihD]>.A6+SWginh3ZhbODWJ(a%&#PU_VfKX,VrS6<_&f9h[I8Ued$qpo*5<6LTIM1;CloC:cl?ODhV=pFfg0L9
%pp',*>V<WhV(S(pq\99k_EA<uE%h#3MG9@jTX_P-LfD4pLb#VCbqV8>cA#^9OE6E>E_-<_Pq%Kl_!6L/Cg[oUM8Q&UWXFnEiaUQH
%9iMA2S!GYBhTQ[mjhmu8q;^Y"msunHfFu2tF'VsY.]W=Zl%2XgWR:ea$L6:\)Wa"l\mqG\pjE,;FOb/0VNZM18=Q>E1g[GhP:?'a
%<V#Tk+0X=W6c#PS!J4t('eZ%Mq6Eg(b:=Ep_3[-].r8l7efM:I3BRGbBR[QEqWdZK1fW5\#\5>lUlmFJ,a'9?UX#l/p_:VQ_0`&7
%&eX$8_D)u'U_CA[Sa]#N:,'4s?*Pq7p3/.B`F*@2fd'+'m#*>ofZfJ6DL3G+L1c"3&CLs8U434Qlg21S-j=[ho@OKMi`"Snd1,k(
%,ft4*oQ@1WS]r9E"?fhI<2/sk#'T!0WWaOoFTP1?8oY$Di5:%]169S+K=sOPGGW6l@K2@&e&9ltQMWWmB'-g)"mE#)ehDK`1Kl\3
%9X%c\=BXBMQnUFS\?U3e`Ac1;H_=j][(qP`93,Z/Zgjr3g5/q4;-YI%d%d@^Z2A8gEA%t%*M]LnW0'+c#!srp238`&Pkbe8UJN\t
%<459cPh8!1`2)qBY+ap6n,,.HBI0K'YFpLo(f<-)-mKLEp%_!9=<BnM=g*)h$dA294UQ4^Er%I(,.\nP&M'9,cOuZ;!1lUfK'e*p
%9Rs9b5:UcjK_,qgcH'Oq9k@-qX^.c'IE.`TR#X_ig5/8[N]>U/\)kXVqa<`LiNY`,<uq[*,nud<q/"%mY8M]R*"G#@jW;2"HOgfX
%=.sX@**_G\dL&C2&U<)7Vj3OeI%R.0iY#Y%2g/Q"1lrj;fTAhZTHT\LJ)`hfeqgB2$BE>ncIPrmWf5g58'#!?>DUq!HG_1cXYlXk
%VKT+G[9XobhsB;(m#<um_FarlIs`PC,ngp=$'(E$AFZa+h<?qTaUc\DSN>s:X3W[cq+8+[aA?HSW%gK!\4qhGNAbUr-&Jk@NA?Q7
%<Z%QIVW5>/<k(?r?0%,CK7l,a0S`sC:MWpud7O*ic6ec*ZImT.Y*%"R9h]a9R,qE^>eX;KT'?2_oY`^"bKY+`T(qK2DaHl`b7'1N
%eqCW_aFA+n-;_MWlk*lP-g7Ys\%pQm<g,CS6CRIqD!'q0nd(&na3k!W$o?1?DY8t7DJcSN,#@KLo@?g7:H<bYS?\q28a-7D>Oj@0
%q\S\h#8Tu/o)8oBhfe\Cb(Ee3Vf9;a;i^RV[+@OYMCR$30lV!Q?>tX$,)k[0g$]+?:(Wm8IZi"^^N(l%Krf(5!InsAf3RXoa;S7n
%0+Eg&H7a-5AXc\o%CL$eF$J)ef#U7:Vst4G$c`<Pro1C[>NW73#;$]4<AX%C2**r@MJOKrTUIg8bQ`Hf`WAb^Efd2u>`[^7:>.k`
%dB+E)c;7=_[7P:&k_"cTWQXuoeK.SfKT:a)!]s"1-u,49Yu=c8[LnJl4bU`)'`d'O<Zo,<FHe9$&gAB5Fi@@6ogV`%DW%3c`go.C
%JOVF@1^4G]Hur3>4d$>maD+!S,frh`!%6AcV[L;@TT>TNH*A!T&QB&L9].ot5DUZTJJ[]?0Hmr[JHCYW`Fpa/RS$9<P-=mRQ2>p3
%gQOZG#TsYT+nCI`6Y0X:1li?1/$[7jS*NRZ>/YC&+DFA>:7hpKFQ;3eo!9f5'_rH'&DNPjg/,gh[^N)7DI%hgLS/3Vn,!ECA@8.L
%Irkn3GI`'FG^`18n)Db9icH<\81`>X7ZkHbel"2M9Ck<N';R/JgcE(N;%sNC+8d;\(klEc:+FRV9ohpcVe8a2<X=ZuJ'_^eC3cEc
%0<J@324qAQ:\s7i!fE*@1hL6K]W+uTi3?E4p>8]Z0!&<rl2`1n&g(7kiL'u,b:RirFJa1WWcX;rU)3n$GK&@PeOl.Spf]4e2`qfc
%<KN&Wfat*56QZ[A\'RkqH`*pe"WI9Fi)BE29jZVp>%&;`Z4j*?o=^!VA!:q;::Md+6M$sL2\3o27JNofj:<KCk-M@f5XO=(4::/j
%Fbu-p<@tr;RFd0!oGYEWgE?a76"[^N#q5.6+>eLfVK81NIIjIfL:?2W\'?Lh*p'7a2j;RE`^/-L5q5p`b7LhT;,oqgT5]5D@kZs=
%mJ)3,H#<Y^aY<f7T7V4aj?\jp8Cmot*)g34#a7tCFNCo$A(r<*bO&[`TArHcOom!!mm)dG!N\e!F2U58rZMZ@Gu15;)[WH.rERa#
%\nM`r\BEE(gb%DMBd/[Omm9W.KZ-Sj+&?^,(`\fo$6_XnZH<L<VAZ?N3*)bqh[k"kbLHRrP+&%hV-p06h+p-WNZ[-Aq;WFUB('WE
%@6AQ'Qr6RBWdNE2c$YBb]-Jr$1Wbjm$[D!cen5FqZ4H]BniQ1j&I+r\E3@X,@7V-$ds!AK7=r9*GUAIO<DOZjanh804oQE9Nf!O4
%edAG#i=![4Cpjj<-`cP)4PAPTP3ruAPdDk@7k00_?IZBS0.E4?+XQuR'P*dXJA%aaiMt5V&Ma#N4.Dmh^1CRND.:a(][3NB#uUlr
%`5!t'V^OOTMNM+dcT07IC)'7Mllf5ce"LPdc9N&b`c2uH33&T=qT2k7I+pu8%$L:_THX?6nAL'Yn26tm]JD;)+;dq4hn/7X&@Br!
%\N))&A9G&,7/;8]'o##9[huoTTQWP)QHWkCeN(\Kemh!*`^P%2__nj_G.6B[hLor<gfTZm0h&0uU`2M:5CV2Diub=EcChIc#4%-s
%,Td'UB,u!F';EXY4t5fSj3&%fC(oUdqatrW5Nm"aU8/[kb@(BA,gmN=LV$@$(b@LjeZb2kfVKYan.:i0&r%N9]D`lL._;G9;g+QR
%qo4GqU;CjfBFmPQ]dG<:CT=6ONf$0QFDB+POE,PF%ur!lE<1Jll\!A1E7Le6G/%A1W:DO@]B0JX(Oklc1gbTF2dLg&,p-Ba_8jVG
%Z\/"lb.F27CAHN<9%u)W:Q&Q4^06PWPr9KOW\\&dOr*=/jBM8M`Hntif6Z06HAPP#r)k@bW]%2Z1NcG+e3`K&l3QRe$3hnop$E3=
%WZ_*QR*fpX+8ugVgTH/tN!6r9*P^Z$1:QI!eg]Up3o*hbTE`7jd4`h!W1'Wa4Z5'iSb[6&kg*Zk#[7T3jE6)p/Z1qJYZc*`+`1F:
%I9<Y,]<NhPQ`_`KSSaaf*N^/AnHjj\CFL1<r#l"M=f++VmL);$kh93j0GIl&]2/k9>)Bb/O-f,l/^e92:F+m.).)UXpcTZW?E";l
%/JTt]Sn8<I<B-3O9+NhGR["7]qKTU`@645?lW(Mu_Opn(7\X$Vf>^;kDi,<O6f#t50$f!lp85kd\[;j;O#]XoKW0>RX6R_4daQpB
%/uZ#%F=GrZbR[_k:U<oY$'U@WGl2/p"`/c>0D?Q]fhud?[1a,X6+GdOI&n8*,f#s.JKAdeed2@bQN[4O):"ZF>(J$0-O1nIKL$f!
%@nS.pb3rL5*2ed&/65L>1DNJBJZ"iR803.,TejB2Q)NhGT%m,'"9kA#e<#i>g^?ZJ_80bP)N?@Ye?g_lS\&Y4O?jhZL/r+\SgGk#
%jc[Eb)"85ZE9"<5ZgGR-_kaIKe6s=]I&LUrFM#1P?R;%$m%br?V1?-:OqKJt4WcE%H%'VL-f(8$3!JIXl2C$"`B'Q?=ju:D,l76<
%'=P;#,O@3C3X4q?@kU&E1*8_Q$LkSXC#AIiXsoBD-oL^r6LZke/("8n=hY0FZrDF9r$6FVj[L[pj>;M)OtZ)R<84E`UQJo$il2;i
%.Udp>D$ftXQC*[(j]TNM!US?`\1:_<M$U='[SaM&E1uk"26%Xr#U<*J:^=s:6cOO0pa>`rT>'n(&))H_nYm/5;2#8RKuqJ?m[/^-
%!/XI;[R`.ql!Z@R54<dA)J8WUcPGX+$OmQ"QsJo#fUN$?SQa=ZTF)4TC8TAZ).Q6#*$Rm&-WS[W6W)VM3ttbZj]<EMAIL>:_
%A_tWDP:/Tu7;s]tm@C8':Db,DJ88'0#EVra"@hm&WSt$,>;#lNXDL(V!;'.5#oC.SC]Y`^n$_J\#DZ9HF19Y37(e;`.-a0'dgN7o
%&"?=l7jG#6(:g0d`_r$o<$egS9Y@BF/C.2mGcC56@[:$I@,p&Fgg5QrfU@%eIXVi:ebi(OaU2nAU:uTQU:g0\`kH]LZ(Bq`Wa,)>
%R2"r24C^Vf0BVHI*:d;3I'_;7fX!Q8C3DI#)YhtP[D%megc@=]=<j0om=!63J6&SkX+^3p?VH1I/f/="%*VgaTI+9o47)$,oXIdN
%.="cr2XcDR<;\&bf;[Cg&k<mLVBrn`:ims;[[E*RTUs*4S<1PdeT_8uBBcAaA;R`_r^$g:f/Cnn<'(ZA'k@@/FgO,*'0/TN'?8:%
%6JFk=2@ME6%LLoGoS<-&$2ib6aPS0R4jc.=Z@3s93$`Hrm%#s`4;R&+S[D]9P4"o=D^B(7b@I]rhN-@$9aA.\]sI;XTt!atZQ0ZQ
%:.\T;:H5&irnoheVLD7R*@r?$:m@[[,V+j9b:P8&4i+6QB4cTV#!_EEg!1VMG%t4^WYL9Ko2YIdIi'jF/]'ROfs&#V^Xut[4bcs4
%#^m#+%)R4:Ja,s'k'RBs>\aGu@<k?dY\4Egq]\nY*tm(&IP]s.Vt7jW<UHl=JjWB0>,I_TVF1MJDg[TW!m428_J<4"`.<f?dgnY\
%<Hig$[ah0dSaFLSb@6)Ehqd(4B!nqg>ZYI_[spqeB8i=''7uoOl(VQ_D^<oB?\J4/X.]Wk$CbQCV@lkX6(h)lDP&"6_O@j`3Q@J1
%q]l5P05(-\2N64D:,[?pV5bOV/U1K4W25b,3Z1e#$A"^,d7Hb)63I8OADCH+fGCae^;m2rP3<QtEqXOXn?R9'V(3t,pI4Xo;a-5<
%<+b.R(<EtGST!#4T8][9Nud4h:QCH;r?*cbI_f<;i[o7UH;eYeB+,*o.EF@3#u7Tf=g_fYR;r'BruCQW($0>olU%G%RFniQ7"L\;
%4pHj2p_n([5FIDgceR-5MhJijft70lVDg<P^?4&2eC.PnG:]h*Q^:uH0^W%HA0)?D)XoVUJQk6D7&XDM_/Oqc)s_O&ibQJVjm2ll
%;kZe3e<_:]="tG=n]fSE_KQ\lMr2"qNtF-IWki'k,t-][,Nl9CSf$0UbkNm1kd:69q\cb2**XYdNPL$>)Ta$AjZMC=Pcn(OeBBBu
%6u4XPaJ!hl3=2'#mTa1tH/embjQ*nemECSBOa!\U>?.6K-[PML6gJQL/F+%j,6iml-[LSCpPU]!%1VSXdg_m`2'm(fmkhk$,B`NL
%GO]2SOJ!!%g`GsnTio>,MJ]=tFc4BSf[T>HXd5n\/q]Kcp0:.bF\S(GJ;k*g['dL!bS*sCUOS;H=N<)mc8BKq*ju%N+teU69,p#I
%Vs?b0UlM"M9K""m@JL&d1%3JLj?c_C\b!>0TtO*n3)BeB\UUs"`6UNnM[LJuo'%Zrr:'-"1qk@]OSp&kY%;+t?eig#;8jbnHn6'^
%EU$JAIgCP.'2sXD6KP<`75^pA')[HP@7#=gG5)eDI,_HPUU.#s0lUp`@<?8C!1!j]f"rg35gmP\EQZ4Br91k(E,]Kim,A<m_Y&k1
%eF1Jf+<29=BNqL'eJbFB?I"<4%3P6/h,dP`Iu'uH;7WKU:;GDJ4&X1pr:2c!G]69iX?5L&OnR&h?U]\u!YuOTIpQ`^Z@FuJ;RpEJ
%$^2@rWP1*%<$_9R"Zak`Q(Ht*CSual\muf*-tNi$V0D,Jm3U"-,ifL?i,.l\iT4>G`h[GJV%OVdhg1`%*n`Dg9e&,2c'S!L!9;_1
%]"\S5XLuW)X[re(PX"H^W9Nu\D*`<H3g)=H+?[JK7h_D,H5i%@_.q1Niu.&&pCF-&<BI@/5$+O0m]0OJ!9G^>NZVC'@].GkK4.du
%r+O/X'cb%2GbkOQW06d<TEPgPj.qo:$cFThi:aS]S5e!Ve-I>L<D`3l6u%Q'Q"O$r!jVMFRJl:kHNaXa6%KkO0flqfS)eb$/>CB;
%iaWh)So@T=p/'@>_%OEY@pM?lctBihSM&'<Wc3,t5m9o[XKk_eP9T@eL4k-Y!Ot8<G;5-@$C*0$:0E_(.MB4JK8IZ;7De%;e;.bV
%&+Up](`l^EjYaM7'][g^aE8+ZONF,/&Ju@%A=Uhd^EkqdW4qnO!baM`.fLtZ_0r!bY8&Be=]jY"954SoA8MXeC/q/T(@5:dKs,)o
%fAjm$P<.a2=/q-=/d'BY6\0A*Q6_'R,<5<$H<hqmE9!;;:;01@Mr)UfZ:'0L5mPRt2G`<O-*KO?&b#_g=9%s^rqOD2otULtJ,M%8
%s6G14j59j^J,K-<(Ou3e^]0)hkPtP9s7+.Eq/:c_L]@1Z5QC_ts8JABs763hrpP@:5Q9CZJ,7&IrnS[pn&l5koj@^fLS+TH(\m;\
%rln5's5>(!naCtrM`f>TpODRWs8;?YLVN]S^]3VlrKklZq,QK?rUsU-?iLM<DZ9H]d!u!))A_1Zra1Uks,2`Hn[JPX5Q/cDj?N\#
%f>%.!huCUoJSOJ"I,oca+8Hjem7+e1WWe5r"-p9n*"q"iqUVL>=8/h+!ulA2!Fum1P*&)W;58J6.MhM=GE$ulD2it.;2j3*E&$e.
%4(gi[\;S4_3nd/#`!\8GEhbmBmRHrSbT9B\fX%qi6O#nY3l`"%#bFkerDc9&-0V?M&Y1oB!e-TRk\P\[d\k>lcj2mqd^b,mhG#"9
%g,%AO*.@IS?un1<q7r7u-\sd>;6%Un&/Ktd6o%SQ^9nf@1=Y9;@cES'1]MHgbm=g$#01sEU,o+Y;H37>0"&bY'e-ai54j]EkKTYj
%JL!qZC5].")[r)X[qSj8LPp'E6ThN3i0gl8>7%=0T_&aP8+2MURo+3'L9<?lM6eoLaZ(M=0c6$leprQ-bIq&:;:n/S#1B(:q3r#^
%j`+3qET5#aUd>Ak*'T0:ZJu@HG"X,N^h9SuHO4%WbB,[W9B]XH=LZ0@V:eG9E-2\Bd2?Tbn@OKLZO#aU.\7P(B#p'8bt_I7#l;?L
%D)CT00,[#/mMlMK27Dt?<*TT4Nhj-VQj+e</d:65<-Oaa7,:]31W26nGX=CMrT@(cj>!^=OLZ&/(Vr.-4Mn6N4#Z&`I0F.=gX"'%
%c#8>C#CHiIGnGX+-K69:7+^E>"[Trqp)\-2</`K4[s#.$CV6iMj^_6^-qO4uO.'SW*/oY#D,Tt88O&YRkt4_Yj%bs]0It"*P%.J.
%#oKH#W2YDZ&[`j&MJY\3@-Hp$Nmhm(!utp0ACiFCK@GFLZLnORmP:@oh_'m?pP80o$dN:qeBhm.Wb;AS/gHZS+*oO_h7@K16Dgk6
%04_\4(XsAU!?r#2LuCAr<Ne8Ift(6>P`SMQqp_AD$@m/gKJkukC$)_:,>%5=`Vu\(i+OA:ImS0tjMSNA$VH5=]"lL&n4g`Hm`bt6
%H"!Y;P1H&@U1l$^]>GI`Y<$I\LFaU<,BHm/FKra)F^0qW>Qh;P;#jV3oUEre8i/<D_M0C754!=>'B<Z+/.1iZe*Xp^\?F#"%rkJ%
%>Nt+@/UL<1^_-ho%RK`LKJ3B-5A#!bKq\S?91k:ZFR&PHd=@`9E842a,!lVm1&cQpT-Gs0d01Sc/0BAoqbp<^9V.(H9B67kO?m']
%Wo3MrDd:*I[/Qg_<PFTpr:s)4laobg:]L0\q#:N4cemg9loj"u@+h/B<p90=rdcgu'*q<dVL3)Y#Sj\<+mdUNfhnD#8udrH$Rg78
%Tm0B2fM*.bfcXph,q<8qKr5!\\\YrcrKf*al?0D.!o3nQcbetHXB:R_3kDDI9df6[,X$:,O0Z*q"<$NSgXaFU-3I@G9n!6BeMSRb
%?&Dr6N?9fHXq^C9hMTH50o'qfOrqV'8cKc%Q.b,F#D?m#&!>0.WhQXAP[XrC\I<RHfb'Hg_qIS_iH`Lh<cs;H@F&J\]MQ:7Ftn4o
%]`Jc*DAj9<aq4e5CDtXO;K!P2(s,O4,LE@7,@PIp?h=aYbDGQe-pfR!38*!`a2h-)2QR+F4RV)FB6WNq1tU^8JClh:=W+6fd\Qj:
%\TZ6Y]"bs7JDU;Kgkebgl--!r%AL"'kQ\aL)nb6?G"rX^q/5^iU]SMI=(@9Pic/7IbK?Cem.,ndiSi"S^I;pTH+%W/"<6</;tDHe
%3FAGUI!JR^=$[3._BMKZ5kn:C]n(3!,>^VA'sO4&f2\_"=C##D@L$9AO(I$m[Hk1Q0VuHl/Y+R(+O,beI2hAmq(n28MBLkr?pt>u
%s0q[h\+<ego1FW:j`=X1!badT>'i&eXM24t=OqQj[mkk=OR-Ha(fM"qV1f"C1!hEN?"H`gr]J6[mXqCmNgV5+Y)DkL*c)&/ZVm^Z
%`9Z,l5EP$JcNU,n:"OoA6H',<]R,mA&pk3tU#&&8g*MUnrE.J2-6Ni4C7prG/opFlk^5_EpZ!q+OXg[d#\!JlGhaoLTJ(S`X(Gj7
%bkM=b&oej,nC_AeJY%TAoBXr;8ASs,>*a^clb()U)VBp3ku[J;<B<URbSe5WO'`"f5JP3:1s+k\9QVSgLt=SCJ)'e4IZ^=V[j</d
%TAhk9GK"4BWIl"NNl75n[Q\<ms7&QNNeS0)\ZgD0Tq`?J-:hHEnobj.W%IsUTp:/uA*hm^!41kRPgld;-5D=[-@'*,,N_aQ1HG<B
%!`?8Ma"2#"8-ca4dmkfXB[HAiC^)MN&q!&ol!&a2+TE?bp9nq;k`RABgF3_WZl[knYjJh9rSl9heS$Vt,0M%!Qh/^3RC^1BeqWee
%4GD(B<jIf`=IS%'J0@0%9X2$ojA(!r>R3nKO_fZ]%Tm(PB?/PBAOF&WP\XBb\.fa+\dnU@F=!3,?b6']V[?UQ"Uu+."cITH-!i6F
%PTW<PApG`$B>poNUr>;%dZa"3"Ikb%YkK/;f470@+F4GD8f$a52b5?^Qs*Y>eWAh32G%ElodsrS$1=a#E^k%%mCgIKAM(^q-"Sj9
%?_sJW2YANl+DegHe>\gNPP6$Nj@A;EKt&*181U92@k#.![M4T);f)iLZV>O^3.2P?XX^Eco:7,"Gr`10V2HU4krkd'.Q^NPlrdej
%4R[\SXr1Ebd$p-C+UbnCMNTtH69<8K&q#CW>4U&Anrg^-/^\a$,HnJ`E.Z]>'Y%>X#583Kb7mlWGiYElW/\4g1mD:hG&sg@60Pc-
%ZnpSYCtW+2Dj%p3)sM1,3m1FhC>#eE`?\dkmnC`?T^&KP!,1#Mq!JHX1Z\#R=aK@b0[h[&P'VBn'rig%#K\%6U#OMT(;Ub1Wf.HM
%H#Cr"J_TJ_Z%Ol?$FV"!F86H)TX)l"ZHB;$:3rKSQ"1c%;CY)N$Qt#(EIkYG:E?qd#*0C6=NH*$W*?2mnIWY$p,*=]hO5%sA6"`b
%hD-aRonRIW_Z"rUK7=8$!b%-Ua1Q0/qcm8+*<BFm8n\mfB$NA0ri0<TeqR\c,9,2o1j<Rupkd>!edI^Y<&XuF=Sg?tL="o:79T8T
%U<@A#33AZm"Pf*R1Mja)+8(raZ:0*,StA_f-^FtUV[%e4fc\<GL<$Jo_@R!)<6--I2b\V"0L3rX:/P\4F[="j<[PI,l=*/LRGa<2
%!!khG&OL6\i^*aJb>f?G?iJZRVtU6(@0re5h80XhB_]dhofh>S8=U)*6SX+JAG@0Q\j@e)E#c4`+dUY%C'V]ta34^s]!t4c]iXO%
%BdG!Da\\[qH#!C[@]^rg436opZlFF&8%^P&]++\:&dDm3Q3Rg$+eC23nLMlZm%S2o,b6o:b-)g_JCYn&mYek.46Z18#lB-V/SIbj
%JiKG0m\QZM7*.W]F5$@@d$0AQ7U5g3Qjq;t.0.-hjplpH]FPR<+3=\\["lRQ(adJ&M<_]ga.K+bQV-[i`FX[/Lik,h/VoVZ"WG=F
%ZQ?<SfdBp7?28E`'A\JM5467'du?jMh>=r9R@BHR`jXF"JHfW5^__7@%MFGf(@i72/t>-+>icr'*PMeXa0P81,o7F<12NM#Ge!QR
%hAS9fQ(t08G[A.uP,)h,h;u]beJF'-,$6-g`Fct$4<)B7e=kbS(a,!ke8CF7ku'2r8B"WC]S4sl)t>-QDhZ]=:30j_J`*n\64*)Q
%2&5#(UZrNj-$F(C"aJaJK!\rnEE.F-O9ns4%j>gg2WerrEP%r[hUb'qBng^ADugLW^W%K(9Z8skJj1aqIP.+CkWHJ`6nc4Z:7#u,
%%,ZL>C)m*3:kdIb%[hfo[,oV$>_#$m%g@>B^#eu;69=[30%jsD]e+CiR1>[#=>!%^#Me\MC0g*V0+GuQ\M/3Zl-E!FA50R<=Y\2t
%@^4+M?c(0[TV-,F.Y=2aN)MGYjl$V;>OX5D+bS\;g<i,bZ-i>FB]KQsM-uZa-mj:6`M4(O+g)7LgIYoqJ\`_c4Q.?</,s.n1=O"f
%[aS9bD)3O]:T57uh+WkuCTXEOG<o+$E2K@S>P/>2'VQ9VKL"qon`94AV`s\5h`Uk+i^%=S6m#PLQ's:V20*n!b/>*E<M+T]5CaqK
%W_uTq>lm=$/k_fhShVGr@<O!ooKFVGnr=W8n<M+1Q(j4\CWSP6qOkK_]rQa8jIH.`/TL?S9N:C!_"$f+;4X/SI.aspUa"6m-(.]f
%;rfO!X+b^[dh3Vd90*kMf#U?[FKQ4C4nIT/WKK:mZDg1rV(6WK4=UK=)>bUb^[a+N/K&Y=!;m3`blP3nmqJJ"Qj[n,o!9u7ERi6[
%CUD.h0-M;DHJrL;5"#if*4^m28p+p3<O-tn'rd,!O8Lg)+g!+k&L9kp3n^]Ag)1Q.O/i;Xm?@g.Csd>".iU*(0oNDD,&3%F^m1.P
%E<qGdf8H+Ka)'!LXRm#?Uc'6G'[q)ah@+eleWt-2fA`<i!1,?.Ckq'fNRJTSP4>QTSpZO!SbNf9g!=;-3r?L?mt&i&%u0cIn,F[!
%s5(`dOJ6F,JJ/1D%Um`2B,;MGM?GsJP>Xo"$0Z-Qa\"CqW-Hdcq?EUXC.Xq@A\89Najk4DQ^-,QD34re`QU\a1SgarF]_i[0Nf/k
%!+F./[+GkfW5lPjAI/sInK-VfB+`u!0u]cq'C^OP(n[+jW:L%\Vui'nnn(LsR%;W-jhq<R-8i=B[l<>VeW2[?_XX1:/`Ongk[,WV
%!a2`/I8sVH4,@jFL[f1XgBlJ2"p5*Vl*rOO@;-G1k!sV!H%0N4bh2Bl;C8:\U?%7@!0i$3gf+sl1Kr')?m%>;bFf<l=uTM3\cku!
%)>YXprN;kUeAWX5<p!s2:V/gbD.O=GWEHe85"7(DA-c'7Y3B>P-fT\P6./j0p`J-A1o9E[7IZbN2VhC6QS.'m89ff?>0`^fJLQ$[
%5F+]]C[_6LfP<mtmPY*[e-=1d:YgQsSicSA[ipgn#[>Z;kap`PmJR1g:'kBfFX-Me4po%dJT`BaUZ=%58`6FOj@Z8q),p'rhQQ^Z
%p$K2YQ5(*]O!W$!hB=S\8qY,aoG(6H^Xn,tVZIP[3Lk'8FC!q@YCR,m&pLM%i4TA'&Hk87e6qI>bSJ@0am:>j717X?NZm`[c,U%_
%,O"Zt'2&;o+XY5u,O(6X/;:,al#TX>WU?UG]o&m:/r$G=_F"a[P-]W4XN9+h/:;,<Nadkap\1fM2^-">WMJgKmYW-`-V/=FiCBP%
%Q8D(([[':K&7)<'Ra6W<!b2[sPT4CcE@=3&7oY5.8t:<D%p-;aH.JDicPfZC]cpZ1cjWt6.!`1U@rH!d`Iup:&H_s]hIt.sRBGn/
%AYur5dt;Z[ab><M@`)_io]>079]6o+)%(5:MaknZo#r?365Pp['SE=Z02W)&s1ZXda5X7N?VUJQ4oodN^KB]NCfsA!Np%#Uh_=h9
%BY>DQ8g$Q^<qTpt0YgF:dU0^UiW4'c)67%kKtFXU6+IC.m=U??;cusnG0L25$XCl9^4h0Yk_L_hO*>%]EUi3glDoDBJqFS^6CA?M
%MIT7F7cF^k[^6LoE:.FLLfmV9'M8`N>Qh$sTh47@dKnbkk>V5'o#atQd\Wh%mQp8-88efQd8SCnR<*](chS\`=-OP&M5Io31-F/\
%QA=H>V?bl3.*#gJ9]M9(9M:&S6+NF[kdhC-k;0,l-&>%:eTj^1A%s&I2C)tUos2bA")DZo62Mskn6MS$8VReH&C+Q$\_K,dL.793
%@.'Mf>XN?[f[F*_DiGhl".!.mfqC8FGC^2qi=S&<*pk4"AoYXg`U;E#\pV?`W3QE@Y/rJ\s#j'%Q?P.Y=S*5Aj`1ful+(9#\u5a_
%R[GT7eHE7)Q;G-P]$q&U#5Y[i*fq\TBf2(bS37aMbM2CV&oqI-=^B<])Dg0Lp^i*_B7I0<ek_X;Q3pf*(oUqI>#V-M\$3:+]H,-1
%0@ErJmRBTN+]YfD<FrpEhZc]amq]E^8V4TANFc]ea7/H[jt0X4U)IU0;b\n6(3Y3E@F)@ZRC8f&+sf[ZDY+U.hScES)NWU\,,^J6
%30L?%bF[[^:5$Csl5NF^Onn,PK3pTZZN\_RXr`RUm=/O^X\O;%ULR:N]Tha?$\!,oi-1>Bc0qurQ=s)L\hc80b@fB9rhIN*$jpul
%Ls]%0OJAJ;,&"'TJfkRF;@/)HX/t_$PZJ"6!`:kW7M,1LYhI,4((GJ4`i(+S[8qRjBg`l'#V$S.^=#n@%Y_P?"jT5Ui1T0%39K3U
%B`#k+T!a.$2Al\4EI.t??HX^U:p35u+<_1:\`NKLJ_W;m@#_bVYp/KJ?+Wo9A47lE7+o:l?3'CIpS3`r5S.WeEj%t%1f5BNdqSfC
%SRTO9b5=+,Vl%#oE2ndUGbrj)"rQ/A+rk)gSmq$_n,MHfqct5os,<;UEI6SpaC6jG=dmWsdE10Ij;F9ISXRi'/3edm7*k]&ATZ!7
%la&l?=RQ@sOR-sm<UdImH@Bq^@e>2p6(BX4@"^c3i-YA^efu$%"V[:AK^DD.ddlcU,iAs=00k8!GQ8jg<BmAF/2N3rX+.\O%f27c
%kM*KN(&):kg9&7+_&nJaA</:BDVHf9=*R/>Zc_Ws8>>M(Q:j7^YpBd-6U%ipe*nCB@I6DgOno%r*;O+Y@7)]""<,pG*mbhc2;f]*
%chhA.)A4<m$BBMV_!0$I&[bA7=c")'78"5c>'1a]@ln]hT"AlVI@T,\'2su6nL:Gc<c"ap`'5X@@2++B[G]lH4mld$;[P-^;,d?%
%nu[R&p"(pf.T1pSG(<ao@1Ja,K[c\!.Hq390M(^RB5dNW!N[10A:IqRXrf]O/9h_FKoP/;dGm*\&NtAqrQpVCk$ud%=s8uQ7\JpZ
%@M[?,#hWZ(iE]aKF2KY/$6T<PXW#8XigtOK0&[rKrtWr0(HiK"L8T+t0hSt!m;U'gLUbXgoNOEaQra4r<b'Tqhc1t#94OjM`lBmV
%HZ-7B<77&-KRFYVXEok%Wc+5#$Jjn2PrfB5]rKp[S,"pX!9X5t%ct#T--b6MYc_[FZ>8AZ:gI$IKNZr,i<$hCc*:Cia]#nRRYX2\
%>#"-35\_s?Hm7V%DMOgH`!TA^S*pV39#Y>&\S(im+,s<h-);OJSW5?`ZY'@_o[Zj;JL(OMEsIZ0)K;nn[+Tq&PmHM+a/D<d&lP#q
%M_G;oKS)PC8ml:i)<No'B"e5WQ0/EI&;mO>7%PMU'<.\ZU"O)]1ic5k;=6'D5n=ehP_'@?!5VL9d6c<q`5jO"n_u>Gdj!]T`r0?+
%:tR')\RX*=72?J5^&g_rlj;8!rS'j5W&/L;gDRiQOq=^Xg#("(O@;j@0"*NbQZ4_"kTE@5\q1""C^RAfs(<EXQ,EUeUDpV%755CP
%Bl*]LDIYlIqem)%5hG37FA]t8jdbO:-3>L_*9rCPa3k/YON%^A/2aASKhDK7DSac3p]X*42uT7B#"`Oi6[W,=T9_BGGiDru26`k#
%G;K3RYT@r%a-s(=36ol8O73%%3<?n8d-l/u<;>Rr1ZmkbL(:r2Rb.K2o,W<bN&M1[oC;N0JW_NN4Rr?srgupQ0tmWBbT)p9T25X"
%#tBschps8B'Fs^<fVZC-lO?gY/3mLU`-f*>"jf!nb"`k=846s69XaF^_2MSlkd_&eiME$O,e42`*n/c#&hF-!;,SfZ8:W!&!qhCi
%lCmGt%=h(\T;lpiY<]I%/gXCe,='r3U:FR[?r4`,=Gj,8'E(9f'?aG,Z_8eLS7+;ts(-n_`[t9J'dSVJ#.fMQ%_9-f?ZUS3DNbTc
%Z\RLDl+D]"3;js;?f<ug>*jj"@L`UU3jI1te3\1%#OFsZJjFD3XDq_@O;\[Xj0*h)?'tn,4RVAoOFfOQh'de6jk#8iD;J-UFK66(
%<Am'oW#D.=N>l7')4,NL-S&--p#N@ucd9kI[1@RPa?4foB-6AJga!`;6"a^_oaU%F(;#O[b,`++$^<>3V.PnjP"aA`MKs,\,"FUP
%Df+mWW85MtR&r[Al]<M-pPfT=e)&kHr=@iDpC\M[Dl.3K\d:nn/gbT=<6`F.'8*,L'3I-i[GSst[08Pd0ggl<5bdcN61Hf1p`dd9
%=W45Ff2iTa"]uR2$UZ(6g8X-A3B@%).&(\1UT3hAPh%XN=a[^L1[*i(P9-qS?_MKc<F)KP,<p(PoW9NU,6a(fTnVD\H9t>^\VD^O
%H`k;SEjlmJhT$-)nE7U%FbQT:?I:%`b4e[I&#fY6iZ+W(NG"8!=n3"THjLnWBM!7j)3RPS)7G5YTEJl;CV99#LfqgJPZiHiQ0M.]
%6fS6o2iIjRYRN)&Oe0p7SC6E`G][WD/(@Qm=-KtBrZ$n0n:Ap+6-X&g;<rP#_2`DtC!HgK*1:KkR)*oe4?7?>,_.^mA]1QOrc?(J
%.mY2[:LD!tZi,brA<ck#k(Tg[fe:AB?CiYZhK]=76R#:+n\00#i%T26%-//cnI7%#2Y^2l@.=ePAaic$KV/6AorA"68k&tsTRW5k
%&`p\*<Dl9g<TS`0?d(_L5J$mCF0&aKMhT^Ah`3Rn56fPChE^oAjp=-"<l#EjkHhf.o)uP>U4O!b"$26h8B"g=BLZL`ThR%9?T81a
%!B;l)jg)rUN-ke!]eS=E3'n0r*9reT_>>_4^6&\U.JId#$TYfs>(H('N"-4M<.d?98VnS]4h?T8!bfCg;Tq2@.N='[LQ<]KZ&09#
%'"U]^]B`^'rlTo)Y%:5k3^1!,!N.DVm8L+Wq>tb`s'6]4<<K<](k=TLeK$J9HVu%)N7+K\g3\(9_kjeZmKLn!I@;p-n,Ue*;b?.L
%_(&6`4MEP!eqbfk)8Y?El-IE=gW\8\[m$u\+I[Sr)@ea$p?cC4bo2XT'+f[/a2fd(<Z-V0\pW!s-2[HY\ss`/,/kAV4hT8[]_J>_
%i)Q,rDn*pd>+\Qg,(<8n]E.IT7$Y[!(Ln#DYM+'iXJ#KRUXp^E2inm?4CRGU6"7G?j6t*2o>H6L62+bMGskV/Mb']\'4<@<I;Og<
%QFXU';hqMc,H?!/880G*?qfE)M>Z9CS[:a%=me`us0P>^OY(?Xc`QY"O9MTs;'e[);lgIP2FC^ffEh?0,.MDn6+RH>Ia&d+4V.]r
%WOSi52M6WU@VE747;:5QF_EjNF8W1CT*/0WU6TM[83ajpaSpQYf=qn;Pp0?lH;MF%dAS#n3g*UG(,CUgj<A7t<nLd49`@S$bMCN[
%qAJ:Wqu.eBK-YpO9op6Fg$nP-l'W-'R(qp1l'sP1FFTdl2CjRu7"ST&@n=#M*SkFq%$!0pZHZt&ZU[M(K0K]C%"*/BEM,h?4([er
%#PH)AFL9Mu9E9=9hI>ZqFhUqMHi"Hu0DfB$06cW8:kJX%gu\](/L@5js,r3N>R,RR>C.95MI:LDIn2&WZ*KOU%m]RJSt6)7W!bht
%AKVif+[G.U-O4e,Za2ipab]_Z,sT>Jn`,rRAZ_#2'\(`NXY2<8]SC=aQPoN/145o*)6(l1LMRar!>69Fn+TL(^iHNI]RZ&ip`[FD
%H%A[C5fLbP##QkD!8Mr8Rln!#je7SKD!F*TPr3\':^$+'=t14_nRoHWYVND-m`Q(jpr;[=-2^*`U#:6aD.Gm>9-@h>,Ig1Z`[KU7
%^8&o,^DN-ccJg4)IejN9-%R*eQAhQHTW`ksLa.?#U0c/2[("1(jhri'jNt3s\^HG9E43C;3%VR_;fr6&Q]_oQZsEEBkFpJ5]glNu
%%me)dWq?$b/SW?b%WOXhc1[<nNdV23=(%mhjhHl"0\[9eZDIJcAl$h\%'m6'1&a;PcOoWJK]9f?Kh#63UI6RH8-]#$7hE#1E<Sql
%o9nF>35^.\Y.R@?.'$EnXN'Z+pYlQ8)LkB9<JmdDop`Ae45ZR$no%qc]L)DCEd)9b&k_o3LQ>e0g-<ISCe.j49erIYlR=4.^g9,%
%pJq2P%1"tr;?o?K)&4:-=*#:p9Q>.m'snbAl@nOn$cJJ$5V%0`Lhq%CSh>(r>leu)7JAL#Ag;Q=9Ti[c@5juJls4Q1M"n-Oe$cSL
%P(^8hG&4H6.MW`\84sU$%2N?.d^fO0Tgd!TBPKD/G9FT[7WKpT-0l%?V>H:B9n.C=*9HsdcXj^''R04mi4L[dGD]_lDm^+m),*iD
%[B9`%'!;tTG*<f86gim<B9teL+J3Y,TMHpC(ul@l\lWH+<1D!_%-!B@<W=%eZ=u*['!)i')E%DL\a#h%(!t9rkOT$BioI0jk`g8R
%%YC@"p4kfR^c+'P',BOTS;q@,kD)c_hNp'FJ;Tn<QU4WD`s[Br&GQg'-shqt/ou6>p"(tHCS`s/G?YAJmuph'Ghfa;-]NpECjM#u
%,W5kIR)Q4@B#ZPHV/*bRK>kp6[lIOp#@:nYo7fOuHG\ZD2^2N".N:B%a&Bj\7[RmZl("GgK]hV8`(d;Rd)&$_Qj=IQ;/s$[mYhZ^
%,d%QUNAI`fk'5\?Kh:9hMp4957jULRWspK*`A][YZ,R\OkM(2]CVc2T3lP'&5WfVhj].IRdK;p1HC1[iXU>>;r2gtfI"Xp5k:Oc/
%V"$9?]EDAYQtn;pDRAQG1&;i)nTP0(qo8&U<`kWSU6B6W/p1Bb!SWB3JlN9<gEW+%*:VXVC%Qr9U,MCPXNq'/\r8<dW$k2!X<-L%
%#D"!qcs^$Tp#RSJ"R0?%!GS1[\W`QsNnH#m*srf]^BC1*)#-?J_R).7@,Hpa>1Su-*Z-bY\GKeWoOgMCN)/G)3GK]Qj[7GG,2C2#
%GFJMu7-ZLgjaP9O=/\&$*5F>jVm==:V.q8op@Ujn7ZPKkY6Ne#'6&TB?eCei!:oc"9rK;;Va_OV^;Hs;f@RM7MDd6HjOK0]@-7r!
%G!]VAD9U;$l;\7<FGsC6O,\:D7\JT1CGDNGn$)E<;X?1+4\$<*<q4c6('-0=Ic6#5iB)3_9P0DDM$LnXYJ^6;7R.EKDH3\ug[\10
%Zt4V#/sM>>9n:22D;PkoCIPCEOEd#`.TEC;6%PAq7!&/Pj"#d<"=?MK#m^`-,_]*t8pu!/.%`oY<%7C9Qtakf59p1*ioc,EX]^k+
%BZ9i#Q8l1u2M00ePsaeOr:;s0`(8E("GT,e>T1H(6i/fSqO]Dsmb^m""!G7+k8g>4gt2@]*=>dk_==MU(<7LP2G)C!E's<OegXTT
%'dV"m.uiXSTP;08BT-MZ70U\:HbmQGI6n)$_P&8pD_bj:RP]).Y@2\aBTap2OQT0I&OH<P+t4G.>7?#,P#kc.?R[I5FpIAmL%N$"
%"`B?;K%e"k?T3(*OF`Zpb`/g^>JUW/X[k%g5q=M5lTNk-H$fb,AHc=p=#e_M$7g;X*i;U@bEUVOY@-[2nL19_.`/WOK+o!i'hIPb
%3P]O;8#<D1Z*LZQ_6g#dUO_6P*%S<gl)5'8\AA\=*)nTZn8F*^rp]o=5!bn67^:l]rc:E$'aKuqkBJpJU@="`NSE/G?e:8Rma$f8
%S%(Z%/S>&G_i/eTVr_sCVo9V%_2sO!BdV'u<BLnQU;rY+!s6m>FaJWNUr4B=-Sk"b'H0K:?0*eYWPH^I*7i]Y-V@*g2sFYsN9#q\
%Md/W'=mhq*oZrn9@9UBPWGG>NHQ7L[8&HdG-hLHUbd4s;3(aQ-<2>%tpF.X2+p=N:E!V<f^*9NG6M1/`EYIKRX2jEbUYr-rO^guI
%,P$[uY;>']iM][<:Md83GQ3T\9=7TaK,bbD[c8\i]R<apV0fETCe"<KL[3cNQ_^%s#nK<A;6(^2O'JtPOARcC@8A%`CGg^Ti_17&
%7"lt:m"/1Q(>ceG2k5*>o9Ka.8B>$HM,jAJMR_3/4R`ikNSB<>=2;)S=c^IX?CgD%Z/XNhE:)+:&(ui\d-f*q]:oCQg[?>f)@^&B
%k%r)L7?BW`:=9]DlhZtH7R*.-@tWACe>=]=84S$:Ro?(_6+UFfa6G-r*6sk"G]6rR=J5N<V+&q`A"G5Q:4%@%d;3dP3FuhbF?9C>
%\*tr`*]_E9%CWF6#K#M,KPE^?hYpa']YDIm=m$04]@?-53(VtqK.IHcpoLiB"X)7%j7UJKH-uZscV"c_7AP^`Q]R['pm=1RlEd2]
%l-G+7I/?@@k)7G[TYQmIlZb[WOD_8ZRTJq&fb(\]%)s`=$lF(lN'dHcj8ATn_/fGeB7)/(0?`rqcSGaX=QZ]rc#\Id;4a+E"YL8W
%C/=78mSnFcfu%a@8iHm+qAU8hpf+;__U5TE.Hgnd;TN?A;&$CIS>4KAFZ07K(7ca7(DKom=oL--"rM1IbX&Jik$R]@IZ5l!":iW+
%hO<0nZ&%4LXk8s:jgHCN9>=->R?fR4j/4F,]mFBq+K6X/@-o'9rmqP@pKls4G)`T6kS_%5PA'e+;$.,)NR<frpSD\^>ppG*n<GB?
%&9*H?,tfd:UtIH'jYkIb_lt^-=V*&D*;1mj-/J=>oW_!^N:FN%=@KWZbe$MkP4j=^QSb*Y,G'\f$'-jeC!OsD[E6tkj/$i&7EfDN
%-la.-]e`:_787PXPQ66ErLMpMcu)MEO@5)dF:!l$s$D#q/#d>j;oU1jnB`AI;Cb._W6t`EBDJfq)lF%m$;pcf!pnOKJ:4Rt+=.F(
%Z-cLV7k1jT",ce&=L72tojsHU&T;;65D/5[]WCMR&M/sI52ZgEC`VI5>!Q18?LgGcBhLr)U@(B1iRoU<q[e!*/ZA68QnTp9G7h-C
%V`0Str83.I%XbsZ=X4dS_MO/VOc/V"1f1e'L1hh`q",jE\,#9"N'Z%`EEmHBUqpnjoBjT`,78_=9Me_-ID"UqArW!$.5<9[4MV&O
%Vi5mn"a-9NkUT&p#*WN9d1U$*CX_r&eH*lpcRFH3mRK$L-ToE?R*ooCpOJFAWm.m1?+oAmXf;tm*S0]*DOrko29iLqcPq'O;$SYu
%&l8;B3qAghan8E>ic#l8!W*\P_&1uLZYGq1cp=jX7*a@=K0IYJ!*K,;T525M9W\q@X_<_nmu8hJk+\*%)@k#7=q6pkgqLX+g1+2W
%9cFp<82V2iP<PA_.#&sa_mqlBa]@60U``?p5u#6O8G]\M\A&W?.P!B.ZbTH0-(M)+?bo)^&QEV7MYR=I]"<-6s1P7=-r$c=48_TZ
%>n'joV="TrlO!"TlN,X_5%>@QU"`^m[keFL0<THW[[)"uW(>W,mm/#snNF5F]%->6T"G1noY?tg2[S6OLT/XAh,kG48j64d.O@dT
%=NcsI+pd]7^8T"'4ms-;a;7)C4FI;GN6mV;Yl^AZ`)q97*KN["V_%d!3Ku::qM*j&O[@G&7f`cB+pltZ6s<d%69&?L974c);nQ,f
%W8Dj;Q7[^`U,GBW:.RS?$g.\J`7WC?o1+n-E=A`qqm&]F(!fMeg'kr4g_r<c0hc9qjgVQ_&T5][\(+:iTVbD0k7?LOh>21*>\lYI
%l(`0Scc<+4q,Dg61D>u3@hpF=!RdG*^;;X`'FVg<6PT75Sa(23EDcq:\=_]+GD/`[EcVK%+ip0)5?mEn>ne9(-@"MkV$ot=*=&kg
%0k%SsNkO<<Pu8)SE(r7L$%[[<]p4I@]_,9SZP*86p'8mP(_-Mm+Sb^$5/4@X'<5O+?c#XHKgY@(9$"E>T(,6U>tP6(lM//Tl6t88
%<RF?U0h<8J8.Qs;>s`._+E_EE<C(Dn7`Kbq^;Pf(YSD\^m.F[V"%k*SB5ZR.Dt;5;n/*U):N4AXb?*=WRPJbXW.+)`G]C<]]JN(O
%V!^teZWBFJ27K5K#@uZZ,#0^X`eRsrQ-q;:5?7psk"+:))65Xf3<s-<I2=.QI\FB:olhSKh0j'#W^6DY2jBUX@2>r*pRTHgFJMeG
%8dSCj+0=^M"^0a^)Y_#`#jPf^GB(OH3N"=YKP."i@J'f&ESq4XEXVp$/Fcs<jVb-T^d'U'Ia:Nt!j@QY)a6[)/L3A'g43S4r7(,`
%6S)r<k81^H3PIo,5]Dm1JtQ\Zpaqarnsf:_'f_Zji2qX&X`'0uFa,+r6TeMlnJ@UkoU`L/^eANZ!sWsA\ZTe=E+/fGR))^2#fspb
%mkttefq^n)>WdGVAO.F;*%SG:O9ZC3#[&m^Por''=+W&V_D;;<-KR;0Dr*nKeg@2FiYWBH1IFZ#=^8!lC6N:Ha<^s-OYnc8duILi
%r1j>8WN\c.:[+ei-!,ErV5a?]V<<1i&!<B3>`I^sr?^H-XqWu7gOc3B)Gu3T\jI4eoo\K.N4p&npoO57'8:E/I<8U<(bQ6RDC%H'
%FljET1BCI-1FH^O)KQIL'VCt@]6/.g`9L.g$r!@:U?^1_"=S_?;A(1?5tV,4:OL+s.;-H-31[m]J;T9s3OZl#6gZrEfV*Cmdo,<`
%3OAIuks@L2-&;e!38@_9l?YUWT]&IN!lhj;)$uB#3TAi>.*afPBS;Tk=]<EMAIL>+Jp%-?9pT?..pD;JhT50MnLQf8\8u
%.Pc8Q1#OGdC)mrfi6.!J*`C"gB#lEG6YMg+>-9B)isKS?TpPEW#A7:m#g]^M*f/7`4ZmA9d!FTVX.[JoODo?IGrgR2'ao!h6uQ9[
%/*NghlBsNo)lnbdFnV+H\]Ao)AQ8u1cR(Q.)FH63U:@Y^b06P3,SW^jW#M'E+)['7X.k?]N.Wfe`ng(u,b;^a`L_&[cQ)8>K4.o@
%BgFs@9\Ar_BpL*11M=0?ba^;j$JZX7Pr)amP0qS\*TCnl0&g`F@1Bq%2_e9e6P8o-a]"ndD-[Q^@>f`j824\%=EF).0lN%%3.8Y4
%L#p`lQ?Te'`tdfpm<GcOT.=9<h["]2F93AAad;_<_)Y["&/J,%'(Oi&<ZFD>]h;A*0,1fmXpa]bIT"aL'K8?7iVcYU.$5s<61E#e
%P\PDD^2?n=9dR(WD,O32Zu\UGLAH#aH(e/q6rtYh7['1$>Z0KjC:=@A:mmA\=G>a2'oGmAHPjdhEi(KTPK0H.[DEO::Pe%MZ'rg]
%JlWRhE2SdqM7sbp*D:]nW,*EL'E2k8]O'V`?9eulesgqIimP"4jA6j]<&ID]4WAi7JqkbJr\Z;>jti*M4$fDPXHBHDDG?]8?$+0N
%T_/":*e@>lMOTV89fFL1di!@`P`u+_YY*pY)\qmIC^deNDn)a]A%/u<CP=1Z]`KDIhS\=rWLT4BiB%kPk/b#45q9/`p7.@2^18Y#
%7KK%%;qfL=%R`#d"%P($C6'('AIDZ)SXI^a\YC8L&6,nKPI\]_L)k!l"OpL]lNPI&gU]YJ`ZBp1(cCZIN05XB8)DWu!X;7a6>>Ej
%O@pc4ld=i5YCLCq4.LOjNW<MF=544TmmT:SI>s&]G!>gsG)Up.on%egMHB>Vcj,Ytn6MG`+D@+YV4:%_&ofA.?<;2r=ZKo#)6+8f
%FtU_]PSYHcUudb>_a)VV;?g>h`/XXDi#."B-PjCFZ(m66hKUsKs$)h>J*jYYZ&D%ADK`)>OE(7m`2C8YH(Gb)E3CA.NBHI_]@dql
%&"/gKHg_\3<M"VO.,PIO/E7)T;@K$UE=rf9W;oX!!]!TFh,?s[.GCf2BB>Gq]Ita.I>obU^<cT@Tkn*UE!1%>``o\Dce]cD548+W
%'H`AN4Qa6'10Lb%%%a!bONJ+0U3]Gqq<8I5TI?rW9A%'6I@]:S5a/ou;<h/iQ*1q(4b?\Bd4B(/$Rg-!Ma's/7SGg".qgeCrj'Qp
%\?B/sUgo+r/Ufb(L`rOQl*o^:4&9g_HqRqE1Y[n0+:id1(gC,Y0hY<8jKVc5UR#S,+,[PO7k9uoOKfnYB*Y#$?a'jNj/hcUs,tNb
%Dbf/@jZMl6K2d>Dhs]>`m(h9]A<>mH]YZ.q7$iAo"KW=V`LPO4C(dOhLmIu6pTbE!3!J\rn<OZnIQrG4V!JldrL,*t2Km%%c6)fY
%nT][DD?4#5pBhd-+$<]ikN\2A&7n&0b]jOhPjm$F;+W`]XAqJKcoA`sViNsr[-`XL&r1n]@LSQha)5XIheu:b!.3;OU<96IN>`mP
%I^k.^>NP(TN<b';q8^uANH#lSBnPRAoq*)9h`:N=AO3N?YQ@*jq3Qn1^90=>V^f6DFo:!/[fY(JV.\.#FI;1cH_e5N<DWQ;40$F8
%NaUITJ<e&i(65@W<<q4`0osmPH\oQbrIl$84:Gu*5Z=>O.r/Y"hER%BLLArh8YXk@K-gLts8U_uf-X.J,?seh*]*bM4m/2m292S`
%GXocpFNs"R+jPW%9;BO40nr\0D4lVh&$)^"TF^e+hfN5nn]8qS/tcFC,pXXKYFRa1hVmFSj\F8.!;BZK(]-$hRCr"h_srelA;lbB
%][m>mOA\t%*lW@fd/oi/jF*6$g(?lZa^ENQjaP)9,hF>eOYATfQ&YVoe5$Kb8MWYJBau^=Yd>B%<_No8qsRrbZ]`t`_b>n=;ad!u
%4Ds5f";l+r:WPmiTOkPH=$q97-EVo?g:IK#D&h?H!#D,eYg[O7dPq+M$skQBbe2r03.S=U6?l/E2^PJD6KEV<T]cUtXd0Ml<A'E%
%a;MghkXl,>;+faP?3QP*X&Qec`][WElT1rqJ[!dNl#=^HRH#m@bS3sA$4==4$"n;r5`5VJZDVuQ8WgYK8[24"S_[lJ*$rXMBmHJ\
%!3g;o#*N*6>2,!+:8bB63mg=6_9bPL(<W,>C+F1l,Zb=J=dLSr?YG:IgnW$YBl0tTMV;IhIOJ=*J%H;50PE.8r2fa6JJ:j;4fa=7
%*5%N_8DP+93qP.?'p(bD-OFgoE;(PCe-?PU2R?Gc`'4_La'a$`!a8,V1f;(i^eagR9P]Omr.Z;IPiI%H?qg9is%c6EAIqt`3)$<)
%EDupJhGt2P?j)q@&AU7]F_,U$9":!ZUA8@3jAV:i3]hn-`_4I.@tKo:HV$eITc=%Z8H]<XW2Sb#KZEq9:t[Vj1q8flAP\NJ,5H1Y
%D&_SJlu".G(,WfO/@X3S/'K8kJqZdC#0&b,@HSu!UZuRp67>WTe"`mZF%cdF4L-&"hk7G@Z8U::`J'WJ&G0+;";=V;'Wjk!%6m2#
%U>qA"K^<bY'%)\U7G.ra,qh7tDce6G`%)It(C)ri.CH2sX0<]*`4AguMN(J:,'JT2H:^KH04l5l)QT4DiPi=WCC<;BQXjS!2GPh<
%_@8gSR=D68NN=^'ZVuLSX%^BM+=Z?5i*NJ"2i_B*'fe/n'(00.ARcCZ`mDUuBV5^D8LgcDC8oO44e*GR0fupi=b\43XnQRPo5&g8
%^1eJ6RH6SIT7P==5qj3(2Q5+<E(_G&<)kI/P70l.RPGbh;IE%EZ(;DCq"uK=CX9gZ9GC8`OYas(OF]I;?Hg4uY]!?KDj%U917i%X
%=l0GH_9gWmGQ`[#'UbQj*(&.ajX*P2qVM"M"J.=.jsfpr*)Zu]:3)P@<^BL7_'$]Z*,>TH?tp"nBaMuj,G8lrRS0<nHSI]uI1rGh
%3%43h<`n?Gfg6i>j-'b^aEd+hjBMDCg:O#+,3N&5Efja0BX.ZqVrAfENi:HeV<4$(TPd<$e@f!ImtphfBa0GCD6r/X_,*U2>6uJV
%F,Cp7'h1Y*9SZ5?iH6bl(>%S)FaOGGl*Q0;;s]KN1gZ%7.+8c^JXf`qJjG';c6=GP4ArI?GK2PVMj>?aU]cPo<g,>u*pgTTUHAjp
%90aOW#+;n:TTIH$CfIfm!r=.!mO0ij;'^Z`PAUAR^r3EsQ*Q*Qgl,'h"CtPEN1JZ.N"Rcqaa;A;+s&hshfGME_;SH:)AeE&DBB%i
%Z0^6%8q:ItMVdQi3(J4f-Y70b9"-#KQ\8bB^kuS06XpEIQno@_Gc\..<43DHO\s8S!*(_**=iOIK0bkk4,0fR@jdZRP%K[YP!ad+
%9]Z1Q9+]OPkSfp!BkN+?KlT?_-7jZMqMn3<-nH7]3lM_J8Hcm`!h_B9UBlo>k&0%X+I^T*Gf(>2dA61Do;q5N<g)aJanf-o=c,\O
%ENU%Eg_<QB=VjCOd8Z\-amg`&;,O?K&nb[t^kB8N;+q/ARZU+8P-I#BW#"H*8Otqc,V+o4"[W?<0IHnS:[5G*!-TD/R#CWGT+o5,
%A'd4>bLDf=MW,nk*u%6;3H^@G]pfuZN2HRd]=[)k(<A$rr_AaJMPMKda,r`>b[WQ"-7Mu_.EFWOUVhD3-TX@%MEo%4Q@Xc'3_fa1
%A$I@``V?Gtm0_aQk<e%@hY7b@W;E6g?m[u^1%UpLf*^IU$X!I`.%A=R-O%Jt_sDJ&+I\ffE1HtFV*)pj8PnbbTcZtf_olF^kN"X_
%-#`@->E?7)nFqP[%o2?FW4N)F.b#@jA"']3,,Z0,*#!sZ+f^/\:jl1o(qo#2G>\V6&V,-pW<6Q@#,iQko&2?V,':%W4ui?`9u*Ko
%=Aq-bB#*e,jFu+jJNKq]/Y\?@ER.+X%jQIc5e3RbZ1V3H*!%rkr>1^$;(0,6q/6UlARPZL*<d="m=IoPljRs@3eid&Cj]7lbm[@i
%8XaG@6o[EP]IWZsWFD&hLQ:ut%:='JXqQsX,UG49S12a_D&Keq1IVh\W5Yo%?mt&K,kZ3K/a/'TI,KL>TdbB1EPWEADS0?P)TuD9
%\P>lO5WDZOJc,lPI%9.Y.jYgAoEYh*?H'1%a'&I@1B9)q@?--g&BkCfE)J06i(eZd/2FL(opsUQdIH>P9/fPpo7nQaZZ]j#>(A;9
%YS1-iIZ/4I1Qn&tp*1%\`bV3%'RbD<-lCX:RRV>GHib/D,$!"BG6YA`HZlfMEA"o_Z[9DRC18e>-_TeI=iDQ87-8Xn\HVL-WN\L9
%,+L&,7/%]b]/O.?,1_^g3BNVd+=TAV=B**ZU?!0-JLAoV`n(:1EeXF3\s"k`Kg),5_2`rg)"c_g+j^t'=X'ISlt']27!:[>=($X]
%K>je!%1<IUFA-^bi42pik#C@'+;J;en\OPaUh&jHU%!/0Zk[o=Hc<f\N1l=c(ETi@nTi#b*)Saf`!JqE::d&EMBFc6;No)?P]<'E
%'G(>;2![/SPuaMZ-'4+:K0!Gk,>dB]#b3$9-KZcc!'AnA9"X@6!7a=ZlV\0BTRFs5n$b1@)GV5WY079,K5--!AreH*59)!)Plg7T
%'Rs0?5>*;q-qXq<GSHlmU*ck(*RWOIVP7Ip^mI/$%qCL*f,:Wc$>BPF!5gO].Vn=31AA;:;R9JED'WkW4:)jd?'VBLpiaLD]#JhS
%+Ago-/nm9NFIDl:p8opWJs25UeCR"'pitH)Q8"5U)Vo2&)HkC_2goi$$rn^rNDnoEE"l5]#qle*iMK9ko%OpPL>XA2ImVj7#2+.b
%M3`[RN+kW*+^mglZ_e%k6$TQ:HS'rX]BWnBGSJfRNK;3bRj"[>Ss1XhVn%=Xf0d'I,nNSp6#g>),$\dc.A8p%-#tTCfCk(T4b6Xp
%)Xa,_V.;L;4VYV60-r<Y#^Wr_?Hj-@ZVlO!i_u-O-?rSnMsMBMR!@"5M""/7N`5Z^>?;*h-L9D!p'Dd6iR(p@n/FQ$4X]NR43geD
%.G<$ML$>T>4Q%tb+DKL0_U!tP8BepNoeK).Tk%.7'\le11`H!`Kecs0jSeb-V79IHJ]`5sq8sOoQNBf&<$R^r=m06EgWJ8@_5oJC
%(`0f?B6Im$PGfT1j(oen.)DC\!$@-WS3R&s#?<IF5[qkYGm#kCJKqQ*2FeoNm"EP-^^MnB>ACE$!1[,Kn*?&P%r`n[$Bl8/K9F6G
%H\'tl)!(+fh8lKGE5Z+)5A"O3N(&MamD&_d?,q(1a>X_knmXCgV-[R[F`_;P#FO'4#i&""ab5-,-AY0]-+'3*'"NpnG@8VS=dP\$
%=`^TQ4I1ePcT,apJi),f.E5[;(eIZ4@IOt4>b%Fs"tn#FSD9o]N&/;n`66s^AK!km'THGR`kfF*S_$%MR0"$B1ct$BpP/,`2AIPT
%QZuQ3rfc$NTp9W`i4V\"-4"`fE]aEaAVUnuM]cSjT8Wus&lm;6MGNVnTZaVTh9'WT$t@?l.(h\gJX5T9Pf4uT/:f*)J.=;['_5uq
%/97?HlM3s'+$!(C9d&U&-HA);D[F]VRJ@kSKdsSF>Th@UpugUq`h4'.RGbFPXT@08NjXNH`0Q'd@q<r%_^':/KC"K8+GGBU'#;sO
%Tq`.+BdQL-MM29\G7J,ZAkAcSoi>D=iKglq3NO*CL2Rh:)H8AGDAFf"/3V9M[HkV`g(18:OdQ=N<GhsQU<sqgK#KQh"i+W+HQCmU
%1el`3ER9L2guWo-4HMF9.Ia-sf*:U^_DuH]VCF_'#Y[:H-8i$Wfpc`(.-'/aI&[])SU24fEj`(,oSp]q8Kb>p0Zu:r$M$1;'O]/9
%&@!'#JJu.@!JDC?AIW:5:BFbGG/?BX$($UU,r(&&a95*l!pAan!gre_(*KC3flbI%_iUp'c:J3L9<OKrKOjW5=p!o_(l(=!SV%)G
%W,1)d_$XW`CK!k;mPA5A?4\M(&7qXNVb#0JBF/e@H_MMe3rrE-6D?N<=Oj:jI@>pX1%4DU-.ju6!iIW7.TDQX![WYUa3k+Xn^\-M
%5-0>\L>+Z0';I:*)meKtTAPAd%"ssuZY#^VNdD`LkLU]%QETY^07jkeEi^se+1-[sH<O2&aK-'cI"qdeZ.!@U2]!$907:XudRo$>
%R7RWm/HFL5cB12&:eY<l!2`KsV@2!/Q.CL"Db7:fc]Q&C&mfN^#s]Eo@kC!FTGb545-+ptb./[k0R4'h+lUd=I=8'170-9#e1tBG
%aO4UQ+ec0T0qO[&68L$QAhjYb!p_ZH"N*a<84?Gbi086>dXZNSK0cIQMQ71RjsZf-jM4nkLl`P]Dokj:YR7&P!YWARGq+NIotk0q
%^#;/q4M,H,%0:<AB8XTQ`V9?UU^IdW?YB_85Xc8hP>uY3egN5Mhud$4kY>N)=`_7`T*B99Am<0\ZD8rp]Z=2Rg*n6M'K?)d!4H)G
%!u9d7/\nZj5WG\n3K5VgfjQR#rZs7NLf0V'NjT<%[4eoU64>(80Mfr&1trsl!JbC[c\nc.+SJX]2G3aPOCp"4kN$d?&7m)cF#p$,
%@bFN)2(YFOk[_=c@@r'=6!!b#f+L^,@43JRf;Z2n$FL6$/9o&C"!V3H`>@[9Sp/n_^@]@t/nA&urJjH?TSI-GSX@3a:Xg$)b*/[5
%N"aJL$8\7]JJ^,[,>/o*\<&CV^mCW(g0"ekE?2Qd-\u/W#H]uFb4ntVOqQIt&)9[oUR!&#6O_OT-]g2>W50q<i`boc!qbGSRZWmj
%I0DYE\[\$b^#hJ:Q$/BSc_0)>0L4i6=1bPuq!\sY*`jO+^qT"tQm0jGlZ5""3g:bCB2GuUN4[m($Dt1-_CukanEEZ3GM'5]0r\0T
%2cJZYLmW18-W6NsCW/R`-Eg[%"YPf<k#c,GZDR.:>S8h+o5i/=WC3GMCL1>4E[?2[MEY,c.J]f/DJ5c4G"KN;[=EqCGZ.#7<@o@e
%o%Brel3%m_1jp_C<0jJcOVsT#B%DXj$r<>O2:f"R0X3Br@Bi-SL;55>Omn#iP9g)RgLcJRB"5Se*[LrVX,C5aeGD[;%E03&9(9]t
%D%kK\.3XtUF>)Y0pgGV&#)GDQl!5T:B#UPh=bM^5KmCY.Pi+h`(*o:gHD+#<!>6>1V(.PSp4/sd1`"0G_,$NXFH2U8Q!fd7_.aWH
%ngX.ZF'=@<4FP[[*q9h__A>fjbK71<?9-Q+YCpE63pLJTp5m!&!T/oM8H;k/W?JZAW6NFt_52(<(3.bZarr2%@PI.`950Wc;H#9s
%W%F0j3?e>-JZRX8Y_\8*BHi?b<3ls82hA)7'#[Cao2HNUJ6j*9a<oU/X609\Y5VfIgt\2tc-=D7Ou5kPW2VN#Um@FG%Mph_p>J:5
%_4X`Q0g7@R[254?:40gNZrb_o#%a6<oTeo[;^Wa*=Z7qXFpKpF#t0-'\e^b`%:tCU,A&I038r)$K0E)`"'o,T*r2f:g/)<.A#Obf
%/o(/BjlOLYhiX\$(X,QA6-VmbODpj.^_%R%,BP^=0X_;!h*H^<P/Rm'M+RS!_I5rR>9@Ib-)Ps%i34QboMnZ\S&rfM_29OIrb#5g
%DVn8G3t=q?Z;XEY,GuF;j9V`1dE-$ZY8+]#:QiE?.=hKjS579D&+3PiRA$sWo$GMHMeo66UaDJ7q/`R)U(35]0,DhJE$A`A7`h,8
%=DjDK#@.,D*/hLc%#V=0;,G15]<154gW2`-E+'L>7)V65J#pqY6mP_0Ph=XuF.RI[)4=?#'Z.g<9hL/ac5A2cku=e2`9Jbu&M;6m
%%[-^Le%6A2;B3%1eD.[5P1\qCXt*H?2,8uLSe*$9_6ZTR=V<*Y?jUrmW"'B3>_-IV5g;hXd>e<+Md$d=>BZfWX^PU/4:#SiG2\l@
%-6&d2Y[/:Xl&(m^jFY?n.3ZM#^jHodqE&u]6(<\A>28%m%h=V5QKTs%dlrI4,&30Xae;4"0?j,bTN0;M,H7dlE]#PqJ];B/?8C"H
%WN_Yg3e:<Ugu^ZU6He:dZe3bId@A30CEjh^p>]&G=3sU`aDErf.VPt3`^uA::cot#JW`t/TRCNVcUT,P.XGCG&n7LVAQg)u_`%gt
%`3@8(Gab9N($lPYDGQ'E+:`@r2p4$4RV&"K)_9Up*Gi5JG=8%"M)K8W_DfD*'.JM.Sg5e7!p;Gc?O7UHW<sh$9PS+&2@p=o"F;kZ
%,7uTG,UHt@Y;;QmI4/N5#8p\tXF[Ot`>#i>0e67c$A4;,,!J#J#21S9q31ut2.n_i'fiNJ$.0bM,<4H9PXG,+Tp0lY>c-ed;16Z@
%#7#8VAOXm?^@+$Aj`mDHa?asMm"Y[jW9L6O5AsNjAQfdaL3b*XRk;&]-aDG_38pS_4/\dZYTA%T/G%p]j#QgVWoRHMTu&kG2%%pC
%Cu,k3"k:G$P"l,dJtCU"GLGN([AXL7`uX.QjqN)G5h,E;lDQmt)bgerO[(\GRLfk*hEFMP-_uMA0Of*r4t*'d-*Z;CQD&"Ree4-!
%nh<+eYUSe@==i@@2pTd:8-Np0idsQ!Ck%L6&HB\>Zum<D*&khu:m[i3XuEG3jsYS\foa7S,X)[#<TZP>;93-+,V[cL<)@L1i(sfc
%Q;7SYSV'1$R@Q=p$Afs\4;b())si'=X_\V_JpFglN+PCU+fuBO2\DtVFtR"U+*da*C-:;8=o.2@#[Q'DDH&H2(N'"r6H`Cq>Tm6p
%Bmdb:Aq[%&5'TW#O1gE[H^kh6W9O@L0PRe3WZnJf@tqH-:4\B9=pi9:&6`o_\0hGj:nA?VO,efJ;/@qMM/_]E?.%?DdcUD^N_L$E
%1IjB_&4@BgCa/44#VZSjDD&"Hea9N$fOGa/L_>lANVsQZ9uM\WNUD[o2jhFM*6pnC("s-")MgM49p_hA&rbj2J2Ear^lEBn%H5:.
%P400k]#Fl.LIH&(;hiusW+GT!:78C#dqSrU,V)GY'&&HsAl0uN;\Y[bM5ZVJFdJf'KQ*MqqJmEJ89V?Gf/.FI!sf,T!r8QBDA/2N
%S4>n?0-jjY*AW3408:)J-eT`+;o;056*a'[Kpg0u+KTbib'D,E,O$4V.%1^#,`%D.THsd7*W%SQK$Hq'`t=I,PPiA44-e+[Lo#$E
%XgcjHLN`n?*]?Gq//+/,n[.9MIjos:2GoiM,V9hWOH>)p)B2ciBfZjl1;I-fT[T+Xr]nGTIAI4U58TeH$^3*.UChBh18-'MCJfF=
%?hm(7-c[*1\^*:fKrk<10O<;,EHNg9OW=dPA>esn]Lk370%P:LMhCCP:6DIf*O^V,_E%]F]bmu+'0m.eM#Y`N#^Y2TKTN?e=QCW<
%\RRj'hQ"NWa`3ULCj$S\>Nl/_:H=Qk'\P]&RH'rkTNDgT:a_=^EgBigW2'ot".)EG1gq[,2jGd=M%`T(>saD\?C'**>VTsJ-Is2C
%/OBAu4>_!fREWej^=EiTq;04<KC$QeHAg(+L!U^th$n!9Z3p(5e@\2>;/-)C",CmH)0,?t;#L]jdU[u3-<D4W608SnXLd._OqMHg
%kq'73TNRhh0+m0[)^Ltn)W/;b#kq-M8P9C>;O$b/5q.4+*L!.X+r(hkNcD'@=K.:hd#`9o0$3K7T>U9=A6/k'-bh^u(tqP,Vb^nX
%7Dc@-#:Ja`JFnd5Vo"fn?<^R/(DCOa=O*[!JK::JpRJ%Did<Q?;<Ol=2D7h>d]:DYOtOJIHOXD7l0o$!:lG=e0Gb8=jMWN.)!3a(
%7(dPV)s0VC-`:L66s\s\faGg[KTZt82CASSo?N-F1eDE*cFfg3$:,n-8`"uU&Ckt3RaH)S^'TNfGETelX.@W@8I`b7<5)J18$Gg`
%F5rY3+!hIi83kI*MQ6e?('Qn=b;<6o.Y5P]HXVRj8kK@M*U!gkq[].,JHHIG:kj*.m&*7.3i\sT@Cq$?"M4k;EGQQV/@Ha+8-,]G
%\X_tc$\5((XPh'J)NPQ+GVP0cWA&l@/q7[@2dIXRe="r^*-es72XZ^6f1QWna?B+B7pB>-ZR1Au7`up[CD)gDXS;ob:K!Q:8I:kH
%i;agq3%I+9@-R?#'4T<_?mC_!B7LEa`RGMK3Mi(*'9pVo=p,GcrQ7_VK_D3/cquA<@!nC-0kgsR,9EZNg55/[8tB)5fl2us9UU64
%P'BA<Hc+K<"YEVdnj"PRcNMLmClM0mnK8`_LQ`uD[S%]2<LD+nLkQ9r6lLls%O<bp"N5=O8X?s\2/s;m9h4b]X%C9DO\M8N4QWC7
%<PT5B\UWmMN>=25p#'kmq\$kcn6-ri3LejE<hV#9eQ*::?GXQRn3':ABAS:m2BgMlZQ>&!7PdVL@7YO;#F544c\mru69%&h1>YQV
%"qTi6*\pZ/ec=E]dCGd',&3,Cc6A%NG")0"$/#>"%%2,O,Rn62?WVn=).knJ_*R=GOACU5[Gfh42V&f7B%V;D3;Lt+mER]]r9'iq
%'l:j"jb!dj*E#WgS.TY#XIZZWGq99-o"cF%"c^F[-eVj][#d*nNjSl1`a-G[6AcM()uP&Qa'g-H.cTsm:p%98eq\,8?<ddiX+Qkh
%kCRQ9LtREPdh>VQJre\_kZO(ANEAmL15MN5f!QAPE57d2%8*e_kkICm(J7.'Q:/#!K?B3D2eR)<Vu@YY[0NW!@5;XQ(=SKsaDjHW
%N9KZET"I-sJE%L03^;<>KQ4_]aNXBLgd76Ho<Rpb/F+ZqO??0fc:#sc`pYZ]c1F8(-TT>&/k-K5aK8!,;FJ!j@^Wn:*+X"sLT]2g
%X<q?@$l1N1^VbA+&`?A(NeBFrL.[+h3-/ahSn%qBPkL9J:jg*pB'CYa.Snuu^<_NbiQqTein4`8]#H%-`H%-?#bZ/"0^,:UE$8[6
%_huo61=OIuAC7\B3?@?5U5\.qrnKILoR=pcn!_/2Kh(^4eJo?87>#$u=f*,<##YeC(ruR(T;NY3NsPXFpVVGH2lE1Q"d**f\.aiW
%-uo]cFJF^CC^<PG;"R_k!bO+s;MTVR<ho8$EY9``F=^e9W"6:?ZH`puKi%'#U-)USW+A1mfpVRo][)Gu92e-G$`4'%;$r[K[S"9*
%/c3u'GoP?%;J(_:b#ujX(7$WaN,HLV#($JhKp:U6bCT8Ko.n^>"X?>7/Z=ujLa3,`7+!-!=0s+'o,QPK%](p2TTrO%*(b!I-XG7B
%FSMQhZJSL,F-/PP3DSn`OnkpX;tOHOlg:<?as&XN9Jp<WK]<I]"\;Ze%2M%;b6[C5]d(ah::.YEq7a^Mot3Lm8?39sEoW-+N/16:
%89>Ms@GZ?A[_lpC`(e1SSk<497;RbGk$FAri^A<4G#Y(";rqnP)ZYFQ.GM<jTT:G(W)]"Ao"$@RPiPe&4)##*rD=%:OT8E0b*2]E
%7S;K*=dE2bIoa$/`-%/Ac[g>nE?ZQ_A.f%fF]LoOP==uiPIbj$]"eVV+GS:SKI!5IlaB.'#t`sP95:nA_Bgoa)&k`W<c8OtMJTs,
%RC^&Y)jD.jTisdGr6LRI'XRSN.]^EXO5r\d0J)^!Pl:-@X<Lf*$V":a@/.k\-lPaZ^e>Z*0njU!!*O^P&U2hF9I1&`7%0u30mOVn
%.]eejn]O2*6$EB3%k._qMY27;$\FP&miUi)DE-B)LDA/F`78b48./#ohN5,Y^h^r,E#P^lSPph(lVd*P,XSAUF?(k-9/cNRc1T,e
%Mq[:&?./4_WXp3k_uaAkOmP,0MBh&N*Ue7*CE$gH1AUs7A:c+4$G=_s[b7#Z/P![KbhHqZ3Ojr-DNYt_1mt81RuP,9]EZ-]HG0Xg
%#m]R'PsV8/=<pC+Wta;8_(;RE93P6u9p.@L]OFQ1JZS%2hX"o/5[Hb)\&<G0js"iT=LNHC]=O_P#&`?iq-4L!-Qq`@)+=BVUumPg
%:24*O(&+#>.64eTj$ei98mQ0M%T<Unlm.U[,c\,\R_XI#%bd"(^6Lt/)^Qt=,%Sg4edCts9"XJL(YW*89hWW<,b1OGV4i2`denfm
%-Q6Q3[B)i,M$3Zg<DBfD#b)6D<i6R'GPoNSA3(9A@C0RFKLpucBL'WH)*#H'8_Mds2[,.4XKs7""HZD_ns>K/)/P.]0a<dGT*JY\
%Y_Bh0,u\hA@,+'.9q="MNU,_+LjUn^4&<2`nZ4$!6j.R.EoMUk=-[*_X>q9L_P!l=E.kR&!ETmZ+1@CY0-U^g_;u)[><<uu'*V%o
%eUd7B6mW%BeHa(41PU:p1l.W'+MBc5YV2sm,B,**m4Hm[ZV:"531u7c7kfb#>6]B@V#V>b88af8q`Q?h\9A;"2s\GE^o3fq&1rqq
%M`(EhE#B(Qd%rpbp'Om8@`TB2JufYOJod1ENNb9]T,TFH`j+D^5[&brc!]^M;A1Mt97k/(duj!7<l=i&`,$(8I^St04@cig)TlrU
%Od'NK;?WmAg"q"C[PTZe,6\o[klBF:RefRU9N,<)OX6&j?sY5I4X;Op'%P:GN9;m!,(,+p7[f>Cj(m/^@H,@aC;^V[B(6&o"r?\'
%>]UHZH#j<2e17L(=M5Vp^Z!AFOJbeWp*n_E_$J3JRU+aR&H*1e7C6l5!Sj3^9d`C\g'<9*/m)YZ`MR@7`7ZZmW?jGJ+-lJD_R"?i
%)W>,e85]I]Oikra<"h*n+X-(MRJ8o@Off]o$+34B[]sc.>I5ZN@8((?$W,2YcY"_-T!uYk-V9:G<!(os`/cJXE[$9/2sK(4k3t^L
%]#F*moP9a$aV/#7_ka@s[RceE`8Mn2?;YGB#%XA@K.>uIj(V$BBN'FO#H:ZW^$+1IG$\3SYnLN'[UE+<']?g(:2Xf9P*`Mq+JedX
%=>^`l.I&6]\Y'phq?%LOd2A-qYG?kGTNrm*rd^j'iKk\k"E9(q&+E\tX8j7"%aZZG.HL`7Nj/"B&K&4jKX(P#K;)'_OA+[;\jt:H
%`7Y9#'Ym-E86B^;,2j5EiiGY02:[PKE1AZL@L4th.Q'V:1+l-]"8R*EE)IcYW\#u/Z,Tc\[A%VcE4_!,@H`?B''l:05hU[jeRt(f
%U.=]/^g'#)Gg)<\UsX1Hj\/bZ%384ID$'2);Lh&QN!H</H%'<V6&A$u7N5Vp="m^*>8,LJ-jW$<VYJ(ik\5q?O\D>V8DP-$Y?$rl
%6A?^k:RX8I"?#8HU)i)m`?La>R/^r>/)he+cS'#%.7=k#F\::JASVS:2M?n*.1.^l2@IEH#PL9!WX5TJ7@Z#@XJt;1KL^4[\]L#d
%N'N1GAE>;0]p<UjS=9PYPiUf%Npdl,Zeidk]a9j+Y./RdM^T2]SFgh\AKP5Mc&mfU)34Rt5-NsAD:uH:`'#,<TnW&j,fSmfO2Mka
%BpK]ICLf1WpA?SKEo.LDNC-SaerIZ6?>Ne$87=(cNlY4YQ%T`A8g&b,c$j0aiUEec'6l)d$8!t06sB(G"s#O]iWM#9Q]#fso(`jU
%f.rIA2"D=tA3`EeSC#GgMC&J`&iMBSO83TW<ok_CPsd'_0Vp6I_Ga":!-+t=4<`F=oOfEX<h'[o(.!W>FhXn)?S,Wu5o!!<(fh,6
%WGT6`)P3#+W%[(>2*G5QEZcs,@,(g]H.)(sWj`qnG>+t:X!/GA=@[lZ^,7J#W;2'bn[%<;hT,i8!H^:L%6T^Q+u^"4$+^JZcjCW1
%guLkY93=Rn#[n"5,EU+M[/fa,n,(:Z.L&$t=]]t$aViRH]hmV)./WH15W:g31b#1nSQfOkNU1WNh#rgb.s^Q=fLL!Q;f@J1^!+t-
%k"F(p>"l:s)7'(6MQ&XpOl*pk2Du_lP`u_:=Y[HU-&c'2DQ)W31cZ!L5bpEB_3A0rJl==#p)ugH#;Lkh[%q@DEL@'jR);*qVoloF
%fY8tFcg\ZSbXsp@qTBhS7o^q8Ju(%F,g@^/=\jr(NX/&UDfVql`==96o1UlNCE5t;a$M51DLgH(DT835Nust+#.Yj7T0Tj!*:'1G
%6-ZqdA)YcN'MIG$XsG@M8pL(^Tp;k7Q#B`8]Y`mBo$T:o5LMV8f_G+2g\p*qrp#F?n]eE/cCSA_hE*rJIb9.`s%^^Jr\k0nLh'U4
%h(p-R6m"d6C?-'rm9uofO,i?PIS7H8df%XiS"'B,H$OQm4l/Cb5!$p\J,eg+j?Hi4i`t"srSG#M(KWPfIWqd*:N??W-i5PmYA^SR
%rS<ZcdqWmXT(cT^T'WY!l)J+9O.4JQ4$Q"A..DJ1agj_Kk'EZehti&[]gDnsVn_^]r4hEEm-`^/5W1^I*ATkN*I[cKo3_M\@t30c
%h+,(As-ME0r5.n$oEd-beR?KqYc_TMJAqmV1NEqj<4>=TP<tR6+o,<.nDomu1&^<^3T8i@3u(7O$+!_L]cc3B&ElW.e9U#qeMh`R
%^A.tnV*lD%4eKhc05"8W8YT,Ai^:Rf&_H2g'qT++aA@`o52'=7@uf9*aIZ?8PqBpXVlB()qa/_#>7Ym%*:B]((\k"FZ*bIk0O:nG
%i,"HC>q6^K30q])&d`CjEf4V$RPHjmW<et`Yu%-0n2!.#Yap?\<8D3.J?JF!r31bP);3B1nt.`AMCC^j$si0[B\s[Oqt"X#E8iX4
%lB-k0@rDr7cs'Uo;GkG)d_<VmK#e'8ptFpS:nC\Flmp)t@\X]hK7uR1co+I+"8I/olL^:2jDJK#HA#:U>eDGO"'eiPB8WdF6%`k_
%<PP+W!]IOk27Cr9mB*sRLuiX4%CLt)_LPn9$'-ohLb;h*I16;"i!`ls,Q!C]G$Zqq>&j%D2j..??NT?3""Bg7eH/$\2"4d*k]WFq
%BXQ$H'!"hib?XurD,r8@k%mI@',2.7(pO_/Y]tnDf.lZo7MIWGOL;uYH\fIh(jLKIq\!huTN9@gE!=[Wd9gA&UHi-1U)pX-MNItC
%#\7_9i?iUMJhBW"Sa,R9^4B>mEC3d9)IKR:S%S5LX!"h1/J6*HH.ENSn/62=-/^5l$#(IOE-b?'PLE$h!#F["Vqie[@[,%LepHtK
%@4F*W=(G&+0t%e5a;GsmE6]W%lk;"%/k;;=["AU0Pa&CS_sE'Fq"Gd(c\m`/*YG6A!jlNXlLe'(7tQ3ujBgEr'RiT!36BlUXF[Yf
%-#f@TRgtRgKnl,\_(ZCfB.IKiN<Gs(%%:?t?k/8L1'7XRfkKNd'rTEo?j<l%LYZa*mI"E\YbXe2)T^)X!gIdb,9n6VAjcr#+sj#f
%X5*a#6K&95;iYq^MH^oddX-4<fe-hRqepI$N^BGj!D.-B/m("fg8IuMC^c!9RFF1GJj>Y"`%b.H6ilf\j9pJ\&HX7Z##8ti^cE5*
%pQh#FZWC6C(S:u)CJ:trZLfa?>f.gj8OM-K5WCb[DJWeRk'IQ^Csa3<<QiRt3%KU1+mQWdQT$/?BhmHJYcISee^(4>?9@Fc:gGQh
%Git?AQ+aRbWtH?;eA^'m9@6AuL(mJBS]h2PB.\[u0Y!:d8eJg*9(u.ml,9*/.j,(5_T:pI%)YV/)GZ(ZrCW(_)XpX!V6%6=iibP&
%ZT%-P6-I9'Uj1l[M<:\?8Mqj4E&pIuRqH.]TaD(9@1`0oL[^G7:Ae9u$QrFl2:s0ETB,d-?7E19U)!E4E`Me_q8"ENB8Vj[(6#Gi
%s(<G+W7G:Q5-/;2Kh[i2V:ATcM1[5!3G(p,\/!m+E^n1mhump#FKJDh7`-*'P'AeJktV#NN6NukT8l1r;Mel':?;)#3m@B;?A?tA
%oAV@mY_suHLf0@qF<?Ku'q><sM3br+rNL4X[3dW!91HU2JD?Ck;S3c?KqLA:()8eT)^RUU&WVMO8P!$0f7TSJ+bYFDTI"-D1uW3>
%[LeZFE^k([(o)^+77k>`jK?Wuhe7$W''FoeQB!FFWSPH:oH?`EHBAAnSX<1mV9^kp-MQ"<&mKqBaP;YlI%]L1DX,4)RdlV$bA%?;
%DK0X5SQDKNBuRm2Kt!["hLWYG4@J14BM#,(:*k'5`e`#FKU"p1X2ij.\Vm\VbF-/B"B$E4O@>qtr<U=q7%m2#a>p[<HBAL5kR<OS
%MGPHnI"H*[%<DnMd"7GG(oI\]aJYLii1[3J"G;*"TlXHYZ7O&271$X=4X")l3"[#!W(oXdiH4P0Q/EBlD]?Vu__eA,+stE*!/A-c
%5]BkU++u7.W#T?)D?(.do5^&bV/!u"Cbp*1P;ZrG]>L_(?9((l3##laO0M6d\Uu"\KpkG94X,.kHt@PfH:9'Q#59^dok5WH(/bA]
%<Z8q2)9jK=`tG[ci7opkN7-]2Y)5G2!PuceSW29Q8q9q\U)=SidKHl\Rg3*BZYqA2j'"bW"V+p.,K1'4Hk>5KaHBhC`]WPa7$fa2
%c=YkKEs:kOOsfG&1N;HDT7h'[&?fiJU(4.?*OgS%\8e1*8TM\c\K8?sGA?s+-))QX>&S/.AJZSSj,9^\2k[]o&PV:D$=:a/C`N#t
%UB'S!"?B?7[]lhH0l^%[d,<2W8Kri+DokH$e_+32JkU&*UE2,p\_)oX(b%/<[V\J'=p7nZ;(fuQ9RDJ%*ug**71;k`E!Xa62Y@H0
%7Q;VnSl5M0J'YbrC[,H)l7g4Lhb92LOqM6g#t;5'Q!93"m88BnL!r!rXT>kM@Oim1K:"B_\[LRXJO,cDpCa)d6q](BM^ALCmITSY
%+q;,G`97ia>`uG]Crlu.PPTaVORqjJ)C%CYhhO[eZN@?\L^oF(=[bVBjDm_A]JmV3'<D(`:BM.n,tI#A7J%-43T?4>AqE&\;3;^b
%A4mq@-#dVnLM9(m%o=\tQ@UguYnIH8.+m&3Zh-SSWcU]^LAqlhCa:-U-quJ@27-&WpG=V:85;4(/A`5^K[l8]85c1%2ZtY.=h[__
%7PN?mX*#[",D"N_l>LNm<NU;'b[bj+LiDhBd"J.61)OcZ#n-BaAub!)g7!sNXW);2qbVYC#7?o[@P2L=9[j7UP*dpa:K1GH9[=d"
%R/8]H_&""6Y+6)+M1Us0CnJ#I;o9a%MTra>%Y:FF@4NF4#/FA;IQK5SP+,p;\g#3:#<rpb$-[NB[;9:pXb?Iqk(qeTm*a/!jK[ui
%3,;]h3:S8sqh^g]SM3n1TadDa1:8m$:DI/,m>lg+YK7dl'a(9+R\l:U<YEm+e7cCMSY\A*cs3lV1f@=_UqhX7U*OW8jMato4lrWR
%M(Ja:K!RIgFM!=OMc^K4B!^5Q(dmrijFLfKmUa"b>ZPs$P:8<H;NkjQ`rPi<\H!KZP$-<bH8"_U;8>@?E-,R@>""WPR#\.t,H7'L
%<D)6`nZ_%%/TQ0tH2UR015egq<EMEFC\BS"o`GRZTLuX(o$!!AW>jf>ltL-3kJ.PQ+('\UR6?lK+M_=_=kY0Q0r/\6b'ghB3b06E
%[Q&8jG/B2.9IsKpOf,9E:=\35f\''6V"EO3EZM@1l<gk=a,Es0.24%7kYsF4,fX>-@R<ODoMo?`-'QI#UlL0dq4N.E\J9#5:6h7]
%H_X0FNkrC4?mW?+]d#<=EX<A7@#?k0"A(G/o-u$M"\Hd+NHt_?5k]#b=?/gJ',W'QVj:'F1[%4nBD&kG<B6?;gd@I-BB>@Sb4<F0
%:^R9W!AM]1-OEP>`Q825H[Zfh+U5nWahdre9Fc@=2GrQm^FaL,m)7;E1h<[EPD@8I)?CU,[n'h"75!%F*cOII(Ep?2U)Tafi0DKb
%5e+S9[u$JXf/&9)kf/FK>)#qnMq-@V$'M[>S4afVm\KPK`Zo<!&L,OgeikUOCHOVV$R>NhOB/X9#S@E5K-;HY37.cFI>pMC]E3b2
%bSJM?*,W%U62&GU:9$9^RE/_1\J`?X,iY*SFi\\mH?8)]PA!$u,Xdi;Y4=]]%.]7m%!K#cjK1l/"T!6<>eE*g!giCiX"(`<rA\OG
%#&2hc?`#&Q(^EUc`eK`"F]rmZ?<9.neu@7C-]CD?YQFuZSk'53P$-86OKq^3^9`F%N[3f-"c9.r?&&PPfZ!"EgWG@c`DK`.\oUri
%I-4lY2S..9-Z]<4FQUXXgBT`6ibVFQdBVt.NRchO)H+j"%G5_XPf+(EPm=RQ`%.8ka1AI2A4?\]+?2+"!0^F&Q>W@g!0g;*1p!]+
%q#Wp&'^_.6[&6lR308*DX.=nfO(6M4J?/qX.KMI;%^COh<QH1`Je<kgo-`7e>\F9X1mjO<^2CiCA\\qDbb'&ZD@V<c'IK#pkY1e-
%9K>kBClVAkM)k5fr<:G/U>"f\kqu<NDgCj5+e2]'"baJ=MXpj6`0=BUNX(<DBsX+42C;<TM4MG>bmO[X^7!4gVk9p".sl6_m>gFe
%V,Z6`Xb/_BO>EtZ?2;."!`IQdB2N*AiZa.++NNKXro'!,Sb.S,SDuW$*g\9'CD5;C'V#r?-e/*_U>7h+Q&RM?`h<N-98P]k-ToH.
%+Qa&$/ILcPG8XM+6r01HRq";X=`b1.5L;"H+]l]Nq+',A\6<Z(;Dh-HXrRX[O^gUY^!BYj!d?OdTZ!t"ZA]JCM'\FBA.ctae#,iE
%<C_+&'20KG&q^(8MoEr4FH6\l=BD2"kNHSH+?TD;d@L^2*8R_KP2$pq:pQfVOkLDpXHSQ"OX_KMT*$sh'@k0Wb_cef#t[QY&-deY
%f.Em=R7/-2oghf6)e9`;pe5Vi:cK+0Tr6hcTb#4%[5o3=`;uo7ACCEJR*"S2".)b4en15aaM]WqSol(=fWr/Km<h7qW?c.r664*n
%Sl#3%kScuP-WsBl9IL<QZsV@&JfksuL22)=NuKX&W`<;*U"M?;C+!]N7C?sCc=ms^ER7&D?'(&+$umi0rjl"?MWog@AJ,k_]>A_0
%)m&ft-+;"tK[?Cn4"(,o=!@pSq^O'GXrZ$8Hm`:1!)<@JGPNYdbE(<s?^tD.]Jr./?%7!=\_ir`SEra9Sm.,0A6td]O5`SJ*R!O5
%6$jhT=%rW6N_?!LS/n0h5.s!?gO40cp%A"GZNoS82sdl[QMsR$pWb(5I.o6XVKk_^OibD$.<>PEV@]8q[<N@-aG1KR$f?a4(ZVF]
%l#ZNr,%hg7hBYFKH,[?80d@AV_dN290P.6EK0T*9qd4:/W;_;CX/Idt,W)N>Q,BLeq_TBiL\Bg(a_((5UAL:T0FG*^'M<IZfH#3C
%G^Ru93/62Qo#TD9O@lRSNP8o_rHNA\eC>lXK9>&RMe")m)@9TeTRK9F;Jr[tCUKR.=.B&HXnVFNj/9ULcB?b5r4O?J9Jh;+<U&p]
%8C"fS0##j^LV>D;L97rVPN/RD/s.F"6LTr+A4T'O*cqUML*_u"dB0e_XZ";o[F'27]g''XVlhrL/WCBo(ci8N#"=#0RdfAl*U"\i
%CMdO]:$h%^IE0q9UKN$1CGXl"-I4h`.'#<*&Vs#9_R0hif=0<X[G(D)%bGo@^#)Vd:L.gL$'!C(:nD0?pL^NCKc-%'/J(M+XgQ9Q
%2K"o"I[J%5qd!BRl=r*s'o(U/hmH"17"0ZPaLM_7XdM9%pXEqfLKWtu=ZjFUL=N3)2Kleg(k+CkNhhbii4[]MZPKCf_@3d&%^UA,
%YApU=XVNl,Laq$G(,I&*Pj;T>5lNNiB6*P7TA?-"GT1-+A2,Q%+krT#FlGfMPGSVoX&&>NqXRF2r!DlmnqOCGb:d0r[BAK[ql1,R
%Mi(@c&o;N/]r0Zi<Rf50^kDiVM:pPLaaEmigEmJ,4Tn6faYV3Ser6M1J5[j@6Y[<n;$6.Q,o>=q@5&73Tc,*o5ma3lC'C!+"ad.)
%O=0ja59eX]TE@R2hf0tUJp_E[-rN0+nCF39AF1UH,0Q_u;]2r@=$Qp^hfBeJD&-:gU]uu=.gcXe0V\XKkTqUi`46Gd5I3Ig`$#+H
%A%;]ri5P%`%jQoC^Wmc=&QiVS\MeOU^q@XrclBtrJ>p+1"XiPP[^EOVE7:^K%s@d]Pf9:T-*Y*gk@Gc.`EIlS*0o$*K%8h2(1d\!
%.b&[0r*If?/YD-@/pJ1Ma5m2P%+J6,YF,V%(V`71T*5dG6eo019](Dq/[$HW4N70.R+VS"XE$b1V0Pmcn!F^Qh4!nd-)Y\ARkm=i
%X`d%^=#!fM&-flJiIL,6J@;W+@MNtL@`-*9b;lII1HH6t%`(HEI%:TVPMF&.gSP&8BApju]'e5X)Bd8gR(,`(3-A.C0tRSK+<on"
%cQl!8O/=KcBKm3t(0,>>LQ*cffRH0<YP)iY;eBK4m2g7hA.U6$QG..QaG/(LW48=uef_>NLp#5G"<C*<>ceJ)DrmZ$[VcL>9!E0-
%:#OAAO>s:F58G7GeLo8=,V<%qkgBHd.)drGB3UVi-n!e0>n[F"3mrIMdRIj/AoAt!gQ[HMm.bPLTeopK7f4</h%2Hc2,Aruf7`3F
%V][5&<K81@.;B0H#Os<2WEsa]2\fPt&F+F9\i,@Wocu=l)J4)L:;q"RF`1`j>EqB'Sdd@g")Q!dqr#U_C:Zh;Scg%g\aIhT(+\6X
%CE_ZHC/GD'jTLaB95n&&'GO=['8uLYrMMO$95&V97A24!n>COUFi)K"Bn#Vq8n^Gl>-CLaM`>L7-ConMCb^=5^EbFr6cJ$S8pEG/
%q'+"T=E/.eaX,<!I^`&<kt$&tRi2M7",ouJi*"XHDQ!'%_e\)d/$UKj]Z'\h?FOMWG;$,ReC@8KMfV8N*[n1OeDa_6==i8Lg0duN
%!^@t7NGaWJNTW:HWG`Xf2)1]'*!VIN_S@gaP9KU,gOb,W>O^3$3&HGL`qaL3Q'liIZBJ#%V&cfhBWL&@PZn96Vg>"_\-Hp1oZeQh
%AHM0PgL7uNBt+1V"bUu].S,KuK3g9QDe]H[\WLkrffEqQW%3"BLb#QWD4[%'D=^!eBiThrrK$CK3>O)VVd=0RcWTd+_5X:![&3V4
%"ccm;iCVW3#8JsMQ*rJ1*F2R9[@J27[)[aIFZg-n>Vdk^\ct7iC>OahPoXO!&+qd,(\2:ii(=Cu&Oj\^>:(0qW?0tpV\ri*g+ek`
%N1KIjnWjAW6jAXB^[)'V^fgedr[o:9G#bK)VGReM<s'8]Wj^o#bO%pU/spm!-Suu1l?5/\)-_ktI2oM#CuO6K`;kcU:51g:.u(3:
%1WV_aboo+'A%000,KL/%iA+7"3fBaO0)PGc6!^]u)c7SeLP6F"QE";4eTeQ*[7^)'>dptB$sb#n.CQ]WSEF?GTgTC0l/#?-5]Cpg
%KBlgD@UnRX39fXk0Y,.L)8uljgeink53]TCWi^m439YZ,O/k/gJ`pk2+E?<0bTMW_PIGL-.1?GZ%En08Xsatcb@<0ASie!LD1K4(
%Er`gnG/3cENoG"=*8XOJ7P.F4W'pHp=:g2N^6]q9:m!;_OL@>C+bUh!ZX":ilBP]<M.!Gf#tPcsUsH%p'qomi`NH00$`i,eaE/Nt
%!d-%(g]j"pQ`S/B&YjRj_kt=XP"qtm=fYY^k`sL&WT0n@?E5@Mj/Ve+FIhcs`f:-8,uFh[&U+s+&4(=>g<^OA]H%PCSC\D":4YRq
%E)0".6=AN_#-#&ilr1,><@EsK.e$HM;n$Ih3>+hI\Ea\3`]!5g>P"r+I`XB_@Dgi?EPqAL,)idV?"EZ_<Ri:FcGk#qe]7;6mQipN
%"Wa1L!O^G2kE5^C<oXi>D:gP0j=)"r@b@H[^fPIu6n!Fb_45PKmE:U*a9.Vc;&^"[Tf"G/49V!f%0ASAFn`fD9i+U`AdcGIn5<ft
%fiOTU.],'_*:"WJC7TZK;qbP^:HqSG@lX-c/&5j"m,G00Zdr3pVK$;>nZCa>]$hM"SHJl\%3.Bs`kjlJ\/;<&#(,N(Q2>:7&W5#N
%H4VdfK(MGo\2nTm[LR5(%/Th>BWt*BA*DS"C`I!uo>]J!;)&OHNhWm@eZ:f'n_@gKs&:@GpNT[bXRRb%79untYLPXhYO/W@iWVO>
%76hWcZ.B2!]Sn_\D2mPrQtj>BeA4cu>,X*S4"]V`hlo.gk_`sMH>6JLa^PU?Wd"kT3%RF,fcWaie3`dN4>ZjB,l:K5H%'`@mO.,s
%%_^HPXc$toh,q$&N!%\3>K9iZ7`Mt]EUJa1flt\#01!V%>:(d>2t29O?(T`_TN#qUb97_c@Bl_GXG%skng]`[?qD*$fh#bL]5fPf
%`R'Ml<>=Cf_=`E%paLsL.R!a?C:uMgA7EU!U1Apiol+N2SmXDi8'0`8@&Q$7jJ+ol)2]Z;s++5rRs;ArW>U2\L(c)iNd%A9?9W1?
%FmF0FbT^5m[Y[a1+)i0(Q/,kN#u`Vq%q'$'mdUim!ELm]q]LDGJ:a[[UN*]uKrGrL,I1g1R&Sp940\@jf\pR%N\qX-</nn_7>PGS
%8pC9W%>Y)HeS#qfl[cipWtFruK&&\XZ?eLp0N=N]<4+Hg[^-hbXc\eJXu9bkMq#5iN2,OG#h*0;Ng6;QC,Y*u9dW\R`_e^pH,N$f
%=.(1K&3eWKDQ1NP%De=)8O4f!1@0r1qiGrPW7a-BpU50SD`1?/A07[\>b+/PFYPb[E<Gf?2X^*M2GT<D;<DUN:7>cM%,q+h?'b&;
%1fpg!WM\%`bn-D<*bo*3L6Uf+&Y'I6RlMJ4OUSfeW:a&pC,D5)$)s5oErf/rJXt2u$C'$u;mub;BbBQtOcH47(lZ.!M@0m0C,8oE
%J*oa(f,:<6/(gu'1p%f7XTB5AR]7NFhf^p<=D(0B2WG&rD1I';lYOR(?>[[oOjF)<28RH6O2kZI2TMO&RjsGM?+1rcEntbu2NGSu
%AfO+<_743G$Y:3uc"7q-\HL9U@8V>b7;"DG$mmdfW>RZE.tN:h;8R6^Hl/qm>c_ZKPBpIbqLSF9ZhCUANK]Hl&m1.54kL^5)a78s
%Z*I3t;HKW5Jl!te7Hk^&P3smo_P.Y"%&tJ\hj[*9EIhn&s)8Z%?3].E1Z`p4G?3b&IN*ZlkIN95/_8M]p-N/#<b%:g7&4oclUT^,
%kf7?E*Gh4.g5tf9gCs])mZf7R1Z\YQ6*`CfG%fj`%(`*`Kk^b(,X&3$Pi0B_fC2/H>tV.al>^0bILhSjWEPc4*\lp-%]]c_j0K*k
%4k;%sHW\h)?<P2@/@+sIg$8r'oaS6Rl;Rql(FC-=<0Q\A>-D<c5Vs76\"8_H[Wt\bH_fTP^r=>^E<2i<B1LI?+<GWMpf'.4g5/dI
%mm/<(0VpYX9p6X%`_?MmmS:<QYg><AfNBG`4.gLM?$MX"F#.h2.c9<@Fjm_"YW$JUaj=-Z:a2b8AHCjq;R?@?a>?r5Rse4,_/aJO
%CuB!am-q%/F@M_MWBfjHI5'qZD*8FkqkicSh0M@&dNo'4mhWFZ/>c1c(-;u6\cBhPnrW5Z&s0YTpMVp.P%d&"LfBRHkTlnZ^_rB[
%ODm9=mtD;fXF?1mnkHsG02-_lHT2pL)dT_3C5hK:]/ip-Y,eb9cZ73l[Oc$-=`<:.#ijY&2PF)SFYi6[2t//O[4tWp57add.Vs2R
%c6-!AQq#?<\5)j7iZ,aBDDo.saPqn2:3jt(]369_]T2#]b&)R6ALGc$Mkqpikc;,GP"Ooo`7Z`_B&dtcC)mKt-f9hOM++i!gu=]f
%fP';]E0Z6u(UW'MKns9IYr8cCgtOf*ZHJLP,r+S'j[RaH20GW+a"7d^fH&Th/sN:4aV;QA0HOXZ)JS6EM\<#BT8d]@VV]>GJYNs`
%iD!<gl\XBL3/.-JETBVd?]=Q,Wc!/D,%>(a_FRn(e&'K>RHK;kaBqD:"MJ.YLW6M$--;T=8l\P)]p-:;X#cBZeS68_Fh0XmB-tc$
%@$hnTAap[,SC5%m-:8qCPdu#S\pKuRE4p:n._thC2n_7gr"Rq%e_0o2C:tsb?)lRJiuh]1+eC-f9CHPEjIMftZ<k\08-r[:m`q?U
%N58qT@1Ek,c&SA):O`H#P\,B"#'Mq+,e6SVZ[FNk>8?.k`okr2T:2j'h;`76m-rel\hoO"(eq/k\-%L$hr/F!&@X_&lTPA/2Bq0%
%[N)MX0])N$!3TOuqOL<mB,'\h$Ef"ck<OG\oNhot,F>iXO%cs$E(0??gtB,ZDfl+5c@I1Sn'?qaWB\#4V.&CE<:MGFa%%g,qUiaP
%#h9C"i?2*+f@Z^aSjV?PD!.kFR+$%p8;lf(igoZLDhqmH'7:P8f85t<f534=6DNXPmna0k1pjd(?8Z]\MU_-1n-1`NA'Np`r]<5+
%]u?IDVO)q7X*Y]Vlso7pVR42(\=,FP<?mrj_&Ejm3;!.46<GS*C/r,GNsqsJ#ula=WCOR]K7od\n^XPSAM>L0TM]P7E[t.oQ=)^9
%jRV8$QUY&XPLd]=_G8J#0.Y8(8SMAVVLcdhp-[<M_Mr$PDm[?JNIKI&TGol'bkaV!P'@q`*Lb,2AXsdR.SR!(4HW%O:>J7?1A1^Z
%5s&?A3gUNTB?"(MFn!k+M'V3Kkgra2VUL"U-)IhQ/DgS6c.2AZh2:D'kuM>A*A[o'[[&2\OK]6qXe65gqq-Sg*ZY'T_ikRm(sVq0
%1F]<f@XBD=W_!G&QZ#kGN[(`_o=0/sl,2*5agc+;NfPH%V&MBN2uV,\f;#4DY(mio:*k\p`$=P?4n8/`CdZ^CU''$g\HB"tF&Id[
%rZk91)mDBsP7W!K1."m:_-WR$21bWQP-d)-MBkcg^W&UYk130j#u(1GE@Il>?nY_1jqAhjYQXj>7e\GZ]FQrcXOd&cU$>bA@b"8P
%!7p(Jid%>J?-Q8CRF^q<8pg7+kiVkp%9g2p=+apgWq$B*<FA>gVBi58jV1/;1>PiT'q$rXZ/4;XErU*&K/.mUa<Z`>_s?#c*U>V?
%Yh(`XW0;2Xj;qRE&N%.TA$f.G\;bgSN\MSIEZ^&r!`RN^gm+R]gdY8L[+Il0R!$sp0"N$I]!j1P!jpQ=Zr)\_ZrSQ#8C_*!\TYWe
%Y"mC(p#-Ym.$ndS.SUKA^t:aBFneqR"%7Vp?5,f:Ye!h*@ZBmb_C"npB8D^(FLf),h)=-KKb8`m_ck`[9nD)+$Yah^-pgg8h!I*V
%cFE0C#WC*Ee^:B!]5ASW2Dlet]H<+OA%NShTGmNQS?)LiT+Rc\3#V'`mWqb7n6sbpjQm(F0g*ah:7Y]e`">Mha]K32J<+hrd#"hB
%kk*Zq3l<85=5DGY6Q^MsgF?189OL'8.b&O_&"7t1nnM(0Q?CZs_S^OS28tN_R"HIPNZ0r;s8'%7:eqV0W7E`,ZK[C&ChiR[\l^O0
%rQH7sY1Elj*C.$^3Ym,QqJki%>6ihs?B'CppaVAb'=7.tEDCd-j_3BoVi2(4gh"NY]D$^u(-P"l+[\7.8W-&-g;"bK7b)3%A-;BG
%S*.Nk24>W>8Gp7m@L)mpnWSI9,Uks\>-H]Rc%SmD2]"AWhd"fQI</iOp!&2ul^J8$Y&=Y"?/HD.Rp+XEl^Lpjl2'PZL?k<:)m4*3
%CqIElY]bG9@I<pRa;ENUNJZtjlhFAX2k(0oY&>q/]=th[iVM]Rg@`_4I^aEIF()uZc%QVY2]&o.ho:G8%`@#nbmAajde1k=%G*NT
%2I,-EfQM[a@Dhc_KalWm(UdW,>hF"DF()uZc%U#d\+*mHI</i7p!&2-f/FL(>i/f=E.)eVg\&he[Hj=RCY(N.[p@m]hf+!bI`(j'
%/I>>ucT/c!qW@^1l^Jh";q1Kbq$kq7k;QZ'SDSL<F+2%"c1qh#2s3,*hjiSCI<0kTp!*;Lf=19YEIFTkOg_IB>s#gcqW@^1l^RK]
%f/Mm%?/M10E.)eV_tD8+Gs]IBc1qh#2p1ZU>;-XohjiSII<0kTp!*:Af/Mm%?/M10E.)eF_tD9bD:c8e@<">II`(hqd1=e#c1qh#
%2s3,)hjiSII</iaqW>YLl^J::Y&>q/\A#MXiVKG0de0_l\@TDbYi5H@_RAd"$QH6e/o8&3WPGb"q?Go=]6ZgpUUYhT(Z&=GZe0]+
%re.0.8h39e;Cb,eQ<&a;L(8$,l<5]*i0IDun<I%/-hpRiUYo$#U\I_;1DV0<RIPEoQ!)X3oI#&SWhQ>.EY!&&A\,VB93l7C('@OD
%1AtleXircmRVU>iC1S)CXC.WK$`si\B,b$B24gjjYgUZ*\C/M2\>>H6I&3*J@7)P4i0IDun<I%oKak/C$eni&Q?S[!).(*OXc.kP
%X)?\Ab,fSC8Ag0_`e=l($s.^A/*"L$<ebs1WKgV.gOQK71AtlcXit8<W`Gu=kGjg3B11:m-obsQ(6_@u1<sfRAQFkM<b@?SP#jM<
%fdprQAQB7n<bC\,;7);<eq%%/)'4m9<ecBIWKklsiDM0^:5[W.[8pls2+9%%R"JE/Yem_Q,M$%d^(s]r_GsoXA[m"YX\:r8#B3<d
%ds#e5l<5^E(q75jCTU8p*IK5LChdN^F%WemH=,=?\$Rmt%pBjg$3!KC@-QraLh_O;.mdiVL]28Rd1h@#S>Y&hCM3"L6tQZN1,Em1
%NM#S',tRoQeTu(-:7?[N%GD[t/Fnoq\>j14&Y9.6eb5!P/.?^URA@GRiUnsaoP7!a;D%:)^dPt=!1\;SLW-U?\uM;mH3)E.25qBh
%\D.5=0!8'O:pi(]fP+uE4N(8c&Br`i&Gd%*#45*t,nmH8)$6r&L7,Uc.TlORlu:M#BSIr?lQ'2#is6Sd-Ua.Sj&J0>1V44NN20>*
%eLP9GHsKu?.nP=Q^Pu)ZKD;6>CHLoWC,p=7E[F*2"u'&T'$]e.#3AP,Qk/KQ:^9n"mKBP1-6_D'#-:bK,UHqCI*0tiToMmu[q&2A
%Xe:n$m_-f!Ka*QE-RVu]e!fRPl"76,LN;j>%O3J;#^*<8Af>=fh+Wl2h+UinNZ$@$E0a=C8h&9SAhqg%0OAlXi[G@6ods<6HE:Cs
%<_1g0ro#+b_1X\:P8kAAG.JQ3*(Wu^WglZ'>Kc^13YsQ!V$ZLQ1J'6Te2UBN!$m;0d'_%N3)2H>daY>-U?2cbg)9IXLsk:_E1phu
%9c_lgW=i8CMj"2WX4eH^:rmc-3[Yf5,.8q5a@@CHK>$0X3os%r=60i*WoKb`RA=!*GoV.$KTP0`5R-U0H.$k?1Sf@mKbh;s9BKVS
%[B,7R,jG4DCjJFP+qs+eYTAubT^$*OnqC5q<%Tsb[XrWW46U:sLI4TfmAaJ>X%IH\#F'C/B3<^-AE7E7Yj,eGYh.&Tgf;$]bnW[j
%>kKk8JP49#dkq2P"o3gL]hDIHQp=ZZo\39IQ_F&Ke$O@eI'Y0QLf6BDK\;KK;q!<H;2t/BSa2HG[9+O)VE]kDCV<D@-T#TT0%8fn
%A)5"5g;qpVSi0\Q&%PcAo$[a/_U2rK5bVir\lGFQ[f]QDM4k1/SI=5Seh-?dOLiJ!&oJV2TN`-DJ%ML\'bZ!Xd&jaOZXY3D^`a&X
%_].WNMX;glbhZ)kgEklpRe6*7<0H$GVaj]V\LQPtG"K65FZtkFWu9t]G/]U.l4-&:U<BqH!GBht]@5je'D_g1?-fDo0C4okU(+=i
%j_BR1)M3ijPcN`TZ30,:1@-bU:p)-L$YZ5..OPa[W@H^Tfc*XZrPMo#Nh4Icf<dsTU`JY<D=,?jCKc;nm6F#f0oU8uXDHhY2;R6A
%CKg/Vf(NG!2X2VNH;3.?.!/eEVV?Kf-4/PAeh/e#_H+%44%5M8kVOkRUiskllJU3,#MrH^FS`pf2J<`NaLd=OU5V$g<1ig0mAO!:
%.6Z>:?aQ$nLU.J?=0-A/gElH+'C<:;MoFB[e>o=RZ:jK5WCj'u^Bk47>nLX;[g=ubX::lGq?->hmT*!G<MUMQ1hp(i3p/L)D?UN6
%Zf0_6(2LrMk9Y_"[6PAq<fX40[0>"0S_,2'M`3eN>(3`"PN9Y&b"2JOCbF2"[W8XV,frbC81iA)hV-gt\q-X"FGEqc=;6,\I[Q9\
%Fg\bXSm&&(H#oC^hCmJ5KanNtUm>CrH="ge,e;]sk==g_m00*SkjFt<XgS"EXu/Lgn`>n"1hC?E4LROr^!h'.NaBO2Dbeb>RcR\D
%+;M'iI]Ai:JY5"tRkgs4V>33Be?gU@g6FP,UgFl1JA)S(^9oH,FCA\5mu%`5`5[hSQ!EVZ.89<_fHpF[6H!+*9`+6)dTPbG\h`pL
%aXcO^5P)tW>US34SWc1BrL>35[*H=]*:9uH3nNlH3nI!kXE1KN]`kL@D`*l,%FjV9!3[[CMg_CineN09[H<I3KZ(CoY9O5O4e>./
%S=QOeBLkAmj0?]h36Fc4?KJ.eC+"8Z.ch++RLN8ACD*17N/XL'W@-B?9d#QIMg_DA8,Jh/kP8jM.&O,LM$5N0\Ef+s&g+oYNj%f"
%Lqr+HV`04kdI)<28CKG><fde2X<8NI<QY=c:E;(r!UP`FRtYC$(p>*Q]Q`-MK(<?QDEY,Z7VbMnqSEF(<+4CH6%kE4hfsO^gQ+E,
%--@g_SYt0\\1=R"U]Ui`8;F[N5"3./FbZ!W*\N'cA1I71UuUglMkQaTA=O2/ZtbLXX="aF^L1gMp,TmXC)'5Cq$fHeHG-A8jC'".
%*(<*$CgRVDY\ee3)2cF?lbK3P41oEl6;tuN2q;Bl`OdqQc+%o^3RO;HboWS3>YIk4A29WQ;Z[P4GggiCZKkrTl<nEc*H^F(o'<97
%'E*\bhi*"f%Udn$"0N["Q.h3cX2-c3F],UZm_kbV(H@cZC/XaG1JXkqGPB+f*Zt./n_>1EF3`%RRWCt['#9>n2'l".Ru?<[Qe'KW
%"ofb1MoaHc7amkFW6J%4D*F6#Gr1uOCQ6[o?DiYJePu"F3DsA9T1U!^W[Lo(MKef)j=i7c`=7Yf(i1!IhN/QnmVSW\+M>=q=XMj3
%Yhe%pE)\;d][oDB;2Xc/D(J0W*(cm(lG,K5QWUWI/l^Y]fRZcVAHc_c]23Vh=+s=3Y24p&F]>K(U.9A6``pO_PdS(S1r!L0k;9\k
%A1P"PEY&Y]a(OtoY/W45m@-:7SI$Ynf_U=UM<h.)[)Eu'0kTD:,Zu\M3'7T`QV)n/J3_rYU8GOY.rKOO&n^3:B=3d<WS&d[\3`&t
%MF.mf`f+I,&2Qk9A6f]rgh"l`A4^0Q"bRRr_00&B8QkSl2L,K[$GfuflfpVC$NjBJQ56YJ`nNollO=<+M#fYVoFl"bk>VY4+SU,'
%5BAOGnF6(aC(>V;j&e?q4cc?j@]4lQ:M."Wj4%di*Tri*/r[:V0:i[me%E]ZQ+t1opM!JrdQaUCd@?jD%,CR5^"GY)j,U,o#6md^
%ma@WVi6_KEhT/WSW>!0-dm^7BCNQGt3A)WeffA>Nl&6^->#3J5dW.'*-%o]0L6*nWX!+F"`+%iTbshN80!cZ%7^j@aBY5PBjj^Au
%+ms@%/md,2Ve43?3K@[PS_K.86jG<@Al,]'e^O^t##.g8+DA27?ZF4rVLp$RpQV_`B:?JL\p]>Wm1?%iedY4g!dgQX@+Tb9Nob<1
%%`l+8S-RQ/<l&rT&#P3g"5tB.#3&=Vm"@oLL!sf>)jB)X]ME]0WR0$?]Am^sbG-t;gHfIu!S>_<J-QBn>"ShV9Nk>LGFk^X<<hf5
%G[%,2Gt`GfYk70qZ6#^:nHU77#pPD/A77q4c4'L\epW`tXnmEh;8jlS#$5$(+p0LF[`'Pp=A1RT`&Kp5])Xq1riDK5VO.=qarO/6
%.NiN3)4YI4)YeGa9)GpLhYD<AL99ikH.@.NK"^(?AJ]M3OmG3FCSJEf,\I$d3?>sm#$APrcdt@uHl9]E1d8p&O!mmJUB]fk]>kbV
%D#`/C4'c.Q;l`QG#"Rr6?cDq>(OY&j"ck5"_Sm"-&S.hug%GANFek:_,^5E^-`o[9c<K(7>ePOS-ZV#!Q#;caS3b&b-iA6@G9hu3
%qJ?T"YOPb>1jgeJama(+D(#87RCZo&XW)blomXdP1Fb$O%@K_N5Ln.Lku$1q/Z&s1f^</>.J2J2r9"LbedZcD9(ibn2VIWPpftF^
%5NV$td=,A)fg>n6S(b>kna"hO_^el&:2\ECc]$IpkC;N)fl(t*^Y`qXVN]U"CfkW9.(I]d_V@n6D5E=f?<=NGrM6b,Z>#elR>`)-
%:<oJP5:>WRer7A`k9Nj`PKp3-f/2fhCmgLcS,$9f=oEKF\'.G#jnjdk]qM8mg$AH4GGl5)oqd,cR4s,6rB/MGn64ApcQd2Win(a0
%#(4je0`8MhZAUp6qK\kol[E\Xdr+jMb]r6]eN^u'qgl@ZX5DU7hSrE3V&tEADT*u#cIm=Q,U7#,SY@p-pbD,`p#C:cp.pgg<F6Ag
%Q?UkXF[kBU<1Uj>GaEdt:Y4PS2jVt&X$:/3NFf(mOFEh*EpHjqP$^<e='`k&=e1lMDp-dUV/MBaHi6m=g@%\OkFq2YTb+.ZS=\LD
%6eB<V?+!/X;fPZ4>^"r(QJ$_YEBdjN]m;^-T4.Hm(g1gaJ$qg!a489(QfC@i*"!FVcId:U[opt&pFu?rH/:bCTD:iGr42-87H-oh
%BX:n!e]n',7eJ3E8*hOTjkLY(_:[C_&'>j'\ZbDnMoQUnTnljBp2)-k;5J2+>g?D78]R"bZKm&)/F,sVRAaC-qd6nqI+;DE-.uk]
%]kWu,k8F!rB&'GR;u[_ImuNTB2u@6'jDnLb>ef%mrYPV*bKo'pkN_3cdpm^]dFEHQIj`I+%"5SUEs8WWcQs"YgRmhn%Nc_I>b-=:
%cgDjeo7?R1O)Ak/=6J\uiUhBpm.fd`HN*%'=/RGGZ2a6oh7mrt]=1!a^DWVln(Z$DX.)o"]A1RcEW&Sg+5N-%Boao"%V'MA8'__C
%pKsG^m'#NU?d\oPia9jr_-=N9rGhfj5AiZ7n,28"GA#5T%Lbc%r;#e6cC\F6T&7cbIeZb/a7HphqeprR0>G-[mj<GVo^H>hs,WfP
%p\ssM(]X'aSWeupqq^;5(Y<$]4ut_hqJq(nqtTGeJ+_C9^>.]%-V&s0l'GIAQX?:)bG1]TXaGKYG*b.KFru3T2#k7<oW?q:.\m#0
%]^<EMAIL>*0+oCq?,&.puLGRq8rC_b1QJX^?la&B;I1<hZ"tps1^PmWr!]GkX54<H(KM55#7HIL>P2jlKIa1mc!Ten^ZmqH2?aR
%-tE-5^H#P(*?B=LDY@`>\4ja/@?eSmH$=ac6'M)5Gm[g'W6Yp$,ZAEKIcEimJWoY):ZSXtdpua-).E,_h#76Mr9.sHZL`#'oY48s
%[C:@mP?Hc/)7mHU5GVK=W%;_;#Nt"2Jn^LF\pWCm^$7f<S2\lheXY$!rGMWOrSc&9]`;<hoCH;W2+KY+M&An#hn.jV/7rd/\+\51
%)57'JK7EZ[q6BPTNZ,:l(@QJ>hmCS#^>$B1GPbt3rVU#>HM?=KnoF@bSH=%[PSIHuJ$8msXiotRQJ_Nm`LTMkgXl)g]^55m`ql1t
%q==1+#>c)?F()#)HX3\[i4F8]I<P5<@6`W_hYrT-MnbeR-^"iZHuM'X3'08f]%QQOF+6/Sr+/&<hu;HE_ih7sp,g=%<Rn#)IWBFO
%8Nlp>e&/gQE<5[QEI,@:,Cn[gnK?0Ug\/sbh&IGbrK<d95.Wp@mU(*VIe)Ljk-jGc_/T/Be"`t5gYR4JW\31SpJCIQdJ`u/ruaNr
%O$,Nb]psEa,<A'%fD^3fRCDT?+3\@]HMM4Q43[_"#'^Kj;P$+nq;6Ap/AIU,GC8<RjtG0\G7O5F5!?S>n/EA!mdTbsZ^\"RY;*!S
%c!VUYMjmN+:cb#je:47*$f;,2%s[3+n#mmX)jTF/^HCl?h-]Tr:TcoG?[qajrkPGdV\f8:eC*2:SX[hDhS4\mnqX!@;n`V.G@g]8
%Z+&V_Qbb'H0BVQn]@lU`KCa9$L3C>c4hqusT'sjJHg[DLePIj$0+US1i?MZgIZkMN'42W@;W.`1RRhElo*%-9ntBYZF!Lo?alFmi
%_E`:?-!GB\fAHPk*U3YcTQbn^N^!E6q60hT)0,T,IC/nCJ!o-70BTH&ThG%Umh6VL\I3koRqMPLY#]%nC-RcMW^Bb+FF+/`a/a+p
%1L_p5=$*!N]5dWuf?rWr>i\Q/hOP>OD(_Lk"F2hAoV!#GR@sktSnk1X?MV3;+tMp'VL7iDGM>512UWT9Nh]W8(kfQ2=S)ugB<Dg3
%VP/+a1qi%,H0W)/H+#leYB6iDpkpf.pGXAoI^S$=nKiU-f:M8@O%f-d8UG'2I?_31g+Tc4B&%WFF/=A!g2Q5i1Z;@nfE@:.8\@.Z
%S*\ru)g)%K*DXqj(EXPf>%SMapu;/4/X\K/%fcG(k43jMXe1ZQ+*0i\J&ij,<^!U<5G'p=i5D<@Q8[O5_27c@p/>-p5GnBKos)9H
%hS8g0rqtf?'21A8\4sGnDjt"$*Q+1oH"oKk:.t&,C3/OFEH8X_Vh@^Ajhi:/_lCq*r$D=<n`-<aCK:AJ'-mN>04*VMd,"$X=5.f7
%S%$!-bs3-Daj6)on%O%Cip'<gZo:M-^83ljf<bk^%D-0eiuPF3=m#k,Bht<e>PPWdA&E/GPe2cdQ?/Fk%)m8eJ<'SW^%41s]"Q+;
%0`U:KDth:*W&".2Mcf;Y4sUm@/?g-cmeYC.=6K7c\SZeVq=OjYLd+LupNKfR5GhM2oW#gJiR=j&oBS%^Y0PVm\GFaH=(0_:FQID[
%pU(,dlJ9_+Q.bi^jniD*CQDdD+!(%sSt:kNSi#4Hbk!d.rVYpZl?M1Fq-,N(JBQF5V\c64efS.&g`5/:5C[nWjjd[q>86e#UZ7i%
%/,]f4\AG!>dob8U`AdG[S#p`10U/oBnK3K(X2i-RMmFcIQ`'_,oosYBSY'/WSc3cHfof.ndqp/4FiF>V.qTR!ps0Pcd4lH\i:Yu+
%"8>aH`qbA!HZF+K^;%7V2dbFSL:qt]D%?#@Y>;I*Ya<PPeYE)Pq`C'CZ:[]r'RG1,Cq?PVGN/eYhg]q*2APC8pu8GjoCKNco==P#
%[(+b#Spba'Eq,KgV8T"uC3p'5,D2<UDfO7<jX>KMlJci^PPg1WA?HqVha_eD\hX4Uo!9FiNRu4+X$PB"^:lX?+R(?Or:"/=F^?,[
%?E$VVB>3&+h2KEV(KZ),$@[F`a4uLDHp0R%qAM+2AL:=`dH/dG4nmud_Tho*e*H<9jE>`=c1uXCrcsENQ-)04_]mXu,O^4q/RG^$
%Ejde@R.$Tqc[jSiaHoqjRJosNq9s!FS/bh[T0C$N*W#B*k;O=qi-G0jchkr.m\:SRRo=.cO/Bt4\^^\MjlU!?'g.r1(n=0[n3(sm
%a/AdY43sLGnA,g\/bXjIq9+8Ys78J'IpRNr_-`h)rn9T0O720pI!tT=5*q')qEH&"J,_HMah1d5*+=b"ZD/''9^gk]F8cfL?>n?]
%q"/>$c+@'S^Z4uIs(jR(6S'F-F@YWc@Uc_6G;7%YB.,Jp;2S@4(Qnpb5<gg3=1[H6r8TB>o(/#koqo@IZE^%`=mjW-Pg%g1Q=H<^
%4o[Z@>Q0C4]:0n0s%$c$2uPNs'FfL^%je^V9;Co*[eTfrY12Y7Qn(&l/QV/ogh/Lbf@R*N?^UgTj%&_-ric=0o^)5:o$D8I[jidi
%q6B\c>Fj/"]><berH-m\DshMoBA(_IPNKP*a[Qunr"&0V)>an^9(Tf=HG^]J_&`!8T6-bI096]jH?@fsh!EFsVpA^AM"gIu?i8*r
%@q[N9U0gju50KL\>af,2O8*6!43J9b0YPV&o+BsW%niuI&"`\[gYGB7T)\^aqS3(*ojm-K?diGpnZBHJ2/!O"psnWR^/aL>BpRnh
%3+)W"C%q!XrokfF2S8_HIieX$f(P5Gd6bR<V-6WtD2F"4r<sd5aeV7/=JD9Is$7Ni!d,$]hp9QFerO)mI\SRe`%B7$Nd8@RU0a)-
%&rFAOT_.)^+`f5@aE'N-\j-2eg^d]DX0699-55)*Hld9&-ltasHrj(lr*k\N80Jpus4jFaB,7ri75*'sK:<5Xe1i*UY8b4&Q_eu)
%(sDM"(;?/3+g=bTS*&,V8.5*fnN)8QhG^A_aj%e-j+\A0hrpQl2A('J\^jQrHKhaq:!U);]s_GeJP+q)Z]TueY16cR0#K1T?g0X!
%mu`5c:RS`LnW!T$K#)t63]g$*j`laAeDU\)'#.7ZJ2\ojR<!4$1upc$'Th>M"ZFZF3,X5Oiu?"AoG,rD_B\LH4S,+=-UXFl.pQ]7
%Ht/XqCr4b&>'sD,f*'799@Z\dBW!5#MrIAQDLPQs\c:Qb1#kU;=S'LUGeqkLD542eiV/8)qf(EZI_]qZ*5'OnFGg`<EMAIL>]/
%Y"o7$pBLm6>(\KEX[#>R?aRqfGg6o\mpDWWm57Z9PXHhG+C5`P(eoAT6DMXe\MuZ3hgIQc@e\9te^rEbcXpmVW?j`Tl`Cn[K=pH/
%>0ESOHZAuCCk$p*mO[*Jon<HT]bofd-CG.!O2C>M_pA$80.qGj*h@fVionRr0/"qb>RKB,WX(38pd4<M^\kb$[#PWof1YJskk])2
%I'k)%*m%upj1Vt:]XZ>oX#,9GH2:("0<!I6G@''0lomG@Sg@3uja[(dj]ZIo)eCs$L!MF9(,K%_ibDd6j?n!;hhRgG?<$b27jKLl
%53mrT?i'9Braa\Do\:`eh;.bgfo&5ho)Ih7Xt,Am?S7DBp?0KRc0q/MgN@:[=4fX6-T\3(f,"Zq/'ge9eo6IMTDn]biqVht(H6F"
%(No67Xf]-P?a_;r[!0*![mM2!)qjo`C>KC`h1'dXHgg%fk32X*iUj#<YTuI/>-^og-V*@W**kdEh=TXO+72j!?_#B1b1nd+7$+IC
%-!MmOU/,\#o=fl=h!@RBf!dQ<'fG0LrUj67?Lfdnn*-`mqoQl3DD()^J'!bO)=7=>`UfkB-i;('L[4YV/TJKOm8#o+rUR"Y59.)&
%*5DLFij/UTm<>_9Nau;MH1Cn"?[qsRh`Q7@mbe%:pM3rkF\U.?Yi#K[K6MO!)!@U<S+k%^k(NSPIe3!VY?m_^hgKHmZN#+Is5!QJ
%[=qP]jjX#2\D4FPMR[gns)A"Wc$S@)W;kPAcZ7cqLI4#VA12/js.:)t1jaBfm_@Es2J/K4I8S%5?4htAoDS)\p3)ir2E[EsM/N$7
%rQYY_H#Yh-r;"68[s<qpD`:_Q<r"]Ge@oe?m>#EDn7VX9Di\eu2pH3o?[_G321N:oV#@VA?5t%-B_Dp3pJ60/<($<Cc'e1uio]C_
%Cb`82Mc/0$U\U;2boM1RI;=11Vc"_I-egPBB>=9&m^Jg;o\o6.I,1G)M$8R@mF[ai3M4=_jsSLLX*j4L+.Vl+a$H'8`ms7%J%_0R
%hmr0(gn.>GA^<FES_LXI`n%3_2tYBCc^rt-,KI+bJkt+QUNq]fT74DDlcZ4*G^;PHAZIV7rSU1TP4'=GI=40CcOW':+?A-/k*-f_
%kN^(gPA`%n$=3Yt`Ub`8%O^lGlF,V>rB<2*5.m8,X?V.*FM"Ad"D`#3%mIP6r,+[9j/dV%`T8Ybd/cli;7->mri;f4:?s`BNK4H7
%&M3s)5A.t23H(8A?1)p/)&#sbR(h_oS^t?!cQ?>=j7f[U=-;Ei^Ur?F8+ZJ(=_onPrkDZ4f\>_8g\<@IEus[nU>G`5m1O'Gf?\OE
%mea:O]CI9:MOS\aA\U"TA,VQte]Ia&IcZ#\_t"?8=jipj^Ih8%\TRd_/Ka(ejS?c'@FjUH4W:!fr6<42g!S&-*p7t?USH"L`kdYt
%Z'inf53j]3EU_OdI/<7Q:JYlBqX'm8Ya#@"d=BE8j&o'E7AZK!+$JDWanWi%B9tR74kUnef:*Fg]A4_4F].lq.e_Wm;ZG@,o:*I/
%\+eMO(Sn:^j6s3ZNq2]3o#q$dYo*N?NF8*CAa*J6$C3'kiUi9SGXlXQ)e,N5Yh%`\NB3RU_-*5`P9+3LefgooaY'&R&!??^WW"f4
%qNu#BHpJO'Ds-HFQh-SXF*#Bfg&L,jDb(DRfi+9\DS?5oSKDcU(&QlnlD=%1S,Lk`.8Oj;e`T]+HBe6+\'`GG%_m'J?i$p0-]Bf8
%>u$.?e=Sr$n98tcg(qurf6hpg31h:;3*5\9o_FE7HD)0n@XjSN?I;ugkDm;<4e,dEQg+tWRf;cQD*AL0fD5+&h],p-s/M:XqIdS-
%HBu=U^?*3&-4iddY/cTAHriHqAHe+-:J]5@3c?2RP4-e(rSt)%'*%I@T$O=qI2[FA3<#A29D<+q55H`V`K0HnHZr!8Dlc`KS%@4o
%\BjC<h$<uWD[QODc2G*THM46kaf1iO0<XL0o-^d@^"+Y/n(1'Oe<>GmhIu=A;u[X!B!0KVXZlbe9DSFB^\e5spD^?#?]uu!+(jj]
%L1VC8[E7=^CV6]SqsW;cpX8b_Hb]CR'5:83jeXR*8oF4oB\$8`V>p#?2IHA,P:-7U#6'>K(&)I84Sb1S?G5mcg90Ud1T0R'a&f0G
%St=i?M<c8^A:t6F*M6WM\2rJ2-@PeBgF3)!/r3;QpV%$8$@I,KIW9\dhtcn(1]#.A&hCb6Hp'=Uk32*RrJ`;U5'Zg.c!:;4+%_;s
%?FjpB?[P8a1U4:CUVk(nr)@:J93V,p40RO?ER5d+8[RpI4+H]HFMCC3r88#ujXE2R*0-'J3K!dq5D^.>pn*<@F.^/Bm/*p,ZfR)B
%.@f9K5G%k_p[)sus%-2Sipu7(9ROX);AG\)n3;bUSF(6T&C^<I.oUn=AcAU=:#9Y[G#&M<Frr'=fR[(3<'S<:pZ9,LDYME]GPX7Y
%ra"T199*<HOCQnN1MPRVp[m/$;dH+[IdP0KPMCdTK*dLmJg+moaX)cCB'(c>(Wu-=Ce!<U&6J:i<1uC%34nHsc.IF?7#[>%%)tln
%]Bl+r1qVc8oUP<:(Y>S8iN0M!Eku_cI/=F.@6P`+QtBKZO2WR;e)h?Xq\_ZD>4pk<1J<ead?p9F\>m8g],_f.?[d:ooqU\\q7QL^
%Mat918pT[PkKh6(IJ(62kP!nB>jpj4c?@d82oP3\lUM2,bZ=JMT/tkZah7@OJ[kC.\!N/i]NTUJhX]CJHZ\S9kNl)nGJ3,V8Oi$g
%j+$BIeB#1<Sa14cP$bTTC=NO#3Bl&U(9d-GPaBqulKDc.[I<DUZMALmIrXY5d)24A^3+c$`Pqdgd="VuL[sUu=4d1"p?(B*rXW@Y
%\upC%6nA/c)Xf]4kK."@*abI#U+PPIFSY-iq`af*9=sKScH9!e2lDG>PILL5<RcM*4MRR,n!C`Y\jl8InITo95"&NlDX6DJr>2h@
%pA2<]l3NRmkqhb+^q>2X0@#Ehl=Ra0/pldXq"Xd`-!S]#g'o"/q4UpM,O[N0FF!PXDoh(D1-]i30WM`k3^8>,hUs:.2[=WpnoF!U
%\pR7Zg)cc9I8k*$a.O!Pp<b_Qp>dY_BYA4fcXV!8H1KsT3msrI:@FXq>fQ<Qr>0@dc>S%U-46IL?8edd\k(dA=7C5S4Eu)M`B;7<
%G0dqX]mDUY@f9SOfq4i%3Os1N&>A->@l[<`;$_CGi2+'CnS8!jB]TDq1g4.R(S,sPif[*O^JH?M`A0>cnM#Y=*3FnDp"DQ6e\Sl1
%\@LqlV+8jDU&N?62",mAcehbu'cN]c+SFPPn&6%;jXm@QlH(^)Drr*eIci%Gc^Yt-YJ.MK]5k'HlPfY`Hc2ZI?d\tf!3bkZ8(q_?
%4bftUk_d"4i@@stFO(F"%pM8fjTG'qSD,f*"n(1?n=$sYN92pse*_I:VTHsZqXhu&QOPH.aY1?kNiT8c;BL9=VNI4Ib[JWLq<Fa@
%kGL7O8!eFcaV4?jHcRK(6b8u1>@p"TU$h=bH>Ge90.WOQ4ET=U.uref_nNkYV%7;clq97]]1"cFGBp,*e\'GhGr7]k?3=7>?(G6F
%6McW3j#0Q^otj(:c+W]<EMAIL>^=k6XWG["Rq!>Vrn!JXsPI*5CX+'*FmiO]>[/8n_q0`!#,)>1(*^'>[a%npHC\Nh#D+W11
%qO,(r(q9/;n*[fR*64<Jh^;n4Td]@uDrbWo]e=["]l`G/rqZd`8tsINce6s;SVa9l_*hlZ*>/pO)"%'Y%u#K5Hc^cO)5/M-I4P7l
%\bVTkU-$MqF]\A.#qOdZ&FY&(9]+P%G4Xle&)I5l"GL6Mc0>Q]S,H-H%>6fqjm;dudnf*))&1*:YJ..9`$'(7&.]SlLV/*ECkhrF
%\F4hu?]$mc(oP3edO])'SrCBLo$aJ[kO"N2Y"\NM;p\<E87.#.hkDoFnsDWjf!.Bpc6)GO`](n%IT<KDJgXC1:B43Eo[WT3?E^_X
%QMr`r^"]/U]+9BIqbL5ld!Z,5QPGS%g$&@AfLc"OeQH)Smqc3sS`d>Whu'?LR!KE<)/4W/@kgR^(/mP%)qiP7WB/@0F&]Z"@HStt
%3TsEg&Lo-1_Fi!qJ+:MOD5fOY\`9l#^G*kp<%uPVZs]7O@f^7g-DqW/4o^/l6^*@J3DG#3gDSl!7'=#OJdZoCQ7(^V;IYsA3H?Yo
%!0_*g:2Y%0FJ;u^3([c@D&D)$'H`@QgS,0_H_5uLm0)7&I$m$I$^V#@O,/0t4U5'V.(lY_1.c+DoRRqlKX+R?p.^)>=2"b&Y0*e<
%/I68,X,I[ipb1RB:S=VS4?K1nD&qL0eR@eI9<@qZi/bZEjDoW;\,'b@^`1J`ScG3AmmV`2n*G%nNbc-nM^UM=5ipuH8dbDf@dS9B
%CT?G=J\58OF_hG.PdrS]=0acbO/.Nk8HJHS"('B4/P.6_j[mGB$/rc+;>Kn.EO;8M/=TcofUBSR:9'[g$8-NWmp.;j_p?k_rBcBr
%.]gr*'m_e,HDB`(m0oe#[6RlA#H/5X#ONB_<+iWMg,H&oYU'EP<#gE9l,o2u%N17*Y/Yae>6kd3WVp-8/(;!q;M/!gET@HX:49;Y
%gI,&^U#ej<?$7W56;q7Ne6Y]nMgD*L6@4?P9d5rc2CEKg=PU9>Q7fIZG1)IRd8\2fk&WrM'VB>N25_0kb''9PqA%drZC!Ro4`c@B
%p6h"Eod0>&C,(E2;7Kmj(/SV032aL:2(9+eX>du^ge2_#%T38A:Hk;_Ul+FSpYEc(gH#T9>^Id3;!oarC"`m?8__1LP.$<tb]1JB
%S.a/31u[6r4m4;[EX'n9*Q74P>C%T\S0pbbm.Vi;k?-gD!K/`-G:>u'k@5SKCM<>V1?Mr2eP5fn/""%*b;?WH0Vq>Cj#tR&(=-5_
%M*OF:DGST1\I5MQjp;>8AF-mN!Jo^JXrb6^C9@Z"s'hJsXZ(6>06m95O3.nm0T2\B_Q5qtJD!K^(pe;!M]L1h"22Jf_NU?;^?o<G
%[<fVNfl-r_Q7Wa3C\/R5^T4NB^tA+qJr<VC_fqq47U3G@7TloQN4RPBN,_6@)!eO%0od*@0g:_t0L;I4=K%8QZ1)9*eV$@39b**#
%1.<bNLb>pMT($2eaJ3-`a#_"8a4Z2]V,IFgfoKDi>s);qXT'VV\WM;j(#iOY<cYEYX?78"=>cYIYr^aA@u]6sK!c(P'mh#jQ]OnM
%<TgT8H/$;m29(E2)jMm2D&mKGHlC1:99@>*)[DtOG+nU93tqcglniFJYcEgY=g7P^B!EDR]Yu$s*>[84<O[573,\7nlpD/P+,)9\
%Ol$0p(1nT0=SRKsAI4.c>t`C%MHCMSp;nL3.ohMYcdJ,$0>4dS\uDF#36fp<b-nsle?=k#V2YRF?#aYb%'%sq1>W&[o5L/*XQff%
%c?eh[N[6jua]tBZenV1UD,pl^?Wu9dERX3VZ7.:i[<X:_TY+<S2nE*9_slHMW3"mY8g.-?VQ..<br%FWCT1O8XgN<$Df&_7:2<Wb
%>-+LIk\QeAW]TO8bcUsalb4PrXVZc&YZZkR\]sB&(H-1r\[:>Sm+THeopnh@%SjH7p5%C4KlGaDPj#U<]hQLInQhltAg!>FFb>[:
%D%s:!D"E$tC"j%L2r!kk%+qiS/IKSO*N_ZRm_(WmG+p\EgY&Xk[7:RaQsISl<I8ah?J>#(W7tY?!XX5.Ec'M,gWdi(?*@B-H8$o'
%O7UK=JPAYA_mu2$*<F4Kkb'q/fF]SF*CjQb\c<U<P&4_Mq:90HW&]6&a*Tg,G)Yo*m9%T"Nao7L"-/l.h/k\19u#.A$auRIUDIm(
%bMfU&arY5/\lbLa9AN_*fj)(JcMh[KGL"mMf=B)$#L;W^&Vnc\)2ht,^g(Q`=FgplU20bn;RLJePm9%l<SLRqY&8G?ED870S80Y6
%XQq?I/T.ICS%+*V\)c[l\X5J8^BE?6PB(Z0!h61$Q'%R(i;q-5=0WYhj$%=*`a`(F?;JAaZ$P&oEq7.IoZ!)hCbnl?E>WuTLKWL:
%o&HlcZg)Zqf>F9AFh8GQ0?G:?hJgUmp*rFWmOe3uXga^:S\68k`U7$pq1/3SWGmBHcS_q!5#P#iHkHCPj&7Vbb#b*<XM/k]7/8/s
%9(hSkS#5)KYAO2dHd(g=6@R<8(/@SRFuqq`)bcUh&%g*!IhSb3rO9a-*q2dmI"6?u[t2Kg#g!4T%9G@4a@0or;7<&'Cbts*d#t(H
%6%>^d%e<OK'9mF7I]s[#;Si*RVB5s'a95;8rYTji`!B,gXW'CJN&3Y</QqV`6>>H>1<&0u5a\dR>rb0)+:@NZ&ZrX27!aOLM>DcJ
%M(p.qLjc;Q+RQ(l8<QiJQ])s7/X_n$D6i70,g#;Pb)rTleOd<:lCMI!pQ1Q+@@\lf;MOHC[KO"`/U6!t/E20g*PBO$3Z?rsFJeH5
%cn1=G9$c5"$[n^b(WO)W>T'6W=dZ!9gR3Alk[l1@"jWWc*ffo<Uc?O2_\B.C_(&Zi<!Vc7le8WlU$^c#?(0jXpJcc$>dBu=EK(gG
%iMPdjLV$7=A<SZ)q+)uXjKl-[oC&r'JR;t%CVa1JO9.GcQ3>=W3!s5>)%hTZ)t85=iX:$4m;aVN=YLE$ilP#D[QK:F[AX/JKPfE0
%+\^'N/Z^AG9=UkkEoC^gis]9bGVar`X@sl_Y?-k7EfuHApoZ]!R_KB.rchY-`]R1DZnPZYU4\1P)a.dM.csB9c`sE(m_oN9:HCCY
%cU/A%*EPa$h!^RgnQ=c*M\hGb;LG;9Pkb&6&e`#i17/V5b*f3>lr[`.gDiY`hqNt:W3[,eBmV!](X9M#G)hlO1U:.V6M\'RZ:FgD
%oqeHa3h@^*5Xl4t4HqC[\X6X8?8M/"S89U^enqF+`PP'c'E\B4f3F3UEL<r05t>af9"`3Hf"7<JXus>)k%q1R=ua#+[)-WXf>beq
%-&I?2d3Rc8qoQ4W!6HL5p.7Gno"*-r>fb&:V0(Ri>"^`=g3L,b[',dTV+%k?SZ[/`EdB;_cPcJ:/7`3GX#EtZ/UNms@i!8!MOdBu
%qVH.$_6QH/_/jbD\bL5-(EJUAg0LlH'Ks!<UU/7X$*98MXX#o6X09VB=peaC0=P%!6rr[hRZ+XNfL<AD0p(#(7AZ'/`K''B/"Y7M
%ZU?E9el"Qc]p/fRrj@^Xk-m+L*A$<Hi3EG?jgg_#='WQS?AVTrY0u6\n]J"2cW#NQ8%k?"<lW[=d=&*)QEI^:fA_or.HDu0(>&Al
%m[5o5FfCQ6e_dEaErB5ND]2;68i6+^=M?M6W9^<bWmZDk;d@Q4c$/#^BqFKB[Df"hlW2F!$F^e?r1r^#g:4T>X,l#K%`uIqUXf9*
%[*6MHCVJ0SIAaYt*FaKfFL'DjhjO2YZ*$/L_7Hq#p"o(rO0KbW$TJ-S?FUObpZgjTY'6g]\NW/cXo0I;54>[7:ONeQf@<1@7<!lN
%CZukNl1Ag@mto;nhmlKO[e.'kMC0=ed<hTe*So/?ni0l'Q,\etUGP/]`g$j;\2F>T8bGDl-AQD`1hD)nos?itcdQ0pj.=X9S=,Y:
%n'Z#&LF[EqFdgO4c.6aGR4"p:78iA=Z0E/W2O=NSUrrT(Z4u(OQJ9[[]V<%+n>]_(F]SB+%OS&g[PURZX'P13I9`SC)o8eMDAnDR
%D(b/7BH'glr3h"uAiiY$(Y^5[e_RB(C72ca,rkY!PN_E^5s0J_EImF!PG4M$jZ"#9P'GB#/6)qmj2$Xb!1=KbN%q5BH=T$G<+'lE
%"F?(!^r%HODYm\Kf?M_[l-GWs^?tTjh!7tTIe>r%T,FZIIGE,d%iZS0l$;EKgDp0hT4h2%2#)\SI&lW'nmrO[SiMbeMY\RhgG#.J
%5Kg!R!&IUlq1El]5&K)Vp7(I2)OML^VRN5j*JF'6o*4M$R^[%UFlts$mW9H958W_`=:2.@Ir&<g\4o6/MiNfVdYp6)UR!Q#3Jr87
%EDGqfTuA(rBUI--PZOWq`Q$J-.VfJ>lH*/"Ne_X\B0L=L9fPino=UWeBe,3NPdRgdiO2.a+7LO"^>gMZac\qT-5"O3k;uZ*.P4Q[
%>TuM\WaP?4Rj`i<F7G)fDV>9t3CU90\G,&a=na3<h<tHH^YB"F`HCF)Z.>DAd'fgYhE%-VN&&F0j"I[j3+:)ib+5\;P66g5WLDd4
%]gQD+ob*rk@-:O;p7(2::?R-9P*\'H'Q;.,fAtkiY8*L!bH&;:hME&*$m!KDaINqUO:LQ3!7Map^ZZcTQ/`!KNgSnjf/Bo!\*eAc
%>,1<kO:g%rmmN^#AXC'*#N:JPV]9$Bi#BNZ@pP:22c$N;YqEj:Bs`Fm/$D"[/ToJE*ZT1_Q#.@AoeTN\CQCRUB5dMkk,OFT*.U*_
%c7IpT!ul.M'&nB"gj,[jE"FRWUT9aCCP[5.S1'du"%\GncF>3T:I?Wm3!)]!nkkb22j:[TBr@#cS[0^)FMum4I0'LPSP$K2>%=(M
%k^+P6BdZ</XZ_=r[^Ul4`E809WhmTkg%$2A.OK#fXPY_Y`\`Ga4_n@1NZnrBo=gh?)&>F",:j*'OMSu'1lZCc0eHj>7@IslLR3GS
%:rc&6aY^6[(@I1$<$/*X(Jt931E$Qqf7cE:k(@/oY:),L1&sqh4BUf1=Wgts$A=kqW/\V=AUq].G8Y)fSR#%0R^2W:o3h9fN^cd5
%`/a0F84jc<`7f6P'hbu,?EB8'1]`a)[B6+NoNc;Cj0sf.FQ7eSf)FfgNSarcWOpU4#A%4AVb!T`n&XmU=i3s>9+Wm%UCIQJ6_[d&
%/TEs-lZ=Q4N$BqEbj*CR%=3D.kofU_9(R2hB$oL)V(3@VJk;[qH?3fCQB:SE'*<ZHpe"$jA&j?m\9>99[ES=;=dgG>BbLAMqlMXY
%gY`PaI1!'oO.s3cKULtC*8U1KXh:]E4Q94MkHb[(F):WfM(XpG3-%T1O<K_17JoL^\nj9tUt=DCL]Y.gq<D[\'oRJhYK?AENt^4:
%<0O5l2N";;kTMBlfkBu6SA"(#7%^J^UE>kRHV/Bq<`G5$q[Ch$8'i"b4qBL]TDAEk2Z[!oC[u-V$\C<2(I#ncSA$!*o'3Mi;<[Xr
%8u141:-HW4SqS.Gi6rE"1pXbQRX*67W@kb)VGZ9N4m,CC#Lf5G*G1Po.dGH("ES45'l3W_,13?c8".Se>C]DcMlYZh7#n;9:`qV#
%)eoDmhZS)-3B?LAOJ?V^HC/ktS2h-2UJA)"4aP=kj\ZA<67:#XQC`VoD--HcJU^/]42;t9iuCKJGH)6.;i&JfbXH1EAP/n7g;&\"
%d6KqshTp^f1#id?mj8#82D6kIWOiC/X3$"ZX%f<<EMAIL>=Mm=%CC2?ABb#:8Z%"cunCc:D,+tDVB:XNhkT"PV@b9uZ)2M27-
%^,f%[%B%iZKMFAD6*?Ge=5]]3Yj,n\EaUd;?H)Hf=Gq6Z^JlH6[^C^YU-J!nOfSO<YZXFSTG+IkHP5B<8CFc"&KqGrSkKIG&d%tE
%#&SgKXld\;r)3f%qD+1tF!BgKQO'3VL870J93m3$dS_Q%K?[hXno:Li5n*$p<HGFNrkR[ulV`6lL'U+^0n^"1WZhe2V\'%5Qgk%u
%%`<Vj/dFM<MZChq#0Bj'5g"lk3TrF[^iW5I%ge+)2utHBT2Kt@14_^KHiouaCe-J.C#^^-,T")A;@<7c!!NrHI+!eGeL(O=-gL6c
%('.tFOF7]rOa<-k2"jljU&b4d^833uh#op#n(YZE:$9l%h3K0n#j$-k#Q\U[B4%dE$H^(&I7Ol!7#:9$lR3DW14"/_-,P5kmNrd1
%57ko^YlZ\Bh\$!=dX(/\S(;X;?WanbrmOJL:Y31=#2,!XSY/Go[6..V?hqdl6(VOD9L*q>EZC)7@C#_`5A8qb0*Y4,E*mtU6F[uA
%/k]kS%kIQa"Q,3R!_4)Q(dm9pG2e5_kU_*m<=``#]gOV\1Q;EMs5(YT0P%VFA;DM4lR%r`ccEs";*gt&P'L6X_]iQG*=t.^b#=&P
%)7aXNH;)eA>^P28DSZhkqRm-7YI8/*#;?M@h=IOXCii#/6h3Qg5BmpS]gi*IQ=Ckb3LF+''Uk@3)+"o/@V*9pJeU1kr%On&[75<M
%YRm;"4ZNKX_73TG!d-$J,MM'Zo-D1t$%&hE:g[emGK3RT#F!o:/<q8:dI(48Z+-1^^0(I4D9jb*fP62PSr8/1_Md!8n<X5?gQ2Uq
%Z*JSso(3pfJomfOH=daopcYO31a!G]&BY>Rk)JR4);LZo3*f,+><#;Tek.dsi@4WjD`\TbAu?GC1/Z9h@:@93-hWA<Fa^7X&e-Kr
%+Os4/`bJ[M'%2"*o<aoUXaHIP!$%1p8YV\b=AQqH/.36QRt*F[%P>4Uc1(DMk.aLYs&Ff7aS]+2r]S.hK@<oc@CRB9J95nr:ktRa
%$<Q#0R/4Z__pEM7)<uNu94B,QcZugbigsah7&=Djs(h#dJ#9E!\dj<Hbq,ko=34(9',ZURN#VgFj%8Fi!DJYLkULhL>2AaYWEp"N
%Ij^^-3"\'T/Goh-m,s*m8qt=:m2NqXITcf@pai8MNT[;G/?u%:]^3`/GS.`&IeXfL[A:?+gT[P/n"_n.*;U,+Kk1RiAsHaS'aG8l
%50?`9QAqdI#L71(it@s,NfP<7q+9XSqJ,D]MhU\GGURG'%2kcGEnW3u>8m^\UQgr*RtMHhq?=Jm8hU0de*2cW39u&VGh@C[#^+&8
%4>WY4%;f;.?B9ZC2r5CBmYGU_,+NLSJ/9cALOFm7?;OM#-DtX`aG?G06lcpPT+R\IfSR1Wj:!S.*H7cHIhlBtDFi2h>61T%!g5Qb
%fiP!LCXPZdpdk?rdW`$.-%Mp5GYm>*Ia;NSeGX5Tg4^RI`!kNLi?l*o<%]I^inN<)9R8*'WXWK``e4.nLm.hb5u&rb(,1iU?`_BB
%\iL`^]O&"n9&6U6Gtl!XLBl(h9s3D\-d[;Hc_r3/E^W'#[B[lXDL2S.<cfa"H<1!eBEWg$Sp5Bf'.UM/pP5>VRMC>8`[SZc3I?KV
%2hkO(hfuG`)<f0t=IDpt+9ojh7EQ(*\COANY:/A,a.i0I\*)TkN6iHjn+qRdm)f6%jWRM?7#T2Vom59dF3a=u#?!e6Y[+HD#tD8/
%P7lBD@":d*Hk<*.ZHc>jpp04QT*^]cd"4Z^:]<XQ`;c9a0Uh!2]ci!J$C\S4\sbRWRa[pDEl9VJ\.:/lC>Q*SA<0f:[5MODVZ_G"
%@.Dn(8->o]T?WS1!U!H1ATnjVL5\BYE<rJZ<JR;/Jd#fhYZ]]N::H&`[/$LID@0K]U8DHRARB\R)D;_sB!<`8P_B@VdH!230_tpi
%7U1eV?atXqAD;b[-$<I6HD#V6Aj6QaPoN'=bu^0@4#'gBfbu%06aPetj^IiSU?8[[[=QlU#Ds4`cVu\Z,uC#H,G!3)=/IDWWf4?!
%HeI&W2VN3N;`1WXF"?,^3G#Uf*ZQ@Mih>eqF1t(("F>2X^QB=j`E8q==:%N*G(25+*E7fhKuj6P+AD?Sp]8=c)A8i_&.??KU]FBT
%*WlPF118>+7A?3sl#&dVYk`*.nE1MSA-JMOU5jlp`t&Aps!^9@!W.k$EOI,.6Y\LkQB%idd4bs]k>$\Wob,(&5CD%7hu]cRl6dc,
%ed3^LWqOMofmAD7q3<d7L]57.9KLjWg)j<fE0]pX/Rc`iGifu`U0j)9387'J"h?o]IgRqJ>M(mM2?\pZU$4P@>HY*Pk@O2l_,H2H
%+$1soihcb:Xmg'Yh'ZM/*)Afc0P>44h1Z%>UBpPG:k7F[igW=]G#U^d$hNZ\AGg?)QXm36i(LCFK"L$eUq"Xt)Womu5(Fc.L/@e'
%gldNjmdf!oeNH:PLh]ZFKZXA?:W^#3D+aU73.S\E4&Gg"Big6J?]F_BR[;tOq;3]s+]t%l4O,Vm0PQh>S%G[ur^8!c>!9?:iK$^=
%)+!Tlhc^+'\%po@QEqAN4)Z<-4t]NH#Q9rMk?WJ`Y'ML[$DL\GNi+t\L]M.!o=?nJ"aiC^Dg47Ad<!7XNqe9=f6UE%RJA0EmF(&c
%]-5A#Q!!=i\mk>5],:np>K548,Xt>ZolTe%.5+`2M4drb(;-.JFPs29g<_^M5MPS;&,N48"]gGGroci4mHRA5U73'?JN2!0/4$nP
%5)h%2Q6!hrcOB32X&he0Hfufr%L'+>:MF;"&06R#3]omH\%SpVFZI5ZJu"4WAtOU4&U1H;l^6+2Z+&*@GaNn?jWcWLmeKPi+:\r?
%F$P^)^(Ak.BBq''&7i!E,Sel'oe/GP.<8EY7[]/aa=ZNVp^Z.nH!*'MPNo"'DcT<'[q],3PKCGT.Qp<H*1mg&Y%#p2B>f5@F4)8d
%"TD<oQ.LJ,fcQa_`VKnRI=e_*d!mI31ujQ,3R#;k<@-D#;4?fI-Htt2\-'1hZl(<Mn_7auU`AKcJ+'F$<cr(3ro.#-l';d]1q(OX
%d^P$D:TskOimJ/I-N\0Pm_(pJ5$[]&"[6!#[$Ff^>9B1-TBGb@*73-hYA/geaqT.%Ip3##9Z't2Z72iqq[)FY!eif8iA'6RA@>OX
%)s"hO=K223a?][=%pTl0M8<O++d]:-jr=4o=b/Z6bgtkB)kB2+NKY=\`)F,oR:99C_O:>a%LVR'#=,77:s/n>M0)Wj=VPZ(Pb<Lo
%2,u!Eqqu-O%i7tp!I#s\V)i(fAhTX%T@oaqJm)JOS%R>WQgRVY72U:V[T*iGZ/uaG?Wr3Um1sOJEFgduH!G7I]npO*$WP;g''me*
%8Um/[I[St0iNrg<bK_hVf4mfDAp<0?KC_48SFqYEcO2%S2_#T64k7l!#Q7.?Z%AO/CgO1l;h*Wbcm*J/<fmd4jW.)4;E/Gj\r%gq
%15SOo%_RJZXqf11&e>&:?\,BAq5g&/%78kJ-s$%-8$o=M%d8=lC&+c>>V6Ka2Qk9.UCW+?l2?SIqqrX.0OR*HP,`]=aR+;Kg7S'r
%&u`%s8rm/4Mn<O$o*s4P+\=_`rUK;2pI.I6SZ<3;HPj>gb(=%R&`9_&@UgdHqM\t);A_g+:FX=IF;[sA@]Tg.7M;4[6*Er^:3nZs
%SK8usUTO!7DC(%7dn),ZN]\V%lR-GhepD29bfa)_Wo%kj;hljpa#@kf].MDH$>AJ<#\)%,bFT!5$?DZ:VBt`taS5r_k\gN"eQq0`
%]QiQi-@BM#m#sM*T#W;/gEeLt)e,)shgc-iG&uk(Sq;)USZ=om-YsF]*#SLSq6UTZcN[!ffE=e+&\j-HUi80(8VH.N3?pE#ZO0Gm
%cB+<agHh'aQ$?CH4\n$5:@r5ne7TXD*Do,:.S[ij2Dp(H;&]jpHs,p)/aB,pl\%dQ_cdAHCCh26_b0=*Q?BFen[H75L3kgp^"?KR
%P=."#Fh^AJ\ZatE3apj'P]s`@gcf$;IbskN,JOoe^?o5!\+t_G2_"d-$*=!;"c/T`%suHZikVg2/he54Lf]0=7%S(:*83efkA_.N
%Y-H4Pf@oWM[.Xq=W[oe:;_T_>jed86k:X*T*hf8!mQ6fUs0C9hiD,,+S_Pc81Zc/bHG5@-6Md5X!BW/-X.&oTm^nQoQG!e#L=5Al
%GmLLXf040ra%@HVm#fX>\hMcIYW6T1cp0eYLXX4AKakHT@ka@>#%lBA0PLarB>fc<^49@N)c5bp9k.a%f%u/uX8YS\)h%6o'dF."
%C>LH@#P@kC0@_]?,R2]fE8>N@$$EXc',JhK?n=aa`B[d[9aIuuqEh%SA]<8F.FFW<+5[VDd:I#Rg4*+VMu!1l_r&*,M5lt@?&CMr
%c@gnp:m!MXfHGWH'>:[H#*6)[jZj==2pd(M7td_a5Ym-cU#A)(/8)g"O%<g]<F(6\hSt6;?'Z1AJEfNpcU(31I<'[iIFK55)(u;)
%CZj+83;]\L%"P,sjLrL`:W3G(*'DMOqLap\cDqsHBDZ<_&@^3UBKpndQ1]JFUu,0-HcfDCfd35ncZaO=UP'Yl!8U&9U06V]j6g7K
%m]LLLO>\-m\lI*!m>!"t."i)^D$]i#(<G:g$[*aI'^\W8NTbFZA)h6t`ZDd)p!+,'j5(3an>BP:.]gH"Be]B:lo5!<Lu0o&+BiYM
%G,Q0!9q)^13e3Ibi:m0FhK7/NL(k6?cm:]pU^VK5.2'q>kTFr5U*0sA%W$0#)S_UfAF;#*\V@K[De\*SU<0<VFF5_K^+5H,9O0'1
%NR3E3)7(4UhO^p&lW.LLpn3X:U%@qh&j#t2S/E"qL;lRu9)U!3=NZ^TD-6H5mm@D*<BX1;-8#9d)Q_5,2IW^%Uc('$HSd&;8&p(T
%:pW<QFc)F]PLZ-;o/r0/C[X(TksNi0#>KZ/-*kn52^/&>K>9$!3NnIXWsnY6L+9AAQG/>FQ.a"o$@`--7[In32K#U.s84Sj[3'1X
%gd+T.`@Utho_2;6eDpJl;6f+.f_a(c-7=?,_ZMpBUSE91r+bq'h=+WJq8<1c=e6/9R<&(8YFf"jWK+f]Q!F=EBnbHi6i(fli%[ba
%N3>S+4C1'b=-#s/?^ImBREpB@rla#MNsQkm"k%o$^i]7qiOo!HBeFZPrC;<;V_jE8s1.1YcWL3=</,YE)5WGUMi<,YZu;c<?V0Hq
%qBYlr@=4mfF<mhF55``FXQ%49`Q@p0*aNi^s3)P[)Z<1gCf3uNof11lB9&ZXs%A*o@)"RS5>dGgfM>?^W&-VLF>[Dg.an5Vop>jB
%c]7TXgom`Er6C%H37X8l(W%pVkl3qCj=`m74p:@[099p%M8joVr<8rU$jq)GdIPU@?[eLToQ"\c?8<Nl9`M%:B[Qk:-:sDg@Ca-K
%iSrbJP\L*dZRJuJ@ZUQb9o6E!6A3GjIMs"n)o,^6jSpl"TW)#OG9ussn]\V&^F@G(E>d=a=+gFm+$N@E^DDOZ$bG9[3^ZCLmmjGG
%DfQjTUAAGUXKZ<>Du4$bUjG`$o8+>1o=H^X+u@[qPi%R29`YFKZ$\om/Bu0"^*Q[jNM3$ep=BXNllXO,?Q.]YAA4M;9a,RPWfh4*
%#K'KF=/Li4B:0oep`Qr9#4kjcAfB)VFff'tdgc1uYq5h8!bCp/.6k7D5(84n5<E^7!S@,L4O&9S'#3Pe>WR74V>UF!SW(!1V=Hi5
%=3dRjT$L]\bK%8&INMfL<DfLsg2qST(Z-7J$M`q,>\8m)LVSBK#F)n!U+Y/d9T%TlrP7q6Fsj0E#[/lf&$(Uo%r\5p_p%QOaoS'h
%nX5DQqRdsV5DT?O*iSu#j.Pkg'n4,6#>OEo@KaLU,S7BX.*)2]0B,_+*s_h`^RXd_OSg'>0]7\:Xh!uZJ%;$2dH\:_i0=*j[n#2*
%J>\Guaq.o$jGjMZ2-(N<@/Q)+]"`?3!JG+=eC2QEkj?[;s"YMP7c,*&#CrBA;`sQqhgaU',5fpdkI9J`i4.a55E6[o=)[)DpX?WH
%g`cFnK)\3`DXaA+rWH>XTQ%\DJ0c'6r_)c<9sNW.j9Z<`2/$E<Ni1h>8MI4G/ndL+.ZeSj-r7QK;$&-$BLJs3`2eq0&GcpMbCHWN
%B`$s%`4>eMVh`#]rrrua5TC1`U.bo4IYo6M*8F!8l)cp?d\^&si<)&0n9Pa@$T)PV!X-CX9f**@Nt^.^LDWlq,!b*DpI7tqJA]35
%5DA\0JHB<`:%4034X_YmdhK`Q1BE@6ZS"PM!FDFT2a8`F5X:D3J-b#**]++hd2V9L6^-H-&0PZ9JJSBX!+\JOJ14*@/N=RL6gNOT
%!F0#(d)rToJtmTJ14j.H=,mQJ/-Ql&W;oN)1r6o!'r[#e!6F[UBf'fli0+<YBMQpD^khWs*$2nH##V-\1B<F;P3#h7K*RA+N4FcM
%_r>r-n+pj3JA=JG5YWek"W0JfbL"&!+ajC5'`:Vg*=4E#?*7F_U)5!d^]q^8Pcb<8.aX<3nmYEsTQMB&IB3K(6\3cl.06p`_*Uir
%CGQn5ZlY)[29B4ZeFr@Gk(!EmJMnV.(lm]<B^VBUJA0Cu=e:3<%fR(l%k_g7,gc%#Sc^0,(BhQpD_=&m&T@In\89&IfdMnG%H#A?
%pL;BkIZ2htCukc#_"+@'B[ATRY^-sU&jSJd&19maI452-0sY[c$j/>E>&bU.0>pEO!=Cr@XYeHa6'4G#XPl[bcClm&i'JQ/V#V7p
%3i7FCEs*+7'bCN+oFR,HFd8-*A-CQ'#<2d?VBrIm^,1o]72jGo:aGN_RK-foTt(IDi!H\UPJEn0e/.l`O#(GlTTu8Y:2e*3+C!JD
%cp/MBM#a:b+?JEj,kP2:!(p^7h7RD>\/[8GSd/!r^jptA1o6ZpM[U=[.S&O?5Cda9/:A$@36afU@*#G;KFH3.C1>jl:3^\4S<2hl
%mKF.U6K32S*Y*duFk0D'7?MdaU,`uH6IFi?+1R(>JfZm*#?0*0'0]W'^cZE)V313S?u2nEI2^JQ@2+=9,tZn]I1Y:TrpEEM)h8jI
%d703#]E@89@(6>f&$RV#@djdk@-Ak$oYC^H+CLdi2<QXZ?:Gu+]ICFdJE.;jTGJ__j:sMU<Y-&>W691r+:&2**jd107<k>4Oo9oT
%2f"A."qm#?hZ6brZna2Jo'e&belu8e3&tLX3.p's@T>@&UWLk4a,u;u&F,G-YqZ8E=?oRp`sKaR_(m9F#0I$o%+54eUE'/1(75j?
%0H$:J9Nj<]!cK:e%aeZ%SCT%n(5MK$N;FmGkY3>sA/S&opJr,j@1u(^/BVZ&0U%l`(reU&0EVC8ObE-9?kaTsl16D/""q[tbG?Z*
%Go0q=cm#.;OZ8EO7^In^i5>U#Mrm3X5Q_Z.:I&2Wd"*o`<Ji,2?SEL?qFna/"<==:'@5$OO<L;n,)l^##Rh%@*131'O9&\3'$(^i
%(mdK3!$_IGj#%0R1^i(F"UZr#YsrQiJY_1r7<0Y`=<pI*oD`X&L'^3W.@SdI"@gGs0qC#B#dn]C4q`H$i:JH6N^VfbG_m)!VT)Gc
%#9:i&63(1CO>37XKF_%)G&Z945d4d%=r/j."8V?PXa5d\>!FhM5eQJ+!+.E<oJs8WpKr&6(?B8[^].d#>jf(%oC'G7P*+OO*$;#c
%IfD?dG!#VI!\E]S;?eD^M[NXk]V.+@U[&=hZ$q>'6<t,bg"U(`p]n7C:6,lm),\o_rp0Rajc',b~>
%AI9_PrivateDataEnd
