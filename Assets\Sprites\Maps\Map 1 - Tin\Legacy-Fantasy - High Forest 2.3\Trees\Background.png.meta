fileFormatVersion: 2
guid: cfbdcec996249c44d969e5292d6f7cbc
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -1955448553306655712
    second: Background_0
  - first:
      213: 7014286043975846214
    second: Background_1
  - first:
      213: -9078219959484023005
    second: Background_2
  - first:
      213: -4817409605821441833
    second: Background_3
  - first:
      213: -2146368423942107817
    second: Background_4
  - first:
      213: -3958678274390686719
    second: Background_5
  - first:
      213: -4821837225436941063
    second: Background_6
  - first:
      213: 7028156775451301730
    second: Background_7
  - first:
      213: -6975036532566498788
    second: Background_8
  - first:
      213: 5657654767279156548
    second: Background_9
  - first:
      213: 3751706319272519800
    second: Background_10
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Background_0
      rect:
        serializedVersion: 2
        x: 81
        y: 220
        width: 16
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 024fa93b6e9dcd4e0800000000000000
      internalID: -1955448553306655712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_1
      rect:
        serializedVersion: 2
        x: 90
        y: 234
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64909246effb75160800000000000000
      internalID: 7014286043975846214
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_2
      rect:
        serializedVersion: 2
        x: 111
        y: 0
        width: 98
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32ff71a9b0fa30280800000000000000
      internalID: -9078219959484023005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_3
      rect:
        serializedVersion: 2
        x: 223
        y: 0
        width: 129
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d8557bbc7f152db0800000000000000
      internalID: -4817409605821441833
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_4
      rect:
        serializedVersion: 2
        x: 433
        y: 220
        width: 16
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75dc0d55a419632e0800000000000000
      internalID: -2146368423942107817
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_5
      rect:
        serializedVersion: 2
        x: 442
        y: 234
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10403ea5513ff09c0800000000000000
      internalID: -3958678274390686719
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_6
      rect:
        serializedVersion: 2
        x: 463
        y: 0
        width: 98
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9f473153794651db0800000000000000
      internalID: -4821837225436941063
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_7
      rect:
        serializedVersion: 2
        x: 575
        y: 0
        width: 129
        height: 256
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 267978ab957098160800000000000000
      internalID: 7028156775451301730
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_8
      rect:
        serializedVersion: 2
        x: 703
        y: 0
        width: 193
        height: 241
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c12600d9a31b33f90800000000000000
      internalID: -6975036532566498788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_9
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 97
        height: 222
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44d28790907048e40800000000000000
      internalID: 5657654767279156548
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Background_10
      rect:
        serializedVersion: 2
        x: 351
        y: 0
        width: 98
        height: 222
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 878a3dd150db01430800000000000000
      internalID: 3751706319272519800
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Background_0: -1955448553306655712
      Background_1: 7014286043975846214
      Background_10: 3751706319272519800
      Background_2: -9078219959484023005
      Background_3: -4817409605821441833
      Background_4: -2146368423942107817
      Background_5: -3958678274390686719
      Background_6: -4821837225436941063
      Background_7: 7028156775451301730
      Background_8: -6975036532566498788
      Background_9: 5657654767279156548
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
