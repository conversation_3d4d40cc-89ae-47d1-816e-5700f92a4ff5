fileFormatVersion: 2
guid: 5f7d10108f126364db512ec99ea93ab2
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4615772414332940735
    second: Magic trap - Level 2_0
  - first:
      213: -8526215022502343275
    second: Magic trap - Level 2_1
  - first:
      213: -6933929110066034880
    second: Magic trap - Level 2_2
  - first:
      213: -148036951091607388
    second: Magic trap - Level 2_3
  - first:
      213: -8317348333135782738
    second: Magic trap - Level 2_4
  - first:
      213: -8883375426856720628
    second: Magic trap - Level 2_5
  - first:
      213: -1713082267525635840
    second: Magic trap - Level 2_6
  - first:
      213: -6592293574734175383
    second: Magic trap - Level 2_7
  - first:
      213: -3247824501882131758
    second: Magic trap - Level 2_8
  - first:
      213: -5026529460058530567
    second: Magic trap - Level 2_9
  - first:
      213: 6553214053355223332
    second: Magic trap - Level 2_10
  - first:
      213: 2958388354594463414
    second: Magic trap - Level 2_11
  - first:
      213: -3544881739634978638
    second: Magic trap - Level 2_12
  - first:
      213: 5045758606304097455
    second: Magic trap - Level 2_13
  - first:
      213: -2144241326479535748
    second: Magic trap - Level 2_14
  - first:
      213: -2236518454185592181
    second: Magic trap - Level 2_15
  - first:
      213: -1002634046617640178
    second: Magic trap - Level 2_16
  - first:
      213: 211966665360012712
    second: Magic trap - Level 2_17
  - first:
      213: -2543072127167447490
    second: Magic trap - Level 2_18
  - first:
      213: 5505593147857255728
    second: Magic trap - Level 2_19
  - first:
      213: -1733603865874560054
    second: Magic trap - Level 2_20
  - first:
      213: 882997122541250539
    second: Magic trap - Level 2_21
  - first:
      213: -894776976145011285
    second: Magic trap - Level 2_22
  - first:
      213: 5346582700647527412
    second: Magic trap - Level 2_23
  - first:
      213: -5593937202035292274
    second: Magic trap - Level 2_24
  - first:
      213: 8524074484563919638
    second: Magic trap - Level 2_25
  - first:
      213: -1113433493904599843
    second: Magic trap - Level 2_26
  - first:
      213: -5363864778654112087
    second: Magic trap - Level 2_27
  - first:
      213: -2727923851802706809
    second: Magic trap - Level 2_28
  - first:
      213: -1762480757720707167
    second: Magic trap - Level 2_29
  - first:
      213: -7492447177611544347
    second: Magic trap - Level 2_30
  - first:
      213: -4957503204401226968
    second: Magic trap - Level 2_31
  - first:
      213: 8000661171958134151
    second: Magic trap - Level 2_32
  - first:
      213: -8562190759501430096
    second: Magic trap - Level 2_33
  - first:
      213: 8322861909486460167
    second: Magic trap - Level 2_34
  - first:
      213: 88495174543662048
    second: Magic trap - Level 2_35
  - first:
      213: -88242145662637924
    second: Magic trap - Level 2_36
  - first:
      213: -1786011144555368915
    second: Magic trap - Level 2_37
  - first:
      213: -6450195013218806124
    second: Magic trap - Level 2_38
  - first:
      213: 3289610949693930094
    second: Magic trap - Level 2_39
  - first:
      213: -3252651963639508251
    second: Magic trap - Level 2_40
  - first:
      213: -1362110010153392030
    second: Magic trap - Level 2_41
  - first:
      213: -3328476255065397437
    second: Magic trap - Level 2_42
  - first:
      213: 7667390173623535600
    second: Magic trap - Level 2_43
  - first:
      213: -6773474938990426114
    second: Magic trap - Level 2_44
  - first:
      213: 7611014625449062856
    second: Magic trap - Level 2_45
  - first:
      213: 6708275803933646548
    second: Magic trap - Level 2_46
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Magic trap - Level 2_0
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 1458
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fb1bc503e848e0040800000000000000
      internalID: 4615772414332940735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_1
      rect:
        serializedVersion: 2
        x: 1485
        y: 5
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59d06216c9ccca980800000000000000
      internalID: -8526215022502343275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_2
      rect:
        serializedVersion: 2
        x: 1489
        y: 0
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04706c0463cb5cf90800000000000000
      internalID: -6933929110066034880
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_3
      rect:
        serializedVersion: 2
        x: 1567
        y: 0
        width: 19
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a88d846d2112fdf0800000000000000
      internalID: -148036951091607388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_4
      rect:
        serializedVersion: 2
        x: 1581
        y: 6
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eac114a12c7d29c80800000000000000
      internalID: -8317348333135782738
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_5
      rect:
        serializedVersion: 2
        x: 1585
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0765cb8d19e7b480800000000000000
      internalID: -8883375426856720628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_6
      rect:
        serializedVersion: 2
        x: 1674
        y: 0
        width: 12
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0012d1501c8e938e0800000000000000
      internalID: -1713082267525635840
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_7
      rect:
        serializedVersion: 2
        x: 1753
        y: 4
        width: 19
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 96321e6dbe77384a0800000000000000
      internalID: -6592293574734175383
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_8
      rect:
        serializedVersion: 2
        x: 1770
        y: 0
        width: 9
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2da6180c2d86de2d0800000000000000
      internalID: -3247824501882131758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_9
      rect:
        serializedVersion: 2
        x: 1771
        y: 12
        width: 7
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fcdc28761e2e3ab0800000000000000
      internalID: -5026529460058530567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_10
      rect:
        serializedVersion: 2
        x: 1777
        y: 4
        width: 21
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 429f55b0671b1fa50800000000000000
      internalID: 6553214053355223332
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_11
      rect:
        serializedVersion: 2
        x: 1827
        y: 4
        width: 41
        height: 49
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6be5147c88e4e0920800000000000000
      internalID: 2958388354594463414
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_12
      rect:
        serializedVersion: 2
        x: 1829
        y: 9
        width: 33
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b0d942e6dc0ecec0800000000000000
      internalID: -3544881739634978638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_13
      rect:
        serializedVersion: 2
        x: 1866
        y: 0
        width: 9
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa4302708b2260640800000000000000
      internalID: 5045758606304097455
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_14
      rect:
        serializedVersion: 2
        x: 1867
        y: 12
        width: 30
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c71997ecfdf1e32e0800000000000000
      internalID: -2144241326479535748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_15
      rect:
        serializedVersion: 2
        x: 1873
        y: 4
        width: 44
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8a79a4c05a46f0e0800000000000000
      internalID: -2236518454185592181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_16
      rect:
        serializedVersion: 2
        x: 1965
        y: 4
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e03a4a395ade512f0800000000000000
      internalID: -1002634046617640178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_17
      rect:
        serializedVersion: 2
        x: 2061
        y: 4
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a5f1012f8e01f200800000000000000
      internalID: 211966665360012712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_18
      rect:
        serializedVersion: 2
        x: 2115
        y: 4
        width: 41
        height: 49
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3ebcf5cf5135bcd0800000000000000
      internalID: -2543072127167447490
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_19
      rect:
        serializedVersion: 2
        x: 2117
        y: 9
        width: 33
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03d3bdf4ccbc76c40800000000000000
      internalID: 5505593147857255728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_20
      rect:
        serializedVersion: 2
        x: 2154
        y: 0
        width: 9
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: acb8d41187001f7e0800000000000000
      internalID: -1733603865874560054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_21
      rect:
        serializedVersion: 2
        x: 2155
        y: 12
        width: 30
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be763c26439014c00800000000000000
      internalID: 882997122541250539
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_22
      rect:
        serializedVersion: 2
        x: 2161
        y: 4
        width: 44
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: badada7b41d1593f0800000000000000
      internalID: -894776976145011285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_23
      rect:
        serializedVersion: 2
        x: 2211
        y: 4
        width: 41
        height: 49
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f3385a83a0e23a40800000000000000
      internalID: 5346582700647527412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_24
      rect:
        serializedVersion: 2
        x: 2213
        y: 9
        width: 33
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8708d0b0c75e52b0800000000000000
      internalID: -5593937202035292274
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_25
      rect:
        serializedVersion: 2
        x: 2250
        y: 0
        width: 9
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 613476bc4989b4670800000000000000
      internalID: 8524074484563919638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_26
      rect:
        serializedVersion: 2
        x: 2251
        y: 12
        width: 30
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd45bc2f22a4c80f0800000000000000
      internalID: -1113433493904599843
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_27
      rect:
        serializedVersion: 2
        x: 2257
        y: 4
        width: 44
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a6a71d1769bf85b0800000000000000
      internalID: -5363864778654112087
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_28
      rect:
        serializedVersion: 2
        x: 2349
        y: 4
        width: 6
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 78cfbfeb5b7742ad0800000000000000
      internalID: -2727923851802706809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_29
      rect:
        serializedVersion: 2
        x: 1663
        y: 0
        width: 13
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1af8890c6196a87e0800000000000000
      internalID: -1762480757720707167
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_30
      rect:
        serializedVersion: 2
        x: 1684
        y: 0
        width: 9
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e8f5181dfa750890800000000000000
      internalID: -7492447177611544347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_31
      rect:
        serializedVersion: 2
        x: 1691
        y: 0
        width: 6
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82bddc00a19633bb0800000000000000
      internalID: -4957503204401226968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_32
      rect:
        serializedVersion: 2
        x: 1759
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 78533d252fe080f60800000000000000
      internalID: 8000661171958134151
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_33
      rect:
        serializedVersion: 2
        x: 1777
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b209c06edcfc2980800000000000000
      internalID: -8562190759501430096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_34
      rect:
        serializedVersion: 2
        x: 1855
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70dd19d6fceb08370800000000000000
      internalID: 8322861909486460167
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_35
      rect:
        serializedVersion: 2
        x: 1873
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ebd20374e56a3100800000000000000
      internalID: 88495174543662048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_36
      rect:
        serializedVersion: 2
        x: 1951
        y: 0
        width: 19
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c986f8d6c3086cef0800000000000000
      internalID: -88242145662637924
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_37
      rect:
        serializedVersion: 2
        x: 1969
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2e63e3a350d637e0800000000000000
      internalID: -1786011144555368915
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_38
      rect:
        serializedVersion: 2
        x: 2047
        y: 0
        width: 19
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 496d8116fcd4c76a0800000000000000
      internalID: -6450195013218806124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_39
      rect:
        serializedVersion: 2
        x: 2065
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6afdb9cabb07ad20800000000000000
      internalID: 3289610949693930094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_40
      rect:
        serializedVersion: 2
        x: 2143
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e25a49b5424cd2d0800000000000000
      internalID: -3252651963639508251
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_41
      rect:
        serializedVersion: 2
        x: 2161
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 268adcb9920d81de0800000000000000
      internalID: -1362110010153392030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_42
      rect:
        serializedVersion: 2
        x: 2239
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 34721852b70eec1d0800000000000000
      internalID: -3328476255065397437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_43
      rect:
        serializedVersion: 2
        x: 2257
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f7c6254dba086a60800000000000000
      internalID: 7667390173623535600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_44
      rect:
        serializedVersion: 2
        x: 2335
        y: 0
        width: 19
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef7077e2e68cff1a0800000000000000
      internalID: -6773474938990426114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_45
      rect:
        serializedVersion: 2
        x: 2353
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c1b13a1a71cf9960800000000000000
      internalID: 7611014625449062856
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 2_46
      rect:
        serializedVersion: 2
        x: 2431
        y: 0
        width: 34
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4de10dc3d45981d50800000000000000
      internalID: 6708275803933646548
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Magic trap - Level 2_0: 4615772414332940735
      Magic trap - Level 2_1: -8526215022502343275
      Magic trap - Level 2_10: 6553214053355223332
      Magic trap - Level 2_11: 2958388354594463414
      Magic trap - Level 2_12: -3544881739634978638
      Magic trap - Level 2_13: 5045758606304097455
      Magic trap - Level 2_14: -2144241326479535748
      Magic trap - Level 2_15: -2236518454185592181
      Magic trap - Level 2_16: -1002634046617640178
      Magic trap - Level 2_17: 211966665360012712
      Magic trap - Level 2_18: -2543072127167447490
      Magic trap - Level 2_19: 5505593147857255728
      Magic trap - Level 2_2: -6933929110066034880
      Magic trap - Level 2_20: -1733603865874560054
      Magic trap - Level 2_21: 882997122541250539
      Magic trap - Level 2_22: -894776976145011285
      Magic trap - Level 2_23: 5346582700647527412
      Magic trap - Level 2_24: -5593937202035292274
      Magic trap - Level 2_25: 8524074484563919638
      Magic trap - Level 2_26: -1113433493904599843
      Magic trap - Level 2_27: -5363864778654112087
      Magic trap - Level 2_28: -2727923851802706809
      Magic trap - Level 2_29: -1762480757720707167
      Magic trap - Level 2_3: -148036951091607388
      Magic trap - Level 2_30: -7492447177611544347
      Magic trap - Level 2_31: -4957503204401226968
      Magic trap - Level 2_32: 8000661171958134151
      Magic trap - Level 2_33: -8562190759501430096
      Magic trap - Level 2_34: 8322861909486460167
      Magic trap - Level 2_35: 88495174543662048
      Magic trap - Level 2_36: -88242145662637924
      Magic trap - Level 2_37: -1786011144555368915
      Magic trap - Level 2_38: -6450195013218806124
      Magic trap - Level 2_39: 3289610949693930094
      Magic trap - Level 2_4: -8317348333135782738
      Magic trap - Level 2_40: -3252651963639508251
      Magic trap - Level 2_41: -1362110010153392030
      Magic trap - Level 2_42: -3328476255065397437
      Magic trap - Level 2_43: 7667390173623535600
      Magic trap - Level 2_44: -6773474938990426114
      Magic trap - Level 2_45: 7611014625449062856
      Magic trap - Level 2_46: 6708275803933646548
      Magic trap - Level 2_5: -8883375426856720628
      Magic trap - Level 2_6: -1713082267525635840
      Magic trap - Level 2_7: -6592293574734175383
      Magic trap - Level 2_8: -3247824501882131758
      Magic trap - Level 2_9: -5026529460058530567
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
