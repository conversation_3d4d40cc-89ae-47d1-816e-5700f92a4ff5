fileFormatVersion: 2
guid: 1291332cf12f072409f17b4e2f19c4be
AssetOrigin:
  serializedVersion: 1
  productId: 61672
  packageName: 2D Platfrom Tile Set - Cave
  packageVersion: 1.2
  assetPath: Assets/Cave Platformer Tileset/Enviroment sprite sheets/bg_cave_blueish_16x16.png
  uploadId: 154679
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: bg_cave_blueish_16x16_0
  - first:
      213: 21300002
    second: bg_cave_blueish_16x16_1
  - first:
      213: 21300004
    second: bg_cave_blueish_16x16_2
  - first:
      213: 21300006
    second: bg_cave_blueish_16x16_3
  - first:
      213: 21300008
    second: bg_cave_blueish_16x16_4
  - first:
      213: 21300010
    second: bg_cave_blueish_16x16_5
  - first:
      213: 21300012
    second: bg_cave_blueish_16x16_6
  - first:
      213: 21300014
    second: bg_cave_blueish_16x16_7
  - first:
      213: 21300016
    second: bg_cave_blueish_16x16_8
  - first:
      213: 21300018
    second: bg_cave_blueish_16x16_9
  - first:
      213: 21300020
    second: bg_cave_blueish_16x16_10
  - first:
      213: 21300022
    second: bg_cave_blueish_16x16_11
  - first:
      213: 21300024
    second: bg_cave_blueish_16x16_12
  - first:
      213: 21300026
    second: bg_cave_blueish_16x16_13
  - first:
      213: 21300028
    second: bg_cave_blueish_16x16_14
  - first:
      213: 21300030
    second: bg_cave_blueish_16x16_15
  - first:
      213: 21300032
    second: bg_cave_blueish_16x16_16
  - first:
      213: 21300034
    second: bg_cave_blueish_16x16_17
  - first:
      213: 21300036
    second: bg_cave_blueish_16x16_18
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 16
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_0
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_1
      rect:
        serializedVersion: 2
        x: 16
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_2
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_3
      rect:
        serializedVersion: 2
        x: 0
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_4
      rect:
        serializedVersion: 2
        x: 16
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_5
      rect:
        serializedVersion: 2
        x: 32
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_6
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_7
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_8
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_9
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_10
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_11
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_12
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_13
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_14
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_15
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_16
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_17
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: bg_cave_blueish_16x16_18
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      bg_cave_blueish_16x16_0: 21300000
      bg_cave_blueish_16x16_1: 21300002
      bg_cave_blueish_16x16_10: 21300020
      bg_cave_blueish_16x16_11: 21300022
      bg_cave_blueish_16x16_12: 21300024
      bg_cave_blueish_16x16_13: 21300026
      bg_cave_blueish_16x16_14: 21300028
      bg_cave_blueish_16x16_15: 21300030
      bg_cave_blueish_16x16_16: 21300032
      bg_cave_blueish_16x16_17: 21300034
      bg_cave_blueish_16x16_18: 21300036
      bg_cave_blueish_16x16_2: 21300004
      bg_cave_blueish_16x16_3: 21300006
      bg_cave_blueish_16x16_4: 21300008
      bg_cave_blueish_16x16_5: 21300010
      bg_cave_blueish_16x16_6: 21300012
      bg_cave_blueish_16x16_7: 21300014
      bg_cave_blueish_16x16_8: 21300016
      bg_cave_blueish_16x16_9: 21300018
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
