%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_01_Right Arm.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 3 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:54:48+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:54:48+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:54:48+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FWK/mpN5sh/L7Wp&#xA;fKSu/mBYR9SWJectDIolMa0NXEXPjTetKYq+DtW82edLyaSPV9Y1G4mRisiXdzO7Ky7EESMSCKYE&#xA;qmi+RvNOuQ+vpdmLmNq0b1oUJI6ijupr7dckIE8nC1HaODCanKj7j+pMB+Wn5kWh5x6TcxltiYnQ&#xA;mnXfg9cPhy7modsaU/xj7XXGqfm15ejE019rmlxFqCb17uFCzDpzDBakDxwGJHNysOrxZDUJRkfI&#xA;vef+cWvzS86eY9Y1Ly7r13LqlrbWZvLe9uKvLGyypGY2l6sHElRyJPw7YHIfR2KHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXzX/zlhD+Vywo0xMXnxlR4BZopLw1&#xA;A/03dRx4g8D9vp1WuKXj35PXN2uqXtupb6qYRK3gJFcBPkSC33ZbiO7ou3YR4In+K/se+6HeXdxA&#xA;ROpISnCY/tD+OZILxOeAB2YP+dPk3zJ5itNNk0dPrK2bSetZhlRiZAvGQcioPHiR177d8rzQJ5O5&#xA;7A12HBKQybcVb/oeT/8AKpvzD/6s0n/IyH/mvKPCl3PTfy1pf54+39Tv+VTfmH/1ZpP+RkP/ADXj&#xA;4Uu5f5a0v88fb+pUXyn+aunfFBZ6nCVHEG3aQkBew9Jj4bYPDl3Nke1dMf8AKRREfnj86tD+1q+v&#xA;Wip+xcyXRQf7Gaqjr4ZExIcmGpxT+mUT7iGQaP8A85R/m9p5X19Qt9TRT9i8to6EeBaAQP8A8Ngb&#xA;3oXl/wD5zL3VPMXl3b9u40+b9UMw/wCZuFaes+Uvz6/K7zOyQ2espZ3j7Czvx9WkqegDP+7YnwRz&#xA;ih6CCCKjpirsVdirsVdirsVdirsVUnu7RJRC80aytQLGWAY16UBNd8VVcVdirsVdirsVdirsVdir&#xA;sVdir4J/Piz1K1/N7zH+kw5aa69aFiT8Vs6gw8WI6COi+1KdsCWb+RG8sppkUuhoDDyVrlHNZeY6&#xA;rLXv+HhmVCq2eJ7S8YzIy8+nd8HqlrdQXMQkhYFe47j2Iy552cDE0VbCxUpbmCIj1ZBHXoWNB95w&#xA;JESeStb3QpyidXXv9lx+NRiyjKUUdbTrO3BrJJvExgofw2yJFdXKxZBM0YCXu2Rsuh2zxc0LW7Uq&#xA;VcggfP8A28iJuXPs+BFi4+95558Hk3TNIuNU8wabBewxME2gjlkZ3NAEZqUr48hhnQFlp0A1E8ox&#xA;4pkH3mmDaB5I/KHz9byvor3GjX8IBmtSxBWuwbixnUpX+Uj3plQjGXJ32XWazSEDJKMgepG3zH6Q&#xA;k3mT/nHLznp0TXOkSRazajcCIiOansjEqf8AgvoyBx9zscHbEZD1jh8x6h9m/wBiV+S/zg/Mr8vL&#xA;0WMVxK1nAQs2iaiHaJQP2VVqPF/sCPeuV8nbQnGYuJsPrX8qvzk8sfmJYMbGtnrFuoa90mVgZEB2&#xA;5xsKCSOu3ICo7gVGLJnuKuxV2KuxV2KvmP8A5yZ/O7UrbUZfI/lq6a1EKga3fQmkjM4DC2jcbqAp&#xA;HMg1J+HsaqXzhBpuoXQ5xRNJXetRU/ecaaJ6nHE0Smdi3nbRqS6dNf2JXcPaSSx071rEdsaRHVYj&#xA;ykHpf5af85MeeND1m1tvM98+saA7iO7Fwoa5hUmhlSUAOxTqVcmvTY74HIfZVvPDcQR3EDrLBMqy&#xA;RSIaqyMKqwI6gg4UMd/MTz7o/kXyvca/qgaSONlit7aOgkmmk+zGtdhsCxPYA4q+Y9b/AOcvfzCu&#xA;5m/RVhp+mW9fgVkkuJR/rOzKh+iMYEqeif8AOXH5jWt/HJqtvZajY8h68AiMEnHv6ciNRW/1lbFX&#xA;1N/jry3/AIK/xn9Z/wBwP1T6961Pi9PjXjxr/eV+Hj/NthQn+KuxV2KvPvze/JvQvzF0tBM31LXL&#xA;RSNP1NRUgHf0pV/bjJ3p1B3HcFV8ceYPLXnn8tPMZtdQieyulqYpkq9tcxg/aRqcZEPgdx3AOIJD&#xA;VnwQyx4ZCw9C8l/mDbazxi5fU9VUfFCCQHA6mM9/9U7/ADzIhkt5PtDsyWHf6ofjmz2z8x3CELcq&#xA;JE7uNmH8Dlok6SemB5MgR4biEOtHikFRXcEH2yThkEFK77QlqZrFjDMN+ANAfke2Ahvhn6S3Chp3&#xA;mbUbCYxzs2x4sejinj2b6cHvcgQI3gaZZBrwuoVMqR3MLdG6H+zHh7kHWy+nJESeD/8AOQvm+2mv&#xA;YPLOmswggC3OoBqV9VhWOOvgqHl9I8MozSPJ6bsHRQAOcXvsL+1575D8xz+VfOGn6nIpWKN1W8ic&#xA;GjW8oAeq9/hbkvuAcqiaLutZgGbEY0Jd3vH4p9jRazbSRqqs1qKfCYwrpv8ARmXwvDR1sSK3h7gC&#xA;PuYn518iaJ5ls2XUo0ua19G+ios0TN3BG67/ALJ+E4mIlsWOHVZtNLjhLiiefcfeOj5uguda/LX8&#xA;xEns5w19otwrB0NFmiZQxRhvtJE/Fh2rmJKNGnvdHqRnxRyDbifoNbzLPBHMgIWVVdQetGFRXA5C&#xA;/FXYq7FXYq/Nq/vrrW9eutQuSXudRuZLiYk1JeZy7bn3OBhmlwxJeheWvL11IqcYq/Sv9csAeV1O&#xA;oA6s8tfKN/6PL6sxFP2aN+quGnVy1cb5vJ/zU0Qadq1vN6Zja6RhIpFKtGQK0+TDISD1HYuo48ZH&#xA;839L66/5x51afVPye8uzTsXlgiltSTX7NtO8UY38I0XA7l5J/wA5m6tObryzpCkiBUubuQdmdika&#xA;f8CA334pDyfyL5Wt7+2SeSATPISd15UANMkA8/2lrJQmQDQCr+ZnlBNJsLS+jt/QDS+ixC8QeSlh&#xA;27cDjIL2PrTknKJN7Wm/+Lr3/oWb9Cc24f4i+qU/Z+rej9c4f8j/AIsg9A+3MKHYq7FXYqk/mvyh&#xA;5d816PLpGvWaXlnJuA2zo1NnjcfEjDxGKvhr81vIR/LvzvJpVlqSXkaBbmznR1+sRKxPFJ1X7Ei0&#xA;+kUbatAFlESFHk9K8t6lJqmhWV9IKSzxAyACg5j4WoPcjMuJsPCavCMeWURyBegaTE1tpsazfARV&#xA;mrtQEk71y0OmynilsufWNNQ0Nwp+VW/UDjaBhn3JHrtzY3E0b255OARIwBAPh1yJcvBGURupaTqb&#xA;2U1GqYHPxr4f5QxBZZsXEPN4L+Y5C/mDqzzMLiM3Ik+FtmjZVZVqK/sEDMTJ9Re37K30sK29P2su&#xA;/Pfyxbwy6P5rsVCWetW6pLGKArNCoCnj4NFxG38vvkso3tx+w8h8LgPOJP3ll/5OfmDDremRaFdk&#xA;jVtPhorEfDLBHRVav8yggMD16+NLsU7FPPdu9mHDM5Y/RI/Ipx+avm3VPLHlj65psYNzcTLbidhy&#xA;WLkGbmV7n4aCu1fuyWWRA2cXsbRQ1GbhmdgLrvfNsWoLNrCahqyPqCPOs19GZPTedeXKRfVo/EuN&#xA;uXE08MwyX0OEBGIjEUA+1fyx/wCchvJXne9j0dIpNH1hwfQsrgqY5eI+zDKtAxA/ZKqfCuKXqmKu&#xA;xV2KuxV+d3njy3deTvPWp6NLGyjT7pxb8tuduW5Qv/s4yDgY5IcUSO96N5K1m1lhjkjcMNq+IPgc&#xA;sBeM12CUSQQ9S0/XYkgpyHTJ26LJgJL58/N7zbB5i8zhbNhJZ2CehFIu4dyayMvtWij5ZVI2912H&#xA;ojgw+r6pb/qfZn5P+Wbjyz+Wnl/RrpSl1BbercxmtUluHa4kQ17q0pGB3Dxz/nMry7cSWfl7zDEh&#xA;aC3eaxu3H7JlCyQ/R8Em/wAsUh5f+VnnSy06yFpdTJC0DHgXYKCrHl1PgTkol5jtnQSnPiiLtb+c&#xA;vn601+Kx0yymWeKBzPPIhDJz4lEAI2JALVxkWfYPZ0sJlOQonYMi/wCVdah/0Kz+lvSP1j9Lfpvh&#xA;Q8/qnD6l0p9n/dv+rvkHpH2HhQ7FXYq4kAVPTFXzJ+df/OTkiyXHl3yFOABWO819NyT+0tp+r1f+&#xA;B7NgS8Q8teSNZ8xzm+u3eGzkYvLeS1aSUk1Ypy3Yn+Y7fPJxgS63W9pww7D1T7v1vetB03T9I0uK&#xA;dkpDAojs4f8AV2B377fxzLAoPD6jLLLMjqebFPO/5j2mlnhOxuLwisVjGaBQehc78f15XPJTs+z+&#xA;ypZdxtHveV6n+YPmvU5SsVw1qjbJBagof+CHxk/TlByEvTYey8GMcuI+f4pB/XfOkP7/ANfUU7+o&#xA;WnA8dycFycg6bCduGPyCOt/zJ82Q2stu1ysxdSqTSIDIle6sKb/61cl4hcafZOAyBqvuRP5eeR7j&#xA;zRrEc17zGlrLW6l3Lyt1KKeu/wC03bHHDiLX2p2iNNjqP11t5fjo9T/P3yzPf+U9M1PT1Ji0JpVv&#xA;LZf2YbgoFlVRtxQxhW+fgMszR6uq9ndVCjA/UXl/5beddG8pzXl9c2UlzqMqCK2kUjisZNXBBK0q&#xA;VG+/0d68cxF2naugyakRjGQEBuWbf8r60G/R7PWdDabT5dpEBjlBHXeOSinf3y3xgeYdP/ocyw9W&#xA;PJUh7x9oTvRfI/5Pea7N7vSbRTSglWOaeOSInoGiL0X2+Gh7ZIQhLk4mo7Q1+mlw5D9go/GnkfnX&#xA;Q5PJfnV7fTLmQGzeK70+5JHqodpENRtyRx19q5jzjRp6vszWHUYRMij1ff2g6l+lNC07Uqcfr1rD&#xA;c8R29aNXp/w2Rc5HYq7FXYq8r/O78jrH8w7SK9spUsfMtmnp2904PpTRVJ9GbiC1ASSrAGm+xrir&#xA;5j1X8g/zh0a4YfoC4nAJCT2LpcBh0qPSYuP9kAcCkA80F/yqz85JB6Z8u6wVf4SrRTBTXbeu1MWA&#xA;xQ7g9h/JD/nGjVbTWLbzJ53gSBLNhLY6MWWR2lXdJLgqWUKp+IJWpP2qdCWx9OYoSrzV5Y0jzR5f&#xA;vdB1aL1bC+j9OUDZlIPJHQ70ZGAZT4jFXydrf/OJP5j2upTRaVNZ6jp4JNvctL6Dle3ONgeLfJiP&#xA;fAlOvJf/ADiF5hfUre483X9rDpiMrz2Vm8ks8gG5jLlY0SvQspb2xW31D+idM/RX6I+rR/ov0Pqn&#xA;1PiPS9Dh6fpcenHh8NPDChF4q7FXYq8K/wCcrvzDvtA8sWflzTZTDda/6n1uZCQy2kXEMgI6eqz0&#xA;/wBUMO+Kvnv8u/I8OpKNX1JOdmrUtoD0kZTuzf5IO1O5/GzHC9y6TtXtE4/3cPq6nu/a9XVVVQqg&#xA;BQKADYADMh5glj/5ieexpGnQWtuQb9ouFumxCDo0rD59PHIZJ0HP7L7O8WZkfpvf9TyTRtEvtdvJ&#xA;JpZG9Llyubp/iJY7kCvVjmPGNvYEiAoPRNH0K0tAtvYwBWP2nO7H3ZjlwjTRKRPNk9rpVvCAXHqS&#xA;dyen0DJ01ksH/NbQk+q22qW0Cr6bNHduigEh6cGYjwII+nKsserfgl0X/lh+ZOnaFaLYagfRERb0&#xA;puJZGR25FW4gsDy70w48gGxdJ2v2TPNLjhvbMPOv5xeU38tX9jp8rXl9fW8tqIlUhFEyFGZnIAoA&#xA;3Qb/ACyc8op1nZ3YuojmjOXpjE3+x5T5A8qWnmDU5E1Bp49Ot05TSW/EOWYgKgLhlFRU9D0yiEbe&#xA;zyT4Xqy/kh5A1ayaLSry8tNQVSUaZkkqfF04gMP9UjJHG1xy28rsLvX/AMu/OrJMONzYyeneQKTw&#xA;mhahIBPVXUhlJG2xyMZGJatbpIanEYH4eRXfmPq8fmPz1d3GnN68Mxhgs+O/KkaqKV8XrjklZaey&#xA;NOcOnjGX1b38339o+nrp2kWOnqQVs7eK3UjYUiQJt92Rdii8VdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVfLf/ADmLc+W7i90RINQjfzBYiWO705Ksy28wV0d2HwoQy7K25DV6YEh5f+V/mnUBfQ6D&#xA;KPWs5BI0LU+KIqC53/lPv3y7FLo6HtjRw4TlG0tvj0enXVzDa20tzO3GGFGkkbwVRU5cXm4QMiIj&#xA;mXg9zcX3mbzE0rmkt3JsOojjHQfJVGYv1F7rBhjhxiI6PQtPgsbKJNPtmUNCoJjqOdD+0w67nLht&#xA;swJJ3ZZp1mLeAEj96+7nw9smGslF4UKc8ENxC8E6LJDIpWSNhUEHqCMVBYDq35SW0sxk0y8NujGv&#xA;oSqXA/1XBBp8wfnlRxdzfHP3oWy/KCcyA3uoKIx1WFCWP+yalPuOAYknP3Bn2j6Np+kWS2djF6cQ&#xA;3YndmbuzHuctApolIk7plb3EtvOk8LcZIzVThQGM/wDOQOgw3mnaV5utloXAtLwDwNXjJp/KwZSf&#xA;lmPMOXjkxX/nH/yq3mP81dFgaPna6fJ+krvaoCWtHTkPBpeC/Tlba+8cKHYq7FXYq7FXYq7FXYq7&#xA;FXYq7FXYq7FXYq7FWM/mZ5nn8reQdc162AN1Y2zNbchUCZyI4iR3AdwSMVfCXl3RdQ836/M95dO7&#xA;uWub+7kYvK5ZtzVurMT1OGEbLh67WDBDiqyeT13Q/LGi6LHxsLcJIRxedvilYe7H9Q2zJjEB5LUa&#xA;zJmPrPw6JR+Z1+1r5UmRTRruRIAR4El2+8IRkch2crsfHxZwf5ot5h5f1SHSPUvXh9aWQiGJa8QF&#xA;FGkNaHf7NMoiaetnG9noHl/QUPmObVvVLrcopWOlOKgKd9991GXRjvbjyltTN8sanYq7FXYq7FXY&#xA;q7FVfzRarqX5T69bMOTWiGdPb0Ss1R/wLZVkDdiKS/8AOHF1GvnnW7UqDJLphlV6bgRXESkV9/VG&#xA;UOUX1xhQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqlXmvy7aeZPLWp6DdkrBqVvJbs4FShdaK4&#xA;HirUYYq+CdV0vzX+W/nCaxvYjBf2pKkMCYbiFjs6HbnG9Kg+PgRjE0WnU6eOaHDJ6FoP5ieXtURV&#xA;lmFjdH7UM5AWv+TIaKfwPtmRHIC8rqeysuM7Dij5fqSX83pQ+k6cUYNG8zMCCCDRNjX6cjl5OX2F&#xA;Gskr7mMaFo+m3fl43d4H42csszBCAWUIpZT/AMDkIgU9DKREtnovlG5jvbP66iFFkReKnqASf6Zd&#xA;E248xRpkGSYOxV2KuxV2KuxV2KplEol8m+aYXNEawmBI60aCUHITbcXNg/8AziR/5NSX/tmXH/Jy&#xA;LMZyy+y8KHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqxrzz+XXlPzvpn1DzBZCcJU29yh4XE&#xA;LHvFINx7g7HuMVfMn5jf84r6z5cs7zWdI1i1u9FtEaaUXzfVp40G9C28T+FarU9FwJeGerL6Xpc2&#xA;9IHkI6njy6Vp44o4Rd9Wd+QpEk0eeFgDxmbkp6FWVev45dj5NGXmz3QAiRyxoAqrx4qNgBuNhloa&#xA;ZJthYuxV2KuxV2KuxV2Kq97cLb+Q/NsjdGsHj+mWOSMfi2V5OTbi5sZ/5xBtWl/My+m3CW+kzEnt&#xA;Vp4FA/En6Mx3LL7EwodirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVeM/wDOWFprVx+VobT1&#xA;drWC/gl1RUBJ+rhJAC1P2BMUJ260PbFXx5aXGnJYXkM9uz3cwT6rcBto+LAsClN+Q2rXbEU1zjMy&#xA;iQfSOY7088g3wi1Ca0Y0FylU/wBeOpp/wJOTxndco2t6Np12La45N/dsKN/XLg45DIEdHUMjBlPQ&#xA;jfJMF2KuxV2KuxV2KuxVJ/zE1JbH8tdRirSTU7q3tU8aIfWb6KJT6cqylvwDdkX/ADhnorf87Lrb&#xA;r8P+j2UD+J+OWUf8m8pckvpvFDsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiq2WKKaJ4pUW&#xA;SKRSkkbgMrKwoQQdiCMVePecP+cWfy212WS605ZtAu3JJFkVNuWPjA4IUe0bKMVfNX5k/lN5v/Lj&#xA;WSZ0kuNMDg2GuRRlYZPANQv6T9ijH5VG+KeaWWPn+8jAW8gWcD/diHg3zI3U/hkxkajiHRkemefN&#xA;JLArO1s56pKtAfmRyXJiYaziLNdM1SK9jBUjkRyBU1Vge6nLAWkikdhQ7FXYq7FULqOqadptubi+&#xA;uEt4h0ZzuT4KOrH2GAkBIiTyeU/mD50i8wyWdnYq66fZc2UuKNJNKQGfiCdgqKq9+vjmPOVly8cO&#xA;EPsb8ivJMvk/8ttM066jMWpXQa+1FCKFZrih4MPGOMIh9xkWbP8AFXYq7FXYq7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FVk8EFxC8M8aywyArJE4DKynqGU7EYq8080f844/lRr5eT9FHSrqSpN&#xA;xpr/AFehO+0VHg/5J4q8I/N//nGqXyT5dn8yaVqr6lp1tJGtxbSwhJYo5G4B+asVcByoPwjrgSw3&#xA;8stW4yNYu28b84wf5H+Fh9B3+nLsZaM0er0G+1VIgY4SGk6FuoX+3LSXHAdo9yZInjc1dTyqepDf&#xA;24hSrXuoxWxVac3PVR2GJKgKWratDY6Nc6kCGWGJnSvQtSir9LUGJNC1jGzTynyj5Q84fmP5lOna&#xA;Z/pd+yGe4nuJOEcUQYKXYmtFDOBxUE+AzFJtzgAH09+WH/OL/lrytd22sa9cfprWrZhLBHx4WkMi&#xA;7qyofikZT0Lbf5NcVe24q7FXYq7FXYqk3mjzn5W8q2kV35h1KHToJn9OEzE1du4VVBZqd6DbFUVo&#xA;+v6Hrdqt3o+oW+oWzAES20qSrv48SaH2OKo/FXYq7FXYq7FUh8zeffJnldFbX9YttPZ6cIpXBlav&#xA;cRLykI9+OKoz/E3l79Af4h/SNv8AoP0vrH6R9RfQ9P8Am59Pb57dcVTLFXYq7FXYq7FXYq7FXYqg&#xA;dc0ax1vRr7R79PUstQgktrhe/CVSpI8CK1B7HFX59a7pGreRfOl7pVyK3elztE1QQssZ3Rx/kyRk&#xA;MPniDSyFhB3fmLXr2Rp/XkRIviKwckRASACePuaVbCZFAgAyDQfzFe0Q/Xoy8yKQkkYHx7dGG1D7&#xA;5OOTvapYe5KNQ80+YNZvQls0qM7fuoLYsZGJ8SvxMciZktkcYCFm8z6zPpDaVPOZbZ3V2L1Lnj0U&#xA;t3Fd9/DBxGqSIC7fUf8AziJ5MOneUr7zRcJS41qX0bQkbi2tiVJH+vKWr/qjAye+4odirsVdirsV&#xA;diryj8+vyXvfzHtNNm03UI7PUtKEwhiuA3oSrPwJDMgZkIMY3CnFXzXqP5C/nP5fujJDotzIybJd&#xA;abKsxIrSq+k3qD6VGBKilj/zkPEhhS383RpJQGMJqYDcdwKAb0xVN/Jnkn/nIG981aZepba3aTw3&#xA;EbG/1Fp4VjQMA5c3BXkvEbrQ8htQ4q+2sKGPfmHY+Y7/AMkazZ+W5jb65PbOljKG4NzPUK9RwZlq&#xA;qtXY74q+Lm/Ln89rdntxpGuAVPIR+syEt1+JCVP34Epp5f8A+cZ/zc1q4DXenppUDtWS6v5kB3Px&#xA;H04zJKT8138cVfSf/KlrX/lS3/Ktf0lJ/d1/SPE0+sfWPrVfS5f3fq7ca9P8rfCh6XirsVdirsVd&#xA;irsVdirsVdirzv8AMP8AInyL581e31fWBdW99AixSS2ciR+tGpJVZQ6SVpUiq0NO/TFWSeX/AMv/&#xA;ACZ5f0WXRdJ0i2t9NuF4XUBQSeupFD67Scml22+MnFXjPnX/AJxD0XUtX+u+WNU/Q1pMxa4sJojc&#xA;Rxk9TAQyMB/kMfpptim3p35b/k75M8g2o/Rdt9Y1Rl43GrXADXD16hTSkaf5KfTXrihL/On/ADj7&#xA;+Wnm3U/0pe2MllfueVxNYOIPWNa1kXiyEnuwAY9zirPtK0vT9J0220zToFtrCzjWG2gSvFI0FFG9&#xA;Sfmd8VRWKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVB6xrWk6Lp02patdxWNhbjlNczs&#xA;ERew3PcnYDvirw3zR/zmB5QsJ3g8v6Vc6zwJH1mVxZwt7pyWWQj/AFkXFNMWH/OZ2seqSfK9v6VN&#xA;l+tPyr48vTp+GBaZJoP/ADmN5VuZEj1vQ7vTQxoZbeRLtF92qIHp8lOFae0+VfOvlXzZYfXvL2pQ&#xA;6hbinqemSJIyegkjYLJGfZlGKE6xV2KuJAFT0xV8s/mv/wA5U60ur3Gk+RGit7K1cxtrLok8kzqa&#xA;FoVcNGI69CVJbrtgSl35b/8AOVnm221q3tPOkkWpaRcOI5r5Yo4J7fkaCSkISNkX9oca06HsVX1s&#xA;jq6h0IZWAKsDUEHoQcKG8VQGva5pmg6Neaxqkwt9PsYmmuJT2VewHck7Adztir5F84/85XfmJqWp&#xA;yN5bePQtMRiLeP0Ybid0HQyvOsiVPWiKKdN+pCXon5Ef85Han5m1uLyv5tWI6hdBv0dqUSiISOoL&#xA;GKVB8AZgDxZaeFKnCr6FxQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVC/vrTT7G5v72UQWdpE89z&#xA;M32UiiUu7GnZVBOKvhP84/zd1f8AMPXWlJe28v2jsNK08noOnqy02Mrjr/KNh4kJY7p/l23aFJbh&#xA;zIXAYKhotDuN+pyYixMkf+gtKpT0P+Gf+uHhCLKT6nZaLBVY5mWYf7rX4x9PSn35EgJBK3yz5p17&#xA;yxrEOr6HeSWV9AdpEOzLWpSRT8Loabq22RZPub8nfzRsfzD8qrqKqsGq2hEOrWanaOWlQ6VJPpyD&#xA;da+47YUM6xV59+ffml/Lf5V61dwyeneXcYsLVhsed0fTYr7rHzYfLFXyp+R3lO01nzBcahfRCa10&#xA;tFZI3FVM8hPpkg9eIVj86ZbhjZdB7Q62WLEIRNSn9w5qP56aZZWHnZGtYliF5aR3EyoAoMheSMmg&#xA;8RGK4MwqTP2dzSnp/Ub4ZEfYD+l9kflRdz3f5Z+Vp525yvpdpzbuSsKrU+5plbvGVYq+e/8AnMLz&#xA;U9p5Z0jy1C1G1Sdrq6AP+6bUAKrezSSBv9jikMB/InyfYfoSbX723Sa4vHeG19RQwWFPhYrXu78g&#xA;fYfPMjDDa3jfaLXS8QYomhHc+954Il0X824k0/8AcrY63GbULtwCXIKgU7DplExRL0+hyGeCEjzM&#xA;Q/QXA5TsVdirsVdirsVdirsVdirsVdirsVdiriQBU9MVfM//ADkP+fXlfVfLF35Q8rXbXt1dTpFq&#xA;F5Eh+r+hGS7pFLUcy0ioKqCpWu+KXh3mKE2Gl6V5ZiH+lMReah4+tKKIp/1F6/RkRuWR2COgiWGF&#xA;Il+zGoUfQKZc0oq7smGlzTxMfWELOg9wtRhPJQxewNnZ6A+p/V0ur9rr0FMw5xxKIw4b0z8LM5r9&#xA;qo2yhtW6lONV0oaiYYory1kEF36KCNXjkFYnKr8NQVZSR7Yqz3/nGXzc2gfmjZWkkvCx1xW0+dST&#xA;xMj/ABW5p/N6qhB/rHCh9vYUPnD/AJzM1lk0jy3oqk8bi4uLyRe1YEWND/yXbFISL8h9MW18km7p&#xA;8d/cyycv8mOkQH0FGzKwj0vB+0eXi1HD/NiP1vPvz8n9TzxElQfRsYk+VXkf/jfKs/1O99m41pj5&#xA;yP6H2L+Vlt9W/LTypCQVZdJsi6nqGa3RmH3nKnfMoxV8Xf8AOV+ryXv5ryWZJ9PS7K3t0U7Csim4&#xA;JHz9an0YEvUvJmmLpnlPSbEDi0NrF6gH+/GUNIfpdjmfAUA+Ya/N4mecu+R/Y+fin6R/OdIlqRce&#xA;YUiXh1IN4EFPc5hT+ovofZ8a0+Mf0I/c/QDA5bsVdirsVeYecP8AnI/8rvLF1JZvfSarexErLBpq&#xA;CcIw2oZWaOGo7gOSO+Ksb07/AJy+/Li4uBFeWGp2SMQBO0UUiDxLCOUv9ynFaeveW/NXlzzNpy6l&#xA;oOoQ6jZtsZIWqValeMiGjI3+SwBxVNcVdirsVdirsVdiryf/AJyV8+f4X/Lm4s7aXhqmvE2FsAfi&#xA;WFhW5kHyjPCo6Fhir460mOy0zzDanWARBBwnkRRyNTGJEUj5kA5EsgpnVLm98w/pCTeW5nqV60Vj&#xA;x4j5LsMMUSZYkbvXgK8RU08Mua03tN7aOvhhQ8/1EHS59T0wpWC44NFX9kq4dGHyQsv05TIbtoOy&#xA;3UdHfT9K02+ScumqRyFkC8QvpuAVO55b0PTI2mkFpt/c6dqNrqFq3C6s5o7iB/CSJg6n6CMKH6Oe&#xA;XNbtdd0DTtatf959StorqIdwJUD8T7itDhQ8z/5yR/K7UfO/lW2u9HQzazobSy29qOs0MwX1o1/y&#xA;/wB2rL40p3xV8p+XPzB84eT0uNMtiEQOfUsruMn0pBs1FJVkPiMlHIYuu1vZWHUESmPV3hV0Hy95&#xA;2/NHzeI7eN7u9uXQXl7wCwW0P2echUBVVFGw6t0FTkSSTZczT6eGGAhAVEPvzTrGGw0+1sYP7m0i&#xA;jgi/1Y1Cr+AxbURir5i/5yp/KbWbrU1886NbveQNCkOsQRAvJEYhRJwo3KcKK38tK9DspeOaX+cH&#xA;nfTdITTILmNo4k9OCeWMPKijoAx2NO3IHJjLICnUZuw9NkycZB35i9mVf848/l35i8z/AJgaf5km&#xA;tn/Qul3X1271GVaJJPGS6JGT9t/VoWp9kde1YO2EQBQ5PtbFXYq7FXzH/wA5Q/nLfQ3knkTy/ctA&#xA;qIP07dRMQ7GRai1DDovAgyUO9eP8wIS8y8hfknqOv2cWqarcHTtOmAe3jVeU8qHowrsinsTWvhTf&#xA;ASmmV6r/AM466M1s36J1S5iuQPh+tBJY2PgfTWIr898HEtPPfLvmLzp+VHnT1oeUF1AwW8s2J9C6&#xA;gr0PZkYfZbqD75IFD7q8p+ZtN80eXNP1/TGLWWoRCWMH7Sn7Lxt/lI4Kt7jChCefvOul+S/Kt95h&#xA;1H4orVaQwAgNNM+0cS17s3XwFT2xV8T+YfzE/ND8xdakBuru451aLSbAyJbxINtokNNq0LvU+JwJ&#xA;Tb8ufzv89+QddW01We6vtIST09Q0i9Z2kjHcwmWrRuvWnQ9/EKvs/wDxPoX+Gf8AE31tf0J9U+v/&#xA;AFzfj9X4epyp1+z269sKE0xV8gf85eX13N+ZOm2YmWWG202JobdDy4SSzSl+Sb0dwqfNeOBIeP69&#xA;olxpb2xvZxJfXSGa5t6kyRVO3qMa7sN8AKSERpiw3Wp3GoLGsFrET6MY2VB+yP8AYr1+/JxDGRTj&#xA;S/M2nfXvqtG/esESbbjXt70JyQkxMVvmHzasBe0sCGmHwyT9Qp8F8TjKSiLGomkv47prqV5ZoIfU&#xA;id2JOzqCtTXajE5DmyTjWP3vkTQZOpgmuYiev2n5fRsMgObM8nWtjp9h/iPRNSMSXUKE2d1IOLF4&#xA;HqFQtv8AvRQj2wlAfVP/ADijr13qf5WC0uAxGkXs1nBIejRsFnAr/kmYj2FMkxey4qlGreTvKOsz&#xA;evq+h6fqU+w9W7tYZ32FB8UiscVR2n6Zpum2y2unWkNlarusFvGsUY7bKgUYqicVdirsVSG58g+R&#xA;Lq6+t3PlzS57uvL6xJZW7ycq1rzZC1a4qnkUUUUaxRIscSAKiKAqqBsAANgMVXYq7FXYq/PbRVbz&#xA;n+ZcUuoEv+l9QkubsN+0rM0zr7VUccBS+xtI8pTXESSzt9XgIHBAPiK9tuijKrZ0mz+TNMKUWSVW&#xA;7MSp+8ccbWngv/OTnkaS00Kx1viJGtrgW5uFFKxTKTxb5Oop8zkolBDK/wDnDzWprnyTq+kyNyGn&#xA;XwkhBr8KXMYPEdqc42P05YwKR/8AOZmuTLF5b0JGIhcz31wm9CycYoj9HKTFIX/849/l/dr5FTWY&#xA;okWbVpHkMrmjGOJ2iRB12BVm+nK5FkAw/wD5yT8p/UZdO1h4fRunY2l0QPtjiXiao2NArCv9MMSg&#xA;qf8Ajq9/6FX/AET6x9T9N/oilfj+rcPr/wA+PL4Plt0ySH0z+bPnz/A3ka/19IRcXUfCGzhb7Bnl&#xA;PFC9P2V+0fGlMKHxguoTRGfzr5hm/SGu6nI8thFKQS0hNDO4HRU6KoG3btSB3ZjZiOoyXst5JNfM&#xA;zXUpEkpb7VXHIV8Nj07ZJir3lyYbSPT4tgo5XJHdzvx/2PTCSgJhfeSNesvL1vrk0Q+qz7vGKmSN&#xA;D9h5BTYN/t9ckYGrcWGtxyyHGOY+1OfJP5eHV9G1LXdSLxWFpbzPaxj4TNKkZYGv8ikb069MlDHY&#xA;suHr+0vCnHHDeUiL8hf3sZ0JPUmuo60DWswJ+S1/WMri7Ypt5Q0XzR5zurLybo8UcrtM1whchBGK&#xA;UkkdyfsIN9hXwr0wUyt9d6x/zjj+XOufUrjVYJ21K3tre2uru3laL6wbaNYw8i/EKsqUJG/vhYs+&#xA;8s+WNC8saPBo+h2iWWnW9fThSpJLGrMzMSzMe5Y1xVrzP5q0Dyvo82sa7eJZWENA0r1JZj0RFWrO&#xA;x7KorirwvVP+cy/L0V2U0zy5dXdqGp6888duxHiEVJ/xbFNM4/Lj/nIjyJ52vI9MUyaTrEu0Nlec&#xA;QsrfywyqSrH/ACTxY9hih6jirsVYb+Y/5s+T/wAv7OOXW52e8uAWtNOtwHuJQNuVCQFWv7TEDwqc&#xA;VeNf9DoQfWqf4Sb6pWhf68PUpXrx9Dj9HL6cU09g/Lf84vJfn+Bxo9w0OowrzuNLuQEuFXoXUAsr&#xA;pX9pSabVpXFDN8VdirsVfn95faPyH+bdsmqqfQ0PVGt72oqfRSQxO9B9r4DyHjkSyD7wililiSWJ&#xA;1kikUNHIpDKysKggjYgjKma/FXgH/OXHmu1g8t6Z5YjkBvr24F5PGDutvCrKvIf5cjfD/qnJwDGS&#xA;af8AOH+hzWnkXU9WlTiNUvuMBI+1HbIF5D29R3H0ZYwLFf8AnM3TZ11DyxqQBMEkVzbE02V0ZHFT&#xA;/lBzT5YEh6H/AM4163a6l+U+mW8TAz6ZJPaXSDqreq0qffHKpyuXNmGF/wDOYGsWa6JoOjVBvJrl&#xA;7wgU5LFFGY9+9GaXb5HDBEnmn+GLv/oWf9Mem3H/ABL9Y5fs/Vvq31Xl/wAj/hybF9jecPKWjebf&#xA;Lt3oGsRmSxu1AYoeLoykMjo1DRlYVH9MKHzB54/I/wAr/lZZyeZdW1n9NBWMegaJJbqnrXRBKG4P&#xA;Nw8UX23UKOXTatCEvC2nuLi4nv7lzNMzmWWVzVnlkJNWJ6ktUnCqYeV7fTpNTF5qsgTT7T97Nz35&#xA;tX4EpuW5HcjwBwx57tOcy4ajzL06x/Nny26zc/WhEKFgJEX95TbinFm3PvTLhlDpMnZc+lMG8y/m&#xA;X5k1qSSG3mex09gVFrASpZKb+o4oWqOo6e2VyyEuw03ZmLHuRxS7z+hjmnz+j9afxgdP+RlE/wCN&#xA;sgHYl7h/zh5pDXHnrV9VK1isNOMVadJLmVOO/wDqRPgUvrrCh2KvjP8A5yr85XWr/mI2gpKf0doE&#xA;aRJED8JuJkEssnzoyp/scCWGad5KsBao14Xed1BYA8QpO9BTwywQYGSQ67pEmi30UltM3EnnBIGp&#xA;IjKajdaEEdQcjIUyBt9s/kN5/u/O/wCXlrqV+eWp2Ur2F/LSgklhVWEnzeORC3+VXAr0QkAVPTFX&#xA;56ed/Ml9578/3+rSOT+kLkrag1pHbIeMS0/yY1FfepxG6U0HkrRvQ9MiQyU/vuR5V+X2fwyzgDDi&#xA;LH7G/wBa8lea7XUtNnMV/p8qz20wqA69wwHVXWquvcVGVkUyBt+g/l3WrbXNA03Wrba31K1hu41O&#xA;5CzIH4n3HKhxVMMVdir5S/5y+8maZYazpnmq2YR3er8re9t6bO1ui8ZgfHgQjfIYEhnn/OLmp61c&#xA;/llI2qzmSzs7uWDTWk6pbxxoxWv8iuzcfDp0AyuXNmGM+av+cu9OiW4t/LOjSXEwLJBe3riOLbYS&#xA;einJ2B7Asv8ADCII4nj/AJY8r+e/zg87ySyySXE1xIr6pq0i/ubaLp0HFdlFEjXr8qnJgMX3L5b8&#xA;v6b5d0Gx0PTE9Ox0+FYYQd2IXqzEUqzGrMfE4UMZ/OT8uo/Pvki60dGEeowsLrS5W+yLiMEKrH+V&#xA;1YofCte2Kvjryr52/MD8qfMN7bQRfU7uoi1HSr6NjE5SpQsgZDtWqujbg9aHIkWyBd+af5hSefrr&#xA;S9evIorXVYrdrC7tYOXp8YZDLHKvIsQH+sMtCf2cQKUl7h+kh/0Jn9Y+rx0+rfVvToONf0r9X9Sl&#xA;Ptft1/m3ySHv2va9pOgaPdaxq1wtrp9khkuJmrsBsAANySdgBuTih8H/AJqfmRq/5jebn1GVWjso&#xA;6waTYV2hgrX4qbc3+07fR0AwJYrqHpRCO0hYOsQrI43DSN1+7phKAjfLHk7zN5oupbXQ7GS8a3ja&#xA;a4dRSOKNQSWkc/Cuy7dz0FTgShvL/l/WPMOsW2j6PbPd6hdvwhhT7ySTsqqN2Y7AYq+xfI3/ADj3&#xA;oPlXyRq1lMqah5k1ewntbvUCu0frwlDDb1FVSp+19pu/YAofFOBL7a/5xm8hTeVvy+W9vYzHqevO&#xA;L2ZGBDJAF426EH/JJf8A2VMKHrmKuxV8Efnnbz2n5w+ZVuAeRvBMAwArHIiyJ0ptwYUwJQXmfzQ8&#xA;RW0096Myh5Jx1AYVUL9G9cslJhGKaeR/yI/MnztFFqNpZi10y4NV1S+f00cV3ZF+KWQe6rT3ytm+&#xA;wvys/Luw8geUYNBtZjcy82uL27K8fVuJAAzBd6AKiqo8BhQyuaJJoZIX+xIpRqeDChxV+ePmzyt5&#xA;j8h+arjTb2J7e5tJJFtblo6JPFuqyxcgVZXU/R0648koWHzhrscgZ5llUdUZEAP0qFOHjKOELfM+&#xA;qW2pXNvPAKfuQHB6huTEr9GMjaxFPur8mbW4tfyq8rQ3AIl/R8L0NahZF5p1/wAlhgVmeKuxV5T/&#xA;AM5HflvqPnXyQh0iL1tY0ib61bW4+1NGVKzRJ/lEUYePGnfFXyf5f/M3z15U0HU/LGnXRtLK+Zxd&#xA;QyRj1YndRHJwLDlGzKvE+Hah3yJDK3oX/OPP5FR+bribXvNVlL/hqJONlEzSQ/W5iR8SspVzEgBq&#xA;VIq2wOxwofWuiaBouhafHp2jWUNhZR/YggQIte5NOpPcnfChH4q7FWC/nF+Wln578nXmnpDCutIg&#xA;k0u9kReaSRtyEfqU5Kkm6t864q+PLL8kPzWu9aXSP8NX0Exk9N7meF0tE33Y3NPSKjrVWNe1cCX2&#xA;P/yrDT/+VS/8q+9X9z+j/qf1qn+76c/W4/8AGb46YUPmH/nJD8z9c8yecb3y2wa00TQbmS3itKke&#xA;tNGeDXEn81afu/BT7nAl5VpGj6zq94tlpNnPfXcmwgto3lcg/wCSgJpir3f8uf8AnEzXdQeO+86z&#xA;nSrLZhptuyPdSDrR3HKOIf8ABN7DFX075a8reX/LGlR6VoNjFYWMe4iiG7N3Z2NWdj/MxJwoUNE8&#xA;j+T9C1C61HR9HtbC+vK/WbiCJUdgTyIqOgLbkDbFU8xV4ppn/OK3kq086t5guLua908TtdQ6LKi+&#xA;kHLclV5AayRoeikb7cid6qva8VdirsVfP/8Azkz+S+peZPS83eXLc3OqWsQh1KwjFZJ4UqUkjUfa&#xA;kStCvUrSnShUvEPyt/JvzX5q822NteaTdWuiRTK+qXlxDJFGsMZ5PGGYLV3HwgDfevTAr7tiiihi&#xA;SKJFjijUJHGgCqqqKAADYADChdirsVWTW8E6hJo1lUGoV1DCvjQ4q8p/Pv8AJs+efLdu2hxQQa7p&#xA;jtJaqQsazxuAJIS4A4k8QVJ2qO1ahV4j+Xv/ADi7561DzFB/iyyGk6HbSBrwtNFJLMqGpiiELv8A&#xA;a6czsBuKnbAl9ixRRxRJFEoSKNQqIooFVRQAAdgMKF2KuxV2KpZe+VvLF/dfXL7SLK6u9v8ASJ7e&#xA;KST4enxspbbFUyVVVQqgBQKADYADFW8VdirsVdirsVdirH9W/LzyJrGonUtV8v6ffX7cQ9zcW0Uj&#xA;txFF5llPKg23xVMbKy0TR4Pq2n2tvYwdoLaNIl/4FAoxppyaiEOZVf0laVoW4k+Iphpp/P4rq0Sj&#xA;o4qpBHiMDlRmJCw3iydirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir&#xA;sVdiqB1W/wDq0YVN5XNFHudsIDre0NZ4UaH1F5554/MP9A3I0zT0FxqjAGeRqkIX3VQACSxG9KdM&#xA;z8Gm4hfR0eozyh6Y/V1KTad54uhJ/ua12C2diOVs1tK4WoqA+y8fuyyWn7gXG9R+qf2Wz3StQuII&#xA;4rlJo7qxm3SeFuaEHMCeOnI0+bLppAk8WM9WVxSLJGrr0YVyp6zHMTiCOq7Fm7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqw3zdqLWlx9ZILJan1nA/ki+N/8AhVOW&#xA;4o2aeQ7VyE6kf0TfyYJFp9vJ+Y17cXNJVmhe4sWG4cMQ3weJ9Nl+jNkZfuwggDISe608ijsLvTrx&#xA;PqccaQoZCu3Fq1J5bd/HKRI206TVeMJDhqki/LW/+qeZbrRYiW0jUEM0ELdEf01k28PhYg/R4Zdq&#xA;I3GzzbdORxGH8Mnr2huxs+JNeBpXNSXe9kSJxUeiYYHaOxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2Ksd1/T4pbmk4rb3CPDL/qyoUP68sxyovN9p4OHOJn6Tt83kNy&#xA;uq6DcppGpHjLZvy026kUlJEUniVYcWGzb8WqpzbCpCx8XA9UPTLpyRd7r/mPXI/0TFHb2sN0eE/1&#xA;QtJNKD1VKqoXkOpPbIjHGO6eKUvSABfcmXlDR2tvMNzqZp6VqhtbBF6SSsAJXXxjQgqG/aynUZfT&#xA;XVrOQQlY58h7/wBQer6TbmCyRW+0dzmuL0/ZuHw8QBRmBz3Yq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVK5to7iIxuKg4tOfBHLHhkkl/pUrQm3uLaK/tf99zKHG3j&#xA;yDD8MtjlIdFl0ubHtQnFLBo0gR4LCwh0+OUcZfq0axsyntyULkjmJcGUNRP0wiIJronlmCyVSygc&#xA;fsoMqlK3Ydn9jeGeKfNPsi9C7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FX/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:d5ff6d33-9d50-4a24-b8e0-18e2e036eb92</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:54:48+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=112 G=137 B=148</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>112</xmpG:red>
                           <xmpG:green>137</xmpG:green>
                           <xmpG:blue>147</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=69 G=230 B=203</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>68</xmpG:red>
                           <xmpG:green>230</xmpG:green>
                           <xmpG:blue>202</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=204 G=255 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>204</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=254 B=208</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>253</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=22 G=132 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>22</xmpG:red>
                           <xmpG:green>131</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=77 G=255 B=207</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>77</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=6 G=46 B=33</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>6</xmpG:red>
                           <xmpG:green>46</xmpG:green>
                           <xmpG:blue>33</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=138 G=128 B=120</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>138</xmpG:red>
                           <xmpG:green>128</xmpG:green>
                           <xmpG:blue>120</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
31.9229 52.7373 mo
31.0024 52.7373 30.0718 52.6494 29.1558 52.4776 cv
22.9224 51.3047 20.061 46.0254 19.3535 43.0918 cv
19.1973 42.4444 19.1719 41.7725 19.2788 41.1152 cv
19.3535 40.6572 21.1431 29.7969 24.439 20.7354 cv
26.0962 16.1748 30.1851 11.2637 35.2451 11.2637 cv
35.5137 11.2637 35.7822 11.2783 36.0479 11.3057 cv
42.084 11.9453 44.7861 18.4385 44.7861 24.5889 cv
44.7861 25.1514 44.7695 38.4541 43.29 46.293 cv
43.0938 47.336 42.5703 48.2891 41.7959 49.0147 cv
41.3896 49.3955 37.6582 52.7373 31.9233 52.7373 cv
31.9229 52.7373 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.587701 0 .334142 0 cmyk
f
35.2451 16.2637 mo
32.8599 16.2637 30.2183 19.4717 29.1382 22.4434 cv
26.0054 31.0557 24.2852 41.4805 24.2134 41.9199 cv
24.2256 41.9668 25.415 46.6856 30.0801 47.5635 cv
30.6934 47.6787 31.314 47.7373 31.9229 47.7373 cv
35.7725 47.7373 38.2734 45.4619 38.3779 45.3652 cv
39.4912 39.459 39.7861 29.1924 39.7861 24.5889 cv
39.7861 21.4297 38.6504 16.6094 35.5205 16.2773 cv
35.4395 16.2695 35.3428 16.2637 35.2451 16.2637 cv
cp
31.9233 57.7373 mo
31.9229 57.7373 li
30.6968 57.7373 29.4556 57.6211 28.2339 57.3916 cv
20.5806 55.9522 15.8188 49.7608 14.4927 44.2637 cv
14.1792 42.9629 14.1289 41.6338 14.3438 40.3125 cv
14.4219 39.833 16.2905 28.5098 19.7402 19.0264 cv
22.0483 12.6748 27.7383 6.26367 35.2451 6.26367 cv
35.6777 6.26367 36.1201 6.28711 36.5596 6.33203 cv
44.4766 7.1709 49.7861 14.5068 49.7861 24.5889 cv
49.7861 26.0469 49.7422 39.0694 48.2031 47.2207 cv
47.8086 49.3184 46.7744 51.2012 45.2148 52.6631 cv
44.3115 53.5098 39.4229 57.7373 31.9233 57.7373 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_01_Right Arm.eps)
%%CreationDate: 7/31/2020 1:54 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&iufFXR#M!Ifg@!!4E1ON?'!KVo(1XZW&G(.aa`"P[V:CeE]S97tKT7dng@ZU[DqhPt=RZ:?fkYZSB[bo7N<bDES'cA<Y@
%i:u`P[pQmrqhO4Ds8CsskNgBVi8]f4roT'`bMu)Wpq_H0orFG:hLF5Sh2CaY]R9V8Qg`hFGkpa9Vg#?%^&RfH2lFDQIs^dnkC8"X
%T&9J6jm1Oerd8d7rVXXL_06AR9jc_jHN!F.h>9.W5MY=t$PC9Xo]=tX$f?OCn3,1B=$M*8UgLn7n,Z\oFjkb75Ps::*:F3pVi+?r
%AUK]-Q4poN9+%NO/2odUrP[OilmLcSG<[N8s77V]O0FZE$a(nWjBCJ90$T*#Dg\\oCOuLkY5$P!hou>=SFP@AIm(UgP(su-:IA;X
%N-MO5rPU;ek3mfZ;nP-i`%SN*?;lL%K8ZLC_;Q@%S&721Vm8_eZGS?-:uKMLp+JoH(J"BuMf$;-`ip_9]VjV!:L=PG^Bt&H?hM+#
%hRg?EC?>Rn=/;6*DMZP2fZY&sEk&P=]$h9YU`3j"(W4ita`%pk1:P]lXg5;%2Wn@`e2=teG=u5YJra$"$;ZN9c1'8NiL:u!I8^JZ
%L:7W>7F,h(\tFmgTq37,Nc:c;=6D'bQI6jf.<]Q_g)Tff=t4VUG+>Tc<UTk%?!$R.nDOraDg31%BR!.o2<2bhf2BCC%7V;NoYZ1T
%e]U1iqY%i(k9"$DHi#Jgc,3=5:Zc$!r8p"h!7M`b&<n9RLH"C_^OQ7=lOQ,$Du*K'p;;r/pu]?d"tAHX\*u[HI=77;\5X\dH/e=#
%^MNcWhtrm^'DXi,r8egbiVA)<gr'=;Hhi9Jg]/cfUdie$IK/`)2p^C]gUI(X!AF8(QZ'T`rVXQ+Ag/+I!Bt?AIZ8j@`P<>S_C%$;
%T\"+RPQgnEho&'8_?,M-jPnI4?N03>b<Q%%k.eZYrq4J/_IF)E5Q(B.rs-1^8,+IM?[_\FjnmrJIJ!UZbgeNkWh7!%qW2L+?iT]a
%lfZ3(L45fqjbeP4rr*E]56-mpp\HrkW?:$I76g\FGAj'9s3"'rKMHFUMf2q9rJ,Us8Bkr[*ZCDu$VtQ>n5']<Yec3Ln\eYiF*bME
%NOmVtQ!b>1H,1[%-#gn3]RTU0=)WTd`5/I"P#<(@q=j>bIGGcRnMca5j(28HT;Fj$>^6#S+'u\3p,i<)l":)+?Cq/_f6Hc-gTOEA
%hV]5uMgldV.D,hmr;,4kjO=)5I0N]!pPh9D&\e,;48?[n[oi1\7mH1_l;t`Jr>g:\p[@!fpWZLkrr1a67mB+`rU+$"hE6/Xp>la5
%GXJ,&kiR[#*8L,2+U.ioq4C#tnDiI4kRi8!ck?6\rdYa!GCKD]bNF9aHM[Qi2a=n:cG0@fQQKQ3nWWCc<IE<Ps7,3/&+AAmD#X:n
%(]X?Hi"*E.+7J7b#OdAR"S[oFH1Lb+%=\qII,Oi;,Q=6aT!Y1f\)$KrNJ"e4?:P1597Q6qLO:2R+`p,Ui1IpH\NSTJZLkROLCY$Z
%+lIRqSrYg8fB)Cl?G:m_%Gm_XI7OHhq>0pAkO40lrV69i2u#.MD@,'c!&K-q+R51o`Zb1sE+a".b7D@9:n*":Hip/9l;*lQEUO(K
%7h<2]o+-<Rl0[TeYJ:&.hJn7a8A0ViP?s:jn-kc(GBuft:=!:^IeAKjcbK;9Wu!_8pYUDra.%R'p[7]eBVZ_>8Ok6=M_3(<dt_p(
%MDEEN7p>a/..hUkH`=R.g'9@)^V'BoGHYA%qkg]i1f#fOMV)!NQIZTti5au[i%6[7Rt1Z$mLB"f_@PErB-pTYk:<biU<q>?1[4`4
%&$K<PIei<EI/Dma_q#ao/+;Bpi[rKVcT#T%F9\(SE0f>qG74oHp=u>02(JW_*lYkdH'aY029!bWo\<?EnB5<H?g]R\B!!/ll&6J1
%4oJZr/\('7"6jic]%Z8&74'FoZopW!e1S6.ce(ooVDjjF?\(mlA[B&@RU?*j3='NK?N'1Q]+PVGmpEa3($+N/hnAmUP(b_OG=U.g
%?b1hLDB(PPfk=!^a6j]B]!if1"nM=75<jGap^npYDb'X"n)$A0qA"hL5DH%cHMu.n-?'":;W-+03kc;Q%o&`>U;=nbBL[P"P__PC
%;-NaQGS+W*A6J]?ooXiQr;,rYIsV%Vs*jmEALN7)Ku1"qPp;Ml4%$PT\S\g2"c@rlW<=U]8qN5"PQU,@n5XoXXdlJ_Tj4PO@Q?&.
%ECWeNaDZG2jmaSmHRs%_<X704[@`5+p[)gK(U^LgWg"\R98Qmp5Pfl.LR-9a>1Mi&Yb\*H%@VcQ/oC]D1q;V(C[c!bGO^Uc55OUr
%FSX=VrPdXu#<Puirg?+_TLhI/S\c8eD-YG^'4J2e%&LrJO)g2WG`J,8S!N+KY^lqGCFX!C9?2-HkB';)H-%]9%!#e]XYs7J(l*L_
%kBn^OE<Z,RaY;s_3L4`s<*-d,&CncX(9/,8`Lhku6geoj]?$Q(DHu\&pd5LV]>OZZ5T>TfUX@F.l/tG<g'.L`T\R]i:nXZ;<rV!u
%s1X"u9u$(6qS3'dYD8i0Bq83b^->Q,nGT&?7qp.DhT`g7XoD.;kh=2h"f;mU['7=KJb]N+&!'n&f&;cU0(:t_qj(HreA2QP0EX+Q
%8VP,YZd@Mp"jR@AM8_E@M^OmM[nrn3!Tmhq2VN[)*+9UhE#q2l6o-RG9Iqk2%l+eO1)&U^alX.>`?h][=d;%5[OSk5@?&J/k&OXj
%o2I?CE]Y&(kKBm8<UiuoWtM<303UC7PuiZ@;pcY<8XQ/JiSlNYZCGjZQh1$#3Zipl[<"JIR%+I(,+SVbSX7gml#JhW:8]>@^NQWT
%!`Ltm,DVII\^PSPk;ck/kMtfO"LcsCoiPVM-Kq6jgiXc8H7%Ko+E!#U1-qfBi;E#I^j^6R^#9++_u]Rjg'[3o$&0T[hcsCND8"G?
%4k1d]fiDlWgMVUO$e5@Z=NYKk!&$>57l^QVWPDdp4E(F0K520%,-8'XXLT"HVFLJ+6e=`c(_6?IneCI7H<,GtI9q>2H=hD2IV+"C
%I;XIRH$4X_fI+$2Zk'M[Zlc4oX=pq>Ud(MkIqXlC+/3)hIaOb7^<tfrrm;e8513ccI\ND8c6g4nSEiL(MYg%nX<:#e*f;m@q!.XG
%2ZAK!lHsh8>fgOOO&.oE"0t9tDVVO*?"fW'lJnr!p]AMDS+Hp*Dm>j[cAL^4Yj>?2R_$"pa_fCTTiH!efXUDGrN.0;2i=)H4Em2i
%A\Ppn*16#(('L<tGcj"sK##(g.u11e\1+YWJ4q_ao2%'Q`5RV?ihZc.n:E)/Bg8#0MKs,Bm/sd%N;VPB44Iq*dD3!l*o6WGaYf>o
%RNop2Va7J:Jm2uE'lHnYau"Yp)O+i9SuF'YW:Y$eq;Tud&M-3h$?c_tSJHV3g1Ojm5l:W8g_lqCi"NSB+eIYLIme$&hQTt,+ceq_
%F[WD(p^qJ;$P_Yi[gdujjA!F`Jo%21]!5S73l++(!RV$<%o=8H\.E?TU&tt!Cnq;Xl2Ss_^I\3]V#,rg8R52D%p.c2p_A%i6:3AR
%-B<M$]KIjb_`\a):,nb'"IXKt0iR$.%;pl,`#!6Ms2"9XQQ#mM253dVcqaY`2Negj(YItCS&ul.<mn!I_EBFtd]cQj"Z[AU/P!+L
%")TP"dm86q-/J\YDaPh;PY5+10_*HVQ8C=\JanLAiCJQ,(5Wkd5E^q-907]r`gRf<aH!(W5cbAp1s!ff0B5rI+uPho7i]p5(5I8j
%'<eIP%%>;b^r^\9]DGrAb5BkV!]BP\E2Cp)^S1j>gai_$kt`l"2q43_T.m+&qKa^2]^1'1^FR"LEmOkF*ahss!-"+Lhr0C]s1Npm
%K;0j0WR-GRq"sS+I_Y+WO'iM;lelVaU2c5Np,W<F*AVN^e+#A1jm]G$bg<nOZU<m]r]\J@3Jo_KF66.7IP820!YbJI2(b1SMp*T@
%;`Lh&?D05A?]j1mqOM>l(&8s7>G7\/F,tMC"8j-U(J!V6SG)<uZtnR)IYSg_bZ"h^*Uhk_%X(h.a`S*ZDXLu,++<;WC4@nKrYN<I
%V8ghdUK^kgfHT>"/eZAh_A8:[<g(*0.2?U!A2DV=jWJ0U':&0n]A/PqAW\"Q%ij-dMPp>Ya/e=+.Bo+0rop6hV$nYS8QO=`d`$?:
%7Soirh3mnJ/1?ER\O\_Z3JgLZ,ea$s\of6s%Q3Ui=uJooR:O$"6?s-%gNP_3gP#kOm`G+i"6nagCpka/_VRVjGU,<!MalQGEn/<.
%e/[&`)bIH;SZ@2l)L>UU8&(.GcIRmuaj,kkkos/eXerh_e_-fs5_:qCSRhW"[]]<5[;^4,LKZ^bG'\b#6fS9-2)LtfPph3T&muBi
%/Tq/cjoYAg%cn9`V308h8V2c8HPbc!^qn@8k7=p55dOiGaL#R_g0&C8\^6@YPhi@fhNhgsoI'mj?@&JSpH\U5m-ZpDD]etigf)L#
%TZ6T'S2TK=.Yk"1Ttp.aNE&Rg9"'naF;&&DiiT.%!=Kr`7'TW/Y-50r',=e2li[oCl9^^%6IY*Al6$T3j%!YMMe)JgAGPh-rPLs\
%L:VtT^onE:,?Y0\A/T2o*CTcIPfJj+Ls9[M,\pg2MkML1M&7(Pk(TmlB<iYO#*,I+7JR4rdB%0$23_/ZQT1JRm:`#`%X,>,$]#Gr
%O)QUFc:P+!j8.)6L[16c[S#hr[oEqh\d]Fcd3^5VhJ@4\]rP/Y<kHWDqe#3_%)-%aDJs1[rk.*Wh9udf)Ju&VV-KGQo5*<Rmi[\Y
%c?brR>_WJic[;;\IJ1SVLUk+-#'O#JQ+4.1<\"45p?`md@;el]WaNlmG#^ee\h_iPA%aqSl_=qOD;S]o+,;`5LAc4!-ejnaFoU7b
%S?uJT`qOc)Y.<7Bh..Tsfq^jeI8ADF]jF>;K;BSl3D'ECHjr[&1AarL50N^@D=H+gg@tJCkhb4an[HF8^FoV0IR$!r\3uM.*D/W-
%KQ7FOQEeNZ;H_sp!Z+#^j$t:RbbYb[I$H3.pB!3anIr"IatZAka^4LYJK"KQ-9euH8J*L>G`j+MEbM1AAs!s1@d*@a8dc^$"C.`J
%-%OQQ`?l-]J,fln(@DKa$VDT>%9ms=<lY.JY:hbVR`p@+<qf.np.TY.9(V$jZK?kg"_(,;N.LgjUS&(kG9j@>g=4f]cIR-X+r!<g
%0QLS!rucLZC=MKHW0+8ue(&-0<t%Fqq]J<D]@PO!2nUC2H;-QTqa9Z4L;WLuXLAXg*N4s-He=*mSA*+pq(F=($?-f^@pR9hkZ.?k
%l,ON5XGgoe;bJ;ee6C^QEEcE;%5UGY^Lkoe`AAidG1XM/Alls$bo1F7A@'T?[?$N:oca[(`GDDd+(snaToGn:^%4YccHG6<ADt0I
%j3),^,We$FS()=YblaqGb&3?SmJ]K2=5GLkl7M@O_N#Q!^;4+$?q5IWWc0IV8,!Lj'j<u6j,jS*l^#I9W)5^5juoML,'[FK1)S9B
%T1[K$plW5D^h:UcGeI@-6&dr_Sd"1\AC"]uT-F+FDon((8S3k@5qM([*.<UOi\q:#)D$YYR),Qm#qt?eFFjOPK9)*WKZDJ!BV%N`
%$Be0r,ZsaINZLKNBeLmNP:Z/n[`:rRBc'YK5[=B/\41cG$KUt.I)/J[SH?/+dPc'UU]*RICF\BFL4ZuElS81EXPmPC=_V9o"CIfL
%JHg3Hg64K@Up/.@3g`SH.:%F%Xfbkc@#l24_-'uIl]g#.d?X.2VsWb#0H=a[*,%ioAs(QCq1;+9BZG]7BMbY4,=7srL^aM[o,rq2
%3%;d@/;mM#P9Nh!coUruAlDV29bGqD=feR0dp)&pd0T>9/Z!/0h4csa=mK%!gT9(FTuM`/d'7CokM:13(9!AQ>r=$('4JGd^tdh@
%%2u!rB>Zh/K*/LB&,cC5&\82(<L67;RD;@oW+cV,`b7pHjtm88PSr,[(MUSZc;*<*TQuEhGodTM%<!`_@>R=Q$g])TS.'aE#oLB\
%T2ndI0V#V]m??hj7XRk(LKb'CC*t>LJQ:8%g>*0q.d\[@js)eL_;F.,[-tLQWV<1A1aC++;hGcIp)a@db2Mq9[@f\j,O[n"]&G8M
%HJigHQ.pGL%2].=F92I5[XH0s$D^*660h?u,Or.I":N"7%lJ+35S"VI2i;mMk]!65'JRm&!T55%JA?I]R,LC;A,7301kUQE/+;i:
%1[WJ^fBi:F&gA)G%[&4@Cbc9Z=CjS1N/O*d7>iMP"cF3&fo]#VC@I-,"Z:#"M,VWh?:5=]&Tf4KVuST=4T#_a#m_OqS:#=]5N(W0
%>*QtiMt!EE;L];6;T\;P+W:J*E>#4dlHXt1S;VpY*@Wj3<15j6IP.A10][uio=.4ECgKClWh-/W`LA@*@>\fc3)8C"$+9hrhFjM1
%#:)26o3aQ;fmnkd!>-3&aRDP$+!(]2Lsu1._G_uGcpC%LWXQ]f#F3;Ge<pf3*#';h>QQ79TR`7d_'k4$8uo>6$)gaQPp_2bT#nrt
%4$47b=^[Ph6S.I.;^hgiZE@T$%(3apc\90#97M-5:Dn,nMGXu<jZsS+<fp.qO"UUrdL!*WrlSYO9cR1YDj<"-1i3n-?)A_q57$1M
%eLLE0oRe;_7o?Kcnio/ALq=QRZ`@=sbjnVq0BTU[q!?rFS4'lh68hX3_nu(<e1r]WZYJVk6-CTA,M.plJI?GXb-Wh7l'#HVe-0T/
%#'B,!('V-c&.l`]H3fTW\4Vu.TIT(F!mu;O,f8l1E&n=0)T'o^'^-g^)(.+-1`DuoUI#&;F0.b0B]jJ1Y\@A,"kGF5S__^$9b_Q$
%J;Q.a2d[f2/mMD\P$eH==?.8M$a,XKXpnJ<&a"OrR=7VJ''=6pVSSfXQZY"/BbN*U_T:n*,A7G6aQB9Q4K7(@`b;"Dl@=%rYW"-@
%Sm)>n_*AG]$Go_YZpX3qKWd(Xp%:#?KAh;be:ONV#Qc(.U3KGL6A[il]rbp"0_R%gBdDiWi/A(dX9*o_0OtrT,QfA27MuIu_Cm:C
%8V!1+=O^d<8the\(]__&F+fM7%Q>]LBcLN\,PZBn__q7kflJ\km*f@^5):Z"&`B]Bd#T.c\83<@Z^ZGdflsoQGJJEH]/nT`Tg$7^
%e9S2Lo7K'EeMWrB)=H#Y/roD,"GMD;$.<=Id'+_^m3i!E)Bs>TNbjDo1^b&Q%A'keZ"r;shEo8#2ka/]#U$Gb1>(j20JX*-;IGdn
%P-%'mk$YJp3#V)OHQcrf:tZCXG!2e*_lYuPW^EmLd-<RkX_*sN)J!CZ.p/&<Yqe#+)J$E&k<)!<3Y\g9%,(^J]+-[M_K"uFjBR8s
%i0^5C2F%[_a*cWl$c7=b*0G8*&X5(=n6kTCUi6^YbL8T#R;b#k:*Fj+1\7]XQ!#:%Jt.H<#Z'U&%LfPeO$l9-5relg*+%f"T.)!%
%YJ]lEOOo0/&g._[-Z`7r%J/=dmmEia`On4:VRVdD$XI(-brt9+gI-R$K?;r^G(5fE9IC-0LEF>T#0PZ`?u't8cU@CsorSiNIXd:'
%2MkVLSd+15Dr3a.oCD4:57$KXF#WMa=8&1VMXr)'E3T%E8eDU`8O+"2oP>])hSN.t]A-eCYV(5[B3Pm:8J35_%Q8L6D4PGLiYO!C
%l)*`BCSmdH`Jo$Y1t2f3"1;%R7UbmV]ac>Nd*5r[%C">Ya#`8'KaF;X?)+i]Z<a+D!3JqnrS3REPCUQDPmI!tfVVMA8YhKAp&S>%
%E&h`\@`K"5"/Eg>/N)bU@=(ZklcYDA/@T.a@1,[C`sk(HfMt:V)KO--;[1g@\RO1l@sZ:.O%P&5e=@9O]n2sT4+g&PH\G%(UNM?I
%eu]MKcDeJ\0=qN_-%IIcY$>dM5#5F2:69H8*&[$b*bY't>suD-7nk!"5bGH?Y/hMjGJ]jS^Q^,EOFClhl@gg2C#r=YYkGu=(Tq$k
%nr?!/\o6$?K'Cr8Q3TKS2OR1u.C$E<a;/,QP,E8G_7=*\=kpIm15@N!&[j=@1SEZ`cZthi[LJB4m\VAsC"t^g$$k5PEHc6+4(d1W
%E?E-tYL9"tC`iD],8h@3X<A:,Db0rErMu$>KOl%W\QG:$ecu.1R,hqm=!9^XMIbaYmo%gCRoN1P_3[)d"<N03LQHR"&#$[/1kmf#
%#*!^)gm](2J`0S-"9ClXHm/8$!l,ZP70#%"IX\C0hdHmp#BfZHN`"-IVWSGX0%>+`&mctVc,*Bi/$>n/0,VWH[,`%I8]dt[_,Q^#
%_sD[)Ni,#1,*48Q3Jng0$u-S</.2&9e+m*$;!j7=!n>CgeC3(%3.TZ9NcZ\>Rb6ZFoV75Y-`dWBrc8jg7mVA,ggT]4WIb?2H%<TF
%1aGTfb_7kkem^,2[#[mj5liT"M(ChSN<M>IG7n&+Hl<BS5^*lE4=#o?ad&'u61C(8htZ'^`]BUljFL;jLQ7`e6:UH8\&aTFPL5,#
%5a;pKc;n>X#G6c,qTZM=[M_=hi`duPM?!jgN3@,<):PaI.9Uts(7XBPen%DD18]QS[h\;bLBcLpfnJ#.TtG.]MS%H5DBJEPC)X0A
%=EMJ.W)]:Z<c*FlSL:)\%("(sAgKQU"Bg]q.lW$[dQLLd`R<n-#o98OVFHW#-<3l-e?+!(e7W22!19\?p4'<2:hN@rE,4^.r8>Cn
%Vbi`n.);k5'FJmo"\kagVDJWrcTh(J$=_=']VblZcXtQPS,Q#=,E8NrT)#gj`;0&a7G6s19M;,h.dHeP1gldU-_F@rW+]b!Pr'pt
%DE`%]DS:(9lD9MHO8\hQme:hIDq$'TGI8h@Vp'=W+,6Vd(CcT&7tMak@fHRIM@_/32<Xb`k+,`^>%8Q09r^$4ch@/>,'TmqG-:WL
%O.Q(f4l`;mXTl>%MrH5!PnGF*KTmm!JljuYGsXBF$0JAk"qFQl:);<2Tr<te(jfRa;rI`0Mb%3M"nlt,\\51+i.u8r8<L^C#J?q`
%$4j/+b$Iu"]c+ZSlgA9mT<8%Ye2V.H4MQ.W2bWq^erinE0-oL8oA-a6JWkH=AY/7&m+LCSF9F0;/Nn_q2:nL4p9Di7jWc'b4RAk0
%\t-[\<5OmpBV"K<Yp;&+@(>PM_6`2mrU6cnn0\1f3YC56P^*(:PcR'[pq6_dma=>6r9_unb]ZL()t>>D`!2EZ<qOXooab^'JUFa_
%5J',qk8pLd_-+`C4Hte%7`j+9/!f+N'3_[5'b)\1ZIVHB<I%.DWq[-fX2kBa+bYXrGG%Q+@lgX-J;[OQ;3N8Zio).Wm3ens$F9B\
%YnE!]Za@-/\s<\:IXZuDlQBdkH&A=FRkdG*6&g#=4'sKKIR/tblhIH5:`uC<r3^;;Rm;MNG#.=ZcWVApj3WZF56(DUrZ#eQr(m9`
%Yf7ET@%B8b0,;:Q`"ljm5Laq-\GmQ>^C'l\$4/gkUL+a[8%D]?k:i:l;',4Y)F*A,^0[JP4$NV1nmArI@CBNVFrtE6=.5sJ)[">8
%7DkW#*)&4/EgdRoOoXKNBciA$m%G=%-;Q'3L9r<1qI3"^5X*+<k$j7[(m#P46A1pm8J375NFNTtSjo]W;Ap2uos0&"L.S?Z['Zm1
%5+m_P&o&s>?1M;6J*(ApIlfLtZi(:7hI)WsR,8Bcp)+(J_/)k'M\hbm-h9"O9T;"jBh"icUQJ&$dC`qLa,,E&%cZs@hN$:@E@kVn
%g=&@%%Z1AQ=KB&12c12Z&FO.&IW_Na_:T_!L+!+YdoJ77-]9]D$6CQ#-HAboI]"(8Cr<Q+0TR;e$``DfGH6`g9VlUKQr0X,;$U)4
%j`csp^G_dlG6d%iTU9BSZ^&:+NH84[nB^@6aelZ1jq7j?q'B*iP7+5r-R3ejK^WW6h-$<b#Lar@C\EcKqX`3lG$7QWmK+HWWO6r-
%IQlG1h<2*7L1SuZ$fi$tBWt3+kgmjfaVM4#5LaoTq,E!rkXXBrL%ll2pJrkV@NT)%PBrOb*<W"_/S@XdlcfZpa"ibV)_DPSIFS4:
%]m"cqg#!2*Kt>atNl0^[Vqtb,+>n<"pX:?O;A1_oIncNk8C7r@U*XjHH'F^t1//O&\baL]a*`7+_lh<j&i7of@PL"*i>-*X$EH^m
%m^l\MmnW/])uXuDS:E_H>D2"!@10sA=,6b\F;blrQ*.D7/+JeAonf`[_VY`l-bsL6Q>FA[gNYMqJ^7:A]6+%uAQ#/=B(jW._6'8"
%"62Z/0JX*6bmoX,03omt+uV6[V6-NQg*]s.UKb\a0g_9F13!l86klR&".7FX?O^hpTb,KN*`+fBYQg=b@,cJA@'IEMH<=IlB*hbO
%$ot7^X5J]2#]l:*P#L=!j@-Qnpmu*5:_C<p\[#shfbD\tY8LTc'!@Je'1d<^A(U?ZR-UXf5rPp]Y1_,DJ?5"#b?45s<Lh3Jp=L3Q
%-qE8[SrrJCL+VN<TVe._jWNN*(CRY"r$Qe7_D_/1R/Q)Ld./0^>_^o%[OI!F,+B9U7;iE=Ob/G&Jqa[\F>ctoEIn15fZt!"JW0rd
%pDW6O4&+9R1JNt'^!`HOdcqtpikXqB='CM_SJ0<#%,>e5Wq\ua*^n'3BjCotW[a6P/OJh3:id\t=`!B>3m9gmUBbf'_8=Vj(cNhY
%kWr?n=+\e0S/[-SqbOkN!).>CGe0i[%]HQ)_NXtV_Tc,'p`/sT(9tl7Vo@8FFac6rAuJY_po2U:H$f#/`NGGK`@`::%d'SKIos[5
%ehOU3rn!<g8C'<=PCsmR;-uK?;e&"XBl=rOT/]V8,<tFB&;eC?3=g_*\3D$6&'[pgD*UTX?M#)^.-SZ">*OnUmU&DAhV(gK&j6TG
%p:MW+N?VML$MT0BqQV;kN\%$ZdVMFDXH,[c;(?no/Stn:Zc!.E#n+.198t<pSi56%/L$*XkQ>.la0<jcX:!DHQ')D/(O&N^Vtb%]
%OT(1gpM8"QV[IdBkJ]<IkS<sAJG7*mMS5;2#&J-nF(FUs7tNDWX:U2@3(h2H8k=fP,6lH*d>aE-Wj&1R4dGE5)W>p6[aK4\RQZmP
%SV_0HRX#!h1.R,+UAcbh)5SfF#V*NbD"/^k`%NbUO1,nTE0?Rm`^LsX"\nb<3pO%]SL"?/Ng%#MjMGDsqiML#2bQ&6"KdWM(<0R(
%ibZi^(-O($/5kY^Iru6t>Y,[B1@ZRN3f\10IcN2G`qUa61$'d<M1b##75OB\_\#H*UQe5*fo<Y\U=8YgR!6db*.bZ2=nfPoI:kE5
%`RaUa`of]d[!XMQmb_MCMJo;HD^:^Cf>YDm?r(%<]P]S:,YIJ=nW(DB7(oHfUA$VY=K*&@EhIQTE?P$$OW["BLd[oRMF:_!E+%jJ
%bV1,EglMKNFf?F7.*!OD<UB8/df/qGj=70,,k3aj!b;lP*5?G.*$A_o$UksKM+H2G"O0HaZVVeDs"pfHPsodXZE+X_-.8S!FAS7)
%7TkE]MF-NIK!%OJM06rTOX-g?duVBPQ+l\t#+'ecf5,!For+p2c4U_+7J=XCn2I+1fDE$PnB80k0=E'Ac^n1u]GgHkheVNuCN@*4
%24j4'?1gK6o<5hO#:Zd9<6s\]Nmu-3]B$BBkonJUQbVN6@XI[354ksgq7)uWjC^O;\sH:J>,0bPWUaR>2j0Ed#3k6c*;>m9"Z>-[
%3ScFDr;(UrWI:c9s1B8Jf^pms@ZO*Qo-J.?gbD*4:Q9-]EQP"=RZ-DD\7=gY1"GZ?*^qhUCA*i?US8Ee;oQTMng]Y(b&2en:>$@X
%F*>OoA]T5"%)P_A(,a2iX@UO@W9Kn;-%4(>b_nX_B!ZN^,$bR6p;G<@3V7-PSda)3BmOWf?=WTXk39Jed<S[5$9pMIS=\/cmZd+d
%7$`uSC>;&!kh\G@pE#s6S&"lI'mY52j3&M9iYp(sGSkh)[t>Pq,94b:N-[naR$1B%pO]uSR,`ff1MVBUq(V"ap3qg*SDk@Jkf###
%g^f"AB5IP6jp:1Bdrh5-Q1@D+:YrCf>*b%R3<n[EJp%R0EMS^93HK&^>UZ6@@F-gnR<d*%CQ-R>fB?Hd:C6"C]WLLIXTL5E1UY(<
%2\*.Q*Ac$/\]'G)N#Y1><\*%2_\;`'_(GtC\[bl;]BfF0<GlSt=QA:[/RM_s]8*P;`YD(Re0__Ef%D"jDZ5AF_[S[8qVgokb-]Qe
%"?kp_RGqo*[^(7]EfX/e1!6_t.C0bmC5o1VCV&1R%-*3dO*"a4AlrHF`okn=jC!`4BEhbc_b+5UjPErmbbT$0\R/RYdo#Q!e_;?0
%rj[F/0$NhsLN2GDj]eT/^`)*5\%DI$kJJ>&'g&Ku=HRHE]rq,#oif&?;IOJ2mF2YLV,p]r*";AA?2$W8o8[;l5CGuG;=[[+Q*!Pm
%s'p3nH!do<`*b3jT;aG9j)e\Fa&qSi<%BtS0]3nRopVE-kGN0"o'4GMcVrkR=E8`fH#qG]KHs$F\\AO?H09tJF*827Rn;."1M9M1
%1F>5Jj?+(E(b?89:X+M>$YapYBM#\b/:Pk)RkXZeFInnFKn63RUMhs@mA^G9kQ-mejp1DcR/.@PC#YVZU$s#kmA^/a+rn--L;:nS
%!C`9(a\e#/m##eX&B@(m,O<)QXBugZ#]H3]GpYY)ZOlM78V[h>,2g+=:W1c/RF5V>MkhF)EkYKn7fG6!B+Kje`fm4P2YfB[hOW)u
%%nTL8UeS>IXB9E?XTK_<"?:=]!c]LlZls.oUb<TRr@n?MrbTuHjL82Zrj"=iGeW[`%t9^oHuGnSF.;4jLFZl(/Y2OulDPJ&_./'K
%),U^S?`2VhXATjpM$nOIdmc.O3A_L:5GlmO7TA<K5)h#ao*YG9lKA`qgV2ebs,fK[>U=K\g]T[H?0jW`UY<b2T,Sb_DLeXPY6(\9
%Nj(=ZWn.-6JoA-WPG:EbS[PXJDGu15GBR22&MiMB1i@`=ObnQIXmRmh35Jo)iup>8.(qK(g/i?tN4[,*Ejqh&ZCI`(/2VR<'C^AU
%&k_%l?Jpe31_4P:+)&kYBYGiT5Jq.q8r$OtebKO-<4!PG"f9g"-"]8Oikq,p'+[7RFI3:GTZPi0BCO^oHM>)c[t.q82]ca./,c-j
%D3Al5C@\h%T=XF[UUu`!IK.f6NuqY%hR9%-qVL:MP#Ch1oPNpTD<P^X-d-phQ!_Q)bLh:1fRAm7MiqiKZ]/dT2LaMFc%u),,N[q*
%,[;T>dRT6H\jP(A08>XTF_mHd=bhLnS3",'%l%h"F5LRZWQI;jc/uZNruoPMB0JkGnpQl'4PQl^jrqo+/"JiD2QIt0PL;IJW?=6\
%Go"(!%rkE0RL'QQXI?9=:H9.]:k`&gU?Z&Sm3?2lG*JU(Kh`=@<M1`c8=msm'M6SYf]YkP3e^+"*_LO'8gCV#ChK^9BgUL+$U'gK
%f$ll`qEWQ7F+B8dOO-?-\u0MSc]A(A*Ub-4;P&rY]`5K6M;tLXX3];5\B<CY:H@^.Z^9X0"&1Jo.)1Dqp??30aS?$`L.+>_TTQfk
%/@&HSWmC=i7uO2ZG%RgL)-Hr^TX-Atm*+[dcGYI]cB_,2=glg2lqB*Z'G/eL6&H"1>CNe'-p>g1VYNatE1s2W(c%-`ap/6Il@DnQ
%8eptD)0;V=R5'T8.g,b\fqLFtLa#>M?DQWI$KL@T1n=eUClg[(7T:(VSDQc\:ap"k4fl<#>hGLg<o#Zud78hThsRK.f8QWr,rqq.
%Mfo5DrR%i1\:%dA*_ubUrRLcAG/r4q,nEJLErbSY5W\@PI,=CmnXVlBBXR9Zld:U7n#**`Z#8mr0jnM:eT*bI[/5(14<rklN0a+`
%\iu0E02o&ANTlVo<+G[P:fTdDXPpLb$<0`uEAgFhRQ9L=TD+/hOE#%r_p'_]B`HVn$U`;tT_.:m`S`3!?W$cZ\3%e]9h3+Z/%SL'
%oR2]R0'hp_(4))(J>sM"WAA:(7$t/T:kj6>Pg`"(inlP2n4WcN^U=""ZT(X'^e4C:,UblIBKj^Bcai<kbq..;cR7L"6a.^Qj0G3l
%WFn\,4#-:S8.3ZeXgt7LE!J8Wp@BLT\$Y:ap0uJ=Vb+n&aia;-qN?IS"gOc@4uN,[-V^2O\B@<HI0r"LQt2KO)EWR,=P@ahS?@pT
%2qODk">"tp-3:D.KqZN^7,dh7_$\lF:`hiLDLHQ0Fu\hW6iP)s_m*?Z6qp^(g<J`hPKf4#TqAQe)DS$"7(nQLTjokT16aha7e9SP
%7kKcV:Ldr.(ah11:^mn@i:A22)aaXPUW:uR&:R;fDb9bu`]Q\@eY0@D$qW(,P68i2DunZYNOf@/)'L7MCip#8)EW7Y1qi+k/(SRc
%-D0[q`O8I.o<7;`Kc6$B\.6t.p6RV/C=f^!]VCEINlh@&4lpSalR=!rFqQMD4b^Dfada0q7`/WPAiC$O's$Zl@!X/5R1s&"8CK\W
%j'suU=#FG#5?+!3GXAY"Srq<@66cu8<6g4onLGXP\8jI'N=L7]<EMAIL>>i0p]Bm-W2m+TW7eRb^dIi#IY&dlas-Dp]M@mC"
%[Val18iPpZs%VQErN"JY3e?mGK0e1b=4Sdd/J^fQ1AdIs\+qoS7*iD;-sFL=D%f"dT5F17N_Kn4J"Y$^:l1IA>7Zm"+"FIWV<J!n
%lbhgaj)mM_6f+'5.<VPqYn6YG6QE=rD:4_eC7"OhJ6Dt2?Db#l)KeQtfd=;>f6XW)SJF8Y("uFk4`3#VcFGI3SAdX`(V3.tauSdR
%B&;fu-MrXnlSCmK]6Ij5>pR6SO-gqtB'7W0orQNH=jmj+,?BsLZd#kmB7s%6d;d<-\;m-IkCVEUTHK"s59!Xun[b%9D/E>p"sfTV
%ll7\+O!a7-e;[E`3g!FDXRmZU%[]/m\e&XD-]7mm/Z@DaNYW9l`[PK(Aotj`XW@>N'"dtKZ5<F#f'!m'\iAq?-aI<=2FTW/4j07a
%RGu,a7#rALA;Ygkb*fV?Nq>1OK@uS*4JT[rc]N.a"/FM?d_POU7*mfTNRgrVgW5W>AHuk^M+Lk)l:,=MH*['J_n#2DdNb<_\_[M!
%H)9srZYrab80XpHOYGY+X.PAqkgp::mUX#fmb(@hXA?K&mj+ll^;m>,_eJ"pbM(f6QBPHbGB/,2Ce?Hp?-0OMCmk<=bD]eCgqR;X
%<MY9BaqYcNh.L7.(@T;)<q*dSSEcd[(BVUA1ei&0Ku@1(O]5hl`>E</)$%/ZAKe>SVQX0NKu,2P3iUK;[FSMTD`tAf._mTR5h`h5
%W->(YP=0:2QW6W;-3,[hE-dCdi:Do.S7m;51QBp0`1EVG]'*#:1e*uuT4o:$G@l^TBG;J+,*]k<8OX%oT9>p\R!pfJRSqc#U9)X&
%&L]B#I\O%^Ff&k4OZF8hpoS-e2.+=nPF'gi4.j5lLJ6oe.H^;:Z7Q69+On6%?S<0AYtS$!)S7c%;qr]q'_rY:YY8AANGe";mt43t
%F+!>7#94a:T`'jiZHg[SF0'2=QG&m>:CO=$@o)6r)`+O9ip+(@GF`rgGLiTm#bf7edcKbQ9?BH(p4:I+@alWGeEdn3QgV<.3GV[A
%P1;@"NS1W69.[B5c(\EWeTA<f05I+-la4cp7<pQ1ZJ;S,h$0FJ/6Po#XMM3Ub(rKJpha'5>LPk7FP0;[]$5b*6,ll@q+<9@NW"!I
%IDOB[_:*eJdsT!\I)49Z_:*eJdsT!\I)49Z_:*eJdsT!\I)49Z_:*e?f,$Qo(HfqY4O'eUp]XIb"]+^n:NHc4k-Tc=\J%gB29+T?
%QB%rt6e/8X2-XTc&mr[m>FVa1MQdsQ2N"e;S;e_0`6r8OP$6U$k!i3,4<'4:DA7@0A=saVhlo9J>-^]4NF_$(SJ`=KO5jg"bbp21
%:AZ@o=AB5MK[+g?W1FU$\a;%j&u_E!WTSV?_=$N\6[%f-m)3Z#l^#9PEC4H_E:<H"+j4qV0l%k8Q+Ij.HTY+?#j1"UX.LCt2WhM6
%%^nTH8-pW7:\*^M5W<s2\i&t6A_%pogQ0CAboVPgM&Nh2f%pI6Yt`jDNdkI/&DVIn`b]lB$OGI;,iAQfZ2(aVa%]_"Bf>l&+`Hd5
%qr1f43@DtFi]G<c&QsC5aQf[2r&a)js&.ktYF\`ud9^T--QR=eks_rGZWlZ`9;pS7\lEkKMp?9%X:jD=IV_]0EtYP4G\YHJZJ<Vq
%U2sr-j;D0-FqW'nC`:n9GXMb@6aOZRHa`it_d0JDp%]EH,Kr%Q29&ADasSDch'oH+0/^:OkL2MgR^QFfo;Q<qA'.QK+u.nP4pf\^
%#HCSt"h3A.flmn]k6Z^KT6GH/P<2.WD3%hi%p.OuYhcKCma\NoK^%fFcI^?t^qH1[1liZAi*D/T2+?sn*/6sAp['uW4!t8O:CuC[
%KPFb_1;*-$NMs`X1ubN/LsR$EGg68qLLM6\A4F>K**21$9n9m"qdm,OXRVM+9=I/KoG_ujp+2M*.!]%<P&kR;YAO+1;WF)^^XW&S
%C]$_.n\*"QcssPa6#Xa>1S]k0pu3%*fJ(=@^$d?Ih9G>iB]B,3XGCAkj$/""5.b6ej*f5D4sg6b8`U6TjH.Nl%I$'3A?KDA&!lAL
%qWSHH<1\0FDmR/:_<^M8s4s4&5Y"b6B/ltgE4@S$]-:i=#<^rI^?6@D]-fR?-MRW)g+fc)SDc/4KAXcUDr-ZF02Xj\eMA0l$%!o!
%%5QhsB/o7Mh_JE^af5h.hO,C5I=B*4V`1^&>ku*HY?]?OIXdCQ#9hRV<Fkq@CcW`g%m0"I4ad)ATtZ.VLXjaklFKTj@F[GCI71/t
%QuuM*:u`J)5,-[+.Q^E3p&W,r+k`R)qJnZRp)2IGUYBF7qfqPX)XBR3hRCT#&cKF5_%14(3odK[cC;+>27=0A6Eb:Ro3345=Mes4
%A$L)bB$C3Yo$pXHKp$K%8H!>#4eNdL'=$sXa+kJ0E?-LtM.J`;j0QBFr9'>Hk:.PggMfZ)"3!0O$-D_A#`!1m%a[Qf9A,gF^5dA=
%T+AXLo`-kp-\]SI0/J0/-I^cZ!LYW%IQg&Frs\F(N`.s>8mC8/g4UoD,:Bo3'QY7J'g$&U/n%]P?<aV-UE^*=J<i9GasefM>m=n/
%-^Tm*lRUeLjObug?&IHYY<9VsBP2ArAaf`&b?_R-l\3[^r1#'-rpA$0*`D%D8N'AFo.Aq#0qa:I5!Gi$T96?//`OT#bL6l8WrtOC
%VFB\5cB1&X=H=a*I>X.=&Uq28J`apY"sjU*0eEj\Z3^ss[S!IPi/?p"n>@dHU>ShLMGNPhjUu(>0=M^FV<UCekIeX"aJ2BM<t>^t
%/')_oQB/+tn,Dj*)Tub2RTMb!AA[n?7O9UX0Hr,tieTi'T/7a:Rnk+;WoZGrnTFiTa#%?gVo]IKYLdgj,ZCkJe(ZZCB2?.'P@Q_!
%H%[bni::QI-j*lQ+<f5^JtE<:)/>ZPVAiOKDQLnuK>h7si<H%\-)>Mr69H`-gI(9rdtO5B\rn=2&V5>\VMj[=];qX=CBK;2A-W2%
%rC>\(7o:<5Zo3g#rM:Z6f8U\Q_o@(cm0K"71P[qgaK>$PF`?dWjZkYOZ5XlQ@;e_LN$+Wi'"nE]9Hp8gYV@%Vfu/^K1oF+T,bD&f
%oD5FV'(u\'a*d$sGrkPq)`].ZSMGbgqDhT&imD^=mGg]WimD<+'Q[nOmHA^SY#\?&.WWOk!h/@q,N)""m'Pemok\3IDI"[EkuLR-
%>#l7W6bDt1-9l0(%/tiY$NDJ6Gq3#'9-kG9[/cMVR;^CS0tSHt$JI$s.m6FW9tG$E9PMCI?"5#"AN6E.l-E-$)@)WT$SSK$#1.&Q
%V-r?;O6h!%GW_ST7;43$J$+'t(k3.g7pL0k^<2PQ@)fNh:iV5JlUc%c3t"fC_C+KKAen]RL1)qMkKOURLL[_gB/LHsL8.(2(A+[W
%1!g?Z;e)a\$G!;[+%kcQ,]Ug?hXlZ.+eY##_A)Jr6CIInXsS[pdB01(SLj7KG3_[YVju_D-T?'R`N98aU(`;L.o,*8EH9W2jff(!
%WaMu03-7An=IGqLcKB!%kutS`'*[hL\F^3qD*1te!\4O%4pmX<?jR1la&)^A1E@".^4lA&NX"^*k>aNZb&LDk3sHue.m]/1_pZ85
%Bd\&CIc:bsa`'-hD2h0RZl-oD+_`VQ&K%ol9LlG"BPeqsFs*es#a/TLe&pl1)gGt)cC>5OP6.MW^K?n+7aoIq%N.tZ1O)obr"6Y(
%K%pROl3+F4iMet^=(._g##8QURt-DWb[M!Zk^aC15_:">$9qE:'a6B1"@rPV$F`#4=@L%M*(V.sE7`KIE%q2mJg$.b2Js-?_*JZi
%VL>+GjNr]eJ]jYg*oC#]JSCL)"A/[?k?nT*Qd?<$*os@F'0U$'LJ@EV#s1dPdR@gL`>Cor"Au8;IciDB<?MJ3msDkbqr&7@"+Z.<
%@$#-k3aq:uJq.908f4eX-?FER=3)B$0SV9?8O:DdKd:0::'p2di<s9;_kRAfe0/X::'W8j5igER%9OI2JW)$D[n/mK0SLZ<JY\ej
%6m9^/HO`lF7<9WSQ4nomaFRYVJI.VhPR^m3$ipZiSh>,R#<EN9LCf,&)gFQnN"DDFB8$PECM7s^P)BI6)oM$h&FhqK&d<diiV\UQ
%Gb$41VMkQ94_/q&Oj&EsJU]FFF^)1`MTpE)P_=uc>N;3l[HuP'j'9^jL(54C/MH#ICM`Qm0=]fq_rQ3%pnh#D=-GA>3_so=NsBHq
%aU15kUUSWcj7`tYT;TDMbWnGhlK@Wmrpp#g[i\qirPOgHUNpPNjRK";Im]i.rp9FZ]5R`EbNBs2]R9bQnY73*r8jPhjKBNsUW:R&
%!NgpfrU]&>hr,M%-_:;20DO0!J9V#$BRdaQ2a=qMVghie^4pS(p/d:s4fN,TU]937?SHsm4D9U2c\UZ1e(>FcPtk\42Uqt\]u:rj
%!lq:u3Tt-DbJhA0_[_F9"'X&Do1L9[@e!uQF*RUCoe5*uh>Ht,Cokh)cQ6lF.^T2i4Z9a[Vm+DCcmW"tGL<7EB1!E)o_VC#m4#42
%nQr=]XUXFO]$@?uWT)U>ep]9>H*&B/7c_,[Ak`e31G%bWP?akU4jU:\^N8]VTB>(tk12-2ZIh@n27h;D^@abKgtKY&8oAukpCme2
%m4GKgV4Pr64+5T:q'dAM]C#+[[&1Ns]ge?72BCZ=Rp3A":gMl5\ipQHDJ.Bt]%"'sFf3-Zs6_s2hXTC9>@lAHd#,7p0D%t7QD_:)
%h!$i0GS`N\S`7(pGL...s5PnU40R+gH@T>^E[Mm^)=t;.C\6Ho^IN1]rT=PDV_id^qp3/c?#,r+#5ZiPTq6&l?I/*5UDfF\HpqpY
%T;I_+[]3)pS(-c77dp#<EMAIL>%p/cV<]t>bc5/)VEURrV"h$Bn@Y>]lg7!\*3KiUMk&;JZjjA]sS(GTVh+Y4+4h3
%YNHgPF),0)2=,EEm$ad;FEj_0\*3KiUMk#ZE_^UsF))"b([U,8(";F=/UBS3kj)HbqD<ik]=i?4g-G5bhR_2!7GmFA7c3"44@N\)
%HgCSD%4^+:GPZ]F?I(M*VSdsfKBU&jlh8iKV/42`e)ubYoj"7f\,O"s4FZoA&McX+SOLa-M4X]&Ln;]fCYHk%Sf*!D3j6qX(!j=r
%Z6X2^hrb_GQ`-'e>Fn;\aQ+K[s0Q>P<p1P:JM"F&=Vtn`oT4lF0?M;F$EW.hf)YEr`N8XiddU]NcP6.t0VH,p,(b''I-0f'p,Oc`
%30;M6h+MeiGZ)%[@e;bp_UFCJ+;3L/&*bUQ2A`li[%Tec<Y7)2kc`KDfS`$Y@YpoV^lPqgLDSF*#uBfKCOCo6<#,,"Z;eKl+X_.;
%gt%+Tb*BeES$[1o/"m0/CQ^k-oX&7>,Cl'a6dJQmZUtqqY`]pOR*.Wu$HsHg!Eoo#/U+41geS;aP+,#9pbdMX-<CuMU54jtQ/X#@
%;$OT7DNfR=DK_ZPKrmSr"]F8BKqZSlYhpje7RI@.5p^G.YDl:l$T(#^*.P*gW5U#k.XWK+eXP9")p<le)&KqO!V$e5*G'N#NIdg^
%oF,L])p<jQ0<>H]IfVE03]=JdR7OI0WClckJG=gUT<u(Ys#@`&F1,\3Nn+W4is[\hjHTl@`%GO%I7[(>\oV"e:0oD5:4-6Y$US*e
%$V\bsH"dT3oblNZN!nEjTd><_&n7adA//&S4F?=7^CW%&H9g\QbTG2UKF$08dH@GrCu.a!=@0j<!ei6NT73Z<"78EYZfrJq&+HL(
%"PYDi;_CMNO-$[!mRZ$s(b@Xi8Q=k=W;sMJ9iPco?m))plA[fD4X?gde&br@9gn>cjIX3qI#nA5@VeAaNYc/C#CJat"B&)>aM-7F
%1\%#fqj+Sc_7FZoY+QQoaP?$a.:eisW"9=EG"lQpN1ppu1Tpji;8(5.J-(RdH&0^[p+W0Ua)kC/O*H=C<NZRTlUhC'Zg]YJKIQHN
%G<AF/a;T,pAH>AlRg0Q4I'*k]k\6.tE_*ueM(t]N98F<(>_k-#Ppm9j'm`NYVIM$#]:>'90,e@E7O88J3,Jci-O_GJ*LSe[OjW5;
%P7ap+4C`-b2&,gkkG\gqKEX<JP'*7&q$0ECGquVMQ9G!&!fL!l&+]s+WmJFcXD=h$F%[2kLcIe\C!dM=3\b6>BACRr(_c9E5YR3K
%3Y5`/OZ="^_=db*`u]+799GcVM+n0q6ng8UYj>ig@)1a'%BqX@1;a"URa'kEf,[k^S9Znt=Ca5DdRW0A(@2395!Vmp)SsDp%n)Hb
%6kJlN'1$`6Fbs:ue-rfVp3"eF/r>r+Cf\8SnrOp*QVB"V?(=_e$@7-e$li9f]SIR4j;;(pTRXP;`*VLt%712g</<A2:TCl![#!+D
%I)`th@\ZkEUN-a5_X\C3EN.FG6.P1pI0.@(/P3$M$Sa(bj(<7.I4"b:k,d:i!FG5PLu"FSF_D)fO+o[rY:_(e0/BeXSj,K!,1`GG
%!#sZaGB@;-/@YqkV;P+@PL)T`D>fJYHdTb=c9n?g(X.'-A/Y4ZS/2Jdq^06*6=4='GZ&7L/M+Qmi.]NJ@0oLURDp7%7Nt$]>l2aH
%;LcSEm;@"adoo*>(4#fkden0EiJ&0^n>k(%6h\HQi8X$<Rf[nA@%#I9^(qXkfNO@O'>;32kVKanf-O0cgC+`/nn2R\32r99)_5p?
%,o.5KOcC2&VP?G2M)=IQ?TM#lZj%Vc&mao_OJ7=G9k.l;QTB:+<U)jM=!Gje+ei?&L%Zi?!ogpbQ+&`CNfbt!r?_'T!ihhrU0?#b
%Z[8AqT^Y-M[rkPC0hD;:_om9]Qc0_CRPABJPH=oNKpa\+?7(L?LpjH!XIg,m\3sP&5SkRSl(L/K33i`!`2Mu)C06u;Bp5h\_#01n
%0[SH.Z%OZ3KWuS_JU))cb[c^jPo<Y&l'BdMk7sjfbX#5M<)#b\V$f@`c666DQ;K/)MssG;-(>N?'\+ku`/BW=.:cl01d"RTBUk&r
%!rMOT7'@T:.[V2MCFHb;O]aef%?\V#^)co3'4Iig^so.aPfQD*BHjB;Tsf`BV#Ol0MDr0GE70.B-6VZ2EkQZk24-h`"1_N=&T,ph
%L_s7^9;&YK0EUft#_Sh8fEJ!](PAG_U^$%A,a)mU+VW]b1kJ^So>ta'Up^;P!=_eA(S)3cWk&s6UoLOg)tMFA-UQ(`G@JgT#agY*
%(3_^g-21U<?>,_3ot0T5e%nS$os00_1Ol/tG@#lG&O8<S]`RsA[1J(pn5MpR>cn3T2-4'75fZ:a9ZIk>E\Y+s8D,CD=07$t7sL?=
%R;`.JGZrE0'@Z,!Q'DF$P)Y0PY^_OWN>AVG5TUJ_MPUH*Zhqgn):htd[)G?:P1fbmda<'\&"ZTqHpSK,2QfPL#+iC`B5-rkcPo%K
%%rU;U\;%u%lIEaLZs&kOOgP4h!U+?K_2GA<)<f&hUd%E]Ag4?t-K:?^6>Q'Z?$EN^n\ppp;`m_j!Q!)4:(-9@%)SdjYj%e3eo`+5
%Yp<CB$4,@.*qsYo;j!gOK\]#>VJ9_go8h&C;M[YP#"sokZU"\L6+bFq1k9>Akg`@>XTE;3M$IUsa$G>Z;.@O>QAGpFiX9UBB\kJ7
%>^];i`E!a((Os!deCGf4N1rbV44:Uq.+d"h<#.3UW)LC8Crb<GO7sf,B=P>5dQ<hY8\\MU]V0><#RN6^-C-FHK#[FK#-qN2pcBCa
%U=91#9+W9Qb4<b%9YSjY_t;ANV[9s3[(ir8!NRb_WJ:Vl2bMD0Z_E<%Q5^Gb[<ZFgPhq2W=0e2%aL`gPNVZ/*9IJ9hf0C)h+p-XN
%6;3CJ2GgM//SGS^V0KM;$pn2NYYd,XYt^77+Cmtg(/oGcmgDIC\t&hjOc("gQrqe=[,#28]>^)tVLQ12$rrN_U)ON,$`.(K6EM["
%_)nqLZJ+V0-ba$.<[:E7*m$?7"KbUf%+NORcspp-XE"V.N3%;uLmF$EDMa2qb$#7".G-OK_isb:KeGotUhL8J-O^Rp>-WD`3)\h-
%5STA2jX_=FYtj\3RsqTT+'!/S)cURT[6(BR"]l.C@9qQM[G%n@%>&(&,,4Hnl5_-FcR(S/\ELmcd<lVL]f=Vs(htNoXLcu=:3LYq
%>7l]*'jW1TZ3X(?&:h]^4#-u?60Z[R>j+TX?Gf1:TKSu7=AYCDf",aTht57\U$H+ZMHF5P5.e+'[;g5n`eHhpUY)$)9(6FOKe"*K
%q*I1#[4Epg;O:Lh%A<&DOn;?p_E8B0de6>.k9Wb?G\^j9SI]0G&(2D'"?%<7Rp0QdY&Z=#bjB$:JcB2-Oa''OO>F[[*%jf!RBF[K
%cKu;Q:Ij-C\A^MPiTYEm"Z<_TLk/Y:7q<AN,'g2">`3Aq/M8*F@6kR<=FjD5)Ti[5fAKM!QX6^TPsOBOk"EOK6/1<9M@Q1-Kdm6Y
%)VA5Qi.H`]4Sj])=edmg1JT-'Q=;DSp#j\lS"r-e36+G6'T!Zo7K&uf9]A!8X\bs*@ci\<_nV#\=Dc,GUa]^3SJngef0*/g?0giT
%&6fQ(68L([a@/b04t8NQ9c6a.dNQ!"Z&8V,E.>Fhe.^.tM`gmP:bmNq?Ql8#m>EhO[qdZu9[md:9A'R/XK_/m4H,9i1qN1:=tUTr
%RYt98>gtBo)t+<"qo.ob,TlLj:+f9Me"Co@Y>c]t?=ZfQG*9p\a\Q!$2tf?uo'_<UPu=AkMF+VJAe#u"?KebFGtu6M6OP$)VM!)M
%[k'$`/VDG#/ChiF?G'T<)`EWR::NJ9GEH#N[)S*lR5>_W04&oa.4\B"\ormsA?h6apHiYo^1O@<M$4!kIc*^(4HS>/_,5]VY(A",
%(K^Hg-dm52O"CZ;#AJm'0t8(SRk!+R0JHcPLNkPs[&(#[p4P#bD%Th_8R1*Fau3:BH7am(6eb%>2[.Q4<8/;[3DkHdEA5dO[ah%q
%X44sd;hA\aTnsd$ShsL<n$L'5S]!0]`'&1dTOGp7OgNXFOU\[reMPiJd5!"74GjFCr^A*^BH&5R9)M>E%<UA1Z;&ie\sV0.5i*'3
%_58Sn7@F#qZ`qbD@d9]1PegCrj+Cg)oDrdJY)&V8`F%uZB>ti,Cc9p#eqTQlb51,h5U4k.Q.MZ<PC4%.$YRarZmms$LW1RX7*hGD
%Pgm)_ESWO`1'M_nTtMqVObpja9Vh&O/5K"MMJD'P=AjF<p/:DgUQ7Yn%]\]cE`[Mg`H`qf+R3sQ_hne^SqEm)V7EUo.gcskLiH;4
%2iW[%@*),k"6-jWTs*\!<ip!pNes]^FK9L9Jm1dO>>ku6dAYH-L^Rp&9k83Q'YkC#";ao*&.Y#I$:L>.2@_(a!jT>`74LE*;s6qp
%#p@6l3<b4Rrrcg'N/*9A'>@;/)q\:^%1]sJUuKTg,+8XXM3mBO+9+pA2HLZF4-0bDM/FU<a`a_X(Loc6&TM?p@a+sK5m`CmWn>!5
%hjDmpb??ODb2OC6\,6qR\WH5j,6f&S:d-fM?EK+e<gHV.j<hi(7&uUh.CU(:^:qdpXUjZ*6,J&Mn,`l/%b8hI3UKCGrN19*D=Kq/
%;(ns;$T"p7TUre7S32M+D<U[^&;=lK>n_5F>.=A?V</&hPR=LM/cd_QM,<DY&Wo;`cSTWB+l2:\/ch@6c"Tu)<omZK@'^S<>D-+.
%(a=LRS!e:q<LXe%9=!ghT4BI4*3s&;=`bJR:+ms8YI%>0%EEu!RlhiE5LJ4o[mtW(TnA!,2Wp>u@S``Ag)R'IQ;5SY'&?+Fh&a7K
%1kNu.OHX0_U,p1`2epM4:iJ?/iKNtr!a20<$H"re-jUpUIYI;S@_qK62RC^?Z_\@If'58"(imJp#$-b8h3YqiLcfMd#DX4!;<:M@
%Hl4Q@"l#NW)uU<MSdF]jdY8a:G.W9+Rd6E72fu:VNNLsWU;:>mRu\095jV1A"n=j1SuECXKr0o9Rd,p4(>7Y]LE#1#M06(k=!%6o
%=!I'p0r]@j]a:X/q(V0@@OJ5:`G2#E1E0=u99^I1J25pS3'?Rs0!8fPE)h<jUh+U_;m!@SVeSIRcT,KGVcQS?Z7V.6TiGERW:Zl:
%h&C>+>.9F;(tT-5:5)f/#Qg-2]5FT@+!];(SJX8!`]R*SN#G5c%%T3'f#2/iPpZZ'>"Sa-[4!h&[@(S0'<hW;L:2NdPS;fe88cVF
%A<CB0MVE`e8(R'!N3:9%-a``+]T)-gWge$GC2M8(%GgI*."WeojHR+eCZ3S/;.mLFOuP]CguGKj'hdjR.)_UM>)7Z+orY#8Eu5[e
%PAZ-b:Lm'Q*HAu)LY#9g's_b60hMD@\u5Vo`/>OZPbi`-e+fOFb<uA1S6rKk;X1Q1&W[$>l#^2jEQZXcZYlZp(:k>.;3Rk4F^"1P
%9AFVK('9:;Q1f)3R9dc5;EVWb[3DggkYfAn_K8nX_mUlnChnd#EM7\a/KcoQVi-4]1UmUphr$Pl2OFKVfM"_Jbe)`r/lhGs$rXk7
%9f8QUW8i0"lnME02if&gRihXnk-tAoWJ0i<5eBH6]Y.H]J=,R4H'DdqHCZ1>BHf[O7kJh7)]I6n'H0OE5b&G#PM7f)Un@`XQ8*D4
%08R+=l\`nZ:m4^C::cQ=6(q2RNo6f&jQSqnZF2=jrmI5;[fQ"SJr50!fbW<E=MQD\I[N#>G/]&pUoI<AM-JbLJm35*d?H.c1R/70
%AA]%m'ap[J)Y[7i>&*;E=^rH@d6^>4k5iT!A)n%fJ8d*nVo^bo"D&CU_.:V<`FFT#)R#mJ\5'9^^qOP"24R2"M;cWg6UaOP1f7RY
%F9M8n0*=Mplr5TSP`/HPE`MUUY-:*N]?=*K_9`>(Lr5)cK>QOi`oUnN>>LuK-OCVF0!`'Zom('!M0TXJ"rCe(6kFR#qKbc?Q%eA4
%&7r;JcqI,<"rqLqQNtBSfeQJ&p9lf3F<GHD))FQO/*!*103Xb;+cNljX!&l'2IW^2Qtj*0"q7`FBG%n+<Ql^XOp^=UM5c>MM\oX$
%+f`@(/EVik"tm?H-$:C^5;fZaD`'@0N?bJ=)`&D6Zf3Z*2Lp0Y<5KjsNV,YYC2>#f_/#lWmPNdmBH*%./'Po+EMQ$6/Sa#_U7m-B
%G$3/]2UZ!LS"gS@WrpO$7^k"/j6ec]-\a,$*3]l(M?V7T+O(<<DR;=];e1T%AC(5X]Jq;MB@!preUpFe_:g$j'=brJa"Crnd<_pX
%Cf;msFq=pS8Z/8I26me-,!$C`(.0DKqcpF%C6/uQ&=5Y26=a..DG>R/I9D@8JT`F2E%FeuoHBXsQm;],#bm>("Lll/$a[qPa48>E
%gL-Ro:(c6j;IXftBgXN]]rJ?lWBQC7?UZKPq=;H;V9[^$=fckJ.o40U,us@'Uu;B59ke'kf'1%.80(mHEK[+YdNBns%bG6J[TRQB
%>!e^t)J!h6M)2^O[U@!kiuG,N5HM7D,N:Wrh?=k)V\^\s7uLh)<Ac-Y3RW(gdZUY+C*;'.oCT!Pa.RcfmOrGn"UQn<Ms8hJ#2S"k
%-"nQG70I$s9b;RSAelO"JT06mlZ1B<EW1m<P)+!$>*fcr<%P&pQb4^_Ol-Il?pX4+<i0[PeG5.4D6n(UE8f's&Epi:?r%pcmbhI&
%M^r;eR0[(M)lY$Bd6o)X"H@P(F39;$9HbMEMT9II;+WH'h9859dTR"OkaOR_h,Q!]gc;#CLuuZI$Jl_s>%pnUI/+nMLrr%,f[i0Q
%C(C=$>o7-BmOKqpa<5+D\A>Lee,1-d'rGOnqeOXM`4uRae[$X=6^/t&>*G6T5E*HpAk#SDN(F.?$>!USXBIt\baM+>$c=ttfB1Ih
%=I#F+\.$'/CkG%A+*6aD&LtC!Hri<j#1"`[Vhm+=0SkV2i#rPm9(\%oCiX=6#\<WXnP;3;fm-RXe5[oZ9\f"iBnI.eKI5&^]WY_a
%NpmLAjJn5\WI:KuNE3rg`-H$NSA_'E$=PWtFlR>4F;I+ZZqOmNXjl.WOhF#VR8q.a16C5&?.FH7gfcaV&P&6Hl0;CVQ.;:T0]g8d
%h&+XVN[W*=n8r9[>M.u>6taHPD-B?T+RYn,R&;79'<qf#;fL)dp![/_L'>R+d&U=uc3io]KoBj?\#lMd^@*Kb$:g\"\N:+@%&bZ+
%i(uaiVK?+J&iB-3'e<FS?E?#c'=$L37E,nO5a7Ae'KO5e9i?I-;a$_S]L:9BN]&_d["cfB_OGOeeqR@.(o8)oWeYKTj<\96(9QCB
%V@1h3T..H77B4d`0KS_IiCKeeHA?mQfZ0859u;`26":1Q=[YMC1CU_q0*u1g7r&[AY@gJjG)Op%e:i=hoZeV)c`VG/&LPRs/J2Lb
%\ed3$7#`OV?j:>#kNPCiH&,-`.6/ksO7WaRLQFr_,'/Xh1L'""TLPN/cJTrVNWI.9Yc0+gOfM/ZJi86M4JUssH_tniFVETq(fUg<
%iHfHMZ0UF8(Mk@5VP;[/9;93@P@dP)B?A+HmE)0ZbbFP^V<=KU0p4\Z?CJiA+uW0Z(6+-FUPMH,Yk$:/`ka?#k>ao+lnS5t#O'd`
%_%b4[/pO4<^7ke9,R'(GO?-ZY1=uC+(mKl7N2%^DJQmE>UD6O-!d*nL?j<00GAJV2U-1H4JY#h)oa!`=3amSbAS+4N0=jqYF`4Os
%K3b`1OC3)5D@,"VQ#<8r37GQU;*hFiVI_51P8ZR\pXVG:*T,+\^D%V<oekQ4CK@;GbXtC+k%$TWRbW^*<G#J#YR__lb!`M6cq[iI
%L<c->rbP;iS>np>8QXAq:6'@hZYd?o9JM/1P!O1Ea>GAAXN=MZE(?U?>(TY;XTH.FY&06fH$(5dJXhQbKV;=u^2f;```[YrP*59E
%dh8?m>1+7OoM#"912'`6]g^J0fkJVs[Q'88+Xb"j@4DUPkI3$?qRRtIY\29u4r?Uo7;'C@\dPS+Ke9>=S8HtpUU5DK4+3jq9hQZt
%?gZN0UU+%oUm;4BapCf*M*Ng0X*.1d/+YU\]`BVS-F\]WahDH\:qXDu?8t>t;pR<^"q#O";rH"r7hM;/OA2L-H+S2nM7C)M;'Im)
%#29ro\@q$nZ_GDf3`5T7@g.\5(.M@OGKH^oi&i!J]W/Lc(K[hU@gk=X-_,cg=a+>h>4h%beA%D!6Jp!#aj0EM\Z8,@YNTXBd2Wf8
%^#FfTaGQJ(':Ou>V3Mt;qA%X651eTSCb"eni"3h3$+<N^+>uJ:6(t[2Z!g=A?rWpD0b9gm0u4^Hmg1#E6ZR_e$V%$J,?BdmXiJL+
%c]E>b5M0'H`*->T6mVS?:0N:u@F%?j>0d\?6=tNY?#keWXfST2QtWEa4:UVFan:dOB1_;cAT-VGBQte'!lgsebk7E?5`h`O,Gr6)
%kDV57F;<Lf;p@]X5D3TWTY:.rqH-O%mW4re.q>fC:*C9=\$.to:'jPWMhFb8;)#<tKD$m*Js#;PQ?<_u(Dn!=NK,-ug'2jqK^M8K
%@C@isb(meU5W&s$*0\Vi^m#$2U!Vsa;J8G-MiRK&fP[5WNhVX*VW*3(aF:u_;ZNuSVNsJl;1'(a/f8_R]?&RKlZA1I`%dgV$NtqO
%K3p'IJcR'Y1QDnTMbM>MFbnFHG:Y:R>c2)%3*K7?Y!Z."M5[]ea>]Ah(?j.=gi\>6VmOD+X#m%fd<8],FItQ!h<6QuTppnCZ*RQP
%d3/6qB-LgTIW.5=?oOJTbg]$XqeT&,\Bi$@;7;JpaU(!kT_:FB1*]"4dCl=nnJGEQ%(3*bIaJu8S:J<Y\M($3d)@_#qgN#:/Zq;b
%P.dEBA-k*I>*J_*PaX2u-CRXYV8@8;hp0Y!TVZW79$]l)J\Y^eBSDSa;"&4A5Vtf,#Ri:E`20B'm6Z3*Cg9rmS*6\H[#I^Wq?G8-
%*V48-R2VV]EGQ#I<$dcWJ._>j_oViK9_`Zc;[!ta9?=om7_%T9KP^`'r,U[;4#4Q3*e;+LRPdR4"L9kJSV#gQ`[W.-0,g:;K&V"Z
%`]1oP//Xhkp*stuDC54a!jE_PrT$ie6coLf2a[D6V"_^fnX`6:5Z(`PaX^r"[ui1#H59,e<sO1AaZ$bn.EMXZ0.XqKbF"5538$BW
%_:sfho?;/V_,Ic2`o/p]cPke'&;G.24dg9Ogc/P1$bfCA;F>a09kBtN*$.Du/Ha-eF;]6sLP8Wt1'UR.ON.Z#_i>8h6L,#7n29"g
%6UlufF05k?WTR_ZRg;HE:^.8Z7$&FCH'tOECCK"Zc*TQn&O`.QCbsc1[Bu&PJ*TWMP-'H&NIXmC0ldrGfnU5kK.@MK'gjkX`\g0P
%Tn0rj]k;k8.VS42&>RZpJ9XiG`a]Sh9%FXF""tM"-Ob*;*$cdf/OT#Q%<)_NU\#r_ZZ6HaLK@md@Zsn5$;WRAYgCdG)f7\0[Xg*^
%Hjhtn4NC>k^)h!kKUF,Eaqttn<Z58f:6H&%7IeNr%ouCYQSn;2'A/r%Zf8"C`j=tK<?*Be@Qe]2-q6rSZ7m>XT;'lL[NS+!8?gbk
%iA8PP!XF7e*rM0=pqqGalj2@eC(4OD_0(lg1q^r1pFU@h&f-K^.touqVunNEbq)',bLBN_&opVT4N![X@Dp)__CH6?aRLR'QcUKk
%3f9MpQ:2.Tn1]1nb/;=F;_S_++mQ"/)UF0-#':cfG`YhhcjRud5]*i<EZiO^'j7$gdB\!T&p;D]Ku&(K8nK5=!>6f5:k7KN0V!/9
%9BVDO^V_"84!`UR_&GF">9q)o67E[gHtsVRO@8l#-73.,($gF\hkCf-X^.+Z=qYj:8Af//nDJ/]0OC1W?8P5G],3sB+FUg_)j_nV
%_dmX#QCjX7GqI"9"2&SpU?9ZR6+SnuQ*[])`rp"6>A1J6,#M6'23Z?2139iZ$s4n,82]Y5U3TTEB<rN\W@G$JP\k0LWae"WmJq<$
%<'rD+.2Yo<`k'f2B,K(;e.QW#Bf_^">Y.tmUua$M`Y_oV2+.+9XTaFW=BM\D$<sQiRqQBWVLmQNNfoO68N%;R3IE[*PX6W.MZ[d/
%C1C^15pT<F9lnQpZuOE7KfZrEllG)k:o4\)T#/?a"/d`H@CHe=_.?YBMq=T<ED-dp?[G3^//Al2=;u3<^n,([MTGT0W^p4K`12uJ
%\Oo\^52,!QH_85V'FkH6$!J@=*Z;qfMWKM"$nYXB'o@ZWEXUHUk24/9F2=BUSuBtU;,bb?9B6p!83kIk(MZ-q2NZrbEZ?:(G67I,
%1<hN7US'0\(lh4f,Se)QQ"uLrL+Uf)1<&0DaWXj-W%-S``3EGRk`8aUJa&(TbuY+hB+i'F%S:C`O[A$>)b%*m\u\FD%:;MHR]AmA
%F_DYEcP:nGM0LeVoA#)c/4ZRJa"LqEfoe>)<4^:hU)]$u/KEs*1qR=U)Z5pZ#6dhm,#OTTU0]S!:"&,$<B(JcTG3.^-;h'n)p0%[
%b<Qlm@5/<bQ>g2#Y\/r)A:tCVk[K[+\F$T^(Ip"ocj)up[^p5In"nWt'Z,h6b?38:N.=;cV.(!&OUNunT1'',U\r!DHRum)-aqOC
%rb!qm-T4bq]<=%7-AI<7HHYHM7b:th@f%d2h'&]'3;f6K7]Y]4M&i4o3/OiN%F+tF0Geo6n4G&&#"(!g,m%[]b3$H1MZ1F[I>q/5
%Pob%0G\7R0;JBV.-gtF:[r\3597lkI0*N^]JPI_tWR'H<9746;c1P#8jl1!+]:mt.Y,$4`5gk<m(c$YgVEG)/T;A5-Ikqc"Hj4[&
%'5X$S(0_RH5NMu&gjs!+`gGRk!.j?Me`Wr"X<BESZaS"i#'D!IOt#B=`Ta;2EB%ct=,H`a.GqLX*&X'U'I*bR?O,jT8Y(SR/9m5Z
%&rpI[91%#SO)%TA8<R#KHXH=g/H*sOLP_J9P;(2H9:iEm]:sNr9`fm9SO(+]*"l3)#^kdaa+/6'UTi++0#35haT:&a,0i**m]9_5
%9]8e'],VNK8TaQ.HbnYmjq:%*$`>R,eHQ`$V(Pri5mi#d8cZ4"k%)jGa'L"r#_.nVRa,ah]SoeD[Kr=7F2i1>G0oY-Pa"U"M>H:#
%UqD">K:g,:h3,W0.0j82FBc'1e\?CX4&Yd/g$d$,7ajmYc"hmTP@1*pWnXuXA4bYI+e.UJeW^d7]D$%*Lnq9C+HFVrfr,Ha`JK8q
%n#8nScrCZ8&A&0=@$84]h*AHiOdI8m6q%f&*]1u(^"_\5-'VRI-`^QHHQ7128[`L`H;Ttsj,ki<cs&W4[Sil:'SE<Kk^kk2r2h/"
%@Wi!u;4DfNM%l<1kY[UfQE1sfV\PEcF!a/b@Zc%>;=c9q-<:t1K6rQ$F^+I72kCI=fdN1h0mE#S/Y.P@EtkLA<Q?4nMeA4oOMc>W
%-C:IVI(F@=hO%]uLi[&Wf!8kICLs6O3&>KY>e(,48;q9@d%*@FV71[8NQIKrBPpKb&fM2hCJ9,r8]\*`k?Dlin$b45h<NR^52W):
%MWARFq:;&3qlQ?*(TUbaqL"4$l0kh(k?DliIl=+d9QehC'7IS`e(sdKICSrVrVb<-/ZkLHGPZ7dB">>+8Y9q7WP@>Z'Z.`G0Q31D
%:I,S:h&dQ)js;WOF)B>;*3>;q@Kpk#3`.;(\+Fl*,.>lDD`+:IL$;#.R>N=57\X($`!PK:d<liNha[Y(;Uj/\EC<PtX/PC;5]K1)
%RS?T72knN:%%FP[jOYA7d7RbmW#+M(dCet^Snmt)cgTIc,u4HP\[<9X2cX.];a$VIG5C@PQoW.2%ijea271U[Y0j);c)j'UF)+>\
%]t$!]r^,DsiYl+fg"a0f2n*rMX1>dKj%.YD/[!R%T$=B!I<4_.'pACD]E!9_FhPaOHEUt&lV@KYqA+44bFRk2p]6!jg&$o29!TbM
%#Y<C+g/hrGJOjN>?+FH`%/OnkaM7+mTI_N?Ae*GW1Pb;h>bh%>jOS"EBPDhZa>X"AXZJ\PO0KpT3/A/(@@-W%TB,\3]I7s[m2_2R
%FHjds8q0mMe*1r[Oh"d1!`GS.a@1^Od2K_Ya\`k#aJ'eM&?O8">+JFN^gp]EZ&+[`FifSo[u47T8')WlgO'npbY^h9hNE1WY^rBK
%e2;3%V@"l%/f,0MC6#g4VWE#b@%&$l?sX59:qC/EXi"31P*Nap]GT<>\`IQVhmE3d?*/4Q=<*B7&u^`8f;k&E(+WT!Hj<sCg"a0C
%akCVQC<H_8DG@!+VH6&9Ab4+]F1)R</:7ZRC#+$`l-0l*[,`.=[q*t_beZa!G_KE0h5Y4$&O-pp,_VPR+KX4o`&0M0ApXQfT=0_&
%qPKDRNgiKbcL'Emf<@"uJ(At\#d@pF+&a^=&bWX:[';C[Kar%FFk]H\D]7<t\7m5PRS+L+.R>V[_`=3?>119^UWYO&02kChHfp`5
%=7%k@E0or4a2WYtWXH/*%u[o)#:)>=X/+;uCm(eLK`r4\QI=6Gp'B`rG$C\>=Hq$9K[Ea/ZZ\s:ZNZ`^`GT@T-jo*>)D[lYe4F*_
%RTt_FF"3X+'*/[,lm,(,Hc0/gFIhI!V8u*QqtReDipEG(>4a[>SjddFU<):Qe+=;sr^g_Yd^.r-?TeX*g(q:=(Ngg_J]o*qA0YU^
%Mnk=NX,8$;/;cVA,)-`4=Q2^'\C&LA0G6s^L(i+l*2p#D[0lpfkLAQtc&uiFT=lq+/GA*.WhPR<E/uoeQIHY>/<pCM7'[I7MT\;>
%%*)k70(g:\L\%\\fElO<:+T/.Z].u1X(Ai<*<q#"`uWQ9W]2\kK'<>._$T'[WG.UtYf^S]1r2dMI7$6mSC;lH'L,$CJd0^%6;+Tu
%YDG-Smau63i,gj2,Ym7<%F0n>&e5b(?cVg04Y&nI1nKB\ZIr'?U51YeOh\5^Jc4kUoTRb4Q/mUs61C9@1N?`+5G`>7o\>n9ec`h)
%J,l11l9F/[L_j:oMNOE?Z^&,<qBW,K"mI<r9HeDM![`(IJM8Y#&KE\;:@G^LU^-bbX^`7E7(MTKXr4PtOiLPOjIjHNWcYcH"!pCH
%`Af:q9uj%Ec,rcT&\bW_q<tc(!$0OWf:mV'Y[\V?pWOK$%@p7oTmE6sZ^Fl#YDF$ZW1?nU1TZ95D97+U)!KOq_.M'rXb4bppm`Jf
%Bp,KbS;Tg:oAH4i/I=4/=CSMnVJSHJI='"SKOt%raPLA2as-D/&SS>Q9IiNV/WE0?U!(>&q67mOY3\cb&RK2hmj%Me@`59Nii5F/
%2@0MG/9^715^+H!P(XE[gKdkZ8-b513^EEF-TRQjhTascrRoO33XCF'!]AL%VYDGFY!pgpTVRf'p-K92kSbu2<;(3$ofg!.^MYN[
%]HnAP4aEfE3Ba9SMBFgCa$is+2W,7Rg(@[-02jJ%ThOu3FbkskI0J.tUT;OjM^58L4:&6e=sEu8EZGaScIf?_3^5dNZRa@8O,q+@
%hWL1#b22KdkDf(I5"tTpfJk)cR(.gs:jLJ-.]3oHD5RJa13d\@1,`$?RO16)hO754RXGVJSnR,!a:!>M&`OqY(X,ZPc'^4m_Se1/
%f(d+-FKgL]ogr60pd]+.Zm;0*_&;8OA(mBrX.@@(+=D0Ye&2e2!&5f)$O?q;+\"?A!@]7Am)InFC\5$B*'[&=QJ'WuFq>2[iL;`'
%]d-07Ul8R:(i"0<j'f)<XW\">9VP,0(H:kj+Yg#^AT7K^gX_j2H+?sS2eWsQ1n%r=/-Q-eRi]=0gS+CPq32tkjHp,/Uc!8jfS>qX
%>%>q&.lh5'(e[M%5fZ2*4$Q0H\65R12,9,dKM;";[&nT^/_o-$ClBXn7ed)*H<Xkn_\P](eFWcf<cs>S(cakg!<B[MNqeDoC=6_a
%io08MA2T3!^t94?#+XA0@_Q_O-:.gN%PL-QBXf?u"K8fC@.l@VPT;(q.<>YB-IYkQIe.@o<`TmQVei>KMIlDAfL5q&M2/,LPc8XA
%cqiWeJ@6M/X%-S.5U<?f$M)/W8eaJUWE)`#J2sk@B#SUh'U;#t-\$HDY/s+nY9uC;48p-?,tq!_B+\QiRaW;f3_"pCZ$oVN%V<^V
%\s<Mj6icKbFPmR+6OYa6J;1'p&.S1^S0(f$!/BASB8a16HTQdV:V0gJ*5`!9Co:4r&Y#'Uk:a3kqU(dcgL6hWl@Iqg`=1+U>p/hc
%#Rb93nA8i1Ca)K/OJI4>O9_T9CY%P]eAFF3egnCso=m(9.[G2W,&&B*g$U/.f1luE.-\UQ1/*J]eBpkNIFuK(Zp"VaW&h1VXg4:U
%:anI0L\A3um]"LA35I$E7-1@8Ds/bR;a1j2OeP<Z5IL_j3UQVkntM9/MB^+^OL8LP09#pq!T?TuOZ,m.S1+MPD"Lmk"YT>4L86dF
%MPK,jlj^oict?tCms'gE/a5qakp_9[<,u]K5oQ46MTdMWMHuaBL:uchk#,!E1==.^N-Dp(Vj?sE#0sBXhW%5T9TuNW3dm#mn+Bq\
%Dg&LZEI,?AA1R03et#"^!Z#.sGa"QP[&L.2nP/D$hBWDTH:'HkkgPe$EngW^2j@2'#D\r&Y:ab.5/.'bgHY].q1fJ6hnKokn`:j!
%]q+CPr6=!*celD5qBl.(nT&\9K[ar?XJ_752st:Oo%Uq;?iL5br@aiBq'Otrm,jr>A\%UT^@^/e6*hd%D,8UirUPpqIU!M?UVeoF
%o!9&k3mK\WCoh*4fIM4$S+d<c%g?uI9X[cX)it^F.$/F#0:6nc(aoIa$gJ1g$Ig6k'Aq`jrU"Z@:IYI0-l[-k_L<&6IWh6mTBLt-
%Ag1A3M-)P`84:\3OTVkoEW2akGg+7J2&#nTM7>G4`@5N/E/iYr0U=t22uj0/F[ihrh[[I'?7q6d;TY36eIdoRNO0VFgWDB8nMYG7
%\60IcUtjD_PW#'"Qs-.EVT&TbY(ZM*BcP5f+8R"QoNPpQheFM,$J2@#@%rjn^t6URrNSbk)oKFdP;*"i=&=M;h<`KR6$17@-:D*^
%7&7/7Ug!6J8eP]13X&p7$UQ5b5@$se5(71&_kJYE7et0%&<IOV:pP1!A`I:SU-etq0&5g=2DJ4Y7h,Due-[Q1#p,Oni:b0$_EpIc
%HGdB=gdecKJW<qBcP^bq!YL!m:5:o("#2VmkchL+h19%!bo(qM3<k1n*\Gr:XKbC*KOd2J)phG-bG9Bn?!3'YOj%AJV,/Y7e_j?q
%6PH8bE(M4c<,/O(/ge\nLtP4B#g+?(9lPpEiW\:P<d;VsEkD18VTOh#G47'1UHNV7?!iE9L1*g:5pY;hKq's;oVNX#fK1FCEJ'+X
%Aq_Wa"GpuoJY56?3.+aRRL/431R:I(,3GUMXPt\"o#PJq@3T:bZ'q$%ZP15>IHWQSFA&.&ME=aL"J2m[M\&6.E[>9rd@Q[s.9.[T
%:gW3D8pJ$.fuI[.lHD1VO#DEL<T=[_/B"(I=etp2&(Pj_AjE>@cB4.fpE\&s#&f`2R0b%-*tjED:E^XMW.fuK"VK"<"`tHj>8+T7
%OF!eU/D"MsrIBaU#gQ%71^]WpU?,Q%H:"PJclN!MQq/oA^,Q:5)Jr#5N(K3*o#3"Xo>OH[L=#UX4,';pj?k-NaV]+Yb$H2,nhudF
%3@n%U9Wp#u?"/86hDb`TcnVkBk[]:;'%nKSB;.nEKH%c!FZW,PHq?'sEVm$O?c'/,C&K@5iA3>QM(b6Q92]5b7Q<Rqo;"5T%"mf3
%,X[BdQ#[<rB=CV!F%_Z:dK;gcMBq<I1ri"Z'fk[:1Sn#Z5.[?`bZkIf-":kfh<`_JDd#-"4"\c,TKOS_+W"d4Cl-"cfG)OtXuk'?
%Ju)6^E]"K#;BS-goT0<h&q/FV\rkW\G$\s-BGlp-chk+<=7&/@4Z4Xhn[pj%a$t+3iAFA#7%"=;H]O$1\QO_3l(k27F&=u<@3VSC
%(H\2oRjRb^n_B8RCsS;:"sn4"/^`f"^Lp0:O9)(3MOTDEF@[^r;4'md$mHV0Pj('I`On2UNA=!:c,PEs<RFtddDG]s@1dh%Zgk,2
%.`)Bo(K<sT9E[5AM3.mtJ2kL)Et+P61R'"e=g\^0"^W-ABhjK:"`\ju9M6R%iW/5d`nQiUD@g:8)?QN6IaS7jr.*Sm^=ccJ%+I'R
%_^L:jZ(un2gDco_S!rTXWZlc<lWNB38t6M%0=YlDVt$WWQB0B%J+#O!X1D5UApY+Ai'(os9o3r1,Laf#RB>Q8G[pC.P_i=>AKhnU
%>>e;FEh>AJ\IBh@BnHDKq.?QNF!E*MIBR3lUWJY6N&dhuEIC@BMqtX4Z#>2q8Nj:7U[:7VR=ll%$WhQ=X=//0@c@K#=#F<[gSPh6
%pgP+sPU'!CL_sOcG^gjJQEVPnmtBn!efJEd-p.VLIZi/;<[98$WjL"'V%hQPXeLNpQ-u?R7'sjk\R?c[V8<IL19HG,U*n/`XcrgX
%Lif5KEDmM8WT%$;di*HoVp*P-nm#j"j^)b@W.C&,FKr`';<H+sRR6G,87BGAXi>,X]eR*\gOL&L-K8N5HXl\)$t8+"079Jd@R)^Q
%[k,]_KgaGPO]B"Y,%[P2XpNg4i&L6;)+%HXVgE\)l\_K;,,2N1/LK&!V*lV.(dATQZ^+QN3`UterMS&-JPVb($5G_kRF^_82AVKA
%n7#a8eCjdf`$f($K(iR=2>;Z7GV"i-#gUEM>GTC$&e(mlSX4T[UHb@7T[Ps[G%&`D'7k!YSR[T7MGf[Y`N`KLXa;Ji=XU1"S^OEb
%eU8^:;7R*nYM%\YVS^7sEe$D^Lrb)#W<%)]4h'4GflNjb8>\!#;aX_t'q6#-bQL$X/Si*D_j>Z]G*ump;QqAZ6Y6)-n0TbUhMnek
%^7^V)kW$pdSP2_c(b"%#M%h#J6h(,^pLV%#*ES]u#5fS<@\A'U<l_q]Sg<$-GX*pdgcZPa"p2bQ=_r0,YZR4A^OGTR^(ZW6Wc#4E
%c:g:>1EQBlADC9_SFns,:H_+rPgnt``!FnP/Ug(oG!ANIGIFU!bJHP0QP:g'M6T*QN*stb[8`?1`K9*/1WLg3F!65\R)mYu6>XdG
%WI@I2?Q8<^'?-Bb@?El-HGG9*_;ufOp<`1ZOF7J>UP/C3mhn9FPo9*ZE(`[M@:G/67M$g]?:LSc45p0\VjpUY2gc6+Pft&g1/:C1
%._aot.p/8(/kI*@3f5P`+V<)_JYL1g^?1Zf('t_?="E%9A"4unT!Aa!#7eHZ]#]4$m%Y@C%7[,gNUlMfNG!%u5rGX#+tlU-M($>B
%X=.(SKH=+\O+JQ&B<!nl-@9oi=>L*<TG>rKXQ>2V^/pg^ID-b^@>Ftf.S%W<#u0O8kk]rX%?se#7H56-dA*!kPd.F"'[UhX.AF4r
%oUtR7\S^#5/4ND&<Y$qppuog[N9Mc8+U^TL\4K_#b?jnH>d7C5MCQqMDF"EH(6llqXiZG'3?dJ79s->3N1jGI;apJ=\,8*(RS#i:
%r7JOX&8SqO7Qrhh=fbRi3?)sc*:p4?X!NLOR%4KA0L,)"e=0%JNl?5ob:6fZ,HdVLD->Sj;&#3Y*t6C`*<R"#2I0:@3gp)67+ND&
%J5^fCE4c2'.PZO@_U4SGcIt49;?Yb(bG9RlPAZ0Hbgn1+:aRk+EK>o%9[@TjqE@:P2ZqIX^d&kU:M5f7"FW*n;^09]$?6[\MtkSo
%B/+;P@5#0)j&B:[^`C@o:;6(H`tR%3Uuf8MnPlPiP*aQ;cST7cou[qgKfpbW?b,@Q<#63"gPj'97u3,?.ktS,d;=Y6`S53=(fi#E
%FAcSQV[odsOoc@b9Qcd-"L4oP%l.]RTFoW]=@kFC$ShF5(I]'lU?79rU-W-CD'OnWcU;.'G:p;n<d22j-,QB*W&jMAW'Q1i&OX>a
%U/U<ohD5k>fPME#$</Ib+=fosB4mLYWd'S^G>KiR)Mk<1l-RQ:R&(.j0oNdUa!!blAUD.u%0<3hGX!&Ql/\7jGWIQpn`)3:_,o$C
%Oc1)$oO*W@CW^F&(I8HE4Uf#qHK_S4:HbYAB=!B<+UA/fj`9!g<,,D*cB0"4)YE-A5ei34=>#m%<:MiH;51]qWh"^]&)1YJUM@:U
%drSIh>V^>qZ'`6r,EBpcX,4AUMFs;]gOWTIUdkjJWbW<bB%c83<E<hHFQ0"=<E<*Gd[iUV?k3u?@[^_?A,SE7Rd$pE#Vdi:+Il7Z
%5:6S*_k+a/TU>:T3b76K;M-<GJ`sLo&CVmQF7>^H.I'N^%BEn%R57l@"M8Np+f`4Baq`('oSq)EJQ]]+bd8DlkA!F`J[@f-EH_#\
%aQL-l\`/,?hFn%Y>B7&#38#bnMJL*bEYK3$fn_TDDUf.ll<(O/l6HN):Mjf+4uJ(r7\qYNJ=\+XHpY-*@g=S09<##OJl.dZ%j)N0
%IU^]g$C2C@YKpRD*?Wc5Fs9_JYq!_VZst]$_j>5mY6:7gr)jbd[`#jr[#D2C8VnQc:#ZRYc47`P6Stp>?;R4bWZK2(`mK@<6+%T0
%^Et&'ak!0&Zf*rES2=P;_D?=\LV;D_a@YNu^g(Y\(I@-j_Tp;f"+qi*A<Z7i9!:r2i=*,L=AshIbPU6M/Fj7D\!al(icUGOea:R7
%_3;i6Ka(/2l.PdNe2P[#C-"2#OHiogL8NNS/lUPF!@C[69EYZWeVc!"C`;W_lG\t"qd]bCOtiuM7G5U:k\;TnRXKAe3X<cE0IFqU
%$;bg\g!XKIg%1&S;3CmfM$ZHL6DDI4$f/Xe^2O8GY`iu1^A-'F&OY!F%E&#T\ja'kBUIGi_t]Ec@kd0#H+96iL;B7=//tqMPXhDh
%VDH$#d%/6=N5GX&*Z3/^T$8qG$XT#2,n!T$"JN6_)Y"^aa6fE7L"KtRr=]c_1/Wt`4Opcd[$",f@6@fikM#.b#V.,M9^&3t&mao`
%'?-"t.k*uWM<`J;!uL'u01$e%X]V5%L%/k$Z-YA2!*4-\+EUa,O8PG%ah3pe:/:<M'G)AFHa1R"OVBuk&0D<ghAo"L6j^fF2S/C%
%U1`57ki%_8CT\<#iN$[g-arK\-k`0^Kd^[q&OX[dmc0L@1DuBa/47p:ij\[9kgia3#"e#_hEPs*'!]NCN99HX*c0JJhD8mnLoC`&
%HT[%&d=S0EBTS1*3JQbgRM&o&,si%37CSehcUnn&OY0Te1%92/[O+t*3*5c^qVaE03k7i!Hd[GnB=`sk8Msft-LIF';Ur\]W99@Y
%,LjNTLXX1T"s8hNB__g1U;um0V,)p43mE8Q@2m%uo[6juH_Q?-BsZCr6PqODS8YQ=@U(S\)K`nB-s_\d7hCs)&i#/uG[`f=@095o
%8uL!_dF^_CYRldF1V=<nHO%.2O@+>BOp6)dY"Z2N3auRf@LOPUClFO"1'C.:60*F6Vab&E77Ha8;dMtB?Q3,aW^qN*&4bA_'djh1
%(QD=LCcjaE%o1t^KGuC<j21$JbL?3!#\+h,PG7a]<c,$+LI!bWO@>>'ZDlXuC.gaZ#5+Ekn.Y-(1i0R7nE3])X$@bra((KlpRlRH
%*gA09p_>"kC3l!J.O&W;*h,hq+2d,P)/2d='"Wc#64H.T:JeNd.RV!B_g,P>7aJ<Z,]PLl;0BOMH'M'&VEdOiN1?PrjPWp;`[X-e
%o9$^R%hH[!10oeM9q'1CACn\$a>C*GH\,5/5*/3D6sVrRjU8qKI7H+C/p%uiJ@).QHUuHVd0#!u$Flc4crT6eU5<pr1$B!\3IMMi
%/*AEsALALM_j+&^"\5pL2pPkSZ6"$VG`ic&QZ7lDNae;6oiK-uJ3hV(4JP><-O>4')F,5np,i_QLA%;2A/^.HAV%JB)7+9/qbmOt
%6)1JgQOIOV$Pj9\8<%D:=R-O28r>LG^`H>J<n.aRdLP'>R0_e(;KD6U\=`PO=\(&$[U,2c29N=_F<WPL"NVuR@ca'un`=!m:'3Uq
%n&[&8G\@R+HS8_9.R$Vtp7tSeN<<M46NM2u$I#uS-)V57L=i`bdH(8J@FX-I\iO%0?W^CE2%Xa7Zc-u^0PHl/rSWSQ&r+%'m%VC=
%b4H04E=mojO>)EO5[[lb3#"p9N$%dV^Yq`mp8NtGE'<E$rs%:ci1r+pLINo7=<8,7HQHWjP:@n7*qJu1%m$d'60$dgLhUm(:*iMA
%"VJud_/3V+RG/52h!;rk`p!%WM2Y&qo(AZb#XMP!GUE.\5U<K6&2(o^9^!\2TV;\a"*5%bHqr#S'4]["49Pg^K'3DEls=Q>Rm]B`
%P;@^\ql-hh8-Z?T4W_DcG;P:&8T\+O>>=a-d:$LL,@\aV;'VCs+(uX/KNh4(b=5\XXFgab)ZueF03O:fr4H<"6U*H>R]:t6rsFmi
%G=!"Y!u'4$'1UC*B6lK#:Q:nb!,:^Rj`'sg.Sf/_5">dICK:n\BhUbAH;@CF%VJ)0gpkG[PMhYc?M+o^!\-tU@H0Ie>buG13\6k"
%9gaRHU#GL`MNlbFSe8CGT;)W5?rGM,!&qsb`\`J^0=,P+1o$<L#Ko!g*!Rnb,-j_J6kgifrFDWjAS+i]i1aYkL-@R"%ndFRTU_<[
%&Yf"B7T^%j(n5A%(!P6B%4<@K2<[bjP,BlGag<4cW4kr)Dj;+Fjodo(E9kGG9LKE78G7323^J4-XAALIAu]>%6!'2S#BSt;@7noC
%M9NZd.;EA>SejGCF3%6=$.2BdXpZHMb+h[:_Ifm)C;6rcWCCS2F;*Z$/Gj'sfs1'Z3+5rn^XQ5!?ki9kLFQO-&>dQO0tgD6/+duP
%fAg:Th3UEW]Zj(#BL`;',ngr(P!!@.e9UKEI'1al!'3EuP%YOJ;s7QlS;%9(l!uhf%h?`AB>(A#?YZYt;2)&E;"haB3X;a=BMo2"
%eT'e!mLo[lJ-"7%J*sBfgS8^R[.!&G;UG`k:a3KR*"b>s;)FGlLfG.Bb]%9ZSM#$;6_\AY,:]j0%jLI+*lEd@d<KPu\n+uW:c="6
%1T6,>*72'W#4IA*o<_N>9aMRGkiR0tlJ(mI!E:X_c&[gU3b>fpTam,QC^d4"bH:%Bmr3.9Na&S>Z:%)Q7RrOk&Rk+'$=jDq@X:^o
%>uY?CU+1%dP].p\&35Pdo\d@<GSiG!"pV-Pj&sSR*U3f.Dp%+5#Epm#`5OIs;G32Xo":Te_5,bl3//!pg="6Qn7W(Te*?f?<lNW4
%>CF"j*0S6rkZgpo<h"=KW)/K+V<Jkb<kh7l#,N\(L98C.q(F'=.UPhD=Utf8El-c,F)(Jh36tCE@SF"(HpgHe*q'uqn]V\q3LJG<
%M"O-Q;^4HhO1QF>N?LCrFAO:#>ZPX_cY40niQX=-*rjg]DH<RO4\-t<i;qr(TUq&tf2`P)%"6SDdbLL^3Is[7U0\]]Rb5]'8I$HN
%]Nnk/bj&s5lQa=lfgJ6p/WCflK7#F3L<n+YV0VGD4&c#5\LdQ\7'6\\5g534c`R^CdX-e;_ULphI>8Y<b6[@5a_O.J-7F`2pW!"p
%^8JP>&fa9*VQ'?-+ZE38SnM?llfoMP'"JJiQ3]s#(9um[)0*U/f\9hc9AXb4#;/80`?1c9_5q'5=!Kmn)\%gMWkmnm/qbZa4IEHs
%H,:,2m3Q"&LoSkO(IngB%6!?/%M0MR>VrbOJOIF&<=&;a7d3DRUp2scJWS5G:3qZsU>?Gak2ZiTT9fn1gI@Y`R#Z-GEjZsME#H;W
%HZ7NXLoGOs0PoR'Z6Mc?)^m:n)^?UikC*E`8L.K[AP&R<1:IsG<XWBa$Tu:RTLQY'Q"![tD]1dW"kpYM!nTTf;aro0Db#-7Xa'BA
%$ZWb0C7q6H0Usr=g9E%]`1?c;rn#+bU*it$&h2&5COhoohDU6Yb8?[:c?-<ra_=#5@%X0+dl`C(I)Xl%k(%Q@TZDpEZ.;TMDGqf"
%!\f3AqpU38=Nhm`7;#1u@Id?;8r\RiL/F^rl,WnpMVBY\3[-o;=d<gPkUd+r$[^Kc*sKMb+DlYuN4]Oql/`\Y#EpXGLHEq%UP2cq
%oC9dB40,*+-6$Y./f>L;D4IAVS.SJt#%8i.QhKFd<2(EG;BOXM"sf?n9#ZrR,SBBR?lJ)AP@4Bd#>*i.dsaYgeHf>nXKahs^aDN$
%^>R"dP"-%QC`ujk\QJ"&Sen:uA!l,m=Fp.]H.ijnFK@AY%l5q_1*/!t't!.#FP441]Ch-Cm`#8YXH;`E.e,JT"MH@(cDuqhI08S,
%^K"WdZjce7:m[WXg#n$MBHa7PS!(T.339:&K2>3B<9iP-d+/;Hh0gQK26i.;TmeaK6U]lUII3\13gc+-QrBbq\=V1?2%-Znbb>c8
%8g2'`k9cfHd79oWhX-"6;l[>oigX;20FO'!@'N>r3DH1a/;cV=OZ2oSHX33q_4aK.5fCaOe9/DB2jP4;TuJU$@Dp8\rA&*O'$ahC
%dLu73o(%rj^-H5iXV)h`>"6bmA+9a`46S_WCmce[9Wpd9qp.k1RW^qU?h:uP&*L,i[n-p5Wg9UL-cM#.h%F%JjeRceEYceCBp*Om
%D-g]k#^@ZT"_23f+m,$dk4rC2Y(/Nl;!Kn+\lpnHLt"PM)a&EP)J_m\f(_CMAeLrnjM:%^)3VT7SBrj<YR(i\1&m4(RKe[5*rZH0
%TNMLoKE%J'NuTO?6-,=+ZB&O_2A$,_`d))'W+[^:S7B?SR'Ddp>gbQ&_Ru6An[a]j9[5m'3Hp:FS8u#WH\e%:#!3Eh6cWSn4ujc)
%UI()n*hntP>dHO*.f:8C:/T-@kXED4R(9H*]]3bi!+M=^\/bB#":6I.&3@(Ri$#Cb3CM@>@Ts9M#.'CA1*TW*kj`J?!8s>=eF9SX
%IWD9C\Vgs!EX%tdF)&2B*>B]E4QK]HJ7Eo.#\8K?SguWUl9Q(VZJE[QB1\D8N^\:c)Yh@rSht+-WW.[X<!91R!1.G7dkT6H+19qj
%+]1a-_iqlMk5[T51A`NV6,HU#/\k74[md#*S$@DLdL]*@e6Yig)?gM3ahcZ^&BPU+<Cn(TCgh".S/K-e6Q16n=cjt_)!n9$GCm>p
%`CrhrC"!YZ1uNb^W:O$Y,fZLV;:g0;`MK&3Y]7)W!GP9H5kQOME@-TJfqM)l[0Q)dc0;-67B3@!oT^W9,YK8TN`?W]MW:]XDbKb-
%)Jb.N;HMp.kh\A"%SYHI$\d*iIO4-BTsMn[kG*W+29V-C=/@CR"JCtR];abF=i<E!p)V^N<<A2ZLt[J;OGnmhJ[RGjfd.%=_m2p@
%T:-1K3WSRn()N0OaWrVKeI]u_kX,BK:224k07GG^I`Qgbe`3==.=-JGlEHk\g+&T17d0"l,aZf@P+2`Y<KSs$pSsE5[\1A2*;"e\
%;C+Jr=!7ZiD0h![6&SFp,T0*EKC4rFj%6(@+_D06Y-T+khW7S3NB7;lm"^g#&>F[VYT-Oq&a'_F4#(qKC?aHY@*jc6'lh&Ik*P*Q
%jagsh6VG@*ED)?"b\4X%)$k#O)B=ZEZkNgL)]d#+a&n.D!i%dB7oiEC&=aW>E86BX9`q"L*R\BERtPKPTl^E5<CIZ?1+.;(lqD-h
%4IL6pcm-pqZ]<XC,i@[E<_d"#U!4if\hG@c09YuL"4eHSfFWs-YrNG:7mqK;Vel1E6MJc>]PLPg@cT#G\n7BD'Bou-0T,Tp4"+!2
%69<[#]NC-g'oQ(c?IebPYcH>B/;Sg-!U?3p@I`p&`iOD!*-)akT54Ed#PebjMA_R5YG&u:68c9tOc;ql`H4(Y)-NSS!<'_kNYs#[
%j7lb0$MC&Hs$^`[a/g]lX>*qbCWmI=-<k8e#Co<AFI1Y"U7jCh7CP<iA\Vt6X60&A&HG]&=i1X"jIjR_"`Y5E9HJ/-@3hD^#S=J@
%fcarMl!?:i<\XAPa%_+5)5Bbr`F??RK3StV%lG\[$\'fRMB=@gMGB/9WQZ]bM)H@C<iWT.Y6GTh38k$D79RMFJX7:lZGV2q$%`jD
%NJ2A8OXL>3.JWH)T(0HSI2qM7H:b0Si(ZS=P3[##:E@45E0=/HZU%Nq3:P;G7D,HV"*>.H&J$&dO/5s-b*4)Ai&i&0(i5NQ,2Ua1
%+EZgu7I['J'Dt5;:-]I#B&Xu.8m>h'<=s'-=tYeU_[0b%o4-#j`l+$LJ.XO"'RVj2)&0STk_cpl^_k^3Ca9mE?.Z%#8^JW]E(,#$
%aT[JK\Ek(s7(,#^EYVBZ:DR_pB6D4[,7VV&>_nLm:8`)(ZV=>45VH>0%jS9RoF@o^;N8!?PZeSM+D_g]>a-aVSA>b6,(L:[0QS67
%00)Z^KK1>^6DbAs,o%V7V/0k$+daZMl=.(F<Y))=NEIH=LkoHI4:K5)J:fgH2V7JG?aSQIi#Wqgh21h`cCOg*%-!'Fh[B?Y#a%mq
%[sH7>X,q:jUUc#FY/cCO]erP7[#VP*f,loa)JV"=$6>uDZTia'/%1Tk/0(Q8O;Q6eT+iQ=G%!?L2:Z)U(`T$E^Af7-7MRtiLf$dn
%oGtM#iE:]NA'STN@j<j_6*&QIfh@2A@?N2;`B$83DB+Me`SRS)a+HRPP>*5DfbEr6.XI[<%YVn_&p,Y>E)JGq'mYWNpm+P+:2m5%
%6_:"QrD@\uIE&%\RE(KR."8@R(h92D5J?ij#aeJKmM$,),Xj6H;2MWAg;T2,D@$cBRb+FP&\p;[3Id,[m$\EE!i&8,NRs4p2Gh`-
%6^VVD4'5GKU;YF3!ERY`e$UL]mfss?FX^afZu)L1rofd^\N9ZROS"]\8*+3+jfqAiDhg8S0e:^<,A`h(Ss#M&$/JZOf^o/d#$[Jq
%jt?)cWok'6cK$2cbQC%iIW@p\AN]K,$#<EU3GJq`%r\p)M$_B#R+&`?!_@_H3]&kVHa^s<R*d#c[iS\?2.dL3'On]j6HfI_`mU=\
%oh;+gdfS'NEs1/@O\B.G6s*h7]<e2dWYl&(Kl%iG5]efSe+'F`S4@1u_Zd3HDM/=2=pKq<c3D&6&.cDNCDr,a"BL3MZ_F^:_pd5q
%??Ki]gB<?f%"^<%G^7c]#:(t1eDR$[b>!s@nh:Xc9,j\<;AA1*SQ\>ukNqPYF/8S]+i?t>!UMK7!HL!pPcQ(E_HB`F4^PF3N%YUR
%e]jg%,UUHI"rkC4*\7pnY?%k]#C>sZe"%31g?p:em>Zu7$rS\?f5'I^@<>uW-TtY:OZ1dT`.19tfURikIO*fP`'1%@RYa5*.MmdD
%#Qe6g.>`3WriIl`_*Sha+'f<`;+uoEq`J^,gcIpR;#Xj/B$.C1SB)gV[.U(E@9Q:RfdKL#jc:N-LdD5rI%5iY3I;jg05:,L6F7sb
%n2=hFB4]1nG#rVf3$Sd7:Q7`&qL\3B']PJ_$_#e%"h`9P/+O;F?o(`C0qV.@e!$[M,ZCJZCg)ATX,_ISPmeNIQF/DN",UoS!)<t!
%>X]/=<gCn)W@i*.K6fZL"It)#,gN"pE,.ih)B_:5mgVcQ]M-79;U2'#L1FRD/tW.39e9;PI3P*k[LCKC*HE2nQYRXX$&(.@TgNPR
%`$]&'S$=*OIq^G6P=TqQ?s0-YQD"JA>^AU6[DH7s.\IS7hLu)Vp%?^;XbU'):@Ma+*PcX93+j;JE!^baCrM4Z+a\b'Crjl*)96aQ
%K"279nKUTSSns'=,J!QCp?IPnUa87]mOfpj7+;be[M<DNC"s1>:$Yo0RInuZ(:SdIapL_5l@kTO!/,ona2RUI5"Z_0Hn6XUM$%(B
%L]RuX6jm:53eWYGL_`>CqCsGK-,bfKEsb"u"%OOEk\pb`Hl?nI7a)\cf3ba'MHQbabJPKP@ko9*U"'FlB,hg6<E!`g2Xtj#fkeXD
%Wal-c9B5H5Z"VdKlQY91kCse6au(]UcfXenlJ<R3$iIK:R>@B2;A;eHMI&1IL?b=i4OYDs2-8eWGSO/5$c6+/l!PE7SJt/q(g.#E
%)JqqW[ga)#($q3MB)GF`!_/@;r*6-;%+.$+qD`Igl0@8e5QZ%-iHZbASS3-k\`G8_%=2(i?pC2K2c3'iMKi*Fg89(JZ@!9]*h\kH
%apquMK<:sI]<6]DiJ$QIQ_8F+:i90?C^nI:>4k9Z(gYF]fhr_Z'ck;kKHLT$m@+,nc[Md3^^Mrm=$I_]#656'LN+gSr$+./B\9=(
%\0S?cm>JKDd3?E:LSXfb8u8kPWR@VK2eEfIp<715h=S\&N"l;MBPL28H`T+hZK>>Dc#f#!YfS=E2(-H`#'SIH\O9=aFV>c#>H=$J
%.u38'R?t@4^lKgQ/e*G.:l@!ZJaF*"_D3%4-S1'>8idTi8/VUY:;INa\0HY?WEgs4+aRlu2)WkdeJ)Y72H>9&eML\@+f,Oo("t\G
%&6'<HH!cqG8LjI?TM]],M%CW5QDe7(odU!8/CKtIMs)0W"uKb$!4BJ7*CP'fauRRrW?qY.8uCq]&N,rg^j[@]Fpdl5+?W2A#aSSh
%ih5_D(SdjDOS[-+'?NO%W"S\oWKkgT_OOie]6WQF=Yb_obiq4IN06/YLc@@g(W^nHJm5*$R[_&?#h?;)7:Ojh`Q=N&.#HI6SD^_:
%]Q_Xq2n'&,%hZi.eRT=;C()J*mUi_J&u.,@BmJ2f+!H@llNfRGS8mnODQ5cpkQm*H+q7N1*1aRWQ@tu.6ACpDjXiR5IYMSg=3odn
%LEnHR.gMe93#\(@LotFWOK?iFdTPX-XBWO3I'C+H%_3R'NLHO3'VRR%<J6('TUWk>>b?`^UHO>igg%ZEh0VDCKT7g"dBIOpKXlhd
%-(4j%&nQ%.<i-A<Ns<of0YuaZ>7B0WiEssdX[ECX0Y(OrS7FkQ!:82$S[NT0X<p5%RU5i(lms<@4IQtV/>V2q?m):ipbIBd`n3Z[
%*XB)@&J:T]`ZZF>@pUH0'&5CGaC]h@3[g-qBH:L4$sAOdgF?J+$;/770#N*!Ps96(?>]i\iU\T4^,.HBN#s96N>#>9nW?i'/e@:u
%)u,>P&`/I1F>uO:4)aIeK44EI2t_g"@cFg8)G1m?"WLdq@!C7so\71N@C"#@MPJ51%+#[BNCPsP`_7!0;LgOKS6(^+(6cOm)kf=Z
%?Et&1:^#NNPkfNVP$$R^8"LLoZIVCi2lNCDcLQ!PhCe_b@dbHaS!;?ua-l22g+'C321nh.j!`;E4\^[Be)+4WUQd(>72n1Qd$Jr@
%-kSq2`1C'M^j+"k37rLU0q.f;!p52Ra?@JH#$OfS7cn+lO[VcS6$@Pm*MEPf$4\Y7lodZk@#B*P0EneY6B>2)+DAYA<`->`,D//K
%(t9P<bTk)@kFo71EH[s`0bc70OV@(P+Uei_'tb<t>FU/BW4mJq$>U]2Z].V.>ikWYhrh'In7GYlX*HG4^^5=F<"YE^-('KYFp*lA
%9O(d!Ksi$',"XnHH:SH<KZuAtU@cQ-q7ltOiTO&C(*[3G;n#%;4:a$B8F,PB3bC@nU+00MqNFmJ]qGPb?gSt3+Fj<hld0<`1CU?!
%<ca/l$<%!t6\rpLJEK&\G#GjRXP#ira<k>1'#-$;n2<$EW=Y6pA""ahMe5E'P!r(Nas)d<5i_'Rg3d:5FXth$iWe:2^n78@#cr<Q
%L%AoRb(GZD]io7^D$t5O<CTDZ2Xn<glQD6@_OSLl;k&]5&B)c1KQ!PlRF$#0;,"%_n/C>hn'QY31GH568u"NoR@/LGgpA)X=%Q:F
%=&-"Z.0@jrFQ=HEcW%H?p9"M+Z&8N-7h_DXlc@53/X!pi@:\,t9Gr:>nJW!:1S7fC'""g5!3StM44rX;mhlPm/UE%_0T.kdpaCt]
%^d0i8#VfUB?<%e;5mN38e/2]4l[T(!M@o5X_*-`A)n?GHf3BWqW<InZl+"t&!hA17=QP@I^<W;k[8!0C^:[u9`4.V[C/lW,V"&tn
%+k<A0a4P@dds)3^OpC1\`./o/0=/jXQH`QP6FC)SqLgZ&/uBTkHX*H'CEHdD1GgRDf>5=Kc%8@na3KBi9%ftnlGYIa/mGMI9mE3_
%5@Be_*a5r4C$8r-4P6=G%`j_Y-H00h"p;!kThI_@iqZ(5,B!hl=_hb&jIiVaZSsB0!8*dfYeB=qie+D7Q)la4?5C.jJCgR>iXk^X
%8E%3:-@3[/j1%Se8'Rbdl@AOaAYeeb.T@PV7Z42\e.Usi%DYX?V?rSN^]:tOXN6#spc3F=<YL5#W=EXb1#]b+N*#!rng;7XGR7^b
%LY/DbTh]fF&Z;ujVat%<bL9G+Re6eEo33QH71)nmj3\!&RX0-e:BbBom"l<([MBXVFqNrRI,4/`Z,7C(R1M\QBODb)Y;;A:Rc;eA
%A.JNj/R?@TJ5<!G7A83"L>^GL7/%R]!f$:-9VtSkM5HR^6PPo@U=L\5aFU&=1J)eXUO!V[X+iPGi@nXrkDHeao.C[sOUr:]5f>+e
%Jh)O;%0s"P.h4gG,aCj8gFu2dODWP5[qAI+cjhN4G9e[H7DtSOkgTrBr$gYI>YNRln.lq<N_Wark9%Q:2?UVM(W%"+m81,3#'$Op
%aGu,LR^#X?>J\)ll-"Bd-An0E6Ur(:4s6[5dGj(RE@($=d#Cp]#kOt75dk*SU(9IOd:5LQI;8#"'L79uKj#X/6T?3Xg8tHiTV'm2
%M@(o9G8X7V\Po*9T8;@_/<,C&goB>[-b(u-QtD$H)'El!IMjrh2B=O`$)pqDPU"q;6aX4bDdk,9Mpr`LWoZ-B?/HhqY#D+iiA7NG
%d(pAHiff2VN#C19L_q2:,_o9*hafQ4DS,F>ed0=Z>)%o6oa#1bs2\=Vs#Z8b6Ln^tbJ&d'KOjX_USDbr7.?QIWWhHd^M,5OK4U6j
%nJ$(-Ce7lo7ULg@SYJBq/W#`^3P:/(NL4jCQu:mBgjsujY^0b\_UZl/$Bn"RCj^`\@i>[/hFt'n*=Uu*#%J8Xd4-')Tfp?9?_h";
%+sunmWIsMb@"iaU'ibkmYX6N=.,rtDA["F$"pqM:%(KXJbMiOAPe4-<Ld!!S+gu+J]WHgP"6"s@'9#Nq[.b/Z2@0jFTodsOHt\n'
%=BBUfQHZTk(YRaW'PTL!I"r8qnWLj$.1=_Of?RQR/L8b'Mkk9_JrZMC,*M/_)tGrbZ&K78UIqp,foT*>>Kj)T4c;Ml-O=C.Oh!qV
%%[>5BqrQ)<ZXFehS=U7tV7*3e8:WmYZ<Fkc51EMB*K8'bp.K2CM%0YqNhI!mkN:!o1!2F+/J2lk-3kpIFe]S,_@eSL#iLji_@8Z0
%2O![22K<75>lg=.[A/mZ`2U_]m536iHRL4V-c"4b1rU!cG=l1oROuu?c^*&&l'>0-8)n/_0JKLl<B16a7ZDD'l_m[`_(=;-M?+Mj
%lr5iF5&?hg#t,j:DNZ33Y/Ud/>GH2)E<C>;V($/AkdeNiaQ>u9%SUi>-J_;)$BVsl&ADp6iB%..Z,Z%RiSgf<3U=2PJcnJ#nNuQ.
%O=o^U,[thSY7NoRh>')UieG0Faj4_Z:Lb=pkC`.d@iH>H57DedD$/g5H@'>9UZK"Qkf73NYViAJp+\E<,37FY>-h5300.$B#78t%
%Kc/o26nHrs[h'YH36KWXZAVh]YlkV3K&T8KX=BV%Lhrm,2'Z+dH28uRd?50rE'*8(R8E7Q4iLPpl<(0#1T1<Ss"s'jCp-`b;Xg!'
%bX%5@K4#%&p?F6s2OX>A.46r<"rX:o8h^ZF0=LEOL)GQ^Y4d8fHG&dCH9*-sP<Uo-KH"!kcs3&C>:L:9K%Oi`BI4C-g'1?sQ5J5,
%.^pUUNAIoC"/dY]$'iqSe\12)RGLn7MU6Lp+t3:(kCO)"Xr`tUZthk!#B)8%2motQ0j^]efDlL@=J,^9KSZVf0qR>/)&5^,X@?C0
%,,AOFK>"J6HuoNg#-N&NC?`Qd5DTedDlQCZ%7.W8V"d[k@29De;J,0:TWF"jG8m4&!(_I*`>iSaoMY6I%c\i#=\aeT?<0BG1mP).
%]L;'$mFaBV21O#T28.ZeA&+=Yfg1!^'$<+^.!GbjOaB"R)=frXBu0BVe2KaeZRE$!``?`!5mKBK1KQh>L]h$W%oo>5q\7EkEmll,
%N9YlBP0?+dS,ZGB0VOOZKq>sh;%e_C2cr)FA9A\XZ2i8aKO"^c69f,%2)#?R@=Z]`R7jNP9#84(i/:O\Rp[hGJtha&llK=&\;A^&
%"ihZMUg-X5d]9(%&lG["0`S4&q9q4&WD"0r6rs@rIZ&;"lVn;2C3^MW[d.IWV[b1sD&$0,Lq*ML"JEeh%Ms'm(u%r8aC\)EB$]M+
%%0hR64GmE,Y3I6R=Q(d)]9nrcH=5i1(hKU@h-(>N_lCX+>EK3td*9p[Btt%*!H;:?4cfjnR-"6?$iH[!Oqhn3BLSfaeeJR[DKK5^
%"ihQ+%NBMlB>kjKJdFr3!=0_lmocZA!30ZG87];SbI)PI2unOF?/US9'4YeRWd!tjgXIA12@2=h1F4WbMO8M`$W+\&4'sSfXpaZS
%&fn4!.n[EQ\s_9Xcsp]I2?b3uEn7N-HNnd8b[pDR1]Vk!1;+N$/\8I4:@0&upd-?D.Jt_4?t_iEXr__/(fY7!lVUiL@bAqk6<mkJ
%.!^**oMnrA;ifA[7+C,sL<$RI2,"6%>HGi_4hP]dSu'gI,`.?=L>gb&[T.rG4jA5S/;\%Gn/u$V+f]gTi%4QrZ6%p(YW<1.,^KWY
%@!=t+$<EE<%$;ba`"t_AqK^a$!%Be?T&1oJ@@Uj<Fg!Rm=.(+;<0AS-c%*e`7PI_:2;'/jZJs42kq*M[LG9`2cAs./GE94,"WZ6P
%TfNd65efq)4[)_pQd>Gi19k<9o*hXdnsDeK3@Ls82+A0u575,d^u@NNbY<$/!1mg_CONZ1Q;#c@\Fu3jE@S@]:Jm&Kd/blMDUMWo
%@Sp;s`_.)R&j>1N#1U`4r.s35T@a?HE9C4sb3H:>EN--sNg#d5Uec,`pbH)0"pJ93hPWl>MgWL"JnAgN1JB-f(8IotAO&+oJL"kF
%\.lu'/;9Z>me-phj18S$:iL/R.?)K$6^BMtm-TmR9a=n-@\B5c?.+G'6S(V4GQa`b9SS*IbB#I5=Y+NH/kiVRg4L;;j5DT"fTV5r
%'PKO,%7WY.8Q&)-=6EQ)Inr3eFUN/*[=X_VWuE?I?;F,0\)K)tE>^i;Pd@r.%=-5tm@$?Q;hFNXB=:6,@_dMrp)'J<i+#r4MI]$&
%JK*"';_;5qDT)m#I\RW>.>/BJF^i<f:&&4m"D/$(k2@f*Fol3KN7BH+NrTf1/E\Gk25_<F9S,4'Hph>=Zlq\Y"=WDG7T/4j$2"Rl
%.k7fu$=L<P8++3:aN@5G8M>kHD79Qg]"FdCI4LC7cYQd9fu["Gn.oNM3+cf$:V@U9BZ?9MD9jhp=V?-bh\A:7VP'$*'2]H`?n-4&
%*??ZSh@j8FU$5j@Osi>:d*!G\dDSD6r,&V2BM*3&Cr)b^:R4ESBq*:ra#osm;;nb(&qOL,d#\E?:@'LQ@j/bq;?mDPStfME]oVXn
%]=t+M.Vr^Oa8i,Dc;Qn2e+Ik*!_$r1Tk\5P79*rp!K.JlBT5s`oSFlV.8Z@PPl%54BFQ!jURO<#nnsn9%7nOeJe)W`"`$:7*p1/3
%O(k3!.n#_3<^M"`6.MK!=gD*.")Z(Y6ldGVq^LRHJGg_qTjmiEKIsB\)lrb%LCGh<C!ccmfJ>Ap-0H_JT%g8MOtH6iXOmiug;+tJ
%LA-9s9MGbfTf!jo[d.**/5)Y:&/X.g5<l]?CU&JZ8AuiXfA["fatJsh?>KFLnYp1,'d<DER2Mha1(]>"LaoEMitAicJ17uOQY$j,
%=WYDaeZ9(U`+<FTaG2l\PoJ`1BuVXLe:o(HR][foE+tVi$`F=:d^XF)["bQ2OX/'D[4u>?%eWXdH)-lRP*<Hgc0eQu,u.Ci4b(c0
%0/MTdco$Xb%,;galGK=IPT6TR/g`#n&Co].X!'c&3R3Q<Xkr@0D<Z5B,9RlGAH^slo-oaRS#%iHW9n@!Yca.7ZJ`M6OYoZL']/S;
%0LBO-qW(rc,=lG0B1K'VKVUst?1IT4G&[#n!Z[%kl`13I2A/k]Gl/>;k_a%/F]?h3McTd%>,b6e.`R#;#o<J2FbW!O1Ma4DZX>Pk
%s&[U#RK0\R;:uqDJ>a"`m(p+:BAJ;)M)B*d2%RH&<(%&M.Ajm1%o83a1@ZX20reY@]'gbF6)oOL&@tCWR_+hVNa[e8'Qf>IaB%c!
%6T=Xc8f$hgJNt[cPP`-0PZE@*6hf:),<<+79]8PB[tHWf*7BXWE<ROm[.7N;`=oA0Ye]E6;:DgaX>M6^977//<MTkW)&$K`9,eY6
%M?c$?Xb1+R`E^L)f1cjNl?9!)UE#[l89-VE2m,qXEN;&g8p^i@Tkqa<7G-.rjjZp32'`>W#f72#c5udl_IGMe#9%[OLnR>XQ=r<t
%00i2U*J^@ac_ZCPeV(>$AHS"=@FV9'<!?4@5fs@Y7\OZo)!MW66XrP8YRL=SO%AaiBkVONV1a]$(gu#KP.SSkc$M:AV(9n!EjFt(
%IBLVjfk?n;;';0XWJ?4e#20G_/M2N@nW%9Y8$Ta>,rsDpe#>kjb_eh37"q),"#qi0P],N#NtblP="&UZZ\(Z$<X5BUdl[6\^d@!a
%UVn@Ydhp?X5RAOOY#22t%m^RG&<jN7(LO:rBgJqkF2DiuT+Y@dQBd>:1"L.pZ55Q5jH'X]*md]Re!p-2(o_AmYYA44/0U1O'N%m'
%^Q2:*Sg0N@IS/JVW%1<T(.J>cM#"pI&g!iXTnK_<%,Wcu5KXiYACmR864f4hc,5M`EgFoUH5MoMp$`MIBgJ+oYj.I..qqqO`G-e7
%et+=T*c_JJ1$f7;RG+FElu%=n33.H>%8!-)j.Hp(L:OAff^4Wl`TpPsfjNmh/Hdprg(I6j6dD\3drB)IjH`Do19G8d<K9D>fg'C[
%Y[p?]pa'M#RO"(+8q7Oe--n_%`,fu8Buo4u,DgJeg/J8-"4YYZX$1!+%_8,R6:k$+R8[oJ'8D>8;P0_:,Xpa-`'C5VA1u<LQBY&Y
%M,Njm_bY=ka^[>(I+tXJWth^9`'VV>GL0ZHdd48>GhZK2!h^IcrS!A'c[!]J'(h$:Jm*6"W8Md5lCc9(@g44-3KZa)N`TM@A482B
%<M?*2,o0(',VaXs=AoK!6,VK!&kPrIBS[_'/Zrg;L(61m-')Ni%uWe*]g8cE\=D^[A0ot2d%P.'`*$__oFC7IN?[Sh:W:")r/Z[8
%)Z;MJWXg\<"aRu;cs5/la>)1t2bd80+*U+edGjqc7gGJf8uo,IeB?H?_^URe@ua(!9/Ze<>Xp1T+5_T?`!S#k3arr7_p!jP*"fAC
%qG]HEBWU=8a.jqg4m7#B/jEmULu_"Erm9@W$%Z1-E`%J)c,bBF6RZ-8,fqlsW6e8ofRrD\S0po$[N6KSW?73G>#R%XdOBT^*0XoX
%6!>js`JO['V'U6Eo[Fb["9knYB7qTK,c$&]$U5c!+Q8Zd-7bcVQA3lR%73[p\8:Z]gbNY<G-Y<_MBK1F]NRjnLs6[:WU7Y(:QSY_
%UX1pRD7#'@Ac\bJK^3GE6V0We;#D/hbBbSV9Jh6SLmf9J-Uag37"m0PcdmXKE\m%0UD?8^O@cqb0J=,?:Qq]6Oej<80WP[7NL_2#
%0pD0q=Y_RUjK13.QW/9=NHDu-GcS)2?t1$`$aQ647aj\Z@Bu1Sr`Fk@C82nALP&pn9=*/'l[9e8VK!6QOiHk-A7.=VggDG&dTf@X
%ks<-UfJo?SL1YhN:25.[!IDU(RpT9`GYUP+r5kHu("<N5+M+oeb2a6*2VBi?(5ET.BZ/#I^`^[FUk&nBTQ/+QMdSVN#E,h@Ks"k*
%9[m^aL]cuUE<nZj$M#p0NY>:sIF5t&#e4fF-Y_0Lh+#ZOfPT.>QqZ%iZiGrl![M"]hAah;.P>T@/MN[j:/&lh(D>O2PSI.k/QOCL
%+j?R7WWmc@BX3Y^'s$p1D!;!@JUeL`Qq+tZk5n]U^*tk33/1gsJ_l:Nihg80XQbnWA0Xp8/1Rs%000OmTj0G>:CpoB67&Hd!W?VD
%,96KPJB;@@<sCcr[4"MH@r;)Fm".D'[Lo=B-F\("2"=0b<.=t,)\Jp&(>"R*(_t&_:-&eN1T^;=BdH_)2CnOVAjFht:`ab2oQXWs
%gZYS936LA<<]!RP/b5Yta!W9MSeLT,=[--E?Q2.4c"O6Lb@VEgnZ.0e/6J[-@2VK_ZQ\=$JMnh-d/puSit9AjS/,fc@FUtf;la(P
%S&A1+D,-cr=q\\0PfBK@45s=M^c&7LP:*bn"Bne"B#%CE)Tj2']nAHYe6\U&W&I(aS,hQA&>)7Noo)M1J7*)iMT(:[+l,`3,qasT
%-fhsm;Nn%Il\^RdZj'\NN+`QFh<$1F"=$sAc53<UgrMs$<fr(_gQfd`$n"iSpaJrGBd!YV4)'`,d!9$=p;VJO[?)Rh1d/hUA:QFS
%X;U#s9Vj;JP(kJ&8E-`K/h]U[*)i7^Q;Mg(9:UpcKS/@knCbeijKP=%\?d_AB"uMNFZjh%n9(r(?DKYspSMC9'Od[C?\cGrW8H'`
%6VbkU8q;!FfX3l.`qE+N8m4lP=JfNu!fO\6QNkpA"fs[&%'Sm/7*Km)F#,FR-gY$9mE@eu%o@Y!8/6(9KCKn9n^4AX.Y"rd#pZ.\
%m/Zn=p'KIXHNo<I>*7VAR=h,O'p>)3;9/9GRh0`&WJ?A*jf'';*=p.JL@)c`\@Sob=ng4cd#(=@S=k"(#mXRE8(Bb7kFi<ed;5cr
%Z&Tu"D:0iMc%q2ngk<0',X+`^!I>0]0nkQ"+pqgYORs*WS0C0p>HK/?e$TR^p1Me"XC-7@oe&tYTrElsMNofB^AJC&R1PZ@_,P_J
%mG"!0?ER\e!QNK?1rK1@cp%+d"Y2N$4UCfd8uFnaNuL$LrV#OBEoNM$2L<24!6]CoQQES,6\["C-XiY0FKT:(I4ol%l?H"bnC#aP
%l6*8Yl=qb@GmY.#;]\:qmM-cg+Z>C]i1ZQo)ur9Q%MPYXRNU<]R$jN&%coQ"6\@3`0O-R-E%0GT6A$!mK-oOhlNlt0is":ahoT:6
%6t<W>>_A%n].PX5M9=+'GQ!"a;Zgm0$SN'XOsu9dVBU5T'W:s"q-`*W8,OeKDQO0iSR(nRL8iPnW"-d`/TT*?"VukG=Z`o78iW`#
%XO0SM9K9\R-qI:XLd')Q"G^(4C6[i?/53Ha%RWH4987cX\p>X8R6eK]F'&s/cf>)Q;c)H-(t#D/"7Ch8J?/"_+d7\V-qD"[6f&@j
%hh=-SR?@XPM.Xi[>`mWeDeW,IMDoU^O,K]'5KutZ5b&r&7Y4FFg_7is$kS71&X".1LnA^NiFe1"c'RAr\)SfZj;2m*gfChHd(S[o
%*?c;#c2VZ+i0V7(`XRuB>Qcj6\lG+(qN_oB<NCcFJ;JN/nDqT.@Y?277,=JD,I=88NQ7+H1b(8;fNd2QNmiM/VH]TgU?<+d4[->/
%'4nhBmT9K)`@h'K^8=P`*K:NmFJJ'22m>>\M08;gWec#1/$)0jQtRZ(QJ0f<S!4ZTdsBAG;!cRYkg9`#,6MO$-K?_EjuV)0:%`)-
%LC@`9=FLr5jPUnS\3-@'"XdfYh/i5H*0.)NYN)HU@"N7r+a'H4oEjQ@a.W6TSV;B`!W8W1L/+<*%8Uqn7(4Ng9W;s!g&U=r3_kR_
%dOf=8C,KJJa/>-MVXt3R2TY,O@D=G/1;,-nL?1p/+XpiE-9.j@mcJP_!hPI'e88/_)@7jR+!:i?jJYITPpXN9CX@Wffhi?4h4d7A
%W<qd.MOB7%=0J/DDfet&&Ad:8ak/#gf[=;-WPJM>>Zg:EeL;8bWqo"@COe@%bt=ob;sG:(Q)q^ukeY*F`D?a@CJI-iF+=LPOdqAI
%)noH3!QE6uZCVZt(5>2<a=s7l6S_<SCVg)*)M*]QOsc)M+rV$98$I*T&V*P?=E>K05*g7cYjR;^*f1rFmRBT:#fV<NA9kPEpWtqO
%6lR@_RX9QTL!.,;*>(k#C4L>7%IT8m;+X]mW,'=q;bP5%,HoD4qMnD=;>6FA&OF`HKoc<9,9hXPl^L+O;)Y1=Mk2EnAH7i5`LY:B
%"C;\f>cXV\0@L<a4i=!F-u4Wt1e>&g-Mn'9%V(W(9Z>AWK^N4af2/=c!1P^^T^fKBOPae(12^3IP=t;19XO<'D?+1sU8DP@ff4CS
%9=Y@&>!n*G^lmea&5,RFSTA#+NkIsX0HCl!]chH[p9ImB?8D9^)HoYHgSeL9Tr3ku#@:bK&n+%'11iH8EMB'Pk<6a*KF62&#<_I:
%9aR'/+g>H_SmZ!,*o9@1,5g&o7Z?%KcjcKf2?eRZnfOb:)8;N)18M,4C,jE%n/HqAh9;!fU3$f1X9b8$p&eCH!W"^$Heeqh'MBL5
%e3e4p6h3d&-U@<Uq(YMs3oj7)j-0:T8n%+;UXCDT@c`?+5J`p7]h'S\>T<Kl4LTo>SM\2Il"S/=OZ^fh?&5Ze];%**CgAtm3n#VM
%Kaa9E!s"1(:e7Z"#o+^pa#Za'.6LIQ&eSsu_!MDUb<]NGa0+3c<e7!e7ZW;D$MMFLCW@\3;s6+^ecrikB.p`fC5$(+;pgj3/ko<S
%J4WCDB$GCKD?D81^rod>2&O:)7:VQA#^3UZ8ds*A@;s#K.R6^=P@FY,<UND?SJ&M<[T#mIK!H"FLe*JCMKgVh!BcSuPJ^Q$,/J14
%8?FmT]J8L-3>+3;kLUS]D[$_>7&O46R`m7t8u$E:]nXfT]:3@5d%2['F%Z]kdq:M+&K4@6,mhbLYgpr^"R1Lb>`]tcD!,D=1XpGY
%DR:2lB4O$$G.S&ZUoEms!8FJV2=liCjGa,i/-C/c1KUVLe4e[@r&@+\CD+X\6nLjVVTO-OcO(*82G=WK#]ErV_YDNqKo1?+9$9"r
%Yt-_E?uSdkVFr*)h*;c>/Bq"F#nsURNCIJ1Tpl3);2(MTKbLWoQTd0k*7Ygs+=r;F"neDt1F#Qs>&o`KC.HGR=k-ZPLDp99+!u^[
%T_#qK_o<P#g,HVK4n(_-aHs7nJ0jrEn(D>a).&(^CBb!=$!<p^qPi7PCPFap*&.Z-QHBn>l3s/b%?,/tjN2[?%Wb!OX>]5f'&E-E
%\_s8W"!5u>3MJhK_?/t<k8F$H8N8J<kX07(af,?qX0S]KFLN\qMe+A,/nq7I7T(n$,Fb?M8IrOFYVQK!118?V-)-%u*5+8Kc"mAC
%:&(II'q-/*RTVBBW:8FD[Qc3dE+o*1F2R5,<Dh?V"hRm%VERta;(#IS"LJDlf-PeM&h#;Ug,iuW-GhX@B%I6mjs*%Ar$9S(Wg#@?
%nJ_%.7R/s[De4k'W8Q\`D4aUZqFS?MT^9JGPhIuD4XOb\p,#BG;YXtF_jI2&%[6?h0UdgmgaPc]]betP*\U]<BOu:E^cj]D_cV)[
%657f;#6YS<#?*=!P<rY_2TM='(N^EB7DT:6YFCE*R$2<g2DluJFFen*`X$AQY]"JR%3"-ni_V4j@#[Ol(m_(%%DJ9B_3JB]&AY@I
%.Q)Ccr'1aOJICBeLld40)qKBZnT,\Q8[o,Ga`4g**q0`JDG$@Di+G`1TEnO.b=;/<S<C?<q1`#0Oh/l\"J^_P\_\*]Zs7j^m%>ZZ
%G;&q#lTo<OdH;'ERk1hp"5Sci*C;$Pn;hm7P+sWt2-f;V391_;S7ads/s(kA)&]>*UB]Te[YUk.$F7Yf=9h.Pa;pqjR2Fnae-pOJ
%HF&#<4q5oI$meWNNZ(>(Hj5=*+iXpRc/Z%k8Mss5iAl3LRa5q4;?\h/^ED*bC7Uc0GPD.tf".S1ZUPnf0H=fL$Tn&>eB_@44I8oM
%Q.-X0/N`N$_H^hI;-`[j69H:HYS!b&UnT,aD3RuX>"&Y[Yk3l!JZ*6aas?I>+a7EW&]%_05]..9&\JR^)M>hr+"/&OUlp"\XMP_m
%blJ.So6'[%c#o<,L'P)[^e+,'3cj!>P^+]QPr4$.H9b0q'T_:O^aIVjG0XPnS@m"q_fEh`_m&%UdRU<kfe>+Kd5_n1Q(a!eU(1Bt
%,1[D\he"Er%oY%s:49Z2H:H/!7-?AR0L6pM4+4s6a6UW;8$Q*Q0(kt^c9t?\>TE.*4s$+[U\p1&+)Y4Jf]WSXinn'p%^<8t"(A#1
%;,']I1.m@i\l^^EXcDVee._Kj&]fa.&/R4SmI;%;1/FI.7R_oqN@nF/(P>Rp+Al^SY"f0&60Spj-NlN&T2W:3!ec'q_:**H$>(s<
%6\lj5$,GZl1C:=TiM?tLQ,3/R[e0bd+<`D*frQH?$BsEp@&87d[HuR3&5!SVf>L-EeY&nr$ksV13u?%eh(\hA%hXV^1c?q3<Up:;
%'WVffXM_\Ta\q1Zld<-K=A2T%M(LW9WM8#tN'>J&/6eW.e%b9F2+o]tE\5!MM;1?E^:jW-[5q9.8C?EY:rP<6)`Zh=Js@jF'd0[n
%gpj&D4rAlCc\uN"J@"VSgmCQB.YSKNk/;R00LMV$BI%sa/!CX2d6HpKp7__CTe-nmF:Gg^TSMM</Pt!:Qk=<g.II1cS>ts5:(c!$
%=pD>WDD85$2ooq'@?aoB@VhskYd:j')*0nW1$fTOoGkZ%$n6(*^q.L6\;8Sm>[tr@RVdP$f/a:J.e$u"&&Ba.-;c?jSf-9BI["0m
%i@nriNaF`%-Ac=p64+-0+db^hEAqB9$BW'uGW&bCo4Xhlb=F>uW;"5^fBG*iN)+DH(&]MoVB>TOoCN:dbj1Jr;3KD9HlD=(7!SrL
%2RJ6WfIXkE6D*IS5!`QtCl#7S":_t,$^W+3V#pe#_c[rfLP/Vg6j5a;G[.[8eR`0YnhoVgi7'MQY*nKGZQ_GoQ\$E,Ja:KkV:0)R
%+4ErMcP$I_rS!41Q@OOnI6pEM(q*:EM'21KE/Gt^Of*Os#'"(:@\4LT=;eCO+=rJ&;Qc#9THU!k;O\9b%/%@SClbG]Qu<'uJdtm7
%e8'aR?'9iTj1u;XN"/G4)l%J?"TePsa*rj']5_E_:ZaUpYbErZ)nos0<!0(oI3fKa)K`r#cC3WLC'JfJ!)s`SKnL$+dPEG_h)*,4
%?p)'-NMMSu+Z3@sM$BLXHinm*)-(2!'31S@o-Rp4+DHLGlTOsm$=+@u/GqNlQ74g5=@V67"Em*fG4<9I?'EKnMEON\Y#QBeYeY<R
%Lk^.Q`G'?5>f4\g!AP_JRdh3Dj;t#]0[V@)VQ"$;JKAFBR]3D&HOSO)<?-R!@S3#_aU#[J@OAV9S=q)OXf<*eB(b[>`r$m#ILS?"
%jo_pd\5&i/WDDWb$Yj@0)7,4S2D*g'cKladRLkYncVDgS-)03D=0'>tCLe]71.rNL%"gn;h#tj?rL?X[$\NViY<ILQGjMV6]kY@X
%+C]j)'m62PQQ&-)mrQopmIYSni)&iP`:"cVi-a7Jjq6b%VFfM]7NE(^U;;nLJ18A<Z()/]rO4a;C0W?T9LUnsET$0fK.kmt^ha;-
%UrT882)>A*.q</10o+Q82"EJo`\"7@(+5W,;9'*,8lR'<L$FK":f!$Y*,EFkBZSL<O^(m#.p41!X$g>F>"6%q!5RjJ]pqi%UP50$
%UljKg[`0%)PSC/#N)]^38eDka&98I*qIC!\5t;7g)upS)UEg*7M?k@[N+tW<k&T%N<AdL,`^*ara)dW7mm5j>ITg<Fkg#Q]07XBp
%-j$0e+<00ul87!M)[MN[4jC-'"qggpm,#NSL$0I/Tr-FR!@r[+iH"E[%)m>?oDo''VMK4K?:/3S>Fj:]p>ce)de.>6XJ$IuJ^%8-
%Y#D]OL6<EIKa#V!aBUHF^lgY^gpH2!T&F(=@ZL;F>lP:.ZT!u7#3<3J,(^ntGg69krd59PTI!.F-l&Tt6el"BiYFSMTc-(;0MV_K
%;"HDCbA@1AamYco6Q$Ce2nK"8-^X#0U:hl(A^rcX-sHSJkODDfGR@b$G-4-W0P5*0Wak+*4Zln7OA2RK</`$)m!l=&GYZb5df8t#
%#fbd@;WXkn=+H]m1EG#BXO2-UW2Lq7EUs#ld?!ZZ-1h<h+?mQ#OqNF*2gpEZE[op$p/2>>"a05:YpZ,,e,;7O<]iN\Pm*Ws63K&1
%L5>%&*N^iZ7[4tVAFM3![Q4HANm2Yrra5mtQ,XY7BrBXW0R3r<C<t_orLOk3bpHuG1Zdn8&KE;t-k>YF49m$*-,BWW5X^A7h:683
%0jW4A;adDe&MmC8e]imcX[_(GI.e^d"k4r\qbtj=]-<]MO#qn^T-\')Ll,T^'hSol+sa4qS"q%9&2iF;,QXl#6q#9?AYLJR#@Ie4
%JJ]"4o$3DQA-u0sPIq6gHq3fO;%4++F*re0+;,D_RV:uX8oZW_&\V<B?NGHt)QFuaX`G\@!cGo-UNN6T1R>\UZ#YOA>;NPD0!Lo%
%HLN7;X)X&S_1]g[?;Z2!9PAn1<m0KV#mX8i*^%!eih4"\0L-DBKIaA_4'"j%\chVpKnr-u'[5MLOr`!o0SS)164`T2&969$*_8Tp
%s$@^,Jd$&(!%@@g.rf;?:6hut><'^3lXD@?,SO1d%.f1R[W)uA"#Y6=S45[oHJ8#B-j2I@PGnQB\QomLoD8bA<Hi2"M'tmq+ZMmV
%p)mtl[3hVK$jfkW&KdI,=]WI7-2LN:m0=4*k1Be#+XRRr&7n=c(*hai;?0*L&bQSMf-&(i*I0027.d3d0Hu-YfkniZ9iabgLg3qR
%Y4@+1MUC_",t#>%EO*qXL5:/.<E2:f%auB^paHgiFV%qhf7G5@bZ:+W,K,b"VT90;\ZQLoeP%btQZD_qAjj)8BEscS!'@qBW=VqS
%)U^lAM;B(QR^-si/mcbt&tJBNR:_7RC-$:oFAp[<dM1p/VP8\V3`C.A\E7gW+ci4C9.4Tsj@o9HY&t>lH6Oj2)N?_9qpHpE)b0I9
%6`(M6nWX#VAg(cmHYUeg3pjcKJsq[VYKPQEX+s%6'N&De8*)rMR"/Ej;;#-DSfMn!'@lBF@R!1F35DI>a9/J?CK1*`E<oOI-I!q8
%_0_?1PK"Ucp/Oq6\gg\7,AFt^^;hng[II,\^Db\uSLa1aCIfKYX.>o7M^aYE<C0XnpDW1t4ni'n!V\hhD\-$9lh>E7<=JQ>A'fED
%i^O+S*I6H1=:m3lKeO)Dqpk6u:(Na^5>l!GYuX&SWY15A`.SF_S9dF0jrco;Vad#GN"jL0*s-KGPG03NZ,#pCL;,*j4QOE\b\h`L
%;BM*R6m!tC/d+8"_1Hg$'a!5J)1c$di>$5R=2WNRB]/(6Oe#0U<j"neB8V)B6$IUZ!U;f.%L%G_[X\-I5I:,In4G^_aQ.!lL]db&
%;\9Zd4pk:*(l]Ct&!QJkKYi7+#qQW(+:)X0M^J]_?cd$onB>Lbf[M.-B3n=)!X4<cM:6,6Z0B:Yk;$ab#gPEW)hdIQiH,HId1;Jl
%2$o0f@Pd8`Q1:FSHWYmMOLqX?HKaSO_&2<Mn+7$b)+>i`(btf"eFt^",DfaiL^+aibU>\1B`p/aqG&!id,8-EAhtpr?DB1iUK=SQ
%*oXUVU==9/>41<(WkYg]A+qim6eE8YV/[2qN[?0pCk4rn7V@Wil_oo1XBlFb;ce7(n$8'sK6jYFbX@,g*AuN)BaMp$/9jSOX9Iq$
%R<9ijY[J0^J4*;S%Z!'4[@[-2oqpYZ\(#Y;$r[1.DVu#G=m6M'THLnP=cfdQc>*YZm.G)Dl!b=HO1`aNYsZI?(3aQCOd3^C)K4k`
%-lPu`P,?$c>5Nojn.Mh@$SV9KZpG,S)N3h;Z92/TRcn7^>ANG23O020,TB<-$/DkQ^-H5YeOp[F)9n8*P"#",H-:<B*qG4M,3cIK
%L/CR+X7bObS>/lm.I*`J(F=As7<X]43&Q"N\ToSK6kl"rm_g@Jj?_,PUY/V%2#3K6)ESGM!3V[BTK$A=9R$?,6#+?RFE[GS0H;K'
%'LfA7BEpC'#e5@nJAue97)i*a@&RkfHN@JO*_*LKoqO7B.>_b'=^[,8dp!6A<]:[B$)1N%@nHo!Z,OUR+::ZgL`2SkokQ&J,c(Br
%BT#eiS'CfWeAeT',koIcUTF-(cs`85'.a@XMC-u1;jbUhrQLmYd$9"\f5bs(4[>`>dP?#?$[AMT3[#*1ah3hoo]4YI/-4>7UpZ5c
%5t@BcYcNuVA7i?KMEY6fF=A)4qW+')kP8m?&1b(61(bO9]X-$t:\Q,V-ta4Zeo5-`6p\s;J+-tebC_[]Z"-iV,>a/_fV%k(dA/EB
%4Pu>tn_@rPg>k3>]o]lX"c9CU.s`eLO>eFV.WXY[gt<X[)J!`S.]5JuIXJY0297T^ER@K80j+j00bH<PYf5fQ+!97a9s7$?,iXdR
%jG%QK"V`0NaWV&nMmbreg/J@`\cs1?%gq+%oYmtE(5nk=P3"u>preU*7eEj#I;c>/,ceUXH8f>celTs,2AO[U2liT0+SVP>a[J?j
%6rP)d@^Escp#dn%#n;u1`N3e'd%U(Do,I"'3&.,['2_-P1TERMDi7RfbC^\]_B]][adrO!r;_,'_4\]PANZ'^-mmc?,)/1V8UPCj
%Y%o%c&lisqFP$3L<5Zt1HkaRLETZM%UA<FZ/e[F'$m(JR4^,-]iIJ.I9o85f)G@0P[IU`AU.Jg5)uKVJ"ClE(qOm3T2!G9\3?@qj
%6u48+$c>EW@NJ+(2rQ.C>Z63[etYA34ea4"LA(0\'MX`'bCU<b7lCf[a$?T*f,si/W[E%%5;Q0c*(EuV7].3kdfJLp&9>nR#-]Es
%Kej>*:-aDZEZ.P'W/V.S4XOcg:>^jt6PrTe1_3@r\?ThERoNObZ1obG"*l_]28&tjrY7%]I=`MdJ/iHf9Urb)d'k'W_1kH7n1'GE
%;.gWn2GTu]*]?_N7j&m?1HO4O@*b]<H9TaHpguhK,XqS`qB`ji+J`<V^6Bt\fc4/KNED=3KB7R3=@"nM&R/sabBc:&o6G;EIG<\c
%L)L2&2=].(V[;'OY&HE&i_)dCW=5nj3V;tYAFS=rl((9Kb7.@'<(EXE*@!#talQW:oC"pB8O*'LIm3_oJ%bUFT7-_)p@U(W?@Qth
%md7Ku5J+\F\@YW'(DhS4]\0+\e%aeqS6]L75%=*169e*1T7?-d5,X($YJ1&$:YsJ@rlFVZ^A<E[fDf+TqTjlhrD>WPc-cRe^[C=Y
%nr'^NYY;F7d);rhr1sE^[,Quf<2;FoTV$,K(X?52&,:7ATBGDWrS5c(I^f7,nsY9@4K4A,#qrIe,rHE(1F0_^=SOS(<)nOs,</\!
%BfbkkR>JX=UD,&H7P,lL;k27PBsg(39I!!Jp_reb=pmdH/GF*,P0QXl4c=8J=[J],<g=iU_5T"bWc![AR$5f2@WQ!(=GTfe/<=Rj
%PU;]rI#B$CgdA"!0JOuH*LB0!NXar[/OZ$WH!_>](-Z7Qk;8f(&!VS)Q(AmKRt]M6`6Q:lrjRSPE=!mK1=:-1\1o?F0qIVUCCep/
%kmI$u,Yp:fR(,\i*l"V^P,QPp@RrO^:(^cf"Ahg2&l5Q.hM7h#7/d'NValj5;JV)FS>rh*ZNs;ubTR<JPQ0\`4Y.SBVFub7,j&h$
%fT[d!U7&Nq4T8BGXCpjM@l]o/O!kZ'.8*@q_EK`_j\Kcnq+TL0'q(HR-)QD<.#4%D>^A\?3qU4#_0TM8%b#i@p`.?J/49Jg0lbV7
%\e"t?&R#^g?7Xikc`n4&+Zd_^OZi;2p-Rk=4&+ST(nBO6$ZUHR]+UmA6^)9K66pomohZ9ne)L!?kG+4h*1EV"8TW4(*qbWU/u+[Q
%(iM>JV.\D!GRI;-;c6je%u]M*QDeNBFH2)Yk`cmMV$A[9#pr:+:aSZ3K#kl&6G&7$/8)7:IF3Lh*OrIC5nBs;XWgh0hr(U"9\Mjr
%Mo4ilF-b-,\71X2&l"jPBi1VsQr3LCp/!%u9F`a,<8;=>4Kstp)G\;VX@SNojG4=\&*igs\mXq<]U!dhRFk`.DM*Vg0Ti,":Tt=_
%ih2\\cpJ@8-iI9Yka$<'*Z-C;g&p#_A]gIV$t@W=bmjn_o.!EY$&l0LJP3O-*?:(HaRl-b6Yb7J+/0D$pLYe19a^eY8bHR;gXp%V
%>5-a',Q3?7(Mblh"tp^RQnGoc9["Bm<a3V[Pk";'g3I/,NO`S-8/3K2pMFk`%D6=_V?d=Z91Q0.e!ID,Z11O*6'iQQ3?n<s'Ra$&
%M3O@$FK*>4`7CFO+CZRf4P*h8@TBc=H?:T:ZjAn\KL<,5JkI@%WCTXYbTR\e,ged;YV>XDNH%4>R#G]OPNf?=3<[LdYM*)F*>]p9
%$aSPbe3G\)lAi&pBi7&pH\c]8AVW7:QAH#a,=HrFaQqI[*\h]"Bb0:=BM,If?[X7J)+,.`>DPupE[(>s7h7U\0D&4Kf>bW3m=@&=
%M<N([UI6_eG`J>&ZRR'R9Wnb>)e!C0UrPS9OZ`ll?.]C\-b>6+N*dMQ%)UgUGc:,0/C._n&g=!Q8ctt=^N5L.S\R_[J8+@:/4s*C
%J@DBZ4&#k<kk2(:H;_&bm*b!VM%>5q>Sp(>B4n]:_jU"_a-tBm/;q/K,(FSlT@Y-c%[EP3IJ50i7Ml^ZSZ$"PJ4drrU#[kEagnE0
%$f5$`9;,*-f$orsAX+5p)F;r:i`S*"=m2)'-Sc"-ep@*`eQGFG*'PR)r1cD/T[!kh'3h)^n$1'&M7(ibQbmVDk*mY(aDOEnA5?e$
%"W(R/II!b<7Ze<d^/ZH0;0tiS+.6F`4%tH4LE]qUCX*djI&?`8Ye/.<%6a%%[\!D99FM%G%/uDT,h@ARrkeFHEeV0qGp;rkpWA+U
%/an4qDWYK\"&">g1U6:8,3\,ci7s<G83F20R#:R0fht/g8W\ce>$KtbAEV3F7+>%_hZQ;9G_X['9d)o1-S1NYQro\d:d25l#c!0@
%j9eo+Rp]]4'WMDV/taO`$ojBsM_S,</]a*[%kd1;>uVf#=nHNqRn\p%oPUno6#r5?6.A9]^!,N:7,k'r84tJi`Jo3n.R2l[7]PuV
%olD)u5t!ZfT)aO&\LgQOM3mE,R<%Pn'r3&!+rtO_4eqX0E`1"1#Y</1>F_Hf7Ns%q&-im@>Le;0m7HB7DQbtO^L_R'AS.@RZH%D;
%"rdYGnRE0A;CG:lFr&/t`XK8TL7ud3Ot*&d^9J**ga.1Y32"f6Z!7Gc"-tt]bCkJ#2_KSkc)0RTZS"L(VlZlkcm>`D^VNpjjE"X0
%.\l+I6V8jULP15_N6`i1hWN3FIBP%UJ_#m3[]hAJ;9=Z!NW3I#Z&XCG$6'SO/K.bt$==S>i<4G0a<DU7ZrJSG8.WWp6W>e672l=b
%0j[GSh5ek0'?bn1kj"QE:a$)P^C"0m49ITm[7KN:Z)B[[n$@tG@j(6S=:@%Tj]DYsl?)&Gd/-r3"=g9idKk#[7TB0-A&FYs=!S1X
%^+=)`&e7?.Ms=Ai89>7cF7;3K13<$!<I-o!i?8sbLP45-'^.Hg:Fisq00N/D4%U0h=U2BS5Tr0-D4G0m>f:P+=$"KA1>9J6ZLnce
%)Mo^pL*YUql(MZ[FC"l<1.o*udBR(J@-q*m=UK#t4'8>NV7jKG.%TU#X]40V(qgKm[l9@(0k?(lf\Pn'+XA!jG^\*:b%4hbo?+P`
%:aAEiVa>hb71G[!XB6,o9?N&QR8i(>Ml+l'Un[.MG-NSbpCbChmfJF>brIA?Tlr"c;k'P641-t/NV[HEoH_&N8f-aB$aG:T'$$Y!
%,9h:3k#fW/8$r/fq`7$X&4T$Y95:Q,^jL(R/P*ec!sa*5:g/CiYT2=CFOi!skUXj%`0I&,/5$RD+XS-1NK'6-?5ARrj"W6b.A'p4
%Sllha`TSMD1dtbn*@U5/ae`s>\P#='SG9OUZ^T-@9=GU#=-B(OQ4@I7jF+%U+;7lXWG0j4dTGd:QAUR.o(,kfB\E7P/dSY6eq%mA
%1LqKN3>U23.UESd77Qfi?FM%X9*1WbU60N%>h2=]OC-X4jodPZfd2&HYI'X\%\@bda<okHV(AC+/]qOE@0Rd?fOSSTL*Z6(1<j3M
%Krf".$aD,L,G1/np*@'HZSPZcHk_*2?9!403<5T(Z)2om-KIiYTnQ$'G96V)6>,Tc+37?6O]2#hJM5,I<QUqb1"'_0S9*DDI\&$H
%/GEbM(P1Gl'dl;:M1T*T+ok\=jFbMPckH&egmp[meBCAMA.2<>-40S)_f@qd?+a9\:[lLY@WE0/<cskN:jH>o+$Iud1'U3&%HhtM
%1W_1'$e<C_b,muUk&81iq8V[$N\W]%pdX5D'WFJsW!b?&o;]-q/BN+!nf%:td%f"q*!o#g`_i']OjUt#AdbH.'7%K89SJiP5:0?"
%?7B^f@7oj"3_:<H`V8)cTn+.9LQ.-s"ZL=eUC]Gnc40rK(T'j-;AM$6kDaH;lprCId(*O.]@XFqeR+^SRf\9S2'[:Tl6PST23D$#
%O'?rRE@bXsGMRHjQ]?pLEO_jsQ<\7M`,Gp0i*BiH;(b9gI/XG_15Hf((@goNdIMUN^0K3kF;=c,d8RFX[S[?.D`(V/+$rm<;mZ+h
%VDF)YN=Odq#cX+3GTfPgR$9b_MUE<4(ffj6p+>`='AaG7fMI6nPPSZ:&UTT7Qq9u6BP"=sLJ8nSqS`s]60&e;`fa4G[M=D_<M'\Z
%Ts2Y9H`7<#UWnL08Ha:nbu#,lc]];0EJ2EmYgn:D@hqp-esW#J1fU2\fpa\f+>,6OhS]q.&)rk:Q#%Ypp)n!@HQ[qI9*$l-UtSVA
%2l@&2'UfFH!uEjEAW.cl(8_B'2s]Wt1LV#flBO2+%EZsGho`5TFs`CiV$WPl/NiZ9MC"%899>ZgXR@[OFa=nW4hQq8]<$%:4r0[_
%_A5M=O-\i-o4/Bm=V,OQ2Ns'^qEEWc'lgX+%bPVjChb860VK/"18]4d'p[**&]71Y&77[il$-7e(kRm,N)=NUZRi*h@2,[U4g8,@
%L$6-&a[!1Fq1p/R+L?mW7h-%;_Z_qBIKu8Hbr_<j8FTW!R!f#mO+oF>@d+N9TbTl%<FK#u+fYq.MO?5`nD/fT`"[8D7N4&`G)]W;
%+tKM&Rqs3.5nE81lcFB/Otc"eU]fF]&jp-SP2C%*)/@WaemJ\mO/qui7IXi?>%%/_C?9\eYc-Q#OU%q#eHk^3`1glW<]+6WNpnXG
%ejGR0bB,EpY/jk5a=jV+S(O1/(e=:6N.J=@=geEB-S$cOTpQ*m(tfFG=/#^A1>Rj/SES6EoH8=f7QS6X-%1R29o/K!\@q?/$_,h3
%=)S(q/\W[#1Hije4U*1#bnZ3<'P2Hi?n[hF<>gF>Qo!-JJkIJ(3)W>?&5,&a&V(ISVsK+n;/p"_VQec_(K8#kXiN;o[oHCW6AU9o
%Z,<]sa>t;[.=@=g<_O<'BdRjX)7c,R`c8"N<uj,dN_XIU&TsO+,"crS)fnQV&<h>fM!3&)9cf\W<FniR4KqIUVO!20M<q+sEl/CO
%feTJ,O&[JL16a+!V&T\dQ5D`<M(C!Ba(Z,tPt<b/Oq>1T3>9)0,$@ug@'a]n=h18DV>^_3rYPg(Q.iCr`CHoW/OXNN!T]OtI,gXO
%i:YXDb-`PGU"p=9^^u>\$$);d#.cR4a,sgJR%=nQ>/VLR1FnnW9_H#N$*o_$1,BQ##[s^?RXEiqZD3:3TLfb%s1VBkDde'VIdbrq
%<(OY9EM@`g0;h&M&:"c/9mp"l4(kJ2,M7b0(>7Y''8m\>[>.%D>3'ShG/V-X&M/!`OJQU-OYTeKrpB*tn*_lImOrMXFF:03%TAhr
%jek?=m<AJHhT8!2q&fY1X37Q?X.TCP?gR6VIpN'*=2-comHs3IrqdLukP(=P2eR>!I,LV\48Pki?=2M>s*20bnF#k:GP;nls7tmA
%9>f*Em`ll^3WJgDlLiuP%CZU5s75KFqsfa>?hZd4GJX=S:YrXSrU\FOI(=?Fo;Bi&IlY,eVld\JI!u%^1X.=/Md%b8cuqY2l,e="
%EVZMNmoR^;%C\3c23[k%ML#,B\#8`5MCJJ-]d\@.l`WOfqX3+EYPj6SEqt%#>c?&Y-?K(/IpSZ&IrYARk4LLL05dCD51?%Y0^nl4
%S'1]<.jhaQqWI1AZ>2SmrnZBhn#e9\c7_Ao3qB<^/rOkdPmQ:%IX-EgJ,UDmT;M4Gg\p[5l=/"MDm\@>MciHj%o;P]ZaUac]"@`e
%n!7fr_f-[(hq$lP."6D]G8G2T?*/D"cu<4<#p5b^c+E<!S_p(<A^pgO)m<d1cOJ[EDr;^LOr&WG&C[#drVkp9b0F8KI!BFE^Uq!;
%]>)$`Yq`$<[Z9(.\*rb10E1A,m(70hcW@d7ccpt"jhBDj:2kLMg_JS68(dZd3Ouj&me6&0rOV=cM+5=ub(g5k0>G#AF7e-OQ.rjk
%*)sK)R_le;Ep97>rF;dHDskmdq=*[Y\!M"Db1p;OF"==C#@23`FL`,KoUsAJFLd#VpYY'8#@6a>=I2Z68XEX=RD%#0Nl)7Ok1379
%p[u'M0)fm2p#WYqYA_2UYoRr`W:.f#a\(t/GUHRKQuDG#s5N#kQY/.H<D(QlHs?4=k3CcD0@esY$#ENIpp[SRDr4lmh0ebjlulTN
%??+b,YMk;@\&]36b[JcFoQlF6e%4kMoA&[*c'u0Yp[>X%H[OFpa)g"Y>OL=)=67&Po648KGJF(AGIKLI2et>6nP?S?DQf\iamI@-
%qm2?PhtY=%k-lPgj6aIj2eV:i?_3lqXYK$W2W*)uY"5[g0[oXJX4t79rVkR7($Z5/k_F;%P]d;'r\mQ(DSbeu4a194%Ldd;\ET_X
%0)t+Plg\=4QK#ZfW".ujZe0DshQP[mokOF`AW5O(?JaJ0XtpF`r6l,897?S1Ps+T4d.?7%!im4/c9Ea"BsROOT>5@5beE=(+6Vb<
%2%HkM=nqT0`e/OmW6l)j2Lga&jq,!\LGetugcROXi-0Mif'oY'Dimbbh-JleH[TnK`hC%H0B5]HQ@HhG^[lC(e`HbucRtit[i?I0
%rD\A5`mX`=pMHB=qO<DT5@)W.m\R:srh@*MrV31`3(3T?=E9Wj7/8r(S#o3Vj;##SFaN&H/_U(FT9J$aX"Wln0)fX?5NIp8nIkCN
%r2UR3_KTXr0Aim7&N&:oQG:/H3a:XfgI"-qV1b;>R)@ckAus[H+#si%PKB<'GMdgqHZf4`'p$iSWZ_U*[e">%,quNhPZG4>1$69a
%r]]&R$]'RDQ"RMjf6`4dAUj.B(:#b\eHOjj.+hPSYj3+cOPRZig&3G>iB>HRs(77d^Fr9,7>UWoP2!aX)mh7/c^k4QI(mHGIIcdY
%c-=5XQPW^>kNd63a$88.;>\6<cb[ZJn*s2^28A<`XQJ,KD<f)`F"UdP(po1,m='#u2_0p=hRoLs-F5A/bqC^,IWopXIW9dUF8q`9
%n+Q=oEC]fMdA&r)`h+@02u[lXfRno?I0b%f]3V$\h5tXZmY,u]bmKrorp[E>)+JV84RRlC/q<K,UUJqTD:MtZqGgmd\\+8R+"R!J
%rOMSO-M,YoG2B;/7XB-$FgWi2kA#>_H?Su[falDc1Rfcarp4Oa&XA[:RZ^l0SStH%di/+X[+j/2XtFWFc;qlK07*9dDf40[#>\('
%26!)"Rh`Eqm/6e;ot0t:*6aV8)fKLqH,[%8]`oHJ>&A$n/)Jp(QgjP&o(YG^;4Hs^29EH<enJ40n+H"iqocaUR[Q+ig'M#X-f(rQ
%P&>JI$@Ho=HbJQIj^q`I/C&,cY?*H_T7<jQ]Du?Jj*$5Hfa.iLr$=e+XI7;HQnS.m&nTTOm/"[\7&'$P,g$KI)L27E6;c=L2==a$
%b*j@)Q`'!XMt4g#*+DeE?LqBp)XL$Hb)6ga<cPS(O$u'Y8[0[Z-F5A/c&7NLnc&To.'!j:Y&h&g'oR,3P%%;>9P.XVgqLh'XP/S<
%^ZmI0(?PH?`M0H%beDm/+4ElhO"iMmp@9H<`]!R3^VTMB7<fhr@fO4mp1GU6kpWu#Jes,Jq"_,.rWQfKI(][k_lbT2)N2uDl"B=4
%AY0)BH`N+uj%s=tI-kG==ul)['h`N(CBqRsi+m\Uf7&"S:_)L=b7`DZBN#FHmD.9OZ+8RS=F3o^g4?:F\Cs/(=.;?>?Z3cq]k^9H
%@L<$7QgA$=!"[]Fj0)<C>b`$,E4AAmCUA(nZ9-2ZU(LtbIomGI347S.1q#)nhd?BUip-8>B0UeB,A.*rAO;1+67Wi9o&fVaS'.;a
%CK57?6@F&tG5mJ#Qjm!KD=.AZ`V&W)8-K6'RKG1eNsHBh*?imZGWH(7RdK.`pbh5P%tJ@X,[MHeBqCjKekp/ZOoHuB,G1>&G"9aU
%qeZ(Tq>WZ`ro*(ijLB#%"8NePQX(8a?<0e8^Or8mSbUu?KuWj6Lk"6&rXm'C\"k(Y0V?P2o<mo>m%[iV]:WV^Hi;dfgI2YQh7MkF
%eDJ?n[`JeM0Y>$Z!\94XIt#Zb1N*ITg7$[)@Lg_<!H$%(DUDpt@Lg`/F(5]/\+a;?Zhj8/H.CU(jo)[?(\_]Imp<\f+30'/jHWuP
%_<f\kq2)m'?c42"O,f$WgtTLK#!-;k*W3\LkPtLC+o)WIhd'g*#QO8/s*O88riK*Ze0R-MJ,eBs3;A!te,RV>,C+flqeW)SH1:Xt
%W5W;fm6:iJn%n16nG.O?J?\`\ICTt_H_^8B%bV@/=')lgIs>^0Q[el*IVEbK:;E>LB(td">EeAR89L8;hRn2^EH6E4W'3.U=Mpn@
%Ce\YH_91Zk]ii:M[AS&Oq2Y'\?%6S]S@kLbC%[EkHA:(g6?]Sp*@t_+$eZ2WHDq[c(K.hgPpgu5P<Cq6XddF#?hs%aG8D&ST.Nt#
%`Fm@]I/0\pci.s1HZa?.g"jX5cYi3sDP$N:+7*Xp?la*<3V2UP0!F0g5?CKtiu"NP[SC760qF6'jaXD>T1s13Co7$I-#9lc`$;`0
%S+6qa1es<=n)pr_URlsZEP=R%GJF(AGDk+Xb51W&-X.5j<nme9p*7d3O+4IK8+\f!p-mWR%d'OW2BUm!Cc1rsf9kj>n8^4q*"4J*
%_oi^W3Z)05L6mPOjc_j>A7cFkMcsQArc0^*m'6hA0YDddiFR@@n`@^bm#1u093%SWg4JtVJ0mHPV;6D#rM=)RmY&F8;_`q-fE?VK
%RpZnFk-4/Z'5N+:FrbC.bs!,_-FWcJI$4%:4!$,dqsWRqjo+Yo/t_fF*?N6AAn\gU3$+0KG2mR)+>N9Qj1(Z]n,0fmOdbdr]T!.!
%VO*"PoF^tH5[QRF05^$BIfAZ:^\mflp$ML/+uTBK+@Rq6.t7`2efcPnB1.?b:M4$3..[%g8u:<V>=WJR*1l2\)ftf?Y$[6\k?bW9
%hRpP3VqZ07nTlKShR>TH4.bIsU(6\Mg"i!o%.)]n][UED#7^(>BkT]@T7-KfIXh0><@F<EgX^`7j-n);Ha,13ar5/53laFfR*HUQ
%C5\h9\k/t8C:>V?%QhZ2D#%JZfL&B+*:+MhUsn\D*M^:J43!^L*:+M(X6K',SNka]%X*e:gR3[7j']p['0?D4B_Q/?@]L"8fh9]<
%c@=p%QgN:V\2ude5J-dZmH'd=D#)ll-Y2%O.Gb$Iqoem.kH'@V$7E7Yj5ngkXQ,;kgR=XZ[/ul]S$TR?^Uh&PJ+#"-cIXI)&)(r;
%DW`ND>C2!Y]ESK(k2#_!QJ;/#<QB#q]ZWBY5^5Hm%G;tU!#dnkoDE*mo=PWRSi*7\!Lr8oWGNZFa1YYRdcpaSNBi!*itCgM]A)X/
%0+p3J.<p4YUW:Ej#(7u=cQD'#IIllA\^C:g'RXoa-`b2N?+2),6I:p5RVG&>am3=_?)&:PUk#XKPM)FUY;MNsnSkZh;qV%VYOP#1
%rEOOG_4gA8*")E^'$CcNpn.#<-/Q=?;`l)>(M&J3&P;Z)f]'@hT,r*bh9tE9h40*U\"28od,khlgd.DkBQMqWk#TQ^#ArlTBQJsM
%WdW@Z<o?Fee@Aj]k>lEK6(L*9EsHjUT["S-\W>6jnk2mD<_[1Ee@B)h!Yi4gPLt;KmXO'L^AcU;fpW07T[1:SII<pH?s"R?*rm1e
%YNr8`T\kqgE-mWrEPTUWi(E>ZY/D)'pMuYQNrtKK*Fn`l,-FOhja[6klKnGpT,e7&p:I[&bPNC\Fd;XkmJRPsCTQ%>DHl:AAKC$K
%\OS15ZS0qo=*3s*neSnulfQobob*nE4fFqCU8<LO$tjkVZ?Y,Qf?7B(UZotnZW2Um7dFn3[-tb8Q)fD2"i68W`t[^Y4oE.)k-ngO
%[+ON_m1!-*?_c@=r5H*V[j#/@mCTthBBL(H&*7EpK*(p/9k/;`f=cHreeb33iBSbSY./gks)iVcDNum%>&J+cb6gY/MlYrhXYb5t
%D7;AKIY,"Yan)LMVdE^"CNI)4+oCk,3=od(Vu"@#V_kmB]VF3E0E:n)mENU"al)\bG$+7M?p@!KH2NkYi-Gt%rW,I"O/bVGWPGZ\
%Ni=(QU:h+q%PlFm5*.F_+7-C3o\rR>+'C7Ga5<C_(oP%D]<cDZ,PKmE^#J#Jo5d+cGr)DH[Z8t.3BRNgZS@6FdotP!T51bf#J]$j
%Y_H6XH&Jchm-aHDgZsbt"Ut+iKm[Tc]B]pU]Dk'$?/GV\m.+;kbi*Qs@>;5VO5l[aA_/$E2>aWDO5l[W3'<3_c[^6=?2R5fq!^30
%s4b&E(?@if_-Ie%4q01#TDeP\UY*a'nZkc+*tX;&$^'X!"3c3*k@Pf'#=!@U[r";THO`a1O.?44WP=D_iVhe$fBaLrm_]Lgq5<QT
%D!>g@/B(pX>5u&/qX&VRReBpk]=P]Qc20D7\(S^C')5c_`5ee%Lth&4g3\d;itZ%hc@]XI^BBd[:M47mb&i+,GKP]sk3_&9OGObi
%>PA_]g5EU*Hf-.m<ru+]O.Qj>pdI/7p0_f^rW`0UKkU3YSt7lU75j,=h4hS!W*dt76tbnf2]$\Q]"HdlrF=H&3#o%S*T*K;F*7!p
%C[I0%Xgb^m4GY*:Cq!,VnuHsnkC;,W2Y*jh\YI%sgRi!LDu4h(r.1:'djb0'X2,s\iXh-c+7IKmhS*,k8RVkMr/3@ZI%6S4o2=aA
%kCDoQiBq4b>tOm#qKQb!399JuX/gl-oA;^7LChX)'4Uco7duO/OFHp!YJ'i0QT)+M(_Y:QOhUh^mEP*d?5TF!=Qs0tr9<carg08V
%jL=$?+9';6J%5L?p55AH\)75W:YjF]5Q-F#qWdsMroDnts)giHU]5A<H7H&kjc>^3$D\Um[#br`]^*fgqBL8mbL'C(oG[oAIJ!^@
%YQ)02oq)WZqkJ9*S"p#TJ)/N<jg/=miMSn#DD(]ce[Kd&]?@BEmd93Vo^$'1H@/_=NCQAFY<pEdM#LCh?&324R.j+Yp%HPY'TUJL
%LUctQbL/anrKUa09sjaHj[a73dakgL,Rms.Q&%7OhiCO.KCGo99HBAiW\5kf+7$+&X@0,W<DKqDrJ(f*l)qE^D9L@3i/U0P<F]o7
%%'Qt;%?8gl,GFj5eQ&/o>Gb1:f2mFsn"[Wgg\<65:E?q+YN<N@*FeM4VH-&67.gFX#XmuOpG"OrKl=/\s,'4jYA22-[_3#)iP'\4
%f@F#=f3[Q@]Ir52GD5e3/`$-#TCRD5-f1@)]Zf<]:+cWnZQ-Z^(ZobQaZb4JofjJGp:/i"F;_gfiq)d5GLq>S]cW!cT7'Gci9TJI
%^%1#3,H'#"bcuIXL:)`Jr3<>oj$+G&6`0M;6rIM:HnLsc-4PA@>>IhFT%!6@Eo-`"D;kN$pFa.J-.d:tgjoAk9cC5@2\GK`4>I<#
%L=+P%@(4naaIlG<7(>j*d$2oRpDrS)j*[8?P;;6/M$4ig,^5duh`:5ZYodbbh5B+YkO-Zt[<BXRLVC@71,nQmS^''Oo:)0q/BR2T
%HE>$PhH;FS$ugZ=(<q9pg:Hll2=0*1gMMAk@i*85477%$P<>E9fC?lVm%QX"g#MG?L6I^=p0*OEEcu2qr4Ll7F*kNpSuTNbr5D`=
%lYB/jmN2/WIX,RA]t4H_\m7s(f^F0WpYrfV[FqTt%6BCWbaHkea+/-$UKi$@5,O[%$h@Qk_fK$_8e9TWe\ZWSNSi[X':a5pD*:MN
%)[YgK<(@,'DLD'D?MaFAn+a5#N`*tqW+T/AK9"YgB*1K`T)D[8%%i#+r%#38Zt,TWU,d_%j(KsD<303>2k7'C]<4tD@q!iWdr_sV
%AU>]H^_pn<1Ip;q?gQS:6IUCoc/1Yq?TUF>S[<T:jEi]^e`D(#+ht>Jk9YK7\#&elS58]+N'=.8;k*`TGGk2U?1i^IBE)dto?[HU
%2uiD4kSjFteD@a.<_Yr^EUK#/HfBE]hUEeCBGB9XB=:F%>37VgqrU+s,$G]t/`lT:M3i^^PJ4!*cB55!;1'2SB@!"p/?/psB=H0g
%%6)W#$<`:t57a@_GK((u5NKPV2Jaupqr-@(Olea;ZpE?mLK:4E`n@W)`K#Yd(g6_cmp<,H$a4+IlZnt<oNSq/5M=bYTTAUTGneF,
%'Whm:gYNb04nob!:@XBms8=09Z$/G!LH*NHbee_uosMnYB77s`/#5a<[skEF[WFj6cV[mgl>GFu'5OJem:RL7bYn.Wo$mA\<cudI
%3U;$66k%E\c)udmLt+N#QB&DH+]W::ij?&0<ph`tcD0.K[iPA1r''1SQd)Oi;pM?l>6CMT?Er+U(891-4N]7$j'Wf:)gUI[b'<\p
%4at?fUU?pW4u8T\PA?^.D62^BR&`qeKOnN`eb4bUEn1)XZWG"gBNcOGT/AO[5*i!&=mbUF*t-eL8`$I)e_-'`>4lR<@b^FdD>LY'
%rU0*'pSe"h/&6V"-IJtV'>RnIA'iQU[5fMi;\Rd/k,7=Uc`F4Ml_L.9QsalL.)K$&@QAJ"!f51IiFg?n<I9DJH54;RNCi)FpCnV)
%Tlpt%dO]j$3G6kmm(),hkXJD$EQC9E[!:]Mo/o/-^AT%I7!3qG:V.KW)HLaGL3gT#I."Hu4UK^hjlbLO_rl""fjtDYo3Ep6\bd/`
%a#]$#3*W4uZ^o9e=3oP;i>B!7H:B#>%UGoVJL_eVj``ZkUT?(nO`T,igu4Y/Q2M<m@1(2&VP1j!W]"gRN)*,7l*?oP0ke>LI-!)"
%4HK0Z@c'46kI.SbiJqko*LC7=:Dl\n\1o#RHuiR<ioS/)*EmSo[2mG[gE$!dLIStpM<Se=$E_G"53n;oBCTf)j"P4nchb[-I(#_u
%AE38'H/3SrDqoH=k+CD*D]X^dFd+Y6hTSqZ&/MnA:otA%WR!"MgekdUlhI-=k@,m"m`tX7g18W@cXaQ6qts1"nGjVF*g2<\*9$G_
%>>IA[a6_ROQbIdohc1'Q.ZjB?kA)UDm[AWJK=n[_hVEB_h5'5#HULp2OA8"NTDNd.L*"eBB@rgO[cbbR\FXdeNf.I!4P(=:jkL$R
%q")SQ-Je['4SO45b.\CQ@dVam].X]L<XrQ*.H_qrb>I0[B%=pBh63=JR&+'mk8AE=\?tRp)Zd(hb@;FD'05(/g@jCX`tu-q.QZod
%\;p5+lh%:-h2lk;Wq0`BSkk.oh>=Y/D+.Cp`L6M+"!dgo2t9O0r^4cadJE=g%puqBcMq)\aZ/%Tq,Gf%iJ+4%rI%b`]D'<HDO<)\
%UH`m2"FfIpp;]hDGPWIl(VS(jcfXnMKWrBBe\er6c<fL8S6_b&%\*A5WStkV6B"aeC9m,cYWI=*KLBcsb*SIQ)=I:,o@*RLYE74j
%Mfgia='kV:^.7d"T'+2H:Ts*f,?V2Uh!N(d3qA0I^M4N;a]PPY^ZaXOE@:3`0#"D)fXgW"Z1]KN/-_J^@#NOH?5M3rm,5>?gs-No
%Gf1*3/F^`uF;d4fXA6JFN@[LY&lbQb^aBm8Jd?XLB"fQ.cDuG0keTtC3D@E4.\pP'c#i[G823G/<4p(FnD$@O,B,2TT$hOeKQG%)
%5J5F)NAEPTB@u5a`)?:q]+Po$U=TmU+O&l,LgT1b<&b?bp(i4B:H.p<KJ@Lj=ItIFck=E>b\CD903//Vn9'.k?PL"aZlVFh7M+dF
%EW-e-"aakK0QFI:O0GH<5Lo^uf7*1F)'dAAcj"D&Bta,qO3OrD\:0J9pi:A#HrhI-Ec014e`sS^Ph"at$rqk/?ZkG-bT4:Wm#^r*
%[0dAl=8i%(:'cmQp$$UY!LdI!cc+1s?3r^QYlZ&7\pH.L+AAhbC0\R%S8pVSYsb8QB"-G+J_fp`kB"5kpItDX]aF?omJ<t,IWpj&
%og$02EkZ_>hER1l*o=)moYc.[9-^q@i%q=6i&\50^,Q[R&cq;OeG'OtITPXGF#eX8-<[TaYW7_mO#]`gN0=q_nGR^lh+QealfF\<
%k`98/i0m33$r6A5<9Ca11%XS$_pkX9M.qrj2B_co-[pN5J,#.$o@`\UL.Qi[2ZiuZ0ee[LQ#EolL-ka7^MITF:[A$\qgNO5))\3P
%lZdp<')Xu?Z_bG.lmDo8V]Y`arf?ED0-%><=*quUHe7Z^RZD^M+d_oeIVP8CronV&MMLr/2u'Q5pn!QB,O_ONL8M:Rc;lpU*qp>b
%bibTgdDnILhBTeZCn&CF^mo$Fh5O\4ei49i7@OE'J6XX9<9<ScP<+of!6:LalrcI&`0@CAON-0JE1n_lbY(H,8^GG[iOM?5p^@f\
%+,sBF_;p,:0ES_3Q*hPRq<Tndh!()i3EP%bpRIS;@HbOJ<[?nA`-GR&O"p3M&cQ'L^pC6-B+Nm]id(?M?^rKfh7A;5HLX%.dH,c,
%@d3?rI'^Tf;n-gUX3NJJhpmXr_*tE';oGd`m(VqM"B_o0!#8n%KZ8"sq$%eZ-jK%ZUF6(*P^=dl"`hKnN[i`)-Q[3jW.i!E-VJ(`
%#5B&Q.C+,$8POEs3Pb5(2BG>d::B",6Dh1T+8NSGm#&s'^2D+Q2j5M:+(#gC=-nO8j=<"PaaQb*B-T6'jjrX(\JqYm`k'M;\D;0]
%?JY%9"aTp(037>Xh)r8Q(s(3&**:)uTbSGHT590**5[GL,OF%gm?jMM6*FoG+I0HH_/mmVY73Cm5Qn>(NbD.%F`)jQ_]FFmk7S_"
%q*d^3RKCT"]&rHGJ,#GI:P:A?iK7UpN"ihKSAW'jfa-d"Q6#+$cAOh-_0n$+^pnIZBDV[+`OU?^K,mh.Dua8>_97-eTCM"erODa_
%a$U`[PE?/1Q-oLQj<a>&a[kJ2_UQJAaoLqLOf=c6&e!0kBaDa-0c6()n[n%<UP9ddgr!O6ZSQgrW4h:/+@&kCBjG'I_B(H*@fWkI
%6L!\Q&4,mna(W`>@Gp;3,Otg9^.]lJhjoBaM.l@ETCkRmqdrmi(f0mfSYKT;KrnF2GO/05e&)%$_9_@QbVR+QXuC*XR)QfNLT:#`
%%DXuA,U7L:J['GlbYtH"jFemc^H/jm0>kEFp$UQ5dsDR3meV9HH7a:se%Bb+1VI8C`puRGla2VE8^Hb.N41J.A%qCn0u^]_^eDDN
%(dh,ZNu-M+Wl%eOh\4F1^r,G@oCnffGL,K"Vto/Ds2X]'s,e'W!H?S5g+N7DU8.)$%!\3\X9#C^kPc\']7=Xt6Lu5gBj<BE;A(a2
%[DT^EZE?/GGVj8&31@<AeT1[q*!6F#g<J$8[ikN8J]?polS6>5hUfO90tu[M2tc:X^:-o;oG,*1M'(QP5CUM\M!X4:K(Hn)50i;L
%I!Cqd&5cs.\>fMr\k6,1MM3a!+9F_Gq!W[uGD@A@SF-*Ca%;.%btO(Dg7ajp:`)RdU2*XJFaT4('/=(ajEruUO8J4GLZJ4-qu*/Y
%)#(kEG/4mumhR)Em9cMgoZK8uQQLUWPTZkWCC/q4C`$>P^d!3!.VGB^#kah=:l3HF)::Z%clc'"rq0V)4+$d=rVm8]Ee6FXQ8/C8
%.-VQfs0P+Q?hSu_YP1)r-_GDXmMC@=NQ(qriic41o$@@7hk4$20>I=`hu<1"J,QF_q&K`Dhp/V2AHIjW0Q"VZ*d*QO3*^t'=6ImA
%'KnB=rr64#+!'TUa>u/@Ob`_N&):FQc[N]ADr:J"J&\nkb<Pl:j,^Me^AZg$pgQ&W%htc9>=Q*E;=UlQ`[jA<_cRfZ1[kEW2XK+,
%.$`X2HPZXs\Gu)+]se5&p>C`*Q!HA1OcR<V\JU%!o,WDG-ChW#(t?*D+*bQ]S#Z>O-cs\Al^Ama3R>\>h><rhnR$Z\$'K(hc@>]B
%^]!h4rqZ<'r0q'h^n:Z!Dh%H'mm$bh_5[.-2TqLCpFWR\"6obO]F.Z0`&OWA!Po.u`Jf"*mY'ZsHlu&pk$E#jc5-HJ/*$X4+4Xdt
%d:l&IYU*X0GaUZm]ZeU]H24F]2dX3'auPO)IJ(k4j$f'#n%LRV+9/+]K`StM'\'\Qcah=SS$0WV3H#Gt7=Np;))JHj3QADK3YH(/
%fpZEWIQ@C'YYm`tQ:g(Q((Btnk@N4W0,$"rj5nV[qRXq_I</q6^\c1C5J]8Z?VHjZ>/qm:?73J1hG*IeJ:P26SqlhPXRCGfA)6C^
%o+<ePlK=rB"C#e\'H*J!S>]$&lfu02<!/YXF(W_4g.Ti7\IP+;jB7Jo/_3CP<S^LVp#Ys5`R`3tc<qjB7G7MCc,Z/:S;/a"YPGm'
%Og!p-*b,?fp)$V9hgK)6TAZIOj10EVG)Srp,Cd3VUOpVZ1?\GqQ^7re9.pfWi+86@;f\3JBE-dh`rBOu<5NCc$%YOLAqe.?C\qA!
%r>5aK]A^TNh228I;?FSSLl4=OAS#/I2^<+eR(]D<59?L^SI,4_)!>,UY1=of%0-4=4T?8AH/@Ia4-^%_)'\sqI@$NL(>8.<inZD]
%4M6$%Dr(#pJq^^!=r_<\roNp)oT-:XKSUd)VDX])$89l,o4e6crUTN=^aQqpX)4\\f!2?'CS/f=kWcmE!NES3r%V%*R:>V.Lm#'/
%FXslrP<Rfdj`QL30UA$.X*9@!.JDUZVZdqQke"/H`SZ?8);Y$h"ZGIfq)%Q39_m@O'p0%\lb%T5Mt):k]nc9BXgE7f4lB%:d=ZZ-
%HbZ/5($D@9f?M>k,j_d=4qYPirY$j33>2g>l^C,1O?NtRhTg_M`a[h--BpJ"f%-EE=k@uU@uWqBfONr*s5j#?_+,N4I"jegB(IC@
%=7N?/[EX+`Y>->u?bPW%T,i)-#X"/__d$^7I6RL'ZgV"4E%E;'EQdu%SQB5&SaSmPj#J)1T?uOf0A:7dU//Z6=PN;Y1Ai6kRhDiD
%HKD))(b>I'SD@)'nqR'%KWW;^bh[HF<F<60kjce>U5rAV/CN_5o5b?cgB3cN@K&E!^:kH_pJ!pKnJ2if$,+MaZ)A&Pi>&O[mQPL5
%XSV`QTDdE':&g2B7%qbI?Q&kkpaB*G,-ke#nqLb%YL]D6fdZ=Oji@/Wq<pLo*Z<Wd?>TSd`rBaRMF@^&A7NRV9[b2@Bn>!-a,X%X
%1nUE=fk0XH3=Z=jogth>POX-SO.,+qq/bA:S)+#A;p1/e8TR*L?Vmp#4TD-<eqe0DD`I$?nc/A+Pu/!^cTUUc*M2D=Q\P>eTC;`!
%7#i3_T?!QuG'o\0A0lW201PsJQANGlp(H_=3DZE"()un3,^A7t<;6!$HtM*I1]1%Q8-"1b)*0iJL]d+/)Damb^$F^$LCn)3cTaL-
%pK5sjr12erJo;S!6go0;%L^=F9LCg&Wcg<B2WW0FH2Q?'.!kFgMn%<(U]7U6."<#cAnL/N^A=C-$!$R8Su%In]OZbAEoi#0nqTA?
%rB26[,fb7gNRIgFm6^Pm-KEb4W+F:MAXn$'iP<Vi*5e)Wr#a64*%cWH<F&roh^8Ed<#tN,00]ADj8\-5N;kq+YN+NqarbcTIO3e@
%^-28aKZ$poh=$)Fr\;TaJaJ'&b3:Ds+B?:1?@TQNVm2cRS?Is:s7u8gTcUYW4HA+o6\bW[mi28Ogo'34"0;5BKiu1Q,^dRrqtf"D
%9%rOc?1G>Qm/?[^-;Yn1p0&559DgPXO)K]j6o:%E4eFs2B+h9"o6dY%J*6=9pcZNE1-mS2rr\2T@sCLN9q7deIdrob@"a#nl_"4J
%GJF%*Du\sLru5o,s6IGuoj=/lnULG^4l5.ClJMU+YC?H-IeR%npAaU>,^amlQgt'is7\IerS>XQ@ss`bm.mLY#(9aYP5>L\d91Em
%bSrc?9agJ=T+&70h=n\`poAo=nX&/5qJZ??hu/'(lL-t9hENP'r-s+oRkr,Es6b*GrQTgTTDT5k'`QW)L)*TW:hQqB"8-S>oA%BQ
%o&Nmo^geAC5]0gD\+=ES'ge6`&PPZur3mXG"eVMZ3PUZ,M1Q:&I$?2l?mh!H;`pCTOTRhJ&Q!bBm_chj!ZM8u=,m<3'KEt,l;S*p
%c,Bc1`9DQW=e#\ho/p(63SE0F\RQsu$K48e;_O,J2OHLe_X7=\_GD@JqK,%-<S!/`\.<9Y4;P>?qYN!H9ZIGi:p*=@Jb*EWC^-'T
%_Eu<sbE,Afb6'S?f5S`4ZckpDQObX5Y_dD',=LaWnPIYem<A^QpdM8,C2jJDAk+h6eeQRB.mK*!!]WIc#N?0d)h=M1_+ms3aF&[:
%_c2"t+0,pk;+CnJ+h5u<0>gS4";F=,#+\e7c2/)R)74%:[g5bP4T=e=%Ej@:MtjGF!(LCYT;ZF-8XMPK<l]upNLOXZ+Do#.APK0o
%5S]iuq(VQ+7OXJ07:33#4MiXQUa$"go.C498$an4M,1;RLc%c8Gnqm5WK0kipQ6I4\CHs:DIVU.#U%`pQ_Hh;,>@*eJe20=QDLur
%(':N5S@G]<0?CSf7)H#7Jt+#C-tLPBHRma-e$pgTK<QfO6=Bs[>Pgc5"fhYo[d18f#[$19O:ff#Y7,o!0:=&l.Z,cPbQ:,SqmhVc
%j%LYsBQE^!/rQ]A0[d<F?n?bD#F!X)0nAR2(r/h/H>*G9SoQR'BJ/*$/Vt2)#ck2$reU/4NI,K77#6_7''dZZ3SUF+oF!uRb/B)C
%Lgmn.=[*n(Ef'iI,JU'HT0V3%@9O'N-F%8^aG>G7EfoOCY:s'$FCYKQZEEq=7jE=1CqrU;\`]dD]l:Dl/c9&t<_YWtSu['VKppPE
%9`8sKa.7nT@p,H*K]QVjG_9k86UbQcnI?6+'e?``SOKkI;4P!,Yed8A(U]Yho@[MO]8kAWm4+uFoIGEmkNWpL=3ZhGPq/qIB>#h]
%90>P%AH5*@eE`9NloDbe=m0AL5<4mWj^Or5?`V/lR>n=9#AH7t=M.'/;11o9jJD7-7%QG&WGj4)fbdUEQ"BJl*$T/\^puh<?HjDL
%qiHl\W:l@SKO)I5_qMT09RU"LhhSFJ"8MN9RgI7_mg.cs0$3-A+c?1C,fKq@4J!X)M9[LDSQF6.KDN-QT>N930g0(ZCB>8)'i]N7
%RPr[Fc0k>sW2)F)='3)tGOCgnIH2,'AIH3GcLe"S?Ub,uCHQ6Yfd0UP]nOGADcj>lS*d6_M[o4g^2B)Xg*#fuo"98F2tFDOZ7sS]
%7Uaiu#cXp%..5KpIu\WW3e;Dgn%G63B`<jO?VNnu>Ff=*[<g8`0U/dOEl90V6i(:&98`uM@bts*-E3W:>"VQfhP5_7q"g!`K$gC?
%&r.dDSrjuc*!!o/b!-^bqYc;-;n'c9Xi9SVUeBC"%N]&9K7NOQ/8NDS/*sY8F(K3J!5jp*7_\4h(e;]k7cOs78shc;q;WI=Of.Z.
%j7b1pB7,b1BOEXGjJFPFY(>Ab;ZiSEI'EptS;)1kW-(8,i;MH(QV4tEq&/8gUFt5G@58dZQquJud<_f:jipE'8F<Y*,LleZoWSXU
%!])B[I^d00\L?QfC<iK_8P'<KkV<7ERJ-9W)k;s1!V(CUM=V4PE>lVT>?9<FYdR@$*-o&SE2Y\6Q43E&nDuiF&Ajs,;Y);hmDJ'k
%P;1'<k^0=,O@g&t[W@g&Ye,!#SFo$G`jDu88f@+2;L:lH>FPcE0-pZ-Xij15;)DX<Us)>bUNMQ"I"K^_F%N^]E#V71GC5&#i-\u]
%lgcg@_R!?q#UB\E<T60e<2'SI598<5NYeU/0*0M6Rg?3_&O)YbLk]*RBet2QqDT)AK^bq2S>O!V71hO(*/Trt4jf*/Q0jfA]phXi
%kai@,W"DQ8T.DX+L7m;NY:i8u.YR!Rp2o!:3VkgbX;,k7[miu]>E'4Qmk/p34h$a_m3e3(0`qmchFMT,B?C.\RWqem(DFl[Z[*Y$
%T1/gM>hQ97hhs!N_l@nO3Yh.W]Q(&FDJ/[,)SYLB8eV+C;uK2+Ah@$I(Y?fdjBHS(E.AHP-(/,l2#2U$>YERSn2j3_]#f"=&!bLm
%j'YB>^A(^6fX`TuE#TnT??SLo9=HUE(qaTX5AC=sE?q)GqYKY>c.<D&f+GdtWOoA)U9@$prqRL>./.$IJEj?<:2'PI55+05%O?VH
%>r8D.O7*q]9'D2AChDbI$YR@(>fi9A.AW&@DW,=1'9Tl82Q77!8tLt)S2sp0/2^n<!Oi%(n]XQf99H<7#cdBs!8A<\MMYLd)Q;Fb
%inp:salL9t\Rtmh&Sj$O2WcVIHB'!2Wi`sh&T&ZE,qm^d2-(sGk/bVGX9$\>Te;Q"*oEJXZ#9`knHpONI,dq61M9CHk\GhiE?(q=
%\p<s%"A<Ua_0AgnV`Q_fbOH2s*]Xji\o*O6eYOE];2Yb:9mp1,rE<&>)m711N6K`?KoK^SO]8SS^e2a1SOY(0fYq_/G6"H9\<,XG
%,c-'FM3i*G'W%lBl5O,$iC#jS%,S2WZ"An9e%Lh)'_@?r)uY?Z2*$`gI"Q2;;#JNM)Fq\A$+`rTg3Mpl)Hl)kNL<ThjUm6ABLe*$
%fja"MYMOLajm,>),@s.!;I2@I!h&m.?u(`0(+X)Di<WY]/PQXMi`2FSG^q2,*qtP9.#S$^;$'Un&lT,r\M58?0gXIOZhW:\Fu*T)
%Mcu:G%!&<CiK&]QeKZ>qb13Th,(I_(p<'+h_3PJ(=\+10ds]o?\XDRU"561-@`jl'^*WQdq*R?-0]$Tt*I':cd5fB#drW<;M..1n
%em_oDB4-=pPk.Y6q9G\.G<A9t8D]/-.\b$SEQI3@Ym=+_dU826]AoF^k,)a(G\Zk6q!O?!r^]!9XmJt1g2^MYrDW-%ZnYma38iXQ
%;mH)/+<>Bje!YL*ci]u^Kt!/L?1bg-[m.9m=guVs5q&+3Z5g]`$[+k`VGkbcTf/L43<#HG+%?<5p5=c9AY#ll=<N[/UG34\pg0C2
%jp=8<ad1PT;b?G0_R.I,Wl^)+,Gm'k4a=$+K4&FGl.2"aAV5c;V\cJNCI)tpd!QpUP'A$.kRI7Z^dXl]DQElUA#WSO)rb?E7BJ;!
%#l_,@h,57P`ir$X2j[WA(^XJ?NY-$mW:OoG92(&701HV6=tD3]X!$2sf&n'pi$EZ_H99U2PORjS`F1(I0u2IRg/*m_<'6KqkLB^!
%r^oLeX4,X\h?csJ&&BaEoY1--<+PeFZ0Z"KUQA_lGF+GN=>oDOJ<l;<`![8`Y?t_gNi'fGrRpF^n,tGX>utiW7b,'JX+`Xp5-Sbu
%ebu.;krc.gj(FD>+P7BNGYZeIGqP/Zo9rmp-b^P]Z)XujWr](l(7?mgiTe0^\a4+.6W#72#44*e.On[]?^;+78S^3ZXU#*+qZ\80
%g=@N<hb_A,6=JEQd%$+I,&r-N-6?SEW2U_r<VY"k]3Dt5eY\%?qeVKDp(ui:cj&bj]&+<?,s9l)55@Ydc=Gk)kVn*SO!$`n;bf)P
%C/atdfBii\/.BfJ\b)ZpZfCUIk+qD?G1@9T@I"?j,tZ)9_7X\YCq:<%nnMM<X[e7[iFO,4:o,,C*2.<2:le69KWcsRNo*_$4/PZ[
%@`]3F-tZ#`RXZ[=/>U7OCad..j(A$,6<;mj&ir5O1Vcg&!rI0%qs`V0_l^Fj!TL##fO?dS95Tf,^iet#]O9'ek<qaeo+@t\qh+OR
%hRm61EVS1L_j[rjh,1-XHa9:X:s&t_1@i+E6"8@bVqY,s#<6Mk$3S@IWSELQJpnHopZ$_@8.k)*>P%JV>FSkV195hr@$_6:NtaA7
%'^Tk#YY^]jVK2s.doOpcgnRpI#4*;I+/h,O%nFK/@3@qSS/D-%aI4"T`4;*"8[0X9T4LDq'#'=N"'U@Wq>h`1;NU6)6R-Y$i?=K7
%,8nsrpZ$-Q'EH"5,hG0!\_ZI#&ls%cc@fc,F+t[&Do55Sd=Kf.Y(Jmr&gVf?:,BT[H!PmU\(CZG9$;@''&SK>gb'("Z([P<lhZJb
%1!iBu,bInRnM1#lWDj<[gmHCGTX)4^<W68/o]PU>'-]58!Y<><>e<J`B=/c?hI*o':g%`sY6`mFmh_tWj)GemT:%Wm]+OI(e;OKh
%FlZH0)*&D&+?D6YjCDtm]#fU/:Qdji[L'W8Qi+#/`(4+L*7kbe@)5b-85$],Bl[HGV#Hsu*`ipprJgVn?K,][=Dtt^E>Om4+Npl]
%KYM;V!Lu#iK>-jekFm,EDQKmfd-i@9(60SP>XMnu4.M[c^J7ap9KcK"_j<s2WF;^m+^@ufq"MZR+K)[5=.2f;8,])(REWMf</$F[
%(\iYmQOa8`kPZq_9oZ2u!r1B7<?*^g)!i:)p5aTaR`MN[Dh`@N]*%]HO`r(T'kmhq,QGJROa=YP(6S<NX:C1fs*Y7/'$6Rd&-d1E
%g0NOF!_$(*G;j!'DC554BN13*k+8@AR-*`hqUp8tQK[V&LHHG^XZ$V]H>?l,_BbZ=Bl=\*(XkC4c$"a1rNS;!'"I"7%qtEWF0Ndl
%JuWCA#V5'b,G.tlcN#H$49lA@6!(]"8U/NVWjdU:I'i"uU'c9>kU2*2Yl"Xm.f2Mdbd76EVW2)lF4BGihs1h^iUbA'bWo0-U25YK
%7BpU%$_UT5p$gl!VW827-K.<-1'@rL2Utrb1T#1<35shQ;YA?FJM1hN`4$$2;5t@NH?L;iRd<_AemKY/_8umFAq`r;EOP\J$cKa#
%%k#ul_9U2TKhR/5^6'M(is*F&R8rdKDL*4mNi`"Z@4C"(UbDm[@JDgGrJBSUi$dgnfEmI21JGW$Leehd1Z*`dV;B=,]opTFm,',]
%K6*<8/IeQN^bA:l5OQ$B0;nr&((O0bUe4e]W*.cMG`]'f]PEn/G5sN&e#m8s9>6!&=bsNT[_IZ/a14c\C0j,62YkuoG*9Sl*e$^'
%X<lNbL:^saQO"OS.#g852;5E03Nl3M",+m+8Z1>K),g&m`&'eeMGcdb>?TOJl2m'!VD6sNmY0?S`e'Qa2@5E_fb.&VYE@2(Ys6]t
%,f!rrP5YX!Ft$/@GGH2l&qXC./-V[R%=n3'.(GQXJ4\(,*a+!5qqu:r:t?-1+a%d1'Zb\[+InOi!QPj3=:3GJkUSiC7c"`I7aJ+4
%e16WZHBH)oq\'Z!-S+^McNO-(E03bM8R+lQB;/@8(#6\DG@0e073fp78!\s*3k2Vh6j;B@8XQ-qXf/]i^N$Fm3&=OB)&D]mhFPYZ
%^fF:m;aP?j,t>R06I!/uP&G:Gf8P^85-T>:'utp-J*$7,($.:1$<Q#8>L<NQWOJa;_-q;Eh]joD&XKd<G64hq27ZJ=YbsaIm?,&A
%p%:R*g)gi()a[YtC;UE@0/lB3Nt0ZFqnf.jJJ%5L9M3%AOL(1t5nYkc$FhA`%Us>0PMhrdS%'CT&!8Q?DU<4R,%RDr&TZ'=.3K_7
%L\rQfmmB8Ch64a^56lah-1!r:(NVBNSfk:>dP/,kaG6E$kU,(A'c95>nDnPOIS8M"LgFBTp.Dm?_\DJQNc7Pl.k')8fKMV,MqH<e
%#JOe<LG$;ndXDbIlUGV0U6Y\[,JX5k5ed0S/ATi(@D[!+Q59fI"!fs/f\Igm^F;pgrO-D=UhT2`(')'aTMiK%O3%r;*SA$%GV+qk
%kSka0d(,l"[>q-FUNi]_*"bAPC,O.-7[/5r-[f)NXRiME%pNDbkt+QNZb.a5#@l9*.!Eq`[G:(X6p,*FH]sKu$0aHHR2HToMt#PC
%P^(A8#!`nW5rkI7#tJ/N6;>"MG^mcr&6?XOFYB?Jpc&cFN3c:;VQY<Z5OJtbS-@PqkLu#%LS/uI@BQQ_)P99d-S8"qfl?Yj2>W66
%BNjr&A;Z'rU*;A[b=E*Hp-u5=2cjgBjJom(P++\a-KlmBC^Kd!@*MqEj&pD8hGR834&(]OFI7Z&K[*Q'45@s@^%/K?CaA%I=)_u]
%M]NQ</-,AiZ3<Pj%1;udMS\(Mk42@OB\Fba1/)jp(8:of?L%Nh)chQoP.D82+E5`ch^gV7IL?hF!?'r-b%!l*!**Je)SlK*DMt#S
%<Q#?`\0/6Zk)Ngn!183%6@;FC9fo.rPMa*`fO9;+4dMVcKYYW(K5o5r(0UnSH%Y>7&6;,cPQr^nQ3TeAWFi_AY)[@#ZFI=s/8>Ve
%4'bA_op%j`OCui!/@#)gc9;DuR:sMiZTMq0g9BJ.Y7O(X>..F9:k6XolXX"jX`8QEI6Eej61IJ_F1+%DbiDpgXVd0\@1=R+P'G:N
%!o:$RR9JkQ2nDXSgsKE"/+-cad,DF/H80nGQqSjBAYV3C4jK!/KN1lECseeCO`.[4\VRkpDVkL1>o.c2f?FgOK]Z"eeg;H:q-cFR
%5N(8LOt5h5$ug&W'sJ=GV!B03\P+F$\/t3A-]Ch0cU)Z$lcV"`5i0,'eg/nM02?hr`G6Lu(l0nBKr_]*`SQD>/=f(8oIY:f\qg_E
%`EkjWAM9q"L"b=4*Pr\&<Y-ohF:kBo/7ARg\5&hLqGM\r?N<".<"#`ll7Y?\1oU5/f0d"r22cpnG0lm&V_Pro?0;L_apX</qUFms
%`T/e/H==OmO@9-s"HXEJORIA<A%=sK?aAu0$<(a[AA64;_Cu[5G6&;*F*Yk%.j2I.XVCIN9"6YsrfVRd4HVmq:sY)^0`o-%gRr/q
%B2/K2<d<m8<S;[^"'dYm6?L7@@Fu!V*X1?"$rb1@e=]Fi:DWCdKZ>_#NfBbJA"/.M$BKlC'iUc0#FtH<Me,/ZhIt)]_#+jrF9T8o
%Y>ND7Mdc`EA97)L&\*@b+AuBK,(NqY2i=O\;[ddh6A8c`=X&AX0?ah.o)bB?*d5\6Tp6(J#i*Ip(KeRsFj,TA^apZ4lo>o5^MqZh
%4iJg9Ts1-t,&d2B`g1+qf5DnDXPbCaM0$df0/;iNM6m)i_Al4n6CE!2em=;*,(IPO42B^%=Pp!9@'+`!=A8F+Gl-YFYjX0JjEo$R
%0h8T43!_g;h&%3@-T'fu!\!%[LT97VV-=@PC:0l9Ra(t37A</A;O);b1dj*9ot//>kMFSl#\^N]"+/VJ(-]U0]K:+M?*3*2]e0^u
%g9G'_9s/=!/.*Q8Yp!.>h<?`(\^SE9UBt,uTJM/[PWq?i4Eg)\/DErK3h`6*_i`WO"O^X:9pOP]LIY_6$b2FU]VhOPW1%ZnCD=d>
%&ht#1W2(!M@lN59b$2@fH3/7qb(ki0j9C#]'KDb=1a7`*X^)4>]o<M4-)-N'4.5/]buc1.LD_hj(>4/e9p2)A4kmH_ktZ[@$,0Qf
%FWgma6p2n_hbo__,Oc"qROIW_irph_/a&Ib_oUnn5WZ'hE.JInMF1?p9\h."!Y.1rEUs:^rZnr<j-h!mrWoT[0WOTQJU7HFQM0<M
%EEtIa2"q:6d(//Z7UW2I@B$fQk:46upse2X6n;WW[Z[tdf=6Oj+Vb:;j6<sdm*"h=NO'?2'iqn9s'u:?2[`Di4QIkr5FCFmic/JI
%^cuutL;5;NMNC;*B)+'F[bllJ$20kc?nHXP,li=^j_?9E6Z/:\2)<paIOe!7bbIBgPtb=(W!d.Eo@=aZ+W4OTotemCF<2BKAIm/@
%CHtn"e4sp3=qQEL&PEuL!O.EdjEmr9:hF99XpYEIWC0%@doUn;)>,gUo*</)\#\+m3*Li*8*/Bh@j9lq^I%T_kA<S&FU:!`nNmSP
%TY1bM%pBX955cM<IE3fIUd%3D!\!J&Mca3-(se1Lnr5aSD-BrmbActEk#G]?60mKH<q\F;aK=>V+<p&AS^A>ET)Q,\PB$2I''Q`Z
%Nt[a#hhk<B*dW#n3:dIM+G]f_%&*JZO%]F?JaIk;$W9fSbq2SG;D',.WPb`Oh^Y+*"Y3r/faK(?JQ$2G$V7f?SWo)<7Q$,$*O*TO
%XUN2sOa*%;kNsGnZ,WFs'?MjCB%SM#5La\R-rP5R-aYi0kKJJ_^kro79P6XN'TlE6`iu'm@"kVU9U-On:1?T%[jmG_Lm+98nL@r+
%>XGcbjpMA4a;Q_&eY4(dha?+BWJJ9enlA(d$MA@H1if[3F>/Gd2:.&=$dO]:Z@p'S_c)!&&jJp^16kKY5\TN)G_J:[>_0g7*N65p
%YF,n/h?9O2ArOm"P=O9.aJ>>&hP&GEQ`Mi0E%%gB!ta\g1$140\-%i&mmAY53Bm9:^[3aT9?EogOnEK-4l2o'pkWT#QZGS!%U5`T
%iJ)mc3\;[@i`937%8/IZ0qI!^'sCNqSt[b=$t=)4m)h>,^u_k)\/n=QH!_5=MA$Y)'+TS*%F)ifCI,6]8oR\J=?a&BJ*3cN_[c5<
%Q,^*%AA:E$BITSMZQ-m^nC3iDdafI7Eg'DiYONBA1Lb0#+>I=.l#0H4+DL-aR2B7mVec#@6:k$A5Yn\RZ/\T"!'jj%($daTs,@ZN
%nIdu@PQb)k>B(D4QeLZT]'m$l1NC/2"JO)?MUVB;M?7HC07JlhdLM8X>M1S)NWG;&^%H4DW8fKH^6Z*ejQH]U%2b@L?l'uI$f:_6
%fqd6mg=<,@5io+'AQF_qE@$WQmk9B39e2?1^nRf*XgoL]#)#t*f`S?mpC54\(]1K)o9_-hSa+6ebN!DsW&J"=[L6OA2rIQ6'10<r
%bH\nGAUH43?]GirA=jOR#P*=]=.@Tn\[Y9-ll7`6^sl7tE^j\@^W)('I`^R5f!VnUF0UFF5t0X:_tKsb9->4i><EMb8irq0\$MqL
%3uEF!EU&^i3=V4+N-k^Dg5opOS3<rb7p!+W"'S/r:\$$AZAtcJ(SljhXfh5,*3IG0D,H[^7(LJ&B1GmhO@1=,KV"`?A=m\W>Zo6L
%fo*A%d/pV]5H%^Fb0p@VdbeOJX2,FWP-U2%JcatN-4qN!jr<0c3Bgk9`@rES9gU'+TdX)o?CWhfYtpn:6O7^$p^SI2X?mD93Y'!T
%n6H*&!^FOfOVCXBs%9"#\=T>9D("%3nap^r]pc3(^1Z@co$\K4@4-,UYQ,N.p1oRA#]NASQ&"U?aBR;H,mb!5\bG=FNJ/XM+i6DN
%%FsE(j4fcgh9sjRWKsKuQsKa?#`,JQD80*Tn*W-VB_G&AC6,Shlj@14H/IRsTe_e""ehkc/Yl"Km,2GDGA$T6.X>.(mC4=h$<bG5
%kiYs?.rKJ[+coKu0,GV;=Y1W\[UJJRE7l1R8W/VDA0[AD7/u)hiZUP\#S4)iU5$qg@:^aGh@_GL#O[\%NR.:c"%'%_>JTb`7gQa`
%:io\H->Z&/oMD;^"[#2HPpVr!PKgVX;9MVa5p^JOP@:Ve%m_4pWts.3W9_o-7TiUqH%6Tj\!>I"&pq>_g)YQ6l<"6X:%T6X_F-]%
%3esQsk`"7RVjfb9HrG`j.)Qef191f:%1g!u"YhW<2\?%43WP:\<Fk&:GAU#]b]6*ODajYGSVBtWG4-2K6mQOL<`2GmYH.sW$L)L=
%k]I79mDSSL,-ldZR3p6c9GZa:0r%3`+V(6&W]!ek9U+nH[#lhGXYr(aJ]:1k.E*X,aB'fN#3Di>#J6<]_f`#LYQ-K+UUh"VL.AAX
%n;8fnZ$k[ml5NPg0sWqe2U&S3TBb^e9;E?[9J!YYEnZC[R>S2Kk)%+C/U#*Of^;!Y;T5eTDTBCD9T2kKK`knid)Z`21:IaoFElUW
%_0Q,oU@Z*SksaHnnmQ%!:"HLrWsW$,V?&fN<S:I-#C.s>^St$0-mj\^J2SG\PoqbIINVUnD",\u6mO/bc<[uUeUkXH_?Y#^+t[8>
%,T!T)N6!VBNOQ6cJ>=SaL9YW@+*f'lb<b(orVfOPPZSq:PGCVgZ,uT--a*8Q&Q2H19nVD9`X<<6$'U7>Kk@As/GUhUVNAmOJ6UTE
%`^pdPH6(on\H'UCR*iu!A+9Oj&/QAff7uX?1_VM<0kA%ND.CZ(C>_C&U%sMS@Wo(K3=!/V,Stum7RA:+k=^@4j#B`^^f4#q0bCiY
%g0nLt222MN<CTenagULAoO;-i%;e:LjM4bG]D4V]@N>0Rq!d!Dd_DA\ObtPo?<#m-K_Yi0JDUtS0a!4BOq2Y$H=*>K[$K'=@a+6V
%U4@L%**<cQ>c^)/M5H[CjO"i;+D)?CV.lb:6ZecR?>?d(/"'7DNT7M&#_\G'"LR.%TV\G\<EMAIL>%OAdC>Mf&Y!bI3
%6tk5Ko_U;EUb[&<lrhrs_fq!T%sD!A!A\aB(Q?BFGn(\<A9-!2fR3$*;UFn5Z+O(SAJ6"3DQ%\LOJ\5o3.No3kuLRN7(\\;"C*I\
%clgr.WNKp/@N\'FDPd,/f_iRuD8jo5F5C2-Bp[,rKh1QaJq6V,D0HF8Ts1!,$?A1FJ3dN=7^thEG;aDW,l^Bmg!#%@61Q8EY=sas
%?/TB;>&suVojt#h;Jd^7[YU:D<\"1q5jfl>".k/;.hD]f+p-0n!^0ZYDG#6So>`qRI^c#_<C6>_V2PCK]bb]?P`a_b@se@b&ioA`
%.gX]3Y+`)!WZ=EPq@mNQe)(`C];1r#dbfhMrWFU#%<+FpfHZnA1DYS9#*o=gpF4F!M#m/U>RQ2`).VTu;@AIuTo^$pi9-U1BL>.M
%]-3OMh/HNO-D`,1E,68TkjC*.`0hu@'%^^A'3KRE;t:RGM>7VXS7V.b>ee3O2'9+s2\9.qGSZi,!N;+Z*ilqAZk^0A99(/j00>XD
%&Y`-Wh,]0gEW5.=MMH"11QYEK/52>?@=4^4VE!=\KGFT,:fs^*Ml;<?TnXVtH4HBgYob%Z+N`W>*#H635\G,SXYdA`NM'[Cb:dI,
%pJ;T+UK+X?5KoabrDJm=G6!ddl!+_.#J)4KRa?K2oStC59GbK,DFfA,NAXPuDUX@T6+togRjY_.`^QuE0iY??Aj#dViR5*;)N31Q
%Dbsk@H*%/)G1H"g;&A)KC-Wpbn]B`?e7k5XF]B+_;97U$2M:?Q`)#NL_G)fA.G-BX$>6)9Gb-iuf5@?Zde,WW<)"*\AW,OZQ?LWL
%_jr\,MPq9&:!A$4%^O=Afc<'aUf"[q3CV>8NIW_^P%p>*-i-:76>&pL^tiDr"Xg5*Ws:Y3`K1)o1<1?HYG;2&YVa]TFXmnTc.pAq
%,,%X"/tK+'6[e27&<rr+1m"jC3T3FI\QkGSW+*ZR,urLsetl'VX?j;*\-h$O':I@n+#@fOK]\=&k0Eeb9r]a_=7uK/Oca:*0+ta)
%amC&c%ZTd<7FXu^`+<FNh`pXR7U#37A7[6Uk;%2d=\ndu*7?$L*)%Y:A/1Qb4^thu2FDGk.lA>N6;[&oZud9l<'J1lH)r`gXX0\h
%@f8UP]nsuG&A@&\<Yt=PnHP&h#9HS20ael@d2_e!g(h,[m#'H\cmOAV1E7ZVfU\3V.UCT3aiVL-rA)cp0M,1t1Mi-cgC\X[U#h*Y
%PpuJ?nioU=G[_69(Nn;6:I(+f>L0$lS(\P`+7riu>\PSeG-#-"J"MZc74@RTR>OHnJ0J=\q]K>h^GZpYJ7lG5^?FMmY\/A^m10TB
%7lOM]BciWa9a]u>PrYLZX[TIi2O3/K$BkU?6&D>9*u8iN%':\I4rr99'6aP(8C*0EX=/*9p^`Js:YlstGq_iHI?R+)k5j]M.WO)O
%F83l.'KaC)2$l]:![0"p5"6f5PK!WD,5NTr$;M,8?+tSCk4Rb'nLhBMJ,NIKjqkf/jkp(*fDjrN^Gj@4J,Ttnrla3toq29X2ui!k
%huE!!rp#8GrU:W<qX(Kco$;g]]>+@=qqq=\^\r#2`GZF4rrHX-mb?XsmGA$KrqV<+rmL97qX87gI(^sO)4sJ!jd1I&55gCeQLaeM
%62gEd$J,.Ls$chW008No0E:$=rkl)4YQ!\E?iT8ts6V0>VuEkgrmP'3GN.<V]&mfS%f)cHLp1t:71U=LCe(=WW-iSdjVb!a<Nf]@
%+=LA09LXQ*[4#"d-<+.9W<V=;\lkgcp'r*MWbqj"FKhY:./rU-,qlhU'I!UGA%;1b8LAl1go>R=L8nMd900(]aj-r)dP\1`o!SOp
%^eZohfHqM(+lSo52SHim>$ZTHG_l-A=?p*4?-qDck$Zt(6>AHr3a-VX5ab@T-NMGER%BDQ5IR'V':0&b"caET@o/SUU>'V@&HkIO
%8>TT#fFGdS@!3>QO9'hVgUKqCXu6TOR*$6HX_JQ$!bo9V5nQ=fKcVBfpTmPm+l3EskjlB0<t`YA;;IpN+U^bR^S2&mcX9LfaDS5Q
%i^J:)^cnH6;1LUDaB<P7.:p"V4=rm&NRsF@`'?oS!JHY]c??GHD\=mm@"5HAL,+fmDAG)9Lp%Y]=^bA'3In>P0ub@G/)VS@^j;.3
%e$Or#,)D3Q0'$hB--(+&&_7B"ZY!BMd;:'i?djp8hjsiU"\`002oPQWNeUg`q\.B6-G%4IA)g6]@t2u.rAr.lQ<C.8S)s4^iIo>.
%T'cu,7gR]O@GhkF9[=0\kANP4eZ:rY+6+#JcuBO`NQm;O`@m=Z-Ps]+N!a'FFV>"51=6@t'XL2KCb1=3FtB*p]3rm]o7q.%Z7rUZ
%-hd[d[[/"c*(:-sEj+1kA7&f5;ZouHUWHap6q5@Rl9jG7f7l'7_CA/9!HHK!$iZj%:J6j'l.Q_-ABtls5f$Ph0FA:(<EMAIL>
%12<407\G,)fQ&Zb"\dPt-Y29F^jjLJ0V0lb&R:)-QaBt]NTrHc*_$\l)2$'oq3td*K$8i>Z<Xl8_eEBLRp%R;dC(DH_Bc+X@3SRr
%q-IG@Zi-]TKe?Y#!,T!!O9`9Sl/uN#+)->u*<7L5]"<&<LU)!&l;BB!WQ[Z[JI*9&I4sFt9Y^*a@?4XK11_tQ=B578)bER1aN1o2
%F0&ttOZ1Ys+ZRpdc5UG+NS!XHa19^c2]te^j0k3$mk2o(n`(=nncLr.kh619L&/;3Y%$7`S^G?2T1.nJlFl-,M@-8=KAZbbVuh5@
%gFQUQ5[5q*KJgr!+:`)<pL#,Wq-dhJBnEE!1s%:-&%cA:YY/SQ043E`hu.9b,ibdX=',?<EMAIL>%VOu<qB=p6DT`@'
%LTn:&#4cM#@?TS?%#$#&SW>l`n3]3/LEO6oWN_JO8>aaNduamI\=bG6Rpu%$3^WHSM.a;-1@,gOk2feU)])Y!Hj,/%;O:kW-pCq6
%Y#<H#,>:s=3.2H%VU9><:DU+M[80+)Tq[Wg6\ejBYuiqI-V$S$gE^!:e%4$O&G?74ecgr8].[$>A>`D6>b9nL^!c'j!#MDcKP3Bo
%K%k2#i"F2E`@m'gQDg*JigfBfKRVS3Vbso/'Ypf.+otA*j^LA%A+/Vbh6(')as+N(?F]K+INd_j$9C$u/[k_^R*6>Mb9<(C?h,^t
%nLh(#:n2l&[hZmF!A3Od=pVO%<&^45F+H#mXtP;kPIKK!7*LPHX"\/(6*:LCVPWDlXRqC^3X>1Al^+n?BXGkrFueT?na_JtF!qQ\
%U&^,l@Z&:%/"%T/k6bD39^k+D<FAm&ogYFu;Lo5]a>]"M!"U#h=GDm4_"TomWQ#)P^c>>VJrF1`%:XA]iU9l_-S/hXlLOgFjY`_l
%.gWqU^B*IZeY%aCK7`L,dTdFbZ4k#lS&K,^BuouU<G<(gR34[G`$5in#2N-)UkpA4?OVlATbV,=U>SDNOf?C_-eX[(,A_Lq&OA6J
%H^^:M!J8VRLduVA/PmLiJ>C*[1o3+mZO%FK=7D.MdBj?H9u4G0etR]a1)-@5ml[S$*l>$j8&G]r^r:4\HuNoa<\KO$<7k;>\HNQ<
%,ki,;[3u`qX+IMM>aZf^]s3aLFINGNbGO0F,>Nq?9*OtREeNEhLjU<:``(i=G/*:XBP.T(E`h=+?tIu%dVM)A9gIl(eA_PS-`p9!
%GP>]dJQNj&0IS/8\CHf)3Duq\'b.rZF#FD=1)K@+"5"g:a!leeN27H)TI7eD!H)so)sm86p]nm08@7s#W=(tVe.hs?EI5Z&NZR9!
%E$_*h+L41/g<UNRa)-FTJ&OjTcl8aKkG@PbM#`"L/0%Zt[b@O[b@`\oNd@g)B[1A?8Fgin[qt>GKfsBEUm^e57,U>lbH+Gih`[.j
%RZlS,c%fJ(4cYS,k<U]`*l)"9qK97**9Nl5HT0MGc#*:q(&^BLRHb9Ge]7>RCB/^d$ndi9&X,b&N,!]\\WR60ZVX`SXkO=Fq^I=4
%L@pBM)E)ZKFDLA3FKsR_qHXn=SYGIuH]"u]L2hsb?.EeX/uArIAh%],_rutKiKWk/"#![iR:@aF;&YK3Pf"jF+7"s?IRub)SlU$V
%e,W#i/N#BV4*qe1&(1b,f5/=!Zd7jb[HTSbU_XLU]Y!UMrpU?I#(j^7T<lYOK[YXOZk@UF20UUUR?<*%UFe@uP3q4g,5<Qd1Vusk
%C&+*5.BZhgMiJWdAtAXMQYuWe;?0ilWYC/D(tG+_Wbe#Q[#YYfBn)+/W\DdF-/m<M<2`-+1?2Ym&bT%\USoPX`1i0(&>kSJWg-B1
%]jYrm+^u:T>7;%aW#qM&_b@T!:A5IqqClH`eVoQZAq_j1F+@SWXdGEC%GPDN$Nd4b(tWLloi*`r8&299N6<(C&>b5=!A,YM#\/=d
%#V(f!rM+L[Qg[)C=rpM=JkQMqO!f!&R*ql<?Ku*N#NY)[pnORbld'?'#T`?Fp20RWdJ^35ij>JejemXO5VpS3GH;Ss7/m*t@Z=HI
%:Y+&pBM:NT^1KpOP>uK-\C/fQ7PGPjnZ"b/J]jrECP&a>l%/eXq94>WWW\t^>U>lhSQ%f7XLaN?:ND2%Pe?DINr`Z)\X4i_UQ"T]
%PBcA,]5P<BjBnl!(/g;9iKDkD[2>Q0j9gDsotup13GWb\.8-"X5Z4P9=J]=k\AtqfCEd;AnlG+DjrJT\@V%U;i=:CFHJ@K52Asp?
%?n,0J>?Xl<ZIc@A3'1(gTUG3K"$r5$#@LqMjg&9:jQ3+Oem(Pdm!rB)`\q26^^:K'HQ>Vh9B]I(%Q4\l4BYr5Qif@kR%7Zc+S^!u
%"p00c@puC-]j2\B/!DG2i(U?9+`'!./:NjJJ7C^ph[P^mh'*r]7O?8iZ.jq%BWbZPs#fF_a<4s3fhH!kYmRsO$Z'8hdaImLQNXrE
%LD.@c-6-bMK0*V%5g(DSO(4`!PnLTs6tq]q@4!T5[$T#&lkKtmYmfabZL+Ted\Yfh8kcN=:iU\<6\<*56plQ+DAU>eQM&HMFpig'
%L!--4)UXi[p%nsFqB`V'g`_!Z&^u/q/DQ_'Sss9_B-B_6plkbH[VEln)0/2Y0`M^'4E$Vq#mZ=T@ZJ8L'Xo8uH%N=jbZUl,!tf&U
%T-H:W<)X);o"+c(`i\PC\$a'\fhi->'?Q:'2^sCWBt^?$i8[g]`/Sl"<Xi"Q67]@SE^uf3\`bIfe#Z&Q!mXa$D*IX&F!*:i#(U9r
%bn8Nf.Q#<P+bUgoanHr;1@WB:OI$#T'ljX2<G[eddY$$s\3*2/_M-mClhs:.UX=7qd1Fkc:q,lj!cE`tmA=!UBQbiojY/EI@X"Ld
%aa6D$9+:oJGZbG9'n^q*3Ri.N:O"B>+X38FYel)UckA3/Oiu>sDV6u!4&IN-VUSl-ZMY.^h^o%jjF3TQX:b+q3uGTOjC_$SD@u.F
%-?(\S,<CTg5PD+O.Afg>0%365Tl&)XDqsL!,2X6)=^EF,^2J+<b_7ga3c;ro8s/R:;)QOSRR1+!G]?BYE@b+c!CLt).l8ad?uBbp
%N5/n'.Yb'U'n/o#]8/_2HeR#@CU<.V]tAt93l>THC73+Nl*S?D/1Nk]>nb&\`FYTAi=pHc<tXo?om&L#R?^#@d"A>nB1&fU/7Ol;
%7:Iklq%P9#_]uJoaH2j5I625j0(DlOokQoFpnf#MWsDSPQ<`*5DMIQeWBWDDPTi-uneDC@K;)>)4mBgDpc=u,UH7r$;kdk8Vn20)
%7Qb=aC_O$V)'UbB;Xt)<(^QP,l`T6j^4FJp@^03QUe>jWQ=Ah?@g_0!cI9R)oge+d?2$*nUdQu?$8??eWqBg)Ljot[]<M7@,?47D
%Ict2Q0G)_?G1(MZXr3rNT7Epsh*@\gA4?Q/n(LbG/8VNN&aRd^khDSm]j-)]pdu)2'PJ,>DPc&WLTZEPbf0.19/J"%QFgqJC?ne)
%9c/LT$mVLXpT$;WpgGj(_.+T4Fk&0/LHpc*7hi*JVVI\M$UPo_YtTI6FL?/?b=&6ufM,o&#-Lrc-P-LE&>&hum<q[JBe>Ac"^<\q
%gR)GXeZ_%B:DWbOKJ&ui>H)E5@D[jIh"$06[.]"+reklbajr`%&UeQD=f."cC:Yht@4O]m)dB?%NPGs"AWmNK8)XHlWMcY6;rJ-h
%*EdG,lmhoi!#NL8OI5!`Z'<VY%.$M^NbD8nrG,;h4YFka%8"_0MZicChc=A$3DkTO#Od3-cjS9B.^!<dpY&6$ZXjA%1m6g,.9(/.
%OR\<L$L]RbN%W]ub8u>85ja>1E\$aFI$5jrV\5g\/d/$+Gp$>HFp6f#Il9VVZk3PNag>'bh9.==7@T=LmLMc3Aa.u/IAb;s=Y:&9
%25X?%doDqtm&L&XRn$".0-[JI.RN'oaj=GIlF>s/F2J#??o_aD@lqa3@?3A+XI+XNh1`h]1<iMX&ZR6n+!^VVJ_,g-R`R"E*6]jN
%U"dJ\OW?jlF@Ck>EirDI"ZL`U>mJ6I=T^V4DEDC;iMFqV?7r'ES#%Q5(?/`&7RYn):N3i8%:GM1YQ=(%!J)Zf?j@P&BtDVV-f\8V
%Y?2i2#._TRn\dA,OJkqk[[:dY&G`\-JnS,uP>EnY'"2+*T$Z;Npf>&Eh\]BqL!+.Mh*&O^$YUYth'+/pF6l#O*JelW#XXgoe\8<?
%)A#+;o-K;ulCo2C5H+7DY>n:HZWAka],hdQ)b8B\),#WE[qd@]G9*"Q[b_A9M=ps1dKj-cIWJ#i0p#Su:4\8A@IH#g[K1"9S^#=j
%XW6<UO_dFTI(I>IjQAIai3HT#QUrp)_lO8Gp_?.1"Lsq4R"<H?8M=D@Kqrk`2W(5RL,HH2gga^`'m6F8K#NEO*\-,hSRc<sX#Cbe
%/fV=RO>bg@A14sk5Y<:;H+Jr,Trl.>="llo<!;nK<gD0300$?FJ4q1bm'It7_jNoN&1%K:O.9huTY0bXi,\;m,Pl`GP]Z6r`BD4u
%*%>*Qde940-C[l9Mm+S%Jfdr7L8Eu-'(t%G2Dj`_=B.uuW(4+[LWpBEn(o<kQ6D[EDE`Z+apPO$1EaFESOPmo7LPUY/.FZVk7#@1
%Omuf6e:9lK_J4aE(R&-J;eQ@JX7PM_4jRRMBaT36rgrLcp^Q<U[+=A3U0VT-OVg_@lpV$;><PWuKYhS/p>*m(/)\NlE#TW"8M88Q
%Cub(#61]_\LWdECpHm7;i!(Y)5`f!*Ru-=pDF`9WEp(\@mRZ\M-LA64f5PbI/"3UM]q:h0rD@Pkf/O?f`R6IQ\sCCTLMqUNNS[7]
%_=f=))n2s$;O:S*b?2FAbD=.I+rIeu-ndCp*/Hj>?&jb=Om9YLif'6:b5a3o.9G#f%l7VS[\SdR>W]!dZ)"c+b-^=KEkCdZHcNE!
%Yo7C5,PhU754rOdRR=coi9m$AVH&8#cK!ac.4Tkujq\JbeVRo!^;,*ZB1;L5\g;J)g=j+U(]."\#i46D`\ih(N$;hiWRLI?-_ind
%!('bCW^_Kec/t"b4S,M;27PnadQ;t@[Vcarm$i1B$3_)5G2if-FYpZPUT*RNelN9\"<ST.O,hY:3!dWm\XPljKj_+r-5C\8CO-S_
%>W,]QR=32Po5Z*n`h4<VgTWqgAupu:7&[ng$m\&5^6'P*dgG!W-T;PG3eA5_,c]l7b89gBqfZP'NG49<alG,t>X9Tkr1#ffY`>)!
%'hgl@@H=Crh>r/.FsQ.3)p@i![*j%U9itpMR'1*k33=sO4P5q'g!F>+9LaB$!QHnDA"OsDo*>W.V''jQl0mPK@/T)Zeq9'84//Ti
%XrPl+B;<KhpCZ>%Q=p)-F:o.<)AdqQ0>BAA$Fc48n"R4CXlFqO$2RRY1Ob3`RZTlebVaF"XU(DA3-g&2\0+:m]<[-Mc#1m3*KC],
%@.!3J*!,R?OJH(t*(7iCW)th'?A!7.L\Uf5q[eYo8sI@.@#Kh3]AZe4ob\%M1a:pC7XQfF^D@mp+#Qu-<sA6N4&54ib%q"hLYD9E
%n"XBlX4=*f&`/G+'mIiq8'hEW*C4DcEq&HYA8)0'<HQL.2iG2@R06LaE%W$+T=JJ^HbiER*i*=))NE5Y8-*+%Vm)Jk3$T/!%9%p]
%>H<H/L7iM3-Pa3t:'8E@9Bl*YA4%W>;.Q+fI$;5M0GGjjDOQ"[AAggj(8q5BSjT>n^:;uT?,\LZn9Y;c?2S0q^oTX[+I^Mk!8^T-
%/.UtpREBu\[G;,HD:JZCBN<CohN5<<!b0SuPSApZ!,p&NQ%P0$IJ<MZCAU=?a76=@-Ds&HgKC1.9^bLgooZ$B*#dEf74a:R'7'q0
%i"U;NPs89L!f@Hl=%,Q71`BSPf'&;G!#(=]VCXC;ab?dUn9KOO7;9[#^g\.Ao`oDMF<Q#6XK_%\(HJM,@Ik!u3)^1PbKR>9k^#.]
%b3l1[7=PgEc@&71,LP-!<m+i/Ge@]YYWB@Djd*o4UF#,Q+Xrc]jdldC%+Y#U=QG0p2RdIJKGfSA>+ga,!5XUM`r9*uh.@=FKTV$]
%G.W$hJ;g?X\Jt8(Rojf0<6kfk:$_n83s%[=M\%1Nr9%]a8ttuU('-b-I!C0Yg-3Te;WBbTnVt7jm(b%.+C-DMKshrbcK&*fpO\`N
%#EEN.n_?,/=i'f*Z;:3mb>4Z;QDtC<$Ol;gFR\"%pLQ`#i-MdJ*m8rdr<.bbfr-Ml_[Z2+Yt<b"0o;u#;?sF6*KaQ7PTG>t3EBPp
%B`k'68V@EVPDjVX%C^s.5TSeQF%NqhV#]$G#+1Xp-Z`8YPNf@iA*9k[)a?ZBkh(-f)QC6Z]im5n`@25p@,r68GVK3S<YI96-'ac,
%)`F._OZB<'L5WuR3.$@$)mp6SBe$oKW\1t"$LtDuDJXCD]^<h(lOg^$ALH`/r,>SRo*B]Mg(WL=D/-FXDC\YJpuS?La]7\[&.5(f
%AmH".>W'+UU7Uq!P_GWeG=Y6JU=o"<SCqoU1c+aEEoA&9KeWT,-9`;iBEgV.^Yt@k>8m(@jA+WAOA!4YZnW03KPt[&L>m`EnIB#C
%B1qKTmap^1j'a;f;%(E27L[HW`#V-3MNFI,h0t`5H0RD,SeF"D)t=Z8N$.C\Ie$CtJ3B`akGIV`T^`#%,1Eqrn\4X/3:WY$^D"8c
%qHX1mVdqjd=$X+"Sq6ZMVB=pZf6g'mmBkriPibN/$La$=7FFTkb,9)qg%f1e)t"(P1fa+p2:'\9O\o;Q,56OV:nlbT\8Sb`JB12\
%Cd@3<KFS>8T&Yum$uQTm&!Y-uFO?Pd"Va=g<WpElp1UalSknn>Ft*+I=ija\IIm1$0*1bXR'Y("s6:,8#bI6WAf3ZqiJPA#`toSY
%M5?$N\3K68*#13l*`8aAWHn<Fm,'@`%VjV/@pL^:eZfC#E)H8WU\;%'ijP*+n52#n*Bcpmao-7ZX^*M+M3m?t,m+^!GDK1X756U^
%kJ%JlU?piO)&ea"<c_T]6G4WLUiGQfPUB)58N&R$`PfW,,P9YP2ss$6@Cs4'fp1tToP!JM4ugr?lZ<bTT9`k?qC+'8X$Q"YpQ)=h
%E\W%k^EF0d-?%D*;t?,8l[k*]Z_1p).+?fnYQ.C7cB*%+`aq;pSd_9pqCEr)Kp'r#bVT.R`Js9)!sg.j:r!hiXQC`"ToKEf\Lg#L
%606-B"`pU>M#cVqYGU[X,!mn.m;!<\*,YW)V1ILcW2&H=9b:W*D%B"F0:a/[h5O%]>t=+<WHYX9d.1h^9mLsIF#jf1KTFIeX+4;-
%;I]jFY*i<mlo-.aF>f1!iMTAo!kbfQ?U4kR&\8j4@*FD"KcK(4_RCWkPe4Aj_>/61o+$qa-NAcSCAV#tOA9aZDe]Y_BYr,&NHq=e
%'c%2gG5mjd.I*@'Mr,n_#G$Tb49AY=o)S*@j9^cLL'7O.f*T4A9q(T_:d*0XgXX48,h%`lB'rn$<Y>/LLg,L9-LLa?O>G3.YT1c'
%gHl:Ck.u2h&B4^W2t$`\(=u'd9H8[?$-QIs!E<!SPGi4YIkF\8`1ubqI5`!R&'c0?C+dC](CW$I]Il;!$R$lnJ:t:?.3q'iKJI!r
%Cj0Z5_p+*HG88pe&<:ManSSEVIFP>_U8*@p*T=>D_OjqupeSqFoS8PI-!WK\&?AZ_/c,dX)E#m6rVjRqTCX2mJ*8u6VggFo7/ght
%O8iQ+l"@Ddh?eV9BfrSJ5X!NZmtZ^!jiu/33-_@]#V>M@;1c;U"L&e_S['::V^u$.Kch1<[^_VoK,O#bgHc^[mPe!>5i-DrTXuq,
%l"\inlV&SuYb>+3c3%L)BQ(ffa>]dQ+p"JCS8DXi"GRMM(EV_t.T&7QDA3oS]os?eZ6H\*#I=g=UNIC;20<^YXgtGClh/,VL)R3i
%A:;UEiS6rRjAq,7>diagl)h6d48/f<AuFRjkNHA0e@P'SYNskHRG2#+8Op>fL!0;lO7PuhB@m(4E)bXN+qqd(%Wq>h#Il7s\DY:<
%W4odr=)K2dP#)Uk+EVfIUm-FarYSu,.`5WVSkAWQAU$Q*JpVH5,u8(.WBF#Ao5S=+ncU1c>PpV\DgOLRAW6>-knSXJ<[$9%*qfp3
%/r[0K6s35?[%YDX-D-(\!jMQJ^S"X21"Z+o-)IWM[!G\&[O-t#mo&:'e6Brq/@iDU.lg$/'Gmg:.6]cfPPLfW;+I,u2T2P;+fa;r
%jQ]2=37iGsScg?C.1T&?(V\?6Ys`Q*[pAs:H`N6+h9j/FNt#bW@SIqS"SXO(H6F(*Ki/aFapOr"=K4bt0)6B82>rj(qf%uaPa52P
%?YZm=<N>@JJElO'%2^=)d#:dYR@PcYJot/.*@N^I"F("L`A'W'C2oEQW(amh*47aiMp4Y1Dh;)kYndVQoBhu&DP]Jlr0^Oe?rS^9
%9%ULlk,@37IiioNj"_OJF.,ER8A-9]Q[<g!DslbhXLG`Z>EU4W;rg;MDAsM_enj<GK_FZ\-no2u=M&_]@#Y691h)l0`bO:IjB;"W
%[F)Wq#qNNX^]lL(,=Q.f]K;;g4CGK`hVH*q`#GM='Y,"]LaRhIqCP6.id31u%ek3,5PYq**fp0N_U<iSEj#Wj!PLqqh"aX&gf+k?
%h#9s5H6$ui%"CVoGBHuElr>pm9,*7,*#FgXU1"8?C=4I&2^ipdLG`E`l#QO&GD,C[5@81p4jc#b,_\r3(G/JM9:B>L0$c.Y_Pm9c
%>"'r=RtRTcqY"&YUYVe460HOqd**W\+'5a2"\CR\)VAF.E#0He2`aSVG)WZ4`t:F1X&])IfZTD4#K$SZlP)?b,<O9S>Ho+=J1CZ%
%0F%qW,t`[?*HP#SDK!X%;a2\,DO[^1"<Y(43CusDpFA:"2>4f^7`lrNq7:p='U"?P/<MAado=lc..b&^dm/DFJWmqs_sAe`;M$QP
%*9c:7&o?<E!R2mATXd!i_GX<6$&&O`MRZ,a)WYsCfp1W-VWkf)Uad9EL39CWWQY!oi'u.ZlSq.sn$BlAcY3rH'Qu+T!1t_2+"nc+
%:NA=TX@"I[&a:Q)0uh_<9cRhN8;a-hR4*4"Yp\Ka/e::7%<9'!$s*!77\`Ld(Ra$'CggX*d`WU`R3X)j]k#5pFldLqJkErXE,"U2
%Z$+d-X/U*I7Nh6i2l?%DZR?6YD0WeH-rhO;gf0=kUsTQ])VugldAAfp^k',^j9VU-L0KNeU9ZK>/p7b*XUV7'"i*JM0#Gg)$Cf0W
%XWQ&'K!R>dCBG"H)cd^%kY3'kFZHVi!dJk<kU,5;0S$L!.1+XF2tH7CYor=oMd,OcGQ(lANSW_kD+F#Lnh]7^LV0"V?e+K-_();n
%p<n"4o<!aAACY<gN0!N'Wa@(,rgrWmb&frerGTK)U?FdV+'g]l\LLDu?UCA\Eh%*nn8lk!2NS]KR[IA6JNJHE2lI8'2k$m_DNFsf
%O$59"0k,%lduXko#+J=Q^>7Z^/&pQg/5@;T3BLJ+cFr"OLKs0Lh>sYb0AoPLbFT3.]6cD:BQ,=^h7^<=LUX$_UpFk`\iC1!=E'<O
%L*$ZSB;E+m2p\j/3]K+!I+%=q*S)!3Qq0ea'Y<j+auG+0Wp>ojn)j*jPb_fpBD@na=mMnRL/t8<8m7L7Qi=5,J;;ZsLrs=t(U^,5
%Z3qIAQ53eXN%7k9C*Ou:<Aj?"F$Y7glAlirST5<o;1?`jmmD!67eA*l:foj*=hTejm)W[ge+C%:\ZA`8,i=BH-uGW#KgBrR(]V]"
%+6JZ\W6i?i4%csiLsAO-YXl%9ogZ,#j]mluW`hd.itXlt2Suk,[AsbM#Sk%]BI?^rd6T"JcJDPT%t0_\1lFt$qV=Mu3N4u,_@1$-
%[I?S,^47443[s2WhT;[j%G2D^m1CY+'j0/BQ35V(DbMuCl"gEsgX6:B@>&*2"=YifAbrWj_$UW/JTg;_aue)XanjNJ"b]ZO]qGa`
%8L@GJq6u#lKY?#Ol.MVY5:&CU;Z#P.;cX38OTGcL6Yn[hR<ABDmuVX9+*Wq2K7HFqiRY(GWbW8&J`34TB<.7L1jp,32fc@8U\BOm
%B<_]eYchhU+;.-@4QRcRk2M33%A0jto1BAh_-ad%*f8-WB!u6Xn/F_K.`kt4eKjg?W!YW`J\lHhY`?![`1=Zq;6t$#%C&E]SkA_j
%o2[&=lmOCpQj<Qk"tiang;GDr/n56'lF,Fl9ikCIjSkaZUN#+eArpVonZ:7b@-1C`oe$$D6V,/T`u)V:'l[4(m[]AlQ>CY_a%NAN
%XZG"9,CF[gs2<>sor#oP5UlWWO>Qf::?O^JeC2Mn_fsqNUemF$P*nG_P3e15@2"[Zfr]m!RRE:e:DTI(0hi<fZ8%`3=>rR'KSetq
%#"hrtY+f-D'J*0m-hB^9AK0tUZ5o)m6#EB1':$^L$n0bNn#+[1Gt<+t\U(^3r?7LZE>XqN7L'E0@+o"=RG\8`okE,.MirpR.a9$O
%bXr9dW@m`REn=NhVl&1tdOc^S$?ZdVr=ZM=!"uKng4\8Br(Wm*mmHI<142dH\f@Dj7OI`YkdU*t_QWhRmkr7nQpDK^JpV"c%4?3>
%&<=]*_5PE@NY-lqP+<H>K+u2eO)ULMB3U@A!@ItXe9qFnkJbK<IO9Fod1('+C<S8DPr6%I]IZh%fbAdlKW9si5#IY]]V*E\R'R/5
%-GHVYnKG[1^[LT2S^[0:,MH\Gf][$4-FZNrYnaHFS"_P\d(s4))`4Bj([AA@@E,(8HQW-VboLno-hSe1B"H@b178jp2Bsrt_l0++
%U18rIG\W!ZXt8W/(*rr13.o!Q2nWp`+]]=:KJ!_[6Q[/^)"j7`@JW5A>([eQ".S%;3Ja!d9C;:+2sMd,5AC0ZAJtFhGo,p2bK%3d
%%=5[UD[ZZkRA6sg8\)Wg[fc"\dE5%&?cX.-[5TZpWi=Xt>AcWF^8fUr[WXM0<3BCe,/p3US^a:fXgNLS"&Lt4@_]b[osQuZ4`"_i
%XPU?E2a*XM.=/Of?a%$jm]cn-)OKEbJ.7CP*?9+m$"CeN`G;-rd"@Lu2K,5P'Yl(S>t%BW]9'MhJa$?2jt<C0X&8b-T^aC*/..)G
%A4[nf+(-@5e>-^a-#'&'h'^"/1O:jJP7nNE6MFIefKKS_%N,B1r3bJnegTHQ#AYl0$-eBZh'WKcXLn^iDU"*rl*X=%Qaf@*X>NMh
%#!G(Ki!29CCa;ta+p`rbJYtbC;M],YNmL0T`_N9dH7rd^e;B+<cXhBRI9ecC5cIO1FL$3:i[ZYOfqdWb?KMqEqjDc[C8-5V-3Nsu
%05$N?.`7<AQ$W"bA5i3;Y$'7dPrFDflAf4'MNG3IeZ6B_SYgTg@Lt@.>)HDhccYH86S-gZ_=A,EEVme.c(GTjkrlbc!HcW7o9\.6
%2`>tWC9Q.0_n^EUeHp/7E&0ofKd!rd\]gBepYhJ@?1OR!8u6o+>gr4%^CTl*d\E>9`>E-TVNCZ.:DW?i+Y2U5*LM[MW3]Bt1B;%:
%"ja3Y'Z4N_6UUCg)i00[`ktqs(5:0io7Qli]q[Q:k#VR9@($/M9f.L05=B2'r*/MG_;.h,=EK*7l!N?B4\fM5W`Qh&\<;,Q+sO)]
%;QL09"[E=/=-$11[kRqEm27&N%gfOfUWT7an\U+<WYlU()4<Y'3TkKYX:[nkh<bPcGHk3OZB[ijI=U:A(En@eDD-!(o7\@p>1>d+
%'mcl$:SC0ZTLVodmf*^&49MH,$M_*am%OF'FL(-!i`gF6Ak&1&I%gZVMnY6GM/"*HHh<%;lZKO->p1TukZ)sKrK1@NN^0FmdC\7=
%pLX]EKWFpHfY6I-OD8m[(*8u4PDGsc^$/F-.9)-2p_9h8n.DN;B'8?[T:>6UJZ0Mcg5CXsgItTue+H%X-84R^;9o#D(su?2=$mDp
%Z@PHf:<lKkHipa2PCQ'jRP/+43Vge1h"V6#Bp#B8kMFRP4V7h^B&n*nq?\?r1Vq_."(L,_B)cnqff'L(dKT1AVlEc1p+ZEA-r]!=
%N7G`DXT0E@(q.qrS,-GA3pnsC;>OER,*Al441_pC!^[NrI#J)"pk]iN\u8Jnio)JiM8Q?I+n\:fi-4h7cTSH=&r9e?b'Rh7;ugKa
%BgK%Mq@p2u;Bc!ne0PBLGBC,A1[\]T#b+Df>iYRu@%qsKK,0#\JStsVRjN$_/#Gf"4[tg\.!e?CUG$(2c,8gDb!@A]91jt)hj0IZ
%gmu'n\"SPY..h#e\MmTEW5\ZG>gidsAbT<6`d!ApS=0%<c9Xj\K-?O7O#ql+Wch6j#A%*Ji+P)O(!:E)!98\bPqmqg0W<;Ej5Op>
%:a6DCYYka+%$bMR3;Q:`FY>HTccGLZIU.%AW/B:MG01(frD^OO#`L<l=`f):n\,.NW?cZa,EVKF@r."k1$1`4?6$Ce,;cHHe`Tq<
%Z7XF\K[O2EKlSnh:u6%g8M1@C.9OUaGLFu;'D%F>I6#ks@O;><ROJqePEi\f*N_qcAuq,R<rSU[dR^uT6:__Hdte/\mp^JYD5f+a
%j-Y^um6odo!SSjs]ho%$16K\Q-[jamZYkC-II+:!mgdVf:hF&!BVnsFoHfLT9lK<j+fLC'Fi=5].:f`IH[\Ml140+l;@3^_08@%X
%n/ZK8QQB5cD%)B1=V"7EQe4[5Ua9ooA2GH5'JR!k$\Xm$s8<3i`\+\WW@Y;.5c-^VdUNVZ;T4mQ3ob8RJeN0=)UU$DKs6RW_Tt't
%O(\.b]Y`=e]i'UbPns/DR*Tta]1C)%KG.$lkhRfB/i:>t>.>BiRp'6C;%>r&Wh:7B*4\1NQH*QZD94Z6V!(^`)H9=F+%JMu'M\1f
%)p`>PZnKg3c(X;$Ds\n1"G]Q,%=%LO6U(eSI8b!9rZ3%-U8:3B<`!T<DbnQ=(Foc1:h4t8Xm,T"--dfL9!N?Fd"2+9r&.I7KJNbA
%pc7ZQrM?g".k.N'&4j5n'91L>"O2E\h)?BqEll'N*\4`Hkr2?-M]aajOq;60ZO4C4>OZeZ(>q1Tn3a>Z8UNpM#Liu/3:RF\j4k5k
%&VsS3]:iPl0#WQrmM,&m9MAO"$GnsC#\r#GV:tTV%c^u"@o6[e-Sf5+?kghB+(HAZ115Qb3mCmi11%@$XF]N=716c+&MeS^LK:N-
%FeC$MnZcueg;To_-<<$#E;O%7?uWE.7i]n/EacC))j15&b9_#(nBQ:-TP`@[s$,j=![?:QWok0r;u;G`l^E=`,!,]1bf1>="[[o`
%W6e)?4oO'Z>8#)<2\3@(F1n%sQDGT_=TjjbAhX,;3:s>OZ[I-<oBid?+EV+sFrBIF=n4CgE,uD?dbLn^&pf,]1:bN_(6P>ubDf<3
%UNM%J^(?FcR2`2>!Bbf:\S:;c#pa1q\C\7%?o8>BplD*GV_KFCF0+jG79tt'B7qJPhO$Iqk&/TICA>7+#g:0pHWWGF3J\n9Am?D)
%^2ML)lMo;E^arA:";[PpJCp@4DVY"'nbMJ&"/a-Yh1Z/:gl_i::','h*#+b_]O#n=>(ae[B2lm`M=j_;_WfX%&sD:5%UD[YLrsN!
%AmRU"lAcDi.Zg:9W1&5P?_Pa_22&T0.$:kGg:!>i9Us]l7PMAb'oT>GC1oP"n<i#]Kc7eTBj(LdK>J2+hW?kTHHkO&,'*!ZbQ:@E
%,nH<W=1@m:W,dSTK_^f4MEgK1(F;[-MAtr?7W8T6@9u7S9nGhd&DP'Z/M^ZS:&+(=/fY-M?,6gCCQZN@MfF_N_K#nh+4b=a$F=G'
%kHcJT&FJYD6gc`rS0ruc+MKpC'fk?f<oSeo#&:`%AQ:Pg6G\$DiV>7-6S<kEPk+W!jIk\SUGSa'_#_/Cs'n3)-#)Jn:Ib$a?<DmA
%N8M4SbYeD_!^L=%X;=CZ&MI@.??b1h#V=:A,BS2u&@PdpM=\lA-d'VidM[h[fQuNmg$m_G4('d-53hKs[rEM(3\Kn#V=:@uhGK(n
%D\Z&?]tW$#$WhQ_il/QQ>uD`Gcd;2?(I'J9.e9B'HSBMgIf!co[AT&?#k"-l#AL?D[UIo=*7cPXRY+M`=YKTt$C5_ZoJFrj!7nB.
%bWl]f3PKbf<$mrE]1kLf$O?kQg@W*QdrH7TJqrI*ig+"]'[Ral3dZMmG%5RT7m_6m"d[06Wg8!G$']K939Eu"]T#S?1XouiJ`n.k
%J`9pELPp//?'8'D"F$esJf[YgE=hqJ/0m/7KkHW`O\"IqH5DNPVd$LP?F7Y0N/,Z;%G2dK>E\^YIQ1$1!0_=S]m1`2iN@hY`V7eg
%^@+;qMr`^5kXh%t'%R[KjtM0d!mZ#nglTOs6m!L)/=Q0Q))8/u'@nY<($^5pOstCnQ./YkHe'\U2*fZ/TQNpO9CG#125ZlT.)rm/
%akRIN4Vbqqe15:\f:EF76ug>^b78m!5'Hc`j\(R7VP4b6qU-[6!BO;IK&D#K.VT(nQO$[>MD'lp'Q>oQQT+7.0>6=d],<FS3gNi)
%KdO.b'U&/fR_`8hO-&`<^:bG;/rSe-\sX$^\gfs4=/PAV8ju@+&$s9o6Igd+6?Qak5t.?A7qN*)_sU3e):1l+ESn"VH?!/f<1NTD
%>b!X8n,%(X4&fGliFdK**t3$1L"4KArl<CHJ*>PR)!dhL/8WlF-Q+pu+%"gAgSf;>Z<#<QOF;$q[&o<SPZuF;dL-S[]/N(EHJ7I9
%9*nZ,G.XUua;!K[4K+k`nK[,_F+OaX\bldp"a,CmKS/1TBtakMULf`5iaOGQg7F9^<!:<h1W&E#9\4R1\>bnN\$(409LY&nc:#<2
%4Er^#1I/T(Lk`l\^s>Uo+O"YdrhQ2j>.4u`\W=0'E-4,IdFRDel72SK3k"Hp5_^j[GflJAg+nG&#$"Qjo8^Uq/-tFhifLLaT]GM>
%>gOt&.13O;\'iSBmm+Xj\u(u3+4lY"WaMbPO1-Ju_u^/09+@T-72Dr"o\r<>(G"UE@jJHDWsWU$4<=AsmgKn4hOa!dUIAsRfUnR&
%?q(gE8n#?GY\Vn-e%T_36-,rr]n,aV\"Vo@.UP"gXY-5PC,7MOg*$l"$58PNN"KCXDRR).T32m:ig7O[%,g:$eQ$#nmP87'G;q5W
%+_lk^Mt8,g'(YHSL4)KZ0abhSeUhkN]pQa,h$L6Y@:nPi)Nflj@YE0I+0Cg>T=i[-nhNU9"8Nqq,Igts04r(5lnacl!'cmT"%E%V
%]1l7\?'9^VY=<P5b6G.`#i4A#M[fT*5EUJ<enfLF;@NQo7+fU4Jn>ZbTkQ3abN:3Qr-3,g02@6b3PnNE4e<?5d)\4VOlSTc0_]"L
%LiS#9*p%PF=eHb+P++I_\sU&MZC5^pWL\Y8W*In[BQIVdLl\JYbCn_R_)l+o@Pe9Xh[Wek&Tsb6WWM1c>e3Qge+\q/@;!7Vh%1o_
%R6+,@84)[(G6B\R;VpPa$%G@GpN%4g\$3k8#Wb+'JKPf3\i"Y40r%ZJi]oh!:'b4KpDQENadP?&fmWDE4Z;Xm,JjoV&Dam!SomVA
%+_Ru0/`luufm5`<&N)]Eb=;8O#AA>EBbn6"7h";qcBinMkC2]XDm7dA6uVohaORR#EYhTW;3>b$VClbPWPpgE$N6.0Yk_*(I*@gg
%h,TC"\Yf4B$cTn\S%R6`"BC1#Mih)J%h!dm%t9R$6BR3IbF\f'5>h;pc'I07Qbjd]RN4En3OF*V(?I./4i3"8,'0hl/[pY%;Ekkq
%@Y3&ZKi$U>qPJ^8ecU2?Y%e,!5CP3O&9cG@i-c[[NkA[(:86,X1.n>;MrQ?Cc'2^ef@=W#e"9!BEhsK]ZN,m+\OsL:M7g4b0.Z,3
%IVk7<DUqYWmFY9u?X2396DCHo]Ydd&Me^E,42]Y<jE)h08ZB[Bp>t/c^H_H+M=t#1K]$OVR9"QLB^n,0*qT)p2jHHCQZj+G-RT9F
%,Z2Lh0n>@bO@Pl[_I?[4]#5^alK(jYS@tNqBX*(7@%4,$cXnrVn&JbNX-2^VDF[gs!2A/?4>3lq`2d$7nob>]!tUN>n?UdFq6A#h
%JISB=pp&p.ak!,O)UeR7qA/(kd\R/!Hn\%8BlT=n4NoiIcM!Ob(=TSSisfY5%$\@Odg@.3$"\.)Z*HUgXicr2$oB+nL[`h)+kKUD
%B$-W0WmN5+\Bb#3GmUr`5RsVS?/IpX_e'A!WKirERnTA<=IjQL@&8+$(GBN:"%er"g5]lR$Ibj*b'kcb!,bngZhB/`1HIfmJ3/JQ
%s$`rjiYP>9QQIGJA:>\2V+.8beGaiXGH4..BhGLiGO+6TOPl>#"E.f8P2XV;cT')Z5@4UuA)H/);6-Ic$<53e^^6F=)(="R"6$1S
%,#IKDMJX.q3,4I2chWJXi(!L0Df&BGMU2B`$8?b7*l<jeq.`c-2<l4ci-ULB/SM,Qb'r6fOsIH"!Kp%R;uCSM9af)iM?j:1crAa'
%d$A\Y!n^X\'V.sPbm''GZ^8aD]:>5K-i'Lmfg3%C@8_u3+*,n';m+qF3`PXKO?%?h&Cu,VDI4"`aN'R,E?FUM=N_ot=fTdJ7Ag0m
%e,'##h@SsE,?R!/7!\H=,t`YNc?/*W7ZV?1mte+]lLP7KGB,$am<BC[MS=c7*"1iq0c2(9n]_l>"B"fPmqa$KiBA'XU@ucIp2`GX
%Go=Eu*'2<l+$uLM9*<W'6AQM\>eN8t.+;c8K]I:GZ3$.\#8[+.jh3[l76h`QGr8^/)fP5_nrbZ*`K=4<)$@i(YOi)n(1J/C0BV[F
%r;YGsPFg$^j#lt;o^Zn"Q1G!Zls:Rf_XQ\?06GAo,e.%],jPF,4@@&4RpDH_JPnd;&0QUDj`cGAR?8*-OChINdfu5]iO`^rO+h15
%n>&a:,me5_nBb]Nnl(@>ZMej_XjI@@PjE-&]KFch.ZIer0OocuH?P0!O'm`i"`FTh#%_;\i%*ZiS4jP2?1M,6l*3O*k-fO"c2X@W
%qtktg4Igk8]OHA#pg`J6IB:K,W@K96d+pjNm%I7ZK)YE4IeF1b\^h87S!QOgE/Q_'7=ohP&<>3os#kFTR%lrR@D%!-@m]XF$2ptt
%rgePH)[$j8QU07S3%iQ-]/o^%67RI]bkcL=ku1&J":,XA_JSImG?[KZ5,;kOTK'"YZ;motQ2ca=*BA`!"H4^6'^;D`V?-/,6'um4
%^f@0:LnMmX#U\k(EOjmJcGQr>r+$YGeNlfL3#HDbH];ut!Eo?':_4Vh#k;C\F<UK-#t8*9;GZf;KtF1(O\hGOMR5[a3!kEdBPf"m
%fKKM$TE="UnP)]63%HBs)M;iVlQ%@:HV%hcDgoW#kP[m:F,#)2;gp?Oor.F>%!5q5g1+gZSPTA%K8Y%nj#RrBJFd7k6sT4J8Pm^s
%+3VWl6-2_bF>*jpn8Eo]MKfJ4P.Rf_f[uFW4C(:9h%4Mi@V30=nTc^/-cR`J-^F*fo@Obk/L^c_@UdMdfSZ<MHC5&]D3$tIK`KFI
%Qih_nn/8mNBg1$0.Q.1tAGcTR3)YZRi^'m`:5?M'O)[_$=qZUN\T,N6QlgXDEe\1r`]"=[e!3]uhqJP_H`DuNj-C@SqUW^pFtb$*
%p4HM<':MI/jYQ[3poOCms7PV6s1F5M%//"sri>kjI7)1'pVS0tn3OEl:i6R*P5LqF3Y.C1c#+GO<N;ea.[u"bfh(6+cq.",jlE\A
%e1`W_DG8ht"U"A6drCCUoaJcm5!Y2CK<QsgQ8>t1J,#`M)*b`^`#TP>;Bs++`5V-Tk68e"M\2P^.FaI?^5LpW*[XlBWaSg(V1Sh;
%[&UgE<AC/:P2oL(KW;*\5F`u5Pl"n8c+#0E`#F[IFY^iR_3UEAnt!%hq).("XukVnY$jj\LI(1rr,suGmlLmQAeP$^YlRN&bNWP;
%T#ZR93Lp7h?K\`Q\'\ii4qGo0il?_i*$:$_])(f:3V&9R?;D!@fFNWJJ)Y;^VfO<H];Mfc<E0X\Hej@mkb-KQ)#6$Y&6,-=9[6W)
%G`E>T)75?g4GF""U%@/>1IDEEW@$Fsq96kHOgJnh#(K-)c(nC`MP0U*+les1#/uqh7Aq42eM?<gYbK6XhTtp[j6i#6KO?00c$jru
%dp15"dia7>?(lG?U*et@g'schM4Q`e6^/'I`20;Nd0c*B7?MndJqT5CXIA-=[jNu?La]Ib/+l_r2(4p*lholB&0nocqN&VpH!<fh
%iK%-"\:)^laAD^O"3=Y3ED9.3#ps61U)cs5@gC25&UMT%5Y&6Y(+`[CZE+DFCqig5?_I&]nI,Q""_[Ro:tMr2*.g%:QDFp=r)!E<
%%GHB1h7uD!K8<\6bK+n^HGn*Rs#j(q,9D>.)Wr#=\`@\,dWb\1S<6bF*0h.daZ(:T&3!>h_'WL@&^jL\U0[<i!0T)Ve3%%aY-ZBJ
%TMfJ,lSS.EbF=kb9#]s!-Fb%bJgtHMZ#`iS&Sb^_qj1iYk6P'u4N=.a`jck6N18JWeTg(\]-f55M"Z8Pc.3(/9sRN/,p1(NWu/`?
%Sg"n+fcQMU@sgB[kr[k/GElV+F>RVR'-X+u]$F'0$6iMi#8t8p@Ab)$hAXX/33:(nIf##;o1Ah\g1#ts'*+Wo0+k,,\0146Fn*N9
%:<Jl*TNqba_hitAZB0YtQ5IVE=4^4_fIF!XKLJV&pI+98n"]g'3-DJ@Uq*]LTLBM_ONFr4Gis>WTZrD3pm?NUpld"]S"[P"@o0("
%E93@9-ES,HLL9*!ZU3mWDHoP[ihV2Y7O<^Z6!t-a_GEONEOf.,83f1fHk$LH(rcA`h>X;h@7'$G%qf#`nM=hs*J:jA&P5SA0cYI"
%^*NKqCVUDD9hoQ!\6>g=l(-_X&_KYi'h#HD">=L%RPk&^>Gid.(_lXj^*2QiSpIO$g[)gDm9jerW$BLM'oZEnL[K/'K#IklHK:Yi
%<EMAIL>*XrD+=$55@=<%"0b=opf0ZYX5NjLpH.dVMC)^1JPY`]5B,NLC'K<P'UW5_0('`rQT$ETkPB#3@&U0_&:-j.
%>F(RgCA8L`XkAE$-&(<^/;5e]XY'u^D;?9Y<1QZNH/rn%ABYeCC'`Y4&scuceRNJ'0%dbOZp4$s]JTnqd`a^[B#8>3O8Oh&(n%55
%T//*#+QE+!^eTBKInXCHIjo7[Ji2&.)"qI,7<JjR=B4G2]t-'([B1X/]J0Z\cFT$0NdYio/B#_2\Ji:/bl_oQDa9icFQ/mK[o1S+
%?NdL!LWA.@;["-:As<$+M&kg#@aF)2]b;b`!#XbkU!ri`h_:BM`K/PF3HYTEjF%;'_0JNI'F*=Bp%j.OWVKK<>T[%-C@;Y8R#FJS
%7EteZj3NC"Pi=R\nWu#YLU*[)4b"(QmF6'%<T!U5@7cY)W8U!q/AZQZileV:SCr"35u?ZD"Kt2nWD(1<#?+5G!<Sq$)7'u/IE$fG
%P\m-)8roX5aqu;SP)PT)lb=d\6#>Th(*V*#93jmsX6&Y/_QFKs!Cha2R*JG[*:*"jk9Rq7d0OTH4[gk39^GG-IY-jL+]s)#hI%HU
%Z?@n,$W:qH-)='Zeh9<2,)8n1g:"+ng^Sp\m,TH4Tm6@MHfHt1bP=rn_P/c0+smfqDl1?pSkP?#U*A\O!_rY,EL/mKG+9I%(*U]>
%lu+aP-pe._fN.t/i6,S^q@,$Y&9[JnAACApTO6DHp8@8JrgNH_^kfYnj(C,LkeS#ee@DXfmIi%;5@$Ml5KSL+d_J-;-9F`[CCH5e
%ng6jeZK!J7Qjj08Ccd7qK-gI=;o`NLTfL=cc.8dpKb0sQANZA\G4Lm+H8'Z&<Lc@"8%)5ri0d_6X+<W+UF3u!<i2$0h'FmAq/!F?
%6A?_VXC)1$$%75:.B"6)pc'p*9)aF$\"8h]7;X(KD;G!A3VM9Y2*3:3bX69@3iDm(&^:Wkm#R.a[AjHM=4Au.4$A9Rd#O=N-utMe
%KpQMr'S=o3V=A0XPfF&FIX$?;D=`Poc`H)c5Hj"0)*p0b)+5D<2+N`S_7:^iWF;b@Jn40=n=8Tc9Ym&MM/Ho(\``Qt^'gnMT/=`d
%&Zn6L@k1gA`"EVW[A0,Y1oO<?l2!d&G#d^ukC&<8LJZ$M>)jumNm86nQo/6"oue`U]^Jp;4f^pW<.))QC@O@rLYP+^#N,@$L+9h&
%9nl7R^LUHi/i5MR)SK_:m1=S_h(TZ'1#t8[M:7]I?W!i#9WCkV5_"`%STEBC'U5"#2:6J,fQ>$[^e1FCZ\-($Fp0/T6b"63YpHcF
%)4qN03rUop,1ts*aC&ouN2b'HQR;S,d:aA%DO%oQ'Y%=4d3nb"P$l'E<[.SK"B4+W4;t3JYu[bH4Kq)'qAq3\"dGTuM<c:5;G)FR
%pL+A)74&4c"D8p!ro=OC_r0._or[0H&&sA2Md5)R3/8!a0+i[S9rXWN*t.TJ3FD7'1bJ!MdB`/;(r0Ii&.&oC*.E*@I6gTCg1kb6
%0TEo<FFC+\XVf^9V\J9Wn-cm7b6bSj1&?]5-@ZZ$BR=>D2(]Oo1$USG)eLs:''!09bY.0Y1AJfBd2Ir$N9^p-2h8;Q)nMj1oJK)L
%;#oPeN9Y/8PO)aY(=gqNUP5di:3Ao!YMhA22;VOVig/"&FN9I0`/\4?XT_Ko53U%mmlA6[ceHu?N$&ot9EtBeoDN3=ci(Rc]=f9;
%gD#l1/q[.49M?CCbmN$_.C*R$S+sD2KIhj/c&opCaXmDI^Uq2mlGQBO75daqMV\^k<(fpW=OUu2Wm`@:0Z3I'B)Ml&pdVlCPnb:u
%l\=5rJui2"Ed.a=k4T[-#%s`p!(_Vc9F7sMAkIrE$:u#u`/'o%ItmW-(o4O!&TSd)=-JZB,F(G(Y@3":e4r>,@]K,qc!ZOa8Q5,^
%0nPsLM0rP,X\ohPlo+k/?s7Y$*g!L&XBQPO.2biqH@nQJd+bWh[*nQ$O5+A2k[*\]Ug<QWTs,jOTJWjCmm@r?Q'6pi&95-_l'g'g
%h@G!r><de2h&RS96/J3#@p99?O<(_T2)%3tAhY0^-<'dq$"Ma]^Et,5R(Bj5*%q1,&\Pg^2FbEpo/u&+`*!:GN'Q@%Dg`5Tq)<aF
%/pLo"^6S!:"(2@Ar(5_D8Gbi]rj'IRilu3`^nOh))X=cY#G0'96?o>1\Rc2:TVqY"QY84=P8/XLoTB`+CAGYr<6f9h.KVoN8:<r[
%\HeM,cjHIsL#4"lCT;QQaEM.+V"`:RE`K94@"FjgYA_CeAAr7k9QI)iVI^guYr&[`3UlC;^9'[.fBtq;O%cs"fIJ"F)b;/dZU_j>
%$ZP0m0F9T,=XF:Om!4P1\IKDV4T-b;=ThlsQh>26`+.>-qd8Ij<G(m@2Ht!?9D8GhoL;sFYc=cpR#t'a!XN@-T16:l!h)V=U1R&4
%Upn!kCRj5P_q<"La*,473$o![%U;ViTZ'1InU\5J1=A4ZHd]Wt\MeR[m!f.(0!3\]dQJ[!1NG5a#tgWV4n>9Y+g$gfTNM9Q6F:FL
%."3CIGJNn?#lm(E"'aBbh=sm`1YJ51L%WpdK8D_D<@K4NLqrj!Saf-YP$k(oM</:.!Q"`g':5MP;Oh.5T:>L780FZ[9F)0Vl=N*8
%*,?:QAL<!7r+jN%CK6`#`@a=tX&7=BlHT[QiVEbYb5)?[khA!nb-,[)&)%,=/K!W&F1'7R$+J3'"]Wl+dlF%@1aBAu0dh=5oY=4(
%'PB9XaFL.t%8T\P]/8+U;foGWcfi8p4\ptDLGU]IR0.A:Vo/$r5iZ.4JufVXdY,Rh%79T+b@Ut@C4e'uc&Fp=SEfMaQ4apr7&!Qf
%`MdbRNq<Fek*[L=rM.[P?#-9;6J>?eMjj2:!_BWR!,'WI#bLmDnkYm7&[V"$95N'd\]m\68(qV2ba4D($E!t3U]`2ea>c&dNd^lL
%5,+3.%^\'(E$gb4)`FKa[go"0gc;/eXp;J?W@cVJD]GFQ'\<p:IUF,lArU3OhBPKaX:XFGl#n0R&>Yg6K@5n[ecttHU]fS=J&)2S
%oA@Cqi]lp"qNcY2%G`iJ"_8R9!]F9hThN-lp:<ZH#=e<>1lTRg#d0B3W#D>X'8kCK,krfDM]2LPnD#]P_r9VZ8gM$`.<?;JKUX?X
%4#S)4.E@#ao8V5pJLGXI''IfN#W6\NL0pRi6cj)G*cQ+hTbe1,RaFL)-9r/aKJ6-4;O#W+j(o@N;)LLKkg:cT[#kt2D@VT96;a$T
%&GYCB?IL&eJ#uG-f74/iZ*;X9=E_l&j\sp&gDeO7;86OCa4^d,AkME>O`_.<dFa%TEV@'S\\qfITUJB\U2::'gV$L-8ZFWC&g=]B
%7qZcgWuPh);_ABILaRRTJJ.r`eBkOMKYtNQl8ue(+.Cg3H@ddcYA(!3.sQV?f*It;GRPQo,sS\XQM<dTe_VQ#!bdKG+im08gaMD*
%8WSS9'j7_4`f.&\7W7>'9)jf(9;2u**6ULpA2S!lH,1<OoDXS<!Lg:5b"&]@K)\"+>+&2Pelc6c#ePul<,>F/TG2)7LM$,Bn`6"C
%?B2O)H8#>No=:Ai!m`%]/d1EQPb8fW=Rb#;+oD\Y0&4V>B%E'6!g9(K?VVTe@iDW;164k/e31Y/;!,U<>aMiCQ)9GalU?XnP@\pW
%]+"WRo$,oGBr^"IiTrt##@%=k&Y@*-j`NKXbf_)X.8@"ic!6]9d;tIUS+`F7m0?":7"U0cM0eiFEq8b*F[WbI3o)J#oE[VDN&:GJ
%/K@(,-a?:"j9'#9hTgbsRI>GE]5JO'hlF[;hu8kYji$f<8nV]8*k05Jr[CV!oRl+RNG!3A),?3c.DNfFha]$:\sSU8%nLuDGjM40
%#AQfa6:bm@TeO#7=$WXM60>(K\9.G?;55%FO;YP7/]0H'?B/Jt(2Dfj_Y=fX"uRe@r%Y4F*O)b#qGZLnr/9^S-PT`0hTVXN[-L.>
%+K0r2i\dCXI>nh*cDACmAc'L`oqNb0\t$Ok?N1uoh"^JO-QPO_he`_pI6OGkFWNf1;i]Hp6U(f)3tFS)QKN%7Ym*YFM&heFcr.=V
%)W"O>B)K$UIML#1,PEhUZD'C"BZGq4[]BNo!rLZ1OnB"1k>7U`?#-5hThL2b3<W.Md:$RAL6;I`[F:'s?].>2.iRd+-5s2Sis8%I
%80@V,rML2rZb].A0oFRZWFSa8XK;IJ0LSSC.c9YAnAi-TqmoUNqKFZ7?t^Zo[_pe7ZMf<NDJApu)>::Mj_FdJLE+^?=[c7]"Ii5S
%Uj>akk9C8[g0m$?m3d<"!6>NI?=mKk_)I<cZ7[fDI$B,0OPNB.U<!Grn<W$2NRU6[C)bQX[[)^iq36m3id_l\[CY,=o;O>B)XIf'
%kFBA#47Me:%l)r:]LE\P?SO>')&-.G5HMcjoHXe*!/?&`8pup@<4>]Q1'7Rd[J5OT^V@cUHp3IjGXG>(_7hd"!Q<R$Jb-gGX#eCQ
%lXL&1k]pm=cPXKsV^L-bN=gu=HU?LE^;_bWU1a,.I.<Fk0MHneoX]k0IGTJD+.j=oepENT'&Q@NnIjSZnIj/s?Xd+X*uP=%/?%kn
%;oA#eQq%0mf/9q>JQ<YI_.Q_Bc>Il_*k";^.5@NdaTfc2W63K"J`Lb.Cpqq+\,nO/#0V6UFQEJ/_IA4sns`,CGR2RLh2Qi7/%Fs8
%+q>PA0/bWEj'@idg0SrF!["auF/es.9(C\`Xq]20F0s`l5c;@TQ=uT%b!WhK/!Ff)HB4sKQ(mCUDs)&Z?%,.7/2V+rbtsMNa1D\E
%_&3I1i)l;JV#AhB_qUE"KI6je_L(?95/O!(WTP,P$WbV-;&><18Mg!F0]oq+l-ea4g6[hAHN]hrD_dK/;lhma@9^;Aq%OAV!KhAC
%pDE'jMW#nk$QRhW9A7"\+NU<:/qJabqYniIk&Wd2RBeLDXniY9o"r/PeRgH.S(-OYg[p@A380o_:$":X01Kuf@%lmp>q+-CT3q8%
%jiYTcFDJ.B4E'2(*uk=<>(/=ro3I%WgX8oi>K7(emJCC_m]%]Y<ENEX1!)diqDj&Fk,f:H7,XeC_s,^S<8NYfd$fjAf&pHh^YZ(n
%#Z^"Vcfmm7.ij5Q:+5r&XDpT0O(MD)MSmRh0KR,#%VuK/jh^O6;],B\ZhkoJ1d7C8IC8+*88BiI_GJ7#=#LmJd[-N9H.1+5L,]rD
%-s+`\!;tki1MYcLLHdL&4s5'ZQ%2,]?A0daN-nbkjcWgCeX&'#P=&KEL<[JkH*9C6qBo9Y4S>"_Bb_uUqA=2$gV1&hg%<qqFiAA1
%lC3.7@kr2lBKM4fs1X7]9SZ5<G<:`k5m2JiJMdkd1d"+[_?n+?s0Z%*E`V1?WM"&Np7&N&Iu<mjr,c-`0ehFtor7?<TElAAp>tVo
%XbEG]T%IB>(e]pT6:tsN?!S4P6b.KO\Lc^I<Bo=&<QEj.nkmk'S'$=B)+o9Ac5F5SrHdV"Gc>C(/QBeF%ddqC?Yqt4lo#cTc'TXK
%55Q>gRb`:;QI6E/(GYB$;=`DB_Sq9OF6Y.TJ=AV=FX6`^ERXR3?:+)NfdhDsfJc"\,W@2UDt.6pi3[qL,8Z'CWjB>DG;+<%F8K0Y
%9q(kC1LXsAD0ijFXg`[;>&r>FNK8&:TT"(l+$IB^h<\?3\PTt33UbBNU7[f4;K%Y$aS>OVqXG1=HSSiV-#SF1[MDcdk@+I0iph'W
%\oA1hOBoHu!pN_dJ!/TU;toY23p[^ZXY&XYs,ucjg+r&1JbgZV#Khpm\YE/o0W/VO;Md!693?tc;u?GP#liZ:QcQ'r2Ei0jF,9Q^
%n4u1hDacuk[-"J_`o3S;ADt2<-a$Nj\7rR:;ETi+jE+(J?uTqEJW;%U'O988afPF&#kI:J>UlNXFoq@BQcch>/T^et!p4h?9h1ah
%B6XT>Fb]hSN9/tXh0,DG[O(Wh'-c0?l<VC8?#6c3U2K<YQ1e&pTrn<S:rLMmTn_DO$;t0!Ks<\ucK_pR2-0Wq":@rI<8tpjWeJ\f
%Zgna?r*T7>o5)JiMt(rnM"6Ubk[qlf;$E5]GH7+FDk\6K2a?'?a;<_r6eJ(%\pBXL%Z(Dg'T92$6Tm"a)("i?)k.G'H.3tbp&9F%
%_r@TdBl;d<8"BL^[^;1=V.M>RaBQ7;oe<IV*!GZ$oA_.@'++>7-e=>%:Zq4'eXAh?2&r<6s&_FG@OjXbNdi^]DZk8iZ%,[l9V;1X
%*+>?Ui>b+e=tL,RkeC7Nrjc_r:V,<OW(iI=m:hrf\gVH(h)5:&!_>8$QSfJ=#cfm@&(W>M>"/2.#_n99ZGu,X2e[[FYb:eM7[/Id
%#[A/;B2-k(\tlaX@X+5K1&N)pIlB9/P5lG_mD->$h.hg5!o3pDE6qGB.HHD8#/d2%R68SN?M7q7XMb<C#;%W<'JRS(7#2bnKq**/
%=>..m\jJt(W+[iLW;k=tkm!R04tbZFF-lN[F'l)6C9%F'Af$T0/4/0`/W!jd,r;$,-RF@aE+r&&g1Suu_LUQdZU/'e'>k#GM+Wfb
%V<oG!T^)P^e6B;1eNWC8R>W+*71b;+iR(4;#R%'/CA9qgP>Ircr^Db4R)Orq6%CgZ;'biEE0.>P\9uO]'L,75#Tm)+*H``G#-8S+
%nTB[s(FU*9(6h)(%5',&&^74]lIY:/7)@g1G!5q>#\fUreXRY`VX=-6p0$C+aUs%V4J2?=fRacT.:d<h2&-ASak^g/eW+A'<F$nF
%\sA$?ZfBF/rql5\`&?/;RdO-YB<6$f^"kB*DEH34$'`C&`7D=7oMX5P<+tBnJ9':#FT.]oPcd>sC[[QcruE^cis*]i&n*+5\.TG*
%==S,V"s;ogJ8Qp9\dMKOlm7Zf95UJ]9f,qP4UjJt^:Q5Ng*m/;\+<])@t8_*B'+u]C91$A97U40$3=kk8f2@fo3@"McA:_sJRE[p
%K5mh%=]?;>3j"CZCSu9lmsQkJ=,Zc:Hbh)6b^Dj?rf5Z@B^AWsJR(t'0n']qm7;J?"9oF>?:-AY(\c$'i$*jp.rF0YAV>b<qUNc#
%(@[#;f;<Wh3+7J_BF%E&T4(Q0U*Qc*1<7VB%.FdMHVm@=41`,[F0=m$dFO:_TL4l>8.fB`rVU&S.`'&p8Yp/+hcg(W"^MTT@@_0'
%NuJG0'W7+5PBACP6a[Tj0t-=`-YA3e9?.5>`M)Ic9@&p?OdE;R+i:`T).SAWd\']IF0MW,%a<s)8G/2YZbgqNYK^17#R/fV78OC'
%hH8,u&NB5kUgj*icd^?/N>>S<+*Iia86"M^8Nt\R?1D-Mpi'I1"LbN\5e2j'<r)Cj)u114*j+WcH4S`hnJ"WG?R4AVILh#^iU,L3
%h'ueSXOjkl+F$fK*j_`a$h![53bVN#mqgGA_GlbO,XfM4(km6N1'L7$qAMs9!"C-g<>I;*;i>4:!C&k0`gq<Vn,RBA,h"^r8;-G/
%<A<9;lBgiLS2%0RoCt,tb7J]q8qnPW*H$I*[ko4:+4keG.%L57:Vmg#&=jEGrpe^;k6TP[$Ge<$@tZJ;%+_:"!M9fYOrXP..7Kca
%Y%%Ft'i6"ljs!(Oc<<S@EeUGRKFY.]mD,[i]SsnQ_T\qU+:p0O!$=IK#r(k&oHGI#(=:N!7(%[WX0Oq+@Hn^:+i[P11S,K&LJ=V;
%A#/#R+(=Y7+R$@P^2Ws;O9#)qRcjc&/40Hi><<u-OlG+$dN`jL"p.*MRIG"uOJ$TZhlCG*Vl!7DaUB3k-:M+Z:F,?65+/]XpLdQa
%i".IPX31n50$a4\Ps+r/VWdVr+tZ*A&[:MYMcG5YplgTi^;4%k<OtO)Rbre)s/Y[tdt\]`*-2P^Fk]T[#QgY1Ck]1u%\>+'aH;N7
%:9P)oI:4W"p,K5>k/?P8h<-:@rkse(a2iZMs1!tl1.p3K+NVufXqi"@*'l.WKJ*u[`JTKmk-6%BN&6,7/a#i1U%[N7iHeRqQe2K6
%#h4[iX/K[GDpIhs/Fr-'%36tIn3+*3jEM.1cIbJB.EKpMVp?hQf<.6FB9?eks'W2$.$plXK&T\YMLi^Z<J9lS.(p`c2.?A0)Y*kp
%p"P(15]407T5'>rOZJ[-&@F^ZYBFRq,d'd)^=ACor+\C]D.5Gc3cJ`8n&cfq&?H+[&L5`00c,(8NY8.D):#+B`lb]%Mq6'/WA_Hf
%HR+k)[ZUKSLkm>2+6so9a<';DWCQLAPl>aBS%rpWlu$1MQ%];V;/7<5cWt\)Yd+<bq__DZ;no!(dVDZr]Fc>9ro#fek'qV]s&\:$
%h2f8/n%XFjISNOA.Z=LBU<LYuCjkH?g+dh(L#r:@XG=O8l[A1-_Uh.9T1-V8X?KYFba%iO#UaZ5G_/S/fYle>@.XRfQDK#Y-!J5b
%;PsR3/*YtqXISgV.o>o44t_A4PRi$pH8&>R!*XN-'1!:(!,ceOW>#(1Dhts!3=%R=-\slQ'e':%8(S%,g701'C>M+A!qOHhn[Ad%
%:r7^>N@GA;/`m[1Q>QR61b2S-Hm+-6X\4sG@XWIh]HC&LW!5BZm$gGa-O5Z@g[bt)OS@isOWU3n/tibPf^>PcZ[Z;Y1u"TEH<*A#
%'Xum1gobh\or]<Hjc0tM4H?*q9TI7/4OMQQ&e&,8BDO]APrtdlhP&#!.V]^u%7),qogSZ(&NiMkj+KmNW^jQc7e?4[QfeZ1QPD+:
%R[qEj)n0TT)d4KE=R@CEbro>7N^[G>;coA'(AobJ'*/j42kC-&Cb7:Cp9QR$],6['DIObMc>D1PJ2@OZ_G/YBC`7u)k;hD'U*JZ]
%=%+H^Cj5N6p5;2VU3L``ZYG#fGG;d@Ep?G^Nf?Op(7d;a4+"6Q+3JHa^%5/M@WX*./4.<Rij`t7i$?T-8CAan_%P`?4H!oQ+R;dm
%Nes8!I,'9QKol;aRfl+*%T1F1C5pSlU6bWk:E9)dL<C2PY=f-k7+bu?X2N!Jgr,20h-/]((J!%52a&oM1QQ,WHWte%caba_X-UkS
%ic#,=#'['aKL]>o)U%%p6fU<0@cUh"Qi6GGaUB"U9N>HL:a5#%/UG&("5]AgaY/fRV<4j\?$ld9Wf@3oqZ>-Ak$5jah#\RHEEAF^
%r_t7M=4#"WU$+L#pR#=YF[6qoI-g!teJ[h<LE"RC3ZT2mD-P5f]j+d>>)8g1Zh7CfH6J:#n]1mbiMh^ONXX?-N#+Ra\Z:\d3'IuY
%aPcP],(i'h%Uq*j(r2^OW68iTTf/Y177!)HONT[,DAi--*c-gZlu:)iB-;iQMRu"Ofa!tigDgU@h)P]1MH31Bq@5_]"HVtd&j`3r
%%VBGSpJ\pjN/R&'@+1#:d<[GNVcrK7><C.!).0iK4/rYe*-$OqoGha2?&?quM7%T>D!1-KT(;Dn>n(%*OZM'/k-(@!4J35"hiAY,
%K$X!S>U]NQ)5;Qa28;Vn1L?cB9-pJr'JW,Ng`p)kn)m8cm<0Y:(G15"`1[9ThmEM:RqoJ?eTNeDpBe<dr(^?&;$3cjO#=O;B1_gY
%r;,W9-=TD^nboKV8Ua%M!?gs#1JDk`Y@,)=^uB8C?o"5%@X!mQ2#`kd.&:U06Xg(ZiWIup7U<a/4-nJ`if#ubiH^2eSu/muqCV3(
%5NCkrPN=H3aQR7rJe>I?frPc^E,8i![h(O?9=>C0AFAPH8DGGUpK<b3,ab$2RQY00i-392@<@^4SiCalp6qio#fStI9!6_UH3u9C
%kd6&i?"\!hFs4Eaora#GqFKBG^XQ/pV708Ak7f^\)SQ&sA&YOdTAe!V[":]l7AoHUiYjkWC^TXrrI;$eWn?rKKI0$EH/=qh#\L9&
%'kf40+"u7/h+WB=$_2A"='m4l]'[#=9ar<0AhC)8"k@%i-,:Qdh&UQN<h#Xi3Y1Mn5!a#<>_0,)$h]0\&^1EJCae(XKI7FWdRPMc
%"kh?@ELCue]WPI`JDOCYc@'WJE&U`N"OZT"gW1c;fN^h$>f^Y&)soXPM'DtZ`XlL1Mu;02@JW`4\]E5sYF3]<U2!1H.#7)e;t,?G
%O"r72Xa):Z2Xm&J^.b2="\JrU>O]?:$mXX?4IV-S#i(jq&^HXn^3CAPQUu"rdW4XLBQ4h#"q\c-$DM=&VjKktiFp(T+C!I):jdlL
%Q>Gi7D&p4=(<q@q8s&mo1]d^[0NMg1Keq2ANq+`DjP>Om<qr+-321*1e(A_J(lGN>dg_@T6K'/6nq\Z+(;s7T9fNYaH>[Ku:8^Ql
%f(>mD9Al$pe?$ZtZ68:>.L",q`/IZrOA71,(2$ed.Wj,41^@_KE#NkP_p+mS%'Q>0&]X5Ul&n/Tns@ci69Vd0&:jr4nN]V>e`*bi
%2%IH,'S,hbL]*jZCS@@Jf9cW[.Ku>(ct!\O+3/Xr!:'!XZp"/6`kAarBYbY-P%p=i$eR#ej0pKX:U%^DGWN7-AeK]"I\th>7aN)(
%NX6uWYc(U5m8Z2oF_C\@8=PJM;B7BY'A05qcu,f*\f:"i,D\IeC&K!A93nB".8S2WQ^'uVMgT=TZj@q&EmNZeUZut;908e>7u&3Q
%,1#>Qd`,mhWSR.h^kH7e+fm11,8Fh?b<YJ,"nQ@26\?dB'nRP?"<9fIdG7f6R47#JiE>H'J'X>(ElW%/<W]T]6*@JT2L]\+Xnun%
%PhSDen0&OB:*0kHB\3XA'COg4r[\X-$K+<Nj?3KtVBZ7FF/iIp3/(et_(&i3#jT$t2#`N^6iTZI@f<hfZuK.OXrrXuKVF3u[k=%+
%M*g+J&r="::@DEcG!.R([]7M=*ao9%ImcH&;(6?4em6E"1SRs?ear.kIp]mDe*((MM?^"k!-/71`lkL\,,'M=U]0h#i)i01IUHk]
%?1BXKf4n:0e!<SglV&>'rZ62A\,^<uOZ3pmjuG:bTr)p58-Ic,.j3[oRb+"?OUP!jYWtJs(>41QeDMkLSg-p-?SE57e:rYj8S'7O
%J@MHoL0:2O$46m3j9K,<nKWnij%$!3Tm.8Mo,0"%%/e>oOs2#$E^(im;.WV?'V!K9gP&6\"'T35Nq+oo9mC>UjWjt5F:'/W^NBoL
%BPaH<f*ln8)oUAG?S4\C'?$DK)%6,.iZN::l7%qN%#eBs#@*4uVghB"&%Z#?-gD>3,X0&=_hgmVgdP:LXiDek"dbsP)68f*Z@7[5
%NFjBPL3+,1ojRr@G7WQ$]sLlk7bBCnN?c2/g*\r,1'*t,`M[:>h.G!+%(L<8W'5G^/us)m-M;fbk9st4Y.q#A]S=D2$A6Aq=^:Hd
%bh\2XV7JN%.r\U1C)@X4*nN)eJ8/#f,=:M7hf<-T2O&0b%c;0/aHsb4j)-k%r#AeX4-b=%qs40b\;^coK?KqK,VJ0noDOt$7r)oq
%1F?g#__8\ts77QfE!?UqbrNdq%]<5KI_uh^7@7g9,gU,RbqMf.a+71C,+VhSY,B@&h2!Dm-r9bF7oTL/cou*Ao*oGdeQCd!PM&Qm
%4V9`i#:q!uNd(*L/T?NqDt*LNXV+Zhp)X(gjN%p!GB3F'GcWDH=TC'>#!$'snXMm:A/$7(=hOA,/(,j('8O$1Aq:EYLFRUY`?[3j
%2tjfK[u_)81TJV(dMtjen\)2B',3%<;p@Z2G";"!+L/(?`g[lb?nR=/KaUgt#MTR8[<'k^8u!j^Ap1909hPc*9`,`t06+gSGHOr$
%UO762N5SRJ=M&dPP8fRl+MUHW2DsrljrN;MfC%Ii+b@,gP'+0*X@F,]DT$QmbmIZ9*E[_!Vg0f&[-X%$:E/bK<R3@oL5%m\N8k%l
%LK#dRdrTl*XS#loT?h_<h:/9nU*)Il8o8a!dP:3W[+98J+WLj26nSBV@<.MHkrEKC/)[R-5s^l`RD>H&6mf?22F=[]WM>4`Z`q#o
%,\bd-5)$:DZrXQ@+e:^raq.gEO-*eD:h(kV=a63P6W2ljGWHY7a&`AZRrc7)(53fRi8'u9UW!:qPZkH;9%,A01=,c!ZA?ZF&!tI&
%e<>&VO+JF>M^Qda&gm[\PNku"pb7a4?"qN;;I*N0NY!OdD<U@=rg9S"C9a%s`)cNje/#Se(,Q$(JXbZB!f##^];+M@%OYhrGW6nG
%L5-mP6@HM`IWLHBGWr_$".Cs^1QXFWi^Mr]jm*fQ>;]b[pMBl>WgUhT7sfm3jo7&9d$Thn!d#JG,.h=:dsZ8LMi03k_+4p\204:4
%3IQA6c;IFVEDt,ZM9)I`1s3b>[?g$)(*#5:oAP_,A-brDH_'q,P*8X0&o!>og3<fO<,Gf>\[acF(8/4mFX&HBVU(JuP2)LLgNhr5
%Lc5E)NM^u^NF6kSqlmGTAo0hbdiXU.es0ffT$90Neg_"t/WusJXeB"#=[,LiCEFQ_n_LQs$9L@MSfj&llo(IcTaqSt:HcfmG+J*k
%6!q03M%60\q)+%&dZTH$.*\J"P'c9\6UGi'[7AUgYsD8WT)\f6RKlEb=u%/r0u]$.qt<[XU?SGEfqB!U65kkT`d.h`M&$#*k@_8Z
%m"E*WCa`g"WY\'?B`sZlC3T,sMua'*8F.q,pSf00;VgU&@-u)_2ZN$iN1;-_f*-.l`CG>5UmV;C9db4hSJD7$%=;+T"W76]LVWa0
%)8.%0X"#o8Ir#4#=RWg'),'lh]+d^YG=o4jfRp5`e+S\i5i]$Wdl\o3_.p5_J8IhP6-Xp'a)q[&XK9Acs/Xm'!KV?j64&*D%&-Yh
%=lZV77[?r\E96F54[-99#n`7e?'PVmH\<rQ!)n't7X28B0qaebmc`%h(5s)$+U"0biu?[eHAkBe3Y+Uu(Z1Y'6QNJIh"M@*D%-"<
%NPk'Zb?Ft$"XRPA`65D)N1%.A2XXDB?dfUdfAV^_C69V\!;f<H+Y7K:21u:l7@pD&T([G7];fCld]=!!CASo7JKe4FM41ZslZ>dS
%%Sq%I2NF%":q(l42qU8F3$g,po5r5nM+q&6)k>cQ4gb1I^\K"E293#OI!U&&pi)dl\\DH`_"9<`?V!QWb+VDh`dQsbo`m#?B%'J[
%,a`_mVIk16&qrGdNPL(s[>@\6#7:EN1Nt73/bp5LQKX^I>r=VF%891,@,qkuU3Gh[d00FH-q2fAQHI_uhpF$#G]pGc`pHgab@2c0
%W;pE6(TGMaXWX`=?pAAbF:,sNmbl<-Z5b7FN,Q,5-se^=,o]kJl\0(Sa-Y.^IHK7\D3DKaqLE#A4@=5<q[OE4OTnY!*\I7M(:W>U
%IV+F^A$+iJ@a.?GLsfY-lmDX)Ke"Yp\e,J^:;0B&/=X6cJhgIX=$"75KVIk!L[mTr#pLeS-r8oD$"O`e"n-)Bb4+EU9j`sYC@7j%
%=o:Ri)66kTXaM`OHb(t8#<-5,S_!?C[CpM6&BV!f2nAAuZ"N_m$rRHN4J#4sO\Y"k>u&KHY_,)*2el_"P+ah4k;[o?a?`fq!neb^
%a?js,Tm1XB&n_H[jl"1Z,BLE^>*Z[$P4)Th,RW-WFMb`PO!lnd)M6u2NZq@-(Eflj<]%k`3'&"12*fGEK$,GHXAl26_?-bCK!U#&
%:aHU0&uc'XB>hcnB:Gh0H8dE[K"[R$(s@\92tG#8+l<r1`3C\G7H_7h`)K_/.FJBh_Gd950GN$C0/Ugob]LllU%.sXoBE:,Z:lYG
%<fo:/O_DC:>:3A1+fj\]cTY[?*J]5:]`SD`N"I\59DPeg$Hj$?b2>W,eJ0u!$YterP8kWq@l-DU(4Gk%-lT+&CBQDW0N(K0i$4C-
%8#+A0B)!(X?bq)dV\0U!*oRrqHd[aVo1jP)![jAj\#rgKR/8t3ls_ZUQ>@0Z2>9NS/*uE=LMhG[XG1A;U_(r^e"V<n%.!8joI'n7
%9BWPtqSfA@pHpmt^Q9_qrLpDn?1)M5)'\9_oggAi7kh.\A#U5sT[X8r`@AhA>_3Ld[E1N\pC6ft[GI/]qhG&2W_F.:hcT=,YaCjU
%7MVT"%U6j?l-<tq3&/6i0k+^2-XjmE+QC8lN>:>=0C==N8^IMhr[_3?_9U&m^1es(X[[;uBLC++lG`=h]u7TTm!$[BQEs'GN0jm]
%@+RYYkZ6h-Q&J&KA5h/"_,Fh+U_/gQ(V>Eb/&(W8>bRFIU:c'fQ=*,.r1]-P[+4=Qj^<L1`FZaFBe1s]htJP?%EdqQ:l+=eS49Sj
%.R$L9c3&;,0aP38+ts$9iI4biJ-kHfEh/?7;Obh%RTd<qX87i:s7&)n@;@5+Prmu-_qgV3Qg>@hV2!"tI:%7F.$d\?b3-)KJ%TC[
%Z#_ngPa-UnT'nO5$seS0-#d[[a%.]lBh%$(CRnL,)Y:Y#QAS=c\_bteH."s0Ht,Z!@=s;Bmer+MJQa;$_C@N#90-9U8Z=IkpE(Q*
%'l5%&A*ON$'XlS7I[Jj"C=RKYi-r"f&$S"PA-sh!4T=l9=HuQN6mjs+SrgptEcn;C*'SgjL@3PW+'<s"fu[%4m6QIVRmi>,Oi2*/
%>]6uX>:rL#rG@dEN3gA8AMY@i27CC#)X&B+F;cF3&V)n6@X)(OdYpr<Vt"OhW?R6PS`LdSI+Q;YGCWhV'uB!i`Sd0@01J`ro<@"4
%kO"+U*r.-^KXAsR-t5oL.ujK$!b;?9m*?_m7X'I0WdY=l/S-eBK-mBC&'/@CNZsO7q"RrP5a,Wr\]h1In_(&32,rX.4`B1\C@irf
%O+oIAPTqV?J7qS>`Fo4fp&2[88RL<rJa_?r^05dnbMj4r(O4*M5*kp=-Wj4l)E\5,D:o;G+dND/\T@Ik6,"#NAr!#>o>7[N$WJ;8
%g2au9gYm9VH8`(Kf$-j?,._)R>\Vl'Q"WO%)o^TnFIG'BOlC)&_r&>9J<mtFmF%<r/B:&7Kh'[P.mT*>pP'l]LcD?q%4SUa+/l`#
%d[qN?Be%_EGGp^K%sE&1LP7Mu'M3/i;\8euU3Z%0rCs[iPU`A:Tn^@CdL;#%VL%SC<j$9BUO<[3n219?)q#)3)j64Zj]OpgK<;;d
%MQkt$$-s&C1s3=,a!K)GL4mR^_39oiC:d",6'te,BS%N*B`rBq4a"=9]F+MV\W@%hnJOWX+Me2P,P6V%nfE&'/d)i>>8Sc=86q1O
%;QHe8PV2e.%og*SpUWMeF+OD_?lC5]a>?j##!qiOdC9giCqmN0Uo$QhV[S`C[,gkjYY@(Eh&nq_1.77#i<Z]:=f`'T9P&!dC?="P
%`'4AVPBu+^&1":kFo.pi&DkUd_pe3EC3^V^mLZ8u).m?_[PWm>9sp:=caP`<"$8deJ`kVC-&A:?R/#SMl,P1d'66ur(2CEddQ%3e
%P[K3l3:R6%9Lg]'PN&DRMn?^d/cLt53O"$CA,qVB=<Oe^eM;Aceqs>a'X%lp(U89O$'3"pQ<JM[!r$_.*=eWS*L&&0USoRFJG`^C
%mGDY)95&])QF@h$MME\&`40UheA.c="b^)n-^)0l<TO:AI9Q+h]Z2?PDI]fQN*k6E;m,rQ7+/>3\Lf16PsM,>dAWN>j/aI&eLg?I
%O]q?Z=]$:bD^ZY1Eoaf"a'_l)06ZKc+Tf>HYO?X*JT$X<Y&XRN3thlohjir$k"+CBWh88;GhT:k8u5%g.F??#F/!YNF]"6XQ1Qhb
%.r&%K:tI("TDI253IX%RTMqFd5\b=5JB"Kp>sEV]UH?o3J0F+*(D4Mp;[shRD=6&"$0cIl=NT_c;MaB:$2cm3@RU:_&_J(C:T/ca
%JnH-h.+Uih.Sr?21n\_i"=NVXobCQOVbigQUtNAAdLK^UP1R_p>9O2@-)!ZHO1;936Fpl#C'K]`.,?i#SbR.]6%PgYBO3-T.9cHZ
%O,2hO2gYJu7_.3a)f6EfS3<b!&s+U#oiX)Cil*MV9B]&)-,]lA]!V-5K9klA4g4bn]1[fCkV8U.oPbXF7HX5Jb<_9K0+4An,='eC
%LLIK;V*^5-7tkFsrfsmCl>l[8E<;WsK?qZ>3^$8^2l3>IKFK5EMSP>]+d*XaXli?7ATgV$^*X#O-O=*5Bic/ER6)VT*bf7#<6qJD
%&^VlUQ\#Ir<%(%I5b8h&"n`Q5VckfqbO51#Z`.-IOY1uO<9&)aR[>nX7'^eP7j$%A-]rj5(>drW&0HO8I4ApO5:Se#TdbB6W$(P&
%N&_.JPiCkDqZ$aN[97BB8ig+GWut8g?T#\S#kA`5=;99QLLJ:u<`#?.>]2b@KO\7d8)@KL95\q]^>_^D8&U)b\K.;ueW=mhVte<Y
%H@5X#[E]Xh[a&8d0gbFCn9ie!QTZLlI-Qp$SiFG(Of^Mb0#"JVBC^cEju5o$Okq')J4(T@+a1Bm</epcZWBjO)1u5[JIbOBkq$6M
%o!.NL_69S+3^.,A3O<YJpaRa%56FVh.(T(_-"0Nu-\nIk&2g#IZSeQ6?.FS&$$TW>hq%Y^Z3jiZWLr"M(Gr.>bNc6UJEF^%DM%"d
%DsaE[ca2\blSJ;Qq8r!]]s7,\Vn?1sG.5nCbkKS\=15;uULU,0#/0:q(kL`Jh1U065."G<f(<-AqckdOC6_$,jA"0:Q?9"LBnBN%
%SQ-]hgfM;lAZ`\[!DjLVT)pfPY2I2NJns+`Y_]R"kIBQ=4@nTM]u<$$4BSN9WXmTE\0h(6R1NBi<40b97^OsimM,U[9Eno5@@t2M
%0WsM:L^-srDCqZ$l_+j4eSmHJ7Hi/7`N6BT1UNnlUF/6V0OcV&,FBS6ruoS8`nroZKT*(9"UIKc#ht&:#gc2^NI/<;cY8M*UO"*j
%DohEGVoI'Q\FE0s^hKcu8bOu>aJctLA6=/F_:5PkYgUS8Ka?/LpKk&O`=`@YQo,N$6Fo'=H?`AqQlG=&hK*u9F1+"jT?43Hn9fU^
%<(WJ"(UL,jX<9[.rTSn@)bl[)?,?GHL%-u=jd7aTBT'oT2HR[@R.>:t!LbSD(K`a2JImM#Xie\qTg8b8=VL!"kA?b$!-UrL&!Z%C
%M6@Y-EdMglZ9fBRdPeOEU8Rp\p!7X3kf%#]F?`]dU_A!!$&@TXLCuYslW8pXDWT"="A)GJ=[3EFWQ2]&1`@?]7\nGq,">;='%FIm
%Q4Fk?&Sc!uG3!@hU/ilQ/;jO)&I17D(2UE"fRK:\<J!8jj+e/Q/<4V7=6Q^GP)#U8KXVXl(Iq@I#,UK-Ai\2@Xn\]=;[fS^>U$l=
%?):Z$'/42?*'p<K:G?3]gG6W06_e/,DD_(X,:2sOUSIm^bEDQNcf"Z<E=\ncr_s0;ApX^8?Rj58iK]^d:Qh?GDSFmSjb]L>&6SKl
%rInujQ#&;Jb>$bO6!Yd1OK:#7^V^[k,COhQEF/]mocENn<d,_hCkcfVD%U$Or-jVTLFU/sdjrp6ER^(iT+(]j4liO0]Y];ET65O]
%`,8Q56%<=KEgAe!@4FO:e"nXZbX[!3%"cfP?F,A(^$i9r3Vl'0K-=hCq?T!W^j`2Io.d2\PI$]lF?miJ&eBc=Oig612>L&_2oH3F
%Q"9\*Wkoho@^J`B<(<97Zc",H:I,Rt8of9VWnnCYa#r:K'_DE:'e4bH4C)^sCWVl8Ijo.P:Efde<-,oFDC_1VM!M*Y)YN$M.Wb$D
%qM^7)ck]&u88EO&<VU"Ol1AXW'u2"p'\ZZHEf$eaepn:7C*(Q>PWrJhr2Q,q<ArKSpAh[(?ok_[4=i]]Lp1Xn-/E*c2K)H"7WqYc
%qJp(Q]^&ZqE<]FGGi_U+ojjXS+G^j*F(u21Xa]-[BjU./S5V;WRNN&em.OUib&VBjRWkiqbOYA@k!;85e)aTHqi_Pf6@5&W215[@
%/2C$8ZfN3NngSliWKrd]W%OS66Ph6O''8FYll_.[NA/hEh?>;gOr2qI7Xn61i`pqo_#Mrp#68E$W:4>S[RC*]#(5#\c3mT5&ChM+
%!U+[53)L>L84+'1c!3Ti6Y//L1eA/`gbSMWp,iZZiI]/.+qZuc2:o-_+`KCdE.mSK1b7>sjjBX,>SE:gSl'DPBPu"(XNB=[ft'l_
%[tMH$o-^6A9<GN@&ku+m=aj.+Tum;l3e+#U9thW!+RW'jU'N"pNB><8/2]T,XY+6uIQds#lU]^S*P6K0>61`=L?%l*or`c]0F08I
%.g+J/\XU@d@?j!1<DNmK$%!)tG<J4a-73dt"*C]R!Um8*UC^P7"m0ri.-)0NC+A,/E9G\qip*Oeej.%p."!Vqb]ZpZJXn_`7gK-,
%VCCK3.hgo-7FC@TYDfJC^'<>,d^.5BAAi&a_2e8-DEP7/=_=fe1?dIWUpi/B9tL_J:o8>3Yc'JnCYLL/r9`%]!E+VG1/(UQ.dA/'
%-t0U2O.KcSg?Y9h9Y5]c!O7tuqPcB3eqm-[Gc.C<*??_V=_F\0Pp\U1;-aN[S2enf7/]FSs6+"K>o)'/O86N1W?IGg'5qpbEj0N8
%48$r8']/^2q7.pra&4BCEHDTLgW.SuC;Wie;kh;BENpl]8$%0$Q[-:&/jAe@3\A%E[Y-GkK]j+\Re6f]<hQPJZAd!erJc;epqDW+
%2tFITBji@^lk`OWjdD/qiXiiDe*Tr.UA4?Ilc2H2h]2>Bhlc4g%'m)42*1DPf$Y9@le(<1<tS-<<Yh)n"M"_aNI0S!cNb%s=1Bt)
%+SisegnOQ\nQu$95P.ogdPUVDLt5R"KjVJK27`2tL",,ZmIatD9U0?H)+k34375f-D1j9kFqZYq9JJTqrq*o/m1ICi#\kURhigO8
%LM&W.2k8uFeIOODap\lON'220hF(*?^(Y^nn>1Ej!06.$]"dXVN\-ApVQ[J/3k%oWmWjQ<fcF;I.uZt,$o^uS&=]g=DaYN_e7GKr
%4On1=fV[QP"NueS`@p&dP<D0_/f\qZ^tXUX,-G!`9BE0X<0"bkG#rLf7!3Mf]QCIB67T*QRX><UKKu65e^\9'TY_]IF0*8?Ic5LR
%EC9B&"W.ej1%\W.fRZ%g-OM5Dm"ArU!MKkD-Q%niLNXpZMFu-aa$gI)>nY,-.R;>YS+S)AhN;,LfFX<D?h*34GL*&[71"P3cOb4N
%)r(AO<n<<]*^Sgr\Nl!MT7Zr-pk,-)10bMCDl"k^.92Yk0@UCDg5EDWY=RXdQTZC\?-SF]>N63g;[Wi74@lan?J,RH*[ZeD-EcB!
%pD&=56VCu,:N-uMB<7kW7m7+8a=p-=;SB0_8EKPc5"oYbTP*`-U0@=\1i88q,pdX7Y0Pgb8f$NT\&lXBTCg=<6f&Yoh[KNbA=+QK
%@8ZO[AMu"GW+.0>Ha^.4Bb@!&)l#kkP+Pd:j*Xtn)2;K['jT*3j(q16CR?N`6Q^"qBjMr+LF<-UC\J>mmg:`qA_Sl'^mP#Mdj4P+
%Y&S5T_$OnkPj\\ka%"VX<R>1970[R&6Kk0A;4g<PYEs)6Wb^.IUtGk/#nB%ci(VVSOL,O!A#:-i7/scG0YOL?ER!B^7B5@]WoX[d
%^;At+Jb:[>b)AG"AsL2nL?qpV&aY'M8hbuL8Ikr-i35.88f>$%bQ8,hD`ba+7K#]85XJ`[(%4SeQ8hcf2RP"GMa*BXIh&bcE%i%A
%LbTX>Z\uIfDJ.-:g5I4>VNL-M-`(ShFZgHj_95'o"nW+LL)\M(U.hKmB?(ceY_:.K/(HF)_B*G_-jUB7bO^)tj*7_'ahmD/("oZ4
%EN;'dLeLbn/H.tqGZO_b0-ZA6@3nlZF#ucENdZI`-fGN&r0\=OJ^Hs,\SV"EU>q3t9@#\MG(ZUMc]Sss(V9*>@?Qn0'e[.p7drjt
%QJefEFW+`*cK/1'C=[MjDd5H)1JW:nWi4r5i/BH+=@Zgl,3WqJLE8ab$RHL)r"@c+f-k1W>?N1DoVW7BYl&#&,ed.HqU[7WPop"b
%^]!+%5QC6Qr1F#chuDI"s75XYpt,QCmsffcaWLN.n,MhUqg\YBJ,So@rl<pqiICn#cbKLP^]3Ei^\n,Hs4cH2YQ+-ZJ,H@>If@7B
%rVbXQhLjT*ro1]mbocsSA,Pkp5Q(K;r6bQD\VIru94.SZJ$2*7`W,l7?iKCB7JGnFOoP",J),SP0>P0pa8\I*ofm+lGlR;`0n0'S
%-M#16repgI:Hsj=qDVd]l48CAGCP?^l@6P/IpXl))eJD+]u[2WM/<?[[Ab,YgQVKbV578fX^VAOFXR9$'L@nE6I/N(HVZ+,n3D2W
%Hus4bI?[Tfg&#_Cma4+/5utLR_MgP08P[230RIi85]uKapTMd^9!HsSZs5s&6WQ7g(-r4t:nmng72I96WZDbC391-iR_\-'IM(#+
%bui<u5`F.%LH+-b*@0>pfLHp0oV5f82%o`#Q!#Jj3JgV5`S_:CKpP1_:hdYO(m6Z(iJ(h$P:\TjEM:V`%*AdpGVc'TV$g1#o7AU&
%a>Ob^]1tQ^M7i#rTLaVZ9^ukUX0`fSQm0"R<M5,*DYHL\Y)_G.Z9MP"WiD`a%lHlGO2@A`E`%Ro1cL8t0bVSf,>9g/O`8m%Lb-`_
%=c6/05o^*WSbMrL''eRu;c%Jna*FII$!J<4Lc2R!%"rds,t>[_W%NE'?W,'h2ebqK>jGM$_Y0hZ8Yg<J%tW+GBC>9$LeG&[kN+1b
%)C/q9OB9\%>4`*s^q=M@m#dVC+;f;4K]!d'f1RTrJhBRA>9usq&Qb;#nL:KMbX/%A./Ffjr#BrJ390u#;RGL>X07Vq'Eo;h3_nJI
%XsuLkiKcV.ZC:1XA]Ej[6a$SUK[%.85q*K;(3ZEfEK,Pmi-p$6V].,*Qe#A&>3.W5=/p;H.!lZ(rLRZHZ$m2F_im!Xq6?_;c+.qm
%-\Zgn9FdF)@3EZP.cCNSd<!ge_6JgM&NI`f+[IgTFZd]Y\&A@X?\r=F/e*H_J>]^G$.SAuSiNDqN@jm[mYVCtAA$F=BW8"F!$<g<
%Q:`6F/g9&^DI,Z,XXPo6]5AG7)V>'7%Z6)$3W`<b2+-'.=##r$9#1>RUBCpSEh7qB4f<#VZG7\Vlkm_@V"3GW,QWB]EcgB[\S)@C
%Lm,Ecb@pB>_0k[q#5iaTW0WUtRDtt8.'pP'(fIOuCZUI*+e']t<ZuRi_oQt*N[n?;YoQ[A<P\6raZiBS1<77d-;ctnEmtc_YZ;Nn
%GI6UX?jPQ>jLg8X.'B]EE7't7-oPH>!t^51qFEpi!.c4J\s#HXJGLQ<82NC#pulBVc4i6UV^E'"Kp`d-?I@(2(Q[b]%oJXR5OA=t
%-mU.c7"ck,J/Vs[QED\srSAIq(nT<.Pc)Y_16m?%VigZ88lSUd&!?X:T?m*f3Au3pp-)UPkE-K?I?/lN%+>>G;kYXIRMA0.lt6TN
%ZtOUsW/Zu*UZ_adhHD>a'0gZh"[P&f).\TH,IHr>^c"-]2kg8WV,+d"Q;jM=+TGSH4d?`i&lhGgeZe:KrIq&F0(De^p6?b1h/D$d
%fth2!l`&!e@)'9b"Kb>EMkon'A]^2]?GTiC\L-Ic%Tr:S==<@*,H3F[h!]c4XnEpd=^G+XQ%W$di@^IJ:+/2U12^4Ka>H'j):,<X
%&N]9HXHXO$GKDHApSC@[@ba\qi^>f$?tN,#\m"BU!tcd2WGr(;oQ__+NU)LoTk-6H>l[=W+Ta#Q]Di^.$JL6b+HjeD>N>)<4H\be
%Hl<P87j]`R<Ebiu+5b\QR9]L<<sI%K++u_\krbNIPeKH5<=7Qt(M/5c4Nu)H"Aca9r",hmZ.[ZpA%?hhQeesQk6&?/+a8/jF6IPi
%>$ITc>6_o5\Cf;Yb&ko2[$33sC?[3]JO3K"8.5cR9"d/[c=mFH>IXa#;tRZdPQe)qX$Z.Q/E!s1S$."+p&J%F/(W,T_Z8,TaP=7U
%%['%$af5UZIKro,&G:NWms[@%V*;(>-r<+:[3^@co<#DZ"3Khtl[i\2[sr2ck6]6oVL\1c,,MYc+=RM@"JDfsC!iR938Ar/[+d=a
%`6<@$*n?7p5^HJr'V'DQ>O;[Dr:mjRFLp]T:f"%"!1.nj&liBoi[rY!BN;Z4&YgEu)VkPWTiV[;;p[rCrW/Qc./JDj`]nM*J/TTt
%4ro^=71/Z5%s6?<Cl'%%J6=(6ir\)L/1o5tOCWgR3Ys7?I(P\U@*(H"GYS?FnL+9Mrio)9#aRIq39TgAT`=3,\;M]e4Fi9kU_`Lq
%7e)Q:hU`8nFRZ^AAFie`L[2,</37om0^2,E3G.Sg#6,c@UuZ>?@?V)"7iaWoO_9:,FX:s]BnP-f%i/=T(dI/Sh$``F>b.%H29Vcs
%0uP>UXD>b`PMf-[I]br#/G([?=t%h80#I5m8eN%!]`ssW$_mj^&79aM?kUPq#/f:5c4jP>j^^G!U5me,3u#-:2PXW,\j&(+9=`-L
%LVM#XC9C69i%SraH$PuJB1OOU816#`DK*B&+;(JQgIuXc]R2>bUm"?X6!X_sYI(XrlF=,C?"qG=X@ZZSM`k@$I4`$n9#E=<I-FQ5
%XW0Wj"F@oD?#keRK,c1nOLSDaalusu.MZYP%YOE7qCf\I,r=n9#Lt\7&/0j@dts7,qXG"-8gU?8(3DFppp!O+R7)JHR\'_,e1`nX
%\<lpG@Y_6)@/c<E);),jd2\YPP^3nL?Z#(-bdGRiNG\6]Ef!+fc3:!Y:TCZ6?WpY@7>.YK"P_TV3]W_EMZ-Wkf&$5JOjL`\g!nG=
%'"%9N;0/!C.1u8)0i4J9^)>N2X^pWiaQJY=>ocAU*"sJ9j[KQj_E0@FkQ#m"hi="9]>ft7g06PQlW"9s/R=#+JWs`'.4dUPc;N.]
%>65_cY%>4*=2Q/&gM5H1mgZ_l0&\`\i;ARV[S36'.<i2df>;![8&-pE0c[,QW]gD$FsXkL_WD:&qfJ-q\ff5hbDE$$*_1o69A;b<
%#BZoSoX72mKnq5=_bQfXhk\&)p%hcW)))4u&%fB"#XhKlB5^r7<^A)r:QS2SN(k`9U>Q3:^t0E20$.#sY(sig6lQoI+>)jkW_tq^
%Z+5:B_j.]C`::)^8:,9CQd,BE2c!l^HM)o?$VCJ/O$Uh!C<??X=T]ZF>p`0W'@^X>3)'=O+"GirA(DkDei>U3B0l<Lc`Pb+P\a;X
%"dE-cc\7,#iSR(,oUhl$Cj\4ERrnkhOM<Sf]i?0IQKb\DHL:\qS&e>U%>mpl!l_K3NZS[j?_N,r&-Sto4kO]Jn?'P-4Yo)eTn1_t
%)2D=^n=HS:&S5R+;iUhhJA+DWWC`!U%BM0`>tf\B?^IA@$UB@kQk"3G3t.hA1M>57KZ>6U111O0!%*lfGC\obSW(I+bkd)s<emUo
%'QY6M(Z=T<)Bnf\Xg^JE)2V"@$DksN"/liJ/_SrGL4?&]fGcp3Kn8)ti('%d+KMk3?m#aF>WIXJBuXd%9rfK&`1^+Ym>(hN%LI>d
%"?tL)LM''.#Ma&[;t:[A#t8ed'r<9Mp<fDZ^3>.*9FN]9g@rC!BN@e-!(JYd!0$#fJ=ug2/eFjB,_!Q`@4"]Jhd&>C*4<=VEka,Z
%%=TJOTe*Yq:;.NX&6mG+*+?0LZHToNhjPq(&S6AqKA!+@[p6N=IoA*].jL!iX'5Z8`hn:lGg6d3KV0+O(@a#I56+sM2l&K^&Z1S"
%jdSjp1,`[q3oTRIo=MXmCb.&K96'I6IX<A4eL8#POVOlL&qNHT8tq3fKIM.(OR&l5^LK#a,cRd!,_=C?@!%KfXa57!Y73GM7f_BQ
%WDB)I_MSH3C%Hujh!8`*^(3`a-hp@$eqsKQg3\FY@:(ah?9-(0/%gWK2^o^'E#EZ+U2&Q:63:kUF:g2,#_cXG('LLD*!!gI'aHsi
%9gWrnh-$J=?3I0!3<Y(ThZPeHrG\]%lQ:o+5@3(7lQImr0:7Lr5+UR$r=gCqq';<n[+O4N4.]+NI;2k:7E,Sfm0.61l7FY`a=FeT
%G1c'*9-@b>\6E)jcVJ#TbFr:[co46Vg%_QQEnW<E`V_+p^L4DPhi\m5poU\>rDOFMNB`+d^6"rmG+"*E@rJ>kFQ+F]Pp(W=M2WZ5
%puIkkg`ph#T<)++qNooZg8_Suk2bPO<Pu,3l*4p([8c0=-jh&FW@6V(4TZHhW!*>J:cj%eH2Q3lO*pa^:VWcOOP2,17oi*UArC&V
%gmsE[nO6g8V]O_;5S58A.AtcMJY656V,%oRR$!%U3tm$g*tr737u]\,b%.nP;+A1*A/m%H:gV6"<C&lj:;5\Q3-e=IWcem1q"D(n
%:/+^%qHH*\&S]p]bc1"ZneQYSKm#W.&f^113Ysj.a@B0NXq@$aoAEKFOub,!WC$oe.s'f7.Km!oq][,snhH^f)0B9er!PI.!OX!;
%Bq\au$ZSi1ZS+R`e]W>U[)`P,+WLo:iFc1T6k*ND\VRO`F_d+>Z?s\.WJe0Fr%mmeXX@'[,Y*8DfrnZ7)&$58Wr]'aPQleWd51]c
%%@?LM79V;U[Dc)&9$5@;"@^K!`QaZX[cS9)q?IF,RuTdlBfS_&#!VQlb;[CUER%:/b82D36h6/TBaS1gGZ@g@^.-[Kr.sA!k;+NW
%I?!0#+EZA%@=A(N5KWc:GO[ek.%5/6"MqQ7(c:m<`<N(]J^2-.e<+)>]%V83&reFlhR'5q!GJ<5'#2(7?A*mjC:<'D8o'a$8:mMo
%b<p##UnWE_=Bc@k=Vb!;@EmZ9fFFmZa1QD'#na-+*oO#1&`*F@`Lj[Sn:7MUn!9fKo)YD\!g((cAkK4nS^CU%_uLR:RTtU%RrP^u
%8":=Qps-AbEFfOMY7`jH/6IJ;.^/[h7(CLZGdaR<KPN&iJHNT-gmQ`t1l?/AUUu,j^GH%)m.X[4PLBt<ElJ7_8jIiC.LLeG/a!*f
%OIFs9d"Z59S#[7Z!i_k%rK=R;=&C"'ifO3ko<O=Q?(_#&Z3^0R/6r<!,F/fp"=XB'FKi_)nN`upkpsFF&*t+$;KV%FGg0?[DC^61
%([b6J4MjB9B?qW,MI4;g-.1Q&]AEqg)Nh>;9n4n)61b%R2djA_%R'&na<N^^(,R;JBD=f?IfY'uK%OPg#X*g,_Y6fuSI+m9\P$!:
%Gc;Li;CQ_-<.(V<.t2;_]5=HW"U3dn$1kNJ$;@SMTWo6F7F^ns2;ufZ!6%M>5<*>fY&W@>^]k%#44e'5h?0tfJt?CcY]EfV$YA('
%G*@iYL$C#*OS/G3@tXXjoQ\mmV187OPN/C*F@USA_H!,u\L4mdpJT+>791$;6WOKO+U2Z&YC?8JaP*==HtZlI@Yu)upT<I7+i.9[
%V!=GPMBJ`C89=.V>G+$C1e+.HQ;a1_?)j9\c\'7>@h"<[*,0h@.RVWS</-Z1S:,)^=1NJLX#1K\252:A7CH0g)6C\Pnn8WUhTick
%$+!t`"T574d@,D;?.fJ+9Q*S%#N[k,$O$pVL<7g+bKBpR_DQp$'\X*7NL&Pb099WO%4^Zm##FhtU(j4!I?el7`EiGTRegI*"6r@)
%Z-WU+ZBU4dd3/Y,@IVRhH9']E5`-J^lG6#"J=U#B^O1dS"6>%h<kEYcG@`:WEZ#;>#8oh)ZD*rWRLABr\<\dr$O)OC&L.::G@E%A
%n`Nm:X++N/K+EAsR1DT@@nK'WiX)!\ZjkQ-mV--dYG?1gPSQ$a#Z<`O+,QIZ,CntTT$l5S,?GmYQ^U+L#.Y*b]d:<[G<oSIIab'@
%ILf>!7>YH<BY"R'_fMqjGppsHHEc4bE^$qK1`lJ<ZpY0>PcOV5Xt*q'5,2:`JnXd]SVP]Wp=jS)6\8oT0=u$XYdX6;ZsP!`hpm"7
%A-g!XiS;D7bC*<%BEUm7k.^Z62:U3/4jEZuf(ebj@(CEF*Er&S(YGAp*eC*Nd8".[()M?um%-A<b81Sr.LRrsRQU3:ho#)&GrK5o
%71ZY(0%Z2QSh%NS(R6+>1Gd"DRXd<B&h8*1QT>0(5^sPT"1!S<X_.-TZR<Y1H(2KR6;Hl-X33LsF,##VDh;roln>fdqWTcd;F,(,
%"*H.fCFYSG'"2tS[mSj.5oP["h2'-&UafE[WpH);h*=R+I1/%\8W[#PZ-3X?(nNnH]Z$mX<28f#WlYG$_@H&Zm(&EHT5?!F84aDo
%)uYGd2mI,C`hTuEaQ[1;No[58Z=is0'-T&`q?E.0AA5Umi"s\oC]QB-X(NYcqd3GpX&H4]kZF)V0BG7iJ=kUGpmn3ToDPbb,oTaA
%mYm/%nsQn<s6.!@K0l(I@Q"o<j=$t(#TBbC+NT1j<.^h+.][n!jV>V<5a2_Y&]?dkXS+BcU>ZU.(j/6GegbStNC[Ol6)C=.='B@Z
%ToaJo^mQP\*0a\La1&Peg-2g$B5QS79h'\Pg`D'>fCDKXeE`2684WhRDL"BA+LfWI=JKbYXN3Fkf.Ia<=j[O*;7<Zt"2Gr>Iamlj
%#U6B?maVT7-48gQ3mAbP;iDPnRD6c#e2.s_:/s"RY>dR8!2H#OKOaAj;f$m^rLY'I^T6Dk^Z&'K0(3!U=\M:F9@N\k3gj314MqfF
%;q%.ljbN>UkW.P7N"4NNI5\#/l^bfkP!,sF"b5j6k;NVt7M!Q[`!LJf,Q)6\hrE%f*l6bcplLn0fWM#'/`.Tm!Ct4Z!AaSi!s$0;
%qCt]WR9A<NrQCa5d!t[Pho7OP\1bJFDB5;Sr]gl;HXp9%gNK:*_CKBF.^MVhM7-nu0'S&-Q7VqCq90II_e5d7[1=QOl2t^=jHE;k
%%D<#[BODL"5\l>1J<,pWn"40,8<B2g3:\L#V_<*[e2g`D-#Rf>/Ob2)eS1P6mE!t*m^fXqEHYhe9L4!CVkj#hZ)'>ks.%;g;=J/K
%Jlg.h\;Sa/.Ca=c:$k$`[h@9A?cQu_8.iko!GY2[3G>Q.;MQ$`UatZH+N8Dl^"o:aY\(t?rHYobId$.'i'G-n2NlrdO4TUco\_%h
%6U)'MO$TOVKg=c<JY&p!=[ZI:@$=lLGBJbg%DPF_J,g:jC*6$'Hh%/B_Embm/#&jnH77JV:[&9sP]2uDiJd.6ELk#3[s/N>Xo99@
%`hqM)X%K1R`_<LoO*cu_hI`sjUao"Cq_$l1oba<#h_(T:]$JDIG8A,D<P=MQo4utYbkp39b%GQ`R_'#:?.09of>)O%\u8:.Z`pFG
%"bKO=A$\"TV`XsJLd0Nq:$+&nC+l!&,3.6lLRs[I$;nj5mgLX;`FP(@BDgC8NE2GnU`AcmN]/u)B*0:Kel8T!,mPl?#E5r,E3Kt^
%fmc\\#j*h]W'WQY;O1=@0EjFhK0\*^?tYB8judn&HN/7@cOlTg'N_Dn4@*F';F@iZZdAtjjHd$Egg+TG*\_7U"WKu5JtZSIKr03R
%O]sa(`G7T57DKFmqnpFIYY-$Xa//lq/0^lP!_#cK$.dr2gILh51i[]n)8XU]&?M&Jhol\R,,S9T>FE7L31+2t&0kXi6%NE%L"GDc
%Z]D62@K6#JNS;FIR#'3J2L[3d0d#BQ[g6lEC\.tF.,p_::38$#JJ*2;ou-Z9B`fML@g3E9%;LrC8H@3c=?785TLeOKTC^/I.?1us
%Nre`bBg[V:^*Oj'45,'?e*db3;>5.5akD:*%/oemgi&Pnn9k`DI`Fa.DiQqi^.rD+i8(GZc$)D0q7"=fdmPo4D_DbP\MD)DcQDQS
%g#O47!Jk1HZlFCGR_VWOH?Aqj<WSq6QnQ65oke#RTW2N'lM;g@QRbr6*<WM3V"#%RBM=K/(-X2BiYE2RXo`rpl"2M=/qU.EB5C$D
%Q@ZCk.?l_9^lf(VqSYAdcubZ[^nlc]<Op'n&FGfWM,_n/:c>^pDHA\4PV>1bU2&[Us,bXHZ;l$c-qZ"&R*E[$=Wsebb>i`i8Rd@(
%DBYiDDXR#Ggto9ijGi-b@JEka04?_55=idq3"E:GV-pg#K0c\9/@?b2OBfm.q\H2>:n0I[SCfs""pEiYlQ1LVVSO+1qtUYrTT47U
%$=HX)oc[A*GEaE0.L&J0c.=)<`[4mL``mrTV4Hqob1(;lH#1Mao^ZneK3f-FcQ:rr8,=&]$-$`.)-KX]KDEg30&sD2WpX8F&k8hL
%9_)PRM0`#RmnHR<#RBOpXW87d)jp]425kpD&k1foO[4!@Rp2ik!W;h$_F*>)'Q$i/!@c+/6k4aIff3nZY`rf&X+nC_]6M*!!(T7c
%BX&CF1k$PqSu*T:"IUT0"4c`68g:YClf)Sd-j3.(Q(0!FVdB]u'TRK='crmnqMSi!hlZeb]"%+&#AhL`W#SB/QYH-6QeO'>.n\Md
%5pLYg+o'9Q:_R=++cF=2.ks5u>Du,A@u#O.F5YpoiM`>LoFZ6-q^7;We:B/j%T)$%1WP\1q19F>KF`.2UcFSH8L3p:gHllV!jgZ>
%ht_60/ECM;/\]+pod@2AEIm9d`dqLL#*n4>:M;U\BlS;Y3je#TFVWBUjK,"=7q2\7i:Cqbo@\37]&%e<UDmd5o0=Wm-CQf(E#frp
%cC6d"+O+./EN5?)S8lqK:^?@9A(YD%?VufA'#2Z':_bY\`H^:`F!\[bcert;`+_*#f+&!)AhK6t*+9mq^_1=55NMn]2fVn'IoSr4
%l/*:Er6Ha%oIj,u.Q@P/Mkl:[kP[&s1)d)@s5X,gri,\?rMT'nrVBkPX<.BRW:BmJOhRlQoF9"XG,hZr-'HSeGnoHJDM:L_,npQ*
%&-J&P\IQuF`i_[u<i3TM#Y5&:ArOlDO\ltk$BBFB\cm&++Q>B1.t`HJ<N:Hl&I_V+iAMHlFSiHGh]WjX(1O3HHWlUG60<N9)'oI5
%Ji0?kFc'0W$8i;88Vggj/%-^FQ(W?Q\ZYjq$$0CqPh'u)C^gJA<Q*Uh=QT2UFEmgk*pcl/X>^uH33UjQ1e)gi+rH9eUZ1!SGb2gL
%[VkL:cB<UE12QF[c^e!+k>B&F:Tcgo0n.]0._lq<j(7K$a7%d,f4DET7j&Af]OOY@*)ZTV1:h(W>I+VA^kuX_#cL`m+gEXA[8G%0
%^4n9'#!>HH"aCJ0[07(iWAo.Q-2&!io"N\U'<6kl;Z^mQ3t,!JnF"74"QlcYS38ANJ)oh$aS$&g#!,Ns3`_?p0#da8o%Z@4TOuP6
%=\g(N<R`4UWPo)r(6cJ@`$if6?2^`if<Gof4?94E',WDMmup.gE/79CTo:OHUQ'b,,k]6EojQ1KpHFZBW=r,T7J9Z]%X'`t>T26#
%qUm(&CQ'Cl"^o)W%At8'!NT2T;[&Z,5pe=ngN$-EFDDoN:>-]&/(Q(6e[]ATRO-D.D\o^/]qjuO#`_N>/0-mP@$,,%:'M_1XAoWG
%6P4>NZlCU_YA>P8UpoH'BJo_sEQW]Z&_B=3^OmlSN59?*q#'c3&TO2Ql?DK_1KJUIS%CR/L_W=0=ug(`[E9NGBpK0f1tAnVA]_8K
%7iI"E$<7L^FEql!8&RK#m;3+$`a!Vme#Z4CGc73CkL"Fd,LF.&M3"#%Z]1;5oKt%CrY]VF!MGaAd\qZ;`!ELJ]rA+s%6R(`\[OdK
%Q-<?58]_2[GBJD,"Os@X5IG&F)#POBjd/1Al?HrSI8Z7@nM*\NLfNKU0/LaS+5J?DYM!)d>\nZpfuPg&Xs%?N8ej*ZS,WuO/E-aW
%1.L0iR8AFq=?ANnO*gXF^dpK$VAGob@g7Q6Pu%Hu^55Q*ft6h)`</$k*RMrt\d^'>ojTKf$E3a-h%3\Vf0&e=LD@I3G2a-`+mi*F
%4j5f>TS4['Z_\8h,%4uIm<[YmB#@tLa_PPZ'1+q^#4n!C%i%`,;-VBl+:I1&+1A-"4V7"_E_/8GbmHJ<CjN6@@k.E1h/F>L>e?sr
%!qAoT#Z06XqePAgemEcb`.T1`L0mpEn#9L=P\V8uH5)k9,s`RK&5]RG9RY'5+.$EE.7duBq(L!aU_+GSBmX=e\V6Y#hHL<*Ja7kW
%=@/cN.MYJYI1JgZZ`c*8,k-**mT?"<^'e"KV$WC:X#jABpATF/Kc#e6-1<>&<;676>5k(2:/LbqYS8-%Htp2R20K;&qqf62Y9E9T
%iI<b]Y14ked>IU]'51dW*WQ!dJ"-^rS5uVA*hSAuOP[#uNF/PB3fOJM$[)-ZFe]-"9NRmP]`CMZ3+#IJ3O;2**+RAR%&MV_*\ijh
%f2gN5Bb5V-)uq1s/*aM/XH4^$D4Vc;XGTW(76/fE`'KRj=JE1jJ7!!;nI(dhc.3n+0=d2b$@g1/"'+U"j'pIZ*XbIuIcb_)D?\!b
%]cB)9bMA_`QhjqRK5_%Ddr;FiJ\-SNKrj*@L?FX[\EqnFcOe01S0@+FaTBJ,"hF&$=V'%p1*M90,^n3-8"XVO%$1U-"h/*g^1+6+
%-q+=3oB,ig@7QiX<<\rAmbC)><6tQJ7iD)6`c\"br.NNr:&4^k.F@[L7Lr>#;smOs$M8T$0,4OAiY'5R);SllcF*XT-@2fdB&dbZ
%U$6uG"Gk*8MgNm.aO@#qQqh+#E/n)fH=gb/o23*M.lLMRgj5HBAT6Wdp,6h+1o)KMNccS`<KFGJ"*=B3=OmabRSc+YkT&Q"oa9]U
%<FfcOTF+?qMnH"`[HlQb+1b!J),qPf>cZ->]Yai8`%)XnF0(EpKW\Aq:o'"&C^OX222ohimoQUp:Nkq9;f)R*6p-]88WH@Q'Ii;*
%93l@>0\GB'm6KVT//gN%5qXFBVjZNjaI:"OF6RY=2)gQChcPfO.qmg!Fr.O&Wl,UB4DT/?gU[g?::q4@C&m-4agBuVcQ5fU#94Pf
%Bh)[H/n!1?KF'q56/l>G..9A+"He9G_j[Sa7k#TP%snGTI;7q?G"O`,].A=X>X\N']U!$b"18E`d\q_F1EpU;=2'#heD0$NPBG6E
%&&Mq+&l6YQ$aD*%MQR`R2NGH!oZ&4TO,5F^6ajQm]a<WtQ6AJX2&7cD,C-W!07l!#1j4:)i?muC)^PF"ecu7!:[ckf3jO'HPi:UX
%X=A=?N@UZ8,NDL)]a+sgc<t32'XlQ3lqFEc`KWm5W($F4=9iOCg0HoY'.Wed45VX/0%8ZuFO=\8Dd?-N+G8YYDV,TY@opW[(.j^7
%,4'P9]O#.Pe6qNAV7Nub:SgWe`b46sfdP8O=H(]%rP5sEjSB(;7qo.nQ-'sYGR6u!Q]EmC+d=/>5noH3@%]nc(tGY9QcBp(PK91B
%7K+!.g9\iGLd/ZL3T=@\eg)tRYs,TBNi]UI@.T/rWUr(IH(CI7UhUMD_k.PoNF:qCr>T,?^U%6Ls7<TW!5eVocnYAL+6PS+-rc0/
%a/<*F*.Q&^,Z+f^%Fo)qVc:o?O>(UU1m\#hVYuCH0pX+$'c;/^"Uggl#n`s@LR,`hacN31d`g7TL?1"[8[;/ZSg3bp"RUi"UhH99
%1&O%)Nj+b_;H1O"%bMX,l$;M#g`5&O[=JFpDWmr?""U48)dg4dJeKD6O]n3+<`l$HH`h/Up]u&d$FgX;37SuU.sBhhVG%KCPh2Qu
%Y][?u'N8]\@9^]:M@"P2&pe7%YZpHkl0DTUXK5&K/ZJ@ji&;e!MC_[eC'-7]\A<rQlGXeCmI9!ThI"VW.!4;o(4=>U:2%#oJ<KmO
%c\=q(hXRag=o7@A](*T7HLYebf>kD_%"MuXnSIU">`SWeGQKoC;K3u,K_`9<B7a"QQnrnN+<%t3:G@Bb(o6=_B&/\<j(u*'4Q\41
%GWDD6g3WN^WR'Nt\sG5uq*iCoY+`]`G>JkIB7+9!>/"@CCjqK?3;f;-L/U']cs$Z0J)ri3;-HMa+pB7):LV?V+o%RZ6V)%f'g^(\
%U>NGFRi"@r4\"$@"f$rCN[:N_a<uGMc@"f/UJCJEZ,)`tr@a$]eO!gWEb)-%Ngn,Zp/.rWm_LZ_)=9d5Y'1gkP%I0249TXlX`cB]
%Elm`77`gUC4b^8FeI:`#1MK5g'q4p&$W:84GlMg>E8F%0>:MHS+`fdl<ooYti`:R"^cUU8DC8>CJ7Er4*etKD3nND$`ODu6g8B3;
%Z5&G"h3g'B70iClhaLX))Ep!+ZQ8?I61P:B8OII"lX0?)rD:Lp%:2+mS#V,a*UVg':5rE\l$fHNX/jTe3*ZC@_71u"5kUmFMlN\q
%4B\ibm.!h=Zb`S]b0G1oFSn!KiE:UmF-^/PCgBI?KZC\1e\'kSrO8/>IuSf]c55J`WR]I;OMEXW=W1];=_V@K+O[^YouJn6dJsLG
%9+M_*Ukd3MOIGQ]R68"E[A!K^f7qh'qa%[Q6"VNAi6@U24J>;:\(>QCOh5[X`%;RRGtK(Z#$i%?&HD;`^HG,lSH$MF?OH&@npK*]
%9L1S&q,Q85Dh9H9r[s6=>ajs.T%l-WWGiBSLYm?AD?pO@2_a%VWE(m1ZXFMGcj9iOON3`_-tmT\8$d4$D#R?a3_OcO.'/Z>r*rn=
%0\GHSK4)C;^Z<<2)C*<+%a3G[qR#d&&\HFoYq>^nD+E<GI[?!L/Ru<1:KRI@pWg-2;G!+hg->T8Y8>LQ]ugHE)Hh./1O)nIdI\f\
%"qmpSUE=FC9A'lg:b(8Y/Ph6Ii"1B2Q<6mQZXI%\D4mk_@9Y4e+B3GBlrUtp]'l'OA!?<uj*0alMJ=<&GF.YAmJF[Pl!pHHGTQ9#
%4NdFno@r%tRp8ZP?:oE<`-aJ$_Pr+:40p2/e?kN/#lp@Ih^L_hmC)'c8^n#@Q`s$W0;Rd;P02?5AUMo0%kWqn[6hfe.P23SQ6.Wf
%EN2:+&CnNJ^j_7DluS-f.:H>7NLlS%;OPK=:paTGRRL#N0=,2MGWT^:*`To5YdS17YW0Z!J-SWu3l(^%J?VSWjG*rfQAueMh1p0j
%ZVSO:+n/fA:ut2%@8K_e,%MooDjktH^B]f?G^DBU81`nW+"GQ*0"-D\5j`G+U62\<e!EJXk,4P#-<\+(EP^Uq#.N.-[i_Z3.-)g5
%5uhXkn]l3F2,9U9`(.)o!?N(?A^"(&W7)GMNt@!`Hk\AsQ72W9S$"ED%cD-q2aoFArCEgQKZA<Y;u2LF*`2E/[<Zr.X4LUU<AgMU
%_ts>bbqhkuVO84kH/gA"l@@u!iX+LE8&"R?\_\c:*6po$@/lr\(o,i,<_`/m3p<u3aOsa/cj/8;!,E\EEt&tFWDs#%'n,,lHfN8+
%Dc'+t%>X(,/I+sFDND#ep=RnnT+-`'Um_[p8KZ/j'Z1ftiERF\A?5-(;@o_ITVgZb,^ii7gtrB0Q'E9(=l?7S&C/k$YM^Rj#c]dV
%`B6Dam]KjQIrFQg9%0g>%f[,D=2WQ_WB5^KH@gMCH2ojpc^4lBk]U!4m0j*AMm6#^erm0@'hS;c2<[IO^1Wa;[lRjDPF#[5DO=eZ
%9u8fl@4Lo.)p9F#abOV4V1a[GCUl2f"9Dit(U_nlqn:u-GlT,*dhb9c#lWBg/"g$:N.n%p\2>0dAa+E\Tp0QG2tVKT'T,pN$7JFW
%8^KpPdaG-1_YMhki]D%GiiRMsrPfMZC_=T%[2C036LpO_3!U95@m=nV+$irI0qGK6b8P(r4>0QF1DH<uC%OC'1o4'6T?[d4\0W*M
%<^Tl;RPO:/IK3aBet8/a@KZHt07?cH5/'XP:Z^1g1GL=BB(<ba5[*'W7p'Ac@UZcdLr-m9;`tUH:5Zh$pChrCY)irrDXJW,"IYJ8
%B1r35Mh32""AdgZPX2:Oj$6Wi-4DiaZ.Jld;TaOqY/r"Q+]-OZ[o+r:[iY3$/)c!-rkS=FUZ"EV"TLZWk;W*R6<,T89,NPpE!TcL
%$+VtE1VUr298R%L*?Wp2*T]>d.N7P'#&,;&l!]7Z9?=1HUeZoF.VK:@aB/u+Qh\oA[K8.I^"t8R5`*J=bfpO]:&\9.*[:jX#nEA2
%H%`;p.H;1tlGjBj(T2W%92fq.lL*.UHPIf)Esm;95h>ldim^;*HjFn&.ID0S@o^l`Nl5e'Z!((uF&8ah=$\%i(V2,"Ihr1Dh3YRF
%)MC=t6YWM4B#4=ghl.#U7k+\9PTVj[_4#7COS/"EE=I;GTFOG:(Dm">F..\TGUE7u<Y%HT!tPsc/c9Cj`,F=qB<]MY"2Rh24Wj>(
%4>N(;GSC%aOQ_pB"+mfK;n#[aZ(tZsDs4Xkp_#Rh_+EJiJsO74]2SjRGLeuXi+t#rV[dmYhD,#]iJFWHnRNZ\?Y#bVHeQ%31Tdbg
%(u-S%]!cImM$Zja#(IOY5UK6N\%1G>-3]iNnFS#n"b$/eXOJ.U*_8sIAAtKJK&?oA$+^BLcO;LsPj9I,Gn[GeRue,;@o<TY$lopk
%ENT5=9qJU5Q]V6jhRb]!EEq+Io@X`P&'+F!-ce@RR.%,`6PDb;P,rFsiXiIKO6KIt)_JA?INTO<8^rOkm8JA6]ed"[*0:_':k252
%'`4t1-S+)bF$J4kN_"'lj0k^E7itWKPbV[o1nNc*K9r*h#*MpZB^9rmmm@B+?W7tshRth[1+NOr`L;B'0Jt$h0>8>T[d$Ag)Ib[0
%E5ICE0/'1>(Jq:f`E@j%^_W2,%qM6E"ALZq?4AAEM=eeJ-_ul`VakSSDbcs-I:R2F#fR`_,k%Ti`E+T_\B&e('S)8Y#MC<b%%nD,
%AhZ!5MF8jJ2T_\QmCO`R(T)!*;p$p&B:=0VM=c#s9+W_AP2T^rV4-gb,W&ae0`gdBb`$M%\hY8G)9<\ZB,Dej7uB&[<[rS%eD!0=
%l(W'LLc&UaJJq+>:(V6*!%ia7EdKYWXbB3@QbWkd:kC0XM)`;_@SKU.`b?>78$ElY/o[Uk-f1qXP3gU)rI]^T8q;][H1(u_kt2uI
%4,.ag=/;ksa(6H7[F=4$!(SH'i(=Pd2752*h:fZ9l$N1pM+2pLD+g[0`4f-\<_$bo-F##X(pXleb-ai?AtSA\KT]JTm)C[3^m/GH
%<T&sOiZMATWRXiIW=s6GW7gI?Y-H&*BIR_.=Wj?*/D+OpdWC%Hc</of_MmSc]0NDd;2Kr6gbRkEKgRC+eJ14-T!1[R(5J8jLQ)me
%S]R9rNgdO]+q'#PbYffe7Lj2R?:`<+XTTB2Uepo.EAQo<X*uN!3O'=fHcE_iKmWrLo>:lrG`6o^Rq-Ir;h,F;cl+?X$lZ78/>TZg
%C8i8)e;gi;jF2qYVtE<2)dG0FpPU]E6q/n#^rAL2@(U3ulNUD>c:!uB9Ic`94Lp0W#5iSao7BXj,[DR5)j7k0*_Ru31-TF[*"-=a
%R"gl4*7R<+b,$HXj$7R\(4[Zk$ZY88(D)YCO$f2_@+M)gKgQ48:8HspXsO&fKm[X0fiBaHbMNNICK!HZiK/[7lO#XYE:VV;b98Hd
%U=EA?n_%=Ji&h,LolDY!P0XCCggZt`FDHQ]85r>*B3',JcOn^f\MXDe!/PDM[W&g?Bc*7XMbuM!]>Rc"/Ja<qG]T;jY%Ff?UOl"B
%`MG.R'^/lB7:=W=i0*/e:>pghVt\HQ]X7mYa<oPc;jp2((gB]8%d#43B!.Rm0c1"V;pVD23Y<r;aRn?24gI(L;ojb.PZ'%YNAUo!
%D!D=t`9YU_oHN$\W%9'C@h2L#-#`%8e4[g'3AP3Q1H/,U,U=`I*-)W^"b!bt)AdJHDOTNPf46Walo)r0`:@1FNmL6>,-[UcI1d=U
%8%)cTYb&@;gm6RnQqFlR/lr=W=UGOJ*fd&SA85t]c.-9k$!Xh]DckH3KsET/!oQ-QqNg4pJYIma!XuaSc0FC9cLC`8S)-)-FN(\T
%ejK@8Cu^"_fdF4J@?Sf2fl.LBIVj=ADJn6B)+%HK6]I(1HN)b.X/J2f_1^iSMoD[Tm[K43/ht2JP`V,eTn4YPj?^105_FXg&LbNP
%j.10=C9$.C27ER4]tc=EDC&,o%m3?l2UF;=!bs2D<'f2eC7Tfl[;Bl?[q8b_ZS+IQ1:)E0>l)SZWdLQpnr#:f5@-YW1;jo<$.\$.
%7jb_q:6Sk_UZgg>11(^ed6Q$bd84WN/>1o-F?2CP1*SMQ1I5T9`KS]\TFJKNN/SJY#CA;>A5e3fe+#PH_Z_"chkV*GOEO>=Ll7io
%(b;qo?qNR*.Z[cE0WGfr@:Hm'4R*M-,W,3dq:W`ZM^')&^8"Tdde$*b3t@Z@=HmmTCbR'ukG\'u2-N%[Qi[:4pq<"jS;'GDf_Q&E
%41$DW7_5N,=02Y_P1/lf8ke/<BHJgD=[;*;(Qb/O;$hWrqopC^C3Zo[iRf5I"8#UtM'X5iFBInf4UQUOCiZ=AR9j"$2+&fbE+pMq
%Uh%n%`D6T#(9O$_O1=#!Wf?TqLE1RnJ8W<1CM8LsDsWtoR^6=!<9YqaakHgW.=2o.<QmX$iuqe9qCjm'D4]I'WY4H;rdstCL3k#!
%>#=s'8Hg;Aj_Mb*?P`ct;l\VY=nGk#fa:,s:HjCrU;O.*D[T]L4S>ZaR#]=!L'nj/)C1S9$VDE!@qQefaiHS20^^6;L&;=pW-JC6
%W">_SlWI?mPkFmW/Ns2`<LtItr@7$t>qoHm!92BuLh`QJ.Z5)a@64Ao<h.WZ98qH[6rHX6`02%a7ohnEMnX+dhE$8Q2$#W$?1\ec
%Vh?M8Mh[YAYC*d6WI=nN29CfUV%HlV?lM+0]UBZQq!W2o6V6Y;@8LG!'N&J6a;]CXJOWo&Km?2loO(l04f"EPYr]C&]dn'0`mG6S
%@'^/248k"=qPKm<.I%'KbUXj;"$#WYb$-((.G9Xn.f[OW76eWTXb>cHE_4JC_'tsU=!D%t=oomm%4q@Yf(ln-8%pVMFY9jB@<%gc
%iJ=fqi[r-nPff3tMRc.ZLH1<dB7R/;e"YQl7,D7WfVSAbm]o5KaT6<)Dj1u%[LHUiM+-"L@6=lTQ(uca??MbH4]5jf]Lf!/6K9o5
%;@:FT/.)@)8.61RG%OP=Hd,0k#"/8r@'fj'7OZWV!F5D&mGUM`>\n`C$r&TJ/RaaP!H0mg3E[)a64f?6\Q."iN"Cu*7&Rr(PrnM3
%@)ap)*K,N@dZ."#Z5)<>R"f<l2hBlUUJPtn0:@d@;=sn-W2:O:bCJ'+.N"l]J48?];M#5=3^Wmd0QgB8[48hO0<4gO8Fe4O4^E\,
%4l`C"_Jq$gc_IA5pJ_V6dj+trAl^`21Z>nCSlE.KK*A_.=8&pJY*'=$CAY(cK=dO8g":hrYhWQkBJ;s]'haJX%#oN6d`sEeU?[%*
%8gl?<e$J@W$Qt(?OkC8PDqJ9V=W;_>nj3^H-$pDd?j^Y7U8-+a_iGA6kX;9Y&c:?]<-3bpW&ho2js>fQ"r`&e?BI(X"NiGgWSQrD
%%=X*@FJH2X4(kogE.]W`8RomrYRW4:e][RR!q,I0,*1ZF/Bls'R"[l;5m.nn:D^F,[A?uKC@ZJS76(i7ANUX]>8;,;3&ac5JFQJ*
%8-:9!:!=M#%gSq^C@!:LXC\Ar5#_/oBjB`c7.3Hs'b,#=Aorqb5,)sb`cSGt=6h[5)#tqW@=^_@QbZgoJ?GqVJM0%uWEIASg.I2V
%q8Ml?Gf/^Rpg6Moo7/5qF<]=k0-gW/QG"-O'!uRJA8fl*#VhoN"efk0o3`JNF!:_'#c^QOIL[f2Z$MUu#lWuH<!"D*\9TPe'10i4
%c7*Y:_&J3+*<7DJiS4cB#Lc9N_+F\XN.QM/Cp&:(mfe*DqA)ZIE+O:8<ah!fKS;a.L6IG,Lcfh\E=79GH^D*8iZ&n./s<jR9g6u%
%<;kk9Laq65H;7TGF]2J/m]Gc"0>s3n1/MLuRjV8GLGCqU8l-ZJ.;j`;Ql/IT*BI1h[(1]<Y30<[`!Pk\knH$OYr@-Z6mLiX9Lf^"
%Ent_2"qgAcbD^Cr9Z[Y0Nb+n#XZ0Es#nf15->1.>fmP/9Gu8aO`&qo;6!I#Z8S:.EWa7q8rK0Kl1`b,J'#7sJ2[E;h_Q-#$CloPn
%Vc4mtR8ngSWD110n^<l=LJ8?>Z#fe*&p5Wu^[a4kE'/4=(hen_Du1r6QAqtlUFX4oiQ7\@\X=c"NY^CWK96c\`>jWK&gf:f&o`*.
%ZJfo6&da[Tb(N/_0YiO#"m02*VT=c0qXF8KB7m'a_a#C[8AHG;?bHRg]ujcfabY+0M-M=hK4;t)-t&RTA<aqDE4%,qBM/u"ILu^j
%Z%sSd5;@BJi<YV2r3(&6!c=7QI0%Zo*GV^DY$.p!NK^2Aj;N'@kiA%SJd>Y<QpaSkaqr*jbX=mr+%jVm=G\JJEKHOpVDT$J7O)CN
%8>qjHAOT0Pg7g)-miip,4BRMnn<5p%"d#:1$0H8X3#2V,j8apB?sBK@8i']TpFF-!I4h'(FRKu8#%sobeF"B>aF]i!`8@O-?AWbl
%r]"m'd!6%pC!gCTR6o,m2EK284L3qf<!3Q2Zj\^Se8Yho*/cu%.`^0=Bohc$nQ`"?YW)PL''_[G._p^M,\@YQHbC'+6-(7K7+i\E
%>ms]e%bU#l5dWM+n2`if8pqIE8Q+nTV&q8R_[E]HF,ELuM+q;q+8N?;rdJnKd0VrsF#Un8;Qg*<du4a"3NWEP9FMl[W"+?RqN>0(
%G0;$MZjPk=+S1Cd#LXeicoPe"_ZbZ:Fg+1r\AY+9i1aMMWEU'XjRs>Z_X<)-%VnOlW2+@qfc9BfL2REt2\[^L+7"M7^]r;<aljY.
%dS#u-QD@V_2S_.HO:erS=A6aF(s2%C1M,85=Z&#<>`=lRp7BBJIaEAlqNFRm(8DAqcHeFs_1>i='m3%s@Q:nV*3!e+H]=GVLEUEK
%Q%ocZLdj-QV`9:2i8I''X%H+F"2k@i#.e6XMER9;F`O%EYX*6*E$P5lq>$-2]c&10C%MO$?=/E=_/h,WW>UN$nLu0H"=,e?%U=rb
%T6O2i--o8dF+"krE=B?^8*u&-Hi>-#\lq&$C'?"1#+WK/ojaj4R+&DN]cVNe!:P?-8ril'Amg?l[>a0Uq#i[o9rh[,mf9BtHO[ZA
%m9FnRBD6_-[)?39:*A/Pl]+oFal%=c\V9MWRBX>c,$ZNA<;2q.gmbea$#<!sc41C*'P#0+M^fT4N#X&Gnmk58pg/JSY*`SAFu&Pk
%f^j5"*8kT:0Tlr7^sKH#7=8FOQ\cH=g$q>1!.t<fk"OR-K`hN!Mr#:)pNkAA8c/il;)XN'U0UPu[16+BcS.dlZ$0\7S;b?*$LMlq
%&N_^2Dto8)8dC>85t'jEqe;/u;V*-N+[247_ZWeF#^EVUk5[TG^9C3`)ZbOOC"3knj,aCK.&[o#PpM"qmB<<k%2sc^W0%OcLD@lo
%'ohtB41L$g7TV\CK&EGnOc;Bl8rnb6L:^&c]n5OqMZ'l(!fCU>@"N#;.Mli_:>(b^mpKi6Fpa1>U@E3*=aPem<iV<08,t=a_kjZA
%+":udLTEAVNRpXnp8En$M:klpc%4UQicUf]GX/4?'jT4,#T,&bB_^/eg;1`tEgc6d@=/pcBI0`(UIFp'W^`$3d[Rs3I&/;,ET7]-
%X;o1l=b=U938Kf&A:E8[aUk2$n1RJ)";%_?O/rJ=A1S+@%L$XjT!^,.%,u=;m^3\Q*kIC9n>pTY)kW?d2FF8M?Ap9Jdp0_llOhqF
%&qhLob&Cp`&mT,T-)!L"&lFQ*XhF!m0;,eL#+^sYQ4/u5"6HSC=ot'[fXi_D2)!D%:)T)1ViV<&H<1a&n;S#R^$#GO]G&D,qVL3.
%$5)&nHX#GmJ\b;,h>I>giA3A9;.i'8;e_P.5M$pAWt+[<bFF*WALC#5)Zd9!$PTnjTYuVC<C'K&@j,MBfqSaO\ao"^q[k>uVih,s
%Genb2-JYH9$4579AU>.QJ5tKPq]qP/jpuF=MaXd*CR\9,.2]@=XIoc9p&\]@MBdZP5m10f/0lqR-ltYS&NJ33J2rM/@2E5s$!)YW
%pstArM;YsKi`q6?4OK]n.kG!<!&]a!H9p?+!2@)P2r_1jcM22I@gub\gh[g9#::kf3q!.o^6-@%(E9%!HDRJ2d?,H@?o?+PnY#Oi
%'Dg695pG'bU#hY7i\&+)Te-e]Xp4:!iD^%E\'0<)2-\gu2E6XE#-SSuV!H\f0Z=K&k@`9e'V1:X=DX!@'>.)Q3_`N!#SNJ;o\MJ/
%rt5jiqFkE,:3=qN*&##bHoLSlJQuI?=hXW,<_%pGi',M7;2@p`l<*\',`$$P'"X!oA;srd"j.0Pcj,:Yj+X;Pne0ac2e.B.Z2uLo
%O%>qGq_(sWM<mlcUdd$l7Sc?WSV9M81Yk1n,l8hGABI5Z?=J5>MtC'64B0/c81\o"<T$>l<lDaDD_uuN5qp,X!EK<T2pSOYKuB:/
%F(5=iX^RYY"<^ss!(n+'."OK4$Tn3b2pG?>>P%K;7-=Pc4\M79DY3c\KNn0,79DA99&D0;1oak!h5j_=<I5]W[JRb;h@Iu!kfb4M
%.U'ZABJRVk@_"$g!VG#2YEM%BVWjI+Jbh(E?/u[85k_:U`/@>rT<*):8(pd7<I4-@=DTZs).;d:\+KGli%]>$kmnT"H^,98BO/=9
%)b!_h5d3"oAXY]7GT7n2(t]`^(P<u0K.@a(\q*K)E-Qa;3-)G8*k=g<1<3G6%&f9le7]!GkHG$'r6u,$;2K7_$MH18lB--#TS);T
%M*XNF5OTn[4pj:J$0<.CX?pR;-:h/KcWHZCJo[L&T(bQ:h1-aqSN93&7`.i[)rDn+8D4AHiJ!-R5d>&ue,(tV_QXmRdM:De7fd<3
%[0iJ#8O*_/_-"9Z4[-G#d>(_A`c/saH/^Bpm_ipde*"2mLOo"O+D]!)iOm3&E.6-\kAb("I?C%m*jNRA5@b"YrNqa#Ojtj"E/,VS
%\^OXQ2cl)O00%p*)^S=G%+9^^$_VY[3H>oDMJ7#?!-LVK;n.;BZjoX>V9OUa,J^,h?6HD(TFCM(Q(V+'^jYJY>61F=DC?r-h$0I,
%E9O[C1%jJ!`2I$W=_p63C%9?-0Nd4_=!PD_%#UdI9M2tuSYh!Lpo'd`BR0\M)+Q:gH7&_e7_R/8D\t9#@4$d4B.kE,D@h1@7n$f#
%_-dE\L0nF?V.K;9DMl-d50'&H5+.-j[\moYBt_Bs^FAD6VXg&lJtPpr`:mf-aOZdp7LD=g%7YOB;lH#J<V\J)$aBIKY#R>ZRG^f[
%>1-/+/YLJlL]tS3d2Z/>kIgj+;*f/!=X&hWdd-rbP^`k<,be88UiU;R6g^B0]+fn95)cJoS7R&2/jPdNIbn>u!k!fh[TUKQP<LKW
%3gXO7L+4-2SMLk3#g7D=Y(;7D@*nF1_EJ-mJPc-3pC^Z;@;%:ff9s'^8\GJc=n%.f#"$8BMD*ne??b+8^@]:rY%1WIrQ[t_@&.BN
%:/#3E(M\fnouYcj`W_-N!lf:-&/0eCAgnCT(D:,9I-H?YS\5#ZlbkA<5pk>JBuLrkL0*J*9,OQhnX'd3<eBlQ!URcX,\mBn_'s&;
%06ph(p@'QVBZiYJ_2`%Y/eG\i]SF&HNf*!;[@74E$Jf[/X%%qA5>h'_&a9jebm)$BI5^t)Vbsdo,XFOXdrGF!Fi"S=j[9EEamb$\
%DkXi(SqY+YhR).4_IF-3E/#`4SiZ>j5pDY/>^VM`NWRee@tUeXnIo.@1CNeqcTGo0Tu]*pY:?rD^Bm=NXcS_DdPEhhcqK4(@'3Nq
%@:$Sm1YA^o:c8&b2A1-5RgYNGPBn]3<7f>&/I=4[6>jSsV0L6JCbdS=7KQU@,VTEt$-7cI!+""ES$:/->KqDB&Njh70WN<$XO@!j
%=:NduDCP4pl>9JNNhnFpWHKrTh=.Y7+9Mbm6[;b6SQW4"8=&26C&tM_ck7O:`f_[T@kY3#.R7M(QI=klfcHM;>/l0)2t3V9_<+9=
%;[KTDSq9rc17obf?EcKWk38ALnq<&8$""sC/<Xja]Q36s?NoVg%FWf!m1Faq"24kL8Kuf7lQNZ*W/Y>pjO3dq79hC'E_=?`QlK@J
%-&lq'C'kN3qPLfj$e(^R#iuQ3VD<=VM6*jV__1#f6FLW'&?F4'94.URH=_[RRtT[#chBl`r4sqoGq?4lRh`-T2(HYFlS6k_Q<BL[
%="r>$&P2a!0UV#?Nm)S1"VB:bmoS*6>'eh39N-SXP!3aF,U>?ke*:bi5bh.H[&9m=\V\I*[]DZ%^@R3i%ZVM%H/Te]`k%)P=&%&>
%VF-bMkE?2)_Ug(e2_Y+q*6"X]DJ9;Y2VPs""2+\&E"L@Fal$TF'6:^[#Tkm])aTgKojal:NoIc;%(.CZGoP[R9t]_k>jaP/f:rD)
%-"[(2.`Ji7.[^A?e"#jURe\@6"unb4H<t^`ftt,u]CM]L4+e+NJEM;7^3F\=o#sdkjV1([].^LUJk8ZkA+N3jRV!%jfe+$`1iad[
%en30rQ6<[%k!LbL@?0I+,6l-Bj!Z24!H+sG@2jL-">3LO/je@2pZLrq.7>rI:C5<aSF`IJW6C3(Z4\cBAMGrN3+n2WUeA0\$`g$l
%7uT8,2/2J?Uj$Ye;?GKoHL>(j8aL4if9IZ$*iC7o0+8DqjB3-m/;]9^YbXGk;?mP*U!YVJTOfBQAB1CBKiV`#b<]+04UeEj#N:Q`
%0&2Z0<+f/65,s7/>VV/">X2QG&e]$li3TZ;h^'`_g*5%sl38nhE-+M>6?h+Rp*DPX;1!n1%R"fX!Sb5:hE4;uVRB[He\DV!)J@0<
%`DGI!_HsG2!d]iF>O0'4/\n2qZ0=9COiFS.,6Do_U!t%kN7p%,>;!SrQE(>%BX4cLYDcGBjO>ne?TREH"@TH!fC^[,"q"5)oc\@c
%3Iha[40R]/P*&_Z,tQj`(hXs2i*pWUC#U\gZlD#2Za0njE%uQ9SsG5AD?n<C"Jps@!59h?MI4GM/b<%27V/T_8;i\#)5S?B6S1U\
%N,U'$79HpMb2[UJ$bMHG/9$]5jT5m5?S8M/r=5o.8LmIiDMe-V*Y#PZ_t#)Y65\![Q7I!,.>cFm1*_ltE2fi6a@Jod1`'+s4fT#T
%G]Y'p:fc`V`-j#s.>N?J!J9U0m&'r5lKP685OV;8"YC'?6TYp7c'DR*crf]N>^0,sS^U/6fLWYiQ?i"iM28iN3dI'&W:5@^NQ/B.
%S$tde&WLn#PS6?8TGRC]/p;V-M[H[MmKfF=;WOO%D!<+e;10THRN>idJQ/'q.2GIfA'sEk9FiFWk2me7cq&F3@,ii?;U]`*-'['S
%@<SAQ9<r'4H/,u,S>"g_'td+p$]5(kr?W(Br&=&>;n"K:\1-c0;h?@,hE_+".!r8QM8cM1S-HqOF.ejJA25/4=Sc#IRC=SaWWf'W
%dt%E/-kK$`-feJD;5B$R6T8G;rD0_a/a?$6mT,:o/;u'C<?h&^lL[6bAWQcjlQ<+rGb^&dM*$MufCa%2/8pkkqDb0kWt;FqWk:0n
%EFX&^e%$M^U=SL6-*]VL.nN]a!"i>?fK<0%<=tV9[Xo4G%&&qoN.mtUSq25l5UGO&d/DTF"IIXCZa`\q%b?o9/M(aIhX+]sW"g<.
%1dVIZ\?^t,Kof\C%c#d21UZVQM:gj=-UnF,c3?L%Y_bcR^<'XmH%Q.kjD^]bYL7SRdQl;ND7ZD2-o?t97+,mN!KM-a%2%-m;XfjL
%\]1G\LK/2c;[2T6d^&OpB1uG3[$lXabt:lkL3?OQ=]EQBP*IA*&aJH4h.7i^+e@d^?V\LqaGukNRZ9"."R.$S+>2C52PE.T0bS)E
%&b-3Ma9'4cN$UG/fb4priO"96fd*E2@'_+Ll^`QPSt][<]b=)a&qYoB;:uJcUo;"[+&d%CSDnf6$!G$a:[$W29n#To^&e,p]@RR:
%"W8AKU,\H-aN#uO&HAp[aPA0n1Q83mOr9Etnj[+V$_g<B@9k3^HO4_=K'eUu4PX#,1Adj!C0?ojiuH<cbL6d2W*)dIMgXUsOA??(
%?.%iVFeS+c1JB`$Bj:a./$VIhNZ9UA'WVqIG.Gm+=WI@(ECrWa*4R;*'%SMAfYmh#:'=QF')jiI,Nr^B:dV`_@Z^o:EX+g.CfTcl
%'TaVlS"8a(<dVX1;_Fe&AbJnQju@)u,aO)je0Ql[*&9$r9)9Y7E_5?2&k9aQWF\4aJnjT3'%3<@ZBV'Db/N?u\aJNn8b.oTWI]Br
%1+VO,XV#N/Z]dm*fh:Du<h0tYckXId`j:Y-"`OSR09P1O)?=U31p.%\cMdm65=$ulD">`uY%)OsS:S\K]8\'bTf-6&;GRglU\"/6
%&E2%J,Fos:mi*do]pYFuViVN[.J=6Cl'(4r,__J]W'E@t<'R`f]BH4;7=$T2mBE2"lbnIW6'.;fS8Ok`,b!2[_Fmb1C?`Q'qFMqq
%PJjFWVM`u+2<i)VSeW#dZkfsB9B]^/PZ#R]6QUA<e\So.`(mM-+)8C^B'qO.-.(U.(f6_#^4D24@WHLU*#uI_/.o',gCE"po]4M\
%Of[mF+M;)+:FIdqZoOD]R!1`uVJ/Y43:i;".OBuq'mYEa6;l\e=[p<*q^+9Wh.[)BBQck31c6CBWFGHmO5R!(8)lkscOtmV,V`r7
%`)Vp?9*-4"AX^4X<P[aa]eInVV'RCV%e>;Cr?k7oJ,htaP_%*]IS,d%UH>J:f_qisfQF!IK4u<.CfRjh!_QC43U5-&TVXoTi6@B&
%%gc+-R`:Ul=MROq5?PbXe-HM+(A<nVhJKNW8*9/tg&hVkB3tH$qs/U[Ut!M"!A_9a0IbD,0-Kp5%AZQbfSkIc0MioC4F(>rorn`'
%$k(b0ZdK^FK7.gS"A)?+[H'Yc10VjD6FgHK9nY3l$k,2^,p\L(W#gk^TK_@,)fM8QpJL1nT$@O'c7ii%\0gae9(p90nHO<:d_nD:
%0[BmFWpH9N"SEnkcAnPhEr;.c8uf+eadEO+CkshX#nA5k:^#j"-q0K6O$.Irntn5=YIC<:ZH*g)"C>iJr$l4`QeXh(XsT&CH9JiE
%gV+r<)d!L-0^!42YuR/`rFi7QA$5ke'7J2pl5?(Qk9_I#H0DN;2nDFF$WH:?4dkAl4YLT>$0CMab'9g%)BtS^0P%?ZX9#UAZZD7b
%,$U'4A<CA*]4Us;_X)f(L.j`<MM8<nY:Du0**>s]E(IPbk*$BXD7]FuNO=PR3V`"T(I&hjmT`T7IiLVFE@VeT+$FT)O:%<49Q8$_
%m?/JCE_2(PO8dKL=WAKSKYNIZ+_=QA>6,eb?,90BMA:CUrHgll@A/H+j7%#dp=q=ALMccl4;(t\%VeS-9=@hDAl!SF02+)l54Z4f
%B17DqC,*P9O?F!&[2!e*^t,)O7]mBbpg>Qf8Oc:;7'Rbrb"bF8<cVW8q&BsR#?(>I%%E3p11_AF*(U2WHTT'-pAbCW`&RMe#q$DE
%\;0:lO%e=QP]Q/rMHVS''.YK&--9aCEin;+DX!Q1,jQ_M9]TZY+c^5)!D/F/QC>;VEcQuP"V+h5F.ak1FbU8@`:('>GQrCcLGPWa
%J>j4@#n@G^i'fST+DEL&Ku+"GRq5\YX1Wt@q*$mFKMr*qKP*(Em@$k1'e<sl,(dt."#+>XiC_$4L6Z//A8[d:`M2I>EX'oiA0&d7
%r6`$9m!]:nGKXD,dR/H4>7M%GU<tY54Qm!*Od&0NL7"V4o2:e3R:4ut5OBh=F/m2_OQ"oK:3=LKL_^-\7MAd0#\UnCoMkAg<\b1X
%LIXR0@u+`;;C=YO/1tAZLXn'-Aqu=3C4/`0*j)*bZ6,J#N'33i$S)"q<3YMJ.D]/O!iM%HP&H"b?^nKA_mWk9%.VS%.ktsV#t&+m
%&AB[J,A8Tl*5>_gaA7h-@eE>gK`YCR,Irq-#VN'M60YO_6,N5`gld5bJecYuWWkuA=OkAk21c<&0O4>k8U@O[-^`qq^51!G*C/#i
%_<eX\S<Q`\DHhG,U.iTF3=;.T<Z8&_p.uXVeU'fS1P9Ff+5jkbXa8eF4f-[6\D0Jb#pXIE/X"nBBUFjCRI)sQ1i\^5XHti2?W5]&
%gt_etA"3gEbDqJ%^3&dd\cE'RMm7\#8,?m"Roq%WPkSaOW$I_@q8Gt!m8%bYW!%']U-*bt_oSdg,g.smo[eN'@U+[jo`Bu*!tkoS
%,a0_QUpDgDSihXt*qc?sdKLn4Tbo->&sIjcB\SCq-RNu&0'V&8"kU\_@Vgu;>GG^#.C8[oB'P9!DGX1:L3a.po04nJ;%2ss.]\<"
%7s^mj)A[9.Kg4FU<A\K2j!\[`*3OZ\U9Ud,@+##Z$KGFmZhc!lmd;r7>#d8U1bEVIGo1E6hFPWT1/_u1SKRPl.SteM5-Jlj<:]#I
%@?0X`fnL)>+?,D/]?rDdW#0bX[8%jJ);5WQ;?NK=,t8tKmfOn'Q@*e^)TYQ0HElZ0"!4aj3H(6>]LWch_-nl+a.1hCCSs-9*oV@!
%@IJ'[R1&TcV<A%4]G*c1b8djn0Nq5)4.Wk2Bl\/+Fi'k56=_i)4Z==mgS+kdKmp`a"UTeOW_&mE+r2V`dAl948Ur*%\>D+ek_cOJ
%'Fb.-"gaf0I^>6OJ9_;ehic$"=I[IhC>$I]%Xge?%,Adho!pL1:I431!A]+k;KKn,,W5&!MO0WS,rrARj(4'X.T:(#KW1/*G#:b(
%&_3PW(,.JM6T-4.[gIr(;6%JJ6C+WfXX*A%[+iDcV7kF7[%]*`_?ie=R_"9Kb[297Z88pi%lFtF''c5d2%B!cQjdafV\1Y,r!XmW
%*/M6]H8/FJ,U?#>1#tb3S7JF[ltkLIJjq'MXG%^HjHEpJAMe$(/5\7D;"DDI0Z=<EqO:&3V9f=a"#\r`),%BIDPTV;_$G7V@9:@J
%'g-%3F%LE3`,o(O?<sWmLhQnR3Z4IG0K/Dm_W8U@Eo^dY_/d2Yd;\Cfk%Koc8\7"`DbO-iaAmY"\9c/NDjU]J$O^KEV]Y\sW\K*l
%'%u,^O#3n(:WERk=a1'7B&.LOF0>boE$98>;TJ5tJV;lo*8bKI6NON9H==0C].E^G-c'pZA4!d)0\`X5q1^V6N!cjoJ-aG']/S3i
%N';G_HOR1:2F(;pbJR^(HVtM+R7fldS^UI&DVi8.;s%modsee$nN:Hm[N<TZS(!U8:/_:^VV0f\9h-+h;OIRp;`^%!<Mht/.a->N
%Ch+hI()Z??hF"m&(00`Q?p(@?P2S2p1!t)R)IKfk%LXI*\e.$_c0>ZJ*A-.6\J?U!@WP-We>oPSTO(@$hr+(-jCsOII7j3T14g]U
%F6FPYUHc&]Lt3"cOdf:[l-sAmqd<=Z"j_"lgbc^,3(N!iotlFtZ]RF)Hra!CV``1Z,ZV'+XmB4W1I2Bf&sEf]M(c.VjkA[P:g8'Y
%-hcqo(+mL=Qj]>M&f#NDo0K41Gq5D5Raakp-0Qp.r7JS'(NF[.MnQf:%WAYRHg]0H^ZNLs]\n+((1Zb%9u@AUKSqO07I(t]R"8-E
%Wfg9jdn[d-Q:-cD;5nS^agKC[]MMkL$#i;e^soCg6uo8bS!qgejmX+_'Yb-YAHEE3h%Dab<.Ll2@J(CA1!TGB>0'%aAYJ-[>RHUB
%)-T9LB*e#qO&a:/J.QpV_*Ff\=0Od'dMkV`qD&<\s$M=:9b:,pirXsjh+o<k6=1GD9a`@SD#fuC8hbD=PK(3%`%!am(q<\rbu75/
%fpn"*1!`,?c3Q&b_^`'*-7`b7Pu0%^QNu6pZjN9da<n1[3fK:4lrnbEBI?7R581-S1B:ocC`o:.UFp2q125r+&Zc;kqm?FEid`DV
%d4rD9Q!hP)T[@B'Xqe%ohps+F%SFl>^aZu?#JEfN4-IS3JZ0TPqUOjd/'Mscojim]>;AQ0'^bP4C7`g(b5C.<>6uAhI==V;0-R&p
%WCu4,WEV_F[in3l/m3Ji\QLoI*T22S9G9%JM`P5-gf)Bh#`nEACQgWZR=GuG`6-U]H`c4'3B%UM.I"b'*8`>/k'/+tktP"0En."u
%H:F#FiFLHM.=?@t2+J66ANK6<qRigm[>/fXM0gSD6;Q^"8;b;LSr#0oYq.iNed+#fGlG2-\Sm2/-8;D/[,^ei=.@bJ8m%"L,BDCF
%'kN'H'#^HuZep"U3'HWu"OTid8de<WOV)t;8I(5P>Y'SfC/Z`ZS.D$:/W9Bj1J`R3"r*ARKO16KZnV8UY]YLbc=]YQWZ[_.1eL&e
%.8rM=?;e$3a@d!$L!b(<58.J0j#A_9Jgf7:H<2i8>$Sa4'e1Lh.#4eOp<sBik$rGo$b1FS:R=<VA-FfM>=+c+gYS6F]8=%-C(6hn
%"GU#KJsF&-ODrGA+h7AO,.TWg!?coQ#Gn1#1-t'BJXTuH!"H&`H-@j+Fh!$7#(['"+?BPB*7,'iO8HK&!i]Y]Z\o@-B7ld1;r!<'
%:!U?=Xq+4DZV$EBb0mg@Nm[)$#Oa!ki2srQ;6`pZNhtT/hg1imNQd`bmIcu_<AJ+FUroMqMi$>@i&8"t]p+hE/Qo?g72:hem;Al^
%CU=3P#%47;XMIe)oKk5b;YUeYO@,C$GZm]rL:YTmE%GrMn4Vs'/FY;cgbWF+/@1cT?N,i-kY]S2Wc;`g$,uJW=6O?9`K@-5^a9<J
%/U'0#Sd36%mr]2d+,!OFDNNRMDT9c@LPM405q\,@(D,/aF5'VNW4@q!/-pZ4Yp-p5?:)J/K&*o4;8R3PI^(B_H]3u#0CR6ZmOs"f
%kjjcN=!(`'rU/\Crpa(ATA<iPm,mZ@X,AVa^=%&ba5][&p\,JJ2gja(cS(nqR)`$(#H-gcO"W]3I!1!p8q-?];fdZ`a%u^$O&']_
%mcS5YcQmNTM`"9EhDc[4ZUT?&`"PX%&Y;`>Suqt^8,:]4bkChso).M;q:X<PBtq0iI.GUs8c,[e]Cfh)[m.KM_B(Dh]s[hu#/.D[
%EIIBN:Yao7)8P/-!U&tP>6",JpZDY3*B`Vq54b%eqVUUGs0Z<c32FHIL]@-FGBiZlJ,%:/]Vot.SKPb[QL08B-gJpo2-<f53o*Ju
%=AG.BlaVU<0.<7;:Tg#ln,:n<&-\oS@TE)Q\Ap;M:[Y1I_UY]*MV`aP*?@e,5+m6dMiD&hW:#ipV:*=QKXmt_cc9%1A@3(<.0K?U
%HPMN#CF:<Q0c<s"&FY7J[M/*;9cNIEfo:6d]<5JU<Rl/+F.-OKEI6WsSt!8kmV6^DQ#e,^qPrTlf`a0C+IoD[Rn*'LTj."BS0hWV
%&?p9N))7dgHm0VToKO!.3`Z0;Kg:4K,LOk\>R0RV-Y.]=<f3P&e#]-c=9j)/Lk4F3U/RL87Z-o1%M_;W(s(;U#D)EH&r3Y-P/V#`
%J-V!_lCdO%ap.M0nD3?,#0ks.om3K88mK"FRKGlXH<5s)VEk;@Lo+O_ScKcY_2PM%CI`;\+_l9bmY9Rc11<_V(6h=X4X?Aq@(qAP
%A8k9rKGjbuU7KnpVd:CViC6^KpIgTQGpL?:V->)'N^g`5JDkIQ$DVcE;dqUUB3DirV6@n8"jU-aZVWs8KFt93%1=s3>mK(dM^S]L
%R'e0%HUtrl(jN\%^;MC7cmooY\:SD<j=`CN8Kb38;%KaiC8@LQK%O7L\AauSTM#kuQTi$WDs3H1glU?o-8r'U-5QU&.VS4R,r1l@
%ArUa2?P?LE!_b<oUj7LLN25_SViS1rCrWk8]2M_K.*>K>Hh!-t'cPjB_$QdSQ6qtYK@:,Z"D3mHKEC4T`/-!+G`kQc$-P17-iAMP
%,Ouq79I?>BV6C$,AK^YllffY9^I</r+*\nl&3ZOqm;(QUfuF^:B)pC(ZaO8?KcZ3E&3O0^j6nf1?"VG=OocjnZKVckjCagA='(n=
%"2buZK+?[PSY,=YGZ`Fi<"%d+N$'F;YKVgCM4%L5cq"Hp_&N,un5Qn17*9LkY.B<Y,^lIYQ(Ls?_1)i\31>M(-RQ6d\_pn.o\$0@
%@18sN=k#CdJfpBWJf#pSU;V9XnHE<^Lk$Bo6ROc4@%.Mu]goANG_K^On]Bk&joil$C>XX+RO6EW)?RYSjlEMUZ)PtepDTIQW]-ZQ
%<n)=:.tZ=qgU\2f2lG5@,]5*eeRG]DkL:^,N]A#"QO5V,7H7a/[hc8!Z,+7]N$GkI]uFr'!KGsX(^QY>!\VXR.NU*7r=1.06dqlq
%C\=HQR^`Y[UF.c+UF^+?*No,e4\B8#>;qg6Fe@/jI8OWqd9i*'%>Ua*iWbD)QE_l%-GpuI,MgVFZBe#bPh[slqo8g88Gf'/Xj@,_
%'grM>kg?:g^2l,g/>6b=rE*<)@s<Ck`Vc.0-C?+>/*[)m".RQh)pF(;/@`'d$FR7oC9qjjANe(]lOLDhq]O0T2%j08NPM]>`m<k1
%JYT/&URg89?`MoJ$N92l'J>ZPG3gDZ5HMb[0CU`]hH^q9dt^:K=[SI3EhpbB<3/=hcpq+<r;(-bVJAm,SP*P-I%oCr&lZG$UdD:)
%1IMDBA<V[:&E-$Ud"J2(],p'mKcplgK9;6idY++/gJ^ptJ;iR50I@#jadRfEkZnorhcXtH&a,At<fSZ5WSQ#jp)tViUWS24:5fk,
%k]>NW`B?)Z`7?=qJm4@t%s%Tq9t`Pk1K!^TGq5T1Ko0cXlO)-)XZ:^<@/DD<*SA$H6R&\0O`]=3<.\%'7nh2PXrNR0,KI7W>m+%X
%14a3IU-cYWAS9]p"QV7IV+H)c*8?Le."m(j#&"$3WK8Ah_6glcgqju:@&,h9=9:8JknXeSl=qW]+RV(,NiPO58,QTBcjSA;b,#2s
%^qc_V,h%Lk]q]b&=/rr^;JQ_T<#8"tMOAu'H)6Y]ZN!*;?$7t`a:$f(*B;X3VW02Wb)+e`oVSL&W0VT3XG,%]4#8l2F6AVS9Y#GA
%+L@l87GU'M0b1HE]P>-c;c*XFbA]T^8_cZ/5ib\8Qkj>fjQTbj((?ehXcQC`S]"7mk9M2&KkdrV4&AT"8lIQV6;O"WhJr,ulZnJ"
%77:HTA>.?N9PC6;i.6N2k=*qf8h1Z[k=;tVTfQCt:X9cYInH<RETcQ.ZT^t8:_R0cejUTV1Bt2e!Sffa`?`"`iS'>uM*(AiG(jVN
%RFYTri8J,jEdZ?#muB7!&A_%HPMg$?bq:]"4U[>mB98=qD(Ef@&/-E)%it*I?6hj0r*EWm6_DX%.L:&&*VIW:Lgh`8@&C`(g5Zo%
%[7[H*Tj6$j=nW1-*ug')AIM7rE!Tu:>NQ)4,?u*:0<'Df^LuAs/Tn?qFZ:2ED\c&*.1*9GKG*"ic7OcYHPW8LiA+0QC;=[kfLf>T
%+D/o2e5i(o3,%XF?K76&BF.SFC*Mlm^7HAWC"SCh7^m3rP$2^8.mY&@bkG%j*4L]7)sJOC4uKXe$34(f*#^Cq73OtXH:F(12phnp
%]DVf/!=>Dk&9<r2HAKW^E@#Q[@3(l<8/uo\[19#H30!gQZ7r`mW>$2mJ4r:K+p!aV04R1]0BAs&B@:8c08[Y;"#>PXUs`KmbG9J\
%$aYcRV";eSb[G!`^$(,+,oX?<Gr<k+6Id"UWWB&@1_HL5eQ_<WCP4XKEmi0BI:L??mm:\SZX]C3[;`u,4el5mH!.R&(Y\)-Rclr"
%+4>^,VPBSQLBj$M_1EpJNm8j_(+@YL_jGLP-6`1O;s(LZ!L,!+R0!2LO-:K:@9,)N(-2SUD?a-*A-Usp07Gu>8;`mGbI/V5*_/F/
%n-%=VQ$Sf76n=tFSi&;_%pZ\c9V.^p#?fO@K#EkcmMBkX%*Chh6^oj#AnhL5:D[;*mE^?6fAqL:WW=kE9@G=n.uict2-TBVBBtQe
%17,X&ND,V),qu$m.*3F'G]C+=:H>ClJL)EjF>aq3DYJ6u;<n8%Cq-U20uSMo*]T2B?J%.-Z=5Up9UUU$RaP+s-Fu/""j![)$rs@n
%]jar0!IRB#kQKL@7[6N3NpD:lW0l+89&gQ[E'TYR35&a,5>\9%VN]CA<Y'/o?C#2P@&XB1knYJ7=h-V:1Y`J3L;$=kh":N$9kg!R
%\]b$SGkkEXQNkHc\lgHUod1WFF\LKJe4htP1S-;*7#OH>WqeVsH^fYoT6X#]]fjk/?>7p(B0FP*Vta(:BYMiiCHt=IkKr;,N,\-a
%!^.jNA9q-D-^oG2^-HS4RrpE]joTHXNRr0_[h.mO(SZ*VK-3e55Qo]?_'2FU@E>KGZkkPBUGV6ZSH7^&\j/8%V\9ppKr5\5pD")d
%TgSeH;cc^o480R=)PN[!8m*]-KMi']#Jd@7>r4Nd'djr+n*r(H6NGY..FA&i9FlF=2DO;MhGt3g]-&"kL($[1I9;H-R:(r!Mo9@F
%HC8+0`#B&o#fTN(h*C'&<\@:$TYIS;Kp^]IZ\Z]h`lsrV:/]$2e^^&o;RFZhV9MAO0mb599Nu8/aR]DcB*2q1-Ppo6L>RPq'jH_1
%Og$ha19.)_*C8Q6"GGp&+N7hm4@a@tYLWq9)TT@difn#K7cWY9aQY.l/)BM&F^o`=iP<oU4)lNVPABRV4BMf-idekJ=$aL,"C+Ki
%cC_.=LIg>A*pl[5\%n'"#@iRuSbcnX_Nf++Uht!?<EuG%pDIh5K=0";rfAgoSH&m!#!IeY5UEb1<=rf9V1",N\W8"Vj-%@0o<';>
%,OJt+q<djnoBIa*,i*Sm@D-`4LJ;Z-53Q80*??,FCASR_8Y-B'-TX1LWUlFfh*T8hd?$N#;G$e:;0`l!7(e[cEGkZB8nLD?6\5sV
%#+0-\[W[9+"gEZWTj_5$M$o9X=g_2Ed[j&3l9KqL=2-.P*b*I'J8,8k.=jDeKNM'RC>t8T_$Ec#q2=T:+cQOC)[rQ1m+\as%+qkJ
%q9.'@joC'5QqdjZ1W<p-Eh$[r*4&NV,PAPD7Emujg>j_$/Q!.h4F6Q(%T[0FWUBHEHpEcJ,sI:J!AHHLB_cd\'(qFA$,#!)=?saJ
%C^#rMU@58Si0)h8k_QWGhS"?14@p)%ekoM=pKBiHg?kmi$n*`CapK=.O7nXU`9a3G/F"49G^<\jW2qSL6Y\_8TI7,F=ea^aW![$n
%S8g)GdSd@\iX(Z**6*BRW$O`8E0m808+hOh[5g0%OD";Ypk.@b*hYQcb!4!sKk_WtOh+t)EpYPW9-!iJl@!M1'=tO/J5WjcaYM;3
%,Z[kcRRO0D:1#Vr>oeoh-Cd8o,UE6nMWN)hFH6hp33GTWcH4JR5^)[SP<G2^3Or@@-Fn3WT]B*qU9tZ/fq9-s+]VoB^HcOi8#DD?
%'oECX#q=+k:^2S(f<&F!R9^hZogDNr)e;t%ptBWL<&b8WWM`#?e-FKp>+H,El2WXh1?JM2biEf(ThD9tlRQL"0eo`*kI;"MC`tHQ
%4'rhRWX0Sq;TE4l;[PaBY*"rid5;)>6=nN,rMkVd,h3G-mcbH1Dm049X*@m'@G?a<SWIu\c*A36VbP1O;%&Aq+H2>2&;tG@mM)j>
%H@_kC+p52&Yg`C.eaKKQ1..-iLB[%K"B&R*+9;.9<`WKfUmH'K5AgLkp:n>e8'-.3gu82)qW[D=RjWu(?^s9m3*b?)\_&t6]TfJG
%&*CKg`MU2k)$s1I+q'3nM/:8kb?044a/+i8YKq(rTA3^Y+$Mg!qsrG!e$n$5]]7JMp\mkcnEs6Nq#8n@0s*fPN_GIn9GIlL5U!X4
%C\9$CO:OAr(;:=U0=ILMVf0eS8t9V6GuFu3eeK,6_Mlip$j5ilBrP:X\,KkYZN#=L+SGcZT2_)$&>@L!WrbLkNDJm!F>]ZFIe$K%
%Vr[gl-&b/A'MT423Of0p$P,0jM.:d->oriK)c:<D[BXqGGX%hUI[Y"MKY]&X`'lG>7SO";k?'sB$Y_8s9c19@b#Fl0N`5DePM8lS
%D1WS+NVIj5l'^n$&?<n/a,->Z_RdZ_Q^['JfGdT!*D^Q;9m+Q%3Au/,NZphOnaV\s=>VQt*0+?-Rl"kY_H\%,a/Sg"E:dkEc$E$H
%6m,6"7'T&"+'Br'3(t^18A25cH.41eF1B!FeGHR6qD?f9]rs""4lIZe\,;AA8P6k-SSZn2XA'S?M=V_Tml:">\/]pcC9pNGP*SW/
%*5gSO=tL%:'NJ<LP->bRVN0A`boDb53P9ci-qBo,g8qop_n4O4QKXFK&'t\EQUBDoqjR2kN8"'O/.S.P]R@Oj8"Z[4q";M^n89M3
%P`UP4UXe=6/_[.WgWX!=RD%AL\o@:Ue:Qk7:us`S1$_sh\^%Smi\-KLP?X:O*.Ds`D%%c4k"LS?[QVh!KLClZV$QcbT@PCWjWB!H
%'B&o0L2cq*:VNH94ep?lpN8H@<_S+-BS2N7^$IAQU1M=eXq\AgAqSnQI/+3*(6D9S!bT'd"%S#%nBq[M#$#8+c"\sO@/mq&@bX%>
%"p,KR+gRN#!*6-tJ?)^i$s!IY&pO7*desM"0P1,%16DhVHc&e2i^(E8J!ub4:N2:EcY?,Wka/9!%^rq2-Cid@[O,\t1hfuk('nBp
%!W^kG6$ipK&?J*s54$cBJ3d[f!ls6\)NGkN&gP%A3ZAZ7A^F\SV@mS!!j4?b\J&A>m`N=6],S30;$(\4dc[Tj++pj!W`&ukjYecU
%:b)/O2!:g7(0%@G&%*f9-rH[X_Y!Sk/o(D;ZFTI1]H)q)'/NHXXeKAJ'O:S@/U\C#`XSV[eX:jf<En<Q(jO1$Fh,?k9<<0YWb$HP
%j6W.qqqYHHJsB_^_p+UBMr$Lgg8b5I&sRrcIQ0(YLS4+:Z['H?\_(;7GWA9/11sNo5mRtd(&LtOSE,fUfeF5'T*:3qIGMdIRNTl>
%S@HPR]M)7tJ6`49M$Pj56"5LrFCV"AqdJEiW)>U(fU9#KrWBTXX`k#<15IZeHdpFT+B<G,IFf5J)oa;!j'hIYK/<cVLXp7&Ca`k7
%NAf.u'gnC:h\_NBi5\;kaIsiOE)r$KCc2p'?'=.q<ii.EPlXW_MZm>J=HrG+(Yf0u'97r2d=,lZ7^-mM>KOZ8V<KqNI5otX\uL1R
%IZI"TO=eR3-Gs;.6M$^?]FX$P[igVD]`c6:qA$9bW&o*'RdX6IICj/k)q;3?d@$sCF1+N((9Q6'2Z]CKAPB^K'2?FKa"[Z<>WkX6
%[&D'0^k98m*P1rReXPr.83$'A'F1q6,a*7BU"Z&(iX'[W82&\H=X18+<gQr'lJ`:;XV5C]I^Ym"-XW^'p>)bAMBoRQZ^,=RAJhRo
%_^G:RiBk*?3'CRO+%J7BZ2iF4is/5[J>(6"D1SU[i2LX0#RDcEK9^si2Sa%)MJ^4Wh<6Ao.n%(in[`C<F/6DalF'B/e'-fb8DaO"
%ePTLTgC]b+["f,NFHI[u`B\(*U2&^#a19DG"&Ta$g>t#7g^?T#Bq>`E#0.L\PhM"UY2Q&t>VmU!_eI?-3ttFJ*S5JqAt\UEX&<gA
%31]>+mbs;<Vpd^m*m+QX@s5^IZXi!SS.u&&U/fPW!J&QOEGla!^t]2X0"qjc'cL8J+R0<7i0^EZ6HB""EB,L8h>XnP+upDhk*K4e
%<6n_?7,b+Vm^0.9jTsXbc%Hm;56r_?['[(o#9-rGTT,M1WO0,:C$U($jE/Z1i92*IfLPO&"fWD2ds9J>'71ltUB,RJkfbG1J?V]I
%9ViihfqoW>+6/`g6,u`Gm'*=K'G7"Eo+sT`"7.>Lo6:4Rn4/0:D48rs:,N<V*S"12D:,Lqm@oHHGJXpNZYbKP5(e"QW/i_nN)Ot[
%AN,-SNmj[sj,.:2fPT]@4Q_`smWi!nQ4[X8L<X;&j*_\U1L/5s4d3J8BPh:&R,'<hCp?t2P3^-"h5W?,hu1.2dpe[O),"t^:#e?[
%qhZS=BYjD6/cq3J)\)4ZXc@6[B>DJUVkk^+2T`$i\G_F;m.&'7h,\A/'Xr^4Vt"r\Ag7jMRQ4's[m)[%+ir2432mTM#>u)USklMj
%'E\/A0%6E)3>0@49kWVnSBfdsDJ*ujLXXN,SMap,rhRk0PWt'0=de\jq&q7'a^,5jlZ4S0Lr3lu7tmmfAX7Ts=Y#L8,RJY"B2+c9
%e3%QdY&*V98%l6mkrtI<lJ)GT"sgCU=GpT"PB\&P/)DgKQSOK#>qBRZ8t(I`mAEigW?hGuWqFMVST%P\j+'NhF?s*L?,ICfOIUhb
%%0[TW!s(@D[DoOG@*^gf$WDT^=AE6CWHR4h.G0ue'M-t.i2Sr>gT8,F`)2Wa3WbJL%DC\HSs:u_H-d<:Hn:VgH!Wdk1!ZuG@';_n
%>%Z.7[N4&n0lr7ccu6@E#qruB?>As#%"FP;n]@4XCbqI<.@SFtfX%m!GAnot(jpH/,N[_5QRR?>#GuI7%\.u8a1*Jc:gCIe$KqQb
%h2fUc5deh*/HpX?>F,A4?NkjX0#uo4=J'P2lh$BEde<NdqH9O7LX^7;J7&Pr`SG]LX077Po?bAED;b50VZH7W6iQQg3--on$]g8*
%!n\"mGirb*k@oVU7;ZBr#f*pB4jaeC$/BXWKTA;DIJn%?TFs:?FRV3CNj=OfgY%+UQiTrP;)pYe]K&PlD@H>jl'#k.]IOYUnKldm
%?+L5Ye2aM3mCml2"hsA^`%4U:?JC"bbLjVNeu4UfV<=me*MV+nK_i!cbC341%I:>jY#mk/:X-$jfhFb0'eeec:<V"mC_EN2C8(J8
%bfoka$"#/?0CE_oo>KF7RpMTRdD`fKBe2^_[KAIggW*E17^dA(j4nkMEpfC`]He@N[S0OZT"sM0G(4,MJl8X</;Ic+Vo;uFZ(@8'
%af?O>JeqEtKp0Y'GNDtRb%VVi\gW/+.EmCcp/=_i<-nCYeTteQa9'E%An6SHrG[Kb0<c=OZfZ33R/0Ob`,&4EL>knlJ)];*VOjo(
%)91QMiPYq\iuJidqhJI\DSOJr=i@<t2UM^!!SS*rlqYGj>)';,"&ARL),GMli"_0`%h_G9,^=&Z)L(LQ>)(Ea1HQA/RDsZ)!o)Lg
%'5g;"giK_Ph.YSHKFkK<VXml-5S>2X7cS;i%g%bqNLCpuS-H:,UPFa:6amqG;]AAF-_%Bf)?h/>DDN'4BmmV<3*Vu4ZtsQu\MI2:
%_e.]-aT@pU3%H@67P)f-Q%e4Pi(LiH[QZh0&DT:7%+GnN_p7u7R&Wjl1E..hhU[^Wc,R_Sm*d_KX"1-J/s]p(*amfkNG#nnNQX[p
%PdO_TMkuh8RrEgU(K-idk*ji1oFr3d(kN.G6N\=^OLBWsP>d(k0SbiP&n'>t[8C_jNQ)k_U",$K/l8P`E&&>UJPt4aqA,__/HNaC
%dNQaD@%Keb?>'I"S$CB?UDI3CB8d&_i3]B(EqHSN'r9J?<"6hCG-W"K5""V>)/"80'9"<Ca_!Zqh.E/p#u**t'<L'j0Fl9<F):QS
%lf'>GFW5BPpFV_Girklg`gOa`^tlM*(s>_r2WN$&-%_h,(1kur9K$D#@"S4"beV2"#n.f3P("Y1LWKmX@SL?7bi<0a&&K,gXbY/7
%>a3H3^hu!#_:`Kan>.(`NJ/9Dh2K@-6EP,@D/)Hg[^5T6Ma`!q1dgcF2f5\:NNP_A_c`s`!KM55WVoefXN&rk2K4Q*^+N>;2YIOP
%:1+o>A!'PdU^T0I\$f#]O3&0KZ#+5j6c"Op;[HRQ.pZ<_jT32WAF8UI0Z'^gL1GmiB`t1X4mSc,&BV19n>hS);N;+Ka*ed#,>"c>
%C1DQX`h[B3]G8eScS"k9fVDK:@8kKA@h$=YjSSgiZkI>F)s6P;kmHFK9K7Wl-99efhS<HJC+Kc84ejHClUIusR=aH%Y!+1e/!sg2
%40mCinY#csVmFdm:1S\J83.oGV&X!o1n!lOg_@!GmSs&h.t+1CP;U4eo7<Cq,+>T<LP[7!q53S]Rr3%rZ[!306eaAZ&LksX9-i-;
%Ra$%Dcr^l%p.Zb8X0[4qJ6Vp5KQksYE8%Zj"mQoiK<;l;pB+j0Du+2*m2':Z>r&YYDPR*QDX*0sZ-1-fQRE_J*qHX)!])cCs#'tW
%R)Y>M82hE`8KsT4SB88L"o$40?:`bbmcUMth[H%Q_Q9*$LU4=p0bpag`__FMeY*@o;gm2E(+h&fZ<XMCZ\^ImN9`m@.Ab`q2j99u
%"?o5Y0nq%Bf8V8n$QG:d1MP>tVpXufmSgih3&fc-D@V&_aQeJeSar57pLnshniCY);]Y16/3A!?g+?S>69eYc,H5dU9q9[Q*&c(R
%ZINAnj2AWE/$YHW)D06WO)8a("7?f,qL[">V%Sn1D6/@?\B[!-?i5B7AkgL[?b)Tt5+4OcLuWZ68"L4W/Zt>9o?!6=/$M1RA2MGc
%o.FgSk'T(I&R&cck6,\)GQT%f@c'u:ai:<uQb+J,PPS`=+.2>:Q6S.Tp.-#<$Zt&^=u#X(V@`9a0bqOkf7hTH@mN\mAoFLq&4c3N
%Y8XuIAE.F4'CI4'*R'p?EYUQQ,DE.(K8``4(W>;Y6qB'LY<qubZV+_-NcbLB5".E2_mZ18GbmI5moHPlUpDR7qe4j-f3O1;-$Gau
%%@FdAR4.s/9Seo=:?Lu%b[pi6)[n\kL^uRnFdW.0N[YEdLB1./<[P@.40s?U9/nCH;N6ah%A0)(FPY^%VV0qUOM3Q>/!s'!Mg,:^
%.t5@Up7Q;I!qq%qDJVYZ]@h5GH3&E[:Pi>cZ-5:tHI!EfBb\DeO3XHq5j5n)5"D<:_BUkek)Clh2!3)g[7>-KBQjqCrV_+U/4TRu
%*A.HKSjOOeno]CP'4T0Q]ZsL4&DS7Y`p9)V;_oWY*T/naA*'6+]0fb-k7!uQB<WOs$X)\o\:'pZl(Mu6@k'?>1!^>0Ud\dO]BduI
%GHOI=mfm].'*&sUDtW#(prjLlYY=KjdoQ3Te8*9B!f,3YVrQ1K;6?;6>\q(3aFGM]Dd<Y'@T0C%f]AeO<Mt?oYnlqEO*MsY(K,Y*
%WGhmh&e&lZNF?_$%e^MtdR3FE?>O=L`<L0bhFQ:Q>5<T#TpX?,'l+_5b^)J^N@`m6G5Wa,N'#G5Q5T;t:+rr!&;qmlk&@5lE\>o)
%`iKNrlO>2(b1L9/kZu3bJ752nmWbAIL&<a]=YFoRbOH2o`n)rKVd`>to?&RQ@D?$\Ks[:=bI:.q@ObnlOlLRl$pNBKMC(^M51Hul
%8k:#p&@,qfgHQ?XCl$SS5&Zk.854<_X)Luh2Ya_.]D?J.nod!2Q`hZ_3&Pt2mi&guUF9@2$(RHRq.NstdF2+i6VJtBP/D\,$2(-c
%G]l',+du0R%6,B=)r/a%@Lb'_8R@cBi98fH9'!CB&W/RXcI,,F$/E!6a]P<BOec+Of<u6u\]ka`+d;nf\5^[<E0dU5pr'X.=J>]O
%7/-$n]FRac=ac\#6J&L-`G1u>M6+PJM&2TRok9X\ag>)m;`^Afa#*NLfLF%Bn89qM9/Z_,9blT"5*TTk^.;+3/[I$O<fqYd+7%B=
%Vp5fK2^fK'$<`T*J+L7V=LWEW<7mZYP0?LBDXn5(iWobF__FW[;qsi,A%+067WP+[E<NG0UN&5Ip?abIR\eh(1;V=?GIZ=H^iUZ"
%GQqh'j4$O9:=+?Wkc&ZVZ0'bs]"04W9=0IE,bSJPAZ6)(F9;(#fJhb^%hDM8c1[m)\qQn`KdPC'?9qschsLnUo7cVAQ`8`JGBd[e
%5K7E>SBI3`mV%<5]7!^'WU0'H8;sn2FV4ism>:7E21\]8D=B8;28X_^N?2TA)nKPup3E1?qJVr;Bp(FV:+Q'`;C1mBb1&h&K:s.=
%cF$67hG;'AcDZqD:"00WX`L.1jJ[XGcE4'%6Lj`IgEpmg`tLYF$55'#R?+HWrS38L9n*HABMseT:"./3_tIbYWYoY4#:&IR:Z1=3
%S'-ga4h[g`\!E`1DE8E1lciAo@^hHC1NNXn]sbbt!Rsd0@klSC*k/\kI?:5ceX37%liPj`@a:ONSPtYC3S?=0.H:QFh.=&#p[%c(
%/:(&*6;DPXMAApFZj@dmcI?PS<CcU'?e<%OC:(KZ,Dd?t'#3BIfm\g^Q6bS(<U3Ys^4g[WodusQY&>JSE.&,Y[-Lpea0'>(ST'!5
%E:<Y3HeA#VmTn_O>'nl1OA<="Klk3)/YpjuD]p.5^4g[qqA.!d2g^O`lW]bO;S"ek81.["69=N1(=EN@[ucVUhh(9?He5`g::S#l
%FMd]&UslJbgP:[r_m/N3;"j7hlWZn->i.k/iVF><C"KhU::S#lFMd]&UslJb+e<S#$gE?1>=bYthmO&rlWZn->i.k/mJ7UH$.lYJ
%::S#lGhVgB`+.E.&<C,S69OZ3(?,YP[h)?F^4e^Y$K\f0:Un,mGf',*C^F%aoe")qY&>JTE.&,a[-LqP-X>Oq4Q6,Pi^qb2&5TgM
%Km:K-/]?,@Dqg&tohC<Tg:g.&*TBZB[h)?F^4gguqA/()f=);e\A!ZA_Y#MIGs^$R/]8=*Dj'.]NaO'B^4ggcoe")qY&>JTE.&,a
%[-LqP-X>Oq38s]L_J.W2mDJ42&E4#J[p;4?f=);e\A!ZA_Y#M!e#mU4*P9%>FMd]&UslJb+e<S#$gE?1Rt5q<\,#"NqA.Kcf=-i:
%]=ru@_Y#LV'<Z7#0V!03UMu!k5,"5r>X<99moV*HgO6[47&Jt+<^sm<<^s;l=rE_H2R%8%a''>]:MFAC5LR\^K($*&#4AlSL@;N*
%gHnjiZ[JBZAWH&QqNJH5?-tDdGm7&ZAZ!2390L_GlisD(A`hD,>++cf>HZ(m;Cc8HeIdc)\'rJb5!,I'3Q>j$%Z[nO!Zl&e"?`NG
%&c>0M'@I2hN.@h514gT^AO8GOPmQ]^<X/-_?7UX3][t:CG^e.Z-ab_M\/qI1KN9Fq'3Z8Qq1McqU_?oLoYb&dZg5%/I\`6s8#'j2
%G!jlh1@mJLXirmP\lQhdX]B$mWD"fUib>O=dGq6l3Q>j87l<$S/=k/1*&Wg]R;HT8U1N(r'6m,"K)sY@AMA4QXc-NrpKF!PU_k[F
%f#et,:"@DQk$V7Q*9#GN&]\(C\JAHuJgqjXA^J*'<b@qk)t9(mB("ZXe75W)DVXC>*c#?Lkn#4>j.o`<BMPQ#`U4*]n5EL.K/ZsO
%LBN+$B^g%bE'cE*dZkrUYTnW#nS$(75=1Uf1M^Y$L5F,mR)n';HYiT$^KQ5rmoHPlk2^(K8&@_W,Dno0WIgUc7MOf]Z.*XJ9[*WX
%d`rQ]]`mkk=f,P9\s.)H[fe#bNWSF_]7!dEH[P&2R:6sDX4EAY)23tqKQ2"V>(s,Mfmo90B#[aj"XM&rmfTC>nR82`l*k3>9fjSm
%\(i[\<'giEgkuh`g)8'f"gHmhPr)7?NTYt0E`ThgB6n/+!Jcdo>J"M/N/V\r8'bq5@-"e:VJ&eu1hl?QSLGdR:05^T.bk;qmSmY`
%?,K,l;_7&u\ifY:<3*W]VOT*Z36VDmPV^;"(^+2\pIaYEe+G<C&jmV:-)c1ZraDXS_s?sUWd7iA]dDQ/2>0l=Np8<u0nDmYRVl_f
%I\WC#nZJ@\7`;Q`FrRS&W\,`S-J_@,\fK^72kuu("ARcO/YpGaSS*Xgm@GCPaqH>YcoL?1+lK/c&]]Hs[I),)@_B+I=1#M7kpn@D
%UmrlW3L%Oo^&p&D,eu)d_rNdCAu0'>EFm<,gpJrt99HX$?/[!NA6C6u>#R!p4fhBc;QOSF`'^6lTpV?\FGfjeS.QisWXY!Y"?%]A
%B"TX,Uei!8e'/TM16-@AFV7<$Mh+RQ@JZdl[Ur/+8;3A-qJ55$8[WAlRA/sD!"M.8=cijqA[<G92PBAIMXcCG2cM@l27A'a1'd)V
%[h#B9FP:lN9Jr*tXq)fFU<TpFQ`83V#dEW+AoQ>E$p8XF9[=G"bM;_FRXn,X&N;S!Gm/1SR9lbZ3%U`%>39[9f#pU1dq.2q/A>8F
%DT$+V0Zra<j4$d28l_WG^M@2>^)%a;f^q+s_QdDoRhM7b4-sZ([..Sf1@91"_FtoMXG?#)Y5EC`\o?sjmANkR)nK%G.JFdYe<\Pl
%N]S&f=aer[8L\e#$Rr`&p^Q&ZP5+o;h5ZY5q[MBH3'&E6OpTo3!DN8r>V14@7@guYl=n"G>V1jS^7%Y,/Ilql9pD[sNoTkuX4?A(
%Q>s<q]Z2+6A;7h9DfF7gq@*I<Z#+j9jV4'D")2#dQ_H.'PXMM\n2'1kKP`RL*M4`]aa)u,N2t1,$ZJJD>'qu2KJo6g]HKs+l"88u
%k]3(5T#,L6A=gH24_#W5L;)(0DVTP4CTsJhcqnHcUYcV`%WL_k4lP44>4:"ucX,'agEsQ*ReH\VR(T<P[.t2+'"rS(heD5;XlsHd
%1gBf\VeP69om>QG5d*qtiEZo;VGA7FK-:hDGupD/'_n=lNPuC`M+TY92M74l`B%^^\D$FJ`@@4bh_VGG_/?9W>-.[WH;t\]IS[Yg
%FQmo%Db<@YO,,Q43!g*eC^d#ZqA\QsgTC2!X%Y=pBf)pfIEj&=mKhTAc@JbI=fi_!3Nj$R7gl+HDrl."Q*ETo\l5*:DXj$J<6Oa]
%DUFuaRki.!*/D_EVQJY@.n+LmDkr]e1[k;/1-NFTbOgI3R912No6si04nWK0PruIjG4XZJm:4qT>mVP(b`Z:aUZm%_H]$s+hk)!%
%3k'<X[A/(hReGUm_mS*Lfu<\U?Eo`ge+#C%.EkQ9'=d]B*r']Cp=\iZnj9>Nkhqf-GnUI<e$NnKBiKi%ZR\CiZ<=fN7ecPUarW5Q
%,0[OQ,a^#sbkc`(@`I'QX`1,g\r@E$q.fi!I<?ps*W&ONkpA/gkG#LBbk!8%kMSJo`8o]$fu?(NH"Q5"/Z*3NGsaSQ@2nOf^aJI'
%.!;i@N@N-fYdqL(>S\nfA)7<^Ol5^%O`&=rD'[r+\!83q`cZa$0EBbq['e3TA]_Q=,\Aae2L,M91=Ci"H"rR)$!t>=GoIpOV_5Uk
%M,#Kh[\BD[-eXfT5U03NqoF_7m;m$7a44q"R.ds8G.=.'@5`^NHiQ'qf\$9CJW!682M0T/*bHqDco"R!O9s]E5ph!DPQOOL]"KX?
%Z7d9T/2Z\1p)8PD^"^4D)'A"gX_7:\f%SN_^2Si4;qOfV&>c1o2kJ[jp=l-.''6S#cI;.ik_a6_RD=e&AAZAb?a<t"-:>l&&!r7r
%A82!#AE+ZSMd`1h@[p6cZl4i]*D\..J)SnIm;m83eN1tangEpAlLh,#RYP`ajfJ<hIJ\'ML[F:5btte7[0bW1nUur4;J@U8E_A'/
%>eDEdh2;)K4=<VIVV._$S%GXKN_Jm%eC6b%,CF/d7bRaE*d)G9Rgpe:SG;dL`i8q^QuBu,/N;V;B,fVeGUdhSM,AomYk:RF7X,Pu
%``tb]q4gMAGKb4s%CpgDcHZ--O56D^)rk0/l`s"Ja<1Pod&<7^7(0K,,eWbh'eSp1VCrQd?eUt2gkOP<c.(lK9?<k_/76GRr8L_F
%ke<\S]eW)mDYd"A)n@H!'r'a;rKoUhgO+!NU(;e7ie@mKf]M$[M@RJ#H^b8f(;Op.S%u.(U#WH`@`_g?FDldO6#]I0/QF/^&\R1:
%`D3,l533(j&harh*M5[KI6K,kM>?@P$t!YtjsYOfVZ8nIm[%\=*MN9LgNLkm7n8RpV_0@5d.-l^dJK]DXNgTRN`-2A6jH6>i0&=I
%Fc*U%"PfO%3B_'lb@@%`#)6oi8Q6h*D0O[]h74bJG01G8bKUO)O5!R=GAQ:H2;fd!H<;jTP0H?&:`.<O*]A8cU-Zn!_!)1q/\6Mn
%[Y.6*nZau,K6N#gY4Hf7*liuNWbn%;<Y=h@OlLatK6q'X$/GB6%=E1GUHK/OhVHKZ9fj2`fiKbeY@K_\JRQmZ;-,L%^nE.If"17u
%)1*Cp9FVe-hMZU(-];\&`p&b<KXVfAY*,N//Bb1VaXA"Qp=[.4;(t@-op>hOV&d.SQ=Fm_rt@T;;k3.e_[k3bla(Bm9Nc/f9c1-V
%oEd;k5Q#Oa4aTfEh=T2.L4'q3W\CW2A1eM,Pp84(lc[#dOpW&TEh&ij(!5pBB"nbJa2>E3q/AGRHY1Uk=Q\JNe#7&(cu*SFo1D69
%RcMSPW<if]@EXNKQOIIa(qOcA[2XIY-0h?hG8Sd=!Hl(n<5Jo#q]mM<]U.ATmPjOOHomhh5e>A_3:?%QFjXSa3681P4Hhff+]DY9
%oK%P?PMLA/:3O+6cnQo[6ak8<;4FHM?o#EWlY(?i-Oj_B9*+$Gg<,(ESu;7[%,1+D[aZbt5^5f'c09#D*MIB?#;T,4,*(u"@Eo)&
%D=SnkpJ(%_Re,?H':WIa\CrtW)6cBH(0@SH\b$FE([P\)!pgp)=ZQ01/H8/&4$Y^(+LKc$Q_(jEI\!"WZ0f#E]Is)'C4"iF282#c
%4s']B8;@^EG$a(6aBauB1Hp)[6[0">lE,3Cc5Gd!p?O3gj8AP*+oO-0:ec4]%*7Gka4B+8lCsn?>q.]k>*nn3(,pBpf5k6maK@-D
%G#K2G2Ka"pq7fE?l*VbTSRh+Zn(<ok(:Ug#0-Bs7D?dZ#:!0(YkjWlVTBCMjU,Z]@QF-TJ5MM4<:Q7Tlr)unNKmujpmQ>n:[_K7s
%5Jm@mMQ:ecee,m!5+4#c><A2-P[PNO*DAYnffKXmY$`7*d$LhGn$$_ghu9(:HT0k]*R5P_f56bcM80MBpbqSiS`anc?Ii9+)t]1I
%it^AFhtU@JF5,,MDRUM4am+)`=`<%l#]1W:[@R6LCEZ]jY'M8ba^u9!dkN5N(cV;]>X-ba:9GP<g]!Y6Ma#B]2QPtIbH00uF\^5#
%`j]-Xrp*m/SWh^fkD!f"_b/7dfAk>%W&-I+V1#Y;Y^R)R4L/j\<n0=Y,AGfuf6-KcI^5BGq/mWf2g[U0o>kZ)rS##\h<N%Kn\+Bl
%Ihf<V>OGOeIb1(^[N1(Kk#"+lX`+p'>/MUO!\)=?.Ct*7O)aXS+1J04r000GbpD>T^8mm+/!>rgeJ2=In'[9Odrk7]fTnA'CHc<R
%NJ1?6aLoiC\b17k-12ht7pQA<*M&M^Dqh_dg>!Z2pZAAA[6L"rc^V'>+sBcoF*aPs2I2[4GP)kK:ZYkRH/Ec%\'4R+N;FAHme`@B
%r]A?\mii.2m%73$=7ah\c'RIMCq(onOZm9q=Zg:Rp?p2\+6WIte9^7q9#BbjdgaDh;SeaNqQ=Q#cSau`Bfkl:+meC+C'h>.4mkUa
%j#$M4]lEf!DEJ>I8#Q<QM,mTFS!3U<'dr7SUq6"U+u[X,;J[[`fB)g0@6\,$el,.hhf[hcbk^:Xq\D!Uqj17]43ZRM,0QSET2Qt9
%o?kY(]C!B2jDY%c\*ptVGQ7[(\\.Ctm]45i#j)Zd]Q\^LFa36.?i'QFg&:lgh9F8`c9AkOpr2jJ-Pt+c55`)VQ4NH^o&bDme6MrJ
%^3Y+!:\s*!J+gp\[s.T<1TaQ3*obmgn'kX#oB)U)0@$^a4M/A?r1cBd.B,JE\id`PI"$6]H1UhbmOk.0o>Od7k!.#grT%nNiN9R"
%^\PA3_tl1`gRm:aH2Q4cB=CqBZ@)Z=YAS8B]">Vc?]00u5k4U"`n\eqr8_dFob?:8r9g>\kNi0JqQp4535>L8hn8XMPM=D9%e&4:
%J+gk?_m>i=cS+\r^V5&8EVK/cI<jb[O3Xo2/jI*4Hht_C\N]l33Y(Tu_VsbX_o&[-J%#?)r92mj#?&S>KD`,A3:SE?q"`InrAX]u
%iWgR_5/2BKI?nH7pdKVY092[T[eTdk5?\$dq=tiUf.=f\ZCe>lr7<mnPkee[gIGV@?%%!CE-QbZGPToQ_<ks&EV_UY^Uh*?]/i'g
%^UU_mFXh8NAf:Bcq\SG)Z7:=<:4ZUeosLJ(FXjNBD`a)qjeVgg-h=7Vb@KFQR:,=^5Q&^Lc.Vd/P='`\@/nK_YJ#"m:?Jh7"8_lV
%J"(1IUU%'8+'a;:88_C%lLO+=m/-52POh6_F>RX3=hhQCKDk;thq8$=lM5S+O-V"1Ir\!cn<\%f$PkOe33@TeGt$eDcLE!(Eo2!\
%I8(0jG0BUbq/8:CeDKY#ft6:YnT"N221>>i.sX.%?;:O846m5kptr-%oB:4?Zc?8lP^J;RF8q,(?MAl^b#gl1TC5Q/H[mtnLQFDo
%$*S*O2L`,QYE/.e]"2#4$1If`c0ZTjn4D]X[siUFp&+[Wk3W*,nF%/p=1<Z]:W[g0[I17CCq]H.\SP:El$"3)?9a\rrV>R*\hTn/
%p#NWq3UdWtDtA5#BE%o&cQ:u9SU^GE5C;kcg#`&-WnX!jcS+`oo_^0U*o?_Vm-ajAmsLP'mbdj56]F6=qY=@eK6U-4H['`0FR<(g
%kkae&p$Z+W7NXgRaaG(Ke:6&ae,&OmLmLN2eFdlhs6\FaqO:KFjp+#QISn\2CKb*<alWI9h@NMqX'aaI^;%;YP(PgQ%+_\t^&M1E
%V'kSsqY/b3ipb7iX*$HDhKo4mg!d".YP8]MpcH5mVrl#9^KZWKr8a>.m%&E.+9(g"(>"6Sg%7n@Q>uNl'sA0Qk[2[(/o_?:GM0]W
%s)RL&N$a%jCPhuIj,5E-[\)=rJ+rr]du0\Cc[L;KI1^X]Ru:pl&A>u1dhQgu?Pup;_ZB+0Q8kc7Jm<s':kR)a8HE%8F\,+@#Y/'l
%2&Cc@_f+Cs\*3pAprun2l$omdrR\lbj2^,r_ghT^4$<>\qXqmLM-g5\6j@PrmXI@#Zi$r)<\LEM[]?Lgel,(Ua1)#f0Ba>OosX4:
%4CCW![``U(No%BO9DjOPpU(\+*L&5^69H/^oV#@4R8"2e4Z>uYETOsL-+]8R[OH_"Z?st]CHfo]M?-ST#R1n0/Vnk4o.2_,h/nei
%>Gg$K0W*\WR:iG<qKq%5J,R":4hJ3?dJ&RZm8Zn3W$6-J*a\d7i^K>OOA3$^S"skh8N0Q6NNutn"l+:aQ/,I>?"M&^s/ps#gL1IU
%.A^F!Sp/kk+5<I3lb@?*T;!,D3([<9D->]omb'bmAB=Gojn57N[XL?6?J@7cr2%[e?1toArcmLO%nDZHZ*NEh!(Y?/[%YXBqG-FE
%!lhVDont0EaUWh-@%BXQI3u#Q/Q:E*lg;gU`B*XbYVm#\h?(=?ik<\"fC-D^l,6mCRibUAs3%a,TDecKk1<D#r+.6FrGc.kMg8aC
%e9bPmSg2Xr4mca>;VrET8+K=IdJ#,o^,Da7EeODGVMtA&2h>&W/ZZN*R,5u@O:[Me7#>?g[;a)Cf0[mE(@Wd)Zq_&:gAVj.qu-04
%%t&SJKaV>fc-QB6EW02m__4m*Z/Y'OdD<$'iK_K/^Nk6m?9cYVo?WeJhsus*SWnE'Gk$6Lf"\Lja%OQ&G@rk\IWp-_rTu8U@Ckdi
%m0kq^69O#ic0;&bbSTE.^ZP6ehf2Nlqc8lp^A\#L^]*r%qe'..me6YHro1C/YNNaUctsl-9n.21IJG76Xn`pk4MQ>eqt*cL(.<kZ
%76N^s]966#gmtW=Mu@8Zh9XEmfA8QkfK]%dq->M"F/#1S3a%KOjSb"_mk1tf;60Weo&c_%9<6OE`rEEFEGmtRqr%&-@b3('2BgR0
%q!9Z5KR4^tEa0auO)FE(0(,=QS"JQPK&-@slJ0&L];_iQQ&9T2bo3QDZr&7o[L^m_o#oLCM7Tjtk?g/bIQY/i4Lop*(O+WXj"J]L
%h#?8&3UsL`D6L3rA>>4a9A]>JE0S%cGPh#?PPdLuRW`BC=e_[%iA,2ie$F+aGPcsajQu<XN<o=4e]ea@YkZ9U[s*>U,/:n&]AH5&
%:X:XBZ5%`KZ`E85RtAtYh=9U)DYN%NOmTaipp9Rm]9h<6ru5k$T3OR,p09`KSk='QgnK;RU;)]a:3`s18c(2rjIsV0^3Ltn$OP>N
%U4?"5^gh,d<o!87D<;p'IeUt;e+M1.T43TTm70,0-Z$TaoY"D:eL-+_T3pD]E\i3T]_D"6RXhHq4P[/nB/c62X!eTeI`IMp/:\hu
%?2X^#]8PM4qf<ZCA'q3GSDBf#2GLtKmp0,%mll3mb?ll)ghNJ>[=@q[l[Sn(q=iW>J,-!4QTpIJaiC%(J,Ss;I=?&Erq:h(r;Zb7
%ktdQ?Qb,H5+72P'SuShW']RC!m-36`KRhB?%WYtglQ.Wu^<1qS>]R%JP'cN&>]T5P-26edGFqq!5HUu6?e;8YC](R%[/PFNH_$c'
%#FI`W&$16p\,KV2=WbeH5Eppq35iL5pBS#"rO>a$XOPR&gQe4VLm@gh]<0Z^lC.Co(T.47n[!@]s6"c6H@1#:oP30I?DtC/pq,C;
%?g*MnZ5^Fsh]JutkPa!kldP-15:Yt?<-3c8Ac5-mUWWN@S(7B(>2Jbg,JN2N2c$=7Nc,C@q<721ir8q/D>WP'mj6,'s2@e5k+cuI
%F_[%G9+!nEc7,G<\\KZ.p]>qjT`p``\Rdob,6%'u*:h[UT2pI:n>GTEs"=?GcTb^*^@FoW9M^&:ml_Sr\,PcQbPR2pXm`.-=kDYZ
%p$\`jD"$>\+90h2I^/nDhG8K8+1dOsjka"k7>>Y49W[_?I+T39RWMu]@>XbHOt\3H*bg+/RkLo$G0T_"H]bPu2?Y5"cm%qhTqUmi
%l`(UA4IK8C[N<MJ";r93Wd>i=b-,*W<o9fdHK4O>j.01J91Vuj9KjWJl!^Mcf[3.M?glA*4A,Ba8S`8Eihmql+0@KibtE4effR>[
%/nRk%9>IquGn?+2JXg$3]7h8OrM-1R$4`E1`-$0Nn^I.YCSb=h]PID(]da0"3"<c1B6(rtol)qL[aLWVeui7.\out7;*"ELEqeJa
%N`lZNZZfH<@@]C-22lS-f;qnj.2W@-:e3oKlpbIYS\8F5pYlcXi7=$P.349V(j%.l%p4eV"KiU6[BgKHXotjp1l;ATUdp="[V>V=
%Dj&J8(H/6Dg,9GN7rsbgIf4\sUJ"kJ'YXBY/@oti2H2M41o@G)e6s[ajG9(DKfo%ojQ6l:=cQ;bR7V1nWN:fnh$fclHWLR?/4.Cb
%e/Y>eFLe+N""%*Xad$^(-VeUuNjWBKc3]DN:2ns]g3q"#Ie;LLa89cQkJd7FY\qJ<i\]H3"iKEo!ZV$A&(To`_S(9hpu#V(^L`4r
%GPmWj%oUpW+a`/=cOe"nMn@0u$]?:F'M2%6SBh=`O%jui\])11I!^9bF80;E`Sn_@].]cs5$d8Yn5+N/p=i)"'DeT0WO35YqmYb7
%Y:je]hd>JZOE27(?B1$?-pT_<X>8\PP7K9pGQ4jZ=3lk'j.5(Tpqa.%S3Qu4<Gqisp!q=`^k8ujqn(%tT&4a,O*BHo]0"L)I[FTY
%Vmf@GSUYX)p#PQ#7\@!5)Z*_;ms`WE]_-d24T"O7cgID'itNjr5Pt>WBCWsI(Ha*`X2[AEYUBJMYGnR?DJq.6VO+H'cbI,;nl":S
%O+*]7pY.XrZcegcCPD`11if-CO"]pNL"<n)6g)M)cUmZ_42l\tVn?89>RF-N9<F/>)<np27/6@4Kn&mrbARdZm-?CbV4HeqTAAC2
%^BWHM/"NF7nE`h$mrH:JlgX"Kl4:.;edB*.p%doWb0PP2LY#p_^B$Fl1D+3uC;rFq8_LUqcg2H+q[]#h,aA3Q`a,3a/U$Rsl?`'*
%ko6,d`Pps%?bFl(diWui)Ap2nAUf*<`VQ%1k<U!rCG0MP="i+"Y?mD>FbA-Ng[j+cr@2-p*VJ$.Qg7rOV4)W+UXqtu^_Zb@pA*P?
%`I"?Mptg2;^Qr*`8&+mh83al&]0:ShQF#i%hd'PoI-oD;R5$r*>-%^Jip0;@n'CDc^URW^.ae,UqrR].e]d%qrDdPErhmY`?=nXk
%9+9:EgFJ,WHARZO++Gm)a#EIKQT+XTG?8G?0<$^p++HX]Lk)k1B5<@e06b'GJ+-XKdX>UUIg:?#99/"Vp^Lrnc0^:9hFcT&i3bS[
%^\N-N@5$HkSc6$qdd>N\bNm>9_,UG<<G]@h^D?LP^:6D&n'cK`9`*,#q4CI4=S,LlEdF1FOTn=f5BONd3pb1b*^2Qqq>C'Ak8/o7
%DP'+BC(#1!3EZ[O[o[eNmH!X!o%>(r5(!""h9^M=c-s!H:WE0CI.PfkX6/nqHh-h"iFpNK^HB1+r;QEZn_(6k4#I8P^\_li3=_YW
%Fu[+Gs6Neap!.,13r"=q@,JW`_k2qVc9W<I?heHi'`<q$Ns!0,meq<R.TXp6Zch[t=+en8m,@NVHpY")]R7"KrhlOI*.quNI^jcK
%HR3:Ucbrc+(slUaoTqMGk?m(0^L'XKm=Xk7fUA/obL,Bhm%(-1rMGSsh-E.T\GQ6DiKK0ahB.4?jRW<:ZcfDmH]o1lM+d1]gSB:?
%G4YU;B9,PA^%EHh?9GcGIXV"d@dTTHlE\nUdA[j(lu;\82s]\#%ekb#U>s;]?CLKE`Nq5o]YlL!n!U*HprT_3h`kC.r#:*l&bNi`
%Q1Ie$qS3$gk/73h4?ELC4>uBikhWlJ_nZ,m0Af!QX9i+3ngCnBB;>%kbE#(b1k4=ko6t\%qq9ZQ`tqOD?Tf?.Sqc_(ci<D%kj%a'
%iKIL)FN91Z:IKJcW?UE67unb,B_2%3@/oZLVdCJ$4$3I?VY/3]T#6?_jk7W3Qe/P74b!C`mr#?.s6cIo8Tc$*pT</!(m?c$JS\gO
%$ouOnF3_#KI/T=@)b_)\cJH,cpRJ64OB_!N-ZunO2&\](2?beB)6V88h2PZ*;_$US?MLN/iqdC!Raq@E+2.I=7bc:7[n-2Wp$<BC
%SeQ9?&Y:H?TAFVR%jW5DV?a-I2l#cta6s&0S,#9pQ[AISIUd^8A<L$'"Zr%IJ&1rto?l/U[Mrmhn5bW0??:;^DfoRi>;tud[Jls<
%WB%TJl:!r4d%BsIs4bJZ]D1Bt?H5!q@jLU%_F8,Oq^F:s)g/>_0;s5+"+#p12YT)G00&d=;[^X6kMlK@h^p@.(E)lL1V`=U8O9h_
%ri@@??V.(9"Sa99SuTMPhLQ.j,.VlZ`\X7kqM\:&71cJN>]Z'@/id(VAM51QP@%CPQ]R%U4KG?i[+/`SjbDjN0'-Ag3'J=G>Hh4J
%2lHZ?WAhBTK04Oa^WUMMqUc;X*_gIa48g*=Y?&]9P?&VE:4U<aVu4<s;3C0`F"=6I!?ti)9O7Mqa4<9`^RBs:pW@"Ao][@BOnG2Y
%YQ$'4=5JL9i=/omo8)EF!5.P('[IKS)@MAAa2$Y_!P]^^l)D=qqC<05'*BQkN8j)\FeErB^VK(Yeh>7#@,E;t5dnEfc/1dXf%0rf
%69PWl`S?t:HjI9#9/ku&.bR#e18'N.TN*'G_"0n="ZchQROS%:<r]f6O![-A:Zd;qjo\MIeU00%4+Z0PMoc7YK7!7Pk,"H&T3!P&
%=27L;ZjNBgaZ#e#IT-i7hA`2^AI&i?n_AnNd9Celf3#@EL;0t%!0HWgkk,&IqQ_^\Wal(!Y0$JI@(8.="03;;!ku9\K\Z28"PL-Q
%H2oL=2#[aXI7k9gOs/cUjV01hjAgeMaE$eVnsn_t:&'T'4"Vo9r\Zn+i&Oku4e1r[3.6hib&c?fNT$AThE]fAh[H,'I/6#oQoYYb
%n1;mM+IAUM3G<*]_ugA]jo(VU3`T/TVl<rgHi5A>5="*CSdPc#<6!;@1^E,`r@->=@ip0bFhKmj$g+Ug5F:c.h_2usT>Hb0@d7OY
%HKuAT5m9:P&NYU.2$3J'<[^1ddbXYQd,sdS_:DRlAVMra7X=Shb_FJWo]e1`<T:/H*.FA8dEFtD%LIHLRd8$bPbALBWiAq\7Z@\%
%-"aKsWq4tmK/@LnS6rq8?FA1r9(),L.69':kVR`a7:,X6Q)gr0oGR_-ot\K";@!DN0q%L[n`!ogf7LYZ2dJ(""6fZlPDMf_Mnb"^
%?bil(H2%VhD)u#7Im/mDU8+RI5(pH1FS)Yj+UZM$6Z'hAoWERi;<@oIT:KV@GUURZJQCsIT2+S5)5&1tCV#$c68*M--3'<^E0>a[
%j'@r6fc1J4F!.'skNKHDXfFP>P<%ri`eX(O/F^%I=(!>#2'WC>"+G%"R;@AU76_Y)eHlKZ_sup-![%D_i.EWprW+7H@t.)?(u;&M
%H8XjS0_.JqZg.!AJ,L(#p+<<T08Y<kS`ZuR-boN0LS5L<_$U*3rB$7ab>47-!$UK=5k(M)YIH\5:S=c<T>,_=UN34LSeZN:!&1))
%Fou(QG'`eqpY7b=LR[<_Xp9o/69gT/<dK>e:up(3m1(397%S]>!&IN%B_;]]J5YP+Ose<!8d3u:7PBii3#1ZmOJ`DGnDucM%L`u'
%LZcF(SAS%a41b\Pg$V>m(GnIXXlcgO)3$B+rnMH'[i+WB78^r$n!*[Aql00[$bf[FRlm8B,p0f0rdBt@?kLlV[,:IR\$*i9_@$r)
%N5&M])4pEGo]m"EHGAEkr*RZgMsbDmX,l$jq0:biQ1&Yd>O:bUQI?j.9O%B)60b64Xpu9,mC!O*ML3_CG4`<;WWrU^:XAj?`jl53
%"8#7Uf:I25J=4E`C60KWZ8hpq%:2(=#W#ZO%[69ic0Z*<2q`-?LcaIC_Z44ta4G]lXp4Y<8A[tXg^E$B3r#M']m2uaZYgmLJ@QVd
%bAA`uddJVu?:bRj1YR;+!<gq`1d93_S+"9&+VE/)DRk(*+;_hEHL18cbIfR?Q`0f%_>L\.mp;$2'Qs8)QimD'%ZU9Nj&$<ZRP,3\
%/GZp$CHI+2Sk#p*o%[T305/*(e0[J/j"t+uFNSj?e`D`.pM@RZ)@#qMhuh>7F9"W'H)'-&H_a:8;UaEfBuFuXdE42udqjDVU&-mt
%iVMHn$O$Zpco1$QP*<$5.=9q:irM\7qliK:ZBabO6HfB%(\s=f6>UsBIh%3Fg>?^!k<E:'h8DNbbY[Ke^?#8\D#Vpj'LR/e]DS"Z
%$*[5KC44r\ouc`$nRnY)/TgY8"bg@)ja7:KS'A"Q#/VBG3dWiHQ(,a"K0oT`/OY*%*l@7Rpqu@jOP19W4nNsif(pAYZ9#Z>Ud/Cg
%B=,B"UD-7*'kJH0.9]`KHrK'R-m:_Y_nR0d?f]V,'N%?3e/S:SOa;-1j^Aat(VKYHSt\K`cs`;s?FREYP("@<dd8F7[-<o9rEXgU
%lo_b2HZn4Ns"o(1jZUt14On?r;$=!OLm$AABL-V]qL)B`EI4"Gs48+$RVfMPCLu(,kuR-<BTNc'^Mc`4GL+H?j`pH'SHD\?Y[jH>
%4FUIe6WI"KHM=90OuVj/($0>W2r,F]eN6C^CHKmYjV[Zr(AKpNQICfn:RSHelhCa#5XCESQ.JaS?htTK[K)gal<<b(\ffmh>C!d1
%C7oQSb$`2fc,Y>6!4k"YUA&d-R#$nXS'bQsLEl5fGF$)bEL>M8EqHbR"]2m.Q6FaN3ppQLPAcKult@4#d*/!M](LY\aU.!Gf;had
%mODmp$SAKp=--qBMe<@`5#k&m;:R,_l5k\ikgOJ7O9\#_h[mUH>]uBVW1L1uXo!'-anPG<hK2i<;C]Z4*&pP<%!]KY]:+OV2[W[X
%g(W<*Mh#pS7H<*&"/D!W;2m22+^6V&4*hIB-;]HGFj]#nM\o*^H0`tqoqm3@jI;lE%'.:+AtdTEVIMZ8SX^`8&VfF8WC$Oq;(SPa
%Pub<h1@@R:R]%i+iArFg6u!s-'f'43K>`N,UNnUm48JSDTR)%7b7k9YQ.G7)F$3@oh"l,9H4`!FpmJeT-.5W9([_p=6-rKMj5]V_
%hqbG]?b2o/>0[op0<6?W?:ap1$Ul:/HO]3M>[c_J?iQ]Nb[*qb?dM%_G,W#3YA#*o)Z%)%pf.Jq,N,lnon/[+1DA2Z)rMIKPS:Lp
%W57Vl7M0V,.r8;Yf3-mICgB9_Y0j2mD)^5.m''cVjmCqEjfK6C_PjXS<]++1q%@oR)Q7GY^JiEDcc<UKOk4u=??_koo?B4gq;Fg@
%o0##A/I4Q?I51Dmq_Atq5UDCLi#XSM^UJupI_C2I[>O&(4Tq_SOFlWtk*,Vah"WBpW<YMl>abLV@@g./*=!2^gWTRAr?G></LfLh
%M)[V6#Q7T0s6o_l+Kl)@PC5aemg81rebqlu%u0l^P?+C)Bf*o()dE;m,!S<?o8:"RUgIlSn$2',e%`8Z41W#HfP\>$oQLJBVYnYR
%kqnN5^3)7IQpP*M[:burqBbb4=SWc'q<$VeG>`fL)<.Lm/tWAFK\ObN"Sqh]]U.gJ?8E%RGK7%VH'*]Vo(^,U<ss'A3(@JDb'`l,
%MWZEgE%:h8(nIVZo]N9I/2VSEU1<jt>JBht97?_FrVV`QJ;EVM="<l-F?6uS5Og\Y$s:Q)f@6d<:B"<bg@%%imfPRJ\&uP#Z3NTd
%^N<2s41nZM?Btn^r>$elh5OdfdH]STrT%Tkrs-Tg?J$6BY+.J`ICgSO:n"/G1<Yk"+eu)Qc2SZb$KK$74M22Omn\E_QW=hde9O'=
%qs&rEF?"2Q3Wo&Je_b;TdZc'HQ6M+&HKL]7Y]Lk+frWSHhfCWZeRXUrqBFHX-qB&@fp4K33Lkk04@\IX1I9JZ*'_RRH0W7@&V%j]
%L]gtpA)s"#]`:dFT>_9Y<;Ik7PJq>`5`a+9Gq_(>4W`q]!EXI:!>'9lh!F&b'.8(QQKV,R93cDA7kn"-c(#s.G`KtSW@fc'eiH&@
%:[n#7l"DD=QuKHBZR&YHT%_duol.Wg,d<6:,JKpcm?Co=F53&u9`Q*MYBUR;'RA-0F4)PDCA9.(lkH"1T>V1/H5:csAG;3OfNM>\
%!^n-*SS;^E5$@T"C6ZR\2nq&'A[cZS&CK^&c2`j.YW7KD+]Ga:B6;`I&C$HY.D<?>c:HGgNCV$%8#V_3cMYRgIgJ?aSZtb0VP#8T
%DkB,89g.CHP9*:GF4fbA3*U#M>INnW"RH_MH:@olXM<>HLmt)\m0r.T?>eR+c8j0sS5A3RQm/%n+)r0=.&W+`^cB\$k3<XEo$*rd
%4qKK3$ejC=Z*`KtCLmtt=`YjW$5i(`Qb^D'bKHk3CCG41D9!NmNLE%O?u2TX6Q7hK;M]H4Tidb"SLn_%X$Y7_cFR!ZXc9VGd5L,t
%Da.WdA'WC&7)QkH*Ae*)!GLtR+[r.Zc;6WrIL=S7)$N4hMY34]a:C$01q8)u6_Tm*Pf(tC3`%sVn;e@R*@/l;`F-?k`+WfaPa&nD
%d(YZ.fRbCT25X4iK1U)"YoBbP@@I.*(IPGO0:_UOS(YjYIB,qK#M!9+.<e-gAMuZ-"k,.Q<_*RtJB5,_N1eY`1Y1cDcPJ2_b'Tnc
%S`OuNIcWR825,G7j[hHj67`<#p?g.ed9ftN"@iZ0@U6iRPNYIs*]Y3P<SEe?LQRgGAq"-="<B+mZkYXrgq92hT(O#&']Hp&!#iU.
%GdbTQ'd;OM`WuJgn@:;NL`L:VrlW>*<mmo;,<,(eWFNUgrPekjD&)9`p7Ag>c`2%X(rpKZSNT#D9:rHi*cT3\_nh%f$UOfH&qqPH
%G%;7q2bbEc$b1P'BX/M:S6Bl[eI*d'jidOD7a-(P>Wb&=o/MjMN8i74q?)#k-ZVaK.lLME%aLZO_+uL_0a@*&!,)?Qj1LmaR`>D0
%0`[aRKhm*hm3B==E's-:F\;!/"$po&l1[92pf#6#,W\08=l_X"blVT<VNTPVq8Y\B)VB/R%[AU.%&Q8<)V/U1pbW=?2.mqd;g5Sa
%bIt:LSHj"OAUoUpablA=FrI>[;Z1j!0/`g!J.n!O/f`Q`>haHXDIIr&+S`8S)_Uk#%0JAM<F9apQDQMIHtCYSQ<$)&*j4-k!`s'3
%\^J^\&MkNj%7$][kSc]F;W02&BQd`+DO(ih7@o=dcHVMKMT<>PCka8<*:RPK9T'eD4k3L3!\A7j*aNsp=B6*iR&_tN!_*sk^r.0=
%B.1i!kc%[-75iQh]JtM,_]VK=c=E++GTb#Yd8MlkWFM^==)nnZZqB@".'Md`;#Nto&YLAVJX7l45>(E@'kh_D6s24-#X=%5nK8n8
%(jDZ?g._qm4[jkQbqr9!BOGlqI0#o.ppDJ(kk$041bg*u=Irn-i%#td5CUuANjIgu=b)!Z@$FEN849d.kZ3rSQRrPAqqrl&&YK03
%jXRLfO+t;p;U?Nf'sc!8:f2MrZK3<Mg^ldX7gl$<i)"!<iUSgU5QD].&;G'cXRQ<e-nGA2WB`:O30>CO6["sM3$>f+i.hS,#TSC4
%nlIGL(`>U^E4phKctJ4ob^Sg!?T8kME67Rk6i[E'+hZfiW+/m;LecI[dObIGr/c:<&s9ic="n_2+2S$XTY?+1Lo(5toD<QK:srM=
%<7*!_(`Ms+FYj)3ZY+0`Y7/go0?TM3>)3C2-s).^>gOnT&X@#<"K/5,0RO`\HCZQO"6hB[cmu8gmYYabBCeO?(%SP3)S2B'2B/GN
%3cflc;8dKPg#')tX+=O/XK"P`QH<PlkDRJ4e%CmY:1$th<9E'2:mdn)[),J7:_t_]RK['C^BA0d\0s`*ic`2=$7I/^e#J>2Kl0!j
%*nSX;8mgnXEYoEn5WhFS<b.DRTP%g0F!ma9>gZe6kb8Y46k8]*8,:<FYEW9h9?!eX9WhXn>g_Jf#(:F+Z!6&6qZH&92"Q+VNR^cp
%I#)-CgcMQ'?SsKEcZ!^F1BFQ"JjScY6ph7>7P%92)%Zj^G'>I*=K,ji?kd(<0IOFs,8_B#$_Yd6F!0=&<?)T8?GN^h'C%)Dh=`N0
%oO)X-O%P+!9XOd3<6S-nfe3t'\*4f"$*3iEU;UL`QuJdi';/3t%B2E)9,i473]_pt+<2gVY>GGcs3/JO)PHW6'Cm+ok-a+V)Vc1/
%=`'"_o6i\m)G<Brh,>/gX]Bk4XZ+uu3??%3GDRX/+')#Yf5`1P$8e7q6)s;?k,QTN$#))IESB%W.t&.LJ2)eTJje3^^ClK\=;E1A
%997OdnBJJ87efSdo3[+Ihhpn8Rf=)_CCrb!S<<EMAIL>"g-bd]ScoVDR,he9^i.<9qfYo9fY*LXER+(]=lV2Y-J/,+-m
%_q1OC6-uOo=d<gF)2Ya&7BmAb!T7KJ@FYcOa17!MK$TS,*hCSc7of(Dand#(_K)/TTL.u2>a7iGebH.iWLC])'M(`,+h=UYZPnp7
%9CqAK)20%,JBD-cESOJ9DD/*hELHVJSY8HtXb'4U1Pdn5HQ1'\GYiKZQM5]&ICciEh"!QUi7Z=^#<\BC7/!HfCC_cSa2@nJ[+a$(
%pdBq8<#%BB!!g#tOqN2/E5GU<@[a!t4[:pMN[XZYm`U6@cA-^B2I+3U#1?]'\eOnGo+[bS>(<^b<8f&#b_H4$_$ms>QOhZ!nmXU6
%>(@kCGT\529L*\(>UFAFBKMDfOG[4LThBYHSfQ.5e#9>J_Z0`-S2kIZdf15nl36U\^IG?),qFOt>+Q*+0&C;(N:GQ_g`t(\R_Ojk
%d.#gES!Jqc#RGfBf'bX[']7(kUj2/,D(W[t[r33[af>VBOJ5s\7'"j?^Gg9rA$nd(e,I97n`fFXj0hWj%VqV#qIr&4gIEDUeC@X$
%=akYH<X38+M`dZQj_%11\3lJ$XVpGZJn;P9>-`$Gr6C7bDE3,aoWDd791*&gZAoc8a\e.fNh!liLBLc&G%Z'4'qoN#KLLr9o4i0p
%g*-+8C!pk1)cpt"mUk_#;Ase<dP9m+Y`nCAjkb-V:8DJ_SJ2eIAh&/\)lL=i,$AtQO:$k+$Qeb2eM/uuL1d[jYtA0s]nV(72AT`\
%ZAWl%#Te>RgHQ#mI?CNno'-u\r:g<Ycsj!6CrL_emK>a+oV3j.Jl4In?>:E.TN.2.IPFp_S?ZMM*e`(*53O`jiKV)nOlI5G4&c75
%/?O/t>t4rG5/h%o"gg-bOjh@Fb<JQ.V@d1k>8Jg\e0/l"WEg=U-jpG&YXK%>i=$ol<?6Z*@Y9Qg!R:4uHnPb+Z='>+Xt1qa+qlu#
%W=Y6I2r7h,*``S#.MbrChDJIt5`d2]Lc'UH4f&b'o.8mJ?S$=m*Y]K-Li9#&a]`Q)](]cc%&dD#/4YmnZgPXf:`qCMc<f+,,XW\U
%41(L@F?1Fue-RW*mRQY?-DuADE3p2/Hiq525'qJrC2$5#<aNuX,EWlMQ.l^TBT)t5NYs+2na/1C;Ou(-0#?ohkAUoj-P.5A5Bh;Y
%U2<=ek<OM!:7NnlH!`*9nOkrLjJ](Ie7F^9gXQ?Bc+SOiK4"e];WpLM/CsG@GcGe@N`jUooDDkYa*R1<$7n4U:TW"u(LWQu9U@H/
%GW^':f?m>+)eL%Gk^0[+>do;a_PZHAcjdgaG\B,HoToB9-^,bMK^i"ei5Tq\,/:3FIk0=>9C"Vk!"PnZHC.]*DAptH@cX4('a<5a
%/\I#F69?'p1HuuY*"G/ToTH<'!,Q@3NGp8XSe,Np3f_m[brZK4ck)),q&<2*\isHCVi#$k9/0HZ3MA85lVh79B'N:^D@E0k`[6q!
%:0QgJbd4ipZalL,)ZSV'e,sCkal<dC!/kI*\(!g2F@U)6`r'#gKpte6r;`Pi)Z$g5DMgmViSjQdak!f>%sCmBfg373%QPK#0ZP/t
%:'ABAA]@Zn6pQH)UNqP6^3=tT"_^5gEpsWZI0<flXC^2/RIk$X%(9j??>moZD(ZTmTpAeE=3bO\`tn3tE#9dpeRGnuLm/_!X,'j$
%$WB6Xo/*!<9tY-)dZ8_Ol%1hY6B2e@9Ac3NGs5@UJ`*g89H_Qc-A0o$/GB#T>\/79nJS+EH0f'5@2IUMfUa5JX3%kRg'5/5PO]72
%IbS_"W:0h*"A&X+aZK\<,Appa`W6g8MX>+?)YUL=UV)7La?6Z"(E<O%bq'DNLJNWC_:j-g#(gZU/ZE-*;gi+h<fnn:ddfebJ_4G[
%%71eSi0SF41<F\HmTJZ5pADi'LaHaJbB,[o`crK=EY?R8BNj1:*W(((5(l"Oc`HisA7$2NNV4%\`ub(`6.mY(f#'?efuq6qWgmd)
%W46\K,kUSX\-BND2lRsX\p%<]+\2(hhRG&%X:?!*O)?tCC7&D+Z'*.YN^53W]J\U]B1<^t&[2POEI)25])$FN5*7(34>I@(mSa#(
%FhBa9\Dm3d0uNO='RBcRej(3R;fbPhBbYhO@D)u?Y_tYLef+e;as]?b4BN%c<S*E?`!u(KAq5dVgaRnUZLM&"`kRB<lj7sM+O=E9
%r"<]0m;Ru]C6I%k^MnnUfo61CoW&EdW>Q8j(4V$!&PX0SUL+h1h[_T-LWK>MC`]J^(Dr4hQZH&!Yu3:mosW0tpX/_;E@NP]:(p=W
%Xft21\8erg#9^9\'mWk1$J4#I.LgAd-Vc[<G4jY/WR1dAAN5NhB!8/*`jJTp>Lf9F@!LA[--Yo%%\[$Dq-3#En]o.FhXbl`?uZCi
%]X8c$ZeSU7F:q7!!V,;AS'ojA.Ym#/PEf&<>s<_Sj[/NN9QEddMjAH1OGnL'giU^F<bdHr%aZdpdrIRnVFL_)2qa%aNRneZahB>f
%0/+lIjL!/I7sj:e(u)To9-.dp,0\jNkPTl.!!oSgWd/L<mT4kk<t1i#EEfI#L`Gdk"'BK?2K279:2c2^a_475iKL<acPu)ZK(>.7
%%0>(k2.o:'8*oN_e5R90Y_S<-F'Yft3i_uuPXpH`aQU\JTVNUib+ierYqpOXf2A>]1`BDBAD^7G<c4XpBRD]rnt^M^2a:YBRL7Pk
%B5in*g?0>9B4=;`'@hriK.OV^KJJ@'b40]Z5P9TNn/-['F4UGnW]"iQQsO8<h0<W+m#^OXF:tHmVb:pIXbgVdEs=c)0^E#Bq@r!6
%<N^@RT<O8*Z=)h\qGfJ=RLS>"Y7?0(*I\^m_#$lNOpLSNZ3DeaA*t@hQm!\?Z\(VKk2LQMG?iDV/(UKG;d!\]E$PYI<G<lD1W_h>
%M*<%<aI@RlBS[*^`_Crt^0Sh:fE+$$@69Nm)RCnaGMF99J76]WXlc)\AW`p%j/js>O5!J0D/@;I)(B4-caccMk^4q//"YW*Cu=k&
%F+<[XM69eF>8<NL>Z9Q<Nt-?*l%aguNGW&\#S=HN0*=4drdeu*]NrR#UN!V@(7<[!%@=IU`h84:=_%Y9`Ds8tDT.H,0<^9a."&V7
%](t5/gFTF:=C]7T!>\V:H.2F?_@>d+*]WNI#\>ks+nnoke;I'03h^Y"+SUFLHf5Sr@B9GQ^buKNU'7)^)OoYG!nG-!*IfQ2/?uoY
%FT?#0Ne8(Sh+O,d_Pqu/NDEssAHs&UDV\Q#T<WfgYR6]`St<&nKW[+sEsf\_SM";CQ"+VI!b&XSC>##.C92+^VY($6/4m`iTVEJ?
%![MN!DHD8hS)+Y3o'!PQa#=lA6#ldCeGGG]nE6cpB0Y?(b.Dj*meWEr43i<Eb.*5USFb!GFZar(g[jMC45/#f;k)kf]EnJu(YSq>
%I6IVdV"DFd<DRUtf3c[?[.`NN3=aq7[8hP/^[b2ci:C"j55g'%:X2jI(`#1@Ks]@K5!gDbp?%Qj5Me=@Jl6lW1M/*94,+d@3+ekJ
%V2-cH3=5;%Y4[_!J"1.k9-V"Fp$3EKfm>K/3NNDnZ_^Msb[b5Z?e9esSc**[:eVJZKBWBN<a`UQ"[8=4/,]p)6Ag^8$:U6u"CNF]
%p>-9C=KUe&X>`hLiE&8?.T*iC%):p>i6(60#JUbr=-5.647L'uJ:e(p^9EuT:\Gq/$EInJX%.8_0+J8!-md*\G7&_<]u07mi,U;R
%p_Aq6J2l#+"<ZD%H5_j)%t")Z$GX/^^!PU?5P2c6JW\%,i./NkZOm1S/@3%lJ46CWquqJiD?d`VDMSPYZfn*g@0\*(h$('h56CR1
%3Cpr(><=_sq3pe`&d\K_<!RCPnImP&8-2Q6Ls8IN7HI(neF__5)H$A($eXD]_QrG%Pc_G[504Z(QFsPHaJm;B:O&_QY?siI:k[AU
%.RM2H;ea`E2&u[@N!@@=B,<GBm[u]PVJ=@t5l5T)!?6D_7/8H[Ot'`Dk)e"^`H%?D>43LV+ana(`6p&MO^_WAN@;)XVYcBP%P<;f
%bI6A#81h7t/U\jFAH+BNF;f9B(2bn&O``2l=dFE=oM!RRW_);T2D^$LB9PO;Za']b#]S'k2ZD!"VXs</0CLZe_474JJQ'+d8c."9
%:=IT^nUVhooU/tNEtXg9e^'_Q3,*9d,cZ(\jFG-Bk.ur53fBC:,=mI\$`E&$MG4f3TV*+>1t%79%rV<Fd"[9a.&I%5lkV-._#I%^
%EfpujK,^DS*N7qW]1'h>/f2)8X=DV)8==s97Q]SKN8EM31Q7J)%$U!6:u4PR_ntXOUsu%EbjK-`0`-3,O#_T>""Ci+>l^U&YH88K
%JX'ijj1j6XHfYC(kELa_gec*I$YHWMI@/D.^LJV=3P`iYY[U6Z&,47$Wt/!6[d^;Rfjj+qk/d<\R'UIf0NYAT_q_BH!qQiUG0E%=
%#;LU3-t:,W)e5-&AAY9dQ,OS5+H<WY@gD%MmK)>m3=6GZF;"1FYn#n<%\]8oTV]qBThme2KknLR(>06o9_(2S/D;q(Z'aW\_W$8t
%"A>2-/M*1E)6o.t/7R;H\Vn.VST_)B@8g9J+O764.S!uK]MZX_kCiqPCgAd^ZQ"Q@*nG(*3NQ.a[BNtkEFFK'WJlfT3tjLiKsK?I
%gHT+>$_G;f^5kFcc$L)UITYK;-Pr1-@&a.Y@,+=6K1.P!%(9<6C2cY[\A<SMpC);Lml+'2%OQK+B&QB%piid`i:h(lL6/eb0LQBm
%HUDmU.N@B<$uJPC;_(5DQ-:3<UE",!Xbn&boi.t/5&`2P93`>Ch(mP=%+YFO`1J4_o-tSbD;;N.])nUVBMR,.CG_7d-DQ8)c]%Sn
%TPek<5sA%?+2!`@:ei7MmN7F>Spt4.PSTl\q)t9--HgYhR&r#o`^6^s)_6i>g\>3a>E*K.B/'#erI])P/[(-A1:$soN13^eAis88
%WQC54:J=ejFD>i>&i13hcR_`u<aD2J<d/HhATjFi.MGi]OqQTJS?pM.9!2[N<;6/#VO"\l0_Kk6VJ24:bY64:nSHpM&frjk5JqN&
%g!Y,kR[n(ALmd/t)`)gRS`[`"-"N>28_KW$'kcZ6->5?)3D[Tk@5p-gS^hb!*\HNUH-u!obe(u9D8A\ig1"?JMAD7a]sH(U_/JiY
%Lo)IR(Ms/3-,J.fQ<[q1YuE9WT]NMI(g-Y0S@2,39[g;0lQ47&D6I.qa1C$j*d\i,]%6RWrC)4Z.V6&q23fhUXG'\'Cou$#4)="A
%b?64ZLXWi[VRk@-1'3O#fW:bhB(J]LL5cpMT.RS2:UXB\hlu3mO0hnFAW*\@l47a,oIao`8N:KI8K!0:.ZK4,Dq%poF4kBN*pt"2
%p;C9$g'aM6aPKHXlh90NbLl!O$:JC]YB\&qQM4.0a3le*fXAG:KTl&VOebZ\8("X04$L+5Nao<Y[iES:]BM:l]1o3Z5&EAQIbT#A
%PD4WembniB]1ZHjlab$Off'nqc:*cf3)>8Agh1f`\jje1``)?/J:$$m`K'K!.mH-7>p&G8jVmikQtnief%l'TAa+B-6V]T'F,Or+
%I:%22<g0Yt`j@.<Y>roZQ_)XLI@boI*iIN3H!&6tjb_3)&</&9Zdh4HCr#,rB<"PL._Su1%;@<MFdG]&00_3IhWQuQn`G6Ib55=9
%GJ$0])=P;d);mEB(r3,NEJ]I=IN2.U$:_ZdQ0\4<#bna:5YRPD2OK.2`4Ohu"O$KVOm:seU:TGW@R*>j`$SWcOllUI,fu_08VX[Y
%'q'o%50/JrP9<BU1.Uc=7Or&:b8Z>8m6)rAoVtlAP<^0BQm1$(46:0D4cdL5iSIM_8I#?fKhd+W%<TKr3mlsuducglVBb,+3_kW1
%k6t$.)Ds<NDr/bJh,Bis],VgVE(KKj="h\8-#]3RPfd9TYd1C`^$oIh*%dW@"T'SW=1CXGe)4saElj:N>6f?l_8RafYOd<UElVge
%'$Yo\r9sPG'3PU\I"W3TEMajLQJ,(^`b#7F2`:][k6!?/A,]?tq;MSe\?BkgpG&9,I.Ks,L(upOB!q<\d6ubCbLWH',.FoX;*Adh
%TDEdqJD6#V+;.u(",5D^($P6@HXK)6lil5(O(^6.(tjQ1rRimWXdLr\?(.HRY!hC`$He!EkWRrsKt<m:Vui[KHtNbth9fdnoR:J/
%o=Jf^glK).n6sob,2R^a)\G*Q)CaFb=CedFmR4/*?A_^s3>Z2#O/Z6RegKIog7Nq$ICYGmid!+eG5+Ag8-u&u3t,O`53dE;:f3A!
%L8*eY.34rf&iJ@l30.T&s*g3]H&kI5f4UhjlfH8DY9c#d8\Dq^X0s]:2Ku5ein!tc)#&Ai6Mu8EXMtM@dkQFLm(ihbju\Ear>hV(
%[*?Kn@dgZGP.6U1-KW*QR;C)VU#aq4R\j>T9IM4@m>FrO3K0gN6K4*8T?6hLBP_dfbEZ3l4faQ0)-ah#bQL-l[7k;i(P)+IT<^BS
%n<"0'8Q^biQE?l+40(4T3BMfBEhU#XbuLcb1:NACfXm?:cJCuJepOYW`qT4MTI&ba\[XjWGAo=p1n7kII;EULOL)[+O:&JBL$kp]
%.Uqee\]Dh#L14JnR/rc9`6$_+2K&ULKrG5WWq"qbC&M@4Qb?m%[@H`P#3AV:^8kXrQF"G0_LY=qB?OARVobUs(tOXGWL<9V5Kf0u
%ohb03CJ6*He8hllXu<Q'(3R5gL<$4WicJ)#J+@2=r@ePs)Jl^mB]P^BIq-(6%6p@G'DEZZX+QIVPN4s5rd=;$Rq9L0'pik9\W5@L
%9k&-b1c*Xgdi<K7Tp@i$`*-%0r3Oollp>Qjm=@b:gAZ<RTNs.8ZgDU@`HCB.C[C,ecUhcp3N?@\[2SA.C]r?12J17+SPG`&d1*AI
%Fdf4:hio%dqVauIhZ&NKb5HC]:`T7'Ku0K@WYG^Ca.P9MhbW_H'uTU)BF_M><.DitVMe;kL`]+P>15[4dl9-9"@&aQ.dAZ]W8rCe
%ak%Kpa`8#fVA)UuVYRNo4'`o:fSh:tr3M!!?(7s^LVoX"g&:MOe^p=0hQnmi?*K#kC/Jk/f8JHA'jM*3eS&*L7;dBUm/!bJXN7S/
%cGXsEBWOSe8>*V%GK>#TA$lWo:!?5:2FB,=gq#JkBq%jqp$HQ,$LC;GDql)Mh;3&8`6nY1T'qB#I6H+>*&,Mk5"%O#g7dij&kBQZ
%,2?3)*o<(QX@sb68GqR&%uG4&AT>-+DFV/)YoNdJ<j"%p_`Kcph/;"44F-t#R!Qr8&(C(4/O*?fOkt+4a1f*F:4jd)jX'Zj/S(dH
%jefgdWSt*S!Il(3VM&i'1V2@W!VcWs*mfS?*XIA^9&o/`KAM[Yk9%X3nfJ,:iEo?),=qfnA/OfiJZ[VAT7>MYL]73.lbY0=Z5Zat
%!.9'8aYpe>chF<Qs22cP+%9(P(NcZ`ms]M-q&Q3qXYrD\)i8I9_L'hl"q\)7qr]'rRgB$Nrr.)t%%K8HoDQ>LgG[p,_to/[7ds[d
%s)cPIPb$b<OItMRL?CN9;<4E!R\37EOlUJ9kJ7B(-Nk2Ip2C(kF$.5QA;.K\OJMA).7h<k[,=n%9/rS"0nA4ipalq=/O8(JDZp'*
%HY^([\'^i+@0uFSJM0nJKf:qZO<+nGKkRTqi]'Nq=p]rXi#KQ`9e7mCTS`7O%&5`.*65]$@:("7M#%`*$E#nWbb-V^h+ck0#:CD9
%Q0(1C]JgK\;'&sBj2.slPn3pNcjBuQBaOkR*!,<A#nP(=$u-tHSl_?dJg%P9g5*7oAN1;&$6Fer!+(g)+PVEa1LOH[J1Z;6'VGS+
%9EPo0'!u[1^ddp/[KOplXrQCD=UIiSV*$#OdffQ"<e^UHDZMki,h46%#I@e=#+?3i-44CM^)ZRWH!4nrBEl7lJD32EE:ABST$]Le
%1sch@:qBt;)'B(?/l333!!\Mdkf_=<]chhj@65o78k/s,PsW[s%&4ZT'0lc>iJ?g4#I"PWA0J])e.lWu6*n^="V8Q[XO9]I^fC`,
%!PIR:)b#;r78^XnkYjWKrV=LpcT`Dr!'D7=Yri8U)ap.ukR0S9/C1QY"8GNnci=(!'G(o<)$a9(4qIk?^"K,GNd/_N_8d)]-PPS2
%X"ii&iGLJWUIT1G_H,LqZ7'&2$C.,n(fk?[8HNjd!UQU1[4`g;$?q#`1H^Zt)3;Rp!0F'?4J?F<?mm^5cib%Z+?A(B?aU0H&tD3:
%M@WZ`5_-W`X,>5$5\.*UU(u>^+$l!Ies@_WKogoEi@/Fr?t9:nR_T#oE.LM;_pLHl8K^imV);2jBqV)k("fmfp^ENKC1>j$`RgPX
%`J9e>h#PlRA6]5t3<D&m$re+s6rBbW]JR['7VLU;e3WMspn.j%7%e>,;>b2[n0Kolp8*nb!=%J`!::'Ma!j@&Eg"@@HXf0#c>38L
%nBbi^i)FC?=TR6-fAOGdJM:(oa+@/fRK3\90*J-B'G+'tWmDDbM]s`#hIM=ORKl\m"uVNs3\C]L<iC`uTu]Y&X<KXCf2BuB805?3
%lKYp!#.b?HfJUQcaMS%N5Uq(Io0<a]'1(#<P<]+Qa<Eo#M%go;ZB4/Vje:'AJ\qKMa!b8da&PSa/>]q&BI:lP^a;%46SkVG[7/+D
%`Vl`0WnJRKXoj@"_C;67*h!84%%AZ*'9ji$"k/#:6\Yu%.VKa30(u:S^t/s%e^6>?e4/$s\3^NB_SIueXmhn#iIV9)3Je.LMLrI'
%%+Yih'tRN"7)?L*FY\:XXs/0^p!5k&E"rQbmc_t]fJf;uX\o,iO0#.3,p5696&F@p7AJNE?S,F^+k6pS0%2$N:Z'O2U-ZR$?UYC,
%i'*a@L[j,=[f%/R^eU4++99W<"TElN0g5+r~>
%AI9_PrivateDataEnd
