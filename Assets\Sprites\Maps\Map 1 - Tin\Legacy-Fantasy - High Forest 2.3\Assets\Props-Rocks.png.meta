fileFormatVersion: 2
guid: 54f51222447a01a4195013f39c513109
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -939405876487846061
    second: Props-Rocks_0
  - first:
      213: 6571697928574821366
    second: Props-Rocks_1
  - first:
      213: 7793740966012600076
    second: Props-Rocks_2
  - first:
      213: 3610508713234154058
    second: Props-Rocks_3
  - first:
      213: -7584760830311294885
    second: Props-Rocks_4
  - first:
      213: -6811012534584805612
    second: Props-Rocks_5
  - first:
      213: -4402797728445989921
    second: Props-Rocks_6
  - first:
      213: -6632212635511954823
    second: Props-Rocks_7
  - first:
      213: -402483502888120932
    second: Props-Rocks_8
  - first:
      213: 7030711752643726308
    second: Props-Rocks_9
  - first:
      213: -6807878441796686397
    second: Props-Rocks_10
  - first:
      213: 5945937871025120996
    second: Props-Rocks_11
  - first:
      213: -8369928339971208041
    second: Props-Rocks_12
  - first:
      213: 8089024887615916771
    second: Props-Rocks_13
  - first:
      213: -5563740420577874058
    second: Props-Rocks_14
  - first:
      213: 5088125881118415797
    second: Props-Rocks_15
  - first:
      213: 4513076207825023870
    second: Props-Rocks_16
  - first:
      213: -8787936616504875083
    second: Props-Rocks_17
  - first:
      213: -7283639985674808369
    second: Props-Rocks_18
  - first:
      213: -3091823448846933478
    second: Props-Rocks_19
  - first:
      213: -1771744001930188805
    second: Props-Rocks_20
  - first:
      213: -801494683660152859
    second: Props-Rocks_21
  - first:
      213: 6809272238038776385
    second: Props-Rocks_22
  - first:
      213: 7984596141705047025
    second: Props-Rocks_23
  - first:
      213: 1278213327448990072
    second: Props-Rocks_24
  - first:
      213: -5810452444641773239
    second: Props-Rocks_25
  - first:
      213: 3678164498894238160
    second: Props-Rocks_26
  - first:
      213: 5904821631525978501
    second: Props-Rocks_27
  - first:
      213: -5821484193359767415
    second: Props-Rocks_28
  - first:
      213: -6996342263466017695
    second: Props-Rocks_29
  - first:
      213: -4956607621314307353
    second: Props-Rocks_30
  - first:
      213: -1184155226362008034
    second: Props-Rocks_31
  - first:
      213: -8702782778686453470
    second: Props-Rocks_32
  - first:
      213: -4061433937583520119
    second: Props-Rocks_33
  - first:
      213: -184632978868446124
    second: Props-Rocks_34
  - first:
      213: -9149271350196656983
    second: Props-Rocks_35
  - first:
      213: 9076579558076801184
    second: Props-Rocks_36
  - first:
      213: -6182919529763732890
    second: Props-Rocks_37
  - first:
      213: 8246205008263265813
    second: Props-Rocks_38
  - first:
      213: 3550417123856436173
    second: Props-Rocks_39
  - first:
      213: -8783528139111429431
    second: Props-Rocks_40
  - first:
      213: 7083281764673778838
    second: Props-Rocks_41
  - first:
      213: -6157271060925380366
    second: Props-Rocks_42
  - first:
      213: -5301119066692687374
    second: Props-Rocks_43
  - first:
      213: 4485809810885253845
    second: Props-Rocks_44
  - first:
      213: 22048024436185565
    second: Props-Rocks_45
  - first:
      213: 5668470160793454758
    second: Props-Rocks_46
  - first:
      213: 3942509726533625591
    second: Props-Rocks_47
  - first:
      213: -1083080260565486353
    second: Props-Rocks_48
  - first:
      213: 1903081244008015751
    second: Props-Rocks_49
  - first:
      213: -6339422060650027661
    second: Props-Rocks_50
  - first:
      213: -3622390544667622577
    second: Props-Rocks_51
  - first:
      213: 21729607831684847
    second: Props-Rocks_52
  - first:
      213: 8880782214877474692
    second: Props-Rocks_53
  - first:
      213: -2686511157910046074
    second: Props-Rocks_54
  - first:
      213: 8154355977209846080
    second: Props-Rocks_55
  - first:
      213: 2825985303468940427
    second: Props-Rocks_56
  - first:
      213: -8948425624103346368
    second: Props-Rocks_57
  - first:
      213: -1698756923397755766
    second: Props-Rocks_58
  - first:
      213: 7096135494055890950
    second: Props-Rocks_59
  - first:
      213: -1076759390257479040
    second: Props-Rocks_60
  - first:
      213: -688672588183887865
    second: Props-Rocks_61
  - first:
      213: -4495624649984847687
    second: Props-Rocks_62
  - first:
      213: 8300198309917774254
    second: Props-Rocks_63
  - first:
      213: -4033139901744039735
    second: Props-Rocks_64
  - first:
      213: -3828361993502986755
    second: Props-Rocks_65
  - first:
      213: -3390858275524788601
    second: Props-Rocks_66
  - first:
      213: -239645053523353104
    second: Props-Rocks_67
  - first:
      213: -8503465053851432602
    second: Props-Rocks_68
  - first:
      213: -3874368013370373315
    second: Props-Rocks_69
  - first:
      213: -4680755941906857478
    second: Props-Rocks_70
  - first:
      213: -5622019844550643282
    second: Props-Rocks_71
  - first:
      213: -390496954808482000
    second: Props-Rocks_72
  - first:
      213: 7224954644122505045
    second: Props-Rocks_73
  - first:
      213: -3921536715034386471
    second: Props-Rocks_74
  - first:
      213: -395447364126998293
    second: Props-Rocks_75
  - first:
      213: -494877294728548765
    second: Props-Rocks_76
  - first:
      213: -2269292460338754552
    second: Props-Rocks_77
  - first:
      213: -7449554448114400373
    second: Props-Rocks_78
  - first:
      213: 1732146639080280920
    second: Props-Rocks_79
  - first:
      213: 7705901299431755323
    second: Props-Rocks_80
  - first:
      213: -2176303437048322752
    second: Props-Rocks_81
  - first:
      213: 5064878709198285637
    second: Props-Rocks_82
  - first:
      213: -3277126299215957648
    second: Props-Rocks_83
  - first:
      213: 2605018976114620781
    second: Props-Rocks_84
  - first:
      213: -8559848186546249623
    second: Props-Rocks_85
  - first:
      213: 8958113869861709554
    second: Props-Rocks_86
  - first:
      213: 8955136852293850789
    second: Props-Rocks_87
  - first:
      213: -3319403379376987962
    second: Props-Rocks_88
  - first:
      213: 5000371195054694135
    second: Props-Rocks_89
  - first:
      213: -3036252506605015645
    second: Props-Rocks_90
  - first:
      213: 5745988659066897542
    second: Props-Rocks_91
  - first:
      213: 1832517795323105343
    second: Props-Rocks_92
  - first:
      213: -5661152964717915243
    second: Props-Rocks_93
  - first:
      213: -4052082383877653483
    second: Props-Rocks_94
  - first:
      213: 8375416210258277303
    second: Props-Rocks_95
  - first:
      213: 6784209141202018179
    second: Props-Rocks_96
  - first:
      213: 4289642582530815277
    second: Props-Rocks_97
  - first:
      213: 833283696678040240
    second: Props-Rocks_98
  - first:
      213: -2270399671144379583
    second: Props-Rocks_99
  - first:
      213: 7926804014836312882
    second: Props-Rocks_100
  - first:
      213: 4232381465754340759
    second: Props-Rocks_101
  - first:
      213: -5791432986119999589
    second: Props-Rocks_102
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Props-Rocks_0
      rect:
        serializedVersion: 2
        x: 5
        y: 255
        width: 57
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3570700255f86f2f0800000000000000
      internalID: -939405876487846061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_1
      rect:
        serializedVersion: 2
        x: 63
        y: 255
        width: 65
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f36fdb927c533b50800000000000000
      internalID: 6571697928574821366
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_2
      rect:
        serializedVersion: 2
        x: 66
        y: 303
        width: 30
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0f37969d1ee82c60800000000000000
      internalID: 7793740966012600076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_3
      rect:
        serializedVersion: 2
        x: 127
        y: 303
        width: 18
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4246ce9b8a1b1230800000000000000
      internalID: 3610508713234154058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_4
      rect:
        serializedVersion: 2
        x: 145
        y: 320
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b589ffae5348db690800000000000000
      internalID: -7584760830311294885
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_5
      rect:
        serializedVersion: 2
        x: 160
        y: 320
        width: 17
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 417f653af2c6a71a0800000000000000
      internalID: -6811012534584805612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_6
      rect:
        serializedVersion: 2
        x: 180
        y: 322
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fdb74120bce16e2c0800000000000000
      internalID: -4402797728445989921
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_7
      rect:
        serializedVersion: 2
        x: 192
        y: 287
        width: 32
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 972e3793fb5a5f3a0800000000000000
      internalID: -6632212635511954823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_8
      rect:
        serializedVersion: 2
        x: 223
        y: 287
        width: 50
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9d0666f2671a6af0800000000000000
      internalID: -402483502888120932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_9
      rect:
        serializedVersion: 2
        x: 147
        y: 305
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4eb4e76b61b129160800000000000000
      internalID: 7030711752643726308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_10
      rect:
        serializedVersion: 2
        x: 160
        y: 304
        width: 14
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3cd2baa70ae8581a0800000000000000
      internalID: -6807878441796686397
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_11
      rect:
        serializedVersion: 2
        x: 175
        y: 287
        width: 18
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e6aef2adf6348250800000000000000
      internalID: 5945937871025120996
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_12
      rect:
        serializedVersion: 2
        x: 5
        y: 175
        width: 57
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79c8a84458a08db80800000000000000
      internalID: -8369928339971208041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_13
      rect:
        serializedVersion: 2
        x: 66
        y: 223
        width: 30
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ea04ad174df14070800000000000000
      internalID: 8089024887615916771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_14
      rect:
        serializedVersion: 2
        x: 131
        y: 240
        width: 46
        height: 63
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67f29f0c09f99c2b0800000000000000
      internalID: -5563740420577874058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_15
      rect:
        serializedVersion: 2
        x: 180
        y: 255
        width: 24
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bbd884e787ac9640800000000000000
      internalID: 5088125881118415797
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_16
      rect:
        serializedVersion: 2
        x: 211
        y: 255
        width: 61
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e737da356eaa1ae30800000000000000
      internalID: 4513076207825023870
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_17
      rect:
        serializedVersion: 2
        x: 63
        y: 175
        width: 65
        height: 79
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b72490353afa0680800000000000000
      internalID: -8787936616504875083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_18
      rect:
        serializedVersion: 2
        x: 127
        y: 223
        width: 18
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc3950ef5005bea90800000000000000
      internalID: -7283639985674808369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_19
      rect:
        serializedVersion: 2
        x: 180
        y: 242
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a12614754f2a715d0800000000000000
      internalID: -3091823448846933478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_20
      rect:
        serializedVersion: 2
        x: 192
        y: 207
        width: 32
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf7a504a7308967e0800000000000000
      internalID: -1771744001930188805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_21
      rect:
        serializedVersion: 2
        x: 223
        y: 207
        width: 50
        height: 45
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ef821be4d480e4f0800000000000000
      internalID: -801494683660152859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_22
      rect:
        serializedVersion: 2
        x: 147
        y: 225
        width: 11
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14afa3ff5056f7e50800000000000000
      internalID: 6809272238038776385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_23
      rect:
        serializedVersion: 2
        x: 160
        y: 224
        width: 14
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1fba84df2ebfece60800000000000000
      internalID: 7984596141705047025
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_24
      rect:
        serializedVersion: 2
        x: 175
        y: 207
        width: 18
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87164c1cf302db110800000000000000
      internalID: 1278213327448990072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_25
      rect:
        serializedVersion: 2
        x: 122
        y: 143
        width: 28
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 949b3d469402d5fa0800000000000000
      internalID: -5810452444641773239
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_26
      rect:
        serializedVersion: 2
        x: 131
        y: 175
        width: 46
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d13b0ebf177b0330800000000000000
      internalID: 3678164498894238160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_27
      rect:
        serializedVersion: 2
        x: 180
        y: 175
        width: 24
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58106212df322f150800000000000000
      internalID: 5904821631525978501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_28
      rect:
        serializedVersion: 2
        x: 211
        y: 175
        width: 61
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9885d3d38fee53fa0800000000000000
      internalID: -5821484193359767415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_29
      rect:
        serializedVersion: 2
        x: 3
        y: 64
        width: 57
        height: 107
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16c3e0397cff7ee90800000000000000
      internalID: -6996342263466017695
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_30
      rect:
        serializedVersion: 2
        x: 70
        y: 64
        width: 42
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7ea119031a7963bb0800000000000000
      internalID: -4956607621314307353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_31
      rect:
        serializedVersion: 2
        x: 116
        y: 41
        width: 42
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e1e25c57819019fe0800000000000000
      internalID: -1184155226362008034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_32
      rect:
        serializedVersion: 2
        x: 162
        y: 127
        width: 45
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 229c10f0c21893780800000000000000
      internalID: -8702782778686453470
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_33
      rect:
        serializedVersion: 2
        x: 210
        y: 127
        width: 27
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9864e972a53e2a7c0800000000000000
      internalID: -4061433937583520119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_34
      rect:
        serializedVersion: 2
        x: 213
        y: 143
        width: 22
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45c59b0b84d007df0800000000000000
      internalID: -184632978868446124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_35
      rect:
        serializedVersion: 2
        x: 242
        y: 143
        width: 44
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a4aed19e22470180800000000000000
      internalID: -9149271350196656983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_36
      rect:
        serializedVersion: 2
        x: 165
        y: 64
        width: 54
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a06dbea40d76fd70800000000000000
      internalID: 9076579558076801184
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_37
      rect:
        serializedVersion: 2
        x: 242
        y: 111
        width: 44
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6628b99327bd13aa0800000000000000
      internalID: -6182919529763732890
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_38
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51e3496c3c7607270800000000000000
      internalID: 8246205008263265813
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_39
      rect:
        serializedVersion: 2
        x: 17
        y: 48
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcb4def209d954130800000000000000
      internalID: 3550417123856436173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_40
      rect:
        serializedVersion: 2
        x: 33
        y: 32
        width: 14
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ca8d91d1b3aa1680800000000000000
      internalID: -8783528139111429431
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_41
      rect:
        serializedVersion: 2
        x: 47
        y: 50
        width: 9
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 694f1637c3fdc4260800000000000000
      internalID: 7083281764673778838
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_42
      rect:
        serializedVersion: 2
        x: 52
        y: 64
        width: 11
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f800cbb79afc8aa0800000000000000
      internalID: -6157271060925380366
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_43
      rect:
        serializedVersion: 2
        x: 63
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f96bbd9b44ae66b0800000000000000
      internalID: -5301119066692687374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_44
      rect:
        serializedVersion: 2
        x: 69
        y: 64
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5da17c3914cc04e30800000000000000
      internalID: 4485809810885253845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_45
      rect:
        serializedVersion: 2
        x: 82
        y: 48
        width: 13
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd99488bf845e4000800000000000000
      internalID: 22048024436185565
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_46
      rect:
        serializedVersion: 2
        x: 96
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a8cef114937aae40800000000000000
      internalID: 5668470160793454758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_47
      rect:
        serializedVersion: 2
        x: 112
        y: 48
        width: 15
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f62f8d35bb96b630800000000000000
      internalID: 3942509726533625591
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_48
      rect:
        serializedVersion: 2
        x: 144
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe4b1cdcd3028f0f0800000000000000
      internalID: -1083080260565486353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_49
      rect:
        serializedVersion: 2
        x: 161
        y: 64
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7876ec7fe4a196a10800000000000000
      internalID: 1903081244008015751
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_50
      rect:
        serializedVersion: 2
        x: 161
        y: 48
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3752ea93939d508a0800000000000000
      internalID: -6339422060650027661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_51
      rect:
        serializedVersion: 2
        x: 177
        y: 32
        width: 14
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f434a5bedfeaabdc0800000000000000
      internalID: -3622390544667622577
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_52
      rect:
        serializedVersion: 2
        x: 191
        y: 50
        width: 9
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fe68ae396f23d4000800000000000000
      internalID: 21729607831684847
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_53
      rect:
        serializedVersion: 2
        x: 207
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48fe06d3f50ee3b70800000000000000
      internalID: 8880782214877474692
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_54
      rect:
        serializedVersion: 2
        x: 213
        y: 64
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68af5abe55897bad0800000000000000
      internalID: -2686511157910046074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_55
      rect:
        serializedVersion: 2
        x: 226
        y: 48
        width: 13
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0491da1ce871a2170800000000000000
      internalID: 8154355977209846080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_56
      rect:
        serializedVersion: 2
        x: 227
        y: 64
        width: 10
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8c9db929aae73720800000000000000
      internalID: 2825985303468940427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_57
      rect:
        serializedVersion: 2
        x: 239
        y: 64
        width: 17
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04f11504e4ec0d380800000000000000
      internalID: -8948425624103346368
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_58
      rect:
        serializedVersion: 2
        x: 240
        y: 48
        width: 16
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8c242d149dcc68e0800000000000000
      internalID: -1698756923397755766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_59
      rect:
        serializedVersion: 2
        x: 256
        y: 41
        width: 26
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6001e3982a98a7260800000000000000
      internalID: 7096135494055890950
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_60
      rect:
        serializedVersion: 2
        x: 261
        y: 64
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 082032b1a059e01f0800000000000000
      internalID: -1076759390257479040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_61
      rect:
        serializedVersion: 2
        x: 2
        y: 32
        width: 12
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70020c60ee75176f0800000000000000
      internalID: -688672588183887865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_62
      rect:
        serializedVersion: 2
        x: 19
        y: 32
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9b0d92e13355c91c0800000000000000
      internalID: -4495624649984847687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_63
      rect:
        serializedVersion: 2
        x: 49
        y: 22
        width: 14
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea152f3f26a303370800000000000000
      internalID: 8300198309917774254
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_64
      rect:
        serializedVersion: 2
        x: 55
        y: 48
        width: 9
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c8b71bb0a86708c0800000000000000
      internalID: -4033139901744039735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_65
      rect:
        serializedVersion: 2
        x: 68
        y: 32
        width: 11
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfde5a9ec0deedac0800000000000000
      internalID: -3828361993502986755
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_66
      rect:
        serializedVersion: 2
        x: 73
        y: 22
        width: 29
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 78eb6ab2d5041f0d0800000000000000
      internalID: -3390858275524788601
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_67
      rect:
        serializedVersion: 2
        x: 95
        y: 32
        width: 17
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f1af80f71c9cacf0800000000000000
      internalID: -239645053523353104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_68
      rect:
        serializedVersion: 2
        x: 115
        y: 32
        width: 11
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 669cc39369f9df980800000000000000
      internalID: -8503465053851432602
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_69
      rect:
        serializedVersion: 2
        x: 146
        y: 32
        width: 12
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3b4ce1b1da7b3ac0800000000000000
      internalID: -3874368013370373315
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_70
      rect:
        serializedVersion: 2
        x: 163
        y: 32
        width: 9
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af1c752454d9a0fb0800000000000000
      internalID: -4680755941906857478
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_71
      rect:
        serializedVersion: 2
        x: 193
        y: 22
        width: 14
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea1bc55fcb29af1b0800000000000000
      internalID: -5622019844550643282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_72
      rect:
        serializedVersion: 2
        x: 199
        y: 48
        width: 9
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 037c4caa61da49af0800000000000000
      internalID: -390496954808482000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_73
      rect:
        serializedVersion: 2
        x: 212
        y: 32
        width: 11
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55f0b3928f1344460800000000000000
      internalID: 7224954644122505045
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_74
      rect:
        serializedVersion: 2
        x: 217
        y: 22
        width: 29
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9df19b68227e399c0800000000000000
      internalID: -3921536715034386471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_75
      rect:
        serializedVersion: 2
        x: 239
        y: 32
        width: 17
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be088dda7b6138af0800000000000000
      internalID: -395447364126998293
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_76
      rect:
        serializedVersion: 2
        x: 259
        y: 18
        width: 23
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 36e6c40f8b7d129f0800000000000000
      internalID: -494877294728548765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_77
      rect:
        serializedVersion: 2
        x: 0
        y: 12
        width: 7
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 80c4902d78ad180e0800000000000000
      internalID: -2269292460338754552
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_78
      rect:
        serializedVersion: 2
        x: 4
        y: 18
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b83c539e2bddd9890800000000000000
      internalID: -7449554448114400373
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_79
      rect:
        serializedVersion: 2
        x: 6
        y: 12
        width: 8
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8535441e032d90810800000000000000
      internalID: 1732146639080280920
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_80
      rect:
        serializedVersion: 2
        x: 13
        y: 12
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b3ec9da776cd0fa60800000000000000
      internalID: 7705901299431755323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_81
      rect:
        serializedVersion: 2
        x: 25
        y: 18
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 045e547fd873cc1e0800000000000000
      internalID: -2176303437048322752
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_82
      rect:
        serializedVersion: 2
        x: 30
        y: 12
        width: 7
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 54be334e9501a4640800000000000000
      internalID: 5064878709198285637
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_83
      rect:
        serializedVersion: 2
        x: 117
        y: 21
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 071742b6efe4582d0800000000000000
      internalID: -3277126299215957648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_84
      rect:
        serializedVersion: 2
        x: 134
        y: 12
        width: 17
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d69b670f4f2e62420800000000000000
      internalID: 2605018976114620781
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_85
      rect:
        serializedVersion: 2
        x: 148
        y: 18
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 960a3a52d6f453980800000000000000
      internalID: -8559848186546249623
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_86
      rect:
        serializedVersion: 2
        x: 150
        y: 12
        width: 8
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f66b9e3a1d915c70800000000000000
      internalID: 8958113869861709554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_87
      rect:
        serializedVersion: 2
        x: 157
        y: 12
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a61cf35589074c70800000000000000
      internalID: 8955136852293850789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_88
      rect:
        serializedVersion: 2
        x: 169
        y: 18
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c0e002a63c1fe1d0800000000000000
      internalID: -3319403379376987962
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_89
      rect:
        serializedVersion: 2
        x: 174
        y: 12
        width: 7
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fe945adb13e46540800000000000000
      internalID: 5000371195054694135
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_90
      rect:
        serializedVersion: 2
        x: 261
        y: 21
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ad2b679e601dd5d0800000000000000
      internalID: -3036252506605015645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_91
      rect:
        serializedVersion: 2
        x: 0
        y: 6
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6840b1bed3addbf40800000000000000
      internalID: 5745988659066897542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_92
      rect:
        serializedVersion: 2
        x: 10
        y: 6
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f30d0dac9396e6910800000000000000
      internalID: 1832517795323105343
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_93
      rect:
        serializedVersion: 2
        x: 16
        y: 6
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 593d6d66f5b8f61b0800000000000000
      internalID: -5661152964717915243
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_94
      rect:
        serializedVersion: 2
        x: 25
        y: 12
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51cba5c6a8c14c7c0800000000000000
      internalID: -4052082383877653483
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_95
      rect:
        serializedVersion: 2
        x: 26
        y: 6
        width: 13
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b33e7a1ba47b3470800000000000000
      internalID: 8375416210258277303
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_96
      rect:
        serializedVersion: 2
        x: 38
        y: 6
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38fdd47054a562e50800000000000000
      internalID: 6784209141202018179
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_97
      rect:
        serializedVersion: 2
        x: 144
        y: 6
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d257329833fd78b30800000000000000
      internalID: 4289642582530815277
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_98
      rect:
        serializedVersion: 2
        x: 154
        y: 6
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b27e917b1b609b00800000000000000
      internalID: 833283696678040240
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_99
      rect:
        serializedVersion: 2
        x: 160
        y: 6
        width: 11
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 14b8647378bed70e0800000000000000
      internalID: -2270399671144379583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_100
      rect:
        serializedVersion: 2
        x: 169
        y: 12
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23b6574e04aa10e60800000000000000
      internalID: 7926804014836312882
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_101
      rect:
        serializedVersion: 2
        x: 170
        y: 6
        width: 13
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79d0c3cd4807cba30800000000000000
      internalID: 4232381465754340759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Props-Rocks_102
      rect:
        serializedVersion: 2
        x: 182
        y: 6
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9f66d92262b0afa0800000000000000
      internalID: -5791432986119999589
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Props-Rocks_0: -939405876487846061
      Props-Rocks_1: 6571697928574821366
      Props-Rocks_10: -6807878441796686397
      Props-Rocks_100: 7926804014836312882
      Props-Rocks_101: 4232381465754340759
      Props-Rocks_102: -5791432986119999589
      Props-Rocks_11: 5945937871025120996
      Props-Rocks_12: -8369928339971208041
      Props-Rocks_13: 8089024887615916771
      Props-Rocks_14: -5563740420577874058
      Props-Rocks_15: 5088125881118415797
      Props-Rocks_16: 4513076207825023870
      Props-Rocks_17: -8787936616504875083
      Props-Rocks_18: -7283639985674808369
      Props-Rocks_19: -3091823448846933478
      Props-Rocks_2: 7793740966012600076
      Props-Rocks_20: -1771744001930188805
      Props-Rocks_21: -801494683660152859
      Props-Rocks_22: 6809272238038776385
      Props-Rocks_23: 7984596141705047025
      Props-Rocks_24: 1278213327448990072
      Props-Rocks_25: -5810452444641773239
      Props-Rocks_26: 3678164498894238160
      Props-Rocks_27: 5904821631525978501
      Props-Rocks_28: -5821484193359767415
      Props-Rocks_29: -6996342263466017695
      Props-Rocks_3: 3610508713234154058
      Props-Rocks_30: -4956607621314307353
      Props-Rocks_31: -1184155226362008034
      Props-Rocks_32: -8702782778686453470
      Props-Rocks_33: -4061433937583520119
      Props-Rocks_34: -184632978868446124
      Props-Rocks_35: -9149271350196656983
      Props-Rocks_36: 9076579558076801184
      Props-Rocks_37: -6182919529763732890
      Props-Rocks_38: 8246205008263265813
      Props-Rocks_39: 3550417123856436173
      Props-Rocks_4: -7584760830311294885
      Props-Rocks_40: -8783528139111429431
      Props-Rocks_41: 7083281764673778838
      Props-Rocks_42: -6157271060925380366
      Props-Rocks_43: -5301119066692687374
      Props-Rocks_44: 4485809810885253845
      Props-Rocks_45: 22048024436185565
      Props-Rocks_46: 5668470160793454758
      Props-Rocks_47: 3942509726533625591
      Props-Rocks_48: -1083080260565486353
      Props-Rocks_49: 1903081244008015751
      Props-Rocks_5: -6811012534584805612
      Props-Rocks_50: -6339422060650027661
      Props-Rocks_51: -3622390544667622577
      Props-Rocks_52: 21729607831684847
      Props-Rocks_53: 8880782214877474692
      Props-Rocks_54: -2686511157910046074
      Props-Rocks_55: 8154355977209846080
      Props-Rocks_56: 2825985303468940427
      Props-Rocks_57: -8948425624103346368
      Props-Rocks_58: -1698756923397755766
      Props-Rocks_59: 7096135494055890950
      Props-Rocks_6: -4402797728445989921
      Props-Rocks_60: -1076759390257479040
      Props-Rocks_61: -688672588183887865
      Props-Rocks_62: -4495624649984847687
      Props-Rocks_63: 8300198309917774254
      Props-Rocks_64: -4033139901744039735
      Props-Rocks_65: -3828361993502986755
      Props-Rocks_66: -3390858275524788601
      Props-Rocks_67: -239645053523353104
      Props-Rocks_68: -8503465053851432602
      Props-Rocks_69: -3874368013370373315
      Props-Rocks_7: -6632212635511954823
      Props-Rocks_70: -4680755941906857478
      Props-Rocks_71: -5622019844550643282
      Props-Rocks_72: -390496954808482000
      Props-Rocks_73: 7224954644122505045
      Props-Rocks_74: -3921536715034386471
      Props-Rocks_75: -395447364126998293
      Props-Rocks_76: -494877294728548765
      Props-Rocks_77: -2269292460338754552
      Props-Rocks_78: -7449554448114400373
      Props-Rocks_79: 1732146639080280920
      Props-Rocks_8: -402483502888120932
      Props-Rocks_80: 7705901299431755323
      Props-Rocks_81: -2176303437048322752
      Props-Rocks_82: 5064878709198285637
      Props-Rocks_83: -3277126299215957648
      Props-Rocks_84: 2605018976114620781
      Props-Rocks_85: -8559848186546249623
      Props-Rocks_86: 8958113869861709554
      Props-Rocks_87: 8955136852293850789
      Props-Rocks_88: -3319403379376987962
      Props-Rocks_89: 5000371195054694135
      Props-Rocks_9: 7030711752643726308
      Props-Rocks_90: -3036252506605015645
      Props-Rocks_91: 5745988659066897542
      Props-Rocks_92: 1832517795323105343
      Props-Rocks_93: -5661152964717915243
      Props-Rocks_94: -4052082383877653483
      Props-Rocks_95: 8375416210258277303
      Props-Rocks_96: 6784209141202018179
      Props-Rocks_97: 4289642582530815277
      Props-Rocks_98: 833283696678040240
      Props-Rocks_99: -2270399671144379583
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
