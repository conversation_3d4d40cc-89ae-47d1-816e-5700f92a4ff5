%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_01_Right Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 8 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:54:48+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:54:48+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:54:48+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FWK/mpN5sh/L7Wp&#xA;fKSu/mBYR9SWJectDIolMa0NXEXPjTetKYq+DtW82edLyaSPV9Y1G4mRisiXdzO7Ky7EESMSCKYE&#xA;qmi+RvNOuQ+vpdmLmNq0b1oUJI6ijupr7dckIE8nC1HaODCanKj7j+pMB+Wn5kWh5x6TcxltiYnQ&#xA;mnXfg9cPhy7modsaU/xj7XXGqfm15ejE019rmlxFqCb17uFCzDpzDBakDxwGJHNysOrxZDUJRkfI&#xA;vef+cWvzS86eY9Y1Ly7r13LqlrbWZvLe9uKvLGyypGY2l6sHElRyJPw7YHIfR2KHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXzX/zlhD+Vywo0xMXnxlR4BZopLw1&#xA;A/03dRx4g8D9vp1WuKXj35PXN2uqXtupb6qYRK3gJFcBPkSC33ZbiO7ou3YR4In+K/se+6HeXdxA&#xA;ROpISnCY/tD+OZILxOeAB2YP+dPk3zJ5itNNk0dPrK2bSetZhlRiZAvGQcioPHiR177d8rzQJ5O5&#xA;7A12HBKQybcVb/oeT/8AKpvzD/6s0n/IyH/mvKPCl3PTfy1pf54+39Tv+VTfmH/1ZpP+RkP/ADXj&#xA;4Uu5f5a0v88fb+pUXyn+aunfFBZ6nCVHEG3aQkBew9Jj4bYPDl3Nke1dMf8AKRREfnj86tD+1q+v&#xA;Wip+xcyXRQf7Gaqjr4ZExIcmGpxT+mUT7iGQaP8A85R/m9p5X19Qt9TRT9i8to6EeBaAQP8A8Ngb&#xA;3oXl/wD5zL3VPMXl3b9u40+b9UMw/wCZuFaes+Uvz6/K7zOyQ2espZ3j7Czvx9WkqegDP+7YnwRz&#xA;ih6CCCKjpirsVdirsVdirsVdirsVUnu7RJRC80aytQLGWAY16UBNd8VVcVdirsVdirsVdirsVdir&#xA;sVdir4J/Piz1K1/N7zH+kw5aa69aFiT8Vs6gw8WI6COi+1KdsCWb+RG8sppkUuhoDDyVrlHNZeY6&#xA;rLXv+HhmVCq2eJ7S8YzIy8+nd8HqlrdQXMQkhYFe47j2Iy552cDE0VbCxUpbmCIj1ZBHXoWNB95w&#xA;JESeStb3QpyidXXv9lx+NRiyjKUUdbTrO3BrJJvExgofw2yJFdXKxZBM0YCXu2Rsuh2zxc0LW7Uq&#xA;VcggfP8A28iJuXPs+BFi4+95558Hk3TNIuNU8wabBewxME2gjlkZ3NAEZqUr48hhnQFlp0A1E8ox&#xA;4pkH3mmDaB5I/KHz9byvor3GjX8IBmtSxBWuwbixnUpX+Uj3plQjGXJ32XWazSEDJKMgepG3zH6Q&#xA;k3mT/nHLznp0TXOkSRazajcCIiOansjEqf8AgvoyBx9zscHbEZD1jh8x6h9m/wBiV+S/zg/Mr8vL&#xA;0WMVxK1nAQs2iaiHaJQP2VVqPF/sCPeuV8nbQnGYuJsPrX8qvzk8sfmJYMbGtnrFuoa90mVgZEB2&#xA;5xsKCSOu3ICo7gVGLJnuKuxV2KuxV2KvmP8A5yZ/O7UrbUZfI/lq6a1EKga3fQmkjM4DC2jcbqAp&#xA;HMg1J+HsaqXzhBpuoXQ5xRNJXetRU/ecaaJ6nHE0Smdi3nbRqS6dNf2JXcPaSSx071rEdsaRHVYj&#xA;ykHpf5af85MeeND1m1tvM98+saA7iO7Fwoa5hUmhlSUAOxTqVcmvTY74HIfZVvPDcQR3EDrLBMqy&#xA;RSIaqyMKqwI6gg4UMd/MTz7o/kXyvca/qgaSONlit7aOgkmmk+zGtdhsCxPYA4q+Y9b/AOcvfzCu&#xA;5m/RVhp+mW9fgVkkuJR/rOzKh+iMYEqeif8AOXH5jWt/HJqtvZajY8h68AiMEnHv6ciNRW/1lbFX&#xA;1N/jry3/AIK/xn9Z/wBwP1T6961Pi9PjXjxr/eV+Hj/NthQn+KuxV2KvPvze/JvQvzF0tBM31LXL&#xA;RSNP1NRUgHf0pV/bjJ3p1B3HcFV8ceYPLXnn8tPMZtdQieyulqYpkq9tcxg/aRqcZEPgdx3AOIJD&#xA;VnwQyx4ZCw9C8l/mDbazxi5fU9VUfFCCQHA6mM9/9U7/ADzIhkt5PtDsyWHf6ofjmz2z8x3CELcq&#xA;JE7uNmH8Dlok6SemB5MgR4biEOtHikFRXcEH2yThkEFK77QlqZrFjDMN+ANAfke2Ahvhn6S3Chp3&#xA;mbUbCYxzs2x4sejinj2b6cHvcgQI3gaZZBrwuoVMqR3MLdG6H+zHh7kHWy+nJESeD/8AOQvm+2mv&#xA;YPLOmswggC3OoBqV9VhWOOvgqHl9I8MozSPJ6bsHRQAOcXvsL+1575D8xz+VfOGn6nIpWKN1W8ic&#xA;GjW8oAeq9/hbkvuAcqiaLutZgGbEY0Jd3vH4p9jRazbSRqqs1qKfCYwrpv8ARmXwvDR1sSK3h7gC&#xA;PuYn518iaJ5ls2XUo0ua19G+ios0TN3BG67/ALJ+E4mIlsWOHVZtNLjhLiiefcfeOj5uguda/LX8&#xA;xEns5w19otwrB0NFmiZQxRhvtJE/Fh2rmJKNGnvdHqRnxRyDbifoNbzLPBHMgIWVVdQetGFRXA5C&#xA;/FXYq7FXYq/Nq/vrrW9eutQuSXudRuZLiYk1JeZy7bn3OBhmlwxJeheWvL11IqcYq/Sv9csAeV1O&#xA;oA6s8tfKN/6PL6sxFP2aN+quGnVy1cb5vJ/zU0Qadq1vN6Zja6RhIpFKtGQK0+TDISD1HYuo48ZH&#xA;839L66/5x51afVPye8uzTsXlgiltSTX7NtO8UY38I0XA7l5J/wA5m6tObryzpCkiBUubuQdmdika&#xA;f8CA334pDyfyL5Wt7+2SeSATPISd15UANMkA8/2lrJQmQDQCr+ZnlBNJsLS+jt/QDS+ixC8QeSlh&#xA;27cDjIL2PrTknKJN7Wm/+Lr3/oWb9Cc24f4i+qU/Z+rej9c4f8j/AIsg9A+3MKHYq7FXYqk/mvyh&#xA;5d816PLpGvWaXlnJuA2zo1NnjcfEjDxGKvhr81vIR/LvzvJpVlqSXkaBbmznR1+sRKxPFJ1X7Ei0&#xA;+kUbatAFlESFHk9K8t6lJqmhWV9IKSzxAyACg5j4WoPcjMuJsPCavCMeWURyBegaTE1tpsazfARV&#xA;mrtQEk71y0OmynilsufWNNQ0Nwp+VW/UDjaBhn3JHrtzY3E0b255OARIwBAPh1yJcvBGURupaTqb&#xA;2U1GqYHPxr4f5QxBZZsXEPN4L+Y5C/mDqzzMLiM3Ik+FtmjZVZVqK/sEDMTJ9Re37K30sK29P2su&#xA;/Pfyxbwy6P5rsVCWetW6pLGKArNCoCnj4NFxG38vvkso3tx+w8h8LgPOJP3ll/5OfmDDremRaFdk&#xA;jVtPhorEfDLBHRVav8yggMD16+NLsU7FPPdu9mHDM5Y/RI/Ipx+avm3VPLHlj65psYNzcTLbidhy&#xA;WLkGbmV7n4aCu1fuyWWRA2cXsbRQ1GbhmdgLrvfNsWoLNrCahqyPqCPOs19GZPTedeXKRfVo/EuN&#xA;uXE08MwyX0OEBGIjEUA+1fyx/wCchvJXne9j0dIpNH1hwfQsrgqY5eI+zDKtAxA/ZKqfCuKXqmKu&#xA;xV2KuxV+d3njy3deTvPWp6NLGyjT7pxb8tuduW5Qv/s4yDgY5IcUSO96N5K1m1lhjkjcMNq+IPgc&#xA;sBeM12CUSQQ9S0/XYkgpyHTJ26LJgJL58/N7zbB5i8zhbNhJZ2CehFIu4dyayMvtWij5ZVI2912H&#xA;ojgw+r6pb/qfZn5P+Wbjyz+Wnl/RrpSl1BbercxmtUluHa4kQ17q0pGB3Dxz/nMry7cSWfl7zDEh&#xA;aC3eaxu3H7JlCyQ/R8Em/wAsUh5f+VnnSy06yFpdTJC0DHgXYKCrHl1PgTkol5jtnQSnPiiLtb+c&#xA;vn601+Kx0yymWeKBzPPIhDJz4lEAI2JALVxkWfYPZ0sJlOQonYMi/wCVdah/0Kz+lvSP1j9Lfpvh&#xA;Q8/qnD6l0p9n/dv+rvkHpH2HhQ7FXYq4kAVPTFXzJ+df/OTkiyXHl3yFOABWO819NyT+0tp+r1f+&#xA;B7NgS8Q8teSNZ8xzm+u3eGzkYvLeS1aSUk1Ypy3Yn+Y7fPJxgS63W9pww7D1T7v1vetB03T9I0uK&#xA;dkpDAojs4f8AV2B377fxzLAoPD6jLLLMjqebFPO/5j2mlnhOxuLwisVjGaBQehc78f15XPJTs+z+&#xA;ypZdxtHveV6n+YPmvU5SsVw1qjbJBagof+CHxk/TlByEvTYey8GMcuI+f4pB/XfOkP7/ANfUU7+o&#xA;WnA8dycFycg6bCduGPyCOt/zJ82Q2stu1ysxdSqTSIDIle6sKb/61cl4hcafZOAyBqvuRP5eeR7j&#xA;zRrEc17zGlrLW6l3Lyt1KKeu/wC03bHHDiLX2p2iNNjqP11t5fjo9T/P3yzPf+U9M1PT1Ji0JpVv&#xA;LZf2YbgoFlVRtxQxhW+fgMszR6uq9ndVCjA/UXl/5beddG8pzXl9c2UlzqMqCK2kUjisZNXBBK0q&#xA;VG+/0d68cxF2naugyakRjGQEBuWbf8r60G/R7PWdDabT5dpEBjlBHXeOSinf3y3xgeYdP/ocyw9W&#xA;PJUh7x9oTvRfI/5Pea7N7vSbRTSglWOaeOSInoGiL0X2+Gh7ZIQhLk4mo7Q1+mlw5D9go/GnkfnX&#xA;Q5PJfnV7fTLmQGzeK70+5JHqodpENRtyRx19q5jzjRp6vszWHUYRMij1ff2g6l+lNC07Uqcfr1rD&#xA;c8R29aNXp/w2Rc5HYq7FXYq8r/O78jrH8w7SK9spUsfMtmnp2904PpTRVJ9GbiC1ASSrAGm+xrir&#xA;5j1X8g/zh0a4YfoC4nAJCT2LpcBh0qPSYuP9kAcCkA80F/yqz85JB6Z8u6wVf4SrRTBTXbeu1MWA&#xA;xQ7g9h/JD/nGjVbTWLbzJ53gSBLNhLY6MWWR2lXdJLgqWUKp+IJWpP2qdCWx9OYoSrzV5Y0jzR5f&#xA;vdB1aL1bC+j9OUDZlIPJHQ70ZGAZT4jFXydrf/OJP5j2upTRaVNZ6jp4JNvctL6Dle3ONgeLfJiP&#xA;fAlOvJf/ADiF5hfUre483X9rDpiMrz2Vm8ks8gG5jLlY0SvQspb2xW31D+idM/RX6I+rR/ov0Pqn&#xA;1PiPS9Dh6fpcenHh8NPDChF4q7FXYq8K/wCcrvzDvtA8sWflzTZTDda/6n1uZCQy2kXEMgI6eqz0&#xA;/wBUMO+Kvnv8u/I8OpKNX1JOdmrUtoD0kZTuzf5IO1O5/GzHC9y6TtXtE4/3cPq6nu/a9XVVVQqg&#xA;BQKADYADMh5glj/5ieexpGnQWtuQb9ouFumxCDo0rD59PHIZJ0HP7L7O8WZkfpvf9TyTRtEvtdvJ&#xA;JpZG9Llyubp/iJY7kCvVjmPGNvYEiAoPRNH0K0tAtvYwBWP2nO7H3ZjlwjTRKRPNk9rpVvCAXHqS&#xA;dyen0DJ01ksH/NbQk+q22qW0Cr6bNHduigEh6cGYjwII+nKsserfgl0X/lh+ZOnaFaLYagfRERb0&#xA;puJZGR25FW4gsDy70w48gGxdJ2v2TPNLjhvbMPOv5xeU38tX9jp8rXl9fW8tqIlUhFEyFGZnIAoA&#xA;3Qb/ACyc8op1nZ3YuojmjOXpjE3+x5T5A8qWnmDU5E1Bp49Ot05TSW/EOWYgKgLhlFRU9D0yiEbe&#xA;zyT4Xqy/kh5A1ayaLSry8tNQVSUaZkkqfF04gMP9UjJHG1xy28rsLvX/AMu/OrJMONzYyeneQKTw&#xA;mhahIBPVXUhlJG2xyMZGJatbpIanEYH4eRXfmPq8fmPz1d3GnN68Mxhgs+O/KkaqKV8XrjklZaey&#xA;NOcOnjGX1b38339o+nrp2kWOnqQVs7eK3UjYUiQJt92Rdii8VdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVfLf/ADmLc+W7i90RINQjfzBYiWO705Ksy28wV0d2HwoQy7K25DV6YEh5f+V/mnUBfQ6D&#xA;KPWs5BI0LU+KIqC53/lPv3y7FLo6HtjRw4TlG0tvj0enXVzDa20tzO3GGFGkkbwVRU5cXm4QMiIj&#xA;mXg9zcX3mbzE0rmkt3JsOojjHQfJVGYv1F7rBhjhxiI6PQtPgsbKJNPtmUNCoJjqOdD+0w67nLht&#xA;swJJ3ZZp1mLeAEj96+7nw9smGslF4UKc8ENxC8E6LJDIpWSNhUEHqCMVBYDq35SW0sxk0y8NujGv&#xA;oSqXA/1XBBp8wfnlRxdzfHP3oWy/KCcyA3uoKIx1WFCWP+yalPuOAYknP3Bn2j6Np+kWS2djF6cQ&#xA;3YndmbuzHuctApolIk7plb3EtvOk8LcZIzVThQGM/wDOQOgw3mnaV5utloXAtLwDwNXjJp/KwZSf&#xA;lmPMOXjkxX/nH/yq3mP81dFgaPna6fJ+krvaoCWtHTkPBpeC/Tlba+8cKHYq7FXYq7FXYq7FXYq7&#xA;FXYq7FXYq7FXYq7FWM/mZ5nn8reQdc162AN1Y2zNbchUCZyI4iR3AdwSMVfCXl3RdQ836/M95dO7&#xA;uWub+7kYvK5ZtzVurMT1OGEbLh67WDBDiqyeT13Q/LGi6LHxsLcJIRxedvilYe7H9Q2zJjEB5LUa&#xA;zJmPrPw6JR+Z1+1r5UmRTRruRIAR4El2+8IRkch2crsfHxZwf5ot5h5f1SHSPUvXh9aWQiGJa8QF&#xA;FGkNaHf7NMoiaetnG9noHl/QUPmObVvVLrcopWOlOKgKd9991GXRjvbjyltTN8sanYq7FXYq7FXY&#xA;q7FVfzRarqX5T69bMOTWiGdPb0Ss1R/wLZVkDdiKS/8AOHF1GvnnW7UqDJLphlV6bgRXESkV9/VG&#xA;UOUX1xhQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqlXmvy7aeZPLWp6DdkrBqVvJbs4FShdaK4&#xA;HirUYYq+CdV0vzX+W/nCaxvYjBf2pKkMCYbiFjs6HbnG9Kg+PgRjE0WnU6eOaHDJ6FoP5ieXtURV&#xA;lmFjdH7UM5AWv+TIaKfwPtmRHIC8rqeysuM7Dij5fqSX83pQ+k6cUYNG8zMCCCDRNjX6cjl5OX2F&#xA;Gskr7mMaFo+m3fl43d4H42csszBCAWUIpZT/AMDkIgU9DKREtnovlG5jvbP66iFFkReKnqASf6Zd&#xA;E248xRpkGSYOxV2KuxV2KuxV2KplEol8m+aYXNEawmBI60aCUHITbcXNg/8AziR/5NSX/tmXH/Jy&#xA;LMZyy+y8KHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqxrzz+XXlPzvpn1DzBZCcJU29yh4XE&#xA;LHvFINx7g7HuMVfMn5jf84r6z5cs7zWdI1i1u9FtEaaUXzfVp40G9C28T+FarU9FwJeGerL6Xpc2&#xA;9IHkI6njy6Vp44o4Rd9Wd+QpEk0eeFgDxmbkp6FWVev45dj5NGXmz3QAiRyxoAqrx4qNgBuNhloa&#xA;ZJthYuxV2KuxV2KuxV2Kq97cLb+Q/NsjdGsHj+mWOSMfi2V5OTbi5sZ/5xBtWl/My+m3CW+kzEnt&#xA;Vp4FA/En6Mx3LL7EwodirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVeM/wDOWFprVx+VobT1&#xA;drWC/gl1RUBJ+rhJAC1P2BMUJ260PbFXx5aXGnJYXkM9uz3cwT6rcBto+LAsClN+Q2rXbEU1zjMy&#xA;iQfSOY7088g3wi1Ca0Y0FylU/wBeOpp/wJOTxndco2t6Np12La45N/dsKN/XLg45DIEdHUMjBlPQ&#xA;jfJMF2KuxV2KuxV2KuxVJ/zE1JbH8tdRirSTU7q3tU8aIfWb6KJT6cqylvwDdkX/ADhnorf87Lrb&#xA;r8P+j2UD+J+OWUf8m8pckvpvFDsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiq2WKKaJ4pUW&#xA;SKRSkkbgMrKwoQQdiCMVePecP+cWfy212WS605ZtAu3JJFkVNuWPjA4IUe0bKMVfNX5k/lN5v/Lj&#xA;WSZ0kuNMDg2GuRRlYZPANQv6T9ijH5VG+KeaWWPn+8jAW8gWcD/diHg3zI3U/hkxkajiHRkemefN&#xA;JLArO1s56pKtAfmRyXJiYaziLNdM1SK9jBUjkRyBU1Vge6nLAWkikdhQ7FXYq7FULqOqadptubi+&#xA;uEt4h0ZzuT4KOrH2GAkBIiTyeU/mD50i8wyWdnYq66fZc2UuKNJNKQGfiCdgqKq9+vjmPOVly8cO&#xA;EPsb8ivJMvk/8ttM066jMWpXQa+1FCKFZrih4MPGOMIh9xkWbP8AFXYq7FXYq7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FVk8EFxC8M8aywyArJE4DKynqGU7EYq8080f844/lRr5eT9FHSrqSpN&#xA;xpr/AFehO+0VHg/5J4q8I/N//nGqXyT5dn8yaVqr6lp1tJGtxbSwhJYo5G4B+asVcByoPwjrgSw3&#xA;8stW4yNYu28b84wf5H+Fh9B3+nLsZaM0er0G+1VIgY4SGk6FuoX+3LSXHAdo9yZInjc1dTyqepDf&#xA;24hSrXuoxWxVac3PVR2GJKgKWratDY6Nc6kCGWGJnSvQtSir9LUGJNC1jGzTynyj5Q84fmP5lOna&#xA;Z/pd+yGe4nuJOEcUQYKXYmtFDOBxUE+AzFJtzgAH09+WH/OL/lrytd22sa9cfprWrZhLBHx4WkMi&#xA;7qyofikZT0Lbf5NcVe24q7FXYq7FXYqk3mjzn5W8q2kV35h1KHToJn9OEzE1du4VVBZqd6DbFUVo&#xA;+v6Hrdqt3o+oW+oWzAES20qSrv48SaH2OKo/FXYq7FXYq7FUh8zeffJnldFbX9YttPZ6cIpXBlav&#xA;cRLykI9+OKoz/E3l79Af4h/SNv8AoP0vrH6R9RfQ9P8Am59Pb57dcVTLFXYq7FXYq7FXYq7FXYqg&#xA;dc0ax1vRr7R79PUstQgktrhe/CVSpI8CK1B7HFX59a7pGreRfOl7pVyK3elztE1QQssZ3Rx/kyRk&#xA;MPniDSyFhB3fmLXr2Rp/XkRIviKwckRASACePuaVbCZFAgAyDQfzFe0Q/Xoy8yKQkkYHx7dGG1D7&#xA;5OOTvapYe5KNQ80+YNZvQls0qM7fuoLYsZGJ8SvxMciZktkcYCFm8z6zPpDaVPOZbZ3V2L1Lnj0U&#xA;t3Fd9/DBxGqSIC7fUf8AziJ5MOneUr7zRcJS41qX0bQkbi2tiVJH+vKWr/qjAye+4odirsVdirsV&#xA;diryj8+vyXvfzHtNNm03UI7PUtKEwhiuA3oSrPwJDMgZkIMY3CnFXzXqP5C/nP5fujJDotzIybJd&#xA;abKsxIrSq+k3qD6VGBKilj/zkPEhhS383RpJQGMJqYDcdwKAb0xVN/Jnkn/nIG981aZepba3aTw3&#xA;EbG/1Fp4VjQMA5c3BXkvEbrQ8htQ4q+2sKGPfmHY+Y7/AMkazZ+W5jb65PbOljKG4NzPUK9RwZlq&#xA;qtXY74q+Lm/Ln89rdntxpGuAVPIR+syEt1+JCVP34Epp5f8A+cZ/zc1q4DXenppUDtWS6v5kB3Px&#xA;H04zJKT8138cVfSf/KlrX/lS3/Ktf0lJ/d1/SPE0+sfWPrVfS5f3fq7ca9P8rfCh6XirsVdirsVd&#xA;irsVdirsVdirzv8AMP8AInyL581e31fWBdW99AixSS2ciR+tGpJVZQ6SVpUiq0NO/TFWSeX/AMv/&#xA;ACZ5f0WXRdJ0i2t9NuF4XUBQSeupFD67Scml22+MnFXjPnX/AJxD0XUtX+u+WNU/Q1pMxa4sJojc&#xA;Rxk9TAQyMB/kMfpptim3p35b/k75M8g2o/Rdt9Y1Rl43GrXADXD16hTSkaf5KfTXrihL/On/ADj7&#xA;+Wnm3U/0pe2MllfueVxNYOIPWNa1kXiyEnuwAY9zirPtK0vT9J0220zToFtrCzjWG2gSvFI0FFG9&#xA;Sfmd8VRWKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVB6xrWk6Lp02patdxWNhbjlNczs&#xA;ERew3PcnYDvirw3zR/zmB5QsJ3g8v6Vc6zwJH1mVxZwt7pyWWQj/AFkXFNMWH/OZ2seqSfK9v6VN&#xA;l+tPyr48vTp+GBaZJoP/ADmN5VuZEj1vQ7vTQxoZbeRLtF92qIHp8lOFae0+VfOvlXzZYfXvL2pQ&#xA;6hbinqemSJIyegkjYLJGfZlGKE6xV2KuJAFT0xV8s/mv/wA5U60ur3Gk+RGit7K1cxtrLok8kzqa&#xA;FoVcNGI69CVJbrtgSl35b/8AOVnm221q3tPOkkWpaRcOI5r5Yo4J7fkaCSkISNkX9oca06HsVX1s&#xA;jq6h0IZWAKsDUEHoQcKG8VQGva5pmg6Neaxqkwt9PsYmmuJT2VewHck7Adztir5F84/85XfmJqWp&#xA;yN5bePQtMRiLeP0Ybid0HQyvOsiVPWiKKdN+pCXon5Ef85Han5m1uLyv5tWI6hdBv0dqUSiISOoL&#xA;GKVB8AZgDxZaeFKnCr6FxQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVC/vrTT7G5v72UQWdpE89z&#xA;M32UiiUu7GnZVBOKvhP84/zd1f8AMPXWlJe28v2jsNK08noOnqy02Mrjr/KNh4kJY7p/l23aFJbh&#xA;zIXAYKhotDuN+pyYixMkf+gtKpT0P+Gf+uHhCLKT6nZaLBVY5mWYf7rX4x9PSn35EgJBK3yz5p17&#xA;yxrEOr6HeSWV9AdpEOzLWpSRT8Loabq22RZPub8nfzRsfzD8qrqKqsGq2hEOrWanaOWlQ6VJPpyD&#xA;da+47YUM6xV59+ffml/Lf5V61dwyeneXcYsLVhsed0fTYr7rHzYfLFXyp+R3lO01nzBcahfRCa10&#xA;tFZI3FVM8hPpkg9eIVj86ZbhjZdB7Q62WLEIRNSn9w5qP56aZZWHnZGtYliF5aR3EyoAoMheSMmg&#xA;8RGK4MwqTP2dzSnp/Ub4ZEfYD+l9kflRdz3f5Z+Vp525yvpdpzbuSsKrU+5plbvGVYq+e/8AnMLz&#xA;U9p5Z0jy1C1G1Sdrq6AP+6bUAKrezSSBv9jikMB/InyfYfoSbX723Sa4vHeG19RQwWFPhYrXu78g&#xA;fYfPMjDDa3jfaLXS8QYomhHc+954Il0X824k0/8AcrY63GbULtwCXIKgU7DplExRL0+hyGeCEjzM&#xA;Q/QXA5TsVdirsVdirsVdirsVdirsVdirsVdiriQBU9MVfM//ADkP+fXlfVfLF35Q8rXbXt1dTpFq&#xA;F5Eh+r+hGS7pFLUcy0ioKqCpWu+KXh3mKE2Gl6V5ZiH+lMReah4+tKKIp/1F6/RkRuWR2COgiWGF&#xA;Il+zGoUfQKZc0oq7smGlzTxMfWELOg9wtRhPJQxewNnZ6A+p/V0ur9rr0FMw5xxKIw4b0z8LM5r9&#xA;qo2yhtW6lONV0oaiYYory1kEF36KCNXjkFYnKr8NQVZSR7Yqz3/nGXzc2gfmjZWkkvCx1xW0+dST&#xA;xMj/ABW5p/N6qhB/rHCh9vYUPnD/AJzM1lk0jy3oqk8bi4uLyRe1YEWND/yXbFISL8h9MW18km7p&#xA;8d/cyycv8mOkQH0FGzKwj0vB+0eXi1HD/NiP1vPvz8n9TzxElQfRsYk+VXkf/jfKs/1O99m41pj5&#xA;yP6H2L+Vlt9W/LTypCQVZdJsi6nqGa3RmH3nKnfMoxV8Xf8AOV+ryXv5ryWZJ9PS7K3t0U7Csim4&#xA;JHz9an0YEvUvJmmLpnlPSbEDi0NrF6gH+/GUNIfpdjmfAUA+Ya/N4mecu+R/Y+fin6R/OdIlqRce&#xA;YUiXh1IN4EFPc5hT+ovofZ8a0+Mf0I/c/QDA5bsVdirsVeYecP8AnI/8rvLF1JZvfSarexErLBpq&#xA;CcIw2oZWaOGo7gOSO+Ksb07/AJy+/Li4uBFeWGp2SMQBO0UUiDxLCOUv9ynFaeveW/NXlzzNpy6l&#xA;oOoQ6jZtsZIWqValeMiGjI3+SwBxVNcVdirsVdirsVdiryf/AJyV8+f4X/Lm4s7aXhqmvE2FsAfi&#xA;WFhW5kHyjPCo6Fhir460mOy0zzDanWARBBwnkRRyNTGJEUj5kA5EsgpnVLm98w/pCTeW5nqV60Vj&#xA;x4j5LsMMUSZYkbvXgK8RU08Mua03tN7aOvhhQ8/1EHS59T0wpWC44NFX9kq4dGHyQsv05TIbtoOy&#xA;3UdHfT9K02+ScumqRyFkC8QvpuAVO55b0PTI2mkFpt/c6dqNrqFq3C6s5o7iB/CSJg6n6CMKH6Oe&#xA;XNbtdd0DTtatf959StorqIdwJUD8T7itDhQ8z/5yR/K7UfO/lW2u9HQzazobSy29qOs0MwX1o1/y&#xA;/wB2rL40p3xV8p+XPzB84eT0uNMtiEQOfUsruMn0pBs1FJVkPiMlHIYuu1vZWHUESmPV3hV0Hy95&#xA;2/NHzeI7eN7u9uXQXl7wCwW0P2echUBVVFGw6t0FTkSSTZczT6eGGAhAVEPvzTrGGw0+1sYP7m0i&#xA;jgi/1Y1Cr+AxbURir5i/5yp/KbWbrU1886NbveQNCkOsQRAvJEYhRJwo3KcKK38tK9DspeOaX+cH&#xA;nfTdITTILmNo4k9OCeWMPKijoAx2NO3IHJjLICnUZuw9NkycZB35i9mVf848/l35i8z/AJgaf5km&#xA;tn/Qul3X1271GVaJJPGS6JGT9t/VoWp9kde1YO2EQBQ5PtbFXYq7FXzH/wA5Q/nLfQ3knkTy/ctA&#xA;qIP07dRMQ7GRai1DDovAgyUO9eP8wIS8y8hfknqOv2cWqarcHTtOmAe3jVeU8qHowrsinsTWvhTf&#xA;ASmmV6r/AM466M1s36J1S5iuQPh+tBJY2PgfTWIr898HEtPPfLvmLzp+VHnT1oeUF1AwW8s2J9C6&#xA;gr0PZkYfZbqD75IFD7q8p+ZtN80eXNP1/TGLWWoRCWMH7Sn7Lxt/lI4Kt7jChCefvOul+S/Kt95h&#xA;1H4orVaQwAgNNM+0cS17s3XwFT2xV8T+YfzE/ND8xdakBuru451aLSbAyJbxINtokNNq0LvU+JwJ&#xA;Tb8ufzv89+QddW01We6vtIST09Q0i9Z2kjHcwmWrRuvWnQ9/EKvs/wDxPoX+Gf8AE31tf0J9U+v/&#xA;AFzfj9X4epyp1+z269sKE0xV8gf85eX13N+ZOm2YmWWG202JobdDy4SSzSl+Sb0dwqfNeOBIeP69&#xA;olxpb2xvZxJfXSGa5t6kyRVO3qMa7sN8AKSERpiw3Wp3GoLGsFrET6MY2VB+yP8AYr1+/JxDGRTj&#xA;S/M2nfXvqtG/esESbbjXt70JyQkxMVvmHzasBe0sCGmHwyT9Qp8F8TjKSiLGomkv47prqV5ZoIfU&#xA;id2JOzqCtTXajE5DmyTjWP3vkTQZOpgmuYiev2n5fRsMgObM8nWtjp9h/iPRNSMSXUKE2d1IOLF4&#xA;HqFQtv8AvRQj2wlAfVP/ADijr13qf5WC0uAxGkXs1nBIejRsFnAr/kmYj2FMkxey4qlGreTvKOsz&#xA;evq+h6fqU+w9W7tYZ32FB8UiscVR2n6Zpum2y2unWkNlarusFvGsUY7bKgUYqicVdirsVSG58g+R&#xA;Lq6+t3PlzS57uvL6xJZW7ycq1rzZC1a4qnkUUUUaxRIscSAKiKAqqBsAANgMVXYq7FXYq/PbRVbz&#xA;n+ZcUuoEv+l9QkubsN+0rM0zr7VUccBS+xtI8pTXESSzt9XgIHBAPiK9tuijKrZ0mz+TNMKUWSVW&#xA;7MSp+8ccbWngv/OTnkaS00Kx1viJGtrgW5uFFKxTKTxb5Oop8zkolBDK/wDnDzWprnyTq+kyNyGn&#xA;XwkhBr8KXMYPEdqc42P05YwKR/8AOZmuTLF5b0JGIhcz31wm9CycYoj9HKTFIX/849/l/dr5FTWY&#xA;okWbVpHkMrmjGOJ2iRB12BVm+nK5FkAw/wD5yT8p/UZdO1h4fRunY2l0QPtjiXiao2NArCv9MMSg&#xA;qf8Ajq9/6FX/AET6x9T9N/oilfj+rcPr/wA+PL4Plt0ySH0z+bPnz/A3ka/19IRcXUfCGzhb7Bnl&#xA;PFC9P2V+0fGlMKHxguoTRGfzr5hm/SGu6nI8thFKQS0hNDO4HRU6KoG3btSB3ZjZiOoyXst5JNfM&#xA;zXUpEkpb7VXHIV8Nj07ZJir3lyYbSPT4tgo5XJHdzvx/2PTCSgJhfeSNesvL1vrk0Q+qz7vGKmSN&#xA;D9h5BTYN/t9ckYGrcWGtxyyHGOY+1OfJP5eHV9G1LXdSLxWFpbzPaxj4TNKkZYGv8ikb069MlDHY&#xA;suHr+0vCnHHDeUiL8hf3sZ0JPUmuo60DWswJ+S1/WMri7Ypt5Q0XzR5zurLybo8UcrtM1whchBGK&#xA;UkkdyfsIN9hXwr0wUyt9d6x/zjj+XOufUrjVYJ21K3tre2uru3laL6wbaNYw8i/EKsqUJG/vhYs+&#xA;8s+WNC8saPBo+h2iWWnW9fThSpJLGrMzMSzMe5Y1xVrzP5q0Dyvo82sa7eJZWENA0r1JZj0RFWrO&#xA;x7KorirwvVP+cy/L0V2U0zy5dXdqGp6888duxHiEVJ/xbFNM4/Lj/nIjyJ52vI9MUyaTrEu0Nlec&#xA;QsrfywyqSrH/ACTxY9hih6jirsVYb+Y/5s+T/wAv7OOXW52e8uAWtNOtwHuJQNuVCQFWv7TEDwqc&#xA;VeNf9DoQfWqf4Sb6pWhf68PUpXrx9Dj9HL6cU09g/Lf84vJfn+Bxo9w0OowrzuNLuQEuFXoXUAsr&#xA;pX9pSabVpXFDN8VdirsVfn95faPyH+bdsmqqfQ0PVGt72oqfRSQxO9B9r4DyHjkSyD7wililiSWJ&#xA;1kikUNHIpDKysKggjYgjKma/FXgH/OXHmu1g8t6Z5YjkBvr24F5PGDutvCrKvIf5cjfD/qnJwDGS&#xA;af8AOH+hzWnkXU9WlTiNUvuMBI+1HbIF5D29R3H0ZYwLFf8AnM3TZ11DyxqQBMEkVzbE02V0ZHFT&#xA;/lBzT5YEh6H/AM4163a6l+U+mW8TAz6ZJPaXSDqreq0qffHKpyuXNmGF/wDOYGsWa6JoOjVBvJrl&#xA;7wgU5LFFGY9+9GaXb5HDBEnmn+GLv/oWf9Mem3H/ABL9Y5fs/Vvq31Xl/wAj/hybF9jecPKWjebf&#xA;Lt3oGsRmSxu1AYoeLoykMjo1DRlYVH9MKHzB54/I/wAr/lZZyeZdW1n9NBWMegaJJbqnrXRBKG4P&#xA;Nw8UX23UKOXTatCEvC2nuLi4nv7lzNMzmWWVzVnlkJNWJ6ktUnCqYeV7fTpNTF5qsgTT7T97Nz35&#xA;tX4EpuW5HcjwBwx57tOcy4ajzL06x/Nny26zc/WhEKFgJEX95TbinFm3PvTLhlDpMnZc+lMG8y/m&#xA;X5k1qSSG3mex09gVFrASpZKb+o4oWqOo6e2VyyEuw03ZmLHuRxS7z+hjmnz+j9afxgdP+RlE/wCN&#xA;sgHYl7h/zh5pDXHnrV9VK1isNOMVadJLmVOO/wDqRPgUvrrCh2KvjP8A5yr85XWr/mI2gpKf0doE&#xA;aRJED8JuJkEssnzoyp/scCWGad5KsBao14Xed1BYA8QpO9BTwywQYGSQ67pEmi30UltM3EnnBIGp&#xA;IjKajdaEEdQcjIUyBt9s/kN5/u/O/wCXlrqV+eWp2Ur2F/LSgklhVWEnzeORC3+VXAr0QkAVPTFX&#xA;56ed/Ml9578/3+rSOT+kLkrag1pHbIeMS0/yY1FfepxG6U0HkrRvQ9MiQyU/vuR5V+X2fwyzgDDi&#xA;LH7G/wBa8lea7XUtNnMV/p8qz20wqA69wwHVXWquvcVGVkUyBt+g/l3WrbXNA03Wrba31K1hu41O&#xA;5CzIH4n3HKhxVMMVdir5S/5y+8maZYazpnmq2YR3er8re9t6bO1ui8ZgfHgQjfIYEhnn/OLmp61c&#xA;/llI2qzmSzs7uWDTWk6pbxxoxWv8iuzcfDp0AyuXNmGM+av+cu9OiW4t/LOjSXEwLJBe3riOLbYS&#xA;einJ2B7Asv8ADCII4nj/AJY8r+e/zg87ySyySXE1xIr6pq0i/ubaLp0HFdlFEjXr8qnJgMX3L5b8&#xA;v6b5d0Gx0PTE9Ox0+FYYQd2IXqzEUqzGrMfE4UMZ/OT8uo/Pvki60dGEeowsLrS5W+yLiMEKrH+V&#xA;1YofCte2Kvjryr52/MD8qfMN7bQRfU7uoi1HSr6NjE5SpQsgZDtWqujbg9aHIkWyBd+af5hSefrr&#xA;S9evIorXVYrdrC7tYOXp8YZDLHKvIsQH+sMtCf2cQKUl7h+kh/0Jn9Y+rx0+rfVvToONf0r9X9Sl&#xA;Ptft1/m3ySHv2va9pOgaPdaxq1wtrp9khkuJmrsBsAANySdgBuTih8H/AJqfmRq/5jebn1GVWjso&#xA;6waTYV2hgrX4qbc3+07fR0AwJYrqHpRCO0hYOsQrI43DSN1+7phKAjfLHk7zN5oupbXQ7GS8a3ja&#xA;a4dRSOKNQSWkc/Cuy7dz0FTgShvL/l/WPMOsW2j6PbPd6hdvwhhT7ySTsqqN2Y7AYq+xfI3/ADj3&#xA;oPlXyRq1lMqah5k1ewntbvUCu0frwlDDb1FVSp+19pu/YAofFOBL7a/5xm8hTeVvy+W9vYzHqevO&#xA;L2ZGBDJAF426EH/JJf8A2VMKHrmKuxV8Efnnbz2n5w+ZVuAeRvBMAwArHIiyJ0ptwYUwJQXmfzQ8&#xA;RW0096Myh5Jx1AYVUL9G9cslJhGKaeR/yI/MnztFFqNpZi10y4NV1S+f00cV3ZF+KWQe6rT3ytm+&#xA;wvys/Luw8geUYNBtZjcy82uL27K8fVuJAAzBd6AKiqo8BhQyuaJJoZIX+xIpRqeDChxV+ePmzyt5&#xA;j8h+arjTb2J7e5tJJFtblo6JPFuqyxcgVZXU/R0648koWHzhrscgZ5llUdUZEAP0qFOHjKOELfM+&#xA;qW2pXNvPAKfuQHB6huTEr9GMjaxFPur8mbW4tfyq8rQ3AIl/R8L0NahZF5p1/wAlhgVmeKuxV5T/&#xA;AM5HflvqPnXyQh0iL1tY0ib61bW4+1NGVKzRJ/lEUYePGnfFXyf5f/M3z15U0HU/LGnXRtLK+Zxd&#xA;QyRj1YndRHJwLDlGzKvE+Hah3yJDK3oX/OPP5FR+bribXvNVlL/hqJONlEzSQ/W5iR8SspVzEgBq&#xA;VIq2wOxwofWuiaBouhafHp2jWUNhZR/YggQIte5NOpPcnfChH4q7FWC/nF+Wln578nXmnpDCutIg&#xA;k0u9kReaSRtyEfqU5Kkm6t864q+PLL8kPzWu9aXSP8NX0Exk9N7meF0tE33Y3NPSKjrVWNe1cCX2&#xA;P/yrDT/+VS/8q+9X9z+j/qf1qn+76c/W4/8AGb46YUPmH/nJD8z9c8yecb3y2wa00TQbmS3itKke&#xA;tNGeDXEn81afu/BT7nAl5VpGj6zq94tlpNnPfXcmwgto3lcg/wCSgJpir3f8uf8AnEzXdQeO+86z&#xA;nSrLZhptuyPdSDrR3HKOIf8ABN7DFX075a8reX/LGlR6VoNjFYWMe4iiG7N3Z2NWdj/MxJwoUNE8&#xA;j+T9C1C61HR9HtbC+vK/WbiCJUdgTyIqOgLbkDbFU8xV4ppn/OK3kq086t5guLua908TtdQ6LKi+&#xA;kHLclV5AayRoeikb7cid6qva8VdirsVfP/8Azkz+S+peZPS83eXLc3OqWsQh1KwjFZJ4UqUkjUfa&#xA;kStCvUrSnShUvEPyt/JvzX5q822NteaTdWuiRTK+qXlxDJFGsMZ5PGGYLV3HwgDfevTAr7tiiihi&#xA;SKJFjijUJHGgCqqqKAADYADChdirsVWTW8E6hJo1lUGoV1DCvjQ4q8p/Pv8AJs+efLdu2hxQQa7p&#xA;jtJaqQsazxuAJIS4A4k8QVJ2qO1ahV4j+Xv/ADi7561DzFB/iyyGk6HbSBrwtNFJLMqGpiiELv8A&#xA;a6czsBuKnbAl9ixRRxRJFEoSKNQqIooFVRQAAdgMKF2KuxV2KpZe+VvLF/dfXL7SLK6u9v8ASJ7e&#xA;KST4enxspbbFUyVVVQqgBQKADYADFW8VdirsVdirsVdirH9W/LzyJrGonUtV8v6ffX7cQ9zcW0Uj&#xA;txFF5llPKg23xVMbKy0TR4Pq2n2tvYwdoLaNIl/4FAoxppyaiEOZVf0laVoW4k+Iphpp/P4rq0Sj&#xA;o4qpBHiMDlRmJCw3iydirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir&#xA;sVdiqB1W/wDq0YVN5XNFHudsIDre0NZ4UaH1F5554/MP9A3I0zT0FxqjAGeRqkIX3VQACSxG9KdM&#xA;z8Gm4hfR0eozyh6Y/V1KTad54uhJ/ua12C2diOVs1tK4WoqA+y8fuyyWn7gXG9R+qf2Wz3StQuII&#xA;4rlJo7qxm3SeFuaEHMCeOnI0+bLppAk8WM9WVxSLJGrr0YVyp6zHMTiCOq7Fm7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqw3zdqLWlx9ZILJan1nA/ki+N/8AhVOW&#xA;4o2aeQ7VyE6kf0TfyYJFp9vJ+Y17cXNJVmhe4sWG4cMQ3weJ9Nl+jNkZfuwggDISe608ijsLvTrx&#xA;PqccaQoZCu3Fq1J5bd/HKRI206TVeMJDhqki/LW/+qeZbrRYiW0jUEM0ELdEf01k28PhYg/R4Zdq&#xA;I3GzzbdORxGH8Mnr2huxs+JNeBpXNSXe9kSJxUeiYYHaOxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2Ksd1/T4pbmk4rb3CPDL/qyoUP68sxyovN9p4OHOJn6Tt83kNy&#xA;uq6DcppGpHjLZvy026kUlJEUniVYcWGzb8WqpzbCpCx8XA9UPTLpyRd7r/mPXI/0TFHb2sN0eE/1&#xA;QtJNKD1VKqoXkOpPbIjHGO6eKUvSABfcmXlDR2tvMNzqZp6VqhtbBF6SSsAJXXxjQgqG/aynUZfT&#xA;XVrOQQlY58h7/wBQer6TbmCyRW+0dzmuL0/ZuHw8QBRmBz3Yq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVK5to7iIxuKg4tOfBHLHhkkl/pUrQm3uLaK/tf99zKHG3j&#xA;yDD8MtjlIdFl0ubHtQnFLBo0gR4LCwh0+OUcZfq0axsyntyULkjmJcGUNRP0wiIJronlmCyVSygc&#xA;fsoMqlK3Ydn9jeGeKfNPsi9C7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FX/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:d5ff6d33-9d50-4a24-b8e0-18e2e036eb92</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:54:48+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=112 G=137 B=148</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>112</xmpG:red>
                           <xmpG:green>137</xmpG:green>
                           <xmpG:blue>147</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=69 G=230 B=203</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>68</xmpG:red>
                           <xmpG:green>230</xmpG:green>
                           <xmpG:blue>202</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=204 G=255 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>204</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=254 B=208</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>253</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=22 G=132 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>22</xmpG:red>
                           <xmpG:green>131</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=77 G=255 B=207</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>77</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=6 G=46 B=33</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>6</xmpG:red>
                           <xmpG:green>46</xmpG:green>
                           <xmpG:blue>33</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=138 G=128 B=120</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>138</xmpG:red>
                           <xmpG:green>128</xmpG:green>
                           <xmpG:blue>120</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
28.688 48.001 mo
28 51 26 54 23 53 cv
16 48 14 39 15.9883 30.5967 cv
16.1597 29.8379 16.3687 29.083 16.6084 28.3496 cv
17.3276 26.1514 18.3237 24.1514 19.3848 22.8555 cv
21.502 20.2725 25.9512 17.6709 31.123 17.6709 cv
32.2593 17.6709 33.3799 17.8027 34.4531 18.0625 cv
39.7822 19.3545 42.1113 22.166 43.6523 24.0273 cv
44.041 24.4932 li
44.5039 25.041 45.041 25.7178 45.5801 26.4844 cv
48.7373 31.1777 48.0391 33.5488 45.6816 35.1143 cv
44.8955 35.6357 43.9258 36.0684 42.8525 36.4668 cv
37 38 31 41 28.688 48.001 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.641413 .508293 .418158 .322362 cmyk
f
31.123 22.6709 mo
27.3652 22.6709 24.3569 24.6768 23.252 26.0254 cv
22.752 26.6357 21.9912 27.9775 21.3604 29.9043 cv
21.1675 30.4941 21.001 31.0986 20.8652 31.6982 cv
19.7134 36.5684 20.0176 42.7676 23.7813 47.0205 cv
23.8145 46.8828 li
23.8496 46.7314 23.8911 46.5811 23.9404 46.4326 cv
26.3594 39.1074 32.208 34.1514 41.3252 31.6992 cv
41.8037 31.5156 42.2002 31.3428 42.5107 31.1826 cv
42.3428 30.7822 42.0313 30.1729 41.46 29.3174 cv
41.0811 28.7813 40.6641 28.2441 40.2217 27.7197 cv
39.8135 27.2305 li
38.4844 25.625 36.9922 23.8232 33.2754 22.9219 cv
32.5879 22.7559 31.8633 22.6709 31.123 22.6709 cv
cp
24.147 58.1934 mo
23.2397 58.1934 22.3213 58.0439 21.4189 57.7432 cv
20.9458 57.5859 20.4995 57.3584 20.0938 57.0684 cv
11.9116 51.2246 8.47412 40.6396 11.1226 29.4453 cv
11.3154 28.5918 11.5659 27.6836 11.856 26.7959 cv
12.7832 23.9629 14.0825 21.4385 15.5161 19.6875 cv
18.1704 16.4502 23.8486 12.6709 31.123 12.6709 cv
32.6548 12.6709 34.1709 12.8496 35.6289 13.2031 cv
42.5762 14.8867 45.7813 18.7588 47.5039 20.8389 cv
47.8799 21.29 li
48.5049 22.0283 49.1133 22.8164 49.6699 23.6084 cv
49.7285 23.6934 li
52.1973 27.3633 53.1162 30.5635 52.5371 33.4766 cv
52.2148 35.0986 51.2432 37.4229 48.4473 39.2793 cv
47.4111 39.9668 46.1865 40.5625 44.5928 41.1543 cv
44.4375 41.2119 44.2793 41.2617 44.1201 41.3037 cv
38.1865 42.8574 34.9004 45.3555 33.498 49.3857 cv
32.6294 52.9023 30.6997 55.7168 28.1763 57.1348 cv
26.9248 57.8379 25.5488 58.1934 24.147 58.1934 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_01_Right Hand.eps)
%%CreationDate: 7/31/2020 1:54 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H]K2jE@:NG!IfgD!!4E!fVj11)QO%eX$)oF/A\X)&o9q.Gq#.k4p51uBsaL]j.2T<Fh7I5PWc<tP0H02VbU8n4sK%WMo["<
%i;!;`GMg&^pUfJ_s8CP.cI\OuU"1174M:4Sl'!4ei4m&+]3!lRI+e>obO=<%HM?sm+5Xb[p$K5dpM\o<q"Cn0:4IT[mcLE#IKuLX
%\G>fu2fIRQn`KW/`QNa`mi6e>IWP8[c)\JN+5Xa,Ga!%^,)5"%Q[ef_Hj_F>pI$[Lq8lh`rSlAJO3eN<)__6-l)2;qTAr(cI(T17
%>fJ^:BfX!K\"ljSc#WEnduW>0:Oa@%If8;Io07`;r!;W`^Yk!49!/?SjO.9=pr;KcYSR#kjbdCspt]35b8J'#qpuM:+aLT6EZ<G)
%@`3Wlh2gCODuSn>5/j#OO8VuRjY\pZ4LdBI(*('Y^,^T$Hn%db00B*;$O+92@fLK@E,ihb[+H.1C(*bPa1M".HhqKUpf/.Zpo<f-
%o\S+E;)9#5\g#DkE'nGR_IfZYQc$5]d@HR7,2FA']GS@h8qrhnQarA&Dj?lA[lNntUTX-@^0Bk8(4='+<-V%5DMG@7%,VJ%kaPK>
%1;%I[/p_C[e-/PT$h`S"FbC]F^,f?+[ZpbN9<i7geE4!MdScdnZ/])3WH._<m^e;5Kb'5(Gf/"?6Gl`:Xr0_^]9WN4Be\91V'#]s
%hnAmBlYf":Gk(=sqfi'6h`Uf^o47j>X5C\M%q,7N$A.h9q!dGDlF/F\Hh3A4q+\U2hL>CM%H%(r^IQGUoIYGe:L@J)3%i_e>+RbP
%f.Ymm_tX-WgdJm&c[UD`H[*7I0/kW%a'QL<2E*nD&#k\uAj<s.rS&,1rpAHgrd^@5rN!EBJ+M[[.qTtCL&jJ4)1IQpkf(=Ske^sd
%<M7r^Ss::6>J>rVr_+C4OD?rHH1csEYQ"=K2uij,Mf2pM&/K?^nF7<us$'m;3Pn!(rnVd:?@DV1]@S-u]j+:lOo">>DdL,.k4LCb
%]m2p!B'9Hq?`s2$(E\,#l6Gf>pM[d`Jbn]%]'*dVqq^#i0E1SN)537`r9X0]hYmEMJ"UD^#1!]FkJD(qfO#5^YJGfX)VbB8ds8VO
%%D(&IQ!b>1H,1*j3AALGI<b_&fC/_8R=ds$/>XnVo5?h*rhf;3ibp=Ha/c9O5>?X'\a])15JDHAm<AOgVk4G=Ge*e;0C[hbGMeqI
%^Vqr&(B6M68pCSGq9Afoaf#(Jq@'D!me?9+7/5$a^5"ASIYSY&IIalF=oHUkhg>YNrlp5mhu3O%msb05l'!21^3T`3YATrHIeW,^
%5J3[cIrt;"IgU)hViUu[Pl+<#48A@8"7uj`)/'>*"+.fji&a1!k1,C("l6n;rVI["E9CU+gT-Ags7N_dbuK/>h>cHabE)*V^hj#4
%k%F_b^YfX6[ib)!p]p8AZip,_n9OU"q$hfbMgXIZ(F/pMJ=a-A:q,9L0o^grnj,bL^.Wt@n&KetQm$Q>YMc%iijh5)Y-R].-&3!D
%jWWQ+et.p]>hA)n+'uX+s*MCC=8S?hr;=ftG:u,gKDOj,s)7C<khHfZ1BD(fO$M2DZ]%9Dhr)f4OB3NRor'bN"8G$<dd',6(;thA
%$m.Xt<\HZ9.gU'shL5"_kP'uer'flcr13J+m""9G,eh`;)KpGsk0<5LI<b;ZIsq:dk4moIDu]Lmn)(m0o*$&sLJcK^$H$>2ZI&5P
%S;s<P1]EN&4pMEXh:Q%K0"1INbi_c?s5j3KfAF=0/18[UN1M6eQ(4[.`!Z:E\LMhs9]0O@!02DY^6A$gGLMrde`ur;m#*:![,=W(
%n_rNoQZ$4/i_SH\I.b"WLAT,KG<^GpZg%o.RLbt*AK@;;GJu]I"1p]Tk^/<3J"fH<Dgg;M,[4"51,1r$PUcV-(tY,O9:B#LLN#:!
%L36:A(N'Uu3rD7S>beHFH")mBMCrbl%gfYe/cRUb;jQ[mjdXjEdp1QWn%Ij"Is_9Kg[j%so"_D]"9%!+mm%&eq8IQ]5JCSMc->&V
%Fk;t_@#c1]9h%%0''ulM`P:Ug8A>3$HBb%o4a[#.o[e;^I/PuG?@;>_`:L`uVLWjg:B`(a!S10,ece+K'R1-_Tc^#Fa:r;WJL6V;
%5B'J'pksA/.)BFrpp0RTIXD"Tl'$UEqW.[*qP7[NKh@'9Y61%7PZ'Y2\PThPGD??ZKArj5-kT('P+e*BkXk8DIN>P\XIPthW8(fA
%/jjVK:F4)#HjAct'Dm,-DCANjeV^c,LP#9fD1NM2YoTj/+sfm=>b4.TJ#J-g0Do#(ka&HKV(<(EIpOaF!JNA?jGIaBBIA#oMt]F3
%lmm2Rd@^BVhZX6H<s8Mj6Of\[68tIP)9Q5id%D%\ZH!BM`WhKEN2Lo\Q["j_j:ebK"Ia*UBHp0jF@t_<8U#I%0)H&t@Krqm=-sh[
%I*Cc$.8+Xaph\&+kup*5F1G7O^%N#f`\eO3($ii2L(crBE2L7gS`FZnn=KDB%@4WDs1(B6MR9/+clD<56j;9Js'MGSao>21V:\iJ
%hoYo4J8.fnm;Mj9s4sl6U,?YU<'.fY;n;DrToDQVb>I-@^T"@8:35XQb7+WP&^7Vd^]]5a#2KfN>%-"@P!1Mf4?35(41NeV%#ckB
%1NZ3B`D3bdh*D4i#F^Lg2jfLNR#?/bV%3Uh@KnaEJ0>n@(1(r$5(,aR1"&`H0G=sP'/)*Si>0Pt&7!1#Y=_r_3gI-'\p.YCbPgRD
%>*\\Y/us"O(7=:.Wjl(!;[KK;VZJMT3W54EKi_4#*eHtNTMa\-0CH-9n'79kq0Y4FJ]5$<4VsKU#p(:\rRma.e(s8/3hK0F,I"q+
%Wi#ns28LMmP<ZV,EeNU9?B44/l$*?>fT,W,@+$CcUd3^X+\m>1hK^5]M&\9:VZu)Wm]aKUE.P^]"M;SQp5I]4m3W-o+Z/(]>O#M-
%e8me',I#j%CIQ]iJmf&;"92KG?+Bqg3<;W1!+2_@>r"N]"C?^r'NQEZZ4!bE'<S-$r#eAY7,f$nq$?O*m3-dVI9M&*I:@M7G[c,6
%I;41JI<'UVIX9WoZj3rKZkp(kW$eZ"Zo=dFTQ#BoNrU8r?I=5LT$b:"hVnI]*n!7#?IF:sSc[1a]g\IW69L/&97*`_7ti"A2a@)^
%nH3Y+O0R30lbp>samL7EG.\#iLU>jHA\kt)0_q26YA3))+=h9V)]ZW3G4,/UQ<RbMWKK,]E@mC+TE.E\gq9n3ht-_L2b7j$2sGST
%182j<IssC3eLrHZ6A=o6EKC<SHTtI>=3GS^?q16cA1Zo;>^D6b'kbKY$!,sf;`N3S1@GqKhQY]=C_%BY]lA7pd<;78@h1]5o3met
%.`jM;P%d[D>QY/=e-mk_U`,J3!_G3sc*0</9p/B<iGA)(S]N*YJijb6''M>-pqjGb_YSiY!OB6l8n5Xamcm,$_\<'+e6'@lWVAe$
%KC%o[>j(>bi'%r*aGK='#Q`#9J3sLn7T"8;]g-^lKBNB2$&BXq3WO+BdK2fA+?nIL&*VIAR:Ui0oGpNt&Nc/eU&Z:<`/jF=n:H@K
%>l%,6qsJ]Q>)7r\.QEu<3,o3ZnTnZA_"er)$GVBMfGspDjfbjFrqU:sR<G9r!0V1E/LiBWducPR6+3nk<N1Xu=AGZ#Tp,q*/neFK
%A6fGo<Zn8lnh*PI"_?JE8hMQZYZbGoS_Y<#@OM?=%njd%"MH?!0l2:a(UKEN!^2N#23a1C8]cNc)6@M5Pu1l-V:n=P2@Y[+U(]*g
%q-GCS=+M5GPG*Pkl.gFm^V#g,Y:BckjEKQTU$[:t$,X2T&<>22=Q"+r0o8)&8g!a!=GV7Uq^-eoHEZG'"_p08W2KaT,c/8n&``n<
%!?DA7;1;H;&RFo#djP'BdiBL7+>hZcgU9N1*bjt`#2hRRgo..2DpZYc?)<s_Es$'1c4Tu&PhC'kD$oIMR5FhJmed58p`4*Q0Q._p
%;*:aq<q3ghCOj,C]he8^dl]/O5L-`)5E4hImJ7(_3SH*Gbk3s_fr&Q9^17*O2*@8*%XpLr^%i]8W4\>[*7is$`BZZac4AG(??Z:N
%I/@]=?arS7ZA,SP`1($XR!"'6,Y>'_GpuW@e6'h)U:\Q6b`4&#k@&SF,>60'4,b7@WnA+_&&4-+b@+=W/X-+]9-$VB9"'naW'R5;
%lbJIP7PD>/l-EI.(8Go4Ei*jiSbbm'og*]c7T(7AfS-_LOQnUCI#3OI+<%`XG$MH`%lW4,l/)`o_40+o;;-]:KlliUEi9e6]^+XY
%Q(P!8XI2#1C/m>srEM2O[YTrm+W[,U(IsXZ)bJIHX;G/9IB0ET9r!4ADu)N:p02B>m2Kg!kV`pYEhMgl"<E9;#8h5i5TqXT!^.jF
%-A5Ui!]nR0oG\dm,BQO&W?IH.&V]s3aLd"UT8Cl9BIA=B_+p)+cNpnai/VlP*U+ua<MdA\d6?bk$DP6+9Y9_Dp1r=:F`-)"rcNLZ
%S?Y>=k>aNZb&LDk;e\UMiIr2NPVSJ>-!BIZ^`^U#;*b7!4Of0rV)DRcB.52X\np"*,3m[3-JO6OcUH>L*:Tk%aPn#Xl5.P4kK3RU
%Kbt8A"lFi;-RqWl$6I8DfZ6+0p-C*&T9'YSe^d:B_E\l(:EgE+0^?ji=V&hh5s,Sr8,8&5&SRqBfn=,hY:qSK2N->;3("_QDOhRT
%-NG4H<h>hF=<Y`XT?Rkt/TGTle9,]1*`dA%/97L[:=d)2jSF1(X.6;8S(eIuf\IEm^"D,scKP4P]f3R$)%JOX4<I2+1=PF>^,Oq8
%*o!7Y2".s`&%nM03qrO<L\VI%HZ'A[X9'kkB-"'S!mL&DHe-*kRZLOcNh,R;J/WQAXOkI<Ffa1b*Hp/N^Ht-pCW$\Zl*sKs/#DP1
%\_T8[[CD4bo7:1g[:d(m5)cd=J$t-(WipcaOqIsl41'$m#`ABqP.5)sV#Yn/nN#=]Xj+6ApSZBO^Y@f;LAcU/!pKQ4GQMi@,c*Se
%JoD%X'Ws#tOenn[_V#&EHH`9_6unYf)p7q*=%_laC<YZb=I5:_N+A-cio^A#qCPEhEbCR[=G$#f4g="o7%;CE1T87=jTUka4NIi=
%84cDRXhBTd+FjFm&i)#n'+PhC6M;A,l?oUjrrl_9C[<mG"[T6XO_jEQlqiu0`DL0j$_n*JQHg<LomoRu)t<;e?;Ir-ZXo]0l\n7\
%Jk0;1_"dpqm-`E&e#-"e?e)$Sd8$k^K`:^3MCs(SX4_E98+Q>AHEd.!eBn_\iS@US)u_lp)oqZV&)fKrPH".\>.,.'/lp$(Q?CTU
%*[oHt/"0QXe`s&u6sQ[7\FJReW:1a<MI_4]Ud9oQ8G)8OJ,dKjMZ9,&g+:<WT5A<b-S+LO&0d%c9[u>Pf'u'G9/CqXM<pe"VbbCN
%Gt5l@d@NpQEl'c7h6NIS;QZ2>,'hf=LVU++=jGQ%f_pID2:?o*]:Qj!n:4Nt<eue(*D@ujL2P$V%iEnMM+pgAH4J2H8B"!d]F8@*
%RV+jMT]PDOH+/@NM#[!k\ak=jGA')HaLfNb`?mc;oI&LX+46:r'Zku0aEooC%e%>S(^r9(%b37c8+@7!aA;"&!uM7b+aXKUUMnLA
%KBs<uT--\/lkLL'Jgf)S:kC&thN;W8P:4j5TE]qX!/bcUoE5lLnOrY/R0ktYBG[I]i,hKfKA>'Xa8l\QFViV=n8VUK_.L"$^o@^T
%5YhFjkYAK4ECNK-SP+)ZR1N9-rh`k)[q`]"1MDEI[^am;YA'a^h/uNJeQH9>csSi[NWdTNVcWHnO/UEc"Wbs_:h*"K3q3e$$C'[=
%'T[63Ln.W`X-=r"TUqg!n4V$'V?iV7Wnk!`;/<)=dVY]jk*9+BdnmVEFgSL+WQ_eRZeIg+#FV8qftPbDjg$I7,JUj*:m`THno0/j
%^I&qs7ZG]JGPDE`B?MClqmI*J#2`FI8<91S8?8CtMXADs8He04"nn/ufWuB-^&$#LgpNf#MP>A;'7gU2.dUT#,-;7>,8cmYHW:@h
%]j#D*-q5&ugY;F)F`%;qV:!2_"<4`^&s'gXlN$<iL..AN$_mHQNI(6kG67.n/kYF-Uc-IgZ7j]M7?@D&5dbY-Z:HRGO.s]W:og`Y
%MjPQq>ZTs(?"O1nJ.eCQJ@oaVEI\+);+T%p<8^;0m4?'TYgGgt)D9Bh"icO2hBk9gkV*/KFAW#taV5l%h?mlid(u)uLC_YfU!CeB
%qsI.iZfj8.5-KWq7>ujE8cY=hJg=CM:,(^;An9;`M0Qi\`^+dDPDL4U,"Y'pRV^stF/Ki%_fl6SL`e0tN$T#7K4k<QLBo87?0mVs
%H%HeXEV^l,k`4/""Lh?NHV'"t_+1!uC9Y9j+>r=++F-IL9b<Se:6;*+-nl)*@DG!COUR^\d#Y1SC1d"5MKG5[RO*t-3)P8Z5c",e
%B[Hj;9u9"0Nd*H)i^,mu"sGc"OMQ\WJfWim^8pbQ$9>IiOL6Q*YfK,,E>s%t6R;tkL(ou)aWr8W,u-/:,*()$m;E'+<++h0TTs/1
%Z/,pc"`5$BA`s[uHGWY(!4&[VQmG*O0D'</YiX<*Sb,hb>d@g<"Eo&%"c66T'Ck,+!4d3gCR)YT(fh<*e]:r@$k#ea`)W-Q#g3_[
%GZM50IN&/maO=!#XHWRC:'0FX#0Q9^<tM]c']k8KCb\,#3!(H>DQAlD'E,.SI1is3bQ"Z4Gu;qoM9s=dUN#,m*4[il.rY:Nf)TrE
%)P$n73kRn4/K=o30U<a]j9Vn$,m^H,\VEFl*]T#G\g3f+6@jlq1_'[MM/gNAN"M&h-ePBE5g3K6:]T30h%=HA=*fG*N'V*#ZpGE0
%S0=Qb%-u0i#ce>7's&Z_5os'%L@p/!jG[jVej4u"?X]h(;4WC9*0+`PB?Xp-%!:3V$]Qk23RA&#!]@8DBadf&G*ar;VnY9ch&G]T
%Mh:+-!GO5a6]6Qc<#b4$HNi@Y9OE32%-q<r*uD=mBd84[#Iu7S;m9)-O&0c\R1"s:$W7n"Gq_3&"!+EB+;kTU"X;'=8VU(HJroKF
%a_VS4VA'eg=L_7B-Plu;1@lJ/UUe9:$#gdfR-Fs+?,U\V5&9j3M[B8;R'@02b&.@WWXJGD`s+/#Ls,/I+'nTr/`.T<h;,4DG&aGk
%n.Bf7X3:EK%WZ]O-Zb#hFb,J2q^b$A+Ba<gfH`;TLjM3UJ=)(Ih2*@\+Welp`HHm3/7e*Nplr87Z+E`o<#GbY/;+FT\hT.;"/cj#
%-%TU-:pN*KC_]\Lqtj\<EMAIL>&S%^tY!Wt)Kt:-bitB!hhG)8HS[V*M*j%&aAuDTFgK&;(GrlnF&M!;io$%CU5GqZ&WX
%+?;EYW.>BhK@r!:2W02V&1C&ESBh?"bRclRHO_L[K(/=f%s0.'%@C'"T#f,</+-qKaLiYYi!@2`\Zl7Slgl>u&5s.c^BfVHH.$?J
%#fc1L49ae'iN`U-"9-C^eSW5%[`SEn-q,k/NiX\4A5#=V=&F%"qV!h<T\*Xm2m5B7U_'k(6I4U\0Sk:C2EKLES_l48I9(csI#-\;
%@^Jk(eukp%puT,30AaG>k2pkS]"umUHdn=/B9M7:M$"a!I@*n86qS(g70bt[LpjdsPeo%3QliDfT$>s>_blSM#dAr@h.p6XRY(;<
%$:)2%3n0U4W(1j&>m:`q_WjCg'XU`i4Ai:L#nM,6fM2N"2:SnV\8l"E-Wo!!/YQtsBEpdQ'5BaUV!TGc0PDa6]`kiYe5Huo,cH-A
%ecB"cS"A:&6fGgoYsD"l3dTF";onfU,R_d9=]<t)P/VEn:)eB,OILup1[L?WF_7cnG/0K:1>/GNTIk<L7";CJkl/lcdI^aRdb/<i
%iLtBEKXu5>Do'V7Jl2oJMOZ15gR$5"(S;@O*!]!Y3e>?)r(@igYLQJ`LHa't/YXc$L=i%NVnk%J8O'!mg9Z*6b?S42<B@5[0^OL/
%f87-bOM1(OoWhkN[Ya+^:6nVO&p[)cD@Ehif9,,Cgg4qP)/L)_D1^!j$X8:dbOc2k\DhS$AZos'UkHAEM0^(a:0^,Wp.=$[)srZR
%p-[U?EW<)Hk98U#4mkj1L'28tL#4s)!-%rfA0YMc7X$JNAq7;*rsU57g?qs3%uVXbGhK].[_ZQUdq8+29c&A9T^X,c*6:K\Y7ciR
%9U\6l*<>i2Kj\:J<@U;M:aTNTre1>+L49d8%(Mp:lbiJL!nKXX4d]a/F0k,AK;K#6?A=+Lb#,M7eAI0EC>oU*0cVe@"`mSIl:E[p
%-,;3]?FSrDO1Ya/MLUX17lH"R"V-GWIF*("Q4-`>A#TJ$Z4lmfp,*=&p9l1d$E#6EGVf:H@dj\>6DjdQ7IO(bScX*Q]ORt7JTi/D
%Y:pF-OZmh09np'C8h99Bdo]Fsb>'7>:JYId:@+N>(8%2<![Q.K6l(hT@psE>Uhp\@ACNQP\$iOOk5]d8(!`Kt-BO<$4OPge,Of6N
%i>PO(`/q;?=R<leTo=Hl/\Gp)d#Ig$984X@5qV,t)dLCi.q;2;SuTaV3Mpp:d_(P'?hS>*qt?`"2Iq!)[<3nd>)2+?Ff=&M4Frq*
%.hNO;YmG01-E'q6TN:4^BJm]/Eedd(%i[f8,K.`!i2ZF"'R6mi3FdYpBX"J`A"cI/'G,D"h?*@QQj3WRBt+$_/1&eK;CUC(3[[.:
%0\.ZeV?@71C,L^^S4#Zl.$Q0?!o*<B4c+dCo*=_5J]Y6$oZ<uFd`k<m.?nGY/Lb[#I<\VpqA.Q.."dC<^)[[8-Gdf:eKQJH=^i[O
%$CI;YS;1=P>].7)Z0S[prnm[GNqn[GjQ!BYjl;TY%eRZC<:lnnmf#b]A'F82J5$X8UpT8(J(hQ!lTf6Ej1@mJL?I5Rs$5Yj'Z/-Q
%?f9?'rhF+RoWZjgf5a_nKssss"HniZ,u"pYV^,':-8E?l28I"9.`]Y?H8$&`d/mh(b@rFG+F=^bCUCl`mO<>j'aG`+/WV=nR/;]k
%PZm$CF]6sdWj#RbpO1kiXPo)&^RQ2ic%+t9cf/OC5M0-pb4gT8r+<Zip+^N,&%p,M4cMU/]UOqSjeJYb:ordt[*1_")74de"HA2-
%1e;gHGaV0k&g+6>*@7!s"q(j+h'@62r2BXD2$)Rp+'U,0]93eF^Pn?+-PsO!TZ1:6VjG/g)Md#6Li4LZ"d@SHZI"KC=g@tbbWV%`
%(?]?)a5Ymle1s"a3/E?Q1!1niRV'MO>]E(\QG0)%79J-P8?+-mBgEIU,'"ED?nR*DET@U/8SUphFVo'0CH6-mUej1DUoF;\[7<O6
%Si]u)0kcl]<`@UJ!R8roE+p?2X<gY>1hBU_W'7/b:2oB3jj>`[EIJom[h&o-Mj?S/NIMNG&DHOY?1CA,UMCiQER3g?rmY;LNF2[a
%rtEtn_A''b#OJ8g\=2&Z%/5Mq*tJ%;6iL/<s$Ir\#AWn3ipJ6+$LSl<o0(5@?;T[[][#+:e9K<;T2PY=f/V"J$dJ)ac/M@%P\4L!
%H)52Ao72ImAj]<L!_*ef>V,9[d?6Xl_nb=^`P[f2A3aK1M&jZm\A_]h;&9?*@S2I'P:&q2"K6<6U=@X?oi+On6D\#>0AA00[=X]f
%[F@\\nQ8,;jc`?o/)nnQK&nY``aQ<J@DEi+nc\R=_9>*\TJr7_M.Z8ZF8&'d7\$Fik9eGEoR/Re-mkBam.!&4E8*\V*SoI)q`:l+
%ls>$"aO`^t+<>6?+6k1F+pl-B9ZaK`ZnrVHKAI/.i,ICb`o1G_k"!8J"Ms/D8^6]\j-jgeQ4N-BPAEN"4mHq8nV=*N9c5a%<''<;
%$DRI4o'"[PFS>\6gd`(Mb^?\dkGiM3$QT.+rAWD/QeYi?5WQU?MJmOOPi6/k%[$6DNNkRgD/h7(;R^9,R%qDiI;#OtPi47Q$(Y7H
%Vs*<-S_&9=.rSO5Xh6o*iIhEijFhEACmJlllh3\oa.H-b8<&)*X]^HEU2fYO672C\p+`OZak8B[)VoG"<4:Y2i-6dTq7k>:@+\!*
%5M(7[\!6j)i*N?DSaJK(m<.^]"V"@.#C\m#ne3k]j7([]l&?DU\0P6%a.>ehBdM5=nWp5j]R.l37mHA:bU"tWm33=`1ELFb(9"Hp
%2!Vfe[J0*bhRh0b-cl\9K==8t/fD)?%"EE`O$KU?JQ:qaqlpbe=^7T/ROe<Ld!5]*G%&K.%'kjO1DfgQ<O,9\s'-oD=_q[lN>UI)
%R5?T-A.Bj5:tk9/R/bT7@P;1Hj'(.t3j*mOjYaVLC_@tWM'3N;JP/C^8hbc%%J&&b[N#Y5/DpPO!tB$2\HF7^f)Q!_>bHl&<IC+*
%L!,R1qQpT9R#3S!GqV/mN^I-+4+Zj7"q26OH^H=a#+&'3I/05IFY)KDkrsZ??SQ^umtJ<$Se5N)qX^0S$j+LBWP4>aNaB](b[uoq
%JV8%h52[=SIV%/0mL3j1Gu+t`DKi?^43`8L-R*AJT8_H!Xp$E'+j4GQ/T7,!fIO7!+]e5Q!#@#Na=CrF?p\K.eK6.E:l#"n?m"$W
%@"#505-r^KI597Sq:R:S=XC0kZ69_5Y&gGIB[o-%I*KG9#GI#NeET>AI%Jo#&>Q6l$.gBNXoL?B@@Y<6*j!9W"$:f?QQ@_0I=cH[
%&r[ls7<^$`9ua_0#^Z9aX%0r.#D&e23pV=]qB.obcR@Rb7>ENnh\Ql?I(1cB<iknNM)I_lg;_HehCmW1T.bAaZn$;#H$(eg/cW^<
%[^2"!SasO]4@FM@;4]&g5`PX*d@M9J\P.A)Gl5L*.N8+Ss+<j!T#c0gSA`>8TR(:+Ebbb<'(Vd;LdjZh:=5e8/9Ch@@e@s+qN0o<
%@(,5=6#77W\l6*\1t%F2a`CuA`Kk*tiadHOb(rsK'52)ika3Yc^e\(22-O`W+n`4Y?>U9Wj?asX>!7U_/nJ%EQEQN@R(9_2_?7ba
%.tmP-<4Q@Gr]ac2q_Bh-d6t1bNs9tk[T2[rlo&6eCRZPQT0<j73G4%2,fEP87T;]Hll@g"hMlESA5e0k:$eiq8(@mc40k-Co?lou
%D!Oc*S(,Y$.$Q_QS/(^@&]lG$iB^<)2._[^8LAn3M0Q\>W.pMS1b^QY?mo^*0@<Xh*U>6gn8t2E*@2LCj4o][BXh3Ni?DrH`3_5T
%aqJDt?FK&`3Fn75&,`JL.-4($fTh:01"UDuX7dcnOYk&o6&[6W3D:D\%kF)o7i@=P=s'RtWl]/>%2PLpn&E"UG2NYMc-<iW?10kU
%Gq-7gLG_7;MPB3Z.<5A/.oLa>7=6>r"MHGMN<agib2mcHE?J,!VhfO"<[?rf4VGHKJ\Xmc3046E;iLmq+,L+<@R27@*EFHFU4rb3
%LLZ)7ki.'h#Hg_m(dpA"fr\_Ec^+:67j(l1N?X4_5ntN#B]/Z>Zetg$,?QABW(9=^7NlG/MOoM'nc,Lq26&4%l]Gb<^akNoFeFAe
%Cc0?b]MK;/QP4Hml:S-VVMHKP%A&"g7]>s-Ik\D)K>(qBMloQUNH:DABIgA1Aqn&<-+_sJFb3ttFa:5,Bm]Q\,3RLTn,$Eqmobk?
%'rg_>p[>,C>+'m.'feg!IAD0IO)^sbhUs3Y<DF'e[^uAOLCr:)\+HB_/Bc)iKHre26.`f/daf>]^.OY<j1(5I,WA7IO63t(UaW23
%*m*Z<dH9&q]1MC*NW81l35qbUR1aj?qOB39e)ZMqEdMP+98Z@hjj@U$Vna<Ohi=[Rp[]er8Q)me9GnS9W15=I5*T,g1f/?/jDi>F
%GabrhShEIf@q?3hb-Is;Smb&kSjcBJS1NoG?=dI0kr5)sK,*WSW3I_)m`D/8U\&)aSkRUKoCNrj[l.%>Dhu].;HES/1$/%l:3<CM
%O!_/se553)ECLK$.=Ud]Utl1)hlFiaHQ?JeW#+'!k/P<s/%lnGL`553k%3$eU;rd_MRaPQ4iJB_/=KH-'@)A!DmB%("QL323(GW&
%8`>+0N'1\:ml![$pRJDPI'/\mJpd-Tc.0+!"NrMZk81L8TT(1UgrPn!CZ[tZe.72'3R>ojAmStp#.R.c13lIt5NclhkA))_adf=F
%f!o^](%7N8`lp#8S7:OjijN%\L)lki`sHoL!6i^4]&-f^&$9kVbJP7uSnn/UqV=;Vm0g'fZDBJ$IC%5UOEU4SN#?Nc=jR\75>e;;
%mNh!V=3D:JF1.@iA!MD2EfX(P2,0L@GZANI9jcAoNaWc+oQ[2/H8WSAK/GirAg[g>N(5\f=rp`8l6,i8MI@i;43[ZW)P2t0?a&<5
%ammFE]67VCLH(5CG;%i/2$);'cFViVY<3op0X',;]u#YC]BaUQQ`o$+eWgJ_F)Rt*bUU<36KMU2a6gI!>+,+)KD>6mEfQ<\ii$ZV
%@f%7Ro>pft*@D&4!W>]7=W41?$U2R&T;5Z4ab'WOGcfTYoKM0@A]^u/#JNlks'a%6rKkc'mh(^"/a!$r\ECOIWmB?i&8rmq)pOm9
%6-kqf9To!<^gjT:Zop8WSh-]%H(lV@?F0DaPAe>!:XaT$iY;ZpY)L*>@(30\gn=sH)Ohhe2dko<fVF@BRYJ[t`4)cKS;M:H;A]H_
%LBJ;k![^Dlm]n@_NC"Ti+c`Z(19c49r$&dj:?S&7"O9?@P9h558;_rhE]k"55A*TP0eJQ;36:nnFVsr'(nNl9\f4G.C>7h8P^%CE
%^HNo#)3Zu79s\<Bq6pie,SAtI5mkPq+OGk=jc[>g&G='Hm/++X;j-$D(&?%uN8aTL#=%uX$M(luda)cn"!"J&D74Q7VlQRrC^#k+
%[U_cPpjScKl`s,g3$p95),D3rJEP9]!<&9[Ii)%6]D=@]eB]Z;a/Pgu?u5_/=8YSbUiD[HBlXmNfQOOg4H+"sf6qOUS@JZl@_N2F
%SiO[5(opj7B*uB/L/3g6c#qaKT"%K(k2"^-a[O:<gXE\*R78P&iO$264Q?Qk'o[q#CnpXR"DM5f[5..k43?<=L<FV#p9N'#@8=W0
%,tKG4PBh^8_UCW_Q$o;[kFhXBkkkjakpRQaTiC2&FApS%$l"q4Vj*S=R#)>`6;0N67+5Qc8:W$sgV(FIA*DbC9&'W&p&0-:0s1)C
%;R]67iN()T^45FVVbc_VhY-nH[lo.[20mr*ZKMOW[n032Hd]C*PII6dKq'\)"]g'mgTP,aWFPfg&hZeVfe57h(#&Fo7lDUGpR[V-
%T9=/UN(BQpY36@$/%lcl<n=NeG%;WmZ#f3cMEJhUXj2?aWMD$4A2]qKRf/!iX'bWE29PZ!p@E+$lC!DYgT5Mj*8@QHgeCE(,c61W
%0]lO1,!<e0q.M*CR':>67R-!6,XE/W"cKC-)7>Ni;;4rs*Y^Y4gf/j!O<Afu(JuG<LLHr&l0/m+3I*%#q'h<AMR/kI_P[]O'/&oZ
%eiQh-<9XK^_p/9D>!PCb8a)b_d69ek#C!h5PY69chX]Xc.OC+IhQ8t?&*#-DG<b2fB?jh<1Cc7X1o'sYP1un,E?OE`fGPJ'FG!'J
%G-T,'NN,1k@im:g>1f7J11atse:Gob1p]!5K/sYb1-8\meRmK4Z`l7%kYXTE`rmh@k\U(2R\9:0bF%E:<PX<[7UsM%67<p$LoL\X
%Bdd!.P@Vn"Wb9="#-SI14(O9+cm+Dfl$/>NY:GR0)pi[BFKn-o5T<Qt(ik'A3CBX1rV!Jd_K'(#/mEJQ:duN9>(4)]VI)-N^6doV
%(+e/OaRr'@lJVAic$2>OE:d5tn%0TIPF"BK_9N(gZN07&^[ZTiVoErWoV9sJ>H^OI2SYlY;T65L^W1ka3EOD[jO<PSSb%f!,;<<L
%8*g"6)SJhX%Q1+V\lXmI;aXOcS[4'sVCGD"TuPb6WG:R_1OS%(7RP1djU=)SUnIZfk:R03c[aH-T+lpUBR`JD/)otBV_fD'K<OK"
%7FUug`ji,If3%6&Q34.%7&8,,W=`RQ7R7pSM,6s._2<`g^qm5Ma8aTYO3cf>i;kIfs,s&Q*230f><V/LY;ERF7m3J`[g^*-ClHP\
%?DO/1H0rPkUED-FO0n&4-GpkRGh:9&Pha]U?eUE0^RlhjM^Bt+>O+olL(qR\)Z/%B2kMpP(?7"`FnI3I:gfGd_JAKHb$\r(20$2k
%[bb7riY/QgKpi.cc4l!Ok7`R$k6EMJ;aZ!*ppp(>_:mkP(Cg0ZHOe).W=_!jd:kP+j%_L&5(nC%Snc3*7M,5J`eTSrm)r5j:`@SW
%/KM5]`]T6CeRP-!(dU#=G_+2%$uZOXk`$Wj@m]rFWP'V>LDoau"]gE,L,($b*)6t`,JXIr#;tcfUh,4@N3LhU#\r_iU!`M,'<bNI
%8Xk+9Eu*LcA^8&ZdhZ[\`oB*RGM$9NJ>unq1@U;ZmPlZuXS.mn%'6@['\c3t;"PI]-7<k@8/i"j&Y%T%**0/Dc\(JMTOh#OFWsQO
%+HDN9KO*,a:&IgnRc=^jWd@2rf*RErN)DYT5/j^Id,W:!PVtSdREXH\K:6&;=/a;F)XOcuT%piQn/9npPXj=-f!s"JXo2J(6RYFb
%>1p?0M^dJPlhqW^+8*r\j6?cm]+"sUMdBP]>dd`O7EOn/f%fE>e.Y".18/@f>IR>nn8Ph\X#W'i"TFN=lXM5A_JVM/Appbn8sZ>E
%8?@l==,ki)S.9gCb,3[Zd`+W/ZOLoi>..",[]*QXRf[=.e9ID(:289!7;&+RhHoN]DKT.HHraoF/bHQp>WLuMb]4KnHsZ"\3@a3n
%>FKi<kF(ZaL9Ai2FmS+MR<4Po'0TfJN1$54$e;RMcXom5OGm:5I`s_!!hU&cBh-2m:Z4?X[\!qBB7b^TIf4(bNQ19f;Ra&>FVp3N
%Ej0Vt6pI-IUJWsJc0.PrZ*Mf5`&V5Z&F2Za$(WL@)mu>Vk;ZX=p_]on`L>0X2mmhulUpO+?cl^EmoZ>h'^F\R#G&r%*SM4hAG%Kd
%G%QUBWtIR=OHV-iaXCfMC,s_oJ.-$s;XfVH)a$X)&J'h0HV+ClWtI_M9K$'EWGEeXNgCcVq'IG"D6"V<XlmgSG7E`HLImPKWoeUD
%guam9&'_g[/L-+>Y!p3PqlYB+hP":8e@Uq;HFmfOBsU"n1;/mI2YnK$?4Wr^?S6,@lW?noU#!i`V6VPlQus_0Nj<6qY:VPZX`=Au
%iDbpLi[\)<I.s7q3EWqd5$>_r[W&7e7U-39=4/qM&Jl'u0gnp!YngiF`FT&-8sG%Jk*s)"nJ)D'<9U,Eldf6T0tm7uR]?Fp]E)a8
%pRpB0b"WMZJQu5%NLKpXaM[6kidoE3Y&^9,8^9.nNt?YiMWgd)3R\gbp3``W+PrM8JtPt_^kknejsLluG^W2ARUQ'7-u)@[T8U!9
%o0f7i>l';o>1At;iC)s5ki0(?h:-feG>;IFF7+GPEZHp/+/YFWUWKqY+G*DpMgi?AR)CaK_.r>ZQqhXj4Ak0T(r7tQ]f!BtHbU(<
%Xj?OB4!&?_\:F93`lU7t43k+:4^.Q*;Eln?6B+-rhepZ>cAAUb?J]n_jka.,@NgCCqam-T1G0sU`S(PTB8BEGfMStRQs[GZ53K"M
%4\Ji%PVm'rl7]E`;DsGNWE%F+?G<L`VQA5n58N@o0["LG2Kp*^T!Dm$q6:.Ip*q:&6/o@VZ0k9fmF,W"/a5.q<Ws@>[H/pLYVu4,
%nb!Vmo+b'm'i4E9:\,;so+b'm'i4E9:\,;so+b'm'i4E9:\,;so+b'm`ElpA1gfc0?h#tF?fP3Mbu[$P=$4_jGad(kT&s?I%Fc5M
%"'I[^Ae.nJ`mnmf2+`09UMXkVXsq5"`7QK\7WC>DZk5s#BhN2Xl^/V[h=,53K"Fn@9osp4%"H1k'PPT4oeqtibB.?0&3Pu>8MctW
%W]BI9b[<F_PRon98YLH"W91P'a@W%Nm\cZFR0C3tLCW"B6a.6)Q*4Eg[`8iR.DrjN",&5OrPbjdCfTn0ORDX(=Wj)]6`b`[!83b"
%r]&R0i#sD6GWs6E>63R@J`t\#e!+!^b\o]e?T*6B<[VY7g0/h2FN$.DgH5Ccd6mEMq&$HsJLhpW#EjK/A&dZ[WZ3s/SYt01:P?B+
%q!<oSA`oR3PD\f\O$mO?'dB]NK(8<":]G3ipUkfQD+/fRD4G^teYF#+[a/if)_Iaua6QdRkq>*8jfh@)U\M)K$-l,DL?#6fkK.`t
%.uDQ>OcsEoFkK>CgTtU!5N!+k`"($%L[<^bk7)XKo%3&3.$$(:.+B(mALjC3ZuiLY!kd%DQCUh?O_WTf@Etd:h+oI][>Mop5kus)
%h`[o%nJBu/De.`_FIso`hL=>7X\A-k3O_.`EB[WIS&'9_k."L1\e(aBf/[2",6IUSg<i$+:kJEb#1jR&j)>hVg"a(""Rhj#]KJQ*
%mh;^@Fis3nM_q.<pX"_s1*Jts`8WZSdiOBd.$>JHiGr%cX#:"Lg?k6S@^*jJ?8(+9n^qd8?eD\e5,71U+\U+5jhJ7cj(jW]C]%Ar
%Q`oomEQ&G4Ma<E[V?X^In'?qF?K$fD#\1ahPP2__oQN7']:t8i]WiZ@`K'<EMAIL>%G:/M'k`\@`f/BH%*0RjirBFDQ
%cg.Jf-rP?9q7b,Mh?:$oQi)&KIKLTb4eCW:5/H3Or9oN1[2e[h&,;`/3:*6p@%#pXS860T5&c7oO:?Xd+1ukl]"^Z'9NTPZQmD$X
%Zo6!QpRgEKIt#RW4X[9ke9GnTq"=Fl&"h^_3cjE[T"T/%qtn8kiZEkJO&qJ.=0UqB9L%m:e+`@r>D2h_7NGMsa/SAS\V=,QB_;tB
%5%<)O2*m4;jSF[=n#Pe;+kR#6qh/L8!pe%s+PLI5qi#Eb&an\i:M['/N-O5(had>9)?i?uiTZ<Ar]4u?<n5htfEo7&6-jIhoOA3>
%-'#siNL;NqF3.KVfbR3&3@"4#Q1g#S(f&#\^Dn>&TZ2,M^h8ttClU1>RdcqN?Ce-E;Ja66d1$fE&1/[Cj!#JG76*UDN@\X^3qtbi
%@=:?7:=KBq31gm+-bRY+rg?H(1B_7*jS8NRnbk8^HT=j_YUXLD)ep%`BTAV87Sq<K'NXRjn[X<Ifm@SLMN3]BfE5=FpaN,>N--#*
%&Y3/jQE@?409ThKaEB"UbJ!HPl_C#q3L%>bZnD++>-A`+n+02<2#4Vq89mmg4"&_:<p*](;e&''8UPnZCH`tO=^&ar@@#XVCT@S#
%2Prq+lHYMNT374#r4U(jQ3b7[P!JWu07smkPaIbkBA%qB)I'&eD=GMe<;=q-0i@H'<lM!0^iG::i0RZ+^/B(sj)a'b:#s]uO/iM?
%`>.GnFNZ%4^j#hN5)nZ^$r&:F4YgIA,,5d:cPR7\5'',bK$P>I]ZoUM#hAJfQM<LjiFA<3S^ZYTeCSKHI$,r1f'2RiT<kHOa[n6.
%]KF&s4)fI%99]I]I#np)'>[HIU'rp)#Le[4?8kpGNPXU.WGmL;JeHKk$R$RVZ0e6-UZk+i(0sKEV2.B#CX^7EJDJc&hR*AO%^H1f
%p#2Z`a.k$n"Blb`nG^iIW9Zp4X^P^'aE@\ZR[HO5.HFP*l8j1ZKlS:S5M;$WTs/_d1I_pN.Z%I51aWg_);#dcS8@1Q!LhIb";9&2
%^j5n.,7/]#q-uV[kWGD]e;]u\i(f]U:]27`4n32FVY96f3Upc<G`'&<T6><8*ET9h=;<jV8--tW\e4EU0m;:@\9bTh>.6*um;s^'
%Be:hAO.fj*%[/!A*!Uo/.Y[ndS@k+<KV]:inlc%:aiq5,Jsb@':BbU$TTpRo_C"(q"K&QbXKF8hjY@h']C]_`I>n>&P(f6%3Wj%P
%pen>9m76k2#s99Tn"R0'^d$G`<K+Zuq'3&R(ggiF?ngVD2Q/UQ`UtLdUR_Rd7c>#%=#14;/&ST[L\'Yahnu^&W3[D#:W$&'-OY7O
%PSe(glDZMf(kl5HNXqt.)3Qu>.rY:.f+?;#!FZ=[)PKiN5$&C_dcf7Za%dCp%D-a.S5[`&dut_2Yi8E03RN]$TbSqph)IE1X)_;G
%"][8..NH$l0W`LG9'>fRn8p6eTo"Bq&R9X:oerDCFb(81&g)g#8?`5NlXQlBKpdsV<o:`@%S$Wn`Ug+"2W_FuLE2#1R?aGb[:*e,
%['Z#X@m=r;SRa]!QF98ZFkVf7`fN"oO9geK2Q!8&>dQC$/NXMD1B^9'm:g/(R129b0kX78Yt9ModpK1M/>MH?2M[5<N>]C/9)]*'
%*C4QFA9ELWNUYF=\uW&29EQZH^J2M_*+go0<:mt_*[&:Z*eb[bP_t&1$j*5T*ei>g%'612&ItTI'"I8k0fU$L/AVlf1)i.YM;Y%_
%7+4?H0QLjFS\X>_!fq9#o>!1V638H`kB!k\L.c39D\)EhOGP#8dDI;Wi.RXI-0ZU1J78q\(kO/:@D?k#`@?<EMAIL>%LOlN+dg
%#V\e,pRO#2#slA2.O>L*B9P><\gq;$@i.QM`h]8;pd1,u>GhUHX71hLNT&7d([;R?0W,#<A0>pXRg(F&a[!k1T\H98F-sfUU-MW%
%(nqRQ`'">Rd%\(4<m(e9&*-!ZS%o/"'1rM3?\X)a6VeLZblaLCG=)X&T+*n)BL[g%bd0V<__j9`:!#hUcAc7UBS^o*+,L%aK5gfY
%35!P#5#]IOnK'GLSk*HZj1mmIUUmS\1<&B<)bNH:UTO(2i`^MA`Gr'AF@1hDNL#q"oeeZ'^_^6nn6p@O7!qo__Q1eG<LkL!NlQd[
%d:<D&n^6*)[IJTudP"PV"(i[Kr8qqT^\dh^fC(o;IeUHecURNQprUu4J,=ThXR^#E&)Vg4Y<RO,n`-Y\n")`=iSVBGj,NtZj*pWB
%\N-<?n5k*eqW'_cj5d$-?i:<2qB,4pIf\qt2t?%.2ui[!bKW!8NF2Y?djNHkgA'mH\,$ob?Om[9iluNg\TAeFiigep9@'NIi3.?L
%j.G9i'g)^AD.pJ)Qu(Ekn97,*>%rFWA5FhKlu!;o5H"L7qm;i"]mu)b\bYJgD-;Q.^phA(<d+qpbR]\b`HiV3Wd$^,d7M+?eCJMW
%XcN3kosQcGL[.")d\ViagKOKf@-4k_YSiSdrnB:ngh'[8gF'O$Q[561WIO*^/`,XJlfQbMd'D:ffJU7iZbH)thqZr'1eMY4CNk[<
%eb0d1kYfahet'P>n9g-aj_d.Bh:AsbZ.J2;c_eTAI<4^t$e<5`Xm9g0EBBAed\WP[XZIP@rE%n8nW)f3h(#b^JQB/FH(f/&ZJ=U-
%Ei8*]idLG-GDb.`h*YOWrK#:GmCZ19l2c3jbIr"6B?]+VY0hS\r"cg\pRa+[SC6mimQV5+FffNG)sC54L3g#MHTf-oO(qefn.iKV
%]hKZ^D,m,j2lYDMNS`u`>Gp68@ba.i\m<ZE[&L(Sk9fkJY0f1(%;]g:IXCC"Rp_(iDr'[L/UBS3kaPe'i9MP)Ds]gjW+gVE:7lIP
%la]LBg`"gM%6GR?]4NKR*T$&P/UBS3kaPe/S5W\KgtKTq"ag#f`H:s(MdF^eqYJdGhI_jt\$n!!%4'iuXZLr;oY$i3oZaM]/IKt]
%>N]3IW,-D.l16KQ4kJne%L6STUAoD<gT?N0foC3QcQ1d+\ON3/rE$a]qW*teA:e2MMjTP<4oH0.4d#cr[#mR91]/kJ?DI6ESo8CU
%8_!IK0.W2<;'k82gdY,]j<5:ggTh/:oF?_nQ<_]d$oM:9849nQA7SpK<q)8fJ9gNk<dA\!Gd2m6$@FoP:u;Tl6+fcQo6(<7S<k-6
%88.5TV7?Rg.GQU3HCbE$[\>=/7!Ir_4lt8W!r%j92!EgH6:M+VPo:'X0rRSnOYe0Td5LsmEnW;-.:$tRn$&%V_B)[BMO-Ec`6Ekf
%HI,X%1AGUJ2D0A'e,XM`d\hbip+1"_d*c=cq'g_mM#n\u;?IhY\Yi)OGp>13Ma=]2:3DQnBu*G_'?M<1`_Y"Cb+`/UnStL*I)I>R
%5aT9'o!djqp\A1MqVtj("n#;+M@9miXg2Us<%o=lnojWEkbPXPdf[ha2p.1b9Hp@N2kjWlWVqg]4ID[Z_&F("01"/dQ3Z.].(Vsq
%YZA'74LjpuRNV)9:m7_cK<(,ke4Q:31k,mh?WcnkHQ2a@j#]/3CDQ,Ibn)q]+JVG58Pi4L1,dLGZim6fQZ_HOV:+DBgZi&UimM(>
%@Wojd97X1kfZ%Q/i;O*9&Yta)4$=afTR,PL9;hZ@GVRPI6>'j.JA*6r\429<7jVX9:0POJ$MG2_F:n?@F'Tf"(XeaHmf7Za5G][Y
%<gfIu.GB3'PeD61b$N+r_6HY-3Y+gdd!]](3dV)6q:o"hb_bX/+mlg8rXroh@Ws'k'NX.E%+l2&'-_P2&:6Lsqgg#SM?6*;^##-5
%mhFQdSD=_,Q(rI*[6R7E:5;Oeb614e=BMeC1+QcIQ'dqa'c;koD(Yr*87]q3Cf[lt$+2ai]@)&E,6Y#e4m6*O3q/MjLS5ncM&3qe
%6/k>aQl@Ub8D;i]O@\;NALKf>Mq!hU!hN&D$/JfPXRP.7N*3>XJU"?E>EUia6daZMS7Hom+:BnR0t>2:)k]E(8@e'-XMKsV-9moW
%BGKPF;[]e&_!a<[>t?(h\ET9[gC27i3$QS1JUOun\K;ViSGIZ$0=Xo6me($p;[&UoU(BW7&RnFlN[W;30`sT!7aAr>BO:aKEm3'b
%#eQ)?M$OKln"2Z7E4]50lmteG1@QE<H.da[(P;qp%W?37SJdY(mU+R^*cJUWW9WHfS,'oFS*?5&nrC.d0.?DmH=G8-2AUN[H;#H5
%M!(03U'n(re1.;,\37.$PMU:N`-bt!o%fIao\dE!@Nk!qKR-PClXOScW&j'0lnBR&d(0J\e"n>`,%#'r,3YghUoAZ<[EYU<N)&Ch
%#&Y4rSZ3Wo>NcceVa-,4SIQ117ORR:+;Sid@eEo\8Vn<DK9PRVaYtqee]nZoH<3N$`gj"q9:1,ae-UAB+VB$_M9YYi!FcbJ%^c>i
%fM9#Q'169G4d[f)GP[h_%O,ZRGP'RH-<>(,)Vqr3[YlHTd'qrG8;PqX+Vk[d.eC]E8dfKEi<9H>KK]`986X+lmYES1nYtU'B4dG*
%)pW/AUXPU])@E^ij.DIrT3D^_`/oLVV*6!,-T2)D=(g8%d-CbVPcjbgfI4PdJuk@'"f=b]aVD?=EFRX6R$`t"1OI,5`C/E^M;'Ve
%W$%%@Zlh(D%4Y&I8Or^,J[2+0pun",Ej($.(#3I@9*;]RZ+Hr4%g'6R@@df<r<b3c2_bmUpI2*p`SkotHnjh<R5YmXp"U21RYI<f
%92u"HpYi]cMfO\@ncp1A`hU'q[`HmU.2/7g'hC"%P]?1J8VB@9Fd6A5Peb3`g:@?s#^JrOe]Gdm20^65>piIdie0jlPdLPSDe+QQ
%`h&#5AC2T[oq2`;CO9J:C](?bW2`J!S8L?j0P+]'Wq)ft,-!+ufU6aiZ/W3N>#AIVDh9IlPZXZYIOQXNCd_&B$WEq]Gt(9`CePMp
%QPUiljd9&ajCh^ad1(rVigFS</4T3tYlC*s6_+FY-8hGa7`%S-60/ul4gZL5&"?S3g$OQYmTV@G@7QRJ]kaXa/@$><`SubgM[i1o
%^iEc`V+lUTO<6k-%&Llp&2h':'X],B5\D@)<1=/]n91K;X>62><^S.?%Hn15E,rSSS1%7b[3-YnD,&`GQ_lkWC6lOHCL0EEFNh2'
%]<O!!f>@8K_j%:\D?#t57ae?('h"Pe>Euek'#]\._O9[(^mD!R0PlH@3m-W+--3-tl='cT-CSR.VP'96ao+WM7WGtR%D4d>ZRjCT
%"/*Q\HG5c65*)\E'W@=6/51`q;qu/q@''Y(JC%4G-2<tc8,guAeoIK:(-7Q$k&6NFNdH6EKTb=NR`MaR4ea_$e?(`(Rtl:Gn-O>Y
%"(/l%/5B3K(7(]Fb(PVQ%7gpnBCcL;>\22G0;^)p8U<'(BpCId\!34_K.lN$K>!6tcKq1_@DW9UIU7"t5X/cj*jp_ss1GV>"$)>e
%\Ooabd&uuMO,KKb']!Eg*b%$-1NT.;:nEt>'KS$P@LSh;)F+_bFj?ib<*MepAia]B=L1dc.#U4u)SCq?1N'Kl9J@2gC4enFNp5Wi
%@q%(!Z;A+,e!6i>%Y87,/HXdk^AV*CXu</_LquKMlBPpBk,rsA%hUn@V+m<5_2M(ajiZTmN<;U?Ls\/dPS"]gV@PGDm=N44dGP_8
%##JQV&4'-YB-q'XV*5`nQY&9Qd+tVu[ET+^ALU88,QPPYH:FA=9m!i!O?fei6d)4r4tOI3`FM6j4W/(c5B:9qR0L/*ZH*RhpeDE1
%Eb)D*C40e^CFC[O_o6-tibF%(MTL`mE^U$R4"@M&_HV7l&:2jn"PAZ<]TBqIP&@,`(;&<Q[OaB9dk,+/U-oXM@'Elah*Mll76qI@
%OYr9O?3`O]%4ek2Bu9U8^0r4!SZ64nDAImHCK"`.9@.*XbmQtICqOg^9I*KdC<0&a[Q,7tXLB^-0kr"/CN)7ALfi#]h.sfg,3K*'
%OAmZAd27jcC<tZ,KC/W9C4k65;KJtr;$AGZC)@?Eb+>]bNob-i*0UpGD(efU+E;]c\Zrt>08>>GRn2td\L,6k;dj.c[758DPB5K9
%8=ZXB3['YT:`>&g,aR5J.9I$OG@oo&0,!am-B\Q")%FH7_@3p)F[Q)(J)1T>#HLqEqRZ=PQ-H=gPFFEf*24:hf%a5V2nLMDg$m.N
%gA.m_ENtY;`-Q8#AY4gTD__peJZ+a??P5LT=k^Yq,CAj+&A*1KhXD_nnNbja6?@76S&&QO2nKUu-gQ]/o7;Rb!=%S'8\2ub<PEEi
%VbhV!dY>^mel`JYT=Z\RYn]S6`*t\MZ`k"_;_@$5>a&oBnQ65.RaB"\S;PfmQKYg_O'9-e>32^l$<K,m$=4.p#1l[nkZfQ9.<*\M
%enP=0/3N%A*u)s<[HX6%&eVGaA%A^+LZ[A;h*K%@-A&*c`5!qh+7s"T^Y*@U65:.iG8qP#7Eibd6P^k?!?:LWR!#t-Yq#*DB:"5&
%@aG^U8V'Aa>,q6m_9lu>G[cpJTl#cp"DEPee!6i&GW&adfa!7pq-3e).5bpKXAQN6K<\m[:30[_4pYeTI8As5ClG:>Km2$e\N%r@
%`#@X0G0/NaIu)l5AsXe=J?Np4EFP:>]gI1D&eXa[F/$")6r$C(\rs_2kP:6i4fk]K)GO-EP"dfT#[-acVtJ5S'"/lc!6I&<A?NIS
%=b19)N)jAd:+#JiojZXZ=nHpTog--HbQ=IqDIh(h\M/I%b0*@ZTk`,c6c:7.Klk(r>!qs?9+=TH(r5eH5?curjF.Apq@dud1mu%i
%dQ]jV^8$0o8YA+8.kHUp`R*I@aYnp*l/F\@Mclo/NHML=Cr'qG\=,tJlF'd&Z(f]_0uP-e/fM>NJDR$Sc@F5t,#!rJM(Z-:#&R\C
%%^qf=6t6[#m""T$Z&4%//Pb[YI]4OZR;3!96O&OGX\O[gTLGu[YssV:.1tQ:7-O@u,,%K\+RrLl?]&Zmj$&QAU*eI1bZ4l;L1guR
%4[CU@g]s7$I3oS*(KI=@[NZ_6'Utm/?o)spjD6LK@ZN]/f"b\"_2bcY`)Kaf(S:pNZ'MQ*#&TaoMl9l?F"X4fWGt-Z*[rF^p=u=:
%Y5O1B[`Od`Qtm&r<EY;T"Fa`&D1?#?Eb\EM"4+iIb"K./MI.3K"F>&a4V*giar;bS;_\DPO...j<-\$T/6+@J<R"cGTEmfAM`SRd
%9WSCjDX&/PcOC5075))a4j.h=7-11JV/m7(6B^/nC<29%NE/DO8F("7K,^QcSffAiJ<G\,YXDM7d`f%;0W>hDeV,VRiTZdtDh:U^
%0o!q%5ZIV.&>Nl#<+MEdV6\mlc73RJd%)?qB0HsU,d9qS4>6O*:V*sEZ3]6:9Q3UG,Ol;Q>6V\W',l[_,O6([(2+%I88M>GmR?rk
%U_[(`EcG+qk?,=WVa/FZF>"Rfcldr2\V]fj>njNKN,g7(.CLC'[BrK8[CGi$?&!3E8/DXHmSefU5X9ChDI1V&:BKj#^"[7Z(V5EK
%;0?65Br)%,jON2L.9p^W[kE=]<28lMLhkVRh1mC98!'QH?sRj\ONC.K6Ec<'r/lE)M6dQR2HP#-^q=S\Ha:(0pEBj3XZ5#Y`;7@_
%!F4L*Pae],]SKaS<DWB8>B/_c'gp<A]<HL*0gU8b[A%Cq"pET>$!I(\h@@.\Z83"3*5&[>Y?V]e&ma)N5jc"%;FZo77Uki[k_5cD
%Cds)J*"*G1),@b?;r3:]]<MX6[!re2!rYq4hLS'\p`T;]N9:c\G%\4NI=@P8V'rZNce60s'>?6+EX_<K%$!U6Bo=r1e@:AcD^sbi
%E.KUnhTXg>LD]MS=C7.Jj;>;Z"h`8gfDN:TK>h_Eh+e^p*[BHfN7\>DB/2&p#9*5.S_,POIH8gUG>,Ec%TbaI!2!#fMp8p*fK["]
%EK?&M]jp>%5kW?V@@%]j=WK.rqkT\6W7kFLm-"V;iP*@kBeOLH27%t:lJ!L7Rp2@sQ(:*="/:(d)'_5VC-%JoH.3A,nhNV0-Y;Q!
%J?.;5?&Ze5?H@(8`*u+GLof?R93qB2TmlIKYXpY5R@7R65al0GS=-f42D_?q))0K2qF@STR7XF'/1[*<-/^_hmH^X>fEOfc>8ANC
%*33)p(5+iVDGO_`9r9tFeOS\;7.4.3P\X35F09BDG0`Vi1m',_]U`.4GE._FgYJ%2!P721BAK^m>1Z%`hAk'4r.D"f,petsYRm@I
%4)B-c;j-C$)[8C:FrIBBI7&rbqBsf^"]-RQeB"C9h-6Gis)_?i$D?DVJjki#Q`Z0j*6:dNlb\KWL/sge4,'rd`?:#+1mP&hC,]JY
%VSfYM5U<>9GItr_.Me-*kuF_uJXo&m.V9<j_`AgH2%a5#1E;A^ea$rNWPLW$,UAnr/XZJ@f.",D"PC\*.S=4-ohK_1MW\T5qRpgI
%q;asbcRWRfOda%4qP;sFn4,FPqL;3kKJ*M_/P>!hbsDNN9S^]a'`6JI"F3BKq:PjbTbH5.W"u,S8.kp^N:<CnNfbFf%5>)HO`Pe6
%`!ogMA6skVKGnmZNFW>&PqmIb2g$KD#==cbD*dZXef;1/LmJ]Q'J.P<fH.g0<Y>#JUbT,)(5*r5PtFX'i_C0BMBe%V>hPRF6pN+=
%QJelI!(GAHY6*<R9<ZMWj+*<EMAIL>?CWSb7VO8VV]9j$S6Y7bg,P9T4($3DWK%1)!<P,3g4??7H?R?^A6Lpk-ntMj'
%W)%dY#himgEbV:ZN09:'K&:X'om,=uQ7<>u-7D>'<jW\X"qNF@:V58*&j8?K^lWPuea9Ha)nSkhK<R&>(u$Bh\6IJ7;jsg>1uF!8
%ZTX"S@cB0$E3$K2&DkTrOkR:RV(&2BE<F=M$!LLMhh67%_RpCf]ZjRg6&DfPCCS*Z];78jJA"C0i:X-[-8CZ_0K2Nr9mg0`d!?-u
%%45L<?OSW-Z&L<6'3Y.1JVAHOTs8;O=fp>Dd9rL[LCpfmjO9h9*l2Nk+,X!,!uVR)-lmB,nbtr2J94t'*Zg:c/L"t-gBJ6!%G\84
%HB,Is"q09-NGr2E=Iq7YF^Z86YG!s2<2o#1D.lctBEeTRAdECO[1T#T.uY^"p*\e;6$YG:VM1ri9O\@["ppp;*R6cj.>s8<CR&G"
%@bJ/A5]B=V%BZ^oL%#i)R<Z.W1'-%jP''ZTKIDD]"S*3M]6\F3nk85j6>Qq74,J`&\b4C+ADUgZ">h9X3fXd,;PPee,4cmX/+7A.
%Fj`9=Cu\-Eg%D0N]e&fU`bq^N4O/EkXjH4R'2OiR@kVAtii0(C)0_BG[EFbg$S434M&BKp$e$5@B^s)6P3PSjSDZA3P9npp_(B@-
%D&-oK`K_^okVCeB%DRmDXBPY.f!:6jhb\P6Cs8JWRKsueMP4#"SAaJZgUTmKXu`P5<3sm!UWO[2mY/3d\60Lk`5F4(?cO"*CP.tH
%OjFgTKLn$ej;F=fKK'+O@C*&Y70CO)0.'=9\rgZ353PqMW&e;VU0L*<+7Ogb\`[+`Z>EG$5:X%b[R%``2fh?ZqhBYbKlFPSIoS/)
%N)=43FL>q\MEF?AJHQN@elBsD,P!OBI'>+BX0"f:_<c8"&#i*8.G1D6k,`Z+Z,gnIJ6F(a(F(ARDiAG-9m,seK2a$E$\;^Ck\-tZ
%!-i]rPSq$8]PJaDS>"jFc`U"6D[9=0QEfe[8]MJH)gkZ3ScB9uT"o)8=q^`e$Eoj84[h.R&J-CUJVgXH%2gm9-F#-2k:Bo.7u(MQ
%G,F1QoIdEY#.]6-XZo7QlmD4-jmX*LVa-h#_W\iZ.Q2p71e9+D04M90XXPKVfr)bQk]g_&<TR%YTgUYU\&Y9R<a69+5dU!pJg!4m
%2b]C83m+;mM:OLe%=8`NTc%k`K8M(1_;*V$[tW79ph8#0Kn6U/F'D0.!gR@u%6)EpC@cUK"#b+6l*s/%A_*ZXB,srPB+DeC(S>h[
%:ocUN/A&gKR14(CO1u<u&c9"N&Oh>^K5P+hQH2h>*e7&&6"e_DF0ul$(cO^`C8!U;YhKmJZI.R<RbmhRVbec]H(*j_,hOiEW`4U<
%$2qE_Fhp\s`%tGqA79ehf!%ofL0G?06gt.S6@)Sk+M0I%r<Z3kGdd/U;:dn`d!X7[1(Ob2`mIp.C/n],Ar*B,\t,%OLnS"ZG,pu_
%82l"#o]d-i9(Tm_`.s!jQ."n;o#5&[=!BO/-]/%d+Uhrp!>3R1g_.7FOSL!#6--d3/"PEH>-Ca=p(>b8LYt>-g:9Y>+A9+1N7$rt
%d$t_+RRi#JU`H&)'631Y1)ZqLW5(RE-]B*5(+kEf.7VX"R4j.1GpYS)P;"s#(ph&UWWYhp12.'K$l8AlP2ko>d`'d3FJiVF-'X/M
%_0s;*Nrr[lOg(72?4/?A!cLc$2$'<q(W3U-PB&I;^<`(9K,V[!p,%)0(5Ib!1ne,1,*%[-+pGg*;ZdmB_^(dS&^@Q#h"Me]?S_pc
%4BK,PQ0.n"(;u@M-ltb@FHssQmh9)*-7Ud2RYV)ajVa+[3,&]G]b?#9FFr>RRDb!VgmA:l;YefepseM\"m2)<"bF>M5#KkD`U44Z
%`M,VLaVhT7A<0g'Ka-I/pSpXc_7lA(WOto>nEf%:o/Li>2oGSlCp;F@FFUDqR8,Fd*Nbcon[N=S,C3@XD5kE:E"FN5`MPs`ENpEC
%?P`M02*Gu[$DAhQ9cH,Rb)+EPn\R$"$pI2ddD[6[TKKJ+>6VnbKt_7-8l`;,n;b&f4hBh,J^=O?-5$oF:)3WJ6)#3/mF=g$^6;8a
%KYD.l@nb<lh'^@<?&eIl<A.s^/J$Oo1hqc8pp,[S-kMs,[RBBGQ8HFL4h(5p#_<_]!?^MXXL8qRXA3`CBrd6Anl>)LM:+=7M%"g?
%BD7A1?I=s?)CR'_1n>uqa\8ei0i>-ue"$.:W6Cad(.otOU<u?umQPsU&E->HfB=&"f]c+]6Sf`OFLFInN@O)Xehnc5k/'NCeBf+M
%?`;uXRVU3+(8p@ag&$^C+?!F@?'X1kS!np"E+ngBk([,G_5`JLi96\M$<C`oM>:o6V^#%<AtB9V1J]8Xa)g@F'%O[iUObgS9N^qE
%++bXdD@4^'4t;k!C(T]4@'fW5F/Q@DYU>&j&9B/M1)Y\r!4;r`dQ5F'0s-"(<EMAIL>"Z@nKS\g=f2&<S@,DSBK
%hGO2Y@keO1Bos!ijI8/D%8)C='2<7*4$X.@kuDiI-$$K#Z797VTa($DXD9077in6JThgreN=2N?O]Vo,M[hMoCT[\(N]rDhr_B(A
%dU:F%B\Q3<':o%4QAL1dk'Q?&4b^GmFh4c8_)=?.9G$$snk_=m.&AR01-n=Eb^']O[tG&WVAi^i4cE`U@/MlCJd?-2kU@+T(NG,]
%_C-fS#(lp+0X,Li2M2!.)D$P.TC5=E%H0K3f$e-@EUl2K6H5LP$^N"6$.DQK\8%L:!oZT<;9ZUZeCEq1><B%;4lR$nFG;CZpsfn(
%V8NZELe,fN2EWNG1!A&i7UOuVY_7"eNcF&l9f9Kl:$j:%asnOk<,u-77k\l6'qV&G&u2YuLmgceBq`q&jrec@`uKbM-GmdJ@Mq[g
%jHH-kLrK8-h(4-5A-t"h'1M;p4\am!$i<BG3]\N6kk'DZl]B8Jr1laTP%MXBA2,miWrDf_@h<S\qB"_3Yb")ra6;meM4(i_rg67M
%Fs<(qctmJliepWJ[bR?c2Q;oJb>E^M9;d<+`[*#r&#fa7LXi.77KPrjZcrNu#Z2a![,;L^SpIgQn\qeR;.X[?QV,X`/ktRL4R[C9
%a3eF4F99Fd/Po?1;"aoZaD22/qp.(Wcd>E$>jO$c"#CnTRNE0N"\eF/I=pn-r9AM,$I`_?(nD;\dS+fkL(>3'KTNo]BJ'XLiu&]o
%&-K7HBEHqh]OeU@kedbCH?f+H90E\4%j^!;m=1;U#Fib\*X)R5D$_MlPaUAtY2JMH./@0ip^JP,j@Z9p$Y6uQ%#6EE161:'mn<q(
%-e?Nu_ds!Ie\W8\k@d"oM!%#?_L;)f_O5K*E$[+24rm0'jo%-'J\H)J"C\bJ-n!DRfJsrf8pSPr5S6=(-S,]g5*QGW0-%j\E46>q
%/JcXB.WD8VM\U(Q'L>&'-k$_SFqj(MR]<JEE0E60mQ<rMEDEgaA?XV57aCq6,E57&(8a"l7L@,V9b:qZ:&kd,5QFjoebC*8&@mQ#
%r^QOO!QM+3[6m9>9$[Ot3I)R)75-,m9>h@R@+_&*d)g9hOP$oK_<3\k0fY0@US,iR0fU.ZKaii4@VmMUfg*D@8,$l-mF9kp^/X\Y
%i8!fA9-sC[Uq%+MQImTq@%:D7F>5jK2HU[4/!E/!28NPK`(Ng4Il>W1X+D;:Y%L^RS?.V@_uZMJ3J#8gGV"<$<qk=&(u6=NArD>g
%+@c_X!:"n"94m)mmrbl[Ca5u<I"_dV7J>-u-EH"4`#2*J36&$6k*VhPW/NX@pdd&:=!:/V/NN;/Eu'HA'2Jq<89c8tQgA!)PA'/.
%H<OJf.>YjYlkq2^-]:[74G\ZhULn4&)o<Q@Bu&^o9!(H6-m&Jtc'$2"/CRR/D43u<in6lP1%u@dl_,bqKd[*qTF=-fJj[Fpb.0'o
%6iR&pDrC#EE.8(1kko7Z$nhm_=M41+AS=FDi?t(]KHX<r#NC2j;Ll,t8]#77aQXfVVd[Gp\"1B\Bf:4`,Ur+i'Hr#!l0`@mR`#]"
%\QN$`DA)'='X&#9_U44/1W'@[dPt=R_N#WMQo/cTftNA48HI@85**e/e]ojVSPUQ]UpGs@75:=WDA8nH[%:1(WVNQSi=rg)bUl[$
%`N5tF7*QS7@Y^L_80:c'['cdqBF8f,=d>/9Vi2I&CdHS[h+P-k'_Z$F[D")+XYOdgPqea8DYH.`7c#KF<#k#hd3MWtO[q#5YbA%U
%M:D`k7(G2"26YEI4kCG6pm/40Ls&,Q2Iq(?$c`^H[ZH7"66HGu=ZJ'?IgX4N0bf*('m6`_8FU)*^tf!;P1_9.k?k55?%!B-9%N&Q
%5u%2hMpq$@&m%pi,buf$F_lA7'R)$m'8A6KB]2o9)Oec"==]hi(PC$;F5D5;50HAh3EIep@l,;/3Hnf0q*<Y!NX*[GLofG>eAKHd
%_j$46Z7'3B.]&;-r.\:U7A^s26jq-OK.A7'BGmSFE'FuUAhfrC>!W:g0Cgr,)&Su;#l?S,WM)]eL%B0rc&1hm,'c"m&_Um$(8]WV
%AI(J$ppDF-afNd/)lg4aR?AT*N6u30io!`!19ZOT-n_lGS6sb3Q6g;8?e-221j+[D;-&U$`ABMFPP8/;31,:q*!Vcual7.cB0\'O
%>tg$m5U9LLI2Tkm22[M.2Z05+71/%_bDopLZ"-*A'Zo"mWnG.R?:.Tr2]=5W;:dN(&fHsl5mTn7aImnr--2):$ig(&pbJDm;A8r"
%`@.8/4H8dCdY*u)*eru4S#701HRXm?]+B.@SC6EdD\<nhT'%ABI*1Fo^@!m="3eYffWi:DRG]"m\WE$]Qt7_9s!B2k#8?bf:+]Fk
%cpcrb6$u5"M7K7kJ)0#-JKcZPg5io]SCds*XgFrhIpXbsi=\$":T9Y<V[;dGDPt"-g495q'7OB2"R;EK'U4!2L6hb^9U,,dcoFe(
%@;8n\=j[25&'/\pOrYEMG)HA(U9djlWt)ChckOdZ.\dm2H&PCnLnbZ^TB=/PYXr.Ik*Mole)kZ">V8m4&Cc1MW8AkC)J6534haNJ
%N\)ji7ADuJ7AV+u>)hruacE[u8';l!C'IJN/@.!Taqj?$ifu>,"EJYi#o@SP9Pl$DR8Y10c@(_i69i$]+.=G[@)n0bgG$4FH>,'e
%5BZF8:eW4`c#;tQLUWXD%,EM*[]EFI;"U'bBFtIuoT^g*cjH/(740(%OW5MA4jIb&[r(Ui:2S`0BV*Nf_]@Jt]<>%/(A/s<P7NfH
%l3.WG()M-e$HJq^J5t'8abj1"k"5#4fdCCH-]8g9,HWf+"ZB/.M)qSQgZF\GH,GDRR_r*';R#WHVl_2,_Rb@Z0!f)`6=:NU&k9X6
%-KRTr7FXTh*[H;.p4@Jjh&]'qK!5k&;4MpY,Qm^WRno>HC/LM&H7eN(e@?pZ\s_bN=VcueY*;1inYk/6NNuCiSlVQnl2^FbMU714
%*@+-lilNKE>\^$(<Rt<YAk\ds9ZGDH!:bq8nm+]/X+6[A@I+Hk1#CGtc(!R753-7L>[k\-[;WNr&sga=QuZ4JK9RsZ9m/c3k=q=K
%MGj'1arPh=Jjgp0NNlU/Q8u+5M<a*E]ZG3U+t+ThVL_*YqV[XqhQ]!d>N2Bf0mQ<'1cVQknJru#^Ik,:]rAd(g44CJ>BfpkMde,u
%YL2N;DsHf#DRs,TDVaRK/ZP\_^Ik,:]rD&-OJ4P]9nuO:q%c\Kj/_h2epI)a><B:#DJn:p__%8;ep8ut^@eB&IVVge$\uX4<>%Fu
%7GN$8BPa-(\0T>,8Oma(8h,Z8aRSL_ThO:jhs#sL7ZY44N7J]?(Can)RX>s/C8hdmO@M_1BkVl5rI[a?:BSQJ3BaL=Z==!U&P6Xo
%4aGq@1<VCgasrQ]F7_jNII3_7O>fT!_Y8AZd<e$k?!!:Nq=Eb"@_CPsFe);#Cms)BRp0Cb'K\NuTEj&p2Rg1cQ[1R!me"c+,u+$o
%f')l:EI3UZFK&nolV??mg2X4Vk15P;9fKPk:8`Cr\*in5XVY#/k$.EYll"=G(>%c`T$=B!rO;)se1iS"UANpT%#/<]8#@cXQI$fu
%8OmN&U"KPmWVLYtNkI12^J,sQ[KD,eUnF)8>c5mfY1I`ShWp;.UT'uamB%VfoJa8IoZTM=No].A7b!)sdK=u^f<b(CfQjrM1q(k1
%)@J%n=kQQJdih-AH:8*1Fi#:<$-GbRpu3Ph?0EGiJh03l7X&Ng3gg)6EJ_[^:);G-1CDtk<bWU$B@q(mBuhtu)'sN#l7'ORH=g%+
%^a5F!Yjf2.9VeUIinBieharE6DCC*^/W<$\Nn(QJ>KKN=qP*LhA7sthX0$cuk0nL]d_TC44J#-;/j`pK8ig(UCcUuJFKK1slbXua
%]61&(%s5E@R7`lR"hBlB1L&)V3]+/3)-j(^j&)^5:\`;"C\6JH:#e#7(Koc5FsV72Wg-$N9E^Ih@VUM!@\XRfi5h;Q"l+R\2MUFb
%7eSo])OWRoG9:)-aq4Msn+e0CR_aGHZ(OM7itB_Yb1ILk]J8#T,DEjGAA@0%Oe?JB@G%R_Z%rnQC_gbW$]C#tf@7N.+e@tj?b0ON
%f0$lT[W`]1"1kNcb^M`,hLZc0kV0*$>.:_-Wj3<S<5;^1:J][hO:0f[M,%JV14'U&;`W(U.?Vmk@'1<XTin*<QtL-SFQ-4HM+qZ(
%.6>_QBLYc#5a\mA*$OY\F&C>-Nmj:<(IdfQYFg]]LJhm7f*lp?e_,d'LZ;(Wce/%@R<_HOMMlrV*0b/AltTeTI7OPtF$PnJN:AP&
%2kmH)K"/'6qQ,Ji9!i&a%rpW!0.4Y.,fLZs<,ZiN\n]bFr55%'PB/TSXq?'T_XOh_NuP$T5,,qXg>H.4=)bHE(?cCQ:,"(K#8K!6
%O!VBeHf-!k^Hu<U`-"&E3+^An<]R@Ljd(',r66:,7#.c$^b?+m$R>m'K]Al*<<BrcT+TEQ.g>STrRc0do8uuL'I=))9_/WDa\obu
%edT0#V_*MLeSLF/I3U[11FbpT@,u_Ve'%Ke$q8Wi&rt.I6Wo9jdZ?uug,90/X4,Hl:>8g4';>]IA,=NiH#`eI_?&,,CT^hS"9S`-
%G]'P]I)@(5DY5MLEgFsNl3?R<XO7l[Qh2tc`/+hgf[0i=YW,-lfJ#:uj*Hn1keFNcA`ONdWa!&HbdKn\?V-H6&rWg=iT:Z8']g+j
%UnfgfP?'Lj2r."!dcT],hX1Ge9P[smmZkVKD%PMZ@mDtp3iRl!EOsJ.?6Qs.>#:r:;J".X()!u#lWn"n>N5mV716I4.So#EJrlm%
%WbQQ,(0Sn7HW%<ubWK<j_1nGfnR2+Y*QJbXm/nS[M]F4(a:Cb5\]Q\0&373V&g`;c&#\L+*_3`G/&=]"WF<H#e<c(*X)<cp(@Ba"
%&2^]FTk=J`NT`qH7\"=>=ns9_5f2^bh'8HN@$Z*tK.HTW6W<1k'`&*b:u@c%QRoW^9lkrM;R4p_4a<W8XGBg.]-Z#)r6"uLWA*n/
%>V,U0XjZ>'S>I`efN912;,,_o:W.!<Hs+*U[J5M4`QQaS/ctb^<'uYJ.MFFtUn7&uaGO_hr"mIt6ps(G_sZadoIS=l4JP&'#5`Y;
%=6_;_Za65^cDUIldHb6$N&Y5)&k57M8Sb8p8mo)u%eRm$\S]I@8<%>Pr#g6"4t14>2%Y#,M6A'Um)_YOK/d3l5=IGB^46M'a/E2%
%$g5pR)FE"&>rCXaXG,&bE)$UMN,+AdCGrA>-7=A7`)0;D6VIKuQl%@s(^,L/eR/WXN:a@oHV:Nhf@G!?.+qa86hf;#4QM99g.:n"
%,QX#_Ee.`!d5d8C\Xp0_,\Sri-<X^2`-[3VXT"I#l*G52Dmnch>#!hG=d79HP52<r%-VSTquU_jifE2"IW1H[(4_V"d-5$^DXM1t
%5qfG&R$D/4/QDfl$W69g'_10pN>C#'29HIcc2Z8$jtX5^F2tO?P%Yfn%iFVLNDU*g'V/1O#O#=@/S\eV"=40Nj;L,>,Pn?1S&L;@
%\!)p(,g$*s6R3UiAkS$W\g<aM%0Sc*#T9;oQAcEKhY)QSbH+TbR#Iib'r[Kn90)jnZY<NE9ba5*V6VP9c'r2Z&MtgUY%CM-`bdt*
%KsLm8HAnsIKfK-qW7iZ@(XFO-popZ"215Mf`$Uid[p`-5%RlociMjC8(HoL(llbZiCOD@#0`dk7KXkelHT:4++hGYh9i3p8+Znpl
%(3C/iW(YKg1JA64aIiVRWZj_caL?,;=MY(VIYXFFectU1"A1iEDIIJm)fW-S:8LHs<lUcHe5#="D\?rCM6<i`MDGu'5Y$!KfT..+
%S-d">T@(!%-n.KGmTi(!OD_7)P?1Qkp[D:gn4q:/cGZY`ecCT&R9X=8QdB#"9`Ul_$cC+@*J.3[-_:!?'3io]&9sr7`%JI??:I8X
%lkCGnh]f\13of3&VZ[QrUCt86[ffD'9OL8G)Q!Db(-(E_kGQCZ7[bib+Ep*%L<7(br_j#S-_Wrb.QqdK#b80ICZQ8?`+'^Ki`mml
%L?Gd"'[K%:R:3kj27g/j1WcGj&2mD-V3(srCjIKbVgp4>EH2ukSemkh4`/>0*ud#mnJ[s%dMj&`]?Y_`.mYscY,^dZ3GuO^F,doe
%d<`gsq::\1^mePPqKrZQHthV8ApVq'ZGOU?[0(`=,k1%-ah4?D<uT.ss/TL)7\LnDIStZ:H2#SR_ft#;ld&pcr:oQp4/!^&rU0?j
%RJt*2^3fV6mAk/C_N19@2[;`ArT]Gi3luF&DAF_:q@il$5N(At!nn`7ilt8;rpcVIRr@4X^\U$*<eS0R2Vl_5hg"/$-"/bo9LC$h
%?]h,DHrJA3.X$Z^Jp4nR5D/u4+NME2$g"l!3>$fD);"G2Bh*]),1+)thLMRj*C<!\%BeJn]qG(06i_hE!Oj]J#N=fXa/Srm:?ZUC
%Op.&*25>ak`/GWq&1\^_:KiCkN*KUD_2k"?>6A6&P#_tBaLr!l$1roR@.7E6F?B*]&00WiieSeD>_uZ*&a@Y5X+@pc>dG)^1KT0t
%2:4_4/Y,XN_s*2\"hJ2Y%$9I@BV1[3OhC=[h_@a<AX<(mB(E*Dc0><V3b\tLI;I=R9=28br(B'WoD`h7-2??A%Lj+?Vjq9a`+;G,
%3h&X>ohMY8;<FZQ!4KN_FMdt)`<>MeLSd$^]$b)QT1fRm.<,eC9/fd-5,Knm,V\2oc0fB];[%U!d!nKdE2&HPTt(GHDbMK/oQ-if
%bT"EI<*L:)\@(gr+nmDs&U<V`\?e"'6^*-4CJ35!-oTbKd3k8j:l+>_@a2h0X&M__`fme2*C[8-MO3(_V.^sm>R"%4V#jg0/epT-
%6+RAh'Z*M>elq-DP"^\!?'L,;/99;ELY+1b&7/t&B7)&8X,gtp]_H4HcOrCp'0"_>Kd^dTeheF9if-sZ3C?JYg6%'%@j2dLR##NY
%Lr)K?fS1:MqSS6/B4[g*OT%"F=k"^+0"SnLT$f'KN5cJW@O;YeH)Wnf1#165?F*D9`/1M(1Nc1F)-af\1#sN[;FVDgH)[$+<UpFW
%7BWD=f6`^0>R%7SHu&@fdjJ9U*oY4-bkZfY\ps6HD's3*Pa^bTq$:q8nf2*-<lpVp&CU@tObi]FU(N8dBW,/IdEG]X8$TZ`TmI*F
%B6qgCIE'_HQtVK;XJA7ZO=;AWXqdZYKUMqtYqLce"@M=0;p_DhL.&tI,Zud-N++YOlm-'H>u/"0pmF];,U/h=6$K*j"`bc)@gS:8
%&gbfpg9T-Qjqm6@F&2lX1V8B:-MT$P4Yh%)HIPHp&NjF0K4Mld,Z>8`0C/*R4St]#?c!gI;mV\_Jsj+o-"^kEj5Vpk=p)SpR#X`n
%KSWe\f>)HNgOqc",hX>(+S;R&;N=Ln9Jfq8e':*X3[Np`lcIJsQ-`_Z()P]LDU&-WKp0*snr*Y(;mI'WEAkk'-qo7f_+`q$,98+5
%qQqDI,LIqDddp3EH(Z\8QQ;e>M7mNa[:Y2[!L`u#?V>LkhS\#VA'u:AMfX8^,\J0)j)d\RM3,7AQjB7PKc#tJC:ZA@V%XXl@Oc]/
%j,(!S66?le*=:!a4IK_V!XI*cSW<5eY&k!g+?H:-M$QG'<'hRYM.FUKU1$``?B\X<RkrJ@F!T.L9^#RnX`Y4-SF74/Tbfg1c^L^T
%Cp8%JQ'B-R:nsEq3M6Y66i9+!OBFV3$,Vs-gX'*][DA*iKSWnFVh`I&,bcOoOM5PDI7`].+U5(mn4DOc*>$@(a;s`=c/m284kDEI
%EX3LT$E1;E2*/=JR3&FZU,TcWAR;OX.C&AogX^`jDn9KT#i4t\hD;R>4/HSu(XW<PZe!Ng3*/3tX^%:I9!42`7W2%i3+Z):@UZen
%"ViW`RFAk(5iS=m#U#ORL.X3[Jg0"E=uJ:-*!siVK*iLJGGMt**SV;*(>,)*"%tH(m\?7C=L,ASFiVdb11r-b5!X9kj$gOrMXcWY
%Q@VRuij=8m!Oe$_#I8+O/!BL!Lu)\0@Fg(J6\+,C3F-,`d^#]AbFDj1JUQ#t>3K3?*I:%?45?2B7\X6>Hse;MWX()#_E?@@$1NdB
%kNNgB-)$n[Nhn_;J_+tc*"=:AKpG^+`TDmn#['b93uf&JhTVcEjA(OG7a0Ct]I(uEUDp`QBJF*C4smZr7Sgo5(=)Ti(KrHr1fJA:
%ZCFia6sUj:[0n6X2HoO;TFD0e'E=.;KS=icQY3S+84k0PC.m5h@LZF3<&00t'@AOM8X6&LKuh\l)GM+EZrs2$dF4\&bt_k\0aRt9
%p-hHAh*4;d,*j?rhrS0c+*`O\dR]2P>YMk#RQN4=d/)@>M&/APj1,X\XTNGW#$itrfOiSm[cjhA72]/HHc=OHLLQluP023%IHjni
%7hQ`*bHUc1c$a%N9M?W8#kWh,r3r485hFD)bFP(b(OCsLg/:*lUa9'HH'-3B^n]>`p'WX'W>B!08Z"'C4#/NiQL>3"i^2_^(i/LH
%)B"Ko"!,q6`+4dWJj#Oss0MH2]oa=:RdUIY+8B<<HXKmp^9jIWg2;.#0<j4p)@QfO`F;tdF4%3oQIWa!mTI7;O^+o([8kLG3la#t
%qG_"\=API[4.I?9>L;uk6W?cOXU[aX:sIYP8!IEUKCjA#bPJk&*;";DEIsPpX&I"Dp.SR]2(;T8.8E$I>S97;;Ab"B*$$6AVC[ho
%Vr[Z]YoYl%SEV"/.E%qcCrdK!%^Kj\h<k?tVsl`>GTZsWd?@\#&O-E-A4.Fb[TI>K6#tpq"=gemmSA&L):`iH-UC/BXJ%3*KG:WL
%/'bh[V2=W'j?]Q9kRnd8+\a"+HE?4D:0D@p:dKB7ZCO(sR?H_^].eQhi!Z6:]LTb$H;W*u1n*;Mmp=gp[Pf4,PKG<EZ:(4=;G>&e
%;Un5"608(o-t74oHGaL'B30mH6N@U"0jW+/DJ8B``EC[Qld6Q(.$19GUeI!!<3D4`:Iro!3@.]_NhIFUC?\)'qcZVn*G:Q[>,t.(
%b)W(JQArmnjQ(4O+Obk%St*]cSg"fQ&CcW-A4<omAIls!MhS"+ngrl<7"B)>M(I,EX@SEd6>kW?8e]JeW^T-PKWfXD?dk^J"Q[[c
%NS=uK`X?PBPEVBQ8i;j>&]_pH^FHZq@MjpSGp!Ug'<ag;R7-CDN5r6*dT&Kjl<2](V0s7N1&:FWctq2d5_3Rs7E)4#)*gc(m1BCZ
%[FYZ($\5>l,A8MG4k];*ID[I%YYDf)l?=rHFst[QNT(%O!ojjdLfhOdm$=O;6Zd9'&7#_R`,Y<h(5YQ.7jBi:EpTt@-S_i]mp(/S
%j!Gf?P)D4n`MF+#6+lLDb:\$%@9lN0c:]3S[;%"5)=?rf@s2.]4<[eYA(("7H_*rS1"!;O8.9Tna;DQ&6hVDRG3Do<br1;bFlGq>
%,o1k(TP>`$:a*bVVcc7rM[KD*;cWu=1!P66A4+*CK:dBc<'_pINgMF;oL74eP%abeoQ>WG;B'Osa.$*R/=M0j,>X@=FN>(L"M>p>
%Ps4Ot6P543AtcCnQ')N\@^L<%<]R:>pS`<A`?M:4GZpUISXb^CWj!DJptKa-+p:fPk=aP@+M*U-PG+(5T"0ZT'=/H=hQdoE(!RT;
%GVMMq`[JBp]JapF`)+k!d-IZd%SQKEK3EOYHe9<i0T]-aLTO%B7TJmsRGk;7?OdA6X4WEn;L)"P?aCXsb,_>l)0l(`?03oBL5RcL
%7hrfc,=`Z1l&.CXW*I!(Qs6AbPhE\iaH]&;`3?AT7M0I&:!R'VQ,ZHE*njk1<E171fjb<]:dHA()5]=CT<kG`c74kdZgn::$,uIV
%6BF%j.?"ao.F4hX-B9Q5B^usq?98EI9nG,*cO$/:Y*@SC$DsCT@hhG:gVt5-g+/t(TaXTOa@mVAjh&?&lOe*=<[8DN4*2NV="fJZ
%Y,dp?%N+-K7$+W!2nO[*XV.>;2Cp(C5r"L]GiS0lblFT#Fk`rl69tS#QBpQ0A&rCab+]+(BbJ`>,^VOpcm#c[ar2lL<E\n^;%_e8
%(-SX)8b)]Sr0>t*5s8U'nmiW=;ERN4f$ZX-fnTGtOQj]U<?Z$KgtP<AphH1\#kHIjc4K+(0UspedjjVdjdDq0E?&F-*Om"eS!!P_
%o!+X"*@a/jY!^0"irp\j;l=ilX$)6.A.B!k[FTI4<5'LAX?8Ks3'p7rR;Snog@Q!&:j\!U-()H[+5XjYh[k+'\XP6Y2^q_^c3t@K
%=o;64TEL[SD1>6-j34Eu6fSDSM:H$JC@1"9`>ob&'#hs\ODn=8aF_X5Z+d;G4NY32-D2\'UY8q:9jE=RVXghAGGh*TOR*0]U5YrL
%7<akGD>'G^T3)pFq1"m$&0rADC+QX:*Lu@%<tGm(q0(\S&'YtH,`UM3X=p8OA;cJ&M*3t[5C#'iPFe>#M^`K[N![V30tU/*'`)6!
%"3_//M6+TS%.NA0d6-jMr:7PZ2+^ZN&7nQI=t?`fYG#skhMG*c>pe>k&J6B([A_>K4kiIWfjkZ6_hETCNNS;G1:"cM0"NFXj_ckO
%r!9ZmEho,%*h>'*;4dV."<uHT"c?+nT-b0Y?/l9"-$KbaQdtWIY:5[ZCWYc#"@#Z_nI`JOCYTImiF<)^_#WWR5u^F?5hp.M$\'OM
%[U"\14up(6.T%5#RRVU4q_!gO!SUA+U4s=9Pl0l34ukNb0aYiT7/\K^H`rEH>pX'DKYHRH"'6ZYMd4A;^4,IcL(3</ULo+FW\Cp&
%@i-"68.(.WaZ,YtLb>k[d8a?o`%YP?bLu`n6kL:#63S#)+89Se67%G/<A13kN^Kh1Rj>Wo%6/d5X,*!>itZ%Or[B-n4fj]j5++;q
%%K(T59oN]RdAVhd?SEdP9OCWmKW)'&/6Eh!&I_>dNTIi$ReVt&H[RT$&JN+Yjr,Sdol@6%7m)COQ0<W!;->F^F-qb2ejC!,d);o*
%qTKb8%299[dE")\)\KZ*TK<TCUt6u.@$g*"7#Rca-3c-C:pS$Z-s7obJ>=&Nc?FYN"L_3-'X'OYFk$/nO`d@T`0UdMKKW@UJe<"M
%.$_lnUD*:XjD@>qTb948N:+DBI?0+:[ZN^%CB%\lp)8#i7,nPU,hXW9g9%'WjJ-EcYNZ3TYoZTV<iZFS/p4Z2oW`[(L3\"S3k+'6
%\Y=^`0p86-(Q"e/U5&!=ae\P`4H>_i`[!%->?E#tnFQ8TF_ds3"H51-f6(s&%!tY\#6PW>a,?Bfa[B\I6mTgo$$o,P/p1a,dK;TR
%ffSiq?jnF3[2@;eR1)T//Y%MgoBNt98@@ac=[Ya/eK\).+DfYR7$HUsQOs]u>?\FijEr%4+NUa:)bL*I<IQ0]3-Ppd^9EpP-_[hd
%^62N?dNLST#rPk)7&t@g/G:7Q+a8Se8HF)SQWWZ(Ar0H9AIJ\Y,V@%m>-rC'nAhO0[*`@/ZbU9g%k:AP;)hGh\3$69$YQb0,p3aU
%\dEW&J<mKK!MP%n\0d$>%`,3A=CVd+!L7S'%A>'7^eL;T+Pn:gpJ-_FM1:nHZsmP27>Y0B>`2-#63B]-N5?f?^:=h_c@t46^si"K
%XFc7`k-2,bo&ZP(itQE-.V7-ii8m'8&3$f.HokMPlEFcS%D*WThObcWm6./DZUOD<FnJr@&Z7<e49l%BQ>+t^^:5=paD8RD3]s<6
%mna]7;7I)B@i,LTeqJrJJV;;CER44qT#>$/\k!1[K?j1/d-V!3.O0%Kj?ege0laP_+kAKL@NshU?gmZ\_[6u4hMmAZ-J.<XU*NbN
%dB!o7(7(;tm3']fD@Bs4:=#-FW2(kL%sj-FF@D+#@]p;1i[Ore6;5[X/s6SlH;Hg[(\,<(oZTKRL<[\):s^JDDU?AjjlWd=`7$h/
%o1-Qf)I%l*&DQ%$!=XBHdhn69QPI0`7.1m3-<<,NL27ng@N#2s`h>l3jb1<`[.V6rGjr*q&?>VjeX4V-g\!Ap;kpCm.Ob.s&BT5G
%7.E>m;9_"-']"27B9W>EfZ=/UVCkdWG;#uUFH5R+<7&0feI\7joM_md'_UR,(=^KdMnD1Z5"Y0B+)rm*o&L??cI9d5)TShpiYg9'
%6mrWc%^2s)M(Rp/@9`7^HLLq^7?%aqF3JMqKD=pklS_3g=ugT,>_C=1TL(:UHZVnK+emaf-;%6L-A;j\)Q%<6K1#.4oH4C]Bk\>+
%H4OJ<[IB$XfnNGAClR"b+u3GVhLm;&g-Uoq)^:1)b=)#(H')K0n8elE:9;E&oWX9bDeG^VjEJW?<,Z9D.TRU4'e+J`8$3f."Ke<;
%gmak/2?rPoTlW2K/;99!^!u#P+JQ&%g=.Eb`d4TWqhg\SPh&)Y+ca[MJ(Bo+)fLHEQDO7I9uR<+;6P5CZK2To\pP7A$O+%,?ful$
%*k'XJ)>(u(XE9GFFRW6b0juEN@s1S;kAJT.l5Tu#'8.ro7\MPjr`-aJG]9$FVK;IhTT!sC]=Rs;`[VX%\B4qc079I7^cI8uV%?cM
%LL/t(0jk,$E"2="cDn05$5Z,-4t\K'I?4j`U?fr6d3BMt,Dh4Y77#!'QR:DM6C0h\\n4.#iemFPo'YNmN2R;IkI.?oL4ohC;j]7k
%MMTFV9hT3iNi4p5>F"!.@.AbbY*Yfq/O(4?eRI,-aS;`5L<C:rn7]b[E,)`7W\3fOf1,5=`>Tso7&4@8"N_8gk"_pAL@?Caa/%<U
%72@5MimpO)=(2ArK,@)h^?^c6O^hm/Yf])"`I-+-?;U<l4:ZZk)7)k=*4%9*1he)YqtV+'RU(h]#Z7]Uh+S3;L2\oM^@,H0C^m]#
%LF^@f8W8JN"m'."M-#68eVIM"Sq1VKX&Ne0H9u,o8t;4I@NqQ;[H*SFJ?U[S$r0$bL(B(2r5:/+"=qG]A9@80ShV839[T2ZM7nIB
%5;YJ[dY%P/gHjR2_@o3?<?fXu[ANklB$h^WI.Q^pD]S:Y77u?fKRsdW+DqMN\N"YI28R,U2n^BZ%=4EUGXN_?Pp)lYND,[\+JR'6
%je^8(Xqnc967e9o7)[s1kTMn+-W=OMgmhE6f5@iL[83"0Oq14H=tg)tgc4lkg(5OCLPRBQ4`o(qLbZ;rOM70r#39?IOuRo/S0-g=
%WZ*8#0m%,uMVf1io0hr%[Y;GuhGGnfl]>P3?mK@s4._WJ8*l/I*>Z0G:%rYc!^p#c!I>g2)Feg+iq'M(i)llqN#q7NQpSfF3D5\d
%%.VnT_?GUk)EiLgUbLA-A"M]\O[7,Y,HDkV6sNWD?uKbDIe5lDX2>YX#sI@FG:4U!%?Y3sShU%eWK=T+[)mpa#j3U;lN%<Y_-=8O
%=07FK>5&QFf"EIn6KG_/dW2iP6F(\##Cr@5U30_,'FH%*9@7C[+;VZ+,KpC-j9o!;2C86td6h03h<QJ.h1Ik%e:[@9Uds:!(GEB3
%V^n\M$476[3Ff-2M]tOD\KBd[?2Pso;Dg(h_JHggaLU8HUH#W>TipGM(X&)6"p>;,.=*nsF/ZoVbcP@b8;@M/N$o?Tc#i&*7C"':
%,A=>H9o+A>%.[A>QK/c-5:*5WBLlhS2%#_2*!!1P]ooB?+p=H:aYo.8P/+c8GO>0&+KPap"piPr]3q(6DK0tVWFC4a,OIBmGbm>@
%?OX6H.LKjpOk/HRJPk"<3I8aadF;nBAtj<[0-3h6l4G34Yt\^n,.XIJ/=Aj<_+k336#JC`k$NFr"<KAd2mcSETV]XH]Aim=6H%jk
%DL<jXoYAQaEWqS`AT$gZEQU+ml"u@o=<F'2*?e"pC1Hih'2p:=_.$\9HO`K2SL_<VcT(?1*5KDuWP5PKO$c:]bMqZsg.9mU;#^)k
%op&%jTW\NAWC`S-:'c[l@[akW);Ke\l=rEe[<+j6:+cii4i#\*8"s0#gK5BZ-dsh='FrRC%[s5H3=1C"27eP.3A"UE&A,n43^K_Z
%,.r8?njsIB,[03`?AL>4MCTsdq1T*\+NY'.X$iT/'T0`$k]7A^6Vk2CjlmBtW^I4_N*K"]a/a2iBjhZi]^ch-S@m?)PEmi[lm489
%@;dAD0=g6R6q6#_+X3sue;Q@&Dih;XXs;#R\`1U#.R^fHS><?&#DD\+n([AOrlT>PUu8(aM7S.6]U7P-iDiuTW^nONThLUr3Hs-<
%27A'd=!Cs)L@u3g::Y;7%p%ZsR*u>;:@kWh23Whebp0d8UPGMIL*USpha;$';4:2hXF?EX\P'^Eh#[kOn8KB=cT&+BYh8!pO,sj?
%FjMu?cWU7gE<7&3>cB(I89S\\;%RZ98&g(:[_g9L"rrC9)i;cbI?Pr#Mhr"KgIT?F$j;Z5LM4dY<u\W(Lj<I6'Ce))%*%Lp';@32
%'FEtHC3e&]b9N^tcU$S*Q!B5R1q\d8$Ob->Y'(9tL3\X!Yoc>pE.SOm'<;B[,0Q?@6k(?Ke\GQH$b7H+,'AebcP?cd^8XL0hs0>O
%=1Xd2keJZ\TNO7PFpm.m+gT-U^'+KrVWWh%('rs26=Zc31pJ=\X\=//A?"`o["kH.J%Y,>K4^;C?RSQ9O@fdtWKoF_'+OV3gV"X@
%FX/h,$(Q/KYd5dbT@+`*9U4<5/;8+K,YG<Zl_bj7_5'Zie[2#IYTT>RS:(j/4E4VkPQB#EL).ju&M,]5Ogghq8OA6LY)7NQLs&rF
%KOg]5*<AP1cF8Zl?Nj57%%8)W]r,+?\d8Q[B8[=_ejKpGO+J0\ZFH/OW^Ks9d#QeLIL9,OWX;%!.0<2K">]7E"bEI/Y3PPc<5597
%L.E\77@o,!Xcr%cA0&6X81j72@W!&EX%[n).Mu0l*(bTZW^9>Y3f1V@)NL1R]X)P6@4bG)i/!8h4ZEPc=@a?D>I0l+_@SFQ]@?MQ
%al'*"i(da$1"hjd,,7sDdB&-f'QE?5Q@ehia5Z$4XJ\rZ%N:k3pP5RTlaa$tZ:SkcISN7NktEPJC!^#uq8.Ta3]U[XlDr5s+#kQH
%d_g>F12tjBL\MIga958$EMPGT5++O)<6>naf!+-pbH^D>%X$L8;p`R68Eg7#$7Gnt+VbZ1%Um!`F(_;7Y!!-sX[l8#(L1I!3Bg=^
%8-V5a6jsj?oVfQa`[d1&YSO7TYp`(7OcZuuJNS:<Kr?@QR_sSboqVl0RDB6Nc%s_'n@@!k*@AHkQj+9t8+9JXA:YnFEX0n8Pa9XZ
%3G/=T&m[<ZGhkgZ9b(n!oJIB;9Z!V61QqMW=mn(Qbn5dAYp^<U,8#,f<9JGIQ4GNgn!qD@7PuP_Y!T1)VC=6^mLTAnG^m)Q,./_m
%@njas`L(#;=;C+$D=7hDC-BLe#fmXOhRO460CN]#MFYK+7reC9k+?^V^Mp?C+Z]-4H41oqKA.C!R+,53LBEm*oiVOH+'din+gNh_
%TB2eD%iY%H`&;\\RPMYsZR*6'499<)[DmLmGSESjM/=d^8jcKuX(,3(Ws5nF:6-7n&9XU&]RW]dMHj*l7mY)S!G<6=dRfAtED)`@
%@Kr"`FlQm6UQ9QQ'Sqo,R^?$FL0H^["ek`gA"pI%pEH@,Z!sfue/4[tCdOS(_Z=\PC0U@G&5eE#?BA0O22i,lW?EfS%%*;&8E&!!
%(ag2X1@Uof+$]33c`>O8OaaI"ZmlL@1o)V3me6P-nTt`n3<_Pb1aS%^%rGUj>Db4%(GR$%+;40GMWLa0.?XhZj,nr0G"Ek:/^4Aa
%LE9P#5Pd4Q5@j*`1pLF8*[7Vk72Al<_)AIXdPs1U&-b*YekBOH/1Cc?&VEipGN5)WJ_Pj\R#;B3JITUgP+gKMNU!#:*O6#RK_Lg<
%J;Ag#T&E%IHa=hrnT=Y=Z0$72B5BTV:e\l_(qJXJ)Q,FH,c(_;Z:4C>nk)OCYaQfe-jreLp##ngAs(2(\`1UH$kXce-ijE+f)bB(
%ZU@0J;kbRK.WJHd+[T5PQ?F*sM5$du2G7NPE;IqK-%^l-#\M8t@-e"UL]_1/*P_>;]8Zp\iSiCZBQ)qdW?98*N]?OA^^l,1q'9s\
%a6^&<$-,&p4.,8YS;?!*j.o$-=BftI@FM9N(:1'g@1"^cPNTYh/%NO6EI%\CdG4Xk6'>puL9j`U%5s!Hd>@/cmm1VdTd,I4V)1p(
%Tj&2%"_^$\4N9QC;Bm$cCAj7=KZF`Pac^#4"2sBBOf2RPSP"&fkYjOq,Qa-'!=u?-kLni[Ll<QE[fQsc-*pU6QWSfWWMJnaVlDc<
%.2pPT<N";Y4GoSQ/:!98pKEA-p*omfMiWaG):dqGd3)%ZZuR4Q3#ei&*'*5Kd(lW+D$h6`Cic!8#AM,Zl/3sDH8O5urMX90oisS3
%WHPe(2:X<O%%SDH2,,f2>9a&q!-1o&&uJ7T(<Y^!XpL5k_ljPA08XmP/uD\kRj@NIG`rlK1(?(WVTJ3SRJE5sI8tqp%o?oCPmLjd
%if:0f:6FL/5%;>ji:H.[G;N.9'/,NQ-A@SW[K--mG'fC>Y`+W'h4#[?e>Js,P9[eSHQ7VT%FT4]Od0'Z2W6nE`bQa9*&)%W]kL<h
%1qG@m:`VMcO?F5u"P,qn"dZDT)g\HOE=c:*f!,EI2tK$MY=cZWA4AXci;2_.R%U=!TLe9_n66@lYfjeYZ'\6n7<h$I)J_5nI1>UE
%B-siWO;PkX8/$ZC2naT(nkr*i*nn-Ze2SW_'B-1&6)lG(5sE\q-)>P+c>GR2M@UOZ]Ykl,rdl472q(RE.KV/k'd_Gk-0rSG_VeYS
%/36(+6L8<g&R=P9,%:+Y"UfCE-Vlk\8b&qMDF2n1W!L3^eT-,P<_a5r-e<dqU\,ON6X<7l=>YfXWCZJJnpNsn@g\ruk[UW5;0[U6
%^f?S]B\22"$?I.;7"OpVeZ]V([&Cacdg!Y%6UVCF&*@H#*.,qe%&-0p&*ae)V,X"FZLh/r#SM_;6m0Rk3=)7`pMp+oAoAq_4)tC@
%6frhuQ5jL\"a>JQU&b)?3:s.&#iA&oN_-IJWBFA!/$6?++&NP,1BeZfai$5'H.kP>22]VT"h;;3R-.PmOF^hC$aV^Pkf*nd`1YFV
%oO)*-a\8/go!U9O`rf,Q.HjLcr$_ZU7Wb@*!^u\;E"p767'%Zq4F1m9eck+)$/j[Y;H;I!U'Y8OnGLo^kFm".k-:4K-o2f'PU9.Q
%1)@;9$Ng6m`W@iNKP(RV"Zg-2>0t?ekOW%2lijKmH9pRs88qX;+3i>^R-!Z;5:4bY%$uoojoGmWJEOjON&Vi`#DgZ[g^KZl*7ld0
%]3l79Jjf"J7X-_PHS9-;^0!a+o$d!oOto9j%C'>G@9/i:mGQC!Nn'If:VC4WqcB_tO9DRiLfjdE(s$u-/2-+[UiF5kE^H'Oi3e@h
%^p$56cul3Q.&hf3+geBoP<rPAm!(4^YQI+/,gq2QG<j'DiRVh`N,`k9"m_kL<Q_O.*9&n(1BJ:pNNdfMTcm;keHTLs5A-;,<ZJ25
%(3$c?mLC.nL<[4\!ks&"Gk%_;ZSMDo/jHB(m@gi$J*WP[aE]7(_=XGbAWpN#hW`*;%3e\k-#SZC0nm+[]VUf%VSUfnObAdX<Igpn
%Ai+M^:a',\?:ekNs)]Jn,als(Fdg,XI7(3!'????0$X_J5Z\\lB9Lc$<_&<WSl415G=;Q+dj]APFYohK7tmjXH<2N<-NW<0H3p_?
%:c:/_9$4p7)L)p%W"+%Y8r!E.-7-[_J/kLf:\9=fjXciPV1DUn@;JOAP+^OC):8*DG8^_1&2SY*e'WRDi*^XD\)P1fEYF1^Xh''V
%,)ag^Lq=NV&r_0.]'lLq3kLb/?.sF2<hX?UI"pKP?Q@9\n\9mW[PYS9C?$OSY!pu:pG`GJS?c%6JuiBg2]\8Y,ZRf&d;cCu>049p
%B[2ohBaY4EBL:k00^&YIpjsO=/jV'?UZ$O9Jf(;Z,8#iu@:8(/*'o?A69(+]Q-pMDVrgrW6VUBOEV=!C"mAo[':'2-WZYT@S\kt(
%25-<0L&%"0Eo.$='do7PYsE/^.L&bR&cg&uXdQMOhXP,A'6XRVb<+EDACm]KP<>4I&eM$Qb+9/AK?4MfpG$0U5jo#rK=$DAM,c#8
%l4/.qC<?a[^Pjk/*41>J0s?gF#sBKV\`LMoGE.b2@i3=X^&uY/M6i"oK@=pnX=#4%AX]@(q29i$9j:[EG26#cS/HsB83P6)523u%
%kM10#Ma6eDEElC92Beg7E3eVD(4.D`qEgd>7ud*P@ed6hYf"Bsa,]A\\c[YHYc'&"5S_SlGtVsF./G3"W**!SZ;^+&W5`lNr!e-H
%dXnlI+?G1N;&PDXMRF#:)SIJg71R;J'eIZ9M]:`=bAl.4#s(ioWfpIP=D/&Q8^,$DBa?HI@q`d)'qE^c#D>Y3-t=#%qpn3'CZnio
%))*=5#5;qFlCe#fn6?&//5EXmJ6a8r"hi_aFPm7bqpiYWp@Ad050_qu4*SVX&lb[kV2rXA'(X,=8b'nh$D%A);L4*O"3>A=9bcN*
%:eMK7iFua@o!(+8<IuF2D6o+9`eUXJ,&)SY!Hu_L`Fuib_Sr^O7""8Tgp^5X!uL=c2II+\cP#20j&3kk0k/Wl@20Cs=`s-&^ojto
%^\AZ)c0.NM!u/9:`dTc^7ZW$t5?KB^DS)U9PQ=sli=ufFLE0"%%1F=*S%Pm`H;)N_4gEgfKQCeM'VR,_..bZ.e_"^n$-]mkWl+6N
%lX0iaYUr;3In(oDjZRhKBu)H<4Tp1c$7b<l0*gV+_TsY(*[7m].;aC$R9lts,-+^"3+Xt-&oH.IA>hTgIC1h&=n5e(EJ"RGH1uq(
%@2)<"M7!ACVn@k=SOXEiBn%F:?J("f6QBm7od"3]*[VJ%FHdPOVl.oS.Zgdjd'C^K,;]M"=Q^)k0M<nfGXV^d2On4kFfRJWfE71#
%c,%Rc;OhnO8p_1`pF-kFiNa=]_2]5Hi.+hDCB;)U$(-V8HY9Fak0Z-B\-0lQ?=3ARcC+`O7Rb(\(W-WNTXRH,@2W$rM0NQ$n-I7V
%V8ZeTlOb8&`:Km^`5"dn>[o-RT3RWl%,__Xre+Z&,%`WR)`5G'd"7.^Y(!3WL6F0g;FRWN8[[4j3oKte%e9bna03&aNV59J^e/#m
%VeE7f-'0s:\3((BcEhC9PtSo]%9.sr&uI#GgJSGu,6O)90e"fRC!sY-<obf7e:aJG_T2j:Nh3/n#KBcQ!Z`_-n;RSK$0m)WR`ASk
%De?"3a=,rHe^i*Ui-&(2<.nfb^[&*6o&q:A3K,`$\X3[h]M)#o((-?64_GVNZq:k41#DaJ%9P@f;erBig,"n[l1`"ggOXU7,UINn
%'RQ#f!*r#`hLY>nQ!7tk3a`o-k\kJu_-+=/X=W$K)9`[^[N3PUiKk4]&J6nlRC+;+lfnf7\obH7>n6Fs_%>It2i%[EGQWY\P/m_F
%<256\doGF.Fn$/fJ7q?10daOC33].pd>ess"0TA#lTfR:<FVS(0R/\>bH*>3#p+b"NbIbEm-.C%`$son985fm46*GDF[AD5&%EHS
%:$Q_W%ECVkVnaO&JF5Z2kb7i.]A.8VS/!DNgEiUj3-4@ob!E8PA]h^[Z#r5tUV_'m/<&13=O_<V>SsE(4^+dj*0]<doh*_ef*V$N
%Ah!&>79c@<%[P(d%9i+K$u'0K]EM[sd#&me"LKVLDZi;qL1$+NVRAf$g+#"-UQQ.<]a5/&Wu``Skdjs>.c]c)Z0MU=4h<0S0jfeP
%c7<D!B,idhHaIbA/8Cpi=AMjbqAuh,"gW$QLh]4-JNhK^bX>j-E4H&ZY^=0C4IChp6O.'X@E=r'OJX!2^0Am--!hg719?1rn?m4k
%9c=o7FGCWO8fMi/[.V'o9a.V$:S9MYAJ/4C&@FP?9sCFmB/fN`E$i\u(nkDS@<$#uJI]oUc$g`rI.<^5+[gtfVXaRF1YJQQ5+P4+
%^e`V2AUFUT<,,5D3Xp!5%P:IuT+NM[-st81qLjrDC)JcpJOq8)IH#et(of`_FGDlV8("_ZGU!e1"%I@7QqK1&1=6[?@6k/eS,t7O
%;TR=+d+Yqf!-CY-Bb\2hP;EEq<ipgtk/7SnTV?BqZEd:pN!YM>XcUMo1@Dut,cq;iTg@u-Unr)1+oGk,^^GT7N.K@d2%*k)G7&uh
%Qq4Cuqp_nd%#g=_XG:D^L^>kZ[,KsG;@4!f%W2t^iZ86Y-tS#bIsMN6)b)7PnC&%6-8a;OQ;0sO1jDos'd>Ph"1"o^3cG<"M1)S+
%^qtDa&N,h8jVduQkT.ssB_]TL)@h1I4+iT.7YmZ;!Z0d]0b\EQ+nLeW>#e6VZ>DQh92#(-L,-5/c/G[+DPPbT/Rmd%20p?.ZW[PR
%4M`4*M$VCo;&t^RiUKN\-jB=(1iquk)fK;IE."g"W(9pLE#/&+S2KiOo,&YsDq;/ZMhjbl-8.8<qY!YeO'/,Z5b@Y<`o8aU2L-*7
%6-$m/`"d"Y7B"]rl3#k*=D=#;M`B1A=_EQT(,!lQ#cIs.-\`pPc&AYVM6t4@pEr,$OhgA/jQb./0Q(VZ-4pilU;7`$6_+'Cl6Rcd
%11->.\D@Hk=GL0Z,uF^o[+p?ZY'jU\$ZosA!1UrXqc0jO*j@r@qc\sB'1*dMRs**33bUMb?:[/oW"_OE+0fIBi6W)DRE`s2L5C_/
%7CR*T**[KH*UZ1TG-f,2#S@q-=mV6U@,64tXB!n>,(6dgqW96j#[/c'M%7*?J$U6Ok`@p,<%F(?.0[0!XLccbki7,DJ6!JSCZOPA
%faPK"h@)Lc[_]uJq(Q#f>:P:/k[Q$A[@?>H4]U(LLn&+m[$I6G5k*kP$Gg\!c7Bp0I)FAFiRV^`E&(]X.\S7A&Yk*]/h&""d)0TS
%)6[cg7u#A*J@S]B.bR4[=tJE',<`Gi@^:.Y9`7:Vr%NStj>Emh1>VM;3N\fh#uuF2?A8UcUC'tc@h<%t(q6r6)W_m]U-5:hE"U&a
%<KlO^k/j5\oHE->.?`q1SFF$G)L!&X:^9pfU)&TW)siAsIMCeW"F"V/Xd#jdfddTl]*8q_1N44ISF.@<Y0*3>Nb^+85]dYm[!i>G
%Eo'>22V7^2nKZMRY9P6/kUVqrXS2TL[_ZuJf`!\8U#GdaJ4JER0Wq:^LR(#*Nf/aC9SNRPK;rW-;_oR`T$=8m>NP^E@siB[?nGHI
%0qLbNE_UlgnY(u<*%`MLLfG?d!5Tp/DD]V<JshY?7VZ0a;H9-_T7S+93KXc[KAM^s`U;GYS;!c!eS7!p)?B8&_iDV-.b,mMD]-V9
%e2TDnVSHfIhU6;*M1:e265*mk@>^ud_iXM".h?$T&SUNGh^>/=)aC@#oAMY(;aWg[EJ4bE[@+G!18,nJ]f(IS181R^nAK."ED<BP
%.oP"Z1YXs0\mgt%.D8d<M[l?C9pML[>rO?eU47/8\?P2^Ja#k?jqE(1p>U3n40'%T<LSgT&5e&`.aUJ;QoZ;K)1IZEJX"GpU_PFI
%l;]^oqs)Bq<rcgi8scB?*\@)=kg9%#RT/)S`61E'n!"Aq!HBGtQ>QrMWlT1h!?)Dt[&7'NOetN",sUHE:9pJQPUPI$-1jN&WJ\ej
%Zu49_D0&YI(X6.4Rk6B"9_k"q`!7T^D"EF&2\6_7B@EFqhp\/44E!N61\<\cnsXB2Fg`*.J<N$8"XJdmi3i2R-/,S!&Nq9r9iK?9
%mT2!^lr1Lk:DEsFqUe^0faAB<Of3MWPnQekG@;a)&04'h7DL5AF&lNJP9:hL#^qoIPR=E)gj9OF3!Hu%eoE5g$;\V.O]@I383Kpk
%lk)Y.ePn*5)X8^_V:l7Ic\dlM?,N,RPT`G:"T[\o8Ct.B`B^Mj7kn_i:.BKnTH_ugJQ7Mn$SY-PH<fZQ!QT_q/[^@M[ZkP#4Kd@F
%?jP#PfYumN5!k#>^*:.HM$]g[<rRbLG5B^jo`."R9$FRUR=G:n$sf2(S,aB+`s'uqBg@8QcJQ"E&'?A)nHO\JA@cYaZ%2(.pB=Q9
%]HQ:=6.^E`bcrR`E'tRoi'Hq*V&BW4H:EHRVH^N7HUiko+B["pSu=k.VP^PC(LOLC%?bUmE!&7%d^#p08]SRG'n!aeeKXCli1;@E
%!NWl-ZqI6bW@\K@G'7@o!9,8:/ISgrTXVaB7^#&ZTXQT'MZMA&MgOg?h>r<`FL%4LcXHp0(A-bS;.kYMV<56.V)9]Vh/"OYosKC7
%Q\S.>AOScT.%O(#%6e0E*%P=4FC;DnC/!F#qaTdP)D+7<LfeO3ISFW_2&OilN9A?HLt2_?ijBAmRYM3p;NEKs!=f+1_dGd)=$R(8
%2ajHL<E[0M3MJif&_*hQQ`U7L;Y05MI52YZ8`ikD.RS]NklF7"BgU>YBWPYG,6_)2/V5,r%LnNV+q\f3ZdIh2(c$R7Gfs(%ToBnh
%'@@'WX45na'.%o!N%ZUn.i(:oTSb1Z3<Yh(_O=;2'I!4Gi!TiBhIFmeoTfsEH%rZVGH5/o1u4(*D`JB,;@E+2[FcQAi)8qhNV+S5
%V+"q-A2,U^"3tgMND.QWkcON7D(l-;?M1+q/k[.MqKh?bBaIH/<!S44_^/eh_"I/EH:5?c=,\P@`muo_AFaFh\iNdp<&5=]Ee]-<
%E?*"skUGM/O5N(%qc*V"Vm_l-a:(%=3Fg"a%LLa/YSAYmb1Y8j?!'/+6DT-%+=b6=(mqTk2oCm!!b^`66n=onW51;3hL^YK\`cY:
%VF/mA'\&WuKnOJh:6kbOe:U]/!7t7%GR:Xd;8VAdKJ\',%YGUbYsUr?X8CG/k<.:t.0M@O(p@pk>9kL!%+o<U&R#3!VF@CR(.91Q
%&Uc[i_WB`WMu`$">-X8X80LhX<\2dS5:0!1[_QAoXo`De5UOWU]b:8L+Q:=\T$gGVRdn6?Gb`DOoOUW6!d$q_@ZG%G,sIJgO-$oH
%&hBs^ArD#s*c-V5gn^A1EZ!VU/+B7U4*o8_!\*`bVA5Jhm,SUo0c#^XFscn3Mp[An9Sq?om,FBYd>.C#d?A&I$>DI371fd%13_*m
%Z,5ijnMM>UhR+bgP+r8qg^tAt49Wd`k'E)co[_)!%%?]s=`fb6\I<`,j67X#%biNH-rp?X=WlkF^61So6@9nu@%c[C\n0Ok#lh-p
%>8jBUAnHi-E8'D9j^TuM?^E<?e7K8lG.(USAMtI2q!a0l3)rbH=;%#'FZDt&5FRDZi3=uIWIlVfVA\!9F;un-YU3>Rjs/hnhFm^4
%k1R@5F9uP*F@R0Ic,l1rC\9p=G$IJc&L+iZ16)dq:GNJ2Q4H^Bnk-EsMP#1Hd#sXuc*)jb;r^\pT$ll6Xi_sC,%&HLSPZC./>?a$
%0,V[XF?gaJe+%1bY/S2^"M+q[d2hlNP7+o:/!u/m9VQY]DNM!u17@I9[U0fBMKVf.P!>S,;;*5sTpUY7U7+"nELRH*JfA`E(9sLn
%&KG$"RXe"82?nk_aHKW7+Wd1dX9)+5#SNfp)WD$hAgt6?MLZh/>K90JQl6f5P)N`eK1=is%n2^QW];7`W=.,?:=W\11hGM7kD]?p
%,<c9:PBamYc*=$-*-t?$EB<;mP+i1Y%;rNk'#^$p]ZApoXj5dhDML7SP*'!=&7.5dnsI*n)Y8[:Y3$8\.[_Xl%Vu_[K>^(7I8:+n
%UmA,YmR8g]A9NEFeET@k3lKJ^?]Sh)-]]goOk@%T=o`Fb&_2QTDrHMTLaH%j2,1PnePDp)g*Ek2%+q1[d@XBN\77b0#4"2U-KFrP
%'g8K%0In30c!^m6$=U^kCn#CpD9.oC_q%l[n7tH]Pjk5Q$EWgfj>\G!:P'R])KqnQ%\a]6.``!kM_q0=>FFOhQ7,Npf>rkdotEG.
%6f4ePiQ`,CU_5Al%no9'(!&CWT.N9j6X.>?Nq@fo=>S2FNL1J_+Q>g?mmU)9d>A2;Hl`kW/W7VP1EsQ_VZ9jgHD$W[0ikNc@=bD&
%?k^mJ3-J9FKm\:('G7?_<uNd8Oa2U*B7kHhqK*I]MIWHhLL;%CAdi%">qK8U]u3`7;UC"<*!,HnO\[;dG/JBo1$=Q6H;n^;VXsK_
%`&,n^.-gG0H72]d=Gp-:nE:i]S[q<k0Qdf"N1aetg?+'N*cX%o<jVqOODj]G"L!uC-9#("Qq3)8QJEaq/*tp$7jD3K!hCG]X3T5X
%HE<Qp`X*Ld@S)r5BZOk[]cbkR&i^ToM"Yp)gM9iQ,E4gVMWDXU=9LriW+e;C_]hPF(*<3@d5?0&!]WA*89ANf0KM#';\'?h'YZFs
%/CHS=d4>`kn58NjV)n8\ZiC.^=j)0^eLHq%oVso!r0I")Z*e7JW@/Nlbm#D>Ti4DjM3-j#rR<j@M3!OVg3*nZKI+GI+ur7-<E9(r
%.)kL4;L.W,j=QD';FG>0'L7\G0.\a75q3XB.!s)**HUhIS5DRq/Li@U!Vt))g^#*:adf#Z;41Dp%hY!@KF%j`:3j@42RQ\hL,*3)
%R6p-]B5ep0Y/$I)4_i1.#0;A%g)&.uj>eq)qClQLb;t10c6ul/DP`R,7P<L`:]mLF&[a=RX9pX%Q^1-ZWIu\KRRUKB-kr*ZeCFe-
%L,=Ni;DtXK1>/<'-ui;YgiGeE2eS8-$uS(g31$O;A\^&dr>`B5,DP?BgDM3"'\/@,`E"a&Le'2l3BB]/1nI/FFG`W_!c;aFa^gM^
%'q[8Z1ma=eQF"N=SJo1F_-08YU^VPSm0+o_/mF%=buH7oWhJ7P%2o29\""d2T84no2$]$H:'[j1WbaA$5VelD=k)MWeR_9P"N+@W
%)ELc*OVC>!ZPSETKtnBZbWR>9'X`^NkRlP.RRV/XJFck5.(9)Y34.*Qe9ZnnC#2WdZ5iocMH3HAFZ)_Xd!jFlK'rJ7gfq\rQl)LD
%:W'=2d#qLm-]AMZAK?gO6VVgS7()f08!Fc@N_aqBTZO%;lAA<LHjqs'dRZ=ZG\"Ht"5qIIKHfsV%@BPbYYqJGL1*0fCtQ`>nQM_a
%KbKNe>&J^M=p!9-?3fN]6["AY?=`C5X_j75GXX2#2$hurr'rYuU1Hr[9N-`Sh8G:9LgJDQP#QAciB`n.!@aa#jg$2'dc\V)8VS%W
%eWO%9(na]/6:XdHS\[opd)pAuBS&N:3`rcY"&]F<I[OrV&h7F+33D$[Uf<8q"qSs1ZaOBl]Chg&&rXu7KnP<8>g]"o:Ak+pGniCJ
%D6r%fHO+'r@(JY6c?;%/2"UgfO28?-LLSIqo66"h\P[W=A-tS$aHH.2*@N$hZS"CU6'MkIKM)=:6;dD4H)_]a8eM02lG1j##ne"0
%.?BlCe$;L#.O0F7V$GRB#<\'V)S6)0L,$+Z]D5hk/T_gVCFS;)<@FY$$?C5JkV^(2>91',f)]S(-NP0#r'!dYAjaiQ<['$<Q'@RU
%'RYRnZO(,T`]*7[``E_E-g\,6Kk6,('Qh<db\:$BNPGss9<4^Z4ooSc/JDW68?mk<S88H>Em.#l\53;HV`_?m[^1+\#eOC+)MZI(
%I<LN.^`F)C^uM14+1(/j+dLCj4WKN5qt>;uG)amde810$<ZQ_4<8C(QOE`9@_Y)j#!DTTRi\o-bZ`3E3`2n2,dB,Z:6,h>_[1XLs
%r/%@eTU1<YlSr%P3cDg"0#/&)BQ8WL'J+>Y?o@DB1)#OT#_j2[XiN,49")s4/pU)J!_(5*,$Xo<e$'J-7AKhb!>["];JHllDh^0e
%JKG5d>c)LP^`um<@amHi[QhP[kVHTc5tYp591M5\Y@Kum-_Rs];2T%,m<F7\@JoAc__<pbC9O%+6<.L$EkcGq1lVt+;("nqa47k_
%N`q.3C`[(Tj0[Ma5H8dPJthBf!C)_>Z_dW.dBhA^ef);>2R+18$>@;.2<l$E;3WgT,/+b);j&.Q^hZ.UJIKh<O_,4q*JuSZWjT-(
%*7`d*'UkgD+^@BK4K?%=IME''Ze@Qf;L:s-7>5Xa9\$>?%'nS[\-#%aV&>;>1F1!I+:\bK)&-6"W.YH_QA2DA>aQJYSWmC=Eenj)
%hCC!3M+Y.XW-CmLTS:4<VQeZhY$saP@O:;e;*)[UCrBp#=_P*HBcP<8o(7g3ij=d_;Gargk'.?+#A.djba29:.7-DN;p8#A-j.MY
%+eHhUb#0a(\71"7cH>VH68ZV8Pt>I'AN.$8\`c^H=FL7BD9P6T)_-G$b9CHi]=_(o6RMtCpBp@X\s%;U.X")i(:s!X#jdWK4l&*Y
%`*BDI3+n7WgLFr6h^"ql[7#j_)E'7Q\@[-&0^W&ZMR1.gMXF-UN0c:Yj2,rUY*I_dr:Bab`Ku?ZnQ%jk^Mt)4Xbs4NLMN)-&h;ZH
%E"?iYY\gUWOP$6Afc?/7p_d-Vab^gV4R]]b;)b(mW$ki@H@alA?u0?*%fD$]$[\Cdn&u3BG]^e/>"=isJ7Wn'h.(ULb2=2_Q:_bT
%(o#M,NAB:JR#&RQGfrkAF+bU-a$:SBa>b?;0K')+%fsg%ne8F]8n'V_]7=XG((JLqG,JL3dUX>dRb4N?l-eb<cd7j^@7_,+V)Qo<
%N@EfI$>(;d$scC[XD\R8;t!YhEb!UOJ7"skqNLfdET>_jYJ)V:T':$c(VBmT/6@#Z@@K)aLf/I1p1[:bMIt<2P)#ura(,Z5/WH_>
%nqs0EQElaQ&CEDt0kB?OiB\)co;E!;RX)sT&Cfks6#*J-U)erUg%k#3?ILYd84*NtFX%E`Jdj7f=MZ_*&g"<<n6S7ai/RK%*2YtX
%<ia4tkX`&G_D7CTm#6#;s0jgBcj%)N!c]ZWU3LE&En<m&l49Ktp2,,oT-E7FMEoVC<=`W-$>kjh:u'VGf=Y*kETiRn,>4baF"56L
%NMOaN_lJWG1kgnPdgT#kGbqN3=[(-J<.!a]o[C*VW2Hf!8(*kE`(N.Homlg2%KJWq'u#Y>^"PU,::LG:\df`9GrU\%jD-j<%<:oD
%\AOLZ3<5.SL2>*LH0t?l-H3051FN(0(n1*Gr$2B_$%cP1piO\BU!1d^O]W2=ae=dj.46!^k31EE)6Rp?DK86r0pg"!<BCNCRh"'_
%P#9d-mkt5r$/B@Pn;,(Q0GCBhKpeTPC#N=)3`lo[LpmXcD@@m^Qi]q1r>@LKCRWq$r5_X.,*Ri-`U2VD%CflYeJTZZ*[0(oEsjd$
%`bXh!'i7co_Ook(]&_MkfT8ieMQV:.X5fLJ#B-Gr\7`Fj=qldL(.)o<4Unnm=j]F8#L*Fk*h@m(XjAjZ?l!3,mUBn5@ia?(?\WpK
%;"fJ%8!-+JShYrBj!e&K15=LG7h<o(\8(t'?4$i_kQVSr#AQf%0J7VZR_RHWW#$Af+1bnEe@FH*":%J>HSWI&qPL5$J<$DbUf]5)
%d[$n6,8P]T3]V%LY@(jmS6cp'lf'GDS)S9eI\[^A&r(%N;d/O*/dElaq$9OI7O=mnORi/IJe1J58eb<?@][9J*I*ql'bT(M3cpIP
%J0(VUH.[H9g6qM$8&YlK$r3+IN<5(k9bs!$lp&[IV4Lt`@M,Q=+,$s)GdH&(KXrq8Y2NR+lJ%@@*/n)1AMC@N">i.EG`Wcaak4&\
%Rmdue]GrKg)$+Ya3!>'H"*uXHZXg]q%%s^7L_qIaP3=-b!O7"T94o4Fa97/Rf4!ZK3VN[BN`b8&kX"NP7&5K4$qNaO7iQ^aSRYC$
%0k.#j#C'4XbT#+FO]8([-o]`8rb)n/1c:3&7XR/&P\`2Il>3Qj#fRj-J!h>ioW2$u)%\JG!_BT1q/[<_1MX;\WIeI]&D9q9ktFM'
%'bS]u3i_d,]tiG[/BWRML<)o<A;b'*O"fWK(7E-@%#mmC%X)h$Aa0<#),gq^J'.ohXi;RFL.+)4e;hX=$h*Bc0NKc?C.=_Qe(:sk
%S%tgWKNJC/=j@2UKOin!\(0`qH\;pY5r%791+-ch]>`_TK:eg1FTf@:$8rQDg5'e-oi3=N"sbL)Ag8BFqIt!,6i96K0J.Udo+'H<
%5hR+Y46GW2(8gBUD'U=BJ'D`HW-na'^^4IKn<Y@!EN5,ZRqX<=qAQMkZ`FYO&Lc>W*9ebA`ur<XT]p#PE)dpODmQq)I:,gTjC(VC
%'F-QBO(<J:6naoLZP90%:A$D,REq`EedaYh9emtpD@$%Oll"6@F<\`4O\a&.9*j'>,OCVcB$;18Z]*Fgpe%bP)M?)]FgIl"]cph)
%?thY%OJiD<`#=b75pCKRK+sHb'9spWd?XggNmP6T"?(Gib+35].%4uEBP=JT?i7-O9EHr&D?F?bi1"l_EZ;#Z)!3Le>q+^[7!/m)
%ZnuCG4=B>NE,l2rCdhZ_6!RbR7Yub*=0&19&D\$)#CNKPe3l2T-6Q1INYL.qTt=l/[<*T@.ku[4`;$3cVa;B=6eO4TSAd,or)SBu
%RZk]06U%(d;3Q9SSMBhMn1[7PWg0L(X:hTk.Q>pokR=c/=XF.9R=[0&A,nE67\^ip_'EBNY\XSLc29qj/ni**g!RlaDAG,t/ViIX
%HP(6eQ@iq]RkR:")UAeqSZ5[Gn#/_$pi"D4O@M-K[AlX>e.'9U;e3-[@2qnh;D`6jN(<NA^p%[noItsf`l0'Z+"^`rkQe5s4OY%'
%6rOU#a4+S0'LL[dFOA"S$rrZK+Y2,W<5oQ^=g/K<aB;A"nS3%-,BLt(D=IqLEbJWQh[)"n_Gm0S(E&rj[%*4XSRm,W&Wlp!@[I0W
%s'%X&N'XLmZ[:%$&(HG`8W*[QEc:`EI7BO]Fm-/D?pL02-0hG$b)Nf@BG8W&@t8?o9Hg7XYj'lWfOdpNS@@Q52jD<sF'^'</\dkb
%:<GEO;/MI@.`<G\ONr$@EK?ZD,BJT'j;+8U.\OI)RjpaKRf^!"VGcK<L>$,_o`@.Z$$qs1i&_-)A$@9^euVan8r1:1CI?n$#11hP
%0:H4@fb(3O'sir.O)8;%PEH0+VB7fWN36J%Fj3aeMcFKV6PUP_Je^a?Ha%a#_M97b_Le]!K6D#S>dJ0gD'3UXdY#e?iZ,$l8b4%-
%5p'\APZ.-R[]#5o5(:rRq/iR-`*3\n%X_!M?'E]H@`DhPUU2>$T2n(53Ek>pq$mV(M29KKJi(u(iOnCEU,@^,7`Pf$F)s@#AToUl
%!A/dd!!.$m"]I?/"P..*b\;&_B;&3;S=oPdWJH?j\`RX*UOl5WkXT?GYZ;;3#.N*TnI7,]eM7H^3(/^6H&C/1X'$3Cq5"N$bR1"@
%@i*?"jNnaR!7$0%.6+eFbm+kGa5NJ?X$E0pLKi^<%mfNYUo=TJ`j>u.;"_&/<0,$JGtX?>L]nj!qj^>6H3fVuD:(ntKf@0b>-`SN
%id\0RZrS&m%\^;P&mINGNL8?A7'[1"W!=\1l9\IfFWQ*$/tpR9Fqa3DH'k.bEV_G*n-rfaLg$MqCOC\U&2]F&!@jUr&^F"nE+bn(
%gSa#Z&7*IUdcB\l;3\n0?s".Qbg=g$%XXdH,;p4,UBdUj%#6'-+@Go3@]cT[RFub=%tJ/l.iL3Z[%RrHOf#dq@p_V%][b2H)9#TI
%NH*FQ,;1@eV3Vh7-''OodfBpt@EM.k9(LS'3>J\sA<79ocSuPnMM@s$!oFZ32l76#bbYU+#2pK:gMj2J#i,6,U;9MQRA\+UCr+L=
%TKk2<2o"6T=35<(5V!231ln:cQ`X#NQf:tIj#=%E`&(9[3,T(N6@EA,/-a)8X)'pMLKlhJqu&*pUfs,p"/?a)6X`q3MYJC\Y]V4#
%4n8)[`fg*NeUlc0QYW@/e/Uu#4qeA=1a9<U(]8`p52:5oCUNA(576CD`+6s.d4X_EO\%CPs7l?IQ6h3DNdr;Pm5=)-4)6V`<b`2r
%Zns5,KSZUc,t$/HLn7:';/[%tI(0N';,c9lO23C=e;7m=,^A#+ZGgo?(c;L1<<hdSA0;GJ%cM@nYiGO>Q!5S#pdZDgSVEkHp&[C1
%#_J3>>Riu^&??[Tb.q=$Cl`(1:XB1Vnq*:S8i!:u).!AFap21pSdbgr,_:>;C_ThXR_$a/T`MZEU!2*tj,`mFJOi@Be"Tni:XDe?
%)Y(X)be',k7?4\qD7b>;'SW%/@KVuIMXA:E_^s$;`T]2Wg[8%I,QfOJi]E=E%><a,)ph]f6T'W5/\V<e>a)gO,JGC)\\_GLniFUH
%)f"Vi+dhoGG]jQVgCD&ZBiLs--t-OZXQK[A1hqk:_KW=A3C+P]UQ01E=tgfI+otBhTXtPW-k=W+<F(P$&\!q_%e.N<g.pZB6(Oob
%5TA\eisKs^r3dY9RDgPnFug'oM`[?#_m:H&:(]q@!IVtMi`68b>O7-'-nW_%[d9dBp:#7JZOiu8:DW^:XMS`ceahdhZ&Rd6l(oC1
%K@]DaFd$L%BU0m=^^q95=X5)51DZhf$&jpXZ\:,p(+QnS'_?j$:Fpa]51@jQ"BS^QJ)Y8?JPSC(J'cNS:.Z!C3F0h+YtdiS=+FmM
%Hg$4/&hGR*M14S#"QmOTkW2JcYj8W73FZ>V+M^%.`PHLa8@:fad"@A!ASY4M(:GZ!K3eXT/VE,QM68ZcU,.'D.$>CV^2-h2IA[$!
%h.6=D;..qXc)>@`(eEC/A-@m#)jQZodbY)(BYk@,\qaf-(KN!YM(ssS+fbU[_AF]_g?8ta'$;H,)8XTps1?,@BM*sR2NT3JJCR!"
%<bTl,3VYZW$t7EEOHdF53bMS,^n6,CLD[`EB*Rj9IM32A8oq5qH\P$JJ3)Cc&eV94=,c7Bq0f#M:cAMBAT:sG#k9N1_d_@pA#odP
%ZTHo4l8TYZKB8>2d&Ff>!18jF2247keX8.1+r1<E7bG0+@rJKr7=i_#T7_$LP-]^N"MRW=-T#<<%h-fBao&*iPGI2AV6L754NE+g
%Lc8>(-J-'kXDAFZ"(JAMImHfs&]tRsXV,-4.WuTk@90Itg,cLZE8=m?&L]!'KK5K[!$!7rJnrUK)FDlP>/=ZK+&?@imnr9"TEAml
%+M3^#6n4N!<.;HPL1Zf-X/Kmao%iHP/u#BQ1&:>*o^#m.g5U-\C7Me'7WKl_UuB3F-m[\EOF5j0l*S2ZBSZEK'52s;r`[jQ#)j':
%#$=]!(HQNGi=l,Q-gfJ[D`81:j\A;CrkST[VnHdc@K()3[(Tnt&MYXOr5]/21-?,kWSlEYMo2:/eO%AWFQ4AHc>G?2P0F$OL1!`%
%9PRB[6ZBCf<b<=maDFWr:Xa%5Z:\tR_J'j0bQ+V17=nO<N[P;.`#-M!-N&pZr2^LP<CO0U<O02."K%o)Pe.%uS3GcPl*N<OW0pC!
%g!+AL>QI/_Yr]Zk`D<6CEIe?<8("#+`_[:iUl`@m&5bDZEY,F1&/)mZ"uL3MB73r`dtDBSPbiXfLO&\:oPNol<>LGZN+r(hVuVJp
%0Y.[uiVf]M6]JF'!#a87m)iiMd8/'U%1H6m^@;hNH>OG%!f.C?#Hu'B9Qs2Q0Q:fb(e<UD1tJt_=cHic%UgnoT-hi%MD5gtHr];i
%7=/YqRB]^nTNm2p'pAkRM0$I,>Me`b%L+BEW:.h#n'En<"0$!"DCZqUq!j1'$o$Vc#;HQhJeX/A@B)'#m3O6"TedUed:3FA1k5oj
%+*4j6k)Z,3hT7U=n_eG_lOSDaH/:(*>@Ll=NAHNfeW5"t+8(Adi$Djq)Y=GJ[O.k47_[HkUEp&r<,,%E..YO[-OYdo7#`Sm:J[!Y
%%'CaRSn`6KQruDt;-h<_9kT<?3l2RYci4[Wf`"f9:QPY;oBReR6!/3WB/h38i[nXne\G$Yk?\^Hior8TO-=4qe$E(XYfS%V+<tEQ
%Lu-M+\q4Wqa`1&[mrDm<'7fK&9Xj!l<^9VuN5:a%RLKUfLZ#6,g^W-/M':Vi6In7tY)X:6Yl]X=i"!PD&Frd5*.SPfFK!PlXl^MM
%-(Qq_7#DO16fhD*N+gUR`!sLp+-KVhNT.H4"Wb!$U,i+\'57#@;ML"+#aS_he3G6FZ9#Thb-;=O'Ac:nO?,`seD*nJ>*=".G!Ht%
%+X&#t/87YP&CiSYX'j%n6k9p\&5b:)LM-u<;8K&bVXCDt-_hd2Mj=;WTJrU=.[&=C-XnL^2Cpgk#O4E]akI^a<uA5#'.`JPgW?Q&
%D_eX;\!S"+BIr?^ok2-pk/V>H/7+]_n:^5GPH-8FlK\QTA>)T)1(1EuFgRl90[EmX8An-:*'PP!0b"1_BK[d9&3g4.B3jVM'ZC#)
%`5*p*i3j#D'$mirVfq#ukmG(WK3Z#@VP6jA;.[p)?ashi2aKW(@u%m7jO9j$9lH"XE"aso$8A(+70,*WIumQNVad#/JeScJ6c<jQ
%@b+e",&5)Y0\%05Timdp7Ee`*04ijd&2@RQ4cg^oZ9SRjAn5R0!^8"95kG^ehgf\U@[u)5[mE\T07!gI1+8IEQJC)&FgQkD<Q4#K
%%u@2%Xe&`P`1%Sk$S>nFW&IgY"f166]qLIY,!f'2!>]i135WUML&!ht/88dTb`J(T$;k*R1c'8'7rN-3A6Xt85WK+So4sKr&J_Dk
%%>9u0NM30t.u3?J`Cqbtl2I&aL,c$,o0uJ67@bVsJW=Ut3]Nfh7>Hh;/sdCFkh3Fu_U_#uA=r-j(BLTbU-c'2LsrjeSJ&G@7*dc%
%3e7l=$'8^6@Q(irf%d%L*ipumfr4\WN*W`8-q$iC7J;+.r_n@SaH[C'Xju[h0aBj=X_oW^,8>e5N,!r,YSso=Pn+O`5kSm?I'Oj,
%-WrHDg]:kk"bikT6H)*N(/3e9r)-i?+3"5[@?'0T!["b'j:SZN#o^=@dMQ::7)a,K@K+u%O7aH2&*33aJQ97003;#i:gZ<hI4$4V
%A2)Am`uU3UnKM0C'616`/[+4,OD4>32.d\@j^Y8>Yp`.:Qk738#Lm,k!ebT/F:$)\_&W:p+Ep!u)R9t`/:V:,HXb&`:(4r)VH5/<
%0c<OS_'8,1YVR.J=Q`g1i'!89aMej"i>-Gk9HAcjIQeh+aY[VkC?0K6a<qpKQPUU6c9q"r+?3s0VpFukMpDL/P`q&"IKkg'&AUCC
%0bNE?:h>4*R.9c%$-tjb$n.@U!c\6mJ<mKc@K?"bX9J^OCj(b34T]okeO2&e@/-!HIAS*UI]RQBPD4hMN"`ljQu3g;[h'AS5ZqBV
%Y\54=PQ"c(n&_eh-!1VegZkkqNZK7#@D;G1cVU'-<+:RoK:bV]0-`06k]Mo1E+.(One73V&/2)4ZnW%"^(kHBUE]+`$=bf:e]R7k
%NdDsB9dc[s%PFOQ)ll)E?Xl!ISUjcZ8"o*ZJo]'N3#$"I[Rdie$<bL9oaj9/Q.Pd`)`]jUf:'`R6jVOEPAQ\QTY/*m8+b)1YcaHu
%B9EH<-8>p",ZX2]Gb\F-Gu%[`$>t#[NMjcM'l`7=mXUgm*cV/e_AesJkpkc,G0n06T4XWH2KAZ9lRWW07%A%4[l,70$*9olQ>VJP
%+]Th&$J_Vg03AW9_+MB0K$Qh8Up-u+L^l&\@l:'=5.8&1L)uX*$SarQOlV\@4_s9i6q1IqA:^/r$lLKs7.6>K9ZQHB^SiB?:7Gi4
%V'NTT:L;%$qS\kt?E/!BU1[G9k;\5qUK<\LLI+<qJI1I64Mjl8oK5=+9m'!jIn?ZFj+%QrHDq@2'?8+8V]QhQ7LVt'LHc-`J\jcO
%7#QS=:r@g6q^2S]I$[94*&S4\\d#han0R1)6lSuf)RGlqR.c^IVV!i@`UIP`1U3)cM?P]2<6u=`4M1fkJ2E7a@C]UWVBSf&g96=@
%3+]MNX<1/Rbh>^1^K`J8>/(7^G,[=7@M#_VJo*K$Gs'd+r.tYafdC_j2`KUr,Ut)lF2tY<=%]M,&i#j!46]I8Na9[<@*M>Jlf]7/
%UmVY7)@92OL23gQZXlXnP/3tjT':Kk^LdPP12A'Id36Lk.p_jj:I9hW2R.-/2jR2QV#9`f:StBZ=E>ES<qS0KFbkk`8j="aqI7>5
%4m<[,c/u!"IKSC6aidSDkSh%,f?t"O6^"5hB?n;`^!WlHk,,+ulJq<QLV$Q=9X>9Hh%UNdV4\V<@1riLN,J4VYmoO]C/d_Jc6uJ+
%kZ1kP+VLCe_@Wqb/#9%:Q#NC3cr'V3^l:uolrn#+)!W;J*]/Vdbh-;kn(Q@e#.m1,g.S8d1F?d`PrrJR0jSe*$>KZ`&"8tme0@C=
%`Bc+74,d3icB=gH!1?m3RRR`,8foe&YmuL%':M&r28hYQb/=+b8@>feQn^0d!n!V+).;,.OQRGh3]pWd"'LOkd`j4M\7:uGk>pQQ
%ec@*p-"f_l?&q`<h#,2_@@RsCT$A[OO.G5ROhT)un]9L\-$p=9#clO@aDS,b`-PZ=?Q1Pb,R1`_muKpl#+)G&b5P2#NhD]eOcN;V
%L(^gH>f82UOX,2cIM0p`'f]tlg/H?C8oeZ[0+UCs,UouL(iaV-e"CglK\CbNU!KEYJk,Mj'/e$CA//s']!4F>0YS#?QV;8sCB@Bn
%'9V\c,hOKBU%ErjW5.(r@`IU'NHd:5nO,e:n3hgH]/I6'FYt(qT-?.S8$c=H$P;6'iUYJGkb/7&iV(@2aX[RDlgYg.GSC7`i`Hh9
%<^CT_,c^!8dq!M&nf]ibPALg'CeCMd'-3-P(?A1f[/Zk-igU-=l6YC^BbHr\Oepli'ra2qmRN6OKMrGeQ);!ka!3R9#IiBq%5)Fp
%5laq!594p9T\\Z9V\:2eFEEejcjH1.?V`rl^C?@_BlpT7+B:sT'H5XZP*2DANb(-KrmRM3UOW.'UTa[KHiO)ukJ(nAs75=F(O,?)
%If'#Kqtl;`)l8"'nYaR":&7*Qqp!C=ou65A-6Fi3UZDFMVs`bi8r3<.qk*lK*W=rX*<53&+6\4QcfMA1>\N":V5)ofrLa,UNa=BR
%q!&C57%4QYX;]8h)F-\@^*l<:C6K)&kluRp]N+NQ:H/,qNkWoe%1VWIbC;+I=_Q!BlF<GTnp4,1;^!tbo"0[2%oZ#!,mZYc;tZBP
%6[t2ckc6L3@H)"N6K0f?)(h@Zb@A^P;O!-D5sMN[:__/p%i6GSS-7QP#Zm6:jCr(')%ms[g+oMiQ7._Ql"hM$-Ur]A(a[#h\9:AV
%@]@MZfYPlsF=Ib6K@8Fm8]9`C&:W1sUoBOOAKbgI6RHhKo==KYq+"cUW=cI>Lg<^@9T8%^>DNu[5[krO/XM;5F$^Jf\PBI(5b44D
%0HX!Z<5XQLMra0nH[73!\r7nhUk@bCd=k*keat3YZs)R>.-a4Mh_0(t"IUmML7$&])@J9Xi2":2U(B-V:X7A*_QdgdPZU=_Si2U'
%M,GLcM$qMeDahco[N7DoKB_r_19m%K`'c6E,?%hGjHc7\Uch\>M4%[ZC?U4h8=@Pq(UU#+%t#.o#PRL<h5?"REGWrf"998P(N$>`
%0i/d^os!ejdbX[<>N$?lPM?r,1[ej-<%bT-jLpMg#u8/FQNt&O1$UeVWJ3#nZM.Q6-1NuI:>^%T3f1_I.=.F>pT3OT%nZ1E>3UV.
%G#M7aFq'59n<>^T`?k/\8=jLORat0qBUFKKH#Zu5n7%tsU/VV6@\bK09WTMHlPRKZ-B$:gm?cFU9.L_KOK.(cEn0ajcAKoC4VYFd
%]FHZ/q&G#;2VB5#[$d9s,ta497?;-n<RSeG!4,#2h3Pn[.p8V*A.6Y=1q>o4plP3B?8&/`jrV?SM.`!.]g_0D8GET,3GS0hlVJu>
%Rcsi9"G([Ccc(EP8LNJJZ/VE>ODS*sXSL$^(;8Pj:s_)'_DIFk.,,TKPRGsViM_9#&)'"=L>DJF.Zo-tG,!Wb^n6fi5:3[q9bsXL
%Y<=knDjYOL<hj0o;A"cMP'b$`S3^u!FUUq]+"sdf-^NJR9*U,M@3U0X#+*oVHR<?^5d7qDoEm@%3GTKOhh2S`ag%$SU2NZOB6B_8
%Gpu@PMTeD']kJ!$2Y+t2bnVO*79cETQXWCQd%a>q7!gAm6m@bNUh%Uf6GZD0h.$p&,>[#1#D@oTLpf..*da;BTNqJrN@!(o+VMF1
%b8j"\oJ3N9ppue=`H/GB.(<0!LuW3;aH/;He-CZ>N\00$,=e0q5jjQ-o\0)jB4@#Y<7/@4f]42'U5s<dOCRrM:W7Hn*.6*S3(&jb
%T%lP&j>)PSfEjI:o&7jcRaAJTG#h=`bbGd';o%1BI=QX<],8lh[?8<Kgb"/6$@g4h$jJ?s,5S;rE-h(:#O0nJCnqQ.`\,3(Uh'mX
%CKdsk6njX-h\rXF,ob&\?2g5d0UTq8U#^?>Ft5SC$*SWU)3)J<a9s?c(PhU-mD8'aaa)<YT0d+B&b48*.V8sbU`9t=ro066X(o!!
%SRoAHl==?u>o?L?aap3!A0!+j7C/23:AJoSPE9O]LTa[JKp9R8,I5BTg&hHBSDZN7oh"p"]6p#K9.bB;lrIN.=e^H5<b+Pj<0gj0
%!>a@tD@0ti.?W^r7:I:FLEuVVl%QF\KSXb!ZLtNcN+(%FR\(,&gMG?cZefNaaUcG15k)VJ0*t$Uh<3]LN.3!Pa:"Uf@P;o.4?1j]
%%OgXT:1cYn<%'[=b?cHG?609qh,oKO,:eSL=:^iP-$5[\R:rA'(%+k@h@)kA%JBHi0P#iL3'G_)6I^.p;Yc!&F]eq16]6IKH>#mQ
%:fY.7-MBm%H9gfuC8/Xs',eJ#,A#V]//e?CZG,!\`K?NVcW7U0jN'm\#"p!_=/cg#4XAn-4K_:%DX)h\[,PBH\<ir\'2E)6)D>)T
%a$C>+FOU0p5\QA9ioH>KM&qa-Qd/c#.cN#-d:V5k8tR=UQ-=i1Zol+B3D*j.,#Shh0L<HR*>mP&6r*%@C6SU#AlhiAQFSPnlka-8
%0lRjl(6L,$iZr2MJW+?f:oe=J]'LT>i[O+2bJ;2)JiA5_>&>%u<U_bOWACeQ-qJUCnlOCY8tJ33!i0n[RVE@=Z5WKW>]i_(kCo`f
%>+RjPZsn&H(^S=[CsqNqrg_I3MF%tirV1"5lf&SCS@I,E06S,s(mqA.&L#l&Ok$;tAK`-jOS=V`_EgXqV/h7]5Wghj'bacX0ea:$
%Q/pZ4q@#9e$tt0oT#gsIK!=aFY][f=OQ0E#K[(TrRUc1Xb6U-5o'VFT5kA<B7A-\V2i\!RO2Xke@QGlr?oMdfL#5!NI#3;@_GK16
%_Bp_j:aEHII\hffa$;BbT+`h5-dQ"n#?2BY@Y&PjBUqJ@DU\KiP0<CD'*YY)<7..W?jd2RU9i7e=a%kTr'k_'U[0>&m5lt*&$`'q
%80VP7im!T,>*_S.jf]r)P`$M%$W[D]Sjf*Ed!!*T<U*`@]?>oVR4d#^jmA,V$aG&*o/ICNL=h+6bq9buZJgGf7ua]W`^MtmI#AD%
%1/r]PjQBFjOQjrG2.<heN6%2SohE#2)D+3(W_R:Fb9D$]5s4]jYXmZAdm=pef-3)[3@_O;CW"W"ch9\$ZJ2C$O^>^4/!qT%$jnd7
%@M`F4A__Dr].^b!'D)Z@1uu3C`ek,IpQjWe7#Wg=OLG=g"aCI!O_mEtbu0A3ZKt7i.I+^S'N#QRZ8'_YBW:*)iJ9]>"(RejknW9I
%AT:h2/ojQ_oL7A_Z7_>G$7D7R6mTU*D_!iKfo3=\9*',\aXLfc:rYcWVof6"OTgi87_L"*V2f,dW+[\G?T!b-XWR"`n4I@73(Ghn
%`o3Ft@;W"'jWIB1'OU$G$Y$Lh^r\&E3>]kM?'GBcKrnl\:udjIdT?*6HV*J[8LK++&oL^l1.p5o/4LFfi.6_ZAu9'/nfB@2$t`S^
%RV'2DMZQ-63QYf-@LFkOF-X^7!EeH;P=Ll?^ptJ$b/\<bc=@SlL_OsoU!G93[P%'#/6*NIKhi<UP)ND+*"!N-I>6'.VqpS"WM7IS
%D\dX2kWN3k'""&TpiS+hE7I??GjGr(5nD2qq/]<U>kCe2&7o74Yg5H`kEFN:M`(53YM:<K8"jQqVeHR)f>[L3!t?ek?kh(/NgDYf
%bTaAiXXpI,.R[bF7P'pFSDNNZZQkBH'[B=58d'jZ&><EMAIL>^AHJM.?n.\4A;5]gZZFa4W<%?+u/:^T7G2]Tk;LoKoS6?V@S
%_R:P`[%=.Y`,Sn,YFQRM\FFUlR-7Ohj-D\4O]1T40^s?r]$r3@+es8<#j9Hm7$7eG$RW0+?$fEHQ0GM%;#!jC@[F,jJdtosR<A,2
%[0I>b6<8EaI<T_rQj=^f>C(/.`PtR[BAa$O2-(RU;T-H8T9(RUQn`(_+$UDjYD;Ac<UEC[d>o;2<I8*mPn<UhF2e#"1_h`&9+Pbu
%UR9Kncu"c(CAEDs@^M%bfe]`YIGp#[V1*%6o%l7_9qdIS7JJmB&#KuEpR%#0`"\&/k.5Tc"<sgPr,<69DD>WP.90h]+o#C:aj:k3
%Rr#[&RZK;Y>hG?1JB-Ic23IDk\Zlf<Lb'$?*Omh9&J\iI\Tpk9-!HVJ#t#6_daI5Or1N^*?5Znf-;n[kEji=[1759[5dO"]rFC4q
%\e%T0a`&tR@?XU/(kQ7+MQhQ9IZ?5u<jAW18Q40$%%Zsk/A#A(15SksOGVN)6VjR]Ei?JNabe(7^9Ut@LOn:!.p94%.Q?U%_0I\M
%0r4YZ8KKQ]p->2Q!G>t"\Tk:GUQ&Ok=,Z6lh58,f<oO;s3Y<`LC6qmsq4&*QSFd$U"Z2I(B*F;=(n&G^ok$D2U+-aEO]p-7X(>Rm
%^'g,51jbOL#iZ+FMjSqRn?=TO?mFZTOGV>sK?9/W?PS@L-U@9:-poSP/s.)V&S,)\Q:2L^\cAn#.5M:f?0G!V0umb1&L1]8QmEo]
%;TcVn#,2e'TME1lN)q51;[uSqYUq1V,k`eb]sa)t2Eba`AjH("C(FPP:H#-TQCk*VNjf7O.Lm8?Y_lJ@&C4*&H7!'#o*+Cbhr8H8
%'KNTq#=IO/bZ0Mt_@!Q)XdP&dA=pjY3%LJ04"ti`dZP?&M=?4]rLe?\)GhX,^"C/XmT`'h@[Cl+du.S]GA,id=RV7(q_#:\R2pSG
%#*@KsRrUO<P>'_@5bcq:d0N!7B%IC`@[UDb0aNRq'>5g(FE3*\Q1PBfUf/!/)"Djn?AKF5(T(UJWuZ@<e]Za*4@=F!7J/$]3;=RW
%gmX5(_Z?+@2^r@XFMX2R,$0tjM[=#9VlFjh6)3].jQ7+QVs,G#6l36cLGTAnM3igMcMZ)NJ]V1t`h^HR]+egkanVIKDYbqDm#[ej
%F3=>]<>$KCBeiQ.@BaC$_,H`B@\S.<(1C73,f;8AO_^LDdAnRVFYsC^gLce;/)"%hJ$A7";,CDpmE/^^PY0auoG/?1DTTaST'6\j
%?bNOG>>cKq*Pi0nO_Bq$=*\JP.oUX3.>EE<)'PtJr.-]2U=mF`^Isq:/,:JI,GBo)Q<L?2[uDLlWYbi,,_>IR/*6OAeedTFT%uhh
%`(L[iRYUc\$<.%gkHNak=1,>TG%s-kD&@/Q*DSDqL_3L2)"aUmmcJCWr6#bi1?SpGH8Z`l'D:\KhVS20mdfN>o&``ga%GMKV>4JI
%qNKfca8b:mr8X.DnSc1R]NVoCkth;tr]^21jaV^OS($9O^:rO?IrTa_IsHB^G5&lZropbaJ,8L(e]mZGa3+JO0#)[UJ+<6)O++f"
%r;?!NlsZCTD_J;#hd,#VMfA;A^V76#s%*04s20$sr/^m=Sbq))+8sBL9;a^ug2-DJZ`A<(lMJcVpYS0`N2!YgeM7,Ap[arIbdSCT
%J,8kf`GEn?>LQ_2RD5nla'X%;$9tVZ_@PCj_WS]5qX^cV^-2AIIs<H0Y?/r%)Js7F\)7,H`VKHK\*n3@04Xq>p:93>c05]/Y(+3s
%]RRP&EZ;-1^2>F4`Y7guh0nn_lJgt(ikqfA^\Zlo=Q`nVd*:sEpQtX;cZ/Gg3cDRb+"sfMenk%Ll`IFZ7ATkN`4e6YHh"/KMuWNh
%g"&"hk<\u^9ob7nLF7:inb1ehRMQDB[l'`GiORaV]DgX^rRkOY*Pf5U8nR\M_#=-7ra@.!n]).pNG72:?iI1obiR*)\*!CrMn?=m
%gUe1>1R`4IW-rsgmdC#4hsSe(qtBT.`QS58r&7sMRpG1[QT*5rZ-@QIXl#J/g["<Ghd50C].T^h'J->5GQ@d7Mi0MaT,gnASX[hh
%Q.rDim#*?[%]\;KL+;r[mVJ!rYC:hFq=^K*m=d=)_THoGj`WqV>JWZ;]%fT<46U_b>u#@cjo0gR\b)&,\Q+jH)dVkk]Duedh'@/N
%TDRX-0,I\sIXCebpA5rd6+;pONkCUXjM$.gEeT>FpAX_<0)fm:7cRXJjNPlX26Ur$hECU2rJd8]2PkqR4+$_qmH*$G(XLEF\)l`e
%a6iO<NqdpY/9/oSeiZ[1\+hk`],j&[]l)`?H$aQ2T%gKiGPu_J\%TH9hQ;$&^:Q@SGBJ*'bJ/LMHZO25An)n^A8C2uqY?];hRpNd
%ql'=MT58:l/##2`^:S`5G(/uCq:X::KC3@CD9_C(e/AZM4hn\A^&-FM^O9F^h@P3d=>r`aGMq_8dkp8(r9iB^pZ:kZd!(J=/pcUL
%Im1Lds0Z;brbIGWE*bD[?!$=i]$4?\eo+L,q#0UAc2HlfUX7=Oh(ZRmL[S3qo3Mbe0?tlJRM##dPOah_Rud(Gm.S`kS'LVF?B/=O
%!%XXp5<idCQoBX9mBC3okI>+6EI2b/ZObC,F(5]/\+a;?TDJ-pg!n^2jo)[?(\_\bb'We:^,0Ku8%eIUs#+1hhZ*TAnl^=_iGmXK
%-2:t$]cV=Q[^i*H\[_Oc"g-il8iu>lg<894:HFA#Pg>C:8,5N:cZ?GGHB_330q2IX#HldDh%0':\\5]QipPgc^K1+s%XB!H+`G95
%>*1:^GP:n(h3o'E_ej8dlcZ?8286^de2(H(W7d%glqM*3Rf3#DiugRS]Qnkuh7'jp[sqTO8F<&gEaWB=cc08mo6NuBASVog%a=8-
%UHQ8?Gh?#A<kEgo=86"nkC.=m9B/rVn3)e`?*t-RIPD>crVMo:^Hrpse*R'Pek#Z7_PC7d=T9t`rB%VWqss7C3C(FD.u_n(hk&ZZ
%>[2]&EHQ98:7$A+PAM>J5HO>oI/Nm`s(#@*HS3<QI\#&e?>MR`i^JYhX#Sl%#KM4ui`PTm$bBq*qsrJ=ipuJ\=(g\4f,$C?^H)1/
%0:s5.K`<#TqU9B\q;Hb^5M(]SBXlNHE7_YTp"@dL$7G(EW-@.#Yc7"#O8G&J'Xu3E:+5;r2t&hC]?T56mDZ58oei/\H\qL%6D*,,
%>JFW&0-+Zfc,sFi2j[TIlQUMT[^VrC=5TYKn=2JEGs^uGmT(B+Bt:>p\J<ThL?^UtYqJ_]D,'-Wo&eqJ79oY_8"]hDG"m5Ne9"oY
%]",LWEI6!Vcei$CHV_l%4K5m][\f<k*4sg<^lbGC[\g=hNLb8"+9*pRs2,e`fm[.h]XqRBG/aAc^V7@qr6)+>=jsLD^_M1Dm2@S@
%G,M1P2g;a_CYi'/[T1T3m,mkb'a+@/V]*AZ!+n<1LIdZ#:sdKp;C'DRam=r]@ZMa2<c,aW?]7>tb.^<=]\oN8k:\7uAIVo)hSl4j
%fqEh>H4I2.:[H7J(?P!B^D3cbT#@A9#XN,=BA<AePk14UMm?qMqsrIRaB0/V%hOZ)l4EAEGC*2l.-E64(1ka$p4^QSHS!=>^"E"Y
%r_d6'c<jI#b*pH=bH:e!]eoU';mU3l5D!ZeF"d8,4E"5Qf3p'#&"I*Y2WBp\@[eTe;>>j;s8)%g0IG#q>HfgJg1%.g(`e]e[LQF5
%8V"(Tb#gk<A`ge[5)-8+"(,9;":)Yai+iS3[MO>YQZii!+KiC/EUA?9ot])f>jWW&WuZl$(F?.'\CuM7rBkY6fd$.^(Auq![NmAh
%i,<b[mZdnDE/d'\)V)__>-S3cCNR'U)eUM0UV%ch;'u6s<<7qTgMKMl$Zl+Aj^0[DIXcM6_q.+n2W.U_FJpdu@Z`9]?=1@U]Qq4l
%j[LNFG8Fod^K?3C0X]`frnh!dh`crtE58;1i6qOZ+<fVS1<kTPF24X*4t_h%F7/`%R-$cq`;u7daf0+tQ,XpAc[n7LH22B&[/@\b
%QG8dds0Lf/N]p0tbP!cSO$S0H2::W!eb&`#.(\/icWsOaHuFW3f1kEM0oUTTlHp"2\Z@o#]?\Ybg3'e"8_1B&hqc"Po^LbOH]Co#
%NVmU^N8OiFgIj0'_V"VGkPR(+Al,8)M4H0Zn6?gW`Z3tR/I!`fnD$,bk2:H1+4`-H5AuZcS_[T.IsZT`^P;FLea*!M9R'?>ajh<R
%?G2Jn#^l/24HfU*M"o')h4%fF6`/bWs1<d[h]E`!n)46%<OR8DruV*Q#QO8M3gE'<P(Wf/mlc_as*$^+^FVO2#<96><_AM[TC7eD
%9Qo4jF7AuHI(fiXmmd`kCQI&5/"s=/>&W_grGZb!Fo2,mVfm4YVf2$j#05m0]X_Kp%tCf@\Sd+#GPUCG]=/S([l961F0gTY4Sm%J
%UkTVcr8$1u79j#C.Q<jc3:GVpQWDGS$[S%<cfh3<L`J$W#7K5$6?]Sp4Y`q&C*@pIV0c:#Z3N2=P>g5IR8!7<ci/$1EH1U'+sgQC
%[QMKpJ%GUF0:0.L(\aQX?+g1'p?q;0=hj[HI^T^:d8lVjci38$qVD*k[1kQ[+^/dL'UBK_\[YiBf1Pqne99#IRU9=*7XI`iEiZGo
%^CWio<13E.*r@h[q0*Y<bJSf'bJ/M8L^gDd&!)4G23D:c4ht_q_S#I7-eiu9T2T6,KPg'`52'`B&amJ9F$Fi-W?)I$#K+a$n8^4q
%4>EgfiT4Hl3>c'dfs==`(,<J#'o)*/#K+_;CS1pMb=<CU[`9+i:7TYU(m)I2j_:nM81tHdihKK,SQF0F=siW3(`Y^S6TBTjd&4>Z
%2R-j(]+-a(#,kt^_'[b"_Z=R_X)H-..=\V_&VI^q5$hOOs8MBU=Wc5RESOuW[d0"7!JtML*le?qdgeU&lP]3;^Pa25$1H-nGX,Y_
%=U7\CZ*+E)V$$SB[Z9V8^]+)QDpMbIIX`bDo8`rNoWkdn=(#eaZLW3/,hW!9p(`9*?a$D-b)NiP[+<I[3R5/iLVN9Rp"4QXFo1=G
%gP^Ei%<iVl=0gn8]CWet3W:8g!<8VGI@86nY3jG+/XCh3csE5sk*,\qLJR..Q`jJEU1q5V]luZCREW,Vlb]<riO5V)m.6,!XHB?f
%-q^V2`#>=mM_a\(@iW:M%sEmpe-*q1Nj2)8c/.20g'b,pXI6uBNj5pZm_7NQK,ru[a4Rej3S6%rG)SSh@uuFgJ$pi8lCac_"T@]b
%7h0t#:0i[fZh+(#X5Dc?Y)1,`hgKH1f):o_rP-"[]K^nk>.C8F;S,/"?EM@2hIsU)agkJ'^j3XSH[G:`]8Q^`mc*AVNKWn+rO1j3
%^;@7b2i,Oj0X,HO?2nfQI=GCBr8u#2]L)pWk6'(m4n8iNT`[@%5:/G;$N]En,>&r[H(LP[f!0MUWtcqOmVg\_oX?n7n\2QG]jC:S
%EO1p^a/:Y<Hjji<V_\2Jn_N"[n#hrq]q7d1(PdIF9!$ll9>W%Z4kZ^3p>_=K%YiChRdj#Xpf]PC]a=I;1RT6opKBA,h%02t:\<8b
%AbR8d$89VsE;q)dlsP\Mq(>h0h9.[pdntKY`qU:2<.BfXI[fom6'p=X?#JKC3G<Kg\Le&'m\%fF[(1fI[lnSGL!4egk$EWtGDN?S
%2goQIDT"i3gBD_;`C:lt\Uuqe;!/>Rgd.M.8.?u^,^""TAG/K`48(<mTj'I4h,ik?fBqtk5M_m4H12=?ge6#W?<RAa'ghNnOCA$t
%cH6:d4XgKBpUCh@I*;\q`#do1CiOJKLO&hX!#aNG7UIb,WYAS7f5Ih;cN!gqOcXIq4$13is5@<d=[1g#s)>Bc9!.KhFm`;]OkuRS
%ZuSA-EfGN:#B)86d>P>KY!#o@nJU/8g,Ufi3dAheQ7">D\*\CQ][\m.^-"Va,m\=ohUY!irnCG`qH$i3r:k\6ELtt'+)G_A.s=XF
%D4]P>E#&((]%t82Chn2Eiapu\]6eUBI^/TCDDj^-Y^b+Y<gRg^Rs/Rp@G2M-FQ2><]6EsUQe1\,q#[\[[PkRq5ifJc>5m,GF8+1-
%>>sN/BFC1iT1XM-`?i8WVs>>.5Q?rM@4#rn:],Z6F">NtWf@#KYM\0m>ca(s*rfc+"Wn0H,:&o%5<0"P#LVr]:pg+u9#R>/m!@i^
%l9QiP$_cc,'E.+sq,7%)?401Tc@l4[p#:2nc2!$4b.GDMqc5C[^Q%&"*kjj'k7aIHK,SUmGcdCOF7:n'F,2o[`VoVC2B-`,]=e_j
%Gh&FqaK!PR0(6Qg^0"CN@'<nXQPR*ChE\dOn)Ms[rU8&bT=)i^*lf:5BY47'Y'F,mrGc)K&%hg#Y1\TJj1+[hGj!u'O48gnDr^i3
%NVdBT4qnsk<MAU)nA>.WO&%fo]fZl[Ws&.tO_DK@M-_sQlj%NJp+^[Y;qHEOk#8SK^dS1Cl$o<$b:"Ih5Q5`KhP&mlgp!?!\usAF
%mT.,$m)>amDp,?A^*fXhY;NqmgUl@UDmOVV9tW$.n"i?uI/8f3D;Lu"a2g5H+h$f$8B10m5freC<i2umkBDERI+W;mr]LIRG]C!2
%+!ISraLeb`:(hRnd%p[9aUjJ>KVN&gp1EK+OC(qBFt_tAH)"DN'COU,g3\DI-+uZff"QHOEVHNW].M`$G&<#Pd0e@mrRj,2(]Ubk
%rj^@%>J&.4q!lUPgZ0]rH?XH0LV74CYNh633ZPL)3G/<"T?#.tR+XZoZ7iX)*5X08i<]AOmd0*'Rl-HFG4<_j37GpgM.o%)FaeEB
%aBf]IA6.HMU^s>`Vfmh_hEJ]c3bUMQh=GpuZ2?F%QChgn+'Aa2B:o4X]<?^0o@f'F0eF('legRUr-mma+_m%&rl)jK/oTm%q<:\(
%q]5OlJ,JkmlYg[EaQ[jErBL>3*I[q9T=CZ"^AmFnhJN7!GP!un0DDKEdX#:"BXss)]$WhP3>'BFhHZd>P4u@(I<A#o6,:iK5LBH,
%T?CUA\^8&T8k6WVqEP#3*PGcCqpcd;qNKt%Jq$dKMat0$s!m*dNik[o5)+b4qlA.\4<e6Z'j<4XXKPOf6;5S<]I.07j_>,mkM/F0
%HDlOD#l0`m`_Ei%B/rjo\$U:[*D+0"8j02^QXsX?+.Yq-iDoE93cN8qQPH0S/VAnFC#r>i[3,@&%(EoRH:pX:95fAC%e3PY\P^E7
%<HbdDEEZsBo]G_FlDi.H/^n'?5/-TqeYBlMg\<65:EB>H9Y"RP@dm*h<^S?)>lHkhU.)PM0f>MUDm,K,U'4lc=\G'hmFAKb$YfF&
%Y-"nY0"oE4G[8=CmL#0$dVZ2a.GoQ?bmD50$m+fs14V=,dJJ5u0)(k%o4MG1dkP^#>F'ZJ$t"DPq>'iulTa1]%n#g3^8>]8ol?.#
%H1.&]e*1X2<R8^IQ=0Mu=ZaZR1cg@bl-"pZ`]j0&3hK(aH@?TT8i$%/Q@G"fk0'g%V^eJAs6RElY@bn!*B6!Ns,`EKjM=R/qZ2br
%6FUBF2'>HW[QH=DA6C*]75cQWB`@mhp"8e_2tLY/Pd<t58CF;SoA',dM8tYT=F9G+]0EXQg**Ncqs+1'gpi!jRAOtQ;q$CG=e/oL
%I\H,rS^hc$!<#SddG3?VXa>^74*SVd>OV==7Ip&1qq]keVt7*P2f<7Em@hWc48#+:#fh4aeml>!pZL5'f,J:=DjSsPeT]6.5J(*k
%>M;%1^utF7)dDXQ/bn#d>&rX!SAX4ZY%MVFC0Q>9U'#HPZL'q`d*GRl4+8:#bJOL/Y4pnc/M!ZQCiBS$X@Ln;%?<WgC$4+!UEn\f
%=;R7E=f.t[`k+2(dYBKgj3O#O8gQiuSlt.b8nTNC%1&MOp/aC5I/]o"^LE*g9:![O3oAP/[$=W@<ig:niHWie$>j1ZqO-E>D(OEd
%@0ZQh)XP\1GN^D9pZp25T5O6Z&&dX+cW_dabnKp(N.6"rhH\.JXElqf-%9&[;ggjOY;#gC5##nfGAg7FQX0:ar"oBN^\c3(s6f@T
%Mlu"lU@un2F:R$OC7P0s`h#.sQoF,>dC8P]FDdF'[EN:Yp?o\M,!#rg><^B&GkmC)g'a*/cIC@=.)(`=ZY*$4a.dnLEHWjnin8;B
%gocaHR/?L5\US8Ii(&Ln4ge^>8_FXC?A^Ssfqf.pZY$=96a^rXCi-FekLkn@CL?2ZWtfU4[1$J,Dt?`=[:L3q@;BM-5/]tP;(Nec
%Zp#ULHXH-0p_BDrnJAq>rY_TcRih?0*9K1liN;qPguP&Vos3TbC!tPpg[f5S-Zo2)SVsQ:epTZcAt%M84i\R`VR]Hng7pu()Dl\X
%qF6;B)HBl$,fJu<?V@HCO=E,-ER)!`.sICukL&\aMpFU+%e?sLB%=mMk>[_KJ^88<f]u@l<,(TaN6*#OSo$T+jGf`!_)On$^Z(4"
%g<pq4R7s-mF![0XL-GcD6Pj1a=+-XG[tZNVKsOjtG\C8'R%H5offk^t2HfG7RNDpkHV*(o2:^BB;d-T$V6]L`ei*K^SpkbT^NifX
%g_F48B,PF8N2BmSA'iQU[5fMi;\OBI>F+A8qRJt8SXd=]bT!0:2cHbcYoL;L!Q=0`n?Z\G^E2@@?A3?=@mW004U&lMW8.Qb1lG^L
%c<9fIH`hTH\e,X"*-.)?/RJ8B=dqGD^1V`;]Kpf1:V.KW)HN^aFjAjtkB"Cqkt/B[.6mR8*^5?(cRq<;SF9oqceZ&q51W$4_q?Mn
%S*g.CY+DsU_D,jMCmkqAcW7&$0An@OhsrkE\Dq9enfm8baF&c\[(t\eAbj4XQLU>s#1op0^aV$&0b'u&HHsR(IAcn`A(l/KlS4<G
%lO9?=^<23m_R4MklFHOt+Mh*qFAVQpGs,ce06RQ&<>X4Hgg8j/m][0"/+jB!VQ]YhBMGAt=biM0Ms4?g]5)]<qSsk/[%Y7=nbLMD
%DE@/DDE:nkO5$OM*=/aAPSs+*VTOIofL8#OGtWC*7o#c:Nq,>:`s9"7/sarLm`]a'1mt`'1RYE<4O\H5!Bejo,Y-',^_)U%YraUm
%#rbNVdUuAPRqgnV"nL7(Hu:7f>k?9ZOLKD^S,;W[S+IP94X)D)8>i)8ci(G'6P!o21MkBbg[jJdh"bs8Nf.I!4P(=:jkL$Rq")Rf
%9tL:-H1(Jf./Q25MRD3LdH^;,D<2=`R<#aiGWP,:Xeh(?\mICG5$oY[r/A3b>hX>INIY'oj^B%\'7iJcD0ta=j,7(s'clum4>52p
%SXsu8)\R'YFab\+HpVZ6hRAbf/"2o&cGZ%ZN;U$;`(q-DG9MH'lb@^0I-raM#B3*Wj(tdY;b;n&)k&8LqlYN0mu>ZK0V;ZAJ*t;^
%/DJG:O-bUioNfBfHCIX$c0PB8'>@-%2[or9`NaW.c!cg>o,`'bc__Zol8eUkoYXrAVEI6i8%?^>QXCa$C$4gZ'!IDGMMlU6$Wg[/
%(;<9pS[[CcKcaCi&Y;3;EOQb-l<k<iMgs:DEQ&qkjl.(>I!40'4j:m3204HE,r%n9Zn)EKEaY6`]3Z<*=mY(TmbBRfb8i]S#@eNo
%jC(*/*>;I$?].C>U)XK>O@S6:7<[`<O,qMo"N,NXf7mU`eW[mMHru)A;@7?>ENHm:@YCPUpjf-t\V6,8?u2C^Y@<0+_b<;-dZ0;C
%30._)iuH!8p*.+h.!k9bb0a;H*tc8B!*q7JL4jJ`&L_q<U&Q[&OgDsrA7U:X5`T>Tj2uhV)A%G&lQc;$+PoNPn[1)L#!BuYL.f@S
%=Z]:sD;0Yr?LtSm\%ME]:WsYa%YcldUIa\oH-p>k^:o%',+0rVO7+RZ6I*gn[p%T'Nj;jDJ>"I8RD.t-:RO:dc.O9-osL5"W*/fR
%.lb@.n*BXhN<'s2Tr3ehgM<(Y,9BkcjAL1t-3Jp8e^qeESFFYTqR%UNFZ6qWQ3ciKZZeU2l[@*DO7-)9De[f^p$eGtr3HXfNK`tE
%;tfpII.QiVrhLq0Ru+4LM[r"(g'XuMqr<kBnRiH<r^;`q(ZIARrYq3AkAHH*QOL%U*\do!L2iAC!PWsm]Qn5(H+gk1(&^C0aRLG!
%[$8Bu5*!cJ[lA"!*-@PdeN,E*SMai/eCQbOj4!q^(HTfRbKh%Qq,uP[p`KC-k]k&up-U7(J,.9B<rTK*?a]>Mf2kg:aKpp$-SE78
%o6@_92kGFTn:1DRVtPUO5CMOah72/XcJ4n,hrfC*o+&=<fD>lhdJE1roGJ2.9lKMq^[Wlto*b#bZBLf4Ejiiip:-_+Aj67jIbt)q
%H&qk[ZY^Ib!HXhaFJnh;20dr&FDK8N5mSSL$Q2t?I<\Y&EZF#ifjU(tCLYH;@7]Dh&R9",0([MR<H>29WcNH=GoueUpYk5aap[?:
%>D>Y$R\d+A!6Ia'nR\V">39(49Aaes@YUS#`Ys^;*@pHgXgQ1dGruC_*uaY8lhSrG2l(\)52[7?a^)AniKBeFN`gTeT:*ttJ,ec&
%rSc)]`eCNUbOBp]0BsnQ8;BUg9#@b.DBK7i@KQT[TH"8sj!sZ&i9toZ#CY^rX];l2\)`8i**';4:HTibRbu/LV(2h($rRI#]G89b
%cCK:ln9bW`%jP%$>ORi=WNArcmcOdO<$(aU>J@Y9,l'fTSStZP!2uPsIDld>Q#oM[*cPkSb,s(/E]7/.7j+HdE4b@HoOkZ1BgJ?)
%j-*L>ZC=@.$NlgnM;tU\,IAZTlIA@;b^/[/#WjpG,3)lnYK'*t.WNL\kF?$g&WEg6Qc)@D.\>/Tg^#ur/]ZSi,rUAGMhoA%'?H/P
%FNkmkIeYXKYCo[jiCSJV_FTgdbW5%eVi>X:DV8HYk.K@,,Ie9H*>j"Grb*ArW]53`IBXrrBF"%W5'U7la(Ke287-N^Ho[Dl4;hH?
%#QZ-Eq-D'7ik9@bLtW8GlJX[E!-mMEe4b#,Ykg3dJK%2iL*.$fDu/Cj@7M%+0UQdM4dnKJDts\Fd$.aLf9_#l7u[B`!%ik#3r0%K
%cof0"=p7S\o&f.G3U7jH94]=DbrPR*M"Z;o=l4qGDe;FuV/Qsu3==>Bk@j9=]OR!pQkngS*#tTr4QDPGobn4+Pf8]=6e-Lk#2gI[
%P**A.^dl,(R&?%TEMk0h5<H>m0>j:&p#b!-bBj_kmeV9HH7a:se%Bb+Z_@VkMF/1KYL>7]-9\]U1>]aTO86NY`"_Bl"#KK.@MD*c
%5$VdIW")b.I8<Ba!=u5Hc0m`<Dk>@c:@jrFs+g0<s-XW_!H?S5g+N7DU15L@)=TIC=B"Tp4G:H[W5^;Dh]\W3'bC6)MHfWK`ZJXn
%NsY:1i6en4iFppH!75,aIil]r6&[ThjCeNM)9Z6:G5)GR*HUFmrG*M74-"G^TC2&mqrHA4nEEVoEMFR@T/Q)u'8#i3bs'G8N-ol9
%%1lBHo!kZKIpJnPm!hA>2%Td1%K-)VrU0KO#0#8U96Hc#375sgaJUV9#mll93BABjP;3Xg%4>W-f#d`0gb8Qic\LS.rql)To,.5<
%8*tmY%c/a'ptai&=S1''"IC3XY*:',:MU9p]klf2qiaAl&J.j:7B$6"A8D7B-ECNX&"l4WoU(O[@^#g#s1YsLF+QOYQLXs^_Q^mK
%?%mZUq-s'rH;?o@qMb#i5PoeB/H5[qrXfknJ,,^dKj'UG]_dKAr;7AGr8q(q"X33T3O2I.ctEa7#-@2h#Pj4ook4;DrZ"5OF5u<<
%0C]Pnl!qdq1m3uVpt9lH>j'F^`as)Wria)p>5[A5rn[?X[_l1]qtj/I_uE%Hb24na<K;rlNAP5Jl5@Ui(molLqauH'HOQb!qno#E
%Iph'Hql@4"@)a78h`nFTk<@T;kI+>5l?l)E\d<"eDSZ(nl[Sk:NfXQgUlqqW?J"fjJ6=L_58jEUs8I(4OBei.rhoekRpZ0SYJ:&3
%YJ0OX5QB3iI.QrJ?iP&\WV0Mbjij+hQ<1DDo_pK"Kdff,*)jS%hut,`j5#bX+aH7q&PEc!E?``RHFEV#T,IZ!po%]jVFd[#q2ptj
%^#3Jm9^skoQcI9FVRuZp4]'FL9^\r+Y=jtJY<OiB&-#Mi_>n!BBV.;F_Q#IXD8(UgS<`#jr3Z8TAmUsIN5mR:H,[qW*1QWMnUH4W
%9'<3)8:&TF"U"blnM9F*Q\h'tnabjhIGSSl^,[sV?[l\_+5r]>0,@5"Kl4lq`-9l7*oL<B&2<k/o.^/qofZ)tX?2ts5%;N<rHRrV
%@I#']fjV]le>22Kp"m73:jk:C7b"7t'oeaLY\?b%eTfq[D=gr\*G&'>NrNU*]aWs1IM>gq+//N*15PPkA`,G\F[#BHI,&5Q5')bc
%2>6]U`r?W7W\C$&Q^i<&2.H/G_;lm2Drmm`#ImD[%p.\'QVg[jQQ^#Aok2;ETg&n2I6&OU#gQS'_o$9e>`nt%#.V;(_9IDPD?eI`
%"FQCRj`aD$S?8kcok$>\=&\gab5HR<qXjA4,g*@j\-tj5/pUt%n,)\R-'iW?<Sk2G\kN5kAA>bFdSn(.qgCuIH\Ra)GOP((Ha[E5
%&beeR@kGkg*WHBE-sK,SqI3WLq4mF7N=HNeT=ge&^(D4$Q[cHGPg;B;8T4!+q&TE(==553G+1N^AZ+1-.$RBXb.<jArCO3eA9m$@
%22nBnlfuaRIuM'b>Z2=>;;\M;;FA.Ake>(eYUPX":\4"rPTP7R:54V8XRkkn:WEEsp)_dP[aNm7oha+gMB9K)hu!4sX>h;55&\0j
%]uO`1-%6p67p%+VK+Qnj21mq5I1iQ_3hj".e>;5.-BpJ"!oVCjXD9ufZ.?DYfXT7W3rY30;&f%?gB24LIhFM-I!**EaF-6YSOd;*
%HI&QSG1a>JnGKiGF0;e>4s'[Cflnu*3,aBsDOLL">nfTf]?o/S*"J91k]!=FmC!SRVdTb1mFRsJreLKM7&uha3@Yc`MZ,>C\!3Z#
%+[6HFlZ#"iIWc[6<F30/kjcg4%-Yik7Jce*IU'e3p:qtaq9XrnqIK6eRt1(k(6p9S^<9W`W!g`+'sBgh5!?*pbc7;-o<BD7!$lOd
%4GZ=hY<OiBO76#b4Wq%aQXN/F.E6r:$smPDZl^qc`iJ:;roj.apuZh,ofjApa&si,'kiej&be(UM]AStC2Y/:'[*3Cr9TJ]ee/f]
%(_B7(<M8O*pclVcc#NG>XrD5aS):uggjGMHpZ(?CaqZ#E5:MXN@[kmeNr!;a+9&$U#&"<LQHj]!RcdrplE>TLpX9^7DErV4rn(a!
%.1C(!'#*'IU]1VC`*cr!$MHWR-^ro<YsE;"Jd7lAF[+d'8#H5(_uC"#K^r]V#'X1&3:JQ"0lqGCVY*-EG8T\uqtsrZm2i;HO2!FI
%O75lB`9>:aBSV<%9W"L'*`]fk%i]OVF2[+"%E>+iD6Sh#I6.38rC-OWa>Ph)J+;4\r(O&XfC1?a?EAA8.^1Y?E9DV`a/n1iTh'4:
%Q.RMPhqp&.+(m?hXc6NXlr:um<tc'+rPI@djhEmLE4#Df<cri]I)f0N].'?OfnVe/r1AD`ps1bg4T<U-r1KPQ1Z=q7mD7*r,X*12
%IP]dNcWSO%C_uG<:1*5cW=g!=o[Cn6ihs&Ojd`N7PQ11E%EVqZAiu+,r_nROg%PsUR(6;Ys,kk06=JHk3=2<tjZ\/P5Eg8:F'!a,
%21,B&eHAM*c<e]RC:V*<g#F#[0bI,*ER2\OkF'9Rp38c%o7(fUT7,&TcP,q5r#](c0B!q"2K=%4pt;j#N=l.gY(-EAhu<>=hu)bh
%rtRMYs5WQsq$Riorbqh=a#cDTqO;F&iKsT:gYX]is*k8$iR?OFNVEJO^VBcmDkEU%2Z&k)mg<ILa42ZM^S`0E"h2I`n\`k`&:a7c
%_#:b"rpmrcJ,A3fhu<1Js+'S;5JR$3lgOf$'#FUg\%b+Z]_gI@roVi4j*^D>k:-=a57K2`mV542=J5`E^O=@J+(*/("Qk6/;&.[s
%`pGnC>D3WfrW&,o/H6Fi!0N0&;1Qan-43*7n56VNJ=Seh9^uAB71T6o7QB!j:A81)5_0@G`%/k.5RNUkOO^<E[[N*Z8:^%738?pi
%p;4>[C>7[W@jYdpiB\8Y09'j-QoU;GmJ]#\:a#A-'.TrdDUc\>:HRL>W*Rm0+-W/4FI]Q:ja)T.^JB^*Z44#?$O\,m/42%4/e?=E
%?,IOmbq^@EM[b)BZ@kr:EucDjLQ%"O->^^Ai4q"2'-SV(OP(3UTlif2_og:M"P&VA:l_ZGml(50=+[!AY`SYd_-j1f(oeC'L70nC
%=iX[`s*T:L,ClquddY7gA'Ol_<ZADJIhJ><=O&b/L'9I<ik$64(^1[XUnqoS@Hu(^U*dEj1X1#5gQH>M2$0fA\l+FJ?IB*jo![2q
%22atl7>l8@q(1$$MXs;M"VH5fpuL_$=;1\oRGKaG;]d_rg3mM)/ugXe^mH\2=jI$IZjC(US:j?*eI^onMrIs[ZF'>S69oZ8EED*8
%A6V\N@FbWK:(\EZ11n)1'l:m(>a4Kd0K>ppbG=%*]D%(O$I8Yp%oaX?M1ATT!ZCO@o?-Rsp6%E;(`!jk4qk8Qd.2YRU=DMc*#KH\
%2fr1p4VpFp28E>lg'oC,h.(sZ7EgCS-M>di%R&o_L='89agp:V8(b\)qBO9,CtrM,,skR)P>GV<NKjG&Y^ce;&J`/?bFQ^qlo'o#
%.R48`:\k4RNXsTJJC&+9'JcE@V-NHm<'@E==<M]r"0&T/pMEDFH7NgI8>WfX@kWA?Y8!,,euVG5flQh(Y#)HNOJtIimC8@!IOkj`
%Y\be5K5Id/!d:4OmkA0WZ!o6@=\N:MU,Zj7@"V=a6<9O5o@[MO]C$)"ej9noN.0lLoisZ?kGfC4Y&]6(m&YDBI>LJX?3\JbHV6>j
%:2atbqO$\P7hW%29JkjH[J2beZ!kYt*q!*3]L<-(I>kBcJ$+sU;l:uD:IWqR;Dj_NnQm1ZCHTBmF]kn[%J"AWa(/0HaAp)0@?M8h
%i\L18-%$fORSAp>O5<89Cr2WN-7X;2-q'2iKVZ,NON6r[/CEtorQZqXU1#la5DNNF@X8@e9dt0M[25V&?ru1NIP6aSR_s0"%.7VS
%kC/lWLV+h/Cn^PDN:W:ob@JD/NT\M]]7e>rC_BqlJm-C`D76Vp,\34Xk=niB)oQ#VSi:TfL,q[-n+k]dXP+R9PuSo`Hh?`u,MfFr
%?sU`u0;feY+4K4`.!M_u%"QG.9KEf@'%W26F?h#J0o)UE!dufl2D(-BcVuJB-[tO36!\kd$$h6acSC8eEc0NN!"cUfs%l@eh`kjp
%Ui)/N>Dq%68XH_#*B_1Q#6Er,=k8giad4u<"M!UF#V[6d@-#L;Kn8erg1\.8>7%Wk1k2$J3V)guEW"\TrJ^48#V[pFQVs/-#6Iq#
%%<NBV`?$.#4"g"KeFe%in)ncBm9\e#aj2*M+*G]V$_TT?%D(:H&$;`Z;VnK#Mpr6[4Ali:J;6iI'UW;HoXbjHa@.QH;fc!`"&@h=
%1'=bKYdZ(:i-b/&B?k<8+NqW9aGa84I0=?_AC$Bnl[3SID2Kn_k_8!=IEE[c3B*es;$Pd1c+-;R%"#Y<H`Br).M1'Hf+`hmV$l7`
%n$+XFKiQ_#ff!?;p)'K?nk,(m6:q@r>*k5IU1h7V8X[nV_'H]`/O"`!\"gU4fk%[Q_Xr2p2u!iBV:0.HQ_`1+/63kp$,OE>e'HuH
%,t<#-.*cXG6Vm:B^t>+CmO#edAV?*k.g!Y#F4uR[L&T>Mo0IU8i;63s?LOVE*"e4L9&ZB*7$,Q7b-&`);O.^`#\;Ej)[/+@nj6po
%*V&I=&@_MQBhksZ5\F'6[`;XbNonn`a`FL;_WKZC0<aR4oUu0$E[tb;!PfW\cJpJ/^K)#/.bkX]EtF#AmACQ3-<IXViV2>(TO)]h
%DI04B*g@n>9B:8.5H3VBifn6;JNq1S_ZQc-Yd?ToPrljZ1FE\%iMG<P?s1t>qlOljGP66Dk&cgMM!d0p",iM"2\W"-93Mtk7ahfD
%F>#_ISY]PX<R?GM,M)L>7Y%Y+F<]u8^Y^kTBnhZCrR^JOb)8G&L&&oYMMP-eohVOcqO`G\s64$C*9od%BY]7kdd"ksN-saN.ak2^
%OV][@N=nl"l-Y%(4J*SEJmAhImW<C7%>2Hn,jN2NY3c[;Mdd.9p-1d53N5Nl,V(*k._I:Ip*HU<F\O\h2u_&!]1:!(_2Z"'!L#K=
%X0gNH@ItO"el>q$NBjkeJ)*O[P)i7*`aTAWC4s/lb'SMW*lYi&5fYC:M"-2Q6=p^PJQ\_N&Sf7(TIfBjePG%.)g3oA'\hj^c/<WT
%7#OGTkH*tR]F*'ukL?iWK^P_O/CT&2)]8R%HipCk8)p-HQeiXt'jCgBUo`$JOpPNq)YY6Pjm%Fn2.bJn9Y>HPU*sT,:u;6_Dk+nh
%#_Vm;8FL+^o>btAGEC\:;0Sd"d#X<P7sTK7C'5l@T34M@BG`3b&cF`i_tGpm`pj>.Mb8m>L8&?ON^A`Ea;9I^\=Cj,%k!6'3\m@)
%*OTp8">]Y&N]NOmFMY6tJW'`9?52,ACg)o;l=`sU5jCGXkqDQIMW0+[PX#q_`0A,5DG1=S9G"Z-/H3)Zh2GIc@R18,bBFFr5Q%c(
%ejP/o;7W,Q?fWkc;$:[^`^#Urd2WI,OE.oc]<b%1qUFfW*LA;`mlQ-9Rm.SmAo]]I.\MTg8T\dF@jZ0:@-j"bFFk'hFkrR=$,0=*
%7@MGE%Mc[Ha>gsZGu8*(#ul[*%'POWDuu@1KNP2/nP2=LWu2hI)LW-4Rke'q_i3Wh&;fR`bNS5`Ed+:;EI8cei*P&<-2)d'UuR.0
%]1I)EAo.1Nn6dB@7Gf&,qc!A0AU0c/K2=8FA3#6I([QgJ9rFLQ6>\hFEVqim5E13#lk^>Q$EPI]JNUem@iEEk6SG.;1l*X/ldDc.
%G<oY@j`.;UFuc:HqKoS]TFQRQ[A7#HW2[)](_D+Fs.7hojC"YV2WQ.R[!T*f#;6"_)f'un==P*WLA2<L;/;+6*r/ph/_+iN%%CdO
%F,B.75tDgH,Lk@CkM;#^QjQ>j>Z%M*/1`WY+P`sIa#fC!9CdK'G,DetiRQ\A_cjKY!195(YAaoKijhH;Y9oY/<tYjEc1iE1oM0A"
%IgEUKP.AI'a3!m6@bJlu7No=bmOf_%0fN&Q%+%=#&q_<;qROK.*=\!.qI?GlTbC7&MrIIUW&hD;[;\XkkF<8bp>:;0]$BWp>VjED
%&8[6U:JTGuGqP/Zo9rmp-b^P]1!-2n>J)LE8E,Kp6PaS$[:n[-YE`FPX`nN8WDc[QZMCe:85!-ea_s>ZSF.>PW,#V$'R`=hpBj@B
%7LRK*aPs/`B&XDgJsr25<T-SHP9"4l.XkRbgBT%oqlU'I.^BaOh+.KKk/h6/aZ%Xe-3O!1^Cbj@":9s2X3d9a(FS#Qi$\qL/.BfL
%\b&8bB?0+rbXpR\mA_U3_U]U]99P/[H.TbZZ0>/2aK'_8ZlUZ^KpR>n5c&@/-^f>e^g0@qKWcsRNo*_$4/PZ[@`]3Fk[mB!RXZ[=
%/>U7O9X$o'2nSeqZJqIAd.+po!t8`aY]!%G%kjM<gkn`@+F\'\/U!nte/#S!>dplW$[/=OC#ZP_,?!q+,J1DMbmVC]eDWd;U0*V&
%YraEm@8@Y\!`%G,`k+1n=^^3t(L1L=*:Wmu$6.&aWEcMh,GU4TO8BM63RS.jTC_\A>C23:$tcmX'^TkVi^"9;3X*-c_\NU@Mp<2F
%:0C:&*j;Hp2^fubLjDG`E$V4nn.)QU-[*/HZP)mj6+tG_;9)'XiQ8t4(_N*f0;J-S(*Srl^d_:A\B8Mr/gWWU;L0Tug@B^J0KR[B
%"d<3_4OJ%g(@"SR(Pidl@hh!Z?\OhYUB@J;>mD;_D*?RaE16%EccbnN^:O;Nk8I:5F9b0\DAS)"fMG8YFa_5l(hfZuP#!L:p^hqi
%>cLgi@lsYQOQAo<1RE<(3hS]SQhkM&/<S@)>?nd0i+KG<j5+1T$rC)g8$!!NOma*cg>d1<*]^ZbiGMnZM5ffoCpRC32P!4O"t,rN
%[c)=BRC^Ln"i"h$2?]GTiC^0YH3S7"9-%I722&bD#>;<H/Nk$#TUVFRTadW>1:RK'gU2-fRP&Tf*3;),[?:DlXpUTN6jqNu70/pY
%)t\geH?lHZ#<1465G0N`[Y:I^)@%HVdk(tUqJj'2Tdodh3q@%r-qRa,6W8(9Oi?;:&"eBBg.Kd@R:;3Q+o"K,1X&-b#T4dlSkr,I
%(^eX9qcScR,dOY,8cPW0gr`uUoZZ":7oC#MYg/^=-?9@TOX!'tV)1VEJJ1o\aXU2L(LH$6n&Xe03;)1,YUu!=.0<qn`8g8P%%=^.
%79",J,KeX/bsWcsb1JVr4\+nU-B]B=5J&Sa''sSZ].!j5HF(D_\+TtQ9L@"_NIh^R3[_8eDG\&3p;Tf.(G`*e2BA8G#HN4pd!KOr
%=?/;Yq66UYGRU6#94SUR<lVX1@&g"uX-&[P/IrYjeYe(L;HP+rJTkI;`R>mLrMJU`?ZX``CZ"Fh@:;Rb".0*RRVF#_n[p8N9ltqQ
%n)9*YU8&ZraTbK@'WPt!3*9HH&u6fX_g+3u2[$k!Q'o+3-)OWlOqR+B7%DHK)??C,2;),TefFg8'L.ncWr-gVI3.aV/6E1`0Jq8(
%U!.KYidf7qGRluA`YWLY1*'QPA2UhpbsX[JiSuUW@;d4YXqtIn/-W>b3n(dh#?p\c6.t%3EfP%,b[%0(-1jQeel6I".(ol3P9bV4
%EHPSa76Xqga@R#..V7V7`Pa%4H?rDjGaBk*#@Jc1m994q[$SqaKP:7#qct1'`LDsdIK#&UGioa4Tk$>kF*4[^[01j4E-G\ghZt0F
%W/Ne60NKV.Tl$,614pmDEZ\g_bmSNh&V8i6PS!Y;/aH/SU!8fq@j]K2/,$KX+D-4"_P9T/KN%.$^&f_lUqi!9!)2srl!N@aQdWh1
%I.ZTr.neIu9*S?=Xa9X/<QeOM?Y*8dSCtN*g!C("`=->FTH0KP>o*Zb>n(&;/.FQ[@=tEgXb=:FM+:2T,=Ha,?8+K]-,p2\8<2sL
%.JAl<11r.Yh=HS%WZhI_O\1L&>Y:8;ePfrmeAIYK>41uR,KYs9QAA6r\@,[2"k`TAR%M[+W4+h&EN^-;1Q&-H!hR@MaVY`!#uTb^
%6>c3=7_LlMc+JfCHPq#XXr/1NI:2^T.ZS?g>Kc,B/(/.IOu_sHXf)8^ESbJW(*!O6.S"%',:mOXmK?RkCN>sY@EuI)p./@W"@jO=
%OplU*0X]kt_M.VNnA<Jo+X1<T%b+e]_PBX3fE7M*I$K['4udXUP,9(6\nV]I88q=l:#(_eLI`7Z2eY*d&Md4J#eB'/'TeC,6[f`n
%[iliGDLZ/hT7b?uliMik\X>BP!)rqNh[e05B@d_F+k<Eg^^/)7[q=c9rg(-$&T"],m8]F[1_7-FVPh9L=@('+`&;;-'eEfURiV9m
%&KSIX%SHUa(p=mXFbj)l]8lgepB(0]K<kP^B7=^7R.=5bKT6(r*@pJoOs0amDZIO(RQkL)Zb$Aho9-Sls"PC#X-5kOnQHpaco4F?
%TQ<P#C*!#42(:dI&`HY2@sFVH75)1L'0kh=XRiME%8/d$5%tF4Q/lU#e]+PO;='mJCDk\g(_Mh8T"W7hKnLS:/lZFMK]42'[i?^N
%Z4@&1$CIJ:Chgh6=/d)O/T&H'4#Vku&h^,0nYl/H3n#IaDZMeFea*0RDqO&;>]`-3%G>M&](Zj"MQ<l6&C6OU-EZVoqUlec8QRaN
%GZ-B:65`W4RJ(2[LOslJf;G)g[QSZ]h?V0I+))D\'Hac:HE%7L>X[UTEbF6*i0%ZuUOAqSF@)QhkY*kG_ju23.mN`YNF^i1$B@i(
%dA$b/6>*a:3CLVJ@D>VnQlF4R5&()BknfS8i5R67#BiAtri$i;_GmGY_u[S<$e]D5RVKTkM=,mfZVQ%@)M9NE)i\5+5ds0WUFP7M
%[DrXVU%NND5rdaG!eAtU0#Y4uOq]dh(UTF9mk$bD>r)\";J8BjdkhmXc-`jhFgaSXD$VZ.-.rg1(q008H7G>#Q)eELPq9'i%#\<,
%dS:Sk6@HgL*9C<Z3D8^fFJtUZb7G"T$P@e?b680F5Y_*4N$;F`n8f/gJ]S5aA6#D,EHdCj4d5_KnOOL<[Q#%eUc*=jOV+b_K]()4
%3VNo']AAar6Y&(!S%-Y%r_),uL(:B1H80nGQqA^d.W<1N>T#iemqZaeSX9E](oijZ\9#,tNngdq]I3B[nhANFZ6Qa\JG>&u"0RUs
%?=j=PnE!B#k#rK9I\DNJj>*E*j*Jf7K^m4S/0sDf^)UVdjLl4?c2BNbPEVSRnX;rK#41r\E-56mfF*2>FA=(W(-DT.\?;'*T?(a$
%MeR1):,cR1iLWl5'l3h7(\'@!p%I\13g$H+,l8fmc2_Z=#[\'8';YWg,>Nl[cZhTpRo6SO8GV+-ZQNb,CV.k(H[,c[T'B7`s'3Ff
%(%oFk@Aqu^-hMcd"nLKpH*G".K/cbPrN:%q69(OA[Aca$1UqP85U6gN941#NC"K5ds%pR<8]XEe?1:7Z.sn4-[5q_V&412cRYd:(
%!a_/]eCifu9Jkl07H5LYGJ'0>\6X-j]ui$/P*lcZ)#2:4F@cb!?+:oBL`BpJ3,q<1/0(UU!o5mVBG$QL+s:$,Zt3Su5JW(j.bN!j
%NCmMT'GKgo,5.CsE!'\VXjamLrlh8aU7+P117*bmiE'6L#iNu91IK!kJ>R[N.Sp0$.O[*I\j\9VL?[uj%9\6b6jQn9!;WSKF;s:1
%90p-tlmIG,]*X/&eDp5&#VIoX8MRGOhb#Hp]YM5KkF.KOD&1:$UTjH$4;.H752624p;?_bFTcL1'>\.`2oFB7omf2Mqro4:?'9*k
%0IRgT4puAfieU(k#mn"i.aR*+LT97VV-=@PC:0l9RN\q$'o<EkBI+]&Kn`-qm=(D4C@>g=">nZf+e*ln_P4\)EG]!`ij5U*Kn2lY
%2@M(OR#Qfi+ljFt`%%`JIE"0h>$,Z!60fTU9HZ@%Z49MK4*Gk7m3`U=cqp4jaQuk-kqAR(b0eQ@7pkBd\<M_?,RG"1EJ>_#_RP&G
%:Oj&bkCJ<V(057XHQmqJ'0;q:Z4.1.<)Y2a5RD-mOP.6h+jeO.2*);7QB>61RH+F#aqQ!_j<>#=7CHQGPGBQNchZo7V`X]K,kth/
%f7ab9Z);on7><ojF,FDoleGr:H!_OElj[3.q'kDZ1A_P**EQd]+Q]8R6WAY2h'lG-ZL=&kgcR8i<Q'(BRN8X3"KY)`d+*?]/-nk[
%8ZhgLAnbO/njttG3-;=uZ4U1G/G0T!&p^Ss[%\o>$hBT:?JES)?K2fELK#C4%o87Z)`BGOK[XWK3(LhXYNVU[f]_Jk?<rX^Rkk+=
%nhQ,*EA_"mXQ#NP@j<FDDdmGXNMmNn;3e8LLJe2hLTNlBj+W4-gMMl%54r,O_IFYc$*%gQ&j]eKbal5_YR8LFFL=;Oo+@P6#%-$c
%FS6Vta!t)(V\eGJ"Q?N%;T;%RpYUD.]4q^b<Yjf..VCaKd[\%OG"+WS\+p-r19OrQG-hOANlnUYa$dd7rX)NOc3+uheKrur`X=53
%K(^rT1Us!k"&c9feXEfqO9a2U+b%rgZ<4GYM[AF#0A@`Ea*n`N%I0]fRC;V2<#6_)H?bpE]hK&YlSf:*QArc#r4`)X"Dru,!H+K*
%!kfB>EAYPJOSY2ekhYpibU;%q*9qH?K]_kg/i@hX[eI<`83mhG[k&t#\V.&\%]ca\X)*g8DB!5^o@%:_$3Etnr@I"p7s%Wa`Yo.1
%pH'3_*o>%ITHcieM#b1o"C<36\t$?:(oG>Ed1:BT.Z0Icb**#lSfN/<0nib<3X`fuLpY;YLAe1S.oZab99"?OEBq+?:m^Y=BQ5oG
%i`3e4\V+EJL!X53A$1SiJA=p;0ZA$$_J$s19CITjc5FJu(l]MN)BFcF>7GZtjJG$E,Z>OB5-+j>(cf&=7V+'98q>:@&+L!RE&h(G
%:'V-pCVFCC/9hh]2OY415g7=pCfb.F:+.c-+_=Z*J8q+l*")$*!!G?2Lj;ZKmJnO#gCOa0N__k=g)`VhPRt![okPF.hP*O8LhIWl
%F%$6B>GQT(7K<[>H4Y4?X/r"tX16/kAl0jEW#$EI'P/Fq&8MXU!k5A6&&Y>A\'&4nbPQU.\'9c1M1nYMRYg_#Jn153,N1VJcRd8s
%3U6.=K7#q\EV.<J%74O@Fl]bH_uB/TR)IG(*_s!t>g(I`(@5Ut76kk[&L2u.7^_:e%@,$($KrP9"!5:oHGS42,*pKmZ[u:L`HmNp
%.0pmZnJ","P_E.A>B(As`)-O>g\4m5jn9)oKJm<b+?u?i!8>U(=)U`'+uHU7D^IGl*[$<g>MgcX3pq%[*+0PF*;qVrUCsg%,33a[
%>9C:!P?ujV=/0[,W)K*>X2W2[J0W_D;dinZ9(Wh%BPLL^Xp8/uEJJLJe?cjWB'N-6K.ph0Q^^bCj.tdV[RJ]8XsDbuWE7CTgF+4u
%P-*Zp@.Q.f(of5ZML;_Ua[.<:Ib_(aUR->bB]k2&60LC#-FXpf5c6aR<k(o9F_0sf>\p*?:uAn:Ye@$q&dSa3(6=8pV4FrVgKE!a
%*=T^!3-@e//`(?nR!\uPg5opOS3<r\>QrBQ#'R_bScAhTBot=;^*#jD?g9+J`mMqfpD.%(\]X&I7oPk<6mHAuSt0.tT5;JOfiYZ1
%r'HA`UKO)tPo"V[](G_^KW2d#[Z[qXJ1(b`^R.EN0TQLXYdJ>$>])8)$VHT/Ld3AWefSZt@rX<Fcl<=#S#I>^af;FO)3Nik4U:bF
%"U>b#'&iMMLM-jokGN?)"F+,4l.s7e'$dcN)>K6iF@LK,a]p&A,hT$ng7euW>Z,S'!Edm6\$5l^&f@h`ocF(DZ&n$&7+c.GTfQFl
%(_+U4DX$Hd6l]%t4,bQi;m3L@mFSArh>J[W(!XWkm1K&J>p2m#mlMXd5E_H=Tr:L6Ue8/`.""I-BD%N>lI&Ls&W9,s!iJ`Xk>e6,
%DKRHQW#bQm64/3C$V.gGfRPMQ,QQJth4ip_fgmD?d=:ncGfWZD_UD1tWQM#V4#"IH/?22Lg`cP5mo"R!BXMO^-pGXC1<91?_9>bR
%F?!8F4j7(En.::Sc0_4aecad?QD^m<9]35!AgfuFVEc/\Wts.3W9_o-7TiUqH%9GH)tkF:;.@jqVqiN?l<'@9kq<m-M1Tnn%SP@"
%)7OBVOZUM=<u$&LDVQPBB)Me1&LYs($L:789a!_>@pJL1<4n-FLc-U&%s(-=UEZ>Soi!=AAW:a!TQ.#C9u'A$kH&^AZ:,oWXoZ4R
%JY891Anfka$LCNOk:S*>Ep+@kOJi<pdRdFgD21heSMj@LXOROFM^ptX`J.SdL6H,:.6tG8TYrNh\%2kR&qE-F$TA^c=09+(C9p-2
%$cc-%J1?G=m2<0;Y''\B=*Isp[I@d;\a>R!]+iFfHsJU*1feP"lR$8/m#Dh"Y))4=^XC%K79)lEQuR#H'EPM<dc#=Q"R?5YM9buF
%S!8d3^q.M)3lhR<SZ38%-Y#9#JGZFj4s5=oAR3u(JTW?W?7<hUb3JF-e#+:D&$ue[EG".TZTgcXmKf%L9Xdmo!8L92@4`:tac+:n
%+OSqH:I%ep,hoD#]9EGn3F@g+ETm.@O]0t<mi/dF%anO1(R)KjFfY(Hbe`^'Cjt=d0k!rlK"$OX)Y@l\5GaDS)&lE1i&Dr9RnU>1
%E0X[Y@St@*P]#T><e1gK,O\JO7^s)Y9T(1OZ<mW6)R?S4Y*beo4rdWlr6A8:E"7&481jlc#b8tm6l,P5QnXPq7*T3(o,=cmFr2)`
%N?P&7f4F1ZjQhXRjs=0W,U5eh>d^kqFMRKW*7S@()4C2_rj>`S;A&$6M^nXbE8KLln6>IG(dNHkq9E3E;(VjW9kt&@748dYQoD9f
%%gU$&(Mf(:U1rc*5N*"s&@77]U9LKZ_[rANU),QOIW6Yi9!!%1FrQ!onrWqGHsb55VjUKmXPEJ-FCDUkG1OGM(AYiVX,j9"-!V9A
%c6LZEDuLRG;[hKW9&Qd9ZdGsS9TVAe]:Zt*8FE@kp&J[6<Vqof^udGH/aD-H<Sf47ZAGq%2q5Da8CL.H*'bIUF=X87U0Yo.!\Vl^
%;hJ=f#&Y2ZB/VQ?H<m>"L_\U!e=*iU`+4kF()L'+V45IG*Tti'>KWbrWi92J'NUE0HHf4d#'oU[KdclE#-JjEJS-bH/dKKq!qO9_
%=NKZOfogQ4\I%rjolI+@Or7KF97*_)J.Wpu&`j&>JPT#nRn\>T;efmm6pM$[(jo@]]XirU.IhKGA%P=K39.\SbQ/TlSNatNL`g]D
%#'^<fR_Ucl2H@.PW,=R+Ae<GK&VTT0K[Ho<4<rO7ah<*LJdlZ]Q.dI5"1OY==UM77\`K510d(T?f#")0`Y`s!e3[YK.&7"_pi0'U
%U3DO?3[5=\+ahknB\T<s,n>g7DK6ZfV_+Xe'5i4t96tabQJU\e]L6gMI`"?^g&!"s?fIrO&$ehc&Ak+Z<b$ddnd`tSF$E+#Qfuqg
%*(,=1PAWDaiXEH$#05d%cNbt,GH+jn7JWr&`qMn-ibZLdg5$(deTMOi:cDX7FK=StM:\&2as)b1Ea2mOg=Cp]C$)'H6HP510BTD)
%JL[flX(LC@Qp3+uW@1.&JG\g!Z#$i&=9m*dJSFXNpIgb#Z(mOWn5nD,`*=cqOG8`QI1&<:^ihg0N#QJ9`!U<GiX[1Sk#`m6>$7t]
%U@mjl<7k/VX+7Zb[6%@;[B'QH!YV+Fi?7`(?)jVqb6a0?)/,p&IPNPjDNa6Z_3Ps83"sO\%Y(g-/K0$ide,WW<.cZ6mopdKdLKH&
%\C8HM,0EA.\'@FEkFq:5*B=\,;&r'1.6=UD@,hc(<:b"mpI<%$Qbn<c0tL.U^K1q":Q-I%h5<9iJ;m2W.9!H-cmVmE%1(.:/<f%.
%LInIKqZJr0W]XCJ!nY7<<qR5G`0&5S=bnOcHs9K[*@&3&!O4eT/2RtSDfumfaJ</AQuNcd5_^O!bY?ujb49^UcgMVeH*FXJ8DM&p
%Xdu?pjiO\&"Dg?j>reTF'tfF-1X^%$-7`#o4I<<?m,0jn^S_iud"!l`%g^.[IoW7&(cfIo9$IVn_("]/lghnhjWgG*O30s<,@jZg
%o+UH9mS6Tb38NMdTpW@5c!qYNb-ae__eSQsRY+pA]9;-m'sTV_T)_@)E00dHs.*Cll)LUT:+kfHloZShJ4Z4+0Plg`gC\j:\m9IB
%cRJY:3SrM('Mf.Y/Oc4a_T3!r1+LYWB]#gJKu+(C=!Lcb8q7pt=S1K,@o*bp4%mSf&IkCh(O[]P%cc.Fit&4hF82JbA&+]/@W=o>
%JX%kIC6or/Ar8;ePh2-SKtH(`=FrT0_(CNm9G6gu-&P=&PP.?D/=9#XJXnT:2irgeX;K$E,%lN-&F&]*4!Q9e7d6e3'.NW;^jJp(
%%f9ZE5M;K7#?.rUTa413,c0%HLh(AN3'^5,^P#:sl/B%n]/O5+?S74[alWQC?i?mpIIm'frUuTXrHI.fAc9CBQXBaO\,Z6T^\RPP
%s5g`Ir671XIIl:PJ+K.JIdu#Ss6W%"p$Js_l$'lKZM89'q*/;HhgY7ahu,#AnBX&kk3%3fkPriicI\NeP4;08+$TYho(k$1=j$45
%KDPET$J,.Ps.[4+MiKm_e%bYgIsa0Rl;o),n^kPOV]R(PqJV;ikI&BL`3fVf'eTpf].ar6Mm,%kB9_EogJR2YidKT^Gm<P1:oas`
%`'=[,%bIFOm>=_o_,PQ\8]A5\V)7lU*FF1%n=gSib#/'u16oB5Q;EA;glLFF^Ef6>2%s!YiXAi!Pf00+L*\pb\p'ENW>B=N8jW7*
%KRebi%#'@Qf%K]l\ncHaM4m!0IO6k\MbQ\u#:&466s2Oe3a-VX5ROWI'7e#fR%BDQ5>I.3':0&b"caET@o/SUU>'V@&Hpjeb>`'R
%,Y<W-[gg+m+ioJAP&4GN937U9JI=/H+>t`q'JXC>%Cuk4iYBVYr@Cl^\;qb0FEq3).gb=1W:!Kb&@XJ6$/#S8cX9LFaGqF`1,%qI
%ggIJjMZ@[?KI%Egf,<uf:J7gM5Wr.gO_9#bagVTMG-K`=<:.'rqhlFQ]F.ZQ*[(iGY8ER.@93"\J?WrH`fYGUprQMhnpQrB&RGU&
%&RU=KNN&;5S0G$PDJ'D%ZSX=i-IT($2L!IIK`WUhYhP1[1_>RNZ!&t3&7+=:A\)@5QaW-`e9M;E+s^WJFZVc'4E>,W7rt.A!@[[_
%_6+:TG#jPU4ZDl%hn=KhWBPMJiY-"PE=PBrc&#$209ELf(VhLiMD2gQ:6S*pdp/%lET#Mu3:2nJa\/U.SS:P9DQ0L#H\"95bDrS4
%##sO+W6Q-iWG173dT;eX87=(Qd`[@06f0;FF>dhdXJ3WX$CW3&YS;9AHRER*NH^De+jPp!TZEW8N5C*<<o5r:.`mfMbB-GUK5bT>
%7ME95-L?s113*Q*:<IX^l&`d)i&\N\Utqb]'WI\,!d])HeI&3):]<]7+[]k2]?FIe+U#4>2mm1VU)fgroU/MgNC9Lo-9ZlJbk?s8
%(M/Cl!,T!!OK,TFl/r]K5$7(F%PY0LRusNM7*(X51)l=GU_.b]2SSaqG]tk?Ao>F1\,b3@S(t!E,V@f!+Z\JamhRn^')YDr-Ft?'
%BA14J"&5leqE=[7Q[8T7<Y32d.Au'/S;B.5En?=A$$7b:i*Zb^lf'cSUn,oN5lO78ZKf+QST!?_,8_I)@Mm)>al8MGE%M03\P*Xe
%9^@9-Ch_SsTHhq`Yd0*B6-bB,[)I(WYY.Fl]4?Zjnu9=noq.RH14CcqFk(sVk5S4u52WTO6/=6EF/J.6qC^iCDT`@'LTm^k#.^Cj
%_B^Q/Q91+)X5J1cEX.%ABB]t\l*=Pq-^#nmpN](Hm,EW*N,OIT"_<4YRH)ufQjfs<#\ZK@V`_k)#@8efK;hb8@R3O:02krOA]57Y
%W%I(94/9e$2:)^.nZL0Tpo#HK;&YH63n,pE`Ob"g3?ha7KpnD\WCm[%@8A"]?(_J8q\$;-G"P2]Q#rL+=Sc8K5)>u"/<'h'6Z0_W
%[r647\J9ttN't(D''F(L+nW[ln#BRp%%VN<,`#>Sg3ddBAaCFCX4u1*6Wp5*jTQg.6%.3*"[+GU]`i)gA@@"o%[db(P$\5JIYib;
%L^A6H>>]d>.BO=:KN_$KRnTQe=IeD!^nAeRB\ZYA/=G7P0BHr0:%gMHN\=FZ`Ef$eG[]],fo@OWVH#njFZJL+_931'%cm_S$c'\(
%VO/p/e>gKZ<ApG%/jl45D'Nl,Tqqf.%`Se."52;+=Y;n$r2mAZQ"jRn!0^;q`rhid8:q2h0I8-Q=2Ikt7pURmWk:g4+KZad=8mrZ
%qAM1fA5KKn=IMnBT6:;2D6jKg/e-K_:mE48UM`7dp8JUaptSOAMla95OoTtd0VX5$PQ?Sl&+.@l05&*t9-TG$eU^2$=u>V*q9D-8
%Q9,?V=S:)DhJ!-=*T!\C[=Mk,_M,80G;5ErD.QkRk$90H;*]@DMU0+_Sl@udiRY;e'$(QeC?A&YoK\GFH?j>-_T!52/RYu2P5C/(
%*4;IaFjN8]J3bt]#jej\dh&O,'11.96J-Fd<'\2N7jp3haS1P6<-Z*!fDTZ<)8_:OQ8f[_)0VE)WEVdp%i7oV/6WRddP#h=Ao3)g
%5es%O(a5n,3J*;+.oo*$daal4_O8W%qeCZ?F_FX#85`C'"<]fg5JV,RD$."d$iC+f9dEoUhUD_%9'Q``@[Oi<.7Xp>@JFPt]jS.[
%c4(=(a)-Dn)<2o?UhrRYcV*"O&cm^gZZ'a2g1lhO/tWjm91Q\HkShKVd7EhLo=&Q$CkY#k7\lI+NC;\ijia`#/DiURMGWg?$7s"\
%D0C#S/YlH?;Y#BWFmIY1GO*0E1N)Xc<MRKuBPj'c;Gc1\PoY4%)GRQB$ndhN&ZGO:FJ7Ea!>Q=l4&-;S44L2KE2ZVI_E_f2$7O][
%B;G:G#EcuCo?cs;\4DmX)[[%6Zt+!j3()XWEqmoT^bcW*,fW6\_rutKiKWk/"#![iR0-\[4EIPcTcB_/Rk!@LIRub)DC[dO3f7js
%[a`Z?kafBpp3Z$,P^G@Id0\-h\i/AVNRS#80BKbYqnqi,`V[%;5%g('$*+G,BdN7l!r:iX.Y'rrY)_&^i(LM6C&RNuWb-pXZI8:s
%h,<$<eNg5G^"A&pPju)-b:f+^aAPnPMObj>S$]3>E!`OA2C,-t22]J'EE`^@%T%6`Tn\KfDB@-^6dRI0Ld`*pM3E1,hHG3TaIlC1
%\fkP"GJru)N;Bds3m<6rmW;6%Y7F"o>0XQ+VHKV9dN"?QVjNd=%GPDN$Nd4b(tWL,O^^,q8&299N6<*Y*&mCA%(NknN;:".W%bCK
%T+IHXmlds[*`/ee9F?!)-Z^u/d\d@Wl=GDLJFd=HUH6*Scg>\mNp\2oiQ0j@$JiOQee*qfQJmlC*mH72gp(0j&GbpmM)ATk5$:r_
%TeZ)Z/4k;rb"Vmn1X3rG65%[q.QuX>bGR"/K!0[hJUP(Vk.aSi!mi*nOr`g_.\@>CmNbO4ng%j]hNG>h>4r6.aMh,h(hIMRT#=Qg
%nll,_cMWfSV+Oi\8Tob6%&36C/s.Xaim\K/''H@HfqkcZK.X-#&$\a?L2`3om]K-C%3*#B1jMSsYZR<RE4>umKXEs*gEn/6+&&NR
%F%\$@6M<Ik&SHkK"Z4[k?nbJI3<0.+/!gF?,;0j=ZfVm8AfGp(87S--12I,KQTW>$n"L,J`/^50<<c@$.g0o97iAY^*IsO@E#f,a
%Ht[#`@P,%>@X1MIU:=;ThRdbp2B#C-^IfT`0d^e%eHWWS5)tUp*o//*6At+ti;8nZ+bPRfBA((/CNZ$3kq879L:$TdapA)eaPfN?
%##B#3;]4@6>6J+V&U6:dq,SpVetL9d$/!'s:`8t5d3'=M:bFEU?;7h,l7_"_8kcP&L-]Ah&k'5h6h/gr(WT48ouniX%"JJ79;$H/
%X+Y;fh`$'eQeW'kLZhTQSF4:*iSG"7ga&XYM^aO=:PlLe]V&^"PTa[M#4i@&bk%.2ODn%@k3<5-%cMDjnZk8Cfcm)H7,%t,Ef9]4
%>lbRR.Z$Ju\)4uW])Gu_WP'u_WMfuR6bQ3%]/V6lr+Wj@2W3WWNNpU>X]8lY[,,4s%OH?7j8t*u9X>e6#rk_+\,"P6c@O?D(n:.&
%9Gj%CX3`DqG>:/9P&E]?A0BrKa@h(g:/8XMc@-P6`@E2[\3,mj%?*5$BrZ5J7\`6bd?&8&A^8m0gq;m212@)MYM#bf?(d(<WVT-l
%j`thGgism5b)F\(0#)llO!LX.W.]t3IS=Te(%tq'@>\=(mVp@`WSWJ88N!@,ns8BU=SZhhP-2j7\]ri_76s!P]?0,H8:-O*R)8L2
%W/^!\LAfc*MPboqSO+l?O[s/TZg/R>3O?dc#%isAVT&:)LIYSt3'?Q;)(-n&^,JL2/5G,kW25IE0"&^(<07@nd?1TYX>ka@Xo1).
%MHQ9-\bkPh[u6QZj.:F:kCUG42*JX*)EK)bf@2R:_pW6(ij#H["@D:VL<DW]_C=gO&,!q_:2;DSH3'(#!Ypf0BSHt8'<<&l2`:`]
%"XK]aOZAV9)($InC=`(FX?F26:FYS\RTJuI3AskLZLaP8qoI%/I3J8Rc75ZHNl.c<:c:2)GmVD#6+qoSdn)a''Akt7edQHVVTEu"
%au?0&RLBf'FlIWIHU'7r1/6A+`KtuTQeTI\BS\:aR;JgVDn5g#KpY;u#(ER:.^/0LQ[pA-P%dpHEALLp-S)uL3tR#mQN^&?hlo7u
%8\bmT&9p(uoNX2jA6T2:b&d`B_N5W9qJ=cdOquXB8fk5INV)U4b?cg:OF*K\Ad+W6M`FX81o*-A$Lt<2do-T[rf=X+*#6r#&t6]h
%d1dW2oP@8<ITSOiJW/)Y_t4oM/^@s55>A[K_\RnfUgohbNp(6Jk;3jC*aq!i4e?5g?3s[CiRom7hT'/sf(-mZ!,(&ED+l[gC=jM\
%-]@mbJr([gV68d4#q)$Xb8:0-gAbma0@F,Q>JkH:4u[P=%S=94nTPNe%&+M6+hna:!gA=g^B!Q^-lbL*jO)GdWGmToNhj3&FqoIP
%<28U7A3>0\im%nESAf,U98D$jd(^p8f]/^-:t/Wc?_ig9!Fs\'Eh[M/0g;N3njk/e@h^H=EG[#-jGNB"gP_iRW]#ZY?3mIuhV*d^
%f6a4[5ZI*Dl$IF4-i#)^i_Cdl@pB:3\ar'eW9jr-U&Jk4nQI/AN]*<69"95%fU<T_f0h<k96Tdj5dS_CZn;]?25W3G8-mj#n:%o4
%.&21(d%O$:<5_6FbH]Q"ig+6'%0u7XprH(IG)uaA*cH`!J5EZ9gKf'%&pUFGNWIhN"_DghIh6k<aJYGr@``e9J/UJ7bY=%-8!/<A
%&n.LI@k+WJZ_O49*BGRm)RuJagEDbF6[0m)W^WDRBiGkh956kJ7JaBJ\%_t1EWJ';eTsR<TY,mt?s&tnqn0^MWik!!_X\C=$PDsJ
%GiFX?h3*cZ1Q<:sT=.>b<#X1XGq"l[ot'[HboQ2mVjM+<=BuXo5Z+q26`8BK&5,?%Mj.&aZ1CA,^`Y.K*C=\0Vq>boL`_9FP.?ks
%Ln:FpM?gh$cW*[hZ_p6`e+L6?A#bs:"uDPNG:peRXG\0e"Ld.oc.il]NANuZ')!_4##\+L.@]+_Nr\`j<;L^)Bpb55=ugV1p3eGr
%>[qO>o:&9i=Y:[VFTTS?aZu\00gn;-A.f>-ALd,!#b?[>`pIWGg0q_RG&iZA'.`?C*srdd.=%it13J`ph*Mu4`oJ]j\/:Jf=@gYt
%U#<Y:.$9n1;]1kCkm-N1>;/=IpkS3ERn:eL_5YBRjP:5YeC=7eJl^]80n=l_kCo8r`;>(`^IIE5qKcUC]66Pc]0GO&[N;iGG*C2?
%T?'/=!*q.Pf,)HHdY;@>&n-Z.J9iW0i*d9/fXj(r.uX%+]D2^iAUn_B1EaGp^b)^#2a0mP%MAiB6qPCJM5CJ]JMsjV;$gnj&K.)f
%1kUV7l/17e/3tHh@;h4A=*kd3"8P'][CMI6.2!kn8f0:@lW2>00r]+2RG)OihAu*]B<O3I&;j_]Ie^E87F:sK6@i]Th0YhT/[WXM
%(^EC/C=l3BB6QGK:)2?%-?g_.mK=sCg#d6oD*+33%D<iDs6G`/4#0P68[3A2KWaDkn_n9PDkhN8X7e!lZe>18\P"NjTXi!l&6)i@
%chh]'A8j4(iH.(,L./+ae@W&Q0S8Ed\#478XnaXQ7W(l!\G4do)fA/(nE--)!Vg-a^;K=Yb::GFXN>SVNS1qnS+UP,ou_luCG(ED
%N=%_-HQ/cZ!*5TDbuS`,)":aY+T9N_*j1\TWN5&_2s"NK&LJ5#6#q$$O3dqsM@rjck?1SMIiJKBXq4UHrCttX0pTdAWauVld$aM9
%i/XqoKH'fc*MldPRDs1C!2,@l"-JbXmV.*gYY+DiiVZe+;Ot_\i60p#TWUU8=9t)t4,AUJ#Yluk/5=@&7#Uf;,8k+;<k_GoGOJWQ
%nVC"m0c:6?;u<0*)WA=+aN]?iNaN&0M<_Ugncf!i^0TE"@gX&?JA/"(<a5dKc`%Q_oRJ?%0`gs(Of)OWY!b@7:#aR^Gs,>F3h0d`
%\LhEV7l[V+^eB?'D.WLR/MQ!KZFq[8/#L?m78W.Z&8"R$k3URS;(p,fab*b-6BOG620F!%OCgQn4oVsP!.V=;gHQp31BaDk"lT$r
%DiLXCO%uud5WaYD,@Fc.rY08IGSID>,D-POhaWX2'k&m/-P+Q3!Sp7s]<[-Mc#1m3*KC],@.!1d$X(G5+HgVNE"F$g2*GV#<dP=r
%jW<FQ?f-UjA5K'aNW<9O7+)OR!cuA!dLbF/q%3)(I/dMp(jo<EYp@B3&L;bkjSksfp+JDTNn8Us*D9\_MKa*B??E9Y>JQmS";c\V
%U9nRBC<N87$"uZOe=:FmEVmHd99+)8L]l]C&Iu;S2]ZeXW1s/M!\#ge87VlP/<L:1JgjICDH%!H$pijDO&Y=`3=Q:hMR/mACl$"d
%MG-s*DmD;&L;G4`:"'W(N)gPbU@P's4h%G1V[BGpkphF=K\n#1mc#%8-Qjqc%f1>@91!o#S&Jf!,u`L#[C."TN)gr9@93V<i=2c%
%,TnEc7nJ9a"kXj&?(c4\h`ZFZg:e->EP/?Saf1?\m4?U'VK\hoqT.'1%\5GF,*mL96qiaQ-TD-Ai_PQ9J>.\4<DcUJJSl3+ZS+Xd
%B;="Aj^k=kXu`:@!=i]-=ig]QrbJT"GYt4DiYt:UiC4X:<Sd!r2AG*Q0j\X5cZEb[F?Nn"7MmJ[U?kCWf\kjb)AHP&\G`b]Q9/H\
%,+NZSo"]`>j@>t<M#)h4?kT9^T]DD,A,dBS7@W%QBsdXm+[0TB.Dc&fF65P`E-L*k:O)2a4K(@1]M1Z@+:\^3/R1-*L7DSg][rp!
%F1<Fp3sQKD8sfnWZQpjEqC%7%+^$uk^lEk_#SALCSl6cG)/E?Zl@X2PCe)umU`6i'7OZrp2,`=_@iB)ELF-D3_=gR.:X3g+F@)i%
%/.EbNohm0CG&_8:N+bL,WYWK^R?+tP?uS'h7oD2!+:fQ`e;Y*)aBBj3!"7?imSW`EKJ,-&XMS/[,6TnRYp\CmE:b)e-d5)J_JPAb
%(K[8uaT=/mnWBJamon'`hZs><aJbLGqZ\KEZA6'&Jr7h-X$m[u3/PHn][;/%kg)<r7[,0Mg=Yi<36fpn*0VMa<EKWAYE3W8=qa$!
%$8A+cdr1LN#Q)AA?h)"<]5taF?(gN5XbZOAIb;J2O]8=1*e_\&K3P>1<i-fsQmI9YA/>%bKEeBLd+Qgf<MOmnZ;A$*/ECW[_hMYP
%Rf3**eL=]N`T9D@%i(m4gEYScblBct)a7DK97oNLadp5bKm6_.Si.piR.-+&a\WuH+l5LYm;+6-deu4tG!,-0-2MVdO:fAc#'!-E
%OWrk!Bt#02>on^9F2ErD,JN0f#bpbJ-SImufMd*@DVC*ORLTlU10k91faQHMKAuf(nB1$?%2bY!l(BD-7&4<U"7ac45K^sKq2Pk'
%V%a=JHB.Wsnc!KX>D`":UMXu'q.U3Agc%;f.H]V0]cJ&feL=DKMn&V,2rK^2)$5q[X?]I'W0q<q..J:DA7TY++T0l[/9K:.[fmI5
%PZ@q,Is*bRLE;94CK0B[ca/e<#cQnQhd6HO_&p&04$'*#O@+h=X1`.%G(@FEIBAP`0J!l1&X@?cUjmGb`%^g6>tE5@CMJ_U^hQra
%A#mf7Y<":Rn]M>=e:.*lgm2t"5)M24TO#_>PPaTB4-#ln,k9<1\RY\_RCQG^U6qUGs!k+.ZA5&`BM^bDc$Agdoo^;V`gn2K*`9dK
%ZGELn-5K&iU!N/;E?>Mng[,0gY&ma=];99VY1i$[<0-SsR#HJDQ>Gcr^:&TmC>(Fm-bmJ'Ib[.fpF=tc?49$D_+()#:btrm?q+N)
%RbF8T`uL)^\riScO,.6P/g\,`Wb_"L:q#5$1`dcZRK3TUX[T*/&9\f+M8@i`5VO$.f7$i!B\2iNPa_m<+gs;>Dh(##.cNf#JKdcH
%gA%dC"22rV8'?CN+M=/s*encD3^$$G9:%tJ8*Gr3GHsW8+XZ2cb.ClM6f"XQ5!gd$!lAkE31LJ4_Tn7P,u/P]4#*I"5@nRF?mhlI
%$7Z%:d*3C1@m1;QY[1!Ur!ZIg\SHO0S6tGBmt33an8jdJCAV#tOA9aZDe]Y_BS-4Ud9=Lu225r9Zb@6;,&Yg8(itVm-``$t:bSJf
%WmQ`<<g,XsEQaI%?gp,8Qb#aESd_V(H>pKBfUcWb&^JUK9C>fBT8\,49]BLS@t:(p-BF[i7gZ;O;=i_WBaA2Kg7*;:1lLZE>UGIs
%R=s\'49;I]J'e%#NlRb"<CPp,]&WP@@$!N2F#^52%+e`<`;&<&lpGn3>s&-#dZUGgo^idMLr$=OXh>0&07M_WFMC(#WeJL*#tXef
%Zi5c@%Vg3@O2;3QAnBsd*im\\7V\0,l24,S7N&Z$T>YKo5_&M+J'gLC6/$hT43$U$(f2FV)X?A_D'T"T2UWP<:SIj=.iVXj*Y_lC
%J@QQ/!VgN3LkA^R2"V$/3s8=%Uj:p_m/c.07E`:SWd<@X=s=DfY`!?'A[nbt"RMaXq[EXL2l6ft<ECGu/-RL9<J*k)%tsn4J=$4s
%Da6l_!j%H+[K0CrMT-bl4Nt]CU3%H<\U;f]4/q0_.oMjW0P_Vj!:)8cCFlYj6#0QueNO\q:rCh?XAAXQS9n=3,%)etdEU<WG>2jR
%D]Ln5"HYsk'`@VDQ(MNflgFGWT$<P2h8Pb'PcJ0$S:<bn=)gF)k-p)?WicpFNfq81G-?+_"HNNZ(kGcjeJEH,b"]f>&usn*K^+R"
%+iu2uKU99;G@>p)H7I<8Q/TipnVAlU4@Jl[9'tT55!G!#?(PF@oYo._s0e88>K1?;QX8Ve?kq*ai,(t='kqfInn'2X2O'THjRZn6
%`);KVqQ?pLi(bSpWj]9\<2`$.Ck%d,,EK5RX?UlRGuesC*[iXALRg49N'tRWHdYA(EIQOOUY:F#DYjO!;`6DH\^!S9,Uh&4EF=)W
%nX[+E-%]R?mHTJW>6VmBO3>!RHVDDI5/[`p>n6No/clB^0VYX6HAJCi']tQ#"0eZ[npDNS1PaCVlJPT8p6/*k]`h6GS[B?F3S(\P
%Wnfg;6tm77]B\cKRF*5!iYMtQ#:PX:S7F<';_KFe/WS0:EX1>8OTr+r(/c]b!-;oJ<m%Ul=Z$1KeRC12Ke$er$BD3S9`bOUr3TYP
%1\Hp$?1-+"I%!]6=mtLcbE*rp;rg;KY\n-e!'!&fQ-_]b-kr%0:#%GkOg@LXBc=8#nAj%@K3[cG&-C^aH,C7qE'Jm_@!"nraI7Bu
%,p@6CAr414M+L0dcC."0BHb_Qcet`#-qNeVX4H&=Vm"#9#W.UCIOJ!S@",0\iWm.cp>#I;N8.AE!DPuK&MNf<idMaDL/dU+Xq]Qi
%C8f`<`)FNm0j2E,=d#s/pgXO>pormr,[KgZp5[M/alW%r/*_tWW?Nd-g:4rZbEj3GR(1>G9:.DJrW-/6'0sWN/[<$<QI'=BQD!.;
%j,jE?]bs67_j*oVJts;>%;`6RE#0He2`c,W,L!r`H&q]5[7Z\:?B.-10H:(,Ur_d=fr=j>k9d6.&Kr.R5QNR3oV^N=%^,0,*@5c$
%rLfCdD6afLjZ;NL2>,i[GFX8tjab%b.;sFVc4XOmZ4]8JJZ<I/Pkj.@BqW5^^e*LJ@0Y$M&ps`@5lW<u@,%OdBI4;oU;V]k9hen>
%'Gae"iYmMbOO%WP=-AK0VX\]&.N1C''dh,hZ9:ZR,>uX+*$EK"=^eTZ9;+ZiC&dZ?7K2BM!rjHG'<'3Q;TX>kReXhRGVIji+W3pN
%'QcNLISc!mK'ST$95njSL%JJcdYk)"*oK>HbUt*]M+@a:QBC%7D\#giDD,/`=C&J,U$Nt6W;>"$p!A4<:V.B/2U9kZ+O&L,9])m_
%;)f=crUY'&<"Z'fVL#%$n=Lq#R)FkhCc,'oQu@P7.:$4:>'*1:[NOoh3]sRQ!T$`T4ID7aQg5AEPrFY#2jk6,o!s<@?#F%/<(ok]
%)thM$gBI*3WmOPVs1eW[/uBm,e[prF^PrEgLgj+*KNVd0eC\Wp"M'oJ-pJCED9.XgqGk53*j"XoN;Qqf1>cj!`3%%N(a%c+h:@f;
%kDYaH'5oR.H-(DRFIsT*Wf*am1R!HpIgLNujsI2JI]g67#2@g,]bk/ZG6E,-?lsuER]^=A0E[\<hPRBh?]VL=jU.G;)(np[Gf<XP
%"3C:Wce/nqj!BOjc6M]6aNVL/GgiT[),TqDh7]7-1q8_jh7^<=LUX$_Uk?<l6oTmp$4H:lZMN%$p)0jW_Ad,k.30BJ=$H.s[16S]
%ld>HXlelh&`[Tu_?J4jd%aRI53[QiN]m;KtHE!%#'e6J*f[&V"V#HIn^k^H^Lrs=t(U^,5Z3qJ,Q53hYN%=1a>./ni2tK,q];h07
%-)i?l=.ojf#U]@%Zb?mZo%P[@'M2sh*p0Q\`[>AH_$$\#CJ1._LgATrgkQhA'W?2/6$Pa5s25p"J2"+(cj1XR9",1ac:A`OnjK/m
%M02eC"][]cCP\])Kb1\PE%P.6)gW)GH>S6+WJp%WjPSV%$:VJ=au@I_K8O*Bba/&n0L'cD0S:693.NjgfYKqNR)^EDnEjlk$Z4Q<
%;$*/Ci&FE=*11o!Tlskmf`XjTV%(u9[]a=TKoRERa=^P-W!Th02rI:8omn]t-8-WlWp1$f+BgP",0R_I<8@c`Gi#!B5:&CU;YuA8
%EJEAoi..)f#nbFcIC[+\"]+fV]1MT[3!n5VCK:hn[>C9dn5%])74DM]?<'jP"#qJdRV#^H@Lnu;?e<XiEC4JI#Eqf%E<M?=-g]9o
%P(nJEQYrEcOS5/bC8b>27>1V4Y4Y[7BPjWk!YHiuQ,>Wuc\V?ZZWJP\<.`CV:XW2+ITl)?7alGmEuu>>AW$t%U>&`_UNd0d'V'um
%h&^9"3@D\?O1(f+N00Q#,&NOfC(W/k.FS'9ql_@l(-f_;DQ\MrWcmh]M!$03GJ&THgr`Ro4MjUdrQd$#<e$Vj#F**?L8<3mE]W*f
%V/@YJPSX=mpK_G9duqQG-bNZS[)MH`B=A@#BbG-U_bt;P]h];JZ8%`3=>rPQ1,Y;NE[MJ9A`c,_QDq-Qq4ZTL%<`67%`t?9[S^Lo
%X/]lX9rO&GW&E_Qb]N]t#:K8$JB4-1ZZupDA74+^Ijadup;`^^b+`F@U.VbI0[j)+P3O55aW)\:UZTE']7ju6"uMct^UVjP2%V:J
%2T`m,]:\lXlmA,3>6D;Zr31i4p?FOFSlk9\&:WGjGcsu1`=0&bR.$Gj>Yrh33=lHdimM5a@s5lu6#;BZ.-a)jr]PaabNq7DC=EVN
%)$m#*)_A7Q[+io[Y9fs5doBmq8ZSr'/`g\d@a;"+@jKja9H5mmrEUoI,1UFi0_'#1ndUP]rqiiNf3n!UT.o%IRh[eDXbY$S>i`14
%Ao3F>N:>oJc8K?.W@l\7dDCi462*S:4e"Os1/DGGjC-,NpjAL)$f:6+`#oScFoqHk(rG=4*Kmq<N9?[p!"bG=E*B&t>!@hHieGh@
%38H.++cK9Z3>A>$\WH%K1Mt/EMmu;mcr,:l?%1d&4j^%"Psq<>>"7C@hSoB;o!'fq"0fN7`mEk'225je.oIuV'Ho-&4@uo#,2F&>
%MfuKe/N'Q-jpX%\Q4uJXd9!s8p9d`-DXe!nSh<0i?eqilHW(#C3/>_!EI`@>T$eZ<ZUS!a>3*8A2WI^-E^__!W32#)=Fq2CTOrF9
%24iO:UW\SLG-Kr)i\b,^mj-[Q_DZ>)knLo\2I/aDoBVL=7FVpu[?aKl6nsgLWI8kDS<^ro,>0dpE_9&V"HJrC9VE^`FJ\j;-?M^q
%VK6c%i^2kC#cZ7!+k\#;Kf=Ht\:ZXG3(UI&"YQKo0sLKWnXGfQR&'<>(=Ic_H^QC+U9l:<a9I;,.e&(\<AkJiT@J&@>mZ5)U_)/;
%*J+0=,mU+:gQ8HN'BI0M:Y'PE5MUBg6>5)M\Q%)H#tB->m:B#nZCfXHXOT_C=sa@FM(`Mg$7j#T9pL%qP@RVc^ao7m@^F4H_G56o
%Y8Kc"g=C!T8LE>A/.dG1^#k,Q$I?lY5!pmPo"jl-[;l)aK$Br!bNV1(&>u+@o_`:ol\1=i'AG$W_U=EBTg"5<DsRb+kb'U42eNN&
%V9+.)9S"gAcNp@F4s[;qpm^/qG\QE)WiRel+PS`,eCT/TBW%=km_^'4M*e/I;PZ*6\>]k-<H0IBbt[Y'eUZtGOD?tKj^q@\rB_ej
%6M+H$rktuA>ei`*rKqKV2,*0Ir*/MG_;.h,=Ad_)oWR`\*iBV:Dh5]H/<%%b3^]jZ'J'Qg)A[YEpBWX$)^CHn*[@I'b!Y*-<#Kd>
%??dj(qU(Igfs;8$:=mE&[jXG@Gg1CJHSqJ9\gX:K11K?6fI"-l`AJ\/h'?UoY%NG0'da3dV*nIZ7oK]]rsZ7s"/[@Opp\uD+VJBJ
%=Wm0]95\<1=]#*R$;au-X.Ro/=c&F-%@>o!N6.A8%\YH#X7l5kp.)OY#ZN8jI6Kf/h-L"liG/?@[Vr]$U`,f0Ka4MP,bci5Dn=Hg
%%SYbXR%j6R5/NQ%m/5Z3F44D<0K!?4H_K_TQ*6tq)@p<o-.>/XMK7:O_hbJT]..:RQOPKLG'8.P_`'i-\7>tI$1fV4N1'oe]niil
%/gpX;mbAcXZ6t5hh2K!!>X(LWE$cEkE^7jHC5Di30k]<1L`u1O*\^;cRJ0k0Qb>B:bq!FXs'#Ld,dGi2)N$.9Q4.fkEJm8MI[5sa
%UoM:l0#;oEHs2ND%$2to]3(<Kin&EPW#<EMAIL>)*K.aA4t&$M9Rg(FYk)/V4=[4,E6I:\43!T`K>6hiPDh-<&NYSoGR)
%0;4f7M`#r:jPa),C9UC90(;;7FNd.X^Lb0L%PS`\0T;a(B+o\*QY@N!YF.\4F\tFs#>];s57?]fGCsNmF&Ha(24T#UCT/BJj8nL(
%=P8tq1G=[gh[kYhpCTCj`ENh,=Md2g!'sPk<8aI+"rY4u3`Hj:DI`b%,-JJA16`0f#j*T-[RnOPC2jb7Kg=2TYuZS-+-XM69;EEp
%9`PAGd?UAKi:aBR7L5u"9##?ON%BihFFDF(^')];]57?h:i`PUfFm.1b^mpL3,Hu35c7&Q/c52)P&5bN`2eo6\55q4%`u>B.`k*[
%<!CJ?p.h%pC1;Y_\hD,#:!D[+;6pO5$/6^ac##rY\aff,,8mW]3K^,"G.E9f,\j1g$XV]/mP<Ko>ti9n'h-l6]$UVe9D3_-%]os:
%(.&II0@.3<`Z96CMjS4tH%_.!V2emCmY\`S&\R@&4Z(s.;t26^==mgYLfrR*6^j;!$eU\A$V2d"#4Bg>1^8!:V8q,1)Zt_g[g+:>
%9C/?U&kSZ1021e(ne3]o]T!6hh[^AUP!;E?LCHPf2q6Xg6@)>Aak]tmr?:g&#pd=n\MT;]CT\6"O[Jo+-N%mbmnYHm^"t5Mpp[WG
%T>u>n!S]aE_#4JQ;hs5SK'g!l)6LA!U)15S&isJe%B?'>26LtF!<!t*eJ1qketD$fGI2\&Um@-_^`T=c?AlfZ_[V2T![t,"!YOqQ
%DA#]H&(retd3fA$(`9;EWf>B_6Re1aC8#hV-oQaf(K?!+M4u3S.rS/-I_!D-j@h!<%Mq0fInlSadI",E6tT[YY^61>!DZQe,?;2q
%OEh%ie81#8q<qtgNCTCD&sFCT$5DP%\!6\1]>9XAB!ATK]a#3sO6At!3<Sb<XoV+A7!AmBL)$6R#(gj_#eUEk:YQ?OH9d53!-"iL
%LF$Cj#[%`,&OpllS6#%RZj>]WMa`>.T?*+JJl+3PIY`9VbCKC'l84ubh5aS.2,HED&3_s<#*5UR/pLa!%^a/&53@h43P"cg`6.3K
%:@HF@$r/_NL]]\B0$LcV46)n@dKUNlP;`qS68/$3K5Eq?"L`XBq2*VN^>eW>s$G:"a'T(Q'_,%N$!n)V8rMH!59%g!c45u[IWu!(
%/5&'HZ2e:]+Z;7EX#Ta2PTc.G`dHMV*//:seUhe$<89cl8[MZB],:!uIL3\q&L9<GgEKO[G.I.b2mgk+g_V.i4EG#7.=fH_+ZD"/
%14b*^HE5XWML:k4h)ee;n"MG\=t?9/=Q'Xs5'k5*]?2i.&edfLciZT;D;PqkM\gQBHB_5q'?moC&I",a;&;2jHX;"7QERPL7V_?'
%AMCm1eDL$;nbKS^9DB+T"Wt7#VMXVAi,&*Y+$k.boO>03M_38RN-QRbSf70^6Fr!uA=2D8%UD[YLrubH<YL#I[fU"AKaQmR0<-@l
%h60qIQBo7"1Jo?1)mjH8l'OH6CQ'BV9uPl!kD#ND9o2"$[`8Qni$b)l,n]:"#Xbi>Wgi=IX0ELMPEB&k@MVWE$[JrZ5.e3m6^91g
%+j@+o./pT[.8,n5'K=b-4Lf;@W81P%G*ARr$cKKX@Z/Hs7h\)`U^MsjOXh8O%9`(QX$0h.)(:95nA3h<UVb5!H+tG*EX_f%n2]bK
%$Kh^=.>[Ff=%*TZYE_(U\Ug4;@/DGm!MSI83%Oe[W'a=0#R@ETr$^W6W#[%p3MVHT!d:6hR-S`m81ppp>*aSg(ER]gr77u]OI4@f
%1+q.9,OC5QXZ3LGDFt%tB9!QlGtVSXeWMnq/9mo%X=0WrckrcSDNTnaNVO&dbRY5,p5lb'+20cgn,B<UVSV+cLd,_k:J/=7eQN?;
%SfG\,J3_`KVBsXd/E:O\5:l(Lnt\[#0QVVjobYHP-_euA0$+UO>VZW<p-_#rkg&P7/'tKmOq4\YS-YRT-JAdA)E&h&bR;\cm\s$N
%>b:=@r?-Z02]l\XH=*O`\I/]!X2@7=7KKX-UpEQ>@5g)WIO$1?>$u%qX(o2s%*H*_*-8#LhSuD[)=FfY`+Stk\].1\\AU!:@5,XR
%L-$gIi?Ss>OcRIJEg&")P'43',_nRs$NL0"\i&7/78(kGGd+`X!?6WB22C_6*uq.`JB=/@hE6VH,VdReYE7ajBe\Fg+GN>UmUGga
%b!cVPM&14M^,5q(49F:Y@q;<pO*HVAKL3mt_Mkm3NT>O^,ZE]e/#f:ap8h=DC4N8=5jOe(QJQtA)OU)-(3"+GFT`90Bj%m4/OtH-
%p=R_Z'UODA0/8gQ5$.Tlc:a.K)`C-;^3%J&JBjE9#L96CP%HQ4(%.a+nlr$?L3dF%s.qZEd;B-gO6eLn>M3s&8UDo2i%CNBn?1f.
%K[\d1j__Ss%PYIm%>dmdiLWY18qde^`l(3oKiCB]@YIYbOetW\$;eY.9]$BQO\=Ig5^"ZO$a!F!oTHlN@EA1k3:._9/qrd>^%Y$L
%>6#5Cit;5$Rf#r%q[I$UpM(l!+.Qm&'YXs#(WR;k*ODi4jl7qdD/F5<giD87Sp1h.f21m]>,6r*AcGm^f^5,r+B*O^nK>Xoil>bF
%f,$1QYj7L+.B!X(-QN&eO62?k/M4=,&/SiMd.ZB";XTE/_<N1;f6ND]aPmM0qu2t?+<d7):L0]SZ7N3"!:d?(dHUW_4tCo/0XCdk
%:I2CqDB1,POb%6Wi9G9G-*5;+e5c!tF_omkJ.,Y@E6F]T^+S$7E!B<?S+DY*g.;S5e`)[s_k?)Xr`mf"XXOUd:UebJ*:H/-Y.CL#
%'Hbqhc)P.g-BGV`4'7A%:o%/[-uT\a!cbAKX'VllRuS(-XK%#s>rG$J3$X.;p/Heb/Qt6Srldb31Re)=$JY6-UP=1)LN2^.F'(a=
%Sc.r@CP/CMBm'7D;O_q<dgff*;JZr-IY\T-j_hN;K$TCm90iM.b`;dh+l[].aQi(D,:7Yjo-+a?Q7+mV0CgA3n*X]<=&]m/0\fqc
%3+q!(8Z^U9i$oXWYg2n[Obpe]K[O`[X[R_-e-#BCLte.OOO"M&K$1"\@O>p24qj=j=Qf6tTI=/r[ttHs9+k(]eO[u#fjNdfE4Y;9
%%K'-hOY$_M.\4`pe/F:MP?ERAUdEO#W%]ZS=iUQaDMC;7_G3liSeNHsa>m^lj?&GWpL0bPEgE)K490>hjS8+Y)"CA,\O>qH"s`qe
%T%YW<O32&$"UL)i!Q[bG=XLXa<Ue;9?CX$Kbt2'?&TsaKj@%YBbGDh8=-4K)9A!B#6$MKFN"E;X.YK(crbt@=K.Z/$hO!_%jEa%W
%&UXt%gk0!*()$uMDl[c8h,V\_jMD30'fj)9.GnnL>pjFYjC\/*C\QIHO186pG)5Qk/C1o'[ZGD^$fQm88'gVX=U`/*p<uM+E!L'#
%k@k^B*CgOr*6tI/oV[0'o"J.''#!OR+]li*eg7AQJrPZ+I?E$ubqca&L9UDF-H!.G)#0IIdCQ@oflKaXi>.<%BdS,)NtG>/MC_5'
%d6>bnA&<lUj,Qq]A&n/\!2?*Qq6jnQqhfk*3KqP2m*IK9rU]F_U'RF)7i3sNA.E',<JVO9Ib#5IS6dWI$La!B[kj0>5[L"^>/AIE
%?V<J6?#1I)hZjHX?;C\OdHD)YV:#4[EL)u(at^ZsC,O.*feEN]9e.9V/!b7AGR"k,C#P[bBGp`c9p3uM)R@)J4"BfZ.nh?<RekJp
%Cc/_qr7m]aL99@Tku)D^;N$\(bIt<5J*JBo*TsD\$=bV;]XIH=lofmRbhI'm<:5GEAdooIKb0Cqii<n]aAg5*ccf8fb=^A4#+800
%K&-g#Ru%9eI!,gNj(C0/=F&OLGZD0@?s1b!)ac6H*Q[jC>!ork`?]AgOR+N7#9l`_HTsY/=ZT@mX"aSY?%0);W0qM/V#+6c/]:_e
%9+:su@#UW)i[/@jH`8aS=-d&abGl(k3/>[R*^,qBjft[Z-H@JaqbuCP+Lbk;"2J5tYJ(CTQO99PW\[1')PmEO?+!f9NKcIoYEA\F
%q@7N_2J^rDq2uPk=_:MK2NUK$p>@OA,<U6K_]+E[I+*74o2:r.eQ2B:+TZQ"qO=lW'V!JV63>pkFT8Q'gnqdmqXR#7M9IV@XON%?
%QJ.rEc#']+&Pf;_&0ph*$UKA?Se?!F;jRo*G'V)1LA:9ocl2fH/rs0VD'm'Y=YXk^->j"pFn%^Vl6?lhF?.=KMh^Je]]M5(ncmkT
%5OkjMU*1+0TMY_:n('j^ObIm:4l<8/h1I-<SKi@e>3_ujhm]j9fgfS'FHRd^5Kkm0ET%TD"G!aGgBFrCjUq]hCSHV=:ki-9"cC@,
%QcN^LS/9%`#g-WWF56F69$qQEXu^:>[CaN+6BDM-@X=cQcMM\[P#+D-Y'c3*E507n*l=#8qA>mh1gkK1^V?WbJmE'n9*^B<Xra`6
%l&Ul]h)NP[e`0iO<RJ3@IJtA\:.?n5FlC,PgmOqXd`Sg[d62)uFRPDfJggJN2`?_KE$#Hf;>]T9ql(YW@)e%2!8@_%ddKMmfLl=p
%j?eZ:<\=Y25KXBj"dKpD<5#@uUsag"bInEc1*IHB1#*Jn8IVjqpgABh6#$FGFceCT>k1M'>FU=K#3:]Q3s]a\.a$EKR=$Q4W)OVL
%[4XA&)<KHqU!h-0V]M:kbH?@8OmR'S/rhj2]@te5\EVSG.?7brcsq`Pq<T@K*l/lb-/;Q]Y>Ed^L&XAaDmH]@P$EdV8hKKYRm%A0
%8ue/,?3o9?jTTS&"VRhE"(_$%'k]4YYH!a!5W"lO2iWRBQi9L"pgD"EF'eF'(!bkpRb`$3Sma>"g_R6Qr9q2Lh[d)(Er)d*M7pt!
%a3Ms'Yo]*-=&Rs!!HCfd6_[m)^%LoDp5IRCSK6m@2NcJD@Z?5,!6\6]$nfTup[ec$NCNLHmH)e<$[R:Rk\i'DmsN`MHrS7"?n$mQ
%(X\"26B7/M+(Q&"*GsNQ0WG-T`YE5Mj@$FU'M>H9["_F@X>$qZ.\dl`$+3K62;39.ZX":5Udq9^n&RfYGRhMh`!H^EdR,^#f7iRM
%#\[kT/0hV4NO@J2M@LSXD0o3$UY="kO`AGe*:AM9l'hfhn?gkpJpA1#QI82gAWerp8C=E;NC?<X,@Q>Y`rI;_i0PsCM(''M)21GQ
%O_&*Pj6Yi5U06uP+7[g*^L@"E+lE0[V[todd[T2,5DNP"@q9jH_$$\H0(QQ4CKh>NerOm6XQO?`,R*;"rioG(Bb[Z`8`!1oLGF=c
%X-1mo3%Wap/=X8V5rEr!La%12]\u@k\s`8;9m*@o>M;h-h5SY/PPDR3%HrV%:&?[*k;6NmN7&S]VBE3Bi0gGflIU97Q\<]Ch+dF5
%mSV*jYF.-9*6++GKODe9f)tAda#&ru4=C\V;rL470L,=jM57D'Ic@P&doA3!rAIA\06Yb0\E*dkIPl9PgR(pO4Y_k1649cpS:b19
%ccVmV?_:ZrcSPAFA,lJi^K@,c2mc8#5>2-Y)6a@$+L"LJW#_j"aA-PPe(?#p<`'\K\.J8<Z]PV\VfE1XMQd34AM^PU<P?X4#3L]g
%EK4H#N+!>\)7;-ul]P^;D*kYGQtSmmWpTXk[o)WdN=_-^&)0=5(LTHZ7X2SC?pg+R/t!L"LImejC7%tMgHeK=CI]4E48uAH;)"(!
%-cHTD)[asK[pGf-k*A&WTe_5LaW94l[X<ajhi)^&YPF0%h-j[\$q%rn1l@pg\#\J7^JXi&_Z-B+#1MgI<h<a;'qNVN2m<0`hqS*3
%aS-O0X+L[X8]rlA,NJ'u2q!iTA?R?b?*"8tD'SR/ZqIt%>VWPi&'#-S^?[*U9nD"5@H?#W5u;FPYrIrTis?mg0dmnkH41D,T:7Al
%W#uQ\>a!56F#J1PVd63BhnEea&R-M,0Ij+3)TN7*A7)b]Tlt*Kadf!?N$c=852Z6p6aMGbY%[IGC%h.6TC2BbM?[L2gD+aa8nsO'
%6%n!+oINiqLOH+tK+N$BWIT"tX/0<_4\eI58Caf1j@?\k1R8.Rf*((2KBf-EF&4\pfD3Vc5t65pocK+h'3ui72WBdcMn"2BM6KA3
%CCD>""kg:?BdB(17[&!PobuRe7Z>]X`WGKGeZWB7,,Ae8fS+=.m%:u!0CO0f%u5IS:C21R1X<g=P@RgtYl'T^B>GH=r[l[9K?/[o
%(lW%e[r"9EB>"89,.M'B5A#psN[eE,[nGsIh]0R9Es\W7NN'jgdnU&MfT"?'>ZdQq9h7Y)$?&MgmS92[EZQkmY-Z=sUKhLMlZD04
%QSpRXUl*J1gu&5q6pYAui>@M>ZH*<Uqj6Y!cOhXrnRR\i)XZnTPMM5s?%j[*[<po\W,s1-@L$2sLdO7<@[^;N4R*bA15_;NRf/#a
%jS<KMGN,uRV.#`T\AL-8&pf`4U/B<o'to7,?]EErNYu2`iZfGoj<hY?`8CBbKoWOP`B$n04+%<q+`9#.A6Tk"bRR(YEs13n;7$Vu
%4%^ht@-8+hPg[XFb-7.7UPTj&JTt@Fs0^:Y`:7*s+(0r1)cIk#*@+bdio:$<k</25P'N`=S*?iXZf,b3h1@Pi;o#p6a[CnM[*FI`
%)J3;`)W8Uk'PcRIfO@fGQtQWR^`s761k[n>jsh>TPS7e44b=WJ"?#L:M8jUilmeBmAST.ZUYG!G,hS9IZB@rk8"R.B&Us9g3a;FL
%-)[nB4=uu)q9ABk#ca[5^89UAN*!-Ant+jA(H'5r`a-pR(SX-mV:TI29qFR'l3>.WEAS.%q)m'0bN7)6DD&.q+Bqe4V*rIN)*aF"
%(&>pOP>3[uGK;\[GiG@!IGY'iUndn2+o[18IJis_mg2IIYQ)Dhqq_GC)d:MM+Ckl(cY:/G'IR`RP='(A[sa9I=h?ul%O8/.*RV>W
%KaYLQ]3l>L0T93E@k`dGQ<gn@IuI2AQb):@^GV"]Cg.(:Wt^k92q_\YEA&1*.,ASJ;D2%Zq;WsMS.f?YI+C7;LOI4a9]c5;G'4&;
%D@HsV/[tPr6l,WcltrQ'"re,uSq^C4GCZgAR,+fEK2L68Z&1pZi0tJ=BD$X2SOiOC8lroTDR]-r%GL"3`&eWN-"Ae0%^eTKHFqm1
%O^Q6S1\s-uG+]qXHQF9r>rXhGB]b=&S^Jn]Kme?bIa!8`V*i$@ha9()Y-U#rV7*GfD:/6L3X]'#3phiHml%=NN()^"epclRB0nUN
%p6t^M96TrA2"T+uN5;(]NEu4JT+g-o@W`Y1%V3WnN/&[m9\Vh#:#QJ=::Gh#80i^r-=Oq66n**Y#_2t_-S]X,V9^"*oM<DXd_k9D
%AW.2VP&dcg)s@"Je8LEb3K6Hu2#^D!i3&BLmg-O[4U2g4C?fmNpHl=FhLD[t3)#=YVThC?D9XZPO&;/t(i)pk>?c!r-5F`=)Y,UT
%/Q,%]Omism&N+A3f(.k>?Q4IHm?Vl5O&.t:iP"b!Zci/GG%&d34CM/!q/;S8GOA/2#DG`A[,;TSq/b2Rre16,7\Yf0LiIV3[i.sh
%63kB[<rM6uQc'+()d5iW.e!IM^cp.2^O2Gsdba5lM-g6$3B)Eb/-ZbN=Mb520a"p],lbPW5\WI9"]NWrJOYdgcB7l?NT2:taU'&W
%p(`'N!Wk-cS]6i4P'W5+irf/lRYJ>N](JKm+[d`-))JVAc9A),)N8:5Wb2msOY%Cc(c0>`2K4gmoRqHVb1'Ce.l]^Q2ni>Emuk^V
%0ESD1^-9(AcA`Qj<o32-P.i(+X_1;U+p0!IQ>ZWf)Z/rHbmmK1\F3faq)Rfdem*1;qUdq1bj'br'n;#,oH_^I3K=hnOq(bR]M,e.
%km"[I]\/N+6*'VTd/KQHk$]?<=X2p+T"nolED/SIh8$3S![S.d(KmShAN5A/lL`GnFZ]lggG+P"AcB5QHSiJ@=bh9*81]fg[+Y`/
%;Rh14,p8d3S657pW)B)KH6[=.WcQPY:*Va`LS+;1KP'Q\&R47QRIfMV^O0;4qsIYX=jarqlrl0Ppm[%*m]Y3XGJ:@AWE`Q43_E-E
%SE;#Uh'1MR)6J(,WbBOdPY`3N&Q3lb[tmr4a,F7YY#3DacCCD`'!ic0H<piGC3UZ,d?V"32a;+R'L0E/&GJOU[KJNFm5+*7iBN;H
%N(oam2"h^)=PC-+EMBdR4s$%-9@sNQQ;R%5S??Z^i!ZRs^fCP;#KdBs3XQEH$'3PaOGa,8"j)9g42H0KT;9o-V@"\c@&:,`E@)mm
%='sSu#/<!tD]-<>0Kqm,W9QjW8ap]s*;3I]Pe?hk0k4R28c+eXEJk$7P&1"M#1-XHIVEqh6gnn"H.%j[%?d5*9S;kI]0=mD<X`g+
%;\J\Yp]Y3XjTJfF&8)i0RJi?-Ej]MVeU_6^1$Wj^G"&!jMjRc%1EW;pAJ"QYFN20Wj*?:$XnLZQ`%9-(*Eug/!]5u%W5^XK!"3)/
%7J4%,BHa^PPFYO%3qs,3NVCtffTdH?NZX2'N5hm+(,@]gO$I*%Nj)IRG4cFl:kapf_)gfX5&&en^E;*CYHY2O+&gt)1fea,@bL6c
%QEbliOYm6t`8)=>Z5Bl(7h5Es<VjDt4&?Utl;[#)437]=g3Y9&C5Rk>B0,SOZHWp%e!^"FE^X](!o(KOg2uE7#sU]l^s^AMN6GKF
%k8piEf[ZE96"bUsDH:6l[a^`9P*7f5O*Lja2:4%*)&f"*`/'N(W6k60`RBrC+0a@Y^hkPK_.YT(Br7c4`p0?Jpt$A!9#=!1/cXD@
%A_=(Br_$`nFNGScX]*GJ)$MGq9l#$,0#siu=9iW,jq"?EYo\-CG=^.7l1Ki]S2VZR.HW\u6bIGSD]!5Zm:NrlnrWPI5NLB+!<p$o
%@jrgZ`u<]A!!(QZdPGPV'hjjm;9CaQT)=\nSCFk5'0d7W&&U;$,E17#^r"1EGFbX[WH"6D'dJgMl<?@045])ZN]iOOmrJ*&!O*"M
%Vr$d*1--fbLMf"0hTSFj/1RMW`!oiST$dE]Z*Rsk(QAIqrCZd1+42J^B3,4.d0rUGHn$`jE)Bb`NMS3mdCS*3C$[Xc6:gUA0A(eZ
%;mmW:^)PZM[?nVk":uq?Y`n+tXdGiai0oobaWZ9UO*S)OlkH[F3j<eXD%0CQTcR0+]%T+)'d7E:MP/34"j\eL-FAFt&2/1]PkIeG
%<3ASW!<9-XD3fLU`2Q]32?i*d0J2XG.+,11UE>fTHN$u[!Y*MGRa'),lOd`N12[.!,tFRo\*/$S*l0Z<@N+J_TA^]$#HY?%EB^t`
%D\n)jKP1TCV30++m>&0"?u?*\#B.^ZYQm2``)C+C,G9X5>&VEKk<icL8nL>\;And.V-'1jlNh8&BK!F0!.=Soatg9[Yh'n7rHeSG
%5Cq5NHFE.16=';eeQWCOVaj2t_aYE-i#mW-VqN"q1aW7O)O=K#(*88kIcZDZ[YdE)4]E#e89etsOAQN8KEmK4a:Nlpit<dQiG&p4
%1TGfY[N>C*<IBC!USRV6SRFuNkQ\'0r!-VkWA@@;$BZGT:H`A%&@,^1<*4U$&q(%$$E0\1VDt2i1aBN$W=E3&4jXiX`DnJkKUZOt
%Ee7Gj\\=FTFoq2^YD&eN#jQ6;.H\F4GSQ67!ZJ>fjFWU.9[\U5>2o>@DZ-<C5GUirMa#IW)GH7^bB7^.M+TZOUI+M*mK][?gWV!<
%B_V//Hr]V9S_REB'C(ed&BaMepk85mD^'Gk#Kf8R-:VBq>;aC.(`$bopEScUDYd!K$5ZHMJeP[a"EnNd*MVA%8/%C:>VkB(@8%ro
%2qD-1effkl<=X"q<=a%U;;2On;$9]sN9`/-qeNi'CD]@d:?/:0'r"tm>n=jRN(lPm89c[oSpu*<rY$p#pS;0#d*2<(Ta1*0G`od0
%+B=%QNFH[+`^I>BGn=;Gg:5_1[XIjD#;qIgAQ3h7)+N-VS^LhEX!)h2B7mt'$\@JqL`(f*(FYF%rCrkFUp21\+FL(JN-E^TYf\$7
%3E6;V1iZk#0ieS1QC0dfLQp>`]@gr]Z'1#6d9s!]:^!po&_!g17Oalmng;Hu<E!B3!kKRNQps^#\2c*.IuCs5>g8bOD`\\Co[her
%o,9<6-Or%L*:JGR)O9m8D9_C+kbcGY5QX3]%nKEX@fM@^F%"^4.>'NA21f*$?3O="#ZkltAD[ru&Q0@<<&rAQqBB';iR))hfi8OQ
%'=h?n3`2O=E<_<m.p&jH<.uQI^4R=#*T4L=c0&h)_&$qE!uYiLfR]c4(sbo="p.ibrLr:Ojc[MQ-;41&8.Gq>!e7#McP]UYaS&a&
%$?a&!^5T3T7W2dG`W&Ar-rE#+`c9ek'5L&W[*sMALJ83(Zo+#6_,b.L-W%[X6\,M?]tf/<Mg5X)&iEf(Ke3KIr1TO,p!@"ZM\,Fl
%+rJA=D#H'gX%VA2,i1tt;Dc!3W.d>LSD-5\?QiQ4irgc8%+-N>aUnto7l1rANg.=hcn4Kl<_o)KVWU6_)*QPG<uNVNY866.i1B:@
%Jpn+,-GB=P1!<\@VKqB*@>%^&O%+rgjdm>ohY`(L?%#0sQf:uoSA]"`=d4-jEZK3G;S9(+5k+"m7R.<*9";M*3Ro5nkRkD\)7fL\
%&k/Sh-r((+q4R%mDu7B4bkF5]gc%<RY8mRVq&;%l>t(Jq7qq<cKjJUl#EFfS]3.uN=jj[4G)^[PBMc1a<Ji?.c"/0F"'j\3iY]*-
%#AV?3_@X-;(Ff0JEs7#(2"P&i9P-!)2!9=_R=U^O10gULj3X*+Tb\OqZqhiROFpG`5=RN;")R)QOk;<9rWRX5_9aCsAf?r1Q(KXM
%",s"E&;TXp?Xnb^F4noYg&A6JrM[f:.d(`Rf0>#u2uYqr8I*Z1GO*+oT9Jn_jT/UE8gGun[ej0P!.<meYAqXk4%POk&TpZ;W2\JP
%%W@PunVeIbALPTYfL)IXN9\?T*@&;P1(3j_7>Q'hFn>qsi_WJC?#ugjc]c4h0ld&0*?>;^ML+UG6OHJk.IokqV!o*&p1k6Lan0QU
%U*b9B-0mdP0U`//)O"$GL.We6Jm^/6+G(bG@NY^6H3/T/9<kW-\Yjp!,6rr-3oE)a*CSXXB^-A"J":3)L%7d)k)XdY&WK:,74DY]
%6=&"YQls_1&YuhPm7=XWe7AV&/+e2QFbp?10HNoKV&FkOA1.hrNK?Pj#/m3q'kk\I3ljj0,Pl,^K^C0-5OHDjd=0VJI]DC+4TIIo
%AJEHNNQlM%jjbHI]sF<7Y?LI-A#DJLl4qbYl``E5)hi;4_*B-[%XsWZPcsT/68AX.Z<I/nro^I>M9iW_Z8X'%AF*O$oGM*%gAnCa
%[@36W>9>)oJR5YVIDuQ`(1:tQ`X(!;41[8_`[a_U`eT_.qil;HR//t63Jl14^44NPkA4@^+YuRb0"o:qChL^W4T#)5+a#mm*"]0D
%Y2:c>WV^WT"fu"Ucp@q?BeUHt;)uKN4@GD#D-B@TPUfnPGRERe.mrh22Lio.h`a71:iO`g)=L%M59hU..X#i.UO#&pkH:kjO)_^s
%*TEhAA>jn.b^bau/S7tX%.?\NB)h!jna2P"H1sEMSh5#*gb>DM?Q74"iF!;i&0Hn:*CWQ;H_u(g,p+MkcJOYor.X?:B[jJ[Tcg7o
%pCo/G.r7hhd3gZjV'L8/L,C9i(uRpQOXKr1Kg*#<A*>OId=)Dl.VY$P,NufBla^MKH\pj7CPl!9@r-b38OsIdZ+A;:A``@C/ATO8
%7+pD.7ls'eR[_eZqNN\"#55S/,=?B3!X6EIrt$c%*`eCsTRL[<_t&G0o;YMlmPh;lDm&=>3UlEL.^*RS(t$I"$XJs=`B]4Rqpq-;
%WNn67h-/SYcW>A*#AY@,f(,^V>45'_=I'-tbG>dlkFh1&CUu$o!*<Un]Yl!)Id&6ZN6>E;.e%\FXl8hcQRm%t0X7\g1<7=F+kXom
%]2]1]bM;<l]L[:d+:+^ZLer-XXDrjpO()+:MF6sh#q6fH4*4DW/.\0sl,#9NLIL:Pr*.7Rq.[l0iRV8RVgp82-X5?VALDE@o\A:G
%a$OA#C2UZ*Q>%<rh[Dg7VSEG$'=SqmlE[@]Z1>/GdNno9qQY*r@<*c\:pBciZ0kV)5lIMDN5o7lHF++nEbu"?C>4gCc:sAS?V`6'
%TY>^7gEocFW(+17insK,M*W5R;K$fa.CgB9)=IRR9K!?@&?g%>E6`s8f#XAHG[*!PRu%%*/&\&hH[9Sc<_!Ql[;Toi?#3IBL5^QV
%\TVa3W.Z&C/#KTD3%<(W\D?N2cVSMnD&u@FCt5lhI-UJf9fd$XZmdZ,>7+@La?F+;lKQoj#NMYHRCH)0XYb-p2,("s@]JBjCY&r)
%lD*O1h0Nm1EJ\a,b^5nFHu>u]nY*sm.Z+5ak)DhO$199LRbmEXkUu=eP)rcI]s4oS;9jKGNST-6qL+<-Pk=1^cLN-QJpFH%X*86?
%N+egn"45OAmY&O<BK8(6En5VlNt?7aA%P]1c[E?nFXp!!W_Y)*>q/>9'+7=s%8mQO+1nEm+45'UG:TZ@Gde/\Y306qDW:2,mCI6G
%H>op.+iW'X!rb0i24*aaEHPA<nlo2<n,F]cOh#&Se,f5`C^8CG\oUB>2ITA0%7qkK=;1#IG\V`;KCHYArJ95:`$]"$Rn\9+Wp81,
%nPr7>7@JK^j2`kp8N=3+M<i\TgrKmN'p%]rEe6JOXV(49k-AA/-'aGT=ui"=h]0IY?$i#oPf?HrXNdLq*fDY/P*S'Mhfi`BFnW7!
%]Srld+o7"I)==)t43H3Qf[EJa`d3j;1.LKm(>EZTNE`<P'lciMr>hT9.u-D"[81=t?J*8W1#gIC\:-;Ir,MoQ:X\*<4KhB0S=5_(
%5`$<'A&a9$YsbPH.IJ8o]u2UJ<(;/tpm/76HqUogkdum=o#!Di&/Jr,Y)6m&,2XnNg3U)?-l-,fS<(W;Or)9nN*+<!7O)4-mk,\/
%5G_+ro-HI<q.&lp2U].cb$@qDC?`,uY_mT\B`F<d="qr(SF;=+A#SK?oLsD)&9&(IkhSHu%s/Z050An8^\tmBObs7"lsSQ4lZ7`!
%C7'&B_eOHJGbP0"XSCti]p,K+HIS_/T(4^[<LI0r!Nb$n5S4%F:#KRl"sh(N%F"u!H'kYr?4"I;Y[?NP4lo/fT)gKN[N-U@^jm2*
%-M5VZd8RteVF+W5*.ou+,npj@Lml1l[tENIjUQEpPjT4q$Asd/<!;b49nBRN19uud_2o]><D\iAd_3I8(D@sWT2iWk9X*C1hAR+i
%W'EdsWC6`X$;<CIWNG3dS2b^bs4I'O3/(=4"kTq+JcXHg.SF0<6kPB)?tPPg,Xq3Cio(9!A-Z<U@%8iM+B0(+m+SYD6Mp7(m1ojC
%Q2cV64ME2Q&Gbcf67U87Y(TSI<Io[?qPf4a!sY,J4mOanCr]h_n/DL444Kmlp*HlHI!C%@3@$g^dKma>_s0kH1p!!4p$63*;oTS)
%J[>ei`"I4.^GpuM<8k77Jg!Efq@ec"G:c&(g$re1I_T`l,pl<C!;tb8gX!5q:V<ZgYr[2*`W<;*G0=R,j#6G-Wfe-'bD,So,A;N*
%r?/*gb#?cS9(RuBp/5L^Jrt<4+ZbuU8aIgjY#V,2X/:!QFXe(Q>$4;ZUom%,$^JRo,4&-7![LEE5*<EMAIL>@?%&!@V!hO*XV%"
%nQTp_fsd8,Hl/-2%W\^t>4$%V_6`T.9PZ!fg_N1e*.fn13s%50`*>/pV3E8+o?[gGC#qs#CdIYtL&fN'C#@k/[hK[Wno"2!4P`(l
%[FXI^YC#.spr/s*cT^jW>.AGU49d!Q@=I/p*?kNgTM?/F,M6j@1^4LdJ>&=mWqXU]i*qE\0l>f_AcsYd9qpla^[Rt4MYhB!M"0h.
%&>%T?a.UdI8Q<fC,$X><#5EtR]u["O*S'$62-9MI.-p^F5ebfRIDQA&/c@C6_o*Z[+G]nFe!-f>I0)l?Vb6l[i>9NhN2jFL,Ml&H
%W354_[bKPM(L[]1b@/;J*dSZmo[darq$;.PAN)#IX;"N`*k^aNQEFh.*`#,&Vhg^O^.Wrm`i6>I7:;knh"Y@+AkGfmM)jp5$m2Zb
%l3T@P4FqV,Bfn[YJZ%>HUg97Vr*D8CTUrq7[KJ"FOk_G$S?S-^pbB0ka3f+X'ouKKrV_g/YP:0b[Y1i2j)qDQIE6$bG(K0/dlg`+
%Nt:%38T`=ZIbrbriJ"*V#0D9X)PaNSeP*7$,cKNq@K?$F*l1hlc4eUA2b6Q\Ar)1OCm$CWSjRWk*??@1M6J'r&O50!)'/TaE<2S:
%o6?bm'bKr":aEtG;A02Ya++F08!rJiTl>&PT7AA@O_"e<:ZM,/f,d(FQp+OVDYY1qFS<lHCapXu5rm')<onJ<H&DR,:I(SDNH$P'
%B%14T>UFSX./7&@g&_(N]M(Wb)cVNg+"X(/q?3gfIhpSR?:6'l!d\$PnJ/<;Q.IB1gbTR0%&c!M.\OB7B@3mu!p*;_6l>h8#^Tn-
%pC"H+"::/#Vl;geKk0CQ6G:!D5V>6*J/rh_%pq1'g_MC@CU!/B0>CSrrM#"u9?GEe*W^O6*TSFbG8MDtfr+^gX+ZT<#om`o@.%KR
%&%h2u\$SQ6PO+ST_5>\aY4BiST30n<@s9PAnIE[>r[&K*bOMu=LDG+SpDC-Gao!-*+p`_Yfi9;:]KQT*Fb"M#U4d"hD`jE8]fX(g
%]puYpp<nQl)D<*:FuYo]prArM,qrN"#"GIboX*g:aUqJbJGIXPOfc-u_gO'i$6r7i;TnkKAuPZq1Hb,`G_oEcO3f-[#hn"=h_/X;
%&LM6uIj*Up!ob:LL2=&2hnc$0$;X-gQFM<CagH;g\UBc'X8E3lGt3-JbnDZ]<M^Nl_IaD"-BNR?G\Y+h:DDmCS9%oA)6c&j4,-sE
%)j*7H!YDb'hd&<c$N_q2-Q[b?PgS9o*qhKJ^S,f.c<fCl6^/`R&$-t0a#o?,Qf>Wm4YH#a/E;pB&,7Me<T-WO9csD(.T%2kdU+2'
%=P`tL/O:!%,MZ\lU&BhrIgS8!*,.XlO)ZK)^dHTk8GI7mPqJZ05g4\:'A'?nj[e2r7X#TaW;a\QSB_*'\c/eb)g=O7MSK/q$N#[G
%\l]fAoleGf^iXC`7HKoSr#sPf(%h^tc3$$RPnK\HZ('qJ+CAZTgSs\1/+Zgb:?7E)(mjHjeacasl`$dd^?O#A2jMmlW2>J6GUtX`
%4RNR!KY3tt0n%.'d=[Vt"2MGRT*Ur`!cZW!&PqIt]Kt\?iZQa0*ofMl,r8FQU*!YcN,*EV\OT192Y]ZfD#>lD,a@ej@\HBrTS-P/
%a-*QSN8;'e0H(hYQ6h/s,V2>.n5SB->h+oX)gI1(C`>kIH!ekg;N@YGK6niJh:VkTqY;gXgLEWE]20j1lJ+)]aE@ZU]HaQ6OeNOi
%75+(+H&TcYh*WM+IYc:BE(!duL]VlE1L!_cqVP[Kc'UQo^$ih\4C97&k+=<Or`"qde3CDQX#uH*^9$%OVRs0J<8eUQ#.^Eb_?=OX
%7i]ZBH3T$kA(spaQP^M9X_:J2lLaX'TN1D>BGS"jhI;.j\qu.)coS[3#,FNB@r?T6HdcYQHKOap,V/^&pf-TJO6_fedf'e+XVY>^
%CK:o&^*2=oUf2c%=@87V>tR.n"BP6UN^'>od!',s"r[:mAQpLT@OcBQG_FYt<$f#HlQ&g+Q_Z=Q=H*Yuh21?_\7r.e*EZ=s6s!_.
%\mVObJuk$XeJb,MMd#[U4Ef6M%$r8*?B5YTL`ho@Y/J45DUM*`7k=g#7B)PVcoeurEpHmdXDMYaT"$Jd=&"nQfV&dMS4KVO#%>Z4
%87NrlYtBBK5`C(@p9Hok'ZGT2kI&.091R-#>P*&h%emZL9C>>i?P.M`Y?+_O(ltt_BMb4J!+_J,(9`;B*Wc5aF?%[P>&*^DoJ%="
%4lLICogN.&\fo=,2d`]4pl5c#]Y<[<E=A.MZ+fpPDOkSeSja1q9%8d'MO8F8FVn,@fTAWQM:qQ@mjd]7a)?P0BcLGNm%@.DNE@AH
%"%\X7+to\nK]eco7F<AN,EHtBDAi--*@-W!3NmmeXVGM6B5S6qmK5''m2cYh>r`,[MGcmsI>;%tME;W_8Ha`p3JaO-hbOjQd&ptf
%):`=#\H&k;aZ*:8LB>8rIo!]'=L9<Hoi'KH>1,OgQsGj$EQ22+0$F'Zh,?LLf-pJB'WGr_VZ5ob_>o#ZqgPUX/FHL_C@:Tpl=&Ia
%%N4f]pk4M\9.k9>@ZK`]a*%c;T?#Wmg4_q$F9jp`]>>9DM-6*(_f<ZNNn41nTiR<QKZOcao(J,#]0p4%o&\YT<(>p.P/Kpr<HV+2
%2maP),`fLQZkiLYk:?7Ulofm@/63-s2@@p(I6uaqE^Oe:*'dt\.(CnqnlsqTHj13rj8fVJ*UE\]/a!e<U^Zj-HsCPl8`T0']4ADr
%N^r,9)PKN>`(/i'!XF1r(.*nLHT;ChH_b^gjXVUDRSpkH`hgM"aSdg(S#IGjq^mh?[q[Wa2Y'ir\N2sR.Cac!;\hQ8S[Fa8I9rZD
%]PFL\T"#6te5C^k[pU6>&lZ\)/mn<TH[Q3ML7!8M"9Aj*<udt#>N-N>(+U=Xjb\>8EA>?h]b[5?Z")#LXou7r]nTU][1$&cEZOVG
%$\VSX2=hVGEhN<r34hlp,5Yt(Uau]"Tsl>`*k1Qt[065+@cn:R+[>=',VrWsjXnsqN,1V`Z;fTI#`g?NViO.O1:!cQpQ3e#T'I.!
%GG%(Ic[B.eE&[E[)ku$+Z;*)U0dnXu7kSI4i/RFMTfC/T^2t*Ep`Ym_7q9,d?b&r\\]F!5fCW^F@177nT5S\]R`*Krn-A;nA)3!<
%D:Fn]L5XZV<((\WpH9n)^CTne7105hiCS^o@#6O#7;5%GLC(NY3jcDT#Qs9/D])E[b:"]7?3j8-"B91$+OI1jE\Z2aO;&98DV/Pd
%,:Q/(*fY]#9ELmP%uQbrGEfA^OEMQEXO&hYECA6BdF]*c%,"]nFMk=W.!uk-L3QU.JSiBZeB%=/mWT/Vjb1^;iY43^/:f3jW*"H^
%!G7]+]__;[)6*>rBT\c>VIcJgOg^Q9ii!bTNB0trjtVeKJ4>j)(mV/P9YWChdZkBdf*L?7Gb>Us[J4O^>(\6jN:XrrK)He+CSIG6
%[i6;;(NAe-PMt"/a*pJXlCa*3ln-rrH'_Q:<PC/U=M+3.d*8:Hr]7se(b9,aVd2B)-Gd3gB<!Ip7!h9iR*^Wc%B5io(oclPmJ.89
%D(;4!FZ=)1@&$0kW4=PPDQmjd,D*t5o5A^lFd7!D=4sgL^&sNP,5FKUlT&PO\D++cb'KVteBTM#WFu[$aKrCW\?TbGC\MWJ#!KmZ
%PpmFm-"[9VL[Pc\o4J5ZK.&hsJfnL7PVI_ZFE^_gAkI8@Ga>r#h_W\`Y./?38t_.ea>GTu`\#f0aLJ*=\c$T%&$d;idP?%o_K]&o
%?D0%O*"aJ0i'^N)4$[@F+U%jEA@[qEdN]p=$f(h%#GWb,\W-J@64a&;<JpJ4Y0b2VLQIWi;F465dp!>VR7*`e.1*po%]/p&HFme5
%PqCk#R7QVlSc5r`5-C"sD9LM$b!K`/Y840Om-&q+I>1\a>qne6j`d[g0>M[fdtY<],QI=>i&EoQIUMD2?/?!o0fPhCSutaPIqQ>&
%3.Hu=*'AbZm&4G/4j7t^JV(r'Y+Q'3dB<Os.m6/TX@CVtfnG+q"CdMl[ml@+L#-m(l.-ZN##AN,c%'qNmP_1$3!N!J!:WoZD?t<D
%Dc--K:RSMDlD'_McZ!T1i+Hr)'[\eq8cY0Cd\9#!2PYo5'9uN$1riE5j^U0[i;j=QjaFL:7AXBleIlE*gSj<n"('Y'%93L;Y(l_l
%#au$rZ#Cp()aSjJ]k!:qJT`d,YSUg;koUSao,*PB'D2WQ&g-.3E8hHpYfk"AHbF2P_*;C4B>]-DdY10Q:LU;$9Vg@'NWJk=49/XB
%"&2uQ/WP]#1<a:ha;mkJ7,++V^mpU9>4U?`p,#Bl)_kfEi(^hZdd]AH+9+@LRBbB$fe#D*@TnK*A#Lq"GY\,'_q@r$RY2D8XEnVp
%<)5WJ@HIR?U10@nY'&iEGqd'*OujQInEUjRI*#VMDO3,q:[qtC;\=hi^<Rh$[h2)0E+tR-[HJ@sqj&NsCKrSM(26"N+2(DKF%_)5
%82+b\0d_"PS-nd*?BA%r^PZC3Ss;7P.LdC'/npGio";M>,*j1qi`F<%P:HFW:>,5[Q.k+<-Wl?A?JJspNj9HfkC0)4G9-/6[p.c%
%aku%TJ'AU,L@-)C,M452"Pq2SZ`-NXB$g\L!&DUrkDIk";BhkQZ,l"&#?]qS!M;>nomsYm$C<^446a[/1.(!d+3/hO6RY%/P[0$+
%i_V[lmu:88TP/I1eI3'/ku5$NppO6`j%S8!P.58'_LnhrKBn3Wg3IG*\pVa2Pok6c2r/*lC[a0J'TE@KHZQ;MCusGd)Mt5`Z$,"o
%-S?Hp+=dpaA5HCfijro/GIUJ]UuZG.,k,rIZ6!&'oa&9LeE*Ep's;X.Ykf54o2T`YfdWA^2W"&*T&G\J$MnEjNY;jaCpii*T%hk-
%Xma++/E3MoVCVW[);.A,f4N'hNcj6t[?^oR.gN+FLDGm/K-BkL8*spmgbgi80[hZgpp6:U`3u+od-BLfdDLfD"Pk%!'BLd0]qVm.
%UCHYP#uHB7\su7X6oq-.:WQ<\2'aKnLR3&oH5pM\.o3=f'u_)8i8(!*gO,9,WRZ/p.D!Jj6S50(PYZa1UoQpABm]N_cPbeF]9%gK
%/pK2j('OFe_ZIP6dj!+37:bt:5;doK)(s?q0QseK*-U^*;cutY;d!YR=jR-kHYTiF',Wq'cH_pjh!5]k&OA8_JsZjeGQi?30fJ"D
%FNjiT0uGr31_0%"F-lOl[.g\m0C7tq7BK/#/Clo:CKsX7O0PF=M)_3k&ObNS@8D+.GS^lr5(et3Q-1KZpf6t^I4IrR?hl%<bu#^H
%iopWZau*qP[EjGjQe!/3;3VL/WGetrmppJP?r/X=C2.5e</<_EB">GZAdcu@mCEjp=OUp)7$CkR:L\a3@.aMp7anrHP\Ts(%nXrW
%eUme+HfI)"?(!T`Y,<IXXe^L",+1LY)ToT/8Q)g(bn(TdVKiqFA*M6k&=!kClq`Qk1)!%7ZN-W-HKUn><2s'[k>jj."NDqR]$H@k
%e&.spUjM1m"*.^;Ch.ll>9kgld`D%;T33t3S78l?LVE4eOqeebm?^:rerT[t.ts&tdW!]HX>ss7XGE?W`iU#u$%679d)Zb2#tNHf
%A$)lFG/0+=(jAmE5r2g.J<K7o;h;>4QR'4aC.?@.q!3Mk.H@0a)SWpOGZRY3F/MIBBN=V/ImK7-8kC$pGBWWPC*bmW\DD!$"m]H<
%+Cg7XB9a[&aDB@hYt)"g0'WqldM0lQlB<$O!<.UYJ?+K$6,e?tL-)VYXj"a/8QiN.!etg"64nZ<^\b%\!j]Bpbq$ddV(CRtfhHjA
%.O,TN5Xun.qmfSPE,qe;&E?1]R+E/`'ZXe^*OK3F;%"ODS(/?7YT>$ZP"lT;Nf/=_oM>cXPkQ5XC`C:g5iM3YeFFr:;*^o%mB/t0
%@'%!UZuH)WN2u&rLlC:91u<D-HiWM3CJ6BJ`FqP]2:%I!:\Qu=a]+Q,P\(*P:tsS%MQs#<L/@t%]lpk1kjTF%d)Mb*5M0fpqp"Q#
%kUBpiT$]F/,o0c@2i->P^%W?Cc&$hn*7IZ(p+Yf'IG-JuID(A#/"Y\5O9Ui!`$09]^+sZX.nER0jTs7slsX^FQH)NnaEVJ'#38Ca
%M)3%7_S=k7JQQJ5+!X;h*D<jl-O6DtM<l%X;<).@7U^3c+"s;lb7/+YqB[iYh1UPr8"!oCDYCb;<aq=$0EuS1RgdRL[WRF\;4KV:
%">S+t_)QL3!\u/O)tpE)(<L>/@%n@96shTXZT<Pg(?[L"2I2O+%1otKLI^3&gcIV<eMI72G-n@:<0>pT\"?f4h3d^D)+L1-0G_(g
%_r%&Rjgq/.\eSLlqnP2a/VisTAYdBS4sbK/r<U,D)3jS]:Cp"gNA;]=7pmk=QLTFTV@5+I&[1M2U5USR515_8)ZlrELHquLp2R>'
%l-*ZY]Thr/>0A%'qqehKOuZ'NEo/rfcg#]kCb;IQBJEpUHBlCW?HPq%=5;1d68`LBs0A<lAkq'd![!XG(R^o>?f7,4a2a:T\O_S0
%JSI4CZ1>su/iuiTLg8QU5)@tZp_ef1gro[/fqqY7/=o?EWItTi:L#]E1E'2.Lou</pp?c2X2RZ,9Hisb4V4+mR(CEB"ITgdej-/Z
%o(C&s>SGXWhknWEKXV)r8Q`cJ3V(3dORIn%`j*S:E7HqU6s!@`.CKUBRAWpf!;3='5:Db3S(esGRfj?`s7pcfX-oiKJ#eBp!XZ<i
%D;0qAOel5-s*iJfACjNY#?S;8VR0roE81%=#uA_VKZ_XeNp7#lE(ICWUPTHk`\EopQUtP4*g29Cg)>MN-&be;-:j;WO98k'H\K)L
%?cIGiQP'nf*oRrq]2OPllV;]!%LV++\$!:nYkE8^fiZY.g9(WOXaRU7F]S*DUJ%0`^FX#I\.IX-e"M6m$nLe7HU=]>mW<$2Mg?gm
%UDr]`kp_2d<U^f+V`UA6L+mAGNZj_P*5TEc1l*Rs"Cq1$ftk1M7u6O"Oa'j^)ls5.]IP;r%I&(mJnU.V2jWbh9P3G#;$H/.oKih>
%6;YB,9K=FZ/k2A4m3G@B(IUAf]5ciBd>P\Sdl%eAi5D661G_;@l_iuj2Sp:U\P0B(=%Dplq,(JX?)6n4XI>HU7dQ(9#^KfZ,;jH/
%4g-WuV.:9T?]hdOnW^OD.E&%T`N#WqMXUJ!/)hfhZ^5LnLk9K.&Vmg\I(7SYqH/WuI*L,B@&uao$Gsjlb3m0/M<KOr7a,Dc5"g.G
%A$T^#']A[O"GO-NQld2,?pa\r?Y?1@e>>\u.dbB\l9!Spa**\T;B.016s.@]Q62IL-SgPgNu@f.1*e9F:o):tfSi%$1[`mUCp_C.
%4+>NM)+]Lb7-,%@[cTD5$:LBrGhlQ$R?bd>hkC;7d]0fPiq?BJZ+;@/lqOB[bK[X@a/I0@_Wo<i#I]6;<mmEdjU>--f,qI?h!EEi
%!QnM,?ti@*_t;3]F^;em?glqUljZ$<L#iR]s7hJQMQr&K;%RFir83=YnoR)PlEq$n($G`?WA8hVVV&gE?pPMS29(Cb[lHA^A_<;=
%P/*D,VD-:&92rn&l;G,V!u[o>+X'rH!\egYL\6I;7Zm1Sr*2cmA?r$f?,N[D+DSY&\6,0^"=(EuZ"Q8KpVl?QE?7D)O*,g/T81/f
%nGjRc^h]>OC5O89I=QV`QB@>,0tJBIU'+Y-CItSK*&Jg5)'HP4F>%>2`9(Q%!C__\ELBH_kHe@HCU2$Vot:*8WS)uMUYq(ejbQM>
%c:l>_"JDMK_g)(LrJ:&^48WdT5obg33*o!gmGhl`^VYlNd3)!kZ>rs$"s@/pG>m)W=mnfN*?d<@i^lr:q;>>3"j2pL>ra;<mEiEp
%4UYE10@(>6<kXfFV'VU2U("F]4?5X]C$*Lh'RtpHk&8@ubK^r@**o!*Q=u1F#&V?n+@kU=6]t0'36:4k1:%Q7W3q0*T,$E!/,-Oo
%lD33f(#@SPiiGUbP@d,EK*iu!BmYO!D.ih\?$n+-oLR4LWMro/9Wj5;CE"GYLfDu^=rO]CiX4`_&s![rWer:q/Qifi'%Y9,K]`O\
%)JS!+E!ahI&CD?Y`MDdPh!Pc&d(H5@@N'G:XYEoua0hH<?5Xc(U=HE'nTh])M+5>d^W$P1=?if!ag`(u"%;EboOL%sCnr]Zq@@,I
%^\>B(9/]1lOkI)FaX91[Vh'9lHm*"eF&H1[Xrjo_+^&H/U`qEOI4edRNC7.3LfJ&C,bMpcnph?U>J$SD=fY;8NBmD/Zt5Ehq_=88
%0Cng,0bD(Bh8@G-K5)8\F\TiW,m?'*`"LkR,W?L0f!St_or$dh/W<nhO!'VF@H!uP9g9\<Qah1r@'^@SHqZJ-T!c'Y7)FdFBJZ3Q
%6Rg,M.qM&FNX>DDl(8%c-j!o:UDag,<M`lJZsg47gqQ]0(sH7'l+nT^$JB3D9_;Y/Z,<7jX[&j`4c5*n3OPD8_Q@FU"NT,'HFr.<
%Uj9O-D$\Y,ToR*#@PdWL*Nm"RTQ*a84uKLq;9_0JTEcTsGFPc2hF,es8@_GB>ie8FpsF<s5UrlAJ&O#M0"P9;hi]-u(Mbtd`X-[=
%/k!aA0h;^s\DbF;''M4LB4g;IasK^Uh5[S!<\1+r)DVZY"_Q1\igR;ZVc\9+RfZj2Ia6tlVb@#NH5XXsZURt%\-9bBacF-rj1"%K
%Sbs'$;R=F)G4Q%6PCXe*Off!!\Yd*>+\[3r+GSY=*SX=4;2sbAm,p?;Lu\UDr&Xs2Q%ZE,Nh6?c-\U"pg7&s.OfV7dW*P8Y0C1K'
%>6Gc1EqYba%pf=/=X#WZEJ\CbY^"R]j,M"hKC*&G#l>i!-PrV=)6Oi>6FCq0+Eui+H@`qSCJ(t.7_oil)Z*`gabu`T^6*<am04ds
%^?*C1Oc6&CEn=sLI&\j.M-*)!$(I)!,F"d#k+JQRM)Ph=*EJL4K?uHF/,+!";T6rG]9\FoJk)u@AKLrKX4=XDS5l3\F$kXZUb7uU
%Mf^3Iqp3qZqDkadk/>Vn_b&FPLnBto6Hn+4?L.DF`R6gn/H1GkjtQq^5L^1&JAI5U8)2c#eT7$6!icpf.=_md,t^Ggbs!lS(=Z8n
%S1;KsNNuaS&PuHg.O>I_82X-I_5DS`LaYAAli:p:<?q4ciWF@WXLTV:\_9?])Y-*`Y8T#Nc.<sNloS.dghoH)dVej\(eqDp5hV'Y
%8liS;;$0reaPZ_G#h&W<HF+9nhcT/;Q;Nog$5OZr_U5Me8%r5,8T&_c^1&GU,N?Ppgl?d[0cQ8sp_<c'3KK5Nm*[nQo%%NV.n7U/
%"O,1Q]L,1U<&s9.&HdiKNgd(.+YJ5H6tc(CmcH1F2C!W1/-(f#)FJ=^G>rrlMc[=t+ktZ;3H5QU*63N`m(mDfhZG#VP>CA<?09;D
%Z/Vff@CQcRLr&0/ZR^kX#K%hY7atffq*ZJ=J6UfhjKGdpfP$T4@"G?gq6TtkJOYrDBs0B)d%'mM92L$'c/!JJA$\`3f.m%;\RfsV
%]eT-(BrAmchJT1W=RW.VX_b=JP%pCOeA19HVa:Y7#O'Qs0@GUkkLl:flRAIl,>*/4KuhG-S)/53*?Y%@pgdpR:6tl7(a+RugD9im
%6#mb)dIID^rX`H^+R?RAC_T<J*d@OFJRi*7m#@2l\k[IR54kTFd,!cM&;5GQC6+9h9+-;Skk]@&%-ke3CiA.n\<^#9jdp6o/Ss1m
%U_hM1n2=<:#Ydaho#l93;ut*-iY)SB<XVl`c4BXK't1I-ipRogNJlE:!ed7lK^D/Z#\ZZd`]IM.kAWW1N9lBL]KrPfSf5r8ET%XM
%A=KG'R(ni6p1]qK6cuY*&;-sM\\P+R4r)?Y!?_1+p!R-!A=*K,Y\HRp"a=g2eF9?i@L2SPh1Rk69(D)#H<1K@]sT\gO$/3+PcD]N
%Zg,R>dU:*51GZg8LX7b-+f)%Fj(T:OEPl>!1MCWD$ePSf?J[mtptSOFhIn#7Ch>3hZ#6X4i6IcV%u@\0+TUklJ03R'p;GPi1q>J(
%88>`:SUTj%dP7TTZi559%\oDmRd+NXP4Tq&?W,*(m&1[^a8&^ZRp/1,-sYbJ!M0JEOHb(($eRq$^h8<>:%WY&^:H0kG^N&TNDMts
%[aS"c'XdL&T[uJiG5loR>h"XdFgg)t<STW/ba*lb-:bPo/(@R>b/6hT_HRAGMia0Z61OcLZ^gQ$.un^iP2R,12RhGT7:h)WQoPV9
%+O\$FR'<]%)I^\5W#5"R5t$4@A?M(B+%tCuN5<3H/"6Tm>9">;jANr)4"Xfm=(2lY9g;5+I&Tt8'04#SE9+(-Zq^("I?>l%Q?Fr<
%dij<R\"NU^cj5?DQ:#fsm:EY7$fdd$)g,@%(G`a4,Kld>n0'/RD>s''NuC.KPK$tk6[nsY1HYXuMT5s!)9?][iB\Z;EoZ;A=m-3S
%Mkj2c8`CuN14t?]-a-XiFRAt05KZ3F"tfR9X6Q#%igTF#RR%ZpS5l+L8<'^Y6lqB*M:d51e-\Zb60sV:ON>_?e1>W*]Hp@Q_Id@C
%<pa?s^db>9P(Rac;K>[^c#?Aiq>df.*r.L]/iJ(g15dFbIjoFX"hfK]\gLb-D.O@\EW*7=UQWZ\!>A_=Q7>^AZ(bT\r/f4rEnl,i
%<3Qa-"kV%W6lT'%:/R;G)R/b3ehM-j\np"TTio20;'HsnH@X`l(DuKGr'!)hISdq^qCfCmN&GUB?Nr:HbqI!+Omb,#cCiArT:AZM
%B'*4uJp>[--g-T;C>@:_LHUiBb%=^68-9sGIFaajlrtk#*HU;)]KNHC&CB]AGnolKjM]P:9hXd_4?d1!g5Z?KFH]M.a-CC3a^db/
%mL.,gM<!R"ZULN).PEM.nQheVLRG`3@oY'bK7Yin[OJ<]rVL:tKu\(&j=fkMn%?X`>&?1u=pXm[PT+HO;%kr\5nhu.\HMJ9VV1J-
%9fXJ<bKq?bo-Eg\#9nYUcK#X=EujT)R4"bd]U$YtX36Ck$bs8j8-8h(o;r/?D2F2lansIi\*.39Z$07J9G@_^=PH-hA="!\PaQJW
%2uCLjpiQJ'F?>LcE:*34VqHU@ob0%0,ba*U5=-6C_$"6$GO2o?/Hr(N)"PU;0TjcJN->cKXT`ObebmAnX5l"ZH;[3^95MFg4[>!"
%N8f3hkhP)#,dFWN,gdL7V2se/+b*%iVXc3#4PH_3`760S[fZd=U[YZ(`4\ai^rdVfV9%;eVeP-JeG3(s`EK"a%mLq7GPhkDZC9:E
%ZQ'j&=Y-f;]=U"n?7d=Te@+_sn:E,#[@OR?g._j-4J`G\9tfLf$TilM[pKW9g0Gu9/E<;:2)+BDN8o+g@HLnZ)Q2?W8-H&Kh!&rc
%0No'ZAFQg,9t&HP$[UHP/88Z`-Qss32gF^E_5ABMWiX8Y\fF<L4@\ON]]%@:PNZL@;YIE$E*20b9uoZ#CucThS=c;CED34G'.;>B
%FSf297W.uUY,JVC2`fT>i-p0FQhe;\?+``%NaOG:DWq?;SW%Tq/eNZt,AYIZnuH^,;qO+BJ->S7j-$5BPjZkG&IDd#bJ?^h_Uk12
%IW.Eerp5(3\;9pL_qsM,/U!p=Gs%fF83=%5H=C;']<poYjWikjR:I@Ij;IGEhLS`j-R.3Gn6ueK0k4L(;4L*f"Ym\4Hn')Yi<<M%
%T^07@(W=6F^6sj`pl7[Unb`%VYsKd92-^Qd"=]kpaLQ,?-cSBGa+ks1V;36NcbMW:3K:!3Xnqc_:@@(@cL?RhI'*7]Ale.>if^Q9
%%`t3mPAn(mC*_?iin,gCA4BhHm'm](AD4_];s-Vmm@H$h@s^`oN0b@l)VnJ]-WIAD]VXeF<Wmg@Kf4$,h2cF$U=d]MB(bL3@"YV(
%;[dU90&h/qZ;dF,bkgO$].:$fU,mQ.?7OY"+>qb.NqIp^&1Fo.\m!;kXY>P1SEI,n0Ds*UDB,r6^jrYi6_>ff*LP;u,&pN$:0=A#
%&BqA+K(7".)p*p\:iH0l=\<GmmLgto[a3m8@$J2JR!&I'lqaC!EnMlAHOAMl^m7*g5(e08`KotN>?S^>o&s0d8kWc')>a[/#:hN?
%3%7h`9TcY1aR]NL=Z+okZGS=e+p/Y;BJ'*kT[pV?kg[NO9Fn.+li$rW&-%P9Ijh/)XpFRZV:Es;H%MAZ4p(khXEW_V#Y0V,g6V;@
%[AGJ+ljjaJ5'X6\WE'CVLo/?m^8-Q[YcQss=0fo^]M1.&gsU,m>rrYCj5RBD.QgB^@,XrOkG:+0XL4"DnQkc!_ELsY7TkL@IQBcT
%rF*9MQOIUj)-fuaj1,FQlJY4sD13bS!BI@AO?\;Yr)mM"UV3N+LJ$:o9-u@h^@d<$9i^6)`.AZNI#f?a!Q>PBlMWL%0_;#Rkn+N=
%>5lT=US?ApD2VHHWX@Fa#/Xji<]!*9e(YGA%m3sK*j^8Y5<?L-0o'k_+t0rNY,e#J!9l!b%ctH-.5UO&\?"rr3^#m"N0KA[n5tV!
%H^,=2$Nth:,aD5bHChK1)2<a)R]YJk&W!Bd!*$]-$L3brfX(uHb2eUsp7FVD$4b(/!\pS;kj2"P;dI54j5V"KHn6N('2DBU,2sVi
%b?3Pd:Cc?PRV^`K*fQqr"Xp&ITfOqdPblL1Jj&]Z2\Kpt8-fcWbBUb%j*7_7ac]\N,T3bj'_;EhM2[+1O)^8$+X;%ON$3"a.LF^N
%<,a8fT6f,Q$.<7=q_]W#NR>c3qM9Y/Y:IoC,QUZj*"b/N'!KsZTY`468UL_VFqLCbed-`Gg(Jde#1G_p6ZG.&\sdbIY8_+MGRcI^
%66T`JXp5cT@OQ==WCmjnmh(d/"MHF0bA[:kf?)W8A5QQ6,E)1!7^3$4kWXiAUqH^"P'H)D^\D_sr8UR;r'15E^]+!-p4pE\q`k&P
%s8MnfSq$=Os7;#\qu?ZQ^]%SiqQ'Yd[t">5J,Rh<roYAPrkm(^ru_76&-(sFr8jRMlGoOrpih5f[t!lipP6NYmsjr,J'f*_qXKVr
%s#5Gu^OP,!Du\83rqZT[%tF+6s6u@>q+4G3q1[qFr:XL,?iCFTh>H=5T`=R)1`X1.pqIZ\r"g#qj)=t:J,-!Tj1kT<f>%1Bhu;H(
%+e/>#+8OgG8,bt]]9+bPl9Jh35\Y(Ss44]O<4=J3[(;CWA2g_iLM-FY/(p%Obu(6_7ktj%dm)=Fg`#K,UDU?3Tjlf`G)h6WE#1M'
%SP`f*>Rq%_T%#0MS\%bt/$k]OQ?SXTlW$;EVcb2L`a^@L?Y4DBK!)*cOnMm,i$b/a@DYQ%qK*So)F9\aJ2RDJ]?,[nGm<Sj!tBNd
%4O-F5+1MsL6t8u$PU]PYJuWqb7+$F6Rp1do.74hf0IB!?nl8_XQ\6@rU^`@SAjgd!(SiJBKaCV_9l2aO0QeT.Gs*Ra5Y@bF='W]L
%ScMdN26@;Z:st=YMOE0>d:^VuCaH$[U'lSdQj<nL2^@FY"C3Ho'!X/28]:sS6B=If$F2KT6kCinQD?%#fKf]DWQn_aX<]HBOod9*
%j%LaA"g&h0O>aNr'.B[SYIAf1(rA=q#Lnsm&rLIAlXne`'KZU;Xo_#p0KSUn*I])RE>B^(Y3Jpf`=+ihQ*V,?4cBt,D[?l"A.(Xn
%)c8d1N,*5o03`F-eE5`I\fht#];cRL:URc,/0se@C-R?1gYo`1n*BiNj#YEL.qgQcJdZ'/-XL4[r!$XD\*O^beSMuR4Z:Up[15/&
%Rsn3\(fRE[,CH+2Tc&Htp[(+U2cu"lV71W4b%o4&Z>q]LHXrrMSla/kIh(&Ij#3n3@K'rCV(4_YXLkm1IVg5NaYbR-"K^!l<7F39
%7.(=KYt[b#/:E99N-8D.E7cJ2>rr`/;?7hOKHT?3^R"E60tRR)eG[4qie0*L?>=Gd<+Rm?cOs\QAP1[3Ls3s+js+D?RM&MUCu'jY
%:$Y1e/2`/4"KFY$MS+)(@O*6-Gb8`85QMj1*<R$R@]g&oBLI$&Y/P5=6"hMFImS/inf]X>JeA.",OdGqI)Q$B9Z[A6H"!YKP#b9*
%N?CG8`9AmfWcPPn%uH3sZoN7-NR5WRA(>QKY4*"@F[Q:1!RksJs%(,/=FpoTUjT]6a="u9bq'4hBXQU"6f>[&kM>3nI(%Hj79d@U
%\:@!V5TG@e3A1L97+X5pP<r->p$3YT)HTWT'b-P/69osF2Bi8YPO-UCqC1dr,=9neb&[9pg7O<j@PT8C/.i!6BEi1EW^=AM]eP%H
%mNrC^DgEo5cD$HokAjNtrt")`J"Q?.)]sE6W0C0VJ-lc7ge/!F]#R$sL1p^_.FXM69ad]TK%=aJO731E^Ck^pO2>dZCE[t!r7FFS
%(mdMI\:aKH6r:4e!Z7Za1^>6tdDN=gO\u44'_Q)foi+s38@U54";WLjIppf)n't*TbubD)'?lRZSIF_.,,UXVL_>5r.XG>XN?968
%c4odThMfU:`Z.+NkQc*&DW*O4Gd3MK&qioXEM)/*Z_H;m-nQ[lAaJ-8*!$U\2+Zq'o^q)LGYWC'j?Z=^0iME"hBc;S]U,L'iVZKf
%i^>dBK(u-=1.:eH7#YfW*b09\67LHHIsB?soX2ek%et`)nIG*?o8g.H6.`=J7+/SpF.D'O-apPq7g&.&R]ka:9MYVha7$EO3nHN;
%AUq<'F^YU(Gq*mWI06m=3qna>cN5cs0KU.):n-Yq5D1Q&q'%QS=ABphA[SV@q,B,%dL%'eIuET0dcBd.#WBOgU?58u+"-*,((=Eo
%<^22XT_C_TKrY@p&H7o/DeM//&?&sp$<U'Ol`)?@apr$;kQ+f$CIR!j<Mr!q'_dKqF@%[3D,0HQ[t"sY59\.qa*qBA^$?FX[f14:
%h/E1ujQ@6_"k)S"[KOs)^:ErLb3JNn\qk@hp2>ri4JqHiiE#Lb_5/u.+Bo72"fT\LC!rV$*:=:nX`dAs0n!?'8&ZfS"6it1;fH3W
%[`T+soAf!8dcmWD!6RZ<ET+>gE`<(VlgY_T%CE-8U<n<Bi^BQ1JFqrsFKoG`BM.@moNX^Jcm\SUWJE;7kiP[5;5[p6>rZE:GWVS!
%9AS5E1HkiGXA"ZcPD"Xc"_cN;jo5b4\4FVPn!]Pn.I.u4M$CETO8IK*A#52o/9?PccJrW3ro_mXM^I.?JnIN[8GG[EO8Iec'58LM
%MMUotIYoM_m]t05Vup-4Bh)*nA44aUo`([a=E69`ONF&f&X)O;R2.A&U:8%G6#Pol'<MCr^G;4`b\kC1Jl_-%Qn@n?l6I`bDoLR)
%E$eZ$aN$U1i@q<h$Um217@68*8V2SlfB?i)_'m/ZfU';uJIB^e*k-hY75ms:H?GR^XnrT_D4f'<<1NBY7X6F1=n_@!S>YMkA8mpH
%iI!a`UGKuV/0kpi-5AKg#`o<jS8Hh5,e\RHKPW"JD1ehuF^!(NVrPpO1lbJ?eiuZ=.8KSY"oC?&7?rU7c5(fE9PEMn=sU^11#jKb
%'=h1$6K2DkP>egIALc,:*!5BXlb<Rl(X92Q*G!ds%Ym=A=AGM!TDJ):-Bq+rXjkjEd.-.bgq)\]C?:c>N>a!R,koaC>HCYE!mG#7
%-3I02a_6]-I>U2O=>*7TIu(b`\0g+W-&h:rJ6VQR)GBe#ZNUi[h4u_bP_-O@<n+Lr(HXMPV0PHc01@daF:5929o\7pj=^fr6$hDb
%H<eo^$.MV6c63k5&g[OZ6>[b4g$Y!0bC,4XOYJIYhImY-PsaEU6EEVJ;W)<SUs0FG/6tqXiCU"H<(:'n)G7/D]/WkD&5(*"<!7c9
%lYbC]6>r%&q&Aa<R>PS77]TN#]L?t@mu08j@O3uUQ$knKE2D)C6KS^Uqm;]]]HHk=b6b(Q)FoSiL9enALf2NW6,f+tD*^nK9hMc'
%X3PhU8Hp\E\RGh2)DE$8V,:J1POR4WB92aX=mp^DrrDRX)7C-#6X'X`i+i`T(Xr2:l_3hS>_'Tn9oF<#`]c0;Er-ckQ0Ss>Ed%AJ
%o%k!4S+VPT7Uc=Cq1F0TQA"E$g=_L#(.6--2:3!qcdDah(h`K"1R7B""`EV8l]DA:>ai8\WABBje`,G:24?&jT*kgr6bePdkW_P%
%g%]Ln`[M#n]&sp=hPrq+ZQ/!XXXbph:!HF,%B?$A%2@+l3IWYEIK^C>5nJZ;U,ZYUSgbG0:*ou>pt=6-3aHF3-h:_7RCU-_7mQ3/
%3tYbBio39*X&E#bI?W*o/j6QXf^+c/3j`*^@>Ko,F%3l>GT8V:#)o6D6WPl"&+E.N4qu&]IorXXTE^,iY[ecie]B[8m!W&UD(>pL
%MhVS<9Id%*/UZ-T]/uJ??YVUP`f:l9d,&Wt7hma$Za:,p?mJ)X;n(nJcRPAZ`Gq([Am<NGBH9"dI1c6b7lrgh_@COc#u0*VTUnss
%pJOm-W/;A03EPRo_O_TTfQHYVZ_<dA"TTCUg`c\Lm=R`G%47M[4KkFQ7>D14BqkIlg@=@Ic)$[%>qp0f(S]"tdk.f)h[=(ZPk@#Q
%<h6#mYjT7`:K/R]qKN3eRf]0aG3Qa@eWL5l=1)n7h&9r\((EA+aShQYeEkN\_Bp'JIpDQe(n\08TI%aG<6BAM0):uM2GZ:hh"30O
%qN`C:!?[%l8E5!gp.@/A"&`@!a9pK7O[-cc78_$5_A)8SaS"Vfhd-^F'<;6\eR=f';*dMmMX5u)m11(PfT-$F)cWGek!Fq-`bFrG
%pDW?pd>IDNW&fOF`,;[oJC34`]YHM1(mU^57WPh\K]8lq9=C0!O%Zqf[ZHSY7?&CPh-\EOC"e9NVA<UHC2d`mO%$p/U;pA>H=<==
%j^<9/p.LM-d$'(d`U+`Pbs+DGo&h/K^`Sq0!GA=>r'!P?8'^1YS/0<&m#Jh+PLEQbVoP;6Ojl$\l]Q13BgH(QKsALDoFaEg-eti*
%=Q_':f]>p_@2K<aNHa]cNk;s3j<5Q\F&^e?;ekg[\#OW$:R,cRDK^![$i(*=QY7d*HV;AR'7g]jLA^EQ1(M+XP@pJ,O7r_\c(g@8
%+S='l2g"MM3ga3Y$S]#&Ns,SAfshEM1q"R#?q/RP(eaMeJG.\(+#ncSCLb-Wl_e+!Gd+SQUlW`YMCaNfpYi`j:o8]hQh@>urW=R#
%9i^cKI7li1<Mdl*=bNfqRO<pEl0(e;(r1_bC>?WT=olFiQ/Y9F_A^8E;I7H$<C/rm:4D.;3-dImb`%#`Nc.^WlI85`X1m-%'bQca
%RlOJ3L#8W<$E'`OBo3AN-1)'\F6"G*%S'q#g]rm6Q.T6u9cY]3dA&3ppFFqL4I;pPrYT-<)P%]n`Q99LDLK1F64[NXA1^6lcaj+u
%<Iq8Pel=omAY"lO+8kcRL\,=qLG'jTdaGMWe]Z#.3,7a4L]87kcsq"1,)@<5BkP3OBW"S\HB51ag.lNNck\d^?cd9ONL?T]h]rB,
%Cck;R)$ckVHF:7)4*CDd_M\AD7WQu*82;b9WQi3rYEjR"]b^M#"LuNdcjDOD8.Ngf7H4P]2^L>RXXZ5[Sb7@K`Nu`[r!\6rHSMU_
%J,_%&s*H->K+"Ii7m3qdeqHCO/*J"q8>'(P/TiTrUK;s!=iiuG11)W=DZErYaGZTj`euM^eOp^+=`2LD(!0![`Qj*O1W6#=F')KU
%0Nf`&JCA=O>$MbNe:ZYg151PD>S$>RF,4`6R,lT+#oK>X-M.L?;<J$B;8r5&d[S@h@_b6?PnO7e9^;0cDQh)<B&K`ra1'*s(^""g
%[nHK!P$gFpo90%^`.@?oGcmu^LMP%b.4nL6kghFFMJMgY\q9cVBa2d`mhMg7a[cu4,EE34P^r\eA/%LG[$1[>J^gJJK/,gs-&A^F
%<sus$1A"6%qOL'_WN2'_=IHYAGZscCrH^pVAa0mF]iOKYRgHU%Jh>i8!M,0c?fC=#@)"sY:1Z&0f8R-%Q3+jtZLq/<6um3]rqEJf
%2:Z'PO!W61$I'DlG#;1]%hW2iKBDp+k;_A/6SlCXnjj?!;0XqTZ@($U?NB!u+h"t"KV$"MiHHZrc40A9\VjP[P41L+&W+edGY7@)
%r)il?r%c-]<%?ZS[AL1tP=pGNqJ'LjqMNAUhs6"U])j6*3U1joGF6jp!XBZgY1)=B)mIBf4i>b\!8Fh#+._6S7Ci87p)#PLh]+Pa
%*^Pce74@mtLR:4W>"S(?<7_Od:94e03`JI(6E>C3EA;-\?lnSU?+K8k`el$i=rq``r9dH:i6C:@,Jhak8_,g`Q.pZEi7'sg$=q2'
%N(_S/\WdEJII(\RpU!b!j._b\XtMD=;Yo0_EP3oZkRN+1qI'$;qK>(Jb!M7mA4_KF_(_JIUP57_qh8MWh"_gZi."VnC9G[#Gor(Z
%\Ae7+TgNq>\WF'iL6$;CQB)-D05DD;0XS[!2HlQ9GFk6T32iXNBI"T#s$!CNr9p\NHY<p1F<"O20"_lm?==OB(QD4@)^D",X>cX>
%nhi)H#HjL40at;79D96D83B-Y-tBN_Cg-@6T13o?GIGjaL/ALDHq,O&4i.uEQ61M_"^NPdEP4DJ3O+frpp1+S5;9qALSb8pM&M]$
%0sO&>Sq(BFfha9`Sg&oR_W=\Z-YRI7E+`jJ\>V4cGg?8P\t1c94h1<j$[a4"A*+shc?]l`.W\CHqRI\KGHe\\M5%W/15p!;b<p:[
%[g--n.+_rZm+ZFo;:/OS.<!?uq!4+US%b=nJnXd=SOZgH"f()4L'Z%K??XG*i,%d*g4"l(Hr"B,#7sW9kH5l:]JgQm_oT8ohO?0(
%6R=n<(id:I)F%O4/@Vdh"9+Ne$4&,2/%&VT.,CgVM[04qE4-(HUK<]`(iE'f4T>*1M8%5:A'lRQ.]0GFmB+X2U_5:Tk%=0?e?-o8
%"j7q4V+ug]Lp`/4!FA*^Ai'7ufdiueC\IT13PKS(>g"6mFL_'SUbC]X=^Q!l>L;ba"ABm!eE&Mhd[CU+HisN.a1EKeCeEo'/0eba
%O]QL%b^K^?F[GjDp3<Lo`^pRm\IbY/J<&UAe&>O]qF*Ee/#A[si.K[U5h\BHnn6\]UPY?PEOo"C=F:d_k%]"k<mBl="HJ/&GQ-7+
%ksPg@fHrK.8:P][K3pTZG7.f_lIAGXrYMHZlI;^K$EMV6-!^a'q!joL^QWN`r'%1FLdpd7gLDJCI!Y1!jLt74:l)V)<l:(cNf5HF
%>dIhPJVR?ZcW((IN7*Sk/.cO5)@0ea`7r<<Mh4$L1$XQoX]aKlNJM'W7_5=M>ZeCZ'6t;-6($]R"+a@Ga2&-ld'b7\R2^B^PEFcB
%[g1mT21<mYp:=u.i\*]?9C%-3\TMN$)EuD#,O8=@Fs-n2MFp^%[!gaK!<&Z7Gru+ZfN'6iC)B`VH2nm2PC$Ar@dH'r]6oUre-JPK
%VokC!(-L4LUUu?g@I2)a[_De@=2F9J's@Qe%X$h%^'0W&T+CV,d,=GaLd-Z_LHNg*]i3=HN9*<VY_Po\^5NQT@so,kLU<ni]>le/
%i,@NS/l$b1PL79=El&e^#lb?C?;aVJaF",MZX>"B`YEM?8BDp:.5=>)^_$j0!A]=J#ct$J;fn=^Zb*8f\ap.(n,1MDE=GU<=frJQ
%K-Y:NKn3co/@0579joDU;.EX`4ebfpo@apM&m@l,-kB^V=lO-T7s\>(;!tAZ,M2.U(iucKQ9YP1d%?JG%92JdH&oVL#`4\@SC/k(
%*_fuR\P?a>M&T_h7hKKDFW./f3H>n,aet#'^9JW?YTfn#gnuAQ[cBp;T9K%('#(GNn7c#HG;?bO"W+>?WI+sI<l1=!X-a?j?GRHp
%HF/lE%Ph@De=d!ol3cf18C%C/26P8#9*8,WII6mIZc@_(!EZd%INLFR[57rNB<iWEEEIUIZp@!>Gt+IiC17rU('sHBX+kguM5@jB
%:,%1u'XVTt7u;T`!+!N/df1h494-1FZl5Ok);%L=0"&S7f,a48_l:+i&hpHa)l1KNbJ)JjdP'QFNfSCgp)X9*OWSW[9#u2ga/Wj$
%S-Z0KP3>*-=6_?@pSrW$1u;L>@(u*$H#D'CV^Q992F,;SBllm=9HS=[3"Hm:0l/&LC<'dtCnC1t-mJXuBf1aZ!7JBQNsh"Q\)"DT
%Z"*q!]0_B[0aVCY)*a.B2POdkQ3D'%&$*Ek"Jer''R!m+L%[?^Z5R\d!jju?`o)?+d_IT;d#*LPA^qqt!Y403T$M*#9C(-j3d>h]
%s1Fc_#Yc-n!67R#XDR*HJge[bSm>U--);r5(YYN0%([dIJrO]s]VF[s@cURGnb/Z0Sn`1<pB-#a/pbU0QUWdaO@LF-#t@D9PcK:A
%k`M_cU)fMi@I-Yo5q8;qboD:?`)Q#2.J*:J5Q(!Rj5/`\"G5;P?Bb@!B_*WP%f*;hC(rsrdX8K:D#@CM@oNE\IuE4hW^a1*(PR:$
%cA@EiC7k^$_l.bV=PQ.q;OE$QP(VI5>W.,s&PSaRhi_1P`]-:1\/$af0u/>Zg\f(#j>=*8c+08ADOA0+H*#],YtsTH[42p/SDQO*
%H[MWT"09b8T2Q^jq"?1Do@qT8rG?\iBq`$YD_F%GnmmP8Sne%rCtd+I*dBru@m`L`rEGpj"Ja"3V,Mmm*%hlFVS:%8%9+&GRYLF#
%fIp<#!E9K?P*<m4;PPs(-<o]0@)ji[oK3<NnBn@C&lf'9O)=6D&f_!&;$+@3]Q=^`:(5EsM([iB.lDFKat4ZcBYQZ%`0*T?Z<N6[
%%oDp-=56P3MT!q2:$e\'`3E>8D?t"=1X,"N?W)\0h"&s#d3LG55dt\lF]>#<G5K-o;_Q^cKdN%NGT*J$,jd)rLRIIkd44;:WL9d)
%,.CTp\sNu[Y>>CTijr:Q`8r.#+R$gPfC6C7%GDZek8]:D*VO@b!':ItCpj(.=lQQXOfbqb);M6C%,&AbeXpu0oj`/VlF*8mg[Pce
%]N>"*SM*f8Y&)ZD-3Ji3a"rgrK;.8l>3nt.qj2G<\S7J(L7Cj790@&>Qs)`B'Igmrol]R)?;8Z,;Kj_(f0N9PhD[Dg[@d0h(92ia
%s1XihOI2H1`fumA']LG7YRkmokB.3!*)1:/)m3BS$#o76Tu-pj+slB<6pJBk1<hG2L#h8eM:;sbAS>L#"=:8)1UP3k[W.3u<4-]4
%9?KC3;Ak3*hp)(XG&bVq+6E`Yj[cJaFK7o\gh%p</SQF9NMN6Oj0O&`9o-N=,Sk`u`8LF*`JF.MG4::E+tnD$Zeu4DKBK#X1%0_!
%3B3r\9mjPN2T?_tZfD?tp_")!-qQdU^W])4:WjM/5RGYTf6ZW@W3_T5PY-=mkPlp<U$7*J$mX[H[R'.;[O8F#;-Lnu6U>LJ:fhYK
%RZnQZk?(1+P`'eh292;`f(Hb0.W:e5bo)U7Y7sA1YAkS;-[WLdP5s&%Q1ki*q+k^+>6u:H5!=X3AoW\#)5ukEl$;[<OS*D/i)ap[
%<6q0=ri7E9msONdJD4]&cOo=0""]!]peq@#6UV%6as-cYn#JhRcUVR=04QaeLk5Oo<(<\TrYJEL%:kb(nUE9C9n%3aWV9N0r"fa=
%a+&cnq#=Sihpk'i#3H5Clt2oNjsr+n$hFM)[&?b1M#dchE/a&I^_JVJI27b;D@k-i!'!,2q6KK<N-q1%,_Rum,*&=&*Y_kQAG_*+
%(o#g)AM!W?dVIMZ/a5[I;$ZJ#'1)JA<#K6TTF#Dd@b)Ek:(fDoLEo9mW[kM'FEPJkEh;Sh?^WTLWOnq[p,Hjt!D[4Eg*^%bo`.s9
%s*Y+FaoMQTV[bu1CK)+6;4<_'7Ru]>EpX=-$$BV\XEuT39D7V9@=ne%GHqVd,84)q\MAQ-rN$T6J2<p\Q`33)hm^.'QdtpPJNu'd
%*G[YX6riR]j/hcc&clEfn/+.e&2KA=XNR:q\iu.HThDos'<-E]5/Z./ZE7`tDYK_Ygn]A#:FB*R5:6cofZu\0.YhrGN^9V!e=N^R
%7EZe$FK:,)0-E!]8qisK#)VG>XM;g%c%lN`KC6^)1.5)>$^$>Ma9qgVl1?."g#mg4Fk*Co=o'"u`N/9'*J"6do0/T5=@7X2L4)g@
%K+[jbE9cC6CLb\mmnTuPK`t[[A3\)u0HCoO&Ld91RWC?qCTC1Lk=E#J=bEEt6R[2'$JI&`1_+[A0@hf9hllD_>l(ce5[S`Sq';n"
%&$7t#eF`@eKBuskl2!>jUOoYC.eFj97A<Yk!b?s0k,K^b`XS^-=K,:I1&3!,/QY<?gWs7Th=h`Y3frnK7Oc0;]0j-ZK@i2r)D79o
%*Tr@re_d7(%4R]lnpH6RitSb!\Y;1A-%h;VR^SWQc9"?2K0K]G$lljk37+G?4*g418*c.]d8<Xp1BE"-RRO5R-9@e>WOG+cnok%L
%j5i09q-&XCT:n"^QLGpTI0XL6El%/"<7[WFT#0,bgE3.^VQHq9aTirT,\Pc$"Os@W5BUPQ)#Zgp?!cA-jRLO<qBRF5lsl@Z&Cf"a
%O5J52B16g2UMf`^9C%Lk/Y=0oq90)k(c6W:$5ohdIg9g+nH$?!a'J)II'@0d4#qi/4o>k!U`<c@$W-OOUP%cA6CH71p7,^]b"ENa
%m+1ob_`i>b!%fP3KeL.2$`TDRkMLESqm+'G^+h-X$,7U,kU-8bLfLm<;]tLoL64(1>%P>&GEkboJP5Q;=o8EM%q=,dP%HaXZ;_V3
%#k3GQdOe>"G9GUDl&p'rR2,GBZKShsN2^d*]APYB\9='YjRf)V+LPMLDJ)f3h;6%(!I8lFr1]'u:YCmX?NUPYNF)YY2F1LG8:1U/
%eAj;pO4,f;/P,5onm@hKRmq>sl?]3'H%hm-hHL<+Ja7lB-)!mi,Siohr=21+Z`c*8..I&XY#sT^<8@_-=De18:ni,T)RoIu/,)eS
%r$c>]ael`&DpIqBr,Mk^^PJ`-<t:d)hG0&16?WZs!`t?[E;N/hT+/&@2*,=r617Amd%JX1r9p]YE>i"%GY5eSL4folh@D';Tpn!,
%.tn>[>=1RjSf:MK,iVKnTTd%i1GO\ta0YJ1FuRc&Wr%X_/d^<OG:#naXfr_laC?[A@.\k9T!fXigfcV^MG7)-An5#.+tZ5',]j5V
%Tp4tq?>*dB7>f)skmh5dM99`^rfV@.[U7q^Yc/.oK0uT<lAj1D<4tL`?O*U4;Kp/)FniF:!<7r3QW1*AJ\-SMKr*TRL1c]"\C?be
%N'6Ns.g/ZS8\d1A'r8EB,s>431_[`^+R,s+3D04u@Xr:!Q>q9%9QdMl-q4@s=jHV!_@AYS<b8X?Dp95s'AqD8s8UkDlQ&8'Q0]l_
%Nb[#*f+kF&]pb^O8r$@1;W+J?1#[%$"?saMMO,j)9AH(NrA@=A>,fcdgK$_kqrA#j`RlO3U_&ZT9lI;,e;_:%B!U`kSoLi%nQNE3
%@'Ob<D)R]rKnj6ReF/,9AST*!9cINDR``X7lg^8pXV#`F:caAk25r&pS6u3hg!/7<9+h_<dF+cKDV*_o+1t-,)42A0?)Ps;]#02X
%ig#;D3T`gX6J&,0a&he`9gPk#L/Id?h:4RU/;F]jeJ8]lfRnGa7@Tbb.!'_35*#4?@uJ+0",5W%[#sqlUdEML%Zrj\OR3lbEbd^W
%,E2WA=o;Pkqn@'^5T<&bdSf-glZT6;36@_979J+4jiN:fEBA+@W<(';eX]Spf&m*(SJ9Nu<"pT!;NK9*"$mK&cllNO01RK8:_.mU
%e2gUT7E;<^Afisa&j@Qn,(Bn_9*sMYRl-t3\Y3t+_J<:U\pNY>\\lrFo:S$AH\VY[Zm4b67<M]u&KNJ&oN427InV-\=NFB)A>q*3
%RpZ_6);<djNC?FhE8I\^$f_X;NE'=;37+<-Q2tu1Ci'^`H$VCse</6Z,7PFofLg76N@,i_,OJ3/]eBe::1Ii]'XH9/ll8a)Qi+[N
%*St:9*X'qY2@CGZ:rt`ar3dl_9R4t3eM\q)l^C7O)Ecu5/`Ad7APC_^RKB+S2"]DQJWFL*@-M:"]!EtAV+Fh0%FrIh&n@"?V<n*>
%s$h'XK2;rd=34'G%gjEj3L\QnN(4.c#],<k-6NRBYpkWJ<N=5P7k#&OlO[!W5K6HFme@[nk8-@:h.KGZA$g'#Cf;=ABcG(SNQS*i
%2?%llltg=8-6Y)@WSl3YN>t01F/T%"X-N?B:Pf/r]`/6PU[N4a^MuVl&OLGaJ7Ml;5O@$5UCIsF3gVn,C8QrCH1RgDh8c5?E?pN`
%/2=+h]5oVbfYgPk,?6^F"<t2MK.-06bgX7[j=R#rP#-b&,P";@Xb5A1X6:nJT<mSa9E$G5-/@;W3Fa3qd/Y=&9\;6hD<QOX2gA9Z
%JX'-g)dg4\K&\1^1eZ'\&Pb#2!()iP'!OG#UD0ZfRqD^;8XCU?%QqXna<<65,8)B.X8S','N8la@9^]?OpQC:dOqaGC:C^J6J!)u
%1+VZ&e<WM_oUB,`Zd:[%,hHF*59.5KmdP.Ms8MNdJiZ2#dp4(R12F*-)D+%DUd)iR0LXk=mi%7pDbeoD$'3Ee`0-C,)fu:K.ZM!H
%dP=\[n];dg!kt"1_ORghQ!b:her1=T5[7LYp&ZI0M+25TUt8<j1p0<Kb%'7jJX>[_Uogn*-'s/Ln2fAuS<;T(;NYa"C7JKNSS.:E
%MjC-12kW@iOS@E*`(.]tS2YOk_T]X)#<>Ba.TEp.K_NS>)Bp#j-od%$G$e\-H6N-5S%4CLj3qL[`@)Rg:#nE7q?VT1QflS:SiH"G
%cXU8_bhJN^T4MBPKh<?G85KtiGUt%+gIJeTWWuJ<jq&?!.2+Vho(rOb>,F]6&Y`;VQl`5;G9Hms82$8A9,^)HW67r6&Op;Eil=eb
%F^a8-bAfmp(H$-9d-QG0F^Xa99h9OK\ih&Za$]64>=t\4JIlPeEL>.So/nX;I+qi-*Cnd+_9P75ddGc.>XPI8_Jf="R%:#*/r7;f
%%?`qQ,aNGQlX0?)ohaA'"^X8eS'$BaOk<%WS/epl;Z03P<`PgqE4Gh`K5m/c=]N]LqS^sP7s21XgE/@kCXgH]-]F"E^W%K\a0/Ri
%nn3:j\#VY'"BK99jpUYF^I?KGCV3l>Bt$^:B#d@BcYQQq!642tN=jOo\S\N?P0$W(0[KVo"%=!q@<3'EZQ1-OJZCsRWh99,BPuLp
%aOWJp1]morHs+JtM@Jh$qQ2bu&"ig*Rj+_n`gYA7$G>q==OMk2E1-E5K&uhVcM14X8Wr8h.>&\Y-Bh<n.`)<J5^W1Hc-iDa?EM=4
%c-t!_Qp()]X*@"?Q&@L$a=EKL_.qW@'QaCBRK!>XR4]I!/k=VL?qa;s0\@3;>[XI>iKK'lI5!Qnk.k);o.N,7?Xj4s^`>1kN;0Xr
%]1KCi7Li(g06Cj(hBhQ2b@n'KdhVo>[5/hbA.k`Dp5a_'bCfD5cglUu7]a;u!!!)AM5EHKZ\?HI[''-LDhqCA-#Sf+RnTS$Lqb67
%/17CL:hGmE!.9V\Nb,3B"eha0;8(s#<06NX'CQh+G:?Xj'0skO&fY'L4omW(X02)NdYj^Ne$hm0=Dl\6!"*2bFE`hbI#b<QXh!tB
%&N$knkq!&Q!&1msQ[JkfD9<kW;8YAa_ICuD@=(0oW@V>=`[=dU:a.(]S*WM1Q?C[^-Pc6R%+GV^I8C'R.%=WFlWdrr#fCDQ/ksV4
%Z-;268D5_,c(l_ej&IjI-n)1YG>S9n_ciX[1%FZ:5^pKN,]+PE^cF`J,UOFcoL+iICIHt`GJ\Ds`:kGEScK;m<g:;6AYb&s&BZl4
%]S4il_[\@NhXi"3R$3itYVW^W9Qe;0IB>*[kY!1=pJ]6PMlbfG]ARXIK:Bf@5r?.KLd[gs'4d@t+7D8"=Q<m5FiL<2e,\+K[44,N
%4q,u%>8TLi->L=f(SN/'ITlXLER>01DN-e:`'9@/[$:&jcS23QM"cF_^WOekrs=ZoX+IJ@OFD[soCS0KX5#h]kDcA^W<82oOXGP:
%*;RmNj9ec\_J*IL_".k^Q6#oLgM9hYo*PNeCEAVEFj-KgZl/d2Sm:014Qb_C^b<@@anI-E`u7WB%:o!ORLY^Rg`BnImBN[f[LFY1
%`0s=%N<4/L]!F@,:5k[TXpp)s6(01^"!pdm:du*_D)IJJ7^KC$d1(GQR"X5*%Xf3*H\oOQ6W_J6"RGL0p&4W^NZg9^o'`Mh<Y=0C
%E8=q:ee:/KY6H-9/@1G*B;qL1mpuMo#]iOBSUX;F*b6P`T?j/@!W/IVG290COWUN[0LRQflD'p[gO+`)dkbk]M^WZ["9R!pMqBbN
%9//j4=9&op@Tds<QnrML'cemT%m[u;rR_f7Lf$U\_GPNi)MO_;ot."r9"Wh:1d>eOj8;GF5fU5iP9t!pOUU3=p:'Q4Y>"LfIA;7$
%L4cquLd=;7'9pi@_DqT,8M(S<r$rM)]9^p2Hs*4`N@:D<Ret^KGc>\=AP/XEccuke\0W$K<eFD&S20Kf5EK+<k+<=G@KZHt+2*b`
%5'A6A:SlZ'dkLmH+k8q4fcQKI#Che)E#0r5L?ft>=6E?P3-YmA"MQhfR8?qgEN?SOit,`rP+O?%j"=<b)&(d?X-?*Dl%2m3L.rM&
%S7AbrHR4r0p]XfR-Njd13^3ZaDa!B(:;[%0omu_:Ne[Qb&H)EmcYqUtKW:F9QS9+ZiS_^'$,U,[K%7&J3.`Z+Hltq%jQf(2BV`'R
%BZCk9K;;nATNNL1SK(4$cl0-N0cpJMQfifl@Op`819uGEp9F$!*%E\e*pI<oG`45):R#9"/<@"MRaQ(O-r?!r!>.SDd7][FY:Co"
%Q1``/,<30(J4VNV^CNE:&8<5<h1J<q:f&!o6*%(HFPCP)Ia.s4eE*T9,N9N>/"(m2"-hk:gR/Lc3](t=&SAlW31JaC$qWl;)$)c4
%)a2Wrm)Q#I1%O(+iu'h-O*sKD\!$>L3os;1Q81K11;n1j!J7k9Ir1MH/aMsa<XE0LBXZe]54TGU1,dtX-2MIEd4*KJ(us[)Dl6p9
%M7Ymbcu$u"V</1'-i6YH1^t58V3@3>OCjObiW>WmFD'nYK4Op7,25P7ZF8dHMU)!oXBRUfLs(sP"^9Ooi<1L>-E1Vs!]"c3]?F93
%XQ,5?'GACIEnl:BUm51pn,n@N'YfPol:5TBJ7DK%Aq8RU+@CnTRu#nSKQN:rMaD[N>4';FY"P@pTfNB;,&7Tl"U!f,Oe3=Q3@1!R
%$D:3A9!7kAMGs7.8'goN%meaf\M)u6IL\G`Zf>LfE&:-2.e-D\Y[U+O`.]%V/V<J[mt7L*'&tYqFFbt&%Mp%%2j5<)3cbGI9E<UN
%c\\C*bMcB$5<YW^.rliQlAtHFFJ0CGFn'>N'YeiN=(4fI.0/T[^1<.4TG.\WJ2^q&KguCj>@c-tp)O5$A@eom.0D393itPb\e*3^
%>'Gr5pSO1,<#>AT88*72R"4mJAk'Y4IL80?1QjPSW]!2^UeFG[?hW?aac>Dm(7`^2d!n$h2`sC_0u/0LV&c!1&sR'9P7iYDCjA^0
%U=j4BD)Ss;N<U]o_u05-^"2&.=`k3Fn,Wm#.>_N+Fm<N!4*U,5g&b;)IiLM7PhV2NE6_U<AJYmteE@%O2@7_C-m?'$1XTB.A9nQ@
%J-.*rnsHdQl'0QU6tREq8G?!Y#>m<]JnXOl2S<0!-/#+2q?I21PlM?@oA1"/4H?/B:WE'6Ofea*Q\iZ2\@S"lE.h[,Xp!-=)nC4:
%>[MWB(&FhDpdU*0IGJa5Ug-=I'%aNP1A`nbN<@4_XWT+F)CBZu.,a=r-*u=XNF`:X^c>o*!f&o%(pI+?X.oeq-f@HkZdiJiA!(QR
%;V'!r?p6I,*Z2FE?(dN?<k#XoA%C5Ed&KrB,2iU4N4;R?&5/j#8K>]j:KShmA(3/ig;3V45a2i9=Obj>&m6&F@?rsB13-LWn.NBY
%.m_.PV3j2(2mZhslDG8V'3k"@B?WGEa="dT5F__1a,lWgZFR'gH#I-4_5rc^D'`l&@aYE3+LG,B5\d4&DFCk-m;8#'P9r4CV>F^q
%A1GSA?Rkk@ZC4dTjMd,F0p7liAl?a;b*-&.CVW`$4>4bi6dc\^[irjdlER/+F;He<NeK5A9Z'IU%Vh\&j\h<=E?I5>$UBlqKt)/W
%$]%=2a.^Xj0^G]R"]B;`+sglF/WhUmq:%g=X^4&S^C?L\VLBZ*+U:<a'lptFjKFb_#q^TT;^%fA/=M\*'^Fj^J0pm[i2<s/=h6K-
%CEtAWO#&;td-$5Mn_BR`;ujio**YDY6;fKlWWM*f*\'uuS6Iq\jtnetc!"OG"cm8R93[-!d(P<p^o0W:,tV?NPemZR:@F[j]X7kM
%F3Vh6Mpk38A#h&o_*!G#N#@,0#>&B]SU*cUe;7jI6DjL;mubkXhhDK+j;B+*`>X@J2YD_uiq;CE'`gAWao[s6*E,'HDs<Y=91GkE
%l^5rBm[m$@85:-ajIuiF:T0M/WE:(PW#8?P9dY#"?K*(d956csBShP\G+S=t=H"?SIK@<3igt-o@Kumi3q``B.g0CK6E"Idbt#Sn
%aTrAnXqlphf[tqk@7`6kb1,h_#gOAJE,Q$s)-0?`oL:[<Q]jDHoB0q9heLOJil/MNi\+d\s/oq*fZ#51&`O=WF!ONEZ/M+]\T+6Q
%#bc2L-%4A8pVNK@*e[q<nJ7Gm`i7tcRuR4rBnBoQ"]HlTo#-YQW-/AY:_8^s8j\T/E@EYC00j8i*J9ET%DjfADC&,o%m3?l2UF;=
%!bs2D<'f2eC7Tfl[EWZJ[q8b_ZS+IQ1:)E0>l)UH8Eaop1Hn$063/j['k@X$i_)cg\13dG/_3ek&hIb+A8Y=,MG+ru[78ID$D'M\
%K@;7o1,Cd$<&RlA)6F;#TM*MQ=@"q!UY-r_1-CqakL"&ti'@&a`CQ-78MtW5/p\FEXN[u%.J8@m4DO,tW)>H''B:\*;%F_/,/1G,
%cRu'qN1b\]G.D[.qBVFKLJ/a?auO/o(_QF^eGA;G/KHfEJH-;[hFf7l6_[k&r<A3$>/3,G7:euhM`0?N=Y<e]KoNtUmA%WP$`O"%
%!g8aF$BU[*hJX_a<PrnF&'+Zg)PFPh81aQbTu1=Emnr:AA!>H<9J7sMR\:EA\29=IZ8Ck@`W2i(X/=l$DE"8(j#kFla'B+H)fb4q
%TcMk%Z#]t[c?`2^V6d6KDZc6%lsZ,Z1&EeeLO^Q3_r*AZH"tg-8snB/F6;q,mX6?q6/@Un6GLabL6K>(0,.0\Ed)*BB>%n-#V]a$
%"4:OYcaSTj5*buoHV?rk8W>;?(9RkH2n#Bt0g9d3JWPi7"$Z>$'`;@s!3"C0!-qPZ@D6Ac'g*aifG:*0^+)nkaF4kGp9#5`>laD.
%,t,.:99get%UhEt$!*SFE_DABi2!05$Y):@=965kqS"8%lNjB>"PBf"d8(aR&uGdb)J1=*Zd8>!eP?1G-CGIR1qA/USK/d(!7Sk/
%3"fekP1]*<TZ#D16=q8QgNQ=Z_+$!Wq03YH?sC`9UmGJo3k/S2.r_o9d1)%BZO'2brJ=\R\t=Y=/1HaU;G9\c*Pc*=+E)kiAm1!?
%rsK*$5Jc]gK]K^b8^'T=.@sO@,%/]"![;ju/GcO#q7;)H^<ZIq0!?L.ELkP:LfZOp/]Gqub*36j\O?[W3K,)f!<Dei%Ju#e[+M5M
%amQH@\<>ffjGgGHaL2,.,dGm`b3[mjd&@F`=1,Fd\E6d&O',B;,Z@p@ELL,]"_1i@SlAFtC7X&,qd-UlXhiA5(tbp>V\'7mBE/it
%<dV>R$C<_nDhb-XU6[K(h1.F'qOFVo9Hf3`_V?X)?:KanF=KFN(1L]5nP<Q40ks^GJtCDN9[qE)HSVI,>2)d0fDI%]*7#!QLa<G=
%GiM"H*._\:BhB*K;$"J,n"<R1"a)5AcjLspQag36hT."4"VMd_8Fe4*is6"bN0f\8a/,FnO.)5<RB;<[&ci$DaJDFGg)>N6AW$ua
%(>OdPfup,"8j:=r%Hei7iGPMg;Z)_(?FfZj$Z7Xh"r0K0-1hV*GllF>/KgeQH3$Vd03L$9lYR"TK5\t_TRF`7C<YDa]2gSlJu</*
%Je:[Bbu/X?]!(oA+r6`joH@\9>De;UYSC09\C\CS0]&Juig3Wd`nq]6]_H%*=58NM=+tlq>Hri8PElJl:rP&$9_ekCJRu'3Gog;-
%]j4hfC_N.G+ldOX//7.L!@[*&/o!1X9=+!7jQUE;EZAc8GUBr0.cA<dDPj-.j.YCn##H/+Qo.>S%S?iRfM,OPb>Kj=VX39i\B*?[
%hSgZR2q/\hkS@TS7,Nr^l1S`Kj>-d,7`,F_8hYb)[&2'.h/!milj\U&/7gSRV8Sc!icmAok4;gTY9SXE5SPk4OiN20kU@ZpAG0I8
%9>J8m`!8`k,/e_,'nj;\c@]i,c"RRl,A8jGBYC_eX#EVTPscFd&pQKYPkp6u8od[HP7?A6-LD@@@NNgsJ9<QJKFk3fYehgP#>"8]
%!A3.9pgWnJP+(@Pn;sch)ZL*OL"6Y[&UP+8li?#\rh0!<@ZCU&J`J#sS'[B?]hd0C*fd2INNMB.78Pq><U&A?T(cR$TI^16f![#/
%%#6T"5>"`q3%^H]/hf"tT+7El,2-9e(>&.4FlEPul#[9%Vq>e"BVH+1/INfg+*ZM(6"IYgf?=MUaHWdamT:6Nd>ERAQ6<9sAo\YL
%P202[hCm%>P*j9!+D?Z>P&t<`NN*fAR&!EC6CNpL2@2ViFunHb#u'oicm`2;$K[Le,VcVT0K?HHQo/22Z\!"F&<EC:muaPAEVbru
%c@lA)V%-[=iS-Og+CV/*U,TY=*"ee/8Y)>;]XMRjXY:S@5uGj7(bB(s,9m5Ea"&*hZ'>S3NA<e"_#Pi((79_?Qm0&'!m0s,7;%7M
%=i3KK'M-"`mU*X1P$VS^(n6ItB9^J+E]tm`/:bolbH+4$'UHqjK+BXXRoX<u?N`Z3/><Er:X%X@3.b[%s3#2l5as'BY@sCGC9<l+
%>->u,PGMFC)KMtDjDIR5%'Z&cPV8%]R>Hef5Eq7dYnC!tiZNMe+`QC^1GUk>;b?hkU8/eR!\ngpY^FT<YSIE$-["8R+?L)IbiShC
%/-^.:!=Zf[[S/!3B"/-7=,&O->WO7rhCV\kc;O\@h)umC"Z)'nEha:SM0&n"35KGQMcAkt(9>Xn,(8/.5d0[mL*on3LrlhF+bE`K
%:cT;<P#[OGq9pDDLZ>pA:LLC)M%lMd^QSuM8B`:+pNe-5K9/N`7+kt!6T.el[-`sGXp7?f^^.(Vb!i;U_C/!5]rL^W`I)o_S'j;c
%Up5U7=`>JY[S$OJEobUS?-"2>h*>;3H4@ZWAo*jYBdFm2@b<A(FT;Qp(OEpN/VeSt.Zf]V+BOO`O;;8VehJBk/f5:42#bB=(2X,P
%>QQa)Mdip$PJ"+]+eZ`Ad3:())g[fYV9naElU?d?Q"'Wq0"A2$#k_O[\jfSDJba4UMH+3iF;"fqEU/4qFR4O?X:?p_Q>N6o6SlNs
%3([oSR7ucKSTh2#\TDXBlHnqrI*EHIaE?^H+g*q:3-f=";VM9I)D`ZMa/\Q'<!ruOb^G)^&Te0e+8T>5C'sDQ/f8+%!"jiRYd@0.
%dh=VFc4k/,59`=!E>C*!c[<Wnf2j,=nD"@YC'N[74H'Rh+INgbi*o[^`.Rn=H"LNVl^Q,=;*a!Ni#1*/Ih2olXW(KP!gF9!P%1nK
%bj#>.Bc24]nh:+$q`L,`qm4P_%'E97*4("o*9!bpYh4/MeV:bl?G^k<]]QPI]gJ5H;hug_HLeT+'PeJ']9(f7G_8[l?:YY\'G@GC
%?9h)ZqiHKY-KOMq>%\]b9-;Zr_J6l&BG%3laedWLj*LfA\IuRKSIGE3p3[ka#A3?;0Tlql7/@S'"S$cr?7&]<l7PB*!.t;;jqs>k
%d6[ndN^[jpR!ZM]_'FI4W@/]V9[U!jX]gK/L$(3[>HrB^1n9NB*")(RjUjR/5-Od@'1rLh3'fsp@f'l%l_<^-RLgPeP,oOc(]n"p
%lT)aUX"UOebXR,U:0R;l4B8CF$oWNcaalRR+_g^"T[]V8W/_d!!KHJuq5Nh6@+[P38oi1TSU!M?;U%`TIH6Y7%qc>qQ,%\LeXoi,
%+jW?]&sO;.n>o%>64_Qp4*'g2/Btt*J[%m**mQWYDfFQ*<RM#lhG)%0WN>DUb1bl!CX$n[[8EkclCqZqG#_@[Pbt.b8>kucN,Ma%
%dr5R0.e`(lBjrc8Z&r;R3bT?PL-8]HX2nu%]q26dj14fnY7;gA<Z6Q:OjD0I/<M4a#8`nJaoX?5D1g#dL@?iiRfj;*>7[e<L1Eh7
%>N+iYRW&CUKZ(qS51_8E$\T>3Hn9e\KqmN"UeJ!1!_eLa`F>8dfQB?/]3^).,[D9_jsf4\1FNRsiKo@(e7e6G00MR=7h%L,="P::
%]6h\%)mof&Mm.D06BI:^[an1kmOPL$g(QcEc1$])9YKWi,.nRpT).=&D1qt6e_X=HLE^p$hkL4$c"HBhFV,:bn1$ta<K3^T$u.9M
%M3g4'Tk+I"58sVZR"YRpAM=n-.M*mbQ@LY3YY^$'Y'bVH5@PofX?utlX@SMV=D@X';Fm`5/DCF6M\8k6H2E]<39ot_/eZ"GXDLiM
%A46>b6j4A&c1V3JDMfZi!'p`f!%]BM"UL/3!>.<>97T#pP(bZh=U-In<.+#NqF#:./TIoq!n&W<Y#ZBG&.Ko78s<<*D+m3:GOJ"m
%[_Nc1NIIte%S?/bY[N2*R%V+X]FP-m,CqX(2F+J=8k\KFK,l"@29R67T_0$5qUIDg,292mjd8[HQ9judQA8UEK6GpeWWShsB"urd
%X_in(Q5CU2@`fo?,O'M4$qtZYebL(?5_aC)S$:Nb6RI&KG]fINTFiG%dHJ8\#lZ+kU@hYWUn(2$dd.r,^=R`knV-&`)UI]nJ$M*r
%Zi]uQhM^B\US3^)a!3lUG0!s"(3K'J>+>c=$<>FXL+hS@Us&>gM$,bIJ=NnH.W:Bd0=L;`<&GQ:^'$uA"9W0+KN#Zh=QDNlc1=`:
%5N\]b..e%6n9?#7aRhpa+c3&A>-69X=+NXl!utYH#'pHh"D7m?%:J'Q[h>>U-I.S_@'kZ&jq*&^70=rHi1gEk<<>f`?hL;,WblOC
%*g#TU_N7X<Os[j]]tOp6QG%JSekEa)2[b(@`PC+?bHYfC,H5i.psf0Ag(Kr:d"&G;9Sb$:/1Hn.1)3'8/bD*7dq"o-BbREU_qa?'
%Q\"L+98OIS]KTK]#`c9cS>u5>.ucR#$'kV#5'oq`%g="Aa-ZdEZ+3pr+ck4)lb64%1N.j$W"cO2F/Ogs;h0G$+`f@DT+tVA#gs/=
%,(YL>3fKTrJjW2/-$*lI/kHCb1]m5h=92fYl32-+J1p!5&<L=9,Ng=;8(JEcXq_mWi$fR>ImQ.#)lq?=YR,mr"CR9Qk[t[Q/>>!g
%n"Pu-:7WS_3.hO1U]e6u4g9rG3ruXQ1/T&?<ip(^T>j\E(.2H?!djDb+DmfQTp%;>W'M[G9:)a#7lM^iYU[p8marsa9!K5F'=6Y?
%TAL,"#0P,>X;R'.f?jA7^\/Xq*mA4_eNcl+!I`u'+f:-G>%,#u$4#@[#Y*Y7/,d^-XAM:=@sSGX-'NEnpAttOC!j%6()56U*WYJY
%jJHnpenH.dGY=Nk(?8P"3oL8%8dNR9T3DVtJgTnaJ(P)6STBFZ0Eao(M'U)\>2P]nE)AsO03T,q%p<)K1*d:Z2rN4W0._aMBI8p@
%00lG7@#N1kJ5eA=!2PPuQNk-e!%+IIBV@ADRA2GL`.>7]OB$0HU*d,X&6M@T=93BZd&E7qd0mQ8p6tju@s7`[)?ST`*ZIl?(%2#p
%8Ja.I^XNetKpj1!-`]YDeuUQ'n.BFi\dOll[9ToXV]f;qeb=$bWi=,DD`ZI4G04M"N=J3)J;f\G.`1d`J;:'G32nHk`g4)e:rjEN
%@&9$^O=BGLg43"HJV/hGN[db2RdEh\!+D>kH#pY,1@:d:D:\3mAC"k/+i=jNdiZh&+VJ6gZtgeX)]3\0OK_NsAfqdOkIh!A#uKF=
%r]8M//ZDf[3t#Bd_]U.!4"RN'PW)rq@;T($/9-R1,SVp^jbKX','uKeVsBPK]8R+-#mH:N8g#Ghn]XlX+>5"U*;S"b6T]Vq&D)bC
%H@ei!GLD@)XphMVMj?%lH/^OtSui.d%h\0I/gP34BYc-2EA)TC!VZ"4c"P9c?bqb5/hR^JpUl:7BL`5,mA>D)s2lr=!s?=b=cXMa
%%cQ&V+C*B.Z]B5I2&f.#_uhhJ#OkP;gqZf_Pd$PaYPIf+auf#o85/;ClO>>Vi4\(X:R(K;hQ)8s0%%3()XZqof%abfWi#:gY)/R#
%i4Wcp1nmh-Rb(W+g<&5M<@]>X?[CjhQHVhFnM%do[/p\6,R)M%X'S,(Xq-@$,`0*_blpb48NkU-\8X-q%[-4l[p`]k-Gm<K9r<We
%a<^tq639O4'L4!-!U,E<hh5k/ghN_\(Pt3'&7AHRacdtu1,NJuUba"+d^:RQBk6Ume;0,!Z-p>0R=g:&";r?;8*!sXG%S^f+^)T\
%eH0(ITI!"SN!\lAY:SI^H3&ta6Z,bd7oqAeBf*h]\Ms#R9>Ta].oQarj:FEWX$T3<78CO(FkFsff6u`a0t]A<du.a@)R!Bsk::LH
%g)sZ1F0$1+.0I;:XAL>:'p?#V6Y\]`\6,0?V(WO@^+9VGP`0f@3Ol^m)Wbeb'He=eHsL6JGCiZZ!,O,p#FWZu$8q0:4tL6Fec`5r
%h7WC!n#0;7W+;*#cM'c_r4sqoGq?4lRh`-T2(HYFlS6kH`-<pKY$eR&,EVO"?nfn]*1?!@$^E`Zi;8eI]p9_j):R)'?O<>_EtT**
%\nQ;qA"Z[I,hg_s[G4/]-sK^o1#I8q"UE'Wqn-kY%"6r'T'P[C"OE;\Orl`-4paci6#dt.JlMpr@"KrS5g9l)(FC-c"X&DLGc"4?
%%l;>6h!QT-$Wt&9;O<=7UH7`imeamqre>.D#.ReLYPTdca.1-7;$"Y!1B0_,@bWi,be\!%%_$@g4q]bn-25@jdds3F7abE,b)9m#
%$q_Xm/_N\\PB"VI9bBN^YJKnfQe>!IUo'Naq'/m61Pa"e(Q0EPTeYb1p0%!aV>JL&K$B'Uq?]`b%nNOf6geq.'(Gr>)AM`6o5^iY
%?a'J'GqhYB3BBfZM#cq2mu"G.5b^oMb`#`2&hYfHH89k5S23W<d\U0?X&-,F3hhTj"2[p1&UTNE6LH!-Ft#TKY3RA,Or:>V0"LX7
%-$a`DJ9W?cRXj^G=r*l@8UVGO%ZCuKR?hgn/L+D`ZnW9&d#&8#-Y1oq@:8*0"'YCJ*dtaMMlG"#$@GeX6b-YA-+SQ9XsL%6!IPZK
%qR]/W6*$+H[1:"md:?3SGM8B%0ksL'c(S2q.NG*?cOm(SXf@3FDE-!&9@fT-m0HAk:)4%*)PRrSHD(MPoIMm`7l8Zh<GOufDEjAS
%UOc9H-5Y7]X'iK,J;Yc?b4*PE"hBQ1A:<^8kGq.>aDhk>2hT>.,Z'_>I>D-7PZ.`a3uh%_N2-M-60e0^3!1>L'77&\'5)$VV+G""
%ZNo3'0m?TgQAN%f#Ket.V2:K<^*ent?c[LEo2e:KAZCnaMj#%sV1lS)oj$0M'URk]&GJ=>U%3G6=%6TlZ%SqXc$p03FC-Z'0h&Aa
%<iVr1-5h#ERR`"t<C\X_JImRB=jIg33YZUN@7Ds*TQf3^7]%qa;m-NmF]o6o5j]`]1(pN>G`f:r@n0,u1aEZa"+9CuW'il=9^i3;
%$9(=8&qq;PE@ZSj-F_IBMmDc@_K?4K\<*('8c<6F0&E:cG=dMr+*cDY>hci8YEoYn1t#r5:':be$^_:q:,tM270V<aGD&bZ.Is,F
%ZLe^jW1M(iV?[qa&>[dZ3#"#C9X/8P(T)s#0P;F4;<XO%J`Ig[Od`.g/"snY&$loQ>EU"bc/[B'iXGc$WShnk>Yl\rmB;WXikKI\
%S[se@`(aYFRM%I!o`k8*7+mdK:+T_Ri&"P%AIe>FKhS@2!DC1"-1_lu::1)/HlC?R!X7Qrk`c9KUko=D2dqpK&(t1:R6[h`LtiN"
%.[3pWoHrkq4jIVA4=WlQie#,g4etjbAr&?@mmbRmG0-VB'3_jPajs:RQR4V.Cgu+M5ZN`@osn-qCesOm'%J!f";WRI:K;","QjZl
%+U'bkBI*jr@;'dpi1:^WPin4;dmbLjX>ZH'9U1;E[#bbh<ML+Ybe"lh%kB8!$]P]a),C3Lakg5Z78?knh"A;rSZ-IM9uJ+&2`aD7
%l-;a.04>NNA:9Y`L<(;BYU>4tU@j2D$VhGb5Y/1S7XYZ08"jq8.VUf@!(T$AL5>+1-[855>[p[i_guP9SMFa%'/nC;),h:f"W,%6
%AgJnKg`Y"F["&IMN@%(Fosq]X81kN9cbllBL(f[ial$V\?j,^:,Tn_nl8B7)%o>a49J7SrJIYgJpkuB[LEq3J+uPmVO>NG;NQPj)
%OOcJoofP[^=u%ch9*c^S6tJU(P+qfU/F$@jSPqY3UEV==Ll72m6tY72-BcO\.8G<cqVeRg4\\cnb7['O'HQ66[00?rA7^MCZs5@R
%;FSAHBP_?JPj+F;IKcgd"?AWep'3&gmOKl&&n*5t=r+!^\0O2mnoR2OC=\RA,%(./jHGL,WT)++KV#_(ZXM%A`\g#EP[s-f,9rIY
%@_?>_Wa2cK,hT+nh"E/G<E.(k>Cn52SUuc*%,g&3O?sP(npHLk#9Q1=0U]".hj3J]MQKL!ScZ2CO:nVbEiV?2kp`[o,@pLD08UY$
%K+^gD"mQ^<>Rm/:5p^pj"1][\Z@nA0De`ENP+\7)Ok?.%?C!@2>e+P'AlViJ1\-TL1PQ,%*ltFQ_a4oRM6iC9-.trOoN%Lt#Vq&?
%VPmh@Z3oLq)I=W())VC,O4(Nf8h"C(2WddHN$b%,1:?H#*&D6#Z0#p+=W'/03D2LuBTCBiT0e3(5E0c:1qp/q#krXgd[icT-sI9]
%qIDT58Ke9:16CKgWZPEDS`D+cb">7OV7@V:UdAV!F-2[DS8Oje;[du8bX/+IL0m!YO@8q:,X%(.*D<T<C=8"OTkcD;jO%/PN\8JV
%TIH_!Dh^C>LoQ)QEJ^5m4l9n`pa)DVNF41=bslM`<.nkY0jeg1-YNOtD.8?Hp"4HG7\hE0ArL8+f*C7<ds+2n$)#PB`5TC\JMiE9
%NT'oe80K:k^H0Ub&j3D49<sM29D;/",3WV#nS)TJ>W4aV>70+No1Y5'WBs,85j$B*ZLIB.B5WnQ=_,Pc"V<88X_Uf<>cWqT-r52S
%QB5oD/Bt9@!c%!)"<]InN+fI=3?V0hG>r^hqc?FplUV`qdl7tu,`3@@!"%m+X]I/@/Hq&7.S5UI<?MO]&BWI9or<-0K,*09NR1L7
%a^cL7].FAuaLl$6/I8n"?Ma$cl[.3Yg<""/Jj9hqk\8!P;"Z)SR!->Y,TNJ.9!Z<t#M)2O#4mutPmc9dV,"F5Z*<ekpBE0G9.j0j
%UU?Q79_&C%,)#LI_P!Z16K7dkAl45+fJ0('A)j[)Tj*[Tm-/?5is\jUO)#AhXCQ5h)fMB^.SU=^$1gipXNKDQ7%gKdRiR&OEf_;R
%SrE!'S@PlGL+!.di'>]l76iJ\;KSo8fFp3?Anc0e[A79;a;2r%?>?bC.UI2oL2g:Y`='@#?*8$OP+2sQc`WG51n<2J9Vm3F<TLDd
%J=)2-k=kQ-9uqAh'"pF"=P+SKU7m:?oTc"Ao#I>g29ge8q(lTpdgQ].4G+euX_/*)XdGY0M$[Fq*"U+7a0^$7p@e_foL04WH572&
%@i&KRL,M'&6o5r)EcY_S)9H!SY.]r02-PTnee/c`)VFJJA@c>em3:%fgks(nV]N>\(1\<.*1XnX&Lq5fO\::t@1$`h*hJ$G0^GMo
%[fS[O]0_QTQsrM@7fOMi5=;aYGe`@6g2k"K+1C!PnORmb3iM-K_gN3:m)dQ;Kde@j7iiK?XRHl_[@#l-aIWS$g'Xh^[e=P&`;)a0
%_,N%-q'8V->T67G-_jlX&R-Por&=r4M#`!CP$sQZ=Jec%NWV:*f\6_(]E^.++i_(=:H4aBa#uh'IUqFRTq#nP9bS;;!0XB6SgDKj
%,''gE?#Jnp*[IBc;'p<<dL\pUU6,(P-D7jAEcQuP"V+h5F/UDslO4R`OtG(-i>LQ**R9=!!iA_G-b/=s<\b6O"s?mEe3]#jK@a*@
%7qS#&E&$6_-^O6ko7n/L;UDQQ`E9C<XCj`-@,*aFA(DMm+r2"rJjkj5DRP9gbOad/+&\WB,Zc?JV.eS.56\m8jfj8EhT@-5CHqrb
%5V+bh2+]6m@>OaD5;+lCSf.6]pX=SSMe.8I0Z1:/S`l"u&M4:BN@([@&C,_Rns<F^P'Kkb4Sl<$6St!j3q9IbE=%gmZu2jkG#Z&Y
%5-W!r_?jbTXN:[EEKB/WJr78t's+rV9#.f:bWM2=Q[GP^5rWZ7\ddD;Beq"RB-l]B>/J4]EX:Qflk-T>8rd72#NR8Q19l-OVa>qc
%W0,k+kVQRA7>'#oab#3/3OpF<9*Hi=f2W9?Up6qXX_ItZIUK\R2q`Dce*+,7?UX%_%\W%E@.r>>:<KFi[k0aQ;/T(mi@S371miH0
%1&W8oX7qW0B8R1pB`D[^eLO`NXkptW-9=d:Ob7A1j<iq'btt[l4'H4u'/tJN]sP'BRbA81k2)FHj$\]-G#V0/V/s,p%D^@lV-nD@
%Nu"sf<>%fI?P\JjEiGO8X9@2TO@)D^J>3e>_2BC)S[qIQ\\[iu@\J&/SX%/cio,DU65mtA$9m^Ub`JaFi26g.l&]uf1`-qE6mi3\
%?ce)(gPT^/R6.ljlP5qH3CP>fO^`*JN':g]hS`)ndFeu/.`"q7TeQLf9u^$=nH18A"\FdI7!1=;i%>@d]HO=hem]Y2Xb26f8k#PW
%@URUa"q'Bs!8XBL31oj^/?LCh<19+3OZZ<T(#DL#n1mmYaIOIG6RkocJj-AV5udF6raCiQ%bZTQ^*GY!d(9lO,!_KpFb91Q8K37M
%C7oXZ`99SQIDA7lEilIo5^JQd(ON;Li(VRXbKQ[k,H2RdLTs)aF6:W*S`5HMUuS!SHZ+P^>`et=7423rV*Gfe?ck2E$`-3&mW@;d
%VocbLe-V&q#a*6Ao\=9PDMo;.(Adj$'I3t,X!WP_#]"XH(lXWu-39E3i0r42TI[Un;D\gS'[1m[qTVe/!p143I7kX%@;RK<X(;Yh
%3qO0C1N4"_D&YJ'5]F-T>$m72VUXD+1eGs(Xl>%G?:dmdDELcGf3"p"1^I#-O6iU$RNecZ2@$Yjl;XVCK;tc12bI)[U*d%cek5L^
%8\6!J<8(R$9e4@q-Cg19V5\ei`b+Is<a=<]4k*#uG#^P[WbH^5^gnke"]Jj5o/Dk?f%Gc)9B$eWKOfodL:iaY4O%=Ml/!+2.R@]!
%q:S>'l"-l/c4!gLBT/YGJb*9,&Ot\:P:;2,0HR1_QL/mq(>"OFLc?71c84g%R'hODb[<&+JViWo3k`+,EtHe%cuh*'_-q0<Q%[87
%^#[8X9S0+WW\iN-KfPSiC9srCn-5)@i8R%KA[m'+PWXQ-AV5m*oE/Gu2+I(5+sFA)8".O"BHjNei=Q'XFfR4B]bWlNKua]r*PWs*
%&pgHqI&gN]"jGQ^L!fJXZJ<?i"P:JO$/PHqCp.m@)[84WU4TH^+G+[cm`i*rUX*5q*^f;'%?Qk5nhA]b959'A\Kk_@R9N>$_7YKt
%Vp*hiVYtE&$[QAhN%#!1Y;o=6jtoN2bYk<1O_Hl)^dt@YX-+IdUeR,Rca:hModq-9.cVZfQl5Zs[oX`>'']\e+jH;]T>AMi`aTin
%ZQ0NbftoRH?Na6('_MWcNY_(`iX?N(YrO*<lIAi:_SIc1DVZst?C&An8,JO,2)mTHdPZfo@3PJ7g_&K?V]/Y\2lC]FWR4?3_5+dQ
%T%RefBmM"DNlH5,`BPkb,CNnnXOp1:nh;;O<5I.mZ>0Wg">+F\9KF:C^;ta;B2rStg8\XEZ#@C]BIQEg>aVD*;*i5cIn>sp$>rRc
%Of5G4>Ac1-X-XhIBp&5bSiL&7OCZPXPC@A>PH/MMlfF:g[Dse=)C@dE9O?+9#6n_3<.h1RjR=Sf'$j(/%T03GH:bo9fm0:$JeR'u
%[XkEE"c+NW1[tV:1jM)D"t@Np_ePS>C?'b&I+jr9ZZU36'k=sZ!Kf*25ag2.$B9KP>(CXJ&5Vq#>s[P:;(riCPk#0)7p)DPe#FrQ
%I2N/4F-(pP9b:,pirUk-]:VX`Kt\sgRhhZt%3Q<Z$)SS5Up7'aYmSc4"tl3`o9<-O2CINcbVKg>1P1P\YY&aiPLX,#=YpLk*b\X(
%.W"r(S&Do(0N'g5C[KfLSJW7B9X&]d!/t'cJTNf1\t#hhO>[]P$/#-_!6=)L-`YQK^T*Q,`1f")(\5GdDn<\8kZG.Mi$[Fs?]]nH
%VRRYgX]ar/7K$tDR2'<n_KrN+JR[i@+^^OOUGl6Fb*#0Ts.7t;\0U>1].S5ZLiT=EKmT_8%E[C[(pPJ3Gk&=I38>P4Gn<X_/t34X
%AAC(1r"<jjDj!"LI7hG6"4<o)#(N1J$'CaIbqYKH5+Kmfh4eD>2mO7V"N;ro@m410'T!Gt7_>/$dSk4C'VFU!Y<K/T:[Oiq-ZRI#
%<9T_ZgI:NaFIbm1N5YtDE[,C[,XW6ks->b@D1tk.]`md_^$\$bQeX+Z=%[o#A$&q9VMn>TJp8m?lo/5m:A9)t6*sOrdK7lnA-<uR
%dQq@W(QtHrCjfI46;4!dPdqg&Vk>^G$;3WN0@)(N(jmjdP"9O1R3kcr2.p@[k@,nh_+Q8d!-tOe]MrKK(8^:G%!ETq-:AF,H4:M^
%9D)^nDL*nN5rgLEWpa0)][/CB>;9JM_)+<GLldN0cnK>=3q:Y\p9sVJ%FQF:XT;C`^_#g&YgU!M8p+bF&7#/b7C_K6"e]MD7\L&B
%5u!DAOkI>GSq^:_4Q_s&3aBKVK0TS2e94i(o'$=Fa8OLpJQ+Ajfu8a'ZU)p).IIAO=4VeDI:*tb<LVE7QHnl:N)"RVGW-3;D^FP+
%ML>M&lV,!c^@aNc)jr?NgZplIWq`.DUhZSbMcJ-o<6fW^QdQMP,qBanUi>+(G=**PUEs"r!$fa"%@0&L(P6ppb(;2Rj:I4i:.km+
%0UI%tNWnhQId&tb:_,m0,Kq:8Zcjo&[)b;U)*OuTSr/l6n)oj=X_>Eb@X6Z]K.._Ca[(3q*DVIS,r@(J?3g!'KDJ:*=u=i`gl.A^
%%hC>@Jm:,j?c`&\-H_%F%;mG$60J]7(%J^*&dm,-fR6sB-1#g:kGp@e`9=`)ir2Vi^[LbaQ[c`ZqWQr4Nu-u'&&3Skg!/'NY0T;D
%](kjUCFsaZ4FVBqGB\%Rc"Gm[G5:@=4GL.&3)*1sI.@$oe%8g`nrp8LQn+CcIe$QiIWBLV2a8O_EtUKq4XTrE^^2H[2+9FVoq7CA
%KW!:SB5>9[=amb-ZTYt44MQ,cIWi,Bg3^;qO8c[KC#;p?\,OVMiE+WlfE2>jZe#01TJtFG*acSU$[6i"#4VA:_E%P/epmVVh>'<Z
%#EQHuc[7=:rqH9@J$.Fr%dEKAiSX[Q*?P0r++F5E05BXRZQP(IZ<^;eKLmIJSZJf.SSfd4=AG.BlaVTQcRG:2:Tg#ln,:n<&-\oS
%@TE)Q\AtPSA-=9M?@>cEk*ti'==V0]pO!_q]lL,sT-PkN/j:K'20F=2Y/1:ASHLr.J5<UTd?q7`^0"8LjN"'RA<h?r[G;IBg1VSM
%DT'HKFnGg=Wh*FadgX]bgB5dqgs!0H=+bRrS.h%3=!b>s/sn(T:,Y/Pi/FE<?"1+,>\(s`1C.+,I7>iTdGbi0YbV5df[-Vg?[<!B
%SFhYmY2_QFh`5-?Nj)CqFdZk7`5=WtRk<#lSfemq7Cc)lM5t#eWRP%nD-(KX*55lQ(7VE+*\7AmK=1mF+$@-qeF;;YVZtE%#5*;>
%<=5^VBb"7Vd^r+Z(C_9,_KS(s3#cKK8jYJF<c'5?6IbRNg^UrPAATb*=s<tnHUobl_0c\*al&[j/j6_2"O20@2a<DTMAo-$h(QSl
%9E.gT[m8"TNTV@uE&_!S;7ihWYngd2Xkgb?=67k6^tHk>(+L``A"4<Q@[4AEd*>f_+n,JM0Ho.eF<^461%9E*I#1YMTN<c<E!;@P
%j=`CN8Kb38;%KaiC8@LQJnQl]\AauSTM#kuQTi#Jd0+D4=I/AU':W';'+=hN'sr.bE"k-s/nU)^!^A$'2/Z<h(_Y7&=@mQA.oJtI
%_.Hs<U>W,Q(^2@lZJLLh%&hP(:(H=f:h*-39KZD@"IBp:P^S&`jDr1q/":gI7n$PB)MVu]i:eEgB.PeESeRn5<^=)%Ug@DSqT'Cf
%]ZdHE^$n;"-[Ibsg:%?EN%"R*6IRnlbM%QA(4%[[6a>->rU2#9P<nD(;l\1uK)j#j1GFr`>t1Eg3(jqf!b[Vi![C@(bb/qd_o%oa
%-!DEEAcss?Wt<:0Gat.h-033>#q`t4pOB>F\.V3PEHJ@pKnZJ'!Zp\WbM^-h9H4GmQ^H9'k(8B$.aEL=8qf!](?`*KCUYFB-,1Ql
%8qmUS;s=\*+;"W3<ea@c3l!Wgn9^4,lgP8)lJe#LF3P#W6e/mScS\FkSMUIu"8"kmbrXm"@Is3".7Vc*d8_^^]9!anl]>u>Mp%Sg
%la30Y\StW.\XK-hM``.F*=]SQ"c[`:)?22![?sr#%1_N0*1<*?C[?ckd-.$ZJ\YD=i'IWL_+IY@6V7%!FQqjir@"<cCr.qr@aLH]
%(hH:fkti?qGa-:"[r*[Ll9;2^qP)9liZ\S12BJ%HM&S:?HRYt>ja+]LV7V"Pee4s4??3'WJ)$f[Zk)HR$8$d/6%Z`c4o."UGE,.T
%-+(P%+,d17m*6-UQp4ME9JB,Z=47,d#<&'Z2jb,V=E%tQ'l%HheY5F;Sn#KWLKjG"&8.dW#qk:.@+RI"B3^e-fQbr9d8'/)UQ6'!
%rcA2KQV(:?FqLo"ej0^Sn1p6b>o.G.LI3t&6^<OLUdq4%'sRfX5Ta)7nE[!#S$"UPF=rjOnqhBg8PJY()bho-@tpqtP\_dIKG1%G
%'9^LuT^tE<P_No:cCU+ldY+[?4')d3pQ1MB1Q70I2JWZn@!>URqTb6[3=)eKL@rPGi'`%f>g?(,nrbjl0hr^`cZ,-%1a3i(,dp1Q
%;4O(H)'Zaokb]sN:TbRtYj?TH*N;j<1nK%gpGDL?`V*6[4&FG"P_VY39-`].\#CW&%*&sDZ\U,%ACno-Q&o+pAW)e29]0*+=D[47
%?7`^\J/ecM[;CU$/EBu$F90Ye/YEsrde[;!p56;q/K2!(UcP6P?ii+RknXeSl=qW]+RV)G^T@uK*q=/O6SDD7.$kDh!X"MKP"ZfI
%pQ!r6?E*Yl8ZakC;-b"n.:rW8jGApU#VQ#CPL=#(PCpNp%FB[$<?#;E<_gjsUGk&I6g\T34AT10QlM!co>c)0l/H0WE3<pVjq3Gi
%Ln3Liob*@\#5XYEH8:YnL%Kr;SOP=ccEbR@Ys^,[4L^e88un/c.K3SqWoiJ\j:9[=.o"EM#sDjOH&$.V(+`YO^*4c)-&&>p\=Og]
%nfZk9;&chlXj,HY5HlFCe_,g]HEM5c;t<>:4%EAD76UBGd$'XjJ_^O(DMP1NaK6dh=?!``d#MCg47tRV,rqJOXgb[R_o51-pnfLt
%*GOpbJsj7u;**L%j[rgjTE<A!A10I>Q"=6WM3EdONct[\Z]u0<"=o;A:M41P$6h.7NdBh-7'RCWYbo30DH]n>X.\``,aF($cE^M2
%S;0ts8%]=M>TfAeGsTLWf1N'&`5W*#1j(RP3h-T32`dGh8McJhj@V4G0a-7;!'5feaL>Y:cu%R]JRUphSB^-f$8uR(k`H^Sj=@'C
%eO73T?+<`Sd]a[EXM8=&Qdfb3>7&HnAA4ZkjLdWl,<]SpLIP=OU"DJQBXER,,7,^J1K&&fOXa866CIcRVZC;e&WTQRHLX^;N]>/6
%S+PX%4'tor$[Atb%]'guNVgW!BPn>W.57&RU:@H(aeRe;R*Y+&_+ctS(=Ra>#/=\KTTEO"eF'pB[$^2;-XC:!@1Jc]e]Uj$8i1WW
%nnOW4KrR'5<!,t_BHfnHX1-U9ed/H,[:lZ"QI@dIXR9ASoS^;4$On5DD.fh[?B1bgDPJ.c64%Zbo;HTDbb`408_Th'&g_O>*gj/s
%R3!EhSGR?>HEZT`>W_C?M8GJpYYY'SWF6WU"nQW*6_NXTBV>pUPpZ_p)C16$*ERpHj627Bd\P`-A1R=,0hCM]qKg_o\Tu,i)oq$M
%l#AgqlB-=SfDQmgNBnG\E+i0^UE"u$JCI"h;>YDWT]eVE:`:Iq+\:((MUkC0]#D6,bs"(/QoR(G>k4aK10('8^kA_^ACK=n69NUj
%3N6nif1VjE\UY;b`Y'%j%k)&-D4QV/p3U5tEa4%l^@#FqV*nt+(b.o3V5@@Pa6)UBTGJ`K68DNBWqVBJ'a=(ETq`9+CDg+r_sFtm
%-n8i?8r8"kK^PIj-nTFb_n<^]:74g(UFE;4iL7G2c2>@T&#do.7+?k"4*<@7%\AEh9FfkMr"1Efme+1G5X6YE7q$8$#0+a^(Wq0>
%aTc>t;mnZ.P7D-C!u">78E?V9O&NNsakP>c$"XHf&#h&!9?n6P^L]Qo9aR`m:ahF&Xei0$Q",Y<,>260"_>sO$BCpB/d/I^Tk)K^
%)Zk>63-CW1)2s4%r!(8skT'b^9]W_ET&Q1s\;;?ML*<`ed9b1QN7+[%F9[D2lN614S5*IZPY"Z7q[*Omd%Pp_W@OmHSpQ`2U(PPI
%9M/G=4G6T<.)['L(KgYn3-c5d)dVq&0GQqTeOaJKrLXo;H$GgGEZ/ksSp&T$1R(KfHakdMOnpfh,-dT*?D^ddnV0@_6?/3#2c+dM
%Q&ATakV)7=n`[=P/0Zj<-K5JhbLLKQ/S9SK/MhE]o?^de?4!.<kNd*tOlmdmcN_B1:[[mB$(EC9bn!K8$t1#O!hAH[l@$pVk*;Ts
%a2h08K;3kF4qRNW3Q=K&Vju'`W;PENWH2E8S^P1]SaUc:OcVER:)jSfA2$_]5?&toL0-=?A+8%^0TePi5M'Gln<XL5/lgS]>-a@l
%5hceI1)M^1E<,ebaKd>!e#N;p+Eh=u1Ts#u6m7K2L74).\kbq1IV;`ob[Dg"K7^Xp,]_q.`7+=\,M`C4R':^Z4n60@SUOD-^g=m(
%k5Z$^fWfhN\Y+.giVcU-LKEA.VHnD>#EF\(hK$)083&RHZKJ6Np`?4$.%JFe1+UXe?__-YGTjn[CmU9@X<EQ.$`[4s<at$R*[`E_
%_F<Fbg8WHJSr,.f%O?jNonu`/+nf.KW1D8;fM)Gk(3DqUp>qs`#gJV0o$WUh!KIVT,K7X9%6Gue6k`3KJ([uP*.Vu8e&`_SVRR%e
%F@%q,Ct-D?pE4r_F3gdC\Oo=aP_:WLSihUWQ!$DQHdb=E"+lI\8]s(7!-?VDQ<uQt;(kd4QBVeK$V7puBjSfn]@)=)Lrb^RLo'[N
%PSVE*"Hd+5FiAX0"dH1GNiKsH8Y`/a+CakSs+jp\U*hMW*5C>?MHusLX=^B'F9qOE-1q.=U>63@3m"KOf`!F9.9QoGKL[$-$Has4
%>#6$jASBao4^h`8cHQdYff]uI+L`Ts4c.b[E(.0pbt-YE/\-$9W3lsA&oM.EFlL%8j[9c#<HTgX/0g5n)T\O.MbmEE^:ZILFYm!b
%@/;^`;bi7ic+^;A+4K#;W'[u=ajJ*np83^6>3Fsa9br0!@Vke9W3ojjNZRh%/Yia;A8qpu(:bT5WoQ7@N%AKG`dj/)K?Q/-]/oTL
%M4.>UJlFXWCc?H936Q'3ScXUE+5hH[FZ(MG"43^^Pl$YZie851/tGnAY?3!s[qb.fY!X;c'K[OO@ecoLBF'n69_LTFn<3im)T0Nb
%dlK:Yd>F7eKDnL<(m8`G4hX2C%2PUN'p#CLCn&hZ'/H\q=Z41Y[)r)&9IrU,69N>>5[SEDmM)k?eT-Do3>M+EU`*!7rLO?I1B^_A
%BHtr6I@7F$a?-u:ZEeT>5VM.mIbXu_m=0PJO-9;E\\n6fj!Qe3Xre[UIW>)Bipca*dDa\!kCm03IPTfWN4@DWO<Bf2Z@E2W>WRBg
%A,44eQTi-/I!0r5jS&07iPW"V0:s:LIA>W94$J(6n'C&*Da0AFms^HBdlJc:aQ2R+<07.h'S>YX<c=]=q4JrJ8\nbM5>=p\Rn&`'
%dtmUmG\aK52B5-_BgRR56p=7:eOmRGn+2Y>a.!=5s6ou^Wm(-9\]4dVI_$Giet)SN4LtETUO#\Wr@:JWKp18L,W^!c#@84<gf]9J
%0g)WlpO06)_l;!uA+)DIIQk=@1tAH/K2FaeW\99"9pVGnXImjn[--$fN7oCGW7@c[j4*trjS%&-S)?_N,Q?P&H7$6LKW=$di^D(<
%DI)8#$aP*O]%@rs]cBmd5>?:B+uqQ,GE*k7$N&0oB<Z&OMDuS=[l.sMVM)K<p5W)?0B)-D<1EQH-Ab!XS;:X,P!?6GGe$'d\O\lm
%2L3e!cZ?V2j4*t448LecB"bB"&O#(uX1/"hKLneQ[@M-cc`KnZ[>FJpmdE..rjWqrRVK0$BTeZOnf5R')d4g"1DEhm&sBK^eu"J]
%KZtKsRlS-0IjdgZVV8,`[_o`/]9_ZB7$6qlSBE'lc$dM+?:Jf0Aep0H/De+uVk[$dD>\]gE-O;S7nS_gMUC^.Z5.<,_JNnghossE
%HX/W-1hsku5cA]D[[?_=P?d9S#P"-PR)pr&Ih'4X_`!81]V,^1bdM.eSJjn"9+gNO5)%Elrp^L`!WHGB#_tYCilA%L;u6Fk=GN)Q
%`IoZL2E2;J&7WL+B&hGGXq\BK/LpshTif)CXb:LLH/OL_#$uTV_4\pZWT$Ys(8Cp(h_Q)'raE`cZVV$l7>UN5eL845.l[a6JNeoP
%rYFaI"V[9/r=55d"H'nOWG1mqi9`a'+:8@VOlZS$Jps*pk[9m,DSiBt@V_.X5a,e'$_DJQ[*YTA0k`3`(P;<meVB\V"JGAF-",XJ
%]NiA%_b=Nmf0s<s6E:N`ldJf7c!.uKjrbTDAi&8.JW"\1CosH8h"9"WWK/-RZ2gn'!]]jh?7)@Flm6>j;+DfN`VT(#Mm@pI'cJGn
%J"K;u\:5:I>kfr.N^`81]f$X-h-L(UMTsR^&S0Z.qGs0O#O!+Fs1O):QeYr7`VBt9Ah8Yq"8sfsTS?rG[_&s*T`f]]DU&Ajo[X?!
%?3RR"5U5^Z@?(70kkjX]hQUB?2AdK?c@I?U;dhrt%dOl,<63i*)ssq@.Ngq`)X$t.LD)L1RaX9:Mh-/AVEt<q"E4tc@ePGm#lWu,
%6<!8AS(pB.X_7C41M$sl#Aj?Bmg_*a&7\o<W1tS&W8uZ'hmLGAL+s&!GZ_W/+H:rnU%K<L)bO7<UCH^2j\R;]2hn+Spp&45fh5.$
%X%,B4)[XtGam]upel(_S!/hnK?mtW@A9MS'j9Jm&p_,T89A51YU[V;>E(jul$+Y=@gJGJ]I?QPbESh5YP$DKP/@Hmmk?PgH\B6B/
%,=O7OEU8bN@&6?KTu5<+Y<qO/7@W!rK..9X])&+/cF2rbU(uGW-=G<&N+r$&U8aO<3+h*$Ws>Vn();*-G];9K6e>YGHF"V"$0PTP
%i]rfn7$;F:<4c("4CKiRksbJ&CLiaXI%!Kt0((F`GaO2Wm3eN4WeC)`WqDN#[*5kBC!pQ0*q+j0/YpM"l8Un4JWHR10<u4%:FhIo
%/@baJRs/)iOGTmLW?ECO?k-3=B%[0^SLV&SmrbD/`3'YtQLK%E[Uu&t>.8(CRq<F+6i%k&Y)^rig5`DmVEK%:To0$cIP:bmD2Qi_
%2oK\gF3?hu"MAn<N(M4s,JQ'2L,+Bk$W]l:`CJPLck+?P&?"JDB&4O8-_$-34*!.(GJTQ<FTA=<4ioiL')-Ko#KT88^jaL&fOgEX
%gl\oETO5&5)C#59)CFlHUB$Z#J]Gb(@C&9flOuZl@20dRZq]bOs!]Qj7F:FOpF_V=Z!r0M`CBNj<DO=#>38TG>$R*'D:_I+Z1ZSY
%p#=E>c5^YB7=d@?Ad%Vk#1D)qB#s5*dD=3fgp)q&N=&QYM-R7\Bjra6Bf+aC%%S8)"q[(,J>r/C4PM^1j^T9/2<`[ANubYcO<P<Z
%G5f>`FF_%-GlI+Q%fWERTM:Ug`2Nqa[A2I(d?a/Leh3D9HRB4F5217jm5%k"O8:hW_`3@j=EXe6Bo^Z9:?.4?le$(8Sil.sT'gl-
%hj+jb$\lo;T]:G9B/Nq3k'JFt_9puXl7fhQ86KH%2G;>SGPbBU7)/VAS#=4Z'f)*7GT5[*p\TdMq6j[X:DX%9mLAT.Xc@6[B>DJU
%Vo]Zl]1HTl_X2PH$0*Elm^?lBTXZ"a4,p_M2Cj@Bn]YG$^AXHC5.n%i_F^T.3KQ^bL?[dA8g1E'k1$!83>0@49kWVnSBfdsDJ*uj
%LXXN,SMap,rhRm*\[N.YkX3+M+0ne,m"LP+5.<k73.+bV</@PUAsk$1[Gj_B&dtU#CVj&Jk$]c`jW7L.n"$"CpVACWk8$TE+F/CS
%=q$=V`8`gZnRbAHKiqsR@O\$QR[\u[m3d2MAP1B8XBI8Of1AIiE?(&gM(V=th!MV'%$D:NVkF*Cf7ngFo312--*V#%mY+mi!gXMP
%7]IIgHj]SD.L"8/$FjhHl8G[r,&n<mQ!?R0[\4Lbca+8H,nJ_HJWJn*2U<np0ZNp2)=4b$<R[`\gNI,Xa)"pAK7:-A9R,e*oMU%.
%BDAMINDJ-4@@P(I:XNEj_k*+s^4TW5U;L&&0N-2UI@5X<idiPKVursTcnV[04;?Ca%0AT+^AF$V=.iaZbnbmqnCD.Nm]-`ZX)^3)
%2okcJRp*j-C&s7]Vk:8A+I$D7WG$_tJQ!EKSCL]YVKljke&oJtj%]^54,<am-ZJ3]g(k_I_Zd7d)Xu&".aQQN&W5;VILq$J6$o=J
%>SFX3[L"%(9`"VABL!kc@d)`e[N/q:H+Va[W0dT\7o;9,e\!n6@;ZMLJ*E/3HJF_k-h&G6?!U-V2q9pVYO/K<M-f,p^.?Jb[-b71
%\@mh1Hu+f-aOkgQ$J$A8*Cct\X1R`]`8%R\HfU(Gj71Xcl%F4Y:fPD,3eaK?TCta5\uUOJf3r::(J+C#Tm:X)$f4S$%CkZ1#p3I.
%9:a(3e6k@WUUkl4Q"FPg(Y$!>2_X?sg!36R0k8B@V;hl0hI6.kp+.W">/YGP`d(bSjVmH!8XI's+sV>0792CI/94g`UaHa<L6s_a
%&U`m'1cZ)4S^?p?V5GD;Xd>o?/9G\b1;_*1E/*s,QuB[/Xd[-J5t_*Q>%4of-UVl#ofqtaA><cfYhBP!)GY,,38QrP]mn]-lLsN`
%bp'i)@hW,j+.s(:bO.'c+O%?<-,Tj\p>QrcGnf0OMnKW68HU5_C,-]soa\JLNAWd5M4)A3IfeB[M\Bttj(u9Y"<8Tq=bI?md\cW&
%2]r7ES'a27f7SV6<^ZIg#H*,hmHEM<_pE8?=k1N*m:8!gqMh-Mghk%42Q^o,)/%-6Z)!B(3-U+W1i10T9a58hj/ed#(pPpP<>LH2
%JhR(gg\UD]6ZD4%1/LX-$p34!A#_FQb\UO;HWrRP2B*G?Z<u$6XpQDIf4)AFG^sdD2P0GV2D4`H;9!K2/s^*ifnAsl\rcK91tShK
%`DU@TBSPG3p!<\o2Pf`4c/b*336^>lNW^1W6>b5@Fe`qgP_<'fp:3Gf>/86ng]^;aS*O:/hR!Co_aoP25Ce"p8U7!AfPJPsqi>'*
%I@W)2\:`8!.a-5We1.-D2H1.)qKB.^k<;8mYSh)"BbQFQYfUK>j5"=K4>d7_2o4eX&*'m12TKD?].T,,?$B^pjhBh3D4H-?brCnh
%"1T;:$Y:1gE&]QpEf"0PRme4&'pHn00pl@_1G`u)<WWBDU4hFFmU<DQTifb\(A52ipOhs(=6j9N)Aa\U'sJX:B5faI%m5!0Z*@,8
%WLM:;Kj*:`g=1pXChMuK(5qqlBSPGjDq\BS_%s;(3%G^s^i@XN[@bU*eMsVe##FdCf4F6d`dYb\#n4^d/FnXTPegu1f!\0K(q.eN
%D,X@P$9i9q9#.a.=]u$k!Psb+%4603!P3-sKk,dhB`t1X4mSc,&BV19n>hS);N;+Ka*ed#,>"c>C1DQX`h[B3]G8eScS"k9fVDK:
%@8kKA@h$=YjSSgiZkI>F)s6P;kmHE`9K6a"P+u=K%I=7s`kq5m1Rc`MDUdC&.6()\oJ,U?JrBiKfo`B`)@sD@Ho`4;lpR_[O/*F5
%=NW?V9p6I``_?K'm7t'MYgPH;3*@Jb4!1u#/u$J1EL8^pQZ(m/]'74831Dim1:/VX=cheDZ:&^bXkA(\"H=/d@Gb$^0Jp;o2[?k#
%DbbOYD$Hn4RoH&0Ys-h*979#9-Ms[4p>(0`<mi8rHg`j*'CMaCa;17'[Q:8\TKM:JLBM4,_#A0T:,._U*Kb4p9Tqk$)`X%$)%,eu
%iOt>YJ_eXL;na,/q1Vs)9b1lLCL\hp;iRI\EN2ABm&'X-I?lH8f!8P_A[@4l_miH`Ro67,FgKLn+6,^&)e3R0Ib^mM]<F#X30QdZ
%@i>dej$)S:M/rc:k2sIWM+fS(h3?Nm]LV:_0KO'_R-IcqA(f74C;4jHDdZ(NG9iH6Z\`a,Nl/jS6FJpkCn/!OD']tH6DMN1m]imR
%F22'*VD%.FYKS793SIR?=)a[HSA+dsb0?BBF2)`Ol[:U4Z4Y.E%Z`^mfNVTJj6)UTP?2.=KZRnl'B`Z08BpC8*$^\[)s7?AX`spi
%Waaj]gr[R8`0\(\5EkqQ.j(/DG*X@.8+oIj)%/:g*3GTRDGuAdbF"/pp?Q1$r/mYJ=gPS8'o8XZnlH9+-aIa"g'MtemLJC',.S%#
%\<0*XY<g/WXUATX&gAThMcZU!f%JSb<fZhZJPW,dlEKZRouWQ"Zhn1]H:^00d^,#2NI9Q9GYhkYg!N'KGD=e,/S#S!-ZaLVMiqgn
%:#k.3+[58VeRA:^32SP-*<nLZ=IJ5*m*,1F/`EXi82WS;2gfA?d<0A/Rq7?co?rSsGK!\&'=_38'0`i0]%mDRqadgbgigk<edK=7
%rfkKH/)](%2"]V>8YZhDG]XGG#=@meo/R0>'HV5t0NbSnbai6ZO5;51mMop:qt\H<N;@TV>R%W"LH=;Rl[5<O7)A.*?rs]9gOi=J
%>m2=JAn8upJE)$:,[<'Cbg\tj8&+dcU4%Dna?jX+mHZUT#3rWOVGa08pi*/U/K.jK?*=IBF%%!l2g?i1JRHCRXXQLh5-r3I/CXL4
%[+2i-ou`qQ",G<ZVrQ1K;6?;6>\q(3PQ=8eBP^oZgL=*`9A&@?,?:Xl-F"?ZBEpLgfS(#HF"1FF[0-T&Bk*N7WIqkeO7\P2LDO/5
%;mAVmk;O#sTiQVL@43J1BuQN1*ja]66R,5_MI!uRh9MSTB?L^0!dA4jFGhL&k&@5lE\>o)`iKNrlO>2(b1L9/kZu3bJ752nmWbAI
%L&<a]=YFoRbOH2o`n)rKVd`>to?&RQ@D?$\Ks[:=bI:.q@ObnlOlLRl$pNBKMC(^M51Hul8k=shGO>8-\,A,A`J-pCk4=mAN7;sY
%]aAo>@Y8UB\#U%K,B&A/G57I$es"k1;gI!I*?W8Q8rmt,DW[^.X[c/gh%)6>S4O4^M'lOdG2h363/2*-75s.HjDJouT8P_BbU7[E
%g45r.^DN*b/0bjO)Vdjs`=f]L7$=fE[j"Dkkc\88C3,jj+D&tc&snIf*7(oj\Y"898;<hXV0K"eFqWX:-^:\rb]BKD=S6,\:T$V3
%i9eI@9`',m9-o.LD2OOCXn@*XB\=_KULbBh2]0i:)Lq7R$?.gD9!Vc1UaWO7fp4\"aLn"'p$&&HkD,rSP`&%cON;S=]!5AHF]3mt
%\KOP8KJuVs_-J%s^jA&\.!:F%k\'6?`^<>ckKut/p>:h@0skl.&^r;up5oZ9cFUp<Q34#]\AC%7q_:i_Pi2bY[u$\K2]->5*GdaB
%iEkc""<WbVHJ+U\1'Gu2bd1g:KJ,JN5kR(jMh:40Nc#T#=CZ[]JI-b:1M"`H]&-hkDVO%,PnEs3VmUOt>C7-Z'f,4N=bWZU]A"Pb
%c?@f2'Pt-(\tddY:>N@pa4+XQq.;H7`caRgaY`AU3-Y&Q4<r9"E<;Y2md[)FjMR4]M<kF60ta&mP<-CLLg(ir-bGhG!IS,75b5tn
%",!.K;eLdClVY,ci22P_Hu'NE?3W/,>8@"F'fVT\&F.Z=(bM5SO/Id#dF;,Og27BidQ=&9an[l^*L1qs;i>NF6^?JL,ZNBbVq/h)
%+dm!*#+U!VK9$,5QeKJhY/*5<F*7@&8'@qf:P[R+ZL,T#4(gXQ,*d2i0t;m8pi`"[*o+*jS$;*:;$]2(d<Ipj&i!.<#FmSm>"R8,
%I)sq4mu86fb\SOl-cPEs1]PPa;gG>f.6k%?0AH0%\<F(6`LUsr/+/)V>B-QHhhjL=I4O`=lW\A,?/Kf9]3a=,lW\A,?/LN$iVHU1
%$.lZG::n4TGO50,gp-0L&\aL_CJ8$kb/BXp^"J9)iVHU1C"KiR::S"QGO5/QfJ^U-Y&D:ME.'8)K^2i^Vd82g%i61>A(NYRXb^M7
%f=0a7\@r]%_Y(%@e#mW.ST'!-mY'n1EMm+"MEBen<nn#5\)L/p^24rmf=0a7\@r]%g@_SX'<Z8mST'##mBl1`DTAnu\K/;rj`]AN
%/[lCrDn2EXIEQuS'ZtL-ST'##m=ae0h@c]QlW\6S>i0Q`iVJkpC"KiL::S%"G/Eo(\!*t5\Xd\fbO'uK>Ag?EhmO&rl^JU3[!e/+
%3k(<UDn2EXIEV$1oe!Z%Y&CkAE.,psK^2k!o7X(.>Abfnh^$6D*Pk-cIEV#blW\6S>i0Q`iVJkpC"KiL::S%"jb2r(?;A,HD')fV
%KA953^?tcj]>!BM_Y#Li'<Z8aST]E3mb%37\.c#``LV1#/+/)V>B)#qho_NE7:"g5LV)kVE@5#KMEC5%<nn#5\)L/m^6Kq[PeC*%
%?%#$iA%7-I(>c;sB".YZrJ=4e,HZoTH``67VmJ372/H[`;NJ4@!hRi_#_&S7_$&Y2AIR@0AIRI3A[[;k<bD4ie]Jl^:%]lT6b9"'
%DXn4-eB1I53L>&fQ.ZUCf?2=4fsNTiEOT2:mA*e&i2\(>dr2.LJUDW-.s$[$/fbpGAHY-Dap<5a.jOhO<^oK0,HZoTVmJ42+),;D
%4/%ogm^-"!o'Tg7K*Ap>ar#APe86S@1<2?bd^e_IPsI"q=rC<8KN=t['3Z:G`*Ot#la@7OUm7?<UMFFa6eA_$o/LrrbWI,]<lVqW
%<b=:i9Ih@._C;V2dGq6l3Q>j87l=/sCg1Q&arE98dt,(D>+'6::#IJ&$jL7GQ,1i7[5)ngghUK0P@Coc==\GL2,u05RtG#:E.h<)
%7N?2Tk#ukt#c`9RPsP$8>+'YJDST1SRCS6R;NJ4@]5a=@H`WL#VaHtBNK[P6UH,1)(V;>j^e\ZU%,PZ/*"r0XJ!-jb'<2A$Xoqm]
%*Vgq97oS:B&YdL@=Z_g[-F0?1^o'#1O6o-mQV*A5"0;+CS=D/1aKj4=8/*aVO"Wj`,B:.lZ.*XJR[Q[\8NTjepD1DKAsn+-ku,*h
%h%H)'3!u;ln+L2_lq'k%c4mm@=LEn5b:$UP+cM=pegbe/bQaTE1GbWn-m0rSHQS?`7'KfUdr3?[Rs%7dDn0;BWItbi\JHXKZnjk_
%P^r^m:!u"/)p\i@jK*UXcLf=5!Y-Gh[W]p<)&_=nO.OlI_8pQT9[TLsBkc76B?J;Xe!2_C$9*R54-`o[f4C_I.2MLu?(UmXWmtB?
%;pH(h*9McGb*c^L%"^.?HR8=3l2$]\)(uEJ&sU7Hs"J0Fpc:RKWh.d>Y=UZOPs(AFUH4?K9H_coVOJH2h<8H_]^'10&TOnFh1htb
%W_5sX8N0S9D?kF<bpXGF6.<;B$gC6*k?4K]]B=qWH%LSVSWb*2!`$<EE?$B1gTI%qb$3&87@O[M^=!'Xb*c_*6YW(V3rfp]!Y"*E
%9TBcs<a')M=116->rs8@7TisN'eZdcQ=U,q2:_9F_s4$?`L:s>4;;NPMTJFZVaqiN)=^S&g>EfD@$>VC`k-7\$FOk-h+Y()iu!PS
%-]p6P<?8Z=EaPI2e(.4W?GK,e;gRTa3YXA=e+j"aH9rru'.fBmH7[d#Y*UT[(FD/G\uMU)`\t%sk!7bhp>M6n::mZqWM\,!omnCI
%Z5M6d16jj<!Hu0#%*j0eJM1:^A%A><bY9ac8]:9AEDn*L<tHaDl"-lO_ZH:,A_r_b^!,2TC@C>@`A_S[NOXo\L<PZ?:>N:M<&KR)
%]CmQ8Ha,;b9n+B;*:%0V:UaVR0%9*!PEqc_[*u9_B%gXu*jSkYo$\oN_QD"gUI$\PkZ18Rc8P@m5g*jGa.3UnX;rC,;G,d"64.WL
%Bg]@%&qAJL6VNcVBJepRa3cn%O\F[[$=L^Uj(c]r.-f%4<)9DTj(_``nC:L$BQ:7$C8+t`E1F;5>cfoV[u@AQj?;a`,;\s4IY4[R
%2f/g[Mf%44/uQ39+%YF<^(c"SUa/iT5aj-'-Z#h,kkIQV9$XKE@9dAu?%<m:e`(oV,IGSRj:,Ot8N2"i7[M<rn.^mm-hHCVm26"o
%2Y<!CGN77i>(["-L5&T9(t45DEe%Atkm?fbdsA2mFnV9tff%Ncf<k"%_W3TAVTj<uP3C/[l:s.5Cdgj8VqKMU3_bfn'_9VjBfaVA
%1MqA<6aQ$@LY6'+6sJMJ9dcAf'WK1+Y$e^d-J)jKZG0Khbn!kpri'"?RBmAZoBHnRV@'BC3c1fs<H4;te>l6/::L2!,\6.q-o6XB
%n;tT.&=T]8@:$@tC/;Oc1uk)q_lDVW4<i^D^s1p[pb\2bg9I4?)Sp[*5RR*`DHBaS$Y6>#[7T^#Dn/ZOr,1m;bDA<I;<Ht">c'Aq
%j_eESR[\OH[</&:<+hQ^!H)2?-bRX@Wn<g8bJNVF8"6R?W=?;*&!puPj*pAfkeLeroF`-<cb%k/1me+=r$^%)]u-lkh36<ppYHso
%]dCrU^-D1GV$roTa"^-.lngkV>Hct9&#X-6h>SrYP_tM3I]8bpJRCH34(cL'jU!Y'%dhAMmHPVGe#t6J^g)[Lhg1JoVQ_f@pK`Rm
%@q3!bQ!Wc7;Af#Um!Ind`&m[\Nmg^5ZHbm[AXMXZ1sWp_jpBd7T4Ao,SE?61kHqItfZY;Wd0\Q0k6=+Qk=2G7F`(/e[XcWqmH1]f
%JK",$&1o3qDBJ,?]\Q'*Xa+,B6=X9`f*MP0*D]o*S6qSpCIhn=j)'umE%</-GaG<tWKZ9%]9Kc:dX:>sgQ:W`@Uq"c\UK]Y\Ht)J
%A[d`EpsqX,p3BoF2Mu3eCLW(b.DF"q&<.49V@u$Q+<tj1l-)U.45-%S$8Q^_P8.U02cc"H?TR;!P,E1N$]45.7lLffIFqgmOM#eL
%2^IG$4M(3EIbS$f>1XSYn6cU8aUCL.lHR<^qqZ_5o15V2[blAA]`DEdm3!(PZ%GaF=R&Pf/(.r6=o.*_Z%FU0hhGs;UNnT<1I@&l
%BqA@8nYY><-0-_LR^7Dpm\a7/21tf]?;8*s<J5YMG#chfTA-h.Vgd9Y^0l\i?*?u\VMcgLClh7fM32"gZ/ah,)<c`n,95$ddBmW]
%.*^mlC'o`_4#o/U&Fg#Jg5]$R5HI?qSF<>-aZLr8<kWLQrs7bV0i,t%3;(r/[go!%SOR)<?or1Q;N29[`1Sh$OA#Rd;U-;hSPV]A
%SrIeI^>3;.kOunT(P9.'Mbd+[!?A6G"c7%AZOmh4``9C]+.Bo7Cjg#5RS,HEq64r!;Zm-cD#$+]dS".Xs,j>UdN#6/duSc!=(S^)
%(d?c:+`J@&+;0)&*#CPO8:k^HD+`1fo6bKJ,he-$^:-hp<iOI9=Tbp>/U"_7"_WhLhV1kLiY0.$DH?*<WpWfW^`i5G_.iZYWp]IU
%+\U$LDq"2u<,IYiB6FX?,NO<Q<o"@Vhm%%]%!ZmT.!+\EKnApDpajhiUTpoFqIYad-fK`0UMkBK4IS=M>'pr84/XGVjTqc>J`0dH
%&f&>?[_&X*mJq.8fdTS4/N5cgD+i.1Z&L9`J=!q7iBg#>m1:'hP"e)Un<?@/k=sW7_/tj*h*'%1A_Go"C^+ebZ+43'H^l[HZFe32
%.Ot$YcIUagpUpU:IUWDOSB>LU7#uQdZTuu7/3@4H5e`EK=9aLCDb"*P:]lKEbZjDA;ePH>a>5\Y(jRdk4eF:&q&$#2rXn!r/A"N3
%rin#djs6^05hfrGAk0ZG*S"X0*0k^2dTV[I:&e#a+A[![:CKH](Tl#dXYXOjGrK0McI"#`AGkr?nas2QQM]pGm(N9OPZ2't%EVj#
%Ft6(#0%QA;R3`S%VWBd)gFJHE:X+>+6B?nAnabX`C0G5qh,rgr\^RKmFO4o?-M'hoR[VB_&MkF5L+Op!>V_]"@-GnLe8FVUQDg;@
%ggNl<"P!+UW=gQ?NDF#P`IO_gbWG2PL%kC*OKS3[NdH(m>r:1F%PhfBc_Si]a:q]<]hVtiA9T&dPMIf;FNhkpODT&ge>?qAQqT0U
%FYk2uahjHh;Lg`j[PHg*k@m(/6g]%oM=hT/i>o]lVm!(CKY"uAW.@PPEEjk!F#IuTP98bnDecK+SF8)"fG)NKe^g/0J-A!B\7-2r
%V2'T2?mkgc@-?b")HGBR=`d@*!84<&e7&fqo].mpf,6H,ld>DYqKJQ,D=@A&`fDJ!a2=MZl0tCfcH)*X*f#uInS)90lUr<$5Q^?G
%TV&MBXe.LYf>oLmit4ZT@o@,Gr:H@!e^sW%[H+;>qJ"HU6A&cjW=>8f7pYo_>SfT0WsaiVlU#,d(O)8HHdn0a44:=,b1`'9Yq(t+
%XOoZBT<bsEjA%W?Z)"@,[d_Ua]d'L9Uu.%AE;h^Y(RopPU_;2>fBRgmnB]5+SYpDDp=E@6#IpBtEHQ.)#/,[EROGjm%u+XSAb`.Z
%%J]!rmkjsQrhbZmkf]]'Jp;a&jB^6<q71\O5/lKNd=8:Qm_V3#m6TWG2Vd,lo@sd>auToVKDp..]S&BHCHp!kD-Qjpm5AH8=oBC[
%f%Y?^9&>Lne$$G#;W2b0L$5EuX(l]nSs'm!Si.sY?>u?slUL3K^W'pR5l5)uCAtWb3\=S!jSWV;X7bhome,-kW<^7QaL4%7oP/_9
%gC^?eVnTN62A7?Si&m(ekKU#qkBS<^g\n_/h7ufE'BWYg4Lu]jdp\h<<jV&Br[$r45L591ICK&+1!@;oDDg_RGdH88UJUMn'_]-R
%:SR-?8V/4NN_e'62Ti"C__OFD:=JGV3A]i]pj&9W-&D<9VeYm=o.L!O4'h$I_R?8N?(4c9gK7"sMJW!3P?Z(6?Wu2IQB/Te83&jc
%fA#u,lgigN485VMj<U0eioa%f!Kj"?mHH(5Yj"25^T5t"H7JAUb58@=d:KbYBA@^'EA'W'qen08Iul4+Wddd(SDT<rou/B5E%]Y%
%F=[Uf=j=X,T)6u^5!:3)r`g(#0@R+ToGK`W@^B7q29l*17f=deHBbdNf1&Bb2>4Nbm>YWD'A;c]4Pr&n.8l1%q`Y[qlgii$]UuAi
%8N.sGIFf(9=Z2UU1XR-#k?irAI'm-R-2Du@rTH$"cSP"$ca];aVuAWB]g#&R4k2bE8'^QmDS+[&mMCBEGCOl'mG#6s0@/a.B=D4i
%r]r5fGjCoiS#aFGDnkc$7q[$i(Ur#2\j$#XlJfKiQ]Ebj^3TL#Gq:&oHN3hZo@.%D:VM9uThkOF-\(gP-TAMYo/!LHM&t[1C@BYX
%MY#u4O"\e]5!6Hh^&53!].'^B]Xg5?G^+[\Fa<r]Haf?&rqYPh/b:(Gs*jJsmI8,bLYr-tqI0>jqnoV?+$KPunRjd^!4%"O_f'Fq
%5J"P@^0?N*G>IaQf#Ct@hu)JPIe*^"/c>%3X(Ubus7Wf!]K!\'`S\>up7n,'/e7@*5(!.FiPW"fZd1:B&&5f+FE.3?[s`P[[h$ce
%XtDrR4aZb=jgdSOaZ9oM*Q$dWh>P60^H,U!5(<.K_`]GYr^$P'+.RS?Ac2Dim-\)-&FkC6Dk:6!g-3\8:AghMmha$jC0Q(7nXlo_
%s6TZAl"cSn&)#:\H2cRaFio*:q<`efcE=lDgcIV\^V0P]S`'2ub;fHi?XMXESm<?XhH`7Pa)rkL+(cDcah&AP2mOpI>,T/qN;D8T
%q`:"nl*m3ShfD]pfG"'[j(5Oa]1fGOJKK>bI=:haimAp`Fg1mXh:LLE?mkQuliDNUo=n)/VGZ$uL_$Z'3@$O5-[#6f(&P95puK*8
%"s(r+CY!DVra\5UppYE3R!O5;l_EPIa.Jla!rTGFrg^rd-;J?K9jSG-T6'o8TA0sJ8)I-qZ/,!L62k'u2>)1jQhKjWDgYgiCJCH#
%qbgbGSsNB+n'8Lr)ufU&h`SJThX'd`HR0aXqqSr^Qe-YXY;4[)f&uqZb8t%cg``If<Plp_Xc;5?gZubhYHKS0Hbs"eI!tUaCO?c*
%n;[1GY+@ej]KWs>3;hOMe^_p:el2.AhlJo@#[;5\I4e`(J,68P2ijS%O8f"HC>G7R^j:$+?6AqJiAZ$EkVI^<qI^?-cHa3&QCkj3
%p@Rp'Iau^9UM#p;c[1*U$kI9)FEW$*]Q_(\<VPt[k+Ted1&_$0q*@+jO53%4I'WGq7s'1S_YX"d:SSGm`oK=C3U/ipheE%5EPtKo
%<pKNAYQ+=^BDp()8+Qg(=MH9Mh_hO>qW.58$?$3Kh:6F^+5Yi1I*"[FD[,N0hqWW2LXMsj9`=kUG&_bBrREs"%WB",q=MeZg"6--
%3W-#ReuAK8`?3\U2F%#G%XVu_f8"i<]YS_675hglXCYXdI`3hU(AaB(gIkd=n6NKkhS!1k';NZVn6.##q"WhUL[?HJa5=RWn`8^+
%dF6uZDdS/<j8.aa019``hfK#Ae5sYTdVo25QR.Z+]cboL51JLHB2ZNG>[7sT#/ZL'B[+M's7#opj_]</++F6tI/;ad?iPCjlM,Ao
%nF5oHi=:otZ$uYV>b]i"5E^S%]DEZ\lR[NqWmu0*^[3Ld\Fn?Kg[T.\nT2:]I_@?1*tO`9L[FZeYG6<Eee5`/m="$5Hj_6ZJWP,4
%'7K)pF(WmR;pUG$SmV;3DMbNlDf!R???XYaB?b(W6ST]u'u;$P]`le:ELqk2XIDu5@E\9Uk%pH304g`G*pXnjX8VE-s7l6G:.3tm
%Nji(MXe:Q]\\3L_kDSM?W^/PbZ>Pi2BdVY,YrBg[=VYo1SQBu9q0/)EGQ#,\mVR_2"Jq?7m<RQ"^NjXJ0!G;f^3bH<]]UPSRC?aM
%o>dbQ^loq&Y:^oHLUHgH)>2.D;;n^$7d75AIIQDEk=7LPA<2urpEqq@I\[EQ<n.^q^D&B!eSh'=M,&3o9E=AmHKR(e"`Kd^AmK>9
%/hbeL%u%F%fCTj2[2/4Y0#)kuSP:L*Wn@6B1Ac8k07NTE`V"-k4o4J>N;E.Yp`B%DG,]]"3'Bd+HZRo]*2UC$G)kW"m\:k(?@?BS
%qrA%[2bb1_'5mq*Tj(QC4YXR7%j<)=H\iBQJns>&D6ASe`@^"HY%aFF'"%U#3qgVYSY,]Fp(A1YqO:jjq;V=?</+bSLh"EU#GQiX
%Ku5F'nUCN'._U;"lfNDYGMi"Tpk+fV07!*eOOh1R<Q@bT-b!a?:]GZ\qtU%qL;S,be_:sZCk[,BgRHYbF-nW)07M159u#eaqR<RP
%s7k`WMgtQ?7s@b<nEnKVg"jUh5$lQf[@u>=>at`Vk4.P?fX9j[E;f.nK:=6TlEMH]F=leBq:V%Gio9ZZ4^.22S*tfSG/!\$EQ.pV
%=3U,:nbX!]r6rc.ZmG0II"$F9Q&Ai--^WF,h-=-SFuB8s9oXM[n_FG:f,)%o\[)U9`keR&9dSSqY`[rQH$F:LH[To2<8RQ6mNq5^
%VcW'Dh*4S=V5mR[952sJje7Z+q65:?`YW!RISA76XZcQYC%Lr<p%-;uZb3g&AfcD@p?:X75KeneoP/`,'dO`HlsK@epRJ#Fh:nDH
%#Fjd=5h*/.lT9r`P(h)\+]$D/C#84^et]S+0/k*5B33]G9YfX4?ejH9AW(qYEI50[kan;M;Ih<Ob\(#'DDq`k5J?1%:YkGg+=#VO
%)t9cg=19im+9+:TS3OQ3qXg2#hE@"`r4GJu]L@;loptSYM3@R]%*:ktf6m0\N#!&)h*h@87"ND.BW\f+(#-qsHMI3/ZR>(:5<Qal
%rJ-H:mnVMcfBIXqDQAg.+$R<J]B!RQIsL5/cSY`)]m7+r#L@9C#?ObJ]_IL/SQPqW]"Rn(h:mkDmJCrC6Y"NA*hD,X4Z#mK>OPg#
%Z#;jSnG`;X(:W!fGjXDemJ<IT;?#tFe+`n5i:<6)%Jfe"qCc44^!"ahhV\<nqu>RR/cCN(s*E;[qQo!Kel-]KLdJbbS3:.^S.P0M
%a[%QG@GR\Ic<'hfcV*]V?ZL(tP"VtV]]?fAna=lkr24\&n*<%-C5nk:SCW1nJ$7<7rrDYGomudsimrV&5O1\inV?[3$&?#Lli':1
%^RnTmM3[qj/%\^.:U*fIEjsSf?XC'Fpql[l.t4qPmf/Ym9+NZt5Ih]AV<9<\55q7biTIeISa6Q!a7i*ird"7_c2+enfoO:e#8Q'f
%E`rF)rMXa$_Ig4T]9_']nVHZ/eb,[d]&VP_6p<ZD]"#Aa@,!mAlplp'hLG'R^\R)`8tB/Hf94"sUN9$U9.k1@0>>5/?>+oO<Gks#
%Nefp4&;Rpgi0Vt955aO=kJ,.=rUVZYVm$"r%au*Kl?eQrm>ZVoipuMH<9o>(ERqP-?N#4b0A:dJl,UP\L\KALhYC`dm<oeVVqemV
%]?%5DKo$P9LA0H^8s:<EMAIL>';<JC\+CqgTK4Y@kSI<h1e1J'O'Q\2`0$4D-_9.KI?@`ajpQ>OW_QZ>'9;O>5>RAf'<.>
%9Q*&mDS3;JC'eTHN6*G83o(=s#s5`>r9G@ASOX!9/SXV>pO4!So[/!FhRmCnQ+`roj^R@_[ffsjP6V[+)T^U>^0B6`f0;m-Y2F8%
%93b!M]jIp,=Y&mVk$?_[lr>5(`G$1O3g2'hW;Fa=\!>g'Y]f,3dC&l&MTWb"Q+R(!GLS\;RD#rL%-tj*Y$mR,^1Npn9>O0/J3jlp
%@Q+q3n*C3n]bc`3!k42C9^,B$^nA;^0>onjlNF@-oX2"<<iuS-6o9c'*2H)^hmR7r<H"&IR27c\/D;ul\4:V%:k&@kXeSNoh%[R"
%C+Qr=Q8V]!<&CJTe'qeQ3TQ!]FbMsNlf@1:Q<B5E$(4A#WMutQ`n_&mE-kXnlRYBC[F?1n=*2!I#6Q;S[`OeO3[[[A)32+7f?O\\
%FA?f0U"Xq82h(DEfZ3l,rS?K25$0c(mr97`jFUh2HB+!`fSAa638n1,H[Ke;]&,<^d5ueKp'3&3s,]D5fk6+LJ+*+%nV'Dp?]$*V
%$I5Q$]YgV[o??4M5JDDjG('-rrPda5ZeI.**^3lk@IR*@qW=8;4;7RgDlC&Rb.BUqcQ6Fuhn?YEdidg-J@lj@nPOgSp/kn?Manjo
%r:02^cE<`VUW-NQs6tZuLcBn\a/e"pkJpZUlN?n,md=sG5C7S*WBK*aqtB_WTDN>[q2sMh4a6nEjjg-\Meda`6>Pq'q7jO-5Ouho
%H2[@G?_-":rPp?^A:F6QWFtb?e)rhADQCcAEj-qkrkD?4?/Jmo]LX\]h4/t?n`Y\<aWCC8-gp_Ii+JD.O4F[8(MVqkB-(C7r<BBL
%JVMI2s-T#0LNeXUk"^3,Y.,13TGLcL0/Ct*oni1o2hcbVlG^49H1odUnC#VRB2@u6*kqd2hb0N7s7#YS0(3htY3YSrBBi9tDUo0.
%CA%D+[?R<_nP?@(r-f)unu!\Srg+N"fHb%gdQOq/g[%.15*Ab5(Dq@Ens.k@;lK$,^SYpdhA6upT7?At>>0P=Q"XS#es?:1`>7bi
%d[kf"3359P8RK5/PJ2K7Ra_dAcY_!@2g;<I+a;GnhHm@/h%sh_nW-uo8HN,W_jb;Jk83TCb2Z"H/cVm,&jQ6;HQi@ak4\>uo<t/`
%J,]!/lJ`bThTh)UnQ00'\.`4f)L-ILqt\4e.T;2\kNVm4^/-VHJ+R)?POLdWrpq?*j0?NV4h/%c=K32$\G>Yr@X`_%%XE>JGB%tP
%G5d(CPPOb#UkK(FHZHQ\GlD0mg\(lU6n@*&[doK`pV5W7Ne^u*em70\pt]!1p'7eFq>K^$dnb>ThX:$>7$VY]gg*+Xr!Q3kY>iKl
%Hi,BkO2i&\BCpWK_3J_!\2+*UOW'-aBN7f'ZSU%fB_R:"G?Te#fos;YB@ib?48\s8nBoLEB2<?;mPcLDiV_j'G/'T<?!TYM@uns-
%IVCb5*bY**m*VR_4rW>VjMn2[r7rDW\\(.qoCi':\"DL4*'QQ5\o+G.i:_AE-oo>a;r8't[s+J#LqhfWCe"_[O9k4=(Oc(bdG6eg
%j8X1m%@_sap#0lj\A>l9%St"MH?ac3pt=2*Zl4;$4>NOjlT6aAXcNMDBK*u)g5$h'TAEh0g%@/k'Jfs#k<Ak+p!*<!2p^pCdVo!?
%qrbEM7Jh*^q-/2Yl?9qAj%$Qp:G6V#\i>(Xn`RO%Y9%!+SUG[,Fa7W*JhM,#]k!V^&'h>m3d^^BFOM3/ro(hkb@ckH+()*4o1WGU
%qB@u.,(q!)mc&O5*Rs'90NiMq\D9f!5.l.:7A#n``p#YXZlVI-mBaDPCPRD_O#DUh2n<u.jY-Yc5?r^5s+j2.hhoJ:]H+\6H[#8@
%n(rQg0X<$AXid9k4IM2OgFkc,/^aK=O3N%?lK5FZX,ep*4M,o#W]UaJ_dEH0iPF(+PJ\b,_Wd'm5G!]%'N7HAEt9<UI,=#WmD/_W
%2c%.O]Q88qV+g4@UHOACrU/phiVgg&o0%KpfcP.8*aKcQCjbaNlgq/iCNogX_iJV39b+j'Q/MtU7Oh?P5)9_.ZOQ#5?TLud;dKHF
%<T'`&536F$LCZ7-nu-!g6iVIXrTNB>:VZkEhd'UgH/<Rchd:9PhGH/JfXBjr&'qtQp54@l.-0MoQC)1d3@qVlV=4&_o)4#<O1*UU
%q,OZDmQ:@s*UTORngjJ5)O>U<`OGg2l!`9_KDSF4o%(#]I2RMMpN,U=CA/0Q@"+m7k+;UikOdgos6*DEo',+^hX@mYq-@>CT@94c
%qEl3UktHN1+,95geT<Wucg?mZ;ubZd:Y$VD6o'5^cG^Uc/=:@7HkoNA";uEH;d8fWIIc>%r=R`)a6E,R:VM#t^2![tGMh)MYg"`s
%4m1ljXQ^c"68t8A98>+khI.j/T&eM\O_hgtAN0@TpWK%rClCL)N2C<$/6aKg[QHcc\K^j\94cisN->*J"r?OLqTrG1oHr1<Y_UR!
%[^jXJ+5;oBUPZ1_LVI>oj`:BJdd!U`!Ed!^aT(QI<Mb6fDPcu$J>d)he(-_@fLdONB+m;M]m=$LI5hJEAc8X_l[K39STg`O5nI^e
%]>nmt!+,:$MFS"oM$;u=IGf(VJDZBip/F7VrLF43M1LM9;*:AR%eOB(B/k=XqMgn>r6+jTaS+2?faHkT4WU1Je;N<V0j"5[mm=2C
%dS4;[9_Al\##at;ZKaRWG[0Gm6`3de1TaoulL.g%ec^[&_0ef0Hm!!'cISbW"69ZaQjb"!kL#Wc(NMQV>V8gb!]*-i.boH6D`eqc
%YAFoc2+6\n+Wr>4@h/`40=fJ[7OJT)`%uauJ93liT-$>=QZ[7D*WPTeP+4`;7aK)W?r)NA7,Ml#@Q1uO+FAE>otaO]U]83gQXoVJ
%)$6_^]27IP<t2kUbN1nJ3VuI<n/Z;QVke/#T@%P7p:6dI,Nqm]e+_MR4NIDm&c,DZ$`"6Fp#&]aVscE,WP`+rp@A+tEleX3iIo3F
%h6B4FhXA"J;al[q-acU=5!KDN#P=#&^0ols3em'u3@(6?X'@TB]mJ9;k&lhc%OQ\JJ'QAard?Zdk8]e:Ojd+ten`<V.OrLaaN,Sg
%/e[Y,nRCT'PjZ=ON4*]+$FWb'>@oi@im^cmUN?cKj):9TE[BZG,`pW''?f4.l%l"1\[d`J9GpSkA""I(YOmY!QX\O8Ma"o"el_T'
%qq9XclZF-WX=!Dm\uO9-/#=S2W@2HJUjDQ2P[4I=[-i0`>miq@:F>[s5+WdRpJR.JDC;K=ViSLjAj#dc&*#;tn2KheZm#DYr\U27
%LHk2sT]gX&\W%c83pf('C4N(ck>:Kl"Fht0?^80SqsW)lg"fY<^-Sf@h[cWkp[iG?juH_?OiWNQ^WHL-+IXS&[_F7)4ds:VD@Y/`
%q<U7'W-*fl>;E+iiFk0]$O4<]i"D?MB/;li>OYk'om$(T/c*_j\5:iHmIn::(7b0gJTn\%p_Sn!N;Y?I,CH$HnCa1]h9^dZcEaqP
%r9(P>diV`#HV,7rlS)OL4Sct2h@2@"oFL.;oq,(KcgN&B!Q*&B!MP](^>nd'1e+WG^B*d0NU!BQZruGr!FS0Fbo2!6lu;ECDn31r
%%+b:t>Rud:_!agmd@8(")#E3E[QBP!+qM?o^^SaPXe$[>.!;[gH*TXc26cIkp]N$tdu[l97@*L8hp17s`umfr7.E@[)+oU3Z>5QD
%Aii+=Tb0k53I@XIG`9K$pP-;l:3iH[MAl6hUsqK^]?X2(pf7uCb%M2U5()VprM&Xd&^rsZ^ZDB]_4F1j:IM!6rb>Tu7)m-McTD'g
%r<@K:Iko1V5.'RO5]Xd'rGd`s^;&%<fULW.:,fs:Dsc?aWV<j5gms/.h;"8EDF%?niVg)a4V2j&4m0R!k8Z>PnZ1To4n!!<;N43.
%+?ee1I/jcMT/n+u1L+gAj3<#"m2f"]fcTd@)rCV1!,R2_kBo\##(-h'@At46$1JGnI+#i;IA5^u2i%u@ncj0G:'I6c4Vc3tM'7;e
%cX3:E+Z1-tp9Vm;-a=jdN-(h!Xr\J%)6g=348m8VY>"@5lJB6>403b_Vdj1)SbsZY!MGWDj1`ocS3oI&$Nbr,Is_04-VBuF7>ej6
%IRa2Zi+WhR%o2_A8>hKW?(O!S$'43Vf$M0+"7Lb`_]8mEa0k?Gh*#$nOI##m9d9imc`%tl3D;rT-b?J`*N]6H4Ae@o7(+g3f:6Y7
%:W;qR)"f'Jm%G=hdoL/sMG<ukJ*?>p&n07)qI;3Ae)#D!h>m*Llmi#1Aqi?BpFZ..@e5BKU)M0[hW6t?9gFC6q:+Rr=0VV-2"]9a
%Qd8Q+"L_[][]nDcT.Mr,D?oFmDEh$[(4QgOr9'mhrV_%uZ9.Q2PFj!oHT^rI5L')TBE+(DR8#mTM5?fkqO3@H+F."P\M'kNW\Tj?
%,4AeDkqO%Nm;VnGO++-c3nh.&fNSlo]d$<sMl=>9kmqGH"n%(g_qapr`hejjYE>g?IY7#ErQ;^\D+qdko,I>KY-[L9VK],S"ko"a
%qI`A.`Tdf)oVE[6<*b=Z6*$!VhFu>ocNo#iD7^dYb,kL2[FX0\T^6uS?'R0`mT3fXG]mG`"/ltd=nj\3h\P0i<Kt$6IX1c^h!(mT
%ol/+kh']Y=6V>aM47IIU3:4'goK>3@d#ERaRIL7>p]tS!;+YlZmnM#tqNqq4&Y)2a;Y#+C+CC+4ICe"$5SIPJYi=/FUVD6Y>ol`0
%LH9lY6s5l$SA*`J@d\#eQD,UD\Ei0qoX/BJ:chL>UU3r\DIpl:0D?j\S5.RB\JOpclUAFS'..$<B;9La7F:68>#tO)TYP=jcVgYC
%<B4na9rCa!pMeVbTDk)cGlW`uJHubGZ>7+jp&*:l\p&por(NaK7<_c`UJZuo6""fO5T?e"B+qQ(K*%XYoY_"2@UP`mZ/k.H"*YsZ
%6o@/>Kl0(7n*!N^-\6;iY)D`;>Kg1bcWd:OXr%$a+CA*UiclJe4:@FW(ELuXbN/;gQnUWE7MG'jLuK;3X/r2KkAeg73/Qo_'WIi/
%+OY%:UlpT**@s*2T9CXt:Ku]t#un*(HC84c5LXql?nOP+*a>8^:="O3-U6'=]Yk`Rq8Q^72$c2LA[EQGB%uWOmopSBe&qLWmV.sI
%apUXfHA$;_jn'SA?b!u<DRTCc^+aa[eU9deMqOde]m3?TXjYSNDF`#&O=(+T:VD331V@EGH?fqgN+(ue>I<!iIF+\o[.Y4nbLuJ<
%mU&)gIf-_<6u@CZh@-9nc"a%jr`Z@pV0E\DID<"$fp^3QTInN`Y-Hhah$?._koP[Dk?I<DZme(@rudb;5)FdebjC'!K$V3(9`<pk
%RBp)TP"#M_#Nk[1B"--jf?^R"Prq*`!*:5iBS4*:4F<M9J&a!TB?)H&VS>uq#LgZhJ`Qgmr-X\+Q)aa4.:$YE4P4NI]gJsspZQE9
%cO%UTI7!J='@8&*qi,jg%.b8^X[2"7BS\rU[epM%I_;TA)/kX.^X\'ga.@'Bs3tq(jROj3]C,[Q(CQg/q2s:WBd1h#k\-tEKt?>\
%NukaqQd"34jkk%>LRhd:8b(bMW2KX,EpP"_DUsRuio>+NSo1o^c87rG%F:X.74OnorL:.>(?F1uKZbdog%'S.cU4_Z<-j<3hGUcL
%c'H+IBQ!0*a6N+7q;)<-NR@dWci:!U7!+3P>,hOue'[j`NPBaL^cFA9Vb[,2o%uLVrgf8K3KU"YWuQFdG3;BKVlg>OLF4UldkZLN
%]EJUE[k1]`[(>pA^Nm$Js1pV"-T25(eK&;STD&En[5iC_ZTP/uEk8:%m(]<,EO6[G#.J%0mIj?Ke>a]\S`a:hce\e7_Ze5]1XHD2
%rOk-kL9VHJdE!rNRee@H,*O-S2J[-[oB]2!/J[H.^.9gnb-PM`Y%c-NG/o/qLspXs9X1[],koGdRCGdd`8C8J+p$;=+5(:@0;+0s
%F2[JLPrV5[WDY>]?q6L7//T9R*fkGaLFY<2#D@\l0`-Naj?]3CU?mZi-*K5;]pu5FVTo`k4=1cne;2D:ecqdE`aktHE&<b$NJ2]C
%ZRJscT)/1`ol.WG,d9hH,/1a'j]#LIA&=NONWBUO=/$'472`QQ6?F)J^A5drkohr"5,]ulF=kXcb'c\[@;A4N#`.?Xg6;47Xt3pP
%HC-%RDK0QW.rOBN-/u$c49?F8DK?<I"0Q0m:"bOk+Ia7,1]$D%3[6@p/J/pB*MIf!3;!dd]`l!93TK/T<(0NDhO^s@RC^+84#;gJ
%c!dZ"kA24$>fH8f-=Do01K+M)nG[_a,-M7&Pr"g%H\GHU3"lG`Ia#mF6(2gHI+3G_JWe^<JTHEc,W$?YkaTQ(4V0B6&),gA;046$
%=6^d^F66"E*ABD2/r;r&QWMV-]J1\M2Jm*"55b'#I:EYG)d8_>8g3&"6t5uQ)gl@Z=;?]Gm(?P$d/#Fd;CuBbnb*$M"QB:2+Z)<m
%dDhqh&3rVV%;?UXE/"u+m4-J=B3"_o/)F8rUYk;mB^cu*%#*[n=p[uVg-CX*^d"@([]q[8'Z50L.!Iu[:smLTK[th[aqcNQW"FsB
%3$PAE0Y:b!#daF659IsHAaWO;hX"=Vf<^p?I(r\hUm+VFY"f3,\S)oBjXSLt'p+.#]+I(55N6V;nF=^K<I(dT[Cs)/-8%Hne4F?B
%)eE92%l$^cgn*=>1AVriPgIL%-JkGdodK^'8ORa6BoVfI)dt9DOdk`_OJ'MQN,GHb/N9l2Y%R]i<N:#c:k6RS$m'6lAP#>4%-N7-
%8TL[<']T"ZLqe6iaOSMnGgFVRC$JfMP5a3r5]'N^e%Rq#q3G%$Z8>2+/6dI;b*P?PI59_W7_>4L*#4,*1F4M@cKrUuj\CQI3YuaZ
%'?F^j*[NrcHC>XHV(,N[<`uu[Noh"[ECM4QCk%h.%9N#NP<P'q1:lP!NQ^8X+W('4VQ.Yo%2"*pkSP&!jMF"\>bqF@2@MV>2)b4$
%hSK`[b&p6m%=LfgqUsI>T(pe3br@W9cZne;h4I-iDG6r\oC@riQ5/%ofoD:JmgNel1+0/e-MTD%m[A3WjInA7ad$a5n]l`pKK?p$
%Rc<FDIPZi1X6Eq7/sb>o9u'??RqIatj0$WWSZqUt2\>maM@UJ0Q4jI=.*J4IVCeFk*;I[L<A#r6%R"'rQNC6%AVK^)iK:J8Pr55^
%H1Vb8-W1[1KimcD8mO"l%Z<*.XmN/8Tq"Yqm)0T6"<?oW"K'02Dsj2r!!+XR<ZYOj#:upt:#&_h"G)]3#+51TaVbHV%)+5n>`j/F
%2Cf6aj9Pld<O1;B3t[9iG'k+PB&ZpnnC;p)**m/I&!GQ-`DHL^r[koTl@#j49YHe?T,MBc0Z7'!PDlZ]jJ\[V$_4h2V<t.jK(%\`
%r-FMW!ufZ/_`se-RCMmbqX%Tg*DI#78RA4&,5e5^oZ%JZmK?n=d7*]*"J`>6<&0c'Tj'*@\SO!Rg4r.saaaS/b?%QI*tNY2;UJkR
%<O+*b:eA@ZZ5"Qsg^lr27LS2&cqkI9iI!S75l_>q#U?f"54O1tW[n/,<"YQ+RRda[Tf\mt";(O/i^X8c"<9]E$5Y=c)&^75BY?_+
%n7\1Jgt0DC3--]o_POoF'*/Ls7X2b1:rb4+0V,_o\Q]'\r/uFF&WsZa="n_.+3&j1i4d/<a@QPQk4e9(OJHsmY,Y-N0KeE-l?E;B
%ZY-JLYCb,o+6o*9>)3DE;6CBH3MTH`@k.!c)"+2O_=2`#fO_49#16YnTGLj7h,W":biXJRX5mWp1itISdql8Fkfa?)6l!mJAcJ$T
%>R]]1Ak`FAf1Z\73M0;PS`Yq(Fo%i[UbVVTLWY:66E@ae$?aq:mof/9r6&c.J[7+"c>G#2WIRUX,HX!G>Nj4nOU9h`ORu=31_(a>
%MjT9e_l@Nn&U]Z-Hu'N[RgY[C#+[UqKZO80Yu`P?r(b@6[8_RCb2hhVcW(E<CXCBC*5C'!]g"bgk[^:2]Wp#oM\#CeO*bA:pufa,
%fWtWdpP`O'R59(n7VF2A?H_I#,RYc7G'gQiajk]\%AQaXUcI*/NSnWc/q?!Kb^i1F:j#2(A<FC_9t7A^ESJAjcpPWd+VlZ$A"qM_
%TQ4HraF06P\kSM>8m3Q"@l5$S@)@bA:-bRC)pur%0!FZIfPhXA69%k<?(qV@s,fsq/>2OH;T]C`o6Tm5%;CdNXgVcqq77H@N9-`4
%jt;P?QHPlD2a1G0,nhOt2i4Bh+4SH=cuc+u$T+B8+B?0ok,QTNWG$d5ij=gm?N)S2J2%8i@[(`VIPhrQnN8)V$]icopJ$A!7eg_/
%o=kV&s&F`5dJU2"r)890-`:S?I[#mH+<4Ziia'B46lG_;Kt]+2nI8QXXss[@&o7#`cB1gLp0mZP<U+8tJ*!9i9DJA<4:(a>4f_IT
%,obsJTIBLt2?8mM-`n`ucuC\4MVquX[)XNBpg-ns&%ZaJM6n+23Mb"_44b`Ue24mO?>-5*/-XJL=\LF5Q`LBhm-#uYrJ[ETq`n`,
%HU$qlR'7Bnre/.:d*>Inlde<JL+1!.<P37L0\e(e(s?]iUtsj/87?N)(Wfpj^BG/1VQkQKg"N!Rq8okU)dTW40lehad[CL_hn]>2
%UT#YgN09F+[O4h)ddhp)523C(caqF]c8m'4,2!6%`sAKK@!*pd@I#@&_tDm<BqVI;nRp_g?j0KgXC".j$S'5_jDYT#7W:jfC4q9)
%%(K!fc_0YrO!#1FTL+uk00J&40rK"o!JLOejuKnXQ_XRne=%2``VpkM8M0`d%+Op>0aNVZb@+)_f>fL\Q<*!VA3_@HK3Ta,.RkQM
%I:'\QjA:0)e?S"I2bn-38\9D?=B\kAJV'-AE]$mu`I$Etdi#@(V,#a:4=!/(h+bbP:!QS;';V6D1\%XcU56ATC+[W4/6gid]PLIA
%m&9JGBd@n9*4[t;h,(%dpIe01Xbo?2EQYCoG)l"0N)=<Q)b\`oK.K"eRH`')4uaf:d9L<V-CPP/H:V$-R!`5am8?#6*]c[#D)^M?
%/_U)*5-pIWDc,B3SIU*r=gQo<::)%@Rgo['"bYLnQf:^n?4W=$]+@LJ8"E)0pT!MG+%_*[f77##0)f0E^jR5]E!aM&4+3>a]I*"6
%`IklqeJNUTM^,c^7s/LU>]hsb?%.,c=*+=RlB7f@%4:8l7N/XK][JOr)OrTI.+s6O)Ua6ZI36T+.i'F>n\-1^5g@]QBXR.s^r<4]
%@V7r1,?6%r0=h#@<.>PN27sC/W_=D<d$iU0!K=V*n,Za(kE_,>JLh`GU_rQ%XU\m5pZ-ZE=NSBEc$VrO^.6182I!#PDVo,S%XE:V
%]Vo"-PsiZ+BnIO>mN-0+W-HHPFQclGpGu[FQ96EJhN1j[[$%iJQ?.OUr,GQBdSYjtN+Un*9qY%J`LXWB[oeA-=$*^WfOQc%bKPMc
%!r$3u#Er(P<B+"dXFC6/OrrL!&?Rc77@.>tM^"9EKZYkV(`MRFc>c49ja:D_%FUck]e6#!.F,a<),DC3pO6ps']em7+uTQ%%6-qn
%-l7:$Cr@FOl68_m/Rg>Z`8@R0@5R`@?4N,-<2J6-"F/qTT+m^Y3BH:u*a07jd9aKOSh+<taa2t6?DWI`KL`UA@)WeiS\K`J,k5=B
%/K&npeZ$`e+H<U^GlbK"),q-kL-]a3G@`lhd17)?6iq78X!A(23qBc.[D"INirX0ZIFc<+<cU8V%3`_4l<[?'DX-1L,S8ZZi6M70
%r+"+Rd*YMX7K_;kBG(meA0WO!o5'#_00X;>YX0Q_EU&%3;74sR>Wu.>6X%S$W[p^&$*6qE\X[M)o/C00^)0(so^gI1+iHO4lY/7Y
%e(*Hml[cX/QI$htE<0/uMn[/%<%3$eSpd)69"O4p;bTD@k\Gm"=uAMf,%'<Fk_dL5UFc,J':mf"]+oL7S!Z%Q*K3_r_(9(Ce"=a4
%#,ao].Q`$]jK\l$6C(5_(N@%m>gfunga_"$G``=k=o)0;k`Td0(I(8]\T4bm[/c4?On$716q*I6iSlCd;6DGp?F_1Q`)tmfkl`i"
%Y-#Uulg9H8fr,9?<\uFUCI[sd@#Y;@M^k)Z=QE0\Y?@<Dou^s5m@qO3KIc6UK-a6^AJ=(8P$Fr^&or72m)WGchoF'tY&d!r&*Q;U
%1gi81#7rVQ(>`Tg1PqLSRNu?N5CYE1F(g6Oo+HAC0u.Se5`>b)OleakgESN\A?A%Y$7=?3#F8teKo.1ON9*%;(?8.$WPBOhN!l%H
%[F49,<C(?DhMq8Cni"%qG*7*/qZ/5Q\RLM/jE8W8PmAW>kXj_XX1^CD-cUV3r=o),Km^)"4t%:*_K?sVc(U<#P&43eKPaY^o^J$V
%!aTQ.Zk,ZQG$9N[UNCU]?.F!sLPt\L[usM$R8Bcc<(;GI5qY11F`<4:,79qh<.nL<]uk'#qJla"<[)?(1't\NV#'iOG\ml!')$)q
%U[^qb^4DlX5o`hTE_8N,PXRNB]aCEgZTXi1)\pWGW&VI7XG5i*lTk[5#!9%,cm"49'I#%&6p+fmT1&qWUb\Cfa)>L;%Q(odkRgV8
%Ugsg]klRchRhI46<sq9].>VP&5!tm"V$r7/Y7&]qLDhWECjftTpX;P=N5tA04./mT=uDZ=[a-P>@^#=M,eD&Y9dk.nU(Unmb1Tf8
%5[r7nMlq.1Xg^r.)A5Y;P&].@g:iE]>8'EJ#uTQI]:aTif00U\20W4miT95\BhhX:(sQe??9@>TG<0J_+0Q&@M_catXKB"%(H1PG
%-b2q;0Q4,$.9!j]Ei>Qg<@u6tQ2Lqk/u&?H8SKX\foh0p;DHL_Rr0-QV2gGk!8Ru;/]YP@\h(A$nQ%9G]p<&)nSRcEJRRf9A8J[W
%IQ"?fLoh&qjPafCKXl0dS0,f_5Z%,[.gW1VYPR"f=odY[f[@IBaPQo.JG"Y2JP1l<p%EVcR8_O1r",m!iu5J2TJGJN6bmh\cM5bQ
%,C]/n=t0q%ln7?Y\Lu-BmBq<Q8L,Js'YIuY-u$YRb&A"bDG>!BV3L0_%L5uUh/a:*9/fjF^4!YrQ#Z&=Nde^,1f^L+G5mrn[Z4:n
%cbt\K-^k]a"F:!)Ot%%'<"b#nH0Bh&n//s)\Jr>aRS@,_-^U2h2]c9gg[ls-)i#<emZOVjlZ.\p++]49B"dN[mQqsm'<2lB8W1YP
%#;]^E0"Rb-C_3MV%1!l]jBa/8Y?TgX,0[6h2,I9m1[b<QL27Ql*?*?]P;gsBj'kL8Yo^*?l7/Q_,B8N@UBA&g:%_=VoT#;)?fh/2
%6[\AeqXjM#S%7O<cbV&&Rp!E&2Q]9Vj*lIXN+t7uXme>P(PulZdJS1WK`0id`!<rGMrsF#RBTGQSG._Ueam"]p.ZWKpU`DAZ44g%
%XTGi;CB@g`n)1sd]eC;Sa9J\2J@\3p*lnrcpO.tXofB5p7Aqk\?s9TG>1UD*K.4d:OWgf`b?.-gaCC]G?>!>9r,?Lpo=mo+GuVni
%`Wo!9.]-JAVTWS)/(>Xb3:,-+B/QSRmY;KjPaiN%6g;0ImXmWKmZQ11U'M?=MaeK07*[,HN(G%@g>JO15@$gFH/Di.&$m-"?q"pT
%P@dWNc#7.6$&YJDJ>)5Yc'A#@>;mM%:tb9qhC;:c39,:^":"(3JLsmrnNdGtEdu?>2+b4NVb*=6G!K/e7EIf!h'N7MSj(u5^&skX
%oNqt#h4sq,K$U+"INA*)V]^:Kok*Y?g-p;YD8C(-IK%7>00U7t\!qHI?7bgUcX:fV^Thj+9fp$p]V(J8YMRWlaNO>RD^M<1VcVWl
%8RL>='A1dWO\,\'M/&gZ])C]`I_a!r3dg^Sn8J"u&d/e29#XK26bJ1@N8cFb,_)P$h?B[>ESd'mr3$7.CQO4'9pHs;6KB96')CZS
%@c@8F9j!Rhm6CXK6P/so64aX-JKSbHS.p%TGi>:<5Nnm@FC\HF;fH1!Tq>=aY?A_r)1Buk+=<kG;,X731.;1JUu(sR_hHWuJ&]*Q
%F9_^9j.B1<oS:i?JdPc])#2J(!WFO+-"[N$2?Oj[+H+57(p<3koFk6TP#dY1JfbI*ALJqNMcU+c>d()Q**c7hMgf,53J]KIHan#n
%phh96g3oZe2>#?<mtSPmkb8<3,VD\bq`F^SSW*cZn_EC2!/jZ(p^d._m1d29Bh/AkU[2E0@0[J=\r1V%CCfhnlC`^c[T:%ZnpR7'
%8J(eAR5X1GjsO?,YlpT&OdOQhR`X$9l-i.GlW!\PJ%uP<&Y@=m-@FABr^"R2GLS][Kq'NYr/&]upu`_MBI(IK=;.B<nY%l+m[A=7
%*MWZ*g=a<%W&qA!@&hdo)ql+7'<o<.6r`BcSTOZoY^huc>,m6EVYTMNP0s#bZ<SH*/^_)[aC:$CT#k'gWIu&'\7P9o84K(r2nJJf
%?'2>b=]D:rQXS9/Aj!AZ"[gJm$^RC+&Yb_9J`j?A[!t!q".tjR:dI?c`cHu<ShV5js8:n_A?pkEFP$m<Qe!%H[``A?4K(Ftj?Yt.
%Ljt?s;-`ecB#dYI=FF)T)g_)5E)QpB&R7gdqeh'Dk\KUF\u!h&%VTh.$Sb@tadN$-IT:,+6SXf\J315kAHr'^1es*q1:;1urNgo(
%gi57j-(</8D?[(4oH10Yrt%X]jX,rC_29oT;fdOe)D!<*/k-E2bQ1RUcids)Zj_Wdp$IZVp`p)FG#c*RU04\%Kmpj1ZOt</pkF.[
%K[=caU0DN2jFi4@=B[o*kYb^n'CG-"Ac]!c&hK^=AVAT(-4*n$q+96!Pmu]nqE9M<A;Cl$HFlq^S+/Mtl+?>9G;9O=jJl<+W4al/
%(urG#0ZP)-E0`bDi*F>\+]+^M+\)#2+XPbTiN!Q-_ESk[0bH]E6Qe^h9S!K$K+fH7VOU'(k>6ueVm/V-/NXW.."4U@P^2"/dY4fc
%aL[EfOmADs`,"">-]/['9pGMFN2WQD/5[YLC./"[2\9mH=.K'L"AstF1uE$Ld4R#?7(:=LLjm$B+bjo>'/:P_El<(K3tMc-R3G\G
%pFfuSn0Ejo60!k_\EK0.8GA,Kj#3>b=JA9C_)?_B)4[rcBlNOc>m$M`q$dat7u<ggS,4rFn^VA1pjMJ*+_9sD%$BBCQQ\7=<t'q/
%Q8PIBl(oQT9GIti@@f?@Q_h*`G,SIOmj&=kPa;SAVa>J6r=ttHqpVo\<eCER`bio'/W+7qk$Y=]7RLa"10W?"e7bKI)b,#Tno9P4
%(QFr=E>lerDf6&+rF9?54T@)bYF0ts<?W$q>[n@&(7QW$=If]Q`cn36C2?IGK<\MEm>Rf3AS%C\ff>][Y04)LEGpse-:8q=2Ha=j
%M94FG:/.NK4#_.ID.Sm!SM*ni]-U-*KsI.FN9iIaSINkDG3spZL<#W;28/[WWTQ'dR`>0ie?nH-O/D:fkqNkN,:`GT/T7gQZh%ja
%R/+/!2Y.1,Bb%dZ6KT[VY*bF%daXf5#4:Q1i/*iY%>Ql`Hb&o,&*Gb*4R[BMk3[DtZ4OnpCU#LH-&]gLg0.Upi4&K=e>FTJPAPii
%P2tq39E&e5[T"i]T]Mr5ls"esN3u>Kgo"_c1d%)!gYT/<p'nL]4n?<ifm:hOop>SNCG`Y3Xm&/`BIu,0@;B@bgKN@PC#1o5hjr=u
%0&<cR((moBa(>ba$(GQY^"IXepXt.>nR.8&r,9#+pdeV3?*W%Nc/-5ob\V@/AX)7mLM57H[miji]/Sk'g4`caA.Q0D*ddHNg"VFV
%`R+UMM)FYU%?\'E6</]Yrj<G#LKXFn3]r^(UP!J6?]K)_>rY`@C#g[F2Ef+XB%2"rQbB8+0l)1u%\(bAdr(ebS@#fj3;k9nhl1_>
%Z1HMB/=hdugWnJ.m*3h$?M$2fL,+)#m':bm;&>co^UaL7c)3)+:X!'t_;=C%D\X/ATZb8ZX8\jP2QWqe\(_mC\cn^1G0@'r%<ltU
%hRMlVPAtf9NP'Vs.8Sd?aTq'M7R-JchU5bW,1A,1j2okh%8T,'0R7ut>0=1qXI!6\L'`+\j`96/%ZQu;G"f<W"lFn$Iag,i0;p,Y
%r@58QUs14k8>^!(OC>;Y+>\eIOfV?C#2(Xqe@#S"PW<SJ8N9/Pg:^Ii@\6Q_*grp@)hFm_<,=q(;oLe7rgLqc*OIllcmHK&Za'3c
%rT<Spl/AQbcsJf.Q*Dj)28cco/ed9V25Vci+chs`&S@W(Lod:I+OS2Jmigh(K)rg*B&9'_p<@#,YbQEkT%u3"4m-'0%\2#[$sYP'
%1NPEl_9NMF'9`653<\3tLn`^4h%ZK;H7OBN>j:bVV/=>(8adG2d:@MgZ4@U^KhJk\DaHTT08/;%it1oO[+OFI%5Er:D8g<Bb).8a
%s/S-&7col=&>:CrPV#L)978=UimWG-*LX_6`IQ.pR(\i6^4_%;pD$I"hFH%sDcR;sW8_I4/iS5;Q;%&g`ObeFPuc0pCh0XH6]Z$d
%8=P_L6t5ajTK!sf"$No'%t'@$\j:Q84VeGdLA'P@D`S/IlDAB:S)K`r/",4IJkEcmHHeM!U!?.LWBb,YgKMI3GAIj$^$](__DYI(
%pC]))]\E.ULprK<7V%(T7Og;P&K._e>FB06(,FA9SB:<[qa><q)In&1m7ea*?d,9Cl*uU_\)]0qN[Bb5m;@F]nhJIkK8I]l/ek?+
%%[ktZT<'Q?\)U9s$Ene&;t,$r<b!U0gMX_<pKBniGZ8e;j0^3Wa(LF_Hc"L(mh:0=<(af`(:?QFE7cNET08S&ggW@5[_F"Cad.46
%:OY=k]+c]*@k"7a>GU,QnPEu^=AI;IC,V<BmIkh:p,Rr*k>H>eYoi5^9P@sYAh)>F.l4;g6<ROOF.Wnj[-QE9W$d/fS/lW.EVH%4
%G%aj1a]5FJ,<)`ScLZ4:W96OSBImMmc05'fl6fo/_IWKmoC:/Y#%r=3>m6Zh_37[i$VHna$_N0KX7-j2=GugYB.Tptc""#"BHKi7
%CO@[iM2e3.ODJMo;]7`CHeIO5P3"AR(WpmJ)_S=#CRL'(A>rRlhK$BZ0%dX5_U*C*`G.A//c':;lJsYef,TFSYnm;^qJ%+;8?'%u
%qA4:W*MhurBdd=1>N57/4^(/+V],,:>\e_CAj^&e%`R?#<,;J6E8(5.P=9rimd-BqGGS=*^UHC@nUp&M`ZMp/0)SRhq)0=q\39RX
%NVAIZ/2M!i%bpqYdiKJ?c'WeMd;8NcTbc&HTWJDMs/W?\Wg,'Eeu$"mg2)g/[L:$o\31KF[>q.cMg*T-9'?]?VTu?89cq!P27;YV
%57`C<.G\q9_NB/>pCSiTHH1-4eG#;f`I<5;0D9UMIX*'M*mb90K?Be^*,MqFhN2qUk/5Vtg^R?X:]#smOPHcOr.pB!0n4JmUqR&n
%6K@Gm?Ap,QKV=h+Uen=<-B%hWQnkMGIp2KIlI4j(\!sWs]73k]q*&HWf,P,kkBq92Sl_B+eU0GM#8`P73F(&K=]k%RgYO[s08[mH
%,;iXBhH$=M,Z%XGg2nOF[b<"m3c:d_l[bB2DHqeVYfW].`b*p8"h$6j0Q)f6:3X<i&fEt+-$DZ2=!QNWlj1Teg-n-+qQAiiP'afd
%/6u<_m_4kWW(\>3C%CMXLXZj6ZFjG6N^gPGYo*FF;9pj7TgLa!m<7"64o##MS:8S@G*C>CV9kjP82#7Q@p^6IGClSlocs4p.qFF[
%q5D)MWStQ`JR/hJdo%R-*hEnN!'['+#F]V^"%.$uk\)_Uj6n5jq$6Ser_(,CkcZ,B:pg!aEub,9:i!66k9%.PXuAF946Xnbg_s6&
%J1lsYfg[@Uq-Hhcs5am'O")Ta".a1nZQC6[IY[06WR(;%8>>FVn=Fd4!^s-Rr:pTn,,7A@J,M?F*`!XO#P6Qn)h2_b-.P@90)o\G
%\3idRn8QtQZlRNtA>l;ZB.9-U?:YSsMR8$h=A#AK&M=ohAp>5"YS8DQk^mFN&m__l2(/ebaWP\^"t=Z.'q3Y,!Y)5+&MX0CIV%GP
%%W-^/:uk:Kjg.Ci%0)!MA`*j7H[&D`?JLK)HUs=Z?<4QN2/k2_a.`qR/hi+N&qdeIDPTR(Bt3(=\6qF^.!"g8@m-@#$SMSjkFjjA
%[k]!:'t$jO_i%\3ba64A4=N%aM2$jZ>$po%EPMg],`G&5CcjO/Lb`"!9rP8R@74-a9tX!W5a8d^"M@-u_9fQ>Du_X"N<)b<LDNM.
%G>@^*1o"hP4aH[s^8,\DGfmo0P,(!m,V;7pm9r!Z0n]_%n[+1=(Hd:?@iUF4p]+t2+J#5DPrsreT`p24<4&A)#%K>S&aNRlD.FrI
%I`\RKSCT1KgQ]LHs0HHuJ.r&BEIt!kM%s0Y'S5uakgNGVb'>B9aUah27RY_`":rP1C0ubtd;XmXTS.&eg*930Vmc0c#88]:*#]=^
%6h2F\B-qXUV)CWHaJ^XTh*>D"\`a)Lr_C.9&![VO4T=p_#I4\t^^3,:2HY\V;%,2cSd(`O=q?*3*a&[r0refHU;14'+[l`+n7%:Z
%"mRBUKZeT3-Q<cAMe,_X@\>Q.=G.'=@9%0B4>\;I>:C%"+-rFQAA\B`JEL=-BOW1I&Qk,-ehti"Z(.c$E["s/(*Ze+BOWr``D&V&
%"pGuWEF23+83f!i2%NEf+;9M(YQ>S$%q`XKW!On&c)W2/K&W?t19,K6PW4L40d+MHcGP<[O\u"5fTor0K#FW\cGI!1?ptem&l0a8
%C"4&oM=[s1aC1:p5l"E&#'jmZ"aV_]=9V"YaN>(g7&3UrON=P@SQ[l)i]1hUB/hRj$=2R:O[ZV<!?$m-bQo8YA2L+cmjR[]A4H3I
%,01L5Y99jR7>&a=8B3`p(e.C4?UWV.NJmCaRK3MW-.TGKA6Y43Qm<-t#HS&EJC/15Uj*Y6n\!RS5X%f*)JYO:(;LZ;B`Z\\Zj*?!
%kVnL7*,7bHKfY+L%4'IR5qdJu"b%MSicu]^$.eg%TK!.:#):8-i&%gT1kh>&]Hnk?^647`Yo(;GYr!(<9T*I:#%/Z-8?/Zi"(:qt
%e5hFh&Z<E#MlO0/]Z<McG9bKZ[Ti7(/9C@d/6W<u.nrkR#/im09fs`[-Aj-![>?%EeV3Vm)$t?/aSgb,,nfg0#Q7*j)YmH86>/ZX
%TQo`/K62g5n1qmK&'uV)&:p%t8-S)Pcn%/PN'*k='a;krUBOCb1oo:JX?$u;s.FYsQ%@gRJf+2He;Jp533Nd[VID1Z;ZkpZCINk0
%*&":ORhmbQi(Ga8=<'nRWoT<8SJjf8!d5oRKGPot+:2_\KdLc9XD6pk@*E=:"1P/0XK7)@!ONhSAckK%.KFWh,n6;f8^puYdRo6o
%I5K!:r"*$jYXem<XWW.$ZQjG\,Y`"p)HsD)_=Ak&;rL+i=ga(j8`1ptW7<4lUc&-MW[Zt/JY?0\:r4-,$lL$BDN\*a>A+'qk"i8b
%"_Uh8V+@[CT_Tj6I(\[<(17i)RB8lX.O1MJK2rIN0Z\%V,cLf"kSS4(875\>AJ>/^9Q`#`2J+^N`).1-+%C)K"d^oL<cc.e=HKQC
%&mff^2iRP<RL)`>eXC.e8IV&$RM&S6$fD.eM?RB0_l9ap6fM)W=UhImg*2S0'20)eWVL=l#*VPH6OVQDK.]bQFnW$Z\2q#ti&K7#
%M`db9ja6)i2;_:.o8afI(*P@OrqQg!@^Eh^2?*Lu(Y&S$a<tpDm<4!.BJuWHiDq*.=JB2MZA4+u"b5>J;C(o)CijFirik(8?M;jm
%?`4-N3d6+=V3(KDO8f7/R>q1~>
%AI9_PrivateDataEnd
