%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Right Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 8 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
28.7778 48.002 mo
28.0898 51 26.0898 54 23.0898 53 cv
16.0898 48 14.0898 39 16.0781 30.5967 cv
16.249 29.8369 16.4585 29.082 16.6982 28.3496 cv
17.4175 26.1514 18.4136 24.1514 19.4746 22.8555 cv
21.5918 20.2725 26.041 17.6709 31.2129 17.6709 cv
32.3491 17.6709 33.4697 17.8027 34.543 18.0625 cv
39.8721 19.3545 42.2012 22.166 43.7422 24.0273 cv
44.1309 24.4932 li
44.5938 25.041 45.1309 25.7178 45.6699 26.4844 cv
48.8271 31.1777 48.1289 33.5488 45.7715 35.1143 cv
44.9854 35.6357 44.0146 36.0684 42.9424 36.4678 cv
37.0898 38 31.0898 41 28.7778 48.002 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.525017 .718013 0 0 cmyk
f
31.2129 22.6709 mo
27.4551 22.6709 24.4468 24.6768 23.3418 26.0254 cv
22.8418 26.6357 22.0811 27.9775 21.4502 29.9043 cv
21.2563 30.498 21.0898 31.1016 20.9561 31.6953 cv
19.8032 36.5674 20.1074 42.7676 23.8711 47.0205 cv
23.9043 46.8828 li
23.9395 46.7314 23.981 46.5811 24.0303 46.4326 cv
26.4492 39.1074 32.2979 34.1514 41.4141 31.6992 cv
41.8936 31.5156 42.29 31.3428 42.6006 31.1826 cv
42.4326 30.7822 42.1211 30.1719 41.5498 29.3174 cv
41.1709 28.7813 40.7539 28.2441 40.3115 27.7197 cv
39.9033 27.2305 li
38.5742 25.625 37.082 23.8232 33.3652 22.9219 cv
32.6777 22.7559 31.9531 22.6709 31.2129 22.6709 cv
cp
24.2368 58.1934 mo
23.3296 58.1934 22.4111 58.0439 21.5088 57.7432 cv
21.0356 57.5859 20.5894 57.3584 20.1836 57.0684 cv
12.0015 51.2246 8.56396 40.6396 11.2124 29.4453 cv
11.4028 28.5986 11.6538 27.6895 11.9458 26.7959 cv
12.873 23.9629 14.1724 21.4385 15.606 19.6875 cv
18.2603 16.4502 23.9385 12.6709 31.2129 12.6709 cv
32.7446 12.6709 34.2607 12.8496 35.7188 13.2031 cv
42.666 14.8867 45.8711 18.7588 47.5938 20.8389 cv
47.9697 21.29 li
48.5947 22.0283 49.2031 22.8164 49.7598 23.6084 cv
49.8184 23.6934 li
52.2871 27.3633 53.2061 30.5635 52.627 33.4766 cv
52.3047 35.0986 51.333 37.4229 48.5371 39.2793 cv
47.502 39.9668 46.2773 40.5615 44.6816 41.1543 cv
44.5264 41.2119 44.3691 41.2617 44.209 41.3037 cv
38.2764 42.8574 34.9902 45.3555 33.5879 49.3857 cv
32.7192 52.9023 30.7896 55.7168 28.2661 57.1348 cv
27.0146 57.8379 25.6387 58.1934 24.2368 58.1934 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Right Hand.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-!`-H4jGoniac!+[(;HK-1$;(Vb:qmJ@te^NoP/X66\DrUT?FB@"?kp@RM/S%%;Sq!.MMe(7YcHhp8-c-r`N
%5C`M"c!RW60:tH*2u(\WVtmCJ(GDE<^3TV@l+=!:m>)UoU+cQ/>PJt5J3O^\IeD/k5l10FGOM1P:cCT;K@A.qs1d_TJUHiOp:fGq
%g%CNe#Oc]7rQ8FEnFRBDos::/X5ErQ?N'>b\0#-"rtddpF"ON2&#FFk,Q86Hb?,/Mhj9()Xn^^MiQDD>h8\&NN59_>VpuEQ8Sm]o
%5Okm_rV]OIeaY"1kL!=fWFO196s#O7ln^aD!^,k.\#4;gc=>2#DpCk"3]hStH[NDJHb-^$A&9<snSUHp,!>6uTWTPegrJ9V]@S47
%dXjY(U;sFH[-B[DgB[(jajic5qKTm/K]-.KB5s=hLQSFLaXPb8A)!ZRMI0'Y^6rqn;Ah-MI<;(C+CcqRZ)&/>h9^QB!Pd:WMi@qa
%"oDp+n9[t&:g+0-L$q%]&&"hC?2WYGrp';$?[Vb=mF-84hRri#qW?b7KbrRKT7?HsIf[_$<]ZHe_Cr+<ci<M,]9hu22-u9?i5^%+
%]&L_ZnI8C1V/JY$Y6'..8b$&S&[X[jptp=4q\/rKY2WI@G(0811U[njJ`<h1L0k2]K&8D8PTNdNBlC\5o?F>6Du]4ZrI'Wcr58,-
%hn4@5mQ#4Z!n.@gbm"*8s.Kt@#,VWtplJq6YFa7,Y$lIN7/tI3rZTSt*qB0S0:r4*a&i<4q9uO>^D0dgGl-p\0E+2gnus?2'&p+/
%HhmU,DdL2'#Q<b!3mZ=%hL,;"-$4'Qq=`oKiX4-gd/W3aIsCh`O3c\a073<]4.N5k?T"arqtYH>m[en-*#Y'/7XjmPo@O$3k-rjb
%hVk#6l?p6^$-*EtDtaR9':XO(oKc]W8XFb.'!+DMY;h0L"=Ei#[kIaUiq8A)pf+DfjBRASrVbIe?P!-:_c>as;;Ki@qU>+uF;J9_
%h7MjR[JrF<Fo'e0.Ga[CIZ84%]`.d"jqZ1X\+n8om"F!+4^Yr!U\o<5K74&*I.rW5$k^5Mq&B+GMq;)$j$u>shmKY:q=6WBjkmh$
%oA)>iIJnp[O!">inF`9#H%'o1iq;mnroFmnp.&1Kr28c75K+W<:G)SLN!fOep]L-kIgXOXr*:gQ!8mV:f?^(LUZ2.7Mf4(:SsFm)
%DpR<(qpQq#buK-jlb@lIm+n/?YaW>@qSg5I/,Y%R%Wa_'s'T&%bKt-YhrfO)^Z&Ur._kK5M[BESJY'9E:q*k,ONt[GH)n7Qhkbhf
%h[fNH0LH\5;8GIc(Uu]^YNeU7ZH!bQne1G9d?.4+>h@6d5<jYnjr8-/X*mMDIeiR!l_e[!8cAGXrs*oh#.MV>8q7Yl1QZ_#\9Wu2
%#IKF1H2d5U&*br)J8\h]?Mo)T+l<+3oW&3Sp%7(45fe9NqWi&aI<9ku0@HfWhcZSl!P!b7ED$i,f]hYDQS8@5p?=McHL^@eq"D=<
%s)doEDr//)%%RgU/EojIBsT,=B_jJjB2PNDs,l,>"-q_\mOr]!Hi"rJoDP_>H1UUG\$$"ZaiaiWDir%70%(\J"oR(Z(&UTri+h6O
%Sk"FIPOTIgC+p=$2=M8=(.i2miC\Fmp>(X>0;&:@qet@TJ(S)FF62fT?bGN$obf,/`51Ah:6$8\ER2S=C!RgrTNV(DoIBR:cb#1t
%W2W&uB&K7YKjS0HnofLP.r5(r?<>(Pn*/_pqgsS5^Q<?bUq9ph=Cd#`><D=I6LE4^J&Q<J2SH]tq?=qs(CnW8TDA.eSq>mDpYoQ:
%F"6-:s5Cq4:dM75rS<BUcWNg1bMc!<lH<!Xa%b#+,6pr&p=c;$?]Q8hY<]gnjl\!e*^>N`a4IYFrV(6&If0>do,g>$.S(m$(2sH.
%;PE=^"(*SV$sfPk7"^o,cje@0iG\Vr%qWdg`.pSap8B'_hYKdX4b!A)d6BgDoRB)SPWP*s5#OC,3CC?.Ea'Lt?UB;u.iCr.PtHh[
%i6Qts&^#sQ\_NF=a,e@pQKk,n7UH7^80ufF[[N)G7\ra<e0C2V4]AnBEn_QKh`rE]^IJT2U6O._PsX$\kKo5c:@6hSg24IhY598e
%JsS&0FLg7Sn&KnBkC1lu&+8?di=P6LJs,*3TMpVhk_I(78EC!9I(22l@Q?ItrYV<H@lf4eMuT\@_uZjXAd+Ws$Li,2E^&@$hct&l
%nGdF3SD;5Y5T.N?o?UA;QH,'c+.>(N=d.\_<^!6B;ZGlC;D4phC*09rd/Saj;L>@U=gW[9:_K$eP7Ee"C/Xom+9ecqrZ=r23CC?.
%EdMt/4Hr)W;8EE;UbjS"9Wpa76XN'McSoK==nmIp[oE!<-oXnYLB;rU"ob+mD;W2k[)OoOIY@_CTfm;&6Ot[FajdqENmLj'IiQ2A
%kn^j`mrj@CXu$Up7Kl9fAducH7,up.KQ4ZBiDsU9IHJk*ar5n*^c4A+^)M:^@E&*(MspWFMs*(o&4-SecuE3CRrB<0aC#<VEK.1g
%@A0:Q9tX%Mq#ocC;b8j_`eA8TK&q$:?o5455(i?d'<icn.a5_bEtQah`3Yh<2<.PHiCa22&cN.5&+Q42O6R2E)!!*5GXubns&QHa
%4rt*](na8Fa^mR?J=SWdeH=%]M.Y=an"\4EiZ$,/&[q?AHj+/l!,Fd8#04960MOO50Ua]%[jf+_(77g`bD[e1$uEPnWH1^R<a`@r
%3)=L2,atD/0Gln]!mSmhfZYpeo%Y1'nH3b">N"<L3&Yfpe)<c/mC'lBkM((tDo8]lFZ6cg2mDC>^t9,UrMICS;rF$9Yh0"t$/u>*
%i<RL/b1Qa0.ra\/^u!,&It55i@.tJis1%q.nnQhSaN75^q[]q2)_A,n*$>:V%D[f@S/aK'@SX^Pi<*>t6a&#12&X[Z4FR.DoijD<
%cL>.1Ge4IK<.tu9_Zko[^mdDFc#/&'Sq)).@$S0Tk4/p.?u3(!n@Yj30OeZ3[j-i<LG?_CJSgS.$k=ts%ed^JN:I:pqES\=Kos]c
%@mFj^H#WQpcMo]E$HsFTX:B;N@Ad\hD]g7@hK.osG2aBOnHf#tVZqLT^g=tjLg70.?+bl>I"p\@(_8>#%9>cG[/_R#XGHp-_hD=I
%s(=;jJ48b)d;Ht4\;X4H'!29c?Ad25IH(BmqtLd&TDH^lA47!HFKj2i1b$sIOTHPR)S%BpM,o3BDoH^IZ/BY4jX^S$%ZCZ6X77V"
%@&6LN5pqO<"_5Yc%#B'/4uC9A`gMaoijceVCUoQOLN5@W3D)<ir8@UKF=AJafj;;(]n+)o"QI;W[Y)*_EI6#T*pg;RhYZr9#CC=E
%H_4a(_-WUShM$CP6+[V-^[8WDgiXG,*397qFn;3"=\Dg\q!.X?P^j!`qqST7A([IocY)H%rX.L!L'DaVm&n!B=<BRR^A>5%ep^&.
%b[bSNpapZ5c^!atEC</?:T*rpOQS4UY=?PRqSfRDFEE-sEBFcN:_ga[^,">*ZSL0_7&.+e#!4EN_a5e7M/%8JjcItZ;\?^!>X9&m
%<>ApCP-W_/"?Y'&^L9Ee6nq^R,Dfrf'L^_36Tl\HCf1[[79"?*#UU2;$5HV@3rM'pqF:l8W.1`GH4h6mnG,c#?i`!V_KJG'&3mrL
%8235hE_,KS6:#,s0pD$09aObkM\IjQ2QDA.K#h[C.pT)dJWdE=kN:3h*#:#q[\BcCX*Ze&P+oeu"Vtbgb3;OgRoR4@C>Z+aRed9S
%-S(i4>+#.F'Yl7,TIPLu<5@OiqMFd@7?g%c^uW0D4F-EY</!#%C*?BeWeNl$AB"%#<5Q\:6j_of/^h`O)?CPZ.DS9#_ZtsT7=.Q)
%Z)CdhaPrA^YRD-.#Rba5_*LO\)'3BKUkt],839_?>n/bPg7TOA2G'XL4q+hu<6paJFY%.1TZP6F*dE"lae;5I1`q*ko]b<nNkbAi
%S<(Hgr3Sg&f%bi-fD6WF3sskh(m5^C#N>iH_j.Gb$[a$Gp+8Zr'D?;Z_VS2;Rmjtm\/QXJ,g8Q6-H8b(S;D+kJP?<U+eT:\b_.]1
%P"c?@nS<qd,&Ri%$a"'7:_e:jlk3@9*tJNs1f1DrET/1[KnJ+p=TD2C1-B*3aC]uh:Z<:BDQKc_KfA6n$JW-QM0+`q]!5&.4T[K1
%gEYHYZF<V?Vq=2\SK`CaGf-ZN(>UVp*9rPm!E&TMm@%bHYdep'NmqJBn_T[X%h"G3A=IcSmi`GkGo+a&LisF!-0"L.RoH?Ih8K1^
%G80DCXhaUK$)W)^$)XX(Z]A[=f."=l0GT@COq7lA[DlK4mt3+B_W8/2h1(5_Ps2BNPu+DOP>E"B#F,.X!b2Qp.T>7\)8o66_fFBY
%Wm_D/%sAH4GW%\rbKpZQSp3n%Mm^W6AUIJWbfKX2c"43F<FUcc`?%^j'fgk^I:pr`Dadl2h3eKn$<(QM>P2rIgeF.r1NmC(B4CnZ
%QfN,$@j6`:&.<sVOK!=%26oGTR6;Z<i0$gWP6YXc"(D5MdHlbSq#Hul_C9gp!dQjOM6lF7kT\0@8<gg(_f>6Xi*""XCSSc]q$me8
%+<ZR1_S_$RnQutC=B/<`0#U^ZLH%%1*gSR,1:gSY=I^@?,R=Q@FI]uI3$<>eZlW1/e>'tK(miFj`H&RCaif8$=0"U<68Y`9hC6Xu
%_(,Xf&3B+,`cI[$b0d:W2nQ-1bGL3@eH>+aMkEsboc,kR2"m]<7Xno_;NS:#W2%"s?q#X8<(V`,)m:1UeBFP^^9hFbZm*YUY-9.S
%0j';(3qMJc9B%<Li[=&fD&gZKEu%Q>UX^.!?Uc(mo9j_uO*JpKmu3O,BW;ZJ[l1@pS&o:iWsdR.RJ^RWFX!)<4\^4m0Qg8FefH,;
%*afmPCgaZ%'o$U`_PANGJq-KJ0@YV[ja_gScr/_!TKT5.n!(1.lg:uccm0r0JJu4I^sp\QCBB`dnMs`,(Sp>Z,[c2-A:g*S>?-oP
%,%?mr?7LCm2'7]B-VBu/ID@L0-tC*[,f853fFViRc%NIH/nM:M,SAY&**VVLA\mb7KXVnHQ'7#C7o=-8$$+RL\SM=QPQR&5AnO^K
%MfpT*OhG'(4;ICDM0XqYISf]o'IZqSi2p]Q:SLH%B`G:t<JccL;Q<j$$H2$H.9ETWSS:'DPENmc8%(Kk3]A8MCbH.I/M4jr%H((t
%YMfLiokRI@:@J3&0:'MQTTItGV+md]V-"Vd,rNo9E5;HjSSo7mD8p_DDe'08H(S``M\k5^T=E0e"fPPl<OB4mE8I[Im4*W,]ikU2
%3k->5>1^RH.MuqDl5G1q_^`*t!aYuqmqI[p&4PD]ci7bRiB/N1@^%_Ym*TF-At-/2V:@1]/CWkr)ReltL:J)soPC4mD:D3/p"E0S
%oaX80\$g&E&!9K>8F"Hq"JbhZ8F8R(_L!D*\*bZT&,k;=?_GDA;;RVf:-!47@2,Y;ea'cQ7B'37;oqrmLDME!>%Z1<&(R2noO@YW
%:WiN*<H^?+<,l"$>o!.C3VA\Ng%*qhC'8#kh;WgN:SmkDlI.1=T0W#4",dF[X#QY5MfZqaSAEIiH.++(5V]SFE$4("d>Vji3bObV
%bl/JYm#7?-(uRG2GBqj:Z8l2+L'1W=eee:++sZO""-j7;P-ir;E,S7=PrB3PR*C"@Gi^n@K:SQ!hi?R_L>J$KP)LZ=MiP7egf+a>
%b>WP4G:g`;TLP5+M8LNuV>oGop9>pdMoYf\)](][cUTka"1LE2J(AXbBMj47q)34_%+FZIl\[;3L9,-ehdQEt#n$2,ZAs]#CFM-c
%-gWfoLU7c8$HCUhlXaD=a3=roYMQXRn+hXLOkJR9mEo/SA!"\=f>[$HB:91WQo=QM=oju5;KC[Y8Thli*Xm+7)ONlC$g3\I$V>+8
%fdiP]8[YHK)O04j:Z?IW1Q,cj:-+Y$T[Z(+`u_=pfbl(G.FIXq8/&[4m\thQrl;:$H"DoPWS3c+`<!mRAh&8-9OkXidegAW;fm56
%&:lg7:,oI&/BBUf#e.V^+tUNdT#eKCi/DS`]fM%!MX4/KHX_n3,5]fm0Fd?2>*[1?+P*>s7oCjgfETQG":H4e/)--/K!_fcCb81H
%S/6%J+ruCa!$nAhar2CG-*f*_.t4c["g\f"@YTl_O6!4O>Mes+B;;)hQilAp;HMkRV.Ve1fE-.OkFh,(W22kbbS@'/5^!++QHh*H
%G47(@oY,>-c&Op$E9+-,H!N:N.+f5l'DN`mL*Re*HXISp$'WV-D!9hlO<U0$b-Y<Q$lP(m1=S:/20[2iUrhjHf?eI4M.45bMrDEt
%5bZb7e$"*OY-sTS@4r65*d,$P5HYqc''do-n:9uo"I"b_^4*^.+ggR$')=CK]-CaaRle(q;6N?E%:&.JZ?N);CR;rgYD7UW-Vf/9
%2l,;klI42AnsMQ2:j:)@A@PnTHMGbNf2#a:s8)#A6Ft#r#^b&9k%"KU%HIV!YUMn5cbK^S&&A")5Y\j2+*r/WFc=\`_,.P7*#uG.
%E6,<#c3ZNe)BB\#4#64a1gmqT6Xr/4m'sR%K-CRDR/sF&@-ktGd91DiIcoXb.O98/&]J40eg#,h7tB/aa#o6W\&>!s0P=q@-Sj!m
%6Y:C%11FY.^k/iWRG"aYmE`^gOjtqNmDN=fYnB,EF>^=#`Bu?F,dp8QP8PYrPKG#92-)k*-:E]V+pO@E0Xo8\Qgn"F</tCM.1>+@
%9SU,]*>s=Y.&[?b&5>SY'tBmq*R5$gm1<60EHS/&Xq<Am@?&\B[Q($soNh-W<-,LBBhm.!&NEI76Nnf8=-OqO7atf`!rcV+>\>kJ
%:^Km-cr9Uf5,OiLRdm[[fi:V96RGc<3E&HO*m^)Q+nAH(n@XC>=<54!@IFQ:K.%Z-c:>^E$kOa8%4<'.PeD&E"Zg'hJ;"L>Y(_gB
%B_XoH=!J@pg3h8GFX3A:_]D,`cRnDIBWe?\fs%/"#^DaH<P[>Hs$nhG*D&)',ciGpKTRNrXr<tU"75Lp%2'*<E?kfO,<V'COHE/j
%UhA,:3k8hL.gg)$p_,7pe0GB?YCNQtZHf(5(I7:*1eob%'pS`iTEDqW_'&SOE5pS(q@(qf\39uf@5Op*L@#6+W%+)T<a6BYET="7
%9a<$l_4*pS+9<G*247qiiKJmand+QRUL8*ZLP')rN]g0O)e[512@:p_L+kEZ#b5S(ENF-]EO7mNGdoSM`4MaLOGSVL!Pe?"D-j`r
%fn,.u".BB5/HZSF6V$/>;V?H+1LgX0NrV5u#BM5%qPT#aXO(5uqL;Cn_&Co/9(kaTSaV1k_liqDT^s5KH%Zs79uf;)U,JjI%&I`?
%`r&e0D4<,]XP,ttL%e];r#=+)3SQ_ko+s9IMr<Rt,I`GebEM*Nmbm-fd"j,1!e[E!m2R=T_TZ9mPLpD2J0GKsb<Zfa:R>K'bKu)*
%\bVR7!+Kb*@q7!D#*^RTESG"X:m8_WrLCZ''V_<7+N]/N_tHe0cD3]_*o&CPhCo/]+=CnZ0YiO]"k5rE%CR9b'8$O+^]=cN&88j/
%cutehl2hkX)7pGI]eX_)B!*S=[L8+!i;kkQ9^BWY-"q$Q]sF-/aBZ-h]O>8&=$pFK1NS^<pd@gOeQ(4ZXM(9UMOt?2s-%2+"ftdl
%)Wc;JjsKo\lq,QZV2hjZIsq*957lAR;3sW'eY5B6`,WET*$(QGA1)oXMBEZ/.:cgbpf"-)HdBGaHNN\oT&uZ2&4NOIoDTD#68=k3
%o?:ilos4V_'GM1h)WY'S8liq<?V!N#T5!$c/!j@ZOdKPD3jI>?/>_LfOE-MYoTR?4URW3c+ju_81T9*a;f:Jo4JI;\6OHD4..b.H
%"#Z+bKBf<HG04-N+8XZk2ip/r=j,\tN=3uR874'Q@@$Hp]\6EWI3f37PC&fGHn;`@Wf)oA9H?[%BpI/S)bfll6+l@u#&<'l4\ZgS
%/uPZci_`K<\a3jm'eQn<Z4<77Znn#c]pDR0b`]nTJ`/B++-TZEfZ5M><A=dbC)k[I%f0pp"Q!Ktam,YE0%LhRHnQQ9_ogk3NRMY:
%,GBX@S2Uj8([E0G>[4N]>+^?k49QYag;>`KOTNgJ`Ls#M,I8(tRB\l/ZIk-^%L<3^/0o4XOgT'P;@Rc<-o#IZaAiYV7&un>%9\kJ
%Jh0D[g+5`G,UjE<(Xgeo2XR_fqCS7dCBpKBo+k*6iLX7iSWujVDTX.eD2MK.h`q+uNrYBakP2"[CAd-RrdAPZRn,E1M'5&J3-[u>
%E/ddLmeQh%^\-dRj6(pXs+3,2+F<o>BM8-UE5Q"6b#S(0lpBg"@(?GKDMn!i(D:u2D7g+MgFM_-gi(QIrB@%9>TWBc)F*A*^0I>N
%H[biHjhGkrmn&\I9Tq3ta(dn@)tGJI(dScJW,0Bccgd.`#g#(rEXRrh.-$'`o(*3i*G2P/J8.H21LPPp[KgVq#&fR.rn)d9M96=K
%_m*Q"m/]X<Q;D)ikYh,3pbP-Eq:hlg>4#sin.)K]_ts)2ZHk5h*EI6Y<U=,\T#es..lH;>U_V!ceJbPH>Z^:^3'lrA$J=5j]H'XP
%a/+GACJP323^Cj=E/Q$d?K0WkKiosWp2d/9H:&Behkm'),0#"2>rb#080MVG<F_77=GEc'r2<`Ic%qci%K'X3qPLX15`PV]]sg-!
%"#V\qqr"gl.-?rnCuqj#itB?hrTEjgKh^*_mPES"@rS7VNi.UCfn^YIU2'cr:Hm-r+g=Z2Y\*?kUH_P&BW&g?EZag8$E\_Qo_/_T
%YQ%j.hWT-b]Kff^KI5GaYb//D2G:KG%Juju`4o[/+>no,[RM<K4MEMNjAmjC@NT)%PBrNW=hkkI`;ZO<Ub*#;"$[?qoUKgGia>&R
%H@E$Cc<sc\7QF=$8NuD2`*mSf=)[O1_Yu5Pnfp!mj7&Drl&?CjP++*-Tnj\8Sn4da9QMXUMWT5P;hOK6bXm*lYIS3f=U4kbE8Mtg
%[r&d%F/ZVEmsm2BBK'ic5I^bb&7rXDIfqfl.dTdbfT2;CbMrN>h;$rG%ejl%]aJ&Q?:TbV^]bPL9NAAkk<CV`qBRu*O%,(Wm7`rc
%#;V(&Bddi_TNM1dGDUa(c-1NKlfbkNRgYkAX^[2*ThGl_3!`b5#ulKW9PBN+!gG*rO@J2%S:F)XcCeDD$TDVda,aae?D'1b\'a^*
%e#",ZMD/--=chi;94Og.F2,M]@i7k<=>dVA'[<&Id@t]=(Wu4RjSOh>C\h2$l_bUd2P7aeg!:dkXH`"j=RYTV>a2)<@q-XT=MslX
%7R!nI7uCkO5*f4@_)=N.k+L;HK11Lpb7Pk"![q)gjII?[Vs>POR&1d+QG[$k6OL)m[hGu:_*gRT6O"3%=O7$L%J\7Up/<r,@"2NU
%K>kguP^=EG_c\;!StZQ>DE#)R>-o3<E;@_r%4@k77)2+mCU95p,h\emN+r\G>`2Fj!n8tE^f"te.F=H3i':)tdY!7MO"bH("J`@a
%oT3bAKlLuCK&-SEUcd`,KaCKUcOb5RQnq.#qr(+r7H%i/3dcta5)WMVicP^4@@a#b[$g4/5(;eoIbrKLFKpb,Ph[RgY+_NU^J90h
%9RAmb+*2nEOTp*!!-st63DM#b2%d,uS8E=RQ1L_`:-SM=LdZ=6Z_l4*8W0[i*<VD_?7X$t%HqK)_4mS@KDZFL6<A,0luZjelW3Kp
%RemUB8hpB!"gM2U)c#@0V-4a%=#i\tF>H]1RAO6;$^Af*5YD@"^aq2AIY!2VUSWuL3Gl!%>=97;cmQIA)>&)E>ns0#$RHHu0("u"
%Se<Xj8Z7]&&D!6K:D`Rcc\H9md47u.hId5OT5tueC4_T7K\ZiKD=(8J'=PA*^AN^:Y)%a=C[6O\4mHoGPI&nDQg%0^fi6oq'724`
%;4Z?7Dol%+(<^A-"F;@ND&/frP#19iM_XMc,*GgEmQj\6N=JAm_6So)_=ES#<5V::)Aj9=0GHA&QRH_,192g@U0C3@19aWE9scSE
%Lh9N9ZJ_hdEAlrjP!P>l,&RR^`I)H?.-4($f[U-Y@]n_t<ukl_6h)\.gmogQE`d:B-3;pqhGJuB+SU\m198NGjp_ro_8s[g<0S&d
%?u9fTrU,-lg!,FaH[4?*,E?@g+/HjRHgF;8Ee\ued0.-+k,EK",Hc:KC)fd-.uqP0,09mC.8cSo5[^Kn*6<^)WQ'MI+,L,g@R29_
%0C26J7F`j?O>l(:%K.0DL2^M"`umrW4A@`0&L&(2V(iW%_pq.48gM,?<IcWpJ`@QlJcfPB@&6DhC)8l!7k\Wpf/+WLs"s,_F^AF@
%2'VKW>1>:Dr/BG[]C:QL&;H]/Er,?%CaKt^:1R9rU/N.YjZ)u^K?K,Fj4;;Gqb_QI>O9FA4#5LrNdiiWFFAW(!l%n)b8]2uG%NCN
%jDm3Ecf;*Dp[$#pNmr<^qbP`ImEE?=NguoP0iL#h>g+RbUG5\7nnOJ_=Y_gtZo-pJ1sO[E]VR90Mgnb`7^E<tSb_M$D6;4PI9#"(
%1f^6hV(D:INdF?TJf=*?SF2e^rqsBte@sH-T<H7'CjNN,ni8&qnneWpKcsUf4ReYeE..%<2HTgdqk+7)`\,&jf^.]_#WEGZ4l:oH
%hgc83*1T`fCEqrp5WMN"$E)96S,,uIdf+rGRV#!,9n;g0OX+4(Q?ji]!g[;0=K3ZWBi@V>:!7R]j=DO<gdj%V,GjPKmB1/LD4Y'D
%o/<"imkmM"9rTZ@i1(3f0'd%rb7l\S!m:mbgnk(Y'JfEIMeNBh`WmlZGu'RK8#/b)c\H9mF5p^"Q/58J`0!l#@iIq\@uY6QFN5WV
%EMo1696>!:L!F[sQP1@r9Xhhll@'VafG9=OQT)]>8W0EZNgFt*oI6p9R=+Ro9Z^G'ME.qb1![/?+S>7'/*jfK`q>focEk'.ATRMk
%VbLE'^q%*jAa^P__+eN*,!6#%\8dTFmo].>-+?urMO?hfgM4\HG'LkF-eRM\YVnH;IWrQADjT3bT(_pamc@+==&P%Gmaf^h,:-n>
%kIt)3DofQd)s.Pflmufc4*.,i)qtaQp%hD=fk9'm,?B*..F4=\_[&e\nqa4^Hb.u!:@jM>P-fV),elpfnfoH+>i+Z)=*qJ!`dLUj
%PP*)Uc"4JR^3mZ]GI`WUp[GPB\%B+J`eW[T?+VeWpEY`h462!uDlF,Ka*77EEn>4&lupEEq\=9bhqbL%Z[BGf0CDeARsQOU6[%^J
%*@cng+*Qp5BP6c$aA:NVduI[&a]%_/[N;^P?aK>PKN>a`!-o[6cd\KN4]$NP8?(!T,eY-#I,hPamb^B=<4VE,DrdB-8=us&s0.tu
%54dn#@<&Ws]l;;EJ#PVEDn#$j$?$pYl7NaA8TGjYG`:WLT2Y?s!k%Y_8b->.=o=`1G@C'ZiYP9PaXn;K,.R]c>nl6-,@JsPr$8uW
%;kLoR9mkDH'O`6#>nl3rKHli#'KA*!TGVlQXDb.AMfFZ5r$VhjB)To"/)hhe.Ef1>dhd61D^!ifDGEAdMLb:]M3tjkMFK?J8&i?,
%+gk$#J""9]VaP_R1MWC7URlTS@%sL:=t!<IP<!@+m:'(s&-"hDO<amt2b8M,&d8&Q\&n2QP@aTR9<3aKD/W*-fS]TLB>s@dnHAUt
%dq[!JPKnKNN!g^f82ahBgG7TAI;)`_mGL"@QHZ91hlfsqh5jKjcA;pQk0moUk#:[q/s@,r=O*k\>sf)8d']t%+/a\h^jBc3a_@&b
%+aqGE9'A+B$/-j>o9hqDh8S,`p-Pa<)rQ\^X$Gqr8(a5><U9WV_7/1gak<>t:>7Z>\#I2k34gcQ#T7\R)Rm!"aM]a4NDGMPMA,>V
%1`g,,%G%-sb$;'V3hIHTak_u3bOUhiZI7H8:;H0A8go+SF`C&5(aZ`NQ*5>lTR"3MWmU5%B=m%AB+=<R7tA4845Mi!Z)5H58%q7p
%'4Th8lY8K)K4H&^Q07j*k(`.38DT$0c[=S2WZOI:5C)"=Vf$kZk[O+kO1_;m;C^Oe^?tL)+(+1(bM<)^k4[!CnfN"\r;5Xlh4\V\
%(IQ=03k!71N8m-GP(p0];iKE[Mt`B,Wq&nsZXmILrt&a!IU6SC,/UYRkLindZV4N1=m6nd[TNdE>pu6O0pO@#*I/EQ#S/A%Y@SOg
%[VsFA(`#+H8Pk+@_4p4$7dVF3eFo-!#DGne2cZNL,Y4NjAp@+>[8C>u%@.oKWuDX"fIk5\5nO1_\kKgH@=&a+nfe4N^E"0[b0DqQ
%=ZANtZ[I;r&+4ZI.@9Vl+l'a_I9"9%H[boToe<PSFfXsS:78HOl\LkYo,R,>(;6_:Xs!"+Y?bq`QfdLCAf=gS;:Tu@S]4P84#9p7
%N%BQA0\Db)G%PjCne/5?5/Al.DV7pGYVX,Z;*`XH"=YhaD#(oYT_((aR-#Vn^kLY<D9+h+-m'^jC'7FK-rnc$^!2[#DY*90qQ:_@
%3#Hs0OWsr]1@TFOnT5G5c@RZ,a;jCg.LK,"mqS22WVuOpo"oEUC[UbN0>?Raph8%F4$5C\RJH^&q'+#HU<80uF`RAr@=4Yoe?cnn
%s1[=Z2$M*SU"=m\EA7akYq=gVIV\jQhgW";&*rn)`TEd/;5n$\f"^BuNFf8YgP&FMI!:ukA=eA*P!3+%Qa8Eh@at3Gd0F:oT2j6T
%f'oN8$<0`uEG:Y:207%\^-!WP6cQ7i$obMgT`%sd/ke_&N]ROW:GI*Op/b5.,484%`bK,T2PKFN9gA@(fe*]G]Z9";ZCF`k1Epsu
%R[OiNCD<"ASd=HM&?PX3"qH+(5Q8-XIlpuG\ruQ3bi,+o36bI\BOu1DqETc]2[(/+0oeJe^I,>TBGM0(&#*2o'Z-[(+pI\UIM\oO
%/nkK6lOg!;31>e)PEfq4qN6-Q"L4Z?4uN+L5&)tAl6U7i&Xt`a1e75K/j2&D9@MF-R^ZHpL#\%K2AqO5g:F+6e0I;*]`rH;`O0PA
%k$h:LUd?u;le<abj3@gG+=GupL"lJ#[e7UVRr"+4p#,t!WY%T:5r?_(+$(dUY>].6l*O3n%+Ohfo/&s<cZ)j("P)Sk"[5.,]K$[6
%FF*r1$/t'Cp6Lr8Y)&XD^tMca<9?:+`k@XY"LnI!j9kblf?u<[X7omm+33L8TZ(`NW1S.if;Zu^o.T75Kc93-\n.8sFM?fB<P+9&
%i"KZcEE!-ESLK%-=AK5Pk%r$roKkk3.cI%0=H0,9`lX%]DA!F0<nHNNoM!Ue.DS!.VKi`na3oXse;-is:gKO7S5X(L4VQZ;KLQqN
%WLOEi&lq'WETLrh?H77:1UlS@-J+RjPHgAWkKED(EoWSi:YQWYj,'H7XDsfs>0"*OLJWrNV6]L[(/nC;DcHa//t0PS2*ZX^eejYG
%?Uh_M&[e9Bp+pLP>t)H>N-Zkt-=Q5KM&kHFD=_4s9acN%TZm7/M3Y;L&!C';.^,#3klDF__rXnYFX*eOfp'&sD5[$:&GPNQGJ7sY
%[VSmSS2UW$$E3K,;0Gj4mO1-N7/-CXT64)Hb)s%$D#!jh3-(S#EgaNPbIHJ71KHQY;7Xo^V4"e^T#o1-H[Br+fYtqTMnT!3Ap-Se
%WcR*!/u_%f)P^;%2koP/M_oQoTcG-iGm`0.ZSrX[X&62/WQ:6TE.?]']@>KeVREpk%Khkf%X*o"kXQ$JB\&/B1HLXuXKM1=E=S?o
%`4?Lq677Zi7P/<+3K=jg50#)eZ"=E\9s#H6\IcCK[rN8^44`Vd6=%6_TGQ:%5arSn;oO3=UQ=qR9qoZ<E\k\E)<FOq/P#XUOMU9V
%$D(+&6f?$Eidj[;:O1rG9d8f4a[YSEQ#1'SnR`#=^55EagZiB]lZlq']7b7]_k\Zk<HEQqDY$KXLM#G>QB=R/QQfjiI91nW[kODC
%lJ9R.]fi^8oNC)(/UikY2Yo4A]sdjY_^;sR!]i"$U.;PI0$@[d1##=/SRp"8hcj^j;WI`ie#aer`)eV-q!PmAF0^$TI^F^qD;eG)
%NOTNRY,#c87U/Ks`=tm$a'-]L(,lm,=/%5k2W+.,Mm#tPUd?FD>.<'dX6H`6em&BGFnmfgm8`_D;Ro@C2iu4O>p+X*bN"4[*+.dT
%GePl'mke(+&:9#/kb:SXdhm)uI&Cs0hH;7YTMosJM,=36-P\@\I7oHd@E-P?C._dhMmBqc7%hJ'`f7i=e=!2m7-[kkijYB4e9mO)
%DI-@bmdg)52J#3>/7MqH-JMFK#XTO_qXqkF,hsRN<b#4nFGei6M'?<E%+^-a>GGDWp\S@HqD(6X`rh>3(XW0D3*C03a(;@=LmG`E
%e5$LS:ub+/[i7-(CZ=?eQhT<[\TcZ%=9tV?ERX%7N_@JZZ1``#1W64^m&8SdbV)+qT?F<=,>6V/PVm*s)BZ6*;DsGAWE%F+?G<L`
%VQA5n58N@o0['$k2@gaK=_,WXjN!]?gT-98"Gu#J`O[o9F=`cl[%uFb=:Je7eWqlL_V3XLkKar,bniLe(HfqY4O"[)bniLe(HfqY
%4O"[)bniLe(HfqY4O"[)bniLeBD[(mdsT!\I)49ZHAu;$1gf`/?gooHi+YqJGa`sk2,IeO/n>ieRQXWp)HX76en";nbdOpUF\NH+
%1WDm]2N"h<UlAL19NtD1gVDSml@rAM(#4cmDk]q41].^J;kPFpeD!PAY)>A/UYp-D/=P\&TpjHEb6jQi64'>Ah\<)7KPGE%)n?8S
%n,:c54c<]lo[4npFpLiP[IOc"htFXT]Z>%6oE$2ZF+7]N5.2pO0?EX-TI^Z!^EHWB]7=_CEIXKpJ+/U69YA$[@ord1_?2NoJYtqK
%m5_r[I!s\rK<Vpa_tM[8>G+'dle^Lg&'Q)HpYCn81d$j'"RSI;ms`R7Hs2fqVQBAG)ub\U2t+NYi_nI?-d&7EHclL'=_3<.)Rj,P
%46P)m;WBp4O]>W5?>CK-?$3R%MjCft@ATGFTk_Ue<men$kRNrs.e/MXa6"X,-a=T8\&OpSQ@%:VFO,p$4Cc"HDPGh@:#j7tO2rGe
%BQcDsaHjS'E7[1$IK,#l#d]LWNG2>"o#XSKpIZsO920<Wq<^=C:#@PHq:)_I0kI7`OdP!(4pf\^&#rG'"h,S4m(7r=$\Z=McTAb(
%ah8k\[b!&_03?q@YhcKCmFAEnK]sFu/tZDn!r$H`d6)SKK(gDmf+`(A\dXX9mb<)?GA[oIhB9K*)ldn#bOEN01ntQTd"na.5_La%
%MjO?;HRQn587qMgQc#h.ZjDiGDSOC2K/J,KEZ#XKLOS:__OeaC$)MGG=`WA'rd.j5^=;qS`\02urYe!6=8ZB81$Gmse=a;1rE5kQ
%=8:bi_UpQ^UqQ4ErS>`cY%Zi%Dleb)Z/\&t[(a.\PN.(ZjaE9hG:/M'k`\ppf06&.S<CF?rBFJScc`dV-rP>Yo6m,$]aJ.j0DY)!
%rW]s&HG7:*I=oH7o"Q\aeoJP>52ZP3E7hk?_(ri&37i_$IEroD;`=EAI.68M)?(8f1L7Z2@3K&tUpWeV]Di@rrVKfbeoS-s6]LhP
%LL8rrnG_/]<d1]okhRa*ZbO7>8,S"$l00BIr;nmJOQhN@FS[%BI>^qZ_5P7h(cZZo-'tk$"0Fa3en<2flmAZNNEi+'lXQ9P(C9^@
%`P+UC6\53tG7a.7>l,/i^-oKWpHG^amRtLq(AU.hhq':O#n9[S4H@a5;mr^GG/5fXWuL^/(.UTU\6IBdm,"*Uk'gIU`DX1!P&`1;
%qNjg(5WMLs@bkCO%kgc>3`=lLbMmE"cgk5s5;.Bg=8AfIL3oX6.(19OO;ndhE4?VD!$a>,Pg/URlY)(HrSTb7%oD<WVXUt.b:LFn
%$e*oE!j]W&K]N[&qfdjWEa4jHWq7$DW%ThnbK,NW"N<-V_M[/*^+=8&p-)]aU8KLJp4/GjI`b)Y`k"jfLi+i;bLTb+(Wip`jL>o9
%j^*4_p!N3iSBBmp>*o,&Xd9FQp\?gB?MLuo96j'f4#bht2]m@c>@To/aaAM1n"Cl?\Xr;"(jB7nDq0Zq`q8>?gUZn;'C1d6^Xj=U
%P2Pfs12("lAPf+_P-XCuQVHh>Yr0,K#!Z?_K7E_Y-OTk&Wqa"DfIeUQr,V6T1o`/DI*X=EM*/BAlP>"ZE<RX__t&p)E9eT1VQ#$1
%WCccHB-t491+Ub#_Mn9fM),2JI+!s]1S6iS<6KV$?N]DjT>,[MbI9-2^O,MIO&)@k$*i[N4FZqoDs2D)Is6;;h7%n1jjSGBT"f^P
%psBV%rqnENnujSDUW;ffJBQkpIsLtU48cq<'j&0I*rkoJi2;&.g0_#2e^(DtnXnXffC4dWrT`pJ-OhGirqB%b<I50/$TVMK&]s5%
%e6TRTV_?jXl@ON!`eJtoF&MN0/0)<#!sfh76\^(\*4jjVbI!FITE4e3mo?8-Ep?`@HO=T<?2a,pp8gOji7i<cS%m6+KrIu[<p)Vf
%WT%fq@Z^RCW4cQ53&J+p)m"^U2g&H&9,2[*>";f$^jFD[NH?16$ejO3a6Nd.jn;D$dA<GH$h`sFh%QmQco+VmGhle'[P)f\0(l<8
%T'2Z@""^56[nZa8P^0`4>bXkSS#-F=1Z7@5mmu+AGeS'@DdY6GV4Pq^&,N0fILXi(D:@l5`jB#C([4iDVWgC%VZ6'Gbj0a"hefZ6
%IEL<r=8HMSlZHpU%!p#Zh+'-F;VLj3*kI"&5JJH;o.k`o#O!bMHg02/<4;I)U5s<h"*-1=^JL%Qjlq+SIK<XF]l32*k_jmaoj"5P
%HN'SPC=/=2/(/OOcE[.]<J/$Pd\u=AIFKK9kNhD3g:h]a1U1jgEM>+u2!VV>hn1hN/XO>_l$/b-Mf"q)oOS]2SkJ]MdfK5i/DI&>
%[;RX8Ri_35';M-loM*fMk$$OXE&]^jh&5]J?T'Y(\O>=3%<>5:D:>kIUC5B:m>`(VAJ:i19D!AU^0MOO^pZthl^mEc^aJu!duSf-
%d5OeOCBSHkRkE<+VV*jjkfY;B4dYB%!=*5-9&.^7^CJB9m9C1uEaVC#Qfs@)s6_h,+8nZ^9Fsma,3FMGcgVf?:ZVO-Drf6[%"Cg=
%Qft3%]5=Z$M;%e"GstVt<&3n03[N-Bpa'1TmR;gEq?`"oQ@(,4%!C?O849nQAS-;l<q)&`J9C6X;0gQ3I+Z=u7;`S^2"XC.&8uS;
%HsFKJ(Sh6G&fTVG6Yc^I;S6IJ$G6ooBN"=R')cqYeT5Y%Ksmcf*QGRFOHHQ@jrBo15#dh=HAMK6RrN\]3&9GEIJ049!8UM+4fBp`
%+>&i%h190*8=h>aYBK0*j!UN)ODD/9-&cqR@3^Bl$<,8h)2_$#k`1oHn:!<EMAIL>+ct#t$kAbP;8Eh>Oj4F&X\ia,<L_@E3
%<Jn!DP"]tWgo#+^YBJh'RI*4&"`iZYKrG(6>.;8]7M%`3I>!H&Blm6WT^*3@pe0^=TV@@3$K"DNoVV>K`q!M[-AN%0&2-?#;3hI-
%(b"!,0<eN8Yrl^PYpKbS-$ro]dC7%^lD6+5>/(fj0<FuFUCI.AF,Sl/T(R@$0t5g)2<(8VhFUO>)DX4t?$;@phA9HX`AMt4FPVP0
%6?M$R-5N5+oE=CSNdZ@H#OOV,,"Sml%=1KE61r%*f0Kq-GVRPig.Z&UJA*6rGXdL_,UHpo:0N:.'upXq\qGJ-:3Qq4gh=s0G5seC
%5G]a[=';'a./Fg7<.YTM/j\[9iS8!ASL:68kb32tSV)]S]X-F`9'1Fj8A!?i?P>"Z*,ai/_?rVo$$.P<+i;#&EV'@I+%ZNJ-kMUs
%l:m!-:;q:t>C3TX4RFYt"U;7@3ZJJq(*G*F_Ld3&<=-"1`6?H+_HaHUCE]E;,'+f(\!dCM.-I#dlPPb7[]d^D+DO=/^ge?[6UU_b
%Dgp#r`1*ha2f]BRD=/H9,k-.l-pZ*RC+(e=cR$Sa+eqA?nDZc.7A%edGcoD?Q9CdJe`KKGZFsq7Kp\)A^%9q_(f-Ms*]c&ln118L
%6Pa0R?4MTgGn3@CC%,(l!b'&8Y8095=T6,S@@dnoZ^8Jfk[8JspL1bn:)/*%LNQBmk\a*]Uq>rKR'P0Y,s!bmTGK`3R@#b#$UDq-
%\N1;\[#m5.@)Iuq;s,3j9=;pai_l,"#8&N.#qVtk]GPU4lN<[$TT&C8qJPVDbB7GtV\D>=*K+a2>DC'DR_r!!job8"U(J@rn3Zc9
%5QhZP0G-\hg6\a<9$P^FZEKJ#kV=k733:!$j<6[@+GX(C\iHIU*;0+a(c\OU`JGT>j;?VFTRZg.`Ej6nMa@Q;</@n\:TCjWm)8*E
%^LQ>RZ-fQH=h05P@<i3U\30!EU2"eu0`bbFSn(m*Kp\--n1Zi$5*J>MF&j7H!2'!c`,8871%g4Q7n9,#G8Cm#'UdSUY9+l6&aN74
%J:m6b-TWt=(>P\<;XgJ8=m.!&p<d=t5r>j(ld4kP8$cL2,#dBo%jE0_d"PRjk\JBa:F[r<a2/3Z?ArA0>g-Bn@brhtNegK=[';BU
%eDG7?q^Ysl[+A-)73;J^2#l!&]HW(5r$md#:]dmFGdr#\VL[MCCf>XDZtcoI7glV3U/tG8@jO(p]#9HiQsjGnrlj^H(DRVJ?mT^R
%;H6""l"E@l3Z:tb8;iN'V$CR)CBu@;d?d]4.4TShFI!,MX5e.rV#^^<-,M\X@e)"m_n][`(lq+'I)Oua9`-U.080oP;"_IoC/)U`
%m[:)*_Y\<02Q*'SP5IV$QhfjZLb5oUA'.1e>6WG*1M*)h64tFQD-pl,5;b4D-/2Kd(Ej=RZ$CUSYm(`WV#(:0E*e?B86I$Br?bP^
%B^V9j@hC<0=:`Qi]XJn;=lX_6,ums"j`J[kKdd[V:mD8c'2_co_82@/9F[%oe;COF^'UfM=(4muqW3lcZOo@@,#"E%k6W#cM_Rq6
%?U9L(T3t\'Mkb%s<=^?1M\iN>Yo(U-iV52@VE.LH9klqMN.d*AK$"blHgFnT*tG>n[6"jG4h"f"_MK\e#N&O6EO$\-`T!q3M]P=*
%aDtVlV+lUTO<6k-N4#N+&9W=:'X]DJ^gL]8<4`F(n=u`rCpKF(<^S(=%Hn9Y3,(!j01o484c2e\HC$f0f9I^Mc#Ru:bt\C8cYSMp
%)s`Zq*4LWV.GMVlnM)&CUt6_NL^s#X.WlOD`JX`5RHbO&Q`,ZnD3^GKb3/=@aK)0%AcXr)(h5Vh*g;B$rC"dtoqc@3Hof-V3,cY7
%d,AgD5>4bCk>ZU3<(f+b-GoR-mPW_&<WdV4kUg&!iG/+P^Rt4'a`PJ<!0Z%5Q-qCcS6nCRV"fTYEp?njW!p=\>:J"`Bq9Hq".n;h
%:N-cgX6S61X'2VVR)I^/e,XSK^Ym"(edRLHmP:B0#;_Kc3>:pRH)<HPWieECg\BaEr[FEcFFEcXMUMXHTn)X22/L:qV%"HdBe7@`
%Ooh?f?,1Mdgf1*55%40sZ`-umoDDX7>)l2<b[?8^"?'on1$[,2>6[o@'>9k!XArZQ+nMKhHQ=qf<@&DCA=8fn.6c2j`u[@lajnGM
%;Jg'?Eq0*^\$c<gG8)V1INj4;Q#^0t9OeZeM7V1UbBD*80s<)>#K*B*Q[XN$RJ"a_k8QGeMNABJk[Gtc/ndp#A0<r;KtjD!.7juA
%\3qKlTIfP*_W3M$1!D/dFBY?LDu4lc)6h;)Oa9OuI&6Ie2ChtpCq1^?;S.^M72^O+I^P!RJ@p4afr0Cb`,maDE"-??+X6EH.`C\q
%?j"r6g8!4q<JlQeaWSOP!ZMl<(#.f*#XW]4@PrkKo!on8UrNeG)0sks^r2CRUZ3,FgH4/US+.@31/q*`M/G"M51X-cMli\G#=">.
%Ok!bK3q3q8[N]G&pIN#,,&!OIA>!fUKq;ILA!P@o?t@N?<O?RY\S!7P-u1[S=0Z\8VPe*KPoiJtUh^D\(CUm+>-UR,3)en.^a+3*
%Pd>%^`]CNjD8sE;IT3^@D,@4B[Ge%c'jNU)a1?:6CUIfc)[+247#Zg3WHXLb".S)ui1^b"647'EpZd/C@ViS1Z0>R>3n_WcDcJ&X
%<-5ebaW`,Ro:_!$N_I9(:qAS1(OD3of.`=<BMW:S*""LW><`PdS+1ba%3=eA&aPo*X/hUf)](GKCcFj4M64=h"E/H^Gd?@ncZJZ)
%0B-FcFc#eF#g,=E8`+&&X92<GmCX]jECK`>7jEBK657%VPgKa\Yjk-9g-Tk&PK$udpT**drnbX=o6MN&B7dsEglaiU?'R'JgGr;@
%Nc\ZKO*-`*gQu=Vl>2.c"0i]'a=Jl1nl3fF<o9`WgcSVi6PI%5JTI(pW_8q!#cOTCW&7o(TPtY;V;T7aD/]qaVp<mdPl_b]Cop1r
%J`A&;rh*fQW6OPMMhmas(s?drGr4\r)%JFI0bH62,"?0A^X\*2r+;uE$<9bN4E,!/(GQl0NCAZ1"DkenB@c1"a/ZF\SQuS3Ma9jH
%+U&ptBPfXs'kaEpj%2rLKT7p5&YL$0q[7>Ui7Ve+B-.s_k-+D-UsuVsYpJLT*Ti69]$,.ELFAZ-c97GJYqprC(Oi*MbJTIn,1_jG
%g*KDTo-*Y6QsGm9"+J,1_jOpOoFkil;'LTuA5*JUP0%F[Dc_!6p:Pe7hbX,!k>'27Kk+-,4@7VNctWONRY9^V2F'/p#FTAl7<W'^
%PL1kkk%+S*TF]M&c(_rhXq/Z\@MT6_nM>M=c%YC7-ncrZKrS/)%M?$`(=N/QBalZ$+uOJI?i\f>r+_?AP/e_l)Gej_ct)k:_YEUe
%qMtPb9*3X0>%)OX(INOJ,MO1^P/VEs&$nr646p?OfRheI\=,tJ)R`_UZ(f^j).F+n(S0@r5jq9]B>E[u&LPN66snSIK/Tpe-<j3(
%U9V]jG!L<$eAT,NSQ@mEE)"86F3U@daInZ;e-V6H[&DMsek"1D,sj_O#c%>J&o+]S&t9O6;IT_KO+A8a/[I'A[N^el6t<I_$!L3b
%0dnU>1Qe'9W%rM$($YXhfHar,LU"$,B*[rC#P=Fh"pThl%ZL'(jef>tpcQoZjbE=E[:=50U2o5;\VsRccK!8sVO/4QF+]XeN[*uH
%/l1VCXsp_Z'f@`Ypjn9Dr/'OWX$KSl4H:_QB50(7^G,Kio\l`p*\)An%o?N/@L/\?2`n+>nE;lO9ckr]c!3i5#XD'Y*T&\$ecT!b
%q?FlZK0iqqLTT:GEZ>`iq&!/s;?aOkjg:m'MP<t3eW:bb?!Uu[@hc]l_uplRndl:saU1G)GqfdUeH3#O3(,7/<^GqTE![Z,YW2lC
%CB"EG$B0a4//(3GQ5eL[73.D7;WR,f7j=B)%S;cV,p3.YO#C@9^`CK!VaN`_s(nT5e;"XX/g?>B=0,Pt.4?@E=VZ].jVsCe?sCgl
%79Q-nl'C3./Jg1TCrASk#g2<W_Q.fV^reFUL"rKVLLer`-KX+DaYus4[e1eL/4b"\<Sd]fc(TH.H*8G!XM9g)V^#O3,3gK)I:8u&
%(=eW#U6i@YX\Zk&"Au*eh),f=Xpo`68SV!C9<#_lF:42P98(0TRu%!)A#5$g$L,!"FUQ!rmp8Miod4O<K?\QZ*>)cZ@>Fo$De@-t
%<GgS\4j0uUI$3kVEla-3a6>#m]KT#N?g4\k)P-s1[Wl7YI\P*GYU=VVTn@uY2BS/Vi^ffeg)R'/Q;5SY')bKD_k)I!1kNu.OAf%g
%U,p1`2sSQ_:iJ?/&[IFX!a/nN.`4?0-jUpUI[0Fc@_j+e[]n'g_doc&C\=2XL7IasOd0EapN:KZ,#<:>i)&G`Wk>m"0>g;g?jAK3
%UE<s^1LY]/qG:)<@2JE*pI"N[L(5!2Ymict1jG&Mn1eCELkEhA-m!'K1Nd.'f_(MF=5>U?.OSI?E!(JLYqC4kb2`9d:;eqDAbD(_
%2Z_I8&>rH-lsss^=RD;eAts*F9lct0:km".LM8s6iFZ[.hpf,(=uQ+5[jEJND?`K:*%W?_]kTTbD0[rJ^%"!KV'd&shMAeqL0]bX
%\6m<r.`?'i&;Y/Togj48N_mLb@b@Iko0ghWC*:bM%aIa<':Tk>SqQY(?H,L97@?`I/M&E>J0j,4[-d1QSCn*9Ea',%M]Aclg_PQ+
%Z=2'gFNp[DU")ZoD+*hM4d9/PCNF3<)YUs9&N#V^YIpE.$P-!']%Xj@iW++G`n@RHQIZRC82*QA&HAis>Kp=%0Pj-1_[ZpCaHU3r
%if:h]YChQZj>[]LQLW)AW;u5&MjemVAU']rpn%T16ig8W?a*+B*jTo'Nrs[lFC094b0Z0T@AbdpH5Io)PGSZr=rYIS4ENOu!f/+e
%NaW`l?,ZkC4G-86,-VaE;T2>HFP*8(YpKNgmtRV-Ym&r8+>qfEMhHa6\4<m_i'*u$QiQGgV4od^r"sOJRs8ftWBZ'=RSRqgG[;hG
%N7@dMj%'(5LHiBHh>_8*+ab`eBi-B&D\a66Ai`=$jYW!(0?>AAaA:Zf(4*e+c"\C/N?eNIc3So_jqnD&d/YEG(cbY/7,3H<7HPnp
%8Z8=n4\/gWW0F8+Gc5t73tT!)D[sTj>'&ag+C7N6;6W'mZSR+8X=EM%8Rl42X9_C4.[?1,`IfMI'i,T>H*VWmM1A>Z/AYPq!/dZD
%cigMJQ<moT`W9Eu%b,Hc?sG`_hI$Hlf913D9J][FG,Z<PL<NOX8*I,_.a!\8"KROKX7f-&H$#So^.]#9K]FEmj[DU.:nRM<&@B\W
%jk=@h(aU?_"Ma+MlgoA4\0`5N/gs1/G-i)#-4_hK:OC_`&j8?[^lZ[[me/R$SR!W*6#[5b`kI9Pm[a3#C:Bafk.@!k[HH1:9QUpX
%[u&91iOs%FC#aGZ>p15E)p$#md*g)3-H(gCk'[NqmG;T68q]Q6CP\kA\]0.#p(f[)lSldV@O.p\DNL6tppQ!PN>dru9VO=M$nuY*
%NOV*,XSfHH_S+LHfee_j<c#unYV(gNLrBAbfr9Wp0H(mclIFNP7Y@%"a*_3@51))-K3"Cq-f[`mA1d*$,bgu`>>EkenM\g(_4.23
%/uSk&0LiN3`c7%X\ed`!C(AmOT#?4m5`fl<&3jA<nL]X83(dS.!^7ThLU1VsM!Df%VI4h6n-M59b[Zutph5:\8[^(6>mCW<Q_`6[
%b*H-8?P0u.B?lSF^cl&O71Bm("QC'8:PeB&_4rHh3jsap&En3K%u7b,159>rA!-sO+c&2(&DZ!&Cs4Bp+*T6-DNf4kHUVc2:.`Ec
%XUV$VoVTWGYPLAaIhf*4k0"-.PWE;-A?k-jXumRtQJ?T'Z\AiI>LLFuD:!uNO?2HSEK[CadNg2b%bG6J[Td]DG-N^>)J!hFM_hq>
%[U@!kiu2\k5-20%)=\XrT7WWedk_ZrP=74UVOE^9*9fFABur:e23;Q2G^<9`A(&%E],u6TJqTLn`:W@5K5Q)C&i:5V,(_MJ/r2Ul
%1Q)g"5WJ+r%Cp/F\GjHX8OPcVD9+)Q.TKL3CN7Kc,kqCI=HIl\Ppnd!FoA(%g[,gl>^/1d"M/LgfM`*348DtNiPl^W-GiU&6)r"9
%1_E7Y6"aKRS2)9!;of3Uij%3<<>$ih&+c0hoTk(l`aR.bG8#lC%h>NAU3SJVK$VH4<nc=U?Tq",U9Y=9p8-'3g&lkL=%8U?q^TZ!
%1<CC@D@#U_2#i:qKkVLLq3lFi0sDfXp$<"S&EO5"etaV.&#8VtR*ehh@ho*-65c/XRat:HZXsi>@lS*Ag,qD)S$FgRQ_AVcD(s'0
%;sE<o@Uh>_(b/%ki&Lu3F:gt_K;dE2*qLYVUu91';gMM7+KJ:pJ(FEH5,cVtY!RtUiaGVc405q;1k?G&I2,+mKQ!5'f6!<o)[ic`
%Zl4rMY!Y#+A-NcG#_T[i4!+iQ\F@n=AI8NRV<53]lijEFX;ZPRJ^iiLRg*]ed5#[!36SBp\%k&'m*65t.D&FK?r3maU+D);e!gfm
%Z,^4.hMC4E["tGJgd/D'02#h'@Lp8`i1RCl(2L;s()RRtTPC/[.ra,U1:#<b[[Q(No15QO3V'OJlQk+;TW0o&6OE]dl+1:LB]9=K
%<jFH4=DM"d@`'dTN%kNf-m?8SEfaRqV#J<8H9>DWM5^:lD.k).%>C^6&*)`Q&$;fb8h3;/%mA4[9>jXYg:L'Q(Si#-.D:!qWmZ2I
%3q4BP8@8s(J:+WSa-DHT_M]U(EsRt476j6s%/0lHM.HTUCCmeT4/I#57I(\g@imH(j:T%;:ODP`$*mN'`0>kF"l1\+UPU7`62D1<
%3>$J,,*Pkr:(#c$IDY2&(=AktS7;--QME#/OXHkZGR9"4J2&!ZRS%P[.[-Hsc+o0KPT4=[8f(\jdCmgkP>E1##L=-35#$.87`&R`
%H7eb7$+r!.ec^Ae;@V*H:!,%=qVE[76oBd_ltQ+G/IiH!BgT:B7%>8d6jeO2QNf2TL.V67,+;r$H0dOV^D.6U\5TusQ'U_N/Vl87
%:cu]ZkUMel0da]/9N5OB2%^/Mat"O8E7,Dm#;69;(^+WZS&C3Xgls14<r"QppseM\"m2@Y"G,q3S7q3E@Wr'h@Sn8aj:'fWZH"KO
%63D2'#M#QC?scXO<*gn/"SV;dGoSlZ]A\2f2H]81HG6h^9IH9oNcr[hG[*&8&\Y3g[a\_-3J`rW@T/ta\69>o0+7_r9i%:Z6KhQr
%dtUVXEh]mW]Q-\#K+G7RF>:io.Sjm6G/;\&co4*RWQKAb[TH_jUBfWr#YcFS+q\8K<2"4M,`6Tm6*6sD45'6okaS/c`['C)3Dh0k
%C8Ua$H8.KuH-2Z/W#9X8b8I1O5k8@<';_mZdKOu@BC!AF!\6;651!Tfln%KlS`">5<Z@msX4eF)V/L[C)#A"9*Z1H]U1^kj:fL3V
%\kWBkR9?q-U-;]qCH`T.Z>=Pc.%T^_7ZTG%qrj;4KK&R/U19kSTQ^PL/a4[XZOO3U<&#K(^+0d+(FDbML=Tbq65-i[ZK@lpEJdMU
%Zd2_S@^Cd929B.0W>RZ+LF03>m`<?^XP+NoNm7s7?rbdKR'0P=3s,(neqqa4lU8%"gaRH4W9tB@%4%BP_P1,K'F`[a:j>=m;6dK+
%K35l4S&L^O%m$0e7l!%,0fO\9P9G_k:bF5^epB<6Bap#Y$o@$Scn-R]_tX>-"*[:"Z=1T5'P>:`Z)I=43><>$NoC9_Mh0Mq:Q1)T
%honZ-:cb,7BNcA.!HK_bDk]n9`5Ba?Wdor')T:+/Y*iN:AU9]%5#q;73_%"3Yn8Z**FsWKg`h=i6_#n0K>,Q#K0`SDD_oopki@N'
%9FQ#$hb0U-WQ]cCdmIUR[a?O7K'3u9LWV&3.79A^#pK^g2IOnG,tp!J2/$.e0ReS(nX&GF,&D\%k=k)G`"<amUS)uO$j'DGJE*#5
%"p*jK=U9_qClq%'idGpMB>euRiqF.@N/`]&Q6Q24dnBePC7B7+n=o\-AmI+UWk$(!cHGj135dhJToT>*%?k-JF+OE:Ou&+-%o*mP
%>muP:="RI=0J:g/:)pC87b=VR_aN!V"lr-!QN_^-1(R[PodN$YqPm!6/G`*PN?/E3Tc6.[.!.6WG'4YE4;oK;lqBgK+\_<9$1'!;
%J=2iSC7jm-$MSQ,(M2prPal..,AOD\.PpXZ&j8dSOarS5bYXf*kFKU8huX<3aPF+m5S'dRmJE<>\%)F)8G73NOsSIkCL,qX:hmGo
%RqO]oPR>T4IQL3#J<iY@Q!U=e"-LLdd0_+LTu#LBJ7sV3&/a,?LeH&=gP,E3fsmud2plE9BH*Q[ko0\rFNheQA862f`N_!k;O()P
%!Ck.G$mYYq$fTFZ9GMso4Wsn`)rZ+1'"1593n"DbCjpN>fSA^uC5bmoA8*?:"iEii<C05`1,KdN`UO:/Bo7I](n/86WaH]^(A);G
%?p21"ae^&VPQqh>K_@W%mt9HV1QR-<P4jQl=3=/(1KX)</sLae:&Ka/DfR.^,R\.sC^c*$F#&d>jm:1qD^X,\/&N/,:US=M8)S&D
%lU]r!(rC]\AAX%Aj)?;-L9V^h%H[1?f88oT4G'eA#*b>81)ueb$`,ss%%rE]bR9`6*FHt04uNo0CO"L0N#%FpXpU\5jC1QI#XshO
%"q#uNO<'%#RAO`b%,Bp]F?#<n6sR#;1F;7kFHA;.)b;3:XbH#aJ5O0<EYl[ILC8Wb.hKL]@Pe#'2'a6:M8'XNA:X2"B^LlmUG?k?
%"A[gnEtJjRehciK+;hO7%!;%[,U>9"6gt6gE/\+uoI),oE=<>4=()>3QWP16:H"<=d*[92lo5GtJ@fDW#$`*uA#U_\O*"I?bVC,K
%%#iXRbq<TU.0b^)G\,;.RjOLQRWte>m*F6cohBDc`K,s-Ss*0ZSi?`b'.4?2N5sqek\[MALs_1pD!!!j#!#<:!)Pn3?O?mbpoI*j
%@@LB$i?pIl0!UIl%pAS>A^3QX_D,Z!0ZOS25\%0h`CC,8\dD"qDTRHBR'S3dR!;5J7D]-e^?"W9R7'D4dF6O%83mq2B(&!13*BdL
%k!:7\*Hb$8fb!"j:'27BEpa$U62:ebDjF',A=9SECY([P(=,IENk)&A=:\-J/4:pP!bDoM&=be99?n=k*:ih7GQT%CK_mQM_r,0&
%1;"`o_?Jt(DX'S)i?t%\KHX<r#NC#e;LnDjA`:$Bj:%-)f.c1*gJ21iZl4O0P*C,EM%DrKoPc_"0!W<)DAJOF\jUhS)Undo3BC#Y
%Asa]H46Ob+m+=%D=lU:/S\%FW'N!WHo7Y=mm0jZZ#rPUT3icq#'Z8Vj7R=%H)_#@1R]kEb:/M52g_/.6b^,:=e;UXr%G2PQoJ*a#
%gM<7<VD;59>q'K5lARI;*]2-gm@*^@Z6'IT%:*CXNH#WFcIl1d\1^E/noi-Pi[KYD-CIbf6tUFh%8D7GX<r`Kb,oB\H&X^'\ZScL
%\!4R)>SbJhZ,g-O3,[faKf&04!\(Y'`bFt_>Bl0bU%%B@VupZU_9Vn-VZDf'ZVs9;5>t"^KeDMPU<-E0Re4uCVqLqmY+fr-a@O\C
%g%<?6mT_=k$tEtu_Pp'_1m;.J<\j\-#+l:c]T5I/QA_2PY$*$iA3^3:mQ=G>Jb4L5&!Gra=hPWM8!%;:.3A_);<Mmt8gUJ3NM'O7
%9J^LA(6g6P=%WtM3_*3/GpJEpC-[9UJd>%WjCs&u1-Um5p1[,s6\uL,kM<)*("X6jnn@eBZI@5Q8oHu<&:CYMCJ2TE=InV=1iGB0
%,s-RHUWZp)i)1GuTS[\;0!oYh?e=Y12/2Ped2Wk-FK.`+2N)n#1cJR$pG_%jR"A3U#+.g#ib^)V5cGE&9S_.`,XoRu)8TW9MNJK/
%r1_I\"nC5Va"P_?2P:S<A8HD=m,)^1O;5Wl#0TurFc:i=7\sQ*6Bqs7.%_dlEhiQL^OIfK9J<j(bl260$>27mUV!^Q+_HIaiI3<>
%WCGKG/n4^\K\[fLZoIuq"@fHT3K"oMZ8d]E]2-]Po.X6hXRJt3p]a)<,UEM(=XrRKabp)67"(V)$e5i2OniN!+&_K8mO.J\n@*J3
%8C)n]@kS!52M"/p]rTID<(hW&P32V?c#W>r_;.q/MCWBC#`pq&(PYVVSnW+/a_,=&akt<1\Xrnn_ZUpUNhp%brD36>%!NiIOYLIW
%)F,Ejg\%E-'PdtKjfGR>Ro,`s\7M["8oaZ@DB'.UjBZ5D=ir[?*_4K:P/IVlj#;Mb>uIS/"q*WTRtThSk)rOtb2ZZ*$Bh48kh_[(
%#]@:Yi@n+bGS4VVIE@C`_7hC*8WuC^B4m<F'o0`%DZi8ql=7W;cV[g.7i1)NQfJ/5GS+R-7X)9`*N&>(iE_J$aX"InXb5R$S@QSb
%h83mLC9n48DlHMJEKK55M%TfjJ2u;)_QfZm!9*Bd:=Ee3-84DRDd*@hh(iKMB05>H'hZWCO[OWV6_qA@;1kL[L#')P0e!?8dr\g"
%j41nidM=;/`7"+r,"C0T/'Vj\h326@!NOoJq.*(Rn0P^/0\)C%m(3dRl\M@@mO4+=,2&n+=N-,MdI4&+q9W-(1_>!:8RR^p)#Qe2
%f9)FCX@,JkD]UaW5eYq8P9lH6MN_&DV7hmMf-FQZ'8%^VU@H<g''Lng<SOKE%Z8BW8p!?>@/a1=)DSjVjJ^>9\3u4tIJ0V^50MN:
%Kr5ZLnT-$BI)8IhX/("Un.;XZ]tlKOppR;M1E(I9:,^6@<m=qIC.fp_aW-#F'\+p/g#COm(Z]PlKS<&rC-Gp:EHEO21ubes(2MS2
%a0d["?(?TZ=fu!4,p]?METf]\][GOM7q[]*/m<Fq9F6\*LG^Emj!<\%l-#q_ooGsZGdhF6d$II+99FUY^2/*os(K"0ENs8)d$IH`
%9n.YT^2/*os4rp61UkVZ0BgS+\t6nXku6&XooGt5>?Ws[&,OcYZeX."V(\=pe3BBKKs5SWbUY!e>>W8on;!mDH&6=f>aog=#HOY`
%R"66L?51OE/H(3]Li)e*>KphU@=$HhVQ.anOf4QaE<6;31nUYjpXqdpWd5R3Nl^O''oa4Q.#phbZc/DpK;c!o_ksLbq))e\[*mPI
%C59GFR2:>%F,<*Gq89eDbfQJ99oJOf;q5G?e[+N*TPp:=bUI^bi'`X-Tl$7.7$i3X9b7)tBh8uOBc8"A0l<Di7$i3X9b7)tC!X&Q
%S#/>=RVnS(9W*1p9b7)tBu^S&Z>&0G,D3QcEJZmEfebkUIonYWk.'1M4k)/o0I2!010$oSKGFS[3mu"X/LD$#c)1q$Nm=<t2UD<U
%/dNqiJmuj\#(=VP7Qm+%UE4+f9bfg0RndeKh_[L[Kmem^ahpE'W!W@&(]NKgGNU9Wo57bh[PL_u%[I$fChM,Z&gDD1+p=]eiK-fd
%o$JBD]]?#L,@jZ5,m`'=%]r#EF'pmYdFlI[ijrYi_<+XZmSS"Zh*.I.%!.Y1Jrc^i&o=9Y(3L'kU8\2Ehoiu,MOGcmX_IUE,ad_S
%,+.!!.$X.&D&]^Eiel0T`f#tMOaF7>nkG`PgYGI<Ml$5q-@r`6ID%/q@^3N+_"Z3`Sk[\!:"Im;CqWMaCRRqn9Q,Mf.d!/$k%pSL
%=nrQ1e%5.Le!h_4lH(@KkfK^nKnG;bo*W)B\lI:]'0d-r,_VPs+KX4o`&2cpAd8@GT=1:6qPKIO`D%^9kOos\CKMIKhi3c%!j%gV
%`tSnR7(k2RD6/\EU!"BJgNHKYRnetZ2k>nPh=G_f$"=JecG_?aOqnfR?,!@fR6isf47@o0Fk"'S]51]A3s3'pDTm)b/2k<S89>\,
%8Zc4M,h6!(M^mYq3ms4P#is-O>G>%uPcS2Ed52?>p.D-m"dnVP@;(rT$KbJ017e6l8R`@%>`*E?Wg_<K!>+D[,Y&,e`-bI)S`dAV
%a@4MRq"r+km@fbJoJK"--$=C*aPf/.oFlEI8S6g?ZJqTc]gQGc(;$$*\dunJAe%A;OYQi(i'oP&<=h))7:-`@6]"hpnKAt/kiH=l
%M547kKc4b:1uGB<Kn#J2C>iDLP%t3X]eXsdNU$e;U6"MYO$:+?e.nK*Pm+`eMAH\Y02[E3cZ=pqob$tSl`UrR$#ptl/5Co*NA]"^
%A\"X##(Nu"*RS;q3&$;CklOGm"lVJ@Bp/P$`chQ-_+I"Adl<AXdsU6k^I:kK#1NK:/mF4bbaRL0luXU7?-P9ab;XEr$_r<,""$4`
%0tmM+b"ssT-fMRO2,mWlObQ!hpNA6p?_+?Ko%LRA]b!%,*n,UNcANVW.3($)\)s\J5qk&:JCg[pcSl9lfiGjs,\PpQV1,^6aY)sX
%7S,73aIK2ZM$$Hp%#]00`sm[M(>"?67/qlK/0gc8=.L;!1#!=dIO.\u8h@]uJ=Ac";NtCEPEPLrR^B(t\Z$V.9:NqN^HDbaRb"b[
%h&Cteq]r`h`dhsjFAiY!j7Soo]L-p>oVb.uUrJp5/L5"*eu-MU\'>@>M`,7<<1_8,"<gR)<7U&7XKn<CpS8j%R!HO]K2%@!q-s)8
%Mt7A#nUZh9eLm(Tj9HpVHt/RFLn%ZfM3:5mVc3.pcSOA/Q!cE7>+F?0qFWVSdj&k_UE,g,Jju89H8a1LN1M;'`7o95PX!S",`E5[
%1VucpAuTu1\O+'#\j@4,d'#\D%APGi'u@:kRN%KUZGg/6`</J;7@_M,P>q#,L%^8#0_(91/b\7jdcL1mXsntT=Iu%]VJO$q@^@T!
%/7,2Vr'Oc.bHr]N!m_T\6]J5u'5iu7T("/LMIq_CS`DgU,2KM4/c)\rQb*XoBWmK54s?/UqT@[]UA'Z=D*CW%HJ(8&%[cJ>CoBI1
%%?49m/8_9uI:;X#p13Te`\]:F+P8O.PdL'?&9sq-YG!rLA0SrXS1g8%U7!fE^kA+c<3>bWoB9QkZ07[[]('"j@Bq/K$\L7T25\#D
%Rl@sCP<_n&W2G=m-3.LK$l0B_!^_iLZjTcDqE0_3-^X%4CH;ADgKeF?nCg;njG33X8bJ6W!Kf-XJRbY+3KKN;T!X5'BJPDk=[1]\
%H\KD4rfkOhZgHN2[cQUS/`!@.PgF)App(SBkjEcq!Zd'UO6'SpE04WGJ<SP<kjoX/>-uSJQWJ4Oau,KNWdq7+W-3ZJG]AX2$Yl1M
%AI)G?s3nF=3OEbSinPDC[#OP[N:jX).#'=1cDg%1oWIJNZnl`T&cr&*9*=^H6IM[Sil/gt,\/cj1;MNRaTc#&P%).C8L?b;_%&+b
%#&(Lm@g$JPK4+B[X-<]5,:R[;.ON>:Y'pap)HKn&`2@FNCoV(<k1E**NJWFk)jI=C+4*gBm=MbYA#i[>:-^))2@8q#E(UhYm&;j:
%?[t/A$"h/)E/Mf3Lm(%f<nF.rlg)Y&_N)CV<s]NRi)N14S*kEPUWsIG>AW(/PsZFpZgMBj-P:m?7\)NVe.@7k3h`f-&k%A"/*D[&
%4FlC7aOt$N#f1DOeD'C2K4EPa>O6M_]je<&rqr/og:2HmE'$bc9g)=CWL*]IEXJU^"_$Ld#XS6EhG$C0&[DhK"*m.d]bO7J5pJM/
%@tVW[3WD/SP@@ZB+Hg>WQ/c1;GO79hKT\BNQ!l]VYDuAhn&#pLV"Go#7+6iq>EcCPKM"6R34-FJG#&Re`<f9G+=^G=#"*;8R/2ZT
%L_M^=kHSlCb%]1?;4L+E.uKu.5h:\34>Zt8-\Nk@6s_<*F/B4JYpjAJ'Y))&)RJF..#s&6E%i!P6sh^edGcI`oOKYtXLAaa;9>Yu
%#&3FM.5IfOWV1<))YRL-Xeh.<AO2qM1UhUc:-BJcN1=0dIE'g9R3l!8%JhhtIFN=p^,aS1`p%'`M64R_V?_P&"DkfbnKp'*CKFYI
%j$rEfhCJt\7RmgSkgP4qEo6ob2j@2'#D\q[YARZrI=1qJ[t"89ds(8P^O7]`j7>LLGeKpQn2IOX5CN5&l`9fUa6r5C#cZ_[7;jPn
%=8p?>q;(f?YQ"bjr@?7oEg<eVG4gprm&n'W0E(<?d&dF`gNC6)^I%+;T3TBTFBF8e^0?_`kBp3PEtu=*;8MSG]jJST:a!fsA<(]c
%J=ME\JV&7%@G2<$TEt1nW&J*6:lit!quM$0n"*F,$6fI9fmoqMqa%ah`H1<nU(dBmM.FFJ@OnB"&[C7[%`HB,q30A@0I%Cc9^lO=
%imGOPkH*4N1X_Rt/:m4K!!_n77^k04LZ!\*%jr'jj@MK!Le*k%=q0P0jdNl-S%0.oXGIEbDB3gk)br!I*i[-6)'Hn`coJ@u8+P*K
%d:/8Y.kL-nc/`0hRahL6FOCs"q:&t<>dCpqr(@5#oD`h7223j0*XrfOVjq9a`,/"4\sl3i%:5VO;<FZI!04]?FMdt)_Z];cQ_l_n
%H88lmT.C<M.<,g)a=pON5,L?2&m0,CC3R(BWIfTLd=4Tun=l$&V7?kLmn>&ZoUXDljpg9(C->@74Bd!SOS]`J#e]gkh*jA\Tug00
%[:^Eg'Urp`kaTaQ-q"JWZ"@H<9r]Kbj2q.JN[!.'2+Vf=<?3;eb90EN<'\(3k8L3Ma=uS=6?oKZg8[8/OpM_Y:\#-_7F&L&&\H"$
%,06biN6gR6NB,7C2d[N^=e[9jj,U%O&>]odT#Q[ZK!>ZnB8GeX#El<89I/X&'62>*;-4):qc!Y`Is?$fU.hp*dU-$+Mjd$b<HhQ_
%><:l2d9+7RbQMB4]>glb_`^@,2=#;+S>_;M;ca+YYna8MA#]#EAQ7'>QV&],--oD-nf/sghHT@<2HUFN/#+Ir51uqB#Yu4F.IYrc
%q[ZKQ%cV(eWI*KcNr[M%^_$dSPh_W4+e;sQM.f[.)PO*`L<9Z3c&;gJ'YA].HB=57VRIbq(VK]sM:N!Y8kLoIR3JBe=s82]W#<rF
%qa-Z@0P#Vl8*q38846-SP4eC`-s#rW?8@8^o9;=jhjNOm,3$iae/DmOO9bRubD7.(-u#Qf?Bd&s^Hh7XK*ph0fej6p_,#n-OHD=R
%TK^5`i:X[P,b)M!R=_qu7Ahg!h=%`N17muh\t-eVbX@e_=JQV%YKo=F%0XRV3[Vr[,&3rZ"0^Bp(WP0lA;Bf/0V0I[H+M6Tdqg=*
%4*RTE9$BfOPFn6nTc]+nII(%I2313W-Zu?SR09mpA2!$/D2l(O9S6'U9]Wbu>p`[oZ6<9od4#7Ih4VeDd?PR6%aiV@;6pd:ZU`2h
%XFEF;-+[j[jm9B0BhdO13PRJ8$W!sG3[2^5a?4cBKY]6,N*-rS4^>#8M$jtO=+/`m6J-BgbXp`DKPh$$9JS&ZK4p*:fD+/NSO*O:
%c7!QSK$Y[+&4^.QR5'M2(fR<@/._@2N&8Zs8MlnM\lM78FeXS2CXEI%in\k12&0Ppm0fqi<[ND:c,@O$76mC&n%"1idMYXg^97<e
%c@V]0VDKYg*.g/`>bNa3,]S?nA`r)H;E"k9EXe.L!^Gq&:jtYNps<k0D^OaEk:k"(?n<b"cM#EEkb_s<[&Sr=om)jK\Iu*aar>5&
%o4`*l(&8TUiSWC%mS-L"^hbZt"[!L$UFU=en]\@4@qI7%3aN];HY)3$<LJZ.jd$ld[+o:hpDsF):YI4IA8Vg@AF9Rh\F9*,mPHp:
%Qn_)G1+o1iA$^Nj.?5XWGGthUB!TlU&!Q47p2JRA?q#c*()%c4];kBF4Z:9(`Xg6\j=G<)5f<Y,cfYD`[=f::I4%GOZM-'=600Q2
%n<9"gYp/G#CXP"WO&B@-onkXg=%\gN&CE*hk)PSfQRcF1ag<CHMV^$_[8I'R,1:m#`_B%'Vi"ET?>JKtS-qj)aa9^-Pt/+VW<P\T
%d_d-h7@?(jBaVCL,)$VM[2Q4ToZlYimK6XOR>A%<rCn`f1doBp?MHf*`JA+5mTG[!$Ak_9USr?e7*8!B6PHj0d<":h15)m9:Mg7N
%l`-a[,,4dq/LK$L=-JdrN1Yot=\EWPSLR$nrpssK^pbm>Ko2IFV_rg(NR]9cHoP)nFa!cr0o9"ai-es%c"#`N*_S:4Jo`gF(2<?N
%"@F,8k:L$9lAn#tFP6uT0+>F&,-]=pCWpKkPbO*e:8?f\_aC]T\k$Due?SY8Sg,,4b&uEch:=bE'ddOT!sJHe7/j[O)F2WrHV7#f
%h?uJ8Z>:t^)U^CAfY#=:-/_d;7E5QL^=8(Fb:Li&JfU6r)p@u@%ric`BoKH[%?1cckCKf$A7E!l5f'$cV!nqeJtAkYU1d/.F[0D8
%eVl&.8-u^h:k*[IXI!MW+O)4S'1Bc@SM3NEc^8LI-K0h*`MmId-.-_K(IJarcll)!_?09,PWYQr<W`M_':iNn9J!KbL7CQ]6$B6$
%gKm/BPb*mDn"]>Mp*<OJfFL8D;FVZN[TKU9JRdZL#9pQchOj4h1Q(FO<W\2B=eI<UKbQ3"X0-e@QH$L/iWBXVU.b?UAk:qa$4goi
%bmA/;$l.i%/).Y14B$ViAJ[fF"MS8)bBoW/7lEsXN(2eGq:r$/@r$@_RT,$_b7\qdP[Z+CjBGoW*^oFWS4/Cg<,XgdjOn*Rbpi\u
%\M7C@>&O2@mo@u.*+,1!$rJ/=c&qpmQqc\-dH(X*!gF@-eV@W_=.:Rc-t?S!.8&4"M29aJ[0C&0Q_n?a0H3$O>$8r!iN(dY(p>;)
%#tYEf`D*Y4=SB%%b8#kfV24]df.F3,fn'c,VDVKg;bf$WFd"=M329G)dSsYhi[oUR\gA&O7mFHMIMHW\B8t\pM-6no6TH$(HlVTV
%fiF">6SUYQEL;c2+j/(u@b8HKb-F>Mq1rq7Z=cb_a'KID5?D-s?H8]G4IIh%p,\WC'LgRo<<1)&jhUDV^fH7gBdm<SeiR[)C6ZTB
%9ta6cG;/9).*lF`U'5*0it1sH=Mf1HQ6tc^FR!I'\el9Gj6G#p(loep7VG+6ln9#"0sInNS82`s-8,"TM`dMe9CLPdHE*_n]BJ4Q
%f!QdTH1BiB<is:fUE,q?]NpjUO-qNb0PPRM?"R$+9seFe@KRW_A.2e#+oA@kG1ER*R]R+<l9n/j+Vm09<7ONe5m7p#S_c[:YpcRn
%pm&gKL.jDe8<T[=M8<!#.#K,],t:Q4:%^s(2A0g'7.bUck>E73Fk8FO7($]K<ZOAM`1VaKL`t[keu@_<mo^C!f^19O'W?E@!fn1Y
%SZPm!X.hU(hPjtH`'?Eo:03u+QW^>'2(%k&l#MS0OHMc!T++(Tf5Z7;.\tOINHdB:C\Hf)=O:)*@jL/o@<sUs8U&>#__*Dn<39Tm
%oiOR_9D)8:=F$2i/A&\+A6bHg<hiQfW-8`rE3GK9$UjZgfT#"Popd5?AhcoBlQk2Po(ZY*b[oQJcGoJr70)M,*.F8L?`4Q,iSrD+
%gqoCMREaJ8\PeS32Je;S;)gMXP-\42V/aqgLGi3T<KTh!p=][3W)P%^2b\(I5=J?]SQ$XWB&O,b-WGkn!eU3`T#.G!R.=Lnb/@/Q
%"pt9,U6c3R9Tu,Ci&RYZl:Qk,8&<\2B<7hHm^DWq;=#$F>p4DbcH(qZR,trZRFWnH>c^I?OET0"S8Gs`o!s]gY.DD*]bq1j2WSIO
%E'.,O'\B5"815l1OqDW&h4beN<Q?l^<F19@reW3Ya1XZI0e-a8"(X9ok>N[R??W>;AA0O/\6q^9ahpW&)*`8jP6dJ1hj<B,b`348
%6"_B;QEeT!-V;9toaYiI@DCgGFR7>R.VLX+D=M/L*[W1,H"mt)LqImZS[43NQ.68$1J--_Een^7i+`g?L@8L0`bMf!W9W&T>udCO
%:-d$9l=:EPZ0.dDbio,V?0a6O,%"aGX?8Ks#bf5$N'!5=D/onp<I9fb,rL0t5J2](gL,>s\XP6Y-7Mo^cE&@;[.oL)J,qDgg\dH8
%7ZgJ@\jcq1&?F9_M7&8E.IObJ-;baW"A'E1B3C>K[#Qf=(V@?W1aQnr5)R$BfYf;:MlAg3o\H(d#pd,j9Gl.k<7(m&db]FUo>tB>
%kl5L96=<&L'J(ufSN>5N/$OdLI66_P&'YtH,XH9LXtQXkZ:0(,`#dM)P>*,mH?NV#9PIoh`_;\XfPSdkU*$+.JpF4TQ?=SdKDP/'
%d;3SV^O.\\R\\#%Lcd!kX_$+?(MFdOn(9$V/eb6,M2ILREiI;ic.6PsRuG4/ki/497MeQs),Xg9BAF6c]XNa=IY@`n]f5>\oJk8&
%^DlZli"lLh^f?s&de[L@m7(I0$S6M2/hSaZMFCZaF4%+!=]YS!a]P.UjJI7H<r!_5B6qQb7HMWMf>q'dTTt`dbs_6gP_k=hckdi:
%\Q17XKYmL@O]^fc<rab]@h?5QN^nOmR:KVs4t!Q'o+V`XiHqiL%?V>gAe=D>@,2hkPsbqs]em=iGG8a)L$7KZ@r&4[e`!QCUp"Ul
%Ki'*\)[]HlE$C%"+5OsBa(@(R_,uGjs28jEV9uEGU^WF5\%KK98gIl..iHU`Y<COBiOigQ9Ft,;k&u4_o:cC[!RW#-#BL=$jY\?q
%8kXgSk)4+;>G%0/![D!L?t/'mSo%3#"ECU0`UqW!89Js:K_M8KVpBH"OcjIl6;6)tV51,Hoj5tJQAn]C59EW6UD\G9lCm;U9Sjh>
%(R.*,Wp;JLQA05Z"4KnQH1-S:5,?rHhAO1h$0$>R%27cJ\X/s6*^T$_oKIl*/=))P/AtW@6P)0&`2RkVa;9gF'FkMua>1XP&\<1Z
%3t43Gf-b9?QfuR[MkVFC"u!tmZGaa2=XQReU_uSGGC4',[Sg'=Y;t]eeG#f%FeO]2QO.Yd4]ZQS&TMKK8n?u0/+hc\D+1t.&iD;#
%m_1f3pP(B2OZb..s'XCN&p+sYis]'9fdWEEXeE2aUM3Q.kllQJ4d^u+<Xo%5l>RL%@rm,.lgX=aDSq*I70gS:,Z@Dj&j\rS%G,*R
%8a1;@*%`Cp8Q8$cYX#/GkMZa4el$&b(hkt5ldFu7p6[*8"t$@1(H&CZM96Ng8</g6kTMsre<R:2&+FsWn'JtC0t^\m`Jc.gA2pl1
%i@JN.=6DYs<4b>U7:\el4]:AL_KL>t1at&!RGi\'V,_k5cM,uoK)>tcT`i_CjYrojEsFG`$YqbN&h5Ep.qrQB*J21o:m2&QZmn#[
%@NVlsE^tA;'TeZ$M<0b,Q8nP=`^#&?dW.OH78#!6J#jPR)_LbG$_2tFT^lW^Kd1/[]@.Y(E$kH\!WkZ_X'h8&BIO<)&.1sfCP#[S
%&A1Vm29*EQ;#+/B&5(@\gp3=Ri[n&Kn(>.BpkN!C.lOhBPaF>N[>8oK1ggI--p1u#S"&s0>Sp3<4DjQ/.Yr=_%NG:?^)U+">iP-B
%V5tR?4O-:pnb*]"Db!Gq*Hhjr@CN#Rqieq!]dIoE[OAkoY't./iY6!_kFc50Z4-WU(N2k^nf"`j%aQTQ3u#XW$J-_89I#,(\hJ+R
%OK]V[ms0?!P8NXb(J3S+db4U/]"PmNAV:PQ>_oZ?-f49:MD\,.R)jNnf6]OBYX/JPG@MhoaYc)D.#A[,1b/^<L#ll_4,IaV[tZS,
%VrpW^<77!a-bVqS\t[/"Z.D&]E>="C+XUhg(Wcgq4fBHiMg40$H=e69_sPE1:s^DBhP,qabH53:Lo5N;k4a>>1q!\3+L\oZ".8qF
%VD0?Q/h;1IM;BgF9WW8&%,!aX`%q<*NC/\Eb6`O+e^auki6Ks:5cF*D<b"$PDjQO3O,^<>WhARj66tlf&B4+R6oE_P=`jD#Sk]&[
%@j_LHR^u0QgqBYDdlF/K;*jB6<C:^DdndC.;q./O=\pV-0(S]]pa!COIDiODb(E'kFe3JVV0SRl/=o^56\H)GiUG!IPh:SZ&[iFH
%U'DcJ;Uj>!\&RdS1l6E0ZYj80UQlJtb9c?l"tK6hZ:DAl'I(]uA>2J'Al?_.U1(Wf4u`ZU9+Q'9S<db!UA#d"=:62fRr(,"bgpG:
%-R."ckY6krV_&V)Zlu0B^._LEPnsbh'n-YOeqpf%L`VkEm-P4'K)EnrTB^:;<5m"4Xs.Efk/_\oPb@[_R<u8=eAe.E%m#JeaAOu(
%d7YL_$R5GiUJ5%2Aupc&^TV=M7VN<R'B5N5qe'd!Z12Ec@9HdKe5U6l'_R?=.U2l`V9;pp]hj50oQBTRj'c`ZQV"s,bt?T$7gDg9
%',+u@4D*3pH1'72Y0KlE3^MD]BG;@`k[+OaL-(1TeT/t'*0b`oH<7Sje6$:V$<NXgm"I*7%Tit`U_G#Rj3UHS-O,M0"0M\(fND4_
%8)]&5hrj*tU(<E\7!)]p,'kN0BI"0%X!e7AiR/u5?0JSL7+]XQ<*[fl7,GUAL)C`#n_?$FbHT?dC^Lhf:_@\-TP5hah@ULcBrdD$
%&hNItc1M_!\)jLiW*7E6T,Bk9a`(BL*U\p>(f"q6>Lb%ViR6p1[od-NM27CTNZ_IK_V@]ta:M[roU,9^W5uhD<SY/6iC_<:EY.9t
%_0<T_0QPqe(p#LeB<e%MW#&FH.F3Vf9ZAUr(WWUM/PR/)oA0<Q-I&^40ncLVQ>WRRHUE%cXM4.2<GW,MeV>Zp+\@WKc3<<;[>@t9
%$Isr<R\#(AorD&+:jPH<fLh["s'd1Ho!RPComj8"-O`a7VhP_;oDHBTjod3R7q?`0nX_HUcV2`QK8I9m=Pl&"2:@,+^Y;>l21u3d
%%]VpabA&X)3R+9_]_U^Cj52oSc&.aq3sc'H4@%[7e8oWlg1l4lTl56ERN(lS-h)pYe13mS'-!>lks.Y'\'%F8=^<V.I=t]JRtum6
%V:e4ROAD1^n@:Q]1p>_EB51d#QR*`>aL,C]Lq.NdfsMtoH4LTd=F02*mgruD)'ODcGn%j<"=<Tt46-5]hc&>Z$'(]Pdo?7ROIHJa
%:UD_GFnLc8M-PfE9*?J"MqIQiN&Y<Sm-V$m-#^AU!uE)SiXaE-@+I$+WtCtrN<[EaeR8#G(W$e?*m@bJShS)eHO1K"U2'>m`L4SF
%K8k/9"%3>B(QGMB+;&Tb'nX,q;>SrJBrheG+=-T@PMW!`W&I,13I/L.kG:N%[a?E0VB_V+>ce5V>R#,o*aH*.@VX`V%9rS$lj/a>
%#"m)'`X']3\XrkF&rG1Z=]-Z(bUl8UeUgoq!fo#WW0o;@V:_%!Rmmp.JTe5l^i^1]PYht:gQA(Q.pfmT6<p[d)CR!j%-NSSFtALZ
%ET_e*s)mjqkXj=76jg9f)Be4?[uKCo159D'1L2P5EZ,K;fU(gT[*G?L?ST$M3?T=h-o.MjX>SIlgChV_!0!Sir\Pc&(,O$1d92W6
%=T"DR;PPD3@Fci73cY55iq?!ENee6g<[HKWH:n>6_VV]\H\&C:8/i[6,O^4uHF_#Z_5)tUiJt;"B]565I!dZicDi:ca[THd9*Rjg
%)lMmCB1,\!6(!MOV7h+1Q)I3+eAo"V!K#lt;R'aB8H_*mQj1+?,aa)G:(hfq[,95221h,4C[@&`n-K\b0=^PGjOjV_>K@OI/sa&M
%f>T2^(m_Wc<a^>pSp#c4*NP1E_b<apbQj&K"mn;a?$<C;DE[$i]6OAa/#<$U8j!Rm5qe.#o4fL]ha%"dDq10q/VCbQe>4L/p,+!=
%>)6nXVIXWN%]cZ#+OngSPrcH$[.OE+GFs_,9l+<RWh2u.&5bDX?TVqe%d##Nk1Jl9Y330>boh1f=`f6n.7AFTgu=MCqFn97]01s)
%V2?cK^A1'J=ch\KMcHLtBaVgi;=ll*]V27Xlgf-(%TT5,,"A`3Y2e'BYr(*d)R$f-&LT7)bG:t-3.$KT9A&"?b=h:!!g/VS8r1Q'
%R5H3qLc<E.6.Z]Zb?XRHH5.XZInnGDZie2@>5.F'FT0F)S#"u=C5KNN;7r:Q/`B]3:9$i5B;&"^-96;&ju'W!J6*q$0S]95@SXJk
%4s-lE'elO'8tFO==)0,pOd\d\K1?G<@gu#^R_OX'9VOA5**E.h;f\4T'=n>Y2W<V>_*?tmVTE:S9Rh_;d&_ks_10c$7p[I66-,=+
%ZB&O_2A$,_`d)/)W+[^:S7B?SR'Ddp>gbQ&_Ru6An[ctU9[5m'3Hp:FS8rc<p(/r3%!EdXL5s(eI<%Y27YN#e4[^m*\Rp+6<;8=a
%S>)0^d>NaG0lpc3H,nI\!Q6ZFEB3`&#SKn:+`q0._*_`NF,6b\`3pR$$t^YaA4352dGrm]!PgXZW8Ut:r8^HdEu,j!jU<sRkL=Cc
%4"*MjGf[>o!6=_:&]b#^4BH34e:T*6A@n,+YECI0*/jNP2"6Qm4_`8:;Z@,9W<cB.!A;pNQ=HY_5&.^]6_]U:L*qQ$c2i*sAG/s5
%K7p4%>C`PHDMpn32Il\"U`l*^WP)Op1^Y!DPCp3F+d+4uX-,62ftu5?3AZ7UL,ALfZQ_sH1"]K'mf[VjMj][ne#"@?BYaFE;<P"<
%8W5r6UTX?SMb?"F@,l&8!n*NnJa-,%i_1,sZ:1#aCCf,RS(!G7MH!S!kUT)P8X;V2*NL<E'ZX3:hNmFc)Jb.N;HMp6kh\A"%SYHI
%$\d*iIO4-BTsMnYkG*W+29V-C=/@CR"JCtR];abF=i<E!p)V^N<<A2ZLt[J;OGlKR5hhaplk5&/@G.K[:JD$`SH*jHMi5]cA.fc6
%C5?IjoVm@>-aaYq(I&Z?5@cpBl>b2/'YQ^^F]csiD3[h),P6$qP"TF[8PXohWlQ"MHe$4U>>S[T%f3siWKJ=JX,qnE[QMM&TLBW8
%P)LW^62/L^nYB'[&@7*+f2[iBmrGAM`Z9[qpHqP##Z3hf=:V<tLiWj^?'m\u[<1e=0PEn,MR_R_o?6Zdnj"toTd=0P\>;\!Ai*hN
%%"u#b%?<k3>)J!7bmrgf@k:#2J^GJ21TMll#gNiZ3,_9>VLd&6:['Rt9u8`c:ca3+WZhgER?K^OG,DWoT$01sk`P(t=i]h\P&GA3
%/#TSMct3nC?'q%u(Wgua!Gd]dCktNRfGoaX,GI6.;mq']TmPrZ?8efD0le"4>r,33M0+LQ(rcBI*>GJ)+WY>"?7a+oMT)SlYA/Dc
%fN%5\(.:ER!;4X^Y]tG#@ob]!%QTEicSb`mKD^FE`=0kVf?e"-Tc2_Kbf6Hu@QL$hN3(>dJH"IF`se)iEVq@(KmM+5s!"n>(T]%f
%X>*qbCWm15-<k8e#Co<AFI1Y"U7jCh7CRTZb'qlK<mBk`,64J+Z\B:#a[)&I$K<IiQos:8_b<+J&Kks_ZU;r%d_0N\XC1\*Ngg)I
%1.IInMT'O-#Its6+)4LA(B.W.'Ruen.loQ-VD=X&-JEFS>H-=W]c':<j+J4Z'kIhq"C>gKb74Vd,m[DY1`")&7\U=hVr6aaHcG[B
%oPp\!lQrY?K=;8g9!5f)5'?dp_b#kScN[+cikSi((\*gN%+%Ge7m)S14I0IP.8,ODJb+,^@'Ep7Lj2SaK[?Ct(;Z%q/as8g3rr&,
%R#-TS.W(;:<CO,rf5`^n%4?f0c<k630d`2#",r%%Vr52Wbb2ag6<DB"#0+F_@jB+BlafQ./qOR9Ke(%;9.iIt_.Cs^+gSdaOK3W;
%IBcoG40(0G*,-fJo0UZ+EB9(PRVe3c"+&WEHC/t(V0?%dO_U*aVpp$3!F`=UjJ#n!is^Zu&cn5IJXPg(Gfa*_+j;C_&i,`^.]Et&
%.qM@8%)G>/;AYfKZ$JBXBZq)T6UV'`j_6Dl!Z],ZZFS9<qkDod$28E5lV=grH1'/mAIq?Opg2m66AU#P\CDD^YmLfX3LW$sl$AkU
%h-8p86L.l\G5YjuX.^<8X3b8^0`.'s^1&V'_SV6'!"u';q3-CUdk4=,US\sb*lb<W<8[]cKLQ\;ZA;@Me+=Zl2;qD_`YY/cf]P_=
%"G9aWT<ajr3Pm%Enr%X@ptRhB[>V^sac6US8gg)r5dDis_,^+i>9!p2E@0mMJDm;7[OUh\/+>-UZsDTh22=:_"8\eXFS^qd;);QM
%Stl(o"u4-3A:Jpq;%YLW)Vlh\6qN8:cD)"b,INFc#Mg"23&RlkJKo\^%Vu%dNS$*/*!JgXjr1EF[;;M-CPOUo%X5.Sg-pjlZNI;+
%.AglR`8=?U%dHs/1hk]2=o?Z)+`_`%eKO0'nW(c2oZC[-,i2:7Z=6k3M+6,S\NM5p*tu5hMQl=Tl3t*V!R+j)d8eLA17ZHV/=6Zi
%*rY)Y#0M2M72"1I0QPYY+<THX:D+>,nM-qgP6@!D(j'G3`:n)/6SP9$RVk9o12G)\*%8aM/VKJn#*#H<%I.(c(R>6)'B]Y,7j=A:
%HAJf@13R:t%okCq+q*-depusr5,\!P8N;dTBVu5C?eku$e?TPu$(VNSX!&O-r+haZf`Y?/pTpU!B#E*Li*LP<!kSmS@i,>_Jai6-
%QVU.YC8o$:[k9TsNl]++3%Sp.l:Op8:NU=XBq261gT?Ut'8?oKYm7MJi!7?JoLlKI!"3F.M=l,8\hUoQq,TG4:6:hS],>Df(OE;V
%(qj+h_Aqd]'2NgMHu$?`:pk4AKOBmV3PSlNV[0%2lTaX]HB#S2Xs=3&>/TK%V"Z\@^*<8*"jp$90ibb4gd)M0W_#dhC[c7r\IB=K
%5l'VY=GUEieDKG#KAbT0U5&4Do.j7)^#h]edA!`JFU?MpWm4(:qC6st#sf7L&EMjiK[8?K3.=H*2\Mi*3J"X_F$D0J%]d3n42;_2
%J:2/PHINaan"LBV,ZrFC*\?67em+>uW!Af[JrKM9@\')Qm3%f87:t!M>[MWto;cEC^p'noeJ^u#@g$a[d,um)Yct-<-;AW.gDJH#
%JLB@moHnSWP^ls_3mdp.,'n/e$K>V@<g%Kp4Z=YPJoUShE,T3bWpLJIKWm0n5Bk1o-sa!$X<eS'b_?V(M%cM?Kl+e\!<8n#!)+Tq
%)1lFNc<,f:1iqpWr7;:&6*#NBd!-?>gst"HF1!&i3BP'<8TXsM-0(M@!lH>B3O';D/r/d'U9c"%_e'nOFUR=a=N$7C;1#S\hMgOP
%MqY3bes)KuEkArZ$kQ9'CtMdUQ)8O_GVlO4ojuQMY?9TZ,q:DjJZ9MqQ2hI#Eh4)j9DKuM>k#"gO<kB]/KtNU/.jIUS`p\XWZYIN
%Xm=$':mV_T*m>qs^eYr,1nhf-d-tbZ2&e8[>[2cpH73Bj1+Wrkpl37O5_W5`/>$osLf@*V_:Z]PR:W]>#HO!k*0/%p6"A<H):q<m
%Qd8AZ*gLMAAtR?08(G:9<#57I>r*'Q0;U(a4r(bI&e*;PSU%(u6u&mCNU\Kq&/U#/FOJ8f\2lR3n1h3UYVb'm->jbPUat(J.BWB;
%D?c%Fe+pOL1Dlj)kk0DaR*sO()1_qSWl"?YOuh"U9QJ<Ll5K;t.):9mKl3WF7MPBkn?ae9$>'#YTKGImTp;1kL'CjRq0-*HDo)n4
%!A?b'&>7gQ`7Pui"1@EN?8/s\7C/gIOX`DN9aO)u<80>6g7J7<o7.]f7^%8$\p",oN"4lKSG5Tl_9fDd2fM?!&_\%@2krfnE=YY8
%h3:NE(K7/']5GhBPjOmbd(ACtP8PhK(X^`u7e^#%Ht:L0#b0os#@Wfh<bbBk2%8iK"*9<pMWts<%^oCgR9'mCfeP3t!J)=ea;bYA
%:/Hj%fR`E&eA<sZXsYcV5tX0NYt,`:-8*+HFLWQK'<,7dN?NU_:,OoY"P"2fD`1UG:=V10#pH,PdM:Irr")g@>'XIW?V)aA?(b&7
%;Mbr([$IY"&G7LRTIeN[3K*)A:BiXaB)o?PUE)aGD9=l\PGol0O\2M;#n]%^]YK!2@-eTk4[F=Sp(/d[9#X*P@4t)Efc]sS&LNZn
%G#0LIPu9-+7t`b/]Q`tm&[GpU1RZ^9^_nG<((kP,RP']p6j0tm9/JSW;%TaAl\`W`4,T4X@o*f;a#UtK]6j":>Ae<j-lsSX9<-)\
%7+<&Kf%eMZmj(A0aNO.QEBZ\t+6s1o9O)T(Li04S;!81CWZhmOi>eDt`0Q(N:I9f)($.#/`dWYW/6C0ToS4\D==XWN6TCO"iJ=XM
%l]A3qCXQAH:omm;o";<=5n:FFGE&&ao+[lZBiTgP;p\6J%^%HPLs[82)fDh:PjUUuLkLdDB$mi$?%fbCIO1E`6P<sLcW6>A\rR'A
%2)2r,@Ts8np$#i1gT<jK5-VP(?!Lo)mf;_'m%(S+8$"%'79X$oB^@8nO$&jFT.Her6!th3GPEqDm)I[G!?MKR6kL$&-H74$CL^Zs
%-kb;nX2nn#a>t`4#&##bf*kf@VV<S*r!H'7V_5l8]9GO!_-V!](?+j9&\^(S<K-Ak7a2TI+n:[pf.IU#._"m`mj#I_IHkd%!dEMR
%Y6Mo.nSd[t9?"hd2`6gT5mqRU7>'E)LXL)S_hZ>\Y:GVS'/MmLCcCe@g_hn:W(R064EFgdG$@WA1N%m2Y^=JQQpD=#92Xj/&P?MX
%cQ.:7nB4uG6/S>eME9eZTY+S*)9(H"6oHKjgE(")(Mg(mC=N_f=cK1ES+STUl:uh9Xc-/Dr"a/)?pYt(IS_5I,)JIf2LtUf2fZ(n
%W+%UNY6<&WgQ6c<Et,LJ"HCXC,U//,dXpiX1cdr,Z5">tHgEm^1LQ)_c7fF.UdEC/1a$JL.99<TMBe<Jn/0Xp4Q_dcZL^oHP;p18
%Yo!h7c,^37U_!_8X9r8Ai2nTH<)ha:@tce$^=gLP5d'\SWpb#Z"F)^PqUN4q?Jkt"7B-X:TFsr:1HU1Y?m[KXr^'hM!0JOu5<U7r
%-Gnm`4&Pk8WdJ1SO)X_=":GerS#P/8&KZ]C<2=[0/%o=MaeC7b[/,tcS%YKthI#Xm0-GcBWpA#FD_hg''NT:6p'O_"8OJ<o8\fD=
%FZZR-!nM1^)J#$c22U4V@X8t2OAq$-JMBl_k!cU#B??kp"D&07-D5AN]PKbcY.>A38F[JQ8ALjj*XJimY(cIZVTP*]o"A4D%;cJj
%7o`#I=>oZ\:?s$`0#F-r-r[Sk1EhR@M$`'+_(q_I%&91jl/=M>>Ml1V94hRH/X8eP7PZdu5.G/c&?h7mA?MG3Dc-_2X6@VdYb%\3
%/+$UICh"4Y9o^P[/(]oK,o`#`)I[120r>AoS9U$LW0tu5,mdaV:B9"C_]A)aOS90<n.r]'1ml6$."%7I*oN][7>.&2)R$0!a.NN&
%,AN/liH;qe*Q<j.6GYhClC5<o7+YZip8J4J#O/bBl2k5K6!36)[_C%Y*jn]^K0ETs*+I\@DmcSY'%2$?`K4h4O)o+smV+ig*m"M+
%#@]q/eX+hJDTh1D32,%Y)1BAi;\r@BJit_bm^:BCo,/G]T'Kcm]Oo!SL3)Op7&B7t6M9?."IH5lU*dN*Qle"T=V!+<nhbGB((W<:
%YlcbWM+R4QJAFK$H70HRVKffer5]h/6.VLY$9;e#f^+Si4ok:'1i0@AW;*%OC*fp>VjGB*)rUDQZ8fdM.8i./N/=-Gei<*F_GRY<
%NB1H19c<2B]F#Cp]$V_%"gnL08-:OZJSE!_%PY]d!@r2'(4H]5Lt./%DlQ0.E_Q7G3THBLNP*B;(1+l0_BCgOAuO0!)WHu>jqMOq
%Gs"e)XBA>l1V[K6>GMh<3Z_#"Unq]_pJc)Yads?C@V]ClC);F2bh(*g+$4l?S-^?,KEe=Lr&QXeUa83q8*7T_f[=>Y"8Gi1:_Yr"
%D[Ht+06Efu.:=Q2c=Hd)OD&AH1.6[^-u+1hJK,N,4d0uC3N>Ts[;'q2&Inn4F0:m'1%-mF&IDl3#Xho!/@\U!_f3uV[c.:/78dh6
%]FC&]"PHhXIXC4q$14C[E(@4>:dg6KN_Z^<`LepR_8R\%#B-;%lKG%?EW.TgA2D"/INaZ$d1/#A@M!_jKC"srY8R5eZ5!e,OhRoP
%Atk(f1N^"_i<)sb&M4K1(0BK:"PkJ;K.qe"$tL;H./NSb'0R[!eu+g/8-(Pl;Q(<S2M;1<WSKUS#5X1)YGf7RHC#4$Prn(Br/'VX
%cI-D$`iU-hI=aP5O(h(>T)f!0i0Q/3D9F*oOH<XG9KBCq3(-D4q'LhO[n+d.WZ&@olM;8NEZH+4:S1cAP7F0l&YDGoB?[<78&T@r
%,rq1kqaR$bFk_nbZ:6f<f.p=$W^f%,q+&`!ptp?*&X#(g=S>0O+s`?g)]a?R-\BAi:b<fnqFtI>)(_8iMoVLN@8#U;2#7b5FcQ;`
%[j4umjmXJ>1h,#R@_.8OEsDcH_<Qkc$!/pX.SM$<Z17c]O(fkYHS`3YF>/]D)N76U7&t!?L@u/pJ!#&6M3uMRV%0Zs!\:A$Y,IL3
%JJDAV64qg507sG>1.Zr?@`&'n=;DG"T\OrI7e@"\&$[]JkA;6H*!e3nSR>ESTY6E=ZUJhH$&-T(k!R$S`4D[I[UbE#[E&i%TLc[t
%i]DgLN0c)=86N07^38'WCa"qT=X8Fe(j#m4(6M!4h2oD)O)8u0*`]5*aG,o\gL=2hkY^tl^Hb&bMp+&!EhI6'h(N[NSm/Y>`p*:Q
%>A@@lKEULK.''bFk<hC4f"m<8B>:g>SEN6-jVS?@E,NrP)maq0BSg]oZoC%5XrEX&'UFR&6NEGe&T6f@[98Gi[>?+qkR,+1Wu[IA
%,#0_UD5WMfg-[St3ru4-ckJ^'h^Ng[C:6gF]NS&\VcuKQT8gmq?slrbWcALLN#M<=elebQK3>R:'E]t^fVrVkI+Z+,'8SgUga#<D
%?&]45[moD<E<C>;V'Tl=kdeNiaQ>u9:/#W)-J_;)$BVsl'Y\?:iB%..Z,YnNiSgf<3U=2@JcnJ#nNuQ.O=o^U,[thS0+gFSh"`uU
%ihjFfaj4_Z:NII+kC`.dEuQ$X57DedD$/g5H@'>9UZK"Qkf73NYViAJp+\E<,37FY>-h5300.$b#78t%Kc/o26nHrq[h'YH4Nc&\
%ZAVh]YlkV3K&T8KX=BV%N,5<02'Z+dH298Zd?50rE'*;)R8E7Q4iUVql<(0#1T1<Ss"s'jCp-`b;Xg!/bX%5@K4#%&p?F6s2OX>A
%.46r<#+;?E8h^ZF0=LEOL)GQ^Y4d8fHG&dSH9*-sP<Uo-KH"!kcs3&C>:L:9K%Oi`BI4C-g'1?sQ5J5,.^pUUNAIoC"/dY]$'iqS
%e\12)RGLn7MU6Lh+t3:(kCO)"Xr`tUZthk!#B)8%2motQ0j^]afDlL@=J,^9KSZVf0j`fD)&5^,X@?C0,,AOFK>"J6HuoNg#-N&N
%C?`Qd5DTedDlH=Y%7.X#V"d[k@29De;J,0:TWF"jG8m4&!(_I)`>WG_oMY6c%c\i#=\aeT?<0BG1mP).]L;'$mFaBV21O#T28.Ze
%A&+=Yfg38L'$<+^.X)P'OaB"R)=frXBu0BVe2KaeZRE#r``?/f5mKBK1KQh>L]h$W%oo>uq\7EkEmll,N9YlBP0?+dS,Z;>0VQfE
%Kq:G`.#C@2)m#Q3ZFUE"=b,]A_CbmBTqA[N)OLZd0L4i+bb\98V;U3OnAR-G9sBp^5uDkNG)r_NgdLG$Jn*+L;Qcm+l.&U#M($nL
%R"&/$r95-N<0dq9U+63t^I>`"FX[N([60=<gN>6f;[4&J[L0*&`*@ga!mosoLC6'GN,>OWA@&+3Zfc>Q"p75+*lTc"=7m.dXRHtP
%>tdol4g=K)N3^p!DQO/b@9$d&XiLVJk[s%i1uJMP!&uU0*_6AGbW(*q!oGH-AAoK%>*8'\Fi=EZ)r2<0_=<89_ZjE4=Y[sk@)<Le
%!eL[!H^8ChJEiO+&t*XnEpXkj:4Q_@(O2VP`13:1l<Se^2Dn%:9u7A39M*F1+sn?1!`\[^c<#q2l[jJX6jKgaMUF,BDPjino[qiV
%9`^'6S@'odSq5`&EuDXm%=m<L/2%3,M_=G&@s\Fp^<ju*$9sFP(g>:UX%/^$#*iBLHBd!V(j<6^&D,M@8mWPcI?bQ?e>%+D&N0?6
%iGkr@%F]mM(9900&+:I2B5+^U8KqWS@JD^bD:N#j:Fi"e9-ROU4G_TYLh[tX3(lEKX9U]bXA4BOM-^3/f\I7(JlQ@g;ri`AnO69T
%IU0A`5YB;)kP*)+(`/T-h5^t_<L*k,Q)"^d1SD"1&W@E'9st=^X>i(&HAb20@E_3%o4,l:h;^(c!J`RBW_PsSd'$>cO&)"ujYOWH
%9WV@g4G,,\4Qd8l%LQu<NDYAac[meqY]u3lF3@cGJ`dor>2QH:-.?rhY.`p^>Y6Tp;q,9kGCW>oRl&``=HjS`nWLkm6k;8l#rT"B
%5GGX<(Uh-ogj.G6ZFVT(>jCXKj.mtPku/TF^'p?e5loTPpZ3"O,%!(!TG^tW$BrT0`TZS`R8D.5+NYK*0#_hCO?u_TGC'PGES[f"
%-oeW:'Ll]M+j1`Jp3'!:VLu!Q0i6/m0'U7$+Vl7*49A@l-H!cdjgc8+//C34(FE;dm7n+fq=#/)m);Cp$8`d'#,@jR,Uk"'X)k<%
%In)X]FUN/*[=X_VWuE?I?;F,0\)K6#E>^i;Pd@r.%=-5lm@$?Q;hFNX=11Oq@_dMrp)'J<i+#r4MI]$&JXb&R9.aBiDT)m#I\RW>
%.>/BJF^i<f:&&4m"D/$(k2@f*Fol3KN7BH+cN"Sq/E\HV25_<F9S,4'Hph>=FA%/P#Z/amN24B^''^&a=&`Wt'u4X*O5,BTOHl:l
%P?na*gMR0YFEqL0qGn_MT%UIPZ]grmi@W'$EQl_&SpE+Pd>TO%g7DRiZR#CO^+4MM9L0g2-_`$K`)Pk/3]^</]a)9K6drX_,uJ^V
%TU._AUPXaKq:f0Cd?NK+fn)LG?7_!EdPdEmO*NqfZbg-T,m)%8THO]]SCgo+a$PRmUeV?j]LKA(HQV2gG(!$$;V92%OTA4hS<nU3
%VX*Q2"cD"B6IjA)MlOuj!u@M8d2JA:kmL-.;kPc+/GDXJd2G+_7^mINjkVcR)N]uS"Wu<K$J'SM4Nr.D+4IlK=,8KFXG$!IK;pr"
%Z=L*:-eckHLcRk5pJ](o!W,Fm6H/Mh$!U^B2cfH):E*KCd\-EtZtF+n9$U'j4h"G%,[9=[=g8Wt[=^ot%.=@pR$nLU6Ye_hD:c04
%=I)3R+V*<o5JO^iCU&JZ8AuiXfA["fatJsh?>KFLnYp1,'d<D=R@0m71(]>"LaoG#eg54W!E3r)0$PU6ZTNkMX'$*4M9Eo3O:QWA
%.Q>;@du7;"W=;,p1gNHgiR07\(Jk\TUiG_1BFRlB,>"'gCLb^^*9s5RnjkW.,U[^Gc0eQu,u.+a4b(c00/MTtco$Xb%,;galGK=I
%PT6Tb/g`#n&Co].X!'c&3Ne:qXkr@.D<Z5B,9RlGAH^slo-n%N2bRYp:tsOu@9k2NAANgK,&<9".(o!T@>*.9ou4TM7Zbj>c]217
%#YBer\Dlh4e\T?P$ZI@LXfiLjgIO^iiUL,3UFW(\e58rh/R%h2BVdW/Wu(s4-Wp!geJ[f-c`FtZc%='Ira/))C')V:7qHb/!hr!u
%YQro0S95eA,1a!+eNEb5;%Y+%Q^@iT4M3XKai'Aca/FaKke;[_"fVk"6KfW<B^Sn:3JJj&A'd+(,$Va!%c,M+.9X/:"TkY+9^W!]
%:g+$C%c(d@O6"Y'0SLbMD[C3V3M[PAiX/)dB]ZoUMC2R>@=c`LU8DML=DLCEQhhC=X%3a81FC*KQ8U9J'b>*^=pN*.MReh0Y?N2;
%ea5s27QE5bOQ:3hDd8p;i`:&XPkGY^6b+5T(L^CgQ;]Fke;tBO+:(J&4+$%P$'3p3*,*R,,DI6S>GCAp]`9dHF<$0!4d@)2;A7V*
%Pmoi:LKUPC?56-S"">JY)25LZ@AHCu%?2+)^bXu?4<q\CUnPn)Q4bO-@Xr%!8F?gH2Xf%PQEEJ#aN:G<osGjAAts?47Y+YSQ6$r"
%)JCcp[Bp&H`B0eWS]1;kQ/kIl:T#DI0Imoi&/ArK%cF]b;8.b)4]6A2?D.@ZcOLR.>+LVt9^gJ`!t0g$N=q/V:1H+R"!r=/]1/bP
%4Tm?e5YTc"?@9-fV?OYJc3_6sI7\6,>#m,1`E9I_aBn(pO[C=gGsFT::2f.*@\gFQdl(W+Y_5Q-;FFO8s#s*GHB(srq^HfKZ`XuS
%>F5SU,N]Al9=U1CKbDO71489?r%)J[OBmd%$UI$A1_2NsaB::%d)2s/WeZ@c8INB<K2ng3>Y1Er?*&c9@I9ILkoj\Y*n.:-RAi>a
%bO-^uJ[=&Hd?*EL0b4%6B:-k'JDI@V>.eaLPZs)O\qQd]86[9Q@E;F`?ofnlO5*V>;5Q4q[)uN"1`NBq$apIX"i$jL>*EL&5m^BI
%XQ6qTKQqC[+q5FFE[GddVbLa<1-tjY\`tDok)e=8,*WelOu^K^11>u00D-bT85_:97LXBr8G3+%Ce;`ZOXLs:1AitqOZ\r9`4$a+
%]ir6U74b5CH3`g@.*XAKKt#g;<WH)d]3cL4r]+mW/V6.u;,D$;()<>L=<1Ao@*F.33[_53amlCSOK8_BZRReRUr4H+O<eEk&8MW(
%.;5)q:*M-%%h6o2[)FoIg<]l`YN/i&qegCeV)P@G%[YYWT<)Qb!'1grR^hX!N8C9,VHF&Xoqf1-LArt_5bh\@>P4[9Yok\:)k+r2
%+$#(En>;%Wn`,WX/Qs-do'AW*952okRsKGk24W)g2s,?q]OV]6c?4danaR'U7'*UjW[!7,43VNnjq7,eUec[XO2b@@Hp1^8eq79V
%j76,lMb%/bljp48P7Vt-/Vb5IDFXUM)a_q-,tXs_7"oO<`Z@OOk<)>9\+O^<9`9VYd@,+/P"t7_o9^f@$drc]/65G@-nV,;Vl-\*
%,q*G<3?pAs+<_aV>5/E'":;go2-C5#Z8A(PD\,gH`FX[Wk*B.SZBO*j:]uUuo4fFW+uWN0VR@0<5=&VpN^DK:\'Z7KQj&jp&?`&S
%%4)I35k/><0'f2J1XZ+@,&2!p?4`M+&/DM34Bi$taN^eZO'3*k6RLb)^q<KC4Y<6T7[B3*^oTT$1j4J(`D"EaB$%FHRCC>[?W-->
%2:;fWj/p>hKT0E!/]_fn(ekAYL;d?=pgY$GWJuKL*=dCT0?mP<XS6j(R`7e37N0#PMh0QCF+]a68o#`Ld=[%3Y]2R0%*eR%S^dEI
%!q_(-[_AssnJm1apld__.]<rH6$-fUPfY<2D6d_^/e0>=d"mkpJO,=l8-9Zc5i\*,(;Y1&%Yh4BKs"k*9[m^aL]cubE=+fl$M#p0
%NXJ_kIF5t&#e4fF(MVJ<h6!-jZ.l8\0U\p[B`Sib"\?-E]JlKT<F"<2[(pMC4#&H??:]/g:f^CI[90P!LG43%WZ((ITuf>j<ijRc
%Yb__H"9Mbs?lfIZSI8Q%qkhSjiY[7j#'qf*Mjok]Z_gCPP)V_(ZDbj1^?@DTKQ>J=5='gR$'gb/!q_tUNH!g2![a7F>T*$eeY3ng
%Nj\6bYRC:7fLCtNRbZ='d)7H&;#L\KDFB^5>$O/G?rRtm3pA=,c*5o;VNjuCfW<kH>6.H.6[@/bdc5e!Dkj(.i@LJ==OB+1[`J4.
%*5=g;o:1?'aSOS'IqeOk25@S!0:P\;`ifH4Y[EGOL1M-sb_Cp,"ko2O7gnh?pI??YF'>B-KMdM69W`'3C_B7G[Q0$gBh8T^:`>BE
%mtb>)!Sa]!:L;Nl&nk/$RfQI\CFFh:p(HdY:jZo2Zec,4E<:rK6@8u+eh\da!eR8B.M`i_L3+dhQae#E?'+df8kl#mXXbl-dMce-
%0VU#_F^(p!&<'_J3a\+HEAJQ==[a*pCf_Oq1;%pX`K,"B8.-jph+;\#K['ZU[Q!>AWeD.\U$4#g-NfTaA$$;<ALV2iPr)aN5S@_k
%GBk>Mig@l_Z-9MC>2ie/-6+R$J^85Y5Rn__Jih."A+i"@WQV9)JZjESpEJEb]/LJ:TEkp2pc8TPHi5#C3on4aXTUUrNa:@KBl)ZP
%U851bM.IS`0<hsuHtbA'>/6$%_=3?M9>ldU1,n71Fht5Gg<TWfH3_EULK*eEh\j1K(a#l;XR_->P;!V!geMf!DP+.MY\0'MTbZ6$
%O?*(R=!>\B(Nhq1[L#eiSpI=`>+U%kgi6tCF4_!#KrYT.S9=;E%'=br_?jC9Q#M7bG#[:-Frnfe)U4R?&aL:.e0^/1gl.VV_(BoT
%:?$>J*h^P=)cOg;+egYK"\DJjasllp[FIlY1gR_DC4@!.bmJWl<U\@H*1E>KVc`33kKC'UP6kYh$V[BUe@o-c^rmJ:3kuTDOChVP
%RbF[rVdSW+W!<@<5j@nGp[QgoB>;.V\t"#B09'<-'#:eXfqjI@dZ>VEiCV\"24Z7d8`*/^U,j?Z3P;^UVE2dKMRm!6;I2[XgQKU=
%OaDce8VkWd.X5Xh+U:8/?c*#GYpP9M'P:t?[mPF8bKq@@&Id:94Br4OYg?b]a.Q"K`&bb^Fans\^$k#9"er8*2(h0bRU$_qWC1<1
%S@ed8V5]#(d"N9l9?@me;cAgj:5Vubqg>Vddj+i+$+Eq<P0U4V+N%)jHWGp][MHF9XTFDJQ`M31&ga&H6.T]r>p^Ot9baBPA43&%
%G_3h+D)KN-E>GG*1T-q=Q6em]F^7.nhTR*KdQ+B6`+O)jkV9%*D9V4ojoK\C-,A&Rk`Ep(;3WkDgECKkMsnM'SkR6,J4)2(CTb`o
%^UH$KI`7d:g#p?$Un?cc)'COC">dll>ODf/Fp<c@M?lc`IDeoAam_)V^ZN^YL"gm:47\<?9Q$ob8a,#Q^lFM8WoeeT/)Zc0c/oIr
%BNtZrJ\"jK$2Tr'c;gotomhmDMt#fIE+4JHYXI<>iDsPb1E2CIfkFDr'!]!W4WL@#=%p(aUI<EaUj@K-0GA$1QL7iU'u_g'285q\
%NZF)gl#a8q\i6iSDM`N*<iUt&[O\FJJXC*ojHeNKJ=Dc<mF-C.>Yse71$I!l4X1&@&<h)M966Zii0O3pgd?h%%kKr&'QW]%.9moi
%\Up#"eF"c3E"9ogldtZ)-nmqGJ<2fh6i)CSa^`]q^!8sXciF'l%7@%ronF8B5bd"s&XLp*9l0lP*$$EGC?_%m<4*XLki9k.BhNaU
%)]Rl1U9Iu@?^]abDo;-%XcSc$4;475@Wul5W.=%U+E\WgP\*8)r<ETp+?:oK=J^kZq@[o.@<PU<@(5@L6#$IIS7:Vhld]c`ar;?9
%1INoV=Y_lB1q'bn<%>-P//OUuG'/X1TW3GPc5PWc@^?^4b*9JlCF-$"5C7?'DD@Bl`0q@dkT&MH@O*=2gP0V^9gJ5hYcMI?]S5S]
%,Vfel_2um(nX;._[UpXk=QE&Jc\MeDne!FDE<IBt&Ct*AM3$cR='0[b$I;*N>'D28-%o_m)R;KZb^8OnCSHe#1X/(uUV^U!Y`=O8
%RPd@@Ym0qmMl<GN=k#=g];_W;UQJX@f)!Zglrj(Lkn-5->(r%=!@&e='^M5QB2LRn7Z3ifpgjGf*Q=Sp6j-g.d9N`Y3\Sqigca,\
%/1>:iN&4)6BI<T^6OmUg=\QfaW.aSH(Y;K\*WV\7`1ZY0/<Jb(/2D*FKTf'rQqXmV^2<p\eY8pA`7_YJ9hW#'9Njf><PXdZ>S'P!
%et#`DSW:BRpb3^&o=Z;gP4l1c**tDq#oZu;^tYquZ_45@-jO'cAJ/E<;DMiBno:C2-KdP1++t!MU'EgLOA-%Gbos<$?(PG4WZL"d
%+sQs:X@A0&-dW<m+LVE&.InRY$5pRGQ858>iG(?qlL;7a-+fQi]gV*%KNJgC^$I,LWWVm1p@c'nbUJi?/?+Eai25Rt$r;E;"nQ5i
%@W]\jI`3]+fp'A-!CrtXL;_4D+b,&+6[Fqk`4._r$$g=nTfQnBIF?T'=X/3XZ+:KR$$JgKnCY<iQ?9!*j9lhV9a:Y2oq7CK1D20.
%`/\u%,#WZ[ng#F514\*kqNccL`f=?81T^^IPDpUmN>:f<+4cHD+K!NAJlnN;At?WjL8kuc3D;kC$Fhoih#\oTNL;ZM"hG/5g]9'4
%Ok3tPf,'EDiZQn(C+1l*2hf'K?`F)`O\LiB86!,E7<V8dDV7o>SI!A1bTJ)a^+&'@&1?sD[S.0DnV[Ob<?S^3AF[[$O4QqPA=d.@
%N4g)k$k]%Fj_6HM"=oJSNV+2NKC!KA#s`j7@*C2?^eA+_BP(p3ad48()o>W3rt(t6RW(Q91pW[AL!<H;WO^D_7%BBZ;!iE>VFXYG
%Glb?P[;+hEe]8X.X]Yl!N+T4nQ)L+^625GW/Cl)*6]#!(VCe)'Ln5=u[bW]%9Hh&g0aQf0K*rFmTTtgU\mge8lG;PK>"k8LkduBu
%H<?W0$54as`>["D=@+qS2PLO=.gdY1%5*'@/ifp`^-Gdq9ag5J+_F32$FJk<6e'DI$aQe=9Fj&aP2B%D.mW:Te\eLDBXMc%%sm/b
%3fu(0E<&Z"%H"Lqa>1C<IFS`78/&3q*VE@AZ.9>#U,cZkE%R>$Usp8??(2\cm)bI_Q5o%AdTCA29-h+1f5nc"R0GdR9l`XU(k`(@
%g!2,9Z'0-;cHq@%Js0+:Zr^g;7HeV^TGG^MMHnS+H]4b,+@e=Umue`>a'_sWemuRb<D#Xc&NG)RMOW9!HCF=ldjm7:+uQ]9p0$@Z
%9l,;Wo&.me1ZsM?eZ5FI)%(Pl.SY>k@^Ah6_?afc(9^C+&0t(Sn?dDdDG>e"<m@dAABfIIYk^k\K2j@7ro5<gdu8ft9&@]+oi],O
%0A8U^%eZ$1X$PEiY:6kp;S#ZJ3:(@F[\WEAMMEoV$QLMN_.5A7n[V;SL3bMC(Hl*3k0V9;(kI*Q/Vc'(Va;*Enco:/!!cFVFd#."
%>rK`n()M'..Rh/[>-FEQLmXBAXYr$ek]DB)Pf<uEorr@R9+;fSji*os-G^UULbXjd=:N!5A\jX=g(_?(!%KjL_>HP.NI_Jq/;TC>
%EuW\X,4T^oY"?,1`6!#/'cuiUZu/K>HJiF0H_:XE&'IH8BUk6p2#2l.i0h1&aTSr*!4gr%NO2r8Zg=>2gNP=M#O=ZkQ-nb+>+7(J
%eH=W`M&eo.X75k0E0?tK6oA64)*$[ilC;Lr^b1_(fbk[*0;Aa/'Xigr,mprt(K^o.j2`76_O28"XOIZ&-@3"H)EnVX[1"?UY0IqS
%)12XC<?]?j4jE#kk=%7;1Z9d/5ap#@3)To8KYJY\MJA]MD0PgV37b[80sa=gg,'d`apU<)2:TrV]f_WY>a<eU[k'W3;SlUP8Pra`
%X?sQ>i,C2?3CuY2rn8p$_R570dUDd,,Cue,=NVoY4X##Z3>')4MEl%jXqA;U[i0n7SY%;KbbUDIMQFZ1*6eKk"S6d-qlP"+gas%M
%7=gZb&Tm0@eLC)fS18=PL@p(ajs;(h/V-]rDsX"5TK%4^XbUo;Q,Bi`hpP#pL"S.6RaF,07[9['0;8q\7j?Ld0Al/!&g*EC0V%u[
%C%R@Qj+tunU1&XCKQd3Dhp9o0S'GKF(1$3<6(EXr%(S@O7D1o@M+7=@raWNN$"9SR@#0;Z#s,lIg'WLu)FQG7:!+k-@d7FEd'Pj.
%-W<gj>=9SUY!QCC<4Da70ZuS$@N#sf?8fB20jP2WNIaK__7DZF#R$tk3*[$]G^`ELQ3@j!h?T+.TdZ;A5S##iTHjN(_p9UHC5/'1
%Yu`:,j4MpakQ)!8_9?L;R8]I]0**Tb"0Q4]#(f[75e\R'$E=;)h.E>,!8P+]k3FVZO7j-984d=?U/rWocqL\_n]j1s]9Op9NjBdD
%]7@j7Kq$_(".1QqmDA)9/BkE=nK\"emSpM<""Mp^4-IYoK];;:crd>922;kt^&qA?PIuH&^*?aj_H6j0]"j:EC?_94R.SUIFq-Ld
%%`+-=<i/bC@:;p4k6!LsC5GEsGI'%b5oKX7NbpFQ0GrLf!DJiQ]*`j4%ab>X?&TX5\!#_\nAhBZ:?*$SSYp$a)r_(^$?h("[N9%K
%`<XD+]F6l[4.2e[Z_,-1&Ok"HI)qE/<X,X0m"^KI/^?5^m%`8-[s+s\&=*An(AJHB_J9e7o$nspS&^T<904*tXkE71-IF*d/8%M)
%+Y3c&8D`fI/kc9Y(a%(9$Y(50<-4NWjoP.H=R\A#Un@GhY-=Z#`7OWET6QlTVk8+&Os!A;\s?-5SWG8'!6p8lcjcHj=B=VfrN%@P
%b_$APFe57?f4X&_`kHO[J9<i0KAhoRC58a\3uuYjes<luCfA%N]`t!1#0A_9?"m>-BCSkr,VO+bfrT;n8H3bBams_O^oCb1;:Q:5
%OL@s+#TCT=.k;4-]a%<[Jg9i*&'5qHXA\BeA!ec6<WRl-0i6#hd2OnfPXfI=Jt8V7GD#;1PR-kt+A'o\X+bl%d%X0Rn9<Y*_NrRm
%.Jq][S7OtdJ,[/(@esu4)8MI39fT4&XhfbL0g)H:eBu!nLi<[3@Ou0:n!c1e\fh&'MrR,jLj/)$(>E^FA"><Fh:Y7qa2sD]/>kmg
%:'J?4^k@ZH1$sgtcHLPIYoo,b_RB+Xp=9RV'\Fu73TubX3?)'ii9LGf68NfS^]7Z(b`@WE8LglC)S<>kCJ9P=GZ2RqEZJE%ap%l$
%+P*"O(Y%LeU$OJ\.P[fs@V:65B#U4<XV6$ifDS\hJRW(\=JNtD^d2EV\CR1D+XF1i'eEtirZpY(mWEsb3frJ+ZMV.,Ugg;VjgPV4
%BSI8.MV9s?heg[*,.a3SP7N^LcgE*Rh'c@*`eA2=,^2":RQ_.46Lrq8anu#V0Dc-0G$(q3JJNLsY>r8N_"]$]J-?[1T`^W#IYH\B
%UtsbhmH26YgrYB0h\j5oZ8^#Qbb%Un[Ps.+,3D:G+.3..L'M0J'cd*hEEL`#=t#erDS5#FfaS-mEdIee+]spLP8_rp&0.g2Po7U^
%Ea.#:drZM;XAW8-E#N=/1I*EeO^Jp(,K`>90SXkS$3-bD5AN`/<Dfi.Q)q,$JZ3<#jXpQ6`t.ob]PNQCC8R1aG&uCV=+F+FX:?p^
%n^B$X>i7oh;0;#'F!q>U;Fo^GLa\c>3J^a)#`2tj".D08ZTbLkBgOWdb0o(;_n(5>qR6(G.Z6]=`jC*o;uf5s(gR@!n:"E9TuPbN
%J;i`,G%EFbkQbbp,n*e0^@;hNH>+/!!f.C?#Hu'B9Qs5"0lUoc(e<UD1tJt_=cHi#%Ugtq?RF&:MD5gtHr];i7XJbrRB]^nTNm2p
%'pAkRM0$I,)t3J-#D3_3e9C!MGA*GYJ^cM!\!7gCI"0(M"rQjmK,=bD5mg)\YgaNLp(A*!k_t72o[pu>%A6&4a':4&q4e20pM(Sg
%HuQXDjnh5%T(e-9Q<9JRis`8-ot?s[8%mk1G_t(`"?jYiXbj$;Og"HIBF-s_Q'tT*MnH@qa[4uI;<'tBdt8m4K54#CVcm=+Aer[6
%<0sm[doiE>N\JGkoD_'?[f;T=';U0RqDq5@&D#Y/),A;QGg"`42/s>[\JN2UGl1,,oAj'EoW2%.CiTdna9WY-U,R2$Y$`/JEh&?0
%]=bH"$,C_#VH`Qq.j1iK`aDA"bm&mD6h"*&mKW.S^lWCk+`!Yu=%A2,=FieYE!P;]#^Ij*%_r;n3`P=G<qD:7P"qJj,"7<TU%57%
%7^Qhdi[3Ti&'6;o7WI3*K9"Y#d@>X?#rMK0.76L&"A>moC7l.^=e4@ojk,9c$1B,G8=^nJC@3J`/]g$R4!789OH>QJQ8BjcM!CDh
%<NtOGU'HNiLa\_P6aQuYW*n%A;tj7KMp[UL`QJ2fcl:A/(!13]'/:2?)\sEq"*L3?jDBmA.nnWbM%t5cmGuiN2jmg.>>,GP1`#_@
%qD2PGoAd8_(,%3uGXD/_ajh.3olYieZHsk%=bYP>3a0oWR--"g,\!TX%\JjLQj/*jZlTp-L`_2(Z``ja$KD(%ia`cTE8(O]M.]tJ
%A%Q][F9UO<8Zl>8;pct\W3Tu:03koE>9G#dZ,>KVnnI<%b?L_QgdFi5!mKhN-iiE(%oTFqf]WrFO9.afLtM)\baN<A7'.:=QC9$^
%1b5o6Lm^m-V(C@3+pQ8r#LYt5(8r@uN!q:-^^$\9LjgQ5)_eH]N!h`<<_/)27F,@0#0#53aaHla7pCdS<D9RdKU@YElFA3.]SUX5
%cpEJCH@o3t5["ZH)s[2I&eoI"?t%Z=,>?<^R*R"qP(ojT$q\"4&Co<)1K0W\oVQ4g[^BcRP_/rt^F#ok;:-<16qE0k(g:+KEel>S
%bbPlAmZ3cJLf8%LDrc"=e1&f[kie>,K*s_=FNCuSEd8hSSpa6[C`VS1p8+p7&\r]:.eFX=36h?K.=iF-oP?ml',^8(!*NnFZe7LL
%jrU)e@!+c[7`EtjmZQ*f+aT"oZKJ"RT*e7WY*>l$XY.q[3Tu<7F]5OeTcb=_<YOPFeYk@*Hrr?2W`PDHPPsQ*ai[38%M8Le-p1cM
%.din8?m=M,Qh&T"./%!WC])Hmpm<oaO,2=]d)<nf`pbH$.Y`9Zldm2)XM4o45Rq]D=I%(uj=7/HlAN]UmmP*R$jJb5^Z2jK?=XFP
%+qF=InN&95R79D=@tq6h5*i2]Mh8m#"[SZ)5Rliu5UaZBQ_99Obb6*[Ep&X\+Y0h5nn3]l#DPA&_uS#!H?E,XPCZ+D/<VUg4-RL4
%FWM8!^1/o"\X:6,gd8/uWIrR5cher6[>\8!)EF?;o#V*lji%G&o9;<N#Ys(PnK\"6iq()=jWl(7T7J4"">EV?9SE0h<*<TcB)FbB
%_D;`1!t$?Y5[8g^TVX1\(r1GDX$ONWS"4%fO$I#s2:2dG(k*ck+6ghC+01pTUnJqkj$[esVON=h%H>_FPgqVtM^Dmhl+`MZSkOK5
%K%5spXj27Vki'f(enaaH%&F\--"[uKphA7P1'/\W?K[+L(\Z'$5;?jT0qnG=)ro<'>JXo-.cOd00G^EbXkXuV\0n$#F<XoVi%h=I
%Z-$SN2.f5.R96;*(4(uf.*$.T6ZGP-Me).POHlnR&(+<WH&_9uO`Q/$Xa/,YKWlISfnO2Iar;mV7.72\laiL,->B\m'#T5f;T0-%
%2k%.GpVca%i+BIJG\RXc!EX0s%`hf;&I4sO]WkV$Y!UEL#>ORXC";HW'??0Hc4[BL`3gA)CWhj*^cEBPl#nHn;H8mW^`?YVK#R3l
%XMbK"pe8%]$JQJWkhra/<^W>>F/rM\k[>H6L^B6(,aSZ%=\E*E1@"-N$d=QAa@ODsH69e23aN?m73.M4g,-\q--=\`n1q/o-f,R1
%;N6SI4ej2_a#P+Y]qX:E'G;Ss9JC\M&UnbukP<MAW^Btf92^`i#@mnf95RLq0Lhp$Ibu8*@UM2Xr)":@'HW,U=p23b)?p?"5;5@_
%].>/dW,XN@-0W$m0.@DVg5PmUR+7G>[%Am'`P1RgL.A;6M3S3@/:]IOJu72[kp6lEUhN4+bME"P'mUG?IRUW_.BC^*I;`kAem=O:
%jap@\qBb@09;Me-K6TfuCr7RCj2$2q:Yb3W5(RKpHh0BcU/KDpG>&BhV6i9,&YmI\Mr+*afQA_Gf"@fdN\3UfR`Vq<Omlmn\>G-I
%>BY*;)-m$g,9<uZf1[;fAkce[3lH8UV99qJ8a'!r(=oVH`mT]qPi)N!X(<i+f%e9.<IKfk*F`K1dLSd1^6nD;007'^Z[$5J^P)$/
%jCKc2F9u7#CMAKM99Q(pB?n;`^!WlHk,+tqlM^/6LV$Q=9WJ^@h%CBbV4\V<@1riLN,J1UYmoO]C/d_Jc6uJ+kZ1kP+VGk:_A'4f
%/#9%:Q#NBhcr'V3^l:uolrn#+)!W;J*]/VdXOpoKn(Q@e#.m1,g.S8d1F?d`Q+UL'0jSe*$>KZ`&"8tme0@C=`Bc+74,d3icB=gH
%!1?m3RRR`,8foe&Yn!WE':M&r28hYQb/8F?O_STU11OLT"JS'4/&8H_+O;bZFEk6Q#I5,aUmk<$E5selc`P',XTM7k9$WIb\fRDW
%\G>l[_E9-i4Io2'+$7A/,(5rsj,plB9D0bR&6>n^Okj2^M=nAZ^,B(M8ITKXhsZP4%PCs-Pl73i*C29T1EW6F%4,Up\;4;3,>%GQ
%r$%YH.WEsb[)\h0PiU?@?64co8Q'##0]MJ^/VDV/&8LOmq`]f##p.dG9[knSR8n@0dp).e!TWJWElmAHYU1KH1D/kH5_=E')G2Z]
%N=)1=->%X#hhZVHQqp+,%o3nLYtLj]<EGs#paK)CGr1F:]`8*4'eW;\$9e"]3SY[D\9PucDquLf%?FuV<?+Z+c75c0>X[+)D45:$
%5X'j5FL'0-SkY(6I%]Nqc%<T"MY8.g-,+(9Ug=\JPU=6"**M!T=HT&)jhB>K5r]#[>A7iq\j(eqF`=he[_ZMa2$h!4ihs[h'==66
%iH,.>Q]A4H)-S2)kj'tEi3u##4,j`9)l*h?N^=sPAl@'0b;!W#+$]a%5PjWjN^!?Dn,3&1^\IQsq6-u.s74FH[pT*mjQ>Ukc';1V
%H1pFmX5!9=rl<R>alV4A,:4KAqGD8tqlr6Z"XV*7p<AYJoT&q?7umW<ZZ#*Crn$r2^Ba.qUNH._U]9q8h9kQYMpMA^-JD^<@au<F
%B`JaIpeZX$e0^UUj'2/lH)p!,+5cAH1&A#$Y%UqP+9+-'Zd?^UlF<GTGWdISW=YLAq;&sH_\?!K#ss^2<'@)BclZdFqWrl5R!9OW
%OSDLh7YB0)H#/m<MN[Kekq?d''LahGi?4</8YA46\=tMt:D9eOOQbWPQ75hC6tsk:B7nj,(^UC$8qQ/_g)[#HV(1A3#=%t0WAmQ[
%8nZ0j&]_."r2`2[WKR!e&@M!N'02ud<-k58,HpahLtN2G(DAkD,Pg4$Ol@!ECh0:%13$f!-t56h8_+r]e'\unK1VYL3&,DDXrb?U
%\!!\",V^Pb/>n:6+otZlS&EcjQ1puN'2a6Dkk#-9?"sg'O]++A0oOtc`#PK_1\jgAdDOnp1gN^)+u)k4a/28(&C&/0o(ZEdZC\0'
%NoaZ[4JGO9Ub"6_$iA#p`h2$ao9lt@.P`a-92#dX:_"of\b(K^FQe;%K(ZpN*N&_`n2ZNr)+4IT0l^lR?&4'[#d&lef1L8YF)6OL
%6moMsZ=i%9?Dq+DVoa9'K+jc$+cZf<.l$<M1oZ3^7'cPk%jga+/Pb2C5!"P;Tdb;V2#"*7;%H7d10WpnnTIi.H?KaQ\+#V<-,h2&
%0I0H$6qqHoF#;+6;dN^p*k[jfQme4eR2Dn1+qlI5m5ftUc=BfKd!g6\\?nRg&#m^R,$-qbi2l-9g-_!\_s7kTQHq:q@Zq669q-uR
%-rG<7.!eR6W0/2K#FH5Um^<fN=NM0b-Y!uDdc:Tmi^;FPHG,[uR!St>-([[Tp(jLZ+Q5+MjJ^I-=GK4_cqFu6+UFm-IP(hH90*@i
%N"lTbJS`4ZA^+QdZ0:ReLOu[S'rI)r7bPesV-KAt%S$\5HI0YT2=.1N;2OXdZP1;)"ob$fqD"+LCpH%&HgSY3I`%,&[2L'<Q\n=-
%QeVrqjpY_"X^bb]pipfG4L*DU>V\"/$5;t,2S$1!hD6lb"G6$;UK<QAj/cg.HkSur--&c@LqY<,T@J32jeXA2.P_C:oqjm.fZ5Gd
%2-6#E'lALC?&BA66B=1d&FH=P&rB"+P*.88#R*:]FH8Ej7\6tA%L<Z1&T)5;4n^Xd5e6em)b[-i+;2=0b8j"\oJ3N9ppue=`H/GB
%.(<0!LuW3;aH/;He-CZ>N\00$,=e0q5jjQ-o\0)jB4@#Y<7/@4f]42'U5s<fOCRrM:W7Hn*.6*S3(&jbT%lP&j>)OhfEjI:o&7jc
%RaAJTG#h=`bbGd';o%1BI=QX<],8lh[?8<Kgb"/6$@g4h$jJ?s,5S;rE-h(:#O0nJCnqQ.`\,3(UbN%c2(dHp+rEhRD[k<^Opu#i
%/q6QB(ej#-;"?Y/]9OA]K\&?;N5f7.j964mMo5A'p0`$Aj?2\=:SBP\M"p1%'f,tl;N;#/IrS-,<OH!!c8&Z4FY^5!Y)Fd0jM9.K
%ZAoXFU=mXTVgLLe8P$8?_cNk`6H\<W&K1/#p:u2>YAg)cqBUHL?+s"6V&$]XFt59(/5\ZU.l&8p.SDG)!(6I^[fnRFPfWEt,-d0^
%6^%>foKq5>6Gto!fm6eB`j"V^c-M1$D740B=`:bAjG.5S+S]@6QNSM;mSiTk`kRTcjFtnnYnt#R*ZSq-"3S3CP?ADI(!=e>Z58@j
%QUcUJ2[5ChA-F\&lTqkRKW^L]'5b+b_YqU%S&'oZU&Mb4L5bU1Ve;GbLq6>`.\AE"Nb)$N1^&Kb6VDHRWo?`,Yn)QR%^?D?M%Tnc
%AFs>iP\gf6AF+rlqIUNY7q*5s>NKI\,9sl@/:!=^V+M<lOE`GM]IK&RW=./&MKa(#?Acm$30B><]N"hL#>#KS/b^UmThTE_P>gHe
%H9e?43bnnuLpKWBRsE=.I?'gs1(+03o)'9d.$]pN828'!"T;AD?u10&3N:c6:%iLn>t(/ak-F?Y+2hPsi$s1,,$,,j8^P`a):b+J
%<n/mGfgAZV;pmVE^rTf52h;'7D&2.]GT!a8R2?SE'&GVIb*l&HZY*QoFA>t;m@d0R"+Ps(NlgIsYZ+*8PD>B'9F_@2@2Y\3)SOIh
%F8B3?BEG:Xs(nnl5#KFY>E/YlB<dJJ\I'8fU+I9d>mapSo&0^sYK.n-A@,>d7n=0/1IG+rTg]n&1GN3-Y2-/)?k^_6#3b??$rA)Q
%$bm$Q-ThJkI^M`1g^?u#9]'J#6,u7IIBaW&hTH9=kc3*kj$<&L@OFu,SWcOIRl$kVD]Tk37)'pS..CeVPE^#7P$1i"dR0a=+IRl.
%$(%'DkH3/7.LH]k5WL*s:8'q^l$Z^VEP1p360ojsZt:?2i.+.92VP[`%_7DQ8?R'!h.3m@k@Ns&(,Z^rA.2&B3RU:<P3=t+8hs#L
%aO.8#7:>LcUfe%Af/m*2Su'-4>UZ0-;QWpTrYW<[]=0_[1GNEr/_JnG@Nd!95'VM*prY<Dg3MdaA13V7nl$\rQ_K4.+"Poqc&sh+
%:F=UNl(Vn!!3)nXWEh'5d(KSPaLf53I</-kAZ/mmeC<WZi(gB`:SU,.K:ZV_<Tt^@H4VTo,]O5"1Ep9E@#cu03htMdmct#S65U/W
%/uc+M!qX*:[ic8-$ln?q5\U6?_`4qrkc@nod8uX)M(;"8=I(.g6;09A)B199]IY&ma(&LN;a.mH4>Rb.`TSMD1dtbn*@U5/ae`s>
%\P#='SG9OUZ^T-@UK\(u.nNJb98>8,nig&;OGPN=e26G*Be4CcV4s]dr-[$G>./mm$c4EfFl47i$lOhANeOm;MO/r\O\pLsf.Dg/
%OoQl1B]NDLQZZ.0j;57f3C/X/G"H&k/+"XZK.!2qZ:VQ+.:\UJ79PR%beO;oS'4RHOQ8PAdgThiaI6^'Ldck:q4)!$-a<iBS[lo&
%UB%Cd_TARq!T;an2<>Wj6I'*T)FOqDo?_+SSZN9I+A3G=:>o+S^*I2g-&2QA8>"j^c=o66H)`8Jm`hC@J_nij-UF5YJmZPB!*;nU
%K?*u<=6P4G4+GPTR>]qD(nqF4')&kkEB\YR]NB5^iKY?kEYNh#Fdg<HH!Pfi4j^u"#"C<VVg)o3aXf1Te]O&tX[4;qDo@8'`oX=_
%1-;f:7cCE_WC0R]K,E]qeQGk;j?#qY%=D%:;<\4D"JS9DW6:[OBOF67Ki69S]\tiI6;3@\'<3:\'XmroV=h3c,`]e%HgT>0^l?BC
%A"m&@NEbNn6r'kj_nbGhku+RHKoFl6,Fg\S&kO-d/ss`JFmVKf=/Nm45iG)TLg@/E#s)TkACf#cefI@%$X)_"pYErf@j6"7DX"PT
%(E5.bS:Mc,f2uIG9+_Q(ImU:H@`F?qI/H[F]H3r2X4iiAU)uICWqO4d.O")Zk)/n3BI[J+Q6+Ot7PUaeU"^J/ea`eq`+TsNZ=d?<
%qV[3-V1*%6o%l7_9qdIS7JJmB&#KuEpR%#0`"\&/k.5Tc"<sgPr,<69DD>WP.90h]+o#C:aj:k3Rr#[&RZK;Y>hG?1JB-Ic23IDk
%\Zlf<Lb'$?*Omh9&J\iI\TpkI,oXt`KV8WjC$G18IaEBPYDfPn'</DF3Et2>),+->TNNPjIPSSs?&5B)A@NJdYXp9b%)K2&79I<-
%5=_.KX4UCTUoA*"L/.PFQ<gaORD^LJaM_iPTdNbi3E078jMVl2^9Ut@LPaj).p94%.Q?U%_0I\M0r4YZ8KKQ]p->2Q!G>t"\Tk:G
%UQ&Ok=,Z6lh58,f<oO;s3Y<0<C6qmsq4&*QSFd$U"Z2I(B*F;=(n&G^ok$D2U+-aEO]p-7X(>Rm^'g,51jbOL#iZ+FMjSqRn?=TO
%?mFZTOGV>sK?9/W?PTKl-U@9:-poSP/s.)V&S,)\Q:2L^\cAn#.5M:f*V:\UQks5=&L1]8QmEo];TcVn#,52_6(N<b)6ZLBVAu1l
%?s?<68F0LMHYk&rD0jVKb^f&"eK24+Snq42/3d"6*,c?'<?*X^?kod^+eG6,oM!-%jpTZN^;\cO-us-l%Yht<RB$trKb\&1>;I#S
%a?Ij'E)nm?G$sZIV'RW+'B'<Dqa'[C1n[76Ha/2:h781[`%KY4V\d+Dn'J]RZ/-G/pK_NB1HY0m%3Vpp2s#(W-^hE`J4-HKU(MpM
%bcV]I`L'ho0aNRq'>5g(FE46'Q1PBfUf/!/)"Djn?AKF5(T(UJWuZ@<e]Za*4@=F!7J/$]3;=RWgmX5(_Z?+@2^r@XFMX2R,$0tj
%M[=#9VlFjh6)3].jQ7+QVs,G#6l36cLGTAnM3igMcMZ)NJ]V1t`h^HR]+egkanVIKDYbqDm?!nkF3=>]<>$KCBeiQ.@BaC$_,H`B
%@\S.<L+oZ%P2Va\aL0<]kg^<f\s`_jD6(k&'l>Jo5M5YL.&a5sG3(>?8u6DKH4(0)[q+G:ch%FFY?k84/ZB6INo1+r8@1sMX#r5c
%Q)Qj*'Z7`YN0)O5r]loTdHk;Ah^(sXQ2Hf5O\eFO9<Hb*gI@8F<K$rQP!Jg:Q#_6[l\kC^cgIMoih_GEc,4t?"KI!nEu6k/AZb<h
%>oM$s)hqR-LXSjH@RS]e-9Z(*]=$B2s7/FZbUGlE*XtW^7/d%-f2mHHF+/7QIeDk?09jaj9'a7ipd`*rRfCVoq8Z;2oA>"<IPJG1
%nW3[gr]^21nR%pep@rj`YL!"4?_*hGS6oWo]_V7as7FF\:]GT`EAdac`>IO&o.StVSA"mSdEb>#?bCZ*Ejs<X=6CC'hjdtlMfA;A
%s*f)Hs,m<Dk1d)cp`G!3rPa8YS_j;8qVB]f0oo[Js6XGhs75FL_UY8ss5L40([puD?h.b,j6HR)m3@T>@#Nd"1#6UCo,Z*H(]XL1
%0.t!<lK!$drooughCpHp[&_<YT=fXdhenh!\,/=Ba%kVTKtm"m$JoWf`)gU7J,\_F_bAEr^Sg\Jq<ONR2U"YFrI!@UpPgPTpFjJ8
%]?B*(hOON:C^UVU)t.NkDr/2'ir.>PF8#=``KoPAL-&lf6]QQoFM9_[HhNpfBE.GVg"#=&Nd.K#XsS@8rTVT&c08^SD40<dDYL(#
%MbWC(<pO$4`-3#-J^<1;4o*WTk@L4a,GXti2<sqZ6t_!qhn56eJ+q[c0n20tHAjf>&<EMAIL>)IE5PHV_^5M\)i9bZ7:
%Hla<d15Y>#ceb2I>eerGQ.^9"pZ9rujk&+&cYp6tQ]Es1ekP!cJ:ICFfp>4$*)sJ^;WZ=mPrkpb#?C3\S^%a5Q.s)"%]\<e<OiQ.
%\Uu6<J"#/ZDbe;P\G4nQq:gNS[nf]dTZ\?t\UuO/Uij5Ajo"]9Y@eZDIWb5XqO;-V[X&OWJ[Vn%')Sj#V!"TCCmrW[6f4"BP78e`
%i4[p=]tM+)rN!9>StFAS2@7ZEo3KW,Mi2e?]i>$)#.I5Si87;d]mY6VEqRVrB=Hc=o7l<!H4+M<]gLYjAI_r$f[i34A@Gj*^#r_R
%?[hhEm,gBI_\9cM^ZW-ja]lJFQ@A`(g$-,,P;f\JYMS-+>]&/\q`ic%397MDnCN0eqYKLDK-iR-ZE":<OLRm@R&AOPf5LZ]S$*l7
%4DtN5hd1+R5\[i99l<<tbM.5Z^-"D=qt0MkgOsH%5Oe;pBGtt#IfJi@qsmNjkki@oLI+-&TD,P0s(keps4O)Pn$c#$houg[dk%r^
%-[$qKr8IMMlK3>\&`VsqD]#,3_fr3.cBArg50tpdpRZr\I!G*eP`P&Bf^GqfC-'[rGrbSX")Le\rj(Y>s(-tpI`'Y.QcJ66CCYfK
%lIgnP`%?%dqn3N$)5;#n+[#iao`SU=kN4-VH#nH+e`GK/QH;7g\+/JUjprj&O4meJO5n\gJj73%q]V;EQQ,eUZT!@tUQp4ida%QE
%p,r*0UQ4=Fj`AMqpomkt=4c%?^O1*Gp-0K)p%oiPZmjjBmk'54k&?,dqPR*/+6&Z(/RE<c_LHXtqY].b2Md+"e^'g[<7^C,Mj#_6
%f-dD_D:k4'P>S[JOF=nh]2P^'qT[W!?!gk:H,F^7LMFp%@UfZ*[AV773Mcgqa]pk\YCDS(o,M42T)m:7@Y<"jl.kar3L=HH(]4S0
%_/@t]ID6U<JgI$oG5i@_,fZa&g"dG>pQ4/06d+lSgPb`(*hF_h;"2L7VWV#fSQoHk?$psAEH\of&%hk*>og=[CiBo4q#'R[A68AL
%/3[e0H!@TgG+=l&KdlA<!78'6k.]JeXIbC3ZXkmfjgFp7q:i)+:/9+fc`V/7Hh-inHue!SrpT(-TDmE6n)$)'F,MH%L3n=SpRWsQ
%0LIu@[W$LI]%[eYnMDNVmhp^uQTX[Njc]W:]67i%q:hd[f_;G\,rPQkidGVs<';*7[Uh5OpT<>^gU?(6Iei.&E_D',2qukhDV3+$
%lRhSPp$Uu3k)>#CpWqp]p$;4=o=o0g=(fufc"[?Q>Gm4:s,JJsrqYAf^U)tpQk6rW(q5#%8ZQ,kj6HJUbOIYWI<YMT4/*7'D:$^U
%=4=`?^H+I0OO8#nEcLs1lRd\P9@I0@b&'+Y?Vgp8/WL*N\#ZKu>^UV`m=sj6J`5bPbsp)I>4-,t?Z(f7s76*I@U&X)`Jug6PH4;u
%9iq>W+);#*!1<.B4%,h0Kn=LES0Ch=9]i=A0:m,.b,0%.pPZ9[FR@2(U7=EN:m=>d^dBZqSlj:IgoRFEoGh=;*nZfH/BU1mIN)7=
%-PD?CM(.e@/BY;\k>%XJ/BX:#4I+O\=iR32+$_C"VF.lfCuGq`ZEFo6+VnX-ptM[Zf_;HG>[pW+T2R;`QH;7S>Q9+tBqAd-c)/2P
%!3WTM]mSPlH(-"j%Qj4Q&3$3+7XW]_luE6T$GZ'Hs8Mht(f08b*EX0PD)&J$N1LC)cZ92Qrn+/64(YK8L,*i.CELFFb(6a5=FpW^
%47KW[?[EE5mFQ7\jWL]H`3k#*CM.`[%p\M;eNSqm*JChQY]er>#Hmc*\D$3[qKrPIm<r3%%862O\n%Ft"7U8h55Z>L__p/P"G^c7
%\43sQ9QipO6StN2qN%*I&sp&=P/r)+osap>rjT4l5?s_+qtU_B*r#%QT3o8qqss[MnQM3e,,)_741Y1;-4Gd.mB3noH"*J[O/kBk
%+;DQoQK!FJZE%]QrL0rehES7I'$:4]ja6%KQe.;(kC3I<AN/uCaF=iXOjE';h9l#t4rJj2s8OJ,+-&N[Nm$iIO:OX)ff.=BGnhh,
%-RcR<M9k!WB`,sTnSaoB%l'%CBt.IB4sP@5!S_gAp?2Lk#NsKP1-Vio^V5.Yo^LbQH]1c!KD]PPN8PF_pE[;b#!:4>[TraFm-rkM
%0Ne>9Z$G2pKAWBd]O;;2KehJQ6eq-YGg*X@CCY'&F8/#BGjjP.&*(?Ms#-I]3PYCTo'BtH`:@HraH$J<padK.Vs&s3^qDmAqAh?i
%%fY@<s7\lnH-?6""5q"D43Im/n%o$>P<8Ph&,tJ\:?t^_j5Br#pj`<0l&e*D_0rUdP!&MepXHLVkD#X^chHAh4nsCh=T7(uI@bra
%jh&!7YHG(KbBUGVN8=BMXq'3qB,;B9hn4Gr([$;Il=MktECp5$)eO?h#<Pj+[W7*u2^4sQC:uF&@08$0pZBYmmr"]ImH/c@qtBub
%ea*0m*i7+;3`sH5(:(WZODT<SSq3<r:b_TN-9IU:%VE$0BdOKVjc_pD3BOkLA4VPiT8\R#r6Nuer:U!aip>fk2eT<Y\UJU?or)6p
%?pEBLjN[sFk1R`KYDr7QV<uRbfsY<a-i\7:q<mCMP(U7#-FDR5RH@T6Ckt%t@;2XE.DHKaj&i.d-[G'f<'5U+p"ASk8`"iFdHLR*
%Fsh*DTAZpQp@T!]5%oQ>-bF^e+&c[AKh^oA52'fD&amJ1F$KAXW?)I$#K+a$n8^4qI./RI(g[],Q\48Y+*_tNbT_cX[DK0Hj^<>d
%@h&'<GkhogbO3<Dk7d$b2[6'R<I&o=(8U,Z;Yr`umG@0lcOOf$0+8=4M]k3"Y_aq3!W*?bYm%(7hn^YJm)&j?@\3_LpA\-?DH/fj
%/^hSVq9:!Vc6)^f*?N6aITZ=BEBG?u*?N6AWEFTJ5]5bsLUV9THZVM\H<pkN+0_pRoVDZdd]SVp%gJrqe#uMQ?[m,F;Y/\Lni*4:
%*O>817O36Ll0K>r7D=50LYdngqV1Fues8=4W4'j.pA91&4nlPeo#nLT2fp"qSA!e#P+UI!s#n]uLSt=Q4hLSRQ%=A<R?-PmGWM=7
%d*N7@gAYf"h_U5X:1j+7kF]D:)I$aR*K!tR-F$"lh4^q9UY@JZYKjSqh-%Xd9daeGc8Xpi2[+29]7V,6%lAiXY[]d=S`\&K1"nFf
%@r.RN_2be0RdO+do*7@s_2bd=4*.W9*:+LI43!lF0lB`EKdf0/5P+.6*;ZtM=@?6Z/q8@sF7RpW\[cM9>tsU+K8o9?5jQ"J)rPtT
%3-flERl>![n(l[Ikn]QB:A\>AmHXPsS+E,ua)!cFC]i2T:ZiO`%cOP@]:.NADbe;@\G4nQq3HCBq;$C1B&2qC`HprgrttnJD.5A0
%^:Cu5,->gjh9BIt23XNq/Jmi$GI)Oc^e\1kWi(pTFF\dcFFt(-:EO4I#HX^1.eL\l_;jPm4['!,k[PKZrq,X'([is\qgHUV_<6H"
%Z:V(QG=06f"?kG7WV45m]FK\EpRdZlN1k@!g!:b`DLP2D?X"^qp,qDOb7sY5i6+9eJ?M\rq-aDRD2@q2Ei/W$_#p<p1#<qPnP@RO
%J+Gjpk9$qImnu>4k]6E.Ug<8+g\BSjRlb;)#ArnNC3(=^J:fbh+&b.4F8Ine+,2\_!JTI)c0\](gTX6SAT%O4\W7\5F\q62lJ+[r
%4/d3*Fm^-JF8pB2rVFiY::Ar:^";@Pph-8ED4AJXlZ;lIl#W>ko>Ar<Sm+(,%B@_TOC@lElG`u'-W10:al>u5n1bH`&l0'?@Y^.o
%50W?i!Q`oQ_<bX04&Tek?ba*13W7,K7mafdqW.;Hk1bCAA.!a3oBjL49:E<De[aF:;#\SlUQ+kj.bElCFRYm_/c,?`@eS["\b1@;
%qd]KhlE3<dl/4/2TtYhZ]^=D(bL?<VYHG(=T(a!PiU/@BFl:"0C/`FWKbalBnQfsNS`&<GltV!=.r9W[HEp,$]@D4/Ze>t+p%I42
%02G?h[SH.3o7I,A%o$PT^"k(-/o@d7r60^eap)OXZ]"?\)pQ8ngRYUj\:L3@r6P3#]1ibECUpMAnC+]G9V.:k55jc:AVB)\m]iC6
%b^hG]37b#+\Opok#l=Y^h#3l3lnWM;:DiK&s7Z-Ye_(1%E;no@YT?3E+/!<A6.M\o(>/;o1"CnQoJD5qK1tpN&*-[N#>\MOH>Wc+
%+2juLrlKG/(Atjj6P%3)LULg46iNE(fDdKc]_rdK]Y?S[k11OMmcL`b+!-&1\i#8QhfF#n^>d]`ddH8%h<qiRYMsV]eNJ2!Ie2+O
%fqu%_:[3A@35>:,an_]TRD\'p`t-A-5E8s8o"I>K>P@%F3:<1SrraCam<nZt2X^SdrF"O&VeA@['H>h/U$(Gi`M^$a+*A8nnjGUD
%'&:g=f%YNP;k@G,g&YcNL$f$s&WbrP9_:L;^*BQUpX!8:H2jK+T3KSc)En>E\G&eN^PuU8rJi"nVo[pP:I]OIn9(<8Qf)tWhgX1q
%p7"c=07iT&6@=1YN4<0MTdPn`n_uQZAA$P;JaY`o`lGD@m:88ms!QpGmdg'9@??>\B7$5#YN,drcb7(ZcS!"n>R>N3rq+U"WLnm>
%j9b1thVT?gV)nI:Pc*,mg5o&(e8EZt(T<tT$UeXpZS0Y]r9!q%7l'3h7Ak)ag>JI23Vh[olp%u5+)eRDeSat$SsP<"2t*nEs7dkm
%aNJnjNXj+rko@$ZH^`"!+.1_[3b?(]BlF?Y30\(UV`,u)Dnk(*8RW.Ur!+Shm(Ls0_rS2<(H\7ciBpArDC+P"l/SJniK1NU>[/7*
%SA4g2*^VDB:5J"%)SlXo62J\VrnY34Isq"DMBO]+'C#,G?!mP=oYM.s09lIZZTk'hXoItP+8tVCoY^_(l*m!d>[NTAlh[*CrR`.h
%il=9(8"Y5$iubtHrVsI(hp_V8QT^VbqtQrJQ3-;?K,/JOP'=UM7"6KNCH;7XKRSQ(U!LHLs8$*_YQ#ZNDiW8nDianDs)gi8a(IX6
%j,\]^U:K2h<o;n6D99O$\[S>,d'JdAcsqQ.HAtE_$njukKo#D5C0cX0%*X&BVO+H_E-05c`6gq(e3h;1r>7K@`m8\$[ViHpXW:!N
%ek/**[?iU&hiD)8#N97aR6#:s_IEc5@q.3t19J45WV3+ePi9';UUNY`0R=,^EJQ8`b`+Z$0=.\gNRO7,d@8<4X/e8h2l8fQ5/-Zs
%eYBlOg\<N=:EB<rVVJBc1&*(oX!&3%NdhRjB[MeoVP^]hmH+*b>2V-]>-bMP0?8YQdeK-2Xfp;.^!*a+?,f0SKumnJgT,eGGMctQ
%6LZa_HTO4lnDuWUT+C()F.7UWjR]"XT^?`/ad:3?b?t8.o^pu$+0TU[U%]q,dl$oJ*2JqX].n=eIBBF4q&%NmN'G>93W>f$0k<lu
%-^#DFi,e.1125tJA`tSbcuU-*+Qq"9Q_UH.U[LD41h2K4B^oYO=_Ht4T:e$,WJkc<1=;Dpbd/Bu#X.Lq8j6/YGq;^P[?"INmN>Ok
%\HU$U^$MR#ToUr%ULU"0nR%\RSAm#C72C6?e8[-u=N5kh`c4R'8)#6*/"q,L/hXaao;LJ0\m(54m=T'0Z1IP%d<b9f5'gIdGJ$0#
%Z=b6EfUL_qQVM=X$Ss[rotn?aO/j6^^/)qqQ](&k>3">%(.&5-n-T^]%UPnT)#T&)43oep-?1<LfU/(cD&U'i*'JP4:;>s*5E<rq
%.\7LB=B.ONDOeXb!/]m!o+f:QO$=9UKd0>t1/pH3ojo\,>:E4A*D:dqk^(/OL/quqnlXthD3WV0UD:jeE3QT:H:%(kggb<WqoAWN
%$h;m:H0D+gGU";6nOL(O_2WhQfA5Z:n_DPpl7jS*6HQY#D+ZZ-4h//^n#l]2O+5Z?IepCGci-S/iU;KfNV@-lZm*+:hNNaO0kTW2
%e&S>0Gn8_U>uHr)*o3`hrs_FS0!JE'*@&u!*uq4<>5U.A-4WLbN;#,T?DpPeWh#Jr>59Y."'<P/9!j8Z*t?9f>Od:U(6X$0"7(U2
%g:6;WK_(1%RYWrs/-Y04.?"oZ'2E?akHT7l=2*jr$oma_9Z/n[F!TZ.+rLeu8n3EI0aV3=dL.d5]ca<?\`ho?/FG1]p\b$h*MV2=
%'>)1oIUEm3>-Gs^GPkMP*D]3=?9RH=CMIlWX59WAE5eES?;K92=kl\`N_d_uk#hH1%MhrI@=a2O?O>p=1$eDC#hY^<2:o,>;B\*X
%S8g)bo3'#?dBr60V<JL#8+^fDC6X>a`iTSR3Zd0r3:"KuRAIG;1-.QXFWBG96=/p50#eI>GS['8Pa[PUXJFmA*)CT'XlqTtCWJj[
%]!8GYpKE[aZI.J9&0@!N&;d"kLTD"X;@ZjRai'60gq*kU7HIM/6rr9HH#/bgNXm/Za3O=0VuOT6ZL82UVm244f9@PY6EWTqH^AIZ
%HEXVnK>2\or(A)LLcd'F/KF)r/]Z&!+s`I?QWl`9nT&<td.k6`@TC-7`6&=KLc*j;(qN)Yg2V-jkR_L2;23G5_mig.?t-!+Y!!=4
%O5,!H9eA-WZ+XZ'LcF6e<AVWM1s_oG\9k?=l+Y%*3-n>f52L-j4>)Dm=lb*Q^Om0*ch\"D+WXr!j/<)ECu`DrlC*\RL1!Zm,V)4"
%EKm2R(SiR@Ds8*0\Du=+kWtHF6.7c_6jq^r)(^9)M4r.)8>LQ#'l\a1T<o"MMtH@k5^OdBj\&V4\[8o)\]Z!3YC.**D@g$=lOq75
%-NXq&9!fS^q1s=@<RcSZNan:YCKtc5hX%>u7/AG**Enicg14kg7.%5ihWZN$+Z9!FgH5*NMf@iEJ'Z!phtc&<QJ_JaOYf:0S)!*l
%coqdm,JNE1jUa/FME]=+(ZICphLWQRju^)5?IUYNQB55)WT+"N,1=o-nZ5@7Z!:lOi(M=]A<>IO0Sj:Zm*(MW,rJ?fV+5J#K5BL4
%#4g.'4LUFameNU_jIt&pi444,O:L5$_m5G`nFY+Q]WYUJkT\f3FIPFSG-i6UgWt4)<%O<OI;Igmj"8W5h\SKfC"I;!g?ZC"h&;'A
%ghj];Z"^\HGn;-rN2'F>X57&r1:b6ujlQ5HAg3Y(?CSj-*=Jn[`)UiFP+cV7`6_EIHM73^7?oY4\a4r.#*_M:*W'@PP"*IXHn=-A
%!fhBJ?(#R;Z8D=D`:n*cq7G.pqi(FcoEVo*KC?_Xcec;,C6fjm@IFE_5P0j7q;F1$IY"*/L+O6[!s?X]q+9Mo:c!d98V?"6Sq$36
%QcT,*eL?M,21&GeaHu>YIIc,S09L6WZ/SECaZ4l%+rP#t24mKE4P\:_WIO7aGk^(i_@?,4FnD#<N52aTh#;P4peF%Cm%UuBo\$nV
%O"fP)A:E!(i*77K2qK$-q)[tQ2th(1&YYe;O%hjoV-r!j&img/5/.6LJ586k^+]!`3N]en8$d0rF`65c1t-`nX]tS2ZB+MQ=c'c(
%=B5[%QsBQiAreJ1==P-%ki&kc-LY@q^9!_IZnI7r.\UE.^m"r5Uu(dU\B\Og[rpl"iEYg/&N^i9^`sB=C$_4k3r)r9Z4uCk:k_"5
%+'W8G?Lmm0HqQ/`=9$q"9<`__R#6r)#UcJ<AfA_T)_"6IMM_p($D(H-Gm).<.K(kg3&f(R_^k-,3.pgIL-<gJ/7gK?.u0kN%]^Rt
%ebp*MCC&;[_J)R9p(i4BO#Q]<KJ@Lj=ItIFck=E>b\CD903//Vn9'.g?PLGJBK^f\N0F>RnmfS6@*bnp-B>NO_*Y@$Z0o*6p3TTZ
%(h!o/2L(uflQLQ$BpF-pr=hXT=.C>h<QKlaRknri\h+n5[C?6)<Ko:feWHIZGNbL%6e]T4C-nS*ePEC,"S]R4O0<=-d-(qP>jU*L
%X+VM`=94LVh7]0a&>iGl2D3p@:YsYE>9ZmfZeK;Q_!X40kB"5kpItDXUC-TUmJ<t,IWpj&nNaa.EkZY<hER1l*o8QBoYc/6OYpqE
%_.QSK_0'F@I!-Y3+p->fWUV&tr3"1lkAp@G9sX2M_):!mjri2.:/WLUp[<ZW09(1ZQZoU,5Ect*I#'R1AT3fnUGC.INgW*:*Mu<6
%:/"/j9c,&HA"EDZC&[WM3mD<\cP\)*=l'GZ+8_erGf;9.k>4_Lr0Ikpr+Z,EC>U\IYG-c5B-$He>_<"AH+*?hT)E?\s5tan[Jr,]
%kq`S3`mrpck2(7X^>pN1GK?V/]V"[4Xae!S^>Rt`lOCSo2nnHbrl"]5lm-5;S?6b34mj\NREn*H47>D0]LRF=ff"blJ2tpk\l3sY
%)SGK#]!Z48+9[daKoE&0+Zu_[3=^#pm'''u2)/\.0Vn6"&R9",0([MR<H>29WcNH=GoueU&)lX#ap[?:>D>Y$R\d+A!6HU\n^C7:
%[*6)GQG2Op`!f$%N&;:U3`agW>AK9SnotcG+Z/-1fCP2]Db0E2I)%D\PIk_g_Blm?*OG6U4u8arrr2LJqo?I))],sJ/Aq3f]r7F5
%,97D:.g`iU]7D6CM@BQaJ8unhNiUC5K"p>[*qY_e[oVHgh3&_CE)aqk54OH%C]4l@QED>=1.Wu*nQ#o'3-XdN_"/F%*_*)'[baM@
%VRAZ-\Xsg/;0ggGD>E=+P2m_Fl[3cE"$d%_d8q=H;+YumirQ(?W8&:S/9dkSE!-6R"1Ob;7XoQm?P__a4$HlFT6BhiDIFYd5B0pC
%DhM/%>^`sk)REa32StK6JB7Ldd-K?j81!\+n=4RD.tWm`q3'A9:rSW%SJu"XYiT!!\\ps6IM'seAHKCSNir2u:EP$4n0?o87X1&f
%NA+8k5'%52rad=ELKeqhUH("$Hj'2[]E@3>fq7U56l"nQYn)"G($u!MZk$Koj8"Y[([7jR#<s6O!1jV&$A8DAdJP`[-1P;%(IW+p
%0_Oq)3WM9^A88!QA6Af!L;46E#"&rXS2Yk8e?C0THPjsccQ5e%[C?&8"DGq0[HgINfH\Cb%"K*[-lm^<,%r%V(p*id*I2_uZ(1Iu
%n>.lPrU(['77;Jes/[;0hD_bba&9F&ll61EY&cFD);_Vg,tn^<'*lRU1XC=,8%lo/-=u`-iD?qRXT:E%NF_+Q+RkM3-0?Z;D;=@^
%f.YG:qoeKsEkT)ns88J+kI^^7RpM!nbr"l/m&XkdVW,rkdHe/uTh3SsZ&H.FmUO!"4#+^bikS7#\/7Y2N=QiZ1TJ>lCYofR9#/@0
%>4[fG]6DTbH`-\qU]9NJT`5BOEWY1!n$HBo4[d=_4>Yo0Ej2Fp_0CW_#cP`m1uBIK`YB$^k)*C5r5Lt@$6Bl;\\Y%T"2`Hm"TY43
%ErR;CJV"_ON6'BHBJPm_#4:n=_E-i+qteo.`-33eVS:j=p\)]N^V].inWOpb#l:oB0:rfG+)gjm2J;*gZYY/J)O&39rJ0\gAS(l\
%_Pi"kpgi;Ir7(2,cY64GmC6-98W"2+9T4AQU6gs<$(%DCdPWKF^sR*FVJMfncc>uu?iBYZr/lJXs5uMaIIO4AE5fZ*70rq_2^fI?
%]FMd>#@Ar,aQ`;"!dHA&q<It=cq":m<+us058kCY5cMjTR+;?r8<Y*90gtBg^ZN;ggTNWRSc[)[s&ri5iM<VC^;#%MqgQ5F+[5a]
%o67tE)epr'rtg0G$C$&1J,TY[,(KL7hu2u0J,AP=rT<hsA:6O0(<Qau/Rd@`[6<dt^Mp^^ipY0b,:IkUI/NkGr/6U-0k;:CaiHjI
%i^[mho4PhrZF=cOpV5]kr9[ki&*)!B*PVE+^\M\%r)XT,r#8,7b#.]5OiAq;4i:A?"#Fn<?V.J7O.fO!J*^>9"31:`s6@9\R'?n2
%m9eM"F!'e.F'GXUFhT(3h1RRC2rK)HotUN/NfXQgUlqqWXfgnO!4,oGIl@-8s36mr+LtP;r25&C]3kU*?iTW=hu(V%rqr[Do;c6t
%nF:u9::6.j=jDu:Dg=2Z8FIY\?'t1.`\HQ_#[Ylo1Z&)?'9_O"(U55o*[gO6XCmH@s+TumL6"CXDOPZNOWS;Zf*bn3cP/#gF*V)@
%1>U3Amt#N)@rbosH!Q$gI/eojs83s]/dqa2O7o"%.'-VDbN:="a]Sqaa=j#9A*10Ja*;c1cRW&)k1Hc-O8h#;<'")d6.1,J.2_Q8
%cc`Cc?7'!ma31+@olZaHqrYuLIfG,\IthkC^0!(''Y5$_'Am@'G^t!P5eu/VcboMcf(ZiDZ0l_jq1oo8p!_<<L965iA;:=M;3R<t
%eU-Ig6hT7T)c;sq;D`Es_ko#2<o\=\[F&JaGEbCA55rm5oeMUbpeb.aI>$bCb95*JQ\!cdhYG5oo5Tg7qoh2-eCaM&)#n7DW5<d3
%??5&5eqhQe"d>.c^Qi/4*oJZa4C!H(>t.PF?&"mJpKnd;ci$^kmaDJn7<$NR*$/G@kDJWH0YlKR%rUfFFu+Xq-/b9Z.)084j_SL6
%W]/tK^RDJ&<:7dOe>Jpg6_Y"TO3:ofl]QIHs'oE-Iu<?XH^/MR*Si%C+L45gGNgPcOo<!=hEAcuj1G'\eihcmP_f43`Nb`jT9'"b
%s!V)?C&2:BF8;U#15ubTm@V$u?G7!g`Ku(^Xd%-m-:r.C;u%I4B36N(Ed!p57V/LMHRP+Ofugmk>5_#7n&b.3`G-5_bLb:V_uC^'
%n$o5C#bo%;bGBr>RF-GD<FAasQV[5@acrD\<[YdQ>h42phIkmK9R@<9+^8[JfQsL\5Xc4@V=4A/5$"2711)tImbVH[HmF.]rF]4Z
%`.JWXehr(@+ak!'Xu`;ad+"oQ==07l%KbS7QE4l8m]k?PpXm"%Im3kXn51.pC>/i+2Lc>p8:5Qmb/eupY>->u?bPW%T,i)-#X!V)
%oUK^`]MD&tH1i8:%F1FcMHcrBBbs4bq(D:sT#8D8#p#&uU>6S/qD>:([Jg(4_uI9^.FBWC=,on,Z(c`lfe);6Dodu,fQu>uX.S4f
%CR*N=4aOcH0E\nl@Z`jfStBd8fcIY6/0>cb?_<>Wa!RJWFGibU"Q*dl=O`&cn;:9hGFtI.B4f]?chZqJ4oe10!"Yf#jng]is"f$:
%E=[kAiFNkQHAQVa5D,<U.IH<[a8"lomlu)H!kp[$D#I/Ls*7Y&70VCUA&S=>disQ]J_HU6Nab#UStGu7AhAS=K)pP14ppq02#bhj
%;0/6jO6]9im,n%\/N7#`c2G;&6+Q"Hr/:,J,IUagiMb)Bs7:_>c2]H4bL61r:\24l6iE:rpO5+H`snunj3K'q87?1qQ13<f)uXDO
%-3jA$9<dmH4ONeMN=C2,&j5YuWr)TM4u)H^)?+8N,a9rt27UOJ`.<,(NL?R519L<@]#.pjhWLLO[X5)j4T<Xq5JoFR&u-[!UE(,1
%1RWu<GZ='J4D`(Jbn6"$)iR3]g0bN%qL%p"pD;pe,2:>Cr7)Uuo*_7,?Gj,!Ha2G(X2)aE_O^;Q4_FX;+,V>aH\ma1rhnM)maFkL
%Z*dHPSt4"`o4l-nQX?+a^\mQG-QF^eZrLD(4l>d/j_ft=J+;Jn)ug_(AGk$sH6*%J<rT1ip']iNp[,dM-R?DCk^GJGo3\en);$Ro
%X.6oP!E(3pkiJ_)Z,KLK3QjEi*@Lm9k8TSt>g7L5Zi-d1s,0*"r%iC9NW2eUk"c\'rK<B6"]t-8BCcLFVg!@(QWCHe5JEROM;6d"
%=^3QQQSEi7,:k19M'VNbo_CV5=P]S3@l=9BL]>=8)?+c1?AJP.(A"HnNkUl?"\ebp+Q!kUP)n3nErVFXD&2[WT>9>\s$6M(q6g5[
%f>%=HmskA<hu.0egE1nCiE-'Q__6N7ol'g>YOK=*l6#uRpV!2ss6'FNs8D?RIfFm#KAlk*n,):or0q'lh66k-msdjC^rqr)21G)M
%hu<-8E5)GLo=t9[?iR6r-3">d_>h[h^HHN7c+j5Umsb-UcUe'm:Z:40BkW'==#5`O$3d+%m;&<jpem\+0F&;OWjno[DWf^0.YL@c
%#FLHXq<p9CLf?d'!_bd3;%UU"O0YS_m,Saf?0<soEo9GF_RW-V\dnlF$>^7r?9&2i;Lg$IXA5UreQb4%!b>ZRi?iTS2o\!]gBPtE
%1GMf6&%\-SBlp\tb_[$OpU:`h!gu9D:dL@hUGhPpZi_A-g,8j-0r\;L`sF7mY"..=[jeRa[K1PZ@3O]uA]U^njT-:0CH1@U=luJe
%Z(V\%QEZ3dXs6?uORZ/qK^F!d*;H@N,(tUL1-<>r['#4.=S[5qTK5sOb-<hqr<AqX9!AsD(,?sT)!hqT6hMYM:ig4olSsgtcf_2h
%!pIcV%<$&@f"<)5[6]`D-fGAbj\-n7=C=K=^?ZA];.,MT>2!!iVU"NZC6BMpdnH+8/\*3.,G9ps/#?qI-&k_JDi<FVZZq.+Zq*]S
%cU-Ha3,`n>&FlZP:Tj\Q2;pckkrnW#oauhQh+%\7U)NV/N6)-mjno@V:&)<X,V6O1L,c*f']1C+V6sYG!il-R]*c:qgKG3rZSi)0
%MV+S`1!Asd?o(R<U.k$8_(c*s-N1s?]g"n_E;kVT:jk58!NZ,$1?*TBQ/V3!-$'2C/1P+W->!:Pjo*t'X+:mD0J#,#kd$G!/qh5Z
%J?emW)ba#=`rJQ^T*IANlD4e,HHO28UUb?-F2(M2Y_DcB8";`p\Ao4SKW(7bTtUXK.K.:q!<jYl.YMT'4-A^YD'#<KJ.V.?oEiGV
%+/[!WU"Qe$A447,\\8eTot8.LJ^BMgUrp:*[iLIlXfqgNQ/7tU?3"bJ.3`Q0=d+lOkN^l.Tk<s?;h(=lE@9`C=;GRXgJIADgK]L;
%lp*)X@D]`f`E'^ZR)JP`ERIUt#,>peX>FeVZb`Ybh&2HYKcZm)rQ65J\U)9A<APn*jU,eV=msN0&s(DQ%$8s3Q>jR.hV'OQ1#3ol
%8\f@$d?Libioe8"L)E("\6i\7(*uBA).@+U>U>N1:/:q_MR"t4C+@Ed]f8F2G^sl#Z<kcojG/hXaf+oqX+Xcq,UM$\Aa70*pca?g
%Rj(?E]nmlaKJgeD<:W"GLa#%i8T@$>O6;:#U>lp`SQF6._e?W1%_Jg\oFqk*;Wr^I01OF,Y.E<b?65@gLZJj\grK?<rA)-K]HX7i
%U%TOdo9A:YAAE5FkKGTkKZ`e#&mH?->0Q8K[]_#'A<t*a9i=@u`*H*/2fJ_N3^X2Aq:d=!g);-"<?K1CHM>aJDH8-hY2[-];\%'5
%C%4$E]@C$g^gmWr*=5(>lUX`2TTK/?_pW"F^6j,6Q:^@SWg8e]^\?rb0##&Q]L).$RDUd?2s`O)Lg-*nW$Ts^+#Q#U\&Oo>`Tl$M
%9A,76*asqYJF__OCWaKS9:S6K>]XZ`(lh9Dnn##A5kupI1u@/mK\8_]5L.(bH2?qgIdciV`qO]\R$ZZ&WQU_4!NW(%3Xbam&!3b2
%(\eX.H="Gbq-8'SQZum.1W])V?*a,)kY?ATm^C<6oi+B&S!@NHg0pC>C>o`mYR-!b1G"0-\&&ok.-5klcAHn_.3)W*!&bMa2ME*?
%Num9(RI()e=Z?kY0oN,o?u;P&3V%-[cRl.kYpBLc0RUE,)2'^,VUR`N@/I/gf4^+OO4S\R2%!4b@bU4<6En(D+e83g<lVMNb$%$a
%B$Abr&_\S8,T/206su^2fn_,8k%g*Dq(*@VdVI@l'8Ufnb$1<?L>++M^LqqHhr%TtSX+Om>(+_+"X9$>$lh;+*6qMm<4@J=bgn]E
%,S(EEXkE,[,.prGph6rW5[64Fc1k<&hb6!OKLZ'WcSY%^/rI^^iCL$amYO[AWGJL!C>9tD\PS>P7ZFWL:m3F`A;8GC^V1+rX)FNj
%,b#$\7Q#"/bdHbMDN0lC[i-K-hHfdEH>YAHg.s9/@KmeP]TM,7cBA-A2=[W^/MT!c5q(N;)?FYpA\/k00<PHC2+R&rFXl<83Pr<p
%DJ/[,)SR[r\J?7]F%eGR@%%3kl_@Xo,@!9NiW(',apeE<2,1gB.bA]RIj\r*2gLGIDA,(8GZf8I6$g2>-#mfZh%3$LZK'V@\qO8j
%>mNE!9g<:okXnDoP.pqm:%OW^k_S-rf\KVSd2/Q3s5L.)@aoW#'<8hH1SO>gqUhEp3fjI_n6i0$qXGRN?TG]TZtX.5`=$=R`0*Q8
%1nZVMkJjo;<s,6[h-56D+L!;OHLQK$HNW&t=MqEBc];^6lHqB/b(u\tmg?:Iaf.O2'+NHN5;W?-;)3E9A+"'H,gOjg/N.2X4\(NT
%e^e%O&8goDGEJ!Jj-#P0=9F'lTe;Q"*oEJXZ#9`knHpONI&"-3R0-c'QKW+;)K?`FMn43,PIK.u*K1fRG.L$s\/[0L,14Fklad,#
%Ji(D+okZ@V];[I,ZsV,`L-Hd;J?&1fG\=mi[t\k@(',p7lZAG'!a)JBI0W$J]&6L]R<ArO0AV1-8m*=e2h8db-h8-`n9uDB>FEq;
%1#;^IUc3MIh<5CAA#/0_So7Gb^dR]K95#:S6R=.T5Nb7I:EsWpNtAn$Zl!4"MK\&/e6%ZW1&-@DkoBM<U0+39!t*RU/&t.&;a9KE
%K*ap^/6`_-8-%sETe4Nd"c8q5p;-p-^&sr*'&TB2rmsOE7%m4H.GX"(K^cta@>iIkDp-kZLd`m2KV5Psk>:ih+.H\56q@lSX',$_
%rfT3/YSM'a"SiVROcTbrVu*b]MhIp&XUa0,acc/-,P\nH6\/Vl2e/ojMV+=ba&Y65!ga>8*[sQ+#Z4aVbo)o,/*1*5aaE8JdCY6o
%""]To7FmVeF\'C=W`TBOE+qRnL"[R9"9brtU#DuupV=3dF&bBI38PU:G`K4kY5Tko2p)uXf9$1'NK$qImq4pIhAEJd6jI,>Ard<1
%#1?]I]hu7'[@^<^[lHGS"g^mg,M7.T:'fQqb*aO)`fh(VT9K<(#Y<-f0eocF3[,?\,A]<9JXeUi&dh9aW"QcrSP"2E'W+m%;a%M2
%&cL29UGW-r&592Ph24QoPmtcakkk8O]G;at"3h?"-]+bWPJ'+unYb2F",+]mZ&go_LG4Th@gqnKa0KT^NZ2K\,eKO=9IC68]%A't
%`f<+K<kQ@M3;VWl;VbejTihQX::3:S5R&m`o@;T`k+76-D_hl=fo?)@C/2O\0qaEe''6cp@+&CR-u:d$KD<hU_28.7jJ#<0$JQXM
%bO:6u-fZ/"j=7dRN+Bl^:jF`[$fOVbJro6[$EJA-1fike:>\DdTW/dHS!m2sDZ=J1Nk!Cq/#;OHqfHtOf<`#"NpRtA'X+.!WYb83
%*a.Y`[rbDn2p,Wn7pWTK=_a&:'AnQrgraE[b%4p1]bXi@e.,ETU;CL5A]^%"ZrWCJHA"<72\8j<[O*I"L-"`;MR&3WRa[D<;:Wq0
%,:j!U3=Dk(Q_#NcaqRkR[*Z8m9&TGL$1_&+.=/RdrjBt6c0UX4Uu=uOmYQB;q"P+-'_>A!1T<$&\gbEl'\ZY+S4Rbc8,6f<jcRbY
%g[jg4.8[n4);\#LMC3]$^+$>O;L$9`I3l6A.@,[SJHS67DIb,_Hj@Gm7.#Z2/L?G7;=-HB9=JBP&O#>i8A1IA-JL)I`Ks9&6lJ"r
%pCOXiSsIan_4La_Z4PZt,N"7m:EOJnV$di![DNcl;-Rdn>Q_-5=6'NHEkO=">4W?*pAL+Zg\'\4@8Q7Y+_=NP8\<UqQ("p)$T\<d
%CiJFj)tY\cnCn='Z:kaQIX6\WA>rN\et8(%`p^tHLli\AK>l/^HD@UU`%_J&g'7t.\R=qM$:^S<?-ZjdL%7[q$neHZ-4#Z6;sbNg
%&mdu>+ac6^;@7Z(<gaU_?KPAsqOF58AWNd"U6k@[iI$T?9>[%CH!lPl8j?].W]+2E1btC1*`=FMOtgg!5Ah=/l,J<HY'HUP2`_=h
%-H?K^$ML`^*1?949=*#se;pbb%>qTcls2Tqo]kUNq0Ck's0$L'.:9Z=+[3An6VhMH-K2PM@S%!8;&ID&P[&Tl^L&Tn/"j/<IWW[%
%U9C!.2t&f#?ai[V2?T79Bot!M>pJjFg5^/k0O4m]/BB&:K^HkTMcB*6\4Vf%>JtJ>0#kiZF[EN>/p3Z/TjWM:c3E$B]!P/eAAWrU
%2&%!]9\OG3<WtVg2i4Ak0`)bTi2M@*fTDI@%;F26:"RaHcTE*#!fQGL3_E,5\69Bn)mJ&;r[h3*9PpMXlBe4F:=TgM7KU7c<6g+8
%l`)&RPsjBD;$;j\Oe_.VL(jNqgr`HYHP[^u2$]?k0?Bh9\q\;cnF)-.5Yc/Bl'"dq8,X"s,-)UDTIPVR&2-g--?]e$ki_oG`)@:O
%fYOB`dQ2:-00spHD!@/5"?!:PAq5`n!IEhJO$Y9iO]LC.e:4c]eDlN)GhP6b3\X>-pF0oO,\r9&(bkKi]+BU,Y!<S!gYI\V[<sdX
%8;At/%<KimHa&?;LXFH9$a9aq>k2dTJ@45b0^5]f_$Q<%F*OH&KUL--Cpk:ImuP/%PJDlGCQhK5k0:^l*shMq)A-#qNCH+5W&gLN
%<+^sq_V!!J<sMEaoP$1g1:H<'!jqfecqb!Aj<a<`S#pqh"1d<24R?jDRXORM*?m%nLjT1Yi($^?j<C03`N"bI;!:&);+>YL6SmE2
%.=aaM9a(d&.n>2u`*bP8LZX?,(Uc*j(9k`AC.2$P?&+5W=]tpc1TDc`X3TE$i/Zg;Z;m/3:9MoqSWs?d5o,2eg,YBXDEo2'=W-\E
%8'C\%NErDK9:N$FG,gsdV_EK<"t8i_A)?k[m"D3#<`WkE2u5d4*]#46qb>A`D3ML<26hg`.+4QA/IeQNi-CL(O#sMi$`_9\M\eBF
%@T'$rI'f9.,ZdbPaqaA#7IL4m"K#JJI[Bb+H(#:]hediVa%l.OUhO1#@Q**=`+<EMAIL>?8pX[f[Mjs`Z6P(N]rD%$:8F\
%AVSE:8Z1>K),g&mghB17[jWOjra*dZ;dF&m-.A6<o@Sp`DCgU3@$C)YOY,NkqRWjq$3M`],7+@64E('4ptDQ$0fU9R\`h2a[DA",
%<Cj/34abe#<pWnU<?GlIDI>B0!`Lg@=Z^;PVKLKZJQu7Y(Fi&[N[JYi'V^jh=$6"dl!f>00$ju\-a(D_5T\$ESKc$:P!\;i7BV!/
%&P?%K!%q/@0o8Q&Ei+.tXFba8dn.`2^1\eFn]2?J8I*kUMl6Up57-r1\J=d<k;VhZ!bXt7'j/8XGU")YJi4d0HqL=HhSc@:8#VjM
%&7R<QDT+JCj0K8t?9uQ8%:j7b]PL"t%9PC+4h)4a!&DiH:!\F/i)&Nk9RDF=/]F9rC3<7R;^0FgpXgNYl\OK_F!/ST(CsF`^+!;X
%?N`%K]Gr*sp?4ct[4b3goRK)T8]b&e(u^*7QR$:&iMN[,:eu[ab&$'TA3<8sLq5mYBoA(0U.!'ShkbjNZl\e5@"&CW(j,Wo"8s3M
%-=[i7%*Zu/GljW?E;=sEk!-J<:^O*R9a+!_(;)L\8+X4>m>>;(j_h9%[&&&hm5$m?f"_Vb2C5dt6`j`.gY(r/f8Sq?`HP/4&&NSI
%8?G7dZJTfcf=r-CQQM_$fK/V>'?69?=s.nqOR-(XGEWHohg>P$D:/8Qe!SrC5adtO^&=#6&6k\>\r,,!\;%^PdK?$jRSrIUVlWGj
%U59/J!ZAoQZmpC<m@X/^QXQFtASG9#H1OI7po5T0qkuDBSW%%P$sh&\#/Z1@c?f120,G7QA@bU*%2"JUnD/,LGfr8T5SYt==4l#U
%pT@CO-<f,ZpNLE`@3o/TpYo';.s7^bC>T)1qPr0Ej5.#ZM7DbqXps'"#eKaCk)-4AgCV\p(+-0M3;V;HrC'dEXa!c"`BkG>&Xi?(
%7#-A@UXtj%/%.T]e_UPQbo^X5oFrCUKR0X(W*DaN=`S03:uILJd%c#bCii"88*EW(]>L1ehCChf^d^*IFTof4]BE+C+JCSqg`3'G
%TWhe%(sItN/fus$k4B&(5]C3JU'!J"FoTr#.q,GVBArpKTS72@$Ve%d_F/T5VV*?3:5qQ)fSrA)!2/'jVbn1KPM5%#J^A_Si#nP.
%&HT]6CDYu7.S5L9i!W`2g\7#+\?6\O2(:@*VJ-s-Mrs6VUcR6Q\i,5,i0*F3.u*FSPb(LE>NP/&FsW&^$cA^iYdDl5KMZ#1'M!H"
%9[49_+!#*ni%fsPdm?I[bAmVM-r?,A,,9s[%K%26FC9.+3a(!HCYW3*?dGNsX*>nK3EmI28l?Thd`;\qG!^.[n=9@Z\m=dHjrq%!
%k"XWB8,oE('gi4-B"aQr-qkp#Eg8TB4g'_dnb4oLkYB?P!Y!W%7GPH.2b.c;7Ga7)>'ZKL(YOr)!^Q\&quPEh#qn(91\gMf7!#Jf
%$bt(V(35g>W,Xt2GJa-rn15jJ\%Whl"m!?1B[-K5lALjtC[mi`!=%o^WGlu]\_%jN\TCRkqA)0n?!LH'YtsGfmDZHB:o>8:^N!$t
%b_ifXcp`?,7D6CG\5&hL3m%AWQSSW2%DnG6dfcYbpk[Cljtg<Sh58[Q,>u67cn*8*hqG5S`*QaNOJhs)m6?pkF@Eksh\^)e^^3.G
%5_4<lEuqD9aWQV.?,*Gp[h)<4Ci*q8lO5D:/J*a@`"EoKA_OO@lp$_R<tb'q]aLFQFCc!UXB$]"#==5`:c"4H?bi]TSiSr\R#.\f
%HdOeYd2alO.W1RmE3D#jQ"4KZ.L,nC,$bhu@i'"f-6gL_1/?U9*X`3&VEhW9#$Q[kYm<nO)f=7EbRZDeRL43o?DE[M'ZC3eNefhF
%.,9?@60n12O?`?9Lo@2uNQgK-<;>"0,m(UJ_TYh10;.R$U'aj"[gL9:W,BujXC8\$Aj]U,((gW!*'c\;2r+\Kd8H'D)#MDtnnnNf
%0(jJf/qV7T?;_1_ediB$4tglYqme%!>apF&m3o'L(lq)aL_"][^&>1L9c_&RogH$Om+q1)f,h.$Da,7QU.`pO*<mf7fOlAq>9)%h
%4X$L7:utimj\YW^\TEbg:9Bhh?S`rCW$9W_'<T3*T[47l[,(1+=7%#fJ:OLX4Ckd:6<KIr-a'SP<I?^TUG-Y$a+cl+(pgK/1PG\G
%L0,aEVpF#X:(,7)->;4fc30:d/*jG#Z"&Q%fmZ98#n=9[Nm(6EOT*+2WZ2IrE:d_UJ4jUT7$@$n*G7?@/r]*4p]N"=E#qOeDdgUC
%]-m0i0VLkV-ekjJ6oBZ"#F(<>"IZr=(BWu??_[^3Bc23(P=[A<W([f_6S?50>@dV22bA<Nnsn%/E4V7j7"R(XF'T%P&::G?>\(nh
%1oD2Q^<S;gPkW/e_VW'CQ=!N",I?h]dW%^1$W7J73(,(6e=D1;+T#Jj`Q\KP<CL_*Y1WjA:V^]j%paU=b^uLpS(*l[DO/jb,G3I1
%J4&LW0,oJ=^eu6t,*CKG5q?\:^E@IjijG>IjY^paG=VnSp3:ldh6<"B+Il2r^c<$.H:@jE`C;eqpZd>eg7/aWP*]=iMQ>^)B,.mL
%(`Q&7HP9',Da^E=LoP:!4HpHY5&:])+)a!sfPXp;p8QqA&g$Z.:JbWOPmHY!@ICccL/gbKV5s:*bsujs)gl3-d+N3m)ciq942!(m
%+UE9<Q803W]`7E3h,RZ-Za)"2<B86"OQQ18[57)^]ZhOSQZ;]P[%g("FB"9;3@5A&q("j-3tl#?<gRch(b_\h$/3SDbi!XG%nO58
%<GTmc5TWIGK_SQ:aC<EV/LHE(^2/k[TD^16)q7BW12Z!BW@gN1p$ajiH^c/=eo)J2/K9D%q0i);#hfn7!o,o3"F;ZZj(O*s+n[/Q
%d(AV[R8:$l3Rlr^#gMOX>\`[:D!u.BOFeZmDHTl%EX>lA*EHJC<nSINh)<PFkEjF9'EjpeqcV!kNp*9LN&;5AmWU@H4M6rq5Y$WT
%&cm6h#eWEKF?+KS0MIOiU*&[1<>6lPPp[#c4?N7W@LBHWF;BNs&S[M=%/X00<i?MNQ5]X,idc,\Te>6YdGenm`N+NGEs]ft$DG:D
%a'9.]!.g[T@>a*(K[Ga@Qei0_S25ht0H)t&1ccJk[Mn?raA$pi8>\(cHrpUZ0lh.ZN65*PPlRM_+6"%/i,Z,lSIQ@jf6khf=R[XC
%D)<JBJ=)NjfWN;kS5<P96HZ;2!TQ3c3#'s2!=3fD&G)8ug]9t$%j;_B*1qWXD0l]A;F7l_eurZWG`*X%,GJUOc4q'RCm"":)Z^rB
%koo?S<`qdq=)j5abb@^i:bO`p.EXum+P%;4"E%RJ+,3XbDO8<fQM:(;DOV;@'+::%1_]2Y"j&CE8&B6sT37JpF4B5Y#PimBj62Qs
%)hc._lc<KpLAg)10o;a/4e-%s\<^L^/_J5rMh'g@,=Vt;NG?QU)Cgm.(!o*Q#<[Thon'AC74apdB*GQ#MY.mi;\1k>j!B!u-k!/a
%[c/bpLnb"[[dUUHbO$#e#]7RN5^kU[!OS10Y25J-6tg+Lhb8"c4?sOW\%YQ:Fkl*@3P[0k3Vm:o7O;I(7EFM@[m"V"-G?S6Y"%76
%;5Z0\<f</?!CsBgQbp2.Q09Z)dF>,G>Rt%+EJJLJe?cjWB'N-6K.ph0Q^^bCj.tdV[RJ]8XsDbuWE7CTgF+4uP-*Zp@.Q.f(of5Z
%ML;_Ua[.<:IVr5VdEB4AZua1$+S;3LPM6NnT[Os7's<a<?!2M2(L*gSeG&AgCi?>`"Mt2P`=pY%Zu^D24)::iKAea+V_75.`Oft*
%PAfAG)q$:'Z\S4CX.Xag^s3bI'0nf</^ajZ(SllP93pZ&3*Ma?hP/1A=5!N<BM&r$BEX=AZa5SVPHC3&)lm#.?`:5>1lLt!o"m"]
%Qi8%^0JutL<`0\G&4M0?(R#+;jq3.][^M0Wb:SHW5tM\-&I:hSqIAo+/8jSeRKGil1Nc&3=d;u<,8*,@k5\kpi.*"V,/!pG&?Wb@
%r)@Ql!)Ld#]mCSt_E%<Q"5f65cKGa"R;7J0"T,Q5>EhF(9.I),!37%/(A;*(!X$T?hP5QZ(6X'"#l?/0</1hjUES=D/RA=IL]cpk
%LCn**l;cEErE9rK]AFm=U3YCj4i/8F.tm0LI0ZS47oHZ9'G[U$<!em3Ks0_.XM.S$>oG1K@aU!6i07sS]Lm:bX_oEf[A1H@8E%m0
%iFo\0)j$nR"9<eV>BKch)l\K:qX8kG&,Fp(4O""qW_=>?!s;fNU3b>)*O;m=0%C2[<h$OJia]&8F,XHHbT%?dLJp(Y_lQi=%oI#j
%g`@q_9u+GXH0IB2">6`kL*B#s$Ilo%C*>0g\j#LlPU2TV#=sT^@3;cg-2)_1g)\&3Nb(5=rdn"7-tl]FYZ)X6EP(?I\O6:C<MAUD
%`a&X]eh<+'E<=t"Y`=Cm6iaSR`W`P2`Ogq^BOde70Lo)".e:\d5G]Q#egU8&)Ri>i"KYRFc71b(>85NE)URdc#[3'rQ?J7e_!X%)
%mUEIMLQ5dPM-:H6SQ)H6G1^oeBd6Wn>&=hc\De`4Cq+%e3'"F8!gN!W8drU[X.-=9!SF0YE$NbZe[c-l2Z)OBE#eN[W1k-^Se2q(
%>1ZPmFlt&K$Z@AXgFcXA9<h10f2_E1A+^VLDK84gmT<?QMJ,3H.ntt#<.u$>)<^-S&Klu]*F?3DY[2jJke(i>8Z=*<XBTMBK8]fX
%W>YeV1<'D&\Bh2A"7ioues3?Q=EbtUKjnl$XSs7qIJSNi5hk39B*S>tq]g4EN^0S^F;ooEi+>"\CUn+QAjgeCM;HO(!g:DCfk.A?
%V'Ho&q/R116be*a3MjF^^4C(c+Kb<\1!+c@[jmHY*:,l,jqf'O'.+G;G`2?j6/S"%9DUW1YlP"<N\_<Sab9Nq=31Uo(<)5'kuM,(
%'aSe>nfII4KI[YZZq/%cq\=(H+U\\=MWoiW_f8,`mnDgGk?p6(j8omE^knRPA:\ZM$0+!;e1>c,YMo=kLQNb0(c%P"?'[*/^7NDT
%?BPId132Hk<@b[P#<XZZ5n18aO_HK5?X&4.'iD]XW5A!/k7,-+^EjtY!Q55bGMZ5WC([C]>"e'/6BN,&]SUcX3$;84=F9*A9f!%R
%$"kZ;QrV'=Ke6:HL?LH-]ngCsM^t2m@ZBEWgB`IsNs)]Mqgt+nc(nfVjfZ@l$aW:.ZNg--a<,"rI8knNiPLCj*dPJJo=q5r@d"k5
%!bV\*NII.%K&csfr=i:&E]4H9#F+1Jm76"7S'QbrW,m#]H)o`C-IPZE;!X8e9dd2c!,_5RXdnO1Ft!e*\3r\Z%<A!W\63@5g!#9;
%Fcd:\<b*FQCYgr\P;NhS>[2!0DAmkDS4@41.?eYMVJ_Mm=A>7K28982jL$c[U@:5`\7u!HT$8gr*$e#\a9rYj+eS(39AU9C-IeC6
%c_]>fAo&VDmB'Cj7Y'8aPQs;U4up/i2Yi&#WKg0k!?ngZn6&3Ml6[^P`6Y]Lm3W"sN;$d9#@un=q@MO,K#GnPi"ZB1!nT]i<7MJP
%]&>oA5tlbu&A!?)7^o\nN+="Wp\(!\2Sei^C6&?)gg\Q?.Bp\OQ=mMAJCFm-$\SErA)O%Y$]SkoO;;*ZkFA26mhVC87OUGepoLaJ
%[Rho?*#)W5j>?'R>==l,h"mt8o]uo9]u_^KM<;bSL4.@QpF!+Nihi.TfqfSmiD9ua0u=!;LM)@P?R"2sFQuS\+i1ud2]Vs>L>>m_
%j)P(]_i!':l;gJR^?UICI1_&jgG"REU;BO1>sp!g%G)cZl8`P;[AB@uaPs$9=4`_G2;qT)3N.p6EoZ_KH)l:bMQ>.)fsi-dccU\c
%b0W6.r]>EW>O=?"5r3If>!1=5&g_\e-m]Q)'fRBmQ\lQS@A?QS!`1>Mh+g)m7N*)J!ZR6r6cb$C(($=.<bnCB4o5Eu2O2lDi,)Q_
%%Ma+<Wr[p)`$\&7aLkZBW[QA;6K;qfg1cM<bR9,oA@b^DAHijo7'h]eheZSV7\>T\I?$8:TF0EM(5K-$K,,Kq[.-s\g$%PK\Z#0`
%[%4NJ#D+e4K1Imha#*p7r5";+hG*t*BK4pJ3,gfGpW;[ol2g%(0dO4)1rgQ>EChKg$C1l7V.uh-@C_M3148IC.91/U5Y"8.#E)L<
%3`sA$hSP73Ek*_/;8n9.9<,('KGPdS[Ki"M7\\nec6<R_eB8piDScOtOJIK=l'kXThQH/>h'+'SG.&HK>?IW-oCS\<fl`p;%B-rj
%-WI$rO5fJa[+`(4b#Bk:AaBsi3"G-?WC7pdTT%P+6F#lae`[H3N\J"A1I=`Rau\Ro]J*=\#Uo(=q1rR7/4t/9@f>!9$#16R)a#qk
%'6h!49]JUQ2X+^RXkcHfd*$-)1>KA`[RTHo^k`j<d[osCP:J+ge[3H?7D4;DVdpJq8Q-^c%8pXsU+]0KWt,(g"[>&gHQ.t(M#k/`
%W3];;)$*H$"aUsO>LfTIpn7(jj@VcGY[\-#rd/5c.RJ,fa]LBo!>P'u%n4cPr7YQVJQ6Fp?hA:GfX"m8mYEa)dR@F.Z@g4l0h-ie
%RPLaZG?Y2C:kpm;71k5F!kt<q;5n`eIL(H-1MJnSF!g;ZnrTFX"5-T59^j,J*M0o"_X_*!D@/Cd@a>/#DlLZ`5Kr(6>>slrC=XXE
%!==FT[/(>rXA'T<aGK,IO=<.P]QX,5h3cei(WG-@GQ3a(l@8cOYC?9X^]3tVpkKqbZMs=mmpH+^n,NBjJ+q^tnEV0Fol#<Pj0*q0
%RpZ-^=+C,!^]3`2s6uN8J'.Td-OfLfhuEY!5Q(4>rPS)U3<"<CmsaMFJ,SPHmX4sG+;l7mroN<m>sA7GI$X>as$?VY%gpncO8hZG
%pb+\rT[3Yt?iJM1numN6iU;9n]DqLZKN%pJ,6)4+niLi.!*t9d6iAVG`?-<U:%fIdGfFo+HAHefG?(5c3Y^*/!Y-rHJuQYq-rdf&
%<+.I\h.oo.Y$<E8]gKgVhZCIS7&Y,?s,Kc!0"N>C+oq@I\FhLH-@O-,e[me.eLi/Q-a-t#cE$A=AQe<;>(BK^IPr?W+W<]kiL=sJ
%]tWo<?%-7]Nk_4.%.7TR&^A.J3!)rXNj+NZSI:NGCZ,C=nKk%[AFGT.,6#NW!b6d+L<LK6Au+YagNE_92X3&_)$!7V+P^f90LlC(
%@]O#YQc[\+-=Pg6N#IMs8fgFF>&]+3_PML2Z>r1QPT&s78/,@$fBu6t3@LmhPUj[(9h%H-!m<Be;\ZNILp>0^qDL8LXhZ-r)]iG0
%"jD+D42fTGX!:He8hT[Nmk4Do_;5ZFH^LhHQ=,3)j+_bdlIR7+!V$oB1+US+4pMYm/]Kcu:)?.*l:&E_diuYe+>VsROLm4^1Qtaj
%28eg_+_6o$c-SlT[D:qm)5'gA`#X$UOcBWTYq&k5johDo>cpb%bl#nT16sui^fc=Y%'XWikCQ26/bs'to-47s^*D;'+i2H]&Z4^q
%hX`ZJ^Zndj]+=E<2!?n(U;l?6UuE33foleO'dH-a?j3IM,*ZdoadG-Xlon2AUdjoQW`*Q!&VsYkl-NbpcM"Q=We1<e\21.+`GTGE
%2kBRUjp+dd$\@qjU&*+p$6bs7g'ar0nVDeZLa4O**VnQKRVCfQ,A`0W@LOY?B*=t`i0)X"cBLqAACN@T'E8V]D8Krf=29K[E^E6u
%JC[;;%Q5>*=;bA!(RML5P`6m;doAXD:Sq=SFQLX%83:A'CibqgCE\A(QMm\HbYbab'8\md8tqG=PgH32=N#$j.!JOCBEZ+/WsG[S
%H]rn2!UkZ+T'lXm>J:tX8jJ&7)(2`kfh!)j#@ZX_^)-5JVVfTLQin3JRrOWRQ'J/!RkH#lr:SkIa'XFJakfEEK]#O.3,bIH2."$f
%+6QP*hnTSN\1Qh7]8JJm7SN7(nL/sk`=3g2!-DMZ`eCcUH5O`\aBVcZ=/GMj]70t$K0Tj?QoeGu&e_?Y+>0!P_O>1%OIn*Vq]9ii
%RNGQgOXA.p;1l;ZDB<uC-t/Idn6S;f=2+^VI$4#"RB*CfV47Mb%F=3!Q8YZ\L3h,Z>s^c14O%_'622!,29d6[FAeJ/`SY^V;/-jD
%c:@<<&*5>_JhUJrlfpknL#P9laFdc'd$F4hYb6M?0Jmi5],d(YoaV!3B_[g))!o$GV-u&Xl;B/=WT@^V.^c?;_A'D3]PD00(nh.;
%JNr#AGo.WgKIfa5EC5LbVQj5^4=<XpX/f&QP9k@N5p5M#A??4R@<Z(Q(<K=NBX*_<P71*VX2G6_e<6Z5J9/d`\F&u!/g[tN;4iuL
%Q;ce5m4F!?f_A`pMh%M-/rBd/C.=]=D6&W/UMG"aH?CWooNY]6Z5V4O*E(85o%%#q<_R*5f]A4:cnG20@,mSUG_;Ak8%05PkF'b%
%d6ae>Uq4YnRZ#-FIK9kG]S4@BQ-GK&h=smNp=No*A**JpV,uI&3J\JP^6J#`iac4-`))dVc5aKui9!iW+LqJG*+FfI,"et@lrdo[
%8a%qjRFO44L)X;GOPsT+TKSBbXd\A2,K\hH4'8K%"V3:9V04u:Bo"U%J.,A.,'tUeAU`q8o1S1&dKO=g!P%\ZN7HOQM'@S(mD74U
%'*#6N^i,]2k\t7Wp-Vi;4H-lI+PEYZ4Q:E8q(K&%Sk&q\`P@9B>#>JH$9kg9bIj>K6S4-W_H$m[Q"eXQhrHlnQs&i"(gtle$[AQa
%OE>`>VUPg5]a5LprnbV!7^SFl]pa/%X@30KALK_Ihg&*'4bR%_%S@j*^oEeI-..>bD>!JP<eZMZPZtq<nKdiFKfEg77I$Om)utV'
%NY'iS-`8+eq'LcjhhA<#A>t*.?&h`p7Zm-QQ)X8!l`U-Dr\B-C3@o/&YVPQ#Bi;Et$6Cgeg/t^'>,>Tom`jGeNIZOF))4HW:N?R[
%.9<O-,,M>#RtWYd$hpCsL=`+focZn$#nqjYm/-jm\[;m^hC\4-O>i;8om0OQJ1@NK!/P(WMPV:UaG8`P4aY"UI@V<"2NHMpJMP56
%W(>[%C7^d#D^5Am"_Q`GWjUA(<F%d=.$fm>XjK"deY82ekN6^9eCb1Ui/C2_[2Xt<J]a<^SQNUQ,Sd$2q%["`LYD+Y#[9j'Pbd@B
%hs/dr"7Ofrq.JL>%B;mS/^`9'X/2K#b:5UI!&^3W@2W<$%jFjC+6Z&!8HUFl5^&Y=Lj#^o3T*`/-A6rsoG(34X3X?*R1CL"h<*,R
%]!t:rTGn4INO354_`1$B/opa3q-]YI!>8ldgL51mUi=J$W.>bcMTc+(+J-,D?;M,.X\BQ"Cqlbf/%:ca!Zf6=A6We^2Y>;O"oDNg
%"I+9kiR%tXS@UW7ArY:A>qb<N'Do]8>fHTIpc`>SG6t;X95,^AFoqL!XX\ii@BPX@eDO!.LM?_V;6"5]TsE,;0ATO]A(<iH%DCDK
%16h=8I*41.!m"8;(Q+cUl:kG^B".uo-l\>trYe5d^:5bJ:Br;ZMbq;[F#T.mn0)H]B#LF%MSr*!3cD.5C1P\i.btXjiB(Ln(e4JV
%$lrrs%<oC]W7QeJH`^o8'[s/1ASDXqX]%ndc%^)@RK\8k)iSPiK1N88kp'Tf0ZN2_@ud%'(^=7$&]X`R&QBV!qbILBbP*&\/Is7/
%5ph;ta-/M#9^,IY0D.*8"2K1;r)'$Ap#gKXA4QA,X)R.fFbT6k6gG"JB?Td7%?G+e;=7O>/r9H$dDQs,(8%\/VpLh+A<nK*=llL\
%R[N:oJriCmT1Y/gi:?-URc*M(HXK'D54bDE.KS`E=31qp-^@I6,Bjcu"*p%c1iLK?%kW-]=la%l>`:GH,>[<u8,97e,%pnr^JZ@/
%Ua/#g'*_<Y6'oY-!-.:Z,DgKmlKhDLZ,)`+W/"KO?:26CZb'ucAAa/*%SbeW(G&Ua,96+O#[qS2BA6rbo7O#AH#@=b;93(a3o]L&
%V1<0h#7O)%-jO-fAEC+)Fl_YYCXu`Wc"t9M(MR^W9nodcn<+#:ia"C7Ok:8P\]R00FbO35j="M]\cWKW*Nm:FJ.HKJ<34'i=tC3+
%<>66B`HsO]a@69Si`G@H36&mRZJ&]qaN(P_ajY+D,IFCU"MTpW*&<>+EL%%R[T81:T'fMPCIh+UX.0ajL)`t\@dp`?23kuuZ:1Et
%&qS*.cF6E&YRXr69u%7aK$(=lM?_1)L2qRmp1gV(;Le5f<+;$H2%1t`O<DEZ)2cIP^Goi2XCk#-B)j2*>QV=>=s[]HrOr<F]&\$r
%4%,-Mj)c@i$hn8#k9_)>=\M[T4seo4n:HD";hgg))9b<51!a(d6Gc#GS#[bR3eYOF`lmoUAW)'g&oA_Lb:V^pF9P4:W?_grh0oGN
%lM>_pV=qnsV4n_:%eV`2mGaoMnu)/Hg456N2o[3A[T8AVds?dl3L&H%O:*$p1<k,u-K%#Jh<%Xs3sS*Y@VjI70O[rSX[9@ahDs]-
%8U^rUOF`Ls+YO":3B77%48W1!(9`gai:9:E2)*n.UoDWo)3"i'6pnP2QT8C`\9ZOBA_%8$?aECV]0I)W;tZ7cb4FTm\FD[HPo690
%>^l]b*_B+:;$diFr0Z3T.dXc,_\BY/h;OZJ;nWjPP%mV6jXn[5Yk$UZ,[HILF.9QHMh6++G*C%oOS1"30SSc-W/^!\LAfc*MPboq
%SO+l?O[s/TZg/R>3O?dc#%isAVT&:h%Ze+rE-U#T1JUo,HuFnB=e*8`;+hei?##@/W?McgU*Es;=`FF_>Pht7'sfN:EqdnZDAOm=
%a$&hTc38XFCO17220;;OY,PtRLS`H0`+)[?$&$T6%@;3DKi?V)*pSaHSCUe/o`?2&"=bP?cjUlO-<<&bDJTHC$V3HM,',*Q1/'rf
%e?%uj=aP=KT2O1B26Yr-Ebqa"AEPmOp5ti<qa0RDc75ZHNl.c<:c:2)GmVD#6+qoSdn)a''Akt7edQHVVTEu"au?0&RLBf'FlIWI
%HU'7r1/6A+`KtuTQeTI\BS\:aR;JgVDn5h66Hl/u"$b<XQ.L07bJ95Qag<P5\/D9sM\Y0Y*Jd"Gb6"Q0DqH,K,[^m:#eZUKHEJ,p
%Z7Q,XA\*F\@E=B-IC<om8IK<\-')/`7f)hUAM4j-a?@fiZN<k,`LJ>,)Ub-1L%r,gdo-T[rf=X+*#6r#&t6]hd1dW2oP.,:ITSOi
%JW/)Y_t4oM/^@s55>A[K_\RnfUgohbNp(6Jk;3jC*aq!i4e?5g?3s[CiRom7hT'/sf(-mZ!,(&ED+l[gC=jM\-]@mbJr([gV68d4
%#q)$Xb8:05gAeb_([3Q9Xl26-T:7i/LSWb+pcA`m##Pc,OPbp-J]U6o?NBb?'Fl6Po(4bV.\'qJ,AAcb?%nmleYmaQ),IG2gf^>;
%ZhR42l&aU?qU_MYHX)dMME8rJM_n<c@*BNW:)%Rm`og<.*XTetN+0adY,t*X>gZi!HZ9a'eHRbS)rVLf>8sj>H8"n2Ll+>PI;dF/
%,i<-(go3\UN!hp#G94g4[9Weme=US.?>F)pd3d=C$$WQa>Os&tRKs+?amP]?BXYI;ej[EZ`ft'E#m&cahCnU.AX(LW3]*goMV)TP
%3L2^!h"'gBiIDpg+'H_<%Pg<%j,L8`&?=</]*n(,!`*sf0d(hQTWISJ%g&pNq)":6/D709:d`9d\V[TX-k_V/,"tsF9Z:"FFu5Gd
%6g(7J"#n_I4(Rnp#`hAW[:lm'NQM<J8WAP18=qg1p,@M-0#2(o>#2(9P]s>6%')1+T,5"1eJ0#V=Iqio+[Vb;:OJA2>I(M]VGT_A
%dl,=>8jdnR0-q7>?8nI[\YC2@'_?,NX/j)@.&rSNk]GM[@S+XWOV6q>G+Qbb(h@3\6g1ZC&pp_]nRF7[1=oD+dBg2KnY"M"[Alf0
%p9_n^\fMZ:9Jt;O@)`i<c=Qd<Fn+LT^t)_`R2a#SO[eP=_B.gY5\c-;AWrVT#p:<[V,*GGL3ZprPp?$7hm^JA25V]b^PN)[jhei4
%-XckC$rm'<6PKYg-IFMGVW&YQcrnG(gtq1n*Td`I-hk\HZ(g[Y;6Y"6a[8LYWKXFI(<UkV-?%SQ`MfTK6=PZX"nra"M8\bOb,A97
%5?OQ2JcUbJ5@b;Z9Np!T:+ZYmmq,+cVT*6][-@jR!?=cm0>+Y.h?QII2*rtDf+V*:[2Z^3rCI7!or-]RRr!iZ7.(?^k[H!S^+_I2
%2Dj`_#ZDuNe>$,tLJ9a9n(nb]/CJn4js/B?!Q'E3JG_k<\%%a[pack98-l"(,oQ_/RMouGg<:m6-(r4FA8nEm$mJ`MIrEZ>"Gnsg
%]fgkKp+Z7Gnp+DSF!Z03,XsPS0ObgLXe)@u;W0Mh[GB!<45`]V?>X'm'Y-(!n8W`2T_[8o!mA<hpHik'BToJO%YRs7YaKa]mWN]C
%.]hl.mRWZ!#uV2;c/Ia;Fu5a/H9XFil]72j1/7sDZ#iZmY2qD.iG7?-7dh0qm8KrrZh8B[QWeu,F,9uBW3g<3pN%BdE%h@PF?(\&
%7B1IZ]<deWK'!$AQZ>Nsq(Mn?DG.">ptEP'DFZVSk*\L9pgW9t_sJJf.`?)'a`R07ft5b'okP'1b(@6@/r3Pf$9F5p)l0D4<h%jg
%<c6/!n6:>arQM2]Y*195X5dABj^S6%-5?nSEcWsrciQXn&lY^k=\hM:*!(_q&66FB:"d06Q"M/gZ3D-/9R6q_a4>+ENIB99;CdZ!
%j#;De:e0Ps84d+][(^!O-2?,3IOl24c=(V[AaX/,=!8D3KLpk<`56Eq-^P:.->$VZ`V[i@$o;KGhl-.YGH?dK7&[ng%"s_AIEuA^
%n6LK`oAH8dR"7`$3ps5"5sN,&=#S]u!lbeEDsEi-(BO)JlGN]d+Gs;q5^d)%WRnd5d>hb$G]\4;O&h\ZVjTefMj`W4Vc<qg8^OWB
%$t$IIVOL,b2e@<SoXLq-l9#-bCamGqIJQ%jAJm^XW:_8Ikqc.72cgK'[^4BAO8B4P2p8Q+1ZC>\&8GGM:;?_XBKH*6P3i>)+`&2#
%itD!^E8/E3`6sSQ6JQu.3Q>*QbrhLY5]6dI*sM)T(,e(_6#1U&6BHf,Vf++s(&reT\qRsdFA16WTUf3P2&M[,LsJ<a&7UhhOn(I5
%C_77N2r"ba)7rI-Ls+sK`7OSYHdQ9k;3f1b*%+7<n7*j?X7Fto_P).chYZM=l=sA4P@O9[A&[7*"pH0[(^q%R4hF]u#5q+Y'7-P<
%I'\X`pUs[LS4c<s&4_Oq^b*(\E(`Y8h:E60JXJnM.mRO"<9P#iEKYL?>0(.Y$MSpGU!)OS,`!qqH4I2''%86a9dRNqh?4Z&P=.?(
%56N`F7GNgDO1P#>Wkt;VW-5&=(^:V%O!)#l?OWNVX?8T.f@;M\j[!`!L\6m*A\ZgMD?MXsF^Tl.'m,-gUooi1R'[hZ"_&9UGL1Ma
%b11q7piU?K66O>=Y-o[T5/l8ZTJoR58/Lr]U\,TfBf&70o6,qW,TBKt!n>/\)I!52#D9(OU1+2[.^WepK]^QLX!u68:'FmF7"#+V
%b1O%`")Rqf=F]h;7Mj!%LcE;30!_OhfjehH_?j\af%ck]b]N&p\E&W'9PR#LO(bf7bF(18^@#t;B;7&&^1]qpjT=Los*i%M7[F\Z
%$YdF&8>/>k/W%LH).>t'gN.)9pba3YB`fg$!!IB*4&g0CgtGPKl\ms)47BgV5h$nd$&'s91n]Sn?GsM.;n4pD&V-F)M@_(-K_ei7
%P%]9.7=^IN+!+f[5<+=PPT%js&DrF(XB>-]\kog[C,aQM>]D!Qm+<5Un%^Xe24d/KYcM5Z-J9d02F!a/.6gHm870'[M6(!Q=r\bj
%OG^'Wn6"i5mO&k,:&9td%6)AGa!Pr(`$DW(8KGTuFbG+krAro\bT*[f<q-HH9hOc/2^KIBL>*%+&Z0LdjN>FVW+N=89PJl"B\^38
%1s%\nJ2T/tk/P,$MNdDcDQ]XAoCcqH";FU?n-<lRT.'R-%Y>9=M4cc8HS\Fg1c!)s89#Bq_V$H8gP",\c`.$TW5S=4-.TiGZ.<!h
%E:!#J06e;s3d'a3rK87GE)S#^C:+;?o&\e1.^t]-L(7f%+=W_t4p[h_W[Mt41ih.1Sr5/Lm3i?&)t1#i(lN"@^N+J>(.rRl&GJNq
%WRCL][mMr:Nr=].f%nb^&6s6'n8KNlQE@'EM's]u@s]sF#CM>O&5tYp9"/oW:8]*C4)p++>V'F(Y`(m'&O`E#q,uG=LImh&@ITek
%UXt=*ph0e-GOgra+p[H`4ZkZ?`rJg=#SWW/Kt;()CZ7X"IOBAOot(Ee:<@V)'.kEVZ(?OABVlm5in&QJ#*)Rg"p[:]qUBb>35DXa
%qXeJ@>]tSL_?'DUc;KP13(L/B7VR"&'_W`R^>07.UKWC;A0WT9diE0M)AGe+a!DT@g`D+j4-P@N>4]1Q@YiHi_"c7D-L9"\OaN.G
%>lAqKneD2U]`%_#J3E[[ep0bUoERC0W4(E,2Ict?>g_1"p]A>gYnU%QpF#p/UEYGd?g0ml?<pK.9EA2+l5a)7/q;DkEJn19OJ;HT
%\)Oi\g[J(9!=%php7L+nQeHHG/16:@k0#%QW6<E(1)In4rVUkJ?'IMM/45'<d;I6f=/_JHJ2H]lM"3q?2ou9%^6i">D1k*bkJhou
%+DeM'0Y>b=L$k809t4r--W<1NRp\#JWE?I"lh]<>WiBdVr13`i,PZ9Y<8FTo[sKk)49lgVAjO[0M1?Ip"__XlL)b7k0'buQZ9BIi
%.CbW/(94OF7mksUbL(Qb`+]DiTXP])IR)Oc$mu6h(B_K]]Nk?=9[U$+)c:JkOGDKBjLtZkUo9([*<_6Rh.YiX/e.G27ia2]BnGhh
%C3pfGIHgAHm4MZOAK@I'VV]E-Tn\qj[jgc`I1CCl9)&[oYR1ZN(#a*m'FHohhNg.K;=2;X*rV>&=b*'5i[^r-4@1u:&<-E=[(puB
%\aVCG!TgKOAT3*hpUh$FI)=3(`i/9+YsURgrOf*]A>'>+.n+4gMq&ZMI@KoCJ/!!e:;E6$r'"7H`.rX>ePhH67'i?C$&i)C"TO)*
%$,=taRdjob4j\i9e\h)+duA<YmTX_8MYq#?>@S9VZ_%8=k*\>PJfZ5F(.QQ$l_Yha!GHE.!R`O%O)ULH#%\F5Zgn0rVZ&mILH1qj
%(TNA2`HB5i]>5V:E9;+?EuD-k@#4?jFkMQ80"Q9/,bu_Z+mJ5;Zk7q!]3BV$DI37PX6>"`E)[4+G8$)+ULsH:*?EYt#bQb`I_%<)
%YDh#q:Eq_h\9.]q<hg(-p.o-TC7mfgj+#H@j[%W#e9?k4s'e6Mr.WbCs7_NFhqghK-hKtEJ9kcObMRr($QUo[6T5"[9bhZPlC!D+
%!-*e0Bpge2I'Pm8:0Fs:$_aSe/ZpDJ?j=6qG#3poqG"d))7A?U5^-^aOAU-h1<<@d,#L4b[.?Q8*sd,E+8H,*<$]9UBP/8\jfW8*
%o"r:,>U<#*!L_+ImCe]%0!Z\/.0m2f+UPC@,/)"eH53f1V-iUW+Up;6S]LYln&)\[eKb3>iZI!8NHWp\B(a`KF&)cbKM93hcVaKe
%f"19UYNskHRG2#+8Op>fL!0;da*AE_ZYFr)33)6^!T>FU*,,(m#>3qL^X!2(&u`OSgK/8:#*MdlY96eg*!f,:q'!E#`OlTEMq(a_
%?E+E0`f3KA8`V&#a&$N3PPTYHRCYMs0^+FeBE)g%kI->s*\Y4D0>K'5S&!d=@Ofgk''7A1E,@!XLWS`$>)9!hdIgdJWPP,t8l]_B
%or(]:p=T?sAO[_V4fgkkH$NG2MB4/u<em5%'oYPuTmP3W*H?P\&>Rs5W,DJ7P#F7o^q%`Xp*;'68o\6b^[Rf&La@X]^[:<AeQW3o
%W/o-2*d^T"RQeXqmTg=q67r<HXY?KN5^J6_8egENT)i'CDgB&-VjTnd.<Y-j-Msf@?1.enDeEnkDN6SlKPG]A'iA-;l`POq:hI)U
%Z1u;TaoTX`Mj"Q=Aa6,QW5\:0>jc"s-IP.;ej>7J'jse=hFdTD0<5;mW*8PAWc7(D;8JCfZ:VjIS"E`^H)=o2ZcbMCO\^%t/*l#]
%G8YEhDi/oj$e>cH(BA*'Y7fRP#_`=YVhlAT4`]+%+AF)H)?-!aEfc48'gf^*1iHL5$LT:G!K?G;&QppYXs=,[fIW//2_iG<Y?;O;
%$nVU.jVe!GT7Z^<Z>.W9EV_`?:S;u<og="^"S8r8L`c7)flQ=40lg2]">g^q8%d@;M#1WU)cF5I]2E1CWhEb:&5NVQ`FKGLAkB^[
%i/\m@i5;"eO`\-\fK2FW-C;bgXf*UNVM_1RC#]8Y04/ae@cB#d/n)Vqq$1Gu9`Q?+[D_q8=[JnN>CqG7N(46ApLY!$O"6WiJts;>
%%;`6R\;Lfn)]cQ<&S>Fk]Z5B+=s_=-01R')$jT$9.<<!hG3V9>\]O,96jSQ-:]LnPrFL2OK.FQN8',"75:E@GS#caA\W/[A:&Xh0
%*N(#u3FM;qb1kaYZUC14X99%[:fZnC1;@$O%J1GS3%8)fbeUbqJhMWokhh(,Ctj%4bp7m+PaF.uWMuqO_?)5V>b`t^ni<i=8ocZ8
%1gZ2A7:XBB_IQeT[V#_#,*cER!Q'5V'dam*'Io?0p((M,j?Hp8i1K+G!nFh\._0Ij';$*\%n<$J,\'E'K$K.q%uLs*E*\WL#n@HR
%O<3^^H83sl"$@upfgR/H&S]&:1=A8Ym=@m_m3PCHX"/BMP[/qN<M-k"*Z?`oC"G\m`n;72KGjY+e+]kF'TPU\s1B="<<OeHW?,aa
%r9&kL-F)PH>><T_k$&s<8lo)R(0sdg/I8tH%ZbCBJG0`X%q[*Gja)VTjUL8"9dU"NHu5NRQQ+Qd'iU:F7_Nm7Fof7eC31-Y58_2h
%`KWcbH8kE[\$9<^;9Z"m0MBWX)S=7`+;[rpj_)G[#^J./^T,o9K2iqUOe7P4k$&:a\CK4Fj$WaXgR?ROSFI50iXJ@-06p?Rm^(qW
%FjZFuAodD60;6_6gnZgFcS[Z9?p"7b(F)W3No81llpL9%VCS)SbQ-dh[jgY3f2G_R>X;:9_kj@HYF(2g!').h3BNsU]Hf5UH%[1X
%R9=b8D]6#3")>Q[]2s'-#'\MEh2C$-GWWK*3ll)+FE8;!Yi$QUX1oWfT91$D]Q'g6d_Wb>jV)!&lN#ME*do-?%R-e!S3lZ0Po]>U
%n19g"@tKp$X#G+&QPp`FTb?+7rClWFl45l`Hrs*uW,.cF;/<`g]5Y#gC%2qZ\>%iZAc((u1OgWt=4Pa$,W2Fu0(beB_'D6Wedip*
%IX=ME+pj7J_jo93pr'6eR,BfLD/%H3Z-,2!qf$&%!nFG-#k.!:YMAXK#h+Y<NR4r*FM!(eN-#(C:DE)V=@_IX:t8LPL+(2A#[uHn
%f5am#TbjDsf+qJ\b)">4rf+Ul:k\/8I%qZ?W#-;nCmeW5o5b]S1RPG#"'@@0*BZ`IGq;i(hi9V!d$F&2-'5)3Nl,DNJSFj;C)84f
%9n7=ig/(L]qg):DkXa]dluIf7RQrrW"0SFr+5#Rfnj:3kWnu)*U*Z!QZM[oneU:hUfA.>>;mJb>[D1?-BDB.K%Tk`[&CLF0Qi!7T
%n5MVm2ebG]-NP0(c"`r`2A$T>rWtZm.&@<sX"*CQ!(UQidk$D[m"$tZlP^ZiDFi%<?ufs6No2YeKO($JddReFdd?W3dXH74%93eN
%M6MKO[9i,#Xj+4U+9H,aE`gBA)%jVr(<aXc9$g`(8U;lB:Q$7:BUWgJmUJVZCls9AFSff^'Q:gTJePDugEXrkk<uiPBfc[=(o`oK
%dPf4K('-4`,->dms*?G0nO!`CjpF)KCA5GoG`G+2a%PX9XZY.;,CFOcIlco*IRRBL.##G*UsX\(e+q9@FZUJ8]DqrF+%%QGQ431G
%iSEdp/Tq@]]6-qAP=Qo=Q=@JkI@BYgNPV^7\e.PrWM)/54-@$$1[e2F9T>Ue&*9*oY]Im/8H&b"N=p;;<DC)@6>[E*[36&mVUH&>
%34V6V7*]V:c!(/l"4Ka94-><&5OPmU(*sV/<LQ((RD'l,<#>@$juBUd2&CGg/QAK!T[EE/`O/aJJQTar8L?@LrPTLb2p$a*)R(OH
%LF'=4=1;o5`):f5nEW9VH]><^Aru-!I*^m6Jr6/DJNu1-QNj3+ZTCV^@S=@#;%HRqn+e25B$].2UL(aX5T"Y$@#8Hjh0p\u/K*B7
%Xr;@S>%Prq.5-SmF&p7$,N7Kkih1Samo$\4WE+Y)M/TG\Dd[tchfj4R#&D9XK]+iBXJkAt4XPAoOir3tg(\rQiQ7(MD:"MF?)Z5H
%Kc,r'mX=GK2L!MZT#`D'_iX'Nq(bGV]\*mR5orM_"RrZNKZ@"Q,]RB5cSm9R+=oh:Zq)4FHk]l*%ACm`\lk%\(o&-n-qk_'+jo[i
%g5[BnEKSbDV>=H/I-!g\d&!J%7A!:X7R'N%`V[sOYqCG,YeKnPd,p*@L2(c[;D'd?+YTKN8[NL^h%s:RG-s7\26*Y<$o;Xa'Ko7>
%=1d8][8=DOnHb2I/j5D*h6")3;ZfiULWjgZJ9kFkdN1<Q7TSt]jLT[rj&5Mi75;WWY)7QW_$M#V1VBD":[R&ej[F#Jf^Ml1)DL/p
%'(-iMS%$^c$K)SeXs0_<IE9q_lA4\.9;X>9R)lqi4usS,9&Zk6-(-;EV'\G/7nn<,:m)U*dX;013hnX_3B7[k+`q(E<9[U1fca:J
%2h#VRK/Euq8@?_H/tmPFYkeLmS!CIMV"3jfdXfkoR4EBRZmSAb#)!F8n:R622A3$^eTB\S$&-Bp&D,n^Jc1'E#GCdGOK0*0jMo&4
%eoEZ5giZsjB\K>o<7L=B7X(p#APN93T@BmBeO6WF-3Nsu05&W=/&REBQ$W"bA5i3;/,EgdZc\cS:3FU\)+@*MenQ!m-Js/$&;*fm
%k(m+HccYH86S-gZ_=A,EEVme.c(GTjkrl`mVk.u%r+5TfNK'MCgFI9\dZ%2!5)0G_lj0HOTJ-L-B+(r?aaH.G5aT,rKMj6@'%_7'
%bo]9b[.HmheeVbWD43fVCHkt/e'004i==("+k0XDdMX1_e-40u\.ljCVK\tR^i`/g2'1%`ZRDVuTopB=qH.`dj];I8i1^.!9K2>W
%VK3+D/U!WAm\W"G.-s:sfCs]Sdg5ZV5i=[=``j+I+k0g*Q@r0G$U*["KGqL_&]e\4#UtT#&@`][#2P:UhT;jJZt*_@CrCtT26k.Q
%MU*bYZe`T,%B-NaMWg7_=EtLe8T$2$9h0UjM-7CT=eS?>$%cn@;&8BC'S?3H""hiLBo)ojXYtN^KUGRNaEug->FA>(PWr"GMQ:Lb
%PI`n-YnFb/Z<NbPfi#d4PQcB`hVQ'IW7Du?8d2!9Vaml$bMOjUbs02@/U)kIUJnVji"V&:Pf<&bQTEuSl!"KnZl["QQu\RbT=]Pi
%\gmo\AkZnl<#r46Pr(hf0m6DL*H\;0BDk#QkUO<I,)1R"^nPCQQT$jN"13hV[SW5C?c(*e=jY`Xp^5r"iXb4t]qI*++pc]CaQp_9
%'1;h5K!?a0`-eS>fY8DIekLf7iXY#MdhJ'mTB1IRRfhN[EYeGb'u30L9-'_"kYbs5+SQnc=@DCdfn`%r.dZ@3bh'Y.e`b0V\Iu6,
%7;4?&N>I;[Z--nAYZWRBS!_,XHpNg@m<M^)Vj+k[nPj37;uk]>AA<J[Vsam#YVZ64eE&BP\rQEA]3'Fec!4PUnTuJkVg;EsAV,&U
%DrRL$-AqaRqqZ@E_0CWA'KnUKk_lrEo)O9b$pq%R$;rp0Jm8.U8<:8"4StP1;Ic]bgm_PAor'5K8AE!`Y3_a*@2/Kk:_^H9j;\kP
%3>c33o=+l*:"<-mLE[XI!%7)/<@GJA!Gm\(k#TW$/g)C+Vp*mB+\/KM4ff>!<O9_L'i'6*JaJdp1#eZ&$rEZK61;VV@Af%q;SA<D
%it*&1'XR$"RcAf&RM0nup.(e:;NJ(/_M=K\Q$IB%U5a[uMcs?Yl-gs5!j.8[*S58(SBMt9G8jPFMZTgaQLVk`1c<e$r8`!-NqJm/
%aha]`&\&I^8T(!ONYZZs"h7#W>5<4`8L*VC#pDmi9Ht=c]SkTW[&(@$NajPr[M*eY#ABIjpI60,%g";8)VSMhFW$-"m#F"Zdu*`&
%)3mi8]u]"EI7YZ.e!kYs\:(:7:S=sr\l99J]'"i*/&N1-.A[fA<a#peT3H"<jFU$cX7%qN#"`_D:)K>M6a\2&4=sm,glLMD@+oiE
%'B[%O-=eBON4'38Jr"?D]+9SeR"CO6X7H-TkVj/GJeN0=)M-LF+floDn.SOkdoj@k<u?:t=3C$idfP":oD&l4pF$%9)ZWYSH8c5?
%gZE(ShQhsoe-\k4L]KB3JnifFb2iZrHX'TQ7#b?%MQ>X^lHPJq7`GIYN2CkP)UAgN#hB9SXB#]s^g"EB3+4s\dssr\0_Aj_X<OW%
%6@TNB86W$IhAr9A&Dg_i$-"9KMN"aO2*(Qq''GE6V-#]^kWo,-H29t=^O?c_EQ,`Ym4SW&9ehW,h1@oS))P6t.>$]sT@#S=AU[3E
%IUjj'O9aQ=k'u_%91##^c78H-9^"X6&%Uj3^^ms[,k2K&*R#F&E8hfB`jm>`,+q&82q7-o)+"G@SY@-jM!6$m6+BE%d8=sg[Hl9c
%N)qVI'o%,\6P^"u_6!DTk0Yd`k#\M1C`7K#=p`bFn^p2'dKg^QQ@c^+I??S;&u55h2_O1PdX)WXRjU1DCS"pQZAD'kd+NGJ[`.)@
%pLK@D^aPS_C![K=WW+&V'*9A^eQ^?QUo*i#LTgM4ZL0aP3aee?Nri#EEi;f<gn+L]0Y9;+6n\Aq&VRD?mEirVJ5(gOfW=Q$co4jW
%5^j[DD-Lrn]!5<E`]6q:#7W$-C<c-T]L2NAoR]MnguN@285\+&`[BZk3a]EN6tCQ)``c:@Nup`,>.S8H$+9D+YAk9P1ul/k[%cDG
%?(SH)5gQ^P(N+ZnN"^E#@fRc$OO0X*KF$;G,:jWalt#c;To:=cD8@G((d"KFE2N?AoI_\%@qUbMg`ssLIW_X)nLrnnCH%UL'AWg6
%\9b$/lWN.^p.6>lGeN`YZj)F?c_ueJ,B'.lnrZ8MM'HPuj,4CQ8hGkAX!h!^JEXHD@K_AljC^-G-D91u=I5>>^1oH$,&&"'D(k.@
%33R+V+EC2ph7Dd(SJP"W4R]`!"j,[NS,4KDk)K^7Yfj&9l_(;`(h&Vj>9@;i9F;/QYQV\O[7<TL/*nG'*t;'EBkLA1.)@<L[**`6
%h]Pb-YFt806),3OD.1C!R4jUJI.g,J%XB:#0X;V&g'q,3\r&:?%RK'kg5VlEbT_>IT.N&-I7<oNoa6EEKTHndGGhJcLRcsgS?Ud6
%9m<i?'odajn/e*bHl:EWVa#ieeRj7;3>cL$88leP@YfOT-oU46c3hZ^'jJke:[l5rcA:Ah[1!]>3E4#;H?"TYnZinmFiR45_",](
%JgbOdCq!R\kEQ3HPa<,]1D(Nm29@GU&YUr:!K6d4;.ks&NVi4qEI%<)Kf9J7N^EZT<BM`'qGqA`,^OJB0*22\S"#^3la)PV;>dYs
%X2pJ;mD7$:q;#B]f:IdbCV(iR89;lu@+u@b&>D7m)m;NAek836U2qMlHCALp!,L5G6o$DPa#>Z*b2i,0\*4,(I(T26F4U4D:!6g-
%k[#8FW\=cn%47W/64(9/3V+I=\^COP@p)gT]k1*d*T3;H:%C1pFjG#nPpS]L=3`Zm3lWIh_$GZ:ei);L5n>>o3/Dr5(6Y.l@?Ipl
%73Xbal!48h=OO>)AhR=s7`N@.V?UiB$J$3:ms[5i])\%5YJ#2mYt8Dp6!b_RpX3H`mm%;>jP@f=8X52Hee0Xu0RRX;c?KK%43#?0
%+!:"i(C>e_eV"fU`>Zks9I*QrPUi`.EqZS9-u+:2h*C:n6C-JTKGp6KAeo?&13E^2g1a]uR4Up]^@lC,+c8q/UR.?@EM"b0?0A%R
%_$um.6!q4a/jSUBR02VN;Fr[5nd9S;?2:U$Bg]S>cMu<3c=f-=J4FdLqlOZoKquDHAoJP]&9ugs6GdGTQh!&j9EuEjWJ3g>K5aV_
%,,N/3=^tp7l9Zto3jHOMKLjQ:1Bj^cGGag6qT'uN)^)bXd"U%#:3oK<oJ#A'5+M<V'ph8^M;J+WK3J<e[c7tu3AgQ(3qbYT3ptNb
%U-CWZa=7$(nh,qS<'+\p/'Gt;6JL5P9"<uq-,&djb-.Moos@7#YKQpO;R"n52*n,/p>\Gj9>ln?bcYkp*amSI#uaY:W)#DJ?(^8;
%81cF"r"'ZSU!WMk"&eQE6G_q19\4QN#0>d1>5S;dMBF(4.Sj?ENNt+=B7P6[L?NaZ-9`r*J2";1APYV"Zu-m4-BWjo%cHVkPU3ES
%_:2KSNHK'nhgc,>N,Zt/Z+_jBVJl`>;Od9<5h\d8kLJRRZbrHsmJ^A;.]k-,\'MP3=,qKt?WUN3:5Y+uaC\F+%$S`dP8u3n*@T@:
%+dBGA+b;W)Y#R,Ub"]n^'38,Nj=VAp<1K)fEI4_'N#&N9@Xhu6JJh2[.Y[9d_GS4bKu:Fo]f@VeLX,nXb-f(5bd=s/HE#0%KP?2q
%3%;:l83b#uYAuSV>et&Bi?b.H($9-/AVg+%%p=>:A]+`?E*`Z6e.SSok=(sfjP>4mqhg8?'Sa6#"m9]K0o7d"'3muD%+_sq,;RYJ
%N)86qUUB<HLjG`.'QKVJ#4adk7rZ!q?He)IfOlBa!UF")%M\$Jml"#dG"DaM]Gemp/K66g)GW6)[t2<f'A68;U!gIk6*7s/4./P$
%8h)ZGKF3o@QusC!lt4n6,OG'Nj%2N<NLV4/8\P95Euu0d-teBna9)F?8WM]RL(W<62O=]`d9n6Wc;Qe.mnpWs]`=`<.ffCD,0L+u
%Ws[^oeVc3gZoKS9251QpMjt&7H+A$`ng]ftFU:.q-0XdpKb6ZF>_S8mfH9Me@^(R!c2/.QLg9E314jFQNI)3Fa^P<;s)$;gZ+Gl2
%SQn,tTctj$+m"'1U$H5`T%r\HGjQOkkRmQh^^eb_Mre*.e/%U,Ag.*AroDa\4,ZDW`dZSH+79-rmQA`<\)W8FSAY/MO+-Jo0(3R5
%-./4](im$c@9p0r.+kIhg_WOfgalrnD[:^)A25_%NId.Y'!8c\OZf%1+)[bhQtDMJqM_)i:h2<OMY:K8(_[tg*d<8])*=-K",MD8
%k1c=6mjnAHj@gabZU;c,pWpGrLa%B?*auu,OZA6N<$>j)qcQuoE-e>l.(hVOh9JF>"*o$lC'?O\IP'puG-PoBI30)^]Ntk=U<t&<
%8u)3?j")q0P[f0oeS:>4Z=<sCRoVX6="NManI?i8d_`c#cnbBORj=o$2.V+sG?!W>=-ufX2"d2ZfP5@kq7/<M$sUK1e"thFV%t</
%Q[<ZNIdJKq*TsD\$=bV;]XII7fR!V-RCD(dWS@jjbSiep$R$cm`-3qdOK"=3SscDVQBeUG$o46>"hbR%2\QLTq<S_&a3S?=Z1?Nt
%GZD0@?s1b!)ac6H*Q[jC>!ork`?]AgOR+N7#9l`_HTsY/=ZT@mX"aSY?%0);W0qM/V#+6c/]:_e9+:su@#UW)i[/@jH`8aS=-d&a
%bGpU^SASnd:J[j1H)rHZamn',IdL>SjEAao!8,KaeW1mJ;PCEfFU"g7UD+iGX#;K$O](B+2,^'Lmi[BoZ\CW.06q&rAJUP>",SjQ
%&)L2H_M]3f]R_KU2f6Sl088XgD.UHr&cakl^KLG$Yo0)T,f#s;p\u/WD<e0p00_jraS\BC8rZAMWVnj)g]iZViKRLJ0g`Xb&1%&3
%.05bmFieT7QY2h"Ln?:%Xg.?_nu1*dAcb)d'a#goib"i[(Cu1o5)DOub:0l*1edB!2:)*)2rI1#b-s9cL.(6U7Ko0l>7VlH(=M16
%I&e0.&#,G8No2L&WLM%Zho:gi+-?TP8VU4B0[&h%Ki:AN!6,dk(XV$'9+ZWVM'Bf%).oEU-m$]@,*VD7?3nk#.*hnU\Ec_NU-F<9
%FjcLiGEJMO+;5+$oL*,YpZM5rTn%Mqq&&kbIYG0l_N:Vj$rD$a0KZDo?dfJ#Xu(a,K1/@5%p,LtkMo5pNcJ1n\/''aFKJB35KLL$
%d7\1Z3%u:qk30(WE\(%/O'*[_qeVH=MZ2S*c(cE:Va=D5I_O(@p4EIk3"ZR,gONH'@u(eFBgA+l#VjTH"l\Q5R:2tc&d+/oQPG<U
%3j<D1pOPp`j<?.0S"_s#+Z9WKQBrSe(6DT6)$@i(Y8"XW=bpWS^6lNbn2@&9S8FA0a0LImK`PHE@T/[lISCG)^$L.2Th2f]$HNh4
%rZU@^BS3su>qLc"DU"X0q"4nU7VJ.7:@d?Ugu2SVI>.8"AK=YCOLL0&-C<-HShomFK7RX$R.]IRk@&iUBZB,_Q",Z!`pdJRht"#j
%PEX%`^d.s0RBhT9]b:2*@sqsMdW4T4[9ed4.5%mG`(5FGW*fh=j8F>Y"L1IlG_P_\.ZY$%?T.P'S,=d$[3Qhok;'7U7#d,g_pq)6
%ONMJe'>GQ!3m&#6!!:"Lh0-krlrHtSZ&6pI6Ii;,aAf-lB!`o7p@>4\5-OmMMo)"VaDAZHmN=>K)rN4*"GBV1+UE#:L#hk3.&XC&
%+cd%c3Eo%3%L[o+Ha;[$[[_7\@?X.4>RP,k2`78EgYU?lbe%i'CgA;c->?tZ3]^9+Yt/PG,g>b&)*ii^>e/pVkTY#4:cL9=R9%lW
%hb*;[RK]mBMb!Yn8+sM]^@j/)7d-pH08;jl(ijVWOl^E`';-hbaQM-_AO-Hd?h>IDcW/Id$Hh6(Ye!q8-sA'_]UQ,9C&BA\-)Gs-
%=L**Q107t<C3!;d7Fm!6_iD7cB$1!/eX\1-.9i/':1YK1`4,!ZJ@=+_PV4hAG4T93W'8,<'P%sd4Auq^r=GUjORO.#45#"[H;nq]
%CM&MfLTk&:687GXlN-Q:@i@ou*YaBq.<2,$kV%=Q7+0^M?h%psl)L0!H0Zf_(VAn5\E*q:r\RXZ>Bm&hH=@*QKJl^GG7HeLYAkZW
%?a#pecSP>E@=A+GO7qbg%4r7L%tfB^#->Wa#jd]kC>8,<a@^8\do[tE<`'.a\.J6fD]opI:2(Ri'Q+/SbBdQIZ>,jT%*T6qj#_kG
%1R^o%,h0g6X@jSW.2/]b_M^ktX4kcNEXN&Ce-PROnZqhao)e!R`!FCu$8s/Ph^-aGq(TpROU[MS:H<)<2XLbp/'H8-*X20!H;ZlX
%=\sY>^@;b''pmEiKi/:V-WbO[=RI]H]4T9&I^j/Uo*kGNGA&2GThtYLTC7N<rC3dX')jOa171i"7cikmCshJuE:_edot=Xt)u2*r
%EM\JQQ#p5BE+EcJ]-A<;9(oo,d(t)P\nt]b%HZk[]!e!ZK83rl;!U0!6^+:R72YhD7M&OgEbs%Eg2UARGR_*?(-RBXjO_*s3QN=$
%&"G8;%Hab:)Fh?GFIg-[3"](7,nMQ+$l[1P2Fp"_:I=GFW0B</_Mfh6Xj>V5j%q9MS1=q0U!8%6jP@58>M^KU0"XI::dIh(2i)Dl
%Kq9`6k)_NeF:]WK/eq289U0].Z,]F,#cW\]0DX"uY)FZX__sf*IA(sXaW)#6fl]L3?c\%Ib5SZU5>a9^BHquM>f>a#9S=-Q+(>5X
%<fgQjP<sjm.5Ya/;6@*WWPo/9dTD$J,Pr-J>D05^!kT"eP5V_qp>J0<Wo6e!mRk<u@E(;5o-INT.kDM-p9W\9-U5dli'HEi3t0!U
%mb3sFZIPP]B+s'^a,cYKS>45mr-U#CjbJK`C2RIE^+klIj-?s'!p%Ac&2(',rZ\/3A;Rc@A:%XO%<<L#(ToR=,G@eF#]k4KGu\cl
%$:tt^7rX"ejCelMTa8[]%$F&*JCRH$Wr.m==c=_R,h"/S7A&Wnm&s#Dn^mJl=(A-9$/mu0!f%k$8J>%X<QLrDAg+iY$)?.f.8>7u
%NVlPAp8TeT_[!fA-:3]`A@uST'OjMu+QZfPe4[i@<FbV4%Vp*Kc^4DWhmDSlm:rn`d+JgH!b@*c$kp+OM\!-\Omj?AFt"+W!pkqh
%""B=1`=Slqm\:CgL"7Y]"@XH);P(X(pm!h1Xo8RX@][Q`Le!=*gL#=J+SG*e)*i%F.)o]liQ><meo'>r19jbC3:<8m%YJGc(-3@C
%?=4[Za#bR?^lbk>W.H^##crSIBF8DYFd1*MfF0/Ue1>qL(Ie^G<\Ga;oa'5N0KI&#Lp'.3d8<H=,r&uq)DN5B><YIMa`j[7jhj]6
%?(lLI11,+IpZi+-U_:D<fJJ7cV2hjQ@b$["j,S*@ot!Js#IX4s^8jj("/0Td.#b!:QtL]=@[GiCHJH-K@Y7?K\[N2#j!B*+Slt,!
%;aiaP_4MlD&)iC1+*0XK-qHl(Q9kG2E4l,IN:-nEabMMh]s$8NhU/AA'nlTOHZ-3r8X<Td^.L#)'?*$Dcq;HGJ6E64oWl:CPs@[(
%/:]:cJ,:,-r6*;,.0A$D"3"!.@/7e?h65_PL1BDN"m5!TheS+5[[G5\24E8`8$]5P+F8g*=-em1>FiWbh4.b)MGb;2/VYts`@f?:
%g:BL<W/OC*o#T^W`a#sSK$[L)9=C>V0t4Y/%"4B.Zj3RsWn[Gk'kbeu0bZb:)tLn\@q;J-IS)]6!bo,u"bN&pr*poYrCciBdTD,2
%Lh9H#/BU&ien<4peZ6NbCUI85+p[V^MXRlt^7.kCHq^B;@6D$Rpm_QUF*?Q6M78X&)LdIq+?dDDD5p,\A<@aXSKL#@O4?CSO7k2r
%*!%m;a-09_PPi9]^0j0;5NU<:@$'h#(^8MoS$>lGeap65I8-79XB_=i`OU13="M@cE)CDco_:59*$or+Hi,0OqD=Z:kZ>d^:Ar28
%qTdCnn1q70%Lr_!A-p7C&mqMQTZj;&"\,Co_)rtU5VeP-ish`I3!'4J!O%\(3M=f5$u#Jbi^)g;FJgl`Li,dEfU\"Cr+kRVaGZS.
%+s\(VWFp0ObL^rk?,N69N%d,33er&*>Arsp=!J]^7VL95TS^,>(?<H68f<Sq-g%N_2f"TaAdT"C(o7usUQA%A#i(4S2?dmlET4/2
%5O<R:3fQWX*rbZcpl*nfr%#C`BJc<bWJCC1bMYI+VV-U=,UHX`$(CcPlq2cEr^8!Y`?N5[48W6A-5<-U@;Muno\`AOKDZ01V%=C:
%J21N)BV8_H9Km1Zo[h#7!0*Kf5COB#M&n5,!i)+`gaA9b?7MVTTBq.Yk.j6W89X/eI#Z-cP2>8QO%fG.5:-C3);@nr8Rk=TaPcT>
%r<Z1c>ZBPU,-)8!'YfTp=_26<dT-fHZWPa6#jo\K$k&#^AJ?(C8Mk`[]HLj$WmVV%T:?j-X6XuO@iuTh=$g8d>RKZ6$:-1\G"P>5
%OS783V.l'C&4T;OrcX9LpUb#BV]FQ:YabP1p1A(m\7L))SptAF5iIU86[*hhB:p6b$91V4Xr1QC>GcLpg_AU]8t)F'H%'FV:0mIq
%&$N&HnL@>m1;c)KT9X<Pf,T@)"LO)g;St70_KC!IB[/X[KCS>r7("fMgp9/4hh%rI]IY=o7j2&QK4ZQ;^Vg`r:DZbWGR@D!c#gW2
%[oLs=k]s'<*;.3FqfkF`G)(l*n\r`Jnr$j=Y314WXn\u2?ScjWR9KO56Qu2*lejj3fpbqn/6X'j-@S^2&]\dOCk7OG4$6M"jYBFV
%N5>k"]SB]ZWTmt_/fE7hjuI'L:.C(COV9m]K2"3c%G^/>fqt5\2CFBS?mgd-+Wjp?S!isfp9*nW`Y$3;9gK\E$Y(`)g:K>lfeae3
%98k05"BWdFp.8"\2Akq;<=!!5JZ8f`"Zm_.#aC"_Bg9[-!<%L[5c1)[4PES/ktb63'/d#BfY]Z^On5V,C^l#()V;HWh`hr"+tQ]9
%UY$i:3.;A7X(Y,^I/fS=0=dm,97s`C"#NTXr>5Y#/gS3=3m+.?2haA.afPb=:(aA>GdCZtCjdf<[dkl(q*b,^1?rn3X`'"ON5*U9
%8T;4D1XkVS16rc"\gtB:ccegO0'o!ed&f&/HTO8u@@Zl#60;3pDua>_B$ZL>Y!rG\7s%_j)/atYY0s%jg1%FN=jG,a-p\l$L;(\)
%C.dhb%WoaP7:o#!<EfhKRZBr]0L&([WHl,PL_6RPa2MF,FFKi=m^_3cCpd?s=-&CkDHn*\3\[gTGW!ZH5F0BZ!H6t8N<NGn=dgRj
%rRECrhY/$=M?J(%oYd;2K3dlXPX77H7*dZ[pikERLZI^Dr!>BX[7>f8hf<Qpo'h=)TF5.AANWHEXc7B4=:FP29p20U^-FWXYn:4*
%9R4`OS(ktGJsi'@(V^B<9la]2aD"UeqkW698>ZpMCImAAQ'5T4RAr)Bdo^+&;AB%EN*mr?,2\]9lf'[9<ZD(80dXDA&'3<Nk,jSo
%P_>pA]Kqlodp&o+[7^#q;sm`u]U(]*Bp\I<`"^Ld&Y0X-SV/.m3OLdS29pA$?#t9^qgfi0l\C"Bh!qW`'Z+@>D(u$@C0^!@1l_ni
%b1.p%)B)D#aCS+]g%U=F$bV7'S#6k<lXsU6jQjG'e%X<k9B1mA86khi*V#]hEH9)@ac^kYHI`)K#)lK!p.?2C'S/V1)]Wd.,68Pp
%di@A%X96f4rK>f9ZB^?fJH('Eqn8&('U#dQ!i9RbUf9MmQOgU)XLuR$QCqpID>uHd)-e=1Gg[CE^o]'?N?mD@l]Tsp?ZZ@*'-,ZH
%n`j.K;-%Y\8sn\$_2ZJtg'r34fJssS]BZg/.GR`1W[1s<S+;Gu2@r=(ArVCT(DINd_Bar(3c7Q:VGShpTd##:<lHsi]isFF.=IZe
%nRn4)G!hL-=,G<8bA9?AX#,A3kY9=aAsN#p1HHC4!JHW2:SD"b]/PdYRKG:(oTmgBNO&`<g@mQK?0\m,6?GQ$"..G,BS#lp><af#
%!jmThA1-#^*.6bFYl5525s28.j37*<1*\)$TMMPGon?9p:"XRk[='kNh$!emr"`i]fl,O/%_2L$!J$fXk]]2EohM25qpt$a"o6Ul
%,EH7X$VPFM6,Z^hN%Z*5!,2m8/11f96l`u4`$JVVU$Ni#LeoA02A4*0#8=t,`[>:&5gXM-P_a99Lp.cI4`=Pcrl$?N6*!j\qs)H\
%UE4LZ<Fu.30Z_`]-m=:8&<Wk3Zj-k>0Qnos5=(,iKJ`p4d5"Xj:HFpVgs/lSY+d!$$&dfC('2:s1-@S5XO:?\9G'@;pqG?HdXD=A
%2Vf'gi(L]ia!UnI=CuS>ap9>._I>*:HuuiHdG>sCUut1=B2/m_8GM:-2q^SF)j4'lphfa-<0_<4'.1)5H<e1apOH"V/c,is8co@\
%GS,>?H'gN&4S8g<T/mW1YedH1@Ts8!1YhkinejCb,nPb<V_c/G>U';326RM;DDS)F9G4KGEQTs^P3<kN/6(qs`/cC'\@nmI\?Dju
%#.,c[W%5l1(l/&IhO?B]TP:<DeAM^AY+)R!@K=Cp+;lFeQ8$Y"]g2T$Qf*5=03DoMr3t%*p^<[hD\5Oji(^!!>*^lTY&#/4f;(=-
%FfB5MIFCIkUN@B=h+i]%2*e(H=4M',hV?fB0C2$`\KPF:i8Xu5A9WNek722WaVkHBW=IsZF=aN!7s%MVJJ(\I=+?/D6FCC%:>m@-
%[HMM:RX+71U/<c"C'#*#lVbYXCPkt]kE!8I1.TmER"9djL_HYJ&iOV;$dmiU0,%FYB(+JLKc'cr2V#GE9]1b[N>fiaTT_jAHu`H_
%H6^!Mni)X1;!nE`PGHk2,3^(E[WT`c9i$`kQb-<"eNC]KgG[_DKPH0>Kd]r]FG[_&Q;`$M(jC.gKD\+i(Pto:-[_G8,5oYaCFE3A
%#b^Wo`dS0&Y!_fk>#4VkS+f1&g(SY&kShH^'9@]pQ$@d,3E<WNE0A=@^,qKF_M#]H.I;+F<'RZO(:un=YHT[TCQ?fsQrrm.d3_JM
%:4R=(p\2Up&I7^q?e]!&"#OkS,\F9GRD9,nnEEal6<#Tl@,,3t6G>i^<^^ASJHgU;Am[^.(W@SO]S$R10elm^%)D-@GRfRS;6:PW
%D@(&$Ns4^T&)gS\<mEZ*$5Z'O$?1#RK@pi2KoRB(n/-7?qO#eHAru8&OBY28[F:BFMpA@l\Lk<=H^CD_K"jl1^Ji?3<jHE\H;tg@
%Q/&,Hid&(I`g/Q5+^J0A;9Yjr'f@ADR7J+Th7-_1gkZ[5C-E2VHAiGt!hL%m/q$JPL^/6ZmWM(fIBc2fW'-#:6;9Z-3o&J=E&mMQ
%%j^no%c]([Ip2h@N)j#B.+hB8qbs1igXC_KUVgtM'M"HGm@dg\*A&2Zm;nO:bA))98b2qKGD\A7D8CcAo!L/>XnbCXIcamd?Q%,d
%QN`QjbVRMShe6W[-+"*4./l35AQl$d7LJN]8/UE\eDQBb%IF."\TT7l98\58hG5i4LqK-F-#)IR$f(gCkU+5V45JL*+m</hoNSbR
%Cf3JIEiR:Xg["N>E8ef\jILB(h>UWgE.>]foP<Oh>W,2"/R5a]=Esmojc$B_aL+*#P26dn8P5q\g&_b&PF1m!*;a:qa,S_=Us"_^
%`1\1r^=k=Mp]s<EFTcjZ#W:U<;"It6UsmYR_KIYRqJdMr"]L;)leb&:9<fr:6q*p@'a=;H&IlkSE<N!_I+ot55D_(_"[3ig;1+*j
%-?FcH/Hm8K+_"n'!Tn\0J%SFZXp_e<Iifm?L'B]s4-UaRgJ;MJ(70NAB%_n/8=\h'Gt8O,7jUSQ@;J89dMnPrc\E+.[DVB&[@br[
%!$EfaHg,[0#@B/+9TQ"gJ4!iq$b;7Y7$.\lToKk$aPf2$88][KBsiW]-lbJ=`<"A8eQ@:#i_qu?37qdVcT.!0Eo?'0+/6HTT!5D%
%nRZ4.4cS_AdgtcK3^"F9EWEp@+MnV;PSfXgnCU&_"$j.qr'UJHY;(m[[b.lE,kY^7>H@B6@HlMHY-k[+OoAQ,,Hu`*ZM/de#kAa=
%D"kpTdN^ib1qps[9M<B\P(ELpGBB5K4de<edXAiZd3%Of>E'd^7JndOa%'rY`CE&frJ2V>4<tmXX.ZWQ/.JTUE#f+`?-gka#$P^p
%)P9ke_DOGDPO33j74+6L=ho7hjtJ;QnHLn$bDY7A56t))cX7XZ7Ya2Q.4crlE_iHV4S7g>X0I_*\X+>rUF0TNI+ZYnO*<0f<29rk
%7mCVck#([9PN$l^4=QjRF*-1kT[R6eb[f[?'(ZYVNNjKm8],nPH)`/4-f3/+Nk>4CkZDNJ[[Ws5ViUQd/41q(9"NCt1!-7;GB\d!
%-+k)Q8+2Qfq$;[<\=R3o/m-MM$1TKZOgH@s]`RN1@s33Uja_-(;:&fhK"jlJ&k]F?d"Z,nmcQ.:c3?\ih7H0=6ReIne1UT^0U;q:
%\H4!CD`e()_6j4R/TDh4O\DEsCQQI)K7EKm9jd<IY01adknQ5Jm:[uQC$lhcaeB/KPBFi0L\UbtQs$Do0f.Whg`t-HGeODu*RNC.
%V\r\,8lSoY"prJL&K^,.m#Pqn/#NAK@HkGC+qK.e-iT(J>7i@SG`f,ennOLBD]ga=X:6r*`+C1FakaR/$6dUJkde>sGf:d2U;Md`
%EpKfHK*.0D<IBZko%OI")_X9p;hut8RO@@:1nf[njO3s1aL_1]a#W,0>fN[l._[97gX]p`Dp@q0hB9j*A`mfG]?-@Q*&ZU^60P2]
%#F#n2*"_D@W1^EdZ$OhE!?_0CF,"+D.$RQ4q=h/^<G+SqV!<oZ?C6&G%2!Vt"upAPO7pjnO5"#;/CgE-H0<p/;(&;3[;k3`pJ?]4
%+.Q5IGd&X-)]BU_!7FP!%?D((E'eNhJ$%;CS<\>(1+8WJrT5WKT43.nDV\>`$r"'ID7*Yc<!.l6IJLEsCE]X0);?[=Z1L")bd+b7
%m,H>fDSBs/9DE>RZho.8pc;,1C7;LY0;r(^<?'"CA$>1^f:3*+V3[OZ:r07jFJC]eW[;2TiEC$K$Y8OtkF(.PCeJc)F%=H$SWnM>
%X&/>[`geN9!9!"!7NMm(.UJS#m3UsKmuiiq=9?1@ZairYHAdd=JCF@CN*MV>kfV3A+ejR6XK\aLBTe<fT+1tMe0==6`8YGj3BD33
%I"dUmE%ke6<(G8_\XR^l8/J:%.HOhb7e@?%8*g[_$Q1%uV]R#.E@5r\WR?%#%T9Im@KLH,nT+$.E".,4X*,Z*f?FBr34Uo5mb)T&
%=>BAN=3C"32:mu$A_,?3olFpSdYbVW8#J7R:=BYD-*C>`ld0lkJ%(("^[(iSg^D%J@LAT!Hr$d-f(8E39Jf2,Grap+0-3\E\'Tut
%eNM^/$ml^MltUQm5#A?noa^,i?kaoPN9aY-R@2qP_0TC6RY@!G:/\1<UG<mK/rPmY3$@+&BFK%W[PGLSWqdh7Lk?T4TW:/sfV[<M
%91['KPOC7rkirr<Ct+GKVk+WC8mD67Ts-&&5n14tA<^EQ`f2_4c0Dff2.lc,";4MYhJYN[9%]Z&Q&e1e(XHU2LA\;EW8/oj*uDW/
%0QpDbH0P*iZ[@-n[`@UDO$3'O2m3c.BRHt?BFZ6D.ok;LZYCm(?;mAT`@-lcb1MYTbbIWP?1757+!s4K:36"Q3un'+n#SFq\^\c-
%;c7Ap[7[Z\Fu3n4Pab`.cNTj-M=Y5UjXg$tFf#^h%7TL:5>D`2(s"n`NdnhB^8-mK",uSSg?S!FKQ1aoEra*mGq=36s5pEXhfX8p
%;]YM/7uKOlC$;QP]j(YA\(uQY!i2=$b8Ld[K[h$1#=S,:=4+\oK&L.Qb].sONOD%R=MS`77\kS9/\"L<T:M^SLp]p3\jSL^@iS,i
%*3Kl2L!+?=]cG69l6%P3.3'$9)25!pj?&e3@p*AR+\.,-4TqdX,^Z>6K/G8.67P4#IY!ko]tM*:.WeL<MXjZ%r'(p`DDY9ra(78N
%mZd^Un%3NGWN6H0?Vpq.XOgI<^bgll)#(hi,25\Hi8qnp(CJ7o[P5KV?*$;i.TY30?WVsTiEP"f$8O+2&"@fk:N.KJhj9!qS7,Y/
%T:i1c!TWfN%=\Tk0P,R]?^03']3+#N#dAKN5<%t&>\!U!pf_`0I[6cALAckWQ*=t)/WK&Ibq,`N/Qr3i(V#8Il@=A(%L&fJCT.J)
%K#%"+(+&BPI[[V'e$T(&gHJ8(QkTSr_/m&WBoAR-;^#XnoU:^@@!Ct'd;$^=i`/u)/\75Wj_%*1qYp@X<)JmXVCRqo)$B6%bSgX0
%n[:f,0S,D>:FoeP5VH/4l$Ver-r..TMUY4'L$BF'Kn2YETu$q'QF_Gr<AMMr[t;I87tnq'TS#?>d#I%oh?($%/TLP>.O,tp>k>>V
%e$+I;hkc^k:*R2"K$$%U@t8_*B'+r\C7ImF9*"+J'a2t-Q-e08k.(m$S70m\_8gIC0M^`cm\>$<GF!!Gbs,P/^5qcA6i2B1^kZ^a
%\fpP[LtIFWL#[92"MfUM:#M"=[t2pD5WAe!?Cn4S-^T@A)03[&IB/bJYZs&BVORP$MlJ5o`uuSFZU7$'CB#9+65(YB-'sI)WHT6<
%+0,#YPI>X6HahU3g#o-3lWXAb>&X'?09EU6a#FJ@KCFC2p8^kcU>#;I(h&sbnUlR'*60b5k!"fM.qtciQ3Z&[DkgTk/MJf4%6&Us
%@;OA!UN,?>fS,UHTT$-R6j\2/VY=9*RY?#L$)sL?@[?gBrO!N@d^BpBN=p3jMaa*a3Ai,FY`geh]UGfIfP'dXdUB'D"@WKcFt8c"
%<Hdq[eX1u8!aF:iTNjs,X8I"T7aC#6e>aNZha(=+B!M"&6aFAami^6q^@%!4XTJ2>S#+jC`fj*j=>`6*E3CY:6*'j-B+p%:QD,3A
%p\eca_R``'`Fb=,G!h)ed#WDS='7LY6jMTl.2eTu)NM'o\g2M1eKK'WN$'C_IPaLkI-Y?cm#T*hE.6'lD7We6aA5-b&.;'(&3(a6
%,#B7fSl^]aao-\jV>s)O+`KQ'f0<SgoGh<9=<Y39CQ-Wn/&S]mLqj8W,S++EfD4PXp:l"W!psf_CEYO0#.rqOWV\utk6f`LfWX:G
%$F8Q^.Q%*3!PQr0:u2&UW%Rcf,0:VIe&%!GL^gDt4u)#fOUtteV&</3eLa"DI^K_8QQdQhi1E9]".R#3\:#PlJ:'=Nm/\^_6gkZQ
%h)tj\n_jQ9\4_,BWbLD"=:&)^AT)qk]rLH9/]:sfDJ&ADa?/W/+5s`IpisqFh4ImtN$LsP=R--3(]/R3AWOU&--Dch\)[fQST].@
%D\i>jieAPeF('uN84N82_,t$(YFR_]^uMrFJCRBVkL^G6!#3b^,(\eI5qo5>Pfjae*0YX-_3FBD&[(B[eCCgj(A-4'ks2JjadgC9
%mVFM,d5!R513K=h7?:$#TTT<!QO]'m`Jko+@t@JoBc<+;cBg4PmDkaUGuqpWX9*^$-<WU\et2hh3Q$n3;="&diJnZbs#;`DHZf2q
%('RQ[1,?8Ujg.R^[4BMhZc$#.5M41Aas1YmBQ:"$`P<'DM_I&?g^Ctt'=NCb+OB2?gS2rC^$4<,gu@%DjI$^4W:fuSg7_^^E*53k
%prfCjk-"B]\!d%$XTNe@qOS\$%)I3s'eqi5C!=1'hZ`Ff;5[qBT1!t?5>eHgB2WUCpt&K%l8Xn)'&g^mp'5F@MT?jt##c'":R(iW
%f\,4Sqq6FW.gZG%U?4_XT=_1HnRMf4p1p2.DZ)%Tl7Oj'Wt7bn++E6drhn*85@\WerO=BWnJ`nC-X,eE@=6Y\-'JAYZ#phI@h/*c
%9_VW<'=^+7i1*b=G_3tRT$N%:OcaOZ_Ip$q?\(2!2o]?4U;L$T>9U/ISo4A$6gro3;Q$7X0'_B"bae45.ju)%Jf;]%+cf,(:0%c'
%k9bR#E'alH:$4u.OcT#:R6-:Y:c!EV"3m.U8AU+2FH%uW`S'>o+d(F8':lAt[kNtA_LIGs.Lgn@DM]H.[f/")ldEU_A5HKr%&/lg
%&<4J(Kp-RX6[ik0+]bmc*"h7[3R:>YSg"X3NPp!Q$TRJ8_jkS)&M<B:R<5KN_mgrG%<1luZuSZo;H+CE\4>X8*`mFoR:,e53eP9]
%?-+;uk^CZk7G[6YK+TtNmKiI!$S1grKcle5^,5^c4b?Q2gchh14@]1QC.k#`cD"0i><!P^L=QVDTQM8p-mRso&/L7B:LRO<=R`eo
%3O,U5bKk]>\`9>ceGacP&J"(Q(8=1^i&Va;!eHECl$;ViUZ(I*a.&6nkX49,YnK:'46fB66_LemI(?@^21YRYFI/1dr@qaj8]Gp&
%l)YJ!fo%rkOD[GCSV\7qm)P?-R=pHs`,XfL>#S@bgk)\Q5f$<EMAIL>#U1<2[]cMB*<H/[XI7`XSWU:(u4uQJXKGH.Sa`$_%
%;<K`?Q2l1n67=%09T$Fp&'MIS-[d#?'[RPj<c14poZ"tSCXoYtV+Gpk_X(Sd:#9B83]M+'V(*@9.5)m)Q0?1:nJqTanu^Qfrn(*_
%",<Au#/;c2.6h[sf*+4jp[TrI,Jedsi!$c"<UjK[auZ7_EV]]t@H(J'#%i2boJb7X7)Nc=ePFQ4E)?1[cTThqF^=3c1l'n7[6!>0
%1:ZlNWmesB=LQ3Fg<7$LZA!N=5o_:M0>]2Jap$@)<(17d)Q=uof>G;&Zo]8B#m-oBGU9VI5&E2eM0o^S9nnS=N"-DjB?D#ZP8oQE
%eiC9Pe;ng81`GMhqAm"FT`(Ck4TN))[$\=L/807h(s"4"'l!C_DTOEs>hCA8'_:?.QKEZ]7D)""RXJaHRgJ-W^qM/U<2glF!6>(%
%m6Y=1NeGtZ@2ETKo5J2_77,$4CGEFPkA,6:q3YH_kaPDE[e6*gWtu$Do\o99YsP\)=,j/h`tPD,EO.Z9`&H]i.aBTUbe])qWn/<H
%-1lU,B@gl7&p_kJkTK:#;j8YA<TX_1'luH?qtl%M7ICg96eoc;5L/U.PpOP=F8&NLNK6^F-Z)l64k\`>EWD1`jpKR@kf]O#f`/5N
%!%,$S/\Kab9R$1:Fa(q:_'*duM=I:t\ua(nFiX%j8=YeI.#`AOqpVpB:NVaTbb+EF\Rd(HZWlj\iJol=NI,Am%a>S-NEZZ.pa',M
%.<b.@,UDf?c?5DUOC2fqq<f,e#59;8_ru!8HL1"-31A['`<b/<=/"^?r1KqFnqBhApq1b#<atfKM<=E4V>>Mt2Rl[Yop+BcFdu)u
%%F_h7fk9^F-2\Wik+NFk-p1dTLotZQB>@ZOCqu8_mKLVL%,:DXHre4H1!dHEi'R,U?@gZ_;ut-a]ULMb?:!,c?gPjbm^(s?2E?T[
%S$e?`'NBkEQ2l9e\i\IUggc236a27AY`FpjB[rHY:erdNn^,EVD8.;XT"(=T$7X';RQfOp7\>"0>:A9Xnk;:BI0maGd!'A`Afo2W
%_-k6T\;2-8T[eQOg2hr,,X;.R<>a_N:m)o[kotT<5,%$]USD/ET5B<;ciJX1_=;7Q46+.!YbR2#-^M2<?]#^R?@u,f_$SumS?2]R
%aBo`8&0QZS&^H"T^A&D="kakBjL4C$i[uD`TPM2O7+EobQpc3C1gptmXegY8ZNeLi?tV`&6nl,ZRfSME+E_oc!Wk+`J>tg^ApW60
%`_o1XD$[@4f+c4:8Rk5":V8Al?^9"J_`"4X@6>Fe8XTaj'k^Gif0?n)3<5BZ,"IMhcq(ADU<K<?ohbjZ@4Ld3f/%$7g9[PCDT\?4
%M$c]*>3:X9C>^r68;3PR/8bP.=fPq1Y#[@WdMteZ67?1cKW!g\6MT*jY;L))U9l`%&)R`fJ,HI+I\,A5E3*<[\H>Q\;eI*I)$-[g
%16SrPC`DG@>&dR<_J!EGdUtj)MsgB+%*)kZO:@b(mDt[uj-H:9Y+-*k%2E;<+7;(`DXM#1Z<+Nu5D6DCJ0*>$UcF!c)?HMn]$.e&
%W]c:H*h1Hid<X5Ab*?:E_g;.h8A^2D!su-nPi:Y?[fRuDFV[n;V^SF:/O1N+ii_:\VOamGZOn&H&B@bI</d'/&O7AN6R9mJ'atSH
%n5K[FK^hs["<PtH<G@jMh(dkklAC);88#Km[[eq"2MN@+\4@RJ>[nu<RO7.icOE(ZJj`/Qi0al2<D@^l^A)b;PXp<<9p+lEPnA]a
%!)/O:;T;:hKY/pZ#A>*9Wn?Ajm`,SrpjZ6let9Q@)nR1Y7OC\571E"14YUF[KtaB/&77G^hRi(?RP@mPGh6OOWD@a7cP$&`7]*OE
%>B_P)SIrP=XIc\ls*aPX:8)mN'Wm_ZV>e+-dk+<6g/a:4bB&6pp^I"MV'C,.1Vh#cLpD<;1'.?EkrRc#4fWJj!7nHNP#:j*c`%'T
%*g=3f,3VM2!pm&]!.D0l8`O=t(0V9*7J>UW2>S&6cOm$Y?`4l*F^[-0etoq3[)1`<."MH8:FWgtbW3C+d=48iar[RD@VS'DV3un,
%\X?>`f6f(L=B\=T!n#b]%baZW;Amh$6ZnWSea/"q/6nM8d979MJlPfDOR[4`U=TfuAV2g=%=G])iZBCY,+-BeNEg9&nNli28T"ad
%`Iu-8N4Gj99K:@.4DI[A,LB#0N(_!D7f^@X(F/A@Y13[TLhWChpQ\8c)OWgDX[Y%-Aj\T`fW4TDE-2[JD8!;9.RqK5PDpf2,io\V
%B:8gA:@`,@B=HRC-in5(Lt.&8m?1)^lh#W'k=B7*?@QUsp^E'p'cN.,Z?Y:fl+7I=9D*$4<oC4IWM*0PnT]eV"&t29;9lc(Q+uFs
%"=$M+A?<\P>>4T6F4%L'ap:5NPOV@9+N!t9J#qKr(feDK;9ILb=3U,;Q5$H@`QVG[r="2J2Z:rR"\p;YUq+L4d,B!p`,Bld8jWA)
%IeA"fh@-GAF<%fd7FK<Lk'DkdDFBCi/Pf*@kY"!Kcd,U+ItO8!2,8C9LNPW"?3m,!J_Jt']U.MnJO:]4KD5C'93<(CGE@knNuSaR
%-Z@>.-:+(h$bukc+:"De?8G-/N+0^LPF!=uKc:2Cim?3\k/&QPhN73%'9gKCM6DCKqooSDm-mnr)+KKNkod]!:2PTWeaZTp88+ep
%ffJ-I?K$i!EQ_#7Wg*o%VV2N_'=)E:WI=(&2)A3\i2POST964kdpQ'1*a%mU[Hau]F/N^tNf(j1I2e`eGSl%63:N"q@>Tp.Y%c_N
%6LBr!^JU*.f6,5^==)NCpps=hMfMk.d](%IB/n.;_51\?BR\Z[c`;AH4)-2-UW#2f2"7a"b'SGP?^%n3#QM,dQb&Zhpet^6&#ig'
%*EtSUI1t49%PY%*X<&Bc1l;fud.86bT+u=saIp=B^J*S3>/Cs-C,SDZIoAcH-Y<s4EsPI(ZV^@QdYa&C\<A:*)1q0p)S$OGOA\7c
%5GckkPW0akc:-r:PbkQG\JeN`CYXJbH`h8X<Q_.=gL[5F(+Tj%QkiO_q-fDg(=f5Je`/i+OC.BA+p,:4d:?>jjMMB6q!Lh64>R_7
%an,J\pk<@j82%j4bs$4$[EhNX76OCWOXXp/"+]a;akf^!pr#6UF$=QH<s2"reM%8<-Iguhi#0SfKP/ak#AD*cha#UL`/DiFdaut)
%[rT3eX*YVi-5EjhmqAnE3W21^MDcg@KUPCBI@l6Wh3$`'4EUB-7T*2<RiL?fdHC8+/G4DPRqsuQNBE)r([rp+W"IWF$X-k?F)XWH
%1)Qq']ct1Sbt3FOc/>P"NA46QqKSFijsl`M5]aF#6<I\kT-Y.FE_/ENoeh?O",n-ml^h"Sh<R6M/Y?#NqTJ;dj.pg/g75^FMPRYC
%!b.'ulEG%Pk@u86`KE)G;!a\6=V-Rh2\SMW8lW#Z0s\ko%#N:3I,aF^Ir8)^@Yp^]!BCc0X-i3:2]b^)P>75-OiJ!RGo<lLm87__
%\0+?fc[UFVbs6<,eP[C`^5kid>[4aF)AQF?lGp(B6:sed;3S9ITZOG_DO)0L=sPKfKkWn!6V&IB:"tZ8[dMiM3sK*J8[/NFFHlr#
%Z9(baf$7N`nB.^J.7`C$$f[t@O6UERq?Gna3I/o@('111a`m[e8`D3GFX5erdG(^ej9XdlY5o?=GMRh/-2sH8MEiAl;+nnM]umsc
%4#fl3/GH,>?=3j0%tXhb,!#lB",7Z\^r!K7(QThQh0')Ad^]]Rpa,n,X&9>HlIIeq(qZ$<KI^UmnaWT;9S`ZhI73Gb7S1M>Pt"*!
%(CabY@k^_!QgK"DJg!Q7KKsBTA)+kGN,VBM7A)Ump6j>r*<8snHSDaB%-n"L_s:1CRfGnYNB4P\*`[itnlF,W!U);AQ6RS/L54-#
%gBmEOYYVAU]a,@J"l7]k:J*b*o3`nD=U%]k'kB[K?NV^rQAunR"g%@Mh3+GKA"5h(LR.jG[sdU%]6g9MKW02cfCYA2FN*#XHC+B_
%?i2i1ni:bb43cHrX<[CPccl(T)?R$cK)7kQ6jaQbb_>r:^f@Q\8U6UH6qah@Z)LkI,=Nd9XUuD3/(Gul$j-h9E\di-Q)T.i+A$Z+
%h6*7fN!:W7?_g/NO2Vri]rc[pKGlC#SDELt^moT^*hFB`P&\+epG,m.<@Yd$bXH>'3%NY4,o/siicGgO0Ll<,bWQ9RTU]iN\&qo#
%Y@2s<;QLLZ]o^M(p^p]"cTKs(9o:kpfWH8:H!bR[n8M@U!$'Y=ZE@+pDTCR^Ar1Y?`^=8RFlpPCeC"kWHY/T0L+;LiFNs1Tj4(OX
%daOGC+tNGQj;IY9pj-LJ^*c'0(tCRfLcc`0Xit"hqcTCD]GO)MY*4\tWa,El<u,][TI(W[[FX'P_6TspPBH@m`n+oVK;e0ais&*;
%+M#UBmta--hdNasON+'7K]VDt%c;R]09KU]8,!;>W;ZY;K&2tWC!R-6:i9NKIPQD(:];=U6h]Xd-"7+Zjm^=5,P/G34I%%0>r2@#
%DQi)5(lE8cqaY1d8e&%M*_E.?.R6+9c"PC99X,bP#--\3MQX&s-^?H@aP6>PE&M+h.M!)$6<%+jTp!LK+5:j2<#^7T1.)`3''prR
%!QX<C+$W'*(SVhg*b3)fj4"]I;<=_.0%a^+8f[o]VjdRW:p+YQG=6<]8DsF/doikdRSNja,&R`5fP\08.l'PZC6e_o;9+l+2\@$7
%eIItpRPDZrf=rRn/Op@%6^'L01MjiMeK0bu3]n8"BV&KVIJ%kV)QIRg">Z2q;Gif(8Ld%:I.cnBmLF^an!RKS7!s?7@l3UK-$HCP
%iJBTDI.Q4+lE\4Zhg&aB7F,_\)C2`aeLK-Qc&[i:GolmhSGhD5LbDGWrWt9[]P$X=V1Q8,Q$"_EV9"X6^d"]o#S'hM?X0/iC^(co
%W4b6H=JBj>"$=mQVHa[kh)f0QisHhNZF`Wd;0d)a(9$i-PdiZJh]W?$2=fU+Y2_4(RJ0/cTT@G-+Wq+Xq*$LoR,GIkEO*$:0O\2t
%nR1b(M=3nep9IEI6th,bJ!S=&!&^sPVVBc0@uTJ(d=tsJeVE6VH&6a-]&0c->-8dK1L_NgJR+8b^qKKTb']P=;5en3g)@t0io[$J
%PL^PNL8$34o-:DoP>ob4ZDf&U[S]L6g91&+7W7U&.5(3L:Les\&kOMX,;1IW%mq\<$Z[+>P:?A[iAlt@RMA_NgC"T-ar4];AE\OM
%kpLkLOj6jo)YL&9/%;PI=S!Wh;1_AZW8%tn%<#I">]ua\-oO2"kkiMaTU8'./tZW0rcl\tA*:)D/h?Dc4l9lp)7gHG9AnHkdg.B6
%ECSl/CRo'<)Y8B8(5tnei];qN=-:MfU,]J<SRO00msZ7O0&m5<0BXA.$>*L6CT<UT<,#TeghLf77pcEJA+>)G28p*T2qQW'!TSXB
%LC^UJm-GFP,C'.H/$^YE.cNo`UnR:-,>-/.rQM4l1+ZFu%s4Viluee,'A%a<<LD8Q+&:VRgf0MH>96?;?\4Z^3%HOW("93ViVj6r
%ON*$.@,2+(guTO,,Gl>I_PcpF+]XZHQHjR]X'U@UrBfFOAY'Qoi6oT=nP%gVUF4',"Pl2km>6<6r[,PcFNa`gAPT0QNr8VPl>]oq
%8j^M\G0l'YBS!93XX8,9KJ[7jm*="lU_+0-O!t5H).pfu0(ka\C2L/r,*1=Hk,Y=j(ZLF+1X;40.QoaI-"dg?Nnt3)mE>5KcF<3-
%X-j34/5G5M&ZrI%UV6X6.k4&A,X'5k<?W92E8B>E=f5oG(X7/WfDp^T4iul>?RZa=6e6DATH(=+l`Tk2A.?:9;d<Ju>3_B=CfZBJ
%e@+l-r,AR%,@a[M+bH,s,pt<,A$BrpA_(([/J]52),Gc&S_jlJ2DsJ:RlAF47XUl[!`FMKO@QD-j*%AbEXu2mCPRbQ)Qc`&"ecGV
%S1LN(&[&U6q+Gi"W?BP421q^%>!4RM-<@9dKTm]Z>pF)uaHh]6OC(O@9$"%_Wkc)S<Er*t1KP0[)gH%lEMpZ"3.F:`A"XX%WY9Eb
%8+]+=RKA=[gm,7d@!*I@Folo"FNB;>]-bP\l/![(#oid\,6VKSW(nVu<q$S1WKY_nmI[]H]&`0AOlcT!oCa`g&/@ZHdhX4$n2-X>
%p>tn"N4$u'Bt_'+`286!SHY16\E*:mE(r8(I=WVDR3@`@$:i/>8n;f+L8nge3k7*LnIO$58\XZ68@#tNgE4C"pb$b$0@(0/A^Jpa
%'t9JCaKQ:ME(cDM=EI`uMNQJ^MK?-YDB1om8;`1VO*3G[GedVEeq&Iu5!8<hh1U()iiWh!-iTd`FG::%,VQ9a$(X+(f`.as%P47Y
%N.I%)9TW>@CAH83`B&#=7YP5(EtP;TO16WLYPBp#Hb%**LWZt*V+UD/-LU'HG1Hr.R`Q0^?]419L:<?.l-?XZ:]lJ6(5IakbhI?O
%mS\VU4Y?HY\7.rcmuW&@J108k]&n>Q8]N%:2P"fP#DM/-j#mCAZm,D_]/[=aDH20ZL9+[^h09kda:Yup*CIgh1#1`B)`pT`fBAip
%R3O5O,is6N[XM>`R.b78."iD(8P=nUg1m0fl&flkE!?,!O2%5<q*XFY&3nDX=XDuA#hLFgp6(KoNWXCo7gMZ1*g>@'-eM3!H;mDG
%-8=MLqP&]ul1ooJDS(+/4,$'[T"D;#j+nkA$RG61#d@8@`_njH#5"Ju<f^p4AtVPt2Dq.N-2,*Xc\--I>e%u1<sCg4$$^'=aFM;W
%VB*P57B_b2(./M=K2EfYO1:]bPW!-`B(-)VdbTQ2M3I6/$I>h'd>AhG>I8)1DuCt8HN*\/\llK]N9p>*+$2nHNe5AYT/lMB^=XcA
%$,n0+8pZFmH<Q_hQF\F"/Eh:[>p9\Ddr;JF_dQ(N4/_ON2S'29\XJh4"H]cegB)@qIJV8!'hXV">^e6POV$kDBD^oU][E5^6gkI3
%=b@oI>3ZAJB<-9ZkM$\DmuXdK,6jRV#W2QX4#@!C2s)0$B+n7j=k3kW:2$O*3nTZF!Ne<X`6_^b:Q]Xs'Z2fNC9^8o;3e2H/3"j(
%3#r-ol5:_=nBA*=\=lo^fLq,5fFbFOP)SR=#OQ9Iruf!i\C<?BG>tg89HA2d$0b$njTAmIeKt?/01m]snOc1YA-F-+hnpR-o+1*5
%7biH&WA@[H<R*$qGYAB5]366NE_Rjjh+d[%RBVa.#BOrJo'sb:8:'2mr8Zs&55qf[k:k%_Vm3BA+o;j*5O7eK*=5U)XA;:o+>u8q
%[b?DEpph$.aUbtP^F7iqo]<:W6kX2BZ-D*%nR4"Uffk(*A%UpB4d9n*qf/qIVd4c_KZ$PJSX4Mf/,R>DYA/dM2UEWPPC(WY,a',U
%PKXtCK72eoOZWV]EBB>u_L>pg=dF[DXubXimah[^=h^`GeiZ3=iYO76n[1<"ds6qt-66@3i+(i6g!TQ6bWD_n'!lkqQ*t29T,+i?
%c#-+QAI;9:UJIEf+cm\:7mk!]+'P1IC\k-1I'/MYh744D0->%WcT-2sgoKJ!OV'ZXqTE8s(ohL]!+Z8T=eJ:.'7BRBJ;o)\kA%8!
%'TICnnOj(LGt#%u:A3m4h8/s7MrmBNP@Hojng1>"0![&\"DXXrcXAQ,(B^nTFedu#8qiLVY'IFZD-N319NT-d=;`8='sYn7itYGq
%,R]H)+H0DS:aJ.ef`,%*cA%7lHN5lT?:tY;L>ifR'=AsU'o<muGd3jXq)C19-_g)7W?->"4HIO:=1FB0ROV_E:AWYAAJPb)_pUm\
%VInrlT9B"L3*QN5ke#G>G7g!H&h(Pq&SW$9[II,3Rr4--bX6RfmbB81T-NW,/R8##dWdtL'])`f(AH(R+#?H?*G<U;#SS4Fa0^M]
%U,%\*=X_<"PO6Yg[Cn/"'6JMFs01F&1C\T(JIo&RXj"hsYo80TCB?XAhH#:0YY,%HE$s?Q?RsLaf&RsQl)o4+WCoh'P^:[Pk63l)
%@^9aKT.*@[M\ph51!d)!>4<Np=,Ca2"neR.M6dA1(/R@rlL?K7)[uk@&T0GQhgtXheemB9+b;O<%E6sp5>=u(as2ID#<CN:mnn+&
%.j!9pEmn*0lS(%t%VhCOh+k.M=32m%.qo<..A/:[;Bc=akG-=C`ilL(d_/5,;fLLHDYk37%3f3B1cU(XruP[e[YedP*CHT2^oISM
%r=P7(8t(,YESiMUpD0rK$4Lfl:um^2'eep\HkB\A;>`?N,^K!X7O_'m'SF2`YL_,[`JaCXRQ+O*_9>T.#a4"qme]6o'G=U[fa5$P
%d>ff;GD9q`D.-\jr-jVVLGH`%S2@YLa%JR1n@274J!CQ?nN5(d!9bgP+%0Z_]2=:fM>YJTRl=rQ.mP_M7Th!nO*&PR*`AQA@L^eo
%n&,,QrtlZ]g^C6a>?Zr#b4B9N<HrLaFW)D\ZITY63!q$BpVBZV(rZZ`4*!V24+gG;a8tgs4J`CSQ2+OiMcg'"WL?Bg`g'-;8O5;Q
%2+d!6a=;Gq_g&cFn3P,HQ6cC)b\\'d#2\[;E&G+i9`W-)dl*1X*_Q]2E33<D5`s$CIN@uH3n)8lq`Rlo/jpFZ4"Cq\9LUs1<PiOe
%`iE:`^d7:l@F'U=!V]*@D#!8rDLYo!YUf5?i;!+6%BJd/jRt@_]OcO0aH/?q%*qGXdkEe7108jD3ea#jB+5.+7K8mJ6U%Pj:H'Bh
%9[5)V?/6V4I-gQ0?coZmSXaoJO\>9=]])4BX&J+[`;>`3;\q;CXZu=MKTTq0(@9ID51L]UW[T6X%3;u4,e#%ZLW@[UJS(jn`3dI,
%JJK8<l7S)sZ#C-Zb#I#jZkE$"V\Kpf]-WTSYQMW2)M`"6h#M+3i6tM?ZpdAWj<+V#BQrVPaIel4_nS.lOMEG8Iq[/l^`OVRr%0sn
%:K5S]XFk/WN$e+!-JPUS/'9OClFJ,Fr:A4C&i@'D13Or37rR4XK3cFP=nsjN)usGb#=Xh^;?0U,_?o&b&"hk1okW"C3+g"7b?pcO
%"!6&i^f6MNIcpV7AK1mfLhYbOl__*)TLrY6'.^me7]S?%3=*)0YVB5N(FuSMa"ZgN)6#0l"lJtQ2(gV@o,43BK,]oj`p/aUB>g*"
%Sf<C]+jlb$X8)0tTCrq"QQ4lm'HE(Kf1HVK-:J"eDp4OlA"T?iM:YSe;e6b\M+U#1A7XNd1Gm3g;R=YhAAi&a^lImOhKX@SKG[VA
%Z=h&_W]\dS`k#kC+W0d70&S\]L1;h%!1)Gi3HT"[OmKB4G66C3YjX<,hg'pDH_"rBDRUi&Zkd\[f58?;7<F^/_eM'^7_:;:@ILIs
%PnBR?W$pK[[DrEBIZ1;4,%Kh[+>*("TsO9D;aX9RO8VW&bJqD-Oo$sY$`1.kGIVU@DUQQ[PmU<W<:A9&A*"Z?iQ&I&`X^ni@/#6L
%rG>m'o<NT1,2#&0[7Ij))and'b0@Lc"J1[U/^qeE^@>!-kNi6_fP?L?Q53<.5PISlGU4sI,B3ur@s7acnI5<$p\!Pjibo<RQX@5S
%Of%40Ll*>B^t._+Klub1ZGVofP\XinH%_;m^t3f9?LaPA@(X:60n@3$Ff@fL)OV>YoR?;/^tFDXFK'hoo)`50'>O<S.WXK0$E;#5
%gZuleR7u.M0pE<HE,$W\Z4WZFs,8!@Q5Gm1IsaH_mtg*_U>EBEh]k.lLK?LR)m8b>l3Ee]V2'k"`Ypi_DdlMZjc6C(<0)rIZ]L@g
%E@J6S9&u1MpJObW)qD?C`mB\3+e:;n@Q\=G?X97"`RaXHSekQBnAuL#d(sfg[RlBd1QS$UZ[733]$a;-]Zn'Ek7u0<H0R0NUJ(!W
%\8$lcj=tnYAC#nX>h:sd"#`K=d+7Gr0i,0n*<(#N_[=0J46O+naL2'N_QFAsVDra,:=k?@8KBm$R4:HU_t",R.lVAl-]11&jdTm3
%WM-psUc@*mK>3"8k*BE/!ALcCNRl3_VZ?'4Cf:C>o#UY9l4k(=^+XA8rD(J0@m)4lR=/Z5+2f8&`0B5V:jFiccDD`1'3&MmBJ7L<
%^EeXE>]caKm#Q^$isqb]'*g3SW+N1/$R@n50;AB<Pj5h(`0Zfk7uQF7?:+Gr#e71sOg?/M):iJ*b*UYIY$A:t9PF4Z\Q5V3/c6c\
%/k>`#,!*QGRQFcr,6.3la`qbt^Kl'N6o9s_bPJ:/0\(]-6c#C]hC,Y[1+("bd74K><8AC&*B*`X"?H12l(209W9n,76'[Dl,1NQH
%)l:%>OY@%$]RiNM"@hrpM91hPRL1#aC"k;/_!([H,8N4<NE[&(:.Xt>IT-Fa4+sX.bFlbsMlNUD7-JC],<q0F?>m;)>$\0YAB,So
%O)\:L">NVh6e"<h?l=:7pA+XMoMW^Ldd;pbi2X;iC3fuXYHU!Ns'hhKaFiHSZ30=_&8<mt5175V/QV=?<WF]_m#@3.7P3nNdL'P&
%$:8L9IPXJ:U94Poq;Np'7k/#t.d/:E5GHh\!nf(`(_[7pR&Zpf2_e!K"bVhrB-d#PN0I*t6!]D)GO-nkEF1miKPP>.D0("WQED!r
%KG&t'aOF%P-JJn$ber4,YTY/55QEFsq6#b<\Ul,#>3V/[\B7^n%n%a"'%T4c6,nV;b_7a*YjJD8>i/DpV$MJ6?)&c<LF)B?O9d[4
%)mAQP!\1jc3&d$(3)t#fi5eL&@aBZ6Y8AVV)5PqSJ.>d.p`XEGn.cj8pG<B!kud.aeoeZDS3>gk`+TO4J<dYd>jR?Acul-:8Jo-2
%:lZ+Z]$N%DnOm+7?R(\%D.S\XdN9=F$4N^,,efE3qXZ$'mLOg??iTOi5Q"mglTbF$s71r[rl78%mJm+)T:UQ&a8bgfJ,"Nmn%\fC
%J,5Z7p@j(7T7?ae+9)-ZIf@72s6h'/rX]%h^]!E;5Q9^#rQ5)Rr:!efjc=ohl!H<`?iTA/If/Gks6u)!rs')*]YiY,[@dEhs48@l
%lG*AXYCH+M6M9FVQN-U3J),SL0@4k5ci8XsbC@lXnG`%Z@f5e(9_aU^r>5Lu?_3D[r-)NJk)Y#l&,um?WW2LNs*]=VDksc[q')jL
%,aJn(ectkeNU'/YldA,#)u33O!ulB-Q;>)T*@]JX27duW@mN'P1QB\^Z&USs0maul`]\,#8B;-^JHOdI($<=)@)2,#N<7eD)qD\Y
%]:!&PBBcp425G>_-%]MeAto687MdN;WYuI`O/2o<dchf"N$P2pQa*4rA;Lh1")toB4@k]LY`:]iO@R=NCaAq0/$Vi+js3Eq&g]-.
%8k2Ykh&!T3[2>&^<8e$kio;UtAVGD$fRZ?Bi#$B%<2Xt,MK3$j&2i4l<oX9\-h<k\K454a,hd@Z<IE^B0L]<sb!6I`h'f%cWU1:G
%VQA*EJuU<30IVL/?pJrm;D(SgQ/X,&)oIe;MuY6t.($*!9-Zh-U#Kl[]!@/5A2',1]G8X_nmWsqhuZ-g<!#tElJVhZA",6)6H]=S
%S!T*XX93`54io:L\CS*+L@%X=PAoJ1:db;Ydn\fZpL+kN&`VoP4uH0\YX;_W4)rZc(]j5od'-$K!/.]kjTE?/`qK#0'j!kA*r`8\
%U;=(78cefgX1Q;:,&aFR1YB2XY']uY^(2p#_lUS(k#*e@0\17''fO$L0ICe_*W]TlpA<9Jo?:%sJ<R5%(KH&AQ23V[pN[C?+UEJQ
%hIC_]`V<>WCIbA:`dB'_-^1i+7`t1FeK"2D?p-ViPDUIEO60WWifh6j>Kc9_('9Y(UEWIK)EgSZ*J?lBN^+<5KET->B6_^8[m^:P
%>pA"16bsl[i,M3l6B:hV):';/9>F$Q+1s+!34%YA#"M<d7K`65+Vn%SUj\'?N4a$l6DgS.+f392I2LA=:;jtHA%4julEdA/>PBR9
%;R1iIWC@1.^sU[e:_>#9WdompAB3n;bZ5d<7O%=L,Y'""ihA"Q+n.Pp0;H<QljqC+/ARkoL2&2P`AHBVntSOAmXh;s8,N4G\2[$K
%6g*>4?/.,7*N.m(%maGj,X<(urMK1I<31s'\BmXdLKfdd@N<tCU<np$],`s9mON>79eOS$.m6k=V2L<@=Mt3X+UF5%64cD(4ek%e
%'Eq[TmP1JVeau_dJ;G`Ye>Hq(64brprd:H4_QH'H6qs^5LHhm=aXAD]@j8`%0gL_1O;=iG`-^;Z""<;LdSTk=Vt0#/7b2eFW1O"W
%h=:LQT$Q<hf_\"3N7nM9E'WAg;WuUnrdcn"'*q=#VK?NU&/?"gAfH)M%hS])Y`!oDG/IMlaa<$f0"Sj.";8`qZqhltRPbhq&B1M,
%(f?/3VN$It$5fS;5>.uRZJn-dALT"2=NUCIS"=r0B!Q3l@]5l+NpPt?5h?Z(XgCQ^!*iLNj0ECspfMR6LemMP<,OPTi5%iHLi[SE
%^KJL5XfRU,>Z-RY"M^<=ok$R*)3HO]f;@U7J/G=Q6<BnZ%p-PfWhUdL6HY4Snj<%&a\q*21^+9MIgQr=b-;Mr-]O94W\5T+6p(&Y
%RtDMLWJ:>gMk6WG9a5^T0/SN0Lprt*N'.NZL/f@Z`r2Cem&iIUc#kuaBYW#D*=$%oqI_s?Y?fVfm\q]=b`/P/98LklR7lreW.ia3
%O!JrcGhIl*a`)gb6oXob>QN+dM'9o[o!g<QAS',aOr9Kbpd"[[<-ONQDjN_n&M=\RLFW*Z).0aYfRgR.&PNDB%jEE,/]`J3Vc8"1
%b;k8Ar<cWAEfsa179NH10IoO%^]Igmg?,Lum/a)o=3RKSJcT\/@ctH/FQ!''8VTG&qpOe9[h,h;J",nUgTMnfY;dql5.@mFJEHIJ
%[5V#bO4VuPN'"LuC\6dEAKM5fP.(]LV25<-@\>/8D153Cr]K.YGG&ZXA%IN]lQSJA2PD$[cUsd\'@X?hIUJWe58TDV2cTrV*YOA=
%\)iaI$4pO)Y>B/1<NQmfmXl*!)V"td0+Xn;23UI-.Sl`#&d!YSWII]4PW:ufJ,5*b*Q.9].7;,0!F9!r`YP!4$(,:8H@2)*/0lf4
%];@4\B3</B1-U':Zj]W2k6Oj?Y[r=>YN?oWFUVcJ1Q6Ar:BU8S^W'J7dH2$'/#l%T2i<+?pV*4ioAEJ6Up'Mk4Mc0,I256l^j8O5
%Rdq^KH8_Pu+-j^+X.b2?mM(YBCe)^VBL*6D*L7.T90\eW-"-Y9adcpc.jbq:9"2`TR=A$\3<=IWp.g2q\b\`Y%RL\]=\W-Lik[7a
%oFX\.*&P>u2ZjR[&\c)qoIUASB`@P3/X.]qC!,&.]<#l*G;*k1C^-WnFDj%Z90(,"L\Kmm2,>QO_orSQj*;Q66*=`ATl;if^d18:
%7$utMZ^r?)jmsU*4,IVi<->.gEcsPc<u.TOPa]#Z3D)qaZDT5*f:a^p2#MC&i"K]h.n@r1fj0FP+7Yf@]qQ)O&'L?1RM@JSOUgC_
%8C(WB-.okDL4/M5]>f%S"JWqW!d4:fO(U;C<+9U=VS7\C+4oT)7?:7q[fgX!&31uGQgW^>KsDA"rfT)UOA\kDF'S!/lp8[<8\4jf
%jF,$*q.;6fI+JGTMT=7LZ7hb#E'Wh<Kup#&gpOVcVDZ*?WSoMT,GKH4ZDjX(GeIg+/9`@h8Y?C<oA8*ZRdNb??F"Y_RFmcQ!FQXW
%l[[<)O/-(L9-fFSEB3SZX*C`dnr=Jp/c(\5=%aoXINW4d@L<K=8sQ,+g(Mb/E\6%b\S?[l;Dt<p#b5.DbE`lhP6B1gE+d?n/.4C?
%Xb,I_W]PWAbaB.q_>1%r"(Hg3XgSfsBCOfV^%C@hTr26m3;(]Z6;0(t77Kb2Eb5_0Y-`$RC;OjdU4$-dVL))o=J<kP6P.[ra[dh>
%if][^`u[?iZqnhjhjaQLLbHoTNNaAU)F+@2@t;+Wc;.[UNCVn.">4@ke0?[@OV*r7f*JNaIpeMIj2[DYQ?_)a5C>6oqt&n[r[h:d
%Sc`Y=4/Q-h*EUf[8)QL"qsS$bd50'I\gJ6&WT1-O(9DRkm`qWR,W8\]HO&,8Y2Fd^'SHtp0%-Ko(fb2._^QG#&.n&sA(Z+.%e\%d
%N4ZI"XL'oD4#&UkIX3hI4;[)R_=7479Gdt"$l4]ZN.H;P=;X)e!;,;a>Mn^fU%+_qM\0O_#,`FAR4IYmFPVe3$O*V,<F,;m9)lKp
%csC+d97lETDBX]W2GVO8q**bWfJja&L(Xqu]neng-ft,"*&IV'Tn\+:(iI-PFT>Jjf*JYUBt^pV,U=u\.\ni!UnY?f=j-7$BSM#9
%j]EH`@K8Z?[t,W$`.6([1;JF&$u&DkRPR..BKd0O9kp&42-ZetfPpRRi0,2KnB;/"^o\q?A\WhC6=JYB8SOhI=qp.)YsSnG@ZY$M
%]namfB#YMb?j(P+r3s$D$HY/mBO:2@K&RRe-M4*jlFUiPT-H:];XSbJYgHg6-C7E8^oi,>S4-?,is8k3Os[uUs40;0N@=,Q):WL1
%G\5X*qu0Yi-4jM6[V^2'TYX_en,OU*CBd\C5PkhX>bq:;aX^kKdT1pb-s':qgBG.KDkboIiQuPm\%Lp/P)KQl(d9+&kLJ<;d8UQG
%D36"m=C4Gt$"F]='oNAsW,Eg1PD=U60u-Ja&r]eF>r"kQ$&L(9&)O`ZA@Q&4!q:)(I4CZn.1R2m`=q$BX@G]0^4W3G#lYIK[:0,*
%H]R[uj1Ji#IW?:Ic/oUqS$F^#?:I%0h'1K<6@oq0IRnX32<1NI(ZLleK[$O5Q%_@:$/::X@qk1]EB9A;g$RNI6&n<.%N6%jIiL<F
%ph7jCLU[>6?j6e)S_ir7'HnJWP=oq"/8qjVrfTrM>7I/FF!a:Q^Heno^?)0pGsrOh$N&4D!2Z9+\$BJ6T>Y3*6bS.],kO"OXd_B>
%o%&tZG[/Hi@J<Kq_1UXFg/1(7[aDV<_3$D3-]D?*U"J]`)37Mq69H.V[o:@-!/"(=H,`#(%i@-a6<8W`8*AaU]gM'mG0)APfV1ME
%#gd,B/i_;3L4Fjn8V:G&dB<Z+CFaej!#DYqrUR.aIbj#"ejbcc1,CDc*-0kjn'47fqF+cBV]P^GTEmGUaf"kb!QA*)/dl5&WZGrI
%+D.OH0C<6L$gss5#HMMA!*`N^)smuJ"D^YU+a[8s!RWq'Kj3KriA<5Er^Af\4\_qT':#ct<Y&8">deJ2<:prSMtSWjQ<L182HZ<Q
%06GF#J!`;;J_>)d4>KoGB8&9eW1ZP>Y%6@lA7P49RXY'BN%Jac@\=D_LZl;AnO<mLLq/VT'I?!0![LhN[T6O4q1P)W4rSk=djlNg
%X/-iNLqH>Qdr&5?+62C[WG<UTBD=iT]`j<:HISj`rQ:n+2Ke=K[N]HJ\H,!#oF-3q_kf;(OqrP2'X4n5,c,,nKHfa4idU7Dl*CaJ
%rW=(\9h-GMZtuNr")#8MnkJg]luNiu,,7FSaK-OF/E7J)(Q@17e9n^)fE?BKo4\ob^..TJ&A%8JYWi'bYIsl'SNIE8OB*@oYb"u6
%#48LbG9eDa@UZ^pZ*]HrrFomj$X(Pu[E-&_BOr;>G$Z%>_E_'R?1;F922bN],G>i6&$F.gM[%I8>t)P>R8a2^ZaFLe;2A'[IcJQb
%,)UG)^f/Q&])?-%5P$40J1@&8)b<Dg&*QCrfAB5>-Kg6e"-lk]$t(iib@A&q*nQd1ZQ^CH*nOj0E08p.i=eb6p!CrVp,`fj\e!i3
%U:))F.1`;_:fX/m_pg*;c&_,sl:[rk0OqdtUn/kW6#;QD/c\]LnoL!l(nK&P%R:7sPI_1F&.OjH]$=S5&0&d=0:D-M(=f:b4q_77
%hCdi&eS"R@)24<S947BqYqp8Ei0k"]GU!E719u=^YQ'1P!XUoQ\HlLtetuXY!0bRV:F-[r]rh+fYMRl1Ih=o289ZR@@r0ge:"_Zt
%JB87hhk-M#I,b^]oFAqe73k:[d<s..6$n(^J5_-RlFIRR"s8QVSNo7qbTl4%?HGjWkM/QQ3YUL%[9rH6n_<TW#_,aOE3n&__ST)$
%!B08ENNMCp,+!e_)rBogOIYC1j2Kk`:'1kDCtuQrKD09!+rg._;aUP$cZX\XG-`"HlGqRfaS^F,f\h&Y2-q#kg^bE7p:+c^Yr>Qd
%1OqKj.X^JW%Fk`dk,HA>TG<'(W.T3RXreN3P4WI6bY@W>9>Q-;O5Gm!6`2;7ht0lcmtHBmqsZ+t*U>F48NZ!2-:IU5Wu^!E6=Gk3
%;M)Em\<KSLF/R"MZ65l'r42-GfcTP7rJW-48R@N*'ljMrGd<AGG2DU&MXbcNB-`'.7DZ%8-I_()4@\6Ej.D):d1,8*`\JDq_JNI?
%%ZnE+@@Y0\#.rT&BjTYAC:<&nP+JD1F:A*ETYg<8Y>Xe^4`o%jE07[";D`ZQ;)DQ84ooP[J?lE9Cs'rQ]K@:GC1>+8^e-99?d'\`
%!-t5r`NHL\`H-GGd:M+d63eAt61"(]R2#B$>Mr\6oIUc!V)6-`lDWg_;Y&em&m51L"M5bE)SdY6=<bf($8m)1Q7ZZ8[@DT$(fC^-
%9;.uN_6(PEThXO@dKnbkpJ^@'f$O'Ik.<88O]7=.Mnh=</Z>_o4[&)nITYFBmTq2smR#)NXq`N!aZ8,tCiuLXO0,X)^KErnB@Wll
%.3%6L:I]0sC17LDdIVH$Abd%!MK9^\1uil7-*)U'[?#"u-'Ia\S[BG&3$Fg(HK=YR<"'3\gOa]h^2sgLoI+4fq0m,WGT!a@]$_)`
%;dq0uW;NdIE\3u,UW+i+pf"u]-A7Cc<>&]L2&Z#\hfC1A2Ih_8P?=lQ_N)(62P6.)\2]nGaj3em0$b7`G<D3Q+]]`:/p[_H$#Q-U
%j9'%uS?f?OlQbKccG=QRK4NBghM[N1KNi);YsE:o\*jn_H'hNsdpPa_16hAB#^d`_L+';UTpr<GQmkL2_,W5e'o^WKiHNr7SVA(2
%0u2j:R*#LlL1a9uDfP%YjX@<sC@Zlc@YY#!pGg?gD&\E\@T(//7Qk2M9rsead:HN>ViMEL&n9_nA*^!*+eW>7e6jO"c(to!iQU+0
%JAJ6e,@UIVQq",TT404M8r&aT*Lu'.GDcAp[S?AjXJ-pX[C^=;NVrA3[DS.$g64cFieCPE9*.+/W[Mo4K)#Tl@L55"%B)$S.tY#k
%U^_)^Lm\in+!W<DmP)YZ,>tp]GsaD4N_()PMB[(Le`KTe;#gi0N[#&;8s(/"08)XGQAfHoi0@#+JoBEcKV-lg8L)mRVT"J.&BQN+
%#86+KR0d[(1rkQV;si:m">hVY5patQF__!LO+-0[":/I@NRY";4H8S+Cm[oa>uccej3[2a21eaO*#Y7r@t%VN?iuAU-6e,J-_o>j
%rqg%+\)12qrb!59`oDIT+q$RVl?Ar<j#I^$%PnW@RRMP#U,*\1JHTBlR;@9h*O<`^/5Qjk96YT@CK/.-^]Ri3p=^jgLQUYN3J%hA
%)oTCbTVR_G#T@?l7Xg:Hj@YMs4XO*:Nm*"u&/54W#icIu<BmAE//)Z?=9!=('D12XT6K<6QqF\i.jbHlg+Wj9aVn;oh;k$A:;!=@
%X:fkJUQFZH/!)$`P#P:"^cJg?dr:?>ZIY`)=.Mg`d7Z4V=H>"a"r<ms-1MnPbY.OKH$']3,>C1p;hdH(^*7Tgbd%j?\r)LleA1?+
%:<.D9/3K+l'u,-+2^Dg#8AD]27qQSZNPn%/nR3\j=gd2:Z97PcFH[(b>Z"Gq%eQlV2=4I")aqAk0eW*3-c6p7]EkK(#u]7fe7"H1
%#<HX*T:M8PVUG@qon-n<#nI*?E0:&^C:n:q">%l05Le_tp_[^tJfsZD5gS!!/>=[,iceH5n#@!nH#&pX!!+K]eNf--pZ)@dLkDN'
%=4EPMW1dl^4Lm8POhtC=LGQ(pfG00?Dt,)\>4jgFND5afW@=ZN_LigMmpPNG"HFZjjt)aYmimcle+bm7<XC)"I86uq=^^\uq8R-]
%]*,osh`^SdUj5a5d_'FINg%oTOHu7tk-f4M1<*eU7"EgJ`R<C$jT/1Gr5$fr&pFAPECh5g*k-%!BmjI4"O;4F'Y.ZcaP8S#*$9#c
%bn_i<ekR;fIJ4"<V.qIG^lf)mBc(D=i*Z_%F0LY?ru._b@oT-:#t`=?6^Rur_Eg;VdAiPaX3GeVm!A\W1X?Z%0PUTJNBC@ubkD6;
%Nh&>F\7SB);=6'D5u/>>9csTYhkYNfdCmK>XUsO[6O>p+hO<j;"GL.RO?5+TB!NS@5gg:(N34U2s1lu+9g_<1.QbQ#R1b*N1@(Di
%!kX8feAY>Jn'ca\i<dN;_h/3tZUqeVr?'DK;5tW,?Q[fHJg.&eCsgo1Z.-djrj2V?_$sA'?I:A4q^.jI(!G)l#s<u?mJ*rrJRG9-
%",A0-[7@`C!7N0[3Z#OW[ht'DKLg&6?K,IF"#m[aNGlpQ?q$Q+1]?,`IGOKsZYO'Cd*HK-.Y]suIIR"+81b'IT.rR.3-=ZW<P7J'
%TC;X2l?LcEEBi;dDP6(4&8s\]n4D<?R=.6fJ>C5?J?\mZ5bA<OO./b>!)Y^\,`k`KMq8oH.LJ!Ak:a)IY*oEj(jI>SND63<@qn]G
%cq2'6>BK(S$LH`H>Ic#Z#d,6'fGu7t##_/uG-Q;V+AeUGSGa(+`jJl_kZ!gO49EoZH'F+2N<!P!(-s%8l5?b7STGR&V2:Yn8Y_,P
%X6SLPeB2Ekc/HF5&667%WOnlr@YB8LiGe(\5e$+RL#&V4X="BM-nk*I:-D/%=""P7f-R1lbe[46<Ja.^Xlsj]*O&nE$3nIlZc7jF
%6Yo$7NPDXTGBk+61#1)8&;*%1h'`8H&9QT?o8U&o>=Bc@Ab.^f<<F,EIH.@mlgj>]m)mgp4W1]g*I<"7p@14OTUoEAXCFp."=&q>
%+H$In^YcHOS+-,)lcsP"1-Z4.PPgblg/'K2IbZAMKVRhued]9\R*VVs>'-s#U56L$E11N+mI`&1rrE%H0B"'/1I[9P1BArC9CcL7
%kVCHL(b@\9g))bR[i,n;fNgk""`?_#bV+g%HkBnX>oF*ma-RF<)6D$._GCM1?F+9XO5Fg7(71t7iS'W_HRP[F$no>aq&/SZP*8)R
%$O"Y8=EWk/O8QN4::=?S7M6V26OUnHo`U`qI1Jn#p,n2JnUHH/l$_pV&&1P>r"*MW(Bt,j8#9_P"_2Um:d^]HFSfp63?DhT&%qBX
%*6_9CLAlXiM1hId/S'AER,tLDcH/pr+epKOpbpIu:M`DI!KS*f^b9MVdW')!BJlNkL`M8&ef:Bdic[1./*P<aMR"ZIo*/ri4\)X?
%I0Wd/2JJU9+Hp+\L_*nQ3XegXGTA.4Uua,q+kH4Vd]U"k3)"aH%*QlS''h>MfBq#Xg63]Gf8RPLil\6l56q*f:H%,+g<fr1c.JX5
%bi8q1!c@G)$=JXF#/6?kq!g#&YNfK8A5j*BrMulU8E]rTU,!Y..f],?@fDhokl+6DZ$uL%(/Bp?kH4:SZ&ntU(=G,q:C(Ji"*HG-
%JQZ*B-[;IVI<%je-kM#@9OA%-OKn,X=O_PIGU<U0kqU>IKh#S4lg,f=UsT`n"SM>Yi!Ldb2]TXFjXr/q_+0hub785X-Xr)sBTJMd
%dbghB-sH[G=Mko\c4G,]W,XdB+c99*YpH*E)a9GV>Z^dEKXj_2*Sn0E@;?-f.O3M.aXXJ!TYgTp1U'pj&1BV_!$YaE\sSH)UkCH`
%8r&RFDt$H!"$9oHH\#;):U^`O4M\IjAS;pKYfb%Pi>_/sNQ;0k&Uh;crAjeic2G5Tf&m;AU:K_pb5icd@UBsbNE;;L#ht!Pa:Wra
%M*tfY%^j^WEVp5Napj_/oaC7,hU\!02%+W?pRL1hm;%,2To/V*8as`Kk+Pg9I!]uE69V.<>WjT@rfF!]/\HK4M:klY$lq,p/=0iE
%4aq9</BA]N0@l=1U68]9>U2R8f\]E$%p@.@pZnl\5)%80c\O,Y<N#*f$lGQ0acsga$;<5AF1N<L\>P$Z%rYSiHZ"7UC)ncTfV0Se
%0i!MgYQ0MFTtf!FX1;M?D)+:FWbp'#%&G"3_7NXmB#1?aY`j=:IM)U/D7AHOT?RE5\gu&R6.L6ojdDsBTAL]#Im0drh[#5L\r*N7
%K+&NP7dUPE&X&N^QIHa1N2C*]<ggneL\S;_%BXM4K:R,A?e"C,b$Q!^"AeFqj]2n$kNlrn<,QfJ1KIJ)S#SBBAF@S2+m+_^`*28[
%mW=d(6CMZCAnjBq#\a_H-tG&Okjm^uNuIKJ[C70Y2ss+)3R,bN!"*BTWCE<>J9%IBrn,EKrNhKMgL0)X-+\S\b'[=.GDu8:W0t^1
%[sM%VHQ%,9@rlL:Lanh$ZEbbuJ6\8-@=&V`UFQ2kPlYCe%tSnLH#o(f4"KW\S;a\5=n4pV4,6%L4/8;\`H@3Pe^l@S0-7QLL+MCA
%+%0Vcn<GJ6Ub;ZfZrt<Po]@6>[K#g6Q>Q$M!"Sbm6\(;>J5%U\fuK$]U6c0t8i,+@QF/4r>thJ,h\Qe"jD=n(R.qH2oZf+c<^r[1
%f7!)1$7knkXMcF_K&!&hoQqf`fc!V:\6Bk@[O>pVm<[Yi-J\nZa_PJX'1>'@=mJ_Ll"%n6=jSD/$dqYV7(qH.BQ'_?b)'\1fL*iV
%k=RJ+_VOX3j"THG:"GHUK0k+<G29d$6*5?X&'"h6h9GGI.fMSB9:C5;*&om'rS-'0Ac3kD't@0aa&kQ$_ece)Z!5A#C%c?]_#^M#
%M>$*IIL:F?,o?li#9K1a-K1%g+DLI0$pi+O`X&<Sk<$:bJHJ_k(]7^P#rk5-g<jWihLD`a40A%NUg5:9nm9'kbJ3Yl#qZ*#Q6KZr
%FD?(_2-(%:,sVfB1&AfT+/+0B`3.YSR$e)j-_3MT2Xg1NYL:I*BR/Mb"_/D_.Y)k1UpPkohH6op.St$mq-8/^5UhnQ1Hg:ap'/aZ
%+IX=sg*>;,5f]hk-fV]_E514s,e:A#C.KS1V'puDpD%u-G-#XuffUcC<RI1r@8Voh'QK5])$GXk\c[8PL#Egs)OmQbORXSG^QJ:a
%dN-thIE07&5!0;bTrtCW5/0qerX=2qW3T%O*J`/'h]`+!.rnup[;H6;&ul\4G,#nG9rd,L;_g0&Y]$ZU5TqBRdtCeal<]bJ:i4TR
%#U=qsYPRE1nP>&DZGB_am8Ysm\S@lV(!pJOcfYQqm-Il?d:0dHDid_A?ShZ\EuV6*"@Z:qT27SK8Y9?k[/Tc6NZKYj:8pm;T=2dn
%^TA(B&=4eOY37KXGFnKZ1sp*82B-R=&P__X,CgM=S(s2o6s_db`H"j8#1o6aY%r@'Yo^l"h;X%*b,r/+=9]Q_QMFFn)iBlZ#n&\/
%KXkhI)cNo[>Jda:fP*\:*&cBDWYf`)n`+7@CUYrg3+t`RMi,,PH`)8\,4T2ODHq@U2TP+n'./T<*]E2K+-d+;T/I]s`=Fj1Z,RYN
%jkFu\lbKhH3lk9)5WiHck#E+4DJGigl0\@-c6J9"rmhPnIN-[(c:<s'8K0`ZngHc3@oURPl-,,@qAX);G6RC2`pEhE#iSELOn5[/
%V)mN[GN'?G<R[9^+6(<-^^6e@*:m6U9$pb5$Q+"a9Tr:MOEa;Z9)B,%6#8iB@#_o@WJ!N"lk*([<&ka8T-B_Pg^HgMOLU,E-29#;
%J.HJIN-=?<_YFJ@AR4QgTqg`[=.d]hhGES[$ImF_S?!FU@0K)A[.r*[m54esOjmka9DssKY$c1"3Ik[SCR6jn,uqj.Z`Ys?1OM#[
%IC7uuK/Qg9`,W&i5lg!1h4.;3;GW9/>T@-%dIfAgJ>A132ehs%.+gP>Q'G1e<ZAAd1rWel#7#EsO,\Dr9FKN)BQQTfkHOR4P3asc
%4\&T.XR*')(5ZbpKH%@Qn(<m[@3]FmI"Ken?Y9U-U61))S!E9a[_.hOAJmF+)g=o56"6(amuRJfc1MN4/`^4a/!W7GJ3`1/?AC9W
%+daFW+c;_=+J?ZM0X=]X/rq$L8-H"]9L\pt470T=B@rh`r"O=H2+<MPZLtIsEFbq`(n3H6QdXL\kB4):SZS?6_!n7R9,BL,r:%t%
%ro`m3qfc$!!!r&NH,\u#G<3KY6\,h9E.!1H>D,6-fOU`e'=Blf3iQkkN.cKJU_2G'U"43/NH$L(WBe(:00%V&:C&1F3-59IZi#G(
%ma',+]nYT"TLk;CJtF:Y7Mr7[gK:N?-GuI^j9SEX.nM&Bh#TNG+[+3Q+VIK$2F23DJR:?\;rE4l;VW3u]<?hH-<i<"bjH'<0J!2j
%P$/L&3s;;mW8sF\!#J4n+iY!S_j&4c0M`o+-a9ZMNdVU?DA?Sa`C?!sGm)PJYTIVh\>)CB)0RbclOhX47-!p/>u@rT3(G7G?roQ4
%\\IS%DhnAlnGRr"6!;ZB+>koiYS.p,4.rB6`S<.8i>_5(O0rn$f8U)tceHC$Q#dmd#*^Ts\[Bj@;<.2g*>ejlQX[h%YMtgT'Kt$+
%'bEW8>3kk`,=mY\C/1SV9e&!&7]X[Ah3*>UlT(cCZ!&H?JF2heh+O9#gegt3CKtVE>9KJLlnXKbURsK<52;7of_iZX__.H<FB??'
%]=\A/8dd+J\:B8,aXe>S(nW39Lr=ID@FTTHqL+jDM>#uY@s#[<(mRhV4Au"]J)ZgXb]fX<l=A4N?38@*R/<Le?cn'p*99Xe':a:u
%,"(*T\Q5^8;o%N,(Ruof2Uq<p"8Q,t7!]H><\'OYWFU2qrjt]SQ4ch/5qAnW:8\[nA8'uC:.TM9iVgr>6[9J*Dm@[UKZnRHnYJrN
%GoO=X*@/Zmm;H3$U`P'uC%H>3dAuk$/nTLc>-8l9fce2W54?qffZ.5aW6W7uqgFj&UY.8U^j$<O5>`\lEN\V7p)E[$g5cDD4&DEh
%KE..e6f"7P[5(YY8j??VT`]8Dr54/,Q"!lrblmm]esJsMZuFbA2f3?[aYG6&N/kZm,<X#M=3gALHQKYs4V.`sC8g8&n*RkYchhLj
%>FB379!)^G7KUnP<,)EQkG4(Ld,)<MY=Fbt;Z\Z!qbdMb(@bgS&sBRA+-V8V4r^MorbQ7cfq-:94NK>]"i#Ff`s[b6l.5^53H>Y&
%rNVh9VN]#gdu+RGnmY_LKOJhfp1T'>qBsS`:io9lkL_59*l97f;=#dL"?8YnoubP-UnqD"aZ[ntcZba>oZ!qV6ai8<2$1iLPYJu/
%fYeJG!^W7S!h5T1*#pS$g+Zo.:'*-%(\q<DVdn\LLeW:O2JM<h=E!da3OrL,"H1&9167ChYo:5G[5^Vc9;X@aB;XN3m<$NbY49sU
%s5(.>*KXeEV6T`TJ,d]Eo6\gSJ@e:P^%_R-!m$4q4qY,Ta3h8spEaa6<@B/2O?8q*UEYN/,uZ?BW7b!ZP1fn`M)C<)WRqBJUr-'C
%iXf(NmUHq!)=;Vs=JZZrcO1@)>PC;^3'++CoSCqRT%_Z&8EErkefk)$@aoLDOMlO,XWjmXnT#3u[&E4?#87(k\%e8X!8;;4*B$d4
%31[oqdskeQ4u9/h?'E8U<U`g/+oRns6^a6_e-@?9o;`;L$;u:K"LKSH@0#UuA,6JKC8<IVc5s77iX&0004W8&-`Z3,11r!`XQ=Y&
%reA<oSp`?`mKZ"+^]Pj!*?`-Y5aWfD)Mn#ROp>bjh$8/@F&0aK+n2'(%J3\c]tn9UL27FD26FNFe25&Q0Iu#P,&s!6QK>>,[RNr5
%`8,jD/`Gnd,]<?Tb8?/=hda:VE,^H!`/m8A\b_+MSidZsPsuka3k&5O@ioj5VV'Auf@%&V2o%TceW*iN4[LK1^e#?9-.76GquC`a
%$c-_?4Z1/BONt4tn79j!V;I+'J<I[ibt%d?NFJr?XmM4!Ir=lMOfth@4NuuJV]_r@O(XS&I,c4BcCO#';>S.D@Rn6*$%J6]r?U-5
%<^lTe!UjeFOgB2STFS=Hr'm8_hhjPYjM\he]Z8.g-BZ=e9LoV'nTC91R$ueO@E\&CWhm-5.*fc"9*C4UF""W+3"JcCS%Pm5KtBX>
%-fb@U:t&61'0g=kP"A-LZ<#?^H[qK:gU[lC)%V(N^hQ5<bQA"6mb7@4pSNro26HH<W+K'Bh:uu*@2.9+4B\il+RCYcOI0[[T*k$u
%!DNi@86qd99=u<V,jh5MTFV)#SsacXWd'XJW/E>t021Z+SdjG%L+1S-DB+Q_F:n]XdNOP$0pbNf7/U-e&E[G)GikALCIo6]/^k4q
%_?g+m'J"u:7E32AGJ"iJSMKlMUej[qgG^rRCej$g85VXl[Pa6qBn;BTiLKVNnK"f,nlC16IUe+b[ciN55dh>jchtX?!"VXQ''K0C
%'m7-Se!5;`UMu2@T,X6rf=Q:,EJS0uTd\74$:_NO);S>G<IgSpmG1!g>OU^H'B*C&qFD^AOF068-(@],f/_;7!QuY:6S@%6Q'hl,
%O%4.*.1V*u+Gs7`:A-Yl%:TIS2t`S8<j6SE\-(b&A58;[/c"EZ<8&3'g_HlS7e,_p<2PCE(iGjRYP*;hQ%Si8=bJ.*$ACN*!ZGu?
%l>KG7^XW+qq,U#'IhW1fIE^k5mVkKm9`7U$MQUo;j-'W)YZns/=$%D<Prq6lUH7sg+B:6r>7@n!$L'iSYY3?E&Yr0^Uag$efZ9Ej
%JaCR$^F$3#F\`lX5[XCjpB_=p(hSj$ZU?pq(;M<?YR$ObfEt>e2^oFh8>LbATY9Qc8\3N='!'1OCOd"e>>N$[RIT0iSQpuX&&I["
%V'dZ>N67en<ZJ\f[3e-$!"J5a;m6l($e:Nd^UjM=ntYTr"uREi3h-J^@"+WjmUCpH,*e)e*K#gtBk[5#;nEnBmB?$gSA<0/,J@='
%Gp/aL.8EMRSH]tk3:Hpagqpfa9Dk)uGoE^d0OQ`lmiESH$;;>,DYOkC[\2guB`j<6nrpO]0@@^DSmU]iAc#AH#)nao-SUtnMr2rS
%b9fcd7gO)8c8rPCoo28CIMe47U-;`7nJ=W,HrJUu)["#iaj'r>Cl<66P?jC%ZOHr'\DUZTW^PG.790bP@WOQt5pt)QXtf^&_?>bR
%73_CL2+#)5.6KE%Ch/VN_td$FDJ_^$aGr\44EIXuiaC@a:YS\d"!!F\#-NQ/n`7be$D<n%96185W"LCXHPj`$qjXADH]NrTlK$`e
%E?mc(S\*QpYpDAuU_/l:WB44e,<t0SN4p&nprrIudnGU<(_sWtQ#VQeNOPBKO(=eAJn69ZC9ZkhdG*dPWN2Y_:V.?%O^e"(GR(fV
%MQ"+22Z\fn]:1=UV3)IBoFkT;75@\sKFu!=0qcB.KT@^jk[=8t!Hp5(f9DL#QX666SiC#3kBj>C7YQ1f8jTJ8r+7324@r43HT*`*
%iDDgnS_"b$)+R[G+_"?HV-r(NrY/,-.(?D0E5$(?:`"YQeEAZND3DmM:nkDM%G)LL1-'qIi(+/h*g:qEVo2XX@`C](HL7:GG0tO;
%AF8Lfqi)3=LnL+1#9.<75_S=PS;/F48*'#,E4p24<KQ6]Lq&nIqe@n#X]Vfs+<%VLVhT6jp+fjhVn7@eoq9%Eg6*j#.9ob+X+Sr8
%&Xu+IU"DY'Y$E[Jr]pQ<,SX:%YT%'Wq88J*X!3<]knpp5`ng(hU/#)aZ"H2:ZcBZN?qAjX>=_O!g]OaGAnaRlF*AS?bj?%@^th:R
%8\kQN)?"ri6_(9[&qJst"`(n/VYi3t%P]o%XorcTh9s:+/8UG&)bHo#%;T9VYcerifR5e2d-s*?o#Z(R(kJ..[ca(KH1JB=%HI3?
%:-dbmlI&#DjU(*cZluoW7RCsg\Qa&u6c6treJFD&g"5WXQAi-ZC7?]`Tp/kb0Tk%JTjFbo7]+?Y'R;.2H44n@<@3E+Wbu]Nf8uVb
%3\OHi&a>1/gF2VJZBpeJGMNlkdE6h^_^Z@UZ;@?;fu#cZ2J7=9MEDs5bnU^5ImP-1PLX3p1UM`Hge";@U94[WEf!R$R;J^@6GJMF
%f4uAI$clb?oqifA]D1\'QDie%V@iqgp16Z2;Q,))!A$DjaA3\UZ[t%I2:Bak;kf-ZqAc:P^W%d#$:&dD@tW74%3()6.VLqq:gl->
%CJ<d%`PQS@qLOP;4B@@Z^LW-Eh('TLmkZL!8V"aH_M<1boZ`J8Xc,nVF'*Vaj%BSdCF$^;4N6&KMEg5h68*=512;jqU1FX6kae05
%c-Wps6=s2>aFEh#P8e\@TG(./Kj>:d;r/EXHL4tsA(6TM1.haMb55Jh-/q]-&rI?XJ6Y8=kQss$3*+,aFXru,:C`#]:OgL2L.eI%
%L(FsMcA-nkMXcaI?BDBtp8YhYe,`+7c?$gN"cQ>.A6QF[HA%i\%.)4#H<;Y*o,+K9)ceT0d`ql$=2[g[%08o*Q-.bM-#PhH6(d,f
%J)\iP(G!'+.,O,"s8V*rs4<;0NFNq;NHBY@S:a#!T>Njhc-:K6/VN%+)ZFU;pC^475GVe`mgVg=qG\@0c="@*(0F$ZR&Lq@'.<@D
%SIEl@+:uF)>)u9p.8$V,7V-IqRZHJL^bGF=7SnQ+p=Y)O^bXh%>0;hc@.p5+5Q6'nm3F)D?7Yl>lUo90WIE3=Ep%?JHAB$6T:^^3
%)V*h>Z@j7sV6:Ne7]!o<(:dbU%8Y0s1$,Sefk@6?;?`:IJ]/]Y.3k^SRUn=F/:Z>_$LUQR32K_#F,Q[911JAh@f#IQqT^*$3o?YK
%=U7cGU]VLu\\m(DZETaC!\'2DdMXo2_td]QV*A/*j0=-22*X/p"8ma)cCTLJ\c*nC*FGGO7@3VM)@TmLg&8R)pQ,Ji.?I"W^ZPm'
%\0\`YRh7>MBGgUs(H%,L0(',1e@9$0"<0iBg@sf8L=,qt0T)&,"(:5?c,c]?*VR&8a$"o7?=Ssb5VMpp6CLaVcS"j2ou]ag3FJ9F
%)Q"okcm2N2Fd:j25L;B=1qSg-DVFg-<KY25Yo&J^m:1'LmI:&Ij92]!]d9\kb1<N]J`_`6R(QG%50J2dfU@:`:=E=CZ(*`+gqNb$
%lh<J2+!E6"o6;>814KeGkj.AnF/kIO=i\:Z<\%i7A'.98_bAD6,9EJZI-MNi;,pJ',K$83J9p6m-B>&g=U3Xd1(RjVD^>,"^d4`p
%H"b]-6,7lJZGPIQIiOpg`k'EY7'^/=9DkpK67tOs*[>dYVt%iiacT`A5M0!c*d@8u00Dp#H.:mM@gp`Ln_ZFA`^E@(PK"QJbB_P!
%=m\T7`GK4+S*Q"T/,_lr[`Am*dc(JB!^$;s+'9fnfI<%&8`K"WP7lVC7*Ib52G["i_01l)4^K'XfiD^noZF]')+oh?KmC_^#mMa:
%N<Q:BrARdApO3cB/?+Q$&gaAg!''9Ui_=qd8djJS5E&;.b;a9r0M3[Y:aTqrp?>fgN,s:JG4:6+q/>*Ml_;)Yci&@[]kMO.a,r9+
%5>Qt4_sohI)q5foYkI?#LYiB">-&U`iYCQdXu2mQJKkIGC]T'lj:D!ohp-U)C[#_eO@7fs/_dRMT$sUKp<(P>8R'e,]af_7\P%]F
%?JQT>8S4i]\l;MI/P8!l_Z6u#J3g_[Z[Ked;XBWaB6Tas><EMAIL>[<o@,#&t,dH$Sb3VY,OK$IDY,lE*E65.G*j_NP$*(Gn`^t3I
%i08@E>>R3-W`<Y,CMV0me-<8Q6c#MqgPq;I6%a1:B@VbFUi;2I[q]U!_+P)oe;a!m6Q^q]!@\.sOs%4@G:t..Bf`)Z.#VSD.e/Qe
%`\D.,P(4=r%S]o(-@O<tAm/D4i&CQ^U`@\o3/B!k3-jPV.F*KUN5LlP(e4``k%t9U'$Q2"l'#9NR(CAh.cEilfdo,!0nCI>icH/8
%TUPD6EoEDDjQBiIdL3B=0M*HR+;QsPBiSA)0W8eJ6b<Q3Ej"bC&mQ\(cDo.75m4L*R61fGO-4b,b"GL>_Fu^5(hZ\)Sq/VW5hNUR
%jJ]XnJ.Tk%<m55lgDn;m$aLjFhk7FcZ8U::Pm]2&+m?8V#UZ3T.9_`!)h*I%7E6X$#heC<,bc74MR!iL8mZNrhJi).0.DO5^D'Li
%iQ53*\:o!/-/_O7`>$/t=sA-B8pX&ratI?IQl<#l`SneGeS>PHD=T!@a>f,W$O+%+(aS]K't'13V.b$en8spe3"@3F4;-Fb(&5dg
%Uo#?V`^@U+%%qEQ6Im!"fEi!d5rVAH2+oOoRBVHFj#gp$pF\*sE^TM+NGb>IO5)ZGA%KAl3;<n!"J]C6K1[]r\$R@'O4A(03S>>^
%Em^,nYa#8Wnr*0WGf/]'q-SIYk$b!/dYXJC5&j@1M'K;BF0Gf%2`U=nP.1cQSn.q*hZi8[d*h%j"G?eTR@^eB;2^^/,hYf&nQcI2
%*>qEH^g[L.-b7>[X!!RK&4eMd7p<\jQs,)_=ou%I#\Y._mSa0EhM^nql^K:[_l;WJC5!u5P:[JH1HpZBMk%hJ6,&%;WP-J0a+QMZ
%Efja0BSm#NVrAfENi:HeV<4$(TPd<$e@f!Imk&NFBa0GCD%j&h$tV9(PorD4Qo*?a6Xnh3*@b'Cf=A\">`[UgnJtl4dh^M1Yr@-Z
%GU;LA-:H@U<SW0lAA1%dL^46HPcW*b0hq/3,mPqLJQUaYj@m&g].`9YT7l#H+HIIo9Nqo:Ube3N<4.7.,\=n.;Iib%8c\LfOm*W]
%@%p`59%cO3-mM\9!LZq*A#D3dUVoeq<"MbHiisa,4X]M-*.I"CEoA]S63Xd%m+^Ah!+@55Uc1>7S0L/DP;5(RG]/as90t=1?puf)
%Ts97_bS^^@4BC*RPc]U(YET?f$>-O&@:=42';c<nUd-b!G*:N$C"@MO3>@:b7.+Ei"HZs[e58+Ad`r,\$fuaI9N_@n"3mnP(_i:b
%<.b^+4r1-K"MY4mdTDMR4PH17H+/B=3re.J?_BiO\n>oM*bm"#Y*uN8"d>crj;R2bOi^m^6C>TZW`))Ar^8"]EqN8g9T0jV0Gcgn
%PKO"DT5I9@7W:ma?C$8OK1$iX/omCVR30XE5k^C&WWdpD#79O%IFonNZ/caD:;J].-)@7p$1=^K,Pmoka3!14<'NiNs+oLeE?Yd$
%(*hp1-+kGdM#$':f6L%fSP<"`0N4)*V5(qSKdSoD&W/iFr%\m1BK66MA$I@``V?Gtm0_aQk<e%@hY7c+)e"4$':tASGu!74L%((U
%/:\kcAemAO-H[HI4(+te!RVT8_Uf+QU6K<D-jbdE/G+QdI<_=IdQ)SJA'($L_QCl51ALa_OU!7U,)6kV"+!tG2.4E\A]$o2.fq^X
%"q<q!#D5_C`P#[OQ35o6'u3=)ONC*8#`CB%m?j1MQPgLN=8p+Z-YWX\>T@PE-Ge?s%e@L%KYSU/PSR+ehmd;2<m!J[W4aTVTi>hk
%ed3.Y!i#Z%9%O>@04t3QJm_a6!)5sL#$ecJ;B7j:S?.`ZUb!IW'gjd0PB)iKg+s>d%l3TGI<)@?Usu*ur-ZBu*0]^(<J+P%aDc0s
%32a2rge,d)4m]Mb5=$kd(c(gtk$-1IHhbDVb!T,&OQ1q\N.=3S_#p&s-.f+&)[Jq@2m=BTT._&k(5?J9@_p3f3-Od^$UAG2i2&T\
%'7`>%,:pW%WUta'nQiHA`7ig%W/i0S\9Y>l=S;UN8Ed/2=L&\bN#4*+B'mVe:106NU%T]@'?4g[LD5/b[O;u\/!DcLBg)QVE02V`
%loTi&LHW7Q_Di@gnC]t^+]Y"'`05:+3NG-XRYeR\5]/m#5q4E]TElnP$.4/s9M$T.&;AL9cIjq;atc;L5$%7fWr]N"Gtpd70#`D[
%?OIFg(#c^kAB\tccf\?56e5!64nq:4LHdLc+J0J:ARkstmZP[,fO2K;EXYk#?m/s1N_+*s4SNU@S4`]!U`sUt5'Xc4PcPlP'V85>
%D2$j>??%Y/@jn.rKh#rhI,=?18!FJrEP`<UPO>?*Y)B/HC9mV*O^pnSJ.V4G=jIte[/.]jJSYH/e-W1I@o&@@%l9\=^gNNlWGp;T
%<^=f&!`0=_Z32Z3"nGSVA:e6*=Akl/7]srCKS+#o!pMkNo_`ZmE*!CJ(Jt$4#pf*.,:u3[_$H)g<)3'NQ*MVq>JX8QX:uLM8)n#O
%dIoV2[8\;DT^:KR%&_7:HDJbJm\82^(0hf8g*9Ap#%UeHC5u7=1mc[*4DEgp"d_%c"M2g(FGV\+-ogH^:44-.N/rbMCC.>gW6r9o
%WfcsZHIGq#-U&:aJjPUG_GO!h,odCi38B`)7"3n-/fPG?59XkVKC$2B6q]d9#)0B0X:Cs2k<KOo&/g/a8d8o/nH,YM"liHf0/l'E
%PM$?^%$M=Cnh_/el?W^p[05J[3`R0D;iD+/+=4C-^I54np=`jJdPGS0W8Z$Y=\<:ih*^/%ohkiIMkG8ei#j0MD9\>$gh'g3V'hcq
%)qd0)0_(lIOMa/jb<t)IXKE,S:"Hd"K;%e"pieU2Dto9^Y:aAF,[LZ>YUiXn$o9aUirPK,!a3^l+T-$]N6EisTO%hN&uu(1E+9L$
%"p>Vi;!'8MhHu=G8d]>nJB__%OtBcco(/)2W(&>m*"(gWTVD#NEW0E#^DFM<B6Im$PGfTea)?4*^]_F*>(DZ&E)%MUOFo#"9HZ'$
%O?J1$jM6m]!h5r5=X\<Z<A4L`ad-,3%05SAiqkcU5api[PmXi66t$^CTh.o:2F\g5GO7ki[YuI1RenfqG=Z++$6;UE!s>?C*S#^K
%hakbL(Rm]@oa4WsUVf/UK39de^n&uA9^&D1"S$03KU247ia<Yl*SBq-en$47esu"BO!UOXF,nt_J-Jp\N_V=fOeImo2&c`qEQ,om
%R;9SUdWs]Z8`pq'JsV=\]1L.%K"rfR]s`oQIt9\bIHEQ<N[?f#G1MT?%Gslr!kGhZZWd$obQ\QZN*?PU7<6V7:re+H&YL[:c<[4]
%FRfL%N!@E;<R?MY:[Nk/bHg2c`,<R'@`f<3]KHtD=K;C!Q=MPi&m/$6kRaLZS,u0t,3PAsSq!"'0d^U(Jq"Vka5\$"9OD`[U0.7?
%eI#;q-($,n(hYLO"g*=!"K)E:%G8C7Ua<6o(cXEC#/)og15@R)Y%S5A(jIQ^[jm!/UJG[S9U<+%$P,erS:lu$c&tQ0l`IgY__#Zm
%E`b-e%,WW0o1BAd,$=J?HCU16f7=I?+';_k1P[jGBE]$Cd2WOG`AD]@^gi:L*Sb?c:#!$pd"h/H`@@d:XBA2I-_7n.B]frPBgXUT
%iPCifEc_CW%4lYqVA^Q!\9A[[lGH3V?H.Y'XfFC/UEF1*`-S2C^bUn?_FI/u`bZ(rMh1Z)fTcgegd""Mck'N=QF!-$f$[.3(gG=Q
%,QT20Jb*qr!R1I@$c&\p5(QXb5_Q8IS<F@"Ycc>q/a9c#*k^M%-_tgfTF-qn)^)J=0N]P1lBqf2$a5PO]-q6d';nAFc9MFZ.Y2Jb
%:UD@c:;C"*Of49h<jcX^TB;hDbe(H.$+A'Q^hF1Q$C.ED5RAXnYr"an]QHSl&$+WEiV&Q-,UJGTgR&FgqYW8<@3E2kS2K>#E=&aC
%#J'!\Zcm5T<"7ag--ujd+(VCMV\O,O9&[R0."GWIq@%jp!OET(mkM3Q<[EB?+P$SqG%oC,9H"UDfKFba'8#Jcoh2-(i5AjJX=u*L
%_Gggp33EB,%J>jL!%M[?2UmMi!*;0>5RIUt"eJP"*$b.LT`u;:Nr%k'q,0SG-7]K2NV/;$XJ?Z$6'4!f6)YT%V^Z$D_W\e7BPIR<
%1(IP-n4mFH..ooH[:.,r`A<&b3,8gtjG$';&hSY:WrP14J/p@T.YXkp_IAD9iBG:E]5:@R!GtVFMSjbZp\lA4$PCNgBF(+n71iaf
%<H>P!-nqRW"'4(<:1/$DF1hpQ#&sZU>JmkSUadV%aB2Bp.3f!12uoWP0MWsn#`i^$Q:1to6"S/PCiN*e$#G.oAgg4`R$*K:%30+Y
%YS/b45c)SiA:(((i%Rn)Gql]7,4)#%D3XP+T[-)M+la_"W^#d*67Bc18_:2"Y+V!Kie2^]('Rk"F&YdTOoW<mXKPQ>eJ)a5``_ec
%*sS>.['QTo4lt4\Slja$%#^Y80aS0Y6MWo]P/*ZM`9Hi'W@m4aZ\62Sk<^:\%.VD:7Na</N#D,]4+qe.!a&\4Y3?6:f\SnENjpin
%<Lfk<G00p];=US2J:@i4/Tq1u"+qM^_C_o:cRV7&Su!<=):MZ[,E[nB$/TIS_l0r;"H.Ge5Yh?:/cu1XK2^,<=!A[#^?@hL,0raE
%F#RhpPTYcp:ph:%]A2?PVR04;0H&a:ktRCdfX!OlWE$&Z;rK`P'h<KmV%9k4*$f"ab/IFiG:,)b)B4neN?@2DR$e6:CZ\27$KYOa
%nu@p)q-1pLL#h<](:g+fZOuMJOC0q]!=:3<S40Y^/Kl@j#K6t#7%M,&S>OVcSn]mJ31.2cPjQ,l@7#8e^p/VgL'c#DaZ6aq1<+lD
%$mOX:K-j1d9t&:dC!G`<6;aWn!mK3Vj@L%"l&)bb_ASBM<4+4*Bjg>$e;+;CZ.-J.RDTP.#rFnp+/*nD>aZ=jKOceiWYX_3G>a"t
%bo"iWeB5(d:b;[PEL^64jO"ieS2?\T!kC8b8J@W>Ocm9gi5e3/E*<Fkq%PRVSCV+':Z(f0kK*-n,Y]p>gQHgCUcDd"a/!k-eSL8R
%Ub>0P\"bg<KYBk&<bb0BAJHRC'=d[k@aV&E5So%C/dB3G09epd.Q%ACNEWut+Nli3<phYD7)jC,Oi8'8fnHMs^fE#n@O8U2Wf,b"
%Y7+7K-=*84$b6%cBlJ,t@Pp(p^Im'**M]LO9MZA_4a"<EMAIL>)/-8O[G%T^S8):e&*)8Rd0)efr%Q8KEu5HZoHe`
%d)[LEm7HSHCn`bN-&RbNXh<YH1WKt$4CJbBFkoodlYNp2Mj,WS+$*@b$'oP$i&Jpeb8c&J'4`R"8qoku+lP[<J0R%(8*FLY@3g,q
%gDr\,!gD/+C1>V?'Sd^FD1*8ChftTV"qGG.9jd#CQnql*.Tir$bl=b:dHqlFAnN@n#MV0m*tb!POSeS\;Y-WP!n(3;b*-B)F@%+c
%>I'FC#N^tgOs7K*?Ce(-0=$iuPF>n%k0L#AME3jh]i[6$%eWSoZAk#+/K>n:$ZA5edLVl/H,lnkUPh!iJg)7<L1Z<fpj!b)PL!>1
%"'gdEmi7iD/Ob3@b]h0B)BdHSA7S/(;$[sc^3UuNJ=JE!>10BRT/0?NE-+K^Ba0Bg7O%O7&qiC.4P2+5gq0GHfMcCdN4@qHj-8cd
%#m'$,4DP)FdqP88JgJoTTK42b,XkhT$.OoERF9)?eDaLsp3u!P>;ZhJr-+No4d/C.XiY%X;5s]``PIY=Qq9h$=Bq1*`:guo(7P(]
%H/V'4/H&3^S3!ClW.aHC:GkJg*oW8L01f,&K2t-*N23o),eBK-5[uWRDtLUc+<`^H(as*S<6WI<QGKP4Z)KaX3K7Cs7QA]\a/)8/
%TU1tlKf.C1BW`]%8X":sGXeJ1h8Ocg^,2qSbFC*qj$lXY"ITop+=Ye)&=GKaW<Q;g?p%.[OpZ@='"97,)%^d/fpcQA(ele\9sTqQ
%h8+ihF%>;*RV;;9+FHPX1m&E<mn'85-ZED@r0<@@R]7LY_PH=QdR7DQC9HnsMG*F=OSbW6OE%qY/&S(X:r8HfjM,:9FBpMr+@WR^
%<iR8W"rVV;P",WA'h9juJ`*!'=j7[Q3YZOLnVf`A6#3K]$r(<i9s#iA`+daN"1;]K(Qgrh_2FDO$p'PLTQ(SVG(:)\*>Ri%N=*Bn
%Mh92MN^P(H$.ql&'bf>9^dVCL:?"<Gk;X9>,Y@oG4C>a,-1@Q:("V?H&[AcUo:@TQ(9!#3,b52HJgN/sm%&6GYSNM?lF93/_FTg0
%3[%3.ToSC=gh:_Y5m$"e)B+B?.>l4pC$pZH:^"do"2PYAP>`%*K?cj5FJL3.?fYX;,%4EsA$K.MF`3eD09HUuUr3Fn!ndJnM'2eS
%4)+"=AiO[cGG$QS'4#NYs3+@6@'_)9=%RcXbb-Wp>)GWNdij:A?DW7"MZSZ;J@0b;d-hqWioK$^`hH!I%u+i9#5]KTD\6U`Z8usc
%^-bjCXV.?e&Z>)@0%8el6"2#S/DTGlqIIGOdcS[-W+b$?g@Y2oQE>bE2.Y[BnHBjNRC64jb)+XEfW-F/a&sRBWX^-SP85+8.^CNZ
%B)"4C'\E\bV&5j3U"($3HW#AjSl=38*<g)MfO-IIW_AMIM0+QRlA6XZhsK(g0]OFc^^*5jiUH<5m(HP"j@%tBS^2Z2RHers8F5=3
%H@mUhg"F1"7;O.hXDa?KORiZ4=HgX*dX&4'bFC&TJY6PAM#&GJNs+Kf(=I$%!^"8U2hW!V35SV8l5C<U*d1ALaHQ^P*jM\>7@CHK
%;/cS!8QM!"bRG9"$4@]%;/&qL;BMsO%-8[ao/sWb_/M`H,8a\8"UlLnKF$Xs[^]%VnH5SL:t#f/=%nBC+T%(j"N*h)#isKS;F6I]
%P1^54,sYT$#aFqF.oq7RTkHb(OruOVJX.#"GA.]9EpB0*E?$9Fl/T9En!M1tHH(2KR;n*_>J5Ki.sMG#'ZMmgVD@]Hnc)JgWD0*@
%_amr`RNWhq^a.C]^k,,+.J!<]0\6s$oE<nj;t47&.8H$3;mBK30Y)c]@H!M"'7(:t(:BM#Ps=>WEZQ`rHoWNT1KR"%D!KHY3gM$?
%)*@3+S4YCVhD6;)0%N$1fZL;*X\l%'W"L,Z":N(-!@PPtY_*TS#Xut$B57W5oC;Df7[)BEgB.:LH)hRM-c]@&5`(bG\Wn5_oTYd7
%&L.+MV8SAW6&Ht>!d4&>/n^T;K1bDO3cIZTTEujr^SsU]8XsJZ87F5AGZdpCElB;DRor^u7EIdVc'UA-&"1Ya(g[IN:;1rCa<5gY
%R,4RD@Y,#_Jk;]'dB!Cs">qhEB9X<b'9C#?8kLP-/2lchi05Q-]J@%_fF0NJE+@GSL1Or1.FVF7iBae2?XG"oKf;O`iAA8u%h\6%
%\<E3m;GPl%32KEH@hKJRb[8Acplt%K<bb"4#ruQCV<=#s%Ydk),2,')afeGuL<_4N4],f7:cbpi3enl@oFjK9k(Pl4?<^R/(Eu24
%A71otCB?RY.CYhCPi@'Hi)/[93]8Wd^='(&"/LjIU:Sqf(4q`d`0s/LBJp4gj[O?D'L:G#RYY%*D8Y.bnDogmOf7UD+FCp=S5<)b
%e)k3!AD[,YRtd0Im/L$M+[s#X36_R;4J9Ded#<]Xoh@+ckXFS&.;ELc2`n]W'&,^W8<7/P6Kc?CR6O<"`5g%]Esq*N!]q/HJ7/1'
%<Kg&5>^[*CZEQhj#skK,<7KVqTVQJ`!(8)s+Y=I&%\0Jm3iX+,qe&X-CHrWTVK#?^&g1@K!?e21UgCG/YnTlBW<>("<<4*;6mS,4
%egnZ"O0.?;CP7@%;^QhrhJH/pH/p!pDW-\(jJ@pG^:n2Rb*@pO5pjt='_JWh'.A/>3_V&41FF/mJVpbMK*Tu3EfJ=:FAu"C,H!Ta
%/B+o[s/7h`nksL[17;>sail/OO<d/+:JM0VW[.e,D_;qsa:B48<%DXpFs?7<W"Wr-)t%^!.<CPC&s"GOD\sFoTLC>)*m!F=R@m]J
%XjMt*;/7mJ*0TB%G*m_6c=_19_?Lr'%7FS/P2[Sc@QC509ATH5\1&+dHW.3s'r+2UBdW>6PNGrOP2k0%],E8-f'C06>5'>caT2A-
%9\E%o``Ac_p!m(TgKq_79^k28Wb:1A/2e["bHi7JpI6r1BOOGSV$E:`H-_D3AjW$cOYYcaL'D(Xi?:"RAdtE8(nF<V#u&hg&"H&u
%C$gfGmh84=-S]/S=cbJ!SU=]Tg19C[;/gL$bmjJZ-t@t@p3K-V8-q[f$_7-.fQLmUdk+I5-_^g_<_QOXn\D!*+O+k;fK"m867K"n
%@nFHl9L+HPj!RtM(hHSbB`T!h?(sh[8?kL"K7=E&T<;i.\DQ:BXgqfiLMc]j>R<'aK::(>^2Gb]HdHh[6AeA(Oo#GJ9Deo^/1,`^
%7*dXSg'ii@[e+C7`;)g2JQ+7Dq#j>7>oQ(@8#)P6+uWBFpi[.k'7otm6&#ig@[]%U3=b9J5]6o$jP;Nm*61%jq8lAYN:T2#B"gdp
%(8@<L#u)W[,KD'2OZZ9gc:#sc`pYZ]c1F7%<?,R:(F'6+jAmPQ?IK%ag0RG_1:XPU=6gj)$-5/["jG)S@(icMn>6[c66-+p-,Ln^
%iYkH-#\=)\W`3Z*aSpbV.ZG[YaqHb6n$=01G_,[C$mEGYf=7WX=KJSo4uoOr3<4Jl)%Ii+2QSlt,W!tebn_mhPEUAbq<S9q3f#4V
%duO;S'>/)j$WbJe\Y*VYSA<&DF%(Qf;@7.Ua8FGZV]5i##lEcY_gW5^Apb^TZ>1P+U/;+`DLn!h<)J>YiTQUrIoue.Qj9V.]ksgt
%Gr7U='-B9$04E@cg+Bk\NXT"?-bB(<35J`JF3q`Pf]2Hb-_`7g"`5lZ&pW,(C*!bk9"pmaTSkXDhM-0be/8maZ]aLR=Mi._Z3fD+
%lk-H:;N>*8#L6,jb=(Y2N!KToTNZ@\U=1qo'FdBQ7158AcbV3t:b&dQ>$9D"OjmESdT+OXLE@uuqb=FtmSs$6XtN<-UCKuFK>FXu
%Im,<>bhOu(N('uoWJB#7[FAU:fu`m,6!^mBKK56<+A)Dg@]Q*:$u,AX?Z*O(KFuV2(5ZoqAs*Q;'>1)S2BZuQ1T[M&Q<t6Y+hP;:
%jbGf-i0k[O-i6lRgYi?E@&jpe)ph(g=O\hB7sSAU!D7C4S"6nh[f(1U&Wm&o-d%YM+%DO,Mi>2Ao8rN0Z,f""M\F%HNY;^j,a0YO
%W3[[7ShbniT%'S=dKQMaJ/Ca:3Tfk.I(6JaW&>O(F)/3j_NC4LKTb7:[X*Si;e52'aOK=2hOq.c%+Pt'niu<1*5sIXYf^_?UdoI"
%)Aj7b0$=0<9idCX`A5S6#K,sXR3Q8H@jVZ)R)6?K!4Sn#c=aNh;oo(FOf%A;+:k5OKiM#*IMo(BkYC($+lsh5Ys,@/5u@.BrBhUW
%%;-Vlp*I;$\CuqT,;Nq!ekQc_1MVQk8.G!W.PY+nC*+Q110n[:<Y/SJgl"&A+6,o?ba4_h*p085(`^Hq@EaiagK07Wi#[o&2!hUZ
%138l]InDK4&hX?pkj4PFfpEm&P&Mf?2B,3tY+grG,Y&,7k,IKTA+)=Z_/HKJ01%tA6$ZV0d-00:SL=[e`O8>l9.Jtbes&IaA;pbr
%(t2D+lZoHg45(((=-3&+YoSV1LtjSJ/RHi-Y3;=*)`ZT\)93bR>qMi$=5nuY2-2>N;Y+@u\@9[Taf3+97nVn0;jWr-_plLI4,K^Z
%M/'9>0j=X"WmpW8eC'jG8lW<r-I0KZ$M&X<]BtOng0;Zn9?L]hS=mfsTmA-`U?RbO>hE43X-#[7.WQ5&e*J*l-:>'lW![mC^CgCL
%7PB^d'$%d56q]?Zj<ZhJBdTlOoqm7:M07C6T,m!VQDU,3b[='XN%pDVpcRbLKK3ujacp$>(jF>Oe+Pk7NU,_+LjUn^4&<2`nZ4$!
%6j.SY$4KIL<U&&UR_pj3%%9cm7lVG,E$[g@@7rWO,*H>*YUU'jG!_39P1oQn^j':O']J+=HE+K"`h_7=\7pTdiq1.&Ugf:uV1_0M
%@uW=?!K]]O8])bi7+SJn4SM_R$l:0J9Y(*C'M."@"ll*F@RsnV:/?\38!h15r^=7)##4;Z+%CU2.pk#Cj%U`=8c^1s8*Sk9>;Q#a
%n^:neqlKsr-(*<nG$^B$E622p2"&Ge7H%WT_u)m?C_G3G[J2bm1/>uV_c*`U8Vq%)752taUe7=pg=!c5V]r/S*U'.jV5]N_au&cK
%)qce&E@`e!n:bV\i`o)\2msdECl.X!hAF=9%-)-Q_gs@C4H7;rJ^c,?Hi^m2`I!n1*CNrbS:Ut<8h'o%?mZ&dlk`DL9?]^-q\4lE
%Hu`gofBhdW`R,dgNkh69Z=*oUEsajs(p.Hn%-"m,Oc:-GVH"7AY?[ukr3LcMdcUq_P.f?N%l6_=$<W0bG-7L,TSX*a8I*>TkL#@O
%XD3tr>BF[#qt@]l9/7F*KJXDJ$9IPL;p]nnm+-h+Om5QOjX.CI>2%@a/Mc0h1P_/+&o;iue$%#;Eg@10H8u.?mBONu`Y6/n,d/Vu
%"!)$I'S@k=jPVGk'[I#F%T998H;VJAg3Hh@`D1q8g4i>8."FEgObq+],K\-^@h;Q94KOtX+;aC"c)2#-1/%9USMB2P-'0p[&P1@h
%9;0-S6OFCM&E!R1X#%hBAH[)^E_:TXX!=^*#I#Zk$ss4U^`5b8BG1Xsdl;p-61kn22Sg15E0134[5!Jj;hR`^&o+MI:99-/N+!Mq
%\9UuO+f^`G8a727[kiPuqQNk7-'QlT2Hao5F*V`:MH"$JK;]FKU;K7LJ?IF8PB<Nhn2%jb4TfrQOq+;8EKoRTAV\clTM=@X?OAJ8
%Nq!*,3mY"2,q-#H9u[LPnFW48Dp*5R;/kYF^_ED##<be$4d&Ice;][ee]KX?aDf)`8J;>lTc]#:>-\=g=N(V?&t&]7DCa_0K[9,"
%e2]o2.s4J:4(W15l8F<O708kn+.IuC]*!C$37W]2joPW&j-!Spo")FL8fK$>-"Or&dA.T^IiW50*h8_=+I/*P*7aRbULVjoftB=1
%Em'`8:QlAZ,(u.dn"qG^e/TaQ#<;.0c`?h>>Lp<:\>LQ$[)du=4E_RO:30m'aZ.Oc6Yq`)6R0qW&hE]nAu.Q_(2LtLF`4Ork[&7j
%+n^tY[:lh&0LA>;jL%snhcP.`*7T37YGF^^"brX?$U2Y)KB?%$5_eQKl\fd!o(8>9RbJm/$5,+eTEJ$RAMHUeJo:sb!XMOadNfGR
%jt*,"0rYM;n._$<5[8%(:Dm<%8Z=`YK,*POP=6&AH?Bqs$h[pDDL*bJY]?,,<ElqWHD*WB\"p`gKLpJN&g8VST^]H'BZNgcgY5\G
%2k1XD8I\c:aV`&Z!mL/9Ti-l=56r&i9+nCI@:@M,?mEJi)5*eGbFI7srZ6ep^c:I'-TYML,(`&cZd<l,AFc]'W5j><?Og$ikh4(h
%g9XQ"QnIf\GHWFlmef=/4L6jnUJ.4_@IK3BE9uB`OEi@%mYKNY3,0J52oN13AnhA<-"qWW-<_b21HqdqX3P1Bl$+uG3iA=-&u$^'
%-BTQ^gi@O)-"!FhBeTN2c"RWagnGUTHBHX-9aKf&M]JFU'I9>(o,o#ofE^bBGrSqiaD)Z'#4[si<d:H4f'u5o;2+P)]M*0*Qfr/f
%o0!]/_hRDTdD24k.rV]1$1U-3-:M/)5b;Ab4sRWSan7nXA'W9<%o6Ma>U25"aSFX'jT=$<duptTkR37onMSo2'!!s9pFW)(Yh54Z
%=(T%]kd^33193XfgE5)Co]4d*l-oU](]0tsBA]7C^GpZ9r:'OHrp0N,;D.RU;%Q+%4P-GN+'(cOPA%_#pM=RT3ur`k3*5WSqA#@b
%T,ukEm'"^XZdY,5rNi9:e%,JH?_&2:B.I8XX1G4drV#gAlK6Ra0%K\FY+PN#s5hAqbn[_&Ha7Qn/WSTWrem!#rUhaJorIn.oD-#]
%a63O-*mZK"=6RJ*n$Dss^G*:/B`S2*(I!WSp\^*.^!e(EAnSYAk%DE':]E0Lofk8+QX/6l[/9HES)*Tfm-[ZAi1d(Y3thj=BsnC_
%S9Rqo9d`Q5>9fWZ7$^apq-'-SjHbr`rTJPfiSMmB'h:OrZWJ!V+Q$\I=jf)RS6UJp\*BH$i)iUg9_Ec[)ng3j=.^`f'eVZqbS>,G
%?<?*BerG>%&\pok.r!4)4.A(+$g5Q`MI`#s-+2RiCYEIf_/=qDh,O%K?WA\d2Bs*:$ap;1mBNU*je;rL;E7)gU!5oA&WK^f#+]RL
%MW$?$.WgQi6?82;_TkDS;YM'?=F,s&jG+3BccChNQ2afNT]i0^0QaG_TRCI/iS1](,)OK"W^e,]%btk$hXUZ_(eMk]W(K`qQB_l<
%2;NGPG)Wc2(ak6$=o]$J!l[I[0]sO)FO@'rX4'jfP.Q_(lOd/H:(7VQ^f6TUR0(!.cl`q7g,[7-35SMOWj+ES45gr!QH+,,e/k=j
%,NNS?"I/*n\n!ho2%1U[1KM$XfY,3p``'JE0ki5MOebnR&o>qe&'7fZ"PqIEmP3pKRoO.0bE<KR,'MgJr@;_#0!0sj>>`TI"iYkJ
%_r;+V_\U1tZPl]^\79G2,2+Vf,-V5*nGG'8-/P^hXdlHN$7&Kq?Xh$M5[G\0*j\1jOTMZkUrASW-?BBC/kK<=lKiO_'#9W;RAsJN
%034lC?ML!iOP7srOcs=e4DT`dp^DD3\4N,uS</K@%%:D/,)%]O/BY@q$I12[;'tB>epHtK@4JXpP8uU``7Rrn,L9@L_8Q]uAY3oB
%F?.4VV9M-LUEhC#%C9fsnaB&8NkEF]G`mk\:]]9*3;*,Idmq\k_.@4"l3XBX6dikDf"Qu3A4YC/&rOViC=X.AC8(sN4=!Q_eGk7)
%3t[,e,c(hV1E7N?A-N8-Zd["5'kd)J5emdP?XV0B75Lmu_t[Y3O5`$YQY-G:W+R7^)=]C8,>*b0OV1QD<UTmSTl:1V.EAu?`@];Z
%3>M>*R^.nN[l>&A`9^,fC.iWaZFifTI\9;E@j!>JF-l')5p/gLiYX*_,0lVa?N:4c&1Fe=E+5"3eu?I"\"%9*]u$MRpdFUV@tI.9
%V/Mdb)CEj()T$q*n3J;D3oXo-k&"O*oVsj%X-T)rEE34A6e$6S/oO4\dr[pqKO)jCXF<p`j+OXU1oN#A0]'dCr2]1dKrdo%@&GWs
%*bfno`,8*H"l@?`&:E%A3.\o2YZ/R0OO?9+=ZS9MgBu#Q2dr.!hT<qpN3oD>F@_E>3(dl"9/W!?l9:$=qBXACj<DUNCcE]Z'A#f4
%pQS)9MY4b8'^k*i=A,RoEllG_rp^O`daj7^.r,0]$5DPaajDIr^8"be/><m!rKphiI<C"C8c>&d5_<,m^4Q?VI:/gRH_(#;Op)X[
%<MGd[>:k&4P]P'7l>S8f&*/\64hqBZ#j>Her/C4[-6'.51[=4qAG#]9@t-N@M+].P*BTD8&*cE71%mAuhOPI$Ob[4A=[SF2FJO7Y
%4\B/eB)m@S-*U`@m+ZK]8PJ<1;(ik2GGTg(YI)^<Uc2m$'p+S17#5e-<(K>Rg(@j:d"oOm:#/qcZ;I'0Z$=:t62;[:,aSpLA>5'H
%K&,ZsQ'T/3j@k:3=)c&\AK3BL5?f]%,'/$eP+@t9*2Ut9PhOW.1]g6b"`bhJ21mZ;H7Qg+SMZp?1F-DnnNo97U'N/,pPS<OlMJcZ
%:D3+];M.:W,&o3AUsiAB&udPn7Z>V:X;mC/4iWdS@[Sj<Ug21J/gZUu(,"Q;Zhk>j@A$`WP#/psXsK)\i%mV*-q]1M^K7hVUoc>7
%">)%$%V[GTJ;eleaTETq>"UKiG8g&0(5FHanS_8k018T,QOZan`]U8":i/2J4)uWlL"b8cZ.:Ugl@asb1(u?/*r0J*_.ERn&@u\Q
%Ef_TY!B>%_XQ)t(M?2CBjJF8@a>N@F`G$h5K0unCrYWgP\pUcM0NY(Non_[Kn<MrIocasA#59^dok5WH(/bA]<Z8q2)9jK=`tG[c
%i7os%-nS3RWq\@rMOGWn&N`f$XY`F%,kRl=Adqh"[U@N=f+m7a/P/L`L\8UV-4U*I(V([#dbK5Lo+PEXX?)j@%*oZiIn60$j3GH*
%B+iP()76%^J"jD<<k#E6A&,39<L>_T#Z-RfMEA4nlr%j`UKVSNK4PiDP2hIrHoI_/B+[i!22]huhFq#9>QkknXKr=(LUF@f'/=K'
%n^-4>?tm&9ihp/fZM2IWXB.^5M_:bG0aG8ANYY_p?j>)^-sirJeN6VX$e_%aY:t>FH?(+41;&'F\]j^SJjq_4:i-=lN^+tkPF8Gg
%(aj_VZ\b8Q':IfaC!&$I_=f8W:>llLgX-q\2K%bEd3*&FB(1+,*AgF]8;L'I),dnV,1s#g\5?A(5rTEZIu.nma]WhrjFiXeI6VGe
%EI/NI4_W_cN@\d2H1StJ;*9UN;)#8FO_!eP:m$nU9'mFB)B^Au_i`n`:VI.V&P"j.K,e-<k::"X@@h;76`L8amJ\e6P1tVg'G/GQ
%;-^d1_4tC:S'U:%_GrXl(;kWY%=UUD!$ORjWSLPBBen@7::cB8_AG0;X/DeMP\Ah]_2;rm03#=k9JsV#*D+1?@()O#>8q#4UaD'9
%]B.P%+c:W5!WAaohuj-Q7b^e?R[61:X\7u7*4Q,4QZ:LkVrlFi_*i.%+Ggdd3_r^WJ5"91`gHXF]NQiN._^'iA0hR\B!?^+qG%KP
%0Io'M-9)-kD?^kkVd+LHJ:U!Aak1#jO?F)&-mVO0%>M;hb9dE#SIf/S*$mNS\TIEtH),a+MjSfc4GiBQ,,[mX$rhUf6Y:rj`R+[Z
%MQWcKa$?l'Itp@!J0]mm8^g77.uEL;lP;AT8C_/6fKaN"WmXF<M3\2VZA=Z4E]4X$5X"5-\d;[r!RBg1;CEDTQs`(-R*BO7?jtH?
%';Ll0lE^`$LWu.uRGh\TF:";&Ua[@CJU<#SJ;acUXD/K`QhR4&@%S#j3jEHPU.T*d9beX>`=,fsdSD,irGstkMk&XK?9].%bT`6J
%'ikk@RQXA6rP1`/W0/`ce:Y"eFX,\%4Ul8n*4Xu'6W@sRlZY[G!)6:K_V?aMN1p&CPl#KbFiZTjCiSG]mD+\$9IsKpOf,9E:=\2j
%D\mI?.oW[!:fT5VFJ`aM2<uu68plLTK`YrA8uNY)3gMHV['W8$i9Zk4^m<Kt3!U3L0%hH;#(]er%TTCh%)u%YIZ<E?oSsDL-?NL%
%:GG9V>Y'rE_`4hdP$X6'N7=d.3!6I!lbppp3h;XHOsW=cmPF&p6>,u'%95ERjkS#b'D8GsMP-3qKu]l$";:5/^Pc7o,WL&b-OX2J
%q-b\+(_e?o$2/jV398cI7$Z7![VTA2%6-_K1t.3f_@`I`__%$Q;j#8qN!J-7^dUe3ZKMK@%s[2V.M2#$m;;FXe+HBY1,k32)]gVq
%X49),$'M[>S4afVm\KPK`Y!S^+6P3G4CmicK"f6g>IdS9c'7nG:K0^*_D4ajlpM(TmV>9KY5h!Rjt,UO38/!/"-Pm%A"6M<_YH<H
%_U$ILFRHK6PH*6.Z1T_D&s>Z;OWu#.R@,D0+#4I$ZWPbbA*g<d?iYU9IP/a6fjDm9]0p4#C<9*I_qK:b0/E^-0Fi($$F&l[la5k>
%-&>i/ldY7HE2&FcA4+JY`4A`8B!uDR/BqTHX^_NU*D+P9$PH6n]+/rggVnuNe&mM[NU$/,p]T.\juWh>Y`TsL3gg@r7jm.(Z$faE
%m6oZNj(dL#)<j#&]7uRA7K`?h;%c)]<$$]kqMSt)Om["HbQpue32,`f,EXiVj%b5A7D,I!\>dK)&rCQ?3/Vn%e[26M$\e`i8%I>F
%..T@k>RJQ;cS"HVK0q3JLd:=M:4a(8.eDo$0!kZg)GEYYnT>d/X@sj5/BIa'BBA80aA-/!)2QS&0>gIE+e)t@C1?1LTYI/17oUF5
%-+tWti[k\44G5L@Z[bmHL'c-1!"i-BVKP'UN(oeLr3A&>H3jqa+Cm`85etW<nhSeP'cpUJ7*[fM:b$?U@=gTJO>EtZ?2;."!`IQd
%HVn4U8L'!=+NNKXro(+_8Pb_l3I\[_,eZUNZ8Q\CW'd4B8?VC,'^_KRe;8'+:A8Bn98P]k-ToH.bTP`IZNbQW]*p5PU*k&]c&nHG
%/@pVR+6]"YTEW_(5Ff8fCGSjG9N/)7%b-r$"q%h9p,[5#@HBm#DcLd-VCWrC0bbsaQ7P@*FSbWA?e*PodY+MoYqkF:=MEITH07P`
%qi*()lPpp^#\5T_."l`'mabdnV_#i6Dt6f<M=0S&0%NFj^n".O5uAjc#S.f#-Q/&#-p\^^,&r^D7V5MQ>!5b^MH>n\`b0Tb(POZI
%2WuU"E9IBt9s'G16u`3A'Ua&m&ktJ#j2%RUMRAk=#kb<%HId5;@iKf,`!co9PlqOkdt,o<-PV"D5Rq9`)/2]S-<+0rFO96TLlW_C
%2HJ9WGhc[E<3>/k$I-$D<BELq.]R(/F\\E^O5l)9&\]e;7#%)C>^p$.?5XR&)5kd%/Dj:(iF'Xn.C!FgTElR7768XG1V+0$"8>ep
%(9S\5bBt[E[fB/"hfnlEWQU@U*F51@?G&6^hHbVRGe)_&>o`<'8"NqCM&\kIFqcUhkZG8e";c5T>qu;!3%fW"EHZT@qt017D"l?9
%e^rEbcQ8=+gp#,]>5C&.h:n:gnX[;iR-#Ph7j\=.VT]@^QpPb`f&BER+Wj`Q,:W,?nPf2S\U%5;1CZ?_P.1.t@bm'_T0d'*L?,Zp
%AZ=aC5PJI\hf%n\rdXdFW&!O;`>T9NnEI<.RWmc?p>kutHL[N"qj437$:D9=8p"[t*I(n8FD#_1a)G6g%W&8,iDZH$7c1K@IQTW>
%l=uN=_8u(9eJCfP7SD)Fe;rf[b3cTVXYu[p<,5FiZ1WQpEr=G=``^aDOM?"8Sr"d*TdmUH(m;4CXc%+?iN-@n0)C=)\+>gDVlhY7
%,f`7?Nh0cP@NV'&$mlEV,);ptCD0lgP3ZnYn(;r9EiVuAD<>%@,m#fC,*ECmreNDiklMad,'@[<@]N5ikAYjif))4FIHkT(hq'4$
%4^fb>2uO>E8W(Bm%;j_+eg@fYpjN_)GAOtM:TI8sD.X*<EMAIL>&$p8_C/QafRI=R)=B?_rLW">(,[N1ER?dh`]l)
%8;BTjHqWO*L;VTd*UA&e[B[V#G/GjPhf$Et;cao"?Pj3dRhJ:iN.(2%l`aNMdV<3FfkZAr420=VeYcFUS-MHICNtc*%oCDk/p/:d
%RD:VhGl#bS:PZTBEH5AeKd^Kde#Z;u79WCtCfd66W\=D5a.EA305h-.^\m*A<9kGd^TZpAZD\TpV6LJi6n/[$5o$/8lS:(,\5=e-
%!/7,6^uVnVW&K"A3Pq6T<"!XPARn=XG7q1u26d'$\_*2YF"B6WeIYjn;/WdV"dl@pH`&`_-i&ce,1Wli"%1SqE@)<MHu4*V;ERnI
%et3-n$>3Tfb8EtTg8QTqJu.qH%YlqMXU.l-8XdV$n?e*U$f,RdSb*QX6I>_Unur^]!:G7.&l>eA?Hd<[0Tm:Clb(UWJQ7GoCI;/)
%bZg#6bs>;1bbD[!"XiPP[^EOVpnlAc*pWMD.#UB28mkCWSX4mU'l+,=F4>_28$G$:d%\`,7>:RJPM\ll,qbhOCbBrKDTAPqaAs5&
%=5@$[9Su9fB)tEU&GN:ddl5Vhh#;'q%8X7C/s>fFK9EL('d)5gPR"3KAjj=:'CM%4\+.]\T^c&E+mZ0GX<kZ<4$5(A56,P+S]*)@
%eA[/tLG.hL#GY4+iXd*ZbL`^)Yoab6A_U2Br%-V_;qF/o5m)d#*>:@\(oM7$,\EBN*(5HBqjGBYqpDd)-JFO/\VG92cf/B.aqj[m
%d.OHQgW5VI(fS%:*$#o-Q!A%f]1.`q(p/=G]?+ds65taE9!(D+TqoZXg;*]i(s67P`XnpKgGW59p&ZMblIM"b7maUV1d"?feJ,SO
%^n_28kYf/DOe%nMnsuoirf_9uR^A4+N3]pUJo++a9ZZ2OW'b:)oM(S6a(71t9leLB@F@#a4Qci:`)Vg?)_#M.K(JsYG$:o4*SO6,
%egc?a?'MK$0UVFi"BRg*R*dKH'sGU-]ns"3<ViqE2S`9#YT5OdK.@2+knY"Y&o_gQ\jPXaXE_(RKnQ5$757K%'1e9=_.C;Y:cr/[
%/CKaUT@QOdU"ReN6<2f,ic3QX5N=+1?E8bQj/Vd@,d`B6(2;32(sWo7B-tu9SQ;7D?5[ZO[p3GmarXS'>?PtaFJ[l3BgW(GlDDQ%
%_#U7Z&pT^"YhQ.)l0qk8pe!(EJS,9qkB1S&dp;5sF`inlinTX,#E#Q,[5aKf(/),l2V;#l^p>*kK17dgB]BK@EDCr*R&Gl3>idXb
%0ddD-dPr+B&%4&l.('4s?%YLs-kDj:L/XLc(FVAu<*\,hMh8AEXpD.>YWLA;h8;Bp$`A1QE@;K&O4b!^AkBlL3%GiQP>jhW";pcg
%OaC"WJBa(4qoRDtdm@?>'1?)6"`3e;FD.0D_,%*Ui:ap1(W`jX0g#&;UNk(A-ONU#Ff.+9:FgDNRLW-)IYuZ^S4_P(SW5<Od+Zdh
%!PF>Ae9*n_3F"?ifX_8Kp1iH?+D$5IosC;;qEdB*)I/,m,GN6rABP+.+k6ZWl87PE7m!TRPHbcB45SDQ'G7"co9Xkj)d#a@cemj%
%cE7LsdPRtTmqUcI=k+:J[V/ajG/;+$4.uuN;7FHJce(:klJm[0@gi#p/I!oG*%+)og(d!,Yo2Vof]J9cm]g!RRLs'5h/F71Gb)fN
%%%"kZO.\+-Xc4_=Zbo+e&kb)53AoYF=03,60%Xt,(.2.o>DhfbM"=m>-PYWGSF/O?">$f9\DFVC2KhiJPE&D3/)Id-7PVb3G>nq0
%8361MC9+!e%_A7Oj/S)*+M+.)8DL[:1WY@'j=M?N$H%GE1,O)#<tl!mjY8.VjP`XG"4Wm4"5'0N"0Q9W'Sk/rn;`YdC3e5tn_>)%
%!r1-'nj=*@P,/`<f&nH7OMb_>X$T4fd`X`bX;*5dM(G1nX9ql<RYOc,MD_PGffKS0m_`A(Nl?S(JF`Q"[?rOT'TZbVFP'77`q:Nk
%'h^Q5V3k"K*C%r8Y>^V`ERfAP\kY5bOlHF18Q7toibl^=7$&C>2DbRSHYj<RSC\D":4YRqE)5C)+Y[b@K2gTEp*j+0.MPE`(&4fb
%WS=eo*/Pm_XQ0M9Z&O+HeiYIc?h-lpf^Q=Sh!ht,#\34.(R_s[<Ad@*0t&<a5%\JJ&)(GBkjVOZW28'/O6j'U)P8El_hPuQ:XPOF
%_]FL8^4N*k;;ot=bUM`/^5TiCo"ZMGe:tkZBT5Xd&&Dg]@f`.:]).%BQ_9lFc'AN_`_2QtYcgeq?PV$.*00\%=1+'[e-%+&:Hr:^
%i)#S3V'>_D#S+#.F';oWS&V/@SlQ1>XZMO4g,\(%hnYALBe8$aL5WloBWu]@<4SW%*2+eJWs8[s/P&[eK<#*0DX6fCa.TVCN!?>9
%2MR7*c,NV8R(&Fl*#6r<mO1\s;pD>+'[Macs%dc8FcWRY@0<-2?IVD$G[6kmqV!Qj(Z5H\p-h4^VQn_WE4h,e]PWYfaB32k$Ilmb
%*EoBpW]Mfq7,P40Hg$>urH`50F@(m78?1YuCuM'=VnE_</nuQjFZ_JW$RC,!\9WP9"V28f!/FnHnCGRde8?g@%E<s7eZE=41aD$c
%&Ehoh]^37BS<k@.jt2d]NZ35X__0:%?;N+3Ol[.5o<?lr2:fX-:3dRF7^/\Gbj;,\d9X;HSk/Wi@S.;&peqp*_0I^dKU14<4oNH:
%]f&#aJn(R=&Y@OjR@OSNq:=ia.QQ9E8?(HFPE5QNB./e5rWcbhD")7P/6B`WKG,lgNd%A9?9W1?FmF/[As$9![Y[a1+)i0(Q/1,t
%_I:H\.N(:OZPW@\LogY-R'1oL5T2o(;k;<<X`o`)d9*\aJXEFmpH,VO^"D:Y1le#_NPHn#J459(<_@T8iWd=%nnL"Ab4S#-]@F^p
%=M\kt93FEJF;V%&3/ZFW>MIhiM13@a!*>XEJOSj.D8oHE>YJS1)99lr'q3BLYK73Qbr?%X231l'm4&df%Do#hb:Rj]WZelm1ash!
%</-t6@3;dM&2bCSinfu]m*3OAKC#M_N2slfBVj?r2K#00bf2ePc,T!5(8\38j.pB9V]hS&X34B^Oli[o3,&^/!n@jPTH7iUdtX+K
%ZOr]?!1Ptgb`N=F6)aHSZYD=WLm!j7q)T<7<>4"&NN5<9D)6;BOII#:Xr6I`b)aBkMkml0&?Id"V>]R`1j,?4)RtCTRS)&o@;$Cc
%f[<_.8c,s&)94d*I'X\\G\I9P)d$`/mdY$J(7lFpZ'o(^rl=%Q%@^kTLJ8LR_6,jC[rU;FOZ&68c=S%^XMMD\QO@4>,-X'^E9%Ws
%Q\IrnV%oQG(bqE%I&CT7UoX\+[RJ;e,E%>n$HC&b`\<7i`NgSA003kZX3im5R43^;B\\6RdCA//)NC#9R[:T(NCBe2c(]<>#YB6+
%H=>3WD[+`'h![t4eX5#6`eU<cnM8bBR46`d\[*2En_:i.T'Nrr[9-chfF*8(=cH!:&WtN+X<2;Kd#[]43nQ>:[3=MR[OD>1;s:&h
%VE=i<8Cl"</g<QgD&O3iYY^=PQ_\7/PS-)<p)8B`EL5+ROF#\`U#1K58ugI>LIY%L[)6VZp%YUTIue_E`?&(QXB3t,5)X6*J+e3*
%9msp\SiTk;m$.l)1ek:-CV>>-!7=mshKhXhgA4+;m?*!1",Dq/`<AXT#bN,7CEi;AMo2HudEt^bH@$/`+b2#DZ#6$&GVs@O=EJc0
%FbGI_Ohf6\U"oI&X6IL%XR)F5_cQZR0<CY?D2#ZKP=^MK14f^Ep+Hdrf92TqG=/74!oE:5641^AY4g*ENqU^-YGt<0UhQA,e5"^&
%UV>7/"/3&>g2>fM(M"[p;R#k4!AWPBlEkB4@Be8gbm3;QgH-Z'Es(mgC>^AdG`WN:-H"2%7(n.-.$l+9G&#kA+Y$Um*$s^9Jt689
%p/:X(1qGTV@O9PAf>2ZRW.J;V?b"FVAP$)AZaQqoECq)"7^i]#g8^WrNd?[D;65bN9)F'6V,q*#1N<VYqW;MA4H/TjY9qNq*9LkN
%]86qLXMpEjXLtV,OV"'A/uIFT7pC.UQ<GosL`=u]mC&h=^,L%TbH]_[SVp[,_8aUkU/>As/Msprjs;q>3#c*K@ebNbHY!<la_G=&
%>f".`ES=ri.lU(0cK33.W:t&l6m[jt*$2@HYX_qbj85#hJlc>B_ItdS`d&@X;R8G(gB`Mi`n>p<'sHRj<G^uXgY"Q?pi:B87o3QO
%nq!fYHT<9ZA66W=>G,E`BSFZO6QtMDUFLlPY7%&o.mZb4Arfh)'1X_;YO&8aA8nq[#^;j-Nm3uKEYTGR7WMYE"qdai08R_HL_+,M
%SqZT=B:#N&3E@L1InH\ALQ.qg-XWdsm`/>n2<jI*bs#kIq76q'9Sm&$7fu?>l$=R4O];DJbhY9S6E32;`gHo3GdF/X_pb6n2Pli!
%Tn2$Z'hL%6_JC=<jAptZW:B\+c)2m=?2(k_1crA9Q_k;$/(u38^gY3+GhX:tG/n"H7spWc`[s\_0*lLn;ZPE$JSccud!#XTNIpKS
%ei?PK&A?]4kK'JOh")7G@DTT3GIFX0>Fe$;F.gVCq_M_[lHem;C"RY?<P6K+nGjn(_K$T'PGA6>5q'CKKXXn/,NPE2G.r86Xshq7
%C'*4d?2>_\5!B`;JJr*IS`JJ^\dcYYBXA%`%TT/j6]8#2<<F&?8.Da,DkSL#X;SYs"UL/bDt.G*l/c=<l5iDX,A:E"VR42(\=,FP
%<?mrj_&H,dE%#MBi'LNmSSD0A:'Uu&SHPt2R`(tl_+qjBT_ReNiA-da;p_I$#.^!K=A,lg*ch)GUe@I5ek,2o+CQX2jd-]o`M>=B
%C"'k-<r8S)ADp"_kVog%R>l[Hbb,ob8^!s",k3dI4#E4DauVJ-<L@$0GTr!'S[sM]AaBG>K6FcaFY5'2c]#3%lJST5&kU6tdBBID
%9W.j391iX-=M>*KR]GP>\eWU-d]LUa3bAe,D(S>B+^lIm>!X;Wk9a9tHNV=fl0tK9,Y%BSb=kNC]%C>G\o6$5fB$5eLZ?JAiB/aM
%FJ(cW:7dEV`o?bab#Z*nTt0EKVR[a*?$/<KW,a@JoHt]"g$"9jLLV9tmM$\Wm`6Df4eIp!]_!Rs6J?t?Z(I9%X-XE]3"6AG7+)5+
%$^%6ed8;/\')f3d6u6bS"=-\;.rd!e_/V,4V1W0V7+)K$-MX_2=\*cZDCAYilfUUbZ-(%cT2#Eg_;iK*8GfueV3F582oSP8<pIEL
%Ztpa@B\=;1%>K;1ar``FJK<")P-!6$;\WrhFA)"$ACgCnH?[]WrPRG#b4ns[+ECc#XtA50DF.1N2^F3CnBg5;>f742]S9q';S0Gu
%HH(t"*^Xo)O(P>rs"Nl`cou=lW``Nl,9=uhlO"G6%QrS%'coBif/U-7M?C4d-[J>6?nAj3Gg#=\pHfBYG`)]hb;Lr;-AZhQllGsa
%I&c-+U;%R!$DLa,h's\T`cqYq'Df3p7*-7Vgg7R0)t3:5Lq^l;XmF4X*sJj%[(C`#+iu>(p7)t4giWZ$)sN4EB[qZ>[gdSORYWX>
%7RL[%QU;Qg_qpGq<WD_J,'4GFKAF[U:T=CY9U_E?h4Q3Oa19d#e*DiMiZ**HW/2A,a#Z<G6rjb=H>[kriKDHdDX5*3O#t!k'e3NN
%0HY'FlFH!]9ttap2(;_P]'3_`@Cqts@RaSG5nA:^%31mfIG%r*Vh\GTCDg4,F@SYU[<Z4eX;6tJAe&5#CBNE'TNZ(NMnTDo\FnCS
%KBtJ!7R%o@(!+COflt_$V`^2aSChe$>PP_ONX'dsqaN,_L(lt/E]!2D?O7UJa90_d3\?&%5X&`Mr:cjR90F4cdn(_[8C'N+plo)8
%8]m*qf'@L-94lZG/X[("[q%C%hicZ9I7n2>qNf?[lWZ.!Y&B5R9s*j3oduSKf=,9K>i1^tE.(Akg\!.d[Hh$PCY-$`Hmub\R(0l,
%N\@5TZR)s:qNf@6lWZ+`Y&DF`?/Go,,Q-KrQHY*L>;@/#Dolh-^5WXN*JUuf)mtu"ZDHkk1)mB7a_\K$-+!"8QHY*L>;@/#Dolh-
%^*U,EqNf?[lWZ.!Y&E@<iqetbL?i&!gTGJ:Cq.2*Zf1`rbI6_@/8NkH=OsWnZ#%%d`NnJ!(<_Hu>?hf<5b<mF_t>UI%G/'K&uMVn
%>:^_rDm=,g^;[DFqA.8/lWYnZY&CR]]"Z.hiVF>5L?i%pgTGJT_rCM^`?K/+Vn7t#e+G;Y%G/'keuiqrCqIF#ZJkWqbD,=e>k3Q(
%lWYnZY&BQb/%8n1\A#qfmJ7UAL?i%p)m4+aCqIF#ZJkWqV[_7e3,Q:AN=Urr5qEtXDi9(CY&CR]\A#qfiVF>5[d.-QCY-%+2I,.&
%flhd:B#F9nR17cWZm8<Wf=1b<lM?gjL?i&!gTGJ:Cq.2*Zf1bH<eMnpXUKFHL/pc`!Ndue[8pnEqc*M#9uWAuL;9WdL9N83$r%0O
%fY5kC<eeCAXUO/=XUJ"pV,g(%V,g(UV,h1Z7'n`0lU380Wgi3JqM%s<9AeeOgsjJgIG'sud`O*FW+oo?dB$N>ESNG'AD,JT."&UX
%H29,"1+]&7BP#b>Y0t-Y.mr5e.mr6\<eg+/<ea"pPu`XT/ZfH?/$2K#)d8Y?[^@kAmk2"']RZ>P)$Q(%h6s;J=lVJ4qm*8'L24'D
%b&EV)Znfa>BP"\$Q<K\F[gi+)/(9PKe]KFtY%hPEENWK5IR[;=N.Rg*Ahg8a.mq@oQ<%Vj\7fI)GV48A'9UPZ3f@$NeNc\2C,WFS
%A%-U=Q<&_FA>tjQTj)8CoQn$sb)DOY[;;t1=\VW<at2[_<BRAPXOi99<i3&heVS71f30&0hGPlQcccFFd`V2fgm,J%at1Q7YO5RE
%dn^5+M&MjeRFRf?m>Kg;2Xi<#N5C3J`c-MohOj#s@#/QLUancD@>Fi$jO"Zs5FeY)dq`T*cU?>`*W.AYggc7<n[0"*rQ&`e2CBls
%5;E]%)2\JblLCjQB.2%`$Poc0>>hd=fP+u]mbWWKb:M)F+^I^JZtf^V!F#SDiZ0*Vc^lgjm7bq9LfUQJ]njPe-Fe5=NDbJ1SJf4<
%44E45[K;%g^@hTk9*jjk1\I&-\8#X^[$hqGl:S!_l$K\Q<BF,89G(CqfH?/`D"*r+:!S]!m7bdk6=;U47Vu51S>#IqEV6CY$0M.D
%7MQBlP8:#]4-Nj.S>r)N:q-O"eYtqm]2"!!->C[/Fa9nf.9:Wo<B68)E>Sf/.;+g,0b?P1lBP19Vs7KlB"Ur:8e;?)jhmaB_s?CE
%b&UZa]fOtC(A:SsNp&1&14`9bWc"\bH-jh.Gh(&L-/t:X]a\T\<Mu'JSSfc!h%&IF2^;j8!*.p>25DV\KkH*MmHu&ki!F"sl($hE
%+L'>>OQ/Thg/d</'&HgAbNiESn<G?);pHZ%*)bfjh#WTSP!R\P_r*N5bXtg;iej0P\8#Zt_'hp6X29RVaY\-J\#)c&4Y0>8<30hI
%a?q-&U$/"WA;]HFS08u./.M<%"7)1-4^OC0<2bh0l=Q2k)<Z#'FV6u\=llNM8I+g^mC$UtpB5YsR8bsIkGFV<P0T-)VC+bE2o8f<
%emjjI<4ts<^+d]T<E1V.27A*Z1-=]3a+m#Q1@;)J-EBa[d0Gnh94qAWb@K[IA?b?1HEc#E1jl<L)9io8b?X]qP!iR#&^rT=Gm4"s
%;S>b)EL9!k[Ii3OYEtXr&++fF0_o<TZ4X!X_LU;Oa/S;=Pc.(Ir6BFVg01QgD:9RpKsb56eo]YliA*!F2ITe!9H+k=WM^10lQLSN
%.R-YS/f\/o%2=\.TN_Ro3SII$"!r[CCj.MoB!$<iP="OU$V@jBKRn"U,lXas5YMomB[l6HgX4\/O`]Lm"jl4fEAb^Y'NO:5W2IOr
%EA^I:pe!#-DR%Y8WhWgaE1F/.>g5=%8&j'fnt*)FaE2Q[+)oWhL4Ok2l*fF4Efpb-J^gjc;j%r?8g;XeKV!4l_aY)$`B24BE*e6L
%E>9@7)&t&+PH>"qcmG$A#JsH*DO0XK-Dpe$/f[AWk0@br+pKe2oSp;&j6/?P_1H*gTp-D60G%8CE:fUenEMk_pZdieYA"quP9%?6
%D*8RoYA)I&^.jdoDr-QlRQ4e,>0WV497M&P"qN0+6\EjpK3)\&GZS1uknBQ>TLhNrn#E/s%0K$KB5ABgg(T)f4kSFp[$1?AZOVId
%/@`o$LRre!$$KU.`F9hll;^\dKdHEW<2J'Khh6Y6XGAX,gOu%AeqR"4rIK5oGH3%_.aj8c)E"RESb!g%[fV?,g$il+Mm[&bo+JmL
%=s+,s/'$2)g&c!S:2CQ$`L@@1Q=$`LUcEGl1/bk,S'/17X^(3C8TGIT;G6\N&%QnPDW'_6h;M?\(<'Up?RB;Z?#?a/Vq1*MhHZq[
%pWKs;T``1`W?iCKSen^\8M9c!q2r70H\#fm\iZg(CTTdjCJBmrI!Od!9c;Y*O)9uUhJ'P'8$Cj*2lC@/c13D]OG[*E5?5t.5gZ$u
%%5Qu;ncY@$S+^Ap]<XnmoW,hMckf$m[oOfB/o45YI1]qCf[s'2P&&krKcsgJZ?K=nhV)VrG;kZ03p5%YTq>+e3=#GL7r0\uOq#u&
%aYV\tBjtMuRFd[u*B%$_)W"W[>,e'8Y#V1)JG&T;jta,k)Z:`82'qX!+/u-M`C<b-H(uW.0s:K@L]Kc%VpRP<V^X5:&s0qSCiU&H
%1/`m/H)d.`$!t&4H%V2j6P\IH6miZnUZc@U8N(T&87]kP+6i=C4t$&L^01u'Brc#YJ]fK+ogTLL&)^1&=*?hsi:<bo)aX+0T6`"Y
%dku;LO:E83+<<XGaoNY]*A7d$m#.p6"gU<CXs'=JGMF-hnmS\=4g!U2CIbB#_?K@,@Fpo55Y4+T4MK\>a.7tujd,#R;sb%L("EfB
%JV+u=_qS(]U0`sC6c`4;Qba.eQ$OCtEVA\:d*DtQcA-jP[Da*9PD[Fg0K"4SS6o%!MG"0L24dg]hGj](fG7JIU7YYu08=f*[_>"]
%U.)8.i6]F`hDRouWT922C9d0ch&5F4EPnpA6u,sbH\?!P0f=g*bA9pJ#t/%W:#"(<Y(q/#K<`sKd$a'Qd],3#oH-hV1\tfGJ6ecV
%\9;gmC,Zd-#32r<8_$;?'8,"Di*>iR8_J=332)erh&GO'I,&5PF*fUd3Cdu(jO<-[!,'o8`//:ZZOnQ/os8>JIn9D[ZLrq!1`(IQ
%o4I1F:S%k`Hct"Z$*TV>r=r?$[5h..VXNOf=%0M`.,Nd@=l<7a5Ygsf@(WpW6R$?V[g>KYO8Zc:JnW?Jrnc4WUJdfF]`qr';J^Pq
%F%.*-hV5R*^cR.7=L?^5<8E;]JPG-XJlD1l<8GPo+d7tf[n6?U=07OX7s56Z>KHc-[CRqH^LC^91q7[=/$nR4$%hb`HmWe!C+OGR
%^?O\-5rbQGlPj(A=&UJ\M`9V%E-fr1<]E2M'UMFEW5,=>h*jnZa5G%`%U$Z0h!F76Od`smK.BoRVmtsL"@f\:L_CWI\fOn^bG$0O
%-4O$1R:L#OO4-[g(ABeZ_*]A,f2I0[dtNo"(HGbiWP?O=HL(@?#B9;KCO##A#(%KeG]c5l0^\YBn5XPf.iHCU+maIoFV-tUCjIk;
%XW+05gZpiF<-)Kd]m0'pd$WrQ%^5dF?G''l8s90.9oJ-:V-r5ip[691Jf/c3NB&[hg2WJAZj)<bn*if<@SE=0[[Aa?SR&!HA^59M
%@E?_k-VS1NYg[oEC.c\2oG%#UAJmh7d'&1s/eX)-6T5hKbIYoL#T2B<m%Ns,A=$6$?A>u1?+Y>a\"9%WflJm6Z$("Dl4P,ER$p=S
%dT9@GGVXZ""$s8)q2/)T1E?nW\Yi\l4k&J'e*T4*BnmN_[;M^lnT"%i%)!L^)U#Y;SjRbkcNEH)X'iQ;#?+'DJT)0'"*#YfG!`"7
%_WeIZNQLT<hC#q)<9WP0h/TmJj\Zu.m@Z8K#hMq'J-QNr>"A\T9Nk>LGFk^X<JK/n]FaB:#f8Y-9:rpFlOdkMJ"QooZ%um>=d[Tf
%1IHVEFl27`X$5EH<3aQC_:@,8aM81jD/i,t(/kYC0htO<0#5cJT@Jk&^U*.&gqdNr!^_f>i)GolLgR'H4KbaPSb2A/Ci/XaX2ZY'
%m/??*]<M0gA7s2L3?O$<R/,4DXQGj5@t55+cNelbF_Uh3I%<F+r3]$/WK0o]BsQQ)KOgSLa6&Ps]#^dMQcp?fX\1::q75#Z?h\]Z
%,E'.+fY/t'`k%OCs5-gH>3SR>M^idD>(a!=KP*mF8c1$#RmF,tP:W"6&^2U"GpISZ&VoFO1'70ak0HW^+$PMa2>gY5Y9s2#RCZo&
%XW)blomXdP1Fb$O%H0TKY)OcpSuW+6[$WUn;saegA:!]$rcA'5C#)UP7F;8.pm.+fFhbrZg(!u%2s6.rCY<kYr)mkInn,;g/q+i<
%DKmJnbfL=jkF(iYmeh#$/:11%o_\#6D)uD0cA)!MN^AW@-kuL28g0V9Y-\c9<+f2``q4=-Z\,1$8PUqkc,k7+2<1n9ZZMjhdTP-#
%RV&+T/T\4I1OgqJXM:q2Q8pB!6#4Qo)cD"5]cQS1h:Gq>l6i,/PW,h=8,DhO<K6r>U4t7KSbm<?-_1SBY>]8n&G9bhcQfPppM9Yp
%G,q6J`[frKrZPn,&)=9jrQ^/j2NC+]_^I'[/"B#\9Uu:m,cjeh4?(E=GJhoo/l'm`.699^P43b^Imo1$lVf[q0YC%B/l($t.6!RB
%[qds7a2YDa/]b0BX5)G&qGbVsZ+0'[7ah.?S8["hf>YHaHL>oOd7MiSK[3$;0g\*!j69.K0Q`lQhc4IA4V\bZ`F[Z7Egc=X)>F)B
%ghW#CrX!3*O-0[UqIam1lsf%_0=GE/^/.88=ph47)LHZmpUg%0H]$,H^*J)t\mI0iB5Cq4c>%W+2u!1feYc70<mo?t5tY69G27fI
%`]!RB/9Y6fDaS5jV"JG/B;0QTm$eZTo+`Y;:f:VC*>"cQI472j[3<!YQ!;O8X`[^.?$>$SQT9PeESl8c]`pLiT4%Bl-s:_oIl9gB
%/k0a6D1DSDJ,.XXO5@U^H0=7EhKO5;h<s/,ZR%u2mJL2iCW(9#6hol/rSd!2LGr9^hpSjC#B,*"rR8ZRS,Ll_H$4LS["!7@G^=E0
%jni+,a8`^Mm]G<[ELFF/+.V3s:VZeoji,cpVr(eJjdEW-VXKT6agne!(DiuZET`,':\X=m3'#i&b@A;as80dMPhgu$G(9AKr*b'0
%I/`R0n[%]0SmS!C&F&fL^UlITH?F7W1Y;N_6bskjmlp3Tq8L,HmZP>SnD2RB(\@ZVr;;'<7lS3XQNqFN?Td67*acUjrbD2TiVDH!
%qlAPn=3(3=[pJdi%c9H14?Yk'o'5`8X-4cRo[C@9O*5DbH#p4epm]1sn+EGom2f.Cr=@nY5M12pA\<55Gk9?*GlGN,(GF'pa(J8h
%`\6n"^K<]ND6`X15FMQO`R.cCf-j!.d\"kWr8[BoWdH]%p;pBkVdJm6m[bG)SNNOBO$8OY[X[o*l,D33l0d6Lr_+E#.6'MM1AE6c
%m#Nc4&q;]5JI<Ch\Y06`Q0.<i:8Ask.:;a9hJ,/Ien(0[2Qt0Wn'B;jch>C@s(J>1NO#`Ng@7pMH*_/ImcOP2C3?;WX:ZMDJ$TN[
%<K?A8I4P#kqfm2=8:sra7[s(_rEo2P48W[l\?>#X^#!ObE;gM,P(:;k_f&]%gr+*tUWG;Ys"alZn0dg;kKt;-B\P,qn*%N#QU?21
%I;SHHbJsGT^Y8>J^TZb\k]o2mnEq\T\FBN)s-m5Ze!hr(ojN_>O$:%Vl/d3DkHg-?2s^(sl(no`->)GX:_i`9<boNqoCi%Ik.B+@
%Hr#"ZTB@#?3Tj0`(I%WPo^p,Uanao?IeR4He'j5ibbsNshp"56$!WJI5,,0Z55j]e`RuR1k^+\To>\]PbU:HQq$rGcILF2K4S*LG
%5C7QpGkCZ%IeNR+qW+T)e]%@SMj'XbSt?2(h4pm'=#[V%o0-/[^@UcdjuoL:q1JZ4IZ>I23'=aAHPIbd5J5VIIINZ6mP>s/B0LY;
%BDt!:s%WcC$G]Q25KETWG5a&-R>#j4i!8Btden;!o;b&<\N/b133M*%a&gT)XtIH.T_:<sH)rZ:5+t@.GWo(+NUAEH(YCn/Q#j[S
%L=g?BMEh.3FT9YcEdrOcrUK^[X&&GeI(F`P_;@[5m@BHOakYkR48:1Q34q<2WrRWN`\/3Vo_uK351tE?=1@)3FtXf)8,X@[rHe2<
%CQNnW0B^a<3V08E>$bR-*e&m*GW#=l<kqIXAP>^M%q'/s`A,URjQssCReb0i'X/jHVn`T@fo_S6c,j=BpSYL"#NUElrTQE?oH3[3
%s0FN@pp'^0pZMQ7]Am8B?[kD/kkX,V1iRLPr-C<JncrI&`ZEZZ*]mAWW/g-9#jRIPh_&NZ=ST(D4JR)[nlr8la/=!@03NXG:<Z45
%65p2fLY1fX/"SOa%sC+f9"DY+GIKt@d[a,.hTP>qGueaW3TM2NX`?*$H#KtO(GO0?+kB@Tl*+lJ&T]j)^"5$c[`A60DRCR9n\t2#
%L.'q&f%V^hV!ma*;]Y7fC#1(KcO@+?o%$A/AJW(?)_Z'2o^>Hmmeh8Uh!,*5!)b8&GNXF=]mFFFY+N&LP;DXGLPCtX=iE&.\A!5]
%Adtr]s#Ij)r#SMsk9K94*)Z,Gnq`H/O6b`M-dF5%EG19,%JOd[ik2#aPZH=qBtmMnm-h.Pms_T-s7l@+'93E;e*Z2M-J)kMY[q46
%>*#<)dX.Y@S9\rkTBt_ii=]o$YfPq1.Pi,q8\]`PGj*pdQ+ue+a?S"/=#_jTpNlbfp)NVbX_rVE&-dXTF!TVS04(WfqNE#gbn7C&
%WK-7$D$#4)DOJIgLTFS.\\N4=PGcbC^+IX_E*FULDIT'r*9J\;Z.qR:A%uC>r/ImtYAk`0^NoS.rQu"Q%)1dHep=M18Hh<dCo8P&
%rTU5\oD74s2S[<1qLNk/+/R0X4"];2djmY/e`#NWl.+bgobSEGBBsnLrT_\/Yj62<E]<%U=ooc<mUkP&m/$&=r@b^h8$@s`:\Ps&
%S;XqL1u';!o/FG_r9=4GB\qbE>;+TYbEMOq(FK:8+'`+,*8^$HIqSfCeR"/<'_^DBdETXqIYA\F@8[&=/F;`_)g_>)<]PXHb$!6h
%%mu?,j5KpTfoq?;PC:,jb.A21O"\dRrjq!'aX?]E2NQP&@Xk<k1Q,<3f8/;IR#>PFqX:D7q^H@AHpc[P\[X]X?X;-M05k17?G=*2
%LNfrR-WsL&bN7aK@/oWn:1]]l;Go*#?69oO/h4fqpd"QI?_4]#`H_s-\80grHuAn1DZ)+<0".0\?QC1pr8hQ?nL)$(hE^bTTV_Tk
%h"5JEb20\?\D6<os.K[cCPUL8&KS>ubWh9gm)O8/p!N$S+RC+S?QCk_oFu_&qI1Uj?65\CWD&4_^=clLN6#':=+;4tIXZU'\In0'
%I!g232.Snn9VrT"bJU*%m`5?\S+EO*f@M[smI$.+oCEaro+q9SQ$5WN7B5)Emt,+&?EqIi[PSefn"%tSJ^enbQWB9;r_AsYdq<S@
%+(!6b5/6/!qX^WJ2]EOjH2=,YqonTuUDKge-eQWRVT8/]C&lP"o.\Dr*_g8UcD4#+*]sA]'E.(\B2:_d@#F"\Hd'8&eM$+cr3,a"
%(S?-Ff-_r\nIOjD]D2tEhn4Tfjl%lmf>7L]J#H$&c1:!.>ATHCQ&oX)s/qj_QSA)YUNX\U=8Qpm\_@'(YL!#WX3hqs&^d@8;MQ&%
%UP6I@+]^coM91TPXLbs(,XDD*H-*bJ-26=>Don9:Qe\W#h0kTKr@H98J"EQKcMk-i_u0!!ms3XX5E+:Or4r5#1@r9FTD>0on3_LM
%\Xd(GF^MDmmiQ\K56&Z.WSqd#;ZGlCX#[o.B&3@$o]I)VS7hSGpU?u3-YJdDmC%,;K&Vt9HU#9q)/Z]!X<,hdkPb#7mUgm5/aYW6
%hJ89j6nusdd(Y^Kn*m22s#[,;]^Zusna?3m(Y\EM(m1_OVu)sQ4kfW%q'udA?@MF&IsH>Cm6seBCWbOI\T<p,;bZNiT57tLH/\t(
%=.*bd,!>m6M)FA1PM]o_qrg!1TDq@[pRf3gN4e^s3_VKHQ1qEcmD0mZ3k<qQCAg-:d_$@YX+)_g^:hQOrT#nLp\t*:j58A?*Z,\F
%jP*h;jn.8mBNWs6n=WH%($`XKh;nABb.n^iH`6mlDEbIWm$dG`I?>mpBl8.=oDKEsU0O!Tr9XT_pRVb\C["0WlT*SqnSdQ=kfR4[
%<BE(+aKF^tLiYoQ(ddAsEgl,,F/222g!KWZ/tFu.\=G$oO^8m[>\Kdj$#+Y?n:eS-bqm=@SklW\`I&#DIH''>Ns8*XW&K^f&Ie<'
%6H)Z:0$Y@o]P.44h__ga=MC`YFgYQP/5DE#lt<LRR73d3\Edt,+eEI,(R3,4MT&ZnNlcY/@*_auY'7N'5`9#(+I(.]'5'<Hj#;*?
%Ju?O?'H>t)$<j=^Rg]`d%i,>EG^b4'F"r^0TBu?-nLK$Aoqh/64$V&=VpiaakKjR&R5EgRjr^(s23es7e_rB:ZLLicOu:tX8_p-9
%;M^eaeSagTpM%j]O>,75nJts*oPrV6lZW(%U?Fe2GInr9g^FF+ZRO^>`*U&(D$TAYoq1Q6!S.."!JK%^95lCX;?%GL1,#5?K_YFG
%-YIdcdlnpPM):%?KKM`Q*l$'8nkUm`%aT?kcG/"[FEmKOp$T.h>%MH*ce`t``Sr"Y\tdh3..:C\]f=c=hn1bg[S2+C!*ClYq2@ll
%6K=iF]DD$'rQU8Dh1pdK?bSMtEph,/b86M^2mFi!9/sKo42J*aY;`T$e!')DG'8-/[:$^P&2"LT1=ku%>[Lgmk^ukR5JB6?;!&.c
%p$V2`2]')%%^0LO<+Yl4I87<g)q.:0bu3Nl$bF=$Q+XVq(BsW]SDX)"kGKs_I>9$[PZsc&6btF(phG;kllCE?P,@[8qW."^C%Loq
%e`X(cMEP<!&$P]i8)MS[S1!'6COK::4K-:]W$g15VmKKrPPhN@'":('p%cSSf,$)Xk0bpJi7hI?F5O]J9n$gFFPUhi3mK[ili#AH
%)go6sp1F'en]B&7o97XeS'&X3r8_Y83iS_kSptg+rN(.jK0Ojm5<a@42K('dcb'fTY(i6YXU'eJ\_5=+''aGB,EZ-dg8n?)S<*Bq
%?e'GD_q;Z#T@WBW+2>=rS3-Wp5H8>"pJ3H6+.dqe^O1$Ih7g$<cI\ljnL#\kIWb7;_q(%3f:QASp0U"0qs*HCQM$dbn%\kp\%dC\
%g"UWKr[$C(Bee0r3T%W$s8*>`fem6/LH9A1-\%UqlHf:N]2-1fEV=eS_>3X#g\Lknmm#><::6B`k9Jo*c^$*@gA1Zar>.E]IX@jh
%B6QP_D''f/h/njSUnRd;?6%D[fmUC1Aq.BHj36qL3puTs3'B:Zn#g5?Q`3C.<!T:i4^Yu&_0[h1:Nc[7@BS?I7e=).X)>!9RsAc+
%[c@)!1jA`$rTS52Drs"'D;/EGnBNn@]eQ>]ZQEKn*M1^G*M2\kplq_3kgc.YkOHO9c,J@FB%7ZnY!n-;0t!K4q!e.2p8h#cP]NF-
%eN-s]hYZ@$\"A-AiU65`h4P46"ba>OfU"f_T>pKb-dVOfI-VRjSFNl)6'od7e`VF3]pAu]^@$I*T2&U%cT;H(gUHmreuSF%oZei1
%T&%.+a0PJl:YOI=H&8,bI(Q!9bGY>aE^!--X##/%i(%7>mi(&2I5:08%m!]53SE-KPT*N`lGiphD_9!:?2j30o.M^kO6?>hD8D/,
%I;jXWqI8<drq+Uem?Y0mXMo$@puLH*]Cc_rZgOA#Qa?HV#:uiBdJCo\Y7A<-WRGrH4t[74Yl*,OS/(5M]Nou1\_hgI9Mel"qOTon
%>3GH>Iq\M7hm]Ul7=tF8mX!J*c/8D0Z-@grs)'i*Dm.o4j1"C"H*HIgHKtsH9:Yfa5LUdcn&t.cb*TakDU.X9rQ8r;oh0a6-hI*/
%Q2=u!b<P?+8UpI4.QOWnf'T6?I$C7ck8/o-=eBd.BRnt2]o/qt_jc!8cMQHgLR6bSK,`-H1S(Rpel@%Vi2schoUS4<*8c/:gHEYe
%.g^$e!LJR3Z^<S-8iQ?*H?u21itq0U?/Bu'A:JoO]fDdpgJ%`G-WT'!@_\HeomaY7^ZheBH[i_Y5mdu_r_E42;')<8IbA'i(?=;e
%&aY[e/A5gc_=HYJ4h7?nhNlpHL0,ieX>P60UCLp.E0dBqUrSjVFbo9tZHFTC6R9<EWkV9&5,&r:?'*%ag\H0jjZFmDgt]/Gp-6+I
%)q)WBp(XC0RkI'_k=8`X`Vqf_H>1%eAq+qpb*-%Roska9*bG?9&\=CKiO?#R9AamXKMa(nbK4^oqU7qVrf4-TgVVYARW$MIcfYNS
%cBZ%'H2cg#?7:Ptf\`Kl=7hgH\YH?':62UP9D+!f:&fFd2P16Sp\FLFh],s.s14EhqIdS-HBu=U^?*3&-4iddY/cTAHriHq@0M\)
%:J]5@3c?2RP4-e(rSt)%'*%I@T$O=qI2[EXEV)+;QgW6lIIpN7MBC^fp?msNhcQMt2Kc3gEM(YW]+=o8h\?(gS,7+3o^#@`P#FH'
%?<kh>k>,L_H`^6=hldRTW$_\d][>MaVZ&0uc!6s7>(-CTQLa\bJ,'Dpml+W%^Eut!5KqbE%*^\NCQlNFf6CB1p><DPm\T=Hp3uW-
%-dnUEb=c(3Q/(HhdB'PJ9E3n]D8,cIP:-7U#6'>K(&)I84Sb1S?G5mcg90Ud1T0R'a&aWqSt+]=M<c8^A:t6F*M6WM\2r>.-@PeB
%gF3)!/r3;QpV%$8$@I,KIW9D>^[p]0B(V/a,Z]HKpj-W3c/I3>q\rP4IIPOqR^rFF5EZYq]QDab^\EVLB4>Me7u*'gpo2JrQaH;k
%G$_q]ih/I4PA&dqGQ-Gpk^J_Epq[otaqJfC3'BC8k:$%bs!1eCi*Qrnk<=7cg%S^7B?M%c;`MO!IQ[RGn(Zkts-'D0`T>@dRJ;=2
%Uae90iI;L538<=1,,XZr=/PoZb5A<O:#9Y[G#&M<W"5(qlp.U*WZU6.qf6%6[sS")48g*gs"<lT--*0tag#X9R5F<fIJRdOW@h$h
%^\6]a8aaEe_$%bq5nUKsj:]C\1\6t0$g%(Y2P]_;Lb![EWmI;#S6bfuAo'YZ,0%`##32NH?$=OIRU'oWEmFC&MsHSlE7S5K\R7CB
%55G@TR#_IcVHML0@rVkRZoJ<DIKiWT<dct(bgI_[2!/@*Y,ZC][6s!%jd0Ue+$:pokJ\6E*8nOZjeZ1R59k+@eaqLbgP^Q$]q6Z5
%gKBm"&AsR3%o2L(>M\asl?rUXoA46dimr2.?64K>[IW%bjgClXkgh!)fqnE1HHe7/N7O97s4.FSO`T>>fAG^:^M(O8WXX,mI'*tq
%YO3BpQdu$!n9BGjn*9\Rq/O"Gim&^@LG.h=lf@u%Mu1hpqio55jj+I@Y4#`ac,JfC+27#^?2*Q=<q`XQhK,:7j1EGr\iN$J/a)cq
%o&-#755<Z$]B\\,b#);#-`:f/Du8k#W;jDQIR>jH072f'NT@e5+mqI@LpAkmf#u,205.EQolG?,&LKHbBA?@8Gl\pkS.gD&c$KCq
%p<N?YdJ<H.[[t%:)`DI&6a=Q3mku,,H$.ImA91h?T/f@XeQji!T0ccNm$$b);gIa#Ep;YrHqn#%*h7gF.(?Wp:^C9P0MT#>GsW!E
%03d'"Qs<l;^UU&!1hrp9G>2[%j65M%$b'L+\%NCf"jm?(S((MFV!sp*GW??W]X.WIT&dr(kMP[`\XF&6r[t_IcVF2(>e+@#d.,#Y
%*%-4I_VIG$l0`c&)ZMm&H,1qh(>%\;-XXWujHK$c'Ztg.N(mJ<9]653.j9&KG#,=,i*B[Egi`>=UM$o0J*0^uGJClTD_%^mNR'VH
%qX0pKiem[)(=Qjfj;6u<Et:FKEtb9\N__=3^:7:WEiu&#)Z,\E`1l^-dK-u\YOCmLg1ob5L8o4H'mI^9=019CSfJ(X[XL-X#NY,/
%[q32Zgpun420Y#sc"C(Th=2%NPN2W7ad;0D)ch9J?[C*?='H'.558AV,;38h_ihY'eVDc&:N"Gf0CLHU-gI&mO@eSiF@1tqb]pih
%h;'aVfR2ae<]8'FkdI<g*7'D<qtf$55,aIF0A/2N(?nZPj_<8Dk,n@&X5WmF)Rt+WlM,*0dU/Ld[9:)D(ES`Tm[_scUU('qMnQXr
%C;11T]&h>7ZgYWB@fjqOdb6[[qMb`p5Bq@Uqr@\OI/,lMLZ6W$4T"0Z:Hs2ApPle%8^cAiRD/V:\+K1f]ES`#n^nuagsX&WVn9A2
%=0hYaHGMpNh7J,A6WcoWn@&=.f>ZFV<;CUu$9c)g$;-@bIsl>t+#`;+*B*QFeU3q$]&"Y^pYCnYrj`bJ\#SE"<9F/KbEkGmZ,$=e
%05J&rk'FA7s5W,7#P5p?n*"(*!sQeP8t!(J7C=nsL/?6^$>KAlF),d/P,Ag>7VUC7s7#qg@Z9c7.`SY?!sOAYn?4fdHM+?/?)Z3G
%IMmTXT=8P+^YXJ7%"[hnMO/bgnoB5)[GC5q1f`_fPAg2n:\Rptm+]*s#ufKg0?VcDGl7@js6K^_+90W]O+1A_rSHM:Y($!;fbYb/
%l\GJk^\d+GYPr3aq>Y[5?-SDi]"7?/2?.+,3EnL'99sYEp2\G=gLnh"?[k@6:+LJ5^3*fNFNl:l+or8M!.pG/>1d@Gi[[DLdP@E[
%r"9C>lQU4<A.QG(r?;OE5U?\keNa\SZiS7*>W,XXp%T'r*h&+Z8gt*"9IFpdQ%eXR!uL)4g7Cu3.$841PK:$p't?fJHaL\EeV\9T
%K5iB#DZ0?2@75S^h%mB?eb?<Zh2_$-/=)Y*>hBI@GX*%&Wu50qKBT`NC_Bd0m"-/]Wb!S+bj.@n=0=Z#*fDYs<A1r&U2m93lor=@
%S&.t]dHTnODJSIe*?0ZlBT"bB&H54j68(eUI&lp0!nhg9qkt'ecFUi!r&]C1`(!bT&.b^@fu%@<%S$!"MfSK82TK<bK*HbY]!7C>
%f2cP8UfkrIR3'otegCR\Ym$Z'O]]tFMTO8GWU1o8FfPB*QfVU97<h#0SF]a)l^a%JDT06?L7C2lr/H0.rp(@Y1brKVVOPr;Ib+q!
%SsiEI*MWZ"g=aN#W&^*?TrZa<&'EMI>@iEtRAeJf-`QZ(?ibmL.945/e&cs8K[P0"bN2=JU;X]6(/&30b`=ob)HmAUQ@75E:ceM>
%=o0PKaK`-PSe1Q;><mF#Pb1o/Z)j#WG!Ea]"7]?Ak;2.EF;[dKS?Z=bMB'ND_&:G9o/pNoNdq#uY1"9*)7l=@>t_f::Th2O]BF9!
%6(=SSgJ5hb:=!>lp-+Bb&RU!pp1@o3<nOj[g';nDrmWr2LlFsW<R.t11U-&bWOSd&>>7gi<Zn;V+ra#)E9<9*GgA085)iL*F"MNs
%UDQU;i18]IPq0+Y.PZ78OK"Gu)2VNeZKV!!e=!qdW(JZYqIj!eU;&CLIh>.3jX,p?HkV6+E^^OSK*Cb9U')''?gnh^]#!`nY(<h?
%Rn(?SP8kG+RWY<]m_-%]\hh?CH]e6F2r#?bjD:HHk`Hg&2eX?CQEH9&]$riNU7EOG/:\O@JcuX0J^m7;#[;Q&-LA"[SoA6<*l;&8
%."nYXWtXnrCdiS<DEHgJ07sD</X?\-/5-AiGVKfS[DilM-'0HuQa'$d<<#@BOW6_ZEAgD`C.+-PO2o=87*s?nLeoL*+N!g0S;?M[
%-_^;T.M6M:qaAYK1(j'90g[/1LCTFr*/m;*I?fZUd+g#>@AfYE>5/e/)TbXHP01$tNGR1%qK6?lRX8+pGjbkRLeXdM'8Tt/dieoq
%c:*]WjU*_V*_aLg'$:gpaQ85cKhN+`?gC8L$_@K@*&inr`C2-`6u$s((_96a?r;juK5K#;)4]).BlK]h!s=r1q@*jugB&;r\+6@)
%@btiUpjKc7+_2St%$=KSBS\:`'goX#@1B2RC1G>.Op+3]P%+s_9ke7=?0YEWMZc?eH"/Q#.n&8Kg&gT.5=HNW2'B+\*kI(n;+r,k
%(Mf!h)Cd+udDCVS7f.5U0Y/c^dRZ)EdE#H-Vbu4Zcc3c95;PdOX*:>-:D\u3$5kd7)sa0%fI=)Fl72!U[Z#C1Yq$h'`aNk31/@"t
%X`6dB<ku_b.n:DqZULIT%#+2pA9C?(->,!:0Tl@Yb/=n"X\%gMO/>b95a\8qAMkbOfknkl)k<no3X8@gO"Xr,Gj@fIct@0aB"?.&
%ZlYa@VSa_=0baP%&D3@%c$\+/]NRC/.R2^Vq2=Sr#P+1B=WqDmF(/!MQue[gh-:OS&Tc-7BpsC<p!Gm)/U?rf5:Ds2ZglU59STXf
%\t5-;*m=7b@0M;@i[[oD94TL+J;kkkf/&^tF1nnZ/Ztc.]p'@:L?+N*bbn_#Z;V\`bLLHJ==r]jgZkAVRoZuSoiV/=J&eR"p8g,O
%W(a;3:+0pq/8YW-n%-SCB7.m:h.BT6lX_(CUM+<M"UUZ2-8fYQ`;=AeQ$BRQir5f,#;OY-h,Q#7:1lPp:""fuD533*m@n,"G,mA=
%gV$Hsd4St9+a^_Cp=L$KokZ:-ee<Bd*GsF6HQls,L8p&hrO<G"LKQc$*?Il.khs)<d1K-ZCIaOB9cYP'IctJ4V`b:G`uW3Hfkif'
%qgR:6\'MZ]>28kCrhNu`<!VL.R<PVLB-Z"j3JW?qft#7^*p:L=(4_Cfei,'23hFL+!q(Kk'g'I&Y/9sMFLA\K`\eek1"UTUDR^.f
%lItV`LR`7ukEaqErn)1&2RC*/c?0uuT[Uumf:MQMI@\-:*iIH3H$IMGj,#;sLlEc\[l$;@Cr#*LAnjo`-GA/Y%;@0IFdL8rY;e7W
%hWQiMo+l]i`qs(8pUgJrc?h,97\n9U#=t_(>a&T6(ame!U62P'eHS#`0c[_+`frJpb5rW?b1jU/Y[Us9W?I\)C3tN/7T=J*m?5FL
%D*BR`RLWMlFD)cNd$\RN75l6Y"?i/R"T"KC=rFqUfeGU)+#$Q0:h,B7P%M:g.Di0lF-/=:$7)ia]pG[]%0cqCRr14qCsuh(Yg\p&
%bao^$R<,MO(J\]N"tgLIR^nlr@->Pt3QW:I%Y8g`PV=agK-VO?]G8.aY4VP;`jXie;Tg7D3\2pTp.66`E/@nu.F^`W>*tiR%cKN9
%K2&Mq&-L&86-M_3<dYGRoAVM==\6V"KU1h9VC3mZqEZUM('/,'21aqK!LM-;CmBC^\:K2"'i+O-e&B/4X9M[4j#:O-HTrHD`;Q]N
%4o;9p2s[(mdT6BZ82^OFc,^&4dFC!K>`;U4U"HUTNjJIGclI!mK-A:R*m>-2j']W>aoi5hNX<6/R8YN4Nk1p3`fUW$hfB&)Tu6G/
%$+o$@FZ>UMFi/o#[MU(*?#'.V(ePBY+lSF%!b73hCh=kHfrbi@dHdrHB%UG4Yt8^`2s-*f]Wr"`MD_VE.8oEA<AFgMV"Bn);)t/d
%:1+b.j;gonVR)t0j`3p_B;7k#2)VQN<@HqSC:`?nW85H[<Y'gf-0DUgQ8(gMfOM<!+ifn/D(BL@XNkh#G9Orsdrd6+.U:7*_IjB+
%34IO5V!NBF-IF]3F,?[@IS,c$-CRnlVff;ULkV!ra(Co`<AU>5Cq$TG[IuSK>"OQLg4#mil-RrD9NS6PDL,0PL,-2(qj[u,pZ`W`
%Q:MQLQ^=dJ4LMK25)j`tb>t^M,J0n`=#i?2e8q=['$XEl900O[r']BjbSPPmO1"QgcBupr"!+f-ai42^[7AZ![c-L$*+Nc"O^8HU
%`Dj?g/bUj298VlXXsGg:2j=Z4bm\D\1FI)ogUot\*+i5<\Lr3Gj___tY'?k1=s`,ZO".hprD4dK*G8BRL',u#cW7!i0=+O#0s2I2
%8iu(6]iQDSg8X@Ef=>4LWok.k>^^4@XVuAZ_?-3]4J1.Bn"SL^36adp-D0Y-RK]V6hjglkj-Fao&mB%Z2JRBh[.mB)Qa:nn+rc!7
%\pXDLD9SSg;mWg*SRLZop7sVSFmQq!d>=<Yn"%bf&F'm6Ns40XrH[;!>:W^Zh0jDjo[k=*lkP?N=`5`KC4/m[(@.l7PgCL2(X@T&
%9jJ"O*OQs\g6_?VPT"\!iCLX:I#esl_eZ8S)YqRqii#&?(IojnW&tjeQTD>g=3acl][*C.'AbTR9hJpd1tgI7p*lIX\S3enY"UcO
%IsK$ZO5jB:dCdcUZNr@"BI5Si<%S"DqRe0k9PNZs'YW8t@^A7hQ>FV0?^>H@p2ZMGU;V/m(WWLTV;p@6%eEY=/QjuqF^:8dom(Zs
%D?@)+\7o6Jl.45:L:340A\7?Wnat+APH;B`Q0R_U?8B$7$(f=(SR`Z9`@)E[ct't;-s$1s_qmt\J^%%3KXbM:2)Lg/`\TbH4Pfk5
%mgp+E]6PaZp<D#4l&V77[d<[ahY+"!rRLr;J#7H<m_`WdIou3e:X=CEEEci.ZkJFVC)jpK7:2>I?[c.3RA]eS[t=>;q.gg5+s5R<
%IAlkH_oNpI47[+EB";,ac54fOIsq/#8m=SPmFH2h^Vf62Rcf+^5()d?)"'S+g@]G9YfcCJB`6sH!?3Xng<Mr.B_;n*Xu'W;\<Y,a
%Z!I.e4!G[m>W-A>3V8h9Hn?5rNP7q8&^%@e?aaIh78DI>&<G4!d7ggHOX/9<8tERb;oISb3$JCTi>ajjBJ6THpp7p'<\&Sqk/gLZ
%&n%UPR(S-mWlJV?J)Z`a["+jC`m$^+4`O>#?%m]N]3t?!4Td;h$YP8S?K3MNC+--B[/k*n)4;Oo8^/MO1SJ,iGN<Mg;u`HTF$3kh
%&m32d&`;9JFfI(c2/Z&\B#"W:.mE/5Y3N+C&C@M^l'j,[96;>i;_K%,=O]'j4[oL7%VgY^k]=*in*T)VE^V]R6?.o6P?`)HGFB)f
%iT(>^Um91bNfCT4/\5?erd\fs/5@M[)PANh/KUHh*MIf5\:@0!E/h#dcIa\[cra5BG:8uIVL`[=92Ce33G`i1S=Q^NX]UU^"87O8
%]g,4"]YE<_aLe^"lO;qR?C2?1QS6fh+*Aqk<n$4mBMPd,Ka'n>I*8I4#Y(\36(?Ud<Am0`X7`0O2+OC?J?]]-8K=1AWi7mp$jk=0
%cP`ZWg-0gnlsdQ"SJf!QiZ1L!Q8[XY908rLOKu=dDB5DC8X?0``gl<7"tXc2q0kYHGk@YaWSf8AL)_(//A?+YiW9!-d"l8?qGC=1
%TM_IgF:&T7,b:W*bXJSkGu,m<#R:3j<PF6jH']B3'bp)U#&5UO]*R5J#a?oC7^]*E0&m4+m:1:6<<eb=k>u0(WihlWbaUW4L-r"5
%iCC:R+K>'%Ecj+Na@4Q;-Wb!d5sQ3s/L**@]Z6InnF`q1%D4?-mB%J[`/WrE]G[iZ=(dfN"KN7#%dhQ'dGN<PKI?ql=J'Wb't0Nj
%7o#sNNV2BO@;I6P>Fp)DTYokjp/:@S2IF)qXSSEd1ufcX-qSLbYI:sPTbK9,q;>&#>\\pYr+I2B="acgR_E#kC\*09T2^<HIe,t#
%1:.,(*^*0lUT1J.Bc=H`%ef+<0>GOsAAFm6:F?qbA$#\_M5HEI3jK(&T;F=)!q&>o\ST9\VH1Q1NrkbM`QE1iOUF;u(QZki,"Z?8
%PB_2TD;e%^KcLfKL(?0``;*%A#5Vl\)#PKZ>#Sn"dt4#jS#='&'Ym@8jNQ6.J!Th=]eNd&/r>3h,Y:`qS,],pJ*O\=7@30`O$fhW
%?)6l-<:\Eis3t]7Jkqa##\5r.0`@TWELh:rVD7SiS96)0rQ/*C%+;/")77@?!]+VTQ`I[q4ArA[L@LX5C''lTPDjYP)EG`@q`Gn.
%n@g4dr[Pn,l_ER]!B\G9W.eqYgR<gu)77QJc,&pTY;Ap>.($9HH/\86R9!mS')4sb^K'JfWe.&YboWq8b7N[;W&*,\HPmI=nT`qd
%/9"*%!:QfC>28>Zdi/EdL%WX1o@2Or_eNX&F&g7H:h6O+0&AEF6U@<[:\D]JASH`W8"_>323UVCj+/%[a%p\nk)Q!5^RC(JPtd_>
%+FCL<Np9F2blA]^O1[e6fd`a]2*c:<`)m;/N3:%YF;,5nS@_n"mA?/+TAi*5DOr6Y)cRfr2pdA>5?^s[hGDUuaT3gk'2Pb:\!2RF
%pn>nPk:9]HM#NWh8hWd_U0/A&eJ-/;M^$_97&;6*)/1gnX.j&f-@*ASfO+_;Yq"F'8UKC$_7UKGL+kt_/VL)H;AIF7r\T/+TP6Mf
%#c"UF^?L2BBIX#<<"YT,l:;5fU&/\B*/&3Er"2:6<K813Knku^MN$ICBY6XW0K#pNm^RETm[=O>Gc>tj,JZUb$m5BZle$glQ4pa;
%2[]VT587Ho66oq1'b!7LLH2"l\8Cb5jT^8HrVO$EaO45h<5T=]%$Q'\*><rYHfc&k'uU=4BO/3E&/2WHY*"[ufkAX8PaDZXGCs9W
%Hm]JRoG-"$H&K2tK@`b;%9ehLI"?B6r!,h]&(eDc#34jfSWH8sZeQ`$YC,Ei+:g?q&;\(N3K3i_LTK(O&(Z6IM_:3rgt*=1&oN6N
%FGU!?8)a1lpoaX.eAK'B%$>nh$0r/L7/.*hG@q;s6o$Ose-BO_>74`S8D#-;+kEl0Rqr3lU\jKb(&-k,k(00e,!oBOL`:""Tu6=a
%Y/+"oBpt3c&"ID@^*Dk9*ZTOGk&WXM;<[/65=K(q&N1`^(T.)GX75L.$J7-9?h_cPFBL>C>XiNX8`%SNffT?cE9?Xu7$F\EbRURJ
%l\%:G#=NMhr[EE0QHXo)s&*u-r+(q7baWQUFmc\nEMA5OgHnjghK,FY[oMP,I%:.I!pDO*Z7'E#hp8Z7%$CoESo^Vb#?nR0oVZ_T
%B9,M0VbdWHO`L#T70rneCg:S'NA"),9,Pn=_&##*0iGL7&i"$J5=ld:62u_<ptah>..`FAf;GKuK^$Cm;Xf`_AQe6,P2@Q(:7*/(
%O!"5p1mb#&d2_7^;b*qe?+'bj`oWr32JBjkN/)X!-K8g%dm*CTKKm`N[ERdJ8b2m]L6($c=%WBC_oJ=sB'=\#+(%Ar]!]H\K$B*`
%gcLbsU,><qA@imrG!Ju84^ZMr13(i.ah4QEU\=`>gsc3m?iP3hKOWZAWfeA[U[V:aMLC-uJ>C'hlcfI%1`+P5n3/S\U[pq@X98Kn
%DL@O6>#-s<(%S]'lP:r7Lp*5u\h!\8"aZ?<R,$15ZPo3?/'I.N*C\#,J<[U\B=aF=gg8e2j!sdu4%"jU>H]e@/.gEll>7lcn=\iO
%/GVb,J%E+N_YQ;CP1e(C!"mFLA,jthCS+kU:PSfMA[AF-adEi;kn(A5+93IIUr$FQP&f*u`6D/j*jAAN-VOGtqu5N6q9T,p7]8AF
%@')<`1r[-J4=+XM5!7[-<J?5ndkZFi/;QC%HtY+1+BK2>'hgB7-TY-M,V3TDAb*9^[OXLpWMftPFD9(<Mt7[!)D<V61dJo=kK^]F
%npenfNrgE6s31<D93qWc*RC:/&FX`^`cu<D*RJD4.m>AVDb5V%2Q`4$&P.:Rg\d6U9)2(""&FDc)[f6kmFt-0lD=jf.CnPTZrW(o
%#BQe!<c9='kYGFrn(tlEDTa;kBWVrLT-i$l@h_MQ4CY]^bHG`n3cSK7W$?Ihq$JsA4#Dl.]($F_Zu#JlT):Dprm$LeDDci_$m';F
%QA3-Dj<nAt-V%TW3cRIp%S<[`m4ZP*-c3NZ7#N,Xp;>]-CPO<)VeNX-"5RTcG.5Y=Q*LrQb3,/@_9X04QM]$1HBGf'EmZ!HbZ'Y'
%2pQY2<3kpY<N8%E/:+r^X,a`i1qn8F=B4)5mkk7'YIOoT='`Jf7u9VtDJp7Cc\kb^M>(_',gS'L%P=$Jc;&2k+"lbloh8(!'!tYk
%q0d*)"fRl\Sd0>MShi8Wfn3rqA-abV5*GG;/8Eu"bp*Le@V$cfm8"qa`M])<B)o&rWgZ,<>lIlH1lqDMhM,98k77,QPPlq]d0:FO
%@#LQ1dM4O!9`nBE`^Wt/"1dT-n\($cA:[Q)=&BX5a@sihWg)N:9b6u_LY't4Wl7qopPkI[(fKK[F%p"A:O.PPFM],,4Ul\XA@s)"
%Ms<sdH<JgIXS?9*\'e99-B7/c#Q$cK\skeY(iQYg_6.tB2pqGK5*D<A;$?8Vm9as1X/a][V82LX<Og!Ei'S6-Bh2,[?G_UUOZK;\
%>n.Pej.O^QK>MZCise;#Yka5(oK'rO?6\P=`(&VX_Zt_I?:@8KJDYm@jGS&p(X#5Eb2Fdje6rr3fd$K6XU*G3qp0GIC]K`00,!3"
%[QbojeU)J`h]fEU/sTj]i1=jpd)gNA]j[anAdMgQM86n?\WadZh^%`#^6sf>UNPkl.S(@lDI*E',_Hl+feD>bBW8=@M>fj39I/OC
%%Vh`BY+p4l^#Gm[TF7%&WB1TrqNBT-Bo"L(:nN:?IT$c\,D"3-KlH/jA+/I*qieZLD_utt:$3,I68fJI,0.\0qmua"$3i>e,qf,?
%-ro#nn&qVtB1Qeu<g<if4GWGUZ'$mP-22rpi5O><f$>f"6AYN=YIEB$FB?fg?3q'K>!e,m`s#kt/GdpV;^L7^HD\6#/Ermq:!mST
%Td0DK%6SN'TJ%jo01rZZAT;gZ5Ihg*>N(gQM$\7I_*",.Qsn3LWgsFZp%3',;hm#%DYiQ)%ne3n$Fj7?QaGrn%r*FS>:5%DQMu[Z
%\n#$f`HJ1)=0C)ST"skB1,^2/-tT`RXR@JNjiSq0Pmc#FN936>o]\<EMAIL>/j53g"9("$SW\e0A(=<iCT\dC&$(3u+[CR/#A
%@2Y'1"#TP9ig',f`KDG5[L)I+8M8MUEOi^mp15l<+5us2YB**+&*QHD'!RkLA&oKY$Z@e\R!;TucK!<VT78i^4Z1+Eq+/11%>^HU
%0V^nYV:E]Ar/(r`ZI<,O"lm5K(S56(Ko.1YcK#i'(:-4$#4Jn4N,+hSG#RUq<C-")>K5!*E]1JFp(Nh5B*+BW>VS^snh0/d`WP`c
%H;jU(f7%hp+.W95r=mpGU=^u101s6DE8="pZ=lg0c#g![gpSk5ch/T0$Wq/2oFs`DG?TW`UNCYI>h/_Q%PPhoh.eFi1ng>W7\'a0
%#.`gaefcFoN$r\?cm%PrkuDZXI9s'!B53*Z8*7YMLT1:9EP+13??,kCR1[+FK--b1CQQTdof4=j1s2P^ZG%S\`8)O'j,M&j@oT"*
%&OsZYc1OL0iW_WO):0L\h@q._q04Gl(OsR7lj7gGU9EJ,r17RAmH>9\c(q3EId'_ua_u&s!FK-2at1=ZHh&Q8M.k>1)Z<G<qF=Es
%Fc=!Ep#^=>d/`43qf5H//Q]"8d^/C`lXMan/'X?%c=oUDG:Qi@%(l_tM??8J>%Q*HMPBWaK9q=GP*9Dr2S8tE`22,fdrL&W40oIK
%?fnb'-F\:\XePHnI7TaceH9Eih2AJQH0/C+H\8NBKqcO(PRH&[bfpNBmA6"O-;IZ%05*u0?<9.oaIB8sUNuP1NeRq\T/!;c2(i+]
%O[(\eGmVq?,E1-0#Q`;S,1gj_[`s>V&2u\ljdk!9m&7W]ra&u1\u9>4/WS_JX&J$966UVpq]`o4^MD+*iWgU*C=N[.[QCSiBj3`O
%o#GUH@5Dr42[qIt0O4rUb2M"qD_?+#1_ei$CB(k4Te!(e3Sk<d>rt\CFEGGBO\DAS6Nm:NEnj!J*js;iHk9F++rF;WaTP/_N=(rm
%5*.akHLAgOU2Me)=+0_f;,fGkWg[V82g=PgF?uPq4dIq4>5@%=)BX()$iCA9]PsSMYssi<O-EUp*2aF18'&kMrsaS3h#LI*D";I#
%J)i.Vo^M(tUUTdW/Bg5)?1L=k/m#R>'m/(`5DN#4.Ek.o6M7$9BcU>fU$(1M!uos_%FXmA!D2=&4Nh7XIsCgQ#jY-g#-+\5koE.:
%OAV+o+d/.f]O)?m%la>[om$,Y7N"?0M*\kUN9*AQ3CGl)$Rp^WRI?INZ#OR5p(&,>Q0O@2+=KkZAZXKge6olJDLGA"XkE3O6@5O<
%'%Lg2o.BLBrSl_T`k7j@Ed%=XfVM3ccO8W6jL9a0S).ZCQ8hsu#31/d^p<-l6e"4[pg[nQ#031d?o/-cY70lQ*'sVG[gp-4j%iX-
%qBcTQ[G@Qcm2Hs;.Ct=/?rYoL)tI=,fq.SR%gBprfD_cL09^5oX^kODj/W24VEF!8O,jlZ=MKb*Y`f__X19hC^.XSXa_ARu.(q6p
%qd?#/ElkpI;1o@^9-]M]TO<s1B&o/+3q7[FT.2,0]WYHW6#WW?(f@ntKGacfa,Mo97p2&s3-<qOIH>msmY;=Rrl./]qVp6Z:?joN
%3Muuik]DA/<R7pi#h!aCc#PL0[920/bo8#A/BOX.5Yq8N#Xj(=T^qng`Rf#WnX"nN^CZY"AT9N]IC0G!p[^$KJ+E[dhT_#D0YIC-
%-JGs<Dc2<"4%*Fki\*4IcuIW]4-9F*%1;UhLhNhFBZ[M_D=gOM00E)s7jR[*C0uUs^;`Ru>o(t8+7BlB4>oh.l6j'M](c0CI\oc5
%]%,:!E1$X3UC>X_;9di2?U&BKE0K4/k0b@DXEn&fnN#>gbCHFk>_]l4j/B':bFncN-aSSW[4T#Oe)g,E/u<Jp#7/[!JdfnFO$2oK
%)rKH[Y#T$*LQmN#p*5#g>-EY%OW&sN$]I@=p0FmC9;0A_1tn>3n,Srnl?hlkCL1e8l3P7r/f=E"2<MdA1=<\+rrU4^W($YS#S-I3
%laju\!OD&*+g_b&UHeOeI',D&[CiY2/2;0:JU'!T3hR0;kI0UtGLN:h%/*`taf/G6[S7oh'CdD,idL\rq^)tL5HLX-HSP$7TUbN$
%KR(E?WuIFQL4S[XnL:u&)I:-?GMER#Y.#dTZ]EBp=2fdMr\1o\s%"Q0dC%-^W4p`o)rYD/4"9_l$E0)=fsrFVg+*>r%JT$$:$LE[
%Z!SC@),JR:`CBh5p!8A<S4NObpHA?:h9^r^97P;77]>L>OF<$Q0T(ldbJWdlL3a.njjtN=^5YJ@l23OUr_ta=4C9#U;11J'kE!T\
%rjWceX_Mi8T;EYkrV[Ef[5bq6mo]*Yh:p?H:JQGcLBft4LBm42puV-ZGJe>jjn?=bG]DGUVs=8RST\bqVgSufgr\[8SQ>OQ32@Q"
%&!%n$d!ld^`pr&;Zb5`PoC]PW0B;j=dXSg:iI-6sgB,>[!AIdWZTkSlGaM-oLB!)oG,NiN=9#hin'>,98QE?Jn/8'Yjbo.Jp9\bF
%r*O1%!I^F9"rL_#"Wp;cO7rVF)cXHm>TTO2UZ!L\r6P#,YH`Q[k9n_hQM8:uJ!Lq>oY2(WGP56#r/#EfqqVBn"-l49N%Sbdqi3N5
%c90DRnn6!gmI"&fQi%?25FcK2rsNC$)<h+:T#b8nI'!eG>h^=qGtU!^S*2NiS&=/LVf,c<H-_g!LBDBT2)HpK*D"ERBI&8pIud1t
%8^1q\^JEhJhdPearp_e/bM$Qhs7snTF5MY,-QN'601[$iXMao7(Er:I<W72Hm![o^2\U\I2=&:s=Q=ZcaIiCh_bfoN2/'+R$P"gg
%Hkc*g\D7!/f>0VMSYm<Ye`ZmJInYkZ:T8e,0n\V;f-ar]IJO932f/8eJ:dL"I)?n+5&Xd%/X+>"KRgYk?-:6mIlM1m$+Op+Ho5@t
%D34YF0%nSB@!!mlnnB6A=_HA-4t,frc?tni4a$"YIB??ldt%D/=QY+>!?3UW7dnMO++K$&IWjFIJCM+m3mG>KM;Mcl.dk(*V>X`0
%1*1=>[`\X>>WiPrGs&Q500eUto(Tu^Vd7dhML@NJi/?J5^FK$EI[m>-%u7rM>n;n,pgRU;085WLS(iMS+)iU*hC)FZ]H6GmG1jV>
%?Ons-S_oM4S)*9k*S9q/5n<i9#&N+Vm3_R[^5!Ns.lK0tiSPC1)I-X#W4g)/f&(sSDq[Dgrh7)YoL@6f)pNAA_kGNFkjQ9&NRMil
%=nG;*rg)C!j[#!mj,l+HQNt'27W7t])''Roj0A#*%:1NYZYXA*p!*5SPN75(30m$<)(#/&:@4_ro]P\Xa>Y"8C&[_YIe1(ek^Vgr
%F2)^<op-RA-i:X<:ZC=;IBC=-b'$2i"o.-:BTAmA2prpI,"XLk3%,M1RK7JS@-TB8%!kTF&l/P7+Ne"'6&BndJs7D2EG(_U6r<1f
%E;0sT9>f9eb5Q[=&=7`ZGKI>9hsdiYRc&FO+@*]tN'M_\P2b?,=t(mm+tD^2.#u!uM/Yu@mtA.u<+[55BtuTS])ud;1I5R0%Z#':
%0>*:P(,RPhW0Hh.BFtF`Z(+jW!<Ot=U*<nV.9&]^(fo)>EZUNu(/RVL5T%G&LEZp;Yp(YaD$:G#YWODp4X(->`GHo_JO6Xc#m=H#
%)iJsFP_U&GDu`Z(d)ZS97AqY2+q\-2ns"pk5q6+bP-b3X["i.*UeRWhT%;QbJk$MODcm:g#am7j5[$HOPU7Hjo2%c!"I>pl4od/=
%WtoM<0r1b5Z4]O>A[PE2@PAT)92>j&e>$]Z7%aZ((\,/VBmO5W<<EMAIL>-A5=V*m\4T<!_=tZD$9s("(b:CE#<A,-';]E
%;BGhMTd#!q+p[rfH'-*PP?:H*MHCo\JlTVB;*e\,hsQR45!^n!,qu%YZbQN\+HQ$uSA:'(=a36,(uuGpc&4:6P$i-ui^L@",mJ@A
%$W5iD6brp0`d"gYK:mg$&2%2N:F7KQ&+?1lD)B-TksS@_"Mhl,6$X;Nih&glUf[t.&W0,$hC(i1.H7eWTO9GoG9g?t`2/u[1^Ea8
%7#BHO(f"U]&LB2Y/Pmdp[3`6;,?ONa1glb]GRqiiVEB)/,G>a/=]Y,8QK9s"6RbZ;,]a5GU/mE>(gI1t(',MU8-7DlJ0IOSFCQg.
%aO]9@&l20QZ,,;LbJ49!&U>7%N8uj(K/n$$j#o4pihi@jNhg[9OAH$<)!&ZZUo,Emb6/TmmCa^R'Ta!8,*>=-jV\aWOi31YI`1I>
%":Xe0WTXp_0Iqa[-tlpV"(FY$Fq2'C*W\YuPVb>ZTT>NhK8q8&a[!r\.qgOB>(g91khNP<6@fG>OhbBNK:[<c[i$Y18R6[*K4TTX
%l!=t<*_JgmAG$Q<.+D4La<54-$Fo!pg(^32-@&4EU#:06W"r1p0$=UKCV;/a@Yp4s@<2g>LLH8a-s"a:MPs_fV@Qk+"\D&g;@?'3
%KbD]V7A0^<!PfqW.&MS<o$D1=[1HqQjq\[k>!jOuNhWnZ_!j<aU*?0AE"Pf:=p[PO=t&P7.>$+b"_fe%J@-[pP6=9DE>sU7:=K=G
%KMSP$9nn`ri?8>M">)l7S-W<=*\*g*LjHl\/7R.\&n-S-0jb37,fT&/#/^t',64+I61EjhT>Ei-#9`;+75XH-ATaed29IJj;,=$E
%$m8^aiOlsA,:Es<]#TpiVcb(D`Hs%9=hk%BKe*ZAP(:P]#UQQa.M,`,FX'.#bVo*%Q3EZ7OJ@M3'^/+B)i?9]a6TuPL'G=?^lW)?
%/REM2TEcZ=d-W%HBJ]bh\gtT^7FItQUX?%W35D2c05u5+#mW1a6q`-orCBaiK^4],[P3GU>,_Kn]N,W*pB&KaJ/_B-acLgILo)*;
%;'34<!T8h]7;r65a;c0p!DjAAMIQ&1L&)WFdS2nRKiEWT#%L4TJ3Y:;!hL\13g9nTUKW&]&mu^f,-?iI'M[h`#t)4U\T4dKG<dZN
%<5ohX9#14W*XO#!7K?5?*/n2lZY1&.,)3t.*]*oIFJM8(-F"4O.F!s!+@NpG%\o*[.+-Cr=BZ([O>G>;F26[H%?1uph.%uH,HFip
%E1@]&!=l<;rIf(urJoUhJ(7@n$2NWBn%\WVS,KJBq"q7rU?C2`h8!1%g\jj?fo4dP<$@I_TNlknL:PbPhXPu<RA(.uhp;C(Qi1Xp
%G$aP85p`JW]`H>j$Ltn2qEK(,0[p~>
%AI9_PrivateDataEnd
