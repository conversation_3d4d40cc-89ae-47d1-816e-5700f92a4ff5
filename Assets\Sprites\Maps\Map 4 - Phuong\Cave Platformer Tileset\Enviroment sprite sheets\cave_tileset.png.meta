fileFormatVersion: 2
guid: 894640e3f9d838a4499398609b715c7f
AssetOrigin:
  serializedVersion: 1
  productId: 61672
  packageName: 2D Platfrom Tile Set - Cave
  packageVersion: 1.2
  assetPath: Assets/Cave Platformer Tileset/Enviroment sprite sheets/cave_tileset.png
  uploadId: 154679
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 21300000
    second: tileset-cave_0
  - first:
      213: 21300002
    second: tileset-cave_1
  - first:
      213: 21300004
    second: tileset-cave_2
  - first:
      213: 21300006
    second: tileset-cave_3
  - first:
      213: 21300008
    second: tileset-cave_4
  - first:
      213: 21300010
    second: tileset-cave_5
  - first:
      213: 21300012
    second: tileset-cave_6
  - first:
      213: 21300014
    second: tileset-cave_7
  - first:
      213: 21300016
    second: tileset-cave_8
  - first:
      213: 21300018
    second: tileset-cave_9
  - first:
      213: 21300020
    second: tileset-cave_10
  - first:
      213: 21300022
    second: tileset-cave_11
  - first:
      213: 21300024
    second: tileset-cave_12
  - first:
      213: 21300026
    second: tileset-cave_13
  - first:
      213: 21300028
    second: tileset-cave_14
  - first:
      213: 21300030
    second: tileset-cave_15
  - first:
      213: 21300032
    second: tileset-cave_16
  - first:
      213: 21300034
    second: tileset-cave_17
  - first:
      213: 21300036
    second: tileset-cave_18
  - first:
      213: 21300038
    second: tileset-cave_19
  - first:
      213: 21300040
    second: tileset-cave_20
  - first:
      213: 21300042
    second: tileset-cave_21
  - first:
      213: 21300044
    second: tileset-cave_22
  - first:
      213: 21300046
    second: tileset-cave_23
  - first:
      213: 21300048
    second: tileset-cave_24
  - first:
      213: 21300050
    second: tileset-cave_25
  - first:
      213: 21300052
    second: tileset-cave_26
  - first:
      213: 21300054
    second: tileset-cave_27
  - first:
      213: 21300056
    second: tileset-cave_28
  - first:
      213: 21300058
    second: tileset-cave_29
  - first:
      213: 21300060
    second: tileset-cave_30
  - first:
      213: 21300062
    second: tileset-cave_31
  - first:
      213: 21300064
    second: tileset-cave_32
  - first:
      213: 21300066
    second: tileset-cave_33
  - first:
      213: 21300068
    second: tileset-cave_34
  - first:
      213: 21300070
    second: tileset-cave_35
  - first:
      213: 21300072
    second: tileset-cave_36
  - first:
      213: 21300074
    second: tileset-cave_37
  - first:
      213: 21300076
    second: tileset-cave_38
  - first:
      213: 21300078
    second: tileset-cave_39
  - first:
      213: 21300080
    second: tileset-cave_40
  - first:
      213: 21300082
    second: tileset-cave_41
  - first:
      213: 21300084
    second: tileset-cave_42
  - first:
      213: 21300086
    second: tileset-cave_43
  - first:
      213: 21300088
    second: tileset-cave_44
  - first:
      213: 21300090
    second: tileset-cave_45
  - first:
      213: 21300092
    second: tileset-cave_46
  - first:
      213: 21300094
    second: tileset-cave_47
  - first:
      213: 21300096
    second: tileset-cave_48
  - first:
      213: 21300098
    second: tileset-cave_49
  - first:
      213: 21300100
    second: tileset-cave_50
  - first:
      213: 21300102
    second: tileset-cave_51
  - first:
      213: 21300104
    second: tileset-cave_52
  - first:
      213: 21300106
    second: tileset-cave_53
  - first:
      213: 21300108
    second: tileset-cave_54
  - first:
      213: 21300110
    second: tileset-cave_55
  - first:
      213: 21300112
    second: tileset-cave_56
  - first:
      213: 21300114
    second: tileset-cave_57
  - first:
      213: 21300116
    second: tileset-cave_58
  - first:
      213: 21300118
    second: tileset-cave_59
  - first:
      213: 21300120
    second: tileset-cave_60
  - first:
      213: 21300122
    second: tileset-cave_61
  - first:
      213: 21300124
    second: tileset-cave_62
  - first:
      213: 21300126
    second: tileset-cave_63
  - first:
      213: 21300128
    second: tileset-cave_64
  - first:
      213: 21300130
    second: tileset-cave_65
  - first:
      213: 21300132
    second: tileset-cave_66
  - first:
      213: 21300134
    second: tileset-cave_67
  - first:
      213: 21300136
    second: tileset-cave_68
  - first:
      213: 21300138
    second: tileset-cave_69
  - first:
      213: 21300140
    second: tileset-cave_70
  - first:
      213: 21300142
    second: tileset-cave_71
  - first:
      213: 21300144
    second: tileset-cave_72
  - first:
      213: 21300146
    second: tileset-cave_73
  - first:
      213: 21300148
    second: tileset-cave_74
  - first:
      213: 21300150
    second: tileset-cave_75
  - first:
      213: 21300152
    second: tileset-cave_76
  - first:
      213: 21300154
    second: tileset-cave_77
  - first:
      213: 21300156
    second: tileset-cave_78
  - first:
      213: 21300158
    second: tileset-cave_79
  - first:
      213: 21300160
    second: tileset-cave_80
  - first:
      213: 21300162
    second: tileset-cave_81
  - first:
      213: 21300164
    second: tileset-cave_82
  - first:
      213: 21300166
    second: tileset-cave_83
  - first:
      213: 21300168
    second: tileset-cave_84
  - first:
      213: 21300170
    second: tileset-cave_85
  - first:
      213: 21300172
    second: tileset-cave_86
  - first:
      213: 21300174
    second: tileset-cave_87
  - first:
      213: 21300176
    second: tileset-cave_88
  - first:
      213: 21300178
    second: tileset-cave_89
  - first:
      213: 21300180
    second: tileset-cave_90
  - first:
      213: 21300182
    second: tileset-cave_91
  - first:
      213: 21300184
    second: tileset-cave_92
  - first:
      213: 21300186
    second: tileset-cave_93
  - first:
      213: 21300188
    second: tileset-cave_94
  - first:
      213: 21300190
    second: tileset-cave_95
  - first:
      213: 21300192
    second: tileset-cave_96
  - first:
      213: 21300194
    second: tileset-cave_97
  - first:
      213: 21300196
    second: tileset-cave_98
  - first:
      213: 21300198
    second: tileset-cave_99
  - first:
      213: 21300200
    second: tileset-cave_100
  - first:
      213: 21300202
    second: tileset-cave_101
  - first:
      213: 21300204
    second: tileset-cave_102
  - first:
      213: 21300206
    second: tileset-cave_103
  - first:
      213: 21300208
    second: tileset-cave_104
  - first:
      213: 21300210
    second: tileset-cave_105
  - first:
      213: 21300212
    second: tileset-cave_106
  - first:
      213: 21300214
    second: tileset-cave_107
  - first:
      213: 21300216
    second: tileset-cave_108
  - first:
      213: 21300218
    second: tileset-cave_109
  - first:
      213: 21300220
    second: tileset-cave_110
  - first:
      213: 21300222
    second: tileset-cave_111
  - first:
      213: 21300224
    second: tileset-cave_112
  - first:
      213: 21300226
    second: tileset-cave_113
  - first:
      213: 21300228
    second: tileset-cave_114
  - first:
      213: 21300230
    second: tileset-cave_115
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: -1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 16
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 0
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 1
  swizzle: 50462976
  cookieLightType: 1
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: tileset-cave_0
      rect:
        serializedVersion: 2
        x: 0
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02305410000000000800000000000000
      internalID: 21300000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_1
      rect:
        serializedVersion: 2
        x: 16
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 22305410000000000800000000000000
      internalID: 21300002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_2
      rect:
        serializedVersion: 2
        x: 32
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 42305410000000000800000000000000
      internalID: 21300004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_3
      rect:
        serializedVersion: 2
        x: 48
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62305410000000000800000000000000
      internalID: 21300006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_4
      rect:
        serializedVersion: 2
        x: 64
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82305410000000000800000000000000
      internalID: 21300008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_5
      rect:
        serializedVersion: 2
        x: 80
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2305410000000000800000000000000
      internalID: 21300010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_6
      rect:
        serializedVersion: 2
        x: 96
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2305410000000000800000000000000
      internalID: 21300012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_7
      rect:
        serializedVersion: 2
        x: 112
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e2305410000000000800000000000000
      internalID: 21300014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_8
      rect:
        serializedVersion: 2
        x: 0
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03305410000000000800000000000000
      internalID: 21300016
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_9
      rect:
        serializedVersion: 2
        x: 16
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23305410000000000800000000000000
      internalID: 21300018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_10
      rect:
        serializedVersion: 2
        x: 32
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43305410000000000800000000000000
      internalID: 21300020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_11
      rect:
        serializedVersion: 2
        x: 48
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63305410000000000800000000000000
      internalID: 21300022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_12
      rect:
        serializedVersion: 2
        x: 64
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 83305410000000000800000000000000
      internalID: 21300024
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_13
      rect:
        serializedVersion: 2
        x: 80
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3305410000000000800000000000000
      internalID: 21300026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_14
      rect:
        serializedVersion: 2
        x: 96
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3305410000000000800000000000000
      internalID: 21300028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_15
      rect:
        serializedVersion: 2
        x: 112
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3305410000000000800000000000000
      internalID: 21300030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_16
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04305410000000000800000000000000
      internalID: 21300032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_17
      rect:
        serializedVersion: 2
        x: 16
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24305410000000000800000000000000
      internalID: 21300034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_18
      rect:
        serializedVersion: 2
        x: 32
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44305410000000000800000000000000
      internalID: 21300036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_19
      rect:
        serializedVersion: 2
        x: 48
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64305410000000000800000000000000
      internalID: 21300038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_20
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84305410000000000800000000000000
      internalID: 21300040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_21
      rect:
        serializedVersion: 2
        x: 80
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a4305410000000000800000000000000
      internalID: 21300042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_22
      rect:
        serializedVersion: 2
        x: 96
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4305410000000000800000000000000
      internalID: 21300044
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_23
      rect:
        serializedVersion: 2
        x: 112
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4305410000000000800000000000000
      internalID: 21300046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_24
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05305410000000000800000000000000
      internalID: 21300048
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_25
      rect:
        serializedVersion: 2
        x: 16
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25305410000000000800000000000000
      internalID: 21300050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_26
      rect:
        serializedVersion: 2
        x: 32
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45305410000000000800000000000000
      internalID: 21300052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_27
      rect:
        serializedVersion: 2
        x: 48
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65305410000000000800000000000000
      internalID: 21300054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_28
      rect:
        serializedVersion: 2
        x: 64
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85305410000000000800000000000000
      internalID: 21300056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_29
      rect:
        serializedVersion: 2
        x: 80
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a5305410000000000800000000000000
      internalID: 21300058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_30
      rect:
        serializedVersion: 2
        x: 96
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5305410000000000800000000000000
      internalID: 21300060
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_31
      rect:
        serializedVersion: 2
        x: 112
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5305410000000000800000000000000
      internalID: 21300062
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_32
      rect:
        serializedVersion: 2
        x: 0
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06305410000000000800000000000000
      internalID: 21300064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_33
      rect:
        serializedVersion: 2
        x: 16
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26305410000000000800000000000000
      internalID: 21300066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_34
      rect:
        serializedVersion: 2
        x: 32
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46305410000000000800000000000000
      internalID: 21300068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_35
      rect:
        serializedVersion: 2
        x: 48
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66305410000000000800000000000000
      internalID: 21300070
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_36
      rect:
        serializedVersion: 2
        x: 64
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86305410000000000800000000000000
      internalID: 21300072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_37
      rect:
        serializedVersion: 2
        x: 80
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6305410000000000800000000000000
      internalID: 21300074
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_38
      rect:
        serializedVersion: 2
        x: 96
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6305410000000000800000000000000
      internalID: 21300076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_39
      rect:
        serializedVersion: 2
        x: 112
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6305410000000000800000000000000
      internalID: 21300078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_40
      rect:
        serializedVersion: 2
        x: 0
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07305410000000000800000000000000
      internalID: 21300080
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_41
      rect:
        serializedVersion: 2
        x: 16
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 27305410000000000800000000000000
      internalID: 21300082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_42
      rect:
        serializedVersion: 2
        x: 32
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47305410000000000800000000000000
      internalID: 21300084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_43
      rect:
        serializedVersion: 2
        x: 48
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67305410000000000800000000000000
      internalID: 21300086
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_44
      rect:
        serializedVersion: 2
        x: 64
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87305410000000000800000000000000
      internalID: 21300088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_45
      rect:
        serializedVersion: 2
        x: 80
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a7305410000000000800000000000000
      internalID: 21300090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_46
      rect:
        serializedVersion: 2
        x: 96
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7305410000000000800000000000000
      internalID: 21300092
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_47
      rect:
        serializedVersion: 2
        x: 112
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7305410000000000800000000000000
      internalID: 21300094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_48
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08305410000000000800000000000000
      internalID: 21300096
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_49
      rect:
        serializedVersion: 2
        x: 16
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28305410000000000800000000000000
      internalID: 21300098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_50
      rect:
        serializedVersion: 2
        x: 32
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48305410000000000800000000000000
      internalID: 21300100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_51
      rect:
        serializedVersion: 2
        x: 48
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68305410000000000800000000000000
      internalID: 21300102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_52
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88305410000000000800000000000000
      internalID: 21300104
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_53
      rect:
        serializedVersion: 2
        x: 80
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8305410000000000800000000000000
      internalID: 21300106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_54
      rect:
        serializedVersion: 2
        x: 96
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8305410000000000800000000000000
      internalID: 21300108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_55
      rect:
        serializedVersion: 2
        x: 112
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8305410000000000800000000000000
      internalID: 21300110
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_56
      rect:
        serializedVersion: 2
        x: 0
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 09305410000000000800000000000000
      internalID: 21300112
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_57
      rect:
        serializedVersion: 2
        x: 16
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29305410000000000800000000000000
      internalID: 21300114
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_58
      rect:
        serializedVersion: 2
        x: 32
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49305410000000000800000000000000
      internalID: 21300116
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_59
      rect:
        serializedVersion: 2
        x: 48
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69305410000000000800000000000000
      internalID: 21300118
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_60
      rect:
        serializedVersion: 2
        x: 64
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89305410000000000800000000000000
      internalID: 21300120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_61
      rect:
        serializedVersion: 2
        x: 80
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9305410000000000800000000000000
      internalID: 21300122
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_62
      rect:
        serializedVersion: 2
        x: 96
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9305410000000000800000000000000
      internalID: 21300124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_63
      rect:
        serializedVersion: 2
        x: 112
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e9305410000000000800000000000000
      internalID: 21300126
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_64
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a305410000000000800000000000000
      internalID: 21300128
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_65
      rect:
        serializedVersion: 2
        x: 16
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a305410000000000800000000000000
      internalID: 21300130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_66
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4a305410000000000800000000000000
      internalID: 21300132
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_67
      rect:
        serializedVersion: 2
        x: 48
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a305410000000000800000000000000
      internalID: 21300134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_68
      rect:
        serializedVersion: 2
        x: 64
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a305410000000000800000000000000
      internalID: 21300136
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_69
      rect:
        serializedVersion: 2
        x: 80
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa305410000000000800000000000000
      internalID: 21300138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_70
      rect:
        serializedVersion: 2
        x: 96
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca305410000000000800000000000000
      internalID: 21300140
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_71
      rect:
        serializedVersion: 2
        x: 112
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea305410000000000800000000000000
      internalID: 21300142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_72
      rect:
        serializedVersion: 2
        x: 0
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b305410000000000800000000000000
      internalID: 21300144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_73
      rect:
        serializedVersion: 2
        x: 16
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2b305410000000000800000000000000
      internalID: 21300146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_74
      rect:
        serializedVersion: 2
        x: 32
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b305410000000000800000000000000
      internalID: 21300148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_75
      rect:
        serializedVersion: 2
        x: 48
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b305410000000000800000000000000
      internalID: 21300150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_76
      rect:
        serializedVersion: 2
        x: 64
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8b305410000000000800000000000000
      internalID: 21300152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_77
      rect:
        serializedVersion: 2
        x: 80
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab305410000000000800000000000000
      internalID: 21300154
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_78
      rect:
        serializedVersion: 2
        x: 96
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb305410000000000800000000000000
      internalID: 21300156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_79
      rect:
        serializedVersion: 2
        x: 112
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb305410000000000800000000000000
      internalID: 21300158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_80
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c305410000000000800000000000000
      internalID: 21300160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_81
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c305410000000000800000000000000
      internalID: 21300162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_82
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c305410000000000800000000000000
      internalID: 21300164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_83
      rect:
        serializedVersion: 2
        x: 48
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c305410000000000800000000000000
      internalID: 21300166
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_85
      rect:
        serializedVersion: 2
        x: 80
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac305410000000000800000000000000
      internalID: 21300170
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_86
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc305410000000000800000000000000
      internalID: 21300172
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_87
      rect:
        serializedVersion: 2
        x: 112
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec305410000000000800000000000000
      internalID: 21300174
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_88
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d305410000000000800000000000000
      internalID: 21300176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_89
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d305410000000000800000000000000
      internalID: 21300178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_90
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d305410000000000800000000000000
      internalID: 21300180
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_91
      rect:
        serializedVersion: 2
        x: 48
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d305410000000000800000000000000
      internalID: 21300182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_92
      rect:
        serializedVersion: 2
        x: 64
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d305410000000000800000000000000
      internalID: 21300184
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_93
      rect:
        serializedVersion: 2
        x: 80
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad305410000000000800000000000000
      internalID: 21300186
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_94
      rect:
        serializedVersion: 2
        x: 96
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd305410000000000800000000000000
      internalID: 21300188
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_95
      rect:
        serializedVersion: 2
        x: 112
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed305410000000000800000000000000
      internalID: 21300190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_96
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0e305410000000000800000000000000
      internalID: 21300192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_97
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e305410000000000800000000000000
      internalID: 21300194
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_98
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e305410000000000800000000000000
      internalID: 21300196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_99
      rect:
        serializedVersion: 2
        x: 48
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e305410000000000800000000000000
      internalID: 21300198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_100
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e305410000000000800000000000000
      internalID: 21300200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_101
      rect:
        serializedVersion: 2
        x: 80
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ae305410000000000800000000000000
      internalID: 21300202
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_102
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce305410000000000800000000000000
      internalID: 21300204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_103
      rect:
        serializedVersion: 2
        x: 112
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee305410000000000800000000000000
      internalID: 21300206
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_104
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f305410000000000800000000000000
      internalID: 21300208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_105
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f305410000000000800000000000000
      internalID: 21300210
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_106
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f305410000000000800000000000000
      internalID: 21300212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_107
      rect:
        serializedVersion: 2
        x: 72
        y: 56
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f305410000000000800000000000000
      internalID: 21300214
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_108
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f305410000000000800000000000000
      internalID: 21300216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_109
      rect:
        serializedVersion: 2
        x: 80
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af305410000000000800000000000000
      internalID: 21300218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_110
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf305410000000000800000000000000
      internalID: 21300220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_111
      rect:
        serializedVersion: 2
        x: 112
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef305410000000000800000000000000
      internalID: 21300222
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_112
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00405410000000000800000000000000
      internalID: 21300224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_113
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20405410000000000800000000000000
      internalID: 21300226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_114
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40405410000000000800000000000000
      internalID: 21300228
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: tileset-cave_115
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60405410000000000800000000000000
      internalID: 21300230
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: cave_tileset_0
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 860a4d7d33ec7e84ea68165469805b6b
      internalID: 1150975435
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 7e3a813c4b885c2428e28f4f6b658d9a
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      cave_tileset_0: 1150975435
      tileset-cave_0: 21300000
      tileset-cave_1: 21300002
      tileset-cave_10: 21300020
      tileset-cave_100: 21300200
      tileset-cave_101: 21300202
      tileset-cave_102: 21300204
      tileset-cave_103: 21300206
      tileset-cave_104: 21300208
      tileset-cave_105: 21300210
      tileset-cave_106: 21300212
      tileset-cave_107: 21300214
      tileset-cave_108: 21300216
      tileset-cave_109: 21300218
      tileset-cave_11: 21300022
      tileset-cave_110: 21300220
      tileset-cave_111: 21300222
      tileset-cave_112: 21300224
      tileset-cave_113: 21300226
      tileset-cave_114: 21300228
      tileset-cave_115: 21300230
      tileset-cave_12: 21300024
      tileset-cave_13: 21300026
      tileset-cave_14: 21300028
      tileset-cave_15: 21300030
      tileset-cave_16: 21300032
      tileset-cave_17: 21300034
      tileset-cave_18: 21300036
      tileset-cave_19: 21300038
      tileset-cave_2: 21300004
      tileset-cave_20: 21300040
      tileset-cave_21: 21300042
      tileset-cave_22: 21300044
      tileset-cave_23: 21300046
      tileset-cave_24: 21300048
      tileset-cave_25: 21300050
      tileset-cave_26: 21300052
      tileset-cave_27: 21300054
      tileset-cave_28: 21300056
      tileset-cave_29: 21300058
      tileset-cave_3: 21300006
      tileset-cave_30: 21300060
      tileset-cave_31: 21300062
      tileset-cave_32: 21300064
      tileset-cave_33: 21300066
      tileset-cave_34: 21300068
      tileset-cave_35: 21300070
      tileset-cave_36: 21300072
      tileset-cave_37: 21300074
      tileset-cave_38: 21300076
      tileset-cave_39: 21300078
      tileset-cave_4: 21300008
      tileset-cave_40: 21300080
      tileset-cave_41: 21300082
      tileset-cave_42: 21300084
      tileset-cave_43: 21300086
      tileset-cave_44: 21300088
      tileset-cave_45: 21300090
      tileset-cave_46: 21300092
      tileset-cave_47: 21300094
      tileset-cave_48: 21300096
      tileset-cave_49: 21300098
      tileset-cave_5: 21300010
      tileset-cave_50: 21300100
      tileset-cave_51: 21300102
      tileset-cave_52: 21300104
      tileset-cave_53: 21300106
      tileset-cave_54: 21300108
      tileset-cave_55: 21300110
      tileset-cave_56: 21300112
      tileset-cave_57: 21300114
      tileset-cave_58: 21300116
      tileset-cave_59: 21300118
      tileset-cave_6: 21300012
      tileset-cave_60: 21300120
      tileset-cave_61: 21300122
      tileset-cave_62: 21300124
      tileset-cave_63: 21300126
      tileset-cave_64: 21300128
      tileset-cave_65: 21300130
      tileset-cave_66: 21300132
      tileset-cave_67: 21300134
      tileset-cave_68: 21300136
      tileset-cave_69: 21300138
      tileset-cave_7: 21300014
      tileset-cave_70: 21300140
      tileset-cave_71: 21300142
      tileset-cave_72: 21300144
      tileset-cave_73: 21300146
      tileset-cave_74: 21300148
      tileset-cave_75: 21300150
      tileset-cave_76: 21300152
      tileset-cave_77: 21300154
      tileset-cave_78: 21300156
      tileset-cave_79: 21300158
      tileset-cave_8: 21300016
      tileset-cave_80: 21300160
      tileset-cave_81: 21300162
      tileset-cave_82: 21300164
      tileset-cave_83: 21300166
      tileset-cave_84: 21300168
      tileset-cave_85: 21300170
      tileset-cave_86: 21300172
      tileset-cave_87: 21300174
      tileset-cave_88: 21300176
      tileset-cave_89: 21300178
      tileset-cave_9: 21300018
      tileset-cave_90: 21300180
      tileset-cave_91: 21300182
      tileset-cave_92: 21300184
      tileset-cave_93: 21300186
      tileset-cave_94: 21300188
      tileset-cave_95: 21300190
      tileset-cave_96: 21300192
      tileset-cave_97: 21300194
      tileset-cave_98: 21300196
      tileset-cave_99: 21300198
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
