fileFormatVersion: 2
guid: 2f9bfaff1523d53489f966b3ef966315
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -207994861796681891
    second: castle_env_0
  - first:
      213: -4445022054983906845
    second: castle_env_1
  - first:
      213: -2332685906206651193
    second: castle_env_2
  - first:
      213: 4852670541126992747
    second: castle_env_3
  - first:
      213: -1162949384569549892
    second: castle_env_4
  - first:
      213: 8883743248699752796
    second: castle_env_5
  - first:
      213: -7802708566780381589
    second: castle_env_6
  - first:
      213: 7505212315336009773
    second: castle_env_7
  - first:
      213: -1129608101658700004
    second: castle_env_8
  - first:
      213: -5018291283235691000
    second: castle_env_9
  - first:
      213: 5529526567596142380
    second: castle_env_10
  - first:
      213: -8137608722759060268
    second: castle_env_11
  - first:
      213: -2584462977090657571
    second: castle_env_12
  - first:
      213: -2491635706789813234
    second: castle_env_13
  - first:
      213: 1309213315113277235
    second: castle_env_14
  - first:
      213: 8784318799370337892
    second: castle_env_15
  - first:
      213: -293550919736355872
    second: castle_env_16
  - first:
      213: 2002100010313610172
    second: castle_env_17
  - first:
      213: 3406053118739073269
    second: castle_env_18
  - first:
      213: -2032595842404912772
    second: castle_env_19
  - first:
      213: 1799875909049825550
    second: castle_env_20
  - first:
      213: 7874498249956533742
    second: castle_env_21
  - first:
      213: -1841994162556502953
    second: castle_env_22
  - first:
      213: -4234831728882617475
    second: castle_env_23
  - first:
      213: 2433876568293344631
    second: castle_env_24
  - first:
      213: -9128615330651066050
    second: castle_env_25
  - first:
      213: 702104906063407186
    second: castle_env_26
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: castle_env_0
      rect:
        serializedVersion: 2
        x: 12
        y: 463
        width: 421
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d57908376cd0d1df0800000000000000
      internalID: -207994861796681891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_1
      rect:
        serializedVersion: 2
        x: 303
        y: 399
        width: 130
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e948e1edfb1052c0800000000000000
      internalID: -4445022054983906845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_2
      rect:
        serializedVersion: 2
        x: 524
        y: 495
        width: 184
        height: 130
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7cc0ef30782a0afd0800000000000000
      internalID: -2332685906206651193
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_3
      rect:
        serializedVersion: 2
        x: 719
        y: 463
        width: 226
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6380e78526285340800000000000000
      internalID: 4852670541126992747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_4
      rect:
        serializedVersion: 2
        x: 815
        y: 399
        width: 130
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbb09f042bf5cdfe0800000000000000
      internalID: -1162949384569549892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_5
      rect:
        serializedVersion: 2
        x: 527
        y: 463
        width: 82
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c510686aa65694b70800000000000000
      internalID: 8883743248699752796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_6
      rect:
        serializedVersion: 2
        x: 623
        y: 463
        width: 82
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b66782726e537b390800000000000000
      internalID: -7802708566780381589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_7
      rect:
        serializedVersion: 2
        x: 0
        y: 351
        width: 113
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d286d7326ded72860800000000000000
      internalID: 7505212315336009773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_8
      rect:
        serializedVersion: 2
        x: 127
        y: 351
        width: 34
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c17da3d9a63d250f0800000000000000
      internalID: -1129608101658700004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_9
      rect:
        serializedVersion: 2
        x: 175
        y: 351
        width: 114
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8062c4b7aa27b5ab0800000000000000
      internalID: -5018291283235691000
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_10
      rect:
        serializedVersion: 2
        x: 335
        y: 360
        width: 50
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2f27a5dd13dcbc40800000000000000
      internalID: 5529526567596142380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_11
      rect:
        serializedVersion: 2
        x: 511
        y: 351
        width: 114
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d473d64bf7611f80800000000000000
      internalID: -8137608722759060268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_12
      rect:
        serializedVersion: 2
        x: 639
        y: 351
        width: 34
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd2ec5b8d94222cd0800000000000000
      internalID: -2584462977090657571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_13
      rect:
        serializedVersion: 2
        x: 687
        y: 351
        width: 114
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e042d13a68eeb6dd0800000000000000
      internalID: -2491635706789813234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_14
      rect:
        serializedVersion: 2
        x: 12
        y: 175
        width: 184
        height: 130
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 337d986d2924b2210800000000000000
      internalID: 1309213315113277235
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_15
      rect:
        serializedVersion: 2
        x: 207
        y: 143
        width: 226
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46e314e186b28e970800000000000000
      internalID: 8784318799370337892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_16
      rect:
        serializedVersion: 2
        x: 303
        y: 79
        width: 130
        height: 162
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0e310a96ef81debf0800000000000000
      internalID: -293550919736355872
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_17
      rect:
        serializedVersion: 2
        x: 495
        y: 223
        width: 146
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb37c8f0a53e8cb10800000000000000
      internalID: 2002100010313610172
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_18
      rect:
        serializedVersion: 2
        x: 783
        y: 191
        width: 162
        height: 114
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5f8cd84f34bb44f20800000000000000
      internalID: 3406053118739073269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_19
      rect:
        serializedVersion: 2
        x: 15
        y: 143
        width: 82
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c71f489b9d4cac3e0800000000000000
      internalID: -2032595842404912772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_20
      rect:
        serializedVersion: 2
        x: 111
        y: 143
        width: 82
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e09b2b66a917af810800000000000000
      internalID: 1799875909049825550
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_21
      rect:
        serializedVersion: 2
        x: 495
        y: 143
        width: 146
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee5281dff66d74d60800000000000000
      internalID: 7874498249956533742
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_22
      rect:
        serializedVersion: 2
        x: 783
        y: 127
        width: 178
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 75cf976811cef66e0800000000000000
      internalID: -1841994162556502953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_23
      rect:
        serializedVersion: 2
        x: 0
        y: 31
        width: 113
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d73f56ccafada35c0800000000000000
      internalID: -4234831728882617475
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_24
      rect:
        serializedVersion: 2
        x: 127
        y: 31
        width: 34
        height: 95
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7758548f7ddd6c120800000000000000
      internalID: 2433876568293344631
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_25
      rect:
        serializedVersion: 2
        x: 175
        y: 31
        width: 114
        height: 103
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e31bb55e8b4a05180800000000000000
      internalID: -9128615330651066050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: castle_env_26
      rect:
        serializedVersion: 2
        x: 495
        y: 63
        width: 146
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 258e7f031b06eb900800000000000000
      internalID: 702104906063407186
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      castle_env_0: -207994861796681891
      castle_env_1: -4445022054983906845
      castle_env_10: 5529526567596142380
      castle_env_11: -8137608722759060268
      castle_env_12: -2584462977090657571
      castle_env_13: -2491635706789813234
      castle_env_14: 1309213315113277235
      castle_env_15: 8784318799370337892
      castle_env_16: -293550919736355872
      castle_env_17: 2002100010313610172
      castle_env_18: 3406053118739073269
      castle_env_19: -2032595842404912772
      castle_env_2: -2332685906206651193
      castle_env_20: 1799875909049825550
      castle_env_21: 7874498249956533742
      castle_env_22: -1841994162556502953
      castle_env_23: -4234831728882617475
      castle_env_24: 2433876568293344631
      castle_env_25: -9128615330651066050
      castle_env_26: 702104906063407186
      castle_env_3: 4852670541126992747
      castle_env_4: -1162949384569549892
      castle_env_5: 8883743248699752796
      castle_env_6: -7802708566780381589
      castle_env_7: 7505212315336009773
      castle_env_8: -1129608101658700004
      castle_env_9: -5018291283235691000
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
