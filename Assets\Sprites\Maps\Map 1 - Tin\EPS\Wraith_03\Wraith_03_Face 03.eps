%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Face 03.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 160 128
%%HiResBoundingBox: 0 0 160 128
%%CropBox: 0 0 160 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 7 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
160 128 li
160 0 li
cp
clp
77.334 73.334 mo
77.334 58.6065 66.2725 46.668 52.6279 46.668 cv
38.9834 46.668 27.9219 58.6065 27.9219 73.334 cv
27.9219 81.4414 31.2783 88.6982 36.5684 93.5889 cv
35.7353 95.2041 35.25 97.0771 35.25 99.083 cv
35.25 105.112 39.5596 110 44.875 110 cv
49.9414 110 54.0849 105.557 54.4629 99.919 cv
67.249 98.9043 77.334 87.3945 77.334 73.334 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
.0251163 .0509651 .105379 0 cmyk
f
28.5104 79.1418 mo
28.1237 77.2705 27.9199 75.326 27.9199 73.3301 cv
27.9199 65.9235 30.7201 59.2208 35.2415 54.3887 cv
30.7214 59.2212 27.9219 65.9244 27.9219 73.334 cv
27.9219 75.3292 28.125 77.2723 28.5104 79.1418 cv
.28455 .2121 .220493 .0271611 cmyk
f
30.5596 85.3203 mo
29.6525 83.382 28.9586 81.3111 28.5104 79.1418 cv
28.125 77.2723 27.9219 75.3292 27.9219 73.334 cv
27.9219 65.9244 30.7214 59.2212 35.2415 54.3887 cv
39.7059 49.6175 45.8485 46.6699 52.6299 46.6699 cv
66.2695 46.6699 77.3301 58.6103 77.3301 73.3301 cv
77.3301 75.4004 77.1104 77.4199 76.6904 79.3496 cv
72.6396 70.6397 64.2803 64.6699 54.6299 64.6699 cv
42.9004 64.6699 33.0801 73.4902 30.5596 85.3203 cv
.290745 .237675 .283604 .047303 cmyk
f
52.6279 51.668 mo
41.7617 51.668 32.9219 61.3877 32.9219 73.334 cv
32.9219 79.7363 35.4883 85.7813 39.9629 89.917 cv
41.6133 91.4434 42.042 93.8828 41.0127 95.8809 cv
40.5137 96.8477 40.25 97.9551 40.25 99.083 cv
40.25 102.29 42.3682 105 44.875 105 cv
47.25 105 49.2705 102.621 49.4736 99.585 cv
49.6396 97.1094 51.5947 95.1309 54.0674 94.9346 cv
64.3105 94.1221 72.334 84.6338 72.334 73.334 cv
72.334 61.3877 63.4941 51.668 52.6279 51.668 cv
cp
44.875 115 mo
36.8105 115 30.25 107.859 30.25 99.083 cv
30.25 97.6338 30.4346 96.1904 30.793 94.7988 cv
25.7568 88.9785 22.9219 81.3311 22.9219 73.334 cv
22.9219 55.873 36.248 41.668 52.6279 41.668 cv
69.0078 41.668 82.334 55.873 82.334 73.334 cv
82.334 88.4268 72.2109 101.328 58.6719 104.332 cv
56.6621 110.557 51.1855 115 44.875 115 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
130.001 74.9062 mo
130.001 62.624 123.453 52.668 115.376 52.668 cv
107.299 52.668 100.751 62.624 100.751 74.9062 cv
100.751 87.1885 107.299 97.1455 115.376 97.1455 cv
115.867 97.1455 116.353 97.1074 116.831 97.0351 cv
116.828 97.1445 li
116.828 102.626 119.33 107.067 122.417 107.067 cv
125.506 107.067 128.009 102.626 128.009 97.1445 cv
128.009 94.2598 127.311 91.6699 126.203 89.8574 cv
128.562 85.9082 130.001 80.6631 130.001 74.9062 cv
.0251163 .0509651 .105379 0 cmyk
f
100.781 76.3479 mo
100.761 75.8724 100.75 75.3931 100.75 74.9102 cv
100.75 69.4673 102.034 64.4836 104.168 60.6196 cv
102.035 64.4838 100.751 69.4668 100.751 74.9062 cv
100.751 75.3905 100.761 75.8713 100.781 76.3479 cv
104.168 60.6194 mo
104.168 60.619 li
104.168 60.6194 li
.28455 .2121 .220493 .0271611 cmyk
f
127.74 86.7695 mo
126.47 76.4599 120.52 68.6699 113.38 68.6699 cv
108.17 68.6699 103.6 72.8096 101.01 79.04 cv
100.898 78.159 100.821 77.2607 100.781 76.3479 cv
100.761 75.8713 100.751 75.3905 100.751 74.9062 cv
100.751 69.4668 102.035 64.4838 104.168 60.6196 cv
104.168 60.6194 li
104.168 60.619 li
106.852 55.7581 110.878 52.6699 115.38 52.6699 cv
123.45 52.6699 130 62.6201 130 74.9102 cv
130 79.2695 129.17 83.3399 127.74 86.7695 cv
.290745 .237675 .283604 .047303 cmyk
f
121.828 97.2061 mo
121.835 98.7441 122.105 99.9551 122.418 100.814 cv
122.735 99.9443 123.009 98.7109 123.009 97.1445 cv
123.009 95.3125 122.608 93.5635 121.937 92.4639 cv
120.968 90.8789 120.958 88.8877 121.911 87.293 cv
123.903 83.958 125.001 79.5586 125.001 74.9063 cv
125.001 64.7471 119.929 57.668 115.376 57.668 cv
110.823 57.668 105.751 64.7471 105.751 74.9063 cv
105.751 85.0654 110.823 92.1455 115.376 92.1455 cv
115.607 92.1455 115.846 92.127 116.084 92.0908 cv
117.526 91.877 118.988 92.2949 120.092 93.2451 cv
121.196 94.1943 121.831 95.5791 121.831 97.0352 cv
121.828 97.2061 li
cp
122.417 112.067 mo
117.553 112.067 113.651 107.901 112.32 101.831 cv
102.816 99.8506 95.751 88.7344 95.751 74.9063 cv
95.751 59.6328 104.371 47.668 115.376 47.668 cv
126.381 47.668 135.001 59.6328 135.001 74.9063 cv
135.001 80.3115 133.871 85.5664 131.786 89.9932 cv
132.583 92.1484 133.009 94.6025 133.009 97.1445 cv
133.009 105.652 128.455 112.067 122.417 112.067 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Face 03.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:HkhONH4jGn8caAH\NG?5K-1$;(Vb:qmJ@te^NoP/X66\DrUT?FB@"?kp@RM/S%%;Sq!.MMe(7YcHhp8-c-r`N
%5C`M"c!RW60:tH*2u(\WVtmCJ(GDE<^3TV@l+=!:m>)UoU+cQ/>PJt5J3O^\IeD/k5l10FGOM1P:cCT;K@A.qs1d_TJUHiOp:fGq
%g%CNe#Oc]7rQ8FEnFRBDos::/X5ErQ?N'>b\0#-"rtddpF"ON2&#FFk,Q86Hb?,/Mhj9()Xn^^MiQDD>h8\&NN59_>VpuEQ8Sm]o
%5Okm_rV]OIeaY"1kL!=fWFO196s#O7ln^aD!^,k.\#4;gc=>2#DpCk"3]hStH[NDJHb-^$A&9<snSUHp,!>6uTWTPegrJ9V]@S47
%dXjY(U;sFH[-B[DgB[(jajic5qKTm/K]-.KB5s=hLQSFLaXPb8A)!ZRMI0'Y^6rqn;Ah-MI<;(C+CcqRYhE!nG!klO#Q/&O0/a_"
%([RXI_s]b56YRiS'u&je5P1BWFRb.dpoES-I.?U9[nknpGk:d(l'mI#(1)*tIf@SjrWOjZ+(':A5D3BMSY,r@l$"f<nS5]T:Vi+\
%IWWPgs5FunHqW@KY$M;.\s)-a1a?[ZipjeRf_-]j3F'P(`9mKnQY1+Zn,om2;heWaq1@K\J<V-R-Y[M4IIk^aVf2V&g\lPZO8Qoe
%=5UZ<qX-@\p6]7Ai3@f^c2Z.G@0=u2:J/6R7K!AO*!ZGC-N7XOf-/5AK1A'(o8Dd]q=3#8I/`d(kAQ%h*8\F,oo&h\[_.BEq9Xfm
%X7'@YJ,>_e0A]!U\VHa=l=Y%LIf9tO*ac[1q3:Zk1_B;/H/]p;^A[*`GJMJ[]_i8;lGluNl0j*#h5e/ch`jVsje\)HI.R:/5J6eD
%FYpn.IhEC,eocQZm;`#HT5[!!"aFArZpC.'W>=g8)RSpi;ts#D@_[0bif>a!s7hFH:iVepY@%8,gD=3-#$8]Bj8W!4f5:B5ooJ_\
%hYl9BkHD;j5(%MLfu,Arp!8)HD;W#*qXinb:;XL8cQd7[f>'RW^A4Q%G5QuA#??0GNP#Z3^A@XEpqhs@=RkEA5Ngq3YC?<<48@pp
%^O>_02a;`hLGt89rUB=LRt#^4Fnango.0h%(&dhr=$QV\khSJB*;NA^rrei!rWMuuoE9XNcnbN).ocT,q<?MWT8p8;qWWoVIe94t
%J,O<3h`^kc-ah3!HrTFHIK&ThS%#Vl^V9Q`^\fV[KA$fV4.cI[>=craDM$d/JA8kY?_f0dnDfE5s.+_4[A-R-[J,d._-2,bZNB$\
%J*Po%q*&ab(l2&*5=HBu5M31)7c1Ml(J401n9<9B:KLfQc0ISYJ,/4GCnIbV8-aV>o',J<Dl62*q"F^mHl3-t`pHBK'u2VeFNM%<
%MZ*jfN#d3CmWT3,1GF>t@$Y-sH*\[NqnVj)#;u2Hh4HsTHN*d,Da/YlI-!$0W8QQ9T>puBK._.:&*_a2l-#DHrqQ3O2h-/Dm-=B>
%]5R^koabut]_i2/%+?cl9Z:_P-.uu.11"TjVA]Drh,jcEhJVa7FVfE%+Qi:oX+,Edo#8)A#0Y-3"\*Em`_J/7)e'!Kg0>Bj85.rV
%iW,Q?K>*Q>(]J/''<3qZ'0$b!B\D?:HM[?kMe=]8s8)V2h`ZJA?GgjGmH\,lk#M'l-S<9Qi/19i)aeVk`5#g-*VuF3q(M@ip8uZQ
%NT@%;5o+(jSl18L?K:U"Imtr(r=WF<`3f!thj+(jLCV(\DV(\r?/A[oJuW$:n4Q+8dc>nsDTXlN](u1+a>=\CbBolWoj=;Bi-+kS
%?f#iRiC*_)`G<gdY($?*Mg*KM8Ft$`8:'RE:b]padifq@9o\l#UZ,XA*osLi^7OHL-VkdJMH@jMZ[D`GDkDLW!lX[P6WYa.-5@;8
%"kHSFkm@N"/,Tf]Tg(qtRAnOB:q#>$qu[I@a'Zc?5R[:a+Fi8E8ARH<rV4c)5(<-R5CShYPX@W43C,Ym\Ued**#)aHJMAYm8h$#t
%LXP,,J]\SIM9<BaPaf^Sat8qETR]XA"-c>&eVqM:MP!A790(KHPZ'_D\UeeU*#7?r,GW"D$!*dbe2!:Ue`,c=N_iL<ghth_]Bucu
%*d`L*/KJrDhKnY2Id6*Fdr,cbi"+cXktcnoiPhHgLN_m:_r4/J"Qb$@3S\((3[jtETYt>$JqB'`Va86%WQ/TSR\:)XF/!\=hT4SZ
%DP6M!Dm>hfae(g9`;fGT<!&B+O8^5i2<\2,hAoW[Ucq]VjuuZrV]KRpV0C;dKCi&gW4c?@Iu7&8Pt[AOPBL\=qWT++Wdt'j:\Zq;
%\UedJG#USg5UB:D"]qKV.fE&E6%_O35G-7\B38=3Ijhd*oPZ-::5Xf.Ik(^%T)NTa_E,'D1Xpl,ru[ptDRVtD.fIK=]6'([=&4;c
%^;]=P\qKNqKK$%j%LGT.7gN]9`p?+RfO9GQ]Yi]/B?bkec[X(UK1j!>3\7uaGO&\VqW*S'rW%l?2Y[hM#+!VU=b`Db)a28M$!;2,
%YX)]kT&WPX8M9fAjHNS728R:dD0^`pEg'lfZcbObn;n<f-"JLL_2*.Ecgo#\QsV`2;.ni\!B:\j$Mb5&S]?fV%Ik:r.%,<NDo_lY
%ZSjug$e-#d6n$/g4:$J.2ji+tbj1*tep6_ZcbSNZbj?"NoaQ9PB=@kV5/INE\W&:;33t<-0XJ5;^ml1-9pj+_2WbEa]>LUh964-A
%[Z3=CE,HqiT`Ztc4:MA:b5p8>/TL@:)fY<LE:@)ih%;V)El6Ab7VD+s_IViaF;o/9XhT!;[l67Mj\N\E%QF?`_')b8QqO[9!ceV,
%$YSE'ap#r?s1g.(kMO)3a.7^1^#*5l(YJbtqZHD(f_`b3:U'^,Yl>XnXo]Rc!V[&=M&i*+2o&lt^hD[/>eEG-/"AI7c5\rpr5&&1
%30DMU?:T7P%d-fF'.^s)LcrPC4#kK&J4Z>_GopFZ[YFMH$n#G!Yh/BWXLBlmB,J*&N*_8o`(tr$P2H`Y0X;2^_$u@o$-)g,B6f2n
%c`gnECk?qOq%h9U1X$3M)XOO8F&ZSGA_:oD,/W2Xkn\I%).rCEqm*+(HaLRp`=Ic0Y]jBcKme?'j5Lmd0`MS_6%QED-RGjN!%Ys@
%>M/sg3)kDslE&BN`jWC9:Qc?m"=[Z7dfOsX!gENV&cSF!b2m/&%`EInd\ScLeOkr**Io'=/,G*'JeW.BEIkCQEb"2^"dTXW#T]u9
%.-4j59//^[MC8X<a^km:Fosob`f\9/Mmq]U^t<G5OWoR$3*A763T0hns.ON`,_"EQ]=kn/_t=/l_VqqU\FjigcZUAr[Dh#"oUQ56
%D;KW3@Gen-Rs*YBIJ6%>%T^@3]+<\>(\SiM"5.hgStfF]=$QKFpm..p#IsZd[>/8ETc_TnkW/VLhACtOFG]@e]Q70s3\%l?[-H5&
%7<6U_D<(CCG'D9!l)o"o6R>g3pU-+K/`X$(`ub)rX^cHpK0+'8&&h?[^V@K<fKZ\tfRH;aZr.)i/\]+h.6NH%GS9,"e3d.H]nfoZ
%'Lc":@0)OsfL5p)ddk.]6&\2d<Y-L4A;M!ggbM5[,V<h1$u\R6F/B3i'%mU,<rP/Y3JdN%$6Z9sVuV,V*MW'!9Zg`D0quO.7thL=
%^`,g!4HXYTlJY&qA/-h_mTWBl;tZ%_0OQf=JuO@CC)8C*Wb-gF[8Q2Md\Rp!.\=)CWZJ3f2l;b4_?U5[BGenV9(ABN^M[fDXIbmW
%#O3ZeTHIF*qDT,(/./%"JR)6jj.uO.nu"p@5UaG/]bNYTn2s[(0McF,:VNi`6TJ\rn2s[ea,`Upj:a,naU]9j'XT=)6@crYLPnTJ
%8,oim=R!odP,YYo&a9Q/FH"%%7Rn=;AiHNBT'/^,;Oq)'2E9E`+,)UEU9d#l[8^,OP8n]rB!S)Oa.48rHK<2A8'3Y1I!YGLiAZe3
%q#HqG^fgTB6P.i(J_&HL+P]$chffp#(^^N-"-:?bYVur;+"Ht96p;:XDAL+G;UCn%]NM``,2FNg6&d529d6*aD@;"i"goMH%Dg8A
%0fY=j$o@EhLg;3B%1ruMB:r'9H>2^Y<l,ph"`uRtY]4[B,/]+egD*ik:*hYXA<b/IJO*)k)LlY+W$R-VktqfB\iP[iNPrE5:Sp8V
%2+H=FgKh+o:LpBU51,oFHNeZ3XmHn[0.\+.NaVrcpPeL.C#?/)GU/=!@0c>ZR<K/LO(`'nG7a3d(*e)@c23<?'(.(*o":GkW8udQ
%qakuGm&@,&ol(57HtE\^W!'F;AolQQ]8eQ[LK0KK#KsNp>rhA+.bNbA[^+_=Lffp+Y>gc`W(N7M("*>Or13t"$8j;3i%c1g9g?O9
%fI<iL%4r*6%b,_]4V5f`%#:F!92(8OaC&S,<<(1`qLHh%ZKY@uLoeQ:9Z%T*Jo#jJd>2^0>?9RJ#:'XTB"_FAE+[m"QoQsGB(fkH
%s2:8bE55s-Lp[d%IdiGuF9"2(5@KsC+=,m.:!/Y)AF0J-s2WORMWjj9Sd<1^HT_FU4!KNfkjR^Zb+;.g]tqU+nmpZ]`8YR-_\DnV
%$a(TpMXSF"KI64r'G=MJ<';kP<(D^'1*.L&13kXd=IiQa*+C0!"[dC%]EL8;FWn+5<'8[*`9\>-bK\"H].V4KJa6C<5hA=ZX%V:u
%bLOEZ"B5pnlIEmqSCYj1X?nkXX2`>[Mp'WQlR%O_;Jl(H2EitkUS1/,@qno'[1oR7]UiM"JXTP0KBX>Y)Yqn,T;K?0AA5Dpb*&p3
%,Hih(M=We"jt06c-S+1(l_JA;aanLK:RS!.(<'WM*]*DUF]"3Ec)L/jQ(r#m&[/Z%Xg=M=B-sYefR91cA*.nXPs[]/H1EY3;D$H@
%,ul!o)F81I[=K'IDMl<,-CBC89/&$KK3dShIeV2ehG^(1W%mb*Zmk>.Z=)$)-:rb,]m7ECcZO")Pa:i"=YU_glB3E#JZr[:Q3phr
%Pa!A$Q\o*!IRQ+9MF&[_Su@(Q02*d(_<`GafhpB5CY[0f[)t"4Yrj9Df:j6gl5sTRm8enL)g[d5Li>j]D7^*peV(JL!aV#mOd0Nk
%8eR&*.bp0"?3EA*nVW0KM,o(%onaA2<2&6PBfJn_8f_UFYW>d@O(V@j=KMqKjW,dOXBqH330'2c`E#L;4(^N4,402"$J-j]__Vb[
%;&$4m!FYS9;SZ&rru<\X_ks)gh%un^V,!jh):g)kbaWRerbe9F("@*?Fj=o9$'0A(B>09?hI`Cic>j)e=RT7s[l&-]XF]agJ/u=,
%"nOdb+]V^t^tpOXCEVm\J9AaZ[]T[<"75JjM\Qdlh-SG-CA-UK.!/q&24EmZAs$\pZs'<V/j1c7l4FrCksL39(Am`?Tq6,N9.T\h
%:;-<15p*SEC@r+"9&)u&c(kK;qWg]>4d3a0+B>o=.(KlegtZ80``Z0qQIAShDTZB%W);+2\[4f);^kYe;_a%D.H4NCdG5>`SBLP6
%qDh?oqFO\13*T@%=63^&ppN3UQ=nnAh"#1_@jBghI%!j-q)%Bc.5[Y^oB?@8#YqE[SNVmPa3L3kc,P%X-Bg\LZN+qei&]8ECOS<W
%QD_t+),om\R8+UW3\tu*f_Mg5>o&.t=Q@sKrGs/-m`Ycu$[.]?g7iP)8"Z&*%fVj?HouZ83&?2XSO).nhXH9VOPZ.Z,_M#8[iO7'
%%Go9,FaeNd`!2l(Q8aZ<,*/T#hoPMr--D.dr]V8;50;,@2=gpZ4W/b#Ed27pIP^dXECUGdmo\n5/f9>EC8Tlsr"TMFnHQ:ZLH0-!
%29dN83*1mB'ZLL"eP#4>?GX\,$<aRh[$^=XKY3H#h9PE8>S^h2`XLJ<lCMXW$IkG$o=eB/Qci9IhrmXS_AjU*F*JUj6S&W.P+i=U
%CBE0>S"!!WV_/mPiXJ]CF?249A0t&4PrbSf49NVFXM!4(,n;BOn@;(']he'qK5(>*Z&3g<Mo;UKR?kem_u"$qI]#kL\hhe3Za3SP
%GCU6U32'),YTk^?1E?b1_%@kB^knZ(\lI[IDdpC@MLCBK:h"c\Mc1hsd]bD7l]PG:aLWMFUgZbk.tQ=Wb^'C"*>g<]O3E3_Z3+np
%84B$bD6b?+L*ZgFUsmI)+Ef':/NG>1Z82Zj&WC1RNQ7+@Vn?6jo.amQT:D8Nj*FU)ihWD`3?QlTF>W0RlKG1C6a+]KW%L>Xs/D=0
%/j=IJ/N*17ETYN+4MPjrX^-3X20LD#'T3Y"+j#Ha*L;%['se41btMs;iG"AtRpX=F=f`#LNgMTO3-dB*b'Tr+kp4m5Du,<B^Qj`9
%^O&5>em6b.HS;P1PrE,)'HA0:\`#cU)`\PrdZO$Ln*EH+MoEDZ]FuHql]r1Z2*SVs@B,eY[hhrG6])=Xl%$'`U\8Ji_pklFIt&hB
%Is<oJhN-sNdc9nd3qQ6mb;Nn@>n22Uc0NtW?TnGclAn9C_F+ZVe.q+V!BgnQ"(8>^6;>eA"e6+=q3+'<],_u>GU.Ejd)!Op[A+]]
%[rQH/Z,+]l3\hTo)$`5YmQ!+3]8]0f3\p'3\>=g%#U#N9X_2&.aL]c%DutLOi1qhJGaoAb@Y!#SE&'q6ekF+HMPmr%U,+K9ABcJW
%cQbehF$)3e@)B^2Z0Rh2Y/'GN`,:^f.\#hD?AYp"4!eu+Yu/J-0^C!m8IUe#,q>,$+IP)f4[:>^M3]=iQF\cZb_DJS;k6mJ@?N&I
%iNl44\lg$kIb6'I#/:^D76hTtTAgm:$W$.$<+g2nVTc1T0a;'GdL*EBUMBF,opA^"[\@J2e&`1]9RoJ6-<"!1,BQE)?l9%F@8^?C
%1Ybe5:1V^Momf6'S5bsIZ;B4S8Y8?s<*_L_8-C![Pl`-":egGlK9fU[_&d%R$GD'?)Xu$B_4_8K-7THP)JPGLE=2q2@8VQ/%g]=l
%N*p>o6A&>a',TAMd&;(4R6/;-66"6&l0E#cW[-9%Tdg=mR1303g>N!$105Jf1i"A<.B8&`,]'V%TZRB'n/2ZJ%.A0]#Wj7nOGAp[
%9&-4TdZ6H"A^N"8C^g(P/euk]$?32!W-YbpT1Bb`lPhZU0`)TLs.5kc^F6o@qGD4OfqA0Men;KV!qp%ZW-*j^#NMa4Q60.4'ub1Q
%W5;76-RHTu7cPTl)pC1-@i"$eK1GJ<l^sq[EYsRa@uZL3:M:\[a;QrD.Xm++1Sb;HBV]XLE<:-e4iqrY1i^IGU0Wk;KAlpHp7?,-
%\BNWXN"uMa"WT)r&NN3Kiri95I'th@#02c)3o:0`-=cnMKc$o<>pT8r;LlS%f*+e?7f]+KK/piPr<T(f*mAn_(9C6N3b+mBV5Xm"
%CKAIQ<(q.u/[2>16/RCj=IK(+dr@D&5"tPa23NmUiWLL8LQ'E?k<&+ij6"^E.6%B]VparP,-f#/>mceJ&!_Yh%ee(3'?X+;@2$i2
%M)`'AQQQ9;CcZ':.#8(l.E$5\A2$iD_3"Vc^f;)CU(STjJAT4t9aAt>5W'V<0*U2d#14P@C,.KYM89AF7'I-/*FKP5#Ah0V$4IJZ
%^ad]SR(V\c)DE&@&4=,dF\QJN.>YD?N`tBB2\o*Q`/l9YC,io5RB=P8[DBLaAhR4Q@P'BWQ8eS,bk]dhf8H`DTRMcj%>=bFC1.:I
%=-jgOHirLX$/b)@d"eHXZDW2#pieq6e[A>A6JBta6<_<I]B^FWS>BaDJCTN"'o*+!J5+uuU%[n;s46NC^aj`"Ip,/QU&N=6:IGN.
%@)C2FTAe>Dri8so8DIsACUkir)(0eT#ml*YD'Q,e@a.>NK!jp_@_8q1RYO8Q8W(ql,%$:LAk[qM?\6UsEcY$eI:)N#qK3;t,k7SP
%pYHG13'IkQRnD<^HSp99B<U/Wo%[k0IlfM?1kQp%\[[Asb!5B1>KQW),lCF*EL-+!get('4r-I/J8-G,W)@$s_+rFRS>6>k2=RN8
%VMoM]do6bGW$e9DFu%6uQ<QFR#L#!X)9_!q9.4.Z8Rf!@E1,dJ8Lc`:jHS,nR"PQ!%V]P\I5O@nmk;"Xp!I=elO,Sa(D$FPj_q':
%Bsmi#:d@@m)hsi?oab+VY1ODicTi*a!fh9f@2a\f!U$t%:f=of=UMO[=<9h4lp_Tm"G15$P.8*E=uR(nL:U*D(puEc.Y_-$<R71i
%/4j\EQ6RSjQD54Y'T.YRBkeZXG$$a&QHm4PK?r=u$RaeMcRlTNasC7NAJ(Om`N7na++NcMJB?hmr6td*IW?J/4uN=6UmfZK&0E#7
%4JY*L`i0dKr;Pa6qK)<!o=ssJ_&?Ad^&8eT-%'U?@4sq..f(N9TlNf1g]B/g7<u$D#s@3*hLj8DQ1kXR2sAtE+4>*\&,8DL+7hL"
%/l:Rk^\sPjhmpc$,fs/caVa_][32LZGnj&4+GgD(2WBSVprIT2j;]DTKh$0Jn\YA&Gm2b/1Y?M/[@tSXYWX,`"1C@F[msSNm(ftr
%AV]=)fIrQaJ0H%-W3Len.=4M]R@gb^mF4+tp+oBeLK0pSI=#)LB:/<$X#\WYG-H-cjC?,nRW*,lMWD-ZNE-t#/,"4XTQXQDm-beJ
%Nhrr>;F6_XVh9@;+D#>u`=cc9na)UZ//CMJ3Re,D+UYbu;p[rF@m6rr*$-/sOL(se0hMs-6W?:!<%R<H`([6:ridf!;qbKL7fBmS
%>9<]n)OI(EE7tO2o?;Tjo%0m]$0.KRoD7@]VWpeJ)/[t[\C(Z';aZ@W`/Z:o[/cmJ+6_,a==/;qZZjX@T;Ol[dpaT5O*Dh%eikMY
%qh:TmIkE6iFLtR[+Ko`NPAN)-:KV4u-!STJrD8"'h_/`+)T-Aq,7cON?PlgaWtmIqO0Oa;4\M@t@#QC@s3Xut('aA]*sbhl:,cj+
%Yb3L05M'O\YUfpPS1'dNT6,D=Yd`5b[s%m^J!_;QOd^f?pe12M7Z(Nobnkt.7#jFV1=Jjm\WeC/F$7aH_017h,X$t8gp3$C"+EbM
%mh4j=2Vu&+<8@Cq^_minS%"e->_+,U116e?!hDdqG,9-Da3)&+EIZtcDX_S"HT1NPKe6N4UI,1BJdZt'.bUZiT9i,t&t@QE,=]Fk
%-;BF$RD[6*PdY[1/H;QY`'2+Ok.S-EFBn[eL!T*dYsjUJ-AG26">5f>._u*31rqTV8eaifU[TR)5bi%+:bI<Q)m]8!8$X!c:`G/D
%B__+3;iZ2nR&g-98G&[RYu9Af?f957N$ME))Vf.dk"O?>dCI1'AbZE!IDshrr@H1cdu5E=cX5;jB+R=/9(c\qGP3L=b0oX]0p#55
%iHm[3+',6``A/b^b:<?rRian::i#..,"$nB#`pS<'Iu06:63WJFcg90;iNBP@B[`qKilaW$!=!2U7e/&K*`tXBE3Sfr+@\[.3(a[
%^FuG"io51)]agt!XHHmc;2#[m)dI)/=q+6:HtMAmP>HBG@%i1[fu\K%m*@4P22)[G7AsJmC7,N=.5m/"BZr\[TI4aD_8=Vj(cN7_
%d97N982TbO3&_.0mstgD*qK'B"=Jb5LcRoI)'[B)n>*gnE3g9"5"C$n7B?gc\q\e?cHdik9Qb(l5P9Fhn*#22fL:!VmZLZ':seoC
%-U(k64W__+3PXEAK=jm.]M*krV\I4@#h^fT[Q1P6Gu6CG+8m9LU,hLAdqS3F798H/!D%46($!jLFL9P*;,E6<IL8`A.l],(3j\$m
%.sMQKr<Eu^)$$"TfIW0pVf9+CXULl'S0AZYRAZ!;;_I;<8nCekV1W2aQ5BY;^d>0N;TGc,2V>&^,)]^i'4-S*H97t+$I[(NOpNq2
%G"[3Y$($MpeAji\CWugV$[JB-__mnH'rPI.ZN]dC3=[o(_+8adD?I>Up*mZ]gKPP&IR=lP6pe,cog#c'FZ5l5=_t1_F=)u`Wc4Vf
%4dGE5:2Hi;A1Kg$A[DgH]b6blMbk0\..`\B)"i(baXC]o+K4p%LE_Xafu1(%q*m6Ai?U,eN/Kk:$C^HWPdYIt<>7C>XX$1MfI4uk
%g)#$&Z`%YBn5b*")tPi,+A-Vc[t<(<^,7m7E[=`Pl@Kg,fC=Np%Prm6p_?itc4`]./3$1aJF4`PW5:ERHc/pPeRUo9#)UW5jPtM8
%apChI2E1M^GJEV5k!cPg4RMYN7l-JB;W@[=G,/D(_nM7HK&.(1e2jN?=1iH/%OB.28)bPOA-&-`W(h7SYZ!+`j?WTAj$<*p7"S+;
%+Hkh&FsQ(AlBH,+Rr@>AresfD\1cBBO<cEL-6(A=&WG_tQjJc2C8)Um&mqC`5Shb_nI'!G!A[-K;P[0t$QSr:QSj_;)A)puRWd^P
%Y:n';O`r!$l53rY`bq#HGXG=66V\IYI<d1uP7)I[ZDf.tW=QX^SGSMdj[AucCLkikW4T;dG%@m!)=7RI^DS?/5/25kh^nK_NU97u
%ZPP]D\Jt5)ERqRFp>gNiC-:8LVs8nXk$NI*:/]f5WLH=/k*+';R&M6S29X;s)4F.N)Y-suAo(9"d[.\e5H>Zs=bH_!r,e75enFck
%.W&7UW_h_RQ?k7*]d-$4NV>t=pWPOFrnU:`?Y]Mb=5tJ_4^/?j*0YjI[Y+1gOK<XIGY:<3^@`V<.t69%cic(5(Z7l$p@98X)iC2X
%DaKh05R8\Hc6g]AO`G<3*%"41dqG=Ir?re&s2$lmZ_FoU5^E8uP--'']ojrJ9<g<)eWR2E.4'nLAN<k5QB=^U,aY!_;Y0'pf/10!
%:Tq$KRnU]n)PYEPe.mFO:'NM@FgA_mMO7t+jTIGY;ZKu.R=!)q1kSWbK9ONlVo\$G;C0'?__mmnnM'c2D^R@#8>rYqN?dPi7Q"ql
%j,rW:;p;Km3k!3uk;^K)$'&r$gL(k@^PlK8Mo=>m)do23`R6T`b<',.PZIZT5']l"j=EsCS63B?B3JXRHMRQ*p"_.YqTU>Lg"a9H
%\U'Ig-E]#[>i)fmG7E`n-8Q_U*Wg]D366u$/:#6u>HBg'bTXoA[GhWW4CO9aluE).S""i@]AMMTbAAn.Hi+!?ntl:8WCD)UlfZo_
%Otr#<hlO.CjPB_CF+tWW(,Ur?3DlZ5m=iB&rMK#LZgI)kX$#C+k=^>6"=-0V7+@/Yh<fB`Gt33Q>/![nh]uY+OchgZmrS'#]j)*.
%4t#+,HKFVX10mR\5%"#qgr6mPGhU!gh:jC=_fGd[?'rf^P/bcfEl$D;e-4Lb48X33CH$T`]47c;$9PQ^maq,7^>>n,G:K]=]p&:9
%]C`rMQ,L6E4*;XP1O"'(-gf)k7kURtWH]msO4aH8fm+8u/)F3T]T;8hC<lJn-pBeieEAiShMhTNn_$2%n"DD7>^(r^nkBUicW0"T
%fZc%@q==0^\CotM\ECQ?<1gM+gN=Hh2i`6-QO1t`3KF;*7e*1uWHhm&>'cbcGqlcZIa-rc0R7*s$RVM,Q4"1b>_(W_MK"=_0PP?P
%'&S5=:LTG6^1eLCYS#bF$?.K3O\9*iS=C3gN4URj[oKNK\A^mc'SJHProLg.E>%,mjkGdZ$Ts!L&FF9m-QaU&_"jQi9O/&q,8l[i
%A)PFig8:9&c/I>SU:ZO*[8!&dcaj`CdF)JUPUWTRS@%O(Yq"=[IigpH&cp8sUd/)i!e%Z7A9kN'14@9@e\ZmG2^TM\7K0m)@X,S2
%!rRgf[:Hm@B>%3_Ko[3!RkN!&RI8#-1oo/Mn=3lZAlOBNf;Z2r<iHJi`.^;EBsS-;.JFLB8W5_^B2mfl+bpBf>#sjJdm/E@]sN^Y
%1s<Lm5%ItGFILtfD3]nP9HhpRib^Q"U"%'knA^,]+SCmBm*)`7Fap7GgMQ`HCsZFbD&\>de6kJ1h3M<!N4C0mgK80#ig838-cTW7
%$2,he&$aXhlVA.)Y1?dI:QM(-DF_TUGtE?cC$dT%i:R^/PeP>P\>:.'P$IM!f=H"L4PkS<k]Z^,h=$B1LA/;)^tm*Qn!^@\;duUK
%h]RRY0lQ#n2'(gFZ4EGIF8I&#6hL\[DSt4A549NBO!!2M]^<U8[lo1^0mVMDhGPpS(%4`OG5:!U06@#%DZ<=MqrMGcO1JKnrUYp7
%d'_;[Tk&9d>@o>(nK0ha_cPnJ7W"F0HV#M`GpfrO19]A\Lp@p`YHf&Y@9N?rCoci(VC^K'FH.?qXFf4[Pg4uM8sb9m4GuLYLD75L
%15lXr[q9CaMk#YMW5>QB;0=Oep5q+FHcjZe677Y[.Ss(W,YrM<]Vj!--nt?!9+A>[NJJ9b(M&Gs#_@)'-=;M3]aA1n&H^kI%ZGUC
%kF8Y+*1k%#5!SoLGJ&Qm96p4BmbX8D.cZCL^]'dM(g#D%eb9WQbh;T17*FnA%X)EA?I^6$&9Xr'Xu564F7_4DNNC^0C;%>VA#Y0Q
%)(SWT(-rrXCeQWH1HK2p$/#TV?l*ajCoKkUR:,Y8+DP;9'GY_ur*Wf,HCAD=o8tN\6LXb/A0)nY%h_8f!i\#@.dF.mk"$N%H1qVn
%<t,6O$pC?+Ob";dpq"Um\d;)D5o1JN%eL>JKdl2/[7e*\IX>1B@C`U"GBq.D?h6.&1hc\*Sso2nI/W/SOl^[^jhe5_\UEEj5#4B-
%fjCk2hsU9.K&jbpCI-+Q(^0]f(@O?"2?'Ci5?k7Fom4&Uo?W>n<3^?,hfHTQ10;Zd_P]Q2]?E&dcbO?gT<7G&fbe9Tl?oGlK/8;/
%.d/d+psN+FW[?6*1eOd:3TgfXSe8:Pj487*RorY9ri:A^VP8:TSHO_G(d(7a/#lqgG:-9u2)BKHcuk5C++*?j(C"4&@rVWS:8k01
%Oh*qTbm<nu$Hb%uKQ;AE<r<%%#J]=As'*Z2*\L]'8U*J^n4m#Sk,N@,Z8b@&+N'A3JdpBM`uiR`TijQ9$]*pMdB"Ju@8_',!de=i
%MpkCeZi[o5#h'>)9ua0Sb-bs^a?=6hfMH(M)Z2E6[2O5]&iR@X9cO8(UO$13*ZFCAl"W-$>HOd>ECCh9%*(eRh9m*S&g4ko"1[p]
%g9qrA0e+/@+e-sRD3Lr=k5mT(PO?3&D1-ieqVnqX2FkLXE.L1&L`qa#)J+p>SWhaYWO/XH>,PO.C3bhbRNnlr32noiC3&eFT:["X
%*""A\4b)UIn8hKS9?L%3`6M4fD(9r`o,Vr5hT@h[>_%!<L&H^@kngSSqlZcVj)TIm@^]@fcaiQ2<9DF#(N\:sNH@JB1/)mQ?h/lM
%?,n_iM)(\_.KcctPeR[/Vh>b*2WGa&TceQEE)89j7aacdAAi(=$&97rlgnM1->8e]@%Bm<4G`$-Bu4b&`$Tr30$S"RWO(i\M=,LQ
%,7^JJcA#+$.in8lE]%^;j#`Hm>BE]*lcXt'fLgefj`k"?ED;"^m>Cf2`6\V;\B]6!j'/NN-q<8qg%K]L.4qSZWdc<2c/7$>T>2[e
%%*JH#UY`P;>#7!OR5O`AU0_.q]p!)mY]\^=%e%`PGqm@:KtooLAppVr9%^6NafH#<=38n_!/^C-nuD5kOjg"?9&5`?;u0aTHhO!l
%?aiU2[]*QXRf[=.e9J;J4/l)$'V0*8Hi'nj\g_"JaB<dHI/QSU\^"Hjb[$':>k"j5/"Pb">\;g?bZc=<jQ5^@4hGt*@cZXihL,tl
%4/-?;3H-L8<1dudd41n1d<)3RRQ@q)NfK#[L$RCE)iL6:W*+dQAB@-I^[B*IC2@`c>MDT%!D+UC]>mS:UP3kJlUIllS]E9]=8J"<
%#>?(G?&1A3'1IDG(,Y@t7uI\Gj-5F'1PXhbedh[aLu%u53=Gg,C\1Aq/m_#oMekDH9alWQ@R%`Wa7ukcd:3ElR:=s1E[IoS,CL$B
%@.klMcR?"@E9%u.^a-@d4E8%]&Tli>UNMgD2DXB=Z\('k`2'JOFYg>r]Wh$6_R])CdM(H:\NU2+H)9ssZYrab80Xp(OYFVcX1t-.
%(sbqLmUX#fT%RHHXDdG@5=n`lr>DqJ>CPU@'4"'.SJQIsUKL$PjY4fDD'dr^Z=Wk#/MhjTEW:m[<fmFBWu#/kpYV)n=hGh@>/BsA
%E2n[_>R20JdPNO#/gT:_MPQ\N/`oT^RgFTq;&0e6Du-KQ="&GoTp#hc>AL@ko[X[kXochYI$sp+.eu`:ga&"$aCWUKO?_L]nNedf
%061bh?Pq+5)QQAa4[E(idA,K2N&Z0udl8!///1Cs.6?BT."CiPMAX"?,t6'4j(8u_V698U=l"?I:gu';7U[n69[dr)FY!sk>Kq&#
%^.LIG0<7*WH2DWO18,d*0s=*2D(.^QTahk:H=L@rD@m&QYq'lV`GP06Ofr0tlb_tid4N95I5#A`)'?KN!CqnQBRb)nX>dKG10/Qd
%b?ds/-O*VME?t/1NLA<SEUho/qsM)O]B`?GKM^^=l#LpeV.?6$qnSL8@alWGeEdn3QQET]A_*rF-#7&6R^6RbO'#pAS&MV]Wp+IU
%?e.8:enL<iMt12AAU.CILCLgGZ=GQX@ni9V<CSNk`,Dtkh3nO/QHNT<dVT2i"u<&jb852-D="Itk6mmD$_HJgT&pEPjpRdC$_HJg
%T&pEPjpRdC$_HJgT&pEPjpRdC$_HLeID4WR^>N_>i+YBg`\2S*06ip=Gaa!K1oD?Vc+SoOW%h!_Z4'FO*i`h3X4\\4ZM6ImgY1)e
%V?iY/A"tFk`RuZf7P&DR/ZH-p?u?[$_lL$LmRE<t;'t!7reC(pd#pi_m3E@s`LW]"n2N]Lc19F^c"Z'H"j:bVK[?-*$Iq4)27NOD
%g]XYOId6m'7Bek"l&]14"=LHXq1b_aHehM)<EMAIL>!(^G0LG>Ess[A9^45(5V,1&?DF/5W7f85>JXOGY&2g?Ja`tXFPcYJJctR
%diu-lSDSe0^23%"=P]oaHY`"#clLk`Dt!H+Ae2`Tm`/EJ*lnD7Fs+nN5A+drXibW^ep3\ns$XqtpYUC=WU/CRAQ&'3jai5hZ9i)]
%>;6&IoBCb(nXe(JWi]s<[>gBQ\m";d9AAX6d`n(S[Gq^ski,rkLWa+kpdXd`EtY",=:OFtA'PJ*^$I#UB1?d1Z/&tMd;89Kn`,3k
%A.UgZ],Yj%b?l=#l^bf)s$D!afg_[TZmf5TA>\I8V%qpb-F`@>p)^31mhY&5C>&QQZ;[ep9^aF&K^$)ReL1?JgCHdI>se8h6K%7o
%S__HLhN2g5CDN8'.rI/qman!&C02:hoSknucRof\HSt2(*]kddq[eBqMRGKML.=/rm6/9I2[=8>&Dm9ne[W2%GiM5];29L3`)Tq_
%6C3MI9E(hP']W.<1]%!l@9"r6"NYAf?Y554)F&MX$k(!@^N"a3cXQj+;RV913U\R"]e(=5CA^uiQPr)<N?=!-IP7uDo[n"qcs-&>
%/CDEeI.7PPC`$[1hY365mcJ[o[./f.<lD8qnK0u!hqbNYn\36<^.A78PK4L2aXNKjT@/-_a]uga+"bb#jkC^gWP$IN^<QN2"g>P)
%r-*43"'nVahr1fR_;Th<h\@k]3T(2cn:>dCm,$Z.RG]H_lbL$Ek737r)uNGpGk740Iu30Q0&GQkOK^[lg*H:GIttr@r@L\.3Ean+
%`d>&0YO?CSs6U8r4'+W<mZi^`hqt%Nr^"WtF:jpi!Llj*oYV/"I1Msa7B#Z_e6t9ES@\-O93Ck<<hBui1M.YN'@aN]Y0Z>)Z-1E#
%"T*j]?Qe$q_#EmdM"W-tI>MiK,2D@.T@R-=-/s?2I%SgTCD?]"L8R*Sa61)a>?,1F?l/%W%=3J]URt37/SQh\Dp/i8^F\JUWS9a2
%3L<;89;OkG*2X[[mOuV9(T;<o.I`1*EAtpNq(2$-(I!JPHrOq"4ja]QgCP/lNe^%_>`*QP&XlkFk'$6:[);`hn=?K_W,[)]GZs$:
%(>tg=l#$e.=^Z9encnX_42aWL0$7U&q?f12(RUq.jMWs.@Mq[E`U4UNQFJAjT<22Be!1&_5g(04jWZ0a>pa/O2j_j%lRUeLjObu'
%>`.?XY<9VsBQnMmAb#l(bC-hMbD#E^r1#&dr7dj?4J]ufRr#6s%mAsT0qa;4^-8Fg:lsU?:O"UQ].U=sC;amtHLef;>^>ptoglsW
%CQ*($n99'o1b\h`-rEN,flasN-?dbg)Vo:`+0MBT5CK(9"b18eHs7+tQ]rtK\b?dV<>Q%Wk7uQ9*aSVb$c'!VJs*!>q8`oQooC3D
%<'SRUV#&1h'A[;hQ0:D?7XY3R)1`,YBh#=p:S/2ujZSI(Jb%@;hn8sMgD@'N]m0Lr-VrlXf^j#&5Pk17oXi,X421[]gHYZ-l+;/c
%hnFR;k_DBPrg,/2f9QB`@s<(2Aam=H4FZoCrnumqX5A>ds8$*le%YtO03@E'B35-.cgL(6pr;cOp%ZpuL6G:1H_pbB]`sjXS?D:9
%jR[NZ/mP4$QgPW3_7s;d_nWuIWH\FZ%UQ+8":b,L_nRE+?1#I>>"LTuJ:"s'Wm_J=orHt5"h*ZuIeHljet<9;mCBRJ?g!lpe>WYQ
%o,bJBEAplP8:MK+hQKcrRbR*:<^Oot3\SpXMS!`-UFfU+]k?\$\rNTjD/dk^FTI&6q^.Zn;U=0QqKJCr$.a02S#1PFmP$$C(V<)%
%HIbedoM*d8Q;RA1VYrBp]\=bcdA<F/dt>@Pn8aM4Uhg8u?am[,Ya=QOcXsrtIFE;QL?d3ql#n(Wg9.60FK&p%oUnS#J+juGofnN+
%2HdFOjmD@aI5@&Z\hmau]k!c'UDISmD>SJeh80)_hi[e]R6K.'oTn:GFR3WigNkL"a#Ntt\J^H8W5tM"oK=81!3Q+S2t]o;7DG^G
%0%!0PJ'_Q1kfWN4Bt@U5=T#V-6!neLe8pI1lB.t8FnI2#h`AZCfXIV8d?e,Woj"50hAY%494?Rs][!b#[S7;/ZADH)-2OM'!.6qq
%D:@It\!FKt&,NSg;9pQbVJ0FZ/aa$,S=6kR%/?JKRi(g'U2QJ)3N)_^C"gJP"_[UR`shgMM,DSFqRUCuXpZXcgSX:ZcZM>-'7IFU
%7m34&S#5L/!]IM^QYSG,CmuL<AJ:i19D$JZ"1(`KDWeT<LZI`DXp9A8YbRd_oL75YpRB3aINq0+#c:6,o0K*`p^ElEo)G"7nn2?b
%5GZRtoMNT`[T8\^mJQjo,,n3b)0^:q&niNf&h[G`U;@99KLB3@^(_?;3c7)+,2:9r1VNl?GfDVV](W,%]]_C?m,=KZ2[:!7H6jbk
%2`VR9Y2GfX;M,2\%<&bM[/`<<?84/<?3h-".<l\dr]5_t\[SD%>LJ9:j4cuC8C^X[%aJN*.SD$M7"2Pg3':CbRIHM+qp$dDm\muD
%<0h<](/!`W5dMUZc]u=J]CaM@P>DA&2-JfL7QRcX&OCVpWTU`!73r:_a%?ec59>l-Xfu]=#3^&?18qqY3&uA^FHPq[C]"rRSuat@
%$"&H;`E..Q-Fu\7;n1+S]C5dDp)fn.3V&QS<.K"kgYZ?_Ln<ip_Sj)D3iO^Xoa#@2V=Gj-d7$`_D^9aW;u93tH*B>n_ZhTHQQYTB
%bCa:5P\['2fIOpC*]34c'*S;t]e5r@>@JfL;O8l8h[8?ZepPon#6*Yn`U7ThHG'*A>AX'QER5Bf,cE(")&qcl]?#*_MlrT0)SJnW
%DS\gd]'^dVMpF\8!d&k25/3kFp)Ru^Ut:t<4X7##HJmWFTEe!")!DQK-F/(ch[q%p,K*EA'!Zd2Xhhu6@g*3F69]gq])s9qZt@cK
%J0*[AdM(uJ/5CI)r<u"B6&-\OTQB%1m]re:P>VGRd(?7%_8A[tg$VQ\F$1eEh<Eq&M&%9F2f_]&"X2$A\WjZ:@Gli!q>oK7,_7cd
%#YGs,VF#[.ijn0Q4kNi-p(c'Y"'GgK%YZ)7&/AY__7n*mKNV8`B'**%/m>Ke&5aR43KL.DblJTEo1\n$N(?4g=6"T<2553N0oi0*
%Tm_eW^ZS2J:"G^O3<t^*G7Pr3pq8EsTP#W0PaI$b&nAe?;'@iBE?V3R:Zb21=6MfjSkcP*F/<F#?8Tbj9M19#H0k]m-VPs"*LSe[
%Q-lu_M_R7GLI%uRLEcaTGcE!>Yi;?XM]OZ*s58hi:BpOG9Ajf3YW;CRY[2:H$9dUj\u.$"[jE9Hh/u+Zg$+G.SJX00$QeA&9UX00
%+D/r)*Je[gaI5Z(iPt\mEt?Nl>30:7WQW54#SuJ(Q;\2u$okom!D[6'7X0</[ro@F]!JD8$6SDj75iUH6fJd(i@$,G!IAA*#S%Vp
%+aQj*Jqt51#bh^o9@PGfAnW7=f1B1gR@"L)<af,$O<-cN6S)j*S5<"AqKs1i**(&=`!uli%./Dg5CLgd0+,*U+bEH=6s?T<<SKi`
%@W\'r0lk%]kg]O3gqo_g49\Vf*cuX(2*0uh!3pQ?199"nbXsF2(n=Qtae=VQWss6&chf2$2^Z_8HG]$DUJAV:$]-Qp9^I>^%hOq\
%Ff5bb_046?SJW7Qma5i"Gg_p0F:_=HBJG_oRTROOBQW^<fe-NlWUJ4>OUt1L6EDC'2<KrIPnpr@i<:S^`\@iRB!#Sun!HO6`2?em
%=IDnKn$o7nPqHS#CFHtBO+0KbJ*ACg&OQVKQ,e2!J_1Mrgq4Y+5]7.@.6SbV[98$T"^*S-.kH[bOrJFIil#r_0JOf%B(h1AMMZGZ
%'";q]WZ[7BZlh(D%4Y&I77[:(K<h=2KG9[7bG]t+'g-.j9*;]SEO_sq!!9YC@@[`[GnoIMi%P4ka!*H+ic$n)IMh^D_?UIIHJTsU
%VVttX9N=@:ICO3b;bf&=rh.0PHur`6(0S7(AYPBj!pPu<1/;.!M%GKOY;5I:)O]fPm44ifOGEJ\]KD]dK*Pl6I0)8c017jsX:MOa
%'.XJJpIqZnL_aF##A%<"gQ%]'n[mM<W[bo,]&gTAP_hE!b(H^NW2fLJ@*0dmL;u&a7G/")"BD`XYpN?HElkuILn8)gVG,J1KT*pi
%QmT&@+@O.D0WTfEdC"_/#83HD$eLJBN9',?r0[ghSL:9]25!?,_F58,jU(TCp+557LK1q>*J]iM>He+d7$Qken#F2`fam.QnPuqb
%O!8%D\M&T`-Gg&0%jDG%>14%=\igIZ>*U"60X=6L?%boHdEr@\=j/7o+d(I_F9`_S1mbik-*$b^7AO\39-9&WaqLI\3BOKX-N^/X
%[`-.DF\,etgHrfBmGI=+QjuU6CQKQ)L\WHc\r2L+70):?oa9VMe3fV3b"Ec:F*SDZe^AQG!$*kS0cb=rai!1g,%[PGd^03B*T::o
%2rOc#n@!li-Do+1.Q[g6,o[4*iVXDa)JrTWJNP(I(9YYr[JS,(:OB3S3auLta_^rKknmCF0:l$=HpS3$Ffn56#%"mC'^>4rc9&`@
%:N#5@\-D+rp$0q)X]?32@VnAF!'6TVnDP^0#"C=k),J\5R7Or[M0PgYdi(PQf,^Aur,?*E'MfCR^A32rB`[6j5^:cs2WODWL(<9$
%QK/rU!E"V)i&NE-.T1A\Y^;2Z<%H1<I]GR_a!>b=Yf%@[)`3'^Fm-Ii"/rYb?HmTC.^8&sGuC0G%N<![&dobd9['^TT4s'j&"`%H
%l=4;&[^/f@q#8-7gOR($MA?4WOhUcd\ZU+QZ&$4a?0[D\46E]5f9M/EiR4Dm5J`6$qW5`g%@mSi8;J^:Z'5h:1o:d=OI_!?hpLAH
%U]sY#U(BUeQ?OZansR3/jqV>d/^W=_rOo<AfO_Ta4X:$A;L1Wu'eOO9glOe#h*]A[N';'m>/Xr:hE@9Ce3?Q73gg2^#-u;]&bo*9
%@eO?:JSIR>;/<*[`P]T(&E7GUNJbi8D/r#I+Zh-XR_i;)>S)50irn"n4R>UH@S+&VEJqa%SScQpp^@NJW[N]l2jc-<l`cJiekj_G
%T?*JBC9Il5pkfejOYl$D,i:9fZqaUV:cF;!:T^\&b%DPFZKB+U=uNhE1D<?E[2Oq'fJcUT;ma*X&#31R[0D["L56SHfV8Y@.<80k
%Ouuae=YrTXS_EZnj'ZLaWnO+(W79VVE)_AqXr<%)>re"Sg9]QDJn#Q?`kmP]4%Yrn9l=cdd>)/(KeAO8otCeg[To6QFKZ:Im,Zs2
%.M=*Y\mC2'&X8=O:df=oVI280h4'N7]h#qSQq/""-Su,>Kc0<\+,9R8pq3,O%X,%3p!&KH<QqJ&MjlR^S>)7df%a8W2nLM$g%!0s
%3r$E*ENtY;`-Q8'AY4gTD__pUJZ+`T?4sr],(8CEOZdn3MHr?eBESq"GbT%lTfK\UA$DBuea<fpDe"oT4cka]_-fBC8Y;BQaqq5J
%*%N[43j?-7]#s"!bHM.m[R,e$=:@]1()A*>Wb#M;mnt`T*im,N$!jXJ\Om=]--nFOeC%pX)D>Yu\;L)[n6s[%\;%"^kA-ndJW!"+
%<[7CTnWHCl:blh9Ap!0(7*JPfjgP1ORWGeD#8u]l#auqL,I=?+lGFlrZdfD:.OGoP3&LPkR#TY9,",T92W)&$lghr'<S=e*/d8N!
%THgh*!fpTn2B/EKZdY30Ua?11`u;Pqcj][IKstCXCp_9i%h'cU=8EY0C=p8QP&e/EF2^OQk.$[D'[@'KT$"t'R6\(d5>fbgiCI_r
%CIh4fP,7dGOEb$S/kPU75N1__bV0M&#rCut&OA(U-3cQRjL"!WX#V#D?G?"!VgXhOVe(`PZDhFW+_sC-["!?q_XTu4+If6\bdI'e
%4#":gBGFhEZ@H6-LZuTddGG>`#N[Qm$\[C3R6?br_K6TYQ]n^AeZ*D?LflVDl$gscL)*sT)S0$)JEo^S[E'$'it+A5-gE0p14+1Q
%I!?f_8,oh?Ja+A[YW@DpTtRFfD%70"i]+l3=f/<lFg3<1_<ap%9dF]fgFM#\dHTa$Z%ocGc>Eu^pS>5Ri,bUu'B<Jp05R[TRoaHj
%XenS8=CW./+M;E'ins"GX.:NG-K:W-B$`5Bcq$4_KZF@$e7q$[UhH//+Wa(*d=)f:2;&"e9o4?KZ%7qU5`U\e=Viku&]boc1"c%"
%Xf1_+'GgD185*;gR6'#YFFI&&OthL)fN43AKD:?,qTN%DK>Rlu`qkd;;d!IT*b`K46[=[F0f^%I\a\`d7]TGMETfM`9FU9u5tPA&
%&bg./-Z_,r'\]UF)OUm/^dL6_`:E!>TVr&9a-+S$[?lbX(juP`E)\t2Q+P$@.'?K'"OW$p*EH-aoGkB<O-ZYTXs?gH+=="HDjh@*
%JLb#S"6-jWTs*b!<c)5)Nes]^%'+KLJm6<:>#Pl!dAY0%8<1'[/i'pk$K)5"Jr;%&#RAO`"X;4((^H7>J_5>i+r+Fb+S7:r";#(q
%S,u*dIf_>.`^;&<$(iZiNU3a@%S&Z2EqSQ>&PW=^,fokGO8l"GS$,TF%V1/iU7[9l[aQ_k_h_h>&P)OFeh#/^P`XP!MSkACNCb_+
%Xl\N9/C(SDbAVrt$Q38/@\?!ij`;*oe[d/:AXVXHDGoggZ>+*;Ec:L/)lF,!qN*<F,WqtSDh*Z+:d)J(f9$3ehoDtM&MJ5<ooeUS
%YgR9ElH*hhb?ObmVdXe$-rcu0RYZ=BL-<I#2(+K^<+'j["j#aN6=J587/k94d_o:BFBbfW"^p/1Y9V_HPIAQKAil$5#dO@_6ia\T
%1Zbd>2K,=]A_.SZa`\XaAAupUoFqdA+"#(4oN"p#c[Z""4L$;!dH5E#'0Gg4_8/X2&'?2<,gg1?%b+o[-Y5]&IO:(`:I-rY_.H*V
%.B,("S`-@`Fle$"\/U>3=iX$`/2.R!i>kH'KF`+<b'4u4?k6:Bf]S#u10'aMHHmKt:ADrPat^-t3]R8(V`Aa9W,WPPhH\$di?MR2
%K:0@!%l*%2YeDN6-n4Db$[ANFU%njZ#E.EjL7Kk64@aYk(pQlT_.7j+%lEL<]!-VLqT?4Hm[.4D'<VeS!PY+,(V=tSEI<!YiYe/i
%HH4O)JEg9ndjZejZ8OqKp.7-J;73MQ3RK?*_rH]n1nX>X1UDaQlJ!L7RsUW/PES0h"/5r/(6Xkf24_[o4;s"3GT3C*PD]if`8,4H
%/k0@+04[$W@Po>86oOCXUq/9NW8DqVCsn2dU1P6"OQI5^B;VG\c&=,J_YA@/j.k/!bTt^SQ5**/'(?jop@V8-C]bomXb`-/Nn#Io
%$c3t<Y\,]%AlG8Il6GjXUA()tb,q_P\[4O0HXNTSRRjbdhG1/+#:Cd&mG;DoJ3#gQCtKIqlN\g`R%mi@Ioc6Ejt(9b2PQOHLBC=_
%8hqE,a5DX&5slB-*/H&epuLR:!;5>?9g$TS]Zk6ic[]T$!%Q8IfH`Hfft*']J\6*90>'8UBZJf1;]#m*%$IP\;hYFX%HA`U3ZJM.
%7$:0_Y`$`.@K@pL6)@`sfElZ!@O>MECs_-C'*5Oc_pS83K'jD9)Jmbh6GaJmUY0R)T2Hb)J9A:u;&Tgk%(abZ4?&JR+$s&RV`8f]
%YEV1>CB!AcUOMmR+'nj5s#%1q^(fh=W0NE&$OmY'*?pPb)FptIj:O!)-gsn?^#4WW[1J$]OoQ&+KKi%LN!-+nJ<=]-S1=jhDSit/
%o+KOue53Fl,VCQGb1%"ce@iL8#[Q"?LYshf3H?2Q0-dDDfUUdN)8H;S(%obG0sN60KHrF?8kIgt5ni%2-]=iaft44O5URM:i2p)]
%7FQKp!nU=CY:ggSJ?K^nC:Hqk/tF5*0EQoCaZ3(mR49Vs<"(*@Ags;ro[a3\ji)@oeOK74qkW15\kWZ@-3l"enrek#U7rm2:OF#c
%g1I7'mB^>^P)sU6ht*-+Gs#A;P7Bqr'I+)L=ptb%LD[Lu4_u@"*hFojaQ-lUHQ2S%XqUCL3!L1/haA%M:45:5gWi.W>thQXF6k5#
%\d=/H`8uR>?FGg"EP/ibO:K2<A7Hs"Bk;Z*i^GBh5(rHLD`'p/_BH!<6rWJXA&&(kMk&_uRpm^!P#O+o8Bb@e>K"lX%7q!Ud?uWl
%(qA3f>TQ:6ITg:@hJ27j)FmgY2J+dJ(WBh$@K?5j<5KjsNK$<uC2>#f_+UV74tf*lkSTCIX3AJFE?lp;(A2MWdEoZ\Vek:9)sOTb
%c-Ze5e:A>OUY?dK\47"aMkNa##OFfR@R2dm82inlT<4+J(.DXO-*TT7+-+L]feY3JojAD2YWbVQ"J0SpZ-,R`ZpC`<gD%i6!sF3K
%dVEN-9.Yb"Lu-'+B?13`T<@CP"pn-.E=(\R7!A7]d1fh+gHo]^F?586_d*Kb6Se*b8HP=l7%IoqDJ_s'AsN%M]9Q1$aZA\",.;Kh
%UuKGGL/A_4;e*:<I3."TMH4E^jb5'SSZs!:=Rf9jSr>$iDE?i]_ZcP^BjufTke]/I*HJtU8QHjGOqTs5CU-@_/\!+=fU$%VZFH@B
%)-nUoYi3'2NAVXselpjfpl1P7_9VE3R>LFTU)7WDbt1ThFI\i-jI;JS82V>K4fS?6l!Bb5O1RhH6;/q>qXoe5[lsp$)Mpfn6'tUf
%7%Sst7>O;R*-TM[>Vo8^s'i8ZU7[9#jHDcmWiGZu39;=sr.$FOaM#C3PT>Cfr_]BVFpn^1]UKiAh)>c/WJ&0[r2][5@uNG^@Ar91
%<TGm'#UFqnZ`\%<)3K\'jF^b.=Db)g#o9_"H-B979>M\00P@5oM+*.(#Lih&]GO5VB4FgiD/POj+/BNH=U50-;J/qq!Pl;tUWgf-
%($#Xgj9u2N"EnXNIm]mG[#O0pbrVI6fFXKtp&oVrncW!/dZWOD;0X\:kUDrTMh"?l$qs>hCFG!A1Ps@2S?i]B*Z]u0fpRH8d(@@W
%)!([qo^N^%Z'ujEPp9]+S-!uI#c!DG<,;c"H'2-)E+6.&]dk=heYg>Ml[0Hi<96qXTgU[+Yc"QC<a69+5`>0HMBP'u2b]BM3m4pj
%L"8+b%7_&_Tc%l+K8M(1_;*V4O+fW"ph8#0Kn6U/F'C$c!nD0h%6r$$C@h-n"#fX^)7W,*A_*Y]?l`3iB8OKi>pt"QW7;kbQG_\G
%c%bE6j0(jh`,TTs7+!:JTNdeM-*e2h#N/g*crga7[Fu&tN!E`e[8<A.3:$tig$!?Y9lL#VUen!0H!7"d''pH3<L^guKRTh@h67MK
%nUqW-fpG(,267Iri>+l%(kY)<OPU^4a=^1bIpuJBSuoXneGYCc[#h\M_UHAWEUq\9el3K_'dBZ5*p;T1@]1(0h'T!,dZ*^[hRk%4
%Bg!<(plKHj=pKI@^0'a^9#N(87?O6'KZU;>+JKa.H\.;H1B4+,#hdh#d^e80oa96SO-kCe#i;nc]m%<r=fNcW8jUdTmZj`u6k[]d
%r<U'+Lc\CpaXAG_"n%L:@ItCpBMi;80m[9OHkk/P'@@]1KY]CYQjkrJ,rfq18Pd]Fi5@EDNjCgDMliK4?K[tQ-k4d\kDDEp-G!S0
%i@l"[(4),HhO$09!kq=,L2"s\jVAV[n!%.Q)T52?1j\O)LPaY?Tj'>tcAj(m"i8>S!uV?],^iK$c*55Np)Ka_$3sMb.:)F8bOp`.
%`0qDhSmia_Q-&q@'4=W`.Pd$QL]hVW'ouBTkm>^h(P<S6K!j6*@RYW]aqHq=Hl<QDC37;oo?Jn=5^%HK1j*`A]7XCo.$.hW#4;fN
%j-=MU!P$R1C/Ij4YGPk\09[:UqtJZ(%NXH_E5*)+/G4-K?X$C)'?s->]B3WdY*n($?<(TAlEH<h`6(KQWR+4AXbOZYKmpM.-VdYb
%oW5Z=6RPAadFgEa3Mr1[#MC62(uuU#-amj'?trR&S"?9PA\.qH/?SBg"7H_Q-BL$GLbNtOco2FI`Am1V<`8LZCjI;t9i!f[P4%t4
%W0;B^dsG!+R%:ZJA^5)@$p!k>$r^"Gq697CdapYHJj$`PojA&V'qG@IkUp[p(A=ItE40J]NRIAtm>QnM2a'_@IC480N6YbWZW\7q
%.OTCF-N_E6XP,C+T.GmX*t1tK2RtE(:A.,O`u=l(oZA>oRsH^70i2i9])mbRF;3-U#2(l6R#bJ/Q&(2''.$ODCTqlRM;)fKF&B;(
%74672B<2*<ftusAj=]-8%%N)?P?0VcjPgMIm>M'9kCcp@.dKjR=0'%UJbh=k7-L+_,2#!EPe0OepPOt8'$W.>d48W4L2;)_4JDGH
%=B_[iXC%+7ildbqSP=e_)'"6q)o7i^&B"c$(DTR.4WDDJ#"D%9)=oQG())X8r.34G4!Yp$Y[DP[1Lem:e9[&n$4M2MnUlh?5]!hU
%7RWDXk+jj_*R7>8Xc;ZI3>SJNd3W)X@:a"M^dM*1ib;NH9WjF3]h:he.XN*TUZ%?f#T/I01-]Mk&ODF#cC"Ss?Y6+MTW?_SR;*LZ
%nj?M+#!/7BV9eg6k!#-rTeQc+kS,d<F:68ZjclF&Th<B/&Hss00PK>V-5`-h[&eTk^VIY[96::K[Q`)[-?Lp##`FC6:d,9l!NKSD
%ED/9C!-=__KL0%S8>-L_5!%7N@_6XMH\t/07`>O(Oo9rIV>sl/9t<f>;1'+b/f8`]>[hL3AU6T]ige<@7fpG[afgph_#P(h)97I:
%-O[75#/o`+(+W#pQg#>7Nn.1)ldW:M,&41\Ehef@!^1jtLT^4EPb&_?eKS`DIA_ii%u#Y_NLi`V\sWgc4,[Yk%?aM1-6h"P-?ld!
%G*=Aj9MH5/ch92Lq`+;-Z?:7]:,(lD[FZjm)>Qj\*[K!$^rTtsW(m6^:1FiS!JV3ImT<8XLK!d;rQNJi>^oEmHTMh]&nn_]0(D@Y
%2-]KC1n-<oL#jbuSY]7M9;@D;02nm%aC4aTKos0ti9bJK1dY(G$uIuBV<=!.6@?%S>ZBCGkd=9pNR*OT%=q;9k2U,12*-<JfG5m7
%8gsQ:,NL_:Oo=HsnE4J^ME[#kOF`gZE'6l$gS-?lklb`Z^H,]f_fT'>7]-Bp[$&+SiM4CB+f$b/RYudWGQjs$`P=GA9ji<JYREGu
%qQp@/*o?kV;NFc!",Ss'qk'\Y_=$i]7mTC3HU>h)AYo-h.sQ@V9"rn(IRD49\WTCE"YsFCL;VS&)+X=>G%QB9'C4eGiJW;c>31A]
%E3u3Z,]Fk7iP%c:iPN-F\<7oN=:V-]o(ed@7!BUl6.dakb"6[m%aKTY;P#WJ:rHJu"\[=)0BH[c)i*o?4R?!9$cb8/"jSrFZ(asG
%,"O*aKg?p(Nl&G&;mg`PDXq3sSAtmZ/m44&'(f<faI0dPU_1`"_XqHU.,c#R$f^Nj"i.qh#iJ=[)1UZb&s(DXhhXHO^j$U$G"@Ed
%)*'q`-ZA%UL^#0kRJ9&Z%,2F"3dD2J&t>11pjr]07^#[8'U791#)C*sYQLjtX:9k2)b=)pke9c7S-D'j>r*Q)Y)U6%<0FFT\gq3$
%R>is+`bCEE(UK;>K1/YM1?jEc'?h1c][R,YBBuYq-FiU*7n+6>af8H=S,eG''8SWfA+<dq[b!IVkXGk:EtUZh*$oV*W#ri/Kn2I9
%etmr%9ruq8',k,BfLNi7XErbZB-:XA(pd,-@tk2\1"sk2,D.OQKd7hNjM3Rq=VPLB4=.WaiIMtUEd!jEU7n@GC$A'^+:8XcGG%'g
%/2BZc+;1E/\u"q5#]Du+!K.1CpAqt#Hj%qX9-snL9NR@aCE>'cB-U9ogf#&kLSeStU(e`$1'e$h:*%4ukDK`Yq>1E0JWE18r>E7V
%n1uF0?4]`fK(1EOoc]7_P>"Ze&1Z65n[q:>5^Mb%n6-HF#"ZXIR.00RV_DF\%7)gB9"UHY#U"PA3#c.ifO6No$d12/U:a_?l2\"-
%SZ"P)9TZEJF6,B)m$PSiE=:UJ>pN#LW,D0O6>!BDL'd1!)Q>'S<AZ@diG<\4+B>7tPd4T>=)AGF-j*jpM/"GN4\$`3``Si;ljQ8X
%Qs,^@2,-S>R4HC^PdEPq`Y`1rRV(c,c$E4>O+l,=Y->=R\;=PTCas\bQR!Go]4=k1Pf$"T9IW@RZT,aTVEQA.'1<Jpehl!k7N7Cn
%=/b.`b2+R"$YA$oj=Mo&2fdV=RW]'i'O581[!:U_4NGB:piTE_J[:GGB77SE!(W4*.3drIW?U.b(NW3rj=4#$qIkf9m!TLJ;)on4
%,\ZjeH=R_6Y*K`U0skWQ<$WBNaXpUFR=?$*cNSCH8"r2j785dD/WjNu+9=KHSuMNNqEU&Ga`3-<h@%gXa>1YtO+O[9F#YYGP#V?K
%:p*6a)7Dp@aqTIY-&60)UT3fG'$"TATKcUG"K!jC1d;iqT1K=c1aT,p6jf4NDH+l<l\cs41jfngBk\?Z2kX^f5$.Ne-/pATc>t(r
%[iCg!*P]nIAM/%CW=2qV6obqt=ujm3CNdmi2#+7f%LQB#<MCn27D30!S#ja/RrB?@5q*9G9VQ(f2O$"je;5fT_eL<W,2p7p?nWG;
%a9W`6;.l#CGjM`r(M>9<cj)up[^p5ID`;FP$K4I,AZG#V7R//m;DAHN8d6/tc]SFA;1@K]58IV'(L(Wds0EPr1Rk;ih,h3"1W.+L
%^D6?>UMDM!Z$f]jmZ:@N*<'4FUXet+`K!_sSAa%8-YIsTQn(7!GUF/PK-;%o&co<i(%^<n7K6bi^<W^3l3OuBHnpKgWHL#jPP9"j
%gGr*+B0lF"Q\#m?^nB67e`S$S,sHEBF8aU#\Tk^DDQ#S7Q*^PpaI;T+6RGXU['u^hZS])NDe>a!c[a;LJdkZ!;D3Y$VgLaa4-8gM
%(pdEU+HX8'qU0JC22%iIelJ3`W6So.10#<j\GS0B%YmdL/ht*ljdS/hUZQSg@YCEE)aO?SWREPR"eY<Z_JsC58LPM=ENL\o8^_?q
%mn8*42dWV*R&Clb$+%jij@-Lb?PTc?8Ka,$1J#$56XnG%!hXc21#UdK-nP"c$h0i_EWC9qLpIPc4'RdV;u"i2/k<lk'&EK:6#_(o
%>[lo7iO!1(fn?*/e0g/6aDpQ7$%Q'lS8:7FfM6I;nFno?l*>5kQZ:E^/V<,&R5*L'*H_&`-"YIpU;$+?;I7&8_9_,XmTJM9PQS.(
%\h2V*lJF^<Mj/X!l1\^#UMHZjk-`"m8MJNleSY22Z6XA`P$N-rC.\j,h1rXc6rI-2'ZXdICfC\Ai^CZIpXH!dkc./lLcp>80ZdW0
%I\PT_85'RqU*>IR&"7%%hX0pV&kY1'af<t@?:UQ%P-(416'Ps.GiVF&oG3LfmF3Bd`T1_Z\tBNeJ#`j7R,(&Ke7N#l,/rY-HW4RK
%Aa@@Qdu)9B3>3N!i?[;\d_#p3LfMIOi#*2UgXa"TNOpVe)s%^5k,s-?V3A,oNl#%Yl56=5nS5?;E]A!S_PJY<T.[K13YO(`ES24f
%[HrV->+)<AcGG>f="](;&lYA]oUnudl"u?R1W9tC/NZn.KLV0C)AD"J'(UtQA^d.F8(.[T>;\S-b9j6c8A7K!2SH)>1u7i7=>`8*
%b9i[T^V<<H2SH*iDG&<EgLe5;oOS\A:ZIB*WTh+m8(4%]Xdc0mI;4@:DJT!pQVDD`+&.qc=?.A`Q'1>_.,o.>6W;UL`&2>59tGq/
%<DlE45`SVn$=t9Er]s&m2L=GdB!eEh]kSZge`=V=<(i31JH@$a:K-HRllU=L4Ui)qjRos=`f:KB7jOe]im#M0<*5/h;c]1]R^4AF
%l"^g;6f*pK$U<pe6m)+"@9lmqjNj<m`H](ql8"pqflVA`2bdfE'Y?SKU.<e]2GIGN90JcA#CY(.Ul!qbNK1c@[S:#$90JcA#CY(.
%k?,u'e".dY2bdPS;a$VI#CY(.@teIH1iL0l6!]ti6m))<RND)cXIEmE>jd1bC7$[lP._s*W[(e8Mh!&nBk)o6'8WIHdtJQ-B@)-t
%BDu`a";OKoZ/fkV^>s7.SkJ]QO#2inMOq:O-ar8XRND+<5/r07i?1V>KRMDZIr`oJo9.aO[k/NQ)(5rL8q0^H1[&oeOh"d1!n+`#
%81-_!BFT/lEn&&?Z7*d,"D\F$etA3e0H6ABCuSuF>r&0_F/_b*-oo1_T2/C?3Q`bd8('p72UMnj\cse7<.U>'j^KE&bn8RY[/=5C
%9QN]9b\LW$9)#Eq%G49m7HmS-`Q6?G4]bk`VU)1-DP`91dX43A5[B&WhN`2f=P\#UHg"YZVWl5MCMPu>?7*6@9r:gc2aK&2o?:6V
%[SEBpHjA8;h0IN7bnE;6j:Zn5ACL@k_*uq#Kj>#@Wp8,kT8=['6Hi^H<btap)E\nDK7M]PgWg@Ye&g"sVN[EhH]Q9+X:3MF`lM3^
%ekGdIM?P<#?C-1#<lVl#jc-*R4"9er-V')a:.DnaGrhr.Z%rnQ@MW]M$]C#thpoG7+e@tjGJgREC0n\][Oodq!Spa7k-uKKRpZIH
%3XhLReu`bOC+P'-(%>HKkt+AJEb2j7d4$V=<\M=')M_IB-.:TNbk9C<1eSMdo.1?=c@"q'YmF,W7CP9Wc00ua#]k0P,D83h4t4(g
%F4sOQU?I%'rF>Bk:eE>9RN^"?3]g4"n=44h#s8/41N+::ERCsrU,$SD>tn)MLL_M`#:/!D)#ke!,9!R3L`;8H08bRZ6n=Su&.`[N
%E\`SA;QShD%CQJ0DcX!W5Kl-qE39tu21k:')*XA+FTY@c7s):3T3P%S$I4s[!aR"h$O4S:W'Z7%\O;e\#lMU5<se5H\.1/-Vo%-,
%)X#YVgsR6r^Y.Y-kV-!V(e2>`JY_4Un=8"88H;lQ3s*!$V4bP:*(S:g^2f+gU*`0YBmq4l/]DL;c#>Xc\eCU:At-a#S%-@i;*i&"
%';+TX?@HErW1(_iE*TgiWiM"J6QZ&M3uHVR:00Z*m9@hAL`-DWks$ue`=7_>%[`hlF4H^%(`<n#CK(2p)[>hKX'[TEZYke$5?XUh
%QhDi&UmEN@:/o$IJL1q_`ArP1L;@S^lU,ums1no*O\_4Bc4/$cD2CBiOsUD[(>iZ<bo\>_at:V)8U5;M>1K_0e;j$p:#j"GScX".
%CKZS`NPUX''&<q*5W<erWJ9^.HHm;_+hT1K<2'k#A'V:o'!s%P;e)P+k67>D)mi<h:ZZqTOJb[MC2tq-g!,_-;NE*I4CO)_VFLH1
%Z2:uKH%tGU^rYAI(WmrmK+Ve2YtQZ9c7P#Dae?o,.J^42)/ee5YShU9cL"/NV&HW'Ogs](N_KR@,L.o)S*5;j:QdEkmC4[h+6`h>
%7L/,pi?\`8e@V,RU#/RMTo2b2=.k;&d?WXIi*%t^%8'7G@P@B5JYB=m:1^V#WqYpJHc&gMpdhP'Hh'enEdLR5'L?Od:JOkf2W,7R
%fbIj00@MKOTe>>9g(Y?Sk6e5_7TYi]2b@%GGSF[WZUFdfEZGaSmb"`ZI9=/*h1^'[f>h9k&B[UnAT)b>qo8Fi+!q3*Cn)(B9QY84
%<1^IO`i0*,[aU)I0R.J^13ZW+RML>`Cmo97>5B-C4OM.":`Og;&`OqY(X,\&baC+l_TXa7f(mI6FKgL%oM&ECpoX3C=VB#6_&;8O
%;r3tfS))1X+=D0Ye%uY0!&5f)$O?q;+\"';"Xu6U*lcTk9D#X"*.O7@0!f8:m33VB_ftLDH:bDm8/\tR1">EWa3.^`>!j&^R7*7?
%+7A.%6=NfAb2i3Jp2+p+noCjuDU^40BJ_/c=UG@TGJ/HVN^N?Wnh!)9a>"(<8S]18GmlV`>%c4*.lh5')G<_'5hA>%]0A`s\4*.r
%2,9,dKIk^SBj:0K>.A9dmYiFF7ed)*H<Y"r_j3^ReFWcf=*7<m)7`$>!<AsmJbVbZC!pV`iaHm@ZC43"?u1X0""nC]0jhC8';:7j
%LDM*91u+6K!]8Us0RJLRaoR]gPdpi1P@Z1E^BHM)/#u8$;moX`eZsIAllq&$`5C+7b1*s4kbmon6/.4#eZ1AO+;.\D"SiZi,mn#Q
%e=*6f^ms$[Zf4/.7HI@aae'ZUCXVJ?oje;9a'\#1KS^$).O*)2pH$/gk6THe2K"DpTa]UWpTgHU#XD[j:7Pa-Ld=M$d+B$=U-ZMY
%o>:nV_!jGHCp?0W?oLpID0M#V0l4WWFutsqZ.TtDr_*Q6>eN_=*@CW[*[o<5bQ&YD"k'o4Y];(D+FHZI-DI:A=bbOZ4<VPuMC,O"
%&K]o96/eB)s.,!<Z)eTM8Mdp"EA+#tG+/Iu8KG^k0HGaH7jm-e@1-;fq@InGl,'U*a]V)"V485>g><RUVZu2@"K)A6Q)bOMTsk.u
%rBt$]MAi>_fJY_N[-+nH:*\!u78O12CVs\S)?6ZL_NRe?YEYV=;Zs8U'XVlf>iIc'=`H&.8&K+%(lDt#0:Cd4nIf,R172V'>6qj>
%jO-VP1j6PHR8^n#HT6?]HS<[qdR]k5T[00s>J*A%Sr?E6cE=K;j(d(;PH1\fI2TLXlFB=?S7j/-oWm`"<o?^$K>).'2IG<cPF::8
%X/rOFUNSqAQ39CH,DC@6ks>Wcmsk'Nascn7_b]#g2m9t[^A[KS5Q10;]6DTY^)$#(q&`tV?IF>uS(Fj9^3]eO<W(6SGBH0(eF3C2
%\!+<PJ*!X,mX+l`s+6mB'ki*-8ln6hqT")/='rk3IePAV<e@[F3a^9AU%?,s/n6k%/41Z>XiSV*^8\`TMI#5?i:Z<j0Aq=.OCM`T
%7lbHP:.1a?_7f1hm0GCB"Su3L)_`#*8*D?"J33LL:T6P^&V-Nj5gu"s+PcqT3H[R6$2r+:Z="<M'i^9N=R[F<@UnHr8S\U!Oc+!%
%pb<HPB]+YeenX.]C!/2r5^+1JKD.F%gR4NuE'TM?'8fc[,T_(rUe;r7#4f3Cc!J'->R;J&6jG0M%Q^EqV\X?7e7.hSOQ&mrS63u[
%H0O@&1!-p?QM7LnnucrVl2`4mBaaidl7sD5=ksqo-%gmHJ%:.L_\d!H"q%)':,4!P[Nd!Pbp1aC02I`ZF4)?77pZlj@K,M"K(\Y,
%;]HQ9))03/(57:'\;"0tQmisre!s/>/@]r4]@t2&6,2^K^S5%Z;+U3)>QhN[-5.0k:?`ejL@?%fSgQQ?/\G]:I0r@FhKka\+s"VK
%jJ^P]ZOI.s9t"[+c/fiX+pPDV1U3c'eIn&8A:6^5O_g4"Uq,cjNfgDS&>`2QT\\OI%9"'RQuG\A]MrW_<?<L/KS[SpGmVV;B3k"_
%h;SkUA5rcPB6(c,SE@h\Y79tPd?D7`a&??$dYDO4j$b60=G>Nd`:U278_?*OP&Z,2\7^LD/-LfXWe$g-]K&;U-<^]>0.W>W9d5n'
%407BmSasp:/7B`7!jS2"j40"FSfs,_Fh)'Y<5jKh.g#1l$<;ne<-aW^Wp%MsV3J!CB;de8NY5[e/bXH/X9S9]d??/SCt/ZA-O[2D
%pMA:VLSnklEAO<<4n$K_J7hd<#_Q2NDTmcqc7b#B[&4Ip:t.g!jar;#`WA2Yh7X1Z84Ynm>ES(VhL<7.lNCiH,oVJ2&^bX$&3V8f
%%-X:2+\X-u1-_;(G)ZtW,,Qp5HMLt`X8-0W)+3V5b+6E<9_'>=nR`2ENNsAAK7&Vh6k2kS1t,Y^8sboPTQe[I"a%uT)@)\gVaM$9
%aI*lA+5ouaHJM>Z9ja2[6/95Le!KUmGM7Na:\[94cJf4DU<?.[dL"7OB:cI7Jmg>RP@Oq2[8O/YfBfpR@r4fC6[D$8:Q_G8h<()=
%[M#L/]2La:k+h+]cch[$O$p'5p8d#<8#)?5]$p@H!npSY(NT2W``s.R_5:TeEnuR)'<G]sOK4N-PQL)ecsrL-8doitk\&nRfa"Y4
%ULc^A5G-O??+'EIp=bn@-Lg/f?Q7+@Vq3mK&S3+r*^(jei"dKUXG`Mg/lh/SKo)1t54!P<Cq;=_,k+&c[f\91R?YcYZp^g-r9feE
%+Tk,F'K6Uik`ADmUG.hS(dp3>.FVLeL\YmY)J+pSR`u.EX.m!_dDEH%k_*:4DER6RMQkM%"]\GN'1%Ui@cDn^+AVJ$>XMra$mc0d
%=+*GK6-p*?RM-kT6(D8ad$@4c3'bKhoC4-+)h]'=L<)<)T6*CJ59LJakdHd9'6U3oR,el6Q5!/.HY%;@;_pR?<CH<P4NkBBM-"0J
%%C`J81cO]Wr!M9LmqjlTVC,q_[[5>B*fAOrQ+3o?Am2#cZ^"c9YDh>O13(I:9Z5(o99(K*mPCbfO+GM&D%o5j2hjp[GCh$opNe`A
%RN)XXR%k!+QVXE^ffe((`t?t9e,ro2g$,8e8T[CJ\8$#fU;@e+M%>\ReTd2tRpnM?LLhZMQ8i_(@XtTDo7?73_M[Eg';B^(H^OK!
%ne>L,8]d<$On?jTl']"N.BnkZ72qdJV+XU?BU9ZZa%5jGPqU?l=qa1jDj[G+.02UEkrp-JVBjYi`MlnA2=Q(?KqmVf^!MqbUY,NE
%"!L>%@?:#SbaY"WL"n,>_MGa+NRn;ZB65Bm!A2;b`7LgaVA*\^U>oK3r1K$Y6o,#@XN3PY7+CU$<4bC>AY\@\M;!OE3UmUt6<Mm<
%[K;A0<Dk:Y;1$G$'C11L8]_[d:INO1,r^iZ'd?<NGrB6lgD5o<78LPER_>fBC(^e9*3.4D]75X(6nCXn^+Z8D1%>[5V3qn2\XIo(
%1jNAYToH8I(9`,Ya.5sdm/ubm#$itrfOi_mk-QR0;8s?K;/rr$U"F!`Up>..=Sbb<Oc2ck3c2TKCS-t$'=5a\!du49s4_E#T]bOP
%V58(uKq9Jk[ZjPsBpht;hINo)n>`jij+7\Hl:'?:Q=W_*N_GCkjnm2U\.JHm``gE;7L#XZ5Z`(`1#$<5?r&+IGR.)2cVk9M1aTCf
%+>Km27M\4a"PY#s^H3CcbHc)Kk*"d3,%@WK(o+BLbdCE6E5dpu<&<Krkd]Q/+c&\Q&10;DJTuZ:Skg<+JHEfI:9[89(0P$4cCjJG
%]@>2E0;*_c:Ig06dJ#):&h,X'LS-"DUCIl?/CoHhZ1-NNOkMPY:NsYn`H4HtJOR[3:mS@QMAPe_f=`G&A)/I$JVHhF-%/TGV""J>
%LGcR'KXDSM$M(0]D;A'\.6a?/$_mn-?SJ&44\Y2e)^Pq_,Opb@ma]:][6.8[.RW9.M7Om'0bl50XY*9X,54UQ,0"Du2]YW`lnYhX
%'nCqlQnE/+2gs?QWbY=hY>$*n.$48e8:F:WVXhmnEeG\=apG?<__ICVc'(>U0s@u!V<ci'6u2I!K<V+De$=4cNLWtM/7e,:ea>MX
%f9;/H,R!f"-<X1sHeGI]MT!A07![q\(0H*'6&6f8R]M;o-^`]#ad"3C;5tCGW\@fM(;D2%'+)aT+'Ws(MD_!37de"//67Y%S;]Ek
%Nq]SjX$qc:R%9L%9Ll`,AS33llX4O?=eUb$7(6g^ZR"pJ08dlDX0,U/@o*?#9G^]U*R+SA,&<5hJE-6Ai-8")/&\Bn'WuWFS[EAn
%8n$GN?7,>,QukESB<k"SJhhmsLjMZD@6/R)POfqYFYb.G$k13sp#mrc8!$(_1a*M*Q\gnC=P.iC27%99(OO7m*_s\NpZt1/e^9^h
%o^GiG>*A3aq!\uu<oT=j%aQugY03-ObS>;g1caYMZ`24s>-]$NJIXIk"u!oVIF`/Fl;TUbeq[[V,,CD6M]I5GC*^NdJr-I%DkVbb
%Pgg6Ab$mV'VAXJ$ajAAPi$VhF71b=h,:8&?qP%[CM)B`+qDt6iWK"6ql^:eJ=u87?%NdiCcp:=C&F^%m<-#5;XGTj@p(hZU<5*C(
%7h!G`egcARd2Wni<3\u'+J&e8X%n\u/60(rPPEmS3CFTQae$i)L7k:,3'Si,dcnV<]UP9Gqse&FBIfN+P4HRG4W'ri46>R4(R5i;
%ehH`BXJ)\kHE'944NeY/?!mjlY0.uh;5mFB4,7FX=%\5OUA@mV`cCTq:(^j>(a[=I'(&A<r30Jh12[P6@sX.ZJ9+*g5KouXe;0l=
%nS2NNiCg#kk3\C<.7k$h[RJdrS==*3fdi-339f'_Rk'Q"8>JCqm=H?X.MCb\VDcGdU8NiC.M!3!n-.#LWl%U562Y+d@8p&sGqNB6
%i[@]W"FhlJ9*HK98Pgc'*7GLB-,c[U@-X.9<`(i#gN\K%p'SO:OLlfOAB"u#o%hehlP4@k<[88J]BVN([U*SA>XgGL?!s,XN!0M,
%EjI8USgkM5CXZ];'GM/$Lc<Usd1#ZIA"!$?0>U6[`0Q_L?6?HiPqoB@ZC9G;WKKCt44PN1?^3KR0VYl6iM48KT/u1-"A?]%bOJYG
%`s:1g1Qa'.=QR_iUggl\LGmnk?ZB[=p1IA?Zl/VHGVWS9P7j`T22O+W?9=Cs=[gf^l351qV!<(_`&K5c22F0.pTJRH2;'rgok_.@
%.S^m0+QbJ&ZA6-GF3mM@FF`VYFVt=E0/.TNSfAT#d,IX:S]S8t3J=`=4ij(TR_QKWhSR-H0YP`dbni$s'WOi><YIT!r'Y56!1BEq
%IS>0baJU@jUB=YTeS3XRg-sU-GMN7_.#oX+Ul3d0/4aS\a-Y;6?/C!u&iFYN`SMeTig%s2<1YE4mS<!LP$KagN+`Quk^mVbDniCL
%A2#M*s#)bY0H_VJQC-o3Sa!p)fY```*VIC,k):.?TJe@Q(XiiH,/#./%r;*bV@P":40L96ZnN>,-HemF2W\r+&()iB4OY=o3g>Cc
%I<itK[PaO]^)&q,;WK5o*YX7YZ>7Ad1le/OC_S([$.Y))`Q:NYe7PmrCUBg17U\E$n]r_\M^,<^W%b*TmYQ&BgI,R:!feNZl2RBh
%2s[U<37<KK%JW0W`dn$(<Ls$8]!BM&UBgDmbMB;:?m&8sb$piuVM\qfrTh4WjbZZKWjcf0cQRGN1EJS,:VOA/`qffOeY?%i0$6Og
%KL"U%Ys8m.j3pD*Tkdm6XQ5C-kd!5jq))`HGqJi^]Y_un5^\7R`>U[';YZVF]JG(\G7[ae,nTh!kec56So#:<LJNr4\OohVYGRGr
%Z@*NYOT95H6rCjY(eT7!+!2Jlad<3@)5@*D^V,t'\#tlL84)5)rf/LkLdeIW1ge#k@)I`jSl%:09N@J7r@uNS(*j&OK4GtG0gqA[
%PisSK&Rq+\]&daV<h]kYFJ4;CJbON"25(Z$\Z-Ip:WFX/a\<E)P9ue`doAe.WppENC&aE\WZK`<4hk/Y64Nj?3),?DnBn1iQ<u<l
%H;%[BqLkfMdk"T>[LipA09,),-^sglDYJHi#W!f`o?B-dN9fS3JrY'YV(9sj#bnV,,3H*@?Wdn*fK^+Jdd\Ph1cA/i/4:`+$u\Pt
%hLY)OjH*]?Uk)gG.haLlNgLZr(dlCX)l$r7$iuN'&38Y<Tb5pL;,P!gPh/diQU]P[.M?)8`#88N`<f#PTi!;%NCU2!K9NbZiJ'tS
%\1kMko(:`W_V@WSUo=6E<SL0n@1\DSA<CE#ChgG6g6.Mo6+s-3r0?q_)R`g,piEFP<M5Dtr@tCk]@4;"&'>V-r/-V128)N`'bN<.
%O%gstcf&lNN&2kY$/NH"Tn]1p-`C6BPp'P2iOl<dUM&4hOuO;GW&e87]LjM#;]m8E7UDdDjPWp;`[X-eo9$^R%sQ%_C:P*9RP^5e
%aKC6'OD/$lmg)hYqEPgY&U!b;PqNEtoc;GU\BnfA"4EK9m3+LIB+G:=/)Z-p5[0N2LaLCf`1Scbjn)-CXIQJiQ(<"&%Tbtk(*(`%
%gc14?aEkoJiCQp4?H+:W4,R2#e6a'q!<u-=q2?(@SKRj8C&i+Xf^gY5)VThdPA(emPQgJMB?\5\lq[on"HBR9>r&(L/6G.c+>p;.
%@]`#e.Oipc"+*;p>?+$=8NUn7Ag3&?8BJPTj*ou0Ag+;/fQpD(egT,odYZ>$&Kmk<Lm;mr`g:aT3">Aa]dKj'ih8AKloY#`;"8O^
%[jRHHC)l_&%gBo+=(&]^/NIt+0uSh&N[F3j%^Yl#d+-gDpqjW=Xau:,Qn=n^JjIR8loSRe-"4l1^b.Z1VbHPU'Se)AVh?da"9Ro+
%`2Q#=AphAVoaO]+YYSOML!*&#q`!a4![j6F3](k#`c@\r]B:Wo1oXq.iuqdujeTd$&N&@(M3oXAjA_j!<Y3Z[&DS%eQaQ3+Y.`[1
%_Ao,<-]q7t;nedo(*Vn!(=0p*mten^!5aGWS-`IH(KC%"IUFefJ^<SKI:*Jm(CretM]d>(/p8nWL.rk`k/!JCqu+sq+s_fnmd:Tn
%l8tHSO<hE=i2n>P\*,NNaq6@=o;E8EpeN^V28Upmm,_8P+0:)V?^h.k\h@3Gg&/Kg5!hIl\sfXNUAkR_XEQuF^B5/"C_LF.h+)cL
%ktd;0)us^<1?EKLU+/q$Ld"<4SSM78_.cS-eTt"g?Og^7Xk^G-gQUQZjBhKkf)dE4%cPua\oj;#EU'cQ7PtVe8q_r:.k=M,$[P:p
%<d]cGI"4dc@ftL)ZQ%@=hS%.ENR<\c:)kgOTQ:sKd#5sdh%ES@.//p^&s\kV+bP]-ko0EV&3ML,aFSA8RRN%hK,A#KHsgd!iD_L8
%UG\Qt9UDD2+\J?7:aFMELna`$;8#t9,a_[.3J:Taem]hL=@TU*1XHI!4(E]=[kUer.hB4KPWbBYf1f1qek]iEbt_+TKD%Fk*6o^#
%i?UfEFgs&[kUV]PFU=PB*if1QU88d)2:,'RF0b/)[:RCuS)K$3C&Lq.Z%5d%.it^,q*UB!?cDY(!Nsf$\R1DQX`[ttrVe]*?)&D\
%4I&T6Qq?k9>uTps>P#Bd7fhfV@8!=lBM>+L)/pM^*2Zf\3P0?/juMa5h>U;^Q&-tqAV.3(^<elJ*CQ)hLocV(06*U$0EMZm#5D\@
%<S8FI$_i'AQ^<Rn8;7c)FleGCOc6#s6q",N+Cp4"PO*Cbb8q:%1ETJ67=,Skp\l"A</AP8gXIgm6A)Qm9UJ+7`8uNHV)3Ee<1Kkg
%8o@5)\3i4,92ON3T+7N/0l)5Mr7!%1AI3Lh2e$8V[97BIQ["9YUNbL&NKGM2;DL:bA;LS5-WM9VGS/D7dd*VO$PQN[l_9%_0n[P5
%N3`cHou=njEGJsdb1rW.HIAKn;"^\J%sfoIZUe=@ZS&XBBct`Go.uaj],&EuU0:d4R253"iRP71^4TFj298f?Xr)p>0d8XpP_7L5
%?3MfX,BddI#+!fC;Pc\D$&r@DEE`@"M29=+P#Kp6_S2No3;o?c4rhl>"1+@.X:ub=dCI;E`cO=!7:-NOp'+uShsf,d&,Q6B-gfIr
%*XFg*&n*Dn[H-L-MuN5.RqIe3lZb?=;W@*i'[6%HED'-S`L1MB#nD^(km_?Ed*%rp,77;O$kZ3MA@eQ=&RF6G06LlL[J9D,@ZbIN
%,5.=7&%Oti=W53@k-WRLG)MojI@4?+HrqTEJfu9!`fXocL*5_D6qh%RTo6WPN2^BAo[W"[+>4Cm`/F)Co7MEdFe^3AL+Z2!n_6BJ
%*f)u,[a<XV43Aji*AT/XN8uO!a)a#qN.%t?_ZH1;kroS^J;B(I5gP'c_hbHbI"!sl*:$RVd6h1F9?;?3BBaZX&9$q^>X4+FdF/nE
%$=aN'ju!V[)5LLoZKd!/`$JChp6J24B+LfC3a@5tcpLQSLB=4,&6\/V$+$7j$@1AqRk0qeE/ntH*ft=Y]L@0de]N]IFal>dh2AQ`
%7'7IW/Z0O4,Wk1<!n^'i>/t2GTcc,"'Ra!i@aPQG_L/"g^0m48;mL<[4i@NX]oE0G4kN\&4ZI@MHKM>SnZiOB\UnRhT[5B_1WJ.R
%-o/`3q\fCb`D<3U<T=]@bdM;;S=A@nMF5Q(RRPMDZ!>Cb5bMLlU1_q9LDkQZgcFSJFI8`BLbc6d9NQ+),T?e=Jen74KG[h8+AYF=
%8R>iq;fYKiL6REqUO_UM!Uu!$/#bQR6I8l#Ct%T"$/>o8f8f;'_HJ(!JFJN1$`<1M(8hD$.^k*odVbh6=*jK.`?77CU4=R0Bal[4
%(hIQSU5,6N9s"V.-7YAUimC`"*YD9lgG/sYOul:TOuXd"lssKBj;*qerhV\Ji%`]06X/(H>&(.D3tB%R6nH(1`eT^i&"IGZ<tMoE
%k-eS4V^u=[hSrZKotbq-&X%7;2Du(%0m,5n/CkQCk>69R\[&j?N8s?g!A(=6lME($3&CcO/8i_5NKehnj\NWk?[B'fqH?^^R^Cc"
%KTKjjVQ$&J_&YM:Y/Z)j935*XjH@m@"+8pQ-E8r.NMWV'1+bj]i?3R9TNO=CP>IWVUMIB2e+,AKfI^^_#th$*iSAR5:Qjotd1YpA
%+NBWqY)rG@#8(Q6OrGrQM*@gd'U(u5k9TVQSo1aTdj6tQM&(ma\X'ge=6%<K?k[:fneO]b][STuQ7h._,q^cXD^-ng\`X>g7c#U<
%,-,;+N-HN&+D1pb8!#<D`J`t\LiEAS8RKpV#j6!Z[hmNh/(8?uJsWJ`lM<s>@X>M:=(++g,,(N\1.dqqggQ](g]dgdM0QiRg.-rZ
%jaR)SYS($QS7X0l>'F8SR7'GX*m"j44Tpa%,[kKf*]c*6dVNr3orCd.>lmdb(-pN*LZ8R<N:WJbMg[>Y&^itO%[mV%3QiLiOamWZ
%=/sA+P$Qd0'Z:sRR5(A/X`/?A!QSWg7NunFh]rO"@NrbV2NojU!s.K<+^/p/eZIRn`4ReVL%lKG#%R;I$.0S)M?NP_[CkXjjdAs(
%f:[,@9!1T9)ICG-Kn\V/=$$Wu_n<qLfT.1H35L?rM,aZhO_)`[U';4aC1&`_Kj9`POh*uBB+"hBhc->)DftZ8.p^A)FC:@icn(\c
%]*7Yr&D?(ehZ<ctdrRr#$\\%TTs7H*RTPa?<i3T'ZIEqHg-a<(5@4&Z5r2+209i:W80nDK<(gGUM2(l*m9U<[3g(DQ"AV#`f\)G@
%:[&?%VFk\+Q9r,6&uF6>oi$s,@+(kEEnSef=HH2dcG#$(SiEoGb(+(3;[_[1#Sn<+bZiAM,U(*6=%0e9`8rO^6F'CVNW>dSk?CBG
%YC\,V##1)ghFfIRh#r@iZUG-jCE`t_8&:*>=kq.8<?eM-BZFpa56`W9<J@*L'EKO`!Z?-^Jj<3R=7pfBWo%Kk+jrqfOiO,Al_HQq
%=beUD;N(F;Qs5<j.LVl0$:`Ps8!75EWmDB/:?(`>L*L:Bn*JeQ=:8+832.U3&'dEq#"\o6CUUQB(a=]GQb#@<)'!c#gds%L-JO\T
%A/)$eRMF5J!`#<9;UAbJ=<.7#oi.s36%`,.rijm\4]f$k2MmF?cY5p&SLGjF%9feV?eFgI-YI6]IJ#Va"/hqeH8r)p#4C49&Dr*U
%=hmTaNg'b(-^2T78sr#i)HL*E3L7A%Te7:.%/S`i.@hSL!<ljkUgbOn+cLVRmYoZD[:d=K'aZALJnrR`a%+8Sae4ETLr&):*Yk'i
%GU5*Vej;5R(<'r9'!aDWOCsWZ&G6/qF4eg?SeN>-1K`VK)%i;A5'K,c_k]3te"B%l#bJ&R/21X1m_IMDddhZ=k7""RU2+?]YId?>
%aW[dV^8=5d8[rkNAeJGrlTGHR3?k^O(564=,iXi?lJ,.fnpuM_S^Vi:W92%><G6O"1aOKs*<KU_muMWgUpbquX9j-V)"T6nX$XM"
%m9O.%XbU!i!EfKR4#b(:jaE+knL-BX-n[a9r,WA=(X<aO,\LFNNraoKE18X&P8]ADE8iXY53i4FK52G_6oN'TZWn406"BnPfOeHo
%V@9]KCrrh700l"CXg&oI*_"I^@N15[dZ>1!l3/^8<J+F3VoO_rLq:nN?GIlm74p'GUEF%:J27Tikp/`K\0]B00`t#A](*"VdDn:c
%$:MuQ9j0"^_^uniK$\nD1!s5#HP'-QKjq.Ie/4[tCdOS(_Z=\PC0U@G&5eE#?BA0O29ZYWW?EfS%%*;&8E&!!(ag2X1@UofT0Mc^
%c`>O8OaaI"ZmlL@18HD1me6P-nTt`n3<hVc1aS%^GAUX(>Db4%(GR$%+;40GMWL`e.?XhZj,nr0G"Ek:/^4qqLE9P#5Phb&5@j*`
%1pLF8*[7Vk72Al<_/<Q%d5X(T&-b+DTp`id=AfP]+pOTin&@/9!k/JA0c(]E!um2Y,X\d$)V)A(3b/u.#k'FW!"fC$4i<&rp1?Un
%ip$0U@a4ACcId05TpX%30lt=u1bNLN8P0HUAW,b\k*;1f@5UQT;&5["lbNaXbp&@0ElF"o(a;QT:]^i5XomT.B8M?sVaO2!<8spR
%6A2G)/aY4p'1GJuCmN$)iUifu9*>\9&^6Q=_:T'5&-p;=3e-UVFrMga_qhLXdGF#r;a6L4*-'n`JKG4Bnk%hA?a.R#,TPu\mU2pX
%E?]RENKX^OF)BV'LJ=t,>0O-9LFL%*:S.H@XlKfs0@BqnN997u$Be)p0V+2lC2eM:[lRB7s#Uc1<TqLJ7O_'\GNdV27faN8E#rM]
%)lXDX>.&h%AjYPaP@VcgecFcOj%RrTp.cnq*qUnPC_?cjVLSnQdIV0o+@O5;TN_GOPEOV!a@iVM[oCe!JQ()=ne;PsO/g[n1EK!0
%Hm*cqiqFA0)#KKpl,?.]^l3#MVe?Vf.9\`m8t:mj0$YGY95*85]X@s'!4Fnhq1CM5f5tsG(d4@(]RJA9JU4I>_GiVP_4#*T-&0\T
%YaVGVXt:68563dC9*s(CD$sqF8(WF8e`g.jUmK"].MpPaO%kd^42&an=d^TT@FO<2Pg"MKke8O`VE:_4fZF=NGB$,S4f?A?-6KL#
%*q4IIM`Vh*j,"RD=HgVC;-SQuGe6+19N]<TSF0)a5jDk+Tudo(ZTSf7N!="-ouP:W0q3-T&EC/0@La53QJOHsB,OuW(8suu,KLp_
%A-2'?_?.p5SII_]brCM1D!P1*U7:tKc;u=N4>N\&/oZ5],>:qW3`%)D2YaD/GhDPiBeF!2=:sE9MT2(o:LAa$eX6a12Frd8&>TKH
%bF;g%+aL&`"qTP$gse;-%hmp+ZpC]B76s,bW?n?[Y0Lpo7Z'tY1;(OH_#Ap-+?.b&aXR?CS/!@$j!F$2pql)rh(1TQ@RBEnYRt.(
%_'3=[C'=udTI3Mth+@.=m;>8@^/Ybs6)s&EHEE\]N)$JSUds`.nqO`=(pIZ_Ro>I+cfFt\fZ+[o1XX6Bje?]!6%H!_-./m3^D5lh
%=GR%?UT7fHMJ5q;T9g"=H\[CqarejN9KDg8',a2r[N<pE9M;?G)eJ97r"11W;2@K!O)#:jBCFmU--b:;Lj%h^hM08QX=QB5mL&@^
%B1)doWH%-!?P'r06Ca6I5uW-7J6,oq76[FeCh=#0k*(XEHLeFqA82gH9abogeM5CROs'qFZ5-JV+OQ/\^kk@LX6@C:Wfic,e`CXX
%:lbjQJ7Hrq`;*=5DN3jJ!sFGD^#:AqQ\eg9b'E^9OK8i0jV,GCM5@5\:IGl5`pio]+m5P@\`U/_:ae+=3g:sdHnlJG'ak*@Qc9(\
%M1fLIQ3B<Z)-YD2V<@GW/*mGA!d[/m>tAp9?Q[PCH<B:q"@"6\!=Y<RF63c;Q[n8;<fFO.Z9*lfL>33,ELbW4R(H`<5Cu;ja[UM2
%P'\isYJ++;>4Tl(mZK=j6k-D0>@CR$`pM^3[#W\'8Pd$/EVl*$XH3Rb?AM_c5T%Rf?7[bkUSMnGa)XM438h3#.)<&T[*Wrm6R\pc
%BHaac+-T#s6L8qNQ`pQVE?rtc%X11UVr?3C`/J\Ur<;M\QrV;54<3?>(,H",>)fb6`i1+Z2:Vn@'0g,qbuPgc8euA*)KuFG7Di_B
%(BIl,2naH8Y$%0@2O>Lcp\%Edi4jEZ7-)"FU1!gsoWh%B<:=qkR0F8&\Ipj@X-c4Y&Dki,-e%#=gdo<&a=>!RBJZ*eD5`Q!@'?B5
%id\NW0To,mk;pjqLq]S7qh\fT<u5KP&tl94ca-1-7&r,c/MF)=d(?"Pqrng+M)lPP`1t2u[_+,Tjcb5c!54%dFt+FLCaJ3#FUbti
%02r1h"JnJ9Ef1Q?CHrBZW[E,/k].H`QI81$kV+n@"A)F,h[F1Fp,gM$Ar%><qW6qt!H9_RN*C;]+1VPB_cGU/-':W0?c:cZ((%uD
%,i*ZC.\0j=PqMI80rn-ZB0J]OQ7l!FV"e:b;d10rA$tih2:j$gQ?sU)9eKSJ3C24MYaZ-H-'BaX>Ne'!>-2=A3AbBl]X.hFIP`3h
%1^lE2/\YTt[S'F)b@nDWYTO-!kt&`:AJgfE"@phtNY"s]G,7$Mkc$Sme5-fX;d@#h;uo$[dtB$#PU]4*B]nkP:ebWbI:7OMWSD]K
%7`@qF6>Y"PU5D7dWs!sY88I?=U3A^;d"IqjeFJWC&D=H?k9Nj#RKQ#.RBe2.e6qhKqu),O;kWjfiBc0bYf&"+#Bbg8(c>lWe6b\4
%8.O?>Q'bWFXb7`-f%fiQbr\G:Otk0k:>Z(bPf-]m`5u`)5h6S\gbLJ/h#'T,W2?[I'"dQ[:<nea8YdEj"J>)L]lS)%\OE09Lc@&e
%-CYQD@lRV42iGA5(k!P\bG3)Q#Vg_.,O@+(`N^Z!WU<$F,:ojP1f/mIFP[X7&rJjZ)/<3:\*Ot/)60rH4b(p&P-eW!*/G*`BNBj4
%\tZ;oZ:;#hB9>QdD?\5TcJf$gjDs$5C4a`0K^,@5&I(rq2i0Q)^'l/SHOOc$\Umbkmf4LD3m*gY_nL7*jr4)]BEel:PefLe=ZYb1
%)EcWi"H/1T0jrV2ImJ"^&fpb_idQ#iOo;opYYVTLg<L_DRKO@>H0M*/Z\1I3BMs5`OFd^gJX#$Mb5ege@hT#j*7PTi7!P.;LO?7a
%H'P$nE8.H<S+.ZfTL]uoFb!aRA20NjX)RSZ-kW)_%/9-erkJ\a\IDYoca-%7d^W72^5j*g:KW=h7oip-4KtmA_I's:n7M<l_Z:,"
%H"4T-%&:kXW?3Z+'=ZfI:s&nm\E+G7G)%)1^J,+Y-C_ZN3-n9B!'RUC\;,-<bS6a'"XrudH.CPa0aZ(6&ZWb:-0nqC7Be@4R=@^T
%M<-mEjCuF`CHVfKK*j*nN0f1fG2['JHOg_AUm,bDS=*]bXWjfG1H8r'NY63[dq#C=rI)(/8m1^hZuL^)ID#GCM(jf4p;GYbk#:-T
%_9Sg5dBpY$0J@60mNKk5d!?<]1(TS)/36B2ZI@:d#SkE]E+nUCpB]#o.FpMs'[2^I4^ZTeTFIf$7GTGR9_Q83:il,n7uGL86YT?U
%lorEu-sSYT-eOPJ(`]b0GV+HT[K'6'IIE+0ei`e@)j^Q8b3Q+BcW(Y:c)Bi-o3urq'D*'>+tS@=0G2Xt#TePb\?u#=D*7qgP'Huu
%O;O>jRqq@Yl'&1=JH7mMd@Ut$7)Wo=DM(E'**Q1$f%$trK&Iu5W`7C2:Ju''L(8HX[=c,Br:-hj6_h?>0X+GWbq[p&9F2sR+c[oN
%c["+/UiHcDZ\`FY9u>C*ITq]oD.Gn0+XRB41ejDTc&/#;Mjb3A72VGC`mOC`q7sN*<hL\V>C7XrItDthYi`J3cC:4O:GU)eC:,q+
%/[ug$)&6T5_!a=dK06^NEt4s=h-S<Y+C6BAHj^,$KUsuY@ToU`f,/FSNs7/'JZ6%V)XSEabk1ejQL-rM8s5@JEf.P%&V.$Y,O`Eo
%QHH>:G9*oI<V/41Rc(8P7okpl8-Wj_O_r.+1Al1P2'Sc,5-pP#BO*b%>aGqEYff>h1Dmu5oEN]N(2ol8;%e/)K5)/6@_-<eW\,?F
%%M4Vu0uemu!=*c^,MEM/pOK;_juO&ZF-YCU((;7[j"dYCE.J7&Pp>#Z3dZ2&2K90%Gfao\EG)r7W_Q)UirKr`W_#A!Z/N3jq%S'!
%&'u>Q!BjCh<gUEMS$V+-C:e$K$"m"=9K<CP\4jHb5.AgMBN05ZK8&ia"cF;ESI6*rZ:>;QMfTM9[U+fZn@E"V^as%[ZtRY3d(rgH
%fV$1naH._q`EN<H"0Ld\9L,*,q*siuD[6PMCMGB\TWM\02_+V'fUL6l^$;<NK<D8?X>6AR(;"I7f;$OK&G)_hiOgomHH'+J\46os
%c([,]JFta1V!mh'<$:KrMl7E@''LPL^e=m.$(!`h!4rK;aIQK/209sA'8F0!%Va$>iOe31=5l+.-Gs#3$""'qfVFq/:3UB-;qdU;
%UoihM1_m6,4b:oVQ:YnT6Ib:F1r5?@T&&1#_3lQq@YBW@KuL#s"?J5D2uA$inATGqKFe_6R-+#+b[cp'piIRF"$^3eQgkOC;lN&[
%kVTq$34-?FI7C#_TmSkakn?CXWFa-^"W!f1on<Wk@&WcmdN.JK*_"\\iJg5b$lI7"@P5X4b="iGKEWM1E=&l-8g+__6#554"4.cS
%V,?_?9[XJ_>IF=pR1gsTK8RMdaiZp^0d\k@[6bXYb.%ZmPI)CDKa+NOPEs5a]C;$/!&+,$0Et!.e1?DAgaNZ;@j8Tulr2).0JGum
%Z6#Hk+stO\d>!De81?s634N*rM0feUUQc5)r8BAlD&Au1dn`d3R*\s-=umf1d,7Kh<f#B>%`kMjlbFD%-hb6J!Xn="7atE*PZR?%
%U3ARiUZ!0$C,khlmL2:V)'OG5#[Dme`_<`kLWi+PC/m"MaL:%=/M6(R(s:VX25dKigd\@1>K"U*D"Ko>B!k;nG_/A3&eUWgUH0G.
%_u5k]:^ZP.C$*u`2r;[qiVZs(:m%b!i@O+53G[W(k:o@qhQ1/=(D2Lc9O;OWo^/1T*j\,>J@t/+NQ#J5D"92"K8t^=M(@$<6`AKp
%l3#k*=D=#;M`B1A=_EQT(,!lQ#cIs.-\`pPc&AYVM6t4@pD5uiOhgA/jQb./1i@%^-4pilU;7`$6X9OXl6Rcd11->.\@r?PYYMg)
%8tlGhBXnL>>l2/B(?im`!B,iBpT%\)4CEe^pU(jc-\O\%[d9NnFjP.O]T@;g;(7+j5%;hR_4`.h10^.W%20?>MJh+13O[K_45>B2
%m:M4D&0`l9ZIg=3_7KHr=f\c\7/CMXoZ^mm&[YY.&tO,3ranC'd33d7WE(2^;@75u=`pE.dDu1e!Ndt0f>u'bZ5Ii#]GYs3f`iCn
%kJM,8D5e'ZTT>eJeo%&ioi,'!1NK)cdTU[c"2U=6-j7Gu3O<PGndV??_QD;Hi+0E:<'jGa,"<(D>Z*u"Tnh010j`BWNXVU3!->8c
%<3h??[90l.7XAe[`FS?=R/0*Ir%NStj>Emh1>VM93N\fh#uuF4*lES\;$EuB0aKIJbZe:aNH,J?d2l0o\;4UlWlag?oAWOgq@mn4
%'ZkGS/pQ.iNB>,heA7B8W(!HD#!4#u+,)[/_4@$:/*/6Gp<$>/Y/,e[$s^djQTKtXX0Ulhj&ud&&Db84X^=E+S2ZUebr*4'r5`pA
%l\pXhqFW%uX*m4WD6TN@[f=QgBK5uFi)-rmN2u41U#h<#j'n`)P@;8XTPE3$e4b[[B<u?t<k5s*(nVfo=N_FkN9:bW>g(tjI&QWb
%7uPJA@[4qG5VNM%)bmZ=@&>`cOW)i1<)r)Fa.Ppq:1V90Uj$?9Z+K]HB-F6a[@5@5"p"SMYbSG99(bcl>:)*12%C+J.BqL+\$Xn#
%ia8OPcib4sRNa5t0I4?aao`Jm"<+o+pPJ,(`m<r7I/$G8e.<1/>bOK*D.[Xa9W/5+YDo3C9W2YE4>C&L>SF-mMA.tobdJ"OY*^M7
%9!;7hU4[*);^EkZf.DoRk[0hf/hFS[?olc>q1Kg%]m/A_cK`>mQ.BCH7+>Uq$?Qh'VN/]AKcai)+JlC_Ba'EVH;tFJ^,kl5'gNYH
%dUfDS#7LMR3jcSs-<2u-nM66d4!fP_5b(E6V;$R,.WX<]J49V`m:@&KUaa\aa`fb(PH8CW,mnm"$,+\bWabN3X]r/FgYC<V7C1S;
%ArDTK;uU>5nP2Y(g\e&+9ghG<fsGD5pU$h%cWa6'%"6Eq]G;Gj?0qe9i2"Y'!D+FIGV)'XM)Oa"6r1lue!)Ygqc-9Eqil!t6cHio
%5.;5Pp)p(T&f#>hEam/U0$Ii,U*BrU#e5P:cAHCF&qn.&Tl2ZP;F-S"HdBMEa.PB->##5iiJH2MZKcECWJ:h@4bH;9RYmqC6QIJI
%<'+Of=]+afCKOM<15(Id!5SLJkuuZ7(l6`5-mif_`<]6[<92a_YR1t`!@A+<Nscs\i#"/VV=p"qQ;Yu!a#+eE%&%q\]=;4u8*I1o
%=%%6e0`gp^b+CB1:1(T?*[$5<M-782'+gh+U$L-W'*&-m\=e-j%7i25r%/;(VlZ$rDbt+9VX)eJ$^bgR?VgpH)^5b2FKK2p*/fCT
%8!F;c*[L2WWdt/MB,4$K\ol3(GD%pVJe_SLJbg%$CAOrSnMT(N+R(cj2pa?,/\?n-FSlMC_:LbU/^gd0mYMPsYf\Ig]2ZA0g9T(8
%B/_^P+AQt.Z4M?eH5k(>oEmDTHMb"\(f2fq\AsWs*DlMt-Yk1///DW,6#WnIF`nU$C45DF>*EFJc'C\$?RmOMH'n5(<n('/db.%<
%hue_SZ-kL'#:<ZV`l($QDhO^Yd1R^]LujBMf>P*tK0_Z&GciTSBO1*M?5k&fH'mM@p%Yg6huKuB%2L48"dED=;a^snPpK+d,:^VJ
%6*:!+fa7^.28>a?-\Gi4A.Eac1A"XN*ahs1-</6?`[XG3;G]7(+rIWFe,\_b&eTmR)a2K-+ldd-[nHrFg5)^h&Ud!\9"dAO;1b.\
%pl[D;@aXcUWgN@I6a/R!X9gaR&TU)D^6$.i4nd)ODqP8bB/j[T[i1Q+e%N9bQ32+U[6RMbMuDK50%Fjf>[Lr;>"$A'(:+W:i;=qO
%aMRk2hHF:=##fL(<DUd&ZF"VNYD8.%QE[/\Fb'6B>VZKe>lWZI8#2&j`JB+XCb7/2-D)'KX$^Gb204<oB,J,ma72J'4[n/n)-k4)
%^C$Xle@&6lR/r'oB8kKt+lSk"[UR3@3LdUU$VQ7WL_T=a6q*IO'2t4[Asfl+i!`nYBEbb)243OD>B$h`(L8K:FJp+:Jsqn!OAN+*
%WKR9%p#+=O^pV]"?:(`2Pjio\+V!=ddicYAlqgR)l@(VO3A7dU$:.>l`bSi4(.j>`"1HonKN<VaC!eAm"]5MB7UIdUE-\r.,Ja,7
%<pZUn;Fo;o'h^FCO,4L:/b#-_WrUl\OGP_nY=(SW8@YppV_lqoB"Tf(hPfuWI@8M8!P0L@0h4$_&g']D8'"q4M%s"jZc,(J&%4i+
%DUM4)3QuBQ(&6/;SNZ1@JWtFldgF;oG4LBs(^n><4,\FM`TT]GVF53HG&b`hkem8"kfGO_Kf#<UU50I]R60Wr=CLnEpmM[emokrD
%ak;;QmKeau*eNHkF1k(BH>@%!#0hCt/36>V>R!=Qq=Mu?LM`:^'WZb=/.cl3?V):H+[1uK1!oM1h(nAGKR_YI/Ib?]1G_E'3:1`m
%nhHN7Y=fWZC,6,q452?e1)g[)I!S4s>cO\T/;[&O\t$+^+&,.=n5pO`dQs+Adu8'-3fXu'=;*/do9&Mrmj3BUoBKU)\cf>P\g%W`
%k%)Vt[JHM/40Kjn#a*q=)+Ont-Q*1)bD.E\GpV6J`QpY`BZWl!k*OPgWGs=H0Br,a<ooK\Ofr<7:+4['(/Zm#(Q;g<3Znm6l$0UA
%f4&,j!aUL>F9hdBi^0fe$>(IsPA"6[)e:J69]gZ'm?beiU)d3djKE3.QM/Bb-mg.<W9.<N\1IqZ_%!q3MqHAH%$%H%9gC!W)hUI@
%jMe.S&<BRB<e7,+":<Fs%<7%oZP6Y076lsSXlH-59FZq+8]Irn5pLBJLEakceJs^Ae3Pq';mhrO/QqiG3FfhJLlXjR,T\XYF6oh9
%LL*o"S5-Y_V!(*DK,^qI">T#J/onFIX/i_+gS'XXA0dKRKNHErI)4YjNVPnX=7_2iQ->Eq#I]Gi6/nR,^*eRG;TmW-pECG?1-7]^
%C3:[F*TH%8Y:io]-]]goOk@%T=o`Fb&_2QTDrHYXLaH%j2,1PnePDp)g*Ek2%+q1[d@XBN\77b0#4"2U-KFrP'g8K%0In30c!^s8
%$=U^kCn#CpD9.W;_q%l[n7tH]Pjhsf$EWgfi]&4t:P'R])KqnQ%\a]6.``!kM_q3>>?U#(Q7.e[f>rkdotEG.6f4ePiQ`,CU_5Al
%%no9'(!&CWT.N9j6X.>?Nq@fo=>S2Fc'T8J+Q>g?mmU)9d>A2;Hl`kW/W7VP1EsQ_VZ9jgHD$W[0ikNc@=bD&?k^mJ3-J9FKm\:(
%&eV-]<uNd8Oa2U*B7kHhqK*I]MIWHhLL;%Cm23PC]3;Y5HAS>MUn@lW3!.ge,G.YSm"X[gA'Q)LoVgCT:$:mILhW[F:t4a/oMCjB
%YSO3Si6aR/3d(1s@-SS")*uRs[*8n%4lU3iXCqd'+lD>u$!nne9l@8$0Td%O/A">k=4jd'O(Pb@"ZeqE<h64noiO'jN>"&S`0.@s
%d#cX?HU4[.-#b9h&aeg6[b%T+8"+]a(!2/4Ym?#]:t'LdVaZL6/NiH`U206*"`Sj4OQb'VA9;F0V]Hg[."#fp=JU'XUKJK`iM5$_
%8p0AABE86FZBk7EX&Ui*kZ)\!patf)@VaAs;Gf!bRLMa[6Dob^&g>L%qPeO^'CQY\[I'?i#tojr6unM9WiH'm=c1gPV"3;:a]k9X
%V23d@/:\]p?W^RMSQ%/);"g(246Pa=3MV2m>#\`4!qN%1\.M-SP'Ma&An>"+4@DkH&1/6q3p#<mg=5M!23\D_cP/t^3J6pjFp6/b
%m87)22aIqDe\6io,gV<ed"aX!>h/rBG"'g<FXs<D@cYrh">rHu&lB$(Y,VKNEG9^ZQ:crgX_FY4c9e5X5]'f2AOn?H-"YPo,a/[0
%J.cK@cTNYoF)r+2aP`@?M/tdr.KbtClK?5g+#,U-gs-J)Uo9U'.CPBG6UZ7":XMIkU@C'LTMn7a(MI;O8g]HaWVD_:Vn%cIZ(*,S
%mF^XQ$J$\9+_B"^BJ2VbG1e@Xh-RJjTGlP_g5tjNH-9n6oSJBU>&1@@i,ld"TZRIK"shYXO[W!)='^Pk75163TqN\W(.j'&0meIR
%;[?&__q];G8PshJL`W6AY%cIA%QJqacXPXQMaK#.6*5@@SEBc\-//IITkMup<G,h6%dc4",3Z0_`P"/,O,3lUrA*0.%kWVAH,8.[
%>h8_l1":k89iR#mG=6/miqSaN#F\miX01+)j(kUPP\.K]a!9UjRfIZ8,8>uqDnmW-Kp>OV0aufFB#?NfLt<i!.\(`?eoKgBd1WZ)
%oK!kVT<_ZhnfhRnBPj5l^aj.[e0&]dnfQNrMO#\_1//oAF;$V,+F6368Q*7(LHB)4-_uRLPfoA78tVTn,T0pM<_Ho,@Y-p\#ou#h
%FTj9]5qC+rTa:o0l!VoU:Ild&pgpGH8uRiJiO!o`OA[Y_)a#sdbhA5Nm+-e58.:nf$OIKO\Y:shSbW3kng\esg1IsVp(5.n^iY3J
%?0u;RC$5YV++nQ9&&jomk3ijYF//;Xa:s0'O<s)C3`&(ZAmK`4Jg_Wp$'tYSKqdjHnPh6KPU$<BeUaR%'-o,@;B@VeVIe)d<CZtN
%8e8"c:3[gu20L?`$tF*>G4WO`>3IXkel'L0X&)?(']eIscte#C[Q8$6Y7o>@/cmrZpjH6Qb_DW,X@-#k.k-H!'RYRnZO(,T`]*7[
%`g770([SF"Kk6,((j*`hb\:*DNPGss9<4^Z4ooSc/JE2F8?mk<S88H>Em.#l\53;HV`_?m[^1*1K\\8&%7=a%5.a6'@/#IUi:0[+
%O'-R0O@n_p*f`b+IJ^ZJ]>j&PeSL9%<ZQ_4<8C(QOE`9@_Y)j#!DTTRibkFnAlITDM-9@8U0E2SJr6PHC*booq%HWU5VEF<eoA!)
%Fl%X#?%=.2d,G3"-s5\<^hV[aAL8,3&.C;?>EN4HP\caF?1O:t"H&@27CMhWVdUm9MamXO!\@$DUsp0Qh[F@T"$[JR\Lcd`J4H[V
%`Mdp\D1@(Acr\"@Js=jIQB$JB?HIlc:I&nEU(l#7g@5BB_YDSOLLFkNeQt&6KW;t&jaPqkBGqi4UFqWLpT&W[*OQ5eff[93`bDhL
%^/sLm'oJIC"T1[=cURe)UM$VFXYkR\Cgo8N(!qU;CXb'iZRBBC7!pH1VBe6,JChM"!Z@NW,0_Bl3YZ(=<GQ0033'C2.5XUh6F_cu
%GZ8rYr$i--B!m!VU@s[6M[J>MR&XL\).^.AE!M!K9/E+2Ak7pq5TCKt1FTQ#;$dmI/.GUa\1fk;4"7_YjUga0]N7pE&s[-9;=Th#
%5RW2V9jt6[>/c5(`(JPTU3)9^fndm&ZI!-odQ!QOe\`ndN71*s83uK8Rc>]9*gj4G0jdg1U^.E(:GG'OTF;fUKm5(?.8-e=iJIf%
%3%Mbe$dfO+<#1I)QJL%'kFsck@/1+VG&%;&fME18=V_m]hsjHI:7Vf@h(-,Skt]sGWRqAC=QmWR.Bq^qp\<;_-g#.i^c?K&dN2%k
%q^)n$XX-Rje$++K`SKWKPA/VM<h'VR=*.dnB"XA=)&KF`G-08;k+C6e.)%g>MRWH-pl32dBN8T.5?sY1O\k"[JpVJ6KPc?)K>\8%
%aR4T&_9]Uu8PZpso6If>Lq<-+6UfDne-k>t4(FbtG2P#Y?.e2=HY6r(`M4)AcoS1[""^dRl5m;':aXe-[T&,d`t5(*Bp@Rg`'b#O
%_-;'qT-Nt.4U0W-6H0<DKs<'tH4!JDOe8SS<YD1lg'(IQZY4#QYWJt5QR:b>eAOPi8s__KIYF@e#YS?/._VPLh3nCOY$ff]`mBDr
%_tqs654q)C.lTp\#l"QqVa%\P[r+>*mFBhM`%ugUoR`D0QK?7A4`$=:$rI@-_EP*d>gZ7A?LeZXnX.PZV55os6!dF<eP__(&gH%q
%4GJgM.m-?hC`ZTm6Sf65!.t%c-:j+R6+!C@1HW>lb%k18#&/m$\8LHf;Qt_+"tMY;9u8.+&`0Sc$[h8g-\j,^NRb%K#s-N$]!+jS
%.Qc\Gp&?U<%CC[=]Kf^oVC8%)MVN>PSGM()K7041nY\pf[3C]SG1?[4Q9s?43Y]'2a>IlN=<+n_mDtG=iLC7\7Pj9aiUCTV+AF"(
%qD?)'*+!B\=dG,T0\)aT%K3Xb)="3elB1^N#4[l%@6G_sP64(26%s5e7^"]\\.^IqY-(-I9HK4gSGCkKGsbha([gd'8e(hHRNYmV
%7:,l)V+L2-hTX)*08:hP1r%*3EIf7"#sR0c:FNP*5U9Au893o=I`b6:T/la*?\U)dcC<UO?d*)%>LcJ8pSJ%9KOe%>s1;J9^BAF4
%Dg4\1,+&\dXhmcY\$U+W=rj[L9Z1.g-T?bn,:nBllM_4$12E?3Gl5c\UEYnXc,K)/3"<B,]%%ZEHCc;1o5fPP.-%^W`sSanG2]Xp
%2-k7hBP^5;RmkrBJ^na5XFmu[7GP4T@4_i)6jk0Ie=Hm"Uo\7%eq<+:@ZQO8O&kH^))-]1N4W2\lWa\(e<f,816d2pq,*rLK4dm:
%np&:lS;a.b:XOf933HH"NgE(`$+^.je;5*l(Q;!.KBDWF(@$SXn:tlu3d%P`]YFO\h4BS0UqUPX"/+]<3%7m$\$W!3eVHX9?VX@#
%dOWu&+K$$0rOVdHoVQjPQChlFUkI]pi05"hS)Za;4<FU'gSgo)'2n6_5S+"=;2N.>9oRVhTE>_2UETcO7AgVn'@>bR1Cm]n?9;*B
%kbsjlK#,QlYu.1!V8]0,Jc05lYgg"[9.U-Eon.57!,iXBPu(PZe2:J]=ag2#K7e0%TaW:!S:F)l!"T\e3>pR0I'[JfMO/s,4X8l:
%9SXaQ@6b?XA<r2+KF,"C!tiEp:D/(F1a&*F@-NUp!1AAAjuQ1`/Eaaa\g(7#-@4%M)\FMB'EcdFjj@\s6g%PbD2airmK"@D0(Xg?
%6mU<a!QpBZ\3F.b,u)XYXqBPc/L;?q]K3OQGD9SQ]"^q"#^Y&OKFA,'&HS\0hu#KmZC*RRoMiX[",Q:lmW9d"n;<%a>+O2hR9nh>
%R20@"=[Q_q6*`p8fgcm"(lQUI.],Z]F9fFfL,/E%fSh*\NPB%Ur)R<iEPiuj>\_;iU4r>a!uS'S274XR`oUu>b"Z9l)FCM,Q7V3N
%=@0=]SD*7Mloi4@l(FF4%@NEeQegZjP/0r@coM6bAd+Y-4`*YKnG4Vu#SWAa0sd"''E'[HmihZQ<1"Fai-C)o8t]u1JK=(I":J0$
%n2:mkiP/t^@amSmDVtZ&-2:,qs#=$"nBJ>)+ajf$_c^#q^D/0MboGso9#i'mMP03F5B0[DTPq0Gf6q+'Z/f=b4VS,[-')S]!^n]/
%+0uZV6Vd;)5+0`o'\3aR.SVZD<c/3=$g\bZRrfjo[?48+@nR*X8Mf^sSn?1bag6rVL%n9E3uWaleoe<p1VFu>aC&^C!0F,aKuBFB
%:dH:KXFh4L_@r1^&^cmFglBaj*!A0Cm)kl5'e#MeoOqp>?sii9G"_rlL$f_pg;Gr9m,^-)XYA".._8NnV2=IQ0PP7FXEcGFD;aIb
%GhIte#(sG"BT*Q/_6-g3M'9GZ$kE&1"fR8oE_J`marMq:91!jGQ>5YP:f_$0&W-$P&Pn*?JVUOBE]Ata,l"JCWAQ&]2hl^m7\V7$
%r&S?Sc/<#dkYs'.Mch?.BiJuOf?bCGGI'sCg+rnBDTMljqMI`u6\@tE,[o@;[[R#I+C3?I$aQOimG'7/L0@?sNV4RUN/WC(\!o'?
%RMsK[Yaqdo1[h83cM2>&16?-*F1&#9c.!3>O&KSo-=Ilde5A,?WJj@>XMD(-QK_fbiJia](dApDW9=JgZolePMaUJS-HFVF\eIKg
%%<U"8dL9-C0HhstBNE+c\ll6L<QJ<?3:Ua&kqdD&;/Ph,_K'Bp:u;R61W!+a,o7GnG.(Y;<Lm]I\/CD+IUZC[3rc?(DN-r_IC":I
%!tWn<HqB4u=E8?TSpa%niH=@bAmdlmJ0jKZT?</$AnVk&&S$?,W@T3hcEQQe`7SZtSt*:N0K569C27-"d4N,)$Vni$i)+?S,:^VS
%/<J/#??)$sKHkPm53*KLS1:2F.[qnSG?Y-V=Jf!WP(q\#,kV4!Z3biDo\_e8]4.uk"4jPO,"d[,d;)\9bQcGCKJ/d#U"L$WR2*'S
%$BK%5qid0JLSg\(2iOnG'm7btY_uLql;JFeFb1>t5^T_[?,D@>J\m#:]n:KD,$_HhG&8F=hU@,%:Z<@q8kJUr00u4B"I)jU<jnsQ
%$9ipdpA&g/T>XqCaprenWEk_9I:C3):U@DSKqDC.N&b)"0_l2*UPXfQ?%Oo_Hf;X$nY.mr<bBAXluQr&N3PGZU4AOo+FjGOR"D<,
%'E[U+3Td]YTX.HD$;Sroh,He\S6Tj;WEDNb$hU2N7Tc^HI=ka=d.f1G%b3<@ei6'V@6DMDnC9&tY5H_F!+lC2WNcin#BFJd>QAD&
%KG9s2YiZUUTAslb_Gq^RNl/!gJ;9e?;ps(r5@`CL7AtN72QB:-Pe<j72oGdDe9t(=QotSnosSP0jhdKu#;+WCHgs=&%pH#m5)7M=
%A;RE0kXJ84&0b82^Xn+%*-EF)iB#<#+dZ$g4M(c?JNV0%a>eSc2>F&Q778_)O>Xbf3X?WomJn!I`!C"PB"0YES*e\oBo&p'-u!^E
%egS]!1GGm;0kTW`*eum3WY(^,:XK9mI&=fD.ZbhO"gM*-lca1Wdg*[neeO142;6ESiCHmRfWhWRIbsAO\W#OJUrKoUfo_U6el#.u
%if[phioL/q>@GZG.SB9`AiUU/<Fh1$ULtPQ6q,!k-@tUl\Jr_K#j,"fEeA=ji,-!S(^0:)7+#BhQ-<0;'t3%6%T":8YFFEfXfLN#
%GsEkeS2?;"1RG4=R?uo4=lVHB#K/GPnMkCmHpW6kk:?hVafB5@N'j`fLAh+;5#NuVL4Ra2cClql$q5OUFkA_fYo\/cDT@q8Yge9`
%e<`h'B@InkF?U?I'/Uo?dhhq2^-:=o(2L>A;EkQK/Au)?`R-='rK0Z.b5_2Xdk+2u)]\rHr7alk!I!$p+KX'8WbH0F7L9Z*(JBL[
%eIas4HD97?!r6dMihE<]h&h8Pk,B$CW(?E`d:J7#@PDO*,cV:(e]C'!-lp1`JmcXjF"4XMJFN:sGe=GR_<_>O#S@1c!Os+u-6ZDG
%]Te8d1>*_h[_>'=8B;0N2Cln>_IRlt==G>2/UKAeKTZK/26g"T2/)85",-s;c@n#\D]+&3LBp:?)OhMU.X?SWMUdN,em[C:A1;Ph
%ok<(`/>aiN`"j<-<)IHJ7\=:$Q7\+=l/-Z^RO&hJ\JF(b6uF+]?f-CR=rjV'UhK9=W8a9,%@lr/_9*M4$BWjCDVS&]mgR%.B?%9!
%^tEl:DG7AlPsPcbF(.9SR%[AM1_RNlQ,s?TkjNY1HdRG]ckZpq\cghhcpMf/(9$M-b_Wco'R,'l0%G+`V[2QM/Ha]<2],W#)s"L$
%0Zu"20Xg"QfNI"O$ltr<*-R2fH4Jk#L(q*P@,59V>XW:GY-DO[c*kC#CRp0`'R6q!LL:j'PGjeN*Z<]pI["0mi@nriNaF`%-Ac=p
%6OF61+db^hIQ)=V$BW'uGW&bCo4Xhlb=jW$W;"5^fC:*abYN23(&]MoVXO?!j7GG3XQu)R;3KD9I%'AS7!SrL2RJ6WfIXkE66GE(
%4[EHsCl#7c"AQKl$^W[CV#pe#_c[rfLP/Vg6j;E2G[@hee7E'Xng3KWi7'MQY*nKGZQ_GgQ\$E,Ja:KkV;k)BT@6N#cP$I_rR-Y)
%Q@OOnI6pEM(q*RMM'21KE/GDNOb\9S#'"(:@NQH)>T'gS+=rJ&;D*scTHU!k;Hjb"%/%@SClbG]Qu<'uJdtm7e8'aR?'9iTj3A4E
%N"/G4[moO3$Ng+pp\bZPGN6mIT$2,i@7=l>2Z/To;%91Yp6A%!9@+D`3GX*$X;TLs!Dlo=(D9'I9@'ipG1.;oK^nES2Ng*rL=BTk
%,oUkTn-o@EA5nP&:K2IKcC9UnJYWmdXm`L'<p69nB&V'%[nOos^bPsu(U+RAZ%E8ckaH%2<:!1SENR[ASHW>068ZWM.Sc2ni"B'K
%#`&Mder7A:-MIdWLP]&g0eF,%$hQ,'ekBqOfOMGZYMog%'2uad9h#*m&i7A3jpV\BBiV9C0rt$r1$<bao1RG]1`qh8a)rR@9788(
%>u3mEXI*#;[V>PTF(Q69eV'<;G]\7T0+mS@\QUjC>U(K(P?H?+@2K]Amj:T^n)nrK?Pp/fHM%TO^ghkrl;[r+#8%%hW2L(E\D6R(
%r&uLqekU-B%h@\r7f<82!)AloBc^^^Cmq`E@FPlD-Q;p(%%&e4&Bh'2fRJgsU;k_Ma]*BB*='l-0_ggc"'?#KRe2usTOrr:BP"9h
%39H=WRV&.ge$rtSg>[_50^G),;Mg86X&'g6(@$H*b[XBB*tA$K1Qner@O8'cFd35+7E[*'#llr6\<l5eCU?noSM[XAgX`k9KT]ee
%J<>_]Agu^@%ml;N6bf'W8^NSd!XCtYi@N):]HT[5%'pl!]XaR*BiRk@U2a"qk5H@Ojh'!NXe/Ws,e>e/jU7^_&;n9e1<>4EZT8OQ
%c3=>\9M_uFP<hT$34ghPW'^-9QVc.(Z[`cUKQ&qr-&,m?CO2%m>>rI")R#j>QEAGI%!uq!F6WNVERmsFTtofk1=n874%%,KV8<;q
%.V,]L4CCaN")ch;[oYO+UmRpk,M0[te*IO>V?+60b_Y3S,@31AJ3P-o-tiob0M7%q`XoVGW45-+1fU6j%`W2_'l#qpC73<g4nu]l
%b>@>RbDe.3pW;s<=1#HT\uZf_Qpn(EofPJ(%8Xf5`6K[m)9-Je9Qc;AXbTD>#dUGZP_rW)Q2OJo'$lCQ&,Mn>*5NV"6`N9!iX.BG
%E2KDAPVnoT&f#PB.5%4l#Pt_(VhCWa"iT$E;,W"Mk,-ID8l!?0`a5[%US,oG+SNb68%P<?c\eW5Xg2>u6\musa:JrsTqc`6e#8Gi
%S.+99Q*!d][UV,LMEb2D;5GuO9A4)<-qk\6YM+%VGeXK<CpOFNo1Z\U=p2kQ'T.N)!7*sP91a(&4qQ,86?fi+B2"Y6g(Y,J6Gs:t
%?Ze0!7B7"Z!M=d2<WP(8j8Km;e?4Vim,Lj6d:Z#g,,-8`EeRBnN<mYklm=1%/jrEY-V6Sg$kZ?X8""$^ecFD(Bd/:bKY4Xe+dkYr
%cQo]G-/T:t85qrEd2X7&-&=qmP9ua0Vc$cYIRGhir`=X,Lk_p#<pQ93Tn(n%i+O.0o8Fu6CTLcLb197f\-m+#[iW3(b\GpB@PFL\
%TNBX8#n*1D\(.d/@DTbn\d)$6[Q(4h'+BtV-K3)sbg+bdMCu_-B-AH2.g:"hKOi+[#Y^rbT[6((#Xk2*Pm:1us1]*mX'gM5/c\L+
%b=Wc/Ki=fun.nc>mCJ[B5063X/]2nnWbpMmq*9o[#,dSZ6VgqC)MR)r@PS[+5.<l[0Y`+!;K^#LQ\^5-&;!9fD@^4->\bNu`K7=R
%*:]_*R:Sh2<2)$WfmXJe@#dC3*b)E?"]8lBBJBQP-j;m.'2dY;Gn"4ffTEX08jk=Y"sYlO`bnH;CU-"T6KG.UEg8#j</al(/K*PB
%N/NB*1.9V2XJu?MEgY][a%qhjW.P'X]+uAp@^a.$*[-h+1du"Ib&rE:Y%F,j\s/sK2']E/76@.^"PEN-8VMtXerV4F9(^-MdZTk_
%FR`feQ/Hb6bVV\W;U]n9VjjDUOetB6[hKifd$^Uq`\j++$[MI4>=)ca+D7u5FfgQ%ee:oSf4`*:hNptDgb>I8)`!u"Lo1Bs14tt=
%m\r)<kd1Sq$YhRL^+OJnX<%_r;aca2+-lk*?ikAF6tL.VH$7Ct:K4BaMZ!eai;g3Q+:kPCX\n2t_BDahRP^Z'-gsW-:)\>,fN1At
%l)IR"pS7s+q<l"^Qc;P#s(YZrGHSa%X<DjWXEK1B/uCI[<s__YghU$,p=NmR-+F2#G_<f3>^e&%;8l@=49g3],&UpFd#n2GcP>so
%>6km^Y[<CAEI#V^qN2[PN4&5]<0^Q",UP&Jim\'F0F%VF3B"GOAY`]Dr=G&PT+h`;M$9201]0Bij=)G\@!:#"PPUdX,l1o2F<T;)
%$l5r9W"AG=P7bmT'OKc7E]$N2KEcn^+5*]+bf\JWktI482WubH2F*h2gn?uPb4^l<l":a'-XJ5L)4CC2*0D@oGq*8I`(-ND!i%L1
%k@"soJP[1[."MQ]!X&NUAb!P9egjl4!hS*3*1M6Wo#e"i?D,+'.rkJX5;rIBj]j,A%D`o0H@mp-1n8FpLOPntY";_P&XuAlXj$NM
%g;=Q)L>6WgejDq<GK-jnH`(L)XXGYR_hc-)UV<',(eO?\7m+;cB1D]R8L'#teT]^aLBcWC2OdPUpDSaj(qj\Gp'A]%&Z)?<d;fJY
%;>lJS*kSU1)oBn=.ZLd]F#60CAd]T9.lpl[>=XCKAG(8)T"<'[I$]h_)q8ZJ@jWCQmAS,b9M84=@)4(6B.[6;c)`]mK+4"]"sf?]
%G!N^fXS'4RXu?MC];<MGA!dV5EW^MNc:X-W!!V>@c%:$PEaCh@@]rS.:)BBZIcPX;L`I\B[CBFFNiWB7d<J<n6d_!qOu*@1f\.,m
%L6l9o??j;!U(pSYelY<FQfYIjdR9^3h-*/Vj2aP\ODH2Q,]M(5q>udV="d]aAi)kE8f#hOi%<FJHH7c)LnrLs)+H&HY1A5lEK*0P
%VPohr>a4Fi("kUmiRm?/jlW7u&5che\J(%nP<1qRO(M<2dc+VsC@!o&'=_@`KAqP=1>VKt#,r1:bLtj8_Jl1;:__;$TH2N9+Q36M
%!?0j,&K+)uK:sn`mfe)1Go4q@e;qVNVBi&;A:Z3'9lDaK>2;lR,`#n1N%#Ju`>26&JLDX8,AL?Keuu'oPE>JgU+K,CD)U^,;k(,e
%P2%],N3_"f6&/_q/[-)1/"0ib/n#e3p9T5-:07,q>g(lkmf=&dP*WXg>/2YbeHq'L:$,aRV?b/E@gTC%-*;<3#s-L.VIba?-SrXt
%<V4tMUJ&_ed]X_VGb)T]!jOLr+qr?I`]G\Vp2EeoLN9i-D@JOj:)'N%rWcd^A%Ar.2O:H0@g)'.'-!0b.%9eIK]d^0.3=u'6_"!"
%F>"mm90o:Fk_`\>/*'ot,S`=\#4Ci;?4AYK/X99'G<\=IXVt_RKt-?4R$]E.]pK(p0\BiVMiWp9kb_FV65"9-H#c9]dK0c<ptu^\
%J43Zc2/T@H"'pM=ap/)f>l%p]15j+J(Wl6@1&PV%"s\HG[_=]Q%?f7J._V<+60kT4"e1D6MORUB)j#EnguP1N.AA7$Ej=;+[/+j0
%r.G9qZsqTCC?SLjM#!$Q0V+TXm"qYamMT"G^2&X+9?WM:e&!@AmE&Z8QMgr3NkqC+ZXl7\[4!BXS0E\LVFc/Q's;<m?kQI[G')@/
%kZ#IE^OHs[.<4M7CngaX)(lQdgC,-abWT.88)]!&C/'QOes]75a0kKG_4jM1E1?`RDZk-#g%qB.s0UtqC,qj\pdApH0aklfb[MVb
%OuM>9iET<4Zdtm7#!J\+VhXp9+;@P5'$Gk=Di\]P[#T]6>;+n.3E/fDaF7`qVE)l%U0F2F>+Z9A?\a_9-9$H4!@aOq89Y+Jm"<b`
%H9lCIi9W0Qj)b&dg`++X,,0@-U;2ao)VqL+)ND^3OW)<9=fgik#k5$:H0SJpTa/Ztp;k1k.gCFfrYTHr'EbPu=;#aW6#GX+Woe$E
%A9.nE+Z,sXD%?8U8u1VP*RAX>j@rOZs3NFh9`lR3P45;)$-q\SdVd!\e/U%6]uCDr.>ZVK;d7iO&Y-"V]A9q*/o.CO!*0at*HmmE
%h;ep@'-P#2FHt"q$CO7gO$Nub%?fR^FKjOC<Ch6H)VHj/Ordkg0q84<nW3Oo:OdQ[fmeG5h`pqU^UMO=IJ*H'Dr/2+l457$2eR'3
%PkrS05G)<=VZ69QY-(MPcX%\Jq.S-9P5Epnh9dl(+m-+LIf,sboS5YOr1D<LV>p5'`aAa/B\sf+UYd-kTE"5;?B8H@p;\o8k!B`^
%`qW1/LhOb_,#k?raeU$9U(>OR,nKeG<^2YmDtS0?_ts?,YOS'82P*mgon:BO5tOe)cj\%b*X3^`OK(naH3C/2.8>Ts+>_DX-^;Kd
%e#7u=B8SJ`S6!gCb;<+]N3>g&;/;jRRW/iCO`X>"m2<bUAI3XN=B1)D#H,"(WOaBJ@no'(`9,!/0SiR=/76op-qu6mq%c'e\VEr!
%@:E%o4"c@LNXar[%3eN3]J"o9Kb'Tm3R+JM"6CVMV5Hf,Ae;SAfU3Sus#,ZIQU#")'.oQ-)fn=Xe%&5Zp->17XtUqknl3M`M*l=V
%pcZ<U6ldM/+mEAp7<B(ITUO^_SlVo)%n3%FqYNRV8!)t5CP[h;W\K<4"=GMX&;?LnIr^Df#03V*;IU5pH.k59,t`l)-4Z?^hhmTi
%Ccu_g#LhcHAR[#uM2>Ki8#A3DjHc7\Uch\>M4%[ZC?U4h8=@Pq(UU#+%t#.o#PRL<h5?"REGWrD!W\196egon%$X4p4cI'T\so=%
%/)27.a_d/'o1o!Gj\e07hTHb+i7&-S4NLfb;:hL520:")^#+,aM9fmUqMt0p8JDY/dF4o%B0D<%891'E8e6liRu5Y#CH/t\Vjo1b
%/tn%DH(lU"=u2_E@0W:nij/47`B:t)W>s(!86$4h=@m2X0naNnMpdCSbNls?'af7X<GiP^lM1Ws7;($iMHh--LR>DqNklI"Cli>a
%9M@g1dUPboA(j;8oIr;@XAEoGV=;8mQ=r5q_cW$J2olRQdN6p(`H8rO,Btg19JWl^.,>(6T4&p6NP<7j'T.NTH9`!,+J&.G0q_b!
%8U+XE3+rh5)jVpgr\B9uD`9`lFNpj?N?g4#2?keq_K9`V.Nm2m2@c]p2gpKSAD`lI-oNODk9ST=M/6[c`G+.D&!$9$AmDFfkSUs3
%6t3ag?8+*H&6iul"&m4[MehL4V)IJ!JK\$\Lu5\`V)@b6M4#P!CbHgBJkR8!/S!h10S1o;G&g"C9e#q?c4_W[7Hm^4=bs1=m6SZ[
%5'a<AU;X'F$hWX\XCr8ne-UnD0*R\5,Z9bh9US6rfL_fab[E3rUlB@ZLmX-799\VFQT52kjfdB?66-;ni!!9O-u,EA@<KWB]eh10
%X$&k]V6WH.:Wl.\Zk9#r+QRZ]!KS'Xb-VCo?SnV2`ihQ>PDl2mjt4Lu%lk5MN3e9ZWu$(3Y&!(UKD*(uP!ns'r_Xl+5s3+]YZ[5%
%Dl2->[Z.MtC=6T5NQ@[mjt?!&e.^ZWGfHqH'F*N6)*Cmn;jM&MG_m!Z=/CJsPM\AkcV@jmFhfhW+VlA`.H%=B_+kZ[3-p!g\=Sq$
%0Y;H&A)bC6+r>usf@eiZjDZHYk-8MT#0S2DR)iVScKDS?X$\2r8`+q'Pn?L(7jk=(+eY!=6n%kh>B6mHc%:/*ku+IDM)BEe6s1f@
%N^f+QUj*[":)5nPbDAnZWjNn^;KWaQ(5IslOFOVB#1RpPo>K3-iHsq^AR[7!i2*Y^Jb`o7!jZ$m$/&6[HGKb%Z>[E.Qr0YIQ>[Lp
%FjqIqgde4Xapo(/etL19bF15A4eHG;54!0n9gd6QZ`PT>*"rFj#D9c:&;bP%K"j#=Kj5cSI%H/-fY-k6:u`Qmf$0`/H=3^KDC1[_
%8-HUg$Cn^_Lr:MgLoFJeROoh-SHP?oUU:j):NkS'7RR^YpJ!>PKP*8-dhG-uFi%&PKSah&KCY]Z+Ntm1Ot0KHDh<go-rbDH>sM,K
%(iU[o(Ls7W>EC.@+'dAU];R\%Zfp'l2k(d)l.oahKB+L^JuFICH^X%sM8W+oOHrq[M];Cg<.;]@N)\i6lK:,tJr"<T562%,Ea,'(
%&hhW71?Hqe.nE+!769/HHUd7@jJ7rA&<N4@[l?mWN'p'k+V#j_\#KO?gQT]Mh-Os(J'71.b02W-AWQd4"rdYGnRE0A;CG:lFr&/t
%`XK8TL7ud3Ot*&d^+fg]mLm/=*7/H,(g"PsJ>LQ0Qr&n6`pm-]CeC9^lN3l'b*m&U?*YCD)dIMZ53IWr10U83UpOTO8/MWUph\CB
%IK#bcpPM9NB`7^=K6?^@8l"/Vr"hGQFUZJofSC)UdE(TNpp,tl-cgX'(?$C2X\q/GH6AdsU5B7/g)-36nOdih?@<$I=9^TI=3kE-
%$:.eh<S0@k)9D?K77-0DLmhiM50F3m#4h2?`Ba$$?ALjJSSfDPHG:"QJVr]^1kOPZ&J]cdf^L3`'a[6Y0+48FKL:mOUAC,r&fh(r
%*.1Mkba^e6Po`b!G_cOG+ea#c"hIFsb=[fTV'cUZ-`NgtlbE]0'V_q]``73'7JoVqV5*B(U[:,r>7\!;O[p[+8E.mV*a4jtfC;q*
%;9D=fNWgp7CX43L;J<]!_(,+/mG6kjktIFYV=O?#k_7[&]m6-)+[^(^mUd7C+kcA$l_DI$NcEIl/)MO<OURMI$Mh4H2.ZPcYpO:Q
%PS&0rWP1q?9qqk_7Faus@TeBP-&>(J"W$?lF'9tZm96<_2FdppC2J26p":DY,eL0N([9J&=MZ8>@o/O)&\A$aN,dGem65W!-g2A`
%BZP6dN>31qmk)-TM&9[Z+E%KW$h,"QO_mEtbu0A3ZKt7i.I+^S'N#QRZ8'_YBW:*)iJ9]>"(RejknW9IYrF:((HJ<@qP*:@=dM]4
%Kp0_d,*Lm&\!<K6CrRCL;?0uoE_9IqPVJ9oWUbU6A-&L';6NN#WFIIo'lE'qX(Kbbp#B<h?4!_9kGW"631P%@X=[Gb*!D.Xip46p
%6*-@T3*AZpk@2!'M_ZEiYa*E=C>?*fqPmk90.@o(ksUV-_Q?EJUQd@Q@WZ#_*gkSjAibYln%kB'0Y9[&1ugkQ$N/!.,a1BJ/\p_s
%<>HY[@$MO'*`W[MkB<pU^L.^q-C`@);UCti4u;D=?/PffW\AH#T-EbN)655I'JN#Q%%hT9]@"r'7?MS.#/(<7Sut$Zg.W4Y3Yhb+
%C*F`^p4p@K&'Wl*F&'tboM3C;ARL\*cTR6Yam7s*RemGqg^o[Ed'Kb$1ks0=iqr(n#*pttMJ5&h9:JLbO]eV`Q;h`:+u+Ql(<e]i
%Dfq;tOi96dB;J-FJjLKr.*ns>U9Z'%8]ki,MaEIs-ZC=B*?%FRhF'g8,FU3KJkkA9a"\<<CAaGq*I4Kcm>!-mVr]1D*#@FIVhPCn
%4:W;D]aAZ41.NL.C7Q@L4tfTr>,<c\oZeE7]\/E<nt:'?7!>bL&$!B"*B"'.+)4:3En*b.+_r:.DB?:\5iPne9(0@[:+dP#U#U)b
%Y:[<;R#0c1r4oqa$DUj`=SVe-Eb,e<Ah;s%B;4M\0R[3VY&kEWf[3X*#I[J9;/\<n]UqMh&Q?%.TVaN&.?+hned/7@+*\qu]9ngG
%du)QY:Q-dr1]*L4XpK"R?#1q+cUiO=Q&*,oI*<k58@1/(Q,=p[8c@Y(M]d%u).Q7;'&*c0pSq#D`_b;ue%O7aPgSX1[Nko:mcLI)
%N9A<BSN&rHES0QlNA6?i+R@7rA""mp=S='M4bua(QT+Geeq83YA0m^9[6;UadN6J;_GUBY,,t:"l-8HZ1kd9o`P@1%8ss/pNeJq!
%qT\jsdS*c^VKsO_<C8N`b."\CMOhj77VP19<XTF$R:=:3iY!La^<lV>h)[\mllKKTRU&t)8KS>cHXVgUeON:'J9eF*ngk&+/"=Z"
%F+#-Im#&;V49t42,rW.D2jhsK\/8/VUl*=XS;?."2i',[Tl`CIE"'S(7)l/ao2ui/M(jh:MB5kOlU(5LQt2lCZ[>.P8!$lllm%:@
%L#E(P&\irlK]X:KIO+]g36aVfRR]^_C9_l;gsCp$gT%J/U(GiYE5bM=8bh_H2_i:a&>P4%!P??&$'CKNhG#m>]g<7peEb5$23KZT
%n>$3A@M2%;c][1$.iE*NPLa+GA<3eb^d[a'K3D[YM/)-sHa'I.E01C+B[mCYm)5W2&lpksTT8LncIP@FA'8sJ1p4o)Q,kV[&=_=@
%W>J/!Bo[bNZ<=U<@j`cY=eV2g?PqH,H<6gA,r4;7*Lu?=-1i='\OJWnpa(?Dk"U[)hRL8f;@AuIO]QH$M(QFjQSUeHZonU=>0ReA
%!#0!fX^Eg%e>eHn5g5Y[7DYga1EEHj:P^<fWX-Cra&D[u`2L1"%9$BrY`#Jek#UO\db?7O59[t]s4H;]Ch+Or\.lYK0e9+/VDJss
%RZ(KF39)tuV5k(E9h-QiT85O&*t]g1W/Wg5=boY^'L['*O!YB2m]&#h'cu#(-rWdYpI'2RDn0S;oPBpof#OeR&/e*(M,Fh94\H3T
%_F,.Qc/UX6=_3a)^Ec4MFj<CYQ:TTW*8#(3>un+A>H.Y=Y!5BAAYlgcGO\N[FUQT$BE5Rh$f?3+2gBcDZ>seRF_Gt%2_[Xo9nHX3
%Tn0@?h/T5-#,Z$dU_`J*d`dS\%o`EZ@=kTAQctDA,!oCHXX;p`K']FF$tte,_S<:=j',VmUu-NB(DRQ97@\$4m`QnQ"[ns"\>_,s
%A`rF.GQunVg`ehM<alXcU@p;*8W2B+h9Pe^#D-M^&[M%B3)V*gkJ+XCqSSfoX./^]5JMH:F:M5Qs5h9cqVBd$Dr/T#Ieo]:p7(Z3
%dFF;-oIoq4dD(_Ap"=8EIs:kfN;rn-q<?0"p!q0RDr:.^T,dh%DjM8Aiu_We!PUSoin_l4gRfMbHo1c?Rl;^=gRN_8Ie!9ZfbLt%
%8,r/K=#]^ml+_,MrVDKQnYauGr:en/bqH9X?+h<NpuV2+IWk?:T8kR0rV?.?rd&VT:4Mg\:ZamEc>VQ'd[1*E\Et-^Xk#_od4Be;
%n"9X>ip3dC554N<hYHIHZ_Bq$RZ.-cq^:q$L,jXnhc"dd,d()@g[:rsh3LW[Fa!cIYAQ8B":b*P3o?IB^A2N]Cp`$E[uGXiG[bpt
%6<q49r;Gm)a^<B&Ye[[:Y4hVPh4$(S5HQkWDr4i:3Nd&*1BoVqaS/KnMj(V_^Njnos5';PSSmDBGM;M#E`^kbhONJn5'G>>$,5ME
%Ilb?sF:N5shW7%NG;0m.m/>Cs?bLYif(hNcCB%S7I_SQup8@Z0CDlYdYPfO6L\%ReGiLC7Lg'32I!p>>m`k"L^V)Z'qQ'5:/Ml*;
%U#!uVd/bk`\Gl?kc,n6'Rgi;a9m:tl?<poeIJrm;B%qJ(8>BSKjS]2UQX?;3H1]3EQ.mfh/fdp%K.;(Ajo4-$r%M*\%8>cSb3`At
%gL-7-BQF!JaaO!IA6ahHhd)kh4m6q``:OPNgd.8gk]0I&QQ',R0@0WDG4Ffu-a0_gGnt:u(Z?6F452*en+oe*?M_sEl237-*I0Bm
%5k5??b731[gRHg03rA7=$&rS]i4Lu*e\&W+Gk]i`oabeV]9EBrmH!*X\*#,?^O1EPq^jaF3,Yq/YkH[1jR$16YiUjm]u*?`g\'3G
%mWJKiQZ(2f^>h+6iOf)9T%fIAjVqap[pPbu^:Q@MoBQ2;mENc'Q@6=DPF[OtDYjQdrO8u6YKf=FK(CA%Rc"@+RhUm.H@9f3k$Pn!
%l>af7QD!a7`>/Q?[bK?n2FOscIJ;mIPLHj6:J\DnkZ79W:]H3I6+>eHmiK(orre(jDqoF_`8gm1Im*dD_c$EY%eY._D&W:hT#dC5
%Z/%UK9,Zb#fB63oa4lOa5Q>uGpf&?L@srNlUsS^aG(I@$$uG(DHiKgf9V9+LDp;50Gs9HqZ#:UiKA,p`/_BtT41t5*T!lBr2U>TK
%n2jUq2U!=^R7#cuI<`sIF__KO$*^BVn/=JL5P`Ejo:)p9cYg0bk"37G([!`,3&uK<hK!i\T;E8!+dUsq\U;$Ed,1G.q=>K+jHGgO
%[U6E)\K;o9'Se*/6cR-$`A5-<^:sC(S$TI$fjLc9s$3GUnKrU$.GQV46QFQ>FZTL5a(/uUE^LK=S!2Ien$]Gd1u,3^p0=>t?CtSp
%D"5#!.Eu\H`]TZJROs<]"!6-d9"ihp^AdEoiqgt?T$oi%6_5)54#!j$AR<\3*H4[6dXc]BF7c!c&52j.Gh*ZMHq$opIXf\S9Y7sq
%:H/])dl-Ri`Rk_Qq;ejY^Xs_b5*a=Z[`H4=h#8C!DZAR"XJbXY4E5n'5Ai/<^\[6VlK[`qe$Ft=dqsE`rL`HAW;U1XHf2g)GQ0&&
%RbqWF8Lr-AR[p\C["L#)@K-F[SqT#q`J.mqA0dd!PBeX]c*,FMIXQ3VH?ORWFn=@OYFjm>j,WW$UO.Le>CXA:htYMLU)Y*>oc`+;
%[]mRVojl^ljiQ_(6^YpCOJop,#gQ`H\6"nfqV1JmSc.\hS+^=KYPOR7lmIYaZ,'90A6@KMaaE!TIc#%tL4f?aS*S6L&UN5K=*qJd
%.rVT"]L1;_S&:^O(/EaOPEXV%cYq,/]&d4g9m8Iq%=E&a)F"-^T#CNhGJ!h!No+>5o]fl?![]1pFu>8[l`2VXn+?8.jh'u;5.J!d
%9B0;Plf.j-lsrIQ_s,:^l#3c7"K!hHG;UhYiQ-F5(#65W,,F%tVK-_Ic,mF6VDA@0S!,cM713h?X!rJ-0h2)ao8ANDiU7REegsS!
%c7_+^q4E/<&Ng/'[MS<BM6[p)g?E[97+j>`VddAb_N?79[UJ&H`YQq/-PqY\qRf-l%M]'VYpas.3T"k`T$#N'LdcSs?Oj=hs/c$5
%:^s!e/BT<:YlCsVcU+3bI87$q>:/r@?CPRFMrK3dYpc(OiSLnp6S-Bo9%8VuPeH$oS+^=KYPN="76,ckYp`g'o2KW+pZ\N"MD6uX
%p@8<q`\.#VBnPScMXW[n`7%*/GWHf6;+In7&NhTA_I*6<kJ(IgQ-XY^@G&'.fO?R].S)eM%8cn2U]0VEZ$mP4b^lsG#[](e^:%GV
%%XdZZf+*2q8as,p^DW8g\98G8p6i%Rd31Yl[0'M-0WoR1edfRNJZBg$g2l4fX%2CF*Vd,<D+QW:_*@4JI8UQj__q;[CK(hrBYtH&
%Y0l2Hh)[@F*T[aXLbe3ZD@/p>g?qQWqn^e>qSW-dc7]*8EUrM[432^&\$LI2p%n$uo;foI<SiUdqYKR>k#\$+.$h<Rg?RjXL%7F1
%o(7K0r"`Cl!trbZVY0Xu_ErPJ`"#<d#:,Q&NK[^U5K]St!@3ocYdYkW8T70`O8jAnj8XN_rZ;+>-fZ;/5#.-]mPL"96%%?EdHGij
%.f:YB]"<gFh,J(caTV-PS'DO!N;mV%?1pH,H/eq[GBW]D!5Ad%d;C^'XtRQ(IQR2khnOMs:]5qDCmU+='k[1c\\JD2d9j=er4hUf
%hJY)MroLD1hO9)SU$0-t%Zc<!h3p^dmsTH)[s)TtBm4Xn5fn9>ho$&c]mKc;T"I(uq<IP;ArFUuADbIh"MXX^*m(>DM"nd!4_4C0
%1Aq^^hh1t'6YC\fs18Bk_0nDg#QO8/s(df[pjWhRhtKn3:^Q1?s#p.,s$>n34M\d]Z;HcIpjDhcZF/kMTS(Sbi:B,,Hp:a4g`V$:
%J,/3<[qDnIYK"b1[H3A'qo,]JgUCf3(Y@QWIu5'*J,\_F1jF"4!1Y!?GkVG);]iGgL/0`PU#0Uu1T=db.t@<UbG"BBS_!2jHd;N^
%rMA'1rJVeobqhC_a:8uk]s<`:HF]VuRI!a(p0]4/R)ohMZKGpK'0YH@5JK3?]D?&/M.`H>L\Uenf:W!6r;,F>k*%`:Y<TRHF)prP
%?i+4qgBHZ`#&EAS`V<dZm7g7.S\;@H_jjL$kPXo,c!r%)DnSkY\RRG?YKkf4ED46t:1I4O-&T^I8o7ApfPda6qY'6]dijpfq#I2;
%rL`FOf\A[,?iTNZ+jtec"QrR(pNA`t`]ad"fYMsBfB+=5K:V.Rll/kd(g[],Q\48Y@!MM*;g:"/*"4J*_ok:6m#mc*$nl@L@Je6a
%n?U(QkeZcZ+bl?>M+>l>c=_&(8=upQoV='0cRl!eK/D;qf?^'0g;I;7*'P-*GhQO=>6"`u*Gf0m:rC#"9l/L<"5(OU"etDaX8(JM
%GW]3+M2Utp5Fe6.lGGYFNdL].m/,gf5[lc:N]U.FpATWuc?Be;j0&.?JACfi&=>Xh?Ou?n#P=iu*Rc)=:=5tHS\2]9k"ql9\Rjl@
%5b0rHjmS1"nDr'O2\_-sr>6'Y's$u9h&WULF4a<+PoK,bp"4WZFoCEgDpN=b2S;%s]oaU*[6+D8T&U>Y#P`XhpN\4jqmJ:&d<4r#
%2W^U**Ak/%e'PgA(tkJ(k(V,GH2f_0aB1M<Z*mA9Mm0f\=k?0Q@1lq<D2Z$4L-(l"g+o4;c<kJsim`,V42r=T!p*h1GX=I9PIG&/
%B4/d415CK&@rF6\Vfq56G)V%oA)DIl@)k7Z0b/Ac4gSYRS88O2HL?7Ee*f<hAT+i0Y23I`rq,@6YFBQRf>bFkeEI/VgQR3>FY>\h
%nU,?Ip?X_VcMl_5Z`DP,.`%\L]:.L)oqsb32lD_iEc0P7W84)k0/aif>HN*,\M)j1N/MZF]Bf&?q'bgGpWh!ohqO(^gtDZ_"rc46
%C;,(:ICVdMiF]]kabFOW2qM7dh"C)InN;Ap[_,U9+8>""qt0(V*FPL'cD1qP8K1XmMp6joa"_mf#].l.1Aq(7h4$(S>n$H(P`::@
%#Q81s<5%aTKT?FcZ:e-`LF%3d%&NDW.6eY:l"%,m5F1.'P_-WX7-<i]aOc_,m!HO7HX6_[S#5ep#l**bn+#I4nMc(*4tC-U4':O8
%Uc&/6I6,\=@#Np=I!X0$m`A#3eE+./]7W"cd,khlglp:N2M_><D/MS8E7!b@D`U)kD/KF__*ORF[5PL&P7Gajr[Dn12mhkCc5kEF
%K0"3Qk76e41KN]<lh^JNTAQ/T\J]atbJ!Dk5b$LdM1+"=o(1;m2GN@iU!Gm2C()%\T*$4RmWI$=$1q#h%,@&ePrR]3*cNEs+KiWm
%-hi"UfCoB/YoA>K`DCcSde^_-EH-$qQVWWpFT(mSl1rT5s16;dqY'(+M_15OD7?Qe\$;V(PIX1&kOVZUd;S7,4ipq#pYj'W]odC%
%q:bS`o\%c#IFci_>D>Lnot5P(Z`f^noeg)^?ZA6t/R$$I?gR'nIIl!dSG`gI%0#7uf]"s.]0Frur&.X/qXjDjP8JMEor_o7+kGrG
%]/O)gS=XSgf'VT)"nj%,`o_TZ:Zbs:?:"@S".bYccqn?kj*to&%khdD`ZjFIG&m.<kEkWq3Adi!Y3*VtcOMXS5*l!9X)jR=NjlR>
%If;&,_G&liTD%Ftc(Q9H#DdHqJ$QE(]R`:U[(lUpf@O):+#Lh0PCpYs&FFpsU>6&SMuHgdfDEQe%mn[m&;grQdJ#hd:R*"aBqab$
%"Xnj3PMZ0akLj#-pYlWF(qUmDRLKf'?aa5>^[1I`Dc$!'nlVAcF7?f2_j5^q$pNq,*9)FniN`08>B<?IS&9E]kPi5RcRi2ljN[MF
%=-mQhat>fUp[R8@jprj&O4n.TO5h9G/GoE+DIL]br]"fKLVr/^B6*n`GTaKeh8A!Ws*OBjI2efQ"o1u2E*n<jO2D[(c@1/j;*;r6
%n[qJ55Kd5KmfcC1MD8cLPZ\d5&&:RqNWUu_pV?ZdSGq]F^Pqa_4l\c9S4>D`$B/unT<<Y_^OH&#5sS5.a^:WumPa"h4=)Vrkrc`Z
%Y)2O)Rs86@hfFMHq"P_H]=^L,*[B^aEra=,YsAHKH^'JMB4]f=FFO6cb(ng7m=2tq'7\@%498g2#KWK`ItO=uJ/u05kf!GaUENQ#
%$V/;PfUKRh4+9hFoHu(`Y8akmS+*E5U*hTj5Go<RcIuo<T"XsEq4!FUb^#(@iGc0?2fj'2`PpM[DRD^cC;$\(k!;*_'A%0Z?(Lub
%8[=JFouJ-oNnMlAB)hQ]O-r3dZ_qofI_!qc&6<d!ci-^Y:9NH&\YYJ+*hN0<:,pmQX2g";BmVLV5"ieKS@[NKdKe7A6bC5ZDnifl
%3+t:$ms.'L=FQbObE"K3%kN>)P@+7"GG"p3lEq?o@Tk,,kJ+XCqSVNN+_k=3rKh/H5/1;$ji9U^h:LkGs7+QV0>G$2+K*ia2a@2E
%48QpWJ)lVRqrYp`H?]8d\te03J%:n/Q.Gj-UZIdCksg:@E[$Zj]t$O\,k#M/qWrlaK7KZA^8:l&^W'X\4_W^mV5)ohrLa,UNa@dm
%IeLf(s6)mgahOpk`M;.%I2R'D8!fl]*s9p^bL1/g,%>d7=tW4%02q1'9OhJe'@FL822.f(mNIW4C7u]+J*G"`M,mu\*3!cnD-^pI
%d]g,W#;Lrf1#J$u8I+3cnX2Foj^fMa5Tf;F9<:N#A'(_DU]6S;[[3bu6uC]B/TA`Z^8s%J,,U^[eV;(Xb_YL?HWP>*DG:(d5N0Xf
%NrCMFa$bi`NcQ7?DHMZ2I$:P[O9uS0Ii/=:ar"W'3XZjeo<c<0^8rVRg\*DLKZj.VY&17R2jQ2=icQ+Dh%_8tjbUV8VgS5E2CAV\
%1UsoX@fTkrU\FRl/iU8mk>+;AkmQ2d=*n(&__6N8Mpp.!Q[cZ^R>0RjHqr;Oo5TjuHLKXVof.B%Vs='pee[iOA#u6]58kP0VY"jP
%WNcstQd-tBL->gs;j*j;2#]edgXtN5;jGaST66SW`_<c=1:O?koSH@?)p;4#2eACj93S4G2a<V(B0BD>BBl.L[1BS:]T8Uep.-R?
%i4-1]^%<H>0ueZa/9EiM=/HS=EJf()`MO<:kU*Yj#*^m'96rVP()^Eb[HhgPfgV<!mJbMhH>I@=49LY8QNrQ%K1GFYiu5S*fp;/0
%k-<A-)qRD;X[+S`fZ;PMo)_.ZEpWQb/MCH`UAJV`CKF[F)71S<0Sj`<)BHZP-Zn*g[W+!X,9b(HbbQTFFlhi'ZZQ%,TD3D:##k'1
%:rfU,#o\^bR"?]Wlf+:<Ud#LjKd0>t1/pH3nRX8(>:E4A*D85E<lXfg%1KAcCA#F?>_l#<`R,%b1cX,i,<_ap:@9hrmJUPoi.ZFB
%p,mHG*k[UGXn\[GoAE)%ZKkqHfC@X2Go=WEl4?Rj<Gtg9q<5ZVrI/th5CWO*Ie;*]*I[WgndL"$Ci?s^/\3.<1#A+M1GNM*44EfZ
%..SFjn[bMGgH<&C8,jNeW'u/KM-e4nFSj7kl&?S4\?^*.MIX;kful_0VJ>;SkfHgE#Hj*=Q"UJ>58oRV\)IRP07E7g%]g2dCZD!R
%&C*40Bd.#+02iDle]*H*#q)%aA+=TBI*YY-E0+^X&E=VPOaO3fA2&fgN<MA^,^]nj0>qPsJu#R4U3jqe\A,u>oDA9+Lgglilk5Z\
%JQ6r.]cLD51A3YK4<[:@Fid!s8PO)9eU',TH0d!GoqXa7`>p&bSPKO!W-iC$[4JJb^bb\.n-F,tR!$foQO7nKI*K&MJlIPd2eTcN
%Y5'VXgM#=6pm*'p%1[S-:JZsjE!Fo5G@*CSH/'B&lbjR,V3(c?o\e5=N/J$gV0ffkA#;AHd76f'7Qa`"nOei'$`+T/#-hT^#X^QE
%24h3!a[K@)EVBNfLEmg,O+"qilU-i"6Pa_unY6]jDF.,r3Rp*OQ+?0&k+K$?6c\i(1]?7rJ+0K)3s-H1RiKO-h)RJ1EXh5sVK)0o
%hQ&3_37"0Bs6bVZd,d8*LOZBE[gXq1Z%0bJk^3c*4C/M7)d3S8XNFV$=Qs3KYpR'd6[+>h4&2p_*C?kcaq/l9(`04b9[%V#[0P^N
%OWW_jMm^WNLYal=NJQWn<&J/:6h;j$ofQhEVY^_Dj6/L7qKTKGn[KRQAs!28r=2HD53j`WLNJ&$N2%$\Yk9a/W,"jh7:br*abm`=
%h5s'\0,j(Ij'+R!Te6M_btXL'B_mIIBL/+@KB3V"0pK3WM,^[W!_s<cPEmA&Oe2L_BTH];4=be)4I7s-0mC/qj0arB_[YTBH]c30
%p*M8odrNMf2%q1"JIRG\b(DQ([qm^W$Jga>T7l[[_W98p6VAV<O+$Lm=4%JV\S'UPZ2[pZ3sYV/(-_cDk9dP5`eU_EXgQb-G@od(
%p/.1pnP.=\LPS5roW^qmLuWT%#7`\[n)-6Q;8[c`6:sqI(ICMn3\rtkb)GF;HN7F<(kp17J/>lI.6G8B6gX-500OX1+(P`i16@8L
%kHUS*E6fUh+:k$?#LD5$LNFqc)4$OmKVqmt\bL6q<tXjbrlgs(4'IZfmH6VO.[E]c4uRAGnJ:?+mu*fn[-P2Km<)a"DN."[mP[%#
%Ct``@*^kf5FD7)Bh?TO3aUR)[0E7-tZO!)]=3]^ONeQ!hi[Qr^aUjn3)k'1[HM73^7?oY4\a4r.,_dSml.@@FO>J_Y1HM#%E27RC
%b&gRDV7Cdt^2Lp1hk_[VBB5EZ5C;kIZWV=EL[>Q75P2sG]I4um(MG*QCdE"Z(sL(0]Fm1mC5,PBBi$'`rd3gLM*,]VIFTQ7boRWG
%E79b,?bY^a,r.)K4;%-C[/QQh_j<!>Er]901=!=C5*`L"\=om"J*8A,oA98Z%J0C<o[du>^ouPKrci/P:>\S.U:8hJft@<FYah7F
%W?0=oG0W;7U>XtkYfVsR*,b,rPTrLXX3%A+_Cc"uSrh#*Y4DB7=9a0,q=/li=3,*p`B'HA/$F:-hkO>73O<i95?IOjSj+`F[hm6Q
%?kef_J(<n*`>qV5iYOb*5%jF-k2ORQ3C<V+=Si_7('@5jYYFCgYD_]tG&UZ0DAga]%N_m-0ag,70V-4D$*."."X?Z;NWN55p1G/o
%OdA0o1#E<HkLDdToEq[k>X#J$O1Z1_9_G@K'I+km`HQp/i44OQ7pr6t;u1?iE,M'-LKE37ECLibp,E;^PeG/@/;daqXE(JSmZ302
%X43l6L4O8]&LdI"6iU.',A2en\Oos*3K@7U=8$,]+95d#-h]tr]SY5"a2Z2?oO)!SA@OJ_RC0rNdC+a!R+D1;pKLu$]dIEN0F'ra
%]MYKtc79m(@srP!*;OULiq3#K68%?SS8kU[D;cnD`=3ZaU7LPLNr5-P6eA-0S,KlucXE2hePEC,K&Ba<+(*V:TID5W>jU*LX+VM`
%=94LVh7]0a&>iGl2D1#o9t?c9=X$[dZeK;Q_!X40kB"5kpItDX6&#-#G(!:"^U_D"GTbj&3F=gYD]d([&+>i\H7I_4k9k;.%mGX"
%$Ecbnj0JjX<tV\99T=NDi\sm:)jeI/DN].//AWSL0aF\4EKWWh^3pO)H"jp:]tA>"rIrH,CrnNVi$9C7AY**G1S79lWDTc2di.Cc
%5k)$Rr:Dgqs!+)sZfU?YPPctM"T4<a.lq!VMu=q9dQdbC6S!XiruVu]?X-r,%3a%YhOh=]s4?jYl#/]]dD5e.O*k^B>P?NY%bcoC
%^#@2Kl1TPI7m-7%9D`ru%Es.uGWU>i_gBiOI+*"]bK'OCpJX(eSBaG]>N&\2ge[+(c?GoF^]0Ge*r0AZgFU&?jucl3:@4I)V3j\)
%g*CXlF0Ug(Ps:!NFXG-0Nh%1K8[p+EC`S6erY32&`os]\*Ea][ggF?;Qh)0n\As(-0<\D^0K%c#N50:;AaK[m55C-ZGC$)sc;h@G
%4\&f%0_EXm6]n$O5b,<kp)#$8(Z?H'".jp(5C#4TMKnrI?^rKfh7A;5HLX%.dH,aF>5J-Bhs=-g%6#nUeQIXq]6[=5FZUUaj&Qe9
%hfhTmXOmN'$7JCGj1*6Z41`*C`IRbHHLak*gq3&-7rDGG;'fHB`+)Oa[ID4]Z?-:Z2DRfT%.R@($eSRD24n=;@>*oVl[:$hC?r3g
%cX#'9Q%seDQ>^Xf8cBE2(H9s1K)4[]Nps!C4*!M(TfFU37M!\uBVk<A(V0^XHO[[Cj*&0gaNJdT[RrdPk0`#%5c0"i\16>@UWBtA
%BHnMU&2o1\@E1hSa8F^"H9E2JWPfebSo]p;"_+G<6kC<rLlUW]Yn;-f>E6+\95Fam(D2X*-?*2@r9;[cT*bEdpp(K<jQCALMVMji
%@Geh9K_'0+%WaJi`L`.K5+$P%gc>7qI=S9UgcbAINi6DHJ?Sk.ANcX2pjA3QgjS\PTa]l$aFnV(oD=W9`2r'Tq-XJ,^O?gr4E4nH
%hTH;UY%cO>P@O$("64US#=f4bU:>upI.Q^PjAC9I00k%7K.C57!l?!5p1"*0&45XF)\>=,#fgTn0^6ZJUQ=e),2gXV,5TPEpoB5E
%If<L-LV.Q&%Zg.%&)#XcdTgF3T:IsS@uj6L(?QtSISToPT(0m_HB!VPb2UIZTkZ_p")s8>aiIaRi$7,ObYtH"jG#$e^Gr]@0>j:&
%p#b!-o6Ut>n+qBIH7a:se%Bb+Zb59h`M&3a"$Y[CDiYH(4-oF1`I5u)I4(mO<udTa(DjtcNV=7C1TJ>lkMP9PZkDl4]%6eQF8+f7
%IHr`)H1_,7*qXrY78<hfoprP7^5(oW7B&b@cbUO8MKA2=_ES6(@hf3s+eQah&,bRI>OhX2T4.t&0?t"J9cFY(jt4rfgjb8mi#p%L
%3L]OT7%YmS31XP2kLS,ZqUaTEG9#qG^>.E9p;nDk_WN`FUDrmHD`APq]mN.c^;&@Afpn08cZ0"0C.o[-ocLB<Pa22C#bqSJi0/`l
%pg_'c4f<jN2Y`la8W"2+9T4AQV3d:*M@NI8FA9n=E&=)@.9e1_o5T98=+<@Y5OdQKs.%->%mIB/XuL[4#iQ;DO(-M_lO[<Mn>0$7
%S3F,&JG6@7YKed2NC;fOrhW&^s(Gs&Od"(Hrpp1#dNn0ds/H]`kH>%FNQZ)s_4C_OGlN)T48+XK5JO:LI<jjcLlXa[r8#^!Sf3Oe
%qtWIN!p*egT>*[oa=k=(LYr'iQbWOMDgi3`eotb^6^^37,to8^[RVA!(Co&hs24YR"@^XrO8o54rf<*30k;:CaiHjIr-N=ao4L;N
%ZF=cOO+5!RpuT9cT>k5M4+-p\^]/++r&5=ar%4[Jjf!FV87NF.Sma10JJgGYY9_c,a&"8!5P?Z-"69CXJ,Rq>bh.P4lX/:uk?;^.
%g]Ko77%NcPfU[-H&3`(^o`OZL*$H!X81@%?K%KYj8&-[B+9-0L,Q@Qn/O=EZdeTejo=JB0no$_ii]"XCJNs2,\%hdYLQDHe37U4D
%B'4V.b^I[8fg'k5+7I(2-Ncsi#9Xke?L!Jn5?<Btk:U48_YGiEFD$M]pP&-eJrcA@#=L.snfoa^HGRecRGqdi0$h^:VRuZp4]'FN
%:%#&,-S2.)Cu^NFs7.68cZO>u!9n0c>4]r".sT/r$,4J6RkJjF\LGRDG$3-A<jq/CXIkRR'<G.no2c3"W`:b2n"5qLT=dk1I#jUi
%l(`+Hm@,>!qqW'Crs?CGpgV(8iIDZH'Am@7GYeE&Y3UB+4H.$_IF$(XlV?W6O-m1R599\MfQqMM3\8oDGZ(^$"69S[bQKUF\!NhO
%@gP#0ag2nh>HEf*X1G&7lLXOqMQN4rSAC8(!!Pl+5/%Hb4FA#kc#9/6Ho3#?(phQ@@mH$%pP&-eJraZ*kMdfZ\ag>kr;OD=$RYC!
%kP45,Qeqq!iT9#*]^.Cgb3keuG@)3e0E"`RXj[T-7BgBX_o$9e>`nt%#.V;0_4AnN!)6'.DR-%PcY@#AlV_4Kc>8^SQ2C(WrTOtA
%15U::EuBoP]&`JAJ*>)IO)sf@-)s']0F7`ArVjN(:D&NOpDrelR=KFmHb\Hq=t_/rCiQP\3'JGa-hsY..%Mufj":)biFNpM54+uT
%?XCGY(r_hk[B5\>3jr%,m8Ng$r@e1B'1g*e1eIoc1<&*;Tj5`@eu_mnUJLSoH/2peN2>(A?@68XT478_i/8J<KP5at)Y.H/pH;Q.
%9mL&/c+>@,*HIIR:54V8XRkkncc6!Ap)_dP[aL$q52KkUU'L/8n&<6:Z/U.qq7?A=rkTYFjWj<]kWco7>7PT;RR*L+QDpKjmu5\,
%(Al*,Tu:rT[MHDrpo;X]CWu$NO,a&7kMuRFbEU2\H#4(nl/Xg17B__Q)7e)VeVQ]VX7r.LZbt&lJPSu\o`;>h4rsU"flnu*3:D_Q
%D@$^9]F#nIHl)Vtcf%^r":n"Kd;B=(I2Z/%g4&dMiW%?`P\??2.n?GQ=OB@qm$;[V2rr"&Cd$dY#p:a)X1W?1kip7,%-Yik7JcY&
%I]d*iHf1&1beM'Tr-\,YRXjth/ZMV[9NbW,(smiAiml**VruTu=nVB-55VT,S,SnB!$4P%b'.20s!RRiPj&5mbElX&lQsP:W5s6n
%VmN(_+60-Z^\d6jFo2=!b$Su`:WEEK<f0<Lg12FEI@1j29FKA,4E_F^U%S?McSfPFN#`<4V"ITXkG.QsJra@f!8!4=g!0!C>&M&J
%]%[PGEpqWC?^/SLZ-BG?r-\FWr($,]X:r5tSaSO9JDX*?YWUh?jbOm8TK\E$Rk3/3S#h:Hp=\Xo_&"iYf3X&W0B['A:Lf%2P(Xa%
%Co[+&2Ej"c59KkPp`u(WCGiW>DMTM<;bH]3H(!7\GhlfHUrDrO%bj-I2'.6Wpn-K6OPoQJa_6H`j%Q>D*$/+l)\3m.;fha(B7mJQ
%8K05?<hGHPQi<`W8S"<YQ%/>Cn,-(^)V=I0oj4_Dl-?qPr&)Q&I^[:u5Ob@;EkI[i..)Mp?0FJsA/nem[HuSG%(q>[2XW5g?gddP
%It'Z@B^qk"lMC/K<Dh.rUA=!JT>1Et`rGiFN;q,Ka5c(4b$T8>INdM<[QXBf$&L;=]&*qjr7<K4-KE9%R_I2^KH_u2or!Ro)Tu8[
%Y`4kHan*R369T.7GoX0hk7%C^*uOugjus^`T30>[.EqVl-fKudg_\<!gT18GY5$F[pAVt1$'`N%]p?Xfo)/%uH6XtQ7Yi">iD6*'
%gT5;=o<FbaKu<rgLPC;LTU4X;M8fBI]V@,,:;IQ`/<FcWE5]f_.@Knh2_tj;MGN/KW.,rhjS[`Hc1Q"(nb_5Dl?C"rJ+qpRmsk"S
%msjldf>%+d]Ya^JX$?EXms@8ps0)I[s81R.rVk[XKAlk.+8Y0Hr9^]eiRe,*pV.q\i8r)P)F+8hDa/V3gi#IWr,;T(lX-)`o"Qpl
%r4d[Tm<S;ArpKNqqsAko,Q6(RL&^XTU'2lGaZ=Jp"":):2:#8;PUE+)+Gsb2D)$`a44!VTK_[m55JUe4b7jG'g(&FnNJot-N6_5c
%Z)l-7#-a:]=?FkLL.)UMW(&b$;,6jo@R*FdJ9^/mJ4P5gR]9:8Cc/isiJ`G\4<uQ]3fcDLakd6J*?uh.#S4M6H>3"ZK,I5=G(1H>
%.$,a'3h_-XlgCEtg&[7'm2H#=UJA=BF,^n_f-:EN7Uj(+!X#0NKofZ-+dY5q]%%%qCGb'B+S!k,jT?1lF%.)_,':nN-u$PA]KtEV
%i=f9k#!liAGd]*+9oI0?%[sB!!gGKA^>Aq_>R>SJU-duPhE6M37f?J.6pmN!6.Dqb??ULXlgd+#asr$=5_uE%]ebL3Zm&Dj.Y?nt
%ajXi293Wg7\K_*L^oDZ8qpCj_0QkC:cJ-jgc.'o_D6'g2eHi+Jdl+Dl?IB*jMTI88c/KUGZq.@2\k@&"mAqETRJ>k.O'%U=Y]=p+
%RehiVdVdPdT)B6`;&s1=Mg1PHA_3`1PHa8b.cQtM&6pfQV6sYGU(RBpZO4GigKG3rZSi)0MVOjb/'I=^?o(R<[]*o/""b(m8U`_e
%&!I]<i5g(i2A6+lquee7H0:IOn*PfoZp(K<Z8aQH#VGN./m8^MP[n`'8QZa,cR;D%0u-ohg'T1)h.(sZmEYcERab!$fgc+s_(_<\
%%G=<4`?75V$pZ*;Li]@1pK0k@&/A+ke<c)7[S<BIAp6Lkb_QU^@_!.tepm;dS2JR4De[WI&9iMH$Xq\7&t9YjN$rp"`Ufp@=qq>9
%``-+@`oMMgGq85VmE2R)m"Km&$YuOg%]^osA^Ao=\RqR8nK:b[WWKktd%J.A5[kI(d2SSb[^?ScPu5L^bh,u2!TcD$8M!XO-g_%Q
%0WA+&rASTUZb`Yb>qp,g#AA[;`K0[s4lb_*);ggJi<j@/>t#X20C]<2QJoo!HV6>*:2atb`\IGU89T;&9Cej;j6+Y+L3hRRm7:[U
%(*uBA).AYo[YW"'V^E5IKED!,WLD,<GV9Ph2'1\Cc:i.R%LqtPjC@aU4*$[l;jaB!ZFESV=9[SH_O?*$LEF3tYXJF+K(kj0nMih%
%8Wa0".5!%0nU)EOF>UPcGpQ`Q+<E*13UF$ado-IU-;_i:5Hg`%Uh6@^=:C0m897n9ON$LPhmm+VpQ='n<]o`^GE,kLnRXI`<@'m$
%b:]JD7)>?D%Xu;>oX/d0OX2P>^dn128W%26`,a8<8`1\PNo133q:d=!g);-"<?K1CHM>aJDH8-hY2[-];\%&:-]uH:L2saab5bY9
%J4]2Y*j1,tF'MG65K9q5=MX?D[qV>-IMR!<-86B"W/IkJ.&!)f5Kb%4+Ibf;fQ.$([k>g[K%"Og@AIe#2d;3jXbiBb25>doR,T6]
%4I<0!5Hh;J'>U0L[RPf+a93_pp*u#`_a_NQhFa.uV"s$j^ps!lEW"\TrJ^48#RtU)RbZDnT&kY\Xg<2.bNJ4+/idB"FZlqdrL?0k
%<T8GKcQL7MFii2BPM&dNI+g36el*;4DcVuhEee3I.HJ!J=;D7fYRLBa$<uScb(YYkR$q6D[)atDR/tr6H[=<Rc:<oqeeQjC!#]bp
%^cYhW*'YO]g-422jP`/:V:lVf1=-EHQi!"5#]iaq.d1/]+#`;NN3T_0CV?Va\u0?"dkuheGNhFRiu]d]>$,pD&G)IH1IBjO,`U3Z
%ih)uoeo">A?AkW'/0UDP)R]>CU"qEK>8@;nj.c0-^;4`,%Uu>IIM=A8hm6F9#UCf:5HCEoR7bs/"!f5%[\4j(>r)eKk1rFMm5Hl=
%8!=ZB;!U)X`ih`/VU(,L+uAdE9maR36?5IT&hAZ_iY/SC;!u-6@4<;S'*9'5hjeg`1Ke&&$j!i:?XJPt<="XTdq&nJUDgR(*&?-n
%4>g%/ghMePH$@ZZlaa\kBKrai`=1ga2\MY$=gEOZ-95D"s"]R=m(!!N!ncSVFuB2(>`""LWSP3^A[CrBJ='CcK'Xl8dkHpUf"qN=
%Sle]D@6?2Cl*mUV43Cc?N_U3(UGW$FRl*pSh^[5uK)A%)kJ/rDSg1c"Je!b='^7tG1H!`k*A0]7!,?9%na%g__JE]NXu1X2&TF(/
%3[\Xsq"L9?/%AtU+oAg^YO\D@7Uc;X]8DElEH(r:lEC4SB`+Jep8=So,_6.^fI<qOI)j<0Y-&scRMgD@_DqrGMY(X!KbSRPmCF3+
%>GU\h4^eoCPcV+[#-R?g!$^!5Ao^]$\<DDTAOK@KG6QX5\1nq=#TBY^-'^)0V=)W]X.!Z$-=OfYW4&4PU^K^`bCEfDV@eWm2T3cA
%+j%bN,@e"("sAkIR?[I@jueMDYpBC@XVh=k^&^uSR5rnY4^MRh)ohukpu^*k:ekT>%X3S84$>ndq3>W51aE]Vh&O&,91(PFR*a[=
%QY#$WRHtj^%B(`uXE7(qA_$iQ'jJo4VC^QT;@pVj*r6]Rjm%Fn3R_kS+r2.`kuh$mq$e";![mOV!@;+.DDJ'aA%_PsdgTEW(j3;]
%#S91HPiiub,;<!mX:cF?_b^Y#;0:N;l!bQ6j*67(7A[s/6W(388+_Oi1["<EBd:+!SZ"hpADDGA1+hmY4L7'SC]tK./m,3NN!'Dj
%Te/D=$::^JKb"\*jQAF)nIA"X^EuPq$=.+%BOQ,"m>VFh('f%7^F`)U,8R\[,p@%cLT2HqfEl=;6@t"DA^)%e9-@EtKd+s%d2WI,
%OE.oSG%YW'])Gg&,!KJWP4?8Q^DfD5",YhgC=jPO0dY5PQ-Mk^P/5ca`FpIAch%Xi:5Y<9A2<%&FS$%\LY;@7B=j$JqZt7'<A=bS
%i%YBY(2A'+'>WDQ$k6!l^'D)@RCqLBj*2-dLpfQ3TRY,?NAANDFl\(j"hjB]T7V/MLl86Mbo"<[&Y"Kh4?8To9]m#uW1L=Z>McMP
%OR'cSE,!cEB9H(1:JMmZNrI?-%G>(@0.)F=UMYYfnd+!0Bcq$t]nF8Z9^mB&?@=<f>u6nhGu"%Z/k>P0Ih-T53XE$u8rl>6Ps)%m
%p\4-%/P$?URL^PhM<C!S`3-NR0)Dr#@gs!Pd4F>fnNMo+#,d%CZ)h00j2MooamS:(dg:Yg0"+9KhGJO,i[(J=i>7+^1Y#rCTihQX
%::3:S5R&m`o@;T`k+76-D_hl=fo?*[=^q4BNWE-BQfgVYJk:G<Ts3'L+9(Jpi'dSVEml=EKlp6eZM]_bFu$d/8?T81b=G+H%@%/#
%Zk+97H$JZJ/$.IPe6&P34+.X2!*%=C\meSoWm:!=/_lB`i2i$Gs6e@dWTI'X5'(NF_5;JUjA`g[kOQ\TYIUKVik&&/M`g:9`S4:C
%,bFcpADq]<McMJ->3W_Roq(@d=)3)IVfC(KQF0bWhjTXT\SiUbY&-m2oF66D$AC6O]nPTJA.]p'DC-E5.B(?kE"tS01tba)OB7ne
%qY!2]r4X[ISr2mGN:-ED>DZ*5Z;82sL,0^W-i/h\j!.)kbZfeHUqW5\.pf\0[';;EPN:/Qo$<3:6X8(5I&h!s1=\1uHV%3,Hnb#Y
%&i8,Tjjh'X>)sB@lt'S<8!Rt^EeM/d6TqHV'E%M9a7#u#*RgihnSBjI%kt7^hd"D:$@LPYUrA[ZYXB?f]CulBiguI_n\Lgu(pMT*
%*Vhn3A>(;,&bAfcYf1M0QJ*P7JEtc$H,6*:mt#S\q2!Pir=ehDbmVC]<([NIMdfE7`U9\SKK\#NP>%/sja2XR@OJ7m7@)$rc5OA_
%"gV)W(Cn&%UJjX\#$<;uI5+OdBPWgt7V9%<'^]qWifB1D$?eFb/JQ?oS?q6rb%b,XFqu@@2^fubLjDG`E$VM!)Lb$ZRtBsX4t#b\
%on](3+F17hLEkfAn7?\_fHR->&"&1<%9GQLbb]uJ<(`3^9O$JIN#`6*@7cSs:6;).3f>1f3F:VaOH7BUDXs3mfifWQSol(3I.*Zn
%>r^+$lK:n;H/Ds.Y:D1`kt-7.n[VpFH=B5r$.nhb94FQR_FQkajPkUcrDKU5?FV_\_a.jZdFZV(P8@Ht`P,rNH5/+f1G*Ve?ZPNP
%JY1J9N-g3AZ?K4FBgpZLI(rKR]bEDtR5]jn)^Qr.JAsWjW=^.J%L)RJTG'+YEP^'YFd!+>r`fF8(2_DkFNO!O!-98lW<kX]#qj"+
%_,BVj994fa,jZD+PTV9ilOc>\=uo9i`5V:E,@C[c6s$>jH$$EM;#O9/^k%F&W8!%YaflDF#5_Cb2P%pQ2$sl^(^M[8LD,m?=6LX(
%,fID=_j<s2WF;^mTelFQ%f_"Q"Q\'5rQ:3Nk,9BiHR?'Q#F-InJbrb$[)%i'J:?D%_U?rDME:llNEJ^&gWEm?3-Vh'8SZ*B<o+8L
%g3*^gOX!BG[jfbJQ4$W;K;9RW#`D6#7ofl7WW,08[Uc\@#_Xp?3%2+4Pq,[![5b"GaboR5Op;_5`[0TB4ao@PN.,qWJ0hcdD1Nc5
%XS$1V8T3u(:7-^@ftn[s\W_(iC,[Qu++*#pJP0nSJG,Qn#KqLf<9B"@Ec*Z')_C)`!0p]YPt,oB'lQ>MSr#!fX-&[P/^Nd-I7`WM
%KdG)n9[l\UIZP_:LFpsE0N'oGo<n%0Lp6C=+tkVPSaL0%0H@7'6#;)<D^ZH)iJD`F/A.Z(@h,"1-rel!n@#rL,d]I3>D5EX+n.eH
%(%JCd`)R=NP:OUeVnBMD&E_-W#!QX)Ru@K@r7H^VR3,2,c\!FbJq\_!Lc4M.1iqH&3$>OWhD9eU#,`\%^'5NjMq&f2_@26SiT#%R
%-^&\(USJpJ:0;V,c`lb0pPpqa-'&q.c2B!PY[1k#`VulI$2m\r2&0=69(/I0M^3c,EHPSa"h:RiS0Tni67+m"9MCVMV[)=@B:5K<
%!&ao-Y#W50]RhA2Lplc\^PQI@\X?";^4+":o!+\N,^lp\I+58Y\[NQ)4IZ$k_T!TgUCQs'D-Ecd;K'1kO;TV6PkuRjC0l]XNM,&Y
%8dUWpf<XgD(YVR)1r^KQ^!LEI=:d:)-K.fH>JdB3oisrn:MR@0!d+.)UORhR'N$31e":t,gX3FFOo\Pr%=n3'..EfCJ=4`'?<Mcu
%kk@rLTr]9A6drX4U-Pl^JQHu[#$a*'_#0D30rt),a&ZU*_E3@tkk)fY(rm<5;4SoMNlIF-1!4RtNb8hG&caMF@M6cr0@sGF-<<Dq
%N9R/"ka=bs6ouI\XsP/kgFf3%D#>R&cfA-V>N(\]!;_DlG/46O`>_Ac'RZ/jT+WVQS&D7L8#VjT+(?n`DT+JCj0K8t?@g)#7t#sE
%YIn[?3Xp0(LjO(kH@V$Wp@Kma%'J+"]UO1<95u>`>$[>mPR'dX4gml/3g-0r^$Gk&<k*"0[nV"F%57(\>[Q;.h.2kV$^3m]VKW9S
%i[TRN/0A,&go?'E\uN;T:P*Z1CuT>C4@I&B\j<Z:Mi`di7*$t;V-pWcejFm#b_M:@6+<IBV5XInAQo)D^te:G-c#PrjniAQ+.;HV
%T\sXT$B-[&Df7F%j1!P*$:=<P^Q3T#H-#A/4V^U.=@('+`:d@H/7L]1*Vc'<*P2"U9)EaSQB<RnlO[KK8XNlb?6EU(E#)gh)-<4[
%S?h5[>)AOL]CaXc8VZ_QMGnE-/Q*1L`YmS6qB?N-\,S0#@]SOJ2+H"jB2CqP#<EMAIL>'*MYJ@5Vs)3j/+0B-KJ1S$Z?!?&%`89
%3%N1ip(qcg#Kp0p3SI!20reauhndq83LA?RGg:X/-=7Q%1:'iGck``gK^Ye5Ug*OI!31PUEe^J=SGbSpg!`F'^LD6m/)c)k(XI,L
%*/ARS=XS1;=2!H4pP(9F#&M(rD9-$Nd(Pc"='TU%/"p`V(@\9UEV-RpqMVLa=X5YVH#JZl,:Xnd^5P\Y]i09+\[fB?1#9)FHn8eA
%oFrCULjHqoGKd_rBKTqa:p-$ocqh-DOXXC&kY*kG_ju23.mNnR!o=g"cg2s%NXdaqg#D-*r<@uS6H*>M/9CWs!H]CaAXMhk/49`U
%^"!!Z2ltMsVcVm9I"N2T6l&s@^P.NIJ.2#RjftP;6'^5b7^)lcgENOm'lelQ(W8'<koE&X!&XT,L%ps+P+mY2+'QhApD,h!A=AQ*
%-SuttcSc6P4^4Lk)$Y9nU)QI%#tE>:K,'kn($gCG?5U/5CfdpP[P07I%8/cg84X9FC!!\n'&A5JqcP,4VTnteA<n$K5o]MZoG'4R
%<hTQh3HjrRfK+QgOWoLc`kJ$9-/;P@S1K\_?m]q^O%:Mi%;RXp<lB(I([-%U&IP5WIn+_)M7_UYrKk5u$lr4EH80p9?cst&fphIP
%B1<HLW[K7'kYB?PZ:;?/`*H<NDiWVUMnCG12#rt9f,h`0N7=FXr7QBMJ&&I3_Vq4;0EYQ!;[&+$Q?5:NV*3['HY!G+WtWKCL*qU9
%`VOV6'p/1HZPfR+YIRDSY/P/t"W3'YLcQ8fM7QC!Ni!]:E/[kK0&j`AYlg8qQbAdK$^G\A2C+e)B$L2V?%hG_M]q#A`H<A)d7*a6
%]UT#T0N0Ri!i`s0Lsr$ZZZ83N>8Gq7$a=4030!a9m5daqLcPe*W'5A$:GKE0V-]PX=NFs%'/F^'5ebu5&&&raQ:3_o&.0*4$ddhS
%ecH@4`%D*:p#bfpDR<sph.R/m%jg"lCqZ_&%8TBnU5+&,KT$KCL=c\f'1-D3^oJNJqB32V?;!GA*-$aGA"4<:<!V3Z$rbQ&W^.@$
%IsEcmM=B/OgOY20@Vcnf$fS@0TOJ7MlV*s?=P/O^AA-BVq1/:'$(;3E1L?i>QP%1>#^eW*Ldi!X6U@NTW^Hit<IOu80iJKT*C>He
%*I'sPG@$XSEqf^)egd[b"`UN8b_YPMY,LbB?/:D!_7(!Xp)G(JW8)JcY)#pX6=W4l7,I4`?(6/QIN-+7"YV#'(@-UkQ10'MHem#U
%JGcTRB[`!8]j?Xf,(IQ.42B^%352Lo=4q(-=A8F#Gl)7efV=<s=RsPSQm4WsluNa4/WOMoT!73^+FkZmEiAij>e7mnVcHIEYN[SL
%<V0$O=K+nl)G0NOVO3d%6i&T/i>&f44$90K]kh+FPF6V2\[tF`VpanJ^niFs@Y"onZ0Y3;8p&r?WB5Md"n0CHEh`qEGL;g0K;(Sj
%&ZVj'T_`C"#t63u'Jl[D4-`)-$`Bu0)uB'fbC#^GJ9!"]6p,R]&]\p#>e\!$gt'>fOJ,l\Z,uEjr36Rp\s*_#Z*J")g$o-b9dQKD
%Z2^_:G'BS]@;-$n204rD!#!:](l&"o%Z6&lp=GNh6[CLci+585SGX]HI']@/63r+oA%Z@ToIX&LTW_bkBB;GY_@$>T"\:m>rfV:=
%RG8?Ea/L49;\%%#K3OI:+VTVglU[m9\9Z#5(l6pX?*aXa++_rR7eIcW$\ZsfQVqNNDALjcfPefTmh*@/F2aK*^_1,@<<!m@:[M=A
%BLONP'\%]pMq;!MG4QW]YWJW^@=(,MZT!Pc3QE6\B/:Z&X>GUuT?Q6Z-F6:cJi)2O),,%A[95FhN(^170a&fk64qX:asDCKqi.P9
%@)'5_erkg:!l0Sn63#sciU')'2'aqT^Qg',k0]fob,/qde-.V^H#!hhONaGaIW=rkS]:qAf`Q?i)Yf%eWmjf4<4!hT2nOCl?o*'-
%h01r][SURI;6C;3o2MG,c06`,4ZK4.`nUCl0`CD([2Epj\7&5pjoE,K:]BW1&.=ATXY/QZ0j[CZ'==*Wm6PMsOFH>Pd%"Zu4HTQ;
%7r9FWJ+X.Y(upH+gEZB(576WZohh__gTdJ*6D4#@2ChY0#eBV4)21VmN5EPap[kY+,"-sN-^6m"7=6'ep;[Zgc\?%\7YJES#7qs;
%:k'.Rq)j^2c.ufG]Tr^M.)P,4,t08LOL<SHe[9=\c*>o<j",;$faL25^o;Bc(J)(KSWo)<7Q$,$*Pgj=[k\d)Nef'l!rth^cp3XE
%V_/d?l(N\ZoKsW"b;n-E<-]\V/U''*-`/R*9P6ZTLpY;Y7eg,j6?Q,9eC\sX#G`<iqrPHU3N3<#Y6$&r,nugtKEd)%[)4F[?mjba
%79X+VA)sJ19?%\2iM(!2!7r]f35`0CkebULX!4c9?<A7JRSeBf(qI'g3X_#9qK5,7ho@86_<")GB?5WrQ`h8;]:'Ab*uW>(.-BS&
%G#W!=:+.c-+_=Z*J8nj,*!km(!!G?rO*ODRmJnO#6\p^[7qconAV<'F/`dSI+^YWCUJ_)$$Z@k\:.8.?YXPQ.D?Op/V^2V0YZ6\n
%.,$/koAFYnn8,W):FjRSi&^BD:9'iU#$b"`Ma0nGePJT+il&dcKL;D86Sl"H>*/(`I3]fQnsO(b4%Gk7Oq5&Z%T1H!K94GX54u^d
%fcQe(P?;#U8LQZ73%1OLh?%PkZ3Vk]4^nQ[Dbq(J*?$Dai>$@,G#'9GPVg>2C1]"KgZ8E#gI9)-%KCpNmF-<:Sf,ZP/_F*/`<"t+
%H.]eGGVmGk5d6.nFN1X9$D>)F@u<S+Tb2Q#;"QoZ%cEh8b443h:rcjZSqCa;>[<AC;5k/O7EFM@[m$<L-G?S6Y"%4I.W6=Y<f=<*
%+%NR0Qbp2.Puo8=dF>,C>l.rFBMGuAC=KM,801mGWc!l#gt7'adWE@s385YB9imMFP16PqL2E.d3_%EVfO4;+[Xq$Bj[bM0eiA:a
%MSdD\]?6(e!OZ1/N($l)ek3:r7s%30-G\oNnk0Hg\#uhrRZ#GUA[<:m%GR@%/qIsADt-6&.kN@FkuBf\R'D;&2]/GFl(Z//D&SUV
%i'JC2/=*Vt[HqQ^?#\:EX=&OK@Tm=,jTR*-qJs`?23(^L]XB4^8>9@#o<Y2@9h:uj1r@J^0!\9]@]g!?n8m4q;;o"+J$.KGKQYCP
%aXD[;o,_LZl(F8T0`GMo6,GZ,&2TaDGTnJd&E#[@NC,'&#Xr#4:e$/Y,(c^bkTLMU2H)PFP#YXR1mR!5pb%+N;5M)pOVCXBIkRN2
%\=T>9D+U&3e'"PGn?,$dkan44l#_4(a^!]c3<LR'qmAj1"Lt8eb+gKBEWd_P872G(mpU:T")bN7k[^fumT2(\P+CFWec2Tb=('r,
%`[q9;V0:biCL-G4IZoFMob("OWsTTR+p5Z0#6\g4;R7J['rcmd$'uZYFq(k;7AsWs[I&<.YSFI*HT6&O/1F9b#1.I9X"P8efs=jA
%g24_SA;EaC]1'bIZB7a]UAsX/nW9q;_Lq0^W*saq@:]W>c4W(U\@=o-cqss1+LKk0"WSV:b)@0%Z&OWu%Wj[1,<aO)<OLFETp6sn
%<0PS:a&1#Qf0La&fbncX6I_kt*?;hMQA3mP3uMU$-0,)):gipi:6b2aRPV;fb"3Vs:b46q$UeE@5s&3a;K[*$m@$]/%e99n^%FWO
%.#.Pt/nd1+jSK\DNor*U8-]\f8,EZa\SG\!"I::4H7SR`eE&2[Yh_ATW)^s.J<eV]lmqB&MF'@/"+&c)5qB4!F%RXGGH-BBH1#.I
%.#4CIdI.i=!<,rsZlM'\0_p;M@')>+>ZuelR@McScP">S-r9"iQm4Op\%2kR0EXJ<$R\.fe@H$k-Mui2E#eN[W1k-^Se2q(>1^Be
%4"l_"GVcV[<_OOg-1$MmX6,8YlIBpj\VK"gU=aUO["Ght-6cc9ruo7<\RPum]!t@ff59K?)#uJ?H/E.r;GdE2YLlrij*KE<j+ij=
%j+//b9QdI-Yl_TTYbNsX/[4YRfF)BQ&Q&k#f[CAd:q]54AMf/;pC$'Q1]gK5dF<>LM6$+-/=D:$>dPh>,.ef.[^2QUUaScg7`%*s
%O$&Qqje-Quan,[>r>GFFJSRX3<;1Skaa6sq.Z`H2@!J+0_F%eLX<M&GfkNej?.psrj$^nANFG/l).A3I*^@O3Z[)Y0ChJ6.<FdX%
%+:7%Jj>lMC+;C7NTu&Np^CCroaML91_=U(c&TPc.bp)Blm,jC7!()bPU_C<1IOQsZ*/B@hR&Wa&,3er;LQNb0(c%b3)*uMA+C@oT
%0e^X<Jb7$G"';TUKX&>6"7gLFA$,,ErTj7oWJL-,72:8A=E![<?E0r[)*iS2I/?TihA>B+XX/(S(phD>.-tCR#EtW0efM@idEHHH
%(caqi?W%J71f((H0LVh,khVMXAhcQ8=DnC^7NeLS,hO:0JsMQdfdt3ii'gq=XWt#9n9Ge*4#ePi$EZ.$$H@?j?gX?_"oTT3pU`0I
%d;8HU'Q_1;.Pj>ENoVB4ooS@P_ir3u;df4%F-=UoHWGEmgr^tl:>A\3Os!IHZ#Kb\nC"G/k)"71$OO=26V\)2SJeq?A=_E0&:(gO
%i`k+"%qaZV&<es]4'Z$)QYm^gZj@(Qp?7a_59@1dP#,9*%GS#IQN2ZNp5OeK$GSMep!ggYRos]!OF5V\-:GL!EAX$5>XCc*O0CiJ
%>KMgUjJBQ?9#(^oqhXfnkB%pKNc8=?.FMCqM1?0!:Vg\/m^kJc+sHj96:QL&hFe="J6N/ce,/nE&L;6\>#u9:$![R@;cPo4l@oCL
%"u6&s60S;B(u96/[$,p*q3BX`!68$;4Yg4q(;aLRV1@fYatm105jAKRKgG`t1%8#=KuUJr8..'>F&()VQ\Hc06SKChe5P*KV'p=[
%C__:Jn,BU]S9aD0a=`+:^s\^BP*]iWb\u.s/:2m+qB^$Zq2emnJP`PeZfn1k5d`\kgb09,,s(]E`t5/8S^#7FgkoOLe_k\Bs7rnY
%YqVZ>J7O,ClA"hWWgi!a"2W+OU0/i!/ts!DL?hZSoirk13cu+!Ug6'+Wr]/@-7@S@1Rm)($9#Hm@RkPO<H0m"2&l;-r)q]+72^OZ
%7(e`/,uT[h[ffVC\kedO&U'D%$3#f$P/p?d$du7sdt]gtojYOMT&<'V(U4%^:q]Uh;ItkW_PNOJ6:IX-prV/f?E8Uc?*uare:i+A
%)Gg]1Hub_+7lTa3*JN3>B\^iT3t:_DH(f2@9QpiPb^dP:(C!7$hJD#XNBTqckBKo_<=kHJMF1M"6&Q4sg3>$?Q4s%_\Soe[VYQ1Q
%kK!ZINTrJibqi^cJ2$t[C6s#iqF1QRB1h-uh-_QdGUpEARaXQ=pM#XH@,mCS(mh(Me\!qT@%)qnnfZWM%[0eup'%le<?<\`KBpV%
%3h($*Oiq<=Lak<1S(cS[TS+I2>h17V**`[8EDRS/6]#8X,tu[+JF-IqOU?t)WNs0B%N9L*1\01?,ZSs[*=`uX7X;<*5!^6jp9$ZC
%\SUf2pc9Y%G&EhL<1_:cTk5FR11c?BYeI+d/DC?IZ@>lL2].(QG,8f(Q@M]uN9d3%b*A$l;P)%OYliE1G-LtM#Y/]kaQ:Oa*/]U%
%90G4Yr;Be[TCcgC/#lQ-8(n]fOmItXlHgo0&MkCPe=>ut>Z'ac-C1!Va9Oe&aXCP[VEPD-Ur7EZf2&lV%^b@4r9.7ogLq9q$rM;E
%cKaX?TXbmaV?9W4i>0WC2eFpfV)8^>=Mpm5hACm&:u#2/(6)u-7D-_9Tam1+-j\a7"II#a?H3'96F"$Ff"anEGE+4D30M)uL%:Zf
%VP#`$qZ0Ksr(^+V,iB'-=fHT"CInN-:3/fhKtX'C*n%B(+EQU;n/ihp?LlCc<_VN?#^['eNB*1HWFq^A#fR:RAkbTjg&OGKGQ*"Y
%Z8QD:+=Emj6Di]5n6uIbNL8=Y(WG-@GQ1J=qVV;M=24OJmsk9Xs7!iMli5rDrq.pos,V[$]ANV.?[qk$hu;p<?[_tK^\ZciIebq;
%s7WpOps&7lja@(a#5H1+s7+hS^\uK.pg"HOT5XM\0Dt7As..E*Y?q>Vj!2[\qY[P?a+'5t;Z?/-e;sX&@*O2r0A=Hrd;]D>rYPV"
%T:V0prL<Q]ptO^)kFWleqEGbkYA99XLhl`n>Sp3tq4PH_YVb$GPiJH]%#m88ji\"mBe:d4eJ^BuJJtVe&?0GB=OLNV9C?)d9fsVZ
%Gc!<@R(p_?n.qZ(25(BBgm+l"742Om'Y:1sh"m?_PJ1XHeN5cYeLm]&-a-t#cDuPJFc*U80@haj16!CZ*(49b,^crDVUsF*X35E*
%,T_G2/i5;m"$<@?+\C9&7j,ss&QTN]A]Jj0!P+&jCgp2q+8Ri!6^7PQEDri#0Y74k*HrN]?rYh`r4oR?H//QV-OB9L@N1ZUd]4:2
%irZkm$OBKg!gS?V'_13`kRk+@5Kf90d74`r\id1PQ%X0S<-K`l#iIBS$/#S8cX9Lfa:9B51,%q))>f)3A.da:QDBZOR-P0)Lo5qT
%.l_Xs8kt`t8fE>G7g+/p#J3,meQtnQ^Y!Nn[XDJVQY[eY@T[5r8^Z]$_S/GkMDVDbLEMkm\FVOOM[1tfaGB:OcGih(9W'cdG2C`a
%cI,8Iklkg0#<Wjo#eP5WUHtcWIe8Tm2D&+thDgI^5I@70Dk)%0X@t)]*Ok'Mk)k;Cju,C);!pD3\3:0#Y8C\oDIE6goC8k^osoDk
%T0S$hF!,46:/!-dJXem6@(p6KLe3HHBgO5s>`pm_i7Ye=4-n-I?.;Hmq'0gIL"U<(_6Hii6P+N4(f4W_g*AGHXr\,_F-bd5kHG^'
%kp:^Ilqu`qYXs0RG:6"b*AhujTXI$98\1^58(\C96l8bqY6(KW5d=Ufi3abjLEiR[l0@>Y:ERc:!a!0jST7%`Y&*N7OmS1`71rj\
%(RHsKP`6m;doAXD:SpbCm&W>F4#(5]@1g+:hUY"!5qVt./X`AILF7]Ed3[=rK"%6Y9mV=Ybk?s8(C=b"!-YT(!!V5KoQ(=`+4pYc
%*<7s1FE_`H&J-*e-Bd%NAIc9]9\B-&mtJB+kr2osluB(1VJP+2\1NuA"ce1*%`l`%E-&*mGEFi#lO3U"7n/h%Z,Z,(ki5q5oc'@A
%qKZ<!kuS[6U5e)A\FK_625=ppkSNOE\nI'>F$*Y;"8Nn(9-HiOR>M?LMhBpg0oTT0AFiAh\<_]U=]DbEf:6D8O@?s\:5a\qk(fL%
%jG]Ct8S5MYfHm;G>r'<Eq:o<PmRQk?``H`SY56afrr71)T5RicTPe^1S>AmBqiI_d/LLi(S`$i][)kV:0F<WOjc4t/r:2o_4d`R4
%UV#4ed:O)KOG5P9Hk;0Y&%nM$ie"UtDi3@8LeYp14M)Vt+]SgL%F#;#(Kkj-"bWdPQXW6T9jX^"Em"fqZ`CNrGATT)`1!:BdRRPr
%CS-826LB]jL'k=^A$MWp*$(0E1;4GrQI)H4JZpM`[4Y:/h0k.69m0E'bN5]l,fA'n0(D6;;6<48ntuZ#h)2^q,),rX7)VL$S.b"$
%D^"`Tb#@+JU/YD+#/]"L,8,tRHUl]QG4k,s(?srO_M,bq$Q7hud-8VUDZO#RR4TrXL.m=>jB$S]LKGBe<dR?G'uf>:/a4ch#dost
%2jm3X_(X%*"Y_@q(4M^.=V6#r`f)mu'nP)Ge/X++??'%J'/,0[>a4X>D8ES/\ABk2>RA&*Z5Mts`+W/J!jGo2CQ$WgD.;*K)c+=!
%6_8$3@D`Pu41$rnj'8qrP9fiI\35;)O!K0im9lb/(Hd5n"%lIQED3/oG[XO&P/p[+b'NCE6&Bn<YCX_f.gWqU^Hq19<.cNT$jsEJ
%963]&b>-t%'s#<M+e'!B.<8n_jtg^/B9Qnc9E%"M.)^rfM2%[S/>3ei!2AC=Km69S/jIID6lod;!tF?9Cn9WKpR#+O+@=<=V*tc2
%"XJLcDGenkD5f:7R:d4t5[.CBX*L&Q=qhNSL>m:DSL7Fa6V(ZG$C;dkijStK-'4li>+*ES.r6c1g]EWi-Fpn/PopdH.d3>IPrZ]Q
%At#\8BJ/1qOS$-rqIHRUIU.t1BZAH4Q-P++ITU_Wgg>g*,)9aT"]U4^-;Ec+mY$SHZe?-*R(,lh@.*I)g&d7aJ3b&-6<$a$"<T'n
%XHEcqB/O>.E$fYbIPcR+;LY-V\0NpX9E;r9o)mdFei"):(AJ-URn0p;^8VL-Z0D[HK'U*Z.7Xp>iOLVj.Cn&n3IT3XLlJ`8kq[/h
%.dZIuc1BBWNC<D]=ZF!-D7)r8QTnU^Unc4=h.BQsLLl1/^L^>V/*qgC,*n&X6m;V=^<Gl\o%bP$8l0FZJL,Q>`K!\l6@,IS,p2n_
%[sf>0'W?<uT&Q!fE+2JI2TojW<-O_25-@CZ%IE13\6'ehImiUbKkceU<)`jdOf0erenkX3C=Inj&-gOdCM'AHK+DFKh<*,R]"$0(
%AZ(!;79=BE3()XWVNji=E1L`BEG&eFFR)U$jae5KMTgWH^PgNb]q?@<>+7#""`dnB(u<?;Ql5s=V58oY-K=MI:m%2-pU-ln[tH);
%H+asaFI+QX4jb\OLM*hL'Hg=sT<lX$KmC;7PZ@jV6Sui2P&NE!5lu>:isP"<4,P<27`\D/C&+*e;I&p#UBSVK.e9JFE'qm)"%rZ*
%.B-2Z_2;I1al[3@bHUVR8@mF_J/(c1:eCG(dP$DQUT%qpk4'-U+mm.*=EH8,_@GWN"2Lq!!@)Hm+^u:T>7;#g:d6^&LRE,!SXq:q
%ojPpJWu:R?e#^nb"jnll;Bi9S@@O?16/_A>j''^#gh:<r#hV"nEB1q]Zk0OtlPa3UcfiMD,@B:l=tt-phCGYg!JN/IKQ-[111(5a
%L1JM$D;oPk(g;WpO01^Nd3,g-n(P80]0uD&WJf5ccBb((.KdpHUYN+\>S-`&X,/^*ntipD#`X\EVU>"!<P91LTh=no+h4dYTZUB)
%\O1&_mi*>#O-e2jkG_&R3lG%(7GUPs;aTYBC+./eS=t@(hY,L*X=!85$SF)l?<CU)>QJYk#[9)\4UZAOd%i:^XV<sl9=>,\fagRc
%#:5:bJ[c/Y:SAha"a^^;*ma8W8W\oY_cMVJLc'UU=IE9sFR,3-HT^%0,FT]*V2B=e.uH[e3n=mULFNHadlh*V<!E3n/2g6-cgl,(
%2/=i%\`g$&.!VV:i<%1>?FE.h&auG#NA!,p3QjZ[FbO5;aAKf-F9_fEjZ3eQpa2IhiQKbT$fHqJkupF^V:Y8L2B#C-^IfT`0d^e%
%eHWWS5)tUp`*9PDNErts]pL"!Ze./g44]k/<elR6]2\baUoEh5Ql>^(%OZQO9t8\PjrJ@B&nm\$i,Pi><%3qL<$.'jHC9fMA=AB7
%hJJ>_Q$%B*d->#;,NWr7dlUQnnC`e_lNinp^WeT-i0_WlNm9,+aVc3%RUUp'Bgc1FIPD!%4lgdJ4%'V6.c#m%_S*!CIG_RUa`'H*
%!MmV>&eQ^7B4].%ftmSR'_AtK/gMD^\r/QQO&4VhR,nY"(9O7s!t'=3pmh&[C+;`!W>)(XaWfasDJOqBZD/0H%eV_'rLK*,_qs4o
%#*>a5nhW?aIcaHC=UKdK$!BuCJO;u*=c^o4S>X$SI%#'<5\O1,nAFHJhkT'Y1s6%GKH)qmLQEGm868Di$8rka"ZMDCMqI#(OqM#k
%aL3#I%<kB/XQeT-PXRD3m)4MJNVW]oL0X,C+LcQUJn%)Ji;a2O8DY;3?5rr(Sd22L,;bD**-/Wj-bL1Z&<Y.3=Q.7?k_U1SaQ;^t
%\*S2)7'38R;KI-,ni*Xt[hpZZqe^5W7NQ$m_:VI/59g1U$$A.7!Z.W?U2q1FA$7AN@ZN%)bEt[agRMR,\BUF1^+h<o`&fQbDfg6B
%Kn@4[Ngk$_;WN2RXd\&O>E#D;o.J2[i`N3O",<b\JIm\RLS]!+^Pos;<[rj+J/*N.Fh;<Y476faG:r*K?IR(ZW1c$<ED8%V3%UVD
%+<HH`:EXa-E4`4C.\*4CnEBU&0<S*_Jag`K:]^6^oK-l>RokA#(/uRA!g6?AaIGi-$kn^GI\m]VHZk7<d]Hk\>/>F*Z)NVBFf)2*
%,<&!aL%MTP_\mH9*98U[b*FA207areW)rJ23lsm7ik)*VbofchE76L!=nsD.?TE/8E=p7?E";m,;7`h'Z>\ZE]3#KZ(3HMcfhr*M
%5,Jm/i&hJHFFR8!-2006UdQu_J8mACXVWM'r.D<Z[\#]/(bCuM<8f52l3'2:3^)('1"d=nW\WBqU;1Fb%?]`*<RbMXgFN)OHCth\
%podLjasU%Pc`7gB?.UHK=0!`"'%3:)K`Jq?S"<[MoRG@rK:I]hBJM-[]EKLZHbZ#5Kc^M?12e&6!#C@SW>79>;sYi'@,:%U@4GXK
%E0D3ocb`$:'L\iY)'ZB,Zg?3miQ<<+qo/=:;VA.57udX1gKn]+RXpaAjtJo#jQ@,dLa$]4;W3`EVjFX<eZ%gCC]>pZ?`:S1[K^K,
%BH=^?k`E/U'n:u\1)N\N^.WW2K;o"P_R%>B]eGb$HQ"gXcG6@jk:`Bq/c7HhA<[`tT<@k*=@tDthsN^A98D$jd(^p8?lM7/\,l5"
%DI4[!04M_I;6"Y`$5)^a*AA4S&^gA/Y6O29Se9JXK,bfOE3WqR$bD7uSo9-X\>Z?XCo?+tI2c#aTN+l_DIn9[DmCYV<2)4hKqOYl
%&$r>+Lj52YrbR76W.:gERnR$4gqABWY\;L*fDRM#OfEN+6ddaAJcnLC>&rN+G,JS'ItV+X*u&q.47t>FWhL[OYr*C;lrkF=4F:s*
%@#'&RiN07(A:Uo)Rp\>9[`c.ln4k;!<&Yg";uD6Q]!FU,**qXS9MVkR?qs#6iV"s>A4McjqQb6./2TEMC^d0+&1*c&E's$$6Y79D
%)K"\H?$`k\RR1R/%^p+?KLP_?-lk7drJK5;W7DXIL(GqGX\bRNojr*/W[fu6YWO&;i@_0g-ba)K)>372(,Lpg>KkG@_37P[H7=IZ
%M-I/&:Y=IM%WB<:r#N]?Wc4q)Y.Fq+@0*'-jD9qkl8a$ZaUSkmAZeDaXn!*OXT@s%0OHTe;?MNZZpjMXfB]iE8S.TF-6(Rs_N1[.
%]$8#'iO'@``YP]:P/-cqTa@N2b)o0W>J_BA`^"9q3sC=)*;3-;@2iU?hq>YYdE3'X*Q^dXaeroJ9n%O)#qjDTK-t,+C`==l_3q21
%-Gp8K:4(EI=(d)Z+F9Mqd1L<L5>LXO0D^R;dmq500#qtpP@TB\c$1rM-;B]@;+ZCoZ;E@kgfG%5&rJeW7Q$\4+!1*<JcUbJ5@b;Z
%9Np!T:,Q2+Oge_r`ROZV/ggfZO+Qt@FD#iC*t\,>#V5at:?iM\L?3T.dQq\4HA&4.]?03tYiiP)<CWa[o_QGW=PD-'9-R&l$rE!o
%#p)"G8_^6IBNGu6]lruZC<?P85\<91j,o6tCiB&Ld'/.U5S1Aurd?Ae)JfT2*.t(#cse1SkVhZWLt+H;jk[X68>IEqmJ2+:LY8bA
%o%oq=jl+-3&gNf;(f?HAX(SE!<4L1B,Za'`!DfeLgV?MJFm;5Z#357g61_Dh"dXRZmtKc.cmF<f*!igL@5IGDh<jDe<ER_<2*BO'
%L(o8VF#^^Bpl79)IWGG>+!l,AS-Y9el'N;nMdrL!LGO'kHh;#aqio>RS+UiCQeDnX.$ln)Jk@8>EI3W76Bf6:#_^[gQl$R;Na!ch
%=_pI#VO)<lXnaXQ7W'EJ!]28p%HDVBUD%P#V;%#9&as[ca0H&W)J_)QqM#@Ve=6,H%e[tr]5'$T<fIGiV$g8L.4TiO%`3j-Fi"UX
%PTN^d/#q4L/ZD+a<n7^aABQ#^;dQ^?i:<ud1eT#*rZ"O(SXsIW).RCe+EW=9HLs/PCu1Q%1QQ9RK*ucJ]`\$?FS[J4Eah+'XcR8p
%`X`(7BKQhkUp_94%l(-uHu(c,WNLu.OF3J.):VZ"1Z'YR"mmG`o]!WO%@1U<?_GC8bE9.L"*ARm@0+#?XGqHmW5+c/KTN0]cXMn'
%?$gV`/4/f6,c]l7b89gBqfZP'NG49<7Rg-:\De^JQ(UklLqALQ()'MO(jTk`GN^reFsJbik?+>-UQoBdFq_r*%3G's?<:Yl@+nji
%Grd<B9K/)Mn!*(e'j%gLl9#.-:FXATIJTX/3cXNt[GGGL[r:=nghpG_Z<FlGF0d-?"SeJJg&;k["9bF<"lRV_[t_ZhdRgkTBSWg;
%"H6"#T>GFe:D$5U1'hmoD<)O<&N96"nc6@mC8a7"ftta@2e]<'7uKUuG[NK1J4R8*_:_4V\aHo.p)8n54iHNu5(qR!:o$46!1P2W
%5t@i?RCYsWLs+t0`7OSYHdQ9k;):X9<C"<G_o_kW]_3FeW#`)92EI/8>'NP*GV;*Ygarp3\@Ri*js3QGQ@hhbCcZ!f$*5Hi;i/G`
%RRs:i62a74JK[Q"M$Rukj;*s4BJ`WI>"5lL,dIIcQ:Q^)_\D([enQ@d+Xl.;8VO<T`Q_\bo_Jct+Yr\(1/0+0`Fta=P!h6'5?,mt
%Qadis*u-d]?lHtS.]@NfX%9b'hnr:ViYS@@78DECRjB4Td&f]u=@>079J+O<3WaP1FeJN0.d.4XW$S3.Z?je?QRR(Y]Ie2*%ROe#
%h,D4Kmb2N^Zg#q8U3)=&ZT*Ik_`en0Ob:V6Va(@t,-;Dr!i.55*Cf-dTfac2VRfqG'kM?2C_^,TQgHbuXt26Cab;nA&O*tf:rJrj
%*1./p/JT50d09%LliER2ke`u1C,^lg("$'6"0%;:e+%/jM+PN;Tb[68=DhHjm#e74%^SZo"Z:P;8_,,0qb"f+L6ae0pC![X2j7W-
%]<3o-pVPnq/)&c"D:RXtK_V>HWiZa6F65QWf4)=&PLs-!4A<'8c.`ASO?U1.;dHF3TSAunZ,KH(!ShRPnm$hr.J%3H=d"q]I?[0N
%O=VIp?q3F@"P]c=&JWFRes4a9;8TTg`\=I'kN3.M,Pl>1fRF-dBt3[Q?<k>?9!Zd>c;RLd<63DYL.Rd0;C@&??Cd>Oc\uN<.7l*I
%d("3^T:kD))V(Zt\3V0"L+u2T8RsE93e1Me&a[p/f*MR^N`B2^G_EjZ(&#S8mcDr:\@lEsJ616"\tupTLdD>!R%SHfg("8bh9IO>
%\`"c$LlRImDQZh%hHhe&JAOkdra;[.p+#$KD26IoEYI3@)5h]tQ"*]r:@k;BH\HP\d@0!>RbC?3`,pntf>512Xfm!.(te0k*/09j
%XG>9lS<B=mRj)KeH*c]ERi6./gWFW7l4Q@LW7<RI1aWl$'nI`$,9_1'>kg@hUFbJB]V@.uMIsW8Eb<A6`[g0`$IoZP4L?1*G%_*V
%ZsQd&\m$']JkHh"Gdn:qgN3!MA0-hcDlb<SEPVHr#;NV:hO!Z^l*TJZ,q^!L$C?7#0o.p.Eo/W)mInGnbG3EH-QHQSpkk,%&9T?b
%GtGQNVtBK6ZlK6**?Z]XDQu4YQQ?S`*Ydd]Jla:>)lo3F@T+!u>O*/_O(S5c/q1Kj8$p+@*15.oku6X@iOTucLN;#$2Rr,OZ@rb^
%AKO>Y%U^p7dHcol>+Hjk]ni_-G6@!+Q6?gmM78F_Ul`ri6A\OcA7TY+&:OVcp<Z7KMRZ),,I^JP`d`.j=XTbKr4/dhp#cZR&G)=!
%J)=)QRbVG0Y!dc$6g3\cA:FsQ!/)m!>4:\VZ<<'`N;Jh<c)uk2e'TXN8Bo>EAcH@V(T:X4Nh%eh^Zr>=`@h3Y4_d3..rJ"j%3J#[
%\3X4\qbQXH9C;LCQ8^+R0OHFM!5MRGUN@<kbb1U.2QVhBLS&-k:c^Y+o;61YJn63e]?i+j\_9V53;(8/_It1dff_:^6NLW-]n"e$
%mOIjBl+2>PC^.Zn("PPT-DR2j,qHR0YAKAbHWZ`O'Aj?,rdNUiU%kT'K?s.#,N(,OR4(=UHNY+WZ-=-mH_YR`2q!o_&\",/9?D(F
%oH;1:Zi==dfP3MKOG5LKC/.7=&SMQiM1JtJY6,KWIOnF]IJFNWa@!1Rb!'_fjEekBI4YMhUQ+0B1sjtsFgV(E$,WaBmC?,hIqRJ@
%NIbJChp<;Kg/kcdN46K1hHfuJ99i-E#d)Is`GaRO34mNkljS.qU2HC;6WTsCYgY*qnJ,"'*h`gK&:!PYBOMK#PHjqH!n(B/bM`:Z
%ms,/[<KmI"(q';lG">[ZZ*%ekOI9I[8)MlI'!!`4qJ^TW(,mZXVl6DM5$?jR-:Uc.f2IZ8.';c385Dqo+n..4SNu]oo8G%MeH7qD
%>s3""1>N4KVQ5L0;O8oNQ:4u:/X,mW#0<4k'm,L.IbSHdbaJdg9g]a"d,)D;UfgqEiA,B].S@":DTb$?WX7TOl\@Kl58U=,i_apt
%^)IKd#A4NZ24*9j$\fMU?^H2>mNRQ:i15K]aUaT,?:#Ma*4A@QDo'\V?[!7@0+a.YiULdedXBWH5GFp*g)].9E5IZ;-N9:8)Z=rW
%7L>F,T>bQ0@E\XQX@<ZkB0Z;1rDOc5rD.j^q"QKHPfR3WIJj,t8H8X*Uc1W4J64('rU<\2l0nM*<TQpb`<6IAME"`2bkduBPi/9r
%!*7RNP?n9[Y\8:u,$)BnE@m:W1YB[TK/-]^DZ]XYW;VGO\f9,eWJ1kfc9u\;[i.roGp)I7*H'UhI.MHB!:-DFD.&X@U+s^s&+a`T
%Qd.tNEhL*g0f[mQRn*[$#831"ld"`P>ch`DW@Gi%&?qSpE-3Aj;n`Y&hGob$X$fl=gJZG+@"RA-WuDD!9Yk)^;>DZl]'^X-YKJrr
%Y_k0s-s+0&3HP*XYMsq1c#4%]<F$gl*0,Om4J6$L5RAG1pDJNa!^Ycb;T0$MJq-0F]Riu9E?HN0jnnOF(\98\031jpHDud75cB9]
%G2u2caLn^7Qe'@#Of?M;$j*!ZkLk@>bIK37[)YhCn-cH-l%[Hf:RH?7UpZPF7S\YPCG:OkkK-n`VA.GN?!4/cI*i3Y<BVh]+XFLG
%PVOobf'r`50!A[:.=.Ys=<54eiIBc]k'PNGNV[]/7FZ&g_YjOTY1:Snk;nMWS\:7\EF=+-n=;JX&f9<Xn*5\a>6UKBU5;a.c'ScG
%:%)Qp@u!tl/cl<\0Q("oooMe2\;>-[L%99Gq*(<\%]-#O_<79RX%INJ"]2n'[>Wdr)55cZLPf8^IIiI\;S=)!1KoH9HMBcMR%9I8
%!CfNCV<"Sod938P>`:=i&):E,bR-&%<hiQah<rOf5rX2F@r1.<A-IbbqnQDTQdL&<BJkk#96,3d7lU#*[c,ATRgV.k3MJH$5*Q5o
%!KCaAI#"n7m/s%Y@#Db^r=#>;KqiY31,FV!kC/VMU:4lP<sbEX07jQ?b<%2o]BU)uG8Jje%bN=pG;h6JatHm2-H*;'k`ofMdsMMN
%h$@,(9:W^+o*mMidnB?YH&;arZPJ?=oG6-OaCf)g/gM/)LmNE._;)9m%B8.5>q*'\ePIop`J#g<AE4"i(5mp8,>Uatf\`@[!?]m?
%Xea.r_ql!&$1Rao/*_tWW?Nd-g:.Dab*Pf!R(1>G9:.DJr\7EYfHm#p`;9116DNqkm>"^U4AlHsRmCRk<t^aL;34a%VL]3M!-b]a
%Gb]==p)PA/n*qC"<q0*u01R')$jX!e.<<!hFrCV$F-G.RL_(%>*R5filLJM1\:Z<$\@?(PW?7@VAPE/>"<Y(43CusDpFA:"(&#E>
%7`rV1$S%iVniMUUa@bqu!`de=-ona?ab'L9""/e;f`bqg9(3"G"9$tNJo-u0TUA2ecQGos.h#jYpG<Xf.H9j[.;)p(#n)oSRATLp
%Uad9EL39CWWQY!oi'sN,lSq.sn$BlA'9a%YP`b\6?VofJ?9mG@j_=j&^JucIqFWQ9fVhT839WYkU'rkq=(mZm=[U%<I#EeQp/pd7
%332?*8T$<51*DuS<HUguXd*f8<6>8$1rs0Lj_BO%cC*cX>T0C2KAinK?:?cted,t$c6W6m[2a]sX)/\)Je7'XW[D>erk--)8RfYG
%@5XarK)$14r0(#k__&hC;-A/2/p%VHXUPhS!oTaa(ZFJ%Kh^Zgedq2^NnKle[=O'_%D<-`oM-E#FZCM:$fK]<URQf6_#X1s4U4t$
%)"-gg`e*QYmM<Dr3r^rmG+1EV/uCJJc\-cRGqiO<X4Q$W<D4RWcUBAUXbKPZRDeLi55nm]X(9uB1L@pm][8FQrGPLM5.%mm\M0"n
%,Xr9Xr[%1sK[jk(!\-ZJ;`DoOR(s`YSK$4:i#>Bl1cM?6bX\9BJJ]1XR88%[VFW?$g\kuo@P#eJJn;ITm"if(m,`[XjC0e3%d6i9
%!H/lZp>GNnmdAV1Tu!;lFSFd<*8Z]nOcH$[,J#lD<EO4$.u#,Ng1R3N$ZDPLV0$Zq>ku=ie^7#oFCAYUX9!J%Od'iXq>iJ95f^Js
%4:<J*R.-pGlHPi;mn%8J8o,I?Za^765O:',Mn#%s1,!CUjF^9&aoJISVR-DFG%LR&:u;)/oN`b-2"a@uZVELe_'D6Weq]>;^V,4h
%9L<7Z0PC&:4u)@2-9+_,>Em;lm5'(=5hJJ=dqA<1Hp\^"jc7YPb$'\&:3l`J>sNL$\cAPH"Kr]=F;Fsh'4W7J`2q$c<?'n#/i989
%$t+YEa_QfX16kEA0Xl8W[gpb4q'sE&N(VC310^'CQDQ]9[<Y^h"g`j`Fpr=HX!9<&1d<!tO;_<28";gcPn'!0#H$<#*(i72bB7%C
%T]8u,p@YMg#/FeDaN=O")E&5l`7fn@^_V-X(S$ekhTuD"-'Kgar*)M<I5%8_Z9'`=[+q,(_4EX(mq$IRW6p9Sk1:+2n%gQ"ZJ=jp
%bm--(KFPD&[8:.uL^=Li"<VHS7)/rD?]:u+ZD2-"`@U_u)>81^%2:ask&iAg1`*'.s#hSF>"QOppe/)eb5drU1UAC.KcsYtP$=CO
%5\i/m(5NMYQ,>Z&c80ZnR:;S7E/6[2:kb^28)&9/7XJ_I%']WZ'EHt')F7)->fIK-e8;ZbR]`dn3:Wj4.%1H5c&dc6T!_`)b^APb
%U$>"dVc)M@L>:#gl;X1GKN1ZGVmtb5FQ6DnlIQV33UK&&F^2\U/-hEV?[:)`^Ye:'j-I9I%%6iS9)ne8$Do+:<Is)a6QEtfcYab&
%dNj^?I^@t^MJ8OKP1TdcZ#54-A83M%j\@'RJ-.qAQDq-Qq4ZTL%<`67%Z)0EgF1]BV5gE+$F+<?.&m_J%\;kVUM$(O*s*X![`mkc
%_[PpPi'_NO84T+,b+`Eu<LQ((R5tdJWf)7X>kM.WJYjS`[nq<jKR<@g`O/aJJQTar$+\1Rh>?_0Log\T?BMWJd+gDM:duZJ4T&6o
%6MP]lj1U1"DRRs\dEGA!0d&$23=lIWiB06fZ)hGg+L]3='X>VC3;jFAZfc-TQqR:iet4m'@MtTCT*\jt7Q76G`nZ_X=`[fibMpI1
%R6=$V^!4J]o@i(J)hVE!K7o)"X-RZSS%f_#L4#bHleG8QDNdfa78N5(\XR:tqIA*R\D_uK*]r,L.AaUPVqi,#3"!CoQ^^)#1/DAE
%`*p`.pjAL)CJ@g9q;*_jJd[CP&0=X7OMo75So7L.&2+_#::T8"DQadTgk.b>@n;&V#\2@o%Ya-L3GAs#nedM6D$;iGIJ]FUq9"]n
%%S&=U!U,h\oY'<k]^IBnld@`UnS5cm">jbO^:GZQ6B#O^g:O,FPN]`d$0r_8Y,4PaRE_\UB&bF$,fKSgY9j,l=TFOm:od><^UdWb
%pSJ.f2$a!n#,o#m.ICP"CLm+@7BX781G2*L<tl=,lDhA7&j1W]AU%uuK48eto]Qt,L8Tp\d-\nC_KP6mH3rLCS$!s3q-D(rp:tdP
%-$'mMbb!f&7_qHZ,^i"Q:lR"4jAQ5:8DpF<:DZXO6<CaBg<n<;l("9"\.H6T;S_fY+k\#[Kf?enmU0<uFKf?g0/P<dG+YG_,&[LU
%9:jNm^%U^bEclG8^QAfT^e6+$jJoMZ:i2%3*9mZ5!f]bpY/NIh'Bp@p!7ZH'\Q^e^Qp=dK?<j-u@Foo6TkKqj'pn\(/%X1&18b-*
%GA:./C8-5V-3NsuW;o16X:.<O<U.\%P"jU30DEZNAsG</SZ7]WlkloMUjd=+&@OY#@h>`n4CZ?/GZI<.).Kk:&!h^DNS?\+R:elm
%kgPIpcoFVB?Y7(&kW,WU;Jn,r>TMqZ9R(&;-q+Ap@?q)(!*boIb;"_%^,3(.%BVEgM%&0b3r\8s&2,\"Q#qsN[V6)JYPi"Z:1(:^
%hVWmP."jW37671s[=t;.c3fKWoaY18\F'W5jg/4*Hs'P-6:m1KIB5ECnuDa,0\_0)>o][=5!gLRhqIET_;.h,=EK)tl!N?B4\d6'
%aG[i6/<&XT//&lDQ@p[e?s)M3Ta#FFB[la7OFi5jm3Y;f6+?:j%q@>-ajBC*D!#h6X:[nk`PlW$]@a.b2:b2.r;d?SVMtkEgpj5U
%^8TIt7<'#BkYYc_RS/Ab$hQu"LejOlQ=n"?6im<gS%Xr::);CZ?+2)4-+J8-U&oV-^2K2tQ#KeM<nnXSXOk]PFbQIsTN@O@e<,&]
%"=e!#,DkYgpCRs_$<QgpZ$je9+k>e@D*7U7/P&9\O)Ko8i#S3!V(b@(!ITP<38"&;lfY.W6==QJ>gr;Cb#d!sNQ9ZTDC4RH;9o#D
%(t"Ur=7#Q->8*K[[-ZESbHo*hK3_]\Y5&]R#&f($?GonFQP5c:GO>ogaV/)9p)/-]\:0#8C]`Tc(sK6VMqGdd+A@NC(uc%d_'!@n
%$%!M<dnJ=5bZKRuO(Hp#D5l9lM#F`Uj,RU]V4V<D&uhn8UPnF]+='h,q\[,%T@ho`U./RKLUX?3%]^A"e/+g2hp<TTHX7Ei5IpQR
%+3PdY$LisA$W:d0oF1f\V,U"#+a")5Ce#kr1?0u.?tM?sQ#$;XE,PEQ#0R/8![1f7'*eT,IQ&:;@?&W+T]cUd1YikPRH@pGR?3f_
%/h%eBI9ZVPaPFBG4?BI@msA!N3QScV+<Y\J>gidsAbSGIV0IlOU<%a,kFd(C_%98,`uVrP<G2`6_iJsfA,liVbtqn2]N#U'l?KFq
%JQtH00Me+G9gXiDU5'INTPF0[bbk51_@hLjNnj'5Vh2&S]`Ya!SMENl&.kEPLVV!*Qt=i'hNslq1A)3CjKIrsN%GXu`uM&%Mo-<I
%"E[hsg*5caFifY//)jRL2-_OG1C*$t7[<&/!8<$9I(5B;L76"jBmr(nDdk&KMOFcOJ4*oj6+uZ!+]i!#CF#[?5:;UNdOpjtP!mPO
%@_p5LfNj4R36ZIa;c0omQ5W&.J9K%`Dq"g"9E\JR:]Pp2%7[R+AT-EGS4E'A06QnPXnn!jU_17*aee6;DO'Z5I>9H*F2?(IIRpe#
%Hi]^7C&htE`PDG_Q,Z(RY4T7#DI_UU=A3\N\ATGg-&L[hV;kGSCFHYe$\Xm$s8BH\N$N-;7Oh%^%$8"1"SLC_X^5&ip\W<U-BLW1
%C2"U&Ksa6L74.nd-N*D8hQJGg^"t5Mp`F4$^Q/,;"1AU&#PB#9:E=a?$apGfn$X"7&/A',d1MjrRap6:N!s1G*gbE",hJ]\].]o/
%W#TII\0EO:;+6n`)UE5OZnKfHp`63o!H7?Pi<4p-2@Xp'#m8'[s*gn)6@TLU'[]gT.>eS'`06K303/YBTZHoNRu$H.P%`i[Ei'NY
%LC,'?5GrF(])D;[:k;RkBQ*;;;P:rXVNIl_&4lGJ&WIJa):E@Na?E3'2^ECb^LPkE_(Bj8\!6\1]>9XAB!ATK]a#3S^'k>M4_M0V
%CHrLSdG4ir_$Wqb"2Qs@"C;4qVsT4bh2idRA`"rj@@<K89bi>.KFu_<7o%U;:NI?`_]"F?_`j1l&I"q;<dZ"\BU8bk/(?!e5Ld;[
%-!18o@uK?(V\([TbD'&7.WqC17e*a$gbd!HpJ.f+Rc(N%bS(C[2?HAl@nU>C]YXYA/P$hb;3,i\[=OG^pTCY%6t!()SmP#_m45'8
%C]5?B',5r8+W=;G?_^#(#Gh:bl-FMm90p,_\85khQnXj(h-DB9esm4iOdH&a*;W^cftKS.q6hk7$Jj?M"QLK$\:Y!LBXQuG1#=)F
%&pf,]1:bN_(6P>ubDf<3Tln(i?\lemb`)c9*rnF;&%QE==qF4)/acs*AH\e=68eapUV<>>N'P^o_KW>W>E=5GKHeUrQp's$-H-,Y
%WpMmbEq.#L,M91B%&7NB*`9`**HaBG3,`_)G[BZod8KUm&t!$3l>QTYpj>s^OX]ijG<nr%gl_i_&qRAj%LPnV/MFdSXheJi1Fcnk
%OWd""q+)[b`:M&.SB^EQ.CC9JYG,,F+m5tX=@&V^q:U\+$#'&`g81XBYM)[78D48bgIjRScKcAnQs?tU`E5>^Zl?f^HFH'6M^g^?
%\nD-B%15HFXf(;B&=O3>)7fE(B#1:IO9\G:X'9qZ:V"=`_Yhd]`Lh=TM\7<Q`Jo%$%r2DO@9u7S9nGhd&DP'Z/M^ZS:&+(=/mM4[
%9'E,mlsa40PHZ$Y_M>N<7tm@"npWf-EX^gln2]bK$Kh^=.>[Ff=%*TZYE_(U\Ug4;YX@pf)G.blGZOBH:hTBZjb))qIL8+!3<?<;
%B2gTF@)k=U\Xp-jZuUu)A_AGU@BniJId>Q?TY/F@1+q.9,OC5QXZ3LGDFt%tB9!QtGme&=C.Y!u(-GH#<g:lh1km\h]gXh+1bO_K
%f;BnV<Sk`Dq!,B%qrbhc#(&]\IcW&N?*lO6F<G$[35sFg>Lt1_dJ=7\rGqGG6![K;JDGr?5/5VR22FT8H=N*Y3iHd4Q32gCPb"L.
%k]\k%nS_RX3>(@9:$&+"2/FEl!9TiPC3H:+\NJV`qGCE]+NVp,pLTeZ!(@[CWI,Q@()9=/_W``O8$X)aQd'o"79jPXV6R,%jW:j@
%I`U;XmPN&?\C<[Z>um-`Cu;pFY-Gm`:#U)F6!3t5d6W<o-\475Hn6%rS".?*'h=7%8]%VC$'LQ'B$E5\d1S5j*]>r7?itUK]pp/>
%EgT0jRa^<]c%Cp.M/``fP8)nIKDE4`0eAK3(S`?X/kT5=]U6S*'EU8S&^WYl(n'UJ_o<O&ND4tkjb*qU-'ba-M.B5KE[Q"ZRT43H
%Q5Ul\2^C+[d`cBK0l(1>=n/APAE&Yr$+/0eC^ZC_e9p##5=BM5]upn4W]jJ[HEg(?e0nn/MkCt(??-imUJr[%M5*o/DcQ(-Xn\-d
%`2SupN`0*4V=B4[U38<pjM:2(^Qf8+OL1A&%.:tmQJ*:*HSl"5(<N'#Va3LHJ-IrZ];JPh;W5Mr!G;a>(GZ9HM%p40Fda@sMB84V
%T:%MlTT<@;^4C:DKJ'Z5M51sLihW8qhhT\;U#JrU5LFiXA8hohWZ@/a0;6Mfgp,b`jIe4YgiRLuO]2$=jB]*OC<%k`*Q/ZApBEt;
%g>kl9jh)nQn0$GgJR%lT&.1QeiC^q(f@0qk_Q-,Q1ouSH-]?YXbQD,sXq7J'0$#@6?(^8;?ohJ'q&qB16D<e_#Gg,iKn@iBR*qf;
%Chd&rStNL[O/WBt_C<IH2i%^"Ap=N9Q\F:\cKAgd%q%2_g^,F.Q<*H-PK6!H#7r9s'@+7j31.;@Wj%Mjo_Gnqgf_-ubrMaTEsQDT
%-+,c%#__2'33O)n_]8q-DtiFH<p?Qpp<B(2kpse9d''LQ.4<*TF_%h?N61/Np1/q\^pn;Mkg"Iq78;C:FY?e:B8ub-<t1?\FG/"\
%7L7'%W=C_q(!Fii68Mg?<=%@PKqjC9$\r`hHZ<.c%\<RWb-f'LRiR4=+$W9/-t.'m\GOtV(u\K_At/,WR<>bWIP/jj!,eBfms)q=
%lRjD/G_bl7&>GI=]f7\9,]s<,\3I,E7U`g=>&_cq(R%t-42J==H5a)6&Xhi57O[Ztd9<OHabRHh?A@f;aQEgN_<'*i0bH<bTCle>
%)q!OTW_-YA7toSA$)mB/,\ON$n;;tIm<q1Q^gHtqA:)X=Fr!saIfDggB9o'!=qW;Y8n$:jkf6/%fTRQHo[;bk5m!%9,8Nkb!ss#j
%(7HYLM2B:UJ6%mS^RKf;D^:fF"Q*lqQ>brcSV$U77-Q;aPun(.7o`Wn^/RiVR[Trr_3E29=Uc-4M+(0mS\/4=96h8o;%(69$$urA
%^MLLE.YK(cIXaj!K.Z/$hO!_%jEa%W&UXt%38l9V?D8#un>+k5=(ZQS8fUC#a9b=,q0'.JDP/=PDK]C"OY-kM+LNEK^^f=obN2km
%C.n@<ZOiFCJ+@EjS]-=t%%`IJrXR<CcI4=b4,t/n3LnFikgt:KODg62KF@iqSA,TF>1Set8OX>0<2<`7W/I-VC@MbFZ<o@)2BRg-
%O-pW_]4qq[a&%2oKu>f582'WR6'\.b@cS9k_a:!4KAY^L:aBPknn.sM:S`8?Zdl0Q\J4bYctr"b#[*^H7kd@3j?:A92'R)QZ`-:8
%=SB)(R,IUS:"bb0I8`?Wl[c]0=1-8scX0=,KO&^aGi+:0UTPDbPA'TDJ=u1l0KVHkN"Y9?hfZ=Fjn.`OY-??3LK+>.j]j+gJOEgJ
%pm2_!#6/Y+jE.__Wc,q<MYuJF-57(erT,'.)I-*fm`[c1ddQ1;.5sht`>IRW]s3(%ne9-*o2J20>iE3GCP+K.,p)[ORl?W0+>$:s
%@4?Yg6kS=r!(*PLmQ<:Z`Nmo\lOOk-9aGgT'`@/scT;QV,U*0_CaSiK\Fp8:PtW!X/]M_-+dq:onf1AJ+^WE&P.+(T*dELbK9tEN
%:QP2<@C!4e*-D!JZ8LjBL&ql7qA/(kd\R-kb7eCHS/*oDj'gNdEON&%\!WI]+)a-nAY?]h%D+aR'IoLDfued*>89`'Tr,=jclmf0
%IE8fqORMHC;K\`K(.Kh>K%u8f/Weeo5$d&3%`<f9CX-fED5nul&M>FOQ:od$jPrZ\D'2FA:"q,A$XpVG>M1Z`"MbK4Dq-p9CdodA
%+.%I@qL=X-X8#*so]>tk':E3`=K)l]/@RhK@,,.P,FhYI+@bQ2(P2d^4=,#qlB:,M0$cN#&3jG)>%?IHj\`(RaolrO]<dj$Mjo8a
%>s&[YqBQ,r0#'Qu/K?XgRcjV%)fR$Lj]`[H&*g:;7Ko0l>7VlH(:H9fq,TB@(0sl$#i:0!\uG&jD(H"($kW((8_^Y58S.T0ZIcL;
%g+ErfE_?Fp[SCQ=1fW+DTQiSGM+kA$Gp,;*TKE=4j,S-_p-+.7FjcLiG<mW@'ai4X7#Pj;T=ZBNP`I=:j$)8%Ya.crZ;dFsf]lVG
%e,'##h%8pF,?R"<'C+lA8sK=&S>u#0GX=Qr3PQfM<iF@%BI54(&6pm7!YGQOH&`&KpDAjNf(eC69nFeiq_pEoJoI=h<DiYh2Z)2=
%%Y,<EF%-aINB/7_"1&3'V0+DDR)-qa&n^a19UdIBP1R[r$:H@Zj,q8ahO?<+"BNF,W(ng&hVa+LV-[&XdXk%4<$Zfc%pQ=NbCFsh
%GDCeHgPcJ[[R$MMGbXaDp3dXE8r`CW[+Q5R)(X30IKkm>5rg.D+$sk*2UkL$>qLc"D@HqBB=]>^(nk5!43<7JE//cHpD(t(<#^bT
%)u5L:A!.6Wl-<iQ*uh9Z9?__Pht+':$T)@6lH6BM^+JhaCZ"]f)O$B*V8-Mp=B"pTW)l;+IBLo_;-L-T(\Z*P6r%`Y4XNW"Ce#)S
%EBE$lVbI_L1l>?_:Ts?Ad.:g9^U;1unl'n<m%:7kb!\aaW,Vi>)')186CB-N9`0'kNi!pV;ZR$]hm_?8B1&A+IFc>M%A@N="oAp<
%gU`?pe'4ZV#XT1qI`k/g:3pG/i,*ZLou-r:KbjoH$4fFIX"GZ![i<EQ/5&O[K>O-1,XmKBOi-VR\</B+gITt#M0Eom/oD:ZTY]C>
%dZ_D>:i;G86Ue1/MDc<f(6NiKd9B^\TpGdJW5+ZkreN0>B*j8ZUQB2$XmrOS?D:Tlb-uW&ml+d\46127o&0Bf=H>"O/V#I8RsPP\
%eeLl/Wo,(n>MGGI&`A&aK9-II-jn,RrNNk8;^bfuO:DJ'h[s1f%-3c-P-rI[MB'MjF+gTNc#B+#EkI'p.IZ"jGH/0LCgEo"Q$IHa
%hkj$Kg1"Pf=0tP:^.\h0DB\X'>jr/UGCWa-5(e&@Rh&M[8ksl\N(sA,\*YC#Kj5.QcPb3a`*=mO6d)"0iA^BF^.ia)VHf+!+EUC\
%cA^''1m,K(7Dm@##0HkOW?tg`hPm';R)(qZNE!ogK9*rdgdh=p9?kL&RlQ/pp!5kVT3jUcN-%8k!\UrmODRP:]k6&]3tJV0>M1j+
%/&`tFTujirq749p<X"BeH_p%OJ=<VG(+?f'RDQc;co\rdR.HaXAX,rOSTm`gNocK3eH4\u,>u6fIZ[h_lU)o8/,3[)N6Vf`1kRO"
%Z'p7Rak_0'Kf1#"=Yd9k;Y;ID"V+G-*FNDhq,t0-+A,X7'ct:(;u!`_VHa-/XAEXtlD+c*$HmjOG1V[uc1cFAB:^7gnIEg^3=q(?
%kO[O![GNCRS!ic\WKP)>3]ANa[pb1iB7ZQ(G-14Mj;G<&Tmd#C*EbtCAT#R?kh5?B,Wf,0]OK5Rc6EtPoK*,I>p9sN@V-,lNZCHg
%.ti>m;<oEbHljR=W_GK3k@O$?/k!@,>"4\s6\bt-b]5^"']pl;dX?K*XN_YQ]<l[PERPVB[>YZS58c;<f56Us=It3"Thm1Z/qk0;
%5$OY_I)gecJW1sTkQu,"r?Ld6p"]6F8VY]!e*3MPko4;ZZd:L6;QstdI7(qGn\\pqK;+/b:-q9<<NLK].N*8]#@O7<KFcU#9'2cp
%cE?c\r^')e;tc:Jre/X_9)(,^&XSh>&QU<nNrda661S1H_h=RZB[Vp8>%7q=M\56NYs>q`lq]<P&]1g\lpB>rp<VIHL\KR]*3.[t
%.G-Mr3(IDk,g0YNj[.gQ(7apUBA!alSV]IPZS8f![\\`F(<8eV6p!_\r0TIh26&9tcaV?SIQ5@'_Ea$dIOU3&l\>(j8EZlh%';s`
%rBj+CaV&M`aS*8')rs+&/mN%X8&F?pI,)%\4Kn$E9HgCpmg(ObJUH&9LJ9a_B#=?RRs4OhX=Ct0A/4-%*m$7]<RF=I$i?t;:].W6
%imj47^5"]R[-%ZL2Ctuk7*PNDX]\1J;SjjU2j=5h2=Wb:g8i(EQlYQs^cg32BeYUg;'DH,4%MBj@$8^k@*3H/C7<r=o-OCY2sm#n
%W"q`R*RksV+*+b,LYQHHI$bY+)t'Cb]+g,<@5_-7I!\ALM?2;@h+&cY$E[1D#g.m3G#:<uI>2)P-SMo]C+d0`=32m[d?s\g5jI%S
%1P"2l:lN<aiQ><m2K.H(19jbC3:<9(%V#cipce[`&P<M:iuJbZi(-sZ<'cm""P1@51^,]aGP>hX"Xo.fo,o1aU\MN]gOC.eoL*j;
%p*DIOiiCF;F;0Tp%F0I77LVfTQ4FRVnf94K.]d/^-j"<[k%h-qIN:_B<,<,dVE;ua=R(]J;cg&H;K3[c!dVJ^OI2$;oh)s!8c:F_
%"LCe)%V0uU`@eVeosf3u`<L>J@W*Pj`_6*44di@F<-UNBn7Thj6b).%8+c)#q#>!q9m1BqP3]nja^In]B_MrmP%#[Z#D+9[/?k_O
%+@-T3J0P$.Ho5Mb,4\SF!EP<nW<j0D4E"U$mCTW8fih"rdB:3Ah\t+e^YeRQ?o7]Q%rAp$4Q(Z`<,4@dK9.Z=^SY;MG:5pu29FB`
%-k;2J=kbfihI5K1V:'Cd5spGXRQ.oe'W!LB>7>!qMHuNR[<6tX:ds-<Q:Lq_LrN]./V"^hq^ZJAfOJ;i0,XO6UXL8&K</t^a&Q6h
%&n328edV@gh_?lbH,$.\?%IWKT.rlF&t'DA)75R?NT@JVO>P4RL0<"1$!X#V]2u0?UTRT6iA]m!^00uSUl`iFdtu"PAr@7]3"H34
%+5>uF`&eWN-"Ae0(:?GSH99k\;-(C;Sg4*Hp_et$kKt63$R\Gp[rOUqPWm2Bm,JKIA+ihR*<HH@*emO"S[B=c[9Yr!s*l4MJ'O=[
%diS5EVeW7'qT!(8i7]hIr_+e3d9b9CIjl@c(r^mpHXh#mIV2a\kiW*D5M7h#]f2a[FA.e7Q]sp8AlJI+UJFYk;i1^>bU!+4&>d;W
%TWa(KD_lFLN>2ZiohWY]d_k9DjjHB)-"1FfXoFjd%Gm!_`Zh\gr_h(heCF-W*F[R(k5Z`#9qBSo?I79Xqp3q$a/K[)'R,F0*f/IZ
%`V;0f;?gXuad"V^1i%^NAdSu-QIR0j'G:]o5oYs(p>k5cjtVU9pgJBRXM]:9io(F:NR*ttcG-[nr_DJ[RsN0U@N#+%c8;SB9N%Sg
%T:j;5_liVZlq]GN>qA=$Za7EsBL3:l&B;0D7h<<Z2TXe&;@69P%k%NH.2+9la#jDVm@F6Fp]\I5H/lrk@PH2^-!Xgl1P#?jo&!(D
%PkL;H0LZru0])0AOn]iSj!;=[#p5T/'n8mY!]%3T`fe?)HB?8/1B=dA+YN%tI?IU/K*B#KR9=*6T@-aL7^H3n,e-QFW%"o-"u$uU
%82PYL;WuaK<U"25n]A?3[G$(%*Ws4a\.\Z7jAJf8M"YiF:+>E8db];Y1bEb>OArR,N;[XRrU@F28*1:1"L+Bn)YRcY0">J@C`'?Y
%4]&ZJn).b(EQ4S93=<WqCPJ2?Q73Zq8PA$eVPFMNpA(E\3H`Ta5.n0j`2e-7;e?leoWusTr0AR(P(7_<<GGW5+]`1:CfEM$@s1Q*
%JP6!QB6uJMKij+3MWI]k<nh"b$J-q>+J*UDZ/@'-V)6Zo7bh$EkFh"/`b-OGiMV)dTc(J[`0LrFp;uq%bK':gn#eO?($o_UXsI#)
%^Nh6grb`bSi%#bJm#Eg$;A6gB=iYEfCk7OC4+mf)2p;G/!DT!_39HAVIRU9JDD?1R\4/4!3HK3r9$E0_k]n1BWo7[M6W_Dg.i=f?
%XoJWR&GP4L.kA.!V]3ZimgB)/*+<[3'Vr"C0ZMP<]o*P0VmRVU9&<ta>V2`)-TPJc@)*RZ+TMQHF)V*2luWpa:jo8[mNDI[A3d"t
%j>sN;UsbmHI4A(r.uc]N1D)X^qngo<poSOachQOQnKGan<B&88JqrC<YeQ'mjN[F[L%o7iT?^=G[qL::agRgO!,LtrO=A0okhW6(
%$\^l:LS-c<n0f3-Fq-2m4UEE).A)cj:W+odKEbZ<(k'=UZ[WT-o%)$,d8IssUJ?#EK1&_1`-Bifo+?;I%&.-TFN20Wj*?:$%J9"Q
%%6\0CqA(<S;1R@$d8>m9\,dAV8rMtKMpXk'3@?n^"Ed\J*:KpWYq%i]*C$tF4,d0kcBp?NTL0-0B;5GP+]@oToarE>Kas$W3p?K*
%C#!dE+#qbV\+4AkMAqo%WX_]R)d^cEerM,_B#'nG>@Tr76^HlU@=PaYJ+S-oJ<Q[%oY_ur#*i'M=[NNri(4c.VGm1fd3o_`6h5?5
%q31XBd4&@4)$g%]@3g.m7cpg!3P5D/"2!4p.-R`c!.eA%+qh/WcBKS%'p:-#M.(TEs'3An"(ROb(_Ld,'hRRf4AEO8g1s7;2O0J6
%1mt@#;q"iYF]&qO>Ds+c_MEN)Ik0-(2QMi;esI(UZ3Oq+'_>?p5tXJm>2J>&QK^!P_-3i6_^Tf2#N!WEP-dl.4YU&<%A.@KP;'p*
%-OJV&jPMfC24'**+8@"gIYdrLa2_)>[caGEmIcN-Or@%X3bIlTM\ae:&.,,f8A?;l[og4f#"%or)R#caKop<B3`#WN_Ni+U,D`kp
%4$2F,hr>(ITLZlL=0A,P!-S@^+4m'BZsTR#QR"1(DcSLl(1eBa[ZP^jS\8AaY@K."N<0(X9(mTPd4?Oj8-5XO-17VjnppkAQ.K%K
%&G1nW]2^>km&^f$O>U6k6/^>4_N#o!NU)b]9BoNf;M(H_D)Ho_$EmS?PE<UTa#mPG;SZ&l^ur4+h:<^&C\=?QCmgb[C<A%ng-??<
%M_tj5?0F1[$'P@Q1"1g>5C6^:.bC@<!(O(?[TOH8S!WbaMFN5#6aX)jLbm:FaVNlE`;>&cH4rN'=_0b5$tl:G5U\Yh'Y5TCN^l'D
%bnec!:DLK^")!C\e8BsVKD(lAJInAUT7BRK,.%=VEW*].#ga'pqjh+*Eq"p`6Ma3>>b0FgBRlOY%'ZgD[R#RN,[uAh6eM(d,V:79
%])XHt:SRVk"&qa:h9k0_q'HPk/h2Eu[d0IcA(b&!%7t\"HX.Z07MZb\&Ad5RcM4+gn`Pdb"dY@+#8m1c:E6CSNu\eU&V"]LCEnCh
%H8$L)BRE.$!!F=/87@P`Oh.=Nm<1YhQW8Eb<ho$"j302E2Z3i[9)JqhR]Xk##n;Bm/NmbX&@,\G&,+OS/`G?I.\MZbRGrVBd>[",
%?=XQ_otX:('N[/sVp?#Gq]Io8XO:?\9G'f2FO)/j]3_B]PXkU$k(&eC'ol&kBX#_AJrC=9dY,T^%2urb^SHZHaHFO'F:+0tKkJ>$
%U1BFUW!"bD1+F!Q_u2j&H"I^:#pJ=!'s`$Aa]$<>RaM1&]MWTtZfb$c:]3QSVp<3dD"-5WfVZT-S)N5e<rb+Tr?YuUL""^'4k,;'
%:<fhLh]3k7A<LoVLf9$_O[S)G+]YOb0WJ%4OLJ<a!;@59`G3'u5[?+I_`0be"IRaoQL-bCeAM^AXr-R2@K=Cpm`g$08+J_XkWe&>
%r(-Yf$dG4A&pb+=`KTpW!i[*NBhthEh_HOqQ-FpmJaP!jlVcL:7,N)__^Jdpj#S&Io`1Q2?<+$LAt#WW_Q"N!DJ4JBpiq?&g"kQP
%\Wb$kYr+qn/3%a8NWnF)2kontS#/RekLHhn-?2TX^H.(,PgqlZ!(WTr(Cfc#d=\R@TSQ&+:pI@S>Fn(M-HR6-;e+spZX&>&.Zk?%
%m_]P&%4n]A?.)T>STlCX>k"ho3us^('*fI6Nfb!1Qm?Oh\])euN;O,1Fd2dUd-Nc-D8>%:.9[&Yp:ck:QOrm6:sC4i[;D+U6`<JY
%nr$CW8='UjANW#547:o6ne8Q=,WZO,I;oIKoIEuW4JWlFr;.n&]E;EjU%qrq-hr;<HQY]C#s1t*%HC#L%)@V$kXVdb#oIZ!P>Yq$
%JqOG=^7&j@O-+54,=$7ZL4'qFF,\tq`1$_,#&Q>9X(3>YO4VSk#OV.jgL2l?1i/-sB!@Q])T/tD$9[_Sl>J7gQI+44A-J_GB^gdh
%FB%4AeM;_8(a^()aI7Tp;)p5t*"q^lOd\`GC*AWAkI(mE)B]I*WuPJY%<e3!1J5%j`oe'\doEu3DN_N>HL`TsGol%4;5;N5=*ck7
%PIE.JpLN_*oK2P$l/bj2X2dgjc,86B,J91ddd(/fR?oj^+[Ond;9Yjr'f@ADR7J+Th7-_17#?\l+`1)I*oo%<%U4_;<TO"3Gb@Rj
%DJ/%e(J>I%g7pVReDp^biSO9\bF.gE@(C4Fr*28]f_\S]Q]S51$OM!@`]bVtoQrW<FEGSu;Ve]3014#>=Ic">Er0+<aKjN)7^V%?
%@b(h5MQbYB/ZsdOC/*>uEj!3]N+W(6K#nG`RHE:C1c+V6>)$f,I2poq/Mod=L%Zr@bd:]b7Q%OT28l4ReNUpd@.,H[>"mc]UQ`(`
%75lbBrC(%FG.MTH6I<8bkdPC.=JUDYbS27KkBd(s>e';+GpaV#\,R'nggBRsrLWcm8P\[0$[hXXk-Jcj$0lN<9=u!+]N-=)FB!,Q
%@Ot%_1[5@-rA'Vt7dM;HAO^h%d#J]FD4(QT"SS&'d;f&8*F*S47CMsoi1CV(V#$C0nO+D2DM`]*ha7a/2Zu!F7=5C$'BWId__QWW
%JZ&l(%DT"E,f441ph38hi>F,5bR&m/'S$IDLl$\1<n%fPj'@Ob?-#2-`u$fj1f/)CU$NoUIR6uBkc+FfH!!2jaao3]d3s<5;9CQ^
%;jCp>eCfR_%]$MMm7=XWe7AV&/+e2QFbp@\0HO317BI)\&rNT;OZt-hVu^o5b"ttaijNXb2PLn&/R^`h.79gZEBpbc>$^N.I&[DO
%#J5r2F5F?=@T59]r?9fTkGq@cilf>:HQ(]j`ZNc?Fa>qQjT($d^t%a*J9>4]9!MnrgM.[k[epR?!%+sm:/8dQEV.+=B53\q,:n6^
%La6o<W[%JO5"8o^]k$U/N"M"p68R?/0`pldBG8=\nL\(`&j8)oIgJ<#*N"-Yp*<&j52X-MO7+4:k')q_;K_T.5@Q])52t8a8$p8I
%5PVt'$L7JGV."KgXJ:HVo,1`Q5W]DZj"i/TQiR`VA8@[C/Ma#)aTfbobfW2`J^*):)F*!>7fblk4bI=Y)]bi=Up0?6R=W0iUV"Z#
%k%ZH1C3>8[n4=KI*B#_3j6H?7Uk@Y)k]Z=M4Y"0q;EXE>-cNE'NfG%_FS]c_WQfGeo?a0jhUT6_oU_+GMOI>RS\H1hq$V&UEA;4_
%btsMNa*S/ZTbUTSh<:&ijAM/Q'4q-CHOZ)+*dNp0<9OF,T,8fnEr.05*@430NhH-ZG0UNCigId/J_--\+B1?,J!5uT@?-jH5g.<p
%cWPdh%>_;;&LPZ5%*;onAtLA>XqD62Es5$fhKT/1Jnb7Y]$0Hu?'5NDDdA^>9+V$XX#>C:^MmKXigO2If*@%46rSA*F2s)3G$R6[
%g+\8Lh2de_0sabh@qN;@LlAP,Hk26fmRD\pU%PQVKk4rugKb.:5Vp&;Y3E*Z!aLGUo+HsDpnaVX1G3T7W!F7r>B@k$7@(D1K^"f1
%J]JG.=U91kpMQ6_a>,]#/h[iD.4[.gU#ad-is#a[`2`;@TZ#j*H[+5;RmLY23(hVQBgN?:qI-N`-H`B]G+94`c"29KlfZAJ[WU83
%%s8<U94n#)<`='$X2pT@*&ZVi!XQ?6lpP`)h4)1I[X>0EF@it?B'0T?jA77u@.PIMh6YDBqFT3TO1d7?BH8;Xg'e@S-pQEI2`#?$
%4g@t=J,q&KC_CjlbmsW/o^@D6M5bcthY:JWE]5h`-K>f0L.6u#3%6hb^K,,\c:U2O)3dk6Ie+<6:U*&G[r/5k#+!t%\(WGZb&hZ$
%@I21`[9F#)-%\Nc9VZV6RbAW;adhiVPacT\`tCR?p$=l73kgVse21m^Htb="fKf-JZ#WGfL67F]>\<0q>I^P\"Za%=U:T<PCY&ri
%lD*O1h0Nm1F,=Bsb^5nFHu>u]nY*sm.Z+5ak)DhO$199LRbmEXkUu=eP)rcI]s6JW7QTie2Kf0tl]3c@Onh%"Mhc6T'?'lZ^ud^8
%ET3TdLB/3pA?6,a[TZGO&IWf2KoZda0Orj:8'cUoRrp7(E-0U3]R-BWm3P6q#-!<8O589GO6F*;;;i>%'rpJlJ'`Ko-K$3l0@t(U
%Y/*WnrO8_4?s<>(F6T8:=%=Z.Icj?!j$2Wd)K&ID!VL5E#L\L@F%<,d@8>8\8L:Ol/n,h>:UHS,/GsF359fh']4!FJbpL.P<H[X'
%paRJVU,9(=HS?'^,jAmtjY_@o;3"@#*no0*"FVE#6,5Ig+Mf?PWG#Pgb/qm*jp9E$Jj#o[-6ObT6#1?-=BjeYi7u*Kq7]3pW7+*O
%Sjm4\&XUg'%\Dmm43H2AZ,`2-ZX"m7AW5!d/OnHQ]QNLB&Z<"dh[taWEY-_kqh.GECVs9?Lc;BpqULu'b7R9-8UJ5B]II?2C0!Mb
%Qi8Mu0k3TM=CGSL+Nco$HAN9e;&\Pmj'0h"nL`E8U[LD?a:I1@6<KoN\U*AF/s-A:*/Cau6_P6j3$4&U,VPCe)6oQ!NCLM9hd&E>
%IS-0nk"Bkcj:B1#)sLURj]4sPV:R35.b`:CrC_%b.g>,<0"<l*L0Dl#cW$j:V18N`G9<^f+tM>:g%"O?hj6/tEIa/l9NupnN\F3;
%Et2Tl"<IqWk^RSQr%G[qOJr_9e=jjQnhbGgR9m#sXP7VX&HU<Si+3?%,f8gUbsMlr0NZY%OM?(n06<YTEhi$J>j#M'*O/J8"-3(\
%PmBbL[$tj!am^q&in`Ws6K)mCm,quiK*8n"L9#s&Ff00gJOM-t7b[kT(e[k4(31Z;E`8X2%T36[V*Dmd\=(E&<lj:_b>P;hFB!gK
%nkt>BIi1P0Jm+Ub>S&;;EUK(jOm`\LfLj5OP:@b^?A,=?>84o`#A+b)8l>d:BP_GFiMFQDH"g1*;Y=FU't3`F=(P>#DV.-gr`JuG
%fMm>Qab<j<73=BFaB&[LJg[Ls5eoAX<5[UEqG8q1lS$]^`EfS7)_=rVrOHWLiX3lLa4HbIo!8IY0^Ug#Y(Y&Me\^O!?$nY`Ea`e;
%&dZ+c785CdHg8lCW7OoT#%u]i6j)Odb>32&I%!GIj#bO!'4sZ5I.3ZnBA0(-hGhQu*PEf[!32aK10X(T9t[L+Ud`n@TcF*=(?kYa
%a%mo6<-_*N>er3sY2J.h(l:Zlp"]aCmUgTd($NW:M4M+%0nXT#2K\rOZ)"NhF_gH3;#fq.B0roRjg1qL.42P?'\55n/4Kh0dui,S
%S1CHF7HVcsip^AU7`(H5H!9TLfYOo_;G:jR&R6,<AK6:V5W`0B4>Bos;^l8a^c\uU5h`a](gG?LQQ__!Ct^fjlu"7+%Q5Mp8g@!,
%ZOsi=6E5Ysr8./[IWK3&0-BuTm]7AefKCiWTWh(h#%s^MN-9j0#0+FYhJVO'%Xdt6\HH_08i(<k-fS"PoH`,uJ8M;\CdSEY6<K.t
%nC7H:#)+BrU090ja"AV76P/3kD+GCL\(=m<kG,*t[fDkOKVmPo!rJ+"(a_]Uk:7K-9o3W>-X_UMMK]OVGVQkJ.!.,bmumq"C3;*=
%Yhe/*]*MVK>RDEX<"D-N9mjB+NdQLk%M@Cub(.k`9":"Dl+NCcAml`V6E6r>Qt=!)Ir33)6n#:R09#2ci/:$n.I:r"WQ?!k?Hpt3
%Cp\Egl*/W]Jp%W"bN;aJmY]m/`.usE-a8:V!Qnt!b4N.V2KBQtVH3(Dq@:*r0oMmC+-P_#Itr1F+k;fVmD!<Xpi_5Y,h"nW9ofh?
%lUc2[GY9d!9cRJAMIM[Ko:n#Ildj;pC_G68-oh8k^C\$_k6X5NJDGYT5LFG/:!t?(Z_$C<.9YSX77`+1BSuT];(l-t7*c^rYN^-D
%%KJl;7m`=<d\WBTfe!YhGQN<VH$Lg.h#Aa1$;63#0OFd(mEp!.l!oG*Q>\KE=0E%XVF$%K`p.!AniGYk`W"Ib^h5^qM]gD,CM9@@
%a)_F,2-]nnOj/[OE[GD453N5#Y?X;ClK4^-Fg2=f_2)&e3N@mkf[*TBbgt:-,]aBO--!9tnUX@ua%3rm5S+/"(dX3,b1\N(4qT&*
%4rc"95k.s>Te++JJGe0@mmZFMO3l,ha=<Z\!Z4)A&Oo+:\i":uSI.;3GQ`g:nK;`r*-W6lIHf5Z\Q-L+e<YpYg_\1?m-bMmf!gt/
%1]?9uTZ.8iFP=4"a]\BPM]7,H$:-PET8`2V6L=_mI,3;GV3Ah[-&e-[B28Z*2&KP[^iB5o)!&b.qq;,GIPB/af*p:+EpDoij\Cts
%_eoT=7dbjQ_PlU[`u$Qh`MjM6/UC*2"@89or$a30g56h2U5oI<3FS^l6u"_h_@3j@SFLl+`(O=%"HLhSXA*NII_VE37l]RrlNAo2
%"<&<C8.=0XUOB%D;G]i[QFM<CagH;g\_W]6X8E3lGt3-JbnDZ]<Mc)GGW9.EjsH1:6$R.]p.H+\!JKaI7@O=)dj=rlCMZE09F=&7
%njon07,feSVH#YHf]&ce`if!=[@-iMV8ScqhoI#o*>0`!`,Nqj7UH/KfP$/0<]3+8`lbW#X0#HK=sZ=Ne9))rU>*cc9A"^[p;]26
%,-j_KB'PNWM:-k[4$+Er5MM6-#mXHOQ+P&.];fu3A-HH9>F`3@r>J&g/i*8IKMh]'h`72+iH+siYrD*"g_G>=4V[Sn[AiRK8d`-j
%Gdee4Ntleb3ac>rgtJ<<-`*&HU$G"9cfYubnJgZhhP*Eq@ij'VKlMqZq_:,cqLTiHjdQbLO7#2Zm"!%^`=6fI11r`KA&[:LA,7m-
%aE,O"e=L)Q;H;3HdfAgV^<I>e(iM1U>Ku@>rtj1ceOK2+CBPbZ--gtJ3:EG@&j]sYpfeS,.,dD06Bqa7,oSM`[VYnPm%I(-G!ngu
%O)[fJLdo6J3'sH",0d86J"c*bFS.sTmP-n,Rc>h^[]a0D8=j<A<6[E`gE,CRP)neX@G)[@m;+^Fn(Ne"T&ZuSi>03BpS^3oj,37J
%'+@)nAPb@\.s\XTrn(klU3?4E_$JPH3c^,,BTMZdao(`elqd9[=3,(*Q^@[kI9HNY\iYg>'lI1.h'GMjL>#-;ML0XR_NMAkKnZ1&
%J'6)naKkIXo<?RkG8rGtg0\bQ_Eikt!S^G=)#e@Z"?pF=<SO!4_pe3a2^DOG;@Yl*443*"UQ2]G"A8"!QeMRk+cK.Q7!CZUe/r2>
%=aO[idTIg!Z57`_+l.me41@7+p?2X7D4-'7WPif:&V*<<&P,eF%`T.B&e&8-.Zed#U1<2[]cMB*<C*F1od-:>Aqd2Dk%]n&U.$[0
%/KQtlkmWn)5[KK_MMRA5k,uY?12c)?`G2M^U8pf8D/Pdt:p1qB)V2>R#u@Xbh5`X2jFB;$0Xsj^@EOL!@td-L-,.8Gl7uL'"lCmX
%1#Tb_!pT%!On-s:I9f+2I4t&P4)uKND"`0X`M`5SlNl"soE1a[c*[hM;]DtQ!%=i+gNOrK:XI.$;"TFe4k>sA?/UGoLY[?c3o`:W
%\FKEqe&F+3p\N;mN3(1cq%XGG8CS#b`/Z9*=9h\<M&g<+P*hnJgV'Yp3ui/>!gI\L-*`(dD)S,]q_<UY)=ZQF5feq`Hr2`BdE#gg
%P?UNTGR$g8@Etq2@;?%ppq5DU!:2nr@c$OXT\YlC+Km1nUtp7)-"/o^0+("tQ\/D/s8<Z%%de$)Q'$#G4M_0tRZ*H,Fr2*?jTn1_
%d>in204?eQWO^C?-R/p<B<9)=ZCJ,O4lp0sSII\JOkSB%qQF3J%+'dEn&Gc$_IrZe/gPnt$;iqg+OWF4S+-f!CaGE.(bnPRg1.aJ
%^<bj+>4"X#0004N`1[6Shm3CZZ>6iTj`\$)pBe6bga>dMVuTfoa-g5-Z6]6dmQN+q#qst9lsJ:+g#(.s!;+\$$I`/Y\&SjSf@YEM
%P?Ft&hWu9O<qd%,@N`N2GlW]k!G[GC!d^n/QHqhUN''@6]ORI@drIYB'c?)Zppf+br$;La4bk7Cic$aBV%GaF7sm.2,&lb4YQ3V,
%]psbo4lU^Y<PCeOW@?r@B3ZA1LYNE8]:%rhj,^Fqe;dGh[!a?e>b)td'O8B!.1;cWcIttY,1(*hmolZnf>)6:Vj?Tu]`m^]Q^A_h
%OZ>]hNRo!:B8i76:(TBQ]&F9,h\+6h$0[M>T"#T(L_.ANla?Lm.QbX4mn*RAJ$7fGBItm8[.t'n!oXm(lKW;2,hh,/EH[fk7/"l/
%86J])Bm<j!k18@nQI,$KRP1X+Ib;[HP`\_6@FPsM75,N%H.m^;GW]t-I2#S@OYqQuLQ"Fi>dlD;`eE6pI:#<[2aMHE;q&-[k$asn
%L`EsueUTU4F+b/trY:,1IL%=sT0FIX=7-_o/i#KQ2>u4e8h(kN4uDSEY/nn5p+hW7ef>_/i4gYY$l?+2Pu+;$]^ITKpcdmpdfO5S
%e^0RC/Fg]WBJ$f.WA3536]8RD)+)O3Q4[-/N+.+I"%nl3b7Rl\816ZAbT2jWH]16&_;L&b"uMKpeD\e/"9eGQksut0HJDB+Ntc<;
%+i$]U;a;]TC!:8WM&1p5X"q7pfL?Q=N@.(UBnO71ibUqD=J`RQ!P"X23DuB!7Y2otAI]NWNtYt4;h8#bShVJ?SiB*uVMo5WW)0;W
%AWAf<i&!]Z.>8_.c#%E&#\,]HXf,8Vr_a_,k[,nbLk(&UGb>Us[J4N3KgYJa$:+HS_>_bF2UN&^2$CtB"e6aN!CTa%a*raDFmNIS
%p=AEZQhl&N@F-rl`9AQQNUHSB%*+^APmBP5D(O[;JGHZnHFRubA.7q2gS:QJARXk%frNm!+"EK0Fi@FbhOYIY/]pW6A_mksm9p,>
%9@N\q3r8t2j\#?\VT6h)N(H4)g\*X=ppbOZ\!pO(ZaaSn/fb[Y"Go7!]<EMAIL>'f'T:I#X>-"^-"JF=$Uo4QO&K.&hsJfnL7
%5>>3^n#f:+=EdG`QFc)51X]F/\8CD$5fd$)Zc(2#_ZuQ!g(:@Phu:1&LKKW<Bc0#H@D&THQd?OuNee;SE2"fP*PfFcO93O1Z<YO3
%Kjh^`.ZeWUl?gH+rf5?Xs$r>`rOFGcYs!%DR3X'm2)r;B^m%D)qNd:0I-"1N^T+bY``1V88A.G9WK=,r(d#oggg^Fbg6Sj&Q%dM>
%?7f4(f[9`5q@':K]4-XQb>5*%:V\eO/l*G.chGMq4K]-Rc_)@^3`;;'_`lAr.%@+b'@>X)XolsVp<;`;O-Ue_\g)NgkL9RP)SR^M
%K!+tcBQ'^PkaU*n)CN/$d"4M6ZN&LSdBCoDN\K;cJu(/u0!&KM;Z_+Zf'@I\-k=$g.CgVG3IXBDIlq;f*jUi9C-BJ2R=V!@R8O/"
%Sh;dKPEsXk5kHt0K=2GY;ik!C<?:1227qfJ$^]uRa4OWG^a-.M+=#faS?4YMS-b:3Wdug)6i:F^ru!g*SrY[bNB$X3QV;EUcn6Xb
%p0u6/rF<]i*i%I'oa#XP+(J5giXl0\d:-oE4[)?QV&j,Hp/+<A:LU;$9VbgQ%Wp%#Q3Vr?HdGH8p.1+:@Y>.Gff4"B;EiKOV?ns>
%,bLBMG+1XS'&\LK:`Xf0:6'Bn`qOoZk=B543p0KfBha:()pe'\cpO:Ue8JtmokG+?i3;RIOc_fsI"n$k</ZW2cFVSl^ZE97,>%<6
%XM_5G9_'KNdC<9##f"<AnP)Fp_YQ<CXPQD`<PbMo*?oH):OBip=_atQ'f0?^p+uWog[m^KOohPD@+&:i9^$FU(#h0b+A'(tKr9S9
%fCP@n"aB/iE(Pt`PNU3`&fHiOdGef^PQ'-1WrPD6T;trjL,d"aSFC9W\T3qJ&@t])']0esjgSIP&BWhfrI<')<eg#_j<N;%7g.Yi
%gQUR#0!f3lGQ9aA^q&G^K9XG*7P$<?klFhp;0;MSoo1DP%Gce3H5-c01nU/-a1cP]lL7\p)+KKNkod]!:2PTWeaZTp83kEp[hRhQ
%]ttY"ig,qL4h;/dp(CpFau+CopXDBLq3i0N5Od0?3WY<CSXp&]900<6RqpP_/*B[V8/rA([JMjCEUfQus71q%CQR+Qe%3GNg3%bg
%-qN:^.V0&6<FS,.cul*9(Zh`;V,MrqcZ#>VJkO3\dJUBASlbRnGLTI97uMAWB]*="Q1sq+^*[WD%t=0BWOll%5=J'8ONOuaZ)(sd
%+*"FE!/!6F?9Pp>?R&6>e07%Gm:QUX2)6R6dk;C0Z^1+!2Of]ai%Yi&'=.tU=Tg(o=X\VcBgkPHF='n9\S4!9Bd_=PL%kFm8uWs_
%EB_%*MpUKtqn;R]H<a=KE<k@fH(X4$Sh;nT0'R#Ts%8ia9c#d:njC/*$.:oaX%,L;`s!*O\#Bdo^fhK<,TfKX^isEpj>h-'1;R,<
%`\0HlXX%AM\=DK"kZnnHo:d;HZ3]tc!B3@[o7Z/E!K.3l0Co%`q/hkl<s2"reM%8<-Iguhi,QfiKP/ak#AD*c[m\Xm`/Djq94&3V
%hobc3:2Buj<Q-N?45+IT:B-@er0"E;V:UmEO2IRWaRLl\54TSEUgTSc0u37PcQ?CplL5Qjc#'DA8)7<=&NSIs6Im-u&Omct0%"+$
%mJ33/VTHUCkJBY/*&Y,14@%JpAX8hO?l#T\G$PB#Z)245GEZ:hTsf#n&_!(4.q-o<a$h\nf6p7phT6\>2ia'PO)[UkB-:ojUk%)A
%\aHYUEiqmFm4;Br"NA)h0afcp<A$^%=Dc'7I[ro.@`\2)B(3`n^/DpZR^"7`7fm08F5>Hd1^Gg2@VKT)nYGElLs\-5(<HrI2"LS=
%a*89R).^.N4mlgC'c5KhQUZ\[O(r9j6:_Ds3,15"*&KZV(EO!o*U?iX-^GFe&1@kA&ZPOt))9_QfJ*Bd:5V35&#^JpP23)eY]f.A
%QiD/B7`pjSE=(X#N0!7;f^8h"U&6]YjH+^#0Ns`hf3_WPOchbgmY<#0+g&s^]cYi+$J)iW5fOH:c727*Ol>B3QIjrnI#>OgZS]XA
%TDIUP-@l-#PWr9E;c#2VLuAiRf9Nmk,Ur&f9>HY7<@G^;l$$L"=ru^F`g^%g>tS8:Q*QnSpf'^07"C:]\>ml7P(o,MH]es#70Aq/
%(sD3SCqi[PL,O`/KKqK%mBM5\+e00mZpPTEoJq#iLFQteH1/Sj[P=io,TX$X/XZUAS8%='d_8Z4nsTFkZuNV1LC3s5_I%[F>epI3
%W;uXs2226aS#%8ZkcX&n;W?r4#;[7(A<btS2r&4o]Z$[^e8`C)J<aHQ>7u#X]5Q'9Td;qUD'/XtG7VF"H:jQ^B66jdM+(Li%AJ&A
%m_(:QRhPT#2Q!&-ls\IB9oGg2.SDo->4DaiKf"T",SldUpRQl9?9sbF/4YM^"KRR8e1?ZCU6+or#Rne>C(k67JV8N`)$1sT0BH[H
%8YT_ZG3.dm'JPZSW0'IGW#0%3Z1@DtC9WB.'4IP[j#agY&'p7kae5F>#nsXK*/pLH1PXI`_6tu$0K*cI'#ZIo"lmfi8O%g5!;ffB
%MrZ`(0L=Bj6sjeAZT<Pg(?[L"2I;Sl>`)OufY\rkc1d5.7lCfmBEaH_Fe("&47;eu9<9'g*^])5B5T6"cu:$T2rQn7WiT1;1BO;^
%6WPSXjt-`]798n,aYW9R.(W_&P]+3pN9V?n6lqE)<ou!orN&_]9`hQ.Y*5iI$"bXA+c2$<YrMr%XgO4BAPZqc.ul`?S^oQPg:fL`
%OCTDqS)!aufX(Dp7T1TAJ.=60P=VTp/=3\a3t[Oj[7iGm5QA$^2jPgG:jW,PPstgGmN$l<(ul.X>gqqtfR[ctr3>h64>+6@V5Z)h
%pF?jh`Fg"h>#k9Wa>UH$O`93?6n4l3`"Ua]k#`X?U#D\efQ0b=5c#`N;CPNMjhVuDcAI3o'fdft@LW=GN9+U&F%oUQFdjS57)IgQ
%'S;"7,kuu)mE5l7NFS$3=,g-OF@ErY"LJOpR3to<0WA[1Krj*dpe1PT)u6s,hg[h\ofAu7jPMHHVEf`-(M'?rKPaMLS:<cgGd?W^
%,5,o$J+YU-KM1E):)6`93J('FR`&&4cN3'-mI=j_[gqW87e>t%l)W,tdSO4R,E\\d)&rRjPCsqRlJ3t<X2A;aH`!,B&`4Yg(lc.+
%M-]GM'X,PUo%ps)gW1<d2`ee%3jQApU7",t-eOtQ2S.spW9JO"LC59>0HL!;^p*C$\L3UKk"]R?Gq\hgj@0sD?_Vju"-H?c3o/Ht
%15&MOoiX<P7=iimS>CB]W"Q8.9MRP,$6R+;>u^/U8s8oQCOnP88r7m^qAl:Qdd#r-Z!>=X1,JA.VJ;@,C-k0BK(==tg+d;A&O!Hq
%-Plk;Lj6.Ij2>#o(R+:h][U^`$_7!u,drDt0C-)b-[QjY\P0B(=%Dplq,(JX?.?L4_8h7j<?lZVkr\aWY"!0ahKrR?^.V$Ge4j-5
%Gru2,`6Cjs-ESq%%7O?Gk=8bh-YXm\;DrYc_)BGl6ec`Ih!!uo@OWr$2,@_5YnBVW#U:uN]+77UU'kuZ0p='`/E&UMadNsKb1d:9
%?1]&/IUqYm$0`gbSWV2qRi6FJZn&Vcmo]0'gCXpbHt43HG=5QNgQiqJh'Xt8%?DD'^+pr^VGO)3kLb_X?8M$%3Gc(GHH]cZcTj7-
%,q>7sYs"us;BY%Zi9SrTc)`baq\Cb$Q]SelEVh665dVOjEj6#kNUP\arY/)Qon7_?8T.6a>?ake(HqVn5B\oofqY_PPd,JKd[K0N
%l'na>\q"e8hSTTN)g6B3b7Ql9nJ,1AA!J[>'K?:rYQZt)DWijN?Ajd3'W`6ZGV$0g:TNXN?pPMS29(Cb[lHALA_<;=OpI[^cIF@r
%--B[Xi\d%=r>E[:M@E.WQN568cJ]aPRe[RQ[deD^<>!=g#/4'Y;?OHah0A@5bQoh,//lk,I5-%WS4CrNA)3aOkM^>WQub8ODW6=9
%(4>;%Jd3F//7j2%L_sD-\kiG[YdL:WA.G&iV@8RB3Ehn/!b1aUFat[RN4@*.T(Z<hXZ]eIfm;M&[FFP0S5F7FR<%^)OEATI"O=G7
%_g)(Lr<Rf.G4r?>U]>^:3*tMl8,G:"lgM*QF>O8(`K=$ZM1WfWG;j*(T(QDHh[*aJ6;R.dM?U-'OHC78RCjck)&#lDB-D&-]f]4u
%=nPt`Q'Q5eM)*T3jKA@<:,>D;VKe=@<Sp9X*@61G)7[5E$V41F(<b^;T7Ap_^Di>PCrBa?DigBHpcttcVZ*A60Aabc,AbLc=OF5k
%G(_*6aOV]_!mq7;'c-XrS3pn87&gkp^N,msW>R$?h<H_X=;b#nmV!t<A'bi3>:Qd@INe50+g7oEK(Qu_H$_kWQ'kou'/PV0V*fel
%RN\C[(?SBQG)tj>i&J,X'4d<acUopeQgWR&!3_=g3AKml0IMfO5uN(e85j1TJJVmg8B*4i'QS@l7O+Hege(9*P&L@cQMX1ugBY+i
%_5A*3SauJJO>9fC/F*nC0feKZp#c)_Rhjpq(.^3IS3@all,M]hO?m@ZmC%"fj#XZF<4Ps[;)-=A'beqUg+peEr><pm[3BO=NnCjm
%UtET.o7LcqR*VYh<g*U<Q`.aB/PW6/k#t=D7E%g`:Bd$,A_]=o"HaDeMlagC8]he)0,\67)*B2mgXO7-SPu75KYiQu4e/35.EP49
%>N)K?$2-UJT*#J"NXVR8)uG]"9""#5/0WSi+]G'A_.MgR:(K&;WU[4t5:1\08N'-+WTeNm2%L9;;mi0="P.<79?c1#HFpCgR*<Go
%dtU*s*9pX6egMo/[F8-P!f_P1n0rb*7P4%?X;-cc.lh%gCi1H]=JX`L'<Wh=e&*AJ?J>P\g.rmkS8?Ft]h:8SOYa0-9TUK@+Z]h&
%ML:OFgt4mo]gf3oF\%+&1c&G5"Plm,P"=nVJPU@*Dr+L?n9$jqep56_,Ms3f26t159<*aK<NN-c-!1rn_2s,Gpp$05r,E^L?cDL!
%nYfpeiL90^"$NY/>n\&q9mrua9JY]XZYfGsD5WI\)SBr7L=!NBGui]:s%Z^1Q@uN-Nh6?!Lc#o8HSYaMP-o;qi?Z*O".E/*&R;ih
%fiL9fmm;82M9)VX8pERoH<(_+!:_Cj(kbLJKs3f$ZH!@<3\t"slu'4QY98&KjD'5I8PRRE>VNX#X\V&'_N24jNSo!HU"^%K/`T7c
%0u]H_[e^$]SG?,klk-LU@Zi/a(4^`*arId%+7NkeRsQS'Xm#kp8Z=7G8VC'JF8@BLKKDn&nAs.e`8KYg<nd[q<djhj%2DKt\m0\8
%dZLbs4(nHSkIdOg&OZ!r-Wu(.G"'4OJ=i@LCSkGJ5j0t1!Gp"dNo9?QG(mi4\2!`64.F8NB3?ILigg-Y]bKWO,Es)1!eLCE'H*f@
%0jLKT(4-/u<Ok_!0-2=f3#-p44Te(dSf)]^iO,[IS05Nf[K9TQ@:[ufN/%8*Fa&R5:%NKt"oHT0P.O20dU&HJiOl2bU#r)FOqk/g
%e3K<+q&fJf#SM:^=Ch'kpC,6jA/SZ5a&JV`h@\Q.LmMmLaak"+KWiEokBa@GDMN:D2C(HT.K8jY*ZtF7V/mL8Xja3q;Ac/NJb-4.
%HJ&t+b#t?Yqp>@le8076EUe)r3g1Y-2h+]5)<o=Q!D'<4(h/uHULGbiMS3b-8JhlZauu!d"O@^67T]"Fa<lai0klu^Z>Zi>l;ea<
%"#"*d.+sl=1K@WhTjh$UPS09>W8*ro)b9I#fIq%tbAsi&<JUM*rVAc(b;W5^gH7`Big9FuYs3:g?2ZlU3k13da2fj=U[O21G>:4G
%B(L0rD9$re/S\j$h3+\'BnJkq&3,Y]2WPM1_sgBr%kZ)521=%S51`;h9'$c</0A_Tca4]7pZhP?gN`pl^VO=)%@O&TX<9M2oOrN%
%%t=k`Q:cUe/N/K\0onDFaZqVbkcM!dfV@bS#O;I1`NC0Y1+!,U=0UEM+sYg006`<Qgbc\ipt!qrW9_i'Kq%nmrEc_1l4-pnX5Ros
%N"BG=<r$=>6E3:X2":m*&aLiKmefo]?Qo3(Vk"%mC]1ZJ!+@8s@67<o!a:_rnW"FOq5$u0A5>VPc)-tu$)hiKG_I*iVPsVD];Om[
%7MZI__pUm\VInrlT9B"L3*QN5ke#FsGHk;DJ?)/8=dQ,D[tZil(*i%)mO$`"N;`,19s84'f$\e,p/D&c_N_g&=nQ8/l/n(!mO$=)
%FA,=5.4W5+mHDaAn.,F-e(V6oYR.hP<1.I`K5cCPP9SfqYgk/nTaipZ2(@>0l#)Fkn>tl[_EfjK`7G>Q\MZAY!"jjZ[Z)l-kUjJ\
%rGse[ojY4AWh1_3dYerieWG`nUK[.,k>t6>hJ%O649ci7C.Lr[h?@_j6/XQc7Up]kRkp3K>6IS1?^F%4V>E3g:YQ5(GU03gZi62?
%)mWdb`C#j-Emn*HemUI"X(dX:dI$Q5Fd8KfY`b=AQ:3PLBFI"TEM?=.B)LA?,Mei"38!$KqZRo%p5"6ee)1KYrF;(pG^YrKfJ,34
%#/!R\`\/\1nWTmO`sV6=-AJnH#='M"I>37%`2*fUOK7`(n4J@Soq7p&[Z5*edR6hb#SKLE(=5-?72t'b25Pc=>!b[!D?5@,&d3*r
%9.p&PkSW_Ne<TCQP-&\XCt*<1>fchlM_'W=_QZrhe40,0iPn&qr*;MIk8uh(K+s\uI(IC:Pi2XMa^jNbDG9/&3ubjLAW\6%dR.W.
%/I8O^_[NR&H.?p\6%bjHEnga4Vnd^.cXRkmiPesUBnR8c[6u&GOs?fe!)Hk9-uOh]Ot4rApT>nk@X(Lb@lO5.a@D%l&6GctjN+eN
%Y5j-t&o^PXqKs_fEl*(4f^KL.MBrRYWMGUa'*%^;-_/FuEnZ8R]1m!AjZf6?:1.aR-fM0j\4)N%&u.'38[dM4's'_9.HQht8::]?
%W(M:DfQae2&WL_RSU^]g/1%i=Yd,Dudt<aO)H^2_&''_M<o&7^m3Rl:W(]4d$tRGK?\[_ghe?G[1FR=`-`9nARCNIoKmukC+eR;X
%dqM#4R@I26];e/cERXVa?coZmrGpa<7kVp<qhbUZX\P-_&arLi%%A3Fo`lP10^^G^6:gYW2u-F">cX`d7*a(G0nh)Vr$<cPP-KX6
%*,U@rQM<)ZU;K[\*Kgq1:#]Cb;k]DM#6(X,U.QM?=q^Xh*Um@d31;bTgkJikH86mZ[#CsBDGec6G',Ff@PUd9LSc%8\7Ok7m<K?>
%_ZQ%9Kpq=aWk".4Hos0QaD72[ZbgL?*o1/GF[Hco>3/rlRbJGm)a>bcl+t)jZmU^DZgoK<32iG]ALKK5/h-JBB#4l)ljma/J%C)'
%\fFHemU28'#p_<d44>cT2*K.@Wlga$T>FtqR`(<BbA1"b=DH@b@PLR`VT\&[)=JPu>DjJeX8.Yg<lnj=o`XnW-0-cdht7!"Q^G_e
%%+)QI#.u3>(@M>fY,u-!)MS)4d@/3r<dD&Jqh_@(gY!5D=,ZUVF>I.P*t'T;r'2A+8l&C(CCmQtY3_n3HZ1k:.FoM(6^+Su4#nQV
%-<q;om.g#6NL*]saimW4%/SNcWU5Xedr%K+j=+A"n`pY)g@C/8-$_LmH?YJ[b>a.P$3V8i=O<5;*RU!rSSOa5[j)IdL9<Ff"JZ^Z
%8Ho^;a(-,+T*7A!Ac%\X8jZLD6m*j$3g$Au$2gqMA*j"d*=MMhhD^rCC(cf(>?#pHZ4hus1UIHkd-ATg"_[q(M0]iM/.mN(nMV[$
%;>E\!Qu*,3Z/`\agjS*1biWJ#07+L49JB6M:3,K_m[bVsP>Q5K,pMi+A8RZBYJ$cNr(G[&]7:%XRcBR-jT:7h>9nG>/dEV@#iB*7
%qr.&R4Btn+$"*nT31G=UgI=3O0X@kW/LpEmf$Y3NlR4>H9_f1/<[F/((jQ=5Mg+(oc\Vq9/%h5N82)dR)qe);=&/2hT*bXIGX7Z7
%kW1BT"YoP*De>["%&=ZRDm(65A270&`DB0g_PrNbb=6_c/7NpQ=CP7`rV?C#]Hb39@"tCI-0d_!AZLu6<]TiaID`-d5M4104[`O1
%d`<#G'71@r:K4ma:@lFZ.3eb'`21\</!3W)h-3l;RO)_CN1h!@ZQJkZ"&>'.i!<FZc\p.M(E*P]V@,N:CeD475IO-@UTB@24hCsO
%TF8`o*q6V3?,Z>lAZ7e7Mp`3IeF&3pQDdP"K6J`GO"csG()6R<fEd!o>GnO?4P:'_r6J#ZXH`#j2\']5@HYC,LH\WV4#!Su7,k$=
%#ce1'35b:n`hVFb)p*r2<qGHf8o!SX,*r:GS76u%AV(@_0^Sk-aIf>9e$;o&MAOlGFlGFI'%R+2d<1Aj>0/!SNgCED-rS035DVO+
%M(->6U%2fQ,+I#G$2u,]R*]0(*I^Qm8$tH#Q!N2f"4!hc6Dl.?@![pZ>52VM8:'b::PlTaT3PGC6&SdaM<"tL6MV&"euX)V/-nb#
%2;4?KcX+MS)s8S9JcH6%4P-MKp;5IO/k>`#3jQSt5BjW#7BR)U[1BTITRf6$--K&mDd.^V*i9j[E%(MQdMnGS'TL5,U+YH0:XtH@
%GKV<tSKPuPYhV$><,3k6$etrhArpF22bJ#[,@M)'Gm0!$3=4cmikiF_?3;/sC"pb:dmUcEFPWp2gmV>a6CRAo/=Z)>Wie"-%Poc<
%Q4=M#X6&f+O8OI$(YJ"&RS&A<8csW7TbL3$7r2ZSM=Yso=4,?p7?0`NWacP'G"#5H<ErT?]`Ha6kmHoc.5UO&6s&'5l<Y2F5oP!h
%MHPYqZKZp?-.:nHUbZ[^cWb(%-^-O8&[d'D8$E`4_Ga`f"nbDtCg$u_AZ0L8I;5X94VLdQo,MsrFu=1^RrDOXd:OZoZ&H9*5Ei[%
%P1OanetKg*EX::HA@jYIJ>QNtP_F0!))nZo=buUmSk6.oYT[GX$,GBF!#00]"\TUG13Xu]Y-Jc77:`Ek+S$>%:um)BF9lB$`6B[3
%\K)Di0WL_o]8!(`Mjlq0,1%0D.?.O![m\1QfpA:KJ@e]Yn^AEG?g>-8_^)hhV:g!G20J[;CD+4b8QHOWCIU@N'TVL*h)Kc0B4U^Z
%=&R(Q_!)[CG'-@`i2&SgatKI?@Z2-#"Fc3njBEn8VW]6Yns8q5@l,\i2+MP?B`B^Hp!8uC]79T7s5C`mh&lWWs7Yp\_nZ6#hu*H1
%rR_)4msfW^`Lm1bTDln2omctms87*@jZib3TDtP`qTGN1e";*fe$&fGTDnO[s7nnYh9UTMqu?Y>rqGLbs5r5=q9QhRP6UfO^\]ZK
%s75@Qnb7P1-P,d\_r(HQTDeArlQ?,NJ,7XOr8PJElH=)ss7S,GFo1R@5hF9\r#bRgre#rXpOE/q`c(mrK\uQks5Mr8j"p8prqj2)
%o&mdd+9+Q9;ucS^rn$h*^Q[4ijGU*#OB=D;=rX<()V5Ygeq+Wo.s5uW!ulA2!b<F#UE`3M%\nAD1);R:]1+t6\#)4/.)tV%W#3j^
%SKf<4p#B&C;,Ig.&%:\XD&qtZRrD]F#88X[G_abrQI;2@jF#?`0GEhl1:g"B=4N,B,ud:l#XuHj9Gg>lT.XV@,LCh$b!#7E2gLK5
%cX7Y^THTAT6eP!FA1?)dRf4fLG);CEUEL.`OphV'Wj*TNUEUbtL+Y5^\W/<L4"s2E*95;"l4blWL!8au9fC`S&6%XWS]H.a%uMs#
%I*J]B%dmkq!E8&:XUOhudLG_Aajg$A5#^$F&S?DFcB\S[JcXlU*9]1u89'h)UkU*30u?rr,95!DL^6>dfogGg$Tj/P+Ot`jR^hS#
%i[k<K-[qR/'iNEFi?N%]/'^Cn[ufoDGuYb9#[nIh'C&/,$,s<\FYdjk./8%;+YYtEoH!oJG&\9@?ER01#K7*5jH[%r(Hm`i0R!g4
%HfQIR#hnYJ_77MYo<K*47].K1XV.AEYcC@sqlFfk.2EPA6aiW7`pLPI+nXCY!;-.#B(ZlqQYO?jZ&TL$YcH346b$]g5V`ha00M5>
%%(-R)$![(pCW*bi@BUs<B`UN8qt;);)_B!qdT\e*jYc0N=ZMn74Yfoac`?2q56pN5ELTr*=Q5D(>.RFQkVIn6JZYc!,hAoM'<jre
%7'@FCOZPluF*/,^iU@i7DVGc;XDQ\!9>M_ci"61c'<'_u4EJe#BhJ((mtqe(m!,MGd8HO_-l-:NNfj#Y+uAf$q$<B39aQN]/%-Qg
%nA71E223UaW]FpU]lfrV80O7KpSgUg'Yr,H!O12ga_op#G.c'Jl@X0]JcB$onptjd:dRi^dA[,jkW^b[GWpV)L@W2X9dQp/7\jb.
%j@XLLngI=7XHak?(C+YqGVNA$$oa,Ea/-;_6P.i]#uCe!:OtW_!YWPjKdP2/eeMh?Ki-'g]WTb%`t![tMOaF&bp*s)%i&s'KAlsh
%lG`f661#o_,C&IIMahhe-jm>18T,HMG_q,Qd==AYbO(6kV`$7CmE[0V-1'nAJdgZhbM`tIIkinsO2hN.+pP!n$o1^\"aI4t@fXEq
%-8cLPl`'gR73h,RE`>j7fC&G6(!l&eHu,aG8c,/bT1;('FcZR<pjXn>Dc!'j0$O\o>>Y87@?8bW(B'Lrj<XUNm"2^PIIV<hrF6RJ
%BNtUqisscj&OA!*'i5kk]UL>+Bn#?1.2<WiVqZnREh8XB3ItukWnQZiHrVuU=kspSNiR#<A-]"L"b<C=0OA'A-?8jkK2(CHa-&5I
%I"F;PEhb@e>6dIWj;*1XE%3Bnmso@#1NG[q04IAHR#d[AEPshBL6oG3eUVQF'"S]=IE[]+OCDO(Zh^=+^FW$3pX0b+S`$AQEM+:s
%<<f@0&_33[0u?_q,1*7JOUsISM4V8sFS&g&KSB!2,]>jG(jcs=%BD`>NVbZTfj/l4S*P#Md!jDj;.<^3g.;H:^Xl4,AO7]E.g,"K
%E@2NmV_XUo2[#=36C[-in>[#*F,Q\Ld7o&q=+Km2@`YgE4KckB=Yj!X'Z?a]FLM$M&>cUCqisFN&M=hVh\)LO!%mHYRnC7IMP+oe
%.om*gM!r9AHb4qrr<cWAEfsa179RgP$Z&1;B"S4R\ctM]@=K>+ML3<I3[#?U2CE\990e9XT9*UIA%s\1hK92<>Q;2.m^N-uo(0:&
%l#TF#DSecu>^Q@i']H7!$guNTrEH$]O"'rs\.uYFF=BWuaQGUQ!jZW;PQ$fI:j%HF0e$CHqRY`!XI:"O4YM'f:.uIc4^W*u,Fngr
%:,c/T:`^7-bIR13AXqbg3&b8R8@h\F$B/3-j*HC*Q\V+s0c?8;+?c&:-#%29?i7fC%]Bu`Wm`EP/n1Jl):8&QDJYFQT7C(ms6miN
%@3K@4O]A$NMIg$<A$+Ro$qsge#dolel=0@#4#*h8I]tS?)!=#Q#3p\eMmmAqc%@$PbAXXTnW3S(f+T#aGZP$Rq'hon]$!9qU(.n)
%Y37=9R&uG6rr7UC@5a+>rn#(Pc6dk@#&5K@??bVDE;,URJnQI+9,W:Z]n!`X.S:UXS7Q/\)!W@OS\eR8G7pFfePGaUjB8M#X<oP6
%nR*/Aq?SAR%[p_Yr&>N^p]gWD[mX=`r>H/m>:<Eld?Lul.:BdCepRe@[a/QiaIhn&7X6F1=n_@!S>YMke--=<2k5rlBljQiP)4kO
%dA-gK"T]6EMR-#Om\6&oK95IGU2<`WiK@YEe/_iNj*A?CC]-Pf:d=@Tp3#S^.ZM2bnYJ4=Pp7"5l!G`KI)JY`W1)[A&'L?1RMEqD
%jMNq[d^.PT))b9Z@Ga/fG=[=Q8<$_H87YKpR(]p\75"0?iT_*t5Hi22M]SQmD@,0u+`^%mY:kODVH'jJ9=:G7O>iK*Y)p=5+)o^D
%Fo,D(*S`um-9j8P8AsUnJ6VQR)GBe#Z\?(aRgc0)r)>a<mJo,H[4\PorLG1-Gq$%uUruZ!jq4.$ZR!DZ.0e5lhUgeVd?Dq8qJ;\S
%<@A3ulqg&1MWQ+1A]%E!U+"#'&JK!N=ukXa/]:ib.0Zm:=oa+o,%^"J$4@BZZP8\PQ@O^QCY:>ceIW!t6?'sNBEqDDlnE"_Jb(hU
%feO;c2S5GVbc3aQB?V6A`5,b?H/D='nMEl#9V9[<HLQO-dk<)=H>+A8eJcRS,9o8A,lu8T[Ok8.+Ant`=NU0SSigft\dhA(""%OD
%+ZK-M#rQu$BV.C/.CcP[K+=W3@2iW6Zo*@:^;U(nM4QbI4Z"3?g:2+K[&?KnEq*PUjn:9XWt8[og>=Y`hHSuNZi8pq;:n*?rnq.\
%,lqt$eJssIhGLlcQIP+(aE*J1RkD'o`\2og:F]O(O1QH9Yg0)U4-M^=?/7!DJB::Bo>`F;s"jId<p4ai(B*Ep1(,0Vl"5X"Uq\^%
%]iBi6VAM>UC\RgEC3F'$E,0f@;bGR*9)I+0-%R0sMaVR)!';eK#4S$P.1-6[I]LqO_L"$k7[0;[S8na_!43DZbE.WTa9AR.ig)Sc
%kN9iBid"O9KdaIU3CehMb4c4'_oBfK0pH@m68SNLg586Dj%p^/s.M[ZC@+:BMm-_6^t$Oa6L9,(HX$$dM2"XIbr(#$ERqG-4.>-k
%>KB0K9U@SlrrVsm71@9'J-!Y+!BNU"LgW)3+-NNGg51H[r)s[9It.uP?NQkdBSU4ifE#kITdfjRfFpm;Q;SumK6E14SF=.<j\ogk
%=8*at8Apl^!/t=R+=HSl@tQ9u4VEM"4ZW6[f<L'-I<Y)RF6)I]b5JI[f):;1-7n>ocbSk7Qm.jJ4Mk^$KX$H8&boT%6oC^?O(V:u
%.3-pE(!V;!?a*pi9kE'k\%WQYNW]1,GA7=[ZTn?1*?7D[(@<:,iJTC4b>cW-5pSZ>2*@GtKF%-Q_UXm0"aq4O+cY[8gM>29bq>51
%Q`HO>KgpZ&>YLN:B*5]94S7Bd!G5f+^5+L[KB8Hm<fiET+o<DBk`Fc#r<U23-#,ki+iQa05)Ud&pKmGgd>IDNW&fOF`,;[oJC,ED
%lc4Q^0rdDt,QjC@LM0af2Ygt-LrZ>r"C"Df^s*uT!XCtXiO=%8JZ3;f5k#TCd@\T+i>V,=[[(5:Vc,UCh7ih#j>cem+AC\lER=XD
%UFgs:iWB%/)&.%5H:#lt2+f,<oR=4mk^)#)0,[5r2#q<X1EhoBJQc0;7ArM^gnY<VKq8(QN0J(oh9(UtIX3WP6G8&G4[DEffMt=6
%Nh'$%2W`m-7Uc$cWPO5EWnL<7<OU<M;ZKIK>]hkq;)FN,]k-\:+qWbUOM@>S`Xs&8o#r?H;/-l=?*Ehkn!9=6`W*<p_7>_7psN`T
%p/pf<[(Lg[[DOm*'[0)dr5IDirAqI<ehQu%ds:))q:AEWDXh+Mj-"2cK$BV$)SS9@ATIT&J-VH.q[]>*2H>/CCKSimjO,>o)slG9
%lt).QO$.s9+Tc8$7u]T4Oi$NqPgO>")Fm[IPS^WLPn4LtP@&P;:7cabWk:90I!_;]VZnX.orT:<a:N5$R0A:&n6<8$8X`(nX$_?X
%./d)>j[]Cf-*e,M4nZYO>I#D<hUEu@AQ6'\A[(.5AjR""@g#f[]t9[LT[3auk4%1&Gb]s6`*CAe%[GOI27Y3>DJ*8>'408cBT.S9
%9#T/YV-Ue@>9UJD4/.%(OVM^#73A]t<*QR4?LL9BX%_0^VJ\Q>2iu`gDEuPa1%M4NQekh9g1S,j!@sX7;L$2I.+2ERDG!1<f'L82
%s+!RZe'U]A/K:PKN>\oI>+f<-`BO%G_#U5q.kjj\9u]gE0,hGOWPq]5fE?BJo3i?Z^..TJ&A%8JYWi'bn%AWAS32ibOB*?IJtUjA
%&_XIAhN<a"arn!dNqjGeMGH;a=i`qlX(Cu6go;si=(W='_E`3#=7Be32/?Rf&,%+@1.)qVa#WLeW!0.%Y68Xii]8?[*rX*D.7CO8
%oDh[h/ag4$7K1N#5QN?O6uOaoFZuH3<</2X2m[Qomu5C=-O(-M'YK(](iHOt`N1.%(W,_0EIih($)i;Fp]Qaf]n@(Iq]4>GgkYE)
%;;2T4PQuh7H4cnog`!%ZR@o&1/#Y+dB#'At?9+B`,`'^-`ruH85^mT)$4?j`e<[J+558S)U4EqFf=@pc@RD%0-$0%1U=r!UfIuo8
%A=)hnlLWeC('+ak;jZE<<0n(WKHnMi'l<D0%-V54q[-^1"8dfO6n36i]On7iZkNM(ock=\O)Rp[?@):YggA!f]^eO(pGX8:;7.=#
%!+0c!k4^6A^jP/Pc1EpTOZgQ.1^%$^g:pJ'kUupTM@`tuI%b0Y^`j49^03A;o2?.Zk?d5lAU0WFp^$7gbpW>Spgo+C&RLGVWh[F,
%/cdlrX,X7F.jC=0Y8c?b$BUe=W8j_t43VDHJV2EsSHb]L!/kO;1JA7r!;:6PgY&]8'HU54M5I:]aNlf#%B`2tj&H]6I!(7qj._fl
%+MFdj"q$g#$=>hn6T)aK,V'fM,@ec=73/0UGQ#ZdOebdr@r@1]>m^eKH2JFK0MiT<kK@YJpVObG$EhcS_Vc=@16ct'V+c[tnS[dX
%_6d$@"!'X>C3O/:Mp_ijkXm0s^ik!Y"'R'aIsqt'1SQEWCK)`93J1a&4j/dT[ufk[MXbcNB-`$m^jW>q-=cLs0Yk/n'08N?S-p4X
%AG?G>C&'`%k[4eY4FpOD#Ci2/GFk6T39\`j"XE7Kq"I(.kC6j8gd?GPT]=rSET*a-nd;.k0hE*?U-]-5_pFlQH23nq-^q3,QuU#e
%kl,$$M$M*(ALr"i*@m`IdtfUY`p1>K\2MoKnK"#3pBY!]>.&br"V#2kO+>.,`)?0bcOs@[^]_7kRMrsfC02\#]He1lPR$]c/iG9D
%Sg&pA,pYmpdPhUfj3-+bPIRQu!dN#hF;"A!Ok$5>Nf$#H#,r!(*_(C4hL`>]gs`$ImK^#.,/pcY']"J/@9b<d*d4p&4kT$h\K4s`
%!6EZ_.72BH<o$OgGpT-V[/I7abP4&f7p/"ZUZ+C0BVrMRU+JgLCUF$Q8ben&=q2[F0[?4PE]rhba3\3nDqe!<8_5),XLb;s)V.,Z
%"t[6dZig;l=:,s4%U#c2K5laf/0=T/f_$JBNa>]u@(8'3oD%315\^$P@uWp@==]GJE8\tU44QnK&8KF2EQBK"bHmVu:?PM&^mcC(
%ScN.aRq=JP^tDLBeZ39.8'$NKVb\asC!H.M;Y@`;87.mt[JBMZ8d/+!2>f!s>)T*0O)T7-]G8R)'59!1DkeX-GRa?s[]!P])_gNj
%dr:M6"Fc%3bOg=FiRu]!>FbOY3Q`eIRQTM&m!so'k?7e#gL)jP,u-Da$.DGA$%L0oZ7Z+B=]Bqs\OsS$g>-I<FpJIt%F90m^U_k1
%0:]Sb"hHNNPZ,8!U!PfrWYRbDST&$0;Y9DTcf7)UIja!J"b\9bU>,0`7__:5K7t%F3p9[m@n#/aX.`*-8&h5E>(YiF4EM./-[uKs
%JjNi[a'=&AL=HQ="qI\^N3F_?_\f//8Ca.[):?N&+%3/Q7E\Z\k"Xo6!!cq-6lnU\0g1PIkUqLrO!NbR)sHb\$D*U=KCI?V=rh4Y
%@u?.3)FSU=l[]2LF_8K/H,F,]enOt)4=1jS;`,lLp2tEV#QWY@4IuO=Coc5>.L2lN"`f;YN?>/u%!a%=]6oOpe-JPKVokBnB(utS
%kRO0q`h&,ulhg.#(Yo!diVGe!:cBHaHP"kiHl<$5Ke-<?L7*fZbs?Z2pmmB*a9T82%(<m/%bY??(8qJeK&oJr3(G:m%E;_Ep=_f%
%O":sc$$-4or6$euV*KorfLRh<'W(iFqkPNCNfJ%=&:c"&WW4gP+uG!]<W0EUUPH^Lb$XpJq#pXDs7;_i#.0XeXq_W!PR4MPJO)nf
%26tH;\ITN_jO6>9B/]eQj`ttkW<D)'/1rt:Li_TDYI=D8d\4VFXT07Zl"'C8VA;H!%4"8iR.9'rI$>/bbT[@MRAQd#P<Mn@MMH<F
%9VJc!YLX"0=&7ju,Y,#SZhRKsnB]_r8QZSQ_bOKUB5G\L`'5X>@3g5'[G]N>4RQa%P7'!J;%m199cHum9uUpJD4A+7^kR#b@?-UU
%.FD0P>)AtNnqZG1EIATj]gGn"4A5Y2fjhmOJ1FkMIuG;L=rIFb'A8H=3%R5tD3#D7R:cYKXsRbCrATI*:,T\b8hB)+1X#hC@cA<I
%;<"005SnF%o`'g&dLS2LXbJ3ILAAA1OY6F[M+'E:LJr-],[agK\'7++*Y"VbWMM:ICZFFq6@5o'r)g:%6GA@;<__UqhcM1h7/&^T
%;J1Qo=(-$?C0)Y`l/not_$Bh@OfIdi!A<L-,8dp#<.TbpO>.NrIl$T#=;m[,4917pJ$?;K`*Mj`Mf;%>:3JqNn_bZCUeK]o=%JH(
%-@sB(j2^];9%7Z[s-Y+GjNQEi+LH6g5[a!\L@Rri;-!c/<e<6G.Js2Vlc5qj.2%N(Q%q'n]m:8^]IpFpokJOg4M0I"?HdJB-B&jn
%@j=dp2Y0'tP[e@P/df-8UaUF'g7SZZ)NCNs;P:j&YgH1m]u8!o&E?3`kdH:?N=ehcK#0./di4VX,dN5fnd$KmC!m.M$79/^dPQW1
%Oiks'G,Bp8Q-B):Nkd8V.Wr"\q8]iaIX#n4kq]fi+IGm_2<k-'7S/m6=[/rapGbWtSYECZ@t&RoD<J,)2c)AX7Csu7RrhD)/5lWs
%i>+bfj2YB5"SC,5*J7eB61#7:)o2(OVU!/!aXm^,*$Gr"MjO2Ui4t=hIcFXQ)HbDLge<2Vg_\f<=L2dqV_-lbR.kB9!e$-'hqn]=
%puU.@bg/:k4Mq%3i1CmsGPuB$]P4(L+7Cg@;"A.Q2c`kgACG/aotZ9I*ZOfTol#CFd4'[:8uDYd3*[_j9s$(<.Ss-0!'SV7I^.PA
%V>r(M1<6+`Udej/9XYj'#:7X(eMGMm5.["PU5ImoZ&MaUjY8HqZ00aEnNig/0f1X2\d[HD;F@@^gu8p%p<04!/8558]<qpB?.C`)
%EBED5Wc">o09)iT6M54e0?Rq[c/HF5&EUIme6UHI1"'uK98>PX!'o8PYhS5s+ndU;/!QC/T!,j-^9:?QB!+eH:bbJg;3b7&hQT+8
%REL^P3&In3mRqDV':4L,J=mj2?,O.L5o[`?S-(LE_qp@S!$k=VIWu%R.-@Y+Ofbqb);M6C%,&AbeXpu0oj`/VlF*9SZFY:MpM(d7
%q#^6MITcG23lXO?Z!)6AH>hZeo3I9fklQ68G;)8g+]NJ/q(M9FTjVX6\5T1/8(H4n)jn)UT9c<>h<U^^@db8+VV4gmI1+RiJ:HHL
%0aF;I8=m$i+GIOsRKK5C1!])%n1(/WA\haN@shWVJ7#p#?NaEVbq'Y/p_%,+\:B1lj%G6Y!j%=,_GCN6Ft0QUTJSQEEJ^\2Y'gVe
%#PQ9A).%\LoCFN!PD3#kk6dM"Z0Kc>*r0f<4_")8/SQF9NNAh-'LJG.-?Q&f!BT/1-S[U[D_WHLNue/W\+%5\,d)M9ETiZQpM(P7
%K7dnR/_IsZ_ghC>#@;`sa).-<J\PR_ZpKS7RZKH^b?aGi&Y&k4bp>RPc_&jEV@ImqnEkdD:caX(#mPJ258?92U7aA6W2mF6:!*h>
%F0))U;Ad-!RUaZ@dre'\>Dd=:%:h^NAX7A2>DD`h(nT7Jkr@5_Q!l%Uq(HGp>7'Xd-V+]H=FuP0%+KF3$IUCkeo)FY?cA:14b#4j
%+VE+:bh%"C**4b_JK-s22'CdV+Msa#J1Vr`q"d`$iTIsV^8cZ$P>OUSoM;2:,+\HmM99KSX8G3^(\@C^8,"*PO7h7WZ#G$e.rUFI
%h[A\PD]sNVi<`POXb8!8DpRsRN9<7=.FDIg!\pE!QP)gEC7i+kW%[3W6NC@4UO63h'Y?kZ!(dH1<:,?)]f[^]4=<.mp.A3MjYE.M
%m-7Q@6)2=cbY\W4&/.A+HW1ko$_RfA,_*cEooc7n3$]Z)<F)jU02l9:MXt']9"kFLQ+B'=boF0H!2hS]m?=XBH2oG-s"OV&joF'8
%Wt%D5CK)+6Od_Lk3]-no[+RX[E^:_u!UJSM`0,P[A_>oA`gnYJ_oRL27]m0WBKp9#(>t/8j(7N%iA^V0h-\i!@UB2agioj]^]sIK
%B6;B:Y9jhm[Khl[]##ti80efOVTG7-kL#ik=u^`;?\$Jl69-djMoe8.]D%iS-LGSs3h_H+3D&_[Icd>)3B"fo)b9;+HBqe7@)&ar
%[kAjY]1RdNZZKR'qKr.qN*)/tk[(><6UW6.nER@!"rN7q4VJ'A^V7;E;qNZ"N6(!M+f1F=JC/Y7ACLX7qkP4Sb&hh^!^mtHP1SF?
%F6gFkGX8>tFA\2!*2eFSc!\:QIkU,FbID>H+PqeR/4pA=.f&GC;p]!iQ>u7;A'%"ce#d\)Vpe-4?#`'-LQ%!hSloh)q9+EWT?lcu
%(P8D+8?<.]aC(-D<%\B/,iYi;FK9<s7E:;.C5BtJnSPfT>,2$)Z=McrJ3><1)b#kgTF0paY;<[q<d$@9P<KTAN"sn#k,Q\J`X3hS
%c()&W>31Y0FI*E1Kkf=Rb,F\5BajuSA2/&eo&;oq)Xlap[iMS0l:#>Gc--<@LM@S4SFT&QZm56e,H9MtZ]1;5"W]`P\poIXI*eKV
%"a$cF0sG0b<`<.J)L)U!F)BesfX-p/TO7b?8TKRQ8O/98n8&W$1&?[p^FsL8np"eYI>X5N/R9Ye`2`@f[kgWZ;-rFa>CF]CqI8+$
%B?3DgRIL\O_q1oMOh1I7ct3k3+HeJ3ec*_s>ILr'"$#(GX%JfK!1R/BZB#kD6n]3GQ"I8`/8J=lFJ6>k-oQtG2-XDf/agS_.O7j\
%B=gI619^T1QPEQjNEM*2pk?4F$i\)#<ORW\+RYP]INP&`Z-%7'mjb[%0:(\,ZM4a#+2k8R--^P;Uo>p/&`<h,V-B[#mm+7hdO""n
%1+VX`7F?1Q(fTI4GJS9(n*FF&_%mLf!>JYJG<:F\n+LY>KWX+Un;Adq4_79T(,$/f)nl7<D1Xh"+^hG&?qhVNe.r[RJQe.;@dm`n
%4\7:7eF7]GdJgeq04F=O"QR;j^`t+uYnP=_$)?CO#\*KZ7M#_ehcU[%(fbe=aX]Ir%V)5VF;hX_hlC+?f(`1lY*)KjbeH-\J*b@8
%/H7&_!\b0H[D1/8.\V+EVfhnY@i+!P`^+dMa2lP>fm;!O4QbZ4,H,XZO&I1[VGdhTKL8'0E:8RHf!*J)k]VN<7D1QX0:C?_R7*S0
%$$`a?>7t%(Y#d`$?STFZVHS:)]%JbYFXB\_X[p17'K&E2Z+3Wn23_ZE'$99=Z)NW#N5^)ndR(l<MZn]!YB1$;>D6G5WC!RLD#$eh
%`*_!I?+onO&"EFb3&l`*3n#+A?W=^'6XOJVV.*PZKh,VjlRN&>*10al\oF'9Xg#/OQ#^\="]nsPWAE$I0$E^9,T;0cKNF9#?BpO>
%Dp_5a,D)_0qWB9s:Q#7Z_-4kG<E%GYC82U\d!.7jk6O09lrfV$0("T:KLXMY%r7D>]d"r"P5U'^;,@%9$7B!4h&JRW3h2@FJU1FZ
%$-uWXIY^W203<7^\3&%5CRSol1AUfF4+p#N4-pd"_q?C2U_&ZT9lI;,fSuR^-F.DpV*ci^lVY=G`tp:.DS=<71:6N^frO!>7EX,5
%79DLc>W%f!4K?G[M,YC=#d&d.R7hOQpZ`L39rH(Z_*Ad'0G\A"EOY69Xd9hq-\HI"[-P(ZZ0iAUg1D<_kEm?V#[O&\*FHbr2@6YN
%)+N`BG?=+GZQ)#.PM/Q5h8Em@c=VB:TSQ/M4*=D,L;k!YCE%t'ZT/3Y#ZMt@dCRR:+PSWNjOSG83fO'p=o:WQqn@-`5T?GT9QA1"
%f'Un0n1k!tI&4pjnEgf^9?6G8$)@bF&%,`?BWTM`N4OZrd?IEt4Ja*qrC:_Y8U9L9",>`/kpZF2.o7'[UM%JaW>fS)9)+4bnku9b
%!(L8UF0lHLUOqp'&RNYl9ck@8m;V_i4QqiG>)Z8<8\&S%(8[[[NY[+jXI[?q7Zr'MA>pZVe?r2BZ37B?c;:2k<hZ=(3,aC.dQ#-7
%A(=Wg%a*mc(M#>U<Q<S$3MDJ/SdK2A3'^Q>lWZKfQ@q7;&8Q)D_9Qa_k!ZN*##56,Cpk@umMIOga$s2[9FKN)BN.?qk-41+<lpnY
%%_LG,AT(k\#k=8@#o@E7hm"\Ad[I++]F]78SSihn@g#k!2_3D^Z9q9!;O3Fh`B,?b-o!4)GD)`-Y()C<Rh5<D:grpfd$GkFZTFEA
%#f-q#%8j\X(ef1<<s)XkG`<CESdmQ=1)9V+m'Pr9T4*4unOqM_epr>?2NQ8+2T:fG[V9R7W*DsPih+iE-,rt[lMg=OJkL;GEr=n.
%48ket1[[J(M$"m5J)qE?^Y\5Qc4)X$_"#Oq*oRVk2OQT,E9$.eR\ZsO1(r`oP=BX9J3G]AS]B/Y''sGt??i]BT+Ji`OnRKq3Li8;
%\4b/le%Nbp%qlS%Pj%:d]TM+fl/9E-)Q0&5J'O3%/GZIPVl74[ElLFl&s'*R&f=)ig;N/_h@t9DV%i[D1Gk:m9t!TOP6Y)W1FrOF
%ddgXq!2r4<8qBUKGmA;NF;\WUi%n%Q(P$cL,:8Ru<7+5gq7/E7@O'!J49EiV."7n6nLXr)-L$t.<n8Lo/W%L32`!<!NH7(i7_ANU
%#^!Yq^E*=N_tOQ("GN7Z!J9Y7cO#Ss*Dk0k@W!$Wn;VXOa4`JMlc+VTo3uS>f-e2p$paG@@rA-MN).G-%;3J-+lI]k]JDYJFfSC3
%>VfDG!-'G!UKV%..1*>)?>8aY.Q$,]MSF$D"0ZsJ-#Hoh"/bJT3o*H!Ok`f>ruQhf<!T8`pl&JoU?+>5ZF(kEZ+4.cN7b_qcrUN0
%n+%O\-nWEWdIoW1q/8Yg7,npa#E]i;ODWKn+o6U/Ip7fKqXbV10a^B]'02*f\G+lBB7B?g;'gR\]"sEhr`@d>EhO*N3>0gE-Q\2e
%e@OP:*\7>]3aR7p9&p==LkI(>n6eY?>-21YXHLiB=-6fG-'gB><`#-R:*Y_=d);!9OK-17qj6>tjt(9(A>(.Y#U+8MZ&<'kHDt`9
%!g)bo/uahS.?Sq0M/?ce%P`8m`YH-(5[8$6<ZG\+'GYc'9=nXTZSet]JoOB),-u;HS2BWdMK=[uGo5qE_)U'>0Dk.!%FSBf1JjR`
%K^aL&F`VTQb)!3%s*.(/ReTGRn6fLt=ADjPJNg=rk)ZM7\JSGOd&;"Xc?:WtrYCRa*5>f?bF]5/A?k<k6`AR`iP#3!k]qZ9G;IQH
%SIrGXG&/GnbXSH6BRES/(]fUBg\,q=RCjsKKD22HMCdc)js1(B4$P%o=+)u+5Q08#8$P4>D!KOh0`qTnHs+JtM@Jh$qQ2bu\'GP2
%8@0Ujl^D,[13P3__D&JnH]$-*Q-m9mH,+^'5k@[hem__:jul"r..>?S"IGF#[3kVVFXk&Am%H@E`>_^Q.L$f">\@!F)Gj_eGqk[a
%7l>>P_SP2ON`P1?D#SJq3_OiQW*$pAn4>I4H@2!IPHj!,?ZL2%`<[*-]7_>BCZEHMlpb^.ZJ9A:4"+N)mo"?&imV/mV^qb[C1f^Q
%an@u0m2uB-[g2)WT&fusNMO,M(#P(BB^-7n@BUD7e%4Bb"D*lh-#Sf+Rj:?d&:[?M=H9B6:dnoO8A<"\a0J\2Jl'nSXD99L^1Y$<
%9tY,=@4qeba;2)Q%`L@"_ncTs&8n@'V+6bVo[KIe*cKquRp8ZP5&tk/`'gYp[,su$&No2nO>*(&J@YWRlo;h^.<jE4%HDO;MbX5*
%'(a>CZ?/eZT2<@6M.DLG&GDek$Xse7Yu8R=.KagF<1g;Z/b1j@!!';HXA*%0:"2<dH`#Rj\8kPATs84X'?Uqfe>+%6jlL\_reA<_
%St3l1)tO.Q+DC)DFC[1!9Jd=FM69/R[;)*kiVA/sI0dJ5*=2:<PeFnEe@HNt7aoM46Tdbl@+N3eUT8'25_<-R`)PfoAjr$q$]_ql
%ieF:k>/jfXr>s^/M2YT4(c&/)X,fg4aYQ4`C%UquF,;c;>H?'%'O)L6>e3tt#-/lW7%.<s4[LE/LcgL?&nsQ^IaiQ)Kj_C0Sgr_A
%88$XKGdM+N12i7(!@<5\RtjjG.olS%Ci+t"HXA(IjQe9S&+b),.7EGdEI0:"^H>#8E$r3#X=sRY.L'^[N9CVIgM:s_/'"[(Gsos/
%0nV-a70#5Posr,'c`'hEV%rB;o"FR(mB/pq#`\+`3ac]CH\<u[B1pcQgP1Z^d^SZ_R!7l/O;$XYYNo\jWa/6HJc[fjE<SQY"n$:G
%`QCe+4qeo!-NQ>B<sa,A)!iOm\Q)^f(]sp,mb7@4pSNro26HH<A3_:g#?kBn]#,qt-[;pp"-@\l=\-cCfMBK\Z@F:+ZW(#Xh\**t
%Z*XE#)>#([h_W`a>88g^"*G62pk?8BFu4%$)5^*R\8a(ZkU$Rg1)9OQMH7Tpq!lo/74rb?b/h7c*WXH-dgn^\#lWBi.ql7=WfLQ;
%*Nqq@VbI%A.?r9Kp'o8B&OXsNU4A5?CF2Ku66dAb3UPMpc;u?<X1hHG0uKk?Jqp#B&@N7:chtX?!"VXWT]poUOdU30l'2lg?HE>u
%bLF)Z`Cl1X/5lmEH$34B_@]EJA$?<%;l78*kCVXHr,Z*#"JChJd0IeV@oou3<-k)k*>;CWWq_.*!7KTi*hg3('F2O8+:@_F,Kqb'
%:(\^)ao-=m1X<#7-!B#SE<n<Iaj2jhs+);HfN?d9Q.B>K-`KcKRkWVWa&;f*p>?`t&Y>U8OcpjfNOKu%02BC!f)!2X?:Vp5/eIsH
%R*Y6:GC;glMbY+Hi4FD@.dO<C_<P+VNZc^;_;g/r:LAljs,l#E+LG)cmTppM?fEM?Unn.OG9+N%aaAmns-)(B9Pgg';IB<'pPJj#
%fP+nf5O1u7U]_20Y=B0UO0B@fdV_r2:lnKS1"jpmmq$\:ZBb+7!%U(=b)spC+2^i+7,!E(pZ-6?ILJ\E+5sTAh6pLNT+e;NF879;
%M"mfnK`&G9md<U"OUWd1LScbRKNXBB'1=ckL0Q'dglNuQ9l0Ik$DdQYN^Vs;^Vr!J!X4eS;Ak4D`fa)B'rRme:4j7#*-d!ADW+qY
%";.e_Q1%pF\GTo(!:lZ2o&_HuFg/L2If!tJd4*KJ(dj]E=9CCjC\7?/h6q-,C<h?LMII6P6`.\'/K@<1Zi7cBB5msr3kDZjNIeN"
%N6'.>T<.GTMkW#!.I/lbGXOfD:,1@OBNJUg0\J<T4eH"6]R$$i>[j09h0qp=;:2Afd-"NQ2$Q9AR)$>Q<q7R%1hhW9Tk=\Xgma_t
%aDcSAZ.kb0%^KSA#cnAMH$XHU!KhOgCdL(2+5AN)$GTtV4?;triiX"'r*#O_`hd#&i:d**dIUkg6]=a#EaOABq).MCWYCDM/]#g[
%NMscgh8,!9r((lP7UM&rI-1<t+@_3-)BHBh!2pAD)K.Qu*m$Ha"NsI]>Snh,*sC=FBr(Uiq6X@g'.Ht3Nd%-(V22,ZJdbW%k,-)^
%D.t7!TEO&5C_7[0'\'ir"qq)RZYGiJNJ$U["kZ,%>a*_V!P5W_-sEh7VI&SkOht%];D@Z(';=C,;j[2qAk'Y4IL/*>1Qd=@3t+;s
%>&$`&P0)p$+%DJ=[H_P+$XE72RAFh[D3@?r.+*9X\CP&MaV!#'7nQVJQk07)<kS"M8V)nDj`=kC8[Jje-.c_C0u=h@oJ6GSj,=S+
%nGJUD5a3mmiJB:@l[;^7JfL+E`75:PGtj7D<\2lt&<0Z]WM*ScUQ+W.2;:&<oghk%C.kF/A6O+D<ema%+uY&n6a;7f\TUuqbbZp;
%'NT@\)n-k1Y-24r+G[VNTWqY>h<tWD:1-JpB0=)<a1CtP8Ch6S:eqN>p:ORtJ@Eefh(8o=[S*+lG6^P_"W)8ko^gH?EAemTp,5jR
%"pni%QdHWgF47YO0d5B$`Pd9q_JCKc1,Z.YH,4B\!1[86T.oID=a4_S:U]T0#h6f$4jT)5PHW7"(k;=(!XXobQPTOV:\5[UjR>*1
%NJ+u(G:U7.8L(Q@?P4n'c>Tsl1>7PNHRblj;HGQ7ac.J*Nc[\2G'BeK7TS#L+r`.uU*EUq\Zm@'3\OHi&a>1/gIMK)ZBpe8NICug
%&\NAE68:RU`sC2I_`/,aB6jQ84<?jMY1`WRI_u+_Z<GIuJlZ\[E$pcGEF"LtN[6kHr23CQU#1$ppL5bhlsrSLoqoM3m,G31bnWJ@
%Y=o5SfU!=e8>6#7"pj`:\1B"2B+8VZC8@>_VmqVE?XBNL^Vq^"$:&dD@tW6)e0\/7>s[QdJ'1C5D_[7G((8%Ikm&h5n5q8[r0AL`
%FK^3#][*f!]qNG_$6RR(:aSWB[5*WZPN2Cu0,]_N:4S7JC.n_MKI@UGi$_&am?-*Y/0PD7Za6u`D^T'$Jh!X,Ej%4.@7LSa@HI&`
%&LUN3)F,9h@LaA8jHtmh6(MK"X1:Xr8O5[_iXh3P-R_P(\5pDQQPE9.-V&]kf1Tuc$M;;\K_5ZaOmRPkQ`M&(*9RJ3lXd8/K0oQt
%,jIK.jB']AQ,4c<7pcmP0t"0ZCp&Pi&iPfE)Ra)966&][!:5ZSAZm8MkdFA->JA%8k:5[Or:<EMAIL>(F$-E\]"N_36(ut@t3YY
%#Ep.`*tmb0:[$E7PCPrP3)^N)GPrdT11Z]Phg6fimN9k3dJ!hKKK?n`#tY+HiE0J*,s"sZD">N$4D<?rIKTh*(oGZ+<.[cj;S=qC
%[kd)eO^[NZN1g)4mB[&(JRlQ1Pa%XY_<aC5J,K1g>(P\n[A&_mY*;VGl3E:rG<Snl6!mSe#sLQYTeNLcQ"11_qFa`n%VB1LfJ>,2
%$b##.WB-pS'dXgDZpq'GOsdL/j!83SPZ"iHAs;=0HVD8(kn9m..qEPfdof-k)-opc\W2,X#D^V!(iCpTcGW6Y1=1)tZ'f03Ze@66
%*da_>C5Rc9_0AV&['EU9]r]-Llk"p2(PHf%oK1Cl0?NSUqW'6>S;5X&i9JYVkFK5?*&pTB1pZp?7p*_^d;4$59d_c\3Nl];,ZG/k
%q&)M'8dsF+6"o/ND])%9pVTNs8e-B:s",IZ2Kma9c6)fYnT^$N*W`t8mg*Vs*B^s\cdaOA+j(e/b]jRqPUIMJ/,`Frn,5N3psJFl
%$C8!rE`M2)D_+@fH2H6`>!"j]4ZNPE=`9-HYHg$#I%^;/*bK=*X3WOB0Xg*cPLDA>Be60A2RdW)_#"#7d7;X.ictsd(?.*!%JpbS
%d#`>F`B_#9j53n<'gQN0<S9obR-FEn`8f=3:]5=J7F]u2Y\5s<70$+TU\84=(bUB_0((#((J1^s#\kD!Hgra\h"`NlT^4;uhL%#O
%Tg6Uu:)HY%Xklr.JG($/o^;*s7)^1<#Qk$*o.S)CY282mLIiqMH9?\;!EqYcH`,J_#g$l1G[knW:\m2SI0dKabm^[7FPLG,qME4/
%OVssk`=4X-G5+']YY#>.;^h7\8`K"WP7lVC`6:>B&UBD0+ti7U3[?D2&Q;/3$YWf8$meeL2",\"]AMN&=HFkEprc\6UYg_992^;]
%P0u2I)!_aq/V-hVUGu]^+"d/nKX0%-0uEt6hRb[K;I!A!pN)hg59*Ce`aGmKE0'KY7*@DF<.PV:+R2kgdC3i-ZEat%9ti/7$\:Lc
%DniR/g=m\]-aOFO8X)WDJNW8:naO#+hiG0t/'9.m'+I_,W4ETBA(2,&_/!H,GKkiF$TMW%,Whm+o;%u1!)#?m\L6?Z-"a$9RT*+P
%>?#D[DRMTN#3`X72f72Kgn_naaZ"C0Bge1*n@@\@Z3$8-6a0+=[S/0W@k3(.)7[W<SJI_(4]5jf]Lf!/6K9o5;AtdB)>QNb9aij@
%/Bj^BQ^Xaa%>OSo^tJRn)'LkN!#O65H^Lf>-h]B8L8Gq[/YS?=!M=5mA$@F:Kch_!k#RW4AV@!UVc:`u"HS93LRC-OlBi:XP]gT3
%P-"^[(6.s0!l+.nTj&ddEY(kZE:Lq41E:sW^t"[\@#,-i2YmJ)h-*^3d`80a0QgrH[7]3fS^R_^)0u::5h-@CB"W4PfJ_s>ojE>r
%)m&+P.D:joOVJ>H9R(Bg-^?:A+kTBuU7l:!ga!u;:lY<,\=;`H1E-lCQ<S)^)@Vcp`K3@l4ep"Z+[F1A],"7dV#Vg+Qb;S1Q*q-0
%1/*h`&%$9->EN&-9@r`$$TnQ=(5uW.C6kU=ke?ITM"!\8"t*'lWaqinfSZt5gbUTDNAo2iLh/J#f;(B`6B8mGYo&QK6O`l("Wu5p
%/CZq\-%[(8Z:c*7IXuWLWiTNuLDOoJf+oN!_'O@HC"ZRLg`QZUq59&&N2.CcG&$Mgc'*/f=NYk6\@3sG`c5glU&i8$_+-A5MQr"@
%A)uT7BqT']P?,,&W>/TJd[3Vd7a=d$/oGBu7#6e6F;nY97,O5fl+fju$:>>b>lg@5CP5!MPe^LlJ8UgUfks3`2[o7n.?eHr,t<gG
%FuI"io,I\\,D(2+'JDM01W(T%JI:`@8<7KlQ@OAcM%>ch7>UIb.gUPB*VWH+RhHYN7Sm[m8MkJ8MWIPF[.L+4RhB[%RM)HG@PO(u
%?JK&rD]k6e#UhD<a&]u2Qr_'X=:&Ah;:&C+bn\Ub]mrmZh^#0=:(8;><ES9G@A2&:i?Vkc,**a0gqk/"hF5Yni57g#OlTMU6KcQ=
%GWWbS<P!1YS?`RgG/:aKX671#O:@M,,&5jp\RIO_dJ6rIKj^df)6C]DOTa.,:fWH4(QcW/\5&U"J]i-rKXFbF[9kbc&bCkJ\qGX)
%(#5!=7//,R,901MA-I1t1sq4^*3?LMq+WWJGmGKh=\OnrLL?oROD'Y$H7l+g=h"SR.pn4i@$sGK-mWq7UIkM"c0AFt$;>]p:afOG
%e/3?'X*A,4dA6?-<mu<Xa9o3Z69KF-^IP"N`T^Tr.MpCOgcbpGA)Z0K9;#huR_<9pT,N^q:\LU0:!#:VBu+k"0N*LsR);a'_?<=t
%!Y18m2OqWb,2A2JR1M37Z%*8o$d?F09G(N$JRj$'UpGVdei8`s"VNMGqc>448[!kFfjmK4UI6_udh&giYQ0E5Q<B8&.4bL4Rpq5_
%,QU(,h#V8#&Ogql@.*15#*S6Kqq$1GH>cC2-"Dqi"jRORXr<.5AkWs@kotdc/"GB;1)lnp&X-l`VgQ<Hh$%$o-'(`YR2D?M(JM[I
%P<P<?)&n2eojruU*<K]hnQd8*^k4Wr#X><K;?0WL0u7*G&!+gt/3/="@,O.)-)B45&_p.kBCpJ($NLM-r+%gI^a?m"<$\bWB!GLi
%6`?PCr]%`:*X`6T#_I-E/rqnOcmq5eiJCM:$6F:7KQ1NVO;s1m,['7(W^#kdhehOC5(L??-^%=#;-*cF4<lTP6aFW\$P"><TFtfD
%%gB7q!`[naf]c!nZil?W3^GAjI25:D;X@tk+&(t;$dd<^JUVRZf]351,N"g/hbT7p32(sE;V_fC<M@nBN+p:dR2DS]9T4g:_\go)
%OAoT.i3'O/`<WLHq$7+Dfr,!E6:0$[VW>W@KAnj/s5>d]D$#d?#Yj-NQm9)DO;F?!!]t3V>!sZb6dNd"*bP2EJ9jS>\N/5(K/OtE
%+W+L[;L/Z@h\6dJap+4O$8UVl"k..L$3DGT*FPZo><PVH1Vq0m8TX,6FfNr:2jYniJk\]Tg<\*sI)1A=LTVQTas2Y)1W,@/QL7Hc
%1F3Q1e.Ph(16fo#1RaZZ=G*d7iC!XGR@:ttL^OD)!I]CE=CSi(6EUtpj<YGsc0CW1Q(MtmMn(kN%>G%iY<qU6Ynb((%)h?*0ID=#
%A4B[PPKReLKDI3GA7BEA/+a`iZ:67QZhu/:W5T(r5.)?mC^#n4K2,h-qD"u`R&4FCrQ)kZGj4g]i`MeC%?05f7!9)ZG<_&UXAuLW
%GC\A$4V+BBIP/:qZeL7CM7J:a;Q_B["fEDU!4N?X/VI0BK]U<M`]'U&DrI\i[ZA6,)WNls_D3=$5&@F>C4L,6^qr>h`Q/p($D?Jf
%GV2&qpatk.>RMV:CPIh/HlrJ#Zdp[acO9J&Yl[79M/#_?ZirEN'=VBn+[O/E2[7L>;RO=dIKZVu)orjc]:R7"3_Utb6]JP&hAo,(
%aZ(9)q3,3Xe*c/h]]M"F-lnPp";.&cmAH.9OLc((M(>(BfZaU(L4E'coc0XPl@W)gfa&/=l(.??BH31c'/*T(\B9C-OB1(B8Yn@Z
%!3.!ZY</#R!n#ha`r#-D>f/i3?'lE2-e40ob)F!]j5HrqY*3K0\e'X*.g%3\fuI+pWPO*T,q9^WTL.<D#@G8')d"LBVM<+.\_5[u
%3,bWCBu6+Z0jH3ob)mm!`DLCdB3lO2g*9DM6<An.c&lTIbsa[Qa)ud)i_75jE6&<&Qft5Rc39_S,cA'\)TIP@+0C2bA@1Q,:Y!O0
%0"p\RA'@o+,&UX0T9Z0R,"ia_5-(/.5j#OE:u%umr"IO+!JKdi@k3*R)/q=ll54/FcUs4[6%!Rqj=CjKJ19(,@j_q7<6^,]#rY/$
%)W-PPQjE3_=EH]X]2F6",b5jVbYuV"RR,nfJ6sL8O[j1?IH^cY4)3,&0l+lbff`0Gg/1KCZNto%%Tps^Arb;"RFcHGM*[bnn;Wjp
%;QJjfR>2rt4I!Nh/1VtpEL;b4pB_8*o'!4]pbe\K*g]$O7L1SS<mjm]Uo"2j%52c(6H]iMIPq0NR"YRpAPd8kYUU3\5/Q`r>,Xb*
%=$F@5*3m$9?lP:$[+%p&H%"89VaNj/@G+*ufRd[AGl-R!`VC`rm'M[qYVU'a<!=]fZ[L%1dkR>D=7@riMBdZP5m10f9I)=r#`g)/
%&NEZ\J2qA\,BK:g0G=l:c_X_c#VN/hD!QVB<2`AO3Rr)P`$$?hr(^tSP.HVZM!cnOW2HcUM&/D@a%Yn4pefZt:?:1D:u6>""p2N$
%^n%rk0R9d%R-$.bgu6(ZRXA$6krcaaW;D<EOHFBlBK>5gJn<:j1NO9rG;3ct'rO+P&[CFXOS2^@D/cFP9R101<j6,P^_A_tcHiT.
%OE<Z]_dmfsRG`5Ij^s[-7Zb,JUR/D4pm:b0>+^e1[$->AR[rj*HbFWe4ASZIXmrLLN:=0K6S!+?d!Kbu")cKD<ud8(FkM>_h^muS
%Vf6Yl-nA7_*uI2l(G^?u/;XJfAdjd#g]^[6*f46oMuV5lBbOX9!(i1.^_f4+&EkYrEgc1Sq'Xi;A("s7qM.Y(237:caPB:(MH[,9
%'h;NN,tN;>3-tB>kS446S/#EhJ:6J18si.c.<qR;JUjLQN1!$=_A*NQKRq_fYZ1kokgRs:QaL\:V["J.#FI/Id\M(WY:i8QAa*KR
%2&"=.c&tA6ahVms/<3k[*(dQ]^k^>Kd$En>+oa#q_1ZJWa+@bT9hbZ\fKubh]"Lm\1Mm?Y+iiC".[P];=\FT!S%Lpek6R*TTsJ="
%:b^R1\l?MVbhc=Y6\1:c_5HjCZFQ;^+ZJ`PFl+U#R5^61A0B&PG7$q>KG7g%CPZ4(P55Bc>Qj<.,fs?MnL7kjffgXK$Pj%@k1E#H
%S/'Oe"Q\aM!l4_5$IDW^Hm5JCJ<[*Mc:NNrP,fk1'Nt@DPX,'ni:JC]F)Y_+N"&uWLbAa$j?Yr8SE7EaW#Lh/Uf40^+jLAH8rt"L
%A*Z\>#uKT;M<[@2C/*`:RR:N930^6iYQp3jdM?R4$;$G3)FT1^FMW:9G!ho5X5`cCMhU_T"(-rQB>a%!^L8NLr[qa+3&=f,l\C8+
%jQQ3FZGX@=#6fP6q'"$ajg9WOJl+Mc%Rk?dSURBL\Ul)RJM2JDCC*f`e!iRf!WmR[Z(b#0'JmWi!7V=k2<"CQTliW[/Su:$*!&fg
%n,dSU"D+NmXb9@tB])(=LTQ*#E,I'Q9]uRS,Vb>.qMq6P(Bm5*1'1o;+\S##L4mG?7Q_XSKk4'pc"'Z9"m@cX,f<+hR>RjUJ<Y&h
%rSU<`Jl+2F6YRAn7q!a#"(s&+Wn\`r;^FFR!ilS:<^7kr/>c=4C+lZcf^%sH$u_27)?ST`c4$WD$MXO&8JbA%9+A_1ctnNAaXRPP
%4':c-=9d@p7X"mNO=f(bnnB62O"]Hq=]:Rl53l<Dg*rp'bS*!-:]M@\2<=_G']:,a#Ti,k`g4)e1ls:c_+Z/s5c1>eCBHoj6*-kk
%OP#=C2*m8('H'5s46t5U_WEUAg-]#tW8WBFT(VA8`Z];u5AH$A,["a-Y(;7Di1T+aKRF3Q#"%9i>Y:Bi%E%OAH(AZMVGO;>c[sdG
%0E;^^,//U)9.ke@XT!74,oN$D3=IaCADt(fPhA/fZKDV'7.g?:*6SdjW.cU2(W]2k#)7A7.0Ig(m2LX;ntf+jm48E+>UOFR#pS'Z
%8LkkGas!sGPEo$?H,[g;GdJ*JJ&lC<&Ht\%&X_e^=@sE'akgTUG`cUFAY?577ktksLj_nP_d$kFiD&\*fg)68\h%Ef`)dD6kGD&_
%?s46SKU_Yfkf;J;RLLPLiW5L6"8Irsgq]-aPgE-Zf6_grnrA&4&sIW[Z;+"a!DK$BM3=d^HZbF+Bf%HVR<EcTF"FR^g!]Z7>S8h#
%_0a9d8XC'?UV//=ik$iXIC)=5nk7<M&ttSENe[pAZPARh+t7+.>Eoi,-D/OTU(2-cK;HmnT5u/l?tO)oj]W:;".h'_CFinXBqd@\
%?s4_10lTb#M,I/*=t0P]RlRpGnbs_We.?"eQap5;Ot[iU+e@QlVP7(Q:*XNZGT*Jnh91%%4KKjM!5\8'hDOK5XI(Oa.o*)b,dUr$
%["OPu@m^&i.LS2/;Lld,$)t)MUIde\Ap'JLmL;AE_I.G\`$GCZL53UF0J]7EVVY^'=O#=HIH:e6)%sYS.Tn%OdWjkQEkVn+8K4J+
%l.[HZ;?hLR=ef4E6IIcYOBNmY7P`]=aV?)?hk]0AK(1Gt#F?7;E^lh$!<EMAIL>[J'81B2i2e\WOGU'A:rP&o^_>D$2*n<52c"#T
%X'V2:aX6!@'aj>;W.&4OT`mC,-"a\jq'o[FEf@RB1He<gpXJ+.#2;<GP(!BNb]=1_k-=SGAe$EfIRS+WV2%db1f`cjl-hQOg5XHR
%X0WfKNC"i:Su/[P-7VAC8+dJc`[_N=*&m6U`'#g(FVgILR\11t%V6'&:Ei19?UnF??M]A<D]TW\:2$2ZRZ03ZiBSgl&KJ_6FY\3/
%gP"AM.)H@Z]M29?PP&F]_iE`j4Nul)R-&Gn]/B,)YO]dA1$donW.8>KRK&sQZ0aW47f1HOHYhT`HmEOfR`H468BHcRbTr20XupFl
%c9Va>YU(6&I8K?6Ssc:WS7:](pLUd20W?L5D#Q_A>&iLjbAO^mL<,4i=W95:>'S+>GM%;aV4VE3aHB.W%^ErL8AkVd<^!mR#>F%W
%(#TcOG)n7e`E<hc=NPq<c>K0DU.`SON&kZdrrP5-6`+oo9hRM^lgl\%5La)BBa07LJ.[-khK:073S8LImTimbm,t1I/'B?-3$iip
%@Y"5ISi$P8kql+(_%OmE80'*D3D[i.J<l=6BpFQZCk[O;0;(;-Q7t#_hZ5>+E_acM=gFF]N2j[Sl<l#AbbQL#&D+=C5hcD9/1gJ'
%,l%NQ>+WF<_)nm:OlShWnI(PpUfp(6*cTAt-gl^sHPb%ukD7U:<Z)*nl9OD%K!=%'F@X]2i!B,nD]XmWRg-RQ!h;ju)p8haOiB%Y
%,6m6&6`G&^E7C1FcAGT$@(D_VM^Ic>nB=FW=Vb,D^3%`n$&Do!YOa<-NeB0i%$AHP!L+GXGo]ZsN2-M-60e0^3!3TQ&ppr[';oQA
%AOaH=qZ.A8:Y6Xl-/KG1&tbD,#YGa^!IRZ]'gE/O[kK0;)3q2dR6h%?)TS!$"*5bip+1Oll(pe#UF#-o\ptI+O!RJFd/;MB5aUTl
%B"Hl2@AO<7@?PV'`$b+hYe:s@5H<gOHa;^i'LIo"i#\9ua>Q$"iM\pqi6G!N8O?!Vk.lU>#BBr.1(m-1(F`!;G,-OaO@03]n=cYQ
%&n/nMi!W^&DME<[cB9Vket#`fG9uR^>a0BoFHRD_,b?m:]_Ka1LqQMPhLu@QI_b`oo;9GcM,0Wh3mqS;:';#X(;3i0hC6JM[&<t_
%j,WU8,=NX\LmB)K*S$>u2jtdq.Y5f#CU*q!0ms/n8^g$5M)`1JV%:MgWZZQl-+2)RQrfHtOH6_3XIUc(jhZtrg!^=IH\T7&.X7Ks
%(F0;pqcb=tp]p5_6ane*\1-c0,s`3J2pac6b+<Em@e#m#R/b1RQij%O-.AQ<WEc9_H&E<5=o__i%Hnl<$H+&WKg72<Rc;.[9\<nU
%PH7HA3YTSX"Q/?u;2);JK\f+f^701HdKMmtODOYiU/re>:Y\5ION-q.hXYj3VN2Bo[gMYdai[74cJ7%t$AS\J&6I*2X!G,dn4F>T
%MZ9C^5"hJ<S>sf5#gD6a6OIXacq4_n`6de3JU:Qc-KY&T3us"YN,"!e6IVu^NPsk/GY5>p6bD*hoW[6tZnNsr'UHlg,0[Zf+t"iF
%mP7b91UZVq_kITFUP*`%,DVTg_iBs5K[UBUK)sQaE6A8V@2GF`Uk+Uq+H6.4.Z[!Xp`)hnN@M^U:;%f?Fc4GiG@biT#m29@W&E$P
%"89BdI*8j9N/0DJiZ]1`d]=^MY%J0=&u,dNN@%((b?3P2VPqf3JqI`7^`^JD"2Upp6'Aei%RL6VU9/;HU\Y#68=8X1`%01X$OD5:
%QJdj8:I(_1dh77EPEZE,c&s3OG62C,$qS?*H7'PA9*c^S6tUhF/1IUfVF`qoV/qg\+(7\%&TNqI!g-XUN#B`K-MkF'(U"qI$8O^t
%7<'lBOH3i(+T>_@&@G)/Af+1a-&a]!!L'1]@Hb(VA6gOLMp*iP-eSDh`Bt/H8SO.\ZP8l\$e$eNNiGmM:?Y9o>1-a<.t<(sE[+Y#
%p<7ZQlT[-u2`,5RTsVoa-2+cn&Pn9aMH%2mkD:`"A".IQ&QRh@(Wl5lP^eFUN$I+*E\rkEOmFNi>:b-<lpu6[8M'E4=mk4'MQMb"
%:K[OdPZX*$\\pU&cS.ND,d]"hr=_#qZUXK2]MZ8&_<$b]&QDX4E?G:KjS;cgL0P_\W$f][RQA!4bYEFd;H7c!A9"*=9:qLIA!4c@
%IQ$aKkJ0D<lF!oYH"<)O6r%[N=[<<.\M:'i`j,s0#c_2Tk/t\IAK"X&;eLR\c(?*mRV_K9jJ$.*baa7(R_+RSF\s)2B;qd6c/sD%
%M$Oo(,O#>FI/as+gcoa*XdQ1X;2W$hgsg@<P,VVa<1k3uWZPED7G@qS.Ckk7gHee;RF0<spDhHB#)ZL,kqg4MnU[-si8Q!0:OlUa
%[i65a`G6t/j(V77.kL7)9k6b\d(M_D8g3(#H.3F+#P6q3(4,ZKOQkbY>Z_l3Kt[Y2GiD;fBiO0fo?;TmSt2MX#e,X:imhOc!]M;d
%3Ak:llaf1a0:9fo8kT/$:>VE,QnH$BkJ6j@`:/amCS<c=`Bf\?65)r81o'#/HkRh=AMf$R1cqIO_"2FmTi@e?\NBjP4]LWY*6A%\
%Jk.):3HIVecK)PYc-(2_9e*s3+`'b52-_O<qE_"]S5>155A9>:)$,OI?OGE!LZEtR*Q:luK&&XqJmIQ'5!1S6YG5d)"f,;Gjeu60
%J<g8@E]FC9W'V?O1p3Xk!bT%A_$]V!7PN=0RHt1LDMo2k1@J>p*^Ie0o?p-9?=0ubq*IOYOP)K;"[/s^!3%=XYS&i=i;a9ca9k/s
%lN1oi!/HDIjAMnG[9-Q,O%TT=k2KD!d+0t.DujUTM%Js9JO)f&KY[*m.A"1iGaL\Ska*`a:5&MCFCIgSK-$(+>E<P4O/QT-*#igh
%B.?UaA/a0i?s/c.Z\b=r7/cHhQm;pi@G8i5SB6m$S#l2)i=Iu:+Tc[EUngZ>O&K3HZ_b>Q7nY<YMeo[mi$g%sY<nOoao,/.ok!Y[
%YK+!*p*K6IV15r7qoQa,Xhsk<2d8_PHNf4nk^/=hceO1mG<eo%;?^QKFH_FpL/_3"$71bGVW"r=-L0$CFK/LGQtJ*`DD#&oA1M9T
%qLP[0#HE=oFFWF.Q2Q,?b.p80VOdr__q<r6MU3\+dnW"6ZK@@-K=^d!W=a18SL?B&io3H1gYEPje\95S1X+X37?P]M],X_$AN1@Y
%+^$mi.u[^G2?X!tLW:c^T,6n#9T!=1^sfe$Rg)fmaP3)@7F'4f[K:]gje5ju^MFG8ik;i6?8`$G,0++1&RtHAkOc\,PN3r1-haW$
%pcGmNi%iBF"makD%WoM;SdgB5?S]e[Q&Ab@acbj5_uPO.=MZc'Yc%178<3W),f:s=YtUQ'F$H]I5Na#"*gWN;j':^oS4nIn*hkH?
%AE"AuS=:1]-DYj[T3+?5C'U5baL-bH\Z'+cONcsF16IJ,6QFd:7+uG8I.5]n:(B"g^nIfP%b1\kS`'X1n7%.F_?Z[;d:kof!J;!C
%!Va7H8oT`,!(!o)6H^MFO3#R$9U\sf^r^tr`GW`O1DsAT0PK'kUo[\&4+X[G+LuP!N_sFKM-+.f)2t/8.V<HuZoh.0+F8pD,Irat
%)@'@-:^Go0LP;**UQs"B(6Va:?oO0bJ/Cj,ZGla+T;NYCNsPXFqf>_51Hl']iS'&^FJIq`Jq74>d6n]d_V$Spr8e[;A0-sF++7FS
%_e:Bp5pniW(@8NYLXn&fb`7ggXH.5gO[j1fHA2r(G/bTDpC/VKb#GV-k1MFV.<+#9,5F]:JtBAab;O1kV9KRTS<]VRC'g)ci@94f
%Z:E8B-olae*0?d4bJ`\2S-fG*TPAKlENaM;.N1j%K`qe/F)jn`//>7:?<9!DP3T&B[]0p]pjqDZD_g>JH#S9ocK^>/I+A<44WM[d
%qo;8glcbotP-HF\*/eXTC1/2%Blfg_e5^gM*g+g#,jespW[1&ddcpp>DFS7F,N<*W.(KZ30^ghQ0P?"bDA%]s1tUhJX6gI9mUc2K
%:5$,g8&]#2$?pi-NjJs0PHfOsNX;6ZSBWO?UV&.95UEtjbth@AD1@^96m[B.M=UH@LQPGXJXUnh4JZ(-N9_%]lG-A6!JuIdP"?C9
%dTM\U:EtLa*g`]jdKLn4Tbt4gRKQSFLWm3eC/T9?P*2qKE)"R@KXcnKCNPM!A[^I29O3gWSi8'q"@K#=`;IrE:!!k<3msYG8:%'Z
%WYuXl(PP([PuTYVZ0*NN*?;50?LG4%"YaWj",1_DJ6@<6AB6>[R-(JX:ha+i`(4pZ<Ht96`lgo\+a8O3*)Zl,-p/Km(.9GjmN:T$
%4sAZ7_@#?.16c\K$t&]Q:&%mAGg4Ti_'1b`3CREL6RmLLhf$V,Qa;7UA>9r6Y!:i8dYogAm4rES2%C++JN2e^F`X69,!Nm6%`;W,
%?o'a*)3Y^bnP\9X_)`MKUZFnRds7FR-3lJ,-VTsR6mhM)EjNToD7&61Tq!c.6%YcBCf4pd&W>]q"s_*u8Ur*%\>D+ek`W*R0H(.A
%$<!eJj^ole6"\G&V2s]Kca1dMZts_N%qS1r%,Ad@>63#2&6pf^elUme.5_nm1fEp[X15X_.oFL4N@.S#@qnoj+AA=gZBI=aMPp`%
%[=Eh%*O6h3^3)d%j-j.@C_^++>*Z`b8tM8EBl>R^/_ifYXair?X#f=pdNK'PZF(k]q)jkpePhL&C)YqP0H'"8R&?k;nKX:>Eg>d@
%E>ID*T1n>f)"JjT:,#Qi4'O,tODruAB.`r4*IUAq/.d]\d3<Spidp]oM%TIR2fm,(9*<F(NON`=;ms2RUdE#9#^;kT=2V*S4@BBp
%c(_+K\uSH,0#H]1oOrBPoV.l5Sd?p%P'e*J,::5FGWDPXekaN;,f,XeFT=T];9;)+l;PUN1Icc0/;RM4+@gSNJM3u@,KN"q+gnRC
%ROrd66s,(r7hB4^Xatcc[/_*t6uJB4rB2TkcoX&(G:j\!2_m$D5t&o&b@1d-&mPZ/-M1OaAp]dnh_8Z$;*e4j&A^AApEISCp:2-M
%`t8,b"(W^f=:KF`8STFqF%JV$p=cb;/^-FIc6bF9(Q0B]F(T2<U$R5ORM*kZMI<rnc'f]T8HUb[V?"Mu%Z+o;E.=C"l<?0P8Rm=>
%JM38+ikcrKeW)11.$(q:P32IjLDenUlu3NZ(8eA5+p`$W;I=t%1"'"1MBK3FEs`]4X/Q@Mk#<&M\mGlY/<PXHp*gSha9[ZFbsU0g
%)#YW:CVFnClu(3qkUbgPp;*E9$_H$b\S6@7E?:=/+DaSY1t(QDcRrF"FNVL[85]WS=Y%*`cqMf]deD2.2mONZO3&95[d)KTm+_7L
%/R^JpM+i,M$9C6I\tJl%^tGWG%l./7>:pW%X!He8>3%tD'5G"@"3bh10)@Ha'jY>PGl8de4@taLL)J-%(^'^iIHa#ef..&6jHS]3
%b7`-feY!g#Vj@NKR@P.O.l[QqBH*Cd[=Ce#F`C?$R_0U]K95(/cl4('O5(8H:E,@^=M.'K2[/fdMGcBqko<`'/qlj2$nWT`1PC"G
%Qc>tLAJ?*m6qV_.M^.SE5uH9ueoQem*$cZ7#lp<P7_Q[Vl6e>6qP"Om[=H3G#&p<A?hBn2es"+biku0eW71Gm(Q(d+#3-H1G#%0;
%Lt`f`aa@3e[\VE;QVGW$'pZrEm8AFd^(4\o[25;B"(d+.XZ`/\'1W6k'?nEO!+V=2+J"Le/n`!EC$75_X9mPV[/e8,J-U3!PkgTH
%"="\Ia[dW2AV.Kur,$/T;&oK:SDr"^FGfZ\L>.ohDY33qX33%K_\(H5'*lBo8e(U;02JcAn!,2Ubl>"DB/cg9Y:OgLLiT=9=4Z_T
%CC'&\`/$h__2HXa_Vsn_c\'%NZ5`ua1Ati[/J$3OG6p!$T+Wm<5Vs'&j>gFN$+[2gk@S0Q-%.bAj#a#G%W,bNhZTiC?+Qpf15\I+
%-V(k>_M;2kF%D#ok'[Q-5-OVP>m,65()$?u7"l])PDMtZjr]Z&(t5FOSKEBaO^r.bq*2p:I4M*N"-`"84&2u+cdfFR@Z'h]IB9Ee
%Jt'\T1*+`XgTo^]I5j>?nH>VP)N>LLINgZE+Hm9RUH"AEeVTD+>adVo,5bU[_*KhSB-,jLJ:\Cr&pegudA4Uf%`H4/kHe*m.]:sQ
%1%Sl^(5"1'NK@rWnkqi"@^M\.fL-Q-3"AW<+NF']$C=m]E78S/%PEWZSbBM2j+S!7d1>LM#`S$4]NA/\.q%MLH),_kOC_L22]d0:
%XT;C`^_#g&0[mL#8p+nJ!3Ud:Ehgrk,o=r"TH*0H9T\N/TI+>%TUaG\EQeI%%Nnk!XhS8NZNquAJ5UoDgpd5>O=GuJ48GTBm%@ub
%NI*tK`2NS$nj"unM_qE./=g#'&bL$]FIM.;,Tq=^0m>&>@I@7_%J?jTRB"JtV![iC?Md9hJE]-1TgenY#AdnFk((nnU*V=UK-98d
%MhQ&IibL@[((@uLF'4,EfB@oS]h#D9':g^fV3XQ3J@WmfE,=Gri2=H03PiH]E6fBA_D81)dUM\cS^F44'@a<&@;0K`an!PHZ<5a5
%)$f.<SMX5ib8j?7k@%`eV%-s2J5@/9ja*#*E^jA!4?ST=jMKPs!F^C:[QRWCRR9fhK$Y;L/)k=s,p[:QfR;pZ93CT(\#V95_!&<(
%V.CElUpo7Un]Am3qpQqiX3^O\2c%1OHZjE*meP8:e,<AFDm`h'2K/t3LgI57b67Icm6Vm>,U24^,OL'2d>(4b'u,l<U%IlsdETr8
%l?n&,H?-ZJH@0hGeGm4_PNVsP93:;Vk37*)\\,G`q;g:'o6lMNB0P1%EH6JKL%[H$[S89kTfqkX3-i6Q^87lZYrqG<Hh[FuHbb/%
%b=g+iH=NrKg&B6g\_Z!qJ:IJ1cJef2r8W"iZj3nkH$%-ubd[0-hj2%VHbo]g$bHpns7p#>p\\ZQpW&>EVEp\nfFa&4jKdm/KZ4=^
%8o-Oc;r'Lh9Z1tO,[]:=*oBjNVk73IQ%]9$'r%@c=Y-U[&GMPSgDtE>\9dfWUGq(X2`SMI((6JE8NG=Z:Qgr7(EX7OAp&OY<oL=6
%:+50?^umL2k,b,0m)`d7DBWP&jt5'.-#GKEn'-1UKs%i=BplM?`3i$PeU^<Pj_U!a\Q5kJE,:dQOjI_e[tnph/a);):/>p!o2)-u
%rda&Ri\UZt_ZT+]eU9n>Q=DVNJNW;_IjR'q7LM9cJHEe\@!q[V%b>jQV!_"IH[s9r/M43bOdjVC>FqKC7`lT@LC1<d%HM;g!4pjj
%e%jomk1#fb"n5n3:`ZnjE;kud:&#uke'C-imP_#q,HXl'i]Y[/&;nn_(1eg%)M?BZRXT\9J.7`N``=gpO5AYYeFL-:L=/FE@?b$A
%OarTX"JO:l%0O`17$bg2%.5DD1S2bM+_e'gOefP_-&f[CgN$i*"[_Wj4SJgc]7X+^[EFjK.uV#;nZP>s*qNkEjHFjSko7Z9<f8oA
%bRf'Hc<C'NhudLSnYho@7'IZWLk2J9=[:3SJJscq"UeC^`_?A01S+a?3Yh>'$E6K%L8.=VUCOc#1\.L&4I+S7OsY\m_(lb7RW8$N
%D.;HGXFetT9/:QIWl8ce:>dqf;<'!7MBO.&J=/7'YTecT&Y+hr?*$KJ'VgTYAZYWH;'ZmF`*7a*5`I?N1W9+G_[0*3B4R5qo>H*\
%(I$NrV=,9fSDab7bUHdrX?#:S3@Rf4N?V->*@0!31Nc!KM9F*E#+i[B+Ot5X5*8#sfuF_95Gqc-X=Y9(&Mc'O-0'i^s$`O/+S;1k
%;M?D>5]YEn#bN#q4C)sFpMHE\K>\QC=gHs,lAAB'4O!JLlrWI(P*D,)p'kQNj2?m-@3-:8Td=sFrh%DP5S,Bb\J9'"$_s7"nmHKt
%)1?*D%LKNQ_k[M"dGlX<!,kc3_AEQ:ZZLIF('Lo'-cAQt&0Yg=I/t01,+urud"tI%\4d)%]k=WnnHt3>ihJWfS7$?Xg:-"NVFe.V
%L;7^m3B:a/f[T'.Hd+[RhFLq[7XZ)Y,%r!kXa;cV342M>f**cg'(d6Oq)Q\Ee,j+%QsA[!8=l50)ZM;"[?sr#%3Ea4G[t!L=^sCt
%J=599(^QY>!`!9F'eIVpG:;\sAmIGPB3u%fJXc.bN1RsAN"W3,k]E2_*[MZm\X6!q1=*7AeCcno7a$NR;E]")NTQh+;U37BG#1@:
%%"gIRP39ne")IN2/cn89?RrJ`=p56'T(M5VNc02D.C.aI^$#LJgK9sfHnp0u3=&%K6H7VFO!sOLNFqPub_K1L\?r5m=-;0NKrgfu
%^KKcpMSh#o+/HZ6C/Md'g^*7MBDd^O,05nC7HT/mhP+B"h!?j*aTF)gEJSAM:?(ra3_Vk^iO*lr`B3D'6Sqqu8k#h_0fg%"O9,!Y
%PlSRJbs\/1J4OMm@e&\!2DH"/AYEX(7>M:EagSP\,2<9:h`Qrfs2B7;:CLYs+-_FpkYbTs-\U*4Q5!U\N(IP`e.h8%h,S%N4)_b`
%iWVS10]N'h[<l<:\YoWjQUM[#G%).mk]>[lJnP:LfOi9chPi"=H7L_C<ZE?gAn9'(EkKI*kfIl/`P[W9\epJQ$00NpN_`OoK")S2
%dV5m`8\Dg)=#.Va8FnWKf9&O&;3mCuH]`?2$!^+7M_0['ckOV*0as^kS/LAK-ZUd^\c?An(,.6RP/J_EgWQ,FDUP0],LmY99gAoT
%DXi6i$/u5N43_Rkrs*cT:H/ZoTUq6Z?fn/sY>[+[&@m#G6L[T?oVe5`BiHHL9TG7JbqR7ji3N8EO=jb%W(H5'B63!5^:*QsN.$79
%$b`NR)mZ<rOc%J[EHXfl-k=6=K0L_S94/5=-&]?[6t..U_XOSu6Jj5SVq5lN)+iUMb!<m3UDP=jg#VC"+r6+dkhq%i>+NTa;'Lq+
%MocMa)bHme2=Uf<ZROT2o8];q7"=C)Y)#rY,p!jdY,@Zr*](-R5JYu>G,8c<8(RL@/SV0/(4;q@$J3$bSs&-Zna7u1%bcYX-7/It
%]+%*_%T17q\!ig/'-Eh5fTenm4H<]=%d+1kPZT6*0GZZtes,VqF3c'sh)/Rf)AP[>H57Vd5#;U0Kk4O@Z,+@Ga#aBt*@UFc]nb8;
%F>hiQ;"jq:kZ]:p+Gt#LJMdUC:@3q%[@/Q7M2mq&"5,1EHu+sbK6\KoEIqG]\cj6L>/n^Cq)jjq:`SJnfFY%ma5BV$%PZS34;lD2
%n)f@/%)MWgJ55&I+jiT;f2$">'ZG2$@`E4RD8cZU#>OoO9`Deh;RU<a;G_6FLbu2bR.09Pk)u6OB1mk41>,\Q6dFFR([6L!T,Ljh
%rc1sA3O,PN75;r$kDh'Q4n)^BMeb0H"]#455fk%3iB;.5A<+/SE&1oCS>o*anmlMV;B^!/pBQgXR+h^B$Ja@rDNE=D*X/2$I(!@#
%-CpjLoNr5hboEel.ca6qZ7EL4ObbhAh8h.a*&c'/-te73gPp:WAOE.pM.ruLR%ij4R>;G_p6PR0)WC(ZQ31[%CT8r[8G=a?oS^;4
%'/`o;gWi^AKh4BJMAqn\6iu2"nj@PmOR"C@V2Y_D;f*9o.PNoE9it5[?9AQTpVdD+^/-h'-[P"$USc7cIEIV1)MV[$0^g>L"H%($
%<^,bYEofjnNWr&e;!)G3VqC%nLEb0BhI'q)k%En2qKgFaF7Y2\2ic!ie)P\me0FK/YPYU/e+n<YH"^,gUE"u$J5AX8d6D85<<sJg
%NeOJ6dR?mZM+`WDFF.luZ)PXbYsIN82l2Y9'#htN0j,],o#<)(]ekrd7-?n?(Ir-0#<3T+TZ^!$omgL0gt'Ra@_^56H,4/tG(X+%
%Ck`=qBB#a7Pn];EhOkZ-Va<.<XfD,QUF?OtAA/^`AP%n)UC/.h<^B^/l7*MdQcOpe6tqM,)s6eqFip4s17H(;fBd4?bmu@iJ1BAA
%`8)aUVf%7D_:-c/`dsVN5MhTYR@_U_WKde4;2CtO`^&?2Pl#cj*LLtCg#CLUH;J39@:C*+&p_X^j-2mjYcHnW3S*iZ*c`.%<9mns
%*Y1&&nZ#D.'@Z0&##mES3]*9;Nd[1cVZ`EH-$?#;MmP.!>No`?9W9J[Wlt^>4^(&R/0l%;I=jfs-"nD&@W^.g?qtMTngYpPL"R4d
%#V(^E.oe'>3g!iKd^_.E#BY;+q8X-4#ZNgLmG)3=-l*lZ/-`LTO$'`q&9$K4hNKjal7iV$j&tN(iJe57VMIM75;>=^7<koVBd\oj
%[$s+JTb!B8D,^oCW8f;'i5c>&U0]C;BfHNhrs&8;fF=iC5TXU=3J>LEH[An`jWZ3!1:o8*jJ%boXB*q=9oq3I#%r_ZAG#]5,AjEJ
%,0p?2iWd[SSm8QC7bhL+\p$:q%Im+Z7Mt>W7o8m1U)hMJ\Y[)lMBPQ,$'$(5omt%]\SO<9AXTb&&d:9Y-2slg*MRGa9+-<H7b`bE
%+e(%+c2&S>Tes]fWZ?`$erIA(2.88D%T1>^Gk>*;G+qe(,oB"VmF3cs"/[g`%,"TN:d;#KF0npWf",P<&W&h$PmoSi'6Kk2b[T,&
%8<C$OI><EbN$NH(P/Pgj5p:Jt<jkc,-Q_>aVi83s,aK(/3sXUh!D.ae\\L<'A6IkN%XL$(Llr]Li$_jqZV%pbB@pn=Si]kBr.'N@
%MIf,E-aVgbp,/,Z,iFR`-dkp<'3R[@+"X?k"hQTF1p!W)H%?Ru!nC$L97)O-bV>JZ!&50!k7N'D"_9D38-uqC+N/kPPm!$U)N6AN
%1*<456`:/J2HWM$gfV!n#8n'ml(dXIBF[9eL$<-0h%,fEo'^X@N(-MmWP9Ggc4Z\a6YhQ91?d"_4F+mDT(KVpU$dW;9Z8T]J`_PA
%a+NulBoA9a2^[a<=G=$4]\m:QS!hW=7LO!o9p6W'ik3&H74=FJj.n_FK#k0V]&LBS!`IE`c_eQE8775\Ak!GirF=nl'ko4eF*u9T
%/lAr)jr]*aacl-_Y1DC#F&IK)#dpFtE@aCHWs>*m7Z51+&qMcJ$gN>U$nXQ@-_+;.@l"5'W*4+2!dYK]j'`4q#L#`X0J3:?%+GWY
%BqC<tDGXunjjrXRT0IulQodV5XBC4^l1DsE?I[@Fb/!W]$[O)q$&Z^HI[8V5L*(64:hh@J)cK=b:RpdNXI\.KRF2bWEkODk<]b:\
%*V<emU^AMTZrXG"DMW@I-Juqda?l2#$j^0!a@mhbW^EbWW%!"E$n<3gfaV4%c6:PG:(*s`+BWB$D+IV2TL+ctEDgCh#q2AL]*eFo
%,pKc?>'^\.Ti<\)`eZ(sK4SiH":]u]a'm:mH?7cQk]eVR#'43"1g8;c@4^@?_T*4()_:.foEG]M[#3Tf<86PKg5-qFh^k6aj0YfU
%EAF@&i=#/UU?=;l*p.:R/ekM)eT)u\"19X^I(=i1^SaKC$pYOg@MA!f8U7.a!8LXY=`s+n?KRf+dfMGpJ)04]A1g)aGC4CRH1Bnl
%q<n+&UW`W(m,IiTGN?<B(o/H`L<g8)RN0LWPR>Sl+:K44r;VOk>l+6Tm-&ikajnMdm_bg#YHE)A^+hi;_`iKimEaj;DdUZ-GOG$3
%>)'s8R@H;n6+[L3_.?fN2c53T:_4f=r1Nt^s3s/ZDB;g;9I%iP6Kk\UfO=S#$0d9DZ,gQdb$)nGrj2"Gs*A`6p&F'E<L(+YO<O4t
%)YiRH4p`U0ITi?05N,1[:<EHMPV<Fi$^F+mF9c+fT%mHRM.=&t/e<B4%D5\5D<r9uhS22+J*Ld4KY_?W8[NLPNLD-cFH6#-\rca]
%g'kAI3%IX=llCl<^HC2!l>&2#dFsDkGEe\U/dS/N37BjLZYlng=R#tQ\lons_h#;?PPqD^/l<n7=l(/hOWd0T36<$h)O;WY8%7%r
%eCmS`;k*?<6gpY%eYfQ.b:QF$JN,Y%`<IW<6t,TuPB`1Ie?--8[5PRd_rVnohOiWh;dZ$U^,Cu7#q&lhjc:NX;K*&.mJ>DfUfs8?
%cJe>S6gp)bl;LcR77*3F4JK]E]/R*#&nWPlZlgW&\(2nXg4XVZQZjEqIn6V9?#>n&_Vs=o>t]91iWn\$SBE'lc/#Y=VcoRpS]0\2
%LYb7MeYfQ.I^q-l]Gl\P,qpR&UXe=2/`p5pTpr`*2gbGr%`@R1>77#-6C$l@`@M7mj>tmRLqcX!9O;6sfo5oTKZ/g2QR/D>@^\GC
%l81l([1pH+o;SO"FM!UKrsA2p@7/]Gl"<4S[BAccqa2B!SeVp91t^_3K.TD`=N,+3]"jR4(W:-oH\*oCG!H)ee,qDY9cc8<XF[9I
%^gKuNL/`.?&W9KibO7;)gZi3&&>2i-Oc?.>YZfQY>df60K*=dKGPiM]IK^Gpd=-jE,Y2?K)BNKB*Yc9KVh&"J5Yf;>Ee$_I5;`TY
%E,L$*_5Y[gCP(,&%D0]5B3bWlFKI]s0YeJ>b/;:+!)O[;d/!/D0X)$nberRO-8EC'7V<r4WskH_6W4'2>JQNd+;Y)Qdp3hNBm7JG
%>"cY*++53Qpb=0)TK4BL;pXT;&5'o>!p'c?VdRirAU%1414oXICbYg9,['.'=_)U4]6HCQmjD_?Q<K9nf)fV`S1T.)6,jA-Dq18i
%!@)O_V1a`(=C)e"4KubLDRO:o*`U5`<?3l9\Q_^5YVb"eE-!Ai7W4iZm8X-Z$-L'84rbF<6dV1M3IAYuiaS*EY%a[YF#Fa[K]'(@
%A!m[dP<@8G>GhPnZ_2<@XEuom'3'$6->kRm:<ca/@$^jg,!sM&O;OZ5?!V-DrJ7S&8@/!]%m*^u^jj/NYOucZ;[+tRFcSRE!i-'5
%:#FK2@`,,J'RZ:\eKE@mLr3J4&8Wb+ERcuBA=`.?0,m:*&q&MK'01/D,\M8ea4Wr!ZqE/:abmJ?\h_&EC9_14XG`7pR:E%H>3?_:
%F^QQCUl@o$$?%3ml]h+5OPRh%L=0Z#S#F_qlY_NEj;O4jZ8J>$cD.rpY!EmR8dU5"o..tLFUa7WRXM/%.,2_r["/E[*):VK6c5F2
%d:jZ(fWmF:CKBO6<mLu=M@%jD#!TPCOLf%$OXG\bG:kt!0#ahLgK$j)/m8qoZCE&#oQ$_OG,LFH_O)IoMtUN/81UAc!_ssI'laJN
%`i0]%qG5:uY>R^aIara5&t6,V0ho4f_qAf"7"6Xj.&R6Ha/e.kkaoRd+J;nXk/ri;i%VXPD1\[\i1Xt%#RDao#:fh]XFe\l29lY;
%l:,-;<?^uciTitfRW5oSh57&eRTUU>.=7`!3_G>%]YWU_m%G.8MF]G<%gJ]59i8=gLuY#ZQ@Yt@GV4"RPF)_IIVGA^W<MT'XH_Fr
%f<4)++_o50CkgE+GulDaC^R?4LpMZH#d-e1-"(eo92#\'#\YJhkm8ER!g[?)_7EcIEVN'SkU3Ka`WuPa%28K5=ocd+^rOQ`@C&:U
%\P*QWKiPA&e%%d1r"%hp.=uI;X*9[GM\Yh^.lOEjXEpW1e1u5O2ilDlm(9.2PH>o0'`jcc\hajT\tVHj4#9fLpB,-q\f5\$Ae_Im
%%<q0`]MUsE-r&l=3DJjo3/oCmLnQ&ga8j(4!AL<iJ4H)@\9ue9.M"R`C-TV%7T)X)cH]elJ6&n=!70Qq_h6h\3b[!<#%LSa+j?G0
%da=BJ9%GV`GY9D`+[4V'f_68b_j`iMgb95sj-WjL(ho#ZTapaQ@I)QgE_icX85K!4XDc8fdXso2)(9`t`R,*\m<jtj0%d67'q"iO
%'K)2V>B7]-[p$1/oXeG=YuqK0+a#6b[qVAqC-RhK%eF(T:D!WLh2MW*]2lcEcDbcgd@%m/X>\2!7_&,O.M2s.`na[4&JY+.\'qio
%[F#7J2`$5GqpoD73)[>8#Ubm(G9E.Z%IM7PPsSl,bXL6L]-FQj:igf'?usV:\=L`=*DX5a4)2d6ogp6b.)-K)U,(Q6nj?A+P-kj!
%f4QaiLr3m!RmL)fd`O\7X;)ZT8V3N"=VIYV2%RCQ'eOlCdlT(JmXnQ5Nq5MR"3KjZem_b",Tb"BFP'46j4&sf74cEU+gjXOs(QcL
%<D(]sY?J,'f1C+l3"AKD6kQ4pgbi>&#0@286WJYF3tSVXY;MnWr2o8CSsTjQ^iTSBUtU^DVdNd[.?IS1^fA!D[on+kM5)3Rf`W:l
%+q"./B4YbC4D5*Q?C/j^4g,uBF5,;8Yh*cmVEnjC4/1tLg(q/jn$[fP:SiS>C?b<qJ!J:R-s_<eR)hd]<l5uD"hoL;+i<0d%?-Nm
%F^M$<,p&GjM-=oq%;g)Zi1[YuCD>,!/A!!bDh2qD2d6XgK$Qjid!ZZ$<AA@,mT=CH;sT[3>13-@Er&q5T)kqKC&rr%!#Kk6RB=1A
%2U+Ssa1$W?gJ.iW3f!Xl#B8g=l3e[O_Zd7a)?[*7W,au%&>?&i.gH+M(:5I3_:='A[l&EtA(&u47KY-6"%*<3Z[S-=bj/mDU1,V)
%*(B,M<:f5tKt1(Bs/A\lk4,0>f]#iRX1AuS?EJ9F?e]_p!klDH^'Ms"eG!,Op6W%Af<(0qN#]C&]$b[m@iO^Vl;$Y*d5J)M*k>bU
%EB?%9U1?+b79)I(6T?YbIG#l8?.D:7+de\b"VSOKCu2\5"h,/O"1qlTnCkjXe7L7H%JkXVeLb9)RZZ-*:s,$Q]PP3WT9gg5`g/&G
%`rgRu_lh>P<`#6+'$DF9k@q2fCTOE[I@99)a9Z*OOVpnDPMDF-l"N\jQr6<a;UU>$0^1e2l'4k:hlg4e9FmQLfJjffiE812+k$g8
%L>o-lg]<.M;b]f3$*Z,u7oK@#01IkmC_\K;_jQ2l]?X:oPlc=2YUe6Uf/Y^H=LIE@3^Gqp)hoQKe$($)`cG!41gjfHDXe9R!FE%9
%oKaU6J-8Y*7cRsk'ASPdOkI!1bZ+'.%61i1f\g?uNg>St^ae>o7>Mi*k2uTl)pmNCd5W&rK7NVqUZP&KRt7*mc,7YXU"G=!WJq/7
%fo-`;4;\>U)su][o;qU:2WJIr/dBq\N2&+,8Y:bnP'Th<ctp^!3N4`BTs^pp'K6/eat7fi_c$u[`mIaN/\r9I<Y(67gB97;Z'^2\
%RC$Sd<.aIB26Mh[K%FIifslRL6IqN`>J->_'9fK@8tBQO?t,-KS0-:8PUE9;Ai3=0KuR@,_27HN$<D#b]RDc%;hAC7LQa_2i6Ve<
%UXlL`NT3D!'10+H1)D?=$eQ;UMa/_.F(Ga+Rhg7"X-!lhIK3k.P4CqL/4PMZ.7JR1Q$()[huYfP.SZCf<N@7]c;iDVXD]&IEO6U8
%RUqtJJI4(3`rfFR1N)_l1QIEI-5_XI<ptMTLT0#JB7&8GEKoh$1NP(5/U**+NNLGbP$!?A)>/#Y/44Kd)+R.K&KF]X-qA,C@l'\g
%K*g[%1BT"9,q*/,Pk5pZ+!rKTMD^B%167>n56tV<[N7?%EDePj`9Wo?\-6*3EP\nEL7OTuFLrtkj$]SA2iG4\6qFbmPREC<Hil2m
%E4\(%<o]UN7I@uiWH\HpF(WSV<,SeWdJPnAMW_8t6Rd10([RF&:p%_]T;Y:@fW=OYS#RPLgrd(Ie:tc>D:8bm>UqCYnpk*q=Z\Z+
%eF71&U<h=IBto[soW3RH_FT^#MOtZ3882!n3UVgP5E]PLX&c.=aI%'dLUR?-nQCPE(2K.l/Ss_*;-Ce](A[P#4PC*#[12_Nl4aEl
%BM:CgC6D*!ef.]u:lU1f[:_=mmG+\K2$M#$>^]9S="oZrm"e[U`e1E)+(M(`:1S\JO`Wnm;qb^*1hlJhg_=_9h'n[&<r,8d)f(+H
%k6!Zl`Mg't[^Wd]5l&F83`G?FfU=`b1=VX&$B`clRQ#ZZ`nn?7)Csgt^1jBNF)0[7?uOq=n=nVVS2RKHJbmLbT\#P,EZd44:#G<L
%jasS09*U^=S`r>ERQ<<1XQs\FJW^&q+l>=+0QR?iD_l?iBftU8'U)s=,bil*)2qKmE'I@kKb+\EIZBlfc+q=Pb^-763'(7_9sL"!
%D!jueTaGs;IFW`]/PF[#R&sr%M)-iElThqA;Jk15Un^,(=&H&ZTh>3JJ3h!qCc!KaTMaU:7b,Dd9_i/Wm(dLV<kOjAl@mbaH"Q0o
%Y)bCKl]k]Hli"+fB;sG0NP'gZeo"234guc`AiWm2k:f<)6YGGt]r79"9FHgqa1TE<)eXk_7'++qm9UZ%6ukPm=0CJ)jU/,b?a&7`
%M)j0<=5EI+;^WK'h!4Bbc%4^2%#s^NVFY$ErWn_88-dGPW0*P9fiU_B4[_B9\)LE#Weu3bE>oiEM(dINIO+ST0-?SH[Zr']7s6,9
%PQeW27Y56DlO"5R),h-SIU*5=h[2uE27o3T5TIJ'^[b3C,+Ydb9"8YYG;*YVa]797NAP2=)J#AdZ61JA+[V@&GS.MkSXg:nEi^\)
%nAu-A3irAB_^j9jID9O1,+W!hfgg*2?Pak,Ph:'CBY`]"a@'/`A\qe@YVC?7A2XPBD(slJ+^XR"fjX^b=Jn"N*@f^s=IJ5j>++c1
%RtaW\,LuR6?1k2=7b?J"2U)_mL6SOV>et5s=i:d%XnW+(gOm)nJV215.d1sPQ:'42i,sMNWBA#.PH";8JW3--B].T)<$)CZf?38i
%8;H\sR;-g+MDQ5mBAM93D\Hf,%a=eHp<8n_qQi1e.b:4Z"Y]F(ZfW_T>iQHfZiU+kBY:LY4Z,`_]E>7ul^0]1oBME<f/^_fk8A)l
%I3B8P(&QU^Ri^kG),mN^'"g8)OVWp.0!87j>e)%Y]GS:Q9`V75][_K:j3q2E_(r\F9O$?/W+Q7X(U,B+GWK_SNb[,"[.8qr"-,M$
%]+E8O9sl0RVh^:3SH>7uR`qO4^dc19V"i),.U7Ka%0elB`\=l"!D<_?konXh:UW*K@L$aUmNcTXj]Y_&MFhYgZDNc)XRnm1aO6&W
%eSgU7gE#Kjb7&[sVjCOJLe/7uF1=S;3N$\7UK*$4o`c($AaIntoHIdhi6Z1n$XVt9,6(>$l_P*OZs=ltpc@.%Pa(F>>Brdhm+9E>
%O]HP0HrI?qRR3GX=^MERi6Bi91`6r#TqW9lFd(*NaDSi.Ii:H&&?4'ih(k1WR]a,$U$eCdn87t:ojL)UFu]6>2a+O>lmGTk?"8#)
%,\8JhJ5^O<*_esSAa-5<+XWI1ae:lfKZ_S84?FPK&2gA!#<#(iLYMWpbi,B]aYdqNk1EI%jOM?Xad`Q>Bd/fFm_).hf4Zh#,`DZe
%$W3Bt.fgqOl#4M:A.F3nCmDd`h@!bKp4EJ'Z]afA31sgT6TZ*AN`@`5m;r\G%IPV#aL&.?=ZT0P[ChsVUNq5DF2,UR/R-2P_6>NA
%pk-1.^sTeEa\$uDU<$MM>AC2_o<(7-RsRnJ+)YsmDVPp].3j<9]"2#@\kO[@2%9lS,ht;g"]o[H'eeM_[F?O@UR\T;N/*PNk:Ve1
%c"T!%`mER\,GnUIm3<&KSTTKr>7R%goa7l_gG_+";2,bVhBmP>`F*MmFsaAp7aX!F__i''0AEd3WZ^#GZ$#DKn>V'GW;`[/.&_#l
%F/r\XNR\aH-M2b"d"IILmA9'UrXQ,jkZaS.b-l*mj;tFbHFJqb,Yb>O4VHfI]2##,UHMHoXW"G.[LTK>;^9N@d3p`XX.]ou@;j2s
%W;kQh&K6K>@'am_P:DY)'?&4[h4Q6Pqmk:Ve'!U#j*Aq>R]$8"YOb#MaJON@SQeg$%*@[,[l%RF`sl@a$55'?1*9^8b.0BQRf6a:
%Rn6I]ek@/]&)5@Y<&<6C%Z[?/4Fi(UY:Fs!lKPGe`EJ!G]B2Z*Xu&qVMoA$2cHHc7HEG*N!E;ZCA'rl#*OiJgAWTFDKC5VS=9PpY
%@^_iVc`*%!3S?=0.H:Ebn$;o.h>4hlMk1T9OHUZDU.#_?D1Dk]1Nck.fo^['kN2/-NTN8['%aFO9QSBAdXRE-?_n\.qc2qs]=pkn
%U'%Q:JCgrB!8"Al!"ZTR!FNCO!F*1M'l`:2[XQkolE4CL9!q^&Df!>6oaQ28euiok&%K=[^34o<D!h'GfJ'd@@s!,$^34o<E9$d!
%n:9gDn?qR$^m44)YWfpbD!c;t]"S2M]=o#!jncE338$!%>i3n3&]n(VDnsu-DZJClDZC<X^7im)Y&=<lY&>/^"0QRXf<0&9fsPjK
%/)\SCD=oSghtXdJlPh<s_"E]cZZ:I]n#<EMAIL>`4bX^0hG0&V9GS)>7;2h5\q[fZ9+Dr%b^q?Cp+q?Cp)q?D33qSm9P
%>i,N&E.(+.euiokf<0$cR]pd["2&f*6SGTJh[6l6I9Sj.f/FDe[!bquc%4NW^9M9#f/J6?!UP@&)`hX!q?Cp+qSmO-]"W/0=0?J_
%nWdlDd1iu-&%TCt;;'[r%H,t5N@E,\Y&D,-Y&BKcCZG7l^m46?l=!DC3QJ8fgU\KNoo6:djn^=^`q9e!npp$FpW).`%82-:qSpch
%lhZ?WC@1j%E:*L&^m44ipTV3B!F37NnPJ&Q^@;nE\\@S6?+IZaJ`2^V)h^:roi<SAE!<p,]T#s(^&+2r"B0<BPl\DCaoFMrQ,1tS
%JSe'9BOq/bd)l>qCLoKA='D!nY-]qf?YO(PKkPe'rcQ`5D!Rt*nSLWedF'n_"V5<Yg:hAr?#+$&"(YT'DDX_K>B-^/.:B?E.IcR+
%`q8T)16g+n>+#J;0%.R>.g'amPlZ+p.g(1$Q%<VQf#dtTTToPlf#hq>=D;=H*mi&+hHai,\:@XaR0"FJ]'YS[joDOhC"d<5=GrGF
%L/m)FA_=_^<e`cBd`SZ'Xi&aoR;OiT/$g9Xe&ldmCg*6E7YDspU]XegoqYKf%$$#D>++d<eB/cHo:.`s1"e*p[5-J@%WU-C"r/_j
%DJR#i=r@gk8h7fQ>A7Zkol-am=\Xe-ZAhms4#O"&=jKB44DaP(f8,DeVQ5F86s/Q1<eek'Bk=lDfmsC810k\TV/EXqlZ$^@]@"cC
%dp[&\NC-m;m@9pEV;)jsi!6VE"n&`Q%LN-mq'B)I-WCa'>m[eB47^E7*_ip:@i/eN1tigEDH&Ja<H[4uDRmegTB93IOS1i<ou`ep
%/H89)ZnjIkR<gtpoo*^t=g:e>hjU!&^d^DLD"jMfdEX"1^P6hZ`R8W19GFQ3^km:.0KjbbW`^S>!b0][;D4b`fn?&*>bb>nBP0B1
%dE]+DLp_c+fNA!#oHf*sg6ltS(>Q8Q!d`$!1GpHmE,L$-_+d!i1/&!VmareE"4h`mW$Z-a<BF,7]*MMuUs[oH.VE:>LU_Vq8V3Af
%`gNQP5&"\IURat67nuq,W5=O%.S_:UfemloRA;PZkjhOS;KILdTdj^'6X<JC@#]1R.2`6W.2T1@1-.k]OtG^JJQdZZ$eGoQ#*,n9
%P:Ps5NRZKY2j9LiPeEhQN3#\qkZlT]<,h2G1G)pT1KfPo"LQp&emotffr*6BA>\)^??A!g`BqrD/QkGe$<URiW.f%0:?2]c<cq/`
%>R9!GB#J[3Bl/cK-7NjblS[C$aGuHum$'<J/r.k7=*=R<%#J4YoeHr9'4t:6=qt,G4MaHJ,7[1?>?hjpZ:q-p!Lkr(a3DMD;F%La
%SIlBd>_\tmP0k^6<oo;8-tI2a3$,jRj\1RKjbsmgj']HdUfk\t%]rJMBJ-?jl=SI3IF:&Y1SoF!)oYLVbe]HWfb?6adeO2Oon6^A
%"u`8p4XP#2dE<fs$^7)^7FbkHYmXY\o0D,Js)868Vd7]nA5d3FqEU4Tg'fKXREQY19NGnPfbSDs1)1<g04VPeYfofpP:>k`3T?Js
%BO8M(<4ts`EEGJ)[C"[dX-\r:Ojg[`=F@G%h2"um@E@fVj4#po8l_WbW-Xl-!?4-^6f%Ar;.6MlM'_!O\8n"7UCDCmc=P_ofS#PK
%R&l)0Q(n2pS0HTGHTEVjViGY[2!&OP\IZn=Zs^RgJXa(*ohJ,Y].YH"VD348X!I4Jh)]#F:dh'N[p"j<L5D],]-r16YDfM2et]/t
%G>gXfXVk3hA;@@cP!k354/*Y9<JKNJY]c)PkFQZNK=lu?WXiPg4+)ga/pHRd30S46N@lThclPaNH9NX'@A>)GD8=Y!Xu9Cc+XhS&
%\Jcj5.c[O@fU^TXX3$\0C1rgQaW4fi8N1GY7Mf##E"e7A-Ze<*m1BHR]e4'h+7%L(9f#l_GkIM;$mSt]]V[43HVHGDnXA$Uoea*`
%du4Rl!4?.^^)RK>f<f<Ohl96d;J<kG1npFb1O!G8qRt?7Gt9(hF\^CF'=im"hUQ&CknC,NTLqU8n#E5uSN;X-A8G>OfFrn,>HkBI
%==:KeB,giI=AAcWXdN7k@+S@[/_3ij]rHsjBhc9%($W*o2j09)]31k+L4aH1`9Io&XPYb9g2=<q?=fRF:8;RS0680HE#Z$NS/#sS
%Z<B?d3R7uNm/fl'>M63W<#8)2GA<=D/RDVt2et?T:>E::e1Sf,1<m1kl[:'^DW4]30qWa"\6*SufXjg9G`dh_Z.JE1M:kfdS#bVL
%*Gd[<0%57hf5"GS#J+&98oQUMY@^S9>jeZa,;nZX$I<m;2bqiYH[@`=RV6m7eWH1TAS?EQej0YFdrY]c?GRA[:=DD<oi[k7'lOS2
%WL>bsTK5e/B#\]L#r+MBm7Fd1FcEQg=32N1'dCl2hB\I8Cs;a('k0'hX$mc+oNKj<@E_fZm[]>n\+Q.1S!urYSK#h=Tq+uSEXm7K
%:Mr%$P',>9k*bW!Zs7fkk#LMVK6>bUMDs+s>&%Q<X&Wgb?u:=M\STb\NG]7DPluTMNtbOLC*P1Y]O$cj.RK/tFj;?rSpK^mT;K,5
%872Y^^<%tZb=E(KjJc<t,C'#.j`NnY%n1`D&fiDj<hc3:-X"bi^rVc5518H(I892$\6=$4Bl@crL$L0/=sF=5"1[TSKcP+<G%p9$
%+Ek,Ied>;tNK"[K!BVf&K272AJE3]1b<c4+iq@!#X:M@I#a4rUHUCUnbjDO1p02J&R#A[tKb(&bCWmosV!^.4Z/"aDSOX=ScKh%!
%fGi0P`=C=HSK4NBlVa@L?'-$maA`Y9c42VSjYgZ04IR'R4!V['+51F/(N7r$.Ns<#e84Mnj*I^?>VjH>nSIL`VBX7%[]4@IrS4u4
%0pKSap'!5dXa7)M].S!j\j:'@HApWYMQI]``]toGmBE,p[;!nP+jK.qC5T%8*LrQ,7,6d%gJQ-HLNT_Oo>PguR--ke<#V\2O263"
%g1f5k9qQR<F`:8o1D7)"rNMPY<CaP2KBc],0)<bfCceMd:fZq$NH6].n$2i%h&5C1@9M1&*BAoU/eeL=+d&mG(Ea=S\;1pq6dlj.
%XY9=D\_L)P=$%3^V=^u=Wl<Fk,em7>ocL,YYKW,42uW))\LO$__XQ]:%"ZlH"d/!N@(j'i6P?K4[umCPnE/Ti6*pg"hV/sQ="4os
%eNcf*Mf%m!O<Y&),gjN.NG&W)ibZRlU$=h[#-8Fj'qC";U$=gZ$CSoDGoOZQ=g^/FT%PQCf9#kY>BO^pI_km+WGq7G>%Z7F,T,6q
%cSTs_)9Y-Hhm@Cg+Ijifm2K@E2cD),M\!\Bb3@#?g#2KV0O-+;U&okRh*k%^#I*B'V_!sWDKb_X:X*4Mc5!@r;dB"b5Xr&.L^,\u
%2*GbpP=7k%c-Tb4FHR*6H^mgQ@<IX;);Gs9F+%`jEOIQUeO@badEl4\CqV]Rh]:AHM,h3..\f-<4^Qpi#+B]h4KYg@!5=WH=:PU`
%?B)ORTa"O\RCCc?;lB&+a>6h%(jRc@4WcMXq#F0nr!biN@p2l1oT*]i2('dU"5D\D2`h@Ykbo,8i2F"MQfbXY]Dca<8tP$IR2EZ,
%0'4q$H=tBjj(cGeSYJlG`q\MjmI[cMQL!cQ(rEL0K;8k1C'`rGZ!'/.F0WG1b:KTBd4E<RlE<a-4Z1Do#X(B$MjGNo;q!LKY/W5_
%c>?$VUOuTG1RM8?AnmJV6`)1M#c]Il/@]4EZbb"ZHcj=[ZUe30l*_VPZpR)aWLGAG(9mI'%':AN),%`mSl9lPcNEE(Dd^8V#?O<G
%Nc:(_"*l4.G!`(9iotRcl]^->]M3Q.RF'>.]0j$OohcZsm@Z2IOR&i,^k7=sXeYs-cHN9()pd!G.7<KGGZuS\Gp'aE34J-fZ5oXA
%ncrp&&k!^@aMOGVSEo_jD#5QG>.+1oU5;TSl<J1sM%!LZFc/mnZ'\,hL`>\kDZ]8FrE1lJ>V#i6-?juV<'b9("e83U)YNF7PTK#T
%p+?XZHoSLHYHJj/Qs`/Hrj3'F%n1YJQI@*c9U(p\-*\GMb=eH[^uRafT7%Y@R=5*XQLSt^,DPDl/2mC\49d'kh.+!r94$-pP:>>Q
%OqF<nlb]j;]&8]n?1OjFH!;)oC]Cm.8?X&EN6lT6g3L`A"R4*GpEY3NkoL5g,fC<m,Kf2+Iidnr8];kO(2OQ,YPHj^s5h_Xp\L*9
%f99,ZjMX8%;g3&l*Vs,I]cZ9`-H5)T+W0isWh[\a9:i2`+"!r%Rr6$>e9\$PUla$DX++kaB1ie#CW$!Uo5j.#I%%*WLAiX(X52-i
%E_oI*2QhKLgMZdPf%H]TkauJ,/%G2cp9X,;[\_l"\#Iiul?Cg]\()o%Fqg6cnnW/tAFQ0m+o81^WXR&D3[Z]r$eUtS`5cajk0`k:
%k%F'Hq:*]innXe=;KeUpjj!M$TDDo!]kpO8+K,`PonJd8F->p(Ft41dQ<t1$9ZY,ubp2V1BN:&J4CY1Rp=HkB#(A$XU&8),MH86]
%dmH'TZt@RdrN"YSI[V-=phod!0['IDh5\unXBTVD;=Ns$Cj.D"6q`:o&gS'u4[ns%4fhk/q7&I[S#><MD+imOd/FAChOLU(f]]nj
%l,WdFm@G6mP1k)%Vek6m8C`U)qg=N1lX$4(\9]]D\dlfc4Y:aE?$#YulI9XAP*;grM4_D_"!Wp_Ff-U5TmKjYhc4GkT8K\4ik"H)
%E1-*k)>Ds"gaeKXrW-X"pQ>[jp!9=o$[FmUTtMH?Z=-h5,&Sn7e6.dE]:8[t)JG]T$(rl,T:8SW,]BK1AleM/mG)Vp<1i_[?W,X]
%:.eVGp-9/R)k4CAm>\0ef&NIFYW_C>9Oq#fI'';qqc$mePh022Us--QPha#(\%`h>o2TS^PiUG%pOgYZ5L$_,53#t#SGEaMHbYmu
%H`2E(NEj*MG$W&,IeU0uqSd)c]mY%pcH3?(n*'&kh7j<Zk=g<t0J4+0:-Fq'KPu:O5<Ac4kiV0k)gQW$`nTtZn]B2'c2R!UIX6<\
%]RP6TFF&!,`HtZ6ci8Fnm@1_;6(,?ohESd>SppF'bDWEj:VONtlSEPX9\UZ6P=oVN/hTorim*UVSaPB^EUnL,XcNEYrq3UIRWi\_
%\bkD/Q4M1!m^%,`T0N;Jm+K?,J1.$SqsDAuPH0o=S#+sP*[q#$H1uU]c-QCR\!Ql-ice0n?S)6Vkh1ISO)KL@:N*oEr8QCYpZUn]
%op<9$O3Wg#fO+#0\pD**HLk]TmeH20UXR\U0<U<f^H=O-7mC-[q:9H9?/@^!IJ!3[o(Vk*2g5e.N:mMHc6"$NP\'dho]YG658`pC
%O"Xh05OY4U)$bVQDu7NI=`'(Xp]&A+mha#O2IEd!HcUZMbk$n@Q`9OepY3_q*h@bfHa%jCI!,!rR6L=$b;fEh?_:Y0*M)VihX&ST
%ELh^[_^kmSH"VVS+d;]eAI;\`W%SU.W)IDuIf2\2$Rb$H4cp74?7,!Lb62&#j6h-Sk=91^kI^8dSc(5,^@p+f4.4-$&*WHDs!?31
%7V,jM5.LN&(L4)4eE+;Cm"b#hKC_PO[$``$XP`IGA3\lF[oU;ko_c#HO-V"1Ir\!cn@*<1/W1K@Ct:e>]HHeFcLE!(Eo2'^h,/g9
%h=CMsr%IGX..?l5m+p^5n`01)CO5Te=7UD*]UT%NGLR?ij8WKbRqnA6Rm#X)+N<0X5528Eja#\FMi@=1lgsf6AIl!lDR[](D,l<l
%[9@3l[qr6DDn\Bj0DC./rr)cS:ZqKuEo@u+T:WbV4#oO:g$eoMIsQG2Vra.jlaP%I/&KkAD]d/'<M"[*n\s1Xo]WJS:7$qH"Y;%A
%an^sX&:Th`K0Jl[LO]$ik32T@Qi-3tdIkV$ql5SGk:]SJDL6gE%!QVQ3P)f4m@F!NlM@&q%lO'>k3$Zi&)oTpm,?@6jjL1LdH0?#
%p$WilNdgbF%.;S!Nr%)mG?Fj6*:X`!V7_29bCBK]=b!a`n,.\@Q7p\WG7Mfs4up/6`>]$2me!9TV`.Zn?ZAosS&$l2(H3GZU%E^m
%;u];S0";;UhS@8ohd9=KFsb2#IrW&LB7IHVIt%@Dq=^kMpSU",5.SIQd7ScKA8*e[]R:$6o)#2_'3k_1h;!Jdm&g#JAao6ho;0%c
%DROoFi4RUCU"qAGDf?/[kM(%6=up;!2,_W`bL<5)q2qjm^Zc*XWNV7RBuU2??'I=Gjat/#.fDb!F!_'\PKQli_E^*/$E^l6lgOiJ
%[e,(kd(;Mk/"cfXq<Pe>:)uQB+7B."^YJ8Qc^(b!Q:pl@hrcb69,IMH\'4D%RTfAICAlq3WXVfCe&*DD5Mge"RLsu,^YE6emJ>qR
%?:U&OQQfeBp?nH<DkVhF&/#LnTF\_^c?)5%oNX!0p<):K&m\(Q2W["R]A)K$m%_duEBr#'JjI>%$_h"d4<%m@h/nei>Gda_`0I=r
%9'4gcZ2R%ug5eGtr-Ei;U@4H'm?M&$GB`GRKC"_7fct(S%(_L/CR"cQ/)^8`=#O.UDjCiVq)\uPD1bOcV3HYh\_Ufb*nk#fkNqj%
%%))GO8^]A4pr2FJ2fIu__)hM_o:*qlYdAj?U;"qc2>g`$;7c%<*W50?q#O51fj\O1r@=Bt5L=j/X6TShhjH\%GLu_9^YYT4o_^]s
%S.o5+gIJ_IY(I_sbJ5m@*^,^`pP*C'QF68\AA:fp+gI=DB"]0"A]CJujO]G6Fe)a:o!b/,q58N."3q;/]cMTIG`IaNc";HH'F''Z
%F!TVS07H`;og_7##=mF]Bf`7qZjHXF]4DT7#A1[?_[mBW4cL*.K%o::Fgje:mO$:3kHIVtM<4^E3qg2Yc<pD4p'MVQnsaj<fcRtj
%b4&%<Z.42L+=d.T?oo+6l]48:S_%63[eUs+f3`bmr*HtI^X;'>5/$O#<uiq(T3ND-?[mNEFgf!(Q*9Vg$4kBU=79b2Y9i2UmVYe'
%bX6__^AfLgo]Yu<dHUX_PB/V5MhYGiH$o4Qc^sL,NgD==hi<!l\3)d$EcIg]q8(V#c-af8ZZl+M>j\Z'<FNX+3ki/7ZEIA_.k^*O
%p4]&CHn@"d3E4;m]5gq0rd@jXo6<6EEVB1enml+1T).lMoB-p&-\'tpCrs`@l*cQ([UR!)'m$EVr'uh)fU/^NnG7(&Jbq0<j1$9#
%*a`0Cn#rnUc'LAm?nXaW2S40Of;D_Oi8o!!ol0rUr_@$iY#8+KKiT91X"Gm??2m/2n%Uu5SVkT1Hu[cFq<?]2l.l,,>$Po#:VT#<
%Ids>.Ur4AJC3pE?,Ft.oYAr%'jX>K=lJchsPPd'saG,"SI2C<$d#IjJP+R0;DS/.s^$:9aj6NaU!6J!`B;[C`;Wm/had'uTI-0bn
%eP2*PH#`N?Y2ErjkF<KrSBoNBY.gXS-^i*#6?IKpI*_cd+)l<#jQ-16Kt,rE8ooI8U4:mkIn8[lOC="2p>It6%kZZ)B9-kC6NH#U
%W1S^@n!GbF:2]nID<=Wc^A[unEr#@\-$JLuSt+[XYQ-?*pO(Y,T'njjEI)]Cc2?Pmkh!9^J@")p>d]/Rf,0.H-ka83NrS=]^3TV?
%j7hH'U:A0+a"Mr4oB$#O8,A:):Z)3Dj-*i0DoftCoC'H*H&&$T#=&51&-)+uQ7c;ubF9_oYt9.=6(Zg/]55c"d*t-<ZTC@]g40(H
%ec'K3IEk&[NU@@9KH>OQFnk<4o^m?I_TM$e0tC,<'fQqk"Q]T3>&ljkP'c6XDop6g/\)t[FSBKmrmSO$rHXfo3KKfp(mb1SIkT7d
%rrDZ&jalNSjHfhg5KcFAnV?[3)Kt/pW;?[$r;L#(lEe7OPoBW?/+p+6Z^TpTCY$0P9`MZ]8'C8uYP,Eso]5B0[,.*d<D\)@IIH@.
%3hm_^#&hQSP@ad,\[u":AhX$V>5"D\)c9;P5I0`gmQ9'cQN-+>RPu;(*UfRaDaHNl*[C7:k]D;qZY/$a4SqE2][pNgm%9jh;gXXj
%r*&[cH#ue"dM*?Wifd/WQ5<;(H'Okb,@\S5hP\#5j70=YhgP:#jj3sgJ,T8J-E"'F8e0;Phs,PIKD*;,^-&sZf4o^uc-im5Q`GTb
%mCp8hT76\g[sN&hkL$J*D'->4HHN^T</^D&?>*I"BD?G;SBt%aJ&Gb6nV:X_$IV)crc)Yd[(c!trYp43.,<6aGKe8/Q1^;FQ;%3>
%#"e9s'XH5/#^NE4;YN\r>5FpAHh18%eDqfYc^,566LbmZ`a(T!bDQr]O[=6^rhW78A/=p'6nc[j_lsgnUglaV?IsU602'J^E?=^#
%cFB6;+i#ISR$Q,bUkO*CN&H8RhD7fEe$'PLh9`j6s2Np&[)mSue<[]B;>Vn>dkJYbhHI4@5]]9Dq'\VCe7pE\ceU*P?eIN<EF]RQ
%4D!Jtk;raQ#`]!2l0LBLf&P/-;hcNUP5%IT"%JW/Q)XL)-UWp1P(8c/0CWs1AjH6Wn))KAAZtU`4Cc<bELHWT9d_<Z,:d#3dW/i%
%IdFqFWO2N)%'L&%NE<-H]X(@/jq`YD+`Dg-];>PbP;,EJTs+:W420XpUUj<gmI7_Vd=&7cG(7>JdiH*OVK@_.Hg@DJ3*:+$s4j1S
%XR9F45j06',e&@>p&E)[oAUW3`8A0f3SRc.p5+StU-ZF-j:RmTk_2HhIsc6ZFM@W_q8Md>UT:T$GFpM9.]=XWIIk\mMhAb.N`tbA
%]Q8)jibX*no?JQ7K)3=aZ<hYD)+Z)@jeM=Wo'k-,c&9d$#=mra]K#asl-#".(RN9'YpmO^35/3N_(Ru&2m&k(PNt)LG'*\J@UlYc
%>TW-@XPpk&^:^t\#E(G<r:8pB0jdJRGAk7+\_)=XFM15<-HlGK1Wd%,R5X3`+r*;ROB]q,ac!32;2>b-q0a?6G5@r[G76fiEpL&!
%=7Aj)^=keYpPq@[.8tG7I<,&@[!MQJij5=-$D&o;7p#TZj):9./sG\6[_=2,):[h-W$g15Vm9?pM>V2K'":('ebR2S<u3N-QI7G/
%i;6__F5O^59n$gF1u7T33\E@sli#A>)gnskk%=AUo95Z=B.'T)2hd#5NRM!%[RpGmkksbAl_h.[hY>D-pWlWAS1U5QdPEOUO=R"N
%LK9'rTA8e"I>N*VfnmddDlm%jC!MM2lS$Go`%X+<Br?+Pa[\"%/sd&tacD;sm(Ll+B1JjAmk8%<?IuARM9@bjs8:o@0>>*prDDt(
%rp*?40&&&:HMd!m8=0"?([jTgq)?(dp!^D3rr$oK?cun0CN?t:rU+mu^U'uZ3"Ki^]I3\:c_Wjm2t&s+o#9:gh>?B]pYRP%hS&[%
%=u_dN5$<nrb<7CX_R9!g#Q)hchYlTVr6OeY(2`L]%aB+ETp28MIpX2;m+Hfks*B0@@)fb5k@NG8'leP?^>E5^hQBf!LPLd9dEJ2c
%hL>LV02>;[rqq5EgG27A[g,siU%3,fWI<N]gCD]\iD93G^H&l.hR'Y,J+`e&fD,3XOLEJaE,g8I.^QU2\p`QOTc0(q+.P?P/oGPZ
%c;la%Zm[d`3a.QCh9s,0f=/2$F"4";S548PH2?hkm!eQJLEGk(/_:HbO<>[qD9'EhalP?c2EuW(?0-ijnbsDd4To&n)crX2h=Y(r
%I/CMNEI0_e.XTi?jP7JW2\*ODIO"</r:K7P40gLI(OXWM*\C%Wr8a@7>[K_gLQh<u.bgKPgBTrBPC1jKk3J4KIt".i"cAMGY>2VO
%26MB6R!`s<<p]CT0:mT@EVb'N?d0A!Wo*^:a/:SV*UAb.4uqEH3eF3<_[erF\GUM3`;QcakJcO,^<$C+.T*YRdJh=aY><:th2KYk
%\fr9s2*ZlUr#FJQ\!R!JVp5I0"fCb`_nkV#J&`XWHfK=Pl=_BXrI/t(CQ@QGr3BF;_#8blT!5]UmC)OhpZ=6`b='&:L"7Q&qbBA^
%+h7!amd/)_]IP%ngFrGh4$;(Iq,Nj,hs"4[?e2IZQi/Jgcb<U0Vp4#.IC.RdiYd/nF]c0@3EM4Fl90WFEHQGHF-"AHmsTG&H'iU@
%_"6>TQYN-MPs00VQ=#%1c6#fQhgG6h]uXV$$1'nZ/ru#/0kh5bDO,_CoKH"30A\k(]_V9krq,jZi.O/HD@qC;WVRYNqos/\s7=5*
%9j>$[qs1p';-;K@GIh.BEqf5&bP'Yp)ZTMjA=$P3l!5#2egK)6ae_,h[be.sLkXR#<eL,tl>Vjg(MS1KHAk9pVjP1E]<!2>7D`9*
%qV*:\A#\GWKif>/=g!Ne/7W/6bmdlhqi`PcO</e_QS=]\EX=cf5<a0hCk$M?Qc=repK*]U4LoT9?TOnE;3nEB@Q03)r:]f(l+=*h
%d*'@J3U*\FJ,Jegho_5KY].%`k*r^+oAtN.9-pc:T"VW_(Q.W-p/`^*#C9&7h!p^YVpDR?C\<C0/cWD]S'N&RoDJHkYE,ihJ([A?
%mB^.Y4c:trDgCib$A;TPli,p[hc7utTYU*ll)CLPQVE-"niaRbIJekj`.*&Bc^.T$4q0Zg3;uO79D<+q4o.N@MXTF7rpGfVhcQMl
%2Kc3gEM(YWRh,LBh\?(_lhbT.o'DEIP"Rlt?X,8IkDs$JH_j[5?a#OOW##QTZI.HWVZ&I(bu15W>CHLbQLa_3J,0c$n(1qp^D9hf
%hp'e<#h:hRCQlNHf6A+Fp><AO_b8bUc2HenO/+pIS6t!E0D-ZAO(Ngn?Ma]QD&_[.U!&XLB`nA\qMKX-:S7P^1p>\ZDf+)^gs"l+
%qU*j8(FSD?C?"qW58k[H]Aq@u(K\ZS8"u(E_)BHNQODf:hnD#P;['nJ5<h@mh;A$M5;V$6IhXEK*Vd3]i6B["(],.MVS3#T]pcM1
%dGL-#_7asB35>5bP\4`D]gmkN5PVM-N0gpBF#^A\;fPVUr_^&jjZ@tX-Zm*phu2IjC#t125+EZ7hfBLda+"dTI,gh>rpN$tp"=On
%P@s1rq-uD!?[b-9Fmn)`:B.)fd`(=madN!UrMV3S>iC/Hof1^`*&4>VHWOhYhNdnLF.9hoRV\QrkcHa:C:+.\_PmGHYMGQB3Y&d2
%hnRXTZ1m]"cWbDUH<VB$%QflOh7HIS*djtZP_[k>Zo6%>nXj=nK3M.tD8!F^iGUE"RD2S$%qXAi%Y_E4kEH>0l,21R+#S]HoW];^
%5OOJo^3/*l^Sl=ph8q\DR2=I&`Smo+A>3HmH+rruRP5h+-;:nO`CK2-pdoT8o^*'KAo7CKpR.::mV'hfAmA3j]N#K%[k>ETqs*,r
%Z$]l*rqSq<(U^ntlgncKmCml&RWEiG0<Y&m7#.=Um1%JAkka8;5DI_em\eA)?\%n-.M*+RpW_g;0<R3>]=5G]pDuKia>:?UhHd<r
%7Z,U)nG(o;TJU\g>N>Qa^#W73\D"=+*upp=LT=K8%a+*PIX1R5J$j4[pp0^8])V]R+d%O5Q[S2\r:>jDc_C<$$A\1b7p"=:0D=Pf
%o)9/,27fr;F@7_Xef+">kO\LTAKG2AIjeOHl-\V1YODAHQS7a+X#hgZA_ilHet'JAYFD9cqr6:g`-B&`Q.I=FpPt$UH1m>1Bt1:3
%TC5"2Xg6L'h,-\2gdIcS7g51^FF8'\r)G]1rHI'Zf>kqpf.Yg'QI9YYeAajqNXs1h@"U)8*juM<\>q1,4D,M]>q]KT@U&<fPO"]:
%II-NFmGtj-:1%0HS:Z5MhqF)c+mf0FBtE]R'62BFft(M0jPob+M`^2PUNUs(f47>g`u"JW\Eu>lm?O1iN"1*cUqURfL$r9_l\Y,N
%48[?j8cF,3m+^>X=3Dc<h?DO0fuTc60]+TQm.g!NNmmbWKY)a'dbXA:1]DU*ncC8"<nHJ6-XXWu`6%a$$"^hQa!Ro0QLKO%NKC_W
%];J_An1HA3mQ,^VdBc9?^N1hU]3j=a\!9mF`b,RQ^3f%V\.ZQheTBJ,q>'j2dR2@Kgk=aVkGOYJT3lD?3Od<lNIAo3@b(3+kl<9M
%lb!$k[[LcgOXckZ`SL(R(">=iZQ6m"Tq<q-JTs4.D<\5*GOBg*c!;P@o,6i#4!'Q!npGq8o#:FJL/jh`9.uaolHfS]%mEq@aNIUr
%\)ct)p!NH=PFJnRb<*nc7/c*d1<K*KD.rI*)36l/h>JOY/P#eO@?XrNm]b3`j8%n\?XEWX#H\U;-(F&Q+s"k=4=qK%I&Oa%C3[2$
%#&YKnH@E7-kqC>ClR^lCM\#&0pWdQmn_$;$MnQXrEk`*^]&h?"ZZ!Ui@fe8Yb1\g(qMdF_p[IFbh&l@:QHSHR3#$l=`m[/"o]4I[
%G@(li/P14l44=^)L6`mOT9hh%4!*_KT3GLOlu]`1r^Je1\6c[A2YX@]Q4R(o'YJ:tfembm03/#,\O'*\\q8(7f3b(R/r.^I?&+D%
%GI:[gYHIWDb'j#ZZ@<pQXkV?E=XqUDpr1FKZrl8[l'Jg4IWKqAX8hSHg&&t3ZL-jV-US'Z>tUNe3lSH]:V0HMN=+q>A+%q-=sZr`
%;U!RHGQ-"cF,_og1THhWOpi$;!e=W$C3oKPr`uVRGCCEZG+Z6Ho=s"/qil["H\n);qsncWFEP+J#?Oor:JSXWT0Cq8ID2oNjs<]0
%n%P0]r7e!mIf:GhqgX[q]_0Zjk$S,akSI=8HM$k$&-)HXnX%#hq9Q8Di-tYi_f%a<5QCT-s1+OnIf87*hq`K7]8kofI\3Y>%nFpu
%b/%5hS9)-"F3`I1hRUCM5t\TIPO@hkI;>ml]BcglOT5YL!'s4(]>)9nqmK1#P.BVV?bZl`5#_g)@5SUL/Rndd,+"3(Im*ft^PA[2
%kAm#C$e$!LemtJ`fYW?QN4()IPL7H&WCg](U'b/$d6$%?_J7'!n_kaa6+;;j)eDEe(u5tXb-gu#&)C"MXXod+,@&k0Lj20#9C"TC
%fn8Ls#&KH'O6hH;#HD@6,gDe["';W,C*&G</i>;n`M.C1VrGG+Kq#qT'=$6dKPp,>ef>ie:2#PCpB^ah>a/QTchLQ.N<HQWh!S;g
%&Y&NH^\E$P\;ZJaU`g=HDR,&>KcNk:lRp;ee&@i57iaGMgX_:D9tlWC\#.AAp;Za0f!%=U+HJ,4P`<VE<mj[5<_OMU3G_NW?b\-^
%dlG;:RFQnm\_HRpS8_!3b?`27\&i"@kN]g2@7)7Qf&rJ.*Lh8\;"rI]#436:8Cu,f/k!sX49JlsB)>E==ktSII)n^p!B`qTScT9:
%1sp>$/2H%5LIt%jmhNAg%9oQHYE?d:ffY0khl28e_78URE[7DcIMgo9VQS">]o)(dod!a0GXI^<.[XBt-[Bo<g_,0+]#aKr8L(YN
%VL6[65"A%:iJHKOk7Rri1#41kIX*l?/E%"Cc[r$jIE!X9?'0PO[@![_;1r46jABNUYRl;V-__2[@D22Ck>#(1Ff>-AH1Lt,,uonb
%8_6\^[Z,@4kLper9BD1sSu/VT0"R$YRHTqmNs3@HR5=DZP,=`gWJ;J4:sK(0gd/+"LnumGVrpX>3kLrFQuss<DN@)O]=X"7c0%G9
%%BEY((C'Ia_J#k&KFrrW_D"]d#sr@3!8_dU+MsNshBGcPR,-.G&GAHV6o]RW"+sme/l^62f@E<5`e<_%P/$dhHg?VqljFCgEoZNk
%/VLsgo+=HY;BSZ?F%hr)n7#%pa<<4@qT'pj$ZA+[4^L8_Ni_,*36R%)iG/!(KY&11':uJ`:3[SU+@5EfAV.K!1P.>f9a#O<o)r\4
%A-h%'lk9FX3"oaHcOa!+9bG*#d3njU#r3/+Znqq1'Zq,HE$Mk8,(%I(7"#Q!LinXY,&TE1Ld^JA+]@H4QUhpR7O%04a[C#W*Y=%0
%dnZ,YI$uIEjV:Wek?4NS[d$9@[Pe]!K.B^0%t[HmVVA*Y7)-D%'#PXN,0#U7Q'f"?-=T.JjbP5DEl>saVMLeHI3K.i:Qd\3RO@+[
%U/M7?4h;Mia(X=;M&IrMgTjg<3+HCFi4bS6"u8F6)]1'>"#^@$mE6?bK%4K&qt=;_a>sYF,3S[A&6i28L."8L)Uc=ker]3pMMC+I
%cPe:fo%m3X2JRt)$&#n\emK=oM1K*J"4hOCRPq845[\4ep'f!)QptnpeiI_+<6lndKaf\pp=6ZeFBA:&O^d%=Og?`:UUV7KN.I[P
%DN%QGmma.8.2l,b11ABa2aLrQ>@rl&FjOuA)sp[9:HBDQUXJ@BS1Ua&j-n8rJW4YVM(O]'MnC_]MW)+flA@/ec@0eJ,5p3&M'M:I
%PLLVC9VLg80Z=aKC.^4dgQZn?3E*n>$(@;(?ddHs2G0j/ce'tso@]sS1VDL:a(s+5-N(=qYVB?<EMAIL>=4q9t'J(DK
%Pakh"a^oa3c=_cQF&h]tb\b8*AaXIq-hDGnim$f*Y[frC-T_\j*O-CGAm2Wf/A%[2'b#PLY=*LaOd0_b2O5WGaBJ/:c/)@)\:U%@
%EcPt6OXo"gQuO.i?>4^:2n-J7[9T/<X*`uG=kQ9jA+c2e?#)9D@'/[&F_WLN6XabOANO)m[q4DWkM&bt/sdV]YFA'"771i=8D$%G
%WFb*9hId5!`N(S^n@*4ZLlo."^1soq];(4"RQ9MHgF11uY0j>eDN2pgG&PT-Z4"3Ja<'bV4SIi-\_'QHc.(Um`1$DYJ]QPHTc.BM
%5A`j7@9-,uLW@gt3r#:eEeIBX`fX_(1Ip,=(cg<*=l08UES%HuofA-!@F0#Jm>IR"((UMF5@kZnPp&/3(0o#5g!$qNcB7'pG-:G2
%a*BOA"CU8q%?+.#SOaQ%!V`;JMI-uH=)i78]%Xh1j-gCE\8+%[1u3n7D<#f/6Vb,KF3AI3E,)tjg\J\Ai4=kofPTd,)c(&0^+J25
%O#,M3H[*_Yl\R0RLlEe:YVeS'>eoD<Aa:ek2RD:e%2gMNF]U&&Y;eC[hUjF1pD/0Y`qs)goXt5pc?h&6a/$s,#=t_0glPr^PTVd6
%P*'Q3PU8CC1E<q1`frK#b5iQ>.bMF6Y\INCkn0>]l3Dm;``2:im8KhcD*G(4RLWMlFQa:ndb.a;9t)+6,X%\u"o=H>=rFqMh(q0/
%+#5Qg=D(5m1rJr2;hS<:(*[O-(.hoOHET%N)[luf2UcgffTsEsAS5l/KtT1;$g6;)Y+3uU$sYP'1NR\WdEYJA$^*T^*!5SJ7)QZ.
%%9%WulE0Wb8p.q1\$dl<P1g\fd:IShZ;2-IKM2$F/YNcN2o>37E!VdZ)Jnu`VAMk#Naam^A]=D*^EB\bOgS^N7VIXMAP5qNdPsk\
%pbEs$B.!+q1#VU_<pd3=QS8s^^;<HKHaZ'5<jeS%D#C0sp>BRnab4"uX;?@\l/hilB#a,NNlYg_Zl-D>,dq"_9$9de0G;"k^hNd3
%%(a@D=>8g'g4P+,EY9?K9\J>Y__E<914'K7Q#SHVa?%bC_0[4h+EUj//Rc+M[L%]rk?bZCba$&?KM)WDO!?+5/LJ.C=^\=nWm+4*
%VRt)M-MkE8b`"S53Bh:5jQ<0sQe0g912787ekrDogI8G=nf7$ePK)hV4E1FlR&AkM@PXJ^`N%O*-8lqn)iCV;Rjpb0E)AAZO%8Fl
%Qa1WOk,WL*-+&P"7>F$4mh0Ygjk-sSf&lgln9^L"U+k?O'BA*FOsGGK@$5ShrFFVg5\uTHo-Bqj3UPuD.:m!<V0(Ri>0D.=[<$.#
%Bicl*9$;DT3aM2J@d1:b4D4">\r[6CN6Y,)[d7RkNEIZF.s<Pui-W5$KkZAH_/jbDfu.qn(ECe8\%>nq.Q_[o7VA:$;a_iP>")rs
%>(&j&f24b0E!hM760@3u6Ba2@$G/Q).=1OkIo5HoFKo>c\Vd<[``f+',@`/CmbUQgC]@QbHLk?D9H4K&U1?%^S+>@VgFG1#M6)Au
%[8:RnhJd%-W+W-eRAF4aH7S#j^M#j_#Xd3H'5QPlXlQf'CQ%M+>^&j[`d5"Up!qs#O`LD*o5>OP5=d[&b>>o-\&?lmHaLosS#3ic
%f:26'g!Dk-6h(qalE+8ZVp4+&D"<duF(c3?9!:ErpH#\V:5i6FGMb.lI]p;0L\G*0T'K5EMkcJI(B)Oc57Vi'_$SMKR9/k5me@Xa
%&Nu0Yf_#(Fhmc`@=*)&BD(G83&T@"PM>):F,rk/l:V@ekUhu>MR'`1)Tq+p8L9WnL)>6/DaiK\fG=UU_UH*X]PcCK_\l9aJ/#C\(
%0l</pB?.M;&I[1pYaJHD<+g6mVFOSA[9-5J'Cn9A2*1FKRj#=F.JOsTD.ERfS4T7uo4#%KRr@F$B#2hfH;UbI#)gaf5V/>ohS1Ue
%GE(^-F$?e[Y2-iBf6s4?^5t^=l'a8"dd(Z"\Q^-ll/5%:Co<bW+q'm`qDd15iam,a^/;5-TsY9q,(]#sf;B:M^""H4pMh_6h+t[o
%nDFpAf0^n(1_7cL-uc!(U@D?c%Yi#VPK3`nP;itjVe16c3"q6`G)gfnNUsq/,VO?4>e9BnV,#I21L#"TlV4qY=8hurs3\SoGUMq>
%Fs_]@nBaqVe*jduY]&f`7O9@3?HAkEapp)>XMW1;L[eSGIAHl]7Zb^S2JO>6l#mp>K5X7D/_mH$S*f>&R2g+UYFsA[K_0`bLA!^Y
%0@f:\SbqoedrDbp]P6!<Xh^t6a.i6fb62XkB^%JYc(NTOC:Q*YV<Eq/cH4nj\(D@^"Q?XhA#kaT@cH^Q3,eFXlH)ko%Jt0OCHcdQ
%9nYp)cAQ:lAW7s\;:`n,7@GG"^;gfl\;los,tiT99uNYKcM(M1CNP[gk99qM<e\Y"BVK`6f#9"C\S3!"$;/2%'AepSA_i9Mmqrrc
%rc/gj`HCDRn]"d"@85#1H>@@!)eDcV`a;V"$.<'lPWN0](@sAskGk`KlU:lE6ndQ3G]lj$A\KI,alset@!s>UKAoUnpR2/PosK2=
%kjfWr$LcK&7YV[i6KLt)!2P5s+V=J_lS&)>\?_XOb`R^m`O/:?\rD&:Lt66c"#eT]k+'@;>hKlsZ3,SA2DZ+][,;js.:YLQS)#'X
%7`Mrk8^_:)Y#`jC4L\9m;l"U_S6)S]Dq30-UK_fll97;A2`XsIS59IW&V"DLQ5Y!tUDK'e'C3p]bko(-,n@(aVQW2K6#r]1/e-0t
%@%@TuL7J*@1%,C,9]^mb[qcc8$Dj<ILN^r_M5:<oB&"\PB\'/Y41m9n`"%3.TJ>j/kcqCJl%\nDkYd4\(m.r^I$65POfCaqNLb9/
%!,YDN,h4^5(%3PY8J'2A1Le7s#CT8L<rk5?fT9_8NAHe)BNKas4KI%o\`,uA)Vq:>JD_4-%#*[n=pZj6)PLasi1I.[I;I/``'CLC
%jmHrX<]'CZU%]7PZ3;bWer7RZ*"c0T=P[YUJ`YebogGr3CSrr^Al%sEaD-V'j*E_I;?e;T[[^UaP%g)GCG^Yd+:Glk&K4C$j/lbb
%p<\s0hA<Qm2c"2JeoOUBNRg9Ao4iZ1<=3h<G%\n[DlQi)P^uPV5WKc\[ZsEPcKAc0p#VZOCaG3JSU%H4_rZguV#El$^33ecqR`b,
%j!%AL8TY/T[Y1r_U1B#M_4N!`;@/'PnKHnq>n(g+F1m"Qjp,\e<.DB+lV*s,Zi\=?4=g6&WbrPcQEXMPp=PP3]Z8''^nJutB,:ka
%XT'H#Wf`&tE_1P<TePU1mU61-Ct>AWf>:(:!if8>l=lTMMu@<Q&Y!E$P0/f_M:cq%`f]m$4!^S*51k5qkH?l_kYj?Kn:M9c9o3`3
%MFqo!ScQ$8!&$'hF'nP.=l,54-?/(X-BEnd,WYg>iIao'l-!KV>?t8l%\<#_M7+APWFs!D1S84`5$bE<OQE%Lb^8KtFjYo1`kYMo
%cB5aLVE;=LbUD=Jd$@K7bcN=#Y2p1P8>DIl-/rCE"`+681>[Fmj;<(]$?X/%qc[D&RhVK2:6msHlmUVN)Q#),CCXfbcmi,*7u+fb
%H'EnWBOun?dc$=pmHP`=nBZo>Y:td^"A:J844U9r+iIedk-DDp1*fg3M1M4"if68(hTs!F\FG?PG6jiKS!T7-fA$DEC@Vp[eL!RY
%A@W"!hD.)Y6(Yng[Dg3eH&=XQTIMfFRn^F,JN.;_:N::gNkJW2QB.E.gC3=4p?Fa\eXti*!B/@CH\G@t>sWu*JEgNuYn/6"?%pDR
%3"ZocGH"'d9ksns`II[],$=q)W&TNMTFmT:[\UqA;,jA=#G`V=4Ll*[;A/idLGF(*YNG$]qGRSJr>:fYj^`%*O&@T7%'fLL.WlWR
%85#q0%2JapaiB[h"q.Ae<R$6?l3!@^k+t=%KGbtK+\W?-WaZI!VZC$+:#hp6(kqYP>n:ID<<;<\L=fI&8'72.4m5u]+EU9<Nu6$<
%EWgA15.1#ZA->t"isg<hWF=V%Vi@bPcs5e[9.?e[!=gkspuIK<a.F?US@HP5=TI+PU%gAU,5%\6@-D;gbQ7[YD+i2)G6iT[hm].D
%QHnHe.[H0(R,kU>QiNB(Z`iSn+u\XSmEu)jR*],ZQ"8sl7W"0^`&)GH3s:2p#D(O6YJ:@a>JaC4]O%&-d!GEJ$`'i_5>`>F]%4PR
%(cOL"65EGCnpp'knr]i&4OpN!+RuJm0^eN7Z>)<_=C6h"9dWTFY:W^"g]=PB&cq9K_XCLo?2lFRLOf[I,*/rAef^WCn.hrEc5(__
%A7S$Y#A!d[$VeN0fFlaYf!&nY>Qd9d#Ob.dpZBT[M4b(t6<G4`)].V8_s]jn_GZA-Wfh-nS>upE+R&P@*.Pr)4s$@FFEc''U9W8T
%Rh+fme,A3)q">a4lremXGig]kX;O,Q,;tc3n)_]>ObFK/Hi,RlagFT]%ofK%d[V9W+ugOF9;$06F!*3Ief58d=X'I/-JVX)\F!^p
%kb7(G(lO\$3RR9LWcE)6aaGrGFISk)PdF.)k%ZLQab1>R3RO;u2;<osAo_I$O2\!Z:`%QB9'Vg5#laR'6=te.Y<XNe(sEr8(H/'D
%d]hGgEG+5a9c5HIs+dNIh;<B$o?C%VVdtC4l,1G;n5&<a=m3Fph^'19QlspS+8W",92a1-!p+,P8%eCu[K/33Pu<i`dXF%k&+MbU
%`&Aa8J*^AHJ"#>sRE^5FjStt`+1(dHP5KZ]O#s8bd/@WA^4CBJB8I-&Rh<oTpF,]'CPKRfDDP8ZJ>U7g^7KCV'8h4,I8Q8%mJjB#
%Vu)mrrolNN`AG9.;I(RfjfJLsq\VBp8V`M\]*%7R(K!or4uM&(Me2g\!WS^.IZQtObd-FIZJNnbMj-':f?G8Z'n2SQl/XCl[Z0IO
%#,^&#<G^?FU#SR,b5C`bh.nHJ05^XoKmT_gU#aS&<EU0)Lhg&][SgOY:j+RZ6F3aV4]k6`EuUIe>]tLJ#j/"hJ#Tc^:G1cW_u=(d
%]5PX/\J.\P&@f+k!$7o7.!pl8PcK6VmZ,`eJ8I>q=n64]Ns?3>LPEl.6Hq4eCMd20'_cqGPb$dQk]A1O'-dElgAkQa,TcaOTs^Pf
%>YRuF0ZaTlkt%A,Yab`\akI-UDY+@7U)(,_V*58?0/otH]W?Y3!"%t]]Bh&qs'sV`'_ocJB@_9/nkgOZofP2>M13cZ9Z6\7+`3Ng
%ifc.oQ:.,$1=:+k!f:iB=4#Wo>c9P7n?`GpZ&kd;^-aHRCg-n$Z<OKNlLjpc\8XLhNE5>)ro>s4mN!>][VTu<eiLYD));ji`K3L3
%1KJ:tr9C_g&a*_`11k1LY<X>C6hAC")s^%gA\12!ADO3Z4#]pFQ-@o9I(D%9)Z%I;Fi#7sNkrq]l@]uoP*BWtYTsn<#kLY$/UAs/
%:Nq/g*MNh1U#!-fFS26lmSk5WAKb5V/odeAT0L@IpMgc37iJ8j<J=g7><=:j"QT/OWJ1d\WSsqbjRKsc3p()'jQ8=-<N1Vcf+;o9
%js"%sEUcdG,nbob]j857(TEj%YAS;)05\'+/QZ]4DLBMIYNT`'Vksq1lDh\[Z*oc!<6`bc_-F&(N).Ui+Bf3l6&W"/AGfGcP-B0=
%#Qgqa\eV>jSHsK1.B_;@5\m0mA1hG[J"@Q3,/0q`.G'f<"+AR-f-N-9!5f%XOgA9'5mFW3U+3#7\7Tk+p\ZSV(Qt%'&F/DY9!TJ*
%/(n`uMRn5Bj!3[Eeb#SJ0i\khUSSKUPe#+F1H39ORA(psnAeVA+V1!"2^J)OD95s3bILDKIiRBI^+$=OdB%\6#aD9C\T?*e(>BDe
%C.E^d>:^P"RCNqVd!j4<7H`1#C->T)m!hrf9>1p$<7."dV!-B&GYog9J5j)"JO8B:oJpfFZCLB*8!"L&5%=o4:2s$0aE[jWZ1n4l
%!?h,<&[<)/2rGtWP$IXuZOq]ZW66/<=N"J<Lqd"E!B'W1ZQ1;15c)tk"F-[$:Pqj5OWF&f#VCq7kU8W:0=ERuA3KJ^W*2NoKWhUJ
%@,Qmsg#'_dP5$`g9#_4C#5*?YU^=I`DZC`>6&j6-(iV"4Q2/'dJ"_*6&o\LS-!B"[:qW'"n^;j6L!&BH",AmRamH5f`#)"CcThNG
%BnSF-5j5.33:i'upW<29-SN^`%H'mc77=YKc(r"rZO+L_l$TF]!KCiN97JH]eT[A#nu:mldRK8KA3m4H@!kSLj_Q`As$-\U@dG6'
%kG,XuM#43UpYNQ)=3LVM#n:<H6Am,0"^o\.q)3FqV;.2&[^CE6l(XM=0g1*7$%92c*k*9=(RDBD-!P;n3'Js:DMS-j39P-fdN!Hb
%3YMY&N1!@`Oo_\VQHcJqn.j%3!.Eg7W<UZL=$,?'Q<iNJa_'\)<3XQo+)sZ7D(E0H$$G@pX`i/+35/)+-^,)Vh6*ZFLIjqF\rCsr
%U9kJAGu"ZnHBB&u#4-!X^:TJf\o;%GFP2^m7,P/u80GFAJI@-"hIQ,EV=eP%6)3cX9mgPtjZJ""UkskD6MsZCMtL%9XrN#qR7mFt
%pjLo_*)Y5gH4^i#fM1K7A;m=<2]9Y0+t!%"M-/EV5FqA-)I`AfVI#&_7*Q^=;'6\'qJ:"s`<K>qX08=NM1,=1:UA21eWNelEZB+X
%q#GhqpK:kCNQe5FC%T]NW:X,-j(C8qRp9M08PJ/#!R"KG)IjY1T5pG7J]%]).K%>(5J$1lcMiEEMJSn]m$6':-'MrNiM2/g[%s^4
%HSm-Y4-V1BI7`8rQC9-P7FBt_efj-'hqWQ[m4kZZMt%UmP"rJ[BVr%7Aq@Z^/j$]T#`;8"8t7<:#e>/*Q$[`Jb8')CM6MiX,1u`G
%ec.alfB9q"@=L-jU/tsr"8$I?nsG.iVrUcG=`dTnQNW1jpqpUS;;#cFMUO)mBNV7G^;rRpi!2.B)T:!l66mkLM!#`::K`63WQ`rc
%m2MNZ&ri(q^#H#\PYBIX4mVA1k$AeleUsmG'$PA=Iq"t-V$r7/Y=m5,P+!j6m!YfkT=^!c(m!L>G;:2L<\p*8^<^XkYsQ+9Li2cY
%':QL:d1TmG-.l6=+C`SFMlq.1NON-)1aJ<V,ic,^[I5HFXY-!,';E,qpLO56?Me;SX+f"@8hW9HUcD:6@Q(iNp_o[chW:h'r-DTr
%=e%dgB^Ndp]Pp\S3lr,rN-4`DVG-r2Ps^PDWELTK:bM3M\;:!f-DdagA4n6^8B!\E@Cg6-OZFsY!-Ba4[2bfU1LqZIc&ZmVn$N;1
%MXQ)1(RUMl9#bI9i-45(N&\CF8=n\QLbK>\Re)1,!D'6EoqJ\VNG<ss<_K+`$!V`*MHoL"",o04%mj1dW;"?EbM4HSV0AUk\F!/W
%$7ZFD2[!BpmYVOG*:[Yee#'tn_*4\8pX#a*Xc#(Z6l(Ub"pKK3!L!(a6?RS[,`eh:qmhsTRnb=g+$FY/U0BQV9=E\]HY$NtU/1`Z
%9:BUt6@XAU%"#H0re1D[373=[1W]/88ub4<;b\M]jE79s#/TVCJu\)oGs>UtEu#cZ4F6_c>p;0;1V0b_?Ka<..bDFd%bkTDkgZ3@
%4JXUZT'DLtO`_.[^2#p;Ts!&k9b3_T1`jbs9Ekhoh3UN3?VtU3VDqW5$02NJ-uqC;d^##HbG+%]7c10dO.PVbUST2TaB,,M\X6*N
%:eoM0b+N,(qdi-Bd[kUZUZQ!eGiNQFMUGoJn$eU*R-qK=?W9L12LN3j=f[0C/XP[M90?Xtq@E*&Ds*j?]6u!ldq._*Kl8j>>J^&.
%mr<PU>X[6lj*l]tWpa+Ve@[d9;g&iZa-t[h1^Hp]$m1-B63#TH$,2G[3\!I+Rfh9rmEi*2%1T0S>=PJD.W/`jq&n"#H@%eA60[3J
%d.REMZH]/0$u:621SD^OkIg'T0u==aG7an"C5QX]pP%CH.FKb//bf^Mf*-1:bd5nRGonA*O9,&gj:PZ3Cbir!e-W&c+8#Nr@]I<B
%>4jWBAsRrZ[5UdJ!H;5taH-do7G35Zf<+?hmkaZ$Hs$%XrjgDmhc=aF^_tH[-#c,I$[epsM,-I"QM^!6UIhK+4!n[h4G.#j(/5#0
%l=WF+ip-i7qMu@u^SSkU&q9aF:r\ium:cOM/mR2X^HDG=#EX0h1T&@bg=Ma+j"e8=)*7*qIBA@@fR-',=l\m7%d,c!^o&EOXj3Rc
%a/ZPp2k@USUO\@qjT!WA#%)e<M8$SQ.>\)^-SkPR.-[e)gtq?\p)Ems/rf/IcD0E>p=#$W:m4=*mot2<[!BGmK#a9PJ'bOUH*tQQ
%D#Z$gQF;)[mAJ:*Z@4GooQNBb4nYjnfd38ocYmtUD$rEjJ-lm!BV'S=EHg=8mV[!O,-*l&<)g(Dn\`dY/PM1(0n`_<"EO8*K'cYI
%-1Rk\`r(N<fE>70@h2l:C1C45&Df7[a=",F(LE;^ecbpV3;,Thl8M%KR$V+-*Sq0cAT`[Q5(JUci:lfQhGi%rRMN0Ha<_!ZB+/lI
%.9=`Bdm<E)784Zd]s>q_=n9:$+a4am/iDX:s%</@'2(QlUXFKDnD0b-TPMF.0Z%Q/k4C?H]feR`=&G_!pu%0ZU!!&*;`agBSA$Hg
%%/5P)N;?%2nVj>F<^ZP4m_]<S;a![63[p^k)J804QtWZ;Cc:(@HShSf8&p'i:d(EB2T8/b$2&V^4AVdOgA>u@qCjb#_9sEIM%T%K
%-=l"D&<*?IoBQ+_SY9B,:uuhTV/N,*A[]s@_OmhT7[In32K#U.J,cbj[3,-0\Q:AB(:3XSVn\juVAeW!Nu]a"Wd?rK?KVITI!F"Z
%CiiWPM-nM6FuT>b<;s*d9U=kZDPeEQhQ*OiIG2a"bFMo;fkH/tN^N)OnF.:C%dd=@BH/ZG[]@GD11+s(q(eQA+T7XR)a4IN]Icq]
%'EH&$hhUgcO,(:,H1[`]F*C7DGkI[Jrr.lLT2_Yd#G4kN=MF"_B2ACobQ!i^T$XD"Dh.AC?1<uPo(__Chk8s4iUZ&W(lti1d=5bh
%I#\(L39s8%>s/\(r`N]@df33Qgt?W)n0c54*VNFE%9+7:1V0$Y*]qgJ1\9f4jNikXg>/rU0DQWCIlPuCrs-]p)<cQX:ZO/r^=ia8
%Y&9<%h\(qG-MgBhjtJdABq(E<hXfjLiBZ3o%?4#l",Y\QNJ%lAcS8#We;Uj5q\1[^)5_NIO.W+N;)`k"cYp7Zq^+_pE:?f@+DlY6
%%dX-:KKrhD_XFqcqo1G\-N7UmerS:aVLCJFTD[Zc;"ArjW3)-hNik0c'_-XZgAWGN_*+f*Af-4ZrVj5i-:b)6e39Ll6$'g,'/5j=
%IR<i>)dX=Wcp!b6GU#l[k&oQ:WSYW#W/(>ZJdhO3U\:%)qhE?s,IVKBgoknCf$AurWRr`W5k),*YWI5/g/":8.XBKcl'm+';'](k
%rOZ1oJ$T.p2+#;(b:F`bL5LG'g0B6Zq#`\&I/o,]no-3%^=oF^;7AS(D-s`os6s8RO8aSl[o"eCk5FlF_%kcYC(/)H%M/?oGK6lo
%rjj2<g#Bmmn6acL,;6ga([Xj=jmDbji:mQ4altaA<B%*O]8Kr*Dkkq%bXJXC%W$<dV`V'Go7@tY5n)Cl&Dp8*#CVZ9kZ;S4.)N_L
%`\k7l'Vb:Bk?*64MgA>%-/i5'=-28e>(!)*%W47*RN);"03e$1>$[HsP-Md`p3:ioAGUPH%/&^Jm[2C+@s('B`rC:bqb)R.MWcU\
%H<PUc^"k"1?X*Y*UTTQ=!t_C0<"Jisf>#Bc)?":JrS)e5d08h,a*oC*PM+^:^"1;[CB"sHDWmf#Ilo"$;%OW(Jg2Q_AFND@a+,m0
%&s"<$]Hj-XNYWL[!B-+n2d7h(^#amj><=7GAV6d!GG_c6AC[g`TDtRr$s]p.m-ck=EV[(=O*mGr"V3,5rJP(e&&YViDMFJH`D0c0
%Yo]Ltff+ZJ4gY1kTpm3>&'p/P$jH[/6,Z:8jKF'\OpL(,Q<GF`$6G&$,TSIBOiA$AHkld)Q3H`M,mnmS$l#tn<"`mni]S87,5SD%
%iC$OhM]o6n7pn;Ub!?](V;>#\QBjnY,RXALc9hZXM:r@SnW\g'BEm`%9hs\F&c]k4X@Esao`hEPUu[Z">j>8O%4Q%o3DM)5H!4*k
%8<bSC6OOs(`J(B`,$F'qWEbbZ0G:1lT1f7?jp2_870sZ1dNaR\aO>+E;\I-D$V=`/;7IQjKT(j'Q)E/4Wct15ZQ]iW*Yrf#ic)q_
%=;l(&cnW,n4:I!6B>B1&.c,e$^j7+[^b(?ci9p0[ita/$p.15G)BDX'WpHGZZp]N-:o>bL1H_t3."ZnJHQrW$!3"m>5k:$9JIi8\
%aIifR)UJU%Nm-N!M1ZuKXDmX@gAHSH</iu$7sY;5i/qcJPsiGE\=e,06gb81.0)7#W<E&aFs$f0'SA'SK*C>"!cgoCCnj5JT_PWr
%4qRunFpO2B7g-k6$]rA-GV['9@\3gM#a5ddo*cl'3\8/m7<>hk!l3b=5sel=d"NmJap476:JuU6&cg%\8F&AL0LG#^oUbn-7!mbu
%%/*;^"^88YI#&7+&o8*#6^!&8)GAu*N(W;:n7;8E"$p'.2$7+3'T%5F"JnPk-7+8OYY7.ceM4iV$?42;'R&-@O+OEjS>P6]rPpSn
%W&Fl;I"jOZ;WKqcjUl`0GS\`U;\:.--j]YnS\gtP$jorrK;0#/WhBmCMNf$,,W0tR."#\\70-LF:0A8GJKTP#ajOo,'LhL^</ID)
%e2_UV!GDZr"c&ii5t-CHJomsTNY[K3-saXk;9pH6"2Z0u$np$gcKR\?V,eB"&bHNd7p7%CVP&+?LkYi.$ol%i(.NQR#?_T?bfF7o
%!5PuP:f'LgYIqZNW0Ipg0n`X>UH/[F;D_*V0PKqu<>TnGkWnbg]3C/d+mM?QJ"#u8eQ#."lH!n/s+t0Q!S8Sg*.sqCafA;8;2@J3
%K6kmq"WW.`'ie*7LsZ;(/3mM%_%t,(T=&nXbOgH/"a>."h7XAS,>&(`kpR\_Z/@61LA)>1&dh'6TO7!S0R2A-7Zh(D<$X5DTL!'B
%4N5&1=t7>OMi3ZQIq\^1@Te]ZZP7Njgfct/`]A`[M/gEuP[^o,hFg[<LjC&_8MBa)d'hY2NL6k:a&H,t49UB[^sO0^/-:b5YSA;o
%P1Wal`$,Ep@VT,-a'(dlM3-Xj3m49@"YDoJiVuXg+H[R68ctCuL*0_TU?'-/@R,8kmLFD"#e8e8=DluJ]b7u57>cYPJqmkP&#]Q*
%0B2"-6V15`C)'"S#Vn5qo?]]C74FK^WN$,`LJp2MnHBAl.Pp;Co+m8`!9tTL(o</qG!Q;1ppe9I6XKAcJ30nke`l/pc+s>^Vl7HD
%R77`qOJ;EE8B!]hVOS4'8I?1UooP+npgFuUWe:pZiFIp8\0Jgn*-_-m('YeH+[8Wlmf+]23/F2?38l%%$J74-k$to0*oR=-_2*=I
%^=Rrk9oE5"8rjTJ_e1,hTI:'aO=?&:6Z<PQVEn/fNJbC28!9YWkV/*=lC\<EMAIL><m1MnbK"PkJ#8?^`h$tGQV9oEk(EGI=JP\
%4t@0n>qi!E??IYqb2@dTC5.!A-3@%1)ckL/(=U5HTaZu^dq^AEcWZt_Ml$^<;g+ggEZBLi4rQRW+ePW.^O[A#5,Le3K8Lf*0Kc6_
%N.k,1D%"AA"e>F?1kI,ac^$OB)&2W1Lmh5+2Cq*GJ*/;Dl3p"r%8];9mp=7Y,-NZIg&+\QEs$t^WL&-1mD8$Ooat`a'4_/h*mN'C
%>5TDD%ncb$W.#E^U.CkCIe+\9_=KF\h+P+@N@379W2h;OS=gUja9a)QVJ<mQ"E_$tpb.VT&;C:g6ig"ZmXV,[<n0#U@46YkBU2Ra
%)eb_(GV)F`,RakA0gL=h5X9WEdRM[,*IuIuoML`u%%Fm:Kb**iAiK4OoObGE[@2UD.i6`:9B,gt?7U$-^F;"/dJN@cH*l;"'pZaL
%o^CHP5.>!CSY9O*hU"!0f>Dn-G$,mc7O>"\UOh)<0.-\pfDc&ha25"~>
%AI9_PrivateDataEnd
