%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6872031012924498196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 107829603250162478}
  - component: {fileID: 4635001547360866758}
  - component: {fileID: 7125187472850440456}
  - component: {fileID: 2593715932897166846}
  - component: {fileID: 8076200860070947583}
  - component: {fileID: 5891862201059643911}
  m_Layer: 0
  m_Name: Player
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &107829603250162478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -1.19, y: -2.98, z: 0}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &4635001547360866758
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a97c105638bdf8b4a8650670310a4cd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 3627241991512469009, guid: 918bc4781af08d9448c30d7b94bc149f, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 0.421875, y: 0.7657207}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!95 &7125187472850440456
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 910d8d598710ad840a28b4a856596a55, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2593715932897166846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 12588b894e28fe84981ae5449d45d2e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  moveAction:
    m_Name: Move
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: a7112663-5f13-4c7a-b29a-99677e91cf33
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings:
    - m_Name: 
      m_Id: 3cf16f46-185b-4954-afc1-f6b30b88acd5
      m_Path: 
      m_Interactions: 
      m_Processors: 
      m_Groups: 
      m_Action: Move
      m_Flags: 0
    m_Flags: 0
  jumpAction:
    m_Name: Jump
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 48410ec9-1396-46c8-a312-bc94d54177f5
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  attackAction:
    m_Name: Attack
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: d0fa6fcd-27d2-4709-aa42-750a85d7ada5
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  attack2Action:
    m_Name: Attack 2
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 96deedfc-7f4f-448f-939e-92cc629609dd
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  attack3Action:
    m_Name: Attack 3
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: c4dc5950-3c54-483d-b307-12a145a486db
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  spell1Action:
    m_Name: Spell 1
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 49339ab8-a279-41e1-b06f-dcb188c558d3
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  spell2Action:
    m_Name: Spell 2
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: e3dd763e-2972-407e-8d80-bc9d9bcbdd88
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  defendAction:
    m_Name: Defend
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: c36cae3e-a457-4223-83c2-5980171a3c33
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  dashAction:
    m_Name: Dash
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 4f7de6b8-a148-4722-97ac-d30e1916c5b0
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  spell3Action:
    m_Name: Spell 3
    m_Type: 0
    m_ExpectedControlType: 
    m_Id: 17363308-6507-442f-b459-d18a18118eda
    m_Processors: 
    m_Interactions: 
    m_SingletonActionBindings: []
    m_Flags: 0
  moveSpeed: 5
  jumpForce: 8
  groundCheck: {fileID: 0}
  groundCheckRadius: 0.2
  groundLayer:
    serializedVersion: 2
    m_Bits: 0
  attackCooldown: 0.5
  attack2Cooldown: 0.8
  attack3Cooldown: 1.2
  spell1Cooldown: 2
  spell2Cooldown: 3
  attackPoint: {fileID: 0}
  attackRange: 0.5
  attack2Range: 0.7
  attack3Range: 1
  spell1Range: 5
  spell2Range: 8
  enemyLayer:
    serializedVersion: 2
    m_Bits: 0
  maxHealth: 100
  currentHealth: 0
  strength: 10
  stamina: 100
  maxStamina: 100
  mana: 100
  maxMana: 100
  speed: 5
  armor: 5
  magicResist: 5
  healthRecoveryRate: 2
  healthRegenDelay: 5
  staminaRegenRate: 5
  staminaRegenDelay: 0.5
  manaRegenRate: 5
  manaRegenDelay: 0.5
  healthBar: {fileID: 0}
  staminaBar: {fileID: 0}
  manaBar: {fileID: 0}
  staminaToHealthRatio: 0.5
  minStaminaForRecovery: 20
  defendStaminaCost: 10
  minStaminaToDefend: 20
  dashForce: 20
  dashDuration: 0.2
  dashCooldown: 1
  dashStaminaCost: 30
  minStaminaToDash: 30
  spell1ManaCost: 20
  spell2ManaCost: 40
  minManaForSpell1: 20
  minManaForSpell2: 40
  fireSpellPrefab: {fileID: 0}
  spellSpawnPoint: {fileID: 0}
  spell3ManaCost: 50
  minManaForSpell3: 50
  spell3Cooldown: 10
  spell3Duration: 5
  hurtStunDuration: 0.5
  hurtKnockbackForce: 5
  hurtFlashColor: {r: 1, g: 0, b: 0, a: 1}
  hurtFlashDuration: 0.1
  hurtFlashCount: 3
--- !u!50 &8076200860070947583
Rigidbody2D:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  m_BodyType: 0
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 6200000, guid: b6c3b8f76ee5bc440a3a862bc135e7f0, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_Interpolate: 0
  m_SleepingMode: 0
  m_CollisionDetection: 1
  m_Constraints: 0
--- !u!61 &5891862201059643911
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6872031012924498196}
  m_Enabled: 1
  serializedVersion: 3
  m_Density: 1
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_ForceSendLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ForceReceiveLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_ContactCaptureLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_CallbackLayers:
    serializedVersion: 2
    m_Bits: 4294967295
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_CompositeOperation: 0
  m_CompositeOrder: 0
  m_Offset: {x: -0.0012983978, y: 0.000000029802322}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 0.453125, y: 0.7657207}
    newSize: {x: 0.421875, y: 0.7657207}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  m_Size: {x: 0.44014174, y: 0.7657208}
  m_EdgeRadius: 0
