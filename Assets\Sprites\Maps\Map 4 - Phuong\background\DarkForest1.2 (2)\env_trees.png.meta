fileFormatVersion: 2
guid: cb5a872bebc55154b9aa53edddb21d80
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5619840528850464871
    second: env_trees_0
  - first:
      213: -2163711329832910871
    second: env_trees_1
  - first:
      213: -6329860156603004030
    second: env_trees_2
  - first:
      213: 606189441930917708
    second: env_trees_3
  - first:
      213: -2966516423299009149
    second: env_trees_4
  - first:
      213: 8448969214746628593
    second: env_trees_5
  - first:
      213: 108726766031101380
    second: env_trees_6
  - first:
      213: -1022136944751439320
    second: env_trees_7
  - first:
      213: -5784459606456063862
    second: env_trees_8
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: env_trees_0
      rect:
        serializedVersion: 2
        x: 0
        y: 287
        width: 257
        height: 247
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 99f468f60d05202b0800000000000000
      internalID: -5619840528850464871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_1
      rect:
        serializedVersion: 2
        x: 341
        y: 271
        width: 172
        height: 271
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ef32d46204f8f1e0800000000000000
      internalID: -2163711329832910871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_2
      rect:
        serializedVersion: 2
        x: 0
        y: 247
        width: 149
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 28b87cf79b1d728a0800000000000000
      internalID: -6329860156603004030
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_3
      rect:
        serializedVersion: 2
        x: 150
        y: 255
        width: 81
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c4772e0f51e996800800000000000000
      internalID: 606189441930917708
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_4
      rect:
        serializedVersion: 2
        x: 341
        y: 235
        width: 122
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38576e0c701d4d6d0800000000000000
      internalID: -2966516423299009149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_5
      rect:
        serializedVersion: 2
        x: 0
        y: 63
        width: 127
        height: 177
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f11f2b7cb4c04570800000000000000
      internalID: 8448969214746628593
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_6
      rect:
        serializedVersion: 2
        x: 422
        y: 47
        width: 121
        height: 153
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cd5cdaea66428100800000000000000
      internalID: 108726766031101380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_7
      rect:
        serializedVersion: 2
        x: 0
        y: 11
        width: 137
        height: 43
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 82299404dd3a0d1f0800000000000000
      internalID: -1022136944751439320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_trees_8
      rect:
        serializedVersion: 2
        x: 425
        y: 15
        width: 85
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a882793a2a879bfa0800000000000000
      internalID: -5784459606456063862
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      env_trees_0: -5619840528850464871
      env_trees_1: -2163711329832910871
      env_trees_2: -6329860156603004030
      env_trees_3: 606189441930917708
      env_trees_4: -2966516423299009149
      env_trees_5: 8448969214746628593
      env_trees_6: 108726766031101380
      env_trees_7: -1022136944751439320
      env_trees_8: -5784459606456063862
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
