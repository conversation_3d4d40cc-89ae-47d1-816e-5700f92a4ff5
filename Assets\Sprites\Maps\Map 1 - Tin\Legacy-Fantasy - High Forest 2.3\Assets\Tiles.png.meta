fileFormatVersion: 2
guid: f95b8a0a320e59241b89f5766e15325a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 7223950226953391895
    second: Tiles_0
  - first:
      213: -8377130690987618698
    second: Tiles_1
  - first:
      213: -4684333735789482159
    second: Tiles_2
  - first:
      213: -5062486230716756072
    second: Tiles_3
  - first:
      213: 4099902767569298868
    second: Tiles_4
  - first:
      213: -6833445872158455971
    second: Tiles_5
  - first:
      213: -537749870668139058
    second: Tiles_6
  - first:
      213: 1561205615984820651
    second: Tiles_7
  - first:
      213: 857779327251732012
    second: Tiles_8
  - first:
      213: 3825752689756085014
    second: Tiles_9
  - first:
      213: 7477827516038983822
    second: Tiles_10
  - first:
      213: 3930821917377472818
    second: Tiles_11
  - first:
      213: -47299315171432025
    second: Tiles_12
  - first:
      213: 9045646400296435942
    second: Tiles_13
  - first:
      213: -6765242926017629268
    second: Tiles_14
  - first:
      213: -8637159173488095169
    second: Tiles_15
  - first:
      213: 8933607801220796041
    second: Tiles_16
  - first:
      213: -8955812619259062215
    second: Tiles_17
  - first:
      213: 4680653795402065389
    second: Tiles_18
  - first:
      213: 4753258692078137619
    second: Tiles_19
  - first:
      213: -7956832237065629149
    second: Tiles_20
  - first:
      213: -6100061461012067082
    second: Tiles_21
  - first:
      213: -7369953315678303813
    second: Tiles_22
  - first:
      213: -7326330765846486680
    second: Tiles_23
  - first:
      213: 5264979163374914166
    second: Tiles_24
  - first:
      213: -8644279775636323431
    second: Tiles_25
  - first:
      213: 3841374895227053369
    second: Tiles_26
  - first:
      213: 2398694386035660818
    second: Tiles_27
  - first:
      213: 8976018127306389262
    second: Tiles_28
  - first:
      213: 6930148502292774571
    second: Tiles_29
  - first:
      213: 4243927867023665446
    second: Tiles_30
  - first:
      213: 4080561482957604121
    second: Tiles_31
  - first:
      213: 887565136908663245
    second: Tiles_32
  - first:
      213: 5891617607487913053
    second: Tiles_33
  - first:
      213: 3342371246333263808
    second: Tiles_34
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Tiles_0
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 78
        height: 80
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 71fc8a01570a04460800000000000000
      internalID: 7223950226953391895
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_1
      rect:
        serializedVersion: 2
        x: 273
        y: 351
        width: 126
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67e09ae35047ebb80800000000000000
      internalID: -8377130690987618698
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_2
      rect:
        serializedVersion: 2
        x: 273
        y: 303
        width: 126
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 15fa3e43947edfeb0800000000000000
      internalID: -4684333735789482159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_3
      rect:
        serializedVersion: 2
        x: 273
        y: 255
        width: 126
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89f42d9689f6eb9b0800000000000000
      internalID: -5062486230716756072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_4
      rect:
        serializedVersion: 2
        x: 273
        y: 207
        width: 126
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b5eb9a48d7c5e830800000000000000
      internalID: 4099902767569298868
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_5
      rect:
        serializedVersion: 2
        x: 240
        y: 94
        width: 16
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d57c2263f29ba21a0800000000000000
      internalID: -6833445872158455971
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_6
      rect:
        serializedVersion: 2
        x: 241
        y: 144
        width: 13
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec530dc77578988f0800000000000000
      internalID: -537749870668139058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_7
      rect:
        serializedVersion: 2
        x: 256
        y: 95
        width: 32
        height: 59
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba1ce127c348aa510800000000000000
      internalID: 1561205615984820651
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_8
      rect:
        serializedVersion: 2
        x: 273
        y: 127
        width: 126
        height: 77
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c265380e0c177eb00800000000000000
      internalID: 857779327251732012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_9
      rect:
        serializedVersion: 2
        x: 288
        y: 95
        width: 32
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 61b18673dcdc71530800000000000000
      internalID: 3825752689756085014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_10
      rect:
        serializedVersion: 2
        x: 320
        y: 63
        width: 80
        height: 63
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e803521b18496c760800000000000000
      internalID: 7477827516038983822
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_11
      rect:
        serializedVersion: 2
        x: 360
        y: 117
        width: 7
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 239aa9c15b51d8630800000000000000
      internalID: 3930821917377472818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_12
      rect:
        serializedVersion: 2
        x: 245
        y: 80
        width: 6
        height: 15
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a966110685f75ff0800000000000000
      internalID: -47299315171432025
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_13
      rect:
        serializedVersion: 2
        x: 256
        y: 79
        width: 17
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e0c575a977988d70800000000000000
      internalID: 9045646400296435942
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_14
      rect:
        serializedVersion: 2
        x: 273
        y: 47
        width: 30
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca3be7f07670d12a0800000000000000
      internalID: -6765242926017629268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_15
      rect:
        serializedVersion: 2
        x: 305
        y: 47
        width: 30
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f305cbb4e75a22880800000000000000
      internalID: -8637159173488095169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_16
      rect:
        serializedVersion: 2
        x: 241
        y: 69
        width: 14
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98e8c6286fc8afb70800000000000000
      internalID: 8933607801220796041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_17
      rect:
        serializedVersion: 2
        x: 256
        y: 47
        width: 17
        height: 28
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9385a1a6fdf86b380800000000000000
      internalID: -8955812619259062215
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_18
      rect:
        serializedVersion: 2
        x: 244
        y: 51
        width: 8
        height: 11
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de52687e3d505f040800000000000000
      internalID: 4680653795402065389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_19
      rect:
        serializedVersion: 2
        x: 241
        y: 31
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 311fadab897f6f140800000000000000
      internalID: 4753258692078137619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_20
      rect:
        serializedVersion: 2
        x: 257
        y: 31
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 322935ccc37a39190800000000000000
      internalID: -7956832237065629149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_21
      rect:
        serializedVersion: 2
        x: 273
        y: 31
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6fc4354bb6a385ba0800000000000000
      internalID: -6100061461012067082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_22
      rect:
        serializedVersion: 2
        x: 289
        y: 31
        width: 14
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb948cfc18aa8b990800000000000000
      internalID: -7369953315678303813
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_23
      rect:
        serializedVersion: 2
        x: 305
        y: 31
        width: 14
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86164ba2cf4a35a90800000000000000
      internalID: -7326330765846486680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_24
      rect:
        serializedVersion: 2
        x: 319
        y: 15
        width: 34
        height: 27
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67a8bce67a6f01940800000000000000
      internalID: 5264979163374914166
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_25
      rect:
        serializedVersion: 2
        x: 242
        y: 15
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9938a1ae759590880800000000000000
      internalID: -8644279775636323431
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_26
      rect:
        serializedVersion: 2
        x: 258
        y: 15
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 935ac1f5d1e4f4530800000000000000
      internalID: 3841374895227053369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_27
      rect:
        serializedVersion: 2
        x: 274
        y: 15
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2181164d5dfd94120800000000000000
      internalID: 2398694386035660818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_28
      rect:
        serializedVersion: 2
        x: 290
        y: 15
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0770970ee8319c70800000000000000
      internalID: 8976018127306389262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_29
      rect:
        serializedVersion: 2
        x: 306
        y: 15
        width: 12
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: baebdd76855dc2060800000000000000
      internalID: 6930148502292774571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_30
      rect:
        serializedVersion: 2
        x: 243
        y: 0
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 629a356e8e575ea30800000000000000
      internalID: 4243927867023665446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_31
      rect:
        serializedVersion: 2
        x: 259
        y: 0
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 91544db8c0111a830800000000000000
      internalID: 4080561482957604121
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_32
      rect:
        serializedVersion: 2
        x: 275
        y: 0
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc9367c1ac3415c00800000000000000
      internalID: 887565136908663245
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_33
      rect:
        serializedVersion: 2
        x: 291
        y: 0
        width: 9
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d5cf0c6bffa33c150800000000000000
      internalID: 5891617607487913053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_34
      rect:
        serializedVersion: 2
        x: 307
        y: 0
        width: 9
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0cf7c2d90fc726e20800000000000000
      internalID: 3342371246333263808
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_35
      rect:
        serializedVersion: 2
        x: 79
        y: 340
        width: 65
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 1, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fddd1b9cede45674f94272693061a6bd
      internalID: 1041086525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_36
      rect:
        serializedVersion: 2
        x: 1
        y: 240
        width: 78
        height: 69
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8b5ed14afdc1567419802293c47ff00a
      internalID: -1767860466
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_37
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 64
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4091e5e56f1aa0244aae5b270ee66dec
      internalID: -677398138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_38
      rect:
        serializedVersion: 2
        x: 48
        y: 64
        width: 48
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 67ef24663d354cf429ff5a80b8a05c68
      internalID: -1068150124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_39
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 48
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b8f07da9e03417946868054968442c12
      internalID: -1142201847
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_40
      rect:
        serializedVersion: 2
        x: 178
        y: 142
        width: 46
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4ec9c5d418092844181365079a80682b
      internalID: -659888007
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_41
      rect:
        serializedVersion: 2
        x: 160
        y: 128
        width: 17
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 14fa5253d5dd9cc4186e7697766c3827
      internalID: -643798662
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_42
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 96
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 58c2baf0fdd414b40af42e988f72c789
      internalID: 580023190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_43
      rect:
        serializedVersion: 2
        x: 66
        y: 160
        width: 93
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1cc810dcf31bc7044b5db238334e4dd0
      internalID: 810608213
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_44
      rect:
        serializedVersion: 2
        x: 160
        y: 176
        width: 49
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf67deaf15aa2b6468f5f2c27be13378
      internalID: -2090867097
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_45
      rect:
        serializedVersion: 2
        x: 80
        y: 241
        width: 80
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0577f7265b7d9d54ba64e15cdcf16346
      internalID: 904673295
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_46
      rect:
        serializedVersion: 2
        x: 64
        y: 207
        width: 32
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f386e53298cbedc46be543515d3049a6
      internalID: -998207839
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_47
      rect:
        serializedVersion: 2
        x: 81
        y: 293
        width: 46
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 79be180d778399f469e01a3cbe208824
      internalID: 889210905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_48
      rect:
        serializedVersion: 2
        x: 129
        y: 303
        width: 31
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e0d5708fd32f0ee429d49026c3db214c
      internalID: 61554629
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_49
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 32
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f012a8eadc2bd9c458420270049a82f6
      internalID: -946154793
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_50
      rect:
        serializedVersion: 2
        x: 144
        y: 192
        width: 16
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2b18fef0654f6f84e88481da990de2d8
      internalID: 1261473571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_51
      rect:
        serializedVersion: 2
        x: 96
        y: 192
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a94e8a5d40f95074685674978a5c30f6
      internalID: 1238528208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_52
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 79
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 95e340b7bd68e614c849f3a68c39aa6c
      internalID: 1191723146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_53
      rect:
        serializedVersion: 2
        x: 79
        y: 32
        width: 81
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ce6ffe92c420d944ea34de7792ddd137
      internalID: -1358454033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Tiles_54
      rect:
        serializedVersion: 2
        x: 160
        y: 32
        width: 80
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 299b58bb72cb72f49b53453cd88f5578
      internalID: -1284632291
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 6303094e17bd0cf4e9939d75a2a9b046
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Tiles_0: 7223950226953391895
      Tiles_1: -8377130690987618698
      Tiles_10: 7477827516038983822
      Tiles_11: 3930821917377472818
      Tiles_12: -47299315171432025
      Tiles_13: 9045646400296435942
      Tiles_14: -6765242926017629268
      Tiles_15: -8637159173488095169
      Tiles_16: 8933607801220796041
      Tiles_17: -8955812619259062215
      Tiles_18: 4680653795402065389
      Tiles_19: 4753258692078137619
      Tiles_2: -4684333735789482159
      Tiles_20: -7956832237065629149
      Tiles_21: -6100061461012067082
      Tiles_22: -7369953315678303813
      Tiles_23: -7326330765846486680
      Tiles_24: 5264979163374914166
      Tiles_25: -8644279775636323431
      Tiles_26: 3841374895227053369
      Tiles_27: 2398694386035660818
      Tiles_28: 8976018127306389262
      Tiles_29: 6930148502292774571
      Tiles_3: -5062486230716756072
      Tiles_30: 4243927867023665446
      Tiles_31: 4080561482957604121
      Tiles_32: 887565136908663245
      Tiles_33: 5891617607487913053
      Tiles_34: 3342371246333263808
      Tiles_35: 1041086525
      Tiles_36: -1767860466
      Tiles_37: -677398138
      Tiles_38: -1068150124
      Tiles_39: -1142201847
      Tiles_4: 4099902767569298868
      Tiles_40: -659888007
      Tiles_41: -643798662
      Tiles_42: 580023190
      Tiles_43: 810608213
      Tiles_44: -2090867097
      Tiles_45: 904673295
      Tiles_46: -998207839
      Tiles_47: 889210905
      Tiles_48: 61554629
      Tiles_49: -946154793
      Tiles_5: -6833445872158455971
      Tiles_50: 1261473571
      Tiles_51: 1238528208
      Tiles_52: 1191723146
      Tiles_53: -1358454033
      Tiles_54: -1284632291
      Tiles_6: -537749870668139058
      Tiles_7: 1561205615984820651
      Tiles_8: 857779327251732012
      Tiles_9: 3825752689756085014
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
