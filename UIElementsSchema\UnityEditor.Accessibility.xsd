<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:editor="UnityEditor.UIElements" xmlns:engine="UnityEngine.UIElements" xmlns="UnityEditor.Accessibility" elementFormDefault="qualified" targetNamespace="UnityEditor.Accessibility" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="UnityEngine.UIElements.xsd" namespace="UnityEngine.UIElements" />
  <xs:simpleType name="AccessibilityHierarchyTreeViewItem_role_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Button" />
      <xs:enumeration value="Image" />
      <xs:enumeration value="StaticText" />
      <xs:enumeration value="SearchField" />
      <xs:enumeration value="KeyboardKey" />
      <xs:enumeration value="Header" />
      <xs:enumeration value="TabBar" />
      <xs:enumeration value="Slider" />
      <xs:enumeration value="Toggle" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="AccessibilityHierarchyTreeViewItemType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="false" name="is-root" type="xs:boolean" use="optional" />
        <xs:attribute default="0" name="id" type="xs:int" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="None" name="role" type="AccessibilityHierarchyTreeViewItem_role_Type" use="optional" />
        <xs:attribute default="false" name="is-active" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AccessibilityHierarchyTreeViewItem" substitutionGroup="engine:VisualElement" type="AccessibilityHierarchyTreeViewItemType" />
  <xs:complexType name="AccessibilityHierarchyTreeViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AccessibilityHierarchyTreeView" substitutionGroup="engine:VisualElement" type="AccessibilityHierarchyTreeViewType" />
  <xs:complexType name="TreeViewSearchBarType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TreeViewSearchBar" substitutionGroup="engine:VisualElement" type="TreeViewSearchBarType" />
  <xs:complexType name="SearchableLabelType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SearchableLabel" substitutionGroup="engine:VisualElement" type="SearchableLabelType" />
</xs:schema>