fileFormatVersion: 2
guid: bac9e425caa1c654fbe0b09507ca6904
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8910899737472627771
    second: Attack_3_0
  - first:
      213: -5464336490339650859
    second: Attack_3_1
  - first:
      213: -1132038362817505424
    second: Attack_3_2
  - first:
      213: -6574224857763752514
    second: Attack_3_3
  - first:
      213: -6827800710642968084
    second: Attack_3_4
  - first:
      213: 5388459929022106953
    second: Attack_3_5
  - first:
      213: -241614906349035404
    second: Attack_3_6
  - first:
      213: -7294828852963927212
    second: Attack_3_7
  - first:
      213: -6599260265685524675
    second: Attack_3_8
  - first:
      213: -3510743571422996371
    second: Attack_3_9
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Attack_3_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5cb82d696ef165480800000000000000
      internalID: -8910899737472627771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_1
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d2b910d9e6ca24b0800000000000000
      internalID: -5464336490339650859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_2
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 077d5285b113a40f0800000000000000
      internalID: -1132038362817505424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_3
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb5d277d259a3c4a0800000000000000
      internalID: -6574224857763752514
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_4
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce957fccd67ce31a0800000000000000
      internalID: -6827800710642968084
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_5
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 94d0ac391c7a7ca40800000000000000
      internalID: 5388459929022106953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_6
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47060a6d58c95acf0800000000000000
      internalID: -241614906349035404
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_7
      rect:
        serializedVersion: 2
        x: 896
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45b90d1decf83ca90800000000000000
      internalID: -7294828852963927212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_8
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3fd673b0c7ba64a0800000000000000
      internalID: -6599260265685524675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_3_9
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6898df8255574fc0800000000000000
      internalID: -3510743571422996371
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 3227e386390a97f418b63e94b0741910
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":128.0,"y":128.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      Attack_3_0: -8910899737472627771
      Attack_3_1: -5464336490339650859
      Attack_3_2: -1132038362817505424
      Attack_3_3: -6574224857763752514
      Attack_3_4: -6827800710642968084
      Attack_3_5: 5388459929022106953
      Attack_3_6: -241614906349035404
      Attack_3_7: -7294828852963927212
      Attack_3_8: -6599260265685524675
      Attack_3_9: -3510743571422996371
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
