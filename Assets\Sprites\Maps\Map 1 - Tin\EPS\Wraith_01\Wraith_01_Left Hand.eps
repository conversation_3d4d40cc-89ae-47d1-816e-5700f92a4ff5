%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_01_Left Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 9 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:54:48+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:54:48+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:54:48+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FWK/mpN5sh/L7Wp&#xA;fKSu/mBYR9SWJectDIolMa0NXEXPjTetKYq+DtW82edLyaSPV9Y1G4mRisiXdzO7Ky7EESMSCKYE&#xA;qmi+RvNOuQ+vpdmLmNq0b1oUJI6ijupr7dckIE8nC1HaODCanKj7j+pMB+Wn5kWh5x6TcxltiYnQ&#xA;mnXfg9cPhy7modsaU/xj7XXGqfm15ejE019rmlxFqCb17uFCzDpzDBakDxwGJHNysOrxZDUJRkfI&#xA;vef+cWvzS86eY9Y1Ly7r13LqlrbWZvLe9uKvLGyypGY2l6sHElRyJPw7YHIfR2KHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXzX/zlhD+Vywo0xMXnxlR4BZopLw1&#xA;A/03dRx4g8D9vp1WuKXj35PXN2uqXtupb6qYRK3gJFcBPkSC33ZbiO7ou3YR4In+K/se+6HeXdxA&#xA;ROpISnCY/tD+OZILxOeAB2YP+dPk3zJ5itNNk0dPrK2bSetZhlRiZAvGQcioPHiR177d8rzQJ5O5&#xA;7A12HBKQybcVb/oeT/8AKpvzD/6s0n/IyH/mvKPCl3PTfy1pf54+39Tv+VTfmH/1ZpP+RkP/ADXj&#xA;4Uu5f5a0v88fb+pUXyn+aunfFBZ6nCVHEG3aQkBew9Jj4bYPDl3Nke1dMf8AKRREfnj86tD+1q+v&#xA;Wip+xcyXRQf7Gaqjr4ZExIcmGpxT+mUT7iGQaP8A85R/m9p5X19Qt9TRT9i8to6EeBaAQP8A8Ngb&#xA;3oXl/wD5zL3VPMXl3b9u40+b9UMw/wCZuFaes+Uvz6/K7zOyQ2espZ3j7Czvx9WkqegDP+7YnwRz&#xA;ih6CCCKjpirsVdirsVdirsVdirsVUnu7RJRC80aytQLGWAY16UBNd8VVcVdirsVdirsVdirsVdir&#xA;sVdir4J/Piz1K1/N7zH+kw5aa69aFiT8Vs6gw8WI6COi+1KdsCWb+RG8sppkUuhoDDyVrlHNZeY6&#xA;rLXv+HhmVCq2eJ7S8YzIy8+nd8HqlrdQXMQkhYFe47j2Iy552cDE0VbCxUpbmCIj1ZBHXoWNB95w&#xA;JESeStb3QpyidXXv9lx+NRiyjKUUdbTrO3BrJJvExgofw2yJFdXKxZBM0YCXu2Rsuh2zxc0LW7Uq&#xA;VcggfP8A28iJuXPs+BFi4+95558Hk3TNIuNU8wabBewxME2gjlkZ3NAEZqUr48hhnQFlp0A1E8ox&#xA;4pkH3mmDaB5I/KHz9byvor3GjX8IBmtSxBWuwbixnUpX+Uj3plQjGXJ32XWazSEDJKMgepG3zH6Q&#xA;k3mT/nHLznp0TXOkSRazajcCIiOansjEqf8AgvoyBx9zscHbEZD1jh8x6h9m/wBiV+S/zg/Mr8vL&#xA;0WMVxK1nAQs2iaiHaJQP2VVqPF/sCPeuV8nbQnGYuJsPrX8qvzk8sfmJYMbGtnrFuoa90mVgZEB2&#xA;5xsKCSOu3ICo7gVGLJnuKuxV2KuxV2KvmP8A5yZ/O7UrbUZfI/lq6a1EKga3fQmkjM4DC2jcbqAp&#xA;HMg1J+HsaqXzhBpuoXQ5xRNJXetRU/ecaaJ6nHE0Smdi3nbRqS6dNf2JXcPaSSx071rEdsaRHVYj&#xA;ykHpf5af85MeeND1m1tvM98+saA7iO7Fwoa5hUmhlSUAOxTqVcmvTY74HIfZVvPDcQR3EDrLBMqy&#xA;RSIaqyMKqwI6gg4UMd/MTz7o/kXyvca/qgaSONlit7aOgkmmk+zGtdhsCxPYA4q+Y9b/AOcvfzCu&#xA;5m/RVhp+mW9fgVkkuJR/rOzKh+iMYEqeif8AOXH5jWt/HJqtvZajY8h68AiMEnHv6ciNRW/1lbFX&#xA;1N/jry3/AIK/xn9Z/wBwP1T6961Pi9PjXjxr/eV+Hj/NthQn+KuxV2KvPvze/JvQvzF0tBM31LXL&#xA;RSNP1NRUgHf0pV/bjJ3p1B3HcFV8ceYPLXnn8tPMZtdQieyulqYpkq9tcxg/aRqcZEPgdx3AOIJD&#xA;VnwQyx4ZCw9C8l/mDbazxi5fU9VUfFCCQHA6mM9/9U7/ADzIhkt5PtDsyWHf6ofjmz2z8x3CELcq&#xA;JE7uNmH8Dlok6SemB5MgR4biEOtHikFRXcEH2yThkEFK77QlqZrFjDMN+ANAfke2Ahvhn6S3Chp3&#xA;mbUbCYxzs2x4sejinj2b6cHvcgQI3gaZZBrwuoVMqR3MLdG6H+zHh7kHWy+nJESeD/8AOQvm+2mv&#xA;YPLOmswggC3OoBqV9VhWOOvgqHl9I8MozSPJ6bsHRQAOcXvsL+1575D8xz+VfOGn6nIpWKN1W8ic&#xA;GjW8oAeq9/hbkvuAcqiaLutZgGbEY0Jd3vH4p9jRazbSRqqs1qKfCYwrpv8ARmXwvDR1sSK3h7gC&#xA;PuYn518iaJ5ls2XUo0ua19G+ios0TN3BG67/ALJ+E4mIlsWOHVZtNLjhLiiefcfeOj5uguda/LX8&#xA;xEns5w19otwrB0NFmiZQxRhvtJE/Fh2rmJKNGnvdHqRnxRyDbifoNbzLPBHMgIWVVdQetGFRXA5C&#xA;/FXYq7FXYq/Nq/vrrW9eutQuSXudRuZLiYk1JeZy7bn3OBhmlwxJeheWvL11IqcYq/Sv9csAeV1O&#xA;oA6s8tfKN/6PL6sxFP2aN+quGnVy1cb5vJ/zU0Qadq1vN6Zja6RhIpFKtGQK0+TDISD1HYuo48ZH&#xA;839L66/5x51afVPye8uzTsXlgiltSTX7NtO8UY38I0XA7l5J/wA5m6tObryzpCkiBUubuQdmdika&#xA;f8CA334pDyfyL5Wt7+2SeSATPISd15UANMkA8/2lrJQmQDQCr+ZnlBNJsLS+jt/QDS+ixC8QeSlh&#xA;27cDjIL2PrTknKJN7Wm/+Lr3/oWb9Cc24f4i+qU/Z+rej9c4f8j/AIsg9A+3MKHYq7FXYqk/mvyh&#xA;5d816PLpGvWaXlnJuA2zo1NnjcfEjDxGKvhr81vIR/LvzvJpVlqSXkaBbmznR1+sRKxPFJ1X7Ei0&#xA;+kUbatAFlESFHk9K8t6lJqmhWV9IKSzxAyACg5j4WoPcjMuJsPCavCMeWURyBegaTE1tpsazfARV&#xA;mrtQEk71y0OmynilsufWNNQ0Nwp+VW/UDjaBhn3JHrtzY3E0b255OARIwBAPh1yJcvBGURupaTqb&#xA;2U1GqYHPxr4f5QxBZZsXEPN4L+Y5C/mDqzzMLiM3Ik+FtmjZVZVqK/sEDMTJ9Re37K30sK29P2su&#xA;/Pfyxbwy6P5rsVCWetW6pLGKArNCoCnj4NFxG38vvkso3tx+w8h8LgPOJP3ll/5OfmDDremRaFdk&#xA;jVtPhorEfDLBHRVav8yggMD16+NLsU7FPPdu9mHDM5Y/RI/Ipx+avm3VPLHlj65psYNzcTLbidhy&#xA;WLkGbmV7n4aCu1fuyWWRA2cXsbRQ1GbhmdgLrvfNsWoLNrCahqyPqCPOs19GZPTedeXKRfVo/EuN&#xA;uXE08MwyX0OEBGIjEUA+1fyx/wCchvJXne9j0dIpNH1hwfQsrgqY5eI+zDKtAxA/ZKqfCuKXqmKu&#xA;xV2KuxV+d3njy3deTvPWp6NLGyjT7pxb8tuduW5Qv/s4yDgY5IcUSO96N5K1m1lhjkjcMNq+IPgc&#xA;sBeM12CUSQQ9S0/XYkgpyHTJ26LJgJL58/N7zbB5i8zhbNhJZ2CehFIu4dyayMvtWij5ZVI2912H&#xA;ojgw+r6pb/qfZn5P+Wbjyz+Wnl/RrpSl1BbercxmtUluHa4kQ17q0pGB3Dxz/nMry7cSWfl7zDEh&#xA;aC3eaxu3H7JlCyQ/R8Em/wAsUh5f+VnnSy06yFpdTJC0DHgXYKCrHl1PgTkol5jtnQSnPiiLtb+c&#xA;vn601+Kx0yymWeKBzPPIhDJz4lEAI2JALVxkWfYPZ0sJlOQonYMi/wCVdah/0Kz+lvSP1j9Lfpvh&#xA;Q8/qnD6l0p9n/dv+rvkHpH2HhQ7FXYq4kAVPTFXzJ+df/OTkiyXHl3yFOABWO819NyT+0tp+r1f+&#xA;B7NgS8Q8teSNZ8xzm+u3eGzkYvLeS1aSUk1Ypy3Yn+Y7fPJxgS63W9pww7D1T7v1vetB03T9I0uK&#xA;dkpDAojs4f8AV2B377fxzLAoPD6jLLLMjqebFPO/5j2mlnhOxuLwisVjGaBQehc78f15XPJTs+z+&#xA;ypZdxtHveV6n+YPmvU5SsVw1qjbJBagof+CHxk/TlByEvTYey8GMcuI+f4pB/XfOkP7/ANfUU7+o&#xA;WnA8dycFycg6bCduGPyCOt/zJ82Q2stu1ysxdSqTSIDIle6sKb/61cl4hcafZOAyBqvuRP5eeR7j&#xA;zRrEc17zGlrLW6l3Lyt1KKeu/wC03bHHDiLX2p2iNNjqP11t5fjo9T/P3yzPf+U9M1PT1Ji0JpVv&#xA;LZf2YbgoFlVRtxQxhW+fgMszR6uq9ndVCjA/UXl/5beddG8pzXl9c2UlzqMqCK2kUjisZNXBBK0q&#xA;VG+/0d68cxF2naugyakRjGQEBuWbf8r60G/R7PWdDabT5dpEBjlBHXeOSinf3y3xgeYdP/ocyw9W&#xA;PJUh7x9oTvRfI/5Pea7N7vSbRTSglWOaeOSInoGiL0X2+Gh7ZIQhLk4mo7Q1+mlw5D9go/GnkfnX&#xA;Q5PJfnV7fTLmQGzeK70+5JHqodpENRtyRx19q5jzjRp6vszWHUYRMij1ff2g6l+lNC07Uqcfr1rD&#xA;c8R29aNXp/w2Rc5HYq7FXYq8r/O78jrH8w7SK9spUsfMtmnp2904PpTRVJ9GbiC1ASSrAGm+xrir&#xA;5j1X8g/zh0a4YfoC4nAJCT2LpcBh0qPSYuP9kAcCkA80F/yqz85JB6Z8u6wVf4SrRTBTXbeu1MWA&#xA;xQ7g9h/JD/nGjVbTWLbzJ53gSBLNhLY6MWWR2lXdJLgqWUKp+IJWpP2qdCWx9OYoSrzV5Y0jzR5f&#xA;vdB1aL1bC+j9OUDZlIPJHQ70ZGAZT4jFXydrf/OJP5j2upTRaVNZ6jp4JNvctL6Dle3ONgeLfJiP&#xA;fAlOvJf/ADiF5hfUre483X9rDpiMrz2Vm8ks8gG5jLlY0SvQspb2xW31D+idM/RX6I+rR/ov0Pqn&#xA;1PiPS9Dh6fpcenHh8NPDChF4q7FXYq8K/wCcrvzDvtA8sWflzTZTDda/6n1uZCQy2kXEMgI6eqz0&#xA;/wBUMO+Kvnv8u/I8OpKNX1JOdmrUtoD0kZTuzf5IO1O5/GzHC9y6TtXtE4/3cPq6nu/a9XVVVQqg&#xA;BQKADYADMh5glj/5ieexpGnQWtuQb9ouFumxCDo0rD59PHIZJ0HP7L7O8WZkfpvf9TyTRtEvtdvJ&#xA;JpZG9Llyubp/iJY7kCvVjmPGNvYEiAoPRNH0K0tAtvYwBWP2nO7H3ZjlwjTRKRPNk9rpVvCAXHqS&#xA;dyen0DJ01ksH/NbQk+q22qW0Cr6bNHduigEh6cGYjwII+nKsserfgl0X/lh+ZOnaFaLYagfRERb0&#xA;puJZGR25FW4gsDy70w48gGxdJ2v2TPNLjhvbMPOv5xeU38tX9jp8rXl9fW8tqIlUhFEyFGZnIAoA&#xA;3Qb/ACyc8op1nZ3YuojmjOXpjE3+x5T5A8qWnmDU5E1Bp49Ot05TSW/EOWYgKgLhlFRU9D0yiEbe&#xA;zyT4Xqy/kh5A1ayaLSry8tNQVSUaZkkqfF04gMP9UjJHG1xy28rsLvX/AMu/OrJMONzYyeneQKTw&#xA;mhahIBPVXUhlJG2xyMZGJatbpIanEYH4eRXfmPq8fmPz1d3GnN68Mxhgs+O/KkaqKV8XrjklZaey&#xA;NOcOnjGX1b38339o+nrp2kWOnqQVs7eK3UjYUiQJt92Rdii8VdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVfLf/ADmLc+W7i90RINQjfzBYiWO705Ksy28wV0d2HwoQy7K25DV6YEh5f+V/mnUBfQ6D&#xA;KPWs5BI0LU+KIqC53/lPv3y7FLo6HtjRw4TlG0tvj0enXVzDa20tzO3GGFGkkbwVRU5cXm4QMiIj&#xA;mXg9zcX3mbzE0rmkt3JsOojjHQfJVGYv1F7rBhjhxiI6PQtPgsbKJNPtmUNCoJjqOdD+0w67nLht&#xA;swJJ3ZZp1mLeAEj96+7nw9smGslF4UKc8ENxC8E6LJDIpWSNhUEHqCMVBYDq35SW0sxk0y8NujGv&#xA;oSqXA/1XBBp8wfnlRxdzfHP3oWy/KCcyA3uoKIx1WFCWP+yalPuOAYknP3Bn2j6Np+kWS2djF6cQ&#xA;3YndmbuzHuctApolIk7plb3EtvOk8LcZIzVThQGM/wDOQOgw3mnaV5utloXAtLwDwNXjJp/KwZSf&#xA;lmPMOXjkxX/nH/yq3mP81dFgaPna6fJ+krvaoCWtHTkPBpeC/Tlba+8cKHYq7FXYq7FXYq7FXYq7&#xA;FXYq7FXYq7FXYq7FWM/mZ5nn8reQdc162AN1Y2zNbchUCZyI4iR3AdwSMVfCXl3RdQ836/M95dO7&#xA;uWub+7kYvK5ZtzVurMT1OGEbLh67WDBDiqyeT13Q/LGi6LHxsLcJIRxedvilYe7H9Q2zJjEB5LUa&#xA;zJmPrPw6JR+Z1+1r5UmRTRruRIAR4El2+8IRkch2crsfHxZwf5ot5h5f1SHSPUvXh9aWQiGJa8QF&#xA;FGkNaHf7NMoiaetnG9noHl/QUPmObVvVLrcopWOlOKgKd9991GXRjvbjyltTN8sanYq7FXYq7FXY&#xA;q7FVfzRarqX5T69bMOTWiGdPb0Ss1R/wLZVkDdiKS/8AOHF1GvnnW7UqDJLphlV6bgRXESkV9/VG&#xA;UOUX1xhQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqlXmvy7aeZPLWp6DdkrBqVvJbs4FShdaK4&#xA;HirUYYq+CdV0vzX+W/nCaxvYjBf2pKkMCYbiFjs6HbnG9Kg+PgRjE0WnU6eOaHDJ6FoP5ieXtURV&#xA;lmFjdH7UM5AWv+TIaKfwPtmRHIC8rqeysuM7Dij5fqSX83pQ+k6cUYNG8zMCCCDRNjX6cjl5OX2F&#xA;Gskr7mMaFo+m3fl43d4H42csszBCAWUIpZT/AMDkIgU9DKREtnovlG5jvbP66iFFkReKnqASf6Zd&#xA;E248xRpkGSYOxV2KuxV2KuxV2KplEol8m+aYXNEawmBI60aCUHITbcXNg/8AziR/5NSX/tmXH/Jy&#xA;LMZyy+y8KHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqxrzz+XXlPzvpn1DzBZCcJU29yh4XE&#xA;LHvFINx7g7HuMVfMn5jf84r6z5cs7zWdI1i1u9FtEaaUXzfVp40G9C28T+FarU9FwJeGerL6Xpc2&#xA;9IHkI6njy6Vp44o4Rd9Wd+QpEk0eeFgDxmbkp6FWVev45dj5NGXmz3QAiRyxoAqrx4qNgBuNhloa&#xA;ZJthYuxV2KuxV2KuxV2Kq97cLb+Q/NsjdGsHj+mWOSMfi2V5OTbi5sZ/5xBtWl/My+m3CW+kzEnt&#xA;Vp4FA/En6Mx3LL7EwodirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVeM/wDOWFprVx+VobT1&#xA;drWC/gl1RUBJ+rhJAC1P2BMUJ260PbFXx5aXGnJYXkM9uz3cwT6rcBto+LAsClN+Q2rXbEU1zjMy&#xA;iQfSOY7088g3wi1Ca0Y0FylU/wBeOpp/wJOTxndco2t6Np12La45N/dsKN/XLg45DIEdHUMjBlPQ&#xA;jfJMF2KuxV2KuxV2KuxVJ/zE1JbH8tdRirSTU7q3tU8aIfWb6KJT6cqylvwDdkX/ADhnorf87Lrb&#xA;r8P+j2UD+J+OWUf8m8pckvpvFDsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiq2WKKaJ4pUW&#xA;SKRSkkbgMrKwoQQdiCMVePecP+cWfy212WS605ZtAu3JJFkVNuWPjA4IUe0bKMVfNX5k/lN5v/Lj&#xA;WSZ0kuNMDg2GuRRlYZPANQv6T9ijH5VG+KeaWWPn+8jAW8gWcD/diHg3zI3U/hkxkajiHRkemefN&#xA;JLArO1s56pKtAfmRyXJiYaziLNdM1SK9jBUjkRyBU1Vge6nLAWkikdhQ7FXYq7FULqOqadptubi+&#xA;uEt4h0ZzuT4KOrH2GAkBIiTyeU/mD50i8wyWdnYq66fZc2UuKNJNKQGfiCdgqKq9+vjmPOVly8cO&#xA;EPsb8ivJMvk/8ttM066jMWpXQa+1FCKFZrih4MPGOMIh9xkWbP8AFXYq7FXYq7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FVk8EFxC8M8aywyArJE4DKynqGU7EYq8080f844/lRr5eT9FHSrqSpN&#xA;xpr/AFehO+0VHg/5J4q8I/N//nGqXyT5dn8yaVqr6lp1tJGtxbSwhJYo5G4B+asVcByoPwjrgSw3&#xA;8stW4yNYu28b84wf5H+Fh9B3+nLsZaM0er0G+1VIgY4SGk6FuoX+3LSXHAdo9yZInjc1dTyqepDf&#xA;24hSrXuoxWxVac3PVR2GJKgKWratDY6Nc6kCGWGJnSvQtSir9LUGJNC1jGzTynyj5Q84fmP5lOna&#xA;Z/pd+yGe4nuJOEcUQYKXYmtFDOBxUE+AzFJtzgAH09+WH/OL/lrytd22sa9cfprWrZhLBHx4WkMi&#xA;7qyofikZT0Lbf5NcVe24q7FXYq7FXYqk3mjzn5W8q2kV35h1KHToJn9OEzE1du4VVBZqd6DbFUVo&#xA;+v6Hrdqt3o+oW+oWzAES20qSrv48SaH2OKo/FXYq7FXYq7FUh8zeffJnldFbX9YttPZ6cIpXBlav&#xA;cRLykI9+OKoz/E3l79Af4h/SNv8AoP0vrH6R9RfQ9P8Am59Pb57dcVTLFXYq7FXYq7FXYq7FXYqg&#xA;dc0ax1vRr7R79PUstQgktrhe/CVSpI8CK1B7HFX59a7pGreRfOl7pVyK3elztE1QQssZ3Rx/kyRk&#xA;MPniDSyFhB3fmLXr2Rp/XkRIviKwckRASACePuaVbCZFAgAyDQfzFe0Q/Xoy8yKQkkYHx7dGG1D7&#xA;5OOTvapYe5KNQ80+YNZvQls0qM7fuoLYsZGJ8SvxMciZktkcYCFm8z6zPpDaVPOZbZ3V2L1Lnj0U&#xA;t3Fd9/DBxGqSIC7fUf8AziJ5MOneUr7zRcJS41qX0bQkbi2tiVJH+vKWr/qjAye+4odirsVdirsV&#xA;diryj8+vyXvfzHtNNm03UI7PUtKEwhiuA3oSrPwJDMgZkIMY3CnFXzXqP5C/nP5fujJDotzIybJd&#xA;abKsxIrSq+k3qD6VGBKilj/zkPEhhS383RpJQGMJqYDcdwKAb0xVN/Jnkn/nIG981aZepba3aTw3&#xA;EbG/1Fp4VjQMA5c3BXkvEbrQ8htQ4q+2sKGPfmHY+Y7/AMkazZ+W5jb65PbOljKG4NzPUK9RwZlq&#xA;qtXY74q+Lm/Ln89rdntxpGuAVPIR+syEt1+JCVP34Epp5f8A+cZ/zc1q4DXenppUDtWS6v5kB3Px&#xA;H04zJKT8138cVfSf/KlrX/lS3/Ktf0lJ/d1/SPE0+sfWPrVfS5f3fq7ca9P8rfCh6XirsVdirsVd&#xA;irsVdirsVdirzv8AMP8AInyL581e31fWBdW99AixSS2ciR+tGpJVZQ6SVpUiq0NO/TFWSeX/AMv/&#xA;ACZ5f0WXRdJ0i2t9NuF4XUBQSeupFD67Scml22+MnFXjPnX/AJxD0XUtX+u+WNU/Q1pMxa4sJojc&#xA;Rxk9TAQyMB/kMfpptim3p35b/k75M8g2o/Rdt9Y1Rl43GrXADXD16hTSkaf5KfTXrihL/On/ADj7&#xA;+Wnm3U/0pe2MllfueVxNYOIPWNa1kXiyEnuwAY9zirPtK0vT9J0220zToFtrCzjWG2gSvFI0FFG9&#xA;Sfmd8VRWKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVB6xrWk6Lp02patdxWNhbjlNczs&#xA;ERew3PcnYDvirw3zR/zmB5QsJ3g8v6Vc6zwJH1mVxZwt7pyWWQj/AFkXFNMWH/OZ2seqSfK9v6VN&#xA;l+tPyr48vTp+GBaZJoP/ADmN5VuZEj1vQ7vTQxoZbeRLtF92qIHp8lOFae0+VfOvlXzZYfXvL2pQ&#xA;6hbinqemSJIyegkjYLJGfZlGKE6xV2KuJAFT0xV8s/mv/wA5U60ur3Gk+RGit7K1cxtrLok8kzqa&#xA;FoVcNGI69CVJbrtgSl35b/8AOVnm221q3tPOkkWpaRcOI5r5Yo4J7fkaCSkISNkX9oca06HsVX1s&#xA;jq6h0IZWAKsDUEHoQcKG8VQGva5pmg6Neaxqkwt9PsYmmuJT2VewHck7Adztir5F84/85XfmJqWp&#xA;yN5bePQtMRiLeP0Ybid0HQyvOsiVPWiKKdN+pCXon5Ef85Han5m1uLyv5tWI6hdBv0dqUSiISOoL&#xA;GKVB8AZgDxZaeFKnCr6FxQ7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVC/vrTT7G5v72UQWdpE89z&#xA;M32UiiUu7GnZVBOKvhP84/zd1f8AMPXWlJe28v2jsNK08noOnqy02Mrjr/KNh4kJY7p/l23aFJbh&#xA;zIXAYKhotDuN+pyYixMkf+gtKpT0P+Gf+uHhCLKT6nZaLBVY5mWYf7rX4x9PSn35EgJBK3yz5p17&#xA;yxrEOr6HeSWV9AdpEOzLWpSRT8Loabq22RZPub8nfzRsfzD8qrqKqsGq2hEOrWanaOWlQ6VJPpyD&#xA;da+47YUM6xV59+ffml/Lf5V61dwyeneXcYsLVhsed0fTYr7rHzYfLFXyp+R3lO01nzBcahfRCa10&#xA;tFZI3FVM8hPpkg9eIVj86ZbhjZdB7Q62WLEIRNSn9w5qP56aZZWHnZGtYliF5aR3EyoAoMheSMmg&#xA;8RGK4MwqTP2dzSnp/Ub4ZEfYD+l9kflRdz3f5Z+Vp525yvpdpzbuSsKrU+5plbvGVYq+e/8AnMLz&#xA;U9p5Z0jy1C1G1Sdrq6AP+6bUAKrezSSBv9jikMB/InyfYfoSbX723Sa4vHeG19RQwWFPhYrXu78g&#xA;fYfPMjDDa3jfaLXS8QYomhHc+954Il0X824k0/8AcrY63GbULtwCXIKgU7DplExRL0+hyGeCEjzM&#xA;Q/QXA5TsVdirsVdirsVdirsVdirsVdirsVdiriQBU9MVfM//ADkP+fXlfVfLF35Q8rXbXt1dTpFq&#xA;F5Eh+r+hGS7pFLUcy0ioKqCpWu+KXh3mKE2Gl6V5ZiH+lMReah4+tKKIp/1F6/RkRuWR2COgiWGF&#xA;Il+zGoUfQKZc0oq7smGlzTxMfWELOg9wtRhPJQxewNnZ6A+p/V0ur9rr0FMw5xxKIw4b0z8LM5r9&#xA;qo2yhtW6lONV0oaiYYory1kEF36KCNXjkFYnKr8NQVZSR7Yqz3/nGXzc2gfmjZWkkvCx1xW0+dST&#xA;xMj/ABW5p/N6qhB/rHCh9vYUPnD/AJzM1lk0jy3oqk8bi4uLyRe1YEWND/yXbFISL8h9MW18km7p&#xA;8d/cyycv8mOkQH0FGzKwj0vB+0eXi1HD/NiP1vPvz8n9TzxElQfRsYk+VXkf/jfKs/1O99m41pj5&#xA;yP6H2L+Vlt9W/LTypCQVZdJsi6nqGa3RmH3nKnfMoxV8Xf8AOV+ryXv5ryWZJ9PS7K3t0U7Csim4&#xA;JHz9an0YEvUvJmmLpnlPSbEDi0NrF6gH+/GUNIfpdjmfAUA+Ya/N4mecu+R/Y+fin6R/OdIlqRce&#xA;YUiXh1IN4EFPc5hT+ovofZ8a0+Mf0I/c/QDA5bsVdirsVeYecP8AnI/8rvLF1JZvfSarexErLBpq&#xA;CcIw2oZWaOGo7gOSO+Ksb07/AJy+/Li4uBFeWGp2SMQBO0UUiDxLCOUv9ynFaeveW/NXlzzNpy6l&#xA;oOoQ6jZtsZIWqValeMiGjI3+SwBxVNcVdirsVdirsVdiryf/AJyV8+f4X/Lm4s7aXhqmvE2FsAfi&#xA;WFhW5kHyjPCo6Fhir460mOy0zzDanWARBBwnkRRyNTGJEUj5kA5EsgpnVLm98w/pCTeW5nqV60Vj&#xA;x4j5LsMMUSZYkbvXgK8RU08Mua03tN7aOvhhQ8/1EHS59T0wpWC44NFX9kq4dGHyQsv05TIbtoOy&#xA;3UdHfT9K02+ScumqRyFkC8QvpuAVO55b0PTI2mkFpt/c6dqNrqFq3C6s5o7iB/CSJg6n6CMKH6Oe&#xA;XNbtdd0DTtatf959StorqIdwJUD8T7itDhQ8z/5yR/K7UfO/lW2u9HQzazobSy29qOs0MwX1o1/y&#xA;/wB2rL40p3xV8p+XPzB84eT0uNMtiEQOfUsruMn0pBs1FJVkPiMlHIYuu1vZWHUESmPV3hV0Hy95&#xA;2/NHzeI7eN7u9uXQXl7wCwW0P2echUBVVFGw6t0FTkSSTZczT6eGGAhAVEPvzTrGGw0+1sYP7m0i&#xA;jgi/1Y1Cr+AxbURir5i/5yp/KbWbrU1886NbveQNCkOsQRAvJEYhRJwo3KcKK38tK9DspeOaX+cH&#xA;nfTdITTILmNo4k9OCeWMPKijoAx2NO3IHJjLICnUZuw9NkycZB35i9mVf848/l35i8z/AJgaf5km&#xA;tn/Qul3X1271GVaJJPGS6JGT9t/VoWp9kde1YO2EQBQ5PtbFXYq7FXzH/wA5Q/nLfQ3knkTy/ctA&#xA;qIP07dRMQ7GRai1DDovAgyUO9eP8wIS8y8hfknqOv2cWqarcHTtOmAe3jVeU8qHowrsinsTWvhTf&#xA;ASmmV6r/AM466M1s36J1S5iuQPh+tBJY2PgfTWIr898HEtPPfLvmLzp+VHnT1oeUF1AwW8s2J9C6&#xA;gr0PZkYfZbqD75IFD7q8p+ZtN80eXNP1/TGLWWoRCWMH7Sn7Lxt/lI4Kt7jChCefvOul+S/Kt95h&#xA;1H4orVaQwAgNNM+0cS17s3XwFT2xV8T+YfzE/ND8xdakBuru451aLSbAyJbxINtokNNq0LvU+JwJ&#xA;Tb8ufzv89+QddW01We6vtIST09Q0i9Z2kjHcwmWrRuvWnQ9/EKvs/wDxPoX+Gf8AE31tf0J9U+v/&#xA;AFzfj9X4epyp1+z269sKE0xV8gf85eX13N+ZOm2YmWWG202JobdDy4SSzSl+Sb0dwqfNeOBIeP69&#xA;olxpb2xvZxJfXSGa5t6kyRVO3qMa7sN8AKSERpiw3Wp3GoLGsFrET6MY2VB+yP8AYr1+/JxDGRTj&#xA;S/M2nfXvqtG/esESbbjXt70JyQkxMVvmHzasBe0sCGmHwyT9Qp8F8TjKSiLGomkv47prqV5ZoIfU&#xA;id2JOzqCtTXajE5DmyTjWP3vkTQZOpgmuYiev2n5fRsMgObM8nWtjp9h/iPRNSMSXUKE2d1IOLF4&#xA;HqFQtv8AvRQj2wlAfVP/ADijr13qf5WC0uAxGkXs1nBIejRsFnAr/kmYj2FMkxey4qlGreTvKOsz&#xA;evq+h6fqU+w9W7tYZ32FB8UiscVR2n6Zpum2y2unWkNlarusFvGsUY7bKgUYqicVdirsVSG58g+R&#xA;Lq6+t3PlzS57uvL6xJZW7ycq1rzZC1a4qnkUUUUaxRIscSAKiKAqqBsAANgMVXYq7FXYq/PbRVbz&#xA;n+ZcUuoEv+l9QkubsN+0rM0zr7VUccBS+xtI8pTXESSzt9XgIHBAPiK9tuijKrZ0mz+TNMKUWSVW&#xA;7MSp+8ccbWngv/OTnkaS00Kx1viJGtrgW5uFFKxTKTxb5Oop8zkolBDK/wDnDzWprnyTq+kyNyGn&#xA;XwkhBr8KXMYPEdqc42P05YwKR/8AOZmuTLF5b0JGIhcz31wm9CycYoj9HKTFIX/849/l/dr5FTWY&#xA;okWbVpHkMrmjGOJ2iRB12BVm+nK5FkAw/wD5yT8p/UZdO1h4fRunY2l0QPtjiXiao2NArCv9MMSg&#xA;qf8Ajq9/6FX/AET6x9T9N/oilfj+rcPr/wA+PL4Plt0ySH0z+bPnz/A3ka/19IRcXUfCGzhb7Bnl&#xA;PFC9P2V+0fGlMKHxguoTRGfzr5hm/SGu6nI8thFKQS0hNDO4HRU6KoG3btSB3ZjZiOoyXst5JNfM&#xA;zXUpEkpb7VXHIV8Nj07ZJir3lyYbSPT4tgo5XJHdzvx/2PTCSgJhfeSNesvL1vrk0Q+qz7vGKmSN&#xA;D9h5BTYN/t9ckYGrcWGtxyyHGOY+1OfJP5eHV9G1LXdSLxWFpbzPaxj4TNKkZYGv8ikb069MlDHY&#xA;suHr+0vCnHHDeUiL8hf3sZ0JPUmuo60DWswJ+S1/WMri7Ypt5Q0XzR5zurLybo8UcrtM1whchBGK&#xA;UkkdyfsIN9hXwr0wUyt9d6x/zjj+XOufUrjVYJ21K3tre2uru3laL6wbaNYw8i/EKsqUJG/vhYs+&#xA;8s+WNC8saPBo+h2iWWnW9fThSpJLGrMzMSzMe5Y1xVrzP5q0Dyvo82sa7eJZWENA0r1JZj0RFWrO&#xA;x7KorirwvVP+cy/L0V2U0zy5dXdqGp6888duxHiEVJ/xbFNM4/Lj/nIjyJ52vI9MUyaTrEu0Nlec&#xA;QsrfywyqSrH/ACTxY9hih6jirsVYb+Y/5s+T/wAv7OOXW52e8uAWtNOtwHuJQNuVCQFWv7TEDwqc&#xA;VeNf9DoQfWqf4Sb6pWhf68PUpXrx9Dj9HL6cU09g/Lf84vJfn+Bxo9w0OowrzuNLuQEuFXoXUAsr&#xA;pX9pSabVpXFDN8VdirsVfn95faPyH+bdsmqqfQ0PVGt72oqfRSQxO9B9r4DyHjkSyD7wililiSWJ&#xA;1kikUNHIpDKysKggjYgjKma/FXgH/OXHmu1g8t6Z5YjkBvr24F5PGDutvCrKvIf5cjfD/qnJwDGS&#xA;af8AOH+hzWnkXU9WlTiNUvuMBI+1HbIF5D29R3H0ZYwLFf8AnM3TZ11DyxqQBMEkVzbE02V0ZHFT&#xA;/lBzT5YEh6H/AM4163a6l+U+mW8TAz6ZJPaXSDqreq0qffHKpyuXNmGF/wDOYGsWa6JoOjVBvJrl&#xA;7wgU5LFFGY9+9GaXb5HDBEnmn+GLv/oWf9Mem3H/ABL9Y5fs/Vvq31Xl/wAj/hybF9jecPKWjebf&#xA;Lt3oGsRmSxu1AYoeLoykMjo1DRlYVH9MKHzB54/I/wAr/lZZyeZdW1n9NBWMegaJJbqnrXRBKG4P&#xA;Nw8UX23UKOXTatCEvC2nuLi4nv7lzNMzmWWVzVnlkJNWJ6ktUnCqYeV7fTpNTF5qsgTT7T97Nz35&#xA;tX4EpuW5HcjwBwx57tOcy4ajzL06x/Nny26zc/WhEKFgJEX95TbinFm3PvTLhlDpMnZc+lMG8y/m&#xA;X5k1qSSG3mex09gVFrASpZKb+o4oWqOo6e2VyyEuw03ZmLHuRxS7z+hjmnz+j9afxgdP+RlE/wCN&#xA;sgHYl7h/zh5pDXHnrV9VK1isNOMVadJLmVOO/wDqRPgUvrrCh2KvjP8A5yr85XWr/mI2gpKf0doE&#xA;aRJED8JuJkEssnzoyp/scCWGad5KsBao14Xed1BYA8QpO9BTwywQYGSQ67pEmi30UltM3EnnBIGp&#xA;IjKajdaEEdQcjIUyBt9s/kN5/u/O/wCXlrqV+eWp2Ur2F/LSgklhVWEnzeORC3+VXAr0QkAVPTFX&#xA;56ed/Ml9578/3+rSOT+kLkrag1pHbIeMS0/yY1FfepxG6U0HkrRvQ9MiQyU/vuR5V+X2fwyzgDDi&#xA;LH7G/wBa8lea7XUtNnMV/p8qz20wqA69wwHVXWquvcVGVkUyBt+g/l3WrbXNA03Wrba31K1hu41O&#xA;5CzIH4n3HKhxVMMVdir5S/5y+8maZYazpnmq2YR3er8re9t6bO1ui8ZgfHgQjfIYEhnn/OLmp61c&#xA;/llI2qzmSzs7uWDTWk6pbxxoxWv8iuzcfDp0AyuXNmGM+av+cu9OiW4t/LOjSXEwLJBe3riOLbYS&#xA;einJ2B7Asv8ADCII4nj/AJY8r+e/zg87ySyySXE1xIr6pq0i/ubaLp0HFdlFEjXr8qnJgMX3L5b8&#xA;v6b5d0Gx0PTE9Ox0+FYYQd2IXqzEUqzGrMfE4UMZ/OT8uo/Pvki60dGEeowsLrS5W+yLiMEKrH+V&#xA;1YofCte2Kvjryr52/MD8qfMN7bQRfU7uoi1HSr6NjE5SpQsgZDtWqujbg9aHIkWyBd+af5hSefrr&#xA;S9evIorXVYrdrC7tYOXp8YZDLHKvIsQH+sMtCf2cQKUl7h+kh/0Jn9Y+rx0+rfVvToONf0r9X9Sl&#xA;Ptft1/m3ySHv2va9pOgaPdaxq1wtrp9khkuJmrsBsAANySdgBuTih8H/AJqfmRq/5jebn1GVWjso&#xA;6waTYV2hgrX4qbc3+07fR0AwJYrqHpRCO0hYOsQrI43DSN1+7phKAjfLHk7zN5oupbXQ7GS8a3ja&#xA;a4dRSOKNQSWkc/Cuy7dz0FTgShvL/l/WPMOsW2j6PbPd6hdvwhhT7ySTsqqN2Y7AYq+xfI3/ADj3&#xA;oPlXyRq1lMqah5k1ewntbvUCu0frwlDDb1FVSp+19pu/YAofFOBL7a/5xm8hTeVvy+W9vYzHqevO&#xA;L2ZGBDJAF426EH/JJf8A2VMKHrmKuxV8Efnnbz2n5w+ZVuAeRvBMAwArHIiyJ0ptwYUwJQXmfzQ8&#xA;RW0096Myh5Jx1AYVUL9G9cslJhGKaeR/yI/MnztFFqNpZi10y4NV1S+f00cV3ZF+KWQe6rT3ytm+&#xA;wvys/Luw8geUYNBtZjcy82uL27K8fVuJAAzBd6AKiqo8BhQyuaJJoZIX+xIpRqeDChxV+ePmzyt5&#xA;j8h+arjTb2J7e5tJJFtblo6JPFuqyxcgVZXU/R0648koWHzhrscgZ5llUdUZEAP0qFOHjKOELfM+&#xA;qW2pXNvPAKfuQHB6huTEr9GMjaxFPur8mbW4tfyq8rQ3AIl/R8L0NahZF5p1/wAlhgVmeKuxV5T/&#xA;AM5HflvqPnXyQh0iL1tY0ib61bW4+1NGVKzRJ/lEUYePGnfFXyf5f/M3z15U0HU/LGnXRtLK+Zxd&#xA;QyRj1YndRHJwLDlGzKvE+Hah3yJDK3oX/OPP5FR+bribXvNVlL/hqJONlEzSQ/W5iR8SspVzEgBq&#xA;VIq2wOxwofWuiaBouhafHp2jWUNhZR/YggQIte5NOpPcnfChH4q7FWC/nF+Wln578nXmnpDCutIg&#xA;k0u9kReaSRtyEfqU5Kkm6t864q+PLL8kPzWu9aXSP8NX0Exk9N7meF0tE33Y3NPSKjrVWNe1cCX2&#xA;P/yrDT/+VS/8q+9X9z+j/qf1qn+76c/W4/8AGb46YUPmH/nJD8z9c8yecb3y2wa00TQbmS3itKke&#xA;tNGeDXEn81afu/BT7nAl5VpGj6zq94tlpNnPfXcmwgto3lcg/wCSgJpir3f8uf8AnEzXdQeO+86z&#xA;nSrLZhptuyPdSDrR3HKOIf8ABN7DFX075a8reX/LGlR6VoNjFYWMe4iiG7N3Z2NWdj/MxJwoUNE8&#xA;j+T9C1C61HR9HtbC+vK/WbiCJUdgTyIqOgLbkDbFU8xV4ppn/OK3kq086t5guLua908TtdQ6LKi+&#xA;kHLclV5AayRoeikb7cid6qva8VdirsVfP/8Azkz+S+peZPS83eXLc3OqWsQh1KwjFZJ4UqUkjUfa&#xA;kStCvUrSnShUvEPyt/JvzX5q822NteaTdWuiRTK+qXlxDJFGsMZ5PGGYLV3HwgDfevTAr7tiiihi&#xA;SKJFjijUJHGgCqqqKAADYADChdirsVWTW8E6hJo1lUGoV1DCvjQ4q8p/Pv8AJs+efLdu2hxQQa7p&#xA;jtJaqQsazxuAJIS4A4k8QVJ2qO1ahV4j+Xv/ADi7561DzFB/iyyGk6HbSBrwtNFJLMqGpiiELv8A&#xA;a6czsBuKnbAl9ixRRxRJFEoSKNQqIooFVRQAAdgMKF2KuxV2KpZe+VvLF/dfXL7SLK6u9v8ASJ7e&#xA;KST4enxspbbFUyVVVQqgBQKADYADFW8VdirsVdirsVdirH9W/LzyJrGonUtV8v6ffX7cQ9zcW0Uj&#xA;txFF5llPKg23xVMbKy0TR4Pq2n2tvYwdoLaNIl/4FAoxppyaiEOZVf0laVoW4k+Iphpp/P4rq0Sj&#xA;o4qpBHiMDlRmJCw3iydirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir&#xA;sVdiqB1W/wDq0YVN5XNFHudsIDre0NZ4UaH1F5554/MP9A3I0zT0FxqjAGeRqkIX3VQACSxG9KdM&#xA;z8Gm4hfR0eozyh6Y/V1KTad54uhJ/ua12C2diOVs1tK4WoqA+y8fuyyWn7gXG9R+qf2Wz3StQuII&#xA;4rlJo7qxm3SeFuaEHMCeOnI0+bLppAk8WM9WVxSLJGrr0YVyp6zHMTiCOq7Fm7FXYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYqw3zdqLWlx9ZILJan1nA/ki+N/8AhVOW&#xA;4o2aeQ7VyE6kf0TfyYJFp9vJ+Y17cXNJVmhe4sWG4cMQ3weJ9Nl+jNkZfuwggDISe608ijsLvTrx&#xA;PqccaQoZCu3Fq1J5bd/HKRI206TVeMJDhqki/LW/+qeZbrRYiW0jUEM0ELdEf01k28PhYg/R4Zdq&#xA;I3GzzbdORxGH8Mnr2huxs+JNeBpXNSXe9kSJxUeiYYHaOxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2Ksd1/T4pbmk4rb3CPDL/qyoUP68sxyovN9p4OHOJn6Tt83kNy&#xA;uq6DcppGpHjLZvy026kUlJEUniVYcWGzb8WqpzbCpCx8XA9UPTLpyRd7r/mPXI/0TFHb2sN0eE/1&#xA;QtJNKD1VKqoXkOpPbIjHGO6eKUvSABfcmXlDR2tvMNzqZp6VqhtbBF6SSsAJXXxjQgqG/aynUZfT&#xA;XVrOQQlY58h7/wBQer6TbmCyRW+0dzmuL0/ZuHw8QBRmBz3Yq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FXYq7FXYq7FXYq7FXYq7FVK5to7iIxuKg4tOfBHLHhkkl/pUrQm3uLaK/tf99zKHG3j&#xA;yDD8MtjlIdFl0ubHtQnFLBo0gR4LCwh0+OUcZfq0axsyntyULkjmJcGUNRP0wiIJronlmCyVSygc&#xA;fsoMqlK3Ydn9jeGeKfNPsi9C7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7F&#xA;XYq7FXYq7FX/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:370520d6-4b08-c94d-b9d4-9fde2549882e</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:d5ff6d33-9d50-4a24-b8e0-18e2e036eb92</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:54:48+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=112 G=137 B=148</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>112</xmpG:red>
                           <xmpG:green>137</xmpG:green>
                           <xmpG:blue>147</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=69 G=230 B=203</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>68</xmpG:red>
                           <xmpG:green>230</xmpG:green>
                           <xmpG:blue>202</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=204 G=255 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>204</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=254 B=208</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>253</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=22 G=132 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>22</xmpG:red>
                           <xmpG:green>131</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=77 G=255 B=207</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>77</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=6 G=46 B=33</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>6</xmpG:red>
                           <xmpG:green>46</xmpG:green>
                           <xmpG:blue>33</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=138 G=128 B=120</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>138</xmpG:red>
                           <xmpG:green>128</xmpG:green>
                           <xmpG:blue>120</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
35.9092 44.9668 mo
35 44 33 42 32.1494 41.2695 cv
31.4063 41.0215 30.6152 40.9004 29.792 40.8555 cv
25.6748 40.6309 20.7588 42.3066 17.0225 39.4854 cv
16.46 38.7051 16.0273 37.8486 15.793 36.9697 cv
14.666 32.7383 16.8369 25.5732 19.3848 22.4609 cv
21.1484 20.3096 24.5322 18.1436 28.6045 17.4814 cv
29.4199 17.3486 30.2617 17.2764 31.124 17.2744 cv
37.3047 17.6221 42.3613 21.0293 45.2324 25.7988 cv
45.7109 26.5938 46.1279 27.4268 46.4814 28.2881 cv
49 35 49 44 43 50 cv
39 53 36 49 35.9102 44.9678 cv
35.9092 44.9668 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.641413 .508293 .418158 .322362 cmyk
f
28.9551 35.834 mo
29.3213 35.834 29.6914 35.8428 30.0645 35.8633 cv
31.415 35.9365 32.6152 36.1533 33.7324 36.5264 cv
34.3467 36.7314 34.916 37.0547 35.4072 37.4766 cv
36.5049 38.4189 38.6885 40.624 39.5518 41.541 cv
40.3643 42.4053 40.8418 43.5293 40.9023 44.71 cv
43.8789 40.2441 43.3652 34.2686 41.8271 30.1172 cv
41.5752 29.5117 41.2793 28.9268 40.9482 28.3779 cv
38.7793 24.7744 35.1572 22.5566 30.9912 22.2754 cv
30.4629 22.2842 29.9316 22.3311 29.4082 22.416 cv
26.6006 22.873 24.2939 24.3594 23.252 25.6309 cv
21.5996 27.6484 19.9609 33.1904 20.625 35.6826 cv
20.6777 35.8457 li
21.7549 36.2578 23.3457 36.1777 25.6143 36.0029 cv
26.6982 35.9189 27.8096 35.834 28.9551 35.834 cv
cp
41.3496 46.4199 mo
41.3594 46.4199 li
41.3496 46.4199 li
cp
40.2695 56.0498 mo
39.0439 56.0498 37.8311 55.7744 36.6826 55.2158 cv
33.8291 53.8281 31.7754 50.8096 31.126 47.2178 cv
30.665 46.7529 30.1748 46.2695 29.7549 45.8643 cv
29.5195 45.8477 li
28.6348 45.7998 27.54 45.8848 26.3818 45.9736 cv
22.9072 46.2383 18.1553 46.6064 14.0098 43.4756 cv
13.6104 43.1738 13.2588 42.8145 12.9668 42.4092 cv
12.0215 41.0977 11.3467 39.7012 10.9619 38.2578 cv
9.375 32.2988 12.0879 23.4805 15.5156 19.2939 cv
18.3301 15.8613 22.9219 13.3398 27.8018 12.5459 cv
28.8936 12.3682 30.0078 12.2773 31.1123 12.2744 cv
31.2119 12.2734 31.3076 12.2773 31.4053 12.2822 cv
38.876 12.7021 45.6465 16.791 49.5166 23.2197 cv
50.1211 24.2246 50.6563 25.291 51.1074 26.3896 cv
51.1631 26.5313 li
54.1143 34.3975 54.4492 45.6221 46.5352 53.5352 cv
46.3682 53.7031 46.1895 53.8584 46 54 cv
44.1895 55.3584 42.2119 56.0498 40.2695 56.0498 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_01_Left Hand.eps)
%%CreationDate: 7/31/2020 1:54 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H]K2jE@:NG!IfgD!!4E!fVj11)QO%eX$)oF/A\X)&o9q.Gq#.k4p51uBsaL]j.2T<Fh7I5PWc<tP0H02VbU8n4sK%WMo["<
%i;!;`GMg&^pUfJ_s8CP.Y5+Q"U"10T1;*/Il'!4ei4m&+]3!lRI+e>obO=<%HM?sm+5Xb[p$K5dpM\o<q"Cn0:4IT[mcLE#IKuLX
%\G>fu2fIRQn`KW/`QNa`mi6e>IWP8[c)\JN+5Xa,Ga!%^,)5"%Q[ef_Hj_F>pI$[Lq8lh`rSlAJO3eN<)__6-l)2;qTAr(cI(T17
%>fJ^:BfX!K\"ljSc#WEnduW>0:Oa@%If8;Io07`;r!;W`^Yk!49!/?SjO.9=pr;KcYSR#kjbdCspt]35b8J'#qpuM:+aLT6EZ<G)
%@`3Wlh2gCODuSn>5/j#OO8VuRjY\pZ7CY>R(*('Y^,^T$Hn%db00B*;$O+92@fLK@E,ihb[+H.1C(*bPa1M".HhqKUpf1\kX1gOA
%HWQP%c!92kMDWf"%C)<-S!g!V%bF`OG?R=hFG&rL@eb6u)/b%5k#Qf@<He)lk.Jls]#GQ_DQ-92n.EiAi@4</)87"FHo$GLDb@jh
%n?sLOkdc[L(Gmite<@f#&T3j/l<NH);YoE)`NIH^S&>4t98.9(NcWaIb2"/LlRI?"5!7i_%8Z!k%8Wt6AfSAJ=pk&%_.^`QIe\tR
%pZct+^]!rd`Q-U!hu,?ZoC.sfPPH#uIfG!aJE[Fl#g*\:_jg7uhnJmRbt"Ae2uP4Nqsd08I.O!/*\$!p\*u[HI=77;\5X\dH04U'
%^MNcWhtrm^'DXgVs7mQ$iVA)<gr'=;Hhi9Jg]/cfUdie$rUBg\DdRne[r;$:"((QmYMT9Xc&7XdLl&nU^.n*2b4'3E6SAY`5SlW-
%%QaRN=ip3BjVq=Lph00eLKrP9c`9:CIeCar^A76$?@;A0K5d3qLQB2prVlmGaV"LYnaF2=oBO4HhnB!pc/i[Y&(FD4J+Mq74@9)=
%h4#5!Ckc8hs5f-QJ+;KAY<E#GI/i=j*2gQcWJc6!\)4lIro-_WO:OC_aj5G9lJ:@4rs*;9b5n1PL<bM-)POoNKEl=ti/e0;JEM#\
%Db5R6'k8gtLXodN"Fgp*nRiqMs6fQl^OG<Q;*@Q$$fh/S&$Nb4I'`U;b5^u]X2@`^+)*AjrPsu&hWY,%s-qJ5#7eE%rq%"On&r=]
%:@I*"+FK)s*apA-"0VRarU8grrWdiU<IFOfGo.b'k&9.p(LFkoMP!*.>Q1`'k3$ZsK+J"E_u&nOs-%ZAIK/r]%hIR1s-HeGcX4G6
%Fi,<Xd&0+>q;FOA\,9/qJU^;kViVqsGk_;cKV&[["M]LN?j6%jT7$7q7Gi]7?bHC:Nukh&0E1@ha-Zi&=nh&.53_M8nET,UpE*d(
%Irk4fJ,*I@#Mn-qpMp+`pHi&8qaU?CH2RSp5DV`lE;b\,s%YgTg/+a&me<*g_-Hf/UB9>L5P=b\^(8F!3hf^F:LWeK*c+n\HJ=V#
%#Dr!_n2kF3`uD2Io'R$lhRrkOhOgis9ETbCk/FqK+,3)[o_\YIpbHr/$/:jJ1Oqt<lBGGk+o6+rB5GYiO2$!?Rt1V<*_,De+#XMd
%_$c!Y*d+M"+9*(/[o`%4q=aE,`teRtoa:[nH9HGeF(c3DkqNW/]m0/2p$Y)Wq#.tAIX-#4nD1SRHLpn"hnSsgNV(+:kt$M:Yshf/
%qCkZ7;2,tg85#QWKq<0u0/XPMq^CUuVml]srlsbIhfb<Ljp=LQ&c52GdMcQur7G6h]HnnD1N3?7]g:OIpjp'Ab_0a(Hj044'O"[Z
%-GB3Dnb('aYC?#>Da1s^gfhUDjhn^&^#N8g"UE;-cJYu<cI0>aSq2I[Sf@=87cck^UY*&ilnXa+VKeMrSm+f%IXR1@bP`a3XS"q?
%SIrcNVnWVK"ZIHN+C2-I=1^T"a94qu[c`F!g?"Y#)4N(uo]G_hCQN85CmOg%o8>iI!5\'ul[O1f=2KOfmH\thKA?Ca]@S-3Z&n,1
%mYJI?933gfNPd(f)bgY4fY-9OoG47[J:'N&cd/1GI0092]2?<ARh%0dGn&d/?UIaW]De]0'=\$X.<+SS*FB/dLSiE/d9pJl1o!;L
%9#Mg]-nY?c]Eja01+[h+qFJ@aIe33R5<aMdIm8A016b,%6K-OIaq;dqSY9;egpOc3!l[Iq<!&9i,sfXLaoV,[paX%=<_c]@:bG`8
%YT0"'\>,r7j>Xf*o(7e;4VfsjX+P0+g<W.&qt@J6MqZen<D&C:V*lq3TDUq'6d$9pX_MrN=OL*5#0f@c(Uno])I.=%20cKl]D0?t
%p?o8jd,dcIp(r2p*:-oBph('oJ0kTYF:)Y/GB#p$:5$_11R&ds4N]CMi]1?)?!<Ce^]kEbY,S(R/031gS^Sn@jX%P*1WYCj[+\bn
%@N=qnTC\m1`>.=;-,ZNoj]Kgi;I/tL6W>#V=K#9%(Om>u%(X9DmNQk<]5bZ6iX[nKmLK9[!c9@6N^cBWUn8;8BG^SrJT<.<6\1P4
%>PWars8)kt2>stul2Ua,^E/9`UifT&q#3tK_Y8gD*i1GYGrPc#[f/A5Tkc;?'UV?De%u/""QgYF5<N\7=bfZH\A7^ols&o9;?o!3
%_@4Z8,o%1WbsHUZ'f[:K-5PKH/tQH'hEdDj"IP)bfOtAAE.hA=_0PhJ$-gCZ1U4_h4NL4/a@q7k,b%/?'plQ_AQA"pg8aArKf(LW
%S',MGc;V9Tb2$0-ShI/(<lIi\X@eui]P-F!='cVJ9gsC7-=MKqKo<^UbAt<Y?d@j+l$NULf?X%o?udT=LiE'#G@"$gVp"=P3h;;H
%rn=?C$;AtQNuUpojA*l1T'(>YSr]f0&_LPSemj8&S"PrHES#n(l'ZeZJ[d"Ha98%PJFgCj!rKe;p4a7I&dR&FC+HZ\,T)9_Huj/(
%GP+k[oi.!fAm;AP9tL^d/Q<8Y@juW`!kdRs*9@0JV#kk_nA#W\%B`Q1M6P,TYi\]fRMt^K$saO'@lWNoaA]I$kZRRmom-4el'^Le
%q3goTossb;jk9Io@@tmfcp#f_d!hXZYeJK@OT'dIrLl@UI#*);qaGj$qaPpfqaZ!(qac'*qh]V+31KOUENT]>../bUZ%<r1H77YJ
%i&A[e[.-B#;NIsok=R)5ItZPG)NYufEoWGhlU&MMDa(k7N@TO\YXrbPr"e%tkGq^&JRt`Of$q[H9O<>U%'=RA`l<GQmr.#)]i!R\
%j13?d.eEW7jhOj1YTO2d_K3[^&d7Y$:cVc4a%_+*"_<e$S;2.L,q`5g'e^k8JJV#=8GgNA;p9U+C*0ZC@-%(%h4[<mM$Au'nEA/Q
%8Zo$<di:rV3=U!<'\lSBXDjA<:o4OEd@`>6nk:o47\tmHaVT.JO'i:b:p<TckHs.Tf00T.#N?!0k0>B7!M?m&$h4X$r$,*LnV3n"
%$[E/dY9S(YVb_j?>bE2e\n`Dk+E%;k'kA7Pd>df#eHl_[$]$,PH@Y8\^kG;e&06;rA0X719E/Xjq*i_U,i(uW8Eu'6I(0eR`im/c
%()h5X1NDcnj5"Dm*q$#cFMd"T+iqUfM4"G.Df@6),/.H+r"#+.^=N)BWtApsKmj(o[ZRqq\A1j0g/J:0[ZX[f'(00gObSQf0$(Sr
%BfV?!*GAZ.Qk%9N..J(7GoJ6DUg1cOJVeSoGGL(h%#dD%#tGX#Z:Mp<q2nq+>1s;T1tL/Q6?XV##@_8KU-VgLGo;r`'<0YA39#`h
%ZoI,nR<W!@B$a5+##;%:gHGK*;Wb`s'W@/OKe%kcq\:C_j(:S:8q1<$^S/XmqLg;GeCS1Rk%4"Nr*AR#Q@fsOnGo>Y#(Vo%rXMGW
%s6g,5(`Z!?9nc&Y_;A%\nG>q(Gf53\=9id!&rh76Z.BkDmuVOaSrUQO.u/>6AqnS?S/jX\nFodlb>GVqS24g+n9__;(RX-)W+j)\
%=_("lSAJFJo*VT#qMOo/dI0d#XN:E$h[9<=SpRj1+5l<q]/VRtj0WHkVHs(ZofL[m@&?![m<JBfGHdk5:9JY?GB4Q&rTH=-;#>Um
%otU(bY;Ldf)VrOS_A3`%G*-1`&[mcHZ'+1E8?#Eq-(BbZ.L.[iSuA07h;sYJ.?!OHIc./:=$/YD3aOCn9nB]GnkUo]-tOg[9!Y/q
%OY3d=1P-OYkg#tg@mO7Vb!jI@b"6^?,O*_`dB+?ZFf8^bf!7TAb9Z[*'$>i?gqMN_fe,D>E)3?\+%QWTB!Xp?'D<3i`?WKu?8V5U
%PH.`5U?8[lhFOYHm9K=*dEpVl3r4PPFLGKq8Tnts8JeX@Cc1mfY0Jqb!YW01lQk5'[<8lqW.h#$37Q*%[aPn3*#]o,XJ#ZKX@*Be
%PQ70aDQsT51`+MWE%$do.?1-X9G0`-hRap!"qH)/5(B5r!1F'Q6)Hdid*@5)c@pG;U[2$Hp9mBdTLUGep<kba\d-rm@<^>6IJ<Ng
%jahHl!]?QNjg6JW<B3aL%0_J&C91=U:pQ?sV4.I;'Q6DD$!?[n,Dqc?E.+:RS>bYW@4-l0;\tm=(RdN(;$V*VT0=EV?R2[Z+NpB)
%lQ6mO1t37f"+Hf>*6B[T+Nn.;lQFmdTfd`i7Vl+0,?YBV=U[HM8Ar<B1G<9%4Hfs=1iOGr/':ATNFl`8Y+`MF\A[&ODF`^qFg/K#
%?9k"\G_B:HE_^(t*7oZu4']85YuO]R]rcT_d;?,/NA`e"o4F3PlWpW5[b>S95;ZkoBCPS'F*g!DpZM5+m%.fKe7eH$.-8TFT4e&X
%Hj=q5GLk6Uk;#0hHKOPKn$=+u5KrN.26aCoW5P6LYZ_>e\*r`f%sr(Y<WtR.[]9IEe<R:F)sc%Z?6==BC&@l:rA^kj2sdI&3n^[#
%Wr(t&kG6Yf2SRKBG0SR/kpH9]asR)?*dXj#meoaM(bG($b#'?2ibM*EQJ%T+n6*coBS3cPe%X_36MLIsO1uY-s*Y3Ho)mtY^a5/.
%mSF8--'V?<[Z!Y@QHBPI'=d6\T0<k/@4lCFj/ZE6\JM2!Mk=B_<fSnt:%jI:$e4CL2"A\Y9o<<b_NXW-P!]n"15jAL&jr!%:g3?2
%-f?0p.06gL.Q2hX!X)'8Y]*At>Ykd\DqM)]WA=GhG^Z^!dps3sZ+<H2[:cd4<LO^oOYneN/eD5J@gpil)?jun^E\"edAG3TFgeG/
%(82!XJWU&prs,g@=//N_6B-@mS$#4B]j_QLhZkUAfX7ko^Yp3Tf5'ZfhB,Wc2]RQrA%caSlTf;,gVZ4.j7H'Cc@9Q];LlA]*U?n[
%7B_$183a7j@V&N>T3;kIU#+bJMqauCDjVu:rABcC/Z']=Zb-58(V;0rCY!1*+IdefWI:;<X;=0Y/7p\<q`%g#$Z.H>m%#N0G@$k)
%+UdHR).,j[-J?KIhkYG=C*#pR;KduZ?2#EB^[bts=)VOA'6_krpC6P:#!9[(<eXa$3kBMhXj@ut*KN<j>dkt:6&k.i2")S&'>a8q
%Peh%)rD\B7VbcC0#!O:2a6Vj0$YnIjnLPgO,'t)pp_;,JGXm3N:GPio%q6^NiTNA9'>Is0e6k:9_[IU*:O"$ITb@iB*.3G++Zte!
%61YMj=-.f]+Mk?bE:aF19RB&3Sn*+:Z9:sW8Afrr!9sV6`\l0X:a!e3i<s6IkS0[qOLB_h*VBq`?W/*Q0GN#B=HC;=AISm6a[uZ9
%-MRX*#rJ;:d@S8o+bb%ke\Pc_8Eou?C3A@4!IN%`%F3\e>,T$1NNRBT4`+85J`/kHiBTI;1lIl1bniL:69>s)5_DC_*>.ST7r0SM
%RKl\P`^q=s@Tfd1RM:=!K%@ak24L$YBSra3cXK>FS_cuJM+3k6C+2kEkn_gsc<@,$gLe]J$T/u9L5ojB3q&BX[QoiMm]&Q]RL%*S
%"m5doD9s+W3ZHr7*upn)J)KeM7d(b<='["5Aca3ZJu1!*)l"\kR*uc%;eBF`?D-uV4$.H6K'"M>k'9W'2S?#nLF%hb0!LCECbk?W
%-"-@_]rdqE@5Mn@gLo'I%JZ5Y&%<0fe4rOs!irC)[(@1k<SB$\js)eLdGNgf[-tLQWV;V11aC,V:kKHFp)a@db2LMf[@f^@,4@e!
%]&G8MHJigHQ+M1,%2].=F92I5[XH0s$D^*660h?u,Or.I":N"7%lJ+35S"VI2i;mMk]!65'JRm&!T55%JA?I]R,LC;A,7301kUQE
%/+;i:1[WJ^fBi:F&gA)G%[&4@Cbc9Z=CjS1N/O*d7>iMP"cF3&fo]#VC@I-,"Z:#"M,VWh?:5=]&Tf4KVuST=4T#_a#m_OqS:#Ia
%5N:c2>*QtiMt!E%;L];6;T\;P+W:J*E>#4dlHXt1S;VpY*@Wj3<15j6IP.A10][uio=.4ECgKClWh-/W`LA@*@>\fc3)8C"$+9hr
%hFjM1#:)26o3aQ;fmnkd!>-3&aRDP$+!(]2Lsu1&_G_uGcpC%LWXQ]f#F3;Ge<pf3*#';h>QSN$TR`7d_'k4$8uo>6$)gaQPp_2b
%T#nrt2`qh^=^[Ph6S.I.;^hgiZE@T$%(3apc\90#97M-5:Dn,nMGXu<jZsS+<fp.qO"UUrdL!*WrlSYO9cR1YDj<"-1i3n-?)A_q
%57$1MeLLE0oRe;_7o?Kcnio/ALq=QRZ`@=sbjnVq0BTU[q!?rFS4'lh68hX3_nu(<e1r]WZYJVk6-CTA,M.plJI?GXb-\@bl*F_!
%e-0T/#'B,!('V-c&.l`]H3fTW\4Vu.TIT(F!mu;O,f8l1E&n=0)T'o^'^-g^)(.+-1`DuoUI#&;F0.b0B]jJ-Y\@A,"kGF5S__[#
%9b_Q$J;Q.a2d[f2/mMD\P$eH==?.8M$a,XKXpnJ<&a"OrR=7VJ''=6pVSSfXQZY"/BbN*U_T:n*,A7G6aQB9Q4K7(@`b;"Dl@=%r
%YW"-@Sm)>n_*AG]$Go_YZpX3qKWd(Xp%:#?KAh;be:ONV#Qc(.U3KGL6A[il]rbp"0_R%gBdDiWi/A(dX9*o_0OtrT,QfA27MuIu
%_Cm:C8V!1+=O^d<8the\(]__&F+fM7%Q>]LBcLN\,PZBn__q7kflJ\km*f@^5):Z"&`B]Bd#T.c\83<@Z^ZGdflsoQGJJEH]/nT`
%Tg$7^e9S2Lo7K'EeMWrB)=H#Y/roD,"GMD;$.<=Id'+_^m3i!E)Bs>PNbjDo1^b&Q%A'keZ"r;shEo9N2PF&\#U$Gb1>(j20JX*-
%;IGdnP-%'mk$YJp3#V)OHQcrf:tZCXG!2e*_lYuPW^EmLd-<RkX_*sN)J!CZ.p/&<Yqe#+)J$E&k<)!<3Y\g9%,(^J]+-[M_K"uG
%jBR8si0^5C2F%[_a*cWl$c59(*0G8*'9k:?n6kTCUi6^YbL8T#R;b#k:*Fj+1\7]XP?B(#Jt.H<#Z'U&%LfPeO$l9-5relg*+%f"
%T.)!%YJ]lEOOo0/&g._[-Z`7r%J/=$mmEia`On4:VRVdD$!gk+brt?-gH:!qK?;r^p2H0]bU*WZLEF>T#"mV5?u'<EMAIL>[d.
%IXd:'2MkVLSd+2@n(ta&qs'Z?h[/NO;`F,A=8&1VMXr)'E3T%E8eDU`8O+"2oP>])hSN.t]A-eCYV(5[B3Pm:8J35_%Q8L6D4PGL
%iYO!Cl)*`BCSmdH`Jo$Y1t2f3"1;%R7UbmV]ac>Nd*5r[%C">Ya#`8'KaF;X?)+i]Z<a+D!3JqnrS3ODPCUQDPmI!tfVVMA8YhKA
%p&S>%E&h`\@`K"5"/Eg>/N)bU@=(ZklcYDA/9bW!@1,[C`sk(HfMt:V)KO--;[1g@\RO1l@sZ:.O%P&5e=@9O]n2sT4+g&PH\G%(
%UNM?Ieu]MKcDeJ\0=qN_-%IIcY$>dM5#5F2:69H8*&[$b*bY't>suD-7nk!"5bGH?Y/hMjGJ]jS^Q^,EOFClhl@gg2C#r=YYkGu=
%(Tq$knr?!/\o6$?K'Cr8Q3TKS2OR1u.C$E<a;/,QP,E8G_7=*\=kpIm15@N!&[j=@1SEZ`cZthi[LJB4m\VAsC"t^g$$k5PEHc6+
%4(d1WE?E-tYL9"tC`iD],8h@3X<A:,mmmGorMu$>KOl%W\QG:$ecu.1R,hqm=!9^XMIbaYmo%gCRoN1P_3[)d"<N03LQHR"&#$[/
%1kmf##*!^)gm](2J`0S-"9ClXHm/8$!l,ZP70#%"IX\C0hd[$r#BfZHN`"-IVWSGX0%>+`&mctVc,*Bi/$>n/0,VWH[,`%I8]dt[
%_,Q^#_sD[)Ni,#1,*48Q3Jng0$u-S</.2&9e+m*$;!j7=!n>CgeC3(%3.TZ9NcZ\>Rb6ZFoV75Y-`dWBrc8jg7mVA,ggT]4WIb?2
%H%`lJ1aGTfb_7kkem^,2ZqjA*5liT"M(ChRN<M>IG7n&+H5[0Q5^*lE4=6&Aad&'u61C(8htZ'^`]BUljFL;jLQ7`e6:UH8\&aTF
%PL5,#5a;pKc;n>X#G6c,qTZM=[M_=hi`duPM?!jgN3@,<):PaI.9Uts(7XBPen%DD18]QS[h\;bLBcLpfnJ#.TtG.]MS%H5DBJEP
%C)X0A=EMJ.W)]:Z<c*FlSL:)\%("(sAgKQU"Bg]q.lW$[dK<D*`R<n-#o98OVFHW#-<3l-e=Cjme7W22!19\?p4'<2:hN@rE+eF*
%r8>CnVbi`n.);k5'FJmo"\kagVDJWrcTh(J$=_=']VblZcXtQPS,Q#=,E8NrReaCf`9HpQ7EOh!9M;,h.dHeP1gldU-_FCsW+]b!
%Pr'ptDE`%aDS:)$ohri5a8YscpNns`2sQQe]3:G[;eA,<&&V;mMh\e>,JfBpYC?U37>R/URWJC@o1lFjXYGk)-<1tUBD_T/OZQJI
%44ek28'h'nT(14G<eFZ#7<'(!8dQ/%6;!J!5qJOh4JA4^"EW/p",Eiq-]@6*:fKpmN'/hlWGmCS7AR-7!dhJUgc\UVE(%/tVFY+_
%,@4;`Kh'ZfAM:%"?B*k:Fap]O038'mC7I*_T&2YgRjIu?CWS!^(R"9Wq<lqV^r\b/1/IU#p2'8:3eEZYQC^CIRVU8*qcIIWnrXPA
%*d5sSh9Fkh.UbqsZqbe/=HXLP0O/dbi*#WGrqFqGGa!VnSI"[V9"b*X8lhQhr*FpmGNk`+rc2`nB"K;%NHc.2@Y;ch/,EksqM-m$
%5X&>@TA:TIEt;3mi2ko\*_OF#UZ?-X'h`Mb$*@>+MM;jS=mI92Wk>,2<IBVD<TJ_A&44<t4&@6&0ceeQ^dY?dW(E.=nSjYgp(Pst
%"^-3?=:*!?=k[%R>tY>X5JP(3FV(mF4@RY^9pqaPTZ0%/SZe;K^S&UBFo:#].$-)cr`h>[73Fj/]-h^>(G1]NEG3<ST7?]9Iu-(4
%q5=3f=Q9`eYYH1BQO;Yc@Z)tr+)60_gj]h0?NpE>K`iHqdBAG>V`ZB;Eu<-q.$&U=NM#c&?S>794eI0qq6+&`0\\6;]8ne,/'Ut`
%NIg5WU1)=L%]5[(3R%gH8H<b8Zk#Z"p.u2#PG]**6e[_)I5*!j+<TSYF0S/>%*4>U+[Str,`*,+`\E<J:SZE<.?+Y!H<J"!_kbch
%>2%M)+&Tsd#rS#E/pXXV5P#8K^RQ3tfm\YVD_T?Jb\m_Bq[A,6?oBC$`='oG'DWJbVFI)F1aCEBdE&)MBO7saj$^_##P!"[Do_51
%\<a@GmHGa##K;79XOV`tRja+=#^8'N^H0il@-e>K6PP*hBr`,,PJrn2"cj<"PN*rH^XJ-W[UE<&R&<`c"]b2n]2clnVU@A6bc"D'
%W.QRUo)$S)?^oEqqZCdZ;9Uif`QFgR`]5Shpuh;,A5ccSF,9t[r/rSEaTYUtP7]lE_Y5n,mOYid_)+a>R_<3kr\F/?SJG.Y]7;8N
%C@.RdT20W:pSFt(@G/'Z!dTJ5)JkTN\ok;]ZGip"&+prm51Lc5qU)``+o[;;]i,<,fZ2XbUn/lFa$=<[b?7`rHLl^JYmf^n7Z2n-
%+*ITgn"&<5p@j/cU$d$6j06#EWOE[#851-"rUe0,e-(`J?^%BH;Y9"4BSd^U*bD469U!G"Y5\"[1#F&NYj4*3`([Or=:EN#\-6'/
%_CF!4qsN%l]9@R07fT#)-QirV(886KfRbgTeY,2/SONc`V5rt'9"\\SIH#"EnFo94$1QZ&-!N(E2VuY_i!WQ,/tN#`RFGlSR=-o$
%E,0+7^c7E9N(cTQZ\)`d936t`Ls:loC$Xump;rTOkZ4r[N)1jU9H40f&HsZ#!%'PD<s.2t."npW#Lj2>X9%DqR(-H)(bBWW*`cA4
%=b7DA6OrD1.X5AO_Hc39jA\)KGnKHI^8Di&<5e35Y,iOH2F[6!CBiCqKI`nr"O__;fJU>Yk5'?mONm+;C\BOi+=j;b1=eO5'rNgV
%4Z3KWb)uILBB8rT@8"-eW2\oF3Cp[9`BrrKs,!>'Yg%SOAj<gAZoj*pQQ`fMD9?:ja<lUCdCTE]UgPCMTHSpoSIJPJS01';p+q>L
%?mC9r]a">W:6Y@m9G?K"YB4EB[!KfJ3,lLTQ.D[0kCiFa"#h3Q.R$_1#LW'e>*hO6WX:%WMbq"ee=;;6(7TWS%eC4tkWrI"['90/
%`m%(/H;k[9Poqn$k6<*ni=iC9!>?FUShB<0"01[>n>&:DE3g9"^-mBV7',q;[eO%2c7^Nu%!?;(5PKH<D!uedfTjmffEL?d_47n1
%&$+@.3lZ?cs'"t)krD7ZP&LL\lDmF%C<Yirm;:nR'Bc?.j<sm:,11WZk<X&"&''=fTc)"T%Hp@(M]O)]"l2.,Mc-h(rMtb%4$2^p
%U.9dfSp(=B;'bI&iI9-E5F*S@OeiarRdGopohd+^8nCekV2_0YQ5BWe@A27cWUYYkdft0,7:_MgIDBXk(tQ6^<C'g&;A*F#ibf&5
%'S!4rd[sCuIZ=1'Hm.t%]XUEE]g=q%nDW##Z+%$nJ?>qjNbj.?7.B<.'`sL/8')TF#q<-'`5$XW]!7Dm'fInRVm7=C,960n<dN%]
%Z\Pig1QcNP1N/p_k3-bbeDE\?UJupP6)E1t/RB]jpuNg]n]mjI:(l$k\EJR=TF&A$7t#Q^ZUDUXEADa&>^23K5>n67B$#dcTTp,<
%_K[S,S;hgIU'KeA7I!#)YKii+/#cuZ-=4Vq`t>K.NuUX;(gdD#VYoL/E=b:7B]^=H3!*fBPgZKW]9,$SF?7,To<-bt,;;&8l[F&k
%n#YZNfNK?Ipj=KIQLCZgrHkV:nVn7QN<Bpe\sPDtm"m6Zf<*&D7*1uDh@WkPW7UZ4oH"5=/(NWE%^\tg%Kq8,&nHe%OU-&2Obeo6
%%dY0q)<R]Pqsbc<0%FGCj]H'&MEO.-g><s&>X^N-,eaI_J1RFGitp!m"([?uTh(q&&]krFT_U!^((/"[T8(mpZA>YRQ8J`I7%2VV
%%]Zhb.')i(;$^H&d']f&Ytg]2P/K2%qJd@GdeE^Vi6i/4RZ/l0*d'?c=X'TBaF9KoraqDYHN2P&I[SdK`E&f%g!bhK=/3LuHYpH/
%9s.Qn`j=L9X$190rjc+k+JnanlIe;>OW&LXQb6?Z4[e#hnlHf#Cerp9LVC)G+*SB]r(I?$pG,?FCGbo<<M#007Zrqi+MBn+j'IqD
%TZ2=(VgCn&?iTO@[BDIeT3(%eRm(FVlnRaG^-k\Z4!r&9WGsL=/t%qoPOY.EG>*EHVRkU0UIZ_'c$hEZ<-4jTW]9cM?H(;WfhIRK
%.D+SRcE;E+%!do!6+AAeU*b/?'kqq/2;N>ZUsF=OH*O:)CfV$HUhc[DSf#$dLRsLRP@8bm9s;gjCM_k]4FOsf3c@Vni<RDfdo/<T
%S[(DIaAPo'NVga64d`/B_;g`&'3Kaf,-UHXI+b9$>bd]+%th(XDD3;MK]AGd;3-MSF##6-?;UA1F*t3*7a4^g*sTEth@C3W;uGVp
%]iZS7q`uq0N30kL*!+l:\utY8;Vh]Ma]gZ)b>Jp'a5EIpd#NZ8N\kc$B<JA)$ft;O%!q%u1XDJ7NU[nE)P,(4.3\VZ[rn7FFl4Qe
%VH-gZ`^*Y<A*+Xb[odX-O[FrZC;h-8GetLLf]<)e2`L&OGJ-5Jb&&^@C]&O^j_O1+pX$<$\0B%\g*HF;3dX6Jc/13e\7'#9590Yj
%R5VX?T]]Q3;]O'8<d'O(mW$B47N'c6A[^SuXV!F=9fG.g@9h4?Om_Pc$k!Kppq3&d*6^)Xc(Jit=NRUrh"rRUR<7gMpM4Ph3m)la
%HCH2#+4ou8KfL460Ws3EI*:"B=Q;L/FuR2,I(7),@YaU`/$eQFfC`Ia?E7./.a6/NSbb(AoZ@N+K-;;FlTT&phK'+bkHDre8tLp8
%ZLs%@J'?1_mq[*:(sE0U1Y$&NS.7+F(b:#tC*b5<#(5I=?B`k8I+sr5YKe-_1,]&d-)^KE2E/<>87Jrnqto<CpO9&)B.$!XR8e#[
%F7o`"A&:#nc6E@N_%P/he3%u]OJ_2tQ?7Juin$l<$'RSJkF%0.(mqliqDNu^Y)71R*\_;EILtCeW=NF$-GF14$GR21XpT+t_N@K"
%#b2kI:fIag*6#G2Xr'))E?'WkZMr%_qM"Htn?t?JB>P<A$dh_8FSHt-!m]!^6qmQL.8@gh8G_9fk=a?6jA091p5nKU4@uPiPHRpE
%h'r=!OORf7g2o9uMY6,h3Y7HBJF`so&6FHK)j6L[qN=.o*ttmIcPB5HIWnfQ00:tEk6iC0i&<++pL[#^[n.'K\2(MGdRg(6NbYg6
%4=8^D&XD;OFfMRe.Ws^;\8Z(CD);?d6Xp/h@ljQs6j<#De!O#tYL"AhNrFR6m;$%u+6f!/<C3V*9qZ"N`P1ptWdS.H\\W:J#'c%$
%.Zeu=#WlOtC>GL2pi[T?3>sa+C"msn2DpkGB=B0'+_,Ccj47i]RJ;Qdl43EE'C8^Q*b)I8dT[B\I5TX!\9mQfG<6]qb=UF26?@oB
%!RI)TU%D(A"jRBLo)MkcJ]=44k'Nio1Q83+FI7b[SY+8'7B8.>:jS[1@`Dn)5)]e&n^%qDLNt!CRZ)+X#2bE6GMaoj)[P$hj0?V2
%dZ7#V-4,I2-?&e\WS=".\NbAopJ9W=oZ>YED>VGL+&(N<.2D;ChmQ;:L9;;4Z9/h'q/pMgNpWa)mIs/].&ggnfZ7*,j-Le>N+h*q
%;D*Dj_F64(IDWLg4*7Us@OPA^a-PD@Fnj#&WH0T\&>QZppMHdjWk=PKCsHhi+/5iciIfW.hfmc"Z`U6?*jTUgdb/\XZO1_3.@lZi
%$DPG*B@2Y,&-q<lR1NfDWnA<hPZ*\I<L=LPWpXHJ5#Oje8,7F,kX9Dh"c>WujJcBPOlRL:NK0g4;i'MqTg\(A1_M=aG(p?r-Bq67
%csp5I9^PcT%n$jg#O#h%#sBQ-HO<:?r9,6XnIAGLjnX/ELQAL#3.?1_qFj&2X)-VjPbXg1HPt*gcihgVniKB[T74e2S8VeJ=S1&O
%)QDK1,-*T$qFlruZrh'OB?&)YOTePd3ZHPlcAW7u9Phf:Cq3N7AP::bc30n4!DsjNUo9rmjTfRQdaKaWRZCDK(O@!Z+nGu5I#oJN
%*_<-$<'a<C0h878)5A%]1>qV_/UBdF-uu-D768/c0V3/Idok&:#+5R16q(P*q-E%iQ"'IA_o`A1Krqn%eSRYkI5GX5m@Kbqc$9(k
%P*9(RBLa2s*t>9'gD;d.nH?YT?`h*3G'm"jnmDS^#LL`tV"=T*k8g05hs&UN<pOY:*j+)mIYp`4laHd6"#f:H/U*CS]75%M1GY!+
%BLs;Jlgnf^1A*]XQnIW020Mb)<<[Ki)EsXj5h1N+B;;9&R:FP3H&idO)0_B&grtq5p0XB&0GHYP8l'n6%%a3l("KLD]9Ac5F>OE?
%U?`Lfhq&_*ZL2(0Ta+^1W2^Gl3hrQAK_ZMiAXFi33B[Qsr]L>g^ZHX<9?TO`Mo12QXG$UHK(<u9k2U:NS?'BPbi0W8I)=\p"<4X$
%0&(Z6H=l6G'-iQi[*Uq`RS0(XG>R=*5<i%d95Auj0.-XHRUdt<XB]\G00'kI&0#BN;hNtTdc=Pi]3Nk9Vk?D.8QH/$Yqme\V&e1+
%l2'%YF+&m@^sX$6&g087(i#R:'OICRXH6-cp!Mg_egC]<LVmFTjDXBF*1Ncoe7;"Gh->t%fc9f!q?_MK&ORI&eB8[N3[V0d6`EFe
%1f(N$eFFIJ1e(N,EDg9Le_o?h5!Lg80bg9>H@1\YiQ%bfQBWJ+bYo3X*D.9nOQ:FA3H0oW(SDeuW-aVl&OJ7i<ehfrZ(m(Ddu-u6
%@biAJo%D;kCs/&'s(Y;5W::^clS..'In9aBFrecA43G(nW!ooa;uSPVSe=q;-P8_n;i$p5b]t@K1^liiG*?lIZ+<+0VItLH8HID!
%1sLY45%YIO<?jEl6Zm+'-NpL<)<kBR1u^2]j_X]+^X>E/.p#['kb)QDF,LNCEd_8f/n\n&bJ&=Dj-mhtA?SiO>n:&t3nLe5DlF5?
%(m/TFgZC)bFE=VZ?UT@m5?j,J'0,G=(mHUuAW:``@UTrsZ_(BARpT)ioUO*XU*XX32R10;ff$%rfX)4b8'7ooPun@)odH7$Z19AI
%RN<WW5$hPQ?Is6Zj@q(m;Kk_P$ZXKN_uXZ,->hG5FuaIJW"i.hAL]d@;0N_FD5)B(XnTEWBqb-$U&D@Fj!Jj/S@^Q"=krUoTo8!1
%S?m*Ik,k4fZC_uF4u:]nMaqC-71r;dLtOLpG!7KWcO)h)2=3Xa@O:h)gIr!&7d-Wg*N)#Rle'm>/uY<I$?Gq7ZXG^0YA6_ceooSP
%5a'7*cLDPGB[Vo1h+0Du'3W;s8fn]/J8%BP$aD%#U?HOFK"@N5BJJIUS5L,<7Mjk0g;0sCZ($4lHbmI,NG_UlMj#3(,$T5(;j<Kr
%Z^f^:.6ah*o[lH)VW]D&S4TdC87)HSR!hQB1C@sf*;N&d0T,6H%:$)/"C3h:BH<qTND)?=G&ZT5R$>"\DFCq)-VN>SSEh4C4jp<u
%lWnJ!=8G*;q^/_0,\">#$01EbqUp+KIbdih:=eauT'`>uqS^SL:6O>pHd$e!SG%BAXR3-"3LPSE-eF^-VBf[&Q#&P)QMJ;(bkSTc
%r@<V$7667.>_2G$In&R.nUC=F"lJ(OW?*!u+^ePHdkjR"8A)nQ8N"LkN0+mB!Iu[t()!*TqPqM9.""pdP7"ErCTYSJee;i%!mL3>
%UqPZ<H=&-\8`U(r=d:g]!oP75Vl,=U50pa]M8go'do\b"lj5/9M]UtRA&R'lRDsf&B;Prd7aV5a6M[+C<5d_f.4K"dkpN7).<U%k
%b3I1aJJ*%g-OOWdB:=PgWFMW[heI=5o2XR+H!/D&oBFG@aFo#0@U>*BgJWP-66.WRojV>Bb7k,\Yq'<F`GP06Ofr0rbN/?Ika(Z8
%5+&]@a2dUm^sV*Bg><_plce]Ta3+*hg$;6.`4u^;p1/Wa0s`ngY+Buchg(Q8B3gCK&8LPZ?*;J_PRJ7A+5!),7Qk3Nbt_\rl#-Bg
%ZY&k]=Zo^7Qn>AR`$.k-/@qZY9o")kZIYGL:7M/`PkW$GbMCn-D:!RNOu;@A)D'XDN*$4>he'j=1C^]eQbBc%gC.1!A5(O>YN3RS
%fO)\N=$4`/4O$qibu[$P=$4`/4O$qibu[$P=$4`/4O$qibu[$P=$4`/4O"[hIDsZ_`RB4RdsQV(?h#s[nMFVC2##pB:-3]hHP&,3
%@iPthl'Ro&<6Z=_ZUNFjnTBf![Fr-g#b)E9UF7)#fdP9</8_79WO8j<DA762Z`sf(7ZfVq#'r$%%I6rc-&^:-81#t<R@uq93*mrV
%HtoVBZu7^Pjg7iNk_pmCl5?:ag\rBEJa0e<HG4#-m#E,JAB,1r:97L1DX#ms2jC0JpKe@AZ97jtZhCY(\U-WR`rfn(@/^V?qUkm[
%@ugHH@/GS3P[NMH,`cgHFHZs(>6@Z"B'h=frHN_Clp4aEW*sIWm4H["gN7<$p]j8!_*dBqb_?A.J=moRo%f?ERnp)*glurQB#?hF
%o&eo;0<US2F.u>X^:-3;Od0"(XF:Mr^N2U;Y;P2.l<1M0mBsBBdSX)0?8UD#]9R9@KW6&r>=GI)kR<fq.e/MXa2TAa-a=SMVoG5S
%bIsu&3bLoWT!?gW-dcbVVK)Qb7q</m1cl\_B?Qo+h"AfsT7<Os_JGuDA)*<NHns-u^![8W;Nk=.5.sIdBtK'ZT3?D&V<l\]'%6sE
%ZS6h%T_cT6E;.&.IFlhoSe-/DH"XTA\MN=%p6`hpE5cYug\mT$?4H3eR(cniI-ljugoG-Zo@Q>8hM2qQ;tQ*f@<c=(hjtLoF)pdD
%"Q@$iW5d@:_](8WLcX<Y,Hf1B#b;QcfDJEk.)qrOB(Ul%@9'Ja"NYAf?Ufsi)F&M\F:6#s^N"a3cXQ^/P.-,r3=hGUg6Rf'21D[t
%CQ2]8`_XMf5DUip4a*;5oM3%`1V.gb04#f\%:`)cqgQuNh;%FU2BMA-MDuA5rmN7\ZPte_hWHHY;tAh:7'<1Y%]N^fOR@0\jsp*)
%?thg.TAH53jZ2N$2X>]']^B+shp(s+F>WZ8L;WijLPPH8.q7(M^_8oiH_03#H\-l]Jfes7*TR"3)%K"lpmlg%jt5is'#cY44i:]k
%YW("a+S4E=L;X#d/[P,?XQiXB^"MArk;PMNHGf$lQ1'(nb15kI&*Jau'J_gX7AaG.V@t``Y_,^._l%Dbl8oi*=:H:KY,gV%<kY9Y
%8&Y7[=bkN126N)-,C7&,ZL``W+&M;;OrXn20=QRIT5j6S\p[&XO68^/Z0jF-I>`IpdI1ibr.'O"1EQGZ]I@&b@ln*Ho]cWD^W"T]
%-1$pW(-=He`n\ld?S,hn-n4><eA#&<,KaFS&aTF:I!gJ`f9$-,=f/Mc^;t*,IpRDCY4>/h%JN7L:i&LdO;s=>E4?\F!'N0EPg/IN
%lY)(Hq;=>3%oD;6;L"`$1)'Fq1>U1dO9q-2k7bf.cOfR,30hC]Zk[JW9phY8EaftR0um-nZ#dqJ&iO=i[Il"13g*k]BY!0$S>%di
%ARIuLEqrhG^='\cNkHXkV:[Boas4!KFuJM["s;4'gh1E9?MM1Z03Q`ahu4h]0fABhA91tGD[B4!!Bh^unM.0AN.bbV&J'V!DDSAr
%'(FP@(7hKf8)AW*4d,=/(*d$-r,,./c&9=F8eb04=rNuD',kP#Ptng)(Y;OC:KPP(_4OpHT@O+f-,tODDCt]WeV/t.H%Ru:8!cEB
%*Z,YoOQ4\>T3)-OrR:X#Q7k!MZ$c8dK]S,N8m"8e6sTT4&V19QCIIYMT0FGtbC<XRlhlHtC!(NmP93\0YN>=HVHd?'[qC08SouJ/
%a`R%C#[?`4jj>VV#/Kdq6E!Z_57r],e2SOD2l_2[=HPNHMo2@-%7U^t8E27SGZ;\kLXqH^$T2.1,tX=F!r8ol(XiD.=$q>d"q3N5
%%Kt@<O1H?mU-'!#D%snb91,AT)`>'YNVO^O&M.X.%WSA->NMpOi5$5PJS=sl2a2u5hN?#!5rbhhH3qrc6qCru@l/53M%^&+:7i(g
%4BDXJoi$nslkSTBq95W*FQQFZE^kXBAHG*hF0,#PUZ%ToVbcBlUWMFCk0AG0dG8nLXe&p#jGY;e1t1\Y^l-.`Buf9!fue'c\koc_
%SXcL`X<]o_G3M"\IaE*Pqus,8r_IS*<jE:&8he>%!A#TShjt-`FuMMuZrqu]HB8K'FOLegdY>*>`^#d3E('0CT:PU3%PUHi?;g"r
%T[D[j_8`l)g$9pQGW`a!dS^)F>k:uNrZ^Ea\E[ONPp(^pN+^h:iX?XTPMhIE(7Z:s:X!+h/A/%m_eL7<Rj3@ma(h<nC+MOX7tmNc
%kZLhnTGac-`_^.ZEa:ajPgX,6if((Rkqc[65UQ_g.H"on1?nU>9`Rb[>=T\q=l;1(eraoH3Q"WgDWJTj3sk%;-Oto4SA]R1jK,K+
%@20JU_-F$[k^LCQ1.Njcce$%_[;^gH$Jn9lko&(,Gd>m(<Y.fL'J:QF&eqL0U&#L93Z_IQ#P0qq%F>p;X)cpYj/O)B$qb,4Jf<6q
%V1$h@:?mf:`#3Z[iG;WM)O8lf1QEDAdU5<a0P]:.Nb3@[g-i=75ZGu>,f-jcQ-j5&nBAnB3>KO\"\):;YqiSCVN][Q17C%3n>[u6
%,CFV:\IFYN$"c-5;g[`2_EK[`SA>+^!>fgE`>DG!""/DF"?UoDI\]di$>(stO90_BDK@M9MLn8VO?Q4nY+s%q@/.&26QF3aga".s
%K-Cq&q`r'?Tb2JMQTkd=^9,Z_dkD(7PQ7<$_%IY=Tcq-5aB(_UHI,%jS*5D:G*\AgMpMH]0na!AU0*-Jc=%Ef5RQ0$pOXjf_^V_n
%!"U6g+P#7k_UKq9!Q1q%0OUGGk4%T;O?&8.$t1!:Jr\dt8]GS;'%F&#(X=pn7giS#6eK8G%TJUBaJj(rYV7C+),l-)PCh(g$:BUN
%8NC::6Bi)V*2#QTk!E6'-O;ks"2/RR'.cj+*OL85!YPu+?0F*UF=,cP+UEKSn9glH+Ykk"$4REqd-rq>(C<,]L=r7DidH8$$31OP
%q%r#Z3`#TG)$@m*aS:!(R0AQPs(%/T[V!\sS+CGE#oSf4`]&fkfs):4\WYDN*BH)^?G>%Cf]3-AE4Nf?anZS>9rSE%-=^)!T'SEX
%&(X46'n!qiDkkk&mhna49RRG=JUm<\^O?.Mk9"$FqtU$Dn")Z]bsUM=:S7RPn*.1$T,g.Dp[,`pr5H^;Y<KUGqW*E]qs'5$o%/lu
%dF74>JC_MCrUK-9n*l=#PKn[N-iE:[^q9UMZp3EcS"JR8j$,1UhnV1"olj3(4KE/UU]939?SHsm4D9U2c\^`2D/>-F8uF=*TB_$C
%hVjHDL>u8&*-AQ]jl0^S@0a^-!A.u]q'D.hZ$<Rd3B[;2qA8SKDLV6[2V.!PVba^3'j:Up*ZJ>>/M&\B1^Y#aI<;IJq+sL-rO,X/
%]>_0AhQ,2]g0bu)lb#iV.\:QomIu4#f36%'7%X9ofCX$O@6nh(j;M=hH%)UhSQiRJRtIo6>IQl@@f'QbkYfb'ecU>ACCelB\a?NT
%/IGJ3>?mpr4e#Z"qPM1%/KU1hmI54%dpC($UJ!b>%e'&7XkPF,(UplV[ir]W9Y*i_H8`8#FL_!NI>>j)T)3`4bZY6TdC/4l^?Na[
%S#a`'Ss,*,d50Q,DVK=FI=_&U4sRQ.3dmq3kVY!ed]o>tDsOr'n,;`[jC[1s^*%Jh_tU\@4P\Jp[]+2<:[K(c\OQ&\r)Q%^@EHDg
%jOWu^L$Z6JFo'mh?I(KTY=EK$DMsEmqpakR61<0tW_T1UeEaY7h`Nd#XX3i2Xi#"--+:L0VpDj9oj"81DXE"YLV1=.#6CKpmB&Hu
%^.#6g)LZ[B?un3lF?&WUV<i;KVpDj9oj"81D@LV]EHQ><#?r#2iS,*rl16KQ4kJo`>J#3N62'-k]%f3*I(-TQoQejYHEPXS[k>-<
%9Q+j?Dpb5`]lg7!\*3KiUMi15Qo0^!gTbY*4t^D.et#!KCp3"?ICf)YrUT3:?]'pOK'=gq5"bJ>?U46Ora4,Drq+G(]@;+chQ(n8
%Xd(20g],!h8@h3SN8&@l5p;GI=T!$Z.]guY,Q_1j3O:EhE9SMXfj9i(E>;Tl9<J<X[>S2L`MMP4%\>-=m9i2-.msO1-Mbm?($p!J
%C2b;hTt?ZR!q<")_o1ba,)"^.Z60e9XU"N`>FQ#U9A7acJ#`hm&bK.>0^QcO*jJ,]I,INLoY\jdbCld8aYgfM;Q:(N:g#8SmZ8)@
%#B&in;ru0[X%h<Xn<HfPa>HP]&pi9<YRs,nK;%J+oN<IIFBD*DB[#S0/d:Bse,V')c'CHA2=@@G0?Hp6Hi+dH7?]6[G#?2uTh=he
%QRDrA!k6;/VOQE)Iqe50GC^I<:LPY,/F9jgU<ufRFAU*6bo<FAg&atZ0ZW'!5ADp0QjV`,HM+0/2obDrG/a](:dDYr=[uU9Ch'.%
%,!EhIai<o0`X?R6^gB?$QAuEW7OD"`FfNmf/KfLNbpVmqi'37jfUT'HTVFA3kW"3R*dK%t4gE2_5[VqPR-n51R?Nt0NtZH4G<q$X
%F+K2$,qghN\isUCT5fGZ0=4-/?:+#NKWKdjJS-X,mMFm%5S"-kOnf+UNY(.f+o31DLs8"q%8/,%=.mo/jO;FtFeE#2CGrj.kQ1[I
%Q=*p.Elt4lpCU<sg)VVh*J4O9/rAjU-l##AXbl?0*=V"M>J2a=Y(q\;k;rE3Ukso`NSIH7JIaMD98lT$d.'Hhm==X1P!CK("PNNq
%@j\ogE8P\Z5?M!IeeQ[9*LSu02ME[q&/AY__61n]KMb]X@cgZu/?61i&5XL30b:NlboS4]igB.+mh(TlW64e+DdDVC@hp,?MD->_
%r/M#=(Kk6cZn*>6F7VX@J^9UT!'Mo36OjEI/hn\pD[E\cK*sDG]eTcN\O(4+eoV<hDjPO.j[FeQ`2LW4(8l#"re)HVXs:7W5;J]*
%A29lZN+Yg9]S6L&lTB;5J["(0=0c^"/H=Xa0M_oHfs9di>[VR:pL1eo8enqhMfh`/BPrYn">NE&0eGG\6mot_oSa_L'4[bb!JniP
%<sKr92DQX(/@`EDlIa#A@O\Uh/@!=4K*^UN#F8X(*Xk$@[/kG_:e%sE?*eghjgq4eP;@.>$]Ahu>PcE&g4I#2bQm?g73gGei.:)H
%JI!HkB-N-aZn>4VPa^S)aM(Dqa!D>hJ^)>]9-2N6pTfX0Ul,oO8O9P@SUmR$Ql!$FJ/grB*qW6pP1Y\?WBl?5P7!GLl:KMaT,'.B
%NR&HO?#4V_DH(MI;coUm.5#a(4K'0hZql3S\0[!^CEY#NB&!eiWm;sJ`10gh42C/f&[<9rV@G1dp<c\ai73NLkX?gVcgqBt;8bNF
%#%1q&l&JLIN<T&a[M7N]EtUNiW3+t)B.o]U_0K^9kCVA>Q7PD\K(C00B&A8D7-L\q+&=@<["X2M*4ujZ-nHNU-.B@K`^n%GP&$\X
%<U+K^Vqm>-@p3Xr^`nhh7[egE1#*=-!e-F2I)qo3&6rJ2D,W^'$S55fhUc53!*]i:@\O[+)LWr6jLW9dBq&O?6N53q)(-.4Bk&H,
%\&>"m0[e@#`,mi70Td\&nOj,0QIauh=/*mO##\E>UM27gi)7V`U.RQSi(AZJ.R`nZ][YfV6pTNDWU^g[dkUF4TYG$o=?tDX92o[F
%DK[[5"VfgKlaAFg.0dm9FEe[ak-EV`9/EG=FL"mh]eEC)_94)9!n#S!K(W%D':3Cu$Kr\p+[&dP(J!'7JJ`O#5RiVb,&L6/#+3/f
%'a>S'-C0`^M_N_71,,p!Kpf%4]2GE(.c#-Vjh/HpM)hNWK;sd,WcVZ^auRCtgG3M4BbA&cWao%;M5'jCS9V:N4bYF\R>0lB)3(sW
%9jh=7M!1+6<X"\^BamKi,Vk9jXs((fag]1eB-Ib,)OQuCJ.`_+(JP$tB@QY%P4UtT<q0e2+&9Cc_$#E3;6KYrW<T]MH6=#^/#"UV
%'G1GjI0l,r&Oo$7&7I8W'"^EYls`f@MUVWsO[N)a,%SRFKgr;hA3O-*Ojl:^0;-/'m@nZr6rhijLfCc>BgZ3qnjKEZp.h]7&HU?8
%gZ%Y@]&[0*h[9ll[XP^a@lbGuY?k6@D<>b:lHW$60*2DlW`K1-U)(VM'qX\=T?EKBYNDjC"mD=eT';Vo?7mT!'.Fm>jf13-mKh.E
%3"ldP%SuEd@Uf0rVQs*m>-bCe&b]"?YL.s0)TW"I:U-/BDg!5u:W$AZM7-OM5VehD<B<2/q8!_:MU,r[a2[e.b.prbjt(?jk'=il
%i5#kM"kTt<<P;R<MH25@-F,NP2#s2l06hK%3<Qs^Re.ul_+^Y)c:$Gc?>9q`W9Xre2SM/WIu9!f,9Zc,`G#AOO?n-@)E.Qc`juZ@
%1m[2P8HD,hcJq)@DCSP+T<Wa!fi65?H2^*T=pe-jb[?8^GW'480kf0!Rg)]+'tp(#XArZQ54P9sp,HaT.S@ppaYGTe<.(YS%a@)4
%PH0b$\_,%kjl0';T%Z4.Sl)j#GXqW(>NNHMR*$TW'6U<VQL1'Oipf3#ljT4=?$Z`31?k,UO#Jm*'HnVhho&D^>gSj%a?[0A$@bQu
%;j81cE="r76!9eIKZTSW@$k8hkqk\MTD$kp1LQKE/G5))pf?VsD,rL@flBF]V)Ai9MphL,r(7`g!I=EMYYkGAOm-Bpi#9Z\6U]io
%7>T'J_$6q4V$3upW"]aYP<k#*"Z@hW/%3P3&VTML`*s'liI+#U;7@3H1NN"P!Y\5<Q*so`DR\9(h"4COOJ.OE8T]U$EhRR_=f<&=
%4a9KJ6rnXTkeoE]fSZI2h]qrOM4f!kP(C2I&:1EPNE%SsLQOGI'[a5Vc7Lc56s6Q60*U+mBR$i>Y40E/Pr,VZiIi@`5q@,^!Cr$;
%'8StQV49ta1tJWhDpo6*pS4_tC4f.'[<>%TW8O[a/?C@KZ;s&Q]0UC06:A2o8A0Yol.P.;"rFri-CSis\JOFt&490GM,i.GdE,//
%D(?fMU8$t,8/=L*(]oe$E_!!72(7:KOUJin^h]<.(:7^2#R^tqgfi;=nXKN'1NXY=?4lMhdUuW>X#<^jf%=qt@ME$f;[M=IVJp+a
%.pm&S]9^FT7d:WP[/lM?-Bc*b7$n4R9%n_coXn^K")j1c\ar,Ri^%n.Mnm=7D0$*0d8b&gMn.[154G2G0+uS9&on0nN5Ht+,1BQf
%_RZ4(iTWlP(:RCY3BV`X[\1W@#VcmKiJ>^p66o@7N,*E-Wl;qe2(WE-(FsGS;F=T]iu1$pl-a@4V_TZ*fmG5n0k_M26d_TKV,4#(
%70?bp&U^hPPXg4"1i(U<GK*3BbYjD][f.88F-C\N)<[@mRN`=GZ`&B#Mb3hYWtbtA9[):3B_WAu)ljENLbo9O]Mi=kjgrL4Y\Te8
%$=\oZ7qa4Q*k>s`ejfm6V^^Y_<B>fA20KS))=C:%SpKDLBTZpL(eWq/odcifY^NcGj`V*QMMXC=E@Lc-S/(`\Q%uV!SjjOQ;9S(\
%5][NQLl9t5F6!3BEkduIP2l_Z_TBcR@2ebJnWc^7d#6,)c;#!4/c"X'o#@9a8,i1cY#9!k9i4G>e]]1IbmU)n0@*5BIV8a-dr^\^
%k*BQ^VI_GbKr$hu^l-g&Bu=Q1h[.m>jXr:rK\!Yq4d5$FoYi=6-Y8"3<S%]mP:RKtrEa4Je^l?01pLEgf+7dmJHqA8+ZSa_e5ak+
%p@:iUn3m,5rimc#l6*45/-ZnXHW)6F*!W9uit*kURC4s'`La=<ZRt0O,)NAT?J3>U:9VVJHYC$)m%sc/MeY<U4su4A0b,PAbdJFs
%H'LGCC.]%s2)MQ?_NVthrggJW[?LB^MR[<%))Ma>1m5:.+gQY7[@5)R.9`T:Ldr&NhnOu#)Jt,F>D[hYZ@S&55dlN<=;Nbt&]boc
%1"c+$XXJ64A/=m,85*;gR53EPFF6o$P!4E@fG0OT^%dLKqTN%DK?XT^`nCu&<EW[V*b`Jq](-cH@BqP\EoG6QN)ldprQoebR-<4E
%K9Eh>,HfcJ=/KB..C</f2)5d=JVgc4M<2jY5uA(R$VMs&eQs_S@It/q`Bj/Z<:25STK#`d&4A[!G$%W#d;uNk]eieJF%(X(!&6+k
%pnfdN(+d"qDa6ED..e7ffW+Tqa"\0kO)C6.7;rW+G&+*$.iL9K%<3PnX,.7&Mun6UR,A$<*l>`+=b0+"cc3es=s-i^\k%OQCC7`]
%/cim-2Q$OPm@QY&T(,X>A\seVEW\D(W51cDMH$,A848ZW0(JMFjPrJhf&k+!E!J/7.FF-n5M0igoO:L-+<liI=0,Pt.4?@E=V\Ob
%jVsCe?s1[l=]q8)l'@qC/JljKCrASk#g2<W_Q.fV^reFTRG=UjL1o,c,3@D8cI[7X[e1eL/4d9H'j^k@c(V^o3N#"HXM=FGV_)6=
%,3hVII:9"<)UssPK9N3P[11s+#bk.T\n`Lm?0cYUU=:/KQ;W9Vd3V%)0+eQ)/gBjVp6d8Q.\tg(eKk4e^,bT&g8`qd*,N(CBT!Gf
%%mpJ=Gr&B:WbV,LC.VGhiKJ)sO[E.X4cAaFhW_u^q3BNgWo4d+@$;sKjX5@9m.SNG4EPQ3f&jVc8aK#j=doN$gX8Y2;5,"d^"]:u
%N^g5j&TVnC9N5XakseBm*t%GU,(.i<;'68=6Xb3E&d@6HgoiE'7kE;`eRnON@CDDpbZ[/B$U[\ab.u.SYkp18!g0B4io!Yn.]U.\
%LbHXL[U=G<GG?6h^b]DI6/%E1e8?)l8on;0o7d_j]<QK?=YtA^E:Yen)i1',ZlgE'cnffR]?p2492jY$eKR([m;N/T3jC:qo?(SC
%n4LV^1+)[SWAak.1EV?W<pX@>]NFQbC1a5UCPf?l(Jok`*f$Z,aMl]R'!A6QMmR;<D5pTZqG[o[juI(5m.@=B7(22S5IASV&=&iO
%V0`<I?L)bh)WQ,e_l43*&O@PFDV$I_l!oDC]bA:1c'W-YL/A%eMP5*'g7$[<W:FK781WpHXVPIpWA_)a?ilXEhpAg8RrVsW(s-Vt
%PUpjV<cM=Tp;jg.M:"2Kl@`1[Ogbq9=*7HE9s)[N[\;G=)b&+AR[iql@P-@q53cs.9+hKnJpX#1]4,J[#DIDD<"&4,WkOa_A.HLD
%FOKQ\iJC6"VB]sqm-r_CW'^1e0dc.W<#697STn!VO0WkGFW1;/@@8NoMXq2k8P39s0&gjn0:f6R-VbO_ERsLk`^1Jo,\kb,VG=2J
%G_qn"a$KcgUse,e,."=P5`2I@WE'.F!aVgn8XL"RFP,/4J8NPM1bMb2Ws,:'lZA8NE!EdZp)@87e-6Ro(RqBPNHs,;h5)TmRmH?6
%^8^7Y(NhjDPW_KNrpo<;G4WTnh>HR<274"0'POs(KsruK%1k<9,*,Xg>H\_$&P_>!&#b1WE'pC(LdE=X!/NGJGB,c7JeU&Wa>-61
%jTQ5T?sSn?$#1)fa@S=R,q)a8e.LKcSNInu?.QFf_-^Aei&2]a7Slsd387nh!"2mte1[$*`3*2RP#PF2-#ni)9lMVC/=LTjCSh^F
%SSVD[[pf@P:#iqp>Y?Z^/o-CJ5$I0KiB4ocFGua+#S=-bq_X^QMP0<F2OFo@V0IsG?fP.N(Y8so)q\6ZU5&:'4>D5OCo@W]M^HT)
%MN4>nnRWhVS"7]uNKQ"g6EUZEcP>$oEHbQc1`2[S(i`[*@Sdn(`4.qs@d?VXn0A$%`^Nq;ZT`<o3\_/^_Vem>RgA"k;.!+5[^+8-
%(S].I'Ujl8bCIL4ipo?Z03^n\hqjZrO:K2<,\&07Bk;Z*i^F7H5(rGaB/Kf<_Bu?A"B4]Rb('Nd(+k-iYDSad85d:Z+>GbDD%T/$
%2+0<hKZ/<n^qdoIZD:6+jei:um]D5`VK$.S@Uhl[F]KJOa9I`%Mb83sUTo7r:5o]n1jGd;balcN'5[j"Bn^hW1ler!X@6tF6o6`>
%b:`*@hF8D7G]P^YH$*M?`ou!-ESS6^k/:)mRKL!l7BrL'&>Y^;`/W>;I#>!ZNpPGkJ/8IPiTusJU3\MM.9IN7HieLOo@Tlj5$G-i
%P&6(ff+%ku.Hr[rZ:5VH=%$]\fj`#*@%U*C8IZ<,"QC'8:PeB&_6(g4V(W4]+jfX&*ff@59-9=BO%Bu2KHTP>6?#SWZ7q3\IpB+g
%]+*=_k7k1\.j\fZQT"KAd&S(sSa,f+s&HEmRkIQ";<j+RON!*T](0Ce=]3d&:DtjifT[^pBUpbLK@HU6)^e4W,7S[Fj)@_YBU]17
%Ua<BNX?dV'OuT5[?jemj2U8RBrPMg^1*V07iE`ZBH[9BMI'b;V;H7cNP9Xr--7udnU]s`A3_tedIgin)k5O@P;32>&Z^?1^A'h=e
%?@kX5=%!THeHD"I?tL#1&."'9Z7Zq&'^*5u/;`R[U36:):W9'nI*%:e&IEp%#8ZBlB4]5o6(S%Sco0V_"W-%>%>!pd#5/bJi=>ie
%]sogeSVb_DZB6X-(ek,G5gWV725cDS_\D[XY'@%V(6o$/e'1"N,-@\LO1q$gdTsg9_jpmEJS8AKUu8p@Wg,Km_3Y[1M5Vs.JpjDh
%WV->Mh1\^<jM(2jKa`!VJf?ha2U/u[:gmPIZG._,:CM(/<BY[14@FGrX#4T_nA?ZeAnLZLc'UmaUeh7B`i:n,`2oBF[P45XFDW'>
%L/lopK$2NQa>Omrr\:n]'VVh%^iu']A+5uiH(##6$\PD1$m*h>WC4LWbpKosN'M!I'*"WbR@REE6X>BYai*!VP;ZN_8SA1M`qp*t
%lj2K&8Zq>,QeEfjdqCW*8XtQJa@QdiU(@NM>;tCS8pB6h6-BrHdRqc$*SCg"RTVN'-j-HngJn^8^oaHn(3,RAPHLL":g'>L#ZG&W
%er;ttk(p>0%Sp^q]S/NJ8p!PhgSRCR%H7l/Km?iG3tZf>4p;1^<BnaYAlrHr!55CdiiKq5=3/HPJ(n^aj85E7TlB(,QFi>ga+10f
%%1X]Vk=X>0.mXip=L"$Cl,?iD3Y<<K;.L*O$Ub<c9p((pdNnK51tCX>b"G-.mq>)[8ScC0/A)ijB5OSIKi+9^U3c$H:0o-!C=1b;
%@_*]<ppnhR;ktnf#GVqS&rop`Vg$2uMg\iefHct8&i>=sQph-G1rF9*n<-'!J$]D'qg,DeDRX;m6=&569^`sQpZuYX(1DeGeIjT+
%YBrQV9%7Mu$"f9BGb.[KSVP9sP`hdCpILo1H3dZ\-o.\;4gS_6#\rFgkR:5'i##kG#+MV**4a4f-"o):`%t#GZfaps>;ss#N^8q,
%)Qj>>'tZX>FMUG`Ah&*YZm6!k0ko2AI$S"Ge"07Ubq/]T=*b?k&0olWkbVE%-=YGWDUF8"3f8_-"=m7WA/l#AFC%Bm_]EmVF+A_a
%*),m-l%3!*k:9^I92_F-%&q]U-?HjrJ3fU%a>doY'"F3>4Bg^>,s_3"$tdimG/n2-/)#&S)X'cS;ifj8UD"E1=(W3NntQ"L<VI9@
%44k:+!XU2di0KHf?Ki4DJ2?#^()6uLC0IM*19&*rIrETld.,p#pIhd-;I@oO[:Wu2_5qacC/2e/Wl^7a<FrV8%f=%-X@s=t$LPdN
%C?U%Jkg.lQaJgYBSoci,ki=->3nqX^`tU?)+;mQ`K3X'"`0tKW%!NE^VBmWjfdad!k1]OmSlZ.`'?HS46l7sbkUQmp@[si-.k19h
%2D#O;VBq3V8^DOO."c\#dT]\aR%:ZJA;ep8)04`[*i>YsFFMsWV6=+O"FKjtk(=f[<ceOnTY%G^?J)PE_:b]f2G2.o[F6Kqhm=Xa
%o[TPh1HKkO:MOU:W1;4VSd6StZ"h<I]eWB)hA`+CDfT&1Sa;8(&&l0Xk_2BW2t^FNA"[5-2[']9F;3-U#2(i5R($;OP_be:'.$OD
%CTr5\MVDpL3@e:'U(_,,1KK%YCghEcEgLTW#0t,[afCjLEcD8`2TC9BF2BA.Q$"J:X$rY@5lI0t,4s,BOmF(6Q+KXfpPOt8'$W.>
%d49YOL9.m64JV_N=B_[iXC$P'icEA8cDJIM$k>SIQ!hdG#\!nc&emt(*#lo)#"D&X&bFB6())X8r1;8d4!YWpY[DP[1Lem:e9[&n
%ndKe6nUlh?5cgeN7RWDXk+jj_*VN/`XnD#\.:f;Wd3Y.#h9*m?J;K"!`7(rnR9UhZGa="H<VA='N.si3-"&8baBYpZ"p?s_0Ojl?
%IEkB96!'as1Y#0@nj?M+#25R8V2t9`k!%D]^+ooIkS,d<F:68ZjclF&^.?<J&HsuV0kfGW-5`.gBNb'`IY!(?fB<JaXDOW.7-Isq
%&S;EGTR7Rb!`[(Q_D0^P!_d=4&.3j",DA%/qrh-XO6ss/mjJM\D^mAAMX2]a1Bl^:D/'B1(WAm!l[hu8[u5(i]MA3W6QFPm\j51T
%5PUKV-Q!sM3TJc]YfOa6;hl3<FiV?3`cX>^M5)rYkW1Q.RtrPpJ:(;;AUto9`*HGhIS*7l^:GP((2]t'7e`='d#B)f&A"88)`rsh
%*"A',Fs\CKmg"'C"EotX^e@H:[EnP'L&3Ff(M2prOIVum")>#,.PpXZ'&?3L&W`'nbYYA:k80+D^^94OOScn@!aQe@ZgT8RhW"BA
%,2(_-8<uNIXYkqg6E/XYDkbp\;CX4fq<]`(!a6M/T_(@B)@.<`O&d)(%/%n(#=Ke)Ktu&e6X4s&fe12ZbJlu9]np&F7GG=/V/Pk9
%dkMq7PGuq9'Z[XI8QA+1#8eVf0oYH`c\+MQ1KAD![tG16CkQSQ8D"\3_(YNlZ6StAirLQ%VcdMO8g$b_fdRb%BWMh5qP'[o-n(*g
%@#RG]b^uMpCp3DqiF\ep.fenoeP@gj0VggmGU'Tq8U;Ip*q+@g";J6iPe9n+H5jDHUM$:]HZe7*P^JZFR?B+_q6Daq[Z&<fJs3l(
%)8)MB2pVo!'g,J(D]G@DorK=."(;K0eWU;PcAf`,[,Ch'+>oDXhc'gV_Vl)[h(kXn7$h?FFBBsQ.:W?H#^MrE2!k!60!r?3#[bL*
%5%M$&39R<WR<gJd[#%T^$"]b`8-IUCR$VNe[`6*WhFtQQ(dZ)#aYo?r?:u4to='`:,2$.eh?XrE*$[C.S:*?t34K"j>VT4<Dd5Zj
%*ZFemd<c'7Vh3J0#r*nm#DhR(D*/TMTO]Vd5Oa85HcA;ua$E%$dkV&uf)WkL1eNg\1+UE'E-N*U+?#(aU$uRp#n%d0\'_W_D]AIL
%c,Y?U_fIAojQhrj8siajUPsbDC8K=slm17l;&$3-qI%hVF'0/021b_Z4=dQADEPP&;8mO\.b57<J7@RB+q*G9paKD&CE$`)K*7Yq
%&jCpf-#`j_lbP6BJNpg!`K%0^XOckU)I+*38&LgtHJ]?J+CX4GZVm0#L,tt_g<AP$]hD^c,U'YX^ba2;'LkbY-+kG\JK/%&GF;.!
%TqggcBj%5S$4jIUTKJ_q1'R7l&u(DTV=A@-Da:3$M[(]=%_\N_"Oo>&-3phM;j"J%*\E:_,>lM:;])TkVIUI:'Hhr^#R(JD%DsXX
%Z?-TPq`DHA[mS%2)8)[7ZUHX^+I$@*`j_8-%7u5FBOVU/:pD'tq^kD@db5uRY9b?iK/B*Q#nPm9$KIu9i'ij*[#7e7!7IdWZZ0[l
%4&"dK@JRH*Ph/gG3ku+n-Ygp8';S:]>WE0RG"c;p\:nPl/eTH6<&=>4+u<:^_glkrNDu+e.[h0mE&P=*OK-`T9%g*(X#bu6j:3lQ
%6o>[bSq7P3A#tS/G(q/g9J+ZCR\li/b`u^=8m7fA@Z7Pq<AS_MeQPf%O+l-\VQdJN\;=8+CausNk9q3nRq,H;Pf$"T9IW@RYrKOR
%VD]c%'1<Jpehl!k7N;),=/b.`b2+9o$YA%RjXD`#2fRJ;)KlL>'O583[!;Nu*65uoppF2Q_."S(MmWS0"7/TkVhgjmWJYfj:!>%h
%PH+gDl(]h*ZhI$r7H'>iStOW=l&eiuWE;n4`mCH6;ita,-F[BbB`;9G4r;Fgl\YHh(H(;Z[7$&&J-=rgH)]p.B`g@:,f_(6H7)N"
%0@L>.4poH,N8^_"8lG0u?F^i>B%R_J-rUPWPT6E/N2jbn80",<JGs3c&X^;RdsP_7HmU$'j,g'k&h+O?]2:C8Y02^`dI9S8Vn#uq
%?AFgipfue0QA?op3mVZAc>mGfG5O*bQS"&H9;ThE-H`$ld)'dO9s\&iW53sdF&:65.2#'P/a=)"ga#VN?\RNJ$;,:T?kOcD\"T;m
%\I<R7*>Sa>A-r!A"FJ]J7Rl&"K.[ogLtaODIW<;h!I[ciA#lYBs'C!\8Kll&Y`)"V`s>4H<%8(q'ZhJMnl_2"2p*fOl_iacFCIm;
%m!feGE+8Dq]),)5@UaR!Y.&5=C38j>/@P"6fH#!)Mo3;$@'k6OPM#CSMj*qIgY`Jo%s)1"$EmulD_U6=;BfM7V/6.#V!&moeCQ[f
%;!C7^M0B0o+dFD:Ie*J\IldmmZm9<Lp/5j='BlgORelr1Z,DDje6G57=tZBn\/W#Mi_!qj![LM8Kcq,.BA-9\nlZl7ni&@%c=]E*
%0YrLC@BjB0s&2*(_];9kA;o1n"qZU9<dh,3`#:s@3,>RRAl.YF/t!oD;Oi3))i)<XH'Ob(QL[b>a&-og6nq#)s)ju`PLO<6_1MFV
%.cE@q\0McJo!fI#Jg>%f]([L8_U9MXF-rmD4:/@<^>p_IYUbT4dNfCFfbl&?`$$kCNPK1%I1/+03=6/mkQ6-FOrSum0B-fckg&Ii
%_%cmu]ur\rR7'!K[20h>@P0BW[rC',;Je\H>O%qO*2Lq_U'*_3?qqK.E9p#6JFN:mVs3!F_94gGD2Y'45%b:JC^9@A6:k>0PGh8@
%7*;PH2#(3YfI\NlPB$Y(9t)E!?N$?@]d%*\R/NU-@Pj(Od_$@J4:*A"TXo!0:R:&N+0oSj;b]j"^3cK\N^sNC%cY"BP)Tg"<LcSs
%rLTIB%p]oI"0m37"=De0h@4RR*/P!=8lS2uk!>1>e@TQ]=[K)VEO+l;[)Z&,RUk_rU).BD1`j2*!klRJC+D0a7PT-kM(Y_/bhjm:
%,A).]*4:VNQUl+qN?2)&B??SsH!2p?5A(&9.i-6N'fE/rDIS=)2n6nQ=1:&*HK4g_PDsU>'m&!@h9nks1.Pcq=`k-N[Wj^`!BHZ/
%@<*ehc)3(9m#;;eNArl:@P(gH\5jrVM_jrObE\!YKZ'5^%c-B%=*f56hO&,9Mrr%?+ed<AYf=V*S:peiID5BOrdJVbhQ:\Ol16Ka
%V+A<iT5VEB^YcWeG-SW$VpDiNRf<:9ID5BOroZY+c;Z_C51/Ot1u4.pdYD&qe_b<4f[@@AFf,bZ@$Y$_^%D-OS,3#a8KfW)%eE^m
%oFr!ZfDVDJBjTe^1tO&p_8.V#,\A1>V\=b>F\@4^,@VTTnl@PM;j^lJOJupa@3hkL7[u^b&^@5InSRn//k%o4'M%6V[#3"d%2;;X
%U"T)!I3Zlca07"n8:Jh2'<Uo7LO)8C&BtU?la]LRp&&u=<T<)cRp1g@H;i@:3n:QNC8YufNL<`3r4mk)=@:iuiYl+fg"a0f2n,)0
%d@4@8j"J@</[!KtT$=B!I<02EZ'q.d4,u/Rgu:AclIjt(dl4aB=<X)NbmUag:tJoVU\B'm\*inGY&@1X\D2r/M]XViP._s*W[(e8
%Mh!&nWI9J4'8WIHe!3#g`h<>iNVqN@#g/<^@chX#5'+5?cS^$P7rF<`MO(][Nbm2%gq(rjq\VDXLP$F@'6ZqXr(15@cAQ/ph;6Q3
%:.K"LW:X_64.T-%&=Mi!+X#;ALKl)Q+0FL[P8n_=LF`e<"Ml#4U"/C<%SXkg)Oi]h>aNY_Ge7(SH'Fh-\':fo_]36Pi%:q+"<6Qm
%79p"^BWAkbkAi;>U?78ICKc07$)Y8,#j/NK$DfgbgH'-s03ROt`f#tMO^"usnkGTLgXSn$MoG42-@r`6ID%,p@'R$!_"Z3`T$=AF
%S#l#.Tsd)1mFGetAU5*P;Duj;0k]h!aMMS/S^rcjS_cKf6VFT0HV68VcQ$2#NE=btd&j<K(@a0m6pc^/$,VRZ8mHunA%!1mqe=e%
%U2Lt'n$m(BinWC=E"+<bo]NpNMZ@Msq_?'A&lF]_5T;4`?g."!>do\,qPKDRNgiLiY.93oWtN>p3h3NZW$O#H3kAQpq_EkIZ?Lc^
%G1Bpj"F&PUEGQ'IY.?=aq65)OG!Me>YY&Zd_Bu8);HQ`rD^dh4BobM3Cpt]OKgj,M72Sb\0PdRS3a7-F?L3DYM5"CLV'4<E:Rrah
%Tuj.p1oI&c4V7[&c;&D5YXco+7FsOp>DcZ"]oTpW.c)4=WC:/MjB5Gp-]/n(1JL)DkFZ;I/NkU:qUKX`VJWT=EDcHR)9C#&8N&?D
%^BiVN\uH%q`hM\',%;tJMBtI)N>oR$$mUlKCH,2)c%5/[<NGU"F&#HTeL\7#nC\2ea'q8NTITeo%ejXhCIqgJa\l979-cU8TXYt@
%aDa8-kYgl#IlRH8JXmA(iPa`?3SRZt]NUV*g0sJ0He]2RVMYJn.$uWK'V#_pPF>MY!3:K:5SL45e2R#0cT06?:@V8I-tk<Z0=2ut
%nX<Q9m#IXF#YnI-:#bf)gu-DT+Q7j5pN9._hm5'P:#ef)/t3;].>gY`*r0U+(tc#e7mB"N>=hSG)?c,c5835!ohu2&9[BiOC/#6J
%$jbe%WW)JuK6XrWY8GAa2I!."XA?,o>EE1>_\<_2.UZ^U'*uM8*5Q($i:N&q9mWK4eiK+Z:R%ntl'a?]&p\jN9b'cHY"FJ=5>Y7B
%>E"c/dWA/ddB),F+5ok^S,[fH$d^W1GqHU/#!97ZFO,mHh9bD^)QLjB5+?hOn="#VM:tOd3Q##kd$CbWNIbu,(Mjj=coS*fL!L&l
%SgLG:agkMa2%T+Hh3'J`KI$gADVE*_h7%SJ7p*21N9i->TI_9O(kLg!bHsUsgsS`X*4e8&Oq2mZiQA;+)r[WJnNKiX/k2qR-*CFY
%A!91)aC9pr%<OP'-V5.p[bYNVO)t@.UB>V!E0>iVlJ2lOd%:hCTn?2*,>^aUDi.o-i*%t^%8'77@P@B3JZc7%:1`m[<:BE/pZn]n
%aBN\ifTWd:Qd^#EV?gX@H!E`pAHP+.Wu^_AnM8YS+3G9N;hqjggfI;]27fe]\4Dd/_Lis]W[74C,hEV?l&6(`U[us@4MUHGp@oKk
%j*b75YO'NZHJUWRk/0TjLQ++LJ;Ho9%@&<CTlMW6flfF'-\LTj-"A/WVJ5URi2ugNUtO?bi/&epL:j`<'-RkFI)5^peXZF:-;5DS
%C5jm:7gEf8=eBfdN.8&I7SCTY)4WFa0rD)+]\8dD"EG,F4\Z>>!>&aMY^g*m&7SUk*KW&)a):5q\OKq1bOM?<B:04bCBsBk+22G6
%eOu&.7*<RSNhJr*4<EX2dMe:AaX!<cI;oqh'b[kL;[0/G[V`_2QBuAEFb=:'6DI-7_br1]]BShnZlP#gQ;@=m8WHpR:0L#_O+"OK
%UGW7h]TQc*Nj5rj"`u:`Z1Co4MjlS-:ug%R6c_+a:YV>Yd)+$Ja2UZED1k,]XLC&V0l0^/:/)h'B2uF>NKFm=&-.f-m"g_dUF'gn
%0-8tF6<99u",m\OCquGj.H]r^A0NiAm-l30J;.8`7Y#g>$YD&u9%dW.Otp+3@PTOskVphb@:_;nI*.btVH>`pNa5'"R!?-06(&Y#
%!W<_X%VY/_[X&YM!(<PrUEEo+VaTL]SncgJ"dfsm?rl';78Z&ZEUnm_lC(uMpj05uYiDqW;8U6SGSUnRVE+&&TOWZ=(@*nJj\U]u
%Uha,[8Hb\=5mP[h28gVi%VVeu"OS@L_YIUD%q,/QEjf'h]XJP&osD>b_(fcAc#u.5*jJicJ,W>-XWP5J\/cp-WZES6<6P=_f`bhL
%K!92@">>-lcR,B^#YDUO!I6rA]k0uF5s=#@A%f`@3WD/SULI@2+Hg>WQ0hmEGO73fKT[qr/&QH7:*#mIhM4gA8aAb%M5h6$e1,e_
%$'hepE+j`Zm%3L$M\DTn5ZEb9oS2380_I5Q&LhIZp1%=bPgc4r+tjjKXsenW!Eg?go@!D0)%`Bm&p^PM`0hfg`^[.s;rA:LBh4,&
%Tf<s^_qn3P"+=d9rJoLndZVm$PFb+n6m\0h)PjY&Tu``*VV&l0C"Rc=CbGCf.2.,Gb]q0DFD=BcADlf8lTMBRbquu2Wmg.@oj5s%
%qIW7`)5lY%8%&J;DVaRK/R\+FNAm9i9q094)rK-TkY75UV`,mmMEY<[+Lh<QIRtE2IW5'RrQXH[2ocHdIJ^34J+<'hWt4ZPH@5Ct
%fC*>J%tFECh2AbG^:=P7HFhlO&0^_I5O.JYbDj,:W;PpVpt,OrGQ/%aJ%UUs5J?m5IpR$,JM6uen"<eqp9r^t6be$/M'&(;iTMs)
%NVn/mNfN^*4Wg`W-2%]&p#c2]r^25V'9b#4is+R4":A[Eb6t0f!="EHs+5P!V?1lGrPUY)Ee!b6g63*L#g*A,]!*90S"C_!mO(&h
%^R7V.9Hr&H$5*q,DWF'aCn*_(%3@<GEQnVNAg2+%HuVb^/R0Op15mO+(Cku3F.L_GbdTrYJ<u0=MH"UBDKS@C6R/];o!WS`:;"hV
%$#\<^#:[-(3i*:`4/NeWNi=`uMCYBK@N%c-dYE:l./fH7hRArq]>?G+#FO_N/b&G<8Ui;=lGi<Hkp4LQb8$9"HT+#C:_91<OpM_e
%*>.OP*i[^P\WT@KP>2/2nq,3po1p/9XH+HliiqW=O&:?].0QHb23]ZJ9\K3.[ciggKF9`NZV=+.oMJ!U't(o86@t!T,.P<@RBY&a
%Q/435#kq25BqXM;_K1IeVa*Z.Xjc7SdQe48%Q^[C=a-OkA9154>`M)nU.:,!'5O[I/Kl!-ecp41aYG;ODHE-uI3`kO6n+?-BJm;!
%r6PT2-<sFS09o^VOc)dd+_#Ul*eL]==VDca[s12`R2)=`RG=r/VQ'.GWuX;qoOq%UE7Yo#Zsd(cGS#]j<[$W70th9k')%?BADCQ:
%Y#unT$UJ5@C*;^N0+OV>M.T*NbD^"0PDHd5$EJDDV`Z^Q9:1O'5cj+Eo'F;6:EOo-3o(DrWoD=R)1cF:q'*b=Md/gk<OCqc;G's2
%,Es"TNY5[e:%iQEX?,sTdA'F.ACYpX-OVZl#8ZS'`C%&(\/A+XSo`s;^cfe\KY^k==(XcikD)GLg/Ees-u,HLE^<)L@f[Uhmad'Z
%-)@-s29YY1:Hr6]21i/t&rf5TLhO>"Ln5/m#'@o"&1/"uR4h<$Rmn>0&S$u7]mMPleU4WcN$3:+j\FcI-M`aZGVTKZNNsAAKtNBk
%6k2kS1t-6`8sboPTJt.^"a%uT)EO;EVaU7"aI26h+5o]YI,,9q%:>8l6/92U_jBlZG?TJ&GPFkV:>uXnif]OnV%J`KcTGnN"N+V.
%-HQfCC8G/;ce3.KqV76EL\-,DT-?ie3mBD,BLPZXpKZsIR#eBt5;QA-Di%S$BK*O\pR;pnl;#MY$><5W:<:E-**]-k%5nAGOmA$L
%Qsf1-L0T^+V\D\J4$DYUY!cW?Li+L3R[r'!*EiYXrptRjd8bN(B9/$DCHO8qnZe4HH$G=t&Q'lAine/hFD_k'833a7i`q^C>aU>H
%kf&Q0_PRWf9K[ME1u%V4P!KBo6q_V_`p^Id.:dtmN8c0.#TZeLE>0a_)2!a"0kGZ*E\VLX8>FCk7'eVA@o]ne7C7$J>?HPbn8(4&
%RYn>cecf&O$55^bAn'hO+s7BcpKU'tkmSjD=&s0*B7\Y=;jn@DG!VW>0%5u>OuP`'ZLRW7W>gL-\;%dHJY.$L-pJhu]Pf]p2Zr_X
%a"YXZ?n<b"cM#iQkb_s<Yc<N9oQlgK\ItgY_@(6co4bl%P3!p1_`rCle>hr$T%RF<$b4Md7oSDPn]8(XAS*I'3o1dg3G%37<LJYs
%jd$T[[+o:hpML(iGM40iAA/p2akR/ZE8PHafP#:N0OkSBA6e#SqI4Yb;s[^rCJkZoc"O_MDC/f6g;;A\4"JIDZ'b:dfi="Jl*`%\
%1VQrq+si)p"sO9(3o1t"XsL/?c9!1qPI60K%gigJL7dOKN>5%.Q7=J-Ft!Ug;6+B(F/=!B#ncQD@r<-?I];gfR7sY*W9=QZ<%B91
%-R,+ZA)*<%HE&rjij8nrcREolS3.Tk;RmU5)epL+.:):Z>a0LiPGXF',uPR7=0(k[5];U!j>N[MR#1U*eD;$*%7=5Wf*br!]bo*i
%_(EiN:*C3lH">GXO@\&<\SHdT+?3-o6\Z-fp?>nT`O[6BQ&)@jAW)C&?n"sp'ZNp:a/gReN_U<59E/]_A`2$LO[;K4H`f4fR>o02
%&A6X3)0L-&&2B&EK&D'DHd-us)'2Eq+bFbiq%d]8\-,1b?fr_Apume%3p<p\\6Tqna[BO+:$:0'\"XRJ>]K*F=6gobIF^t>9;hE]
%?ZdG^BN-V%ibLIHM5`0`M&[Yb(F&G-G%q"_8'`c2CA(or.?\C<q:(Ll=N0Dk-8P[`9$>pAaRH$4KO#pm[M8)L`)'UN0>*,LO`t<:
%[4c3(0KK\c<$r?C/Va:t]eA]2m'tJk2rO_O(B`IqZT@fVod%q._ZP49jmLN(0G#WJb@6s!L;gI"rq1?YnstTt;K]%BEb<`[T(11*
%,3-[hiiM%rA5=@hHAHB'cVk?O2'oXk&2?L/@uDF+K&$schjl@bo<N>^cR$O:@UcE6)'dci@4g$*>VW$6ouqDIr*2d$#UXS6L_-&]
%5cUH>kL[_"i'_R^F',4LM]T)GB5flPY!$m=$Y5mHlR:_?r-Ln/@V&%,&DWE\FdUfjYn7,\KpNCMe5!M<h'lu9dg_oP&LFc<ZA]Gp
%]S`mPpBm35\kA1kNK0`+BF'8JH(aB+QEN;[X[PP[jjhLsmMKS/Su:qlk^e@>KJ)g:!W2(L8V>E(I\XQ&FlV0/Hr*NeUWCUkE?\"0
%'e;mV(SM)!qkKg?adME'X3$r=+k@&&3,$9Q4_qU%Z0rE?9qSUZ@i1q*QHG=F9gVdh2S)'q8H\(==OB*_EqH<,%qY*C/Hp6d9o<Vd
%*^(o5H>ZqF)N0`A]@DO-"$V$O%V'gB8B=+o05cpfeZtN)/6=A<QD2O.PZn\"Z3;P[9P^pX5u%]rFd"=M3+Jm&rDXBCi[oXSLSb]G
%-U0M,5K"HnW0'kN@MUKE:sMNM+34;ZfP67O+D0ef36Y12'(oiLZ"#L0bHa`Aq1u(a=^.3cBb\PIcNchD;9&Y?SkU"9UQ/pd-'>T*
%#iO3=*ulp6[RuLOV/Z';^U*VCUD1O&oeNN(_5,hZ4L6o&&!RBZ6einq$.H4bR_B<[_9G5e2)#T^`R@s8*^Lm-d$2VV^I"I/^`pL6
%kuit7FP'5=g,Kh65&^bsWfo*Ji:2JC/SR3uGs'WWQ/lM`a"r_.FhR@!>[`[bdoYs_2V#c7b;LS"ck.I3n4F,BB6;n%;t2d#(l$Rt
%,Ft'$m-0/&Oeb6r&6#-3!C87doRfh?%;<[:"'1QorB^pAi^H]JTr9t\\nKG,:"r45nMASEq41/q][\$@>)BIs$EB+K4_i=e9>hD2
%MWe%9YkJPp.!D)6^&h;,0$&ptg=]WjZmsq-iZ/_kU^#gmEB82^3bGA.C<Y[U_f:JD8D0RfD*rj;3o3Ye699DS,+_QR>5WWg>4>NK
%@jL*R#X'$"jI5YnfL6?=71VbhV]IlMePcunjk9!/EsTWJ7_'MI)O24@]lR_=]*<[D+OI<$2jR+u3E:IOC6U-MP%nXfn\]O<bGZ=3
%`q16N!1/WaBe6,7dYOZ=nS2NNOeP`co6)6X#3c`;[d"c"YZWkEH^.SL0:d?$F-Ta*M6poV+kT-cOdKaJg/rjMSn:$a4@797(C2B&
%bKgJ\d]fW'3[gjfQO]ECWmm]`?Qa[^+N-)CarEMkFGdY-@`'[_HKL/d'Z"Z+F,4S7OE-%1/:Aa!F^UO("uEAO^q!X2<$:STH(80R
%/Fp?S@]h[uJ2\--_Ns!D$;S$%K]:^iN#W#?6oO%a[p(]lPWIB4Y;usjrDQp(N_bWXX&1=()PF!"4J.`%jk)kWaAOL'\6r9IahpV;
%Ln@fAUk0o3n*BRHB#b/LJ=XcFAZSMo,je],n*jm-p+&G+'7X&:TIk/%)_L*N@8\;j;j01.PdA8Hc4E^NW#(\2P,cQBLiIOpC/Ab:
%lEA=MC9hu\n8,TE'dntWORPr7hI3V]*-.3&/25[Zf0rO3K!;TPjM9[Z/_O[GRF5m"7lWm'+kDlcrpkM_$]BC"k\]I"^3JJ0:j"iU
%9^lT_;k\aTA9t]_1%gM^T:-7L+(QZuLS:]TMoufY>_#B[-PJe_@J(fP17Q;[Z?l$dmlm.iRUh@lDc&E4%D&"NZY&*cK$HZf)iTV/
%RK=2Z7CKSVDt\Qf@8u=fD.5hnJ<6_7`'SZeVCu1?4RD.`JM02DB>flgC;[9iKPlo(3Z9@=n1ak&d25Q'Fri95ZsbX8,g43o2W[g"
%LZo4$06ASI%c]7YQNp"O\7bEVpO_!B7EIl\2*koX\44,c`c6>Q7NlrCE;r<oS4qG,2Ga!^+0B(;5o:+Krjaqu3l9%\$5:#7Sj\pW
%bs_(Hcq;HCAtjTnb.pGklAH40>UpR<6=G8181\kB.tEo?0aM&ca!8?r9JWdtT9Brd49`Ynpg1%A6R7AH)8la^R!/d4,uF_`YDk14
%S`naM+aWqEfUAj/Fnf\il&"f>0T$!H6Qq=*:;g!"ZhsOYfM':1(s<K`TD7fe2S+$rPX,rYQM1npM*rn8-):;4[0IX/SG.u2$"BpO
%g`DMb>dPV&+;uNc@#cDA]V\(uBo*D=I*jNYla%3m@&+/F9FQ=(Z`OP!+O4!#R!q")WD7>on=DJpoU2j+;GrKULe7KAPZ7=[53hrS
%g'BS*oAMM\R__Lh*a4$/PisiM60T&W\k]S>C"Pm/0YK</pRZ'X6e^o"_%9(0&EfNuQl+Gu]`oMI#YJ0mpCTf)YscfE,+_mI6.eF9
%Vpafc?SjZ;3%RHBb7&;Wkh^mY1usa)T;7$DU+6m/MN4&@]GO#X&f[TVSI>O^Ae6u[nYm,Q8l8Huk*4Iphq(?KKdm^B-H$Mi=N(S9
%`'pG#+rcS<d"@Ok(#am>e@73DqssAB)T(,rp8"YcK2pZAcs[KF,@lpIVeg+WL>%fOVEqnm(];3t0F_6g)Z7.qi@<3pk4';NiB?D[
%L'Qgt%XuAfL72EoJr5@&Y/_lQd`:i:>"0EoW.]%sJPa81dhC65V90?'dVrOE[F$es0hE>n9N3#cC0i2"Ppm9,*$Zip+Z['^Q*Epl
%n9ed8d)1JjS4qOD7$D4<*8.`=(,F.8o:@)TXq,%T),XIr<;D-M?/H5gM@3]Z3$<'PeK0nFIIr%4^B/:$!OPl&NG.>,5n*V,>#(]C
%b0'NP>1lK&i,C!lAAJk[b)Na[LTH9d&Y:6OjCMS*\YGN<:ApG4&7CU,*7&<GdL!c4Ws6p?M`oAfVkH8P4IJ[t86Om=FN,l%ao7NT
%5g\^EfHgW'b(9eM!`J'I+P&RVYEE@S0sCm^^/[%DP-b<MD'jjZMj3!Y2=;jk@2@:KNNF!CArUg_,+SuK(]cB!O')5?7XR34L=6,u
%gO$45+s*2j`nnn*K8P^g]KN6Y<TAUro2%^-]E7]lBZ[6kNZg=IFu4JoEYu4YKW`T9`>@+UD[f+)_JJ`^'F\0B@6aPHL4g_R!P@Rb
%S2.cF;-ZNJ3[BYm,S4&o-4U1l2WMh@cYn%@K73#/.9mBEgV6%`(HACQd%QR00p]kuc(9\me"hkXN/)eof7OP`7*p0;-=WEKP>d=$
%=@#?]6?$.2?KS\K3;:!=KJ>MN:ElQsVL1Pj/OIf!/Y7g44qNRO!/(_73>8tp/u[u"rP2[V"/"$uqns=0(CretP9@G&(V?$'_^5sk
%oAn+;rI/Kt&X#FrGE2t_oGpY98.o3/n5^49>BH67jbObZH.7X]r!+-q)W?tqp9N)s#OC<af>^(SmRpSj2Eu-3cd5IIY,84lkeBG0
%l^\U+n%_=KgOrFGpR.UIqGUlO#=+`h9KCnAW/B%aU<c\&kF,C<E#R2$[GFj3Qbus&.lT*Op*BDY\Q=3t[=HD`6eF@o)cp_nV[eVt
%<7;DP6teQmZ6CU'TP&pp`TQI$#FH2_2SdQl].jcS_oo.9(sQaU`5-I'3er7n]nl%&/J3\mE^,s?;43gd1:V"bY+Z@9_8F]R*%7_]
%=bQ!%L_84T-cTa;:'Is]>/Q3<F:EO0U?$"r75A_9.&np6eQ_-(JiUZn+l#,`(),*516T>AJPM<a)=(57NL?eKH-fQem(j[PDO;'D
%INiiOG)Fuj[-*.gi>f-;:."SNL[0PJ^@Gr9Q`q@nU"\rc>,kG'"5SVi-HlBk>BXhq),=;"eh8lq)]2#Wih=M\5=Fc,<D5%[i!On'
%qt53d>5)UT+)eL62;6Ho;out2R3[jH20GM&3M3?n'S&pi[NlcF`YA8N!N4nm@60h%fn4o4c<9W$p6Q\u9N9t#'9p3"L!B_b+I\N]
%A9Al<M>P5A&R`C3^n3]hb4[5g&FKA)]N"Mh16dTt.n<!gA/("K,%;8j+Z!AlA>]f@p5nL,q1snYg4RJYO2irK68Y:gVJ[7e)O*s)
%_@.n</dF3bV856fi]M'XE`7S/"1l$LK)2qRDu`mr=n<luVbS*^;lIU/&KAWec#Tkr`&h3C^#KlDN%>R'P0jje915./W<*sSMjPQL
%?5F>ZR&GmH(F<=C)+4sD*'O)"2]bGKNIenqo6;&B8V_UXeXQ+hkeUfR,&qVH]%#0(JNWDaB/"%C/WLuB[C105/NargagCoYK2o06
%aAsCc(l'@k,!$O)K@M;T"Y*IrG%l7,VU_kJV*X>PNF=>W>5jG:Ls">4;q;&f^76l:rL=ci*7"-hZPX@W'H`Ib\]9':F?D?J?OeFV
%#%IPXnC0u>qVuGuD.<iI"<CHu0UL=YnoL6gIsCS0ZcBh']UKWbE=WZh7X0Hn-)g"dbdGt(#=ID@L,:jQAPV.qKrA(-LaG#8?.+:[
%/oR)C3jZD$r;$](82_%lHV:,hg`W4_NT"r">%q<T`Qlss9eK(&/<O_a&.ipCaKoYLORsS<VD.\i7Gf9.e1sAeC@W:)QO-:Dpq:Zr
%ORK/dPQ]%RV.hE.>KG&&Yp\6@qRUuC\O<SfL*_G6S42dh:.?5.0D9Qki0$%Ta+4U0;=OnTGqa"NdFF'!cUNOsSSBj)W!d#@)%Yj\
%?(Nag>C-Ym&>Eq3oYls0=G)TYNWVZR.2RuFH6<f>8JQijV,@*:")4hH=!/d\OH\OpJ>bCpQ8s6-m,t33hR:fY$`5/=UKjo+,[:Ol
%d2lQ#11G"S57CcjU?i/JlNTu)iPl/FcFG"sFqTY_EEQ0fLs.G>T@JNqoE((-+/k;T=JaZ7m$gHTEIi;:UVM!_p68DbcJ+I&Gq346
%lp@5FTGf4,1^rQ+Z$%[U$utHZK`?2bPNYTg\3p\1m2@,1L:<0%cn^>fOY^8o=)o2j.+kH]feh$K#%'*oN]A4&-7CXD9WU,#Q.m"p
%L&d1*`Vq\;"r9#9;pea2=.&oMS5Q+*V.%k%?oi<.]]/WWWfDlHFB0^B1jVdrpVPdpj%"[)(.]Ae4<s<HBmR>/l+s0QmGX)4gM#Q2
%hL']*aNge6TZKZ,K1cD<=W?Pf+_O2AX$N,0b,)dr1mP"0K[e%#N<+msN`*,m%S6db)41W=`?^h#ks5r,bd?9S<],Jp)Je]""7QU/
%M_DU1U#aT'-X4Y,6nXe9XlmYPdhnsk0ih\1E1V6+,TWqFc,34KSW!f3FTq.m'O>mblu"uH\@Z_\$S/%L^.LKt%7)Y*S+CA>A`#cK
%dr3c<*\E#Y(=]0-D>#t5,[-"<lDA*2#7df8nK??0'pUhj,dd4E?[!TKC/&MSBrIXtb7IN187+GlQ4E%&'^!p/)Qr81I*G"E(:33-
%V,E-=m^^Fe8bA-YM0F:i`fqsUOSIrS,0YWa2d!-oI83]h#)f5%8`U=Dh-QGG95pgB/J'XW/Ti);`f6gK9rRU)>8VAN;SPn/\qIDV
%\?pq&NP)pbShCWmH)lUSXHl^>i`NiZc47FG+C!uc(XoDriVmn]^8;n^cI]^Ia2@^'dqt8"Q`;@sqFU3dHFJQ/@6(;2Tq$fl8Xe_q
%@@Pm$"p/+fGek6N7u01e^3MnEM(;t/,Et76]<9^Y+sJn*j9CYdK#S^lYVgk?SXDl[2EA#<<JKQe!@Mss+Dep4EQgZ-]V`MSO,#qd
%3b[)1e)3H'q'3odBFo3\Rd;9PV+j?D]Q"u1""N"32s2#s*+j"417.W-Ot\O96G.1\)3Zif5gCCE-)Y!bg`@[%M8;s*kKkq#UJFX@
%`i)]ZTSKK-2:jC?KF0pKNDies/UGh611i-i/FU+&3Iht/,qd5e$ph`:2M=i,,'\np@h').LtG`1K>B,"MN!A@/UBN`+9;26&iG';
%S"EsM@d]rmX.4`QiLJURGR&rD*Y8&^!8s69.frirN9:P-NI&siP&.T_oi[HSSn$.hOp:pUZ6p*M8a:f!//:H#+i'pXg2p"Fe44BM
%H:ZPJeF4`&g,ZsoVTaK]GE=LYC(%6[b&*7h`Fc3DJi!Uo-FHq(L.9"2A9)Y_2+7#REE,m@1g:f#+D40JaH/mJE`OFl+U5P.lCB%M
%B/('_IG/cG&.LVV.dDBr4Gbe+C=MpJEcL=j(i5]o69n]f0i1"??C]-s<jnq>@I6?BAomet=HTU5"F[*P0ZasKW+-PFP2a!Oa4jMj
%QGsgeS^\C"$G*]AWWSH-kX$H#\6k*'Aa$3NnbK+k<6]G/q,OC$/HP&\rbcI1@M\LZ^3tdE:mHPU.\cXp$m]=+BqT=fG'40Z`3AT+
%f;BsJUCMf9M8dGE>;D%o68_0;,5P-OWKUKmg5@&2K-.Cj9(b/WE/NKoQ9),1Wj^%sYn?gm%EYG&V'Ms)CK9F'qn;:UN%M!34pQMo
%PZ13bk>fU=?(2np8'iFn3(_,Pd\-JJnp"%];i"SoMX)h6Z?J=YC#_rS+8[4X'8.d,!%`n)9Z/rEnP7<!O]o?i//Y`<5t,$tNVV]2
%89l<qo["4*=PV?"!](au6jCM3W!:;:(bJ0Cej]o8lnK9MT"o"LqiTEm+p;N_.i^Jm6IWa5:5<qYoHmub<%&d';cSq`L<Ra[3'1&B
%5#:=2!VAp)c)frJ(WeTrUm9E\<+n&oQS-KQieq?\a+-$;,W<T8:i).[g_!DSgF6>jYgC())D<&[S/AdDXfe(h`'h1(!0#q9d12&`
%QB7+a!A>3uI`5brW)pC?/*s;/&.9[)97V?O7Td=9Y;%OK"c"):l0"Lbc9_HH\RL<Sb\QDsJ5?\mF(AcY@keu6>Xm:W*C,*nNFR^a
%`]/F5MaBR?b^U5ZbV<nU!,KfEll,Hu[)pl;lN,p@'\t(DZ"PNp8^XZ#=kTtsbc3ZdActBKIFdlh2E<1+;*c0*&X*Hi@2MQ`0`o9F
%Bm,:M8@mE4LD>b3eo=V29#bPm;R%'Vko^dqk!P,bhSS0.LuQI4Z(b0,CauatBqc%A]ToL6eZ1u(2,8B^<`.F%7c:+tYj=&O<e;HJ
%94MD#A0f=5IgUZ&]Os@1:*3"&\R*^a0R2'.WoUbGmo.9q3Lo.EoPDm=/.OeLFV/O@c2_.gI)LY.^gegZT/*66&ibqB$3o62Ko!2p
%%gX2%?kN>#dff]4YnPL&Jsgk>W6uGa<)j(U\<]eJgi,Rsi;?\5M(u92(go`?3X+E#B*JNW'=qX*1,&nj(`Q&$?Iq'7EtWT9.U3SB
%?ZH/k$8-[[&!?8(Jm?:Dm?>MXhcZMTj[_`n)&HcCMUQ:0dVP,3)UQYCdDO$U.D<W6N`;c)32P2Y$OE4j-5Yd$)eUimPR*ob%*Vr]
%^e;rc%:N"gQB8)a<CqlM=XdS/@0e3;P.K9sM:KN.6XuDf)BpAm/J!K='AsuCONhqGhA>Q;%_.:L?[&6XNH;"H."aM=_"Rmp'LLlE
%`2t)ea!r>Sk@j9kVX&4GG&NA($E[fTg%C+pYD-6)Y!Rmm_!FjgqhhN:$4Fo=#p_u]o-X]5VHk%a7SPbm*6(GqWpd\-Ti$N(/7Ojl
%1jt-s9m@GHnX/nbqq+H!@*kqH6T,T%8!kPpI^'@oT,U:pG-Q6@g$t_2l"#t`82^W6\;HkjMZY+XNWe/F0V"BO<^g>("hpgr[M,12
%k)cKS5^ub(:7q&)C?De^3Ju^0+i`04lq]8u.KX3]UH:ns-qVh/GuWd>Uipb=TNa_bVrkB9E!3Di@Eosj&\FomUVQ*@>iYLPQ4;>b
%m1?/Ki==F<]-oCUG(A+"`H5sRq"k*56![*M->cs8P<ZW*7'7$73[DBahGLhN??p@M`7s6F<m8]Ccj6*f\#3d!@m]oi&N2d;S\\>R
%J@US"]';:EL,8X]"nR8jUS$>h5T*A18A^f07ZQ1(jMTa>8RfOl53?s*OoSCPJdtM8pn$km#\Gna_lZF0ArYJs",.SqKb/=@TX"-d
%5s<]\_R)RNi#G&V80ejN%hTRZ`pR[,Tk_S5A78]_)A(`KkB+?d$0REgm;RXBWuGK!Rb!$6TsMRR28mBbs-gr:2LuV%rN'\6g$nVk
%\6#5h^c"Jf%"fZs&745cnc?j*/N%cdj)S)YLce@a_pSagXqV'87&Q?4`]6,mh&Ik-@*%gMHN<pNIXp3I5:*?9Y]*X-=HAfM)rh<Y
%DfrDM"GmGiS&%R5Ta-L/dAn!5b^l?JnT6.t\$imT<I:Jb"G9[sq=RoO72VRts%/:&CZGqI,r&fT*&a]=]cYAj'sWfKLh<BBWQIZg
%!J1;Q9hM-cK7>DqD6OQTb"#ON657nCr]X*s"SWB<23^=mQ^Mm\2((/8(2Z459:2#CFZ.Cj=8=(3#bhl]+<TbhI*UThnOo=aOV$p$
%$+p_8qcR'BR,&M4!R?K&lr2rkUV7ju(POgq_1JQX?idI<k)+OS4kRsmABZD7lh'7P#KbID17K';qPn%s?R8&DL1Ms\ej,rK,CgG[
%5$--aKiKETBr]A2`h4@`49;@Q2i7.("PapCM$=4sd<ic.&2+^%P*GS%\KP)lN6<T!HP9%PD4cV2KIt+5@u$S0dcHq<>!eq,D!4]a
%3:Y*mfOcZ?n6[(A^a'Pa+]oNDN+HZg,F>"JJ$#pkZjQ"#NHZP^?%%,X=6j:\ZP38(6jGDN1<u9m0O+[N'3d3NFN>LG3ZE)qCrUI"
%[p\WU8c/i,[3lpPbt.99W4i%:%;Ep[\%>;U!"h*,L2DWIBc=I58(0M"?R@r/MT+siTs?utW-TV!#lnBVkAdc>P`M!I"Wtno=lOt`
%,,,(T'%'%tR1>pd$DN7$\pd<[FU>>5_48@Pr4nFc"$53<%A/AZZOo"5`Ut%KLLO:5_t%c&hjDUn<+n/+nem4;8C^ZYN(s5?=Z0K(
%YUWekiR54tBIk&[j15Nko"!_uUN4W%m'F8#W4KHnao_UFCF17gW>#NbLSC,0Jt[Lg,L83&8BmF00j_g5ie$Zc:-+aH=6E$c5"8QR
%Q:@@k`,.kqF9R";V&B<^NI[.%3^;51#"/"l]fo12;nBP",EMn2<Ht*,;C>Wg8T&fG?%^uK"kZVKcqW$;La!lTT!mlhTe)f=PR$Cu
%['E'PcsS@Qs/p.q9mE?mi$Bcl!gK1=(g@)_TN45''1S=pBXX-0;FQAG.3V66CAVU.gU#dO*&'b(kg`I!pAoesd:(gf"96G:(`KjJ
%"tk``6MhtpB!b)_mNLi/Ro8M;@]FNaf8WpeZ9@dsH8g7l0I+R:,+@66kT6daO0#l-@5Ha"+Dc)@9$I[JL4S0&?nRR@;;FX\_:E?I
%j3"&U>NVb6+Rl-4R7kGj'@c"LG$qob[BPUElNmr.OHPVl/5h4RM,S?r*UX3l6u#dq@u-_F;b[]Y5rHX7RnXWj;`.9O6:e)-FG4lJ
%56TMi<o+YT=-C^S(MHd<<$K6bXX0ta7(l1m.#YHEc6@STP6"D1)*feBBEsZ*RrV9p@&cd`Ulm0R6:;800<j?SN1C$`-O:72*eG\=
%WAph<m)>nP)l3Hg_F/Fk%][I[;JINMLbS"XpMShJ6C*R=,]iel327bE+sILldfm%KLu=S5MWX%S$:6Ue?'3c^7r`Q]$q_`:3%+K;
%&+?'1e'0HG`C%R`8N#1SL`41QR_)_hS]'_"H!27Hc:<]Lb<!KM'5&:HU7:EX'KX+*W^m4l3(N-`YpH+-PE;JM7=!'OZ(I^.bLQ2C
%r#nW>f#iR-&<94k32.qW3[AM5RWK<*'^Jf(4<H+lO@(Y@?)&b14@Q2D=qtJ>.Hi*5#M5js`+Y4*%6%@-9(sA<,,(cj=Z#JL(LaJ?
%?\rC1O@0uAZ^%MBm\@U>%8<ucfbO[5IJ=WNFt:sk%nuCm(FRn^3ro.7H`-8d&Z\K"Of"I/)EUU5,EF1j-OVAJ&f=1(*PMq)].l3@
%JB'r-;+-)8M/gVWg6bqJM?LE5.^Bp6Z4;bf!Eo`\2+&.Sl*^p8^Od*gWQT8<:?<!kYa=*p7JJu<KJ/<X($!>j;<*=08:4su[EAZa
%ap.k)@tZe9%il-Vi6E;=eR04Wr_<?!WL13_L5&S1BYfF)1tWgl./c%XI$ea@]&Mp.0a%'dG2j1[SM=&r\eLg]1ZqbJ\&BUHj$osW
%]9fUO&JW'i'P77aTd5rugl\K<^T@)>oF3:KW3a,VPd_#bUY'pLa<majl#C<]Z*oOk<mBQeeMp1.;,<CLme$8(as^iCDpcQ6-M6EU
%%n_LP,dA`J"u)Y>-4Y2?<K*gRQ%4-g)fcI/XF\rdTTOMe6tF\B>*,;H7RsCB[Xpli*d`sp%%(Lp1JGV9WR0Z?%1([,MH/0.ilGL<
%49dYJ0Cq_Sg3G+68Y"V7=:Bo,B&j(V;@%mW<X#Z1nCApo.]Vs.Z,2r"he_<c+BNi:eb;(>!k]BcrG#XI05uO",#s;-:^JK.OWD7_
%0GBcgJ"`u7J4L=!T,nUI'4GsASLAo,<B`*e7l^>YJq]#Jc.$UW#gYjG.akp)Q&Z_WZD^A1Xo9TrAoHm`pK'Gt'?D91l8gcUgC+07
%L"2]f]u$"!dLbF/,iC]/\t)eQ!GaSi%C/QmR`',;0t`JS81MQR^mLujo:fBMZXcoHK!GY,'@8^bSi.Z-=5=4*Uq"6"'(@9I#6JZ^
%CZ2poWU_1,I'6RT6ed4I;3aujQ4<bp_`Gl6M_O<Jb1,Q_-3Wio1#Z=b==3[1@2.0?4hSdZl[(HraUCkFWR>J+87naV#<-K)i`2/+
%b`u,n9o8pm'chQ)eh^<DV7l2FD60JHajC6(jZ9]QA;atP7i9p&/5q:1::s%aeBDRVOp%nf-\1O2i=:#kaEn+YG`1E$)U.1M'L#,5
%%rb@),!nt)NE=ZL;pYQ>&N.Rqn2fLCNabC<<cASZqF7Uid5>!^I;6UV5rpX)\c=+&&6:fcXfo(/7r8p;TJ%r9%PdC1[p%;g$#)N[
%@RrAUa"]IlpGAHCO5p>Q"#6I(lHAO7[q0V]S5='hN'?4EWJ`21_&eplpY*[=q$`5icZQHG?F+$:_m+8kTMkJsTmC]PK$2^q;%m7`
%bR^);XG7R.GoA_G((W<:YlcbWM+R4QJAFK$H70HRVKffer5]h/6.VLY$9;e#f^+Si4ok:'1i0@AW;*%OC*fp>VjGB*)rUDQZ8fdM
%.8i./N/=-Ge2ZmD_GRY<NB1H19c<2B]F#Cp]$V_%"gnL08-:OZJSE!_%PY]d!A!_K(4H`6Lt./%DlQ0nE_Q7G3THBLNP*B;(1+l0
%_BCgOA>mrt>/Vk,bULukoC=^QY\$%LcMWu"Clq'6l?G/%P,=i,fMfVS,^X!RMQMTOWEfWf0OHd9II-IBF!cPO&K7%VoBXV2PG\+s
%*gm*p@7usW%H(0a5uHi&]HK8A4OJRHUk#.d3Kr\@6DCGja:JM%Tr@X@#BF4Np.WmTk,9=if;deh6oYSlc+.:9`OsA^7jN`k+V8W#
%Yi&+s:W#J6gPQ*['hP6!n2<tg&7f"QqX'[a-a\M`_B$b@64kq!3'+H6(4XK:#8`T2*k[12WM>ZC`qbDZPKa(\pk:C+7Q]*NLdBfF
%%'j@e]k6V0b>p;N7f'D3RY`48bg=alLCZp)7'nq`;-\s)&9O\3%Dr&-0p$5hU$0Bi:\:%$=J&bX+U-6N8YK1@fFSP6VKV:@)<`IA
%]qPQ9lWm!0<:5u`n0arS3(p%*)n0QAoEjpo5,K2@IKg'_J7$;iZqZ>"6UGNf1$-Kbi=IRmjdR+0g`eV2X(o2)<nFg:OmCN\IJD$$
%)0G?NMtePA3+GF#4<fO#/'4J!fJh7&XkRa+Pr6GK\X&p5<B\3%bWg.!`;e+mLlf8P_m1p>&g<[Uf]_PR3tXSeJSG42eo)Ibb@'#a
%>s&92%pKZJUsU!P;WjthFA$)>;%>8=9I"/?+f8JR3-QL<)Q]MIQ>e];Wkp%0&f]`#oU&)L[l9MO7gHrAWPOri7'qgUB4qfqne\9l
%R#&k3=mFpk+';qlG"%Za$*;Ko(Q,`rGo80[PsGIf('Eu+_6S=$#3G8g0qqGLI>Dao3H+5VjZn&6m0'1^"RUWUSkXf(9.4=/1ERM^
%,1[LbZ6aR.bscfc"%Cqg'&Oj#A@uHX6ct"'o<ko*@1.qabp)DAVk>8JZ%sYdk_B#[IVbcHnu,Hg6oLhTfG:gY6I2Beq[!=-?"C+#
%P65APm!a49n8-_])lMl:CoM9N&/(XsTrcg_Se(Kn=U-0lT)I)@EhWZPQX_EH_mqe6ClIoo91sqBVoccsFPWmKU9I%E*WijILi>]l
%X3G1fW_FHN6<=0L>4&8!'6.clCcdXKe/jLhg'KJ+$Vg0/pmmT"TOuAhbHp<;Ek3Prq9aF)((?-O:fWNAJ5Kh3g,`u5CG:\?OCH!o
%'K];hQ4KH4Ife#QJ=E?*VQ4^Rj47o!A_]Nk9PKAf,^O@4,I7,BL]\=GWCaQsXo`.;4G</A9_coKAZTm_F*P2ECX+uDW?;5h9t\jL
%-)A_0,Dk&Xj"&'/9R"42Lq'.^<:[BLpX@b!bP>QCce*u,isu55Fka^ARbf)hmV"U5-MfG7#&FaKVL;._KEui]X4D*PlV!9.d6WAl
%>[bWs%M?lAm4H833f\6fR-Z"tBe08fPG#G51ogsq3[,NBEQq$SAY+>XHF2a$2V\dg7j\q^CJSpcOucXEfaMTq^cr8i$JVU\T+nqL
%+ELB>i#nBgS%X,@]1jXO<QYVBO?nUMU(Rl%dYk4iJ71p?Z)`;FL`UL4W1M:ihrIQB!TD$5.Q#ahes=I3MBMWH#b&kgR^U$GZR*UK
%TusK.@H5RJlbMbB=[JtW>'2JZGc!E"Os5#(Zj;Qg`OXpDU8nNr1[UW,keZ#3f1kY6:5i7g/iGs_%2Pm-#9dRAG`6;15t2#U3ieBD
%YR%40eR7jH1ETm'P^t>jQ<JU=i")PLgRDqUF[H]n""Cr_6C7qpR?Ujc*@Rs4a5$r51]YsqAhcfE'"sP-OMa'"HBqL(J<uPJ$VdHI
%-83cX7=2Jf:!I$%CJA`Ua;?Vm?Zlu&P_??e+3ND)<90WFH\QoDHWopO"ng-ek:FbkZE_s5?I/^JlHC!)'I&+[dR0:5_`gN]K\SCY
%3MX/m[3W(Sl/k31$r#kp9;,uO7$L*6iXJp`/6Ue_OXrb>"CW)IQdF&]E=GTnE#9_i:$,>'W)@U'3V_SJoHm\`WLtY%&\CP64`2J:
%:r0D,&1L[ZZ1Me0UI:>]-d4e*9)`oACFW8;&CD3mR,9)nb;p@?Z!&3fV!U2\iMhHe>[%A4+_!(>,Q[+KTu.$R"3.e!*jXHsWnI!R
%AqPXX1ll)^O/r*_*EMrmkUn2/J0%hg'1U[>;n(<[jKL*gb>r-T!d$5?>oIY`?iV/_<"Gc*$"c2IdfM`^X(90g'O$X`\oksnVAn>-
%<5J*ZLr0F&lWGs_U0thMAH7;eaa9=g+Fr_p"VR5@h/_m'+V'$172L3Crkt1m^?T!Obr.FM\kU$NoLqj"Xs,25rrQ0hq9E'"[-N<j
%U-o6fnqtjJQ#<J-/G.TJND0G0PFT$3`1DP:#'1Zq!Y'\:^.^0a</WBsYu>uS'Y_r9=pdKP"W1FiS#]&M*<qdthg1O<U_n9]V%D81
%$;+VG-@M-op!faY2A%hAJDkLLF:H]MaVLp3E=_LWTkA)H"A'eH\RG7jZ#/$!:erm=.n_'i(HdN*7Yke#-9+X]^8qQ!!3?Z_"c6Od
%-.q#bOb-SE#t*d0$LpP]X+"B`!^KB&YNPZ%?l18Z6f"dOrDf^"(mUm'<AU;QM@kd]<)'e,L9*?k5^J>V`*pkZO7Ql&ptas^;ZVY3
%@]3$i+4.2hDi[\Yi8lc/UeIjloK7aUVEEuPC^pef6B+iqglI%?,d;NS4`f(V2Lss.c<B,-L*b;j0<W"M5b(Fucr8G'Irp36[@9#(
%QWTIfOn"URX@*RY^B$2I0gDDAegIoB'\muaPHqbND-ZAlU#Vpp/Ep(R+MJ8NN'RKr^\?t)kosIhU7`B.b7'TVVo@lb:`3*S>Th-+
%"tml7*)!!'Lq>g%laZ@a!>M'X<XL'-i/m&Z&UQSb>gpMiX;E?u;jVT7kn"5b!Zlok#a8\)b4=<r;\fb2TaMO9JG,W?ds-*F0)"A#
%.g;@O<J+.FF-74Mb1/LPo&!@V:>GtV>HQg0`9KeuO4P&nn?"YY@U-n_($2Ob=$rSN<4f"=`0=ZB`gL\+PSc)^X$&pc"o+H#SK_2J
%Fl5a]j\uVSLKcJ7SDS#!:'r.u7%IGb<"o7u."U%QO*S5BYakc^-tDpBoHSm>)p<M!6d9>b$^9M..Nd').;]@;*<D$XRZWj084#I+
%k))d4Vui><mZDk#*O9^LB%"k2FPEq5LG&&Z6/N$FbVP_h@!8i>2n[+30TOhSe5J"i-^lb9>2-BAA!TM)eosPYN[ZBPiC"AK*14n(
%5tAs&ZhMDiFD-!PU*N?k*qXJC?o7Bb,_)<L4dsf',?lj8`fju]6/(HVi%^:jIoJCDZUCHg"Z0b@QkT?hnlQ1fJ^f7%T',='@IGH=
%_ldolQjT=N+YpiIQh-H&_/t*2Z9V9V6)`MjDGc\PBY`WF.pCt44agd2A.$iJ*9I05,$KW>_W[j,U:ct->Q`Do[,Aij:r/T]KSOh+
%l!p/pmg@GgR$H#=@8Jg/;jON8s$5=Y'i@JWm!d):113>g5c@dK1h"QXiPm?rk$^VM<Z3g0)YQja6cjUGVrtK<QXl#%,qF.*Z$*@-
%Rc1qf'N<H]BkU5]X6mJ6"3V>@.`=c"d*GK?jBAWm-;lRTf!W"R[dqUPY1MBV=aV^Q5t+?DHtGi^Nsp\N,,0s0hRLL@3a`;4aJE%X
%I[3KCCc,u0.WO,Y'_3DC<oo'\FIKHA1pnJtJ>R+<,@$\6huYA0Ji%?c(Th+\?mJ<!IVfK3M*jE?;')_!Lub?a7'esTH\p49FjH[9
%p</Ei'Qd(/!<Y:$Nr!_Y2'p<-TSlC!*^hlqd:u2`;i^GnGH826HX"'%l3KpMEZ%/feU5MLp1A$_(M^RN5HLnrSh)k*7:Zi?#DMn8
%+cMLd[Q_5<_$H4Wq:SWG$R+aG:KMp,SfO=`Z2dC(e,!e[\;`mU'-=oB,J#(f9HDY?h4dn6M,nAT]S\FOItC+=;Oud,rQH1]h^k.R
%li0uWTI.IUGZ6SV+Wr7f!IM%f_HF>k:LXc;>Kt'h'bsT_GD[iRb/9G'PY=1GEf=bcNUAla<fj=(08(t+LurSdP%.Ug49]"80='.*
%k0OQcT9=mi;]q/o^uR[5"f53AkKnXcr(%<7!E1u+s".[(1l!ns>$QW7(Tk=d7I`br+tW5/7+ARp)JdTRpem^cA/4?[@`H>g_:"&A
%VW*/$5jR@\;)&mXnT@buW4H8K^aM_8&X&Xnif39Q<ST'aOjY;&BdfH*HkCP0CcV/$6OcUQ$\E=#['N7dRIJi<c[j*+4(@DNYpGja
%gC"SA8B7F&drXcI9Jp61;VAj\Eu9&b*`^&+0p"/BSl'0j'1R;de^.FG$7nQYaWhG28fF/RA1:q2.0`mtMr_%FA+Lb$B^#kGJt/o'
%;kNB\C,QtXA/'hG@HsO0UOi,j<(fmB:qS%,dnsqb=\4W1noQ@E$aX:S#Z7:mkl;3O']iPDg`o8>i"ZUWq!9-NU".!Cbk+Xn33Z6S
%egRdolSM);1f2%ZZ"oThe3hRa#fL7*n&8A/?s:pXk+(pFeu-I;U1L0q=H#*VN9l6n)*&s/RKrI`kO^)b.Rn1-0l.Mh"A=R3-q.*8
%rU6KqYNJI<Z(I2g39j*JZ%Z$T3F]_,\:b*d==Bm0-,XrNc&Zm7"kg;^\.Af(6h>7ALn#c4Jf[Bm<]rQ59NhuS(p#d[iC_hRB!E8s
%KXGG"V2(SWGmq:dB;YC$/\;rE#L7'k6pm.#`&QtuS7+&9!-UH!B^r[!;DoB&F:1ADOL@R,_N;)R^iWgb*kA*[U.f>@BpGGiBJr[k
%(Mju;%":e$ZmNn*K+aiq=ika2$1Zf@72dqkj[eB9Si2i7b5h3VjJn7XRR:t*LepuK9BHpmab1f]^!u6/+A't*)DTSf,2!@2YpC][
%7A<b/b4*kb7e0[,'&RkIoP+PF'sWiUkT.#HTJX8s(#CiMXT5;SG/#qGr,^_8RKS$_U7g'W/1NV"YWA_g_3b8FFA?@DPN,Z0+^#Bc
%B2rX/8;Tc5+A'd`+P%5.;p[5mS4$9)*ZCbCBT$XE[@m]J?cf(qp;>n:8oIPl*A4785?Z*O!-BVm20%4#(qi<69qr!*lVheA%KBpI
%J4;a4\*?=R@R6S>2DgbC4E:ifiCt!9il<':>-p7QjjAW+QdVla2YHVue8S#)h3d5bnWF?344b(%`5afE'!];GWK%gMm4bkT)b7h^
%OZjLT4;r"GnaU9!=:]]uN6;ab/L#J&Ys5V(9g[bQ[9V3(E&7&,fas%,.7l9h+Y%RI2teA?4m,C7]rd%pAa0MrLt7g6Q?5`k)oj3A
%?BA2\@^0Bm6>6`H3B_Kh.mZHOb8Hu["psO%dD?Db,tRfBWl[l3OJUpCH@q;W/M7u)1q1]_P+CsjK-Ei;19VH+-=V$bCMGs$rtFHn
%lmT6cFrTflLfo8m#2qVtd889<#8BB,j0B_`0b_gh+en*gjU%%u!)Xn4cEhYdJDAA[oV_pj3b/jU!R(p?e^NTY@UpoZ"O*kJ94K>?
%=NmR'AsQ-6*dOfFr$@UL=<ejU4Pji>%bMW<fRiJPK@.'Q@@4-mN'C@:OP2,(dG^bMpsNu$b^*V$UhQ>%Co$Uu0H$5e3B(&sW&.RR
%>'a$7&N7o]N1h-HY`fu46%GQ2a6"2(-(\&^'n8!:;$AfY1PdZcJhm-)]M5h.N5ZsZ()h1u+Y^tl+)0Y"&SR^*e0<Pjf"iQsG+i[g
%1)'?6/`HbW6lZ8hO+Mc'U>po+6=9A(49Fs*k=OAb7^l7$3HeVL!%(We,:u1?XT/[ZKD,R,WJZVg5RYf5^(V0die9e0!bDNM;iB0u
%F9qIcU8icN&t5o^AB!iH!F*,87j+JD$0G*hd(+\W]Pkqd.#oGf@\C1S]=9<C12FJ^/_LCD-&[Xsl&/<S8gkL8q@a!6Qr$AR9gHH2
%g'@q4-t%(``g$Rc==oD-lWDp@!5"oMJ8GE<XiW.8!DU4i$.YYui^Q<bU;aP#%7Z'AViqV.cjZtgAm,L@M9X3A[MdFK2O$_&+?^cI
%pGP'I'ooSFN"q?$C5`"gcLD3uRcahO7+T\Mp=Dd[<\mopr3G"oCA]QS#n,=_@ou2tb%un/;P6`UG-h7QrHJH#32]368JlRu`mh$>
%asM+-CR\79P*Uc?%AiTnnrEKF(pM8"pZ+p5+b:CD^)8V)^P4MbC+nNU"LXS.39nbM*%-7r.)]apA3H\#-6QD/Wa]sVDFetLEf%s_
%ed6W*T[Y5:dCQ(3-*;_6"s]SR?FRJ&%DdjcW:NIB'K<<0RoO!ma`aM#/AoTY.t3B)6<ZoGkt[]-209n,Z+Vl!;D"[M%8BVQ58g+&
%*,C:R.T7chXeap5"d1hq^WR#4&B@`<EMAIL>=1Z;a;!IV=1"$(1r#js7%N7m[j:0eC$%"-k]]GKg-g:aK<"[uPW_Ufs85O.ub
%j,apq[1_Df3Hf8#V6PH4Lm$=%-'i8t]YNOpntj10CB5s2#/_t&0+g!Hb1qp`f*T&iC-2%5lN7i.*`Bs]rF#QC@=5F3ZH=8l9u9H"
%E'(iu\:)6&_4Er%LK7r05j%!;>_OFN?I\MXgEo>EGj:usBR/kHElWg;NF=p<(ToCWERqfZ1UQ/h#q'C,<Ped&fhlmQfm+uuCG]6F
%7BC!T;O]McE&1rI"ukKm4,M8qH@R%=D+l,5KS[Lt'I5gE.&Y-^e5e)Vd<+aWWr6'W1b74LB"^j$EG0)rT44WiT.`Dr:F:`@9e_jh
%;#</JJ<qdRFau2g+jI7*27-/m:Vk25;%E_WJ^WdYmc2<HB>;.V\t"#BYDllX'#:eXfqjI@dZ>VEiCV\"3Lq[h8`*/^U,j?Z3P;^U
%VE2dKMRm!6;P$3CgQ]a?OaDce8VkWd.X5Xh+U:8/T>Lf2YpP9M'P:t?[mPF8bKq@@&Id9N4Br4OYg?b]a.Q"K`&bb^olu$HhKO"-
%!mt,P)V:W,c)qGt<2-ZSc<^J-daUN$ke[_GV.>JCWN!sE-cN&LHp62lkn/E&KNfr.8S;*fOC9WG4fUkh>DlbX<e85`9@f+SM%\+5
%+DWfs/e\_u-4808187Q#4Mg!A[Mi`Q3"&[PRD[BIbEAQj\um)GDe9P6koA7Vi\NRpoFt:"2Fd=Ro7/p]'&`%9FNEP%.*@[Um>s9F
%`HU8N:T!1Q5b].%2-8kHhclN`^Z*M.m,MPgdSKIu$kOclJs#!?/baoR3s.l[7>)qA52rK1AGDRf?h7j=_X4kT*VmZZ-F["B,]mrc
%?q3aW<V*u[#l#]bZZ3l-)Ag4`TPbKVK"e(8F4KP`4^]Jj,!<CjS/>ZA/5c.SfZ:..B&iD+4,o+k+uU7HLZ:5LMWn/SPj!CG<0,?W
%bX<>PV)q^C7Bm5MNj%]1@n1#r3o)3qmL?7X>@JVcPq(dbm0[W++=:7t\Y]!Vi$N#(qm7D$(DM4<$@9'GccCW>Lrm/bV7DV/E)V0&
%mNFsN#F6H#$9<@N'J>HE>f"N!CA/]23/?Oop$6iO'UYO45Y)oo+aq5;j=t?I?>#s<BE3PGL7qQ/HG^C9+OTSu#ZL#XVD\E8NfK<4
%[<,&GW`kBao^V2,Zmi\?N=BoS;:r(1Y=rkl[pf)p<_,h"SdA/+Z+IOdW.=%U+E\WgPX\!^is,iV+?:oK=J^kZqAOJ6@<PU<@(5@H
%6#$IIS7:Vhld]c`ar;?91INoV=Y_lB1q#5D<%>-P//OUuG'/[2TW3GPc5PWc@^?^4b*9JlCF-$"5C.9&DD@Bl^mYq`kT&MH@O*=2
%gP0V^9gJ5hYcMI?]S5;U,Vfel_2um(nX;.^[RMBKR,gi5O,+"Yne!FDE<IBt&Ct*AM3$cR'<F9)Kh%jBXh,1-&je;q%9X`hj?7UF
%2:9F"RH>RK;;j9K=@YbWN>eJF=G-LG79%^b/F"/Dh:,?.dE&ApZ+_FsHdr"*H:6,d<Zu!=^k`7=`VS`C=d710dA!NGIMkC]7rbF_
%&V4"O1tV#kcHGh]GL*KA$d'A3,7TefR`Rb1OMb12(/lqE.\SrV`ANmZ#JaqF0kgJ:$fnc8Mi8SjiT*lu-5.uDY8b8Z[A^&BEAgR!
%do(&bPL^:S'sM4/(W6rLFm%O\Vdo0-5"l4#HuI)rUqQ+1LR/EuJqhhgn2;cK9^$18b(8/GR7%mRP`2#iI%9Duaa"Ee#GK#AkQ0!k
%A5`P@EsV-"QR>n&We\'GL^=#<WtD-#$$67sLeY]mMYtMo6ABEUV$uU(\/NYu3rDI?A2eS*QREl,&1`@0Qi4^"Fb%3NhHor-3Q7/:
%-1t(SglOX]i<^(/@"-<tN".kuchM$7Ro3?Mi2AKSOBB%:jA3!m88lq5(tV5@+ce2u<%![0:O8HaCF%4]<YsaG_<ttF*a@7@P0W]W
%Hm+-=kl<uC*Y]6&6p_'6fWboa6jc^hSjYtN#&lXjhf9:\fWcrNVH':&ZI,G@Z%@Do_qHgO`"]clLp_ePf%J5`W*3Vs_c1`SJ4gR+
%/R[#TQs'$NT\I9(*Up?LGmsstIF/lX%Y@ugQ4cQ,;]$MDV$^$4g#SNN7$Ti3A>b+KQ7^Y8M4grg$mH>ERj+F2E<$M\bN*UX^I[iu
%"]B,2(2jWWG`G0dk4s/>fUK^p&CIIXSgLd9YgTK*=KeDSaR3Z.!46AG`nZ^Xbk)d4AqUAPHqD2\+`'%-%mde"#o@,B,MTb3fF5/-
%RN(W_`#2\fWnhR0'PV;E0>Mb2<mA:P)EHim29S6+dEL.@&iETH#X;(H`L;I"-rEXb[&Gf%dB`nl[LL^'&AfA&K.qb]E1s!J[*nM=
%="FtY4W#/;Ms4+FI5fMA%ne]X@D[]!=JJ_29B^!(7T:OD7H)1c_534:,tGA^-O@nhBgI.p`68KcJX0POLt^6s6<85pl/>)snqW'1
%#3bu'orp.TXnL$-5n?(S-h8Q8%UdDliHt89=bbP%c^KCcB`aU!UOF[Oes`:,1e3K*Y,*n-PWH^ZCBJ(94aD))P/H'O)QV,.M+"iN
%H7h0+dssrr8WK_\K<RU%gNge%FqThZ)>]G!E,qt/p8Vr/#i0>)e7+h<O\6U-O-.p"jN&\3?.+Ao)!m3tRLE<)8r4r>!_9F'EAJP!
%:D:HUHA,s$Uh3ZO4t8\'JaH6**ZUVtjP2H#H<&HP6btBUV5Xu*Ct&bl(]j$?icPPLJclDgT(WZJXbh#AWnFe2XSGBt2F!%>YUUsD
%J%Pu6Qk<.ia]mGm4t?@&8_`FWTmdVm2(1QU25PIj8rnfHLHUXFQ5"dOnM'*=J]$P1(lTdc5'I@^;"',0U;r2C**O4#,I#M=jY3"V
%W=T6?quHg$^qg^Yh4tTaQd%7_`<Dfd8l9PZf'B1W,3IsTX%eQ\3aJC#V(ohj4mX-C;M2tJ]Jrm6,Zs(<0o"d=<eISf=`EJh2?Y]a
%J4bf,0L(XdA)IDJ$R2D)gkI[.8GjH_CD*cOEH3%:Ka0^CXdbKN*r$W%*[jA*_m3-&)Q2XJ9thbdGbjbK1;W/t5d%Rb,:s_g/3@Oe
%p>(Z,_0XI3Ab8!9eg=%+oftHF@YYN:.f?%_SE-UV;%;-&":htq3p50jYRBN#G"0L9bG`69KInB_M1Vi!"kGud3-#8&E$e%6.lG58
%M;E'@L/10Y/ON*nX.r-]#+pHT<JW@s:\-R4GuBXS9R)R=OQJ>=N`:Q<i@g"pU0;2AgDrHXNjZ$'bcK3H2@N*Eo"pE8%JB<n0@+2D
%=!]`D:!8R0PVAG-;A>H[WXqr23,F6s-`JN9?Q8*+\5n_X3\3GS80BB#Q8/bo:WV@Z:.-e&,*3asCBSRnXi;K&k6HkK1T=oV,-.pO
%7rs1^_0k):IWJd@\)@@i-udTsK$IW03b;WT;e:sGYT=A)4N6?j.I[Z:NLdBm'IU)ioroEVjY6&q\+Get@5\OQAonN:;.V08Ml=PE
%Obi\KbI8?^ikn_'L/VH^NLj!g*7=RU1ec8Pcq[s;qsjN%do!3sU.bG5LbfX,+W!Y\W/jFqd4#%ThhPF%&2lQNLA870TEFP^bs5C1
%5uc=]Ul5p1VCn/C9rfLb1A+&K70N2?.`=bMFaH4TZcl>qVGWK6AY[Eae&>_E(e@UDXK.P95YsLp_[4/>kN>f^R/l1fIIM=sMLsLh
%oFN2P8cirlr(KGYAp>a,HY'EENbppF%nObLAi;0]SAJf]$!8VuE%QDp&;62Lnhm[L:c2qO2?SX;i6I$3?T3p.g-Yue-&kWW?BmW=
%1W=+I#O=)4<jEE"c?ckb!Z"]KDISM5@&bW`$_n6=0Zt60ch).f;pCI6W*G8L80Tu(MQ^q`A$$6k*)a0=%TIFBnck-LNaUrU@g0s>
%"?14sV%8;gdPKq:3T+^8"?32TR7a0+KfQE0&'WD!,oR*Ic-#)MAb;$E[_6@j?tbH$0O9>!.d3Qc6jlt:0kNi/8r>^G+/`j8NDft&
%SsBCoL`;:'fF@E+H5L3/T,?>I2!cd]A;1T?ILJ;,'Kl]j+[_,k)6e"`+"j:p37lfgO(R/(hR1WqQjPrYi'X^UY=V^:P@qlnNZY*a
%FuTn6ZfBY3U.M(^NfX<0=9rN$I,)tKQi\Ne5+^"7`]RG'kV?&["6r%Si-"li5N<D^@7C3QYNZ9UnN?b95P$o^6'UGP/YC$D+]2PH
%RLSYX)`C?nXO&?kR84B]#90E'3'kHbcbee"IHc7^lYmUE_]^2R%T,M&3ZaWi9m'UkVL/i!%61a(P%uDUd]A8MjJAM??Jt[c=m>RH
%rl9,Ejmtlk<PLGeW[2$qD4cn%1kojH<[((]&"K@#8*&nUP!N>TV:[080NNq0!XK0DTTj5e2eY`1O47bE4%&u8#2>n%&a%h5P+VnX
%$h[\s7@3)nms$O>25<N:p;Z<dAO-<GrimPSphlT>^m"Rg334OQ_1qCr*n:8UBO=>!\-?4nQF#`JNLE/h&-/!7E?QLD]CZMiD)[HZ
%%N*O[Ca5q>A4R/q;kcRU!oEuc6:TKBp-_nn`k7.1&4d^JgNnpo[6bZ52@G!E\n+LZ#*okr3e<jKN#b#75h@:QULI#^MJJKs*\?e#
%';?Z,<,n@3#M62c5sPj7[:]=?27Z4*4&!"RN+o<E=eLOQZ"@b:`#cg=IhbJHR$\NKBJKX.OI-?W2.pS.8ZQS0MckHjM(ub8SR7!Z
%V"oQ-5->Qu=R&i\m=H7F@E,CC[S=qhRkq[4XsV*J_m@guH\lDpb13M3iYi]VS]m7O&&Y'%HoF@^EBgAMrkb<Kk,V:N"WmPV<Nd;p
%5):%`5t,)C^u^;HnGA`_><BOS4?/Qe)=dS/"#uqHVKmjjZY@k]L/CIZS7qC:,@J[`5V/gbA,e+jM],Y),u`3NG8rTS_;IsE#;@*K
%2<cip6-ThSdE5RZEQfT822s^mVfBm0[D`HM@FR"6U_4CK<hVRK/CY>C(rr*758s+!7O2%M`Ca1Ql7*C5MO%--&ccnIF>'Q@enCah
%3b`+`\#jWR#URIT!$`4CNDd[u*eYM%LHZSC8^NSd!XCtYi@)f6]HT[5%&4`f\@J.&BiRk@U2a"qk4'GRja5@`Xe/Ws,e>e/jU7^_
%&;n9e1<>4EUH/iAc3=>\9M_uFP<hT$3P.4(<$D+X9;q*_=hk@e6F\#J&jnD021<9UXXR^!NRuNZb>r75L-7PMZ#kD_3,9q^:gilp
%RHkRSSY9T6;IP,s'f&ia*j?p8!B4k@[q@Z;UmRpk,M0[te*IO>V?+60N/6E4,G$^,J3P-o-tiob0M7%u`XoVGW47Q,BW+C]*K8GI
%.b&jieM<UYHLcD1dnn&:bDiYhoZ?3?Y@qj2F]gTI0T&mql?Rd,)kMYJLn/>.1Q:"UR-GOa=q?[[&7e_=.2AW!/,Pqi,b>Z,T(G61
%3J&uqL/`Gu`>)cmiClaa.;Lc2,qA0c;k,uM:\8/o:C00M$Ad$3USJ$$btXhhPamW?N4i0m7mW\m60sH+cZDBHSeY&I>%PMsLCf#q
%O<Gfo6V$GLVGSY[3>oKQM60MQ[UV,LMEb2D;5GuO;qbqD-qk\6YM+%VGeYV\CpOFNo1Z\U=p2kQ'T.N)!7*sP;b:p.4qQ,86?fi+
%B2"Y6g(Y,J6Gs:t?Ze0!7B7"Z!2"[1<WP(8j8Ka7e?4Vim,Lj6d:Z#g,,-8`EeRBnN<mYklm=1%/jrEY-V6Sg$kZ?X8""$^ecFD(
%Bcr.`KY4Xe+dkYrcQo]G-/T:t85qrEd2X7&-&=qmP9ua0Vc'%DIRGhir`=X,Lk_p#<pQ93Tn(n%i+O.0o8Fu6CTLcLaOX%d\-m+#
%[iW3(b\GpB@PFL\TNBX8#nraL\(.d/@D]ho\d)$6[Q(4h'+BtV-K3)ub`:6$MCu_-B-AH2.g:"hKOi+[#Y^rbT[6((#Xk2*Pm:1u
%s1]*MX'gM5/c\L+b=\;YKi=fun.lLSmCJ[B5063X/]2tpWbpMoq*9o[#,dSZMk2i?2%%/o`+1@5G]"<$_#40t9%BIN>p9VR5R7i3
%]01sQF01:t'goi;F2WiGAX4&c;i/5QC&:37J3!<gHAKZE'h"IPTu@<4TG.CR:IKU5`\iQl@Ro8^.1s8W)2uY0=E[nh?^'$f(*Zfp
%PH-egVIHoYDTNH.@[o!VO<L@T?r[r'QG`?L3duqh73)M5fX9WC'=/G9o*DUsTd_`a;Q_N<FZ%Xle>4.uW>Uh8/_U6e+Mc:+8Ld,-
%["^CK;PH+)QDU`'U5KgDW<B-rA#:.'Pa]l:3SbBlMDPJt\uFoOK[(8P19S/m?HAmSh\$9##<@urWbYlCYb$XX]ga5Aj1]V-i._h,
%gQ:Y&7Q_t^Q<B$sm%.C%O%t&+\tEZ1ip/@Cam,Q24[g(<o25<Z"XY?n8lW6"Ve=$ioL-*uWS!8.&.UCo$1blHe/D=N9H4,aX%Dp)
%Idiq>jeQl$Lj^!PSurD8pIJIAOmhg5H#7pCqlb`3Ich&cbSlX7`+gZ5i*o;gGa/gT_nCg#CX5/pDnnMt_]&_ZjncYq7281<nJLnh
%Lq&S`5Xul>4BBCYC`uKj_1&f)Nmg@md>ueE@B>`TV5hJ.+>I:k&>pRGK/j2Kb,Nc7-mJn:k_F]Dq4q]F8h@4ERGmUk+V-Rf!4//$
%S^b\j+OLFPU^P#aA?]I4B8(m19L^-@;g="#aOMte&/t?>G[r2NR$F_7e!VDPCsZHoCk+PW\O1r+PjP),e'BR/&%ke81,AYC3$LWg
%o2NXrLlXlf"\*"Acbh"j"/.EA:]_$C"UG05b2S6YbZD(g"?a$D0fJ\1jI''_]g./-=5rk?IVekcb.11a)M0b?oEJa8BfOp8>kA,0
%F\T`IM:bMuDe6R/_]G+O2<8YUZQ;/RGVFmJg,hGbBgtsV)+G$`)YLe&^dt3O3T@l/4Yt5V9H$2jW4pa#3&LD2Yh=tm\CW-f`TU;P
%[mrFGM't6PLmC-4N9Y>_o%8AMfar<2<bWP-StcV70jER4SC-"6ffCTn-*,Wbo[HlIj1B-ggn7th*]"BOCe%4.@9S9R"q)L"4);[D
%DJ#G2)JkeZ0HcKWYlj!FB!(5ZEP3g2fe#(Q)T,OiPoI48E`RM)!@S4lC_ihIORq0l'75.rFh65DnKY6D7-kJ)Y/T5NFCZ]%NlG;7
%*K#SNQ)#jJVq/Ri0t[W>n\PYt'<RA5[`0LP\1E]kO='hVlIHl"=H4rN"Na,r9*4jhV(WAtFnLn$Ak"ReUgt(N:YmiIYD<%MN^u3J
%RUMGDjD$i')<^2sDi/k?`^P`U;3@F0*);K";/RE^#n[=fQojgD5C(WIph2S9+\hq^WQ'r#14\\e2CM<;,L`34BTO,e0V'6`-k$=f
%%n.'^!nopL!s7n_(ngD)(6NUd3#Lp!oNo>"P_Tif7/^fCC._sh:DT=,ht.]%UXdW-8qJH,^mJ"c;f)!n&r6V;1no`dDZ-#]4:,`:
%,22%@eRl.:1po&R0?O05_'j*t)KaW.dKGIo]V5_@i=tGEA3]Z5*cttc^pVtQHk2=;PdJXTdfNi)X\MF*BJT%P2a'0E*D:o?057P`
%CXA2U1+[2d4II(iWM7_)*F28AQTf\lj*CWm$,jufM+g<m)"4PJg!`(j*Qm'R\i&8F3`2"0`udF3`q!N^D(Ji?`sC0<-8m:N:c.Ep
%$.&@A;`m".V`+>[F>"mm90o:fk_`\>/*%.2P)>bi"*aH.Y6LDaVTPm!],r-_<Xgh/6<nV29MCb(?;-#H(i1q<aiDF[FOR9f+c/1R
%4Lq.iB`WoYHu]K[^nRrmRPn0_JZ_:/jb!Un/q(!?RDiX6Me%+[)#c:#K+Ye4>MgCd#0CU5Q/9cQT_2=UKMBF-`Q]m2%EL\GDK8U8
%'[[U"\_-7&>5^HSIR4-I=tse220:6p`;IV9(f!b<p-dCApP^R4n(T&:ddgecZnMeSHa7:7ANrGO@kd-9C`*ao/W*+YVhj;lBbI%B
%L$MnIQpd40*Skj$3n83@+!62V8t:/fRqP!/L5SIr2M9$pF,uXR;)f]">#QWW[DijFENOV*Y\91:gr@&m)Sm7q[QZ,:5>=1j>#%RO
%4qS`+bee<2Z^=7GjA#D@37h-fXS$7&6'!G9WM.!f#XsrQ`3Ndhc&k_QehI_Nlh-#sL[#0eg!<@A1l%2n[*e-XqK0OJ3eU`n\H/c<
%8E!61\eC05T:CYa$YlF<B9.leT@5aYd1F;2&^A3,"`E\\k[rAqBInUie3G)"@SJL@J:E9r;kK_<*[$6m,82H.)"arNA$Foh%(qI-
%8^plH[>gBKRYOV$bn8$Pd#:`.3S:YeOe,?j4MFs[2bDErMggM$)@3=/KJ0/nE&L4PFsC%`k$Y_A2-(8`6,Zfmg9Mc\i-ZU$B&>MV
%XJ#h!=AA^8fP,q\P:N.uJG@R?<@U?dA.F#N"W@4r8:%2]_/b0-+k9G'AB.55H?)$3g&1_CJ'sDWl(=$NqHr^iqe)Q$\paP#S(D$&
%]mKYDn'CFj0CE_BqSgFG/oNZ4s7Gb=2eU_YYE,&u[*XksqraT_UX(A4k,[s#qNH-qLHfhWT<E^:s7JUC^]&;JI=IKAIWP.2ri\D0
%^&6lj5:dIb/i9hJ/:V@6&Zti&#Sj=51?bVToS)m;aTF`_kiXu&7R&ggXPYW0[Cg+F/PiD,HG[kk5tOe)cj\%b*Ypq5+]XdMoE\4B
%;jn3p6"Zjpc7"C'VbmhYcP1$!E*=iS/^m6f1;3tk\CNuD1s8$eLno))B[7l-;3m<AM,e+XFDu!<Pc@r^2!1U?L"Mn4#o7!7"O#iZ
%*MnUh.c4\?5PNO(3(s`QB?);1QX`DX=UU@h9&>ngW.bbMa>qN.dKASQH)7E.b[r#A0sNMhP5fAB;Hs9[ND-]a->q$6782l*ak?i>
%>.VRu0Zd6&-RLI/X2j\nXX*0eOr0>lQ;^0f+otZlS&EcjQ1puN'2a7od-2-Q]$qX-,,]0,`%6M(&p&jqbiCuK7h6!JVWguf&W(ee
%3&W'TKkMec/<@O\-0s61i)gJdb9P0J:<F1_\Zh,gD>BB"C-IjT:dl^CHASVp#Ke3$,*uW_Q4?(I=g/t"ZbpmW)Eu3H4UVi%?qah4
%]e75gBnY'TZI_]mj)]$j>DQ$p>@c'mPJ'f'e4u5%1'T*cog2n)jK?G"ld2FG\e*!?D`s?'kHm3sa8+pE$AYMBC!teCSjtph\]\Z_
%#KGLm7HrJ_*8\P2cBK$0d).&QU*XPX1S(:L(-:QrAa.$hH%LlN3L#A_WZX(%P0g@A#g8Ve8mer4?3`*b:s_S0Ei[8]cSmg63g986
%jPoYi`15q1%'J5nK1+1#@To!E4_>BkVEImM&!I+>$5Ibh$4j4KM2%3K/1]00#!([1g4O^bQnFbQ"Ks,C8LF-HV;!-O1JHl.d1`TK
%Tf?__#P8Q1dRcY>"1feLKEoMSKD%R=@@=F`08!C!G-aP%f=VI>8-5Q&A)L6;9F$?J!\uoPj,b1&dtakip:].9@ZpkC:/_%Xa>PqZ
%SCYCIi(1Uk`l2,@Nkn@W,c46&"VYq]Z.7D[0P,XE\9=HGC1Ak6(fH'EpjF)d=p+5]Qmi*Qp<q>=aRnSqASc`-7aNI%:6(.E.7KlZ
%E?aM@-s\;X$aKB%4c%b3-dOYOPZSZ;JKA*S'l?9u3oLY;V;MuC6n4@_.<>au4%XOT3@p+OBk&+o+snPgdU[[jAIa93q5ZE>:re8\
%n:E38'K+7\Ye&l\?CDSSeK1I?;V<4RVe%&>g'COt&FqljJ4GP<A_I6s0:L=)j&_k08]FRqo+kf!LRa/a`mI/6eI+MTf<oVf6$qMu
%8KrJ$s!X#Q+J*'jfW<5N2q)P/gIBgu2<ciV7V#9qEu4NNlA=q<4QG$_$A3<,$m$nr.EaLa]L7S>X4"fJamU4FB.'pG3a`l<&;u^;
%.H%=B_+kZ[3-p!g\=Sq$0Y;H&A)bC6+r>usf@eiZjDZHYk-8MT#0S2DR)iVScKDS?X$\2r8`+q'Pn?L(7jk=(+eY!=6n%kh>B6mH
%c%:/*ku+IDM)BEe6s1f@N^f+QUj*[":)5nPbDAnZWjNn^;KWaQ(5Isl,6)<e%&e6!o>K3-iHsq^AR[7!i2*Y^Jb`o7!jZ$m$/&6[
%HGKbeADasIQr0YIQ>[LpFjqIqgde4Xapo(/etL19bF15A4eHG;54!0n9gd6QZ`PT>*"rFj#D9c:&;bP%K>0,>Kj5cSI%H/-fY-k6
%:s1qnXID5<otXJ!ge98GOU-8Y'KC;H+VKj;+JrU2BXX/PFV85ZNRh.?5M:<<(_JXTh`&B5&Y@tR9N)3of*P+3'.2:A)3bX`!lF/p
%-+Q?BnKsXU(PV,TYm[Le&d(m>oO<cDD%!X[m"<LXAeu1P@MV+2j^s]$,J>$IH&u:-8eCZWGVAZu.Zq6?$e!I\GB)YLMTS^SOcaCk
%<;ddA8J'9DpEos79l>lQ;rT*L5c.dICB^\nZUdTUH-[meNgs'($NecIr"eHDQ'$kn0uUm2rDn&KCjQdTWS=IJp2?@)7@@<@ZL"i'
%Lr%*H<W#reXqZbWTUc6AW\HlCWaY+sLSTg*Ui.dd,BjIN":f+Q=Y*XjbCHG7`%n#/iJl#f=gKQ&p8eX`c7*P/4`ne\i,<Sb1&7=U
%QHEldnrV:WaWY(8/E='.U\FS*:>[Q/9q_1>T.R.&:K2Ao<o^sI=fc+@Y/3Y]BT#>\Qbo)CHoOFK/$]Ec)/YY]&^gdObRdcJB[Oi8
%9T3&9C\R*MfYfP<!P3S("&oQC_QUi-$-tks?S)mOG:[Khl2*"VkSVCQ0,=Z7HT;jmXubAu%eMq#4+W:RS2>iRU_+>jo@WOYq@s<(
%JM.D#UkD*te=uf)*YKIqaW,\'\BLI*Dj;A?;0[m#as!I$$3e"8n'al]a%K2B`(j3!CY"%g*)MI:7N6!n+W'Yfad%mlRuFfE]PKaL
%U.->`N$4$:7s\iO&m^j=P)N9kEc`j7&QjAqBpM*?oaY$%B5-fe(J8>O<%F6C5=R<Emd89Z9FMeJ$[+J@=:!$'%pg>8^2b)-mC`!A
%1).i,q5KIJ92S'R0.YV,c&sh+:F=UNl(Vn!!3)nXWEh'5d(KSPaLf53I</-kAZ/mmeC<WZi(gB`:SU,.$U)ULX3sF_og5`fP/K('
%b_V'\J2rp8f+IDake9g=08.`7kE6`6.PTaaFpep;_SAIo%[7ae0e>i'$(WqH+o9?e1NB7i"3XS1;2E?65\$RcQC!:Mrcej1C!*>T
%OCslu@55@7.8A2+>mJN`9QDSPAF:(m"=LpcjK)rLf$Z,Rd$5lSPbg_b"*pLg+36hc5\_51MDUV[QDL=MXo*Rs/6]K2]F2S,Tlo95
%eI3=NF,ks^V2CUl5`nokS_#HLeWKn[X(^R!/=1KV#36T/OuYgf>BWSFeLU#5V1U.d#dR%3<@Q*>AW*i2?'p0J-8q9[NZlMkYY6j?
%>p4!PnNnc6r5e%HP<FaS_!K9F(*<]fPo!1:<=T#lG-!,7ZO&XU9kY\Jd%,qUZd@C:^^%/E@H8+d7Gpfo<`O/g@#X^q53JGZUS"HA
%,0RZ&kXD0=FN$huAtn58YmA-F'D^Rk5P,(A0R)^+3Akm*3uLDMJP!^BpTU/`?5G9Gg_7>@MjPK<SlM>Y0iVHqha\26Y`+-19Z&ks
%g.X8[bX/_CUiNLIM>8*q@(pW,X/!n!/A=t@.n(p"'^A#Do"=6=.SK?'Uq?oG..jH[!_3=>TlJTD9k6(7U=hj^kX=9H*)e#X`fCGV
%O]MnEI[JQ0RSFL9@I_$SC1uPFb@+nT=2Bl#jsWVmXg>p\kCr.uX-A<shK^?92`hXD*cs$%aR43<61n<V_ko]X6d:EmXuZkMA5-Uc
%c/[@r8/r"?l!=&>ae`JkF<#2_[3ZV.1GPDN+*cC>@?aCs$_Mo8XpedZN(&-l9XMGRVCu#Y.pYS.G4\c$JSe@Qe6C04mt]7h`7->$
%W9K7M8u"b_2+">)#@<56/gb/U[%d2H-UsBtR=38*f7Z):/iFF&kG1;/9#TRr5%YF+,[-W%b$bs>UjhjO71d#KN3T0XM/@t)HdsK2
%@jl0!l!A*kb%Pj)>7prXGB;8%`q/92:)kF_3,E`q`gB1oOEK^J0b'C]=nX0N4bua(QT+Geeq83YA0m^9[6;UadN6J;_GUBY,,t:"
%l-8HZ1kd9o`P@1%8f;+ENeJq!qT\jsdS*c^VKsO_<C8N`b."\CMOhj77VP19<XTF$R:=:3iY!La*p`^GDOhgQFq:cebq?'PUlP]B
%]s,J;lD#YN^c^9PGnpOQQ-JCL\\=+_G"#XfSca/*&t<'])bfH`S;aFQ;FT\g:;h,")oNQ>:cb0_3/1gO,%K,lq(.H(6l8@-71ZJc
%FWpT69JXu]=["$c,KMEFp6!81_Jk'cLu1KF6Lt[658&?D*+kfCc(gs@[91".DJ2J#D,j[R;2FK=\72>Z,^ag02_i8kMpY"QJ6cZN
%KZHb7mj9!0:=+VSom/B"N?h1X4=S+TR)G*=ZfLl78qu'lUc"M?g#K(qE"P4cTGCXYU09S6*\=<N>i`^N=uAW/40e";KN685W$?\J
%Zh7/UfJ4HUN@Qbc-1VEo`$Vp).`RU!)Iq^,CmOJ(fZ?`Y<[]<3(In+NhN_b>M+mYQ#:I*(#sF)MY)uI4IS&FjGnXZ#2fA)reA>g@
%jOG27@Z,0Ij\/H*D3"^SAogBc!(E;]/(fbkC/r7r+QcAiU>XJA)3.\E-cD0C<<Q^Jj!f<u@T6U"L+03IfYpfCF"?eil"q0cT+L#?
%J*_,i2R3et>RKAaQke'R;jrPJ9h$`^S8k(!dT$O3VPB?E:ILbNO,+ESeA`KV/Arf?$6hP&8!=0)G?#NE$4lK$Pc5thHQp)d\(n2T
%HFD$HlXNpdL^^-%6n*o-Sg=S:@3UVdk&FRX=_3a)^Ec4MFj<CYQ:TTW*8#(s>un+A>H.Y=Y!5BAAYlgcGO\N[FUQT$BE5Rh$f?3+
%2gBcDZ>seRF_Gt%2_[Xo9nHX3Tn0@?h/T5-#,Z$dU_`J*d`dS\%o`EZ@=kTAQctDA,!oCHXX;p`K']FF$tte,_S<:=j',VmUu-NB
%(DRQ97@\$4m`R%U"[ns"E`6>qb0J\>nHcR3\4(R#c,:eq7."F0P8:]u],gDp:B\k2,@p&lE261WYCCnGoD?/CS,:Bu591grS\p]S
%J,/>)Iet/cRs4Fa?iJ*\rT`rgl0>02q%rr*k[0\Vp!mtUIs_(hK)bhhjn\l:mEO>?%j1SEc]8"hYLci7b<I%$"2)8Dk2$!TgODE`
%q9nd(]=YD)D9fo0]R#!MEPjBFdD,VUk'm4QrR^uYhd,_9rf_QoBE*AO04-?&hY_)od9k2%hJ'%Z043Udqb/f'cJhmJ!4:S.GBu%%
%?<mKog\V_l6,RP6B?aud\mc_1h<cZhY[Rr(Ci9ZQs8)'-YJ0o-ltcX[WEd/D0c+T3Xi]@Io:Hi^pKu^i]6V)KnVc/bNmBP[2pAo5
%rT<\Mm>B#&[/Tm4WU(O!]RP/"c+C$7S+DOKYE&PGK?I_pDe26o[pS!!hOd$Q_5HP1j`Z-d]Xh4Ofc.'&`aT^Go"u.<k3VKSYO4%B
%hn*H(f.`^"mC+r53f5#Ngtfs(G?8Qd1c4"=FM`grEqA_qGN\^5Ip$mokJf$?c#X!]mi2%WRH2=/5K#CUEPI#!\$@fV2]pnFg[?c_
%cL,W><c#un\uhb$jmB[lo8A+148\I#bOZH%pN,r`Ge%a9SJ,8b4Enb&k7P.O?VYLB^Uno@H,&dYI%jKBYk$`Rr6$`GhAgre:uuk<
%1'^fs\#-A_Wa%T`ErQuVgmbp^76\B\mHs#iK/0nPb1m=Sb<_KL"^Q!L%mB,s#@6bjbPcWF_*OFb#M/"b9@Dp&9kJ<c\`</\[(1=C
%fR829Mi5>T>^cG8g$@D]Ir#AG53b6#;R2J_!oD$6,3IYWf>"ijbN0V$B9eW92rE3!^:ARage7<!C'*.S!rhTRh:oE*B/a0[5J,a;
%Ro^T=f5!A*lRfE\202mk1qhc*gU-Bplob$RIIlUg`Vk.$_]I_.hsoLqns4X3i`5gBir%W_g"e=]qVCP``Qaj2H2Nl.M;.@ZGB/'%
%pAXERju7apXnf)Tp3?K&m`jG\`L#b#=)/5US'AE"Y:ELd.*V>]p?MA>?bcX7H%gQ3!;^-[*2VJBVuKK?TNgotGL,*3^B6j\2/^"h
%Oj!e#n9Y#aMP^*O/ZQ^6eLYlLWV>^:A%c'\9c<!c\!K&JVsB:.5Q>iCpei3J;e=:%.J184Qb[?*]^nqA8"+6X`[A"F40ridcc:;k
%G)ZU*GS^;VSFL"D$u3/eRa%JbRQ8=Rc!ej\4?(TG3V_FeLGaJEcWapYB6T^:hd0[FYAkuZe][a9SE1j_ddR+gG]e<3qm4IYm4F+6
%EBf`HO0S>+b^J7.LAp>pmS=Y+P'\YFXM8F%F$DkQ-k9*<5F*;N%oGO:c"?9=5'b+`G244LC.i_7aQMXXp?IRJMi7U=i=@s0D7kP7
%%KC03WpnitlcXKf<Wil@[H4SFYKk_oo(Go+Hmtb$/*=&pR2Kf6j%#,^r+,B\mG3!DhHo%;BuM=)K5/JYNU8hIHO7p^-)Hm/8W5Y`
%YJ6.ln,hTX=63TPqXn1RDqUc[]nJ"s:NaMJ3!siZ2XL:Z3+VdP>B^#REHk9cqnH8Yl0f"D?4gZF\)d8Oliuk#_mAM+"eVtP8tUKX
%G&q_Ep#X0C`VDQ#o'oMaFk$Dp_K55:o6^D!D9_O[(p!IT>+9iCh40B>+U5(.CqCc/o?E/&[5Tc&R`Xp>C\*J-\)1J^SR5W/gt_ql
%m(9rYEnh9+F7eZl^[^KBpPo2X,PIeh:*7>YkAJma96VnVUjJflIC$ef[QcnB7tsmr-BC6^SBKg&n#5V3Ip9;s]7/;^A1m<shT#tO
%H@Ki2MTNMQR%6/dK9>"q?<s[kfX]_mL7V\!J+gd$ilpNk]g',5GsnLj[\i+d9]m@pe$R-/..+H#Q)i)tMmF.<jbk1.rgVY"iPAIA
%D,'<Xp1D;23BKHTGQ$qm0aI<Gk/Q/E<aKe4?EXB"P#g>+`el]uhBdRI[ljX#Dq&CrD,'ThJp#c^o6^H4o)A25X/9K'[rqgSkXi7!
%[%uHIQ3,t!h-R:1=1R!b22ef1bNi.:m,t!U">9bBmC4/\;0G8l4+Bha-u1090BS0UN^R$X%D.'D%Zr1:(5_U6M[#cfUem:5E/f2\
%Mm?rMp>tO!`1fB!08EYo^XrBaW!7S]Mt/%pTiIV"pL@(G?W&GtR`Xp>C\&`[;>`XD/BXt[\Ehr;7E)!k^SFtr23Z]0*k1UC.'*&N
%f<>$DNC>jW8[0[Z-F5A7rl=ZZlsi`6WkJ<9Y-H6bgi)\p<=rPmS_-/GMZ)4a:HFAMNo3@b(OlALYCFBU0OJb7_Q[g6Z,U6p5l0.p
%@+cn.`ni1TrA^%0"(1pB^/b_kD8d,-ptC;&*Ve>pn?lW[/X1mtp9MWE$ej)4=$(B/Ir)92&Ct0PY]er>#Hmb2c.-Eed+;LkDsuDC
%XB:(heKqh/%`#t>qY4i*&(bL6&fe(%i>QR'RI#t)MJ,DGoH.*;,Yi3c-&jM#iV/[g]t!q;e_(I?s2`lD>FEj3[a9n#]"E<:n`KE)
%q-'%&O\jq@SDF3S'kYA5G$FinGB&'./5j:bQf`fko-8j?Hq277+.gaYQY--)1k*][gB1iP%uD)-<1524[WPOfJ)ig%m`=SQ^YR<.
%s#sZd?N0uNQ@Cmj1c[2Cdm-)$nR^<?UR,!0n*cq5DC7Vl<MArr$0#;NSU\R3r[k*>0?iUbnpKgc2u@:?dI&Abhd1eVqf^B)J+r@k
%54pl*ZY_IL'?k>I\\JP^hk-P4a3:#=NOC0WY*d+J@6bcQLg.5'XI-nHoA#^chsGho_lWjM7:(+[GBs5Uou6kJf"5"ej2Wn54aA>T
%CG:,.7XcQN`>A&8m\;#d,PQA"Jq4E"IL#oArqV%nA4XFes*%Ptm_SpMnGaR-^OPQK%Wt>QrnkKb4:_AJrUg,qoLL%]i'(?o8KN8n
%4)-M'EVn(ukBl^o*dk[D/Kh,X>$nFcdb\ZNX[Dg"`RWN+pE-!\e`EF;]jDZtT+g_VMV1/j_:I\Y'iNlLRlBfm?C]g-/?7%@)V4EA
%(L*(A<j;.=GAe7:C$hnTCqerBDqA?"4XVoH;-H,XQ_#@DY`KkVA-WLq_*[k6UgC'Tm"L\l>p[W7b[aLED<kdhd8rO/e(r!^c"Ds!
%Gk'JX&7>G$p9VM=hHjLCIs-&+qQnqgZ^YV+g'@N.2HWIR@Eq"4YKQ6UpWqAfgc1%%\ZJe4<_V]9CAD3em:TAP/XpI'EQK-d.IkK]
%`hrZ#'TH%&f)'aZf;Rli?(<PYqqWq*qV<>RXKk#PrlN_ZJee"[U\tJib2]+1mh'tCNI;DIZ%MjcVp.`A#@#IOfJTOP0YAA60*fD<
%_=7$6U^i-GKAkp"Gkkmr/t#k\n=ir#[_Edj0>"<:*Xj\D@<gHH=+!#J5!*7>qGNTV]s]&s31u+h;JC>]6%<U:Zo_]8pf%[\!&d-8
%hBpmnRQ9^qfYe]$L0Y%R=L/MCY_fKr.A*F6`K!Fsmf**.Fa=#aZcd!0=GOq:LDegK5E5soVVQJ:%nKAUr&Cil^cVQKI\\='2fI^W
%Ja$_h0R^mR*mg5`kY'YkVP!nblS$"9H[YeiTh*Em$:rRfb0"(BX`__-h\hPan'frk'T.:OU/EF!g=FYT1Pb-QkJeM@]'IsXkIK43
%Rl>7=mccXb?+aLcP5"@hs,Q:;O/N0YGBW^l.Kd;k1G?.<p-D&mE>k9AmUmV*U#04k?58?pmC)]Eq7(#lhd3AlK<g6dRa/1a/#KXB
%QJ/&][;HUmK\A$#[)7oD!*_Y>1%nEW-jMiR*pGM.DoZNE^lG[3%L5)8c>'rfG39BZN.W-\f66:aa#M^+p;DtW)VY/CWn=>(IeFEH
%Zh#8!oOU+,+n!Y$n'A.cH[E:M(0c7^_$O9=>>"Yio)A4Zr&JQOL;HOLrci^r.dM>SosQ41?EF4jq<%&2`NS\^rnk>GS+AL0c@biF
%RbRWFe(3@u,-Shu\p8Mu]B/bbA_$=?)t3>-*>"eQ5XY7d++T(UkWJm4kWCL$S7dg0EPU^^Eb\h!k&IS*P]XLO>pI_Rm^U44k3hqh
%c&9Nb<XKn?`ouqIYX+o(nFO\EGMg'F>&Nt])?jIfqT14lHIj_TT+LX*S#4o#9Cb+\9%Rar0.GgpeR\A#i+pSJG!+PqbnP=cfP<Us
%?U'n5IrHgK0SdODLW$KN-3!)>A;PL`f+_"NnHBfj=%#sb/[kFkb,5JMDRX_uFZ8KVc#)XP*438/2L!O1%bf_3[(-o,j$B/]$/4/X
%Z>/:h\J;Ud#Bao#e@?;5F\q62lJ+[r4/d3*Fm^J3T_su&\Q+jH)g1T$^Art*h'@/NV>FW/\$,gRGIHYM46U`=6(`L<"W18Gd0Dpk
%mHZe`'Bo1d%,B_"8f[=T&%E7!Lb#aLah8\Vp#?$_Cr[B@[>:k]@/40E\N2hPo4LQ[])D'`r:9iBIpHXaI!bn2eJ]sG[bKBD>?OIB
%Dsl#qq<Z9^q@t$`]t?cecFUR[AA,ooF:W-L]P-Qf/]1__]I_pJht?P@f4b$Or+qW@YgIc$#OVD,=68^SBgM7KpG'X4EY!Z(Xnf*O
%2(c7^EdP](f[o>1%tCjdfr^'sH2I$tc.Vp3g-`F(r1Lk):"*3M`cZ4VV=fOV?T?DhO"9YTfmd:6^K]e-[$KJ#fWkR@kMg7P?RsCW
%;ql.`M4^Fm@.J[pn`^jVr7[[DF3+@Y;\"JK^o9Nfq^Kdl3:Ee(qTHX&GC+D98htYhP8tEd0\DGJHli=K+LiI\]73)nm\;6DIk%\g
%SR\Z)TkH*4U4Z/3UCZEYjTg,a'YQI>r^YOthsiYg'?2KirpK,W_2/%JG-?/crqr9^I,59H[&rArAe*/'a)&sCm^*c(kNCQ8be`cA
%5-3^CJ)2BM3kN#:s7Z:u<<7`cH6HX!i9kmnp$TSU=-rZr5J)<o/ou7WGF5)kqW#*J@G1=oEQp]6qW#(t*tF@lc84CLIht=I'0a'g
%qu8SBUBUHiCiEI0Skm63pLc]SkpZ8r7(fT6g]p6ArB_cUaT/Xh`4=RAd(rL`o50smadrSJq'2g;H0R&XI!G\dcS^')LAPr-G5ga;
%5$PU&!DB[6O4M&t^OH'OLo.l:m&k6pIe1:ug2/b<Z37n.$M!ZN1ZGigL\;c6j*6:1](7$^a"IH>j3q/![PFE2rLNLf[s@lF(hDXF
%`R]HjT_9Suc5nr:aH]#SqVrqj3.;I"P!.HeS\II>9d/6(Pc*](>*2OhDmsEK"mVHH%aeGVlHR<(Y:f:PB`H_m*=bRi9ZQm?hcsIb
%a'/B#7dFpY2@nff2qr25S,DR-c1rITP)[,S8+,S[bkC4erS)cB1OieEqfKupfP5@2X#PbWnAi/IS%!=e<)VAb1]QH.+!2l-mC^sE
%d5`=r@JGpeD-)laQp.+q*=dAlr9ER?iV[oGlQO4;dtChPU(Mc`&Yu%IrbLu:roMC?.u)!L9VekAH[GA]V&+lb3EGtm(Os$/RK)BH
%5Pa\jk_K3.b9*n%\\02bfBr>4r)7h3q+lM"NkYAhVa#I9q1%5Jn*%OAX.DrLIJKZ"Q3-;?$SCF-,k$&#M>g'&f5gN6)>sa?6XTdW
%s6G.eJ,SOsqu.uarT<>Dr*O[?HMQo/J,&8(Nbcu6<V7pbeX$-6OF'(##!Mi#(4,>N9!@c@M!\2U\Ic$[9!'nSd(m:A@X@-N$:j:7
%6Wn`6KmF%#Q!_PEct[rnEhp-]C$SqaP(p*(H;p4qQ\kNeF<Va4=2eh;^b$4+<e+lZ<JcuTj],'`+8pu%?+Of'&Nq\Y"n"AE=#K,0
%*Of,'eV;(]R1[i9bHIqH3X`DjJ"c]5b1B[TrZ^[T?_ub]A&%rl)V-&9hdgIZUr((J.KlKbV>HXKc6B/fc$<LNk[2l'V/:Od/BC.]
%^4itSeh4YYZ$9QoL$f;3bL172qC1<MQd#iQ$h;V>/d$@)*5/Nlk&S5Y5(E;!599W4He^CHkq[YV`TQFIn#l\0HTqqP5PpnP_:Qjr
%HKDh]bH.&TBK]AAa,lVFLtBrB+,u:(;u1MYWNcstQd-tB7_+J[V^+XUs.?uKgY",<Xon,%kT0M3!;iK/Gs9#F-8B!IdfTm(G\g_)
%<3\"emNMlm8W;MTEZH@u=&jaX9Y&hc6a?]e(qotIp<H$9**0.Sk<F4+SRj="lKrf`rleP]LTGs\=8,eY`Z<RS1l_*rSu/h>X7PY^
%%_I(#[G3LPq:Sn-pG6UN*oY&_*F>\t^><?9aj3':YJ/D%)<ZKl]Q$$Fk`!WGbLr!e-_ZOk6EPUHS8EA>*I>Crk4Wsi4]QpNh"cl/
%ou3bUCCbf,^V$hZhV<]igo_qNY"q(HV0>'E]@ti/Zfo2p)/X#(+RJ8>f,_Vel/glU4P0K"gm45R6T-n+&D!Ulr!$dIiO&2llVS:%
%8QZdo8R9\cj/Y-5deb,2a.Fr)PY-Zs4gW9OPg3#d)\>%hean&$s(M2Zh;UptQH:^>^TQU1.rFg-'l2T43$*3r65u;oIWSpHeZuND
%0EYsd=jJ(5LOQ[Q^BmX3q)3`o5^`+o]QMZZ3g"@*p_T:[c%Z)4Wh5XEM;Qn0<1fTO.uZZi:K*b]X#HsNLL'TQp_WsFJ+^#Fbuar@
%5Ns*c6.Fc%T?]_A>)dTqeCYGt$hL6C+-!!HEFu8YWF\92\$,d(.&(d5ZfUekM3`XWF4(jOs,:"D;&%p+rRitSWaem`pj"j`.%Ul,
%cg;'BMRbq^T)3`O%DKU&I8=_4>Z1:5B3gBOhq=ic_XVb!e539;*Eub7=Qk[*[BK/K=]W=lf.r$P]ZjOSXY%`S#[9A(oKGG_O(.p8
%U@eP$g\YIC`38r`MLXR]miPtq^%l(Yi\5jG$ZFACQR;I.DqT,a2!O:jO7gqe:?_=1>4(X%:7.(iekD:>cTH+5OJD1.jJcGVXQlIp
%F4U*LL_ud^F_+<7XNpXL[(6[F;A0jZSK5.ZQe?O'h;5m;fM>#T6%4"1XP,Z"S>rkPE9cWIpgL*iWjHl4>Ok0i)\3m5A*5fgB.>:D
%if):=dMngH9Us4FDR?['56G(V(LbuuIBEMA)S.&"fsMI46dl+Qr2d0FNaA4/A@8mCTTCO-VaoUD1_k2pPZBs$XiP[5F.7Vu:VXI;
%h`<6:mL!p*V+J%0c3/)fkSWZmG&7`2pXI.KZh\2.T35<$f?$4'0[\g/,&B+fQ6Y!1JG@n>I^Ufqf-?g:Wt8$D9F,:CVr7=[[<pht
%B(7Cq3NHKFCt!V^\e,X"*-.)?/RJ6l4V&(:Da"CT@@>-G5OE'!bS3dNj&iChc/U$V6k%8b^!2>a3?3<ff`C8`(CPTX^'4)4%Uos@
%X\u[^c=O_jjpWK19(TnU!o\._WltkG9'<\U%S&\j:\0\b3Ho8Z*T">'fcS$PVQc"l)A6;YC'1BTpr#"R0'bM>KD$h?=!#i1o2s<i
%p8p,mDYkn*mKhFqEB-MXP_!q0)q.*t#NbX&U0m6q>cTKYeRNlO]#,T^d%kk>0n$/oTf&7lB,u$`/=u6L*(DOK=oXkB:H%hUCjc6B
%Y+)0o44it<\qO(0Dk"I`+L$l(B$d$8.L3h"(EKMU_DCjFKn_\3pLkQheFHiqV:"*ZIo`%4>3qYN=8Mhh](#bAC*&Oo!^M!*>j%r&
%m#&+107EAd^Sr"*<$6*2cBhT&m5=pl#'juC]t*UG\4P""pP>pC+e=''554C:%6^Ncc`fS(D9nF/E9HDS*>cn"GciPRbIJ".n`Q%,
%9tL:-q(1NH;"f=I'Pk1*hL0\%[W2Yk9K=1T?B4kZ/k[:1F?cCga"6V`bg34<`MHiFh$@\Z?67;:T#_N<f!b>0nWK=u;pf#<_Xg)g
%f'->8]-6]V<9M;b4eE9i](^'<gPVljZSV,=#=e\iDW9i:r'SOifD9FB%punADu`s-1=!=C5*`L"\=om"^[EXsoA98Z%J0C<o]L+N
%^ouPKrci/P:*1['dG&L6CgR-3#YTQfr*DA[S;X`>F3S-WYO`fY@rWY]W9;rGdgpXOR.c2$E,Nh]Qf&bLppBYQq\$)odu''+1^dAk
%pD1J5o3@mMaOcKVlin\:Ym9(O`NhNA`T6#c22df#/<u-#o^L8<PP6aI?e,X%?29Xcq\!'J"&(_Z9BofdMsDp;E*IO():%=R*jL>P
%$U!u6S]6ZrX-9_U,>1mo#$&_hn?GE:+@7-AQu<^ooBID:H>o'?%TDo&$E4kf1EhiC;N-\O'd%B/1q+1i#TV$XVtB.,_RX*P+'r[%
%`Yk;e1[K)k@OZYd>r`V4p/`B\j,]"'j)n<\%K,o8JWc'QoUgglEctQVN(hS2a?F5!G4:dq@Po8.?e6\!`IOFIlg\Ip"G%%#kE"RW
%Y\2Ms'1^gN#[:L(A)2'KmJ!3>0u=n=D=C$")gE."djk=kqDn-,nAXY^?%lP)\N/\NX4tKg<f7nN<!<>PeWHIZGNbK['Pi0#<G7c6
%lDI^&6ZB-1O0<=-d-(qP>jRF6Q*;JS.faUh],-Y3_R/UK`W[@,akQZ%9*FNtG+1&qfKF.u5,[^pc]jLZ<L@]=f/NBlRsY'+#NDkL
%o>OZ%erb@On:&\T:Y[n51ieKb5(hPrY!`*HZNK`1&[;7.NIM6(Rk"GtbMc)Q8\7XH@$6J9K2f`J8YH.A^'EN-1u>lOhRJIB6tXNn
%hIg$7i$9C7AY**G1S79lWDTc2e'V;37S*J[-[pB15Q"?Uq/"n*-M^g$#l#M=U?Hj@/bZ)=8H8=K%B]PBrP*+)0<VLQL6-$gn'>6j
%s6"[9oJ`B?B]/p]"/"<IQ2U?CK6H9iD`DfV\p[rT&Wa,Cm%X4DI[\?$p5c]3QTVpi]YQQ*`j^?>+*^#8S`D>dZPAM.a8L,:l$W5n
%rqhb@cb9<5ZY^Ib!HXhaFJnh;20`CP:5Uq963+PuaJd6'_NR$EirI-F12G/dSnhfXemV,0mj#?h'OBjt,HGA"A(N-uE]er%guCVP
%QWG[jQud*^*l/-;p/T[]JE2p?pgL(%>39(49Aag?`=/RIb?9t#o+L5'XZa\Fj$i`ud"#b9cVGUr+iX'D!P^TiSh!mOE(M]ha"4ln
%cV3Mu^]45Vrr17&`eCNUbOBp]+4O#GF@a?P,j`,2#*Js:2?9BOb)sJ!XtnmB!suq#kUST1XeX[`n1=b)fiJg:c$\L_6.c#XSh+/5
%(hA_3(uhY/(3&]sF+EF0CcmYQSSZ!#KM6622Y("OmVM'k^=r/&bp:Ega4L.L6HV:0AUV:e_k-4=jEWXMF&[%:*gXFc8nY6X4&Fmi
%DFT+u]3c?7q2Wd&l%e(W\m$#me5%&c,qc;Gj6ED!E.gU2&8m.A)dsfZF"?JIC]pOHG:\!a3TbJ$``9_u2Sp'.8Zg9tY[c<qOQTkX
%,:L<_BUgi/g^$F%r;EN(583gSn7<iWaNm<m.<;%@LOK3.&^J9G43Z*B(OJ>tqdA93ETc[ap&UnFF8+;l4/.UU!'f0B0C/0!)4rM/
%&%`@$30XG+)."%C#9fl<+ljs>?Kk#A)T%`VDs@>l`0%$el4OKRY%\0^-nuReJapA:K;4/A;;=NH^9`],-]ctcIK9L,jd+Wn,p;Od
%@m:/_$R(ap^&\g&O)U%Y!L&PV3VnOoJ:K;<M<Hn]i6NV$qtXlQ#^P=OMB([@*jTi$XLYS.b4(ES.UcMJT0U=TrJPqj\BX\9/X!F7
%nSkF1cdi)dj$keE<tH4,Vo1]O]T%s`TZAe3mG#hRk&S/G[f>uk_rnZ2*h-Jo*R-G0*:0b?6)NYg7FCG.N1ihhm'2MY0CE>amlN8C
%\`1Cu]XSeI:lY2cg,#bH<,:A)dRq<ml<<fVcJ;];oZbk_k0a3-H-lR^MjsdWl8rjLI1O_.m8P=Qk?na,`B!Z/i?&-$YmSa"aJX$s
%#YI\0kgfCgK8&X1\\Y%T"2`Hm"ce\frJ*?:$=\s9F#<&3Ld"\/EB7'E%.U%corl&iml8lmICZ]Qm?9b`L!ODk#<1q/hf(1lHhi?Q
%I(7kMfpn08cZ0%1C.o[-ocL?;Pa22c#bqSJi0/`ln:NeJk;4U4G'qWr,i-Nt1,8?6MdA_:-![[4*g<nfBK-T&[SG-2j8Q9drd%,L
%kML4XSB&,M5.(d$3-_b8iV.hS@aWNf&6s\RV,Do8hP__#S4rbDro29q<7]a#/OKD+,@^g(29@COM8TcGUQZa?0gtBg^Ri:Xa4$%p
%UqebB^K)3&)7EA_pQmOFhllc0`(-G>rpFEK6]K(bqq9l#!p*eha'YTBjHj6Pm;ILEb?o\pled8Tp"_X6:aXP&$.FX`p8EWA_Vt?t
%^HI&V/,n?!pttQ"J*9EF6e7lnOb`_b#B$^9B0YF^\$,e7^Yi;nnh:!mpsX&7TE"n/52-MG$\^!Y`TU>_.NULrQj'F$(e\@H#*/+%
%$23^?PY1D*4c=eth#I<@hV*T"HgpA#jt8bpIuJaZmRn:hcBgPcRrIL:@Sk+RIc10f0<4;I5(1=Wc<on+hXEPO`V@^4UEB$`Ht`nM
%CR*1pqY.c'eM$sn]3kL4>9EY3TDn-1?[VV<Ig]#ZO0mTf8W:2uGr6O3mgnq@9R.92R'Hb/I.^BM]_c[r/3b5tP(\+NaE+\Z/cO!2
%`r?W7C;H^?du*p/9@Y,<W5_O$1,G9[jX8O5I/.ZEo?+%Q?QFDtK'[mVL%0YGO%L@AoqQrt\`1rGc7bZji$E"^p`dIh^5SK3VB"XL
%ANd5!fp\i#T9'!sHk_[F"\J0XUBC\un@[!AjCR?4Hb=_u+)S?%h]/'<Dn[q'O75lZ(CMQqN[?4.T7hFA!$:'()A"UseakKoK(V8[
%H`T<"G<R4:l*JceN31]/A;"DNe>22Kp"hg2!D^kka.g]CV.epl6;:\TXN'QACSSqMmT?@4a8Z`8W_O[Ln>"$&BmkZsj3&Rj_j>r+
%r7:\b=7^Em+oBJH#1V9a?Prf5po)#R/m`Q>,8.i6&,0X;0Su,Va$7>O4MY6KH+3o/]8W0k]*p^sDn[q)TDG;_h5:nfMr:*/3'=j^
%cP>,o@=YtZ*o*2W!Al5+hdjYoSaWki0%HGR[;!kA;n4!2d!??3r0jkh\-tj5/pUt%n,)\R6S.DGK6-G0d;9Kr,5^?592?.Xm.VOj
%mMSb@hn@(>mL2aF\3F.:CPD\mnUH4QU/dGT0;e=meg>X07L+bC:Ym`Xh[#2#bJ2b3b%DaLWjh]$r/UcSs.fgoibh]8W9b4F%mcGE
%XfPa+/t_uN:R9Ta<l8$C^96<4r^U';e@)"mM#)"k-oL)/N:)%o!h8bnI%#7T?9@4!CRkkIEEQr@^!Fb!OnKjI6akF#4?hV5Tq[>2
%deA2R+"PW,RBN)5GOMe>4qbVjrZc6mF!_a\ehr(@+Y=A5]H'jQB^/K9//(Vq#6F=,9@gLWpK6b9HYhL#59KpGfR)r>]XA'rFE/A)
%U0#C9N83Ufl9lifeU('afjX&q_"4K_bk:D::IbA(p)T&#cC5r6>JbtQYJLQ+hhAeaF#[8cTu3"![+do8T8")b[blI@pcnF1-!KF&
%b#,,l<qMG5];6M/%JauL>EAP/G8m_c?FOqHFCekbFCiE8I<n'6.h9OB!9LLWI>>f;q<6]p!Tf)@<2(Z50&rD>"j,^<Y<JP+lHH9#
%TC^`sI/oGO&Q,LtbnnD"PCF:k?Qs%J=PhCUmoAF]n1slC_p?Men^6scI.BBTZ=U]$qO``Hj/`rQ$FIppL_(b)M]AStC2Y.TMWN32
%IeIED[04JZ"pEBN.+%S5pclVfj`:&WXrD5aS):uggjGN3[hH1raq^Q3?^/SLZ-BF]qYBnN_FL:<4N#@jq481Y.UP-a?ho*<QQpP+
%JGMD-CpK!6L8-!PX*lo&O75RHp9g5phog;>l2;M>oTgbIi+#]VcaWG$C&c3B5JecK!2e<)%_EmE!_b%!8$'Dij)p-nI_F&l4)FXn
%eK1EFc\MPLIhQ:>]d"o"*>XYkR>,a,<:,DkYTYq^7m-ce0GZfcK14S\o?mTf?^*d3CbrEg(LR$a086EO%F7r@`EI/$ELm^&KfeJL
%kC;%Nq+r=shF_UKp2=gb\kb+"Kr'&D4-bqt#mXddi>B$$jo9]lTi#%Ep"Aq""(9F1'[QgGr?!utcb=&if,8lTs5mA]Zj6-GE6f?e
%_(R/]^)6'_1YhS_=jC8L5ei5SYB3a9!Z(G)e#^#>s2&dl^fYUI5CL6ueAM!Ha"c=ubj[joT&F$gm\B=PJ^sZaJp1RD'#*SPIIV-f
%I?rdI'jk.hhS4OtJJ,N"f<VnimOr)DpdSB0%.H1e_k0&@<bI6=m<Llro7(fUT7,&TY7pOhr'.EQ5D47Q`Ph!YiFfq$%7^+'\c1\D
%IeUHmJ,AR3rR]t,rqF2-eLsYLpqQdZ*q/S\oiL&MmB?N0ms`Hurr4"/KJ.9@1>`'*s8M/i^O(51OH<uGHr\I6_pp4S$n,alZJ`0Y
%2h%-_(?/+R)K>`LGJAJ(m9dX.`HT)np#"c@s3GW`ro<JGitB3.a*59YK7*@D^\dD:rqlNPJ!d/'DuWA8!)%o)M``Lds5@aZI47sP
%2GZ`gS?)SR3e_Q8ch<*Hq/coX_.3\l'CmtWn0r[Jo*]iM^h<eNR\O\5!':Kq#+EH6aFPG`#cPC1pL.Mr-ht"4bV9*[!Y`L+@s``,
%ZV)BP7@[]"i?iS(#Q]5*#1";G0iJ>g\sReXoO*<>8E"`A)dt-bQA7Yp:dLFjcNNTQ^gg6RVS>nq`H7WS!p07g`!9YI`+%C(fR\]k
%&b=+#jAMmK\&JJn%;21gbBc#"#:<*rA3&805a:BuY=oV<r<C:a'*qru?@sB,!BaR4fEU&+Q(J/0T<///`#7PR#1.E;fSPImR0S!.
%*#^n>?R$J(!+M<TMZm4SlFOWZ_WXV040PT.+$f8@TO#*dn-$Vgs)-Dl^kP+%=!KQKa0<W//T!!E;eL:Ipb9*q1Q?QLgQH>M4[Sm$
%#74rW?PZ*ZPIJMK;6r4^SXKtqDP/rW;?22'!MVW%okWrCI)MKC+4'SNGp_jrE,#'/*>c3h:Xn;L#+fB=5d!]I982Fs35dh2)$a6$
%ASLOuKn/GPii^-OaL[[*_Q42uS0Bj>AB])kn!D6XNS=_"+2C/tciRXFjJTop&4s_6J&h`BB/:.(b"j-)Sp+KYrn^te7bFHVil!Aj
%[(N$;BJiU1#DU4[%:,8tc\9_J%<1CHM1E]^pNQkn4YVDS-M>de%R&o_L='85abNE,Nc5Blkn22K2=@`&&g8a%6)ZHQ7L<J-Tj\N^
%,B>46U0-hOm]V[M8U1>'\9lXO&9iMH$Xn9TLtK'_A446Y[''X49pgC(EX.I+HLH1;G!=D[@09!AcX$.q$YO8+;FK)><PUIgp0>;#
%CF2Xg%l3gu\K,l,cAuOf"q[*(7;m-'f]G5rNQS3Z*@C,'<c$kZJ6AHl:>UrQWc_4/Beu@j<jh6aIF53,8k-Sda*On[nT34h$!4[S
%>'AK/ZG+7%A1n&G[(RcU0;l9;$[k_C7n42r>YI!9CUNptZ*@`Wc5uCT=M.'/;11o9R6ObEjJgUlJ>]..CmJ[Ah91^DA;pgRG=^;c
%=bD)<nhD$45'!!o;jaB!]!tF^=:*l?Yi_V"iC@f1#`cPBGji0gNt[4m$k#XH5p;<PqcKTCVr)bG?Sj2c1u)XgVPeskPX60cHSLs!
%G1Fk*p[Y8Y3@n7Qi'-:Im[*`tjL(g!%r:XM3);QK9Em'"GXc7+G6sPn:18/3)qM+IB)?=06=qZofd&6XV4Eu9d3bQ:J3$%'n+l8t
%XP+R9PuSpF4aQkKO`"2IYcd!!QW"C=O(n,@Pe5IK#/FaR-G?=%\;o(7",c<'0e=mlk_j_ql):D,VJ7P?l'==+8pl%W$.W;Y=lupk
%f/%Hbrd01*7JR2K_nV7g;n'b.Q<C;,9tHS6khp$NiZOmVQ`V]$YHWis8:YJnN<$kY#tDYqUG/Q^1u@/mK\8_]5L.(bH2?qgIdciV
%`qT9!6))>F.<Sg8OEF8,FOj3,q4+"m,sML]\t3!mIU:CE-/P!d%!)8CQYa>8\eEqn]C#0&4]RX8AeeGoG?F!i3d90GY]/NTL)j`e
%ohomoMc/D#@nb-A8m2L]]sV?+@;f)S_>.b3"S$`i#8;3^ZpTAf=]li_g-422jP`.l>?%*O!MC6`0D?oJ&E_c4RR\63+#`:#NNpSd
%k^0=,O=CgjDV#SSh5(M<XREDLVM/%$H@hKFg+u(MjHWE+IOPk2BO*NjMk]`F,\9.IL8/mN>'pD!DF\tFZcd;,L$A>jDpTGRqP)Fo
%0*YL&KFL<l+W./UeBd)I,t<%9:PZ?;j2Jo7RfI0Z$fc#RnrFB3lr+Cu;!U)X`ih`/AjDgs*jqHc[+Do<,"I;2Yu8(Z3YA0n($,r'
%)j^'B4X#p7*YJfUP3#-V&9+-8ojrq`Ko^BU:,WXHN,%#=E6)9So@-=YF/q%4k1rHZYNPWMTEaWG<m6'rJ2\+2a'#]tXX6&7ZNN#g
%5q(N;)?FYpA\/k00<PEeX4kha<&Q81Ygt)/1]'c:1r*D=>54C@eaM_SXJIEF't&'-q%fJ8*8n_-8`nV^9toL7f+KqX9C?H32+=)]
%HDiA"4V%0;dO2e%B'DJVmRGZ"omjqH\fMJr'm,_C21;Y?ON*E):=?ZBmfgA-5_b9nrK#c_Po"j'$M1IYJKZZ??2o`GYQ%1-r#\/s
%VkFSGJ]M+RV:CXq)#6efMmdG6jM_L>_'W]T*Y#'lo=8flf[Ts3Z$U8M$IPa33D"!ccJOXa6UEpdD-WU<SudkVWpm.kgR"Ff!QJ@C
%)E*fTGE7p/K<&1V!L(l9X0fJnQ].L\a1?#$QP,+_SA`VCNZ+t'asnbn(2EoWB;j"L]Z2to;;Bt-FM%[3(uOk\PF2MZ@$^$BFNoX%
%5V:U4X+)<qC;UP$$T/(1oOp1e=/*[U)Cf#Oa0+^U&:LOsK^P_O/CT&2)]8R%SF=bE/FQ9bGg[.rbpN;f!%TKkpDoq5?miPig]<N)
%'7?f/dr&URV)o5W.4^)J#1ud4Oj=;cpFCWmg#C%on"Vn^]VFmaJ)G]KcD*MZTGiB<ib+P'-lRh4d#"LGc:\u&a;RQ`b_.Gp:Ld69
%mDtDL,J>j>*MeZSo&3",,@s.!;I2YtKfQ,bN]NOmFMVE4D'U*0.p5LO!`"J2g+*[FjQB0:]L,Qo08U<t6C4'7>1hYLHXhZ3`Fn_M
%AaX/Doj__GadOj<8bQ)2/0(iAJ4?W&X2@sDX9IWj=gTp9Ee1$6&CM]_F?f-;fZ\I91%dt_`/l;(IE1I<`X`6)\Up;*<WPYU9VQ$_
%c_9B^_:U!O..446\o,a-"CEV@SL3s!6UgHgLh>7l_r;9,m[.O+C2j8Li6l"q:qPa"^tdEJ%8Vtt#N's4WeVJ4HcM-1BZkZUp.r62
%;8tT0NZ<$,3)d*VZQL:W32_u(!6j*Kh=j06i".o46VQYUNm5[3-s1&d(r<?_G/C(MLR^q&D*0gur&OR;N!-<M(DF^/4BFPJ5TZW2
%0b*3FTpJSXRL*<h:<)ir*]Z\$aI?2$;a%M2&cM=Y:N7RGQ54Tn%lIA\":nF,03WqYK&g=4q(C_RnN:j=)slsCMa-UK)B`:[=i_QA
%I2GDT0nou5Z=Vjt*B2!B8Tm%ZQql9ZThEK^_N#XZhQ=^/0@tbAGk?BgYgBJ'E2Qm3G9DO)jSA*.9QO"HX1&t%[;<k`B:!="6!dL[
%>b7G6'286Q$&,M$qX@#D"=O&$OIIj?.r9^*/AEEqSW%J$P,ms;16N6m-69F>Q]8p+#e"'S_Wi-NNE,h]e+_2qW2`;VVIieugV6qE
%,OPZ'[Qh>FTkBotF9Taj*<_k+%aV,#1!esDF^B,^pV^^>4Qc.@B/RK3aWrZSmFE0/A5FoU>3Sit$C*n=9V%%AQd(jC6X%oA9n"TZ
%!U7=LLq:#P8^TI:AJeJsXP+GYdud'E%a`J=,;]Q]Fi10cT6csm.,9NW9acCH@&`m+$1_&+.=/RdrjBt6c0UX4UuG&PmYQB;q"P+-
%'KbD%ZXkJ[YB\l`d)Nr\9Ti%Ujj1e,he=BRcaTsP5RFLJ%@Ek8'orUk^+$>P;L$9`I3l6A.@/Z1^^Q2ph8i>Hf3R_#$)Vm_f9R&i
%n#5q#e"p9Rc\NWa1Se@%5Efq"dc*nH=<``oh0TE1EDK6kpu'Hu6Wj]]gQCsBMl.)pimJ!dTA!_3h5Dp57BQ[CXXiso*GJTX;/8?>
%gV,mQH.SN%*%;@>>B38u1)S%*7;B]2/Ed5,Z8mi4T,Vmo#t$gm:R`?6'=6GcXY#gME'r!U6Mu;fO4_@45$7_M"nf+-QT2um<pN#u
%/f-'&:":]FZ7iX_-sq#?NlrlP$,Y`Q<;QlrKGRL(#To([PQK^8<?Z2*T7YW.3kH."Lk8iI:DQ.;Lq"^X)4_#@#c[Fu5j%/NfichK
%V1aV'8rmLV>N)0T*XVmU8Wdd@OInNoMhI?9YG4hY@hh!Z57]El7L)gU\din899ALG36h*\k@OI75JR2)k8I:5F9b0\DAS)"fMG8Y
%Fa_5l`9Ib>,bInRnNQr$\Ps"g-,Kor.a'>Ejkk/FJ*JY/"[S^-,$S<a#&@#B*TQ!a\dY2A%d+R847iEf%ka\KN\+_M$fSh<e9+)f
%*P'\#3esFC6mm_SS1H6j2jpo.8[:k*L((=Fgk\rrcbM,A.IrPj)FAb\K;I4I5"@u/F#&.QOnsD[\<WEMkjOllk9[9sK/E^2N4\H<
%WfX2SKYM:k!M$//oGnH^*\-?hNHNb^qY`h:_GPPs&P;Ae>8U--6g^/4.ebPVnnS^`8R;D!PHI;Vs7#K4:I%Zl-0^Ue0r#Y[+j"JR
%3N*?0cgq<$TpXIeA@/62=]h,=Y@7M3)Hgef9KiZ-pN$;Tf;'balHYkB.3/M`#Z`tVN<H[6Xl+(rs2c`gO*?7GUqJ"R"67n)r"6@`
%Ou9'aNsrpWp[3()5S7#Nh2(1%I.K\F7\e_K@.$$iO(llLUS$?Y?jV"djP?%$CV<kYdTcU#'.2[S:OAkp\<tR`FLE-QmuP/%PJDlG
%CQhK5k0:^l*sem42'T,l)iad<.`>s"Q!)B>J(Y_"WIX59Ap9YaL/57*TaH0=kU2'1cLU_oI-VP8jq7;Xa$\>Af$;#']oE[?SiL.c
%#-+Dr-?9^Eb<*_DM(UbS36i<_1ZO3R>;6d/K"nTc<D_g0LE*4:0V!ncF$nk'>FBaE5^4!C7\$&ULM"N-__lru=&!j=S[A]Qaj#kM
%EGs:CH+6$KG#fq@"E-^:*.D.Q)\<6b<lTL-OY5ZE1#(j;dMAnZ@)Xo8nJ$_=1YA<jh>rY"FppH@.?KhFRI2)(&ka9%.%C*.c=+DL
%b['SmV-WClV.*(bkrg^/P/NDCY0IET!2-108MZJZAE'B'`2)e=ku>8\H.O`=mf7/,VHp6OQZr[(Z4Lm1D1<;@:@TYj26S)V)t3^9
%A1-W"mGX\&[$G+')t&a??Qs=ATebmEJtIMe]XlH#[4-_SST5f_8Bh7lBrj"ZPS.H^b-LG7(P!2TNU[3m08po1(;rnN8U/b+Q>e=%
%NJ.:irL%gS!="aQoIo^A,0GSc.E@=!@+P\%J1BVpNZPp4E@U\*8W;]BVuD_N_(dZ3=Z^;PeAZQ-+P5/ZE'fipgeZRq.7>\[Y4rkt
%,OYn5UUT32Q$<HF<DRWpAUabKk);a324cgI,QKH[g5'/MQVuq9.+ME'oe>OaQhu0X8@%9<,uTqe6&<Xka1mfl5Dr`CDfq)i]is@<
%.&$bNGSY3C@o-P`<*q45kR:W.UY[#bB&5o"2n4NV`l;X5=M9<D@mS\\Ed?>qF;fCX/+16UVuZnXh.q<P*!QZD4X/@HKdJCjS\Su_
%9s.D=lm^:kHR,^<(oj21a_6/d9Pk'oG,c!)*-o)Z_kR<Vf=l\P1,+;CJ4W=6)0;,7_+=\t9`ZE,Y%hk.PMiMtS%'CT&!8Q?DU<3W
%S</T%7*$shq[RSa>7^O,0].T^>b>0iOqeOZJ!H=LcBoOVmGg%s8hu/:jF^88aCk*DN6$U[(<k2$/TnZLH?DZc=3+qoEr/O+7?[pV
%:pMO1H\;kEZ(9."+CC!9?$lmRr?.'NF_[JAQ2Rj1^](o3^AQp5A/==Z-4HegH%B!o+:a4YHETQnh\;u1e/:H@j+05%"\diGo(McA
%-Gri0-l#Xm(<8<)NKG:G>g\d>7oOGD;LiF)_T,AFMG;0mU)2VaM')FFXQ-B5%>uU;3B`0)CR-p@q-].B)9)"]^6\M'6p,*FH]sd(
%$!B&\R10acMt#PCP^(AX!s"^]W>4;eg<5778#RlG&b#2@*5Y=XT^Kdog/mR`32p3!9jdR>J(u!O3#Ae"ca=pC%70_p_H_)g26aRG
%P6.[iFr^HsNW,Y&RT@'MR:;kKW/+.E187.P^!Z/hbn\tS\SCSciT?3.$$qI>>=(JQ=>Lc@Ga@-Q2\`c4)>%Sa.2s;)?lbpsea*ci
%np.G:liS3K\CVH8)sj:h"]Cid'%qjZr<@uS6H*>M/9CWs!As-U5oD"2/K<*bP\AVu>aQ=R[WWu-n-rRBFgKFoq^kK`#@roT.$#+N
%!`Y$4BES\C\h.q@<=q.qI6f!_Sh<!9Xrmdrg".jp[oAM)!RVRjVs&Jc*<n4qA'#T_#^lR";?CH!Zj\2M#19OsXFr>X%os)j%u/M2
%.Hjg;3jr>9inD8RW&6-noKYa9Y8E.l4=_tP<pY:>ZVSJMd"n)*U.EPH0gR7YoG(iV>D39,\Y&qdA*X7=7#D](3d^R?//4.u0dhk]
%50hg7co6rS@1=ts<ebkErV,!JpXq^Xq/Vgl\:_CW=5:QJTk?@((\7lcWFS>ung;#iPnD5?82P22SX9E](oik5SWgX5$WM4Rj?9KG
%_>gqHU1=3OnNbX.D[D5=D4GPL2Kp8kRh3gMCo6MA$b8qZe@6Q?=!rrf%*q4[Id>2K)Zb6Q"BokrNV_Y1k?>O\;1O37o2N62X'G!3
%Z&#5t)UZ%P"e^c`^oZ8n6'o5$*(jM8dgaqQX:DR3&2.:1&Z5;9$q?r&\5&hL3m%AWQSSU\Au^Ph+S?NmE>m>S5.cSf;ed*HE:';a
%4&0:7CV)#D^ah''T'B7`s'3Ff(%oFka$LODS.^W\>(<u%*iq$:+HBEO$)'&m[c,[OASY]k'![AQd]?4s%l`2tNc)q97VY3mgnhZ\
%h??[hcm@0t,@l`50Z5lJRIHVC^oJ*>3^Qj$9Jkks#E=q@fuPFCOQfWXIEO<UN6_s5f_o?C/d^LA$b'W@BZ8UZ+f`ChiBY?rkgfcI
%]ujdrOK:CpZ7_Ld@)OpVnm]7$,qg^)$.+dYYt%4?!eu:tC$j_"J*'ZA;,*f)REIsrn>e-6"E<Pk"bVDP!DRPE.qO52DAABRmUP2`
%lr$MYa)&g(bT]BhRKLW$jeb*AR8F)R@m:PnhG#-JV&lg)O5MdGQP9O[V0K]?1l8R9\\]=ZbsInC&Ct0H7j<H$q`DUMIQ5'DDHGM$
%,$7P4ApF3N*jFPD=E5[FkcM@MkmZ3W+mrPSdAT2OTiOGZ7D._Yot.bVMr@F5QF(!T,drbI5!cOg%t[/9RjhqN6$(T5d0L"kWl%2I
%jT)f=Qh'R_[<8m7O7^";E,"4\F>.(SG9nhpOPd;d<_;-3ZRL,H(7#n6d(5.\`26gC=be;a*SZ%cp(N<YBW00pA9KGRoG>eWb\63m
%UG)3B>Y7@0P"29E2<!Wa>8YsFn-MoXiB2Y%gqnq#G#.1[<FFFTI$iP\DU=_:JHJM8,D)lBg8A\/H_:ILBaK'=oRTOcfSh+CI$bM`
%Cm"+O3CA*Jnsgp7>QlNA<gH;G`H&*h+m:HA3<AuBWjE^r:I*DtH!_OElj[.D0.D[=fln"l3&Li9%ASeJ<.[g".9HYWn!+^E4:Jf-
%7;Q&.f`CD7^hP,*?%'0kg_8>HZkkYolbX.V4T[TbY5#:7=B\F$C+f"DmX'pprLHKdo#5B+3Ql3n_X*?kK^J=%ju2pEXV$Q!)%[N`
%HJSSB%)-UsD\s#\M:WQ@301T)=>B<+S`^)]JP_"VOtubYQF>]G)@?,,qd;"$-mJ_>Mre\'WC(Qjn22uUVmq=\Yc.!Z5T<Z=IR[-M
%aDC%A0s!\s\2`fnc&[\+@tUt$UG0%+^fS0rWL_nFBLNL#8hB']/)0I'h,RZ-Za)"2K`^.2U*._FX'4SCH5^_n#Jfu@SXaFbMf@pZ
%*:%W=,^f/aW8?`3J.Z!k_G+LPOMW>X0*Bs]gSrW!NtI]TOAUIhr]+^,CS=1u/LHE(^2/k[TD^16)q7BW%PkDiM\'Pb]ZnUU-4%0T
%>8`,cH;\Bt#/$j`D%e(EUaVo>H7e.:"=;-uU(j#NhH^Z;I"FN&U&HB-BKjc&;KI%iHXFsJ'GOtsbEATa9?:<%Yc?D91`94r,-pe$
%Qd$We"IqMCG(Gksd7MFsCK.XMhZ8)Z5*`rWXTs]<aEA/G=C]^JM.Dq\1Mi;M+6j,6cDWP!4?JuulIk=<'P;p\/-q.XdON-OiFft6
%eNqkdA)BfsOD\&>mgp!e_;\J1`4:P11L9C"_$73b1(+ZG8N0=o['>R%N4ee#?iZ]?9LUD#;N21+YcS,@&9:^@2\N=%GZY1V0lh-/
%dZhnN&g0%)ID!E<>Y\f?L)d.4q3piXX!p[PUD^<n`1/qVG=<k?H[-B)<[A"gf+l,QY\jt:T[We*`t[PD5iTKcZ-_Y.jE"mMO.f`L
%(3L&]>$)l3!YAR\ULD-BaRs)%<%T(j?bh5a*hmOiQ/`#^D3a.,BWFhGX44YW-jp'.=Bi;IEm@n!\SN%aa%j)^8gmZ/R(/T(#W-=1
%-IuN3P$OfCK]?/uZRgUhW3]h$!L:[0ojkg'88'Fm3@r4T5<LeMZ*`5KE0@'VTT!d$L"]43&M0"\0gb*m!>ct;<$K41C2JbIn-E14
%HKn'[1NqPF1g;(ULl1jGX]$]ALrW0\9McL.Bl1ghC_0**^Xdh:"^,`H!'t9S2?NHU.12`]]JYU5g7aYBI5A_(o.g_oHL';g:rcjZ
%Scd2'dcLXNHR;gT[>U-e"`JhACsqWqm:t,[T^p[M@^S0U'lX;&Ijo2L>)AgP(/9d'c1S:u+Jb1#3u<p*6h8.6IVemDVTV]7qJjsS
%385YB9imMFdaY>\L2E.d>&skf`aJBn^)3k'7uD]"cO&S5GsAd=c"P%#>bugkNYiU@C:Q2^U,+P.(m2onEIH`UZM%s8VGJqEaP1L_
%hGeJ$KEHIW-_am\kNioMXC9*YL>QC/\tj*g"!o*8hnXq(Uff6]%r9kG<GhM]Q4_o&r//EgZ1RU='G7Cr<PFEePo9$B\r'=eZME2$
%PU7['#S+C=B,B5K>;94110'cMK8^Rf3r7HC<fZ`-^q/TK?k]TW'"3t/OP#-dj"D_k,c'QAhR5F.@GPB0%(2;GLd3AWefP\UNCE1I
%-1h`S@[j>91<HPt!iP',Wq`"`^'&n<K-Db[WrJ?>BEbR%VfGuEGZs7oh+W/QA#4S!^j"k;1V1J!jpns&0J`%I(1e'8P):b#e(Iob
%^dJeA%+p>]^\tbFqIq9;=f\S642*l2fe$M.T>kmT#2WeD1,/DOle?lj$f9EPP$$*o_Pu@<EMAIL>:c)3*W>?Uh2n@.al3b>^#VB
%Y+ZWD!#,jB[XE'"[R(aM&,KFp-&%2RUk3t\0M=BCZW_T+[g;NjA6_%Rk[=CTKOc<mYXJNI:Z<N^o`u)2f0X\_c<"%I\<KGTW#'Z6
%^;dca*tVUmg&d.m7>/H<"%?;A@)=hW8.S_L!Zn)_Wjt8lgmSE?H$SX8a94+R$k77]I0f+G>d]BRL0^Ep1<PbP8->O]6O`F/W"UJ1
%fkGX9ZT=Dg:a>oBUG00MSXmYrF`bXjRVUsonb6%soh^B9h$L/c^E69agolNRGn(FJG)Qf8_%bB8Vj888(paKEjf$6JF8_>\a<fWR
%!YuBiP(8hI;=#g6!>(M?,6l_L?FN2H8h<??P/mjEC@^0!A5Nh"\bBr,"Q3(@9']p7eZ?-?.U]A:.XQa/d$$^eEP@R=;K<2g1YMGS
%jXI_-ghst1bWq6S6Gs`_[I1m^[ZFfI*65%_(H-6@B>fP8f(E$Kh0Fk,_OHDD[S%D);Ckc:Fk&V%=FH'4&Lk*CZ(fm9c0^l7L5Acb
%U?4-6$hs3aOWK<.*F?4/LPCn&3q#.u?K@YT5&@=,ang-9I`NH_*_>dFG%G#i)bBB0Auc*dSHsRn1S62`q`6h$^7fk.mWS:K_V\iB
%od(h*R:h;l#B0bh/RiK@=)h]0a=^#C\$MZVUbG>oT6eBOa:g"0%LUg/gF*NgeXqum)n<<bdTV,:PIrhsX7BpF!0pU$N#*NJ'4qs[
%$dJUrWQAe`0ni$8#2HME1CfR]^bXg*._,PuU6s?",%L-C6P(%T!7uul-o;SiqCuNqJo,#=_Fi`PGbOqP>S6Kf;`VZ*h9@+R$O@n!
%&V]nS^k?\I-#*u!<3o-,52'FU#W7>F&621r@EGtD$*7]EOp,\dEob#9/%bR>o3r]h?jdj?p6,E_<@C`W=g1O6IRc@BpdY1IHmAn-
%DqQsLe^UN5oH-e=EmRg>+u+gt'C5@25cf"2p;ePKAe'nm<so'aX,&3!/(K%l3mC&G2;,Ppo-gl`n_;JV86bp]5TB,!>2h@ppVk9\
%O-#(KU3TcAHY@nrRWh_D`^_`.UO1D&q-R12nGFDUf!Y8ASLhb8Yd;I\OJ*l/f_b%ZcN++d:%"QV@.UXEP,NH.b?O(/2N`1BL0Adc
%<[Y.l!,_5RXdnO1Ft!e*\3r\Z%<A#-iJJs3#"%D4<f1gLbob0a2B88L65N^L"BuZdYnm`O1?l6923`d2C*G1Ll*p`uH0lR0%Y`fn
%^f:IZn>rAp:cj<ja!u@G6H&1&7rrCh,p_!,RD,?J@Y1?YO6s&M!_.^q8!F6@#O,<1ft`IN^aTQ]8/%\oUr,>Mjf\11J/YL8IZAhm
%+X96Nn&8`GaN3c@5/';cM46B<.gX]/Y(<gVWZ=EK_gsLoR/W*gFT3\HDE3,+a?E;cmCLgJa&As#!]Y.W3L?Rml$^]1=^jY]"K[;C
%s,8]%:oT/gZqs:$0(<pW!4:EgZd&iq7gP_E2VL&<7>ZY+X4Up-pQmJ+bgT$,EErc3JYb:c,qmZ9R,>-o=n_j5oh4(>o-(_F"!OQ>
%RY0,G!%OeZa!r_Sm3Ogd-H?[qQ^uB]#gk':DGbM=FG4A8jhAl()+Z.`(8a^[0YU>*;j;-,6Ap@Q.'-B$h$,!m7/fQc\U"aSG\u]B
%/@'LLa45=/D,%N1QpGSrV[+QDF^<f*<EtMaS]:U^Ioc89;23p.pYXA.fkS"$%[qqNdGhuKi&dr,0O4\M^**gR2:b$p8"73rTMf10
%STr/61bP:?NZmqJO:QC`&h^;f(5pbMDbsk@H*%/)G1H"W;:i)l:^BU>EqfucR`0,L'nnsgZM0^15WatqPBij@P>$0-[`j9=R:mA-
%kEE$+7\>T\IAWjr5nsN')&J(4["B,7m^-seEQ#5O^u*@aHOfVnG0U[[+J_jT6uj#2a#*p7r5";+hG*h&BLm?ZAn;7g_NWI/l@JW,
%F:d2TMW9]K\0R7n?'Y[3P%pfo@>W&\/6Dt+AQ$[,+Ol`-O.[J(UOdM5a9afbaQ8`W7hluW9l98h:r3bT/o-Yf:9FX[M^2h207V3[
%#nralN#Nt0PEl,;QFDboWi!kY1VX`_>I*=5SfTjJWr"VM0Cqaf)cQV4M6A_`WY%eo5V&s$fHFcgh9WAtMnJ(X.^:.td!XWD*PK9S
%HQZAs5.s9U^nu=qFn+b"Hj#]-WI1Mp=sA8:efQ0oR@mUJZ$aciQoMe/F+]7;$p5BkA)Pb+=&c0eN3k*S,?Ybd^iV!s$"1^,2\H>p
%41V*6<DAsQ>gt:W7#So-9mmj:86HbN&^YPfkmArtE/V)?1l<Ua=-+=m@\'-A-LS=67Jg,n.24u0],J6.RKd?sPQ#o@$n>^$B?=74
%U&d(WjSL(_0Rdl;[s\2/E4I-`9a==[=km!o4qtCD[Y+(W%.5O.f9A!YkuujQkY9Co4%3S@!XgDR<3!VC(W/6u7"%,#Ze#ZI0g.#k
%ZDGX1i'0Uj]]>VB^h/f)$Zf2(TKu=!%f8Pu9k8od#?.rUTa413,c0&gLh(AN3'^5,^P#:sl/AIRmE]9K5?nokP0<n"J+k%6p?aM^
%s1;Y:\)2[JQM(:ST76IVhu2A<rq8;Nm8r1nqcD[(q"DV_I/DZ:J,,GGptaj+r4CkaO5KsqaPsZ8jo:,kIeWHkr;P<aJ:G;l\%hFE
%^]$<a4'un58B+/_J,B%KQ]M.jT;DJb5H#\(6,B">s'N-os.\N`qYW#Gm6<]Ps6eM-s6_h\r?"6j3;$Voq9Ni=hhZ%MkUKgSl??Yb
%IYb3bfk9)r5k3kdC4=u=!phYg8#Xr?35kLB'I%ZO$Il3a<>\D0XnL.*0)/sLA+-JY[r)5/FJu'\K>^8<,46Qc6Yur[a)L?OP"[nZ
%*EDRf$rsnRQ?6*DP+>\1Uha3Ij_a`:8dRi"68So(+lBB-l1K2NlLM!o6c@m"!YSp*YZ".8_R<D`JLE$;=:nANk5pn4[!enlVO=$;
%]g(4U1`f?p`WAZoVF%rF"uro9475-4LfE*IJF*n1&V)l.O9'hVgUKqCXu6T=R*$6HNJA4!!bo9V5nQ=fKi]ZQpTmPmi]_r^dH,Z@
%Xs'$^U:N_&65GN#,G>H%cX9LFaGqF`1,%qI8T5S;7=[>8aB<P7.:p"V4>!s%!qiW?+U1UY6bZ!nLJ9ACa\@B,''q8lggF,:orAZ1
%5&&h-/@33Xk]6`4h]`k*)O@9%_KBk]YiFg!_[Q`(igZ%A0blL.;m`IAmAu_A=EL#<cW?T*`;4YmK-?;[gNGWHG"Gc:(6C+">05V\
%N0uo;V1@RO4)edYPsS:"$6D4!0Ugbo#XGrh#^1VeYd7ell+)AOnC>R_3n<E]I=))*3mN;TYrOP<p]Y^3jF[/WOWk4</uK#L9QNeN
%`.;RqES;ctP*](;3fu!h(/D@'KL]AjL*-V>(f8%W<h1aa;q&JFUouI:OMY1WN0`rRYt'L`_^=b>gnAB__Orn5$*0NoUrnjr9=f;"
%%GA'U(f2oPcre;=;S,32CU9(4G0=t^!o;>GQ@j`=@)>Z!+X:%gr_oD@nSEoE_0(!B8S^cJW.%+(8ddqhA.0sZjR36;'kA)]QR1E&
%GW9Chqb8Z1USrqq6Gb"IVJRMSdJ7*V&[VG1gdN*T&HLXg,3/dm9e![hn34&[j?!?<noT*??k*BJ2QOE2Ta[2HkW2OcegJ0JEcNb%
%!VX%R/Q4QG3i"0hU]lcF8.PF\44);m;&oUu2kfOFnBK&<Os\b)r>pBW9>,cV_N[&u90<+;i_6]k:4Xo[TYknni!-p>!1TN%<-U[^
%)0mZeT1.m?#Z@P"7>8(e61BCA7PT+mq'7@nb<Xi6.U!*nVY.aE4t0$n(eARof\&Z[+Ql0&g1%U<fHm;G>r'<Eq:o<Pk0M3u)*aFt
%]';KfoDb^KT5W6c!JdhL1I[YWLQJFE/LLfG6KJTn"e?>SfoF!I=>%A[Ag?h^iomPR6@4d:Ea9oP]_=,SGsWS=rdC.'9sOR#D>"a%
%B=DA+lk_uR"&kCLG!Wc;)?s-:0#b+mI(E!Wjd$X.NMnF&^53lZaAM]L]/iRi5YEi]Mi'j?%2EWG`aV*i3BA?i?%lH9=`-"k<7;(i
%,=?uFceX#+g.c`g7>mFQ]2Pp*\Oq7H(d3P)6-GB+gih<-`e@7.08X_8W0UBASF%nc4cpfA,#A9G!_%^>),2pX*\q6okZZfF?6.04
%4dQt<X9S>lGLRji:6;sA!jlDl>JMkH9SRu;Y5lSS`54cZmN(+a'V>W;Up"GTe65c+j:KQ>_XP+M!!Z0SJkm%7.tXtCR$$EVY6`XT
%`QI_i9!9r#HOqj3Hn!g&7[mXkQSWLs09<V!LU+\QD`!6:HinI:;_@^RJl_EG4j&l&Y+"Vr!A,&-E.E>2BFjTO2T99b,>%D'%T]TL
%_#nZ%Pb]G^Qq2k^*5Rl4W3d5(Z9NL-q5*YNd$tgRi60U%OJjYF![)9]]CcS*ih)cl3G/j_b"Y73*i`,N\[qSZ:p*-t8Ke!A\t`(m
%'1m_(RC4r;\gR=B:a$lYO3:>)-':^]l9HSGBUSST485,qd*WQ@Zp8o@!g4&^e!/RRcR)E%fn*iNLo0PO7dr$m[:n@$^[Rpg/uo>*
%#e-*?%57&JX(DiH"61RqM6o%!D,o4j6Fd72p*BAKk-`o>F]S`Po=6VH8:"blVs-+?d_2-!pDn6'4BX/,?^DuOa$A__TJrIb40\TV
%%n8WJG`flVk5m0OJk;.NLu/>Wc9pt+n8m#-rGm*8eeVp%":WG/&YaSLK[;bXXB63+8lp'l$YM42mRI?fY="656Y7kU'7fT=quF4:
%[1.;+)spdc-5*Fe,BX$\>Y<G_m1i8SHB8ZT;0_edane8:5?hqD[g>D$'c!E/aJre=VRCCg!r+XZE,=(YC6PYMJ$Kt`7t'af)TQU?
%H4L/PEAhsJ_B(Z]dS0%b7%j,=192)3GE8OD'Kj,39CPcI*?APrl]n1)"5iCpYHn]$0`R*d]<OP7DU[49kQbiOW:$t6NigrW1sNlm
%\6'e@9^4iM<80L$.uBpG$?bG2\fGb`;\1)\lOu]o1!JE*h&:YP=mS&-Ib2OrX!g'e4[iFi_`1$B/op_1]X=nO9Ym+;Fr4MB&Z:SC
%)L/N%lsgKr\Q)s4%ZB\'XFOs\-2lK+XmA<4=aWTFV1rT2BCDVp[<)Jk24HQ=fOo<kg2rfA;@@T[Pj;*2%p-(q9S1.C0X8a3K[kdQ
%Zk@UF/U&abCi]_\a(fAY-/6>EIeY<k1VuskC&+*e;I$XYk4JqgiKc^a&0YjJ**<Oj91`D;?m=Im9itD-\--N<)Nnu]!t`!rD@>FP
%;,l1:W3K;YGOp%`+0m83`1i0I`)@-+,&?:?,6LhuF@3u#0"2Z=7\G3@L(6#uL-*(<eEb#6]id=Bj>8B/;*d$XmotW.+@`[,pnfY7
%)iSPiUO&r.kp'Tf0ZN2_",PCD_giMdCP4E,UFRjSI!Pa**T#n)n#;TX'Y5D#_VO7oHl5W:;Znb]M8pdn1Ak!Hf(J4\%74c!pH\%0
%mTruQRN#F#3'(8u5H"uEZR[cQ*UCZJ_9G5*=l\&lPBHO4)AA0n0,YDpA<iVcY-^$X&VMF(s!1P]hLWkJ0,*^H%1hf\b@gfQ<$e%`
%9=IN:W&s5Kc,nOr/32-*&DiSZk`5c''Y0S\(ScrVaG5$*BtG$$Eb+6<W=ftEOr[tpfdaBHTZ\PbMBVk*-#pFqB:#o]%1"4n/n1)l
%%H0`+@+Qbl2O4bY*LaX90%d7!)sF8*Y?]iBfd7Tar*UsRS6K/o'XH>qN]9+".A3*s!<YWR-jO-fLlE&o+%I-ZkaFAq&9brDC&=eI
%HA&htUF]?o22HJ(KSH66biZ^KPj/UlI$(1[g$&u39Q[cS!s(TmYqT2'<d"uPQ7+AOMYD#DOGsI/`NR\pE/]Y.A@96kOcO"I''?LP
%D8INnW*Vml4QUN?gW!gF>1;'f0\b&^.fU@6eFtF?"M>n5'>55FKkO#0UH;.DXrCiaac>o(<D)1idK_"#QjJ0AmA2;B8i0)Y16fu*
%[\K-\Q[7,LTpgY\U:l=)6'04^A!Pff0p?031Q"=;J-9PC"_qC$U+I>LpC4:u95Y!F6hGmRVY`&M\2V;f2p&#E@Z'1APQXDFJd.13
%$r(sVjr^=bk:L`ui?6NRbV@af=DgROnq??bQ_<^J$@.R)*4^pERS'<9Dl"!ioIMcSijMl5@6/-HE['3,q^,m95HX#[\Q&)5:k!#3
%`Xf";</SK^arQ0kjJ&Q69X>c`#rk_+&+_7<*!jfH!FV=5F+j4]$I*U;kEP!Rq(C_p#"$%Y]p/+_j:kRnfHRbKKqnj<V^OuenKqQ1
%=-<lL#$F>]=OTN+?^WS*B#rM?MT(E-+jo(GcrCdl$ggLA)gYh*#gfcA[:Mg@)P&nLFB)+].k2\dQQK.1=G;/>VjJZpY<1N"U8iF3
%<`2Yk\\)ML)chtNL4&F'hrZh47/bLC`bmR-)3VYn!N%P6(pt'K_lt50?&E#A2j@k_9q0\fS6b$^c'_#70ee6u1G->EFEk`XK#>Kd
%RrP0#jEjg)c*F(@N2;L)0"&^(<07@nd?1TYX>ka@h0T5KW0h]F#N24Qh(Bl_j.:F:k?=t_9/#b,L<f9+Rp+ro=?>j=)N1Xe=kCSS
%eo4dT<B,[Qi)R^BEIHOMKL[@T(n_,1hq:il@dW^n.["[%+Sc_.baBa"0EE%hZ`\4ZmFd1$Ni6:e%Y+2A@oF*cfYLhG7rF(L$gD*+
%Amp:ciuh]K:c9^oe_!eQS:[rn8Ns-fq^Up_%.:o*KBnD[Db=Ri8%rEILuYHqN,eA9h,Sh_DW'9gU:ScDQg0YaKu2SKJ&3-a:BNi;
%*u3CMh93Tria`t+KT[eh&]J!.<e/qJe.pVbfFb.Z#!]IC0pm4O!j?4,Snj(JCl:R/;/NZ++Ys0:He5DU?t"C.h_VMMkhDSm]j-(2
%pGdNDlM?1/>=D\GQ3intbf0.19:-t8rf=X+TK2%365.A3'#TO(meplq(4I+E8amS<86n)H:UZXa8M"+29tBKr>k3sd25u`:/1`(#
%Z4cA?K\8ceCuC1+>R=IAiRom7hFD1JeoKk#q!d^j2Pq<n2/Ec?P=9pA+c@bs<[ne)KnL+4aA,`R=o1F#s$aNBA8@k#Ln\X+Q74$=
%WGY<ofZG=O!GiX^gHkQci%IiNNktga;bp3LVSX4Z3g/W7fQG:-fLF+=&i8<X>-K,Q05'gCg<q.P4nPpL/_7Wa.l8M#QgbIPMnX&Z
%+EF2q1GOnjk2siT>)kTe"a_=qBt.^Z3AcnN;L=(s>n/X(,FlVt$SeF"ou43A+Y$=cTpFKVr$:018_g:5BoEEc.F1+e'cs?HDt9\M
%#@iU(lg@WX[!R(PZY7h=<r^leUa_l#OL36>Zn;]?25W3GZE$$)fN%l92ij&PSqsV^W!GSU$k7B^Na&)<2]O^ZQ:"\.[Y+0Q.&&)*
%-Sl!K]*n(,!`/KbA%!e"+!]Kjf8G$FaXNNi<sP5d=>CJsm*efW5Z^XH8EX0q<H+^')]%R="#n_I4(PeY'cs?)Q7(B;9r#!Z?;`!l
%/CGnJ=kjkg.q1T5>#2(9P]s>6%.1F'M8]?o?*WpOjT/nR]5/sgpl6=>lI1G=Af<Np5&HK+)V#ojO\:OaXXC%PdQd#1LUZ7XY->Je
%>Z-aNm_)G6!FZK'Clo@q3Hh"9%S42j"<e_CC>[ZZ%>Z*Yq3&:S3\An/qa%F;CK6S@ltMUF'HWV))b8B\#tos';YZV"b/Wg+5&Z-e
%7f.]UBnX2HlqqX/UmAMh@%qYn4,p(4Q^QR?`'UDf,J6+$NG+MOf9i$)r$E%]Q:p3m.0am]V1D7R@YfU9G;B[afPU"I08@b]dQ2]D
%GL7#?RbLU*YqPs2]$j0cikcuP'lgcZZ82*l2lJ6?;E-.?Y[I3fU#<Y:.$7?ekqX`B[Kc%R>;/;s<(-Gac(npan$!3cL66.m>*@A!
%9S-;N@'R]56EHsKq3C[TQ\.ck-2MrIP]Z;ImWO(TkqH]OgN3(XCdu)j\L0;@?os\&lO.34Bg]3ZM(h@R5Wra0Gh<.]f'YtI?EPj-
%HgTr?e<GNXG%:g<2knbL.E,Zq';)+0#75rp(7/;X<)\FO5hh50mUEKFD@4W2@eEH=H$Pu+8>IEqmJ2+:LVYp2cdQ'e.uAGSI7@[>
%-&e51<M.35M8F+3p>)!=f`=mMKA41,-us%'A/gl5#b%A[.'W&2,_A,j;Rskgh#eg$2/FV2Zb%5`VqTe0\U!W^;@KkUiT+0d1n>S3
%5F9Ue8')bh-\A-r3[R>JP/:R]YC1hM5c!dTqikBGgX^/>9.f.7/Dg#16p*4K(q6t&0/OReN'K@.Neq2,._WC\X<m"(rdT8D=4EZL
%1LT8jE[j=qEmud\7lF1-T,A>NaR-^Zm2aA"7"m.jp)/:R,G0:gIa+Qm=`AT0"js>I60E6+K+o'9!XI[MYr^9k]]BCJc]%/J3cR_3
%Omoes8mqZ8@3\)KRrQ[tUd1(1/T?Y\ToiiW!JITee[YQPft9&)B-,U/"rHJsH3\E0enr9`IFF3S.u_`t9=>=,8)o.k8R;(t^1(1j
%pt0P7FHe-&3N_FAo!94`<<rd:@8^M=Z1&^K8[W`:[4mPKYPmQRL)I=_Dq+U=44Zk`,#hrD#*PT+n#P)aK^\J)DS@%55U0,HG?DSP
%TAR+.)f/1K@gLG*Voohl!V1<s`5+LoU2L*;r56U^YBa3D*=9:;`q$b`XX'Au#D(YG1W^;*Wsg\#FsUr_[$_SkdGAMdZ-;jMWsIuQ
%!lAb&aII\T2u"=/5?TXG82KKd:fB;S%V`AXdGp;b#k0drZi)]>2a\-k>QE'`r747bgeP.\5W^@p"cR.BMZ/jbN,L-Udse%H7/4cq
%^Ciq84m;s[j+86Sm`H;'2S27jFZ;^LK!hNFj9RGp09U3=((hQc2*FT%g1rAppCNsHI2?@V6':^t!c/G\8J,i/bY6ig+iNL652pAR
%rk7MLpo)>FJRE\rj"i(W-fqc9J4&^K]slCoeCEh_NX5)Ba,7K?dNdt7YG>W"HZlHu%,#_(RO5UXfg92s;YN?8$L1R"QqJbGO@8"@
%.Q,%3,LtC$UJ)qq'qhd!.Wc#p8/spt(:"d/_EFMjBQH0C&19$*f/Jd#OdJV#Be&XIp*RROQmB7WqB:q4$[#[H!o/)3Q,<@VG0b7F
%_a(B^6\t0i(IJGMa[1?+XF<a>k\,jl!fkB5.YQ"o3eS?P3\\FR$G+(n;V+H)9NmI>!jN-kD4._N*-A4L*l$!;?NB.HhUU3<e>IOQ
%$hT2<*Plq/ldDoud(!D<^eJ((g6<fmS>$Lgnu'K.5gdF+Xf*Y17UrLG)LW5%@"L,iPiY=n\T$(25/6#P;VEfb0H%l7.Q1b2$fW,X
%"#RX;`H5*=cr(;8.\a$J#OlG?VW64ZLl*l[C.5fS'!Am;fcsAG*I(p^Z_.M`2ouD-7fi1B7NGZA1o=9fOS1@7_EAP\)63"/=<(a<
%S(i!u[.lL%5&#Qt)Zg.`ju]4@6Fp`hG_D378Tq^kRt.G'*(nQ-[2Q,-RF>B@\;<A^`>nt%cUG*'2ZE<S,ULk7.#S'K=["r5O+)&U
%L0AI>&Drl$[URblksF&U[,"8s,KC_[!5*tHE(iWYkKj>_/(i]Lp;ski0:bo_5>+@7?bOsGkhr:48l/($G#-MY)i\(bo*GVFZVb"c
%L)]44A!=K$@M2h%U^ge!FbFXd:jVVB`9*+\=K?\"^a9XCTIsbGGON/(1HN>3k6,hEiX/WeT`Z*?NbX"u>o$r:>W6`A7gq<^.IQ=*
%(2n,p'MUi4$A1P[\OQ9BiI#K"0nWP*C&+uAUNmcG@hPTI$>\T4ODY_8'/2E5,ZJp^XchsWhpGF90-$C-qZTK`i_eNX8AF5@PCf8o
%Qu@7+)->HTWus>`D\d9'";":kX'nPE#='#PPSB+1d+rb?:O,%E6I*S4Ua'L!lo/"K:>$ai'(ZB_lA#]\_Y2'[6Smp4qaGYg];8pY
%/Z0h[aJI^=g6d\-_DenN6ZKp=GR#t2Z__b:pZlqTnSfa+\!Dri1'V_g&<S0tB5+gs-=*3h:7LF"c-Ae+`CD//\s&c!Ug2PNH?a>]
%NWGRY&3`3^$Pi,'O+n;YMXrpG/$%Pb@!"H7n6ZtDku^_+4qN>kr?rJshS^X2cR$V,WB&D)`r!Yr2nfgaY.&7eg%f1e#M_8q/1"Qm
%*5*C2V:@"mfSd99FuNfgC)I)8STq2cd&WSuV,5#h0$22mp)s8uM;1H7*B3g1[>`m-&K(P9h..OPi$Mgu;X2RCP(8!Cc@UQ':fRY\
%XRj`PqRtDJg.UfrgVjXZp;Z`q2Eei49+dG/J'u(f.UpafU.Q5"oBMd5@LB?n<U\T*EqubrMd-*''%s&RZa\r&PhY\)1k?[S9A+f8
%^KbNoZ*V(lRC(WO4hFjnG6^+h<"p#<o03cOU?piO)&ea"G&q!&6G4WLUiGQfPk&)H,TE9M@U`cQ&c1jc)u$%VYh`X$lq2I:\m)c*
%F)2%g#HC:>'21k['h^H,?!l>O<AeUsIj_tH#l_9)G%Sq>:\Lm%C]p732%rEH`ZB[7BPMUCN3aIi@5!WYof'@I!_^XQTd7#a7mm*V
%[qIl(<4XgCRk6s/bsgnb*>991$j9@.Zhq"$_;+i$rJ:p'6]!Sm%18>WMYJNfOjL[/)o$N0TY!5LB_bF;KPsfD_iX;\IqRJ@\eN`g
%%D<qX:6l/hrf">"A)?&<*0*B9i?P_H.Nk%9eE\=fLRfgibm71ek&DXD'Qmf]=-hhrVuZ2KA2=G+R$XLhKSY"siFe5Rb$EcFi;>Xb
%Fk<9$l+]p3W9mZk6TW'^]W!JnTa=)5DQRBQ)FG]2f_o%.Uo8'@8XXt3_J]TCI)fSq20tt'Z:CTN23f?$^Yj7O06NIj4;h*/o\aoc
%YW\h3iQU)dpap["&o:^["1CnWq\^MhSk3<MP2A0O(.QONl_Yte!GHE.!R`O%O'nBc#%\F5Zgn0rVZ%`9eTh'uW%*Tnh=:A[0ZY<*
%3LnW)L2/CY@XNZ2p7]&*o)qTT99qomdn=@!#/g.Jm4Ghnna!>eCd"kZC>n]#!n)MHX?WGSKA%@)PAnC2=df&Ga(k7eOW#NN3e%jE
%;2-\"8$Nsid%>ad?h@->OS1p2*TQ1p(f2FV*9uSaAL%/L2UWP<:H"26YA@bFHVeY^?b%&c&I7cH*.]Db1eL#B(]g_X>A(<E!,#ej
%5[l*VQknSl\40KQ5oHij2Z`ege:&VSfo>5d<l5,EB61%C<"12A4:q4Ap,;8uHiltj&n$.[FD$CLd2:quLO$C1Pg6TS7A.?Qh(-Eq
%4D#7`5VW7tet(9*n<MK/;H8PGH=9?3%9Fp_5388QmTh],J=ZI*8]Is.Bu'qij&\/R#ap:ZStkjTXEE>^gg?ChiaER3b#,<jiGqW"
%a*AE_ZYFr)33)6^!T@u#%,h6[H3[(#pqP,\`"K#_fHF8-0ImDW<5U3leK"l]P#]4,06K#<_YRa7<2,^/_\5?Ogp84qnjMVg?+eTl
%5)r<BSm\@4Mk=Y^D(k(\N6r\"]tuG<.\mHA`BI_eKooE$-uUufd"X;s(k&!WKigqZ?/Lp4)Vh;th93nq:,:PFeqC=,?-59eHL#hQ
%2n?R`.n5[HQDe9c;8jn1IkooF2X7g8B8PVPNQr!cDVL-ZE3?N,e"Ho)U[5egIc3@_&PE6SHJ?s\X1-4sV7L+LrRPH\>)ZS4q\[/_
%bFr7\70'"r`[r^6*b))]L&$I*-neYnn&&l%:g-I3I'1S?;Q6h.!33N72IMKB5r1jXA9OiW#hE;SG+_NB&`O6*'^#JXWPo_ZUGDL?
%En'+D/1qUX]bA>INO=jW@0N$^ZaWgRIK%Hj!ALMh)l7?0BOh9:f#oB@")iWlXlc$t8^8sOkXIblbun3<k.Jm!,C3;L6WpW7d!2s1
%$X^m,#=>-"OKi<_g)Y]$$:rX5A]B_[\DPB6ijWZtH,C7qE'Jm_@!"nraI7Bu,p@6CAr414M+NL^81uE40*Dh^#!_LQ+MFhW>'!?,
%^(0`D1_\=gog="FK)&t0&..PPZKKMG@cV.d1q3M5V-ZYG2pg_V:Z-7pGGWAe<C=GfTbtXd0j-%GSfWHPf\`@[!?_#_%:/BCp)1\<
%J%jKkHD5#O8.(ho*GC!J?ZMNCi\&P0@DW_n07(+tU.1c2>%2pW/>:Jbjn:L$41@O4K?/@Da`IoR!7jt?!upS-mUCh57V?GRNY.TO
%G`Pn+po1Nb,7b<>",,8TEk4_`U'3HQ!'*o!.O'O5*%35#./d_$W.XDH',MN:nBhug7I%>GAjYuql^3mce;C!2itUQ+#OFTKH&Fg9
%$;$?b23B>5J?@NGAU_+))X&h0E)Pnc>t<S5&ps`@5lZ*X_7!&ScpT'KRX5\p1c9E@:fq,%Lp'#Rq4(K/,rm>LNL(kidF^1T0NhGC
%m283L80o&]'860FX8#l$#(]<RkOK&NM'DuY!WrBI<ti\:e>fkel!ut4`71PV*,&PIMP>Kd.!<Y:-Xd<@GY'A(id*BtX?(VDN?Xd]
%A8Wb=%H\r]^!-#(8WpBp%5Ha;Y$B4JcoJGfLJ(PVNRD[q6HI1")M,iA?^\W0/I*t/;uaKM-rhO;U8]1?D>`SMn2@P32Rcg-_7*65
%.!7H';HHgs(1+T\h*YI`""I4MnBcuLn8c4ZmcpfLU<*OMCTc7n)KiTAh,#gP4.*$lDVe4jc`S2B"T/c7E%E^iOf1G^H/6JlW(/IX
%Z.l&F02lAi%J&f#PXbJ:nF&\Mbgu7JFnT3Ca(4nlF"?jbL.u4/B2=K\nb6p:$F]mrj!EL!41EuE##$G(mP!-T\nHtA=.g[U..m<X
%`sL?+5M+\,")/pE^ZR5:^`C7^)q]r(.ns)%2jaWi8"UWL(brKq=Zt>eO^-&<qqGT!<1F&7CUiPDM9>@O`%^?dH>mD,i<?l.e=qm3
%Pdaq=Ka)bL96W%*a5QEa(?q#o/()UMW5=.5]WD*QTY8Hk[.))'<_<T%a-BH>0k6b0dY3OBZj,$27oE?GE*IIuZK'a\SqW>L9WBi5
%U,p!$-`Ih1Z#/.>iZ;X)N=H%4<mr3ARq5pUHH?D[eYAt[<tOC!<1)AFM*t@'r8:hYV&L)NpT-`b-?>=m1[DZ7/&/cl8!/JEiftcN
%`pEcFqLT#Ke(5AsWh:Y2npS$Q7"K)S(kahinP@s1@1Gkd$6@p+UNF+uk_sTaP8#NPDtIdAc3;<6FgPO=?6Ou=,@&DLZeEca:sj':
%Fu#<p*Al4/C+0Da,[KLcc/rjla"gl](TBAs=9iJFAJ?7`19cBs-cJ3h48F!*!P@?s4lQT"""QIfEa[W"*>=E+3h$-6[^6'>=P`]D
%XK2\m;*oH:6:4J?S[sCC%eW,n4uR[0iDW`pBL6*i8JPD-/YVGn-6"Pmh:VjZ8p2VZp;sY=X:iSu"V)Z1fPVH[lh3O!JR+pTh+a=K
%_F_Ot>L`)`doUTek3u06ZW0U6RU5p2bh:.!VHF\@fE5d=f/5ArM+Z%Y?ufs6No2YeKWXrN.(Tuf"2As4\Ib7^b\YC9iHG^HW$HSm
%<KkoCU+.]*!JTXX@3&k@M*$3mUF,A1k3c@78U;lB:X!%"BQ9fO38Y0.eJ)Dc3j]LK`7uu'1iX_JW"X0H>gA2]hpZ`_m^psWdVL_2
%[9<^WI&]cu])C6CALqgE6gj(D33-fg:<,\\[Sdlb]S(1Xb#A7"_C)8[cR`@&J"/%pPeoY?UsVtY3o@HDDC7f9(BC-&+gci8aa-r[
%Z?M6:Mf=?qDB2^),go4Rh,q;S]peGRNPV^7Z4T\S9W['mSOd"MRJ/+3VT)BnLPhC7J?)`A\j:$Ws#D!$W2=tMZ0;:`g(8:*S?$:l
%g`HSAeIrK^(c$$gi8+B`F[qKT:t2Q+0\]K#f@5"!2c?%-.fr:bOcPTnm0Gj4h56J\UhXcRrI/n^[V?pcE,@qEJf*L*Z^YJ<?7?>>
%O3(Rl,IF^WGL57[AUB.#le][/NNt*IO@>Akeg#FW3LYXHEG7,l0tZJg$=ZYO@OSs"P<:Gp8!VbK[ah;mE&%toh)AGF)/6RP$"$=p
%2lE^rglb]@F'Hf1il%?eZ,]DF\!]3h?Mh?6m_t^P\u%hMdZ%e+qYR/g-V!+jK]4oCXJkAt4XPAoOitIN/cJMs"jk(qB,`\(<#=i,
%gVSnK`B!,sf\AcZGp9:7%msB1i]_shM[Z);)!#,T:&rZr6[1`$6P:asH+liV!+?08-f"2L[ince2hWNuksZ[2_AMr^[4m"O-`qq\
%iGuj\/s7-[<1drmT=@`_#l^OH$:=fZd0Ie"Gi)Xfetm<3j&LJ:+R3NtU"\:ZP`+>/44)gdXdhrV`R^cSLn2q;<)^&q\(lAlHr["#
%D=)'@W*HfVKL\h)T),e*[<?J'!".nNGqKS'Dp))5_e.K@l<T<he$BpY>e`2<Y?,!EpK3$BQ-8[*^]qd9%M$&'G-Kr)i\aPISV6n(
%8Nq'bko.>b2I/aDo:(iB7FYR_XX-m^;3FU+[>tM1J+COP:oHjL"EO/9-D.Z6oF.E=/Tdid!4[)>Dl.Sr,2trQ^'L^df0Zc1V@%8>
%g2I:4'QB.KSI8[ho3u,#$0!loDZQOu\EobmaiT//10<[MX7?PLTSKEPr#f(+%>F(["lW9*n/>t6$?7ZS&_R+bEWBjh$b9FqM>Oh7
%:U%%g#m)OJM1n]*^]Rq`M9cA@s7S4G!83F!UK/$3PVO,\:OG0tlr4jOCoZ!KT-To3#B%pB':W"Y'R$K<Ia))M1bMN@_KH/l$psWU
%,@W)a/#2tMYuo0Sb<OLrD-[W0K0CiapS\;C3ChD&B"ujZ3O-71-gWU-%<,3"iCsf"VWa[,"?.]pq/R/*b:s13j;m&",TdZNFSsDP
%6G0\t86+4V@utPr6L&>N=gZY9XGlmt?mZ5Sc:W)'hkmh9'ZO:,ibNjqWWl^PKO!Qr/2rJRl5(ql&Yb=$k>2"*^]cjMhiI$!m<VF!
%Ds1]:gB;^Zc!a%L(Xg(4mrP#=2-$i'j)HC:ejMo,mA3nuTN&f1_,l[oK=<Q:3UMh,L2R[c+fdQ%%([K5N7`!I2QE7j*oNQ0\c\1$
%'^p2K[_(1m!SPViLd&WDcAR4gDmu(CVPCjrJel=&=J86=M);4AeL'9eO[IAYb;3O0U4@`3>BuQ1(B%N\90ua4X61cY!-)#@IT!Z<
%G>,)+Dn,/"Ah#DFLJScMeX#L2.U;;"5c&;lZ>Zjl$4&eKX8/[G^?(B:C37TV*ZRM/LpG[H.5o%@W"Kj7.9[8?,>:,AcHif(C3<S`
%9[/r?H6-)^as?_k`\*WcGL3=`SFr@*U9"'lMAZ2$",iJ9S<off5^U"O8-H-l&pD:pVl^d*'q!J0&*M6OTeCa:#h7,?lb`<.6i^kO
%YpfprI&bEiV#t-],fC7<+:iFkC1Z7WiA]"lmm3PY_#LNr#:]q#KT;qDf8B8B[oc&_BA&)/WlfKAGtb+.+,NnP`rJ`U6LHI@'W-,"
%bA-%@=Y8m-R)Ij26'(r^o;Wp(GP6VfCX#-Knro!uG>Ii&.:QqdZD7-q@X]S$f9)Z6Qi!@lIY8BV)S^J)-E;8IBG">_pl;%fAYRi7
%ZJSBco,V6BMB5X?k_lrEo.X71Ef^UpP0tN#\&Ftngmu'nYG-cR..h#e\Tb1b;2b5o\>BMPb3lNJD2V\k6;cf-O`M(2jC!cibi90E
%1f[0P*gBM^OH4"P0cf+#O`6RRcaQf,0FA$Wfjn@#D%^jl<4o2c'gnX/p4sKDUV?q:2qott:$A&Hp$8>&5ulG.YZ4/KlDOK<6XXG%
%bbFE=NqXi5:*m1"++]ZK*b.irpFun&oAZuC)dumoX$3`PP$NW>,aA!@/sMQf6h5,i8odc/<P^H)]9B+HR[B_[m`Vka'/MXJe-r$Y
%S#jiIm:]K41h2MU[.<BJP"+0e&jhb[R78`PGpg5IBM3J&*QM+nD5X4"&0.?KZJQUD^b-9$(4+E<]fbU9(tlqnHh6sh49B*VV2f/f
%AO^%T#i=pD*ZAqRWG?po;TNApp\^+bAbZ<bV%aPI?4:s:i&flN00QJPgDCcAZQ>Vj0!U1H8L/Qo>)7\UBS4R:nMV^fGTp#ul_5*J
%RT,ST(a*lA#jO,J-`LPV&'?5=,*2pTj:[_$>",M(<ftM<Ta?3.X7rM,`a3SK1AjdKi6PmshmgRdPZ>I!0V+n]ZgMdGIPY0>(PmmA
%N/dlaBGO0$+ujUgi%0r4l8j]9:eB'2BJl`QfKP=C?7D?*V._tYOS0uh)q0*SN*s&OKQD[],b#!#qLkOa"(N';[1W7^E1i;YMN*!L
%Z13h7h%qTOb#i2%jfQYX00u+$An)_FBW.pt#P$-^dkA<2rJOB04+-5Y0;[r+b3Cmc>R_T#:q!7%ZIhZ=DpZEKs,QVO_(A6nREbc!
%%@2Z$K:Q"'X.(J4lu&CN!'fN_OMo1]GKBsup]c=imKgH2`%8LU:bN&a#SGFSU)fPUkoaM&\47sp;0-?KG(!PIn]JhF_QZgHa?m]k
%QkeN!4PFA^r!IT#]?"QrC*csa`FI(,Gde8?Gq9,A_>>F!i$I6oE?Q]rSls`/_<Gb,"G;n44Q:n:Q4(#ffoTCAN"r,tfVi<NVErtB
%V>btsMZcGP]2d01Uo*i#8(TWuNf3,r.khpk>^e1C.Pt%>[m(gPk(Id8c%@V8>jj"uASHop(."a\fh;?j*;W.I&BLV$+-7>Hi$`\W
%Y8*";C/=jdE):T`dbq`=_^4pVZ"7s>].P9rBpTd2Hpd5te9rK*G;;,.RK@_]#e=[6<Y.g+V71c1Ws_t7d&j7U]0'l2J=^8)Y:/[^
%oK+93=n]l%JN#OO!S$UUh[u<8(NrEeI\e0IfT=;<&`)8?(H>??hVO2FMprVs7HYs>p*-ePK6L6u".rr4\V-U=/ke7t9G?$aX"@p[
%@"Rloq8K5qC`n\Z)"Haf+Wi//Rs6!jp;J4'3-6UW+=aRh=A?:jSdL:H:kAQ2709_S-2NK6['UP8Ath]A2K7F=90+EFlP_"(>Vg6%
%)!e(hhPN]4GMDTRi$cN:C`rY1VCC9I>_c000u>7^\),.VQkU<VpQs^>DXHsIaqaU]k)T]prj9!\99`g82qf"+i!up,]RmS]"H?S-
%[Y5H#AA10pnC]0p3"TC#?fEn&r+6c*)OlEA&/7=0h$b$1dqI$>99);FOmr?#'[@@Y;@q`VY)43>?7U$4EW\2T_6Q*?J`^'dpTTXo
%APe/PrfAPio2E`H:cj`dN%0Pff[9_$cWMF%W+'OFp'G&Me/@d\\!B&>hcYK/d_TRL*V$EMkI&?)M88]+0nLfl.;\nLZgNDmb0,[j
%R0\cqC4(qU6tjeg^gb$-eCc'bj$%j5>i,F#iD"s<Tb)_5.XmT=>HP;PL$'Rbls83hjjEo5*H-#h_a.R^FMHR_RH'J]:9H_lKd+#G
%,WdeU!k(6(#?^9mbs<fA]Z%Vc6`@BoBR6,i70.2VN'm_q;FK:XL!&KICq"1]9,@Y\i.KmQBj0p+;"1uB(sij17KKX-UpENUDGbRe
%WrXQ#OBcLNV6R,%j^)^(jkKls\^MgBj&5U[G?JY>Z[MSb\],*q2hf9a#",Ic*/]gf%juofLjU`hP+.uYUNiQRQc+=E/f;i.[q;,C
%BSdTE&"B%,Y^sEa?;?RXE0teL-JuN1k%SEDP&R5l?5h&^*Je-dgmuR'Kh)sr$>YJ7Eu;VK!G=LrDFe8JU5og%(<u[/!\qk)1Y.Gc
%;"lc.9Sl^9U^kT$ZX0?(!MX`>X^_]@HLDGh@*/M$;S6cYJ(2_))\p>Ke15:\f:EFW6ug>^b78m!5'He6c:a,uVP4b6qJO4na2cIF
%31l@,R^Z2XC;lOqbUA7??<QG529ig(],;l,ZFen9A]#rBmA2Xl]5Sf7gDg(gC(.X(PW-'IK7$EO,g%6gdq^K`'gA-&E2HHS+dGlp
%=CG$'Vs0>Akb6,oRHaU"5'!XpMs])RgHq_L8MA5E*eo("LXY+Ri_g%Hd<kn$E!ot:g@:!u'Unr\*W$:?%N<5OU-CWZa=2KBGo+L:
%WZAFuFO1&MI;^NuX\GsO>L$I$$P0!0)Tnra)dHu(iC^q(f@0qk_Q-+&59]#VaOTGVbQI5.,[h&F!qcj)^["n[-tT*#89)n@Q9f22
%2VLt>,jW\\X+rB_o?M>S1Uh:0qu2t?+K(ceG_:81BSL/MGZbReS'<&\J0"[kS(T4g8O9bkDYqH552$pje_qoE>F0uAX5Fr_fXefu
%eNkq,/SiB&Wa&cF),s1Ec8u)A-R#!]MaPc"K9E_@F$ZjKSFcI)Rp5,)Nd%.'='\a"MN@)EAlUMnPK2FA*A#/Up8*L?hBIkX;L7jW
%eVf]5<Et@6SQ,i'LJ"HdWgs1L\38AN7Z5h-Ys0t*d#9<#Q,7]miM^2B_I6^HhAhiC6YCmmjkAX+Am/1u9tNW=Nd=R>i1r%=l2)=V
%<GkmF"eQZdQ[o'8R%FDD+l[].AG()3&JMgpGnC<Z99`bQ(O;1*GP*\$W'D*:-k(<$$dRH!@M3I"-b,%gR>l9&6mhbf&PBZ`ZkiTP
%;%M0C,&U?15X%^5#VMI`Mkq?*]M)o=r+(6r.*.b4D5'A`Op'9Z23P:L2H]_WU"fHl[Xiqn-PDM90qn:mMSTg!KUp=2BJr^%RKiA0
%'$nf<5G#`MP@77IYgZ7+-Nsf5EfO6Iq$n[oI9:0WS0]L+&(!R%4=)q-!d1aR^&`Q+\+A*E_0]-^Ig2ji$4n:VeWgMg?NonSAJQQu
%WbbF=3GLr&-l+fTG4K\<0'-(Y7k4j,-0udKr\A\dUl^8^n*^NkY_Ba7q\&ma>jpi2Jo>',-QI5S@KJhL>'4WIY$skuWPj@@MOY6l
%P&FD`FIfMTPFLAEDr,!A*],(r^^eb_Mre+5cH^C4/1ou_jOsKR()ru&GW*G94$^D>ca,SN%[!t[_N4ES:]8cdhdEK6M;IklaORR#
%EYhTW;3A@AP.J/+`YRGCp3t+ofR,(O5%[Eom]&_L>Z-![KjnGY8RC[<Jgs-M`Q0R`LO!C&)-YnWN_h!2a7<jCd8.u6>I/Hrb,)=4
%L8K`d5n:$RSOIf3H&jXgO2r<A=3L!jUsop/WgM`:8g-m;YJ-tXPP%1ISZ?MAc&Z4)4+pmU)gL75_SIMQ,riGprkRUMr5EZAQtFJq
%^[#Vlb":9;eDs4P*hFmJF-UtT&p\3M?WP7ErRrMWh5hdcgUX('^Y$DV@\Tj:]Ydd&MeU?+42]Y<jE)h08ZB["iQJp>It-H*P3oHO
%N6n.5a3>#kB^n,0*qT)p2jHHCQZj+G%jq`.,Z2M+@fRZN+a9NG_I?[4m[H";.Ks8KQ\%F4Tt3K&Y_Dl8Z^F=b>^M1.WYQYp&XGM/
%Bm(WF.iRk!^=+V[H+S`?B7\,dGgl'YoN2VJQg$%C#@2eaMl7LH2&(;VdC0ZoPqFp"jF]1-:5^"3nM':m387N#>>35?f,KH,a1Su2
%PBrVDa/74CIm./kTDDT"b:D!q+[LnQeVB65WL,,5,?/'*`Vm/V6sYn@E2s0';!RH@Kfg4B\<&LRU,:%E.`4aSd(DMd9CW:k?BdA[
%V>7"UR-(5!?)k$nQU1c3L@mK@IjbdrVr8]k'Lr,+iJKI@(%gVba:jtKLfgVg.4hkkTB+fXOa``sCZO<_\.Bq\j=.q"Qm+<KbV%4$
%b7HmD#$PM$^WWBQ7p?_,QAL/+13#*HW/Cnt!#8+rr4;U/-lb+bRcjV%)fR&"`4%WR-:R-K7Ko0l>7VlH(:H9fq,TB<*X(DJNo2L&
%WLM%Zho:g)[?L^?8cDZd0[&f_/l>"JJ;0rT/qCBp>7/0kihGX1.Vq">4J'5S-Jddr,X9BemEQ3Ah%,]IoXg3c:7?4j0%-F+A3uuX
%SnN,gqfB57JF[^AA))k:("Vp]6P_+2&u+YJ@!5_g^SVs%?"j^?mrA#>,t`YNc?/)`7uqH2mte+]F[`FAGB+p^m<BC[MS=c7*"*J:
%A!T[[52.?=U;)1fo-an=WDF>;T5V'.r5!):0E?k<Zh-V@'74FYRTp+qc,p2un=pO7-4Z.a?5KdP&?0bZJT"oPRl+FRPS'<bK,)#g
%#-TR26#$FGFceCT>k1LJY[k(u!01(W[SJ\/aOpccaPf9]^&m4r(RrR+o'9V6P&S[4*h&>:Y(.LSmI*@oN]+tjg=9*2l.2pbNaIjo
%@t]%$lm*Yo_&_qigfHJ=:t?54Qb3JD-Y!dnk]GuCM.[Y61qU]-(]QW/qm/OD-E"!d9C+Hh+'\W.O4BVf*#KgllY+J+4XB<Xcpo2D
%1OG@Ps8VH!aj)5)EZP!+[ChuFqE."nWE'(H+_Yq`(VH^_8>qCTONmN$SA?bjW_-"dd:@ja's,.A5Jk;C)igJaZNKOUUPX@X%-/<?
%,Jga)MO.iPHhmlM7^I@Dp@;pY"hd,9oX>V3pV(FjJ0O)1&:6j,$b4?^!l%#ljU\YRhi9U5AUmO5A@oD[qYVUsUei4_2T08<UO$$N
%A>rNt1Kk=@4)tI&kKogfU2VJC1'Mnm%RS3W!3S(P(mQ.o0tT&KB1,!HK4Pq2AS@ld"!]6UClB;+/gV3?cn'WI0F//6K_Vh*c9V4Q
%!?+)fgNlbl;3''n6=n$4V@Dh0M+AUUF!IrDm:-,..e(UrO^GAaBRA<i6_fcTAUWV*>q)LNn<Y6eds8]:qck<%q2tuFT>5b`GoHd+
%JR.&@A9g8Q"K2e_o!s`q>7^hdO)kkU2g(:8E_cX:.b6G6<XPXT6.gsB;f^=iZV.@a>rGQU]!4OYH+JeUDa:s>q+*Hn5-LsRe8\.9
%8Z"SWGq9TcQD=O%/a*nPeZEO%?RCc\)5^9;E"Y9#@gm<@8b`_N>neCG*gN8pRMd?r@8,?CfUKAlW3Ea@)G)n0'Sj,rE2.WE++0PG
%d`>2B)]GR;WgQ_Jq)+;JfL<^&ecMh"[4RLm)KTZ&-AFbW"-B_`lhg/"Hrg6rs8)AVJ"FbP)>iS]s2Fj4cCngFqdC*Kp`N0ak`u6m
%8IW!OPm?!!c"\/U<NMqC.[u"b8RNCIK$,l:.)M"Y/S%Th*VVJ@(mlC7a#Sb)1LM$q!(JOK^q-=D:7G'30I@^#p9X(?h+#4.2*sDl
%4Db$o?@`!](ub"RJdk<:].dR_@?Mq3)CF"lG#3^F<jaio1'XOQ&j6Se:ut^9LQ`/N@t)L?dlp6KTe_5LaW94l[X<ajhi)^&YPF0%
%h-j[\$q%rn2!RF'm:uFQD\U^G9_a+ea^@0#bfV_XTG%ao0&bFIhBpuGFZ_Jo@'p8q3=^nk])(fQF<is$l\RnDl[`I':V7/(;D(+6
%bj<0`EPnaD\d4'gTQWa@q)(VQ<1A%^c=H<e6ATjp8Oemb6Nlu<q/=BCTtpZ+8G?W>g"8HXG+I(FhiY)-+]lhoUU&%8EQkXQp18R_
%C559DbkW^+B_'pHo:<'EO=tG/>QHq&C%h.6TC2BbNX!nGX.p396R=T]h]TdQ!")>]K(U;h?phk:o8hX,9?.ZlS;`,_d[9)caDn7:
%Q9b5GPRArR8bGo;3@KoglholB+="UsqN(IOK[H#Vpg`T+LFq7KfV.pp#*?3Dls,u7&kkom8Wo-\NGU3k7^u$"!?TeV="nJ'd]uL3
%ZM[%pIt?%g`uN0Q5gSjI_6.4d3>cOp/;k6Dpn@]W)0>sdX+>aTOU(h=(sC!=gNqMX3?Il=g(E'!O.#$uU\^_\bu4Dp2ogb$$qVY?
%&q<Y<'bk%7:'\e9cMf/dTkoFl,]%ZPbm$1hQQ+OZk)iND1lQRWnJnoAe4>:U<:Mu#)dJh#6"Lbh(lUbd#WApArC_=!k6R?tGd9go
%NHTL;af\nOl7Ve92"LUbUJ^8B7`5d?oACn[Ul"%um!O[FCh+5/@l4&nQ-&XFaR@&]mn1K*G.;-U'-X%sWm=@uMFO]2*MiXKKV!;0
%phCKbiNOXfq<]R/c55kO27gcR'7`:,T>N:LEC/I2f#8`;6]n[u!q"Da=p2?l,nh`3o8'sLo(&\6ltt=Wl3n"SmtoKOhe2fYP.aWe
%#HM$5QjV2T'8=h]*8o&2k8cfC0/R/prKMI`]T'!scmEoC(frnhkV,I<o'gGmHY"T_^l;tTeA.3O`MeABd0Qh)7jpuCcu/Mk(^2>&
%jAbdX7?(/l?fpa*N"KYF]*]ltR2YVEkVbI?$+=@h8P(:a,>['\#XA+nSZR;7P#J=c4=uu)q9ABk#ca]+'h#TH(p@*`Z"j,lY?o]u
%LD[(`cS%\9:eS"Br0f`t?nC+lKfYHg?I?%$jj6/.0!JG#>>ssd7*9`l5F,psq8cU#:+YmY8>`"n4RpbL^@)%oda>53MdICSa1pO%
%SmXDNq@@DUml]NWNX9JYUP"X[0L)K6?;mB$XX:U@fRu`#XMH;'=;Z<pWAmT^T45Dm]3l>L0T93E@k`dGQ<gn@IuI2AQb):@^KmQg
%QYd+<eeV1dZJAoLJQ]ZokbF)3HY<hn]$2<u@7=CVRIF?8jJRFHa@t^oP]h3RdOkt:(s?dqn[&^.Mr$oACOi3pp,(!f2FuKsN7kmn
%[2IRGN:7d-!8jkh8EAFlV@s7X;sA$6cg<VSVa:3MSTnf,X^mBFhk;r!:N'=(.M<-rSngu0#Pt^;kF[h//t<m^@#X,;Vt<Q0@38j1
%%/_G"^sXB6ORHTp"kHhGYu9?,o:G/o%@m[*>b1Sm00duaS_$(AM[.j_l1&CecL!?mL@"Gsm-)nHk+c9&K0/`F]BX`--U_\>"$%@B
%OJQH=,ect,5ab@*$]Iii6:*s`J7L!8`u@DqE<PK@Xm4FCHSl!ar=e^ooJ!jT)'`\$,jaltoj:Y46!iV/(6JdiQ[4C_Y*Q9[#ePgS
%#8glal?!O16FPb-J$)7#9T@Dr\5Un2$5#A/(dCJib$_;6\PaOm9$A6R@&D;4bG?L(Kg`dJi'u5#4h3P`H&S)@n@"m8KHl:3Is>mZ
%`p@aFQ!Nm')Gf#86iNQdn=;Yilp5'COpZI7:&Z,UV*AC<U64YK&Rq9$rN/O&MU6o.[T,P<n0P>*re?_:L^JMp)%]E.BXp.c&U8$Y
%o[h#7J<%\i5COB#M&n5,6Cu@Mdr)p6iMR#2m3fMRME*B;.$ECH&mqDRS4'<8n<%Eln])mt+Cj/?/86Z7#8oWfYWngqLl89;?Xso>
%@-R.(XKiN!el@@M/Cr8I>:'8/jgE).@KS!77aG9G;Pp<@?,?'Q\ad)=I[</$Je;rJo_$>&E,M`g[.7h,Pk5,nD`q<n()[Bd<8Q[T
%C[A[j2*Eqdk_Iq9]Y)[fKJaQNZT^:+2cr53Iu!IaQT1bO!Y2iMnmGG?'TD1MV=8(qdKH\to0`CrlC*KVG\*.r=7HEkbV5(AB;HRB
%2+O;b!U[k=+sj`ci(T[_R^8$p@*FX&4E/D,pM>oQ[ijuj1F_.Yc=RDJVLq'3HCKG0arSHQVa-Nq9\15g>?.5HSp)*FNDPD&p@3fJ
%ljaV3ieo[d4J0B9>gN<8>4GbC^1Q_81:?nHLI1F4f"lREZGF\$H'>#Q`6W-Min,iG/We@pLENu=b_94$dMj@KC!su086(j#<REnT
%+/>4dS2iSI-"]S@oXkYLeaPE">UeELo>@Fn0du7<\0(:H`P>."B@si5Ii:fo)n2`(]nJ2,2XOe?kk0-h_r,NkZ%S+pC)PPcam$qP
%"2:uH!>$B@Hi(iqVdll)oc[^o#.Y#,l3f"'m0)IsIZd<QROXi+JX+4r`YjT&?[lMG0RdS<h_KZT@!n'f8_&f0PMkEp3UErD.=(O`
%A&c4s7.i-+d@r64-/3m-!H]-p/j1uBeF`uLE3%5%KP<-sSHG*Q:P'L]PYXlp-fPqU]9X+HI7'N5r+5Rt!YfLrbbh,2N$Uram`L8d
%%9(;pee-R]g'JII]cEZK0[=q"+EIWe>9&0\Kc+h<ff]X!3%jt72o%a]Bi.$10j;XLk@>s)TFO.Q?fQPl-$CR>]&UgT1S_3IAK!?O
%H%T__pDdV>k3_FY7\C)/(P?kWq0g]$kC,Y"eR"a:D)-tfQc?$89Iq,hbqdk2-*h-uS%)[45o)hRk/^t\[7\4.ICEEGSLQem8@!fH
%;.:r865=`CXH4U&Wl?C68uC\6I.J^IpF'!:[p5D^4?VRM+nmH2o@@+IblN__gE]9U@@9[e9n8s,,[sE;.5*\1O^u:'%(j+G@5kq0
%N(n>%7*'AtKr7M]e)X/r]nQZ-;`P.RPGq/122sIo-Ukkd`X#TX-Lu$L[k9;N-FNAN),m8&p4-s"!Ll+7%5=9jT1OWKdp&o+[7^#q
%;sm`u]U(]*Bp\I<8?=7722Kl0S-1[\p>VpqL6@&@9\3MjdXL,0h"i2Xm-aXN$4>as`jQ]a5Wc"<9EiF+`sc"DP\Xqpq&u4fWFas7
%IOst0L/ie/`p78F@H?adcI9LlE2@Qn#U6GJ`fBiNpGg4tO(?AE>Wlj&F9BP/j1:G:p5<V_LMf"0hQ1#b/1RMW`!oiS5106ro1HH3
%d2;B1Y@PEB&WK+tJg=0MAk:&I[CCUW5\fDg2?X0],5.ER==Q"7-ahDuI)-$BRTS4)jL]D;;L@X9HW@4._1A-sYAQcDAHg\eKh9AD
%`a^&qUH8YGS.;W+F^'J4AuGsb'!=,<7''r#B`h38D:U+[FodWY&gi/r51>2K5C98).bC@<JD%,8Rt[!8GtHnjBJ`IIQN&n_&(K/6
%itbM?<LQ/ToVQ5AZc[TI)P)hV!('G,9;,Q0!R2ht[:Y>2Uo0)US&X.K2<,au)dE8a+\Yl$'6#C[KGH!*h>2]5&YOPY;"pc5pF)o1
%37:%.jQa+<ORDoUR?<Z#R\0dsmmB,mC.R[URt8kE\cC(bd?<"o-`@II\td0>NrZtJ1U%2Ud&2'pqb4HkDjWaP5IciRVLHbs%O-#n
%9YX,k1"fqK_n)+t`r4$npM:Qj3PgM92s[l65Xmo(&0>SeS#Loo>U!aGR2\Q0VrjS[X[iD#'q_/L-p-rY29\n,^4E:>s1p2=gI=g"
%Za$O!h/+`S_6>!IX)6uj7$TP3<7*I"S52270bT]R8P2hjoG89C/WE&0MVABX$"fIbqI5=c\)8."''RR8&_,KU?SMeu`2dj)&jXoM
%\0,>cP;H^QQ8h(>>JE]@F\%Xd(O[i6n.q$D8sE"I':!B)7YTj2h%-l7\!a.#s./-U?G8c"K(3P,q[%C;!sg#@&U_H@Qo>U>qZGrP
%3YNgD_6'/8RhEn]L3oP"!Sl&qQf&K[J-,q?2'T8'oBHnS2FEqS,d@SXqD3qa,l36'WD]jN;3`YeUrN'-2aqQofR7Z@:tLZl@-=r4
%`d>%H&N&l3Ng_D-g3(m;5RbH\"=/VQLNk)ZEIn*&0#@:X?a.o$q/;r3nK3"=^(lIki(YFn5aF,r@)IU.bg`\(X\3O.^hDStL/GMj
%`cX%q)?DnT_0GoIKV*'md7FMA:#WW+@YWh$"\N<LJ$\=`ee)5[..0WFkgZD<6'D%UF:M46&l"ltIgg/UC)_VE$9'E77<c)gbi=<W
%&mrIU@o*;gDA1L#.7PbW5Op/C**(U9h:*V-KP@68NLRg0Y>?dbF!Vi\"$.-*o[hero,9<6-Or%L*(t0P&l%Gqk4M2ce'*#<c@+Vc
%-H5s#8`to8X]#F@H>3Sn`*H$/R\t/*D-NsomZU7E.;;+J.&nlgkV2G7GNm,ijW`*/f^,(`:r<6iJu<aHE4FLqblQapZOY7CC-BBL
%Cu,73C/<K5U2(TA11C5AXQBL23E=J!_au1J3W1T%rP0L(luU18*75f8qc;<!SM<R,oP!m,1\e_2d"YKXY[ejc2Hd/M^(PfZaXl2j
%!"E-WM-2bBK)\"/>3Sk6f(nTL2//B'f$n46UD[b7LLtX-iH<[+6?tg0N8o!EO*+8t#+e3A>qf1*-p]HGZ/FF2@Jn8%8<._J@o%SD
%WRatjE/'CJZ7bKr`!:;TUb7!8oZZu12-jq#kbJt?mnBV3C%!N1(&49jniOfA@jRW,TiZp[h?u7\+eG$IAo)ba(XdI!)9#j<;c.V!
%]f%I;c>,Q[8gG$+Ub1l\"3em-@;Bko$N#*74O=>MFZUiB^0%+Ce/lRXTdCk'QL&HQ\=EAd#Ent=d.Y.nr3?!!j!CJNPm_l^%)?h?
%rS'P+j@qcS#&K77j^m()#,hAsWk&[s/F-`abRIRk)Su5Oh@<XI\e[uAq1ZcJ_79W^ZK8JjK4dt"2;F<=RMg>Vq+sB4<8q@jDf.*_
%;J;[h_B9)[#(MSHBEofa[3O)mn;;]]>keFhMohGs<0ER64%;0E34]E9fM;/u7L2&A%K2U[+.Lc@MO^\erq:[:F1?2Lcf:R<&D_fE
%0&61]8\\l$6BY9(,bUj'5g!n@+Gs`X/MIt1<gVGn8I77S>@<sF1#ph'fs#H.m?J_:?"R%`%db,'eHBa'X+V\81.G:h5j_'].&ct:
%"Q5mqb(L,V`Ph#YF`\<k.I-;MDl"=c^5D[s`of%bLk]L;0EP)bm)++&].]!pRKYmDP#6IY8g\V2?nDO)Jg"T4OSOo_!e.dTZ*"JF
%Vgfd5#DNF^Ail4t7,(LNl4uPUXCg.>njoA7G8;X%OgBq>PTZYa6=$nICUPC$I#unh[j0FP;O7E7XO-F4f-MJc_0@XsRN.D:*/:b\
%gIOmMC-Wd(=@&dpLtb\tBM6`&]S5`$*'`fm#6ap(@l^qelgP",K3cLT3?MN`0rc1jrXeFeon7bDMD#o1m:g[G(k5YCe`ka8PQM0q
%Js5p#(dP[94H`&eUT86*a8ZSS^nkhkah^i&Na<QMN`r7Z@f+mgm1(&>rne=?5gMqN-3kK@H@iEX)^@n7m-)mm)QthH)';HTmShuq
%@B0'4js3Rnq>Ng4S?je)L67lJI[ApO,IXr<,G+j_`/m#$&e9^t:tKWOL=CX]NY9pgB4rFjm5,Z=c_qiB#!@R&m;6#q-Bp#`^j%&O
%Pa-fE".R&ZZe9d]VuejN%$m"pIDGlL'G`m9#hDg8*r9q:Wj.U8ia-eaO.MT0dU=p65-Ad18idgGO+,=Pc(j:Q-cP]FGu?b/k30<`
%5c;@TR)u(AP^6%W`UU+fD5R&T`DY35ipH[2\BA5m$]^-BgMVatS,gItF<"Im;ppXhM+K<G\N<[>^Ui"s$1Dh=H,:MI$bp&-O\hWJ
%q^(e7Os=(`h@%=c"13<PF#U3o2\cL%ZGfaT!^`'*3HAA<_m4=WEu&.r"/>-RKLsbHA$[ldY[+Aoq49)j1A&R2:_6=t&(P7:!tr_M
%IDKh2E`ba@rlqK%IC\7F0#+ro>NERsmR/msf/.]1k[`;q]RWOmR+_F/)&Y4\<AnHd>D1)m$IAHSXTR9\r\R(0?&8p3dZC&),N*=o
%+4kU9:'%N7dWp[<*rOG^r#(j1,#jZ>A[^`a/W))t=R)3[6,A0*X<%&-N%B[EAT%-F?6m0m&2^O6Ph\<8gu,u3=;@FeOSfk2*L2?S
%$<:V^coam^($d<TqI-N`bY>9ig2Tlt24*h^QuV#I%>'MfALDDfeM5u8RHYYm:!G6jpr@G3lFV&/:J?<MclI!a+<[I]+pnLKGc[]6
%Rd5%XOPX-d]-Z,-qcNTm1SB1pFiKV`=c^!76E\R`:iH6YhRV<Zqs0*8)RMTpI+oq3Zr(ud-iSNj+MkdWHGbA0akb=bGR)YNk255*
%nHpWIkOdA@NQbO^a!HADN5Y2L(#i#o4[$8lX!7h7g,HLp/iL9AK>tY'>e@BT<5OU]("9*$Ju3^G9en*o%-?X+`j*J.<^"(@VtSa$
%-Pj&LMThD&6\u7_T5hE>?ZVftQX".^63RgnC'n()F))ts)m6_p]g/GYHCifla0mgk3-N6Qf1F*I[L*`kC<WZO#QSc!B]P<6Y2Z;B
%h-P^$np/P&)uIXpI[_;@XLHj7%61KIa2g\@O7)B$NA;Wk]+oMb%S`)umF,KGphZJW"k\.EajH6WAdTWR.a1cA;B7ppj_iYO.)XH[
%[IPpb,Pm7N,PD>@KoD)KduTRR\<ETf9QDX#YNR(8Sr3obM9!fIlbYO;6<<IT#c*pnr,Ham0&<kol3PAMDd4a+s-Ht\k*B.K#Lq))
%*Cq^Gc5iJ0KK2ac,e5LePBFcfU=BZaiIBJ"8&T3VT84q42Xrp:ri,)@X-dRX2"Pb-TqH`XiS6aIDJf,e6[%Fbih9XP:7t]d)[)+P
%\Lbm!-sK=(\lMR<ju5f7?IrZt.A4`68W'p$<5rFqAYG4qR.!*nL,qSaLbaoYebfu,`6q%lbNb*CG#7Pq,p[!6a`eUIFENLY2X8;a
%;pU=\V'Q@$^(\,!(t;+RKLY3[+Yfjq2+JM8YJuN.N^2SqM+ZQJ?Bq`T2(Kc"&hfisN",SmM,4]`UAX&GQPb8oV,:Rq2N-hs,moRD
%q!MXQr:%r]KEW9dX!4"-Y]<X>""?S%lf2R@`@-lcb1MYTbbIWP?17570.&o[:36"Q>9*HKn#SFq\^\cU#^K(N]H)T([%fqaVqeq:
%Gs2Jc=XVLUTpE%71Zb#k^blh"GQ0Wccs\'o-p.G,pA:"B.Ln/<K5%BicOn0r@Enl^X6$UZL&Ti5ih,W3,16pHe>(!SnhbGgSJ4#e
%Y.F^'"@tJ&QSfVA#ci.f(t("R>X_^[5!R+jju>Nc7baO9/1A5lOb2j;_&NF0:=7PD`3mg"GSajEoH*[F;)hf*qmkDITXR;!6QIH1
%/LWrCCdfLSX>:\+A^B@m_?^N0&e+"R:k]OLFDnD'`C,M%]B^8h$.dkc];%[t'r^?`?'5/b!n3-@C?rA\-dFllYj]bV;@.WAh*_j6
%IGE?df=KC\1m[qQfodNrJP/ol%j5h-@=W?t=CJH-M.*_^M2HMpp0%"-b5_'0f4+t#,6*'_8-MQr=c?hRU^@6`dn_k!*QUf#e$K%Z
%2q_K<n/EoX7/aZ1?fDF9a2cf#6h7PdNA0`YXKI4V_eNn-^FmlN"Qe(aZ6^'*JW&1!)XoE*T`A);/[\u*[cL<#UDX-4o@'3MT-f]A
%6nO^l0Im4_KBI&8dhY+:VF'FQ<:eR(PIk$gWu\[+DcN(0kZTm/T,g45rd,dPAdGPE9b'e&iYgKVE,QPO`7kdo:]_rJT3/[*JR5FH
%LCC:G'hT`T;+9cGY!R7=Q,;2;4]IfoN%U_iKUJ4c<_uoYW3@*VFHB!f,E>^9"YsH9'$&R$)<p&lc-pE'_[`8dQR/Q$6uZ0gO$bT*
%BZFNCA#9m08h9"lY5(S$56_a-&S$]eblHo7'j2W;]qrM(fSE3NN@^XG`pL*2]]&V!O78&pqk&Ar"'DG3+e/Ikb7Q/hP,a(59WW(.
%Tc38U0H(%L;Qld\kGn9u[EE-9*&2;;-fS"PoH[1W!FLY(D2S5:#U-9D6$R/E*.pQk,uGtTH"u\$_EV#e74)m=W:k,4\['OO=kC+_
%o!R3:7%H0cdRd7]53$9Q.@\0BQ2jlNQNK]ZVg.CM%$"h\;i/$+]TqKMg55D\h<f&@<f)dY@De'GHG6r^f4Z0"#gF>5ZjW*!@u)Oq
%/?[[Y<#u;,nH?"T;`=abH'eM6NDk[1?su0+P^j][/D<d+=5s\gR\/Ng9Kg3]M^OSr4,kV$kO>i_RC'$#PM`F\*7PYS_Nk2`1(n0p
%b6]+JM]YluFrYo8?1F<$dbbSWnqt3"])Ifg`p6ZfR6l4*[gS-BRI7=pH@4XW:SNJBp%-HU%PKA%0h?mA//t@gDUhE(4tsg*<XaM-
%`$5ER-0A\%Yj=%$-Q8$8k'\)%3!.3D7M$2@5FNdFIalO@7R%Y)_"fjYY/13I#l+cT&e5;M$%h!]Z<;<0c`9DV)sZ\oQi^raLH>!>
%]`.HTUZVp3`sTX7?Xnc7?5]>/71NZ.)p&Qig!mS/gJdI2I0'ad_b(@L\[."p$?/,'?3`BJD!=8E+2uE7.5CGUPCB`$MW>GLDZ3dd
%OC!Ul563o!6rXPt^ZG,#%T?/dbb8C6)\V$g)B[&mF,]a<Y!@R"7j.#@Cqalc7$%EEE2<C3goVhh!S:O;+THWTYD")V!h+Da1iQZN
%dONmX[_;TF)=-\$;l@LW$m#En768L[3:8IaT!D/dk>hm6#nh?k,`K5cqg6t,O2%M;]'E&!j9RZ7G'0'g(B,Rr;J1/*D"/"WW;\,e
%P@PR?FupYYj1*.&:7'5T'-X77$7-Jp*3W.i6&[?@1R)k>%eIN$mbJb`n`A9N!P-N%(Pb-blu4]`7td#L,qt'I9jLA^k\8FSOs?nN
%!:uJA#]7P?*RVo*QSHoF,.3po@><L^3EtWZkNiP>^_*#G!C9q:pA<q[5JeSYDL<T`ddT=S0kC0)q[OAF;0XiO[D_159!5QPc0_jU
%=lhkmbWErlC7]*YW@Gl;ia+1Qg]VXN#Z"1IIZ&e?J5H3QBJX%7\tks5NK$K#8HFWN$E,d(-$Te1fB")m;ati[`j!M-DeL0$1RYtN
%a0p(\Zqq>Zd]tUrnsg]=9bHT/;5^4gZ'+@`X0%_$;H;-I7ieQAeSSjq,p4D^m17n`'TkPq%&TV*H24)Lc43?QG./Q'9#?.7W&]t/
%k:S8j4B[6&r>I&d>JItPdVH5D\>p01ocJYCD#T49omas;\GL8Ir;AQNf[$$oYqQfXM)=,91d>c<jVVAb07!pG)mCC=SOb5$.H*!f
%o6`a)!/]SekKdGHPj!J<7s_G$Yr:!<'8soH#f^ZUVF._HWMH,D&6C^'qcIIUGcj`EqXB(<E0'W7:]-)F<=FCU?2-gKq$=d<hIuIJ
%T#t"UYm;`f6I"^K_@KL5$:aZ_CP4Jqc42ormiEGGaft<EO7AB0_LIGs.LcA1jq9nED#Ijc=/rk&)>.Ju82<_Ahq^6[j.A'lpk/Fr
%;+#eK]gf<pR04/`4iT2r:E;bB*$[S;%9gPM59EmqD(okLa7JW$m/@i!E`BIdi8j8Rjfr/MgbPU)O32P)%9IW^R:,e5)Vs9_?-0tf
%FEE(F/*^18)iR&!F%91o3fG:Sk,[Sd4MhdfRbNTt*Z/mBW@_hq4,ZQCDW,8j)qlN!@C;.U'_hM6chA=3T#*h@XM5LpM,g*Q*W`B`
%a$I&1rUgpN*)u\FYSEL]&MJf&i%mGbcXUl3%8W%)VBdZ'T6r,3U;qCH*GH%7MFI's[_Q7+YB0>1FI/1dr@q3Tn]8eIZTmPN>F-s?
%dV\3[1JpNXk`i3;KSlbIET7YXP;jeKUduJa.jTfdMeQ-5lV9jg`(bT3*`uDRl!Jj1F9no7#p(-G=)3I)/S`CGn^nuqDR_4unp5sc
%7"d*LG9n;rE>'uT<bjk6<KF69gcT/hX_ZIk[\ZQ?ikk%gh6A=DeA6tT.94J4(mdd'Bm`rpI"tW+&](U%bPp-l'Ea)\f*8uB*@X3i
%dIP'pn@cJ-bi'mJWS-TK'q`L@X<W;LH2s81\JB10[h/uKJ<EN['>:iQ,3oR3(_2EEs.dX2U94m[OQNd:Y?#:`7g:nY?Zk'^`op`9
%HTAQ1[L`u=>l@Zs?>q-49Y%r"N2fG880aD<pon8?GEm?,j#95R1(LK5G0m/37P'[_!Mmh&:+%&)j@3+_.%941jK["P/Ydd-K;?mK
%kLK%J[/t'\=mH+@IA"Qlh.&a_/&sn];6!1!mr1q:Gk>=-#R^BJ&52W0Y9Mhs?-U)=Wm(YTF^SR?EN7Lu^9qJNSp$g4BLbDTVm$Q9
%Uj9I)]-<s/N4#$YQLcuGVgQ^k;=mR2;VQLls4%L1!W)<[rE(46poGB2F,q\F&*##"f8+@b-`"#4>ioG4*7\hcm1T7ZO0`W0$]2O[
%&9JJ-Kdk.XZRU$Nj,1AfXo3Z7XWmKGP9DCUec'Q#Q`)H,^7;frb%POkkUlM3lCR?\BK6&`*cm$L/aVWF=!h%^pAcPH==nFb45R+-
%)[`_diK-0P`:s=T3,AW/eK)hG+E9lBK#MtiAk(61!o6Gj0Rq*_jd,!].EL]RMEBgB2"%1R)[XRIUN$ru6bd.>+iVa?S:ngDicq(q
%^-[FNfhA_b>T2CQg^g0lR0nBRqCR_>[q[Wa2Y'ir\N2sR.Cac!;\hQ8S[Fa8"m*>XI%F;E&);n_C8nqq>Hj-/M5ao%QS86;]tT.a
%6d.Z?K"0s;kk^U88@GhG9MaFON/JR^W@%\3A&ju">mZ$hp)77gehZ[`EZOVG$\VSX2=hVG&ie]jSD>LsOaXPO;O16cqU"WNXh3&;
%g*(VL*_,_<5"8f13e<;g:GfBI:mYuPgi.qe'=ZF\+U&`ddoYQbjkBK7[nsT0iW)%6I0maGd!%*uk$r5Y#mMma`C8k/"g&!Ad@Qrb
%Eg!%/g+3[&6R^';,*Rgi\PFWaaQTSSoOe2%("+""nO'WX_?YZU7PTn0et#psd(`QO@fh?7:b#;s>>YMWN;PldBI[1.:huLf3"M>M
%i_psP'A;06jOA<cE>K2k+N<%iU2$#l9[@p-I!HJ.=h:J`,)hgu;seADP:KpU"uMKp<$OLeh[g?LYrWmS`(Z4Rb#iCL]+)d,22-$<
%d[8G7'/'dI=(QKkYY?Nn(f,m2;J(Js`S#ohYYoOH")p8#,.".:-!15gU1-!OPmK1#W?aIpN!ZKW7))+MVMo5WW)0:lAWAaM)Q3+m
%;rNJ*144\j6C<"fY$90C9624]$))a+&>"aa$K5'E^XfJDLm*"P+2/KVs7fk5rB-EB'f]5Hi3`Cd8>?14\"DW3\b'KKHend_GA2WT
%JhN'0eh2@dFL11k7mX:5,'\i1-u@LqS4SbR842A.E9.L@$m5Ld\9Wg#[CZ!5L'i(9p38eFfeUNJXrc"+5'B6&Te>qSY\F[nr5>K(
%Dfd%CQ#`^&fs%'^"-P>^q*9Pt3G#]YCNXPSZX.9abFi-%.Kg%jH-XYdDJ\%kg0qs=[:Sn8W+)R@Mu`/4*?nqkS7^\%$;Z?u8F3-D
%)&%6_G?:VPWcZ:7B;mE\4X6NZMVD17Ud?V+ODA*s9j<XSqeMXbgHRXl+:Af*/V@[[N)qmq(&HpNECBr!BSq0B^sEcni%uh)V2UTZ
%$$fh?%a[0P<O'Y_glE%nnGfFbX?Uo_2SSatARpsg:t4FqecSht;<V4V!qDOCl+O.SXr2:lCH>2N&ZRqkB?>N@RF8N[h5MrI7eCH#
%HH(ujVJVsa'REhS;$#2U5V<)dr4#`g')[MJnh5-JpEf#.QB'JWc*4*e29P(Gb,"TN\dg]LO"),3J@4ar3"\<tF)$8X:$JgR#eKG*
%TY6U3TPmjUBq4P,643k"aSoD=7__:Y0`n.IY+R8j2)KGa+X3n;##0gEH<i%Y,;E"e/>d"(nL]Xtj!dq#:jj$VD[)P?#(G]H8eDW$
%<W]M4PSOErYo+RhSZ&>>TK.bl-m,MC%1fKiSHRu08cabH[l4UsUQ(.;F2aQOM6SeL>Rg5LonkWC1`#XD77IPUOc:,L+h8J((D@:*
%1$5),1#Y6UnYeTJO$G)_0eNVW*eADL?$;JS+6+,AMJQnK50CtE.;Ul!M)61"0ppLL"2NGMBQs0uii!\;_b;tKgl[gs[(TJc3Q\0F
%TA=*TUBJ(=E)[)!ZqDkhX@[g^s#JMqZ.Imd/O%']>(su&@Y=ouphDnDr:k"><oC4KWKC%@nT]eV")NmQD)G:qr%/GV@6k2=lgX3U
%PC1JW1iAX8_GB]4HrCW5YeJC)X1&[1<H)Z:o(=9A@Xd4?UhG`<B#Z%^BDA72l$n.-"\p;YUq+L4d,B!p`,Bld8jV6+87'WbRphS:
%NWYWT832%Q>V2(:,WDI:c^#sWXi%8uY+PO#9'pPY'<K1[0CH%s8(e9DF$JR_=`qBDhen!4,_(I@s#!GO)7k%]^Y'i%"Pq2SPMi\u
%ZY0Aa!#ajJF2_o!.?RJd=Ch!N"13$BJ57/rqN_FGF<<!sKbKZk#ens3Z&]s/etU&Tfju!L%]+gQhDhnLMH7brN?A8qj]`'dX=Kf\
%->n*#gnX`XZReB,1qRq&g8?Q11IL7/T%Zp5)7<.#%44O(Jb@l)Wt&>oGFD"\W+`9g\'^"iBD;i)#f^[qh)2;rI$603q&9JDHsNYO
%LK.fs'T0%4Fh&o5Ke&]$XF>oQHkfmYG)GF[NVC)cB=O^kJj*@sj$Wt[>HPQ9kOpc9l[/+cgNTX/l&%\o7eVqd[Fqg]A)0(`XlTQC
%b/*B2d#q7R0SH%\W;WfJ]1)u.B)6'?J(>7(fN,<jP3#CQ4e.m.d*3^f;9hM].nV<Ug:B&%!mFBL6%kh2VgBhaOIoEc84oB_0BXPO
%pcW<[jr,=]7BsiQGdk_?5!Ioh;R>bRIQ%),#\"DuU3uOL,$ce#8S\H:(C,Tja%dHA$]"o$UWK__aG1WKnD0KJH-Y0#9TU_Vf5COU
%H4`Ha2>[f6#f/VFj05mRnN]rT725?Hj2V0.#n3GUeMHB0iUo][6r4>F+D*q\?K&UY`[3g;XuSa'B(uRn`Z\Z!mZP;Up:cg`-$@Q-
%O/n.@XEV&'\=EO?_u,\'/#@HB^/)Y<m`=oN*kH_',:V-FR03@:qY\X(V(OrdfkcZeGna%!:$c-NMV;]/Yh>It4,T1a``i'&(%Iin
%,!Y60S4I_Ne0aYb=dt^[DQ\TE!]5,5/qbP%oN<3^,_m48C-0c<.`(BN7ZGE1?Z6U9mWBV7Q72GO_<Hs>Uig,"S@P5KUpZ?e@Z+d^
%Y\PYH!*3G90GK,UAur=Q/@Clog-LRQ\jc8MLjT>t"o-$h3tDs@WFDhI5QQ-dMG)H^M.u0$81+Q.iCS*U&JNnDgELo/P")&CrYTRJ
%4+jW#[)[SgZJslTrR.VmqPYLN;@"^lT1!5\.prjc?1L*94;_\cC6>$JdY:5Gi2uI(<'4&S[C^UtNWCCHHW'3]]hda?T'T3NJ[G\I
%G#YD"@SW0i7\]9.B:QHrBQ,a:_R#Hh!LA8`L:sVd!XsRDOui/h8#>V4bR@lamkoWIor^B<Z,FD`8eFU);RFMiUK8>\p5;F+iNE^"
%H$"TDfT;qFYD>q>`IK+d3tT0.[lS\/MR,skONpiS_Ct.t?^4e1!ifs@0m5!W$!58eio<rTR1KB[qB0]?2bFu`Xu$c#]P%<:)?[p!
%>dj)e$O.$8&-DL?Gu=Y.0u>AR%#?kqp6j>r%CEm_pL%Od(lTj_?B88F!See`8Y')Dj]DtHYm"uL,?MY2-sSE[LX9&V!V0q)p8KpH
%(SP<FTY$ZU8VsA,LhBdl##Lb2F+e6-"d38Q&To>"o\/,aTYpA+2/)%4HJ*-WqonK"kUBpiT$]F/,o0cp7.`,#]ta>\:?3)@R!+q)
%#9u.04Yt^AB*bl-*W1UJ9V3;.A1lY5#-L1XWAf`QU*[XU,;qF&A#(M`]JT`m@augN$j-h9E\dh:Ub2BZ6'CG6\m7<V(_'/l_dMO:
%=JC$/iDBaVh3;[-m\\.W)rK%;G^+Cr8]L.0hofOV<iH(/0JeI7=i%O!Jc2#q.LBROUOmLJSfAuFCtQ5>>9pC\J-g%c&q*RB!&gn<
%7qcm+o5)l58R?7N?q<B*+C4WQ_o#>G"%Zj]Q?=43gMs]p=^p3SnSj`eb[%6qE#pGei=SDA'pB[E#T0JRjcH(2N"fF2V^0Tm#?\QD
%)3jU#])S5q7N%?/,I!qVbBQ6e;hc)!?i$4[WjRn0Aau[/0ika6>5L9]p2joPN\u5*[$F#-p:W25,]f(&jM#iVT%39`ROVae0k`:M
%3I6sO@C_o!\.GHhKU\eV[@b3:cl:/La?R]9f?K;5DR7Poi0/e<7J>jf-"7+Zjm^=5,P/G34I,DSJco*r9TUYb_[t1pZ[YBOW>uGf
%boKBO&h6./EQr0We\N">*_N=[5"3qZ&2Q;FPUV7g<q#hoJFN%.)+]m1JB"/m-c;NkU>"hcFEA,gVPdf7(4<]U5CS33017[X4[pDP
%f;U(-U0fbZg'*tiPGnYO>dGLdb-fL]Ne9a[oDRk$=g)G+:B%@>DMfUcGhKML;tqN:jPQ"ZT'X%Fi8CVY!99J%_JRo>,WmYK&1U&2
%33(pt-l1ATO1'/rFEF99cRtEqqE-#4e[77I);6N>j:=@EaMG(:]u$g2r+3NRH79Hrm1G*#\3#u.[I/\ql;3)SHg@]T!c*;KOkgJ$
%erecG'j/Lk($hs`a$uCX%t7[,rbs%!(T'S"";k1r1\ZQ#_;+:t3Z%Hej@2)dJ$;MQH_[cCSaL:u)8`>c$J)Et"\*iYQJ/kl(qY"7
%2WG,d+gS^Mi_au>YSO2/cL(ms(fd`972+_<=K^[<R@3e='qfI\9>b^lB9[5L1$MmS4gRoH>mt\/rF#$4M=3neh1<"kciPTo/;\5[
%c).,IDC=B823Q-N)gZX++-n#1-/M)UI`JI&\SB(.LkQBF2QdIP0_8<,]9J7@`F;d")TG_8k+iQPeebBAU+<tL,HcidF:rF>Rl[8l
%ISiQSg91%Pj^<Kf`FZdGBe1sehjeV$QI]L_cti&=(Z[>\-<'V@_K-c^2(N)&[hg5B!?,BqAE\OMkpLkLOj;D8V]WMQ@tYZMGHCV$
%m0JJA4u*V54!LcbD9J!`mLpJWL;s^N?TGtYKd'eda(G8>RW6Bn@A@+/hXPs,cTj7-,q>7sYqr9iO&!t+^2C(%bXuC->G-Kl`[/PT
%ou?C"8-=Q:hoP;a;lo)pG<Z1\>/PgM]KaSb1##<(drXm'8,[G._9F.>(8-W*".%NP0RfD'\[JXK)#NV^I"K&44,)c%*rgJBX@aZ^
%!_QI:T!4,cjbBYp*&g*H^ntMq_!7uENX*eo_1F.1C5`ZNDKB\Fb-3IY,_7V2jtY-g92rn&l;G,V,ZWp1+jks$!\egYL\6I;k)5n;
%NLpTXMG1H8VJB<,!Xuo4mKG;f0dF2MZ"Q8KpVl?QE@sO9:U`IcH2*k:&Mc-8Ls\Lkn=:p6*hXR0AY^UL_AM)=3j\Z7YdL:WA.G&i
%V@8RB3P(P6!b1aUV19c-N4@*.T(?*eD'N3Ed<aYs[I!6H]MWXfR;t]l#7tao=a]Y@e!jdYIJJ<p^2H+1g3cfj.ka/4b0`D5pA/R+
%BK)8-ZeM4fi!p>ffV,fre9eH8"gJ(>:O82%JWWifk/`XH'];s_YO0]J.m48CVsEH`6/eg[1O#!s)/R5mg1so;EdkOIb`T.Zr_WBE
%^P-+t-%GI>V(;-tbbHa*)HAScT]Wj0aehNIAFg"nfn^0g>!VB!$M)`sn]i.Qm_Ocum4`O7KHaWi1^cVl'GO?.[`;`l&JZ&O=K!@h
%,;k+LVHQ,:\KY('N$Y8>CCq%`,65;@Y!@FZngH5rWi1Lpm8nR[37ME?,!7gRcq*1lKhH*S)8+:W#e$`HEk=j66!.8Ag'$*ipS1X1
%82+<'$/h)r+m4P$/B2<>%GO+!%=X9K!jR2R-^&I"GR'%(g'gYf&%<18]&`0AOQHJuoCd!k,:\Y8?nFo5iFt7\I!#13+_0n\;He"A
%+&d9i'M'_5P1/U22;LgqL$%PQJp,m.6<mkECI9.A>hZUNjOOUT5=A^&q7[pqR]KQNmEZ@`2u*7DnFB8QAZd\_mU2po)-5`(DWubc
%%<F*)gUB"(""Q[?*E7YlYY.NE#:')#oOB/(#rsGnKpl>7D4Ib1/U)n>3:R5jVDE<B"aT?BS0DpJR*piJ04FrN?jnl(@tM@l<6L=\
%/CoZ(DH$lG1D`oo!u%7E+/eohfmC9K%Yr36!i8N^-mn?_OM<;<#AosP&fb8:l0XA;P32#SZlr6JLH61nDP<RkekVR]8Pq,XJkt>e
%2JI.MCP?8G8ktaqc-O[ogPeSDD*$W*%gp::`d7^&=[ebN[\OEY[cWe1AS4BeO?F6;n9'SJ_5JPbj:+%kO?g"s5;P)c<]3OlmoF<(
%:cE%@V]hfH?=9AKcEWLrlWu,*nkF[QAj7XM#?;9<W`W.PKA1_'n19^R-X<;AfLnF-)@k+ccYXft15cYLlh\^gO6T9f"GU/[?doW6
%4^[]?D>0'5ViVClK1l^@+biN?OMJU5/Z+[bQBJI4l9-;lSdE^OPXsB&#1TforYP9rYUtG7G<,PXVr]K%UF+Ej?&33MYk[39YAK"2
%C^6_%fH5)n\Y*i/l'h9Yl0^k*`6%ZR7HUQu[6krJm`>Ba7n!.2Mk3/IO`&b;7e3Kt,5.BJ)2G8?f4WBVW\:f;=@+_KS!#2";tnES
%[+$u1Gj0ES>p9\Ddr:Po_Au?A'g&=sE_aQ=Wu8JuK$"[og(kN=p?=e#<%,`6?@FHROV$kDBD^oU][E5^6gkI3=b@oI>3`=?DXo!T
%omE6<koCBPJ&EH3j)s)k7_M5n6F<IYjV/ASZV:j>@dOk'BN(W.Xo\F<cs'<WTm.S`Qn0U]/\B&pmOYAYQ(hSI7&kfaj>0_>dR4e@
%)0UZs>1FQed.ossZ0&pEY"oo=q.GF0!#RIuG>tg8lmLJIOIigP5(H+$D2oKlZ;\l&=!DB%bCof>qC2\8Sm_VWN5#7^@G.&"UFPGO
%hKhj$8%VqFq/VHD3W(lL^1&GU,N?Ppgl?d[0cQ8sp_<c'3KK5Nm*[nQo%%NV.n7U/"O,1Q]L,1U<&s9.&Hfn:;`-Rg\G3TIS]MDR
%S%gB+N;VdPO@1!Sr-PQ=l_kYYKjq5h<Y>Tuj9$K1K&TqM''-EH#o4n\:=:b1A`$V[eZCO'KN'Ulgj=2jiCqdf6`;D$9dAh;%4ZR7
%oq6!86Nb]E[6An>"g0oIcgKBi)Hjd#Fk>CnGF\HI92L$'c/!JJA$\`3f.m%;\RfsV]eT+2V7]tqDR\&;XS:2<<jp[5H!!7EOb!#%
%;]u:,Qg9WcI$$V"37$1J=\niu+&TUjT4@Er?&_`CPIFj7e3`?"nd).C0Gk3aAn`M@8Bq9">+XZi++e.[[BZZ?Jb-D3QOG/2MIpds
%m*Tt0KCo6b#hP&b)&"30*j#(JaL8Ge+E:HSkHGfQMZNWa<s$b?/WP`aW82nUfN'lo`H3uMNKk):<(1fY=bdjJd>?6aT0O6u>+eK0
%I^&jUYal4>J+ipc1:ibe+:Q46F=1fh5V!I+hXP&<VaUB?m"m.'6,Q?e)UH9>iK_'+!ZWC],5l?+U\Se2C1`Xg8'T6]1OXXUO>*7'
%me,"6KOi`;EP%&a$gu_no6A?VnB$:<b]iK>im/50XqiX:CM>p"Sf2-2R.;%<X3U:Ch+`Vt:8kDDF@b.D16^p]7!Xo.R%u8S^Y*H1
%Rt/]*qZgfCQZ)u9'/^>qiHi[//MgEPE+`^KU!qg3+TUklJ0-J_fb7^Cd,`d=!+gj=8EBPW6SI1uZeYW/7P?*hVYZ^D,e]#MQPB$"
%415==EIO0@kqR3Cd3L.0/>.`neDjiM2B!E*\C%6!7S^Q,e@DW?%gfTTC+.)FqItPU(*3eS3e4-L^hnt5LN2]dDYdOS*@>AkE/2<k
%k]V9+\k5_C/1G9>D^h>36;f!3`TP]b`ilL(d_.r$o@SusIYmm54X+:r1cU(Ys6`J2<\]T<@q\gMGa72>5GE?T/8=[[b^Fh>DXe(\
%85PpaK!2^K_>SMYB19HGU\Gl%PMj]P)Po9Q;[`^t^041Gg/]eiZW'(a5@H,eJn2=jHAA?Y#UHhuZ4gn)UE*P_^h1YWh%n"abjX0)
%E[<EMAIL><0%q[mZYXtj[4Lpc8.R2pPHp\Ep8_F6%*0^j2R'MN<?c4b5,3V$93@S%$H>=]OD?#[;Ten(B#(rCa'Ta*Pp_L\F8L(
%1kR8?_H*QH;Bos/r>$YK@e\QsaTnTN+EgG]7$le@hc`ld<@!4[7:oMJ1K$nU(VE:j=0;;tZKb`2H:u%28a$7$=9?Bb3rR]/RUiu$
%QSQ&?KJqaYF\fN9gI`aliqdBD(;(AJ.K_"?]Ze\-q_bi2o0YRf$`(WMdhVOF+fYMX\K]G@<0TiPh=2!:nQDoCa4)*jO.-Kk8nNpF
%NAB?R_!M;mNm3OgkT9a@=gE9X;GC3ZVDIheW-H&Pji+ejm9Sc]b$=JYknJGUQ6BT,ccJ3.ZhjK0g3(l]DouZn2b4#m]7&7sZKtdC
%k8eX)P.Wq,*^m@r<F/,,5HCq+@(j#A#9F*mm]0upeSN3oI&]?4Dc97LA=)-H6PV)-j2&FM('/q>!L9"=QM<)ZU;K[\X`R$^:efq7
%m0ch5dRqm$8%UI=R($63q$T:B40S1t#?3\pfbK<aU"7UHU?93W#(^r?/iAkVQtF7N&'?&I>DE.(SgT(R4:q<m32.e#juV'H#S)W)
%0_!rPAqeh\4hBI@l%Z"sr?C,kd+jr.f_M(*O^R(ZVuj)3$q#+_U,[1am@\-u9?QmR_?o&bO'_!Ff<7cTK*fI:N)J*'8#C9=\qR,\
%a4ODR9[D$nn8jVSD(+;l9l2=EQbmnkR)ir``]FFaej@QrebmDoX5l"ZH:$ggaC>dqld)fq&u.FYd2+UK!Oq;6^CYrb=^XE0)hhP6
%7;FBV@c>V1rP[54a0u]cf?>3`2m[)oZ*El>Hmsl%daL&CRRDr6'h+r@IPS4sGQc3);R9,=AAVo_^lImOhKX@SKG[VAZB*5_:2;q$
%h6`X_%!bG]$&a,IYnhK_bAP'73^B^L8;:\*6:D%Z#l>\'pmkL=&be?<38JIB,&$?):9WE(d>I-5K3(jSg/67LO4B+[=rG^ZM!C-/
%j?uB`'(lVBr1h>X4"]q=5t1X);ajETO8VW&bJqD-He"q8.(o;.o5e:832BI2M-j3iRZ`B'Hm:TJ(g#5>5q[>bq^sdT2D=ZIft2K[
%VA[3RUtjBep%OK^Mp'AV6j0*BX=_oBZr>lWVm(5c!=A*Ma!F@d4L)q>L]jp"j^9>DiG6YT5<,6CIlquSG_u3b_qsM,FaGp(XD<9E
%8j/HIoYea19$7]C5!lDP8`gUSScN3Y*I,PE,*M?Y\>FM-]Y'@Ij`fhYIdj=5UhT*e&?RIP'#43R#5[^PC@V2pqQW@6;`.egV$&6;
%g;pg@KI6tJ'0SN10e2SipjNG(&T(Vg6"L2tH^rW5Ga1(ti@838I^#5?HAK(AUlcnm-MA]mT=AoqSJE$^Ag&NXi_jC/Q,k?%m\QF7
%2en3Gb&D0HiFT^@Gf7D]ncWm-SekbP(KqF?aVW[X_/qoN$)$$@\6l7oBD2BFFJ5)V"5[(cMg/R5/=!+cZTbtMmY\'2W(nCdG:9SS
%knhtEe[NjlQT7\%JToq@%1lt1<,iBe("HpW\3YGn&e#%&!^$%Bin/fB>pg74eOLHD)cc?g@JL&dQ('^q'1I?4c/YUSmOjX]J/+a?
%rQY6Q1N#:]\9K1NT2qT^nEkM2^*o"'Z9CY*[8aGLi`=ti%&Xr@#Gi`QYDiF4pk+j!17T%.Dl1V/']aO577QsYcmUJ!8`F6H4<Bbn
%h&ca,`Z7CD-4XF0:sE2<(J&k.//%%TV,Z%]UBcR;b"ePd-'Z`+J1Kq+3=E]d@?!J*DL'CAQ#$)K#n7t=fBQ>9Ktl]rjX=J67lG[9
%jpkIu\?k6e`e.LlmCIr-BX\Mc-giMdre(__3#(BD_AafP>/nMUU3i.c?.u4A`lM#^a\O;*Ys1qV%$+"-G#sZ#-0)Ol<7Z#`F>lDs
%k(&t'E6(<PTW,W.Q8daa@dr@5gJ+I_on_am<T>G"KbDg]Nuo,$V]J1Tqdn,_k!dhK,9h6;r/mU@7Bc'dAZKTHL$VnDi@u)uRNSKl
%NRoM=30/i;(&bek?dS&6N]C1+&V;%/*h8SR!9pQ5<Q8dRPj+ebEUQm&<VNNO#hHfW.lrClRiY[36EiE.5Utup:q@]EE$=0Ii1t2f
%Ar],E>m2,7`E,ls<5teI"_.$m2#E>R$4b(/!\pS;kj2"P;dI548#VQga"h++<l4Bi$I]di!_m0A3BSWATEFOq2'Bic<Fc<T_RY\c
%%$j^mV$)/-+UNK'*"(!OedUs0?)XG4/6Q)GOs2tl:#&>NhA!UdWI%=!GY+EB@glf#;b8rV.9+-cecg)_K.l<Jc]e\l7N8P3,D%DX
%XuI['ddmp`h*@ub9Hob[^/0Mb%$QI7KbZ)MClRA.X@_VN^!/^"6W#n1\sdbIY8_+MArY#@hb*0YJ/U-V\rNq8:Jj+"*^S.?kVX#S
%><i?iCs<N0<<Bm[j=TUrJe")rX?MJZV7cg#P'H)D^\D_sr8UR;r'15E^]+!-p4pE\q`k&Ps8MnfSq$=Os7;#\qu?ZQ^]%SiqQ'Yd
%[t">5J,Rh<roYAPrkm(^ru_76&-(sFr8jRMlGoOrpih5f[t!lipP6NYmsjr,J'f*_qXKVrs#5Gu^OP,!Du\83rqYIELVNVVJ,%3Z
%r1p:*r5/#3If$>'0E23eDZ90Ud!tj%)@kVRr*PCireh)tn[JPX^\lQenR)=YlX0V\Du]7OOA5]"O8SJ4,C^r?]uV!mFWdr*@!-Kh
%5i[0"h--9"EfAaR2m?/7BDJ>Rd?,Wt;58Jn)%._+akfr5Q!70-AJAYJ5#dJbf$9D>mJcX9N!aXaH8&b#Udfq%g`98^37gj.]X)]b
%K*F\@3mA:'\?^jWBRc;pXVO.`bDd\Ya*IkXBCUnA5$c0_7\KQ&P^A5Hh;gkr4G.>j;+Ei+*bQ_V%pXs6U+n#M8u3IH_PHZV8]F"R
%UDT.L<8e$kio;UtAVGD$3%^,s_(jf*W(ul7']m!C9<Dfq!gd17Qo^B(]H.bk+:1uZni$s<"p:aDCK_V>TqiWXM<0Fd8"V6rZZW5U
%fe%c3?pJrm;D(SgQ/X,&)oIe;MuY6t.'tQk3tifi:iWoh?.m.+1)L:>hT+J@eB;f0,W!B"1R3J)[!ML4g=W8r#Ro^"Dem!j_O!Zt
%;i\@6'\D&^c*2UFG*4tD[0V^d4+2nALL$E=LmP1=e3Gb+%XI1&`'k2H"O%W"p;fr0&@BUcU2c1[k@H0HN7pDl>M#Uc[&Ze"qc'W.
%`'PYdW:i%"\/cD0=F\R+0Wr<p^4PKF/\!LcET.]k`G[FWEQ]C(?Hr3@dUVmm;!WXqYf4r^(r]1e=4iA4`A8-Y_"[6PZWi%#pelER
%8#a/@Hj)$5@8cg?8GPE043X=Lp$fIc)PDpORVV/V:C>nqbN[Uo!g6f`,#5ol4\%p]KLOs^5SVt+"MrN!Gg:!3mHe'@VHl7t$rR+t
%bgn.<]?>H]QsBbOlMf^IEPe+a0/Y^mW\PL.LJ*H":ktW4a>0rgA"[RNaogA#/%-R0#Db?AC(+/L<.3=3H0[Z5O?tDtmopuMl.24G
%[-8WoliN4nZ=>0WZ]tchOU1jka`5DRd)RHS'O^r_.\LeB#Jtk[n01%`I:9(OP#b9*N?CG8`9AmfWcPP.&'9`^ZoJb3Q2VY=(orZq
%EJ<CW\;j+1.U[Q0jB\Qp<%Pk>G(_j-6[W>_<m7;fN`Nb43s=t%Edg_8b%CgCVJ]T%@cIOHlG`f66&;$IJcN13N9Xs0Na8G>9N\sH
%_Q3@LE4rNI1$d=5As3,g6pHB2I7,+T8m)XR=Sn;eWHaAh8?bJj:fHsJ<)nbK\i0gib`11qbBO!+;u(VQNN>@2UO)cd#fluBSBl-K
%f\dsIp&PmJ:q2jQ:#4hO8Ad9NO>j+68scE/Ub1mM`ZA"D-r:jGfLK?d]Tk@:a6iS]<HOpS#pbNmBV4dTqm!""(o#Y>pP&:,D1:c4
%.2D=^:k+d/:F/D!03Lf3fs%GdXer#QX-r=UDL?Hl[r%/ld'#mi'/?U)'fAm$R66Su7RormRssO[-ceXi*1MQ9&X'Aad4\`!9i.*X
%iP$8J74l4E$%=TZlA^Nr*oZOX76BJYWHMlEgIO1aK8etm7n4VRPegsabG)'%Kqa0#)X%usEAn&Z'BP%'R5k_UC.SYjL`"N7SKZ@X
%j,jm3XtP*UP3$4>#:Mq"E59T@mQ\]4"^T"q&4tnGF.C`3+H6blDo9tQ\oB8UXTtLiqsf%#Rd>&VF_)\iW)-lUkWG-@FLd"Q<Ls$&
%(M//aI@-Q6(RY#9njBngS1bLBNof>A>uo.kRLeKML;YX:bi-=EK1Ut>gp>[I;0-i#Ni9(37Eu<G/:8?_F+U%r&inbW@`u/<[%(&#
%e0@Wfb=R"c'kK\r_Q4;3!"4%RBUcKrZj3Ph?87dA#R7NXM4OMZd"1d9,T6I2Zc$.X)'RN+^Wgu[mFRMn<u_oFT3L"^5kCDSG2tuN
%.<k.E_Kfs=qmT%E%TE(`E%?3li)6"'O=^YH1ssN&:&7ek.Jm'2Qu6C0\k%m>9rKDeP<9!rVJEO9TJf#`/g-^&WWpZZ/2"a"h:%nI
%TAm;H4^h_h\k49+)#Y1uk"L4!BqUiP<dE[H3'3'ss.5)rF+A0-oGMd4L>.D\"t:PrFCi$&>@bao0MC+O>ekP"3YjR^Fns@_\4FVC
%]4i.MZHa%s(T0#H^U=fF1TDOubSeD\O/IR*T3pV1RV;9m-G#pT`:T&K+8<IP-dk,%'G9Tqr=i%Hh.<3I;$R=>O$o=D[Q\<qs'nO(
%Bb^5)alGeR;*i6-dS(4cmdP9`k,k,#jU&6Q@%B7+$a=u\U82AG19j(QeLVL-)R4JfS>k$lno54eGQ;fYL^1NtdmkfXE7"4qh$cC_
%7JI)ad^MUlJOJcXltrFid7mQR?JT10lb8![gLi>-`M\'`;-qC:Q9Dl6kGe7c=5YcR_s)D8OA^^ib=F$$9e(*Y&0RAJEOK#rOS&*h
%&@.ipZjgfjTh@C-SrP</dY4E#_jnZA.+ps>cXY[E;l/H>ZNZsT'3[DJ(('bfgPF>6'=h1$6K2DkP>egIALc,:*!5BPlVHe?$S<[h
%0!]8?#9ME@:Qr(d5G$8U;M_<;RedLTIU&(@(Hk*eG>$j`%?WH0Hg0rM<[^$6o9Z%*)7Zl5d2\ML'RLCo?XN)+bdGOhb\JsiQS<li
%"f+F2cfi0Zb5llTcDG#c;`d"(^5Dp@(=q>^n.=!['\QU:V%^3uO&p?l7bBN95^M:V*Z$FiS`o"$T['5D]+QDtkEm19O`!:EG.C0i
%bD^F$nj][)XW6nl(1JlnPQk2^-Z-Rg=h0p:_SbhqP7c+([L*CfU?&dU+I&*"Ta3t4fA3aOPjJ\#hf*5O<%L\RX(As[-BmeEA'UlP
%M,kYIr;[;=dV9_e3MZU<mA1%@LckWV_PsHQ&S$Ed/2AuT6XLIo&8Bl7e:`1/JE='V5\Nq#gfCVr'%2<P819JWP'7ObnEeOd.I0]1
%Q22"4j)P93U0-+f5QY;&J@V&m:L"Ei8)OG>aJ*BGO20C<7U0h<,Ihdm!VX+ba-?=aGhK/_Hnu-,f?596gU&5f$)P2)N$WbgQ,&P$
%*<BL<bFLnCW,NGII.3rkCcLj/AAL<nk3bq.4SH<cH3'4hb3+9fNuQiKXjHM\/c(N*AMu`Bd`pOK/OqSB5a5<)H=f1W?!:;4<G2m/
%6>2`jL(+jC"Z_#c4FRGIL-ZB'7A;bOTUmSJ#4S$4T`os,^")@6iB<SF,Ke4>:@AH=Fs;alaMl+"<i;rMq^.BL\K0B3Cj$+=*SW0A
%0Z:uQ3@!E/4:W;X"3.R>+UcQa]4:rCHmk#3s,b:d:hp<X*668*\2q/iLqQUMY&,I.nYrVjZL8\s'qMbY-,?Yf2oED[Eib6@0`[l[
%gVAk%@R+O>).;CJ!I:i4`2>TTZ\mmDEElG[4N;)('VKU$E;)fZP9'&[bQrQKD*h1p/#Xg0`k@V$Y"759ER#:\TICP,%(kpjF>bsa
%XpBOibT-[nBck,=4d#n8Bi!$&?q/^$Wpd)2Iu`;h9"Ego_a],!cCo=:B,Kq.]&m)e#[;EaG?t#_bnJW`Z9FjGh55^85!EWRrNDM\
%+M=^kh<`]XK<]+J9YhB(O:["g['B;a;#UH6Q$_^&oC,293i8/U:":P+8@*TYU#DkeM5/*H(58fcfQ^`9]b*uhJCG>`Z@_a<,`j_2
%A[T!Z.4Z6>T_bSqa2J$7Yt?^5TQJ<>$5.[raZbMNG\+Bn56C=l:jqW<+iOKBoUsC@f6gQ)i[[!?cclap'"bP\!(M<rOYZ8R7cc"Q
%Okaao@C[g+Fq!TTV-=(l,pZlnOI6;<6fHXA"bEXd#=.c+g!QG8+sAG^Xt3FC09"AZ*!4$e?\08QSfG&_qStnnO,paIH&)jt#A-fJ
%g$&f#,M+?+Li&'YiJYk5m#L8r1pkeP>V]qY,kXaj^.,iuV$2-5(WHgZd7_::::OM2Z-H-SYtLiu_CuUK)t.rE*J`Ga;)'sTME*0Q
%Ai9mLMjeLooM]<?NLLq.q*KPIfHhGnc/(4#XUtj/AB<Y'+lhMJSilV'Hb:FSBHDl/!0Q#2$6Q[2e,^fa+h6?bp/p+hai7VJ'P89c
%$>(Om`lYui?O&ul8*"$<++FH0A&%s-VbadHYdJHgSJQ+N1tM0P=X[4`BE[hkRaqB>."Y/no'(Qq^e7o&)0[qg"$MebpE(B'Bck6t
%"YebVY_lK&CNhe+#U%_)es>knp8/H(U3#l5YJA8tBjbOX[BR3>8Ted@nm1/<iJJ'&(6M`rob;kuQ6/B&SnX&keg9_-%!1L)-p#rc
%bb"^iOf>?1gB#So"f3G`j\3!n@l&-Lf0A\\/SS0nJGg96R_s44j,Fgl&9B!t1/q/hr/k7Wd,3:G@>bicHo0\22fsHKgZ=?t'loeS
%,H@*Vp?h/!L]@2H6gY#7+F;Q)^k!9eH2r*RnMDArS_Sag/HWPLA@V9O7j1YIUk"_i-Rh4=+;@t^(,o$LfpZ2KnK6+KDE8\-VI_![
%)*$[L6jkj<[FjXh^u^IdgHkX(iA;FUBY:4K-XXc,YDQ%^Y6nT?2p]3-n1tWemDZ:nrr'%5rpQ==:G.`0*.!40gL1]I(A*Vt,Z"]-
%/TiTrUKq`Z=_V$[6Fbl-qZap9(A',`N#,nFWg>@5ZJ:nf/!?%AH=7%,_t5pDI22E#<C"Hj[:sV$MGXn5j2r&[:ua^S;pQVurF;!T
%o-Rjq.\"d(84B'[kX60;hb.l7kDB9BPuUQf?o"_)A%92nf9h,q+roJ]f4#/-dJd._-]eH]cr]=?^B4eOq_4TlC>7^9nmJ+U0-5>K
%2,'"hgAInP_!`r$J/R[d(=M8c6^6qW!ItfET::eVk_tgJ!'641e7DI2BV5LUFma!qfMI<Oa]uT%G=AC34q_77hCdh+Wm=tp,"*Sc
%l4WlZ<pYd7$+Qd_/`)9aSW%AliTn820A*,F7IZbN2VV+0QR^e484`/cc2W7c^8h$fpp!5uqm`F!-Euj2DY4_g^uP^Emn_ejq8OH2
%)/hk`d3EHo6_\p^+11bY!*D[GXf`V<B:b1$mnTN]1sA>4Q+I$@WC3mCouTC,J[qTTMB@c6NaJY5H@gGoI6XS_:OAE.S+Y=O<qI,h
%bJ2X<DfQ"L:3+kSZK3+h1pFd3`guEPpppY\Z9_8?211-mcZX\XG-`"HlGqRfaS^F,f\h&Y2-q#kg^bE7p:+c^ob*^i:_dX?ggb0C
%H[V$Pps^t;mo&ibk+N:5Q+MKXX@Ud;jY8^5mo[(q+[jt:"@K&LXD`D7\+^Ai@h6<Ufs/IM!k,LAb(SuA`ROiKQkg$EJ>D7"96fE,
%fY`_ML6YH&]D2qL\ojF9+<09:(7I+\Pa@oV9H7Uc/aU.)iE+E8p1GY%5I`XU;b`!93q9`4Z_S6q6oUVD)9ZRc#e*BqF>uooWF;=5
%cj%,9BPCe8n7T'2#<b8D[Rj<jpHaMrJ%qi8@rbYq(Ci&1(LDtGY:tj2MoI-[N=h6]d[NsVq3mWtR]`(r0at;79D96D83B-Y-tBN_
%Cg-@6T13o?0Nn]!7R?CnHEq<-W*S=bIe7Ik1cU@L!\")kM5B:NT-^`@JI-Ka1c9eVe?D?$8I?>t-XbV*2H7l1VhcL>#mjJ5oI[<N
%,IoV;8R,6K!BfOo8mP5QX<-K[<:nQo86lMh/`A:-$gK3_:QP@_+'*Ti]2si`]?i<lCF*jjEXoP`>MU^Z@k6p#LVGXYFJ5@4h+nl5
%-(sq<r=OmPnSpUNZ@!!K`H`11Rdn&WP1@A$g./OK&kVkic<:]#S1r6Z4`]@[BuJA+<,^McJhLCsR4#SDH+r/S5@+OF\$!*Ue0--Z
%FV9a#dIcWL;.Gn%r$gQ?'1,3m.L@faRZ)MS"V<h$^<Kbid3R9mVQci_Xj*c>&hZrAB(@b-Xikbhb8np*:5BY3T>bG!U@;kTP7O0H
%3ujTjfkZYc7<LfG1.ZTcY2W(cDOpI>#/(2r^.5.fb,^8hAP$YmPk,ImHlqg3=Ks<u0G[6h;%jXkRf/+5l@eXgm#fbIgIrtL^uWIg
%AL\--SipAl`Mm4/UV'L)aTF__*PbRDBDJlNK@-K[38LM\/A?s@F%6Y(D:P.?7c6gnH0:6jRneWeNbq[HKd$q196Te4BC9X<'Yg\M
%,@UIVQq",TT404M8r&aT*Lu'.GDcAp[S?AjXJ-pXXVfk8)u8UECPO/'[3pMl`!rsiQ3;5=<)EdPeRT5%i$"CoYdmBk*_'j40G\W]
%&4Rh!&NHlc0uB[C%V#nlaOp)3\s4<VP;(E"7HD=cc'ueL!"Ql9N'(^La2&-ld.WQ34ECV.CbcWNj7XVnoVDcaYLr1!)2Lfc88`-)
%O<D$X9NWNXGg+5i9j^:$O;4%R&o%6S2#nJM5Os%#"L5'ulI@S_HX?g:c/d_]V&8l/FLup[i7&$&#,fV2UT[WZfDq[.GX<6o15`4&
%^].;Qqd"pAs,'2"Ge@r\A?cuNhaJC+$A<6I+]!0@hB@=qA,.H6+DT320;Co.=J#c^+`6K3mW"49ptKD-I^;m^oIuGu0Ll\kIo?ik
%a.Hj*K#UrXrWDVQj@XUEna-o$E!5&7+N#i@!4J,"nDg7%?3B.doe2O)%l$KBK*ACYaaM:=DY*@N;&DjTa>3'.f+`Df'fBTgfHZMH
%gU8]!F@'FmMi5jgiS<?[@?0ucetE4<I/j_2FL$b-Tb]oq%4jgVBT;O#d#jLRrGue[oV+2(PYN*BqGoKr+9A_r.!C+i;!S7W>'1a]
%,<9cZT#mmf?Y8XOT9t<(YZ'0.)-_,+n]t'[QjI,#/bl\(O#mr$jFRb@e-)*<I4J`#]4ZGX/5h-U/q@+t/4OT]&lOJtACE*"?p"ZH
%C:HG8@*K7KoU4K?5Bumhn0$Wr(Wl-@`iGCp_"'ne,<ZNpO+F'A"[V8gJY&on=_(YX`:'iIhGkkfo%#d4!$Nb(eNf--pZ)@XLkDN'
%=4EObWM*u_4M!>UOhtC=LGQ1`2r\fA/Y3)=X8W=&0d<.\=<slB0N2cIiuP57a')D5<K\!c?,X!_)BU?D.\S-!cfp]U$gbP6HUF3H
%#sf%Y7suPSBTqRA77hDk"(5D4)BI6=e&U5Zc9oTR3.E'/gTaj!S#8@KMMOK!>-4cnBIP'hk[ZUdI7\lR14_5_#k2%g8me[%`K0&S
%%G,F<Z=*'AI*%eg*C+<&:8&Mu!RR(A"%HG$Fg)X8`rgiD="&H`@oT-9#t<%;6^Rur_Ef;]-.PYP)<Nu)G.mpgRHFiM(c;<6/kNo7
%@oEIqP[c(I!JT;A;NtC94Lh>E6.Z[2"CApKd8&*&oGLhgnKM'T"r9IZOIHYE@99n<k,$I!2WPH@>(Vk[K+ukN+aR+F3(MX>_&60<
%O@j,51Ikgh0pT=Pb5?\A%mN1eeF)fK!l8[cY:5.+_c#TXs2o)@T'])?fqPbAA#YGJK_O@J\:L[-/)!F#hkY=f@bEGG@0WaFNhC6B
%OhqMHQ5nBq@Boo-O!&:@rg)>KRsN@-NGN=i97..u:Wm=$2ngT9JJE%INVPBP1YP!Q23jQ%3$\Aabkhih!e-3Hhr>rh**@X/r1)cn
%_7*M9Rbp<:,[k?+Rhgm7HEj9q5fo35hRKTEPHnmY;50<mAo<0o:T+HP%&:j;-(gMP*%hlFVS:%8!`Tm<RYLF#0MXJ/:bhHW.n=_@
%)H1.OKh_tugnI0^;d^+Xh9%$@7<%qP`pP7k0efr=LX_&HDXno(5;P?gUaCL!$'uLY/KTIV^H>-,P"'u->DmBC<eJDoi\<D/eMoj'
%`r/4#>kMu*8t4c73;f*WU4K=;?1!AKVr,@W%Wt`9?ZZ+]DGr4>K8&H@ne+aYd"@R2L#Oe->r#U&*3bj18h@LuFh18o!IKJO(_kTH
%)CI-JYlR-Lrn?PFnhb#i\=[gb#'JRFTE&`jp#-X-UYI3Q.N6tiS"IO!)FSArq!-2:]_X@p13HesL@'5@?8@Zqj`eF(4h7k$a-[8@
%b1r7#aC=]Zb]bZ4-G4c2mV%+`6E&s=nmCBj6c%:KEMl>>NiK9e2_]82&clTYd.dB[-i7K\`G)MoU&##`ms?MG^s!KMVG%(ijGTut
%!3Bem]T0gkUC""?rn?i$qcX%@opDF,i5!b8JY77FIXD`<RU$iQ?Itu@g;dT,L),M1+LP/$1M#\@$_`Bn;=CX*K3_[L3-t,Nbg0r=
%Q^m]!/GTX=,I-O^XWH`99iKrL-#Fp$OYjFQHra%I$.8L/`+%'l3Gc61N(EDI#ClM#o&>Ea$PXRC+-hk3(Q?Vt`=?TkoZGp,_]%m\
%&%qBX*6_9CArADid1Op7Ka^fE+Yan1",ni`G8<M,>&/t_l;)4`dfGLI^-/\09#n2"_-h.IaV6fH"g'dO?,c2G;HD/:@hf.hpj<3Z
%N]fm,f+GE.<o,`KRPQ(M?7Ei3e$on*'4=\TE=tN?^+t#_OV%@<m:n.;*`+4T&#r_7Q#&=@Z!DZ^aGQ>_@_D/0>WL@+pEd=LLQ!rp
%]gi;I<\Bnn*+9mQJ/^'8;['Bm6j$f1O[]'8hcGU.T9%.Y?cHVU&-MlhW/O:1r@am#)TXE.j8WTfRf)CK;t$u?q(ELYO8fKfnc-.]
%^8lt[NJ&ZsWm*dL1s',@<lPefKggE18.6nLLdilj3^>K)m1\9^'_U9Y#RCHMH&V<*iDJq_h#ScEUp6qt#=6$c-tlrkgG^.h&EPOZ
%];ag?]bVlh7&-MFbY\W4&JIJ,HW1ko$XaOs!8QEKU.u=Y%Yl;tZ)T=_][0X]&'f6%kpmG0.WorWgE'9fL&e,,@dTA\np19c]`1.t
%HfY7jbRQlLYO6(6ZmI)Xd8;rh>^M*9664\Z.nY'+P'soQ35>WP4'@;mOU8(I>T(9'Ib'<+^_tP?bLE0%n(ZX$bA)Hc5TlNmNnqi3
%N97!Wep''/J?V1]N#Vl/b"*f/j\Qnam,MaBdu`hZIK;W"J%s?qRYAB0Hd)!NQ)K,5:M3W=4spZnfZuZZkf:/6`um'C2<!_#-ur>@
%UY!B;(Qb"iV-?LO]2^]KqKr.qN*)/tk[(><6UW6.Lh(8*!QLk_\!fA#dT9LuC/]7k6l)u+;froZ-Kfe4jaeJIPB&'!e(lG4T,(0b
%&<5DrEsnmlhutP\fV0Se0i!MgYQ0MF2(G:[=*"c0XtbNQ(P)IQ2.3F,"'2FH"UhbtK:Ts=DTb<pe#d\)Vpe-4?#`'-LQ%!hSlqd*
%rTuoEYNL$+gV?Z*8[9%VOMN-gW*KEZ533@`&i>>\a;q-f,t'W/jG3N=>g(o?5D,MKhd=n!5g!FNV$'T^%cp_5gN,hS<i2qSV)W+h
%W;\\TD9,E-PNP<W)5i:ub%9u*/fB=!<OC"!CK%Q0+cX;[K.I3:?!\N7:ue+Zp9pah`kCRaVjWqQn5.BhB';;5&ZFRcfZW%JduB^K
%e3bY3rA.F8_\0\_J<'OD7?"J/G1QbG@Y2I7FR6WJ?UU/Q>?NU-pmU7PgQR.+<T0jY$lIeb"QE\'AM=12]X+n-aiOhcQmFs"[<)-'
%[kl00d:F$Af.=$W*k$HN/EAT/7&+Jk=IZM;:J3.RYDEeRbs1W--59k@ekQ_5E">/9(b#^Be,kk_=A=D*Ai$,SBa/q0Z??sU(U\0-
%E<*,Bg%p:G)"B2*l()0PXc,CB,g1gY/;4gd@ohS%IR3BuX9VVtF-Wld'Ss>sBk3.-<nYdj-2?OnP^P,Z%&8Z.p2oJoJ0%IO/<pB%
%!k]YXFA(-L*JjDi\tkiuAq`CT/:+I@K0"4g$]]nDI1]:BX<Sk&1ke1BW6N@L%I-5o:WY>bL7mH@kZGH=9TUi?>.fj)*nb.PS"PLY
%#fp(TYU8VLO4,de/P,/mnm@i6,\]6fn5LH_rHX*h_VSK'`e!H,M^>&g3$]81)Z:.5&@8PpJgg-acE:9V&p5":_::FEiH-!IpYRLq
%:2BbVBiiD'#j-'No'cKs!mA?7V8u^JS_B]I?bW^4JLa3>\G[ZEcN`(1T)W84Zu!E$_'utINrONm5#)8JNi*SMAOra"mer6q)EJO@
%\Pf@k]erbBiOR>;EMb,1akd"A)aq$a.rf0\!'3Sl5='R18dkqGG97U6A"t9-9+@%.nQc/.DloFj7'_)=CbdLj)&B>F$P<UMSH2CU
%+l*//j5C$+.[1:YVV_O:F(.\O"'Om&j'bk.*X@Hs=4J=`?`8:#L;".^f-U5\dS'Qk@$b1;(c5)l[;H6;&ul\4G,#nG9rd,L;t?a0
%&=UqT6;XSDFGTGo;VeD%Yo"jW$"65`oMto2Fg>^95W2`f\@lXS3`g/4UB9_+o6cS&_p4iR)(jEDo*0bC2>b90A`0%ln$@cIK[S7=
%qYZF(d!Vg2#,p0'BbhBdH;++g$@r_-M;mWSmLja,=as-Z]iJmSn[91[oZeb;`Gi9-4Xie[A^aj7-Zrp<NLB.EVIpn7;7tI3/m,41
%HCKn*B]65)Gse5J^tXqX@Df`!_,JGsR,]c,=sNAjTla[Q*9"Rl"``!?1*hfF(76ckCJeNLC[[Ju?6l*Nb`0fkdfrp4316cp??t1n
%@t%B$FS5)5>,7&uC^OX21Q9Jcn#k5mB/t'qWO@>%_B-q%ClV'rPW?F*+3/Y[Z,PVSJ\m2qS-!p<WK:(K1W;j\E":pPQ,4!Vg^'[i
%E>F%*d(XGR/guD[?WueYlZS]P]F<^n]HXGW!8ejf+X4]<rA?IjR(p451tLgk7\(7B,ZGI'*`A'IrW>Ft>L6g6qJrAE3]3Y0$F`gm
%gqu_jDl>5o*tfBP^BCt[^^"fjVPI'XpWWuIAR-@o'cf/U\GB_WoQmOVpdC_GY1a(7kjXA74GU>npr>p'c\3qC6^G<8]a<g$QGGd#
%nMDOl_g)p6OiA8&)QUSYE-MJN!V$u72[GkF]/cRFUn0&M\2_9)U?cSW!D+5EDl:U`'C;7/8jk=CX,YdmAA*j$:bc_D4Z3Ac.tMlq
%e)H*Sc=,nF-.ukPHg[rC=kO`sh=t]-nUq:Y`:)?D0f_nV,T`ZBf@b_9W8q@NAu3(;r9dsnUb(Q?TN-j*H:j,op3f<=<mC:P'T_<]
%,qG6D..eh,dC]s#1r9Z5#],<k-6NRBYpkWJ<Dom8N`%,(efgn7IuKrlh!m2ecS'_S]?cpDfbc;F+57K"B%i!EH)#C/I>r__-Tg)9
%W_!I)Ik%sM@3`f'hq[Jrc>$qhcVrg'#k'uE`TXq(c>VK7a;PAF:U[\;J(V$J7NDIfe>$5,WG8;hKR*0b&`!\\&"(5Z@u3b\g-Yr#
%2"MBt8/2UTJ]KDB+H$%Po5rk5Gme9u-'[WVLqF<hleoW/)Q0&5J'M_:W]Z6KafsRV!BFX54V(PDRr+Xf2Y=eg)n[Wh^e-'DNFuoO
%URH"[RTc/>Alt_WH_,"s6jP]pCZlAK/N$FB!7*>FE]C)+#!IR7X49]pj]2u%<GWqKO@LG\!lY2K6[P2]8\3-IX@H)k&rk@Y2FYkW
%iuF1dK3eFpjSla.^E*=N_tOQ("D(;OJ^hcB_+n[Nm!t8u'Q_/'Kl=V>4P%5,>rO)m5&V*-<mGR/)$%u:@rAEU4GN`I0`qmoOl*b1
%S_<824,outR]unqdI;:*JJ"\G,;gjslR!>b=//aV\SfO)bOo[cCL$n*9>G^qSaIiM8FEGEIpWnB89B7!0!f4OqKYTfr@Y>%go`b/
%CgCoAY(W3eGn4q[QK$oIn337/6NlE_"UC1BmOO6"01<Qn1H3^1I!UaS(^6\?M4@(.+5p#nnsoO$'1\&KH-Ap3CE%?@PI&*m+cqH<
%+NM((_aS*Lq^C&gojA-Z5\frQLMPXrQY\IQ&fuu;;6(F*O&`K:N9VV#=@t,Y"Ke=L4.ShXOeIUR4!E.-L%d7@NIVj'Dm@[UJBU/G
%;liSE<K15MnH85VqjWjA2$.qpC$+G0UK?K$>g2uO[9VI/T8-sr@qdmuN*Y!8^[:c(G)ccjVEN[QD@n-)URa4YkIUY(^JPN<rE/!c
%"1>+%#i*fKlK70+Q'(?5qQ[%-%Dg`g%hHZ[M?U('?!L>f2<NTRb&*2X;C#-Jfbp)9@:XKX2'1^:I_Bcf59"-=eK,!UG;W@kL%k;u
%U?DJ4hQ>#/>GYB,@j?P;2C^Qaj)`Yg#XG2&gZsu4ns'4![Npqciksn(,\K2&-3I=t2H2p>fl3Bu:F)EU[78)"_(T0HnUb4;]!Zu3
%>/fd5cOQ#R'('=ia+-YB`CRq^]ZtFknTgue\sG&\3:E(t%fs]$@TO-g^]E/i=$Dl,dSa/bh0(YO5MooCcl91J%7*A[NNR6a<'-S$
%^UpQ-[t15\rp*Ok.-PV1dY:%g,oO@(?fZ9ZSW8D"+u]*7(8N7`%4[Ior(BfQf,2=JE/2X^,CZ`6DNn0QbFQ?60_Wl*Pau\0br's]
%inq@kV?;uTU,:I#naCsQE.77Y*cT_e[?m>uX+h:9bT1qieJS8[H=-5t=G9Z4#%)X2NJ\kX`qO('7+?$Tel!Hcb$eD<MHpcc3ZQ[#
%O9!s2TruK(OLX`jaJ<2Yfg9l=*Higph?SL$Ru&u]);JS4P]9K?Dsk\@/cq[g7SS(3`ojWUd[D+.(I:KJRa,q-!<@2A6NPI.]:*bq
%:"1jtVm+hG?R.@%-;Rs?T^bSg4HrB-s!IX,WeLrT_+DWD1'9t?"i?LSr>Gc?k8FY);P1`_H?/_@AX6+WdXE'L,X#RS<AJ2%JE>m:
%;#Z3e>+QbS@N4n@*[#1_!;H3g_OiO/69E82Lk5LAg^2:iOl,=)3umlnPeFnEUkW^\ktNZ=s*HHd^t63_7H-!K/IQ6pRZd&WH&oJI
%O`Kl1Xq?)=`9i"g).Sg%HYfl311j6iaG9P@&W&Vq-HIZr52LF"Z,T.nPh6\>)W2tsGk.4L=\rOsjW,KX29:)*[%sLS(-b&hCHIE5
%4Z1/BONt4tn79j!A^?2,J<I[ic*QD8IHHEhWY1:3ddMULn<(1?c#f?!)c&at#9M28:\_pinsqka[9'?eE#k)B>:1Z;@CFdhMWPZ@
%T[u:MBBtpN*G@8(NBFbc'egJgS/>p:d2oLPH]uN\%47m.TXdhH!E51d,<R+oh&`V#C>2!%6EI`ERWMq4Tbt)e<%+)g,?^p[QR%5u
%&u_c8K>^PYJbpBUZa*WZ%=<Vm"Af[#d$O:,QO'ZNFN$@-YjSZ"hg]S0(fJg4rso!i.\`COXuhTZHD:03eGu\0cc?sT_:r,cZo%tC
%42s>O=A-:JA0gQ7c_(dLqjs4!mAbDf:L8\tH=9\nok<"/1JF4.EED4,X,5?*eobo,BI1=6#'bQ'jC28:k*Rr?8]VdWkm&>>KRR`p
%'sq,#N.n%pGVogiAa4J2Tp0`0DeOUPSAtA='(Vj'D9\kJ>3[q332\nYhtbD?Dq-f32eG$_MiJVWA33*QbV,Ign;u-DER#XqB#_*G
%8B<3SOpN>d%)umA)Dr#09IU\;fic,s[j7_Q8$E>^;l78*#Op8]r](QC<hPNGd0n(Z@oou3<-k)k*L#nFM]7i[C$kX3_-Nij>+\Ji
%kDh7*@`=09R,i]Wh0TNui9TA4N(&gg8Ue,+O+Vil)[h"od[mQ+#fpc5K]0p2QJ1f7#JVC>nGMY2U%]e)n^aK7js9kc=s#`]l=ft=
%!l;X4q>:GEQV\Dqrt:&1pauh*8:kg5X^Lh&*ff0)H$l9g-!pmT7H;o63t]n.#^r&4=X-J.*(a/W)R,>Y*s1,^kC.i9Xd3b98(<&u
%/4Ic7L=mgMX=p:q-$\p@!^"6%,cjl@/qqe.,iZ*2,,4rlJq?o!!/DP+08!eSDB85o!Eo0oY`fHWp0lT:WElZVD!E7`K=BT9C[uNf
%dZjVXfSK<Vl:U3r"i7ZGWt:\VCf&TS.81gl(<hj.T$39qUll<`$)2l.bis9gRG[u^cbKj7lnd)%Gi7a@1`;eP9[k5e*e548*[$hS
%pt#p?KWAFE1E7TDQI0N`j?\]O"f@5Q2ggI2>>TF!ZiS0Gk.>(3d,_??-GYs]bP(&[$tKn%G62:$@,Y<NC"WW'p';fo^fa0O[_FPA
%Kc-$?U-;`5Z0n(EN,QkM!Ac=`,@GJgR-Rc`&Pc`>CQa"H+s7O8]ZXPVAJ50jo5^Nt?6hfYqA_#r`o%(/'+"H*.b5*bnC@k2^PRf*
%O.#=TE.204mLdl5Ft<tN,&DUGYj7IMD5`HqNlW+@=2D[sEo\SO:elindWeqY!@YM4_e>Tp?QVGgGs#'YS:@&giU$O`G4&t_`PVKF
%,ABOl?Kr#$m)Y6t&>QEt&3f]6G=5m[7YNI6A^oZ_j6^I,,?\Z>@$q=o)TIWR#Jmi?)EC4"JZ%=]S4Kp]A%VnL-q.*@&7g=Np[709
%ChY-u+T96FClrfD,%1WD7`5N##iT9O/3;Ft6\(_$f/7954.S0C:RnTJL+GX5`Z.oLdE(oQKSCjM?.uoY)$uB#.I=^m8Bs2ppfp?Q
%ZHUt2O!S4iUO=%XMU\ZpTe6=,WR59&gE_ak:m)D.!P-chOi\3b!CoaYo9]LX:GrKmo!$o?460qb8[Jje-.c_C0uEe%m(I1u*QN:S
%31h4bs)<@(G_3S<J=0R9ZpPKmJeH=D;W87P/W]-m1`J$PbsjXdd@`mUR,:*8f@i;BX3L_P5!T-,D9Ke!?'U\9SZhuo45-MIq?I1f
%PlM?@oA1"/4H?/BO2gj!OKJX)Q]Y62M^D4)fL)oo.m^PkeB.oPMHQg+3lQcO*^P51D+g[07,ej2[5oL[R`*3sA&r6nQ"tF7J17g_
%3^'0)Wf>hZBmno]2((pMBM=`El%D*?!cA\u=WQ'R[Vu^[b$(2KbbleH+=!68;X*;%frZmNQnGfS-s)VNbS4"6_T^PWTb_C.,[4*N
%KJe??lBe>GX`"9$&u$=L<)+aF0UT^.Fu!XiYf>gZ`4Xr!d,U<(8ma2'B"_fq:F.,2[c2_[Qrh"`:4TNr2us\R+YTs.F'6K9;No:L
%K@\+4ZGpXdU:QQiKU1mE8F?&N]-(:_1Y5%7<&43+cc/AM`u%s;*;N'WmB/``^OR','=p1[P=fL'G&C..J=d<s?B?UjUam^.6^!eG
%FQ/WU@T,,fNeK5YVI?;;#II='E[ala3"VUZ"X(os,u1U^J9V3G*)ot&=\i<ROFk+9mprb?M18#r5.+)A4eZB7r,&[N2Y@1:%QOs'
%/i^lZY5EFm/:O>DW/L3!@/SqF":FgC.mT<ELpg,ON8VF@b\L`dR^R$5FJ7"me`41t!N?'T,932CDBUJH&QM-$RYM67)<]S1lY($B
%\<=59R0k"[()OFg;4r7N(P,G=N(['-K>K"(%8!9$F0U;;bV^bW[nJ#\N30)[-hA$q1M6P^*[K^gEU"EA@a@1>'/I7F]LFeH^f2I%
%:/HsM9GVCPDV/&PLQL]-!m--37VS@=#KS^\/KL%$aXj`YFD2MS)Xj:h84ZJq396e<cpqlQENh9GMe4rr&tD!k$Vk^u9Qc_!!EpZk
%Yan:4&f0L4[U+KgT"$-ePh6"%U5jAiEiKMg/;6pD3g3(MEKUnRg0?3FRBWq)H,H@g/sN2H'TdT)<SUd[7>@'?%)!7tlK';:nGG;)
%hR?[1KoRRaDn_0Ar4A8C.YEaL[4f[WK8B->L*u-G`dO/bQk/pK2DT%H?8<`^HOpDqmb0j,S1<s@adDOk-<M$B6=g^5mZ[QADUbL=
%5_FZ=$r_2fF2k&t<4r:2\HUXO"bs!t.tRPFi+XoX"&/kMTVZ[5QoP:ORGPF,-9/tKZRh-ikFOGs=[)o;:-gI2q*n`PX"uq(?:"[-
%LoZU`'T4W81;SgrU_`:gkiJ**R\4!roLp25[77o:K.#%1i,m*IcV2RdBq4+FMfh<)FGZS,.gQB,1q@9D__`.LEqFZ"E-V.)`CS!_
%f@5A]k\5]idY+>f1$T'BH'I(pUIrp1#NID^UE2N=6=V+&T3_B*0S0A'V4ThbdjMuu36pU$U7@ujK9;pX>-u#C/=$bcVEfY;Id\Rn
%V]jHJFr=[iT`n[j\u=[\FjYq*-d)\Z?uDO^?>@9cAHA(2?U/X0AgQq0ml-FAMrCZ-LR8!Z*2'bj81aQbTu1=Emnr;VZ,qtHAU7cV
%edS<%O`MX:,peaEc@-\)=LDmYh8/J*'Q'@AEnGgW"6fU">r8oUqfh:p!1L6#;cK`\jlgQ3dZcnRVc?9[VXY3O"FV,WVC&2f:.<%T
%cCZ>rYIXCiW!.<,Q;f!$<ISaDl0D7/iUe0jmOhRhen_\qdGL_rjKCbk3D:oJHj]PGpR$u`P8[X^/=[048tlJ8Nq#bT*QZOI4Ao2I
%-%[$4$`S;a*W`fOLf(t\XK3;^H@=EmT7FX(:D1`t'm0/O-1l9[^_\M0;>[_"=jl@BVW4:Vff:WC08h\I.Oeq?N9:9n+S_toX,FHI
%@!YOtc$C3)`8SgH9(8Q`B(D6`p+Y#m!/i1l&[aI+JJt("*nUJA`),C,a!hHDZ.FasM6.sgAf_Z@=%oER>dOXpj&OHYD'ek:?@pkq
%i9Vup\6.eT#"Ghb5UoXKZ';uj4(2iL5m6dO8Ep0b3s9gB"?4dre+:>);"PDphF`U,/!n1J'V+/SFX7*dUa7.8f>+KFC4i]_n:dM+
%I@eXF86B,oF?&9]N-IuM1W;JQJmrUp_-mfu#]0RWWbbsM?ZB)(Xk?KnQhA31%<!J0PSKj-)hO1t(0PFbnHcX+!`%+!??KNE.`cV"
%B@UI+.l:"b/!XkNbED?JNl43UIV8BH^=On`CG<+;"ubf)BnRrF)SdXHM*9X=$5]2'2a^N#Mj?&Z\s9:SC,5E1=r!Dk&43,%<JhD/
%:n7)IPU3qQ-iOF@E=>mgXJ"`j7g(]VC/9#4oW\D2mTZA$itF%>Op,+'nK/^1^bXh_9(eTL!PfK.6)=\ll;7>M(I4iMEOEq]GH[HB
%J7S/3gs\@cL,n:jj@tS!?e:9+nl^=0!!Q;I,69gHV+>N.GC`=sM[\tggko@i[46&A+(9#:F-(ok2q'."r'R?E:@&'P%#$u&W#.9<
%TkDlB[t:u.$](am?@W[29A]=%HJ8MCi#B!EB]$&')RD?qY13o^i%`rl?j'D)ms%?tqmp\hb<h[:U7l)AG'"BL;fR\AbtAQ,%@Z2(
%/+O`T07i:'UX\^TX9Lo-!o9g/MR(VB_$qmK-&j@g^cG9sX&n9B"l.k?7398k=dZm.0FJ&p5lcf<V]W=7[A@#LC@ZJSWZ2801*-bi
%XU;RXS=?K+^j%:&@Kt1i=_$OOTVeI%DUQ]$/@A,-p2Fa$6Kpo,@ZlV'G$3&Q[_VZo^8Yh@Lh#KQE:OkH,bne#6)Bp`>FL)FemZ!k
%OR*F/gam"lD5G2#@Wj"+YIT%=s7LtO*Y?;K:?8\='g3;4AM6NW"RI'uI)'!)*\ghe60R(e\5u/RSAp(5Jp+#XMat'^od2+"qMSRT
%&pQKY4S9@\L#ec`-6'UK9\LY_`&sPo!UF,s#pECW@"hJ)&!5SE!a<5Qn7qX@P+(@Pn;s`g)ZL*OL"6Yo&UP+8li?#\rh0!<@ZCU&
%J`J#OfV'`SHCq3e4<.4p)dCW;MkFsZ<i(-N*B>FJm1LrP7FJUZElDuGPA*pFo+6ip2o-uF)+bq\SZ6-R=[3bSo-$0<"F"iHL!:h8
%g/"/L`-da48A[V5GMJBJ7"!8m,9.V#Z@]]`<dN/NK9_`FEPlC/(47:;YJ_hj"sQT5Y!A/[6J>n#,b5N`)14$qSB!/^16`d/lKn^^
%8N-q?JOr$?#%`^*N&k?Y<Wr]*2?B#]95F)>2F3/>cL$$*l;F.d[C()7!,Somel_^UNtP!N=]KB^AN1krfoqXpC"cImi0Mid+>k9N
%=6_>F![AojLW@j_^D^=5J;[;P+>0$D/9U)`JN4R#.=s'!O:72R.Lf(X`dpN+OFUKh^Np23]n5GrRuB$U1aR1)">u&S$XgYdRIpiL
%_@gfB&Hde)62F"]eA`\inGIt+QGX:[iqtI5pB1q`-$:m!=k-5@@+A88os6AnD6_/>jBA6rK?.k;qQ6uFM)jZ*%dG\)\g[en@P#I=
%>eFMY(7=d*gr\31:bCtQ1.aP1jG_!cTO,Ti,qB6cWUjErfm/rm0(k!dC`oSp_%7#kFXeOn_r]g*AN6I2V/l^iD?2>eOkP6:hV=K$
%9`1H?n4IZj`sJnh*+1VJ=tH'jUs"s]%eL)=Cj6R-JV$B4M'2.2"tIZ&:U8.521O=)r,=1pjTP;2mmoe$#@.M$n;P::+FUFp_D`I?
%#a-ST/9&ZW$BV\:ZIAJsBQ4NuF[EVeoiW+X8[2?]IP@-"YCNl6>U#.R/C/qFKjS'*hbT7p3h_>]h$rFDZ.Au7BuYmq)5&XS,th.!
%4cS_,#c]QMK!SYknXrl)*s%d"N&m%9@=m&b,9JNT'UQf6&*oCd$)DSJd*u(7?um]@TfF:#_j\ss.d7muQ=6Ut3_\+_)AOa@p2+)S
%ALS2U^M%_KV"=`[?]BpXAHR#f"W;=GJnd>i^dqK2Ql4A^c`3ZV?o:^-dX0!_q*F,Fod=OqW_nOhjmt@%E$3c+X+n.TLuIF"iCIen
%ghY,u4M2ANJ"g,^,9"\:S<ab/"V2^O?S0bAhEtO%a9Vbi#*3/0SQr'?C_3*cgrOq4IQh:e01Ldd)Ekp2f2!Q5nD"@YC'N[74:D6%
%ON[tlYduTV&CbpG,89C?^<q<J4YG?EG!diEC9\r:VflQ#<e)r7^^7)E5:1LSe$KOs+1[d>msQ6u>5GSM^]^f6)9M12mY;Bl=p9n6
%-Wk3;9B[[<A`5M5mZ<uR1PM>aXs]8lc=9QP[gQ5'Z/6ih/6>`&$\a9!^2&@J'A"C9c%sHo!J5r?lkj,-/S#DIEf:jVV:[3d]`>HF
%0u'6\ADjr$kK>850PdS:$i*$j$r(N'`TMqjR90Kh]*,YG$jH^"@o1lK$OI)"WnnR8HbF11:AbAq;)XL^/p9Bl`B$[A4R3\u`SOl9
%_oXm^U!PDn*jHC=o=@Sb>:eb:&Xh1ZR[cd-\fRi-/ocee-3?X,RlJT;%^=FI$`aE:&Kn%$46)s[3$\MT&#p+EAW^Aur=q;YTE2!O
%)VO%XB=l7+:ON`V_qs@J,R[oBS%mmWRZW;LTa9T8UuAIC&>>cnZXWlLSU_n%243<YQOX$$AOd*AK=MlP.59aLj5F7Lp'0]^<0AS!
%2l^At<7Z*ITZ9FJR\`L=-)(#\<PccCWhALq9g`ua:?0/-hk[cZi=I[WXO6O>D5Dm87^C<^cnIWlqdLkQ7n/F)ofU#0bL1/i4![iK
%W_gWn9B<F"'MDsC_SHCgegLQehmQ*3KLJ9',L[t?K^71k.tJi]+Yr7XYXJ19_%7L&k?9$AJa``.Ns5$Kn=l?Z.2-NP!&_J0q(WK@
%"0h*;"F%N^iCLO8SBOpIQ"Ei*g;>QU[4$s@QbN(l]koXjS5ck+R-a5i?gXR6J6@Q=gEM6&Ji#:Xc<5B4f:E9ECk?Sa@-U/c&E02D
%o%5D/PKaZsY1V@3j%q:tq"JHP?ZV&58CRso#g(Cj1l;bsW2Y;;?KMBDQ5q+HC]mF>('\H=6&?,+<IlRk&I9;7p8C7\=$D)JDm9q!
%l4C`X?=mI;&W>B8opiQL$-P4n_8L-;GMYLiAu5E];]P0K!US%a-<3>=`bZ`6dGW8U^lk/G80C//5nsAU8PB0%,=k'&Nq<rlNad%]
%b&reR+W.P(]iZ?_Y-V]-l[XLBTdKHTW@#Po#fI>tIRf\KklnSV$&J$;h8nCf39.3iNs4;^&UkEPN3EBtP]*,eJUhYaE9Bq,Wn2R9
%gA:#@Ka1`(njklVIDpkg2$q@@2JZtdA-p(-kT/CJ+E\uDQOTiRi2J$rHR[(j(>3*j?5c3Q)&Z#tZXH,;%9M_*e,UB7+ZH*`N$/kI
%$=9-R'JtE[,oXJ+%Dfft_d\&&+OX!f?)b2k-JrUQR]K"*OGdm:X&gOYGF8rC^jVkU6&85le5<\FJi`:pKYDk+f(eM]:faNH4tsX0
%%k:kN7(:%X(a)7J5iqBq'56\I5,U<$I0hm4&I^LG9$"'>YfKhmNAO>75GkO,a>@5+Qu:r)HHS-r3:K!BM^)S_7)q?d35#.uoEp0V
%c3gco^qOf+,sX[gS-pRW`>YVA6NFt<%A8B*NtSZr#qR*FeXP]Y[]Og1JY@-?6Uiq+)L?VtqD!Y-p>/5?1\!d^M@>rs#4c`k`;!]H
%a,s?5%t*aB!,[Qb%Q**["`O;_@o9LqlVmjI[a=?_S`\?G2M(]Ya=_,9%-5i2IDE/qYE-A<P,8\bW7%1]@(*mnH%<"iDsh='J1mVV
%[6L0Up504<IU9_M$\^*'&dVMBYY;+<Vq2Vj?u&<uRd&@0PG#L<8i0RL?AG,Y*B:EJ>-_%d#ShJZ:nnVaT>M!9o2@;mUB8)k;aWg)
%XnA3J"<CWHjLUX/C);,"$s.qqTMYdk8PVkhX<7Lij?_\087o,/,P3T<)<o?odTZc$.7%j!&$.#+:*::S9N27S<GS&[V_Jm?/YR9i
%8g-e,Nrm`FC)&d$PCOQ>K!BFFH@0geUCXEboU[S(nq7,&Mqq"ClRq'&4:II@!Eh3")m5n-R2-:nkjr1?PBE"XTFS6Dgcra1WdEJm
%hT0.?%_+k<>]cfDIrbe:U*/kY]`b9s8NMrZ!KjFi=OF/Q`AG(0J3"jb.9sW1=pH>0dUpI54:AP1?6HD(TFCM(Ps?g4i]d'kXanbY
%2b49CE!2cRiQu:eQe6qK,,0P6ZDI5UV<X<h@BdHH01L8])&*7t;5kA)do<P'b34@T1q`mbN$FYn]orFcUL%W-2iO0"+:7&Cfh4k^
%DTJP!Yn@UM75.gEE5YRMMKSR.:;gI_T4="$+&,*FbL9t=Bt_Bs^X6UpP+YgYM@,VoO%=RH&k90!0psABOb/WrK@*u?(lIF$<B^lN
%!\BZ1%QJiY"E2H(,AF'G(=;7F_ue">RAhAmmfdqU'a,nL-.:H:Q4Ph)!Ym7cOsj8W\[;I3p&^#FPWf*t9jjFW9tC=5RWuMg2OCB9
%:t@NZHr.D>cR*]V_[nfV+&,-HZ65F6?%Wg%JO.<a$2YEQ#"%9ihFeEC!6&7/T/1=aNP,['.<s)r"!M,\O"[1(=BF3g^@]:rY%1WI
%rD"LME)FagEFMi>Hr'bOJ??GB$]Yl-(seI49GlcWV+a-\8:W_kV$D`up&]2<-a)\?5Z8*F7\LQ0>T#_jWg5>^H/_IP4BW!O+!T?q
%XX%Z^BYc-2EA)UN1>'M1Bgu;2q?hBkF)cch^#qOp7QW*(C,Z;aqH%iC/k1[n%2O9FLi`nUbm&d>#8oD6P\JW`-qOs\Ybs$k[-S:X
%^mn2bYRuWA>eGd]Nb]CCPBt](K*j8VD`GWJ,u:*+l6)5[@U+;?G'<tFWG"Q;LGXIq2)2Ek5PNBcl;HW'CL05j9D@6YMEY,cOo3W`
%Z:&B'U#TR5eRA\mVFV&c_,e7&_H\2"3(ss-!LjI_5#ZmTL$H7T%[-4l[imLi'B.HY->TD4;IHn!!^kl[5UJSokd%)CKPfl,C8U`l
%+RB)dE>"Rrj@MX`^DX'qS`'S2*dEN@mC9oa^f72b^!)msLU@7&+P]KJ>HN!Z`LnAW^o;&LZQbg7"!g7=fII=gTj@_/j,dJEc_Id.
%+DE[PK"3i0Ul3'"UrOb]c\>@s%1r=L.=/hqY`mPfIHChX+VVTbb%+_HGr]!P=5XeH/6r^+X;?:6"26Re,SFqMkc=qh<cL]#9.kVt
%>/[FKLs^G`UP$aJUesmXRR2g"4[7PcjddNY3rkbAZ*O_A\j?k<cmnTsX4@EY)VlD1jEK*('3)9Z0^&<WDZ4#ZgcVH9ccq>Cko>t*
%eFf2C;9RHMi<i%Y5(\rH#T1d,$sVl)og3's/bjc@D6KdkFilNudoXDac('FiLMg:dm20N$",Z_.FY+D,)9S8p8'H0`EP]J2_`lCO
%"*:)YP39D9/&="fMWBPQSPLGV%:;V3DI31G3/T;F\_7JA%$A7ZiBT$r&KJ_&FY\3/8Y5$ea!5u_/8bSrb1;WF%QfRFr\0PN6-S'`
%2fmt9TB0DI2q@4p!QC3Ws$N&\bI8sh&X_^&V:Nj\hD2n2(1K3I@35/Rn<-e^1(Hao8f+DK[klHYBABcE1ZN"+`R*l79,fdVe.h4b
%rf,M9:>F?FhR#[G,YDo_(kVtiL2;bY\b(S9Q>;Nu'N:'fOGF.3Tmad5EfUN3Vud[m*e(kHB1X80aWpC_[PU7D9.3JgjLc*X<Yp`(
%)9s*E/SQ43X+cuGd1NLY)$o)5mQ74gD=i/YgJ6).lV'ehHr/F[;OYQRiShoZnV')@nh%*l<hj&Z4`@gZJMA?/d5!>IAL&e3ip`n#
%<[;oSAPoj=(`<0p5g*K>XMR,'9Q<O%i0P4dF,J.5Ee38^QJcG'!2"tBrg$5.j?Y:_?*.i:KFa*'BM%D?4-RC3-8K=3E6Y((:+p"%
%(XUWP"<^'s`7;Yk!tB2rlP2d8;if"o=*4PW7_#RCi.Vr>d<Z/$3MMYUY?3"NEr2Q[Zl$;dbJ\6XpF9:J-K(W?\h^oP;F-IE0@BXE
%6-go$HBl1H60?$L+j./U6nVIceH",0a4!I%WIRX'\ihdn5WHQuhQ/#69/A0S%?JCt`*SSf3HsJ2&N%Tu9L4<Z6q%i<:DssTaa#K/
%X!+5^W[-#e:a;_l36cdC0eZl\%tH(k["O.flT-g']8n8f&iBVG1sgZ^\Af6)@&8`a@?PV'`$b+hYeB>#,rlORE&R$m,%4*<=\9rk
%f`o.U4u/:!&$%.X\p@<tGjsWVNlZ?;C`OA*aKq+@#(rYmIQG9bSrQG8o?Ii[TLIgKW*<^B2I"F.d`cno6sc+#l-5A,Fs10KbImVp
%/@]q[:>R97.X^/[,B7Venh8Bj6l3F.b)X7[BE;W*g*tb][&<u:j,WU81IW@,6C&\0*7Z:Ed<#KN#mBKLHK'(HpQDqpQ0FtF9Sj^?
%OS=@t'HhZ+_a7)H\Wr8CV@RGib(la/Mp4%!olq#GQ;bWcPdhi/!8Cj*&]B1Z:--?8Pn>s/'kek+_Y"-0B11E'S"4MkAF'N&FQPiF
%6710TJ@T$`Wd0L8Vcj((4q6]dJHoZ+m"Ba:EYN6I_9bp/AH.j0AX_FVen;p7[_Sh._1^Z5S\D"M)=%A7&kJ#&?ppMK6'rhIFl&*'
%^C9^WaeFh:>*fTT$7X+jZSX:N;/&)CB18rK[7XnTJle,6i;b&f#EgGI].1-:!d!#=O91mpZkA<p)-U;_\>KK]Ucb!FSMgi@a]r[:
%-$m.EYcd>/@;-,@C'W5*MT_]I;%Z,4:B0_c/qaA@Eo=+t^$&/eE[SmKe9>nk'%3&)*+$K/ZLDZRG=CR%"uqd`n`[$\*KJMc5,q<n
%<#upsEUOo>ZV^lc_PnY7Fc-nqa'u2+1%8UOS#FDi4\l+]CU;)c#?jj=PlY=M"V'iI'!k-OIIFhrU^2*`)X]B;SEE1FcH'][4-IEK
%2AItO#tP:E!HHNL?*J#QF,Yd??K?>73b/9M0?+VEP3`L2e8X\tiA@%"<#BG;"DPO_d.UD9Vg!`t=n&eg+:A!3VNcE*7.i*tcH2[4
%Oc]5hLl5L>Ls3GCbX*p<8[Gf!me+It@IZ0CE@c/2Q'"a#hh`:k[n4^N@qiK,Z)/XO&i;KTSJH3;R(i]g?@qO8AGZHD%m?#,1Adj!
%@Tf'bj-9%TbL7mPeZ:(a7DA>>a="H9VLn"roZU<l%BEDG=&6[9B6Ig(nYDPK8@?WdNW`QAUSj&L_SR;&,^9sQ.9PtH!!am'4;<>o
%ch@]k=Y!$6QP53EmNKaor(sXtRElPP,`iY>n8bQm@-K+uW(f/HVjQMsK+^gDa"-$a>`LTjJkGk^#'*;BfFp`sTsQ-%8V1^Bj:]n2
%o!7>MjD%!@2N+SMPEC@%7Le-9OBFT!M'-23Ag`X+ZBn&N<h0s210+ga)tu7&'u1SA]L5XUC&kJhCEf%FcMdm65=%&nD">^_e$2IN
%?tbkGR_+P).=jAuH`<nJc-cOe;]OcU:NJQ_)W00?#krXgdQZW?-q^af84oL"gE[FBLl)P(@n?7p/+j5bC.OJYMn'kHfjtml3i^<I
%?]8]+<Y>U"M$cD/KRC&si,\[NYpqOi.<a[X<MJRo-p9(3nn;%/UJ'fWd$.GL2a\X/7+K,d=I2ddmdDr;Hqim4""_Hbfnc%c"n/q?
%I(>*)TEU-Y\L$7s[Z)2%h^cF6a;E%1Xq/AWVK%C>2Lm]`&/Xr`#M(`/1ku$optG4Q:7Yu@#p*4+)D.^m2;a'%?SlaLZ4qXt6E&#o
%QD.BB0Ga`t<Tn*9aS`FKiT!APga7#@V()F-]hFP0BOQ(9'jRf#n0h(Z'MB"<DR>AKc1q:/V/ANu@LAPM_W0^-6lkEg$=']3P$h@S
%#V3ti]R$s3!_>k#q(6mnC+56L@m[l,<?MO]E35ADl;d'=#:m<R)PNkM_Hmj3+b['F1%1KP$UX9LQTf]lI-k(#MNQ:!"G%Uld)1_O
%;"Z(V:!1J(0*qksTIT5%bg40R">EeL#GH5Db[B=uPopO7MjB;(LUL6IR7XtA9nY3lX:./T,t*bHW#i:15Tb">923)5Tj*[TZue4m
%EJ>p;a#/^/=0acFNAo3?PpQ\jLKP6@X0Qpr#WukhUa%Qb:+[]='WSD*ab^\#R1s0S+rj-Wi";("+_RA2I]7aq-VJg8MC'(U,cd4;
%4H&3`\K&9Ep^R2)L<0h:*aPpk?'=VTs(l+Z/E+NFOo9?eMDWlk&2V`.*3#O?#^J$hU6ffOej`^uZnUVse*Z4W2(r\,-tao+`]MGl
%d)\>%+`ko[BdmZW+q[8L*"U+7a0^$7f(Y%MqHRaUeG"TLZ&>e96CV$&.8pWFd:kc^@F\E1hLuP!/-3Js`5ReWEU-KJIr%9AE@DYR
%T6fG<nm/=+@NS(FG/<4U.3V_UonR]/LW:d9%MULj#fX=hi+^u3_!0&W/+PRopS>gR69!Gbqki(g^Dq1#h0\\Un8bm59DVoK2aPY/
%D4ch^0hFQLd6b.F6?-`p_.A]k$eD^#b]LYOZ#Q<o?a^KBEfjmsA4[Ki0rXFm\ci\N-&6af&@Da@pj`blN5Z?r`Z,3r_8tK9nDl`_
%!=gj0>s@4X!Z^qGQ2JY?/ODJ,<e%C@-ikSpP+#LZe>sBMXu1UMeXj$:Z;)Ud6nUq;EF^RTBs/hB]sm]535j_VLT]#b<"k(1L(bEB
%n$HEra?<Q6/reGJ9U1!l>0guc*:@qMaHH60=pA4c\6.ml=lfgg`EFCMn4*#)n8-+;?'BoG@O4`J7$-1a"IGns:*LGj<^O#VlXjp7
%ZoiQX/:*2L,Irh!)s?R:$A/"a_X-^VUP1]Z!/84+.%hM%7T'S-(h.sjla]F&'>hKZIG.KXk#AXo+MkGrS`gj4K*2:gG,$'X?jLD[
%8G#mR$5PB-8r#'Kbf6lSaXO1HULS9MP<Q?k=h8\h]!=N(a[a4)Usu[lf\%-X*5<;7oS"\qF%\:t;$n!VhbUs(Q@3W(\d[>6BbHSn
%Z]]@0Q7rU_*f'3<lk-T>8rd7R#NR8Q1pM@lXG6kF:a<N5S.Jq%70DeW0HoD#:2\1h;S`_(F]O,>C!>jYZPl,b*uM0HN?g[=F!m@8
%XM0M7B,s7b_ni_75Bk8IP1s`t=Hj_fH?`.bM[%YU/uCi9FnOTgk00o1hKuc9,V"`ud\O*s1G\oC_MAnX<C,V>Wb/1pAiiTe'/tJN
%4h!0ERbA81k2*!Xj$\]=G#SfqmG.)c3RYi\SW3#`ED5RA?o+i/[dlU6,*h4e2d`Zif`$t"6r*eQ43E0lhi'U==&q3Wj1\cDk+.dE
%?VI;^O[r"fRAf%2B-i9p'a?lckYm84$V8peA!lUUi:3([\@/JLj.)8baZ[n>Ht'@fa)U(C2.oCN10\[-VQQHV#$$mqW0`U6s1(PZ
%.-C%%.]\H&8%PI-)A[9.Kg4ERX(^)D`)5)H34P^tM8lhNJ4Th[.#`KRFGhV8gtcaB>>[*>Bj/5=nh@USGU4*ba%3T]GCn1#WPB_m
%oQbikU66&]NtOq`3<&FP&pcKG70El0^6VAt1MK44Fd/!bM,?0DlN8J#Q@*eN(e?;8oO?/>#FnASV9=.rPi5'T2[7<LX29/lofA7.
%EC>2Jj';tW@nmq3aT_Kibpm+hQ\(rRR6qlK:PWu#;n4JTPCqUe-V)Dn,/RT3DDlaK/Je7"qYm3',RDOCFp^qOXPI$<<]Q..!\C=d
%RF[Uqb<!]'@Mn;XR.\,bS"6VabUY+<Q8H%fde)<hb8+`4kVLpJE%;FQ2K7N(i+e[e^5O/b;fg"=FDBYa'eamq7<3-`j,$[E/9si^
%K.qH=aB!H5PbT.6F$_+scjj3?&Dnr[nj]U"Se/1AASY<:IT^[GU9qCqRoYhqSG"4JC$Zmu,/5u,e8/s'%Vd7e]-a,EW1&Hb#Y]t^
%?SE5gH<*Z!QZ[U_>et)#+do'e+YR<_,U9-rB,f\A0IfA$/bB;oTCh+rUH(,<M4u_Z-P,PK;iEC3\!]o=oRQHsNOO"!/Jcrt</B0D
%iQa7GC62W8@j:B2,U\UGGLbs.`L;FH(4^:a_0JO\"0WtD53sRc?=FPKbf^k5\%XUfP6=MooB$Us_'C>:G`Z5LY+@B-)l<fKaYJWJ
%-NW1!Wl><N8n<3?(25+?dR73j_BT%9lW.VhHS'c&$TU/q1>XA^&pgJ'<:'!]-t(IupOU`_m':0\:V_*,7%db6R-dnVI)Eb>S+Ro)
%"j"M?C%is^2+sRR7=,HJh_m`#JkO!hf.<m-8u;nu`gsZJ`9ub>bWdFHPFD3W`@pG?Z$)ua&\[<`-(<#k9.<f9Lm[lm0I"l8=d<Mb
%-Z,l>51;!>kZ]sNeeee=%",<-(2m&M3sCT+N&kAA$l0%%'J^T4g?)3C),Y`C"MUtO!/k?_kIRi#*a,a-718D2<!6%$`kL;/]e"_g
%NqOo)e$3s0Uh^Hg_*[dL\0Jf\:CD.&k#?4Z)Qg4R7eL%PTU@C#hFO.Qj#V>"L9r3NIBIruG#kM'b_RNEYViQL>!Xr1$QB_B%Z#s[
%dH"Yk_;=O4U'\Mnf'O8:>/Fs?1=K2^.d6*k$S#/tX`4!5j+E"WW4-%!G1RiHO.k:Z/`=_U=k,Z:kH8/r@6+8FH<I#Jp@q(o$k'p@
%k^U.%%V,c922=e!3Gp-Liq\HM<2645agKC[]MMkL$#i;e^spO/`"SH)Ge>:27e1SE%,qu9K#KJGLn_%q<u:C@A"q2u]gZ"]q-F4m
%`\[^-FllY:oZL(?3_.I,ifROcOqf+OBOEM*.HM<2S/(Zc;-85aP567%Uc&7HG]a?qmghXF'^1HA\7ktpZOJ#j:'md89;Jhq40].<
%4U*O)FEj'K@koFlQH_2s[kiWdHs!5RR`VRu9bttTQNu6@rW_+@8/Cm*lS)3pY\WXZXSt8?_9W8-n-l88p5QN639kT$/CASCJm_<g
%g91Zj('ct%)kb8RYnE!t'K"P)KjQ?4C3LP9jrIEdT7A+lOb6t.gpUb`FC=CRWd_?:*s+5<@6SDb5]N;l6Tu4OlLe8D-&KkGQJ5#:
%1t:\i2#%\-W\ZMOmF7sURbJAu(/%S4HnH0EG6`@6"CoN9`J"LOi(P^UTW[]l6VoKNO@KthE])$H/,2HV9sL.?fH3lNpo`"!-Pj!(
%?NaT@Qqpd#7A9q!c0hG]>A=Z<6*HE2qh[M?l;-f@E@5bP6r`B<%9lSb_`o3!0rbVIj[Rl@8VSR`s",hYo3\Y39WA3^3$#<TVBtto
%qh:q3nZc+H<`;I9/O9ff4o5[[5pUWH`tL"-WoWX1d0tIJ4nq;5$9PG?,ur&`I8V[lljp)DRp(35RAV/`",`Be*ZUaqRPjlkK33Al
%4?FjiP"\i6;@m>YNnpC5gek@O5[J12f>=W4U/YD/K,r!eM'Igr^%uJk[CCOH.TB#Z:_$F'mACIUk'"r5$OBGTdt&L[$>:88!M+IL
%T<j&s].(7"C(6hn"GU#KJq^or38oNQ$;`r[,kiW93<JC]Y`\1$9Mj3u'kFc@n'fWU\1[J'<WKt!#(]__U&uh+J6mbOge[lK8JT.7
%XZlBK%D'T=\/4cH]uQ5+E1)Gn4\b2i9BUJC=e)X6n89EUhFbkL*rA?K/G4bof'<PSR4h.dlW):W6T0+f(@9BL^]2)\/2Fo'QJa>t
%Z+*56*OkHk._\A0=>0_"c".CIIaJRs\T7!'<&;YSKc($*V$%+'bBtGk=Pu6uC[=t@rah4-`j0]P=lkp7IkAt0;3gZNhXMfp,FJ;`
%5u5Zm_c+s571RIEY&Huq%uN+!.CVb;#PAs$2g@rYRkaL)iSh@MOGh-@$[Gn-7(L[P_2VtV#^p-dEdTIPKFIMX<o4Z_$+j45q5TAq
%0flJN\3JHsn,)b)je"3/5.\9&j2LiM_lk/3G'!>Wla+p?0##!n>0EHY?TpN_]=YSdjtlHh]6n^/*^`L>S>N2J^A.Wsl.mp@q8q5a
%bSA8B5P_?E5In><S"B_TeLJ>U6br$(98L#qf"gRWDA:NUn9"NrD!dQRb?LGkgV^)8o8ACeB3/lH]m"i!kb!3cp0I?8lYGo!^Uem4
%*\cNC[Jnb^]s/@Cpu9P*\AR&VJA;*tp>Q%Zrl6,;SMt)'n>P+<bh)FOJ$8nKcP??++W`g0r;1uR_kUT6_s]hP7Elfq`1efl7.Z3N
%4V39=CcY-dh.BqdW`W#]jQj=/#L=iei[<@'aEp-P.RZQOZe2!u6/5u"=]0()H-r[2>eKmp4$&Z7I=QT,$_3J\h0bA^.U@G_0\>Ob
%I%grYXEJ&$AH8>RYG*aQ2.!M_/trF-'!cB`QJ,*H0/RNfCh,Kak3dP%9jC[]QSui)8O#]lNL.>Lq#O7?i7oe07m^RIg`j;U/E^Y'
%V^LF(nCVTJ"[Bn=!d4bKK=T=Ds2s=7nZ\egRFok(/KB'B'KiuET7't"hKM<[\%kUueRb3"'+aCoDRN)SH%KRc']h)M,u2Ma"8UY]
%,;>]hip[[MZIi[kE+9JV*OMA1`e,C_*kj#,F]ELl6+rl2:a@[MQ0*U3/<]<fpjS)WBRg<7,h`t;CnLnL>>Zrc9XbQ[g,[Oig-b1L
%.HsW"Y,4>0Zpi(_/"!MKX>H<cKeU>M9XOE4Ge/Lli!hW.gq$jEjN:D!][/5AE_#0(#;@,m<h\,?/?R9b<Cph+\pn;>d(0sqa<r37
%Uk5<J,?CZ\(*jl*#MN=!V?+L8]YT87\'^$tJjo:*++l6:aVsfojW:QI)Dh2URdhYbfBH-CXH71mTIYWH>*BjJK-b`)j(`Gep(9+^
%S/a)$GH4l8f@)&nrlHWlm:8e!10mG1DuueXnF]*&Ukfj#dbM`ZK'!hLU_kVuA6eH+3YaH5<j^$3KEDr-g_IX3TZ.b0%.X5cFFm/N
%).hJ`\K"IcAHbT]M\;=s(f'i,P(gu,9JR2B+\JG*7$\7W?EZM?,l+o!1S.Q`L]Uc2^@cG>AKOQ1S,lAgbh@ZB(4%[W+aCF&_/)F$
%d4qI)90e#<;[J+Q%7d"m3-C@7T`i3)"8CNGN-cB`f(:AK#4bY9TV5[&@EaNt_+ZC'Br$DSEA?n]<2aa^Lm6r`>]gF<8G\#]<FqsW
%U]2)bEE!7PQE2\q]Ib2fe`;uFQ,Z'mS4.k4oLYXnKcdU^!jecP(P"dq!]!eGS_/%7LF@u,+3E*m'pd@ga-5\T![cJ,SQ1SNJ3.HN
%r<DJ<-3W1Zrh<P:^d,3)2c<_Z7DFPX#jTaoD;L2oRo>Ya,]:b0eWs1Yc`&MO+k4o4]2[.%-g<u*%=7$9`N2[FE%i*gKkYgq`bq`a
%8"t2_"p0.RJ<:8CK&rkYLQ_)!l-r;$pea;5=G.>DH4o6lb*8dPiHi8eX[EB+F]0_gpJLn>X#YN.V.J[6WLgV"UUarPZGg>S=$@<9
%#&QV$Sl=/;iuWWEI8I[ZBE;4h->Kcb>@6Oh=IW/u#t8XRk/k5Wk6IX(T;cOV=Yd^'H:3T#CbfdE:`i`sFXXVr$FR7oC7B/R-/HUh
%lOLDhq]O0TP%mk088\QRX_ZaL(6kuec.I5YVpst:j2%?_5iGU4C<+WZog9t?MH>$?9(/pH\*:dRfi^\-MR''383JHC$=j-b?mWUl
%TD!,n:*&P'$L?ducX"gN`G7mlKPm6DUB4B"`Pa!kS?OgnBuR*m,ib"1%-W>+OA4/VX2R.8*gH+:,IqD'99M#.;m9?8X3/q9QCLO,
%F2e[TNH)abkD.Ns7@JsnS?6Ht9;g)BMn#Z34g?k-2eJfnOl"4hW"%GB9Tctj^6]sG.i^1mIBQWlcpO[s$^RF/85+;,cU2_XM`p<H
%-mhQ*.cuQ/?Gr6VB@hiI]BEN'Vd*a6B*f40a0TI(QJG>%%RX6be9f9AmF[=`pa:E=(6FI^W@b"778@f'2/'fsFd?bKaMau\4Q'@o
%,T+Ij7eUg_A:\Q[4.Lt1W1Hg<*Cp;;*n;0EVk.c"9.+sqRdT7d=p#\n8hMDE'7_6%:'MiNm-dH@bBA1A>/=_Z*:%^QnWh%*OkF@=
%VP\34HJ0Cch,YUELLTTI0od\A%m7q_B]:#V,80%@FG<lJ90aOUd'jG8PEEd8U^olY1lAP!b9g$*'!YioW.S<B*Y;*+VYG3F&F;_<
%BdAtlii&.)d>SAp7DQQ'[:BRZ/T[Nr$a1j3;.r*-)C[@b?mB'oGg3Cu2SZNB%r=j*Fgg:`Sh$?24F&-kS`AOQI?M%4._Me+<6d8L
%Tl.nJa!nKdQm=1(??Fr@Uimg6Xq2&Qc6jo=8[oVZ3M(Lu0dTD//Qj"UXs)UibFa=+?dAeL`u3q`ESG#,#;L5*iun[^A]UX;pnP>I
%LG$jM.L:&&V/Q$k5nlE.!c&.ekiNa/EtqZf<@F2UEL+#L@6^!D=]1P;&e9J6=kHCR$*PqVg5'R8J5oMqK;ugDpbHBD-@+$5A56fB
%41?&Ym9'F[_M*Xg1ol0[j'I-!/;T_B9O=pL#;$^L:\&6u=%rhQg4^IL7iLba7gg*W-)K8:Q@gqJfhs%^cJ-5Vl+qKa)VL"L!)s+.
%:YC=2,7,RG4d+m>:m$VMb3Tqs%2]c9WfWE$eO(n3&_AKK'@t"!MBnB5>>#6m^rc!T.i:.Q#H/?HZltVES4LGji:E%Cp4D5MG-j[N
%^0+ubMcL!&o%.*(pd"OB7-3Q@`b];*VIB-1eAmeek_A<O1lD`ADZm!CT[qLYMujc5WP>hQU>X'5;lXFVlO-'XpF\u47@h8_#*?^1
%ZW&_60;>mmUDlfn$*l5M<aX*V3]re*%2pK8Y-5+2Y[D-t"[tN\.Jk<BhQ+3pf1memQned;.DJPk(ku20]US-o-b:1!W'faj'>GP+
%,fNK_-(>KUO/!d(GQLNF8j,iVU6QiOO7Q\!o8$$u[`NU>j*8Sn,Om%g3@@R]5oHW*C%KQgEtk>Li]+M-)[L[_W[<Q,89"p`1nCAK
%d[4oKUQp;p9\ls>ABC=haAe"&!Y^G4-(F%7>lU)d/go2ooQl@92OA1#m@GW&omk)!\?1rL`53\9"35ET_60Ju'pW_m</+(#=GfVZ
%#Is2<.b;&H/G,A7m2l2OW@3;C85+2l<H83KdrD>l1aJp6P9*(rUAF$s8kY]2+P5b?fi/e_@E!#*Xj-dZ@LoEc,t'l%;KqD<\/d[@
%I@_Fgl1n6jBpe.qbQ%Zgq%mn7#0)J1=Xa%;oua@GaqgmmdZr%$?t*7i'"JeLEQW9AfN!!g`)Z*94YJ^"(*fab.]0Ul$[;-7:`rJ:
%MlHfSb:SLH"_NEt?Y/FM5pg)5*ETn:`<mp/^e<&?_bna-/!tU#+m=8l6#pKI6jXcf&<f"S67ju4L:djrH_HkF/I3i;B.Ht3Z9*VG
%;M:VgpU/RP:ZtWiqq.04MsbqG@A:8e)Pnu3*^V;D]`eYVU8+9+E3`8]]uS$f)"Pe=PUc)#`43q,gVe=/+c)%j2iZT[WALr:)oE8e
%+6+pcZ`3W@Zs66>oA9P_(qY@A"3fOm22@unfA79PG+6fu6q5Yd!f]s>'_,p\c"_i`Q5R6?\`m.5l3tKHHnNp"-(=P\^no)8Mn1V$
%i%A3Peg!R.C^u1.3@2ItNhG7\7"6kY=+Y`\L@2\Z8#JfQ5gCJ4(l'S93M0SUIVsbe\I7qbmk3t+hkc#/:S[<qOUt4ke"l[>=9`1:
%!J=[]Jp;&;(eL@Ff/;6fSA7!E3d$].r&B!VL1o>8?NpV01(MB>Rjo[:Y1$A#aCN(;-Ym"nl%h_N<R6ED`^JW1:M,&h<CBu'C#>Ul
%mN[SD\0UV:S5=66KO%:(3dm]R4(ct+K;?+Q-*2;#n?C#Oc3KU<]q`b.63GKNE>!Uc2*93rkt.GTh!GkTNeN5S\I4\@<&-'gSBC@o
%UuL1\24i"pLB<aajhaF:3/n.NWn$-Hd*T#"VW7nmKp-8LE_h$38Lg$>2?C`mi&HeMF"LeaI!/Y2)@WmD,=2rcA.;"!VTbAa.`>27
%@tUIs=Saf>TM/S&P5/U!7<8IG.835ukY,t6Q5h8D(IrDn`:@P2KsRs$eL"A>-1k?n4a@/8/$jh,pI99h+NTq5=Qf(f(L=DAjk#2X
%e4Wc&Xb(-3G$ae+!CZd@^:MaFCR$j*&SnAY/]Gm(,XGsC7pWCWCB:,E[hi%Q5]KY1;@p*X[!sWsX.guc(>L/)oQl@GZ8TU&;A46^
%kQuB["tr;T;C>&$)NpLr(-OF1%TMJ-&G&>cQ3;?H?&"XceIr/<k1n0GT%tXM%gspr;B='e9<qou>/t6NDHrH33sa>Nin,nbn&M:]
%TfX2OW#BuJ&Y'[VaIb>Jd%WRLfs2Al7l-tJ`('q:,,`E>B&?#NM"$$1<TpiSXc7nuiY*\TWhr^X^*1rk('c.hWY<sd$IN\J<Cgpc
%eD,pS;"?a_OG!e"n"_YCO<k_GJm>d:Bk2C0(tDiT"\EkXS;eWF%:'^o/"T^9+b`i:i-K-\2C,7(d%JIEqibdSjVh'M?%m^C'G2""
%jHpq=TiVcfAM3/.iMYrR;(WfUXEdEVNmKYHE^lTu?m[^6d<r^X?V(:.q>dtr$Lm86'dsKmic,]fD6oXR6KXim>q=p8c"U,:j.M*\
%RMnXc64siDDg.(?28*B'ji@Gh$LBT5HM)X-[*/O"6Ur)X%^Zge+nCZ]X/mjX44)0\P978a:&A;TjJ-6L6LJfAoD$GPZg1,:VdGrW
%\Mu^cH$$<6jEVnMVk;1W.3WRd*.3LIB/q)J?sS,4?u_`Z5IKP"a#B(*Xq./[?9UQ#G]ruGe&/)%[!VSfVf2#%RJc%2-h?lbDt7CA
%LVt1#*$(FS'c<Zu?c?2SQmUmHNc_.]HMhQ)qV"'3`0j)b2aJarDA<C:r\Pe9!-:u<74\2R^X#:@+)`3rrkJJkW\P**BJ`5Rb$S61
%f=DT%N@23Md\<=3&uN?5(ko2=2bA,Ri"!Cp4QHa33/6VHjh;hE5q:u&)uIXSn)MOWYGjE_(lXc5W;;ZjeA05i=?iJeC!WtW(puYa
%Y?i1F`i8FeamH:B2Sk=&7O%epoLs:&#Z\hQ`Q:.+\TM%E>une1l=+*[olWC-rAE+OaiUe.&-O:l80O7sZ3RR?>8Q^0T3n($[F*gB
%f>bOEr-a!6qeN4VH:sGi+JRnN%8-V^KZ-S:OARW.c`Kn[[Af>uO)LQbIa(Btk1Zp2[CW"YS7M;YImd_.EcRA,jncNqaeLa?cF'Ws
%Y0H\;,p*#RC9sMY,V>)(R)7Z`![=q')1n/R8A^s&Rke`B_d&ZDkLsaeT>T'[WAa(BmI9N9XsH+S;<3'IkAgi4F5J[dl_mNuF)>rb
%CU<tgE_!mAgA(?Xi:(S/NYE./>SaBXa]c&KMl"_,88bTLp:Es\qDE$(rBR<%*L<(809]Z(=pOSaM3-f,aT!6/Itm4(9Rq;B'&ATG
%\f/>r-3E@ogKu,uO+7)FGC]]0$c:ju^\DfKooqYT5M"uX[:2mJE%Q$)d4/=3%-$P\$">fi8p&'L$ns>86E4YE'c[h:^iM.O(fZkG
%[Ah,YJGjoY@bX,+=Demh+gMAN*jlmi\7,Up_.oW=if:cBL0qasA$YijM+"#52`ruM*Yc9ceHhmgi/BB7Qkf79.\ME2m^WX"oG;E1
%0h!cuWRP;UEu"bk?4,CZaG"%#=nH.#%KQtV.halH09pMK0U!@4F,R@:TiD<Aol2I4jt5L`F3ojGZ^L7(^oH3">O*[<2gt=Dm,Q-Z
%/:d#M!.DE"AJ@bU?@q;Ze^T2.p#2S[7=t4#,<32CqS&hbg-:L30)QK'a5[%s]J^O,h-L(UMTsR^&S0Z.qGs0O#O#/lhFe]1:Ao6*
%SDO<]`_rc;o:Yq<4(^=]>72KmYcm$J9Mj/VBdXVl'Yn#VNOYI5*M\)iqE@!12_9%eRqB4.JOa@I:_A;sH7`D:&qjLTZ-*VS5^jJ*
%cLlGn-YS*H:5@b>"&kaY+bO&%3&@$hqE\SDIWT(oWV3+3mNH*)ffe[qf`M0SS%mOdK1m0$R+=EBF<h-W>T;q>[[>]$7KFM?eQWLB
%(l/l0Y<fOhPfYf:!<6(0Ab:CZLg#BP$S@UV"GO1.oOG4i0^Y#3:UX^aKrtigkQ(sD4ASR]B,98+RhPQEoMCj\&T$7l<\eWM[&60,
%*_nVXY%Z^khr@QmGY<&d@Al(3R-._i`43rA#U;bk6#iuenR/1G\jD2+I'5m'b=.oS5_CLD-[#(aD65D;\Lgiu?^lR?ZZ:o7B,s^c
%"NND&+'dVmh))+PPJE0ioG+t0@b$t8plKIO[3uMq3DiQ"/V[_Y$Ag-()+T##rlZ:L:M;r;8"kCe_68!0Qju@8?Tu3R'A<ttek8&t
%H1&(6P:jcBGW1#E^D&;bX,b*X(&6Fm.i8TrbSn=G=`OOt2QsQp+;9fa;+!T&_%`sR8D+!@Q"k"HhWmX<M-V:s/E,b->FMh/CYe8T
%2pEn6L@d?">ofc\V&a!D9l&sC6U)27<!irqCf-ud^*/NCc7K,q'CaGr@8(,_(pb*$B?2kgQ=-Q=<d\uu2\"`?$-P/q`lu%^iFLDW
%D+A=FqL3BpBH=m&=3!IO+V'q["h?&ifXKN?gITH%*B>VMqPmP'Mu^]50QD"L"i/n!I&XgjRhZiF(D(_C<H-#nS'W>YEN)Gsd-Z>W
%/WsVK@_U..7eN:J-]7=qk"b6r;NJV1f1^Ftq:!"L@VoUd%Q:nm5T;40!dJnkC%HX,jE/Z1i92)^fLKtW#:W_Gds9Jf'DinqUB,RJ
%BK00G#g3@dR/7)Z9[%JO(u&!r3d.m&ECg6],!fl=076"ifLpbXn,D;(NsZ?M1o\;HeKDeR<*ME;Z<c/;72'V.KQ2fD[N'2^%+aC4
%55&58:>sBZG6=f+3$%9DXr''Xh<*LVL(*>7m\fkt)*t]=fN\VAhPJl*iGp13T%!p6F2P&rlt.GeNMY?&3A]OZB<;dEmcU^E-Grg(
%R#9p>N:Yi.-^)QE"^??M&[8+gi-s;CfX>i*S8EA:>k,7\UfSerIRP"Fph\u.ZaLqi[tAS1nTNK0,nN5**%(+.mhrtRjN8pA<!o*(
%Bk1nEFtTs4!rHi[e"45*'#suHHfkU'p=TmmHa6_.e'7%B2.P-bVt]Je[lG4K1>Qlo%pl5+p4n_a5IZCZEVM*aW";t4b\%tmer$4)
%Op!l"ZZ_`,e3%Q.?,"?Zm!EAhVYaab:o>("1E^sk`EE\.R)J#E=RkO;ZjZJf[3&$l1CQs4eEno7nn"DgIos*bR]_j7C>q2q;%igM
%c/W]^'kEDrJ3FGH(q_a0h!EW2cRsc3@rouW&eQgi^c&+EJ]"Bn?`I!HD6&2fp7uT4qB?PB6!DA6,\%d>_=p&B@@m%_/KFPQ/n+@a
%Jdc.Z5NfjhHW2lKTX/Js]]rub/Y58C=UHK<pB#Z?^!Ak><K>)>CYH#;@#Cjqb.D*Vao^J:m3/4A=%=rBcAct7JM#l4.@d==Vr,iU
%XpI3)f4>BrFF\f4,CQ^[--qK$%fX7J56Gk1Xe;jiq&%n'lP"/N*S[OsbeEOpb3gE\QrGQ(*sP<K_q=.R^'pL^9adOn#+co\)XuTD
%j:=J+1Iop&n:`T%!;iVomi8J#/67R'eFrQp!.51.'7Jo0^eM:4pul59fC)02.\RMC#B-s9..`h8P6OFo$ujpXnju^Mjs1,`)!CqQ
%KkbqhPR/`,R`_ccVSSbem*+()f0&^-htDb6(8brPD8>OMCuAf;<(Fr?q'r`]Mq2;DogO#h?7+fhFm5#=Q=#Y@Z_nJjM4gf)CHR<k
%[MO987buT]am1(;o*(;k<+t-o]`i<WEs$<+D8JdZpJRoo(cBqUgo,*4A#1'rb(;\Kmc8EVR]ThXN#>9c-ll+Ao*W+eZfcs=.p4EY
%\AgI2m,]/OBh9,=76)T;;+26[WDpDY:T#R#I#MRX3/VAVXne?irO"ib&l2:fYZ`\S&"2\7BOldWqrS=M6%R9Wln*uSJs?++2)!(C
%S.*B.eGmJ!Oc>s7^e-PUBfNAK@s\FapASLPX4sh!1lM!@NCC56I"4,.IWgKT"_#EQ.iB[QZ5:i39VVTkf[BX>Ou,FUV^A1D=mpm1
%dF^6:(5\I!I=D=Qgkuh`7UpAIfE<0a*3ohnAu/6!05j)]O>ir>2m*Ada/t*Y`stiHg"OikfnQ3TN4l6NdFu]S/@7l4#IrNI"`.B"
%DDV!PkG#tUc.I5pW]4h7G"i0S2Iiq\e/5GMgJo_a)hQ[jbADZP\fnS)1`<5<FG?@R_b2O-+D*MA_S*&l3Lc"\"khKeN2nJM<#6Z2
%:fYL8Q]9[C`k0n`gt,-.FK9Dc1m<%qcB%8MPUE9;Ai1'a6:4!o":(r^"urt5D?$[bVZahE%OZ<C^p#iK/m>T!1tF&]Xk(p3JZ!5`
%-_kZG:eKJI?NTks-"%QZ3^icE9_4C2(E.kLAo+jSkXF+d)5DaE36eF-Nuk0!!QDL@C<4u8PKha($!E#-f[EK_i,SmgCrS9fD8r00
%cid9+_<Ut0j%q[mA$^qr5E:n4[kbF5?ZMPm:;Ft`lq+8Zgl9L2Z4%%!S/;gJ6nm]HUg18APKfILQsdWa1N5$K!X$K=K\sl=@BtKY
%=OS)kosiZKKCD;GlX;Rf#:2/@YT&R7E&JUF4EdL)Yu"r1S'a4b:<EMAIL>*"@9j=YQfHRa7H_o^*3;/bDqQ;q`
%NP51*E#10U?H-=<dP'7b"2h>]bP=PePej/ECYPY6$qog([]-58"Wt0I-"'$Bo[>QD%g[&JemLhr,=&_T\e+7:Tc-5='oSl#*FoHh
%P*u.h%(.:"YNC438Dik&cn?hKO]I'6lf'(48UY]o(!sEc47mHqTiBg_Wh)YIV=.9qJ%mPC_pu;+hKBo<&D(Mf33d7^-"`RsH38Kp
%Wj+7*oSfZS?"UK&H?*_tE;GrD>X:i2@.-We'9*7QDEbJ7dE,"VH@#[\MG(aTO0KiT)r-CA!1U)cV2qk/2[VOG&_4D_JZJ#:6W@lI
%CV9^2h^gkTVAi1eCu,<:d&)ca/4ajYR"2jh\)pE3=:lYK9dKs1K=cPQJ6Vp5KQksYE8%Zj"mW/]_,aq.qh?fX2uU,Op'\0h/fE;g
%[um%s.lqNXMiAZ[S`@hBk93&O,VX9Aeo*?`_D>IfUmheKF<[._SBeVQ"o!.a0>qC/pN+iKD[&sc@Fe*#_o^TeR#9EnA#R:#eY+)O
%VY\_hQ7^;2Z;do:Z\d-cN9`m@.Ab`q2_,pA0g:V,@gqb3^MHm6rNJ#E31WKd6]?bTE>SFginT&'fc9c4aRm@'M7dH^4dmI\d:q@W
%<6"hQduM^Oql-V^kXT]<,aD\J'B_1mLQ+*CA9HPW3,pV*SleYoN>AFta0mnO!V[6^I6m&(Z7-C-IUR)G.sRJ!S3HiKbk7ElF#S//
%d6fD.S`k;CNn\FmK(f_$Med#(Zni"ZqV>in_eA,krD)j24rH7R)kf7G3%&Z\`>eSK(&P`*:,Yin^>*W%Jk+!25,kT^\t.+YdL0pU
%:,Wbe-YV9q=u#X(V@`9a0bqOkf>Z,3UP"_Ac/,%l5TII%J!JB'+raUah;N8"h8,VL-!,X"2c;'h297bRF[B%T+[V@&GWg/p3kJ:&
%jk)P(J;Gu+lEKZZouWQ"Zhn1]H:^00d'Jf0c2"pmi^&FWAk)qthAKoM[ZFK#SBTe8<@-2PR`</E6@@G5Md)E1HP1&^3t-)>YqjF4
%fU;-%X5U"=Kkc!_DYV]qU[-d^2TlRPk,Go0hA'N5:Z,j):\\$5k]R38Y9Q]fkI+$DZ<df#eu)'AgCiY69LI\HS2:R]Jk,!#I3A/g
%4.i:0:Z2\a!!Xs^(j"kBg)"Z/[+bhS^-D'(eYk^1c"1;;IAl2^H-kHH$*j9a46WZT+h622Q.K%*Z"ks#Go(GdY6tY<$lkG!8b_HC
%IRpi,D[WP?7bN5co5((;)e1?T>/eet<MW&nMnA<*!-bS:"7HG4V'Zai(q_^0#f^McG_9RR5F=@@2ks[r1iM;Kg2M6uOrW#P$-gH/
%)bq8iMVkrU[VDPf:Y=X?,?:XlG%(pGd11+V@0]\X9rW(<Q5g<TVN##%V%3234OQUb+0ILZVIG1dc>QJ1JQhktKqXm`VIaN]H-CS!
%%#lgo.!GP9$]I0pc^#I@"Rbi0l39tqc/F[Mjd_7N`iKNrlO<tgce)Zpkl$IR!Q.Agh!m#?Jc%<.WL#Xsolu>sj6N)q;_Y)tH0,:*
%Yhr145[)F-ko6K;0blIgaRM8E"s5EK6'EI3*U6fo,pY-m*3s3rgHQ@CD2?\T4ti>?854<OX)M!;)gn-h?%'4'H/"judoV\rS._F1
%]>eac#f]nkN)OdRDbY"1HFiCUM+Qgqj=p%I6@EQ0So'KtLo+I""&<(g#.OOkR)@+[P6[ro384(Y&UDW6KV2(mS'sd0"R`lHadAjX
%,>$8GY?`;dF.5rjJP>o1a/*/Ha7It6glK1A8;<hX`T:(nlqVdIS5?Nf0Zuf7Z0K;C(+2W'i/QY\g!Vmo2d3XjCf-_0CosmF6d%Ar
%)_Kg[c!\%1ebFbU<Igk?:m1MJ,"7X%ba-a]7HWF=X\\\]1-[nWT\OQe#`2f1T,Wu:>;miVRi3Kr."1o_!c&%NM^''`&'#N`:2q5$
%O57g#.*;G#N4=?1hi,0%Zu;9Cdk'n;OU6cf]#2di"2oUmiL.%N(oK:7E.Zr9TOS4+E3ZC%fek3^(419o!pX?$VTLlS:10M2q^/uL
%n,V"rCbH-q><'2oBOZtg'a,lUmE4?E?g)raRC[6^LK?t;A";,m\SMT5?EB6.[nh<@$G3o5''!e0*QNelp-a,3R_YoW2Y[hX7[S*`
%_d[HV6g%.\5*'bds3228V@t>mC$$HEPp7BJLaKDH%)q^:eg>Nch\!>BC#u9?C&m/B3D_)DeA,Jhf$kQ\qf:2''=d;e#p2fo*A7M_
%O0&b;hc_q,rXd57:lL]7ioB93HR,%p7bc(YR!S>cq-h]c]/r&I87V\e$bBNb:h;S&$,uT"dCM:g,a2bV_9m%&Hu)']<#^`i%An."
%<7_<l-ER/D<Eh\LD$4q/3EqnTW0BZ0_&?)-gJrF/ER2@;_S+tt+ljn=irSsJ`&K%aE6csI3>P-U+^`fMDq&HGAg80S.Judos2aV0
%I]4SA5Hn<Eg\!/1PHJiWST'##mY'n1G,JX'Z9*5XbO1&LRt5q<\)"s)=*O-0B%>VI^1DN*oe!chY&E!aG(%R%f_$i.A(S1@`jj];
%9O6QH`0\ocfs3]uqNfrOf=1*A\@uNum/!#j^"^05ATu#a/\;\!DqU\)IG6rUD&A:=hm0P#mp4hV45p%%h$M]BlqY1,ATu#a/\;\!
%DqU\)IEV&3lW\N[>i,tF]=r,(<nFhI::S#LGM;^A]+_>cfq!)19C9+P>AD3]DlFbh^86#,oe!QbY&C;1E.(B+_A&U);ENk6QJ)Ys
%\$RGT1"Wna::S$7G/Eo([F;9sWL\!V9C7Du>A>NkhblPpqNfhAf=07)]=r,(<nB8Z(2<[2Uq9ChV!D6"-X>P,4*H$FmBl1`C+!9D
%;ENk6QM*EalM?h#$.h,XnPr%4&RF9l472m1g'QB?e5!NfV0:Sc/[Z7pDlK=II7s7?leD,AFZN(\K^7Bq+1gcBC+!9D;ENk6QJ-')
%\*T,\hm,?&qNfrOf=1*A\@uNu_Y)0h^$FGNE-H`RmY'n1G,JX'Z9.%AQJDl!2`i^`Edd!,%UFKL.q?)@hBq*a<bAu?%/l$=ifR82
%^qEc<YZ"^C&Ifc59rZ0:)$*NIAHY6Gb!<c:=r@G:=rG6P>+'4NWg27d::2iIF]87\e<$A;R@:bh:MOH1?Z&J!d`UpbLTZo17WW6S
%[c;89-JM+oN58Rc0Z7n-Q,3:_KN@4P0E$!3Um2P]81PqC.BSQe7p;>I:2"2A4Je;Tr(&?%h$L2WG,M`'3:H:C175k(Xc.ho8)8>d
%.s$Yj,G!$G>++_PBOt$%'3^gj:2"4c'76k*Y1kl0O]H'8NNSQ"%:.T.bi@Kd1:'AjXc.g8XisP=9Il=\Bs#V,j/SA)`_.gP2:8b`
%[a(n<3GP4a"*Guu1>?]9^f+DS%*2a*K;%%8AaF`Qf?.o`@^PK(.ruu)Wg28[i(uNUiPhr3a'+n1(.0I$.N`G;d(I%mAs1@iGY0JB
%Fn&`%YuYHuDO]o4CLni4(g<?0?uA$gUfPM'$RbFB=rDrO=rE(uIor?(l6)UJ78?p9XG7hl7hC`Y[/!3o7!`J49U_BjL8k?jiljQo
%qitJ;6.ujNek:()ZMYqiUR`"9,#aQSTq2ibGYp#8gUF%l&TH1D"C&PCn!@1S?Xp[3DJ?Q_-45b7ket)]=?V"?mFmt9(D*Xu3;#$6
%(JP1qigR4]>s#@n-gf-:a#"-ZMClLP87*RnXI\UbAALu%,G;O$0Qek=K5L%q#rh6FcS-YQ^LNHa%bSdB68^bRhR,.&Z\s@O8=HVc
%ZZ8OT2l*3]2+]LmgGi+gW)&khG1+ZhOX$bSB*&fc"ESc:Z-9@nURVW+o-%B\q`q13>sM?6"D/Y9J5+1S?t9[tf@%>FqWN$Lb@2>B
%%+Gp>]m'K1C#JbHh74<WXef?fDIuc>;"fq70G\."Oj5i?bQ,gU2`'K"2mX>5`sUO;GgT?18LZL\Aeq8u!T%rTl7#KToIYuToYe',
%XO+')o>?,A_8(fN8[f'.-f'_@%V2\Y=3^s1X_#0]PD)>1B`TUC9X)^61sYb/J;GEML2e(X`sFLOeQKkV/5L6:cA4,BdM@%\MbK=<
%-ZGMQ/KI!U:'sn#BkP@OU%G%GS7GSeb#$B>O$_4u0?XZ<;U3L2=3a@12@cUR$'o-Bj"TdEU?8a26T5\Hl]Zd$RX)J7.^V%_h_GG'
%TpQ.Tc4Aj['/GE6?m0INA(Cj+]hD8:0ZeUj<@p?k[Xh_O"*Ls)LL\OXG$Qc-QqDGO#Ej8X,cp<C(?T95f_%!U39t,[?FsO`E^aE*
%RpJQ?E9TRGVFE8C+Ef0^b:IfV(=6#4o<L4=AL+s'Xh0A-2ll41SAu+,Ak-a:DOOD%)TD\]qNUWcfHlPHIreRKf$!i[DS,7?VUa'@
%et(!ke$5l.D(o*'fdgA(l(W=Zen*Q4Tbo_1X/%1WS,.Q+V)X+fc38+:NfQ7N:Yjbl12e(N6aoZ#J(AI(9GRHPB[ElAX`IqqRD;N2
%^k<6<eXXW[e=!qJSrS0;eQct_AL^9[37o!(;6FXn:>AdN(6do5j(]`_:H[XX%"3iei2Xt[)Zn<$bP%aTaY(jXd^Yn#X<3ss7&g]P
%1ZdnTD)DG:T.!d_Gni8sX3@dUE'q1)I4nu]2VC*96^VVdRlu+n96ku2$HKEBH1IEtN.Hp,qF9ASEU,Ka_1HYo6`Df!EC3hg^oc5\
%KQjV6]iW60HVJ9D:b4]3C#:k'a_TU)Mso"<d@3(\SRtXMMN25Fg!nn?9p:>HkFmmaFJ)]Jh!hJ$B&]#/fjL>+[_/Z0:,Hi:;N\4i
%_/<&-Y0Gm8J$Db;+"fJL;mGC=B,tUHeT%g_.]aEO\=m5/`Dt\lYL\.`h=d>$OMrH=bS3ghj!6O;d*DJ,k#GWfp=--:1JTYC<rFZB
%Lk%Pg2o7TE8WqWXZ'BTo?p'l#R<^<lZRLqA1?P.],^,A1go">)2g9o9`1>'Wo5ks)e!d^c\#okY9tWKoFJ&A2As_`_DI'+e]Ut$\
%YrAkQJ_<t_(0AUV)f.AR0%58Pp2a;%#<#K.95l^Nn"_=E0),>D&Z0u'LDS^V-`:\I]ffVH9pf46^Z>Vl)*2VqA!$!B1d-CoYMasB
%VeMdYIP23o'd%;j;Di@tK&JQRQl1*3:Pd\DBMUO-=6Je=IT>&'k!Fasf%ULC$6-=#hh@FP2KjGWdVI\$'ZPMXqqa=oiZ7hgf/DGQ
%L366(HIJ-fOs^99f^0SW9[6qb2V'n51HW^cJB9'T-q>Y$A"=S6A=X[9:=csQKujD)WNV`5cn_(>'/b?<Utr-th3>BQ8@gZsH]l_m
%fBI>XB%rocei@)Qg,;@7f%6g.SK$M\N&9Ml4>fa%%UQlAnhu7[+8b#6SEQ2HA:*riI4*ci'51q!Frf8o>PjrEkAdo`.&O]&M&ib2
%Gb'g:)BXJuBBec5N53E,1E/VrCTk&ZAEXpZ/'%j2X<80_46;!UVf"-qOmIur2[a5GfNgk+]Q^tG39.n=](M#2gKH[Bf,*lE?jaNf
%$pmSOH/X=R+PXSD1\YX#]9R$])pf>U%PiSS433)9WEtcoOX<Fdm@2;=%T6NG%I#iFbNQo!8Hb"DE>GT0H/k#`Jc(4r3SES+<S314
%a#/MI0j[Pe5#m8]fQL7iWh8%T0?(ZYNkSnUU0Xrsi22D[Hn:@O;i-X;^"@>p*X4[AI5mOLdBlJkP\+teRaQ,5$p>4h%7E=qLUL]W
%h)FXs#Nr_`?o$&b?VbU1Rcg^HB$=_p!N/-9i7>_CWRFCs)Jk22$e6*Qknl6f!P.:c6Fl?JX[%kepLKfKi7g,4]5MVHF!6@/YhX&I
%*5HP7cU_RsR>m;aFBOY?pkP_"&mJ_uRc`U'D*I;kppGX&FLHQ;,OqTcYQ%/DHYJMi]'(e]j7VKX$=W^O5K89'E!;Bf"5p]!EBt4R
%F)P!7N[;sE2G^&!!QVlj!k1dUNmAW"R^C`3hA2V=DL6:heVR')0<&*Z;lI9gB`c>3QO6L*VbhN.NXDaY3r5;"YG5iR'Krd$!$<BY
%[9.QABR\"1SMT3OOe^."b:=+K8)mMamuPJOR>#j[Y(RGmNkXHunORc-[J2#;G>:q7aq7AZ"2hOp,UsAPc<=Q'h#Z;OZh#`QCq86P
%Dd-uMNbr$p#$fW[LGKgi[R6S,82g.F_,D*YQ(,FsJuOjnh*'&:1@OXN-Gc0<9\/f_O0a\*G8E9/7<uVgfmK:a^,P!$:MesQ;]4tR
%kje(i(22F<nBAU]JUAcuYm_%fhN#/g6Ob#]12j;LMU#>iOCr=<0_/P_Heqn!o.a"Dr$/j+9AWZHoT-QNRcB?Y!H"IbRiN#OkboPD
%i2F"MQ^3"-DYek"#C22J>ql11?*l4i>A))VdS=2gcHuf-14K"E/=r0UE\F"&_QY[\8JROC#s2E<Fu)X+0%QA;RQ3N=9[e!9[T=`h
%(X7CH6B?nAnabX@1bH&sDCe`TghG7q3bTn0cFhSd6rS@d<mcR&_6@K&*L@1H2Aj#Gf@#XA[4]&Sc5jp36U4q\RjqiIE:uk^o.f0:
%/CR@@cGTOl+%TIZ"33Pf\pcKg&.UI2+SV\W:<4,Ngq?HjgLPLKT9J)24*8]gmZ;:,^X]o03LEJnb&oOZi6K3-G,G=[->e%F-dU7^
%</0,on?pmto14*2Yg)EJMJ]&)n-<^$&rnrSPD)"934I"jE83CKf4rTTQgEA3jGg4BZQfQ!kZHiM<@m5Q!oeh!lr/G*UH^7scctXf
%`QfJH)c_u'C]S<I?DDt?&F$#30CP(.^rKBsqu3VcUE/]f_k.G/n=cACOZ.(3%XDO'YB=$N++5Pf7ln;*US"hEXhQcD/i#W9:QH?%
%O.k32rUcI"`L$D*;WkXTY>H,,O]Yo?j&=^^Kn#][_FJcl@BjlZLhuD:%eK]AFemWLPbZCTPMWPpATG36mn*(#M46frFH\\M1d.3p
%r_qpPk])E.p!.F2k3oDBVnWjOft=HT=-<NQ9\jF\Q1Z,8>J2-:IO<#eRt`3flD'3a9D;hM>ENnr]%5d$0/n6UiLfEtD0/.;(<I3-
%lX.j'm4rg:r`Y%t7XnX/h8k(hgOjt.Cq.*drS.iHaug&XKDo"/?G`95[?n1+gQMPFX3D2'X5Ds^I+sZJ:7G*oqId!c'L[OV+o864
%(!iaE-J,*D%@LIN/[J+d"Kosej0s3)rA(@@h\N3?Z;i770CuBKkBPbHXYQjp_.^ou2mi5*[fj1p<0jh8HQrj_mlJXS^##i!I[23m
%f_4^BCq=&C-"26aVpPsq\tRDiA2P<E>J5OYe`pehpsE0Drt86'4]e[]^W2i>[N1'l2P[K`Xgf#Z=6G2_OEeEr()QJ-Y'p"b\!rDU
%Y\-]Wl0g[i,3&RkY$-5.HqStWG1Yt-e9WTYDV&Keh).)"A@'S9\TP"H\[C7'IU="UI(W[d\*In_V7AUpn'+Xn>tJVZFc&]q<8R&7
%dL2PIoCH2"7ak:b=-DHbh2d"sqcVpm]>VC9F5DBKl2<n\_,L;REod.9jK("0SIOaK%>i-'J1fAW]4]fhp@J*Z]IeuTlhjeT-$Co[
%UftMjao5P?n^J2\--bi2S.^")4mkW5@[S8NA@L95J(p6sOg8]Im?2-gQf_'4H[4^J3gUPIUTRg5.@Wsb8[O6I\a/Ne?EMeRUq'ba
%l-/t0`-lidHO8lbrs$pKK?gd#qWHq\q;',/=52X2ic"ha%r].0HhZsi\U<l4p?TU\#j)Y9]lS79Fa2s6p\j[1g"kK'mIdZccT\tP
%Gjj>IUPl$g*r]Ie98C"5^V8Gb2-9gAYFf(`PQ(@!?iBfBc0a?b9Ij):LK,&%GNpeL\U!N$MmKGpcgG[(s")K68nnm?0$c0lStB:o
%?@Vt2mH(*m^A(mTr($R4^\F:AGiR&dn,IP/Z+fB"n>kkF&&3Skg!(7^,esBKS"&`)DD/3^efsP<;uE2o1"Z#(IdoA3H%Ne2rp?J]
%BC#Ttm:ZHEEIRY?^Nnl,7lR&G*8\5Sp@O9\LM,4OT3p@pIXCtUiqCW8qr<6WI;j@PFntk#fon'Ach5[]eo>s#&.VM(mN6oTe_0ff
%j%%uhgljMU(Xb7VE8j`\na!6^]5N++_$_@B4M-'LI?nH7Gb[CiNT4ZZ/O$lC/*m4bG5fJQB9ls:g$>]/s7UaRR4gJe55=.(lH@)Y
%_o)*#bKj2i\#3BKl$%T)F`VnQFa/.Tn^nma;;=@Z1@WGu([!_4/&#1Ui'p1aD<QB_V,dH%K(M3[o#DO,;iMT%h058plNBHMmeZeH
%n\q9@o%(YV2sIpIec@:ha'oSCS"'G+W\odY^.*KhIHM0tW1;B!]2n:=mc0LOUCB8I)/IfA0)fB7*rK5(+A=r5Y4[_7#ff.YOTPHg
%)ucALN$_;@T1rW(q7lSlMZ;V$qk8'&%5f!G/\`CgJ(Fd+m,.LimG#gun]UdDf(A3V?cpFBo<JbngIZ$s_uIFJ097`P07/*>?;?90
%II:Ija323Cf+Z\1f&uqdb8t%cg`aU1<PlqJXc7g7Qe.":?XI*?pOp$Tq>05LfD$ZT_Dg>d\pF)Fo(q_@ip^k&='%>1>'J)tI^?2]
%EnG^(m;lF1qU9@G\9\!?HM#gU=6m_V!nNq&o(p-l#f"LK6(duMe#tpI4$3K5>@)[>m-3X-r2H1O8'`gVT(i//0Kd#@dFmpCo=k#E
%<S`/rRthnt`q9*Ho7M9_*kQoFq./kmNp-?/L%XmRT.W<T\jZ;WOr"#BqX:rnGBUoG,aQXM40APMfAHGMHB.+u4L"f.YQ$4prR<3]
%G?=Q+5jb_'Hi*=#p?#!Xl?kBia84rdA(d(fXaB?jDjQC">O,Lo3$$k#p[[R4X0]#*'BekohHNG?j,6.)7;Q<+[LRY"QKuFQ*o>hf
%O<U0#$.^<EMAIL>$L)g+o#9I^V,!)p*W=nDt$pS078.eL!B=TS)_8sHh$1VqU*dL?>i&]($#/M.5+)-Z/9X-*7S'0jkR'@
%H^?"<8OE6*peZUhS4N`cG3f)8#m9-W/+HKrYCH?Gg[[5XTF\g;>(6G=oj@`EE9d4<q!deAs5=HKI^/nJA%>"2s8>ImDXHkA4KMH[
%``0#%e:-.Peo9rSUUK3drT33aNP3"Wq]??1W5EfTmH;kY=)3ZM1AAL[0],2'@j&]on3e@Eopj_-i7)hCPil-IRp2UCko6,_SYt<8
%jK//&1:.0PXWeTM]XMkPMj1QpKQU4`g)s_]d3k[_;WR3:=T><mm;N%"a/9_tEqK=iG^5S1$EAoOg\Ve:^3JM/)J"uSlQRd9B%2MK
%CSNGJ700l'H"D,`s":V:Y5^,+HWso75q+*iH(J*jD][Z+e*B6%n"-0gmn#42o/<^grqQ*qGVnLOe`Gi[0XlMfUL)V:.f?^J.%U(E
%&,s6'r!6MeXKf(jrWHHNO0kS&XSP6MQNDnDrUtM'FKu!V!a%nTS^1g^]O>QEFhU#M]NBqQACfmLdp$53>.jI;]`$,;Rni<M1&7It
%qfbghIeD71oktp8qWeJXq,o>aLKrI]p!+AOARoe!:@";-WUI6[On?M=oV8!Z?^];*3Ptbi;b$`(Mni!LQM%m3R,5u@&.t"0(PuS:
%[;a)Cf0[mEB(.79\;H"DD1@V,rGd/H-bd4+`/1_4o;b)_S3N4[n;f!W=Rl'8kht(1iK]r'IfJ[_]mcF9k')p!ms.M&6-?j,4F)EI
%lX/AIj!EfNgU$4DJ%W=?r;A:d@Ckdim0kq^9K_"qc0;&bbSTCX^ZP6ehf;Wng\)ompu\W8J,-uPG>G^;mEOJ?h8LI"?MB)[U"_b9
%S,VX6n`=-1[H;CHrTs',RjXQfYokN</aDTrfp-sEiq@cU>3qg)hgTus^@p,t`Vui<bjSa*q\/)he&cX;-1>/fIX@cR>I;OK0!@LN
%\[4EbCA^ks)Y_83D]`4j?gKi8c3-InK2;N99"7r2+ib>gnDNI&qVq4JI/1n?93BG7cd'eEJ!df>B;m;GiiJ876Q6ch@J`;m?@7(q
%+fO^3HhL0CoCL9#o?R%#H#^=;4T+4*dES;44t"(mZgAK.ODOQoJ'GU^F1/AuDf;3e9ZV_!;:@G*mrWZ9/6lcrH#e('o5<Hi*hD,9
%\N'_?G'3c)%Tn,>MjrJ9CltnRDXY"]\gM-W4L/OFk-M+$\!quqTAK/!Vf)5nOHJpc%J\EDX4nFqO8g.OcC\?*IJQW"n"*'kIU8au
%?D>2qqG):=6qM`T$.2,$f6m0\N#!V9h*h>b*?=c!,?i9n^1*#r7h=L+FS98DonOnsU&XW0p"]onn1)0(la$BNnDU^S]1Xd=pZjoU
%I]a!ekfq!S4*Ye03OjkhiPemr`L-o!UYC^1hS$VTh7c+O0m(P6e>'@^Z\PCaGjCnn/,&@c&)MfN-?6)7K[9YRhld"N5OuSFI/<hL
%*aJ\bGsd&[Wr1&,GOP*H]D&f7j8T)QInP9ceA'l5\bp8&P5/'^$).uL/gX<F(+064:3)F(HW^#UOo8;ZkN=;gl;+rL?HFYJI\(T;
%hg"m0EW4FmT'siPHDN0bQ5%sN?N7]r-iO4l+4^\JIq6%f\,9le-oo!i&*7b4p1E-QaQE6g9Kb+/5C6sSCA]Mml;-iV>11T0edGiP
%rdDc3<pBUEq6oqbo'qpEEEdalio8u?H5h=945.eon@!ap['$$FhqY;/MjO)bYA6tc"Y/X,5I)=FY")Tqf)IAu2/bV:4+;>NBSu6Q
%p+VVicE8efmOm/#O$<G<U:g5Mpr2-<FAAZ-amum9nFPk6*-+ak:@["Mpra$sY^FUDFmE0!&+=R"]^apH`?.;Ajnd;QPQ1C'^T:0p
%q5%o)-iU.'4W:[drq22m]8KOg+/[(]kMgg\n"m^2n);fXJ*@I3XoE\0QbBj%>tCRMhY"76%,5*U=Q*t=^K$ViFC@g.K#6VrTscB*
%,OJjhk"G$L?*jKfI7W"5)ZgW2g2>bg6UT\]eeKQfn7Z,Waa,(0#r4WE<VJ\YQ!_.8XhKj=qVq&&`]&gOQB7sp9CrgY:A7+q_N$6]
%rq-b\cJo4\/SXV>pO3tql(dpk^%ob;:\Kd\Q4(f+Xp/.cUT_P>M[!(jeo(pXZm;E'`FWj<CPhB@]_BVBQ*o$mCqd=UMg[&X?_)p[
%Y**%o6f"pNgfA_7_V18j7G$s3.k2M<<:9a(]qa.E:[FYFbchEaFUb*NTdrm$?9ihk"V_Ts'>'CZrQRO1f"67H'"E'07&33D!JBgk
%[1MrFXTD`]k\Fg"bZkHX=jSQ,E0)-_IbUaeT6c]Sc9/F)\SBkYi#^-/6M^<Q;F["_Z71+oV_RF:.;c"s-tl:W`RI2:*-0!?]L&39
%aef1I(+DbPKLjNpC<UIeb>,o$gqFN@.RiR2(7Tr$/M7e1@"<2B'<Ym**%Bf$*,ETtSJ/#F:2ns]gAR"<r90]VNq_*#]>aSoYV*rQ
%@lN25$\ldh3B$S@+0*fKKR<CYn]&Q5Ia8Qqn+]DY5=eZOLTH'Q5!obUHaf?&0.`:a;GM8,i-[#q4)EV4cRdrOj.")%f8!/Q2\$q$
%h1#1lnEG6?"qS\NDn^M81SJ1PL0Eo9]R+`!m.&'fh7Muu)WVkBOI'HuHk`6C4k;FH`M3qrrcn/jB33E<;<+e9J,$iuj>_Ycjn7F.
%kJpZUlN?n,md=sG5C7S*WBK*aqtB_WTDN>[q2rBH4a6nEjjg-\Meda`6>Pr6o)J+^rQ#rXkOm:gI!'W0oH0tjOnm\6VOkaKSj)='
%EEb="Ob5gmp]$MilMBZ>i^J1XlK8#fMaoGM:%me3.Ce9MJY-4S5?]T(?_igbbWNPJq[Ha#!Y(\Bs"T5eLNeXUk"^3,Y.,13TGO%7
%0/Ct*F[>fjMo%]+p#=/H4Srk!]X7;.g!dR&a03;LpJOHP+.f(Gj^!&^FWS9,V#D)"B!'F\<hKCq;u,Z"s7?Z>pM7!r5F&#1J!mQK
%g9P$iC\Q&%DR9@bq+*io?XV[^a]DsENhNO5rt<)SH!af]If@:o/M[4O=/(9)>?gga(RqM08:0dDiNW%2-@PhZ:&=]$Bi6lL4h!SH
%h;#-Q$b<&3p@`O?lb63eNaI*D:(gYQ+5T,l4hl53<$"9XCAD5uQMAjGh@&l*2n+joT"!csqX]c7<u9choN!a0a'XZ@i(j2qe%iT%
%gWqP@eh`TTI@e1QmL(oDpne!gRY:_*n38QM+-._&l+tW0`)=96a3B[['(YS[/*,gbh8Sd3g%c#U9]jB&P85>bmE+$biq\LLDqc(7
%M,q3+D<2jJmsi/N*"Te2Xh2=Cn@Qr`m0g,Arr)6=dnb>ThQHLS7$VY]gg*+Xr!Q3kY>iKlHi,BkO2i&\BCpWK_3J_!\2+*UOW'-a
%BN@l(ZSUV!B_R:"G?Te#fos;YB@ib?48\s8nBoLEB2<?<mM@6$iV_j'G/'Q;?!TYM@uns-IVCb5*bY**m*VR_4rW>VjMn2[r7rDW
%\\(.qoCi':\"DL4*'QQ5\h9oCi:_AEa=/]nVnO1sDXTd$&V%K8=cO!k+;*8X0Db0NUUsG9`rG3c)`@kLlbd37E.`MP*M/'%o^MMD
%n[#:4BJn@[H">bTf5FtZ=s*dfd;"\-[1PW.5/"K>Zg28`.:s!%c[GZ5l^RK!Dk>deV;Lp]pWm^$MtZ7Go!e><eEq]`a,b*kSmL3$
%F`I3;im2h(?Tl!548\C8lLN6G$9g+ckp;fq5<-:SlKmcQd7nQWqhGuK/tY9jI!i3mbVOBElBCG]N"C0B]8PutFARb)_dq0bj*!*#
%q=*D.(L8XJN72u_Yo^+Hp=\8c[DTc@V9M=+Rp7uRnsBCB+0Ij+J&J,RDoH4-?B3mV]fZ/[GON:n(g.N\<om+pT$0[cm2nP./^aLZ
%*h2o\e]h`>=!(d3H$8k&<-]CrLVNi?_h5#5-\kK8K[UqdIOp<G'N7HAEt9<UI,<lSmD/_U2c%.O]Q88qV24g;<EMAIL>#
%q&V`sm#O),O"QGn2Epp8p%d.E[C^qgiC>4oP=)NMAb\Qn&b?^@&)tKd[%Y/"7q_?QjTR>>"`B4W;grn6peV&<NuBo@oWOV5O)Fsh
%o\1Jch=]m%k9Y?HpWLh:4/um0bEfg9nE_Z>YP:VK6>ir;MFgj16aalcI](fgpNs"^/Zg\s<j9R241uAif$G4YbC.-[V;:t,gYR7&
%Qkm^"^=0R(2=*ar`n-Ni9RT:q^0qMq?PM>Oo0<#?T)@`;5'8KK*q&4)\*_?,qQ]-BSb]B@g*fXAlAN=m^2I$3d/ZA>92:BD]?d(M
%0?Au\]^j@e(TS7o2b#uh-WE@NS`J:`r(ir4pd'Bt<q<MBI,gFZh2&8'/SS;Bb-J#;p6CD/cH\(\TDnlHI:nc7\?bVuXkNe/->/hN
%fjHQD)((GdL*<'M]>+?RA-4.e1OF&R?f'tFRPJnVh<Lg([VVcuYZPhpLYhTq2nA@/4aauQc#![]fT,kI9mj?3r#H_.Qi#[(<Zg]=
%^uFHCra5Du.Ll#h/#@^s6p5%I[e9aB<]UgWS&f=3ebeq%_`?X,lW;VCs$Olr]E?+\Jj<8i4cW/8n=hR44Aq86/0OEM",GV7r-!1i
%Vu!CXmmM+f$qqY"io,i=TJr'!_s&DsDp3mo?cM7o[^SHE?6E&0kl>k"?5,V]\_:D8(NNfJDCVDmWcnFuOGY]R<UVZXj$M'03fWEn
%C#o\&J%plL-6UeKk!SFr""DE6[N<+%+?):!AE]CCO1sD^OOK'HEd?XZQjJXNiU2>Gj3ce3D3O1c+\JX@!F[DtHRLRoac69U3a!RQ
%T%R<NUtZL\quAdlXFl/jpjWg@jST<<H8]pMHSVBBqKC=!]BKb-g!eu]&!f6rr>5II,`CR%&3,*pKaP<%C@I3b0(0t0fjE0\moZnZ
%-"WSGN-8F4ch53t6$He.^;g`H3UZp+d$N0e+Dp>E#EKK]I@gO4*<mIiQfl,F928k)-T#`+NWlHRIa81g.VelCWEo)eq,,L'TPoMW
%"Tq!>0m"ef+o,ALjgnmeh5aj9Y7m$YR/AE53BuEspXPY'F'/9-:-[!-Y8m_FW$%-D,EM?(8AN!YMj9&o\c_sdg;9sAfKRi(RGjBd
%;a7sVdNO!t^Kl$*Q%TO,5[NVJ/9dGX,D)NLjuW&fAN0jQBmNamdN_@kMHOjMqYW*QA(B?#B4pt?f53XSR_4"5W_u?jPa]7]U7f&\
%lGZh%H4<-g]&Val/HgVeRESJ'G]kdt91@^LSZETu54\]Bn^sU#N5*<HhnZGBLA3k*dr`;GrjX&$U>rZD2MSa,[.c,N!KPHs6`iil
%r3:Wt<TXeZ^K]#pG?9e"#-H1!5Fg$Ue&PIUkB)8E+ua]E-NCQ*BTbX8rh`B]BF_Yd3D^7JZhN7JeeOcoadi'ZUYY,d)#.Ef93@(C
%bm4>g_/ITLj3@&#kVo9r6=IX<g])_)E8nV;hW[Ac?SLIpp9t`sn8YJGB4DEp6P"2tbMLhATAK]FIq7*t.<0%&_eaVgj;7iB2q36(
%5G5L8ImU\1^Pi!m#W(oPKMJ!2Nm#>f(qE9;#.u<?WgfI)As"GDJ,p*;C\/@G_nH)`jj&</=RU`Y4YYRj^E]FAD\D5Ak_Bi*mQC?&
%4S1*'1K<8=HA3-,e@@d)ao8No^p`"V';Pn/6[Ac$V]e^jcJrZ[+@UQDnN4f08qh`"CQQYM4$5^#&P6h"H`##3N5=ZNL:b&mH(QLB
%)Ya9-e2uKuc;-%e<Y9/jBFIKsI-L\0U?l8"dm%T^jeqO_Sk)LM7&2U;C0B[e`l#+.48e=@#']Wf1<U+j[-=GQEl6u:EJmo4^X10V
%S(hRN2o$h[mG.rFpU7]hSV=Q/gm'=+6!'Q7rNfQg&9pE&kJ%u!*&RBZX5M;emq.>QNCW(Q4lgj9!"S[o3["V^>]+cGc$e",*_nZI
%+7b4,hZ(@kCk2EGHpk+AHjh3-W>k73n.+o7d_T7<U>f$/L_!>Q#'7=M"ePQR(!SOk+aqAV?_7<A2&VGJZcW%kYp)_tFJE'C*Z<$:
%YcmFUrQ-gu9^X@phS)39XErX!@q%ltr$!HgTT_b'^<q1q6TsT?!eVeKHi@n1M!f94W\2W2jlhZs8&3S!W#qPlo8l]fDnA,M;A1+r
%(Ta<[Rg.^Mm=m\.dZ82"F1Ze1<$3d;I@<V6<h<5QAoj?"G#*3e#<Guc]m-9%!/YN%G-3jYC7^=GV(q7&o%?AfP,>A0-kr0EQiGFi
%R3]YHfC[*kGc&1IqRcji$.r8Uo?@\dT'fhNho4^gm6rms]=c=:hO8s%"nJY=MlpI`@qk/+KeB!oTEoAKQu-9:=[-,,&DRcDg"h+Q
%*\I@2'`$'J-2RN3&7p(D%jsS/T([EmTmrO*WR(PeX#$5o)A5\+U\-J^[6X;a.!&`j3E9W?=%1uX'%18Do%W-q73;D.&TtI%_6LHj
%C3rIBmM8ZHU!k+12@A4ImtSC=FcB$?l!GZ6]DVrgC&4*;DqgY2S`_rqTj;<c4&#*FGmgu$:%Zbp(lHiY7-R?1[&CPIrI\,iFhO3;
%pR`&s2VWC83gEkBj_M!1Tg@B)rjjRjhV><Mb44i42@!&1=L.F)*ks;DiMn;I3W/I?Q+5:G/*c)\0=jqqCPS.KmrmXXb&Q-L/a.P%
%]V$2ED):/Ncg_UqJ>/4jW8F)34nUNWfHN;/oVLX?E'_2V\,(3K4b4rqP[VP1cUNE_!kLL_i"'u.RY\7%RlPo+O2Ti*%P:+>ic*Mi
%jlgL/.\LsX/4XEPFPP$!V-iXdfK#M$_f@EqpXY7G2())1>g>h#KSuMN0sn%U@6oIr:7e:W#HDguLmh)0cL0YiZTe=<*V'1Eo`GdG
%`b!bHL[4c6ZZY^H.J]Aqf8K'u=@T&&R=i<=KRl$`S^&Qk[0HjtIHcS!oYnU,K4;sOUB^-Sie_DtapQsPEo,37J567."C2i"iO>2Z
%/=s=G%9tR_:I)XmC;o^(m!'9q>q\79Z^8eu5quem5)^i<?P175)HU'9X3]gtKl@[TGplQ\"[Jm8b"*fljZ?R\GWmKN7\@-'4VF]"
%TCanX6@Pg)HGNj0e316(SXl>UNDG##Dg7*reFS[36u*?S'Zt)RfZgkVX5:<<.&N:$jk\B8ad!["rq$KX7*=aUUq^l&e_P'7msJR3
%;QBVK"r=O4H[>:R#$Rjs\)!(9<?P'?S=b-+@V0l67.g$ei#0G^/[f>r_>9fn&SUP^DO.Wl=kpc:G5_2Uc&(0PB"]'UGFR,Rg&/6Y
%5>1MpN;A!3eZq''00rg86!\!6Xh#*j2UX`$H-WnOj_7Y:[f/QL^O-[opmKkC/Hh\q5FMAck;$\X^a4sCE"C1WYK^sY?a$)+XX\U<
%&-8prUsTBtfm*3I2u\h9g<P^WY"X>?0Ft%=pB/P9c2%/\q^Y\B=&QWX&p^_9oQu?un*eZW5b8XJ-i8SVIj^m^<;.@%g_o6-9Nm(g
%gX-RbY&*nMMYq]pcSOE%Oaa>?s,K7oT)P_FgrSS[`cg2:[kALZS+7f-V:.i$qpEej;$qHff,d(kf.WQ$qoZ\4bRZeMIZi^.a\Ye3
%GNIII,2B71A]2XPj?;=4pRGrR]GmG*c&5O`eb_:<T.S-lr<S`eWjCgc/tRhtTrIYo,J9jfh4$!GZd@L[MiQ?F4n?8q=rO19Xe5Vn
%+,K+j1UU-\9;eQNnJcuLaFJDLGYXh'l/]U%V#<A&o5c$ElW-%<XR+5nZ;4a>nd\GrN2L4gom$oXZ>NmNY%VtDbC9%hec18=Al!4Q
%m]/FpY!;@[&8H:Q>\87X,NNrHXkA1obMm1o*Y!#)hV7FilD[cPLcWkoEhC#O`eFke:M6=4[/,,fXpeeh\>,0lE]*MaejLiN\89aK
%XQS9+pf]/m)h!iP16cFh^0K-UO6hl/+VCXDU7#In,>%<g\MA/eVMN"SEC9l2m67i%dU#0Pn`P_a#qC"P:/[on,f#EiYurb;:cofb
%j:57/p^RQ5-"6KR+3B-jDai=i[nC%EEdKfP3D>tehKV^BXJlf&0<G[KUQ!;gs5@W<5oL.(dUuL]`]Wq_Z0L:qX[li^BLR;qccW5s
%bf>Ag3?/,>bcaDN*/ZLh22(0KZ@)8'UXMSH.XY&o*9gXk!L<P>"N&`E3Y2k9O0pA&%:!&MPk6Y(7,?kX,Zid@[7\AADg2CAdbNt)
%`!9Ph(7VTMIm>nJ(2*9ieTF>jelj7U3]]V#N,e,)%!i;'206dX\Z(IT,/$-J2`#T)ZZ]pt?(/Z3pWhGSb.3Dk$VeDXnRn^-T],kB
%<^^7)X=Ip;n+R0iAoK(&hYih(GCqa6>kk8hO96f8Y63qjlUY\CjNoiDH$^ZE.Nc%.?)-U].'D&[]][M$=?9i+`*F"2^+QUb5C$a0
%Li(kk5^iFlU\i%H*]LGa_j!L#_hc8%aZ'"C)R1b+?.aG3VP*TjNhfju]]/;^/.'2hYsW2LFJFgc_'"8_ll4A(ro"83!npYI&^RKH
%0iu#gN^ge4C!bcsdMWD2?*k69"LWe!_17"pIr/\0SgfEa$;p5E#,%]Y1C:/k$'[#D7Sk<F[rFf8l!S\P\e&KL%t6^(X304sN4moJ
%Gcta;7JVD7'M'[DOBe=d%W]rRaNISP5q3ubX`l"3)LfW^]&D\;9<.PhBkr'8),4@^G,^\PhD`Te&g(SK_RMb@1M$,:<%4N'!DU3o
%3U9Q9/X)"WG1.Aqb;[:?8g64KnK:,Qb8K,,m9=I7:L2<C'<>*kU7hRHh\eQ1'$?ApOr;OMJ!/kd*@Sl#2\CQ4@$%FW_ss=f2q$3\
%rZqHO*J)qEaJXH3B(@OSNb)phYZ\F.fT\%=`;=)\To=.V(HErLrF\`%Nh"db9?Ne6Klo$+mO"%;Aj&b40(^tRTLr4cI''\pTcMB*
%@cND<;VD%'&K@n0YpXrBLLbMK#90#Gq*Qe0&PZ8b_&FQu%F?gZ.*70g"Lq&)jD5ZRlt^G*l+Mci^m45Ig`$$PjRfd^2a$W.pR?/=
%[\**BFh0eq[,%*E?0:5jq(+&#l*;<Y=l(%?Ht5uS-Va)E%,<`'*6CQ\\<8AuN8gB3D$b?8Yr0MDebj"lWE7Q^6sM:V!LeP&N&T'&
%fu@<e=#sdKI;^8'UD+e$^+:W?fMIkseP073XZ>m96*"6sDB4&5;D/3Vd)l^WWP]rnh^`J-U\![sJ52mi=:2=g=-^@#66$bP/6gk1
%90tY$kh(_@Sd7=6oa"6LlegJ\W;m!%>cg-"mLE?"PFK(_3X>d39+<TF2j6$3j;E>HY.,\AfPFS,U<*uQUoIt3.<csIkNqsrT$>3Y
%<oL+TLcN&jO<,[Nc\nLn;uBjMNR[i3TpU-ZUH^W_l<oFQ&1/9#Kum1V&Jd?MYtEbFEs:I?J,&[^=U$^]3JN#K-Sf]+8bLiOW&g@e
%OHR7IECKXo8a)3Wg>!"KP,!s!:C0L)a/EN"-UY@HP]GGd#dk2&;Nh+jd$Cl0Aj7@-I&3RR"KH!4EhSYB_dt>dd't6X&<.IBUS4A2
%(rQ/e!.t7i";"%?4ac5kR8d;h]W!)3'f;$c7RMA`XU--H9uW/)ap9tY0GH-E3\rOC6-sq1mbuPIDn*!)X)R3+k_)TS<@7hbA;p#0
%+/+^%0OQq]O)qc1T_To01*Rob`NN\5,!LqdrP<o?(Qagfgs#6AaWX7=n6K%\&7Kh)bLH%^ct:U1CL77=5sNjJS[B">FJ<"\13,jZ
%G/mSA0ehBS"bYe*!3s^P!=bd4Ld<](Y.C[6Fd^JV9ioHOBPH[>>,U%]^!W5]P)(TRZc`7t)n?g,L!?%n,d*OT`cF>EX?+TFl0It1
%UIet`FUs`D-\YTHM%o6VL#95p]D7f@$j1-RLn*9r=Ao6cKL]3g2R^tt-R0;93?[RYH>"9f`"k'<0o&c^j<F@?h>,\QGW$?p(C`E@
%6jStYIUfQdGkdhmX(nX11mQ25Mk6sfI+Fs_/NA3]RsKR7][4b_T6XqK',D\P(EW=jWpn>u(U.NU^?ulVl8!#A\r>!4P.[+(j%1"Q
%i9^/L7?aeFll&?e(.iSQ!uf#/G[1;l/=?TscCsNZprO_"ad[6VA`C.REM/)=gHmGp4NBjjDaEL%A<?[8!Hae`;3\F]CrU3@#/Ps+
%kF@&lM`.<#O^)5IA$OuFe:_e:S@V*#K)['^OBFj/H_lrE/EZW`'cPL;WEX5]\FXi!9Gu#>s34g`V_+5uR;[J]HqG_VQu3O0_*=l`
%AYJ?\I2?QV+[N,Wh98lc92a1:"N>("8%eb*ZiOYFb/Gd`T`&UZ*o_H9`"sJm^[IX!^Q%a5aQ@scjSu7d+/AX="o5&%+&>YVd/>@7
%?U-T^ZGaA,c%n.C2%"b75iWfd(0soM-mdI(opd7r;!c*d's#j3rr:,(h3[X([s8PUBmNgVRS#K\><u-QI;Am/*DTd6#,(!]6l!PY
%ctJJ7VX_?sp`FV<L(\2Tb&U%HF`soZUo8!Tc+MCsiE6M`UU-=keh:/8UtF'tD#,#:&,(RsI.r*F6SW"&0)Z*1qk0LRACmr79FOjQ
%261uZ;IR(4E\Wm37S15(Z&li^fIrDuCjug6bY;UE;R<bm'D%DrIlFrDf43lDMPW0F351"IgdY4>p)#))=)Ca'%&N%dfmL5>ae*^J
%Dk(r6e4$YTWf@Z^CVf:6gr%]AEe`nnYL>a:+l^h^)o)fX`*'j&W"r?o/'6`iP)S=ZNmn=@di!+m]UqLEOWHe<C-dP+oj2mS5@RCJ
%Z2t@8,g:=I^Fi%Vc_r]SE/XZTlB>EB;&H7r3F`U5m?YYWLp)kFg+9AEbK,3''F)*AZrDuLi;:67e]FNW>,c#Ndr(],6BZNh2R0Wp
%HijO$\`IS-<f?^`';mWmInQ?>5C#eKGe9Gu/Xr9(=slj*6Y>=b0glUpn^Ug/?WjhE_0O_.o\!+'OjZegco?&_`k%j?g$Y2mXBQEh
%1?4U'am:#'M]&;<co78l,6h:k$o6;*'7=ai;PL_IPY<nYbk7'rkV;nGR+nH\Lcfd6G2$oSeQ]?VZ[?@6$f7VjO#nB[pp+MB\".1o
%;E#U+WX8s:eM"n2@([O1<HfUJ.ZQQJ*q*+Z0gCg*8$;s"8mVA.L4K.^(?#m`Mg,"GOa'UQhhq_fOfC/.l4um1s,QBkEk1IO`k20B
%J?J&2dF9I.XVsmY[>d"VeTe!!bV_@mHr.*R;)P-S4o.@*nQ.86_W#u4.Y*t_9@7M+Oar?cEQ-4R[RR8hAm]TSa/T%`ibNcXE`JMe
%A>(lclN.oGJ`-^>aNc@X+Tbe)&4eP4Y?Dsf4R^Dd6ElW*JXZClP'i=LQ#*m$7GY]*p)NYmC3WU`Qn!rQdF&4bb1o/2bYdnpLZm*k
%M!oK#cn0N6A@s(jNbKeTXY&`8WqbVIDPa*@9,Z!c)k0*T3f?(D=`:PR6*>BA2mLJ8d[-@(;$?=-ljkG@JuWdk'NkA8_BhFX2]u:<
%8;]%5KGR'$FU47:*&'4p_49P?.*MG#:)]ogRsSF-O&MR$))@dK]^PgOhC-J0;+,4)qAXC:Hr2Nkin9,P]%'60RTdX7p5$I`/&Guf
%c+P)B)lQB6*=;5RV`uUceT5iVT00J/cD&!sf0*9RU6G8ggV$2kAI4E>M84urE]\aQ5=skPr*(rM)QQf'7mS&%'Cu9I6SbS]RLOo+
%$Lc,^>bW;oSr!U*`)]GF(3S\VV/(Kn?H*0,E;HYk3N_@c@N%Rl4k.0>JDJ_mMb"tt<PY1$`V@KHs.Q3Sk)BSLV9pP4*6:TIT'"]/
%@:+E=J7+(#:u^")(8GWFrOI<U>)'DGj&K6DaqImLP&iY<LurWq!.?SN,3e9cX$VVh1tU2mY,88=J(FdF+YBBVP2cVJBGQ#SPGhCI
%83Qi.]9IEb_krfMJC)mTq6Xmc6:AQ)K@(EtX[cO\D^RTP6Zt!Hnhr4:N]S)K#_H4!9Vt%<!=_e^k#HU"?HHa`d"Es8i%Er6&%JEL
%e$rD`RIMI9CpN]8hU!S'9J1dKRW@Ll4_b!;?R"N_6&:CNkJP8AUnV*s7_Eb-RY\5]kL0`N&m\^s^";!L(U?mSG-#@^p=>2dj1uW?
%";b&lJ[!4b$sp7ss6>ls.bdCKcNmUtis/5G36NYeG9sdr9-rZi-J4Hk[(hZo[pA0Q.Nd#iTP2,Y1(PL:,V?[NfOkgO\MZss_LDJn
%PipC!S*)*i6=YPtb^gT7E[&bPf!e._&BJ%',Sa"SCC_u@.LQta7N'BhLb6W;+B!bPIMiqWQYNAE1TQ,UR_9[/dH#.bL$^/U)#",7
%%W]N*bK0"6j!t^4%+(3T7ojtPZ;6GX9)C(.r<rF-8UXDjBS>\8\sl&5<@-D"1'u^47-WYVGm1e/0PM\fp?]-j+\1e\?G!6#X:?',
%pMM:JIP[ZkA#YCgD1c)6GjF9%Va_@[;5ac2G']G&FS4`&Ei_[]6t?pX]1M\afC@Q3E$al&@RfCC(jlEK>D"o4Y2tmqas`0cb'Fdf
%&SHGMY;[r"V,;5Y79C#B2:mHd`!plt1S@/F4XicunqhV*/j>1#lq(bFViT`Vr#BBtg!M%9RRtU8r6baDB/_27d'"481/eb-L4"_:
%)+th]UL+h1h[VMC#2&e!D!n3Z()W+gQZk3WEj]X7l#!%sI)TFPV+GckGFD@nDFGkg^m27.3.SrI<>Jm)(g:jE\25IqS0[.!fsQ5A
%LUSUdQ4p"*1Nu.`0Wog@ghQ8aMSdQ_D:4rQDD@U17_EoiA++NhmQOqnMABi$/m)=t2_T!sLJDlXfO<WABiCmP?0+mGDlR!$]dimi
%Zs6#t=d.95'!`es$)D%%.lZ9@_Fbg`_#_+^A6P"?flUA9ET=0+?@+9?VW-@gr;smb6-7bq$2eUE)%g'hEll5JNX0^hbW.nL++U$L
%]iG\FX^:\0IC0'tT<P/=Diqa3blI&=dfPjMh<OCtA2:'YU%fd143'4!>sW&=nV2S[fUVK*01Ys"ag>"a4P3r\EH5?Rf@k$9+Hf8`
%0IV1h"AmO($'jHrOPH;YmRo"V2]N=-5.]o*7[J"mRXG!W>:\a^!?M,d.;>eYYO2<Dd;9o%BV1+r(JZe7ed1jE(F<'(`i13r&b=9N
%Qf0ZL-Z_4s<Ag8YmS>GiMf(YiHFE'<+uToe]%nZ6j4<<!f006Hhp$K!WSN7jUaB%om#r3IGn+H#C0YjJXk=glG8h)MDTTRc#%"dM
%SOAiP;/;i)3+h"J`!k<I1-7=gAL;>m"inT@^10<44k3P%$V01fY=8b;+e.bjR-5PFK>,M&98<6"IIudm2)]Np)kVa]PW#jmqe]Mh
%FN=@Z!#X7&pfcWtXtC@;>b_&]i!K:s^PNJG3u?/rm5&1A/iiA.hl>kKK'f$kipM#1&nN#@qcX/8D:\p'k2*q(AU@i>XH<rmK)g%R
%n2udWkjSBa_<Z5K2i:F=k.n?)/$uC+1Bpd89d<#-'pl1[]TL5m]tDXnT_RL"kiRA[D"FTg@>teX##n:,j@tuc#Zmrh6u^eX+jOgt
%L"m1fdO7+-G_a^iJ_UKUp=pst_aG(\^_Vn\U5>C7L)?+8!k#j+D1B-Y.eCB`=s%?49#-><l6acKE15\*O:r<sf`=IhXd^j01U_0Z
%<hZhT_nB'?e8`M":'h?UklP(hmc/p2K]W3%AS[HDnP>?*C$^AS'T8-*[^o0=83D=3?$ur9Zg#4=n@M3f(BpEsEW%r`m7ot>HKrc3
%:UH&Nk1bLBhYR/0ZX8)8mVXuHCqWs=&"^W"G^a/coU9rL]m)gjTjPL,/WlCgoA<56s%]c+Mj(lf.4;?tQQqauq@_=Jm</<J6iDOr
%5BZbj"l\A*V'H<jI[#_XBSUgCKc%d584ieKcb'WT/*"8sa7s!J@b2_/$_&V9I"fX-RO.OLJ&K5$ELO*WF5)3Dq1g*Z?%jC@<WbY?
%1,p`FEq."2Q[e4orH)RO8lK\;g7b-=<3KHAKc,<fh]O8@O\o#Qa>-0a>bp6QZGVCHMLWXXK6.=ro])Q@7eDJ`c[lL/r>>=VcN4"o
%M^;sC(H)@64<TR$q&$:$61bgjeWNsom"-;h"D&u/c&5X-Y$A0/HQ69ceVelc;E9Ohh0ZYN;tt5S2VNSQT/H8i*Ue`sD!7SuIj?X=
%:]!6O;k8mN[.2r:!W\-T/,0YMP3isr"DLC0Pb[SREa]1dot$'%okP*+3U38ghmQRuDt8=NSOjCj+.5%L)WiDNU9OikZB:O&8t*lN
%MWrS[G3`-'T7=WdG7gFmbU._frVf+'$$Xl\?%ndrRJ_oe<;>Vu/8-<Gg/o#^A+?6NlL(*a^`B0Nn_2[4NMnqGB4.ajC`D(EZP0RZ
%LI>,*^n#TC6El!*-<dF&7AfnGTp!gtO0W"3L+Uf_f>qEYCnAaMgSoiA_74(Jk"%t2HoJY+%:,+"pK:In;3t_=6Y8HdNWheniX;'D
%aUt89FFiV]"13P9jTD-?UJO6AE5lsU=.V,e#QO_^?"C6o1N_7U)OK"mT=2pW1-DL!0]YM`;i#p^'2`$-o4H$<<`jcJ[2XG_i,DTu
%=:ne.3W!]I2!A`CN4Pk6-Osg/cjMR&FNKsJ=+4.a+[[uK14VHqJRo&`Il'o]P;9OeU]%NI/_k>))'t`tLMd9r8b=++bl85D467fJ
%hSG('$r_5!:`:l5mk.'@iQM="C=!)qGI.d3rUoQ9E;1[`<d%KS!2<W"XbD5?9!],OJ,"bFl%IE),,/:ZUu[2_0N_LB_WXm(#nu^m
%'4q9HfFfa.k@K=s7j\OHGkjq`j-:)4aSJ&QZXek)Y'R]T\=<"KiT[uAKQf0Z''-&p,,i1/+rK@V6nlY":N2UbL>Z@W2Bbnf*?doC
%$m?qL']H))D8JLS+>FHo91'ggThL"Cf7O_\ZDO-[eE9YafVbuA`%=%aOXPZILm32>,gqCKOiY+p16Lmj42bpeBd\cR5#%:<nr0NC
%o.3``heGRX3&_Ce)crR)kQFb=7l9+<!mrVPRmaa.\VP^J>Tni?>8&_4L2t4SdlS*K$^i2>^@s9i;t)&hY<h3,34S1:JOoNq#3.V1
%2cueDMnq<"Wu7G40PPLpc!:F>n+dD$Pt-\&JXcNl#4^5Y1\cCaVBKL;"UpOLqj/Ha/QW@;G*Y"]>Nqt-f1ZnfKjgQ1PaA5STm!A9
%%IYPNe<H:(6`37m%/gp6*rFEn@9/!]<lT""YUt`HK]Z[EnO>lWB/3ZCnC2-Doc$AE"nHYjGo)I1KlJ$M<GudaFT#]ZUceZ2.^,7l
%0i]A1`a!!c1_uQ,el\IJh8XZ6h.gat>D'1^m-Tb,8S)J'e(HaS0s8^)WGuj=-RM2(4>p>JZ&-TU:/4NZ5<CfD6u?LuR[l*1.(^d!
%0nlp%;N0s#jdck+<-IY]VM<-X1c;u>U8d<3I[\:Pm*rE+mZ474+mC]prZ`oeQn<6[3OA<jKp_\D[l3g_fkCr/>!h@"3Nea5e'ca9
%cN`e!MVs>Yh[P?"F*kIm1(WP(hE:7KMrsDOYd+hX*c&#5L*_buZJ&Ja_;MftMU\OM16ZR*`ffEK=\Ja2Z*mU`Y+XmOPjDV#jG^K?
%7bjnQXY`/+UYFY0>4rE7eNNK;=kS,EA+c2d9kuSt@BJc\AS<YR6Xab?B%pMDa(aBkk22`\<A*+4*Yo?p7E9!L"UUZ2/A9_]M>,\T
%F7EXr-i@nk4SVHOF^;9)Ra'^)2U#uY[Uk]aD22fU@(/Tq"1pMS/D>e_/n>L%pE"\P9A/'XT#K(UQAIKch$oLScU(Wb`r<@[3JUc5
%E]Q=K:SWP\.:8*SMHr2>N+q?$=Z/F`qe"?$oU!7g@khbI3Y%Y)VpfOU[%+M5krAVj6f4kbag(HfV(A0sr7fNM1[NB3H!^,&%u)ic
%cKqiZN):C^8AD_;m`hX[^/)sXKu)X0H>aN9f:^=*?*!t!iARd<$b+k1Ek2FH>B\2n?O_4&3Sq:3>a'/ORuP(-i@.RYa2nSNECN1a
%*o>=PH6Ufi6K?2=D%09((GL,\filP)bcI.;6\Ynlh;r\7.h7Q/2g.m&]`m;EnUAtCD(hh4kPZm=qkVI`#=t_(>``BsQQS)>U62NQ
%PQoeo6.E=I`0<9!6V07+^"U/!Y[X5&koQ7jC)_`$qG_8uX]*2"D>p!G1``qal-LckUpI?"48n=2,jF9d,1'[WUQr)7Pr6Icp+@$=
%\i$?56m7VX9G(m.Ru"/>;'=9+E:4t4A:jp!_IhYq#56;M3j>B'e"5AOVEO*I3"DJ.&.Df!(,L"hDo'^.g_5kJE]F9ZiJ=5a?elEg
%LjCS![9VtcrM=AQInB)E_9le=6*_"7BSB*fZYr/n6na5PDW;<%6aYM,$OnhP8o;O`/BWhl+oeT&b=:WWYn3TTHB^!R;+l<fQ/^8C
%e/asmo#6.&kErrLI(k8b!;@4'qVF`kX`%5L.]D?>at>W%2O7&3'udcY=-Zh35AXl3r^2`_"A##E$UeIq=eI_pU]WcbBb,TN5Is.)
%pcTB$3JR7cCMu6>G3Yr@E1>gRO=QaTKn;q3'me+cd\4rV*iVD<453rUDKfn/%__:6iPIKS%/1Ae-+r"+UklLAoL<ABYsp3$1D(M-
%(0d2?[h^aJqjbduiVQ#\9H%DdDXC.A='s1H;X(]KB"-hRg5[M>dHth%$M#ptX+Q8NF=n9.*"JCh\=]iQVRPS^&IP^g,ua;XPZD9D
%/)+sU'),QrZZoGkf]0@d+ie5UeU,0eR-t];mo2olNY]#j^MqgQ$(jAJjP43+RC9jS1s)&/OAC<cmCO&]ARd0c\6GccVX[Jtj]G:+
%Y%fkZQL4Bk](S.?V<:`sQa%Xsf1]jgFCm#QBe)!AO$ZuRiH(V#I^JFonu*peo:-hA2KAJ_$[-2_LWNPtZ6M<1J'RY/MQ#]*ZY1ms
%Xj-)-4R+;#S\eq6.eE2Vf*5l8I9d#F9#6]ep^9]`U^NfLUT<l!?W`6YZtCqe_p)\rF(cXf6u.0$M2@4c<01p.5Z8R_H$e6,/X#3L
%1De2Vd2O^A?>GsdG@[[QP2:5odOs1]<Pm+)<lJf5:bDN[L$I_QmaiG2]!3%Lbkc&rPt7m_X83WTXrX>'q.V6a+'[arL-8^+gSYt\
%A6`ntLh9"a2\*gapf)&.<cHdjDj*V=nF#Y3KQj7mlbBnmC%6i>c,Lj>BeK<\Fnmp;X11=e8WEJ)cR^kdIh(63:t$I*&C$Sfp<d^M
%\"OSbnt<)h/cVlThl;qk\^mXh'\[Aa2ms+4a1+8G*4:g)<f;1M2G30AhPST$SP:3<Yr/rQ[Qb:TSgiK6p;2r<U\>?L<dMf.Z./N7
%MoC-n=>0AlNdc6r^)kW/'naZ#qAa=Wl/%MG:$0@AJD:T8ohYI@c&g_Z)#T2MQ8*l7bbVrWj$Men3*qOgb_2[XoV><gF=)qEL6s`m
%[=;BPY3OKt@^hi39tPmUWqehkldm*`9]peCOIfOBcII;(8I$&pAN=#F'j4Z*kM+iK&2/m`pEMP!);\Df<?tCDXkIk@ZaSR5lT/p[
%Rd5Nk$uT=%kHN((R)Wal%gBBu=YO`OPtlP8E=s-So-JQIgfG`V1[GCe1YP8[Xno3\&<_CPGq<6&#k#Eior(P,VeP_dRtoK(WNs0A
%SrQ:MmBIN,>uAp#*2,_XjR%-?gHG?2Tq$SRO*6`7n>4L(_alG+P[e&@89]Z\=S3p5kUU2!c2"<RF/e-48qC:YOOI)T^)ijV[K&La
%J-VN^eH80XS`j`B>N>/sr:pTjUAcP!5Pcm$0,-"Pcl<9T37OD_s7qGPZ0TXEdgeE[.C[;3"5q@t;mt'[HbPg[r-u6[m\<C3lp.K&
%jm7bXVr(po?L"j[3g/_'/A,U9XoK%a3e.,p^<#NagL!1jgJ-WEUR@<Io0tAG^SH2>^#`VG)o6_tpkNq)VsK3l+?q/84A2pno0Ra_
%;PYU\_,;Jr!D+>eVAYsp(G%u]4M'HRJOC=@Mlds5KpQ<]\jV/b3o(-;l"ttU<[S0?r^OXXE&EEZ^Kr>Z-3*Ph,%$(S_ThYDJ'bR\
%O[t=YEAP8kOa(2W=lEE:n:Eis!>dk'YTA[eE&6[NH&7!,+ZRKZP!C1.-#$a79m_*b6nKsK9VNlV,=iNP2MiDD^C6bc-$XZc;]C":
%"C)Z.&NGol#X&SF5Z^j8;/$4l!@Erh0ih3EYWRjQXjDc4Kbr6,dK7ESGsk\"I0Dlo]94j&+<=l:Gn84G0TPl5kjV9q;e1,@0IqE.
%JY@W+b/9%:LmiRA8Q#k,J--u6OC`u*#m'T<NhLE!)K%;a*";rqnbcMs.&JEJ(KeIN&uW/n/BA8TMWQ?RD$&-_?beq5V\W>!.KM;*
%lcLH'cj\jq.GTTd%[3\+_*`j`W3[$\;8dmV<M2m9K*1e0(dQ9#J-.Tq".Enp_bEBE)NFog6rB3[MtkIe0Br_h"U9M`//A8=)?hr,
%`$PKs%uLKnGZ#ea;inWB#9!ijf_EcW*69Bj!sB[t"'bu"@?<A91lhc8K'Mh>O3E3?(tC0.;;e\COMGNHa;,/8f&VI&q2YGbK-GRq
%$XaOo>Vlg#]!.?RFe,5aRZV]oM!#sLi@+ge"dVUKoDnQsg-d1W`5:Iq@'_=9i(BfL&-[Vp0rmD\R]%];g_JmB$)5Q[0GGRL'MY$^
%YXVFj<]b!3KOWEh<,2PuGa;4=;M[Dder!-*2j(,"TdEt1bGdOXRTMj5!2Q4q7`$B=6*mCQmo^).WBEt4T`:5\,7+d^l9DReJ_%o[
%[R6/uMW5P]M'mQk":U57-/;SkYQC`r,rtH%,ce@O@!F?'M<73'[<@Z6%Auc5l%@R*+q);:=l`^Fc,W@Gksu#;L_j%"+HR-Bcm0'K
%Ypk9#TK,<'%gggASIClINN<2B-48BhJNf4WYbU,)%e5Qj!/f1J*Ln^6SIC-Q:SI"SI5JqEL'>gOmmIepM?.)"EmffdU;ZP9/#-iB
%%=AaE5["^UF/Q6`_bHe8"4a6""-QE]%#Y/&*V).;=ctl>'krq5%,a:b$.p7#Tfe7`N$XT&7ck,c`@_]=!;*=,Jc$=D24bOGi9WaF
%To%rW7Wg.:mKs)\!irmG7[-m*Z7#$o7[?=M"gtSeAYCF_R1F&lZ-tt0L]MDQXupZR74:,l3CuJa61V*FX1AQ(Nl(dP"4s=e)8<t/
%'iiQ=<e4d\5YAs5O/rrJ"bY&ko7m2=2Y^8E2LAp,g%Om*6k$[TRR'Q$rK_u"BLu+8#IJLaX)#sCK[&IAW#%m&%uK=GrsFd@<L6re
%<EN``NS(*SMUbnW):DWFGa5U+.2h9D1+]7Z8lu/!>/LD-X,A(=iJ!,@"V7dhd)=>EVJZ7aP!D5K$DHQEg1h(WO[G$WG\gEgHtZsm
%?BmTs6r%%P^u?JI!=oD2a,Aqk;5Y"Y6/+BLQ#L+QlB2?&Ap>kMD!)dj\Lt1Pju,2A8HXkte[8b;AP?BQ^u#Kn(1#9<MhKBl@!J]E
%6a0O?joY9G>/:&iW.bj@KjeA7<rq7eRP>qe`X&g>8MmW.?Xg9D5niIEF>>C5"U+GD('JSe*+fPV/B?]d$2R8YejQ9%5]cQK:J4kA
%a:V'G@PEk:M*WX$NY"`YlcW1pbCrB?,W.Onffd<T*XWR4'b=OopcE?SLHt,EXDeeQ5Zm/o(?mkJ2%kdXTF"/U8/=^#4KU%B?JTR%
%BESmS<PYa;20$"#S!Xet8J$Q=EWUV^7=/4A8C\(mned\A1lOb,E9O3g4Z&94^mlc&WLO2N`qkA%X#c0&:#)1tC7L=<T7>?eW!cJi
%nBX>o(RtBbXmtQr\Yter5UqFC!4Vip7^Et\a0uEOb7`nCP<IuY0DNHOOPQW!I\R#6pP7"2oWk*,r^&38e%#Qk>D:"fJ,Bg^Y54s~>
%AI9_PrivateDataEnd
