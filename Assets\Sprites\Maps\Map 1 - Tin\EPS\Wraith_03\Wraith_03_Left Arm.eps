%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Left Arm.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 4 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
31.9238 52.7373 mo
31.0029 52.7373 30.0723 52.6494 29.1563 52.4775 cv
22.9238 51.3047 7.70801 47.9336 7 45 cv
6.84375 44.3525 15.8926 35.6572 16 35 cv
16.0742 34.542 21.1436 29.7969 24.4385 20.7354 cv
26.0957 16.1748 30.1855 11.2637 35.2451 11.2637 cv
35.5137 11.2637 35.7822 11.2783 36.0479 11.3057 cv
42.084 11.9453 44.7861 18.4385 44.7861 24.5889 cv
44.7861 25.1514 44.7695 38.4541 43.29 46.293 cv
43.0938 47.3359 42.5703 48.2891 41.7959 49.0146 cv
41.3896 49.3955 37.6582 52.7373 31.9238 52.7373 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.651301 .62591 .384436 .281712 cmyk
f
20.9355 35.7998 mo
20.9316 35.8271 li
20.9355 35.7998 li
cp
14.7188 43.7666 mo
18.2148 44.9385 23.6963 46.3623 30.0811 47.5635 cv
30.6934 47.6787 31.3145 47.7373 31.9238 47.7373 cv
35.7725 47.7373 38.2734 45.4619 38.3779 45.3652 cv
39.4912 39.459 39.7861 29.1924 39.7861 24.5889 cv
39.7861 21.4297 38.6504 16.6094 35.5205 16.2773 cv
35.4395 16.2695 35.3428 16.2637 35.2451 16.2637 cv
32.8594 16.2637 30.2178 19.4717 29.1377 22.4434 cv
26.2451 30.3994 22.0752 35.4277 20.5059 37.3213 cv
20.3633 37.4922 li
19.7471 38.5635 18.4893 39.8652 15.6582 42.7939 cv
15.375 43.0869 15.0547 43.418 14.7188 43.7666 cv
cp
31.9238 57.7373 mo
30.6973 57.7373 29.4561 57.6211 28.2344 57.3916 cv
3.73438 52.7813 2.73633 48.6445 2.13965 46.1729 cv
1.40918 43.1455 2.625 41.8877 8.46777 35.8438 cv
9.60254 34.6699 11.1133 33.1074 11.6914 32.4424 cv
12.002 31.9102 12.3896 31.4424 12.8076 30.9385 cv
14.1934 29.2666 17.4395 25.3516 19.7393 19.0264 cv
22.0479 12.6748 27.7373 6.26367 35.2451 6.26367 cv
35.6777 6.26367 36.1201 6.28711 36.5596 6.33203 cv
44.4766 7.1709 49.7861 14.5068 49.7861 24.5889 cv
49.7861 26.0469 49.7422 39.0693 48.2031 47.2207 cv
47.8086 49.3184 46.7744 51.2012 45.2148 52.6631 cv
44.3115 53.5098 39.4229 57.7373 31.9238 57.7373 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Left Arm.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-#^bH4iTV$FsD)$,o&VoRe7`cPO?BrpBS)426(7h5f_Bhu3-5RqL'^rV#mGjdWa:rqPdNrR9A"5C<5sps%9g
%h`lVdo$(OEN\@"gq`";qiMZLPgD;N8hnFC5lKPp]Ep\Z"^S3C]k$.Tr<.I1H]mfY'=$ZWTo69Yr-Vub0HhuFas7b:qOKVs2hA?)5
%]3[8ji*DAN?b*3ZSpo\&IeJtMp%tB2X8i,3pEQd"s+2V5Y,,.nU"&L?`#h<[)/Fg'qpOu7'u*s&I(A1Eg\?)Q1&-e%Zs!MfM/E4V
%-f3CS^]0/&3rUDc4PEC^Ff`-/kXr.9I>3MEJ>[hcp2getH0e],c1u$KB*V4VO&+AF:D]@,9_^t+hMnF+KVuKke<OR*qq*uRpH$5n
%>0t;Wo[jnF<]u4eqg^g_\U%pn+&D[#0RTqq/1sl_0X<Mp3MkGDC`Cl'nZ++S2\V3J.cVh\O3Z2oA8L?g[ZhuE)k*roi"nJ(Z-csi
%@,L;lId^tl8h0qCn3';>U">O%Ma.-[?TnGLMf7LdIJWBNS(I[6h`UhDn=ogQo5C5k04:3a8r)t)puU'%q&c2Wp[T?.`iJVDr:o\M
%=5<R(^-ToNoI]:l<M6NW.@s0gJsIc*ru7#9J&7hq<P1rZc7aZm`\ml_:u#'nnEV3(n='WDnjD[fmB*j9^3TDDmD"(]^Q7oIIsCtX
%S!Qq.4f?OG6FtJ3gE,:Vq&fGeCg_SY8b"Lu=&5iKTe9ENoC/BV&)gmdm,/K%s8:mP?[dCq5G(1=qY=?("c'SUIf8:+K.RTqU*Lo]
%lFWD/hu;d2Mg*NTY,:earTqe\cX4ONN%a)&HqIXP)@$&Q4RiqXhu<0A45a[g?@IYCp#E+8oQNP"ma^.Bn"1iBo$YT4^3T_S+5Zp]
%\qup>$1+*2+0Sm`n5d&I]2(DYfET''cmEJXV.-M0ld4?9:>mjWMuDZ&Ma+q#rSo7h_9h@MKZ!cegD=3-#$8]Bj8W!4f5:B5ooJ_]
%hYu?CkHFO^I/*"!Z\_\nl_"+ogV.t3p$'^OS:khNT1@N@YCV,9I.Kl(mJ$qb%]]<l)gN;FI.d(in:_[^Z/WfbIa5]D?iBQWGOWej
%J,AB?DgqQZ%rWLRqVgE!2uiIHlL)XWk?%T)/,JXoY($.AdCXmc3V&eGrWtP!r!DitkR%5&TOmo1<iQ/6o@'n8594CUp!X`7r9-9q
%s8(WE^O,[P:MQB"po)bnqu#-Z2fE+bIsq!JJ,*1@#IPT7G!,f@[ZHfKh#tL=!IoU<^IV@RiPu`Jr]:3FCIb(9C[WF;K=18NB*H"B
%s4"e*npTGN0G("2Iu5jtJ$<;1NP8tb09YBBi:!BbT<>],S(E.=rpn;mfKNC6O:M3ZjjVgWhbKC3na6>epbE7qNSB]u.tD7Tl&p&X
%'`APU)*@Eeh<uE7B4(_s^b!1oo4CA&pOVR1%r1Co]0Ck2p&+L7hgYFcprZs>;8L#R5*#eb#@6>T*n%=Be!D^pr:01'DuTCgg"#W[
%GMiDal6"lqH2/==)5^QbR>TI*9<ut;A\?<_9JcYm]<MPi^"qINl7Vf(6-SNh=9%jRjbn%a%@33E$]NpdNLa=M2oH'u[CId^OdNr7
%_uVu]#(7lZ0Ds@.-<+d=-ZCW"d'CQSo^qO_(XHDOs7Q-D^3fj`]nY^mgXj2bc)^+c:0NI+_%iL\2hp=`M1O]Y47khFnmCT\ls$*+
%)p(rUK.P6^4f&M#]Z0(#resn/qB`eWLhYbq^FT$^%ip*Bh6'=n]=bDi"BDpSi0T,NV9/aph3;c&Fn>86OD-<eQM3T7lb>Oc_<p[0
%^V&`/_MS90Mq=VS>lFN2([ms%OQMnIOn@.iTjWnMVF*f_RMtS$8&`4a4igu]Hp,[!:S'[t'X*P#BDMDmh`h#8"c2>+L9=L;9I_UO
%$EU'jdhN)$<qmQD6@Xdq1K0tdTPVO'p]hk_Nk^A\JJRTL62nOiOb.rXqXL>1I/N4.If(U<.#32GEe8=dEWbF33%2Lo!b57dPYrur
%%]."6"-k*q':!XL.6!;0Q!5fi5lm7b"t5U+WYpdR'gI[MQZB#p.&VBgEWbF33@_ao7Rhlg'!+JMWF[Q5Wl<;X*MK"W\_XXIG2$<s
%4n]"3><:tg^%L6CrRB0lVV`HM_&pN;d[pXg_i$gY%dhURLVo7s$-N'_F1B20FAVli5`*O'"Jlq7:P4H*;NK'01d`#9k="EZ]TKt>
%gd'iuhdSUVP<XURM#ZY1W<=c5+8nD\CXBC7]fW<A8:@<5c$cBo:-I)j9(/GQ#O/)Y:j]N^rtM.P.[_V(-LK<Yo[?)5;uuq^T($fU
%EWbFsm&5.WJP)Yg$a*$7<;N"hK*I+FIQjBBcEOVDs%mU4khf3SS.lJ;r`0C(4oO-LKlq'gB;kb7s$&hsghqlg<;MfXGNg*@YFb_Q
%I#HH*F:$jl#]Es^*"n2;NY'DQN7j'-Z,6k-G_a2=cC4XSTDt-5#+'j[FBEoLmaf:5ouS$-r;d\]D=A^%$nS+4Z4&\N2L:J$'!U@6
%@=uE`4ia%:P?dYba=4$MC4_EQg[YKjjX.fWAs[oMi?9RV9?:)"K+[8jT&kfA0u%JCUX$`C!cT@](%NI+4-(K6)r`Tn;)7['hN%W<
%Ao2oX(8rrQM,9AYGRsj:D_S3sR+E"rXRk:=T7Os?R+ij%lPoU+c>E[6I"VrhEuSPVE+WN8@:jFVJ2o28RkU0HD9NmMG)'#ZQKG9a
%C`RMei7pj[6O.!QGT$^RQNOI[>N4_S2rN[#i8D)[]-D92jG'SMNR$6pKuqWLkr.CQ>CYsVDJsE#bFa<i*H(^JK0uNO0pc:Q"lp<7
%(XBl.PRE_\s++80cG+qDO$!@AHb[Db0<kIrpBTd0YkO9DT4%C8@/i/f>Qm)P!q%"X'0A06DLf]rJC24=\9Nd8=#atNS2kijpk.nA
%E$Ck3]oMT**R:Vk-WYs2&:8pdGAs#,!0]MGo00r>D%5mo),A!"@'AO7=`7`ec7j-+(qgDhLn=b',f(?<?t:;EK,YZh'9)U8cLM;e
%T4,cjfECg(nh$C3B:'F$2:u%Pk,?-lbHTkh7"i8:dOjh(1X+hjp17#/p1T#jMB<D?@.1[O$iB]-a2KbS@/_+HJc]]g:.eV%!EOp_
%\$5nYE2Wbpe6/R&N,<PPT-P^d#u\DMVZlq:"Xa!6,Q'e!Q-7:,*/NieV+YN#Wg+b33WM$X=7d0."=`2birW`,ji5GG$7m27&NWtQ
%;9H^IQXYPA'N#4WP04[Rm07rON$F<<(N794J[*gI,=N+(E3aMKEkqLes$Q$K8God,G'eQ<L[,8bKu7a4EU2ZYT'\YmCQ#n#krTFL
%g:[/D_nLc:2XS-bqsK)[*NX_EG9=?\0'kT##I<[X4\)iEY(#ljnLZ0j%rh9RCCeIi6U.0gcu]+"]J0g(kRuQSH0;CqFB!Y\C"9=+
%Mr^8Ig;`Yem-^Gu'4"\AKM&CBmqX)u>K1$0N]m#m>K>sk#']'O*f?X@IXccWY^^7rYlBMKBr)5]>'u*Z;Kri)n0H."WJ@>pH4d]>
%.#P#S_?(uoZ&8m2Uud*DJf'>RX<9tFaV$%Y\7LD@86XZA(uC.Kk"H@\,dJ.7XSd5;EtJ#*'L5Lp:]^263^hruR?YMh@RPq;NX?qY
%J2`R!GTu72eAHula=:XGh7&gcVWnsH@)-SX"]GSeeLjn4<6YIjCSf>$V+Wg"<'5"d<BXCWDG2?FKb"MAcnL^5Q/Xa'IbiMf=ZnY8
%&(FBU5XDb2oku7/=VO,$!kYC]a%>q;j\Ld_JP^m=HRj@3i-D7.@@kt8SpXNILN:IniH_ATNus2ka<t/eP92U_-tlP0K`Hl=%Mt&s
%NrENcZ-ncR,u\4i,17&=kSSr)N/fYUb\frb4jfC8UcQ+-CiHaI5727j6t^obC8n/'-TUHob[a#'O#oMooZ34`O-F?Bq"3e!_f$NE
%o)UfmJ[>,cLEN\/"0Jd"6+D+Q^@*a$0bb,9#9JXN@;cqV5#glQLjUQ9h(45mV4]c*GdD?J7(PsWK,RICRm]4Lh%g']$>Mpn)hOIa
%@rN]_(hVdZ&A(?c)Bor#cTn0Ro[;A<XFreZ$Ku/r@,p7b7>;0T[k"`aS4[=:a=*1q!e[)_2?)B5;+h77d\@MbF`e>])MrTHT1jS7
%C5fSk[_$'gT?'d4IA/elp'U;D>MCa@?W];;*Q%rQmMXb:e%T71nOOY!_[b\>1[cA#*mr%emi^IS/4T1_ReIE],hu&2j`rea:s$>+
%pQP"nfMc"*lerIMq90IF;$ghVbiZ',Frb!@%]^iu%[Lmi]5la5<NsHaD.U?Z&@!^5?E#EJ:mEB$.\mS'q)no$'P^UE^h#9WRsp+R
%YZ"N!)d4<L*2rCDH6ANI)%Jh"QC&G'Oho38W;`3Io`:Q*ACITt&6XoSR"d,3"PNXsU(Q:?[]I%r%nID3b^-b`iQSg$0QUjmbj<Zo
%reW;MiIJs:&8NF)r88_skl>I/J&3sf5Y/[:S!592aP$m9rf<i-(")UR4;!6Fp3Hl4G!lsUdGN>?PWb-WH\7+6jN53DLrIt8LFMd7
%(0j-j']4V"#u0Eo-mZ$sW-V^)WK%I.A3;t*Ab'ARYr]-L3Q+H"$ARb(GmeRVl9]/IW-G72M:jU9Q^`opG$U8t"4s_WJZa]?<gUHt
%QE,X>#cJkfe>nSl33AI@=bUa:=,iP@(RM0-ekHoGV:t2pCj]p_7mi:8`mge,CFN(MGs0!$!]?s?#1GM;2=d`75>?Q?aa@ekPU0UD
%7p]]0''FE"c!-LP:KGD0ek+UUOoskuSh`g:/W.<%4D*_3l_5HjRT&)].n@ud,@5;*>%]dXcV.@UYkp3Oa3<j;.Yi>=o&O6EUfsi_
%8ub"h1kOAqC',sph%YQ79eZ_OQXF-u#J8+ZrU-AU]Ve#A:h/?2BiEU;A\k$29TfH7HLlZdTBk&2-nXJ#Z=5FWeKmf&!b'1R/JY^o
%-n.U'/eu'!r/,5Q'ST;H4Al!+?^O[0K%W_KZ_O`Jf!q1UBpDuH@X1KgY!k@Xe3D*-gT:a"2tSRI&`JbEg3+.jWX3b""M,ud+tLp`
%PU%%3<4F0"]a/j4iY<-u'!;))l4YUCW'\=)dr:mHPrd:k@<JR_+4$`^Z!%lub<&V)=M6cEE#^5OMli"UGKb/H7,$=#'WtYDL1_F?
%UF9Hd"2O3RUk##ms#FC:LJ:&X].ZdG8tASZ19=,`QoEuSrS9Nl.]D*\l^ZkR&g$X.c[6N^]ZiW[S`L2TZ/2NpDen:D=TdDY!'>M7
%$KcJM6`RMrJ\>&;ej._B!:,A>Cg@4W#2%h^(+UMb]"O^8ea12!;!>i*CGa_>bp(CjBsgR6?$]VMeKZnedt\?Q/b\G^69X)%QWECZ
%SU0TBJj+'he`n5#Q+2t+Rn3oUp",>[HR=I@5cS_X;0!cT\[fI?NO,@l/Z5.[gm#]):nt&BF(pP1Vc'CTVd^,h;T,ueU:VMI3LB!L
%oQ-UgoolEBE42_)Y0+@+n8373/^L`a\`M?I`^[PYq)"^9o4c^P;eS@Gk0jPN&Y)mA3dVY*Nh&1_RuR$:9dYF#Ac^gT^ic@hfDKa9
%/5M]41T&eB1S$58F_:(4Yk)FI\h"6rYf<duqWC79gmJBs([ND][70'2N]nt3*;hO\q0;DOEFoD:4+k6f^#9F6+h]0>8HotOD`bJ.
%)SDE7lMU'RL^cV//8l/V735,%^QInn99^6RrI$RVI$1+_C?>b=HSYT%jm^Wkr+>M:if4kQhm(`I>qc[ieP*`qpaZskiXKE=%s-<"
%C77rNENTdc.?#"#X-_D\]Rl36's_/ZBeeN:$$dc%]:I]O\Lb`CN>\mWeNM58'rWj(k'aW=/siEq^<r/0KK2.3k3t2]LKG>;,t/W5
%ec`9[2E-h7:L,e*`>bDel#^MQa\84H.Wn"UGm99l=F%5/8fUa'iH(,.HCrtk#LiU3@hnUX(5Ytt1G+LeLA/qlr(`["FD%EDB4n.+
%mJnC3EC-48?q4=\B0$TAK-NcdJ.k).FfW:qho'h`'`/TtTu?ZC(8jRoV.!dNeg7^RO``sk8]-Ra<W]N8R.U_#3\XUC+.3:HAI$jk
%OGZ"Ng13W5$qgRk8>h_15jV*R>B3dBA7l6],8eB.)il)_:O0F^k@;e,5<1G'`q?.1`(<VJE^$]2k\/7-e^75eLL-B!:gJV:r_kD>
%?$u"s>AEAMj33u5H%+_n>J'I;C@"d$.2F<#6]qmM3\:$@.V9>@S!hpU_p]_s2SYJjZWJ)#*&2$'EUml4PkR_4dn-^Iht.QcIk)>Q
%J+nI[Xh1H;pKh*A.rW:2-oa?SEl3B32f^1nV'Fq"hq<i5(Q3_?GUGgkf.A<>COC:q_GiI<DD.elLD1];dK*qJ7d\e[LT+Vks8Gcd
%rpXer]cYe&V8q[RFm,OeQ>O]^],^J4S(Fj9^3]eOeK9KeKo$B7W%6*6!dYd+#Ja_GKUSK`$TK5Yo-]*XFuqn[n4;g]Tn@ukC.C9D
%DWTf<@tU<cFC[3h1(JJ<giI/EFrQ4VFCa$DED$O*&3quQ>0k":O`dG*huru'_+@Wtn2MYM`;m#1i+.iJXdP2p'MiY(7;$#RaI5n8
%Sk"OZk'1FT^k@7BA(NNC?%Lb&M;9AV<ArTg]b4g$G"Ut5@\\g9@+IncP8GW&8lR.&5r*5WH@S\F&hQN\/9PB=R11h0V`Lds_^&.r
%_Io8FFgF+ar30$p%=TIhML[0q5K,^S(S9>(W6XDf9UT02@LU*lV%mfd7b-_7l7o:#CdlgCVNW6DQiMmK9W"s@8*Go1_(l/k_kaff
%B"4NISB.A$l2c<,3N4jqAYHE1P!4XpW4I#HO9dt?.g89#Tpjnb#;*2AJj%!-'m^*^1uOpcJjUCu9N2p*1t!e!it_uD_P.#<*tVZb
%)8X\hKa+_M-8)\$TMb#G13\I9KJoE+daulO<CsK)6WF]e1)m6D[DDm(A?ItVB\#^V;cF&J8_@9*5a;T,iA2At(uF:D&9^Nf+:fV?
%Q+0E3U`X`"bG&&PfGO**>Ul[D']<9u;>"Ij5ERRKei.94@.l*!s#iXQISk\_oV:?'ZU+7%XNtj6"RO$>;!SPE&&qIH/3g5G.Z39+
%;2(JL:.g*sNQ+6c2OJ88a"5(T#ERpXeiu[@j=h&K`u5tFT$TC@O>Uhg<;[/5Ak3Lnd7E;"irn@TH]mo<C#Xqm7CsZU#K6dom604:
%E1+$9)(c%L$TM;o,'&Eu`X/HHq.s]`%#uD1FM8:J9ZH^#$SbbW]1DSoU]?$)Xp^K\NrV5u#BPW*q[l*V4dbgH/QeL&FiHgd8lHU"
%f;sr,WL2Bt>@:UAK>%`^Yql,6VW2^*<EMAIL>*XV3`<f"O%iL]]cZe0\a3C:i;fDjD:Slf+6t1k<]+cTs+"@:[*:0#E-Bt,T_^:\C
%'6/'a00oTVfQ>-S:_4'a;i'JBa^CbgK-C+PJZC1e73t3^!JQ<rRgtr[JSI<W?OFGS%&-$_e7;s;'8$[kM-q9=42<0I&(lC7'Gqt>
%J5r900R;1P1gi(^+bk;SlC,q%;\3d^*4<WcDCh1+M'5O=e8T_H1L$!PC4gfLb[.H,`*-d8/8t$7R.IAZXqt5f62_K^)[QHke\VYq
%Y:_\)p]er:'#*%_TFX[9AP`@&nF(fKWbnL`Ks[mLKsZZrG1S`83(q@g!36i#.e$BX^o$V!:j_r.Ir"`\i"\E"5:n%9;#f\V-_c9'
%0Oa.4cg^42s4&PH,]5K\[9SrtN"`peKS2S=[LlOmZ0!5b_:j$@Z!CL)c,1^d,XprFOY90a1F>Jb0>V9t\[eWn^9;cLrORauP5%l9
%Hg_2S*1mJd9dT.j]c'--ZWD&fH1&#)^RZ8ZRR*%N>[5/tjWA`TXlT@O&q25&3(s$KDCJO$*t'5(5W+aQ<%0Nu?mk29:/Z^q)Yd6,
%;oZ>jBe"j^e<<]]4.0YKbH7>:"6LKgN9,$I-'U'h,V`G0\4lJ6,aB?-Elr)r9Yu?!LU=C?^7$3rpQo$gqWP5Con==AM[06co!_QX
%[+bIL-m0\r%E$H0qM-R;=6tdpB-7Kk!QV_D0TA>nJ9+sM.&l%D/-T3h/<?JUFsDhG!^SUM8R,P3XW**r6es(]N*76B'gjS#Wb5'o
%QDCr3b7Tjpb>AX=$H9o:1pr?<4"QnN95!/9_.W2KKbJC7k8%97jcZa8ZA;;G@b0uAO$ECa^uY#GIVOEP5Ilf(T,R_V;GCh6L^t%,
%*`=%aj4'!6IfJqVrAj`Lq;;%`@#Z[mhL5me&jA80Ya;#'(&\fX:q7lSD?6,oU-.%]"<MP%mm643b'O<d)g#r3%r""i#CN2aO85;!
%(T?kqhgS8pmp+n"P%6,mA.8@?>*.9h]S\%*OMh8O)tCjfI,m=TEfQ:;_BV(`pgJ^N]E2l()/M4(g/-f<fH-.A!Sa3^gS:i7G$nLJ
%ZGZ3Olk`<A5S9&'e6"rHPeA:?bg0DjG&!OJq\^`n_^a!:^-/Ra1JE+MeXU=g4'9*BnhK,r9t3)q7<2S>7Onr"'mi&<:ckf]p3-p`
%a3rRZ.A=r=dm:\XOKpb!@L9B-GkOd=((6f6*,:&]&;AoK.HmL^0qVHI%M'(JaB;Nn(o7J'+fZVK.Md._@O>+XIoqrL.;^\6U]+Ne
%/J%hGNQ]X33:-g*H"K6Eq.nNjKQ0`dH2VYi;g"r6N4/$h>\SlOW?FYf@`O_s>(Ft`%s2Lk/<`2tffS?[cVkq>Bebe+8%apMCEF8h
%rPHkG5F7Wo\m6g>OB4E8aYk%'-S2UKOtC:`I],L$mh[iPNFB8tOTubb0+9AA<=>5Ia4SG.Sg@/J0ZFa1s6)"J$\S8j%g4@FV]3!&
%=O<>)TBig>=;HKc:6\G8:HCXYf\>`BgH0tj5>7.9856lZr$?W7,=SfsjpOI',0-9fR;!tr>fr5(\K9n_@(SUDOrhPWDHTN]!P]l7
%G7!nY)t2T&.W55Ii!]tHc.gGQY.tW;)6hI0JPMsI]@&Y3@qFN&\3Fsl2u'l"]c27c_Nq=Ud3/R1_$*#$Q0crpcc`+JM9YC^&Yi\p
%P:";#b[G*%8m=>)QN,B=@NT&8o3U-33\LBn_In%mfHNd5'14)V!gcFZQ!aS*)W[@f-&NtD;>:bOT[C,&.$q`d%9`W!,MA"lW"$X]
%2#R-U.EB+GbZ0*-,l5p:==N[n06$)V7Zo6%%;n'mF!b\0kZm,$1400!^1-GtIMV)BBh"3/kHA[p1C,*RV>;pt4*q\YASLkj(e?(+
%n3*A*%kH+k@MtAjAJKWtbn$I-W&=-ROWhM\"A"gYMA7+VVaon6\k"WSWQ(7cYZKnI6EKD<">!G);,GW$_1WL<1k7gnrj.si'Fl>>
%hj;c!EUc-P?AI!K<_9KmW5=DG%Bd&RXTg.X4u72r8Z4Z^YY`0ilsr4Mp1G/9RR.>4,1Naq[EJiZ'UqQ!2!,l>d$)!3?t&:EN113k
%BIs]W,T:l8S=>2)pHSm2&,CQ\Js3u+7%GL`$k_ZOGg]qr\Cl`LT;0-H,1_El>siC0k@YHFVE2,GTDH9oGP"()m%QQfGKHn$-trL]
%'-AmV*foC&S6p316!bm'?D]uJ;[R&0K^9IegRM@,]I954O8]Y6d2_h\l*PW^,-,_(!2R-VMK*D63a-:&.&b-.5D>r\'q?%$SQYTr
%'g.96re$&j%"Lu:C_k-I;n-&2<X-oN:([ihbYq!..@9Z.V6VJqd_RUk98nC.@%l.bWT-rQ)sl)jO[Unp$*Qc%]q&&&"_m'bab6'T
%]-Y1hKM+`HlJj"?2<OqfKtPb'i>PG_MGpbRf`Lp2*/BuO?mIgB[fK];qiorjmB)=#^EJLc+sGUmHD"B$\sq"+/2l'j3g3#k<B/@D
%T#u4U-aqK.ZB_$#1>2n_?3s>q74='>P^1D\N;C/BA/)=r&6*tN6]jgACuSO#I%qV1E0?Rm`^LsX"\nb<8mAdK.Z0_Z<g'*aCm8NF
%m>F*$=k'i1GcO*"NHpq&&>cmm>JY#.h\mJ,\WSFcFhs!'lZg9HLRH%Vr!FrukD>rR(*',A5kgFc<8eb:4lWKcC9jIWK1'?+o'ngW
%jTHr5S""i@]AIgUo:jlDT(_r8URB:1.<5A/44::%@GaU46#VR)C)tf[/)I`R#En/*,OpdbZ2dTke>>2:fVoVkEhIi\E?P$H,!i).
%&BXJN3tcO1F\4RQc*q[[In$F]gm@:\8<O`aP7;5Z#Y&fJb_3uT2,T>GM6Bbk+H'pkpk?'4!#["`.FP0K"V1IXb8SAX%>b$K9tPD9
%f9^'.a?,M"Fc8"=@lMO_]V-a,+X`5=^:Y,KaT]6hg!l2Ke-B;?cMblCEh`NB26uFpeD8am4#5KLN-9g5hi&1RTAR5qD\i6@`ce/K
%fo$l]gl;1%3,@83HZHeE24j47;tWF<o<5hP-RioVeBK0Ro?$-.bg_`eRcS1J%*^&7%J_MK1U\ZLktBpnT@JnJX?h>KIQC,+CG^BF
%Q*r2;<N'md90J[&?BV%U`d8u/HY/a3s6_5A0/`alX)S`@Sh5\EN_.!5gVJ/D8Cfj5]Hn\*hg1C/'gM+MBS)VV$h,E"H[-.=%E2)g
%2^-C(+9W>_B+sC\8@^WT%MPYTBf+/5IM@mNIpnqG=j^FeTKIZK8QV(O?HJL`V-"Y%C<9T3P`eJaZ5bDUb=Em;P0;*@WVLVICRSS!
%Vq9S69d\hG%FJ`cl3]a8-\Ii13a(>q7Ei'QEe54=.0(rRbWY&sRR*nB_+A7q;rmN^W=n+[i>TtrGaNlT\!%\L,h1Dt`f]goUDc#G
%nORfX.H]7q*SY/!Eua6%"NNIMD(pp[?Uc]W7:PZG%5:Q*@VM:kjW-%'8um@eT=cLLEgEP2:97`[1T`;9]mTk&qX0VgrT9:7D!kW_
%>eS6n'A"%>0(2rr]7tBGPF7I;%tQl]*+[#MQ98[!/QSBNjq-O\>4I=fT!66AFu]Q(c-<u[h='gejgLMR4oU"Zq9o8-<27(;olKIj
%8X[T/Dq<S\no"G]3Pu:f$_#&[S1)k+p;14NIT(Iaflm(F<??/&o-M1+!YV+f,&5,hmWQ4k]VE.c/RKjHDj%@&84akhpGr'"?ET''
%T9JYQ^%-AgR4b?iT.g(Imc*%c4RM(omd68/@C^m>0$NE?8Rpon\R=c.l2p>BSUJ-*[@9=kh6GFXKcSijpZqZ,?glO'3u-?/hF[[-
%?$]q78n-V3S[o?cRCbQOPP4+F,F;9u<'6GJ7r84WloYWK(%3S:hFI2o2<T8r'V?FEC@iH:mm[?8phZW#pWs^VY.MRjq'H?pkGn&:
%lt2U1I!L$?h!B&7h"0B0WmBAQmCJ:o)ooY'bCjMkS4<W%U\N4!e3's#/Nlmm]U2n=^Lm%BR(TZu"d;`&b6<Yl0#2?@7C^5@Qo$]c
%M/U2ZVm&`+h_Yd2fSFG^"ZR7UaJCQo:/2*D`a'>FgFCc`gu9MB$::7cs75$(3!jNGo'OHh"eNM6Lj$4rP7Ng#i-\<EVCmUtOc7DE
%0lUYoD:?_$k4%^ddG;m&g8<)mB3bh2BPF^eb*:Cd:>0g%=;C/>^CMG4M#c]J;BW(EJNhn,ZFnk$RD.b1C>hG4)\W^i,(JEO0t^gT
%!WL!D>-ct[ZX,*@_FG*!bnoNN9QsrQRal.7GYYIh1Fg68lVq(IX42iEikh8^[+U-.Pl$<\,fUjj1Fd@qOM_3C/M$J6BqR][?J7hg
%RcRgG+#:#43_aLD[`ZL8V@[Kdn[=n":ho"pG[nT?OF#"\G32oW]"_/4m5B?42JB8B[Z/4BC+paTDG)TK`nZ]rD6,QLn]P\-'4\<,
%"FClCLY-?ood>SO=)4q`VoMPQ\"hof]VN6B[!%fMn9Tp(9&F2cgsQX$8M58LCL&r6*q-jYFM%G'mWZ]S_u&7%i9oZdGKj0i.5B;6
%D\0d=R()RrRL\p3fnW;`\c%T"U&"jh\)H^1TD+B2a,fXahY,p-gE&SjR(Vf2DlK%eMYEob]6s(fQb!&M[st6brGo7B8)5`rrcpqV
%kZ[5i:pRZm/[H/OplCuAi@FI5UUJ>)]qggk]T_#bRFgdi6s5MA=':N=YcR_I[T2uO;jM9$3_,3I<^CU>9'8N7V+X0GT#Dhh6]0W6
%)+KAJ>I-2A78?8a<+/e2W4J<mqa_U44ltimTbGC>'dtO<OsdgYhG\$''Gu1LV$>]>7RRTl$aN2tKL!+$P:o;U?A5Vr#_DG_#KFB]
%o?C>P%Su&"T:c(745N9GV*')1GAg,]Q#Xd7?iNkaN2pdNl?e=ck3Vn)U?2&1#J791YA01MLcX%$f:)6+3I7*]7TO;([9d4<Z.-Y9
%N0Ph:MkCOg2Pun5)'-)sKPZ<;Y`#tp[aZMf9JCc,&2c.-M@)CKI]tFQ4j>aZq+)6>Tm(o(163!h#77+CJQ*O[(%kUGF/4iN]_dAr
%X+gWbL)s4QaM<]BHsL;Gh1VZ3TSj77#Ps76_NaZ(g*L$>^HJ[2Y[#hL]=d-]YPB)#)RTDP:X+,r^A`/eaE"kjEa`(@gqI_ET-k`R
%m'#!TDg-S'61SFI[30$c$j(inMt`e")Lp2ET<6\^H9GK;q<(1GW`Zb'ml=ccRB,FmiR8k*?03#mB4/0DcdqdNClGY:FhUcG_3r2R
%'m,p&HtfS^<>0+P)C8D.S9'E<c\U89EGISPbq(;Ws4+dj;pi5;:4gA^%%a3l("KLD]+_0KRMib4kW2/]O$.Zp$Ni'NYrI=d-WJWT
%86qseAq]su"_F&K69.13/,k*NK4"21s/kB)%i;CO,eP4?GUG":F4E3Q=e)6NOC?7*_1lcbj,`?kcn$9-"[qs7B\!`K0IaM&JN^6p
%`Ta6m=omK+KP:^P-KETdjk@(jjIS1oCoFU7%=Xb,=q/)i$(G5=-B8,O;*D'T%h86\F>]QMX]ABZ\>"sWL?##:DWqNd$'8Js!F0ni
%D;1&\(_mO[ONm%d[`R%/F+G:O8bZTN[_BImI<"!gRj29g\A4\N`/mG"NAAP0cH5G=<8(;4X]$eR[62Jlc&pOtSCANE2*#o4cd*#<
%%L!]?Sj.9_pc5=e-0;$T@co/D2O-Ikq$suUDrhr>/jMM/_Z%E[oF'=:IFlofEB-0q1#"5Dk?Re).WabMM`GXJ7_571)5]Kd06on7
%Y%&@E7%$gjPlXoub23ES;aQ@%S+-s$:lr:]32dZp,AEom11ISZKYr\tom&5S'=:F?Yg0%Y*lRTR1gr=#@M:t*QY&&:eCeri7/&a9
%&Vj45B>^X#Pn&+F\XKtYnX[eG/NTiPoju!NCo+uDEkJO0\>I'jG/aE)@d!i.>j&]LnL`97'I.XtD#:laPa9i=e\@9*k&5"Z:Z.?m
%#3GfM;=@aX/L[%cbaNl[;(jSthT<)qfXbp/L\GH9]U33X6Jrq6ZT_>t-1'38ACcOYX(5pjJAh;'H.@/qaQZMZV/Ao[.=Ehe4o<Nq
%YM5j)gXIk=9`_X'C:m]6S^aT"MU"--]m\IEh3>*`jK(H_^4%>f>\C3Ek,uWX0)4"VPtl@!0!fG0k,jbYo()J1Sm=I%1%OmEn%OUG
%*Ds0.*4V8,W_^(CBU-u)BY)V99q>L%8&n&i_KG53NCD--e1<p9ZK*.`?[(N_27BrmXmMk#J0^>2h;]gXd6]p`FX,Fq:1P*?.t'sY
%"/ZO40#X4*M5%a^$Q=2KUW%o4ECr[$)+/@ACBo>A`,>RVS-AqQ20u/s(G@#s7CF2_-3h:cYohrg@shCBkcocq9JLES3KmLeOhM$1
%0`.#bB,&u03:`&R?kV3mT"&*j#eFoZd5jm2)jt`Zftj(p@T(dc\f'1I?<I%ViEZ+2l%s<.>b?W&][HPJff,mA,SAM%8=3fBe_eV'
%N9@'aGHtOncYP7_ei.c1T-UCqret!`/\gh[M(YQRc3q8JdB'(cEgUC][ZYK?fs:PMQ5RHe\GsKhX2k;2eI+(FqsVUr/DcF0Xl+P1
%37UA@/d.+`Bpt>"(D:X@78hC8(3?:jblkhI-jtmV2gs69.hk1Hcq/rBXgQ_pHL$DF<rlo=^.a!&Q2IIXDN`'MjK`A6aIhkjp`LBn
%(V)Ao09#'UNR]8lStIREkY^b)7[uX!BcMtR(5a6u'HM-:Pe[#c71<M[P+lQUEANK@;V1Z;XR81_-o%'.UTbMVVJ/#P3YmJFX_'LL
%?_s<_QdqV<]R@=bREuJ&R+J+T[M0j9:^;o-]s06t[toXdfTeIfi\l(VaO`+uFlE#Eka(3+5+&^kN/ufbJ>:!c[(eUresk?4)(ReC
%AM5G('*Bc730O+)`m!_:\9RK(IJ7%8h/t.^6E"nZFLf!C;DM(MI:15,1$T?4C3BsU9+Te?12GI^Ou5"+c.TDB7kCs1c!jdieTA<f
%05I+-la4cp7<pQ1Z9555_ZjB^=gAh=YpRZf.O,]pijW'qDG:3Rb@N=Ykr&WE!uY%FjU8W'2YPbuo*%q]"]+^n:\+`co*%q]"]+^n
%:\+`co*%q]"]+^n:\+`co*%q]"]+aC5%!e9?gomZn2-bDitji&(HfqY4O$$6RaVb<k$CFbe<m*@=U(a8%oosUeSL@*=aV6rD/Eum
%;hWDSYu-aFibT<C,F17:QIOVH0K4m#iRM$6GGE6K-kktVJ&D*sBZVIkG*7^Jim,n!Gaon7ApJ0jk-XT_K&sF;6KlWPL$B\PRbRg2
%D?A@85P9KO,1mpLFNCU*Js0<=I)FCA]l#5O1>E,J5fiNOhjCg4XieNh1-?U+$UjT)Lt0g(+IdH-+",eb]VGZD05pCu<^=Fh5R_Gd
%BouPFc>PpS?afSLXDZOl]s[PLk`5&A[s.`PZ\RIepL>``%qG],4-8O8T//Bt<oph?lTEFHIj3suqsV8/<-I\d19('*nj#TD=Wt)j
%/K"N5q=MGOGgC#5eQ0'/g;Z]c>qP]CV<r>+l"3Seg@9ntFR_#q_r\*pI&$JA3=3u&XG6>K1$=8P??Qt;1F"j)f^?&7BXW-6pi4+p
%15Hs>>n/m#jY%/"p!-q%J"]!ACnjg:g6A^e1=Pg-dL,JAP?W3ZH]M.TG7/IU[Hr?9fr7ssVY?<N_XuZ:l4\Z`m0=m5/tr.D+n53s
%c>I36DofKV22fZ$'t5&spM3NN2670oqF7%!B:"Fi]pa+$NuaJCI>G]s79c97_]tZJp7C35RtJ1/#k._HC0]RM]^[2jW5CbTii4O@
%ThE>`-3)F8MK(,/)?'NqYUUH+!p$cD0=/W*NM!jgL'?S1hmgG*B<cnP.GI1TSG/?LhNj3U21DOp99#RY`fJ%Q^DC#]HL*OIkcK)Z
%QK[<C^3G>c2N0A)n,#\+GB:AHg3CIR/)j[tpl?N!mr$e=G[G'.?R5[-8`U6TjHRfp:[('@A?KDAO-\r"o'HmD<*n^7he%<*K%JgO
%rk#_UJZblAmr2Cdi:+JYmu!L?*:SUlpqSs2G&M?(9^i_@F^SJ3F,.Yt%=TZH]Qm/)^I7Tc([l<F8D"kqD3F__^VaL[rg'DRS?2!P
%j1X.)fCq5:s7V2tSLYe.pWCJAn*eR7rubiu\qj%pJ5%D%qI'W"^5#MA,1LfjlED33:0i(c-*6r.X3ZUp)7'gb$#3]if4XaPfOa4L
%Jpftj09GNs@"7tm7/IVK^-oF`&ao8(:M['/'(N\)^<c$;2%"e!_p]Wej)a(AXfAX3YRC*g#!K`?dEa-,Q8BDi2eIoW?^>`;e8:nT
%S4f[WV:$G^%b$D>pQspX$e2\H'_oVP\=;NbHkuM'$_P784t=#"T'-B9D28QFa29)@/k%e9#gFo3F$'.Xg0oCopeK<@e@<.?4>$%X
%$h2KZFLQpR/20(mH%Ynk*TEh6(ZhkNI>&X*$V\r'EaftRZ&GH^@eYg79AGcFcdo/\kt6OjT]M3+EY4SA/e^%8S'1!#op+r6EpON$
%0#__=<utat[(A>r1AQIOjZZo7jhgb?Ia4)mrpA$0*`D%D9fCVJLS"%e(skW*?Q[aD-qJ<[-bL;9>o2-t2<)"u4aGq.Y.XSKH6hJ<
%2+G#"pq+-HRMUGkPUN?'loO!7'0BlDNGc5A&(f4eT>&U-Jj6Xm^9%XK91fr6gj9BfWf%&<o*T7WO"UCB"^kG;5tTP0Ht3E9qT"Y2
%Wh3j;;Z5[EM=.^o8p$[Z,<gV:N'IQ=1aCYsVb[TKnt*eO5l'^.DdNJ7m>KTb?G-9tP:(E<m.iT$+8u*VqH`.=*T)>?m@Sk'o\I.B
%DdP_XoY[:cs3$]*lcT7kZ+E'T1AK[4*^BK2J+.KteaL5mJ,M%ql!K#8Q`G6$Z`ATRB6X#+I,fFcHMltu_o\bT^!_D\?3AD<:02X-
%o(b=h(G=/#96Z<*i*W[miEtN5<4i2=#HuVWJd2.7iSP6&Y4gd/XeZkK^cgP$e`hjZqUP&+!o%fu5C9IpCX<1Yp>"?`YB*EHl;WDd
%Gn42\\=9K8Ud#9&n(/rtc#*+X/"u#uSJUNg`SIuRd2";&hD9="?,tBF2E9pj\q!&VI?R<GWTW\9rB!aJ"RA(T:")73GF4T]Md<&M
%4mSsmqPNKWb:*7)e,C7shJEpmkYfa(ks=28Ge(gU;RQ\!Y?Tj&fLp=cB/AIu^1k/c6Zm+tFM!PfD-,.S]%"'#HI/AM^\\N4qObfP
%S#feco(MaA^)>%=?(*F!?F!B$d0m=G[XGcCmU6(@n&YI?9V:SNH;![43qb@pD*=6L@iYJugl09,<+JaLqAt])J6%*eS,+K.U0gh^
%Qft395No:SFCk<+1u5<U/:LgRTJ*o6lFB;)ogmR-])%Z"Dk5i\m+YC-kfYT<H7mT(D[gO+V6Fet?>%o"gSP4(fu0iP&q/5N!59Mt
%[cqbu>Kbcu#CY:DW94eAdkn;>(3]s&:<csd#(4da9oOBNd5OdPS5Xjj2!s8cJhqed@g7A7`2Mi3IGM8Kf*)kmD:A0hkIM]'M8%d;
%URk-N:"/bR!Lqi@b;G`&[S7;/ZADH)-2M5hJQ\l`[r^B/6ZR<2f7UdWfMTs@qP*5hqp".k^C_V&"Oj3'q&n'@Hj7t3q1$R,q(j_B
%+&_8JqP`Ck>:W@@G5cnrOjk\BN4Z\s$+(:n#oBak;.50-66`W[?O@0.*Od(&OmQ_J);frZ4Q@>f?2IT#??@20p@(h>RtD"V]b1pq
%RiDbW=)cFgWPO4?L,[o7=ob-.YF([Y08'V"'Kh=BIiUlKgtP`#/a5WXnSKI\V!=p>#A::PPpMS7,/;iD*1eaB9R'4&IHMA2G>qu]
%.a'\?$RKk<T\0m>kK;_`?2Ed[8Z2[NR]LE6,99n=LnMAseFQn!U(RX@A#0CB+-/q'<`h:Y"8"Q0)-#L=S/a^j\k)Pi21CH9:KAL1
%KWdb.i[_Sc'3ujW.9u&:h=lD2qi=O(*-k4d.R:PqmI.4j7*frs@:EO]S^\G=HA"0T;Y^p'kTZn@2j-A<WVrZu4BS/r@077_99=:\
%jhWYUau.TTm$2$2%iTVm#m,Uu?C+I[/MWB6WCrN-D[#Y=CHgur"97oH@e[<D4^NQ\/N/!939Z3C'%E,"N"'lq?"CP@79<6(NF!u<
%\)="m?2'J<`TJ@,!P6!TTAS$^qi8)jdVDO/Srm#L4n.l^d"<QL%.o?6'3RPmmg'Ms&Rm.[M:fM*f&1#VYl]V3+eQKth16.s>-mH6
%5`bD1km@&`(8j9PIKgs\TYlmbcoH&)G?Io.8Z;]9kZq0Ni*dhuD"jfi\K2C3mWA!N6jjT^)nDlNJe2M1>Y8:X0^qCKrIF?,P!Bom
%"=9"Q;^&kRn_E]d*pbCQq[.(hJLlE`LI.+,L^LDk@,GNq_CR/k1\7V#(G49CLa\A+SB'-]jo>e3H)CI"7O4YoX)U:Y)GrQb(rt+P
%cp#F<ht&,`-L8m8S-)?P]*ArTr*C6Jd'JoS9$G(lM(rG[W"9o\\.DTd-Z^Q)/+fEE:T%=&\Pf_L0:HDp-7).M]_2BG';h"LNm&F>
%b37Q@`>G/4_k:#d6]lj:4OoQZ=Rjb=7?8=PIrIlE-Nj84-1ElTfUVed=>.0_Kc]kph,>'">E]W_mQ)PhluY^R:5g(S"V5W#VG-.S
%&@6#P%R`9nA5+?%E9$lG\VK<FX`C_We7IW*":K5Ob:*WK"ruLr!%0QN,<W]SgH+23h-&8WKb&7F,+Ih_+n:EOE"nQ4!'RZ%":#=I
%&A9D%6,X.)"OVqs-#/]C1Gk0Zl_H,D9[!_OX"Z)M8<4q8+VqD%:8fO1I6J)ENiHT/@YXtELA!doT0?nm(PUS;&4%ZY+t_?/Wbin@
%0fmSJ(qF!iFD?8*DIH@DScU>n%m%A%)P(tDJ69?0RFQ'rAgJ3T$r3hKjNtl9eHS+NkC")M)j=i,4^n'3;((;XKgZbH-MG7@#Dbsi
%3n+Al?p!V0:(3*cpLpuL4R#KS3f'__ZkuCH9s!>81qNG/C_s7qeG&0Z8;JR6+]]3O)Y;"58djI[n:Hi?j-TudZW=B!GKcd+i_qFG
%XNW$aG?iVr8s_:"2&'G\a$1`l^[r5DL`lf6b%!T!5\p]IDVgC&TJm,1Pb&Dfg+:%eJuk@''pcjAaUPd5EFLt@R$a"#1OI,1`C)ah
%M;,R?<=hV\>*,,3"r/K5U*G-O6.s[)64-?WA^iu&$6D$pV#fB:3*]Es!!->]YYKn>]F+6aE#8VqA!%]PEOZu%57IB]iI_;54n"P;
%e*sSgVCJ8.52<Wl.ArOYJ'd.c5.WCVMlba%1==3EJb7)Y)(2VL6jQ38<uL^-ND+H9p(bqCa?t8i?62kC_#lFV5(OWB(Eskted#<l
%M%p5`HR@<G7#NbMK/,.LD9#?$pg%`.<L$uQh/Zh1b/>9!j[%Eb<)n6`0]`pG_daRlU?mPOJh#H==Hf\4\RaQ57*i+D;l8f)6:U!E
%9G?&[&0g*]QrUu^kZU@(",Y7]Kkj417WNQ0r_/!Ec4a-?)GhV&iM)7'nq?i\H^=2W_^\sZNl+H7/QdOB,0KMnGL^)klj%R9paY!l
%7hIJ]gm:<@'4D%)#8%/MX_A&/h&ZbhX\'$V(g/-7/j^o_k\(0i/EW/HO@X3j\cWC:RS2Lq'%MAj,#Yi*V%:OfjbZg?S=Ncg'*6Q<
%gZ%23\tgpuD4tC\p@!0P9Eug,2969%_u,clh8MhQ,(OX0H3Ndal6/i*je\".\NC2hC2(7^J;tMeR#\_tjC*'nOg7i4Bj(V2Npn[H
%RrA@LGh^"E'@Z,)Q(8!,P)Y0PE.3[kNAdlg5TUJ_$WgiJg3r)OVnH+d*O.;KA2\q6F:9Y^(XFKY5,!\#3nGT+"#&J2MY)0tk8iH1
%-TCT0>_DXJHM-L%<\"P)Yr3^^!1h@fpi)ES"/D6qN$ml+9Vbt>6p+A=Bapc9lOM2uI^lV3MPgbdhu(4J["*-E+?XBJS+1c<6O.WM
%96-#fJ>bgOn/N6''e-^ifKI/hWYOYY^K$kk@hLgY=CM\i%2qKj3cs5E!E@fAY@]h2'j,Ou4=SQ^#7Y!>M$4Dm->$A;ca:Tp#L@L4
%oeE^NgKCI[I"1+WD*[#"7>l\g8Delm>ZX#9=N"UA0(m5i*HP<+lUo+3E9Y`GT3t*"rGproL<b>o,fCD.=NZF-)Uep/aA,%[mq?Z4
%;?J=";2?>Cb<(nAq+P-(o*R4C(?k2@rn^Z[m'9ll*YJH[.D;D!MAA6WDFgGMm\+2h7N]'G/RgIXn"*32l5q<,SP0,j"'K/j#^iNW
%0mb\.5dd<ZW&7P>io+<$#][8f7`AoW2E;t5OI_V<:#W4%/dOW)E<9rG*d/e_Z)$.<\AdG#cF2hHI"m=`<KonG)bcQYF]c^oCFJD_
%:Lm11[F^%+I)UuF8=K$2P&H]n>,NhfW1-5LVq0C#jYI;^=n?);/K7p^R>E33=q/GNm$jp;.GA%gLJ]Sd>(aj!6c=j_lqml1'KINp
%8Y.En//k:g:@3=rENlbk<GbPO<,-;f33#4I<t3'PXrPMdm9/i]_(c<0@pqdjSKuLr-9!imkejXO65RbWHJaDn>HUY9]%;a5G&m"T
%'o<S=>q\UO#YN-b-mH2H;m71)m`ih,?6nGd9I(#"':K&Z_@61i%n$8,HsTQ8#J4'UqW>f_.V@5N`Q];?:/OVmC[N[gS)"dMm.fWt
%*IQb&\Ca>X@_F[OZHkqe2jjs;5ZC<:08X"?OZr9^aI^!T`@`3C1kH!L4OLUGclCl;1"a_KCA3Fs\$=#e*_=A??n`Y2UeaZcjU9[`
%Ng5qU*E_*,?01SLA_;Sq>9UpM/;>B)$O`(ZeM=>YGGu?:O4E18KJ$<`>c!2?P33_bC?[KgNL)CK>fD(>pb6A#>XR&@o<hJm^dTJP
%WtBa;Gfc^F.%)r-1HP+O,%`:DEnc'bc+-u3K8akF"AP#7O]g1PojJKJfkQ4-'b^rc*#efFbf4C-&L&e-S+#.#Fnt"$WpEo%Q\*k!
%ck0GPJO_=rRuL:ag$-Y(;Nh-T@uXasBSQE`_HS[<2Ho1p#DSE;/,]f(2/HW98\&,^\RM9cF54n]$>5'6:ZZ$O9VC'mT;^rDE29mt
%2'f)CajU!483AM:QR)BWTCDDjAs`f$KGeuuLnGPe'8)idEa&%geJsN2044PLe%Wu8dkX?8fi0bgOL63Qg:t9IiV9)UOA41ik$!(n
%*LPYn[#2#3=hlZR_fT:mkjO5kK6!<G"[[/*baqCI@Ccihb=Pj1C=T^Z`%7C3FMDHl6OU"e%:(M%_"Aqe>3(P$EJU4+'6P$HR6A/9
%5.lukU]8u05]r/h=Im:Id,*9n2M[*!E?*uU/CW1q]%@\)@!3p#-Bblnm?MOikk&nMfL+C^B/^"@HW&T9n2X@!M/b5s(Uhkec)\:p
%<`>c,XKd[(&Df6$naCS_eP6`^'62?'1M@U\BI"U@6=^0MC,MP>;RB,S&<A$PBYTFXRd>RCVSp66fKdJe+NHmnXG`LK#ilHB(i4JL
%<`F;P$4H`),c32o9V#ug3^5#NaV_g%m&S4\62\\&I:fR3_-qru@spnYWN7a:%l@b+TtEk^R%0)5>^1<BUK+;b3-:`@-3j2!TVSaN
%M#4X(P<#(I$LLh^ND'J(?m6V@@X7NZ:<EMAIL><N'7;k33&R*b$F$0'\B<$JnW*I%P'"kH4F3/7nZc:<tZn_&<g&52pI3P
%^m2(:!V+s<cr]mKX#@1%a$`ljL/kfa_(B1XXf7&LB[gQM,Y)$>(E)!F"n2XL_*sSNKS*jA!g.)$N.X^05\rUoOUA9l&:,-t!f/RI
%:'%*C5Co1'j.R+/K[5nE7Wqg0LSLFT\U*k0LaI[?''+J_a+)O4buZ93#;X+EdEf`qgM%Dq@77DZLa2d3CDLRj8k<c!`Sj8]`hXC&
%f(*cW(?\i3jgRLuKa]UR1!h%po!sVHC0_OXZHR>42_"GD=gbV.\[QhSNDkRKI7P.^&tW$e2aBcPW#f:%lUV*Cmpk#7L_mXYH:`6d
%=D>03p#NOEjXklGe$(o"'WP#S9uO_\_]E9MRM3b?.^1ti!bCj7TeP[W,(JZUC#Z3\\ueug!jM+)f9'm_8R(7`ZQ2P+KNN^@,(SF;
%R;uA/Rl=3jZKhghA35ekZ=<$;H3sn\%hn#*H7Q"MB0^u!Slh5LBQH0"M4JqUi8CBT#N_+.P3B20#O8OiP;8k#582)AV]Zt=?o'"f
%PZ4'Lc>_3k](/$!gl4aUXQ-'@()VhLn.$]N_?[VYANZ#UYQq3\m.8+KRB"J74_Kcu-i@LcjVZWuSK*4%e!rBWe@:AcDm&*Cn<#<T
%_9Ca!LR@R)=C7:VPa$:B"h`:^d/8KhK>h_E_bLsVSg-@FN7]N;?nsm&#9*5.?._>aIHB0_GKd23$.fC:!8gPQMqu&:3'OtgnVg.E
%]jp>%5kQ\Hl')oE=dpL6qkT\6W7k:9SEL]PiGlkGRSS4=R9@BcokfhWc+VB(a[r+o!RUrR$ctuDRSI<r*fWO*]F@a&a[LqCipO3_
%(S`_Q(G_KfYo42W+rb\g;Vd]beF1'f2JL,m;)=.L899Z@1XjaiAk&%5@=5_Sn^2)KAs-m:b6fS($2=sHqg'Z'[XjRr<^b%Ra(c9s
%"_!I.fWuE#Z`-]5oauHgd<eRujjqu9>[!bS4g7cd9r-GmD^X,QK+jENG42^r5TQG92XH<to`Gm@bY]t15HFW]F-\Zl)puj5_gs0j
%-(0e'@rT<NTV2]Q%`FTCHua9XJ9pa[-D"cdhW?]pB>?9"!#=Z5Cm(<DCgG$?5[HM-(LEUe[,YuT.?&s%#"d=?WBu6gL@Gle*KGgR
%,"\V@fL9CRZ%*P7TMhltlian!0p=:32X#+]M#c;BiFmUT6$E]-%6!Do+lNdrdHig%:T4jO^cG]!.#eBp#2SGhSsr=:&#$&d;k,n?
%=3;R/21P5m;8;tdO03s+s-d*s?]&r/eB5h$Ka*@$%Zs7ANMG)5nq\(PPP;#0?>r9<>)5Ki8H=Q&_OmVa7Y4Sr^rSE'c4pHo2dp!S
%GmS3uC+*3q&f294Aa0OBl<`<WKJ)S[6h$Gn*4ZT9QP!12CsMJ8$t'+:MY^o4R+N-(6B[d0V'KsKTF#MTP=8EAm+kVbTT]gXn5_)j
%U1l_HJSV6]f,'Ed^fQEr2-_IFQd1^P(]hL]jI@RG9GNdt.L)([ZP:^tqJ1\?o&;^HC*WVUIF<'U>pk@[P62OCH-&I";:-!*-b]ul
%D6m0Op/s.?8]Wj,n+k-&4J"1.8V`uIM@kUa/I$pN6]BdK*k%2!%o3rpA9+uf]o@;Mf84ba*.s0SD^(!aVnSbVD.fR</ga`g3Hc&L
%h1W[_ipo?Z03^p"38RnA8-e,YZ8!$"Znf?%nY2:o+2W97[i1tRi=OQYU8`==0joOF78E@K9ehhK8Lg)H,j$_n/RiCgL85$;kfahF
%$s[Tn/e9-V5:s0[D`)VpN?bJ=)`&D6MrHF"0n=Xp.cCtu`l=]u2)^QDi2+jVT,4+qF:?6`<T`8^30FqXMu#>gBP?=i;`8U-NHA:l
%k%KDUlG*acd;=n`gnTVl`DED""88E9Z(MsGUmiOGcdkR5MkVnc'%ih,%nGa?m$SW`H8(1)=<Af9!`(fIf]=;@g7VH/D2MoV!<U(`
%kr#iR,o4@!6uV(Q1LpQk:KMX8K7kY(\-2@9,.i0jBF:o&D':e?3h=4,iN@fl+dr(l,QUUFU/!KI\$dP$Zc`-bh9)YMA=`@!&_jfo
%;Xn74_^Go+WNk3Y5*,#:74Yb@o"l':cI`M-XE^]p:WASE2]_Iji<%;j1bh?:FC?)`%_5u;,ccq^aU+QV2-H[@(>P'Ylq=Sf=^952
%N38?rf^RY*`gVktCFsEnr5QmW?t]3*bXD5:;%0k3js29D3mLH'o$'f:Um_aaSlG]VF>(l+a5*I4+XRsZrHcJV>9AG"NPqNH+AAdC
%,1"'K,/bW9%_H:>/fH.@Ikb(=dEb3"nk#HG<RpnKS8o2JI_Za8jBc5*b)Sbns/=:f3sGj)hTZL\mMcB(<5RThr`+BV0h.^j0[t-)
%.e4EN";3uH=][JYN6&mNo"hJRXL;,o"H1mL4Q\-,V-o@(R'Y5s`$3*O")<C#?At]<ZSAGE2Rgep%oS7_/-LQQWO&St!8u[u;.en'
%$?CenEe]/b!kUA8^EHE^g;\0IAt;^+C^<`uHNH;tGlk$(Bh@g3.(g=-oG#Q;76mYF"t$4E2&+!1RDe5)c<5o\Nt0()lqG6,kZq^<
%%!)AIqKRqNfM)p3aq6=P:4YN5KMfc4.Q2q"4@q"O33h/$hN\\ol;Q]7FZt]oWcG&=:a2<PfMg?2.kV-&TZ)._`K2,!)^`ZaS`SSF
%6L,PlL*MQ@cjVq&5t)L)?uG;Ua$Le!Ho,N)_E_;(\ZHNlJSI-EL7`$"[<_YH!?69j%,<&P12G;iYRW.pZUA6E/egGce7t!lbMhs_
%B#A]VnQ2GDi]&hJU1b0`cn3Jb'%r,o"7Rm%kcBKW>4%%J`WIon=t%Z'*-R$pD"P3=VDnN;;CGM)4>"uB$$H`UWm0Hu6GHI1DHI46
%Gf#?'Cs4$Q)V0btE/PoMN'SRY8FMEUA="XB5I%71cW4?rl@)5B>0'l7iG%8g3-jgWCFY:k$5(fUO)sk)0iX))DA1J&l,Ip>DdJR+
%ZlY0$Hq64pXb/e[?S(nj-"<)-U.EWN_IQ[Z&CCp(4i'W4RK(YQK^A#"l.g4)HA1Y:7nc-m"Rk$mhE,.t/Cb@fV4_HepI`qK+phkC
%IKWtP`1<gIA<`7@!r#6X0RA[HZm[]-(qm.b^'\W9M<G@S_Vc8=bQaQ6&feI)UaKh3E8h`]a4[$3`R[b*YB/&9P_SM?F2a_H'4%i)
%E0uMh$U%&_Dp4Y-!T0_Q_mF'?EXRf>G=jMc%:UT0)SL<P_aJ=0cn-.JB1EPrJmd]:JW,4iP!5h#B%UT7HOeD@KnCiBPV.3Wja'?'
%@a,5o:GEBkb%,s[#r!b@'cBM96isjgMFT1eoE=BoMnof+_-61PZ(aB?jb]R/4q.c]2*,/sH"RmY+?M_6R_#u\h8-6r'M'o<"8@K8
%nOg:;!*nbS[AYM+=48piQUqX;rHi<$LQ:h@\6k+&QMS1aYH95%M.WTZ?#qbBf1^'MY:?k1FkB1o@V):d<9PU1<l<k=6GHaR';qiA
%HIgoZTp)7Ak\M3A*)k)>K5;*)%!%?M'3hnN0XW>$9i"TcZ<_u4QIbfoJTh>c'?HS47$p#8kSjb`@[qR;.k19h2E_ZKVC.oh8U#JU
%e4I9@krl"PbYDB61?UN0L7J$ZL8cS_I+\-\l0Fp__4KK9H8(#fMU$__F;Hhs$[Y_u38:gj7VV[uG/cr7S":m[^=p1(7VApg=Y1's
%PnUd4'7j\+<pc9Q:R8tg%gJs`S(ncO-i9Xc@h&E$HK>^s9g+j,(aqB-?%G@93f<-;KBsLVbf?jS9#SW$M%Ze]2;#Id`9jt6\L:XO
%U(cYT1KK%YCghE[EgLTW#0t,[afCjlEcD8`G/f'-F2BG0Q$"J:X&\&;5lI0p,4s,@OmF'3b$?<nHUUF,M<:20k`mh*6T.%@T$Vd_
%/?RDEf!!/,nR]stcDJI@$k>SINF9q?#\!n#M[H<RSdf15"/@'XN-Q7^M[@BWr]o\_*KlJ"=>2di)6mp-C->#r"bs[bpqasZ+L]u;
%,9k5gF4(tk%VI,Wejah5*"15bk`RRgYd1Pa@%sV)nMI=_-J(bUhBepmQ+`0;dI9\C"H5bSR3+;qL`j_LB2&>u0/M$acrKGe9Jm2=
%q&q:&".d^2dcYp+F.^YJckqCPF:+EY\qT7>E_90N:aKY(Lk;$((pn2f'9#To>1Piq?XR:>,s$V`>G(+>'=o!"KZ1eV-m&YG!7`d]
%3$t+\J2pC@6D6&:UsOj@+!'YbZ/)qb4[gN(UZ-m%aF:u_;ZNuSVHbCZ.)(RA(Q>q?0!VhU1-H`iEDC01,Cs4>jB'Ko@"='oN+9bX
%PD*-U"(Ln&Mj5TIbB/[V7r,-PF_]Va&N*Si\P^I[JKDMK6W\P]8l(D[C(W=]^=,HELVc@@7Sb<;h+WKmS]*@F#0A5S'+o!cPIjs!
%]?(apVC%2SB7$)aI2r,QffD/?-Q)K3g?Y!GN;TJ>&!CNM?t:uJe>E4jV_JIe!5f(_GH<1=_^Tm.IV.^oY!47r]q'si#rLC?QhVbh
%RP"7\RS=]s_X`rKcI+/7V9q`.QRUHMjKNs;_FS'Jn9-c6)BgO4L,L%2ddtP'+hh&:/un84o\(_I`ofeeL;5/Wo5QUT)P'-5C^ZJ,
%-'Wh.&b6jXaStctpiA8j7A!%FaM?"h\/qN#D:+]qF94=h?QCfn@61M/UJh4s>01*en53\\&CQmRbsap<]E2$#@UL11-Et05=GE:K
%IG+^(&+=sfWQ-ILJ]&M$rD1l=@/'H?UEH5*4e^r%ZI8Wo'g0/;-/WJO^S0_X>fe23!gt\\_dVl$N$EX/40ucXM08F4n3t/lXn':?
%3*OWh&i^GWE*jiXE8fS3>Y0ub/-f'?q>AK[U,rjG+RGBpjWqDG#A:h=.8Lf`-t4b!Ju*2%Qh%DBNC3IZ*qh%XL#\](!bWF^=OAH^
%&L<Tl_B9G$7q#`$WS0B82u,-Jc=6thQRp0N$2Q1njNQKcdYMFL@<s_;P]2T:"`a7p!oVuD"E:3iN5'?AM+;7=DaYZb@(ZhM4!_bC
%N#WskPIL'e7"^Zq9`-"=L@#9LSNI,`M+u/)HpIiSUXpnW$Hd0)K>ZYJfR_NueqV&*%4!KHFPeE,:4jSFY*k=Of1+\#.`p9eh3B\#
%bXNHPj0V:^MqL[Z_4i>aRIn=m$0I*l?KqUhZZT;s'ARj&,GU.ZA6#]Y:&r8O#t,cnZ1onsgZoefFJAu.3=2<DNY4>Pe;rOS_S@6W
%ldA&N-<=pW#n=%1m%`N,<kWF>1Q\h[N*.UR0gc&i(i<mTO[5;96B[F7Eo7ht/;c51Se>@ln3E!e3P.s3dEkb_[.GP?&;>nm4&?tn
%(7CnB&.)3(h9pO+KK#RQJAm/2qg`!L4p(#=-'JF6VQ7el234'BZ]V1rmO8Rq6W5`J;$rCM)2&%oViK`!o0ik=r;?`S^rIVWrX@X;
%pmI<S0*nCn_>'=cqN+/@8L>cm#SlZVGi#//+MDo#GV'4^"/Om59R--:;]%0iL*2p1V-Qb="HYf1S;j[ElnFisL#nXR;-pC0Fb!PR
%:=PdO-:lb6\T/ZOG"c;p\:nh`/eTH6<&aV8+Z!1]_h</!NDu+e.[h0mE&P=*OK-_u9%gB0X#>`3PQ<JI6o>[bStclTA#rH.G(q/g
%9WcG1R\ljZb`u^?8m7fI@uRYt9f$n'Aj**Za22RY=4g\dgdJ>e2AJ@B99P7H?*^GSb2K*eVA(5:=WCg:;kF`(#pP4HCRT$FUQ%dH
%/(F)@Aa^>"KedKrneN"$)`_aY9tQ*E$8/Z)>.jA@Smu4Xr&Q7k^fa44ZTd;]J0W2&'Tr"5<0;'lM`IVIEg<RMIC-t-p-+>6W#QF*
%'!usC4YhmV=3Cof(g=:c.M<38j;+h^beK(PkCP__,>k)pU8FH]QH1cuOFWi_cd[=bI3?R_j>]P.mfhtgA=6lK7mU;-3?4;^8Lf06
%.+]Zl$sTG0AW"<hP!cV%;,qi^M.<k1cl]A4!n.s2R\,OtcQn0lRZc/s,)+0b2l^K/ogP!*)SV$oZo!]h)pg>CT.Bin'(HZ:kID,J
%>E2D!NaLu517(#2</)r;U)]$uXW6NU27mEo)Z3Yn#6c]M.T)GTU0]S!:"JD(9fNW[TG3.^-;h'n)p0$pl9lH;@5STfO`4YsYSW:.
%A:tE,W3]#\4EfEJM_gY.k^t&s>?s,`[i728KlEg'10&H;,9W+GW>L:b-%cWKB1[^1.)59?+,_f$M_-fms&k;tREa4Em]0WLRGhSa
%hhl]ZdC#=!fY/kEGK;3b%f\/4dHZ#&@`PAt:?(U-P;C!:b`s2!]TWZ9_2o$rM1A`pMYV1rU44jo?g#p*FTbu\4rM:o<4eOpann$p
%m2RP&Z_79L9>QHZ@*iZWlLPP:&g+]\\UJ;">Wc;22q4AW9%j7HA52iQ+VUeeg07DE=diOb2`">!B0bW6_1nq!W>@l#;a)>A*Ct?a
%N7k<;&4kZ$I;(a])F@@_CTGZk<9GuRR4=4p>^i+\LVkHaQ^D-GnkGUo;008n0ga7^%A80:e7k;dK$X3=iAe9V,SZ7/\5nlH,\]-I
%pE`%U)m<:%9[jKBKNVnonfh;BYDQE0,S8&M)'iJ++gL5MJ^;"*(i\k`'GgPm"o-Fj3<2-I`7]mmSLmBf.K!n)Q_S"FM/Ie.+?2Jr
%/hqIWE8%VOCdQNRlB=XVj>_=W"@+JF:,\Y^CaV5.pj8O[FP4.FbIHcj(.!#NbS]d$%Q\ukP-SaH;.')0.Bd(-i+I%<pT/=-aoU-O
%h3RDPol$G/7ERg!oR!m"d5B?Eo3,Pr,a`7qlF)+)=ct^kaf[Wt2'iG'mR,hl+t9TTMW;!52C\i1E?\i`HYQGBFB'Rq`#_2WQt.iS
%5LJk@,UNdI;%^d:#>HsMn+QQf$)O0OACYI0YG9B#ak"_T+Nh!'4S#9NHAb;DG&!1m@e-l=>u1cn^K#qVbjHT6C,7LqOlCDR4fYg6
%Z?9[9BuOW\S-]b!E0BZ>BjLs*`%#88n;I[fD/8!e`nclCNH0kUF4\.[dRdOr7q"#=FUV0VGW".X3Li$:iDPo/:D[1SSV\,A\Eo0n
%>5#gQX\;\1BAl4D.i1JXM()4?HI+%CoX;`dR:;#2QCTI'6DMY2NJ[*`$2I$dZKY*^UM0heXdU='AJ<Tl,[[9!S)2XZRdPK,XI:4&
%AJ7eehd<04S)2YE2l5`^D):)XqQbs\Vt!4PeF[)GUM8&?ekOVG4uL/-\$^SIbGI5kO/>JlX;h`A8kJXj'QH)0TrDg6igMaV-=+I(
%.]F\*+NLArKea03s.CTG)a3am1Km_E?8[hDC@YdYWhi1T5QMJAVlB99p(+a7Sd(&snp4M/A&ee2,EgDin`JjS.P/WE.B?'ic.Nc4
%oX0J.+`Bp6KcftmU5sX"Yc]Hso&i`r@_CPsFdZ"tCq;[kS#.o]MVT@6d3EF?RjKc8V4LE1K>)&'dR8#B7`Sk0gEnQ#V4LE1K>)'R
%F0+N$C!Vp=RjKc:WLci5K>)'RYsPa4RQ'0GTWV!oU5sUY9b7(BekNM3/pBSl2,"hqaki(P<K\r-76m#r[';uV#t.\_ksC<'1[)Tu
%1]OlkJr6@s=E:o;?ZNYRcR&Fd8")nG7F0_c'AN.<9b2PY+6,WWE"F7/_ER:>5<7r`GtIA8gD5e9%$ZK6V8"G5)0EHCaBYnS!GPk"
%Um%I!["^Xq3U5T0=VPBQJi/9MCXC0CR#Oa\2K?%4/t#S@\^[F%Pal0kc_i50*+bAm,AEqVS*5Pp>m$F,WkcdOE\W]Nk(meg>(/+2
%VR`H-k-ZlMV0c7t#&L-GU3*='@Us-4Su\u@e*#2'\'i6)Btg01TIrQ<mn1/nXDU&f4`mf=dr]/b[5At/0,*01-;uABRin&TH"O*;
%>Gk4s^4YaYmQBe,jp@Z+Eeuu+ZKZbqi$.L"_QJQ0<V>XqcU8<N+m(B_.lJks%A&O36,;lcD.e[=l/ZMte&fhE]uT1P<efg^A)sZj
%CF8p5`I\_"Y=h,".q;rMo#B+dSJ:ptPG?)k-R2sl4<7FR=@@pc0akCbKgeLumqQ4,OA>Mp]4"b]2)!l?gD8ttJF9EVF5.:a9s=^4
%*JR9dlW,o823u+R$@&_6oV<]5\[(%WkS+dYWu#2$NPhj2''X97k'C_YRO*iCH'X1YB>/MOfRtT<U>)3<B(SIkKK2-9&],SDT+kSo
%3Gg3c;=m'Orip]p.&@]-c&cR0SXh0LGg8/EKUmUURCA3X\EI!I;44l3XsU(76Saak"-RJ2N.2GL&Ih_T`/R45(Ic8=U(p=KL^,kb
%3>k:1.FrI3#$V2S2lg!<+6K(s3*2%!)SpX$$m/.&\q-aB,J)[*:Tc!d"_UJ>JM*)E"bYf-<$B0N>bXl>"TIBVWs!T4>_a,R;rMP&
%NUpmfDJ>0Jhs>>QFI4Nf%&74A5Z2PeGYW#-,QOq9*<BHM;Gc7-N[*^D?TH)D;3RZh1r#-qQJsd.AiLblh$HhXZVBHM:#+^EW$(&L
%M9g<<00c`t<))CE3%eDEeQ#&6+clQ7*=V:9VlQH&p8qp[6k+agoUhQCieBl/#>EIG\S=hMN!p!M[Aj/s%>/o6eZTh3=Z8jMT<(ho
%97)n#d`W=[-RrN`5SFF@iZ,dS_r.o@ocZKGs'PrPa=#,1kD!Tm[R?]o8X"c>Mf1B/jq![jjVHmPUqF2aX_LmSC.J'IVX`P^:BA&(
%[B)gk`aHhNM=-'PTGorJ<($>'4_G/k&De'`.T$GM0kXSr$/1U9WA2f&o7G5]%GE.oVt)u:aO;D72)uJRm,lDQWCIS5T!6.@;kncT
%fR;N64Mu2e?t=2`$Y9qG_$DmT=JcfWkE\T3jO!#QP^M-T$o`>UfF5A-kB<Z8;N9?$86kh$a!QA1&SIH%9lrUp-V50FG2*hoO)tC/
%UBC.In</E,lJ5.:d-mj7cp]AT.o8UNkXoe_E%RO@"sp*^Ynq`V^sH0qVm=o"eU)M6]jWBaHmDdO4aElG\NQj+MBFgC-RYoC)t8^:
%m#!HSQY@68:`&XWD2O7eEs:)jUT;OjRj>$^4:8Bg=X&>C\W2J:GALB>4tPRPm_V'>lXR.p#\BjHZ8]DZIH,^E%hjS%2U7*\-Fu/U
%.Sj58j&?(QgZ_+5(d,8jR6.B&9aa/k2U+0,/U\'2T'50L.#pHY#]UF=$g+@#k0+Vq@HJD,l[7eV3`na#qPL<2r761]/.(LVi/DZ8
%.<!Jnc0jX<OHVZhBjlf(J/ArP"U'I.&>L$.Js;Ze&*)lq-2Q>!Nk`_[Qe=4.FqG8\iOa82]d-`GUl8R:(i"0<j(5A@XW\(@9VP,0
%%sRPM+gIt1ATIW`q_l$&H+?sK2ej*S1n#,m/-Q-e]3`6;a.`9<q3E+-jHp,/UbrSW4GK=@XY]2&PoR,NNMWI$TPG^Nh4GltgnN2J
%)Q-%B65Jne[&nT`/_m]mG=E3^,Q%RP]ra)J@8!>9CAIqDX1B\GN81(Z!.`MG5^]Ah1hj:@nZY"[=j<ZL0K)>)!>i2?R'0_WM9nYp
%_i'TW)K*Y6!?1>JR(^?djabHDb$9LTag./3hZB:%Q.7/M.9e9kC>$6[FqI#Mio0/,Aa8%UoM^"HTP_X"l;a2b&;dD]Jp[lp&qquc
%C/*.n?qtNi=`GORU2h[AjNin;2<jc0HEr1-j0/&S_ScS%'bTT*HQD(DF+i`m)`L4I:kn><HWe]e"JDnp-dFD'`#tgMBP`P/d3.>h
%q-aF;@/(a_[TFV<YT#"`[QDOf(cL:f]:DQtfPHL2J#7iVY21GZ%MO7h%hrY+Aq5o3!bkn*=?.$]&3ck5'2_Y\/Apd=*fsfK7?^g"
%#`nK-T^Y_%s3AQY=Ome7UmYKL\<kQJ]1i:!UlJkpQk$s5UQb-CY_=ZCI0_s_FCEePj=DOL;GIQ/mI,nf;hK(0Jl;`,b#O;7cs$RK
%INgHi7?,`@C`Ambg@JM_Vig,!+t*PT[:(i:N;qB6iCPG[=%^f/.0<Re$<fFn0(GIOX>h#RUZ"'M%)k$MQcsEUG_n%9),)gO/VID/
%Ep9Ac)S9=5bUHr"4e03?]bb<sBr"I+d-&]u/RG/M:IZ]VB@g:YEA_L.a]2>n^5Q8<oj"50::-.'HJ*CL/+BG#6!lMNRkJ]ma\;0W
%<E@b^;7iJ[9*1a5Ohs3VFJ4?BpV6S7AJG!WiMZ(DS(D!hhgY=eT76V.h7Mig?OQQ%r/WL;0Bk_!c0NtW?TnGc.fSY:]/h'$l?@a*
%gI^1c^N*;&pHANAJ&+q\$FIQQV5p4orSp,SX0:JT^\\af/&Bn^SZh7\d!=RtQSBI#QD'H0<oi=%hpgIe`@c.0n9T3F([sX'a>#E;
%UDu38-R)B[i*LSoFp&X1!rXY7%@@LP,P2Y!^n(A7Vpq>j#XD3ET]sUJ&FP#;S@Y?VK`5R-=YP]b$7\S7/9m6YZ*A<JUbr;!aMg"M
%r0W=c2"8CCCUNYjZt1(ITK<X6_0_a#mEEfu3$?;[#t6=h&s#'td\W(,"*n+]AhW"QXoo8NU4\\7#G':t;iA1VC,'o:89#qt:+YOi
%]_$3NQt4tZbPP<rGuqLfo`,YrZitnBFVt^VXDSIHP!Qt_^Ka'a@19J4!sR($-^bNcgC]S9k)jD2QR>@h3U%0,,;0Cp0`Uc!_><r'
%.1V9-$lJ(RMaB[$>XL(ub`n*JBhkQ/(0nN+?#=&NTO2j6?VrJh-mX%O/qVh>P6hVFVt?!p6[4PncP%=[(1&fX4p<,3DnSniOUbi6
%En"gjfnKUJ-<i;&k&Ln<&Hc4<RFkE$C'i",Z9FqV8@D)!dTgopa%4c:Lf,W9cu/?`L8bP99Xpo1?7N@k.M%_R6H%lI]EDf.ZS$L@
%DXiGeZ75q91V$k&:35I?f*6JckfM\kj/F\"l,0m+nKO-(X?u=m@X?XW,jZNbagf/Tgp9<](4sIgeNhJ'hAi4;'.nn0QPI]<-4ro$
%*S0_G:3gD-Q7rn,JQU1"n`n&3cO`Uk]%f%gWoCkE'nLRFKdt#CW]Wij<HR;u;G's21K9l,7gZBn(Ak`R<eL4jkfK,d[V>l\PD*,]
%HT(-f_c*sq\=$1YT(h:j^b[EYKY]/b2eGBIkEeR\g/Ees-u,HLE^<)L@f[UhmbWX=,c%$rXiU);Da.X(Fb6r_&rf5TLhO>"Ln52n
%#'A2*&1/"uR30^O]1*#<&Q>!+^&0RAeblY<N$7gUj\FcY-M`aZGV]Q37bJ115sE:D,)7I:Rd$pjV+]#cco^E`!]jq:NIspo;^)HW
%j@fu\O)QIk]l#4>VCtT>+RWUal-">G470eA-[_V*BCQ-]d:FU>l%J6c1J_0V_(_7:8MYsT=t/(=CNeHdZ*kq2+h]"WVas^WDKF#Y
%>D^hSh'nm-o?m0jBBDhM7j:tUqpfT/UX@4Uh._3_!UV>h$b:Rf@kJ):?rJ`m\aDkP$.c@taONh'8qD(Ckcmg'-&/uuoJ:L:m"<mU
%d4uj1T?h;0Y1e4_HYlG['6nTDYDqV[dqbJ6Lp@RINuesCE/P9;ejW:DQ`>/e_F2UJ+83>Y2I./@&pPOmgOU0)9M4@g>,'J'IWnD^
%O9)(3MOTDEF@[^r;4'md$mHV0Pj'dC_gFEgNA=!:c/s\^<RG!@B]7`MF@*0U2kG.d7G);NJh!abM4d<o0l]Gj&?#;MY+`(AL(]0C
%X1k:6+D?NZbm=u;+AORkBM0UB*$AbEq=p-&%76"Y6fa`P:HBY`+-6^kFBcqXM7V.r9QCFV9+%W(]eVX[WL4g0.\_-8Sn262`%/+`
%L>1:,RN$B<IYD\7pU6K:duuO@gIl2\%n5gJ8mGCZ1Tf)mfh/E-=%;XbR5ebX-=UPs,tA\%pR0LDa$AdN2@:Rp)abp>4$aJHHTdi[
%9o]?gbYa(Qb9t`jm%/'Ois>!Wl@H(*m.A]C,eB^5>dZOn;;mJQ`!=AdC:m)u9ei70__Mfa9:RmO0u2B3q*9*T@7>4o$.6BO4j84K
%GmZ8'UuY1M8:'EeoZZ'b'\GphU5dH`d\S<Z[*+H>@jLp4aqnYFXbi^F2p>4&PQ7>3FJ"S5;\JApi`)u1)L+L0_U3>n?>)pl;=&b3
%JWQ`#YfD#dk06Sg6LL(/@DlEQ`bPXh1V/_GJ/2WAibMGA;[P>jd;c<*IaCP=U7$U[<ofi=,3nkMWa-a0ZI/6i7.!83SG7jJ+Y;tY
%>Cjc).OgX=W'+^MM06VaUgshm-_b8)OrHoh$B_1b4I\+qD2UrYU8Qg]c!FF\2$jC-%TR,3h*4;$,*j?rhj743R!=A+dS,JT>YVq$
%R_18hd)O[_Mc[Xhj%^tmp&XmqK.`PtCpWFGF4q>).-$36W4:OM;!bM@;V=*R/H)I/84K@pSMjia[80JMM-8m>J\D\Xs)#4Lcu\j9
%dSdRK6;N^p>KWju[*1!XD_bH%GZ@qpEC#=4oci`XbHdsP7jc6qnpTUe>_mbGA$'6.,D4BhTW:,AQu+WUYU:)54G9W*kGa1aRZ^bn
%O;LtT,E!XAK'a)uhjk5BAQ^u`F%Pp*&M[<6N)A7ak$"cV\7/!KWgRftFBn=S&4@dc#SW]35eK<-c_lcQ5_7o_-WhW-M_)(UB@(d_
%?#&T3(XP@BV^'+VkkgT-M3JnO6dV$];29rZ(@+9E=Es7b8F;f=V`Wjr@_/7u^nP?T.*G^9`JaMklX#aNZ0meM5f9G^P!5f^deJ@0
%_jXeN_HI=7"o6Zj[dGQi'H^-(Ki%r'0::&U*[^TCNKOL@On_C0pZgbj=rsUhPp,_(6sUD$R#2XS<YqV<Oo$A9&`Yb!RuSh@G*uI=
%$G\rF9UE/QRm-1ce[6aEf;9'G'ZbZC,ek[<;gDqr3C8l/jb-b/i>B_fk"-Z;R+GN!dWKCNU:#<L6.jS]Bj!RB7S]sa(,C(.CA/ag
%lUaQ4&qYFLPH60u^$O9i7:K[SU-/$i$S4QO+@MB,9if2s'23<"j@U*2-rl0^<LBt7MdM[NM$2neO0-$$`>['*,BmL((+[@#:.?3F
%a88lFeKVn-bg&g#VB]E'Z87WqFYL80XO'C"U>*$@=d&K`(WGI]<S+>(0rP.LVMhIfNb/8[OgR[o_!u\1E',MP'k1-G$<Ok4cJ#`G
%V(cab09c5Q9Y)7e1KgJd6(,MJ`'#B30HId%anZL=\f$S^"pX.uH?9qB,KQPjRLff&bJZs\/F5I]Rb>3-MnS0q&#\F8HhO,(lKHDD
%H?cH4/]i-AI!CNK/+L_pLM%NDf4E-8jpF1DR[eDbfi2St/_Q*85_g3pK,8"<5&7Q3FXihllbfqf&^j7,`K!,^2%j7m_8K<N[no@A
%9'QY1AMKi$dgS:#AE[Z8n.RI4+p^Vo&X,LZIF5E]`#:iPI@\]peAbYIoh6l5/Xi20LQ<K2kb'52#^D&G.QQ..<^eE[HODh;.UTa%
%,DK]@CR)c:BT@soW`UN$&5Rr-<@>guQE",J8c3G:*23e9AC'FO6Vu0QS=c#Ql#^>YhG)4_rHL"3Zk^h&8blh_*fNtESau?UMb4CX
%CD_@\f$MrF4k1ZUSn/AS/hdAFfB&+EW736\SO53g/#C,b;>htfA%?hIVhhNZN"Y55M0>aYIb68o)7P@,0g/#=^c<To+(it<C;`uZ
%GVq4bn>0#po6*a/Pba'DgS/HtcHWZUm$1(TS94+kc':e!,gr9tG/4Y<'oDGi;]FamdF1K2Q%t3!p]BRae`!EV+F^Pm0IeKJ4I<3+
%E>5B<K"mQ6V1K9-,Ua=NNp2A2&n9<e0_NX-X!jLMD7i4MHNdbXaPE"811LK"q/0rEoa3Yp/!:16?$.3$>;*=1/u+f7/hg"<`e&jQ
%\_.];:DF8V[HT@X$AsXM`#E?uBEie5YtTM0Qf+Zh@S=maY7Fcp8t"30=\\8Y<6:_uSSAb)YK@b9(f=H,n53W6:EB'QJgKFNjnL@4
%ira)DRE1S'XRbIE;R'K?6^qrFY;e<Yqm3:0g5LA_4;k>X8I8=eR`$'fY8p6uXJ0E?o`F/I;Y@U@@N6,m)FU(Rqq&?_RVWtDH8]#0
%'dntS&G);Nfu%._3Tq`03l#jh\dX23(RR97:C[e"B^mA.cK&/u*5Yk/*b7u:c!T<<DdhT_R,2HCk)5Qt$<<rZWsKh!INgUVJ5"9I
%5Gl.ljAV8Fd=EjelEk>9m@nBR]5E-j'Lrg&d`(KS(*kf?j%J[V0(1tuM41r8@W)i:nOi%T.aJ`UGGfNaaY"DD7Pk9KF@!@B[pR^6
%1)Lc&IiFjg(_@;`9?_LUc?.tOlsWEANr%a&F%-R0:`G_dMs5t4OkpYSLGf'Adg)).SQ?V+=r<2QP@Yt^S+=)QLZjt1T';8H*Ql9m
%^-#s`gR?A?hi"'Q.<6+HO,62=feqam)Fr+82@>SiK^)(%iaecgC9q!J2-SBSUG*b#pudpi`=Z-?<#F(epI%(2D'JuZm(HP?V8n]F
%HuHh#JN!f"&$G3QQe%F-[MqB&(Kg*C%0coc@Smoq>VoG3bN`I7ALpVfE`9Y_X*bDV1S4-`;0h`W2)<`1`/d/6.7]h`lHq$o(Zh>D
%66L<N=J,qRnS'2Pco%s+<cZ5'FBKWFr0fC_]U!q??K(&r+?m[:@Z;<N.=B>^hAO,?4,>AC&r:mKFQ)[VcaF5/_^ELU>U?Df=49]I
%ftIih8:WT4U8[$=$me-LNt78qA5%T[$rR%]hqh#$>?=B6,b].%J&dgF`$/8<R]g(FY[PFpc`!6SVCKgWrYXcd$^(Rc5r&r4(o#2h
%8p$>a#VjO>h/]qf.o?F=\kp5]_#$:LRSX=M>h+bsVrNm(j<b3%8J=jkkpdl'eTce8[!t\><=eE/SmO&gTa#s0S0g]]ph8/Eb:f4q
%4f0k\I6pobBbi6/gB\"\QcAUQ'2<jF[sPeEKGbHAq;rSB`q=@*5t=%hdZs$E"Ar;Q&T_'1YH3L%CnQV`l1gAo)B1(E(8?q&L,U;u
%mm)(8E_*B0d_IK_Q&-8F7na=tN1jd=NDZuVL&ae$Ln*r/cj4HaW$ktnb3Q"p9-`ah'o=RWiXC18iWLLccmY1#`hQW!_9(GhE'kpd
%>Su:FH2?q<@;[<:;Uk]^WpM+r0Sm^dZ:M9"2RQaVD95;sTO-(TrlTO@%9oqQr41>9.aZ5urYX5Fh<E^L#N^gQI`4jTRU.7kMMN1R
%a!R"ukO:H77N.JhKPk4L:rCVs'@a.\aq-8TE*h.md51,D8K8.4<#mW,?6p8M.1hU],HoH]EcA!.A!J+nq8hEdLHB!j[9SV-9q'1C
%ACn\$a>C*GG6FmgI3=HhLq7o.aqo`uqMo5e>j*r\!GP3-p5kg5ZNgX/'lZMHTWQ@T6ke_n@aGqBEr%%\<mFdpb"au#LF]&F$]\n#
%DOe[0A3K"6n0BG*0B3\g*Q9RLlE?.t!/%*/Hq'$[:([D,21RSgm!0@+%IHICaYXAqaoZdb1M5T>p88#rJjrhX/fE#6(+^RBO;c^R
%Z.:(CPn\"mJN][sXf<P/U`HsV1D.Q0Uu^C;EBit(Z]J4(CqV5OCQrTHksT4##`qo.`6("tj3+teS-F5lhNLqNn^&76p76RkW;*m@
%gQP642%Fl$LB?K&/$RljQCL#&(h1C#8!EZpLKSt"kiut]Hsp=Zej./&bSJMj_4Y?Wp73knOtbrS?l'iT;l4aeMF/)\;o4nk!eGK&
%i`%%/1VVc<qM$@PfV^?b_W@T"r>Tj*JWn4^*?)I"A%>CJh=D=rRT)#(nW9sKEmeBM#TE0O6qie[E[jp!.u7k>#]:$nbM%+Pf3WA)
%@?++/PK9\u.:5hrM\'u!Me?M&G=:F?!9$7<c@dg5$S$I!5;3oD^ut@64tq`GMhdsu71d.$(V?#<_^5skoAIj]rI/Hs&X#FrGP;=r
%oc6b:8.o3/n5^49>BH67jbObZH.7X]r$ND<)W?tqp2[49&(\RfYK_Vph3Y4_Cjt4n+/'bq>tn<bd=6j@eiP%4hgpW![L';(mNXla
%oV]](%Y-BYR<#fa;&-!M7%^6+c8DYWi&%='C;$OD08Hm,<c21'm7He=Ej,=qCB9\*LN)QAh7?5M3-A=cURHoC,soN.PnbaQ"[+)H
%.m?B45/<tmYlSa%fo9\Ymp9V^`b7gl-OuG8coDM6kXB$CDM]c0Pl&QjM9=&<OMOB'F:EY;#Tf9Qj?P^Wbp#RE6&`#`4tnnLE%76W
%d2GbJVG#:*&1(+V.$A:36rAB#W89!W'$Mm(*5\fkCG?Da/0ij&)<_5!SZtB/gR+stPm:S`ar\chl_Z*slR+I^AuDSe6$jYp%d/qM
%E"\n33a<IhoT_q9\qJ=1&(Q,9d8GtP)X&Nd3Ec(%g9P5Kc#CP*[!o!RfYAp#'b=<Qr#n\!Y@;gO!*ACMgonc9f"*MuIXdiPY0i7>
%Sk:=VbTqJXY,V$JXnc4mUB#CfYU.[F1oAX7$oe]?%b%K?SD?1Ro:5KVDL\Xjb!ZuI1-sS$?g&K6%\cND6rq@%Qafg"(]ahG"8oF1
%Wbda5"jt'1b=b8G,X[FP\oL4284Lut+sL&b&2M/"8Tq\lAIjX#R?+;VU-4=FH[cG[.R`=-D<dEqTg@?G-Hr,,ic7<5;B!1m.aHLD
%-+>.%gnC5'-)bb*:Bs_(Qo.)aId3*TZ@k9o)mM,fg+9_59>&0=d5u6N`lJc).2a-lZ:'AVP:DY;]SPf,l#fjcKo)h>F]$#@QpKdU
%`n?"5HK/Gp\2>sBAa[lR4m>crW-VC6#<eH5=X5V[=dN<\1m$n4q&)kp?&N1u;6;G+9FLTLn7l*Shnc>F)J#lZ=,]M0(_N;Hb/%g+
%Y6#H=&\Bk_"3]t2WR@I3KZ:6]3%b0L6q$-PaXQNV@:)bHS:43B*tIK0JQ^2'<f2sZkhP2]@lg3LU+Z`bHNUOeDg6#m#Q9+\PBL^I
%%g3nPM(k:H>4Qa'7=TRRc*PI*FhF1Y.<0PE$>+O5\0\*:ilng\":%<OoS,1]k[dNI&V[/bL570710r=ZLb<V4QTDIa>5\4&Yt-bb
%&UR0WLYO#E/</V0F'<9a]>`(F^.^.P4tMh3_%70!j2eQB6]gp],,VU:cpch8`mcb1qJ,R>OI$8Gil-,]q*D`m\ks([6^O[Lph_05
%&&b&Q>NkD<*T[pE%N1Q<7WK9Lj#Ius`kL*[@=_).F<:`?5X`R5T]a.m@740A5!KtqNchA<kb0V^-"K-*ZZJ<<Lq!'j/g*R4kin!]
%Ks:h$o,7ji$rS\r=nP$(ifYboHV:.+ZNno\*NhXukTM=e6NL&&Lb*W<K\=[EKtM8t9bpDm3(LM_%nO2=hP*0CC1Y?5]"]2mmR>e@
%,1d9gQICg*P+?YY!UM'EXlH^_:_9%!ME\'EZ02?4@6RLD?a*/-.G6Y>*o[7g?:TS4*pb>N*h9\a4nD^eGhIe\>X:5D:hU]k).W&9
%'H,oUr=&_li[<-;Wc<k0B%o1.c:r3r`M$iObp$f3=K^^A+Af9qdBdPX6Ogb=DB3d`3_W@\7%%.mVCT,%OpqF/6&Y\U64>DWOJehZ
%,d4HI.644o6V>7t;8@;7!-lKMPuJb9+m?""[V>B""R^uWCWH/N@4dR!5^5cT"k3*aMpm;M(#-VHBt)L,/%tcR@Z[-\;*^>)Zj)j*
%N3Y?:d6l38VH/=''9u4;nRsCLNsM_GD&EEgaW73eaW-HLFtt`\Ef3LCs3dG`n<dq)Tri&4/N),3*=(L9+r4O)j$Uqp#>V^h.u7F]
%F'Gi+;jK-hDe#laqVXL'#Y@'X)k2VNQo^WG(@)=]F""Wd>hRqZ`c64o!#AU+omN*M*#aE8(:WGV7`rGrnu(BF00NJCI4_Bj:"nrL
%6:eHpe'q+6i!p`-=5uTFV(4$<E_0s1!P[tc'@iQ(`mRgNR?f"jE0.eWcn$3]8LVffd5>1TC&+2`lkZqk"Jo"PnF!j+-V<ruBSgq[
%&7\<I=%I`1K*\e+aUK#d7%_pBMTMUVEtWac:Ues;kn4u96jlDA>YAACX)VW`YR*0nGmbil?L!kK9:'Vk&snog[um#Dh"([n,B&hY
%Oju4&7_B:NOL'SB,KQ1]il1O>7(E8eUp"J;K_)V>gPb=oQ0C1u_+W;kFoA%Z0tgf.X0A*nO\j=iR3YLIm]arOD?BpC7(cn9m3BQ>
%Ekd#d=G\P9:,A+qXh1ae9HkZ<O5pM+*eMBMP-?fn&"T+VBf7uUqGjER/cd>A$Qs9&_sr>.`d(:B`P.`=#jJ!b#>KjNSE5gE83hfh
%/(J/PaY%ES$K@':bSX1(<k(01JE&=n,E]O4Dj#d!0bNDfS&q%fJH0`Y&1op(lIP>G@U9o<6@91^K/*5`KP6;O707dk>$c9pnk?"O
%CX>%0-.ek-%525R_SYl(X.9?K@:%r6m)KY_S7"_J`%$@oa>2lh;1j[A2)#lk6E\Cc8DU#\ZNUD\D^nW%\%;CWPqM2O3j?aEBGSmm
%h1BiIM!XYnDZKiul+&"""i>Med+G9%9e=D0.oTe$=mA!_m3-3%+"quhTUDVT(WoVfU_3`6Whh;f6plCPG-?Zh*D$]9!iMRkltjf1
%VfW.M;l-o&9;1,VM,J00qC+s&0POF3\SPpn/4_)mB4""O:RkK4AO*SUW<I<S"H*\&jt147Oq;*,X.nGWic5ej+k`bf7f^qeo<"b4
%=2C(;K;M+Dmj/d:DM#^EfcgQp2%]r@UYs+Z/8f$W.M:5QZsu"l++k<-W^>'6M?&hkJW)Wj5p.V:X*VE1eSd:q&F$!naCET1F]'6I
%X@!d2WQ"g.bUl1pQ%dOS"f#gu,K[,]<G2\(-h2CZ_\'52GP:DcXG*YW*)R<U#A5.sK;=(,2;?e1$k^C_93n0Y%$!@LmN`PaPASDe
%1()PCc&1^`!N/\-.;1A`/.R-MH7IJ*TYW('Iou!?Su^QpS&@e[k;4HNcBOK^#-Cmf0C8G5P;BY?^A8iAJQ#Gm4e,Rs"*\U-M!mWf
%/Dqck7nSF%PKDiVV+`%E%4eT^*6[4#:m[/'#(>opPYMdaJ-)qp;DAdH&B;=9G=H?3g9Y26M?FZ6_):AAj!3Z:AC*\:`*dRXNsa*E
%]Fl(fCS;XdMdj%-$!E`<aKrohLjB,t3UCBZ:Po^R)6@f6N!N,[T0&Xm@FCWuBi(LF"A`#d(76j)GMqg]C&'lZEsBu9d5A7jf3KZZ
%jH*D;?W3ZC,[@o71PmcJoc=4d*0JkbMaF[/&ogE0F`+*nq7o>kc=sq-<-)O0.^Zd!RM$:u%KS8@pI@:n;HlIKec`WfN;43H<M<aL
%p9(X#ejD!EJ1L`dSXX'Xo"N,FGSD-<'UPG-r](5Z$g3BbOu';8`rJF`\BUG$8WC]23,og=T61/4_)75j+rfS;=Y:'STJ?I8Cbm_H
%;[-?6[Ue!W(SFL2elWF_O.thjZ&M1>Bh4,!on&J-Wkkd*dpkiI7,;Jb049ur+r;!4;%PI-^_qlpF:of`glc9)R";&[h0<&<k[PVl
%"eoOd-7osjiKaso6"iI3R-6."]ne*968;M_C5bku2Bbf%iIJC8[B4a4LoA<"0?>Wc)Wh=<e.@od#0][NUi:$!N0B2g)>I"Dc_'rj
%kLJhW83b^!=qq6[),c`)GPcgRpcXCr*.sglRZbX@]='?O/]AU#MjTQM&.*S4`UZrn'M/AhnOUK(];-#.QKALI6Ot`"++7>N+#8!@
%RTM6W%hVgq,)`IY@(3<#BUg#:#QpU3cqInB/1Cc?&VEipGN5)WJ_Pj\R#;B3JITUgP+gKMNTsaO*O6#RK_Lg<J;Ag#T&E%IHa=hr
%nT=Y;Z0$72B5BTV:e^#*R(;3u)OE;8,c(_;Z:4C>o1DXDYaQfe-jreLp##ngAs(2(\`1UH$kXce-ijE+f)bB(ZU@0J;kbRK.WJHd
%+[T5PQ?F*sM5$du2G7NPE;IqK-%^l-#\M9/@-e"UL]_1/S\Onf]8[KliU5<gBQ*LtW?98*N]?OA^^l,1q'9s\0A'bL&rq#iG;.M=
%3"`d3`^tm8\[Ll$_kpL&/S8(X_\?MP-dR4[<cWnJ([1rG7W\/K"\GRsR*J/q27Pf.>Fh_,ruDB).Wf25UQhY?4*58TUB$9,301>j
%NRS4<X]dr#1Egdl8[@CnlN$IcE@,E:H`*#I&,MMc2Mlrp;o"$dk]sTr&0g..d&d;c8]bdKj<[jb>:O@!^o;&YH&;gu8(Hkr)3:M(
%^5tHInTt^SN.&hIo\uVi?q*#b;`';n'Wmlq--etp(M=5h,rBRU?<[J$!8ALEr';7+CH=E^N1S9O?9`2X5X!^/@B(@9?qn%e&jp::
%=A;^ff9V6-T7@p2V1`R2[Y;$^UME3WlL_,EdS&R?Q&Fmla!OBjS_dnrX@s:eY\A-)8nL76FC,bkdiHpUCh3[8]==W:Sl9/ZPE/g"
%&,89``>oDPn\g>2X@Zj]W%C7u]\T/)-7n1e:A6(A+S@JQd,^#OfcGoV7Y<PRH=UU<Qq]P:M"+XS0aA+*95b`uZ]$Lf$WJM!&S-qj
%14f*0@=`"VcA!C?AfO2S[WSYPdER&6kGsbb*gtDN(V%2j&LOGf*@MO])uN`(4RDjp1`*J)X9e9-7:XSsVm!qMlHBBS)^NEW#gre5
%j\7D#OM'(@K84>MDJC/RLP]tPg*"q2U*-)le.U1h=(f!H,K2#=).$b_@"6!'OIPL$A/,+\cAE8#nWJ$)HsuTJDAJcc0d1]rfEa,%
%@$*-h[=X)md$#iKm\KX/FuQ+0hl6su+OtL]]iIl?7OQc:d\HIRGsbk/$s9lkc)F8&BC^I>lsgBsRHS.\E`'?!+M_!@P%`J*h[4ED
%/4>'[dFGH4`O)SYccZO/]gG0sjU^KbVAss-M%$+I>7^!3VPR7_NO&1VrWj-g-q#3!a#+-pZhJJ;P%JVX6p#DjDaWZ9<gF_+G6N2@
%1SOmHe2[*!0*pH(+jNZ`TW((V^alOt+s0Ym2DYLSo?"p34aGaI1,Xs5V?OKDlBpa9acM'^=UV8fOQa[ii5D;7<V0[-<QRq'lLMA<
%W5f!9^bP%Iiqt5+[mkJ6!<P[]hJa1Ib=&EWjZIm-8Cdt)nd472`)9Ui-_^oU@sEIjORl;[>k;&j.$U,/*D-tm4rFa_M?O%[94J!i
%7)C__b6"6>$nZ/Tde!:<(&!5[!PP/r/gRs-YE.g\4fi[IJfg3?!/=.d3V*B.9>GUX.n3bRfc<In6Z.Y'36ph*b[%H/+$lXpA0\`)
%ah8Lu='r$X/c"LOG=`YpU5%=)XY;8"j*'p*>0#pOUo.P(\G]'"<_*9l0>oCB+:R<n0:%sq;:;u4@lY]**:R-"'OY#e>3J#r+d>ql
%Zk-EmO%H#t+a,u89A"ff3"fplLHD0fdr9(\@`Gpfrdsf>bUR1+*Y.]ZM]%'QX[ZDVj4"X>RVIJ[M4ZTIAh+?l,mu[PNOsh_U1#j\
%Mh!IQRpJ3,f<!1[S'-kmH[D\BnDCfh,'%!^;)!DJHJ'P\WcpMq9S8/N>_sE[<D4Pg#]Jt'PA+uYDC"1NA//KdZl)Tn2H34KYZF_+
%nNYh<(eLUrF.Hns7,QAWrPUFeX,Ab8$.)\+k?0'QU=G[mQC$)YkZq&9rH&D&6lh78i_eZK>M^+;nju)lJ6kPm4-^6a[Z^4M\d%#E
%(TIRDK$kf-\OD@[2'<-=eJ#W(F?,7k95,SMF;UJ[Jg@;'D[*R3qjljM1W55YrGm#K!&tf9`iV5jO5,>\@B4;(P/niS0B-k=MZi&]
%P4#q2Q-M"Z8sa_WQrPQh1S:@bb82N^;Z&2BW@a+tZ.euoRVNKnb<6?P-C:g`S02SafM)V5&kNhg/T_tKX]@3\*1F3F?J5G^5FRZo
%)?ub*(149JgEiePAMdXf=:g+LF<k=X1(6i]Jg9Ju7gLJ?]2BP7o[KCGC8_H=.B_NDWW8TiBu1KL8en-PZuc!cW$\nB^9G>7<:7@`
%UZ/'^+Z=!c;8j[C<<>EgUc!3/d6!p.BYr$pC3`<2M!Wi[F-7nL9`cN(9\C('C+sparI2T8.F<Enn0%*A=CRPQK=\s,N1.%<l7XAU
%,R<_09$Af^<^MiQCMnFdk+*`-aV\VFVsf-l9&_BGioDq%+R=jiDOHf(DZ6@Q<7BD5M-Y<>-YLGl,Z9\EK$SW7hDr(#g`fQW7%BTC
%P>-k30q>>US&O5UN4tC?j\b&cKGZo(&U'POin+k!<-P"^&JeA8)Q:M53cBj,#t:J>$oKQX>BYu(%+SI_T"jM#8Qr?!%Rc(k1b6HU
%?.%4H=ejTEZcJhB[t<^eB5rPDE]NP+2*pCS6?U_V#_T"IS&?:O?\T,e4bblM>eqlFGCU52*G*Hh@GeX%Et/(?1]rIXb2Akn/0/gS
%%A%?EK#P^;(pN>T^S&&?M%4o@EPK)paF;tHfVdCaD<Hp]9nJ80]_'V(=[F0T1atUk8ATqo5YCM7AH:DCZ&&NE%VghE,!8SY6U',A
%]Ll!G\8>9/c1mBC:a?Ls3l%m9Z5?<F<Ohf=P_`+@LAQ-CJ)Gplg]f=Hk?0!VBj@YTha`Wo-`fYo,HItQ*nXJ1i@j%XGVf3Gi;a&L
%]WkfQL=,&=e.7k&M;)o_-tRuGh"$=,]>G,Th^4'gP>,@b*'GW\J=bD2gr$ZYAr9EO!gIum]P?fAQiq$V#h<AXP'&I2,1r3UbeGBe
%`:BK]o!I>A[2sq65m8!G`lgYn]C7V6]n_D[;G+D]c:fnB<ftq4)4[uN7u9.iBen2/rk;PRV(DEog:4tP52Q726l8?*HX^glo-nV;
%@-:D+B\Hf"Ql!1SpCQL+BKZY?R>4@%(*04Tg$*3mKF26?3&L?]H\?"H'^M8t$>)k`T!)fmd"YK#U2C]9-@=Z*W&]+H,K46WTsQ1e
%Frt3K'J>l;'C875$k?AS]GA:e>C`]O^3@R(CE@o1ND"e,jn2X2kGejXAlSE'GqltI$$qM/&XG^/R##Bu"HU>l>[%&Z[N5Gn8N_KK
%8.81Fbr'[=oZ:,/^]5G7kYDI",%<H/[m;6$%P=VMlfqPt_/PQ+<N9a*-`K"N6O19=g-K%1rqKLFU!__/(g&5gjr!J#V?E':&BBub
%k</'R;Rlq3fgI^=-=LX%^FdFs2DQDS&<d0*RO6:;Ajt".784'1,)f2\j61dAHsg3%/'sF</\[?t5=%FD=S#c*kKQ\c-Q2#m[FuPQ
%(>OqMN/FiUi-1aC5os<b3JUJ/DD,Tg&2+\1^4h/M6I-N=0s/mACQ,a:7g#($^slO;NV8<AB)6ppbB>"7,tZ3`3Q9jN#XDJ=O`t1r
%9BB4.]+3K5.f,VS9lO.9,:cDF,_ItkaL5SP)1FU9)A,h&T34f"['#u#Y"OO3f]=7E)%>IUH@olbM`4K-.#C(%5rFQ+0jQYC<>U]^
%#Dbk!(h:GKJ-.lj&ab:(Hbe/jEug(>\Ou3e$O.-iELG>\\A3a$aq8K=SNT,NRlCV#]OX"?\22uV<@9&fnUQN@eYF8LfPoWpHjW!!
%#NuZ9J0(^DX39=bbuo$Q[9YQaKJTMYVAo_c>c-:lT3LJ71b(W>_8:HAJjg.3c@q*IfqX8d`An5W>H^FhG[3!f@$\)>>.!C*k[5q_
%CsZVrA4RAti[k._JQDD>-D4*'HmAEK2["c72740>crRF))jPdNlqM0G?M..b6.aXZ<Z+[dMd89Wld99`M"srEE*aCq4l`V`gnTRu
%Al0Li^j;G)de@u$.ZeeJ78sY0$1s>a?mYs(KZbBDJDC<.jN]=S)EJG1#t*QKLU?-0nD/-)/+FO'P@6&UKWg(sCspO(V`Q`R.;d9X
%dan#7RL30'Sj6q;b9XMe+_l-^RUA][cYi0Mi(T:s0g`k1_W'&t!ZdZ3S,GNopu4:I6APmV9_8(&B!P!$Hod>4JY00CbP2;2WRRRh
%F;j!M**Q\44sNt@:qiIAoEc\ge1tQj!fKn)qSE=p0N<BGkmhc6O.pF?n4"TA#)q\L0pcA+AYLC^_L`>T\-,oQV3$JkTXAXU!U,E:
%dO9>Z-><akX]k\sb_["e5t0amA7_Hj(_[m[=s8egA__or8QlY]6A*e88PA+A?22NRJ/A,M(^$NRC)4`1mM#lY0p,cup8MUR(`4KG
%=V&bF&JJd?BZ%^mU_G"V*8E*J`'"C;;9B)OrpUcG2N1K)l),nT9PCM'/Y.t)B^hfo.n"1Z#@pcFFl3]#'Dl*5JVEbL,AJ]Pat*6#
%d6!kpdI<-"2&uGqG6T/<N"A2U">7JC@j]oq6foO8[AoU7AD;'ZQBq)9$tX==RaY7oDPPbT/Rmd%2L:uZZW\[r4M`4*M$VCo;&t^R
%iVlGi-jB=(1iquk)fK;IE.4s$W(9pLE#/&+S2KiOo,&YsDq;/ZMhjbl-8.8<qY!YeO'/,Z5[O,Q`o8aU2L-+"6-$m/`"d"Y+]R_H
%omq!PX>J)Y7@\)1X>@e:$Q%t9"B:$('>kHcAk#df6s='[qhlNE8Ds2REqSX()E0MjPDG#qdGPH#+YNa<ob%omR4h2R>i,]c=JoG%
%,uF^o[+p?ZY'jU\$ZosA!1Ur\qc0jO*j@r@qc\sB'1*dM>B\<H3bUMb?:[/oW"_OE+0fI:i6W)DRB=\gL5C_/7CR*T**[3@*UZ1T
%G-f,2#S@q-=mV6U@,64tXB!n>,(6dgqW9Nr#[/c'M,(W*J$U6Ok`@p,<%F(?.0[0!XLccRki7,CJ6!JSCZOPAfaPK"h@)NUCkI_r
%o3o)W[T!M=d)Ss`CH'RpHE+'!)*-#lBe;?mJ`+^+'S><!S67a^q1d90iRV^_E&(]X.\S7A&Yk*]/h&""d)0V)(p@Zf7u#A*J@S]B
%.bR4[=tJE',<`Gi@^:.Y9`:V5IMfgunf$"oR;R;XS5rCoKIT2*O(#l?Vufu\(^([5k,kbl7_&`0BFcNr>XYhqeR\K[H1<6nI1!JU
%MIa::(HcSp`Z=(Dl<m4We0<:]K,p*!O$]A(i(d"XQ#[T^HfZ](f3lI>"tjDFbFQPg<EXCoE@h>#Li24+eh81Pc5T?njr.SNIUp"\
%F[j;DIAN+!e\7[<2H\60gOJ?D1`Uu^n>P(G`ms_TcuMY"EA>k%aY7+<:c7WMC*l?iZWX\JX'B"PN6_tHXQ9dF`cHr<Y%;Mp51FkB
%UW$a[YtAL4+IE;NNM]iY0[gom8<%CSW[dW4A'gts-aM]SdQ94XfNii_Z]N\A>0UYU!s&=7fMU:-V>@"G/JFP))@$M`'\Md&>M<pL
%nZPhcBS$XJ9bEXu(m83AjT,cG!f^LQqaC($j(J%,55ZaWC'Y)(Y#(h&2RBhk-<(,Qf1++\-IfC3SsV)aXpJ*G71'uHAm5L8=3M:,
%V:V_odD7WPWKIshl]st9oInJn(D^:>0V)q0HpSA#?G(/jB6@[r8o([4,&4>I"Zco$;o`C\_@JCOOO@9k[":_;]rD9`?_.#V$D7f4
%l*AgeK*?`9*ST@JPH#RRGaV+mSJ&dj+O6e,dd9;&'fk\?5be@Ap+hO`dZisljLZDO8_15<&d>qLKO/=A<AAcUeh,U^D/O+fU028X
%ZUMla.K?]+Gc)gOD>mN&-DD^YCt^]+HeQr#k:NWNL-B6I?AjfFY4dGWE)L>OJ><__]TsV=7%<m!U*o$!C!%=DrMh03I7cHuTjhCr
%T3DXcHOs$eM2F5o3O*+;(M9rQd1M%;"C+8XB>l_^$,U,$d'oB8WLul"^$"=3A'g]Q/Z42pn3lUa=nT:]e3aD[SjB/W9uYP]ToKa_
%WgtjDX=3nn26<c.)+$_m!+:86F=lfV%)cn+'U,<EMAIL>@=G;RA!0[PYa+Y!>E"!T<;L?Js9;uOLj-gD]L<r&??/2XK,BR&H
%/#'YCQiMG?j\H_)V_?k0&!4]/`%5,T#meo&cuj'<$%N%q>YmSF"sfRUrg!`%;c_"t2^l$Wdrju6"\^kd0;sK_%2"@)]%42sN^^d;
%,K^,l&!HZ<eNa,b1CGH`h7D_%4$oG;_29l75lHO"21=#epmQ&7OE?tF)sE]QQ<cpQ3rT;]i9Qr;(2;A(pI'iJ=QL9o>q4Z(D-?)-
%ZQ#A8&19JR=bo3C]aa*ZH3G2e]mX$i%&aqIggeCu%Oc^uPI2*R((2h'TJor53]da"[D);^X[t86AkO;MY8*;74A>T$/*a*(l"hPY
%n,^Ge=Qpa$"-Y<;A)\Qd2aYj=BF0hi`,`cbCLZ%u_4[B#4BIh:Zn?S7YDj+D4A9^[H@/j+Dua"2"q)'W!_T]/.3\qraqCSm&Ja;`
%TN;$&C^#?RRU=D0PJJq*15EGB)>^Bb%koJ)'<5Y0@hg4*.B&\O&I_f^C4PplM29':N?79'OR]s'>G_H3D8]DELd%M>V;<8c.)F)>
%HqB`.Z06H;<D7\`+kRbKeq=s:#e?Q2?V"Sp*dd%8[ql,l1Rtke>7F6&l!@VAbCR0f>+hdl`I@7UQYJHnXtR%./L'4$McklXE./I8
%A79pTDlk[/K.4>%W[DA#=k^C8f0dSM93BW?3^A'1/fB9CY(,o`,LT#p@RNL<[[%XTP>@+aeY+alR^k\H1CRNGj*2`$Sg*Ur%'JX%
%?Nj8FC0N-Gb^`'H1Ig`uODmo!>HqY1S5!;;Kr%-f6je/AU*<9cM(-Vh1JCq&E/#u=Zi\sP)GKb]/\'GkMlmb-3`HPX6-Vu!817R&
%<6>0#H>r-bi7_oL0-O@T8pJ"?OG7\mBb9;[G,Qg%Fh\jcS<qqeKcm5qj#*uUMkn:A!F+qG67]jl[-/4GJuAc1UG%s;\3/Q(&`ERV
%.s=er.4"2sMPZd2a$^6X(3n'@<IjH>a@$oH=/);f,huMI;jqGr1L?FODcHOg^.ddW!8S6[R%kNjM2rq3,@msU`/6$pg#='`#MUCP
%2eaVP*+l\9$@M(.c5uV[5fu3qBa&)r4*e_JN!87/SOG^7@W\=^;kgZ_]/XCooO3/Lo]2Bk65iTe;8e<jbT1eIX>"u3HqfBnG:cF]
%jQo/cG6CAK%mfcq\_a)1]f!)!K4_bu(*02f/d!0dI/7K0_mW/?MH)o/(5OuUY9]0_&0FH6)!H7)DOL2^_E[D`QA26?R2I1NS9?nG
%q%ufWf-Le=[2FXtSa@1mR>hD%5/![J0%JDeQ:.T8?-ZU@O!^SYGV"ekkoe-\BuW"Q*CgK$X9fWCq+,atpQ@_;H1e<O?%Q2ch%9ik
%F#%=K>5_7(SQ?DGKZNSZN$AGuP73,%j[0]i]Fo+`ib+i@Zt-"!o?1joe2W\_QZROkX)^e?aB,ZV-PU>$Ml7NM$Uuj.*KZ$,FM->1
%CGE%EJZ_<Z\q>H\E?WqC"Z)9u8[PY>%C27+-?HlOp<2God1.Wmo%,ZR97(1l'GD'Y<:d4bgm/&>i.bL*7I4Z4#00cN-D2!<NBn^0
%nmYU:Le"?\X$BT&JcobuL,d&H=UHdS+s9G:eoB(UV?TMQ,idLrTTM4`6P8FBC5th[l6)POWS1"8QE5H4S?ZF5`5eNd&e>hh3Hio-
%6a%q!:+'=@;Y2*3_2VJ_Js^R`(V*b`e^`F&m8-;<16Tfd_Q2ft^0klF`dFJgX80ZE9'46I"'\1E+E:5&h\3j4WFbCRHP)2ZR@Trj
%[CQp4%e4MWf,(K?P=HDH88MI:/HE8BLhafe[r=f<7$FUFR\j;rl6jtOD3@sT#4,-ikYJ37gp*JSKCt];PB$Q9MB:7MQkb\)jtHs,
%KeQmF[S8aI2WR<.@;DohppHh?9)("n"]fnnEL`2K-UEbi%D1#9#LNlV($((q7@MWZXXnK$99dJiCZIpmHJ]^RU$kpcE+]L\;@/`G
%#H"0$$KN2<c^#ZpTe0XZ8,BtHXI844Ak\+5&FlK[pS'(-BLRRX5*(MgQ:5;c)%g6@;h1qn4]'@i(b8_BYeX4#YR+t`*'5Y469`,$
%M$R+j.h*?Wa?2ePZU)6DICb;?`A(9E6SJu2Fpq`2>qK8U]u3`7;UC"<*!,HnO\[;dG/JBo1$=Q6H;n^;VXsK_`&,n^.-gG(H72E\
%=Gp-:nE:j(S[s/J0Qdf"N1aetg?+'N*cX%o<jVqOODj]K"L!uC-9#("Qq3)8QJEaq/*tp$7l+>[!hCG]X3T5HHE<Qp`X*Ld@S'[J
%BZOk[]cbkR&i^ToM"Yp+gM9iQ,L&?AMWDXU=9LriW+e;Cdiq6V(*<3@d5?0&!]WA*89ANf1-.5);\'?h'YZFs/CHS=d4>`kn58Nj
%V7Q:1ZiC.^=j)0^eLHq%oVso!r0I"%Z*e7JW@/Nlbm#D>Ti4DjM3-j#rR<j@M>)mig3-0EKI+GI+ur7-<E9(r/B-p8;L.].j=SZg
%;FG>0(-mnI0.\a7:+@#O.!s)*STFD/S5DRq/Li@U!Vt))g^#*:ah0qNZSJR&*[2p_#SRVISap_GD<c;L)Tm`@B*tJjSABJE])lWB
%p8BRTRihs]C0s@rP39bCkeip!0'`L\]:s!/\s6020llLoJf5bKM5Z+O=&je8\?nA=9-luoehki*B;*[gTXHK*18GZ_P-SdrP0!nS
%5`)h1kFN@H\N,R)A8o_[`&XDIPl]&2olK2oNu4<QDJ+c%;H-<O'O/\4+X4VLVs#6pd<HQacm^0l$ad/b-'Lcl<.)jXdp,B5=O%Q:
%G&6ec"`"jXO>?#?ZkoAl]5/5=DQd4E:^uf@D92M8]]HL+qF!_;XYj3[E&qBL:h>86"-Vo=aJ($%X"HcF+rJV*d*RmfMkiV$R(Ybd
%.0QN@@I?.4Uo6F57$#\1f/.81#FrPlB<gj9`M&RRT[l20:@iq?P3i85:pausWj=F+L\@3L&TlWkinbUQa2@t;IMm%RLR,l1]O5Ph
%0'doG(i$pW-E>%G4/0)qEI>p8"&1DE<EF#%nMSg88i,:jj,rkp9n5=W&W4NI2dd9'_Fgef(l%E3ZXK=n`+tK!'i$k0CUs!2BF.cO
%HC^#<:Y@<DH',?r['i[qi"`X>C(Rlmq2]=t7E^o@RALN1\qqAQ&A=\,,cTYO_jrRU'2h56b%44,V9dCrOpj!7X!P#Q0gVM?KT;Ro
%\q63?TU"VtcieuSFKfH;-_qA#r3Fg_,uht6E*Bq@8?!Ak%3CsBAo5XbG3_F+,_jODKnP<8>g]"o:Ak+pGniCJD6r%fHO+'r@(JY6
%Y')Xd2"UgfO28?-LLSIqo66"h\P[W<A-tS$aHH.2*@N$hZS"CU6'MkIKM)=:6;dE_GcDT`8eM02lG1j##ne"0.?BlCe$<WC.O0F7
%V$GRB-TmI!)S6Y@L,$+Z]D5hk/T_gFCFS;)<@FY$$?C5JkV^(2>91',f*Q.0(BGIhr&uY9AjaiQ<['#qQ'=`K$9l>Hfn;(:iu</>
%j%B[)$hi6L_Qq+$%(bFojts-37UQGJV,^>=T7FDmQAN.3UtA!Y:,W4Z3T_Oq>UTZ5;k@.qgY"V)_JU1$L*8?MTA:\$Y^=;;nG"EQ
%a"08(81!ms&'#pQ^3s=`?"7ucC,X+MWt)pUWc"U98A#Z[iHk"MJ0n99EOX9rZ`3E3`2n2,dB,Z:6,h>_[1XLsr/%@eTU1<YlSr%P
%3cDg"0#/&)BQ8WL'J+>Y@(#El1)#OT#_j2[XiN,49")s4/pU)J!_(5*,$Xo<e$'J-7AKhb!>["];JHTdDh^0eJKG5d>aBA@^`um<
%@amHi[QhP[kUU$[5tYp591M5\Y@Kum-_Rs];2T%,m<F7\@JoAc__<pbC9O%+6<.L$EkcGp1lVt+;&;caqpiB>N`q.CC`[(Tj0[Ma
%hlChr$:W3\!e)?ZB;>F%dBhA^ef);>2R+18$>@;.2<l$E=d1Z\,/+b);j&.Q^h_gLJIKh<O_,4q*JuSZWjT-(*7`d*'UkgD+^@BK
%4K?%=IME''Ze@Qf;0tj,7>5Xa9\$>?%'nS[\-#%aV&@R)1F1!I+:\bK)&-6"W.YH_QA2DA>aQJYSWmC=Eenj(hCC!3M+Y.XW-CmL
%TS:4<VQeZhX^XXO@O:;e;*)\@CrBp#=_P*HBcP=clJWIB`b?SJUnDfWc1)E-&'W\_QnPES;M9e%VjO(b:^2rf69LO0PbhF/E5_lM
%S<`!nKkQ9P.Z;;%bAM'OF4$CpYl"Vf]/9[NCo@X-/;oJ?ms(`_VbRH1mM4T:F=6J4<:#2\/9Kk:'\MmIH[P.@'6CPoi1)fNBof*q
%I?TJMee09pC"U)6ipQB6ag>ga/'\@:/%RAG1LA5ZN!n5@]2iZ.o?Z_C'OMnZ`ERb'r5N/mZmrB(+"g:)8>pJh_*,;V_R@:P6"5UM
%A9U<$i+H;K,UZpJq7PH/7,<+QTqZ4Gl324KS[$FJ47F&h0'mT/]e_IOimA&[BH:*i!LDD9FcY4$.$JF'>:N(C@u+$P1s0bn@Nl#c
%i%7$IcOkJR*Wp7Q+le63_UoPJ4U!73aA:;d.uDYqm/j<d=YqrcfUZ'V99X@/C1<fEV+[F6^Ht^CKI&3(Q/5@7DG:-b="rq?A*?7J
%@=;p+T6d,]PoHJ>"T/=tdi[Ac>;r/Pp?LsaigIM;HGN5SbAq0\*k"X-L+%a'@@u+mY%T/106rB=GYI8hdaFNJ+Km3YC+2f$#n_#I
%*^`Eb'cnVD2NOlrTp^ZU!'u#B';WX:+PK\[R@W4FjY\VWK/>tM>W;7n.G2F&K,#AY-K,R&#k-=B"hsZDPJ\)j`bO&6"<D3"?!*s:
%Q(;p_qYukY#22<Y?6CjH;\W#%7IE2c:AsVP_7iX)perHn=qO:d]BXqU9;1`U*K&TTjI3Ob/.PskG%=1/E)$R>UR?4lE-O6;OJ\*O
%I@B+$%PP6?/Bc(:Qtjs;L43<l%/&+mogjBbK6TuNYT=@JaT8'TTYe/mUK<oig^0;I=4a-5-4`UDc?sJa]HJoA$[6hN,mOD_c&f'<
%,-Ut%d\QXQDrtT&(IOC8RU9TU\3LVL"J9SB-^<:%+;-1K,WYK/5NO0.cQ)nP0>eNBk=tBc0BP%#X_K5WHd`!W68GR0s4sf-?NN0U
%2a!gS&^1CC<ad?=>?X!fXU`na-K6VoP8hDr&XL3FFao-MR5N6*4FUn?;3=Ggk2Q)RS-E4Qh<Kq34jT5Tq74=8Pjqqg@g,gG]C=GI
%RP$UD1cD/Yc(aO\_!8EUf"kRi,4=-eYSICOU4n\5C/4qL;HC/#lToSXYt)j-7k=4j%%'=S`a(.?Ffp@$lH=,W)9P.Ir$YIa_)%s-
%Gs(0q:<$,BVs(rW**97La%N,A"Pj&El9l*G$cXKR61ah3$hZggpd6tKS\!Ak?Jkg?DGN`(;Vs==!RPh.S.r$#>M<!*C;cj-0.3YL
%knS#NOOK-)IU.>4H;hHcbKmO^;SqosE(Z&Ec#KBXSr\EOD,aE%#q>Tj+:*PZW'k'Z-H>=Dd"-u*;%\B8U/M;r$0ZAdR>7nGYFR+\
%FB$Hq_;JkGfI5,!dUH(Q_#CZF=R+Si,o2'3qE[)VJ2`D28uO7=l5HgjXM4Z"_83Y#:^.SK:-bRqJ-UpnS;_;(^0*8n`QQ*'Se:H-
%VF-Hd0VAY<1/#W&_?AS]JV[`s-](#3)A((^Y]RksJ4r41EucSkQ?-DAh%;/"'0U$bNJNf1M?.G4nn9iJTl[;l2FbnIpAg6]Q[-J0
%,*H]l!9M3=g`J,BP,;hgf8/mmQBI4s?Cb<d4%$8ch-[$L"?gN863[ROL]C>SDuQ7qfut?dHE,ni!CV)FG<-BLGXY#AXj64E9J:AZ
%9T-3LX<B@I+PEKWllup!N(%>5'iUk?\q=9n6^`6#CrR*?`o"'eI]K`p\7#tE0"#1EdDGdl!K:$:RT^;9j75&ZjX)Zq%3\aQ99sWb
%/0S/?:2P,7p)`21FAU3UL<N6CbAM<Eal"O[BH7+lZ\$GRSi3>`G^YiKKF(5lR+]R$M11k_pCMfc.SPaAn@V+H,u?LT^lEP_Jq^1M
%GT2Kqn6`L?0l!=G2fOB$')\TIJ"/!!ph!^POM6IMi@M%Ih[5V7ArcKrV.1'G7F:\4T=Cm2d'q]_lTR&$=Rr\l*f>T>P".8i!MUCS
%O'.@f+fG/OT?LqH$>TkdPpR@].lW.Z"niAh9f`mHg;kZ&YpG(gU`&kt:GZUBjB9Lf6@:*3*K@mFlag6IR9l"/A?[m2!(bUl6=S21
%W#u[6ej50aiJG^@LhOs3DF`np%K[S2G3.#V$5?2mH8IHZYV1H-4!oNG6?`fHD;q%-p2V+P<g`#''jW9HdR83cR'\\^eiXc42K3[A
%]PL#CK0`aL1e*=S?rsD*`":`="p]OT!n9Ur3@5kGjc1&.-)%q^9=8jc-n@$)LrB(cLo3S0^dHd13Ln&A')Yd]e=)T?)aceq,>jYM
%I[r4eB(.KBFK1V(`N0]R1b(H8CZl24]3-H\m?rP\[q#IpI79p!Ttr!]P-Aa.>L!(5O=k4`"kh;EG&A'R6S54u7XG_e7`Ia%gJ+%Z
%9o\<>fM9tsR<MUTBDf5N)9=V&\QW"-B'KTZa!n;rP;!ImC+5U[<5p//<aaR'bOdKBnA`EiN#rK]<:kfD>+Xu97A?adP@J>^>md:o
%#.j#,Bne*2R#i%u1b3&Bh(737.cdZZS9Qp$oU38NW4)I&@6$1sW:,D,).>#AP)B`G4'O>YWm3@_g^H`&5;B5>SUX]O\&P)k^=b[5
%JHtI.^8/^K/@d]ecb:UHn2g2A1GBqG^_1e>:Z]T"1UN#$LbZ]Q<0iVDB@q<C@Vi@u:X30bQlAY-[5BV"ka#T%"Xdk"n>J7e&J\ad
%(.`("YIMVJ_N?iG+*%`a:)2,^Pt`!e].JS;/5n!<8OMl"P'V[!f`\u]HLMr->qoHF!Gb^b&LBj'kd;jWAcq72_A>qMd-QS<b_k(d
%KuZ*VrCP+`6dsAO)b*o4MRmCJfYsjIor3hn3l).JTKQDi0&]0Z5[hLXhEeb2&[#7o]/Gd/n*.[N-ZKVsV53pJQ_7,1!m2s;.pLM9
%"WtKmqg:HS:ZAJ\AI#rHe?2C-4u)*%Vch`:_G;['`Z-RLR!].&d6tq9/jUE@]l7<MGgVsI/$i^gp:7RN`n6gh;*`;H&3p489Yo4Q
%$A!?Q*-5:gcrm:]"XdusDCQi>:9M"Ye?#=l"o;(7,HTG5^;2BYB`+/4LME4[CE+P<0V2`2phH)u=+4j^!4)a*eCXrr"$&1B/qC8N
%642"T=S%BfcZ-HAi@5Bda5L'D^dHJ[.;A$tT/#_a,#g]V)qC^'9&A"W)e+A2l96R/bT6iHH<W5SEoG9KK9g?2^%`\N#H^uqT0m:/
%ZG]cSF<`,U#a)4*hs8*MNk,ePE1LY"O@uNnT%sI[5TXNMA=UAm)LPK9,,1AOa;SDn*<_?HGCU$5@YD'c1YeD^c1YlH1doqNPVfp3
%CD:@L)&QD.(c1e@O2IP*eVMJ'Vs".q51AI](!$scJljP'F_8)<kl]jGl\`^+)Js0:E24p9m+Bod5B$5c>Xi^5;IeLfm)Vg+CT/UK
%EQPNonT'/tXYAi^'d`ZkZ^_BS.PaPMd5-dcU7u'FPJHpGgl:D`"EQ!n\O!_pn2B':$j(X%U1c6E9'.S.$<qI+#:L/-f?Ncnel?b"
%4J3Dm:7=]"REJX/bY)rUXRR7\"(I^cGT=0q5,#]qEu'DfjAiW0`Z\Cn_g[).+/p#f_n]qTk>3!qL*F?f3c#gn=:`&l[pr!W=DGZk
%lH?$O1MVpp3hM7`#oXE0kmMr)?QX/HMmZ`1WLj?6(1K&[ib0/$rl??'jo>4=l'<WKNK/N4rp;#q!'=sH&6<NWe[..^UP+H&M^:a>
%lA2$*4]-,0JUFs7ED]ZjmZ518o@/V2<$Z_Akd&0L0c2bP&lf/%l=;"KPRcYk6*TDF3L*ea5ko[J4C3c:@.jXbKF!0m!*g!uP7Xc4
%?HU2mR;@po>@4'/,if+bS!@MZ@5>IuXHX7*Q9?/m6:h7SRb0$e)DlT+JO0HXk<bT?[u<Q*6NeSZND5=;Q+T@<`G3=Ql`feXZ4o=E
%HF3&@(/kob@Z(\'W[P:`UJJ3Mb8/-ZoP_??c&rL6>`8&A+u^'jYAZ[9XU`l$;RH3/<,kY'#1!"(@-%`*"j$!22f>RjGD9MR1ZR.K
%i+tIX2l9`G8tgol3O,.dbg<:7)@><Gb2`\:o_1DT]kG5iBT!!I?%VJokb0t($WM7'AjkEHMEATqQYJUA;Zq67Q2tiY)iQ<"%J&8"
%Qt7$T(u+R9ln%R8L(;OY%Qd)n4U:I"6],(cY\p]f/u#^4f@[BiB%u5"[E_*@$9VJL6a-p$8Q<l7O,Ros5>!TrE#>roa01t#P=2aI
%+U*V)&Bl>D^RMbf"\<$K4I[p]H*gDqAYth"e9bZ@lhHTkjsR[UMZ+8re+\7LEVc5TeoaT:.*:^W50\4:U:^Oa)qmY<CmJI3Tb$e%
%Sff]t2FQ/BJu2hGL!(A2dK'C"i@GIn6UIdnU4VfT4>0oClE[Wgq%E<<E,$79f1^:_=V74D90n]Q_"D9Fdd\R\cfle"kD98@IVD8O
%9>EgH4s;.a%,7kb7$.*`3(448aMU.dK=J-XYn)e%XpeG:&/N8N.@3MBd#_'FWN?GLLAKd:[RX7?bdW-K5mOJ,lErs:0$1reEG#P]
%7Ydf+>GH8*"TePsqgO@c]5_E_:ZaUpYbJK/)h,isW!6'gqar'L-0U5kS2J'"edUhu!2qH/$OJ!5V.WnH]4m4H_K^6:)bD%t6Y`iq
%&e-l:p]^^319/@$-`T3`k=rmH5gfqmep#bN.s+WrZgd.N>GgIt?l=O!$ePd1fYIXlFA4MT.eY-e\5m>1c2t1(+WB?7'rT/rE!\$6
%"N5gmCW9^XPC%tg_aLQDR$NW#"aZONCSnO8lnMc>=)eA#M()km-6iKqM4*:*F,#F2[&R02R+;(tR.IrAq'G5iRLdL-j#W<[V7r2O
%/uYH]<m3%.gU)@;3O=Y-C-kTX4?>X;Q\]g[>cen\Y*"k%aXh0&0FS<1G7u7jpN+"60+?QC^%qk8i3C$tor8&QK89OD<7HU3>]0<O
%I\2gICT"Y2#D[=I,CY,T!2n#s1lj>?2GMo^0^=I]'+KD$###mULuhTTm(^MJ;<)DbA1G1\NWWoQ(jsHm!N_#`9mTKJ:btIXZnbZo
%*-92<9f#SoC#$!dD/mC+R!$UQWC01,<N(p+$h_:Pk-692O,!S6)9GlIZ'&+m\k3+&U1KS$"99E+gro5n2-QrH:)_g1D/3AW6:nFC
%^duF?ZP7D1#9cUbTjQ'<,j7cBJHI$hE0fQ-?4e>+L04t!hH\A&[&Tu0d5WOIF+4\co%iQbek`=t'&=GSnc_hj#Xr-C).^Y^=e19c
%B*//iVPi+4aWR<"SDBMc<$D+X9;q*O=hk@e6F\#J&jnD028.&GXXR^!NRuNZb>r75L-7ML\TE7f3,9q^:gilpRHk^WSY9T6;IP,s
%'f&ia*j?p8!B4l.gFF6P;Tr"qOmCnuC%_a/dt$4SAjl.e&[*'[^n2-H'XRul(o>)I@u/l4<8=-&)QM1p#NIXk$8ioH[ERco*rOlq
%ALR/dj[PU*qrE"Y/)"3:>ulq@bbEY^HCc5OL8WrU@cnBr$tH^mVE.2\f#+7/"PHah9#W@Pb5NarM<@9d#Q7F/NaN@"+]Y+KE<V_4
%37C`1ar3ueM2F@2'Go*q"9$mO;aO9A!oiP3-n.GaF44b]-)]`S@kZBN;,HF^&:<F,,Mg\0B>r?+<`uZK+[dFtA;H&ud*]FVBiN2o
%:'U1Xb#TkigTd/a`?213W6uN8-1*N.'IF@,=7*O;]\`fY[b6>8H)=ge/V7I9$H9h%JEJ'9V'J##+,K,WTt=LQ1T!f+D2O,`+_$2K
%YIYV!U=G'hJBpD)X+2,-EISBXC0*fEp2MJ,kd.&n&Q',k\O*9H7YG;pG*A/#(SW7hP9_8nL5;e=UJ/$?lN$:%1_E)A6JbknOA$gt
%B9H?4P4+2J,cW#^BT<VN&k&IG8J=hS;^j=g5GArEs!p?Q6pjs"X*%1U:r$pME3p,)H,c#V2--k6ASWVng]b-M>7]S$Ai4H\0pp<i
%cn"D-KE]V]gM0m(Yi+FGh1I(V>9)/E#mSI;PAk(JB'8Gm7@.DRZOi7TQ%D#D6F()>"K'Ol;!9'O"<u.&aoa)Ks'LSG<Ns8U(B>bQ
%AL3B(_PtruGRGn0G$Rd1T4F\=Q=7IGeM_;rI%\IhK@@o>Tr0J\NC%Rt0prA&TA-%>R,:,!WOcT7912&Q#f.Zn[g+X'Xu-g!@RsU9
%Nqgu&bd&F).aaTgCd3^m0ZP5*%l%4[Jg`E1ZkrfcPQE!(M5Y@.]EU)Cm)WBS-))2=K+St8A%*cY2-Cr:+`^R;\]PRp.RpK%Q43:1
%`l17P)'\>T<`K073D=@ij/5FEe3SV=>mlZH0jA'MNt=r&RNaO5jgmcX=#8)p?-:QaRM"6(U)d&?!poiRUd@I<CIf*^-$j'7C!"L@
%\p,rn8oV@+Assl<WTh'XdnN];85l1V>D`nCkY0AIj-n0QKfj_U/YTCkO>GRV]%0:Ml\VReCGb$-mn9P]DA^d-%@Kt!`)?_JR6f&/
%pXGXYFP;At"Z756?^J;r<Y'oJ.AFnTO32r%0EJ^^U9_2<4?M\u-`Y]k`I<JlE.D(cO9\g]eu8.Ji=Mqo9q"lOPP6=Q-]L2Qm&QfK
%FB,9LHd[M&I.q!j9B2f"Il4=t4'1?MerW!<<]`S\(K26iX+[DgmPV(QHgJ$:'&3RL]Koq*0#*UNW+)\Y*eV0?&N;H^kfE^_kDFMH
%/VJs?fWW<\\3+:?rCDnc``i1?W_+fL&ec#`EU!Q^R"GC4*#i181/]f]IL+#c:Psn._ue,S)?(Zonrsdi0Y?T"8UX><&q-uT\e+4%
%"q0"-<!`8ZaU$s:$86B,\Jh=T63BI@O6kCPB'!dgFJd,,)g%D_)kbJTmSG#cAc'N/oWsG$'<dXaN(e[)N^s_s4;BR_ihK=]J^KkT
%o<8MH5U[$hPe[??!J5ifZ?TaWl]nOU!RGS*N_MY<H0*RE02Q'OPrO5g+.I_\E\<Q1L>\"(]g8$')GW5I6U+CJf-Do9Ls;aq<p"`a
%m9p<%6ghBDl_1)/4(DBr^!ed%<YUgd@Dq*%;;]P&N$N_?,9C+B1SaldU_1Oul8rjA6\B:\S'@mfH]>mEN*`o4H\CFNLf2]YBXrc=
%W;addO58ET%:NmYQ,Ztj3LZT\ZNUiX'qHq>XeS3`Z?eZ%:L3'>^<9!k%I1@`0bY/9G1:%AVBmX/Yi$,,ZPKY.B%EBG_1kMi!tn0?
%4!<Bnep?0df:.A]h,<:40h_aU\UcgbB;J*<J:d:1AjO!83NjG[Z.GmRVi+7h5Opj.`"!C2>26537of]WBY:1rTkHts8X]^)D!_TG
%_b73H0>-4!d0_==CFl34bOSaEBqiqUDQTUfEF^4>82cW9'"DR+I=]J<X-Y@k1R]I3-&^u8n<W<`4ldE%7+,eu%&9%4=6m]G\43*8
%;q+Gt/kYbpMX2>GE,>0(Eq<*uLaXsngkePrado$d8$f0)C%3iJ20Kt$$!]-k6#jaYRI9iuK@G^.jm;J,@5q)..#M[Mcjje-&9.Zb
%!0-HQLlA+K_,-JkpB&()4H*r0l:5>b;\IQ.ZG71OVRIB`X_oId&k&u)`fph!@Z..N5ST<W&N)+`ldo+s8P&^nd2&X]2OjBQWQeRm
%8a_oQ7To&DTYiCI(0D")'htnAQS=I*qcQ.'V^qWIY3#&qpAg%B8^#mD/`;oBl@_Qa-M&j9dt;/^Yln2#P1D\*"<D2'e$;H[PF:Bu
%.f*u7dA:AmkuWpf]Zsk?JQS<tObr95j.2GfHakGs6TJ@Q[tYhp-ONb#s+@<EMAIL>')bX6)0`lKRM%*'A'M\F5_XkJ)'T^N$ThYM!
%\egNrV'+/3F@EC0(%NqJP)>bi"*aH.Y6LDaQHH3$]:U25<Xggd6<nV*9MCb(?;-#H(i1q<`Q-"WFOR9f+c/1R4Lq.iB`WoYHuK?i
%^nRrmRPn0_JM'5Yjb!Un/q(!?RDiX6Me%+[)#ggMK+Ye4>MgCd#0CU5Q/9cQT_2=UJka4+`Q]m2%EL\GDK8U8'[[U"\_-7&>5^HS
%IR4-I=tse220:6p`;IV9Qqg=gp-dCApP^R4hmGnQV<,gXBjhX1p11?,bC5%*a'd9QfeOVi=qm0<:(bAadj.,d$J2Zq0Sq8>41aa(
%FK4<^h`hJ>PW88V2UQp=$l=cmD$H%kjrQ,-UMrJ#[AH?8CX"0Vj')94@*p5S\BTH:2hF'"Cjf.RIp-ps[@GNiHm1J5R"aKCAhf>l
%ad_dXE3?1U=mOHVK-!mQ;aYnW&;hf+M.OMZ[n!k8>/rn,XdAMR*3(B2A3VkIdi;t#dB-/^X\T1\Y=$AWP8h<+J/$9sUcSS`p;(Gk
%]q@85E;#ZdnMu"BmL<Sg&^`^';;fGHNG_d&NQ0tU8.FY-/CsFp"Sc'.4S:5sci`?JHXu-q'na5CIge4tMLeiK/;ZEgTXJhP<V*S3
%Z8i!^&=V"g2@"Re-.;C9Nor>/ng5g>J*<6oVLahT8bc2P"D;ddBfBKiC(?R,?=O/t'LZc6W@dH8#Ys!fh<s#%QSm88!3:sK%QdD3
%DK:H[#nUH)\k6OIL!66o`up#l#"e8?3`p9].\o*4NU3$(8ImGoR*C-Ypdb;HVa!9>m(^:+n"9O;hqR>/^3]b$2t,UPonSa#S$P%T
%9)[l)T?j]Ydsq39='$c9B<RA`Ho1%Wao1SHmcYIO&G+R65Q9%lqSOC8rm,67df9/Nj0/J(Zu:uQd;P*Fci8,XYK&:[qr=&WF.nFj
%A,N/(7'tr@&Lp\JAC?NWd0Jj:Op?A^X.RGr\,*Y0@JtZQf6GP,)c*KoHG\_8TVSGOBSPSl%tbBk86$rA4b?W*PbpftOI>=='1u]m
%C",K/1W:4@:93K]AK!!i`n.$$W3oG9c+&O]83/,Lp'fo;Z@k>7X=?T3K2Z!O<8A1`0diM$@WQ!((rRg/Q7lusPU;]rI#B$CgdA"!
%0JOuH*LB/6a+j)i#*GcThNpOX_M:hGSE<c7Jb"l7dShB&1Pf=1lqEB!IiCe59-CL%M3c?'NB*\gl!,T=H_=,,=-sLFH)fiA`$)[;
%r1;b;U5kASOE@^s,!(M_:ebj@:TILPLS@O3rV`D<,KOu+28hmXeJQ4UJs-ggLdK>H5<2.CK4EAP.C"]I4Dc(-&u@oO'*l_@Dahco
%[N7DoKB_r_19m%K`'c6E,?#P2E_FYidN5F0`(Vg=2=s-oUeKjt$ej%&#<iOH"+,3YmSh$d34<H2!<C-XU$ZtG#"g,I*l_M:h9F9N
%'lFSRjL3.$Gpds4EiGTVmpuo&E9`-:*osKBW9_g+)S-L%hJYQA`94KeIE20I,RT=(k\8K#Z^sZM,e;*3V$G#p9h"="2&tJi;pLUA
%QdEX34OFd!XdRJ^0S<,GEERVW@\-u%<"<JKUah0oX<b1=(rA7r7Hm2:j`TM0$3`Sg.^t:?omDCuU,-Ko74o(R6d/[sa5DhL2FoZA
%-DmJ)kqSss1$tZ,HC,[[<iEO_;KuRq9/N/t@B<"`)ruecBb0M%@_,t8&\OES-CNLjP\u,,cS1KV7UKRE$H5:e4eRQQOA>Y_(sjl!
%,Wmb]*4,sV%EjtnIi62!\!nBF]&m"[7M6PL)ZuFI@6-@f'p.ZG)i)qIRm'4dZL?%5'Uog3F->h/7(0ABijA-]#=nV"1G76nF:;J*
%+u*AD0:8+_Lon+GJLTX>`O5<U;B']!^lU$>`:)IAdMdAV`(UaK[[3"\6)K^LQEfsS(dXK.4#nM]VA+GZkD>FiU3*BU/B$,/p)mfh
%T01_1;.<P4"aY8><jW/rl@_O3Q\%jU&ueDoVT^1tlm0tAjtIWtd`+6h6qg(WV9/A^b8^TFnlKZZToOarn:E38'K+7\Ye&l\?CDSS
%eK1I?;V<4RVe%&>g'COt&FqljJ4GP<A_I6s0:L=)j&_k08]FRqo+kf!LRa/a`n<_>eI+MTf<oVf6$qMu8KrJ$s!X#Q+J*$ifW<5N
%2q)P/gIBgu2<ciV7V#9qEu4NNlA=q<4QG$_$A3<,$m$nr.EaLa]L7S>X4"fJamU4FB.'pG3a`l<&;u^k'_'\\@&Jih*50'D>YdsM
%QsI<$1%A[+OUFOulg3t=E]=_=o2mdeK4U/]9]Ri:kO[D[eY//tV!gL$ap96OUD$YO&5YqY,*`#E/NHn_B#-RPF=G_]`1"7mU9'L[
%7jH)9;ETkL-OZL9j[:F=eQNJjWB(D9Mo/%qa?SA\"7!P9H"-*'n3(sj19m-Kn5@o@^jNIV!SOTrK^>Zh^#/r#fsffRbU?@5bI<AI
%3bf0smN^0gAVUR(lVM-XA^)+1T$K5X+*P-HVP.Y9g")?/Ner>FK>Cn-Lr?l#_-\&Z6EZCd5#9+'ls=sVW::?GlXin(4g7m62\XA@
%,_B>D"jYp@7,j=D`)NfCc'B!R:4c0HdFns%-Tc5N,GKqhq^*.8_D@^RBaQ$K3ao"8_SeL$62Ak=&7uG)aVCh52o3Er'WO75/taO`
%$ojBsM_S,</]a*[%kd1;>uVf#=nHNqRn\p%oPUno6#r5?6.A9]^!-)J7,k'r84tJi`Jo3n.R2l[7]PuVolD)u5t&3;T)aO&\LgQO
%M3mE,R<%Pn'r3&!+rtO_4eqX0E`1"1#Y</1>F_Hf7Ns%q&-im@>Le;0m7HB7DQbtO^L_R'AS.@RZH%D*K8kG_pp#W[.24-q3t#Ru
%@tn/e_pDs*8JP#mh\ZG?G7!+/%VW7QN2tlJ^f"fS9INGVj7k-?2C6\@FTKoNAPKRf/m/Y]%B_`=TC]ot)(eW*;Hbe8,Rf?;Ho>^2
%5(ClBHUXV7[!qqZ_7KD0,q!Rfrem>d3X4^rCda)fBOl67Hs+OG'BHhNMf=d*<iI&^4V5pJ;8n2(D%+Y,GU9nDY<IS5/-?f`X(O1Q
%KchrE.Vt[F%-736,,''27*RH7T4J-G"*sX[@\A!"01;J6:,e18]jHSd5fIh?)Sp=>L^LnBD"nYAMM.\=Q\A.3_P'M8;>j)tM2m-t
%%R)7FAknr,8e7jK4N%<_OO-%B!o9aJAL5CedLuehP>oquF^Th($IRO?j!m.O,(:cIdSY1OdIs,tXbU$.80j<P,k:"f%kUGKlhE!P
%.-2XCa+>%W[:bV6.C<CL@$Q&(p?lspFJ_^=;L/.LoKqp$hE9*%O<M&?GI*3]ODO^Mohja"a1,gGQ#DeY8;>8_L&>25)DZ`B=;*Sc
%b(r0J<8XL0VU5I@U?\OtYq/69'#Z$`!fQ]q3A$JhFtHTjS"kQs[5L.,qJ6[gP2008MtH:#XC.400dt8%Lg>Nk`jk>nFs"<!'D)Z@
%1uu3C`ek,IpQjWe7#Wg=OLG=g"aCI9a>T`uAgp,T=`u-p'QmedMC8fdfbeC=[++ZPE5WiZJMBmpoEt05=<&*OMk&30IF321XNMlU
%_F`lB&P6s$gIfb+2Ih_a.=eQH3Me8Iar&4s<;Ad+Z2deOW)oe"e1m8HMRN*I<O6AlqXH0DYCtJXF&Xr+*6pP[es7dl%K]'gnTA0I
%+BD,:S?/HIF0T!$72_1ofLk9/2/_(nI9!JXQ]quOF<\dQiDue6;9BYc0tK%@%npdp1EAiGpL$ZNQsCkNRdhL9"b,NRP0'<6(1;=J
%.Z9>hYf[>$O"(BbF$%s;?a'hsP>,^%.Ho&ET,DaZ0(=EC<>[`M:D*AbN)c,_$5b"9L<hlXh<=%$U<'i'K3e\,:KO%hD'fU=*K'o&
%23ACjqa4_a#NkIP3N$IAH7*3Y1,);Pk8qY=jE:!%c$b:IDMZD3kZQGM)FNV/nU:*r"3VMu`AF+EV9L9la=L:@9.ICXOdNlq$g&Cp
%[lV[uaPn.mZVmS36(nctPil%Z;-B(N,ipoQ7A35J'0$V\%ZR59D^(s-&^;(`5pu5Xj-U0.21A5tNkA9Bp;<-GdrLU2%ZBe`;aUXG
%*f#`3?3N9*RA1<R[E]6aT,4@tX\ooiHKU9,hX!cYH-j*0,.lI7#M!1L%\!NR%lGST\`tI(O>W/'2ih0i+E8rCV0Ca>-^TjMcuHRl
%f9TZXbf"H)Ic+LAKi'Hk/:@F'\MAsY1D]NN1XYc>Qp.Zf=1X9<m-(G&"5Bc-W4/3GhG9iELoFO':t$<$PX^FGCP5[1&%iIK>tiD4
%Bgq9=-UsBtR=38*f7Z):/iFF&kG1;/9#TRr5%YF+,[-W%b$bs>UjhjO71d#KN3T0XM/@t)HdsK2@jl0!l!A*kb%Pj)>7prXGB;8%
%`q/92:)kF_3,E`q`gB1oOEK^J0hmpH/:/%b*lOnObFA8mlTr0=ZB@qXg)f<kBb+`.@4?_=O];5"o]C6=RR34s@c5*MV9CXsa2/O!
%rFZ!JBdToj;n\?kWZ`6@j]bl2`Qi!,UGO,-X+c>#9X3YTE=%dA?g.C0DOhgqFq:cebq?'PUlP]B]s,J;lD#YN^c^9PGnpOQQ-JCL
%\\=+_G"#XfSca/*&t<'])bfH`>`>Xf;FT\g:;h,")oNQ>:cb0_3/1gO,%K,lq(.H(6l8@-71ZJcFWpT69JXu]=["$c,KMEFp6!81
%_Jk'cLu1KF6Lt[658&?DS7\Anc(gs@[91".DJ2J#D,j[R;2FK=\72>Z,^ag_S![YkLs\\NJ6cZNKZHb7mj9!0?D.UHlLe]#)G-<:
%GZ'-10o7'YB?BVMPmk(b8S^!^ZH#sli$*EO5qT>=7(%'J4]k['\B';&Ztb8=G%/iT$*0LJ:e19rBBu84Z"5s5)HUIP9&pdhLeV^1
%</`(!1rmC7fdtm/Z'1H=XADTD09#6&]dqK\&t/1,%Sh-/&pb,$>U,eGr0"c]o-VD%Dqs2nWJ/U`aK%7M`"q6pbF+r4g)^>/XlbL1
%!/`PD<j3BMC/r7r+QcAiU>XJAR>t7p-cD0C<<Q^Jj!f<u@T6U"L+03IfYpfCF"?eil"q0cT+L#?J*_,i2R3et>RKAaQke'R;jrPJ
%9h$`^S8k(!dT$O3VPB?E:ILbNO,+ESeA`KV/Arf?$6hP&8!=0)G?#NE$4lK$Pc5thHQp)d\(n>XHFD$HlXNpdL^^-%6n*o-Sg=S:
%@3UVdk&D<VX>3A%?P4R73bP0g9-ih<%W&SU/h:#1XjmAYf,pc\Z;TGB]D*e>\qT?"1k=AEKl9*&)n`n2=h1u:3j^u#)]0dH-:+g*
%cp1[0mPn)Q"4K%m;N(;PBkBf?LT,`h0L8b194gX[&Kr^5<g.G@6$?3^"uJmQiSR_ZnL^gqddK?2M[G<-,#0s*pZ3$d![>s!gsh[u
%Z?(2'4G.JfDNPr7/$XmB;1"]&Uda_&DJ/mj"2Qaj#[)I1*%;QooA<h\rSc#se]mp?T3o6-3eo/ds751lrU+J#2t,fL5Q/oXqp"rU
%kj$2QqA9&+k[2C1qWt]3^UnrC`de$Rr:K.LqWd.d\*s-j:QBo#\'']1nW,?C!8j;rna>Q+mE^fA4rXF[c'sp/D,.i,^Nf_>m####
%UOW+6X./pGoNsQ7IXT69ps\O^rql'Sk*O]gY$R07r:9]&5<J_.:WF9Srd=SZIm(>eVa(!?-hAE]k;R<$BhSP3gieY@<pQCHBU6F.
%pWn>/nb#s2TDS@Yn,2j5=](r"9ud.mrKI#"_]6DHmjUAB'%\T1D0JqJDTa<>3]mB5=#V(1Jqj.cSSue\hgDh?2VRT^gIAho4>FKu
%+g0[-rr2QPjKE3#=Q%nX=8R@9m`cR:T@TL<\*krX**4uP)$:asjSPkH`Q?l@hn1uHJ+([c::G2\47.5L\Z>$lDpE8r+1l40"Q+73
%5Fl.t3et1Jn+%U8],1p'pAX<uYMM>olhhhB[K!C,^L&<KqpYH)[>ammf7&eV_thhm]^_;,7'1WT^-95ZpZD,7hqjlNrRL`.Q5OP.
%d-g&fkQ$r@>^q1qk2b\$c%`4AVE;LFYHBOn^4(EXZgG>OUf"@6o)8[fbHK4*4Sn-3b3bHDQO^%N6'XO1Ee!P"IMf*?"t"=dj`[cK
%D(s*Q1qES6jM$"_19SL5n#k!o*d"r@@X88bm\%`DFL`d$b7-&dQY1e2]D2$!PLnFD4:AXKMsq.^SSaSCGC:i%07@K^FFG)'NkC`q
%+F+1[jTb,>mEOs(Sc/6ZKZ:>iE*f%&C0k7P]R0"Aq?Jmf>tP.tG4P(gg[gW0?TpZcrK`p3*4OO(=SqD)o(FYVf^d%rhVf]km<\+^
%pU^@pbI;+Chf0U,n6Q&Wcg=<\ndVnsgTSpuhd)bbH$+Q.p?(FNbJ)d3aj.gJ[s`kCrnH&V=(`U3_>0:#:%3bQ9a\p']tV"*o<7&!
%osj",92PEW@Z(7Zg[/`r)kpNm^3fJ5am%KVV^L4GFK>4gVuK-5TNgp_GE:RHIf`!p2f:YjiphNT^E3E]@4D1gLO-Yk2N<,D:ZP6V
%fP[>6V2Xr"lgq0Hj)%8AT7=L^I&[_7Ys(6F;X!pA]>39M"uc)3]mimCVGCU7\)s[S4<N_IfJa;E61+KkQ>?Me*T$W%:YT4tS*-n6
%p`6AI)s.^@bTZEK5!3G5\i#98"PD3;GR^:7TD[cpq9<!-B/a$Ao;(6_Mt<E&S=DfYD`Kq?:K%T!&52JI>eXK]k\nf(r:u:PnjuG8
%gFq:PglILWMF3WS+_0QM@[UP.?XNa%c.UeMCbXB-Ij!4;Ga#hM'Q0;U+cbfZ3ZW^+A$WLe\KM:Zc,o;CG?`]B)X^WjHS//uYKnjs
%[Wl&LP\.@4A"H@`c'C`j!KZSBV;CMs?[m3HEW,&0cflLN+jZ(+*LPHM19]l*%_*g+l+jt23I8tlLa@HR4R7n75,5#s^VAr:VHmNt
%-QE<%BcHdE@Vc=9Hudp=?YkiAT?:_h>NF[/DZ:5![sr<Lel-j=SiB!O+#fRY?[[';F`hltl.O&/kr(]kr_I][e:+[=4n.G%]Dn(#
%c0qq4Um:+[btco2>/D&PYl=_hcU+)t@`,Hs16TtLaZPj?k#^_a^V2Zf4hEhg](ta8f2(tZEC]fMdChdCXZt^XDuAc6;%='0q@N'X
%>M*><H89<qnmljO+jAt\8CZNQ"QqC_gagMnrU"grc@5Ao9ma/6=+*_Vp63GlfO1Y(1+[7bAA7M:^MgSu_a47lc1U1a#X*(6X1d;m
%PrI<!hP'8kc/Hnb$`EHcaiWl#k;VRRh=@_oVRr;I#!TLk%3PTj:ZD?E]A7pKa6t`+HM&t0!>CVs]:-aip!oAgGC',Ro%eLX+5GSC
%V=C^cF`DkQG-W9diUl3joXEFV!n3L5]:+u=E9+a+$>s&f&^kPu;`VD`k2b8+dhr5)c,lH7U4ou0eWdf'(aK%Aq8L=]nFqh]CDJ:!
%kEh[@r6IUY#bD&NgP^627+hqOmIWD-U267A;__WAiCK2-gT^,_@ghMSP7'f>I9rTqLPgY<=HktRS8Yr@:Z^h$`1fAu08EYoJ(FNU
%W!6$C(1e.Xf`,'<B-GTl5,VMI/X(H0YKaD47I`Tm=HlO8E:;Js+VnWr-0diKb22Ws9ma/6=8f\LU7=EF=HkD$Gq-<&qt/<!`L*)g
%Hhd\IA!_Q<[))>l7<fhrioqV(4IqK,W2&#,#bDc[i@k3/o3X5Db3(jj0^RP'Cp=g?'r7Jb"t9GT;1Ec3=?d4*k.oS4KWgXC?e_df
%#JPB>CBqTI,l$)sh[IXn>W[aWqomX:k`?jqg4?;Q(frd)CBn;8^srp"mC@2DeKa_^%fG)Y[NlfXi#d*`4t-`pi>R.>26)FIZsa7N
%fB@15DOm3^Nq.p<7%*Zh2[(HZD0I:grF"o/rSdZmB,?'-3;N:>*TT?NgK?_TH@>MKq:4N5.W7aBI=69Zo;bS&'MI1dD>!M=6?sYS
%H29cSIL@^G!=<==dsCmK@A1>`@L"0C!trcN`lTm;+(`du!#!Flf\;Pg,e,(ka+(^rnc(cjIujX0POT/R+",*?G8ePX+MR33B_8qE
%(&ej\h;-$4DCRLBA:jTc:$79K`r@mN/pj4Q]^^O>4$.e2JDZ"NBK$eNf9]iO^E%Xqn)$:JVuAu2[S'*Z$F>)B>[W2TkV$-mra0hn
%ml)(7s74f)Dp?,e:itPJLW;`LDTs?mpHC_%gUf=u[(A>GTOb_0DrQQBhS"CX:YqW!I!'^XZUJ>KZ>\eo!ag<jO5s5]7"!qL*]![(
%R=5D@n%o&$TsMDns4m_Fi4bcDKE(](s0]rhr4`L:msCsTW.]0[ruV*Qs.K!T*oPs?fdKE5I(jrBg"LL7d)"mlnG+,Q^)nnUDNI'.
%^\mZYgTsu5=6!l)g2b4$rF/i`mG"t*MsG<<5K/QP5C[f3)SA$U!6u$0]_d>PW=N2n_l"H9cu6>KRFJHlQ+q\;jj84\:@!)p4m.7j
%rmL*)IR]CHjr#0jA-WLq?<P?-]j/n!9Qhg$HSCX(b[^r7g%.%aM4X:[TAQ00?%"K(`3W7Z6[\mrlVIM+IXQ3Zo?!K.f:Q<_3OsIc
%YC3XIm/h<@K/N8e@f3Ehp84\RcJI6_@883MF+3rQk-:)%\)&I=gp*:0=6FCU3$r'JVm.\c&k1h_V7%asm'fqVIJ`]jl&\KnI"9W.
%r_I\bCi5m'YQ+=h&EuAlK(CA%HTN<JA"SHLlsN$2C\&/+_+o&9p5Q$m$nhk'bJE[g0YDf&.6JGR%Yg=&iT2/+G0.rPL)787Yl3Zk
%GhHR9oO)q>&Au]Z`1uK/kHd)OUeb!9HI<S)kE]$C5oT.IlfZS(D.5.,%NgTP4Dh<Z/H>hK%Q:QqW+"T"-FW9Y!Gl5;!n$`A<IA[a
%4J".Q`5VPs+&:*'ojJj^a$"lRpAJtn+>FlX`uV-^HiH?KkIZMYES#PZ5[\nE#YZ>EY6X]r"8^HKNp.&YVs)U5:0q:WF!srXgp6K0
%TM?N5o(PU!Gk1+c)[]$JIZ0'=MH0N-mZ,ka3GbY&b)Q,lHL*fh])M9D[q@XA)r@*uhT1kPg7A7,cgcdh"8k<oq`L-pIG:0NkW3rL
%RrZm&%N=(#BkZB[N9j;Oo>9[_]RLi(A?6f/=PG1-7G-FiXQuY90T!!Y[`.'*_jsLLD4/[.kH?fJEGE+<*FfU:JT@u)4<^9XakJQ(
%1U(A*RDZk$Z+$1i;nMW+43#*sZ#;^FY[\[>(^E.BT%Pi9:,[eT4a0-^l1Z1oZF<q(=77bkIsQ2,f1ifdCZA\pl?%Z<mE%WZ\sFAo
%GeQ._HhNpfBE.GVg"#=&Q/GF7?-Vd%qU:r*S(#Do3O`f,eF$,q(RkonXk(+Qgm;qS`^RE4h=^T0HkcD4HY6sHn*S)?mVMn@!tB*V
%2.+).^>'p7E3nBFA4&5<S*MXBmWs)`Gb2]H>McBXO8F$LI<p!f%^8_NB2Su9U^\gG`FcsHitspC"?,IR)1MSWm`cR:/dD3$b!s51
%K7:*tWnqqe_T)dBfqk\A_ihYBL=5ggPTPjXFL'TG+3SPN8jQfg,'3H?jD2pQp-%?W4g0C>:"/oH"FP'BpNU_Upm])%T9V.f*N\d,
%dMdW,5+UjYYf6'/5!A+MpLL(*l>kYSh*IMlBQFDqm`B3b)ao4/2Rac,3,!C12k;%F2R`^@i1S?^>+=9N8I&gEs-1'T)d7A\k76e4
%6(L*9EsHjU)67iYFaa5bcYlRegl/quj^.^pTZq>C`4kPYq0D4GS#5epd-%!)222R?cN0,9pH%TZ"SsKD#4BUn8f[=T&%E6J&6E=r
%PC#L;lh^_(fT!`6@]2@dC&MDR\2ZMIb9oeH])MQeo_dk+J)+WBI=(SQ`>2+8[bK?CgK7;OakWYNo6Dh;kVmVQT&_L"qf$$<hT38#
%I-pf@qX!LM5&9E@/]49rH="8Og"4ErqO4UjYIL1u(+nM506VKr^A2MBcMiM5#(Q/KCiQ$(h&kuuI[_j(I<p4F8W::3H<7H,&8Ut4
%h4$&n:=$@DlZVlP!rEL&@ro?>-hAs-Y9<beJPO>lBW/7qn\6uNLR5G2A!(a54$!,/o?2?I*#dCK=7bCKB*Xed+%q"XeN(eYa4oBZ
%5Q@)Q@4#rp:]#3uk"lW_"2qbI5M=_$hS>a;g0amIlg(UX&";GSa[';tLiu!JdI)Xe7=Qkml[<9C#GL?q#Xnudk^+DmVofOAZr$DM
%!gGp*8alVAoB\%'Hguh3N85"]c%_#O03bUZ?Zp\k2lLtNH*#8m3V_FT@EUhs#+p!Q%WT8Hn5t(WXZ<35c!e6?F8oUdk8(+Fo&fl4
%X37hDAJ^rfqfm+0o7rP$8*r'ea)R.^Q@46&[kQolIiPra_r5Uj1HGF@4HS=nmbr$<s#p4p^5Yq9Jp\t)\?A6Fa'jj$B0STF.%]LV
%G[;]+T4P.6pB%3S`>Graat*qV#@J_s`rVRkHf0<B:B0q4?Uf>@T(/FWc6F5k"ie(H:Y3@@?bcQ"+J>XRj=a:uG8p$oSe@CJFIqB=
%f>RBP9ft([n$tc4I/Jr5?/D8&O-/tl3IkYQfH>b64\E`7ZSLoY3kt\BjhpNWp-2uIM8*\MSq+$*"(]`k^Hp0u^lDY+FQ3;ld?(?"
%Kqm]8m*4DE*P\F3qNI/Af8\LG:&*4Ud?>BFT@409BC3#YceeT^r67_ejuc)1E&cS0S%1QTio9;h\(VpB[GKGOo,sWkM<d+hY"o#l
%,h^8^HK:(ra6`%1Z[[=ja35Wmfi'qC^YEOB#cfqLBE+kg-Wb6$gep^P&'t.Y-QMKde`4R.[(R8;T;CL6:>Ke6l%A6\U#"[h[pNBF
%*&O2#pUhP6/3cmcj[U6*#8TW%aY3YL]?btTp"G9H0eF('oA<h\rF,?8&2c,*rl_W4T3n`#o&ChjmVDI4s7kifQX=!)OONOlS"KZ3
%*W>!<5B98dIIlJ@]t=5mh9k]U^YI#(9'^p'd;hm2FJD,03>'BFhHZd>P4u@(I<Irl6,:i[hpVQ$?fS?i*jfjGda;uorm1Ue8$BsG
%5PsK%J+TK/>[LOhi_f(q^C7Z^UJ&t?O+HNbAa)&n&[B"X/JfW-Q`5*S()iS78%&$`da]%l::Z-8HDkEIcrtq`a1)AV`g8,EC0O/3
%f@l?DCopY\d_pMtH6jT=Qc3V&TVRp&X8pZ=b7Z>QWdC.4m771*D:?\U>GAQ;oG4ssggAX")\L!2ltSmt2eF?-dDAnSKcAL=NpB>&
%rqko_Z4?hE.\8\=A*i6jF<+i[9r:rY.SLiJ(PpYk(h7;AMk"*3k9JCGb?D3Y9eQTC\;tQ:D54LS068[NQZ:AXiVaXhNkO_UHlq[m
%RhLAiik:m&"8HmV^!?q)\[fsKJ+ED2?hglhDG5Gg%);`%02>=#?2jBjAf>uFni:!.cc8=tl+M.Jemjo`*3874=Bij:]QT>8BQOZN
%d`73?`]j0&3hK(aV10A/8i$%/`]r[4$SP"G<CYkoenN,@1XX3FC0>36':kfa`\NhL9jOX$,q\`TqcK#QojfA@W<5B9502GZ4h>bU
%2*/9:2l_m*<dG>R(h5ckQIAaOs2m0-5>KViPC!f"6mfJOT'fq-H<fY:B;UaKPu,(6(RJElH&Q)m\m(55m6bOEZ1IP%d<b9f5'gId
%GBD^=R!4k$Nle`ZV:-@D_MjIJ@r7%Sa&S-?hl#Stb=-LpX`<`#$R(.'>#\rN]LGCF%M@N?So#H@i^em1'kTseG[3=Un1:,ZcR4/]
%RDDib1"<J.I"DRWC#]1+cZO5$0@Cr!rj!b4\7DO[7(n'fbMZEA`!bs7U,i"bXa4ekBVj+M)^i[[W3gQ+BE^^>*,M^=T$Vg3pDBFo
%r\8ol69KR@*kH%rh8k@Pr6anWE$HIXooo]:k./$B!lrIK&5'**Dc<\3keZBZIWt`TIf,sio[BEQIJ.%=%"BpHDnJjt\g"a@hNNaO
%0kTW2o8u!lMB%I.?C+W&Rn*5;k^S3s[7Cf[e*@j-Ck!HuhA\8REFn0<'ZYG_Z^6B?9pO!oA&hZ5%U:$X/(#?fIP`&5DoE)JhX0+!
%**=;Qf>_<-JoJGD8eVl]b7oUsQ#U8C_MO944;KU5aq()?$oma_9Z/n[F!TZ.3GQa%`WD]?'#"LF(Ljat5uQ<U0uh&9\A,u>oDA9+
%Lgglilk5Z\r[L%C?B6]+f\b'GKI#O,bn'%b27]D\mnl_2j1&ND4_`5QEQ<.IHhG8BA*$qL`\P0C!mfNV_'@;m8h>-3=p43;pmN/W
%Jd&+b*H+[l-`m+p@bYSMp%[)]8WE0PI@RS]jku*rCS=DA/ruKsNJT5"95DK<S1_b<3N]6A=2pq%A,ik*/4L^12&S-nSlW/)_(SD2
%3fVikQ%q&8@p^h&CrJnIT5jT,KSLfl7,hjt+WGg2aru['JUnHiKdQF+Ki?BUXMS!7fIE"(+$?E]T'6JUs$Kk!h\fgG@iJRR#u+0K
%>_>660%D0<?=i)A*8euadJA)Tj>e)ub#gT;)9:o<KZ#Y0EXR[c?4Lfk)F(W^=I$'&Z-pI!@SM"R`lX-A4&2p_*C?kcaq2QI`OK$0
%?t-!+Y!!=4O5.7*`^Ro5a$k$mY/f+nRaGiG&hh.a#0&C<,Y3_>8g!L[2f<^KC)/g;h!^oSm'FD-IRj]'_rIZu5&>ZV36I[e%kc:\
%I?PBhi]).E`=#JQdeK,eJT[BHKcWXmCE7=QM&*Jo10FN0'1822i(3Nu.c:FA5%tm$(?sK_JbDXcbEo6GF(o\1F-esF?Mc-3g`X$X
%f;4Kt:B[IZm'IbVq._]/@N!g[32/N;j)XJZqIJEf1)IMS9Q"]&Zm;C>fE^ke'aNJ6$DVG+fRGHhSHJgR$_Mq+F#/&Ljuj]>Y0jAZ
%f-Y^U&:sOPQl5EgcRaj-5umPsQFd^Kl0o0&DD9taFnDD_q65Ojl:r<08.q&NcAX,N]#V'\Ae3fmc'Mt&fa:!_n[<#L(R;/.kY$un
%oEX'dK+-h7c$=+Q+5ZV0%`h#8l+qH8kTLth>_Zf9(ZFVRnT\EQ^<f<Ys6)@f*pmW$0"7JICoCGcL&9N<=iO[=&&0cH*n%4Knm`?r
%7HrA_"QaMgS;p=U:!J:r0kJFVA\%)Y\MT`0i;MH>N]#'*E/n7o_kS4[WtE9MEgW#`C*J;#;sJ]&L6e'g1Bk>UK@kqN,1>`.-TdDH
%11o]:Or[K:Oj<tkHgfOl=`U'gh$<G!R%"*$71\[2J?R0_@MkL5K'Xc.PsI9AM=<>>d%h6Oqp][h[GUEOH=Y19&K<V2#X++CX*="n
%XBLBl4f`KOh,Z?iT:Lh23\^,#EYCZ=j<$_f\^l9%Ie_?&JciM,P&[p1o;SP)_[T>*qI<UCS+jo=DOAbRUVCn\"FfIpp;]hDGPVlU
%Mr&'pBCksa_M47Aol+fQF7qU1mJL87@?VcX2;Hij.7]&;<g-O5K\c0f-R<_cX*f@&[K<(EIXGM^'lmJtZ&WGTb3Rp$pLlGf(N^$L
%=8!5t5:^`_(X)*b.i//bg#Ir?QE%nWHMAJNaa9D505K\Um7UpU95%H:0]g(cWHZ$.Y-ZcsD;Dan-$Jd6*K4.ZOb`0XG"c#/Hn?7>
%MK1PP661W</@$KRH$j)8<>a#^Ir+=K=?SZ<`Q'I0LmmO>3m0H[N0"_gYAb14[hSB=e>cID?&W`?R-BB-g9GW%kOAt-S:aqd?>*Ek
%V!,5$ip<B,O,..1J?B\`6c6CB#aB_LU&Q<qOgDsrA7U:X5kZpJ40IodWrW7r\j!AP#3&@a*^;PP7Y(g?Z/IIBB@[P+8)*_C>?^W.
%h/@T+pR[a,_Aiu$n4'Z%GUBfH.fK/=)2QP=3'G!VO:+'-BA@6E><'c?E<Crqki80X7t/V9TkdQSc2LtKk:k,olDI^&6ZB-1O0<=-
%ckI.</p;'7<Pjc@X8p><mTrQk#h,dq)])#sVHhE-/.nhmfkn/c@(3u6P1t=_;3pPH7>:Q'G(!:"^U_D"F<KF$3F=m[D]QqY&+>RL
%oYc/Vqb][3555P$/%?)VO3\<S?UV^+1H#.T`,D[R2_Kk=h'=S<$oZ/RmkG'HL2iAC!PWsm]Qn5(H+gk1(&aQE&6ol_PefpTNljJn
%3+b@>##h:#-`/cHfmr!jSbVG`rpWjWk'>f69_,O&&b'.eJU&NEI#Iq`'[f5GAO@tt>5te[pP\@\W$18*a87ZZi;\+p/cCZA58s=s
%q2%o7J'3<;bX&j\[5XVd2r22$pkZsl5'Bg#GX'GmB"`],S\F1D^#]^;kr6?<A,aFDWLT[H/<mIUNjZq+FPc80pR2$XO-Iu(r\Cfs
%(1.)`Sh5&^Fq%X8[@J7*Rfjmb>V'DbjmU*N3g8S(a-heN=Zk<SPc_ib`]lk1TddRs91N%)3nK/4AOW?V\As(@?='bF?uj%-(kLGU
%b1[;dI.A.>mIX#oS?$PlH]G\)U"&5X,`2_q00=)fm4h'O0>bI7%=%U>r8cfU;EQTSn;Ho!\T2g#Qh99[bbE;Vm%U>ks8:.@i@j00
%KrmDZn9n@[e4k-JjMn;Im(VqM"B_o0!#8n%KZ8"sq0$k,1Q%mBBl)X08%Ej'>9GjUi>CHVq*]o+<XamRbTKSF%]+k&hF_OoF3alY
%c`lpn:#GKcVa@8h4rsG9'XKmUH1(",Huh)8ndk85EH?7OW/n[P#`hTh,L<\.R\On=oW[,1<]WG]HY:.]QCIF0G/#TLo,XI,dSIK#
%FLNrcWLl/Q8mGOXY!1PF7lZ/2iC;J)&`tf4[t7r47R/GCa+EKE@pY4"SlHnbfEb!S-FGln_N&'NM93<H7SnOGdC?&=EfmO#LJK"@
%-L`8=b=6a;fE`]jmPF%1mp@uiH+7F@3L]PKcUVprbW@NHSGgPn5ZDak*rB>8<&#B.f7!.o*\X/UfcpC%qr"A>)8MGu#re-sr"/F^
%Gcr)K]J`-EEqi?H!1l/*2,ddi@G3RJa-lD7?q:LAn"pVU)KnMg%tXO'd"9B7p6K'9!):##FaXQ%Hc4Qt!RO.=V5%ro"O//2UGXES
%JX`Eqj!Y37?R?H6DpH1+7'u^[ch\4D^6o]r`m[(]k9sGRSI[i\g,o#,KP=kD!YK)<(A!JTIai-aA7?>S4rgTRZS_aG\<2a*'a-_k
%Xi*>UMF1-`_Y&h.qt;eX\Qj'Um6AAKnG06%a/7Ht7l/CO#<UBb/tIke(IbOV157,=Zc=iX^:97"^@/uSkDB?tn`"!d5rIM+B[<Ue
%;ltID81O]OX)#s%4*PV6d5OkmRn*M)j[&n[/TLpNWpd<#p,tfV[8W&5T7]"O($F@ZKRY(?NE'Z+6U$J[0)3[+\)R4CIu"h5]f7rq
%1i5K;R^YQ4ErUP-!s^R7c-ZOW17#BRmI5=QFj-QRorl&iml8lmICZ]Qm?9b`L!ODk-TC=Ohf(1lHhi?QI(7kMfpn08cZ0%1C.o[-
%p"s+X.4\H(&4R*s^aB6arD2jCSC=,j>D@41Dr_e()#:O+0-R#CL4IB0-7+\"FX!]!SXe$HrFoX;ooT*m^Yf#NO)Z7hC):,pn7E9L
%+9)PaU7^$\\GY8XV$&fl;+gmK)%8)Fi")H;c429T6N-((<+<1U7eG"71e<h7Ip3WQj1j/ph]q/`64,8Rr:Nq80fK#FKcIkos7O+_
%lOY.d`qd5Rr!bM!s6gbRLC08qq1&AZ[3EB^k&5bLb?t51f>%7d#[-&UCB_i`)D,COE8)FVOC'tPJ+:#2cXFeb)^#+Y0)e4.qA?O;
%S;]SK00X>rSNluTQb@h=J,B\W`NT9[s"s^;gYob/rlo)FiP2&tjf]QX.S%Iq7N/T5oaG>E%*ZMr0XG36*^cq!rn+RP#KH]Qs8&i\
%R'?n2m;MF01\G]#\>,o4FhZ'3h1RSn3oGDKotUN/NfXQgUu-6\_F2F'gbZuA>o%oJq@WbPQ^/c)<pBNXs5s'jq:/jNnB9_gg'@WT
%:Oi?/?iKQbrt_,>+)/'VP8JAunnL(EhB1`_R.;NB0kCH=q<Fg%Gk^5n=?`TS&r_i5>Q"@ON;k\]'OH(5BgmKHV<X+.<+DeMR@27i
%nl+dj]R2Z\H"E3f+l9to73fn!pKnd;BD<l?3]kNK.Ul@7^MLUc:ZoAR^_cfXo4?.Y2WYe%0'c&uBk;(OQ@D*tcgXf6Ip5PlE&S./
%r8DphouYCqbP-8WPWmf5h7bm)[GQIBpn-N7J1i6CHEHeI-2Wnq/pfOj9*57X^3Uj??a.9-AWLu3o?KkE14;(Wca!.(MN#i!:>e,'
%\pEhT*iQfBkKKHG[?mVM@gP#0ag2nh>HEf*X1Df/4s0f;po$!N4^m@%\L:Z(U&"I=M"C+Ii&/8l54[6OggdF9_B&eom/&Gps"f&P
%?p#5Yfo?Z$@-!+?1+J[8!*-0!o'ud)l/gk2]^.Ce9("Iqm(`S;pj_^m8luN^+g;7'XDiE/D4`>a+2\)&2a+Z.QNChu!kue%Mk\':
%>?R<Xc$38D.=4kPIQTs_:#Q%e@[uml#,>=f]J9>FI<kf@QBOLSX#r'i#lp+dE8RLiLdh%7Nd1:l-P,\&j')NdRt\@:=r_=3r3PpC
%L6&'/KSUd)VXlCj-?tqJSPT,1n!BAZ"@hqC?Yq=TZOgqhm8Ng$rQkL8\;^8rnHVo[.J=eij\iOmDPapllB=A6?FO#G@qkbMH?8kR
%@@7\17mR^Zh,t?m)>Q%_q];fRVE?N(k1u6Q%RL"-OUs23N4nu]pcm6a_g9C)>2`*5>o"r:n1K.XfCQ9,I$FH<q,iB)4Z:JRin)S-
%L966rWm9mO]I`?$ZqLR1D\b/e(Al*,Tu:rT[e=OZ]%60gCFiB(*ZOkLcc?&Gp>.hhYO-luSK):mY[0K!I"+>`:*_T%^#r@e*Hj%@
%4C;55E-L.dU]3IRNl;4C;F2),KEZL)Kb8F@$[n#tE3O^VLEjKG[tTW%%4bh'^]21?J*q5I[*%WuUJbO%kT=kh90(-6o&C:i*q@d$
%PQas2^2Dupl,k3EO&o@5d\k^g^#ItD27R0&oC&nC59KjE-D:9YM[G]%hITru(cD;.`N,&4T2oN(rVW23IdG+P5B;8Z;5j'a(WQK]
%Ih^.7,%UL)$u!>m39nl6lsgJh00dHHc[Yu:o*t36nRCg61B,V+;Cj<E-6'DdD-H!S/PpfcfMO,Rk4>oDgT2D[`rUau4ppq02#bh*
%;Ag@@F5;)1h>I7NbDhi[G<:q5@_Y,,U-."_D0?rKcc?&'5,oi3II:71mht)_1Yo"Eo(h,q8*%/\om-JgA@_-\A0lW2cURpP^50V>
%p(H_=3DZE"&f^J/,^A6W;k;7DnVLu[`pc41-_Yp5cHt8/L,]MoV9Bn[.b+??h-i\h`UhCcgHATF8*K")iW&i5KNMUMFiFn!a-fUg
%3BX<b6Pe=94.#.#B7loAaVuej<hGHPkPh4R8S"<YQ%/>#n,-(^Rb.$[FaWI#,K,cr0AMl.4o\N3j?o'jGUp$rgAc3*d!_A)-nNV9
%fr9qj;M];MobW/:`\7@Io*LE1Wij[>n+g'RQ)Z(#s2Fk"D>[c=aS;qpoK)urXo*GTig\nu0A(na:.Taed/@RdkF]&M\Y>0s(JS9$
%W'1A9kbFbfqIqKn!K7a@s3p/s69T.7GoX0hV[WT`*uY'G\W=f27oo4/$/U*X8bf#1pVjK7p/uik=+"^>HiNWd"A7b#?H_>CM-oE3
%nVsc?7o>)PkEG)Tl3sr/4jV/`r`9U0M]Wi9UBu5arZ>:eT>E\d@cA*cN+7Ed;pL`IlR<]^,\O*^OK\E^reC7Si_Rk'rS(TWlBd"q
%_lnRig\pSips&\Cs1A$?r8BTLrEHqOs8M>6n5%s9I\-OGs1eU.GJF0q^CLOds7nnQc_(%!?iTM3rlZC4:VachPNOfPn[m]WnF:J4
%"o7c)q;ZCBAS0&6@NtSsqr)b^c+j'uJ,'!2rpq#l+6P*NgHQ9%J?[8_bdD8[rkm>=qbj".a@d5l\Z*?G;]*"8Ci;k3:Sdgh9Lc6K
%+\rb>.\]GTleB'T?o0rmpaB>b^eR4`J`\X;Eo9GF69B[`m_ctncIN!catWmY!ja1QV`G*K?2?7;%GEr",S)$EGue<C\&!.+9["3f
%K.a$-)El_\-;qA0haSI*Ol`94-WJH(h%LOB45dBB?.7[Y(XmB@$C'j<&0H:H%bVVnAS_Z=O!@dof%?g'@tD7H1X;G#4dtI7M^<2F
%SEiikb&[58V?u"+KB&"!KJ,Zq6'0b"Sui?9;kii(J7`Ps!9MEoNc,b"c]qlc.OnsMi6>2JBqn4\BW0</U0^jiZ88J_b8[*)Wsh%k
%&iK-u5*S?[<o/KW&?[gKW*rgBDWQ`fgA#isC461P/CFk2;eO>>]R"GM9VVc*gD[4doa!^hq(VQ+7OXJ0`S$W]=WG\n84SJi\k@&"
%mAqETRJ>k.O'%UAS(Og#X;@WJQ?G'+h-0;9SR$@r1%?eC]b'Yf!%24&I?h`2Kc(+sMe-ViQ\BtT5.Z'@n-cP)-'9lsK-7(J%13uZ
%h#@0s1JZsgK$FU?ODXm8g*Am'p4l0G(+Mt.n@kBKJ5q%")=bB2b4,0!Ou:)l72@7_!MSWRS40Ju-4s67\TpQ/`RJs1?'>IPEoKL&
%jKs9H_NOV*!&I@7V#_Q^b/Fda\S.0;Ol@?h("UhY+CDoI2GbSc`A@knjl4XI)]BKS1,5L)U0-hOmVe.b3CSI`Ol+T_:E]*#n.rn1
%S.Mr>;94QF7qguZi[YEP#T^m><*@iM[e$3L$6(ZsIdT3FG;#JIYD$>B_l?9uflQh(Y*g\<4NQ"okB>WP^`a8&!+G\,oEOK&\(77I
%19,SS$2]Sp>PSa_`:?<9nn)--XcSgFbre_>_El:Ik[d:S^[fEkY+G0)eI2ac\\_Nnf"86%`$=>FL*NV`/`D(;]sV#,A(j..P*>mt
%P'jAI5A)l4<9@.u7gP$^-m>V:#`H@&W-ofu%8K\Kg^-Jo2G:B\mcDD2ZGltd-R.m,X?h:@q3[+UpD#"@'IFmPF"#+@lGH6h5(kcu
%J<(ecRBSgENo7V6OuuJRjtSD4`<CrXT'p0Gc6rCX=7SeDN1nWO.ks1u&8M?$f(/D>8k3VC8rgm[W2)F)='3)tGOCgnIH7f4'b>Z[
%SYMKkHl3AqXmq@mG4#tm6g.l[*;kOKX1-crR]tuUiuWP*$1(K(V]q]kDuW;`eg%ij%hu<lCP5YfI2)G>h*4E@=e3@>MYCFP3Agb(
%afhS8go\jWJ6G,#4iP<]O<aH=r$(uI$qlK^qI8;u3\%,$WEMnh\g;</\Bgk.Eq*e!_ie9Na:8%8!TFmV?UqqH[_XXq\&LLq=fcL8
%\s1J'i+8kH+J:i`XXQK<Yg_%HQ*iIN$qDW]q6H'uR'tCeK=;J*%r)#&#BStj]Ar\"bPN17j3%[a(@S*LC!Arp2)W%T;d;M!Vsm;?
%s/L3)YMf?f1S^:]?h-"onOHljotk5t'JZ5gbG4`07EV*"A<GYUR7@U,Q6$eOgsE8sCpIB)/ojh3qp_-%F-Db&_IINOfM$X<fF?+r
%)^7gJnjn9NJ_W3ebd1klI0=?_AC$BnlNi8\;`k':Eqj[I1'h=69rWE:b!'Xe2&Tn7[_8RW"fVVZ@695n"M7;C!qpT];Ds%0\$sVM
%^%RE3[PeFm84j"#>*k64U1h1T8XYVN-L!C&nHhOoJbjE1@K<f);>M<jTJniF@s5.1K=8)94,`%AV9Q8J-/RZU-#.tj9[0fMEC4fY
%p(ALmHXV;;;#^:H-7`Q!eoBE&?\SL(Yo6J.A4Ab[Bs2_X7#hOC@\n$>F<X7e/'8n-2_='cHU8jM4f!7;W+:/c1BcL1IXN*n<oIPZ
%Rs@er4WW!hLQg;`Nom='U?>QeK[%,e?=(tFjZs?k*KWUZGU#r:mb,?UP8pd4X@D=lB<("cdg0?F/)/VDHZ:>I.D!-:S]YR+e]K5J
%1]'c:1b=_gPjMW:<'ppTHr]EtW0LR82&Dbt5;9a74NqT0I(r[ajT>5ah+uCfDXe_*((t^sf;D.]d[A)*%72tADDGUSKlfI'bU*Ot
%>1<3a!GNNFk9Q$+s'D@I:htqTp2bFbj[EO8_YhT$GbNo:r=c3Gr[X]4J,7fiL-"12c&W)kV:CXq)#;==<MW>F,;3@_E)ou77ft74
%I$.l=Xp0mIm]DK"#'L-1H9J1/ErNLW%C#g*UlC::U*hH9Jh"D`i,R5FJ?^UKUK*Y&0e%6h*aI6!"#,R6HYI\6/f?fBNcin'7`@"m
%D'7OY0,(cPai>\PA$H4tqn1Nl;VpH'o-j$E!'4G;7#X)o4j4nUO$-fXcA:L31->$kT.$VC@0bhH)M2OH49Ou9BQ4M-&p<$'m=[;a
%?YV<h];[I,]O*0Q4'[aO1N>64^/,3c&?q5:5(L1:=h5+r%_4/'c7Ji&J9#cb,e,9lBg?+dV-=L".4^)J#&nhI6p;h(hQNc(.oVm$
%VL-(>Q75;ZNW)0DO2<<k2n2<W`Qp$-;)A]HTF42dSX8c?>>'o+<&L\O4H2d,1hs:*E%pi?W^n(+)&c9",WRXK4B+hQ+PW@+e.2Ra
%CL")G8K.sRoo*$HH1'2#X#.T^!'u(JAApVE`W))LJLGVsl(j$hrO+X3#[;ZlIp;&QL/pO9erZutd[ao5e]4Bk-jlUZCZOcNq:_iA
%3m(41:dd?!RNisb;GQu(mYoCAf=r$1j,t_IIEUa@`Xhl1U;js&h16AP&tN7k*',\i%PfJ&&N*jolc'?,n<J#IdU^(5W9E';AF7fu
%K542S3WqgJ()Pm3/e&)k:qPa"^tdEJ%8Vtt#I[?p,kS9CEBJ'A*RU24r[E%Mg))JA*,tA6k_Qq0"mLOK(DDiIH6Dj"%n$R2OY`ED
%F=uP)S/d36<@3V9m(-RP>hX;@R3^;%P7u!/PQ(_'ZG!PN9FNH;>iim*H%[5uBlM%8"_-F&<Z9TnTp)`$Viom?KkZh8<0J!k\F[dK
%l0kN0OWdAU\s8%S.N@Hh4hYM4?42pKJ`_`LPJi[M\(//TE7(S[)f'un\3:]!L3Oe0;/;+6*r/ph/_b:*`E^d6Z,C\"NfYm%8(b)A
%&p3dTXfFKZ#Zn%F_EtR9_OYFihLQ)AP38mT1X/&hYMN;"<jE_JS4V+_K"Ru?\Ql+YJk:G<Ts<-^lUUCH-i&O3#@0)cfG>9*IRbqN
%);chf=4uD@,U;H+b%#V`ZB9B_@dE\;XP2(O=J\5DI1MnL>d:92f[Pk[F8K#HHc1>)&S;!uVlTF%U/ZR'fFEL-nbUA,H83b-O`auO
%9D?A;UHFp@ZU$aC)C4jkpJJPFL,Kp6bJ%"<g,pf`#44*e.M+J0^+:/MP1FC=>:YR;mi.(]CKlu:I09Bu?_Jn&BV@F,=Y*M-9KYX?
%:m86D.X^OGh5t#+C=C&0I5X\2qht#XBENAph<t`Y.Sht/:VMsK1E!\MqMjFn,=kYT>D:@u^,YAS33P2+)[h;)81o=U57@45Vj4VP
%hl`CL-*2DO^jAXNn_)Pos*]#=4QP&,&mC>1bNreJ]gn?*Zm53K*9pD#SMGg@EVti3h_AEJ(!lSooUqQpokOJoCpT3=?a?@208uCV
%HO*bo:J9pHi6MBj=U=BKOmg2GV[p8rdKPH!g0ooqW%GDGXo[.V/+S9TZ^qi+;p@gCL[STD5Ajd=#,MeO;AM]iPef&f02`JWA3hT9
%PRY\@o5fO:4(1,(dj>=f`9d21CJ3bAg^s+Y&9\rc,M86;8c_43(">SOPNt\M[13n9L1'P->,"d.Q@0&IL&#OFB405SeV#V[@mMm:
%>P8N#(K#)V+%EQ$_E`iD#b,=5%p;gb.F]T".2Z3$$7a"#\0G$rG%6QoSN^_sb%YB=,s7u$H6d"S$MT2W:H'%?E_V3S,!$+(`/YWH
%,td>/3ATKGQY)upW?5CN,R*DRhM'EPYC9fETD.17s16X!SY!b1+[3An6VhMH-K2PM@S($OL+ncd.(OREJ%ns_L)4_ON52*l]]a.n
%o$*X6kh#6#=0fM[^Jo4?)Q`>7A)&"sQb?<14KSnDRI<'X'3O%;B&4@WS"(d>7Y'L,>j><6LOt2sTG%EW=/BKn-5..Wrm$B1q)po&
%m]&sNT.N9U`\fU#oOZlBT5R)c*7kb>S!2Ia.`.sqF!ObF'9ADT,@H35YcC_)0!dGJ$U+n"3,@d`I7LUk&&f3%@pSb[E)<nn*/+Xl
%r7l:O]#0BHpM33?ajbt90moB''NkP'a/LRdej'*0i=h;O>P0L@VO13iaCm8qKfQ(?+]"afF@%>`8K"YpIMS6HhQ*#\a-]lFH749R
%fBj[U,W3o"2s&<Bf<uOSS4Db`TnAdXXl+*Xhnq>j$>cKEGONW<^c6HbItVZqA5+Mq,K"IsAgo63TSTW8mRHQlbmVAcj<XkKcVaQL
%!aUT/cgD1F6s&!QF#R[G>&P1Do[^b7KLm":]MT6W9]oqIgC;="a54<ti0'QmTY9O5_($N4Wg5LdXIPc=I.3tlB)n3"i]\:8)<7]H
%kn.P#G&W1s[QJH7e`odo5;=^/hH/W@AO"7tGrFT@F?DBCDf+9n]_9%!&Ji*#+p\e&Veh1PjW.j]6l/+n+R0OXC&3RU5R%c;18.NY
%;rLOmF%"arGpe::=T*;.i#no:Gs/@Rk$!#:37u4nE*ce%8NWK,%=HC22Aqg6++#OpY[Zi+L?nGMI:pD`n5d2<;&YPV#/gP1<UlY%
%oJDcmgs_sn/QQpd$8We?L6".CT1If>NfnPo4(6NN/mY%4h@V*>j>!F<U>P#OegnktKpl,t[7a\%Z)+/d*Z,)Qil<'@k64kU2<BLB
%:0(&gO>W)fKl)\Pl&$jXU3?Sq!n4;P^Iu4k,?]i2n$]ufA&l,O8CP;%`,,"L9l6g^[YLL'\(22YFi3%?pIg1!4-W2TlFkpD_abRW
%r;[us=OJYm6&*lO5YNE`V'mWXra*dZ;dF&m7FRWB&GO&oEFV;Y"Ik'7NGJp!dau)7;E-tc6kTL0Ye94-i+kcG$)(:X#q39c&Olc#
%ciA;tNO%BBNft=#Gu+AZaLmO-1IG;,BZKt13"D&IC-Xa%9*=T0Cu>l,KYHHn<@qL8&111eFEle;N,LO:FB#//Z1$Y53E_3jGilf(
%!4YR7,DJcp8sR:O0/ZeH4cJ?qjZ[$.P3dF(M:A='CYME[^l=t>q,uL1HE6).goCV(%$b\Lp7'#?aVY`!$)tXBB$_D\%A];^&SgGk
%K\4JX>B.p?q!8*`=&<mQ6;+sr?FHSu#':ATgk/hY"&rg=Ve<hSn0:V_9R@PDZkR]cX49)<:5Q3Q5`b;p]T2[X5[oN8GaR1r_X1;A
%?9%Lr.>LCulE5R61h9tsHN97mXto,jj`A4,9[jafD?0ts2sj1U<gc\;NJrW`(f<5*2FUB^6r>/hQJ6fm3qYYs8&>*7noK4ka\,Z"
%KLj_VT/5U66Mia!?!C#Q#l,$2+qYPD@g$AZdgG5Nr0Fp"5tG>Rp[J_O&(>C!E-@@#Y_&$4EEl2GJjjKgZQmPXU-faS6"rOhj)pnr
%VeBYVlc?8Z09@hO3&?tVq6^OH.9g,OPn]%,iDo@>=+l'%SC18IpiMN$p?38GTtU$jp-0nA<I3R`;7mZO*r<lI%s4sf9!.Pj:!,8S
%,JqJqX[\_'ECDOjrKBG319BN;/.Yrlb\8%J?^9\c+4\B0P>U\<iG3^'7`9B!ZT%]iOH;=%Q'i5X\-UtD7u=_A2#on416j>g:tj*J
%4\)G"[h8)tfGqb#\=i9/ZQ((-I[G-+$/G/QYP6)N@>be0]^**djOPL<hFj4dWj]npOQT1CP?)iHrG7JmU+;[5HmtImTo>q+9`$T.
%6\2qulc[NfgE*n?mt9T]O0f4>M@WoX4XC51/u!?paLn@F`cmjigZU<plNj&\Ap:bKc[4bd+4s-(]rp;G?l(sS&6Jr!94SE^oG\M<
%$Pe<)Z-;=i#10SI"D*teZtr-#;kdEuE+*):g"M]N!.g(-raqk'p03pJ%`goGPfJYb!NO(U1kK;-Cfj;$&N:!UK%Tph4+`:h@.pu9
%nL_.+m>r*nVs&Jc*<u;gA'#T_LsWre;?CGVZj[4:%AI&q=:%IEpOa2JbCcM\<"$9;XH%.!ZFI=s/8>Ve4$AZ:lkbB8+O:D<VA6]b
%*GVJ11.\+\/5OuOp'Z31/&[OY$4Xes+[cQ]?HYB\#E^3aZd,.j*"0_1@EcjQQmDBr<SiV^aKSPJ_3mgTOQYDKTC$qL1JaPpG=dWQ
%phPif9A_LZIX^QuY`&=0_IPD&/fVJ1mr;qB+fpQA@Z0Y2ML;bdb2(?7h7bmi].WYDY5?;?+>e,$'rdb0El($EY,s:dU(5BIf>^e+
%&Xk:X*jUmNKL1[a+a8(GprMqE,j2YN4MMJipF`*r$$PWmPFcR[RR[A_=Bh]?eQ_-EmtaC`SO$]V/U.<[,+cZQ)iu"3?47=?0%B"f
%lk%_RZ_@]K72KBYO2bQAHAh)WWhM/c-h@LL_b@JB$Dd[\,>Nl[cSaTI[ja2h%G7t0SAtKXG#M3D&W-S0;gms)T42ga9O6=/)"S)8
%+:A5XjL^uS=RHaV75m:?$<(bfJ^0llZmFsZ#+&qMEd>1BQ>q"64uFR>9^1KV5H/kE'+q;/Tq3,F@g%<*[R&/k:tcWQC5&Q\eRp4t
%na1tK!Qjo0R>U.#%_-EpVMt&U5o!inH1='dGjN<7O@*uG%3B^0EX/3o@L@5C+:cW$=9^&:NOGnnbWi@+6?t:lUQUrZ`<8F,3VE`+
%Ged/NOtI>3X/g.Q;,*o)l8jUebZmd%29`u'=Et7H.:,Fl!sESDTZi5W(;9d:[Z),!g=>(L#hX3KmR<D1%fJW5-pjW4T*QWZp?fkc
%@uo71,.O`4-,dT..:1=aIssGjT518FD4sZc#\u*5,EY4Mr>f9a59/Q]\#\jM&M,8U1VE[o#OHGOa?bP$Q#>nA-lKlcM'1#[%$0*U
%)KCZhB6C[TBG'CT/VZ?p>1.joB7["UGGITu;O);nBP93@bR8?Xl;/]JTZIZSlrbuGGU$kSWMeBB?@9Lfl#gFmd_;jJ@(n<6Mo(?P
%UAIt_h<D6pGUF8m)bn\jMpB8<?1Q$U;5pQ`jSRX!AMho6)jQsd+T7#c(=N_@Gk>R]+Sn:_4b[WsVd_Q*7dUS2;IY:-$Stdb1$2-Y
%#X&4#0'1Q_$k$[`;CHJeWG+b^ft6sWRb_k'+HnKG?6NT)Op_CB;hXr,%<;GZqdn'7SkM<%\&.B)IM3%%=jD.5"@2=QSeIc8WCd/=
%"`m2;^nJCoNYe@qS_<EYjY/I#+cq^_6XZc2<,kHJN:%B2!='St\9Sp5SUn<,a#,qdr=N-@@8u,-ZTor+-6Aqq\63&Q1b*=D7f\=i
%'jij#9=URlrkUaHY4?q!=8a0$/RQ0!P-^Y0\:rTZ'aWRSl(,L2m)oV2gh@E)7"poo[sp/cYFC6DD%"eU&IMI,7m([j(fs!N@gEQu
%:kUZg1<XmH[/J"r8?h",#A_FK)$/[5etE'5Npq5#gD7,DMCXJdMs:c_ZpAinB*<@.b]_h]_^Zpada`Y`o-:"$f\e:7-tL3M>kHN@
%mo6Y<#^7JYnsYigICIgJSsC$Ya+PnaFlBF!&d;uE?\+XTcD[WqRQh@\^[:+3E3sS^7cda=@j9lq^H)b@Jmr5j9=RQP(K/lgm:B[P
%d$d4I\$R)68C"M+IZaUiC(p?77BA(QN$fj\:ZS2fkm$fXWe1`)FV+h[LUUF>gB8\/IOh0oZVcOaJtht@ppE8KEV"hrkd?5-;d)<@
%MGi.oh=]^M-eO%kl>JG2k4:\fobW<CIk;%+:k[(F98ZV"+`"'pWbUG2AofMl->0%Db@V:d:2/["Yr6)[-RE9(;9e*L#9^XZ:M'#n
%lTt37[u3M#Ordn(>qQQ7A#/=D3WbU'cpY6RpSLsjS:UBT.pC:TSHWG!6s=.=,Q')pU$=%)V8g69fT9uo5.GuaK?^7B+iM?T&t7'%
%?SL+4NcXHd(@>f8?F/&'OdFo6P;5LmMGcFt32M35\>(q.Koc\fN*W`B_;iF3IDlg`8>@r*9-li:m/)$]4]GYe4KKO$&q%rOf[Z4"
%jjM+$O;)d,#i-5`Pjd^:86M3#?sHb9#K"&8!.do6@ieR-3rk-7+iH^:j-.fS\NP[^.3Vq`^)#;?Zie)ZhImasMQ"=!Mc;8J<hH'#
%SJDk[EN:'4^eMY+kFo"5'sCNqSt[b=$t='^5iD?Yp!IQf:UeO$kuL.XKo-NW<tc'pAdtfk?ON$^;*obs0p^-:bMP>FXI"<"V/^<7
%fl%E7>0<`aXmbTe:Q7psXnLh]@r%%E/]`N%,K?%,CQB;uqO<]PCPRX:@X(>AWSLJY,t/7D4iq<UN]Y>`@*WRWoZ[=7;8%C=Qfsj2
%/.<l.\;KZH!PD>(]bY9.mT4o]ZrpCK3hK,D!3PA%YE;-)Tb2Q#;"X\j4GGi/okac/j";.^QEu[[q1e3$3+)%sjDi"?+/UBK?Irso
%UM^3r[LO(R`S%a68$M`S_R?>,a=UfIe>Ur'%pQq*mXZ:!ll2*8\[T.5DK01`Kh9`"bN!DsW&J"=[L5\kedZk_m=Z%>9U]I$Y^%*n
%/4^hhAj.RO(rH7,Xu<$eF)dK9.V<(=WTD^lLsDrJ%BL&k?tF_?\#uhrR[,DP$gN*5)R_S)>f*X#^fro5m7KGFTQ[`LL)E&iIY2Y*
%OtKqNgSQ_$"tm8+c!i\[=Nc`-C;WOIo!6)+r4HjmI6iah^=60>N2)XUY:U(pOsjDbr9S)h'9[#3LKp^_MsQY0Qn,L\Q+_u^e2*O>
%88m&H+KS427j[9<oN"j*P/L$EY(W)e"<.IG1$h%BT<Onr(m)6STb2iP'WjTWjU-u9KO:7r6O7^$p^SI2X?qpN"D:TNf*q01!^`,k
%'V$?NP*0J^Y34tg>;eb-l(pNm]IOOG\n>uhq!!b*Y`lVM/uWf_p1oRA#eL4@*aHV6EWd`QYY^^6^\tcA;qOccZ;t$KFr1\1c>Mn3
%s"mbU@">9;Q@Z;B=l8pg?1>"GPDj+W'Me$"\Vt!!n:TkO#L6XRZQKqsl/<@04&3U1C;C:.bckj:pP*'+A0L]?T4M/'R#$IKE-VSm
%6jeEE$d[oM+W_b!'a`OFQn:AuP)WGX')d#=M2-KG&KY2\71GaX.k<,T9&UEV`>t\Vn[[M)5\RQ>$TL<SS6a).ftkm0;;fOO8.S_L
%!Zn)S72&Cqa_'>gW9'lATG"d(-\`g5_k7L2^EQi"G(&)d-qc$5%051c+E&d0'BM;iA#8"t@],5t7q7td!fk;QBP6M4<#qNL]B4E\
%LU[]@>7V13KtGRoJp,%!Y$Y<l.]2HA.)lE"U5CLjpY)"5"ciX`^(pIElgbc?f^@PsMhkn8^e-(!p6F/.8BZi%""[8SrO.6$:h"]e
%&_Xg,CN,F!/gcKskfLN5]FY<F6rf-o?m\L]30ebp/LHdI2pB[D3V4SY;JZDlI$Kk)piPU]fY+p(Xuj+^YQ-K+UUh!Dk>COt]NXZ_
%f>;Nt$@AK%+PS\]_d)O(Wikd&6u(]*`,aTYVl_A5WVF&>SnP2h`NG"2b3hN'S;LTT,J^rGe8$LjRb&b#N-Y54:hcUQ*F?4/LPA>R
%3q$l7=B#t75VX5DYfk_6)*]Nd;5VSM?0b@h+R]J<Z`--bZfBUq4Om\>hq-O9GD'XP&lH0Rjj,TCbKC/^6"'uV"1UY%#dH7MX#Mh(
%A/?_C46qa)Z%?L/(q\aTjGB*S#DMJ9$;,./BB8poXpkZTmdFVJpSMh&`AS0[QPhG=,aFHj"=uB6/Run8\M8e+`Wf6V3mJ!USG"q3
%51U*</;b=)/2@tO0EcCS($YQK#_clB[M_I+/!anj_BKN-$dD2['lu3iUiWc06ZNtS_U92,k=^@4j#B`^^f4$C(.m+J4,aTA`fS$u
%@g!O+fA#s$&=_3^+M1p>*4T`p+nPgl&'3'#@(j"8oWRFZUa!*PQR_p31ECifIS3YY$m=E4Bco7^W##1'VD:.u#^CZB5SP?I",hcE
%MmZ&-d55pCmr>`XYYL(CU1C[3mM!u^-@0ZQoQ=U);.>OF\#"S9[e,[^p3^5o;Gnoi\G)(TaMMbmY?tZJQi(9(1.HCuI$b2a4^9X(
%(D.(I*u(bumCceW_N0pp1I-\TjA(Bg0.;HCN*e;@@.UXE83t,q`:?s>Ptc(j\=qL0O4?g<*'bIUF=X87U0Yo.!j7eiBTVQ(^s+(h
%b[s=DYD\8b[@[WDhRm\b?B)j?(KYp9C@'=PqGep\aJ@S!LQYJ9bmiF0k*H(#]_2dkWP\+?i7^a$O4hlW#H/Y/nfuA^9Sdq!i+O08
%EsW0G4i2NE[\,R*$Qe&,XTXR_\W8-LGRi0*5QHlO4TMBf5_)j1+Y#&a1)f2eRh<.CU5pm;"DI)6OCOuCgeR=u)b?(3ES"A[YXWR@
%KhYS7&CT%*0S>blN^26nH>fKBqM10]TAW,A!KO&jH\>=0`^2B/^f'+_5381`0gM=]CLPRS@gkJ!C8!@6'[hR@r1"G];JA[B2[#=Q
%<K3!H9hJ:Bi7KM1dG80<Lbhircdl[S'3KRE;t:RGM>7VXS7V.b>ee3O2'9+s2\9.qGSZi,!N;C]>id9+XjA)M<.Qu@B/BBW9pmLO
%Fb/Rmro_1VLL^B@OAH]D9U31tHsaS)Q#0nqMh!`E%^24:"8NHOs(QCaQf3HLLUmdRW-;t3_$6R\R@J5W0MQD[mc,q7M!?%]ibGjc
%2"$.pZa*qq1AhW\UIe)Ni@GGo&kl$`2Fr-afMP6fGTL`nU(nCp8Ai#5+50uLMs[ruJfAE/3'SBMof<+FPrTTdQ_oJe@),?B!$ssJ
%.^)jfgTK6N/F(*a;%4oV,X+59KfdQ=[]YKS[Aei!h$gU<&3lR!$9-(KVdR[V.%H-+]2C64Nc80\FFC$_gnkFqZ:/a=906h7Z+fU3
%i^bVY[u%Y6cU!lm4F9I+#\d6j#S^iu^)-lZr$BlGkt^l1#)(9Dc,pTXKdWe=eH>-bko_mJBi0epP6P]eV#a&b>A8V%-6gX;@#'oX
%Y^^1OJ@UV5%r"lZ>_db."?GF1*4ai;rhs9N(dWOMJ9<;>J-;#d[)l>+*hS*uKc.17_?@rA`4-t1[7,7t[tI;VSWO/N;iejKq.MfZ
%7X;<)5#F0#p9$ZC\SUf2pc9Y%)M(9e8jYYm+_3alcZ2R7]G&+&95HNF^lNiLh&?c?U^G?ISks*W_5GSg/s-OCHpfCMZ,VTG&u5c(
%T3-R[+:t"e+F<T5Mdk]<$-s&%DDK$+U5rGBa",ep!f%C?Z?fP?/E4b[I1dAOpPjjBs%]_qFB'!2O]B5JSYp!4k<gs[ClkqTP2emP
%&Z%"ZGWQ0!gU<)2dDWiLG=+s?"`HX>;1Y<-q_ki2'L(`m)S+:b#">V%d=7dI5t.jVKLoap!1F&]I1S-Z.&;14kALNj,JdbI#&mq4
%C4D>JW'aFo&[W(lO=#\PR`ZUHeen,`[Pl=t'ST*c/52pM]OW;pJ-BDPl+kTT4C\_dSeV?1;<1F?+!M_3o$04tFn1!4&RE:?K:GQ(
%ADk@$[\h\CeuZQk=fP/;(8]RYLh(AN3'^5,^P#:sl/AIRmE]9K5@"tAS)+$-s6G13MdQMTO+6rLJ,819-<^A0s7^G\onU<ZX5EsF
%5Q0`"?iTf&5Q(Aqhu*?F^])bjrSbBGrUI@HpltCK:Di#VJ,$YEIe_`Kq;^pr3<"<CmsjSGJ,ORLGJ8M4OGo5Gs6B3j/g(*^5"k_3
%Io&B/K7Yu\n\8`Zs$asKo_DklX+,Ldrq13es5!-ms'L50E9dI*jM.cbINu!)#<ZWDEQACX+i;Z1'KsP-%)n::W;1R;$*XZaTrQ]s
%?4`>P%&P4SVcje5>na4\R&X>,=TbaE77q(YX5g[,!FBjp<'a>WR7^e-,)1^6ON)8*?]n)D+FU<IaVh+%.HnB!X's,qX28tGU@#gU
%[>UJ!K92896t>^,o$'4b?=.oY0#*C-q#,R7Vd%8Kd+rI=du>TJ((o-B)V"+u/gDJtnKk%[KVskh(QCp`J[*!&\uDnP2S_,r7hLsi
%H&?&a.)$aS-R.VC#dUUM0ef?'=D\[#0#1,f!$J3a:iuq#.,r:Jcm*)_I[2Eoh?K\;o"9.t&B[:9h4\'sa:cQba@9Q_#'5YhG)1^6
%Gu<HJYYT61"aE*F^Ig+dXu"iVSee$$`p66[S4Q:^,dg(F#Mcj<-[D?9''q8pgd$KU0@:0D-_,l,<gXMJ%^p&tMhM?VJlLmlcGB0@
%>!s>h+sXf43?(o=0qD7MVoA&)+_6o$S_9Flf,!HY4`ZC`)[Z-o5^#A<7XLU?XlS+oe(!%=2*ekcZ]Sf7C<D,c>sb%8p]s-qmg.nK
%+:PnlK^$$uZgc5[YF=#)mX9Ha%6DdcW3J?;WV?X;p>f6SQ'qq[2fV)2'9-thf#hii?*;Y:AJ8Mh9?CKY;K*CH[cujZi6M&+i#,CF
%0Icarh*gr_.RU(0aUaWH?jNQpn/\c(NGCn$9G)J^9/gP@"ols#V^4p$FR9>Qig^hfMDPa$AJ411l@eZ\lm%qS14>>moq"@U*"6'Q
%'6_M))7]fP-fmA<mR(9Oo*[1-R:ugA$6ia7<'iNM/"E_(q)`><6Y=/\=i,$lrK5[:hpdfLMRkq_3&!IO8tqG=PgH32==!Mf@=K[U
%1BO5=<J3KM]d[O%%.=:%JUCKW6($m(6@TUZ+]g"Rfh#`b\dhEdpp'9fLXL+R:tXf/[nPII)ckS)Cj+@-QB?7Ik#fmr,&]g"(L5A/
%k`R:DGl8\3S4gPE_rr<1kA=q2?X91Yj+iiWaq'"6N35mJ?`9%Oi[X(bg)5/b5lO78;@$1c%8l(`E=b5T##tZl)LN/3Y%8_n()8ES
%29U:gMa3@b0p(8iYd90c4=+[(=`,:(aXCSabj\q&[o^<DaaB9A<LKngrhZ+aruKGK>;\a_Tr[@80;/n":NK`8&H4c2qdjIP'<"IA
%8.:46niJ'*^q^KB?#>;h^6GV$OH`:jcu%6o#J5KCAUIGCr]qL$/'#!N0c:`]WHn:T_o>+\*FHWjWmmkI^l=VGWN4g[!pMAi(@nWA
%&.Sn2ZW[-\Wt7HE$!@BhA&SllC73(N4lJC^+tDCQnXc+!);u;9KKXm^h,"nRpqR5:ql"2=493M\UV'21J9McF$O;=(.kS9uY/-)*
%To*1ijXa7\Vo1UM>82E.4h=(TH>s+G"BTM>\QDYMfK6ZF\#WScZ:HX8(G>WN?c`"sJkehmH4/)Wa_V!*(c!!&-,2c;m1KfeHJ1Hl
%XXH@DBLuRY6E%;f]#c+a$\[J[I/sbF]EOoS@TEV=KeXe^E^cWZ/N(RVV,s&M4bsnX^Qe-LD8ES/b-3L2gbN1WKTdCurkaHm0QMuk
%2ZR5c&\`#K77%*VC00-qNrV?_1)ut-\S)NCYT\e;Xm8O;\(2$iWc>iWX?T`I*me,J1^GaNjua:0$;9'E]);P*H**r=5leV]N@:8%
%ROBp>fH)Tg-1T;V+GX/:X2UnY>;&sP8^q='pp^lr5l;RC+"8P"!<4fkBe"7$o&$--]MU=p5uL?"O5LL),)DP!:VLn2R1102d7Xk3
%#:gq]Jnp7P#^qZMl%c4;lM?'Fg)aZ*mh/c]`\%&q+.D8rWh:_2Tp?^\,<N]aDZt)P]'f%$4LS0s@gV645hM:U?f9m2Tua*cpG`0M
%`Z-/%T:MJfb-%3gSb0P_'TR(Q:[W?c6D58c^(6ffWOr=K6$k<>)8_:OQ8f[_)&>o7*'sKL-)%'<a3R>XPfqlW=J"`j(bd?_7_%!4
%jZ-5?=;;:\<c^/,B/1:k7g%Bf`Q'E_92\\GFocmKkQ,RBCE('mgM"o21m)J5)6"!E^T&daB]A`CV&VY>LZ'XDHU21jUpQaG2NHMp
%!CWk1<!ZWl/40;`,8$kQ2%)oSXfXL\lge0fUn^+s'Ul@3[,(lc:G\J$>@A&4&S\-?6m>HQGF@!<jh"%2?$!;C?@Abpe`6Eul]pGi
%"5`=oYG6SO+6K28HT*k0RbRHl.f+^"1=]ZR2^g2b*PgMo,:a2AVRd<)]52%;*>O*:,jNMSF?AE^P8,5LKL%Gb<&5cT)LN*%lo'oR
%f>WG-3PM36cq$gL2N0F+m](02FK]t;R]3gM%\lZOifrt0"#!]7IB5`NbW_:&D=8/DMq]oLa2ZTN/t!UXBr.Y\AkDIXIFQ#s:4SLG
%i_3aPdA^p1D:V\>-t6E4S_,$e_:m7d9U3M[?%o#]2f]$Uot6jJan7">ap>/1[aa5.[YHr%i1j8r#X`1"j^3=+EJ],X*AQK7nu!FG
%6d:>f9*&QPWDXGHMHdb#X3,T'Xsl4R,quQb.ZAL%_P`G/L$Gb0]`o_YPn=K:7*^`nF>46A%-/UJj\p;?R%/&M(rkYQ_a=d\O3GsZ
%-JWR<J`TkE4q-,6Kk%"-XO'B>dD45t<_Q02L2F5b"bG,AN,(;'8?nTI,@K+W`oRW(PBITM",^aC#\/<Q=&p78o)=4)mIIrA6a,L@
%Qr/qOiU65H^'pn..>!o?`8cLham_gHQ**@/d3*P@n(N!E]0uD&Y)CbhcBb)S<'"Vl!4*^3`<F*&5WN!-.;)jO$&sdsO\(914^$mD
%+GlWN1P2TnLfa!6%U;2GR:*dJG[NaA54bDA.cHcUkuPnQFXR-\3Ur([3RT5c7`cU'e?VS(74tI[`@,4@DGIDL`.A:,+l[?44UZ?9
%d%H'_%@-E=&8]^i&8sZ-@3op'<bIc)F9r3uBeEk[H4Ah%/$NQSO:B?:Z!WrDe-2>,$.u\6l]l744PM,+RNF)W9!%-5;GHjGee)PM
%.KqZX4^]DR''0*%%'adR*HQ>-Mlp3(q2SQ"__+MjHUEiJ=*'U;Wk&E)/tlXW,%`_<:qot2#j.$bh=JK_1&J,k5_'A^/6&^Ieli>7
%kK9SmYW:RM+WCk=MdN:kL3`MnD6R7*j4L:ujU<[9E%sr%l:(Em;\!HHD-V7c2/THj+(<h(S+r4UoueupGXDrgH7;QJe5YS$^tLZV
%DM\*843$km.3W]+&$?+[#T9BEXqD2,YmfabZ=0k<9-r;SVDXS9Y0_>*,`$lgBh7T($"h5H</b&&U]<r9VV%Pb)UXi[p%p'b_d,g;
%QmkE6#,lVc-4BQ[R-,e6rE2Zg>b;Xu9glA@CLq1rF"mV*`B8(,$OI'KJ!jfNh6>[lQrdI7R7kp#D=SpO;@&)#ePri;lh2:M\#t3"
%fDU_&V5OQN6bQ2r^oO7>nu&m#+rBhl;gr>6`m6:&,"nUiPaKuH(\eK;=c\XIS>X#Tq%S#_KP&1G4$.A%>es9kB"9%;OQ.\`+ll)s
%K$eSj90)m'S:[6$r@CiFHGm^QbEHI>_K^^r;@k]b@gJ&`'dYf1"SL9H426<8=Z8o)4im$H,;Ch7.LJ@mFsQMc4YI:q)p\V&R1YlW
%B+?q-eJ$es8r1RM-`jOsi'"Ke'Y/VIiQ`$8]b@f:C.cb4/^*%UEbmI2*8YUTRl-LldLKjiTubQQGI@d"#SX^O5=O`hm)NG!,$<"t
%AQ_CLB(\&pf/E_qnl$$!K&pRliql3#a':%Z2KjeWB:]T@hK7/5W25IE0"&^(<07@nd?1TYX>ka@h0XdFe7N7/\$H%E7GVjfYFm8n
%SI>:kfC\VDC?Q)D]W#H`LS`I[4B6A9.=rBF"eq$;eL=SY'R#"^H=Jt523rCfi/pSB#3\?QVo&_KM,eL7";Qd(JE0Y)e.%J_-6PZj
%f(JF34hG[LEU252<q)Z#Coeh6,ElBc7rF(L$gD*+7cM\?EL<s?b-iQPYO[Kii=p[1:@_8QfN23Q1J"*<WIEEZX)&HR=rAO?p9N<Q
%/d!9FFPTrE3H!d0`<+"]?'[;$9Fp$$:\/2T^j-jnl2IX.piS4l7uELEJ>+DG2B_e'?E=.s&)qIc[\&@dWa5*]M\:Z![k?<)fBKhX
%Lgk`5WfCh`AIk1@TIk<R.dAagm?N(F"Q7I/e14&kctEJf&$K0DI+.`p^eU`[#Ii2M@tm=&A^`G+nsG?[[bCRW65.A3'#TO(meplq
%(4I+E8amS<86n)H:UZXa8ShWr9Y'Bq>k3sd25u`:/1b=<-9=<,Ca+3gb?hjpP$O0dZX9`GO3BScjiUHiiSBE/9JNbdfGoVnMu5\(
%(/^m0<%'j9#q)%#SZ:PPp:kMF7CpZQCIgc\;)MHqMO:HARo2MVU`mI3/QM>$>")ED,X][N3.M\4oJCG8S7/HCCG'iu6`.m6e3*SL
%h-QY1m0]eY$1otK.U#e;LRHY$m>J9M<W/'K<G2Sq3ePM16A.F"1Msj"UubQ\M4DV2Co-*V*TYR<$;,lY?`Fl<GP->.NJdEFkeSn'
%Qg0T25+=BWn!s2F^<)PJ;i/p>(P5PVH,qHtZ6/$s9+Jtil0.P4[Bt:7S=UOH-+KO"ftCt5j=o1t\L8<GQr<aB&25Nnff3'!1BM4#
%cl$7l;%[RARLBYV#feOCSf80/T']pU't/bd=7h6UI]h:M]*jODKm9UF*OA1+X8Ja!57"o.jO6dtZ-6TN5m,PYR5>K=@MB,@hLp_Z
%N2a=%O?R/VQtHl`:qQ+VfnPh_)Zkqo8"6>(<#4J$X%#M0Ueaj)G/#60"02$I\j:TH42;DgR5L+GQ`;Po?EcO7\)@[,`=q.6f9#iN
%D$Hnb6p>IOTQ=RlkX7$L'"4C)*f-EtA#cQ3n9:auXSN"0mbS=//0LW[,:1i\lo^IP+NV]1LX98e-2(LEil>H@A5Q!*<($*Z[9iI4
%W_$dq.OJriEUh(K1QAYm-,OFHI#KX\a6b`JDhTMFAl=XG65QaG;Fr>s7P7s$.0rUffK5(HD1Gc<-hEL\^4_>J6Y%FqjO5u`-9o8%
%"IJ5e_%SOP[Z(_qp_?.1"QVK'P+bk:8M=D@"W\8ic+E1q'ppi/Z^^-JFf1:P73?X@:ba@48QKp@7D,#^]kLA(.XYaGR`J<E?\+G4
%$<F-bC9\I`E7bm6:b;SHHPis53pM+u(V]JZ2`iUgL1_,N6Bi224E@$SYeT`m+R8D-7eHAl.I#Io'Q+L/1So_kbn2/\G*C0I[g7<^
%.ep+Q`3>J2h]k5&`mTTR0U5YGA:T!OP^1n0-Z,mo+C3[\#bBf/DE`Z+aqP=r#37]a3@Vq-4:@dX@E7At_71u,=@`QIKd;BF#^8!8
%)0DA?@:FBYgpZB+bg]6nDpR=I4Uf)VI%JpC>fjVmM0\jB.<?8+I1g<aL"hiimRjX`J1&sbD.(jR028QZ)jdJ$Robp!^-j0d'/<VV
%$^9Wm#dUIM1\%!kmDq=g"^5tahRcJ6a]9EWY'!1^[A!>9ak.]smb]ls"A,k?9T6.A/_CnZVXA:3qC8pLfo34.3<I_dFoIhe2glRk
%\3U8Xp(lbD-A,g_#56!689Nf6EQhl:Q@U`^>HXn^+(i=0:#o9(k2`ILgl?1Ns-%0CJZX(MGIY=r=bWen-.T`(DlB7"]ii,4:p2F[
%]B4]ZeJNWRpGPQ>!A&1$q\gqX#%6LD5G3I+<,\idW?W&4f$+Ke5P0"5$NVm;H9nk_,2piSJ2B@rN?%g(+EW=97eAW>n26JD*n@Pc
%Su1@Q34)-5^1kWh:b[sM-!LVQUX96Rn%'M.%l(-uHu(c,3_K/*+=(=VD?b4uK#'d@<7jL<l,DJSeo%i1L/I<F^Doh71"-m'3kp$^
%g5&7P$B3(lHDu0s`O7"=2d^UseR/S"M<_Ugncf!i^0TE"A"$k=P*(rOB+#VLjQG$L"*$7sbgIkg@H?*MhMi-787;;>Yro3[/'0-a
%-BQ+`4QL2^Vc<qg6-(G#0FVg\`"Z891Vd+)H=S\e]+#Th90q_,duW&pSahp;ie92imYRCD\aiI\,WQf,rj\!hJ$V8l5GOEm+Omn$
%S:148d<,6L,hoqE/Rdu/E^HrI"_HZ$O'BcE[%Aa*@GO$;=,ab#![T;f62$)maqcln]0U4T3+DZo.it(!n.1VV>ED;;F"&T'b-1LW
%N6=B&k?gH,=Cb7&4H9DG6pt43>KN$b'RXU_`4_6,>/#J>3Xe2eVT>nIkdR);YKCY\$&@7)H>fQgWN4QM)%k_`NYQaGasi#%iq6tW
%H^9;&k93JCTW++"Cdd3[7l(J$@gqO`!W6CZ)/qhRJQ&Ms"<3>"?d$*m8KoZM@>RtI1e\KfD64SZ(kTUfT2]KF3=QNI/>^rWO:@=k
%6]l(4nP/^(iagI@)_ga#?m3[eB+%[h>L0]+go5m?B=`t/+P6'*7!J0ZOa!"Q"e#s*]u'G!7:K-;^gnql[FTN%#B$T&7f&_C&7SMg
%83fjPNfjIK$P9Af/l7s,A9E+`dIr:5?<*VPVI6lX3t(\8koITa%INB1F!/"0,*6q$$5$?)MGhr4<;ok!$7IqP]G2/-dV*G3>(b"(
%!%(Zq2._'&/+cfjFhsc@/^K4L'k&E&=.FRA^&d;/fo7Ur<-?+@+,Y#h(f.@HBTO-U$a;OaAbT-iU-Al_X8A!O8#!.J-u%*\W57Lg
%ZIX?V=)M_'`RWct,X"hQ7qb^S<7egf35UP2\*$e/nS:I=dK3E,"Qp8-N;^(t\n-pbA)#7^At-79aCsk%];N5@2a@i,e:Zg/p@K,E
%"FrTN`Vs'>p-),I'!75##S<0\_ZP#0p-ET(%&sm`^;22;%ZD#1dY4u4BY0CEi//JIAB9Ar*B9qT`cH[m.G3KJp?>mu:FB-8B5#3=
%!eWNG*PAi8IC\MG.*H?[4I5fQq[!INZVb"cL)]44A!=K$@M2h%U^ge!FbGEX7d8m8Eid+jdKW$JP:Cq=9aO>IG9(=<JB,tBjGLo6
%W!6sK"YQ18EUVTg"TI6&c->&=&]5&EMP;ah,qa,>'MUjO,R/`PW,9"g1!Y/`2).IMX4$QI_aa5S1<26-E0461O+;<k[1K_De4r=V
%VVV[S72G$c'_B2H]5taF.!?M05Itu!V^S:cc.Hr[1S?#;QVW^HhH$MC*dM(,RTp:h!]$&A<RCV()@<+LkCUVBG*F9f,Cip536%]G
%n#r87EV+)h@NH,"UP)2>E+SRpTc%r5rLu>nCh^uFE]S5t>hZ<XNJUaaHt.LY'F2:3G7[\7>=uAH^J:[I%nGW"CUlDU@h<he&Lg%q
%Q?N.6dcH>Ld6&cn@?(&>O%'\'-U2lJ\I]H<4?%,t!0%5>':H"WMYEr;_heW;q46H/eaFDj6(M-=GQ?">H5c=)#YUF,0qmICS4q*<
%*mBa1(#C$ZC)PoaEnB&(\Es.!Kamd1^#:LgR#?%XH=W7@VoZR/&X6sQ"4g[.__dPiDrCX:6:,#d-(O"YN!sS7_@MmP4`o(kUMj#Q
%a=S*WnXhh1XFml:1f:Z1b<K'!!rD:D$iYQA<SN<V6O;*4H`=ig=dke%Ylg!!0n#ro%ku7+6tP/Go^@`r(c388dY62`Hs-/]Q?HtA
%'3Q+lbN5og(-rE_MQYk>Y+OR.gJVq,7q(86Stl>HI_!sghe.4515R+RQ4-GJ:W5((`QY!Is":C2ZA5&`BM^`nc$B*loo^;V`gn2K
%b&atS+`Q_)M1s>Hkd,VRgkPsJG'a7r/)=4hdF*/b]5.Zo:JS!>>(Xl[Vn?QA#,s925H:a*-29PnCHeJJj_\Y=SGWNb4C9uJc@VLA
%B&DFQa405[).tl\_d4&*PK/.&B.;Lc<$hA3bl7?VA%V72WiZ[XkZP&7/&=r'`]cgqI`g?UqlriBY"/K0"lu@F=n[+Q+kJf08;_r[
%M!V6:"77-dZM^U,_RHN3@7YUi^GG7P>tXGZL>FhVU;J!Z]^6_,G1,Pq*)[(bTbUT/27C'15/M8BJX')%e87./Q(Oc\m.AFuHj(78
%4e6E/+I9AnjjRWm9eLf$;F%d.HPr>\B=dLL,0BC_(o+NH`=D,8p[b8jOG,$c/u,h3`N6;Z/h5\lV+k,j2qd3bam)0i_8[^,[E(JG
%3F\%9H"nfr_@G^N,4\3#:@Z8jl@C#3Xrb$Le1[>[q\Hrc/m!WVMmOC,-LLa?O>G3nJ0']AQ56Xk-tcrs5^W)4OCiK\"O"_@.$Lm*
%Pnu\CPrRFHk'6l1W%*Tnh=:A[0ZY<*3LnW)L2/CY@XNZ2G-VoN:7_FlBus7DH?om/`,O?MXh=ls07M_WN3.lC;(Xoe9gH:NRe90m
%FATRoHkK3Ll,prZ101/7NRTB8deT)/NA>>'5DdphJc>#Vs.Y"cK=(Z`mhd/H`IJTs$a5Wk*ujIZ^Q%kTk``tk]_V[%7&Rd1&Za'D
%Tc5-1V^gD'oh7^pbZ.:]>`CQnN(L-Q!%4pnhM#N0Z3H%68V7oI@bX0dY3n5G_"c`(,U0iTIHbM!+R;]HpTU>l^<QghIphkVJ)hPF
%Efg@M!n3\`En&tk00IIr[A%aa%X&>h2nXKk=AY.66m;UX)RZf'5Gn&kHmLY%<#&lU?5OQH>b/t4PjcN#O/g=(&l#oQb=0lt[qt56
%!)blB:L548;lk,t4l"pCk6*ra#e*[P*=0hCeK3n=SBnA6Xl64iCIBM$+WT_0p!0F$0.o5h"KF+u8/3eJ<Of`:i>!fWe/\drbCG:q
%+("4^_dDSu9(Z"mGXA?\HZnI@T#eRJMc\WUa-!@E'?hdLI\-MI]q!h47XMf?$GXB?:)>gHIDWW),HWFm-H8fSg)S=FoQf(A!5#Gl
%?CU",>MD6IX*Zk8[!G]52_)lAG:>?%pg)l_Q8D?k4oqc\d0%A,l3J0;iW9eupS<d0meL"[pf=/2"1H%iYf0$25?$MI:nj67_"<*7
%4&$;[`,r@lrNLXLTPDs'`.%$WCK@P$I>m+@j\RVi,(NJsUY`9Xl62HCM<*rV=Lr;E/^O1M1ik&sV%,]@A7&?:lRdYPmB#-fWG@0%
%Fq5<b=p4/oH94qMZj8a?$7Lo8b#=3S$q=gnV<"SonQA8<M^!'KmZSQ)2=ijh3I<:<&c+2^#/I0@liqVtjigT<U&I7#h&pWbg9'M"
%H:cC7kNGClgbrD?Dfalh3I9LrDKK.1QJ2G"LWo,diP8"]OBbY2ooS='Gs;D87KI3AZcjt/`OXmK\()3[fXf<HQEYu<$#BU:4&SJt
%be#Y;_#lSgjP<O(;YEc7KF)WM"?6DkIbM7R*Js7PeG0YN?\eF]RYg::qUra6r=F"Q"l]d#7J>O49T7b#SMl-ad\U8?0K7)99e*@;
%+/d1d<C=H]O&tY@8k+iVA?ffmH3A+pItN=XI+\$JFLcbNB7t/CH+_^cHD5#O8.(ho*@RYd056<#]0*]Y#ugc9khW+(D%`"(le?%Y
%NV514@M=^#XjG97Z6!)Qh1?o&2&+N<#-L*.9K.9g&RgE5BoaK`TN8,gWNhd7%hFBsK\%Yn\jgj\1#qcYA9B@GKL6_-a@qluZ9.3?
%:>NKlNj&f.E"UuD\sX+WfK/.?279/ld;c1ks4oV3*c]>(lre'AKdk)8E.c5.94Ip8XDq'q4e%"d6FZ;JAOd'@!-4`^(J:oKdfhRj
%#k#Jtl4o$Z?#qk===N7qcSGqdXmAZ#:8#2%"0%0tNa^aX:[55K,Iijmje?180]-LX?\9n`$>$%3r>-1D:DXT;B=XHXGug7<ED($.
%':\B''*lC!P+kW([48PmaK[+$0eNu'Q47iIB6CsFE>=@AG*9D,`tn%G)!RSKHD!kG\=lJ8X".7-'QhVOpYjQM6Na3um+Q-CXV'J\
%lu,STX-NjlC:+Mu0iXWkU:9.m@>cE06s_$n'n:neKFSCP>6$ol8t?F#'O*t:iO-9%>CC]+C]GLpE=lN[=4gea22[ksE44nTD4i%[
%_l?$A\V$k68$mH?m]mdj$gSN030otqJGkKt?FR2t8l<fh#--td]9jMKD3.h[g^iLZ_LDi:0jDDnf@bdV9HeIAXj"Bq2N^mm4Ybg6
%hlD3k<',HH9.86q@J%=Nj=@ePI\l>(.EYYA!r3K**5\^5_p:2!gH\quC'I]4J-k!Ce@@kn#en].7<=)$^jPd2&Vq$?qioW@*>lnp
%I(R-F<fG!X=I_S1F1/T!S9qg(%_:4"]E?O3U$:0b37&0,^@i&;e(k>n96W%*a5QEa(?sJ\VVGX%!VL]R*`ib7%^\1jFpi5[Br,oj
%E)cY/fCdPWU'hV`Baq!CO/._52L=NYb)U[eGSWp"0u6GJT1O9((noFd<F&1sddY[,Fr9_QRLW3J/G#Ue90NX#U^Sqa6&[<j@;7#4
%&lgg[K6'>-B^1Y)cT:,2"Jbm^=[.SpOH&I$,b_GeS?mM\T@f:H"K1L"MfMsu=Q=ZKDPFMS3>)PT%:/ms4$F>$.7H,*]RATgW(nLA
%5X9T<RuqbX<KB1E9GHS:KJ74qCUltKblAO+aBi'^j&AKi:fl)q;@Z]%]#m)r$,r3=R4\#gTU"^lX@&JtV&#Xc#I%\>9WOON-hZur
%BS11qbP$`*aKsn.b-i:WG)*:Ml"gEsgX69j67=`^OKt=Z/GcT1_$UW/JTg;_>Y1VQYf5GZ"b]ZO]qGa#9.!YLq6u#lKM>@IB6Uf"
%T9:3eWVsr135b2rE56;;KS\dB%drf-/9nJUmO$0`!KCL)/Sb#O8uZP8arLBe51i8[i4K\P0r?;PNo&[e4,Z7@L4*>pUDs1_\]CVX
%39"kon6>:d:io"u#Cl!HF.)6re]_jOC152P96/_8>,4R=9UhUO_!8=J=NB'>im('e"Bd(U7uQ#pc(`;VBsM4dm4*_GoA.dB9`b#A
%"?"GH),j@1-gF)>.90HR^?92l8tE&IdS+3URFNKhk#4tm=7fjTOGLmM0n>`Di2is74+pU,L-5+Vg/&mc*iJjWKMPA?:M1^"Y:.Qp
%V=5:m2#?Zb3o@HDSkb"sP.!"m<%!N5-;V#pK*kaI3'sN/#F.-&,,_%a3434q-G3k6/1WGV]MYP\dST@HGXck<E*f3BPPLoHAK-4&
%L^VZFFk\=s7&@1+!tNMcpJ]8_49UV'R8,tm[aG&I6SBD`A?O<'\Q=+t3"t($r&APX7ENJ9Q"CP8AgIYC<#>?9\a-@o@IH+^"<gcd
%9Bc*^@99QG_A"N12Bb">IoGS$LCh)3%-84,l[ireH:FbM1jJetI"h(UhT'u&Z7KUL7?cZd(kcgdB&Q9cR`()W(nh,<-!m8Z?a32A
%1T<g24_IuYH3#Aq@FT]-U?BM9,q^a_QKSN4>rCc1[*"il;I1+dk%oo<7RGWoV'Y`ehkb?HJ?/CTVF-5iI%Yc6hh0mNUcCFuT2;7V
%G'4G_RbT(f``<MS(/2a5bD9QNf\$I'.Fj,5mC"^d;:*#&c3d3=Z,'2:^80Fu"?Km:ZU/Ts&W&dp7dU>CbV4^j;`c7EZBFBTPQ7Z?
%$j%g3n?+2ea\?Qbp0ARHY-,UrP(YRTfkOsl5V?5m*&[nggIaJ^QKiPYns(0eK0<)^!-H@=qB1&!mlN>F?2=ag?HkD5h)09GeMSdW
%X5X;I2[hK4FC4P/3Y=HKn#3*Md`l\/fb#ha7:5G&gXedRWl63P;pqLTnuKC1G&M*q$Kk7P<u@UeQD_*Fc)d&X],%sFT$=e+qR(mA
%mAR1FHS!!Q?9B(]cs3"FV8;#dHE3c0M\*aecUG]/DkH84^aB1IOja.kjDB6O]%3%TM^[FpWI6U":m3980q]S/"*H)A8s5q`%kDD@
%P^]lV7^l]oo$S`A6Kl-U!LY>87PY1jQ2bq2CD:`dK/FFSK]^7hmM")Bj=MKrCL36B<-#;&]u1Wm@BDOV2F*\0^^kHcfheWU6kBiN
%!j@0pU_)/;*MJ=3$uu>_IgESh0sqWrGYG?FCZn(?QOo%_M-k7Zc`j,oD(fr;OSthQAZ[;qc"c!mNM+J*jiHq$;l-)`,h\DFE-&cf
%DH;$]fW"<6eSYuI\2\%D>42HM6-r#5.:O_g/#2tMYuo0Sb<OLrD-[W0K0CiapS\;C3ChD&B"ujZ3N9\i.k_<<EMAIL>#7'n
%_9LX7Cq0l@jd:W9\t_??j?qpqNkbBWLmOGL*=c836+1J7WNZ82d7tdh1>^o>R4*_L/&gn(h%iK,EM!so5#js;n]`#/KY5=B=`,"/
%dP(2roiHH,EIR"N_'EBYLkq@KZl^JK@f@s&S)E_33@qa,Zn$t050%C(=nQMg*`k+89lu+dHRW&0V?#gVZTUVahSoi^Zo\>USi,A<
%2_h<@\fNN[S$<g;A7YCa3[(C_5`)4[o]N1)elAmX<"Tm7oOi<UB(MG!Qaipg6ICar.#q2P:u-_rg4j>UnZnU3cToZ0?s9:TIrAP:
%QVJ;1+hAsC/A]m.&f#q!oR1]P;j.NZUGj/qYFB+g$:,OpYI7CrC7r%)TMd[1Eo4$BDZQQ0E&IY0gUGrM;N4H)`e-Wl&lB@T2oS7d
%qrLD:U@eLM_N_t)p"8pLeQj?NUN`m*GuWlUBnXJ`G)7@J3`VENINM%$(#ROQ6bdGFdB[7@/C+g/2GQS/!cYl,Sal`eoch*O\=CW>
%\T;]QbFStZ^_qE'\:0#8&<4"c(sK6VPM!Wl+A@NC(uc%d!DZXs,j\t79f%&QXI6?g"58!m@b=Sf;\AnB7E6Fh,"b)C7O/0a[IATE
%&$a7"?Z(OD"lUi3+*CGU0ZCq6<YL6g$Ua3;d(sYaOfpmNatfYqI*-eM%P",h->5]NH2:WR;l*Ga^EI`Vb,ubU/Bb]>gqh3P;CgAo
%]jkoHH<5`1JouVU[\/IEc#g^iDW_jFVEiO)n>*WK@PQ-F>Z&X0-F?E<LHsa_PD%_^]>:\FL99B2A])'&XIO>7A@Zs9"(Z!;=?O=:
%/l*X'Aj)8t:qYmtLaC:*%Ub_TACbktdEY,gi7gqBHE/=.ebrZ#"1`r@@%K,lRtM\g6k:f&5fHR1Vo%]E,V1YUccGKK-?!T*T_I%2
%YZ4/K$4Zik6SO9aU,`e0kBL?ck0.u:Nr\^)*b.irpFun&oAZuC)ZcKF[C3"=X1DhCTG@!siu0P7Mj]4gDPKgE;#j>,14H`sVM:Om
%$a';HXP^ri%M^k-A5YR*6%XF;d6b"K!7sb2.t:;>Br##e+XDo5BuGUiGI"i>[aZ)AED/gKG$8V`_#`79qn5i3lnX^FEbk%'=gu4,
%QgB`VXQ25$&ad\dhdEGhO#htUGdET[BXiJgY''4X/R>A!@D(cY1b6H[YUQDFf'^MmrOJ355VBS=\GC)Q@+>gW'?2?l/2U_n##%K"
%U(W^$F]op+KF(EWC+)M'3_A&?Oie:E"(^g%5s;+'A9[/0qp97.OG%PV6tM0&gBq@(RHtl]n>EU=^TK9aHE3:hJ#!8I1?S1f3uj,F
%,b3el6gnXCiR%L?CJeXT]edAlPf$=M,&([YHOX$CEEQT1E"+:)l],"uP`"kg=GNHABjPi?)7J,<QY:9\d.;dsG*-_R!"?/I^nX[B
%.)G]QDC_[#mqsi!6<d?(HnTc+M\Q@S-o/O-f(=;6Li_707g;%;*>LVkIu`DU)NT'P`0u#o1C3(@;?T!P<ju5nlD9VHMP0e6X8Bnp
%DF.$GYe-;MhFohps"PHD$"4$"X)o+NB_aF)3-`_V@`pVOM3$ohgqq?Q'li=bq6&75oYh:4Ec`Ho"2LJ%*L;QHWXH[,64%DZ,H#;.
%V`B]k@?!^0@@tW&<R%W?53C6FK3H[C6?qg(eOm%MR`?1t27F/QGq=_Oc,B3[iZ0sqmGcPM/"7orI;>E^E3b<j*73AZ@CC84]D&*m
%)q\)&ntn]Gkb\9.TZSK%!aEA2r'G.5?P2qu=a^=3mHOC]_-#jj>N&D@\`<Br6dD2uDH)i'>8eD^LbP-k*BI?=f]WmYX2fF(kZ^ns
%qfEeK!HTOp%RN,J>*j5hPpK3.#<0+?.cRj0nN2h`,WE:",#XYK\add#[*I:k$NV-:\Zns>%tM]UOIMQ="G$]Tpsj/MoT[@<]4h]p
%p%:.R"F?(C^2WD5B:QK4*!7<<JLTqrgOS=jm5mXUnH'T(`-t/3IMkji>TQbXfH#*#kJu>H&Y>>8+cI.!U'0TpQf!dPM9`l*9DFA3
%Je,N(,muICY-s]`JN7GqFULWN_aZki^X.q?+Wi//Rs6"K'$$=er_j3!o%L[C(QYT-,6W]-jC^+YLs=@cIKepl78$;4DC.t$D$.97
%^8,8u]WNu@aYhlW:Hp_D)o0=8\,G.q*!LD\fKf3@$:B?U;pBI;Q$k!o(6Mhi@hqTB/%PQ#mT6mkgpZ;OHO045!u-7=YqAY5?n=7j
%jZKUb>_RG,D)Vb>[6SO9/,$8rGr4Kd:V(Kf]F;Nm)<C;R@U*E,YT/83LUA'k7@?U6=[$&1%+0MV3TN@;`-JYTnSBj(OK-AFkHtpM
%/2pDS*Gg3q,L@D\F]3Rl%.T?cIgGH6Apt(IkW/;N#nkWVdbdGO^-1(h<g$JnZ*c.[">;;mElN%R>3>TdaoXh>XJ[jJGGPd9@k5i?
%O.-c5jdBV3/oM$4[dQEG/pO,'X=0WZBT/@B>@bWe=7:[YI/g.O8)D76q!,B%qrbhc#(&]\IcW&.PM]TH[/^HU6+dKSmFp@&,[-g_
%oLgZ_32ikhrc`]CF*r#IagJ%,gc1$SZ86>1W9oF$^sJWTclD8JH&I07GSWq4BR6,i7?O.p6=umGWJEX2:mkR[m<7k`g/nml1:6:o
%RAT'u8H"hb"5Lc1.%iGbFOg^AVMN5@mVl9p\C(FO@p)gT]k$-X42<RpRcJ<jlCO\%/&:Pr*c-De>[rPIS&41kK>G?nU4WLh:',DH
%q,f0n2n<+BV&_G-FU5TO,Y1t9fVW6-_bB>gW!7&LOtFd#4<14&9YPl<bl]*sG9n4X^6]&qI0X4`,^3mAEj%%?_m)Ab`>jV.9>DF+
%!1FR2A>5,\AXg1RTb4W$0O_'r>G:It,+0(AgdrNPZY-tg:]j,Q#$T'IdmWH67rX<X+Adr>D;pVfH"WnH30AMU%JnebTo1<O\Tc_8
%:Y!K\?`/:N;-I$;6.LjP42A3Za/3%^--]YnEsbih1S\bR*N-3G9B-?AK^EARR?l[j^+1-Tqko6b!:>ltKbI(eNY`='H+T4u[nhX(
%WW3L*i:^q;.2AgW"e7#@?-"?l-WV'`e7j6i.=,ZJIV.$P^^OEsr;s9o$!h\GMkh0NihW8qhhT\;U#JrU5LFiXBQ+>.XELc@5DLTc
%;oa>SPCplZYZWUehie]ioUIbme^Z>dnp!eU>FSk(\"'q'(EPtgDhNM,YTXrK6K(PQDV*A@RDQ!nd54P8O2S`qnA?E=bHG=0>\nSH
%ht=!hp+9W`q&qB16D<eGN`?5(St:Vcf;5pBmpcJY/.k\)g@rWe":=%O@c\QIK1@^["2rBggNENC4tCo/0XH\+pIVf2qaO7g+#/;H
%_p="FFZGi#$GNd*Xns'QBb2LOA^*kbR6rrd.]L[<7pn%eK_&quaOsuunofL8BU+U!hS+8V.t-N(#O8_^RW;;R>Xp]QJt\VqhOa'F
%$Q,TV22(N9+X@W^g9p4b9"?E(BYpca\COL@oL$;J.T-cP`>9;l?*1q268P)*<=7LRKjDS+T(]ScA)E3^P&fdQ+0OB<]W0X^f_t>]
%e;FqD-Uea^l2)=V<GkmF>s>SE(<"OQq&-umZ`CD=(@EqeJp01-![2<8dYm]@:D36$5)'I\W1pd_m1scO]pQa,h$L6Y@0W[)f8*,m
%'dJrdqe3+]rT$=OaGWfX23?,r5(^tSnRAIe0/&NW!;hj"#7C%`GFPNB]-ID7?B+$IQ470J`(u=Br+H1^m3coa1SKDkKbT.]Sd7EG
%76lc7'Uh3PKh3`;3Y"\t1[N3qG'`ECU`b4Xc4Ps[hgpHk%gtgj;i6ru491`Y8P0C[W9lSGFJH":_T]L\e5!i,<*"JQW5K$.5,u27
%Z)\`MhJjbV,mgSf:<I=2Vut"<'bH_ndTt"`^5(KsSJ(W--@]0@cW(6BM#I=55R(H8gUo2uO3%!C/gL-,eo8LR1dt(\keF7220N%U
%UQ]*g&h7pR&Q6>KmEg!]-;SN&?bCZF%B`+nWk]+lSVuT4o?PX#Y:M9eroDc/F;>gi0mGUnq`@'T43t5m*"J+85JiKTkgt:KHG<KZ
%'><XcOK2q%j=[07UE\N'9jQO*;iC+eVG4O*9TSa]5iQsIem5])Snk>:[nmK,[E2'i7SmYEZ5@jUqlnGFnDO&N,8A8PZ2>J)oFmmj
%F_`nJYP:&<1PlN7%ip9B>.m"UneUd;@Dj4L\OAaXG&^FFLI,!7E-e>lWfcj2ojb9KaK'A^/\(WHCMAR.Mrk$.HV$2aKY%@7\'Z;m
%I%%f0c4\T"3TbNkX[Y>L\Op/9iaETfEB.*WG9E$7(B.m!R__5D,WX\^U$CC;UE[J*XpKeIAUeu\XTfT+T0)YiYk:(<?-5l>C,&oe
%Ec:=%YP^egn;N%Y?r'A1reF7nUa69$B&8".VK,0CTl.a?iFF*b,`;@L"qWO)Ep&i>/=)Rh^,a*-;@qu>.JWKGWb[4*7u2XYS&mA!
%YJ%eS-.BhDMt%a$J=M1+b4CM14,E>^Mb2bX+aMuh1(W5@HTsaGK?.UbbJuo8T-RWn?>:nXOJ?_;^+*E'&BG)djI?uAP2g!%d;cUu
%/@V"4<Ne=iH&d/m:F<e,I;B5Gpug+krS"_q8nUdOd"DNB_H.gGY,-_t=#E<5<t0.6N>te3"K:*j!A.XCmIc/q)"G-&Zrh2mD)-58
%@?%)#<2mI#I.<r<JL&K!D9"IdL$;KPA\Sol!&q!o=o1R+0KK[;\>M%WIgoJTnZ/<9haa9Zr'J=oNC[KWrAfPM?kH5Q9<AZse@n]5
%%Spf+_)Z+NH,a!ApusQ\)Z/qTO'3P5-G\YE0lTH+D?^GjnN[qa6A^n5f:Eb3UZP\MS3M?&DM53@CSU;`^%L$V!0_Wm+jA#jY81\L
%U[CD^o?Ir\"YFjqg$dZt&onp6R:Jk-%qj:sjJH88&ph0#1`s<*!%;:f&I-B3lb#LQGkMc<FI2ScGRd#b"u+)'DP@jD+loYeVHg_@
%6M%0ABVm.\>k@sM/J4Y'>ko62iI6Kg4Mm1K8oE(qD!lV6>/.hl)a[hE$qp''HF+<]59FOLEKmd>_rsr+(gSZWMHXreAFrc'(Cn[/
%SN2!IXW.`0mP,P%XmsYKRflU\pMXjh"lVW[WhT4YD>)L%S7Vm+JQ)6m&]&t@Q6mD7e6qD'jmo/X*$u/X,$l(h$4s1Y@!dae#?/a&
%.:G$97X'Y__AAN)959#i?nO3(VHR;?#J/4I,%tgkr3fBW.`g9I=\,fkneJkcD=4!jr485f,XcA=m@d+^kbhN<>lpnZUB;_$?0uAU
%giR=.=uFbL\]5!Co%?Su4G#ZM9=V0E?D6(T:jC)dn8U93aZu'X>UK!GJUqF"R.]IRk@&iUBZB,_Q",Z!`pdJRht"#jPEQ(NKIrAA
%11^uQH6r42a84"$V<-,G>`bk>]\*bT*YTb![\JElglD-tBbSE+]u;L`VcS.[kkVNXhqI-uql*"J?(K/8!#ILFi%!K%r":0<hah@.
%432f6Of)gm"GGfYgDMDpf[9PsM:!5.eZ_B]<2kO_r^/c8p@,*L5SHtCb=,i7H=SqlD0HO0Sr"FW.te6?6$TF_,ZhGoaQY6Ka<O%q
%bo6p[d:$JEYbtC(QLer=CpF=<bGc*YOM6Q<&T@Q2Stmd!A*Y>41*KWE#5cAr8:7;u/^,(R0*@#,JLd'TI[*Ud/>7#r'Jcel286IK
%8Mgf]q%f%brJ'Z]LOA2f*^kGYl]L;2"jdEnF,tn6D$:TG._(9lD;2?=!phRd&9>Sp77#V=+''1n$:QHKd_R'm"!fVW6Xs^E%_V)t
%Z`;"le`<_SMVNr2P8CPf9TPhbfqu>Z`@YZe1>SqRA!$ID>oM'jJkg<t]/A^0bG7tjp*eEAX8pR;(^',+&jCql#Fc6(O%N/sc&>Eu
%KX^=mjFphLPg:5rfYh#HMF5RW\D]C=(VWUdL8LK#D^]^'=3cb^b]aWOP?g#hCi,:#:Rc+)7H'fkWJTSdIKB"^^OQ7^MV#A!qQEuF
%pZE9=Up.T4q+(>R.u0FQmY=(ni.K[F6+79,9BuPIDWa;d@PaJX@2>mlKQ:=Rq749p<X#PQ4dn^lm6Dbi*a#*%g1CZoo0`!ANaVPh
%aFug.O;ihV/p/_CCb@sITFiV[1V_!_'RWe'`%/dLcBlfaOKT9A*31O@OOS[p_JMJ1Z=IL`V!:kg$AcR?mZ"?)!U5h:O+MY!4_]&^
%#7@*sgm"k8%P*cIO?0qh)o?W&U"qS9?M",$j-aQ$8%_2&E=R]6:d3bG?i,h7]U9'*iAR?l_/q^s.SmO)7PKKOKjCa+Rqrnor_U?0
%X+@@J\5Gm_\b0c_30NAL/$Be30H\q=Dgt7W*<O.Ih9>6%_m)S-FbR%Q3GMQIOmcc_6+*%QqXLe5*#Bf*6S5')O!+RM\-h><B#q7G
%9%&-3kH)oH7_?#aE9"ND3k8E`#fWDE)TV/%83SS#6L@UJPW>'sN$b1#cW@j@5e\q+F%)cM[+U4)hHfHXm1#erY#HKPLC.dZ^7jmA
%oLtSc%+RHiko`C,'L,j"3%0sBj!5n'<5kk=TqZUZ.q=bMjX#;Q*`=`u`H$-m:\GS[igmB)bkEK<VGtEnWOBLAQSK.R.(tZBe65.Y
%DG:ED-695tZd@h,8TdVniu<Ti!o.SET4$BIi@)0gSRhc%XhDC1QifWh]<*^mbr>r:^*Lm2L#kYXIMSIDjCd-Tr1/[,Lo?"][C`lD
%L?o\r=#[r)H2O$hc8DUTRa8Fjf7#^1h83qVPka4[fg,XZj)cCpWXE#t)G6<;97i:S?CD=D*Y0'i1X./<9PFcR=5eGs-Dll(nTP*P
%\3N7Dp;83sA!;d3Cu3]<ZE!h_OB%60GpLie=%+4m3]5hfB._:Nhf$/2;VCe"kCE7g]/6k$OqTS=>I7uc]grH8m(\k7jS.3Cmt>DY
%$H$7pZtDZ<TghhF?fgi-S5D@=q:5iCdP57^fR/@iRbZuq-2u%Gm20!V@dGL(:hT\LjD6VI\]_SZ0X?o/8SZU)Ks9MaA2VVUG@[Wh
%ar+:4UCtdbeuu.\$@AC/-lr:N_QT2f(L'3a(T,PjU(Gc*Al$t#ej(rHOi04Q5@"#h&h/"\JhjQ/83$%,ThJWF+:mAMh_RU`!Hu+C
%!op)1,t9268O?eo4@2q2K5u1>L6-q+Fb\-^+fR/QhoiP.G;2DhacRo_Ql>KpkV>1+nN*1]iq8oUJ1mUDM"$VsJ.L(#G(h,pfnHfq
%qfN$R;Ro(5Rtbl,(85#4=0+Genk_=;H<h]HKALo#hpngQ_Gi+^X/#[.,:[lI*NI/n)_<n'432[tJMQ*!QgKW7Q)):WBnJW;1Aji)
%ME`2(56ET1-b8Lp4r00F^Rt%e?o30/KqnC-45=L5Vd@WH+Ckf&LFZHnh-chFH0@!m`IZAeC[>ceUF):.QBK]err9>!-9I]-a`ZOB
%jK^_,]^l-*Ug+5)KqM-n)sgB-,a?7LaQ!*+T>AadbRBXY:b=G!>`_KYI;sa\^QT,Ic7ode3l0JQUFCo5'e4qm(sO[kY1gHO7];2;
%eagf=0%$'0[KS@#,UBV]#($d&mKE\9TlF<[Cq)>,Xhot(&2W:OB#mdFNffaPq`4'/$A;\'(,2'/r8VK/[$JCb0\-4qe*PC7MqT:^
%@oe_!p\NEP>cTQqDp/60ZXSAacZ^%8+XU.PC)E!k%i.1Ag?7(EG:)0=7Q:7^.*ap]I'QH]h9\*sF)?d#5X0Gdr%i'U_T<D*X*FQi
%.=I@8bi;@iA+ihR*<HH@*emO"SP_S)`ch$frnKnVrfeqbiQ>$ch8U1fYp`l,g+PVgbfXjNJm!a"pe0l86P!D2`*XC747sQ)l?b`C
%_#MM/1kZW860XaZM(d=9#S%Q_^o2?*N)q9TA1$:%JbTaGJ7]1R7V:_s2'NIm9%kW_T_'qL;jhC)rF5tRebZJKh]OOl)2lQ;pe*b;
%;-1rmGD'8>Scap*20Ct7]s+WJpRec(mYX3U^K`gF&=pAN(p&b!PegIKEi#iQc.WTrKSFl5b:Ff*4L(I\D+"lgPDc3*`u*stL[EKp
%hUu_T_FO\=9k/?,s2lth4,kV(l662P1AK.cC%e2J8\'aEPCEkMe"7SLdl"^=KG\?7J#gtM,'4rdD:O[gHqA.9^S.J<U&lE5#)#l$
%J2bM2!\`r3rgNTcTTO<Ua3S3Kd=W`M?t9DNh'\Bc?7MVTTBq.YikU*7Ap99Rf4Z-O&SaZsZL'1AQj!V+CcdV&N@"6?PK.;<BQ6@D
%eD9sHX)J;:J%s2W4!AKI2&Lb\>+0s,`'6G2XYG@Z^c2#ZH:KYhkq9gBa`!qi'f6p#AHa=:9'GT)90qg8D0`)HP`ia'mB("odip^]
%N67W;@uYp2];A*\%OhObS,=Grc.6-=mQI<7#-18Cn#1*t&nf^e*/2tt28`(0b7ootUo!Qmj-"'&F+.eP:0mIq&$IOZ/+j%CbV6>Q
%9dAH/Y;q1[$",FK'e-s6Eh&Q(Ocl:mB#c/<\E'9/[R]pnLirKg=<<00[1/.mE;^DD5Wn.g@_l!98nk0gNS(A5c9]eR)51(fB&n!F
%0RHcZ0Or?TcfZr'fCQ_gepLN&Y8Os<9J)k-:rrUc3r%!%2H\WaD_dQGpd:Kmm#E7B!Z@hXLt;GUQj^;'Uc/?`KTpW_O2"TCTF>.a
%hSD?>ML!L8PESjQ<ocZplX3R!:BN8];RT`E?q#8C.-Q;n+"i!sn5Artp9*nW`Y$51m3uO`@>q$WHP\(+r6]0p4DpFs_\BUa7^Z%_
%F(Kt)?W`!^l7D;h+.Jg]!X,M6JN/+?hd`&+p8=8`PO$#5P"L5hp)-7-X>KW)c9UIgHA7^&pm#iIbP9nGnHl(VWZ/l'_8GeuYeQ'm
%jN[F[L%kkIR;ZeI%lM.cAD9m7J@0RHZ5sn2JEltl/%O1qJKs,N)"5`NMRe@J\+1u)q*b,^mETa(X`'.^ig8=_#nD5rAhFfMALoS$
%FB:@<[Fn,FV9m:98Ze%=;(s.8S[%3Eo)XdsEuR&L6^LoG^;^Fn'K?N'UJIkf`CXTY8rMtKMpXk'3@?n^o[bc+I?"!OL3cKA#J/s9
%<apih8nAoJ9KMAK"5VWY!2?7<]Nos(#Z@A6@^$>'k9l79mWX8bp.FT%*g8$j>T0Y(0E]R7A,TLRCBhG.]/:@70k>!Y*;3I7oR#J>
%E7GD?U*h!R[b[[k(M\6t:Vs%dS[,/N7]CnUP,IorTBpc_>$"kHTmg9dQS,D!?'VmbjR<LY%.%R^TV/W'J0@\r][Tbj8s5AF8J^[2
%Ye,hnN8U%ROjlE]aDo]5D4aci9thn,CImAAQ'5SiRAr,Cdo^+&;AB%EN*mr?,2\]9/(]G4WK$r#QkE]1#A!-+fZ[32JCuLQN#.DU
%obQk$Xjm:--[2X.`.'Trl1Mij"=V6)B7:!'-H/WO%42%_RBF(_V#A)ch"i2Xm-aXN$I\Xd)V\^$fJOtS_4@T9#-5"B0sqOS0\VNY
%CEdeWV5n5oE2h/Sj7P440_4mBk@n9q\C.lr";+^`@n1q8HQ9t&8$_2[Y+@KNH2tt2&L6ZZdJ?KKQ]=#*QN2qEI;)Z+UCM[WpC$,K
%CuqNKTS&j&pHG<K85>6L9$p9mbfSrK"]?eQiMd(crBgJ=.[Ackbh,MS[$*9OgmV?tOlX,o>uH%6$[YmVP[K'5*3X6K2.aa@.83&4
%2]_M"b#6#Ml+jX7lF$mG9WJD\-c(W0*qG1LDU]ZC)?nG>-ml<S8MjEBNgnIgcR>(E:_@)N[b,2#A8Y`%[j`4)hH-$C%n)8_g:C\I
%'(tWa'52pQ#g43"Q1YD?S;Bu@+[:9GTj.0$2ifZBROtD!Sgno#)JAgB0^`qnZ.3b3K]pq^"C+P4TuDQ6nU\651X\=_HW%SI^,C6d
%Ujn&YldNFrb4g:>bXfkBi8':kmG0f[1f@LH7X"l4cke"-InrlYlubgR$X1b3#^U9U&.a3/:-i[^[f-`q(H$PV*Q(>^Y2CFSM!+Dg
%"<%r'Y/IPKXe[fA=.Zp7X(]1[!/a!"la;j?7Qm+M*]-&(6]<_C<=F42Lk*Q9!&Q70Xsp*&:XEj@STpYCAn67,TW64cd"_8&O]%dZ
%.6V??EQEa:g*U0DK/d"o*EKu,['ejPT6)o",U`sm2PM8F0.a,,)?WVqg'=*nY9ic;@Z"?ui#)h/Y1MoYiXk2"+m+<AGSQ67!beg^
%jFXHH9[\U5apl5dYpD1.EOEV8%oTa0+=X[67N<^H-E2ho2XH&mr!rH'[H<mT*a$s>>eWPuU1Cd"rId5.!<eKp]`HEM2\A1L"6H.9
%'.$]>RASW6`,_@+hWHF419eNH04t<Gk7SA8"bM\3oBOu[^[*1#8[rBV.>+!YU2RQ'.b-Qs<Om.+gT,XNS"d?H\EX?mXr5iB_"J"C
%^O$E"ftJ\#bnMr#]'4A\#%/^I)3*d@r5Jp:0=V/BPCC>71ZuN,*lMFT8,?7/[Y$MU<Kn&hN!]Mmo,qRpX(8>*D>OlA^Hi=(F;^&'
%A/I0*F@pm[XTf,oC1^=o^B^@"jh^Ia"Fn&\Rltt#Y%dumLm\C$f]0*,LnlaCS[6KlRQ.KK+/N*Ipq/YAS!;Gc4[rF-Nt2RG-3mb7
%5nR>CGrltYjnWD)'$O3qS_$RV,#5(+9%^X6/4=uGj`cqPIV^kV@eA:+<:_"^JL:pcC)ijt:)Z\\OkZ>V)pqA9hdJP>!PKINm!k.>
%%SP2ooi>%?!1&V+9.4KdW92Dce[E`kTeNiY3Nf(_;5'pHj3_SrEUOs1T?u[4d>?NBRkmm:?b)ohC#'pPhW<6,!!!Y_U%qrq-hq0@
%R?i`F?O/;]F:I8%7L[(eYNSKb@L4SH>hbte!OP:_&GRJhDJ8+bed.?[ACi@!nu*'^)EmKtri[BMiL>gf)+aK6CF0rA4_iWn!)5XE
%Aj0TQaYefgs3RI$9\L#_D)FPCU-;d!`B_=*1o$fQYH*s$]YQ;<3'c'$lFS%Z7'pIo/f:R%Pb8f_=G0/-"rIE`EG!eSe'f_q=r<l5
%Lh0PhK5Qj^\9\4-BmLi]QI03mE2C[NP?O3`1RVjQ]rTokiotHP(/:&4r"29BO2*HdHf6<b%\s9@R@a.(9iL1`0#afg3JjG#(?GRc
%l^7IZ":]('qXac/no<;c,$e27mlF?$m7'/^"i&S'%\rTY3$m)8K3I6jp4LPVms-%dDp6_EEr<3:V^O/FQA<bb,A$q1>O:8H.3`]q
%_(]8,$na3Nc7r8C8laIdO_(r2N0tWbMDYIZ%k'dMC.QqXF:?LF$D5/FJnBnnnli!.ZR:bOm`6!b%(q7@W.;sa`5T+?gfS6ccp!/:
%5n6Krre+FC]l/6fiYM>$s4UkK$&04Z2oFi)m<[.h0ZmfNU2q^Th+g!I2#)qV#L9GBrM[f:.d(`Rf71Vj)rHd%dQr!PDrJS3"b3=T
%ZUl=^.E-(uF.PY>Cf9R[^4m\1]>RnSXOsPKKU/Vi?S$$:r^WVVZXEr>SA"P+kq]"pEG3CKY9mp=4/2r1ktOuEN<"fC`h=+"iY-il
%7OXJZ>OIF!0N:Oa;ki.JpK^BIdX2f"+cK5nr.4ml14ApnU*cZmr3H<c#btP\UF@(7B;f>r-OA:$!7P];P%D1$:V`$8.FrVbp[mo`
%jM9-8?EcfM_6j/kVJad[cPlAY&-kE[E02=_5;YeX^JOopW(VR&2FNN8db<Z'UtSW01'!CNqRuO4`.MtF&&*o"4UHD81&X_=:]o_H
%Q)V8DDEHCD/Oc`0(:GHYZEZT45d9-L?0u9PWWrM/]8XRI5.YE:<&[SN+kSlkfB?tgQ]7R0)G7\Pr*^UHVe;c$%U^-pW8pI*61kTX
%Q-AH,)j@`.%0us*Vf[A-W*c'i(C$M,<[jOdc]("?UgoI#kgt"s,\mldg5HW&HD8jdN=gu=HVD(/^;_[*U1a+CI.\qLlnHt:;X5N!
%p\Fg-Is_H?2WmLi8Xu.(@hRO!0o9PXP8I3<((Q.#:tKWOL=L]S'0YC9)-Y*H3t4ZIql+2M*<EMAIL>#0,!5aq-*Zq&Q-\
%[f'SA!!W**QB<,7_#L;H(:klr#hDg8*^V[WW5dg7h93O*9Gb5g*B#_3j6H?7Uk;[<R"kY';=X%;id(8\.@r,4FPJRCqdN9C[98Mt
%*g.#ce[s)uT!)7RRSUSe^E%NDL:6$M(q&aC.*)]KXA/i"Y^RnSk`_Cp;NeX*Ac8ZVN=>Rlf9I_rYd\R7LDV7;MtTg)gEf?Tq=SVC
%a>3q*8-)gE#@(2bK%Orpfej!Xaho+/5`Lqll^qNmf4[FF4c(kD&7l(ibei^Fge4]D^#o`]%))0:'Elna,N?#OgdM$+9e/*S[@mj*
%k#Inbgi$Gahd"V^Eo:>]75U:]1j=mV+m\F*+L=&Om!bah/?*nL`00mVmL]f;Yt,)/q"E2OX`l'@/5%d[o[q]A;"hpeh3CohX0?\P
%@Z^o1oFl_ojfJe'P(B%'\R^U2bM,lQ[$DoHobm,7mR7e9-YINiH6nmo]AjV/3C:iiZc3Fa4Gd(s.1S/G($'dHG'J]jZ93*DV9$dk
%HC34nF/?-Q]D?6^3T8Oo(5;)Q4P=[0;jSNu:re+$A>X:@KCj"&c\c8V1lhEM*#r5upn7>7*"_3"Q&%b%.ip5*P$7\:3C"W6>n!D8
%:h9JVf"L>WW1R%[n^n6/=b4qoV-a5%=PJo1I[_@e^ReQ-6-ubb.r5i@K(PSs=8;6:!f;]);K$fa.CgB9)=Ki,_u,uD+^X-'i10dM
%XcYUon[N++2A,i2=,9)[<EMAIL>=6#C>TZi.lOs>K>tY'>e@BT<5KI+.mB_K#l:naCm$g;1mFJJ)oiShXnq?I;si8s'Jjr'ED1XD
%>RVo:OF9U"S.^`g,i^2;RCH':VP9L\n$1+JJJ+FWW3JVQeYMArh0Nm1F,<hlmTq7eTA(hpI&JctMBW&1H)$KlK"Ho,VK[@n]$'8"
%HYi$f)uFJiUpJoe@lN>QIMX4FRCBN/GR.A)60p6/Cq`7M-iWsa(_BB!\.cE.dIQ>]a?b!mX+);N+H<krkd`?jZYi2LNfL9-GF;h(
%-Ph`p)l+6)5'B^d5,.(4VBY*.@*'/^K@Y)u!B9On\TV8g#!KIr]=.>>9FH>b0&<kol3PAMDd4a+qq#(Ck*A;f'F3,0@QTZPe=AZ^
%[0^oCDFeeP*'`!`j,$em\iA-brKp">"a8qd'/l?TSlF7Bk@7O\1du+beb<Gs<(PH\]$&]+&%=sbP)>$<2`[FBK7IrX2$9DBUnVj7
%.jm.ERj1J-M[FZo!a;On%gT5E`FEbAgI%hH#NYal9qb;kAU#,QFlq?ia2&GFG"3E3./*EKC:5)B]8R?5]#=jmFlV&PP`Of0/NJW,
%VPl!M6&i6IBI(2JN)a"rN\IVCN>]JNJ]*bZpD_GZ'"5aYne&J*?hk]u2WTqLnTG%knrJ^_bt_Y:Bp=sE\X_bk0%h;@GoqCo`&Fk2
%(2/)u??rY"%JhL`@B5,,\#q\4Mbt`QPd)+2Qq*-*]&)=M?;,f?S`]'-[RB0e`(:p4>\`@5As-nS3s;Tt7t^N]\TnMQ]Hh,MEIJ=1
%3iAPucP-G`Ta8@/a6<U@>P:p;r-M:h_mo#i",uSSg?S!FKQ1aoEra$kFY%4"s7WPhhfa>i;nYndg9fL2SqaBte&sV!FV[\,!Ra^#
%AIShh_WmZr7_X;<X6;%S5<S;!3DNA\dIq2Q$Y#tf;G_cdGe5F;L?L'2=OEfX&WV9J\^:7u&DeD[ccp>4^)ccF3j'dH7ZV"<$Me.j
%NfYZ9(/GW6=NHV4aA.;dDX&;jd>+(pER1&o#r08BH[E4PFB!gKhVK"CIk!/.BVr4u96]CN,7OI`-kpMEj<5R^'p0t!6IhVYMI>3A
%o.@6o7;t1Xp'WPD_%m4lVhu5]]u194l\DN]W-C7;T/D,"r-tn^/X3lK6-2P;2)]\S_Nf3e\>Ogm!P"koJHqPt4mObJPMO3XEoHR2
%2q^.*6,N37TB)Fc_be\cdsf4"8luO)/WB,LKp*u_/rVAd!b"$taH1iEe2PqDoNH"Gi7GD%;*as2Eb`m[WC\?jk&H^^?rdEGK]gs9
%5$RZtRgL%6UXu"S#e>-%BXQkYnZAgS(1#+<o!9QSc%"%e&&FO3Ns"o(964sd]j3<)D`c&7&h&V8`6u%#oO?@hcJ+ML@9--WXRS7b
%Uom%,$TP0=kWMn4!6ONo/3"d^LW0En=Q??a(`D$07Xi?7K/+./05HE^LJ\oqXT0SAX$$nRGkPF/S9bga<=r3M+W(<g@kdKd;i+;H
%Q#]T#r$/Kh",X4<0hKW%J2:29I5(A\!omZ519!+U31Z&e/J:r%K\Z=9O78&pqg\X]#A_*?L+E>9*.bsZW>Z%l_oft@Wb/`6d25-5
%H4@PUNiZeV9NbiG1"5i=+s&U9I7/7`B^8"e<cS.!+b[gD^GH.M6(s-uW05=l[T'ktn./NGO\&b=WkI/&Y3baWer<l6]2305&iTD-
%j%peF!u=Rc*SK51!)u:O"%HCjGj!F!+GNr7L$iM:!XkI7l<57%]=j3tjhc%>-<3P.d)4MSB1q:+7qh#rTj=<"(bMJ8bjR:"=4>\N
%bi9"7h5MT-QME03[2PqUTtnNi_PdqT^0%b1_X>F1Oc86H2,gJUIB&o)3p\\pTUj!LZM@I,GK"J(ikoQ^'A10f!9GsKAbsR\2Y&AI
%Hs3r<BHD97j"$Gp:*e"5SqhGOO_RK,p0Y0<I(R1=P3F$gVT/i6H4h7*Wh4JnEHkn@MEu:TO2`SVC08\mPG$i".gJ=75mZ>*o7T[F
%Pa>^l:Ga&$POr9'iWpgYZ^jc\7-a1*6hRRJ*QVVm>QEs=FLheaJ0<o4PlsOM"GP>.EC="hMs;9CR)__)M`Mc2.1@@\_g).?[7AOT
%VN2ID>JA)_?+i!-b*'YDnOIa&aJR85hk"<WgF",jcLs[<bg.1G.)>A/H6#<F'ri:Z1ZN%h_;(\5'`B0ND4UblJEe=d^st5e&RPJ?
%oFu:E2m_M0f7tj+ZbDT5%TQkQa>L<oJP8;K8AF?f"I:-\TEjl(Ic:!W+<e4^PTP_4^6Ze_l<W?3E:JqUcj3<C#oKr\PZC@A@kYSu
%`d/S[FY>%"1O'%Y'^6`)iOHf#Gfi5+T3EV#6Z%[4Fm!rKq0R5AqplD986!ba6&cJ\l*RLlSFAs?S)D?P>X1]l75A"YoJ[recSQL*
%FuFlkEK:,c5V/D(-ULMM'jY'L,nNIo#mN-TOi6LL^qCg`l/&#j)IH6F*M[:6IuTdQJfQ([_%C*TF3.o#R@:^O3mlENa!=/O8UrBA
%dLr7U\?R:T=HG*K7N+alQLcEH;#C(5<cGG+lO&ik&]bR'WMjIE1M?Ip%f(6WL6c'>1!`N%ftO50B5F:2A7TG3b@d)FCKHtZCed_#
%PBtG7as1YmBQ:"$`Lmti(1D(^\JKps->^Fc5or:iITs/sYW#k9:US23,gW7d6nJRA)S1cM$SD\Zg>\ick,s\PO1H:_3cJ^eI3VUo
%F@`K0&_j,JTeF4*OP;O*.%?)4/"+,`g3S#k<@'eIe9))r!\J6e-"n^4gRnFg-mF"kBr2s)T.'[$YW%-h:itH@N%#&<*hqd^_OnH?
%F>6M$j6!6NUCPZuFVbpD&]1LG_f)'is7El$BC(Vi+*Y[gIKP:P$E-[AO&8eOeNq.H0#p4N)ncB8%7j[i:;AlhPjfN.n9e,J9WRou
%MjG')O1@h8p+]Us@6_c2^^.qqq5+P!%<imXG/.QU*W01/@a95$b`0;kj:h$OU^qb1f8A9<CdY4[)Lu(<9H)mr,>[C"O:`7r4,sHB
%)*PulAkQa3?pm5j7R6/2d`>@WGE32!@G(A0a*:0(iBP:JPm4bAN>Slm[f/!fldETtA.Wp:,_KbU/;,6)C*V0^<C;4O#a:NiHThK;
%nfB$c*>hS'9>4ZO#`iW9X\e]8ak6mYQ@(f-LLp5#\D.qEl!<%@b%&2$\0Z?ZH;pI\Da[$\=E.)(@o,.Z.s\XTN\7PP7BPj)K+W.i
%g(=eu@2q`F;]L*F:%R#8OO;iIXRa.4?WM#8fA0Rd\o>9Y\=l(&ZabD$Tt'8uGiiXTHO:-HZi68['1;$8,G8')3+s)8^YWFQK*jgE
%[c0%s#Wk?qnG,+8cXUl3%8W%)L(kX`mXn<S:@u'aW$C[1E*sJhD@?ArR%:!4d9TL.ouFR^efG]sFS8[/[<:^X-/F=<SQgME_?75Y
%&h9aka,NTV7nm!(@(P1q"acA)0T=P\Ee#/N'0#:jH=0`=Ujk7cE4r=a[G5#h&R)Q?-,7E/a$jdR/-5<fKMY'i/2h\u*gUf0Z7C(9
%;?0NhXk\Njl&Cg0f;`7r9-<,L$aY2&a.?OlDhPrt8d<,A6lJ_YZU/.C:/RA"cmea-.>\(8XK!:@j@K)\Mj?m3;4d+ef#.psS?mMB
%gjWaYeJr-@kAe85QBK<WVA$9WJP'6CCRgZ/4[C1-6.+_BoN5JKFeQ72_f!1lSM?06DS3^KZuM&P]tX2\DOkSe*WU2b7(qMJm.fWC
%lS,=_Yp56-''$limG:js*KJGG!QmC7P1W'm2B.d=q&$o9%*6WU!$O:qnjUqO7jF(;IbZ^Da#dBAm9&>37]nupERoF9FrtBPDtdA\
%m`I8G=-P2_mQ93e0HRnDF//Hj+I;/>?U_Up]:+.ZCmmo9X6aEX<2glEaCG7Ap1:5<UTED"@2ETKhr2T,'aYnq=GsB+Ek]9U.3DpK
%fg)<'D<j(X="b*hkem#hYsOQA=[%WLM;P^-<6pr/jRZ,Pf>^j3R=c)m<3JHn95&bqB@gl7&p_kJW#uQbTUTQtNWWQ7e#f`Yqtl%M
%7ICg96W=&crSu*S;-B'03HlR1dJ13sERIlfh37dH/I+NO>coc_L#&KpQ&$XO^"lSf(>eBumoK/l[?+PHR^;md(a":q_A:L&h6D>3
%&ng9$YSciUb\10MTP&i=hMfS<QK9bKaQNXsFN!-Oge0"Q=SJ;G`_?RGrK8ac'Kc'[,:)]>FJ`'P6@u2Y"hJ>O)Vg,'&#6b)$@WN^
%/WRP_eRG<i?n.CGSKH>q>e\Q6$_OoRg.L]q0@BF:0dfk+++`1n:+b!tH4/;D/ho:YUR$sa)QtgQcO_lCT-2=R>=R5RjG,i^.aQbN
%4E3#./nWb5Sq%:'1$3a_!p6]<n-ZOgTaFW!j5"g.n-WECitjciNRmJV]1ibZqdqFU/>op5]Ia0X\93#l:toIB=O)QsY`BA])P04;
%ko:S!?nSs"KAhP04J''R4pia[aI5<K6Um\oY$2`.D]D%*^+Wp))rr'/I-T0I-Q(&J!,9W=$X+!aUHLcN'hSBDKcs3EPPgKn0B-($
%'1sl43pJU$=_pqc66Z'L=3#G,G/duBX4NnEg<aqBF#%A9:b#GD[\=&;>+?[J1_hU(JQ*'jcBT]iR#6?EC^s=\D:bK#>soMpf6GJp
%Wa/(K4l>3)9iqOopcBc9@4[RPPC!(k-Wf)8+SGFg@RlXq&.g*Q3ZTZf9TM9X=R&Gh%E?aPKlKN/R*jP"0+?N8ATYho9dkB+:PO*l
%Aq=g<fn=US;KH%EE&lH[E=*TjQ@Q6==H-s)6HijfnESV49LuQ=qAgb=APC98Kd(@eF-=@c0g0m$$9.$_-+<o7/6.PWOf`6lV)XQu
%CG-Gki4r;S:^Fg>r3`E=+o2r\+T4d6s3l0doQ-Te06"!t^hNTRO@B8FDa2,EF6Us!p:LJGn'V7P2d[CpXBh81PXg,ADe1n9";h!C
%CIc[G`s?<\3'CM,_\pA4IiE9s@8a3[:!6A,Nn5?@-R;onpr[lO'%j,pTZUB'GrJj5LCr-6e+kp`O1Iro;F\p^EQf\e8NqYT*M$uP
%f')LU)fc_'%,.PQ6BE=P;qi'H2XPVRYi<.G1AM&P0iDIM[1)g?OYD8[1N3nZVa:q?FqW^Unqc_:9$:2uNX=$0=8[D73m/8IX^"GO
%=I:5_\4E*tg`Fmu?tKB3cOE(VJgC%.-?3u<W[?Lg?i2X]8:V@JOmdhT6QlWPW7L['q\4d1];n3:+fOiBe+c!k&,mYNMY6/8d)K?4
%\;X+E&qLXbhKiSP,RSrm8Kb`f.(sQLg\&Njfg70g73\^oWm$(fOcL_cg6Sj&PsohNC9`*)Nm=M=TA^K1=%r/.duUIm:V\e7[q\F=
%65KB`]$Qk.oE.`VC5^>5SZ>UG:KJlipsXB$q0Q*"huTojL\Q']&+c;pT_"&qb9+q+$FDaSjoOAaC*\e&?!-Ig7tB[82/;b6oH#6M
%o#0)q/;5g$@3HhoXCO/Com0moM?E?H8h?iNcLE-:ro6eOm%O8(VV[:,PebW#9IYQ!:[4k6@OmmhSI$.`M+T6"RU`:Vo!+Y>n:659
%o)<,];(80ZL,TErB1>=n\`-;51fOp3\JjJ"O&)XPXiVGL7L2YLN>>/1\J]'>'+T=Ck+;Q,iB4-l/fX1'ZFJ*9`$57kX2S1GH@1ZU
%E%(iKR!k3]q,=T^6nsg96TWSHhXUYcFV!2s]sKaK7`[9IN?`WmjV)KLe_cnXdur$pZ_pY%g^>=3%_QMB<-h\fbWP5-Q,8LH(8#"i
%X[;&N0uANKI'oM]IXF!Z.r\VaeB"S[GW`mf!ObG9=AuDGj_oDu_+9_N)=-eX(6a-f>Vtl71;_B-A:.;fLp4V=+02j_7TFDA'V3/g
%]jf9*=]2WH'f0?^p+nhqD>KkH8V*r?^X=]dqbtIYJB$MGF$VPfK8$4V&_FA[Altn'j1lsXP0Rh)-5ijNgfE5$Uq_'%.KHW6cW+_0
%K:H=,:3a1gCcTXE#bChn#KWq*^&3$]*$Y1t]`50<_TL6=K7!fKjG9HjH$^/den#UK!"U3)*,r'*boe;fM@kb??@%,;DH&D8Y7<i2
%D!4&^dL.ELOV(eQ13r5]!\`iC2C#gB,K6?u&DnG)2F5aUGcD?$^qP1hZ(McUGZ3Q1-2F>5&8<cu;GjU-ZbE@2OW.:l0sfoiDE:+G
%8!s8ap6U#3]f4QC_cD4JI7E4p%--ho2"KYjqCdo6K\=kdCKbql:qLW6*X)koY#Ep%.^<:e7G!O1>a2S3PthK`5Jq3L$8*Vd7J#!J
%HX>5YgegF)+"bKPU4J*%<o2;HqjOVZ4ntXdYghLY`Le>!HV!8Pm+Bakm[(H7Ee#;Q$EfuVii`q@-Yed8U\;"qmMZrWR-BqDr)l[;
%@U%)Hh2)E0R"a3>BJ5G#hlflMMUIBC[]W<"!-;(V&>A:;koRtpjA\_\8W!&tj]mH[LO.10-TCUR689hVDU7sG38ianZgEp)2Z']!
%W$tn<q;d=O-+H5/N->8FEU^JgR.&SYqHO#9W^7`e_[HpagmH=jlA/.%N5dM-#p?p;+QNb(%(^<^o6cM7de:T]7;91!.:lj50RPL2
%*]Y>;HDK5'E(!M3K-4bm*F8afGDo7@2I1BSB$B`Q,`P1(aP8(i4mdYJk6jBG%uaN-/Clo:CKsX7O(!K/fp1!T6/SSK:NtlE(Cse8
%K96k3b9dJh<V6*X.na01-Z(GH2Y&8f7W%%nee5C:K%rh2%dk`;p)%SqgahAG;+3+]qufMaD#t60[:u&jjsl`M\5h7*+Y9mqcg&Ru
%\M^Lto;o,.1P;I/ESZuCBNGIu3.K)W<Yr/H\Y57p/kh`j2VUc+.k)UL8>Zm+L=-@K%;LEg&/;Pd?X\_eb-StsU9`L#We`hYOdfiS
%I,aL`#DolgZ,FtjJ/elS<QoTX)m?QV9P!'pTAoAsXtjHqHXp>+01aO4oA:YM9lt#ah6PtcpO,`@io7QJd\tOh;9eH(#Used6tC<R
%n-u=%Eh43ZK\9"*Q?#;a)[[I7lig6j)POu)l`M84@fVA+*Y`t&Pc5YLPMPIQYgrf,)Hon\83%=L&:ukjj.eXR"Xo69RWS*_^_/>)
%Q$h1jJDm^0#_[7@Q*s=rB#ah["r[lF4-6_c^U$O@,VjD@8'`l-oI>KjmDU<COO8g;4M&<2n/hc8CdW:O.L$;1Y#j'MjT&-LgNEl?
%d^]]2pgl199#g%Q2m4(1Pd5rmAN0/LWJjN,J10]_3'^aoA9%i5iQQ_U^b1d50]!n<mCR)KW:h`p^c5A)Ug3EqVkaoCc#L#7PTFS4
%rT1]t,MU4GV!=79/XZTVOIu^bkQPV:fRc!kWSjHO5<YOS(BZNmJ:&8cI;Fld!T7!m#RM)#c]\u;b7U-UA]XqK=Z8j<##Nf!*B2UN
%/%p,8,j=_[Qa%6>aU`RLa$-91VaXAf;t5gLkq01($6u9hH4Ol-phtW&-a5>m1qt)jW2A6(F+&JfJEaHc5Oh0RXPd[(0&05,Y!8:,
%08j_B-,HT+2DrZfUaT3W7:8BX<1EYo(eFFgS%]!AoMRkBO<clQDHBRC7KX;1qQ<]U@kgG]D^un_Ta"/qc!A'e^3MZriRq'T)7Q)`
%09_Z'V#lSfU>@b?A#ZTRe99QE,`!o*`gM0o$p3.LDj@e&?8K&>)jTW&lmOp8O3lm6k1QVI'#g!9%K,"/p;lb6\p&BYWbDH:_4tRI
%B8bQ*]D]qdlc*2QO@P<eGH!pHmmstqNtA=mpum9ZJ8oT^MtJo<PfO2GLeeN=RP9Bb4]hAb;r8uJnot;G12e'!B5Pf\@_gj3:8U<(
%R&+bW,U35Jbooo!XaM0?HTErca-s^P>0A%'qqd]+P$+2@]C-cFBD"?FVFoKN4C1]3O\T`A)9U7C5OQo^68`LBJ!6NaQfjH[N9@BK
%BdDUWF$k,.@gq%[Mj0#<Um?WT/!%p7ZKkJ1QfL5dJjeQ8L4E<qr?JUBm:_/N4-E,\<.K5QRaE+UK.SoBPR`&C@9cDrn>&sZ&-[jG
%o,U@ZZ"8-U(3p%FY87b\Ws6h?iU&0/$%(-"0U<4hQQq"X"SlA[C)hSZ8B5&7QKX3TeHfh)Cbee`%Ua>%^/-602Rba`r3(O_s*Qln
%[7o&k^K&3HJHVGpa@9SDgR]fRs*iPhB\,r]#?S;8(se-Q/sT**Z&f=9&WFlj7i^(K*%6s#*fmaOl_*-W1k/dXI5O/5%FqjD&fHR3
%OXNm1PRM`jZe[-ZWl&2`H`!,B&`4X<(l`l?M-]FBGbP8%]"GMT\h!u+h=!f\(to+dq@m'/C)&8#k/Ts#4:d,5ci.l,`##:<rra1i
%hK.qD$Kb)rfF7c=P9.Y5^cs9'"U>*+fr7_j2M2EHoNnp88<.(cB.]5RC@#e8-E!;PKob0M^G^p=jh8K6T[#X2)[MR+\KV3><\Np$
%jZ3m8R<`^M!W%nhC-k1-K(==tg+hjH&O!Epk?SKu>^io#j2?/:=-Yb1Ca-7%M*FXB>IpVZIc5GO?=+qf9XLKp8+g<d;-r6fV!dtD
%EPts:@V<VGL]\StL6*;WeX6*Z3IsMF/kMtC@6>iR:'r^iIBdN8]&Ge9iX26P,iob_E(!21CnlqKZrQ;LNLl^eQJsZ,o/%4"/!6'J
%0_0h$q_2P)\fd=O1RYPp&t\[pSja@9gC"Ti+,t5(13AKbDW0o?FI!t4rR`,-,J:P#A'KL>7lY>P`1_3AKJgDZ3P"K?O\oq>bg?*L
%,:lbAdcGOd`ZMB%GO-;-g1B@d^ZYJ&,XYY-EX<^Ij"jR?KU`X%h--_EQ,_%W(ag2fYhF!M^5Oo,+CF$In>9-cjh>q'qG[$3BKpOo
%;:lZ1QCZGWo]TS1`.(4b&@!t_L/J@OCL7oC9\is]lFMMN^YZjUfXl<L$Hj#Drp^\*Q,o9qPR9YagGN&MkZ1um7F11gJor:[b;)0U
%>AIOU&ldOXC5`ZNDNj(+'5!k,&n-8/$!-)fYXDVfhqOuH:F,'2)fcVf&qE!l@b`bD"9+/WpB-m`:F99adl$.\Y)2BIdXN;oJkHo<
%`\W$MN1LmL@_-ifgCsTLr(XWO0#u#WS5<9,q<nDV":>F54X*A)K2.Xr$Z%D/249(DUaZCVGE*.@&'!aAl2YhJN4@*.T(,scD'N0D
%fm;M&[HshjZ;3Vl?:Rt@#gl7n75jBg_g)(LrJ:-USUG?=.Jf;G+?\b!+%L\V2-,s%#/`^!7]SXK&32/1TR^4qHa.;pZ.Yfuq1PA=
%M?V.aOVHaLr!&]H&QO,mZC!2,6J9Ya(Gg9XV&_JR/+'_r'QZLoC$*Ld'MjU.rjEnAro1C[=6=Rk7pG=?U8;hW4l6]?2DsJ:RlA:0
%#(3)p!`FMGOJf$^O5$?JaurkSXW8b6OAKJ'PJ2Xq#6i(FVsSE.4i.\X;&'4WUh5M<lAT`tZ1;BUdF#[`E;1=P*JWP\`!hbuM0W)k
%(!BTs-"25L':p.fBV]XG_Bkk]#OaGoJutfSbc@C9Z7(g%`a)/OI*kT2D'sj87YdLaCc=(4_l+7pOMs"H;eRh8(M0..`[qT55buh?
%?oiYEQ'g&I?iPs6p\18g-TXj25R+)Oni:<XJT'$!o7`.2]-$@`oj`m)Q3'WF>]Q:9BdI4<_^RAeP#($BOeOX7gR:tnhbto37Nh[R
%g+peEr>:U-AXU"l7r6uE2;H*m1T?\N^6H4(^?&c$[(naG[ks@3Q"qi$^8:WC=:'$8TE]'5\(P3>=)c&/r>;['KUe8Mar^rKh)IY,
%AY*$OTQ&m$g.Y!_mh=cYO)^W(nSIk_\b%SM,6MW'`WUQaWKtP6YtieiKCQN,EtK][0]B;,Of=YLUW-hs`1Di]k;ece'3;SKD.fDI
%d%_hL,riUSe(YUU`LuMi9H+$jE2[7I-=t>VAF4J=WBfKo&pt&XfVHK'X;-cc.61hb2*X/YDDN%W\*8RH4=Cn.)L:pF$`m$J>LJ9=
%>OtGTOrS0=gEJB9"?AdbU15mWa;9->N?':kC\7I0LEuXAD5;O\&j$a4NBNpoB:)O%0/-16kJ[BeHP,#r(T&2hgQ9Jk?hI6Z_W(JG
%#Q@`5'Lhktil>Brn^h`1:3pd[WHf8'o*P,SJKE@(XpGNIVS8uP9JY^#R;DVXFf1<d)SBr7L=!NBfdU*3r&a1%70)WcfGsRUOT,m>
%hTS6AA8Q-J"mY9N%;8hN7<%3<B4'>_I0=U5M9(4f-)M^"ieuR(FtgiW<"^[6r=PB!:8mL&FNo-=FPe3bilBRN"d5=IH+@=#dCPrp
%7"cYrkkX6Xl"8gjf,`c>A#Irl\?=Frc?uWFG)9<f1!(+Ab=.WAarId%+7NkeRsQS'Xj9og==O!K(fJ>ul)U<fl;2;K7i).MW#VKc
%(hVAQ=m5\EaDahk^V!US\f?0<6t!?LoQ)dgri?\eo\OPM2(7I^V%/FC-84pncaP?hhB=?ERM]RC]Pb?uW6a2VB=pUef`]js.r,:3
%X*+iCK2%18`3nqF6Z57RK]7-mqka(tA$q[KF;@lWHDJ_*,1SqAo(c0edS3Fo[R6R=X1GSEj8:$p=E@n]f;FR^r1g_G!#RIuG>tg8
%lt>"4OIigP5(H+$D'Dg-U?kjb3`eeFr0Zj:r--\h496uglLr-"lqE^BQ1IWu.hfeIY#\',>[pC/.p$fa-0ul9_4nI\cB6eAoY@C"
%a3T$Yr*u#;Bp?)%@pLfUp*8MmXJ9+qKW+IB:XXP%+].iO#]]BU,s%5'\sm>sD+:,-)C1Hb&H>KUkW135@\MGTQBd->YcQVIaV3UB
%fn5pQ9<^DOFH^lN/,R>DYAT'12UEWPPC(WY,b1jfEX3D6W51[gM:b95&`HLV`Wc!OXcHdhJZCF!ZhsEH7[@tae@2tYmPs`pQ_=--
%S%IqMk?66LFUUH'DODMnDp5lcZVt<kDn"/<XS:2<<jo2lULU/1)L^ln=d'<*j16DkU^o'&ZSd0^;a/f'PY1$R_//p\dsKBNj5XGd
%T4DV5-d)aI_%:.C7^@X,=.i(,'7>$KJ-?SO3Ep;67H6Jtpa$$a]V;mH:193ddr*_AEZeUGqIN?Ff?7pTMi;=^^)TBjYo&-GKH?L2
%!K=n,ch^i:C15l*55Hs=%<#`fK[<]:._gq/8/7X34n/i3&aLh`dJ:WX5BrU-pq$c]cZC`j5T#AKkYBWZJ6!q5]]6tW%d2lraI\4)
%qm&n`#q0o.Z][QW&HN@\1CI28A7XY\1+YT,?ts:+0U9k[_V$-k6h=Wfgr3`R!j3H(B#.alD]Wgt[DgK%p"b9_Qa*0r^$546hHCiD
%];OTNP4V7di]U2N@Z[2DFE'X$KdQAKLQJ5cN2Kf,-Q4<\MoRiI.q'*1H$TDi7Ih'DOJP@V-'1lfGKPNmp0X$#4e;#<#Q\>""T/6t
%A5@TT5a,E9"+d+"88Cha;B]PnFp%Adp0t=BWh1_3dKr%Tg=U/OKC#F:Lo[IF*Od$K'%nNYn1:5eUt"F1^c!)J9=WBJ,"><H8In!0
%Nt2Q(&Sc"!G5,d'U)"dV9T)ce/crL2_B-_)F1LON7I)W]>CnpN-:bPo/(@R>b%""796a)1FV0cL0do5X<\o2A`KBQt17ZBkK'sLo
%F*g<jb\;#!PQ'C%YcVa\@E6aE-g%adqbZqQj[W/Kipm]Um9R!9%fuPnI0S:!-8&<j=^:8q&[/R+HntXG(6?rnV2:^O&D3IerIo,s
%[8>.Hog&J/[=egK\L!nm=B^_M@`ooVJbCjfh$M(_6T45<0J6mb^IFMV).D0s'l'LieDP>=2#\sHo$_fSi;q<Q4?cbU2p1W?RsHi2
%(,r*cL!E/c93EMHQ/%H=KP^"#`>WL@^5lKY,\Sm]fnF!PE=1KF.QJT'.QkRkVpRd2D!AEJ=HlQI1pV^1g^lQKoB>allrtutM&;g^
%cVB"gb/Zi+8aM%)+;ddme45I!J3[]Q\0_N1?j)O<(f.%O>8W*8?/RAIDPNk1k?dn9X1>?3![+Y<[V`ib&Yr$h]-TZdlo2FCOWW#>
%?DsR>U=8@(Laqlt=!ZLeMUDcIL"Cj7=0$kljNc_FQO:K"Asnnn1>Y10m=b[.g3>T+ISdq^q_,4fKJkJ>o#pMTb*uamkAt1k,%ja7
%MGI!FcsHu&O>HfUEXqoH7Uj"*Y\.uOg$Iu.C#EL1ch^P*II#t,@uafW[l;9YLfUFrX,-L_eTHNAG[#[UE<,"4W])3lE0F)*a7X:A
%8OsVb.&JcU`*X][Oc1;sib0RT[)@0>(*-82iE0+Ns%jgE++\8tnI>QE;Gi!<\/>#H]T0.7r%kkaLi)@t*2DbHiu\Ytd&tZjf")V9
%s*[]R&_o/6L\<h:r8E4Jha4C)7+GP_3j';TR`K94CpUuoOWN=rcZDMemN(Ip$(BT])/DGt'FFJ-W_BL)$pu(r6ub1VLbb3aB9s<g
%s-[gm2u?Br<U'?;l#nb3\:[Y9,`p5,mS()Deas]fWgr>L3=Hs%(ijtK:$NK]NS7`A9N-P"l?=De@'g33$o3.RebmDoX5l"ZH30@h
%'(Qlmms5n\0/8=T)1=6a%7j(&,MiZ=PAC[ie0sAj/m;16B7i_O[%Fk>ZCNjME0!lk>,CPkrn(=uj*(V<:jSQS?Gk/t>hPs6)HS__
%a>5FPf]dGRK*,o3j_n4(N)IepD`k^:8S5-+B$e#988VuHg>%?haAYEJT[k'B[H/2jrq:BrN9_\@A[g%n>Q_mXa*<(*$9R6Rk8L8Q
%m77AV2=C*8/JtUG:,7'ir_V#Cn&U)%p)Zk1):;$3lB2G66prC8F^.Ri$94bi>(B@T]l,lA3`99b<;*W&E**n!<lB[.HfG<?-L+>'
%Z:''nTjld&E0)#NLPBU2i!"@c+kZ'Mo.lpS]o0N,c-q9.*4[B6(a>]<O`&`[S2u3VCWeOimF4)o3UHXcb1k"tJu(nrEc7)uA^%'(
%@C+r$mJZQSA:H^drHO1oS&=+Ef3NEmBk1N@CK0SuXJI:&ekoUo'ul>N<K_W\O_;J]GR%RFr"-tnF];<#(Saft59o<UTD*t6"TeUD
%1N4[ses/sd?d+phIQNWpSDRX;<le7?L65^I;E2(HF4ggp9\'B%C:?`JpSW3RF@R<D,H-R:J'U$94hSoThJea`<Bo-[.Qp1)1=F5U
%GnYN;YYSN7_G9^A!?=\f4L87[*EOGW9Y7b`41A#8mMSAB[RU_b98kfEOOBfUeF^EP`eB.X`DhDDW[0cpOK!K6#pgm?=Pt7EmK#60
%;I;$6D+2'-6>CRkS@V/R#cuXiii4<);\@SW>/)Y1>tb35^5"<O!5r,JG:*ssqWCgD1DG"%NZ4dL/\us0\kf4`G$n(sM8W!Y&I)>a
%$P:IbI"LnPGN_M:U8[OnH&@b']":@f".3<=qjXUtSDFi]>eCX0:K+H2fdQoJ$J3l`a'R=CX9C_4i`=ti%&Xr@#Gi`QYDi@2pk+j!
%17T%.Dl+qg.F&'G+_R1h?5+?,8J8jC7sR3H]=L32(ml=1V^;m]HB?BTR'I5J4h2sei];O1>nMa,9<2PkncUW)Hj0p^j^lUD*#[>k
%ZgF&CR9dA:ljjaJ5,d/HY?EM?P=Lt7Hq>@3h/[[Z-&XiXNat8^V;JF;pl48+:$]"2_c-^R%Y^9VGb^FPU+nC:5hdcd$i<oZ(jrf/
%o$[ncljq9&4&&E8Xr='-`mr:&HT+Rki(!k"V5ahBkqr4B]>%P+J=DJ5LO3quO`#]:Bt-FPYSJrZ8Fn-U+2BiOWpVNm%PoaN?u$rQ
%=3D]]BiO`IDHjQ\<eVgD)@2D/=O@7+9lI[&J])gr/=)CKa7C*Feu9Y@]UoR:("ZG[Dh.K1kmHpH.V8J>Zad(&!]iL>YlRA!"s/kt
%Rb,ER#6HIgm#<ud$G%[>Qj)tJ8@S^fC:/NJVDL2iaOLs'V&lUp9t-dj9`Bi[s"R9b_3e%A``*tL?-Sk[-Ce2[BU*]Qs,[YEZ#imq
%*qFX"\=tYVQl"*MFq$;O&dRk**a,c0e=]=O?#B:/>Y_U/%L#REXVoj?]21jH=L'gea%.sjAgrf]]IA)R6MD>s^,AQQmD'P[AjW=t
%fRjk?XCEXNY0g<VMjlq0+tt_)<EMAIL>=K4OA!8J$XMEaa+E*jI1RUPcWd-S=HJ.Z=7q5gQQ\J5Q.@Ae4K^_lB`h)Kbu".%]P
%<$cZL5`HTf>t7u-V^NO!O-FDC[1ErP.c?Z':;01AMqZ;lGtWI+(:+([W'6&VV7ceT8\Fr<?iATkqpQ)UfDk]ns7O/)ric@l:Oi7+
%s7,:8?i9GDrVdo<TE"e,s2Uc2k.gln+91N]rquZnrqeqXrX]%tn,NC]s5Ulml)-cFj59gWs1\F#=9%BCJ,K+L28ET!l#2%Iq2bRF
%O+71V&+[BNhuC*VrU[ei%tF90J,e+bq_+Z>ndEWjqX)%8^\eo3](tOh63$%2BK18:q0t,Jq(F)m`o$cRs7@soOoP?'?iT8LJ,^&Z
%#I:?/1!0I0-M\Tpm7"b1WWS)h"07W\!ZKZ(ldjM&(3S00l3903:^"k<i^ZLr1e3Pn4_?7,"K-QAcZ*hsFd,a^g&"T#hGH@!_$I4H
%?PsU?_"D*Nlq*2rFL=j<`jihf9:GY7['ZZON[9:k0GZMZm4U9,!3W7iFO>go&k@'-mU<t'6MbdA$Q9&%;(D;Oa1KHG7!T!c5QVC?
%A8KO\_B69#lNAt)0hR-JBgG`!lVIVCi[Vp/PbAFC(_\!0RJp"6-/l3fdK[a:1SQqL$W<5\,&4d3\0LS/o`J`(8-COA<a?/(1dD:>
%Ok6rqd)/Gaq`u8.mnUBBen=;H)7!HJ'>]4gAu&[rT^-WI2]^"r&Z.T+@GCkq5ma>5l,jcb&A^W1o02jpU0`SD,+0U'S08W146kc,
%Bd5.R^]q/V@RN\Wm'6n**QV;?>;85o],tk@%,7+X-cq!sM<`>?0R\`6KjC3;'S]V[)CT4M+`Eom@I&L!;8A,&=u$Hd]-Do1P"RY1
%)!QqFk@H0HN(ul@>K*>Q[&ZebBkk&e9.V@ZL2&r*NSB":QM&l-!;-.#B(ZlqQYO@5f)%Gs$G.eTfS!K&#k2T;hj5CR^C7L[Ur4j6
%2AAHu9I-n;7+VK_.gQ45[s*>X&hM&P\%XprjYabRY]:L+AWMe7ht+@qr&Hm:]%Ge7`S)m9Zco:m#3ApVIVg5NaYbR-"DSYn'm1''
%OZNWIAePd[94-X<_9+k:(:n53M#+8(^k7K#&9>ctbBW1I\eMU.+1s+!34%YA#"M<d7Y<jMg6$%_U\5iT1\/+;#-*ud]MKlb@LE8!
%.!KEIA%4julEb*D>PBS$'N,6^h\&Kgg'`1K!8@'5e(iF!:D_](=X(QK#fQrGK]G,R",WM?KY[$^]SlpL'[5_H04Gjb$UZe:Zsjr;
%VVS+9SF1tUZ)dINOh<YERmZArqId<HUcL-I'oI(k3[+U)JE\MEL]!X\Z#HBW+V+2WT,<=uZ`)I5M0lPc=608`\`bc7VJ]SZKAlsh
%lGarH]L'p?"ttDKW(tq2fYr0SJAk_D]S>8[HJD7$REFB5fUjVh3UH#U!l/oCjm\&558frJa,#QX9B6(12:#3$"aI4t@fXEq3].Vd
%l)FUVU65+S3NB"WH[#DfK"pJ-Hu,aG8c)muT1;('FcV"p%F\QKhOm%]?()Lk[MQ(!_B,D<&H.m$Rsc"4Z(aLTI3e>m'jYK`JXf3,
%W'%p'9a)p`%gk'ul50rY7R]q'3U>pQ+c;d;VFXH)gPDm,S&i$P-9J7I,JCbD2FhrSf)d5EUdqX^7L?k!M*,a`C&Ia.Ei+XgOG><+
%P3)Oo76ek7P5725.^R!?%gWtbO3]3K`N6!g)G5:.4'ab4NESf1eKCP9,6An$Z#3)e0<j-fZ>e8#70TkOd')0J/]mXZ*0Dp2V]e.>
%keIcB,MFS%)(J3M.FjN0GgS=3/17_2o`1,l&K07"a4R/[CKL`aC#!?]PI^oAn#E)8K,gl)(F>sO!/h2AI]m%@?#IEg14[aO+P_fB
%kGc\%b8$F00m&KL;;c"-B'0pqD5*NtOB`20],G`A-]dT!>#s4%+I>$_%SnPfrN0o`/MN>`@1\66Z)IJ_kaVl;8RGe'dZr2_KrE[K
%2i'';hC.5EEtV<;-3+?KUoGgNfm0njli8Tp[G3ZIXZGhMWjl-SV%\F]Cia(K^'/W:r#&'1-Tes4J5I#],[15Wq<kln>4V9BK*(:Y
%93VI24u:fN>j*p(NdY1mO)8e7f'(ElP^S4G7Y)L2V25<-@c0Kb%2REg5NS?>5LkeY-Dmu)oVc)4-IS)?"XcLcB)+`0I[i#5SSkG3
%TKFJ8(GYu\k03f=[mf*Z"%1L:.T(-o`sP%PVS5/2qq"LW9dMe2r!NNkUX[&Z,0CmuRm6j@SW;ZP&HdH<J?JCS\A@6S%'ubc_?I.O
%.8Zsb1ee_.\4FVC]4i.MZHa%s(T0#H^UDTJ:mT*VB,KeorX@<Vr#0M$7%8r]_%kKE>PX/+h"SP^OMQRY73J'unH@HLp/r)P!1>,"
%BZcNtkh1poIoHc]Bp<130bR:1#g%8.9T'[NiTC9d^RKW4$*rT!SN#%+Y^<OIUfGB:-5ME2RK[Y.CW5-:E2HZ#:EY>ZeH6e2Tnirc
%#h)9l8T<&K)Vkfa(e+3(qpJ_)9Q'k"\1Npo\nK7>mF:RE[dH3o[XC'/OoKNA29JQPag-]rjZGDmi<gQ:DE&Z_#Z>E!E>](:UWEZL
%E9\`J+jMu)\W^>`%mm^gLpga:3-6T^a4#$>5oN7-YgtJ'$2*>Z?se\lW7(0.(LQ)/PsE4(Y!iE2O>c)TpP0_7)BnXTN6/&%nM1jD
%0Fi[+kk./4^mSfeUE[;s$"<t`@l\Dthcfg1V8sE76aHt;5H[R%PK7ZFh^sBu2+5_\m\RP)=J;f6b[5\EA0CG<7WVi";sZf[IXS2J
%1"RW<2m:=^F,<<?]+uMi1aR`rKn#Eknq<N-_q(].Eo\s5r`0cbhJr#rdC^1H=OO#Z[.GAYIa4)^H_qWNc[4.$_F:@9*I>iW3E0Hh
%TdUaqLDnTl!#=JYl:E8E<(mn=*TPp\,NKU6(AL9WPm1<JfAOA6=h0p:_JA2g+2SoO<ad.IPl+PiKJF^\@tZS3>BZLAh8&h*mVJ;K
%AKgbYP230LP.bd]1C:sm*ERAJ:e"`QmiKZBd=7e^dXr?(Spi]Ye5PC`0u765q2ei\1YE'tBJR"=;)Okf3IoJJA?\Cc.u#1B!L'e]
%OIQ.B#X*J,Q<A*:[*o*Y(IoTRYT):75QY;&J@S'D*Om./Nu[OUUql[K<4R6-ic=o7_3M39f^"P>;r1a<A[DC*bp_r)QTt8SBh9EV
%HuhLi9CdrjJL)$l!k_4&ct5c)4lZuc6@VtqToQ`+=g62J\N:JA#VEJW04;(_hR"$h5BD1Y(&D437K4g:bf5E:\p:oqj]LX_oll1;
%bO)T(I-ppr8'Oe&%;p)[F9Y:cbM&jh.ZG@KOT!*8"$UM*0S%Jc#qZ/$W?C6?d[&%6&S:)/Vg!e/3t_FnjPjW?ThAB%I?R1ageR5?
%1qNmXc?S++S0Ppajnj_O@H1n6(mY1rCkggn$WuLXED6:c4B@5@42>l2=L&IqlJrj,p-,Uj[ZuNaVmaIMP)A*_/Tc^&h9cZ2;Tl='
%G17=_RZW.C>Ju'9dPAVRSHC:!Pf0abj0OW?;j$Ic_,EP"L]Xpn)t(EGI'TLfSf(stYYAfRIKfCt_RPqqdSLZl;4Q+Ka'FcDj?e,9
%VVsgj/^+q10S6iFG=_T27d6')E9bQU+.b;dI<Y)RF6)I]b5JI[2Ymg/jDs\c%(r1"GMQ0Jis;,s'&[=?WtG86o0bI1XH."5[jd"F
%'[<=a=tQ/3R1\/afr]go@F;lY!d*oi"0'tGrImru;Z6Z8Q$_^&oC,293i8/U9nfAXlX71):H12d4I<O3N!4SG4s>pKe_;%r</m]c
%'Nr9K+qdE`la#5KZ]p__631XabF03a[>"sGci[_Pj^n[Q]n^,?`$;oMgoqk$Gd6E0gQ:EFEI,tH^":l<p^3&F()#Osm+.$p)!u=h
%4V$h9;)RG`q5hpaK&'/SfY_:=j/Q,NVM#(g9tUb5)7:Ie9S9<X#=.ckg!QG8,2mCd@k94F0M\Ndn9+X%99A2\*s]H0gHQ1:In\Se
%oFMhs$"d#LYL=/"7]oTp$+Gf#iJYk5lAd6[S@0=9ZtdTNg%V:_?R+J!dXo-+$Y+m]qRcqteqCZ#($kcC(:uEg,n0/I'$A4s,04KE
%N/F[4OAslWC^ppk^&qZ9loc;I5=OBraA3Yb6pn64>6_8g![4-Dq:eJO*.bpRcTL/-iA]$-Y#frG38E_o_:C2\5TNY*)O>F!*fJHM
%9/c3)P%3oZnoLa`OJI:+1uQ4^q%l6#U-A:o^OEaUqpjE1N6Y'S>8V/NmOXU%?Shh/IV.t3J0NWF-(eMQ"H4g9\oM*L=S'bHs#,-b
%Y3r(d@(Pgb/!8@37N(%5U2_IO6$XW#;[EKP3VeA1ioJKB;3gk[Nc.^WlI8)\]=uh5'cFk?,.!s-.K#J`561Q9Pde6bQY[OU%!1L)
%-p#rcbb"^iYu7UEG"CLg6&?Xgah$Ac`b+"ar;?-+(gGFe`./VhMWP0P`u?U8'6CuB8q,:o8?N;9Xg5,##qmGAc(+gulOUB-cn8[Y
%XcV;]*DsMGTL*]QqKVt4G0D/O*d'P`TZ+J`5i$`;16^EeDEuPaE@Xk?95f1lo!C[i=P7q^b"FF^89qVQ%3]V4@E!_hf/Zh*V(.#Q
%+k41#`Tr?rB\7+*cV8qJN!?C,Z\IPFN)qiBeHb3fnCi^@OhE5hD;@>O)SsamQrf2SIJitLH2ZVZH(J7`XZ-<$T\ElefbWCOA0atU
%OY(_B0*.pEPdAf4O@@0YQJ"39X1"[.R:X#:"4>Y8_3a97/7"BoK[pMm5T-H>3UEfF-`I.Mj/bt5l]@'O$>MAN#e&b7i2PlNh>T.N
%T=JAJTZY9`ALctm5UWQ3,VQj!S1Anu&QHkc1+<:88p`]RB)Qsj&WUdjCCsXf_L;f`5SC/b;JaI(L]/(`@6@W@[_jua_#Rdc*d>1Q
%9TA&cr2?ck^/UO%1Y-nAl<,kT`9(1)$FcTM5;sI6d2=OtJBLDibp/T`>Bd8K<"$X!1A"6%qOP5K13N8P!%[Ya):)--mUd5#;D1t8
%i(m==cE=td>Z/BRDo@0$7^fK;PEG?_Zr9kjl\aG87D>*/"Dat/;ous&*#,69?G3PBrZ]=hnjWU8Z=OhpX"TA2'>6DHrlH*5hmfFC
%j"8=sDaP0V6Bk8Yr*k4'&O]WPf]p%&H=do:].ikpR)]Q_/jNB`F!$P.N3;qNm8s5<r-%iP<RDK0@g*$Wi?su-e1$A8[\?pC]AMq/
%2n8u6-TTt$n5\&Lmq6$ZK8htEC%-6IY_WKH_:1.J;k>S.D6pR<]dPq?DY=o&5MmG=\/mr)>t$1?6\h$l/m:gPd\U(R<H5Ind5a;R
%+f2"0e1]./.=23h:^:^V?,%C1.ZLKS6q].q.>lt\#SD5(_:Z:aei,`,5tZ5FG?&Sk10Xn5MX&$dQ:mVW>t4MTkJ:e^YQc,t1G.Ph
%XPr+KVo886+Mp41*j1q0bB>*m?0!bV<%0I[K5!I$7ghEHG2=0[Qp$EOn<]?r<0O#\i43g3B1_8UbAB3T1)>r\7lL>@/6U:tg,O_Y
%%<f/,d_$d_O(Jb/H@.tpOeCq,K*os)IS75,p<Z+G_1d3&W%@?D,AF`"Hii(?!G,]R=`q'U^<70a=+(o?%'\.fpK%C?b_4,.@^'._
%_Hg<e+C,22ej5%QXt8au8TBg<"g6s_DkitfQ1,'pj.0:E4T)aSqW]/ii95q[_9<-/aYC=m9qHlH9A*,D[8Y"_>Ionhh\&R9Oul%#
%`'pLC^iSqAeFhnD<FP,id7;M*FWh>3YCJE*=.+D]eBe(9<`c(6<f*Q_cn^Pq-#Qo9IEt:"l:/<*>G2uH&B".gKn`cR5@NW<&9X"J
%@L8dVo5l$_o@S!#Gngp[n\"4OeL8jcF5&@1K^\0<fOP9)$00g=:BR05NhI?ODDqm9F:EqeZ=CiKIV\k+)6ho-dLQ%NHs9O5Au1fM
%*gCPWNU+P$40"g35cmW[%(@Qn*e+6ISh8i3XKgt6f#m"k1t-Nb:3BHkV"*^/OA/!,B*jkcSkGUSDqol^B/?:OBf;.cS3:,@[X?>+
%HP@^m*&^UBn/ZaTnKeIMcMD!Sbb)2?6E*5mX+m6X8S^)Gq[p8SS[Ur=i^4'2Ut^^H/&J$`jq0rS=*"IKAUXj=Au6i)?fAjW4J62/
%p2og?Vg_L'Z:h/D%sSaL!8?/u/:Y*F0Uf?]V]5;^`Ab&:mYIRBXd[_Z#TUrdncb*<1abA<L&)!p7ju6i[:BNWE9?R((aPO#Rh<+/
%Kk[nT2&/Vi<B@dglei"sI&H2sAD8s(B,)H7BZ93/fZ5_<?9A!*^%N^J=Sg.i:aD,IUP^7sH/qtd^dBZhkj"Ma4cIgH?nC6C+cNar
%ds/(i0uB[C%V#nla;BPKFr:.81*=4-djZ\GD;I\;&Hh2dj=qlBhS9M-d.WQ3]L(TOG_Ln&m6UO39<FOgYCgI1+q5[*V=d**gc@6"
%NP+4),O717&TS=a3/$img:hG7!-Z*ROiJ+XLi#"IYGOP3e,hYISRsFooI]-\d-^B]JlB.h0K<(P*:Od7?iuAUi!EiYaXFU3s8H*9
%m<9;Jru$U'nT`rCaEHXn#iIdT\b\[\H-"GSUj%XPa]K#uLjR3Nlc\LR4k=7sH8+65?.)L!QN_jP.oWe%AFs91!]a\L@"^c.l<B<6
%a*\#LZRGd-Q3kZuoVLU&,:)1Q["eW!IM$QE0+7ch.&6NQZb*8fREU./cP/TGi#9sC-Fm/k[*CN-5R\oRA=7Sp<h[:F.r*9XYt#-P
%D;2Kud\3kO1Q)WWNc8lDotr`:b?BZ6FFsTNYYI(g2UipATRfPqa%3iI-]7@]ArA\Y5+^1O6]G\ho)TI8Rlu?7q^Q?O+CiAI:.K?c
%.Qho<fQq$dObJs[*d6gp4(Cp$,lTf]&+cf6eUgT?o!f@L7kV;j787_-0VT-0e9@dV:D%rs@mlB$M8a]X/)aHt'r.%9(rUZZc5P;I
%dEb%QaUW4cqSBMYKEhNZ\4D&b2.!aKiXkH.`&kEHa,on"6'-@]T]^&t(?PH42VHXMXt&tPTblru.8ojsOJB`WH%7F)+R/n@[A@47
%102;f4$sr9gnf0R'8plU%]A1C8\h^uDibk^_HDpgP$/2K[5L5*6/$Z^l4Wot^-W/%0&g"+)';r;.\S-!cd++a(>*sKp?#E7h'E'@
%qi5PWrE5&AhY7Ka\KEDa>m&\-N\deP2N)PR5Sgh=Yr"@7GlU6B5?M*kWLIQM+I2/$2\^[3IR,`?1KOjF(e;0JW5?gSYpYO$j!CWD
%XdtK?S@RATK\fl><EO910frd-<Ef]Dd@eb`A'#Gm<pjHsTbIf%bGmq>=r()d.+QMfX3GeVm!A\W1X?Z%+;>VR[T&ECgn:)SWYrER
%pHg%4KG<M`+KsRN5sqqY6BVUE%3$l5h+OB6=c=0GaFrCd7_+NQ_a[JTQ0kSkBg<l##Utg"dLp>Ior.j!dA'UX3QfWF#!A&@AplI*
%?l(P_\ld,Q?/![d7KkLjen#O$Afo4XaXF-/aF@bY9$_[Vq/"I2pp&>gY5W)6/V4q?)SMkN'_Y6^-RtDk6PcP7ir$'kJRG9-",A0-
%[7?U#!7N/`gQfN"8M?#`6=aV",@BG?!5N+n6Zt#gA0WVYr&3]`KTSmCpl74sQmgV8QG<;+E<=G+d-l/_.[W6i)07jXTnBmWnX=Nk
%>*?+rj%TnM6G6K!C7sY0&8tg'[/O^%HAi(W-s:RnH4%'1RpbkbakZH4M8Gt$!85l2Gg4N*8UrjOq*iMD>:8l>N^]F.NQ0J8Cn[%j
%834&YSr$QC>EcTCW<fXJgXmMm3!NtQp7UeQ/L`dfbg25C-:/L<O\-(RWcF"f+PJK0'!\5ET^:5RZmf$=6Pm4qNhYgYM>kq:M?-QB
%L4Y?jSr^d=8Kf"N]%#8JLirPGD!@%Mqu+q!-X&PY?OX2pDT\0c9/qnu%oW*9MSmY\aVZMi'c>?`e$p27,Zf*'*aoE\`HAN,M9A8&
%5gYM*p,T9<%GDZek;Ns)3p]"+!*+>nRm!mb;/R%c'od$pc"U"T`eK\]Dp8'a0!/C)Y"[[&P@6Q0TpL^K_8dq:@"2G76H?Tm0NO+W
%aC9g604^K82og-r;&kJp8dM,R1Wb,<,3hX:^WqZMiNNd#DI?`>&cq,LkBhWhP9!CsmAIY3L$Bb8TJOP,"PW+?\d"tO(K5AT"C9#m
%*J5Y.'3I-i[GSt$=ot3.:C716BSLsY.%[.T?Ncd/QNjJZ2(=Z,e-`:8d)&TbUin57L-RjIZiSVu6`thD\HaqZKbrXkr/>rnjJ7@!
%RO_L#`2ZJ>HDKFVIDDd-D'Yg;1pgY<<_oq+dJ&a@@aG$6E3%i)7!]q:pqr_SIqo1-(+;.e-FM%S5ES'=,[kCr[%uGp6[MlfIc,pJ
%E%jdR)Z'=W:Fh*3W<mk;+Yan1"$CmQ(,gLBr/u8R@N-3rG^eW+"5BJj*8;,8jp%T]A&k@;2+OUG\6VhO$MnfX7_f/!O$LspkG[W+
%U=Lnb2r_D)+Hmu_Rn2#<Nl%"O3_f`IdoV9pXQ;[E?#:lq$IB+-K.?T!4?Wdhh(CjoY)r<l`o*2'--SG\E%<1X>4qV6Er>b?82Qb[
%bi8qIK-c#i!#@5-<uQRN7JQ+#Q*9I[a(/-\"9#:!N(E.1YG*cja=@5]s'ktrr94,Qn$.?;0,@<DJ*3s(s07[bneTY4&tWKRfh,@u
%c1O)'Q16uV.FDIgP(dSFU9=Z0S^foteLXR)J=f..>TTYf=a1Xm7s=r)#\gC*ks8-!%gkG1_=fIT@uGAg+)gGS>-A+FnuFkK\q#;L
%\!RHLhkS=B>6';[L=cWl<AkII%Yl;tZ)T=_][0X]&'f6%kpmG0\s?Q(f.kfd#]M7<eiq2*Y6U`;qsQ;E<t5ci4*L[*X,G(J9hGI)
%geiB^>^M+d3LjjMU4d/1fr&&/"$D+DF3<`08m#Zcb4(DaZUSI+cA4YjVZ%Qahr]An<pOf0!n#eIlrJjL1cLE#`iE5F"CT2'EWd*C
%,*>uo6Z&(]Nh<"@6`-p]"o&N)SY7Pk$[95Wpqk?.;>XNqB2fJ?;Zq$S3t)l#1&'rMChTqFpc0+I>ZDmq<XWj'5[qm9$].PG%Jggc
%qKr.qN*)/tk[(><6UW6.!$Rmb2S`F:qS_X5?XI+3.;Y<!7V)%bO9.B.a9R:f52"$7NWPCQqjd)/Vfl6'oft?KA'Z`s.V),R.M5Z[
%A3\&t?tD.HY8W\QS?$QI]j*`kWbp'#%%&LM:6d<s_1F9!?mfrtK_,)uYO**1qQi1I?['KbU`M94>'X_:B(WP;fq6UTqR;$Nidao5
%ieYDpdAU:Y$;$7SOAI*"6S=>l7&NA-aWL%i\X'e^IKhmu^;$Vu6'T-e:THgnQ!577(:'tAT(Ik!B<8'22HObHgK8HZ3FdlQb%9u*
%/fB=!<OC#)U:33N)M:6Y=UZ:]]K"93`]JJ5D+EoOHdPtdG43_464T*b000*U+'WU;j6cj1`lfBeQ!0%4TA_OIW*b]cR[iZU*UoK.
%%L<2C$G/CrYc95FUe7MdX;;Ru9lc!U8O/98n5EBmN:%eQN6/anaQ&hVogqnJfZ,O'&Z`p4hp*WkIGd.Vh<JdAee\j$5!p+VbD^'f
%%3KBWFno`#7JJb1!?rD6^!)-2/DJn#!?COdLB&R\!1R/BZ4F!-&DSf`'S)Zk@Zs=5Y%$XU'!:U.Q#!hB!*\09\Pu+mib?"jYVINJ
%K/o77$Kd_@\:e4F41(cpI`*^?_^Q.>9@LE$KMDAfVCrP#QRM`7Sesp1r@jB'P%H1H!qiFS.9%_?BVc54eE\-2p/\c#S60BW4gZbd
%Y&o<kQ`CLSKWfe9:dV6.@jcs1$EW'qSO'B=M]7Z_-j#jq(N#([:YCo.`JXi]7^%;gS"PLY#fp(TYU5C8I[l/GC-bb,PlK1u,>#d-
%;_!jFrk-+o@IL<$j1t?',p]0M3$]81)Z:/#R!jr+7"N[.G/XNC!:)7"H0$A,McP%l`pNa!l5-0PrfjdjA.o+;2eVr1IQ;u@ho&Cm
%#"#6]&$iCKY=X9q7XqrH,P9:dfsMm9N1JA%oo]HUBKSKXpJq&L'm/LtV%.6f&h8EYYMqBf)<Buo.VLEdeI0\4-tO$2VC0a4hU&be
%\6e^,4h=i:ML.e<?oK%EU!=q*[2$g8;INXk_,0h2OEcDTY:)sjRqK^;/K-E#/6\/O+q@d-OlP$n8<YM#6=IT2\cZYG?6k<E?]shB
%<k@aEf[P\s_,66-q-94i!KKUTe%ej)m>`?c0TGajJO's'<U4DX/tlGlBfCn-g#NJmCNc[5KTh*?A.oITb7S&HWd@`,(?;omn-FS_
%m\%QeBNbh+%G!:PXdX[g\S@lV(!pJOcfYQqm-Il?d4Xh3)[&X\ljmlrJT%Dk&Ja[\P`A5]kFWBh^\D-pkQ*/V8t(Gb@e%:l<X;C;
%-&S$>\kChLBDj\^oNJ6=`nON^dlWm5(;nfPpS8@_QVDD'SCEP72.j/[R@\E"8+Bcd\6uR^lYSPGTnQ"t`XRKR!,k4SKFRO!"Fj$#
%J')CL'<1%n&<7'nF,&Z[n'rkCRnf(h@2-#_KX"X;MAV7e>Oj!.A5I06G+LV->/\N^7IeO<jrEuf&tu@U:MSG>#Fg<Iis_^RmCTEA
%i(4ELA?%4(T!/Ik.OoHt2,M5eLRl$-#\/e-[0)*nZeELr#Z;hFN+Rs.T\pg0Wh@4gcJ/]#^Mt$==4"["gb-s.W$5.[Eh7Ecb\g_k
%E$ns(<`OZgO(_^i.P:L4C;B@96ZZ$gQVI_:>!eT!<"pT!;NK9*"$mK&csb^u3Dp0S)UVIee[!?.?oM<$U"C^3!A(_]gG88L>_FP?
%<7-+M_J<;@LN!Csi=8q]b_'5q>;?nkTcNr7[:oFii=BXJ%V_j*O--60$X)bM/9M,C'?gJ.6X.0YV^\S`cC5r:1sYU<=S;?=_]/)s
%"AdM?"p&4^2^d+;e5IY&(VaBYU?cSW!D+5EDl:U`'C;7/8jmS\M:nU%c&MfIg=F,fC]p-W@l=]5h,)43fK$Dd]1dLrmC$0N)d1uV
%27rX9$b"!!m)k^>,&.aOcsq7o]])"##>7!+eXb]D#<8<\N6f=oZr-TZkT->_qa%ujRd98@gB>u?@&:.6Kt[[MR`hC`7[V"#JIPK+
%"9\`O2MI9q8nrM$;3"&kNGqN#!+qSU3$KmFcXHQNZR-WdAa<iqilFgJQ)k&@W*Dr%j.FrF-+69.@/*[;V.&^!g:soAs*UDXs,K\k
%!9*l<Y8I6"Ia2le:ukV\Q8jV3U'li!Rr$RTE'le"Fd&h;b".mY:E%]1e>X.**_T0VclNf*+Wj,t<B,('m`c4Z,i[.ods[#4/W'dt
%:njR,njXIuK+"SjS8L+[3MVG1P)!V<%*RHIO>@[#'+GKs_1ZY=e:o%>Fb;UV*`sl1^$uNUA9="e\Gs-1(r<EV9[0Ua!G6Ke:f%Ku
%o$'+DMknOXfDqLO9&pPNV\+b!f\(_O5)LeuMQj3l,KYU$!ZQHZpkd_Df,2<7c2^REHFK0po).^J4+[DKVl3a3S0O24Ye(tegg>:#
%\&3]FpjSp\-^?Ur2i>ftm"@jO%OgSfnSK,;n;VXOa4`I:f8U=/l,C,+>T`@11SA-&Hs8*Q5?"En%DI>o6Sa\WnUB0UftBl&F3Ref
%TiqRm$]H2S)Q>`Z?8nk1\/M1ncZMkb?ERp6>N>RH>QVCPlZ>(*7WM"As8/N-7&&]tG$0Wen"e+WL:kRhG3(GflY8dK@HLkPK,g_a
%\2a?:ju37!Bo$/EU>R.,gO4cQl\.(C2KKQ!a/?uAMG`"@G<Gr^qer#]hg+4[OZcLr?q]CK-YJ(d6QciW6&p)/LPs5e]MPbrlG4+;
%)=<%uY)uDHo++=Y_@H?qmbgsE@!\o;%IJ=qZ*:*eZF*k%?$88g0Hrjt5B]An#n-3/NZ/!RC>NEn)$-.PN)p^odS!U#<kI9*Q.gJJ
%&ue@1,j[6-LDdeZD8D]m#!+?fL84*?l]UGZDV3!76S)HsVKsZ7b0f^RC!S[=aLOI%H&t#W7H<M&0Dg`T..,%VY8sQ9AO:o<m%J;l
%N`m="I\e-eWmCb/>lN]u\4X"+%beljePfuWDGf_@0sFoV;<O16m8.p&2'1^:I_BcfVsT(9SaD!,oVeIP0>+do(-V,inS[LbK&%oa
%qDpMIG\/1!"#N1hD2a!on83M8P"`S(J0BYd/&QuYXQuk>#A6+FPC]d3%Fn6M)p/ZF+-K@3=sIXP^s&-f]V*o=DQVfP<Z?VLgC#SY
%g5gLfBQRSI/Aj0O9RDZC1R6A$I!NMQV[RkW0X_b/l8J);Z:&tF%OJWle.ls^[(]/'$L]Zqk`&[6L-X&C7TVQkWg-<;C=lPWg\=Sj
%nc2sd/fYi`k@c_j`TBD(TsM]#Jr4,"b<MlBJB1*5KXR!p\BVeLa8Ho$8-BFCf&#-7potaL;-L4UpY4FdE#2$F!:=mOBWgmS('!-u
%1EDQf&uDl,O+72QKCaH9npn>AdTi.>!!2FSTAU2IDKtg6l[?ko@MMLl)s#9kaUlH,HM==.&"&f&\jRfN@Z=l#.X"*+l#9b.TVC\2
%$?ZNt6H";9/=YI%1`O,!dEI&!3'!W4iFX8qIg5YC396[.U@4,tjD+P]P.Lf@=p__r&FlZbKJkEQjj[U$E@?>QF]VN0Y_a:<l4b#'
%rei&aO\'-KRP[4-Jqju\'O)aIUT5:ge-@?9o?.OVN>;d=F?dLZ!7n\M#(tDQY`56E4_0uB'9KS.`YqEEfGdc_Yd^B[X]Xpc_ciWP
%`aTnY"!/S!lbJkM!sD3T,s=(AD)oPMILPXtkFTcj3Pc37P[".(FE1GMp1,9'nas3)YQI!#2n_a%k(Oas)Z(YDQb:f^6t=,dUG%b>
%btfqO8l;^#HS(@7CTG))I]P^WOXJHX=f'Nd]?W"Zpt/*-Ypm+<^q"Qhb)L2/U"XSrUIU,.%J=rd>#NW?>',1LFTB&(H\u3mqr(4Q
%$'4cg;tc/pDjXGY``dfQMQI5o[R9o)Y7a#S(`I-/g3)7s$B[C-GSYSA2pRQSge"XK;&+PWBa/CEN$'UPgM9hYo*PNeCEAVEFj-Kg
%ZpauG51JaF0Gm`d"A12e1(tqZP8/FA9Lon/nTC91R$ueK+l"->>&)fbZU(\'d$f&a%NlCBoB8o_am3,m#p)21pX(,a@gd`h$JaMm
%MNsGui(S+6r!;e%XqL4;D*r\W"IFPo(a)q)f<4SW2g#P.BpF1'reRAND\(G?7U\I-A+OF8_TjnE0n,&Z<.$>,BYmW3\iTkWGWj,=
%g-;2q'*8!%R<IttGc<>eKHYYMm\Cd2gNnT'dkgEL]PCNF\9a8)N>/LK=.r']q*coZl$]a]NALX!iR8<&_?g+m'J$s[MN!7amW^Sr
%4([]$8YMAl[r:L-'JU:R.gjp"&kU"X,&)Uj%,q-Ng%`io-@)ZSW8IBeVC_jW=Chlm:/F,qL3%kg1A:>%r*a,JS<2i1@cj4T5T-p5
%@l#0c)M^8)UAT.70Z#E`kgg?I!p#S[n,>TR[@;Y@KSpQgR3m?GHp2^"Rnom43R]]+!JU<"Q2o@emiGoJM\O/K5npKI*FWhSGD[5g
%8a`M*]sjX@`\B6UHI;N,/oKqs[c!4!I1R>*H`YYeXQq!oWF6Hp)s:=<^XI(L5t^/K#_58qR#:G%*q_H>iEp>Ji-_T#iBIYPs$"0[
%2rM<$WgNXL0GM?I+_"A=jP#jEp`cgm3J-Y[nX3<^5C&%k>Se;rSd=RH<jYNHX:_6\8ejX-^dc=k#e<mRI#4tq,Z_hA;31Waru3!f
%?A:lS5Yk#A+I_RDJu''fS#Oqh2hISn$A]C\>U(XFc0c'*kEf4a-g+N;Or=:*fZAgq#oUYKpGEX!7T1BLB@pmu2>K6=^0,ml2BmUD
%\rul5+6-C:'d?T%#5+f[;j]bc/=7Li..%g$;TT3WP,KIaL6h6d3YIS4rYj+u&'.dKVg%[H:RomECr.u*LTl()EZB=S?uFq11B\;m
%HIZgQ&MD6DCqo-X?\)"mVAm6;^F^ip7G:#SSl"R]@L^])?-,cjR:se;jlVM]/ehVK]Q/]!`E^Q:m+-%I#UqBVYE&$ecS61U,I0M_
%-S%8A9):!MVl\WVi=Q*C42kI6\j\T`J4[uCKj=gliduBB$`\#PTHOK$FNLeu*;<:+-Fq.S#8.[U=u!=J^fWp)U7pRXio1WqFeIX)
%04OY]ko`&'KbY2CG]?GM`O65TKV1QD`k[OVHue;41)[B62c-Fa-!,F1Xg@N_UZX_"LO]Omb'aGRj:>\V;Pdc+/?Ed!1nsl^GHbO<
%5;HN#d;3s71blLa@N7jm/uuM"7E4*,Udq`9*5OBD<45!<p`S3I8L'#32t41YOV90EIu8HC$`/ZZ$\Ad`r8skE<N)aV'S,fh?a<+:
%>7,+PZE/r9T\IL&=`glrVZ8da-#:piK1f"o&JVA!EC88<#*tq)4XLu;3mZ<tJ+D*c]pkFPn>MtscKe/E$WVR,d,7`(C3N74almC[
%7[,+)l?fN=ln3$p)9Fd.UBHfl]Jj)2YfPl2l@:**W-M&:J1iEZH!'KPR*pi2Rmalm;fZ1;)^DEr5qhg?2U3.j#g]^M*Ji._1H[B]
%E4p24<KQ6]LrtLH40OM>d>CB)$)/lk?:29[8)#pc?->W6O/0M8g6*j#H*46ml<s9f`+G-/+EgR,f=";>\^Z=d_Q")hj*kLtQ;;Q>
%/KBGg16PB\W=p+Gll".=m'3]V(m;;Z(=Z:#L-;`+-Eam>]t4_^k&uS[^g'UtRG!_<AXP'`_V'<Af\6hN)Nl-1&jph%(bqYJ4Csu;
%lP,nD/J;Y?acrc5c-$K<MZhtnP5BnuL#p`kQ;=sT`sC25[QVrqc]bQWD]$7dFohu28XC4C]Y6_$*<$/COg+TBDT*5?XXH#,8Mn:#
%'@-'P>;DU^L3b>GrDH]NOi`T6nJ:YSJF2bsf8UAj'"X>VPEtM'>`qFe=m(K20d9m8E>q>Q7Si$1No!`.i']*eO:80d*6^a+b%2+D
%37sbc!@[M`A/A=W1+%?M-q;<n1$]-L;(08Or&ZFo>/5>5o;O>'O?giurAI\_YAkZkaDmZ+IQZuZY<,VO.p!#X)uHob%4TfA`W./G
%;nu0HXg<U:&iJ*3e$c!.%f.0@*l#]F:^TI2[UY6E9h`H(`a(mljM(l"dR_b"fPe#Kgn&t&-[;^.2h&j[7ClPc"Q!cn%$CUI#n!:B
%2'h2:G>&,WfjK.NV2q#Q``=J&`B:!OXLN#EV6EHE12;m"Sm_q.kagt`Al4+0+]I/>s3YUUDV24X6cdjd="^g5VJXa?+N7X-0c;9i
%[ca3^)3n[I5Uf5P:)j=Ufgl9m_Oq<R!S>%d3:R8#5hJ$:c+);,6DZDiLraa'r(C(LIu<iLK_;WPYsG&n+=U`h$V+DA7]R4Uq)k*;
%fYu?t?-pR&KOL^1buG@a@HBfq:%&(B:Mm9YT]ig^)I'qoNTVeT1]7)F*.%Wo72NAFnbgT15E>8`mF^;od"+AN2]E5gcMnIL.;eM@
%WnS=JqR\]W=MIq]#WI3'a$dBpU`7hbek\m><4$[GoC4XfBTnM%2DSZc!)O62!sKK`/]"8sWSX57>8m+Da>!9E7SnQ+p=Y)O^bXh%
%L6Po/EX6OZj57o+?@q1s[A&_mY*;VGl3E:r.B"c4+>(e`q"thZdc_HJ;WU>sl%]lkC,6p>f_)7Ebh"W$*l]\_BVmXB\gC3\n`1s,
%7$UZVN%.TdQT[OKIH_@p'e=*<n^CW1//cQ3r,s*/h[Y[:Dc)I8"=H:ZJIM2+@6?+i8ig[XV+>muQ-Q<W)VWAZ.t=q3;lHJ/bl$g(
%,gd#Y_0>6'T'KaHg%"aW>BDk9X2/n!Ds5#\%lnTp!bl3:H]G=f>G@&@1:6;!7Pa.lMjf<6*Cauis'AT4LUDsRZB"?WL=,qt0T)'W
%dG>3J9%q;qqqV#-Sj+:"Z,S#]Qi%jIc)?8-(]([gjXrrh"-sh.&JQNNDaZ(sVTr1+->4A`R]AN/DVFg-<Kk=4/4O-kNJimR]_M?;
%4ZNPE=`9-HYHg$#9[N]6&!OKFeRob\DJ^d>k=joljg<Ftp@Y%[^Y_'J#\cSsa&'iE<r!87(O]S)E_aA'j^-O.Dl_Cq-ABiY,LKqs
%k9%a"6jZlhe1Zu[Wa+L:G3oK)8_dq;R+daH!;/.1H*h2O,oN'E(Ks`.^o4:8mIV])=^e5p0(%MC/B_o%,a%HF`Y*d%\!.8n]uLE7
%ok?ND:j=`'pfCS".irp-*\TWD8dSB8oQ^?L"13CPpJ.p^+XF6_j,L7cn"f2Nm6fgND.X>\X>>=,oFm5=+u;b_ZaC(YRi3l"]>O\2
%KegD&8R24(;IY!0R$jj&,0pj#`2.lRLBPUh[[!.1]eZa8L(_3#br]6,0JFA_/4^F3r-"?Sml)C.[i$<OH)44;^fcFHD[H4`U,ZT]
%+/Fq)KX0%-0u>?,>XFjW(#,X--/0'j1pV`mN6?_:^3j56&l'L/f]W$N+tW.(E]?=,ZelC@d]l!3TVO.H43M%f<]0(bdqZ<De5sf_
%Y\2LI]$'@)P3\,Y^K<EE"u+d9Ca+_TT:/nFU:m"GPB<N`Vd0]oG[sb0lpt@NjXdRA:TuH97T),*UF!80s2Wuc2V2phfqRqV8,,:g
%h[Xj,2k&g!C2/McY"0"&[q,#co(9!n"UH*)YV64\b*#!Ehu5+Wb1o?a!JD`H(2_`jKp/F[Y!)7/R4D?bT53#%emMTE9K62T<[0HL
%T`M^b,dE+PC>in+bF>8@85W$Y8DV'-?piftWrc3*@ZkDB($T$oLrsi)9;8LjhIY]=TGbf.FmN,lWU7bp-!LDAbSKI9"Feg\6Jp+u
%Iki?D2Fc<Q[B4(L)<C'TQ%5?\R1(;(HLsL"JdmnF8g!3t^sk5t0qqa\qd'pUMi\?2LmSJu.N'lOZkN)pHE^M;@`jql8;&!6)N^BV
%d!eb?>DRn8[j"oI33(aj+3CPM4+/")B):T,#"44-k*e^PK4IeiBsaI/B:]53%D?p:U?Zb"8gob%)(rBMJ.-mab^Q45_qp9pg6fL<
%(L"mYZ=guZVF%uVX[`%Z5=2$#1H68<,ZEO;=#;n%Hd,1nk7&sDU=0N]j-P1RHAju>jX[KEL]Iu_j?\45ir:>W&R\GK?.,,2$`jG?
%@J(O`GX:cbg*0MY\0Ug?Sp_?:)aoA=rlM/uZaTLlSXMZHe#45kQ80Kfm]?i+E?(FJ.):*Vnn8N[cEbI2_a.Qs)D\:WbundmcR"R=
%1bNflU3E<K$J6UnF1Oe"+43#W=&HmjEWpLS)*g_^_]B;Q>QShX,';jIgia:OhD!$7V+dbiPVVp!X_,Hi5/.8N*>-KtU3Nr"S=4gN
%)/-8@`<_d&ImYE,REJ<XE?%p`HQ.N_6V3a\)%]92lL_r5PWC<qDj!He)-s=9^k>!;$RNP!9,ku5`?uS:$'DRs6A]tIZi]C+Zk>^0
%'#4jL6=5C^Xp?W9$hD^1gIl:9'9e3s.]i04n5*fCE"]DlOiIqugqk/rhBhQQA0D7)(E!EURX?=U<:TJYU.R@>3N"E[\uDf(pC7=H
%8m9sd/-A>=D&^P2_kHG@OodY/@&1:=<afSciH3.?(").FZFCsAW=pe%Sk`LqHA]E+Me4_R5(S,T(s$EWHSUE$([%K%KYt`mXPiL`
%8"sZ#@&51Eq)@GV(3q1;Ds*N-LI1'AK+PE/'Ir@+k=1^EQq2]P9Hb'>F]@E7+*O/lJmK\0WOs\paqi;?FblGg'f(<n7+c4dZrW8_
%&E9r^q`PR7WLVVpZ#JZHk4UO'2&=j<;?B!#^d-5(r5@ZPn75=8DG&C;W!,tF(KF!6LqpMV9:1NtOat@0M8gVn>DYW7PqkWdQdfMq
%CmSgU8"sG7PnZcDa,`8#QtD<[M,9iWq"l\0&sW$g$^j^YQl%.rEfgJ^.T7*J&]T]$7O]l3*c-`,oHf?U\-NGLZ%sSdgmG^kDC(J1
%r4=n7JVIi0h'HQjg]^GIXL;m6ISPi,M\`jX>Uu?"+>SkZKnrC/2c.606/;^T*_HI$(^J4)>Wg1T/?mahqFnmU=33YjbBf=pLla+s
%mt,g"%h?8[JWi.="d#:1$0JhsE9p;T,tDAL-Ci)UKRP\e../H0BCr`V:>jt\6Hs3iNrP6HW(:I8b[M4G@t[na:eIq)Y4GbKJ3m'l
%-119K7L+*ja#6%#%=o^!3YHIQ$RSi5`LLJ(j(innQ$g'4$c@_cNB`Ol1A5oGi>IJT5l+fjKPs\m-rcpW@=^d(;$b^]"*V6q&1mo-
%]M=QH;P/YAOq`7qdLQQ$n3WKt_^3`5@ZqV=O*k.WIm5sa#XT>J8AYi*Wg%9!`\_2ClkuIg&n>D'Yc.2S@^eu[KIV+X-"+3[gtSTA
%0^:oLKIO9g.J[r!TL^$'O:RAVf-YIaNj.#m.Wa\E(%OZn+gAj2UIA/12tUo&m9t`fMGS4>;J\PJ7EtlP;#_i:CK*nZ:mWGm%E\mB
%.LIrRXU@V)YsXI\FV$?=HMad)RNWZ1PB=l/isC1]GrOh4a"/0h3jbHMDtnnd\PDE)OOT3KjOE6]D%!o+Qmm:tWYV6iA1/,<:,J26
%/4!?&p^hp4cr,*1EP\5tDV2.%Nocif\P>oP+EF>u,Pr)Kq53##K`MAb-;.ZPL"O4/.tFT:.%eZ"Lkl]fR1U4Od1(4)6*X'(R95sT
%NeVNf+`C[PL(-.XEC.*0mD`kE!gF9!+L&]YYp0OuBj-8HO$$)Yc[DD]0'6do'6X@Ub=IZMmX?#38ZSMhmh?6ilpJi?Zub18TA;da
%<D:U_d>;m,3R)TE2''24YS2nrX#"=4,Xo4>8>to[i_7Ua$.b&gS/knZ/0bG0o9:sK)It:Y-Soe_k5s.S-1W5JADjr$kD)rS!7hRq
%83RRsL.Q13$g*.iR<Sb#*=q3<E#:i"3E*EO+TXmLW&Fq)]j.sQ)F&gQPRSn8B\/G^YRXS=cVR&7Z$0\73>mT4'\_Zk,IT<jDu,E6
%d3;?DiuO,[8VkfV(l*-RM2OJi+A1]REL-8;_G%!`X:i`HJ=o[+N.41SO@2'+;0CeH9,)!l?P3$"ci@$8Ml[2DmD:#2omIA[HO"5/
%YMmW-5\F%P^#nRfLH>rh'nQ]+.\9Xn6CrU=,q(X<a?*]h6s(j!]&9p!T1kV$0N(@"iS7u2%F/1W,YaA<<+*48eoKZ@a$Za^lR#L_
%$+7f-W\?0>.]>@H]0O$3Eto2kNOL+`1`R\J"86E]0#BGUiFjGreof=n4q)eF&n[EVU3f$<R.X^1_cMW3l8/!+il3;8=ZM?cZ36(Q
%jNJMg6KY`\W]"StIq%+W9sgMFm3HM);<fBY%RnqtH^:Jt*gA`dd,LED+JplP"lu53UKT:G9X5Of&Sa.s=A3cNAm:>;>qSE&P"n]R
%bZ!PdBa,lW4`H7V`=T/bMPV,_\"a.udg]J9>^#k,Y;U,%?l!2>:'mtH'Yo!d4"'S[Y!!BhfacXOOXZ.t6AB\VQ`!LphW5IrE];Y*
%HL;F`9]aZ-%e(E$@qXUl>b!mOZL&pjcY40<<JU.`bFAjWb"a+"2BqL@=dVOf$k@hp"dRLW+[W`oC8!*qjN=ChcUY'iP"?-_VL.IF
%56Q"nC'nWl9bLYg.ij]\54eIWOn/fuonrsW1M`K0"=Z*:q`o<Xi+@$2Z("^Q"9K8]Vuji-,c2+[,)H'2>[7Ed;+q3ZEZ_)rTJ6%f
%b/O3FiOi?'?&'Kl6o^T8=ATP?16"GD6""16U:YJ*ij>PPn^m!F+a=`FP78c7^_Fr'%eDpk2Ni+DD'FpFl]oI=O,iSr'Fs8NUc`==
%`J&b=9p!FSCM5`jP/'N=47qWCedXpWXFTRYn"u-D4q+!i/STi)R:8F8E$S`7L4LdkEWn\&#,0ZE3/5iC(c&poguG46Tb4)*U="&:
%&H#1HLt645;fJH]Q.B^?^#?S[QhZb9j&U`Aj8%ka!a6cAJR/L-62j0&?ZA2+Lh4U>o'll.XKt/pTaLYCjC<J/dUaTtq.O>Yb)0#@
%HR+&[arWaW&%g>n8q<d.@A*KrDTL,?,3PA3UgYqbo\,7qc:d+[jtY_RjR/`R?5/NuP-73(Vl7r8,>nFeoI9tWS/#EhJ:HV+8f1-9
%.<9<bh?(2d8BQ,g!f@2UKRoI&aO;oMUJGpZgn8MR4INu%7(,J(W+JUgmmaK66b$^q?%b(651#33K1=;tURP97`Behj8[\0*Vq_fe
%QKZO9s!h3g)o'V%Up@-FWre+06E_n`g,r*QZjF*p@I>>3#R3%r=!-M:9jXH4kB*.NPZ*k8,SB`.R'&*Z.F6j7Vtkj*L+FG<_nF=.
%5pt`6?+o29Kp/b5SfAl5/CTLOoU5giC^lq%1VMd*.9%._+KTqOFiZSt+cfkR-s7uhKSk(PTgg7L5DKsRk,2I7Nf(nJEb^DlH?ILn
%8YLe!.S2-Z(I-+iO?O"l`=iH8)M!q1U_Jt,`&#skQ_<,H1$?4]Tl*+^Me&JOY6gjuK&$,B>=[+;J-q?2<!S:\CRX+X0bbsm@E>B>
%\aP,?!GMqL$CR\]KGm71Z#n*6]QAcNL/m>'cOGU!`+b1denfT[kB@'G5^=6/2<33I]Z3T_Ia6@(EFm0U$qZ.:iV^KQA[JT#N?!no
%!I#q!,BnGDH8$Ma?=``=@4ESMPRPuk/f-e=Yfo0f>#<EGGQ=ZUP,lImVbHE,`*:qHi#Em[1Z8Ms8gopt5-tre$6!rWo#"KnaIF2A
%$8]&?-ckYPoo1C*(F;QL9$?4Y6^C3F$jhX\%J%>+0/S<UaSM^^$a=?FO:35ZiOuDrUl[?l>T-!X6(!UW@1'PRbT)>)+99\l6:\^T
%j<Zf9!im^X<^>"#CJ:Q]Q<mZeYr<\E/@4(YZOk4OFD>iBYAis&#T6C/`a;mTJr1n=pl_^V>c+'LOb/Y"#u5[VVD),T=67nmf]U)V
%hE_XQNlWSZj:Us.VZf`s!3lP!/O0l/d?c'O:_b<i<0g'bM_mL,p1.8s?o^h+ljR8SSk3a[PO,nRkn@IQKM8_c8J`LC'0i*ERu;63
%,7cDAgG+(P\eWPOP<m8D"O?@('iBd'840Rd!tF(>6,s?>qc#PC&-ecE>f&>M0MJXmY;9tEQuWqq=n%.f#"$6lX"WD1(:8p,l%QNN
%afpG[Ss*!(Od9:HY4%T<h\3a*iKPMG,):mS`cEF;'8CaD.2*jX,`k%WV=tDcbQU7;Vd.[aLlga\TbV3F).(jQ>!b72.?C%A4"EVm
%LEOg[N[/Cjd"53Ci`u&%A[$r&D+7_6q?hBkF)cch4m+tu1@8c?bdo7?7&=,!X<3B6T/]^?\.Rt%*%)[23Zhn^HlT.`Z_8X14G5_+
%JYQkeaA(=])^R>)i=2F96@$U7$].Hg=hR+T`unj4)4if;n;Y:K)Ah:!Lnb4\1XU`5O(nM[Y)/R#i&t_E<(Mj5Y'GFq5@j-[(sNF;
%aY,e%9-cZ1kY9\=<^>!DG[nd%X'EN7a_*[[;&A3PJu-dmbW2t'Z4:mic#$!FcU;c>_^6ZC-(EJT(D8J-:5P&=:EXbmVVd&Fk0Rk_
%qhrNW%08q@R>Y&NQmZB$<&!+['8;_+VQ:f$I*[p+!L\!8cKWSk^]=N-:u4&=R9;7H8=%>2C&tXbZ/8GfdYnM"8$4CN6Fd)UN>#2q
%eOP<aQV!r`/=:bd:V%6VNo[\QaEs-";@Of@.:VQGX2AD<^?I.DaSq=kAH_D]W:#.j`.-#,.WS;<?FlTX*`47$nD28+jT6$W2P[So
%3=3*UN0[`F6F#bOdF'G@AA!iB!GX-CXio3.68[sTc,JHorJ:eggjsEB*Et93)AK4HKcK<t-cIH9Z?N,+S:Ud8Pbp8dfd(ND3StY7
%L'P'Ck-DpGDH=DBf;K[a0#pXYR,Br)EYTqU5(\rHLbKZd$qKHRo`Bs8'`6EpHS%Bs;PpFN^64jA!sq!W0mAqC2DNRW=/%iP?->sL
%f:gb@\'^P9"NLuK]]U?gQZX+4YF7eM-6:5]ANgFBS3UbclkmD-pAu%e.WaW\6H_h;QuF/aJ-ZY-45+670XhA!h,/mpPq8LnfCd^U
%Sq%iHqtr.lQCR)9&N4Fa%:A3\F<pUu*XT`I1B57V@b\@1R:C/tpTf3'o9$86CEch"+a,XSk&HL&E`X;"LE^9*,S0t%q.P0GjGr>i
%R4?N3L/)r"qQRf3VR"G.YLa(5ght8[dlMLnERo:AA&.38\'9\5qSF5q]gj'"?&k\q-^beQc=\DX,]J;@E(!qA+_;NBM'Nc]o3JP6
%J4?di$quE:P\b9PFcJqG$ue[-90/AleO%Ntk_Pa@+F'fpmuZCK,461^4$!&<1E1K+6(og(J5mJbKSN&%Lb$f?]Lupe7\a?_3H)CV
%JI5ADkSY21AL*`a@&d7H)EL"h"h8;U@)25]Q@01I$g2%TK/9M/+/bd&1Upa_/tf@_$')_)n6'GiDoo@Q*jk`=K>C3L'a:=W$(f43
%qSL\$W'-8Y%R#@kK'cisT#_KL2,m/W(C`V33Ks.4_0,r."NMKN9X%Q6e-dj]7dDL4bn5qKgqam@OP:;BG0tt?>:)?\qHO3IeHEEb
%Z;+(W0N''MD'2tg3nX6rLgFYrlWFi7U76qZiT:,4%*q"iG_VEE;udQ,YU@F.b_$5U#VDaZ1t,kU"T(@rZ&dNW671QUM'fch3RM4k
%dA[Je-]O!*aa#K0=$oGgWffo2PF<Ac*E>HY5of:u7hT%HW$OhQYHtHGpT]A`!#\olYX%C5i[2P2AH,Q/fq`06'(Lf@)JlU3j\$):
%:r$\/.rpm@$KTjVN'OhfEC^rl,9boWmP+->W4U?R%+M$T'b-Va3\Yf@\s-\/"%e&u,U*6(VjrbS9Wk8fJej'0TcJuBA;Zgtk9P4p
%Ph\NE;:_kYQaEuZnj0V+$Z7:8p3+[@?':nu#Mq<bY&3"oA`?T+$(7"k6%biL0%P+47!_(6,U6-VqK(U/7Z';BcrkZ#"0.FC7ahH5
%23<B,9boEY:8DfYHt7OO&U)qLU^neKSjGF,(,jOfFD)umERKWM'8$;Mm9#l@j&+E@kY&:72^r`40*,m=O$jQZ`1=9S*n;KQ$9M'U
%d"L]om*CKn=#N-[&G.pk7$6%n`%\,E%`1D:V+*h1;GOOuXF(r5*s*0d@BN&5PnC7gOPTDsZMt50P%OHRrL#C/4ei?Q-C/`WI95U;
%BF7]a3]QDZSS=_+K8;CGG9b3__n99\l/RHJX[8,BKC[I/M&TZVRKImF@b0^K-*]VL.nN\@J0][l\rN[<ZRS<(YcT#O2FD.(#&%O\
%[:eEh((a&_/p'<9$=4@FP!r(F\[pB8'a*IJ"SPd_G&TSQK:M"Z;%Z,4Y&#;730G]^b(2WFN(B;/#XQr]#C0dCOR?r#.=t8>;RZq+
%(6$D,i_+=%=oG#!$VhF+CIejpI(H!Aki!NUhN9W6<<*S*-c2[`8s/j^0PF,eI(6*-7&LIGo;#shrQZU&"c]$AMFRl(H)L0.=ZFh3
%-:4mm.AP@>`EIdNnGFh5`"IFE*Bh:t*?ef)&g/Gd>dSbmUe^/7''=M.-8ISI#eV!IE(I8^6DT?7i.]WrP#>_m=].fZSI"UFQ&:Fl
%:19SBGZlq[Jc-]a8l7fEOlb"O^#sB'.d)Z%4&-OY#hh5Uo9@@q+7'=i4E*`//:/N^_mD`-^J4tfRrld]@qiK,Z"?P`BP\MH/^m9n
%QRNnp#pCbP+/sL@5RsP9&XHl2bJFr?cI%ZV4C&Rkh-/65j=A!+Hud<lMEXP5$XNcQ_fS82`mmGYO_!hN,9rIY@_CmF<4cDu4]rbP
%ST.8,!.e?@8&@;%Ncm,W^kkBXMM*A'k6QCe/!Q-5,-f4;A@VdP'jHr3Srb7LS"8a(<dVX1;i[$%:pYY+?@*6g,QqHLM8A<#=V4Vr
%#;M<5!n;<c(b,6WWRI%VW24B0Y8K7r^)4DX?u_[3Kb4t8aZmuNG9:?rcJ_qO_D0UaO+CgpOjdNO<_u+kn"X0cZ`F3fJdP,Gl\ue_
%M+9Fc9!;WP&K7761R`.Nap_llP7ks`g_[J[S4U*-b?=+eTNRhh02^]V)l0#INUPalL&37cUh.Do?<R/fd\i:=OpMB36j:^fV##Y/
%)CX$o.4]CiHa&K&@MV?n9;\Ae)iSK<&q=RiT)g@ZqoD[0l+ogc8SM.9E`26*[5B5Q2_a<oYfNRsOrYQ(Su.`=^)?L?,H;7A`A(tN
%FoO_U`D6iEJLl?^:XWi/N=(,!JW!$-E]VYh+Ran.ioRG))UR=k/P!fOeocEQ-l>+@Zji\Q)(6<qcA0t-9kc/JO^N&$2N8goJ0lC*
%W:-*)UZ#n=KNT@;#1KN"7cXg9h,6S=U4g)48'hH6k\(sWr3ZZ)>F73J;Lo7?o%tt?Znk?)i=h7)$Tn3[=Ftd?P@]-5N:5Y,l37A6
%G!WH&G\@RXe6tUBa;+Lo$$IVOYR5q/OT\L@oo]CQ8_rL,="]#p!qCk<6G99s85W[Q,Sc&Q+QO&>E*"n`6_+nMaEi7o7HWrrg&hVk
%B3tG[gZunM2(9C\/VVEOM'2S$-.#C2_G&U`#rht[&o:A)OqPEc7mqK_gQSLeE(W0N%/,mk&u5P]\Kh>PBPSJR#^h^\67>V6"s)e<
%'$"'k-\ru-_o.A+?cF^B,2f6EM3cGA4jG`moXH7<n4NDoZ10159VmSslpEoL/??(#2A$WheBX)I8TK2M+,)2gB6)%HbtJI"&.&=L
%3QX\njDsI2?0XV6H)Z"Rp81nP+YL(J14f1/3+X]IZ@km=+1nR^n\VG!aL1G/"2Np0!p500CV<a?<EMAIL>>=!rWb639Sstho
%jepu/7Q5'q@Hu$.2pEFk?Dj@S#kDWNGc)Dt-8NnTaP>jsC+EoZ$C&t]QK&!KjI"M9ZMf2^G/-@5VZ&b](qX0]&_]VUho(.7dN('7
%N;+G"m0s.BC03eUR4Ka&*0'"+PLTF]7o,M^'oSE>#[BBc_I.3ls"KYE#DpsjOq98gn^&Fn*$1^T?:M$>1+<^8!"\7qaSVPX<oe,9
%pW3.ZLP6eLN5NQ,RWH6F&)3%L>U_>,K3D5NZE4HgHa7]R0uYF6aMeM.c3f2&NsRFklrgQqbsFo#[Z6>'Eg;0=SB%_XYQ4PC=!LPp
%EF'.9_X?`2@*j:X,f9gr0rE15*!^rNK`H0(1_7G'LD]2:563Nj*;:#8q%FFN#j'iHrFjk]!^,pXO<r"\,''gE?#Ik(M^3@)U.jWW
%`"5GIU'F8Pgm0:*mH*C?LC4)fV/p#kJ9Q_U=Ji$ppVJ;LZ$ie^F[VUU#S1rpBLCYB#(Z("YX43HUZ5r83Q%^k4XK,r/J(];D8<[-
%VP%5D#@k^c^Cr$=+B7bK#\0bg>_uS$YnDWt>&dc)8'CMb`%@h7OdsIaNH98-"i:(HPM&+G7m1oq0]&=[dGR;L5T*-BUg])TaZo6L
%V","(hg@Ze-c'N:#B%V5p7W.*ZogluY\Q1dWm[sHMo%2o'b4aqPR`bgXMtgrjV2grULS9M$bg^U[+PI)eWdNWj3^3Eas#HrFPPpO
%^-L3b;o+na9I<,k!iM%(R]f-S@)I`RD">#?l3tMB>'9"9ShMm/p-ndXp'CL%--%[*"7hXcR97.W?BkRp:aNZ7ctU(aL'H(3@6js?
%:2\1h;S`_(F]F&=WN>CA<],I=5;]q=2nmI]-^`qq^51!G*C/#i_6hr]:Wc7AmPO'b73-q8EYKX!/K1^>pA$C.:fm'*A^1u/8?`!X
%\Q-uQUM?k(ETBT%#p["mX:"p!NqjN[foEC)'/p68H9[\V2lZ6W0_`6X(ahY\Ak1rn+#A+f`uJffc1f6O*?>eS6,bdd,`_H.//!s'
%;E$gD$2"0>#p_BYeVWklmY?p%G=e*3R-@E<G/^s$EN9;@;X9H980aO9i'V."%!g:ZVoO)IOE:L0>H!;$6;7M2b]f9<\=1o1C'(rY
%j(`$uUuJ3(g@pOiA[^I69O11.cSWL20U,5o2?39H*5tlA@'Z5qZH_^!$uA3raK3SE@:h*_36GD0LF\ubSkN/g0PNcV$KG@kZhb#^
%^!APQVa6#.[:sn"aMVttn4G1MQ]Z#-H/_\$/"d4QHX>Q\B]R)0@n<Era05WUJV;g<SnUKS](!+,n=$$[+cW=O5&6*t3*RIY&Mg;.
%"r$QjNF'i>rZD`BFZFB,1;$-mK#.!6W\@dI]oJmd*)RaX]Ygu:2X&?gq>]+5"RC=^O@A?Q^7($EUZFnSds4#79b)'@:7j@5LeQqc
%k@Oh-/R%XJaQJIZO<>ri0J0YiLd_X`K@+mSP5n6*j@j>]U$Hi\V._X2;_[P5P](`N(GN%cfNYq-*"J1th=PN'7>JOM/H`K)RIYW4
%\=X+:>DCM:/5034.]n!$<SUU;7<3/)`p;[kn#Oqp#ZE+3m&30Dfj%pf(1CZcLMTM;D\XGd7O^;mP$/C=euIg0@WcZfBl>R^Xb<cA
%gQTRP.3fO^cI*kYcIc;@bXg)O"R6[_1JIrl!ghk"(oY@rV?KG"\OmBc=\d(D6F&um6XHY;Ga]qL<C'R!W<A]ujC]k9U_g\&k0/?'
%MU!&R*]O\1(h$4QR?1g2*&&FInWrQd*%\63W=X&'!mXaBC7)CQ:E=`<KOcVgh,U=R(N0dm=s.p@$r6@pH7#_4Q@.>C_kU]S/B>pO
%*OgK.N[>'u*":(8nqi2q*4`(F9WlGR_1npV6aFpiW1Y5?8_"]p98S>OFr"d=nkKj,3=CmuOBK%4oA"*^FBjXY0Fm4=Ud.989QV"r
%],6F0dnZNhLmD\?FVarZ\IO?fkUAa?p*s.Xad=eP'j3+R'^&6'[o6Y?oXTn6@u1)l!O<>C<ucB^:21t!dfh>`cKQahMmkD?e8[CG
%CHm1DGb-]q>7P6=PLGb0Y4m4-C9!M#V1Fa8m>:4p".cfu1A.&9=f/oE,D3ULd+!e8&*Z\ZC>+e="lh>(?dIMV@d3(n%6</$+J(/Y
%A&TVtflXE"JERj73V6Qu\iD&ElIAi:*Cr3]IP^8##qU2j:\>Ws!?mh(FMhS[7i.$[dfLN"3L-(o:+P[`8[/X/1p]p*)i8VmY#?]a
%8?ju!kE4't?)COAAjhf^E14/>Es9el/SmsO3Zp&hW6)e8#(*Zm##4<hg:-ON>/G"+ojkX,1scj0Mb>,RX`3t_n3N4n&M5C.fX`1/
%[`PntGe4,Aq=tL#4t>6VZ[H[+cS$8X]hG3+p@tRpL,'?M,5s@[%MZQ^E<o343D;NNOc='OLMCk"??/Om5;*FH!!/C!)n:4%J4M;8
%&b_[+*/Hmon[!2=9E9\2(c0Z10Prp]@Rc9QdIOu.;7@q%\_'amQU2rM6Xb+tS0=+EU,6D`,K-.i?p^ZR=(16m?rh.[/!^4cm8<1<
%CBB0@l4dZO:iIUa+;T[j8IGf/D%JJ]PfOQ?-]N6d`%#f!0n<@=n7kpVm+SEtA0>c&'TRoVQ.e!WQ+Up47P2#&ZX&GIB`"I/W2E(>
%Og9ODCX;^3f9mKR9]8g0!McoJUMVoL\su4?&NYN#?qnGnY6FPL%e?DL5abu3Q!hP)niIKCc<n1lb`Eu\kTFlAp$;kC=R:0fgQ=$*
%##dH-`RX7"cjWEH:RX8iK2063$@J?Xof+Xo7@F0-\!938'_C'JL5>+ee\+njL1IM37P'E.]_L85*:=;%*X#uoP0HG3QQg'WGnJcu
%4uZWBR0]d\#hdsCfkX"tQ#e`9Q'+>d)=Uq,@l?6N]5N:Qclo33cmTMQn=B2h$4LiLN5"LN"R]((]IKN,"'eOZSr0Jnds*&`N.R+'
%@Z8%1/2tGs#Vn4Zs/.J]jM+sCQrF=AE<<ENY-(nU@4e)Db#YTM)54D?5qV3b2^AQhfRY#<&@GKZhb2K.30>2.+F/[3='WkGA>m4@
%/kqiHh+0@[1/\WF\<-e\9+V5]k7m;@pst("3-%2E-*%ZK]LF>@jCZPbDUAu@>CaB9:cpMe:-<?e$t_L09W0)Rp$,'-X2WVY<2!3A
%K5k"rdm!QlKb!]/3Q2%d`q4?c52M?$^r14c8Fp@8b<Fg$,4:h%".18U6`_Md"0j]h<"FC+5]N*1\FIE;U$"`+Ld7i!\T)"(W%(&&
%/&c#*7sSSW]i0PK7LPq(Tst2F!K+CjqeIHW%"!0i+nog"1T&+(TPqD:pYGXrC3X?-;FakN2iD0f]#+R)6m6e*0m?UaPIY2&2\]K"
%0C6c-8_RLS'2>Q\32m$%7&:'ss1iJfA9o[V_-=3S0s:73DAe0Y(]6!i0E<?BAI\A/U)?4;jV7,>p*>9WF9OcY"mA7nHm%BMi'-)>
%Pa=N%AVkb'N1"6Xe%ps[1HM:$2%\9UV6)$Hb9oNq9k<7\iFuu@-3rQ_G;pn?#H*^1PhUcmK2N%C7VZXX1Ib\74?Q(@Ea`du#iU1l
%ZoqEARYR&b"edS#<n,_EQdB4m_<sC];SlmE^=eb@$DZXYQ^4'088j"9]-kYVlqa5>0>H-]?9eD^o\'&^]4VAp:A/&^^X[?f\>A>e
%e;7[0CsQEs+,)l_%[_OfQM(<#o[1K[`Gag=-p9aS\A$g,SkW!VNXtk[%hDFtqS.N*I.Z<mF9P`G:VZeO`VJWgVr(etp8M7\o?%!T
%j/_K=[(gpmDH\Y&m<S`Nn^7@$L9CBlr4)Y%h6S@"J-d:A00(i9]t*!tqmV%:G&PP(ro#LiL]@,HO5IWL+38?_d38*;ho_ksRfE6U
%/GmUCm698&l$qLVk\#A=LJN.lbCCUia`*R5Jef`fbQJicWG[(=9IB;".`p;RKZ?$9bipM,cGY&oFt,mG.C3,U]--Mr%.o`FlF_*/
%lC4M,Cq_-u'Y:-s>J[U$WA59S[Rb?FJG=pU2\Go!;2*%k]<8UC51s8]@]j=U?qFYB"NE$(4r>iYh->u:ND:/?NHhtFS6$A*<R7e,
%Yt*6gM+QaVB__ZeC2djO6e'qbj;+9oPND]^%J5n']8Y4YM5.:"B?!>(K*Xi!c\Qm]nVMMC&Uu,T(Sc\:Kg:4K,;DB/?&.3Z_XJT)
%V$IErB(Z:8*Inn;#JFdH:Y-IoA9=8K`X/&@+CCT@cl(.ZjhQ`s=_q^Sn6cj%!jit-s2`A1(5n^$<alEIah0)D)2\RV4jk>^N(%d$
%#X@Mc*?%[d&8am0"=GWGUUh4C/Da'&#SZTAFG'8u3]E4,/LUWGI8P%,$ALE3,98+O+ma^qP@-Xd49^fP!,oYDqC1G;3bR\!6D$CP
%E^qA3'ng#)=bU*B#qCM6CdNMd``M/3_gE*uq./n,\-/NB([:IeCnNEe['SUo"!S9"g(DFiF<C!7`5(DE^_1S:+BP_li<j$KPJ>V,
%-ZPPL<B[QQWfr$2KVT;8lHWeaV5UHTP^OW>'>TH6fk4D]S@iU+=SS@cYhFpX`+6HH28TZYg+g\s_.K-tjut\R8?-P=P)U0!`jIGU
%U;:*1ZhA19pO\G,'cI/]!Lf^g7:sQ@o$,d*Gd0SC7ThWF/k;GAZC.KV`<aXE$kRm'bS"3m_]k;6L_-E0.3_t#;QikVI3ddX,khqP
%[R*oFAaPsTkn@PK90`i%\.4q7"[akf1$>7ZI(f9ZI5)"5B$b5Pk*Yg*9KE9,WStoK'M!2_9UnCF&HT(X^>-X$Ybjo.1m"%[Js:Au
%".TRdQlSg*SrjXHM[/;A&"Wu,$B]?KSZAtK\J7RXWdIB@7f4#WED.![/QlBCG`*V\7c,Xp.\2._3/dbVo-3W8BKgBkO>W;*!!\9R
%a<^+!`EJ@$c&3W&Pi$g5>6cXZ;i$J^cS[);GVaGA7IpLldLbCBP`p1HND4iX\C\ek7D"[K[A(AC=V2@oi,.9XjNT,!=]-Z"CJR:M
%]"r>\;oIAr*J"D4;PDCJW;N\aCE]_k)]T8q*6G&@20M@7*f<_45iA^Y@8[;26**b;3u#3BUe@U)#%%]S&iX6[N1Q8R(hH8PF=<A8
%4A'/"VsGmekrpP&I5J2&BXDSAj.',:8.lhSGia75nE,jPF+QJU!@9Zu$NIW>9`@=tXfql2$BbHp%(1>ikPdG97ZeF4mR?jN<K.mo
%i-W?t$(P1X04]ItM22cQChMV]TGU_gFW_p+Zn)#g-fH35oGmI$#2C^%6,6$SC/KY@g^*7M2e<Q&R)jVC1Zm0@EV+81!Ddp3[,RcX
%5B\]Y+$'^uUiH(]]CY$Qo\n/bY+\<R0Y)C&eI#geI;C20f2)E)[8R;kQ\"i#M0bM09^sSOc-U+TXJcIsaX.=TTYcP?V$T_)8+>S?
%nf#Aq4t$`j,Ybdo]KI"kqoD=R$tHScpRS0h+8)doV=,rqPGiQF%4bO+-/]-u!W-htaOmH&+L;aA`6%X36/YXs7M(Pr;SB>:-kgp-
%B0`S4+C$^"//UR;+BC#0E92gJ"[2KG\R>=!)2Te<W]h]n>09F7Kfjud$1VIE0fb0%E^Y,2**_rdOtKru<(&<G#r;G.76>/8rC"_8
%&>`oHN)r^6>/D_>1I)?3PR7^%!j0/W$1:7l=tijF3*HA=_/p%dSg?W(`JH4p>s3UJBMYP"(MY=^9*Z)\@=oQKR&O^G5Wq^.au%qF
%;f<*F@jAOK`(`LO49:C83DL$m"O^j/eUqIBB)iFPdHUEQO:P'%(sZo66aX3^SJ#T$_rOY\14P6](C>1gbZ-71&<RfbQl05<?Cb"p
%"+A@34W-iH6LmB`R78ojcEbP*=H]DUpQNDsUU5Wkc1'm!'][&<Z]<EMAIL>:+lko8qbr6S'`3nlc[IR*5.*I?Om:qZY#'
%TNgESG<#uAmqFhPjdsO.Y((2XaDpBfpD+/_Hpa)pP!?D0+O'4LFe!KQ!Fj3,#<d78W1Cu6/%d_lo1@'D"c/<)n]\n.,tW,m(C[Gi
%h*oY^#a)ln"\=\1a@YC"ag)Cciah<t#V]i2PA0r$+NbkRk'RmXQ5lZ<eN&Fp.p?>X^m*PR.n\ABWHoj[=>smG.i^6$PAl/"*Srb"
%j4W'9#T5kHb"J`Vk3n^^>>(M<8#p.hAa"7piCS*Qc"Bqn/V[Y[(8.=>@F:RG#2I4PX^i>$LmbRe[2$c]%N5COaoCP'AZI,/YkB5;
%V9s1&oO-'P1(:Utog'I`Co`[Aa6d<:,<`R*dipm;k/fu#MD.oKX[UQ,3Br=)(;+2O?f3a`!L0P;Sp$Y9W9-C%c%L4YC*IZYajq(k
%-?-9f_^nL*m6N3*DJd`K!?qQ*K($m1em=GbR[n-OJ*nnoG=km>.f#j<q_$Wh68EFBAB]3TC^d?\RujL86T69oY$##.E[*h,!4.\4
%@MIeZ/bQS264Ee!.Z5T%A0]1.gcNBbKS!X"lrYT%/O3E1cPWs7)+RMXECk7_,(->9TE#=Me^7Z+&5:LV$^)>e3fd!<TM?#N1$?81
%J]JWW#OP&?8SBnT%;[`OB/9t6XPht&$&0,J%(pt^Ft-Rb"%$7D8f0Wifd=V1Lc3oqGq8Q,0hCL2=a?P3bfp5\2ic!IZf<0Dg+X`@
%,`arWmm?NOf0c#gRq?E#rAtbEFg?Q0]?I(:,]uAlBPBNHC8EZ@in]c0+.>46J.49#]GZ^QPh8Q8_P!nSDiB;-R/23Q+WCkQ!GNh3
%>cQ<^m3*g+T_EtO2IPi]%'aaWU#1$8pNH'$o29$R>aQg`$:@SYQj@WW.p/uI1/+g-\F1e&'EBqP3kNOt*'m%1(aN-J9[!j/MMt2-
%HnM/#"S@FoD4o*E-_hQ\Gg.;Ma`(W:#?9q',06i<K=Vq<#Ge'^.f_Mj5B;EfeF1HY!L]U>N.\$S<%Dl"b'j6aaK/mo(8ikN;46r8
%Ao6J.HQ3ISe>EYXX7"%]k'O^><Q?VTqWFs#h7ut%(_ef6W7iC0Em(/=E.#]'5k[mT_OT+qQB,R]6HO39;X!f4;0>&l-.;\DNJ6Ep
%3Z/b;;1\ura@9QFJuND#OB8.-?C2.?$\F7pCo']l:+%ejcBFhPGMX?1nrG*F&ZB\!g:>6X;;=M>ZO#i_7OZ6(>eJKMf\f0O>W0AJ
%oH"O4@E<Ws;@<'r;l5=4\8JVJ:dU$N!';ACPX?3[^P"CCa&)mt3bBL'0TKG9j]W9+^B&d<Fp!^/=Lf[$*&6R3(@"k'c_;Vi)e"E0
%%o(ZgY"i8aoE-@OL[OBNS0@H`^=8/mc[BKh-6.%g3FXap42>=O&L%id9h34]#bn)UpGEQ"nk((U@sC+/E3u\Ip44K?F+`/%Q"<:P
%6PE1T,38A9&i3O".l6YB+HY)d4.1Y9Z>_a:5BN"oV4gp)lPRuP+rj1HcCZVe6_[114M!>4S`ik;r.ijHg6X+Q?Ps+0qSNbV&EkrF
%2*a$,faUR@UEt_]?rYsDfpHIM<6_LH<BB2$gQ@Z.G=*6>jMiL&VrrY(K4-]l3dRHM)u?s@7U973Es_H/"$CB$*q2JY!q5@2aO<C1
%!D`3%&Bg7F(-($iM/TrZ/Yj@*%G>e-)Mr0=N)[uR3)=/*U$ZWqR>iF#W8'"8(ei!$DWm95Fqua&c'5`RLq$CL<,ZJHV#UQfYdG[0
%!pVmWcaA::%Kn53<d04!:p6$!nt<f)SJq+?.H!2-"ZSDB+MBDiD:h,ZS2qtfa3KS)5R:2RNeE(0i%ciSKhaQsYLLK]rU"9;Q7t-8
%>&ome+g9%Ij:OCSR8le!YJ<+Y#$uT`1^>I?4s'WpRl7AG\hT%e8c8cU8)"&SKUWkOXrQ":!I8WCMG\.upm[Mj]=/p?IC'';@3'L0
%H*7C)Cu$/^KRqn8O?++#(6AHUaMdEFa]WSD$k"^c1tMA>Q3[d-m0=7Fbl&Ce++^HEWTB(;(JIen&`(bm*'o_j3jg63$ZHu0G19:>
%ZSmU$#4h+%$%Id#JDnSGnS`jn_)30LV!M?O>WWAD)MBAG1?YZ71a"DJEW4$71,2A%Qom3`:c+ZU(rjf?c'0QF5!f\HP@\Z0X/k`>
%+T,WB$Ft<qRn3"'m&?FKBjTej8a^,-j1PbR&Q4ksRSINgLpYqE,cL;p'9E4g@CWP_6C+8Q#a-]#"gc9$l;4JsN<O#mh>#K;kVP8W
%]Mglq""jeL3gs6C%I"Q,<%'N`rhRd!&r&RS]*eE93gc^99,s.<*HLg`jHL6tAGE,j,"ADh_k*dujq9pSi2q5)Vp%i5$sFc7af3]X
%?@<s)oEt/t#\_RaXGZ<n']8%`ed8%r;89/*eH4ISpc+\iN:7STTVG%W$N"tCGU53u4D.J.?sOrP1jR&'p%6Hn8>SZtKe:^RW[p(Q
%3EcGjARuV1J]tN>g243I:&EhW@?_2kj*$4@V\eMLcAm'LcOVZ%]@6AHI1,,HYo$opP)[c]?n<:@_!fV(J=r\#q>X4k>l*OZo\aQ[
%l/hI"m\>EXYHE&@h4*i-@@t6@`&%5FgDBGNS_J*Wo8jfNaUuTN^jlFm_0K37KW1,^QmLbNq_8&ErDYq.Ar%o;dg113<&1d4ZP-r(
%8]WU$*0eH/.s^SZFk?W=><Aha+9&:;bak+m$qN5Gdn0?l+&43FanCo?'KP9n?6Welih;_bd?t(VG[>X)BhBA_9m(a>95Tb_3S2:-
%Z1WQ.fm]r9dgLe*g^-BS'8mc2q&J]JF<T,X[0`Zf8;WjPA+/&r5Fu[Ue[5]iX>[qHeOe)//d\5OgjP^V=Z=nXQFb9RX/_)k@I*<*
%ZMdbQV%_*ng*$_oF@[R?)!)0loSpNaNc(pmZJ+5aXD286=MJ48Cd1q-R;Zf&#XiLan3P/<SZm>F?olCulGhP"V)Dg@LW_cfhEU_7
%:N^O:qq;n7'2GiZb9'<CUn$59A@oR\2LVRLOQtPYI9i?9rBjb:WbO>*BTeZOBM8I$Q8$os_@/_'.O8F]en-><ksJaG+k<u7hqs4>
%VlDZH;;q$4q5u#%,UXHOKUg4/CsCeA^;j-@cIK>e6X?2TrLI/@WbUSW$\Q@/OZ66D?n"RG_e(Kt;N!Nc[HLk$MtXi(>^\*0i-?GE
%\IMifI"4hlF01)QAJ/VG']hQk59+'VJ3I^gQK>.,Vj$[ccm7e%ZMcUEBeTMILS"SH+#\f.?[h]`Wc^bSn#JL>]N37E%o0;%@XDMu
%-f`d3>S3:3BZ'9F^3obX77^79#eSJ(\fsXt2<90<+IHhML/_#R`FDl#q=g>9D=K:t#Z.IRaEF?^YZd:nggC<+?o?:YEQ;uD"]nNR
%WO(J*'8_NSDKqVWGYmnlT-e1;)j<^V;qS.tO&Z3`37U/naZJL9d@-jg:rb2oi)pl4$=.CB&;DGiRo4\P)$(jZ5QEheY#Z4%_<Zi[
%HC.9_n@Ih"7-Kn%F+(&\TR.&eV)anu!N+PQ9K6d2_q/'ai([ST4QZ*C:_n]k8cYc\c-%AsMR`l73LWL7UU!!E[%^*qh?*lcB:8bU
%P:U_L/@)hU?+_1R?4G,W<OBu&,$3QmV9bltEC8qo"k-PiaP21sTud*`^TJN+^ll'2P?#fsTZ.n;1$-$Tj9=k%]aZo5$7-.JG/@Xj
%JhWOa%".`cNmU'*qRej:DmGHJ2H.mYNRVLaVS\-#*7cY&WR,XJ8,?f?<'YmJ1sY3*%hLROl;#C<m7l5mePtbK/cdsgRpbrB`!H>*
%b!Mlr_h?R78n%\&1Lgg^"1EZ\G70foLbZ#Y#]ul(-Fo;RX-JMPUD/gO@J!TQ<!K');m&ElTp3Mrg-C$9L,/S;0dJ?eQ]8F$LCS>7
%G#]d&7FtY.A9`DNel(^,NfH@36XMK=,0E@BJuo>L6_.>o2/+;`Mup+l_D7sERr'88Cae,4IH*3]ES1fXP$DKPN4(&icaa/r0s7es
%_F]k_0R5EYd:?D3Yh7"<k:3:?>te6;'NkmOHEd68B3TIB74U:b9orAU18`$sNilgub:s)-XraCA>#aQSi5%S;*I>fG.[k4V,TLDH
%M?@!V&l![E@B_hI`"R;W<-khKb8R?;Zsg%9Trd\?dl`aBjer&65P[>tZl`>Nf;,*cT!_7U0#MY>(d>33$5^iVQ4rd<noAi5?mlRp
%Y7W=>-$KX=7p:)j*_8e`VnAuP+IL"u=:(@+79JqVEN8*1DT#-u$;n/i?5IHoNeDbQDIZ[*bq+m0j?6>5Nmcoq>04@Tg-^cXA<e:c
%-mV,lS"a*H&]Zo_/H\i`j?qM9Lq"6=ZPi@Fk\FV[JsB5<jeZa;BLjoU&?FbHB&84U+Iaq6G'&oSI9'U<E_D)9WK)=MU`;3M.cd93
%W.)LI27'+"*B>VmoV5H<0`i%YP6nn$'EE>%jDjg;fW<FRjU%YX=;@-I3`XT`s&0n=74@N;I2C#1=><%kC82tcM+#`ibM]aFMW7=]
%G"j:OD>bTU#bl;g4*.XR'a%XmR@-[Z*CsDdc&`U-ObKWI\7;`)N@Ig=M(GRA8Nol8KfG'L3"nX]BWXUG-6Q!15/97LB"35Q@ql91
%G4_DH9-Yl0K[.kM?pKW(QN6+8o&8M.Ka)C_e0%dL#\dJm<e7\m=)[K#M`5iH;;s[l%bj1"GBhZYSf>\84N;h)J=c0g*U6V,(q,H1
%U\AkA%dE6GX7^<I6!@]1/d#7JB<aD9ANeq9\mVp(]E2]7L#ahOWa9`!H+-n`7eF;t\%Z#S<?Yp&QFCRuk@-Z<6k3/"]dKk2jR,Ki
%o3Elig<M/,hF;-M@p!Q1?Cc&Y*EAV^]oBY+g8l^2K5RT,]5#"kLot>"'DF*)$t0E^_j6fl$I+-d_m8Tlb:A:^nsK%@-T=P))5-9]
%CkU>!gW8bUp'Ncr%WANgUR!:@2kp4R/b$o)?gA5/WCW=I86MRe&gH)Ji)bW2H)9);PdOts+A&f$4_r3)VraUP]=c*9XEnNQdsY=K
%eD1h[D'4+P`ENb3R)J#E\.3:K]'jF"263Pu1CVc*CAY"!?`POP_lEC`Cn1R+!B;3!&m:>tFk>\(A<<;eI4>FTC^2/#c>/DPQb]25
%\G&Z#,/SdrenDH[%SX^E<2DipX+76oAT"jN+qdD"K^5!A>^O<M4=d+3nt7Qco!2Leo=PDJ``U"$K=uG.2:%r%[N45k0m/CiTl7Ak
%9&A_4EiHUq4n'r%naW&+X;O]/*P-\Sls<iQZZ)U+R.t2<7`r9I02q`L*FeT)S@G9#>SD@\>dlR?kWQ'K[Y6Mb9<:=*^"H<R(\s;<
%!8U^B?e+gb@<8MOE\m%BP?LNHI6RT\d>pZ[[o,Jq!W[5LS_<;OY\?8-E."/EE8>5ikKY6#pkG;!RbGV'A%3Z*LBc*c>31'6+[T&\
%QOa/e/SIr@K<#VCh,_Wj+-XEf0cD_PD%%H3B'h"IRb,gh7)]jKFVOM<;p?7P$?N!+s'qX"e`5>MYa1@+Yc(4'i0?>b`S)r6kD01X
%qnb&q;ql)IYe>u1H.a)NaOj_2$EZ\F3`MfeW:%0c'6V4`m#`faO6`d5%4-o].&PuYD9;#[C<IqYlCo&/&&HJk09EtIZ$C>/$a(jh
%,A_\.P*[fHV,h)'C=!GqWGWbeV&="1K#Y>Sk%3@+mCX\^1U]t33c<%)*Hl^?f8A!5o\qmL21D:??EUQ)SoaubMN6W6'KH/h1<Nn5
%et0HPX!q0dd1fcl@a]_jdVIC)ITX!aRiSd>Y]2ER_UEll6`1a*3JtN4E/-./Rqa$,Ha7Z+/+l-gfu^,4<fmjEEMh^5-7m2!.'T$K
%]3O0>SQ!"R<NBr1,P2)F+_9E"NYMjVT!FKP&m#"k>+W<'J6,mtI?6u<+Mh!Adl;^C6uGb0cg4aZAh*QR8iYA7=NSg%luOef0ft@N
%M\'>uVX"*0)it3DS($"/f7O@PX[_---EIDlRT9sn2@S8Om*0>Tf%<q<5TU>eQ:3kqqe=LTAtQ?;4;V^JI)\?+</nk0S?L$Y1nV#W
%cuR'&m8k1)DMW\a\(iDC:NckRN[>ES$(55"I5n+?-A]3l_7crk=hBZG"kffY3*1fPfP@bFD-V`]j#4(%fkh]Mkb9'&f/Qm$ajc"8
%UUnhCKJ_;n`Z_8,(lBOtcob9\`>UK`TCq90TWkrDaW(UqBU%5L29Sa)cR9+%<m5W9>."VdVf>`cV/7i'!IE3-@44l37/9.$eLE"$
%1a4iMZ-iQG(L:?[N!@SEC/+B]l=%(M8?l)J2>#J/Q-\+G));nuYpW/5_RLbiI)`QVqi!Z_Tdf?6$Wc_G(l,BL*%-*cATa7(\eB-8
%k:N$7it-C`<K.!`#3;pl/<EMAIL><_0\h^lpKXf-WeN55YgS[j<(YnEG./5"nkg5!cWL18D:(^tDhkQdpB[N7?)EDSDh&S@q\
%/ZbM_76Y=]Z*@,8WLM?r:oW!i@]C?/HJ;TnKuG7M+Z)].]f3?*j,)-WqI-R,]F^BMAfUB@>^;=BiAPY4%s_HX>\qV#m"N;Q]Z-\<
%W(IqDf!i@f[?&EFgA!Q`[@t["Dc6sEcmr7'#3>l>NFqP`G0&Xk'S^.UKj"U*-k&\nZB-A'Nn#`m689u7pM/Th$H^?%.K-$)9PN&J
%oGBG4?/+>?J:)a5dbi-';6^_6Gnss$q2Fn?rBmq*DQFf>mF3imnMhs2Nl]_5/@f/6XZ'Z@5V3`u00Q'ZD26iHNLl*XQ<Z-k-<FUl
%o@_Mo9c+DP3./`i7OL9[g?h="YEK7hIP!)!Ulh-f'>hk7V]h,*dPm&fGU#]<cA,Y1(Drm_[^R\%T_cp;n8>hC("VeIO:4!X9[8HY
%WgAPrG@Tsq"H=/X`P6,_?pj>=/AfuQ=in!a1H95TAHMP"og*@Hp%^kOr0tFSGOku@8XE,<HZpZ]VO]bHaVHrr^,ed/JGPA2!=@!*
%JGn4BS3Wt;Lh)jGX#-R)_<Hp%:um'Vk_7NS0]O-1jdQQ,>aY1^ol0)1D,eJhW\N!H<cuK@>ub6cc_>!a[B(=KA[RHF0oTnCgQYTQ
%e]jnbhN/ORXGiieqa4jcccS`=icH+3c"U,g`d,^>(M=t/k%9`W'3S17m?H5(]W:_QPLm'F9LDr)A_AM7C9_kDDd]WR>EVSh[>V@p
%NlB$V6>(FSI%7\_[2Os+U!_k;=NXehB+\d9=L"OL?A\F2Ec/:2isHiB+/eT+EohUAbdgidPPSlm;o]hs>^L]h^,iK&\Pf#"-^+g-
%^KV^e(l`umhMFA\-cO,?,;dt8c:Z`8nVXuHML@[..j(Z%?a`K(Usp3?IZR:5Po_ZZpNift<"!eQ!3MW6Xsld.11h`/EErj0?Rna;
%27qD=5TIJe^P7he+q<%:U"iYV41I(na]797N>)Rc%5L[m?4`lq&0]/#4=f6qbq=_(30k"]p_7"N%Vn;*n60[P5HJ%la^u?0p<g%j
%YHniP9'iM])DHFa1/[1,R1LfE=Ll$KA2](EfjMSr6@&*r%k]>]Fu?,4mne\fNKm'fqh<cg\M:/mM)=8QFTk]"(gV?tg,(lR)NpF6
%Es8nlGnF,pB#RjBZ99W&:5!m7+cJ#HM_0^c(Qb!^.?WpsKOlN0oSa),MaFA\V/p<6&:1M`js]k+o#OM4>m!NQ`nX_*p?6>p/^Ao=
%Ip\fcUJZMALJ*c;k>7IWn0!p7;`<EMAIL>,4U@&![B!*JLlcfdbP<%]cKT4d81Sf-j:unD)e*$m8??:t3ecglDN-H;(+kXb5S\"\
%f]!=Q.`)?'WQ;E`S(%S&'o09XG;)4g`u?SYbJCR9h2M1A]2T)pd>"RZC?DHp9kZ-t1`RBPjS9QX3/oiHZA'A@3)XmVcns<]LisA2
%)Km7B=-X,Z_P%JeFCpM@DP\o.-$Z5@>Wt:(d\r$4g:FH7YQM#ub6l-@WD2]\C#h"lrD5&NKMa;s9n5NIURs+ok_>TUZ[*18-Jmm(
%Vda]7#Fc[?.86p(Qq45eLMEMq*P2o<`]BXOGqLVhI66bk%WP`,O^NPWom-tbFBYl\?ki<mjVpak\QZ_@=qOaXeVYo]"(Uh8CC-F<
%M>L,X$7[2ZR[gMK7G.<0-1>],e7i6-RDDEg(YbuepPh*+[X>FWYT8!NSD3-U"I*,SBAd'*EI7n#/)?<9E`4&SP+TY?<Tc_C.VG2;
%IGtR\OI^W>30k_f:=2O5*P%0bcKAScQhhs<9H12sp4F!'2*+&XcsWtMKUfik<Ct=OD,la,d9g`6l>s!`TKjk&Q+@Y<+B[r1!IWn/
%&f0[qf]\o\LA0Y]Ok#55/n>p%?EEGVjN&7p9KX.r18f>+AX`nY'It]gPkV:hAZK@$Q*&-P@G$fZr4u)B!&Q>uZ<Ub4.'f/lQ<Kd!
%]W7+dlCKf]%p(\<(KZ<Lb)"rfXt8/4DPJ\.V',n0akM"S=BgH:3.`@#i>AE@.W-K'Z#,GTOkb.T*.aUUe[QPcHb59)ASJE1<+=L]
%ZZre;JE^CCn-mZMb@uqk3JH>E7)oV4_IuR]X/e"_7!lF3!pX?$Q:b"B.q)K]q^VE/_ZSD;dp<a<[]m>CBOZtp$3D#i]/+]V(V8C.
%WbuV@nB-R$QIB&@Q]95B<_0dUGH)q]Mn;+bj0ZFYULKu]Ob$PuYj/Yd?DaShR+J*q@r)rKkKLeLO)m5ZeMG'B)CL3gb$@dm7m9V7
%R`KAC%)_I;X3Bi(6u:tD3b`B(%e-pPJMrWj",!.K<G.!A&$'TKKM<Oio!28_HJ+MXfW(%ZXk6bP6PrjfU9q"j5+W&7e^RPW=uuu.
%e^HJ'(RNFS\>-rq<6jC6TrYX2<HcoT0Y#n#H5DKpUmd^RYk2*L0*b<+e]+>_5rEOSF`!6!_HLhWj-1:aAn!Zhe'=p`URZ.i!m9/W
%\%?I'E7``$iSJQ`&9<r/;64HjUeO+i3A[T+BlG=>o#(BL@n)@c)bFku8:8<>dXI(WQR([J<U4cpha6%M^4iopI1'`kqA.L;oIZjr
%oIZjJkZ^U4d'q!3/m!6'I1'a.qA.K`oIZjJkZ^U4d'o+fd'o+fd'k-F.5bj6Tl6Xa9?CkUO>`bM+`0U(6J?t(KsZTKM6)^+,P3R`
%%Hq"e)pl'U[\7X,_n?Ms)Yt56cZohF$Y]_>(=EEZ/Ym7I>=]^[[u`M@D]rsbha6%I^4iopI1'a.qURj3d'o)pTPkuj6f15aJA-`E
%!IY>q)u:?0)Yt6/)Yt6/2Y8T>%Hq#P)pl'u,YXmq(ZGYk/]8+^>A+=A%/Lb!#4sLc%Hq#PgX*D@2kY(4DF!)Ggk<Cq\,h!cE<H%Q
%i\pG2I9<1];Cp8iNKrll`(fH"L`=^M&D,:<&@\?O+`9R&6JR+*KdBNN6Jk,bJA+SjOeDmk&c*:&2Y8T>%Hq#P)pl&*\"R^`DF!)G
%fRU\iYRQ"r+'-`SL`=^M76b>H>DO6F[h(KkD]s!cha1L6ha6%I^4ip'I1'`kqA.K`oIVU#;\]'DdK]=qU^cNl8DDM#O>`bM+`0L%
%_MU$YBpCtcDcgREY!R%7Df(bRGFr>/Of>jrarnGt^Qr_>ARoQ>^c&$lXYM*UZH@uNAX1(7?Wq#7\?sDGHtej[$W3ruq`;.-XQl\V
%PH#eFcr)a3&T1c`8maKQY,jC:iuqW<WmN@uR<I@;f4<[Db*_s7--#R]=Cn.tD-1B#nf_cR/$"U7=&p1NDHUO=^9Yn;C9Y"dLiCNe
%,P%+"-DV*ON!Qno1=163<Z*IHV,bNgLT>=I[*#K3HB>!5WsiaJ[*#&a)ciAg2QS[tM5=3ad;=kjAX05b,?n^p6^Oo1of-Tb9KaW+
%<Q9]o<G!tbbUOjX_Nn26ose1:f(*5f<])Vf/8\L#<Xkj)VX4dL['\Xi$+Huk(m(Ro#",l,b1QB+BpEB[7;:H/.^PS4C6`Mr?njdF
%jF^__elJ3=(<%VP.^,s0B\q/e;pENe\7Lr)]!sA8D3\hUXf:aB.T9&s@[1XTB%&c0<Z/M&Crj)e1+$n?1(FWi`6dH(l6)UJK4n=1
%R?\fg,7($CC]4T:Al!97nI6/"Dj\UN_`cS"nunB&m>"^-[=Oi!s%$h)1QjX68#jYT4<m68K?@*cI5<_gKU$[,f^:?^I$nRB25X$g
%Sp!b7U;Be.6+lU&MX9jV>A&On5ce-XlX^b_RLmW\[F6U9lV&P)X>@7HFesVaZC(MSkt<9fHtAkj[q$S<F[h&@El3i9$!B7<]i`D$
%3b'&)+O#%G-&iR@K:Gb2c7I;mR@Mr!KZ-O:=hAG/N/V]=<mPND@-"e:AnY"7R^BN/c@h?*-Q?\"%;lmR),t(5>si+l#o<+U?(UUP
%Wn1O0At01O36VE(N&/;k(dpOt)3p:R-ehk"-&+=OQCs]2nmSAG_s?sUML&H!]dhc12"jc<NnQ1]0nK*rCbnND)r"%<caK6T6lb&r
%j%Rs!F\+#]fk)GWm6I7[1Qca?6F4-h$gI21JZR5"h=\ZOH%JIcCef:]Lp420#]QHgg1KA=0]$!_/)"5FLm8`aAXj'M3V:$&B7_Ib
%KJ8Y60KDmi=_Xs3>b0s_cINj6(j4QlCiGN!*lglTXf8:(4ffb0mu)<X&.1cGM@(W(;oBL4N*m>(D/bFQZH>.*bHl1X$FOk-gJ"h&
%iu!R*-D=t[(s$5.VGI&'CSl8._TFPG/9A%k=uO8lcua^J(-<[BO[U&^Aj,kAJ8CPY`Y[cC0t$)lfI"h+3RB\=5+GNSdk9BdU7!hS
%I3;*eD2)W69Os/Q#d[g6(=mYs+qN?ZPB0171,lg=Up@&aLoVH1]Ri,:bUsQ'*>Y?:>3]t([`_3:aDkHI/D.%$2r_QA0Zra<j4%':
%8kl'?^PBHII4dFTZKX<J_V-X&%><mB!]$lC)]oiWBr>GSC>*',4[cJaj/5Q$Ke6ihd@f\B\lGRUpC1'de>6>NmTegb20l`r;X83.
%l$:bI%Ge%Iq"lAnWfm/SL2OC=SJ;f0-#B:U.X>H-mCRBQA`dm!Vu;cu[3+f*AL^9[37ngC;6FXnNX8AQfR.QS<sOo_p_?;2a?+bo
%='MiL<G:-1A]s=DWXknG4Wf$!@:^]^COiT;="@Eh+is53Eg<.r^0t8XL0Y'(b15A3!_R-il,aBCWbFCbp.![,otschNh+B/C>(HJ
%Z@8@Ri'4teQIK;.hp>27%&7_H.uUW#9ipnERauhnpGH\h,D*R8TVd-$'IqV>X#AjE8KsVTKZ.Um1\GMN9XaRtd'WA*qBbTF+KEmL
%]W5JHl-gBV6.(KlhGpEdKtL6X`nh3]MFob:W>8P6([ImniPC8`.TX+(rK;H6.!AUEeRRtcl;]]H=[*UYHe(sHIT$lb75mk=9%Xgo
%9rnbsl1tk\WpNh[l;a]!k;4YprB06T!Oi\"PI&?2RB58*G0Xk+j(a/;bZic'VS4UCZL63iYunZOCTZ0*d%%U?)51As"Z!2o.i8)?
%hNh?I-"H)NY9D@`fKuGcH.Ii&YZL:[J_<t'S#bVL%CV`BaL%"J\TT9@TU@+:"@q)8@eKrLjh<iA6?R0!jF\K/'kl<JD\!Ah-Hn*V
%4S"*#%'>Q11.\)C9io1I'jTf'2!mB64Ndi7imkju<V*(!'[X4V7^K$-]X-p^oqYa&\ji%YX3)J[(F%)4hDCTLCs;a(&Rrc4fN,Q2
%CL*]hBtg$)n#TL$'kVc^WgcQLcHTgP10!j:KJ"fNNEJ7,l!ZGD78EQoC/0\kZ^FmRUQqqNc4tWdSL^O;hRQO<aog5EfA'qNRB;^2
%RQsI6(]-aHjXiBRm*0<'$&rr,?73VHNi1GikJsiC)^<-Jmn^na=+m]9<6;I1<D'A-0;#Yb7S32#.rRkO;i6]<olKpSCUja.18Wes
%)L:Y@;uaK@9>e-G26i8/.3<4OLrK[E25@.4&\("US03!^LY<,6ClO\rU_5RlO@h]KaoNeaD(c9Rm#.p6Kk>aHY9?Hng[5Nrpb5dY
%T$hPZ[q!SS_M(+>_6.g2-ma#Dn\A/OgV'RhHXiK[4&>B*TI[;iU_5S7O@iiFqUjI9WH*ZNq/C'G+QJ])6TWN@185Vc)$/oJWi&j"
%]P07&5lSX"=o%5k=(nL8Q8=0kL3t]HH;tR%_q%l!;u>@UI9k!ND<VGQ:t52B-#C0&P*ZVUBdtiX@kP2p]ShD;j?B[.6tog^H^&-K
%SXRuFN!rud5n-4eac!R%Par?N;?uBkl_&:EN=Vm82br1MUL>tDC"Wcm5&961_,[Jb8<b%"_j2ZM)<"0s.WYX$jHl(9l'QW\'tq5"
%c8G09O)<51`]4jP]W@5W15H!llZeIY[_if@nC#?[[D3lQ^.<X"WHHIrL>(G5eE5eUO@Q'j^=Eqm`TqWn]pe6"2BXk9rGe8Ee<<re
%7$^pKA*,J;lAHSpI6^meDV1$nC[Q&8kR`#^Dt>+*K!=%b.Ud*laR\$d0m6`:F`2mR6"*D!/QJ^],'`?!/%,dbpSk3H7H9)Q1Wugq
%Fl0;.).U.%)oL91[5s2:fdS$s+nt#)eLdKj=k7ak&XSM/AQE)3Jt_l4N7%OQ=<b*@En!Z?ObKWME(O;1S_dP_+eG3.J=\TUj]YE4
%J]Y=J,V!c9kZZikh747Z4*:k]EjN[Mj'$3k/8LnT76G`f%uUjTAG$L$.#_\:&"C)OU-[%%K[gfr<cf"@gCbiEQC#"9)8%ANF.KMX
%pGac6<ioM.=@J=<M]C=f)k7QD<5KCu<ob!oN;c*7G_Vgo\oobi2-);jYG<2^^p*!Fe5Zr8@Bl#ACL'#Eb$Kic;[(%ZHh$*@B/.YH
%3DoT&q%R>RW2/&fqf1"'B+tbfHOXn:<6Uk1H`N5t4*9`6YD#^N=7S\Li`r&l^:5em4M+cNjY1e7#pXnZ4CK`&LEG-s1T.Tb9M`\?
%L1M5pW_fl'B+Tp=b)r0OojqHA:)/_&3S'quMWC+&1LGm,VL)6MkJMU\k00Fa=E_5rX&7L4TZ,E&L.YfI:%Di9e-(;ppp+@p9*R0k
%%,JJ7+Zk%j1$YW?G8Sd=60`LHBS6t+NCcfm]pIJU/iRXfHooSu+C45+=RPFqA_!Fs*+[VcSk5?@O=@>WH>UaWA9V0_-bC<ll)9g%
%+ku/MW--X6:q9oRFjqDh:;`Kc>(D,pVnS<qaO=Qt#5r^m>KW1U+M=IgWmfQ.G(dHD+36\nW]2VpKfL)3fAoG(pW\e8<Th![9PoZ8
%"1<9K115[!/ZuR?FaX`#EPYED._s=1ccW6MCAffSh>soZ'uGh;iL#h\&D,#Q*N/#mNn8D;D/JabX2ZYgm,RLe]<qH+FQ^2A8%ED5
%A&]mWF9kZ2i'EJ\cdt4qr7$0YaS7`%4^o%N9SsjFe5_Pq1A^jP@BAdhT1WCRO$r:hX%uYm(OfZAKo[eMJmt4\W2-DNgXZ=0FemWL
%'NJ`48ak;soAUZoaS-s-&sttOkpGN<=m5?8nP>\d"4\RneiVL]>!M>5VOo47V"qEi[Y]K7Y3rH?bF4]'>J2-=IO<%SVBHsEjj@W'
%SR&O!4([P'>lU7b;N4GhT<g5;DTGJZ,LRcc4"PhD<8F4>L'#XK^Re'N9`uNpXQt$!CL;;'.p6LNDpU_nN7W%3J)b1pHa]d.D7:tC
%am+*(XLE?e_GlT5geN=c[?*Zu6$"4Ca_'*$\Zb2W]Tp&`-g+:1Ss+d0Xu*oUkMDfulWI6f>%X=kX`Lds1V[MGb-.r(3[_2Uf*uG@
%_b0Ca88sJS,nA2'S!bs[*p\)^h%6\s^7IjCYkHZt2Ver1RF(kX.V2E,%:_P^XCgSG<ps,(>Q$B7?MSL3%l)8AkAF;bGM9.-Fqhem
%O2>Y@_FZQm6`:_oOn@]!'V0q@*ol+D5IRJ(ootu7F\2M4n&hQn/Ep35l7<pEGNm0AFKM0E41b9Ac0qZ.fk"[*,,t>4XLFc:8KSb0
%q.49)\CG:Rs)r.hDQ92$k?dc0YUF3fFIc#L"KHB8:\7!&F^=sH>bgR5^F72W]C&\bCd61SmkNj/]q4uE0RJ;*0"BS!>9c(KX-'G9
%a,Ouh'WR<YA'8"[Ai9;MB'u#W]QpMq9>]kldnS8m[":'6oj-,'YA7+/MQ&2,DHEqd//u4h6%*6#pT;-6I4Pl/d[F<>cJ1/gXf/5e
%"M^J13(]U&;I"?MXCk[AMTnQT\utJ4EB.C.eto1R?*?c]r+X0!Nu@]Wp!eJGg&"-Yg!0NL)S)PGNEj0Orq:")nr(g9^3t;*cLIU@
%n)3ckh1(7):BU";@@e4j4%1cA#TnN$5</K&i;3$>)gQUNn*LX5Hua1S]tZ\!St6oDm[kOAS/e/Lq>TLGd=:W"I;ATG.!,.PK<sn+
%l$'nQ35<-*o\5;;51?X+WV`H:E\?b,q>!TdgtBNMPPtA3NfWm8qUdI_s*jYpe*c's]0H4Br\-S)5(E=nkioRScd#</kiD?u^H"N*
%Ed<%U4kKT1I?d$chgJOTo<WrBbltg#q;%6u(\@ZVr9Vc,_s<rnbItBGcCaKJO"OdE4l:[0E-Ca4rc7c'jjF(uXd=IHj0t'[:/6ha
%]KE]KIXJ)TIQFt/i@aILXrm!orq^%%Hi</4]DGJqrIaOh+)F'^1>Y)Q\:3ZQ,(B%'q2;SSp0!]dnVcA'?`]mM[eT:Y5FMQO`SpFR
%CR/B>g!Ai,rpeZ(<Lu.aIJj$$jK%,8h:'9bj(IuUZ):aEp2AeL3PR[9ffd%*TA&<d"l"f8#(H-grpX/>n^m@H31!L7)u;gtdQIK^
%M=`qYjf=uo>E+uk]'M27c0KBDgU"5=e,@ZH52=h@45JL0m]_n#D"\FrhYch@X_Lh$2<'8[Dg'6fb"5mQCW0V&+)fFDD:!%C=L3I_
%s,6hpk8u;ZU$DH!YE+to*.MuuMW!sZn/E-!RTQ\?2L1V0s5k7LrkTuV6g)iK2(732h]LpoZ\"M6]DZMlG?d$icMt;L=9!\#05&VS
%.DU.O\q2=-n+FH+FLf';j/r(QDAV!hr\_B:=SDQl;>nc9I!L%k"0@@6L"''J]WTdGn[j'nI<tkZaaUrO4T+ZSo=pgSPN[_p4ubQJ
%EI*"UE4t0-EIW"Lrg3GFI/'1VLD=CdiS0-n<TU,#5!D3EO</i=pgi6K:r%I!*1uWn#fU`PGgL+&IJ[YQY7D,XHHFJS*F'U,euq,!
%4*kmoIFIeo@^pd[jP]V*VK^mnWloVC51J4@Wh81L+2i6kfRnh:Mn)nLU@Z9Lled&]73=^OaaFr:W;k(kVto5`UV'Beou$^<S,_m=
%cha,6H05Dp0RqKrIh-7BeYgI>-[8!Dm7ojZI!U3drY5[G^)>.)I=D-O*(i`mRJQAmGJRQmG7Q4eCk"m]gR/h,p>sEqSc-P,m-*`p
%Zh7Cllq_T3I<pqB3\0Tgj13:qnG41#amT]lO)YoMG>nAbA.RRiQbb'H0BVQN]@lU`KCc<R6b?^nmVr]1kNd.0cCHbp)QDiK`Q>s*
%r5_sGhfK%g'J&5?I_l-P>pjVc'$;:!6Oc5#-3]oc2rDrC0G$KQM2r:^rpC;89+U4e^&,^Vo'*pO^](le$N/P)[/8>P\D>QYk-%"3
%9D&/N,8n#%$\#XUBDK6@<c*n$5I[1u6^5W5HN``VX)Ae1U+Q;043XX/]DRu\kg@LWj@e(\XV::L8gjcbBYfZloST/QHEc\F;Td7L
%^V(r)P&>-ZG@#GOc*PN915B=(e1-&9"MFuh06*S;8Q[nTe;!36Ld%5QY@X;BO7b$ZC-V^WMlZfaW4pU0gFL%'mr$lYKC"a!]LU,9
%R<0OiegUFWTE<__]X#)j^B`G2kJ%7aHeVqT$Dit.]5t[-hEJ<gf$EU&nL8eS^'&L<Dt<KQY--[1=bCklkHjK4n$'0J3hh)E&Jb9g
%O!n<,]FOp_ik0jV8%)OQE;p+ic7@UEAQA\jp,fV$rq:+,maq?BO,o;\iM>Beh*/PeEc#t.49P3)HLsmshEA4-b\Q8&GX"u\d'K&4
%-bUL3A&b8sjk#P724^5;o(S[l_b]m`(![3A]cK>oSg2Xr<UF^bb2A>0Nc]L5McZp]rock!F+jKrAtsZHS&!pc>@o`V0^Fu7+X'U-
%N"XirgYsc"DMquZm6S'@Uj:*R/GJo&f^IBQJ)dTaCKq/:Dr1G)J+u>[__4m*g@"X-R,DV2m6pTL5Q#bC?@SI!k'*2"ht)m'K5>g_
%VoAO#HK>C83aNdi]f\9dqVqGDFFB4n?ep#C%b49Tmq0)5oosZDGB8(A%eXu?m[)_aUK<3U-i4F8cdZ5R(2d.7n]e7%J$QgOS@<`j
%)tb+gEI@K@r)e[K#FFW*#M8k!hq<L)\[UQ6KtV\LG^Y$T?a0g%>h"7:$TgNO>$cZk<R1'%EY^P3>uP6#4Ij)EI=-K`]['YTIJ:S2
%dJ<%s?h^$Aq.)\FjtU(Am!$!LDUF>+[aJq1F7frJrB2`O5DF"p2oOP4o?4K=jd!LSiqbP!jkFRWH1uW5MJ!)@Qh/B*qeu2;)QRtF
%MJ4Nip8G.HU/JI9*uO"nQ_uK8ELqe9DOWOKSQe=L><ch[Mf%V@o7Nh0^\jlRrhroQ)e^:,XL*J8S)+TEffPP`(H\,IhtJ6$jj<iV
%WoMZiq$3(TrNL1P3]srhPeFu8b<lP]4%5d4]NAj<(C/!IVUP+(YP4C8XlT<Rj^4L/T:MKlDKP]%5!$4OME7A?-8Q1[`PLLRpZ$b?
%c2#iKX/d$<]=X0er3SomZph`%j.29?M<X_"%*?DE2u1Q?>?XSUHPc9pE^9.fR^B&<j54Q,J+E7!UP"L#e)qP"Tg+$IG.li8Su;&1
%][%Sb5'h3hIE]t6_>*6-)]c_HhE5'5Jp4A<"qP06p^TCbULX2`mAH"0pgEFk(41^Aro3,4J,WOLa7IV@q!6u#?c@B_n_N/J?X:Fa
%cdVibs/u\-pHPKIk0<JnJ6Qin4/tiCgAO*Oc<o#M)kHdFT3Ug!IckbOGJ$*M5C&6+;2^)K4bu+GEa@jbGB(LBk9Mdm:l8hOMeE1_
%T:_G).p[2irpc<0o[AtMorbpQ#3S2iB"`\]VrTPMc\='ss.97^V_.n!bjX[!k!+/Ns',Jj+6jhq-kGuZ(RInQ=7#JS7pW+?r\7Jh
%qHL&N^H#nKk[%W&mX/I>dk!_NBD;GU\#$"CV\^`B[WJ9qifbI15JIeh[b2,HNp.q_rcrp=ZT]>@loY0YfCZWdFoD6!loP=D*Ge@>
%^PDdtD`iV5hE<s#69AKp,P'pRA:K$@BA9"?HR0ilb:e><02E'l]bl(k[H-fJkaXj/A(LephtnI&H/Sml=1N<7+sR$U^)qOm*rg!E
%]8qCus7o1\^]4;`kk(-Ia^MdNXhK!:B_QuUr:kSFWqF7\9Cl>WYPu)>]Cm:pqT5qrmeusR8&*3SIGdHTWSc!)/^eE]Tj\,MnA-rm
%IZc=R\f_UDo"HeQ8\.FG+6VcZleARV^TeSDiKcsF]4&FgZ<i^;P)6q`P4[UP+`f5&)Wl@kFas%a\K@iW='t&am9Kt\q)mY-.,LdW
%i3(jro8=20dAOh&qr]RPO]gJ#'"o\H9OTP18]RX@H!U#q]r[i=MY!32h1U^^QkD+C@@TgiOVW\KM)KrWhD7`CA!i2G/i54r^Tf`B
%D%c+dJ_#n-8+!X#*('kShHD\85Z<`Ho:6<[>cYE.@;9,Rpr_GJ`lK.d_`.qR45Z067;s4X8S?)#ZQ,&"RlhZ>9&^YD%>_/_<i4`C
%S.%C^9*/!8?JrH+R"4o)^3rBGQ+b&TBu5[]37%M1e&b2Ea9pOe1uHsJ+1nm;Pnr9=iH7_tnOiJ#n#XubK14`saIAgODXRQ!A^!tA
%C2:QYN\>YJW,.]D48`tnF<Af?h=imHRY/*EP\tGLY9?Nm_0ScOkPA^sFh_PhTm-Etg^6s/s!+#WMe@hWJ,f*7FDl0Qa.b&?"n>21
%O4^g00.`RiPOm7UZe_PN06SEX>^gh'qeXha5qVW[>ApWG*:ut7e63+=Sb'`8Vu*gdG@oYHe/'GSlU@S@JRfr>4po'J%mSlW^UVpE
%G]<B<Mh<\G^-'/TEM7anX!4Wu_fPJar8R<qdlKHK^08ZS&$EWE=j.q?MZ^YO<P0,L(['ED@+N>Y/rh"jo7b\?p\'O2oA(c!=0i*G
%])43FQX#H&Bk,lA>2cf/\])dX4MRC&Kj\T>meZFZP8BY$,7"UuNR*?Vi0QWT\Na9kN]_M/;g3G.I/;iuT_s<0H)QT%o&\lhEdr9-
%mJP7TZ<`aH=,"nOH=VYSI!'l^CZUuJcPA-Qj3qA+SKE<'ro/5VrB(V)^TfY?mE<r=QhF2)mCoR[Hm?8W?d?:"Vu(D8GOHRn?QVBl
%4up@&4=kn#@kQF]p#C\2Y7Ie-Q>X;.ViUOAa"L3N9kEa\(TPR<d#tLG+jh[hqfuOX*NjU\1>?#kf1MM'dJiEVSsL;6l94gI/jK1,
%`V%4*i@k"(G([JXErPF`jHKP]Mj(44FY7[J-LHjX3WAUFp$WA_:UXV[H@A_@q=*dpb6)&$[pH`IqU]*So"Cr>bPh>$EkdpZR+LDH
%"8hJ(5BpgBLiMk;]QmlR2Fl2fo:'rr5(@SKD"kd!dJfX2s**orYniA1Q;?Wfi*m5CQ#Q,<ro*j7lZ^+S2Z271c_BgO_l9)B3g;DL
%s6kjJR6A.mpL!Ab2J/K6I?DQu?4eR*BD^MMmd3'#I!RKI'%Q@ElKo,=HZ=(8oB1==h<$G\^YN"7>NK5<;u<F4[iVEW_k6?mIXQ/u
%^M.T?rpk\cY42,(OOha,G^amV,."b(\piX;Tc0(qDpFkCkL\>KVi-t.>LZ_+3gbu-h;#gO=#[R#FXm($p>jCdo(9WQmsb6"7j%.?
%c.<EXOJ!_\D9'Ehan[cb2a;`)?KGD+ptuk,*eH#2NQ]ZJp1gcA^3s#[\3?Ilb+<ei3O`qnNNH:8DrdIMT0Br88%*!fJhP^-K6`<F
%*IkDelcZ2TIsa.iAZIV7rSU32P-5d1I=9o;chAnfTR^j,!Tg2mK)-Wunu_"UZ-/cRGkT,PajE5cW\&Hm^CSU":L;7,efR)%ZJEqa
%K"8DY#9TRFI(LHZG1O@uiaPsGjF@u/-sQ^bs8:g@j#g%;itG<h"SBIok:iEu@CuI2@__fa\R`:ep`4i$XNo:=r1USYIb1Sh;[Mmc
%];o4[1k4\TF_(d4rHHEkHZS"\)S]j%c@\8Ue/Mb.>nYq:qO%f/?!hbj=2*a$h<*LAfrs3)=MNouGqslI?\Rt#Yi5KXXLFgM[n;[]
%\TRLW/K_qfqY]?WE9hQt*t>T(^3>$OH>n:fa0G)]FE"M034SK@2q,=kVcBQP%c7,A:Z)!Hn^7%$?ggDo>A7Ia%CeAWSCOE<<P"dr
%d?D1!RD4"@o]#`%a8;'aX3CsYh/6)?.]rOW(&RZOWW2;[fl^T.S,;OMF"peLnTV9GNcOa`o#pm`Yo*N;NF\BGFm31/$C3'kamk'I
%n@%2V2Uk(\@B76A(3MTPK!RAK(H]X<XA_i#P?erhSgr]Y>5_/6oqBn)n6A'chos]i0BY'pjpi,1B)RodglUrR]pCF<h0]Mi3'5p,
%.b;AFYY4$PfDE`!0qR11X3[>um3qR-&)#4%%_mEL?i$ot-aYq>>Y^aRe/pmNn2GB!g/cM]f6hXcE]-.$J$.konZs%`q*NKa`;V+&
%]V;l7cQ,264up400?k=,[J\p)fmN5dgY(-??QWNZrNalHl=[l7H59jmh=0?*;$aQf=7+$e^8mhG_gpe9dSk4RcC5*)j?%HbrV3F`
%`8A>7BCdQtQ\M*%++>NUV)pN^*ds<;ieM`2^V(l[2V,pS:#0MpggtU-mXtPq2hh;,qX3*i4T"0+jO2>k(K^5(q'4Pk?L&=(O/i@C
%lGZ%DD_u[1WIKhK1KWe\<hFlCV0qsT?[`.Lq[M3"YJ_.F%l<qR;t5k&f]6u>1u?ogrHE-WG\MN=\_I&\OgHLTl(otXXuH/<2!]F8
%l2Q+>/Pbhe)b0kU#6%'X'mFDb55Cse?G>sdfWJksREP9qj3-I9O3d7D`:]1?1eImoa/A)R/e@];`#es$2TgN6$f[B-]H<<)!ajfV
%?Q>^)Dgf3f)>3#SMA>&-^2=j6I;o#Drn<<F+1uZmB!-XUO(:Xt0ASM2c9Ds%RJL(Tcsc;PIOm;8-*@(Hi:T/M%a)[RMUoIgkHAkl
%GFS6L&)NCK*XhJrjP]NfUKBb[UM/W"Is(P4?2!R4n$XQ=QRhXIE.5S"Gl$1O\(dJQ7o?,SMsKD%`IRoc.Q1YmIh_Q8h-V6XVX"Ig
%B)9?/q,`2ChJ#\b:TN\f*@A2HJQ?2YhZpRdEi`d'^&>dLpr4*,--09kG.fKCY&,k,^O,^se'4ej7!X%Ea%McS]g%Vc%'ZCb[.OJ,
%_V\c#1;(t1*ZA:A*D5,pc7'L>'uO0S+#S]JoW^G)5A?XO^4u!GSJT'#me@4ibk%mq$ok:NR4+FFc\K91$hI6U_K<t)I&s!V:CASt
%f/#[\[HbXF1FnOAd[6BG\88m)=:U=G(LNb_p#)6_50q"=iW8M+>$c^=o;@1`?Ti>P\[hJO=8R9;_p>n^6Sq$=H>qjSiH;p6WO)Pi
%i4ZT,FBd(>F7J23b8"ddL=`ltdB,p1!E+8k^#g1]:@$-aJ)c10aLQ1'oXCLS(N$Ze[D?dAcddZeX"/_kZC9)MNqSh:WfAa%+"-q=
%DB0@8pgW*hNmGF!^\]Rn9;qoU5<HBf\pL^6k8/>lpp.2Jh4Z&cff.K,O$'%)\!;5`H1XA1S<JS`#@HB]_<3K=WlnTs-Y6R*T9PnZ
%m'!Iard=_3>%5-Op<'EUaa;2ukWF2_Z8C@db=DF_A?2g!F19Y]/iU%Jger]K!?Ic($%LLuHN.:EcILn4g!ra(T"24D3489Q2g#X?
%fPJpRjb(,fS%Hi@bRqakDL:XG'\dqLT3PZok[PDF99AK13IPUe:_F(12n#J9#^cSZ*,g/o=9)MX>M&J6S6m8L\>>3@C+%'29kKW0
%G^fC@>HdXf?HJ&@k^]!olH:YTpsp1mS-\lGfmb_PntY$IDg1YTcA,MM(L6ZHV0e52\n-]Mn]e>oNU];,\,YB_]AMR^r,6<23DpbI
%Y0Dl"^3XdMPOC.;b'^p^9j0Y1i<KT<-$?nE:G27s?(>9ZG4s(RVmB9NlVa0q^UiTH.OQa+cO`Wqqp'P;.Cd%0FBLd&`S'q>6dKZ+
%K]JPrPDdU;cIYn<olF,C?ZM30o_hED!"8;]p3r7.B&7$n$7Ahn[lf$Eo1nsp6h%>QZWTL(UuU6rp*t%I^;nj\2I\G6@8=Gr+djZI
%qlAa1cX&N2ng%Of?6**O$R@t39O/kr>FEa!]Cj\Z=3lo1:Cf'q/JAfjb<+o5o0]s[FZ7NhgnAWS(O9g\nh2bfj?.P==t>ZsK9L_4
%m7SP.7ei<dR^]>>/FVRD>?$B[p<K_tg%$&kHKaq`E`UUqS/mnu\ulhkdVJ9Qf8.>n#DS."^OEn(L/Zptki(d8?emI<k9jhX*,#/g
%^#)"uqI''prNl=jGp%XscgEsprPq\tHY`@u\7nOWgZk3@r>8^aE-@NLKl;7<d=6%I@C=S?f_[2THulu?TrdZf^W[k&@OQKUI^?*7
%ls=H)\TNUtgi`jda0*-;^6fG#ZeGs/H1#^ec<aiER?d[C7qM_UMQL+5E*b$cIe`$JT<q_YqWX?SgO=4Og"$+Frp5:5@TdnsD3LZE
%j>NdWg72VQRAWb-kfSRcrKt4%\D##&LD7)%N6XXI444u+>GI)O/'1%"+TV9PU8D#,C>Zu[IHQseB6SWj2?0`U[ndn*^6a)t[kBB-
%#FF3$*aCFB4urUSJ+@-"q8L(LS!Pg_^&)8bnm@GAs7mc:o0;7s%mU'doZQt=IW>1a7o-npk[-Q5^\l@&It%3?p[`l-^]*<or5ZkX
%kB]1<n"\5.3e%"I(XM"9\+`,"q!2;eqX^?Zs4r*)PN00Yk5.R=IjiH4PbC&,Ue&XT?h3'#l-;H`lPBOMM(7+?B4G:l-DHq3J]pOE
%Q\bi9!UcY5@32<M_d"&e+=jZV[J8)>5PTS=.Xj"J8T8*"_qUWupD-tP@5#Ocab6H0T-\W:*(sCbNAl/22n3ffdB,I"UbhKuKPk@)
%#fY_=;6O5OT*4-?"Pl[:R6H31QN\!491!>U#?U=!]WD2m8Z,qdJf>6%AZ6sBRfX&%]EQqm9rotT!5(apZAQEV]7:82M4SI=g%bbh
%lgWK'%\kD]`L:]JreRXL/]VLGX7o'dZ>o7!=[>^)H]`ffGODt?a#1`.KpA.gVfOR'/I-YLWHPl*2-'?H5i8`93jo2Rb1rDj/(klt
%7p8:VU]GaO!F2p>91he)GtP>aK#FlZ<rVp8EO;:s*1'e[\=0W":8sUV%P2g$r,.^*nE9*:r.>Ce9j"#,-M2-2og7,+gDO\FC4Wt9
%#H/1l#NZiM<G/`Nau?AJAP3T'W&OcQdZp`G49CLIY/UUU>O[hJ\c#is.asbP<WcA,8`]Fq:46B(DBc+@d./ID/iT"R_5TABC9M.]
%U7PnR&9!Z98Hi&5js9=(MjkL5dRB2>:)\sh,Se8JI#a/X@M9eaF'=-iD7@VOrrb6J/;?ZuBAV"`4qj0\hY9:>>"mnP./^4eodd@A
%N*"p"RN)hResepJN\gU96e4KCP7OKCWM2F#]Jh:o[a'Fef=:/\b8hll9fiA9GoPYhns6\EGs628e'`N)-Lm)JK8sCiB)qDU6)reG
%,qc7J4=QD*+!+3;a-,BP>El\%9.P$H+8okT_kR/R0`Qc.h4tRfK&IZT9N,^8;jn*pml>r-Z-3r"&SN$*mHIb-GG0/r4@.AI^oglX
%<&)&;eUr'Hc#jSRJ%2@oMoRL=K`E4jcut$Obi7gi0rnA)?oA?6@s/o<Yp3.WTbf\@6kXR[[N3So<Y(QF)tIK`fcjq_7_aNM]':4*
%O*tmsYZO=J@TP$hjFs47[.S1OkZaUq=@Y?'EF*E&j2o+T'B49U'/_+j"m]84$V_tp9N]6\jKWAT;oE.)a9%:g8K<qH*X,2YX92.=
%S8FWo3lS@grRf*;`M40kqJHq3)qbHu+bmV6P(QD;jfu20Wp)WueVR<ICY[JVXEF$4f_ZesKP\6%3fisWgFbCcZ!M8%W)U$9lY+2u
%dqB4qJm8f?3/A9W$arj`U$,9]Vb-K5?+j4[Ju(srMtJL]/IpJ!Ydg"$\ckmJ#`J.\+'OKu-tK$nr!os9Yj5]:+r$P"9.7==>,';>
%fFgEX:R8r);RL3:/7V9GdT]AIRjqU!dl_->Cs./urS"2Qg&rPJQ*qIOTSa10iuMMj&#C_uWX]=$XN-po=;'&Dc.,t[XHr/)Ap#f4
%V:E6t2m'eDQa2cr>HkCC)Rg'cdD["FL?@'3Ac6LH$CRVG<@'J;Y.9Bk9AM@5XfWg3CYS,t>'o8V@NC_'BBsA2h-&?/Fq*)+pQ`\F
%H>uI`S#$skgRR-=_M7fK9,eJcY&,^jhq>s9i+.aXO-hMmOA9#;Q@-,'hILI<mj2FYqL_@D2p`u+7R:h12U?$&[a";^ZQ65@^E!^<
%,(P[/d445Dps%L"PD.E@c<VaO*\KFHG+"_!mE&pbC!UE:DL7ZCa6d`e[:SfF2^eKE^#JDE_6@H7c6k[Y2B=L^k@Rn@p`6uAKAeC*
%8$GC)*:]<T!c/iJY*!kf>FW=$k3th[n)`W_0d9YW@khITNkNm54)R6+e*4A!I88ZNcuLn$T&1D8LUp5@HXNB.@WgreFe^[(F6,GD
%qmU.n3To,+F-fN1SC^It#,@kJ)So\X\_(qB+r>>if##t6C8$qD%q;?8[]__sh<nt9RV%R?f2a4Xf2^+"geN,QE<d,$aj'!h;0T*k
%n[ju4jl.og2cFoNPA%O_#;#1I7;J!Eqm7)?O'Od)*l"I[ol*(UjGB_#N@.0LAtH<g0_2R]Zb?\*a'I#'ea%I_LHu/h],*W#&e5"+
%Pd66fAOt&8JlgXbc$\i9O";)/^<R+8r+`5#J;r/<Yf^9E9,G6LhkB(10A=QQS6W/PG-=;\-UV)1m`(`nca'`kHp2lkp[LPAjHBqK
%VD#QP+++O+9DaJ"B&:#e=-gSTigoT!E[U>=*94.c^-"_?rbTL&oJ^f!Rt$[)Vu5;nIN,M%k]"==WJgqreB.t.[o>Rg$sr26HTfgg
%!'%98;lMu.>V7I!cunZ/Lp81aaBVA=U9RO``4/LI`#^TZm:fW3YT_,?4$Z98mNn8B&UC[2i$i\?-4]*#<GGA(dn6@5L_H\\#B`[N
%'E'i>o(\EuFB7M&;pO[(h+DfcF,YH:-eMju0(Et:mYSA3^!824`mQCNJ.31%-?(:EjrI-mNE_d9a*)$+D;0cTd'a=?D`h*%D"8?"
%R7Pu`HD'J6-b]c#T"H,9o6B=5-]l4[VoaK>=0#:*gj&/=E[S-Zi&tA#j$i]AK-OX,T5F+X.(/$3kC\Q)9]9BC0/eN'.WlH2$(2C^
%US3:Db/W&-[2>f0C6gH.ZD2CRbN^i6%VI]Xq*N^QoW6d.?r41;+FM.k_=h\j%'F=W"gA<dZJ.a&8L1^*U,Xg"RaTB5VTk&UF<(t<
%0]B=CiHShF$C@'BFEf'Qh;Qi\I2MLng5/AM1Nc1pUZEaVB\9VQ@tphThIQ="N@<gn[#jsVUP&;'KL#1>3]^_6?Xa\,(='TqnX(iG
%7[dD8SW>!M*[i?;EJ^TanZ'cm`M^>m;8W1CNC8gg.nQ;?(K,>%S4dY\BIQ$\S$af5D`]t+,gLF;:0Qc>H%H,V$%KcY-R:/X9iS=J
%9O^1\2ABB]2HBN]iM'k?I_X<H/g]U=bZE>F,J$ca_;eS^>te-tSm>QA2V/`s'Ng5r@CE.Nj5T`p`.IDJP4].[AD1=>Cm$sY,bI3X
%mW46sZ\aoE3c@ouV&E(q4PN<Y>K]LbP2!ZFf/o0aH7^Ji)>B+?A>1k1SuL;,^!*c:Me686c6XETml.O[=sARV\[pp._RZ4q$n/X?
%?*<IW4Lo$uZd-0SXHARHj@$^_?W$X,*5W%B^K;6s:@/aaXL1mWKtBt)T@ZCuV,'q,NofCTT%&b+6nLp[;EECBPm5k>[>ae@\$DYI
%/ENViZ3*#H3R5/cY!@iB`99'cBaB8lAQPgCWe$S5?;1VRj(c?m51VD\(U".Q=L_B=\r*o7FZusT-4O`rmUUtCpM.?G5M&5k#2>3#
%NVkQ#Dp06e]'5N]$t.PK(@ukD?*IP%kBU$DEN.g+<Q&1:emai8[.0iLD_&Nt\D?II_E0YZc/4/s>jT/d%3FAMW]SfUk8-oA9/$.4
%Bn%,i\g%liS$9*Q]A<!3g;A5c#@Q!g'nYIP]j/hT%^dnGB1AE!?c:gar>"e#&,c+33_6kRIP>IfnY)FDhhHtN\Jb*&f?)+"*d:,Z
%Gb[9FO-clA>ImcTLFT.K\1qib`b3W-1(alF#%06d?HCFQOA1e5S9M60Q7'Ur\ncEC1F>JnZVojiSo#.7b+)4t-M(]QFdm=3EI'oL
%FF$fc3X1Meja\rInijE3"a5meG&ojNR'&NX.bB<De\7,tM<UHZ:"&?B(Z2?UMf`JS3k!L36]q/gAUPP8_eN?,[Vt5CB5-b-]6Lis
%g\!!IZZiuKk?`:oh>JTa6k3P[Im%=?FRu^V8&IiS-sD\.l<nj!i)g2f3=3SOHYTEJQ8;#-BBQe>f/O:'I.H:*cc,dq=o'EB3O@8&
%^##"NIatX:qB,[^%.1u]gs2]+$Qd=PH0--VH0.gcdAWjSn:`FkWpK6Gb::FmEu;Y,oge/J@<doZDPQp!RV@P>5PTE_e&'@E^Y)qr
%rlO,2q?fi%5tLH'DfEHYo2:Qnhmj8i3upigc^T*+g^:-oIk4e'\4j]VMN//fDFU.i;9PH&*CZS?5::Zui*qaY1sG9+-<;Ak`Q$G,
%.VB41lcEh3M$7s+B0LdY9mBC/nq*EC(b7mWPdRgd'"3%`n+Xc<hW#o2a\i/T-6C<<kBj>T<F2KD\TjL0<Pd`J3`5g(kMd/Ygp@M-
%nqq#io'a]$ZL5iY]?^jsJ%5s&`H@#-3U9C<(gBpFDk0V<7[XpXnX%aA]K#'nj\AmW-2`bk\XMgfHh$Splc"Jmrsi,Cp7(2::?R-:
%d[)j3'Q;.,fAtnlY8*SNbA1S/^QU;h)*3ugR6/C*+<Ar<!MM0'rh=lB=,mra3F]9i=PKXOcaq_^BT8lJ^d_S@n;027bHAdH)2,ki
%ST`q3_)m<K`k%J'h)s(S`ju;:UsTBQX`/ES>O&:]+u&$&/))bfl=[!befJo/c?T(1jf4=Sm!q/=at2LL!uG_EimN;8gu4BC-ig-_
%Ok%YI2+To%:6`u!!AqhYB7,miVSQ7V0EO]jo+@>84-R.$BrC^!S[6?Ml*.RpH:8Up3gFfBZUXc/ToXsJdnYuC>C20DFDhCiMK0ag
%1eWU>EofG`<E.hb3M-?!`\^2I4_n@1N5?&6_r&e%%0n!I&J\7KYT_#P1kT]8W"q:o_G)ngf2$YATh^S^;e^d?(4euNWg?UmMhjZt
%)$u(qlTu[ro-#i]=-VB")c02Q4@JBr73Glu$EQ*p:`525aoJC[Xth]d41CeP2._ato0E#FN^cd5b`1rME(V!9V:os1PtJJV?E-jZ
%KRh#8g/cR6qQ@8]Z.hW=3d>#Yg>>#l$![EiFZ3M$TZjJ%'WDen*P(^=2:U-X"=XF2l@?el1=OBZnah$/B7fqk$s?/0=(`"Fd,W+,
%K@p4&c.B+f<I\)8DHo^>-Ad!.k0fJG.2*RW#lZ*+PtiaMN=YPUm:qbuRHs7H2Et<mV#%^r>A"A1_%f5tjBkJ#<HY?u1X5#@\e@`>
%nue"0YXb+PcX$[3Z(Zpu\jdb+a<dZKL.%C:qh?#2frSOPYPF:m"a$dPe^H-$(&N#X9?]0n6"\/9/MZ(h;'c+W%S:rh7@I$3gbf<@
%qGb]Xq&ffF;drq:U6=<aB7O&DN0]ieH*DSh/FUW*'6*'H-BFIt.,lDIn3JFqo]7c&X=c"5Khc!Q7(KGae?`MU=jXDU5$`.qOQH"'
%jumeK]'+a57Y@W4S0rS&9QsBWQr*pcLeH2'bag1XWoXbLGb^QGBrY!I!B+AV:qI6t**)I$5\U]\fE-fBM<\VmEniYt8(ipp0^\'`
%F!+LQS5NpFoU+s07k"uVSIAk.HMg[L?/<C$rYa,P'aF1HmjKN'V],@dKQnKZ>]TOFL4kG]%G]SKFT_j*r=$1O49"aA%KT[?o.om(
%RX;iSRQRP*g*?_6=`+9WDqXVnKVQpn@ki"(T#/c]YY%X3AlClFXKla9'_Q(sg$A<Hq7'_eAdCW.=6JhHTbQ)c1\&IE^m@sd$N*g-
%W/a8[#A01X<K*<)[2@6d8i=b.>:\ndqSV\:aU2DXM\G:Y1a9;>96/f0aZ;:./1/3OP:_>i;M9NdTZj<ReJqS@J'*:65=$V03=.OC
%bG<aTJ?4Bb8mR$)dS_Q%K>f!%no:M45m$=\<HHQnrkRh$o27h4MASZp;+(kVWZhq6VdWO.:?+Wo(_#`8],ld(Xob8c)Iud#*@W3k
%e=bjOJC#m=5@c1Yb&PLrpsd_ZO`=>(N]6GFU5!d-Si5Hb6Z^X:0;Y=c"E9G6_^BrT>((EVEhX>1AmrL;&&jm.&?-Io7A<TL+V*uP
%C0Q!b\O5:Bs1[k?Zucd@eZf5\6R8:07g))F48XoL.V&X"oI`:'L^;f_f1Et(D"Qj\8jid3]0e7bHOr_1q[1t@GtY+$PLiHK*EpeG
%I"?BFr$P+3:Y32P#3!S_:=(2r=WW\'=$H1sOJeNV#\^D6NhJX$+KK_XKCqr`0k<&(qq=A<_B%"7/g6l/./[BTht=_cHPh3^+oVa/
%0`qKg#[a:jiG?o.84I9i5!\Gk>8S95_Np8nObS#5jPoA@20_>8W&IL1:BWfl&Ok]0\8bB;3rBKK3\9rkVCT4N`4:od#D.En_<NO/
%c1(d'Tt>[N'TJ)AaF$NcM!bMO6FZ\;i6Lmu_`nC$bF1o\11Wb0dU&rt(Klh$B9+-(1o]'ImK"&CPI)\B!"l;j1F5l,Z9>8uXu0!;
%7$Oq=]EtuG(-ZpN-$h8U/LKQ,,:D2n>?*.*-Fn%AJ3M^DM\<MQ@BSd&+IU$mS.iUsd'F1,0U!u5G9Sb)8%TlaWMH+g"<Vs9BbCO#
%":M$2!eYbCo"p-c`Z%ZS;T0$!C!'V9KK_Len,Ba?M/.9\5p[2\C`b!iHtZE]8:9$YqC^12_$$`\qTr<m>`t>l$^dj:4DNuiC]i%O
%<(\ppUdBA_*p#@8&`<BRcoAPrs"qXF1RnCkao>E[5%=+j-2I9D+*C>NTnhQRHiqgESO4Fg/h<H3mpYJhf*m;\h.EV?!D\K2Hp-Z7
%C5RA,I8Q8)mJjB#Vsg%frolNN`\b$%;I(S!F%u]LpD>mj6,f#4]*"EW(Y;CN5..s8Mkt_SJH&A%5=cu9Am+m9g$ImV77mdDlh6=s
%$9q799QHFsgW";8"/a`A.^A=ci:/!Vb5C`bh.oT'QaS&3@G`LrRN20_.LXE4Em6(b<[n:pPeFH!cn$,3%h=3Kgr:WaQQ7/6"S5L3
%^\$45-Pm.&Z-Z>(mMcIA/gf!N"E&&f!0;.mAo9@iHKuC6mO14_YYD#KM4q$>n_euokRcI&YBa`q=rYI<`P!>kAN\N\SaM/GHp!X1
%>C[M@Ym8!pE%S":<KklhQt/V1<4n6J8C"Hk(?e.k2eiPoU(4][V*Bki0/o\@[&a8V!"nIc^$I9?s#"R:$@Lq61[@+R3>iCnqB$]%
%Xl&_P20FBf;Kd%-4=S0d8bbk0,F;&COSH8."d9tLKk^\jcZjCN$OOrl4=&@t[KQY/p.;Y[]rmBM$QN4eXjeBia_UH10Ka$OCiT^2
%3e?r[6q0<cTLAE.<ZACClF(=:k9Pg<AnKSs5U6-"oD#@*Rn1A,]0Gpue#<%'2bs15bh4e"\hRG.qSn^n_E^mlJ=5X9#;J1f.WTF2
%,d_HZIO,::4VjTd]04aU@>)p&51^%NAl&,C.M%e"+Bh_s.dl9XcS<Ue*6A)'_fi`Y&<(.8-RGG5!C)ueG[ZjUauW2p^rr`fIO7g-
%!=Mej555@6@GbiQ_J4sp_h4b8'f:-HF>Y^L25^K(i(.,,E#i,^eF9V%jGm;d[5Lt4LBN%U@.@@R8-;i9cfW(!W/%F;Z9&+h6Um`f
%3(VDiW^G/RJHCK)/=*bsdqo]u/O$.kRg*Pcf9FLUlj_3<j$c)_$oFfLZLGX_RbXbC7NZ$JYUb1rM)UNICn%Y$8KHEQ*pB2f`=IK0
%,mO]uo2R*iNmEu,[RSERi9^50GrRt#W&\ZoD.%MYJKZ?51Y9iFnQ2UW814'"Q"e<l-K?Z25%-!F)eD%eW>\Y8gr_Q%Og<\8%h@P2
%q(R$\3Skr!!kZ%;h]rtq`E8k<=:&Y*iVoQmH<%X",DPs-WPU">LEEN591,Gd%:B4(M)7Jtggbn%<>P40[E&aq4P^T["Im_>K!/,B
%aU1()=<.Y'Nun@BIhe)J!W7[lJh'F3TULjqjJ+/F1glI8]&TJj]`b;0%\N,2\c>]%rHO^I3h`?7f,iU"GE:+&Y`_NAd'qYZ5ptu"
%qkIm4c4[k.&r(oaYiegr]h.3m>Uu3j,T>b8<KJBV_S-''&1r3]AmtbLHs<#bbL@>cVrM2-G#XXD9`rC>nJ*_5-XBP/Q@@aGB.UUL
%Pm/,$#k]&`^i(-r9fsCY.Nk78"SI0uIQ,AAEE3J:V:d'<?\rAm>36\3F)O)]WZXRE>/E&E8s&`%jfs35H\T%me=+=6Pm&"sP%uN$
%Pk,u1!5)Dqd/&$5=,aFlp%@12QgPdfh^bBDR$LPM_6RK'BU47>];\]BGU(sH4dU9LbGKr\5kG2o=p#hT^u-#pZPOoLDiue</kg-f
%rK]\2>scuBH>&KK,$EVpUcZ]&;:!>GF`dfp`-)-qn8M>OkHME9p57%ror)CWo1^^_AZ1["TusO'<9cl`;Y=)&)$q9a-Ig-_9$5o]
%FRq2c+B>o@J5d4!66bme^eZVjTkhGk`pLASqPY26<_/SQkXO(hlAUKCRi66sR)%B+!P>j*N@Jn3*Q!ZZ%'?&#=%=0UfI"`M7B'Bb
%Bi=%()+\R)1e-rg?K[-U*oMr!3UF3--QlnE*1Y87VR;*,>A/jqLjM2E,;hgUq+SIE%1kC<^l1C\&D(Q-lROltb$KQD(")aJ00IK.
%nPr(KR7f$N/`m01%Gk`G=Z6t_lD<H$X.n$H[B6(SjBYZK)'ffRcNG4hWG5.<Uj^C,d_a]@^+'!&CHoDs&'k0+IZeB:@-eX$(F>8W
%X:_7_3OklbA:$1;EuiE!\\9KZ!1''3,=O?DS^p*L;82l"hV)I?LDJo'4scb;g#,P6X3AG/McBBQ)B^F\XY=`=i?I>(%k.2?R[5O\
%\IrP^p`e/_0\-1cNn<HEQf``S1Se*Ld0'/]%<EMAIL>(9`p@*jXGb_Uu=qs.BJC1T!pO86UpjaHr/8u/QD[N2&S>PEf
%_G=<c4/pHTeEn3E58&s>)nt=2gQYAd,X1O^r8q<#i2[*_dS!u6"7[&jA0PS^X<CMorfP]0R3:sN^%pGh35G=F_)tJ:Ufu#L2Z#\M
%3@,#IGp.+s\.)3hST'E>r"<hH*["q:.1+jQ!fj`oZo#^fEJ!nogr=OOf2`]TQK;ZfcTPE\RFcnUg`#.Y#1Lc@0ko6S8&T"`&71"\
%4Kt@KLL6)M,mPKRMU%gYj7n9+dRgHHB;D"51hGsUWuQHH:":_MJoZ-l_hB)Qg6Ab78WHJ,S4`Q`_K=B:7VZ'+!RR`0fCF<tc&_rL
%DLRg"qg]p><ItJJ]th"&<AH1nL>In?1[O=Z+aJD6,/QNgBJ'G+%tg[X>qbc\hPg.GD2eZJUq+pK<rCrp=ho8LpHV94O(NmS]?G]O
%:jV;?KA.H%Y)'Ak3D8"Ol`Rb]aeM$ER5#E\&aWH=E=Z>Ee@HLg6"lB?Dc+k/'0q^eK!.o[iT/D;pH=iUK@B(\(8OEkP.^IM[:JU:
%[9)MP/,[oc\YQ4V.eLb?D:lHPd4Z3\_MJlCRLtQAO^]'d3)r\2'A&B:)h`aF#ujoK8c/&;RBr:EU!^2BmBjeo7[q+EH(iuSWbh\>
%FP+$k6`9GgbX:ZCmAW9h'49ofcZdV]2Y8jGW9I_(,`P=R+q`eNdI\^sd[0;/f+cO\0EF1$<ebJs8NKo#@9N4bh<6rNUX<!^AW1_!
%"o5/\Oc%iA#j+NlW!qs&^0e=IPP2'k)Dr%@-(qD,:_XEj[GP0=_0!`Lrh;/QZf12gc<;;8C,rEn/TK$`%3<q)k@WhggV)V@kq`6`
%iEMB@:>l#XGLb:\(,F@7IBO2aot:_E2(p$=PT@_nA'J7d1^K;K$m1-bOjH(tM7el/-7XCQRffh$gVI=c)]EfYZ&DJpW45PYV1UUK
%RI0YQ"f6G3_:8LOaYjB*17[XjAq>jhc[""2@tYZLmfEdIW@=:dg_Y"g`H\_J44"d9Y7'DW0[F'PV0rb&JIJbQ6'(6H@$AFhKLnF]
%pn/^$(K2&$fW'uuDe9n);1$3V*<=B@)]<)<EMAIL>'ap"o\%j+jN69dF*inGq`*8kQQ(P)\J?JnJaM`Q]HZoh<3*,T.jmF]?G
%o*bap0lSnpf09oM,//M&C&V6)oik&\-/llt"=8.mg5bl6mD?jAr6*"8HiT"H1S_Q=B7-6V2_IIePek6IiRaO^Mp42*S2K"1jD]PM
%"mC%r\CDI1aYJ0Zpe<-VRjc&s'"NM"YJF:IA&@[e98YVmqaATQNKV$PamHe0A&#:@ku+a'm6!ENB0Yc3")9Tqk'`/kT/I4i9COo^
%kmr)@40NhkSF)q[5gi]:U]'h1<7^MLOFOa'fi,Z^Q<hr7qYXrl1q'Uj#ga^r%F)0(4mq_Whq\aE=(I8FUlGfE,CUKPW.i;)3eh#M
%KL#81?*G8>TkJ@*D8`&rJrOE%4ndekV`0?QGlGr!N-O>#ET%=o-pu@2JR^pC7K,a^(@cDa\jLfF15q&$g]rp!*rK>.n!gI`cHmbu
%(qLtgRu@@Y-q2:hD`Ytr[:gt@\Q:[L2+-0..:hlkhPGt>mpBedAR_U9F`_p_#/2%@j\8H_'1NhV^oSCXhRD[Gi&YT]!?^)o06Fb]
%9M/rhSMO>9Jt`1jL/ItA.#p3mar%(+d!ZbiDQ3pj_B];`OTr.5>thb0$8NRccTR^ppS26518l'dAt'ths+G^)@@ejpIdMP15CF@e
%dU9!a\jQhsIhT-V0aN`4POn)7]:GX%_GK?:LS\pa:h@'hLVf[`C88t[3?'nXZbQNBN7s>>U"l]77Z1FG\^TG3;,`PIX2g585LmFh
%%<$7gGWn7:F(07@:PiH'??S:*Q;mjf5>7C+_eu=krnct=(4FoWi6.ZrrnV:Bhi:)#EU_m@83;c;Ktq]@FP*`;43;l`D<8rcre'iM
%mVV_9i6etu)GgDNY5UVT2h*s6--r@d,bDVH)TSoYh!k&NXT,(G6TsXl;TSDt7mD@S2d63a2"ZGb7=>8sYO$k$,aHB)s7m*R?4\_`
%cA1rRRN)3<p;kjQ5D$5`1tgF>0^G>WL*ljP"\,']((Ph'SA\i#7GC)(iAm[6q<G40c+c&q3C*T133V0:CA3nK@JGUV9t>bI/<bYa
%+7+j^A]WK8!1rN2,Fc/1A&!m"U9Q]e$?X:bje)9pq/JC@[o7q>P3G5@k!pX^7/qU-;C49=mJ];'MgpL2e,86=-s-&c*rki$/`sbR
%&LJLqe1ZnAJ?]UKs'LFE;lde([b1Js1[c[iAo#tYe$,]p2oqWhiPSL_HSt#hTF6d_1NX?$I^Ii.-J(OHWaB?DKf/K$[Qc*/\u4%A
%T9qpL^ZuP@1,Jl^nf'KW.A4J)/DnfDQKHHX^1C3UN?M+/qeH?Rp('[\0*1eVS$,:tdg!?im?2=X#K'KF=/LQ,B:U2ipn5!d3qHAB
%B'Rf'qc^m.V??^>Yq5P0!c7K7R<U9hI/Fj3r7Pe*$)Gigi+_T]OSCC<kL/=i?.pBCX-:s)AZP_>c^"+cn)na.q=8%P07P'K1$t])
%PDnbqJ1l@qr2S\Yqf/#U4raY(FZj-njVRfgI4:u!h;/Y^s+!0[rC/R=k4N\;Z0U@coQlo#4`SRES)*=sNbeL(+:!B-")L+spX@/K
%hkM_"'q63XU#-T[)XK]n;1"@EXC$I>Du0J:p`KLs`A`qmhC+Zn2_6N)NIUogC7[qrlZ,8;m^Lsm/IqoN4ru.?IX.#:@eA5R?/U;(
%59B.idbprG4NjUn<o/FHG=he#eW;Waas\tEimaUcJU`.I7D)4]:[@K,p=i*3*e2^]3#95"kM8oS2i.d?%oDp`PjAT-MVsPf@nE*h
%')=PF(#cp7^b@SM/gu;3!N\Qn8+6Q^0.<"F6u]uT\LJ2RH:p^1l:m;#"Q`*9C2G)!]mkR[J)FT:>cg<FhesX_jki*^ibeN"4KSJW
%MkXn"8C>;DO!Z;?%p'?dH7;-VU8aBO-pPOHZsL[*+p3]n5ENl5@X'STS:J$no:SYNBnG=$85]$N"=t^@^IX![JJ*3_)N^]O(DtYP
%@/Mhh#$hSc'1@_)o2-\>oE4p,p>?Fc+9Wep6briDc[lO^V#;RgR?%s\0+k.=+uql\b/,>(oRURqks/10jZt?Aq+7.i:sV[`Kb\m]
%:N83%MKhA)Yb\6([KWMC<;_N/]Kf=`&MHC<!=fNW-WC6FW4/T@563<k$:T[Qok4gljU_qgQdet[J:7fVW&K-jZ9[@kI%VZYKH^N[
%0e<E79XB1Y0S0E,dX"7roT_?,:=Caj$4qjO$"u)e6_Gs^O9Q&%;,bk<O:8%rV#^un:+rG4TH8t'JLLTQbb4`aT#?t_b5M0Prp)3J
%`4V^TGu!9@gj,$XBnUh.?m4CC?]l)<"T_I%JPQP6:cZ*08WJ:A,pGJIQB-R>2Z/bu#fIDOB$S$qblFsl:Ras"F;YkZ'Gb[&$1pI`
%Ifo9R]ndFq:'W-S>3MfU0*75h"IS4`cHm`TJE[Cl,&(=e&64sn\#'$3+[Ukp&>#t0&U6;eJ\/qn!'pbWAWGBYXnr,c39fT3MU`<a
%EPPj.8-A,b'S.cbIoO@C,Xdn'*0?j2Ujh5j^j0?3J/S\%Dm2Vg-7hLV@$*_DW/(F;.2\'KU_d`7G9`"*3)EHTi'^BDkbD:R(8*7P
%!"hi?"2n"UF8,PnmX`*7B9ApkDBC"oag:D(L1bKgcbEEnr$5*k;'bWV.UlQ1%8]IAeD32'SjS2o:d"h9`K1VV!cTLeU.e!fcSCc+
%5^:Rfb92QE6&XYd0.@)*K:kflN;uW\LDGFBKtDu!!q]+$cgs1u5VO<GjJHV;Jn,9ki&F!@O;V#K0E?*N,t&ju%1GVg&XWOa>l;LM
%k&:T)FAq_*+R%bCTrB0a8`J#+(`8YU$lAsi3a;\iN\Y'(n0bn>+G/q!7rABf%ncpKGpEI4Yun^'UK$t3_gjjF]i:eY.(i<8"V<%M
%<(->j*r:F9/;<R#].T8$ar(niSot\H+<b(U0+DSEL&)I.`.MP+7M"u.J]9.c8W$pK,)Ul-C]GE?_Is:7BSI/f73=u#1L>Y,YTi[:
%OTLtAILM`uiCR>t+)-CC!P0ZW(kE*Q_"24O$4'pmB.5tJ0GFp'!%of'UGD$I*;+HBiAf$GD*b-$dhZ$g#%kOSFU/b6pd?&i,,M=X
%H7b4O+#B#++^,]`3ASso!:9eW?6dN)+Pqs^Y%K_Cn>qX-!.#7-,?QY.UEYEqJp[cu%Tb'=8>G\i##c,,I<R>UN$06fP$W5Pog.l\
%(e*N!#Rm;N=2([/"h>G`\j2k`YJ9?V0*3TLo[dI*S'1c]CB"&[-QDk*8=NJbkjs4$BXbkoIkXq.ZNH:f/hUNAn:l,S#4lrH1%ZDI
%B^'h&ce^:t_r%[$2L71[R<_[,p"03E)S[UP~>
%AI9_PrivateDataEnd
