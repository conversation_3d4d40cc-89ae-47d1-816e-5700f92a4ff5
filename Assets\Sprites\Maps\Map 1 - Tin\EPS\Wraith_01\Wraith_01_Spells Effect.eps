%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_01_Spells Effect.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 60 60
%%HiResBoundingBox: 0 0 60 60
%%CropBox: 0 0 60 60
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 1 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:57:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:57:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:57:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>256</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAAEAAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXYq7&#xA;FXYq7FXEgdcUErGmQe+LA5QFNrnwxajnUzcnxxazqCtNyfHC1nOt+se+LHxnfWPfFfGbFyfHFkM6&#xA;8XJ8cDMagqi3Pji2jUKizIfbFtGUFeCD0xZg27FLsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVaZlXqcWJkAovcU6bYtE86He498LjSzKLT++LjyzKTXHvi0nMsNx74tZzLDcYsDma+sYWPj&#xA;NfWMV8ZcLjAyGZcLj3xZjMqLce+LYMyqs/vi3RzKyXHvi3xzIhLmvXfA5UM/erK6t0OLfGQLeLJ2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuJA64rajJOB0+/FonmrkhZJ/fC4U8yHef3xcWeZDvcYuN&#xA;LMovce+FolmUWn98WmWZTNx74tRzODyt9lWPyBOGkccjyDj6/wDvtvuONLc+4rDMy/aBHzxYHKRz&#xA;cLj3wKMy8XHvizGZUWfFtjmVVuPfFujmVkuMDfHMiEn98XJhmREc/vi5UMyLjuAdm+/A5sM181YE&#xA;HcYuQC7FXYq7FXYq7FXYq7FXYq7FXYq7FWmYKKnFjKQCFlnri4mTMhJJsLgzyoWSfFw55UNJPhcW&#xA;eVDvPi4s8ypDaXU+9OCH9ptvwyQiWzHp8k9+QRkWlwLvITIfuH4ZMQDmw0MBz3RSQQp9hFX3AGSp&#xA;yY4ox5AL8Wx2KtEAih3GKkKMtjaSfajAPiux/DBQceelxy5hBT6MesEn+xf+oyJg4OXs3rA/NLpV&#xA;ngbjKhU9j2PyOQIdbkE8ZqQptJ8UxzKyT4HIjmREc+LlQzImOfA5UMqKjmxcyGVFw3BHy8MDm4sy&#xA;LVwwqMXNjIFvFk7FXYq7FXYq7FXYq7FXYq07hRU4sZSpBzTYXBy5UHLNi4GTKg5ZsLhZMqFkmxcK&#xA;eVRjEs8nCIVPfwHzwgNEeLJKopra6dFDRn/eSeJ6D5DLBGnb4NHGG53kjMk5jsVdiqjcXtpbCs8y&#xA;R+AYgE/IYteTNCH1EBLZvNekRmiu0v8AqL/zVxwuHPtLEOVlDN5ytP2YGPzIH9cDSe1o9Ily+cLY&#xA;9YGHyYH+GKB2sP5qJi80aY+zc4/cgEfgTjbbHtTEedhHR3unXi8FlSQN+wev3HfFyRmxZRVg+SX3&#xA;2jMlZLWrL1MfUj5eOQMXV6rs0x9WPl3JYspHXIOrGQhXjn98XIhlRMc2BzIZUXFNi5mPKi4psDm4&#xA;8qMhnIPXFz8WVGo4cVGBz4TEguxZuxV2KuxV2KuxV2KtMwUVOLGUqCDmmwuDlyoGabF12TIgpZsL&#xA;g5MqElmxcDJlU7eKW6m9NOnVm7AZIC2rFjlllwhPre2it4+EY+Z7k++WgU9BhwxxxqKri3OxVK9X&#xA;8xaZpa0nk5T0qsCbt9Ph9OSESXGz6uGPnz7mGap58v7glYWFtF2VD8X0t1+6mWDG6fNr8k+XpDHp&#xA;da5MWZyWPUk1JyYxuFVqB1pf5sPhrwuGtL/Nj4a8KqmsL/NgONeFFRasp75A42PCjYdSB75AwRwp&#xA;1YeZLyCgWTmn8j/EP6jIU5OHWZcfI2PNMZLuy1EepHSC8/ajJ+F/kfHIkNmaWPPuPTk+w/tQocgk&#xA;HYjYg5CnX7hXjmIwNsMiKimwObjyoyKbFzseVGwzYHOx5EbBMQRi7HFlRyMGFRgdhGVhvFk7FXYq&#xA;7FXYq4mgripQk8tcXCzZEDNLhdblyICaXC6/LkQUsuLr8mRDD1JpVjjFWY0Awhw/VOQiOZZHZ2iW&#xA;0IjXdurt4nLQKem02nGKND4q+FyHEgCp2A6nFWB+a/zEigL2elOCw2kuxuK+Ef8AzV92XwxXzdVq&#xA;tf8Aww+f6nmt7r5LMzuWZjVmJqST45kxxOq4bSmbXHY/DU5cMTIQQrajdv0FMnwBlwLTc3h748IT&#xA;wO+sXg748IXgXLqF2vUVx4AjgREOtyKfiqMgcSDBNLXXQafFlUsTAxTm01gGnxZTLGwMU5tdSBpv&#xA;lEoNZimsF5y3JqTlRixIRscoOVkMURHIRgZwnSMilwOdjyI2GXF2GLIjoZcDsMWRH281D7d8DssO&#xA;WkYCCKjFzwXYq7FXYq7FVGeSgp9+LRmnWyXzSYXV5ZoCaXC67LkQM0uLrcuRBSyVOFwMk7TbQrSi&#xA;G6cfE+0fy7n6csiHcdl6ah4h5nkm2Sdw7FXlv5jfmEoaXSNOlpElUu51P2z3RSP2R38fl1y8OG9y&#xA;6jWaq/RHl1eWT6hcTtRK08czRABwBFTS1dzViSffJ22CKJjsfbI2yEUQlj7YLZcKoLH2wWnhTHTP&#xA;KOsaoaWNo8q1oZKcUHzdqL+ORllEeZZwwylyDLNP/Je6ko2o30cA6mOFTI3y5NwA/HKJasdA5MdC&#xA;epT22/JzyjEB6xuLk9+cgUfcir+vKjqpto0UEav5U+Qlp/uMJI/a9e4BP3SDI/mZ97P8ni7vvVf+&#xA;VaeTVWkdk0Z/mWeYn/hnbB48mJ0WI9PvUZfy20gb2tzPC3YMVdfuIB/HB4paZ9mwPIkIKbyXrFrv&#xA;bypdKOw/dufob4f+GwGQLhZezJj6d0MDc28npXEbRP8AyuCMrMXW5MUomiKRsUoYZWQ1ImOShyLO&#xA;E6RsMuBz8WRHQy4uxxZEfDJgdliyJjbyVHE/RgdpgyXsr4uS7FXYq0zcVJxYyNBATydcLrc00vnk&#xA;xdZlmgJpMLrcs0BNJhdblmoxI008cK/akYCvh74QGGLGZyER1ZdGixoqKKKoAUewy16+MREADkF2&#xA;LJgv5pedP0Hpo0+0empXymhX7UcXQv7Fui/T4Zk6fFxGzycLWZ+EcI5l4cscs78n39s2PJ1Iij7e&#xA;y6bYCWwRTCGy9sgSzEW5LnTrfaWZQw6qPiP3LXGiVJAUTr2lL0EjfJR/EjDwFHiRZFoXmr8vbRVm&#xA;voLu7uuvpvGghU+BAduX0/dlU8eQ8m/HmxDnZLKovzYtJ1WPTbaEACiI0m9PZAqHKDpiObkDWA8g&#xA;1J+YuvV+G3tgPArIf+Nxj4EV/My8nJ+ZmsKR6tnA478S6/rLY/lx3r+al3I22/NSyJAvLCWLxaJl&#xA;kH3H08idMehZjVjqGQaZ5v8ALmpMEt71BK2whlrG9fAB6cvoyqWKQ6N0c0ZcinOVtrsVUri2t7iM&#xA;xzxrIh/ZYVxYTxxkKkLCRXvlkx1ksWJHUwsd/wDYt/XAQ6jU9l9cfyS1SysUcFWXYqdiDlZDpZRI&#xA;NFERSUOBtxzR8MmB2OKaPgkwOyxTTCCTpi7PDNMUbkoOB2kZWLbxZOxVQuHpt4YuNnml079cLqs0&#xA;0unkwusyzS+eTrhdZlmg5H74uCTaM8uR+rfSSnpCu3+s236q5ZEO17Kx3My7h97Jck9ApXd1BaWs&#xA;11cOI4IEaSVz0CqKk/dhAs0gkAWXzbrmqXOv65dapcVBnf8Adxn9iMbIg+S5t4REY06LJMzkSV9t&#xA;adNsBKgIm4ntbGIPMfiP2Ix9o4ACWRICQ3usXdySvL0ov99oabe575aIgNMpktabo+oajJwtYiwH&#xA;2pDsi/M4ykBzWMDLkyuw8hWqANeStM/dE+Ff+aj+GUSzHo5MdOOqcweWdJiACWcfsWUOfvauVnIe&#xA;9tGKI6LNQtNBsYfVvIoIo+3JFJJHgKVOGJkeSJCI5sdvfOVug9OwtuSrsrymg/4Eb/jloxd7RLOO&#xA;gSmbzPq8hqJFjHgqL/xtyyYxhrOWSide1UneYH24J/AYeAI8Qr016c7TRq48R8J/jjwL4hZR5d/M&#xA;bV9NKJbXJkgG31O5qyU8ENar9B+jKcmAHm5GLUyjyL1byx5+0bXSLev1TUD/AMespHxf8Y22Dfr9&#xA;swcmAx9zscWojPbkWTZS5DsVQWo6XDeLy+xOB8Mg/UfbAQ4er0cco7pd7HpIpYJTFKvF1/H3GVkP&#xA;NZcUscqlzV4JMDfimmEEmB2mKaYwP0wOzwzTK2k7eOB22nn0RGLlOJoK4oJQFw/XC67NJLrh8XVZ&#xA;pJdO+F1eaaAmepwutyStBXElBkgGoJ55SWtlNL3eWn0KB/XLHoeyoVjJ7ynuLtGC/m9q7Wvl2PT4&#xA;2pLqMnBh39KOjP8A8NxH05k6WNyvucPWTqNd7yW0tum2ZxLrQEZdXENhamZxVukafzNkQLLMmgxW&#xA;5uZriZpZW5O33AeA9svApxibTryx5Xl1V/XnqlkhoSNi5H7I9vE5XkycPvbsWLi36PR7PToLeJYY&#xA;YxHGgoqKKDMQytzoxA5IxLb2yNsqSTzJ5gj0wrZ2kZutVmH7q3UFuNehYDf5DLMcL3PJqy5OHYbl&#xA;jkPkXzNq831vVZxAz/z/ABuB7ItFUe1cuOaMdg0DTzlvJNYfyy0pB++nnlb2KqPuoT+OVnUFsGli&#xA;3N+Xmh0ovrIfEP8A1BxGeSnTRSq9/LxVBNpdEHssqg/8MtP1ZYM/e1y03cWNaloOp6fU3ER9P/fq&#xA;fEn39vpy2MwWiWMx5pfk2tGWt+6FVkJoPsv+0p7ZExZiT1byP+aTRvHpvmCXlGaLb6k3UeAmPcf5&#xA;f3+OYObT9YuxwavpL5vVAQQCDUHcEZhOxbxVCajYJdxU6Sr/AHbfwPtgItxNZpRmj/SHJjwDxyFH&#xA;HFlNCD45U80AYyo80bA/TA7DDNMbd8DtMMkxt36Yu0wzTEGoB8cDtAbWTNRPniwymgls7dcLqc0k&#xA;uuH64XV5pJbO+F1WaSCkbqcXBKWXs1ActiEgMp8nkHQ42H7TyE/8ER/DJyel7OH7ofFO8i5zx381&#xA;bs3XmlLYGqWUCJx8Hk/eE/8AAlc2GmFRvvdXqzc67kgtLfptlhLSAxnXL761fMFNYYvgj+jqfpOX&#xA;QFBoySsu0HSJdV1KO1SoT7Uzj9lB1P8AAYzlwi1xw4jT1+ysIYIUhhQJFGAqKOgAzBMrdnGNI+O3&#xA;9shbMBWe3lMTCHiJSPgLVKg+JA60wWmkNpfl2zsDJKgMt5OeVxdyUMkhPiew8FG2SlkJYwxCPvTA&#xA;2/tkLZ0ptb+2G0UoSW/thtBCDmt/bJAsSEvubYEEEVB2IOSBayGF+YPKSENcWC8XG7QDof8AV8D7&#xA;ZkQy97i5MPUMPIIJBFCNiDmQ4yKtZaj0W6H7B9/DIkJBeoflb5/eCaLy7q0lYXPHTrlz9hu0LE/s&#xA;n9jw6eFMPU4L9QdhpNRXpl8HruYDs3YqlOt2VV+tIPiWgkHiOxyEg6ftTTWPEHTml8D5B1+GSY27&#xA;9MDtMMkygbpgdrhkmcDVT5YHb4ZWFly3bFrzlLLhuuF1GYpbO3XC6vNJLp2wuqzSQUzUGEOMkeoz&#xA;UBzIgGwBmHkOYSeXo6dUkkU/8FX+OOQbvSdn/wB0GRZBzXhvmh/rHmvVJDvS4eP/AJFnh/xrmyx7&#xA;RDqMu8yhL6Q2ul3E42ZUop8Gb4R+JyQ3LGRoWwXMlw3pX5c6SItLa9YfvLtjxP8AkISB/wANXMPP&#xA;LenP00KF97OIYem2YxLlgI2OHBbMBEJDkbZUqCHG1pxhxtNLGhxtFIeSHDbEhBzQ+2SBYkJfcQ5M&#xA;FgQllzD1yYLWQwfzZooWt/AtD/u9R3/yv65kYp9HEzQ6sWBINR1HTL3HRsvxxpKux61HYjIsi95/&#xA;LPzcdf0QRXL11KyASck7uv7Mn00o3v8APNXqMXDLydvpM/HGjzDMcoctplV1KsKqwoR7HFEogiix&#xA;p4mt7h4T+ydj4jtlJDy8sZxzMe5GQN0wOfhkmVu3TA7XCU0tW7e2B3GnksuTucWGoO6W3B64XUZi&#xA;llweuF1WYpdMd8Lqsp3QNy1FOTiwDG9VloDmTjDZFk35VX6zadf2tatBOJKdwsqAD8Yzhzxqnfdm&#xA;y9BHmznKHZPDtTRzr2ol/tm6mLfP1Grmyj9IdRP6j70u801TRNv2pEB/E/wyeP6mGX6WFZkOI9y8&#xA;v2Yt9JsoAKenDGD8+Ir+Oa2Zsl2+ONRCewx5WW4BGRx5FkAiFjAFT0yLJCzaxpULFWnBYdkBb8QC&#xA;MNFxMmvwxNGX6XQ6xpUx4rOAx7OCv4kAY0Vx6/DI0JfoRbRgio6YHLQ8keFBCDmjyQYEIC4j65MM&#xA;CEruY+uTDWQk19AroyOKqwIYHuDtlgLVIPM9QtDaXstuf91tRT4qdwfuzMibDgSFGlS1+K2Zf5T+&#xA;vAUjkn3kHXpNE8wQ3HIiEtwuF8Yn2b7vtD3GU54cUWWHL4cwX0YrKyhlNVIqCOhBzVPQN4qlGtw0&#xA;kimHccG+jcZCYdN2njqQn8FC3bplbThKZW5wO1wlNLVviGB3GnO7VydzhY5yllwcXU5iltweuF1W&#xA;YpdKdzhdXPml942xyyKAxXWJKK2ZeMN0W/ys1oWvnGSxdqR6hCyL/wAZI/jX/heQy7PC4X3O17Pn&#xA;U673tGYDuXjmv25i8yaipHW4d/8Agzz/AONs2ED6Q6vIKmUm82QltBZh0jkRj8ieP/G2WYj6mrMP&#xA;SwTMlw30FpwBtoSNwUUg/Rmrlzd1HkmsKZWW0IyNQBU9MiyYvrOsyXUjQwsVtVNNv2/c+2SAeZ1+&#xA;vOQ8Mfo+9Ksk612KptoutSWsiwzMWtWNN/2Pce3jkSHZaDXnGeGX0fcyiRQRUdMi9MhJkyQYlLrh&#xA;KgjJhgWM6Zfm6iuLeY1u7GVre4/yuJ+GT/Zrvlso18XHhK9uoU7tOuEILAPOFuEvYpQKeohB+an/&#xA;AJuzKxHZw8w3S7ThWOb/AGP8cnJri63PC8HvglyYSfQ3kDVDf+WbYuay21bdz/qU4/8ACEZqcsak&#xA;7rRZOLGPLZkeVuWg9Wj52TnuhDD76fqOCXJwu0IXiPklVuemUuqwlM7c4u1wlM7Y7jA7fAd3XHfF&#xA;cyW3GF1OZLLjvi6rMl8nU4XWy5pbenY5ZFQxfUIJrmdLaEcpZnEca+LMaDMzHs344kkAMQmuJNJ8&#xA;0ma0fk9hcEwuduXpPtX503zNiOKO/Vy4+iW3QvpDTdQt9R0+3vrc1huY1kT2DCtD7joc1Uo0ad/G&#xA;QkLDz3z3ZmHzGZqfDdRo9fdRwI+5RmVhPpcLURqSS3tj9d0u5tR9qWMhP9Ybr+IywGjbVKNgh5aQ&#xA;QaHYjqMzXXvdvJt4t55d0+cGp9FUc/5UfwN+K5rMoqRdvglcQyWEZUW8LdYmaHSp2U0YgIP9kQD+&#xA;BwDm42vyGOGRH4thmTeSdirsVdirNNGmabSoHY1YAof9iSB+Ays83rdBk4sMSfxS+YYQ5RS+4GTD&#xA;AvN2vPqP5jXdvWkN+iBl7cxEGU/eCPpzLq8Y8nA4qykd6e3Y65WG4sG86ja2+b/wzIwuJn6JTpsd&#xA;LWR/5moPoH9uWS5tURsona6X54nkwk9j/KO7P+mWhOzIkqjw4ni3/Ehmu1Ac3s2e8ovR8xnbqN4v&#xA;K0mH+Q36sB5NOpF45e4pHb9spdDhTK3xdthTO37YHa4V1x3xZ5ksuMLqcyW3HfF1WZL5OpwuslzS&#xA;29GxyyKQt8n6cLnzCJ3FUtEMg8ObfCv6yfoy8nZ2nZuPiyX3PHb5y+oySHq7Fj9Jrm0jyaom3q/5&#xA;ReZQEbQrl9iTJZE+J3eP/jYfTmHqsf8AEHaaPL/CWTef9PM+nQ3iCrWj0b/UkoD/AMMBlOCW9ORq&#xA;I2L7mIWh6ZeXFDA/O2iNYakbqNf9FvCXUjosnV1/iMycM7FOHnx0b6Fkv5S62tbjR5W3qZ7av0B1&#xA;H4H78p1MOrkaPJ/C9ThOYZdiFusQmbSp1XdgA4/2JBP4DAObi6/GZYZAfimGZN5J2KuxV2Ks00aF&#xA;odKgVhRiC5/2RJH4HKzzet0GMxwxB/Fr5jhDlFL7g5MMC8b81XvDz486HaCaDcf5CpX8a5n4x6HV&#xA;5pfvLZzdnrmMHLLAfOkvO7t4F3KqWoP8o0H/ABHMrENnDzndZ6H1e0SLuo+L5nc4bsoqglZ3uk+e&#xA;SPJpk9R/K5ymtIv+/InU/dy/41zX527QGsvwes5iu+Urk0tpT/kN+rEtWf6Je4pFb9sodBhTK3xd&#xA;thTO37YHa4V9yNzi25wltwMXU5gllwOuF1OYJfKNzhdZPml14uxyyKAnXke2CWt1cU3llCfQi1/4&#xA;3yyReg7Kj6Ce8vA9YhMGpyR0pwdkofY0zbYzYdcNjSO0q5lgmjmhcpLGweNx1DA1BwSFt8DT3Py5&#xA;rtp5l0Z45gBccPTvIR/lCnNfY9vDNbkgYF2+LIMkWGzWktjey2kv24m418R2P0jfMgGxbikUaX3u&#xA;mWmqWElnciscg+Fh1Vh0ZfcYiRibCZREhReY3thq/lfWonrxmhYSW04HwOB/nRhmYCJh18oyxye1&#xA;eVvMdnrmmpeW54uPhnhJ3jfuD7eBzXZMZiadtiyiYsMhjYEUPTKm5i+taLJayNNCpa1Y12/Y9j7e&#xA;+SBeZ1+gOM8Ufo+5Kck612KptouiyXUizTKVtVNd/wBv2Ht4nIkuy0GgOQ8Uvo+9lMjACg6ZF6ZB&#xA;zNkgxKW3txHDDJNIwWONS7segVRUnJgNcjTwWe5e/wBZe5I+O5uC4HhzeoH45swKDpyblb1G7cUO&#xA;YQdgWDRxHUNUm1Jx/o6Nxt6/tcdgR7d/nmTyFOIBxG2r5uuIWSUwjneD2ycuTjyeoflxG36dtSOg&#xA;WQn5emwzXZi2aH++Hx+56zmM9Ch9QbjZTHxWn37fxwS5ONq5Vil7kmtx0yl02EJlbjF2uEJnbDcY&#xA;Hb4Buq3I3OBt1A3Sy4HXC6nMEtuB1wupzBLphvhdXkG6BulqDk4sAyTytHw0eM/zs7f8MR/DLC9N&#xA;2cP3I+Lwz8wrA2nmTUEpQCdnUf5Mh5j8GzZ6eVxDrM0eHJIeaV2MnTLSmJZRoOq3WnXcd1bPxlTs&#xA;ejA9VYdwcpnEEUXIxzMTYZ9fz2nmGwXUrMcL+2X/AEm2rViniP5qeOYsQYGjyc2RExY5pXbSdMmW&#xA;sFE3um6fqlobW+iEsTbiuxU/zKeoOASMTYZSiJCixFfKnmfyvfnUfL0n122P97bH7bJWvF06N813&#xA;9sv8SMxUtnF8GeM3Hdnflzzhp2rfuCGs9SQfvbCccJQR14g05D5fTmNkxGPuczFmEvI9zI1kBFD0&#xA;ylvQs2jaVMxZ4AGPdCV/AEDGy4mTQYZbmP6HQ6NpULBlgBYd3Jb8CSMbK49BhibEf0otpABQdMXL&#xA;UJJMKCUHNJkgxJef/mZ5hW2sBpcL/wCkXe8tOqwg/wDGx2+VcytPCzbharJQrvYD5ZtTc65aJSqo&#xA;4lbwpH8W/wB1MyshqLhYhcgz7VFM8LRciivtIR149wPn0zFi5s90muPTjjEcYCoooqjoAMsDUUjv&#xA;pOuWBpkUNpUZkuS3vTHIdmiZes/lvbE6pzptHEzV+dF/jmuylyOzheX3B6VlDvkv1mTjbrH3kb8B&#xA;vkZuu7SnUBHvKBtx0ypwcITK3GB2uEJnar8QwO3043Vrle+LfqAllwvXC6jMEtnXrhdVmils64XV&#xA;Zgg51qMkHHDJPL1P0RAPAuD/AMGTlj1HZx/cx+P3vNPzj0grqMN8o+C6i4sf8uLY/wDClczdNLo4&#xA;faEKmJd7zOzkKtxPUbZmlxolPrKbplZDdEsg02+mt5UmgkMcq7qymhyqQtvhKk3S7Espk4hC27Ku&#xA;wr3oO2V02WiLlJbizdIJPSuAOUEn8si7qT7V2PtgGxZHcbL/AC15lg1WFkYejqEHw3dqdmRgaEiv&#xA;Va45MfD7lxZRL3p5JBZ3QUXMEc3HdfUUNQ+IqNsqshuIB5o6OUAADoMjTO1UTYKTbjNjS2ptNhpF&#xA;qEk2GkEsd80eaLLRbMyysHuHBFvbg/E7fwUdzluPGZFoy5RAPF9Qv7rULyW8un5zTNyY9h4Aew7Z&#xA;sIxAFB1UpGRsss8kacYbeXUZBRpv3cNf5AfiP0n9WU5pdHJwRoWm93N1ysBsJSS8m65YA1SKQ3st&#xA;a5aA0yKZaDan4SRvlGWTjzL2L8u7Lhb3NyR9orGp+XxN+sZgZDu7LsuH1S+DMcrdukepzereFR9m&#xA;Icfp75XI7ug1uTjy10jsugXpkGzDFMrdemB2uEJnaL39sDuNNFWmWqfLFvyiwls69cLqc0UtuF64&#xA;XVZopdOmF1WaKBkXrhDglPfLj1sGT+SRh99D/HLA9H2XK8VdxQfnvRf0r5enRF5T2/7+EDqSoPIf&#xA;StctxSouRrMXHjPeN3zzeQm3ueXYnfNrA2HSRKNsp+m+AhuiU8tJ+mVkNwKb20/TKyG0FNLe46ZA&#xA;hmCxzzbpF3BcDX9JZorqLe4EfWg25077faHh9OXYpA+ktGaBB4o80V5f/M62kCwauvoS9PrKAmM+&#xA;7Abr+r5ZGenPRlj1Q/iZzZ6la3UQltpkmiPR42DD7xmMYkOZGQPJEi4wUytxuMaW0PdajbW0RluJ&#xA;UhiHV3YKPvOERtiZAc2E+YPzOs4VaHSV+sz9PXYERKfYbFv1Zkw056uJk1QH0vN76/vL+5e5u5Wm&#xA;nf7Tt+oDoB7DMsRAFBwZSJNlGaDosup3VDVbaOhnk9v5R7nIznQZY8fEWeyNHDEsUQCRoAqKOgA2&#xA;AzFc3kld3P1yYDUSk+pGSGRopRxkX7SnqDStDlkWuWyURIbi4A7A75Mmg0SLMtGs6BdswcknGkXt&#xA;Gg2H1HSreAij8eUn+s25+7pmIS9LpMXBjA6oi+uhbW7SftdEHix6ZEml1WcYoGXXp70hhBJqdydy&#xA;cqeexC0xt06YHaYYpjAvTA7XDFNIFonzwO4wxoKhFRTFsItAXCdcLrc0UtuExdVmil06YXV5ooCV&#xA;aHC63JGimHl6XjNNCf2gGH0bH9eTiXadkZN5R+Ke5N3rxL8y/KX6O1F5oUpZ3RLwkdFb9pPoPT2z&#xA;PwZLdDq8Phz2+ksCgkaKTg3UZl82qJTi0uem+QIbgU4trnpvlZDaCmlvc9N8gQ2AphDc5EhmCxfz&#xA;D5LjuGa60sLHKd3teisf8g9j7dMuhlrYuPkwXuGHV1DTrkqDLaXCdaFo2H3UOX7Fxd4nuTGLzj5n&#xA;iXiuoykf5dHP3sDkTij3Mxnn3ul85eZ5Vo2oygf5NEP3qBj4Ue5Jzz70ruLq6uX9S5meaT+eRi5+&#xA;8k5MABqJJ5qWFCe6R5VvLwiW5BtrbrVhR2H+Sp/WcqlkA5N0MJPNl8SW1nbrb2yCOJegHc+JPc5R&#xA;ueblCgKCDubnrvkgGBKO8oaQmqam91diml6av1i8YioIUFgn00qfYZHLKhQ5lnhhxGzyDBtX1CS9&#xA;vp7hv7y5leRvm7Fj+vMiMaDhznZtH6Lp52JG565Tkm48i9K8laJ9Zvkd1rBb0d/An9lfpOYM5N+i&#xA;weJk35B6QSACSaAbknKnoSaY9f3hu7j4f7pNkHj4nK5G3m9XqfGnt9I5LoE6ZBswwTG3TF2mGKZW&#xA;6VIwO0wwTECgA8MDtQKdilQuUrv44uNnh1S2dOuF1OaCXTx4XWZoJfPH1wurywU7SY293HL2U0b/&#xA;AFTscILXpsvh5BJlQIIqOmWvWpfrui2usabLY3AoH3jkpUo4+ywyUZUbas2EZI8JfP8A5o8uXmmX&#xA;0lvOnCaM7EdGHZlPcHNliyAh0EoGEuEpRbXBU8W2I6jLiGUZJtbXfTfIENoKaW9303yBDYCj4bv3&#xA;yBDMFGR3fvkaZguuobG+j9O7hSZR05DcfI9R9GIJHJBAPNJLnyTo8pJgkkgJ/ZqHX8d/xy0Zi1HB&#xA;Hogz5CjrtqG3Yel/zfkvG8mP5fzVIvI9ip/fXckg7hFCfr54DmKjAO9M7PSdIsCGggX1B/ux/jb6&#xA;Cen0ZAyJbIwiOStNd++ABJKAnu/fJAMCVCxs77VtQisLJPUnmNB4KO7MewGSJERZRGJkaDOvPDWn&#xA;lDyCNItGrdag3otJ0Z60MzkeHGiewIzFw3knZ6OZqKxY+EdXlGmae8sgdxuczJzdNKTNdF0qSSSO&#xA;KJC0jkBVHcnMLJNqAMjQ5vW9G0yLTLFIFoX+1M/i3f6BmMS9JpsAxQr5oHVdT9YmCA/uh9tx+17D&#xA;2yuUnUa/Xcfoh9PXzQkEeQcPFBMII8Ds8MExgTpgdphgmVrH+14YHbaeHVE4uW7FWnXkpGLGUbCX&#xA;zx9cLrM0EvnjxdZlgl80eF1uWCBmjocLrckE70a79a39Jj+8i2+a9ssiXe9m6jjhwnnH7kwyTskk&#xA;80+VrLX7P05f3dzHX0LgCpWvY+KnJwmYlxtRphlHm8K8y+V7/S7x4LmIxyrup6qw7Mp7jNjjygh0&#xA;k4SgakksVw8bcX2Iy6mQkmVvedN8iQ2CSPhvPfIkMxJGR3nvkaZCSIS898jTLiVRee+NJ4nG898a&#xA;XiU2vPfGkcSHkvPfJUxMkJNee+EBiZNabY6lrN8llp8Rlmfr/Kq92c9gMMiIiysYmRoPaPKPlGy8&#xA;u2RAIlvpRW6uiKV78Vr0QZrsuUzPk7XDhEB5vKvO+qt5k8yvLGeVhaVgtPAgH4pP9mfwpmXjHBHz&#xA;dNq8/HLbkFTStKYsiqhZmICqBUknKpzcIkk0Hp/l/RLfRrb61eMq3DDv+wD+yPEnvmNKTudNp44I&#xA;8czv9zV/q8l1WOKqQd/FvnlRk6/WdoHJ6Y7R+9CxJU5BwccLR0MeB2OKCYQR4uzxQTCCPpgdnhgm&#xA;SLxUD78DtYRoU3iydirsVUbiOo5D6cXHzQvdLpo8Lq8sEBNFhdblxoCaLF12XGoQyyWtwsqduo8R&#xA;3GSBpxceSWKYkGSQTRzRLJGaq2WgvUYssZxEo8iqYtiA1jRNN1e1NtfRCRP2HGzofFW7ZKMiOTVl&#xA;wxyCpPJPNn5XalY857RTe2m55xj94o/ykH6x+GZuLUDq6jNo5w3G4YDLbXNu3QkD78yxIFxhJuK9&#xA;INDscSGwSRcd975GmQkiEvvfBTLiVRfe+NLxON9740vEptfe+NLxKEl974aYmTKfLH5c6/rbJPdK&#xA;dP080Pqyj946/wDFcZ3+k7fPKcmeMeW5cjFppT3OwevaD5d0nQrP6rp0Ppqd5JG3kkPi7d/1eGYE&#xA;8hkbLs8eKMBQSD8wfMTQWraPZt/pVytLlx1jibqP9Zx+H0ZPFHqXC1+p4RwjmWC6fpYFNqZZOboj&#xA;JmWkvZ6aga2i9e8IoZ5BRF/1F6/SaZjSk5OPUQxbxHFPvPL4K8k1xcyepO5du1eg+Q7ZUS4mXNLI&#xA;bkbVI0rkWEY2jIYsDnYsaPhixdlixo+GPA7HFBMraKg5H6MDtsGOt1fFyXYq7FXYq4ioocVIQdxD&#xA;Q+3bFwM2JATRYXW5caBmiwuvy40FLFi6/JjdZXklnJv8ULfbX+IyUTSNLqThl/RPNkEciSIHQ8kb&#xA;cEZa9HCYkLHJdiydiqR635L8vaxye6tgk7dbiL4Hr4mmzf7IHLI5JR5OPl0sJ8xuwPV/yUmJZtOv&#xA;I5B1CTAow9uS8gfuGZMdV3uDPs+Q+ksTvvyv842hNLF5FHQxFZAfoUk5eNTEtB02UdEqk8r+a4jx&#xA;bTLoHwMEgP8AxHLPFj3sOCfcfk0nl3zWx4ppN458FglP/GuPiR7wohPuPyTOz/Lzz3dkcNLeJT1a&#xA;ZkiA+hyD+GQOeA6tsdNkPRk+lfknq0rK2q6hFbp1MduDK59uTcFH3HKZasdA5ENDL+Is90D8v/K+&#xA;iFZLa19a6XpdXB9SSviKjip/1QMxp55S5uZj00IchuyPKW9JPMfmJNMh9KCkl9IP3adQo/mb+A75&#xA;KItwtXqxiFD6mAJaSzzPPOxkmkYs7t1JPfJmbzs5kmymdvZgU2yoyayUwihAGVksUQkdciyjG0XF&#xA;Fi5uPGjYYsDsMWNHQxYHYYsaYW8NT7d8XZ4MVo0AAUHTA7AB2KuxV2KuxV2KtOoYUOLGUbCBnhIJ&#xA;wuvy4kFNDi67JjQUsOFwcmJByw4uBkxNWt1PaPVPijP2kPQ/25IGmODPPCduXcndreQXK1jPxD7S&#xA;HqMsBt3uDUwyj08+5XwuQ7FXYq7FXYq7FXYq7FWiQBU7AdTiqQav5lWMNBYUkl6Gbqi/L+Y/hhdV&#xA;qu0hHaG572MC2eWRpZSXkc1Z23JOAydHKZJs80XFbAdsgZMLRKRgZG0KyRE4GyGO0VFDgczHiRkU&#xA;OLn48SNhhwOdjxo6CEkgDF2OHFaPRAq0GB2UY0KbxZOxV2KuxV2KuxV2KrXQOKHFhOAkEFNAQemF&#xA;wMuJByw4uBkxIOWHC4OTEhZIcXDniQxjZGDISrDoRscLimBBsc0Zb6xPHRZ19Rf5hs39DkhNzMXa&#xA;U47THF96Yw6lZy/ZkCt/K3wn8cmJB2WLW4p8j89kThcp2KuxV2KrJJoohWR1QeLED9eLCeSMeZpL&#xA;rnzBaR1EIMz+3wr95/pg4nAzdqY4/T6ikl7f317VZH4xH/dSbL9Pj9ODidPn1uTJzO3chVtwO2Rt&#xA;xLVliAwWqosROBkIEq8cGLkQxImOHA5cMSLihxc3HiRcUOBzceJGwwEmgGLn4sNo6OMIKDr3OB2M&#xA;ICIXYs3Yq7FXYq7FXYq7FXYq7FWmQMKHFjKIKEmtyPl44uFlw0g5IcLg5MSFkgxcOeJDSQYXFniQ&#xA;7we2LizwqDwYXHlhWr68X927J/qkjG2A44/SSFQX+or0mP00P6xh4i2DV5x/E46pqR29X/hV/ph4&#xA;ip12f+d9g/UpPe3z/amf6DT9WDiLVLVZjzkVBlZjVqk+J3wW45s83CP2xXhLYiOLIQKosJxZjEqp&#xA;Bi3RwqyQe2ByIYURHBi5MMKJjgxcuGJFRw4HLhiRkNuT8vHFz8WC0YiKooMDnRiBybxZOxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuIBFDipChJbg7r92LjZMF8kJJB7YXCnhQ7we2Lizwod7fFxpYVB7f2wuPLC&#xA;pNB7YtMsKm1v7YtRwrDb+2LA4Wvq/tix8F31f2xXwWxb+2LIYV4t/bFmMKosHti2RwqqW/ti3Rwq&#xA;yW+LkRwohIPbA5MMKIjg9sXKhhRkVsBu33YHOx4K5q4AAoOmLkgOxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KtMit1GLGUQeahJbeG+Ljz0/chnt/bC4k8Ki0Hti0SwqTW/ti0nCpm3xajhWm3wsDhW&#xA;/V8UeC76vivgti3xSMK8W+BmMKotv7Ytgwqiwe2LdHCrpbk9sW+GFER2v822By4afvRCoq9B9OLk&#xA;xgBybxZOxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVxAPUVxQRam0CH2xa5YQVNrXwpi&#xA;1S06m1q3hi1HTlYbY+GFrOArfq/tiw8F31f2xXwWxbHwxZjAV62reGBmNOVRbTxpi2x0yqsCD3xb&#xA;o4QFQADoKYtgFOxS7FXYq7FXYq7FXYq7FXYq7FX/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:e9a0dc25-c378-2d48-936a-a3230763dd63</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:e9a0dc25-c378-2d48-936a-a3230763dd63</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</stRef:instanceID>
            <stRef:documentID>xmp.did:370520d6-4b08-c94d-b9d4-9fde2549882e</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:370520d6-4b08-c94d-b9d4-9fde2549882e</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:54:48+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>converted</stEvt:action>
                  <stEvt:parameters>from application/postscript to application/vnd.adobe.illustrator</stEvt:parameters>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:e9a0dc25-c378-2d48-936a-a3230763dd63</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:57:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=112 G=137 B=148</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>112</xmpG:red>
                           <xmpG:green>137</xmpG:green>
                           <xmpG:blue>147</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=69 G=230 B=203</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>68</xmpG:red>
                           <xmpG:green>230</xmpG:green>
                           <xmpG:blue>202</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=204 G=255 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>204</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=254 B=208</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>253</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=22 G=132 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>22</xmpG:red>
                           <xmpG:green>131</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=77 G=255 B=207</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>77</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>207</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=6 G=46 B=33</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>5</xmpG:red>
                           <xmpG:green>45</xmpG:green>
                           <xmpG:blue>33</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=138 G=128 B=120</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>137</xmpG:red>
                           <xmpG:green>128</xmpG:green>
                           <xmpG:blue>119</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -60 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 60 li
60 60 li
60 0 li
cp
clp
gsave
0 0 mo
60 0 li
60 60 li
0 60 li
0 0 li
clp
30 60 mo
13.4316 60 0 46.5684 0 30 cv
0 13.4316 13.4316 0 30 0 cv
46.5684 0 60 13.4316 60 30 cv
60 46.5684 46.5684 60 30 60 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
/0 /CSA get_res setcolorspace
gsave
clp
[1 0 0 -1 0 60 ]ct
[60.96 0 0 60.96 -.480001 -.479998 ]ct
snap_to_device
Adobe_AGM_Image/AGMIMG_fl cf /ASCII85Decode fl /RunLengthDecode filter ddf
<<
/T 1
/W 127 
/H 127 
/M[127 0 0 -127 0 127 ]
/BC 8 
/D[0 1 0 1 0 1 0 1 ]
/DS [
[AGMIMG_fl 127 string /rs cvx /pop cvx] cvx
[AGMIMG_fl 127 string /rs cvx /pop cvx] cvx
[AGMIMG_fl 127 string /rs cvx /pop cvx] cvx
[AGMIMG_fl 127 string /rs cvx /pop cvx] cvx
]
/O 3
>>
%%BeginBinary: 1
img
JeWe:Jdd52JeWe:Jdd52bS1<u(E+54*?H7Dr?)@f*Zc=A)]BM.bnPa0bR=Ua%M00`rY#)ArXo>G&.f<]
bm]1(d1d!((E4A:+X86Z-7LAnq^W().4HSj,U4HS*?,e0dM.95d0p9i%M06d'GVB"rYPVQq\oMQrYPnW
()7Ms&eGN^dL:^-e.`?-)BBqF,q(5p0/,.<2>9$=2)I'B0.eY%,pONQ)A^j;JcO:/$P!^\'GVB#)&jS:
*r[8^+o<G`+oWSl*ZZ7?)&O,('G:reeI7$0f+\c3)]g.J-n?r+2)dKT4?YknrB^HMrBUu[5!;"g3&`]M
0.SFr+W_R<fG&o;f*i)u&/#Zm(`OJ:+<_pQ,lT.h./tap./kP&,U+EU+<DOB(`!eu&.[>$JcOI<&f;W4
,Uk<!2)dNX5X@_*7Rp!Ar_*2`r_!kr84Q-<6:!k!3B&fL/L_qg*>mKEJcOI4&e5Ng()Ru2+<i!T-7C;m
.P*,&r\".&r[ng8/1`8#.4?Mg+sJ-M)]0>)&e<V(JcOR?()@i2,:G&r2)mZ]5t+7692//W:f:4jr`&nt
r_sY3;c6Ff:/+AP7R]U,4#f)O.juVa)]%6DJcOR7'+PQe()Ru2+<i'X-n6\t/h\n51A`m-2>]0C1,1I9
/hAJ%-mg2`+<;C<()%5hh$el8h@peB)^$CT0/GOK5XIn192/2Z;cQn#=^#$6q-4b@>?Y02=&i0r:JFJQ
77'7$2`!*9,Tn$Ah\:YBh@(;4&/,fr*?ZOO-7LDq/hf"81Gq'K2`E]U3B@%!)EBf;3&`cS2Dm9G0eb42
.Oc\i+s7mC()%5hh[G):i"R+F)^$CU0/YaQ5t+=;:Jk%j='8a4?!^lG@:E^C@iJld@:3GK?!LT:=&i0r
:J45I5s74c0.J4i)\q<GJcO^;'b(]f()\,8+sn]f/1rV31Gq'L3'',"4T7M@5Q3hF4;Bn<3&``Q1GUX:
/1N%p+s7mD()%5gi=(;<i=m1K+!`<h2*!og7nZTS<**4,?!^lI@qB7bBP@-T)JEesAn>I]?X@#B=]\R"
:.n)D5WL\T.O6)SiY6tEi=$Y:',DH++<i-]/1rV31H%0O3]oMf5<hG"6:+'86lSK[6:!n%5<V+k3]K&V
1GUX:/1Dni+<;=8'+j.3JcOgF*#BV?.5*P=5Xe4;:K(:s>?tTFA7fIgC27['Df>VorGhgU*,fb4D/3m#
B4b[`?X6l=<`;df84>d(2(pC'*>dcNJcOg>*"ESr(`a_C-7UQ"0f([G3''/b5=%S%6psL67n<;F*(NLg
7RTX25s[^u4?>G\2)?s?/1Mtj+<;:6&e<t2JcOjG*?-+J/i5RP6V'pJ<**:0?XdPYBPM@$DfB];FED["
G9(gJFE;G@Df0B+BP1jb?X6l;<)?:[6U!Le/g_\[jV3:Hj9uq<'Gh]2+sn`i0/>=A3''/b5=%V(7Ros?
8kT(VpJ1]])G3Un7n6$:6:!h!4?>G[1GUU7.4-5])]'2"jU?_@jqK!T*[<-i2`sDs9Me\j>?tWHAnYss
DfKi?Fa&(UH@($eI=;'C,(4leI!^0bG^":PF)c,8CM7<i?sQu=<)?7Y69I1^.O,uMk7iLJjpW:@&f)?+
+s\Tg0/>=B3''/d5sdt/7nH<H9MSAZ;,R!b*`5d1:JOSW8kDNB6pa1(4?>G\1GUU7.3p)Y)&<hpk6uqB
k7f$W+=8]u4$c>/:K(>!?"%8VC2Im/F*)VNH@1-iJ:N3%pOXN=JUi9"I=-?cGB@nGDf'6%A78bJ=&Vmf
7R9*n/g_\ZkS/UKk6rFD',MT1,Ub,p0f(^I3^#\n6q0[<8k_uU;,^Cn<EE9#=T)D$=<CUG<E2sr;,9q\
8kDNB6pX%#3]K#R0eOq(,Tn$B'+j@9JcP!K)]KtM0K2-^85<)b>?tZKBPVI(F*)VOH[U?nJbaopKDC*<
JUi2tH[0gWF)Yu3BOtUX>?=`u84,O!0I\1bknJ^LkR8FB'Gqi7-7LK"1H%3R4[28$7S-3G:/=_c<EE:)
m9(E2=BAL$;,9q\8kDK?69dUo3&WQH/1Dni*>fJ%kmW.Dl4b*O*@!'j3C$&,:fUY(?t<qdDK'Z?G^Fjg
Jc(-!KDgK#L&H](KD^<=JU`)pG]n.IDJNor?sQo8:eO8C3AW01*>[rTJcP'E)@dAq)^$=O.P<J62`a&c
6:==88kr/Z;cQn$qcNo'o3),uqcOh>=&i0r:JOMR7mo[.4?>DY1+k%'+s7g?&e=49JcP*N(D\&</i>aX
85<)b>[LuRC2Ip2GBnRbJGk*"KE$W)L&Zo#L]<,/L&Zi+KDgB<J:;lkGB7_?C1h$`>ZXj!84#Er/gVPS
lkG$OlO4U?&f)E/,Uk8u1H%6U5=%\,8P2`R;cHh#r)s#&!a/`3r`]2'r`TA->?Ur/r)jk==&`*o:.e)I
6pX%"3ArZI/1Dhe)\s%rljSIGlkC3O+!rX"4[ht=<EWU9Anl1%F*DnWIY%KM"bVG\Knb?]L]<21M><&/
M>rD2LBWEeKnP)2rdko:IXHEbF)Yu2AmntJ<DQ4U4Z>#@*uF;YJcP-G((Luo*?caZ/ho4B3^#br7S-3I
:K(7prE0>/>$>-7>$Lo.r`fD+r`f;(#$Y88>?Y37rE0n;<Du[g8kDK>5s77f2)-^5-6XBG'+XC<JcP0P
.2O*R0/l-b92Sbp?=RYaDfTuGI"$WuJqJ]/KnbA<LkpnFMMmFPNK0%tO"$*LNJrgSMMd7HLkgb?KnP)2
JqAQ&I!KjTDej#r?<^K.91;*)0.%eXmM(6Qm0j^>',V`6-7UW'2E3f`6:==99M\Se=T)A*>$>-6>Q%_0
=^"s3=]nu-=pA26=^"s3=oDM,>?Y67>5_S==B/9q9M.fC69dRl2DQm7-6aHI'+XF=JcP3Q-l4!S0K;<e
9iG+u?tF(iE-->OIY!*&K7ei2L51SAMMd@PO-#HdP*>^+.$KgWOH>N_N/ELLLkg_=KS+o/Jq8AtG][nA
C1UdX=&Vg_5WCGG+;aJ\JcP3I'Fkcn*?lg[0JbUJ4?l2&8PDoW<*0'%#?tA7>?b97>Q.e(=ohc.q,m`#
=oDP'=U//7>$G36>$Lu/'N\72:J=>L6U*[o2D[$:-6aHI'+XI>JcP6R.i0<V0KDBg:/b;$@V0@nEcu_V
J:`B*K7nu7LPUhGN/j!^PE_>uQ^F/.Rf/^+R[KP1Q^3o$PECreN/NRLLPLV;K7ec-J:2]dEc5])@U3,8
:.IQ00IS%[n.^HSmgL'D',V`6-7gi,2`a)f6q9jE:K(=s=]ns4>Q.e1>?Y37=]nl+=TV].=SZ)$=BSc*
=U%u2=^,'5>Q.eD>?Y66=]ea(:JOMP6pWss2`!-=-6aHI'+XL?JcP9S.Ma*Q0KDEh:/kA%@V9IqF*Dt[
JV&K+KSG8;M2I4OOHPinQC+,3StD^NrhKLhrhCs;Tq@pIR[BD+P`h,gMi*@HLPCJ7JqAQ&HZsOMCM.*^
=]A-d5rgVG*u=A\JcP9K'+PZm*?lm_0JbXK4[DJ+8kr5`=T2J#>5hY)>5hY*=BYT+!EiV'=TM]/rE95)
=T2G'>5h\%>5hY<=B/3n8k2984ZYMX0J"Ur*>fG"nI1!LnJ!Mi*[WO"5Y"RK>$b]PD/aTAI"6g#K7nr6
LP^nIO-#QjQC4;9U8+Q_WN*)'ql_QRXfA@uV5'cWSXGe/PE:iaMM[+DKnP)1Jq/5nFDu)1@pW><:.IQ/
0.%_Une?ZUnI-$?&f2Q4-7gi,2`s;k7S-9L;cis$!ErY)=p%o2>$1c,#[1A7=^"p0=^(W(#[1A5=B\j2
=^(f-"'A`1=oDM)=^(f-&m&"-9M%]?5<Lk\0J"Op*>]=undL*MnJ!Pq,qh2B7o!#e?=[_dEHQPUJV&K+
KnbA=MN!UYPa.W+T;&-ZWiiS3['d?P\G`ri\@AuT['Hp=Wi)YeT:D:6P`_#cMMR%BKnFu/JUVleEG]B"
?<U<'7mK!d,ouOjaT)hP#7M(U',22t'GD,i$jQgU!:^J)&f)K3-7gi,3'9Dl7S-9L;cd+)>?Y68>$>'4
=]nj/>$>'4=BSa.=]ea-<EE4$<E<1$=BAU-=BJ^.>$5$4=BJ^/>$5$4>?b98=]ng):J=>L69dRl1GC@,
+rqF3ndL*Mne<Yp,;(o?7SZod?=[beEHc_XJV&N-KnkJAMiNm`QC4;;Uo18tZF.3R]YD8!_Y_5&_SX+'
]XkV\ZEUC-UnO?IQB[JkMi*=GKnP)1JUVohEG]E#?<U<'7R/mb,9-4gblA%N$546t,Uk5r0`<a50J4k)
,T[d9$3AEPndHHK()nDD.PNY;4?l/%8kr5`=BSd1=^#!5>$G2/=UnV<=BJ^/=B/C$<Dugm;#O0":f((h
<E*"!=BSd/=BSi+=W:OK>$G06=^"s2=B/3n8k)-34?#&M.OH;X((g'GaT)JF!X&Z2#Q4o3#6k>7!WgFD
o+Whr+tGN6786]`?=[beEHcbZJqAW.L51VCNKB?hR%9tJWNWV8\\,_p`5]m>b0/$-bqI;ab/hZD`59=&
\[A`HWMQ>[R$EksNJ`RJL4k22Jq&,kEG]E#?<L3$76NLY+r^(fci>'e$ksa2/Mf@L6:FC:92/,R8k2<:
4ZG5L-ls'2!RUo=$lTZk)^$IW0JbXM5=.h3:/P"n=]np4>?Y04>5hVO=^#!2=]ng,<)Z[j9M.fF7RTX3
6UX=37n60F:Jt+j<``F,=B\m2r`C(B=]np4>?Y04=B/3n8k)-34?#&M.OH8W'G0mFblA%N!sJu=%M06d
'E&UO',(ui%LWRH!s-[IoFrtp+=T*/78$Q^?"7PbEHcb[JqAW/L51VDO-5cqSY;m[YdCpQ^r+.4b0A/R
b0%iJrQ$\7ai_cLbg";O`P]L(\?rK?V4jHIQ'.2dM2-h>K7\]*HZsIHBOb=N;bKPA1FXF^ob>gS%0mOd
-nmSE7SQKI8P)JM7L_m[8P2TL7R&gc-l`m*dfB"K'b1lo+=&Eh1H.?Z6:ORB;,p_$=^#&->5qb)=p\;6
=B/@":ejPNr^6ZO!(6]N!(6`Pr^-u]85;u[<E3.(=T2M(=o;G<>?Y04=B/3n8k)-33]AiI.O6,R'+X[D
ci=:O!s]2E&el#Y')rRB&H<@A&dK'a&e>?T!s-dLoFro!-87GH8l8Yq@VBUuGC+jlJqJc4Ll..QPEqZ0
UoCQ)\A#i!aNVlOaiMNC`PqYk0#bfMa2lBGbfn/I_S3OeYGn=iR[0/!N/EFHKnFu/J9uK[D.mBa=&DRV
3A;d$ob>mU%gNaj.lKU`9M.fE6pj:,62NnX5s[h)6q0[=9M%H,.j,K1eH#4M&eZ!$+t5-"2EEuf7S6EQ
<EWI/r`U1D>$4s1=^"s1=]ea+;boqQ6:4(*6:*t*62Ntd5smq)6:!q*6:FF@;cZt%=]eg1=]nj0>$Lu0
&mJLA<DcL`7RB7!2DQm6+rqI4oaK7K$ipM=%hfTh&J,H`%M&:D$k<aX%hK<c',(l`"Td'Pob9%u,;(o?
85E;l@V9OtGC+jlJqJc4Ll.1RPaJ#9VlmA9^;@n3c-+5Ma2Q!6_SO%`^G!C=_SX71a2lBGc,muB^::MP
VkT`LP`Ul^Lkg\:JqAN"GB%J4@U)u183f*d,9-=jec69b'-Jkd6VU0C770@+4Zkee3r:oL3]]>b4[;;#
77TsE6T?Y?'*QneoaEPd()nGE/29%D4[DP/:/P"o=^,'5>?Y05=]np2=BSa+<`2U\6UO1-6:4(,6:*t(
6N'2)6:!n(6:4(,6:4(,6UjaI<`N7)=BSg1=^"s3>?Y37=]na%:.[o@4ZYJU/1)P[((g0Je,TpY"UbnV
&ePZc%LigS$hju;$O[@P%M'*_&ekoc"Td-Rp(T7u+=]637SZrf?tO1nFaAOhJqJc4Ll.4UQ'n8?Wj/tE
_SsXAb/hWB_ns7)^:h1k]"7jN1:k'/]Y2%o_8=+/a2lEJai2-0[BZa-T:(t-Nf&XJKnFu/IsQ6VCM$sY
<DH"H2(B^bpCu3Z&e#a12a^817RKI*4?GP_2Dm?Kq_JjA2)R9O3BK>e5sn+59LC`h*":I"p'_QD'H&#?
.PN_>4@)A,9Mnel=^1l/!a/Z0rE0h:=]\U%:.@Q:5t"(+6:!n&5s[b#q`ki[5X@\$5s[h'6UX4,6:O^K
<EE=*rE02*=]t`-&mJL@<DcF]6pWpp2)$L,+;kk'pC,UQ%06V?&JGcg%h9!U$OR4>#l+`)#Rq(I$O[@Q
%hK?e&ISaCf)YLY1*%\s4$uY>=^GZSE--ASJqAW/LPUhJP*MK/VQI28^r4==b/hT@_SNt"]=PP_\$i]P
rNn>f[C3QS\[oDd^;7\)`lQ<IaMbm)Z`U*tR['%rMMR%AK7\](H$""?AR8M<91(cr-6;gpf`2`j'IGY'
91_Q>5Wq1g2Dm9G1,(@60)[=9/hSe21,:XC2E3c]5XRt390tH^'F!1jp'_NG(EOhN/i,FK5=A"8:K:M"
>Q.e<=^,$2=]nj1=&r9q7mo[26:1W:r]goW5!V=s5X%@s5<hCs5Q3qH5nQFM6UF12:/k7q>$+p1=BSj2
r`Tn<=]e["9LhK84?#&K.3orN'))gd!"f8=&/5`g%h/pT#m^eA"pP57q#h95"U5/9#RC_E$k3^Y&ekl`
!n[MI'J^RO/iQ'e:fgq4C2\0<IY!*'KnkJANfo]uU8P3&]>DV4bK.`B_S<gs\[])UZa$a<YPYIXYHP18
ZaI6O\\#Sj_SjL9bKS#D]="i?U7@O4Nf&XJKnFu.IX#pPC1CON:e3l2/gD;0'\WO%$6(QY8kMQ?5Wh(d
2)?s?/h8D#-n5$@'e(O[.P!,)0f([G3]oYp7S697/K5&O!;?m6&/?-.-nI,33^5u%8l&Af=]t`-%U!"=
=^#!2=]e^&912-5r^%#Z5sR\"5X.Is4[)"lrB(KO4[)"m5<hCt5X7V$r^%)\6UXRF<`iL,>$>'3>?Ur/
&Qr7=;bp"U6U*Ui1+Xgt)\iqZ%+tUk!<ioG&eGQ_$OR1G#6k>7rWDlrrW<Z6"U52;#mq%L%hK?e%0Zdd
!;I6b'd"k]4@W(G>[_;_Ed)n^JqJc4Ll.4UQCO\JYdV9`aNVcH`59:&]",;XZEUO7XK/A#WVrbPWMunu
XKA\1ZF.0P]"Pkq`5g!DaMYa#Yc+:dQBI8cLkg\:Jq8;nEc#Ju>ZFNg4>\K/(&8Qu!&G*/1."Z)6pWss
2`*9D/h8A"-7'o^+X&$P+<hsP,:"Q^.4Hc"0JYID3^#bs7nc6)+qEB.p^A8S',Vi;.PN_?4[MV1:/b4s
=^#!5=^,$3>$5!1=B/-er^-NK!^Jq6rB:'@"$/M*3rD,=4T[i35l<qI66Jco<EN@+=^"s2>?P06>$5!0
;bfqS69R=d0Ie=j(_[AS%,([l!XK>N&J#<Z$4-qA"U"o0rW;`nrW3T4!sAc3#71_G%1a$a&-r?l!;I6a
)^I's6;(6[?Y4(mG'eakK7nu8MN3jcS>*!f\A6)+bfIc@_7mOl[^36BXK/>!Vl$;dqkGsCV5C/hWN3,(
Z*UmL]=u,!`Q69G`PB!gWM5oKP)bEUL4k22J9uHYCM$pV;bKG;0-hM4'\i[*'.,V,91D644#]#P0.eY&
-6s`X+<MUD*;pfn*#ot@+<_mR-7LDr0/>CD4$Q&$91Cfd'AWZ`$ks6g*?lpb1H.E^6qL-N='8[0>?Y50
=U826=BSX':I[\K62j+W4[)"j4?P\d3BB/\rAY0F3BB/]4$5Ve4[)%.5l<nR6:OaM<EWF,>5hY=>$>-6
=]ng':.[i>4?#&K.3]cJ&GZah!"T,<&eY`c$k!@I"pG,3!Wr?%o)Saj%fue7"9eu8#n%.O&/#T_!o*eO
'JgLI.Q'C[:KLh3C2\3>IY*0)L51YGP*MQ3WNitK`luTH`PTC&\[JiKY,nY%VPKu]TqM4Q1SCG9USOcc
WiWA-ZaREW^V[t2b07l@\?W$-S<]7sMMHq>JqAGsFDko(>usfl4uFf4(A\a$!#?4r3(cb35s..d1,1@0
-RBu\*ul4<)?(HN(&f!b)&aG6+!DjT-RgW%1,h3U5t4F<3%,Hs!;R$\%MK^&-7gl03^5u%9M\Yk=^#$5
>?P-3>$4s0=&Vd\6:!t*5sR\!4Ztni4$,G_3&`cU2`L[p2)dHR2`E]V3BK;b4?Yhk5X7V%6U=%,9N,"p
=BSg0>$5'5>?Y04<DQ:Y6U*Ui0e=Xo)&!MV%,:go!t,bT&.T'T#R1G8!s/N)!UTju!<E6'!sAc3#RUqL
&/#Wa"5NtQ'K$gT00)Bn;d3XBD/slLJV&N.LP^tOQ("GGZ+.Thc-")F_7mLj[BZm9WMcPeTq@pHR[KP1
rKfC/R$jD5StD^OUo12nY-GCE]"Z#!a3)ZH^ppYJU7.@/N/<=DK7\Z&G]IY6?s?Q'6THeF)u:9*!#cM"
3DE(55W^n]1+k%(,p=BO)]9J-'GD)j&cE1X&J,Qg'GVH&)]^%E,pt/p1,LpO5X\1<3@PX!!;R$9&/H91
.5*J:4@)G/:K(@u>?P20=pS86>$5!0;b]XX64#qD5<h=p4?PYcrAXp=2Dd9IrA4X52)I3MrAY*E3]oMf
5!M7t62a+U6VUK\=]np2=oMS;>?P05=Ar!f7R9-r1G:1$*>T4_%,Cmn":PtW%h/mQ#6k;5!WiDc!"K#2
!WrQ0#71bI%h]Qb"Ps.T'K-^L.Q'I^:fgq5C2e<BJ:`E-LP^qNPaS2BYdhKgc-")E^qI:eZEL@/Vkg&[
Ssu.7QBd\uPPpY&PEV5rQC"#/StMgRVl?c$ZF7<W^r+48c,RQ/Yc".^P`Uf[LP:A4J9uEXC1LUO:e3i/
.NfN(']/m0'.>k88OG[&2D[!:-m^#Z*#]V.'+tcb%1<ROr<jPO$k!LS%MBEi(E+89+XJNe0/GLI5!qn9
3@GR!!;[*8%MK^&-7go13^6#(9Mnko=oDP&=U/&2<DuUZ6:1Z;.6p-_4Zkhf3B9&Y2Dd6H1GgjC1GLU?
1GUdC1GgpH3&ioY3]oMf5<qL55mK_C6VUKZ=BPN)rE9b8=]\Ho8OPd(2DQg1+;kh&q[D<]%0I"Q&e>BX
#R:M8!WiD_!"Ar1!WrT2#RUtN&ek`VhZ3Ke2B*np3CQYA>@D2^F*W4dJqSl7MN<sgT;Jg#^rFXAa2>[)
\[A]FX/MkhTUhO=Q'@GnOH5E^rJ`^qNfT9`P*D6!R[p+GV5UGtZ*h-T^Ve+7c,RQ-YGRqZPE1TWL4k/1
IX-!OBOP+E9gq&q,THUphuF`,-9+^k6U*Uj1+k%(,U"0H(D[Yr%LWXO#m^_?r<4/G"pbMB$O[FV',DB&
*?c[U.P<J83^5u'9fOI0i;i]_2@pWE+t>6%3'BSt8l&Dg=^#$5=^"p1=]eg/<(oYG6:!n&5X.Fp4?GSa
3&WZN1GgmC0eb=8r@g,_0eb=;1GgmD2E!NT3]fGd5!M:t5s[h(6VLH]=]eg0=B\j2>?Y03<)6+U69R:a
0.J.d((g?OhZ+)e$PX3a$jm7E"9S]+!T="f!<E6("9o,=$kEsb$Nf5dq@kb..5a@]:fq"7CN+KFJV&N.
LPh(SQCXhR[D'W(b/_E7]XYARXJr(lTq.X=Q'7;jNf8jQq1pkbMN!RVOHYrrR[p.IVQ$Z$[CEla_oKpD
`P/a^Un*g7NJ`LFK7\Z%GAq>/?!'oo5W'u4q\7uh*"!lJ8kDB73AiND.4-2Z)]9D)%LiaP#R:J7"9\N%
*!6<G":#2>$4RIY()\&4+XSWj0f:sU6V0m.+q!<0q@!fH(a(.V1,h6Y6qL-P=8l>H>?b66=^"s3=B/-b
6:*t)5s[^u4$5Sa2`<QN1G^d?0/#$R/g;u&0/,.;1Gh!J2`Nl]4$>eo5sdk(6:=UL=B\j2=^"s4>Q.b7
=&Vjc6pE^k1+Xap(_ccThuF/e$54$_$jm7E"9S]+h#Iia!WrQ0#RUtN&ebTRi;i`h2AdYl3C6D=>@D2_
F*W4dK7o#9Mia3mU8kQ3_og-B_nWjo['6X1V4sQNR$EksNf8jOLP:D6qgp_YKS>2;MN!RWPEhN)St`'[
XK](A]YDA)b0%W6Z`9agQ&po[LP1;3IX-!PBOP+E9L:`m,8g=miW'i")_slS6p<Xh0e=b"+rqR<'+kZ\
#mLM8"7lL1!sA`2#mq+R',MQ-+seZh0f;!U6qKj')[57%q[<uF(*+SJ0/GRN6:XXD<EWK&>5hY/=BAO!
7RBH?5mKY<4?>M`2`CXk!\lD_r[n4'0)7%!/hR&T!\Z8_r\Fd;2`Wo[4?Yt.5mBYB7Sd&d=T2J$=q=b=
<DQ4W69R:a0.A(c((U6Ni;a8e#SIa\$jm7E"9S]+gAhW_!WrQ0#RUtN&eYHNiW/ii2B=2%4\&:L?"@_i
GC4snK8,/=Ng#j&Vm3eLaNV`D^q@1aYH4[uTq7[<P`UoaM2-e;JU`,sqg:;MIXcp$KnkMDNf]KkR@^+J
W3!20\@fVqaNV`B\Zr')R$3PfLPCG5JUDZ\CLg^P:IdQ(-lr4"iW'`./OE*"5WUhY/1;ef*#TJ(&.AjL
"9Rrj('4UC#n.@X'c@u7,q(<!2`a2n90t3EirJrb%h]^%-7gl03^6#(:/b7t>Q.f#>?G$3=BS[&7m]L.
5sR_$5!1ne3&i`M1,1L:0.nh..k3%u.Olkr.Olkt.P!)&/h\k40etL@3&io[4?c"r5X@_&6:XpS=]ej1
=Bkc.%pN1=;bfnP5<:\W/1)JV')W0s!"/rA&eGKZ#R1D5!WgsS$NU;1"9o,=%1j-`"l]O['K?jO.lTae
;d3XBD0'uOJqJ`3M2[O]SYWEr^rO[?`PK6u[BQa2UnO?HQ'.2eM2-b9IslZiH$FT4G;shlH$XgcItE?/
M2[LYQ'e,9Uo:B![CNue`Q69D^pU>@SX#:qM2-b:JpqudD.d6\;bKD8.j5],']T0.$mn&07R9*o1+k"%
+WM@6&.K!Q"p4#h'`nRF$OmX])&seG.5!A64$c>04!kBs!;m69%MTj+-n[;84@)G/:K:P#r`URM>$+s2
=]\Qq6:!t*5sRXt4?GP^2`3BG0JG.4/hAJ$.4Qbpq^Ed=.4Qbq.P*2)0/,(71Gq*M3BK>d5<qM$6U=%3
<**4*>$+s2r`Th8=]J6i7R9-r1G1(")\`e\%,q6p!Y#eS%LWOH"9S]+f)Q0Z!WrQ0#R_+R'+G$'!;mNj
)^I+!78?lh@qot+I"?m%L51YIPaJ/DZb46#ai2-2\[AZCW2-,WR$<\mMMQt<IscQfGBJ"LF8^,=F*)MH
GBnL^ItEB1MN3d_R%0kGW3!52\\>r"aiq]9Z`0XcP`L]WL4b)/I!0ID@pN,176<1K)uUK3!#5VV1eL8)
4?#&K.4$,W)&*Yl$3gS5k5Z4t!sT)A&JQ*&+XJQi1H.E_7nPTX"Q]X]$kNsc*@**g2**ri8PW2c=^,,0
=rUUH>$+m,:..E76:!k$4Zked2`ENK1,1I8/M-lN"XtlK-R^>?-NthF-R^Dmr[Jj</M8_31,C^F2`Wr^
4[21t6:!q+:/tCu>$+r,=qFnB=]\Ns8OPd(2)$L**>T4b%-%<q!=9DO%LWRJ"9S]+eGosX!WrQ1#n%4T
'+"^#!;mNi*[rs385WMsAo2U6IY*3*LP^tPQCXkU\AH>1a2>['[]up4UnO<GP`Ul^L4au'H?aUTEcH&:
qee?2E,fo?G'J=\ItNK5NKBBlS=uaWXKf4F^Ve+8aMPHjV4Ep7N/<=CK7\PsF)>Mr=]%^T2(9UF']]6.
'.ZCA6pE[g0.J:m*#TG&$OHt>!Ta:q!<`Q4$P='j*$Q^Z0/P[R6qTcr'BK5l$k<me+=8Wp2`sAq8l&Dh
=oMV'=U&&4<DuCT6N07c5X%7j3B&lQ1bg[:/hJP&.4HYm-RSg=r?V@g'db:U-n-Sp.P38*0JPC?2`E`X
4$Gq/5mBYC7oNDj>5_V'=q4\<<)6+U5s.(]/1)JV')`7!!"&l@&e>BX#6b22!Rh#U!<N?,#7:kM&eG6-
!<!UU(*P7h6;(9^@VB\%H[p^#KnkPHPaS8G[(aN(aMbp-\?`9:V4jHIP`Ul\Kn=c#G]n1KE,KK-C2.Hr
BPD3rCM[m-EHHAKH[^R#LlIL\R%9qIWNNP:]YVP-c,IE(WhPoGO,A^HK7\W#FDki%>?"6^3%Z9hrY4Go
'*fX=91D3/2_lm2+WVF7&ISmK"5s4n!sJu=&JQ*'+X\cq2`j;p9/dsujoG>g%1X'k+t5-#3'BSu9Mnmj
=oMS0=]np1=B80br]q)\5<h7m3]K&S1bg[;/M&D$r[@^k![K$9r?MFh,UE=7r[8=,.kE8*0JPC?2`Ni\
4?l%qr]pcW:0(J!>$5#->5hY6=&DX]69R:a/h%t`'Ft*Nj8]Mf$PX-]$O?q=!Wg[K$3:21"pbSG&/5KQ
jT,5nG8__\4%2qH?"@_jGC>'pKSG;@OHlB5YdhQlb/_B5\[AWAVP9ZLP`Ul\Kn4YtGB@kDD/3m!An>L_
@U`hXAS,RiCMe!1FE`"YJVAo>OHc-(U8Fup[CO#ga3)N@\?Mg#Q&po[L4k/1I<T[HA6i8476<1K)ZCH4
!"oqs6V0X.2_m!6,T[j=&.JpL"5a(k!sJu?&/?*),Ub9#2a'N"6S/u6!<!<[&/H93.PNbA5=A(<;cm1+
>?Y05=BSg1=&MLS6:!n&5<V+j3B/lP0eb43.k2ts-iPCo,U4KWq&oha+sSB4,qp\q.4Qi"/hf"92)mTX
4?Yno5s[h(7T*;j>$4s2=^#$5=]eTt8OPd(2D?U+*>T4c%-.Bp"VD@X$jd+@!WgUI#lt)1"pk\K&eP</
!<!Tl*@Nd/85WMsB5M^8ItE<,LPh+UR%^Ld^;eF=`4rpmZ)jn!Ssbk*NJN:=I!KmVE,KH*An>F[?srq>
3FH6'@V'.cCMn*4G'SIcKnt_MQ'n5=W3!84]>);*c,RK)WM,]CNf&UGK7\SuF)>Ps=]%^R2(0LE']oB.
#9Z!"76imk0e+Im)]'+r$3^Of!"oA;$4RUa)^-LW0f;!W78,Z`#Nl*c$nr;2+=8Wp2`sAq8l&Gi=^#$6
=]ed0=BJTt6:4(*5X%@o4#f/U1GUX;/Lr;"-mg;?,Q]/5+nm)\+X&'/,VUPl-n-Vr/MAe51Gh$M4$5\j
5X@_(6;1E^=B\g/=^,*6=]na#8k)*/2_lp2+;baj%-7Hr!=KPP%13@E!s/ML!"&`.!sJr;%1j0\!TjC]
'KHmN.lTae;d3^EDfg>WJqJf6Mia3oUp%;Fai_W?]t(JPWMQ;XQBI8cKn4VsF`MD:C1_!c?s[,Dqcc-e
?!^oJA7fRmDfTuGI"@!/Nfo]tTr"`kZamcd`lcE?\?D]uQ&po[L4b)/HZX1>@Tuc(5rU;:'bhAE!"fkp
6V0X-2_ls4+WVF6&.8[FgAhfg"q(tU)''kJ/M]7I6V0Wq'B]Ap$lTQh)^6[_1H7Na7ncc]=^,*6>$5!3
=]na'7fG^o5sRRr4?>DZ1,1L9/Lr;"-mp;e,9e9S+<VaJ*W7'"*?Q@G+<_mP,:+W`-n-Vr/M8b41,_'P
4?Yhm62j1j7T3;i=^"s2>$>-7=]\Ho7m]?u1G1(")\`_i%-7Hq"VD@X$OI"?!WgOG#lt)1"pkYJ&eP<0
!<*Zl(F(Rn786cfA86+.I=d'(LP^tQQCk(\]Z&.:`4rmlYcOasSX>Y&MM?_2H$+.GCM79g?sR#A=T2D%
<rQ/]=Bf$9?t*\\CN"69H$t:!MN=!hSYE$`YdV-X`5p3B]X"H-QBI2`LP182I<T[GA6i2076*"G(DISH
!"o;P2GHY+3]&K=,p4*A&I\pI!S[Se!<WT9&Jc9-,q1K(3^?//2B2b]!<*B:%M^!..PN_@5=A(<;cm3#
=sI0R=BSX"6U=%)5sRRq3]T)S1,(C7/1N(s-RC#`+X%sN+!:J&!ur=%*<$s'+!2UJ+X&*V,pt)i.P*2+
0JYFB3BB8c5X@\&5snFJ=]ej1r)sY7=Aqsd6pE^i0Ie7e'b1W\jo>\g$5=$\$3p_:!m:QM!WrT2$4RO\
$3B;irY.@3,r.SP:KLk7CiaiMJqJc4MN3phU8t];aNMW@^:CSQWMH2UQ&pr\K78)hEc5`,ARSnM>?Fs,
<;TTU<)m"%>?kKCAS>jtEcu_WK8>JJQ("AAWNNS<^;@q6aM>3aU7%7+MMHn<JpqrcCh@!T:I[H%,o[O4
k5Z)&-pL<k4Z>,J-QsHG'+G6P!SIGb!<ri>',MW3-S7)44[Vh+-k,;>rX9;K(*4_N0fD-[7S6HV=]nr-
>Q.bE=BAEl6UX4+5Wq4l3B/oQ0eb43.k)ko,pXZXrZD4`*VCBQ*?Y8')^-CN,pt)i.kNA-0f(^I3BTDe
5X@\(6V(-W=T2J&>7k%A=]J6i7R9-p0e+Fi((^l_jo>\i%h]B]#mLM6!QtHL!<NB.#n.=W%g1tprY.@5
-oF7\;-@7=DKL2TJqJf6MNF*oUp.DIbfRi?]=5&HVkT`KPE(KRIsZB\Dej#s@9m)>=&`*r;>X-N;,U=l
='/[4@:X%eDf^,LIt``>PF%i7Vl[/4]>;J.bepuqUn*d4MMR">Jq//hDJ*<Z;+Nr0-lj!9k5Z&-0M+c#
4#J]A,p4*A%LNCAf)Q?`"pthU)B^=U0JtpX7nGBKkQ(Sj&/#g&-7go24@)J2;--n'>?_#/+BhrO<_u4O
6U<t$5!1kd2Dd3E0.eY&.4->d+sA'O*?G,!ru:hRru:qW+<;IF+<_pS,pk&i.P3;.1Gh!M3]oSi5X@b(
78R#f>$Co.&mALB=]\Ho7mT6r1+Xap(_R5cjo>Yl'+k]^#R(;3aT)SJ!sJr<%M9?Zk5bJqHQO^r5tb-\
@VKb'I"?m&L5:eNQCk(\]Z&+9_nN[hYH"CkR[&tnLOsnuFDu&0ARSkK=&i0s:JOVX9M8#P9MJ8X;H$Rs
>?tZKBP_X0G^P*uN0'<nTVSTk[CO#haNVW9ZDX=\Oc5*NKS"c&F`1u'>>n0\2_#m`'^,N.!?O0n76W[e
/h&"c(D@5a"P!J`":5MN(EF_L/i,IN78,W]!U0U`$kO'i+Xes!3'BSu9N,%r>$G2/=X.$O<),bI5s[b#
4Zb\a2D['@/hAJ#-RL,b+WqjK*?6%<)B/Smrtt_Q,8qI>*#ot@+!;^P,pjuf.P*2*0f(^I3]fJh5X@\'
6;CK_=T)A8>$G05=Aqm`69R:a/gqk\'+><4!!rZ6'+bQY#6Y)/aT)VJ!X&`7$kO'[!U'MF'GM<+/N?'k
<Es$KE-H_^JqSo9NK]a(WjTX`bK%K5\?`37URmj:NerF=H?O=HC1UgZ>?=d&:ej\Wr_!2^r^o4E9MSD_
<**:0@:a1kEcubYKSb_QR%C(OXKo@L_T0j@^pU2:R$3MdLP182I<TXF@pE#.69$P?()5s4&.0gM8jb^!
0e4Ro)\iql"p3<T&-<%C&JZ6.-7^f/4@;_*-jo2=&.AsW'H85F0/YdT6qL-P=BYW,+^%oL>$4s+84,^1
5sILo4#f2U1GLO8.Ocbl,U4HT+<MX'*"<f((`4&*(`4&*(`4,/rZ!(%+!;[L,:"Q`.4Qr'0f(UF3'02b
5=%V&6V(-X=^"p0=oMS8=]eTt8OPd(2(pC&)\`bjk5Yej%h]B\#R1A4!Qb<J!<N?-#R_.U%g2"qIM<J4
,;2&G:01_4CN4TJJqJc4MN=!jUTV,Db/qW=]=+rEVP0KEOGnsHI!BaOCM%$^>?=d&:JFJS8Ou?A7Rfm<
84cEH9i"\e='K!?Anu=,G^P*uN0'<oTr+lp[_'Apai_K1Y+qGMO,A^HK7\PsEGK,k=&)1E0-_8IkPu,,
/Oi5r4#JZ?,Tdm;%LN?f!"T2>%MK^%,UkB(4$cA+/JI@K'FYB['ceJJ0K(sV6qU6T=]np4>$Co.,?RrD
7RBI.5X7Ip3B/oQ1+t72.Ocbl,U+BS*??+>)AsA0)#b?O(B,*p(`=/.)B9_;*?ZLL,UFif.P*2*1,CgJ
3BTGi5X@b(7T*5gr`Kn<>$G05=]J6i6pE^j0Ie7e'b1Z8!!i`@&Io0S"TneG!!iT-"UGJG&J>92!'M#1
)C."!7Sd)lAo2U7J:`E-LPh1YSY`R%`6HBA^UghVWMH/SP`L]UIsQ9XD/!Ng>Zb!):JFJS84Q-<rBrb:
7Rp!A92/2\<ENL6A8,n#G'\XlMN=!iT;8Hi[CX,kaN_Z7YbdnUOG\gIK7\W!F)>Mq=&2:J0I.PNkPu,1
1JL>(3AW9:+WM=3$O?ma!"T2<$PF9r+Xo$"3C$&.1Do?V%L`aW(*+YM0fD-[7S6HVr`LRO=^"s3>$+g$
6U=%)5X.Co3B/iP0J4q,.OZSh,U"9Q*?6%;)&W>h!>ZLM'`f*drYHmu)&aJ7*?ZIJ,UF`b.Om#&0JkUG
3BTGh5X@_&6Vg`b>$>'4=^(f-%961r7R9-p0e+Fh((^o;!!ilF&.JsO"9JVE!!iT,":#8C&/5H7!'M#1
)^[='7o3>qB5Ma:J:`E.Ll7@\Su8m,`luK?^:CSPW2#rNP)b?NI<fpRCM%$]>?=a"9hS)K7R]^4rB`V6
6q'R88kVlT;cd1/@V0CpFEr:eLlRXbSti6dZamfea3;Q9ZDX:[OGepLK7\W"FDYYt=\qUO1+!qSkl;7q
*]Qb[4ZG2I-6X<C%LWLDdf9p]#7D"V)^-OY1,qEb8iRjml2VD)$kO3r-7go24@)G1;--n)>Q.eH=^"p1
<(oYG6U3n$4ZbV_1c$d;/L`+r,pXZX+<US&',_W'(D[f%().Ju'G_E"()Ic()?(Tr+!;^N,UFfe.4m/*
1Gh'N4$>en5XRk*9N5.s>$:i.&6i@A=&DX\5s.(]/1)DS&.Ib&#ltDI%h&aK!s/MD!!rZ-!sT&?%hf<O
kl<j\'HAGV5"JRT?taJ#H\$d%L5:eOQ_17a^W=U=_S*IaXJ_e_QB7&\JU;T]DJE`j>Zas':.n2L7RTU1
62a&25sdq,7Rp'D:/Y%n>[V,XDK9rJJVT2GQCO\IXKf:K_T0j@^U0u4Q]d;`L4k/0HZX1>?s6H!4u=T+
'^5T.#:)H)6Td7^.O?2U'FkHQd/X[[$4[ag+=/Km2EX?!4X:9t!'L`!&JuW;/2K4I6:X^H='8^2>$5$2
>$+m,8jbp35sIOq3]AuR1+t71.4HVj,9\0O*?6":(`*u('c%Mur=_:-'c%N"(Ddo,)]Tk?+<_sU-RgJp
/h\t92`Ec[5!M>"6:4@F=BJa/>$5$5>$4p(9LhE32`!!3+;b\!kPtki%MB9[#R1A4`;g/F!sT#=%h]?R
kl?/H'HJV[5Y=sZ@;'S%I"?m&LP^tQR%^Of^rX[;_7[7]X/;SZQ&plWIsQ6WChI3`>?=a!9M.iF6pj7*
5!D+m5!D4t6UXC78k`#Z='K!?B5DR1H@LX+OHl9.VQ6r1]YVS0aM>3`TpV"%M2-b9JUDTYBOP+D8jG6_
*>I`<%h'mR8O>Kr0I\7h)&!Gc"47)Z!soDM)':+Q0JtpY8O=j7l2V>'$kaC"-7q#54[MY4;cm3$=smHU
>$4p*7m]L.5s@Fo3B&iP0eb1/.OZVi+sA'N*#ok7)&F&''GU?V-PR70()Ii+)B9_=+<_pT-7C>n/MJn8
2E*WY4[).u5se(?='8^0=oDM6=]eTr7m]?u1G'pr(_R8@!!i]=&e59T"TneE!!iT-"UGJG&eG31!'M#1
*[s$692f&(C2eBEJqAZ2M2d^dU91l@b0%]=]=+oCV4a6@O,AU>H$""@AmntI<DlRb8Ol3:5sRUsrB*2*
5!VD$77TpB:/Y(o?"%>\E--ASKSkhTR@pCVYI;'Y`QH??[]Q9kPDtESKS"f'GAq;+>>n0\2^od^kl;5(
.n*#q3]/Q=,97R5$O?m_!"T2<$PF6r,:G3&3^H>+.h:hE%h&jZ(a(4Z1H7Nb8PW2d=oMSP=]ed/>$+]n
6:4%(5X%:k3&EHG/hSY'-RC#^+<MUD)]BP1(Ddi$pCeng()Ic((`F>5*?QCJ,UFfe.kWG.1c.0Q4?c"q
5smt/;cm4+=BJ`*=q4\;;bfnO4ZG5M-m0?>%-RZs"VMFX$4$e:!Q>$E!<NE1$4[[]"mH"M'GMT<2aL)9
>[hGeGC>'pKSPGFPaS;L\AHG2`4rmkYGn=hR$3PdK7.udDe`ik>ZXm%9hIrG6pa.&4Zr[)4?Pbj5XIk.
84lTR<**=4ASQ.*G^Y4#Ng#j%UoCQ*]"c2*bJLclUR@@,MMHk:Jphi^C1:FI90kKd*Ydi=%hgQc83o9n
/h%q`(D$uZ"47)Z!sT/F(E=SI/i5UT84P9Gl2VG*$kaF$-n[;94[_k:;cm1+>$Lu/,?e5M:e!c;5sIRs
4?5>X1,1C4.4?Sj,U"9P*?6"9(`*r&(&emD')iOh'bqK#(`=84*?QCI,UFie.4d)+1,V!N4?Ynn5s[h,
:fUY%r`Kh;>$>'2;bfnP4ZP;N-m9H@%-RZs"qqRX$3p\8!Q>$E!<NB/$4RR\#3c+N'GMW>2aL)9>[qMf
GC>'qKnkPGPa\AN\AQP4`4rmkY,S1fR$3PdJp_faDJE]i>?=a!9M.fD6U<n!4?NL&4?GYg5!_P)7nQHN
;cd12A86"'G^P+!Ng#j%UT(E(]"c2*beproURIF-MMHn<Jphi^C1CLJ90kKe*u*r>%hgWh7R/si/ghe^
(D-uZ!RUlX!=&rD(E=SG/i,LQ7S5?Jl2VA($kjR(-n[;95=A(<;cm1+r`f>(**5s)6UF+)5<V+h2`!6C
/hAJ$-7'l[+<;IB)B&Mj!u;Ra'DW7C',)&qrYHOl)B9b<+<_sU-7LDq/h]":2`Wu`5<qP%6UXaPrE9,(
%pE+<;bfnP5<1MP-mBTD%-RZs#87[Y$3p\8!Q>$E!<NB/$4RR\#O)5;'GMZ?2aL,:>[qMgGCG-rKnkPH
PaeGO\\l\5`4rjiY,J+eQ]d>aJp_c_DJ<Te>?=a!9M.fC69m^s4$#D_3BB2_4?c"s6q9jC:K(>!?=R\e
Ed)n_LlIRaSti6e[(3riaN_W5YG7SOO,A^HK7\PrEGAug<(oS:.j,Pj!"Tc!91D0,1Fsmt)\ihj"Td!N
%flhA%M]p,-S.&65XnC%'^>Z3$k*dg+t,*#3^6#(:/b:u>$G37rE17D:IIK85sR[t3]K)U1GCF4.Oc_j
,U"9P*#ok7(Ddf#rY,8Drt55CrY$Cg'c.Z()B9_=+<`!U-RgMr/hf(<3&ir]5X7V%5t"LLrE0b:>?b96
=]8'e6pE^g0.8"`'+F()#RD+U%13=C!Wg7?#QXu0#7:nO'F<q'4VSSQ.lTdg<Es$KEHck`K8#)<O-H-0
YIVQqaMbj(['$@&SsPV"Lk:"uEc,Q"?<^H/:J48K6pa.%4?GV&3BT>a4?Yqr6q0a@:K(:u?=IScEd)n_
LlIRaSti6e[(3riaN_W5YG@YPO,A^HK7\PrEGAug<D5_<.j,Pj!"Ti#9h%B.1Fsmr)\ihj"Td!N%flhA
%M]p*-S.&65XnI'(?tl5$k*dg+t50&3^6#(:/b:u>$G06rE17C:I@E75sRUr4#f2V0eb42.4HVi,U"9P
*?5t7(`!i$rY,;Er=T&BrY$Ch'c%W')B9b>+<`!U-RgJq/hf":3&iu^5!VD#5snFJrE0b:>$G05=]8'e
6pE^i0.A(a'+F()#RD+U%13=C!Wg7?#QXu0#7:nO'F<q'4VSSQ.lTdg<Es$KEHck`K8#)<O-H-0YIVQq
aMbj(['$@&SsPV"Lk:"uEc,Q"?<^H/:J48K6pa.%4?GV&3BT>a4?Yqr6q0a@:K(:u?=IScEd)n_LlIRa
Sti6e[(3riaN_W5YG@YPO,A^HK7\PrEGAug<D5_<.j,Pj!"Ti#9h%B.1Fsmr)\ihj"Td!N%flhA%M]p*
-S.&65XnI'(?tl5$k*dg+t50&3^6#(:/b:u>$G06rE17C:I@E75sRUr4#f2V0eb42.4HVi,U"9P*?5t7
(`!i$rY,;Er=T&BrY$Ch'c%W')B9b>+<`!U-RgJq/hf":3&iu^5!VD#5snFJrE0b:>$G05=]8'e6pE^i
0.A(a'+F()#RD+U%13=C!Wg7?#QXu0#7:nO'F<q'IM<J=.lTaf<*WpJEHck`K8#)<O-H-/YIVQqaMbj(
['$@&SsPV"Lk:"uEc,T$?<^K0:J=AN6pa.&4?GSb3B9,]4$5\l6:==99M\Sg>@(fRDK9rKJr#DLQ_'tN
Y-baS`5p3@\Z_ctP`L]WKnFu+GB%A->Z=B_2_-!bkl;5.1.t)%3AW36+r_7,$O$RY!"T)6$P!mj+t"ot
3C$&,1)0!R'+>9_)^6[`1HI`g8PW2e=^#$6>5_SH<_l+L6:!k%4?>G]1c$g</1N(r,U=NU*ul7=)&F)(
'`JdF')rRC')iOe'GVE#)&aJ7+!;^P,UY#j/1rV41cRHU4?u.t6:""8<rH/6>$G37=]nZu8OPd(1G:.#
)\WYD!!iiE&.JsO"9JVC!!iT,":#8C&/5E7!'M#1+"K9992o/+C2eBFJqJc4MN3piUT_2FbfRf<]!\]?
URmj:Nei:7G]Rb;A7/VC;bp(Z7n#d25!;"irAm&&4?Yko6UaL;9M\Sg>@:uVDKC&MJr#GNR%C(PY-kjU
`6$6@\?DZrP`CWVKnFu*GAq;,>Z=?^2_,sakl;5-0M4f"3AW98+WM:1$3pUZ!"T)8$5"'o+X\lu3C$&+
0GE^O&.As\)^6[_1H@Zf8PW2d=^(f/rE1+A:ddZ;69mau4?5>V1G:@3.Oc\j,9\0M*?5t8rYGYP'GD/U
&d&d\'GgZa*#TY7*??7G,:+Zc.P*2*1G^sM4?Yno6:+%-:fmTur`]n:=]eTt8OPd'1G1(")\WYD!!ifC
&If'P"9JVC!!iT,":#8C&JGE6!'M#1*\'*792o/*C2eBFJqAZ2MN3phUTV,DbK7];]!\`@Un3s;Nei=9
G][k=ARJbE<)H@^84H!65X%=nrB!,(4[)+t6q0^>9i4kk>[V,XDfg5PK8>POR%C+QYI1sV`6$3>\?;Tp
P`CWVKS"f(GAq;+>Z=?^2_#j_kl;5-/k8Gu3]&H<+WM:0$jHg]!"T/9$kO3p+Xo$#3^H;,/edLM&e#0]
)'LC\1H@Td8PW2d=^#!5r`LIL=]J9f6:!n%5<V+h2`3?F/hAG"-m^,^+<MUD)]9J/()7S`'DiFA'-e5,
()If*)]Tk?+<`!V-n$Mq/ho+=2`Wu`5<hG"6:OdO=^(f-&R/FB=]eTt8OP^%1G1(!)%mAA!!icB&If'P
"9JVC!!iT,":#8C&JGB5!'M#1*[s$692f&(C2eBEJqAZ2M2d^dU91l@b0%]=]=+oCV4a6@O,AU>H$""@
AmntI<DlRb8Ol3:5sRUsrB*2*5!VD$77TpB:/Y(o?"%>\E--ASKSkhTR@pCVYI;'Y`QH??[]Q9kPDtES
KS"f'GAq;+>>n0\2^od^kl;5(.n*#q3]/Q=,97R5$O?m_!"T2<$PF6r,:G3&3^H>+.h:hE%h&jZ(a(4Z
1H7Nb8PW2d=oMSP=]ed/>$+]n6:4%(5X%:k3&EHG/hSY'-RC#^+<MUD)]BP1(Ddi$pCeng()Ic((`F>5
*?QCJ,UFfe.kWG.1c.0Q4?c"q5smt/;cm4+=BJ`*=q4\;;bfnO4ZG5M-m0?>%-RZs"VMFX$4$e:!Q>$E
!<NE1$4[[]"mH#9'GMQ:2*ac4>@D2`FaSalKSPDCP*hrC[D9l+`PK3rYcFXpR[&tlKn"DkEGT8s?<^K0
:J=AO7RTU05X%@p4[)%p5sn%084lQO;cd10@V9LsFaAOkMNF*lTr+lp\%KPrb/hB+WhGfCN/<=CJq8;l
DeNQ`;bB;4.30,e!"TJe6:jL*2)$I)*uP_$#m8QT%g*+I',_l;.53V@6:jKm%-dg'$k*ac+=8Zr3'BSu
9N,%rrE:RO=^"s0;b'/?6:!du4?5>Y1GLR8.Olhn,pFKT*ul7=)&X5+'bhAX'.4J.'c%T')&aJ7+!;^N
,pk#i.P3>/1H%0P4?Yko6:!q.;cd+*=]t],%Tun49LhE43&<-5+;b_"kPtkk&/#H\#R(;2`;g/E!sJr<
%MB<Ukl<j\'HAGV5"JRT?taJ#H\$d%L5:eOQ_17a^W=U=_S*IaXJ_e_QB7&\JU;T]DJE`j>Zas':.n2L
7RTU162a&25sdq,7Rp'D:/Y%n>[V,XDK9rJJVT2GQCO\IXKf:K_T0j@^U0u4Q]d;`L4k/0HZX1>?s6H!
4u=T+'^5T.#:)H)6Td7^.O?2U'FkHQd/X[[$4[ag+=/Km2EX?!4X:9t!'L`!&JuW;/2K4I6:X^H='8^2
>$5$2>$+m,8jbp35sIOq3]AuR1+t71.4HVj,9\0O*?6":(`*u('c%Mur=_:-'c%N"(Ddo,)]Tk?+<_sU
-RgJp/h\t92`Ec[5!M>"6:4@F=BJa/>$5$5>$4p(9LhE32`!!3+;b\!kPtki%MB9[#R1A4`;g/F!sT#=
%h]?Rkl<j\'H8AS4\&=O?Y=4tH@UU"L5:bLQCb"[]Z/19_S3ReXf/"dQ]dAbK7/#eE,0&p?!CB/:eaST
7n,p86iB>66UX@47nQEK:fCG"?".D]E--ARK8>PNQ_'tNXg>RP_T0m?]X"H,QB7#\L4b).H?=":?Wg2q
4Ye?''^5T/!?O3m6p3Lb.jcDY'b1TV"4I5]!s]2D'cS;C.l'(I6qTE[!U9Xs$k*[`*[WEn2`sAq9Mnko
>?h)1*EcQJ=]A'^6:4"'5<V(f3&EEE/hJM$-RC#^+Wqm+*"s2,(Ddi''c%Ju'bhDu(D[f'(`E;m*?QCI
+s\H^.4Hi$0/GFE3'08f5X7\'6VLH^=B\l,=qFnB=]\Ho7R9-q1+Xan(D%#=!!rZ6'+YKX"p4o-`rHDH
!WrW5$kF!Z!U0SI'GMH20K_g#='fKUF*W:fK8#)<OHlB5Ye%ctaMbj)[BHR*T:(q)MM6P+G&_A5A7/YE
<Du[f91h`F7Rd8D4@M_084lNL:K(7q>[V)VD/j`FIt`c@PaS/>WNW_@^Vn4<`4E1LSX#:oLP:A4J9c3Q
AmSP77Q`@M)AMB8%i@)s7R/si/h%q`(_R2^"Od>]":,AJ(`X\J/i,LQ7SPZUkl;2%$kO0p,UtN,4@)G/
:K@Bs-<aPU=^#!2<_l(K6:!k$4Zk_a1c-m=/Li4u,paf[+<V[E*#]\3rYGSN(]>'L(Dcud-5[U=*#otA
+<`!V,q(5m/M8_61cRKX4[21t6:!t7<`iO/=^"s3r`K\1:J+)B4>noD,TRX3%-ITr#o!p[$3p\8!QP0G
!<NB/$4RO\$0VA<'GM?.00)Hr=']BQEHlqaK8#)<O-H*.Y.2?kai2*.[]lg0Tpq=/N/*"4G][k>An#(N
=&`*o9hS)M84Q0>7Ros>8P2WM:K(4o>@(cNCN"<>I"I-4OHl</VQ6r0]>;J.b/1ZkURIF.MMHn<Jpqra
CLpdQ:.7/r,8V'X!"T_p7Roa+2(pC(*uP_%$3\fX%g34L',_l;.5*P?5t=Br'BoK2$k*U\*?lse2EF&j
8l&Gi=^#$5>5hYL=B/6g6:4%(5Wq4i3&WQJ0.ne*.4?Jf+sA'L*?6%;)&O5.rYPSMrYHdr)&O50*#ot>
+<_pS,q(2l/1rS21H%3Q4?Yqp5smt.;H6iu=VFtB>?Y04<DQ4T5<:\V.O#lJ%LhM##R(hQ%LWOG!s-FB
#QY#2#R_+S&d@J!3trAE+Y>Z?9iYG.C2eBFJqAZ2M2d^dU8kW:aNMW@]stALW2#rOPE(KQIsQ9YDJE`k
?!CB0;c$4_9E%Q`8cD=C92&)W;cHh&?"%;ZDK9oHItWZ=PF.r:W3!;7]u%h5aM>3`U6q.(M2-b9Jphi^
C1CLK9gUfk+VkdU!"]Jc5>":*2DH^/+WD..$3gR^!"]29$5"!l+XS]o2EX?!5:?m'!"]PI%MTj+.5*P>
5=A(<;cm3%=s7$N=B\j0;b08B6:!h!4$,D]1G^a=/h/:u,pX]Y+<VaHrYu@c)&O2,(Dmu*(Dmu,)ZC^"
*?QCH+X86Y-n-Vu0/57=3&iu]4[25!6:4:B=BSg0=BYW,%p<"5:.[i<3]/Q>+r_4+%-ITs!tQ"T$O?q=
!WgCC#lt)0"pbSI&e>*.!<*[Z+"B3892f&(BlA-AJV&Q0Ll@I^T;T!-`QZE@^:CYTWhc;VQ&pr[Jp_fb
E,93!@9m)=<Dudk:/+DU91qrP9hnJ^;cQn'?"%;XCiFK@I"I-4OHl9-UoLW+\\>u&bf.6#Vk99;N/34A
Jq8;lDeWZb;bKD8.N]?=kPu.p*]Hb]4ub>N.3fiL'+G6NeGp-`#n7Oa*@!!c1H7Qe9K+!nklC\k&/,p)
-n[;84[MY4;cm1+>?^u.,?S)J:-q<65sRXt4$#>Z1GLO8/1N%q-7'o\+<M[G*#on9)ZCWS)#bBt)B'P7
*$$+C+<`!V-7:2k/1rS11H%3R4$>en5s[k):/k@urE0b:>?Y03;bT\J4ZG5M-m9HA$k2;!#ltDI&.AjM
"9JVG!!rZ-":#5A&/,EPkQ(Sr3?9M*5Y+aT?Y=7uH@UU"L5:_KQ(=_T\\lY5`PK0qZ)jjtSX5P#M1pG+
GB7\<BOkOW>?Fs+;c6Ki:HMEO;cHh#>?tZJBPVO-GC+gnM2mddSYE$`Z+%?]`Q??A\Zhm#Q&plZL4b).
H?=%;?Wg5s5W'r1'GT^1&/6fi7mT3o0Ie@k)AWei"p3BV&-<%C%Mfs*,q:T+4$cA.0GN^NrX9>N(a(.V
1,h<]7ncc]=]ns4r`L@I>$4m&77'@-5X%@o3]T)T1GLO7.Ocen,pXZX+<V[FrZ(qWq\oJRrZ!%$*?ZIJ
+s\H].4H_u0/57>2E<f\5!D4t6UF4<<`rU0r`Kk;>?P*1;+sJG4?##H-6O-<$k27u#R;"T%LWOG!s6RE
#Qb)3#R_+S'*m\#rY.@3,r.SP:KLk7CiaiMJqJc4MN3phU8t];aNMW@^:CSQWMH2UQ&pr\K78)hEc5`,
ARSnM>?Fs,<;TTU<)m"%>?kKCAS>jtEcu_WK8>JJQ("AAWNNS<^;@q6aM>3aU7%7+MMHn<JpqrcCh@!T
:I[H%,o[O4k5Z)&-pL<k4Z>,J-QsHG'+G6P!SIGb!<ri>',MW3-S7)44[Vh+-k,;>rX9;K(*4_N0fD-[
7S6HV=]nr->Q.bE=BAEl6UX4+5Wq4l3B/oQ0eb43.k)ko,pXZXrZD4`*VCBQ*?Y8')^-CN,pt)i.kNA-
0f(^I3BTDe5X@\(6V(-W=T2J&>7k%A=]J6i7R9-p0e+Fi((^l_jo>\i%h]B]#mLM6!QtHL!<NB.#n.=W
%g1tprY.=.,;2&G9iYJ0C2nHGJqAZ2M2[U`T;T!-`QcKB^UgkXXJ_e`R$<YhL4Xi!F`MD9BOtX\?!LT9
r`B,#r`;-_>?kHA@V'4gDfTuGI=d32O-H$&U8P)s[^s5laN_]:ZDX=\P)P6QKS"f(GB%A.>uaTf3A2Tn
'GT^1&cit19h%B/1bU:(+;u"-$jZq9gAhie!sf;I()e;A.5*M=5XnI+*!,7'rX98H'ceJJ0JtmU6qL-P
=BYQ*,?\2P=]J6b5sdk'5Wq4j3ArZK0JG+1.Ocbn,pX`Z+WqmL+9!8_*$$%>rZ*4*+!;[L+XA<Z-R^Dn
/1rV11,CgK3]oPj5s[h'6Vg]b=B\l*=qFh=;bfnP5<:YU.O#lJ%L`d.!!rZ5'G(]\#6b22b5_hM!sJo:
%1j3\!TsI^'KHmN.lTae;d3^EDfg>WJqJf6Mia3oUp%;Fai_W?]t(JPWMQ;XQBI8cKn4VsF`MD:C1_!c
?s[,Dqcc-e?!^oJA7fRmDfTuGI"@!/Nfo]tTr"`kZamcd`lcE?\?D]uQ&po[L4b)/HZX1>@Tuc(5rU;:
'bhAE!"fkp6V0X-2_ls4+WVF6&.8[FgAhfg"q(tU)''kJ/M]7I6V0Wq'B]Ap$lTQh)^6[_1H7Na7ncc]
=^,*6>$5!3=]na'7fG^o5sRRr4?>DZ1,1L9/Lr;"-mp;e,9e9S+<VaJ*W7'"*?Q@G+<_mP,:+W`-n-Vr
/M8b41,_'P4?Yhm62j1j7T3;i=^"s2>$>-7=]\Ho7m]?u1G1(")\`_i%-7Hq"VD@X$OI"?!WgOG#lt)1
"pkYJ&eP<0!<!Tl*@Nd/85WMsB5M^8ItE<,LPh+UR%^Ld^;eF=`4rpmZ)jn!Ssbk*NJN:=I!KmVE,KH*
An>F[?srq>3FH6'@V'.cCMn*4G'SIcKnt_MQ'n5=W3!84]>);*c,RK)WM,]CNf&UGK7\SuF)>Ps=]%^R
2(0LE']oB.#9Z!"76imk0e+Im)]'+r$3^Of!"oA;$4RUa)^-LW0f;!W78,Z`#Nl*c$nr;2+=8Wp2`sAq
8l&Gi=^#$6=]ed0=BJTt6:4(*5X%@o4#f/U1GUX;/Lr;"-mg;?,Q]/5+nm)\+X&'/,VUPl-n-Vr/MAe5
1Gh$M4$5\j5X@_(6;1E^=B\g/=^,*6=]na#8k)*/2_lp2+;baj%-7Hr!=KPP%13@E!s/ML!"&`.!sJr;
%1j0\!TjC\'R(H@0KV^!=']?PEHch_JqSl7Mia3pV6IJHai_Z@^:CYTX/;V_R$<\lLkUD.H$=@NDf'9)
B4b^c@q&nWA7]@cC27[)EccMNI=[*.NKBBlSt`*_YI1pT_T0g@_RQhHSX#:pLk^S7Jphi_CLg^P:IR?!
,T?XrjT#o,/ON)s4Z>,K-mTiP'bCf\"U!W^&d&:D%1j6m*[E-d1,h?`8OG'CjoG>g0G#!?,UtK+3^6#(
:/b7t>$G05>$+p2=]\Kj6:*t(5X.Co4#o8X1GLR9/Lr;".4>*@"!f*8,5EAa+sJ6WrZs6G.4H_s/M8_3
1Gq-P4$5Yj5X@\&6:OjS=^"s1>$5$5>$4s+:.[i<3]8ZA,TRX3rX@if#mCnR%h&aK"9JVM!!rZ-"9o/@
%h]HXjT,5nG8;>R3'p8;>@M8aFF/OiK8#)<O-H',WjKLZc,di>]XY8MWMQ;ZR$<\lLkUG0H['^UEH#f3
CMITuBP;*pC27X'E,fuCG^FmkKnt_MPaJ#8V5^W'\@fYraN_]<[&TjgP`L]WKnFu-HZX1>@Tuf)69$P?
(B,$0!##S[2bQV+4#\oF-QsKJ'G1]Z"U!]`'*ACE$P='j*?cd\1,h9\7S>Z_#Nc$b$k3[\)^6^`1H7Na
7ncc]r`T5(#?b25=BJU!7/fI`5X.Iq4?>G\1Ggg?0.e_*.K1ao,m#>;,Q/kh,:"Q5-2o;(.4Zr$0/,+;
1H.9S4?Phm5lO%S6r$cb=B\j1r`];(%9HD#7m]?u1G1+#)\`e]%-.Bq!=KSQ%1<FG!s/MN!"&`.!sT#=
%1j3]!Ta=Z'K7'^2*a]0=^PcXF*N.cK7o#:N00EsV6@DGaNMWA^UgkYXJhqgS=#P&NJ`ICIscQeG'%bE
E,YVk2fEYTEccJLH@13pL5LtPPaIu7Uo:B"[^s5ka32Q@\?Da"QB@,_LP1;3IX#pNAm\\<83f$]*rQf6
!#-(s6:sX13A`B=-6aEI'G1]Z"U"kf!#,G:"UPPK'H%l7-7UZ*3Boo(67W`2!;m68&f;`</2B.H5t4IB
<*<B'>5_S.>$4j(7mfWB5nuXK4$#A\2`!9F0J4t0.Olkr-mpAhr[%LgrZr:+-RU;k.4Qf!/hSh41c%'L
3BB5c5lO%R6:47A<EWN(=oMS7=]n^"8k)*/2_lp2+;bai%-%<p"V;:W$jm4C!s/MP!"&`.!sJo:$kEs^
"lo[]'K@$W0KV]u<aB3ME-?V[JqJf5MN=!hTrGB2`6??B_S3RgYcOe!Tq%L7OH,3SKn=f%H?j^WFEDU&
E]A,^FEMeQH@13oKntVHOHc-&TqnWgYdM$U_SsX@`4WFUTpV(*MMHq=Jq8;mE,&lg<_c(F0I.V9']]6/
"<96j7m]<s1G1+&+WM@6&If'O!s.Qb'`eC@$4RR])&seG.5!D84@2P11`QY[!;m6:&/H93.PN_@5=A(<
;cd++>Q.eF=]ej0=B/*a6:!q(5X.Co4#o5Y1G^d@0J4q.r[J(#.4?Pl-N,:q-7C5i.4P6G)_<Qn0JP==
1H.6Q4$5Yj5X@_&6:=RK=BJa/=oMS9>?b63;bfnP5<:YU.O6#M&H*$t!"/f6'+k]^#mUS8!WggO$NU;2
":#5@%M9?\!TX7Y'K?jO.lTae;d3XBD0'uOJqJ`3M2[O]SYWEr^rO[?`PK6u[BQa2UnO?HQ'.2eM2-b9
IslZiH$FT4G;shlH$XgcItE?/M2[LYQ'e,9Uo:B![CNue`Q69D^pU>@SX#:qM2-b:JpqudD.d6\;bKD8
.j5],']T0.$mn&07R9*o1+k"%+WM@6&.K!Q"p4#h'`nRF$OmX])&seG.5!A64$c>04!kBs!;m69%MTj+
-n[;84@)G/:K:P#r`URM>$+s2=]\Qq6:!t*5sRXt4?GP^2`3BG0JG.4/hAJ$.4Qbpq^Ed=.4Qbq.P*2)
0/,(71Gq*M3BK>d5<qM$6U=%3<**4*>$+s2r`Th8=]J6i7R9-r1G1(")\`e\%,q6p!Y#eS%LWOH"9S]+
f)Q0Z!WrQ0#R_+R'+G$'!;dHg*@N^,7o*2lA8?..I"?m%L51YHPF.u?Z+7`mbfI`<]XY>PXJhqgSsbn/
OH,9VLP:A3IsuhCHo?D%It<6+LP^tMOHc'!StVsYXKT"@]YMJ,bfIZ3Z)=4]PE(NUL4b)/I!9OFA6i84
7m/XV*W-W2!#?8#6;'d53]AcE-m^#V)&3bq$jd+@!pot'!X&]6$kO$d)&seG-nI)33^?/-67io3!;d08
&f;`</29%D5=A(<;cm1,r`W$#=BSg0=]SEj6:!q(5X@Y!4?GP_3&EBG0ek=7/hJY*.Ouqt.Ocer.Ockt
.kN>*0/#%71,:[G3&s&_5!VD!5sdk(7oEAj=B\j0>Q.e:>?P',:.[i<3]8]D-6O-<r!_Na$O%(S&.JsP
"p=u/!SIG\!<N<*"pbPF&/#NXiW/ii2AdYl3C6D=>@D2_F*W4dK7o#9Mia3mU8kQ3_og-B_nWjo['6X1
V4sQNR$EksNf8jOLP:D6qgp_YKS>2;MN!RWPEhN)St`'[XK](A]YDA)b0%W6Z`9agQ&po[LP1;3IX-!P
BOP+E9L:`m,8g=miW'i")_slS6p<Xh0e=b"+rqR<'+kZ\#mLM8"7lL1!sA`2#mq+R',MQ-+seZh0f;!U
6qKj')[57%q[<uF(*+SJ0/GRN6:XXD<EWK&>5hY/=BAO!7RBH?5mKY<4?>M`2`CXk!\lD_r[n4'0)7%!
/hR&T!\Z8_r\Fd;2`Wo[4?Yt.5mBYB7Sd&d=T2J$=q=b=<DQ4W69R:a0.A(c((U6Ni;a8e#SIa\$jm7E
"9S]+gAhW_!WrQ0#RUtN&eYHNiW/fh1`n/(5Y+^R?"IejGC4snK8#)<NKTTuUoh&=`QZKF_nWjo[BQa3
VPBiUR[9;'OH5E\MMh4c1l@0LNfT9cQ'[r1TqeKaXK]+B]YDA)b07i=[B66qQBI5bLP:A4JUDZ\CLpdR
:e3i/.3JF$i;ai)+uW%b6U*Rg0eOn%+W_R?()%/e$47%D"9Sc/qZ.Z="9Sc3#RUnJ%MKTq)]g.L.5!>4
3Bol&8i@k#iW/f`%20R$-7go23^6#(:/b6n=X%'S=^"s3=^"p-:IIN95sdk'5<V(j4#f/W2Dd3G1,(@7
0_[1D0J>(51,C^C2E!KR4$5Sf5=%S%5sdn+:K:M#=^"s3=^,,0=UeG2:.[i<3]AfF-6O-=q[DB_$j$bL
&e>BX#R1D6!Wh*W$ipD3"9o,=$kEsa$3K/dq@kh,,VM/H9iP>*BPhg9IY*0)L5:_JPF.u>YI;6dc-")E
^V%+bZ*(.+V4sWRR[BA)P)tcdNfO't2NEfYOH>WiQ'e#2TVA9]X0/_8\\,\pa3)ZH^p^GDT9th'MMR"?
JqAGsF)>T!>?"<c3A2Wsq\7rg*"F;T9h@]:3]AfH.OQJb*?#_0'+k]^$OR1G"pb)1*!ZZR#mq%J%M9?i
(E+;;,UY&o1,h6Y6V1!3-4\u7q@#7n(*+SK0/GRN6:ORC<*3:,>?Y05=BSd/=]eWs6UF+*5sR\"5!1qh
3]T/X2D[-G1GU[=0e`JZ2D6d=0etOA1G^pI3&s#]4?Pel5X7V$6:+%5<*3:*=]nj1=^#$5=]eX!8k)-1
3&<3;+rqF1q[D?^%0?nO&e>BX#R1D6!W`>\!"Ar0!WrQ0#RUtN&ek]ThuNTf2ARDb2*a]/=']?OE-6MX
JqJc4M2RI[R@pI^\A6,/b/_E8]t(SWYH4_$UnXNRS!fV/Q'@JrrK9(&PE_>uQ^O>7TVA6[WN<;/[^j)e
_oKpE`k]*gVkBHBOGesNKnFu-HZa:BA6i8583f$^+;XhghZ+T/-ok!n6U!Oh1G:7,,p=BN)Aa,%&e5B\
$OdC@$QBE]$OmRU&ebur)B9kC,q(8s1H%9X6V1!4-kkM=q@!oF',_o>/2B+E5=A(:;--n'rEB/&#?Y).
;b]VG6N08"5X7Os4?PYb3B/uV1c.!E1bpjC0ekI?1,LdC1c.*M3&ru[4?Pbk5X><7#=:aE9iG%o=T)D$
=qFh>=&DX_6pE^j1+Xap)&!MW%,Lsn!t5kV%h&dN#6b22!Wh<]%06M3!sJo8$4IFX'FY*$!;R<d)^I's
6VLE_?tX=sG^Y-pK8#);N0'<nU8Y?,^rFXAa2Gg.]"#/QY,nV#UnaWUSt)7;R$di=240c!R[ftATqeE]
WN<8-[CEf^_8OC:c,RQ/Z)FCcQ&pr]LP:A4JUMc`D.d6\;bTP=0-hM5']&g.'.Gq98OPg*2`*9B.OQMc
+<;C=(_m_u&eGZR%N5oj&ebro(`=85+<`$Y.P<J52`j8m8PM2o'Aifc$ka*e*?lpc1H7K_7S6HT=BSj2
>Q.e3=]np2=]\Nr6iB=X5s[_"5!;"j3]d4!"?/4q2)G1f">_nl2E1Ro#s(+04?Ykm5=%U663oqQ;cd+)
>$5#-=qY%B>?P',:J+)B4?,,M.3]cJ&Gcgk!"K,?'G:oc$OI%B"9S],!TsFm!<N<)"9o);$OmUZ'FY-$
!;R<b(*G(a5"ACM?"7VfFEr=eJqSl6MN*daS>*!f\A6,.b/hQ=^V%+cZ`pU6Wi2ejUS=HTq49C8Tq\<X
VPpMrY->7@\\,\o`Q63G`k]*hWM5oJP)Y?SL4k/1IX-$RC1CON:IdT+.NfN''\ra)'.>h48Oc!.3B&]J
/L`%n,9\-K)]BUn(]>*J(D[l,)]TqC,:+]d/MT">3BTSs8P1rj'A``b$oJM.)'C7W0fD'W6UsgG<EWI.
>?Y66>$+s2=BJX'9LM356UF((5X%=o4?P\e3B9&X2`N`TrAZYn2`N`T3&ru\4$5Vg4[)+r5sdq)6:=LG
='/R->$+s2>?Y67=]na#9LhH73]8]D-6O0>%f-Uh!"]5?'+tid$OR.D"U"o/!W`>h!"]/3!WiH,"U55>
$P!^\'+4ou!;I6a)^I's6;(6[?Y4(mG'eakK7nu8MN3jcS>*!f\A6)+bfIc@_7mOl[^36BXK/>!Vl$;d
qkGsCV5C/hWN3,(Z*UmL]=u,!`Q69G`PB!gWM5oKP)bEUL4k22J9uHYCM$pV;bKG;0-hM4'\i[*'.,V,
91D644#]#P0.eY&-6s`X+<MUD*;pfn*#ot@+<_mR-7LDr0/>CD4$Q&$91Cfd'AWZ`$ks6g*?lpb1H.E^
6qL-N='8[0>?Y50=U826=BSX':I[\K62j+W4[)"j4?P\d3BB/\rAY0F3BB/]4$5Ve4[)%.5l<nR6:OaM
<EWF,>5hY=>$>-6=]ng':.[i>4?#&K.3]cJ&GZah!"T,<&eY`c$k!@I"pG,3!Wr?%o)Saj%fue7"9eu8
#n%.O&/#T_!o*eN'JpOH.5X.V:/tP-BPhd6I"?m%KnkJANfo]uU8Y<)^;J%9aiD?:^V.4h\$`NGYH=n,
Wi;urW&1KQWiN5'YHY=?\%0)`^V[q0aiqiD^:(8EU7@O4Nf&XJKnFu.I!9UJBOP.G:.@?&.3B<#'\`UD
%O*Yp9LhK:4ZYMY1,(=2.4HSi,U4HU+s8!P+X&'U,pk&i.kWM21cIHY5t"4:68B_pg]7*X+:Su$+=&Ej
2*!fd7S6ES='8[0>$G-6=]np2=]ed+:IkCNr':BL5!T!/r&b'B4$,M$4T.DE5<_@162j1n7o*,e=BSd1
=]ns3>?Y35=]\Ns8k)-13AiK?,Tmp;%J^Fd!"T,:&/,Wd%1ERM#6k>7!sAN(o`>$n%g)n:"U52<$4I@S
&JG]]!o!_L'J^RO/iQ'e:fgq4C2\0<IY!*'KnkJANfo]uU8P3&]>DV4bK.`B_S<gs\[])UZa$a<YPYIX
YHP18ZaI6O\\#Sj_SjL9bKS#D]="i?U7@O4Nf&XJKnFu.IX#pPC1CON:e3l2/gD;0'\WO%$6(QY8kMQ?
5Wh(d2)?s?/h8D#-n5$@'e(O[.P!,)0f([G3]oYp7S697/K5&O!;?m6&/?-.-nI,33^5u%8l&Af=]t`-
%U!"==^#!2=]e^&912-5r^%#Z5sR\"5X.Is4[)"lrB(KO4[)"m5<hCt5X7V$r^%)\6UXRF<`iL,>$>'3
>?Ur/&Qr7=;bp"U6U*Ui1+Xgt)\iqZ%+tUk!<ioG&eGQ_$OR1G#6k>7rWDlrrW<Z6"U52;#mq%L%hK?e
%0Zdd!;7*^*@<O'6VC?\?=dkjFEr=eJqJc4Ll.4UQ("ACXg>RR`5p3GaMu08_7mUp]",>[[^EOAZnf%s
[^WcW]">Yi_8=.1aNDcK`5&piXeqb[Q'./bLkg\:JqAGsFDtu+?<L3"6T[%M*VU9$!#5YO-8\%^8OZ!3
4ZbV]2)@!C0J>%2r@A[9/h\n61,CdG3'08g6UaR>68U#'"kitM$kX*g*[E0e1H.E\6q9pH<ENC.r`Tn:
>?P'2=]ej/<`Dga6pa7-6N'4L5RfqA5X.Iu5!D4r5<qCsr]pEJ&OJfO6:===<*!((=BSd/=^,&.=qOtA
=B/-j7moR(2`!'8,T[a7pC,XR&HW7M'G:rf%1EUN#RCY>"pG2+";(e@"pP;<#RLkI%1a!_'G(QPfDtUZ
1)D&_2*FE(<EijCCN+EBItE9)KnkJANfoZqT;8Kl[CjAqaj%uLa2Ps5_8!^t]XtbdrOF\p]">Vg^;%M$
_o9[;b0A&I_S!:]Whc8TQ'./bLkg\:JqAJuF`;,.?s?W+7QiUY+;abdf)QEl*%Ns89M%]@5s@Cm3B/oS
2)P1d&i;I'2E3]Y4?c%u7S-9@2_,jVfDtUR&e5Tn+=8Ql2)m]a6qL'L<ENC.r`TA+=^(c,'NeL><DuX^
6:*t+6U=%)5sR_%5X5-2'L+iK5sR_%6:!t,5sdn-:/b.o=]kW*!a8`0r`Tq==]e[$9M%W<4?,2P.OH8U
'FsjGec6-["UbnU&eGQ`$k!FM$2t5+#Q=u<#mq%J$k<dZ&ebib"Td3Tob9%u,;(o?85E;l@V9OtGC+jl
JqJc4Ll.1RPaJ#9VlmA9^;@n3c-+5Ma2Q!6_SO%`^G!C=_SX71a2lBGc,muB^::MPVkT`LP`Ul^Lkg\:
JqAN"GB%J4@U)u183f*d,9-=jec69b'-Jkd6VU0C770@+4Zkee3r:oL3]]>b4[;;#77TsE6T?Y?'*Qne
oaEPd()nGE/29%D4[DP/:/P"o=^,'5>?Y05=]np2=BSa+<`2U\6UO1-6:4(,6:*t(6N'2)6:!n(6:4(,
6:4(,6UjaI<`N7)=BSg1=^"s3>?Y37=]na%:.[o@4ZYJU/1)P[((g0Je,TpY"UbnV&ePZc%LigS$hju;
$O[@P%M'*_&ekoc"Td-RoFro!-87GH8l8Yq@VBUuGC+jlJqJc4Ll..QPEqZ0UoCQ)\A#i!aNVlOaiMNC
`PqYk0#bfMa2lBGbfn/I_S3OeYGn=iR[0/!N/EFHKnFu/J9uK[D.mBa=&DRV3A;d$ob>mU%gNaj.lKU`
9M.fE6pj:,62NnX5s[h)6q0[=9M%H,.j,K1eH#4M&eZ!$+t5-"2EEuf7S6EQ<EWI/r`U1D>$4s1=^"s1
=]ea+;boqQ6:4(*6:*t*62Ntd5smq)6:!q*6:FF@;cZt%=]eg1=]nj0>$Lu0&mJLA<DcL`7RB7!2DQm6
+rqI4oaK7K$ipM=%hfTh&J,H`%M&:D$k<aX%hK<c',(l`"Td'PoFrtp+=T*/78$Q^?"7PbEHcb[JqAW/
L51VDO-5cqSY;m[YdCpQ^r+.4b0A/Rb0%iJrQ$\7ai_cLbg";O`P]L(\?rK?V4jHIQ'.2dM2-h>K7\]*
HZsIHBOb=N;bKPA1FXF^ob>gS%0mOd-nmSE7SQKI8P)JM7L_m[8P2TL7R&gc-l`m*dfB"K'b1lo+=&Eh
1H.?Z6:ORB;,p_$=^#&->5qb)=p\;6=B/@":ejPNr^6ZO!(6]N!(6`Pr^-u]85;u[<E3.(=T2M(=o;G<
>?Y04=B/3n8k)-33]AiI.O6,R'+X[Dci=:O!s]2E&el#Y')rRB&H<@A&dK'a&e>?T!s-dLo+Whr+tGN6
786]`?=[beEHcbZJqAW.L51VCNKB?hR%9tJWNWV8\\,_p`5]m>b0/$-bqI;ab/hZD`59=&\[A`HWMQ>[
R$EksNJ`RJL4k22Jq&,kEG]E#?<L3$76NLY+r^(fci>'e$ksa2/Mf@L6:FC:92/,R8k2<:4ZG5L-ls'2
!RUo=$lTZk)^$IW0JbXM5=.h3:/P"n=]np4>?Y04>5hVO=^#!2=]ng,<)Z[j9M.fF7RTX36UX=37n60F
:Jt+j<``F,=B\m2r`C(B=]np4>?Y04=B/3n8k)-34?#&M.OH8W'G0mFblA%N!sJu=%M06d'E&UO',(ui
%LWRH!s-[Ine<Yp,;(o?7SZod?=[beEHc_XJV&N-KnkJAMiNm`QC4;;Uo18tZF.3R]YD8!_Y_5&_SX+'
]XkV\ZEUC-UnO?IQB[JkMi*=GKnP)1JUVohEG]E#?<U<'7R/mb,9-4gblA%N$546t,Uk5r0`<a50J4k)
,T[d9$3AEPndHHK()nDD.PNY;4?l/%8kr5`=BSd1=^#!5>$G2/=UnV<=BJ^/=B/C$<Dugm;#O0":f((h
<E*"!=BSd/=BSi+=W:OK>$G06=^"s2=B/3n8k)-34?#&M.OH;X((g'GaT)JF!X&Z2#Q4o3#6k>7!WgFD
nJ!Pq,qh2B7o!#e?=[_dEHQPUJV&K+KnbA=MN!UYPa.W+T;&-ZWiiS3['d?P\G`ri\@AuT['Hp=Wi)Ye
T:D:6P`_#cMMR%BKnFu/JUVleEG]B"?<U<'7mK!d,ouOjaT)hP#7M(U',22t'GD,i$jQgU!:^J)&f)K3
-7gi,3'9Dl7S-9L;cd+)>?Y68>$>'4=]nj/>$>'4=BSa.=]ea-<EE4$<E<1$=BAU-=BJ^.>$5$4=BJ^/
>$5$4>?b98=]ng):J=>L69dRl1GC@,+rqF3ndL*MnJ!Mi*[WO"5Y"RK>$b]PD/aTAI"6g#K7nr6LP^nI
O-#QjQC4;9U8+Q_WN*)'ql_QRXfA@uV5'cWSXGe/PE:iaMM[+DKnP)1Jq/5nFDu)1@pW><:.IQ/0.%_U
ne?ZUnI-$?&f2Q4-7gi,2`s;k7S-9L;cis$!ErY)=p%o2>$1c,#[1A7=^"p0=^(W(#[1A5=B\j2=^(f-
"'A`1=oDM)=^(f-&m&"-9M%]?5<Lk\0J"Op*>]=undL*Mn.[>f+!r[%5t=[L=^>KLCN+?=H[^NtJqJ`2
LPL_DMiNm^Pa.Q%R[p%BTq_O]s.oXh.\`W/St2=;QBd\tOH,6VM2-k@KS+l.JUVriF)Yo-@U3,9:.IT1
0I@kWnJ$QTn-fs?',V`6-nI&.2`a)f6q9jE;,p`t=o2D%=TDY)=TMW-rE95)>5MG'>$:f-!EiP(=TDY)
=o2D%=VFn;;,0_R6pWss2`!-=-mBZK'+XO@JcP6R.i0<V0KDBg:/b;$@V0@nEcu_VJ:`B*K7nu7LPUhG
N/j!^PE_>uQ^F/.Rf/^+R[KP1Q^3o$PECreN/NRLLPLV;K7ec-J:2]dEc5])@U3,8:.IQ00IS%[n.^HS
mgL'D',V`6-7gi,2`a)f6q9jE:K(=s=]ns4>Q.e1>?Y37=]nl+=TV].=SZ)$=BSc*=U%u2=^,'5>Q.eD
>?Y66=]ea(:JOMP6pWss2`!-=-6aHI'+XL?JcP3Q-l4!S0K;<e9iG+u?tF(iE-->OIY!*&K7ei2L51SA
MMd@PO-#HdP*>^+.$KgWOH>N_N/ELLLkg_=KS+o/Jq8AtG][nAC1UdX=&Vg_5WCGG+;aJ\JcP3I'Fkcn
*?lg[0JbUJ4?l2&8PDoW<*0'%#?tA7>?b97>Q.e(=ohc.q,m`#=oDP'=U//7>$G36>$Lu/'N\72:J=>L
6U*[o2D[$:-6aHI'+XI>JcP0P.2O*R0/l-b92Sbp?=RYaDfTuGI"$WuJqJ]/KnbA<LkpnFMMmFPNK0%t
O"$*LNJrgSMMd7HLkgb?KnP)2JqAQ&I!KjTDej#r?<^K.91;*)0.%eXmM(6Qm0j^>',V`6-7UW'2E3f`
6:==99M\Se=T)A*>$>-6>Q%_0=^"s3=]nu-=pA26=^"s3=oDM,>?Y67>5_S==B/9q9M.fC69dRl2DQm7
-6aHI'+XF=JcP-O()J&>0/l$_8l&Gi?"%>[D/aQ?H$k'lrdk<+K7nr5LAlu.M#W>-MZ8P5M#N/5L5(D8
K7j/T(4gp^H$==KD/*Zk?!10'8jtm%0.%bWm1b-PljOX>',MZ4-7UW&2)dQZ5sn+58kr/\<EK-%#$Y87
>?b98r*')(s'#D+r)s;/>$G37>$Lr.(0=L7:JOMR7RKI)3]AoN/h/1k*>]A!m0nRHlP(-N*[NEr4@2S6
;cd42@qTOnE-$5KI"$VEK)^K'K`?c+L@p?%LAuu-K`?]'JerrdI!KpWE,B?&@p`JB;bfkM4>e`;*Z")V
JcP*F(_./o)^$CT/2/k<3BTMl6q0a?:/=ef='5B'r`TA+>?h)1qcj&'!aAl3r`]5&(g'j=;bp.\8Oc*6
5<Lqa1GC@/,Tn$A&e=7:JcP'M)&=5;.l01N7S?QX=^>EGBPVL)F*;eTI=HhGK)UE$K_pK%K`6W%Jf0,h
I=$3^F)Z#4BOkLV=]J9k7R/si.jH&MlP+pNl3nR@&f)E/+sncl1,V!O4?l/#7nH<J:Jk%j='5?&!*f%u
!*f>')H^'@;c-=c8kDN@69dRn2`3?C.OH>^)\s%rlO8@FkS+sT+t53)4[Vb8;cd1/@:a+fD/aQ<GBnO`
IXm"FK):2rJfB8kIXQQgGB@nFD/3fr@9m)=;bfkN4Z>&D+rfb\JcP!C*"Wf$*?lgZ/2/k<3''2e6:=:7
8k_uU;,^Ir=BOrn**?<E<Duaj:/"8O7RTR-4ZYP\1GC@/-6aHI'G0L;JcOsJ+;u@L/i>^V7S?NU='Js=
A7oXoDfKi?GBeF]I=?]sJV!QI+G#)oJ:;ooH?aXUF)c,8C1h'c?!CB/:J4/C4#JW;+<'GXJcOsB*tT)%
)^$CS.P<G32E3`\5=%\,7nH<H:/=_c;cQh!=8c8"=oDGE='&C%;c?Oh:/"8O7n,m45<V%e2D['?.OZPb
)]'/!kR<%CjqK!T*[<-i2`sDs9Me\j>?tWHAnYssDfKi?Fa&(UH@($eI=;'C,(4leI!^0bG^":PF)c,8
CM7<i?sQu=<)?7Y69I1^.O,uMk7iLJjpW:@&f)?++s\Tg0/>=B3''/d5sdt/7nH<H9MSAZ;,R!b*`5d1
:JOSW8kDNB6pa1(4?>G\1GUU7.3p)Y)&<hpk6uqBj:iUR+=8]t3Bff$9i+ej>?tTFA7oRjCi401EH?5D
Fn^)#F`__HEH#i6ChdWrA7AnO>?=d#9h7W83A`?8+<0DVJcOj?)A!T!)^$=O.4d,-1H%3R4?Ynp6:=:5
7nH<Fr_3&]r_+5&8kDNC7RTR/5<V+i3&WQJ0.\P!+s7g@'G0@7JcOgF*#BV?.5*P=5Xe4;:K(:s>?tTF
A7fIgC27['Df>VorGhgU*,fb4D/3m#B4b[`?X6l=<`;df84>d(2(pC'*>dcNJcOg>*"ESr(`a_C-7UQ"
0f([G3''/b5=%S%6psL67n<;F*(NLg7RTX25s[^u4?>G\2)?s?/1Mtj+<;:6&e<t2JcOaD)B'\C.PEY>
5XIt6:/Otl=^,0<?XdMVAnPdjpM(U[BP1pg@q&eO?!CH5<)HC`7m]I$2(pF)*ua,QJcOa<)\<Yu)''hD
-7LJu0JYFB2`Ni\4?Ynn5s[h(6hs+h6UF+*5sIRs4?PYa2`<HI0J4n*-6aQQ)&<ksiXCD=i"R+F)^$CU
0/YaQ5t+=;:Jk%j='8a4?!^lG@:E^C@iJld@:3GK?!LT:=&i0r:J45I5s74c0.J4i)\q<GJcO^;'b(]f
()\,8+sn]f/1rV31Gq'L3'',"4T7M@5Q3hF4;Bn<3&``Q1GUX:/1N%p+s7mD()%5gi=(;<h@peB)^$CT
0/GOK5XIn192/2Z;cQn#=^#$6q-4b@>?Y02=&i0r:JFJQ77'7$2`!*9,Tn$Ah\:YBh@(;4&/,fr*?ZOO
-7LDq/hf"81Gq'K2`E]U3B@%!)EBf;3&`cS2Dm9G0eb42.Oc\i+s7mC()%5hh[G):g_:M>)]p:Q.kih?
4$Gr!7S$0H:/=\a;cEZps&K%t(/n.0:esh\91VN@5s@@i2)$O/,9Ij?h%YG@g^Fi+&/,fr)]^%F,UOoh
.P*2*0JP?[1]K?01D;Mu0J>%0.Olhn,U+?O)]0>(&.[M)JcOI<&f;W4,Uk<!2)dNX5X@_*7Rp!Ar_*2`
r_!kr84Q-<6:!k!3B&fL/L_qg*>mKEJcOI4&e5Ng()Ru2+<i!T-7C;m.P*,&r\".&r[ng8/1`8#.4?Mg
+sJ-M)]0>)&e<V(JcOC:&/H3++XAHd0/>CD3&s)a5!]'4s$HZM&3rEB4?5A[2)6g8-mTrW)]%'?JcOC2
%1Wp^'G_N)*?H=H+sJ<3-N,@m.K([o-4M4E+sA'M*?,h2'G:refF3?3e.`?-)BBqF,q(5p0/,.<2>9$=
2)I'B0.eY%,pONQ)A^j;JcO:/$P!^\'GVB#)&jS:*r[8^+o<G`+oWSl*ZZ7?)&O,('G:reeI7$0d1d!(
(E4A:+X86Z-7LAnq^W().4HSj,U4HS*?,e0dM.95d0p9i%M06d'GVB"rYPVQq\oMQrYPnW()7Ms&eGN^
dL:^-bS1<u(E+54*?H7Dr?)@f*Zc=A)]BM.bnPa0bR=Ua%M00`rY#)ArXo>G&.f<]bm]1(JeWe:Jdd52
J,~>

%%EndBinary
grestore
np
grestore
37.25 13.5967 mo
33.6123 13.5967 30.2505 14.7559 27.4917 16.7109 cv
29.2915 16.0576 31.2246 15.6836 33.25 15.6836 cv
42.5928 15.6836 50.167 23.2578 50.167 32.6006 cv
50.167 38.3047 47.335 43.3379 43.0098 46.4023 cv
49.5127 44.0439 54.167 37.8301 54.167 30.5137 cv
54.167 21.1709 46.5928 13.5967 37.25 13.5967 cv
cp
false sop
.221805 0 .068574 0 cmyk
f
38.7441 47.9707 mo
41.3164 45.3984 42.873 42.2012 43.4414 38.8682 cv
42.6309 40.6025 41.5293 42.2344 40.0957 43.666 cv
33.4902 50.2725 22.7778 50.2725 16.1714 43.666 cv
12.1382 39.6328 10.5815 34.0713 11.4731 28.8457 cv
8.54248 35.1123 9.64551 42.7969 14.8188 47.9707 cv
21.4253 54.5771 32.1367 54.5771 38.7441 47.9707 cv
cp
f
12.9023 28.3965 mo
14.1943 31.7979 16.4727 34.5283 19.2798 36.4121 cv
18.0303 34.9619 16.9932 33.2881 16.2749 31.3945 cv
12.9561 22.6611 17.3467 12.8906 26.0806 9.57227 cv
31.4121 7.54688 37.123 8.40625 41.5244 11.3613 cv
37.0098 6.11914 29.5483 3.97559 22.7085 6.57422 cv
13.9746 9.89258 9.58447 19.6621 12.9023 28.3965 cv
cp
f
41.165 32.2607 mo
41.165 30.1294 40.4863 28.1592 39.3398 26.5425 cv
39.7227 27.5972 39.9424 28.73 39.9424 29.917 cv
39.9424 35.3926 35.5029 39.8311 30.0283 39.8311 cv
26.6855 39.8311 23.7358 38.1709 21.9399 35.6367 cv
23.3218 39.4473 26.9634 42.1748 31.251 42.1748 cv
36.7266 42.1748 41.165 37.7363 41.165 32.2607 cv
cp
.805814 .303349 .261204 .0776379 cmyk
f
21.0205 33.1367 mo
22.5283 34.6445 24.4019 35.5566 26.355 35.8896 cv
25.3389 35.415 24.3823 34.7686 23.5435 33.9287 cv
19.6719 30.0576 19.6719 23.7798 23.5435 19.9082 cv
25.9072 17.5449 29.1665 16.6323 32.2285 17.1548 cv
28.5562 15.4375 24.0527 16.084 21.0205 19.1157 cv
17.1489 22.9873 17.1489 29.2646 21.0205 33.1367 cv
cp
f
32.4922 17.9927 mo
30.4985 18.7495 28.8984 20.085 27.7944 21.73 cv
28.6445 20.9976 29.6255 20.3901 30.7349 19.9688 cv
35.8535 18.0239 41.5791 20.5972 43.5234 25.7153 cv
44.7109 28.8398 44.207 32.1865 42.4756 34.7656 cv
45.5479 32.1201 46.8037 27.7476 45.2803 23.7393 cv
43.3359 18.6211 37.6104 16.0483 32.4922 17.9927 cv
cp
f
34.25 30 mo
34.25 32.3477 32.3477 34.25 30 34.25 cv
27.6528 34.25 25.75 32.3477 25.75 30 cv
25.75 27.6523 27.6528 25.75 30 25.75 cv
32.3477 25.75 34.25 27.6523 34.25 30 cv
cp
.587701 0 .334142 0 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_01_Spells Effect.eps)
%%CreationDate: 7/31/2020 1:57 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QE@:TI!Iff^!<brB?m585(_(GN.Z\2?"d7sp_)<2gS!*!odU9DXdJNPhp<9ku@^/LM7Q)cdc-*&,1\9Z?)9XZWJ+/*>
%g[F!HrUS2T:2g1,Nt:4=,\;0Jn9=6enY73(i4m&)eVe?1nBC"]oC$##Gk:p$n(p6Xna3gK\$+Z&qtA'co3\*&3W/6s+5f*(%_mA\
%lIFWR+&9#24o\mYS`<N;=*H<)]srFdme*p\LN;F2(r)dus5mP\(0#gVM-dsnl"_F?qSi@+cjcqCg-,K&GQ2`%'?E*bAFBnTaKCj<
%+2#e%p4GI)nGErLpt8I&iHIMcL]@/`pEQd"s+)P4Y,,.n:ho_ZieL?6``h=#^,5b&`UB*"?S;-@2SPcVKcH`dG8%<`.:HGqqMBFT
%nFo-R3?:jZ$*K9.>OFZ4_^Y6AnO!T<is-ZHFc!j!n]O*spp*]Ub:16E$,=qfGM(F[?,kL.#m$n+^D\&?2)P5G(u5V:2j^M5%c4?\
%2Zkmmh9=NrSEt6?[WuW0`L*TTipgIPHE@>h_(Tj_"#a/lICK+39;eHiQ+gIlM[j+!\d!)0h_"?J<8a6#:1I3Q5).>%jAXu#6=%sW
%k\)6>V\:mdq!$5ffilG^\8'>*W6O@IA_1>BESu69\af/Z*kI*`KjUEjntrndQ):IfdL+9)nNBD?csaBA90`?P?bQ6PbAZ:QHhRI#
%gGf.!h`UnFn7)CiMf53W04=VTpu%kD&:So7]_MH3mp<]j<IPc\_8l:73cO,op]`2)d^S^7"+AusnQ_LpZommRhfru7#sX244="h-
%QbU1@)hB(ZmZ94%3li-8V?*LjO?/5Fp)dVRroVb;pFh=c`,Lok6Ne7jq6N6lH_):*&orX+4ZmGPh=P`D^=#6Z^Mugd*dm(gT3f8,
%o8aF^rPc+H?b@'GEI@N[f.[.g?kXj&ImX(`GHY>cqotUsmpXEId5oUjcea-LiYB,dq"!7AB>dB==/>l>'E@Rgh&ft9#NkdSeTWtZ
%5<^p%^\dhNYFe#Ub6qj$?5iXY2_X%c_qT[:DE\@IGU"b1+)b=tmGJYj@>EYAF+nJHMu')@dYpf;=:<]R[9&li?NTEf`OhIdGkpLB
%:H\!2-TN"%MLYZojk#,6eo5=#5Me_DqU>+uoG?B`rVICLoEBX;lMRuF]t0]*r>OG)IIm.-m;&5FDt0GI=QXOPjh?'<#Pmc^#Q5.4
%q!K)H)'XJ$o=+4CQZ'+fa,b\p^M@3To$qsRbJ/Q$p:G6lqW@Nj+$]V\na![aqVq#:jdR%kroJ][qkM%>s#GE"cS7rh;p.DGio*[`
%5<r$6VZ8<dhluj[!r1&:^OG]hS1chI?@ML@&%)9#s7PCMo07\%gK__lCAf5]4FdD)*A$BW&&7nkItN5IgL5!?o,hcVBc5W!nG=3p
%pBU[dWq$N^^6nEI)<b3b$?]R"&*Y(8Q$58>n6Pmgrr%rI"/gd&(_usiFFj=ArMjc-/UMl-,VK1J(EN<ni4<g`r9;2$\)1Tj>jXm>
%nB4nZhE6-D:$VIks1c@9@;p#;Xoqi-49J0AN16a/^HfupOVdJ9qk-+T"8E(4l/eNq)rcJ\9YYmM;pX>#&L=\!Dnc5EdmqG[rZ[=/
%rgi_-*.Xs6-#Vrj*BQK4p$1W\hECbuO"[+I:@Gpop5auYGl.3bBBWlnAk?Vb:7EiW7am^o3f>c<plFd])Q<:OF+:1$f\6WcT>/&<
%f=.qiIf"GqS@i*7oG20-j<D8\<kto`<P_17!f)U7GTI=j:<$Z9_dFfjCZ$RZ/<\Ac_j0l\h><Pb?bQ7-?bUsgiaI0Cc]r]mIdr.o
%eRtDZ&-mh<3]V'd`B=k:a$"Y1ULrUPd&Yg.54:CmUS\5uR"9TV'n1FfleD@uXfV1gG[1p1^8HH^mL('sr'Iu#PO<U>A!%(tDXG4m
%$+3Eirt2jlg[oI^oh1B\XrNc%54e))56Jahl-"ST^o;u#rVMq"5R0=IqR`*_?a,XSQc"sXe<ck:Ngln57Lkk*mBoI'^E,PZ?\*Sf
%bKitU4FZ?%V`1r0rV,cPIJjeso,g>$5^!(h$4F?FCT5[&Kn6EB5!rE8&(+cs.WhFP(M^fOn5+<+7=5@tglTqCn<kd0V'I'V?U'#3
%:QHUrf>iq]s'Rin/Z2<'6]L7t\P2+.PiSoj.mZe7!^D_fiVuiB;P[8BT\H;<jZ&M=Pr"[5Q!5_^kmhI@b:\,i!D8TN:#Jgh<D'%7
%o.cl1S>fblggV]B$7Ki3;[H9.AO-QpMLn(#TXge`,Erk,1R(PCV/fZ*WiI15TG$@_5Z($m=N-m:V,KD*'9k;;$^BCl=7;Sn*_L)X
%8tsU?K=0Z6LN8&gC?GdWdk:#'arQf9TQhlY7io;Q0A3%ock(X<IuaKWci):1WSAL7'RpNfB_if8Jq;5B@RoW#2KXVU*08,RPV\TJ
%nb.3O]K8,%*_LrOP&MOX.-_,MG(N3Q$Rb^pr9/6WE]MrB\NAW<h:Y.%<,akVFQ*uK*/&C.!aUJVn;I'_3F<glqJWaLh.Q`_;WIfI
%)Eed)ImDQbjq1mRrthUg]*_aS[KHGeM]3Ju$3$IRrtd)m;"!,@pJ;%m6n$,u$>r&[6Id/d42CF,Aa3^5^r!_Y4+A3BU#>0r&!7\C
%Z45US2>#8R:WG%!:>l8toG5Ri<:3#_o^E*8OHmYBL81W]+5Em?=,CE"B=oILa?5R[?6NY,J/NIfB>ho6D>OkD"=_ND>q&SpIu7;7
%)/`N?.#aWtWI/;I#lfVtOc&1'XOL5nk%10sr4C?h/OqBhit5nb,ipRgI)6c]s,V1.EsT*#IoE$YFnk^k8,RWg*!$NH1tOJ7Yse2T
%B^U!Hi$W'j"kE_Rh4_hXT=_37>TLCFnF)RB(B"%\CZY][Ft.^p()HZcX<d!SI\IRrCC4,XYH%Xr7=PqucLc=SF]RW(\DA_6l+H`R
%e3CgBPF^^S>Yg86qn+nrn*'5PGR%I@(+0"jVOC7urWFSuTB*S<oecCldNAE%-L1@WVu@LHrM@N[>bbYj5_$./.k6Eg.O<HY.DhoI
%NHhIF4&tcT_/cDnR8+PHdaZK\M-&ur5(E@jFL<)I"nj^n5GPTbe>X-4fntU+PIM+H#\lbA93EaX,g'RqDP^U:E4>UtKKbR\3_=J]
%7)CFrGp(WPP5-j>jfBpS"(Xl]a7LC;>:::;pi9I[s.E>JdNqMR-uHiS!cfY0Mo`\iS^IN>;q(2+<kUc7iP=m>fC9*B=qm_>8\->?
%i\;G:OF_Q'*QZ2J+((\2knUGM:6IF@iJ")n7$;_d6cu)*%W_=r8E4?/h':&+%E3LPi!ZLq$(*Cf%N^rM8Goq>rEiqVE+k7SE213T
%5uU%.+T^m=FB#i!7iKAN-L(`fa"bJ:6O,))"r^)8N?urflThAY"oSY'ncXTn8STY!5pYF&%XC!Pn-ed^,Fnta/N=%DSJdPb(-HSS
%ksRSSC";E`bB+3uP_/*M^!Na*V"5L"G?rsI&-'3=5e19R)DA.3hNZKh*#GGI"f<)NCf9OtMNr!(=c$%.'br!%_aBD3<bf\Jk9B"]
%7CN^Xn68bfDJ?B3ddjp`FV#qunK[fn/$#a$ZUd>*bUkOf)@CA4J'M#49CheMjc@U[6Ajd_8%@JZBdHcH%sAXJ:dcK37Y?PgjiWeu
%$'u7<U3m+?.,;_ML@P]3'$;;\2^i#UNj.FF`!LraGi8bI"dfNGpHS%og\\GJBnCWfgTH;Pc+BSAI'GgVFO/E&=RPoYqgI2c[^JE-
%@WtoCojV]`];#&Op"nt?XDk,cf(i;1Q'K'gm<e.>jK[N@:[=UqoYTpZYA8WhITZWS[eKL2c7YcQG4rae-c"HM]4X@pm0VG\ZmU$?
%"k9J4A%ahP]48N=g2tc'o+f8bg*;&-:Zm/3h'l(EcgAhG]4IM&b8kZse6JQ4T%8'Gr8=X)4-iU\]7&nF`kDs6.H/kO4j<Ud,2frX
%G=bl3!.:./4iTS!>L9oFhEoQf?t]MqFT1`%!.u@):B3#0V'IQg\:el0hq_OJCT4CS0nG6hoC4&f*1!EsAcAZ18f7eIjA1&"d]159
%,qKGR(q)9`FKcKZMI6`7OS$>O6PjUFLs2M)_iP[2=pQ5ui$Ka!!OVPI2Lu,8"A$V<ER-UM:b(.:fW+OkAc0mDo;0GmP>r15lCQ)J
%?H<W,BO.,rf"<;uqOQ9e*EggC=Oc#a?K?&""0sH2eCh.Qf(DA\FgeQ!qQp,";=*_qW/KP"NVla_4H4O\/t^.uO&C)+CL,cqL/0(%
%YGk%hI'e?:9ufuAIJVQXqDe5)I0cai2jq8TQ^03,\;SnBY%:o2W=K[\`t_=MN,&#a9-S9'4$7cX=;3ocGB+r*CEX-j9LGOe&,lBD
%G$"V)XO1'71&Gcgq`%_]af9+L;m_smI@RG"Pn'*tp<GJI4)Z3*+q*QS)..!&-J?M7%(g(,&#<AXLILcOp^_p_"ga`"[rD*KS,!>F
%DfF;aa9Ed:WaStRj(>WI0T1FBbNpIDH,am^P1TmpWoqaALK$qM05`8'dCb-$M-:rbP##eijEo(8+/j17`iFX&MsOU5i_r6piZ7h,
%"3F5n4G]98LU6I=:Afj/r.UR:KtqQI=*PutiJ$@,47X!go+J'aO?J8pff*T961tYK:CndRp'T(d6'Q)%;1^/uhNMc:M^Zt0TE]qX
%!/bceoE5lLnOrWZ.i!.5cn7lD^u^UQ#J%%;O96=,l7T0Yi9R\'KAC(7JQ3B]!Bg`GTKCPm`#0_PFY;4\A3ia(qO;5B_l<`MbbP\#
%[DfOKGtf5`l)oBeY-%iS\'5"+cLreqi$*2f1LG);?"EX7?Y$Ue1CQ.@J(TT)&7br`@9k%Gc^<<P(#Q:mO4B#5,cu]Ke8/lWTIHTr
%4O/gf4+if,'2>^6e5Da51+]H3S[C&ldPbla>H%'?.s*DuT?D,_h%lrO5@$,p//Ej7E.PF36^oo%%Cq$cp^[kGOal<H2n^#/d=-Pn
%HmV!G^\U_s>XcM+^^d+"0uej\H3f:`k^<.<_Vi2_O.KL&B.'($!</DP3djXAqI!D7Ht[JRAI>'/E/n>A$(P!Q@Rru#BqQ;OLrSZ5
%-e5beifQu39l\eZigaDl`rP=B6U.n=e:%Loq:*ZEIt&hl8ieiprEamR8au=q#V3mNS1XoPV6I&u6Qt0+0\:7"]Qs5I`Rfl0#jHle
%<I7[Pj;G2%8W*'A<(Q(JC?.Lg^ff[<0nc]N(9C7T:u.6f'b"VL=b$R9kstm*`C-U)0$*&AAT<Q\V6-\l/m2'<?o&;_W#Nh9,XTn\
%W-,2c'bYCLHi_%d`Jn%^Js`P?3NWiHku-ukW/s;15iLW:`.CP.&./Lf#i*hDOl:Q#6fAG$Ua%diM3[,k%La7X0m5_O0b-m:O)SW3
%G\lu6?0S:Oi;jC#Qa\3Ma>&WD0H[rLkgE>O7kCkj4';;<Xa73'%N7KJlJe`[T@L>,5+/a85-*/5(i:7">.6Q2L2el&8PBX\22mB;
%N/l%b0p:cEW^]31R;!PZY*QC7^a^qk@Z?8BJG.DJ#5YJRIN@Np*CoC?Otm(ZSFhS1SYP[/R9)o>E)I`)"7#_*PY^?q<F[QXk_tY?
%\;L'Xk$p7bHSuCmi5RA_r&KmN&4Jcf9BHAn6]heMpk(`HhA(>AZsr!@?Go+`C$p5[ct%F!O[cS\[nn3H%-8)rH2Ird@5K@+aUl^L
%NJr,G<C=7SM(F&mp(2PtmRWX/LL;F=RpCSrgjr4*J\^!ts(iT5M4pZ,=XB8P_?Lj##dR+m7/OQP4##.?&&]3E42=K#(^)i(ndogD
%P!$lV]bBlV<9$eBF(Ee<V:*GXLIM;7`]Q*@Yq_<`H,0Bq^*:+gH*1HF__jZKo'2<Tie1t=4,aA*hT=^tN2%0IWQ,`T7HN#YWc6G'
%8P/:`BZu=l0#TW"^p08&%N]mG"u-$tU9Qlo-Y:20A<70c@g#VL(:S&pR#WP\/*[g])JL1,S3S'2:YGh%9cW[u$97=&$)%J1l=(84
%).UbaDC/@@_ltRZ5n:dHo)Rn0gg6SB4LaCs3K`@!d8%llc[tMc>B7<FaN`/S[?AY*JpY\'oAmc+"bJ6]E[PC<,9Yl_ju2/re\k=H
%KGuu2GjGq,iX!cp![O%ah>S3GpE.B=`st=Wn2kX^S]/_&`5.eYQ1&BNZ4e>V-nbF,c?jJiqm/<Mfk%>sHZn#AZbD?m?JP0`9Gh[D
%fh&r#dB=S_=.Sq*&$2;m3b+oBc_^[u(bEl:Z[os:%^SZebnJHJiB:4R/SJq$>ae#mDhUHVTuJQ9L*C#[nRFOs1V6%2.s(5]Clq)E
%DYPm6m8P]:0eG#KVD9:SP6VA:mHdml@.SYO7q_"W30f%86ke)N\eAT&)-[s'o"o@s(P]9>_JHY;R[%;<'-9'$b[uKq?^T",#GaTc
%bcfsqac'VB^@,\'3h(E\c4!il[SjFG)k%HXYL%NK`A94INj)RlMe>k6h>lOOE-U$C$A`%UiY</7(SLO:KAm+]?3'N39Nd3WK+*PP
%8GkgQ73.[L.9+Fmd6WWJN#1tNq'::/Y#Ap7<F\-c)!aGN#4tKpYQECV98t\h!-8O9_s-hc:dh.??2a*L^\\R"lTQUEUg5Q%OJZo1
%'PN6K_-'D("B_ei7)IFm);#YV%r1Rio5B!FnluYfc(>au#C)m.qbKU$9Hiabo,1lJm([k_=-$PpE\/*('j-UVmj'&fK"tGJrtu1$
%9O`U]C]<Y[U7BGe_aSEO-\FEcgj)a6mW^81QQS!k#'m?+IAhDKLu(:j#gDi?57=.^KBf>>#EdoEnRI(:LYdNho!_QX68RQmc)\?Y
%p]aqqB)00(K\m)dGVZcnLMGaW+i(gie$R833kM%[#.@UVqq;3R8Ea0eTMSEN\SN57G.2Q^;2L2b[WUe;Ak?I;^i5_&O.B+UnaH(L
%R"RibNQ]2<[9)c[>\;+CH^Cs<TZ1:6pFh"X2,:4.'Ar5^`\A_f_Q%LXQ7l_G_j,SEn.8isJL!>dhnZXTJO6lH(m9H5g9&RsTOh.U
%>`lo;=o(`'MV(AK\s:&ue86C$=+qnMOfLa9\r]C'8!6h*8SS_f4IR74fuR"u8IEGSKetYR$p/fm`.u6Z'#EjIfV/)NoRO5l+OL%<
%PnB:mfK'(\do&,9J^K&fgVS5YXZZ@`C0^b.5.*Y8T3TFO:Q9i;iB25;6]E02*'C#Z\AkKap\b$f^\-dRj6(pXs*uu0JfrVun3(1R
%eM)[p]F?`I(qdNbE3E[);^$\sC(.0&mjT=mg_nW2&`j$X:uh<G\Qgdaj#t0U535"MNRMXWV:C!g<SV8QnnO,jY+3`2703LM!rB)%
%o1oGTpM<DJ3<-+3peM+M=4k5Rp(aer"gQ,Q>J]sRLH#D/q]\";)%sCeLC4d2I0t^])X!3l&JR/*j6#j@;:[sT?kQ:B9>=Za",_=S
%TuQrB9A(iTchra`Dl:Th[9m=Y407u]leB>"^;-Og=O!77Z%KH2-6FJ8e:_V$<[@N*3dDDqXXj*iS-c3;'S"0mFtA%^k36"D3S'c3
%no]JIm3P0Z"+a2+qiF(_74@2M[K_LCU,PJm)B/o>Ju2?iZn[L(9$4H80F8BHYJqtRYUmBKnY:-01/LrnJNkbZhip,b!p"P%>jj6T
%rn.8pmtXh*TU9BSZ^&:+ND#AboUjb8(,f/o]$CM+$Ega(q3s+(0,_$)+s7SuK#qN"hWeO;jC-<i0h6_bqTquqkuW;O,e@1VT]kh\
%G$b3ZXLusJ>'Y\3_?irFa^CaFbVi,&QjrqdIcB6<C60@OCg/^22?rU+WJ+%4A*d]'c]TX`(h8ETIrpgk,f+ld^_,6!N>;a,CY/0!
%I<I*DRgYgAHID&7_qn_'hHnPO2%]]GoZ6L##oJH4-;SQUk_Fj[MB$u2AYt_d;oDnCrpo#&(SCNa1#MYl9:-`j,21$:Z7hAGV9*d[
%%(a1FhOW2D2uE%HC/,h_N>E#3/0$s@PJ"M\:880.dPpl0A_s`&583.!:Yen-Fl'a7ALg(odp^?%5#[a5deUYE8UV5@fgd8V^NLqu
%N!#KcbVYQmFAOOgV7XZpLs=.jC$Xump7!QsZWkBa0g_9F13"#<6kZF$".7FXYORE;EoAEoM0%rqc5i-B@,ZhkrcnFiQdoBc&M5!5
%\1X\m,0X%N$UXhLU"o)>^8LK0.Uiq$3i`IoS"eT^1`o6?`MP:]g;U%0MQpsn/Bme9N$+U^^TQ89+FpPXlAr4,frMV29=)[Y5XH(O
%+7P5:>a^V:4+iu:<"$aR>V)IX7'i,YOXtQ)MaXneOV5Y!P:`e',?m"9V+HWh\fCI%*)_S-]pjO@$76)e)D[\A_L_t'BY],_!q'PP
%+5#IdT[2&#iciPY%]A9eP$Uqhm1ZrJ>\Pcn%8OE\B&\Q/:j"*mTn9]*a`s-<D_+@8KbCOWW*KF=)-+EcRhQd=!YZ(9,VD5^=Sa)Y
%c%TQtTU[D\6"j\sRtRFF5QtTX56*#i#s:T)=_@2?;e5ATGg]qr\Cl`LT;0-H,1_El;aY>&k@YHFVE2,GTDH9oGP"()m)jTTliQ_d
%?c(m87nKu+L"#:HX:/+Wnr9AXqhebmncU:m+/i%d#VmgL"YWF+q.uOa$!nC$q$U[>JR*rX</uQo31AdDBagK,2rmqnReT\m+_DCg
%OruD;E?_.rK7"#!Tki^tZd\_NdPP^u``rEFbkA=eLs7-sE!hZ5OX&!/!4p<%66XU!^bHX>/SC<jSj5pR_n[fX!e,]S>,.oh;`Sd#
%='J'.\^2_:(5LL[93i:.Pq+-oAhNJ:pjj'\'ghaQW][@;s.:q)I@_O`d6t0OWFjZ&3\]Xt<?A'&1:Mj-?u'PM'$.>kQ1<tFA8t$`
%"C#)k'^JV?'?4=r`k"h\f>sfr#Fa:DfoG@9G'ZZedYh3s]-,HNTkUh=AW_X$Ns?Wgmimoli;$5Hkq*^0(We"Q"Tf;dFrIVQB8L>1
%>kHE?d"GbX'/E?cB27LO.66T4V2E[;Bgkf2kGo<s_!5)=]N$*"`k9N0g/G:J>pfP.30\G0<UB"&(GXP3KW>S=LQSAh[t5GqIWA\4
%&5>="j;iA8BY.#u40-JPjp_ro_8sYWMUp3G_V3f3qq_4bZ^VcMp@>T2QROPpI$>9<n$++V2c@UR&l$^l@\-a.2<'MfXi>prD^;?,
%"El'U24F0:9lV;[;_8L?8GcBH5k'h&JkWElBAT8b,7t_lVf[sUHlKQeB-_Q:IAn9?7!isMF4:JXM^<@'=q"IGeL6RT)H?gDL^&lH
%\-NS+0nPZ7N@JgTgQ>'OUG6sfh0Y`:UWOdl+'I0N]G#!V7nPjZ]2<b1/O^fk3C3D6m_i^XJaPoa[3Rg+JQIkQIknNm[*;5gMsa*k
%NGYpFPO6GRRMu3<Pj%uAecgN7fU'(U,ON]]m)\:+eBd0VD>3D=oo@OH[eikN#OTaMp5YZ=ZnYLg-BHD-2=o*:H-Jpo$f^_Kg)BN`
%RV$A3hU7b)ULZa8)5C>erm(F`QF>@Xp:`"n=.6-cj&21h0ZUjR76uq-85Ap'2ZN]eS(,1%pjZY1MqIZa!bIB`+,8i:l,fI`A0a4@
%'IREhSk[5JKI1aaP<!X)1.fTXM?IFekJSR5I6UiFS[^FPY)SZ]"6D4&.\&u7TBM=;P"$3+AIrUq+"Au?$Q_O<`''R%-X-qM@YoKt
%Pg2ctqXc5AO!aUP!]>up59n_)dO;d_9J9)gC\.0:S$t:Ohf"<'Ro3>__)[(n>l%4K-CV=F6VuflEnV%9SbZYKgtd'?+'%CbUQSJ=
%k_Eq1F5q92Q/55I7#c/e(>Cf$r6_I_5l)L2mKI`/0uleBB#H"*^"R+W]q6Jc(\*F,kf"kT\W29HB5IP&k!G]&aD&B4LD+d#Nu.B/
%CRQlhc6l_WVNETCFfCFAUmW0IF)<M>:cXT;I?pC9a`Ec\`qTs]NPT2K,/%&^3'7C8RCUG`7tcZ5*]FS7iZ1[`Z/cZN:,-eqEEfPi
%_[]Jpirlt6!7p:Ehs^"-&++DlbL7O4aLut<s6cc2rerq%CoL]\hlO.CjPB_CF+tWW(6#<.cBN$Q3!j\F(UCW231>cmlk-9\iflrl
%1@/`-j*G.#7b!A[h(>V_]VFWt]%4J22AkfNj_jdTAoCGOVgE^CafB3f`\*"2FR<hGCO:;G1"UaGG"Hg7n)(M\F&'h9n(]rMk0n`O
%?%>)PZYu2BG-bhj4*K>]YTs&5IU/T-lb.UeVo%0C4/j=-9<[\Y8b;\\;tiR>o:DcOT=uP:;=`0VlW]G&rib"#'LF>,;h2NAk\"YU
%oc>MF4f$Tk2n:ednmK6l4rZ?/==?,a\['@#pN^NO812hu]Ys=rh[-j'&b98<kInW0T<jk,hEn5#RQ3liSiPbt,j1;?a!a.D3Ah?0
%M:M5\8[?3N=o=`1G@C(*7<WG9$3O4.7c0P?\#U_.E<JrRS"IVX@-U\CUks#YL*CHP\#TS#MK%WSQi\Y5";Kls`Z`?=mq#0C+c_0d
%B@_PoZ2Q`=aLIFLFb(q0(8@?G)&#*:c=02RQAB6c%G#&`Nf8NMn&=i#>E3)#c^kd*c$=l,\*).2TKC6W[8<`r-?IV4]>^dP+9'_`
%T-OJC2FrD+&d651SpIU'h'D9HoHi*i$O>DRhrZrMb^.7tHLV.5&(>82)0ja`ARsc]+PD-RDMua*8Q%_<c"aX,4=AdE)3s.WFfMRu
%<U.F]ET,2jg2'6i"#_gpoJl7.Oh#a4:pUr%L;J+rZpnQO0CN`UfQib/BlXmNg^^UX1<o+XQI``GC?#MC-e0].HH?bBk#g0F^JaRg
%2LKO'BWfgmNPo[K0]1`\`l?er`bhfaAG8UZM6U0&K"[&]6Ys0HqNF=8X0l!kdpjc9gW=D;?IE.\>!'q7GeHKOV=1]BE('n5#Iq+$
%2!5i8T4!BmbU_!'*R#b*_o,\>_JCC2*Hu"hT_,Z`M4%+mpb4&5]S"hdFI5Q2TZPi0Dt)R&HM:9ngH`!W+-4tErhkOKD?CL8hW/Mq
%T=XF[U\g7aI=1=ga,dAMD]?0eI8obq8Z?r1qR5]KX*pDj06@"lRbNlo\1"=g5MOC?ki:isfn*npHVuJFT7i,C6h<go/%S+"8hYp^
%eTG\r,p:6Xm,S^bOf'&(<>P$LPuQk9b\#&2B)bAcebFETeVFLS\t<V><R3F3fk.(%jOe&ZjDLZ50#U^#*TfJB6%k"M.3IB!#SU7`
%eef/_'hM5U<PbEp?:O;qA^pF<T*L'F'K9$bW$Fp*l`DRuSAVS["_22N%skj.K.V[!"G1"njKabS_M)=%EG'$Y.X`FS^uAJMq/3*l
%K]+AMmIRZ6E5t$gQc[L='n#fI!P<<srZ3\@nM%,OFfXsS:78H?(WjpgagKYVP&_.@LO*#<8Et?&)Hj1>eJGd[9k1/VSJ`qV7M\91
%(i2l%4#8p2H-PX\T?rLe1g=],Fs\%h$A]Uf+@QH>/3d5QPb)J)dsRqu\@Ak<?j-p+#j%fa;2mgj%AdMbOHU['S>Z-5pn'aDn#BfH
%K:tdL3AU(J"(,+I:H,!3/EgPqb^hsPU+k^,Y'++3\8Z$DL#p2&guW[BI-'GYd<*!^njmcu>3])LM^YJR-l7WgamBIknR_8gci.\X
%qejI+'I42Y'Phh/=3EiNgZ!YE\&M5?p9W:q=Kc?f2Lf%$V2BD"s.0<NQFU2e(-_-Uh.<1?Gm7kRqJ9GEcGh8j;5\2Ko=\o*Sf4U9
%kV?rKdSpCM-IfO^8ue=?k6t^h&[NY@c>nZ/=]3WGX*YY[,45_neN4WQ:XT0!l;tp[RFG?`%,N=QSYGAh*kU'ibh)Sk*'ol(WbP#!
%/l>P'_L.>p1<^E*S0!7ss2`!l0DWZgf(+lW/!kl%hN'\0?6,uTlM8Rfc;>\)YiMZh6T2sSO65oX/0C;a7.Z%Eee!Nh.C_p"4g<h_
%l)%*W!T&-UBaB_Gj4%W$g9gkCrN(t1:h-+9r%oM')Kmccjg\mnY2uS4*7\d>,h!mWFjY51CYie`'rnf@9no+Y\r6)T%>^J)R`r]H
%j2H$pMHS=H%@X1lF#BtcEL7ZpTU[V$$CjO`rl1)<Z!1s29icAmc/lI!c&o1D>@:5a@Rq`"7[`:(k@F:/.eB4>S^sd+JQeXTT'",Z
%3DO=LTUHk5^uo?b2hqHn:<u.G6MNR?]d;cq)Ts<3CqdHh:M!.=WOJYEM5*LiG#4s*6!@k+aUDCEJtV7WSiTS1TZ)kJ("`7tQW\.j
%,'3b6S@28S.@o$u]$pJ<-WKIoQ@Vhrq8?,J@!/@ED.#LQUmSJ+hpMl9\Z:C9MIAB*2B!qf2S2O?XB8c#8(07[bG5.(9Ub*@*$7fe
%G#KVDnO%G52KMW!9_V5&Y7hgo(o=ONpi4kN:FfLFhWUUK138?`C3'9#M;hPS*4#%)gpj5se"S:mY@X5rloW2#DtcdC7I"9>aL/>@
%o+k8.<c:t;8hQRIWAQnk8(JNA/UCufEnOo1QBu$_1M^n"CJ'W3<EYu.$!Xl5m-rtMRoaZI=pY$.A_)W/XG#DUONdPtH'KpT&!0m8
%0!?1s)`\q]VUS=S4/L8W\7_GnJ@03W[VSmS>W)c8$E3K<;0Gj4mO1-N7/-D]53o(nPp9r'`UNOFETB0%jYD$+(<TfPGHY`#8gcVK
%QW^dSo@Cu=fqs^hj!HglV4if;<mn)qQl$FAF3O!,RT8MNY:m;\*D&_:,X3&KBp#:</[V;%,#@/YG4sK2B/OE>+Ve.#&FQa6EL.SJ
%gXOeteR:_+ajKPH')cL;*R20XC.;jAMUdtU,gHG>L0,7$:$>9uhl$\f43,HP_WNnT,1_\r\>clgJ$-_-riM[os,Js_Vbe0_,NqS)
%*7-#:_,RZ]iTOd!=HhsRUYJNtDFV0u<m-W>#%TFbP(I<\fFU%5NHehDgJa]R?BYZ+DR.YN8nAs0Ek:)CR;ZP4f!`.-fm6e'qQAd^
%pFtigDUi!aK%mrdpS$8!WF[=ee$V`6@,KMlD6MC-1=a"#om1ssHg.lNZ!2U=Uc/(;YO\9dUhb>C>g?rTh9#>dZ73n_D'dr^Z=WjX
%/MhjTEW:m[<fmFBX&i\VpYV)n=hGh@>/BsAE2n[_>R20JdPNO#/gT:_MPQ\N/`oT^gBiB\;&0e6Du-KQ="&GoTp#hc>AL@ko[Yg6
%XocfS#,JriK6glS2bE'7E`nS_D?A`?%qhPk#JSNoac4;Q\^OYD6L5#YU5PW(:1M5qo:<m`G\<]UJkj\oB0uE`T2`beodqV-L)DqF
%W$uk;0?WS*'7gd;))hn<;.l>P'<,JMLu2rj;tF#C\Tq?&\]``ogQW$C\;5DnR;>Ls+p`=qkgs#HPAlF.=H-USdj]0u-]39\1KYH"
%DiJWPhX@42lc](U*=k+j>X81Uj(,ik*LF%:,?jee;Eln?6B*4XhepZ>Y)04R?J]n`jka:0@NgCCa%_o$4"_f]`S(R*bRgNjZ'/dt
%0Yd7aI4hj<N@\3W;q@HjC]&uWQEjX%9<FWmp[Vmt1M*+9q6;@:Kis^s[<^Dtb9BU1,!XXhfj87.,"VVi/(U8*TOYP!V'Xr0^hP!%
%Wj[:$(Y6V*3raTDdsT!\I)49Z_:*eJdsT!\I)49Z_:*eJdsT!\I)49Z_:*eJdsT!\I(#Y#Ga^:1RP/oS:Ts9rT&pEPjpRdC*^%\m
%#>]CoU'>@+dtK=T;mV7Z=lX8*aI4ZoNnLkL[&tjpB#`IOb!pA/MR3sgRqYN1D&\i]7G[HJXFe&]j@tkqG)!b?I1!>lOa9+2`-$'4
%BG<R(O@?Lg2,nYNG?Mb&>::Y:k*n^1N_mu;Ik*I/lFig9^Ln/FNa=bWf+,VgXB*&Dl.]N'PSY5*K3f[2H1#Uqg;d7Ui<:)?&!R6l
%q)?7WB,?ZS3UT\,m13N$Jr,gQGml3r?[*]J2=S9"X?GaUT<BnPH4X+9C=qBHHOFr9G2:n7rdi`in=f_6QA,XMJ=moRo%hV0S'S-U
%g6?`OB#?hFo&di6?X,*CpI(EKHuF9Q,-.dV=lU9VJ*24U?Y[7:B4TOn[+F$O8QT1LoV/2LF'.[=9;pS7L/i?c0=o'_\4V.Dpougc
%c<76'o;sa6L#lWa'&>>g2p^.EZC>O9GLQ>,_':^k*L^7D3O7a"&)?j'?f-@,4Mkp0:h/4DXaDnQo"EHYn5>HSG(+e!<7?%!F$@"u
%I\3oEX%5o+.%c1W]*Hk6rdqbC7eU+TpB<nAmH:%rb2hE@F5(IGQ^94?1YD-ISG$]"(W,g(3g\/F&V59dm:[P&.)GclK56@Q3*h8/
%2YZT%_)jR\Df63Dq[M)c\n--H7@I)/HYC>t=Wg%tLrF0/V`baS<$X9rLWa[;Y1a$"CU[Y_Mnp;rH+aUo`'_:ZIppZ4rIe(WKe,5+
%/H<74+(&d\;WF)^5C\[M'3G)tA23;QgXMm?ir*#SMc]]5'uJTW`h^<:-iMlGmB1-_$#co$A$P&YI^Hi$@?]`"RH8jZr-'NN/*N0q
%.b?k\qj`h[MZ#bCRa:</2^hRLRIbirot:`>G;&d\k5GRrRX2]c@=<dGL[r21@b,S8'0b>!mr6pm_8.nC:H6sWEW*Zn%T>:\SUBqh
%/K+O#`A:f5_dO]sDksd4k.CX'L].*SQI^,_W^Dd?AqsaNhsfUZ)ch0cI-fZ>=0oYk3(Nn!OQl!Z1AqP<SWN*V*H_-!5(/>chnc8/
%DAY?Uh'L[Wl3".P,M$MO3p\2lEK+4qdHb;/>o!_>$,sVDZ9s3Aj`_:'93h?,h7c:echBW*Z0k9'q\JukUYjZOp^%C*G<CnXbGK)U
%NU;='TN=)9q(7VU/oe@r\_+SM9?d@?PcLWu$ROn>,5&.)rd]$ZDT'-$S/u"0`c\)m-^Ma-*1u>*JcECR1UtU%MJ]jN\q+<0iW@6l
%YR$#";%>9H1J?EV%_dsH=PjY''D2>_/oM(m$bX[br3J*5/Kl5E9MS^H+MN>\paE%/3Nbt]iJ.11;m_LRfLA[gj!_"]4la]u2eDGj
%lW*S!-,CYLElUtdLrrhdM&F^cj`B,O9(sMip50iV@^k,C&$S7Oh4.E3HpI"<T<7d4iVPf'B?:P]p1GfbWH%)JS"&P-cZj46kTQ@t
%O6JeA,WOBh?4>k3$LFl5n_0A%]Pfl5hL&cqP482M,?kfYh-C60Mn]Zm'N.hP*Ug:j*Ak2o\(fgOf?rNK^<VRe5IDuTXTu:-8eo+r
%HO>b4MZr#0J2NHZQ[[`l0J'B"UhNrFB*I)epE.j`5KhQPqT1N,h.N.@kWWEEI/_&%JI4ST7^/:VT+J$a",X44o$u(D^A6m&QZ(`q
%I.N+@:Zd>_o5>[7qY<)B]>hTI^&.6P5Q:6,qtDc#]m9P>LHg:YkC`]ui.(CO*KIW<If&<KO1rLB?EUg7m9f?irrYI`)pnS38;EfX
%romeFiG85.Dh%HTd?B<K9E0@@s/Na:R"_%?:Pn0(:$O?9V:1d&0'\%<]toGQJTF`Z!\>>(;?I1Cn0,]a,7\#H_.?eA2:-l#]_AGT
%LRe5"\'KSA]#2i8CX3pk!5:U?[eZCt;j]Vr@!1<nQR\S%<=I=E#IX:F'SkhZH@qJZnts^5FK+9a[arhA/F_LPFsGh]dXjA:kjbO#
%5e2<,CNQQ=l';*hRdLL5F(h&?HbASs-efHL7[JGP9i(CUdfGb3p=lJC%Ec?<-EbamSk[[d^)?E\K6O,-#<^nN]Jm(HkYf`=^^#;:
%FMECld^JC`'4]b!UoSU:B@HLG6dpPZ*Tm%0N:/Z-[6E^>lRRB>P4b\/?O"2STC/HjBIRfqFq<EFrh&)1qg"_reH><EMAIL>^
%Y0k9Lr#WBdO'ej,SFZ;8*kq]WqVF1(:%UlhVL#Dm7m4W=?a]+?oG))Oh;h]9[]2W#G%oHINBZWiVXmOT.;ds'_jT/dUH$Yf>oNB@
%UMo!-fTo0=gN:*MYf4%F&,NSg;9q,rVWe).4dKs6iPr?m[nRD0ZADH)2>X1a?umpf$ojcG[I+IuTJ)cOoj"81RKbdMqR1+l_pJX#
%pia[dqQbrTcSbR1C#<e8-t(f,lV5]J45^d#\`lUIfBA^$lO<nR[nW2.1t-91*qX"WURl8nc%#o&:*jf@]&/2%l?9<8I6"O,Snmse
%dsq+]a,bZo67)u89+gb]U1a60U*oO3Q8eAI@"pnAc>3&L`RA\+/;`*VpN]2*QW,a.mNs7,7ee97\EQG9(&ESO?q\3J(5=kf4X;6+
%b;(lf!@rE?28HfsWMmJn3^2ohSK]ut',l0C,)A0<'L&\XS-r5I#71iX\=p;14=M>=Qi\po#auGiN'r./6M9-g;P>],6\k2K,%RM7
%WAt2rYn**GOrbg^g1D=r09/_N9:pXq(-m(C@1Oj22+S*$F*F;1&<c5!i2^/[kkI`J!T]]ND[@X/=P/G&]>E(h>DAjb"hJ3?#.u%=
%8%M`'2c9bM3&DLPhg(_35#Ss_p@pLoM_7e+*MS4j6^?X]\g.qp,PNYBjMUE_5I^U3Va-8)VD&aX>jM+X2[H-j\rSO"YDc4k$ZnPK
%)LnU]#ZPmX;rJZeTAJ)sb#f^dcK)3\$K<AQ3m.$O(^&k<klpcQ0pW%R44XOc^LXOk-i0-r8A3NL9'`s<pk:StFgQ`"G]'d5gTqs)
%53<V"^.'+7`&e:j8OLK%]#u]]h><osVmo4"PAXRn;2DCPaMd(ZOIcGXhm(Mrkb*>o>6O1JVErPL.]=D5pH%NS&MF?77%9%7+4,4b
%#gWc2Pb^_6hH=W5Z/Sl:6C3d05LnS@&N<n"koE$Mn5&9Tp/Mm+4GnRl+dJ_<O\=%]E&`&E<9HiP!.*j7<9N1J,+mTW9dOjD,c!TR
%`Es916l^+*T#(h$PR2C[FWnW;$ZYe@^)ooJBdaOW0^;AdpeJeLb?K=tY<;2&W!*C2$p#@872@!q`57+SQW4?uW5Qp(X28,K?u$p[
%5U(7j;n&1t/1B\Lmct^PJ@;Z$ocS!;l+gdK:I@MH'Q8HHPJirl1H0=a,]2rj80m\OALKgi:2213"4&:5KH[*WD"-FN9LYStOa+%U
%H^6gc@b'U5ag/TnP(f9f(qoOp#8g7(;B@kSC[`jLCI8kIFC#H0<d>3Er\Sl",D!)YTi[-n1G&RaN%e$3p=W*sEHJEFp%tt"E2O%+
%kek!0.Wi-H+k#1h6ql:uN"VZu1XHLNj@i78c35>&cWhKHE8&C%8?h'mq5JEGc)"V*A9"Tl`.J=2j91cI$sj?%]_);#Al(sAI!rJH
%]]H`2W`'6Hk)Mkgdk*rH'@#Ja..[IcB$gko(3h!K9d#%D(RPK&$ggU:.]$`j*nf.J>BH:#%ieVY=RqOp6]aYdB&/tR:QG:!)@RTc
%a5Y#I"*+h;HdG!*c=/F6[7)6j:+AY8Wb5>d6?Zg[Rboi?^Y%c"r2e\jTWgN^;Ht8bGYp=Zl'MIUE_eHkf0[\R/9i#%<M.C,M`IgI
%G(@p9.Uk=`bF!Umm%^=N^q0u20T&QjJ_(DDPDF@[%D]f'ee=Sk)@Pu\CajVWQRm4i;>Y#]PTZDs`H>j9n"\Pg>`l#Sd0GdF9B\^D
%,4mbTO(X^n\ms\&-SNIo$SH%nA23P?77Y:o>2!)H:$V2WVYrfI1$ph4@$/Jo+jIdo'G&-DJ7%EC*_IP5":mG0`tZmiK5F6,LK87-
%i+PDW9_8ZbA$"aG>aIE)*A^C5aIek`j.^!,*N[3H>cK7Z,98@,9Lj^B'5>i+^\"18Bal3h-2KB3+@QGERa0=]DKQpOW\f5+Y!cBi
%6;1^>Go/N"!9:?*NK6&(Nenh^rMB#"!ihi98Zc]OfKE:N:j=)MgZF`!9ThX(n1l*A(U6E[DG6J^A?.tAAYtEM=%FM<U@"uiqkln1
%\n([FkT](24Z4r(-e@4,Qunrc%F,?D%Op]GXP^r'"3itBCFl2K)!aI*R.n%!->WO^[\TqGYafSkY:sNs%]])mC*KZ-4ZJgLM5fI=
%N*9)%_DacGlq4?N<52ZnD`4?Q=G/-PoK4:lEA=<slmi>RnIs-H?u^:jU$@1sB(<_q,ShW83?3Mq_Go]TWC[OIOcaV178S7rI?-P7
%pqpO*%_Lb?eoM37+In8YI.1E!(ao33>V*UH[cgRVN%F^7:te;S!Vo$7_,tIk!`?F^5uF[\!t7X78[XJ<#p124@$AQW[MfdXD*W<<
%_+A^iHVpN+mnlKQf":RJ\?7q+C6l1>CN^n0H=gADgfToMjgZm!9\KF$NfWF4,eXE86$/L/<iN^@-sd#fc;^tI^-k,;Brenr;*L<o
%6!c-;2[dte_\giWpR"':mO(mjW\7(^i6r:p`H`+,J.?K:pgIY.4K#J`TN\Y+0G2T+ENdcE[2R*g6>tgt%#<Y?rl_\#8u<gK#*@'k
%YEb,6jo"*U-,h7=OAk=g6tP;QgIdSl:[>[R)t;oXIY)?S?<+0M?.E\$_'IS9U^!Kuq#qjXZ=[JYF-lDB4d_)5bF+/Vb&a[.<-@$5
%fu=O=p.t38U$J3*=.C$[%L%MUX^O"LYAEDf:1HkuOVA@LkQ7@9ihdngpBeHeQqb!*SECuZVra;!c4$!U6XYNN&>K`+[q,Qi2USRn
%c<:D&*&:<IZe%Kq=&i_G6^EQHQJuuQE9tCsSQpS:/CenZ.MmTFo%#]An;a53^aIR>?RsaYc86:kR&EZkZ#%J9(%]!LEA1WYFQjI?
%aK/`d4Q"LG:kXLh65N94EC"+2+9U:G./Y(p:.]1#`?bj'"D)jk&gj7:Md]p8VUGV&IG<Z4b(*c`Mpt)qi+o/*ZrtdCB?L_jPhJ:-
%/B[1op8^VU#3=U(a[Z6(,c6c>J869c$k\'Z<$>jL"rjL!e/l#PW_trA9ElZC(>,RQXOnji6kHYf'!!IoQ-TW--Tt4Tc,de_"=BFV
%)]cCIgtp_mgl0R`OJ.Pp89BL+o!&Z4=_nNN4nqFr6rnXtkem^,Cdld*mu`"87*mtpa["Z5$9bf"`[[Th^rVr\X(^2=EmIE);:]J1
%Y%$9N9i'1!.R/kq8_4kC/f5d5Zso(7E2L]:JP#E3.;.$FNHK!^gPgcUrMXI`g7V>bCVs!Q._'41NcjGKenWNO2[GFHL_pLE;XYlN
%"ua#s_*oH#Kbh6jma_1e`7T*A@ai"[FL%*Ohl95;W9AONP!r2.k!Ka&*LVN0TQG*A0(^=gY@/VXd%8T03##u7[sf1R2rjLE)EQNa
%,1\\3<`_#V2D/mufPl^G&mKEY$/Y$Gn79QeTBkG4?GsfPl4WEj&X7\jP.o%+=9kQmg3H9^iemAZO%/iuKd_-7.&.AB@,m*P[>!c,
%-B5iRmYBU7relb)k4Lu+c3/di\0ZQ4\gh's[WAO_*9k8u*pY;2[kGW7eCb0P#%B;,O]beBjJeGjXhIH9\9Y.[LF.,J!p;$j<0o`!
%&5Z#d;/<h/6/a<U9>QBLgYa!M:7\SR.KqFCfijBn"34%UrB\Q,;4Gt$(D/Ap0p^Sno4ZFo1)shp@j5TD7#]?ar_XaanXHX]-r_q+
%nA(aV?c6c^2ARIa'!V2WSQV:$*imGdF`o3i/I!5hKbSdmU:&Yk<LfV_N$gJ#&3ACp7=]![^Kc'M"j]Yr4Suoj14QE5+f0q\M_-Be
%lS)C6e7FYE3CMi-E:0\fN1C83]C:Z'/e:HUMc`>eC6pKBc\KI!@Wk>+&,6Ga%qgW2cmR&IUI56uaI3t4,a6]?hlc*Lm!8EL^ROps
%cCL7M$H]06H%`7&U!s#&2$q;6D1HGk%PlYaMsS7G-DOR_c-$035p)s+Rmhf[>T]3B`%2IGj(J'ZRL@SM:gQo>$<=.0*?o+K/>W/+
%f,@h+7;D$q_#UV[q:-Zb,`b=b1nLYHTZQQrQ1<geoH!nNQ3=4?[)2):/r'(s7^b<F,`C[o+(gqLG1OX(YQ_?pEA`ls2/KI4@n!;^
%1W1?g/ipTnJDQLDc@F5t,#!rJM8$3r#&R\S9t$K/7:V<_m""T&WJQ,&40Ngji0oIKk*eTRO?k,UW"U?oBMkhpXHB5g8q_F'&OuXt
%,h-?0,W6tJV8NR!*WnDL>%V'aCdfIbLrNiG'==LN@o-:[B-K'Q;.]$'/(45ZYt;q8%V&j7c48he%d5]Y%1E^c*#S!/b"ZGqnTgc>
%b73PjC<,C?7-2:TEuD)PS]@Gq9JJ9+k6<5T*Cnno?(T9f>Z5=>.V`K<n,j?gp_1i7=+d4cGT9@+cdc>OIUVj\keg9j4B1_e+/$,=
%`=PE^DK],ZiR)`)RQaoDR^nSH&V-7=3kf<'X9YnMoaQ`?#)0dk%pQGmjZ"T]nhI5oV$_)`b@s[.'LecDX!'LO]"5u@`[H?bL^>].
%j<5KoP8/m1o3r\5WWcl'EJRS=Xb+p3i"A;6@<2beec#ln'Gq@G==/Eu/2sl@ME2aMUr_)UO$kf2*0MH590W?]*c8VPJNJr":5OEH
%rlIp9W"1,9>so[cY?8+r;c#eiZRQG<au:ZT^p]PaMm>=gdOi3;=tX?1fnb1`&X:U9KNIK6J<a]3$b8j6&':iJ:!1/gPAcsHD!OI"
%=HN$BWk8?VRR?c;nm4g!=FVF1:.MtD7FXu1qnbt+/ZL2%75&T<>G$Z+$)17U\n`MX>oWJKPLQ*fQ;W:aknYD*QO&:22\Gj1a%@"X
%("-s$l5"nmhn>%\lV6(W#Fk*?3[2N=_[lk(hTV4rWnP+BHBqi4pa"U6jH(*DO4.#eG^Yr%^X?=`2*:sBD"5H<r'[$l@8H766O*i<
%D*BA7`0!HS[5h'=/Y/+<-2O#hL-5_!BEXe:+fD-Y7!46JDUasGU#9f>,%M]9"gYp'</,T>;%Pq4r@6iQ`.C-SD.0%YL<3D+f'58:
%$p!8o,:ggMmd&p>7%WPZ_4ekK<-`O"?\XVX_%()F7R+hEB>XM>oV&(V__0l4mYBp@$lpmB@iKTsB^d##iFBei&I4Na;*<3uBBmA-
%Z0NplY.A+\<DL%^i!/t"@U8?_Q,iFRSVUohb3C!GDZZqO+\nr:fZDkFYhCJTbXU-kRcHl?TF?l;%b#gJ_p-C<^8Z%/Zu#/IDG<n&
%g^JuS3*8^HHI[*Mg[]"tHK/ju8l%&p]Ff@k%D*I:EPIUo</B(\+;!51l]LJP*2.iM`NVoak)#O8e3KI$*1WFW-8mZ[4:1'.]o8"Q
%M_^Jq>?Fo[!DC4HB\V-+3O90RjL-7)(-,Eb\1Iu5AAb"XkaG4g6E?0ggPF^%H768*f&kEW2!fdQ,%r1F??nW;(*9!-FL?I^`<#8n
%N3cqo/?L"eOC4)`+oY'`\!jY)@G$BBL)gadOX\Co`#XID?3^p>aDi?"/E;q`;ZYC+(H(_6bO@EnnNR,AM#sV8^L38d4Cmb-*XD8a
%l+ZZHPbB+1_bOVkoIrh1-;>3nZo3l0Gj')t";"-S*Q'Kb]8?cfGQsFJ7:7LiV2C[ol**I/@nd*Yh@3!8@gfkP5\dMh(C:CLE0"VG
%_0nq(0EKbX8k!GFpbCtr2t>Zs;L\sX24rmXn[qcn(od>#a,g)ULHditpA["X&AAkC[&B7N2[^'+1E@[#EZ.HO(ZZ/[j<l$G$bbJQ
%B!i2(7Zm`_B7unQ3C\U"1r.0j7a;_O&N=-(Oa.7J&o&gt%h]Zn.V7lN*pR(g%YQJ#RmZ\3ef;42LmJ]Q'Pu(_ljR8&X,?abd[8Z%
%.mfDf8u3eNnXp2+`KN_hT'Y94,+t,/9CPt5J>-a?1kBGAV:QNCEPPY6"#X=1(miO12])S4[H*i)dnCL@>q-&B+gVm/&c-9pMXoaf
%_.k4lWdW<7*aL.J09ZJQ+aM0tGu;s$e0hs="DtJD\b9/]7S1Z[_=.3BqE1?%Y"=DB$]5OO*A%JLa[;$AdiPr[6k<'/0P#40]=j2L
%B@huc&0-=\Z#:hBH`+TLg21Jrq(=!sm5]>''A\>ZD;t+OGV<8jg,Rqo=%ESU#-ors['VU&$1XaTGosnJ]=/]Q'&aDQ)PX3SXta=7
%]gY]cqFr].(eH3E)^OPu^+Ef-UP>QKPN^4,!t8FN,;'KN.mGVkE,RUU2N8r^'cSqIXGc'l@PNC\Ft2G_N5il2H8RUBdGU;<0qT8S
%:NR#dTN1.5aZ,Fs=c<,7M.gS\Q;]\\]T%#8nE+D;$gPM7N6uL&ESR=Dm_r#!RKN8W-aB<^&>Yd=`6["(I#F^QNY:!:J.G03iTusJ
%U3\MM.9IZ;Hid)'o@W.K52)Z0P&6(ff)Yrh-0[:oZ6^:'=%$udR58@TYYS>B;1jNMJ[bjR'-b&7n7W03Nbopt"?N*l_r5KbN$4V`
%Qm;],#bk'M"Lkfb)md]`a5+n9gLQlI?4kr%;c<-qCVqP.]rA9kX$2U1?UZKPq=;B9V9[^$=fckI.o40K,rZk8XPj6+Q8C<`)g#L,
%U^jqCS0Y.q1s$&q__kSkm2"K??0k4h#$G3Uif(%(/_KRI\Ajts%r3b77f:9KVdO02o\;k0A5NcCWTGHQ7lnpi>4Tp2%A]F%St8p\
%Quk"*m`4.X+<`'I1&ZF<TN`Li"HC?nL`fBk$R3p4bTatK&.Z<`6S99*Y(&0/'%,#n)tHRm8laF;>+BUF$%BZ+(?Ae0A]g$!?2pkM
%Fuom3(Z*CGJaOH]p/Jof:B"7A\1pIDM(mG"O=Kf<9`u(ocrjF-Vi8^7'_9)CGc\>RPm!#s_`['2]qo+tEL=)\h8CMTK*,k>BO6Xn
%i3P/;Pr?+nQOcuNW%8('IAO9Lp&O%,Pu2/S^'\n69QGl>)ZmW0c+bDu@G8_A^1Np3$u?2D4aR5m7!Sj72/,YO_^N/5B(Qe^Qpj99
%:^/8DVDT(+XI7#h(sZ9>2NJ?cVX.!mAa!E1)[pce<;Litf[rn0L<:)^GYWfP?%c(q+=]:%a#lpC.6+TcPhqZ;aQc^`T6*8T&#sZK
%X&(".Gh>sG%P'.<9q]IM?cA*Ii>X,c24`j_`^8u0XbJ'Wld-&#R6a7U_I.9IcGr6l/poKhRDM3mWO],EH\#ojX+m6X+LQLlV?%W\
%Zjb_6c8'1`/[,^7]<an6at=Ak(t<;\.*LT=oOc!^m."/O2rZeT/YZnkGL.qcb7`KbfE:Up39[Gt`EMY`"i^WuW*1Up$645CN,8-q
%Xen%,*[qdQ8"LK1SHM"Y<7PT".%rk*\o(UVRWu],Q#+Yeef*+rfPl`m@uN\r8kAE.*+YB_WB[Yg*m[kn,3jVI)d:<d6fDb<_`2r-
%6Ye01dL$T:6]Z<Z&i!@Dp21Pm"mO$&Q/9.t<G=T5SbS:cUfn"O5e^@ej%@6:iC/m%3J>MU+s<TtL3nN56o+e;2$hme;l+QM7I(\g
%@imFRnd+*YVnI;k"Bh`N@S/r4K'no&d6nVk+T2SYS;97&Oi]!t-O&q#5%4(#MeGsu:,.''bBt&Sa:lr=]S+*U^mGQh9dMc>Pt=cu
%B&LT`8rg6U8f(\jdCmgkP>E1##L=-35#$.87`&R`H7eb7$+r!.ec^Ae;@V*H:!,%=qE@o*M/!SHg!f/m>9"u!dtDVdLc@JRL_KtB
%0+DG3%$U<L75Vn'o@S,7IOcC3nPnb)='Db.Zlmr$5k,Q[TW:cKa-ZjZ1/X0Re3kR$/$F3)_F[OR*kW?6?kBEYDA"JRF&S[o=ln+_
%i)?Ba(79;X&HoDgF/#X\MqoE?MFE(!P;@/Ra<%O,$Osn:*`S&TJpfL.;gFe\&D4b,k&KV[mY:V8fO1\^lhT7l17`)Z3ncH(j)N?)
%7eE]9gJWfTj<.JMMG8e"ibC=Z^+AoSCO4u>'gg6XRl5A/PS9U)j-=,3)17MTU5M=?;0cWo[L8rJKtcdX8l`;-Yf5&e*^0#\5\L3Z
%'8ZunV[@k4+An*(%Vd:9hoV`C6JqF21FD@cSeH7pY%DuUWY`"jQD_rPNLD"<^78U]$7$_%4]2'e-XErh:Mi636=<5p^^%FYS"58H
%W"Y\<g1mCiHrW7<U@_/U&jp$nh#jFY=7iS[#)^Q[?.Xou1,GtS9[kH5@a,,[<0g6r$`0&8dHIh>Iil@EM"%:5CNKt!*;$DLTd:^J
%FLFJYN@O)NeorG"iPJ!>eBf-#>\N-j>1Qpm/Pa]MEqUA$5]!k_\gFs\2DkU"iR$XcboO@)JlOmB^sP1$'s,Qh'D'bSDb$K"brQI5
%At;J9Ni1$*-)u;\7g"V1R'>e>0Ejg%4;=C6H;lGte0*>GaCasRc%F<=_N>6:6-P<rb@Clf"-e]NV=gDh@p/u0,/]@b(j(ZB`\@BW
%keJ4%O).oP3e\HVC+O*_7LfTV]VG8<`aU(!g(P\TaZ":l)O(_E-CWLTG(:P?e!oQa0(H0OZ797VW<VlLXD9078"&3[UJI/gNK2EU
%88JEf70f5&27JAa`t2lWs.D#$dU:F%m+Y2k$Te),//&,Vc1fZLH7F!.lh&O@K5G];R39(pje6]e:s)T\A:fVhR11"?D[@)99FY1V
%4cE`U@/MlCJd>SQkW'<f(NG,I_C-fS#(lp+0]4o;RmDQb:DQ9j03b7!L3Od\lPZt`\G/3U+_.;,Ju-P;_DG0kY1faX,ePP>MHLaS
%\nWk&[IZFB+:`BdBBURk?c1r+b+)jiLuaM)_h4/,=Vh0LoMpg7-&h/3N"\]sWmPs(g+X*r<WTa.a^p#E^!&iFH$r:\N4s$=22f2t
%6_fFR60Ld6,mrI%KUn[Q162X]-T_tRCC$-9=TZ4W'O[dbMV;]iR_jImqaK1!3[H-joSD5ePfes_UdP`VAgCbp0f.=VrctCr#e0lP
%#]b-j-d]a[H2E&&jt3`#-Bc'5Lh$fKgKKs=,*\NJpEl>')Pt=APi'd7.A3Nb7AZ>"eNi3"HF1O[?tf%Q3$9+_.0PA`*9,E<aEA`@
%=>he3lqR-+.,)](7J_bS-QVeu1mo8Q5]LTJPZ/rnI=Mq#)T0Yqo0W4n1fHi87Sh+@bXT)`VM&g6Qd0[S24Iqn0D8g8XW!s5!a/^K
%?GZ,.A!IVRQOM,-6#jO=;jq0'*/5fE]dd-=Rg"5o!E]m$IWDF..HQ-!)km4+dedGHproKUTVO`EjH[%!qofd;]aH.DCD4IVjI9Dr
%,09nS0,qf;l^WnYH9"'n_:sfho?-Pk_,IK,_%AY1cT2[u&;G.2,FNN0gc/P1O'sRE;FC9Xc#oaB%Z_b!Q3$(m\dSgV^d9JG)$;9r
%/G"IiBucS2Z&al3p_k4I.AMUWS>u^()S&tt-:/:iP_$[o&S+C??Da^U3Wh>:0HJGK";-tOa%Qg9/03fqhZhMbA8>V"1LQGdN8+SV
%p>H)2i!KU+V:^aAE=IapB]<qu*uSJ!$<fM:`2JV7+G#`cnQ\J7P4_NH+:*_h#m6_\#765]b7K%e"!")f-p7p[C`J)FiDnN]e0W\#
%_NPES/9H!k-9FtT*T4)gNrdj%i)hJNf-B+:0XJtm)$Ap+7^J$+8W(?Y-si>6_57P$MC)Zp!rK<H<dbY:Gk!#RMW\MMX=$aC`P`'N
%*94=!/UH1;)e*oa"IhCXT0\JM3<7Qii0V<r+$T#=5hIDaC\S2FY"S,ViBpYZDj`DCY],P(.EjM6"4.m%B(;!LQEEQ;OBZb`)<NK-
%1FA\+?BO_BN\<E)9IA06;=q6f,#Su+LY4ac*_mK=U3R:V;+KHjGW(46T[SG1K:Mgh?;NfelC0)`"'MM%aP&M4QGp1mW+56E%+[dE
%1(-qdO?a\:!X,E]iKEe$#)E-T"5^i6+lWD/hD"_mP70)MV/X9GQCI`47JKG$-=I/pl[a<;U(e``F]ir@#rr*`iAF;Ro!VI60<KsO
%RO"_89HjV$5!>EqY"S+8R@AolQS)-AXp0$!=,p&*S/EYEf?UpI*'8=;@+MiADW`Vb,m@7,q)ffV=$+<QF[D'ePKkYH(WC>O]2=Zi
%dRHj^Y3)cCLJsE<0XjG-'XOC+&N+(":BPcp+am%danD1#UT)J&A62B,SiUA?Ym3uZFLu-JW9hRAC-u(T>%?>UYB)T:4RPk*N4`H%
%WHcSEUIh<s,*?"J?pdkb'<;E`M/mC#CL3cq8$4AlnL\>@'UOM/D(P5n(60=oD';WcKg5ttZZ00]nJ7^+@OV.8.-eTN8?fEai+[]d
%am9_(o?UF!DP;tKV.an7W$Zr"`G,$0M6!Qp&i(*,H8[oAME=n_$,[T`Zu@K-%F*rLXHh#EMo"S.3U\1iTB2fES?%ss0c?('S3,'I
%Hm!h[a+NG46rra[l4IQp@EM)+=k#GOPu:1'INp8m,1?s)U'5+c_3L0N[*DNKGTM]OZPq14XWR\o(O;IAN/Z(.KRK;Pe5.?Cb'>qG
%B#XF3OZXPrM!VK*Mp`Qk1'F4"r7VeR'NXD-&'\==9Z`<%kNF[f3Oi%C/81atM@6O+kLVT'APF*@=#:-c/Y+@gPg,q/1!NGDjS_e4
%%]5m5LB/`!Z?R>2),TTd(XleM&4r"'?Uh79NFHFO%DSjJ83o@]g$stVQ4<&1+rOTj2>aj%2.VD[UVS]cFWD\LO\r(pKMWtI%*E3S
%K%qchnF%EV0@Ph227*<cN5D"-e*-5#?&4p-@CY3PH)f9"Q]'M1.q*YC.4l&0/qMF8H-Tm>V^tU&lgBrm^c]i+*@ndd$,46;$V?2*
%=dFs@DmH*e0Mu3?e3\9\?"?,:`.q=(R/>#&2uN%_.+5GprRacZM+qC)C@CHZ&(M:7\tllcC9q/-?#VaPSDm/p!!6\*pk9k)W+Xgg
%;"@bX>4OU.;=8'^k:j`,WFMnD62skI+N#3jG%?QbXTf>-hKF<]3lN"(;`SWob[si;0lEC/N`',2RN%%"-@(rF?^C7WS&\BI\Kimr
%d.e'CI[APuR)+C#pa4rZ[^dXtNUjS[Q%C8q27iN.5CsiX<]Ee&%%`,TAmb*Q>Hk;g=4/?*aOqFV>f@kkkSo*!]_WIam;isWqU#KA
%cm:[P4'okYL=)PD8;AYlBm>u>@PWdXId8+94D-9$FLQ5CZJ'+]d9Gn"73(AJG+o+).pu+1ST&Bm3C*@dSC;T5\&lh'kh?k=o5pBT
%gp8#Cp^/<Yd7C"Gn0(\9]r8cnikk+diL#i3Y\C[#g%"@b4DTk>I:DI8A.bSPUm:)M_!XcJd/_^6qg>gtDtoK$F-p]7$MN;2]QHV?
%=EbJ"AW^e.#d!#R_W^aDUjVC+8/d:IJg<%l?4;:WgZqC(&6>MLMDl'7A>%fHo@XaK3Q9+?f@!tqSSOCjRu'FqA^Pb`8I>#)O/hTs
%,R;<LBdL?`%PWC5#`Y5-6#tIe?<e\njZ\#jAY3Q"`kp2^anRk0+?na0hc8+;$@%A,`noVk'7AQc]GZ^,;bf6n-!&8rqg(m_@EBLC
%\J3RK8D3"QA/F<GhLpSU;//:%BlS5A=u>6E1DXcZ$cfY1;-f]G%p?&?"=5=j4E9RNT=iV,(.W"p,q`AEVG%;U-AOH*^>gL"=6[o*
%2t`6/%=#?sHI/!Q<(i@$NP94T;u^OBlZ'1EHI/!Q1eiILQ+h'\;ub;q7;$M-!FO3?9lrOXYL9>GpZ`hpHa^;!RND+#$"g`Mh+M*5
%]r?K<9aY=jE1#6LU:6eO]AX8p?1S]\XLU7R$4^*.,r#K^5.rA/H!'nJ*>.&iOgge,R_n]sJP9(-%jOq'2<"`GLhJtJSaWn7crK`q
%:`PVLgF=+'2GbBUKP$-$*sYYO3JL72<UsqNSnOt(4k'se6S^=mW95(IeFWQ[<LK%8DjhI%oqnbTFK0!&eNr(LNL<`3rkO@3=[Uq#
%EK)UDCmlF;:",7PHIF]3raOS>EK)UDCmlF;:",\D2)e6VN\oQKELfktCmlF;:",+;P%E+T!Cm?:D;lI^e?`k8d!rfJU<R\g$[Sa[
%_I,q.IUoIAC^DZpP'!LZ*El,ZP+']u=ra)!s)"A'[p9PE,R)/M_[ZI*_i8uFd76@J1n#n?hDUqWVS?hkcjT^f#Ejjp(f:t?=YK]p
%61<)l@cuRUBP_=S1`S"D7G5tZdj7EEH:8*1o>2ZA"QcC9gj."=(IuQ@+A39I0cXAL:?K?.>b\Q]PJq[dN.T<N-JbHX%(gpO:Dr:C
%UBDZ1,:1u10.cB,\3s7!<kaoYM%hg;>[,\JHV#p9*CVZcKi'8SZe@<F/&`7"4,k%$4!XK)D<76NT=;Ft/f%+Ooh5F0&c-fTd`#[<
%bCT,UCH\/a/"<WBl.C<MkghSY8t"N=\Q$d/>Kth^FgCQY>j$7m;5Z?KpsbLCo[/jsk6hM2=m$NC-$62f=!rGDJk-/%G<o?9;2K)X
%ABYMbDEi&$6hF)#7uNj3B$Td,-J,IW7"76e^'_)VZ6*!r-kZ-`"LPEhp4Y,;/8a-5Tl2tP>243TicWSr;'h#eF>t)C:eOtoFEI>]
%p14TqAFI/MG1BI]!q*@2EUF8!Y.?@.rNOoW4!;pZfHdkBAWp)'.4hKLkNYT63[0Y9gMMdB@8X=V&Ot5/bh=S0mM935Q\M2:U*uCA
%BukDY>:58%BQ>Q5c)GGI:BWqMZ]c@Plk/h\d<2&0Q6I,6n)NjXMmnI>C=1WAH'Zt5M'@`<9[ceiq'VSUlc=sAIe=>=Bj9q=Ep>h6
%".;.la[X;F(P*OQ:fTg:=;60\6jtAFZ,DEBE2/b`@3h"F%2Wc`2:-`WC)-_VcDcM=[7IG4I.teQYr&U&BSo!Jb<>^_>#JF@[T"%+
%&h.-@.)&(a1-El/]$s7.+0N@N?mim8Gd04Cc8O65YEn:9p)*_P*d:#-WT(]I$Dn1V"W)jTUo$if^`))Bcikp'ohFI+E_c4k;u@Cg
%#oZM/$b:(&?8HAO_m.[]i,gj2,VIum*A39X&e:<(>fZL-,:c.31nKB\ZIMcW<C@braPY[e^j^F;qT3O1CA(!D+F$T[R5lp<=JToO
%o\>n9ec`h1J,ioFl9F/[M!&"@MNOEOF-QOCqBW\[7i<Vd9HeJO![\C6JM8Y#&KE_<:0UsFdM)TR2R&-fU0MhYBZ=j]jS>&t3@M0-
%-3J/b5Y4gj0ht?`Megb@B'#n5KJ.0\s5!A_TPI`S>'+ZkG3kFV?5NdUU!l^n<0,XP[dXdc4jg#;Fc[M_L:*,94^"Bq'AY]&fIYaL
%D,PX/h\1Z*9i=uTF0Wl*T%oNU,[ogA9+3^9g?4CM[k5l]/B7ubp/\e%(6K\t5kBtH=s7p(Bd?:\?(Og5LHWt&@on9,Y\S5r8*VVT
%LeWrK<KD<>&dTCqG3R[JpG7,YKFr.C>uCSl(Q^=35gK]h3LHZ"c/]61l-pHZ#_d^2C1nGi$*FQ:7FU%bUm*J)68'As)CsfHp6qe'
%T3c5MQjOKKajjir?7T<!cfplBLlLN<M/(0;Hm1J\ilg=mR`.M@7D)<`iZdks5rI0UW\WN[am'1nJuiXOaPTRl,9kPV)qL_oMg'Gm
%jW3a]1"[k,Z;P&_RpG[#EPB\QM*Wd6B>afa?(;@hB<@kI.Y4G`FJ:DsT%fj\S^a%(Kc^Gek*qO"^'4X7FR_4&a.>tX3/I/0o2@Je
%jpRP1F,[,A^2b88X<A@-doA@+d<_02$0T1`N,ah+Dp@Fhmc5XTP;qiA@>G5?(__N*"G;49UDE6KPaO]Gj64TBqV#,5^WL_dfeg?b
%&J+]r]67)c,[!C.N*P9XC37b]AZf[J+Ua`4U.NoHGO\u_Z)BluEr'VL2S*aN\Q2?^C)/*PRM<Ie"Tu?J?GAbGU%i-nQ60AmQj)EL
%2%9=DL78s\1[*qc*4'[FV55Z7CE]_6T-@UsU="Q2Va_Oj(@2"5Xa(C.gpB%b7M9P_quL<@XYYiC9;6KBq>e40\gQm(gn9<#JC;k2
%I&)i\BsW'SYW3"mc(YV#2FOhO(Ug-,Y?R0HcpY+UMi[rXDTN^r)XSZ7]tu8\JB:@)>_.dSZBVEI!h4q0eKjZ6>:Q+Wft9=g&OBpd
%iT^VYlJ)X:;g>pID8b)l<L=F2M5j:%HjQ;UKeIdf:"'nd:2GMbE?C^FE[]r.:QO$`l1FguZ@fCAYObc2_ut<"&a#s;*T<r<K]j0j
%2auh)L?u!g=jT_CTWV->c7h,o+JV\FCl/AC+Q(?;@0I0Lp`4C`=<Bl6)^tBf43-7r2W,@_SP$*<ClCT.Y(JX8hB^kd'7Edi'PAn-
%J<$-$Bj<r+3>4^j5,3<8;,W&u_s9Q/OC<&rP?6*:O7:epn4q:/cGZM\ed7/.R9\j`QdB$M9`Ul_NaZLd%`,3@POA=PKe7"e#X$LV
%@Fs)k/0gid:'qKumtok!K5km_;Z_ct<JeQ=>6:[N+tt30NRE)g)k:s&o32p,OlhNF-j,R=@5?/gs3qA68LT_Q9$a"V!`s1F=uca=
%nXRV#3#@IIiADS``A>o.b]'q6N@m@39JYocKO%Ptl(gT`]3^B=.:B/=S@sZj(M@MtUeP"=kK_:'?DCFp7OKEnf3+_c2/9#1<E4[c
%N*@Cs>bc44[#o?SflU[Rn>>G9?T%qS05nMF9^g\8<nOoZ]:_)U!h[l&_E7UAV1[Rt^\\IO'`,Z9VsUP>2d`36IJ<+NJ,/I0q\S_X
%#kdiL%klfkks,*jRlAZ"^\IX:J+VA6Neienm@X&p&pNgM8*.40_12Coq"/JiqstsOIWkLaIrU7IiCgra'm(tBfliPioN17hn<;$4
%NXj7ZaA5muj>l,Ie%(?Aii(e[kh[uNIjgCg@PUG6DEprbTKp?,lnIf40MhoK=4A0C'rXXd-[rk%qqGc5T7R>6:c>!2b'joiIdrr>
%TBLt-Ag1A3M:`.b845"RO9;bmEW3$s"O+s+2&#nTM0LlH_`;M?n;O_c(e^M`%Y2BBS^HiKboo%haMS<Q.X;Yf3[3"KO]DR0>Ie?!
%hI`nn%r7nlPZnB`dY:\Y\OUBhG6UTX-$2m5k)m=KEO;*Dg^;7%<\o#&\7YC6':lK6IQhll&!sA8%0)JV_Yh/R6&f:]ZUY*$d4$H2
%&96G:Om_@oC4hj#8X)7$&qD2[$CrmNoJ",Xpn]DLMk1=ojX5sQ5Y>9K/MbP7NK7dKVZ[c?4RB4/0icT(3r6%Y(L@dg]JLG@m@D@P
%Kmp&oNd4!:N*=O@*KLtB1ZU_'+p)\r:3Vg1@VBf3C$jT."2uTk1]$6S%13[L>-+.E;Nki%a`t6XTVG&d8Ia*QF]HIQehW%Z-l(*H
%n@=:Q$SkDcV%?r_[C.OqP@R"uifh"XAWa;OGf4LiL'Y(7!ol)I?XliPj3?0[Ga@R8hRMUF01>'B[&<F<P$k@XmMTJ[f7>46*>?Q)
%_*m(b6512e`'2^:cl_*aTdUR+K_1TGbG$<q9@Y'6n<QgpAMf],&Mnu?c*$U%`,O$68'SX#1+0*]_:03NGAMs_`)s$%8JtNMjJ>aH
%<QR$rS30:JYP/h0]F@noq:b&SQ;=r(!lC.T=NUk,l2JS![=buW;!OYHj<MGr.<'sVCF(oO6:g:+6mC>`Ldie-TqQKm6o/<R*Q@I'
%KcVM8T'<Auo*'%s@%qdqZ]Yr\6DfQP\Q4NX&S_Mlcb9r;&e(0Mc;D2C#8.VIQ=R],0T+EYYa::loK;51iHHXEOGJ-;#.6qR$fjT;
%`\J`n,taWjXV[g,bV.=)kF_l;Ap0/d9^kt)#@umh%&f5+-[<=B-cr5=9;Wj"?d\,]GkX;e^PjOfTl+N5"Z+P;,o5`ukNaqM=p)Sp
%R#X`nKSWe\f>)0FgP)o@,hX?S+H33h;P%cIbVWO\e':*V3[Np`kK2&oQ-`#F<Zh3I\)u(OjeV=O4d1EiPjF*/]ra#UKP@Y>R#,'l
%U<<]*=>o5T,W_beH918p9QVS8\jQ*GERa?-f'Ld>+R>ZGCPf4JHT?Fi9Ni*SF3^tKjMKA]S;rE2&2K:f'4Gj<&7a9[9b3SjFQ1/*
%bZ,bY]FZu,(ip=7+JIP@(R%Nbj<.X#dkuSMPtiJ)6t[2m&OGWB8r05rOj"Y11lNm4>F('?E(1aWN[1(<$!jU+[5u^P\[CXS?L_nq
%l^E^8dIm`D;C`DHNFlYZJ0H+2nb69U@He1H#[^Y]6YA^3%TAf0+q?o/?EVY@8<Pqt`j54+-)hi:(qGCB2h@TD67>&@%t*TLQ8]KM
%<9GsW-"&8QT[*8bkiemUHF0].hXu/[H+=Qk`6'$:>P!,Xe.qco]&>m+B'rAhb.qgHT`mOmXeRCd=n0_C8n2H`\N4%9]mF>MPZ=--
%Q0";O<OCOo:A5e3R8;NbJ7NO+Q"9:c7C_gZD!j,?&stCD'f)qa87SuQ[OI4]Y]m&O[Uuho7h:gIMY;#>ia>3/1DUWG+M4ZD,!@j'
%GfC9;7-#n=g-ERV>L2I7fsQJJaaGO)n$;[AlTsV]_!mPNYH];9R;:5,Di[e(+8(LL3`eq?Se0:%2s--_ka*f,R)u]^Jr8D=:UO(%
%Hm7!e3lXlcW2GMOX^5f$rL=6c_o[8!-'_!*ql'cdQI<Ylk=(dt1MI3,;^-o-F0upi$1uO,7d^@^N1#L*8c;@4cnn/2\R,%s)o[VF
%$LV&ZHO>V.gS:]jJOIXMb<lrN"4;2L+#8MRYYT*>(@.ur<$J(1PV"65!EYJJ.?'mTSln^(*1\QIQN$Jlb.tJ<,)(oGp/Gi$Z5Tc8
%+S]oS1@+<++^u(h"MsuLN1.f*1H^t863QIdnh&;NFU<d\^<Trqo$ngXFjatGE5+*jP)6t4]<3ld9i6d'XuMDuRgfnI53jDXX]*9g
%DgIFS1b.*ko6":7`)N+k71Kmm$PE3lG&%(@8+/#]FaGe6'ZmYWHtl3qXCCcJPEgZ"V%k!eAGB%U_Ci/G>7,Oa@VdUQ=$FQVaJUcD
%3]8fM(nCoCWfr:m2?O,016KF@GRAbgC5AT5M:DW`;,oEXp`Us9r]pG<fs"eLKnD4K,Yns`@s,X*Ps&W[V4*&fL(pPS$("'#"^_[(
%6pcY_YmCfVqc!'5g/ndu-r+l975D-h!ZQ.a>mK&ZL2M>=m8J<l1;&[^:YCq&R:/;_4\6N6LtH1A*X%LWk8oH/Nd6,c'@&#')d3P!
%8nNX[N"o7'Lp+DX-!;/P/r:)q>`(/uK:3sC%k'QA\MYb9[4kRA:T\I!Nq,m6nW[;r2%]P7kq):3@NX8OBeUH^DrbuC/rm'qJSJ(#
%+tlU='iBjf=HRNE@<rKq5B7Mo?;nQ%BTd"pM)D=6*9>+]=_kS4_nK2XCN=f>qCk<gbVr&_CLT2ef1))O,RI:BLfGOWTc>sdqG>2E
%el%a8CD.;^eS>W2Nil+"iaF`c;RNflZ,;@QG8RM2)$rk?lGZK'U0.,p=3D>8Kr'Urbh"GN!82r88Z"@J&Q;FY\TO)d9fSnI/^dU,
%^>^_n9tDYaO@MODmX>md4D0Z/I>M+.'LOkuEI8bp=4pW4$pua2.rgr:GTk5T6t4cafZLKd(\rl)ca%1Tg;dM6P<W,SZX1\F$9X_/
%WqJKB!>IuR*`W3\kN0MLSA80];/%I!G3b^,D`(-qg881'ofJO_4-XB`7aZfCJT6Km81\11G"\%j+Zaa%+C,BaaDpn:/<!)\6R+E2
%')%ZKP8IESGKr(PE>'.8a[I7G`MF+35s<cGQX07Q<9m_Nlb*#^'WY9oPL0ld1u>CYQH.2aG*k*HjfYqhc6c`#)6n`&1GT.f`/oDu
%C:ib11sOU,@<EciP7WWmIB%$-^7^(7WD7B*Jln8e&,aRZ5XVCdF<nuTcc"`h?5,g>M@q-\K;SL>BcDmdINnVLT6$ukZb#Ij<h`bS
%3NEW34^uc(9>hD2$L:Z3YkP5V;!dlkEXGd[Ee-^ha3N6cKiQM?(%5,,87E^5#,eLsNlrtR[2P8(gN5GD'hHH<cd.?=U?ad'.2C;,
%b!pb>0l-LC_-#&b1h.fmMd^oc7S.gHbJPq^0iY8`kk/%F=%i3cK33Db2(ZAT$tNV06WTs[USrAHc#BkO%LY\tX4WEniq8fc^1K)l
%Pa`rPRI^,]]?FjA)U?@p*(9kjL`Cl29jah28)`IX`8&4r8t!rY.Ci?>*]@hmY#./;bEB25*i3l)Z%k5=*`\=nHhQ.s4/c+(`]$S.
%N'Ba,':i)D->)3EQmYjgT)sW>_4JNBTU>:T]#qrgU]q/);PKKb6;3T99].q.,O.mW9h3%L1M<e`E:11)%T2R-HsIn>9?tV;'q"*p
%WL;<fi*<?S,*6uuH(8`bbjr<'@]m;X1qab7&iSf_=)^9%//6^5_al1(,*EOVSDBnWkZ,YYm8[JEPQ$Cj/]YOgA2,QF<WckiZ0,%3
%HX:lq7:a+^CUdb?A]_h[Kc**td$"a$Lu4QZYKW";X?gJC:R89.QR>ADEqKoZ[UHr1b]N3tW[_[<mVVK)]GW%B9R1@cB8F3D.%R]%
%VNo[>N&+&UlZ.f$<8_L&D2.Ldj2Pi>EN&0UE*ZJ61ep<[BBj"<Ha2b,PK`7kdp;8p#U3V`m]8N20F4AkPDhSeCMe(tB2s;?2'k,0
%GsArqL"g!]S%5"A?7%+#REhl\GdR7&d!u-P4kon%qF6TC9tEe!Z4O(cY)0QEXsreM!@CY`9a"$(X(PRsfX-YlW>E1Zq`H0>i]`75
%7G,OoV2M<89D%%=V%ZCC_3cpN.B&sKZDG85ZKAL0W6kS!fS%%jO=44?VE8pt"'8k@48O-M(E])%@Ru+)YZtO/2e7j=L58!onL'ei
%R.TaX\sUp_@#UL))mS.^a$i3\VZcTbG:`&V*X;<!.APSICju1doJB.%UC_]E'O&\_&MMKjHJYo(Lg29+58"\Y'BN,]]pc(NMDc?+
%n^,6>YDk/pYVMriR*N8jdWoaEOo5PFP.[TI-m"p7ntPKYVui)s`IK-<j-)`;V@0OlXlPM*ct.2ZdV2<!o30R'3U>9q:jdrb`?&oS
%V!p)hERfVcFqdph)2S1DkfhqHSo#7;=.^I8F+l"P?O7r?gUo>;8=J7oM6)FV0pI'.*d@Kn1+&m'$rMIBBCBW2>?+63,\Gs)IcX0i
%@c8mVc.=U+=LZh+:K]K=Qmso$D'-be$QK3l+;q*>(o6b;MKFuH0OpJ'1?E%p(*X-F\=nM2J_QW19^iDK>fDWcLZ8s7a\<348L'ci
%d7$GqU@Bun8c9Kh.N[/8c0&db`5#0,e&5ol4rS"[dKq6gYHQ8TqNr\6e',?ZCa(8^4[70R4jqu<[&eE#Kn0Q)G]SFmd=&R/Q*,?7
%TaT9FW'k(!8BbKdSuma_CnS%3l04HKN=?SRKa>!.nr5?Z4/G#i*9-4H"PC])1=@B4Ll%92CKE9$@tZBdK*b,U&h2[WY6Qs`GUZs?
%_X"Gof1)'W.Z?#jida0mG(LkL1eJpSn`[&33'LO]DC@ohL#LnOUMnV80ob6c&c2g4[]bt!#15CQM^UYu;nE3JMa!T,l8>0kB</j*
%Cp@_OF/c4@5Rs9#+6N!n._"iFEeOn9C/9SQ?5Pt^7"OZdae\RV4[R?]_^I!K[;(7(_pA;ib1,Fq&8J#7\?=FQcYAM4lsLs1k]fta
%.Z#QI!K$"W3GT)GWCpM6/(^GcO6iFgYj0,Og(jcbRaH(bgVOh=RRZH]OD:*aZET99UPGjs;:r&?MC-j_>m;8[VmK&*an\,8:lo$]
%DTDh3.f>#YEN1,c3FOM:O.ikl^67:<,[GB#-,a=[LfWKSRI*@B6gb:WiX=hX?Ym8?bS#X/EZZuD&3i18d3;X>0Rp/*TT+[hc^[`t
%C(P[]s(CfFHK7c&1Z<Z]q@@^Y_#>J)YqPMqM6[R$8djE\1`-W>e\u9G=]MOE2Q9aSf7.e+k<ri4c'M>WR9<./X=5/Z`Z+6#ZT]b]
%_ND.>h<Bl$30X:i!J-ok<No+#[$1_%#R.$D2*n<d#[Sgr)Im.cW-kX\#UO0iDV7\dnJ3RaGAc23r51(]'cU@19$A48>!tC`)DD5'
%'HXOMc-?$(/e"WYT!nB('gt/@LQ26T?OePL0(JY2;HAbZ*p95I\9eD1gX20_LE/`uf^3ACro>bKmg39j/W)3_X5=hO3/-&F3@G`O
%/Ao3r`>o7P?3>AuiAkIqLS?ariAr9DBd=6l(FDl\&iSd*4g/dK&qbVt+t;;B3nqD8[hh$'N6n8\$Sds&`8.#_=OX('M2[4;]sj18
%.n%a/2^W"&>a&*CP#t"<ZU/V8=C0RePIU=t1P@bL4\m8sP,<Ugkq,6K'eQ#6^"sEZWrRe\0f]oY81pk)62eT(_ulX\1],61Fr(ZT
%6&Qks6PmA$>Jhd":\3eo+8gS%C_;grCj-Bp0_HG\@n75<<q'-']j#TG=QP>UZ?arUAc)cOLDo-;'DA`\ed(O!hUgmTW:!;Z@l?Pi
%[nkAsNNT.YT]j-W,jV!4`UCT12;<deFSCW`:&7du-O2o*\kG->]r6h;3MOhs60F\:6Z<sm:UT%Xe<e'U:b+IWo(F@(,e7!)`3k-<
%UI3Zo:HMWl*iBL+B2m=kTgWQR]Wr_-[\.8dD8hk*_$?>A2)`7P0tT/7ir.'%gDJ1Kji"uU$Ta&o$GD8^d4GraLiP_J-rHD#'Jd13
%R'*+n%`-'2K,c;;V-/K84bs^$_FMUX)bpRC2-RnSaE.f9';FQ%$UlgfLa]8V'@g=K7=of=T0p,2@P&q$LLh`X3s2#r^GFH=H5eY?
%Z1=Qg8D/VUEnrPuB`1?]hWtra0kemccJ]bK'M_!e;MXRiLq`Fn;jX^+l_3&$.pArM=p*T63%)oK=^-&c]GXIHQI[]H'cRuY^#Ga.
%Q9Re%nl8@:']9=HSbr))fNS#\dN<A5dP]b+_,JIoKUF*HfemoD]/?4]Etj."&@Ni&KuOi'QN=#ChI1`<S?mPIAY-6bea5"I<gfo'
%<@0,@i<u1Yej2,AKuiO=oFgnT!@,C1COeWl<gE&L\00N"H$QCQK4HAB?!!&B*m#fK8OUKH$:8@71s&K/]'LTf2=hCp=0r3pG9;iR
%7YPM<[nY[6/2ScE_Lre+6sL48>"XEk7)aH*WoTHKMgKPe]`e"Fq<7mZ(:3.9`_Tkq>aA3A#9u-];k)lLFbt=P21!:peehO6+sj!/
%8=#&cG>\T"KGQ@+-G;#]Z7o,j[k::_&i5=``&N,5?#Mia*'47T<T`6kerpU:JuUckH-h2cQpp#oR!#C)8HX6eR%M3jfsNeo&CoL_
%c*ZtJ#d9QEpDp>mX[0DlE5ad^M%S.GTH($knL@A/)R2ra1U*s0Pq!58:M:/@M:;$&9LMjoQCP6%&GG4=_<L.4nDS>X+.:*<@&<XS
%N6XTC1T'3#.Je'=Yr<6[LPD]rHI*aW4)$cDGU2LG8qaaVerlZS/Gd8RDk9,kXlJ6=8;XsF:b]ch`%=8<(MBnQ-9)k=VDk<]5qd9\
%O!mE01.3t(Om/#=7!*^d][WA"e`,1NLdeV+LtGq#4lchCA8)IES't"$g+@KFp188InmRA[$\j2!)b6K*)hOm[&2Am8LG&E50rC6K
%dT;XaJ@nQP&he\fc4BJ/eW#aM(qMOu`GZ-pGpN)OgHo7KmjOODFi^dT0Trbu*RDi`1\SfE%LZP4VY`>l!?s#m!'Lj)%3rG&nbLjO
%E3)tI7LsVb/0X&iS>G6P6bKgnDuh,^7SdB].?$.:R+F;_A<:iZaJoIX&XQH?fNT8jT08:Ul>Qnn64Z*@*=@UK6Y)UK-d7$G.cbFc
%D6dP1_E#chH9f>Yn.tE-<@];kf)>QOFYCmt&@l\O[%)#mOR/M7$",-CBH<EcKn@D$;Y4*DLd`ud#kNV9q1;V(:!(+`oFMgPpGrXd
%pL$c7p#*J'l']$?`QKmPjU/#=JjR@oc5:?;U<!mimUu<%(I*bJ</jPH0\``2Ec-3X-k7h=BGNVW`H!9&!J,iNM4RH-*,=sXF6f1q
%dU@_O,Kdr.1L7OCO_b<=#[6Rj'4P?(K00m>-$b3`:WY%C)H%JcN<5t%#J_j-YGCH>#XN*'Z;%+',]GF'>s!KH#]#!u!Q$Y`/srN&
%>FZa.Wn8B\8@sis?Ko_T(PD(UM?95_Uc#Ym@$J'=M&p\,1pn6TRAaBEMap^&H:1;PCs*ZW8F@l@9BM=(Y`jWPOBYD1q3ULqJ]CI]
%%;f)iBW88VmPk3=&@)"^)]q\D4`I]\>X@F[RG3$nS?e)^GoL5sed(AP7nD(5g=SO3`72nVYZOuRSjUn:kK<5D1QK):#I(F!Wpd",
%j3SA0ZFDcq2H'5XPX#+IIHf-7k\EIiC=6^$'1/FIR&4an#']"0\f))G/R;5QP=n5H&+F`9RAe]T2O:6:ag;HgKnK$*K4a@U:-rC7
%NNV5d:<V5t@^h^n-Rb6HKQk`o5"VKp6qb2IK,@Oe\3f2E+%2J"8T(b7-2O./:eVWnT9I>8HFgdB:RC!9-!DF#jBCh0?B#msK2&<7
%b$,^&nl!4r>.TBnQajDU]oB\;jIS"9)0r99LocnF1ZF+5K:Es(*C7-X'ecFc+i=3@,^iBK+IoWXhd0">Y=uDs$=9`uW9Y8=Ms9%1
%?A7L:MM-!cas:\aF,+U=EuPC0-/o)<BHJD[Pl'nE?mVt+3R(Cc[-kM/1Ppq;4H^XGH3bF)8A`sU4`k9KV:jqFl;sC:]*+YN/V(&3
%&'#(W)<WkO(AhS;,G]s(*Ae6)F-]&]+or->Y#LU5,eLF?.#9h-1Li[=>N+]a"-19XNC;ol^.At"`C,O6D'ib5"p2lV6aYp=X.A!4
%`4ReVL%l?C#%R;I$.0S)M?NP_[CkXjjdAs(f:[,@9!1T9)ICG-Kn\V/=$$Wu_n<qLfT.1H35L?rM,aZhO_)`[U';4aC1&`_Kj9`P
%Oh*uBB+"hBhc->)DftZ8.p^A)FC:@icn(\c]*7Yr&D?(ehZ<ctdrRr#$\\%TTs7H*RTPa?<i3T'ZIEqHg-a<(5@4&Z5r2+209i:W
%80nDK<(gGUM2(l*m9U<[3g(DQ"AV#`f\)G@:[&?%VFk\+Q9r,6&uF6>oi$s,@+(kEEnSef=HH2dcG#$(SiEoGb(+(3;[_[1#Sn<+
%bZiAM,U(*6=%0e9`8rO^6F'CVNW>dSk?CBGYC\,V##1)ghFfIRh#r@iZUG-jCE`t_8&:*>=kq.8<?eM-BZFpa56i]:<J@*L'EKO`
%!Z?-^Jj<3R=7pfBWo%Kk+jrqfOlrBal_HQq=beUD;N(F;Qs5<j.LVl0$:`Ps8!75EWmDB/:?(`>L*L:Bn*JeQ=:8+832.U3&'dEq
%#"\o6CUUQB(a=]GQb#@<)'!c#gds%L-JO\TA/)$eRMF5J!`#<9;UAbJ=<.7#oi.s36%`,.rijm\4]f$k2MmF?cY5p&SLGjF%9feV
%?eFgI-YI6]IJ#Va"/hqeH8r)p#4C49&3k[\=hmTaNg'b(-^2T78sr#i)HL*E3L7A%Te7:.%f4rk.@hSL!<ljkUgbOn+cLVRmYoZD
%[:d=K'aZALJnrR`a%+8Sae4ETLr&):*Yk'iGU5*Vej;5R(<'r9'!aDWOCsWZ&G6/qF4eg?SeN>-1K`VK)%i;A5'K2e_k]3te"B%l
%#bJ&R/21X1m_IMDddhZ=k7""RU2+?]YId?>aW[dV^8=5d8[rkNAeJGrlTGHR3?k^O(564=,iXi?lJ,.fnpuM_S^Vi:W92%><G6O"
%1aOKs*<KU_muMWgUpbquX9j-V)"T6nX$XM"m9O.%XbU!i!EfKR4#b(:jaE+knL-BX-n[a9r,WA=(X<aO,\LFNNraoKE18X&P8]AD
%E8iXY53i4FK52G_6oN'TZWn406"BnPfOeHoV@9]KCrrh700l"CXg&oI*_"I^@N15[dZ>1!l3/^8<J+F3VoO_rLq:nN?GIlm74p'G
%UEF%:J27Tikp/`K\0]B00`t#A](*"VdDn:c$:MuQ9j0"^_^uniK$\nD1!s5#HP'-QKjq.Ie/4[tCdOS(_Z=\PC0U@G&5eE#?BA0O
%29ZYWW?EfS%%*;&8E&!!(ag2X1@UofT0Mc^c`>O8OaaI"ZmlL@18HD1me6P-nTt`n3<q\d1aS%^pMF3S>Db4%(GR$%+;40GMWL`e
%.?XhZj,nr0G"Ek:/^4qqLE9P#5Phb&5@j*`1pLF8*[7Vk72Al<_03!CBUg#:#QpU3cqInB/1Cc?&VEipGN5)WJ_Pj\R#;B3JITUg
%P+gKMNTsaO*O6#RK_Lg<J;Ag#T&E%IHa=hrnT=Y;Z0$72B5BTV:e^#*R(;3u)OE;8,c(_;Z:4C>o1DXDYaQfe-jreLp##ngAs(2(
%\`1UH$kXce-ijE+f)bB(ZU@0J;kbRK.WJHd+[T5PQ?F*sM5$du2G7NPE;IqK-%^l-#\M9/@-e"UL]_1/S\Onf]8[KliVqH"BQ*Lt
%W?98*N]?OA^^l,1q'9s\0A'bL&rq#iG;.M=3"`d3`^tm8\[Ll$_kpL&/S8(X_\?MP-dR4[<cWnJ([2MW7W\/K"\GRsR*J/q2:rqh
%:+*(1rr>B\$6<9f.&CYS%c*TC,XCAtc=?3^,;k'=X4C)"9SaZsOt?FJ3^A(1S;Gt=hPk&+K0H,q9d1_5'_($1HCc\uKZH+dF=Y.G
%;DMGkH&3:2Q:fj`E2o?oStZ8a;0=pJ`hTK#0-nk@r*G!m@pcN+^%eN3Qr/k2'Fgc^74[OuM6i>u`L2?r#n<Dnf9X2a^c$s*Ium.d
%RLY>ZUM"Y-f8j)n:sZO:QthC<=B"U]`6d0<<mqc3[3ai$-QiY:.:J[PD4rN[kaE'n3rS?*oTFGhj^NPtYt5RHV`.a5/(6m2Cms.N
%P*q@;h,d%tF:WKu>F>KR0(.r'k7efZj<GGa"$VcpECU+B4RjU%CJl!0C($+6Dmq*N$-W+2e%J<S8::t/1caQB[`9PYd3)/m*a+mg
%AePXg@_<ICbQ3@9d[LI6/>ETr!nE-K"PG#^9OL"O=C<#YZ_</S)$c:XD&mCWF>KQ<q5P^17qT-W"g5%HKF)Br#E"2[#/A\"%m0bJ
%c,FK#/&M0dd9*a6l"b7A3\u?SK#AK16!>DD]]nMkP*]?enBfJBVT'3nQjAJD%8pI\$M'$K/Kp*pF`tAS;LH?Ci?e0tp)!W,\X@n<
%<Y8`uX=1=p03L6R(FJ&)B$gnt'4NnQNSm,6(($a;.p//@4aH&O:5Wt,G8%"M:.5EmWL!/A[;q-Y3jJ[J*j+&8)sU%)^nN]&:.9In
%POC#2+&``q6m#?mMo%U[!NqlU7(W['<g_WL+UXHpY(hWYVFCH3?8`)W*?o5HIkWN3jk+?DA.U?HH;Kq^/Z%`KPbkSS8!"9M+Taqn
%Y!MiQ]oV3?1j^Gjg6!%n)`%/oZK/7$/%>\O&I$gb`h>nN623&>EDD)h:$oau?>7Y!q1s@$R464E:F=ZWajU`_WDR1a/\q$Za#q+/
%L("L6>$.nX6oRR8-O,"%U'D(,8nQLNQa_r?4W\hLc9Z@]k>)Do251`n&0R_AL8,kt-=S8MkLjoDZ7Zu\:B:,9QE&Y&&PWNM_t.r)
%S;5PSR`ASuU!.?::msB6a]R1r/-t`P)@O>+&tU7K(?g]gBL+G@T$MngYSXYRkQQri+i.H4I5G$k@?n/4r0TkW0O-BYR3s[n!hL'0
%1#d5pmNFV1E\hOOEF-+^mXdn*#SONm)r]%4m_,R4qI305aY1-89?I5H28k5dKir+.[t@3"3`]T74nX]Om9cTn65$5C+_D#\@3F`Y
%Z+Q<^P/[YIb6_>2JCc!>!5fGs6J%V8Tqi$iL![D[9F>nKZ/`Z*_@HL^O[p-Hg]7kVL0Ltr1WIE,(T`C$<[krWE/<CRbQq0]28\o%
%VhRam<#^cjr1$nJ>B-ks=hs!0g`83e%@_?C"FU%#o\r`YAnC\h>^fZ.gI#P's40%@On2J.K'?4]b9k&%^!iB^)FUC`9&(8e@L@*@
%GY9ET9S9Be,7,G:WrPefc"Q+FTgH>B]opg$s64.282?a.lSO5;qM&;u-B9N\?(;KtJ?C@acQ]>%Xj/3$]VM\rmtq25Vc2e+l=`Qt
%Ns\\;nubfT:BJZ@oFb?\WGH7PQ'?dM2=Ms*:`]$<PR[`:/P:u(!BEuWT'-?Qb?6Z+8e\1u_q:1b,XJhd1SF-gmkbQB+_LB3Q_Sm<
%^qo4gDT.H^j;&.5>BL%76l)JE&U-$7,T-9;FPfflFETB=]!UeCX?tU3q$ar@^,VOCidVP7D/"*Qe](,1>`5ghmVrdr\R_Q!"BiXX
%DE9J<8?/V+U#TNr[?>gsd$u]ZdM<Dhd=f`k0^&YIpjsO=/jV'?UZ$O9Jf(;Z,8#iu@:8(-*'o?A69(+]Q-pMDVrgrW6VUBOEV3pB
%"mAo[':'2-WZYT@S\kt(25-<0L%U_,Eo.$='do7PYsE/^.L&bR&cg&uXdQMOhXP,A'6XRVb<+EDACm]KP<>7J&eM$Qb+9/AK?4Mf
%pC"u9J_h&n#AF^b'!"tOe/e6le<CD?Ii2Z=3G8Xt@U:Lk&pcrJF3Anhmi3KD`\<T:I0Z9>&n_dg-`?21=\hG)bHsZYo+qS(R^T@i
%m(/rO3AUjcOa<N2I(&qHcFN'!(4kHgik2(UCdLOLiFL3h/G;hJoS'q/NuI1+`:1$lYf"Boa,]A\\c[YHYc'&"5S_SlGtVsF./G3"
%W**!SZ?,AFW5`lNr(VZ3dXnlI+?G1N=W*7`MRF#:)SIJg71R;J'eI[XM]:`=bAl.4#s(ioWfpIP(c2EP,j+%]15$M30sop%$;Ti7
%!/*o,-t=#%qpn3'CZnio))*=5#5;qFl(Ioen6?V?/2"BMJ6efH"hi_aFPm7ZqpiYSp@Ad050_qu4*SVX&lb[gV2rXA'(X,=8b'nh
%$6B?T;L4*O"3>A=9bcN*:eMK7iFua@o!p[@<IuF2D6o+9`eUXJ,&)SY!Hu_L`Fuib_Sr^O7""8Tgp]rP!uL=c2II+\cP#20j&3kk
%+_&q`@20Cs=`s-&^ojtq^\AB!c0/)]!u/9:`dTc^7ZW$t5?KB^DS)mAPR1NtitW#HLE0"%9ai*jS%Pm`H;)N_4gEgfJ9,AI'VR,_
%..bZ.e_"^n$-]mkWl+6NlX0ocYUr;3In(oDjZRhKBu)H<4Tp1c$7b>"Q\4jPiF`jO%h[KjP]f489J=stOk$HLS?a&'M)O[61=Vlo
%^02DN/GUmO\A<h^hYj*8(e#'a@Vl,:.<"3Rk>B33g0Y/R(UbL]&EasF4UE$pLIjlL*Miqml"P6m$=kYH2"oRKKP&"6l^VAjAk@]J
%Y@p2JL/Y1JDA>YgHf-WW3I&am`F\5+ZmTj:T49Si/t7t:lpr9.%TjIY(;O-YJ>oK]GK'u**dtAS97U:/oi9o4WX80,<:m5:0tb:.
%HAb`7Q9cgaLhCl6O2+Fo>5X)_*W[HV4F_ZJ$qu?!20P_da]8/P!$:+_:C%^[@Q%/o5pJK$/Z;5+l;r;JE0<XiWbD&QWA])j#J@h*
%6!chUpf5q>dGAIP==L?6*:4K<"HnKNa-2&:4R7/g\HK"e6oE%4@;6Fi/]I??1*EP-P6,6_-I%H\7F%&b9aJlcN(cLsk]tj/?sIh)
%d#m;thakPTTH4B?H*2kmAu<_7N:OYcD%1=iDF[\r79DJ;]2%Z7O"S;>_^JHqMjY-pbJ]'PTjX;8F,!OcX7Ym"j/qdtJ0d!*[?aF[
%4X6A_INR@N*Jdcg;DM<KYr=*5crG2K*E^Wc\Wulk"1GIlr[(,KS<`E]b*!4U@#ZkGm2/cV?V\7'7+BFnerg0<X!`G`dbt[Z3QQOO
%6[4OG+<&J`%2:uu+qJWhbp#JJ=,g;3`H0B,U(K9&R.*/p^lDklH_K@=E:qI\#GQ":')U11_1Qi7Ap",,n0"fQ6sF#2:QO3Vc9WO*
%)%JChN43!@#mk<.i*o57WQ."W*;;:^!fuCC\cX[[L[*(JEjoV$<h*!'Q]+,ebc-LL#JhIJ.JMQZf(f`6$I<k!0OD(n;8]qc@A6Zn
%BVB`l:4g[?.o?sd(X*DWV6#FfD0[$\(lJV<[+GBsd_qr+$c$/C'g7cfBN%%N#_(hD_'!MfD1rt:<G_@u,p*,6',n;\4]HgUFg[B4
%`m`+?f+%4qFBKSm^(`#d=2mYS3,9N.B]5mme4(I_Su.J=4^NgLADLo2eUgs+IA?n2M81t,EX*FhpOF!h2[dBT%`E,!O>k]5/NG/*
%\")t\]LLag=:6e@CZ,ho1c*YG%LmDM)fpnDofShC!nOG#d$`cHM(MV]=riYN=OPX(RsQVEP@RIJ:pIF4344Z0#Nk>a^Q0DLGg:sR
%,!lC\I"%WSCsirgc6IDg-Z&$&^&`U22(/g&L,T9kQ5@-YL_MP-05!%g6:`r@$lPeT+r%H!n3@HleXf@D_]74%kZTjVOU-^CUH)E"
%V3lIpk_Onr+9Wl@!>\ohjDd.mD-Lu[\g"+1$JpM/)M2e+<kge=ie2JO;7IiR=XYK6"@o@jaPou5pY]MDeq:HY+E_eSdPu^uI^0jL
%"htVg^hALl"YkfpKgBlUUt:QRC=rpu`^jIf6ShTXKGci)nF-H-KnpKL2iB2+%t\\`Z9ajB4pJ;%,8>S&l%pGp)XC<p-_p_\4=Sbe
%&KARfg!jm[3!9,t^/n9A@D0j?R`_2l$N<Hc%VKK4I,e6:LH`PF_8eAW`6SF7$h2?,M72N-Gt:K..]+3=2B8HfV0iqDR9QKLFB+=T
%BQ\`](qIqOhP\]$Q:V<WY_Z(!Jhq"<a!5&<\<7h(JUJcfgq)mm>?N2@T5,;o;Um"^`V^rn:&OHBL'Ynj2(\VK+q^![MiEb2]bA4l
%(8r"1=8f#$\."ZthSjQ_nZX<Z72gR[Pr_E`TN`$ZNH07UB,WMj$o-ST`OQ'OeR8'gY_9'F;&(F?QIu!3U;o)'\@n;84Z\tM9b#)f
%,,/'o3(&%KMn_fr`'d;]b>JSs%tPJHBie8*Pe[VXGG0h.T9m@q>o=p'Ahi7n'eG7JCf'Q6(*i[/d"g8>Sb'9"=q63%dS;JG7F9m]
%m+ERB["LbX/+]HC,%=j*/R(!NellU>4&Da//Fa`I7)hk84/=E02d\X^'t02ph^jr)Gn)le(5k_XPA\J!m)M"F,`aG*8n::]JSd-B
%0(^Fc\6gAOeOAU=3B9no_sWjM"4*!EY]4tQba-tK$6]HH(s6U\k4qhLQ$"c*kLhMj,pt_bHoZpJ0V"UgbJl*j0No[U3\:h:VX$\t
%'SRiBoFSea7>)V+=D\i6<Gga:&@qosf4mf%Rtt"$A_UppVtNq\ekUap+#LqIj*++%K:4C@/[(Jhb3>3gj_*U.//T4(TW/ua!2IQH
%[<:%Kf3fB]+iSti.!DVZ/0?c'rWJrk!B3T49NU8+a0!b!A)'<i9PK>e,^O@4,I7,BL_CHWWCaQsXo`.;4GE5B9_coKAZTm^F*P2E
%CX+uDV'#fd9t\jL-)A_0,Dk&Xj%I=O/9ehRLq'Ff<:[BLpX@n%bP>QCceO80isu55Fka^ARbf)hmV"U5-MfG7#&FaKVL;._KEui]
%X4D*PlV!9.fg14t>[bWs%M?mlln-/23fe<gR-Z"tBe08fPG#G51ogsq3[5TCEQq$SAY+>\HF2a$2V_&R7j\q^CX6u9OucXEfaMTq
%^cr8i$JVU\T+nqL,'-T@i#nBgS%X,@]1jXO<QYVBO?o`mU(Rl%dYk4iJ71p?Z)`;FL`UL4XId^mhrIQB!TD$5.Q#ahes=I3MBMWH
%#b&kgR^U$GZR*UKTusK.@H5RJlbMbB=[JtW>'2JZGc!E"Os5#(Z3Z?e`OXpDU8nNr1[UW,keZ#3f1kY6:5i7g/N,j^%2Pm-#9dRA
%G`5`!5t2#U3ieBDYR%40eR7jH1ETm'P^t>jQ<JU=i")PLgKSDjF[H]n'.LXo6C7qpR?Ujc*@Rs4a5$r51]Ys1B!Fgo'"sNWT>NY1
%HBqL(J<uPJ$VdHI-83cX7=2Jf:!I$%CJA`Ua;?Vm?Zm"LQ%ZHfT?>tX<90WFH\QoDHWopO"ng-ek:FbkZ*Dj0?I/^JlHC!)'I&+[
%dR0:5dlp4mK\SCY3MX/m[3W(Sl/k32$r#l+9;,u/,"a%VnHLKkQE9Mk9GSa3K!:/`9BbQ?\-=9G3"-Ap(3:o><%5?O*.2`9H5!CA
%<)=:#Lts;V*]K4-W8Q;aLm4s>=F)jS;'JW?'BYo%-%EL\23fITM!,0Gb\rWHAR11E==oSCdWSoTnC0`mY,qc+&2=u/OoY-ad,>%d
%!Fo>K&(tbJea.QdZbaEgRRaW@LK/bU%P)oqF;GT(5S'so$)?kZV-E6dE`eToALfM:!P0/[/e'c@0E@+@WX$t&">e?\ZG?4W.Tu9G
%KiV`qDI'MtkskCN<H,Soiik`#H</7p.%"sAR=O3]nt)Zr#bZKUJOHA>[p48ML_Cn5d01Sj5;B(5YBh!lZV:mlDNf#A4]Be7X,'?f
%^Ao!r^%9bumCM5I.+&'r]V;c+V44G9$U&ZV@oEBe,\J\:Z"K`=_-BI55Qd[g0?]7SeY7tKm+gFuKeIPRQGUsW!JVB^k3>fVLI%cf
%[iVA'.>HYp.E(neJs)IU8RH$t4nU4o9gfFS@!EYA?,LblZGgQPgr'"YkZJfU6'@%+DA[gsCg/b6eC0<<b*%S3"V9V#&Yb_L$-lG0
%0C&WK!3=RV6)%FG$$J6rUgR2*_A##)6@9BpWaD*p!0C+MlhPra(rf#,;"lG,J!K*r7O;b8eI6,m@K[nZ<Kf"d+Y-:sOPV/nE>Z%0
%j89`LIeS*1<5>2eR&UhI-hOIhgZ&qnGkm:O.2+3I4jTdY.F1!WRtJSH:h(1_pTrcc#qXqCceS)W:#L;OZds?$@EC@^Med5k&=69&
%oG"^c*us3:m3^*N,o3=GA@uG-/("IEDaKF7b`Ction@6:"a=;GUoZ1A)jgDIkW!8_9DVTmLbHXl@o0u6msTIL]#[s3-tpWOZGL/C
%Bed1[<$kR#(J&S$JP0u&0;Qrn`8.$$F^4[A!")J<.ts*'E6*(>LqOfAY3B=E<Xb5!WCsh,FGPWA!KTKFKZPo&AU/0J.>nATd"R<W
%5eC*MBg005(OL/LPla[8Wkg*^3CsPajm&?cq*.[d:>GtV>HQg0`9KeuO4P&nn?"YZ@U-n_($2Ob=$rSN;S/e;`0=ZB`g(D'PSc)^
%X$&pc"o+H#SK_2JFl6m(j\cJQLKcJ7SDS#!:'r.u7%IGb<"o7u."U%QO*S5BYakc^-tDpBoJ;#N)p<M!;pB$r$^9M..Nd').I@Df
%*<D$XRZWj084#I+k))d4Vui><mZDk#*O9^LB%"k2G2'.7LG&&^6/N$FbVP_h@!8i>2n[+30TOghe5J"i-^lb9>2-BAA!TM)eosPY
%N[ZBPiC"AKS=%IS5tAs&ZhMPmFD-!PU*N@V*qXJC?o7Bb,_)<L4dsf',?lj8`f"EU6/(HVi%`QUIo\OFX$iU_"Z0b@QkT?hnlQ1f
%J^fO-T%E1lC%!;E_ldol=:1Oc+YpiIQh-B$_/t*2Z9V9V6)`MjDGc\PBY`WV.pCt24aga1A.$iJSE9``,$KW>_W[9qU:ct->Q`Do
%[,Aij:r/T]KSOh+l!p/`mg@GgR$H#=@8Jg/;jON8s$5=a'i@JWm!d):113>g5c@dK1h"QXiPm?rk%R1U<Z3g0SG#X96cjUFVrtK<
%QXl#%,qF.*Z$*@-Rc1qf'N<H]BkU5]X6mJ6"3V>@.`A#BkiW`uE\1<G'<.?elWW'd>PVhcf5#6</3Xg9THYY24YsFO7g;:7&Q(tS
%mp+k\*&%OTjO,Sg5KbiB[[K&SQ+1W=$Mfc"X)^Qi3mCb1RbA;u5gqU/&MD=+Dug[S5oR4mMq0R>0Tm\!^GZ:U`1\60.$%>K6ul01
%dHjWY?K/>&h=KfgrA#T]#@3O<^qe4"UHTZD:!%B9l!fgM#>kqJZl0RqPiK?rh<M,FhNi&73WbJ,>Xf9224sFCHa>Qk$aj9b+4aFI
%:DOr&,-hE0K>RMW&4T2B>@60D?j+U<r9UB4KogD4a"+m<=<3noZ2dC(e,!e[\;`mU'-=oB1V+d!9HDY?h4dn6M,nAT]S\FOItC+=
%;Oud,rQH1]h^k.Rli0uWTI.IUGZ6SV.3L*n!IM%f_HF>k04GAp>Kt'h'bsT_GDRcQb/9.tPY=1GEf=bcNUAla<fj=(08(t+N95"h
%O^hLf49o.:0='.*k0OQcT9=mi;]q/o^uR[5"f53AkKnXcr(%<7!E1u,s"7a)1l!nt>$QW7(Tk=d7I`br+tW5/7+ARp)JdTRpem^c
%A/4?[@`H>g_:"&AVW*/$5jR@\;)&mXnT@JmW4H8K^aM_8&X&Xnif39Q<ST'aOjY;&BdgSJHkCP0CcV/$6OcUQ$\E=#['N7dRIJi<
%c[j*+4(@DNYpGjagC"SA8B7F&dnAr!/2^if;VAj\F-q+8*`Tu*0p"/BSl'0j'1R;de^.FG$7nQYaYORB8fF/RA1:q2.0`mtMr_%F
%A+LbdB^#kGJt/o';d\jqC,QtXA/'hG@HjI/UOi,j<(fmB:qS%,dogLj=\4W1o5lIF$aX:S#Z7:mkl=J:']iPDg`o8>i"ZUWq!9-N
%U".9Kbk+Xn33Z6SegRdolSM);1f2%ZZ"oThdR2@_#fL7*n&8A/?s:pXk+(pFesF>+U1L0q=H#*VN9l6n)*&s/RKrI`kO^)b-q7t+
%0l7Si"A=R3-q.*8rU7',YNJI<\Y#%o39s0KZ%Z<\3F]_,\:f4.==Bm0-,XrPc-LE""kg;^\.Af(6h>7ALn#d_$9cVUXEo,IR'[u0
%0N`J?_j.U.h-r7+$#@j$8e3q7o9a\)c:mV&>BVqk%\2(`Lk[2$M/gmt3Q#+Q!UOtudGf:!V<fmVkn\jh+Dlt6KcsMtJEaSN4E=(@
%7%)U_djf"(ctoA`0%_qT(&X6#Bhac4"slNkZ]aLC'B6P_M_ns`bF-:/Si2i7b67KZjJn7XRR:t*LepuK9BHpmab1f]^!rD4+A't*
%)DTSf,2!@2YpC][7A<b/b4*kb7e0[,'&RkMoP+PF'sWiUkT.#HTJX8s(#F+8XT5;SG/#qGr,^_8=p06tU7g'U/1NV"YWA_g_3b8F
%FA?@DPN,Z0+^#BcB2rX/8;Tc5+A'd`+P%5.;p[5mS4$9)*ZCbCBT$Yp[@m]J?cfY,pAO!t8oIPl*A4785?Z*O!-BVm20%1"(qi<6
%9sY,:lUu59%KBpIJ4@9^\*?=R@R4<S2DgbC5&q&hiCt!9il<':>-p7Qjk523QdVla2YHh`C,i&XDTqXlGf_;JSa-PMio:t3M-+2_
%<(@A7G*psebp25U8=p5:*f[S_pjBFOX9ZF6`oX,b(6Q8NfH4;O-6_l9=q3Cqgl2/$2M@f98s2kHL^MtVNBN)(cZt[QDsQ@gRJI4=
%U,D&<-(WKIjtTG^(SoTE=KIm_:g-1U:/>038l3mWE^j_o5sdV7[."`,#nh\iC9eI%A8+$*hB8UnMou;8bq691A1,"^TEl/tk1)f8
%6u9#>9l29WItG=j*JLrTDPK>t0af\k+C%u0)G1qEJE"UW>h27sAto)_7+C"j]HAs5+=^pDH+"1sOIKC>*jm+_LJ5Q2J9dieH<NF2
%N%?93J6@#&C$Ys/Mj]gMb`Z\n1N/P@^SK9[CSVW8a'4PZ#ogc!>EIZg0U`b<%#_NXn]**n;B#d"3ZRc1?^e*W=mt[01l[EDNAQ)+
%('Cldk6<FKQ.AQ"UB^rE@H21@(n0t(4/q`r"R$#9Cjf_pi].6LnL&CH<DXnoKBU`5.$IHBgLSLG>T?XXTb/Xk!eVKaOTmEPJ]9-7
%/V<A+*=QAc$XLZ3RAeGL&I6qjiY6Wb']`a>*>bLaqP&c*&THVk0@I!X\t,;7Z.l8\0U\p[B`Sib"\?-E]JlKT<F";h>$s8]SX>cZ
%0-n+D-nD6`>-(c!6^U(M<=SS`ct".E.otgBf[9p_!WalJ0TUg>c3:<#I8a`EE=BYp"2,H&`D+I?=jn[8a[VpO=]Ap)?Z[2e6FgdZ
%T-1qdKM"jR!W('f7QBm)!L(\^XpfQnC/Kpna4g4lfEH[,Ca2Jb9lB2$B]>:NW;Zo62^1j+/Zp+40Ii#GSTG^'Am"FXdn6Q2Cf]u5
%Xam:RTfhTAl1)!h\'\'RE0eaYXD",)>N?$3NaJKYGtu.NAGp>O^GU>GRaG=!Qd$jXj&Z7U=Kk78__R-JAjaM'!c:Q8UBPo0LEhns
%3A&ZQ_PfgVVH1**2Mn24gRLToZm:<?.#g`^GJp]%J8J?!-SK3F$+-Z#c%)ei23^C-HO4m=W4b%*=`9&U3!$r`+[1N&lPYsA!Q!2\
%'oNH@6b8JE93_t30$&ACV']&reeJoQBaqF'(ss&k\uetK#Y(m`SM*U5\=&?/X<J$H[O[>I).#J?io`9QUkKMIm\Dl"6K`n;gDf`1
%eO#/?d.A%DP6&hAYuZ[YZBVZE8fFj7+GmGq]=\[aED0rk=D$5\/TEC('+U>#^h5_bOMcg1i6JXLR/N%hl?;H'i9O_.]h!@qmS1YQ
%.)J:e^.s7X6,2SELRaa4PlU!kZ!2Q+NU<3'PdH`)YtW't"Vu<K028<L$WqC,(gbU<M4!g2k%7l.0%dR0gR3Otn"KOIO=K,PHfI$p
%j.fV:<W6rQ&k53AgBm;am1Zo;p'_QqZm**a1'^&(.O7%EU<_rX2BhA*;\'U3b>U'U478)F%blQLE-5TMZgOBPTbWV`3'll/&e;.i
%NiIHMc9n")UY/No@N@h#g8%T#Rh6;g\-6g(8UQQF"8!FD@gX)$6kmY<+R(=?3CSCk[TZ7]VJ?uEmE_N#=i'O5l<Tj;6W4Zq'J!Jc
%I/+M'1*J2_JZ89rg:&jA]Ni:S",ro]Bnl;_WI=&[$<D&'H4]QRPYQ^M+*VTbqsDqcjhrs'C\34F!1)`h/j4$7L($od:Vo;tl!2V0
%qHi_(ea]$NiMEA*eNmM=eC@H_ne*/#Q99n\gabKX6>R]C_*^!h3<4[-)CA"72*nUE0Jbf**Qi/$L'D@J@(:+:iDQn2Ka'%e##2rZ
%ee-c>LC:cd^6?GKLrO2[\-3meG$S4I&s]#-meZsLV?Ya>(LA4:,uYLR9h"J2.8Kj@o>/.8O8)X!gfb:\3kO[.$qm;]=WNCS>3)-]
%$7lZmZ?BbMP]0A$=ehC,R;dC.;37Z:&Ug,,#nAVreL\na=dWsMS;)JrQ43H9FnJ:O1P9pDkHH!=T?.,,kFf]$0r%g>#M]WP!*@dG
%6RE=6;9jQ+LV+c_^'f.01*m/*'$ZN?\K\6UhU84p'lW5FT,0kYI[XVSJ@IiUbQOPU\L3Wp)'BPB+s]2@&O5A&_p9>$RP2PnDWaa^
%a=l[2\ZKToTmP5h4$k^&RfD2=^b:8.bo7_O\-Q[JFfR20oIVYbX&]Jk!>Fu=iQ7)<`!B"DM7Pph:1d3V)ipWEBiJUUZ+7A-*2`g=
%9Xd'X7*dZbH@B`c'4nhBmT9K)`@h'G^8=P`*K:NmFL12B2m>>\M08;gWec#1/$)0jQtRZ(QJ0f<S!4ZTdsBAG;!cRikg9`#,6MNh
%PAqC3F.MXTQMNAl6\_lW/3f"+Eq##:>TQ\OJs.q=mNm0u%`_T8fC;`e0L7XJO?-6+qLW_/A'k.ec9f4k!<1=S6`7*[L)2g7U>(kD
%QIIifD#eYt*@Jg@l'AbW[2Q=6j3,PY96pa\)s$XcY[82RRGJWr_t#UcOH_F]':dK[GB4b&JEJ."FW6U1AjXYX#7*X=3@[m.V2;JR
%(3JJCG#Uk;\*2=-C'&^O@ch+beSAg?)]]YL6ngT'1,]Ir[^9.9.^8q((RBAjFbrP[WdYN))W8kLZP)QGPe)Tb-*(rKHZ1ZV0pKMm
%RM4'3*8j+l,U[*VL2Ri%5k/m!XCh[KL$9-o1(<B_ON`mXRP9<$AftPbj@Q)lL^(fg&M0Jm"<\?hem*ueO6,.u=Eh[jcU8:IG9\:X
%KOR1bZ96jbHfuGbU5]a@brrk;_I_T.NejL"2*e^W#5:WGW2-Cqe2?6tWMO.#O]Q1.I:ju%XS^35LnJC_6HF\-&J79cp!',8.%=A7
%78FZr14[H+i_F,1!j@DnY16[GQg"\ASnV%;PVkiu)C/NDPC8*-LU"oOVIF:>6?fYlCFE*lJB_2*d.gf\aDWpO))j+`aea/S-<bY$
%2h8/J;,]")Cg=]OV;)2#XWbV4@**FALa=>4cD@X<a5/%gQk##LhN0`hHeBt\0,aZj%BUl5D-!lDd*lTk"0\m`M(g)%))E6-370CC
%o,cm%6A9.$".j_XV?G'(&D46?:TuNQ%rW[)&cQQH,=_&6kQXcn>CsU(q2\I.%,X`O):I,U24S6#pmj[lDWXJC;*"n)<Wl,Mqh_B=
%!.C?M^$ZPTMBrd+lCg2I.=!2V'-MV;r0X;t*HJ/en]?/:V6G-Y;/)1:Z#W4Q?XV3AhP:f>Y)Rfq*a:t0:7>U`oJC(/8=jCo0#UhC
%h9d'%[]Z)GS`pmb63^*3!J!R$-m[B&KF/?s@i_A$'V;6cM28SKi-'dfjWLe4@or(lX$G'CUIR[]"oI;7[:9i*.<IW_CD5U,Z^9EC
%[DFbUWTZqTQR8_:^nde]Zf\e`[t0`)i+4G0)@YX%U+o7["M7hh-&1W\0K@u`'qcnZaY<gQ.X.]0/d^>c>H4M56!9$^6mTc276D=E
%"<P^Oal+e"Ol3\UUt2%ehAGhR)Msp,oBV@?2hQl/U/S0V/S:8UV,=e.?:/@eh9@\UB[7BO\Kq?Fkpm[s$k4QZP([C6fP9OjJoj9l
%0$'&B[WAdZ)/;/h2qj1G1GTH?h/:(DBkfa`^il_Yc+Kei3TIk3b<VZHbgi^+2,cF>5>I?[RY5Z/;+o<DV(s+>ZNA%Q*Rhn?_Aa;D
%0Rbjg:sfeWBmHR+(5\n09TH*kF=DmW)sF#OV&?9e@7""rdB&9QFL,@"WfXQ]E8oF+dV%^*UY=]8K]U^jTY;(KAq[-K9.htfm;0<U
%X#eGrE&/X4_o/b\FT,E+m#462IC":I!tWn<HqB4u=E8?TSpa%niH=@bAmdlmJ0jKZT?</$AnVk&&S$?,W@T3hcEQQe`7SZtSt*:N
%0K569C27-"d4N,)$Vni$i)+?S,:^VS/<J/#??)$sKHkPm53*KLS1:2F.[qnSG?Y-V=Jf!WP(q\#,kV4!Z3biDo\_e8]4.uk"4jPO
%,"d[,d;)\9bQcGCKJ/d#U"L$WR2*'S$BK%5qid0JLSg\(2iOnG'm7btY_uLql;JFeFb1>t5^T_[?,D@>J\m#:]n:KD,$_HhG&8F=
%51"r,:Z<@q8kJUrDaC"-"I)jU<jnsQ$9ipdpA&g/T>XqCaprenWEk_96Xn&D:U@DSKqDC.N&b)"0_l2*UPXfQ?%Oo_Hf;X$nY.mr
%<bBAXluQr&N3PGZU4AOo+FjGOR"D<,'E[U+3Td]YTX.HD$;Sroh,He\S6Tj;WEDNb$hU2N7Tc^HI=ka=d.iV;LME4[CE+P<0V2`2
%phH)u=+4j^!4)a*eCXrr"$&1B/qC8N642"T=S%BfcZ-HAi@5Bda5L'D^dHJ[.;A$tT/#_a,#g]V)qC^'9&A"W)e+A2l96R/bT6iH
%H<W5SEoG9KK9g?2^%`\N#H^uqT0m:/ZG]cSF<`,U#a)4*5NopTNk,ePE1LY"O@uNnT%sI[5TXNMA=UAm)LPK9,,1AOa;SDn*<_?H
%GCU$5@YD'c1YeD^c1YlH1doqNPVfp3CD:@L)&QD.(c1e@O2IP*eVMJ'QfnHa51AI](!$scJljP'F_8)<kl]jGl\`^+)Js0:E24p9
%m+Bod5B$5c>Xi^5;IeLfm)Vg+CT/UKEQPNonT'/t:7-'bMN[nF=\aZd'cEgbkSZkl;:2V48`4q^m`'8kK"2SHg`TiHGT1NX#(\j#
%;)FY3-1_gRKe59Q!u)%'lfRqGlRKFc%qnC_PNEI!k-EMdo2;).C]&q[J=,IF*lfj`:T/3uSAh-\H'PLeEQFE_Yi"?d8#h9r0JJsC
%3>=rJTnk@r%U2FIefuj*DCgVT9.^uUICV4]7a5p@k;1L>5pJ]XIDPC8lbcG0YuZc8os5dd6;3,=*:9Ia*193ir."`1h6M^<d1J(N
%^V'dk?tX<[_Fb93R[,:3PRoQlZ".NO*RU(M-gE9-O:PD9/o%E@__INiSiSTcM?GRO?+-+Q#,B1'U<6kLSI>XQdNKS_#^/<pVfKr^
%#lih8kK?[9b[-rt&4',*JBQna&n3`99:=E_PO3@+lRVUXKY1])Z]`N]9SSmaQ1=t-Z=a8*-qPOg',q*TK;NNB:hTjs>U]2da7uDm
%6m,CUfX0jBaU9q(X@Bk3%dS!Y99J898)#Y(0h(%J[b(SWMIO1OqD8SdCiTGBs0b#>/BNFb[J5b-EW`$1q?Ae-l;5_K7>pV"("X^L
%i/I+1<j",WJ@mLRj+^``D[7[Q1S1->cF-)ZA"&[^'M;Jp'C5J0%#nGbOmq2.9MSDRs.ElVM\Z_U[R^DI/a0?h*f<=l5ZJV17`;F)
%\-)YKWOC8878W/]):4,J_75*Q+MY%6=_@2dZ(3PccJhT2kj[s%YZ!s;Er[:;UpuZjlPBBV,"dc7N@k$@[f,T!lR[^c^lnpc"KV$\
%7(%F8`7X5BU\)tELI#^!r45HV8MnU.;IBb<_(:`[]9s%*J-<m4Ze<3_7gg&;Ar:k!]dFT.cCa<5IY')OkWeSprP0ePVVg]P?)Js(
%P+8]:dp+7Gat#``TfmM8AdOb<qFEj:#m0^+6ZekHB\N,^\=BfW*=uU!cJi4k,c'-;.d13Zo<Pd#/dpEuDaCQ-Vj9VhbsYX%("$0s
%'GSXs>[KB>SNkO%S8PW[Sg^i.[q8UI":V*n'/G0ZYg(<_"DR=U;mbJ=4PiF\L^U6tqoBW:b)fU'@:9S/Ebk:O%12-Yg599NW;k[]
%HSd%cHlGThFMMXb?7WDHOrXB?pP,n`F@aR2)4e)b#iO#noKu=0NACZ]1;=-N'r<e?n-=\<21RaA<tZ*_6!'$,XSI:NAl(Dh5\4q?
%8-n`<4i:a`9k17<`j7b]8Er/P;M4(qB[E`o2ZT5,;)P46;:V'pD^#r"P"+*4I_afDTo/uAKg,gu1*pQ8b6WK$^eSR_%NZI[CQeKj
%YpW,^[7Xs'(3kfFd1C5r3)-Q.>?KQ$+9obf1]Dupg`r%(-<__8ZrHBCd)2qe1UV@7O5[[bMBbqk9F5Jk)$)]fbQQOd1Gjc\<OIFt
%X?N/mSDStEDa=WVHpDisf83HXoqhS05n2)C@TH@\VWGG7R9`3^'4UOkam%Kl7'mopC=Th5NJupnAkCI,@8KM/RiqO2TCZWD@F,MM
%eL/]FO8ZJd<t,ug6k$;mU+UQGEl5*Mqjb#t42'H430ZKBnb[Y8\6Sa)H%c""C!u^0;+JQpB^d6&@*7t(/>n)0s"+_=)HR:m;pgSK
%*&tRHi(J4uYSr*NkqT/'bm_G9b#k'ObTXGQbre^uEQ]i>`<E/JehI9&&et^RTfSo!e0AIja-uK^)Y14H,Zb`"8lj(!.M8&*Q;R&_
%!-%p^0B^O0BVB$!Bk003X`4LM-,o-L,8EGf;T&_[KH*F9IPlnDTHf-n%Y0AP=d"D^7>-a>Ai66,F#iR8WgY8&j.IsJ_`=gAGHYph
%5HV4^FD":j(I/-H'S4YCO:CYkFW,"b%0Xb>Sn;%NK8=tuG$i'^L$0I/Tr,;2!@r[+iH"E[%)m>?oDo''VMK4K?:/3S>Fj:]p?3(M
%de.22XJ$IuJ^%8-Y&gsoL6<EIKa#V!aBUHF^lgY^gpHThYAc0d0h6X^Y()5(fc*IV"*.V6&]"uJ]OlZpIm/Zcck<-^PR:'C+`8r1
%EJ)PMW()ggN7.bVe9oKj17LLToR!d-U!*b.)r5u,PN-qlBQ&`M=Z+JYMcN(+H%+6^Sq<;bSQjGsN7jYel59>#&!*"fA5fHV'skt3
%qk#DbSeEt&ofkT#JiU>q<-bL_'jtY49Lj4SCG_S..d@:<>Wi%IoOIbo#sCQ]Lp+"WU]b,N9qUC/S;Oe7]i5Y86(N?gm"sn$F?`Qm
%'i3.ZA\f^K&:oe%+X25"8*^MZ;'j7C)8+-C>Fbb1a(DmtJ$9!K8n/9q[*rhfQokLY[HDGsIZFojAeVK4R<!GW#`]WJ'F/g^SqE,k
%P2H=f+JM5WmV4W*QnIX1.3_ZCL`%\Wl=NGB<[2J`55UGC!brFirAj)?>nP?7a.5Jjc]U%O6q+?@$DdqF&X(\t9i@!WLmmg.P(;%"
%,,44[1=;8d"0_mU6jsA.H"T]9152Zu8R;Qn^*@rcW.p,&3Bfi(&;^`@9f-ugV77B@LgI1\Y5l6J%98Olei==0!OlLRd5k+e),&i;
%=Ll;5XdN:2(YDK#4a<0Ye[WT:?p\?hYGaZ!(,S6nX(Ch<JqGFm&"[$CEDU!i(a'2\_Ne,gSZAM#?%R@I6:<"u$Kggaac:'HQpU+)
%TniCTLcF]M&#:?IIj%Qf_#h)O!0h_oPrQ5\Vb1%K/Y(nUFYOVZP)1YmL@rD`gUIFXJJpTY:*ZBcT!l9h$GfHTA1GBBDMJ:$IfCOe
%lHYAA&HsU+"G_fh*lP.jejS#F+^q?1i^:%-bDM[d6sn*Br?,Rb*.&ek`+4E+U<L^*@K_'T$@q=fU:p[")T9.i,B&:#LgK]^7V#>S
%h3QjJA9G85aAp4?.Skl"(p0B61;oY!(CMg*Lo<77<<]2[0YO0?T-?Qu#6>T`/NCVcCk_[I67chaMTh0HlO$Kpbqq5!.7\;k7V#_]
%G!J%nYk)Xn$5"#T+\dWSk`):4,qFc7il+S@_<LrdBneUDVXm8P2as8]9nA=B.6=u31I[(XCG(qio'G&=KYl;+c=C;I=r.4@-_`r2
%d>68a0:``c@78F"A3,8R:C3+o"u2V';etgKBb+5\e8/*3?03bB4VXWgLa%10.fZK`"QW13b1>kKm$NEsW)bhlq[j1P!G42T*a"D*
%UWS@G6_J`_Bg13GhNP%22!t@KYCaX?<MACn8.-d84a\\sNK7QKeZd'^>_.r=6h6%8m0$-g9PFp=&u&jFf<B_kWQ4icE"D%!#(]db
%rB[T\MP7b/%/N^egrNlr,7cl-/)j"+&16CE^Da<9nrRSX,;j'MCZT'*.^:Bm9NVkEW<^p\%K_<(DKH3-8AQ;B+Wttm.2oK)$S6d>
%pj6Pm;bdb:m&!i3of3m\oYDa96Ei6`/6M(,+lESP,ERg4]YlD_C7%$f%K(T5Z<,5:Q)g%rR3J8>cs9soJ3$K:6\40[m@QVjcRM+S
%]M_K[1AMeI,!c5#<5_1\O$]*87UsHa_]h2FTeN-9!XlpM#Qe2PifO8C=0`_I4L<^$2Y@@:PueX45_7lqir"X^D#1EZ\P*n[Jhp0o
%#%>)W38G\erBOLkPH\^E2O",ERIA8)LF$%3\_HDNLQ3GDNFBNo:<ua?;,h"Z&VH]FI4lg!P3@%KG]7*;N:IXG7M$65n$FeZ%DEQn
%2PVJa2%!hJHI^1*&WV2^8kg\-jUNo<qAZItG$e`;KUms")J>c,BS(9K(-!"0FLF`KNXAq2lLb]OFd:SR/fQf0W7RKY9LK0@;3Pja
%QCtn,Jk,IY>-P0!=_Em%)^[9J80h4%J9[hllZl92D[LB*99#jh0GSQg#4@#3PoaoF$GmaO"mIMt]QVZ??Ct8h/iI_IaQkAh4%*=h
%EOW@r=UD1)nWn'JU*3M_BaWY+[4bVj0>U8nn:JtS/#9DOZ%rlBb@<E*M*AD[[>#aBObK/Ffgp-Ti1@@K"X9_E?F%AjBSMJ1/>Dmd
%>GgHROC-KL8Z,9=N)GgDX[b&))L^U>;(pJ0.+'h#Z?)PR\^;qMbsNWtU;-nQ-Q_4opNiDQc)*Ot`P3\KfPs/'E4sa*`<g$b](6FA
%;BlC3Zbml=iBZ@q.&'-q4/cRQ_#5J[1qq-W16*7X6f!@N$i#nU0VZgj+,sDt8PK$'!`SHThI:`:\sZYl&B+\f;^uI</Y68M;*^R>
%e?I`1?W3(7+mi?!>F`"QCt9,o2J;D'\R-s+AZajA%_G\D?nmI=1r2(T_E8n,BDoc^Y3Zh3QFiXT!>:Z\QFUL/^al83$&KKnk$@VV
%8&qcB$*`P<X`\^n3mp^!AW;?j1W)(Mo\$OF9?H,?YFas&S\Jb<Yqg)='@@uC4"FV1GY^Y]Eji2i%6'SJ'VtZc#@B&m\[U]ZQE9@]
%"R6O3)jGe"Re4RPkBkT*Ssmo6S,="C0-i2D6)%TIMI&q+j;ggTMOZ[/GP4M0#+:sHb,Go`T-*K%%5P%P>]`V49U:9:9S?+WCb`N?
%a2^)[e#Q=hM)_7m\Y35A!JO'lF9kg\U90NG2H@VFDF$eSK*!taIBdRU`F>d(A9lN=rj$!c&\Vsah](nda_EJohT_F\oi:R79uV4n
%bpoY$a?=3SE`%l3d1chrf^(:qrAdN"63Cb%0l%[bF=;RUr5Ldb:/#?p7+;eBN%,?&Zph!kjiNHq@1nCiA?Q!VIY']&@*mlc1*4M?
%'U*E0&]5X<,X/[Ef<l)BM(1MI]'FZaWo<))4::\E3:h5Md<t9h(Q%d$#*2::*\H'?E'RO5VF5TCbpCs$>5?l[d3LHVc$b'qJiU>W
%I8KWe>''nTSBcM0+uY[QL#F4f0omU%S+)>TY,T3>ld;;V*`8)!6[O(iMC(oOA\j27,?kAS@j':PgCJ]BeIt'NT:)Wl%Pt/!Vph40
%l%&;H#e=Jd"'?4u_O64%-Qp5h3Xqo%e4'Ve*YU=nVf0M++j;;XRYN6tgt8aVc)Rhl=Srl4JNTC?RU1LErsG*jIYAhhJ/iHf9O,5>
%d'k'V_1kH7n1'GE;.gWn2D1_=Si0;$7j&m?1HO4O@*b]<H9TdIr+87O,XqS@r=\!E&5k.f?V6O?ClU(67]]/*_/_bRX<=$bLoiMA
%AN_$LjZ9p-^27gl6Oa+$ReZ-Odff%b=#c`NnYN"]</+Gp*;Xsk1AG^JoM206jT_\NWh[q^NgZ.(k]2X8r;09>da.pdn'6CVT0N,F
%hu*CZg)'eak4LCa^Amomhd3sBGP1Zh;L`]Zqs-2QoY:A!2fC<dY?mhUq/"F(P5Dk`]!J#-6Hno4qrpo5di\S5nc/UVHhq[\C]*j4
%KD+,(3r@I\s7sFtm.L.mn,@H'[(4Q%W\OM$!$YV:Ta2C49ettr47p@7X5j;Rn+?2cIA`\r1p#SnrtqqBB]D[,>YfUW*gG/R#gJJ/
%mU>Tn$E:$.Tgs(%T$h`E%l+gsmXe?(GT*uOpe5ZsK@=R\CmdbMP4:Eo05<'Q75OAj+Hk#ATKPZPU+@H`'<B9;khssV.93iFC!9.]
%K@l\2#mN+VK&qpE*MnUh.c4\?5PNO(3(s`QB?);1a0?fcX?iY]P4rN![I>=Q3N)[NHD(0\YOp>#faM=+'0?jPq;2C=iY"duA>80@
%EFV`l@QN+4efLd=dM:ns8Y0ZV0l]ae*l"V^P,Mk90dIbjVhbg,!E@O;`7&IdpS"N!;1CgooE[3XC(2n[;`+b6Mr:DL*24!#q+iWj
%_r00(>tQXl0i&b/VF7Bpc,@HhpfgV$Sui6Xj^>Y^U.jpL/2<7pqoI:,TA3?9RC%`!1,76N9FX-]%Oi(%oRaMnk^h'3ot[.>VYem4
%3KEJE6i_`lEG0])Lk3t[XbX158pBlJCkWfr-b1h7BAUa:W*F)8aa4nX':a@lNX**F1$UeVWJ3#nZM.Q6-1NuI:>^%T3f1_I.=.F>
%pT3OT%nZ1E>3UV.G#M7aFq'59n<>^T`?k.1,Ytd89l$VI1sDUk?<id;]U1(6.$[/f(o[id'.XCV3ef?6A5W3MIX)sDZpI%hWQM=e
%=$>I!XQW@36]m;ElO\-'cS;q=P78.!Ru(i-;0pG7H;&[aOrbqTQk_+=[M0cbaV#%1@i-`Ej9@X(6e[0=]pLF>HN^MuSk1:&(8]b.
%iA40gW6n[KLI-"-9Fp>1#c*Z5/d?Dj3XH=U'aO,WCr8#>c!L[[kj;u;K!'A6VND!NfuCh(>aQ!27g3FLd)I4dm%t:jM<oSq'?):%
%rg]e\0p-<hZ8et5mD4p6_i0M])BFHsMF^F-g^>Ik$t?9>@pmISG]E+5Z.-cu1,,L2'CkrX\[2Rt1GMf2K'fD0=-2\,XCZ3q'1U=p
%a%hYN^*uLCeTiA#V'oV!1(1A[VEKafa=#:k=-Qf%D1Na6MA9tpO#l52mA8_RM*rhVOstGX"Zo:tO#[V<T!U3^#SE74D.tagp+hCg
%\boGH9;pkn'"f@1*CUG019Bp=\sFb_F1$cO[o@O,j(Xq&O>]GT#DBaM/`73c8"''9mYdt7gA'32@ZiHFdCb1cj<5nFO)alTA+`/a
%`H?#;I*`oo6^!7sgcnEl15M:L#r\jTiQ_"`d'aO0<_[naUCl0X^taWlJ9ms%(shubOhr6r/VAfcJ'.<j"tpQ4qPlAkHLa-&l.HAZ
%U^id30#XK[0a53I/_Fb](e:cJ%G-f,%;(qZInY5,9]X&eniAkEPoS)&f0%WC`aDs1"V^]i_tgt=@!/F/4R'IRTeaK@puk*\1B^ZT
%NX-q\ED+SW[9<"E10k6019m^ZN$taf^sNFnNYKg6fuZRYHo[<\8@@$d=/sP4b2Vn&)=ij^^jnJLE1Y_Y,^bYY^.bW)(;(?).koj3
%;*]:T1*Luo9/!dT6h[XoJf.!LAGK8g=@_a%aaI]GU$OHV@NC>B]rX5(f^kNnkJQ^GgGJQ(2qrZL__EkWHnb=g!g-UZ-27l.?cWuQ
%_WQd.Mqolo%Q3)DA_kSPX9Y$uj^quTMaYq8UVODZK1J,1Js_GgT\340f1D'\2Hj>Drab3:?prL@J):Ar7X0[:1JK#\>ph1g:RG8)
%1+44_!lF;t-+Q?BnKsXULI--EfEKbmM#etZH8.n3[KTehp;(<gZ\s\c0oMX*EjNlMOkpP54@lVQ-&?k<]U*nMPt9]["_h\i]=;i6
%7HGnea@J0pWd]t\,`(ZMq\+M,-F^t9.Ie$6+B'n`[=VAr=eqh;]P!Hm7ntP%"T`=_IZ&93b"+pr(ueqTI]GL`[QTJ;eEtd5qmXeP
%U<G00=n^JY@^%S+'gEIr.tut.BP2mTC<TMTC)FPK+fXHMko4,?j=\9<^]CPqMfBKE$s;dgN!TXoD]F7<c)0RTZS"L(VlZlkcm>`D
%^VNpjjE"X0.\l+I6V8jULP15_N6`i1hWN3FIBP%UJ_#m32HUcfW8tn!`db8"=NA3^KoHAc(6,CJKeEi0E.Y`(A.];,g+&@4U^,nI
%+f^pVU5a_l(p>4:maYsSM.JpSFS/<3W"=W9?\PTqSq.lr=sS4XKr@#/n$@tG@j(6S=:@%Tjb3t^e`k(nT_Ai!'<%;D8L)k^)H#HP
%Nt#Hj?]d#Vpp.+q8iRMV/ZN1B+ji$+be`IqbKS*"<p9W"Knid'*"lGQ:nZS8^9$BT?@&=gG*4=YZOUej5Tr0-D-UY->f:P+=$"KA
%1>9J6ZLnce)Mo^pL*YUql(MZ[F<,iW)5UU!kZG&5YkBWr/;:Ou*NW/bdb\7^Pg4BM<[r$;N*_=rgRH6O(c'#Flu%!O&<[JE]KU(X
%jVSg$]^dX0'Zj2IWK<`F;+%4A'l1Zu8K!X<PNnWZd55$W<)fF</s\ri?@mF_?!rf/3N%Qde?25TC3*?nVq-U#;4m5A:U8`TFHKlm
%L_WUH3!P'S&[;]t`u'#kdI'BrFb5&cN.sH]e6#S!q;/D]OGFJQ%e=e9ZiH.q*8)Z\eguO=/'H5VQ(ea>9+aT9-6!&HNL!aS(681M
%0efIRT3agT)2lCAFHKLjU+a(fP]`DT;E2eR,pEXjHs=]qc"=')/HsLd,c?m'6Q#uOT.1r(6l&%&)YJtEQ9*#`;:/En^1nE8><gsJ
%iK_bNE/n[fN5IZr_V.o,b<_fB.)+!m(;srZJ<NtiE\<[$\gR[O$X:`CTZ/RqUp''Kp6*k?P>286,i#Da0hU,n/4LFfi.6_ZAu9'/
%nfEa(#.#gj9f!j/U;2h;:,:LdfE0:B>\3G&!*4C(jAG"=E,N^bnd@?!B<hgQ+pHeJkVIk:ece7!V$\HFn0nEGP-M$M6NL\mcgWsX
%ZlAs+<>6>FB$cH$n*,^nTZom@Y@A/pX![DR@t3\moYB7Q5NgP?7IOaq;$J?RqqT(KT=cd_XLYP(apg'c$9bFN9k1-70##XQBOD\[
%UR6d/]X4);(7BkkAP(ua+VOB*@_mHPnlA8K*EdcbE)qiq+t1r,N:OXH/1O<`SYWn&^*rWPF94r6d@kR0j\98B0V[5e.Hj1VE#_e"
%VT+Z'eWEo?gO>O<H<O&Va'nMOG%t^K`oF9XdAu3F/TVUMVq0O0i:Fg-WE%)-7dmu%cC5i2,S)pP9P!n/p7V74dpZ1Jn=aI4/m"SH
%*O7"IlfN2&%j[0hXRr[mmc;eN&NUE2o\,IdNL:X/^^n(nX>%enLkX/>+]ek&A&]]VIO86)&(AR!m7LI-Nu^A`HXIeX&G9(F`VV)@
%'eqR=%r+/#R*t3FD2jWlq10>k[#7N/1T`'RnAM8'5I59Rn!`aCd)C_`r1)7A;i0.Rbf<CdMF8t9>rc^&Ojl%5\-O:fQnank.^?)e
%I'<BWBFKSh2Vh##8/Z7sCeGtfj)0mGaEX<3fSLh@(5K"%g_dAR$F7t44Jo(jNFd%Fd"Aj8FEnNaBK_/Q7TQgO<83%q/a\)t-7f>I
%/<Xa4RGOGd6rRbS>RXliUm*loi)l?jXP^+O,L(mACoURb<&GpgVgL#<Y1T.9@[&3M@^Y^PP:;e;.P=#-iX:0Afr``@`9uTLXP%0M
%S+NuD]GJ!n;LPnt&""_k+O>XiZa-!(4P0>T`ib,u`H/h4;drZUEJ"RgLl4!q9<+q<cA<B5H@tto)G8&V!lkk29Os*.1S85UPmF-U
%Xlt>dZ7\Q&Kh2K;4j-=uE<M[ubW^&l=;WZp&b.s(6tkGnj@=g*MUUo*TQrAYTb^:fa:56WZE4JW+pq1Q!*@C/R=t#=H4s6$$Sg@k
%&[6&pOq:bFjkXGLd4=j^:;*6X=!J#M%So^$/LE1tp+6$Q[Z[=B4_Y@;5k_^&%pT652Q-eFT;YeN[g.g@%MsDpMH5]JcV?$;-=KpG
%"mQBNibUal3OVTd6fX'J<JgXj$F,GiR>PupTB6tM/":*30QsbMOqik2n:d#'TQ_\#:a4B%fYNF4>XAs1QYWDGMdOnVB\nDEmu?*!
%0"%pO*I7;^OP&Q^E]MU49\X&BU=EMr#XD7!f*_4\9WcGS"N6ge?5-X*3Ra*_qLhu!WJU\7GW]Oj7#[!2dkHL+%_J+nASsr.+hb"P
%q@c18r/Vk9bsp:CJ4njj'XM8[?qN5;X3&H%KJSWe<P%Igd<'JNo80EpoFXrpMqXOcRY@-I90ksYrb1l<jK9tB;W314MDDSMHr"93
%rK`pr8C*S^AUUX0$'Co<R4./NfX&NS.848,d6(/kQrPKTn4Q+/>3ATOC5HJ,c%4-JeRdj"[[$\2m.t3+<*!bGEYfgo;R[4NKa*WC
%<EMAIL>?</5WeE!&HQg*Fa>*ZF7be/_&:@:4CN888`d+O.!$p+Y%c&gk[lI`]f)-mF>@enXbm,KPlp`dfC-+T2+/3PL4?
%7Ja]1G>TOR;T+FSiP&Ou<C#V*N19[L<d)b@NG#I)__&13BdFNL,&.XPT!ZSpg?jiKGpHMS&g9nDL*L9(Idua92nscfrT;r/S):u*
%Tl^cC>6!]^5Pat2r,:H65CWSRs85[E2l!FGroEP8n$@K\s6Y$F3rem/lRVu3Qe.SmY4(4P]`%g3s7G!thu;m6k0<\A(Lqu6YJ5Jb
%mcrQA48[]Eq"(t50CH<[r;-9Pp"QN+g`i9erP-!j2uf$>j5'Ie>k(&XX`s2OrpK.A59HH1q>G3+5@3-Xf2gd7^OGqM'?qpdMd)/A
%BY0lWoOP\M])MZ[ml&Ao%JM`N/X-"ZL3`^]Yc%!hL+.Mb^FB*Y?bZ=2br9!:I,R:PQ0[$@k!G($24;'0s6Jt@h(S3Q^Nhu^pkub^
%NmBJYYF'P4qjZnqhenh::@"NhS'%Gp`-:\-Iej!*lHSG@LK?upotb4EcM')f2g:n/]7+u"'F/!Bo.@HK\*KNa/XsH&H>b?HLm0!Q
%fU!E3?N&@-X8hpCD!MjkF0GPV-:^Sr_ioFop$MXG/MA"\mV=lB_fL+prq8:;mk4ULkl@lG2_pl##4?tSi#cQo=9%t;VQl*WLR3KD
%4*1A__cq3Vc.1R,GB[saCs6_,YG:geh>=[J=9&3lig[-T0/pQ!:X+CPjaM:7SDa7g;cddZ*b"O3jji2S2g=.c^Y<p;51(c\=S_Fd
%rr/6c?_)Ug;WSC+1+-)iYbl@jWa%TaEdo+0gfJDIL4"7;lauJ$:WhHi.!$?//14`k&9PGl4S5GmSTME+0)+OLJs.h]=00TAkG6:<
%R`s1CE\G9cd,qc2ipcWO?X3R"lK7]A04*OapL?;KN`"\rQ]dWhBCJZ6&2CqJp\L4XJ%WLgnSD*Df&ljuf9\GbT6dL96;:j[;SYM(
%]`7][l\d^:G^=Og/N7T-DQgNg`7K?HM-K,Hj_9%Sa1#d;of.3aFnaEfhd4"nJ_P.^GPsH_\"gUthJIL;^:Ch&Irk/R+91uXh4(H(
%>;kX&:\H,QDkGpf]/SW]]D/?#c(F?<IN,"E^4$%TX,G0%cS$qh[MX+1S*t-!2l,#$R-E_gc/%-(q!RIG($Ze?kkf4<P]eFGrYJ:]
%DS^8J5BU?4#8m<2ERUUONrSqEHMmMlV>0LBWl',2/.PsbpY.T9]3;sF)<82Mf5KN:D_hb!m*cF(F+*i/Ps$4cd.?U/!h1(t_*9@j
%BsROOT>4n(bd$Cp+6V`F1_-bK=nom5`e/CiW6kW]2LgNejq.8GLFrDme3%s;i"(/V[d^7\m#b"J]Y/dgp$L;A/Ipm;H5W?EZhu%S
%qLa8:Dm&!rp>Yp?HctYn4AI3t@k>;8F5Q]%Tte0bqthmIaeP)D[U6E)\K@Gc'W3@O+%qXbV^S18B!_4[T=eSjLMdXXZmCJLjR_[)
%mcG=(YHIkOK0SF]\+(I/2Z1`-WPWR,?=1J)Z6EtlY:Y6PI<2[4QZn+/k&UhdgG"DXairDC+$Xccj<p-Mn(rNbp"dmKml*@,#H3BI
%f\"$HZ0/p8Sl8hGQGf.(oD5*,*hGY[m%unG6bhIsr%5s1^\<hdnYn/9&P'ZTg4hu0(oQa"A'=;t+:]K-G5mn5,lM4]dMkNSs,u1:
%KAE21H$G>hT]"uPN7+oD^3K83lK7]=S#a_L]&^c$m-CVKlNY\Oa3X55:\HZ\VdcaYf4f$c`I;k\SqS#gM6e4OE6OXfNn)OiTN`aa
%/?d;OYJ:%uk2Pssp?Ko7?X8&#PY[<$_uH;kps\^0q;Hb^5IZG3BR%mZnBe`#mD\:,K9PpUe@R@mf[Pq:a8O(5MYlISY:/T)=$LfV
%%\(4Mg5A>5l<?$1NtUnkK0F]G[sl5*Sib0A<uZdS^:M%hfbUi6ZbHs_\G>RPqkqB#h%TYQipF`1.P:BP-+@;D?W*Gl/bVVI\$)dd
%>edh\fFV+Jo]XJ*h/*H/?CL:t;K)![Cq)VEQ^@/Zhu>sc_)eR'n\1lc2]TH]>JQAse\7ociJh,e#p91)O8m*:[G-.;PC._r3ph[t
%]mbD.meW!6VIiQsFojVd8U"CGUq;8F!l'&.GIusBj^rki/2#daheln25PnD)GQts"0A7ifjWG8EbNBRrZ=Pf3@_Oh]8<cj'E9J+u
%'T$jHOXSPcB].un$:WVLf6DDB-sLlY=Iu*_/BT47Ef*<]T:4XAe_iu-;4`Ia[#)+0IOrY'F,EE>17;WtD4Y;iMZ52_7G2bg&"U<q
%X&tV0k7ii[AMV<Cj,Q8<A^as*qGi$JYpbsb.2HL<A<,d&s-R,]@uRD?[$MlN2>4SI`snWL.DkDX'?:>QEBG318Op&3&NhTA_OpkO
%(VaKSQnS/@@5tX5fO?Qr+oUaJYj@PRA+,\fIN6MSM6D=9?RmM<[UBJ;I-nl(:42FWpt@sH*L)btp:S>O/)&Jd=$(E0^M^2t'%WY>
%Y]f#@-a*/Ec.,jUckh&uD]d\rCfl;(e0TH@%`$ONqY4i*:Y09t&tH,Pko/rZRI&5iL1iuCoY58N86jm:9,\bniO@Eg]"7_)c,KEa
%r5dQAH^W6[[a9at\pj8IlDs50:Y2W%M1k`flJgVQ2dnTqD>1I<Dm+'ISjY%fiR468BmrI3ODbDmk/5Y.iR7'7Qc<r(G=MX,o-*Y`
%KM(k"eFnnpj3RX9_4CBarmREts8-X14oYK-`]ouARN.[mBcnLMpi:&BdIXC&pHP>7mO(2B;k``V$0'i#S\N/ur]R5NcckRXnU)?<
%2u@:?dGZHUhq`a&p[->FJ+NXg54o`_Z[FT\=.%f@Ee+\>^-=aAO'+Do)XU8cgb=M]_LQgl&GKimfJ&)Yk.*8QJ(XY&Kj"@_O1ac;
%mIe)pDm/#7@D,2(3dr%^5*AufZNbJMDr`8I?[NWMj=r\>5&tS&-5bs2l$)^u*hWK,7u$(dq0rp?m@O6j-i`YWs8/-Xj]%T-jT#1N
%T#\tQH^t(BQ6+-M*8G0G--;@spHU;W#B'Q%q0Yt%hilC\UBQ=Q8*fc+:te"5pN=Qk^UgtdqsM@#]=Iq>fC)1](RG9;\RB[UJ$:lB
%DM(o4rRG!<^V+r9pL^?fDmlm)#J\o\e?FLh^OQ.2&=9hsb2)1bcMJ+MV:BYXN#nh?1OAI,@ODQ:a[10VOI[q5O%eiL>0*:,l/#./
%m)_rpkg]<D-51ghZerO2\pEu$O:n`\)\;K3n*`I;j7V6Qe[s/a?G6@)md7u_-iMaHqq4/88)%Fs]_Ls]g\:0.Whm4H%+0*&Vm(!)
%cIL27D]A(V9A;V!Bmt?P)XTQTaM#Tjr)+5gF(NqCH0i#=kMKNMNedT#5Q9o:+Z=/B5!8bbhF)r)pAgue#kQ3VTBu"YI7fKI(:LK3
%q<4Kr8%8eNc2BUYVKt#1S.%%u_op0ko#RV%L6n+_j^`W3ABulr=iBg(<Z&9[S.%'+XFZrO0-=VIfcO:C5&iak@mKlqQ)CCc+?_fZ
%M2tPK%phPODOIj:@;u!@%,h=I6(d!]fYj(9*'2`j(8-CL#FjAO%k"T<]tPqfV#82_7gpI<qfm2\m^F"a:\q:jO5g6sZpT<_:In07
%nMm,%Qk&n2gs1E\ra#Fm:&&DY`Y/F&o0Qj]Lj_F[0:gIVp2s&Yr\%W+rq47r]C/K`CAqC%MOmmMrW[2@;c`n6L]g5?cG#uKjQd8?
%9"E?`Nn0LV4N0=)k4#99^E*X+^$`W2lJ&@`F;s(C^#$hnO6t6mL?J?`VuurX]DF^UJOA1[k?9G6UnKq5%F$9p2]k5Whd+"R'T/dT
%p@[;<\7IF&?Yp1'ZA/7Fc3p7'9P987[7=)/gEa35[<n-PV]Fe#[X>Aj*2+]U:3MR@dV4`uNmXGL*Fm84:3MSKeT?l>cCaG?-Tf>A
%mEE\4EO''oM&h4*[!*=U@]N9#flPNd/q;s/Qi5EfG^FH+mc*B\GLp&.Z/p0OS<.m.Vf_sbhd+"/I*,X&UMs,CJ%NGJP]aXfH=)kW
%0/nFQI!P8LeN^a\qL@t;hHXqqEpg?mfA%u;R'r"Z&U=`geslEC^DXNLO.20egA$bRk_/GQ!ZJ@7rWG'"6Ke117-AhBi&k43M%_en
%UJoo/2NN3qTqp/L].F*_h1853?bO.bh=;b0Z]0RWV_NLF$uF:g1Aq()qmTqH^DT!,3/rO9%fKq%=FKD!KMMl"Z;4Eda!H#%%&I/b
%j6%-G[:qBd'C`D@1'\lg[:K]#+7(kWkV&OMmQW]GSs_t)h1rqSP9p3@LLc6ccdrk,8+@H7j#9A(r?\oIIEJH8?1+q=\SX:gUs8;i
%[[a_8P;[X5k"aXcrj93F1?1rqHOecb.C/6koXPF@&LV_h&Ce6")g,'A:?pV+SNEfl-["i%25'l;e@A;;3?!&.Kms:6h6olp^.S-l
%GB^6?s7TpFK6Pc,SqB6$g^5`O&>DsN%/\Y!2C?cm8K#%5a-AXCA"IWcNY#9$O-=oSbj3:MaOlP.qIYPa;IK;B^:_"dr53/[6G!'V
%ipus%p]%J>qYBNjmr,X:WO:QrcJ?K$dRSA>2U:JfY0fJ]_)oeDo^&ZK>>NgkHq'0iG0[F[%_cL6b?*#J[iK\+]Zi=&5/4b_,REbD
%hUb'jmd!TXqDVRhr5B1$`H?4qJ$?-PD:g]t[e]4V%9HuqU>+N(`P,rn+^X<cpM[gX\@gOA.!M/Q'\L06)Onp[*mM=(]eJh0#<MBp
%_qhs"n*Aa&Rs]e<EaKbi2B_Qqh2MBBdG2FBh:Ind$/,J.c_hDJIX02Ac1s8Qm*@lW5aVkJlP"c<dbKTKNbY?hpnr&uhHXo\$m,E/
%J-@#'p*1iTLHdd*Xs_WbY>DV'W(P"#$LIWIR-I6k3XVSE,SU.?"8Q3t_,1S/cd%4DOnI]Er6>%jEQX'O43(AUijb!()]l^X4QR5`
%2hN%9lZd97_c-%Z4*-2_Xi:7tWAZhZn;"[f>Bn[?p;j6dhHmtoqY"Z&!J@.n+eMusmWJ^*iP1F4fC+08EW)\=k4CVS0LIqoa)T;?
%Ep/SEQ@1aba)T9dS0#_'B>=cP/q0TCI!Q`ms(skf$hC:Li1UK@T8".'c[L8i;&n\SG[;]+:P_EZKh1j+!G4QPF*!;6K:fa;gHM6m
%,6oRdB28L$Z+l7giVhe$\*P+R*lAFKpo!HO@W]'[QJs;6Xa_D,rq6R@RZ:RX]=E:cWup&s_e'S.9Ak0"&0IN.,As,tkia`V?>L0W
%%j!"V`H+;dpiKQ6]hITJs$QUdqn2kt$o7A>@d?3E:j\LPk6RS'j@Z(:rS&\e3.;X'OsAVKK>0^$9-N#aPc*,mg5o$RCq&Wl"mY:C
%%aeGVh,dZrs69CI*pk&)[_q'?KB`0e`KrMbs0:,R\C5b2]?Q!'n]c7Sj<jp!rlsQ>*[MbN=jPmVl'NL;1&L.o4D+T2b`:qPrF2tH
%+fsZ7NkiEC]m0/9='I.'*8m67G,C=]'@RFtlMTicOJ=LWaK5(c8Y8ZuO6mF4]`%cjk+pu6\``W"@ej_)/3hMf5-bIBhu*CZ]_g\1
%hn6C,85.2pqVp+NDgjco@>L?g9)ll\n#tWMs74X2r;%B)_uKN-pU=Mdh2p+Om^p72WB:"Q:OhBLIeVYoJ+A'_s1,Jnd!rQl4dFUq
%pnQr:!pN1,(31U^GJ1OTIjFJu3W%V9r<<9i^\aW%g]&1ArR[]jGF$O'FnU=aY<.(o55VsArTR*a1nXWfl<?BOj*tBigtUL6kh4!A
%o_c#e)UAUB]9f5Q6H&XS[4]BO#3J&\h+LgJ34n5mo&ub_lCn!>IZ/X#8q*1KUpb!EFfh3+_q%F#`#-M4Hp;ts*>J7RN8BCt?M:W*
%e@*(BYnAXBQT<TFp)ik1noFAgW,3Ke"mO[SX>)XXVO,udL;mZGM!8W\E)gQ0`q5,=?i&LAfcR&mJ!l1lNq4*7CKQ?/I$:P[O9uTK
%=MMYY1<,]Mc6AO?c$<LN;IA"<V/:JqBYn>.9@l(5Zl@2UA+@-hKC0)1a/ELOqC1<MQd#iQ$h;&.NWFMbNntS)\\-[.T7?IOhnJSi
%HX,rL\I?7J`Qp1jhr"G!h7ICbrL5J()LO7LdS'&")m^T`YOtjX+2cf!*e0-VF8SP'A&slu:*_^j@I-bPP=_O.01/N,L7Zlh#<d"i
%jli\dke#/%bnsq;g,M_WlRYOJFFr4KVD>lbVep6Ab;B+Ya--(r-S,!d*ON*F6.Wd6d2ecI3k5F7D&fk0K@%XUqq8OPgr=ie1u^Dg
%;(lrpdQ!AGDF+_@5Uka'N"__85#-$p?&soe0$6:kpJ:5.P?[q:)0kGZ4E@eM4b?HNfAI[o[Z#KE)=*Z+93jT\5q^C6aOCUh!^Ei.
%m.`ar04-?+qe1t)='>kIZm%"SLiMo&*PpY]FPD)LEA35*+i_OGUaG)Djm:C$FSR/Rpi0gK;t7e&a/\\:)N3Fk!@49&ig=&J58*pK
%,>MK!<'I&HALrI<aUuk+@u9?p#.Nm&JY<f^XZfL?$W1WO3*Lm)"pZ)_UaPfNaZTm,I<`sX]b!uqSk!_Q@lNt`'n:7N*o4mbG!4Em
%Tm:NuSA;D83eWZ3e_?='5'qBsJ)"?0pRhG\TDe07Sc.t@jVe%pf\UkF>'*2VA%X0$B4B*3GGaQ>;<19Tn[bMGgH<&CBE&p0W'u/K
%M*BKd\%dkJmu84:\?^*.MK?G&ful_0VFp$HZ'24J%U:$X/("FLIP`&5Dq/-iG<>OK#M'.C[;jM9#j3/)1bl8K9L1X;)^dNjSCfsL
%A+=TBI*YY-E0+^X&E=VPR=)&nA2&fgN<Qo3,fV3s?AR"o"\E"G7JDgTnSj>7jS/,h&B,]]fI\`I,HL=tH9JOC`:lX>'9\#M&&HKA
%eDu?b]gq*T`LaAA:YIU-gqbUhk7]Vb&M)aB>%Ui<i#2DRGQ^&ubdtgk0GX]$1VW!T(-sE<a."kKiPobkfJ]1A4hfsjVnq>J5#0cB
%aj+E,j61[Nf]@*oUi@Qda=N;fL=:hn4Jro/nk/Yd(h5!FcXQO/;Nh^Am!0nt#n!LDS]5omX#u'/)uCQ>f8@5;M=+]#n;q158GnE+
%A:7"3El_]b-@QZghG*2_.]414Z8,K%`#Ma"I-d4aFf$6DrJt9M6YCq%=X6LX9lj')Kj98H_eU/<O6O@#_@9i8=n3YDbBQD83>jqm
%X;eVg%nheA=9b=7!L(RITl!.i6&8P:.M40O9IA>$[pY`=R'ku8C=",cQ6Sc\[:*=A7PHRG-.a2oMar5`qe05F]Q%1%a6\+gXf<7K
%)<_e^ifFB@R-lIUO/(m&l/UTc`cMc,(-,dXn"@T]Zk@Wf`W^/q"0FO)[QXm8Fj3Ad0!]`\A4;;6c<XuNU62T8^85<^?&!9=CV6H0
%dl3db$eftG/:X%DU!.'RUj>HEE.S@fZj\+9o^%>uHRA(?=lH-I8t2lK]A0+Mg.0QL#-i<b0&s0s]noAJZlI<cL):Ka.UldQie[/5
%N@(sE1b*TO8XX)&6OT)^Rg^m*W'0jj4'>!]]0g"km2c(b7C[Q7Iq7O0c[9USbAVdl)VnSDD/s7nhJ"'cR1\?Kb]8LV<(.mA\I3DI
%%e%3l#4ij?7k\VE%nfHMM;nV.,1=o-nX3Hj/=5#lpeo-\A@;ONEP-/AnO99i/GdeY2uQU^n0eBaCic3%aiF/RW<;_DBmTF,4aBSR
%17)EPa[LINBKY/'XtOl/mQWbUSjtkGNm$hQ4W>l:9&VrlE0g#0kefXALj;P'IVV(5F^C;MNC-P12T4k4[+j(mL_*L>$s8grhHObN
%\\\1)@2`pM7Go0!2MXda(02Z9X5kr\6YIZ\BG.<1g$;!A<R)<>Z4CC`dYgYoA"W*Qd`>P9(m=6Q!fhBJ?(#R;Z8<*[`/eaPq6SSh
%LQ)-HoLHFjU[Q*!O3+mP%@2rmXHMF4Q?`0i1J@\?Vr5sHj,"T7iH',)_?fCkSW>uERWPH3fIh3E8Z'bYruaeB3QrPa,oq3%f-674
%71Yg!NH(m83t>hQWM_&)l3B$OK5&d[15IMEHj!q'S[Rt#QS\j6)BRImPXt]cYHV<W8B(gYL$+X_LuW+KDgP;a/8W;dbr7OB<,o=B
%o&^0k4K_H@GF1<rUmXMJ?'=nFJBLgq<>=(2ls5?=cP]JsC::QecK,^EE.Vd=pr\fa<&tdG<Z)YZ=^pKK`!TFm+H[+ZnE$8:(.iI_
%Gur@N,k^s%5Uq_'23F5>CgV2G_6I!f4M=*Zd'Y/D?%M.DgsT9f@;L2=`oGUe_'uNX[mN0k<otNFAJ"18Na\HOXHUHOa?Z'o012GD
%-Q#I!&+i-n,NFH:-T=:'n@3)eT'Rm`O9%PRHh'TPHP9&5]>@*6Ud=DU/t(!YlO\'sfFDA2XmYb/V8mE@6q%WoY\A_s'NG7UJnC-;
%GFkF#[[Dm)UMgnC6H73*="SSAQI$7N5d6-q`][5C:T,;H`]8=tkHd3%6d74>ItAm_a)]p_Y,R%!2M(\.e+%/REl=1DLbA%3&K-Dg
%>20iF"u0+T3Hl_\@Ws]1fE/s.,aD#]M1ZOg;8&T4R#c7=8,;YLBDe_:n^u*3ns2Fb,p0h5:Z$71pmB[LKG#Uo3`RUg(-*HI0#U$m
%X<4\:>EBq<q2[WJ:VA$m[`jk);Qp?X69&smJ<.GIO$4JC4thsZ7A!*ZTd/eo.(mVg;5rRjVj2HLOf/kW-b<?uh&sQe:V;1HDh"kC
%ggAM+$h"qe$\Nq%CmZU;>$nO1_3"Bjf_kd9]h@fAad!mT2_a-_2[8>32<+Rb<l9YD#]9`s);1<W!iO\>+]LlK'M3P`^#M4mjKBW\
%'W#CP=Yo!5GWYrqbKPd$\;t^WTpJ!Ws0(/N9B`FX+m`@(pEq-#s7KW$qIcu0@,V(J8,d9'8(Q-:!SYf!I3t?YCtSJF1#glaO'gC#
%>Yek#\)'>S:>ZlHI4cH9Cu"fr)"U7(:LtmW$frpnj6^hLSU3EDI9usP8'62>QT*Z2nI]_XXZ`k2NO4qVkXCC4:4+ZP19:!CLnr>>
%EIq\5\M`P%Smu6Pf3q;3T-ChmQsJ::,Hh73JZZiu$9]b!rd@1[[H`:A!KUUmDYkht2aR#^A,Nj?=1c[:lB\%)C_nlc[=&gI1-t*F
%cJ<g@(aG(Xj6pI_4R7<BFE+)jEUs4G3*5OZFUFK-&p;Em_tgC.V8*'&cMm(SiP^GR3m7#<=!,r:F?/FEX^pc^mna-hYlFb=:lT"t
%18qn")9"9@1c@"hZak;=>E3!'[UZ=AogGVG<XanIQq<[))G1B-]G89bcC'#Ci;!d#(Xr$t?[Za$C$UtFO*B,[m6'QS><]T#,l+?_
%\<EMAIL>^"-F@Y2#+Z*inf'))')m+42mQu+/G-@TdHc#3;$$i-&dolNe(QZiW-8P6--WKV&R!,Vr=9ur,KFQm,fWbua8`*-d
%4UV:e`#TM_F,ou%#Y,Vc+;VgKXiXk>$Xq)[B@Z-L@Nh\aLhrS2JQrInYPG)W07I6c#KE*an7"Ti@AKiMjrFC!b<e0)\*PqAMn7Ph
%#d/dt"7sai+0>WU($G"i%X)@t,63*s#;>]=4N=:_o]**,*tMq584G5e<^lV.fLo](El3;ZKW3h<#lpRlA89uE'@X+"f['FiXPIc%
%r,2>gCr=OHmNF:A!*V(%eDBa(&>7/(8iN&dYt'iQ2ukOhfQ?`oaImg3j0T/\FR79*a*ooQ!FI_md@YglIhp_o:ESnNf_]:Fn2O5<
%,<MNLG-80af.Orkp1t&a`r+o6cg377)3=`4Y;)/2<W'?d[T^GJ!BA-EAE9m=n?HmHk%Rd9E[)4"htq:U57O6UY0YD\5C_b`l)MLj
%9s;NeAt&H7m&[.1EoYM4d/fkYWOGBTVB=-/qqs[+qn=_\\5jT:M3ec`$**Oh6@21ERVM6eKsVK^Ye&E`U$qDjhrn^fk0JMQJ#WeN
%^XFDT!3Z/lgL]9'kj6j!7[DBB)Sh5OmOuT;#?51J-IZCq`YCH1k)*+-r5Lt?%&R?9)a$3!5WG4!5hC,X5!B&r0ug?CS@]p?7#AqJ
%)XD[QU&4Md]oD27hG&DYZg[.B5J2\Gho(TDe=X80*(5hX$,Ln:X,jTt+s2s/"@Ub,4>,pVhbYc&qO`j=%7J!%"98?j%nP!t^cWQF
%cP%SC(X5R2@bRZJOHCL<"AJN/.IkM](LkP<Qh0IO5OPYgIO%/In'D$(a1)pL]aO6fphUV\n,=BQU7_-&\Gb@_=p]6G9J6G)A?&_o
%Q[hOPG[[F;WrMG,`$6XX,PpR,)PfsW^]*Om+9".ts,=igs)70u55=GDmasI"r;<l"lcU;56[\A?kO3lnpY<%K^RtHL3`,h#s7n2;
%MYuJ7J,%:7J+:_NIe?nVZG&=L*1I7X["R%scS9LLKD`UFqQkZ2Ih(i!3UuY>ho,QB-[ct;Va`H,&3kNf`HD1HB:S\hec5/@S;UbP
%ec.K*1s=jIhu3L$MrB/Y&`mr!h(!&qLQ=9(!U:QJdMR32BC@>Wrp4Wh9(.g*Mgo-is7k3<>D!f/nE^)L<EP>G5iVbGQpT<u0Lp;M
%_-@#2n(doO+52i4@?$NFF!"c\QsR0#B";aWAFsC2qsU#+jBe\STDmYbIeeo:H2$o;IHM.?r27=.mcX%&r,9ENp>Kmi)KY=N;9]<n
%qgI6-9:j']VAt+ogj5qUmeqeP[eHniKX5s?&N`Y`g[?$[-[;Qnf4/IF7p>KRJt"bBFkk3bjgk3/E]7pN#KjUDc?49rZO*r/mIuS>
%'0K7:!3B])8]XcZr3H))DK8F&0Cm<GbC&ndP[chVXQL<S>Or:#-fb40%oqfE[1KQq&j1dGpWq*cO)?:^^;nKroYE<KNm1G#rUtSm
%rs>8'pgV(8O42[<kDAr=#s>-dL"5kOH61(5YeT@4NHLTZc9oN,I5:Ykre^2c02mW244'1WfA60rk^i063QaD_C+i:+`tN2kP%Q[Z
%QWP1iAp$T_e>M@,"Og=.O>H[Z'</?&]^tB"OQYTI+DUo2pQ=pia#1m%1Y*C>A)Da]pclS^FlmabP@pYn84L>.2oCAAgs#Tlo?5Bs
%!lApJ#R+O8)0\]3Lmn:Oh_Y8(qOs!SB+P-*@*ORZLWB*K,5Z?gE-=$h9A'5I+IQ9#A1)O^Z^&4,QPtc:Q(mP,2tmstqU#s[A.n2J
%k:d`)p6&4,s.Lgd5L,%NccW6b@,MXt,5_Ar92C\.m.VQ@n!S]Kgj>)pc%$n0^h8^<H11RQpclS^d48\G&-p[QOdF)42"(23J+A'!
%q*na+DuS:r;EYE0`n!>^hpMJrO-G/ogao3#[B``44Z(<Xm*3,,lHe[h2-F7P=:JnW[I=ts1]1%Rp;Zo3=A_Z2`^d'QDe=*`@hmX\
%kN:7+C\%<FB3OV3RY]ERn(RYLr1@Zi@6Wa&Xf1:??5>VKE%h/29`.(THllq6/j?.^Yf:;N_#-7i:U0PXdb?fpcmPW')G#329:OW$
%)q0HUa2-WdZR"j?YA:3oU*\+W?N99b5J]/kAEW:iSF&=;hX[f/<^uNK\TK*YH0[VkqQ-oB5i>KQqZC<hI6@@%ZgQI^E6_j%ED(OQ
%,q4JlHXX#GHlQmni5^sdG.30a4rl.]D86%@iW%ERPj"Ag.S$DR=J6gX<_GHB2sf^>>*&Sr5+@8a"4LZ#pWntU2kVBG-L'RNq)O1,
%m6O9t2#e&Ho@)*:kQR1mDc2:P@*.E>`>QYR't6Bp3X+7A.]^:4c=]<6!D)2SGS#iahgWoH5J]]-pQl.gh@('$00'M/7F'0EPkK1i
%@si7o[i\qk1Z4b_`Rk-'r1@SufMnM42Qj5Zf-5ucJ_lm:O((2XY+PYqAhAS=K*d+95"P<n0`I^DTaGd%3,Z+IDLMQYEq<nk>u"Lf
%HbWneZ?`M`bNoG1T8rpb4B)F6#uR#&mhtqOo=?:i=FT0)jV'X_5^b87(0O*56]8$6XS0d"5J\!fg"0PnI4sL,kTAsl[3Q&9qTr7*
%okf*Cg4)0]:[gng?e0%4L#!G5#,[XK.J-C-5$h;-0kkY+ZbgmH=cLTh1]1&<9IPF6e7>r',&emX:l^@3`PUYegtXo;q6RlhoV;Y6
%o2_KBrhk4>r)%'qQ'K,\hWOgN<&K]p=mA.PbKV[-o:VT-Hi88O=b#?q;<7LW@AC40jf6[U>fet>2L=&_9,Zn$1#]q>$c`A$0E9)a
%#\Or<]2`./>X-^-LsS!Y>lV.>J)'f`qk<PP2tXr]\$kk*r=D2cmeOL[p]C\Vmn7bP2Vmu^1S$enT4.]091KVPYb9U?*r9SEB0#ts
%kkgDomh>)mmIbM4m6>4XTDcRpc\q[.pcgI#5>mg?HX_ZsNT@hJe,9+hIst7BhdlH[_ikCgaF/CeMlPn7A[6oX42P5(W_;3?O0:dG
%Pt9`&lXM^M)i+IH%h8R8JmO33"-`Oj"@q?R2a&D`m+9"l#ld*?^\R\DJ,A&?IeCJT$C&^5mWZGDL%uYekPs&l*hKG3p:GhLja[6r
%DckWk%m`e^Ejm^^rI=k9YCH/uQYXV;H609QWrhYYM6q4q1Q9UGgZ#p:_5(4i5P\^&rHI^Zq=_G8Hi*DY^\lVHi48m^n,DRhkPaH+
%_#N0$?_9WuqRbYak1BXp4RsSk.K"rc&hK,09%I-[)$'V*W*/\&4@+uD+I]i"9&kN5SSNSQ0VZu;=>F@IJU!V&4=Xf1fdOq:^ZZLH
%_FN5+!-IcK>6UD3W5sN3eHcW7m!CH0T_!DZ5^;B-!'l*F/8@qVZ.3)A5QP>$\D\]N2])Q9h8[_\0/:q3!4nPCBloQTn<>,]SU`bo
%:l[+Z4Z\^WlKUQ&5^rkVMq0cbrS$\9isRBi>EA;ZOV6Ef_,mpZ9LSfa$b=ct492T>IcKX;j/'(C":"@n:NUXu,;P$JLQWs"`uU*f
%%)2ML"u'Z0fEBb?cOLmYO"g_q_>]Yi2Y)8#JEKOd',C3qT,ZZp:M%NGR6tm,N#bKnWcT-BKffFhasqI-h(0a(B)Ap@S*pJL5\euh
%!`2#-],IB7cVdL/i4pRM"cXo)`'%M0`l5=Mj_Uu88bE?FQ?b@\P"$iu6q59MMoTL3V"5KY;0OK.:o;0-'g-O06``jQfEps:RX0e0
%cL,`Vc*KTK]Z9W;NC>g"`jG'.K9EJ&]IKYGJe:s6[\^B=-DD+NS?r(+@%3St)7((a8r?lH3NL6gV3'cWBj>cE_-!4?J[#6IXn?+_
%!lVnTGi-J.]IG"SKQpgKAb^OtaW+`$0kBgn/-*&iSuYsNNZChN\TpK-`QE7!>E]7.MbdPCjKs7BOL,TUJ!hYXV$.]^,*!ZHUUb'#
%06,tk@i*YD%tSa%f2tIs&3$ITUb=)#"F0/.Je<hp&Y]?#=RM*k?_<^Kp'#tcpIh+Yh!nj"$ns;rC!k"TJu<X:/Y":O1YEjlCriLF
%K0Ydlfb(n3G3A9#Sq?d>L%Ff!/^b@UX'RiuSuYq6`AXb)=o%*i*VUkM+3VM0+=dsm_$b'p*]b47MH?1=Y@kjU^&opK4OK\+'!T0Y
%[2rOd>0OlX/0'nMKmnk)^p!O<Su^cD3GO;-X'B-dCo\sOm3NgI`\\Ys?#LVDbL]sfNU`Ecn4=+E@nZO'>5Xo4fF%<p/Op_;QREHG
%cNoH9Zt5HA;dW<M>RAG/L:3Pe<3%C%^`!`&@L-#DBZCVR0'm*=9WQ8UlnTo)@Tc[-r`Fm25K9/:OJ@TfXB-Hi8+,7&;D_-&U;p&!
%10i2Icgu!Id@#I@o1D&W7PTQcX/s8*.>(H\R\HG.PL6b1[SD#'QUWQ;3@re'??Np<\R44^A$7:0gZZ%h5.-X8alL6XNp"V>X+Zu$
%=X<]#p^E)%f@#pPQEfWU5/u2CP/J9W<DAo@0E4L"Jn)IN*uJMp#!O9-ZKec9O5BhVA=M*c=BG%Hb:1KWO1$"BL2rVA])^KSJ<u:B
%To=+*C(?O=Z6+-iGq;50hIYD&R@h$tPalhfi1o6l8)&8@IOnE"K'F5M@K/JEgc?i2qG"::L69&k]Q5dIm+0*B\XLm]Dm>4PJG*6V
%?MV-c9pbptR'edQ*(SQrH*bhOZ,9)L$/S:a4_4BK4.%6gT,i37D`[30I!-3c)=$=\??Ij0SbQs(B;&Ss!O?mho<2?LEsl=#))k^X
%IS%kY\0#S0hA7MsTh,!d?(P=t9r'mSaVCciQMF*`HuU&?!Sl&+7:`]qMCEF?gABA],:E2nEHJenB*IYd@5fSV4T]A^ndd5&>j8Qp
%Qp]eVmV;-Q5=ZWp@pEbCYpY19*'W/U*Prei6,?UTLY]KfB<6X9p3tQU8*uc>FBp'b>riPBr#RM$g/rKn<2+3_1IBi$,RlQ*9`uM@
%Q]J):B\jLbR?%q4l#gb2#QhW/\&"V@NnYt^^>g)Q#KL\NSY#ZQ?o\%4MkJ=kcbXK"r>`PN`#[bem2=b=:2%?o1_.fS=4@"LR/Gut
%O!ZL]rqN",j&]Z:Zf(\X@7!.YBlA)jaGn8+@\j?+r'c.XQAdnaDqN1Pn:Wbj7nD\8:s&Dt@L.]_rPgUGXWkbG-aO:eYSl!\m1GGj
%NTRZ?.<;ID_WKOj.'Mh-EhVO3'Fck&-_<j7*bWUo[@.eSSV'\Q*!B1\Vk6EYO\+5DL>RnFEMiBo;7gP5b%B[6$>qqhT2bQlV-LDS
%$%AJu9QYk<\-,Irj&W]Y:P6@e2\Qj@,esE22"uI"S\GBs'8<(2dR8/0E*LD7d@VJ.=O2c3ABp257B+P!G$I+#l2kHR06l*=^=<Lf
%ilDJlr:p.`pN;#8Fi<`u)"+H:k^0Wis5jcZeT0R?O(eOhDa_VFnRK=3OF<5^5V\(gm*t`X<`%_3G#bk,rJeJBB65-EB\!P@.Fam2
%NB,oSGLO\[I,OQE_1Qe%G$KF/IjZ73&?t>*IN.$9nubB"KpN$2Dm%Hnmn=<3V4+b?P?N1n_<EO7J<Qf-NipRc:WiPpG"aU%eW/Z'
%a>S#+Cb:`:&ou[H>c`usYHnfnPOk@gS_rLs#,:,n8B?V5BnjB$[p&G^_RKmmZ@^I/fP%=kLBfJ%8Y-h\Rf#L^GjHh,lJ-sgKXM5(
%^EpoDeX(,Z\="lenI4V6+7+5EfH7J#[&)&"!`r:N-?tIrJkc1>@fWm<$V$C\EORY7'>BT(.4^@)L&ioFI%&@eB`1!cn[3aZrYQZ+
%o)_5('Vk)^#mCC&_C3_dW)mB"$(@tQ:5bejFJ/H:oCU";A?Y#J@O!9Q?=QnpmF[Pr>YK_A0(R"l][>4U8MUc?(g3I(#fi2O"Ls$q
%6mjQu;VhJ6XjF'(a9eR%RE!DT7/I;(=56/?,-ZkALdne@d^[D]$0+_i_C5_F/>"M0a$<EMAIL>)lQ8E&DLI@q]_(-
%9LJ_S6p*3+,9o?M^@'28bf.dG!+q"+\STBthr+%e&*\_bl52Au&DWY1ku^P##PguQAZAT=UU%Y(Dd[`dXdldihtauRVstDQ]6(gG
%>SFUW=ja4j"\=3:.>Y7U_rH"FK2XQo4>d<d=>ja]W)$D"4W&79?'9>0mV7tfNi+*<*/sKlr@)CbN&[Nref-]Rd/>(Jn;)IU7?$Cb
%%W":@I^[+"NM%,C.j=-*BkO\I$W#.iIu]*,!$)q\)KfP!=rH7")0[qY]*)kVB.prW_UK!UCEi])=33.K`jEn[<=c3PU]LGg>MR<(
%V8Y-T+#UOo!>=Ib??QJn$aPDAk$?V2k'?=_b4bPE7?9?FdK.?$aP4cLib`p1%FG:F@qfk4lcB8bL.u^(3Q6rs?t?A+&[GIPm]C_k
%\`*%76'%FKZ*DZjJoMm)k.(^PH-)ZH^7FN_aa!Ps-=guJ*WW67$OZTrMVmMUf)1+kK-d9HesIQYk^C:(JKjHT:$)lMTGhA<*AQql
%I\W$qATsE%PaDh.O4h4h7S:QkY?teiMWTq!qQnWEi"po=qG^NIC8TUnr2Yh+#p=UGV!c"`FEMKmBN^rfQ@n*o4FV=NT$28#A::Yl
%V>J55UHKt"e9-ZC+e^01nK#i0g,"sa^uhr+]8fW/\ae;mVS7q=fZf0tF'IA+RKD,Obj!u8#eRNhP+lS+bss0?_q&&YS4YNq_5M,.
%&VdT\Hjf5Zf02op'Q2!m9cJNWTG;q5R35j*G(3!Ur;sC,0,nINT1I3Q%@e+1VXj6g,V%rU_Pg@.bW6N#gC_'&YW.7\ds(Cr"/!oH
%P.'8W\u,aHlt:u+M-E(qbkou2\m+0&Y7@!D5uMMt\A@t^<I:JcU2T%d*`]fcZQer8SZR=\ldDbWQC$#*Gd4@1gmoNdq".][O"[!\
%M0*WYmpTbi4[#JPN'@CuF00RHo"B:i6VA%*o2"cC`GYO<]1&k'gT;t5KKV-\6\B%#o'/5u!"&PC/J,dm<,E#NMkZc,=YU,VW)5OM
%"g*;i2od4=q4=6(3>sE(Q0iVM9[&eg>?b;ap/Iq\VjpQm5c-DbT.T@l]qMW(c5X=Er1N,;$QaeVlrS/mg$GEZ^(dE7fMc)iN$kX)
%NX\U;.&'0`T7MYSKn`K^O>a%_d$oX(>EMrcC+2L'b3W#bJ2:Yg_AeCbLjHA5ARNm/jJkM!<(`4O9Nr+#bS_?"nrk9N9WQr*/Rf=t
%?Ds5F`aYZ$R:-_*RY(goJ;PQ/NBf*V\kp*)K/dd!cMQbDIpM`Xa[kDsR*Y$V*#++GA6_&o/4o3R>_Z#DU\3V`r;BO:)K*'A3!+`T
%o4"ehb?(;F5ei]=\s!&8.0:jlY0l"T+labT!=eL+!U^n`4:qRc6g',/`G.4cZFX[3i!<AC!D5G2X_3]p$M&:kr#5t7L?pd]]M,qq
%2g.11N4&6QSK;Ts@[]o.kZ%-<:k>&)p\75>E[0QV4%qtLDt9Dd6@CG6;\nAT#%;W]Mcbh=h?i8F[$hW]@1#poKP2hG\)C;(d4Cf_
%lg@+GDF2B_'UH3e?ruJt-nbJYPUhB5'@,sb"(ms?C=oKfl+FP]LOVO?"-RK$Tk2:mEuklM%[OS+=Cb<Tk7jO<r8Ssh2;Q<ieC6IA
%(Qa0,FiFdGO6PPmY+XiSk!X$a8fCTbm1-@pC(A,LVVE[>fr$_>Z@';.:a59IFWPe,:@9bsR4R6[U<55R#h>BDWX$\O()L+/BbI#c
%o&,OriV,?n5Ja;a`;3Vb!MAR"\NP^I#0unmKunQ+P>3N[^2I*\(XuHR8c:q$!O+J"=h*PT:h]<CX0Sf<_GNM5=sTPXPID&([IFZu
%^<85rB]0m#<XSL:X2H&ao93*+W7st%5:Jet18UrWm*)3J4N;aVoPh/tC@h8iH&P/]B_+PJTP$`46cg+h%7l+C#<.nHLkW3a<"B\V
%L_\SgH)G`&P_LB'['pQ(>N)?6Uc;EBA4+HU<86D)G1Q&n/[u4Z!uQ+@b6DEf?0<cVpZB@[dNn):[+qkjLF]jr]4+#+REE2%cd)hO
%WtIYGHo:g_1)"MDQ8WI&q,1<.Yq^j$7DU1%$2Bal@OR5Tk]orqe\*,F@OZC'LNC5l3m8D>A?il6@#Bc:ZGmFh8Z&25+".B9lGjW@
%B?]A?GJ5d#bE?%o2A+FKcEU$A>P`sZZf.,)!N6F(<D*?dhL\[0aM1*r1Hmn7)pkb5f7u?"9@4BM_k\Z'W./F-]*-)k\2ma$)n4T]
%h5,l2YVpq"faN^mJ<;]@m4%crk12X,pGIO,ZD^;^6=8KYMdImIa@F5j6tp/533ck'a\Bd]NGKK1$JN\u"[5r?4e;YqZ->N9:V0Rc
%?5=$FnXKk'@2WN3TVAd]Nj:3Ca-RX81OsE=]41ud@`E$IllB'lDQEoONX"V\Z?F$3o*<IL=rDJ$@jt6qn;a0Y3^[qQb,L'XoM4qr
%^6C]l;KCS;(Qs>E$(UIpUj._AN`V0GC<V8tN>\k[5$rIY3qTY#Wci?!]9is[+\=()BuTA?c<5UW(.:%8D"26%=hKiL9#4Fd"Y-9f
%[Rh0[GD)D&#U0R2mS[:G(-[F\FURQ%*Rr0Rj^fkrH5Q#5X*6"=J;u\boNP-U*6*XG[7;(fV77/j^93g'g'C3I4dJUd4G'm8q&lq/
%G$N8I&B=(r)fF;n\clA5SIN@fmL,OO.Ah<%\`,#I^u(^B3dXu:OXdGY2si&>G.C=PND,;P)!O!GCg&$YVC7RRbBGEoU\c,+Zba9^
%q\Y:l8[tm(!>`JXr,[G+fsb?C.H<IJG1eiqQa;=Nq,Jut`N^UT--b>1r"Y/93e&+C+SUu@BDPVqgfiCQVG]@?m?BK&^<I/+bmqiJ
%hS[hQ$T;/FDA:i6_Wm:=^](oS^O4rj#T#N$duIM<4@(LFe"G`$milI4`$o-*ICE%*dVGA=%ld$p#2@?$\.#XlOf31lWPJZhZ/5ia
%!S[oj]/P]$8PoqQ<$)o63B(BM8n2i9-TtR.Y4Gk#*k&hne+g.p<0FV:<'-kT9(<sM/0dk,:;lb+B.rgu5pFIbHFISL@X^6<-(V?&
%"-<[d:bcVJKs.N52-*u9M#18:Nqjt@;"rmN:6]6ObkT`qjn)F=qd7t8"?Z'uNXbf"aM.B]mY]c&<tXtd-75k]K_egirFN0?T-Or'
%Gu'DL-Rr$D5)`,.\Pmj'3Oufhn[cCDi40Eh5bF^IGd'UV'[FGQk1++bEsW?T93D`foH[WI=0R_;1SoPU1\og-Ib$eq^6)\;m"r#o
%g-CgSMX3X9A[c(@$Qar4YtXR17;2ND!]LN[0+6gV-CaFq](mYj.]KJ#E1T?/3MRRrD81d_Y07:^#j(TNP$sY]NQ/>!ZO^hM;X+]\
%*tVP(l,&.O0dh#=g'.G@9%G3#4"p.9_Slia*;/j_KJ/#AfqtVZ^anDWON$%1eM3HMmXa3Q6'#GI*i`-Jo<31Xm(8G3[=r*fCO"Rt
%Jjd+U0dfJ<<dkek7p>kQXI0:>TU$q6]P%W*Z(J:._,K$%4[=RE(V^r:-\rmCe8gQoHm9<Ep3,TZE?+]c@2b*berue>5Z#`k7<Mq4
%%&=$uV1@a<a_TV>Jqpr,>]K`mR8,K7MZUhOaf<3c\Z+04='-AoYq"Rd-$jH]!>d)\guVcds36\\%?^DGILSWRV.1$T=/Ke(VjA&o
%!K_2Hi<7Ho^f%IME/^ZTJ&;P;BoP(A?oc#nP/d:4na'fPpO7B3hZ1LeSkUZQ_T`P<F"O!TBZU76X12-&;q0nQ%L<uPe1j.;llr&g
%6s'Y'Q_Tr-"-ikt2Iu0:DagC:h*[@54Q_5g(AWfqMD"Te+Qs6UL7;pf(Ms$r7\#2nT4>UUhNij[22'/bj"q+Q[Mp2P=mJE:\UEc8
%T;$L;DhZ>M&9JTH$0br=,2_LP#B-`8"hU",67PYK0F@[V%+Ki)(PW`^`;12@E2"p/l\8k`AD>ba4o9Q-_&KV7>Xa_@R2Ibl!1747
%gmO6rRt>,B)lY'CChfF3o`NCZ=@,^6Z;AG$,uR'&/5JDCX-!,"MePq^`r^c]C8/eS6./L>*^&#0=27btfTuDFOcZZ,D-?+SpS0W+
%Q1\b'7SPSSm0,#"(:hJ?%6&fMn6=C!d%Gg[PqoA]S3p3sU!g,>4B<-%3b_n%H<RAZ"-p[(!]M@)>Bg86i_@E((Tlt@+pI=WJFk6d
%Iiu>dVAOk]BM^%<[tMD0PN_<kO^(rX&nZ["PdmX\T<HNUGccq"D3Sg+A*q7N,SE_^]`[WgMrWK*G2#/d"P_*`S)4-R1A<2V$YGDm
%9tI'.fPQ9I*JB*(4W8/dCdo705YLjK5Wm7r?-IY1g9_;II2'U;]cg8Le=P@L&LX#a.q(?"+DM$kdCV,I9RssH</E!NXd^BAX5GU0
%>hW7.$[rMp^;(B;9'C;\e6Pn?6@NBu\`*b#]>..BhSm8M,;=5[qVQ;pKrZF^lMN'iWU-&TM(r8NfTuBm*^DrE/5&@dPMLR"^=@UX
%k&N_\#VHh^"+X&dUafbC+rr@9\:iX*\i^Cq=4;sE5fkVb5^Q`IFB2cgRt2B/b9bmB";J=u-D3^<\NU]RKX%9BVYh>FQ]W6uc:etk
%A9^n*+<Q,UZ&upo@,bakN_jh;91H_#F.<\>K(5D*]tCTKrR;lX->N_kFHd,5m=CQ"KdMq]U_0JS"0P]rr<DLN.IYEjrWh4D@]j-u
%`]W=NLpFuQV:&]0/^DNh-nP&4j=na9bsmG%%`,6-&LkiB4mi)-g0;9fDO"uRcZ:&+F"8rQ-fdXE1!q[&*jqfP18BM"_^K)-ARRMX
%:J)DDd9]CSdIYT,\k-C&;KQr9Y`Vrb)SDnOZDY.ge-`2LRh`5p6BrUR4tI-I,&`9b0*8MSU65:h1>ujeap$>@?"A-[g/]Mh_^[T$
%.@tZ*ooZ,l!ajU_Y_6n[HL=OHeB]=\pE[?@p8O"OaIUk).0$TOk_3hs.ab<Z3n@Dj9)T[3/-I+FDJ\V>!!\TIR`_KO'H*N0(7a%&
%@u%;S'(HSJLGZG%DNj.S>CdiYdVb+UEOc8HBEDRXWuBliCTJg/e0gi(O49fp.-qn]O&G*"Jtt[a'>h(GfNF&p7II;8VPuSm4'^!W
%:]K,qAd"3AJdZ[)7*H6jJ%!Z#/8,f67+GN7!@!aJ;Ee7#p\?s)ahD7HJ*e(NG(d/f,t4/eYdW?)WnZMgA[A>QE@*`]/-b&Wp5"6W
%D&X]VQF8.Bcn6ajCm7A,dkDbJ;,X-=j&c=!E+[EFE>b=c3I7rl0-:5E_=$Ch:hI@cD&KKQi@V::onFDU0-M$^m$7T7]5_V/D.DJO
%S-.i3/M3@Zput]uMamdukVA%Y'Lp!XY^tO\5_^ZBHj*-7nt%T,d_12NE/$(^V'U2DmNQ2bC>]P02]4[G+%#)Z@XUL4Q>8MBTpdSm
%!J4n?`\$PnksY"s'M7E0jG]#QBZVSP;%F?eT00uLAkgqPnlnXf9d8M)="4Hp\ig%e:N,8VVAOLW,kiq8`h+LRg8Z1PSZC"uH@uX:
%0L':^+PQ=^`>DlKEt6/f20#"3+UIkNQjS[Cja8.=9BCgL=t3a5.nTROPp;+*RJr)r<2X)V$Fr34O\n]Z;+e>tHnfUti?Q]RK?RGI
%m8\?(5#^Och^gkL3U2GPK9sAZF%Bs;RuP?=jf]`"GRf\1`[&,rCNB.$iNO3U`!^cF2+h6]g*9)VrXH:;<,_G&8_gMj:UU'7@[#4M
%EV;uH*,Co.M?M9T*mO%3&cq/-q]7D)1h0_IV6q%.EM,;r3lgZn^e]$a63a]a<V<Kcg(_6JT&*mnC$/#$[U>m!2tO;mp8qs@A0#<n
%.1ktaOqqDKLBud=]9mj`nD4]=O@ue;[7e@UWQM=RMiHfas(@b$^\#/rc,:M$Pie"+SSLGX%c%*jm\jXQgcWr/<B?4EXP(<r&pV:b
%36NLL;:35`2^M&7kN6\^)*6MVcu1JXRSb=oS,YcqVXFY*.=*3,kpeV1?fQ//1U9\Ic<@Jd3R]gJScI`eatL^rS\"OgkZSa?05q_q
%+csI<FqY\tJ/;!r]@,0F;,DVT+U7OWE)+sN-'*9)2's'S`AjYrCo=kPr+M7$E7?H2-_Q%M?t<@$Ke9#RF39V&Z;tkUQ72L7AdiR?
%eg^?4_@iAcfe,'Y6!MH4jB;7;<(gaCmS*6M=u,5%$f@eZV3$[IH7<uj4UBkE*!n4?fNMfr-7^06l9n_UHk/:FT1EGD(4C:Y$B-*3
%<b2dZ+UV'%gdtPM'maVME,$r[l!e$i)9h%i;F[XAESs<8m_gE4dU\r(orhuSPg1/dNdPrGH)P`NljF`iVrQJd-(1?W71T[)L'!+i
%=7O1Y26TOL#8(pUboA,>;X4U^iA;eb];8.<C*`Oai#Y?EL.T@cDo]9NKsq[@Yu&,%:n,9_UEgWGFn=Gln,ah+7L3j#&sl3&Z3)Z^
%h<-^0(m]Wg?th0t:^C;0iPUtm9'GB.mBamd[Xa\m6-O*(BNLi9^4nBDkgA6=["-7s.UrcrhN.4N?VII)kA69XF+#?P749G\N"JTK
%8g!M"rM@khdop_1C_W`R]L2`KWJ24t+g!o703'IjT_h/rS5@7<,ikoc7@9/_?*##lkfWl9k.s`,+a&ctTlr3ZP.Qk.q$NRL?6iPX
%@h$pBOPga/X",SROH,0pGDV6Sl84J8SP)oqnb6&SHe9]76=2%'G4S*cBY-A'PnJ4j!:_J,[J3I?X\eYJ8#@clY5LP,$4AQS30,&j
%Z5#YW\QMdUecR2ZFuT^8^P._LW>QR"P23q+c*'3350C-=6BBJC7=YG>m;4YL3s,G\Z^P!2:0oVe+3Uot!^"BqN=+5NVAput`LtG,
%(r2WCdfL4Lc]P=No4<DQ:Y%7$;(jAMe2*c6@Ut6beC=/gh^/3Ge"i^$et'<[o@Qc/9kE,1%RmSoDKgWHLBGu[#L!$/2d6%rV'qB7
%5);N<g%u+T2":"i/Za89^mc1[0/?;5?Q`R"#AuEhW]E'.>qn(Dd/T?\oS6qQOb5WoIP(?_AGut9^_,f;[c'"bKNg'A*6nGOJN\CX
%g0%,O\ku]p1gUFtWgKm3g9/0/'Id?8LK"L)84D2eTM!h4/_G6.kdR+l?Z)-CDr#=Sh1/)`IP-q7D_Y=ads),0cZ&l;OMhOU(-u$H
%mL-=WBtk@H'3Q>H%&S\&]Q$!pN-X]B/5l$;fW#3]lpNIW?50n284=_Ypa5&1a,hMR++2/[,$2Vh'hBSeXm>C'@5mB3![9DQotl+2
%Id'"(6AMBVpoJ0X)1i\r.uYaJ:*(SP@k'>p_1(cA-?GM)oM1Ge<H5Fo_WV;<AWVr\HB?ie$Gq8,+s)lmRFM->Z.YV#"/8G)`QTSk
%fY6C4B&,-0=R)Qs?6rM:&tfE\'^FW<VEj%F\gT&>"eHqt&ctG$e1$1gWZDR_C6j=2Ja!]n,eC0NrROGGVYj!cNP7').4M1N/5P==
%W_cqGWiJT6;BD74"'1NK>58M'H?QgTMD.qtbi+I36u.j?'o!$SD>!ulF@qn%NXnfnR+@4!<!O-<K8Lb\B6Cp<^2,"e`!Nj>HL*8"
%PM3(u)dc?=8ZU$',[Fk/k72O-)T5ijVBH7Bdd=n+-@,fEQH\_#o!68DL\i\e68og>4B98;^o"e^GUOG`hBY_'k*Jbo_KW;u.1]46
%<t<$TThc[JP-X2BNdKCkEP"Wi>ip)F$3'&,'m*]:VYd40NCG7h#0;inHN84cO<rcn,/Sl'U8rkS'#aC7h>HYgO1mP_Eb2&r/Sp)V
%lFs&sL<gh::N,827&!5npKBl"-FaMD9b$PF8hc$O._+g5#f?Hl!p`m1qMGEp/@<VRN?#]*XUunDj.4@l_!?D02q`:<<X$^$gAmUW
%i(#)YNG5/l(P74qmfY,SF<nC8K'feClc/R/.@M<BAAoJ1]>#m/Q_;,qYV++ji7!(Oo9.LX*KCH-H-*RXi%K(Jc%ckZ)FM1FiUY"_
%a>:Ibhu/_l?=I/8?dD!1;uDTeR&q3]Rgk]kCfJ\5YV_UhZ!";D<SIrrrFV*q!J2$h%NAHKm:%WOh/FEM1tHXo?Bi[O5"Ik_D2sO+
%1tXV[_EGaor=Ls?Pg!PSrBB"oX7<7F80A\rb'n'Rn3*GN-L&-1Spfge@#[M98^T[VZcfA#8%?)9n<g9q3-HhJ!H1dfg=u$%:8Dka
%/;Oi`LB8YMo.UeBXn!5I38GG:8OTWM=9[XMDS(O<?FVq3"3Le(F,kP(H-7kkl):`,`a'#Ddu[+g(E>BUL8V,r$uh_u@W3UX;ms;A
%TVV4$i[L?lLrBS-Mm1le*Z`P5E=[&`A67=0\N':VpDHV".0P2A,t`cDgcf)8J8kLF\s[XeqEuPSS\&!iO=gBNH5o)4NDMA:Rt5hB
%LmC[P?sEa<GSsQ:!!uAqR,Pjk<I8^BaGHQX"1LC;P_Rp9q*EXH:;i:<AMf7X2EY2AY8n_obQT`-/Iu]>(:_@'!F+N;XJhX??pt]n
%b<Zr`P>pZ#/e0=p@Tf/?U*K3&bO>ea3&3O]eIm`t67]hQ=6;ul%he8'\N2S#.Tre0AMb!*^EkP"FqGRhRr0PR_DaSd,QBZ[E)e;T
%G_b@=!DFi#-B6af$#o:\$]+sQod3(U6]JnBK43D&^.`1M\eo`S:[n.+TJM?a,_;:(c.Oc%?ZFuVc*H"_:a.1I^*teThoS$Il>o#+
%+V"T#jd%+`KS2<,$k&l4$g@r$3R`+^10\b4jL,Ljd>L;d0?m(amqua$=b3.^^gf^8.$QpZ],JQ7RKc3`@534D1OPukaJDjJ%%u)d
%WB_^AR08:)>>b?&!'8nK"9t*u(XkHd%IAKTT=Ysa\j9*6kr"`$faeqAP#9_jV^tM>@UZJfj!cKMO=[c[9db,XH89fFdu(!'.>9)g
%&0VQ[!W(G0=JqWd:8Hk?!8o2feSgr!!TafkhO9C8]><WaE;j]bZ8NRA?mis$7b6R@nPUV+V`Cq&0:d:nkl8h5s8/F:p[d;@p6YgT
%GONborh'2Y?iK&os7eOfq=T7:jh(!\p:L(V^&RuuIe2AKroWCYrPrj5rVF;.mPdE[^J-ch7uZumDgqYs?Nj+%H%t5Sh"UgEmqdH$
%h'oQG+'AO0Vghu9r/5s"nq[7<IeX-OF8j(,d6@JdN\fnJ^\G9fphfs%YMT9=YCH.NItG>u^J=?lf-LAmJfF4\O^`C!1[9DLVo_c1
%WmB<m:Cm!pim8m&O#TOI&<**G7Qf7d.oi&GpKs&c0WQ\bRbXBZ6!<:$4#Zu-FWBnh'C!Zmm&+Wc4Rn_VM=Ag1YY6_g.@lm@%Ik\\
%Q+"gqKT<r_`3Lkpa(Q8f2qBUA&q'7"A<D1ALJtd,BQN_0d+'QNFO#o]7iL'&mm'et0d,gR-kLUa<9l.[F"j-l\YZNH(/Ptrb,R3I
%,!8,M,7D%nH/bq&m5X_Na/d3/SCs[,!=57Caa8l5>eWk7')T[?GZbkQF'fE?\0;Ps+rN^GEq"qC1qHSRK5fuD]FNR,mQp*nU$<eG
%'ZaHKdBG`c?,a?9j;Q4SJFI#)'Vtu5LaU7JZK8POmia;"iiWjG4YrMaPGjGqJ9f;SEX!7BI+DUs0Z5r&CK8>]NBfH1hnfbB9GGH[
%ls!/Za>sZc#i_6[8Oo1M5s>XZpeng8]0r_;U!>*Ie7Rr\(g4#(dB=@"h&V#SfH^FI<M1]1WjM)[p2-8*>eP*D4F]=cF1m^BZ">oj
%Q'l)e7f2"d#b\j!#S@.^+1u'9l$!6EpT"gC[7V,$D*.k_42-?K-iktJ(#K"B_(t`#PS"+%QgTObVR^5M"i5-O7dl^&@<aMl;Y<FN
%6*bK7dZY*ZQUQ]E,g<>4Y$h\h=&[SZa^hj,\oW<.)>+UjI]QBih8miG`%MI$EG*Uq!$PPBF=A)sY+6g2_!_mNMEmKs'RilA(*qfr
%qie+I=%C>Z8\(cE\Q^0c=Lh@M86?YC%(8chBjAVKn0&!6M<mXqcpH!T:[p%\AZ#tB!!H9HhiO9t+BenEKRJ1h6S<+6J79El2)9q4
%C]rA@['RhC$!+W6,Luh5c\jW30`a'97t@H):&Au=^u%!3eZBN4`k=@m#a%qW'BSVU%L='[-?]5/#fG?33ZJYElJ_#m.@V\*C6bgM
%@_EE/!?:8?DR%a10+'5@5m=kjBpVf)7e$0!HnP'r&:KGhT;SN[Ik*\rItVLQ9d,$5(iNWobYXoK7WXO[4&s7N=:HcQQrWll*bCrA
%,KLr0CDb#YbG(GeE@jP!3DD9[/FS,m"$uk'N-b*7/h=dB=X?%mfngc!aXH,7bQq]BoiJm+o*S74:Z]'gqCa$&pre&W5NHhedUs.4
%T18:eT'!JPpj<pBV)G>F<b+i1d)#/mAC)h/".->sVV+rPA+6P9W7!G\iol"S<4dWEjb;/7eY-K+O@<EdSYZ@XLtZ1.;35F8mXPqH
%@@U?_g0qe/OI,46,]p:H=:`o2a@#]R3bH&W5ugG`6)Tl28$,0+;9>/=R+@J*@mnbqlBb1ka\C<i&1S!3qZXd>>Ks*8q,;XJ@NT^G
%2$(MN$+8,]-s9ajO:Sl*&`32n1Y"^qF\cUke>\[-;2jLrA;8)lWB),Kgf4)_a'M`_GJ:1sGITC>qqr/uRqR7ZmMebc*IG[IU\I4#
%[htEZ;[Ou'Y`4">/B$Hj3DU>-/^2i\CslAj&%._Q>Id:Y_%td1?a,2-Gi>E[`Kf[i_9WCL>`lT\mJpI@f`W(gIPX!:(sg1iNn)-9
%/,7&7<t9"J:jTX=lh\WrMUWS7ShUBTR!uBW3fK1U>*Z#(-9M>hDs2F2(nK.^]NAi8OE^Pb`,DaubZ,nG"Onqufn-;96_4oS,k6YQ
%oi<ciG'isT+>[I0KX`Pb_O"US3\#_RMC]-Y$qkZrr3I%*cD`or];Wltls*jqUAX*'FV?XZ39ErQG:s^DD$W#?2I"Q9+>5q.UY#D)
%2(0(mnd*gK&Y$.TI$17ObcJ;cmKs]=8;j]uGkdA1R\D]lqU&48mP$(=?36[0\Zt:/,.#\,*J"erPDQ`IK,Eg<g!#JjXB1L2=+I?I
%)bK3cHFoG5Q;R/QN'/U"7M]BgUMHo=kk`\l<C=.=X'AAo3]gaq="fM4?^@/If-M-Mc8_8ZdTQ(W(TSj<lc\KujDrMgJ>TTEh;ek$
%5XE\(8D=,/a'Aj<&Ui,q71$uX5oDCD1feW"Io:)p,uqb+P'"$M-55Qk0S!D,5$V6W!L:G$(sg>8\=3V@?qbFEB.B@+[apIJ#)nEk
%8L:iI9]bKS<'9oO73t0&1'-lI!3pLOBj^#i:E<*!b@9Ad2^O#'_FttUD7EcFlJ3Ou'[imCZJ0*7AJ%Te>5TtAB3Y0BHm<*`=AMNj
%J]4Ho-bAbqP,e^]2]6'&;2\&;OC%B+9B?H9ea7?H'/$_>I6>J;[,t8QW,J!2-.Cg)CIi`$Cs4Q1_%$&sE(0_fKlb]QW:U(57cLYb
%1JUI"fLA:J6Y!>5(U__*D[k!J.0c;TJ(MM*I&KdgGWqWQM)W`4E#/mV\R(L6+3hISeB[8M:#RTI/f2'gWY?J88R;87lbP`'CMi@"
%0CF#X`b5Cc@ep)e)^U,;FO+8`PPo:FqeJouh@RDWm$cdu!H/NA]XT9,JT234LB=QW,,`p7%S&]U&L"di7Z!5VI*-K_85e0",M5nt
%r(%I.KH-7u)uc\gV0finChlBs[^Q<RBOUe&Ds@ssVkbY;A1aO\P?sOQ1()4qK[#_k?um!e,tZI+P,!hTUT'mR<q_bS+Z7/rUi\=k
%_D[]WD=hS8J\5!o\RIOb>:^?)3$>i,_ecjq<tf8C*d1LK_rkUud1k#?koGj6^R2p-12l`P"cCbLN9`?RCpBB;,_4Nd`YB5p,@=[b
%BPq'"SGB]>]<H/kTRp?P5rbBm.Xm9B?;2>^O"5:US2-rI''j*=#jfq9lm`-A3raTjO;4luC\oGfQdC\\3]J/DCrIhOY3X@'i/%3g
%KIF(^725>+#$&i0N(1p!'/4\5Y,kN;6s2OpB7qDU?H/E'*]SRoZ6hj6V\.RB=5j)?D-/3cgeQQIERXZfqe29YEF16\[`.XGh.ADD
%W->a6nhA+u`,:VO?"$6`NFXV@`^9[1@iY&HBtS]7@gBdmqr0$iJ.F+a2+LqbD./)PobtgXAAIb36?=u/_"r?_B&g+iE.T-S#RCKS
%e".,[aID;oCNn$6;i>fo8gWe-Vll:_SEYK:B`F8c%I5gMDSc:mbDk3AbJ_+MNQ@XN]Y2EXpoQP[2q<J3$lFD,P^7ol*>ncRh*m\5
%<6?b`%g,WL\O?TL^IcNp1iu<N$_8NQ1<,o_I_;(:aqL_h?aVV1Zh:@2@CN2^;;QuV/S\70KQ9-o;cIlp<TgZoYA%cqg'[B`TX(.I
%XVPH36pB[fVO%XF92[T+9W$IN9Jg]5C`F6PA>j7E#;4#Dn1?26H0)*1N)$?m=(<lJ[urk!e"!7OXg&>Jd0Q53^iP)l&8+kG6]XYA
%3b'PQBl(FD]kQ6"1.)K.A\t4u8UJfQV0apG],1'%\*U'6@aX0>Q[nR][2+k21A&F^cias=4DtH5fa.u_@b&:>,V'b=F2@OE*?9RE
%HZ$-b;bJ74muii5=/B:pn,F;\I(s+S;X"R0$VWSQl]u6];6ArC]pL'JWk2F,3A;ffa;D!kDcGmFEB"\^HOX<r/hjp-`?Hp!C;o-6
%j)it9'VI!#60U&Y#^9Qr+m_0dhM'dY_.-iiiQ>:V,TpWUCm_.493)E6J$T&gpc%N;$;-V8C(cJ,4?n%sNfNtoSnXN^'btpe70VC?
%Q>5)>VUto5#X^:;p@\d$n.M[0bh)&^,%"FE$ZAJi73qjGN*)nGIPK)p]"Hj-]nnB+7Eo@\6'9-2:/\rSgfM)haZ/'1N`D8g.d;+n
%jcS1hT=i+jKs+oJWdU-,j1==5-L9'b]`qd=>3==lj"b7Qq'O"<+/bisXrZXt_8epZcM4,3<.NGO1]Vu,`_m7lQWiFU<<!.bdZL\0
%bW5INT5t)ZYi1id_\SmhNTfcS%N9Gc1qg-NoU9+S6$gnVcnV?eY`.E>L+MaL78&ndK%mE"GJbjBO9]4_]T>@["sPFY5n,pLG>-.2
%[:YNlRBFJU5mf4WBf8P]@C@p`hF^;'D.seX\8H*:dL<ieeP-L-k^7I#+8Hke&#B2(Z-_g21c`13Pb"pF5[Wc-.[F!KG.R%\mDNX5
%MKV=6eQ#hgHHNOP;cWGEG47bJ@3Fb@=h*d8nB4"Hqj"h0_;F?@;l+g8PhV6LKG-=@@mVKFb2^)$ALL-,nlcfpgoONA`="A&+ZYZ;
%;jdEudIK+f'F$HW!E#`.T'M,uOGarV\ScY;9FK8\8T&<4X%'M3P4(>\IYBD7/5u`8#D(?Z7kKDRFF8[(\r,&_U%!=pQL_G@!c[=l
%.g7/n35BDo<T8ZfW)>?JX*_UmPD'MOG7n-cE`,sJf2KOCE/=boV^j^d!2hu]_pUI8T*:EHqgb=M"WCc#=sPQi/*"c\4R9tBMNX@2
%[GAe/>eGEgO"%Y@%@b9*AH/8lW+)%!Qc]aJ7O!h'UqYCB]_sK0OPqNuAi>mMU@<EMAIL>`W6'tsZ[>#ZG!3ekq:$.Z[@170'
%O#D#gF,cEI["U4HCQ*BoZ6%E5Q29S#A`<DI`I_ikLP5HTLrFn\ZVLQPCus/L^1X2)1-gbqhL$6(!9%??Zb;`;%V.CQcLEkgb<d9r
%ge]s8U\7r3@kd<FL.D\2*eEc&Lr!a@fE)m@^:lP/"XP7)WJrn*m/R6Z;XSls6Et?N$'H<6c@s]QE+%##1@:ZP),%Yj?>.n8P0^&n
%hF\d>nCq-Y!#b4soP_elfIf.5=C-)HUJPZ$jBU5[KRU]`O+Z`LESOKl_hlmL*"9[#Ec=I*@F<ZPopW'DnP1C'<jt<rQf@#4]'DXQ
%`ElBj']'Gd6IW$/'=hE&%9.#<DE%m5,"lK^^hVra(cLMZ"&dXORPIljl+J,;Um3"\9N-HnAB%Z0*3g?TJ?n4P\5*^[!?HRijIc[0
%\hWR0aEfV[:"<Mi0/+J%iGb&^AJehoQMJB1'B^9TTlNnI,n#p.b2?(TdLurj"X+33iN@Ek/((LPQtg1R&(&3/WW\taOf7GFGpYpE
%g6L2!!P?KI2RWhdO6M)Ggi!XDg<pVQKq>K7c6p*\X5Rk_kf"WB?SrUd?q.XX-'YZ&laWDFj*#EOSl1Tc6JjP1[j2X*-)),2'62\%
%)@KPufqKiY',)N88F]%8gBHV$<)][OdXk\I&a>.k'[+Lf.KYQ;l+:dC%:N%Z$BB>V_R#"eq@nc@R_Q>TIFM>*d@9uKYg?;rBbbq<
%U[/=K;eD-E9.!H$UQREkc+`lH*gZVcd&!U?.iK_),u'O:hk[3j(<`*<,3R`$cQ4`\g>^ZXRmckWE+F3S6MdEbs+MBnFT@K,/.9V(
%Uh_h=Ws*5KU2h\cN+TidB/FLgT7VK>4/UHt$a4&S6*kcl#Y*624a]FZg'0W!?M+=[VFAA/h$Sk3*+0bb'e"T#h_P#Jf[4!n?96.K
%"CisofI/2%V2X/f&aj0R7;.tZ*d$aJXDeF`[&u7ZpADTlAag*Rs7EJ=RG:@_);3g-#l<3MPhK:tG:sR6el_joHr@(TjHrM9;H"]N
%>Z.=>Tp5mOpKC*?'TiIiILr5]_d_VLG*etN=c%i@T+BMUdDhAt#V/)q-0'6g_7BC_R$),<^'HK-kqb'`[Z+[2^c-C3HcS<JFG8c`
%=dpXT/lNqj:kPYV,=\:rAV;n>%Q$0c_p5JnN#Ghh1m7G^[\6RIe'dG?+(iX99h\Rflf@8NgeMi3-]j`:!$Tn&pMcsAfr:p_-+A<!
%iR$\YHLq-)@s_cchK9XQ_8ZpFO*_e@kRnY9#T&@"$7Ed@RDAc(l+NCWm>`<7GrMZ`W%B,H@DbkRLMskN>*>0?k@tf<7W4u_R@fn#
%Y$=s;H.5:)a&JZ).&!,!rZ0f^GE.U$I1)1o<le'c.GF=_)F'$=Ut)C='cefEaas)>V`UE.jNZK!+>"cs/-J_%+2km#A1<ib(3g5)
%!;G2S5"W@@c!B`5IeSl5WL??Jl;mY%Ze4`UQm7[D?E3\Sn-1q1eEKhCK789)Q5b6Ge'Kr"GLGq"E=edP=%t>bAN@JTqQT[p*:@*<
%(,;NK43-o=4hHk^heKS[be_3(L:g\9e%qHU.QfF*D-"aapEm?9NG?3hilW%+SOYrZ/HtaP^]"D&@7&ho9,'.7`@$]2<mMSMPS@s+
%pu=O>(X<e6s,=YpA>@.ujK'KA7_J=:7,%mY6iK2'a!nMr&\jD(/[<qPISP.^c2k@=@c;3fk&@X@lD>B?U##(CFHMZe@"7G\2S4jA
%5`FP$0d9?3N=(Aj[b>Lt0)%&lJ%Z5r$)>S&*18^_7S*A;K@O=<e1fnOGoMUKI/eYr2M"Lq.ll-+rOF'F0/J"UM,FE5Voln$+_52u
%^f_8_9Y=o,9h5e_#bSfjZD#W?"=8sb(N'O*#AI$X)`h59"t#:#8K\2GOJn!1<Tch$[)Vp#]G%8WKf(DY*1]PQ,.pq((dHQ=YsHNV
%5/kn]>e8kd/Zs^aE@;If-ji`"`5R@e#+'FO#]PLIqOElh)c\U-V'Ni!KVU*BG$\74ESUVNkhX3*/kHcVD^]?cpQrs^g6A)'ZJ@:I
%PVkqn"L$6D+kMd\P#KDfcmLD8gOZ:/E4;oX3A\5mgW-4UC()g<-d::j3VDY/c=.:'lQ,3s)!p1pL2<j)JseWJ!K2Id9$s\:q,=7C
%`TR?.ntZC3NNK@"k5Cd'O]GE:\%7KY!i"r=7Zi[2\R7+r\($L-SI@h,-m>%UP>Sbm^]C6?JlasMPro%P422fFL*!oQKI4,th-(\^
%0qlO[c98+[OHW!,OLOR[HDk2RXK>-`G0,_?ccWVIa1/7c2+2onN3GVpG>4.F!t7e'<(e7ik0/03bG(lg>)BsAVB?)BnE/74+khuB
%4GdAhQ&tffJ^IUrGA"7B!:76E#mfdX\<[OMRNL&p7Sn\]J/NBJ81\J)L,r+rfN45dUPr\U"Ym%YkZ/6m,;9\g"+:EmBiqhSW'.!7
%)_"+?KWF[m9*:tc6Qeq9_\2Y2'1,DfNkm=r"4o:1W*AHV#YLj_k:VH^#>+fkEApM&7.r-&5V+<U\4/jR'7Z9W(=jrV+^ds.P'YST
%;#Ma!bI3QrInoc?9WmkCK1`7,k"r@goPFgms"<IH>&\_BTh.<R:L^OV8U>ZIck&XQQt%&>`t\Jt"riE!;5dN3905n23i_Hj<R38h
%Kr\'!^_+Wo@]68kKPjL7e.))n^0s718E;0/h(NXQ?'/8+VlR:iMmAjcIEgLJc%CT<>6@uqH"et=gB=4\>`DR3Y*lMpT`7tp&k7fJ
%!\:j#^V[6S[tf9@ComU/g0$(am.1c_$&t*HKl=[gneHC=b-,0B8%qV74k\#k+%_^H<j#Jk#KJ"@iM^XK^4Eb5&)qf2,'?QQl1DCD
%@[Aj$f4;34L1N."Nj(']##QRmXYBJ.HS?lU7p%"-ie%i]o>sL/g0?$t4oB3EF`!U3+KF8TL323(6Q<aa&>S,!%&f;$.Huc8=f?2^
%V_K=)R&cI8?Z.A+.b_<U:RIoMl;do'n:pq9-2+>KQ^qKLY`Z`Z9QT#&I)e7C,+5Ua=/>a1+nt@V^_2V``$i53#=>71OEBtDnN/AF
%T],Tps%lh]YnsNf'r74toL<5%BXL;kp47Gn82shrYXNF2qqHrGr;/sg,-U;q&$XN0W$+''!r]#^'>V+E(4/gfYYAiMc+)Y65]eN<
%1rraupVLbj"3EcCGH'@EZ48q(eDKZ'=<SLj\39[UHRi.)gd[T1A.FIKT4VWHLYCJ4p84i&hNth\5dI39!Ht_*^#P2Xpm^b*ar\9_
%3C<,\U$\=#:r6olq)][mPil^-S`&!2'-3j%ctE*b.HUqgW6^_AVPr>,cpJCgID+tOTQ2Y%IM-Th58@*tFBi21+=BfiMX6PQ&E*P:
%e^/&O3>B!qHl3=e(8OC1I!46dUIlGN$*N]!2$fGK'=Z9h;b=5k,Zt,IC];sja;"]ZDR_ZNM3;P'JA3ed\=l!ME=-I!qg,+Rq,'7o
%+aRg3Q@Ic0hXuV7r*"b>7uD4jc=e`[aR`ID$!_?[ikpoH94&4,8od8fY&=e&e%^f[J;L`oXMem=BAekD`!EJ!gr43f]Jos"je#t"
%`n5.od4.nZH4*D,a&?pZE1BgY?3ge<*A)$BYUZ,p5u?Oo?H2BaH,"PW@Pu+X9NF6XU;/g0s#d8tA-j*Q9HhJdZZIMBf1M369J04:
%:/SJ<A$Wsa"YWoHM#cA\4b0q4ifK:kBLED=pc-qa9$k*=dQo"-gXX48,hJ#HB81a-XWi@]*N'+.9\T@]+D9h>P$sG-C):JDKR9cQ
%ki2^-Dr(NC/8h"t.ldeKIi7+c$eCc.:'a>5nbfq:<g+$1No.<0+XK6l"KQ/;E'"8FNGiaV51GaX`?cWO4]r[pC+l?P(_5<&$uR>:
%na3KRo**-2D*/&7TY8Z0l\F]N?+V>/PYV!0CF0#F.$4Ef#,Ho>kDu?&b4'!B-jlaSY-];MMuGGFdf)V[0g>MnJPG3464a%0/u67\
%L%I_m:t26QI/op*+Va]8P#cjlGnI9hWNLGg7^Iju+Z'gA=!]4&Y=u.P::R_p'U`!gl\.[F<YsqbC8!alWDj;sK_Lt!W"umUNmlDg
%ajg*)VH1E.";-4FS=7r1^6p;?&$Uh8>#1k^=pCne0POU/Hi5'!D?.Kbjt#&8A#G/s!4&$r.DX2L_$r\oF<oD9GWg[QH7!hiZ?>9`
%M"9A)M*jY3V23rn)QU>YRq7hO$B-OYh[i[#/,$mK*c\Dg5&mhG2AIrJhf,!14]/;JU=C&b,@Zqh"H:6t0]WP9)8E.BoFd&q?(`6L
%k>lT?CbDB#cZh:'lbH&KbI8(d/llH6K=k8B(WliV;,-l9nOS+kH/D^WO-bVHXd<BKV+l>#FhBl.gGYpW4S?k7:i6*lc`p5IM.+Q7
%QUjbEj_S_ZN%t+LRY[i8d1'R<`5.^2,$$:0.9d31)ohH0G3:/c*Y0q=qsmrGa;dc?27+TDlt-Y+j6i6llF;-k8]9O"9K/7V"kG9>
%!X,$6/A[rZMDNcZKdF,dWJJeB[9\3c'j4GJ4`i9%[O8ng`SabM"Y7Ud,/+*q]j]]V"7APA@Y->l>YB.q^[:i'S7[dU9/VAuGI#!-
%in*@tX&l=5R$-(ZeJ]&OOpJs@8D2E&ZjB9Tb,.a80gN:o'RL!Y[4mRVrAsd'@3kg,)c7&:TW=Fhas0;Y4X.\7i'=6B=XU,Nb3A-\
%O??@<9&A"([Hg\K8M3-WO>kM:+]gtb<4]Ug1qhH2Tt4>9pcNAY)+7#jD$WT3`)+?]J(ku/I13([,u##DS-uMTB$jmAa^,$34YO;Q
%ab:aPk$>ZZ/BNf")IM2L;3Ekr$0LL';-5@#6pPh>\>](BY@mN9leN2sYHL!r<t$eW+k]eTQD6MXa=-ZrL%VB`Au.7n'J,*=HQ>ED
%^d8pGL!-%CQ(jjMN<2JH?J+j;qMp)AZ,;sjoAk/Ac_,@]0D>9H3orNk1Vtt-4`5%(cd=G_,`PM+(Bk+6QcXUhGC"E=?`*UIT`M(>
%D%Xi]o@mo7X_hE75ie1)8YHsWF<g)UQg"@(VPP88Nati<ZbUgmF#EY3,:p%gCro`WeC_sTL^2c;^?cP6?@tG.BQX%*jAH&#@1Yg2
%A?!>F%P,@n"%Fsf7."+n"-"L@2:<(@T/ilIrL&]XX=D`Iqb!?5=&qntB"?A=EBsqaLHd2f^IP]i/a!?8(O8)V[,P/=$:9g^O@&o8
%fEBqe[m1W"+DbXk^b#t+3Dqe0=@M6hiPWst1U7rF2`u4Ml,[WsFpr0*5&HYR)8Up%Zn!H>@dh!2))p2GCn](d^\sa`r5VGBr3Mtl
%V#C&E8qiTY(,CCDY1t"QkGa`PlVQhBbjpGZ*b,Sh7b$Vu#jM2#h)%tLP,%`@Fiu>;0JWqCM9HN2V(KW"2qbC;?(rg1i[ji`d\t-i
%iS;o2Koo!_)?EFJrKZ&c-!^L8=6oB=YE5i,,MBQDfP3MLd^Da6qTF=%Po@lp!"_#3G6rIBm0KXoG[c>p\hOGL#6a9'<D!P.D?5lW
%,U=r'NjgmtWt:%?%g9L:63?Q?3[K?#Xci'W_:-b<E.X<ReZ`&l)-.'-5[c>6r1p!FV1_u/]-:'L0-=Ue0Do,.Mcp)1l7T1YnVd&;
%BVS?05EVM/g)=nin($bsf)@hm-EIR2e()hhJ4m5<<$q2Bb?ZU%'.GD@opd>Ga^f4Nf??Pmg_$Uf_+s^-oX[@7Tc1R>]bphknS[qC
%m'MY5]X2Sbk$l?_^k9b&=Fd;ND<=sDrCZKe/atHEgi2kg!K<Zdk&p&+<[F\(KalQ4>`Q_F;4?fQ[q7a4Mn]C*Z,W'cNt9t%8.J@5
%I+,1jnAB6i'Q7'F*]![WoHQ=+r_BY`$I2P@ZVo)<_f)4oIO>!@HhCjnTjap+Tb@B[p"Z@5#Pg?5&@bj%7GM^FBiSqF(33n_EL=5>
%[4;fi])?Dc9+VZn#3htpJ(4@t_bM+)foNo@0'k'9cd4_9d<N?BLW6WOiejFaf$sLA]A-B0n[oYs&`I-Pm?/1_F>*V[6D<GWo[pDK
%]"uhC0a""(Oplkf;6"(Eb(?Y:@(**_P=2['Mq)^UjpbVYNO^Y10m]"V:K(C5!F]oE(tQ]MmDGLC7'UpfSBJoQ@%GZ8.G[Pf>_%;q
%i"%Nt)?p>5UU.jm6k7T*A_BFJOnfi6&nUqQ/Bu)b<1#RndacEIX3l+_k?Vj*JB"=c6GfU.UW?31V:\;Beu]hm^8NO3BnFlu3>pL,
%/u+k'0YkjeXV\'BU(`k_=.[2b8H8dN;Dk19[`ou@HV"!jDZD7O640IVl5$jS(aT8Qgn5l-5-T;J6((_!C]kH_4DQumnJ6IZ&c9-\
%7l+1JG*5g?Er,Le1YJIi'L[VhFG1qu)@`-l1"g%FgPA>Ud\JS!`O+;m!X;>k11E9+i8=k,%GVXO2R-):h+@q&V?@eXX9umn^'f6k
%>Q=s,7]jHh0@'tDn@&"U\m$m9#cJgce(J6GWKNp:c2/_`\MH/`eP$#h;2oL(!/i\l>Et:4N)`d\N5O*!!8o1M=FSf(,CK-ks-]/%
%l;(=b_5?]TTs4/*q)X#h\@NT8BM&8nmsT!_VDh^`@V7AAo9Jh!XY5u^Mdj<UCfC!:eP2Q*aYE6rmZXE*;UsRa$/7miH>aD@@-^Rb
%QO5H,?s>-?raka&GtFj4'>=h1klIMK&U*lI9+=Vs&^cdugLc3WkfHXJKM*Y@*13.hUb*K:2L7H3(c+*0`#8Z?Ug780YZD.h1/JZE
%*RH\BIWC2Z4""'dm1jahZ2G2E-/XF0>9TFjZK?il9Bs7TVUORDj_I8[pGVGTkQIecEY"R#M/>p!>#<<X`fW5<PC5oEKHS@S/X58$
%[OCj<_p/"'gWK+3=p!U2]n7VoOr4<M\h1JOHsJVd/a8\rDG,8CS9S[Nj>/RZD'BKOgjUk9)rqB1f7tc/`F1&Ds*^/]2Fp(#,;]dI
%ccC$1-bPj)UgHV.b^)</!$r[JgTr?*]"P"q3#Y!b3rkp"=H_DCbH]\KK>Tt3QRTZ"<$.V@n0HYreBJqNJXqV&W1^-YRm[<3Q4aZ1
%>^SQi0XLMC7e:JLD#k.a`YeCD7H"?b:,7L5i]>3DEc];_GX'ke^(`MU;>WWtUKbI\HeGHUjOnt4[ILIS<4ld5HHmMY]dEKr?2T;3
%EtMf<2@Pk)/d564_OGnsNYRi*eVsq#mW'%_Z1gjZ&nM?JJ[/2)Xkqm'1e&'9MF@mTe>oTA&)"l@=n9)hnB5E]$Fs9G+H5T_]#Q`t
%s(3t(6,/iqoB?(X!YI:iQ-'-b4Cg'YD"C8%f76Mr^nH5sp9m+<UMea9</Ng0P!V#[r0-2=R7[-t2qV3^T84[%jaUrCAfjK"b-Uul
%K#!Ij/M3,?"T5<aO@AiGA93A_+FY*J7b`\dZ8'0A[U9M["esdWgRuq7djW<_Usjb&3BEp*+@e<p18/94S\7$=9(3,++gLPU(etuh
%52Hm?aNIrr%/4&[/b2:mYd41YF`,+LF`>2GqBnHq&?(H],&L+s1,PW0moE46,tSsoVmi#rE]"h%IA8t$9%#MBi*f2P/loqm=K0^]
%UF890QaEh?#EKM:*J!A\[X4b&DiJH,.G`km>J0lBq1m\6lKB?XkehS`PL>iD3FmhH+d846P&O?;&!N?*-V.$p.<GKR2@Pd+TJpBF
%j^,)>W!Cf#qBgBV_+*RGjPPeZhh??$l62SB@LU[de&[O'<u.q1-$SD:*LOf6ChKSXcNq?))WP&<`i!E`Cjbj:-t0<!EE;!dU\:&!
%*TaUHkEg39CeDL+d"o*/?0t=<+,$'573)agF#qD%*s@F5GQ9Tb9+&knn9C<R,KUKR-`CEAhni=o"oE.t/;2V2qq:BCkgUE@$%sj!
%Q&@Ti%1aUAjP/#nJo0Xl#ADLi&*8ju]jIa(@/h_!qX7m)BJBSVp6,;PAsg2T;2V2Pqgf]AG14_854TmA1GEV*YE2^85m4uh)]44"
%(;!/iE=$+]cC^GV!R+P,s$OrWDchClr#GO>Rr-TCTFI8k3B]nMWFm9_JM+,IM(JDF%jURT>g5K%@@0,CWhIQe3&ut1-,o#uPVstV
%jHD=C83&r*A3VQm>sHrC2o0!IY3G\Z``[3%j1<!9*Y3^#FMiPHDKR7D(/funhBsb1MH@^dgEC!'f2AVa^8.Xhj<M"G1<1$6i&Fg+
%c&*r.U$hgEiR<tb>8J*PXdgp5+t[79dg';Z-);?$MA[a*&W)")RF@W.!L=S2!2aL7GbB02g3:$3qU_p2_i?mI^6TBZg9\Z&:2Zt[
%Ob>6'@fS<@k&$eM),jeQjZ[ZdW?cN!Ls>]NqoBRPK\bI\N[8E5a>\Xk>!+%;RK;mpMh!PEo?b3CQGQJC/Fs4i;d#ucr/87?0MW!f
%_bjfk+cIXjkEqgRX)HN*J_R:b=V*/eK<^7li]&U=&jg?@@(Bc&E/Tec1M@*kfd%%8^RjG.YZ3&@kU@XkoDhmf^gl7o[Fcnuh']fE
%cF=kLZNQ'=!R,loff8@#:eW%A3!aCmrMI2!U2h8-*B1>5#72k1&]*/!&XI"6PU;[",cV]V^EdCb&i]?@6LY1L\+ial;n+u,QWjNh
%kIf02U;4hoY=6\]a8HWl8*V0_RJaEtQVhnaD7ftYMfICDK`G+Cbq8e7W_V:d?q9#;\?68-UACOrgWbmeS3gFfNd(5o8\>`*-a.l<
%liY4r*,LY4;5d8O>]%7"Rq`1u,It)W3O,`Ld(+glXDI#FS,!r<IOLLRSah5Nr]r<D;qr2DqF)WV',"DY@as[;\,ea[>tD3]A1udW
%,YF_9$;tVhG9lc)<Dr"&^n>F+SEL_P`ib=HOn[,trK+L"p`pVF(&%$^$ll'IV%+3"991k_7#91OOHhcc/i%QQJ]M&s;+sl-4mo16
%]Y^$si[sM?)'2BLrfsoCYrTTHW<qs%><I?%+Fb@-Ri*Z;=Bp03:a'%OX<WMjURgnpo7R(K;VgY[/J8"d3r&;kT0Il&"e2J;%FFkT
%_<CX!pE7`;>s=E"W/R^+L.4'3Hu]cdipLg^@`NLflUk5$-BZ-9B,8jX+ja:AEjH&FIOOQ+D"p99G11d>QB9?J*h$V!QhgBA0\"u=
%MjYU_'PGIqZHkL=_5N!hj_Je/bX7Gd.="M%;J[=fQk6TpSo)RYn1P7Z/OP"2[&f(GPc\ipeb(&`b"9]4s*_@Y4mZC?F!t9"Xh!)1
%@:mVrX<ifC]WVCB_PpRXK+:8`1\/@^h\T;/AJAVWSq.HE3s&1\g=*KT?T$19YHmU2dT`T]9C=M.#VAS[_*:r$+4qgULLCX'Kr^ie
%Z^Atipbq!(K*]+sJQA8nC-68Xp^#-+$_I,`I2NH]6aLBBHVH5f?93BUY<2qemK&*^qCr(,TMre"_F>PXYK7BJ=DGYRJV?-3NuW\<
%QF$i^6q[(SQ?qlmKeCp'A'-k$hiQ/[WEFU:@u=?(VC;IpWMs/T3#(#!"Q/81Y^tG=M8frY[(\*,hXhCa396S(kSlNQ-C&Bu!]]Si
%d:a,s)-h>qq[S3P@bdCeBtQdpV$[<9B!S)d'Led?QUkD2DV+\NU*Ci*1KPD,&BdDNKVOJ$Xp$Zd"i[<[?A9&;1eMVHalc/''&YkF
%%Q"-%TfTGf3B]XM"1.(AgHf:[h[tU4+BD%Har#$G\l>MV_`K"D\]H?-M-*G$*%cjuQ'qak/[S'.$0P*)-'D+p#+1Y8`UY[\B"2Vd
%#Kp_SUXs>s;%AQ4C*RIWW;r*X\qs%"-aAF]`(R'5(O?gS/%2X'4"5?7ZbV%HE]-ZUSt]NQ>=Pg@>`9q]d/(FtAtmhY0nT3_Xh:R(
%)HmU.=1IQ<rFJTo,)%G:&O(h&'Wm.P%u_j+D8pq9NON<P=kJ@_n28SKAX.&t@E]f%;#G"Ih^EC(TVgFk%q9o/Hi+4_E,n3YJuD*g
%6HX\T3YB>,#'E0u"XW1=JIo[1CFg+,<RCLPK<r^gc."bG,rl/?GsihK)?gd[ll'^o4Ho's+g&2/ce,ZKJKHFm.29QG:ll#jQ_]%h
%LlbF8TD<1,@4YEBM^W2;A-@eNkaq,9<g%&)Bt#W`"ZIt6?D_lt@rnA!<=5sAX@ItV]&O#uZPDB`N$AuRa-gE?Zg,E[p?elJDOdH7
%XD#FiedNG-HCK'$REXgD<1ha1=*kn\ik*7mh*3bdWU+[Fig6t&B6&hBJOd!6*8HQ^po_k6.A=3Tf%=5TENis3b7RcmQr!@D^N*to
%8P)4L'0JH?S-3T/Lh7'YL'%!4X`rX1@aa<M[6SS8252;.^4ka#JuV@.UBR+!U$YI+iBRuoJg>:^.*A\j9mHr\7X&#:U>G_S`KE>T
%M8>VN/r>T?&3.*ul23,so\KV/"4Uk"j0Aar9=lHhkIu58FebDF\D/eU@1_;@/d-1NiSl-H#D&)X7e35$\C6K+iN`T7-%7+tJ=YNS
%<5Ks0A3>Nb;LN%*f;/p*QS3J!Du&Nq&.&5d8KF&J5Z0]t?0HVG7)$b!bqQNS$7TI:EFVVkdg3IC7f\a1OYn:'<2`MmN/nl?VT/tE
%8Kl[`]L^W"Bij7`NUg6+4\Mf5=q+%KR<,@,h]q3tVUO!nZSPHO]`^f>fsV&p)Nt*nh`%fREpZcU:OUKfF^p[Aq[,2A6PV:\rYU04
%eTqL).4LYE6`]3gJc9#bJl9#BNj#akb7ohQ72e)e]bp$iVZb&KOj'9J)6<iG3T`A/GWllBb205Q#5_@i)q9hTD?Ju0EIC.sI&Y<&
%Vs#cO"SfnDKD%?#O$#j"9j5q<-6"X<0(kA.(s_D"GHKlAR-^Q\TmbgVe)%@KV(dA*n,:)8(N[&57f$&"l$r'P5I@6hW`$A'&3lrX
%n362EY<BM27`Hp_*>mmdPE+T[$CCD%\`f+[G'3)e.*N!W.g;,CP85s6G<5t<C_]`B(5o98%B)95@g[C4>_5WBmcWd[UW5oHc'S5l
%W-rUqFgY;f.!U29'&@pS,>][TS4%/M+&dM4N)MehmiX5F>Jr<_gakn'qcE;iaTL2c-9X^'>,QM^,OlD7biSon3sTq??,!3hC+RRR
%I=FHJ.ebOML2"l8)Sp!T6-Id?]H\!0#NNJO3UCnVYdaLN6/f2JkeTGg8m_ir66T]YgH+ReDN('=LeO\KGE=mS4bh0N%AO+)P1uh9
%bA0[OfYmk_moGFiB\V&acOa)L"NGF=hVhnP@_5c=k[=i($Y-eWEjWr4Tf9.?0B,[ZZ&?B/G-,k"W`F=W&'<\OR!'=!s.YGMoPKQV
%C:@Bt\0Pfpe;Kq;o.tp9]TYu!2XW?["n.7_KON@EV3e\]5pkr(Ef/'bW+e9&7MkN0HSPn-].^ZP]q0tA?R.6"M@nK\l:Fb,=$D"g
%$p`lV_ELJC&+7NYNMROc@NnQunH<:d)54(.#'TX1>onS9[mihhgBJ1*(bIB4aTDHN5#%7p`P]:!#S,`Vl1gZXB*4N[b,NgVoj%ts
%/o4n:C7Q$)dtn3eS0N#FD?Uo!+#]RYVhtDo"+Y$H%)X$sTt1qg3RNap$dD`QCSr:Z.]Yc9<$D`Y+'%<.A#Nm9gc._;Ke@S6[Mg-?
%15c_N96q%#Rh8)=fM*/3@h3)?PBCZV_FQ`U[s%uFkJH*<]a6nkY4lq(m^KGjTFU@T-12s&=NXY&16%O7"u7[?Je:npid1)5C\#Zn
%QKK4Q&e>#3.rl0%&>$b/Si,uk=dJF&<:5gMm>o'9&nSqd&Dl7Eg0JuQMp!O_)>dqA@#]UiT3n)H:n_cWId6Z9Q\5k8C<qu\P0UHY
%<B8g04e=&G'cT*NOCI/._1\%T/`IB\l:&1EO9c[@aZ^rJq%U5t]L'mpG$%0.He]3jCm7hI"q]rdRTCsKpFFZ&.Xni<:=EeW,<Na1
%#c8hs]gpAQ72g)B>NDeVWF/-SfQ"OYO_h['&*dq>#_Kb$_DNkV^gB_rN*RGlPX<++[PhGo<C1pAFXVAtiU-E8@W<m<qd"DmURa,H
%ihj0D@^T6F3`h/b+H;a8ragh=YsVbA3Sdl6HKh]/U+/1+;h_;bJ%:=dSIO<!.=0mU2L5tWPZukkA;l?=5-o^QPYVthcto$h&B)G0
%B7;@1(oPS&poRRnbG/J$XcgA&CP7i:']6h/K";H7(/$+[`aW0:(A(bt`e#r>.g_<'cs^E@_0LEF-5H<cXpf66oGW?+kVk-q44E#m
%k7rk>8-Ur@8L?)8H0J`M*FIOO*RnX`BOnQ0KH,+08lA(@VM;Aj.0LCU<2j.d&a1r(Zd.03L3-\E<^"1aCWe;9bGmL1L-<S([9+,]
%<kR-C>*8%/RJg%7/8@dEZFrfL,3ebam$]`_GBEp9>JZ$LfZMa;F<_5#)!Qc;AMbTZ@;!,><LTlk0TZQ0c!m_<?<=j/>$=M'k$.LW
%\X5tMbP7?N;lCenWk_YR--SY+KbhMdT>(_E/%XV/,%%&MNM]aQ3XNM=;$""$4#2]uSNn6!)dT<<pZ7me&PZ8(9?3N-IcG6ir-0.D
%W-r`b7iG'dL%MluB2;7*4]nq9'YtWlgG1/6DF@tgNgaJ'F8k51\.HB929sb[aT,42!H7M:>_j5O@LPEW<m'5bf4BtM9k^MqCk%3E
%-LL@u"t@aF?jV^E/\:p)=`,+bDIBR-FrRgQp,q!\9l2G->^Q9GVL++(df^I`:U/O>Mog,<0'm>uVS0AS=L9\;RW+/0fXsFm]+AWt
%f073f\6i_tO"kIM4p+WH>&U>3qeJ5KW1,$mH^f!lE@+;-*_m`)Pf=P0lD*l'F>`?@#'2A1W]dHl1oA9+%Mbq-@tLe#K+ZNM?[]4N
%OG&,\RDmY;r63,tj8EMVI5tZ]0I-^q+)kNCLH2UA+4fMMM]OEXV*-Xeh&Zu`UmLWD!;+FZI)rE@0@CnM"PS#mn'RYmPOX?Ck&P6&
%+iu=]^`gqd@7rN##uk#an+Q$Yd8&q<d_b@E<Z.Pb\k$+A1k2tM>`%YK$JeM2`=;l0''&GtDU(66C%WGJ\'<:_(s,,2&UKD_JnWL(
%i=X^5*QEMeo,=I860#j+e*L_1#UeGPRD]U@,+MruXV_"#^\VSpB"T,3qKb&BIc/8t^9)--?;;OB[K\^r/MdWo8m"RM!M#e8UTn)B
%1T[(DKq7?=CJ9*]=H4T(j)Ve^:G3mBBJdlYSmLfhT]aLMrnJ^t;EeFp;G1j9?$F[l6^;@TM6O>?,,+<8RUH,l@8sQ:N$B$gW',!C
%/(AC%)Jd/0c44"p=aQF]jP,d3Bp\=Kp3H&7=V[O*8#CTO]7WYtLR_!fN(PS9GWS#uQU25LjKXl1d0YdmgIS1Q)dO0@;bP(6G2A4;
%Xd-C$pBLf3.+'$im;6p\HPa&UE_B[i[lYKrW,Y>QEuN$J`&Beb"q[s?B.j!'bVq+`'70_<XA`npD]ecjgOj.Q`MJ!4`qHu1^6l;=
%*HL-?a[=YgTojl=8C7G4?GTX?[P9P_[;B,E(!`/:&U#<77?Z?90:!A4NT.PO/RhN`!qhp5)eLeKoViU@;d#,h,!7(/`4%_NAN?>u
%[Hr=2N)0Dd[&qNjVr!S*No"l78mAB9g6Xfk],<b?m(7u!DrJQRBY4G[dKBn*9]HhX$0XUZ?UB,I1koTfm=gc<[*KVnZ&*!q,`K1B
%!5rO608uRP\#,^r4Z$?(Q>]57hD4o@peSd0f)j'C2"i3LG[Kk1MeQE0>"E5[GOU0/NI.Z)HI[Cm?7AMclg(0mYWff5oU:#\PXA^,
%PE=N)Ga.<k6U+EWi@</>k#2D]0UbfXYbHEtaFL*"GrJn[*L`40?$<ON7#Np]!@qV-I7Gte2saZV>O5-mZ:TRP2?@Gu%-42MCZZGF
%NH.]L6JR02Q:B%ed(W,2E#bmNBsNIIW)nV_1POQ39&f17-=3pi^.%]d?Y^)Q]LR1P<pNc4Gh'fF=#L.:PZk:cWR76UEc7Z-3FecT
%m@E:*B@o`06SkJJN$8c.F>\G#B+,,e<\%kKUG1$?c0dBpf])]Ei"NQflQ[F(bNtUq#FLEDTqO59</IcKc=Tgpf&md#%^Ii)EqA0#
%5gLQ"FOSA;6-'P2-<Mk6$Keq0/`4PR_M?U#<I3mt:7)X&5m;)pG3GtVrR1G5,hY;!\f<ea+pBhDHgTQYYNkI',`rsQ'^%7h-Z<ZA
%qli3q(aPl6nXKq`XaE/o?cpe!.g4rge9HlLmJUP`mQKA9*C8_bmOt@WR1g=="C6(,Su0\<##\M[PRsKoMD$S\j;:<RG;9f?WohuI
%Mp8_0\#=:R`eT0j-'MgafBmipIpl73.Jq/5[)-M)-iB?gPg`9h$6,hu`W-X-OF<FA0gY9Y%;If4h7omOl>8XNn8!P$0)H5SXAk`9
%Qa'JpJA&Z-BmWkcM0atZ$JH#Qj3a5I6aW4s3K^#XTf8Qj/QL*RQsjMa)cO@i>ADf.Z[T0hDqO*j7*r)6FnC'mPeNNgADX^VOtr0g
%YFU7VYR;+4@hIh<&A1lK_ug^^4YIBQ$(H]3l<-'/EL@^F8$G)7/\Xn'$1I<gm^%HbWi%V@)\$Q(q$rm<op'?;^E[8H#L`&*SF#Mp
%Utpt3=d:Q;g0Sc78c@kjA-cB:'ZXdc-XXg)GL;k=$oql-(4r=`r4Osb;;noa-#`9r[!9uI>=9TiecHVLBUh@D7m!DWZHF'#XAMWE
%_jZ&9djb8o<q"]QUO*I"&86TC+*Isj_lbmR?-LmA"o=6.NjsOp)o+:nDYaN[D*!3[[sP(_F.0)03q'AiA/pCJrB@QsY`mmI7`d&3
%17]hFm55_J^e!221P"P%BZ=-Hmb6?E2fJYI2XtFq3:<?*Y[[t,pbr+X#u1r6iuSi@I:Ol`Wo-c3!m]^9MUEWS9;e7KiS%`$H]XAL
%>'sZ=s1aC&H6W@s?Ht6qj/Z$oI56]L%9+81qDTD)gHNPVb'U'L]eGXUH&HPa,gH.6*C6N)/.e_f@GGV2`2]deAj*+r:TQ:S1F*S[
%0"mG:CiUbP;(9@g*=fp:7o7]U3E2&d(K`4"9B:@IOn`8&QfZ>X(Tk6/]>1$^RD#J;`Li4%T)k21UOC<erkLL"rdMk.Jog&@pO%9o
%89DRD$$g9m2R4XlrO&[TYd&f+$9&6iglIoJ$qI)6CglBJVP5q)eAS'5P'P&\'JVA%<:#]D*4]n[c1>M"/C%,c@e/5!<d*/n^hfF'
%$+B4=aFTG.@Y8<4!C0AAF`UlZ"]3Ged_^?jr.+,i7V2_kh6j:hYu0I:#"d$)o0ldE/`3Eu(iqsLC%c?+8c>']eK/XQqpB!qZnT@n
%]E@r1`V`7@SBn(7$'RAAN3L7Cd)]]t?JeL?1U14@7ON4SJ#6RkAcF=8[.aahjWuql?0qQ>:nc^!F%63ZjRPsAUs#<;mpiT*DYaJe
%*J+t#*!U(aSWt6H[BM7D/5uT9%'<+(>]'AB&)C6iVYO]kN,\0^)F"n`=L.+*O'db]!6-Puj@@3j?UsnLX869`%'Z,)Wu&p%86flD
%+;HVFQCpOQ2!quqI[\o*f5/qn6AH5$JYoR$=JjFi2":/-=@h+C_rtEAN,[N2@;"K$Inabjcd<54&tK',e3+Lgft;`&QoXMWJ/5#V
%?L6uscrS?]kNpRUWtE8bVi8&,aS!(MqA<4PK/(tNldB"EPCp9?$\EY__h9@*Cq#u=I?>MX?J:KY2J/>GDmi/7I:=#R#U>$t1l\sT
%q]i_gpGB%WWQY7(AakEKg$4E&NcU>oAk-J9:b<%R5[@</FO^Z,:(i/AUFAZo6l=+g06@6C&6V<UC1B![5o^f]p+0,`>g>P5R]$c?
%!jq"IZRX0j+cRBY7:P2hQ2C:g5,E\`Y+-cK%N+s-(6Zl]NGVrXg^*YBp(9M9@0@Iks3KPko^$U(f>2rj_b;\2q?@f`*!P7Ca<\Y6
%rb`G&#\a=Jr;T]rft/;_Ztoc'Cf-cqS'O69eZ%$aB'm!L)G:uA8mj"?&3pfgKV#3#"j!b'NgfdhbUP^OVP\)gR`/I!J#g-]VnCRt
%^uXX1)gf7CQuI1)1R2]mI*oB@PB&qh"g3Nm5%u+'3sm.4m+dc",42LFMq'#D&<'Yjr:q9.ok^6<q**jY^rZ(VfUK"Ro%XpJj#%JV
%.Ui5q\jKi1Nue_0X'$L*7eLf<f^);'-<-!p=S"oSFNO]9I<^UVLhZA^Lgk3@e'CVsnS=.MblugZ\55/d#T]J^ZV`FrN7l[:#;5i8
%Spf&b(*ZU^Tb#490G39L%'X)J#kY#_;lk@=E@E8X]d'GOjZ2OMV\-L6_b^.h8Mdf3krFmToF^`kbJu-Hn+JUV([PqWp*I>0^Nla[
%s5#g3Pq6oQbW6lKb%(@:Af\g48XD_4.6U#YTc6SPh&7[&LQo1l8[F'`[Woc%>bd\-SXDSXV;F_ipEXlE<!(RbLL-9`Q>!K\j;5.3
%D_o+/N3>d=p)3S,>QKg/N/`.@afB8gXWJd0OhU:&%Cj77<p<\%6+^]-K.!QIqAf.EWZsc;@IEM)Rq%Kd$`Z"6(42/>XI?']]D)qL
%#(lfL<l!45</F+IC$]8u$2XJ205R28T,Q<7)1P(<!P!9KBEuLNOGNfOp/fh1$3C.2JENRSp&tqe7KXXPeU859=F#.kO&e0JUWo!%
%EO`EY^'Zjnc2h)S7a5H==7H]V[:UIO[E:Hu+7[P@n-!O5KgUp$TJDfMFh3-O+AL+XB0>LIq$8hl(*@lb3d!rP*XEV-9fHg%V]KhV
%I>nigY:piL7fuain!,_NY?8j?[)IP@dC>*N;&Vdr%"4-0eW^Q0-L-U5>F-DN.a;hSQrN%<Lq9p"!WGk%s/nOp"=`hK0j#0^h^=D1
%me6g=jR$!6:.B7&dTG$E3Xh^iZ$n6d2$6.`D&Wkg(_nnu%f:URoN/@"d3`F,'ts92c)or"A.$53*s5!'ne?XTkXl`>&:g<ien)Oa
%eLK$'%^m-k.PYXoXKJr)Q<\QojM5X;#0L09;ljRX4e]dC.?gR.6ERXt,HD">!3%SPE*5(jW=b,f;'BS:qQJ$;7!fA/i3p&=iGMfI
%QjU-5XKbHuQ2WX1#[M#;s-A-bYD"lkYlC:LjN23KGh=J-j,Cj@A@LA&NMAq>/Gdat.<EEVG<ECZ@n>W@/cNHVj<3PGhaqLUaciCe
%:s*aFb+$Ral5[]sh"i2Hq<n#[$5,>;)V\^Q=?Qi!_$R16#-5"B0o]6W9m39=LNr&ml09,tJ9i,B]RC(9<PH354RGdU=rLJ$+Sr3I
%fFblec!H:<n2KTfCXf4I.5'EP.\b_s+oqcP"OfAD3>;a!jpYW:=]dl@PQp_O??T1!nDCG1q:D=bb&kKR:to<NOpXZ&lEONADAAj@
%^JKp"8i(\mT6B3+nGiD8JWUW'4Apej/l:h.WH[Js(DhVHnOdqGo5.n*>)S)fF?92:\YY1UP)+Hi'nQmEfN(c$BI%PX8kY.8>d-ja
%.P,T/&;)l/9)qFM[IC\1m[6P$Itq>3KPg#j"N*Q>('75_mFm0d(-2)j2DZ2Hh,pRq$6#QrZ2BtIf-Bp,0+,OQSFi8U^M(l1*au%+
%>VmlmV>LmA#h)kTDg&k"@Ys7fGWrcDYpbGQ&rm,[j9*XO3;p09cV>;`.0[\m+ml1#W8#uLQ.XFD`rO",D@:FZ,2JPskoC\AVB_j_
%&C<eu;SJe-DN4A$UX98a6lH1jY>#Q8#S(pR^f\#kIh:>4q<[B/2U(?O8I>)Sr??gj`1JP7lrB)sNVaH+DUHA5d.D$d8-lcXCEl-/
%H8%WSBRHP2(h'u)$@DEE!*c?B]\E,C?S#Fr<$5a"d\hj<3T;!&/Q"C`CN=>4KB:FfV2jq3&s?p1&+\QuisAej<$>VUb`iJXQd-H<
%#iI56@S+'9\DjM8cLk?*dEYkE*A5J"(TgYL8@%5b?a%olnuM_SXh[TBHI!rN3ETna77K"3[NN*NLQEt@;V$E'g`Y2bR35g9ef2Pn
%@(4Uqa^-T.g9D/Ts#g2je?!<*AqDqTa"8hV0etS$fjUgXZ:D]=ef0G7H1T\$+0>^GW6k[%4=,UV7=s!*L;TWj!R-sTS1o!crW#28
%D@M7>VrlO7muANg`R.IEh\4D$"Rdt0<HI*(?$O<T<B\,L=jR0?pl1C#RSpk!&`E4@W'c!R0UD)f@arU\e#oY;)L4SX'9sua8jnU5
%$Q&"kiu@nV.5`Y5Qi:2;?i1('dN8nBDQpnKNl"._5nUnKSqtlX/QjP:YlM/mDp[/cV$>s@)-Dm6[F/A]iSi\PZ(jRb"a1jeD?ra=
%^d[f)2#Hk50Z9F_!OneV6:hNP[ZtdXL;2>lS4'CCHitsbKTTWH`_0'E*[A(nQ,k<WoV7%I8)O^]qZN<^'ciSM,.Wu7]rEN^!sW(0
%40359KoPu6[2]<*E]9%Wc&]!).$":1RqeDtg@3#e8&aZ35PQ#G$Z4)_a$mMfb]c$AM_*d(O8:<P1<<)]>*OQ,Sf3/?(YH$2_A2N?
%5<4Qe'U[n)`VqV<`<=pLp794Zi]Dai7D9Qu=[^kKSX^m&bS=t+Hq"<_$X0)Y#65<hASaP3lNuQY%j=JMHbpR5Do6g_Z\(;a@S40<
%.U^3n:%jb[LlAPaE(gUQ&pD[Vp';4NWF6TuBVB4Za!H%RLJ@+eRi(9IpR;>V*d`6"*)l0P$0=0`CLgh*XX$a^mXGr?C%#,QCIk"M
%2YR*lBX/V0HWS_fdF6<!!,jR8TFMr#s4B+\a+>/1=K`-&auC\,L.iPVgHLRK=[q6+bH.tH5GP$$@=2B)!BSb>3;I/kC4Rc>>nGHB
%kcmbVe0+Yq*DTn39Z>K;-"_D\GgbDT'_73JD#T:!'i0'#f44r7G(M[%o:BL%".4A4lO*"NeD%oR,/KO'dr>!OE<?A.dMCBtJLr?>
%JkcV0\\]A<E2#k$DPlLO5AF,P;1"OB#/`?nPJpXJk'J.Or#?cp<<Ric5=O)4Yrm-V'epFWmUAEVG7fF*q1b"NK5nF@nrbq&o9Zc8
%+R7\(h(Ws9r?])>/06mbP666oCS?P,'XjR60Q8^M>RN*_bi$KT:('S,">:o?.UX)BEA=Rh'[9I,Tm[8=K<1:C#;E0?+eXuOqGZe3
%4/9K]i/fZB22oi<p(nACUHE29l_'%%CQ&gX1[$V1Pn1N41(s.5gFA'Zr-J<?pi>Cjj/-qfpAWbAEds[Eo]aQtKWGXY:Mo,"jS'.m
%2WVj,<H^Yj8DI7:bSF#<l)5d.ith/'-'cJF$?j@;V2$[k.H?7t4%>7<-,R.E!_cdPY`fu6>Xt[@(#osX+\*Z?lK\r"("ILP:htk"
%5lHUB"bgm6g'ApF%_n]BLa3<f!jNN(>CL,maO&<cA^WuCMJ&l`C+$%4=pMt!itA#q;/3n-G:?Rs+fG&.0lfNu:eK@`>p3JT@U6HV
%[mpsg>R7j3*4SQCNYPY'El)K,V_mJh&GJCKG_]LqT)Y.o'>S[CLb[^=&cEXP":mp#Ss2Oj1$Ke>0,1rVHt>ik(\.$pG\oV#>/GG.
%bsiiCqAA/AC1mSl<]pFRk%+u#p--H&UEXb//]$:(?Zh5<5W1Oue#9u;rNHo)=bh<cEWC_^OBh-,J2M8]&0I>`;_;`@niqV`8M$Er
%;fRL2]eIMU[7]CHl.^=SG0l%gd!#Wdnak.#T.tp`;\`H<)^>oC#9n?H*adqB/4NV1nX$HadFRUWA@FqUq=FQCs7d'FE./TP6Lh\[
%*ZqYX'd)u4pENj9WEC)^<^C"U>8l'I_E(;Z4J/=$(:Fic1d7[TKP;uo-<o63D6Y)B8iG,37@o8!'Poq1hYPoSYQH_7q\(&Y+ZDF`
%!Ju9ao2b8C-GS(R#K^jKX_EN5nk$YL*B#_CkZs)alsoH"?t0*OO,@:..@tb0PN(7N8&l&`48lRleC(m$M8VnoO@MUfFJGo#PF>:[
%S\H1hq$V&UH800hc+&nba*S/ZTbUV)<p>g8XB/<p',CNtrXH+%*c[A'KK"Y[n_&Y!/g6h0Xt'i_8nJ+s_:>Z8H"LG*.r$>7%_SYQ
%%TR$Q-WM1[SU"<r_hC"MUc1MN@<M<(b+@[bO9T<5:,uM"H(b/W<Pj<8Y/&*q*03AL1[RC@Slo/`C0<l=poQ%]GNlqnnZ#<]mUn`-
%c`ZLE&r!!f79Gi,1j=ngE7`X"N*PBs;*>"ac)gR#R&b*:/)r-Bhh*.=7EhC$BtZ$,@GRJW,YHCqGVAsSRdNd#^f=Jt0<rTN-2S!:
%j766L$?i7>gJlU'Ob`?25e;)bL8=qgou-8%@om:Kc9+`P0/@Mr4lT9a\'>mN=Sa;dKr4oP(C/fs)"SKBkkr5&QS(rDpmC^cOgoCK
%HM,aIF2G&i$rh0D4P<QHVDpa*U&"e>aaE3k$[HWnR%e:7p"_Se]%3+:kRO_A1[LfgjMtFqhfRW9c2f>ek]seYWkXU;ccQ>?.TL+D
%p;5=SFU53e1CVd1r+PQSXLS6%`0*PUQsEbFUfmp7?T'J"hhPMUm$g!p51l*k%um^53(4R^&`OO#9'b`FeJ]mD&s#]^B@Fu[I"h>o
%Ck^3;gZjTGWH>;F[`aKDSQo,2qsnek&T9'e@E/"uBe2DB$LpC0-M\F+L2um&o2t#Nkh1AJFaT`(`AgS\;IEZ:,rUj57]`*mMdZ`9
%Vj?#HrYDXl?pW)?M5]_(f%TcuWVYh(B"&i9dR<<O&bB=A-_K-CA;[=_V]$df]6U06]RmbQQ_ntR#suqL/<r%\)p/V[D3K"k>AWX1
%/D'VNEfp/I:!8<29b[(#U@75+1bnm":PWB<Z]Xb=\W<>/4t#NL6G02$'+eQI0Ij6,kJ#`r1)qPreYeYa0Ej0,M1m1O#`514O589G
%O6F*;f*ID=!bXh).G&VqHT3a,\TXa4%E;B95H-?(e,Ij%\nB(6&_`VeE2@s^a';-Pbt_7De;pjT8:9\4+J%4S5YX0qQ+1;(BROPV
%dCWG9b-$U)j!/RtYg4(`fPKPo;Fn<hO09bA/#j0hd0([k6rKN*>n.j/]#.C<%3cqJeLUm[UKSo-*F&<t@V.//f<n^;;ntDF"Nb$^
%M5,oBqJaeb><E[,()klRq7]3pW7.#,[F)d'Sh4q%4OJ"iPd*TgL+f7g]h0Ko(nn"rXKi!hDOl)S`$m;%P'Nq'W<Y_KUg.RY,<1W^
%l"c;5Y$[0f^Dqf`R'cT,c`<cuVe5$8F=H$d*]#_8Nthj7@AWn^U6d9Z0;VNP3kj],+5`7;2>Z`NO8SLl.tj0+T;u%"X-njZMaQ.n
%W/s2@S<(W;65cfoN*+<!7RLMNmk/NjAYh5+o-N-8ncoAqNH?edEiZS,dVG*Z([Yo7rZO5hQ0:(4AgblbU"6VNDWR9QF>cb=]76=l
%5ZPlhb)UtdrOsP^M?AgmAD6>7F+eOJQu`6`1%8;l6N?janLo'4U!+OiX[A"07<[mp1='2-F(Yq1ZS((PACdDZ+pQrX>U80]$'MHC
%r5-@#R=#a^jki%=/6[3nmZ*DJne]-s<NRt9>nLk?VmVBL%*@fpQ;BkP,9+SS)Bn&pe32<*JfG4u34h3Y;)LoA?c$NV^WVLpe4soH
%D)KcN0t0BuMFs5gUC\*?S-LkcQahjGc;[RR![oI>UH;[p1^Fe_62(XX^+[]?.l36p"&#;/GnDOtCiL9O*gg)IX3"XP%%r=ER;;LX
%^drH!PnCIgC.nSG<=6&`_tI7)a!q3+C#$67X?(AFD132RE%DB2;Man8riq.D-nFaeEf.3PoW5gKlW8;J_Jc[6&p<lEI>M0-6"Ji-
%CC\J4;VV*;-1'(@)MMmZd=&c#;$jE6A'9m!fF9bpXPZbK6\RV8(AfsGg\*!5-tlh6R^;&',Y/g1EL)tGM\iV.-YE7D[V2s*7l+19
%YPDn*<84M^erKUPZ0;#mWOeB9lM/\/T_\&-Xdc""F#>sgJQlIR7"W$k,S!%p.?7B=l`OZs;EqOS#t3^NJ99F%]`)n#8<\`_fA8+6
%!N%rW$.A)gr(mSsoQ/:4g,pjdXTfE;OjaS@P268.0<:tfLJ^46!CrWWeXjc&4SpaH:G^\e\p-!j&<$YDdAlAd\S@X,WLs63UD;#(
%cBZ2tJq(??i?^BnEd]Y8Q;Zur=e"dsB:R7::a6:bN\^1=mj%bkd/K@RpiLm`5\)1fjTnh1ao+Ii*+^I*%,Ms@KaMA8=("]AphZ18
%&bDB:7hjl_M0Kdp4c*0F!,Pq(AWO)ucAU4M9.!]W6k_1M/H`StbCfs0BGd/E[A3*#^#mI$+8+I""gABZHj)1-*uP=Tk`>8tIYWoN
%VSp%4'>VIG`GpZJZ\W4%']'WG5*M8VC5jnX0\m+,];T+F>RDG8F>#h;9shJgNdQLg;A.]eLr@E3`!?M`h.$YoTrV<EaHIb4/n/L^
%n'YS/E@MP?/CZ&+CB!.LS\VDrClYJdA\MM'fFaphGQK>>ds-K(%St96<R'rhh[7qif;^4FL+ORlAL-^9V\:t/(-e[>#].<FTIV(i
%=Shn<=^LWB=>uo<2dWtN8els(\m8]+8\pHfe)LQ)H4hPMel5=['29=0U1c(CI%.%DDV\sAN^@.`:(DfEJhXl3FKt]MB^Q]4'5bnE
%,^nlrmu!@rZ^jd4K`,TPqYF&B/%h+tNq7$I?%iBrT^lMb&e>@a,*GjQRU;#.FYL"[C)?5f='3=aL@P,\CQG&39HPcQ\U9AK]%Uh:
%2AJ6""b1%fP&0DgrV7o900K>mJ$q!3N:*d@dR#lm0\nATG%$pPj(0A.R@*g@6$9]2DI5W%lXmn#FV,OH?c`"U0^@\pp+@+NZINoN
%GCX4fa,^,)dLFp`$:3sMA'!O@+IiGiX+(STT`"r]'1r<3`/u;K8odfp"`A8GBEGDrh[T2H#h?;8J<C$rXch/Q?@K*E&\`@DQOt]`
%DpSbhk2J\`S7RoSnW\I55IX6%LKYKc/2Z2ajR6ND4oUac&h<"j/\qeq8=aqsU2N)C&mrJ9<?XUq^I;g-\NcYS?$&6e`<^WF!6fH7
%!Qm%CIqP+$Gb-'(=<u\W4uj%N[ASE^>YU&f0QDDuE(r!`4qDC/#KJjk`jB7:/mKTZC,^Es+f&%s;C?coFIhIu!\M<;-#u=VEN;TO
%[ZhH(Bc)t9cBg4:mcU+2TSgeVp(G9#FGc]bT)F*uj,e6$Y8^j4"9g[<=<rW;^X:Cq;C=>l:g-SP^JO=C[<AAm%Kb+K\h$gVmLE3g
%&20JYW_,!^bGin0;WV3rVj]mM)?kC/UAA`SGecdlWDqCd)j+*c65$\*G,a[Yi[re'i+n7?YSt.)_r>plU#$C2]fb?*N$C:m,^@==
%jtuo9rTZ#^JL#Zp/Zu3<H_strGp_(=O,noq]#EktZ9f6DM+A)[fc+/%De>L/8+n<&rup`FI\cpY5K9"b":n@L.Ik_i<P!.T!]p39
%:0]`1Td\W11uJ'PKWtj[s7<SmT0Cm,hmL`-*0/jjSuR-r15N(C[XD,8fat8;9_UIO5\YEDf1Ed_>TosWI?l.HjEP;qpro"c3?04!
%(oJ0kE'M@E^uu09r@K_2+oQD4P,dP%(7Z>JAFXO/B>$.,FIs/@,9>Q:m!g/&:_7eljCpLS@H_-`JatP`jLsH6=o&18)mHqT)pu!p
%&bZ^^GpoIEP4%gr:>#>G(s+u#5K,sS"umu61T8%4A7gVZM^7r?%V3rH01j?C7`%JO_P!JFbu6L\`s29-&qJms3LniTD5VUcf^0W7
%m@gXY2<Q69K+[2fSda?jN5lbuZi%IO8%*.T77ggD9oa<bOCKkDF>s#*p`Tio_@[7#,7<YpEMfL([AtM^7]=A.8#J`nD1KT)/VLnr
%lq#Ta]`jP\p0j3k7ej:cEHT#3N@N`#d3&t-7sGf9O#RjJr!^pG);Cb"]#:i2Dn0/Y=f0dqTR#rbBU5lQhI;.j\qu+X];\I4s7Xtu
%*1YP7_ODmu_gl4*10DW-1G_5IU`S,.eZeuqE*sJhDJN8^;"RJ(Q]o>cq32\.m?*N&B8s^L?JP(+g`61Yh!3E^,-VMt=>?(#DEPce
%C9s'*e+F2!fSb93@B;p=FFYAP'0tsX3!6P1-9Q]J#aSW)l9sMe7qg;Wp2XOLe-;7ZZWjGqG_?WHKX`sb,p&@l'/(h9C]+lG?*eEr
%o9:E-Y#SChC,+A*?*^([kk55Wp929A>KJ!)O6C7kHrap.#JeW5/,%o@jHbsl1%8LL40$Dbo$\A&h>XN>=^b&&cbX4o(VoFY.R&e]
%=\"9R8El:18VA>5M-5m)M6Wp8c:YU<TTA')kZPP]esGH/i.a(Dh0cUkj#]$+nbT97`\A;E@=@AW7TO*mr!J7<V)AN:@`O]PXFmHD
%9T_#,if"C0G&W\_mVofb#a!'bQ,Mh][+P]flGil?Jj<X3#7*=*PqU:g3>d%CF)oI&+7C>s2U$I>4%ubA)P7[eHN:JtBJJsh[MYfc
%PfRT>.uni2iKf/eke3?&[4_b"2uib-Q6+c1r[T5PEd32hpTeeEe.*_@XrK`@/D?65qB>20/N">*rFFunj,CC5?'b(>];L&#Y-W[K
%OkSB%qQF3J%+'dEn&Gc$_IrZe0'&>M$;is]RF0>BF^5qja^pL(pJJWtN[Pe8I3/.$&f["$d35G2%MbZ6SeYY8\<b34DF\JlT9..8
%IDTNN$:%H6iL*uGZ=N@q6#mS!A=".*ZrL-#A>;N8!RM7A.nj^Z,1J-=C1H_7Oh/-nAtq&>5NWml^du9bE<(h(U''RXQf%(gjYMuX
%,>Z-a]L+KMBcVd($4QN=HsCPlrfTm9*hYr;*0/%P1]iL3RubQ@":k>B('*.,Q]mN@7i_!(b#_+f2-#G+N(\g#OS`JYkc,OD;QQrW
%Tn?X/EK![rPcOD.@Y`,C3]6<releK!>m,Juk?SDYpmS]%maP-_V75A=k7VhD2<mR+`ediT5@_o"I"E?)DAca;'$ih[LaM_r^Af-n
%Ts@Cr7C6aKR-.`]pjTo2=.ls[7_Ng_n"pJG=5^*Z5M;/<D.puCD=SCo2f#+l.B!`iXYt7dme!3U"kp5OLNmD9`288aTFn-#&KQ\=
%A<Zq'0N!,_oMMYT7b74r*]!TAF5=\5HJecapO*@ah9_!rM!!>j9.*L>KhURK"jmOed;\ED9bL%sQ%^B/#UfH<T1'ka88c>9NaB;c
%r[\n5Po+*!Tim.l78Cm9Lu>,l\msnFr:cP+AglSJ/9M'O2]*\[932i[&5:uW.sG+TMbeoZR+c3c79nD=#H9)<)$132Q4[-/N+0)7
%MU7#F^rLgJkm1d_1>H@JdKg%9Cbq`H%&OAm#VY5C%5:h\,DDNLEC\\G;C!Cj@g]@?3Z1M!7Usc_&(t#qFMk>B.!uq/L3QTCK5JT\
%KQs^n*h3=O3VCb6TTNUg7%)<=C9!`k$r_^Y^Rd/)e_4<g7V,9:7"lh%06;4G##:r\:t'tuC^2f&<5kfk4_O\hk`J]k/!aAs^sl7Y
%#esR@[ALeaD@ch>Iids+pFG2&C5AIY$BeVPLrs3Mq_jHT%\M)WcDf:&7iMZP$i^&C:1=20)qnJCfjCR:*[=hMVgU\u.`"(j[&U`N
%7%Zh6][2Eq&?5$n(o]%:*<HX8DOqD,9^',iM/^rM:l29sPT273_h`Z<ko^t:B3q?n.Xu):aiqblrr[@.K4q!>\idU<RJo8.VX=FH
%7B,36MtD<a[S8i34=pH37SQ&IV3@.;;,IEj6tFS=-Tt>:/4;j79C6YqbH5tij_)/(cNJ'`/,h\!T&nDTG1$N,]lcAAr#@tLJ@-Xe
%_%Qe8T0%IW+K:ITL=mPOM(0"%O@"GU-SjY6<TH67)-O3JOAk5[P]7&SI&"B*`Z`Rp&U!n264[fRQ&PD!Y$f:KY.0G7Q4fB",iO&W
%W\k-<9DKaFEer.N(#CfX.WH'&hBJ`E8S9piU30q*Chd1_m[P%r,eAZdQ\%d,H[m;cQrB2T_*o^jV3/;T:?1C*fV*0T8AoW>[ETsB
%=?9ZA2ZmkiOsmH1]+Z%>R6C``@Q)QrD3'u'i4:/ER]!+@qlHOjnmk2L!pm&]5_$*k8`O>C(4$Pu9(q-\2&[9WcOma8@'C?beBN]o
%U^A@-pg1.>c#Y"W8q9S21jAa2kPt(:3K;+7MQ&NZ:aLWK2fjm"bL;GOH8%eIU[(UhW6SLCQdG@l2:4!X@IANY_NibAho-lD9EL+Z
%1'/iDfig1NQ5q_[21Xh(Oai<8U"]?&UY&[g[<fs-C^mV,,_o)3PO;tZQ52oa+Y^QF[(#fIS"^UX!3E:<@ri(lMMAuB4iXKYUl8!C
%_H%>D"1,3Pkbhc>%fhmj(;7u@ii!]f%o%M5glW:h[(TJc3Ou2eleQ/KUQiS*6u<rqdn[&ff]f)TfI_,t*GI,i_]f<eNG$39#E&6*
%Fts!TeYL^1@F\UM,J3%4$o8-u(,2N/G-*9EbC^*jo&%PkLL7ND-k"9gj'G;/r")ZD2\i0F$LRVnRN,a8.@3N4MSO9rmJXb1WOi@9
%b`#!EcgXGCl>V99)W`dg<LR]^R2moX66M%",e;R3n<>rmH61P5i(d\d/85]AXrqkMO[:Bur9A^ih`C$?!;jk#.[@al]!*)#]_Z3B
%4D$7"0)-ajg-DrdU2'jm84EjQs(_\@13Dt-0u7H^6Kq8%Sj\]`R8"BG"3:KGc5!N#;IL>.(R`87!$g9t@eO+KJu%2KIAlKK:/!u"
%pgTl9IW9!K>J685L6;=23f*6a;k)ua[6E-"(+0$QB^o3X$*Tj:>[A%E4]Q_\'mXbhOHVb%m8K@TR@uYE:[P"m!""ADBop**"7*Y2
%<ZSVhmPKn+<7+g[Dke$`&*Vj<b%p#C]W$$$c3I[S?+9QeaooG]En<!CUXi)3l+0a(Miq/Od]*93B/n.;_50o*Lk"-'c`;AH>AGW%
%Ur>;gFRl[!b'SGP?^%n[_2muqeE/U,W$3IU8`e[mf[@%mO(d0dJ3cF-YFaS0Y7WE8ZB:`Rp9OhpHM]c(rA('3CC*dQZg.l,*]"Em
%0oI`U7AurNKsFe0=F@JMV'LbbRs'F=Fs7r)W\<G2Pg5FIkekTS#Y#;Up+&pV`"g7#G6$MA8%JDFVb=/kb>(&NQ5n73gN8"Qq1O4[
%@q9R_,N(6m4>,7kfi4$gT\*dWO9EN.$i'/6O$)TB6meX]Z<Mf/<?rJ?X&WqNJbNt/bcs>u?r5?T&]HkPq/O2:DStXrT99'9:^^D<
%*"cJo+G_IDLKD:5*J5&t:"LAX)*H[U;WA<A;KtcZ:OCtkrkctZ"(P>2MhR5c)OKaiA!hR?G3/F$0`,S:diCI*`C$!<+@l[Rb`;dR
%0DbUSBNEZiPNZVW[7C1!kTb3C[7)s[U/e?R7sRa$=c]J_7A/#r6s[@.k9fD.C(m+fKMK\n37h"Z19p5X1m[@SBC06a>i-^^dmf6k
%"HJJ=%58gr$Bl&,;$/a%FVqZqPf@T7Uba>c^[G3H5`Cl]!*;NJHSa=Ac6>/V;/Dm<ZS5=IV,@/Cok.O!2tG*b'q'Yo.aZ)YhU:-k
%RC=A]A271?6[nGg*$_2O&1&UJ+XR>,0LDd:&iV,YkP=SmX;OlY0jff`h\S'TNJf=_9;=g986%LtU=+`B7[%0[F!(pV!CknjrAU&7
%M3LOf<WlZU2qkbHfR@U[21[mn0[=4d6g[PNJtBnV<=uZ\R8i?iN^bL`O)YHtUstGTCWNmhQA:(=#ho7)O.aq$mCOPE<.C1+4++0*
%@X!_E%6+I]5!TF$f,[sr+lZA.#])7E2I5.>mTrBWA:5$s-j@'<`Xe2W0Rpa"86@'<:jHH)r9f^S0_>/Q%G^Xd(u)SR76i1?n>kQl
%K#HV)jV[l\ZD]:u\.ps(Mn87<=_K6G2i8TtZ7NF"B5,!]pG":Oj$N`%A([7>?EWHlJr^;"i<\lT+75/VAW[p^?GHOu0J>[.fgl;l
%A\:-?acu:FasAof=4E"6*5O$.`J:.MItar@30D/L!W*abqQX1XgjiVJ,!^D':/A;\/Otng0jV3n'tQEB(8M"ZCfH5.?N\[Ig@)<U
%`!CI=Re245pT\Z2qr#)ub65q4L=p[<=GDMA@6>q3hA6dLM0pKe;c@c2?poDpZ;7JXN=^`]'AODb+I\BoA1lsS7^.1B$/2^N+ek,7
%b"5_i,C:R9Y7[^p/bLL:$mQ,ZE\eJ_lVYgF#SL[gdd9ZP_'l6+K2Q39`_q(OnS]p"0;4]T"e<m2946&W\Id:lKYkj(+qM3uZ[P"<
%63_KC^C8Bp2L:4Q;(O_[PVGj4_)QL3p(cFBlrsWHdb->&Ur'X7)P-e,a,eU,I-0,%CD)\$4\Q].\E8#@h)d_=fg`3=90?BWH<@1[
%e:D+XQ<6'm6\H.pS@M-I)pT\9.6Kp&^LGUn'*7.C\$T\DN0IqjV^*(`#B5rqdJ9B7qlcu_PbE@b!h5[gN;'Y^AUXpciOVd]:2ZGq
%\;.F8T#]tqW3t2?%[K8KU9%j!?O*]E1pqaMm,62Pbbj4jNnC]7*JU!l@6@ji-a?Hq=_&o/&-JLXOgLYHlK96)&'+5\Eg6+lFXs'e
%"QVWGPr+6^c"0NN&_,IlO-NQ(ES5Ia\Sq';M?HN@)2=ecN?'=N:$lToI<K.b8nnS?@X@gcYr^N:_aE[m$QI*^g]UD&V$o<RfR`9,
%NT:aWG1UVO[u-h!GeU2XFK<,MO\R*eDgDaVb>Jt5!Q+fA]F`mk2UJ!KYB*BZ7DJ+u70?t^Gqt-TPH+C_aP)UZ`5FCFnR)0@s7pd5
%l\()SB\(.&VG.k;UNhH/:UhBo^I;^0G25Q*E)f;Vn]Vj/;A5=ZKq)8*+In05EE!$`!$AC-*_cc_0Kq+IB:%:YI3.s9lIm/5'a:g@
%UaElBMP:2[]mGHOp&*!Xch(*Z#2CFSj)\;%^2)Piiq7OghHP2B)gT?_7OL<%8%)SJ\b;(Z=4[+UUJ&c8^FX#I\.E*\e"M6m*\6]I
%HU9`$mW>=u6#D"tKBU\a_!*9L#^&r_=6D`9@>?OOYD-(KNZb8JNX@.f?kcrK=^&e\Jk'dK([,V\PiTNoU7fdn],QQk=i(e#O]F.(
%NTGa%Z^Q/X0P@.#Q?6Xk]/+1\iL$:>)(02]mpa)lnq4=h-b`d!R"Z=ZL6+N;cW8DMo_5ShqW[<h!cVKkb=qi-=%Dplq,(ea?.?L4
%5pRScC;e`6fQ?9ij;PX]Hgc6Fq(<uP;K58hIgmV`;XMMq'C1S<6STJ9q%6]H8QptcPYF;=Y`1Etd!!592gTe5=Gk:$cI:ubX@/3[
%_T3,-m_b-CG_96j9PMX596e`lZ?V)1o!SW'n;g&;+/q;o7/@3sB;Dm+"MVqE^_l%Loc7iI@lqP9PE/B;on]i,[k0fsqb^l<59<br
%IH:aTUl'9i*1QK=CW7AVLQ-I[H,oCL4p-)YQ*FSGbDr)^aG#F=!j>ho1tU.hN8udZjC#i>]5d]td^WASB3JXm"^UPCpS24edmO"p
%o8X0eR()FHI\B7RKugE_2hqN;&2R?!=C_6FpgO$]lFJTUS?M\V$3)VV]u+jnO+4f2?c?,9Cak/s'`Kc$r,P0V*edh>L.q]^d?hn"
%qk>ol>RqTU@8^T_3,tF/1C1Y>,=rSG==H^G@>V:iNd-O@EIS1?1N#aVs$f9;0'@0`ER0"E=LRa@9tFD_TKA=d@O)j?DlC=oA$U'3
%BYeqjmu,R,p"Y.U*p`O<57`7-%CaONE>(tZdc!8`'KcB[Ah!,R8&XV3cik5!J[QlP-Blg[3G!a-?IET!_Us60*?^B1&XU&jdlts`
%@g'V1S'd_/QCE;Z1Fg^1;pN&WRk?^Zkh>=H^<EMAIL>[h=LgtE3faEJpBHK`krI+;d*=]kenLu0(^^^rjfVVho'Rm<%NO9Y'
%6J8W#7.NVqq(":h@;&>&Q(A^B\@YIUAiIn?bhVa5J<VT^O4p&X<Bo8Qk/c%!&#pWb\q07GFRU&0Q;:=Q1s2sFU'bPjoA.LYFHgGD
%/sc<[Ph`V]OSY/!4Vd1,g^\1"$,Z7Om4`O7KHcnV1^emY1fR>;^B[B)79%cejg`HUhFPm6WTiQ+O]9NkR0NDqO!S>N7Y#/!f`V&(
%`2qJ<0"CO*=WUWW<YrFL]7H&5)L"fJJoX,N*^dV2>!(P6$VD_''T$PZPsiXmU3Por6$d*AaLTDA.h650Q%Hs!3eKnUp(@YZ/gsY0
%l#&M4B\<TeZ\P$.8S7PdT?)'QC(g=&?kteb&Sgfbp)Ws:o#-CO(lD,f=IcN9&T&\&DDe7:RQ5f*@7=\Ka>l[Wr_GfgOfL:!gYAKV
%M4@=AYU]6[dBQ7mnAi&p8BV/8;)!Y6FSG8crEs&_(7K8gRsMKDW'3_\Br@*\Db@Stm,QOl%'0nPrA&mD5)[LK#9t2t.D_b%SKO0J
%`<IU<]Gi6?F,36%#iui:dB5MtVc3/T2Os?lfN51PY!K.&ec8OQ0iQq.]Ob#P$&Qu_XH#mVRjj:Eidi@N4cqV#1XVL[*^[_Ac*YqK
%29"s(X7ir]19cq'j:_)llf]l^Nip0Wk56BA"G^.s+GcoU86/YjQ'sO).KQR=*?-lLS'b:scaVD[alIhf@d@.)gh%J-EhuS9Br'6g
%"K@W34joE(On2.''=XY@^"Lu;9K?Q!iajgDGo:VPN?.rDC\7J[Mrak,qW#^Y+QkQ2]92te^b\YBKGU#t=GcoY)P9M&5&sO%HmXsq
%Z5cjT2(2:M7!h'ad1fk!<*^5]p6)bC9CFJhHj@Y0KcU6ea^s("7XS03]&kp3Reiab1!,u=1N'h,Cc[mc52jPKai:V4njkBpJAq[U
%VBRa\P0c7)94;fD@Gf:bM*,\2HGBPeFU5+PNX7>njYZ<JbJ6&)^lQEI_"BGh%rjPr!4tN<K:#4i&[kG>[X[7^SEZfnkk8?)qh3)A
%!X6Z6G^B0pmbV->#XuZTET8^'],cRgRTjY6QU<i'Uqf1>opIq7C7.6ES5=/[`R+b6R:u>_N#!W@hJ?`[03+o-4Up\Bne7KY<c"eM
%8j;c-A8.5H0&cU?k2^r=r1<b+6PWP\0UEl>9I9$-R'V4JH<r>YLqNJ:\#_m#@@*E9>4d;q&8AlWZ?-`d\K<#^H&SXj7i'5V7Hm/4
%D-)A]3Qi?"$2:f*J@V"e4_cqN^0H[h.Miu7C,0%h//r@-<`ch(Ojb<0gN^r(M:.P.lG'SE!\R[,Cs_,;!MDkJ>o;6+.\jTVd.S%f
%R`sC.(2QQPDJk%VQ?:EV>?LqqZ#F._Gu7>:?1CNT0k:Ghs)[]I((!&G/Md1r4&&Gm1:i-fi-PW/*:3*F`61%#k4%QX"cG3uSAe'/
%82Ye19/9A-*r$HLpc4CrJ*\*M)<&$QW;][3ZW.%tZ]<bp'=>nbTCi/V20RK)FU<BaF!Y.V+*9Gk;0F)XbOps72lbNmlL5XKL!$W'
%<Y>q\Z>t:<1!7Ob+ImU._<*;uIMnQaZ>Zi>l7Nrh?tg2t;TXY>*#I=LL`(oO_*7FHW/+mWYSR9H@(<U.g0=fjg*@L#f*<3#^j/i8
%joL>q^j$.m[(T&$Y#_/>d9Hq:A)rs/d1XJ64K"`r[!B9K[U["C(.GR37`nY'_Ejq,#b4@?HZ]?.;Qmh0+Bk:JC\k-1euk&^e]`U)
%9gWLTG,>>L6d180=X=c$r[,1l&r;iC&r,Hud%f\/55ZSD0d4->D\PrV*d@OF&S5:r>7IR97mlPM63sr?>k\51E4F0uiB8eKih-$e
%:eHYbT^E4R:gF]!41OK-2SjV7]jP'p*O*au_qS:m+nQ_El@uYP_6fsNUF/lh^g\<Q.saM6)H[9#UZ:P=i%nZ>%M4]\&Z.@mi2.7(
%)t/"`U;Qeg*Ynb_-YRiu@H1KmY/%o6Ck<cR@mU>g\<)i5A"orX:/&nH#!O=HX)PM'r6TYk=70N*m/j@-nS,gF)fJ&=QohpR^70*)
%1'k7r2nbg9G"^k@pCmsaj9("KR-F9TEp96DEFsn>VaEqWOX\8ENC;ljkbY(s*Z8nN8c/]r?4,&PB(Bss+GiuAB`ai:V*OJBC;aI(
%c%qnX`WuUkRP$*1oO:UDXh^9-Xoi5bK9>;^FsS/-MF6^%K+t!qo2np7:"b\97.*_AH,_&[lKC4B9&fQkE$qG*ciesL!dmPI4l-?Y
%X?kdnlpbK1-da`U+'[Mn]7l&FU\d!e:D5^qnOQJpB)VE4gpX_?(t['p2R!j#`;jg*W,9bADWb.N\`DPdOkQ*4R"F`M,UJ1A<'P+Y
%BDgJ@,MehO3VBbP!lg*3b7,Y'-9!o9p])CGijXX[L)iW[ba?]\>""LC[r94u#d#LTWp@k\%at:opCdi"RqPi8NVn/=E/Bsb*rN47
%e<aX@"I)h4!_9u9:6CI:f$HE,,H0-h\E_5>mU[3%pOPeAB0(GHF92"T_Fo&g@D"B1V/?\$643L'A3#kmh?4YHjrZ7!Xhj$]C&GYl
%aEPCb<"GX_k5+<d`)3OI9IXS'>b.-D!LpnS-m8F7+SO5/6l:)R6&-M'^sS,ed=\,NF-'a5^]H#R0$#ln_BW;E+d&d5M-q<pMt0IG
%93/9W)g"RrR7OefHpJ>M,)+>&WZkLo#9(-"FoPFjlbtb@DDLZD)\'Fl!T56f%ufG8&*)n`beHNn?2@3^jJ^G(]@VJkl?d<#OGN5#
%D-MNl7&uLC*m*As;ksWt-k\s0g`)Ki%aiI90+`jQ5fj`Y2s-nu_?osfo3;Hb-GL%?HrLGR<m/^(CbH(LHNWNeedfVYoSn0"h3s1K
%-6RURR;iF3jNT[%"d0P,Y6Z,4_W$d.47pau,1%,HFn-1@cpk%Iq:'&C:AO:W->Ie3DO3gp\a3iS0-7I^d`4MS^^u%_P_9c3s4"t`
%JSA>`i-Fn'\4BanJH/s>;k8!sRWe6i8IIrg/8Y:ud<)[?YeQrm&DRmG#.f#GZ9&aX;;3!XP].SIGpV;J:g0kO4Eg$<Ipn[kf7!Y"
%"T&9)Bbn2\dm/8Hnqok3BNJ-]TOo59D0%$U&`G?0Iq[/l:`u?gr%7P7:=R[6^7X)sH6W)pZ3(K<71+275MNkDBC&1%&k9>V6oMb:
%V+34]*Yt`<<=FsPP0$!5NOV@OHh'n1Yr38'-pdoY<W1AOCsr#b-?0:V'T"$%Enm(,@F"+?#$5rnF9**e_rE<&:1Vn?D7MnJa0%3m
%LTFQi]Q4Y`/Z<Dhh]Jr18j^G>.%k9'+e_1eY<TqtD'f9Jj"\"<!tWM7`=2kAR&se`HAFZb\lSXO.Za:;LN.I54aZ2Jn<196[H)5s
%>%0(D*scCCl#MZO%AJ$b[9b\@&$BLu<4*-2!Iq[SIDR:pA/UM5/@jfQ]5=/-X^j:Z<M/JZ;5=a[ekNa_b)jegi7UcL*?MX8)M&fT
%H$1<La0HSKWCRg?&N"715`TVAZm.dsf/W_ILEn:VEMjK']Rpg"9.CIaa+PFR]>TK<a!+T>FqUi:,RZUEg50MA:p`Y71('&4=r6D7
%^2I7fb0n<oV#Fq8iNR*5dUa*or4i]O8MS8U.UDU>nC[Fj*$g@_@uI?K#fmL"+1V-PYF3_[[`1ZAmU.qNP3Fuj6rk\1r2i>Fi;#q$
%/d5[19+:WOY]G!Hg@guqP0)ecRat\RH6kf],EW:>@s7b.nI1(UFU%Vs*1Xq'V:S.G(-QCL=m>i&[<noll%tCKPhmFXI&EbjZcs_R
%49N=;47J-j6j+_M?;-J,-q:!\`G&k/&C^Nm\Um?o?I3,d8EU/h+D-/obmaPTJ_f>[mIatC9]g0$(eP+o3-CU0fa)#FqDCE"/2=0p
%IeS9qh@YRgK>iYPeQAT&IJ3.[B:Qk>^/k2?4KJ)cEQM<QLBLE>;E1KCWf^T]`;sL@hE5:O`Z8,)T1ll+Wd_l%\f0L3#PgUm^#XIP
%S%.UB)b86/CKtP_=JdQ2\Br=,KPUEoRii8L3\54\Aj6<CGG2%k"7XmKMg/R55*`$`;-j<jg0J^K''IAHn4ND'OQ)cf)2cV_pX&"&
%!">V*56om@p!6;)Ygi&N2\'\JB]m-3o0.494&DmA6jBLa#QmnePmF3i52geZ4*2]-A&RMtD<W?Y2fs8+AcO_aZ+n.0pP*4IG!Jg`
%:K,_Vl,>]><OfcCH2+[1@D1S['?L<"@5uk(bpY03I:^&5S00B)Pi:t*GuI1p<cLb,$[K^Es.V@?9;I@LJJ*9.Is&d?)GbO4Tpeo1
%Gs\rXiKJ[tl_,1\-1nd_Fb(,jpX8=dO[[)Z.'Wf8<u*u?(5+6V)K![R"?0RG#3+Bd`H*//D&bH@pd5a$Nq+K=A2d0<ad"DtD-E(O
%#Ij?[LBkRKgqFdIXb.g0EZ@l(??i6.M4u`(<T'O:(A=^/1'r9+0uamffH6E+2M]r=>s:`gN2mhT*Q(%idi:uI"7fp;+\)^!O%%m>
%BXm:mm_c`IS+nsX&JZS7a/t1@-fk/"c2.5E1$e\!lS&L;ZKVp'\s`V[36Ub<N.G9=U9]H&?k^Wnc>]bS!2.thD$$3uGZ\%pchmVL
%i&C>U+n)Q>$Ef6oXCaX]*Xgqb&n*>Zl(="PI:`IX%o,$%$.?p_HH\N?NGT4X0-Sse-WuI%'_[Uj'K3*;Mg:OG(s6L/Lnm1@6![D,
%RpLY;R0=3<s'sgcLbYSge>[aV]@o8Jf8Z'NW*75X5,V!NTLj&`ao+dUaS";.GYINjVrN(O#2u!`cN2I6/gpt"V.+g.CuTK(6'R%U
%!$Sn3kBe#*BEXOMADe:rL8ujorP3*t*SSU0pU*Qsm2C*QgRcR-XVms?P]ol;@RK[i(:SpNN8QI+HW/tWfp<AKB4P@q^&_mf6miN&
%7LXW?l88qqS1\Zu8k9fJqhrdhb*c>Y&2SOUb<gQ^Y3U5jlj5.!9(>25AS&$"(T;?W4o-L6oH&RjMSRk8=D\XK&DJs-i)Q3?k)mUf
%"J_B;K2;=[;dTKMft?n#?iAU6rq;+crmUoC0E:FSroUEanTTK[^]3hZkF_\Gs8;"$rkJL/\,Yc<J+MRtrjVq)Dh%Q,s7jj"^\RF"
%s39I$k(!@(^]*'HI.tNss5>("r\knZIe-Y@mI,Mu7ud&Nmsb.pJ+l&)qr]"!ric:eTDuNPo2ktP2h1dYT+kk(T32*aci8.bl+]J*
%c6*#jrMT[giSa[Ps'kou?VL6S5Q;Q"q4F:=s71A@s5pe?rssU2[-E-gjF:MYq%)Sr-cm[8:*p_hPi%3S;<+6G-+hG6p-td?NP`[Y
%MCf]#GW)U!T[^d7P%4cUm&mUT:9q.115\qV+-OYpX6F[1&d'Mr;Cb@olbRPg8\/A+F]A/$WBhIr!bi^$^aDZsB3J(:GLm2Ug7P6c
%Tl[$0&^gbMN0!0aho"fD"Sq:_2HU$/.3\:MM!lZl'"&_')$t]OO`<?4$-1t%fb7Bb(U_`uT]W#CD9kTgi.k.T4YTC+H%T,eELX/[
%_n7SfaWQ(PN'JW5;qrNq4epmleVDW3].4"6r<%5ma;QZ-<M^-ooL9()OnZ7]d6lTr<^-lPi9%!bJ/-g2QrRYN(fMs+9/6H3>"5L+
%FshUbUPrShX18h^CX<>r`n?NBAID4gW$%pa)uN7<Z`L88dCEMt?nPXMI3<]D%2r)3$\1DJXqHCR/F/*5i(qcr.)<6mRZ@8A-6aIV
%i/2i%0MEkRqll"1kciN*6rFT!h>6XIid)FdEpcgOp;fU9&[^:1JoQe;k@L!_N6Y'oLa<ai@9:#!&?I)</1QQG!`>M&Y]2`Y<3TJo
%++O@liiTqH$<,ck0\17''m<UVE<s\Q&`DM^^FrkOcO>L>3!ie/DG[VcHP'Un-f,Uu>_*OW^:hSR#3GBK:=kNcH&_VT>(bfa>>(Y_
%X3][OR:?KfiUNNs^_#0Uf?tDs'\;)DcGR(iS&?3[VZPH5O]EC\,LT1,S8H,t@-K1:8(cR>[PUZVZQ6%RW?`!,M1,NH]8U^Sfqf&<
%rh*eAk*aIJ2<\8E;]@#^>>V&W:a/4_EkhDUl(MSnE;10C&84XhI!3iG@\Kg.#/Je/H%j7C;@1.2MNts\**jQioeJ8mK"+e(aH@R#
%5QjXrZ2O-VYoia-">0hO7rW'>qM:'KR?F29^;*_>:+NgdYo57*U/E6(JgP'1p%]bNEKF%Q;1LC5Pms0sYZX$2F<-Jh<D`?fe#Vh'
%*/:[n&bk7M,Nfo!2>\eTVU^ODSq3Agkj5dH&J16LP2"Ct`<@g<!$W*+=gC5cQnE8jPUV<e+>NUFrGf6NGRndL[(i/.)3MW,YWncD
%LTmhu#]I#kEZE"ATT$a"1,onCEDa>$`5tr0"W.0O0n9:?o32O*r.:j;mjp"mIk$NPoY61fi6QI.s,aRHNX7-]FK>8ZfqqsD2l(2W
%9;!8(/>Zs"K%7@T?S,@mWi+O&$+m-.@5mbbP2I^:lnT!.9DEL37f+H5W.Ycc#atrNW="@[P?F5GZ@HjeWjTamkQs222olK(WZ=kl
%=>fK:23BC1Vem)^8!=#nU7,$i`lidb9WaZdRAeKc#]Q&L7sonN?.g6CrPI\H`.>H&m3F3tG+Qe=0&i0\i@q!&V$)ku%<7[qJ_$3\
%P&Z7"c=VA^UQ+G]-Y"/L:ZfsgEBal.#d9KAX!mHhjR`Kr.k+6da,9)rN0+^Z)VJ^k0ap`B'"K?\VGZQITJ*)NkAkU*D"mj;CgYP;
%XHF/:M_-*dkc8JrCk\/*:Lh/je9`noPP7;ljG<>AHJ.UaUB.t*)^/'0<DPb2!IE+E0dP(j5"`QUea2'8W)e!r-'GH_UuY'^O6(J(
%?.RZN.M,#AL*SHEGG;ipc*dfPnnA4Q0JA_Yhib"G^K-N`]W&t&UY$!k\nT#(r$bY+d$@H&:d;f4k*uk5<T+]cTk9=p!F`cZ\[DE=
%X4dYtn["U$-Y*G6!QFJUbO3.R'XeF`mTK1"J1h].PBB`C_Dpr@P0?ui'hG8r&/.NF`'$H_epQJ\dgIY;\c#_76^MNc8BR4Q7dquu
%0$*3:RK@H8X_D7;Em=%p)Th&i^$5/5@B(-u!<R-!1,q!oRfs+@!0V@&fVb2O"%6j0:H2WX)5m6_c:q[`d]h5e!\5(\\fN"ol!Yr]
%9L`ST8Dtjg"HDgDMgm_2!Bu+gPrm"A7<J2S,.faf3k!"THielPW/N`1,Si]`J,tK5i)U"P1=k/Rh7t:ZNpn%5>P;]N2ZHn$A/NL[
%@@Zq6)Q4SN2;Y'i=qC=T7E:5<mjS0Thtq^DK.HDJD)OVt'4lC@B2SGTP4B?A`Ke`W5,L=ATNLdYPG08R6rgrm2'a+E(<"0Rr*8Fc
%kUjkX!o]3J-`I49SuZgEG4D#X.:T71*)ucM`SO1r-<EF"b)pYBNjjo@;FHsZK^p@t,^='MNCF-oN$<t.IHar;a%Or%%XVF$=EJPO
%ZOnZ@G21Ys#9C;"Ae6ehm]ArU.VDuZn@kEjk/gR+(GgQ2Nb:a12$ggQNPa)=OpBM>&p&r:9o\Z%U<eIRSL1(S=>LFmMW"#H>Zmf@
%;&U/C#Pg^*_c9RK%UIJ]iKl1;J/=j]8A;A'DX'&CD8^.+q4QH97hQ?Ui1'ib0:S(6j,d7O4of6TbXq.uO_0er0eqsuS<h9;/XPnb
%j=hCt%T5SuP?ShdAi[='Dq'k<,6=2"hI9cs&NKe3<#QHY:\o-;l;Qc[\:/k*K3hUd8C(eb2>O+'Ocdhp!4Us-24!T%B+#]@]?,*I
%:qoo+3>r1Rcdo[8q"Aot=Qu[BVBR[`TVZp%%u3PH$,1&TJR#pW'nCAeFp1dHSuD$:-:O5!*?Xe(Y"3!uI*#u_3\Eq`"^Bb`NdXD*
%AJ]be_6(6`co.0A=h9uPac@br,Fm.uaVJnga`"Sg-o6M'L'8sVNO5I78gQ4b2r*(A(">QT;dG(CJD'0KA'X9_,%Kk0+;$,=qCa"P
%#60,*92FseHMZ`V;_M]-_urHlA3@=hck'/].H3\d2i1RD+4gJ)YpU-b:`G<9#cY[OCct2O4sls,(E`%.k9Gq'9`TI*,EO>s%W)ZZ
%JZgfD>iN%aUebQ;c1'n%o56cZQ&>kl0"8"r>_R"Sf)CttU>V1sS+W7XN5GSe&deTXh$S+^<J<]<+4YZ@QoMegS3T"I>D+NC4G$ZX
%ETl(EB'Ql^P-sX]UUr>,o@W5,0a9(Q<S#9HIUf!42l#+k8Z)Jm'VGUMQgus4g&PXP0qu?]a*d"o_51>moMnaU'U+79\Z]ffO=$?k
%;k7M55!l9eES(8k+Jo?.aJCPDP#f?J#t.-HOXVLTbtV%'#Ud&+Q,#ih$+`NMd9A'qm(-@I(=.b,/NPV^9>ou83?nVSW",:I"6"3n
%BaYCA4)fA.6HC<PB!OgpY<uS;i7f&W=XS33\rGp#+59`k902.nX?pt!f"eN.U!!r8BUF9pkZBFcD%2fH^k>st#Vp>A8->1h:bmD&
%+`<I@4HIN=K1G(75cg`dO.5^S#a_ilAY(nkO)W?mb#S`>NP`1O1^Ui=M'*r4lO+8Ls+Dc;XpYe8%o%!o@Uk(A0Bf$*WuD]4<&&[4
%m^&XNWP@/`-K+>b#*d",goVk">qrF-&<UZUOa!Li4^mQ9fFm?,#b.(m^l=fJ'DPf'7"(f2O(V9Z.Zg-2/$&jK^(A2G.2(@3WNZna
%;*-L%kjiT1=L3e[$i=;,(BR8\pQ.0%7;t=8RM0ARMVaWOUuG4"e$hH?L\M%C)*T4'kEYB!d<H,-D3:K,,Ct8#Ek@4TMWKO@j>M3[
%$o#@3cEd!HN)1*p6"X\&M>`:,.Qm'>S6;Ha@Xn/mMglZI*\^>"HJG.;7n(&(U*M#F(s3r;\lK6TYjX:jLlstLrf\WQM;151ET8tD
%Vi5k*qF@cDn-&XuE[Pf+1`_%)<tNZ7G$kQ)O<$Ks//pqI`jI-Cn:bjCVaF4Wm?\q=U*g0kKA/U#9`TRJjj-;iJ3P`_"3sY;'H2g"
%#uZFI[R@W-n7Dq(OdrT*(`^oe2>1LhYTSi(Kg6<->DtF`b52er=VD*C<iknV0^VmiXt[41a?CQTML`jS1H_fU:f=8OdlKHnp^ZqY
%Z7ToMrY-J?JO3e@/?(OSb:'ee7)Y94Ildt*,A-`3R7BdnNggh.c(gA<ppI^<gC.A@F/@7a@u1uTe)ctogn*q15REDY_`7IQ>Bt1e
%X^e64U@ZSlorl:$mXK@B/d(j"qNf,>:-;0^l7'tK*hB^mIt1+phb)2-D[@mgaa'\:6IfSj'.iSDpa)I>"Qg^^mU1_WLa]hs9lr.l
%&^2lh7ItA-#"8:J(S1Z$LXX$Q\Z0.\:D<3H"RN2Ke\`+6nLaW#($!)Jd9_OU;CRL28OhAgl<#C_8""J%&TBr,b2Z],;7T`=oYP<A
%Fa[iNiTB=X2#[!cY13#[AdBm%eL[K5F6fei$'3Bm>tA=[^RkZ28RMSEWUq]h_TU*Z[6Qu<rBkV7[*'7oQfL':8(9=6e/+SNe_e)n
%g.G0OU6"+?*2p3H#B1npA\Yr]PhLoj<!AZh?JD$QIAW$PZ*NU>KkDC$,OIk@]2nKbgP%LT6EMT?UQL462G+DU2Sj[R`X%f%iYS%E
%(gn&:"\-Y:a`pDM7:Ql-C-5^#O93$Dr%O"m?R,=`'`;]#XH.l2HX?`UKkZU^OHpsaL7n6K%GDDiOJFO+:6BC;beP?D2!q\]Ba_JU
%QMEL:NO@\ZR:X%h"**(#6(I&oXF7cZfV@L>:mgKe+T"q!_8$&S18Jc,09UdF%ckd.,5'Ff=7r)\gHR#r1p7%9XL&Sl_;s,A5ZLcl
%93s5-H!,7<R\MAN`B4<5Ms!3?YNs.i+<aj?m+_17AK;^G\fc$4%]&"i?rhQPo$Fl0?B+-Ng@i1V>T(n;MuB4G%?mWu5<nEb/E/Ns
%Be1'YA@09&c:HOpfT\W'h;f_KpU'Psd&#6f8V=lJ"+Q-pFk7-lSMKh1B)eFHCGL&*RK,hKFs9__8$?VF&gYdZR0g/fFn1J4r4srg
%E:HDVlssXb$X`pfKu#BM0B1[o_c[`d^ne8!<9_@HL_L#^?Fd;C*[H1c;=uht6YtFENSDo#_M=)Eq>K"<52Q?.Co>`qa:fB4(LdAe
%fH4.:?Yj*dNOClh%'5jkhjkUsBW;<`WceE-PS;QpaGNHZI&(j8IOm6:IMkFM)Ogm9Z#V9AQN/!$e`p3-'p7=ef1g8Y$-6Xte.pHo
%-K[.hjMBuMlbrRuJRnt78dMjpAr#A`^UO*0r@0q.(`Z8FZp='=pq[[P9#N2YR3+3(m"<\X6F8Ae;lGnd`U5H%A\u/PR[e(n2E+)8
%&*BQ<e!@UpB@'2(W9#`m[I_[M+$,:`)^Mjd&Z;OI$SX]6W[JhaH]58VBbH*?V+gOL@isU>kD'IBge0%J9FH=NL<)Q&BL]/#m6mVf
%bP[kDH2qenZTJR`ZUWdAP%bcQPJ+DAWPddW]o,!cIn%lc+-aR0X#*goaC@Ub4oT`*(S1%NA1WpajCNf=pafREL2dEg)sh=IV0R@L
%W?HnH*Zn51N%+]%i6B`cVg[8?1?pj/aDuO."\ZRs%"eA=]TOLSL-Rj0]pTh&2(qa->r=?G+^j](IPBoG#_>*7`a\,aZ!b.,N@6Yl
%[#?c!6Jp``1/70KVJs$Bkh?)OoK[[)5kRl',p3+;MY.LiY^<5r+^CbA,pl?f:_PBffa_;(=O'&e&@[o8\b+.7$R>I)Zq4M7g4;iY
%IS*a>NiYG,F]8Rn1J!]8h80<-kUe4+bWS,SkC*sG/7P#IU'r^Q$j0aSplN;//]?4.b4XBo,=+R)Vj8rS/lY>PZb-rFLip-(;<TFg
%j)c>Rq%Sc+oP@u$j*B<(`dtJ(O(\tLaRD?`[2_G(@0lf'BqWr._)/)rg3+_"9Bjg%839;2n$g;tQa+c:)cKWSpC?_\B'iVna<d:a
%Gi1)p#6>C8M7$_kiiU>\a8[2YC!3t'X"KjZ>'XA*S9h?jWQm-hXs?_jL:VMQ[\1S!\^ZK7L:^FW%.Fn`1ul[_"<WU)PQWtok4I'b
%3sQHp`m980[hj[IM8[\s@tRC+DVq4Z+OsW4ca"`QH-Q9n=1?IZZYV@Y7OR^aC-md/TZVkKl6?-9Hb@h3PI:UCKhK\[J<#cHe)ah4
%q'fH,V/MP&]ZD7O`5o:_fs\_C,2,JUDNl@]-cN*r)OCfAC1&,.O<mt2(pkqXhoRK[iN^5e1"+r!lLG_oh2MPiN7</0,5T&.T2qr1
%KaD7:Pn#Gr4ZL9=mh@pXCoujo=[bh;lBg<J)d2FSCYsmW3rYCT%1tP;C1rd\O`BT?7FCE?Gj$8<]J9YD6#!Q\OKN3p&B7jIJ@nt\
%(h$:=LG=N<AMB!E):=6G.:JGk>0Qp9B(RL@K.nYsO1(T,7mM0_n_?cpo1Te0WlrKUNSB(+e<_)iBW;;SBchgJaBJu`ACIY:1Y5/?
%*n.Sn;]ObE\8[hA.?VBKRfbodl[C,O";,+`NPr">0a2<U>iAH<LT2@^e-M$f?R%Nfk)mKZ;Wlu!-6S.)FU(\J@o#S!p&4X"(Yo"8
%`pJ8Mi[2gieGZ01oQ)kg(hTB51^@#%2?nHP!>9CE)/O^=33pU$XBc>`$B^<JX#WZr1(M<1S-YTM@F&3H^$c6p!WYs/LaunE@Y2U&
%PRmsQ<,3@6LF8IUO>R[e$$L'Q^t=<MeW6P0McAD15n&5nT:gZ!Isr(%!48TP?>EEl2N6N)*m(Ebk2G)P$mMn>T2r@>'2u5"Y>l/\
%;4$@/nc2B>[=m7QNqBW?Ylnla"U;ehgB?D1_&tH<'!m?VTH'&eU,0ELl`WVi<s_+=Gg^:op60c'6:t<VHNfT*FW/,,=`]n'`PMuU
%[g_8XQ5,BBlr+&cbi+^UM18:S`CL$9NkqW)_;+;>,%"T:UF%5i87*Z0S''Kaj#2%_'Jsn:*F36L`Xc4)reftM;GT^T9iPqeP_H(R
%^NTPuj^[G*==i5Uhac3uD3$sKR2Lg$*nDr2'jq3Ijr"3$WsNE,Tcao3;r-uD)b[^,N1^OZ?>.jhPnF_knj=/8OQ6M%HIrt[=A2M>
%%;[;=QWW6@R*(D=S8sT:Z.,ep6)e\#&6oP1e1[42qKRdWmOLSkbZQ?fr^DXDVXa3P/M]qEr1%)M]>3:(?ob+m$8;?3?$r:gV.4:8
%e$sQtQQHhi/Wq=,a5#8%6Om6N!!"%KA$%UH=F#`G/GmH0Csn'3mNI^KkSEJRW?6sT87cc&$:0+>89Pel\J[VlO&i!4$VCV;Yk(N5
%h!OlcP-"fQ\HX0N@3@,%+UB=_SFbKu.hl7Fa+-Mb)Nr=0LG8bAqA&N<-(5]l\%*!??11!rRQ?$87L^oSYpeNCB>4Q=Q")1n!N"Tb
%8sW[`]i"qU%0X/5'gOGi;+_Rs%.:o/.4nmpNTulr26QIu=GRI!dU"4P%4H$)4QR0rE7-!\etX:r7&iigkU8htH@l$D]&K,&Yg\t+
%?S$)DTD$/5+-P&-$5TtFeb3O<dQ,gphYIqYm7<,gR^;NoIW_mKSfCdM#Du9[BISX`PP^2gB5Y#.DO1@lpp#X.@&0:pLF%#L8(_Vj
%dj'ZfQbOgY)2paGRe5=n,sDM5E/)O]PaZE!NLP!!Z:u/BeAaW%'QPZRjS(@s2^Ak-4beE81Le1e@kTT%V[.M9m8_Ho[neGFf\%";
%Kqb/%/1EGZnmmP8mb_23F@^Jm"#L]H\Ld[c[=F_Xp(5p43X(>_i?BG'lV"nuS.uc,Nt[p8DY4B6E:@`Q:JEbm;kgRO\k6NkA6XHQ
%m,=^D8L96*Xrc>"`Dmr].;T't.)0j8R+3'K:6\YpPuVS?>a8MU!eGlIP_`4sfO]7I!E)ni$r]Pj3f9qa.cq!!-B>4.G=,q25/Shp
%"%Z#2(tPJ-GGD@jDdg,1RP!g6p-''a9YM?q&D`R4oU:"CVjo%".g1cuoTCpsco^0\eYdD"_3QR=,E[!C$5ga)Zc7jF6e"D`S\M>m
%=*Y^k1%*@J&FV[H4`CGXEdu0L26A/Ec%ct=Y1dU]1Ur>F=b_@bX$>q@lGr8He87@0<-^"V,Hph7XY3LHY&#864N&kQ7AoK2(B/a0
%biHf)^=,d'_t%a0W01Q1Iu1)J0t@+':.Fh6#D8,1[S&uT2+$3dfA5k6RVG(Sods,K!AkjId_<[P;E(ZX;5k^tc*iWqcKc>:.\cWJ
%_-d('^989i_>6>0"s@AcAC)S:njR5,+pA=ZO=u)]#okTb$\K[cj/K,X"7]tKit/CG>l0[.&XC"aiPg#i7&(qm2/5+iIBK52&d9rE
%lB*04qLDGPg.:o^.rR_$\^-oi3n!+4"*:p%YmI]QXPUu-f'uu>@*j4#@Lp;ko23-trKCAP6k[r)Jj'#iaBeJu#@;`sa))TtJ\PR_
%ZpKS7P*.c^bZ3u"P7pZGboQmuo'T;\6W4daB))'b4")c&c@s&ZQ\ZYt;:(aVe5b;GAj$@t3L3V5J^6iZk1Fb)F<+6NQ4=lpSMMCP
%SkD)k8422)@<_i1eE]*%n<IE0`VC$YB?(>F&k7II]WqemfnC\]]*ct#Z@:%8r$nkNC'U!N!fMk84^S>W?U"Qo8TP#kgJf/E.t=Q!
%[YTn#b:dB<^AIZT7K04F4o42PNs]?h5Do2kgu!Sqn'OCHq%*L`q>3iWpRcenp!\DqE-hF.[*Scm6Me#l8:N&ZM4f;JEsW?/kF>Q;
%EuVYXHPLl_S#qo3XMHYq.:L0-+&p0V8=ZiXS6?9BoOo3eFWlaEVD,TJ.s6muT/%E"inhk4/NE'ZHU28dS3+s9*-KpEYna(p8!-]X
%$(B\)D.oKFl6X.(E,7jkX#an:MeYZpiIogJ:,<Kd;MdoHi&H/prD'3/g*al$o`.sKs#qZT-3MpIlRp"_EAACXBaLPU"E89lqhWQ%
%IUJLY*t^r&52B6ZDup08/5.]CcT\(:`c8AX2'O&[7eHO4#O",e4k8u:C`bqsTkWph<ZN0bWe0/%;X8Qb8VaO(G`3UC-+q;If&`8<
%`P1AFkgFgn7&lN&c]BPN:AeReO<*jSB=LK">G"YM,CWCn_6N0KW1=FUSm@%(NnSRI%(?DWITHod?Pm9rSdJK<<"]S;=FZCLS"U4Q
%#Mk=2A?;`rf%nK!CBKO7nGXJOSYaf<MA8$B59nU%dT(ifTho1ii[<u75ZMr)6tL#f)q/n4Gk\,_4YI?_&VLl-kuT0f0&Eo&icMCY
%)XLqYL/E"bj/t+g\/@PZ1i"49O\KE4(j9t5dl@&aZ/hRBqpmd7dsIkgXuN''?H,>_cRb3Brqmo9fCQ`UXc]XA?*+H'89^1]<NZZ4
%.AFCBjg)CC[fi0Nd&Wj8?c:CS1?XW42*b<V)$F_/"uhgM<BT)PN+VjHSIb4l:IIBV7:E^:NcVN%![(@hCb0R3\R=7L8V8.`Re=3%
%8cC-Z0eT8co"`)c$P?ZZcBB@-G-pG2ELd7kdhi)WTNNhunHg3!960N_ID$XfdnmW8UUl`S&/m2!JL;cjeU`=="qj>RH;fC/)j:V=
%F4n<bBplE8&haZp/ud7&:PlJVJY'Qn1:kN)0hV0m+UrZ3h5?V[$W4D/+]aRE^YMi2PO`$M-0o>Ih%p0-5"!orbD^'fGot6'Fno`#
%C%sQZ"/7,p?+n+&Y[q$#3Ih2sr"(ia*+2]D;"cW=bNPcsWR;j]S7OQ.Q>UWg-XUiXB6t%p1qf9$Kkr:=CcC!Kk&jW#YWrT10l(ie
%bRifJ5qDqj<ORXG,&XN;EN<RgX)-DbXtg@i@nBuWPcnHu653X2W_KBeRL4`i!Uc%M\i&sL%KqA6JPo5oZ_BWfPX/Ac3K[mO\cb$r
%GBQfI?jd4s!(sEM]:EB5#H7>mO=D\RaGbGag8Fj?e)J>tF+Y=&]o>q51'T!_0THge(o<)-Gb@5LG#\8jc(7n&QkJ4RZp1b`6eH`9
%5u2G_E!pAoPd&+I8Yb1Zkd.HIJf+.UcE:UFUcIR-RXtn+0IEG_Vf@(`]m>QLNB\`R/(oU$r7P6PCUp:7(190QJ-R6FqkNL*DK%Ap
%8f7V[^^(sg+\o1hfL@Gs:D/6hZ/=Sk?_t*X5nTjOC81Vrhm:4G+&ZoBlYik/$6H,XLFO]75Tma3<19CD8,Oe1-sX9,efK,+!N/Dh
%;U^XbN(\"]P'nk/_Zb'I8-*Z0Y=RU^85>;g7.P\*I_$^f,=/(NDBT4X<AU3Ff)T2l;a:OpI?W4dF9(An:""_e!X&hq^>iX#E#pA4
%O]cq>\LcJ:Ll9!m!4RY=5aKd81qqd[:f.\p],oVKd;MRo;,kjg'mD*)RE9j/[oNd.,1+deJhfm1Y=]=8Pit6"7;4gN1"_grhI]VD
%[Z&tAVQ.nk7e&/4HTKL9#WqBhE0\gGm`_^77[<R(bEmd2VS1:op(N<T4_a\(M`tRnQQ#+=Q(=(_JI5P@$%L\Un0'EH^0K(NGiL/M
%Jt)tsWU[uD=$5"pQP^LUg#`L8TTPR8Dm>h^Z,&:EY&&2-V!=]*UM-o.gpC1o:Y]Gi)-V7gTT0sX%fu/@]EuJ\4dGN&j?/UH<3e(]
%cii9p99Fr&l4se'Vo[6$5jk!n(26rl\*F-u%lP`*$qd#<Y1%"%\cZE'qi$<T3Mok(MV%kLJT((29u4&RL/CS;h:4RUXKN*deeTr9
%fRp[K`O_OB.cT@':0jr9d+I>rm6KsSC]XZ#K%*ITj.#$Q-n(!nk#*WuB5Rk'^MjtgH,BT1iL0c\:)+T*Ouod]B$=Y(#_F$5EF=Xb
%"a\^ndAEi^:H)6p'&M-C8YrL[(RMO4BLbZgE0JD@94Re_'9\1X9p-Ouia?L3D)aIs<NSP]+S$S9o`V^5E!3-K;^89:<G-t,ZYn6Y
%LJ'F.Te`6dhqdRHia0dhMgD8;,e@=[l*;-Fs-UmqZ(`PBaDtCE5J_1*);M"(\k$IV5MS9:+CUf![BVLRQ%q<V9aR8dm@==@qef$*
%,OMF)&1n.Nb(4u#-eK&%J*]lZ^b?+j:1IkC'S=sA=+^H-aEfmIi*,o>N@>K6$TTnSFS@*EYj:%El)/h?gh\Kf@hUabbq>T,6^p-u
%igZj3KI6\Y1VZZq$q2Ob9/ij#SHM5g.8B(iDUcXKFQ?B,<.a\6>>e+Ca),YX<ZoHD8l8rQ2c=j=UCfub8ROn"FUUQuM&g+P]2J%1
%PFZHUaHTiMmp`1XQi.?io8=^-mR%5l&9QQ5lXQD-ac,%02K)PIV6BF8lf8]E.pt.1WNan]MY]9H\C:JKe^OAeV@3O!hHrTLl-FB2
%s3OcbG"lig1`a"1LtLn4X9rneb2IO/G]Bu%V%cF[fL!rj^bg8,:KR@sZVse2O5N#$U?P2_!DN`e?3`Gdccfnu`p"!RPJimIq1o.d
%7P!iB9cBq!?%:.5IHlJ[DKBO&-NlcXJq]q96=cOUI<rQS%`Ld.)qC3OgehF4q1V\J\S/78h'9!DBE/%q'rM$BVh-[]N(Or`"NljC
%)^>3q1JHmN8?X!n("h'Da6nj77uJ4f@O&F>49?%CH<f3%5Kq.f?W.BXH3?)]d5_):>Ke]Vc4P=\f[WD\lEEAJgYon3I*n%VQJ$+a
%P9T8B"%Oq)E2Pq=gb<F#)t+]+[;SglN=#@B`S;dGOEQg1:^TDK<Z99YABNb:"ro)olZ^B2ZdW9j#36h@`f^\LnmYd16o<:^&W?;!
%aeZr,BYA_n>;H"p4^0`^IXq)-YT7%/I#*-440&282ao$U1S:qq64Wt.PhXuTpXn9(>XrU9D.r+\]ZYY@X0e*C8$hR;VU$uT&WBLj
%*'@cDY:s_)&1YH>]g;PckqTcD(6$DeEulcYKOaQ$F6;);;]=^q;D["R8,,sTTmWhM7ufDf_6(b8o-%m@$5?,OViD:Z\NtCR[OdTn
%HqFV\d;--\lELmZi/!PA;)U+M345!^luXI\7.M[Rls/'PK&46^dFg<P+=EWfV-4&0)aNmkrfE;ElMDKM'+P>;GDVl*";1[$cGRk6
%,Ns@%J#@ENE2FX2>h&;HnUV.oc9cKA^m"XZbY$c3d^49)%PU68@C4bnj':3Nlo0g"Zla))G$mIu,U'0B"OsJs1^D.1LS(]Lnh@9_
%Q[a66Y-K_5G$UV=1M,q!'j!U96WJ1;7\*J8\P%MFf6Du3*;7NXrbUdQ(CM<qjOK=9(Z*nrMn\u<GW55J_/:E$c&Rd:X4bRq!KGEf
%)^D#HI+qtg<1+8nAamE5=-5oSgRNpBJKJ]Q@&#28)bQt+Ca`"o:Ln5^U^)GS^3j)($]=oLQ4pD]EjZ\oH`4_udid$$PhA7?CiGg9
%e@0(VUO2h-V"7?4R[T2;`NHesH\-'4hNpmG0.h=ci.I23eZ5Cf*g5YcZm4L9\q-[tbdlrnQ?9NV?4<^edB#BOoAFRJ8#u/0>5p2b
%SL$<6<&G&7(d*Y0KU&ahFu%Y'r(BfQf,-gEE]CK=J&RK!(QS'EQ+u5-GY1b%K)t@UjeoZ/:KRI@f@P+0Ot;4)g->T8:Ck`X9`bS?
%3)]dnQN2EKFbUN>KV8;_rV47-es&S#Kst]DD3@kl<IoY7Mp-a^hS>l;V.0Mo@Z=mZ.lL6Q+*#l>Cj"Vk*QfQM2;7Ls*/fb")tshG
%1q,RJTUrNkg6CYqqUJN6].GH]bOJb15<a+<?plB:jQt%4C!]H"J)0KdaKFUH5R+NpWI*9V%:aU*MrXnmD>qbfZHc0*TG6*I).e^)
%&GDeCb["tYP@FAgEp<j75H"cieCZI*83XkaYTu^DikV/@5GG:!'2T0(`gVb3$U.+$SUq`+/\cp93uh:tFrH9U9HAA.i5*>.:7WY(
%,SNO5RNj8CFb9=>KAj?piqj:r:Kim`r::`%S9sR6\f+9n&$FOrX[3U/@X?U*oI^0/O:t=-CML#Whneh"=lE-Qe4mq,lhB%<9-ht)
%KpT'7`p+dhHu`)GE#R,egNVjI_ER7D9sLNRcD>:[E8:KS,\c[soE^Ss_5<S02Q<4J`=M1ag5]rY:DNC:UjHs`4k_]2=Za]mWl`t3
%'PC'M1h'\D+Z&"W8[!EO,gK>:hKTBi6f=IM$,\Ed,JP#'\`6L9,"+5r[Z3m0K=\a@<GLu!%DO5:3EtYUMFLg/@cKi?Xol3hF^1RL
%UWlO83?GHXPbM4D(r&fZ%9!O;,BQ"6<m]@q!JS/\GbJp;R/QjLP`d33`_FJ*%N:>CZZ$\N"u)'F^7@Xd-Q0nt9(q5fI>j\LH$/u,
%GhA2_RN+A$*[(6*f<4SW2\uopDj@)q\0YiQedQ147R9I/]_1P!_TnSYR=:IuZLD09lNBG],i/:*6khtje,K.Oq@Kn"RAnl=ne_[g
%\en!1_VA$N)o)1W;l43_3$1GC8*C4\N%C@:HnU0inRKC)R'0fT7]P`Vnc24J:HuWo?Nkmpc5*d&M;<l\mW^U(7qI!k8I^sYBER5k
%'N#pB&1s!KD9\kZ?XP/R32c^lTZ8RH(09=khj@jD+?a(1%5,#Y+Htq(c2i/nC6?"Y!$ei5=ADp)Bq<s;N1a!'Xhb1sWS9JA9W:JB
%fi^Tq[j9Y]Ha"0FUo(j&#M%CC69?n]G$JVTk_$89Yq+MG.W*P#*_J'm]XT\QYL\0s#i4=80^]4IE/3ieHonoE`&15s5TTHpGM1VK
%!.qH^BIA)Q(5-e6%mk&?O&ancc^cUE2/IkpiuECLk<#_8+tR9DSt@#1,(9[-]1;hQ8beWU6TDj1)=\e;KRY*8q1m>3"Lsh)R,A^Q
%J&)Iq71hVGlM)Q/0Y3#9^'m;;.h85h/ckGCdt+2e27n3X(4RM-)8Y`O?`F1gJEMljP/diQkt@H+7']4oO#<fe:kpV-.0f)XJG>A]
%_ZSCf9C2"cG;eicRf]JoeJ8[+)oQ,Y3^44t6a>l%XP1eY<K=H2^CND/(24kBmt7E385^:!WjJ=EUTN*XrWEDE>^d$'2L_?_PF-c,
%ge,=>.9.r!WLFl&//=7UZeXW3V#$AOG(FZHiB:)h&fX?!,BD^Liu'h-O1e!i(_?OLCe_nr'GND8Ws9b]Jd2Q2)#cB;(u`,/H,E(@
%c8(hY(t;KPb.s7HH&"'CKM"Q0.2E1+6NrR3J9\\"O.K_B[Uo>VP6\`&O`uDna95`BQLZ;Z`M1%:;SN4<Nco>hA),@b0j7^p]X==,
%H!DjU-00"Wl])(#GD[%EGX)G$!%HHo$O):.X/AB8$jpsJZoKF$9<l$4bsk4P8es3BKL-<G_5)qm"BW9I81poOJjTVA7nO\_W8@aU
%NZqSYlrXsV6XRHHYj8`q9i)*PVYfIE;>fI<,<mW"=I8E0(H#[fC(I)lH@L,1r)t9i@*4Q$@t?i>1$i*O(^uh7b[EY(j^dWdibbSg
%R]A@CCqU%n.1uJ>d-;*QHAe(,)K.Q;+8J0C"A;nu$R&l7.d'u_i2'DN5+d!KQ6M7QGOZ/bWYArc6(%bVhqXcq@r,j)b9/sgQd69'
%l'1d'PTpj`C9uMD5&(ag\!2A4jA;%#)K^DUWITc\FmcVRYrDH\-ED`\#Fd[d:EI9XGEY6(<:rW2H*<A1EE"[35jq&X.k#0_0*J$`
%!AL6cWTE#(aT-LiYKKI'9,]D57,tu>CkJ?,7o2i!=\$_u_Hb<B<usCXJE]qVQf24jq8,nCGM-nnYXX;^9MO/!@fuj$^ZIWijGRYq
%U5kn1hiu,'NTLdD&;,6?Oam(J_/9`tK[`LSp[,m:2iMh016K9rfsS0c,Z[EI>Xp,gYkfo<1WWQ-4"0Z;=*;holG?>*<<n(qYOUTJ
%$9eXAe;J76h*FV3Pb\_H1%pQ=GkP7k*XnTEC^Z-1@De[<^p=i.Uq+*sJED$3;a;m1m(M)7gLVfbBAmYj0'mG03"G:VWFti-`8!1%
%G,3Boood_&fHQY?P-::jft>:3[giGa/br6\<!Y/uB),uLIZ-N8DVOd,pcSB+quu,aW%ER8B2lj(KZk+oLG8QW/mZ?NS,4=tG;bCR
%@!s2:T"JD9cLd=E+jg)PdVpW:EN1$_NX^qK.]+K@]+U<;Yb,H=`a_nO`fW@8ECbXNE>cpJLt>)NE)`,@fqf$t:$jlY+[NfnX\Q2I
%5t/5L4cKAJgSY:L`8iWQjI-'flR,)M32'(;4omDs"i->]kI:de<6-h+gCa&g>X9MOG?l/=nV-M;I="oC"a3c(5q_s?r)&_0Yd#k)
%i/k,?CCCc32[pIgd?JEG,d8WO9Q8#uGPq"U/,;&/J[,B`X3mEDEFM_;0(0.UW;'RsH&f'_NV=3hdHtYJ5j*SI;)`qr6:GWli!J7Q
%=H)DCUbPLRk?QZ(,1Tp9`=rZeg=ed*A?`<EMAIL>"8Y`[05\e0qN-@'YIkf=dYhP4k"%)=$u1N1#%o(R)q]WG1%*#Ic8F\Jjs%i
%,\t:1+/=PpPd**%>B[/]Pa)uG;?]q)42gaU$-LF`3!/K\XJXth>%"jAnu#JC-aKj_^$8*u<Zh;ebKJe^GsQIufrGCV:b*Ys43n'j
%(c5D7n]:ORQ\8XTY^h"C;*/2gl>Z>3WaQj49>o6M%cE/np)9rjmSfsej\+BRY(F[4YQ)P7pWo.T#m,QnoD]Wiq&_]l+7e47QUusg
%,X#g'qD%?tGS'\re@en9G+m/9_B`KunO'l;]#Tr8:eOqCWC$["/*f<M'\."1PFQ_=.b[HU9pPmd=Ws.^_:rYBi0r>))Y:M\B(\#N
%:^qsKfn2CQC?.3T8</a=VP1[frd?nG-+H-F.lsKl9''YVNR2p5pR9p?/^,NN"!+#bRa&1sA(]2>qOp93qMg7^G/cc[i+]/OYP]-3
%?HnF!#2\Dd/H.WN9$nq%/Y`XiqIP^:\1]^LQV<.X/Ufb(La=;5N1[;K?]3)p^O-H6Y<SpqU^S+C\r#*^[]ip5)W-"IoM\Hh0g98o
%l)IeX+;OOO.:#GH.Xju,FnO.U4P.=nS`K+>[qMQYetVEkq9Xc]*qJBn)q)h,qA`oYG?pPa1H<sRNJ,V.<],a+JoQ6/f2a_\"CfaB
%f4^.udHU`dOpI6R?jhA,TV[Vgp:9e84b#S#@uWuD]D=TURSc5o7e^usb#<gIa"fGW,Nqr8:W\G90C5\\+(uWY>)VGQ&rkX!=3X:*
%@2b-eD3Zd>(%M4qU`C#T]N?<"X)*b6+)$+:Qe?l-lEc:;ZSIf)F/ErqEnY3"qta*ihd6b9#rtDEd#7fWcZf=Blq7IL6[n>;k3'oK
%rb]&V_PCYQCnt4X>lHl1&*cd$IZZ*\'eqYQ0\R:=["&gd;0t&<b\#iGg-td)j!'18TJu!8K!?-lP-goiEs0Vce*^KI,<?NLCq:<h
%/Fg2q9@X*tH]6<We]&j&4$4)V&2o^D(6?(RNu&k7`#NM:r@S:fp%\!H?=P0)pIXD)g#Y?\o;qYZ1SY.4;#/J9QVf1])Rm+lOM_hW
%d1f".]D01A7k>9CCMu#(>lA.FEdNY1%VR>,cOj.QV0toF"\/!$b34ccN@UToKQf]QNgIBTrU#RJBi9bZYL6e&h$ENt"d(?(9d@e`
%]eERr8LgI1i>@+KeKpHOArRBla\3D+9<Hi1SQORr-E!!^_I6^K>PD\:gMT5'Gt;arr4/pO^[aKtl"(!Pim_'PdSpuF7AZg8G_W51
%i5b%pS5ktt4R?5DUKW:.LToZ;a!q9I`<U3X"E@X6C0tHNX_HM:b4e9W714c=2UB[/*#Du-@S]tgh&_,8;>Fls0Nr6Ufp_HJb`IMJ
%`r(:5TqX:JmAK#l/>Y#8P]gi,VtT<qiMDd6rPkb-,9B.6L0Q"THGT6R#o1Wt-0#X)1YIL.#i]%LZE_RP_*Skm!e6X&l^oig?d7\g
%\tM*G+_:kJs,UT$mi(Sr`'Unc`=pmM9qtkKQ4^.SjT$P7>C^NAe*eAO0Z,=<+K/f=B%&9LM'D+rro_"1MDGRER9U'<W<XH.9`,h/
%?IZPC[qMS6ReJhNLJn?3mlLEf#[?mRY?kV:"ZW-ULRDD&[Sbb81ZMX].aWNfC>$+`".8!fb3lUs]/:3NXsl44c])OP8kq.><^AD+
%A&N<-7qSOV^n6&2?d5DK#kq.c2"ON%eY1L"Ms]%-lE'q<I/oqhQ:sQu@MI9EB?%i>!u';mHZRFL"\5gB&Qc@aOHV(1W=I3(7XT?L
%d9[jI^j$\nK7L)2-ubQAKWK!t^$`DAp3I'[P:l2C'.\aI4cDHDJPjP@W&-B_*b"O;Qb>=pY2d+!"oE@0pcmP[0Br`6g'#`d;`WVF
%+t:X6Ot8do*ic=L#7+R+-sbaT!)S;4oPa3RC[HP)bR4t#.+9[!Q3AgMSijnSdiR(@ql/5JR*J/B%Vf_+n4]s#cD)n;cX]t<`^Pb6
%H5AM#A4)8eZ\u%nU2S&S@]EpW$uh@X!+\j/#^]5tJN#Rih)H1N?UQq9%.[q#SS;gbV?XMp2#LS]?!P#e!f'$P0:J,H_agqq$B77`
%/L);=jq4D$kV`!!N,XE$d*g;fE%$Qk3X22ge8rA-$`D:-jg>c?q9d0]"PfXq=@-/b`5XTpH!bBg_&LJ&+r44!@KjuNC*eO@-%MLT
%6!ll8=$VZq"$Tn:HV#9`1H]b[@HHR#JO=4-]nsFYP^<CMAUfki(P0k`LHoSf\C,]*"T2HR"WoA"dZ%1#Iu?j<Q`PN=\ql5nD%)^T
%UXNo-M3Kk;b0JKh^i;PM&tWJGesn0#LJ3pE9brh0p8!L>XPbX)LTjt#IQ"M;^=Gm?@1^b7SoIOMPPLM-F:@""9HsoPO`@1l0![>\
%"ab/GCr)6C*&uCA&FAUt,[R]"SYLt5$,Zn(Kf0.rWWVcFEHh`]M;lF5E[PH=P-<$+6=Gn2)BgafZbZ]q]:=DB7VccCf`WHb40eOB
%;bV2Bmgb&7-Sc-nJ_!r5c^,8X+V@td9fp6)<m%#0O@n!]W!,s[".oe;2e!U2CRMt/T"s=T9$3>h;aRc8Ict$7Th/WdTn@o!)8%Ol
%9c<NkpedkR1W!QC&U#F-090VS"=<"6WK>hi.S';F7]1f&51:1BURVGu0Y'%A%jkaKD,LG`d=S#L(86X6#@r&2X<8,<5Q4>gC'p8h
%?F&q=IGrshWk+=?B7eV=8EV5YLS9rb@4afu+u\$/!WW?.V\jC/V@p1)%`FTBH(J,.Of@!OBem$5M,R?FoOC%p6Nk?Wj0t\P`IgO]
%+?Zl'EWJiQ`M@`aIQ7$*@dXg:_8'cd9'@Y?j`%sjS?_P23J"6AJ=;6b4:,]B:Ir49ChOGXA-?.hPLrWL'kdX,>=!4#g?"lb%[7N#
%Cj8"@%g>`i&otMj#*Sm=jUAC6?ZXn+iL@5+$jX#/cQ4CnK%+aiX3]GK&s<fQV52$)MkH5[$VqdATdKOi)GaY*1NEfYn]F/+9RR[W
%:6GSe8s#Fi?Pm1r7?rse'?e,J54WJ0Et4;NNt1VF?`B%onu7jh!%KqflXW=7!>1.*L,"nq+?@N=(S'<7L^1$`Bb:JgK97,$`(\N=
%VI]?'a(((JcoB$OS%t#iK77`<b`RW^kS:FN"$16Ri^RLS9,>[M>`>6HU>B@TfY#T>cDVeG'.+rg<3@t7-6/4YKCTcN'fR%Nl[mM`
%oRN'<%l!UW[Ekl7)W5;1WC-*A8@Jqk].#s)Ao';EdoJY^/i+$>5P,-tYqN.i>&YbN"n"6W9-34E9Y?)#TdG4Sd2],p$2K/d&RE$r
%.N0BQ4;m2c!"f6&Yb*]::X1j16'a=+lW9tmK&`dabOd_#L*a$q6KI-_'u7Hp`2E/[,XF[N2(ZPjnc043l5o&T:U[:lkffYs5UE3A
%kHjM[KRiF*\oAXi!&AZCcYTh7m*s?TF%YMBjRrX;pNc&9R%KoXlp0kc,<8p_"HMuI<Nm$^J:e=.&98)Z/Grbk4E\L2N(M6d>[bo6
%:@1s[kmU]R`6lm@e0Fe6L"eq:kVsO6asM3O#m20Y9Re7`6S,`Dapc;DOTK5=4,SJ>J>P$Y6XJM4]OB+sV;OVR2?>%Z8d#QdeuT[s
%)Qmse<eT`OeJLHKM4CG:7,)Bh8`WOq:5EN68&9@!5_tBIPf+:'$8W.B@ELKsW`UqJDN'=6-(.,o?*6k6#KGri,'q*`Tk#[4NjTU&
%Z<SX#%rpCn,X>ut-nuuqZ>Aq/F)h8S]3nA;"I[\3.%l;^#rqL!2M"m\Bg?Ir/=qOk1MB%TUI&$IUYuJK9`-O0aPO`f4_jc!(,-RC
%:=T4%p2Hqg\!!"V6(3i7$M4"KPs?*@i#VK'!Z!DN0),kKPC]-kUTaG]S=?Un_0IP2fc27V%c,R0ZBiW<4WZYd=Z>l2.@_X#kUga#
%ABp9cdSO?t"aO"c[o6OqM$Oq&"PZRS9pgYDN]*&1>mH?9`3;`^@6RCh['?MMAM$P>Rq\%!A7Im:KOcULZ37Zk:44lFB`XX;G<THZ
%!i4TQ4ju[tKU^U-.tNg+]jhVL@"T)?8UC8"M-n5'SR`)5TUHf)n4qr@q@4qf%#b0Dn/Ma"&SVA%CPW+;\HJUIg)7%==hW/2Le/;e
%#Y::c%+3d%:O[iN2BR$92e^=;!BsJgh5DWMqh2XE,tr(\;]IR_H0TWuf:E9EC^9\R_:2s*+N$pMb>V_8;P#@i]>;54F2>Y".LGTb
%J:B2GRp^CCXbC:1*Ajc,0^5qI:V?M>lAJbN!U8&X?jD-kJ9GuJC'[ohLf0-S+:Ji5<1s?e6[h8g^68KH@:><e]$tNfM8ZI5HBQ(#
%aVUXb1H,$P2(7$l$ITL1^668dX_grFVI7VW1(NTWRgY&.Uc@og&4C=>^n(t9!9&>O+Q?IC$*\?m\0c(9i'9!hI`fY+6u\_UZ*"2[
%<oI'Oaoa#j^p*2s."P<3\"kWk!i!;J2sUAm3!7(GNs"'0i2U5'30GXc5l&u&*XF`9!bV^VJ6qQ,`[QrYfDIoLBr(eBNX4M+!ho`/
%fk-4hLFaT\#JELb+J`l\g1A6S%sWl&pE>[*=$^\B.O(``2_pOK-hi.I)m6?qW'TWI*lft'N$7At$JnSc.B!c69:1*E)krLPLM2)O
%&c!f+:*QA(jSQJ=/+\W/Y&k,[ok*'Q7_7%MpOOr\PL'jDNQT4p8PnRlU:ST-0DYT:B+_enC@m)9A,.5r^4O:i_oWc=AQM-n(m;\W
%.Jl^DUOu4qWWX=$#R9!]-'+_*Gg0CPNTa(,>^!e=USftK144+P:QBg4"NO9<Q-,Lq;A&Ju/,<0h\Y9(M"i4MX"]u4A-TSFE7^@%A
%c4pt"["`9a'S(.L)_Jm!.044M^s^WBgb^s'gEo_*96:XtT@:SImmaK&6QBe-?,Tq\+6WqVKB@I^)7qV5>o^5lO[hMEI*2BfeVZs/
%a^`^*Apb7098[lj=;=*i0_@F%hF(%qfE>s:+M40316=L\/+MJLf-a4f,rcO5;H2ne,%,>Al1tHV=aKrb\-b?QN0!enhILP$JR$L$
%T?W6:r5(V]8chSj\Rb>0PF[8,U8&[M%+7_3+Pil?naGn2Jth>,&+iQ]XCPm4&SCDr5n/]:h@V7<CfS"G*?N$edXMPD,Bl_c$..X-
%.sX2;R!t2k+QX>W3\ee&33`gYgm]Dr1]VRs7+oOacK?@DY!`nlURmjinW=#+6#MBPX<VFk5_V_*j9&8\2,3Pp(^e/iaE(Hk&#KRW
%B[eMB@OV=8Q3Cd_P4a(aL-Wd^`TaARB6G%#HnMjmL7aHuj%=.K@n6uJDRctPKVK]g-1n_)$`aDu9`t=Q\>.-tjo%hO*eU^li1<@k
%lIm?Ep9#MEC4oj:%9"\VRV;7:FLcq?F^+5(UBTmUE'+-D6ZNpJ^m#]bOCCnQ"m8L.A*2o=S-qV#o+NW?RB;&;PTR=86Daub,+I'b
%_B?sse,%(KK:NY7P0pQpjUeH>/"&buX91X'5DLPE'RaRkQ3_.j'dOZRFEm8TJ1W%e,OG^VTE$<KM3pVI(is4R'#\QBQdFtg,)B"5
%8jK$c_-dH=,gV/mV6T[06H"=2*mZ%m5++Gt(9DSpBb#cY?rE_T8DQ#N#sk9,&76&mM@I!m(g.*#9eb`<R)36VDNt;l(_$_Bj2a:+
%c8HlC<H$4#R2$QeN*$(*1BSAM-b05<;a_^dO:s'jH'@HWo2*8S)O3RVV\<hJ=9/O7H9r6s1M!j'QJbNL!.gqjIEDsTi]fJef29`q
%)V5hm=`<_C(]n?mVMa.3acq,=)B@TC$p%_9;5iD`+LFK`\V+\$@@1;d5A.!u1$'ml;Xeb\*e\6V51tJ&9H76[V-dp#Q-r;BpF2('
%7Oj.HS%mGE!0JlX!2o1oSrbOWkn=8r5naTQ->!pQQPNthK^^4=:NHOrdM'+f$^#Q)j@s:&)a;f3>!b4L&<EC@SX#l/8H<h:n=WY*
%Jn<<g>dAR+4Zjg.Zt5oc?q3!N(`;U\cGqZGW;=>PU&lY_bb`M()?Grb/l0_\/q=793"Sc9.\l.d:lUnQ,oQ1Qk*uj2VW*1Hf_5VC
%BS>@-,3Qi$a&NQ$=hQ9/_<f3eBAa2k*7W`'?ltA3E2#d.^nJq;9:_n5'O9*d,MS=Afi5-i!Mb(C+*%Y'd$\$814X[?\k!GXBSMmc
%G-D$oAqfLS7Y0Y6#0jqF(e_MlP7*[D<*eBMZc>2]P0c+b-O2b;T%bk&G(Ro7,HPKO@#X:^OJuLc\mBH>_T$Y+2<,]In4oaD;-bNE
%'k6FL8/g`;#I)N(R%Z=YOVpYQ=7';uio+;t5m@V_Nq0;da@)04@`8IO]aT_GK9d'?Q6r+J\[(]maouJ*So>D/;0<FGi(8?;1s6/r
%F=ET9jR<H5^#:DB'd@]j6M,l[@3MO5RjluYakNphMih\3;c"=I2$:#>Pho13S:3kHc<uaFr&o"r#j:!-UOS7X*3"4O2*e)bI6T1u
%;DLGXjPX#_5BWAq6O6XS5:;HlN48%4We:@IH4H8`c0_O`QNHa7:?i4`R<\mTP>8raXuO;h*_8-HA!V2,&NAT'Mo./g/SD*&Vf]5e
%`p.Sh[feIH@tcG#C`Ni)=<_-,0/[R?-e8Y-o7t0.XOHlHZJ==3Z3i02rTDB88T:W:eZk0d:S%%\C*I#fVDcqmni-D#P[NcbTD)S!
%].sCKB(%)1e"f@aV9FM<7?+ZEQ?$^G3cRcMMRd^^cuS_)VH1bA+HV8$7g0ADk'A105b@C*A]#nZ]&*9<dO.S*T-%\)41'Dc?o'0Z
%0%2u[JY\!%25-&Y*!]<UfRg7elCcGR>gSo38U/]AXXuGrS1\,'R$*P)f'-%QR5uWW^`mXfr'AGE\Vm?-'T4]qopA3g&Aor/Gmc[H
%Ec;5,G56qL5@RHWp73Ok$V*n`LK0.5",,NfMi\c&],]+Ih]O'`lcDNVXF]AM&2"$5J'8t&_IM>eoREX55T3&\0MoqV.>csk3b*Mk
%gIFi`3gMnb_CL6W`10XIM,oY1KG"DJdFf;*5rfU`1DP+S8Y';phc\_K*Zc3BY-`0M$<bbir%TW2K@GHB.d(M\(XTImc#^f5?YG-]
%H-`..bN?#cnI,I"I4q0C53Ks(SC&k^87tC;SEZ<+""/@G+jb<3%M$a>0$.Zqldj6TR[[.;m5bl$8.8#6)'IUD&5EL9'+'OX,^A\[
%o]hVa+@E%RiKVQh3J-;ZcjSI^Uu%H%?KVLa)K@\r^%;@GF5e7aR,'!RiF%WN88sA_]4bU%Q:Z7[5'mN;SgrZmp=^BJ0MZ'irB[a'
%FAN;OLgFYrF&S3]K]XisOi+!s1`kM!*D`#\=9Hi<l;5GJ#PgsXeloqh8[h,4K%HgkNj`?:NqnAja[4?)=V;E!.]QmYdK'9/Uu!<1
%)TsV)8ieX52>/?<L$!F<2:2#X,YL6&[=jS;ljSp@5*dl46*l,0Dk3eaS*8fYlPD_CSQE7>#t7;&&@PXX"=2_h-uNi]@4&:,0ZHX/
%!gsf/EV32,)bDE5k(5!,7i?p0O'_,bA#j=X'8%`ZW,g,4!,=[#)`]m.=Ft5ZSK3DMjhf!`p;T[e@njOTWh48!G_akHGEgmmOnd9t
%fb#SU.(IS;A.eZ3N"90=Ei>8Uc90KFYKRi)FLLDYHENgEI6cm$0REj-:GU?#U4CN%@AnBtn&Y!q)jh[8-<`i@J7Q$G]f()aN*'C%
%-uK;'iX#rZJ>?'/->K;NXFrc4qQP.;(GC6l(+2oX^@`QDMW;N;;Mp38iop@_.51Z>GLE&3=!PSkYS<@m<4ME_=UcDP-0OU5#RM\B
%`/po4D,Q'.^cl,.#'3<]:5o92JP%p6!ftEdUUqJj,a3I>A5bJ0%#)i!EuiFjc'Zn@;3!D;7.%,ek1'YfRQ$`b#+Rj@\;DAJ.hRa_
%DbH6kLH3OgouC+8-VeY>*L'2E:0caeB,SP)CLZ:tKm\2Q)6U7iM85l3bJa+se<7Te[P]<5B1T3o+H;?UNf5(0!]kA5ca^;YLi_1q
%EmmYtl_5UlggJA>F[i<r4kM3Zs6%[9.#)]8/+L_,jsPaX\UEX>]'*.2h30s?*U;DRN)A"LNW`nPmM:)hn8^]0Q0-&P`-Cl;b>U(X
%?Pjt2,WQ9'(Y4]i[e8hc=i!r=Gh-R0?N'3nn'bYs2C\.SOW"rL)/oRJbN9b9d@nW9&`K,jZ`j1+-?\FB]>i69-=AZ"QU.r:CB79Q
%o`\-5<t98,,hWTYL:r1\1_L@$Dfrhal<#GZ@<S*]PC3c\*7.f>6\7T)OVU_c#b"=`.L^>mK!b@H6f(pB0fd5e9Tu;:Oe]k10NFN=
%eEi$]4FC"QUtd#DG2s2HT%T04@ET]MO[O"Vr8P'W&68rP*n[SSQG"uM6#8e[TOTF.PYOed//+G5lO8U("QKtZ\OE#Lg^&5YS@Z!<
%PI$@P61f"(:,==lZ%F?o$56umTV%h`FK[SVBeQd$5=>jlaI+M_?":st\iN:gV^e$MADh9Ben8O>9-f=4bW!akhNM7iek*=41J8PR
%+f(^m5q!6(7,^0]F9_;X"Hl!A,PkuRE$XW;Ucs'[`-uZA`DWPH;\<^d\Ca7,EjZ-'2<Q*m(cCoa=?<CEOLQ$3/\Pf$3tAG"@*+$<
%8/`g8:0#adISs+B4%W9)h4uAU/GOWjH]?.A7TI\s-nECN<@@MP"C06''g]Vieik8[l"E,8[q`0e)O31dVkteDg&u4X`Q9Bb7TFu7
%X-teUe9D#qbmP\+qJS(F%&S02'$*0UMmlG"9i6s6Mfca;mB8an=YBY0`t[SireEY[rF:"KI?nY(.Wh,F!*P7Afe8aj6'PEC8OIYG
%g9s/ICf$7NNeLUe]U7RoF=mB'B0JD+^A_7RUBth!Jp!A1?'bMDgUU.^P;*p2?\9A/K]uc@j!&Wu8G[NMp,%[C1@(#M*hdtc7?0+o
%a%+(F,DE7GkBIQi#>N=A$"N&pBk#LucVDggO?H/k&*s5^60q.o:sG<J]$:i'M+4s(m0"!t%)g*foRSkoo/_f\EJe(<Tc*"Sek-Gt
%WUH3*S)]l!)SDI31ja"N[RcGn&\[=VNKY_L;7hk.+^4t?O<ascM1[,=aZqV2Df2U!lW+1Z*(%"X;2n4/76j[hO?CLG,rEt'7(O,`
%oLUe=O!=d*P%;m?%jqB)a;07Khs81Bl%gA+(g90hWGN[Kap$.J+=?^%`Fr7B;=3SiOY#EUf0PeJ?.B2b>"e/Vq7P)?_^R@k>,ON6
%aF>J;:S#h(chX8^VUPdjbb@:ia^7;J:'<-Ge&\HfgQg\OlK85n70(6(q(@8>BF>*4J<Sag3lcL<Y0b`T@*sm6O_/nf:F=(=jUr3H
%GO>Nl=N&+!3_-rD?1(+jTh6]p`'*>Sc4/@bR\_8Z0-/*Q)sE\Z'Ed$D=qWBo[!*i&l?C`M7pnA#jM@gLESd3I:2HebOF]sl2N^[(
%""m:h&b8!5I2qiX+"Y6(N0Zt.-J<(sd/0NG">V5cQRkZ&dO(.oRQgRegU[OVo3VC_r"'>h'rGMK/JVPo/&2ujB6NFL`Zg(/"S0*$
%`JCqLN>fg(qE343])lBblSOYl@+50;GBB:1_ikB36fo^[<_G//f*<e#R?mu3o9dX<ZLrWVQGc01Vm\0k&3YDQ))UHo">\A'=qWnO
%@?)c4i$'_a7`l<c?L0mMOru,AL1kq\QS<b-P^99\(]\i6QVAr3/iUTe1=ci),,^)2!,9H>$OoK%n0BX[U2Lg@33`NImCm%$ka=e-
%^;atFZ@@VN$K+@>Rqb]9ZefN)oAf*MZeDA>/4upEiL*KM:\W)58UC&ce"_"c(nB;VE%>JFJer;/"!+ppCdQDLJ=R\.d0gm%r$*J_
%KOfEb]#-d&n$1^SH;+uC8oN-0Mo\SRIBb3iAfri6OpU*G6TfLW:H5'Ka+Zo-h_R'JOQ&ttRjPU^;,kc4Gb67F6tDS#\(m?L1<C7@
%@4Q@cdO7_pU*k5qYQ]Ah082be0_#&kSO7gV>e2qH779<+)4n\*dc&Ce,[eboI=ch&B6!8#qlmRth&mIFhHOP2j#!e'A2MnP0@msr
%"qeDuVP%5D-)KpZ-&PrB!g:EU@-gk0aThoH9<]6jT.Sd$EI$uBSQo5W*Q`U)fskE+<RB5k-HtF>+,u_Y6_G"C%1sQb`(5_.%7:\(
%IXSpQ/)E#!*8_i!"Z:jbJ0"Ofd>?..*moS4h.T<D:).1?a5dsTaGhSg*dsH9+ds!84oX*=@-jFnl'<qp8#-7R':XC1og5Cu+kbc^
%>iYTBo".a5XLY->JCofm0M=U%I!ZIL8XhUk_mWg=KQq"WK\WSE9VR]niEI`]BK_UX`gdO[c&QLIp$,%1'Zbs-Z8]lX*)tskoNO'o
%iODYVbJ7"A=6Od-K;toplJ?,1_APQlSEl0!D!+Pig0&j?!l-jI.>WVEeH"5N-^EJ$bUJskM.88BIU6_B/am-W7$LgA*P,$5):E=#
%7P<4IW2J1$\D0Jbq(K<el6HmEN$d<sku01^TUDd4*kn8hoUF@!#VZ!9r>9H>g_.IjfZ-ZJ:WEG9@rG?.d4o-rJ<SY8Z'FKdOf0ju
%](s`GTJ]PDU?Dnk[A,jps(5dM@Zjh3qYKfr>VRct%'>)ki]8>UdYK=%oLW\<%%YfhW[E)WOPg08B6XguIgi29'c#/-/$:5+GFB[.
%YIL$Qe-W/gO0.pAK=@oNKI<W@'B]taTUaFLjUum:]_9Y7P]?V?F<u+mkCY-"(ZRZ,p<$N,'/b,sZIZkNb3*=[>8=s-!=Wef\H!s3
%p2]QB2&N5W_5*uCq%@2ej]:?"gC,/KXbVu0=k-FON_ju1b=`DSICOb8W>+8-N$('@OLrmr0\WR)?V:R9)VP`Rci]i&:Q.L0\I[mP
%%Xt%d5[LbDM`I1KE9*ibQh,AL_ol_6)'"ADEQfkS;^PTj*oW<<@F&dEaW3q*8HJ.&.?I-aa1d_VSl']P\5TpYSE<2JUJBEVV@sR(
%0diTGj'/l5\rcY&V)pt^hdEA//g?NOS.]AebDQMe>CgPk=+II*3L6CJ@)-0EpF5"dA1\YN1X>281%0lhceW?q_MetM7d$T-`,Su^
%c8uTM0KZD?"sifiS?Z%aRMSW9^!F?SO'=(07/7i"._uSri(7lLWXl!Y0$mf1Ql$AY$PEMQ"[j7[p"PZmhLj7tC;L#12fV^jGV'<l
%]^%=WL7%p<G$Yku(UH2W?o5.iC*[iFHCgn1UP?MhCkoo9DC.[;p`6``Eg>d@0n=#M2r/i/:gtG+At@-E'soSZGqHu7,f:&M6'JVA
%d6`3EoRZt6$9InQ5a0u"+\/11WkN'?*<urd8$5?uN,\88"u]#Si2IseS?2UEft0!Q]U3gQ#m8Nh.QYuOH"Pe:Q=`-Lm_=cB]`Kj"
%[,CeC.AnHTd]O(q-Y?KfACKBQY.c[N/Z+gCa`iF=.08C>W`BE]haqL!QZBUHpo/7c%Mh\<O@dAYIntD%p7tGf/."u@qkRF_mnC)+
%]6l>t<jL0X7//pWXL!m+\.46!oTUG0H^31>0W)J0K<,h2#Nnjo[o9'.FK18Q@gFEj!O<>CBD6S.VIjA$P%fi?EG):*=^A]1#,I:-
%Uc/3gqW!%MjE!=>hG6m&]K&]@/5iKn?Fi1Qc(l6%+OZ*'Xs3^9+?^i57V?=1`"Y#6@*0qJa'Mb-19$p/k4IssB;&Fe2:me(%V"FL
%(opSeY[I4j'G)8e73s$(NiZKUOp))+eq=%7%pNskq,YMDH'b8G[mk<:S>MAa/4PGKNkU%T?j0G].Nok8=',]k3\blVgZ>0oNEnR`
%j_>@DV:ZY)ouLd"[F6Qj1]gMhS0kB)>X>\b$fM`i,D`HTZ6+ekaViR:lIZTJg\3m8MY44>8[$`E5t>!a+#IaLL]Z*/656Nd5F8YV
%ek9&/0\Y,13bjtp,=dVT?g62%r*)+s%kp!'",]Bs'1EIAU.8h:*@MtV`A/tnngJ[EIQ>f`6e#W\0=`<r_&%tZ\-81S*8#YHJ;toD
%!fi_H*Bo-=`Q'qU)lBu^%%2"U+UEV&qMtu%"V]UI;R\"W];0UGS-g8W1phcu&ea2\mM_61^3@]7@,Tff_8,CUMmW'[e#=K7+usDO
%<1>#'jQdL6*>O?lPj">uGgZX+B,a*Jb7[M)pS>&H]<r!k4U+ZJFFd0/O3"JpHLhlim>Tt:?EI+0&ph^@7O@G"*AcHHNX'DCFEV,!
%F;Uu!fX:EhYFXkp4&'\8/cllrh?ME]39kU?>a+^gJm_<gg975-Z8gc6*<SaE"tRN'9goo@a#a[-)mT]$Ei4#:KBIb/5!Q(lDHjF!
%Js_DCX/E)F#L1DEq5!6c`Igc7qP%hue.6Rtr["EeYf'f+X$JCHRHfSUj\5?YG3[M;b^YH-N<4^9T8SE@h#T,6@E_Ap2g7s3#PGb%
%H$,aKXC<M!O![375$:om_4qWkp.YL^%sR.CYCSJ7(:*fjJs0+\micCqM3;(h]plm>=-JDqnqef1P,B,_6[Dj]\/UV<W$4\&9_U]n
%O=MCD8DQ#=d1Z!U1F-\c,SjeQ1@qZQhur3BJTdP9g(S+cj;9ipGj5sGCML-40+hZ:Nu%JdS>aKo_r3"6(dsZ>T%HY-%#9s=TIL0L
%kLICZ<!HCaOm@*BK_Ph/PhQ:!DWGKY'#^'Q;abtle9.EANg+1S1g#o(9B-d5\hiG+aSDL<$-5aKiK4^t@UYJ<@)fH[iWrurZ]^`F
%,e:F2H`nM2*IJU`3phR'53O,M5,_C&0]psX@T=;JJ;N=KOcdfGRsi$,Ghb$]##I_i6TcX1JPJ+$W\a7%i9G812gSe4!.&OIJNh0Y
%RG/6eJuSmpBPfAO$=X0hh\O*rODl/f>X1"Kj9!g9^ZmJN!H>i=SfN7#4WAL(.?Q=JS$(4_GCH2'T(t4BUF_t*i<\F'GW^u7V#:[9
%9ZW58qa>TY-]!*'lN[ZXCQ^4#X.Y;m&3El/mEgGUJ:M2^3bH7W&u+Jo(B:>)j6!X3W]2#Y_869kQ7d6_+%eYJ;g2,5fNpp,-;s+O
%CquS)a^>mo%eqbWI84`<93a8^b`'!QC+TY9k_tC!]qLVPeCG7qRdXhDl=e('A;Fa.;P`lL/"H1fbN@^X>R,6T_kC&^?C6(+QH9HD
%"S(su/aS1BquXb3Eaiga#!SrJ,mM1>/67e76S>(Q;M*GC8u73[WdME.j)kLM^"TaUmIu&'m-L5/mb.=!ZgGlZo5Dr^m!%o'*5C-C
%^YF:=jjoL5B?)qigT(]GoA7Zse#g.jFmA3j4n\@=o<[bP.oc)'D_K?_b-.FbkF9ZneP5uN&uEnEgNemIN#OM5G=$R"Hmdp1,Fo+,
%V[njOIecIiC<E'<n'4\:cVKRFF/Q]qFa!WM\OjuIqNq"8pXaUA:>#^R4J(kg@F0Y;M"C"<^N,gQAH;c.2>6#/4oaP$:[8JCD*7)A
%5P)#Tp2BOus#":nDpmdcc!UJ!D,,TB2h1go(H$9d&W?5D/Em?r:(Sn[l#`AuGI5=W/>l)U[P$O\/L=?njEfMNhN*p/EDakTeu;se
%pDAYaaW5Mq\9dgBd@6?6$Vmn>fto%SY=.g3-V6q,MiSk?-M;pr=5f"d/r^Xp-d/Q(bZ0XheCb*W0J4MW?;36aT,\A!q6bH2`(=m^
%d'`!t/JCZC>[-i$NTG_<40oS?W54<j:6(!V:aV.H"Z9iog"Fk(7]aecD1X1ROlJj"-=CHt0U_fs>V)X=/E1).s"i?=(eF!F-jk>E
%M4r\i3`t9b@2"^[X.RmcR[+_/[1;8BF**m!W"bKU854jH@4)KEI?TaR@rN_(?SQ*&mAp;*/-;iL[KhX3q;"GKOYcJfVqL1)?LMQm
%&jtDEF$MLWGF5k:0%$=(KNHRnlOqZ<%SJaLYuQ\#Qg#9[QH+,,j;nqb;3EhKZt']h,qG@d@3V\@QO@4,O("tJ&PVNPjp*qPE?j_G
%m$Efc?+6^d>9N!e`dJA*fGptZWEj/I\g6n"OSp\.Vc6n'fO.iUY6p/\UW&_T_!$%Tg00FYn:7hekX4Q%^pmbM(n?Hoef\%sZa^0P
%biC-/j%6&De"[st-IkZ'!&YkhE/Q_dL^HmeQ7e#*LKB?Q/Gnd7'.9u]9hbJ;<Nt1;8]gCp>0KHE)a5Vj23Lj*%!cgqlq!cW.oJta
%_81BPjd8#p@alP0_<-CqPF@]j.r^rNYsfqU8N\Vqi+.7*(0i]BalS<p5(m346UV6=3BY(r+*potb8'H7P+4q]<+lu.,m+J7LQ^TD
%HbIYI@,co#K-@-+'F-Qop9=b2IIg^bS1tjqnbRnY9"l$?s3EW"$VMRf-?NZ&#aq2^!`MooR^qDdig%]63dcL@[7_dDgr&V8k6J;4
%WOI[5''G-H9[uYLlr\jmb'98LGg\\VJj\Kh&Gac%\`sqWZ1ZlejM@?Q%K9R*rumL[.I^b+CL\lX/3:[^Ak$`NUW\F/5nH\<O'jCS
%+s_E00*%b9"Os(^3jIoLB2Ocb4Yt&%^0Frk+rRa:?sOVVDo&)@PA'(lJ_tuuHVV5YWQgq,)W@TjhMA=<;87en%-l.MD;L2oRo=ei
%amFo-NeG5mKD@N%&S_=dl<WM!81kgc+-FnbhHEV@KE_[&0oep71hdMVH@)T71'Rf,XaiP`@0c!JeF-5h*@@=<h(\V%^lZ&38)tX9
%L1-T_9%3$D(j.@FJ5=it$IsQ&J+Z[0]ocS+k&I;]cO)"^N@$,]3?KLRB;E08UkC'm!TcfClMg=8512Wn?RrJ`XF`F>M4tar1G?:$
%X*e*6j.,jREE/"Oq8)gfcQqr.U3n,*/2I^M#IZF(DbqNV@<s&iVR.l.C.I53l?"->;g,[Imt:6kE^0uaP:_>kl_a#:.k:*r]c#(*
%%>QbmFsHtM"I,al;`f.U?9bNDXHW-/PhqrYn"^JLcH[?.<0SstG#")5858s!8q:85mFu`bRDAGMY[0N@02,WhTW\rGVVZ473%?U<
%SGB;1Tu&R]N\lItU`g01K9@$G;Oc.p/V8e2nj?qbf0j6WXQN/De0ho0,3b&_L'mGHJ0%g?Od)Y7PUW3QH(lk5=ciHCZ/]A,*DtW^
%6mKd0D:3LcW3/hXXe(Z_@St.8)54n9\HL;`_$sI@Z!>c=@<f=jq<@,a-r9CrK]?7Yq6BmoU*SUQP@LDOlp[F)Z;j=kD+e[rq6pt-
%fioh[m>,E&&1kqrJ/AIs(]_bE6_jeh%25HDYQnhBK<caoI"H*[OhTV1)RNn"44.d=1\c'M#<YY@4GUGqM#XnEA-E+QFW,S"H:#/a
%+:T3%=N=t+E4L0$R3._4`Bd]h.,eo"13?F<"JjL>T!Xm\rC$X%;53bVE]KKhLZIaLnVuHQRb0=\f@ku);Z$Z/imrlV\/6urN3mcH
%Bh:@2["EsLB@8";it+"4XO\m)4).=D12]lW'"<rb&7+F'9V\r4pN.tVSRrb(_$)X&3f0?c01_gDVHJF+P%=srfR,McK$aSD(bQkm
%VLF>47RXqUODpQXBd0G[BNI1:#A/"FVg6<D5HuXHe_,mOHEM5dlcE'\loEq9h.X6j5rm5_fPM2bFrYXpnup.t)Yrl5gTF4bkHXDH
%C-%]\VSmc_Ks@0.R26W;&gZ.N">b627*%b"_dE]aoQ]:CCrj!N')S9eGj'Bb#dLiPs!TMB+nIDB<H.qiS/s*d.YB.X@MRHfS3pZ0
%.%.+<:OJmjVWpFt$3qXlEA44.MV-$@SBLb@6HBV(61a)Q_\>CF(,sM&/E@h3;4)f3Bu6'G,+FYG($uj4mTBO\_M+;/%Y>6G0&ptG
%*HEtmU`$QT&gn^,3,*c':bUdlXdY&0.9d.>1tmq?(+bnIL;?p!4t&pb/N#6AiCqZfH=5R,7Q0Mja\?9&:f0WS+Q/,-(1h_CM\2i0
%p'^3kUkD'h<pFhg2T3OUP!h-Hj+;i%;I^)J[^CdQ.@p&.+o#BML4E3R9,f]#3,jFF_=pBiXV[LucHp%s08[Y[8lc*Yj9Lg3XBQ=\
%\0f(Nb\G@1dsR-AS\k#7kQ[UfIH#r12e),IDm*jt"WY2:,hGN,p;idcGe3"tKqY8gQg[MnW&(r]-7cCh/Ce!mA.-Ot>[gT*F<;E]
%e:Af'1RAjfV?$Ho]&5W0c4`8jka>bIK.-V+"<s`lmY#,@'LsM5<5Ad(I9+6>bR3LgT$D;LRt6i#Nh&e;kMRMdN_1-M4n*^f6]ohC
%D_P9c-*!T2'D1r+iiA!=5N:+ND=YX;31dtZ8:/JErZp4RTU]!6[!?9D3==.6.3Dham6<;)@QNN)=lOpEaE!gt#o0S-#'YuJ9:C9<
%l7d9q&6\S$1br(j+FsiO*[UL!.!iQjj:]C6MI%rrcK9-g*eb1<p7"J9!KYoQ>:4QL]oU^8k2,'QCRA@!9YuKmU*l=K?HJf,%=/0s
%ooKS^X6RBONJNGnR;](2X"7BdA9?d_`%$S#=^@jb`[137J1.N#j.F"]*?/33XE_u2a3"7p;@]YR,t&0F?$>LF\/d[@I:#!Re+6(3
%!4(5F81n*Y:l^.+r7M3M2[a-1G(O451Za[mS$[i9d)d[!:+s*EEA/K4Ci>N@X#VqWq;*`(ea_otrH%O@3VLNH?5*b-Ar83V5Xj_/
%6SgL^*;cakSeA+[3J"!p*-nY_ZB+9_D+[MO<mQ/"L^%PkN36X%&A9;J"]9jU+s?_`SI$X?g[dtZctb::nja4fMX9"gQ&=p',bLT\
%?;9i/lka1XlcDemb,N\[4bef&ik`k`W*MH)cgkK%^pgs([4WD+Y4m1n'LQal-`'6.5;D!TI:"M&`Koh(2g*nCWAN(Z`L*[c(qe]L
%0q5%fR>.Z-rrs=\fF=iCOI=:V,19iLgVTt*o3%*pm)FV0\ie37[(6Gt6&LsG.D<:VNb5HthU.WMnAt24g1LMc*)A0n(-C;]jddhj
%Lrc!8L5Zi16q:)Hf:ncVi,^CkO2nJLQJguV=WaHb#g#LMVfWUC)TfVm;EUd6g$92lj'>hfb8P4FNeD5SE)0Y%FA->,@o^p:_-9Q-
%7?1X=HsOEH3]UROCR;'f3a],l6/,0518;Q$*\Gtc/kD/VEDYJh[oo;ej+ep'ccE%t6V["Ph'Z!1?.#YtA5M$BXg`LA)+c+7-*=*.
%KSkuhD?tBhmW^]u4!iY#,r(`?h`:0`E?=U8AV#&IYi]YmR5dr^D\+^j1Zo3EM)oQ(j=R/I$Oqr8-Rsrp1+J@*13#;hWAq/*9i#B,
%RZco'QHR?+g,P&s$`_a0?*EeX=\[!G9HUcCEbT*B<@L0R8.anJ7jZDIm(!nYR/oTGi(X:s(-GZTlYOpUKL(n!K;9(dW>+:C?ss:\
%0e^ESB>AsMk'pN:d2[[U$<(Apn.&PQKdsaa`A&9HYp:B7<&f6&;/L?=N&fj[0:k;$d^CY3Fg#KH7io.Pdm.ujWpRVs[V1Sb?Pb%d
%0<(KbAt'TaN,UQT,=2lU6*rNA>CfJe=t8.D1%@@t&Gb-Z=7#Zb5g%gJa]'*ddMPl[i4s"-Z%#0e<RDKRjrEj0A="\mCSAgh_T"t"
%+(WI1qWoK]]r1BsZE]/09JE"Y)+=e1EB(*+SYu^7d9QsB*Rtg`XNcTiJRDH2Z2hA0)&1iA6qtM=R.LJmUhlEa<':Z>&jq=S^!BDc
%6?fjM#GR<$PMjA5_FIgo&9Xl!(R`P5BI#oqpl(RLa`>*llT32<-"YZ=$Ft=G@hYQeJte@7PIi]9KjnoJH]tqm60k?`C[mTTiSkL"
%jk,V:Nr.m!dE"Y??jm<IaN.%"Z`$6oSu=)`K1pSL5.;KB.\'G3bFB>CI6bsNgt@ca*InK2VVIf.ZlDiS_61Y(^_B?Qm)1V]f-+46
%nA^S>$&W;.dNf;t3TRF,.J((QOON`#K"IpS;.r@mi@+;*8HH?t!7!#14E%G(9rke,6EtBRdnY^VYqsS#X.2(*5#+Ka&\^4GFG/^G
%@;ct1=2EF--:\8T=.CZlP#oDrSsC7EHKaRm8-_ZQ.m40RI?g'tf+.%VhT8hN@+5,L>s:mC,NLa1qt&#0bDXioobYZ8gWfQnFPAkI
%grn74-hCUV$_=B%@O;J'(@\'?R+EB>8'I1bVfl>AG^k0iZ2*JGm!SE`n^WuXRnk,"&+PWThcfk4q9@Z\o'=c:r0ZUi0h3RgN_Igq
%J4jhsCNX'cO:O)j=ZnsiDm[8Z:LBJZPrR6Ko;9lpcSIIqi9i%)(eDE0`^)-Ni%ObbrT@B(WW/UjVQ.l9;ks0RFH4-'no*N^]us3[
%^WD]3S.^mu-t*h24t:j+kRK^N4NU88,`^il\fCND)m#L=2YN+thj5_Fhs/qf_I+)@ZFd(LNLFAaFcQ,.\rc1Mg4W?C0](mEfJa:g
%?_2(`CMFNqBPf"CVN[V9k1*n3ki,:rjIC@.?"0c@gn/UmljJT_/l/pjeTL-;j>b8j]-q;_;)sZB#L?bH([a-:?CR0pj!Rp[`kHHN
%qfoFFXffor99U>=:3K%16-0Y.Y]u!r4NU%Wf%P5B2FB(6q&>+'5E*eCRbH![[5"C'SE('E+-J<L_(eWLlW\/RQeM)>%-MOKqL7<T
%J#d?@T?nj+O/T;@mOcTY?G[+>NL5AEm+0l!V)DU2_tb7,^>GdKDfopZ<SQKeo3G"28>>\E6I+[([c["Fhd`W[Whh\)j3[O!=T2@Q
%`m5G*LMnF+,T,*BZoT2.`DgRaC[')T+8TksbMT='E'FoZ>_aV;hTI1&]%01FAJ-86MXmCaT8t]ii#%RiK#7LIs0aGmc)^984PB_"
%ZTPhDa$4qVpj]OkCqfo!O&N/rqY^=pS!2H]"gu1+EkN:F$`c6*aY%Pt(BKQ#H\+PUG!H)ee,uq]RlkXX=9J#5!PtF[$!,$96nCe2
%bjT?,[`--p+@)V9,284m^hQjVE.\nL)_=5u\Dt%hnLH"qlKg[p-4qZgL8RV$#8_<-2)3'Wf3;cG_-TDl<X!#QWdCncWQemfP1Dl-
%]1A?&>m)a$WBHe+OMWI.LQNe]TK34Ko-o1sQGMmAY^2]4NUXLBCd_s%6$%IS8t^G;\C)@UR/k]X<sF]=h6=;Q&oc;iT;Of#%*AXM
%:'ok.1V$1GWL-=$3JbI\Z=g't4K:gVTE'ocVXI$<,qbfYIM[Hk\)T3CJf1tRQDRJtRM898X@84j6g.`sVQ[dq\,T]"_>>j7XrH;M
%,oOHEX-R?n'h(N1bS]s->*NECYcm$J9[H4HSM8]o!9OoYJZjM^0`$Qtca.]`L5"u'1Zk4XaBI_"'Lmc6Y[0&B6:W3fBU:<FJ;c'n
%SBLLF:='-oS.<C&%ID),'RHuV!%/@%U`UjOkXS]$RVG'ug-)68HJ4.F.9k^p@.Vur\$.?7(t6&d*?>1/"1)P3C,)u%PQET]7W1.T
%AK\Q<jVfB`8srq\%6UIp.$Msb6dcTp=_N/&kQiTteYO?8)[XtGameq'X<l^D!tBea6XNV]-?g6\"e-2#&UGium"h1-n5_<KLbqmd
%mDGJ(V&Qd[U..@"]-1@<C^@plF$fJp6NQPn'aQ$Q0#@&6r/*s:aG.f#:-M`&jlKsOg-C9>2CEY?PD2)%UAHBCZ$C0f<mAdS(=*2R
%M@#Sc#!TQ.Oh,:*OXG]!pQ/J?+W%39D!OI*8_0UqOQ@`J8P^cDG,LFH_QLqA?F9_f8*UcAI))_A6M\-Dhr7`[IbN]A-XZ$Cm>"^-
%24`)<pKD03QXMj\%%l`:KaVIYEI,8)?FK86$j?^4'dj]]gm"24[&g]!4<4T$Gu6po0YoAD$';Fo<\%1?.Ql=B^(Q>jkZPdK79c4c
%]5-6BWc"8d*"C#*TXm0VMhc##F]5]&(-(A"6%g<g5\H"LC?#oH3S#ba;%rFBT>R&m>VP_AF(a]PYUmU_WNCF0)K$Hb#`3"JSArdb
%5iH7;EG\3))<g\.<@PB[XEO7Zp[C`/e*.kq2e<$]2HsULPSDo8ZN?dA[0dfs!&,<<mS0MXQlCUsdH=rE6Go#k6sX49/93dOBT`</
%a&N^nqd4`/jB/LN+7!F3HHrN<$?n(CVfboX=('c?(#I^C5u8/dE@NJ[YgF0h>$78]GInkdS(GoHdkrA&V/*SMYN/.`;A+g;]>?2h
%-MBal7Kq)UclJn&!5^Jl0_K\nmnCrEq'b"I&9R9gIYK/.Ukf#AmD&,k/RU13T"iI(K3o?3ZtWEj=OeOiBiS@ug9P"t[>QBgg^3'R
%hq<&&p)0sLX$2PK0Fm%rp58s11rniWVfKb\H0gu@:KM'tGP>4aDbCPfJt]^`A3<M)1rElVmn-liRN#*EAt+Vk23t81EnP]HR'fEU
%]:?@8RLE1>ALa"LBq4?q4cr,B;m<YW*s'L>N<\RX<lWk=.<'4#1SlfW(<NLE\E/St`kgNOh.CLf.(4bG]5]oJUCi<b,_rAsQ@"??
%"M*J&:+Cs06cp8blCBFTKtq_uMmOij>@JJCC@>uSC7f?^gQSBnS2Qj\eC7@=SN[rhIXNc'b&h!_+/2Yq/.Wn8I^Y"C=WRd^.R_f;
%opJ<Q.olm#$$NUem4]9SH"`7EdGbsC]sB]II8D#a].&hR5Vi:q[29Z6M8sV?imfF@37^UNHn6:/UL,*@gY_-@?ft#WLK3ZJkrRe%
%#0-b$P/mlB4mj5k+G8_XoVj7SYT*r$m+30%/qH/sF75M(7)8d%espI_%\J>aWCh`k<WQ:h9&4+P73mnFN+\&`"kR)M3R:<rV`\W#
%$XT]'BV&:e[(5km,1p[4<Jq8HCYKFq+&fYY#(<fl<-Y#CaJJ#_O764RNDJ(%@Ei3&d@okB_]G'H5(eb\KYb88N8/Jc?0:BE4>;I&
%[G^Zr>2u'e6ZHD_GX.>"!n\-Gm9?6WY8:Caqur_`)*leRcrYAg)JlkT$9,@fhPhf`YKshQ-2Iqh!ETU6<WHC[D.U0PC=k@%/Ss;K
%Sub!_GjZRBLEF="UT#h-1560EhDE(dJlT>s.mte#qsm2V^'3n\]Q$qZdqoh+qO$pJMp[&IkePQ]gT2E,4m'0#J>eh:<a/FN:t3-<
%8O58Wq1$UT<BZq=$#=qTL.,CR#-ZN:W3>ToH%B;[D-mEX@;:.k!Sbe"_9:7t7n/MD<aBE#V-&NLZF]'o-L-=sl&dK^O)*.eMNWZ!
%MQ1npLN$FOg9O;/\u:>J6Qhfi"VSOKCgLB%Jnr2=^o#^5]QA-G[2Os$K6H0FFn6H6An3%m8suuHbIVIm\Ki%KQ:t.:*l.(VG.D8;
%#Gk#/F2[Z;)!>V<QYW\#-%2*N8^7TJ<079Q<(=DKf;GOnf>j,''H%a%>TZYnha7?H:JX3V>-CX2fu((71bur>]K,"G0<c=OULnWR
%R.<tZ7i"dKQ(_<Zs+tA5RsMLM2Is^nKbM)<;B1r'Hhjnhe]l]+22bSEas&n+I"4,.I!2Dr7Db"]XCS9Fb$J?5W@J-s`FA7f7+)&A
%SuRahfm5$U)3#lIA!*.Xh"N=<WdD"fc"J(K(Ros:L]\D9Ah]^SrE!o&_%i_b1p.'C]B3=WRNE!?#'-/5&,.sjfk)3uD(MfkCrS8+
%*Xm^f7GBj"C!uiiRrPet(Vqsej(Jj$#hI6FWRkY]c"=$XZlH>MgJp]a2Zt5o/U,@TP7>@Jd?HpK_&.>2&'$MoJ=,",gtUPF[FmP$
%+F@1KX26g^[Ff:RcCgdJZZ1t;ZZ0GpPu4O0X9MABd=h+d\iAdXBs(Tu;bj#@'glr7HDl?_)_s";NT?<H=No`7NW^2f6Z(>AAYX6U
%P_<'fAm+&k>/89oS.Ck42OBa1]kIciMNLZ#e#>&?gP&+)ZPa_L5C1`=H,aOU$s%GL+'/uR<^DV6MQ3AZ^9)4kd3JiP)b_.`'jq*_
%"'B+jY46<aNf8W<fOY_thZ#8>A$\-ip9;`\;6oMJ9X:1Tgp,=GXDPt>`#?Es\Qfupcr=i$Oc`9>6f1S[W3*"7`f._:o:;;#PHkg3
%YY7[cNO04['b&'M`\<h$`M+H9O5#!5N5eH29EiX0iu5"Y`YlhnQo7u[YKKWu)CHS@Fg7=67\nmAQ@*,GLi7fU#d2dVenR=p[A\O6
%.MXVPZHnIshN]UlEAYE>m,CC(QRN^io*'J\?mo&c3M5Mt176S#[S>;5"h1^Q1:8:p@bW^@(0@,o6$Y`"3E\F)4-]5KJ>1B>2JKpP
%9mW*k/]!W\YMd$OLV@+1P.1=9kZ<orX]c;INCh`FGUjnTTD?i@A!KrsL.shsPr4MIOjDo,U?uo0h(WcZ:0%pQA&<4SPO.[?DdY1_
%2&:q5T$`e2;kqq;9Yk]K=!&*n($oDd4$'DKj$B$$O0R)@,r30I-_1gb^0bB8R+-@=D@/AVl87Q6<r,:)'GYD:ET[E_dtQD[17#Bu
%(VoX%JONJ[;HTDI5r-rU&MfKs<1>gpm`*hj#oY>R`P-&^?rLq"/7<8NXRN=F$r39mWBgDqIF.7PIGPMbqkgRJh0_LhdNo!:mnRts
%=bohp(@)lC?/WhTk941o,VX;WBM;;2nn@6b@PSABM5W=O#342IglKF$+a"!$5=\HDAmj^:B"_/,gc:iC<O%j)ChcY6TcEF!qPsED
%>1Qp$]"qQNRlCMf\.3U".Fm#*9KMG@GWCYH1"=[d-"'P'+#YS^B@*SqMG`mtc6AlTS.4,lB#,u`<\LOTjI)9W4[8ct/8&kOYIDtU
%SY[*#2t@0]R_'lFoMh;>X6E`&S0;uTLW7>jHVo-!0eG4cg^QoX[;csAT8;#6ZsG7-M+A,HlBNm0T"/;`ouiaB2)497k,N9A,s'G0
%ite+SX>]D\B4u\!1I^jAZs/oV7Xng\M]/Qtmu;UGl:nVd>:X$rMlPFm*Oi=oP5'(.jX^!mHTj.6D1.F@lh0NsOsfJQ0G656>Z6IR
%bF"0[p?P%Yr;*(^=gQj]'o8WOkH?%>SOhdU[5crA\iZ98Hc]:G^l9$4obqA2dQA[B-Qtg>\Tg;[f/c!cC?8PuJ<?KYeEX:32kOEW
%cL-HflS[qn;U.He2!=1PL^,7"<')!PGpb@Ek"FA]@`D%:LgocnOP(%MZbV(nKQsbs%k]>]FsYSpG:6jA@qChHEFH:OD@ER4,1ZJ`
%>k+d2N39d`D42Id%EVP4\UrMq4GbSmRIM"uQ3VQE'/6U7FCRZBQ[6aslNQd`5(Q\)?s1V$HcEinUC\obF:Qh+6=7(g3=>^.4>Kk&
%d't8gA*tm@Hh8XMMg]!]pZjtP2!WD%F"(5'%qRkqYF1?=fMDe)p"Fq>,;``YU#%XO6J_a/rA>(Ll!^hgX6Z;7Q1GD"`3tbi^5D=@
%e4gHrCQ,7BBF.ubW^r&f(2)7"!\R`?$D%^`Ua?(B%,U9aKCPomhI)aX%]7M=B%.%gc"F7/D7D[1,Y,7W6?CP!_iXt4"=788D5YcL
%/YB8rO^f6:J9GBrk0YehFi$nLpo79O!Z_&ETJLEnF":fUQf*uIEr#3<;kkg#B^:E8KUo',?WJ#$8SqXL9d>^%mY@O!V&RCJho%7j
%*U%.Z%!hoOQWZJ-8J?#`\5Q6bpOgRWXU<PC%0T8KfVt>'q2F-[U!X?_4Nk$ngp^07Fbo((MBq6JW99A-MmbDp.>@_@qBjX*Y,6-;
%AjfWi2'^<-^_j^f>*:4"i]<=3Js.h86TJ]/@,!m\hV/U@dC`d@PrO&s]F&f=BS16P2ldJDOgiTGS\@EUGt</ahDH)'7oHeD"#r.s
%p!tDa3@nHYhSc41G&jO*4<YWK*R?Xa1.9YFg47_>gYfM)-ZaO3GOi\Z!NU'%.?R\Q7S)Yu=J$p<:>jtM8]i#iO!MiA^Y2$(k#S+)
%&t/Ana/*;VK([eHJPWpAKI,=_%Wp%VK_N:`-*%Qa/7]d%?E@m]$=jT`"j]!llUjd_A6Kof,43Y8UQYQ[CU@m]cN_:u<9"L,WW5Q\
%U*RqT7>`:)R4YDGMTi\Y>HdHT6iCtHK5ts3aJb'mc0*5[D<j+F@BXE:0IPhjaYgSa,:IdHa!dcbE>s1aD.>\ejF1e%#i*q5`n_Kc
%gL\;[98dqgJt]Nrn79uKia/eWR^7Er;]B:-617Op;kFN=Q>:-om&CaOd2jp&J9on7WDX5#("h&sI2+s.n,V"rZkinr/Z`h)9WYM/
%K#!&rXpe2!KeA^YWnZ5h]J?4bEP4o'p"d?OU(pSop737f`l6aTM/h6L-h(N=4cpbe.^83H"tu^M&a&b"j;?Xjciqn&fAiCOJ$h[t
%lGO"5R^m4-$KT)=d6r/1U)7cZX\u([*O3R$Fjei/%H))o"*Gfg"pWhkWm1m`+'.2uJP@4fGZcN_GWCr_fW(&-Xk7=`6Prij;-I"W
%O/Id#8i5(W[ZEdNHH-+I3<,GQ]i%Wb[E-oS<7t8U_VfJ+e@M#!,dh/@@+B$%?8eH]C*&;E#5,LVe]ia5Pn1G<#G2N;X^K_Ne&kXc
%;GNNPEQU7k\#X>WES%pC_P63]+ljn=UM/&n8=SN<k=hGtVnnLCa:ImgN>g&!Ca?/q,8mAIJ#*$;Sp[)DZl`>NfBi>\H&E0B^&_QO
%^&_KM^&^@Sq?D?+q?Cd#qNesOleCu=Q`I`;IEU/E\A#3W$eI>uBj/*^!Bf<:-MZ$^9sCJ[Rp][)]uK:4#(5\3-K*&>%H%9=2rI?\
%2h2:f2dgKU+5,AGIdQt9E9[4b_1h40io/aK@HI??f#j+kiVEb+l1tU'l1tVRJEn0NI-LJ3E9[4R&NeqI#(5\3`gg<Bf=)/@>i-;-
%e+J]m>I_D@@L$/qfKTbq2tZOdoaR=r%_05-d95f3-J6c>9s@!fDh>/thZ,97h_Niq^,=6*lPmX9lPmXIlPncYlPln4E.%h5_"A.R
%>IqNL>e7WM>e@]N?FmkE(RM:C)-j\c/KUeT)iA4\^7r7%5ce5J=L3_I)o-[u45fsic<7`m!F34ME?*Q4D]HlQlPjkl%4.BTm'I]B
%\\94ag!]O&i6q:X.F$GHDnq^BDj'6ujnb:ZL$RLQ@82P<F_fU<ANuRJ)l7!%qBk.CoaR,UJEn0+a4Bnm;gmp,`l2ESqM'E5l1tU'
%l2$^^L$Mt7YdqIK>e7Xh;dI#LE>Mul^2X6\\A%&,_]dVj*:DJ0CTU:M(%aES<a(pQl=9Z(LuM;_")ss!!Hp^Y"4=?(X\2r6AH3du
%aoGd`ALQIEX\8p8>+(=&ZndP+1qcZ]3GN-VF]7#h\fmqaBpr:ZLU8gZ?e2&+[5,o+19F5+RN]jT\(8]4Ygpm,Gdi8JI]ZY*kHhQs
%jo@U+e%np4")t6)!I-j["+7)5_9lYiPlZ+p.g(0uQ/N@[d)l>NTU"J]GG1;\"C>bQ13g+i[R45d"1W6ErE:_h=tI$9XR]OF_tG[h
%1@0jO<bC$%%DL?71o"J_RH;e`<i-aIe&jK[&b6O/5G49E$C,@;YUpjAb,c\_<osBG9Z*kWFuJ[<>+'L86Pido5t/tkgFD]C<e^^^
%d)p=k(0[,R4^;Z3(.(l%[aN="7?^oCV$rgP@>te@NEBn+RTc(RKN2>mA_=YL<i2#'#!AQDBtCf(Fet@+Na#fFPuaao#@AK/%V]M*
%1.CS'^+UJA%qoN0gh:7=')]T;9<Jb"W$^SI--1JdOWT1"1j.%R+c>nkg)3b*'K&EYF_Fgo=q/pZ2SE.CZl`DPfB2.)q+">XSWU;$
%9Keu)H:E]Q#/sKKI;<R5q5Mff,[7qcc^.d/^F%Tp/a\k>7);b$S9Ph\e$Fh+RPdtr+BGUbl9uC>Rh3`][F6U9m7[U_g6ijT>q$mF
%(3:RZ^-o2/4-OU)joSta\(iZI<C-nf\ecmPZnj_[P^rRi:!u"/NTYt0Eo^nPg6nJa7DN@XmAsU$E^n&-FK9&B,T'lEm7^EM1pj^>
%cD6UJ-S&gB'lF0Olr7G^>uRMg8K.2O/l-a82+OTqVOU6%36VE(PVYnQ(<EMAIL>$PP$MuA\9]n9@/menmTt^nT29_Fh1dM]dhi3
%VsL,D*S"S?aojKP`N'/PURS7QXIJI=LgtsaDTjubkA&gHa)jDYZYlY8$kbCF9;`pr)(_p.e3_^s-h7BB.lo'6/d/7t1ZCC@CMgCu
%-7NjblP3,0#"N*ZNH-B!j`$/I3Kl!KUk#.DL;@lHbXuBIip&'jE7W<jK2JNuc3b@*l#Ef?C/.LZl<u_&TNK[gLkij/LSAgP;%\XR
%3?p]pK/P^/"'DCiWU['[O[<n)9K/.lb;dP9ek%.3AJL,@-6;?7^06tN]#RO=X6QmVKB<#LMOA#>queQs67-]iKrK"QA$6?_HC:RV
%@0:BXhW10*Yqqc$3D_Zh5+GNS@[HaCSZ+F^I3;*e9u=2BVIPWUJ6=O(K=,$_i$mu'/6<O_\`mZd*d2ZJEE+5?Y95Y\_<S)A@B=f1
%jV_dU;QZ:JS<2d@GZ]@"7U:iIZGVdWOkZk9-BtN"Z%!:09);e/9P)UiNXBR89[)b0j2,ArN](!&)jb9:A!:Z]@'UiZ=0hB11.AEe
%p2j7^NXaeoC!,,+Hln`e<.j;X"`DnMQTHWlh/+;uD]3+Q[n79_eu^*m9)3."kZ5mX=YZ%<?kW]?iIu9Zn&e[qRCfGi*g_hdrFh\(
%VNO?*K5FI#jXS/f10D-.jLJi,*C/K!=k*^2jV>MG<L7L2K!oJs\t%WRH:'16o<HmX<_=Rd#)kG$C5'&6JrEcqX5.Gk@9h=k]Ao&p
%[J.4^'&$mZjG#A=:,bi>7b@>+_c8[VmECjIZptO8o=:g9J#llA29fMpi5KK#0oTpgp6mUAlSVu,fkXd[eRP\oVATua!NNi1IGPYf
%YA%&i^."5"qG`#7BgaiO8p2PNqQ84'Gt;orC7M0+-ZTYL]W5Hrn`bqaTU^gAhGm8t3]7\OaOmY'[O3C\eR2s<Yth;@*c1VP@Q!uQ
%\7Ant#J7"QC9\g!l;^hh=[*U[HTn"/rC.F*AN*7]8iR*CP?8\h46niLX0u1`WE5L"S75I!qH;qm!Oi+Li@K4"a_L3VAY'KmlNtuq
%De2#"qD(@Z]Mk<<@e0)m[@4_LEoPY]Uj>';QYE>!>coU!EYI'&(A!r,MmLsBJ?@":L$(7s.e`0#1,4.@C2oM$[D>o8i\Kn2>g?U]
%*GD5oQf)P'?:[kt>jdOK,?=X7!Zu7EY+2B&p\+*<CSKM$(L(4oPbIdD=9^5):=Bs1H3G+t3)0:q/behS;t4V_Q`><m&/;$Va3;'d
%Gs]`87=3J,?(I``qGjg+1kc5,Xg\s;'fK\&^'HG$S^jGSY4PoLSALYsB%]8H,Mq-eg/g4qau\_1Q(aKkYFL6)jS=')cU-2bkEonj
%Om*bM9_+'][)*aI>eqrV>Ud(dcBTm/(9kBafj*Ys:nq.q]TV2+f9'ml>:_&QRb+,V]H/5+D4U:3D,79VcAuJEBLlM8j0m&efUe3T
%4J/NDWR\i.k>P].8Fn8PhTC\8'I/O2T?B<!b>#dnPiXr(j/m`)mIPK%0+)B2F(0^d/tQf&#ic#sket[T(m81Y)C(&TmA[OG'O>3[
%"f];W$<#hmVAp_(lJf+2^qR=m=YT0ZiYmF0^$XMbPRhh`lRDe`>!&U_03mGO'R#`ZKTt!2_W17D^0Q"0;=:erH*Y*a?'4Ls("h/)
%._JIJZ*PrYAI'1Fk0hmJ`[Tf"Cn/1a1"6gZ`q`Sl<LLL6/Q>(96IDn%F+,QQ):o;fCO@i/H;tR>@9Z^A;uPLWI9k!bD<VF?-sH7H
%"ZBXkiFRSi*:t)Y0c+%HhN`Y0`dLt46togN4f$b9MWW]4L^[RI+9N5-GgU]!ZL_[6lJR%J*E;/]2\1%KSE`>/q.A,cfmm(p27RdJ
%<GFB(V$?=kAYCRu>TJHS/0L;W8gs"cBa(N,]5-G3NON\>]5KXJ\Idn;NSKX^%cBZ^d.^(A4%oY]07B@O(AL2E(Q#7I[ALL.VD]NN
%RbE/')Bk/2AbfLS_eWgis,j>Ug)R)7d3!aWY)6hF0YFt=j1oB=Jk5M7Y[#tn+:6WSgIT^8G]drr+PHm!mVN]k.MX^FCEUVlMllDa
%3(FNr8YST%7aqPoh.4K*U$=ga'j+5D<,`a^KrY#f<QH#he^kYPcDoDloZU&2]S,($K2oC6k%Cd_S-#%;/7hjf:4f24pl*VteQ<<F
%oh?I)%`4&2QG2<(E@!ZZ[J1ja(/@Y+apP&%J`0dH&/>2PjXt;Z4+JnA43nO,bK\=]CT5uCf!n$]GjH2/*6:\%%WNU9=k1Co='7@Z
%DF/CJ;]uO,H^kCR=PVq1Jt#)#ejFkHZ\]80(HIsRTt\V$I&m%V)mJuK.sU/T7XH8(4M.3hRW^AI[BEa6?ccb."2^WGUTVX6",ibp
%>O,J?.;Ksp9GJ@5hMW2r#E<F]JC?#uKhjGsMb8m+Yoae,#ti%L[b'rlNX)mVWP'%BdLKPDoP:\qrsCF?.F)M@nATK(Aa*s#.>OU$
%#`RCrZG9GZ%mR59do'@%`A93$@9Ee+'q3eebXGtqF6)ImI@7!9ciZ`p*G_Ue,5J'Kk0),El/!-@Hru<(AbKAfAX>Kf)Kh#L>)6cE
%YOn)nOs`Ub'+P/aQ5)2.7g9n@?+K.1RFPadUFs-#LGA0-:m^LF8smFR5)H7&#->hm0=9n)]\!%3FJUq#gs_J17tZP8OBD[r-Y7Hf
%VRYXRFT?3@h.=5oFuS-KUJ>ufCn&VM2H7jcAU5NO;6tfY$YNY5!lULKl79O<4<7I6W8.qj9:7OGKQdQs3do7\>Gs(MJJtJ?,EBrX
%@Eo)&2>Ij8I5BqfebSVo4#Y'lEOBq4AAA="93cBMlhVS&ie$Ra<IgX85:].Je`X\\k73T5.S)/"J7D>\UOJaFkr>f3G@;Wel-T"8
%]P@@X#Q)%,HBU6/b1ToE^biNi-Y>')ba[YjdH:Rms*jH[nDrN^5PHosY(";#HORm=]hgirUl3K-Y,R=`f#<24hs/)*mA_f6F&Nl;
%ZE/sge]"ZVi\pIZ<upP5,=Q6+pS<$"b0eHkRd?Waho*d4bB/q-QAcKT:!Xn]*[X#[l!G$;>eSD>mHcL@:!W?qqJB.tpAD0NFa)AC
%h;W:RpCl?.V"h*1#R5\qb@n3RSEHnjLFF@@>j2Ijf%8bmOVC\SH$fOh<?3VV]`*%W]KOg$^u)$KS7->3C>A%KA%"PibP'PtGWXL4
%n>"46?oR:V,icq[oDc^$ft4\ghf#p%`+`DaI5MAG*eh.qr3Nk!*&u*B&H+1RPm_(HP7DD'-Bn%8Y)u9\L61\;r]]&b@j>"8D+Yhk
%c8&P:XFC\-qmP&0A<IltBum#-ZuhR)[\M\Y_UQ6S8qMu(OX'7Jc5IH;3?mo_rH?e76e=jqH(0tk(%q:-CZJ$%<o192s7iYF09Yk#
%Y?!jm.Jpi[<Nb5)1Z&tCi9O6BY?';4k'MJuJgB@,]4`9V4JlndrOUWUX/BLSX@qRAWHsKDYhiCWc8FM-?Pg=irKLNN<4/d[A_Y5a
%<38]tcJ,n;XS%#.(6?F7f)Z3Dh6KhjBo`2(fJVumVF=)*ZKm<;#ZT^LT$e"k[taa];PWe^>a,qh?2(+dfm]'nmk/U`iLKZ"5^Iq,
%=n#8f*#c@E/4V>CQfIdUfG(?A>FttS<+G<5AA@Kooi#@)&q<^M>@Sn_<@M(Nq5mioWu@8$6Jn).\#VM8//il.#PF"?lt^t&eJA&:
%U9mtDb+;f_O[[gk>5%@[WGYR2\*CQ-Og;Q_U&EP=^ZAMeWjOH*ea7ZWB?jj0m^l-ICH/6k2iul<FI#)>5DM_2?>Z/aia[K5eNa#U
%?QZt0Fn>7l7)!kmp$LmK"nS3RO8&P-k/#g@qtTOQk%8s_n]o,DMr"*?QY35NlaJdeQH-S]U\?EZR]#H9J+E!g<hK$M-f=a^Edr41
%5;NaJU5(_;g,jAB6H;4bG^I+#nG)/2ll15Y.;=+B4$2+Z5<7Sk?$uY.e_03%DnjgXIj>!fX+-Qr&)Q%<q+pT8o5!ctmp)AHs7ZB0
%IKuO0huEQMrPc+DZ\t<`LkcKUIlo[PUAi?>ros0DptNm)4kmZ?s8Si1s0Y##q:.\mGl)U_@_6o5p%%s;c%j=Ua.I:R2BQ4qDQhoC
%eK6aJl]^Abmd0PGj3'+$:@uq0k&GXQ$(K,-UWl;9*R?sR++7qr:s,_DkjEiW^nWI@h;.VG.pn*WiT'm[MpqB&Q>tiBiO1\rcY?[3
%GQ1b,s*o=b7J1;Z@1V&WZ^cMe0oO6lc+?cc38T(oH+8-KEcq(b(3"VhAK60^h;/(qGdhsIZTYe<<>=2C=U&2X\in`iXS?NGJ\@$=
%gs@8\V-3_"6$`u,H)Ml1Gg#4W2G_oK+"f1+caTgAph41;m<">6[)&d'Z#BfV].)X8>#c(dCW$J@G;dR.b"5mQCW1bh5!C@sg7[!k
%>I/dnrRtT5pdY]7<'<ILgX5S?j$u_.!.XHlh_Hq6`+g@"aYP.@F,p:Y2to]$k`-RGV3BdIo&'76Pk)<^C\r9[2EoFd[AI%*I4ZOR
%cc_3kDg+6p-iEH+J%9[3I+dP\&,U5Xg#r>lI=,/jq(VjCVs<m8:1Om>->jJp;jHj`B'[CMc-,GERr<N*q98m&h_4\1?f3k'j#qX:
%[sNCLG&_o9h>G<:\(cBqms'nB5Hh;3E-h0MM(D[&ksnPCkEjVln"/q/Ka@q,43-$nL5umpMq`kJoBX;t4hj!@q=59(4[-SD4/kHP
%^GpXeG&uF0o-&&Dkh5LVc#)@N:K<b=]R-T^AlPruW#rs!o%;@[g#_"i*7=T@XnD.8:SSGm`oK=C?.=AdT@<`,h@]2iZ$r'in)f,s
%q0.7"4qmN(4>?f/T77p1r2EK;DKbeA5j`_:>s5s5g4.41X\\ca=RU/lQ;-UchG\p[/f1!Io38O\D&tf&]>!&@J'22=VLJ0o/=IZ!
%5F[RE??oP+4f[Uio:*u,c3l(Adc::2`l7+&?3aZtO2k=7\5M7'Sc.s?g"`2:9tnFoL:m$GG)+?9PPt>r:Uc\FMpq!Q0%phE`OOQ,
%o5aGYrMDAYUOFl!mjDqehK9Hp1gbE]+TBaG->.qFWHZ7IOSfVaYsM;?^A"*]^F`gA%=7X/DrnHXRt(:<NDh3bc%he'r28;>rbDH(
%a)W>ps*Z@!2g>"72RAW4W+pHJmG"eLL0IHUg5`r1QZ)+,Hb1m,2]G<;VO`1ZH1Z>?QhZHKGGeh464%3G,duYs++!U3BmR#K\PgT;
%Q+spW3g8uJb'0G#\&m=a4-?3R&:o_S%e7KSd4\AB_B]PY:/Y;SGY*3c&\Zd?B8=P(7pTjoqWtQSm>W*6^K<.mr1j2WXu/)X)VNGP
%Uo]tUf24)YZ<e"J\9?hr`Dun#aFZMR4:(@lJaEb9.6U_pHBGmfBS4U5s15_N6mk0?P(1^1XP//II02SLEkXUsrshunQS%K^h4hdD
%CQ$VDcg0LB43inkj2_i`$gpfpj8;I6WQ_@ae:&j>Lt,#)$.6R!J2VlmTuSfs(-F:QbrjlYcaNGlLRto!39__C.kMfl/oNt'giR#.
%MTH+rdXSd`^@TnKihOuO^TV#R^W=CgO:,9h*e/037\Y71>0$WI-)A.s&,iG^eMh*A&!!iCb;<'\nGp=YDOD%$JU+8C#RQXDA!C@8
%g^.Vp\J'i-[g+YYOCln%kj.]bYs2"Pr59D\f/OB)GM`.-Hh(&a_`slO[FXXG;8CLJ_k!=erqtnn]RH<]k')p!ms.:u6(5I<4F)EI
%lX/DJit^[>X5f-(ch:H'lW[Wne"%<6:u-3cAqo&D:V=$'S'qadW%Z?g^Z+C1?ZA^:7sg+Xhg`'4j'SG7>ds4QroWufqq$nlh-b+n
%gOjS5:A<paIq-NfgVCeOo-aFY1NbekmC=!TI<)ua:Md40k)I@:C&[hon]B&TC0e+%jbik&/,jphSDP><7pGP+qt\cC.i5V9QY.,\
%p1IF(7?)fKe2TB.J,A0j@_:ig)]Nk+$XWirol"cc161\\k/mVf@XE5L?bX0u%;Ydu\_2@W4[6og!XG+ke^=)[m@C7,SY`&_9!pRW
%/"3EdD;#5LGlR$mMkJ@:mH%V_o?4l@bJ*YmBlUE%4TFF9FT;"eVo1N?[_9N"O[^/apUobHZB[Sdg0\_kaM715(<WNISW8_n9Znt1
%QcCJ\m,ecCo_@^pjp:`kGB%@&/3`XDrp"DRo%!I!VEN=4*hVuDf%Vu6Ft^%fJkrPQq!g^qq]AA1p;7+4qIFemch7(JiSsdWg%G#2
%EOrcHk5;MQs0>%B+6B'?=,Dh..#-fV`LHedOl%]80XQbY5n`i21Rq1Q)rIm=r7U\N:QI0lhu7qY(&G,[/oD0OIqS;bR$Z@Pc%drr
%2eQC>O2f6/hJ3$1o]G+i%D6bI.<lCm_YmR6kfintltZFJe#VlqpAHu\Ts<4I:60=RT:71cdGm#5p?h$=hLC%A`2!#?TDb++T)\CH
%qZ#2tqYoBDSGb?0OC-l_rqPU";uSsDr9+*"o`+kSd[qr\mrs1$I^'d4HlCcO6leJ%Y4M:#EPeL6,aGm$J7W@/fjEi*^J)0sJ''p!
%,H'>hbEVo'VRoV/k<C!LoDLA$hCulDLG$jOSq!?N'E8((S,%X2LYg;UQ@J%P3l`&uTg!;PhoV\kb=k&Q;d[2B1H#+F1\E-i=<5!)
%f;s=+R/Sl$a0TmfB74!1IWg1N/%;?Z'c.tld+e&lY%S'P_;"\^CgK>EmV^URK9u80T@]`B(T=L/s,ltfi-L'qCB!tSs+<6OTcNT,
%S5t1]YDs0>G.cSdghbZPB.n^u@.!%tIe?L_:8lY[H?i^MnTf4O+8W8.i=FiC*j5(NmOOoMj1KM&r8;ufC`Y_7mGKoFrc\;25NMkD
%rO8QN:OXa^l%-V7G?9Fji(m^Mh/#`lI[D0hSr<3j5F6QNhSZB9s42&DS,[d3gK`JX(HTl`msNhXU22K9Wphc+RRYn)dY1U9`eY]^
%QVs"7OnQi+q9o,JCYHPULP-ps64?2LgWb(_Qr*OfFb>lFGVYnUc?c-&-,+=S=5Ea-=+9S+\b\LLeJHhe/)M<l6\I9B`oIebS;tJE
%K]HC`r::pIS];EC>ghE^n,5.4l(e'oh0`c'-i6@tQOCq"g_b!I:OX"X>VqPj21Jh$T'%3l1q4Trb?X$h3q*#jIV#[h$dH07^Y#GN
%n>"G<bZU)6lqg44#.KlQKJ7W3>g^+Zk).=$?^88$>OJG8co^sHHtJ`LLUr4<meiJU'ZV7>i;s?Xk_[8L/H06'R-Qn]KFt_>,XeFq
%mfNdM%73jAWRln+KC4FH5\+$W<?`@#!=mO&]l2ZTr[Bct=NPnJW?8Nb*,pu$+><Zjr\0QhEC3[si_O.6bu-TrU1$;s"sI<',WVZH
%Q$J(*o<WC7Jjmfo,pAF.Q^[c]d(#1A$FarM0M<!=DG$+FGX^3nkeR8Xl:e]K'B_aaQf4;nG4(kS9'6?/%b9QWr8P''AKB*tolgf6
%qd<`h^W16ES-t4rlP:H:g4S[0,iMWa]AC&"h<u6;kT0[o&dKlNs,o8/jU6aF^NXgD^9o*S1HER6K(E0-a)'JiQPSlcnc&I]@e7_>
%`ob2!X"UY8T?cV.cq/W]elsZ9#lW0l[FRWh\j(Ic^:h%%3hN=dZ!dXs24ER#!86]nTD>pn+)F0Rq;_#5V%I3"qX]Iar*GETLL6Y!
%mGFH&0'3B[*oGaXS\98nP8QD&]mY$GXO$i%`V`86WLc3IibWgb[Csqb/?Hkj/H9om=@=5.YD__]IW^$\rL59+Uji$<Wb:jWlWYDE
%VcBK(g";M'r],,O`Vs=nT3g7=:VPVUhQ*.4d2u;+^OH>mS6_Z1(&%B9j=E+5[l;&lC[TTsqXGY+."6E=pq&WLIU-q?A3]gW)>>$5
%^UkV9pI4%J^UjHN+O@b>!\`q'H$OhE5CWd$^Xjs0s*B)EQZq+kkBV<%8c:+#c!6r$XZetQWI4Ztf5H1err]@`f5d,Sfo1?OccNXS
%q!57bM''LQM!L7C`4CAXL4-<iqtuA!/M$YE2kk$FPqDmqIM;>_C!X]H]m?os]iUE3I7'HfND-gikJdF45#TJ8oV8Inq0m[ok,i!:
%@nmP:(8(&&?CbmSMjiZr>9SJK2oB:CIkRUApK-Ou[k0U7IsuRLgYVaJ.iJ$?Do<''#QEL8nE0N2oBjeMNY5XDp%qPRe]d\Z5:c-;
%s)e%^IH9Enig-2&G7m,_O,g7_Ij=%7eLT_6`Qe*kGC4F,2+dcA5=c.oKQ_W'Q[!PcmB(fqNG,/8ALfutNW5TF=<@GHC#-c-_s]Js
%gDA_6rVP."dnb><DHHZnU.Y=+S$V^m^VN&47TF9=]>"sN38`R4e)0Bp=(_<FQ2.&MC*:+>XK8"*B;@ktmPihM1_9K,bKU:4(SAH)
%rd;>'[_I38BbPX"3*?P8qq]eC3B>re>q`JSj6J?5M`J6.o(_jaN/tt8?gb%b`Q.B<o'E;3kO&"&Dnis<Da$A!SsNAsC7B.\>o*mf
%r3SJu\p:Os+2&=jN])>2;:cWYIJ?7qmOecJs/jV%VVkN>>W)R`B_/.HV-#o?f4X8"GD3l5XPr_@]gL*&4?K)Us4_9Ga.V.BDpGN[
%4#+.W&FV;_4M1:N0/*#H?g_:Xbbae"i;6__F5O\_9n$[B<8Hu33^110?>m$]g29+-IWMi@kOe-'pr1qSm)I<X^5ldJ-(@^q3PP:R
%q"f:#V?_TZ0K&&(3qE9,6RI7On?ha/Le"=*1q5$`)F+772=V1$,%6SFfj;lIML+/r.a*l+)?K)S&%C.tA6Hq!Da*%'U\;!VK'8Y)
%5'hKq3=jOg?>;(tGI`,#m;N%!^Ku<_J&LsMT>e.C?GbR5]%R#?&+;a=PU$R;p%agMASlg4s7ij4QKg]t(r#`fq\R;bO11H/GLr\6
%eW<"FpJ78+]<f]*-[VP8LQnlo=.Wl5#^Pj[oB3S7=K_pphqh^h]6SdOntf$6Mq'l-L]28+h7S4iX6&r>00\.Z::I:SO:T'TEO'pO
%GP@^oHtIt43W9A^L9F"KDoHQ1)JMQam71s^3ohkh<sA#Nid6-%Zh[E".9?tZ+8XI>`TdBflW6=GLPrKWl=G;3pTnJGY,WA^;C=+]
%5Q4:/.t6!EOSsl,0hDGP)/Xpuhs%QJqn4nhQK.kT'l8I!:2cuZ\pL'Yh"PSd[lBSalE0AH[?q0mS\F3f@JB^;?].RF\taFmb^oVK
%U<gIMK=CL6h(O3A>^(G&DgS\Tb4#23GY;\`njOLOC*11IMrFP626@)Peh/s2qG1I)c7d6fVS)JEqo()ioduBRiX$?]EfW5f,ld:r
%%mS-ra@]c^O#rt]D_F:k[\W;,!8C7_?[7*GI'UGsrnW%[XS3Mia1Hu)8c&uup^`E=5M2UXk<\k-cb+=JH1UEem;@,rs/Q$;HZ_,g
%na9fb[r6ZboM8p'qd"%rnoo5DmX9/;SQA1+=$s(VT<)%q#MT71p-qFIRtFMn[Q=24(l$t\of("o?rJR5%LGZd$bUo$4ub)Pn7Cj3
%PAJgZ%Df>*F2R04ENmF4UPm.TkNW`H/^qZ4S'UXkpA!Y0Cbsu9NiNDcUY]LJD]1jYeWHn4pH7@$IWp/q=$oB'-aWXf%ieU[jepuP
%?0M/]RqM'%Ij-KRo'$8DEE.AJCX2A7@_91=mh9jk$h;:Yea?dO^A\&C06]MDX*L/TBm'F@JNM#J_QVLXF?'p\]-56srm/bt3>cTs
%fRC69);'qZH_8P)^"e4:5dH[R'o2'u^:>[/qE#`6HWp$B9r.N:j679$oR8u7J+<+-`B7'p\)5F0^No;$D.>R"FS8b+s7D,&Ds?i%
%YNL8XFh&jSehre;OkYAn]t+o-=R2QP8e>(=EJ8bb_lpMcW4&+t%EB84LG"GV4"#&k38'59cStIooCKL%p6g\beHHZDgO#V,7I<;Q
%]D%8[FI'\PITu=aT)4^_</K^IHqMK3U177J@?ll8K=UMqs2d0mn#jKPn<XZpVe7[/X)$_S%N@<E^OPF[[9XV;$R71=gTi"C<\3m4
%GFgr^pf*O9&"if6.139ERP#>do57l7+#UqNoSL`<a,M)7>((Is0Clb9]7B,4YKue1>g##4k2lQh+">^#)khGu>(!-haX:Z%o!JTk
%ZW;M,l;-K'3u[]?H-?Z?US>_WFSYb.QU`]>p"t8mfb"\gjU?O[oB5*m^O"b*F*mF1YICj2A$9%,+e"b#O*:L*VmF"-]%DHA6N((<
%Z[2&/bG3Ce<n`pF5$KVr#Ffd_oE&3`]B_=IUb$#D\,Y"aV3,iTXqtiI6Z'>:[D<q)TBi8kV^oa8ZC5,?T5Lj@X2Da?"1Xi0U:XSS
%%q!WJf%J#Tqtj%6I7f36n\t%Kp&0f>(\.AeHrV2@n'M`Dls"8<7J>6[]?37H3^&WPB6S`Ko[S@3r8NuIF`h?YUo^&l?!5:mqRS0A
%^\0<u,b"[3T=iH(bBH(%cEi9O)oZ[-I(K8MQHIoemuBHWq,n\2m\mo`JPPnT_A7=$?@S<TF+&(LEW(ckB.iQUYnT=6:#/YT#9.c1
%F]:<\i2SOsF39<?]088?`O=XV-eC:=II-OqoT#!TF8F;Ti0aGiFDErV0152h`ppJbcf?SJWd-agao:l'jW/K.;;7Yt--RdtA:OW9
%HduGJmgp2)qd1Z!=&hrdSMPL>#S3ME5F[B)FS"MHc-r_='<BCLbPnT%JRb)8(a=-NZRpB24A+l+(LFZKSZKj2N`PS1\nRi\=`j6-
%b=h2L?VAc>IVG]`4=p6Xj4,;q4a2*OH13H\.lG<:dI[>$g0RuJ1p;@Ncehbu4uBu=aS=RAn,>q^)Mcg.ool"RgrqqRim't`SiPV4
%?@B7h/)Ae&<K2FG4^uh5YNPs[_!AW0;3mYrNKG^WWoEGkp^5kUe[#^M`M(oGo7:0'k-hWuJNMIHIcddF'7#>Wp5Xnr9MW(KB:iPD
%C09RhHur_4=,/+nIdi'fWkN4[2H0%h:ENrY34h18^:^dG#jUUI30VPsfn.f2,4W01h;'aTfY$<QAq&'OkRO4g\NM&'[e[85I*`ej
%?a4;$IAC@uQb2V:2B6K#4<%TXYCWa0Y"l1_6i+uab&4\k(E_(@m[_scPOg$Ja6[`YfO!AWqphGhkML`6LB,Uqb1nu"3i<443-p%\
%q:#&VI/,l?*Qb2iT)\&MB:loGq't/X-08pMU`Y$o/NphXaA*t(4P$$PDJ<OedbWXSX0_W<q8.uo\p+*"]/,8;i,*gR;^j8)W>`?h
%L@4K@K$%TT?_1QKS'AoXRuKOS2.(""Y.)V=IeTVCIMPZMmJEU4mcO33o2e.jINeC5YA1P[F+(9ns)%eqrV9o*GBBsNTn;>XV9E,6
%@To2_NKSL^69>%3]=#.Pa]#g/YdCKN4MEdThiU"67JN0ZM%DTQ]EJh7eQ_FM=JbKa0tqh'Y?CIh?Q<GPl&0hYJ%-;ih-VgrDn]=r
%jmo,2mH:l/.Jd!m6pjh-53!8R)d4?YesYOmX"?<d/pCMuZX/.:9&63O:ToenDH*$^^H\?7Nk>:nD4!b-hQi78j1\,FBLQE&OD/NO
%QBR"/1cH-a[jEjFILkqoFdgiLGj"ps.RDi`qB()K&d`gVo\I9FQf@AW29)592D5F+(R<=_%Cmq^P<8MBHh?->.aL3%+4%B&hrEs^
%\"A<"j5-'r\@Tp%^Rmm^CLU46S^Xu\F_]_h+4R*mS_%[!bHX]KZd-L!R<MQ<+YW`![s09JG>n(1G!d-\8QD7EJ8j^040>ufX.>QT
%11`Ip3hPNu43>jsr02\",s$G>/aYX`o.C"1.[eDZlhB^>Pts;QNFb-ZYo4$jYd,6<8IYiO-uU<o0-78C<,uSckcBYBCLgo]TAG\H
%Bf3[aT\WGX;C0CQa!S1*&qaP0C9#>a`N&7)YG2i6f;@j*<#Sh&MMe)j;$Zc#Q0P.<C/V%VjlSk5Tuog7WYY1>mpA35ZC5gk_6BUr
%[ZUf8I609Y[62uLMF"sLlKa!M*d*6N?StXN5U/@4=S*mbo]VV&;YD59.%Y=[:ThtqgKj:!-0U2coA@9n;bPQ?6B>Jb6F!O-?g0_>
%_G@>[cG/+;pd%d-h,9#]mSN5oh/s")_sujMp+?fM<?PMZma3dL9ft%?J@dk$0"0bTE,hYN#hf*Hm<<2ehr;A&mJ=&t`bPD0raLBJ
%`RXtfDso3GAQ?#>^N[Wp&"@Sm>frajEd)_ORsod7rN(\C/a(o<r?`j<k+9)j+j!u,R3]#)aLcq>p"*8m<CVD/=Kf'=Vc\PZ9nTuG
%#CCmjoO@B59B9`r73?`C;N'p\<:blhikN4jG!cYVkJ)IRrQG'nGI,Y^1fTPX\C3Zda457Frn2(0c(bdbBIaT"ZpA(S?2QZ@r_?AC
%SK52M1AQpGI)2to+%RC>me8*n5;JM=V54tY&P'mtE,\P_r\_5JaNk!)bl0lthc%msWr!J&HudFqE9V.VD*&XKd#0RWJK]NVVroG\
%,*dUm%=_/9YEAfS^Nn;O:G]H2iIC]3_+uV3't'VaO^rn[BZauqopDq2FgM$,A\RU`s$ecYg%k;&/UkeMnRMJL?%tn(]C!.bjS1NV
%5Amk*=_NW(2jsV(0@KBT5<j#^;aRXW-JC4IH*R@]Ms!a5&1`D)7,(VI*A#ckP%`_Mj[mtZh=>tI>&'7N2pVHN(oZZD$[nlN%ti0V
%Ld-_T?TIV>(k/@MqMOBa4d#I4K$<Zk3V"WBTmL@Ci[Fb8BL:,*LQ(c`14anh"Y'7]>O^+9eU&kEPNStF#I^'!i,`jChf8Ad)F3M[
%2b3>[/75Ij1/FrSMuT6Io983V13tU#hm\+#?an0FeTp&;qpUa:F?!oI3<Q[^eN^"EP*@9UQ6OB%B'u.*Z)!8fCt@fZi&R4X8mGea
%^.9Ocb0XR(\o#\[NgC?U.V+8m'/XREnWUVMB.hsRnTR-V8AMVt78>;[$YUa`1j6P)8tMk^]TA-VkXd57#_kgSa((Zg_Nc0$!$N_j
%AfoW`S`OEQU76%e\Cm]S@\Rt94%$+>U<9jB-8!j#)PE4jG]NR*1_V61\S@hg(<]QNom<ddrHt/,@&8#j^6<3iP[3F0C5\(LJ4>`h
%g4t2&:HLF/>"@.dhc-&:UN<S0]L\i:O[hh9&.+#l2'!5>I>W2\h&RJ<%WZ$K^a(kR1l3NfEN9aM+,hk'7TeB7,A"e-Csb9!o*7Z>
%AT:=F=5OmP=t=?0EcHc48`SSj)@]a2B]2pfl+)hC^qBj<\1mT(Mf*PdXj(:Tqrhj'`,s+A5Rc9iroBRN1p"IGM29oKFlNmBr*F9i
%%PB`dH^[jlEKhT9.j&a93KfT(.S,T+::M3rO*uqbG5t5^1-qDa23HHpI\ZM]8=LjO7)2o'>QTQ768']iX>.X^G?njS3c=c7Ts:B/
%%;jA*;M]H4O]kq)N@et/W'\qTcFU1`=rjM\U1;EghEerO@a=*JTjc?&Xp.>\!bh(s+[KlkcB&sS*X-:N9(*jE.=E3TfcUr9_0+NN
%$7RT*%aT`.5db"85@.s6RPsUK"2(/BgsA;>.=!;7o,rUg"cK>2K\"io9Tjak\03MYBsu,97q,V-3p!>"F\i8EX%?cUPsQTGpHi>7
%5!"UIdjQH,8ICl9aE8Cj(c6+[omZ[VQ]c7!0@ni_4eV=\cS9lI>%8pU9?L?M.DQtVSKXcCVJ?K("uJr5E)n9rSl?rTj1I_U1:=?`
%-(\6Z=jWhZjcjpTpKa5kX?VXB-7W<3#S`s%4+e?=.^'-b:GP-/M;\g_:l$.[,6%/d+I85Sb@bP'C%Fpg2so4X(htRrR!.sM`-5'a
%MS_ch=n_RQS1QXa+WbUDPN1&E:\$gOSJ#3PGB'Eg@P:;d2A^?,<TLG7O.9<mK521J[E=gGS!9<+8f9KI:%C9+)NNA3Ggk"`kneZ%
%8S^4dHN7^)l!RGIQ]8g?+oi)!1!.PBQ$EG@$3:iW."(<\m69"rhJWNeY&eDt(^blD:M8Qqi_Q(fe*,eu@("AGDWOI%0bJp)je)<!
%)t<*d*>r#"),)rP2ls>D`&%h(Ra[;L\MrB$V1)>9/qPu]#;ER*=g=LMbp(\Y@\>2uTak`;:0_Mj-*gTW].Bpb=C3'_Ih@pml<q/2
%cl?U[d)35eAPGOn$sLX0HLH+H<uAk!'O#4R3-TVc<26f(1f"_^etEm/F-^&fI4,a^.E#YWVkUTrOqNYPg9Y:1N=cH3gBkA*1OQf\
%1BH!6ooaTlL-$N[`cd6?4:6Z_$T2nn]#bfsZBTN4/@ctZk[IC+,N^]0V(ANBEOQi_(qXrkj(@H+lM\noLfmn/E=^K!lf"fA3*Qp?
%Om:<:jl;=EqPf!F#=F^Z1Pt<DN),1CQt+qI12/[!AaQMk_es+l,+q6H;=+JjiTanu1$fAq*L[fV@Ot;/GrR)Qo](]b\&t#bA01[`
%!(o1B`1grXL/fM9AW%b^4p7]+!ZI^`;b##\%HRIY8#$ZpP>t51$Mjpm/Jg]l6Q/8o/Yk["TLp,Wqpg5J"jinP@2.;12`]eN>m):>
%1."o+N"e**A?TQEJ`s\*+!D=qk6SqdHV!BRaGACNjVr;9K;QbF3NQJr]Au:;>]NkX">GWM_?YOC>'XN&kgW)WLBR\&+-J9$,2t9H
%$AKVedkIF:D/B4!j^nR4r-ca.dWA[)WnS.19EY)u>lt(Dp=-/9Pn4jUU9esN64M;$EZJ]ON^b+n;5qCYqV=h!(qp*.5'R5fLu,eq
%omuF?\8GI\DSE>sSc"8.L*!L?j$pAE-[.lqdFE7rftZVn%gRB9q.]urX,t4;cZbT'fmM9JPJ(48\dJ1SKJ%qI60kgE#'IDlmmZ^d
%s$ak7i,/scHe8X39gC)b/$$6C>NdAriT_'e8Gfk.<s^*\o9p(F$1#/o8$m&BYITuWDB<I4(3I$AO@B_uNKa:%iHIboS#%1TF<Qtm
%G-5'=h1C+E_9/fq50!a>Kl6>S;`)2udaY2:98!-(<SH[h$J88s>P1Z5].>B_0-`<,+E#0XFI%Lk3447ZBO\N@g&_st'f3mV?kJ?W
%I4CmB"`lrrS]d%L+4"GXH':-eT"45sXjBGSD([="mkrMTgTG"'2G/HT!HaeP<Ks^gG>l0jK4;]sbrGgL[G$^g'=#'T8#,*SW"%iZ
%1&e*q3.E`dMDV!rAI3V_ah/O>6%^+$<N@^K34$5:gfP!]hl`\7D7q!HCou7XaiPZ;HCJ#Wr_]L?Ec"nU]0aCA';Y3XNRt)=,U!V0
%W(rgAPi?o;#/D&jo+G=RrH^1>@Tf(SY<5S[co:.e[)1EFRt%?Ws'$5PL?@ACno#8W`gf4[pS7QZ$HgbU7HnkO,FWUj;<^^U)W,eG
%lK`a];rVT+Sp$o/ffi0ELe<Hrs45uSr?u%QF<6*Eg9[a;D)ioI<\P6b:icW,%*,G.#C)D:6ruZqCpZ]FF6.L$r$GQnUI83Y;@G>E
%-U5@\,_*5sMKs>eKqG5-:NYaZlfqt\"&j#M&Y6D*kf,e_RH]3UHd8\cr7\?s9TsRQ@X\!YbKMY1=AuN7*Y6/BJr?pFCPD*)SH\1W
%QXLq2VhTPS?uAm607C;&ai41&f\`%HgM.Fp$IQfB">1,hJ9r^td2**:>a5tGF-M[iFOTN4Jj8A2?b"j<W/l&<PoUOber"pFih#n9
%JhpXP*,^;L>f<7L(r-OAf\O8hBItV[Q=Al#9JV2i1/bpkNR!'tjD>';(NKRU;%$Rkd]\AfVi)m9D-6n)!J2Y>k-Q^4s)$9;..E8q
%B@JlHA0h6*p=qBAC:E4WWlk%`-dBM_Im=2p4?#k[V*[?IKl:IbIF:J+>H2FmGJ.t=KfJ>jm@jo4>mCH\\A`Kq;GdYsRFA-7lg^J;
%a3Lre\%bjYWRq9SFb9=NGZ$Oi&Ok1o<aTM8V01r.S/!XHOIh]N1)dtLRRUW>p^%X<[eiEfI`dP_F5sc7:*(GSLE!"bTl3'hM&><q
%;4*c3SIgE@Jh3aFF'NQPW)o'od::aR\D(N\&4Pe.l*e:AIJ.2qX*>F0%FLnp;Oe5?qT)h-I8B,tWi#Aj2o/n%I#u#kjn02TWXEqu
%CK5$(j*b'QbY3];`G<ggV'gbr+lIgO72T$JYtcCW@p.@=aIf(@WG5r3;f]fP%C?Ts(<b"3dV&;eFL6FMVPaQFeR3Q-gIQip9omWB
%r7]JrN!f8$=q)@bLnP?Wr$@9&>.e!K!iT7*o2b`%$/4C&,24QNejrq=SdI+M1d7EuHo5h:/B]i)aE;+gVZK=`0PnN?3P'nP<$:KF
%lB55'AZGpDk@aA!:opGn:chU=@u\Q/:8Q*djRu=ApQC<^bDW[F<)S'49,A9k17tfu0=%K87NY-Ypp/)n;p%HW@h%]]H<mhj[\_f8
%P$\62.pV.Kgh;/JYRBNK]=QIPI&lKCoP-mF':mB[6]jE>H'!L)rWR@h=1o\-"Lc)0_ShoA[k?:/+[K:Y0e]OpW_b0h(_EX_!7-Ng
%U)DkIAjP$Vo@]GVm1e_sJXe*\%)'paRt9@4777r"^B#_-k/Eid8O`R*ri'qNRRP_;O8(f%4@!uV53kM=JZ7_BOT:<Ir%XcmhlC8\
%(86#&+Us[MHa\[)PO^-X5q.k,$")?uVAUH$*;qb^:(eLXl`k+]=sY6!K!'dObU]n,_4X;33Xg2T2<=#/5^jl$.M?]1HT@Br<DgCD
%?L=TfJ.%pcM7Xe*Y=ialTcq?XK_fL5i&B3U.M<$SAI)pt?>5FSbpRg/en>%jR`lu2)6_#tH%o?Sql9>?75JW,MkBn4;51?Vh3Uo[
%cOa-O;AM9d3.q,C>DfDKP^.l^p0qh]m:W4h0#W.Vl]%AMG-CSr/"f("91U+:QiJDV]Phpk<SSUoVWs)[OtO?!'R^/s3c%Vd.q`Hr
%j:EA,?A*..M#;3VHZD4/\(i]Jj'u]?5s;1iR%hfLU(n&@qd$7J>o^R0^?u#jOJNXR#$d.<9:32JHT@@]Y2[8U9to?k=g0KrHn3\^
%9:,52>/7BH>T&MfKX>'G=*Kr#@VHkQME,+b7l-a&'@<\+>EAc"[nR\0;3r&#&R>1gNQB;r)@6OJAT32Sc^G8KZ`lP#MC?[AdDT[l
%XdgnS9*2`k-H,T]0is44@W<4NM+kcNeF'fZc4P1ZTGp0p^htogTOPhkXN<Gte>V#fWR7rBI5p?%1t(1N;0jB&&2hq07Ll$sbG2KE
%'cPS5X5B'IWnYZ4Sn8_JOsMPQ70RFaBeP99i01g4/!5[4''Poe:@9YO`m8<R2R,[E@N(3O!%_cfA;hkSD;9,-"6p0J.K%>(hd,p\
%B7I18!cK&TlkT.3-)4YRl6H1?[%o1L3b:pkm0^dQImMk?/_*PH''4H.ZiVODiSr_*b4f&0[MXL7bDAXCKOu-<GnT<Z`.Gm2KqLCa
%U;DaPMi_/@=2(s:]Ng"`jTqtXB#4>u<<%"\Y=nt`C4B5>JleVP$g?Fce1oM>S<AjN7G^a@?OA4JmLT&>USCs2AZ(lMCfmdK^8O<P
%i!2.B8n.!Z+ek-"Pt[JCW@G-mjN3RtI&$:[TlI<3><EXaSBUe_8&$Fd\TJ)Ql9JEc%Vj\U5G=*+is+3#Ik&h&6&jugk'`&Tcg7A/
%j0AJ::9#V+bN?[Jmk_"31oh=&dOO96TrGX=/J/1nJo/U1,aPPVmn,uF^0PMI6X7Ha;\#;q\o6S5DhDdB:1BdY#O3u.g+SB=CH*o@
%o!5ZaRFUk76aPJ9YJGMLSPQiqYOb*;iXC])CI`BNgePWK,SmY[-CYfe8lf8"3V:B30,LBdq:%gs6Lq$JEa)R59/7-97m/M6\o!*o
%/b'APh(j"&a(O6#BaA_-]ia'9/]"m^gM-Ng`eCah[+SFGhRG\ap+@&Ba1!'$k2'BaINUiT79-+Gj#3Goq+o%;$Ric)9+:*JPJ(ja
%Hp`$!,Y@lGJ%W>9D:4MaEol.hMaCKASh[;ii4?93D!9eR*g,eM'o!"fWT:RCY%uNJ?0?i2,8&3DCKi-9P"q5lNH]s+hUH[4Q4eW+
%nYke!#EHI(Fq`t>]ij78e%uuN,5SR?WV=PYLDRAU\]iGb'<AQ\EXcKc+Nkf3T`d5J8Q@#1GlT#*@t7,4Y(<N$2K:gK4_g91GZsh-
%OrH8>%9$'794$OW`=gY5J.*CZkKnLf$M0>d=+5s$=@JNii-\IMfS7+PK-]9V6.*3_:Egk$m[nY8C%@FgJ[4WEGXkRdUa5#E#c)#T
%mcHOM_i;AD]p:hS/9<C5'W>*0OdJe\#H/ta+CU?boDO8,))W`ms.Hf)b$JWc5uH8dP\tnUJa&3,nO7)tB#@YiXehU7b%\9^/TB6h
%p@"4XCM0H%h7hgUM1Y?j_RCT^Xl0)RVW)L_1Pr83mT1'(FBp\YRO8>e8.EGo=;)WaK)kG!n.*MOoFHANi+jB`K#1O>BlL3!#,S_G
%?r2R@4>MYm\;o.YSN+#?lg#T!nkIa(M>rpa2N(TkXNKa95`7(PH.-7Z_@,WU%fnaUen-eKL"rO:W>1p=D26R*!kAj(fh7[A%:a[^
%!';IsLc,Su8Sj8K&U4K9lhV:*G`h[L.mr6,U-_?:oODJe\80kji\OP&f`=IprPU5P1NqV5<hX*:0DAL%6I2WAS,t[CdfmHb4t36<
%Pi_n%AT&229$8L(C.BBt:8>or=5P>-,i2k`Y!3#Xg^QJgg9X;I%pAdI`TuQ_mlL^#j/HsD5LD++[s&5J#M],;cMlaeEM%Dq0".hR
%l0K;T0)$`:DNAKZ[]9r2`UoKEZj$Bt:B*8MStG8\mc#K6:Wd\LmGUT=7;npSl(d*BH2%g;lW5*Uq1.t,m%qWS9XtH)Ut!:#Tb&Ms
%F)f%"%a;td<17EXC!*'eY[\-_/9JNAn`:s-@A.;e^\D'o106'4o`!IG?PmDDb8BBG)DKC$e0`..>06_hr.eV4:I^@u"9Ab),<@EO
%opR`XR*e"o%=Cj,9O^%OQ9"6G7&lf1*=f/Om8W>2&kR31f1mWLpP7.1:@DG*NdU&4Un*BjJ_J"mc%(<L6pAAU_N@!;:=6(Fbj#dG
%E8j-r%)nTZQ)3E[PsrS*#WZHL3n6c4T[%YOi4r'#=4QNqGX//AFQup'Tn<dp%,aW`,]Z(2C8X*O!mnqBRn!]S6Cm$fLaDpY6j_YA
%#3:^+U!d?S-$e<lZ$\MsGa9<paP59CL*g78m7`,ks$CicWG1ok.9b2N;`%s);GL%+;Dtq1GeZibJ,KF54@WF2\X9X5GG@_ZQs9.,
%2^.?Y-\%h..fB.>.2o1i1;0^7Z"iQ`:KL7\OH._=AF8_tNG(;Y-\595WXJ3p`N?J_LK'N(_3IFY5qo2SNDq<[H)dG.&2C03/;hDd
%fHMbN,HXgRo?7\MV]8kD(!EcJLe2Hl0<KV3=6._185C#o!/k_MCQP/V0Xe%mOEtLTW+lr*Q;*@crn4dl@TJp_$Nchs8ZQ![10mAa
%IX,#:']ogpO5*`-gSF;Lb@rjulS1*S5r"R%RT!57<rK\1WKZI&/Y_f*:Bgl<49lb?FV.O],><_"9;ghd$8&BQd>D8/Br!Ur*koQm
%Tn=?(:e1ZUoFB:X`.(?%V_O;n]$^N1iO.H\[jbJ&A4#aB]k[2+B$A"DURc@67dk#C)921mL<2:kN;"1.Y]0/l-oG6EAp8>*Qp"Y<
%?k/ged#;8^/p+XMYH<H&+Of\%r"iRYYFSL9c?OHQNJnj`?j99ELDaB<(,M)u+_KlPM_2ab\mL1pI<Z1,L"@p#Y*=*s,N\Z-c@S@!
%>h*^,F17D\jr(btR!5tE@e_9W^k`X__'n?sKMe'CM_UM_&n>4e0#eL:,K<5/'IKc#*:m170'478OIEDHV8cr;:bmf[X2,Yhfb.%R
%ni:53g+r@U`H!eDCAnd\ioYY.9B>e9/EOU%=F+BC@G.m6No_j/!\4\u"]e6+BWQg;.p2JM1gp!I9>R(1+=cocG.kb$h86a_oui:%
%)chkCCg>keYj"-3`oanL)-^8534A?7%bNDsM.)0:m=:a5ADD\]"rDo304bP-a_3E]k4OVJ)%GU1pW;2Li0f/R5S4kjd5*IL?dc`&
%XYtgh[r!'Nej7(_3AqA%ke("QW8//)89>J[6b4;a)04aGD()Op>=7LW/K5Tf!,@F-K:uA`HA:A_:f%#rd]fYMJ;nR+1$nLZ!hhVY
%/$.,p3gu!C3]o@T(W2]h?%Vl35rEm3A"fD^IPWr$"+C4!(b%),7QHs^)<3#J25Y/iTJ>=Vd)PW3kVb>XG,qkmlj^s#IW.,=Au1K,
%8Z%f(`jdTikJi]SS='ernC1uk7;!r[2`<VRrm]B7@$Q^CB_N"6:hl&ZU,$.7`@92OauF0qWTZAH9f7tbAnl[nH.0QACmh/geD.Fr
%T.[#>3T`&+rX2ot0jEqr[+T`06Hu#@CsD<LFIE;79?8[Cdul^L0?h\/<7#M8ELqus@5ul`h=^&@*\S>2H.hNHc26a=A&6.eg1"?I
%$Pne7Za\;O_/Jl*M3]Hn&8cs;&n@sk9;]GW>07U^d*aJ`L6_kSTImJVe$<fOR`BGggM9j5WTLY#-f2G"Y')p.J&1$N8uWhJ:#noV
%CVCer>AFW$hV,Ze1Y+#0E*>3b7*g0c7F2ejRl(nuCn@V&\$6##ZNZ]nfqt_I\h+"=dT54j/@K_K0X?\%qm:2liN2DtV$qOJ1`=u[
%;.nb2V:-T7\9mf!@gDg?c[/]&HT`7Kg&!i^ARC,5#`\eejr&qW4uaQ(9?ZO,47^3"NmfLKS2*eY"j:fAB^@'Oa)F1T4'%87bAlD3
%>#)\Xeo?lNY35Gt%7@&Ka/@4a93s\qRJP;k0V$A4AVTq=c\<XTcg7t1N09)u'3SDP]kb0CPIb_p7OOt3dE,=i;4g`kS)LC02JgPe
%\(_mC\iHBeFGng>c(/$6GO?s0VVM,n/`U/_MKAN'f#0^HWKm"XQJpO2RI:_+:TA01H)g+ZaYQIfigt:5]Cm/+<5@fP(5H4FCh13+
%9>'T8q9G56=\3eM@#g3?ORE[!RnX=L<5lAa_otBbPNTRjYKl2I4tJtL[KXU&3FP:=E.Z0b]gT)8T+d%^SfoRFN[0?IF@U0-q'p)B
%/4>X0>+K2;Vl\!>[+c_h%Sr$':J)UP8/n[q>qTtCQF?Oe.+>@rGtrs:]O6UnbOgak%CI192MQS);(!@mE#(LfE#RrO_fVb'%F^.t
%;5P#e>WL)g:u_sX//gB%'lVCCN8-4Z3Og%KlYoC.pK'T)Wfjj0QA_R\4'^Oklp5;e3(KHM3Pdm[;21!O;g#5],$3Rp\8uVe0hO?l
%>`B94j*Ns)Q"[lLnn7be.^BbI\HjXGP>kkM/ChF1;!%eP5;@`Po)Y3(Z"Oi!51P0Lp^5&hNQHS:>Ejaq>4FDFeiQ):NN%A0[\!D.
%\Wc`JCItT,4MY_5gh('uOm$ftaNmA5^jp@X",CTs+*<8Wd9t$M[Kj-kq'amrRSptMP+b4F=R$_1b/6t&RrTc@]k.J:5\Rpb>Jb9%
%F;59sO.C6-T?`2sW_3gDpB)Suc,I=-Oe]"?P0JqS`gbLb`4DM)8.A`1;&M<)@Vo/8MG*8C%Wh<n;A'>'NKi/W8#\#XN7.\b64<?]
%:Y%g#75>m@QHnZ(F?5@\AQ/O_QFU8o,U[XSO5-_Z7=!e:PSC;IZqr%)@94pq9N2df9gc>>Q7(5OSI91UZDsk@h"_Z%I.gs,EV&!g
%_Oa20@NG%g'0A2@a#G]IU2`2C>fH#M,o#5rf!C%1S-gQ1]7$*GZprH];MZ?Wp'[9_L*fB&Z;dE>P4RraPd9CSVc9nRXepu>JH3N-
%EDG9`@j<#\O_>a^?*]?JoXrm]B']Y$c%Hj11fLP.aNRQQ@p(0<RqG/jBk;(=0q6u%g2bK&;>iMcMj(];iSr!#Pa++%c3M@:,2Bon
%Hfsh-J9@`U!jD##Kq)O,".I*VakDY2D]UQdW?kWIo%n>*PH?*'hMJ(,MKt,t;E&7GQSCk^=m!od5WS;>jiXq$FBEOAWlBqG9)0s]
%;P[,LQMmj6dgZr?bWM,h:+aVoAsZ,'qc$:[9e3UuD`6+*du[I?j.0aEkHt'NZW1VeE;!"26gdp.ND>DmS(M+'D>NDZE3np%m@#!5
%So,.hPG=D;B&2%J3t/Oa_%G7YRMX4lHajT/Y`8s*2<ahA^1!p;.&B#;ns\mm"4l,L@EiM1_)Wu.:SjfX&iP@-NTtRthbGOOrTKPL
%ot.?tBOFF@gOjI@=rT:Cq9n2aa;9U1@Qm#XNiW!7d4m24q!8k]:baGjLt67+8dr_@CUP3!7egu9WOD.,3f,YVI'>%oekpp:9#V=9
%Bs(LO9NT[K25<\l1!>h5^"M\X_rap,l_!@RS*GC73Z])Zg[8D_._aJCRn]ZpFNNZ8M.N[Rgar3+6uXGSf&Kb1(:ht0\K@mT8qC>.
%0#V:1W,G"l8>*?CF@e@#[FtcL<j<;)mDt1jG:q0^hBh2]D)WPk!dbR+)>Er)YJ8sTYp>u&nGNo>W$MY&,=Zr*a,$Wl4n]k#lsr%N
%L"@&k:N+AOC7.=\OSFl4*YQ5U.Z<WcmF)@0,?C"XQ1XMc:=)Kh`;P-?3.CDs6S\+nR2fk'nOIK,"@sUP1rk(@\AG/_#INZ$9F*dm
%42-3J!n]k_$oD[V)-RQJ-V\32XnLJ$^]$NHkl5VsLOM8BK-9t#P%!:%6*9D1?iR@\Ne>;rhF!o58oM+k!+>Q%%rS-QkP7;ZrVqdQ
%[5#5on/`"<4?i$!j[\\p\j!-P'Dg/(lR2+L)Xu/eM5'ar5O]0qrRClC>J<\I2rY_S4L8>IUQ5b1-rXFMJ&Jo^VKX!N-0q00]HEBU
%4Z^i]+.Y0sp+LNRKVKj#rUYZW5;F=_%fS3^$SE4pq?KZB\7r2"c1R0-4UfLi+#L2$Fee,`.8p1-!\B:cVMK$bo7/>2k^nh69(-rl
%:'iruoG@AZ_4u)FGm+!E,Rk@UqEJ\S34-(RS7lt#"93FZNhb;J6c37jAf1JrXc<++%WK5Cn/)$6SJYTRLcPPLP2Ye0fZsa1QtCjH
%K@2E_W@RF&Ns$)a,'kO!>:jFkVq'NL"QdVeq;mrc1bn3"FE;h1P.C=_M<i:PFFg%GriJ**)b5C]%s.3#1h,krSKRVI>!\\V-h[&D
%<m[<q^=8(O2q=o(:[+a.IY86$rM*]J/tq!sVg[@l4d_n/p3lp*D*iS&p?-Vk6';-+Kb0\s$badEA&"YM+(Q<AHp2*Nj3W.Fmu+HL
%'n',b1`u'35$r]A*Npj2hgEsPJ+!=CT[-]pl*&,C\$Pt$A(e&t`kQmEq+I(P@KO[2%q*qZhTa?p%.Ro,kQJT7T^fJ3rmE*t?(dOL
%(G5g^k0r3#=#pkPk@aARC4&I_hMkS9>hi\nc/UAJ",6E'2W*#IL\/5>I<53AQm8VfO8AW5-R8YP3SDZ:fSBG*Vu;2As8MM?57k^3
%mJcT2L&Va7pq)7(c$S<*LB"@4r)>N:IWP7PMcQLEP0?:V"HT=8)#)sM@l[^SjO%(j+L!iGJ(TWE&uRs'5u^<Gn4VLni:Rga,6E1Z
%O-YkFJ`T^^5VX=G+K;8.MM+a7erBA']hjdt*a0[GeA?Mk!Z+%p0TbdcJlXTk,L18p#=SZA1lGN;-r3,:F2`>d8+dc0=J2mJ1btk^
%?kI>Z`\YEH]n%Fl/<4M`*c8QF_J/Fj(-E:b?isGeJo[8k4?+t_HKdcV^_.4#&0?QKd5[$Wi%ua-0^K5[kM[k[G\Y$f0?%YXDJ]O8
%+ZSBreML@-Dc1lpj[^oeB\W^D-4iAd,B1.>@N$X?=;j%P*_PAT%hJfg+djQXj"MVZiaE\+o>e1"M26pn/-X+g)\.H"F9%.@L"ekM
%#"YNp(Doo6"3YGb!\C^/UT+O>+ZUB0Jg*48Iqg-O?q`r_(e;poK&X1Q8-$lL/jXe#?ILVo^c@#C!$0H,KbspuODd-I-ioL+2///2
%LHsDnEarg<d0\n;(+++(LF7?G8M[6[#A>B8pd&N.EPr*9B[B.sC\]MZ?.<4_B/V^5Tbs(,kbjLp8`C"&U:5sV)BOEE!S8:k32-PA
%5q2tO!<d-_$(rA77X8BHZQp(c6h8_3>Tl,Pi(,O]Jg?F+^kcq:Vt=j.d")B.JmMr=IIfRP>"FYhFPrBK1KWNC5W9khXI^`2)d#)%
%M#o%f]E!B\[XgdLJLYF9KEFUeD,WK9B]MDf+DNT)c(i,)^_*6td*.56b/OH*95(I/]l\`)Sj@'%LV]`'Rb%aA+DHS\liu'7H'f(\
%1jPY%#onAiJeprnBq#--5k)U/=<Qb4:k)La%BKmNcMjfkTSsGR_kRI2+qNgp6ZA[u'HE2Geo9,jEu[jCk^[;;%Y?(<3QFGl!N2Xt
%oG1XF1.d:h"K4m!&hNR/QlnGAAOns$O[q'nQUVr_%1W\^8Fqe5o1M?Ipn0q$[-!$L9Aghtk0[R1\l:pp!iP](!4"cu.![ii4=K9N
%ZpE;%&qpiU!_6X;MUif5#B'_m_o\D2U'B[_4G6euW=t6Q.-;F8$09<<O9-h-LWiX-"crn_:L[Tqd57b(S\S-)?RATm6\-;ql;J(r
%6XYGoUYN9I8PC+sL23EXq6O4S?%Km$Ir#na,I/T?l+X%6&^Zj3YGCTL/.G(-r"UT^Hnu0BL9_0(&:+`1$]HD=%PiqXZChf(1Q)H(
%J>@<a*/g+t5e!1uO=\5[29A5Tis8sM%if/f7gm72NY@uhK$+EZYIQD2'HkPm7c_^"U>V1;b>NYLKof!'!(i>9VtV>O>0k/I@Y<0o
%.`=m_;2R/U_La!R'&Q&7J<:rk4ljL(Uinod#Uh&`[6TIV&_!A*qQnV3+BUqFs&<^J&M9)*6\7R<nJ=lT(3lsG!4/C'-mc[L"`<LU
%5b:Yb<=($d;27DS-/9aH.]2lVK"u(EAG$oE/JJ`O:eJ<WT&g.#HD)6b[(HSKEDeSmL(j2Vi2QoPd0rSiOrsaF"J^Z->-!G2CH=kt
%">*\`JJ[eu/BD<Xa,i6L@C7pX,Q2ZT[2Y!%:grk'7MWiG-nf!O#O+R1%7UFS=[U9'Z:E:+A4^$ap8h!jdPT3a3V6=E5O/]o3Rf02
%q<u8YlV\mZIh#(\XVcc]97W(s6^!)#N$>G*hSu9.Q+PYHi&ji1;E-.'EU'MkAbEW,?ba1E5upK2TkOD.-;:p9OnV6(59L<hkk-8:
%G+M4]cge'FEE@G'.-fhI)CbFg><a*A<dM`4]m?OA2Yr8J?/3b88k[d$?^S`Flb7922iVE*qC"`,3*<90lL=-spg93qm418No)sK[
%jTBoFot3@]5#1_*5>_e,F<=mK.lm:+J,F6OmBDT~>
%AI9_PrivateDataEnd
