fileFormatVersion: 2
guid: b19394b75701f4c4eaf5497e227c3351
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2972036191842016592
    second: Enemy3-Die_0
  - first:
      213: -159471237804162671
    second: Enemy3-Die_1
  - first:
      213: -7271908975891209729
    second: Enemy3-Die_2
  - first:
      213: 6582403810663315581
    second: Enemy3-Die_3
  - first:
      213: -4233480742366209014
    second: Enemy3-Die_4
  - first:
      213: -4104085121918862432
    second: Enemy3-Die_5
  - first:
      213: 2194431486516726683
    second: Enemy3-Die_6
  - first:
      213: 6578289606886903594
    second: Enemy3-Die_7
  - first:
      213: 4495158783311054929
    second: Enemy3-Die_8
  - first:
      213: 3013106740965844416
    second: Enemy3-Die_9
  - first:
      213: -2939310452358904614
    second: Enemy3-Die_10
  - first:
      213: -7698739107275621989
    second: Enemy3-Die_11
  - first:
      213: 7164601772064196678
    second: Enemy3-Die_12
  - first:
      213: 3408891426301796182
    second: Enemy3-Die_13
  - first:
      213: 1153381017663961975
    second: Enemy3-Die_14
  - first:
      213: 5150658302090646717
    second: Enemy3-Die_15
  - first:
      213: -3012442119957255600
    second: Enemy3-Die_16
  - first:
      213: 4339944327612879585
    second: Enemy3-Die_17
  - first:
      213: -8797068181711134152
    second: Enemy3-Die_18
  - first:
      213: 2115770690344236900
    second: Enemy3-Die_19
  - first:
      213: 1291130238646272762
    second: Enemy3-Die_20
  - first:
      213: 1628644410123361574
    second: Enemy3-Die_21
  - first:
      213: -1768149229200685205
    second: Enemy3-Die_22
  - first:
      213: 9076715064295181232
    second: Enemy3-Die_23
  - first:
      213: -655509917987538867
    second: Enemy3-Die_24
  - first:
      213: 1299518158781609373
    second: Enemy3-Die_25
  - first:
      213: -1314205383524535499
    second: Enemy3-Die_26
  - first:
      213: -6392498101780756578
    second: Enemy3-Die_27
  - first:
      213: 5785179071258548934
    second: Enemy3-Die_28
  - first:
      213: -1446911466899368380
    second: Enemy3-Die_29
  - first:
      213: 3482835095664444641
    second: Enemy3-Die_30
  - first:
      213: 8583866900899135933
    second: Enemy3-Die_31
  - first:
      213: -7559465039586383130
    second: Enemy3-Die_32
  - first:
      213: 8080532241963273501
    second: Enemy3-Die_33
  - first:
      213: 6194601154565674828
    second: Enemy3-Die_34
  - first:
      213: 4215731163153831983
    second: Enemy3-Die_35
  - first:
      213: 5545976062224533242
    second: Enemy3-Die_36
  - first:
      213: -8113586616164209056
    second: Enemy3-Die_37
  - first:
      213: 2586803595054750331
    second: Enemy3-Die_38
  - first:
      213: 2303568163690090351
    second: Enemy3-Die_39
  - first:
      213: 3208348337413445461
    second: Enemy3-Die_40
  - first:
      213: -7335703763139852203
    second: Enemy3-Die_41
  - first:
      213: 8341940983803048083
    second: Enemy3-Die_42
  - first:
      213: -8449456897293999444
    second: Enemy3-Die_43
  - first:
      213: -2713971215820153462
    second: Enemy3-Die_44
  - first:
      213: -762913654323211830
    second: Enemy3-Die_45
  - first:
      213: -1925726483491964220
    second: Enemy3-Die_46
  - first:
      213: -8734995482329770828
    second: Enemy3-Die_47
  - first:
      213: -4066299292104154904
    second: Enemy3-Die_48
  - first:
      213: 2613102239677133242
    second: Enemy3-Die_49
  - first:
      213: -2006805581527582338
    second: Enemy3-Die_50
  - first:
      213: 1458277070157374567
    second: Enemy3-Die_51
  - first:
      213: 6929709610784197837
    second: Enemy3-Die_52
  - first:
      213: 700373535909643369
    second: Enemy3-Die_53
  - first:
      213: -5274295097204266808
    second: Enemy3-Die_54
  - first:
      213: -8012076108860706011
    second: Enemy3-Die_55
  - first:
      213: 4841062207073559320
    second: Enemy3-Die_56
  - first:
      213: -1899025085360914061
    second: Enemy3-Die_57
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Enemy3-Die_0
      rect:
        serializedVersion: 2
        x: 10
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b2e6ad74d431c6d0800000000000000
      internalID: -2972036191842016592
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_1
      rect:
        serializedVersion: 2
        x: 75
        y: 7
        width: 41
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1918ace01c179cdf0800000000000000
      internalID: -159471237804162671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_2
      rect:
        serializedVersion: 2
        x: 139
        y: 7
        width: 41
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ff1a70d805df41b90800000000000000
      internalID: -7271908975891209729
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_3
      rect:
        serializedVersion: 2
        x: 201
        y: 3
        width: 45
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d78b3c60465695b50800000000000000
      internalID: 6582403810663315581
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_4
      rect:
        serializedVersion: 2
        x: 265
        y: 3
        width: 45
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0c1ecbd1b7af35c0800000000000000
      internalID: -4233480742366209014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_5
      rect:
        serializedVersion: 2
        x: 329
        y: 3
        width: 45
        height: 60
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ab7f04735c5b07c0800000000000000
      internalID: -4104085121918862432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_6
      rect:
        serializedVersion: 2
        x: 393
        y: 3
        width: 45
        height: 59
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9b03c57fcf247e10800000000000000
      internalID: 2194431486516726683
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_7
      rect:
        serializedVersion: 2
        x: 456
        y: 50
        width: 11
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2f43205b87ca4b50800000000000000
      internalID: 6578289606886903594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_8
      rect:
        serializedVersion: 2
        x: 472
        y: 47
        width: 8
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1588b38d813026e30800000000000000
      internalID: 4495158783311054929
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_9
      rect:
        serializedVersion: 2
        x: 490
        y: 49
        width: 11
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c5aa95ed94b0d920800000000000000
      internalID: 3013106740965844416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_10
      rect:
        serializedVersion: 2
        x: 516
        y: 52
        width: 10
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad40e8a77b87537d0800000000000000
      internalID: -2939310452358904614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_11
      rect:
        serializedVersion: 2
        x: 558
        y: 53
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b99bd645295982590800000000000000
      internalID: -7698739107275621989
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_12
      rect:
        serializedVersion: 2
        x: 579
        y: 55
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 648450d1a57cd6360800000000000000
      internalID: 7164601772064196678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_13
      rect:
        serializedVersion: 2
        x: 627
        y: 56
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65fd79ee0b0de4f20800000000000000
      internalID: 3408891426301796182
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_14
      rect:
        serializedVersion: 2
        x: 639
        y: 39
        width: 15
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77bf988bce1a10010800000000000000
      internalID: 1153381017663961975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_15
      rect:
        serializedVersion: 2
        x: 684
        y: 47
        width: 23
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db49787bf60da7740800000000000000
      internalID: 5150658302090646717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_16
      rect:
        serializedVersion: 2
        x: 710
        y: 29
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05ae58d3ad7a136d0800000000000000
      internalID: -3012442119957255600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_17
      rect:
        serializedVersion: 2
        x: 752
        y: 43
        width: 16
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e256583f549a3c30800000000000000
      internalID: 4339944327612879585
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_18
      rect:
        serializedVersion: 2
        x: 789
        y: 25
        width: 36
        height: 35
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 832673cf8198ae580800000000000000
      internalID: -8797068181711134152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_19
      rect:
        serializedVersion: 2
        x: 860
        y: 54
        width: 6
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 467b628ab3abc5d10800000000000000
      internalID: 2115770690344236900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_20
      rect:
        serializedVersion: 2
        x: 924
        y: 57
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af2c6418c140be110800000000000000
      internalID: 1291130238646272762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_21
      rect:
        serializedVersion: 2
        x: 924
        y: 47
        width: 6
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 62d14da467b1a9610800000000000000
      internalID: 1628644410123361574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_22
      rect:
        serializedVersion: 2
        x: 988
        y: 44
        width: 6
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b67cc93e4a54677e0800000000000000
      internalID: -1768149229200685205
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_23
      rect:
        serializedVersion: 2
        x: 454
        y: 11
        width: 51
        height: 43
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bb40cea248f6fd70800000000000000
      internalID: 9076715064295181232
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_24
      rect:
        serializedVersion: 2
        x: 522
        y: 43
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d487b9fd33927e6f0800000000000000
      internalID: -655509917987538867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_25
      rect:
        serializedVersion: 2
        x: 522
        y: 6
        width: 44
        height: 46
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d95f85951e0d80210800000000000000
      internalID: 1299518158781609373
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_26
      rect:
        serializedVersion: 2
        x: 581
        y: 45
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5333d19aa2103cde0800000000000000
      internalID: -1314205383524535499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_27
      rect:
        serializedVersion: 2
        x: 583
        y: 14
        width: 45
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e97cff768d84947a0800000000000000
      internalID: -6392498101780756578
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_28
      rect:
        serializedVersion: 2
        x: 627
        y: 48
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6cad360d6b5194050800000000000000
      internalID: 5785179071258548934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_29
      rect:
        serializedVersion: 2
        x: 655
        y: 28
        width: 24
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 446160adfa98bebe0800000000000000
      internalID: -1446911466899368380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_30
      rect:
        serializedVersion: 2
        x: 741
        y: 41
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e4c7501114855030800000000000000
      internalID: 3482835095664444641
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_31
      rect:
        serializedVersion: 2
        x: 853
        y: 38
        width: 21
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db183539775002770800000000000000
      internalID: 8583866900899135933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_32
      rect:
        serializedVersion: 2
        x: 917
        y: 42
        width: 21
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ea28772992671790800000000000000
      internalID: -7559465039586383130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_33
      rect:
        serializedVersion: 2
        x: 514
        y: 30
        width: 15
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d1dcf46f241d32070800000000000000
      internalID: 8080532241963273501
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_34
      rect:
        serializedVersion: 2
        x: 564
        y: 37
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c479c1ffde4a7f550800000000000000
      internalID: 6194601154565674828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_35
      rect:
        serializedVersion: 2
        x: 575
        y: 30
        width: 10
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f2814503829418a30800000000000000
      internalID: 4215731163153831983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_36
      rect:
        serializedVersion: 2
        x: 624
        y: 33
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af686b148d347fc40800000000000000
      internalID: 5545976062224533242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_37
      rect:
        serializedVersion: 2
        x: 629
        y: 41
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06e2b21d5ffb66f80800000000000000
      internalID: -8113586616164209056
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_38
      rect:
        serializedVersion: 2
        x: 676
        y: 38
        width: 7
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b7a11fa0a2c26e320800000000000000
      internalID: 2586803595054750331
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_39
      rect:
        serializedVersion: 2
        x: 696
        y: 40
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6f85a04a0be7ff10800000000000000
      internalID: 2303568163690090351
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_40
      rect:
        serializedVersion: 2
        x: 806
        y: 31
        width: 7
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5575e3770d7568c20800000000000000
      internalID: 3208348337413445461
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_41
      rect:
        serializedVersion: 2
        x: 457
        y: 10
        width: 12
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5588d8c2b48523a90800000000000000
      internalID: -7335703763139852203
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_42
      rect:
        serializedVersion: 2
        x: 494
        y: 17
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3907ad4902784c370800000000000000
      internalID: 8341940983803048083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_43
      rect:
        serializedVersion: 2
        x: 516
        y: 14
        width: 9
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca221d318bf7dba80800000000000000
      internalID: -8449456897293999444
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_44
      rect:
        serializedVersion: 2
        x: 563
        y: 16
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8d9766fe89065ad0800000000000000
      internalID: -2713971215820153462
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_45
      rect:
        serializedVersion: 2
        x: 632
        y: 13
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac115cfc2169965f0800000000000000
      internalID: -762913654323211830
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_46
      rect:
        serializedVersion: 2
        x: 660
        y: 18
        width: 10
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ca36e857f17645e0800000000000000
      internalID: -1925726483491964220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_47
      rect:
        serializedVersion: 2
        x: 672
        y: 14
        width: 19
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b4971983ef07c680800000000000000
      internalID: -8734995482329770828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_48
      rect:
        serializedVersion: 2
        x: 725
        y: 19
        width: 8
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e0a5b7865a9197c0800000000000000
      internalID: -4066299292104154904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_49
      rect:
        serializedVersion: 2
        x: 739
        y: 18
        width: 9
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab1bbb564aa934420800000000000000
      internalID: 2613102239677133242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_50
      rect:
        serializedVersion: 2
        x: 493
        y: 10
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e79f6c3c4f46624e0800000000000000
      internalID: -2006805581527582338
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_51
      rect:
        serializedVersion: 2
        x: 564
        y: 4
        width: 9
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 764e3ded447dc3410800000000000000
      internalID: 1458277070157374567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_52
      rect:
        serializedVersion: 2
        x: 577
        y: 11
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcc07630d264b2060800000000000000
      internalID: 6929709610784197837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_53
      rect:
        serializedVersion: 2
        x: 585
        y: 3
        width: 11
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9648af9250a38b900800000000000000
      internalID: 700373535909643369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_54
      rect:
        serializedVersion: 2
        x: 598
        y: 8
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8c82e82bd80fdc6b0800000000000000
      internalID: -5274295097204266808
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_55
      rect:
        serializedVersion: 2
        x: 623
        y: 2
        width: 14
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 523b6f19a336fc090800000000000000
      internalID: -8012076108860706011
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_56
      rect:
        serializedVersion: 2
        x: 644
        y: 1
        width: 13
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8132034ad68ee2340800000000000000
      internalID: 4841062207073559320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Die_57
      rect:
        serializedVersion: 2
        x: 700
        y: 0
        width: 16
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 371c18f0fbe45a5e0800000000000000
      internalID: -1899025085360914061
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Enemy3-Die_0: -2972036191842016592
      Enemy3-Die_1: -159471237804162671
      Enemy3-Die_10: -2939310452358904614
      Enemy3-Die_11: -7698739107275621989
      Enemy3-Die_12: 7164601772064196678
      Enemy3-Die_13: 3408891426301796182
      Enemy3-Die_14: 1153381017663961975
      Enemy3-Die_15: 5150658302090646717
      Enemy3-Die_16: -3012442119957255600
      Enemy3-Die_17: 4339944327612879585
      Enemy3-Die_18: -8797068181711134152
      Enemy3-Die_19: 2115770690344236900
      Enemy3-Die_2: -7271908975891209729
      Enemy3-Die_20: 1291130238646272762
      Enemy3-Die_21: 1628644410123361574
      Enemy3-Die_22: -1768149229200685205
      Enemy3-Die_23: 9076715064295181232
      Enemy3-Die_24: -655509917987538867
      Enemy3-Die_25: 1299518158781609373
      Enemy3-Die_26: -1314205383524535499
      Enemy3-Die_27: -6392498101780756578
      Enemy3-Die_28: 5785179071258548934
      Enemy3-Die_29: -1446911466899368380
      Enemy3-Die_3: 6582403810663315581
      Enemy3-Die_30: 3482835095664444641
      Enemy3-Die_31: 8583866900899135933
      Enemy3-Die_32: -7559465039586383130
      Enemy3-Die_33: 8080532241963273501
      Enemy3-Die_34: 6194601154565674828
      Enemy3-Die_35: 4215731163153831983
      Enemy3-Die_36: 5545976062224533242
      Enemy3-Die_37: -8113586616164209056
      Enemy3-Die_38: 2586803595054750331
      Enemy3-Die_39: 2303568163690090351
      Enemy3-Die_4: -4233480742366209014
      Enemy3-Die_40: 3208348337413445461
      Enemy3-Die_41: -7335703763139852203
      Enemy3-Die_42: 8341940983803048083
      Enemy3-Die_43: -8449456897293999444
      Enemy3-Die_44: -2713971215820153462
      Enemy3-Die_45: -762913654323211830
      Enemy3-Die_46: -1925726483491964220
      Enemy3-Die_47: -8734995482329770828
      Enemy3-Die_48: -4066299292104154904
      Enemy3-Die_49: 2613102239677133242
      Enemy3-Die_5: -4104085121918862432
      Enemy3-Die_50: -2006805581527582338
      Enemy3-Die_51: 1458277070157374567
      Enemy3-Die_52: 6929709610784197837
      Enemy3-Die_53: 700373535909643369
      Enemy3-Die_54: -5274295097204266808
      Enemy3-Die_55: -8012076108860706011
      Enemy3-Die_56: 4841062207073559320
      Enemy3-Die_57: -1899025085360914061
      Enemy3-Die_6: 2194431486516726683
      Enemy3-Die_7: 6578289606886903594
      Enemy3-Die_8: 4495158783311054929
      Enemy3-Die_9: 3013106740965844416
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
