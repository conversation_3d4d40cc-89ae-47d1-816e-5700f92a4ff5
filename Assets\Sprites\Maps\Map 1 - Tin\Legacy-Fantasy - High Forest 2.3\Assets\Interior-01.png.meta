fileFormatVersion: 2
guid: 041785738d57287498caf68a55f7c980
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 9005922282470283138
    second: Interior-01_0
  - first:
      213: -2299218363090941100
    second: Interior-01_1
  - first:
      213: 1334064958430550042
    second: Interior-01_2
  - first:
      213: -2877530801802796873
    second: Interior-01_3
  - first:
      213: 4474051815429698467
    second: Interior-01_4
  - first:
      213: 8477816759506125708
    second: Interior-01_5
  - first:
      213: 7355729156465041254
    second: Interior-01_6
  - first:
      213: 9068344544288912772
    second: Interior-01_7
  - first:
      213: 1958166437901023365
    second: Interior-01_8
  - first:
      213: 7768557006944882573
    second: Interior-01_9
  - first:
      213: -2865783949448231797
    second: Interior-01_10
  - first:
      213: -975719206167366078
    second: Interior-01_11
  - first:
      213: -373867369714067150
    second: Interior-01_12
  - first:
      213: -7970821674188882224
    second: Interior-01_13
  - first:
      213: -7007403119567751380
    second: Interior-01_14
  - first:
      213: -8905408693272202373
    second: Interior-01_15
  - first:
      213: -8171132025095924124
    second: Interior-01_16
  - first:
      213: 1185881367766898217
    second: Interior-01_17
  - first:
      213: 4011045497755950
    second: Interior-01_18
  - first:
      213: 3028485208872291412
    second: Interior-01_19
  - first:
      213: -4829482261854635502
    second: Interior-01_20
  - first:
      213: -4811600331405541046
    second: Interior-01_21
  - first:
      213: -4936374738304941437
    second: Interior-01_22
  - first:
      213: 407328009321993120
    second: Interior-01_23
  - first:
      213: 3432809492596274039
    second: Interior-01_24
  - first:
      213: 7398925653014551673
    second: Interior-01_25
  - first:
      213: 3619532252837542872
    second: Interior-01_26
  - first:
      213: 2951247129238131598
    second: Interior-01_27
  - first:
      213: -8887679939509448334
    second: Interior-01_28
  - first:
      213: -5660800754315658728
    second: Interior-01_29
  - first:
      213: -3149807114765735652
    second: Interior-01_30
  - first:
      213: 3176748198000228038
    second: Interior-01_31
  - first:
      213: -2598354460994661405
    second: Interior-01_32
  - first:
      213: 5582246508299964937
    second: Interior-01_33
  - first:
      213: 979377429806867614
    second: Interior-01_34
  - first:
      213: -749180563764466618
    second: Interior-01_35
  - first:
      213: 5946532050825361933
    second: Interior-01_36
  - first:
      213: 3186450686312242192
    second: Interior-01_37
  - first:
      213: -4008546351188693018
    second: Interior-01_38
  - first:
      213: -8350505777597358917
    second: Interior-01_39
  - first:
      213: -6133548967773156350
    second: Interior-01_40
  - first:
      213: -6039551812821248364
    second: Interior-01_41
  - first:
      213: 2936989324916746482
    second: Interior-01_42
  - first:
      213: -810560076874753379
    second: Interior-01_43
  - first:
      213: 3896790052557827775
    second: Interior-01_44
  - first:
      213: 3586743489239532798
    second: Interior-01_45
  - first:
      213: 476815806889240687
    second: Interior-01_46
  - first:
      213: -3049783413355108821
    second: Interior-01_47
  - first:
      213: 5132371444046428456
    second: Interior-01_48
  - first:
      213: 1207189244362657542
    second: Interior-01_49
  - first:
      213: -4983329779585703198
    second: Interior-01_50
  - first:
      213: -7363508146084569432
    second: Interior-01_51
  - first:
      213: 2492088346287760190
    second: Interior-01_52
  - first:
      213: -8814707426208766809
    second: Interior-01_53
  - first:
      213: -5224693143810465916
    second: Interior-01_54
  - first:
      213: -8771675959831554435
    second: Interior-01_55
  - first:
      213: 1360389877709556857
    second: Interior-01_56
  - first:
      213: -8320459525630170726
    second: Interior-01_57
  - first:
      213: -2202468918493915285
    second: Interior-01_58
  - first:
      213: -571985506492904038
    second: Interior-01_59
  - first:
      213: 6877661972470716991
    second: Interior-01_60
  - first:
      213: -6367886490032750173
    second: Interior-01_61
  - first:
      213: 663641263172406413
    second: Interior-01_62
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Interior-01_0
      rect:
        serializedVersion: 2
        x: 0
        y: 271
        width: 129
        height: 129
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2879a69b9967bfc70800000000000000
      internalID: 9005922282470283138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_1
      rect:
        serializedVersion: 2
        x: 130
        y: 335
        width: 44
        height: 62
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45bc8ee94198710e0800000000000000
      internalID: -2299218363090941100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_2
      rect:
        serializedVersion: 2
        x: 212
        y: 383
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a189c82f20d838210800000000000000
      internalID: 1334064958430550042
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_3
      rect:
        serializedVersion: 2
        x: 229
        y: 383
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7b0ac484bf4f018d0800000000000000
      internalID: -2877530801802796873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_4
      rect:
        serializedVersion: 2
        x: 260
        y: 383
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a3f06deb66071e30800000000000000
      internalID: 4474051815429698467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_5
      rect:
        serializedVersion: 2
        x: 277
        y: 383
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c834735ec6147a570800000000000000
      internalID: 8477816759506125708
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_6
      rect:
        serializedVersion: 2
        x: 308
        y: 383
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 66f151b02bcc41660800000000000000
      internalID: 7355729156465041254
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_7
      rect:
        serializedVersion: 2
        x: 325
        y: 383
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 485313f115b39dd70800000000000000
      internalID: 9068344544288912772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_8
      rect:
        serializedVersion: 2
        x: 356
        y: 383
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 588be2e100ecc2b10800000000000000
      internalID: 1958166437901023365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_9
      rect:
        serializedVersion: 2
        x: 373
        y: 383
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8f425030757fcb60800000000000000
      internalID: 7768557006944882573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_10
      rect:
        serializedVersion: 2
        x: 210
        y: 367
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b844f1a7ea0ba38d0800000000000000
      internalID: -2865783949448231797
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_11
      rect:
        serializedVersion: 2
        x: 242
        y: 367
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2463ed33d8c8572f0800000000000000
      internalID: -975719206167366078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_12
      rect:
        serializedVersion: 2
        x: 290
        y: 367
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 239551bab91cfcaf0800000000000000
      internalID: -373867369714067150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_13
      rect:
        serializedVersion: 2
        x: 338
        y: 367
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0debcf52be3f16190800000000000000
      internalID: -7970821674188882224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_14
      rect:
        serializedVersion: 2
        x: 369
        y: 351
        width: 31
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2733345df3b0ce90800000000000000
      internalID: -7007403119567751380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_15
      rect:
        serializedVersion: 2
        x: 212
        y: 351
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b73b5d1f9f1a96480800000000000000
      internalID: -8905408693272202373
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_16
      rect:
        serializedVersion: 2
        x: 229
        y: 351
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 466d65737be4a9e80800000000000000
      internalID: -8171132025095924124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_17
      rect:
        serializedVersion: 2
        x: 260
        y: 351
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 926108722d8157010800000000000000
      internalID: 1185881367766898217
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_18
      rect:
        serializedVersion: 2
        x: 277
        y: 351
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e29521e46004e0000800000000000000
      internalID: 4011045497755950
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_19
      rect:
        serializedVersion: 2
        x: 308
        y: 351
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45c8b5a7047570a20800000000000000
      internalID: 3028485208872291412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_20
      rect:
        serializedVersion: 2
        x: 325
        y: 351
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21aba15787b3afcb0800000000000000
      internalID: -4829482261854635502
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_21
      rect:
        serializedVersion: 2
        x: 356
        y: 351
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a41cb65ddf2c93db0800000000000000
      internalID: -4811600331405541046
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_22
      rect:
        serializedVersion: 2
        x: 210
        y: 335
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3824f1d54597e7bb0800000000000000
      internalID: -4936374738304941437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_23
      rect:
        serializedVersion: 2
        x: 242
        y: 335
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0a354b69aae17a500800000000000000
      internalID: 407328009321993120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_24
      rect:
        serializedVersion: 2
        x: 290
        y: 335
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7733ac3bb0ac3af20800000000000000
      internalID: 3432809492596274039
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_25
      rect:
        serializedVersion: 2
        x: 338
        y: 335
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97c15732ea34ea660800000000000000
      internalID: 7398925653014551673
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_26
      rect:
        serializedVersion: 2
        x: 369
        y: 319
        width: 31
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8db737728692b3230800000000000000
      internalID: 3619532252837542872
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_27
      rect:
        serializedVersion: 2
        x: 0
        y: 159
        width: 177
        height: 174
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8351aea0afe4f820800000000000000
      internalID: 2951247129238131598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_28
      rect:
        serializedVersion: 2
        x: 212
        y: 319
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2719c281f2e98a480800000000000000
      internalID: -8887679939509448334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_29
      rect:
        serializedVersion: 2
        x: 229
        y: 319
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 81a70c6c4bbc071b0800000000000000
      internalID: -5660800754315658728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_30
      rect:
        serializedVersion: 2
        x: 260
        y: 319
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c19e3651e13a944d0800000000000000
      internalID: -3149807114765735652
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_31
      rect:
        serializedVersion: 2
        x: 277
        y: 319
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c67aaaa7a3161c20800000000000000
      internalID: 3176748198000228038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_32
      rect:
        serializedVersion: 2
        x: 308
        y: 319
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e39cd7626ac0fbd0800000000000000
      internalID: -2598354460994661405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_33
      rect:
        serializedVersion: 2
        x: 325
        y: 319
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 90252ef8f9f187d40800000000000000
      internalID: 5582246508299964937
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_34
      rect:
        serializedVersion: 2
        x: 356
        y: 319
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e946cac5592779d00800000000000000
      internalID: 979377429806867614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_35
      rect:
        serializedVersion: 2
        x: 210
        y: 303
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 64c21302f306a95f0800000000000000
      internalID: -749180563764466618
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_36
      rect:
        serializedVersion: 2
        x: 242
        y: 303
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0abbe6e463568250800000000000000
      internalID: 5946532050825361933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_37
      rect:
        serializedVersion: 2
        x: 290
        y: 303
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 01cd0f3440c883c20800000000000000
      internalID: 3186450686312242192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_38
      rect:
        serializedVersion: 2
        x: 338
        y: 303
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6efa5bfe458ce58c0800000000000000
      internalID: -4008546351188693018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_39
      rect:
        serializedVersion: 2
        x: 369
        y: 287
        width: 31
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bbc5677fc3b0d1c80800000000000000
      internalID: -8350505777597358917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_40
      rect:
        serializedVersion: 2
        x: 212
        y: 287
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 20cca63f5b141eaa0800000000000000
      internalID: -6133548967773156350
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_41
      rect:
        serializedVersion: 2
        x: 229
        y: 287
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 496193a00a33f2ca0800000000000000
      internalID: -6039551812821248364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_42
      rect:
        serializedVersion: 2
        x: 260
        y: 287
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f8e6ccea3842c820800000000000000
      internalID: 2936989324916746482
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_43
      rect:
        serializedVersion: 2
        x: 277
        y: 287
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9a54c497ef40c4f0800000000000000
      internalID: -810560076874753379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_44
      rect:
        serializedVersion: 2
        x: 308
        y: 287
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbe2cfe18ed241630800000000000000
      internalID: 3896790052557827775
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_45
      rect:
        serializedVersion: 2
        x: 325
        y: 287
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef44198333ca6c130800000000000000
      internalID: 3586743489239532798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_46
      rect:
        serializedVersion: 2
        x: 356
        y: 287
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f68ace8337dfd9600800000000000000
      internalID: 476815806889240687
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_47
      rect:
        serializedVersion: 2
        x: 210
        y: 271
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b26271dd42efca5d0800000000000000
      internalID: -3049783413355108821
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_48
      rect:
        serializedVersion: 2
        x: 242
        y: 271
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 829feb9c2a8d93740800000000000000
      internalID: 5132371444046428456
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_49
      rect:
        serializedVersion: 2
        x: 290
        y: 271
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60f88e6c83cc0c010800000000000000
      internalID: 1207189244362657542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_50
      rect:
        serializedVersion: 2
        x: 338
        y: 271
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e22b21e7f7a7dab0800000000000000
      internalID: -4983329779585703198
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_51
      rect:
        serializedVersion: 2
        x: 369
        y: 239
        width: 31
        height: 37
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ae1febca509fc990800000000000000
      internalID: -7363508146084569432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_52
      rect:
        serializedVersion: 2
        x: 212
        y: 255
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e336f55b52da59220800000000000000
      internalID: 2492088346287760190
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_53
      rect:
        serializedVersion: 2
        x: 229
        y: 255
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7a424d55c4edba580800000000000000
      internalID: -8814707426208766809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_54
      rect:
        serializedVersion: 2
        x: 260
        y: 255
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 48f12ce64492e77b0800000000000000
      internalID: -5224693143810465916
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_55
      rect:
        serializedVersion: 2
        x: 277
        y: 255
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d761869503fb44680800000000000000
      internalID: -8771675959831554435
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_56
      rect:
        serializedVersion: 2
        x: 308
        y: 255
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97c4ae8d26311e210800000000000000
      internalID: 1360389877709556857
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_57
      rect:
        serializedVersion: 2
        x: 325
        y: 255
        width: 24
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a996cc6252ac78c80800000000000000
      internalID: -8320459525630170726
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_58
      rect:
        serializedVersion: 2
        x: 356
        y: 255
        width: 7
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b67d45410324f61e0800000000000000
      internalID: -2202468918493915285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_59
      rect:
        serializedVersion: 2
        x: 210
        y: 239
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9d81dc5636ef08f0800000000000000
      internalID: -571985506492904038
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_60
      rect:
        serializedVersion: 2
        x: 242
        y: 239
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3a5452ef1d527f50800000000000000
      internalID: 6877661972470716991
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_61
      rect:
        serializedVersion: 2
        x: 290
        y: 239
        width: 45
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a1c69fc9f8b0a7a0800000000000000
      internalID: -6367886490032750173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Interior-01_62
      rect:
        serializedVersion: 2
        x: 338
        y: 239
        width: 29
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8ccea5763ab53900800000000000000
      internalID: 663641263172406413
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Interior-01_0: 9005922282470283138
      Interior-01_1: -2299218363090941100
      Interior-01_10: -2865783949448231797
      Interior-01_11: -975719206167366078
      Interior-01_12: -373867369714067150
      Interior-01_13: -7970821674188882224
      Interior-01_14: -7007403119567751380
      Interior-01_15: -8905408693272202373
      Interior-01_16: -8171132025095924124
      Interior-01_17: 1185881367766898217
      Interior-01_18: 4011045497755950
      Interior-01_19: 3028485208872291412
      Interior-01_2: 1334064958430550042
      Interior-01_20: -4829482261854635502
      Interior-01_21: -4811600331405541046
      Interior-01_22: -4936374738304941437
      Interior-01_23: 407328009321993120
      Interior-01_24: 3432809492596274039
      Interior-01_25: 7398925653014551673
      Interior-01_26: 3619532252837542872
      Interior-01_27: 2951247129238131598
      Interior-01_28: -8887679939509448334
      Interior-01_29: -5660800754315658728
      Interior-01_3: -2877530801802796873
      Interior-01_30: -3149807114765735652
      Interior-01_31: 3176748198000228038
      Interior-01_32: -2598354460994661405
      Interior-01_33: 5582246508299964937
      Interior-01_34: 979377429806867614
      Interior-01_35: -749180563764466618
      Interior-01_36: 5946532050825361933
      Interior-01_37: 3186450686312242192
      Interior-01_38: -4008546351188693018
      Interior-01_39: -8350505777597358917
      Interior-01_4: 4474051815429698467
      Interior-01_40: -6133548967773156350
      Interior-01_41: -6039551812821248364
      Interior-01_42: 2936989324916746482
      Interior-01_43: -810560076874753379
      Interior-01_44: 3896790052557827775
      Interior-01_45: 3586743489239532798
      Interior-01_46: 476815806889240687
      Interior-01_47: -3049783413355108821
      Interior-01_48: 5132371444046428456
      Interior-01_49: 1207189244362657542
      Interior-01_5: 8477816759506125708
      Interior-01_50: -4983329779585703198
      Interior-01_51: -7363508146084569432
      Interior-01_52: 2492088346287760190
      Interior-01_53: -8814707426208766809
      Interior-01_54: -5224693143810465916
      Interior-01_55: -8771675959831554435
      Interior-01_56: 1360389877709556857
      Interior-01_57: -8320459525630170726
      Interior-01_58: -2202468918493915285
      Interior-01_59: -571985506492904038
      Interior-01_6: 7355729156465041254
      Interior-01_60: 6877661972470716991
      Interior-01_61: -6367886490032750173
      Interior-01_62: 663641263172406413
      Interior-01_7: 9068344544288912772
      Interior-01_8: 1958166437901023365
      Interior-01_9: 7768557006944882573
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
