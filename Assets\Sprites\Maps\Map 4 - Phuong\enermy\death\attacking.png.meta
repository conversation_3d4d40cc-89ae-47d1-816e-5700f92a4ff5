fileFormatVersion: 2
guid: cce55c9d2cdb27940b410a18c471bcb7
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 210704702857509274
    second: attacking_0
  - first:
      213: 7467001538722481690
    second: attacking_1
  - first:
      213: -9118675687842726345
    second: attacking_2
  - first:
      213: -4401908728460256907
    second: attacking_3
  - first:
      213: -2559348636315826340
    second: attacking_4
  - first:
      213: -7854623405326919840
    second: attacking_5
  - first:
      213: -3255058754827376486
    second: attacking_6
  - first:
      213: 4774364955286074200
    second: attacking_7
  - first:
      213: 3496524092526253487
    second: attacking_8
  - first:
      213: 3275866309459176758
    second: attacking_9
  - first:
      213: 5928742637116941630
    second: attacking_10
  - first:
      213: 4741326518408471436
    second: attacking_11
  - first:
      213: -2630918475818767111
    second: attacking_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: attacking_0
      rect:
        serializedVersion: 2
        x: 0
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a9973569fc29ce200800000000000000
      internalID: 210704702857509274
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_1
      rect:
        serializedVersion: 2
        x: 100
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1649dc665e10a760800000000000000
      internalID: 7467001538722481690
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_2
      rect:
        serializedVersion: 2
        x: 200
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73eb42456c4f37180800000000000000
      internalID: -9118675687842726345
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_3
      rect:
        serializedVersion: 2
        x: 300
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5795f62755749e2c0800000000000000
      internalID: -4401908728460256907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_4
      rect:
        serializedVersion: 2
        x: 400
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c57c30ca9fd5b7cd0800000000000000
      internalID: -2559348636315826340
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_5
      rect:
        serializedVersion: 2
        x: 500
        y: 200
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0671501e0a5cef290800000000000000
      internalID: -7854623405326919840
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_6
      rect:
        serializedVersion: 2
        x: 0
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a982d7bfe45b3d2d0800000000000000
      internalID: -3255058754827376486
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_7
      rect:
        serializedVersion: 2
        x: 100
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85376e391a3f14240800000000000000
      internalID: 4774364955286074200
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_8
      rect:
        serializedVersion: 2
        x: 200
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa55f930326268030800000000000000
      internalID: 3496524092526253487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_9
      rect:
        serializedVersion: 2
        x: 300
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63debfa5d07367d20800000000000000
      internalID: 3275866309459176758
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_10
      rect:
        serializedVersion: 2
        x: 400
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e3561124400274250800000000000000
      internalID: 5928742637116941630
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_11
      rect:
        serializedVersion: 2
        x: 500
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8360ed09539cc140800000000000000
      internalID: 4741326518408471436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: attacking_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fc6fcfd5991d7bd0800000000000000
      internalID: -2630918475818767111
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 3c904fe629ec54644adb41049eb8ae64
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":100.0,"y":100.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      attacking_0: 210704702857509274
      attacking_1: 7467001538722481690
      attacking_10: 5928742637116941630
      attacking_11: 4741326518408471436
      attacking_12: -2630918475818767111
      attacking_2: -9118675687842726345
      attacking_3: -4401908728460256907
      attacking_4: -2559348636315826340
      attacking_5: -7854623405326919840
      attacking_6: -3255058754827376486
      attacking_7: 4774364955286074200
      attacking_8: 3496524092526253487
      attacking_9: 3275866309459176758
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
