fileFormatVersion: 2
guid: 9b77d1f7a5e28de4bb0990a114a1a920
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 2347532270071011419
    second: Assets_0
  - first:
      213: 3052182395561689994
    second: Assets_1
  - first:
      213: 7918522965169776436
    second: Assets_2
  - first:
      213: -7383382961532566217
    second: Assets_3
  - first:
      213: 5799697062963633374
    second: Assets_4
  - first:
      213: 6113260494978225853
    second: Assets_5
  - first:
      213: 7040758548717563640
    second: Assets_6
  - first:
      213: -465877756999394448
    second: Assets_7
  - first:
      213: 5463075118845396717
    second: Assets_8
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Assets_0
      rect:
        serializedVersion: 2
        x: 79
        y: 47
        width: 33
        height: 31
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b585950792c149020800000000000000
      internalID: 2347532270071011419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_1
      rect:
        serializedVersion: 2
        x: 127
        y: 79
        width: 139
        height: 81
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a83c725b7b78b5a20800000000000000
      internalID: 3052182395561689994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_2
      rect:
        serializedVersion: 2
        x: 259
        y: 127
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 437a7b4ceae34ed60800000000000000
      internalID: 7918522965169776436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_3
      rect:
        serializedVersion: 2
        x: 263
        y: 127
        width: 9
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7350e29b054f88990800000000000000
      internalID: -7383382961532566217
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_4
      rect:
        serializedVersion: 2
        x: 267
        y: 111
        width: 21
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: edcd2da20c9ac7050800000000000000
      internalID: 5799697062963633374
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_5
      rect:
        serializedVersion: 2
        x: 174
        y: 9
        width: 114
        height: 94
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db61b9ed70aa6d450800000000000000
      internalID: 6113260494978225853
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_6
      rect:
        serializedVersion: 2
        x: 251
        y: 95
        width: 22
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8feea95b89cc5b160800000000000000
      internalID: 7040758548717563640
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_7
      rect:
        serializedVersion: 2
        x: 114
        y: 32
        width: 27
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 07b9b49f5aed889f0800000000000000
      internalID: -465877756999394448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_8
      rect:
        serializedVersion: 2
        x: 258
        y: 47
        width: 28
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de60e0040edb0db40800000000000000
      internalID: 5463075118845396717
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_9
      rect:
        serializedVersion: 2
        x: 79
        y: 122
        width: 3
        height: 1
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e1db576a2f10fb547aca8d501c16b36f
      internalID: 767201574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Assets_10
      rect:
        serializedVersion: 2
        x: 79
        y: 47
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d4d498f3517cc8b4e82fe63f39fe8443
      internalID: -104395191
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 73435092d4a6a1745bfaee53eac0d542
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Assets_10: -104395191
      Assets_9: 767201574
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
