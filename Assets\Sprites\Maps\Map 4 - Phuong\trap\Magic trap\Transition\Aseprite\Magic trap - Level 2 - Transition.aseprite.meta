fileFormatVersion: 2
guid: f20e28d7e5b5e0948a37070967adeb60
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 4
      width: 96
      height: 64
    spriteID: f1b7d9665c742da40a6f9b8dad6dfaea
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 77
      width: 96
      height: 64
    spriteID: 7d9a6f36e1beb7f4ca5108a66f03f0c5
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 77}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 77
      width: 96
      height: 64
    spriteID: 3020f6802388a6f4a9185ebafb42069b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 77}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 77
      width: 96
      height: 64
    spriteID: 0c0a60d211521be45a48a456ab93e307
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 77}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 77
      width: 96
      height: 64
    spriteID: 5f03f67de06462147837ce69df06a985
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 77}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 149
      width: 96
      height: 64
    spriteID: bbdf4df2b97c3e547a910fd2b061ce50
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 149}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 96
      height: 65
    spriteID: 5fa3812afb651764f912784de8ac7d3d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 4
      width: 96
      height: 65
    spriteID: 7f252e3cfc7ab124e8c0ca4ca5daa3c4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 4}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: 0.015384615}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 4
      width: 96
      height: 65
    spriteID: d7a2d060017220e44976517870a746d8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 4}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 149
      width: 96
      height: 64
    spriteID: 29e8ff10b5dbbad499ee673123472ef4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 149}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 149
      width: 96
      height: 64
    spriteID: 9161a60fd6af8c34c9161fa1d56f8e83
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 149}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 149
      width: 96
      height: 64
    spriteID: 49d594525d4ce704fb3c67e295c2b5b1
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 149}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 221
      width: 96
      height: 64
    spriteID: 28819d92d3242244182df9e37955a96c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 221}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 221
      width: 96
      height: 64
    spriteID: 3f6825320439452429875e128f1fc001
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 221}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 221
      width: 96
      height: 64
    spriteID: c60033d732eef424689257bc8d427d97
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 221}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 221
      width: 96
      height: 64
    spriteID: 5cbf4576c7ff09440866dd55c50e3b2e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 221}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 293
      width: 96
      height: 64
    spriteID: e995af3d41aca2545a9be981dbc128b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 293}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 293
      width: 96
      height: 64
    spriteID: bbf71ed18f1ba1645a9f0f35658610dc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 293}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 293
      width: 96
      height: 64
    spriteID: 2ba1f9dc5de3dfa40867db85a6e0fac3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 293}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2237462297
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Magic trap - Level 2 - Transition
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: f1b7d9665c742da40a6f9b8dad6dfaea
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 7d9a6f36e1beb7f4ca5108a66f03f0c5
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 3020f6802388a6f4a9185ebafb42069b
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 0c0a60d211521be45a48a456ab93e307
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5f03f67de06462147837ce69df06a985
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: bbdf4df2b97c3e547a910fd2b061ce50
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 5fa3812afb651764f912784de8ac7d3d
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: 7f252e3cfc7ab124e8c0ca4ca5daa3c4
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: -1
        width: 96
        height: 65
      spriteId: d7a2d060017220e44976517870a746d8
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 29e8ff10b5dbbad499ee673123472ef4
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 9161a60fd6af8c34c9161fa1d56f8e83
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 49d594525d4ce704fb3c67e295c2b5b1
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 28819d92d3242244182df9e37955a96c
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 3f6825320439452429875e128f1fc001
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: c60033d732eef424689257bc8d427d97
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5cbf4576c7ff09440866dd55c50e3b2e
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: e995af3d41aca2545a9be981dbc128b7
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: bbf71ed18f1ba1645a9f0f35658610dc
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 2ba1f9dc5de3dfa40867db85a6e0fac3
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings: []
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 96, y: 64}
  previousTextureSize: {x: 512, y: 512}
