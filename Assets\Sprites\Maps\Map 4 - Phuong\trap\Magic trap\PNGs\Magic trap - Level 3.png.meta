fileFormatVersion: 2
guid: 84d58b8301a5cc049b4dbf1310384af6
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7673469027116050723
    second: Magic trap - Level 3_0
  - first:
      213: 7015996904912957928
    second: Magic trap - Level 3_1
  - first:
      213: 7346355279615851865
    second: Magic trap - Level 3_2
  - first:
      213: -5731245588497859563
    second: Magic trap - Level 3_3
  - first:
      213: -2620987837672017983
    second: Magic trap - Level 3_4
  - first:
      213: 7747134453736894771
    second: Magic trap - Level 3_5
  - first:
      213: -2578803016512539505
    second: Magic trap - Level 3_6
  - first:
      213: -4517867895719826369
    second: Magic trap - Level 3_7
  - first:
      213: 6109550009211128987
    second: Magic trap - Level 3_8
  - first:
      213: -1821149752995795521
    second: Magic trap - Level 3_9
  - first:
      213: -4270093435352997573
    second: Magic trap - Level 3_10
  - first:
      213: 6129367799656235600
    second: Magic trap - Level 3_11
  - first:
      213: 6889821259494526069
    second: Magic trap - Level 3_12
  - first:
      213: -6775695443384827485
    second: Magic trap - Level 3_13
  - first:
      213: -3719124951047418941
    second: Magic trap - Level 3_14
  - first:
      213: -1956811049354947342
    second: Magic trap - Level 3_15
  - first:
      213: -252793539081322554
    second: Magic trap - Level 3_16
  - first:
      213: 370434087686482828
    second: Magic trap - Level 3_17
  - first:
      213: 1474157042715390078
    second: Magic trap - Level 3_18
  - first:
      213: 1694632407468510378
    second: Magic trap - Level 3_19
  - first:
      213: -4769291232115383954
    second: Magic trap - Level 3_20
  - first:
      213: -8735957480530241192
    second: Magic trap - Level 3_21
  - first:
      213: -5298069865605813757
    second: Magic trap - Level 3_22
  - first:
      213: -4081220790223430788
    second: Magic trap - Level 3_23
  - first:
      213: -1471481351050559491
    second: Magic trap - Level 3_24
  - first:
      213: 4657564061061293270
    second: Magic trap - Level 3_25
  - first:
      213: 7637707236050401115
    second: Magic trap - Level 3_26
  - first:
      213: 7781264304565536102
    second: Magic trap - Level 3_27
  - first:
      213: 2857819378394579791
    second: Magic trap - Level 3_28
  - first:
      213: 5022510502141800530
    second: Magic trap - Level 3_29
  - first:
      213: -8498916671329491144
    second: Magic trap - Level 3_30
  - first:
      213: -8989396930255759137
    second: Magic trap - Level 3_31
  - first:
      213: -6277370729526379272
    second: Magic trap - Level 3_32
  - first:
      213: -2230262953856608908
    second: Magic trap - Level 3_33
  - first:
      213: -4028042801067173716
    second: Magic trap - Level 3_34
  - first:
      213: -6443785148783607803
    second: Magic trap - Level 3_35
  - first:
      213: 1845673699871674073
    second: Magic trap - Level 3_36
  - first:
      213: -3943027459571371891
    second: Magic trap - Level 3_37
  - first:
      213: -7875882964808873524
    second: Magic trap - Level 3_38
  - first:
      213: 6733340361808197297
    second: Magic trap - Level 3_39
  - first:
      213: 5188345159540459972
    second: Magic trap - Level 3_40
  - first:
      213: 2626119665004003749
    second: Magic trap - Level 3_41
  - first:
      213: -6218772131606426307
    second: Magic trap - Level 3_42
  - first:
      213: -200434419484942015
    second: Magic trap - Level 3_43
  - first:
      213: 5767932459000048246
    second: Magic trap - Level 3_44
  - first:
      213: 881632078570475583
    second: Magic trap - Level 3_45
  - first:
      213: -1619135704965815594
    second: Magic trap - Level 3_46
  - first:
      213: -50495820123421596
    second: Magic trap - Level 3_47
  - first:
      213: -5701371123063724481
    second: Magic trap - Level 3_48
  - first:
      213: -8605290224821337990
    second: Magic trap - Level 3_49
  - first:
      213: -2782204753029542712
    second: Magic trap - Level 3_50
  - first:
      213: 1386448183252899549
    second: Magic trap - Level 3_51
  - first:
      213: 6692528252669043068
    second: Magic trap - Level 3_52
  - first:
      213: -7194880775295831707
    second: Magic trap - Level 3_53
  - first:
      213: -2988459509700537387
    second: Magic trap - Level 3_54
  - first:
      213: 9064949882467641095
    second: Magic trap - Level 3_55
  - first:
      213: -3824488528749204337
    second: Magic trap - Level 3_56
  - first:
      213: -2966683562985342201
    second: Magic trap - Level 3_57
  - first:
      213: -622006087202811014
    second: Magic trap - Level 3_58
  - first:
      213: -7994596260570708115
    second: Magic trap - Level 3_59
  - first:
      213: 8827525264457194052
    second: Magic trap - Level 3_60
  - first:
      213: 4704845026934134753
    second: Magic trap - Level 3_61
  - first:
      213: 6177583910039512634
    second: Magic trap - Level 3_62
  - first:
      213: 8081126429510982443
    second: Magic trap - Level 3_63
  - first:
      213: 706456228461422595
    second: Magic trap - Level 3_64
  - first:
      213: 3534761906067171382
    second: Magic trap - Level 3_65
  - first:
      213: 8034530316810772173
    second: Magic trap - Level 3_66
  - first:
      213: 8502726004636679036
    second: Magic trap - Level 3_67
  - first:
      213: -1295446450151765194
    second: Magic trap - Level 3_68
  - first:
      213: 3172365248502445653
    second: Magic trap - Level 3_69
  - first:
      213: 8295523448721347699
    second: Magic trap - Level 3_70
  - first:
      213: -724673211083441697
    second: Magic trap - Level 3_71
  - first:
      213: -9173737022368472177
    second: Magic trap - Level 3_72
  - first:
      213: 5715688753167995841
    second: Magic trap - Level 3_73
  - first:
      213: 6346277891163637925
    second: Magic trap - Level 3_74
  - first:
      213: 3098578279126543119
    second: Magic trap - Level 3_75
  - first:
      213: -3462425588241267384
    second: Magic trap - Level 3_76
  - first:
      213: 7706916336665051704
    second: Magic trap - Level 3_77
  - first:
      213: -2970826056521020943
    second: Magic trap - Level 3_78
  - first:
      213: 5174812925900440699
    second: Magic trap - Level 3_79
  - first:
      213: 4066316298453069076
    second: Magic trap - Level 3_80
  - first:
      213: 6366415196015795667
    second: Magic trap - Level 3_81
  - first:
      213: 1898107236317708083
    second: Magic trap - Level 3_82
  - first:
      213: 3647152405370922094
    second: Magic trap - Level 3_83
  - first:
      213: -8582849529062727706
    second: Magic trap - Level 3_84
  - first:
      213: -7743544687033167459
    second: Magic trap - Level 3_85
  - first:
      213: -7945444602709763904
    second: Magic trap - Level 3_86
  - first:
      213: -6617711317793639156
    second: Magic trap - Level 3_87
  - first:
      213: -4356165999799165955
    second: Magic trap - Level 3_88
  - first:
      213: 2170485182664677578
    second: Magic trap - Level 3_89
  - first:
      213: 8869512524907129268
    second: Magic trap - Level 3_90
  - first:
      213: 8703166363056946219
    second: Magic trap - Level 3_91
  - first:
      213: 4243002902804757308
    second: Magic trap - Level 3_92
  - first:
      213: -5960585040381049788
    second: Magic trap - Level 3_93
  - first:
      213: -3447721178850656737
    second: Magic trap - Level 3_94
  - first:
      213: 4945335537940373740
    second: Magic trap - Level 3_95
  - first:
      213: -4068434278730788505
    second: Magic trap - Level 3_96
  - first:
      213: 8073760003785882799
    second: Magic trap - Level 3_97
  - first:
      213: 5325181343134058946
    second: Magic trap - Level 3_98
  - first:
      213: 701032658438549646
    second: Magic trap - Level 3_99
  - first:
      213: -5692497268606192005
    second: Magic trap - Level 3_100
  - first:
      213: -8751192355803230421
    second: Magic trap - Level 3_101
  - first:
      213: 6770799220220849253
    second: Magic trap - Level 3_102
  - first:
      213: -8788956574880189435
    second: Magic trap - Level 3_103
  - first:
      213: 3248820956239175098
    second: Magic trap - Level 3_104
  - first:
      213: 3267069501976559884
    second: Magic trap - Level 3_105
  - first:
      213: 1762126896779694045
    second: Magic trap - Level 3_106
  - first:
      213: 3879904856665405549
    second: Magic trap - Level 3_107
  - first:
      213: -967203568126572930
    second: Magic trap - Level 3_108
  - first:
      213: -7565852425749024014
    second: Magic trap - Level 3_109
  - first:
      213: -6284295503481689615
    second: Magic trap - Level 3_110
  - first:
      213: -6194528879049804393
    second: Magic trap - Level 3_111
  - first:
      213: -3531461635625452356
    second: Magic trap - Level 3_112
  - first:
      213: -3837921685523408862
    second: Magic trap - Level 3_113
  - first:
      213: 8040732359031383861
    second: Magic trap - Level 3_114
  - first:
      213: -4095187743709975958
    second: Magic trap - Level 3_115
  - first:
      213: -6873115082384116471
    second: Magic trap - Level 3_116
  - first:
      213: 2373335601822577204
    second: Magic trap - Level 3_117
  - first:
      213: -7292228953045205927
    second: Magic trap - Level 3_118
  - first:
      213: 6092569601948514722
    second: Magic trap - Level 3_119
  - first:
      213: 6247679420995839709
    second: Magic trap - Level 3_120
  - first:
      213: 7016883003630003258
    second: Magic trap - Level 3_121
  - first:
      213: 4910742216798314417
    second: Magic trap - Level 3_122
  - first:
      213: 7109780948612412831
    second: Magic trap - Level 3_123
  - first:
      213: 8518157998347388020
    second: Magic trap - Level 3_124
  - first:
      213: 7170830130500233094
    second: Magic trap - Level 3_125
  - first:
      213: 2769271443541130167
    second: Magic trap - Level 3_126
  - first:
      213: 7429077160498915589
    second: Magic trap - Level 3_127
  - first:
      213: -6714674007978880748
    second: Magic trap - Level 3_128
  - first:
      213: -1294155311480474169
    second: Magic trap - Level 3_129
  - first:
      213: 6166614956897985871
    second: Magic trap - Level 3_130
  - first:
      213: -265646185348100392
    second: Magic trap - Level 3_131
  - first:
      213: 6887303016774758875
    second: Magic trap - Level 3_132
  - first:
      213: -4215754102295676557
    second: Magic trap - Level 3_133
  - first:
      213: 1126521210636082643
    second: Magic trap - Level 3_134
  - first:
      213: -9191856504102831436
    second: Magic trap - Level 3_135
  - first:
      213: -5517156130958068561
    second: Magic trap - Level 3_136
  - first:
      213: 9112640301797283106
    second: Magic trap - Level 3_137
  - first:
      213: 6253497241416570356
    second: Magic trap - Level 3_138
  - first:
      213: 3671210597527326297
    second: Magic trap - Level 3_139
  - first:
      213: 1424266888833864164
    second: Magic trap - Level 3_140
  - first:
      213: 696319115476260964
    second: Magic trap - Level 3_141
  - first:
      213: 4331964465871382759
    second: Magic trap - Level 3_142
  - first:
      213: -4686017450556912877
    second: Magic trap - Level 3_143
  - first:
      213: 1989342752128441535
    second: Magic trap - Level 3_144
  - first:
      213: -6420229881600381692
    second: Magic trap - Level 3_145
  - first:
      213: 8059588130812704288
    second: Magic trap - Level 3_146
  - first:
      213: 4604380128140815165
    second: Magic trap - Level 3_147
  - first:
      213: -1104539784735628564
    second: Magic trap - Level 3_148
  - first:
      213: 4364169091947295207
    second: Magic trap - Level 3_149
  - first:
      213: 6063168337323254395
    second: Magic trap - Level 3_150
  - first:
      213: 6463227040523616923
    second: Magic trap - Level 3_151
  - first:
      213: 3380437046453836448
    second: Magic trap - Level 3_152
  - first:
      213: 6304873189092855059
    second: Magic trap - Level 3_153
  - first:
      213: 1562684153653678610
    second: Magic trap - Level 3_154
  - first:
      213: -6222739470432902828
    second: Magic trap - Level 3_155
  - first:
      213: -4670962164101261530
    second: Magic trap - Level 3_156
  - first:
      213: 7228356034707071925
    second: Magic trap - Level 3_157
  - first:
      213: -4000530435627396952
    second: Magic trap - Level 3_158
  - first:
      213: 7163941867061185346
    second: Magic trap - Level 3_159
  - first:
      213: 2764128787580779467
    second: Magic trap - Level 3_160
  - first:
      213: 4033988254350847626
    second: Magic trap - Level 3_161
  - first:
      213: -7938367564078876360
    second: Magic trap - Level 3_162
  - first:
      213: 5173598651236556616
    second: Magic trap - Level 3_163
  - first:
      213: -2871797479469928119
    second: Magic trap - Level 3_164
  - first:
      213: 7851796504092321837
    second: Magic trap - Level 3_165
  - first:
      213: 2292191684259881396
    second: Magic trap - Level 3_166
  - first:
      213: 7840358849891262318
    second: Magic trap - Level 3_167
  - first:
      213: 1293639107879249606
    second: Magic trap - Level 3_168
  - first:
      213: -1016842203759259732
    second: Magic trap - Level 3_169
  - first:
      213: 5085860091973759091
    second: Magic trap - Level 3_170
  - first:
      213: 685202073380860517
    second: Magic trap - Level 3_171
  - first:
      213: -7554309595011140307
    second: Magic trap - Level 3_172
  - first:
      213: 5676431440729247506
    second: Magic trap - Level 3_173
  - first:
      213: -2161573267262241413
    second: Magic trap - Level 3_174
  - first:
      213: 7333454902974273918
    second: Magic trap - Level 3_175
  - first:
      213: 7441242534988060667
    second: Magic trap - Level 3_176
  - first:
      213: 7121404710261175869
    second: Magic trap - Level 3_177
  - first:
      213: 8939178166030992606
    second: Magic trap - Level 3_178
  - first:
      213: -1063325160310367018
    second: Magic trap - Level 3_179
  - first:
      213: -1889300877760417939
    second: Magic trap - Level 3_180
  - first:
      213: -7885711542664289994
    second: Magic trap - Level 3_181
  - first:
      213: 5512924200558807050
    second: Magic trap - Level 3_182
  - first:
      213: 4145984409944558969
    second: Magic trap - Level 3_183
  - first:
      213: 7123637293944316229
    second: Magic trap - Level 3_184
  - first:
      213: -807935002963374967
    second: Magic trap - Level 3_185
  - first:
      213: 6361182534129859391
    second: Magic trap - Level 3_186
  - first:
      213: 8653774940249009249
    second: Magic trap - Level 3_187
  - first:
      213: 1760178607203572971
    second: Magic trap - Level 3_188
  - first:
      213: 5687363929062996625
    second: Magic trap - Level 3_189
  - first:
      213: -1067001841281137649
    second: Magic trap - Level 3_190
  - first:
      213: -3584284292041624594
    second: Magic trap - Level 3_191
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Magic trap - Level 3_0
      rect:
        serializedVersion: 2
        x: 488
        y: 0
        width: 19
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd619cb539c528590800000000000000
      internalID: -7673469027116050723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_1
      rect:
        serializedVersion: 2
        x: 632
        y: 0
        width: 19
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e1b39e3304dd5160800000000000000
      internalID: 7015996904912957928
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_2
      rect:
        serializedVersion: 2
        x: 779
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95d7153443f73f560800000000000000
      internalID: 7346355279615851865
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_3
      rect:
        serializedVersion: 2
        x: 923
        y: 0
        width: 16
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 51c7f01b0868670b0800000000000000
      internalID: -5731245588497859563
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_4
      rect:
        serializedVersion: 2
        x: 1071
        y: 20
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1cfa0feb27160abd0800000000000000
      internalID: -2620987837672017983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_5
      rect:
        serializedVersion: 2
        x: 1193
        y: 0
        width: 23
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3317d265db9538b60800000000000000
      internalID: 7747134453736894771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_6
      rect:
        serializedVersion: 2
        x: 1214
        y: 30
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f84701fc150463cd0800000000000000
      internalID: -2578803016512539505
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_7
      rect:
        serializedVersion: 2
        x: 1214
        y: 19
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3c6a75e51f4d41c0800000000000000
      internalID: -4517867895719826369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_8
      rect:
        serializedVersion: 2
        x: 1223
        y: 28
        width: 11
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9c83841d5b79c450800000000000000
      internalID: 6109550009211128987
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_9
      rect:
        serializedVersion: 2
        x: 1225
        y: 19
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbd25e1d2f9f9b6e0800000000000000
      internalID: -1821149752995795521
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_10
      rect:
        serializedVersion: 2
        x: 1232
        y: 0
        width: 23
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b31ae0cf4a49db4c0800000000000000
      internalID: -4270093435352997573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_11
      rect:
        serializedVersion: 2
        x: 1340
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0524a3cf983ef0550800000000000000
      internalID: 6129367799656235600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_12
      rect:
        serializedVersion: 2
        x: 1358
        y: 33
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57c0b128eef8d9f50800000000000000
      internalID: 6889821259494526069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_13
      rect:
        serializedVersion: 2
        x: 1358
        y: 19
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a180d6b4e4e7f1a0800000000000000
      internalID: -6775695443384827485
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_14
      rect:
        serializedVersion: 2
        x: 1364
        y: 24
        width: 14
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3cbc681df83036cc0800000000000000
      internalID: -3719124951047418941
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_15
      rect:
        serializedVersion: 2
        x: 1372
        y: 19
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f493aed7b208d4e0800000000000000
      internalID: -1956811049354947342
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_16
      rect:
        serializedVersion: 2
        x: 1376
        y: 0
        width: 20
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6cf3b859d95ed7cf0800000000000000
      internalID: -252793539081322554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_17
      rect:
        serializedVersion: 2
        x: 1485
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8baf3117db042500800000000000000
      internalID: 370434087686482828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_18
      rect:
        serializedVersion: 2
        x: 1503
        y: 0
        width: 36
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e70f03a1502457410800000000000000
      internalID: 1474157042715390078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_19
      rect:
        serializedVersion: 2
        x: 1611
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aac8600323b848710800000000000000
      internalID: 1694632407468510378
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_20
      rect:
        serializedVersion: 2
        x: 1630
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e61a067a4e210ddb0800000000000000
      internalID: -4769291232115383954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_21
      rect:
        serializedVersion: 2
        x: 1647
        y: 0
        width: 35
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8591410e4f4a3c680800000000000000
      internalID: -8735957480530241192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_22
      rect:
        serializedVersion: 2
        x: 1684
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30a2d5117897976b0800000000000000
      internalID: -5298069865605813757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_23
      rect:
        serializedVersion: 2
        x: 1758
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7b1bc480579c57c0800000000000000
      internalID: -4081220790223430788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_24
      rect:
        serializedVersion: 2
        x: 1774
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfff409e18f349be0800000000000000
      internalID: -1471481351050559491
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_25
      rect:
        serializedVersion: 2
        x: 1796
        y: 4
        width: 30
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d42f2236ddf2a040800000000000000
      internalID: 4657564061061293270
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_26
      rect:
        serializedVersion: 2
        x: 1831
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b57f63fc3469ef960800000000000000
      internalID: 7637707236050401115
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_27
      rect:
        serializedVersion: 2
        x: 1903
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6654e0978aa9cfb60800000000000000
      internalID: 7781264304565536102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_28
      rect:
        serializedVersion: 2
        x: 1918
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f43cf61559309a720800000000000000
      internalID: 2857819378394579791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_29
      rect:
        serializedVersion: 2
        x: 1940
        y: 4
        width: 30
        height: 30
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 250b4d8f0ba83b540800000000000000
      internalID: 5022510502141800530
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_30
      rect:
        serializedVersion: 2
        x: 1976
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 837a2cd0158cd0a80800000000000000
      internalID: -8498916671329491144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_31
      rect:
        serializedVersion: 2
        x: 2029
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd85e1bae1f3f3380800000000000000
      internalID: -8989396930255759137
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_32
      rect:
        serializedVersion: 2
        x: 2048
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8fc3527949c42e8a0800000000000000
      internalID: -6277370729526379272
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_33
      rect:
        serializedVersion: 2
        x: 2061
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 475d9afe8a38c01e0800000000000000
      internalID: -2230262953856608908
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_34
      rect:
        serializedVersion: 2
        x: 2084
        y: 16
        width: 31
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ca85256f9648918c0800000000000000
      internalID: -4028042801067173716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_35
      rect:
        serializedVersion: 2
        x: 2121
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 500c0dd3c831396a0800000000000000
      internalID: -6443785148783607803
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_36
      rect:
        serializedVersion: 2
        x: 2130
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9dec12483762d9910800000000000000
      internalID: 1845673699871674073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_37
      rect:
        serializedVersion: 2
        x: 2176
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8033a4aa6d8749c0800000000000000
      internalID: -3943027459571371891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_38
      rect:
        serializedVersion: 2
        x: 2192
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc94406fb2e33b290800000000000000
      internalID: -7875882964808873524
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_39
      rect:
        serializedVersion: 2
        x: 2203
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1b6b0616261a17d50800000000000000
      internalID: 6733340361808197297
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_40
      rect:
        serializedVersion: 2
        x: 2228
        y: 14
        width: 33
        height: 20
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4c9c68efe64b00840800000000000000
      internalID: 5188345159540459972
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_41
      rect:
        serializedVersion: 2
        x: 2265
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a14b3aebe9d17420800000000000000
      internalID: 2626119665004003749
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_42
      rect:
        serializedVersion: 2
        x: 2277
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3d008cd1bb72b9a0800000000000000
      internalID: -6218772131606426307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_43
      rect:
        serializedVersion: 2
        x: 2321
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 141a6d915f9e73df0800000000000000
      internalID: -200434419484942015
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_44
      rect:
        serializedVersion: 2
        x: 2336
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67aa42bf200db0050800000000000000
      internalID: 5767932459000048246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_45
      rect:
        serializedVersion: 2
        x: 2345
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3058c154bf2c3c00800000000000000
      internalID: 881632078570475583
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_46
      rect:
        serializedVersion: 2
        x: 2354
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d26c56a7aca789e0800000000000000
      internalID: -1619135704965815594
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_47
      rect:
        serializedVersion: 2
        x: 2372
        y: 21
        width: 26
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 464c6e2b15a9c4ff0800000000000000
      internalID: -50495820123421596
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_48
      rect:
        serializedVersion: 2
        x: 2399
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f36ed15bb29a0e0b0800000000000000
      internalID: -5701371123063724481
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_49
      rect:
        serializedVersion: 2
        x: 2409
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a700260222ed39880800000000000000
      internalID: -8605290224821337990
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_50
      rect:
        serializedVersion: 2
        x: 2422
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8cc4543948f9369d0800000000000000
      internalID: -2782204753029542712
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_51
      rect:
        serializedVersion: 2
        x: 2447
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd65646e647ad3310800000000000000
      internalID: 1386448183252899549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_52
      rect:
        serializedVersion: 2
        x: 2466
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c71aa9dbcf2a0ec50800000000000000
      internalID: 6692528252669043068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_53
      rect:
        serializedVersion: 2
        x: 2479
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56174331e06a62c90800000000000000
      internalID: -7194880775295831707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_54
      rect:
        serializedVersion: 2
        x: 2514
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5d359a2d8ebd686d0800000000000000
      internalID: -2988459509700537387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_55
      rect:
        serializedVersion: 2
        x: 2552
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 707e333d3eb2dcd70800000000000000
      internalID: 9064949882467641095
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_56
      rect:
        serializedVersion: 2
        x: 2567
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f8ce85432ffaceac0800000000000000
      internalID: -3824488528749204337
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_57
      rect:
        serializedVersion: 2
        x: 2576
        y: 0
        width: 17
        height: 24
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70bb443840934d6d0800000000000000
      internalID: -2966683562985342201
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_58
      rect:
        serializedVersion: 2
        x: 2594
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a73552c52c03e57f0800000000000000
      internalID: -622006087202811014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_59
      rect:
        serializedVersion: 2
        x: 2610
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d674406ee0d7d0190800000000000000
      internalID: -7994596260570708115
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_60
      rect:
        serializedVersion: 2
        x: 2621
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44ad1c1357ba18a70800000000000000
      internalID: 8827525264457194052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_61
      rect:
        serializedVersion: 2
        x: 2658
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e71ca9ef97fa4140800000000000000
      internalID: 4704845026934134753
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_62
      rect:
        serializedVersion: 2
        x: 2694
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a36e21006df2bb550800000000000000
      internalID: 6177583910039512634
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_63
      rect:
        serializedVersion: 2
        x: 2711
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2bd5080cade52070800000000000000
      internalID: 8081126429510982443
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_64
      rect:
        serializedVersion: 2
        x: 2723
        y: 0
        width: 11
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3082ec26236ddc900800000000000000
      internalID: 706456228461422595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_65
      rect:
        serializedVersion: 2
        x: 2739
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63867b0c93ffd0130800000000000000
      internalID: 3534761906067171382
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_66
      rect:
        serializedVersion: 2
        x: 2754
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcedc9e11c2608f60800000000000000
      internalID: 8034530316810772173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_67
      rect:
        serializedVersion: 2
        x: 2763
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c7b78d87040cff570800000000000000
      internalID: 8502726004636679036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_68
      rect:
        serializedVersion: 2
        x: 2772
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 63bc9213156a50ee0800000000000000
      internalID: -1295446450151765194
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_69
      rect:
        serializedVersion: 2
        x: 2802
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55638c6b261860c20800000000000000
      internalID: 3172365248502445653
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_70
      rect:
        serializedVersion: 2
        x: 2836
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37495f200ae9f1370800000000000000
      internalID: 8295523448721347699
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_71
      rect:
        serializedVersion: 2
        x: 2845
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd93123dd8171f5f0800000000000000
      internalID: -724673211083441697
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_72
      rect:
        serializedVersion: 2
        x: 2855
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f83a0ee58c650b080800000000000000
      internalID: -9173737022368472177
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_73
      rect:
        serializedVersion: 2
        x: 2868
        y: 1
        width: 9
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c3164353a4325f40800000000000000
      internalID: 5715688753167995841
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_74
      rect:
        serializedVersion: 2
        x: 2884
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5a849b43e12821850800000000000000
      internalID: 6346277891163637925
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_75
      rect:
        serializedVersion: 2
        x: 2897
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0b2414278c500b20800000000000000
      internalID: 3098578279126543119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_76
      rect:
        serializedVersion: 2
        x: 2946
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84508afa54ef2ffc0800000000000000
      internalID: -3462425588241267384
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_77
      rect:
        serializedVersion: 2
        x: 2998
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8324390439774fa60800000000000000
      internalID: 7706916336665051704
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_78
      rect:
        serializedVersion: 2
        x: 3013
        y: 1
        width: 7
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f90793117185c6d0800000000000000
      internalID: -2970826056521020943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_79
      rect:
        serializedVersion: 2
        x: 3028
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b74456350f0a0d740800000000000000
      internalID: 5174812925900440699
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_80
      rect:
        serializedVersion: 2
        x: 3039
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41d2da111257e6830800000000000000
      internalID: 4066316298453069076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_81
      rect:
        serializedVersion: 2
        x: 3090
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3dd9b2bd3ec0a5850800000000000000
      internalID: 6366415196015795667
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_82
      rect:
        serializedVersion: 2
        x: 3140
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 33fb05e897e675a10800000000000000
      internalID: 1898107236317708083
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_83
      rect:
        serializedVersion: 2
        x: 3157
        y: 6
        width: 7
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6468db0ac94d9230800000000000000
      internalID: 3647152405370922094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_84
      rect:
        serializedVersion: 2
        x: 3172
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ef5693c3d793e880800000000000000
      internalID: -8582849529062727706
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_85
      rect:
        serializedVersion: 2
        x: 3181
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d91560b5227698490800000000000000
      internalID: -7743544687033167459
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_86
      rect:
        serializedVersion: 2
        x: 3190
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0c890f80b3c1cb190800000000000000
      internalID: -7945444602709763904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_87
      rect:
        serializedVersion: 2
        x: 3234
        y: 0
        width: 23
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c05f3dc5e9a2924a0800000000000000
      internalID: -6617711317793639156
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_88
      rect:
        serializedVersion: 2
        x: 3282
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dfb16576a1acb83c0800000000000000
      internalID: -4356165999799165955
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_89
      rect:
        serializedVersion: 2
        x: 3291
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac81afe16cc1f1e10800000000000000
      internalID: 2170485182664677578
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_90
      rect:
        serializedVersion: 2
        x: 3301
        y: 10
        width: 7
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b9a7f116a6d61b70800000000000000
      internalID: 8869512524907129268
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_91
      rect:
        serializedVersion: 2
        x: 3315
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2c332322bbd7c870800000000000000
      internalID: 8703166363056946219
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_92
      rect:
        serializedVersion: 2
        x: 3378
        y: 0
        width: 23
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c3bf0a2e8ac22ea30800000000000000
      internalID: 4243002902804757308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_93
      rect:
        serializedVersion: 2
        x: 3444
        y: 16
        width: 9
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 444849e7c7fb74da0800000000000000
      internalID: -5960585040381049788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_94
      rect:
        serializedVersion: 2
        x: 3457
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f1ef30acadb3720d0800000000000000
      internalID: -3447721178850656737
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_95
      rect:
        serializedVersion: 2
        x: 3515
        y: 0
        width: 16
        height: 28
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce02d02687c51a440800000000000000
      internalID: 4945335537940373740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_96
      rect:
        serializedVersion: 2
        x: 3586
        y: 14
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7619a3834940a87c0800000000000000
      internalID: -4068434278730788505
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_97
      rect:
        serializedVersion: 2
        x: 3599
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: facf96362f1cb0070800000000000000
      internalID: 8073760003785882799
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_98
      rect:
        serializedVersion: 2
        x: 3608
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2c90b02b738d6e940800000000000000
      internalID: 5325181343134058946
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_99
      rect:
        serializedVersion: 2
        x: 3728
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e807d261d719ab900800000000000000
      internalID: 701032658438549646
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_100
      rect:
        serializedVersion: 2
        x: 3737
        y: 21
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b76b8d3f4ef2001b0800000000000000
      internalID: -5692497268606192005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_101
      rect:
        serializedVersion: 2
        x: 59
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2f0c3f0be48d8680800000000000000
      internalID: -8751192355803230421
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_102
      rect:
        serializedVersion: 2
        x: 199
        y: 5
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56c61ce7406b6fd50800000000000000
      internalID: 6770799220220849253
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_103
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 19
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 504beef9f8a570680800000000000000
      internalID: -8788956574880189435
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_104
      rect:
        serializedVersion: 2
        x: 217
        y: 0
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: abd1b7b5271261d20800000000000000
      internalID: 3248820956239175098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_105
      rect:
        serializedVersion: 2
        x: 343
        y: 6
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c0143d10766f65d20800000000000000
      internalID: 3267069501976559884
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_106
      rect:
        serializedVersion: 2
        x: 344
        y: 0
        width: 19
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ddf07b39315547810800000000000000
      internalID: 1762126896779694045
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_107
      rect:
        serializedVersion: 2
        x: 361
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d68bad719e038d530800000000000000
      internalID: 3879904856665405549
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_108
      rect:
        serializedVersion: 2
        x: 487
        y: 6
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e764350ba7dc392f0800000000000000
      internalID: -967203568126572930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_109
      rect:
        serializedVersion: 2
        x: 505
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f2b33ced41b00790800000000000000
      internalID: -7565852425749024014
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_110
      rect:
        serializedVersion: 2
        x: 631
        y: 5
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f9a3630982b9c8a0800000000000000
      internalID: -6284295503481689615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_111
      rect:
        serializedVersion: 2
        x: 649
        y: 0
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7957655fdcc980aa0800000000000000
      internalID: -6194528879049804393
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_112
      rect:
        serializedVersion: 2
        x: 2345
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cbcff965a5abdfec0800000000000000
      internalID: -3531461635625452356
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_113
      rect:
        serializedVersion: 2
        x: 2354
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 228db9aaf86fcbac0800000000000000
      internalID: -3837921685523408862
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_114
      rect:
        serializedVersion: 2
        x: 2390
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 537ed0a9a7b669f60800000000000000
      internalID: 8040732359031383861
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_115
      rect:
        serializedVersion: 2
        x: 2399
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6a22afb178fa27c0800000000000000
      internalID: -4095187743709975958
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_116
      rect:
        serializedVersion: 2
        x: 2763
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9057d167f3acd90a0800000000000000
      internalID: -6873115082384116471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_117
      rect:
        serializedVersion: 2
        x: 2772
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4323c67c728cfe020800000000000000
      internalID: 2373335601822577204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_118
      rect:
        serializedVersion: 2
        x: 2836
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95cfbe0376cccca90800000000000000
      internalID: -7292228953045205927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_119
      rect:
        serializedVersion: 2
        x: 2845
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2a9fb7de5c72d8450800000000000000
      internalID: 6092569601948514722
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_120
      rect:
        serializedVersion: 2
        x: 3181
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd6a9fee45734b650800000000000000
      internalID: 6247679420995839709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_121
      rect:
        serializedVersion: 2
        x: 3190
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a34a7dd2ae9f06160800000000000000
      internalID: 7016883003630003258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_122
      rect:
        serializedVersion: 2
        x: 3282
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1bf17c72706762440800000000000000
      internalID: 4910742216798314417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_123
      rect:
        serializedVersion: 2
        x: 3291
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f95f1e08a140ba260800000000000000
      internalID: 7109780948612412831
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_124
      rect:
        serializedVersion: 2
        x: 3599
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47406208193963670800000000000000
      internalID: 8518157998347388020
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_125
      rect:
        serializedVersion: 2
        x: 3608
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6876221d208e38360800000000000000
      internalID: 7170830130500233094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_126
      rect:
        serializedVersion: 2
        x: 3659
        y: 0
        width: 16
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7bbf6e6a4bd6e6620800000000000000
      internalID: 2769271443541130167
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_127
      rect:
        serializedVersion: 2
        x: 3728
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50961880152691760800000000000000
      internalID: 7429077160498915589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_128
      rect:
        serializedVersion: 2
        x: 3737
        y: 12
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 411dedbc09fa0d2a0800000000000000
      internalID: -6714674007978880748
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_129
      rect:
        serializedVersion: 2
        x: 3803
        y: 0
        width: 16
        height: 19
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7c57507d99c3a0ee0800000000000000
      internalID: -1294155311480474169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_130
      rect:
        serializedVersion: 2
        x: 3947
        y: 0
        width: 16
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f45e38491a7349550800000000000000
      internalID: 6166614956897985871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_131
      rect:
        serializedVersion: 2
        x: 4091
        y: 0
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8de836ea33c305cf0800000000000000
      internalID: -265646185348100392
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_132
      rect:
        serializedVersion: 2
        x: 55
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd1f55f6a9d949f50800000000000000
      internalID: 6887303016774758875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_133
      rect:
        serializedVersion: 2
        x: 73
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3793540eaf1ae75c0800000000000000
      internalID: -4215754102295676557
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_134
      rect:
        serializedVersion: 2
        x: 775
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3dd18ef821532af00800000000000000
      internalID: 1126521210636082643
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_135
      rect:
        serializedVersion: 2
        x: 793
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ba3696c537ff6080800000000000000
      internalID: -9191856504102831436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_136
      rect:
        serializedVersion: 2
        x: 919
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa430e3eabf1f63b0800000000000000
      internalID: -5517156130958068561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_137
      rect:
        serializedVersion: 2
        x: 937
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 225198ad21a967e70800000000000000
      internalID: 9112640301797283106
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_138
      rect:
        serializedVersion: 2
        x: 1063
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4f5158ecb92e8c650800000000000000
      internalID: 6253497241416570356
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_139
      rect:
        serializedVersion: 2
        x: 1067
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95e04496692c2f230800000000000000
      internalID: 3671210597527326297
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_140
      rect:
        serializedVersion: 2
        x: 1081
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e11b46703304c310800000000000000
      internalID: 1424266888833864164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_141
      rect:
        serializedVersion: 2
        x: 1207
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 468d648db82d9a900800000000000000
      internalID: 696319115476260964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_142
      rect:
        serializedVersion: 2
        x: 1214
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e8e0efdaba3e1c30800000000000000
      internalID: 4331964465871382759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_143
      rect:
        serializedVersion: 2
        x: 1225
        y: 0
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31b0640d4fbe7feb0800000000000000
      internalID: -4686017450556912877
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_144
      rect:
        serializedVersion: 2
        x: 1351
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fbc1d1d61b09b9b10800000000000000
      internalID: 1989342752128441535
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_145
      rect:
        serializedVersion: 2
        x: 1355
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 40d6cd240f2c6e6a0800000000000000
      internalID: -6420229881600381692
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_146
      rect:
        serializedVersion: 2
        x: 1369
        y: 0
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 026bb0514b869df60800000000000000
      internalID: 8059588130812704288
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_147
      rect:
        serializedVersion: 2
        x: 1495
        y: 0
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3b63bcd45b06ef30800000000000000
      internalID: 4604380128140815165
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_148
      rect:
        serializedVersion: 2
        x: 1502
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce2eb8afae2eba0f0800000000000000
      internalID: -1104539784735628564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_149
      rect:
        serializedVersion: 2
        x: 1516
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e1f141baa4a09c30800000000000000
      internalID: 4364169091947295207
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_150
      rect:
        serializedVersion: 2
        x: 1639
        y: 0
        width: 9
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b726c8f8a73b42450800000000000000
      internalID: 6063168337323254395
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_151
      rect:
        serializedVersion: 2
        x: 1646
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b920c1defbef1b950800000000000000
      internalID: 6463227040523616923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_152
      rect:
        serializedVersion: 2
        x: 1660
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0aa937b5599b9ee20800000000000000
      internalID: 3380437046453836448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_153
      rect:
        serializedVersion: 2
        x: 1783
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31d83c5c2c86f7750800000000000000
      internalID: 6304873189092855059
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_154
      rect:
        serializedVersion: 2
        x: 1787
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 212d8a255f4cfa510800000000000000
      internalID: 1562684153653678610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_155
      rect:
        serializedVersion: 2
        x: 1801
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4516b0fcb6364a9a0800000000000000
      internalID: -6222739470432902828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_156
      rect:
        serializedVersion: 2
        x: 1927
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6275479d8a86d2fb0800000000000000
      internalID: -4670962164101261530
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_157
      rect:
        serializedVersion: 2
        x: 1931
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b324ce1487405460800000000000000
      internalID: 7228356034707071925
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_158
      rect:
        serializedVersion: 2
        x: 1945
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a05a47b3c24b78c0800000000000000
      internalID: -4000530435627396952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_159
      rect:
        serializedVersion: 2
        x: 2071
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24b0bb10c2f6b6360800000000000000
      internalID: 7163941867061185346
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_160
      rect:
        serializedVersion: 2
        x: 2075
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bcbfe21cc782c5620800000000000000
      internalID: 2764128787580779467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_161
      rect:
        serializedVersion: 2
        x: 2089
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a8e2ea0c1fa9bf730800000000000000
      internalID: 4033988254350847626
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_162
      rect:
        serializedVersion: 2
        x: 2215
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 839789e72c045d190800000000000000
      internalID: -7938367564078876360
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_163
      rect:
        serializedVersion: 2
        x: 2219
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84f1569ff805cc740800000000000000
      internalID: 5173598651236556616
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_164
      rect:
        serializedVersion: 2
        x: 2233
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 941355968635528d0800000000000000
      internalID: -2871797479469928119
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_165
      rect:
        serializedVersion: 2
        x: 2359
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d28a313e15f27fc60800000000000000
      internalID: 7851796504092321837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_166
      rect:
        serializedVersion: 2
        x: 2363
        y: 0
        width: 15
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b12a6931308fcf10800000000000000
      internalID: 2292191684259881396
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_167
      rect:
        serializedVersion: 2
        x: 2377
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6fdb5f75dc8ecc60800000000000000
      internalID: 7840358849891262318
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_168
      rect:
        serializedVersion: 2
        x: 2503
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6c622b42aede3f110800000000000000
      internalID: 1293639107879249606
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_169
      rect:
        serializedVersion: 2
        x: 2510
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cafcd44376373e1f0800000000000000
      internalID: -1016842203759259732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_170
      rect:
        serializedVersion: 2
        x: 2647
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3707e6fbeca949640800000000000000
      internalID: 5085860091973759091
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_171
      rect:
        serializedVersion: 2
        x: 2654
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5623a37c7a3528900800000000000000
      internalID: 685202073380860517
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_172
      rect:
        serializedVersion: 2
        x: 2791
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d253902a273b92790800000000000000
      internalID: -7554309595011140307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_173
      rect:
        serializedVersion: 2
        x: 2798
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 21f8680025cb6ce40800000000000000
      internalID: 5676431440729247506
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_174
      rect:
        serializedVersion: 2
        x: 2935
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b758ad0e09c8002e0800000000000000
      internalID: -2161573267262241413
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_175
      rect:
        serializedVersion: 2
        x: 2942
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e79d024416aa5c560800000000000000
      internalID: 7333454902974273918
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_176
      rect:
        serializedVersion: 2
        x: 3079
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bf79d7109aa944760800000000000000
      internalID: 7441242534988060667
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_177
      rect:
        serializedVersion: 2
        x: 3086
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3639e86adf44d260800000000000000
      internalID: 7121404710261175869
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_178
      rect:
        serializedVersion: 2
        x: 3223
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ed484322e275e0c70800000000000000
      internalID: 8939178166030992606
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_179
      rect:
        serializedVersion: 2
        x: 3230
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6dc17f2866f4e31f0800000000000000
      internalID: -1063325160310367018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_180
      rect:
        serializedVersion: 2
        x: 3367
        y: 0
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d6fa1439cdad7c5e0800000000000000
      internalID: -1889300877760417939
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_181
      rect:
        serializedVersion: 2
        x: 3374
        y: 4
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6312c4ad123509290800000000000000
      internalID: -7885711542664289994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_182
      rect:
        serializedVersion: 2
        x: 3511
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0846f70a57d18c40800000000000000
      internalID: 5512924200558807050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_183
      rect:
        serializedVersion: 2
        x: 3529
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97d8d0fbade798930800000000000000
      internalID: 4145984409944558969
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_184
      rect:
        serializedVersion: 2
        x: 3655
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 541a64e406e3cd260800000000000000
      internalID: 7123637293944316229
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_185
      rect:
        serializedVersion: 2
        x: 3673
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98c83ca3563a9c4f0800000000000000
      internalID: -807935002963374967
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_186
      rect:
        serializedVersion: 2
        x: 3799
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3bba32dfc5774850800000000000000
      internalID: 6361182534129859391
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_187
      rect:
        serializedVersion: 2
        x: 3817
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16c28bf5572681870800000000000000
      internalID: 8653774940249009249
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_188
      rect:
        serializedVersion: 2
        x: 3943
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bec5c9f0e196d6810800000000000000
      internalID: 1760178607203572971
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_189
      rect:
        serializedVersion: 2
        x: 3961
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 19a7b2c4c539dee40800000000000000
      internalID: 5687363929062996625
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_190
      rect:
        serializedVersion: 2
        x: 4087
        y: 0
        width: 6
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f0400c38a7f3131f0800000000000000
      internalID: -1067001841281137649
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 3_191
      rect:
        serializedVersion: 2
        x: 4105
        y: 0
        width: 16
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ee3b8de3d60124ec0800000000000000
      internalID: -3584284292041624594
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Magic trap - Level 3_0: -7673469027116050723
      Magic trap - Level 3_1: 7015996904912957928
      Magic trap - Level 3_10: -4270093435352997573
      Magic trap - Level 3_100: -5692497268606192005
      Magic trap - Level 3_101: -8751192355803230421
      Magic trap - Level 3_102: 6770799220220849253
      Magic trap - Level 3_103: -8788956574880189435
      Magic trap - Level 3_104: 3248820956239175098
      Magic trap - Level 3_105: 3267069501976559884
      Magic trap - Level 3_106: 1762126896779694045
      Magic trap - Level 3_107: 3879904856665405549
      Magic trap - Level 3_108: -967203568126572930
      Magic trap - Level 3_109: -7565852425749024014
      Magic trap - Level 3_11: 6129367799656235600
      Magic trap - Level 3_110: -6284295503481689615
      Magic trap - Level 3_111: -6194528879049804393
      Magic trap - Level 3_112: -3531461635625452356
      Magic trap - Level 3_113: -3837921685523408862
      Magic trap - Level 3_114: 8040732359031383861
      Magic trap - Level 3_115: -4095187743709975958
      Magic trap - Level 3_116: -6873115082384116471
      Magic trap - Level 3_117: 2373335601822577204
      Magic trap - Level 3_118: -7292228953045205927
      Magic trap - Level 3_119: 6092569601948514722
      Magic trap - Level 3_12: 6889821259494526069
      Magic trap - Level 3_120: 6247679420995839709
      Magic trap - Level 3_121: 7016883003630003258
      Magic trap - Level 3_122: 4910742216798314417
      Magic trap - Level 3_123: 7109780948612412831
      Magic trap - Level 3_124: 8518157998347388020
      Magic trap - Level 3_125: 7170830130500233094
      Magic trap - Level 3_126: 2769271443541130167
      Magic trap - Level 3_127: 7429077160498915589
      Magic trap - Level 3_128: -6714674007978880748
      Magic trap - Level 3_129: -1294155311480474169
      Magic trap - Level 3_13: -6775695443384827485
      Magic trap - Level 3_130: 6166614956897985871
      Magic trap - Level 3_131: -265646185348100392
      Magic trap - Level 3_132: 6887303016774758875
      Magic trap - Level 3_133: -4215754102295676557
      Magic trap - Level 3_134: 1126521210636082643
      Magic trap - Level 3_135: -9191856504102831436
      Magic trap - Level 3_136: -5517156130958068561
      Magic trap - Level 3_137: 9112640301797283106
      Magic trap - Level 3_138: 6253497241416570356
      Magic trap - Level 3_139: 3671210597527326297
      Magic trap - Level 3_14: -3719124951047418941
      Magic trap - Level 3_140: 1424266888833864164
      Magic trap - Level 3_141: 696319115476260964
      Magic trap - Level 3_142: 4331964465871382759
      Magic trap - Level 3_143: -4686017450556912877
      Magic trap - Level 3_144: 1989342752128441535
      Magic trap - Level 3_145: -6420229881600381692
      Magic trap - Level 3_146: 8059588130812704288
      Magic trap - Level 3_147: 4604380128140815165
      Magic trap - Level 3_148: -1104539784735628564
      Magic trap - Level 3_149: 4364169091947295207
      Magic trap - Level 3_15: -1956811049354947342
      Magic trap - Level 3_150: 6063168337323254395
      Magic trap - Level 3_151: 6463227040523616923
      Magic trap - Level 3_152: 3380437046453836448
      Magic trap - Level 3_153: 6304873189092855059
      Magic trap - Level 3_154: 1562684153653678610
      Magic trap - Level 3_155: -6222739470432902828
      Magic trap - Level 3_156: -4670962164101261530
      Magic trap - Level 3_157: 7228356034707071925
      Magic trap - Level 3_158: -4000530435627396952
      Magic trap - Level 3_159: 7163941867061185346
      Magic trap - Level 3_16: -252793539081322554
      Magic trap - Level 3_160: 2764128787580779467
      Magic trap - Level 3_161: 4033988254350847626
      Magic trap - Level 3_162: -7938367564078876360
      Magic trap - Level 3_163: 5173598651236556616
      Magic trap - Level 3_164: -2871797479469928119
      Magic trap - Level 3_165: 7851796504092321837
      Magic trap - Level 3_166: 2292191684259881396
      Magic trap - Level 3_167: 7840358849891262318
      Magic trap - Level 3_168: 1293639107879249606
      Magic trap - Level 3_169: -1016842203759259732
      Magic trap - Level 3_17: 370434087686482828
      Magic trap - Level 3_170: 5085860091973759091
      Magic trap - Level 3_171: 685202073380860517
      Magic trap - Level 3_172: -7554309595011140307
      Magic trap - Level 3_173: 5676431440729247506
      Magic trap - Level 3_174: -2161573267262241413
      Magic trap - Level 3_175: 7333454902974273918
      Magic trap - Level 3_176: 7441242534988060667
      Magic trap - Level 3_177: 7121404710261175869
      Magic trap - Level 3_178: 8939178166030992606
      Magic trap - Level 3_179: -1063325160310367018
      Magic trap - Level 3_18: 1474157042715390078
      Magic trap - Level 3_180: -1889300877760417939
      Magic trap - Level 3_181: -7885711542664289994
      Magic trap - Level 3_182: 5512924200558807050
      Magic trap - Level 3_183: 4145984409944558969
      Magic trap - Level 3_184: 7123637293944316229
      Magic trap - Level 3_185: -807935002963374967
      Magic trap - Level 3_186: 6361182534129859391
      Magic trap - Level 3_187: 8653774940249009249
      Magic trap - Level 3_188: 1760178607203572971
      Magic trap - Level 3_189: 5687363929062996625
      Magic trap - Level 3_19: 1694632407468510378
      Magic trap - Level 3_190: -1067001841281137649
      Magic trap - Level 3_191: -3584284292041624594
      Magic trap - Level 3_2: 7346355279615851865
      Magic trap - Level 3_20: -4769291232115383954
      Magic trap - Level 3_21: -8735957480530241192
      Magic trap - Level 3_22: -5298069865605813757
      Magic trap - Level 3_23: -4081220790223430788
      Magic trap - Level 3_24: -1471481351050559491
      Magic trap - Level 3_25: 4657564061061293270
      Magic trap - Level 3_26: 7637707236050401115
      Magic trap - Level 3_27: 7781264304565536102
      Magic trap - Level 3_28: 2857819378394579791
      Magic trap - Level 3_29: 5022510502141800530
      Magic trap - Level 3_3: -5731245588497859563
      Magic trap - Level 3_30: -8498916671329491144
      Magic trap - Level 3_31: -8989396930255759137
      Magic trap - Level 3_32: -6277370729526379272
      Magic trap - Level 3_33: -2230262953856608908
      Magic trap - Level 3_34: -4028042801067173716
      Magic trap - Level 3_35: -6443785148783607803
      Magic trap - Level 3_36: 1845673699871674073
      Magic trap - Level 3_37: -3943027459571371891
      Magic trap - Level 3_38: -7875882964808873524
      Magic trap - Level 3_39: 6733340361808197297
      Magic trap - Level 3_4: -2620987837672017983
      Magic trap - Level 3_40: 5188345159540459972
      Magic trap - Level 3_41: 2626119665004003749
      Magic trap - Level 3_42: -6218772131606426307
      Magic trap - Level 3_43: -200434419484942015
      Magic trap - Level 3_44: 5767932459000048246
      Magic trap - Level 3_45: 881632078570475583
      Magic trap - Level 3_46: -1619135704965815594
      Magic trap - Level 3_47: -50495820123421596
      Magic trap - Level 3_48: -5701371123063724481
      Magic trap - Level 3_49: -8605290224821337990
      Magic trap - Level 3_5: 7747134453736894771
      Magic trap - Level 3_50: -2782204753029542712
      Magic trap - Level 3_51: 1386448183252899549
      Magic trap - Level 3_52: 6692528252669043068
      Magic trap - Level 3_53: -7194880775295831707
      Magic trap - Level 3_54: -2988459509700537387
      Magic trap - Level 3_55: 9064949882467641095
      Magic trap - Level 3_56: -3824488528749204337
      Magic trap - Level 3_57: -2966683562985342201
      Magic trap - Level 3_58: -622006087202811014
      Magic trap - Level 3_59: -7994596260570708115
      Magic trap - Level 3_6: -2578803016512539505
      Magic trap - Level 3_60: 8827525264457194052
      Magic trap - Level 3_61: 4704845026934134753
      Magic trap - Level 3_62: 6177583910039512634
      Magic trap - Level 3_63: 8081126429510982443
      Magic trap - Level 3_64: 706456228461422595
      Magic trap - Level 3_65: 3534761906067171382
      Magic trap - Level 3_66: 8034530316810772173
      Magic trap - Level 3_67: 8502726004636679036
      Magic trap - Level 3_68: -1295446450151765194
      Magic trap - Level 3_69: 3172365248502445653
      Magic trap - Level 3_7: -4517867895719826369
      Magic trap - Level 3_70: 8295523448721347699
      Magic trap - Level 3_71: -724673211083441697
      Magic trap - Level 3_72: -9173737022368472177
      Magic trap - Level 3_73: 5715688753167995841
      Magic trap - Level 3_74: 6346277891163637925
      Magic trap - Level 3_75: 3098578279126543119
      Magic trap - Level 3_76: -3462425588241267384
      Magic trap - Level 3_77: 7706916336665051704
      Magic trap - Level 3_78: -2970826056521020943
      Magic trap - Level 3_79: 5174812925900440699
      Magic trap - Level 3_8: 6109550009211128987
      Magic trap - Level 3_80: 4066316298453069076
      Magic trap - Level 3_81: 6366415196015795667
      Magic trap - Level 3_82: 1898107236317708083
      Magic trap - Level 3_83: 3647152405370922094
      Magic trap - Level 3_84: -8582849529062727706
      Magic trap - Level 3_85: -7743544687033167459
      Magic trap - Level 3_86: -7945444602709763904
      Magic trap - Level 3_87: -6617711317793639156
      Magic trap - Level 3_88: -4356165999799165955
      Magic trap - Level 3_89: 2170485182664677578
      Magic trap - Level 3_9: -1821149752995795521
      Magic trap - Level 3_90: 8869512524907129268
      Magic trap - Level 3_91: 8703166363056946219
      Magic trap - Level 3_92: 4243002902804757308
      Magic trap - Level 3_93: -5960585040381049788
      Magic trap - Level 3_94: -3447721178850656737
      Magic trap - Level 3_95: 4945335537940373740
      Magic trap - Level 3_96: -4068434278730788505
      Magic trap - Level 3_97: 8073760003785882799
      Magic trap - Level 3_98: 5325181343134058946
      Magic trap - Level 3_99: 701032658438549646
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
