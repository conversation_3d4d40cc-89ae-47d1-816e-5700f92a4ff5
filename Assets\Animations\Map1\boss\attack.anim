%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: attack
  serializedVersion: 7
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - serializedVersion: 2
    curve:
    - time: 0.033333335
      value: {fileID: -2982883362412430192, guid: 8d39ba1e893079545a3fce35e67c08b0, type: 3}
    - time: 0.06666667
      value: {fileID: -2699563537944854062, guid: 63c75b66f7cf1c14ab380f60de519c54, type: 3}
    - time: 0.083333336
      value: {fileID: -5744798761563532661, guid: 3c27c4f82ff6ee04e9122e967d1451b9, type: 3}
    - time: 0.11666667
      value: {fileID: -1851018196381985808, guid: 834bf44f4bc843648af39253e1c885f9, type: 3}
    - time: 0.16666667
      value: {fileID: 9086480038822547140, guid: b37e34ad34017364385f380938bc644a, type: 3}
    - time: 0.18333334
      value: {fileID: -6748459056130299976, guid: 757c737cb6ec08a42897c35c6b585892, type: 3}
    - time: 0.21666667
      value: {fileID: -8344408918930796531, guid: 537ae65f71a20f346b21af15887d1211, type: 3}
    - time: 0.25
      value: {fileID: -725315184728704951, guid: bbf8b00e07416ea4ba22a2634274c291, type: 3}
    - time: 0.26666668
      value: {fileID: 4042106224595548164, guid: 4f0e5f1b7364bbd43af7b706e11338e4, type: 3}
    - time: 0.3
      value: {fileID: -901419604204589762, guid: 13b3dea136d6ea44d83b8bcf5bfa8a34, type: 3}
    - time: 0.35
      value: {fileID: 7604716941377299701, guid: f994506e6bded384e8c36c91c8c34241, type: 3}
    - time: 0.36666667
      value: {fileID: -3103047370051857671, guid: efca0700e60ba6e4c89c39aa880d7159, type: 3}
    - time: 0.4
      value: {fileID: -4700641293172570793, guid: 08efcf2884e6c40498e9afe1c21407dd, type: 3}
    attribute: m_Sprite
    path: 
    classID: 212
    script: {fileID: 0}
    flags: 2
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
      isIntCurve: 0
      isSerializeReferenceCurve: 0
    pptrCurveMapping:
    - {fileID: -2982883362412430192, guid: 8d39ba1e893079545a3fce35e67c08b0, type: 3}
    - {fileID: -2699563537944854062, guid: 63c75b66f7cf1c14ab380f60de519c54, type: 3}
    - {fileID: -5744798761563532661, guid: 3c27c4f82ff6ee04e9122e967d1451b9, type: 3}
    - {fileID: -1851018196381985808, guid: 834bf44f4bc843648af39253e1c885f9, type: 3}
    - {fileID: 9086480038822547140, guid: b37e34ad34017364385f380938bc644a, type: 3}
    - {fileID: -6748459056130299976, guid: 757c737cb6ec08a42897c35c6b585892, type: 3}
    - {fileID: -8344408918930796531, guid: 537ae65f71a20f346b21af15887d1211, type: 3}
    - {fileID: -725315184728704951, guid: bbf8b00e07416ea4ba22a2634274c291, type: 3}
    - {fileID: 4042106224595548164, guid: 4f0e5f1b7364bbd43af7b706e11338e4, type: 3}
    - {fileID: -901419604204589762, guid: 13b3dea136d6ea44d83b8bcf5bfa8a34, type: 3}
    - {fileID: 7604716941377299701, guid: f994506e6bded384e8c36c91c8c34241, type: 3}
    - {fileID: -3103047370051857671, guid: efca0700e60ba6e4c89c39aa880d7159, type: 3}
    - {fileID: -4700641293172570793, guid: 08efcf2884e6c40498e9afe1c21407dd, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.4166667
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 0
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
