fileFormatVersion: 2
guid: c99bb1a33ce7c9c418de16365aca9f60
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -7321596740296343610
    second: main_lev_build_0
  - first:
      213: 2036342897494705274
    second: main_lev_build_1
  - first:
      213: 6690074168642156511
    second: main_lev_build_2
  - first:
      213: 5491440574836273819
    second: main_lev_build_3
  - first:
      213: -5725699659698489387
    second: main_lev_build_4
  - first:
      213: 983365781762884736
    second: main_lev_build_5
  - first:
      213: 6610379306505157424
    second: main_lev_build_6
  - first:
      213: -3638452437159074076
    second: main_lev_build_7
  - first:
      213: 5620601010649040209
    second: main_lev_build_8
  - first:
      213: 196594637770524257
    second: main_lev_build_9
  - first:
      213: -1768509884834896043
    second: main_lev_build_10
  - first:
      213: -3536153544004010262
    second: main_lev_build_11
  - first:
      213: -5078194952174540175
    second: main_lev_build_12
  - first:
      213: -7553685545647535464
    second: main_lev_build_13
  - first:
      213: 6195799413063252396
    second: main_lev_build_14
  - first:
      213: 7158266655426338334
    second: main_lev_build_15
  - first:
      213: -5141582850063230037
    second: main_lev_build_16
  - first:
      213: -6145452952202039930
    second: main_lev_build_17
  - first:
      213: -3545220061732740010
    second: main_lev_build_18
  - first:
      213: -4363625974722005862
    second: main_lev_build_19
  - first:
      213: 1311053599123588642
    second: main_lev_build_20
  - first:
      213: -7391597574769789040
    second: main_lev_build_21
  - first:
      213: 4061335903751664338
    second: main_lev_build_22
  - first:
      213: -8727740464184286612
    second: main_lev_build_23
  - first:
      213: -5928373181076509889
    second: main_lev_build_24
  - first:
      213: -3600304986691838317
    second: main_lev_build_25
  - first:
      213: -5807119682142418728
    second: main_lev_build_26
  - first:
      213: -769258450561268069
    second: main_lev_build_27
  - first:
      213: 8804179717975675962
    second: main_lev_build_28
  - first:
      213: -7556422623461489005
    second: main_lev_build_29
  - first:
      213: -3179065492051820323
    second: main_lev_build_30
  - first:
      213: 6367844002441579164
    second: main_lev_build_31
  - first:
      213: 530697048666210728
    second: main_lev_build_32
  - first:
      213: 4395045069217402341
    second: main_lev_build_33
  - first:
      213: 2092627775496249973
    second: main_lev_build_34
  - first:
      213: -2376033672055137745
    second: main_lev_build_35
  - first:
      213: -1375255701310948154
    second: main_lev_build_36
  - first:
      213: -485270578018098674
    second: main_lev_build_37
  - first:
      213: 3633828979255977272
    second: main_lev_build_38
  - first:
      213: 6434411865305387303
    second: main_lev_build_39
  - first:
      213: -4289367072409244655
    second: main_lev_build_40
  - first:
      213: 997770449508396777
    second: main_lev_build_41
  - first:
      213: 1640190099747058255
    second: main_lev_build_42
  - first:
      213: 8123933813599918988
    second: main_lev_build_43
  - first:
      213: -3347604227171386211
    second: main_lev_build_44
  - first:
      213: -57643045793180287
    second: main_lev_build_45
  - first:
      213: 1037133018308231650
    second: main_lev_build_46
  - first:
      213: 2385191094207926952
    second: main_lev_build_47
  - first:
      213: -7460362894449188874
    second: main_lev_build_48
  - first:
      213: 9004167804233604409
    second: main_lev_build_49
  - first:
      213: -7841815696645775610
    second: main_lev_build_50
  - first:
      213: -5227130058253163313
    second: main_lev_build_51
  - first:
      213: -2254558842325139031
    second: main_lev_build_52
  - first:
      213: 6552942105971872028
    second: main_lev_build_53
  - first:
      213: 3476739193793682195
    second: main_lev_build_54
  - first:
      213: -4964059160939747986
    second: main_lev_build_55
  - first:
      213: 2733483858526708977
    second: main_lev_build_56
  - first:
      213: 4554884612160548212
    second: main_lev_build_57
  - first:
      213: -7699477089096638638
    second: main_lev_build_58
  - first:
      213: -7386319898669079621
    second: main_lev_build_59
  - first:
      213: 8630728240354937356
    second: main_lev_build_60
  - first:
      213: 7265131014059844303
    second: main_lev_build_61
  - first:
      213: 425690847245178098
    second: main_lev_build_62
  - first:
      213: -4354054293520004780
    second: main_lev_build_63
  - first:
      213: 415934858433069832
    second: main_lev_build_64
  - first:
      213: 3458790219721625088
    second: main_lev_build_65
  - first:
      213: -2547002701821570693
    second: main_lev_build_66
  - first:
      213: -6710359665389112560
    second: main_lev_build_67
  - first:
      213: 7914680821397812226
    second: main_lev_build_68
  - first:
      213: -2815293676601201418
    second: main_lev_build_69
  - first:
      213: 8014137359537955170
    second: main_lev_build_70
  - first:
      213: 1998666568486746414
    second: main_lev_build_71
  - first:
      213: 1008047188828136873
    second: main_lev_build_72
  - first:
      213: 2975915751906570460
    second: main_lev_build_73
  - first:
      213: -38210694034918336
    second: main_lev_build_74
  - first:
      213: -4708163543103279049
    second: main_lev_build_75
  - first:
      213: 2077038968476330148
    second: main_lev_build_76
  - first:
      213: -3715384358238098460
    second: main_lev_build_77
  - first:
      213: -2389742835691918131
    second: main_lev_build_78
  - first:
      213: -417794507198324013
    second: main_lev_build_79
  - first:
      213: 2277096919056543555
    second: main_lev_build_80
  - first:
      213: -8135579845998952261
    second: main_lev_build_81
  - first:
      213: 3397236183029400507
    second: main_lev_build_82
  - first:
      213: -1641970265319708320
    second: main_lev_build_83
  - first:
      213: -8641513879433373621
    second: main_lev_build_84
  - first:
      213: 3857487717421996754
    second: main_lev_build_85
  - first:
      213: 7613901830944957695
    second: main_lev_build_86
  - first:
      213: -4401267317277905845
    second: main_lev_build_87
  - first:
      213: 4542144513190088667
    second: main_lev_build_88
  - first:
      213: -1876134194446544935
    second: main_lev_build_89
  - first:
      213: 567746709657321588
    second: main_lev_build_90
  - first:
      213: 2657539554596082145
    second: main_lev_build_91
  - first:
      213: -7945661129260217745
    second: main_lev_build_92
  - first:
      213: -6924404252945211523
    second: main_lev_build_93
  - first:
      213: 6734544703757080922
    second: main_lev_build_94
  - first:
      213: -5132723718999068900
    second: main_lev_build_95
  - first:
      213: -3759255416618297252
    second: main_lev_build_96
  - first:
      213: -7154212806900720164
    second: main_lev_build_97
  - first:
      213: -5007958133994179420
    second: main_lev_build_98
  - first:
      213: 2877082738769336614
    second: main_lev_build_99
  - first:
      213: 621298860972965650
    second: main_lev_build_100
  - first:
      213: 193150957163764894
    second: main_lev_build_101
  - first:
      213: 6524111832456121975
    second: main_lev_build_102
  - first:
      213: -2660265737741929098
    second: main_lev_build_103
  - first:
      213: -8021619760953474160
    second: main_lev_build_104
  - first:
      213: -5837570162875244700
    second: main_lev_build_105
  - first:
      213: -44995796484101441
    second: main_lev_build_106
  - first:
      213: -1062130375744474319
    second: main_lev_build_107
  - first:
      213: -622970485326188544
    second: main_lev_build_108
  - first:
      213: -8362269351111413254
    second: main_lev_build_109
  - first:
      213: 9069556223356447325
    second: main_lev_build_110
  - first:
      213: 4233993977004366485
    second: main_lev_build_111
  - first:
      213: 2276484700516229305
    second: main_lev_build_112
  - first:
      213: -5210863717491564103
    second: main_lev_build_113
  - first:
      213: 2070571115308212702
    second: main_lev_build_114
  - first:
      213: -8610950664914644571
    second: main_lev_build_115
  - first:
      213: -4960856750088168621
    second: main_lev_build_116
  - first:
      213: 8199563988000738364
    second: main_lev_build_117
  - first:
      213: -2206904888097387290
    second: main_lev_build_118
  - first:
      213: 151522025708051767
    second: main_lev_build_119
  - first:
      213: -803912396741906382
    second: main_lev_build_120
  - first:
      213: -746997140243067684
    second: main_lev_build_121
  - first:
      213: -2717125245174493432
    second: main_lev_build_122
  - first:
      213: -5028564513039268329
    second: main_lev_build_123
  - first:
      213: -5745291454783856453
    second: main_lev_build_124
  - first:
      213: -4973437095308503593
    second: main_lev_build_125
  - first:
      213: -137578337358671734
    second: main_lev_build_126
  - first:
      213: -6349806415376227467
    second: main_lev_build_127
  - first:
      213: 1567748191973282318
    second: main_lev_build_128
  - first:
      213: -4809865843447429893
    second: main_lev_build_129
  - first:
      213: 1883204427629340399
    second: main_lev_build_130
  - first:
      213: 3652705750433374453
    second: main_lev_build_131
  - first:
      213: 4506731305153409238
    second: main_lev_build_132
  - first:
      213: 8138469426065980930
    second: main_lev_build_133
  - first:
      213: 5538656881412035547
    second: main_lev_build_134
  - first:
      213: -7205058503226540146
    second: main_lev_build_135
  - first:
      213: 2077223764941323018
    second: main_lev_build_136
  - first:
      213: 5310899633670182907
    second: main_lev_build_137
  - first:
      213: -3617914648690902344
    second: main_lev_build_138
  - first:
      213: 7653767872406317246
    second: main_lev_build_139
  - first:
      213: -8706730559259112915
    second: main_lev_build_140
  - first:
      213: 914101506007890269
    second: main_lev_build_141
  - first:
      213: -2990363170449962759
    second: main_lev_build_142
  - first:
      213: 2820104096465587458
    second: main_lev_build_143
  - first:
      213: -3656541953600747061
    second: main_lev_build_144
  - first:
      213: 3685598938932587791
    second: main_lev_build_145
  - first:
      213: 6404976995850105157
    second: main_lev_build_146
  - first:
      213: 1775907248318098425
    second: main_lev_build_147
  - first:
      213: -85771850945150337
    second: main_lev_build_148
  - first:
      213: -2848409702277105412
    second: main_lev_build_149
  - first:
      213: -8606718571102087664
    second: main_lev_build_150
  - first:
      213: 1459307770886656934
    second: main_lev_build_151
  - first:
      213: 8280804147713585263
    second: main_lev_build_152
  - first:
      213: 6659875845012194595
    second: main_lev_build_153
  - first:
      213: -8798629170826030308
    second: main_lev_build_154
  - first:
      213: 4018352706525230109
    second: main_lev_build_155
  - first:
      213: -6478097100844814417
    second: main_lev_build_156
  - first:
      213: 3021414291474572455
    second: main_lev_build_157
  - first:
      213: -3931259207788963719
    second: main_lev_build_158
  - first:
      213: 8397031581954423213
    second: main_lev_build_159
  - first:
      213: -2030788552439912968
    second: main_lev_build_160
  - first:
      213: 5173426924836933001
    second: main_lev_build_161
  - first:
      213: 5768514499765748328
    second: main_lev_build_162
  - first:
      213: 7403732665445563138
    second: main_lev_build_163
  - first:
      213: 6203363360349413242
    second: main_lev_build_164
  - first:
      213: -5186957502616284361
    second: main_lev_build_165
  - first:
      213: 6493089649917051727
    second: main_lev_build_166
  - first:
      213: -4178230385048556639
    second: main_lev_build_167
  - first:
      213: -4505704549085117572
    second: main_lev_build_168
  - first:
      213: 6671728359232285413
    second: main_lev_build_169
  - first:
      213: 8992988136671466827
    second: main_lev_build_170
  - first:
      213: 4334709636670138438
    second: main_lev_build_171
  - first:
      213: -8537465212095923645
    second: main_lev_build_172
  - first:
      213: 2007698340716412346
    second: main_lev_build_173
  - first:
      213: 3517821118963878152
    second: main_lev_build_174
  - first:
      213: 679591032650909225
    second: main_lev_build_175
  - first:
      213: -1338473756145795794
    second: main_lev_build_176
  - first:
      213: 3249143235801612437
    second: main_lev_build_177
  - first:
      213: -5929037604892486029
    second: main_lev_build_178
  - first:
      213: -6119435950934704069
    second: main_lev_build_179
  - first:
      213: -56535418712522192
    second: main_lev_build_180
  - first:
      213: 8673472826528810150
    second: main_lev_build_181
  - first:
      213: -2454689013905749240
    second: main_lev_build_182
  - first:
      213: 6609580008924592949
    second: main_lev_build_183
  - first:
      213: 4322834419882472075
    second: main_lev_build_184
  - first:
      213: 7787843610557489562
    second: main_lev_build_185
  - first:
      213: 835815792770037724
    second: main_lev_build_186
  - first:
      213: 4297576865304843797
    second: main_lev_build_187
  - first:
      213: -4730080549538457322
    second: main_lev_build_188
  - first:
      213: 6812129714742556852
    second: main_lev_build_189
  - first:
      213: -1790161686602783691
    second: main_lev_build_190
  - first:
      213: 1551073831299704365
    second: main_lev_build_191
  - first:
      213: 4409297632960698333
    second: main_lev_build_192
  - first:
      213: -8921089400312680607
    second: main_lev_build_193
  - first:
      213: -2922252033548441204
    second: main_lev_build_194
  - first:
      213: -5613948687429347574
    second: main_lev_build_195
  - first:
      213: 1647603177372701032
    second: main_lev_build_196
  - first:
      213: -472264205905250565
    second: main_lev_build_197
  - first:
      213: 2440129190174126388
    second: main_lev_build_198
  - first:
      213: -5753392255462573284
    second: main_lev_build_199
  - first:
      213: 737117795878514275
    second: main_lev_build_200
  - first:
      213: 824798980326647433
    second: main_lev_build_201
  - first:
      213: -244147896970422931
    second: main_lev_build_202
  - first:
      213: -5329197585624229906
    second: main_lev_build_203
  - first:
      213: -6504040958126615827
    second: main_lev_build_204
  - first:
      213: 2967152432066799913
    second: main_lev_build_205
  - first:
      213: 7143798672687524453
    second: main_lev_build_206
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: main_lev_build_0
      rect:
        serializedVersion: 2
        x: 32
        y: 1169
        width: 96
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6cf7af16e86746a90800000000000000
      internalID: -7321596740296343610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_1
      rect:
        serializedVersion: 2
        x: 144
        y: 1168
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a74cf06831b824c10800000000000000
      internalID: 2036342897494705274
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_2
      rect:
        serializedVersion: 2
        x: 224
        y: 1200
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd7bee6c20be7dc50800000000000000
      internalID: 6690074168642156511
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_3
      rect:
        serializedVersion: 2
        x: 464
        y: 1168
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.75}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b927840ab14853c40800000000000000
      internalID: 5491440574836273819
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_4
      rect:
        serializedVersion: 2
        x: 543
        y: 1168
        width: 96
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5d7b81dde7a3a80b0800000000000000
      internalID: -5725699659698489387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_5
      rect:
        serializedVersion: 2
        x: 672
        y: 1200
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 084404ae7fd95ad00800000000000000
      internalID: 983365781762884736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_6
      rect:
        serializedVersion: 2
        x: 704
        y: 1200
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 03b35dab4f8ccbb50800000000000000
      internalID: 6610379306505157424
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_7
      rect:
        serializedVersion: 2
        x: 752
        y: 1200
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4e6aaf629ce918dc0800000000000000
      internalID: -3638452437159074076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_8
      rect:
        serializedVersion: 2
        x: 800
        y: 1200
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 15500f707d2600e40800000000000000
      internalID: 5620601010649040209
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_9
      rect:
        serializedVersion: 2
        x: 848
        y: 1200
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1660f0d08c17ab200800000000000000
      internalID: 196594637770524257
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_10
      rect:
        serializedVersion: 2
        x: 896
        y: 1200
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 55726a431adf477e0800000000000000
      internalID: -1768509884834896043
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_11
      rect:
        serializedVersion: 2
        x: 944
        y: 1200
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aeef66a461f0deec0800000000000000
      internalID: -3536153544004010262
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_12
      rect:
        serializedVersion: 2
        x: 992
        y: 1200
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17a647fa890a689b0800000000000000
      internalID: -5078194952174540175
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_13
      rect:
        serializedVersion: 2
        x: 1088
        y: 1216
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 89a0da2740beb2790800000000000000
      internalID: -7553685545647535464
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_14
      rect:
        serializedVersion: 2
        x: 1152
        y: 1216
        width: 128
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ca9753b4db6ebf550800000000000000
      internalID: 6195799413063252396
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_15
      rect:
        serializedVersion: 2
        x: 1384
        y: 1188
        width: 96
        height: 64
      alignment: 9
      pivot: {x: 0.75, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e1ed534d895475360800000000000000
      internalID: 7158266655426338334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_16
      rect:
        serializedVersion: 2
        x: 1856
        y: 1200
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bab9dbe93ad65a8b0800000000000000
      internalID: -5141582850063230037
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_17
      rect:
        serializedVersion: 2
        x: 1936
        y: 1200
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 68d2f189917f6baa0800000000000000
      internalID: -6145452952202039930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_18
      rect:
        serializedVersion: 2
        x: 160
        y: 1120
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 654e4d12329dccec0800000000000000
      internalID: -3545220061732740010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_19
      rect:
        serializedVersion: 2
        x: 192
        y: 1136
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a94f604ab494173c0800000000000000
      internalID: -4363625974722005862
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_20
      rect:
        serializedVersion: 2
        x: 224
        y: 1152
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 22642495d4cc13210800000000000000
      internalID: 1311053599123588642
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_21
      rect:
        serializedVersion: 2
        x: 464
        y: 1136
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 09735390b25cb6990800000000000000
      internalID: -7391597574769789040
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_22
      rect:
        serializedVersion: 2
        x: 496
        y: 1120
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2d278a09c73cc5830800000000000000
      internalID: 4061335903751664338
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_23
      rect:
        serializedVersion: 2
        x: 672
        y: 1040
        width: 16
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c622e741a46d0e680800000000000000
      internalID: -8727740464184286612
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_24
      rect:
        serializedVersion: 2
        x: 704
        y: 1152
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3b6bfd60003abda0800000000000000
      internalID: -5928373181076509889
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_25
      rect:
        serializedVersion: 2
        x: 752
        y: 1136
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 39e5a2990b5290ec0800000000000000
      internalID: -3600304986691838317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_26
      rect:
        serializedVersion: 2
        x: 816
        y: 1008
        width: 32
        height: 160
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8d8c8b1aa67f86fa0800000000000000
      internalID: -5807119682142418728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_27
      rect:
        serializedVersion: 2
        x: 896
        y: 1136
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b9604d1d38b0355f0800000000000000
      internalID: -769258450561268069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_28
      rect:
        serializedVersion: 2
        x: 944
        y: 1152
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3493a49ecabe2a70800000000000000
      internalID: 8804179717975675962
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_29
      rect:
        serializedVersion: 2
        x: 992
        y: 1040
        width: 16
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3964adbd8a1322790800000000000000
      internalID: -7556422623461489005
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_30
      rect:
        serializedVersion: 2
        x: 1096
        y: 1129
        width: 32
        height: 64
      alignment: 9
      pivot: {x: 0.5, y: 0.75}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dd08c8447c0b1e3d0800000000000000
      internalID: -3179065492051820323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_31
      rect:
        serializedVersion: 2
        x: 1160
        y: 1097
        width: 128
        height: 96
      alignment: 9
      pivot: {x: 0.62, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c9ac1a6c1602f5850800000000000000
      internalID: 6367844002441579164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_32
      rect:
        serializedVersion: 2
        x: 1312
        y: 1168
        width: 64
        height: 32
      alignment: 9
      pivot: {x: 0.75, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8a1091d672a6d5700800000000000000
      internalID: 530697048666210728
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_33
      rect:
        serializedVersion: 2
        x: 1392
        y: 1056
        width: 16
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ed098a54365efc30800000000000000
      internalID: 4395045069217402341
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_34
      rect:
        serializedVersion: 2
        x: 1424
        y: 1152
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 572e195efd18a0d10800000000000000
      internalID: 2092627775496249973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_35
      rect:
        serializedVersion: 2
        x: 1472
        y: 1120
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 0.75, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f2a59cac6f1a60fd0800000000000000
      internalID: -2376033672055137745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_36
      rect:
        serializedVersion: 2
        x: 1552
        y: 1110
        width: 32
        height: 64
      alignment: 4
      pivot: {x: 0, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ccb700e93c1aece0800000000000000
      internalID: -1375255701310948154
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_37
      rect:
        serializedVersion: 2
        x: 1600
        y: 1120
        width: 16
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e026849faf8f349f0800000000000000
      internalID: -485270578018098674
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_38
      rect:
        serializedVersion: 2
        x: 1824
        y: 1134
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 835a21b2434fd6230800000000000000
      internalID: 3633828979255977272
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_39
      rect:
        serializedVersion: 2
        x: 1872
        y: 1136
        width: 96
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7211612208f9b4950800000000000000
      internalID: 6434411865305387303
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_40
      rect:
        serializedVersion: 2
        x: 1984
        y: 1135
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 11c2ac9af5b1974c0800000000000000
      internalID: -4289367072409244655
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_41
      rect:
        serializedVersion: 2
        x: 32
        y: 1104
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9ee645affeac8dd00800000000000000
      internalID: 997770449508396777
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_42
      rect:
        serializedVersion: 2
        x: 144
        y: 976
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f423983a43023c610800000000000000
      internalID: 1640190099747058255
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_43
      rect:
        serializedVersion: 2
        x: 496
        y: 976
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c8b5862d2c20eb070800000000000000
      internalID: 8123933813599918988
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_44
      rect:
        serializedVersion: 2
        x: 1320
        y: 1104
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d9c7ad611bbea81d0800000000000000
      internalID: -3347604227171386211
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_45
      rect:
        serializedVersion: 2
        x: 1423
        y: 1055
        width: 34
        height: 82
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 185d9bb84f5333ff0800000000000000
      internalID: -57643045793180287
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_46
      rect:
        serializedVersion: 2
        x: 1472
        y: 1088
        width: 64
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e147153cf2a46e00800000000000000
      internalID: 1037133018308231650
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_47
      rect:
        serializedVersion: 2
        x: 1872
        y: 976
        width: 160
        height: 128
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8a250b3b9a6e91120800000000000000
      internalID: 2385191094207926952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_48
      rect:
        serializedVersion: 2
        x: 192
        y: 1056
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ffd9d46977777890800000000000000
      internalID: -7460362894449188874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_49
      rect:
        serializedVersion: 2
        x: 256
        y: 1072
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 93d5a5d69ea35fc70800000000000000
      internalID: 9004167804233604409
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_50
      rect:
        serializedVersion: 2
        x: 304
        y: 1072
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 60f985feb264c2390800000000000000
      internalID: -7841815696645775610
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_51
      rect:
        serializedVersion: 2
        x: 320
        y: 1056
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fc4ad0318e08577b0800000000000000
      internalID: -5227130058253163313
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_52
      rect:
        serializedVersion: 2
        x: 1151
        y: 1055
        width: 114
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9a18ef59da236b0e0800000000000000
      internalID: -2254558842325139031
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_53
      rect:
        serializedVersion: 2
        x: 1472
        y: 1056
        width: 64
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c11572c502ab0fa50800000000000000
      internalID: 6552942105971872028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_54
      rect:
        serializedVersion: 2
        x: 1584
        y: 1024
        width: 16
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 31b10ce40ebdf3030800000000000000
      internalID: 3476739193793682195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_55
      rect:
        serializedVersion: 2
        x: 79
        y: 1007
        width: 50
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e65a92c6e7e1c1bb0800000000000000
      internalID: -4964059160939747986
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_56
      rect:
        serializedVersion: 2
        x: 544
        y: 1008
        width: 48
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1f4afb236194fe520800000000000000
      internalID: 2733483858526708977
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_57
      rect:
        serializedVersion: 2
        x: 1088
        y: 913
        width: 160
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 47de8b40c63363f30800000000000000
      internalID: 4554884612160548212
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_58
      rect:
        serializedVersion: 2
        x: 1423
        y: 1007
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 25fd22c8166f52590800000000000000
      internalID: -7699477089096638638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_59
      rect:
        serializedVersion: 2
        x: 1472
        y: 1024
        width: 96
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb32e62cf258e7990800000000000000
      internalID: -7386319898669079621
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_60
      rect:
        serializedVersion: 2
        x: 48
        y: 928
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c0ac9476b9186c770800000000000000
      internalID: 8630728240354937356
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_61
      rect:
        serializedVersion: 2
        x: 192
        y: 976
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fc2e5bb3a2ee2d460800000000000000
      internalID: 7265131014059844303
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_62
      rect:
        serializedVersion: 2
        x: 400
        y: 976
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f41f26f19b58e500800000000000000
      internalID: 425690847245178098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_63
      rect:
        serializedVersion: 2
        x: 448
        y: 981
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 459c9f550ba4393c0800000000000000
      internalID: -4354054293520004780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_64
      rect:
        serializedVersion: 2
        x: 592
        y: 927
        width: 32
        height: 64
      alignment: 9
      pivot: {x: 0.51877594, y: 0.50000095}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8030b9ecc82b5c500800000000000000
      internalID: 415934858433069832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_65
      rect:
        serializedVersion: 2
        x: 96
        y: 928
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 00e4a1f1167100030800000000000000
      internalID: 3458790219721625088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_66
      rect:
        serializedVersion: 2
        x: 144
        y: 928
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b7117fb898a37acd0800000000000000
      internalID: -2547002701821570693
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_67
      rect:
        serializedVersion: 2
        x: 192
        y: 928
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0137f55ff6300e2a0800000000000000
      internalID: -6710359665389112560
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_68
      rect:
        serializedVersion: 2
        x: 224
        y: 928
        width: 224
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2047306f54896dd60800000000000000
      internalID: 7914680821397812226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_69
      rect:
        serializedVersion: 2
        x: 463
        y: 927
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6fc28c331511ee8d0800000000000000
      internalID: -2815293676601201418
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_70
      rect:
        serializedVersion: 2
        x: 496
        y: 928
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2616270c77fe73f60800000000000000
      internalID: 8014137359537955170
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_71
      rect:
        serializedVersion: 2
        x: 544
        y: 927
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e29f91797a0bcbb10800000000000000
      internalID: 1998666568486746414
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_72
      rect:
        serializedVersion: 2
        x: 48
        y: 768
        width: 96
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9a5aeeec39d4dfd00800000000000000
      internalID: 1008047188828136873
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_73
      rect:
        serializedVersion: 2
        x: 160
        y: 768
        width: 64
        height: 64
      alignment: 9
      pivot: {x: 1, y: 1}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd071e9fb939c4920800000000000000
      internalID: 2975915751906570460
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_74
      rect:
        serializedVersion: 2
        x: 240
        y: 800
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0447d63839f387ff0800000000000000
      internalID: -38210694034918336
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_75
      rect:
        serializedVersion: 2
        x: 479
        y: 767
        width: 66
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 73072e4d33e39aeb0800000000000000
      internalID: -4708163543103279049
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_76
      rect:
        serializedVersion: 2
        x: 559
        y: 767
        width: 98
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a8ecacefef13dc10800000000000000
      internalID: 2077038968476330148
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_77
      rect:
        serializedVersion: 2
        x: 687
        y: 815
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4ebec237c9d407cc0800000000000000
      internalID: -3715384358238098460
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_78
      rect:
        serializedVersion: 2
        x: 719
        y: 815
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dcc6c066d8de5ded0800000000000000
      internalID: -2389742835691918131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_79
      rect:
        serializedVersion: 2
        x: 767
        y: 815
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3d2a559fb12b33af0800000000000000
      internalID: -417794507198324013
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_80
      rect:
        serializedVersion: 2
        x: 815
        y: 815
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34f6083559fd99f10800000000000000
      internalID: 2277096919056543555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_81
      rect:
        serializedVersion: 2
        x: 863
        y: 815
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb0c765fb3d981f80800000000000000
      internalID: -8135579845998952261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_82
      rect:
        serializedVersion: 2
        x: 911
        y: 815
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb342932f48652f20800000000000000
      internalID: 3397236183029400507
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_83
      rect:
        serializedVersion: 2
        x: 960
        y: 816
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape:
      - - {x: 16, y: 8}
        - {x: -16, y: 8}
        - {x: -16, y: -8}
        - {x: 16, y: -8}
      tessellationDetail: 0
      bones: []
      spriteID: 0696dd34ebc8639e0800000000000000
      internalID: -1641970265319708320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_84
      rect:
        serializedVersion: 2
        x: 1007
        y: 815
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b4814ce49ec231880800000000000000
      internalID: -8641513879433373621
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_85
      rect:
        serializedVersion: 2
        x: 1088
        y: 832
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2dade5224ac888530800000000000000
      internalID: 3857487717421996754
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_86
      rect:
        serializedVersion: 2
        x: 1152
        y: 832
        width: 96
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ff4ab2400630aa960800000000000000
      internalID: 7613901830944957695
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_87
      rect:
        serializedVersion: 2
        x: 240
        y: 752
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b4c2d10a1be8be2c0800000000000000
      internalID: -4401267317277905845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_88
      rect:
        serializedVersion: 2
        x: 688
        y: 656
        width: 16
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bdf4f239e50f80f30800000000000000
      internalID: 4542144513190088667
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_89
      rect:
        serializedVersion: 2
        x: 719
        y: 767
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9dfcc0de3e1a6f5e0800000000000000
      internalID: -1876134194446544935
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_90
      rect:
        serializedVersion: 2
        x: 767
        y: 751
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4708587df9a01e700800000000000000
      internalID: 567746709657321588
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_91
      rect:
        serializedVersion: 2
        x: 832
        y: 624
        width: 32
        height: 160
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1e1df38052a71e420800000000000000
      internalID: 2657539554596082145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_92
      rect:
        serializedVersion: 2
        x: 911
        y: 751
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f6ac0d40d475bb190800000000000000
      internalID: -7945661129260217745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_93
      rect:
        serializedVersion: 2
        x: 959
        y: 767
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d7f1ffdd40397ef90800000000000000
      internalID: -6924404252945211523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_94
      rect:
        serializedVersion: 2
        x: 1008
        y: 656
        width: 16
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a59dd671ab8e57d50800000000000000
      internalID: 6734544703757080922
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_95
      rect:
        serializedVersion: 2
        x: 1088
        y: 720
        width: 48
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1fa6dec8f6e4c8b0800000000000000
      internalID: -5132723718999068900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_96
      rect:
        serializedVersion: 2
        x: 1159
        y: 712
        width: 128
        height: 96
      alignment: 9
      pivot: {x: 0.62, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c58d080bd1174dbc0800000000000000
      internalID: -3759255416618297252
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_97
      rect:
        serializedVersion: 2
        x: 1312
        y: 784
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cddd1255b5127bc90800000000000000
      internalID: -7154212806900720164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_98
      rect:
        serializedVersion: 2
        x: 1455
        y: 783
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4a4bf354c98208ab0800000000000000
      internalID: -5007958133994179420
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_99
      rect:
        serializedVersion: 2
        x: 1520
        y: 784
        width: 16
        height: 21
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 62911c9e1837de720800000000000000
      internalID: 2877082738769336614
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_100
      rect:
        serializedVersion: 2
        x: 1552
        y: 784
        width: 16
        height: 21
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 21ff5a3b50c4f9800800000000000000
      internalID: 621298860972965650
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_101
      rect:
        serializedVersion: 2
        x: 1584
        y: 768
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e9cd5cda5c53ea200800000000000000
      internalID: 193150957163764894
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_102
      rect:
        serializedVersion: 2
        x: 48
        y: 704
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 77e4878352d4a8a50800000000000000
      internalID: 6524111832456121975
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_103
      rect:
        serializedVersion: 2
        x: 176
        y: 720
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 675ffbdf766d41bd0800000000000000
      internalID: -2660265737741929098
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_104
      rect:
        serializedVersion: 2
        x: 208
        y: 736
        width: 16
        height: 16
      alignment: 9
      pivot: {x: 0.5, y: 0.5554161}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0934d19e35b7da090800000000000000
      internalID: -8021619760953474160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_105
      rect:
        serializedVersion: 2
        x: 479
        y: 735
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 46fd66a9dd8ccfea0800000000000000
      internalID: -5837570162875244700
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_106
      rect:
        serializedVersion: 2
        x: 511
        y: 719
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fbaed2ebf84206ff0800000000000000
      internalID: -44995796484101441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_107
      rect:
        serializedVersion: 2
        x: 1320
        y: 720
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1333998fc0e8241f0800000000000000
      internalID: -1062130375744474319
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_108
      rect:
        serializedVersion: 2
        x: 1392
        y: 640
        width: 16
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 000749ce4a3ca57f0800000000000000
      internalID: -622970485326188544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_109
      rect:
        serializedVersion: 2
        x: 1424
        y: 735
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: af13ed2945043fb80800000000000000
      internalID: -8362269351111413254
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_110
      rect:
        serializedVersion: 2
        x: 1552
        y: 704
        width: 32
        height: 41
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d5a289325598ddd70800000000000000
      internalID: 9069556223356447325
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_111
      rect:
        serializedVersion: 2
        x: 1600
        y: 704
        width: 16
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 59a1d74e61b22ca30800000000000000
      internalID: 4233993977004366485
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_112
      rect:
        serializedVersion: 2
        x: 160
        y: 576
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9b0a1e616c2b79f10800000000000000
      internalID: 2276484700516229305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_113
      rect:
        serializedVersion: 2
        x: 208
        y: 656
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9b1a1f9ae0b4fa7b0800000000000000
      internalID: -5210863717491564103
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_114
      rect:
        serializedVersion: 2
        x: 272
        y: 672
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ed1612285752cbc10800000000000000
      internalID: 2070571115308212702
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_115
      rect:
        serializedVersion: 2
        x: 320
        y: 672
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5a159573ef1cf7880800000000000000
      internalID: -8610950664914644571
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_116
      rect:
        serializedVersion: 2
        x: 336
        y: 656
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 35b556dc11f772bb0800000000000000
      internalID: -4960856750088168621
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_117
      rect:
        serializedVersion: 2
        x: 512
        y: 576
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c3c8ed10104bac170800000000000000
      internalID: 8199563988000738364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_118
      rect:
        serializedVersion: 2
        x: 1152
        y: 672
        width: 96
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ecc1eb62bf7f51e0800000000000000
      internalID: -2206904888097387290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_119
      rect:
        serializedVersion: 2
        x: 1321
        y: 616
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 73dd9f5ca705a1200800000000000000
      internalID: 151522025708051767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_120
      rect:
        serializedVersion: 2
        x: 1424
        y: 641
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2307aa93fede7d4f0800000000000000
      internalID: -803912396741906382
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_121
      rect:
        serializedVersion: 2
        x: 1472
        y: 672
        width: 64
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cdc44970f0222a5f0800000000000000
      internalID: -746997140243067684
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_122
      rect:
        serializedVersion: 2
        x: 1584
        y: 608
        width: 16
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80fc0274cf4da4ad0800000000000000
      internalID: -2717125245174493432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_123
      rect:
        serializedVersion: 2
        x: 96
        y: 608
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 716c826c733f63ab0800000000000000
      internalID: -5028564513039268329
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_124
      rect:
        serializedVersion: 2
        x: 560
        y: 608
        width: 48
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb0658a9cdf9440b0800000000000000
      internalID: -5745291454783856453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_125
      rect:
        serializedVersion: 2
        x: 1472
        y: 640
        width: 64
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7d5d26def4dcafab0800000000000000
      internalID: -4973437095308503593
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_126
      rect:
        serializedVersion: 2
        x: 208
        y: 576
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a8092f82b39371ef0800000000000000
      internalID: -137578337358671734
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_127
      rect:
        serializedVersion: 2
        x: 416
        y: 576
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 573e00b35b4f0e7a0800000000000000
      internalID: -6349806415376227467
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_128
      rect:
        serializedVersion: 2
        x: 464
        y: 575
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e0214df9ca2c1c510800000000000000
      internalID: 1567748191973282318
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_129
      rect:
        serializedVersion: 2
        x: 1424
        y: 592
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf40808cf7cef3db0800000000000000
      internalID: -4809865843447429893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_130
      rect:
        serializedVersion: 2
        x: 1472
        y: 608
        width: 96
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fe62a44037c722a10800000000000000
      internalID: 1883204427629340399
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_131
      rect:
        serializedVersion: 2
        x: 64
        y: 527
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5f0ab93f68401b230800000000000000
      internalID: 3652705750433374453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_132
      rect:
        serializedVersion: 2
        x: 112
        y: 543
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6d03dcd8e302b8e30800000000000000
      internalID: 4506731305153409238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_133
      rect:
        serializedVersion: 2
        x: 160
        y: 528
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 20e3a04d2d6a1f070800000000000000
      internalID: 8138469426065980930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_134
      rect:
        serializedVersion: 2
        x: 208
        y: 528
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd3565da6134ddc40800000000000000
      internalID: 5538656881412035547
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_135
      rect:
        serializedVersion: 2
        x: 239
        y: 528
        width: 224
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e8f3641277d720c90800000000000000
      internalID: -7205058503226540146
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_136
      rect:
        serializedVersion: 2
        x: 480
        y: 528
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a0bb3543208c3dc10800000000000000
      internalID: 2077223764941323018
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_137
      rect:
        serializedVersion: 2
        x: 512
        y: 528
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf7d9a6141b14b940800000000000000
      internalID: 5310899633670182907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_138
      rect:
        serializedVersion: 2
        x: 560
        y: 528
        width: 32
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8bab819abc59acdc0800000000000000
      internalID: -3617914648690902344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_139
      rect:
        serializedVersion: 2
        x: 608
        y: 559
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: eb8631c1455a73a60800000000000000
      internalID: 7653767872406317246
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_140
      rect:
        serializedVersion: 2
        x: 47
        y: 367
        width: 98
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d2a8254cfaa7b2780800000000000000
      internalID: -8706730559259112915
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_141
      rect:
        serializedVersion: 2
        x: 159
        y: 367
        width: 66
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d59ff320a7a8fac00800000000000000
      internalID: 914101506007890269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_142
      rect:
        serializedVersion: 2
        x: 239
        y: 399
        width: 162
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9f84be34a881086d0800000000000000
      internalID: -2990363170449962759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_143
      rect:
        serializedVersion: 2
        x: 479
        y: 367
        width: 66
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 20d8473fbb5032720800000000000000
      internalID: 2820104096465587458
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_144
      rect:
        serializedVersion: 2
        x: 559
        y: 367
        width: 98
        height: 66
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bc98055677a514dc0800000000000000
      internalID: -3656541953600747061
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_145
      rect:
        serializedVersion: 2
        x: 687
        y: 415
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f0d22a335b0e52330800000000000000
      internalID: 3685598938932587791
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_146
      rect:
        serializedVersion: 2
        x: 719
        y: 415
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 541b13884ac03e850800000000000000
      internalID: 6404976995850105157
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_147
      rect:
        serializedVersion: 2
        x: 767
        y: 415
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9fbaa02bb3a45a810800000000000000
      internalID: 1775907248318098425
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_148
      rect:
        serializedVersion: 2
        x: 815
        y: 415
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f72cfacb4f64fcef0800000000000000
      internalID: -85771850945150337
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_149
      rect:
        serializedVersion: 2
        x: 863
        y: 415
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cf83ff0a77a6878d0800000000000000
      internalID: -2848409702277105412
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_150
      rect:
        serializedVersion: 2
        x: 911
        y: 415
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 016d3b75f0bce8880800000000000000
      internalID: -8606718571102087664
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_151
      rect:
        serializedVersion: 2
        x: 959
        y: 415
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6a39aa59fa0804410800000000000000
      internalID: 1459307770886656934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_152
      rect:
        serializedVersion: 2
        x: 1007
        y: 415
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f60bc0faf735be270800000000000000
      internalID: 8280804147713585263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_153
      rect:
        serializedVersion: 2
        x: 1087
        y: 431
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3250826abc1ac6c50800000000000000
      internalID: 6659875845012194595
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_154
      rect:
        serializedVersion: 2
        x: 1151
        y: 431
        width: 114
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c135682e26df4e580800000000000000
      internalID: -8798629170826030308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_155
      rect:
        serializedVersion: 2
        x: 239
        y: 351
        width: 178
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d147aa61f7e04c730800000000000000
      internalID: 4018352706525230109
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_156
      rect:
        serializedVersion: 2
        x: 687
        y: 239
        width: 18
        height: 146
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: faf28ab520d2916a0800000000000000
      internalID: -6478097100844814417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_157
      rect:
        serializedVersion: 2
        x: 719
        y: 367
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7a461aa3a483ee920800000000000000
      internalID: 3021414291474572455
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_158
      rect:
        serializedVersion: 2
        x: 767
        y: 351
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 97851c7449c5179c0800000000000000
      internalID: -3931259207788963719
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_159
      rect:
        serializedVersion: 2
        x: 833
        y: 223
        width: 32
        height: 160
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: da931220cbf388470800000000000000
      internalID: 8397031581954423213
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_160
      rect:
        serializedVersion: 2
        x: 911
        y: 351
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8f5a3e5329031d3e0800000000000000
      internalID: -2030788552439912968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_161
      rect:
        serializedVersion: 2
        x: 959
        y: 367
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 98dc85ec064bbc740800000000000000
      internalID: 5173426924836933001
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_162
      rect:
        serializedVersion: 2
        x: 1007
        y: 239
        width: 18
        height: 146
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8624a38ef51ed0050800000000000000
      internalID: 5768514499765748328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_163
      rect:
        serializedVersion: 2
        x: 1087
        y: 303
        width: 50
        height: 114
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 20bb6dfe1a75fb660800000000000000
      internalID: 7403732665445563138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_164
      rect:
        serializedVersion: 2
        x: 1152
        y: 320
        width: 128
        height: 96
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a7fdc420c16c61650800000000000000
      internalID: 6203363360349413242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_165
      rect:
        serializedVersion: 2
        x: 1312
        y: 384
        width: 64
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 733528f02a93408b0800000000000000
      internalID: -5186957502616284361
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_166
      rect:
        serializedVersion: 2
        x: 1471
        y: 383
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f43421e72a61c1a50800000000000000
      internalID: 6493089649917051727
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_167
      rect:
        serializedVersion: 2
        x: 1535
        y: 383
        width: 18
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1abef6d1a91f306c0800000000000000
      internalID: -4178230385048556639
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_168
      rect:
        serializedVersion: 2
        x: 1567
        y: 383
        width: 18
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c738be8b5958871c0800000000000000
      internalID: -4505704549085117572
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_169
      rect:
        serializedVersion: 2
        x: 1599
        y: 367
        width: 34
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5eaa0f9289db69c50800000000000000
      internalID: 6671728359232285413
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_170
      rect:
        serializedVersion: 2
        x: 47
        y: 303
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b45da9b30138dcc70800000000000000
      internalID: 8992988136671466827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_171
      rect:
        serializedVersion: 2
        x: 175
        y: 319
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 648af22c27bf72c30800000000000000
      internalID: 4334709636670138438
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_172
      rect:
        serializedVersion: 2
        x: 207
        y: 335
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34a74ce7f94d48980800000000000000
      internalID: -8537465212095923645
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_173
      rect:
        serializedVersion: 2
        x: 479
        y: 335
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: abdfa8ee007ccdb10800000000000000
      internalID: 2007698340716412346
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_174
      rect:
        serializedVersion: 2
        x: 511
        y: 319
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80985336bafc1d030800000000000000
      internalID: 3517821118963878152
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_175
      rect:
        serializedVersion: 2
        x: 1311
        y: 303
        width: 50
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 926c2bd81746e6900800000000000000
      internalID: 679591032650909225
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_176
      rect:
        serializedVersion: 2
        x: 1407
        y: 239
        width: 18
        height: 98
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e2d3fb7d539cc6de0800000000000000
      internalID: -1338473756145795794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_177
      rect:
        serializedVersion: 2
        x: 1439
        y: 335
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 590815aee86471d20800000000000000
      internalID: 3249143235801612437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_178
      rect:
        serializedVersion: 2
        x: 1567
        y: 303
        width: 34
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 376537436b3d7bda0800000000000000
      internalID: -5929037604892486029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_179
      rect:
        serializedVersion: 2
        x: 1615
        y: 303
        width: 18
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b3048cebc65631ba0800000000000000
      internalID: -6119435950934704069
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_180
      rect:
        serializedVersion: 2
        x: 159
        y: 176
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 03289621655273ff0800000000000000
      internalID: -56535418712522192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_181
      rect:
        serializedVersion: 2
        x: 207
        y: 255
        width: 50
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6a4c04ae49d5e5870800000000000000
      internalID: 8673472826528810150
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_182
      rect:
        serializedVersion: 2
        x: 271
        y: 271
        width: 34
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80b081ce8513fedd0800000000000000
      internalID: -2454689013905749240
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_183
      rect:
        serializedVersion: 2
        x: 319
        y: 271
        width: 18
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 53b3972cff1f9bb50800000000000000
      internalID: 6609580008924592949
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_184
      rect:
        serializedVersion: 2
        x: 351
        y: 255
        width: 50
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b823de3600bcdfb30800000000000000
      internalID: 4322834419882472075
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_185
      rect:
        serializedVersion: 2
        x: 512
        y: 176
        width: 32
        height: 128
      alignment: 9
      pivot: {x: 0.5, y: 0.62}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a912395808af31c60800000000000000
      internalID: 7787843610557489562
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_186
      rect:
        serializedVersion: 2
        x: 1151
        y: 271
        width: 114
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: cd738b1090a699b00800000000000000
      internalID: 835815792770037724
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_187
      rect:
        serializedVersion: 2
        x: 1321
        y: 215
        width: 32
        height: 64
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5169e63b36f04ab30800000000000000
      internalID: 4297576865304843797
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_188
      rect:
        serializedVersion: 2
        x: 1439
        y: 239
        width: 34
        height: 82
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 61996cd1dc06b5eb0800000000000000
      internalID: -4730080549538457322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_189
      rect:
        serializedVersion: 2
        x: 1487
        y: 271
        width: 66
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4b8fc1322eb898e50800000000000000
      internalID: 6812129714742556852
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_190
      rect:
        serializedVersion: 2
        x: 95
        y: 207
        width: 50
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 534e6834e611827e0800000000000000
      internalID: -1790161686602783691
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_191
      rect:
        serializedVersion: 2
        x: 559
        y: 207
        width: 50
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d26e50f7e65868510800000000000000
      internalID: 1551073831299704365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_192
      rect:
        serializedVersion: 2
        x: 1088
        y: 127
        width: 96
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dd7d791f5d8f03d30800000000000000
      internalID: 4409297632960698333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_193
      rect:
        serializedVersion: 2
        x: 1487
        y: 239
        width: 82
        height: 18
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16f2cd4d47ce13480800000000000000
      internalID: -8921089400312680607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_194
      rect:
        serializedVersion: 2
        x: 63
        y: 127
        width: 34
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c89ae2c12431277d0800000000000000
      internalID: -2922252033548441204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_195
      rect:
        serializedVersion: 2
        x: 208
        y: 176
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a07d08aa96f3712b0800000000000000
      internalID: -5613948687429347574
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_196
      rect:
        serializedVersion: 2
        x: 416
        y: 176
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 861da164c567dd610800000000000000
      internalID: 1647603177372701032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_197
      rect:
        serializedVersion: 2
        x: 463
        y: 183
        width: 34
        height: 26
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bf6b2e6f43e2279f0800000000000000
      internalID: -472264205905250565
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_198
      rect:
        serializedVersion: 2
        x: 607
        y: 127
        width: 34
        height: 65
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 431f362f1941dd120800000000000000
      internalID: 2440129190174126388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_199
      rect:
        serializedVersion: 2
        x: 1439
        y: 191
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c1b7ba7f938d720b0800000000000000
      internalID: -5753392255462573284
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_200
      rect:
        serializedVersion: 2
        x: 111
        y: 127
        width: 34
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36e25fcdab4ca3a00800000000000000
      internalID: 737117795878514275
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_201
      rect:
        serializedVersion: 2
        x: 159
        y: 127
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 98669797d46427b00800000000000000
      internalID: 824798980326647433
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_202
      rect:
        serializedVersion: 2
        x: 207
        y: 127
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d655b6008cc9c9cf0800000000000000
      internalID: -244147896970422931
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_203
      rect:
        serializedVersion: 2
        x: 239
        y: 127
        width: 224
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ee79ffaf703ea06b0800000000000000
      internalID: -5329197585624229906
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_204
      rect:
        serializedVersion: 2
        x: 479
        y: 127
        width: 18
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: de2954e55310db5a0800000000000000
      internalID: -6504040958126615827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_205
      rect:
        serializedVersion: 2
        x: 511
        y: 127
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 92927c39a617d2920800000000000000
      internalID: 2967152432066799913
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_206
      rect:
        serializedVersion: 2
        x: 559
        y: 127
        width: 34
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 56a62781b0fd32360800000000000000
      internalID: 7143798672687524453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_207
      rect:
        serializedVersion: 2
        x: 1088
        y: 160
        width: 96
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 8270ee2deb92845458c00ffac87fbb46
      internalID: 843452
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_208
      rect:
        serializedVersion: 2
        x: 1088
        y: 192
        width: 96
        height: 63
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ba2115ca68a79e48940d14e29b829b8
      internalID: 2032788670
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_209
      rect:
        serializedVersion: 2
        x: 608
        y: 527
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2d49dba378b22c34a839d59a427870bb
      internalID: 853615811
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_210
      rect:
        serializedVersion: 2
        x: 560
        y: 527
        width: 32
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 872da47ad706b924bad5a5ae993558be
      internalID: 2052123419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: main_lev_build_211
      rect:
        serializedVersion: 2
        x: 1384
        y: 1188
        width: 96
        height: 64
      alignment: 9
      pivot: {x: 0.75, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 285da199dcb3dfe4783452de62448903
      internalID: 2108330842
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: adf1abb9bb6c01a4f947cb3291aab5aa
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      main_lev_build_0: -7321596740296343610
      main_lev_build_1: 2036342897494705274
      main_lev_build_10: -1768509884834896043
      main_lev_build_100: 621298860972965650
      main_lev_build_101: 193150957163764894
      main_lev_build_102: 6524111832456121975
      main_lev_build_103: -2660265737741929098
      main_lev_build_104: -8021619760953474160
      main_lev_build_105: -5837570162875244700
      main_lev_build_106: -44995796484101441
      main_lev_build_107: -1062130375744474319
      main_lev_build_108: -622970485326188544
      main_lev_build_109: -8362269351111413254
      main_lev_build_11: -3536153544004010262
      main_lev_build_110: 9069556223356447325
      main_lev_build_111: 4233993977004366485
      main_lev_build_112: 2276484700516229305
      main_lev_build_113: -5210863717491564103
      main_lev_build_114: 2070571115308212702
      main_lev_build_115: -8610950664914644571
      main_lev_build_116: -4960856750088168621
      main_lev_build_117: 8199563988000738364
      main_lev_build_118: -2206904888097387290
      main_lev_build_119: 151522025708051767
      main_lev_build_12: -5078194952174540175
      main_lev_build_120: -803912396741906382
      main_lev_build_121: -746997140243067684
      main_lev_build_122: -2717125245174493432
      main_lev_build_123: -5028564513039268329
      main_lev_build_124: -5745291454783856453
      main_lev_build_125: -4973437095308503593
      main_lev_build_126: -137578337358671734
      main_lev_build_127: -6349806415376227467
      main_lev_build_128: 1567748191973282318
      main_lev_build_129: -4809865843447429893
      main_lev_build_13: -7553685545647535464
      main_lev_build_130: 1883204427629340399
      main_lev_build_131: 3652705750433374453
      main_lev_build_132: 4506731305153409238
      main_lev_build_133: 8138469426065980930
      main_lev_build_134: 5538656881412035547
      main_lev_build_135: -7205058503226540146
      main_lev_build_136: 2077223764941323018
      main_lev_build_137: 5310899633670182907
      main_lev_build_138: -3617914648690902344
      main_lev_build_139: 7653767872406317246
      main_lev_build_14: 6195799413063252396
      main_lev_build_140: -8706730559259112915
      main_lev_build_141: 914101506007890269
      main_lev_build_142: -2990363170449962759
      main_lev_build_143: 2820104096465587458
      main_lev_build_144: -3656541953600747061
      main_lev_build_145: 3685598938932587791
      main_lev_build_146: 6404976995850105157
      main_lev_build_147: 1775907248318098425
      main_lev_build_148: -85771850945150337
      main_lev_build_149: -2848409702277105412
      main_lev_build_15: 7158266655426338334
      main_lev_build_150: -8606718571102087664
      main_lev_build_151: 1459307770886656934
      main_lev_build_152: 8280804147713585263
      main_lev_build_153: 6659875845012194595
      main_lev_build_154: -8798629170826030308
      main_lev_build_155: 4018352706525230109
      main_lev_build_156: -6478097100844814417
      main_lev_build_157: 3021414291474572455
      main_lev_build_158: -3931259207788963719
      main_lev_build_159: 8397031581954423213
      main_lev_build_16: -5141582850063230037
      main_lev_build_160: -2030788552439912968
      main_lev_build_161: 5173426924836933001
      main_lev_build_162: 5768514499765748328
      main_lev_build_163: 7403732665445563138
      main_lev_build_164: 6203363360349413242
      main_lev_build_165: -5186957502616284361
      main_lev_build_166: 6493089649917051727
      main_lev_build_167: -4178230385048556639
      main_lev_build_168: -4505704549085117572
      main_lev_build_169: 6671728359232285413
      main_lev_build_17: -6145452952202039930
      main_lev_build_170: 8992988136671466827
      main_lev_build_171: 4334709636670138438
      main_lev_build_172: -8537465212095923645
      main_lev_build_173: 2007698340716412346
      main_lev_build_174: 3517821118963878152
      main_lev_build_175: 679591032650909225
      main_lev_build_176: -1338473756145795794
      main_lev_build_177: 3249143235801612437
      main_lev_build_178: -5929037604892486029
      main_lev_build_179: -6119435950934704069
      main_lev_build_18: -3545220061732740010
      main_lev_build_180: -56535418712522192
      main_lev_build_181: 8673472826528810150
      main_lev_build_182: -2454689013905749240
      main_lev_build_183: 6609580008924592949
      main_lev_build_184: 4322834419882472075
      main_lev_build_185: 7787843610557489562
      main_lev_build_186: 835815792770037724
      main_lev_build_187: 4297576865304843797
      main_lev_build_188: -4730080549538457322
      main_lev_build_189: 6812129714742556852
      main_lev_build_19: -4363625974722005862
      main_lev_build_190: -1790161686602783691
      main_lev_build_191: 1551073831299704365
      main_lev_build_192: 4409297632960698333
      main_lev_build_193: -8921089400312680607
      main_lev_build_194: -2922252033548441204
      main_lev_build_195: -5613948687429347574
      main_lev_build_196: 1647603177372701032
      main_lev_build_197: -472264205905250565
      main_lev_build_198: 2440129190174126388
      main_lev_build_199: -5753392255462573284
      main_lev_build_2: 6690074168642156511
      main_lev_build_20: 1311053599123588642
      main_lev_build_200: 737117795878514275
      main_lev_build_201: 824798980326647433
      main_lev_build_202: -244147896970422931
      main_lev_build_203: -5329197585624229906
      main_lev_build_204: -6504040958126615827
      main_lev_build_205: 2967152432066799913
      main_lev_build_206: 7143798672687524453
      main_lev_build_207: 843452
      main_lev_build_208: 2032788670
      main_lev_build_209: 853615811
      main_lev_build_21: -7391597574769789040
      main_lev_build_210: 2052123419
      main_lev_build_211: 2108330842
      main_lev_build_22: 4061335903751664338
      main_lev_build_23: -8727740464184286612
      main_lev_build_24: -5928373181076509889
      main_lev_build_25: -3600304986691838317
      main_lev_build_26: -5807119682142418728
      main_lev_build_27: -769258450561268069
      main_lev_build_28: 8804179717975675962
      main_lev_build_29: -7556422623461489005
      main_lev_build_3: 5491440574836273819
      main_lev_build_30: -3179065492051820323
      main_lev_build_31: 6367844002441579164
      main_lev_build_32: 530697048666210728
      main_lev_build_33: 4395045069217402341
      main_lev_build_34: 2092627775496249973
      main_lev_build_35: -2376033672055137745
      main_lev_build_36: -1375255701310948154
      main_lev_build_37: -485270578018098674
      main_lev_build_38: 3633828979255977272
      main_lev_build_39: 6434411865305387303
      main_lev_build_4: -5725699659698489387
      main_lev_build_40: -4289367072409244655
      main_lev_build_41: 997770449508396777
      main_lev_build_42: 1640190099747058255
      main_lev_build_43: 8123933813599918988
      main_lev_build_44: -3347604227171386211
      main_lev_build_45: -57643045793180287
      main_lev_build_46: 1037133018308231650
      main_lev_build_47: 2385191094207926952
      main_lev_build_48: -7460362894449188874
      main_lev_build_49: 9004167804233604409
      main_lev_build_5: 983365781762884736
      main_lev_build_50: -7841815696645775610
      main_lev_build_51: -5227130058253163313
      main_lev_build_52: -2254558842325139031
      main_lev_build_53: 6552942105971872028
      main_lev_build_54: 3476739193793682195
      main_lev_build_55: -4964059160939747986
      main_lev_build_56: 2733483858526708977
      main_lev_build_57: 4554884612160548212
      main_lev_build_58: -7699477089096638638
      main_lev_build_59: -7386319898669079621
      main_lev_build_6: 6610379306505157424
      main_lev_build_60: 8630728240354937356
      main_lev_build_61: 7265131014059844303
      main_lev_build_62: 425690847245178098
      main_lev_build_63: -4354054293520004780
      main_lev_build_64: 415934858433069832
      main_lev_build_65: 3458790219721625088
      main_lev_build_66: -2547002701821570693
      main_lev_build_67: -6710359665389112560
      main_lev_build_68: 7914680821397812226
      main_lev_build_69: -2815293676601201418
      main_lev_build_7: -3638452437159074076
      main_lev_build_70: 8014137359537955170
      main_lev_build_71: 1998666568486746414
      main_lev_build_72: 1008047188828136873
      main_lev_build_73: 2975915751906570460
      main_lev_build_74: -38210694034918336
      main_lev_build_75: -4708163543103279049
      main_lev_build_76: 2077038968476330148
      main_lev_build_77: -3715384358238098460
      main_lev_build_78: -2389742835691918131
      main_lev_build_79: -417794507198324013
      main_lev_build_8: 5620601010649040209
      main_lev_build_80: 2277096919056543555
      main_lev_build_81: -8135579845998952261
      main_lev_build_82: 3397236183029400507
      main_lev_build_83: -1641970265319708320
      main_lev_build_84: -8641513879433373621
      main_lev_build_85: 3857487717421996754
      main_lev_build_86: 7613901830944957695
      main_lev_build_87: -4401267317277905845
      main_lev_build_88: 4542144513190088667
      main_lev_build_89: -1876134194446544935
      main_lev_build_9: 196594637770524257
      main_lev_build_90: 567746709657321588
      main_lev_build_91: 2657539554596082145
      main_lev_build_92: -7945661129260217745
      main_lev_build_93: -6924404252945211523
      main_lev_build_94: 6734544703757080922
      main_lev_build_95: -5132723718999068900
      main_lev_build_96: -3759255416618297252
      main_lev_build_97: -7154212806900720164
      main_lev_build_98: -5007958133994179420
      main_lev_build_99: 2877082738769336614
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
