fileFormatVersion: 2
guid: 3b9a54b8541a0df4c8ca6447cbcc279a
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -9192638271177797499
    second: death_0
  - first:
      213: 8559509556701815258
    second: death_1
  - first:
      213: -5286180679089938723
    second: death_2
  - first:
      213: -1891855120789479077
    second: death_3
  - first:
      213: -5582970244224541034
    second: death_4
  - first:
      213: 830680230883465780
    second: death_5
  - first:
      213: 6596258498841548387
    second: death_6
  - first:
      213: -4147321062969920389
    second: death_7
  - first:
      213: 2602597460433512539
    second: death_8
  - first:
      213: -2134515381725696932
    second: death_9
  - first:
      213: -270028788923616002
    second: death_10
  - first:
      213: -5121890589552714621
    second: death_11
  - first:
      213: 627783550670650900
    second: death_12
  - first:
      213: 2891620135027730149
    second: death_13
  - first:
      213: 7975919600420608313
    second: death_14
  - first:
      213: 6275631635777415744
    second: death_15
  - first:
      213: -9015114149152096869
    second: death_16
  - first:
      213: -133209616158212645
    second: death_17
  - first:
      213: -7524816321260968566
    second: death_18
  - first:
      213: -3394814105837533734
    second: death_19
  - first:
      213: -7933534624763666014
    second: death_20
  - first:
      213: -5417755882475958080
    second: death_21
  - first:
      213: -3414521176158850147
    second: death_22
  - first:
      213: -965786126583861285
    second: death_23
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: death_0
      rect:
        serializedVersion: 2
        x: 0
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 58059c172303d6080800000000000000
      internalID: -9192638271177797499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_1
      rect:
        serializedVersion: 2
        x: 100
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad1e5c2779c79c670800000000000000
      internalID: 8559509556701815258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_2
      rect:
        serializedVersion: 2
        x: 200
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dde9f440ea6b3a6b0800000000000000
      internalID: -5286180679089938723
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_3
      rect:
        serializedVersion: 2
        x: 300
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b51e6d68ac7ceb5e0800000000000000
      internalID: -1891855120789479077
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_4
      rect:
        serializedVersion: 2
        x: 400
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69e9b69842e4582b0800000000000000
      internalID: -5582970244224541034
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_5
      rect:
        serializedVersion: 2
        x: 500
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 43a9fb3d44b278b00800000000000000
      internalID: 830680230883465780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_6
      rect:
        serializedVersion: 2
        x: 600
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3661a1ef72e9a8b50800000000000000
      internalID: 6596258498841548387
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_7
      rect:
        serializedVersion: 2
        x: 700
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b74be287771c176c0800000000000000
      internalID: -4147321062969920389
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_8
      rect:
        serializedVersion: 2
        x: 800
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b582972e9984e1420800000000000000
      internalID: 2602597460433512539
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_9
      rect:
        serializedVersion: 2
        x: 900
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5ce1a8c19da062e0800000000000000
      internalID: -2134515381725696932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_10
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef434154f3aa04cf0800000000000000
      internalID: -270028788923616002
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_11
      rect:
        serializedVersion: 2
        x: 100
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 38c5b4f45a36be8b0800000000000000
      internalID: -5121890589552714621
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_12
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41ebe8d20d556b800800000000000000
      internalID: 627783550670650900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_13
      rect:
        serializedVersion: 2
        x: 300
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e6e1bd3139112820800000000000000
      internalID: 2891620135027730149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_14
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 939552c4e9820be60800000000000000
      internalID: 7975919600420608313
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_15
      rect:
        serializedVersion: 2
        x: 500
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04a67b719b5871750800000000000000
      internalID: 6275631635777415744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_16
      rect:
        serializedVersion: 2
        x: 600
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9df03a0271e3e280800000000000000
      internalID: -9015114149152096869
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: death_17
      rect:
        serializedVersion: 2
        x: 700
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bdd39345f8eb62ef0800000000000000
      internalID: -133209616158212645
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: a978c030bd616734f85a2aacddcb11ad
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":100.0,"y":100.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      death_0: -9192638271177797499
      death_1: 8559509556701815258
      death_10: -270028788923616002
      death_11: -5121890589552714621
      death_12: 627783550670650900
      death_13: 2891620135027730149
      death_14: 7975919600420608313
      death_15: 6275631635777415744
      death_16: -9015114149152096869
      death_17: -133209616158212645
      death_2: -5286180679089938723
      death_3: -1891855120789479077
      death_4: -5582970244224541034
      death_5: 830680230883465780
      death_6: 6596258498841548387
      death_7: -4147321062969920389
      death_8: 2602597460433512539
      death_9: -2134515381725696932
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
