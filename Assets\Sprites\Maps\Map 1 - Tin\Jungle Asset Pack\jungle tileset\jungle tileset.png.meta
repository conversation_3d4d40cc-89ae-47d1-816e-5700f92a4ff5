fileFormatVersion: 2
guid: 1b292c656a0d5ca4694b3a227932b8bc
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -337650094436226022
    second: jungle tileset_0
  - first:
      213: 4762510025463220358
    second: jungle tileset_1
  - first:
      213: 2841706884336108830
    second: jungle tileset_2
  - first:
      213: 5702881312061213028
    second: jungle tileset_3
  - first:
      213: 1675147065547018455
    second: jungle tileset_4
  - first:
      213: -6678837315819468931
    second: jungle tileset_5
  - first:
      213: 5884953701289943405
    second: jungle tileset_6
  - first:
      213: 942894245647230796
    second: jungle tileset_7
  - first:
      213: -2015094521346789308
    second: jungle tileset_8
  - first:
      213: -3398328481707034505
    second: jungle tileset_9
  - first:
      213: -4748219608448842564
    second: jungle tileset_10
  - first:
      213: 6255656890204623732
    second: jungle tileset_11
  - first:
      213: -8856663237800934286
    second: jungle tileset_12
  - first:
      213: -7237579458346611564
    second: jungle tileset_13
  - first:
      213: -3726219476323514782
    second: jungle tileset_14
  - first:
      213: 6520680041915987534
    second: jungle tileset_15
  - first:
      213: -5458122804092721661
    second: jungle tileset_16
  - first:
      213: -8833861716742289968
    second: jungle tileset_17
  - first:
      213: 1922136139609853226
    second: jungle tileset_18
  - first:
      213: -3934263221575823159
    second: jungle tileset_19
  - first:
      213: -3261541911563217398
    second: jungle tileset_20
  - first:
      213: -2387910127377212895
    second: jungle tileset_21
  - first:
      213: -5465477971330731349
    second: jungle tileset_22
  - first:
      213: 4424424743136084365
    second: jungle tileset_23
  - first:
      213: 5049845495281047351
    second: jungle tileset_24
  - first:
      213: 8720780663327681697
    second: jungle tileset_25
  - first:
      213: 653053054627100677
    second: jungle tileset_26
  - first:
      213: 2620094406820684301
    second: jungle tileset_27
  - first:
      213: 8362945715815971176
    second: jungle tileset_28
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: jungle tileset_0
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a1c60ce270d605bf0800000000000000
      internalID: -337650094436226022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_1
      rect:
        serializedVersion: 2
        x: 160
        y: 304
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 688c8f2a2a5d71240800000000000000
      internalID: 4762510025463220358
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_2
      rect:
        serializedVersion: 2
        x: 208
        y: 256
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e15c2a4fa55cf6720800000000000000
      internalID: 2841706884336108830
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_3
      rect:
        serializedVersion: 2
        x: 304
        y: 240
        width: 48
        height: 112
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4694f919654b42f40800000000000000
      internalID: 5702881312061213028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_4
      rect:
        serializedVersion: 2
        x: 368
        y: 240
        width: 112
        height: 112
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d0d77c71615f3710800000000000000
      internalID: 1675147065547018455
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_5
      rect:
        serializedVersion: 2
        x: 560
        y: 272
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7705f5e8d00053a0800000000000000
      internalID: -6678837315819468931
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_6
      rect:
        serializedVersion: 2
        x: 656
        y: 272
        width: 48
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d61e105263e8ba150800000000000000
      internalID: 5884953701289943405
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_7
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c47209c1255d51d00800000000000000
      internalID: 942894245647230796
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_8
      rect:
        serializedVersion: 2
        x: 128
        y: 176
        width: 96
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 440f9429532f804e0800000000000000
      internalID: -2015094521346789308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_9
      rect:
        serializedVersion: 2
        x: 240
        y: 176
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77872c14046b6d0d0800000000000000
      internalID: -3398328481707034505
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_10
      rect:
        serializedVersion: 2
        x: 320
        y: 176
        width: 112
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb4ccda5c6fea1eb0800000000000000
      internalID: -4748219608448842564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_11
      rect:
        serializedVersion: 2
        x: 464
        y: 176
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4731f593cce80d650800000000000000
      internalID: 6255656890204623732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_12
      rect:
        serializedVersion: 2
        x: 528
        y: 208
        width: 80
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2744d97b5bfc61580800000000000000
      internalID: -8856663237800934286
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_13
      rect:
        serializedVersion: 2
        x: 656
        y: 176
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49c069734d3fe8b90800000000000000
      internalID: -7237579458346611564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_14
      rect:
        serializedVersion: 2
        x: 192
        y: 64
        width: 96
        height: 96
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 266489ee02fc94cc0800000000000000
      internalID: -3726219476323514782
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_15
      rect:
        serializedVersion: 2
        x: 304
        y: 128
        width: 32
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e46b16833fb1e7a50800000000000000
      internalID: 6520680041915987534
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_16
      rect:
        serializedVersion: 2
        x: 352
        y: 112
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30628916a3ad044b0800000000000000
      internalID: -5458122804092721661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_17
      rect:
        serializedVersion: 2
        x: 416
        y: 112
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d547aa8291d76580800000000000000
      internalID: -8833861716742289968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_18
      rect:
        serializedVersion: 2
        x: 480
        y: 145
        width: 15
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2dc26392acccaa10800000000000000
      internalID: 1922136139609853226
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_19
      rect:
        serializedVersion: 2
        x: 497
        y: 145
        width: 15
        height: 15
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9c0d611d170b669c0800000000000000
      internalID: -3934263221575823159
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_20
      rect:
        serializedVersion: 2
        x: 16
        y: 112
        width: 160
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a06d93d69ecacb2d0800000000000000
      internalID: -3261541911563217398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_21
      rect:
        serializedVersion: 2
        x: 480
        y: 128
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 12a9f1e04607cded0800000000000000
      internalID: -2387910127377212895
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_22
      rect:
        serializedVersion: 2
        x: 497
        y: 128
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba2fb1f0eb8b624b0800000000000000
      internalID: -5465477971330731349
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_23
      rect:
        serializedVersion: 2
        x: 672
        y: 48
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d81431ebcd6b66d30800000000000000
      internalID: 4424424743136084365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_24
      rect:
        serializedVersion: 2
        x: 304
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73755d409b7a41640800000000000000
      internalID: 5049845495281047351
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_25
      rect:
        serializedVersion: 2
        x: 336
        y: 80
        width: 48
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1acedb10fcf660970800000000000000
      internalID: 8720780663327681697
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_26
      rect:
        serializedVersion: 2
        x: 400
        y: 48
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 508388c0b4c101900800000000000000
      internalID: 653053054627100677
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_27
      rect:
        serializedVersion: 2
        x: 480
        y: 64
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d02e88c2bf17c5420800000000000000
      internalID: 2620094406820684301
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: jungle tileset_28
      rect:
        serializedVersion: 2
        x: 544
        y: 32
        width: 80
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 869f51bd1d62f0470800000000000000
      internalID: 8362945715815971176
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 29fd396d26d169147b845352a8af7382
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":64.0,"y":64.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":0,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      jungle tileset_0: -337650094436226022
      jungle tileset_1: 4762510025463220358
      jungle tileset_10: -4748219608448842564
      jungle tileset_11: 6255656890204623732
      jungle tileset_12: -8856663237800934286
      jungle tileset_13: -7237579458346611564
      jungle tileset_14: -3726219476323514782
      jungle tileset_15: 6520680041915987534
      jungle tileset_16: -5458122804092721661
      jungle tileset_17: -8833861716742289968
      jungle tileset_18: 1922136139609853226
      jungle tileset_19: -3934263221575823159
      jungle tileset_2: 2841706884336108830
      jungle tileset_20: -3261541911563217398
      jungle tileset_21: -2387910127377212895
      jungle tileset_22: -5465477971330731349
      jungle tileset_23: 4424424743136084365
      jungle tileset_24: 5049845495281047351
      jungle tileset_25: 8720780663327681697
      jungle tileset_26: 653053054627100677
      jungle tileset_27: 2620094406820684301
      jungle tileset_28: 8362945715815971176
      jungle tileset_3: 5702881312061213028
      jungle tileset_4: 1675147065547018455
      jungle tileset_5: -6678837315819468931
      jungle tileset_6: 5884953701289943405
      jungle tileset_7: 942894245647230796
      jungle tileset_8: -2015094521346789308
      jungle tileset_9: -3398328481707034505
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
