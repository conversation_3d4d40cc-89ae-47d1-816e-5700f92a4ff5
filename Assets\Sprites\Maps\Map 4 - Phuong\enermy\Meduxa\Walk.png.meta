fileFormatVersion: 2
guid: b34e7147587832241a1182d23fc62977
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 4035959289877228764
    second: Walk_0
  - first:
      213: -982904232830982447
    second: Walk_1
  - first:
      213: -350908408369970039
    second: Walk_2
  - first:
      213: -5409570386352685021
    second: Walk_3
  - first:
      213: 1553940418269799609
    second: Walk_4
  - first:
      213: 2416434504916517810
    second: Walk_5
  - first:
      213: -6428935122508569504
    second: Walk_6
  - first:
      213: 7144598999076838305
    second: Walk_7
  - first:
      213: 7500509103958437945
    second: Walk_8
  - first:
      213: 1008816216748069464
    second: Walk_9
  - first:
      213: 11406907883527339
    second: Walk_10
  - first:
      213: 8030128826893041618
    second: Walk_11
  - first:
      213: 2315132830120601726
    second: Walk_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Walk_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 807051018411f864f854bb23b58fb302
      internalID: -144256328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_1
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 47b732a1e2111134ea066e3de7e89d3e
      internalID: 1293484930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_2
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 62fcbaa888df649428ec4f8ca2b3091a
      internalID: 81551766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_3
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 784fe6e502c05d9438d90ce3e80c681a
      internalID: 425116546
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_4
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a669a2d8bda1bf5418c0736ee7f8c7a1
      internalID: -2091261334
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_5
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 41137a7cc58b8864f88dc45d58e4df98
      internalID: -1819411058
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_6
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7b7a69230cc3b74498e0991963936074
      internalID: 1387549176
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_7
      rect:
        serializedVersion: 2
        x: 896
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aa9d8eb2127bc374d94d234a53bbc204
      internalID: -1500144313
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_8
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 51db3429e29fb7848a28639ea53a8bf6
      internalID: -411766523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_9
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 541ef612c620b444f97848330e1ed11d
      internalID: -169175682
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_10
      rect:
        serializedVersion: 2
        x: 1280
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 650d3a8ad5ad0c746b9fc866cecfd8bb
      internalID: -1103037263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_11
      rect:
        serializedVersion: 2
        x: 1408
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 307e742d90bf6144a9b2f6046b1d027b
      internalID: -730315158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Walk_12
      rect:
        serializedVersion: 2
        x: 1536
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 025053f3aed14d6488b72a8612a08b4a
      internalID: -1070857501
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: bccf8457b85a4dd47b79988d483bdfee
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":128.0,"y":128.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      Walk_0: -144256328
      Walk_1: 1293484930
      Walk_10: -1103037263
      Walk_11: -730315158
      Walk_12: -1070857501
      Walk_2: 81551766
      Walk_3: 425116546
      Walk_4: -2091261334
      Walk_5: -1819411058
      Walk_6: 1387549176
      Walk_7: -1500144313
      Walk_8: -411766523
      Walk_9: -169175682
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
