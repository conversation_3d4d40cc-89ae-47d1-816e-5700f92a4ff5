%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Face 02.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 160 128
%%HiResBoundingBox: 0 0 160 128
%%CropBox: 0 0 160 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 6 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
160 128 li
160 0 li
cp
clp
58.2861 92.957 mo
50.8145 92.957 41.2656 92.001 32.3975 88.2773 cv
29.8516 87.208 28.6543 84.2773 29.7236 81.7305 cv
30.793 79.1846 33.7227 77.9863 36.2705 79.0566 cv
51.3789 85.4033 69.7041 82.0986 69.8877 82.0644 cv
72.6035 81.5664 75.2129 83.3506 75.7188 86.0654 cv
76.2236 88.7803 74.4326 91.3906 71.7178 91.8965 cv
71.2813 91.9775 65.8477 92.957 58.2861 92.957 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.757687 .679133 .626856 .856168 cmyk
f
111.386 92.3516 mo
109.306 92.3516 107.102 92.2119 104.795 91.8838 cv
102.062 91.4941 100.16 88.9619 100.55 86.2285 cv
100.939 83.4951 103.466 81.5898 106.205 81.9834 cv
117.572 83.6055 125.774 79.3262 125.854 79.2822 cv
128.278 77.9531 131.313 78.8428 132.643 81.2637 cv
133.97 83.6846 133.083 86.7236 130.661 88.0518 cv
130.286 88.2568 122.664 92.3516 111.386 92.3516 cv
cp
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Face 02.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9ON;h^Sd)]/q<:%WJRiI?=@RpT;\Eq"%5qE/c)72d1XC*"`C74GZa-XC(j0_.Sm3ILq*jB=^3=tr
%h<V7O]`,h=c2[[YRo%nbUQEYn$LiramGW]dIID[!pr2iChuD@)c*QkF4b*FukAT6?HiEEAV`-9d]`%PZ2lFDQI=:^NVgj634MCRX
%o9V,fr;#LMo'*K1LX-rhSA2X`nacMCS%#Sm^:PhT*>.=5o\j/*$_N"Xn9:VYYC?BV8ABQOmN(p&lGglZGP]F"s3KIIO<;)r]eB.R
%G.hG]`Od+*^Mu3i5Q/[\r8^B<k-mRT2ui&2mKZk7rrl3J>YD-e9<$afLqeVk1=cLkI;/K(MOuI85$[U+D1"H(BG=[&ca]M'QX5Pj
%=AOr/q0_f4?i8!WE:],Tb3e?d,G2tGF3d!H*PkhB!g&k+?UAEXo1-->O8A(uZ[F["@VRpMPHjb+K-,+['j(2u'gGY[HmjZq@*#&"
%P;*:)n-q2G1j9q;*+J$UQ@b`'*BP4QIG"8oDrIoHHn<2=O5[,]VnmK&2ejK@QQfJcC5N&LbrJl[JNlc?9*%5FlM,2Z5%Ub4V1?60
%]t5>]]hIfZX%+/Tku5r#GQ^L1oFU]S9V'':THj/L8NqEg)V7<r?]o=Kjord0#*QRLY]oTWANM@%QLKTKK/J4pPT0d"Gg81aTAZIb
%qcIg+oCukj^3Uc_g.EkTB>o)i/H2WG"O2ApT7?D/%kkYFp\*j`(N3X[$[qU8j20R0Qd<Elb("pNn3\%AJ+J*`j3@Dqn"5('nf@1_
%S+.3jqX+SIh"]406-?\/.2^F$5Tb2J4I9X_/WW$n2]g`QL;/M5Ga#Y.TE"hJD=BsK!1/4]%+O#-A##de;^Z;2^O>^uX/Bn9rS)`1
%K9,rZo2jh^I/<7kZ([Ombi,`g_#NPHkek2>keTcc_/?o2P\Pog^T!(hfd$D^*Z`Jjq4CtUs76b>rn$b>?U'#j4R2G0h(N.5J&$t)
%FA"]Z^\Yon0;$#Xea"%?TYlIsSYta#Dn^]"&tU-d^O>pB(tLMRoM_@VVl+J5nGF<2ff(S!_jUA[9lp0?T76P"aj\!e7pbJWOe7'n
%r:N:fq)*:O^^bXC9>:N]-m@K`NeM]K#q$GAJ/=NB+VUsm@kCf<em<>+Igun\B5_/TiPk+)54T#[cFF5PrPuB#^)jqrL7?1IGh9@f
%]Bcru(Z,/I]B?Bj"ap+1oTP,i]>m/3-+85IrUoF"/"D?4nXi>($a5,f^O5VXq<Re\r=,e%mMgEh,(0(42t2A7YHd)B%t;ZNCL@&<
%j`g47Ish.U]5N/rqY+nL55>-9lSnk?r8u+Zf?\C1O#Q^_+5D8DGL=%[`>n]cQJ][@iB+h6ckCdCTOmm_E(R,254dg<T0F@:f]/k_
%^A5Tl>5mWu5C`FoO.Xo[=7K.is8)PtGAl?TK)YW/BE.gHn.07qbjBZS`jUtpU"oj$cN4&p_gb3Wrntg%'E1&q4_mQ]DJS])SV+?@
%<bDS"9E3NAhrjUH%UgJL"i#sJE.>*!eW%:SO"Z[W-/eel7m>aJ?'j/mHN*K[2qU;qH_1fGr8lDZ:Z"p2qXNtQ&,7geDN3D9!%WS4
%+R,&/o+_-6BGio&r7u;ZeRr7ihnUeVpsWhB]_@!A:\SVhl+903!;QKbfAD&RkkN:BUf"u.B71g+i!FL^>ImVN:/>62I.<!$:VZ_c
%Me=uh\)2W2a.%R)oB,.Ik[^&m8S9MJRk;`Oe!KScM_`QPO6jk)W:Y7SJ#U'4gNtZI^V'Ep2_SNcqeE@*]Nek:(2<-r,/#.#^ou!_
%_6:c[DC,kOGr4_@)?8W5"uDrV"$_?HObB#@Y,%t6hn=:.o(as=o'O'PpY0J.Y.T2]Ze)ji53&Gm3#GoJa`1f=m[+hNm^Y\,1b8T[
%?H#,$H+/nU2)WIl%,=d]nB5TPhsSg(AZ]=Vl\_)'4oN!5?guG$(Xh]>FLTF4MbHriCBo;[W*PkiYLlS&VDjjF?[tgkjflDhR:#sh
%.0sfe?Ms,9:*lFohnOCF>0+rA^O,M=3=BPM3?1UdI2NCS\7n!7jIiL"?@,ZLbL(t?(!,"5r;>d+_Zuk3^7OHPIsh.clPC\8rt)b>
%5r,FDJ1Iji$;H0<$ic">Te#b01mf0-qgV\@$T6m%4Hpk9&0Cs2fV@!To@?E(l1+(p&,uC`KDu7]iN355j"!^C;@8Ws"'2AMWAH5(
%,Q@^.jTsrZ:[msDjX`X(>bKk=TqBG(nPB9$DA^)/5"HP`J\jW%HoLiRTQDXgqEC]"PS99;PRE`1FIO;"6ODG`GCZ"c%j00A,l18=
%^C$24h[P#m.%SK]^5qas^CTo8,F-_+:u`GmJ$sBkn(W4=0&OP?7=VK>"b2jdpb0,OD^_7enmBE"j>ZoLeUNmW\+UcMHEhrQ$MEpO
%rH3Sc@Q!&s:[F#&^AQlq<nq?eqQ&32>soVL%\g#,60[eCjlA&^f%kWWMSFpXT7$1^=5TYjr6_.0SGd;D<+)iTQ:P[(KMk/F*QfNF
%_^Sj-3S]?nff`4+8YOteTc@b-j#Y6L>?]bneoRLLSS9ZW>/e!pca7^GZsGnte0qB(d#.2^I@ujLju#O_KPMmLG0C-)>3Sin:laX:
%cK2Li<d;:rTW(WBV)Ba8*p]eslR`MS]G,29g^<tQ[GDB(+1+Bs[SsfFg[;Y.=&,t;8oME;6U16b^Du%F*s-88$KZ4d69--*FfjH%
%^FiJ\<"?B--lpIh[Qu?Ai.Z&gbMnW#'[[02<)+63]pGLZ'9o8?@lZeWrdCu?0:0lLXbQtA!I[YNZI#FI.bLLK]iu<>pR0lKkA07_
%5On'S7#dPXGi9"n\)m(AYMB0$>7"?)'Nfd*gu6Z!/AFeC5)&)`g^<l-)9Tq.(4QMI>a`*'+n!l&54Di;f\'W9"LS%2j'U$BOE2T6
%3en)b`62keN,'HpZj)NoSBB5nN8.$NeUc"Ji)@LhP+A*VD^)oK4=M.D6@K'L(c]rt?5,Ct"E'GiO?n>;:k#e08B@"aX6Wr_S^+'9
%Bi_6u,)$Q9,_ZA]9;'-h">bs'HbQ>nlit4"</sq.XdqP=#,P4Kf8sJWKae`.!YlRKLC\@@R*cN#_5d"j#j";T)!D9nlNpnm"AG7P
%#S^N_i*gBi#Wff?,I9[Zp@&**"=q8@A@3Eb0^'_ig(=OT";2Du,#7P<('-$.@TbZip^/D.d.dbQr8E810R2O1GYk&8KAof4DB=>+
%Hf46#fH1ST^,C0/f^WJHeN+ZjGf67^%LO8!Tu1*P.07U-QmYW3%b1kI#gL.TBF`.l"#Ggd)[_E&O5/lRl/JM0EB)m1n3H7%I=P<p
%iNt)A8eQuKJ^-#@m.u<t!5jQoE1g.5iPVL.cCg(0Sn=Q4)I27daQjc[ljTLTREcuj9EQ"h25!E&QgfpZV3@H6h:<[O4'cd;dWgcA
%lI(q$Ws[EMQShcR2=Xk%FX1bd@]P8cNij:,1V-n7XhYF1FeWY>f>gu4\h;!kkd2*TXbF"iGRZotZj)OIs,H05k?_b,jrER],kK9%
%SS>6PSfQ)GRHF8M1J9MtZ\pW/A7hCVl:GTpd>SiK0*ukp5\ZIY;j=\N,agTSfJCUWRH/XXnj28j_+/5)8=DghN?JB7"*G1MR3R^O
%9R=h>,L7M4^&g/uqcij2,%h,0(uJQG]h7Dg2<HtnH#K\*!]AP'C%!MLXo^K?=a4".hH<V>!]A:'52T3ib<ftjh0e[lj',ERQi;GU
%@TPN8koTpH]FH#Pn(h&h4Lp"LcpO3cJ,C'dGs?\n4\c-$!*u3^%QSZe^?#cG\[`Y$>L`qM+ha*EYb0!`e6&HbOMAAX,^j"r#eei+
%*hh\@TOi-eQ&ug$3[ot';AS4e,%fqrRWp09itfV9LC_>=_FBf"VNWXp&iGcL8]7(uQHf>)`UH.t%p4s/n@3%^LD`/*]RcdpWfMDM
%Z\d[b'$i<RR@Ogb:_mKSK$$_;\WDu!!/?I7R>4Na'#kEsk0NE]C1.gQ&66RTkXuBqO/+=lfRFY[Y"V'HPdt?#HP/&AnIoY2e-l6n
%j_'Z"L;f#>e'U)h3-tAp;'CPqN>!=##80&.*(c]Sd7/m)ITJP,po%Y!Ukg4c7AV'?B,\Cppj3Sr[ts%'D00M'gSM6KKch(UFmM$G
%kY]()O@1JUBUOP$;5+An`5s&a&5a.V=9rajJlWL2U55GGUfip9+ek0f=\6[Ki=ZlR&0fD!'abL'5-Pkq+s`#+#:^u"Ou4i.RdhXU
%gmWK?aQu`,P5/[_&8+Z?_Ci\,N[9Piai(t&#Ri(9F+ASA3m.1,>s0D)E#T-#+!c4V>YNP>r=4]b<[DNZZ#3:L9%0@R4!+..)MM/$
%3iZX8H[@XRW>Wd#1S)@pQ]pds,,Rph:**tJ_n2C(nk;9/Mk=asjA0b)=<#j2ZE62$C6'Ra<Y8kFLh-UQfA]*7efl;R&93t:f[0h%
%&B.@;AKg_5M@?[>(/@jdA\h?b;ADfZ81F]*8fSkgkY`+#$DM;joH\dTp,;9jI"r"(?3/RNSLQfS$M4,6IN1`^@Pt(jK:.!+F1<JR
%oKWT*$Jl0/_/;tA@PR^q>jNjgdm<bdEj$rTL\VI#,N7ud<2h#;aKqB`<\W*pc_=[87):pI8WhQ)/mX746(tK9^<EMAIL>\Vs
%7`"X>gY9[%9L`_;4#3S:$p.]+_Z_cb%qTc?F>:)$X'qNi:bV/2/&q_'(4s+5jAb5^<DV3!XhZOmE[o^%CT[ioTt!VkFQQ'G<U*I1
%1AkY>MBd]Gm+A^Y1W&#Zdk73?J^3OOkB2qb/u%J\`6n<H2rU+a628B*i.VKpRe?m'O0*/L%G_AKpHcTH>T.qZJWQY2e?B%h-/ceu
%ft"/U<8/1'4$*;3.+dV-/(\BTqk[Gu]j($=Y6nr8*(e:?j]ed*GH#COp$AEq(XtEDY?NT#.eAcDk^:sRJ^/!_R1,kWfO=A"(>(;$
%jC(R)J^/6f-ZT!e1s^lg"JUHoq<@.V%q8Fr!Mhkf-Wus,Kiolsr[8)WWV$8^^_%iD'02#YgnOV0\K3bm:fd(LI^%s]KBKWOf/oFB
%odnCcgNZ"?#P(#c&Y?KS)F'DT]gN'e9SV@S:=Ug_q4B]KO7I^.klb``PC_gHG1G=^59(Xt:0]MKgi7MP630iKaFJm_PIY9`^8[W$
%q<,RRkWpJ`UD7pmWjD:Z3BN54%N"HgJG[1;Dn*l5Br`s9W51rn"\IL;R9Z2'g4@fJ)?9_-]Bj).a5:Km`\$X3fhGuF,hYEK<lhRf
%)LQb^2r/Y9C;4E8.j123NmW9,HR$u,clLPJG;S%rLM:tZSsX\SmY(Z<@-e*R#t&K/]pVllZ1:n9k#G1b%9BX7oM2QJK1Ocfd@W&r
%)"?[>*U!6#N=60PF`n\+IaP2r[n_$?ZOiudO<_;I:SbPVP.n&Y&D"BBRD$4GUo2<\i^C+aS[s\(fu[*<-B7;V*48XK[H=UqM>;r;
%>LLk#0oc[:>QY<05Yg(WE8.7;O&GBoF]kBWC><>j<MQ(]EY8+^E#+YE#'YV1=8q3%Be=Q;ns)o(,RGitA0g<*>O#"['8[\e94A!,
%E>d+3pk_mb0W?UlA<t?b-$#DE`@N5EBDbU`eWWPU(L@2eBX3gcOEerP0&%;kp4021D%U()a+:&d1Tc`H><#GT&A&0O6mA`L[bPE@
%ZPu"%6+]B`5O0^6ZK[HWc>FV$FD3>sC<EoP:q^P5^CFck30>hJC[8F(7DR*DN\Il/^'gtgZI\OB6n=9uSK,EG2K09E?9k&qA90^M
%O6"MEP>D2oR$`XRPcYW+-E=lDBV9"4$/u4okN-dn%*eqg82Q#('X8.:l3XiK>@;UT()(We(b/M7%\Vuk5bJAc!iY5_WrM2:dXTL[
%hsUj(F_tB+?gPq/k%'i"#]$u6/NK_KiedTSh,#K1S9o3u-"88kcFZQSSfoDcI!D7D_H@YEXhJTR!c:f:5]$0PNV5D$k[HHDX]=RM
%qWAJ2PY=PMK7\hp[h'Wjr^.[GcC751b"DTMc5C]9-XpDGg\ouBONX3VX9E?$:Z\r5a&SNN3.4Ym(9"KA.9<a>EKER;@CUK[a<K$W
%!jBtSZakEY$cp_("[SNl#;c!;J-Sn\(_5Ld@0[$i*AiBh$qmdU%UeBJm,1%"K771S!Us?6-/T"h(k@7K!?@WK/hG:Z3#DDmDST<4
%\-"XVTa&c7#'o0FL@Y_s/qk1Ae+k\K%+Oeu2F"JCWSL?%J*MJ0gE5d9@.q?D!F3..D(htllX!_ti;L&W"_t]]WXY3^5q<mhI*^MQ
%XR#Ie>q]9fXD9jUkEW^beG.Ro?EWN=OG6dmXP=[a@k2l3T!,9Ybt<H[@!F@Pprmb!O&`9cLrC:(+>_@QXB^J'CtGkl?CEPOat204
%=Lg`"e$="\@#6I?@^]7.2Cg\Bop/W/aI3pMe1N?(HT(1)CgmF50[Krm2"u`!SF5uk]V?I4"_q"C2+j?1SP#<m^g[h.Jn.;R'g[_E
%HIUSXRo5UPfiAKa(Zg)1#:!Xp3+Oo)<=uc!9e/dY=G%t2(tQnK"cRS;"NRP%6^n*Z"-TL.EXg4G#A1g*5(U%l6*lk^F3-A])GK.C
%nE,"HPmA@Mk+>q,%!R9/FA3e\1i$9]E#QmQ>d)GX50"!NYatm^RF`jh;RU^>8:djAP-Q![/7c%3"L>BpZ8S5pqJ[R("PP10aDkn"
%#\`fCX2\;D*=&m$<3hFTSHLO*%U&)6%f\chXmE_8WmBd;?Ff$Y;m5!CRqnn-((;o6]@]rrUY*rC_T(<6drFCG/0i.rRg\!<=I)(+
%DrYNBGaC.%@Ke;<\m1DW1@kT#lr*qaPs(QKb&J$/qcR`u)utS[UPo'`^X\3?/Y&VuCn0/76e7lCo+b)ZB%mk3[A0<ac:4G,>$DMH
%J*fNABk2M3A?@Fm0M#ZBMB]sED0OWYSK=7Goq-qTW4eS+JkhD3r13f'9f**]2bU,bXRO?SLq3mn81T60EEV*mAI1\$BTb'R:"e1R
%6!HC_(ML2[D-\a9q8rM`(@Q_dmsd1?5Op0c$1WA4K\lLNao&MfrkZ]Y%,%=%i*#-Lj5f$t_JdA&b8(fP2gPhJ2d'Rhq/>eMp`l?#
%MJ(V)#77VJ[-uXoQc-&FHOOae?M;n,6mo8`G2l(m6C";3(=_JgT+'RuiLL"Mr$5]KLW.!+X8NUM*hZeXOZdY!cb`NeXR^7UTFk8+
%@h2+e[/Sa>iE'fd76/qC8b7Wn3S(mFHs63&ei]9MX$+R.B5;NCccfMSX&[F`q6]!k48%#h[.YU/_Z]F['>>!73;KmKeh7^AJqE3S
%V##^sXKX6^7K1T0:R\A5\V9J[Ks4hG)oGH*N+E2&+&0aUl'd)GZd-?U'25Z'fPX2p4J1U)RH3_@@"_M5T(PWCCb.Hc*>glfkZ%[C
%San=0pt93JdmAXJOsTNEp,JAs%V22a!\tTcDg`qN/YtCgfHtl\/\0hYBEs2*1o*+L/$Uoi8T[=s)o#f!U?DFXiYY4/#:k4"g7m(s
%.B?q'.&`7[RLADTUcV3((8u5.H41m"%(XdmKnY*`8q,'i=0-VMN$mBr$L>]j"$ted8.RqH0u,/:MGkC1QJbL^F"k(6%0L/K-P?RQ
%a.p^nNFb',PJqe*OPj)mgIcHg:3\tY#CjALGn)t#A3?uPJ#h8EXlm*`d=->3(T6hG)AB>Wcf)cr)]9Jib0lJ;qqTZCn$`Po^((so
%%kRZ8If7OETQf\]<A)kL/tTQd=[ZmE>S=\]'pL&#,dDhf4`E[uW0nosWWA:r/pV&)S9RkBs%+Bk$&;:taE)AWT:DZF(_3db`H.26
%&$0;akbKRIM63@@O2O"G-_;"FSL!ft<8pcG\CaY@.-<m*5&$NDP*VW[QVrV]2rWE5d>u6-cc#]6F9E5><)*Z44X,/UeDF0bFD9f]
%l/[7QM!]sn-iCi&MbYt[5d2Ej9_9OSZTX.@5:f7K`%#)_B(jfn'_49$43r#s?GplbObY,o8-r>H%Qq9=Xd!BnW*#C!X5)-4[ga!V
%QApT[m"jj!Pe4'^Mk[8"ngN9(A%M:Z-pl2V#o&2urm'J$BT$/<R<DH7>uYYW*H`1W4sF7,qAHSm*B3M"o00b@Z&dBBBg,))KTZjd
%lm:@WBsj/9"=j"L3nmt]k(f2W6t^_K*S&2jP5Vc&UOm_<Qb/.K'hBn3Ua+]"@s?W*+s5s[^S86Vo4jW.RM=XET6Au%ho;6LX.e,\
%Q6'E<Gf'D,reWV"iJ>fY.>q)*AV=R1>MJWFNcMXJ1#M='Lij)4?8>=5nE"f_jW.F:]Z\*8J_$V<]dNof"4pOALMA>Mf28U[B>\g?
%MNuR>IYD6*]-u5^T'J4PX96E[D^cZM&'6kB\s@$\257Jtk2g9],:?-PEF*--<d:cu[oak-/<,D:WlbD=]#8Os>M!:5>7?j+0d\0t
%3A2.V^7EFOSEO$#BVBMS@=bIbQb+@mH!?r]*GYE6YD#DBPJ/ek(:1^fe<eUt_Jf41hM]:%=Ucq"hpMiN]ZgJ8OBE.m6d1d`1>L@o
%Y4d:9fL[8;i7[reK%E>9gesk;]"_POgXM$ePHSWOeU\bBHj61F*?#9C_>D#_G1-*p,dLDL.6UqQ]0C6.mMc5;bsC'dB2SNUldnp>
%4obB@%mmkD1s:6q<CW`To(N3Z/-H_K&.!<0nRW$kd>SAMd'F/?K-P"=l7k7g[*YDM![E^:^1@j_&4Xnu`+:'`jt>AFAVF55YX2`3
%aI"Y`%tlPH[iu)\SO1EEXV_<XL]S9Fk!e:6-mkXVEGLtG:]NP-h!@SaaX#Z7=9IOkLb[QTbciQ.=,nPtTMU5l#R_`>`/GoI'Xo>E
%Y`M]Bf^AsEi_1j='"tu7g6ceI"`"mMc'0cWd;0XKAmUmb0&sDrrAL-Rkl4JSr@Xt6fa'B=G&u5fi<5b.GN-OcF8'6t2jfM*KZV>Z
%,`]R+U>F/$Ba>NV;>ggJ?CJl!hmJ=ca.fK#XjB3k[#.,#-[gpM>71h3fgJXh_jPh<%!AmDFr9pJjPEl]++7:Z=;=crS8Uaq!Kuh8
%`(N!iT#igG67Rkq*2"^?IM@1'V%K/P]dr^b*/\u6Tnr'J+5p\m=?Y&&UY?Kl:`rI@ATdbLQ(X5V_+CRK)n=6YK^R&/WVC/Vnmp6:
%=WQ<&*6aYEcm:V;Q/JPAcIKR-%;O$\![l:%fuR13M2#A'bZeW9!YLP(>f]:ZXQ@^&Ko<1!fSM@/>'2I)<7o#$a^*su#X4XY-^2@p
%ll/04/L/k6,i]$B>DGdB:)Xk`8c0(<;)M&<Yi;Z2:p353#:Cf_*/t0ncaE%!/$>%^`a"r,I2#a04(-_6Jh@j]#<QolT#:r:%M_bD
%7!9[o8Z8iP(l?(uj6oOa%Q+P>$3^n$\mY-e8RV6S$aV98/W=h$hse(F)07rcaYiXW68D+5D3@!AZ\2j%i)d4;I%HGDLCY/G5s@=T
%F+@KAAbTUN5rN\5'j,u+C^[It)&VN:a>jZLKL3rbO],BC'H/t884)4#n:C0t3?#s"X@R3%":?NT(of($!\O#q$niH/nf!r8k_H8Y
%iV:<>2So*"0`QD"q9M6b_[:]T"a4aPap9>]#<&bn#msEE,n,k:_*\'=_C"#IQ5sb^Hpc:GXAo+kgb2%%VXQ.q@<3oR3='b<[S1.n
%]1WJV2;H%h7S8GuX)aTn%\c"bOure:eSpjEfVf\W7\7EE#YA,%,:!XbW+Hkg7QiTo+Z/Dg$;u^m->2qA)Yf&Dc]!78R849?^h:d=
%VoHI%q^0r1mg=%lO9Xohjr80q/!?'aZKi4*WiD#U$/5TDISqc!NqMZMh/uUm29pT1Kg#g#W74]<e?BJ`lj$"UDUbMEG."9Qb^>#b
%["G]L$jj.A1Kbf-P8).XoJk@Wd>a3:mD6te3T/,@h\PnjZla,5d0uaO$^e0ib\)1nZN'1n?Ti9aaL,9[%85Vu]OCnelI9#B-87Df
%0+`<o/3L%B4XU@.N,1qGM.ctt]%g/#hcDpZ`:p.9''JEl5]Ps'K03bdl+K^A.=3q$kgs.SJ`OAf>5itc>\F>S^9[r\+)"A`d\BsZ
%Y9`(N#m<Bt4RQ6j9p51&Jtb'C7iVqcY`n]m`cCafq:03%OJeph*;(T&,JD;BI2;;-C_ZVanuEJ/%U%",8PBm:0%U.=f5f#J<D:7Q
%8EDaSEF!J>?Y7FFUJ'5i:ua2,j3-=UNRMY:`4%Hl)?ZWegpt7)5)](E="]7Sj8'00iDj!6\FqPZc>Zd'!&7)\(RZREd8MVao>PaC
%"ptgAPJ_K%9E!p&27\Tpee\\6)K*tKag-YrFLXrpEn!F=&hs*BVA+d3"c@71Pg`2H$!c+9VnR"2TY)&er%*<o&?RKnHcrLV#eYR<
%D(_!6<8Fq5bItZ``oYhB;[\;3I+Il?Wq#O%K_9b=1-`)"]>gfdGPqF"hthEdnajNgJ&*%)6)s6Ye%MEerG^1bd+oC6=r&Efb5)C)
%4,!g/.55Z\TXS"Z/%AN>lA)$cmdW/g]me&Sq0JaAEhBc;GHPphm2#l$G>;EdX"i&6E\cZf".Umk>XLKKr7'n68ietfCsM".H_Hn[
%"-4a1PC/1-cLW@[HPs=PL$\10lj;;`C37'1mh*2kL[a\`]gkoViD/&"FU7bk&W`cB#C]?LK?;<tchra`n#+0>[@c^/O@m>)ScUDr
%I%jN37"?#"TiKi]V']H:Zm&9[PgF*?iOWKZ`sHM$<P\ToiU'bD3]>)-`Qtg_FHE\9OTBs$s*d2JEXo@e=B\3dkpUi*kEM!J6H?H)
%k.!tFBF,\t#?$WN?T9^%4,sH)=q6J=2eA,V(M[b2*TS7&"2I\__B-)LK?=pRf&'kp9AnFoT@a+/R'+k#@A%(KV;X)(@WFpn@`Ll<
%EEhSCl=$+A@EmXr"au^5cq]M/0d#qCCTW"]o^</LYQ%j.hWT-b]Q&#afjpRWKQet`Q>A8pp-\e<s1YEtC(.FcblFZ+-;?(&@NT)%
%PBrPmF:/KRO3V9Sn<C_IIGb?%6Afuj=;t=oVj,f8_k6Rsdh@MM]pep#OXV3IraC-67qr+FiNLAP)_$(Z2c%lk/E_m1H_+fGPtY,p
%>H[triA"]90qN:;=SU*3H4[9j[W+f8fO[\-[di4Fe9a.Omsm2rlITmq='+;c6AV4'Ifqfl.dTd?Y9*XERC=a8:Ya3SZ&l2$H1/V9
%XcF^nlm;)kF&aZ.`P5P&ogrt3+,pQePr!,`*69+3VP1AnK4=X0h'Mb`ChDNs>=Z`A^KgT'h(#jh&^&%RM1`SiP!C<M54>/LGu,bQ
%fcp#b2pb*CPY3_(?DHrOLjMA*fj?<$G[6>V`M/O[GDSmU8mDU6QXfe3kE[';6B1)'8DRiUDDg/g0\<NYnOaZ_JUCNRG"o^ZF8[p?
%aQDflC\F5Q`FI:KA%f3MED`$6O,\\n%Y4=<ccCm,kt&)WQ:K1QGb@U!o.%)>jqXs5#Bpk!lllXO`94puZ59,X&NUO4[uAc"X*C18
%O[BJ\U:1f?8AR]8p4@8rkd;uAFTM4kQk/<5>loG?:(tB@mW:DtB9i+8b(h`l]>9c`U=J1Tb@`/>3!iT4l\^;%fuZc&A5/"t$7/r8
%:qHd.>)5pHZ"[I?2/^6t`2.eg9-7Tt>R3gNZI`r6Hu60:D2"YWhqi;_R,B7Z$rLYn3CC`q)kQ\''Q1@R_rmueZ\UI^5-CS:XRicT
%2H-1#a%q-@cT?HF/p??h.s:r=F`A(3s5BF5P'TYl[59C;b*7]1)brA,qA#r+^Od^RnFG%4;PI`;+r,c#3D$4Pq?\@nS;Z>K3/POJ
%$6qFs."E$+g.:C95Y"C9T.GXmK,MR)kaHMq..>NYpLaknUu*a%ePZsJ7C(kdmT-?Q!HaT*"DTJh;Ba:V@LVk7&8:<9QrCP5YY],B
%eFK>b2>1dCa;W02mS4\,GK8OI9+j!gk4W`QHf'7#=498Cd[sCu@?(*_F<U,EHuY,mH::J_GLtZ7@Vlj,/%Z6IOjkSOikl"Q0>CUM
%.CQ\MPu;Z9:q^L@YKg:JZ/ZNGKs&JL-F2Zb-g,M,HRRW0o9B.unkJ@l>J#]%PTKfHW:Z3S.Tst$$=;$+060Iu8R`SJErA=odn3Jp
%p47@P,]&Wkm<6?9SYhWeh0<6AFY+!X.M-7aP$CU4Ud0jfH/`1`6Rc)7`NCh<&T\sg\EYbh(lDZ<Br.&d*#9M^6a`8nUC,W"Uf)9%
%eRUo9##!;u&k[/KKMn$MD7/hVp\W6R^,&n"QG(f!n\^U$s+h[E^6'nZO_B@'&G+C4W"E+DDs)dl6'AtHeQh8d$0m<K$$,DPH:8;g
%l'krP[nP:5,s@O:&EYg5j0GZmI4t'h<KBG/o_a0'@e+?Wr^W^)Q:IrHNT9SokY788Sn1]X8-M_KR&:sm/1%#GpY1hoD3<stlpm@!
%,4@p'e1j!VIHW5ud=q;R8bV*C?HkS%>(X4OVgaa"#4n74(/rC;\`)2'@lLj8N_Hl+URg>3l7Ho!DdD:f`P*=K%.28Fm)\:+>c&t*
%m`W4dUrKasIB).>BE#;_j>%V768i&GTq$DkOM'_OI"e^cdS#\a'U3pob?!KJQbK"gnh&9c6@WWs3_N]/;d\c_mR^@34+m"HEN_r2
%FAqbdG:MK-fDjeV[4aE:mFA_&:!XSB50'H06h8V;".]RE1)lB65nKju,*VS61<u18BTXo<;^Y<HK/@/d8ME&c2@+'1h'<O%U2n8E
%'LHR[jk;s[9T[<l,no!(-T"e1neXE9E$^dr?4rs?P;6gI!0X%0)X#UWVZft5rjpoC9s;gjCM_k]4FO[^\njuEi<RuV_tK:MmA1G"
%,SbqMpFd8+hfT'';iIh2iqOWMiMsuN#@&$bcS)HKb`.D"=*crr&N.FFRH./lr(0lJS=iPPpCq[2Vu"$H,t]DBE=[DGg.>3Y2URG(
%b.j_0lS"!lpP,<t-N./Er?Is47@[P&lG*;YJ+lK7Ape92TtM_%b;=b4SO@&/B7Ob+0!e4UfJX&Y:90%uQN\HC*jS=,[cAH%T!=V#
%coB"g+iJ6*iXIt3@?2)gEWA]D]\<+lqu(rTh"L<Ga$4Mr?e`=lj42u^3Hh3'AosZ`K:9P`0Cb^TcC?<3`c)R7M$N>jn_e\MV$V>l
%*eZ\*/%^*Q*n3?T;9@!%XV[WV\P@)"c*5#lO"Y_KS9?U@`Fmp.T$*p^@i*H?#Ih>?>/J+<DK*:9]3M4Q-&q(_'$mh(la8R@YfCt]
%m;gfrp?\?L4CLbRh>OoKbL_+6l;!&@H$;U$lauf3%]'PZmZl;nNnsO1*Vi;(EnZZ!.![;6b17fGs(?KrL!Y_O'Hlj)iL=m/1P@pb
%0r3=_0=fL'aqX?)cN]I)/#+bt4RN^JKUs/ST<$!3Zs/s9kIm-@/8f%5'CL//+4]Q@-&B$lWdU\tPVkbQSFB48o16Z,'Hd1ARcf8]
%Z:h3U1u26BAFXF^hl#hH(C=YQ-D)P>,_noC^i!4B\AJT>grV3Rkl//UNaeKWGqHa9(#W&E,F?7]3&r:X=Cn"nE]:2@\4,Ol$::7c
%s(,[<dI+\Qgj5q;Rhp;ND^!h;Otj3TlQes,CPeFih-d2'QoN]sc?lfX$T6JVSB4_Dh2Qc*FVX,"]<i2!1[2Ofb*:Cd:>0g%=;C/>
%^CMH_AGoaX"k$!SR."']hPe"Z?ZL"kE1lU;KS-DJ?[#NZY89ubs"LPjc18PR.CZTT/1^7U\LfSC7nNBX2lkJPn=3lZAlOBNf;Z22
%X42iEikh8^[+U-.PWOFhRW"s+bc&<_K?`XaU`0Zq=je4J4g4XAr)*&8U!r'Ld((0+hg([@0sCK0i4.hP0Xn*I1jA&R<\]^3_-\Yd
%FrNWU]h*?A:SN^#e6p#)-1rP'gl]TnXL.)+W1#m&<h*R$U@Ae!D<'L%eQT$Fl*6H-m:a!\053*8?;oth]O%=cdW\h1\0;!V"5MQ7
%\\j[#aMut-%t(E4bTj"&`*#1;Ca$S7G@RGAoX7;IR?!!r0IF`NL"FjsQj47sak1cLnD,(1pHaYjkVgAkn6!s'3kT+j2@u#=9]#L%
%WS?XVkW=s:I<b$8bUn;jH/%n-_L!YKPE^rKUi3kge!kho@-*`crA(i(41?;nE)BNXI^YY]NUA7kN&+ZLm<_@G`K*U#84KD9QreBb
%Y*E<%-*-@a1<W4Ks4GFE^*6?-C9,<EMAIL>`4.4?ZH-Q=ePh#PSQ.:ZrbIX<Y>?0!)A_C[lF6(tc($'P3F=tL2&09mqMgAZTRZrkQ
%@CccGe"u;CV0j9H,$W4<Z]e8`8/\fujlbSqdZ5_Okb[JZ;'\/W@;iC"?s?o)UgUeW>6IA#"Mo[i_'DW\(\M\LUkD>("&T.9a3r7W
%R6hO@Vf6^JaMp\fhEf;\4F7>ePB"`N.<aO:rI;?P`P]4.eucZA3+^BYdfmm_@%'rA#Kc?h0"@1ID*-7Z])Gp;:Y*U%NN1QFCGhD]
%fU9!"ARp1V\$@a5m&ZP=V$Rqp_C+!TgFZWEa^rKU;*`XH"=YhaD#(p>g/Q<PVYNatE,fY057/0^&BY4aU)JS^)G7tN+=B1,oqKp1
%?$"R+'<E&/@-A1kNXWSA^t7-$3`<rP$LVDVb^hsPU+k^,Y'.dtg2@KP0.[pJk0DS7lXf(n7t[,biMd^@bSSB[MoLtVPEG1WL=OE5
%$]"7tnkma4CAn2"Niu$X=O`8,MHHo0?$kEDmlS9ArhaTu>HN\RHF@;Smu+,DZ#8n-kCAP'Y*Rgr:O@J4R:g'%J_?2M>M#4J1!3tT
%TuMK%GH.opC8`VTbH0Yq;\c7":uftPWG:Rc1OOI;b_D'\b8G208\=V0EUUbDoJWJ.i:%G5VeCQKodf%i6u&f8#+r%02DkBb@uSUB
%H]Q,XA3$?`Ajkns277X&i#;R4.&ZrM`BQ%qn4WcN^UO.$96'b'7HK3Sj';8:<Yjks%qJd_;m7Daj7l:=GA*9QblXX85r<A56C8Hl
%Uqe%a5jm4IGFD`>o1K<CksmJmWECk^6DK?$=*%.r6Yr^+:CVcAO'3")B>JUnfXu"qWAR2RRK^ei#]a4XSCO(;>"s]c/l\%.B!Ui6
%4B)EWk-ZNl1":HVgl,]C1e32\C"-CIC1\H_7PuT,9=k7<d?MCcjDaIO)Gu\5ST]'7mUUC7rSn^H;iMV4Q_8&\j$W!?p2qj%.\5.i
%Q@W52A"Mi(l8_USNhDg\]YN]CL,XkhN)%]+F$fX^WP'V>Ve]l,.'PUp5*[S=j`L+^*!%ZW4/d.(gNH_l717c5'Eg9(o[IUO!\J#u
%ghiJ*G+$=X/i)Tsc"RPCdcGDj0\tu@aV1[t*Qu5Gb'`3I%'Vckr][QX&lQs,N%afi61-5]TOM#_UlEC9Ls9fM%P(T3kJ?dacn[&c
%3g6Fd&BDi>K\b.6:&IgnRcDiLZg"'<mWE8d8Mfq*Zf+j"8d@M^>YTL=K:^3//NTiPojrPcj3De-fAsq/V:(WiCQuld"0Pu7BqMk]
%U^eSk0Lk,,g%K]L.G\6+q0.rCorhEV,$X3smHk+1Z:1su;(c'(N1skV1M^n"CJ'W3<EYu.$!Xl5m-t!jRoaZI=pY$hA_-`NXG#E@
%Qcsb_H'KpTpQ/MG0<Z:t)`\osG@XI2]3<o`l#S$9fr$f;/_d'QgXIk=9`_X'C:l+?:289!7;&+RhS.QRh3>*`jK(H_^4%>f>\C3E
%k,uWh=Y&b)29f7f,Y-rOl]u/>)L3[aPjrbK6moE"FRdFE\k%F0&MZAfb`8W)FUmp;;JY!;AAgt.A$M:7C$OJ?4$#"b'WtqY0,?RK
%;6pP-aHD0pJ)-*reC`KP[^N4H"-Q=fG)r!R7gn]ter<Wb3g!FDY4NlW%[]/m\e&XD-]7mm/SNcsNYW9la!hb.AdlLMXW@=^-$JnK
%jXg0;XO0+`\p,)Y0='\i2DGK7MZ!fN+618-7#rALAr:Rab*fV?Nq>1OK@uS*4JT[r\K[Je?kV3mT"&*j#eFoZd5jm2)jt`Zm,6)H
%0e$mBh2H/50._P'E3<7pXuE`RqXiN<]sL57Dc4bL,,T.<0YorRJ"'XZcV=*#AA-ikfj=NVDsS7S8Q$N+*OB'VIcFiM>t+jc(O"3$
%=[e\=0Zf]!-/6_NAbkdH1Oc<Cs,#u@OTFIB=($E"hiTp%]H9=>_=tBWWr#7j#T-Xk1bBq),i3!fUmbdFk"(ER"Mg9N$]3+eIF[=-
%'L&MC8VP`t[FT)=l\RK#!G2K;dba@W]doA;,]X!tX@X<h!H=0V1Jl!lhe1a*7cT]fBri@!p&nCC7?;Je1#hQq:?+mXZVUcjVs&XE
%U&</2.UqoDQ4u3oN13\oPe+RFATd0m5`*%1)hk\u1JPcDe?L^GD`,/+q'tde]WC8NjmC6.b(P4G`4R-c[\Ws9KK<6-lc$^dg`d)+
%@oqf7MVI3K,%&4reqM^[UK`KIqd@kK1-Tm%"-*j-d/N/e=`7omAZY6SQG&m>:CO=$j%/=A)`+O1ip+4DGMRJRGLrZn#bf7Xe)fkT
%9?BH(p44eO`2H2mW7`WD00O-DbH+hk9@h1K2.t)N?Xf.oC_jodX/<eJHs,c/=f4uC/BW<JQK;Ol*]E5faHEsPO#:(K<uJ7K'#2\K
%F__oZ=>"r78[fSA)T\;G/RQVR[B[Kl?XN:Z?gomZn2+MA1gf`/?gomZn2+MA1gf`/?gomZn2+MA1gf`/?gonEmHY.OjpRdC$_HM0
%C]&d^q1>O?Ju?-]:NP7+e(feYJK!!Z+j7[Nm%J1F\=,"T-B#';[]M-`CPo?a0fFop;4NUiD8ak7g6,Xo#3JBP2nU'0i,06S'4mo/
%iGVhj!\IQNhG,hG?/-;9"cj=-d4B2FeXZ>=<j:?p8dM!`VGOrN<--f$j<d^:pJ^PT/:u^*98Dlf+ZP>/au:l"gZsUb,/\iU$\U(W
%rPbjdCfMN_OQ#^p=Wj/_6`b`[!SO"'r]&U1i$B\:G^5LnYQFZT#@^d;:Q*HNF+4#^qQcq&@<J?"mB*e*5^.%r]tNGJQotT@],tNs
%GT[A"g4:O,rLC.gZms*j=m'KWr"C5lhLG:=[BajLPY&i<Q?sg]apO7gDSc)YcDU]=`]Ln(<c(9PX_jSMe^Zu>>1u/qQ#=$`WZ?"[
%72Puu4'W8r`&EPqS!2d#`+h9h)KT4emU[cn4Yq-MN9r70M.V)nN?8s!,(nOAg#\;?>Q43/?17^ar>3U$<EMAIL>'
%27[;]YBNMpICCon;p4ULPatuH@X:iG,Y[)9W'9_fgjUr_mi&bj&-?c7n*F(%pP,3m?F./S>]H7LG/*1H=2VT\VJjtpI!1>PgoG-Z
%o@Q>8hLueO;maO.1-DkQDsn"h\N+n]KCI`)W5d@>_](8WN&o`],Hf/j&\:@]?LpDKTbs[ORF0o!%]aX#-9tnLrA%Rcf(dAQAQU)C
%qh_omHPYopPd2lOaa%h#mqqmq;WF)^^XW&SC]$_.nU$D;TZ:qKKAML[B8J6IiJUbCA&B.Ip:d0kG;m"CU5^P5Yp,4INrSUbqX0)4
%Nq[+NpI4Z1SOBWR:JRloqipMi9'.Vss(H+1YM\308EFF.pMJLU/pQ3cjM.2])sD&%q$r2Q%1_jMs/q/\b5kE6J.K<1@J9s2b61&g
%>R/:A4a/kZg#.)F_4.oEpe)=IFS--!KnR.%eg?gRmQ2E^mr)?4ccN0s2;X`AIXQ^`qepCTh7<*MGHY8rrr2m:nb5AIVlRo2/(er1
%VB:!.l$N3t/OFjjUCOnEi7)n9>f3QP[/,_`T/#Z9S"RbYEd^E\O'Qp8+P8%UqZ=kL"nn;?6*r:bm7VQu'&c%3qm=AS0+mYWjSOmb
%?_cO,1d'Ua353\-Z;icj$[.)-h4Wr?3;U2(h3-o0pVna=pWHI;QF\Z4P]M]2^K11ob$hjnlgAf+It8NiOTM.W(3ahIQELJko?,\C
%.N]D[E(>!P`AYX<P2h!'%bIEp@"`-XZFM!'*UU)F++,I$!GYlr'V6(bR0E@u@CfZ05Sf`WYe38LrsnQrNf.p[Q+k*Eo`_4+)*+s]
%TNK32=L!4mQR-OlhUg>Zkik]k4eUnsT5kO/Yq?\GU79?hZLsLN7<7`03A/9g\QA3-IC-UHV_\'uf&\Mglh/-hI=BJ)(]NgJ;XRKD
%c:p3XRh%[BXYHK(AA17)pJ?t0>YfT!N-ef;Dq0Zq`q8>?gUZn;'C1d6^Q1+T-,KVnbFj.NLI$b`9$4<s>rJs?`n'?s)uS\V)Z8(9
%3Do:D<lMQ@^ii;KieMkVTi#s;i-X<g:#s]u5IUV$M^p2i)O?GcJ6W'K0hq>I9MMOZ4>YB5Q<B(4'5#2G:Ri#qi0.aUS,)[_Tj$<:
%p_kPfrUeA#T>,gEqV@(]IsCU!:5D(5jl(iaIRAU#r3t_@n%Sb\c_%pup\aFELC_-!e)u4pOal3^+$=X(#@%JJq=DK/hr-4pY.4>Q
%?gqG&)h.ZCT`+[5h>H?rTDumdHju_CbqC,%Ggp\Ql,P=Q=7%l\/ECpEKLe9k:>LW)FSa@J7:#Ddf@]T=E)%lhJ;&VWC&gO%I`_*J
%LdP6jE1'a)%5cuan%J#Y92>HJ2c#!A\so<Se=JP]!IT4]D"E^/9k!<dK@P5P?)i-)<A@%Z+4T-hUGt*>eJkTCOuIMiV+VP![^D.(
%@"J'DYoDqI/1kOfK,u*N&1G#+EfD^E4LnAi:=DABH4Ff@FH;JEbfTRtY0j);Fjh"Pdr^+OPl8%)rto>(D%kOb;U;6)6/N)qH&QsC
%C^3'eDL$/.YL02<c%$?$VWn'sW_;*DgZ'."FsUFj90JcA#9B94L[Qq#s*9"#af>"_DdQl9Ruse59Cl`A9D7U&H@_C1[VpB:*UNh%
%*P%foku0`1Dopg,a*snkXlgUNN5P+HS+69-MTdMFTB#GLd&QP_;r9uMoP:Ji)L^-,Hi6m$Y"Tq"VVC,NMr.PmkfYT<HEPXc4jnG$
%>^]6!\'Aq>p?a8'oOX@Y4kJne[g';&;V3lJ+(s58gSP4(fu0iP)L^(V!59Mt[d!;6\!FKt&,NSg;9q,rVJ0FZ/aa$,S=6kR%/?JK
%Ri(g'UMlS*3N)_^C"gJP"_[UR`shgMM,DSVqRUCu*Z%Yg[S>Ao4kK(O:\<bG+8&m6Djmhi&[8f_\i`G<c*lk&?T'Y(\OPHb3&'Yt
%ic_L%j,dO%cLmh`#BnU<NP9*&++EnP_668J"Mo1/@bscM%%fiX7/l*77fEh?n;#M=NBX@eZE9B<;*4eAY=RrA9#f[M:'n9>IL-f3
%pS,M3rJ!^J9>2)UL,s38,U2!d1,HWF.sT&k^q06=W4_=T54%7!+u]`?)L<\RLc7=.5-Eh6$W;T^#n:g_TsXk_.Gca5"^VHH1b!Yd
%M>hK#eT5Y%Ksmcf*QGRFOHHQ@jrBo15#dh=HAMK6RrN\]3&9GEIJ049!8UM+4fBp`+>&i%h190*8=h>aO*9c_j!UN)ODD/9-&cqR
%@3^Bl$<,8h)2_$#k`1oHn:!<EMAIL>+ct#t$kAbP;8Eh>Oj2^pH\ia,<L_@E3<Jn!DP"]tWgo'X$hsA)W1Y[D,$KT9<$<$uJ
%[;LKo(d[dfpCSb7V9qkOJZ_LFj"HZ>JVl'h.XWH)e!o&u)U!c`R2e+]6+d>'7p-lQA"['O]tdr'`q#d2a-n.?QgAkj7,JUjWL(1q
%C&M'D^TcjaN]-AKcRj<YHe.6-a4t.Af8,'NGUGd?BunplGM,E^HXCmV'\DPkdc2*j'VanW19pToUaJFZGO4o_3b[Eu(.!8+C!\ZA
%$E=?f]af@-`/B0fd:OTg#P9#[_D=Le,q0G<GL@C7X>j7Rcm@c%:NUeg`/W>aH3_eAr)7onI?hc#O.r7481!J=j:ZPE*6qh*eTk%B
%J0'"QeN%lAa="EC;or8T$#*1Wn^g/:&Vh=9Vm(aaA\q\E?&13(66,rScUX-`)@?TnN-\DG^/ORWo%"lK.o6$LC4UpeK+<Z'OTgOJ
%Z*7WnAQ?Su.PWaNW\>5@fNO(ZON<s4e?.j5$d*alq`71lT^Ae"4bt"R3q+!,e'QH*`LGcV,k35Ib`/+p8Xm<s&deJA)%+`sP8[';
%^iP>e@E>AY)n`Zl;pct6,b?OYSn1]q=LG\;Z>;1_A0E9G7dn"`!QGeMP_Y:C)Z7bAg5s8jSX76e<Sg**IiCN#O[*%=d&TTr)4#bk
%7[PP*qs5Wu3',]^qKZ0r\5S)&oO6QS'tRV5&F&UD,,X^K7Ysm!)<_57E[IYWk5l4$B<D_43,N2#,hR(rr7&94k#/=%ZFJCqi^!\T
%EWXE5"tp0#hYI4"1FOJ1^:r>_hK%FTeL?1_F2oJol'<L_M<8akPkb;BZY0IH$bR%a-4iJ]$VU]NL%Zg-Pu9CpO6l2./\4X"#7ddg
%/9sdI+ikfBZYiO:-V&SK%0him@rYt5!BGo.4m8M%B<`8,g7f-E-P[gWeW["M+ZhD>c0lL[?Yjh!ITC>p:fs:j.4u,l]Vla>FA.3e
%\L3fqCE[:9(-E"#WmBbQ`LPH_42C$-'sS^AAP>6qp<c_bi8')T(e#eF_!;5]8]3[>#2ilNl%r.DN<T&i[M1jg9,9REW-u#i8e=^u
%i]8p-pX*dnY/o,ek^uE^-$1;2&c*F;a-0+'h6HI$P8E8H"W+LGZC#j[U*JYr/T%T5-M;Rfe,Dh'1$ph4@$/Jo,0dmp'Q8dN!Pcce
%4d83J#Tdm?N[^V[#MYK7&$=P:^sRb8R-59N``U=l\Lql23bFbHOZtXK`^Rj73`q6n\PuQ?7QO_7R#V@c-ISV5Id'/OdMbFZ9(Z]E
%5`,mi1nCHDh!-k(<+u=5>`#[[Kp]L[o.Y,#!QJW3)]s(0*"tOFqFfe""]RYRP$6?(Y^<5sT^Y/#\'6A"Qm@1.iFP3a04Kj@h3]tF
%a]<ubb"N[#YE2-X7,(c\p.pZAFNO4kcpc#CH>Hn/:9;861$LlQ)OqTf*E(Gn=MK\-#+BlcelY=u0\1k30^il!9\/uED+R`m@6*.a
%?=D!p*)u#ceO3A:H>kS"&lZ\X)7?4*KPuGlfp,['W.)9fhJ>U+Ym=:*l$5Zdj'k[qfN/V.i[:*n_;aZ^6e)9qbi4=l81[5NF#a.m
%KW361;j/+r+sZ'@MkKWsqAt!LnV>"3h<^0>8^AtjJ8c^To>,Y$@%?QgEj>Bhg7h(L0s]T$6"q!@"QTe"#%OcL#XmJi#">Xd%58W%
%,h4M6-N*.B#+I1+PLYacD$.?O%8N?bhMW#sI#fIL[GI_j`HC:!:pZ[_=L/YAdkfb<iPrI)/]V_%@ES<4G'lsg,O%),%\gl=PtKlO
%7I_RPE2Ipbn0R?C9O$VTNq0_C$1_hF\ljQD)9/&(\]<c>E^,\l;13.^".tVC0$]r*!-EJ?^^h+-k;efu!nEHnKo'btML1=@XOTQW
%&:+^cA%:3?jhFK9X^jJ%@0"Dcr^uDl<6d.g;,Vd0$TW@47oT\!\uc\2o<ce)^&_#Ij<qGAk>h3?d8d;L(//1S5\^9[RpY[D->NfT
%0m=L2dY!rY]:=_&Ucd*E6<2VXS8J1C@K.$;'=tpRIDon$j%r!ce/C7*o?r@'j><1`)G's,LG%LF/oE.:F<@c7LtP`Xb^"Z>4>"T!
%FCqbq(]kX5J/EFm]Ia5a[Y\>8FOhqGk)ajdS@oCN]W)SP*jSJ`YS<^DLLUjdl/_,>@]?fB:q?AEQ++1&JThe[!e)ABqiu2-m7_"%
%K8I1h)p<EK>qrN5&I64M6^N7S'q+a4NTV6A#E"TS;s7[>,;*Y'+(QKU"bj^d_'qqbTX[B5JtO[`9Z#jHI5/qr`,o1U]5RRN>8aZg
%EfkZ=!.:X8Ji5,_k\RdQJPs+8Q:2[>fRC5aaah$N1>/UUPf.Z!%<)beOWbq<SLVSEa^>5$L>&ER:fWC.LWL[WeH-[)P82j5L^6=j
%;IVM;\+7'PoVBM6YSh`=OhenuDPQ5-LYFoFQCIC^(,Aid&rCK[=[(DF+cEIrTEi5o23HEq%m'4i/%-X9_WOJ(@3TBUWaqc?(58>`
%=(@NA0OSG9McI-#Cq41*Osk*''N_PXSu/E!F-Rh:Xe]_A*Z#4s.pB2J%6.;ISA6CqWg'i'N5dodkAA5Tc@/a#6#BGt\[CCp!Ynrt
%%*,r"+D\At\Jk[l/4>GN&ZKu;3!5Z*f\iJ7_a4`:,kpSP(Lh3k19p(M,<FN"\SkIn:/l>K&BQ+E*V5t\jGlpMC/DK#,8YZgJCcK;
%/81)X&f_67gfluOnXK&o3=R"q?4lLm)lp$,<>u`5CMYt!.>b1;Ws!?a;g>V.PL"@?I4sSh,B\k9g'Ghb'1q(l,-1lD-1/>jH>uCM
%^dfVrmQqW"Ga-S],-^T*gY+u8FHa^P1\[Bp(]K>P7HX8.6I#^dn[Kln@_+$r\@-I[]H$1OiZ@D8LUSqS(=_"E+U2C1*0FB+LubP$
%H*+eR[CCL`#)=OIYla)<2,oo)f+:bOQVhP2PX47Xk"EOG6/1<9W_T+gPpuqi)VJ;Rhuh"m*rSCHXAPJ7R26Gi9/2aerpsgKbq^8L
%S7<aVMF<EIU403TPPEC'.ho"N=ErcNn1S6/<Z?r.BaNF>kK^0P&!'_%f/gMX`"muccpA(%Em`TWN%3+J=2pGm$j[khlln\Ngg@4s
%on$hO7@np8.%.iuO-!8Wp;N>^gNAl6-L*A,XW4'S<]KhQ*lcJ.RU,]JH7f^u>)QKMEb^plS^3)0j'm1'Oq7<o0+V]MC!^dcECYik
%HBC'>fL[Ef-,3a1_:\AMQ%(m_X.27';\YBh2=h)V$UUcibA.`N+5-_J1`^,0V&.g9fhFr:kaIhAm".Dp#<%M>?FW0IGDTHFoZ#5D
%R5>_W]:cg,PSL3!>rtHuZ<9`mH_r[-d#-?"`I\+q5OtjDSk'oXk\3.V=$`&'ZRb(R$gBbLO"CZ;#AJnB)-dU;9oWt1Le&<%+[4+*
%D=+E_4^ne2GN*?]M/1aMfuAnE05$)(Zs@cs;ZOKY[@I>j'1J9*#8;Mo>7K<!C7B#s22]Bl]@[/C9iD`*:3U%3,f)>MpWPLIL*;1=
%dSVA[2UJ1!dodnabEN;&-mFWDX%922%S1D.r(DkUF?BbJ9XN$TR+[?^h@P[_Q7$?7Ku]jjYg5^HlCuGNa]>5/K4:%'!?B+nMOY^*
%?Q@F*`Yo[62E3DALQn:NqVq*3,F0:kCXeBd\VSC^2s2r"PrasnkF#dg@*]9Sl8js8DnA-"UUd8K?<,Lr7,S:sdd\]N-a*_C2=NSk
%<1^)&<;aVs,LD4@l?T>`2*O9FSVeSH7bm@7U;E\g^bpPI1c9>#JKb(K3H]_uS6-;,l093!cOC50<?JYa"3Y[X7-.o\V+VQY6C-)h
%C++s/9iaW%UiggB_2V:l:Cn1E7*.eU==,O0C$*AoQrFI3k2R#Pn6-!hGJPSU(rPJM+KGARLab#!.^;\B9Nc7DB,,PKl:I,=2]mli
%&i_.&hNrWf-U$12FTQb0':]^69`1*F<`'r.,eJjlZ<skKJYo)n`+E!h?BfA%C4U4R%i8esSu#Y?$aa0[n?NW.%3d3mRg?4K70+p^
%//N>j@NUb59:M/mM^RCAA_>RhFKltI3E:>,6o9\aiT7b5,d@83L7O?LTo5k9<;2*3<a/O!?Ai.H_RD]<]+[T+)KjY"gq,K2+XX0K
%JLmW!X4&Xc-8,r<K5Mn(7uii]c75XE>1G2)lb7]dajurg71M61EHFU)onO[l\@n:ZG?)%r'D3ogT(#=[j2`AoL*!7jE:E]X6%5Z>
%U2"oW"PD^j7JVcuLVo_9R>27^52(B\/r:g0/o,O$%U1tC,>.SaE=@1$_U/WI,=5-sQ&4c7-f(^OmmFqI=@C4k4pkhOEMs<75b]BK
%OS`%to8!QsYhHrX57EaTb,j-7jqr-U:I\BFJ_er"&VrQoM1\0TX/PUO._Z<5fn(6ggl6c=$.":t7Hm028V^Te/):`\0,Q`Pm)CsM
%AC9<q_SaEMI3K.Je"_/\6-n&7m:4aLY$H1jgo?l`D86VMmDQTgbq"k8_`JIVaHY4:VVVGTcU+-s=g=0uq-g[,WgSjHD)KFl?9oGM
%B,mfEP_Y`r-%&YK(2;IWgYA6foXPhN/T@qUSFiiG,dug^3:t5F&=&hlA9]?e?5n&B)WQ8i_l43J&O@PF@FlaSNB'?(hMYg+<m2dG
%_l/,CV`6Xl*DL?^<-]+ra*S[GXVPIpWOB%4?ilXEhpAg8LN6uG(s-V`PUpjV2K;q4p;htOMpXDOl@`1[Ogbq9fCVmW?*2AJokE%\
%)b&+AR_A98@P']%+mEb9/rKh]i>H:GY#njl5U!r`eO'1tWc'b-=i7HU]aQtBGTp(Ll%hD*-bKp-WfZ7+9Nf@_2l?_U(GBO_=dJ#`
%?K6M>//&1`EIP_Naa(tY<BLcPV:0kO`@4/9cA%n,=Qj1T6qs+"1h^_C0CdEJ=9p%4<'#b>A9J[q8F[*<eV_.;TM$"U$.E=8%`b"D
%Ye;!9`lAj:[t)t9K.[cLY2CAGIY7N^qHsB+Z^$Er=R]hII;\&TRABfn\nXFJWT/9#lnME0<t?DFS+$C8ju<=$WJ0i88>enYI(WTq
%J=,R4H'Hc;HG(G^BH]TdO':`N2`.Rh-o@(iJ2eeO9q#Q;84<W>Ch)O25nJ#LeeOU>TI-@eP4]r;;5$mbNo6W!jm>=sZG&=%Iqd/i
%qg`o/5t/WPm*<lV(9jg;?RCbh?*PS!WMrihiYE&ni)VsC2!)j99dKj%frtTt`<%HV`^:ELQ<JT_d23+9[5_"#AV+pd*-m/K+C%Rb
%Bemq46.r)D0GuW=nJn7;-E&=GXqRlpZEMt0%Hl,]@e_]=:_AkW9U$>.*C5,5b<XE44-aTR-"E0mHkZFLYH6(1l/J.R(qQ@WES/ZJ
%:maA`\394f)bK\kA-#%E-bJ-fR!3_EQj:G^\/>)iK"-$il`&]`(7O]$P1?.62_i]S38>i5%ts\_AYb=Gc#n_^+Dr;@1I4Yaj-pT5
%>+,+.N44/7+Y"'6@BC]AC?HK;#2!/@"L$\s1Pq6iK"40Vmc)tFL,Nd;C+tLiHniFP`AZ`H+L:Re+q+-G6YFk-Z<FLfUVXMi%O2_k
%aChC`*l2N+*3*(M!uVPS.NS,+nbGSnJ.,Um*hJ<83K^_JDj-!3#&ZG4"39.eLBVUV7_#*$XM>\/jqu-%C?St4<N8&e+*nP-RK5^c
%)++7;D8`n3$KEOcr<`7<<$'>VBd'J]PF-sp6,G#=KJSZp0&iW8=s_k!=EY]-&6tB)6RuAM+ac`N-D!4b-33pb72IgPL_>M:+?q$T
%Ws=]_i9Jr1@RHg;8N\!\EKDn#o,a)@TF!p6BnkQsQ=OK+=oZ!9q/i+-PMRg!)g=n(IV`j<AYD%?G'hd&EW)qOKf3:EGbW\*HQe:^
%06QBuGd?D-6_6/?^tFktAnZfL9S:h<(Q=PgeWD1=_/m+59=3N,>I'08.FoW"X'(t?Fg;/iIN-m@ZghJ+`Qa4r=SWZtbatDo#bNZ*
%Tf1Gk`8jJ0\OfH.H)Z?oZbb+Sarci8L"ZF7>8/@$`$2'ccPUci6]dt'3"usX8Td>[)44Q[3!@*,[qs,S"3'pd-;,QZR:nrOku4R]
%3a[eD/gA+BpqhX<$`m%T0rXC[(#!QGT]!]!\KT=J_n_j938DWjWXh&.W&#$?=q4G!*i"QW!XLjQWofNXH'go^fG0It[]5KunTpSC
%>c"nDlnJuk<k=c&A9`&Y/J-0bOVE1$-FM:a2Ylo0.M7r"5d2na2UriK]XorAN8.K#NfZiX:iJd0f;A@&"=d+"=O,O"o8/#-TrD[#
%J;5OK@'q=5$%<_Ff><K<U)C9CL_S"I`72/KJs\EZfGqdhld4eA@G$g8.C]f"Men#3m[m)nD\3r#$,TM!!X?t"OJe!jl52q.B(208
%8L4Nf2Oi.,1'1U,9)OmuBCS8f%WrLY,SL]8b1^:C-(tXP)"4LBZ6Ob7,e9eY>h`@o?),qh,]Oin+X](ELdDs&D;G"iW5F=M"t`aj
%81QR,F^d"#m:Oo<ZOEWfDr3BZ"-8b!>JsZV9tCMZ6"P8+-qr<JU'H<Sbn4MI*1l#'G`Frd9(YRma/29>:#ZYoKm?fF3tZf>^'+b4
%[BNc6h6=\>!!ZF2`E@c0Y)nd*rjLALk2/Z9L!#OU79V(TNWE.f)&u94:R&"$<e;Y>Z!o!u1PiGnFWra!U<"0]&;UtI]-7L5V+KrJ
%m&J+4Q'[<<hpI/oZIVis>50dHhYPTf$DXsp7.npeS@h5ueY9b3`,n;VnIq`XY=MYs%n7p1,TNf75p)1A(AkTSYt91;,\RZr191\:
%BSPK;i$4B.s(Dj.p@Ui<gi(SdL$I;\9YYiUn(H1H4(3^leIjU5TP$Rb9%7Mu$"f9J"C=jASYsP>P`h43pILo1HAk;"-o.\;]s)(^
%$>SXakR:e7i##kG#+MJ&*4a4f-YP;TiYa&_fb:3?XdeN\kFbBp%G-6qMERRY\mQcA0GV8:HBJEfQo/X\^.YG[C!S-f()D=j.(r#+
%Llq%<o\(l,'<u7<[qJ1"*CW@'JsDZ,1(K%1qOf%$@?5^#3C(=o%]4KFFMTIZf.1#9CK@*QML!QK-M(_@J3fU%a>doY'"F3.4Bg^>
%-U82K$t_1!em^Rp(%&O\:\:aZBgD0ed0U3)40o1og$(NEe@iih%X,gc5_@)q\0kgJ=1X(ii(=N[77G&,R[0qc9POk"hco\3FF8;M
%rDII9eCXA@D+0jf1b0U6RZm9OAo],B<LI^'_`i)P.hfn!676c\)?sOaqNEd"EN0A>BAH9$3]Y?>cF38c0uXPba9L&2+P3PSZ";Yn
%;i=!"oE;!?gBH5=Bml$`G]t8eJXhQbKW.n(^2]5_``WPSP*59EdhAEnHI<[poL.#^3b_YO]dM?gfkJW.k.F1.O:u!H0GObS&)3[(
%IGL)>fWus6*Xj3rU,1^0h1a,!_Ht^uc8KOI;-L]6SM="<VC.4oYP.C8dFg$r<_EN`ji"=f6m.nc<BDPmP@_#]K)mIF-F\]WahDH\
%=G+AH?9(Du;pR<^"ptid=5_GgNZpO=+M1)Xo5X*.'(KpLU-ra0%<a?\EI09Q/rCFBkX7H.N"mHn>8UY0ga/GYJb*honuCi4>[5+F
%SMB'dSF?kdA_i4=C=Td<;#b-u#^s$S,Wj$[jfT6H]qV9Q6uu%(oLbL&+WDI0O(^[+Q<%`3lW7W'pp0$JB2db[#)=>`7^-GZ"L/!M
%7>909c\=0VJPhP<`]O[@`<9ZfIirjk%`q4!//XqpN*%qQ[2pQJ!^kgAr*X"h&6nOY&Wu.D3GAppLGKT<CEko+NeQ^lF@hgD=BScA
%0[WjPGT,-jPO"u;mul(rb2,V.BQtd\!lgseb`A2s7$(o71T#ZKQPjKkkUQ:CVO@[Zr",0NJ^3C>l#T"Q\^,DEBT0bc3e^6=h88_Z
%3@-(bW"LjO7)@(O%,/1D#tCcr/aFIt0/,*Z)BD+sX\\SZ$/LM!_I?pfPRiC4J8,p?*0\Vi^mlb[U!T,X;J8G1MiRIhfPXslSt_=O
%;/sEMA3XLk.0*Fd;_A\h'OSh\$V"DZQe:#;SK8>t32\S)Ohe//nge9H0L/$H`j+L=ahk-;!AXGN`QAh5FZRO!UG^Tc3]1cAL`3=E
%go0;>5`]76TrYgiV'e`>[>:8?hs$i^6fB/0USe`YDC36Gc=323KB/5eM24$Bak\M!h.-jH;\MUeZTV%A5*#T9C`T(0'+B1Tm<)$4
%7Xe70#>$]a0X@&`C/b/Fdhi3mJ7/Sk44Y)/iKV!'4u;dGf,k[JhG-JE"Ia2097.=Dc'E\ibpJEJ@<p"6BB^,WdcdFRbEQ`aE`bJ.
%@3dN`GWQlVNK=hU6Q6MTl$6gO&76t-(KL/UH>O@5j7=IC6f=/gq)Fg:ND?-+[KT:''$<DR#^MD<AH-our&L4FU/.O3A70!o>RscM
%[cg@s3e<aEYDsFr0V)7(d4'VtX^nVCpo(Gi#jG!:k+\M.?@aP"0ea))'A-V+/Ao_a54&?OLNbtneDlea5iRdMr[6rYY^:c[;%QRP
%Sl"uM=m:@s$D-+.P4,f8hb`l<Y%3Z*J]sI?iNRH"`Y#nSS_;rg`4C5*pnH]G<r(YZ*3Eio#oj4<\?D#g\8Z=*Y,')lQ34+[I/`7h
%d2e!4&9c6IEY;Z4K/6o/Pc'H@'JYo!_:#^N9DR5\`Z]^=&,VU=_K!k$JMW9jXCr9@#SP9F@?:c#UG+@M<,J1WRs^S5k:qRE99s(b
%"FZRGEoq9BBgf870YJ>X8iT<.JiNXH!H;u]!k;-E`o":\`$7,/2kl?A0O=pb*>2g\7LkNqakM%mU-ZnI-@QL/_tJn;:7d)k`27)O
%^7YLed;+sf"_BTP_;Xl5lp0hKCI@(&"qh\4\o3cQVnij^f?D9cCEmdLQ/q7nmRijLAtt<9EE]-j`U"kh?r7WA9_r/G"ER!F06MjE
%ffH1J$#[ENO\s)=Z7=E=-\,[c"<rlr=SrrJmI^oC\l"((S:o327u82cl::>:@GhZgF_Mt7'<="gKEg$Sp<i@QX5:>0)+[jh`iKm:
%R%XOoN&J$;a<4,W+\Ba,3UdGu(<%.):C43qGTb%nS6[J*B]pjj>'^c0Lr.'r*@MFGMbMM\LkM_%DJ;3P_Agj9^u@Z*I6]G6*s)&Z
%P0&8+doBrGRRbQ\fgsUIpCmhtTdcC`-j</7$pjuH;o`k!q'(GYrr)q:i8]o<Ig0g.r6.`e(PG\r@!p-lIE3W1,SQ@qKSoFf4E"((
%OP[Q"]U"5@!RgHU-9QS..?'To6P)tTdOljY!_Aq)c:6D3G*pLu_X^?d-neYS3l!79-Ygp8';TF,>WIfb4!lXsgd8Jk(Q":VWgeC-
%OI</?@DY(!7]K$m(!VYG31J_P86'@KV/06)<L^lTaoIf5U)Jnlcd@Q;1"N6'42[.DVUf:):".Khk";p0,qVo`0uhjuVAZsNZQ<*>
%@pud=.r;=BDPG`CRgL52,tUS_0%j2djm^YC;[SZ./.SnXWR$FO"Hc*_29e"^dDdE_($bR[Z?M2L6CB_IGmb##NL[G=VVOQ]"ddn%
%/`(^[cS7-gIN9,Fi%-/+=Wd,i^l`/NMTGU+W^sVG`LKg_\Oohb52,!QH_85V<"93P$!KK]*Z;qfX6#"C$nYXB'o@ZWEXUGjk24/9
%F2=C@Of6TH;,bb?9B;HK83kIkB50Vl5*4ejEZ?:(pB(!nZHP#aUS'1'*0*Xj,Se'+P\ZCqL+Uf)1<&0DaWXjNW%'oj`3EGSk`8aU
%Ja&(TbtggKB+i'F9hB(JO[A0B)ca6(qP$M%%:;MH>+.nE%;6W2cP:nGM0KB.oA#,`/Ou[Ka00Q+REHRT.ROt.;%CR!<fZe;Rbb7r
%%K7lHK7]CaPpjde;6QAL->W2O-6*7h:l<-jP:0SH%HS!HFeXfYYSr=n8@U;tfS`3R1;\cQe6/Ri*kUe6`>Qi'oKeUuXfe,kgPm,W
%6FbENRB!iY&Wk)4<")*lP!K<6ZQqhSP[F3p&&jn"`KltGs"$XubiWV]pXLn7bj[=ADoFihkh=3KCu:M^462VALOYZ+B_=K#Z/\au
%-h2?RaVeJ-k/r]!hT5oX@*"%t7)1?H`V_Ytd6F"sYB+HP3eAt>T+'^HWa3jIjS^%sFq,5N=jVWaV;T9=0PIk<p%aAXM%<n?>X,W!
%/tO]*)er,f-#EV_Z6oLdO9sDmm4Ba^/57_lS!b]!1SAfV@)LL!e;L"".3FZ1%O=+k`p?_.#UF?#^,2Bi%ABbk[8gfpWq.(:9GL&H
%/jIU?_dSbA9?]%^q52EsW4?/r(a3Sj##N'-l86^m_.S.ZE#dV;&rK/(>cUI_P-c.`qiW&;NE7VM->E`\_CW#HH'']2=2c\(&d[Rb
%$kf`&OBD.7^h6u%%(Q!k$4Hgr!ds3p*.T%_ibZNrc5*4n'`Kr%b>C!^6oVl'&0.8tQPd;<3,Mf82PK=douSBfEZ@/<!h^8^-_!jj
%[Z_[Rr&N&:]'S24AR+kEMkG@$bS]d$%Q\ukP-SaH;.')0.Bd(-i+I%<pT/=-aoU-Oh3RDPol$G/7ERg!oR!m"d5B?Eo3,Pr,a`7q
%lF)+)=ct^kaf[Wt2'iG'mR,hl+t9TTMW;!52C\i1E?\i`HYQGBFB'Rq`#_2WQt.iS5LJk@,UNdI;%^d:#>HsMn+QQf$)O0OACYI0
%YG9B#ak"_T+Nh!'4S#9NHAb;DG&!1m@e-l=>u1cn^K#qVbjHT6C,7LqOlCDR4fYg6Z?9[9BuOW\S-]b!E0BZ>BjLs*`%#88n;I[f
%D/8!e`fhi!)s%^5k,s-?V3A,oNl#%Yl56=5nS5?;E]A!S_PJY<T<>O\3YO(`ES24f[HrV->+)<AcGG>f="](;&lYA]oUnudl"u?R
%1W9tC/NZn.KLV0C)AD"J'(W*qA^d.F8(.[T>;\S-b9j6c8A7Q#2SH)>1u7i7=>`8*b9i[T^V<BJ2SH*iacrMU\'9FVkfY<aT#Ojr
%;q$*dO/>'E=uT.dqU>YSgt(nh0:UhJ5FNpQY];_I.j`SI:rM2ZLSh5"M/1[IRref=X/(oGJK1:g'ZrNhrI^,dD=knRc"Kg[HIY9X
%Wl^$YW0S?A!X1tKT;Kp.fg"]#H5\2laQm[XN?&rdN^uLC`LDp@W3@8ZVQ;9C2.o^ldaoOTLV+du(OjkTNBZY'rOT#Qae(LdMt)*l
%eTEq'Zg$bJDjn\i.<^4!7$"FCD47t(Q[1Pa&,N/;8JAdO)BF;^D4A%)Q[1Pa&,N/;c`rq.VEIG<DOS(1Vg:6q&,N/;`sTqoC#4@b
%K=Ws\NBZX\1d1';?T6Bp\D.6Ne1b;b,^Va2<CsWP('-ufd`)bK-4ibnV^j?ZcCk1qchuNM#V)!h@`dR5IECG;4kJnm*bcNe'g6K)
%:2JD:1d1&VI>e9M_a06[$2^e?roB_rk9[D)DHes+1Jeu"Q2[LoB@,lU,'1FA"Jg>%OA:Dud2MGcjJ[t]A5SF7#hBh&X[+=U?oBXb
%fu2"l\n":Hk#.E2;0%EI5+e\\F-KORO/.mNCn_^]FU_TMW<+U-bJcl,RNnu;C&,FfR-'DQR+K2'PjV[k)mGUeMUJ'8MijQmH`jgJ
%9qP59h+AKAV>5EaJ[u,8]dr>VZ+B&4GL3ah:",A%f%+qZ]hEN`RS9PODh;4Dk*`@6Cn3Uiq$sRV](;lLRjWUKaYiqYaenZ`K8Zi&
%$G-q^<Rn,`58-=.KUB@oXOsJi20^lg#6D<+[[eT<VN[do9d_a[p)]E5=;dht)'`Jl=>SWk.2J/(H<gU+=;h!&QT(5=mA/)fSK'5!
%3Z7J!kNSoX`$.74Mclu)/M&roHrp2#KljVEh@7*^W-JDffsWka#'<r&R,`WtCk<[hkV-h9>.:e/Wj*6R<PWZuUaI/paHc/$7&R7;
%=hP*9BcLaPPt6*,0\8t8cn&$.bd0-:3qKJ;`$R=O']%nN25;N#+O&M1NY(o?q5!48cY6%7Mk]t9[h$I46RfG,@tm*=lKgp$_fM^>
%8*p/;be/(/`C7OfM9<1WFu;:&+-M3!*0=\UA,M8#NG,8j+Dr`f^*k1['!59q6Thm+bK2FN8[4!X2:rg\^0.iNs"NWc_RWhqf*K*;
%Ab]@Gf*sQ*+490kI<$p=.l^`_$Zq4>0,PC1U'_d0k7V(c,Oc5q?R;oii]=_SS-imJCReJLE_C`fraq3QU;KrM?l0'r"d4HF_K/W(
%-37M6mg/-/Q%Jk0E?%!:q8t.8MjabXVYPpN[M5p528eD*ktZ3/RV`\(DrM6C7Kj)'9RO!QHMN\gTkYKA_K"7DW3.To%=.#Qm9j>:
%3aM^F[;RpL+_hQNVZ4l1(H3mC3+.&MbuJP2?t;M&Y>+\^D]$F!Xa\5[cFQ#.rFC>>?.u'4P$Y#I3Dfej#F[mp(?Ys`)[<2lXq0cR
%rd,Sd7Qs\P3]Am*[O(MB8t$`a=F?;52h.Kr.(:$@-0<*'BiVK[;`hj_2N@`[HOA+WX^s)q2[2A;8TZVE!Su,eVAcJTlT&0oL[^o!
%;2I>)NB+m[8C3s0:Q:3KSJQ)XD9t2?4I,@A6CoO'Wl_eRAN*EQ8Ni4jo7SCoRi:\c`U;Irk8BTG"!`/n>ptWP%n<5f`\OY-3O^gX
%-&4*KVrYaeB=P2s_.M?-34C.*Q"n778)gO=4#RBJNB"QBCngeE4\<MH\)XV>J#!HC(`f8^KU3\&;X\4<L&PWJ$=/<OIf_JaN33:b
%"P9A^DGX6R(42_k%h+F4E@Q<0;S>l/Zo:[4M3$_4YpZ`>0":"c9bG=`o&N1rAHP+.RpGPqnM8AK+6k%VVG2%BGcm\XC33IDE0Fi*
%KL\=/1eZou,hEV?l&H3gd>,]Q4Lt$CpA,Wm'1,j$YO'LdHJ^^>k/5-1Q]3f\J;Go(L<>_2co@<+dWR\!(C&3&:ZbDe9[gihJkbi2
%8P<CmJLA&()N&$#-:/]jq1ADkRQ9sC9UIh0eN.%tNt0_PMn\GU)o&g&-;24d0K<QIA'/#^H*]Rm$03@lH<M.p![,M$?ie(d+N13I
%]-([^cbh_gEfIfAQK+UCh.8JRY?f(6Ie^(:<%gfY&1Jh?3//?Bo6?\h8ScD!-Bi,+ott(J=%Fn-:RR_:gV/Yb>@+J_dZ'\a$&p9c
%02'-3m]P.pcuCK5=MEl%(HH(.3,&hS5*+["7I(A.GpUM4>[qKI$KtZLCA4W^(Un(bTZ3s.L60CN5Ab2-6OPh)cCL(cgB`;E=E!Ma
%@b?k-S=2W,n#GeI4!Q[4+TS1B1_:`X,YcuD?9E%!6<:E@",m\OCqt`V.H]r^A0NiBm-l30J;.8`7Y!ri#\L9E8i^97Otp+3@^7QH
%lF4R+@:_;fI*.btVHPlrNa5'"R!?-P6('e.!W<_h/nc1Y[X&\N"@Su!P9=36VaTLeSm'V8"dfsm?rl%fVl1Sm3;M#jF\Op1^7O[6
%m.fmoPqXon?:BOXl!?k>l5UtY`PtVL3Kt[KlWt?K&cnqHe;5tT%5Gr&',m>a5r?ZlEdh-,!uQKBS1W<I&F9Tn^$X*"3/.mP`GVU%
%.lJ@,c_'HpNN4uE\&1Vke-![XMA`'3Va)/%&6@)0&;8Bnh*dJA+j9@9O9]=K>LDLH1r:J"2CQ5%o=m(9.@,)X,&&B*3US28f5;6e
%.&g]Y)5]djlKG&@5&=Y#=s!fA1gSbA2*&iNW0EKt6i24>pXESUSD]V3U2j7C0_CZ]W?2G*85Z.(*npmsSG)jr`G)-YU'SmAA8R^X
%1ZL]B6?j,8jH\.LB8XJ>Rt=[9JP/,'0e:P'ib3QG#=J#ToHc:5/RG2F:Bm$,H4+-tQ!?@aON+Gi,'+*YC(Fs\@IM1g=rM*R(A,-n
%F,SZ6e<A;%aOt^eB3^FbBl[5(TJ,?V7m5JfRhKXLS6e+EfF#9*HI/!Q^uifZD_feRG*Ne#DU^<&c'#kTFJSDd5$dCqp\c1_72^fn
%GPH1TlLOf;j4j%:rSu4r^3fVPm;&C:TE!lhlQuN,^O#kTl)WuSho$U!F>jFm8H%\sH96tqnMcKNDuP@gqVUes^B*`J2sKHP2uie/
%mcJ<[cu4@6h+DXp4jH"Y^X.8b-^QP-*L<C]T<-,UF2"V$Etu=*;8MSG]q<+O'Z`!`RA1`\+=A@0T\s."R!&TL-j'>tl4'*fe0FT!
%J&.]QS#=c.Jd=V<2PB<WILtprnXR1_k_/qI,$?UVfEs*!6nESE"0o+NrhMCSbfIa\'E"5SGVnp-3GZ%lN3m.59;"?VJAHT'dI#0&
%+ZLUc_[/a]GnQ0!,1<<M<WdVO\RmMOB!)7ICSHHG>6g_IL/X#@7qsc&#"W_[FA=CLOhC=+[$YCZ$Hr<I1O3'3B(Z-;S[8QL5'7_R
%Q`P'5s,o[br4cK&NF<7%7frqB.4:MpESD(fmcKXHK9mrWPW@t@J0Q]SSM:*90Z!V\V'un4SdZIt-]:;V8mF_81<buBO(h@e";Y7T
%fj/H(Wh;S!oNtHK]I5%MBeG"l4-KsYrC#ljo7l`O[@Sa,*\G$:aF+C`"Q"HqmNN[id,_.Sg9V4nMG:NkoZcJ9'I!ag=>R4Y-Ii`l
%nRR&58!.VO)Pjq/WfE4CAIt\7.\PVUEt)%aA=.=/+hUchD,hW(ab5Jh-[?$@,%k3NLu%&"&S+lE`b0>,`gl22S$*:j/CBZpEQ;!b
%Lsh&m:ZFkh_,uEH1W8pg"%h-,-B`A$M7@2P-nGLXIB%jk5J0!C;'oHPkq=QQ784tlW]R<@/Y2ITBImR9Acf6+?/sIliLV3QRWZ/P
%:/o2bW@J&=fEtW7YuH"3190QZ9;RlQP%U^QH&`MDmk+6Y)ls88Q-kdIT5<$\KI7/4PkT"BrJ.:c#B;Pne339B7t>5Mi!=s:8oDk+
%ONoM97'rj'NDS+k6fioUB#Xm5MVYrR4\/+,dp%st$f6?J7-atg,paI`9G,ZmXUmW?e-tQ4I3D90(c"=GUNVV,,U0*:aa3bk'W_%<
%0,[.@H-2\pmnE9q&b0HAC5oM8aFel!AOI#$PVgin01qRu?_DVg5m;ASll$+si28sQaN0bd:`nXkn9WncP"f;L9L7IKU=5J!mW[B7
%),L#oh9P!fAtmI@/CFjNf4U\^#(g8;*Kt">&[bN>J_07s$Y+$FZ9rq((f-9i4Pf.eBsnYPSU7)Q9$BfOPFn6nTjNXYII&o)2313W
%-Zu?SR09mpA2!$/D2l(O9S6'U9]Wbu>p`[oZ6<9od4#7IP<k7@U+7tJ*MT0_ULkUTAri5Y=oWkU96A_@bLpZ@d[It@F+.sO(S4sn
%F@;@IOEpJc#_Q?7(pbi0HFQqN',N!)Y55BcKs9gYR$5;f$/H*(Qt0)=#LO0TY46k5F:&m.3hs3A#XX;G66(]9AB#ad@8**GZ96?d
%1X[[m,ggF'kZS_'eVP'eX[OW0MKe)`f2RE_[P+SB>+#Y21_UN-'EN32^ZQLB94oY=pq#`03X^O_R*qK9E<tRrEeUqiP/IAVQCX'i
%8_kN-a>HB"$2gS46MUY-i'Xu]]U>]G3ep9Z!Fn\%Fgb@@7lN<SUK'bTY0XGubE[;u<o.fMRSS""Y/m6^%KG9CF)QA&#%i2g/`<=9
%*F4NFMJ&ed)DbU>ea^fGh('/:XNEr4/@X]=U?+R[](6c[I*tIb,]dIo,]b=]_L8V#F/R-B^l1:ROE(4e)OoQh9lV/(]h:Mm23XRp
%HC.*%["k[t#:h9oZ'b:dfi="Jl*2\W1O`FQ,%lb]"sO9(H9NFlXsL/?mQ2S<QbA/W%k:?UL7a]XN>5%2>a(b)H^"U/W)p@R\Pg$2
%KSXf^0mARE^KgTO9VsZZ<:kojWg+4T'9V_30lBTM4]S"eoi_Idk?Ktb:>Q@1WB?'o:Gi]hPTKNX0?Uqq8Q3^$&h/d,X4;GhTQ`ja
%EZ`Y;bX?hQl>K:GVBJ"Goth6n-q6$lE6jEs;b0=4*_!IE,Z6O(*h'#JLn5:[O<K:2I;1::=B?fF&eVOLCb`1)\=/XU@JiOcbZ]&I
%kYr_2oUfbp#/4pd.9,rH-X*jCeQ>NI_1tCC=_II>&X/"s#i>;ee3#(LiNY3Q+uiYfDilBM9/*aCESf]?YPrd@P>;"*qh7>&9E_fW
%FcACuMGf[Y`G&<,Cj7B%=Vn&RSN<'cdrQe0;7TGkYM7bYVS^8&Ee$D^LrfV)>6F(LH>dMsgO#Wl8<tjh;aX`#'q7.MX9?0cXm!8;
%_j>Z]G*upp;QqAZ@q5>Knf]VR5)lhuZ([;rkWI3lSP789)(=.$M%h#J;pb,86PN6dX:@d[Wg]'h%k5?g#O:d'L@r,/*2L+jH%Lhh
%WtWLOlksjreKeBSUc?mu\0O*fpgOLQ&it]&9irOpT9ABd`bM/3EM=*tP)2UCfT7oQ3lip2FP@1:T@22NiI1?Db+;#D%/V438]mu!
%`"n:(+E/kopHYPJs.?tXl](8@dX4"3s1fEQh#juEp8.F-d<<D0H@b2Gd9nX>-l1!8Ru<$?ZE4&uF3jEJLu_HVX0$DSdQ*ohd&/)J
%`d6XGh]^dImZ]N-l%VucLltn.=iLas?H$!9HNd%Uh'L0q7RtiP1^$.64AbZPbL`8iefpf>nnM_tGDn=(cVo!qoYA9Z65Op.J:7'a
%Uqp_$^JWk$]('TR5,]fn;<2;F3"`!SMA;tfMpR+!rD?D0j@o6$eR["/&8R"#*401dT!dA#f_:90VU*kh0apo%9BA^4-D@DD)dT(t
%Uk*PYXCrS@3I93'LGJS2Q3'+m-:P:BNujN+4h!!^NQ&u\h<I;'!?XHbLG:q\,\/'s(H9HnC>$cOQ7E^Y92XeRat8DLfn'i>VDVM=
%+=Dit]#g3aS2"!$I]<Z\nWpqe6W4<4P91aQ+(C3GeAs!b0oM<3-tacbO(3W=Cph28&@:sn*9JVTM0^t7fWc9SAQbk1I)ORAX=[*B
%1l>c5kC]J]W+1>Zc_cVX;+EEmP/u?%"E8+Z%uqG+>9ubc;E0!.hc]i2;2SbNHC<:O@++F=T%P%$#Kd1h+morIK^%/A:#C`ii+=+C
%)An:jior!,O._P'BM.@<?Qi0R?kM;,oIS!V3p\./D4HJV+1QrJe]F.`n9Dg]QF*0K4J$<<9(Fakj-dpR\mBYKY->sBkpuKjS*FIW
%AXef!kQm;*GU8+21V2u#WHanL%)_AKO\SM"p@LZ$85c*ILoF.UJ05UBqE_!["uKeX!@u9HIN\n[nY2t6:t-I>>r:c'VJRU+pmL@3
%r6D,shWg+[X[H8J"]\'a*]</C-"7.)7<C#-fR&>s'YDUV?\'[Q(M(KumHgop=r$L'nICFF;?QHr3#t%?*OA^R2.lBfiAa42UhnAD
%[NRnX*Ub@C+e:`:&Pjcd/Uk?D/bgfa0p;'9KH?(LE_Ugrllq7ZU5,Go;\'n7lDY#rnne$(3<Wb5UKe:5NCo21hDm@/?%]j2&Em3#
%)p>)KS1;6b2+j*78[UADpgLeYAQ0*T@sX.bJ4i@l1`"OVBuElZGVq4b85UhBGrq*<KCjt.>BPoLfIWM^]uhgaQVKZM\]UqP7,"uf
%&F?*B85(g5D6,s7:G\PA*[,.W$\T1Njl_:>C"Q?$*>DGD98?1\<GKnA0+bg?&7V(2AIb:F3^m='Z!eA@4`eUmMI<oQ3CL8V8@_&)
%QG/KL3\\a$K:1:8i*<BT.Zei;4O1*9(AV3:0io>K^mg0'@7tK]"XdM#6?-jE7ZN(0+rg'lgT?nF8fd6+=.P"pri%J$a/?q=eYn^$
%%FEQLSkhoNEpTGfA1<d$>VN1`jPcjX`(qq1;SeOUp\">4Zf?[a^eWt41015rP'3q'GPJHQqir;QM*J%-d$D2NNL'+b0I[UpWC`U'
%b1U2_B8EEbe-jDT8_)i\`&Kes25iDXp"/872;(#iGW&f^MNc$f89h",n$#l?%QVWN(78m>lQW9T6.f=cEaWj>Q>A>49^/tL,FkJ$
%&8OllIsF8kKgi^!oJUbL?bC8SW4JN;-?uikWR*m:1-OCkR/4gj:JHVa&$chK_pHoe`FXog0#/`iPDLF@Y^2E8R8%1>=hT'CGFqPo
%9eo2G[j[`U#$jsb=gRQl60BBD%E:dR9`^.>,$Wdf2g[`n0IlYn2DH@G5Y0C,@\GkCdhf/0*d7,A^_\)]1L`ln[:*0E6FY#O*=W[/
%GSkr$BFH4N3tE-+g+X?WP%A-HS+8!"_fQ*M(V18_LN+0hb6+Mbgp?h<HbjK\U1>FiRNSt<>U*QBj1)c9UCb%2\:5\H:*s^QS#:S@
%O&iR.+H2'`s'-LK*FWMiKaD%,:SQ%gAto&4BI2b21Jp:rA`0:FogK-SXq_<YTeJZ),Smu2'u3H0(kaNBj,m\I-CNJKccHKBSc[mr
%HnX'\+d0^_%,um?be&M+Oskmkf1$SU:@rA7&3YD]Cs>tS3d`fEoYgH/(e"M5Tb'X%-XnM"fmS6gCaNX)N+IhA:]0om)r8(tarh"=
%bPMOI6m<DWP0n[Ug4KhRc?i%*">NnbDNDgl/m=>N&.OeB0ZO`1?;mT!Zp]^Y^?Clhoi[+q0[bX^-3c[%g"(gK&Eg'"9KsMP<$sUr
%GY]5sqFjs&.B1<;`2*?18ud2>+8(":D1ii&q/Dc>c/>?o%kYP(9(\LbT_&Of?)Li01i+BRQsL]SHVZKg+`6rL?jIu(M"AkK9T8<!
%?A"8_"JmWrqZrq%=<cl]&PoK`+Dd^-;sEqB0,bcX*#>92AVN,fFRMJ=)KJBP:X[$2d?TP(77U#[?AtRgM2bB<:5/a?1PhRips]Rc
%V5PeKF3<gImqX0665>>1'B0:EXPs@-ihB:"Oc;jYkeYBqMJtp/C0VV3I<kZ1%:O&tHW&Am_5cl[kV.h4&[Fs5;`;$f6Z'q8e"C$r
%$[uPJR"h_D%K9VtE#%SHo6:]8n/c[h_h2tu#JX^n6VT3H5t+0Nf41"9l!nrX/L-6H<'n'u5cEZ)km?Y+;Ip+NBfN<^g?9FJQm<.G
%-E<(B26W1"aqTY&Nfb"IO;qNjb#kJFGWrEWki'>p:8Ve]U.I-YNpKq/$^p/-Gu'N:f*AU;N2WdIWdI+bY4%/n`J#oh*0@V9C(JG^
%5'fJU?NE*M!8=IN7Po,Q+9m7&/Z2B2A`\:cXmDiNE4?NqZJ^O>ji1Hi6e4Wm#Z$TbnhMi&gel9.VuFgU#V\eQNb>_4klft+<<Hp0
%`L^^ne'K.8SkPnuUb$J/3b&rNA:I3:+D>j3m#hB$A\eF7!NC'5&8N;<=%T[:(t\p?hl7S]8_S`b2A8Bh78!!=)YXpF0FMU6`n$%\
%ZUVJ@O\UQ6MuX`L8$%)Z,JKZU_eFY!m6+SU&J*,pA+/O&6,gknhO[]hWc>htGpjeQ?@hoq[,f_F`t0254.<fH3K2[=6<@fXiX>);
%[g"$O@CCCj$3i*2YTJ746c+r:J6d8A:79t4-n_65*>6@G&dYQH'8HVqS+5O1k;PM[_*3"('Wq\3mGFSk$_10ckg7D)(s?FKB$WiG
%Bi;o<`^@JslTp:@,&"V.'/<4aaf.["X<=6?+h0*T06>l6S9s'/_AJi8-]q7t;na=F(*Vn!(=0p*+,Ij8!5aGWS-`IH(KC%"IUFef
%J^<SKI:<VSMhdsu8J'^NMquS$@?ZMFH#i&.IQo]u#g"3t]>k!jqMqF-,`+-(GcQ\X/NQS,E^U>h4DMeirWA-INH!&IHeE)uK6?\A
%CL\JdpEcjpS"I1UBBZ6_=&W*qoNrc)ohL>&G@7/6D8I^4qb5<_rMd&c"!C<DVB##1<5n)AdH@I$F3U^.30!1#>43p*bMb"NPoCNb
%HP1^hgoEWu>!V2k+`*[HNN4mr;hr=JWbI8c+uC9G=cj>$cniPIicFbM"&+Rj)rTkGh3\E:iFUSWN+TG;iaB;$S\m^H?:=MNQA@jq
%\XuQ[W6@pB)-f!lf?abXi*kk9%[>Gj/4/tM`!C/;'4\?XVh3S?Xl2ZY\d#hS;=_)J,+1>W'\/P,lE0V$_49HH&8n%@Mi$Y+)9La\
%5UXYAN:j1,`_9C64Qn7mp0`nc2bXN]5EWK84%3upg@IYoE"6"X-R!f8_f\75?[9#X9A#3r;!mul/_)8OJSmdoP@acq/NYlI$n!T!
%l^+&t%?.#fnPEf?+/3lQ.]+!hE.u$OrH^SBXnsm;O1/9V)XZbHWFTJT9TmH_RQAdNSBo_G$:#sE>7uo^@g`18JCF#G0V(o#m(kt*
%kH&lMqb)oK-E;$M$-HTL6K`lA&5>b?Z8rK/`-l+1#Vb0Ti6RroAU>+DM"Z7%?7Pdo)+m<KPp<"n15\O6&[@4FO;TZFZI=L[HUr8'
%Hpkpgm6Tgha(#r`+WA[D;n!/C%EbP%@0RGYQNN\Bdbl-CELsTg\LGAS!Sq!66%)sd3.S$JXSJ"K;l:%j.Fd>(LlLBCB":oIiZ51]
%hJnH27[Aj$alj"n,pLR(<.PJ:78/b6YD\8h9N9!5M\<22$mGp2NZNT")\416`kYuIGs%#\UdMhgl:lODoO'FdOgmA_?#"*%5b@`A
%1RL#2(<6u\>$JS+(*3qDA6OE=_5c-+A?-5BN'iaF&Y0<P_.jXe!Yq^I]/2YQe*:#6d\-5c`it^f/cWdX7-/4+.I."C?VVH.ISL=o
%%d3YE=c<YfMN>jBguCP-3hDb60*`/;"0m;gG\X#ZrGf<![P<F_Je"eKR*05=q)Q]o5J6gSg#LsNhT^qB\-E<D,<S6H'%CuBk1XR%
%K;!406^etd1+2RI6I[MQ6k^N-0'PVhQab.2SQT5MIXQlO,b'UG]qs+DDNIYk`pgN!XYd6;@V9GJ-C6##QH6IA#`-$2jOppa89tdY
%;j_mpU@/Y'C)J1C20fYPb60,2r8'CtaS/Xm8qLR:dPM]R/`c&NfFr*0I9jO]>U%cC_\0dVc6@Eo-R4Z((OJ4pE64U;j1khSW;$Je
%4IA!bk\A'!B;<;J:,NkOe:fT1N!FEi/l*>DXhBAq#Z3I*qVk%SXMMn=`rRB:P`&"34ckK0U^FrpdO>(XJM^D_.hE>>a@Yhs5h$_s
%bFC\'p@D0*Dqerh"k+(/;6JIP&h\gGBFh7L))8Pe+,6nE;0IW6FT\K%E+=Q3kM-RJ4,HAk\1B'C7-9f0cY>`sH@\SR&(JZ:XAWk,
%p._<;\3R[X;;f"jHci8lkNtg$]G3)+Fs0U^:^n*Q)MW=Q=?@9eL:D:h_Z.4B8bAfn>U"mTFq'OS_qRY#BGj1DaI+ZH.le%E'^Scj
%CmsN`K<LYH`uL0N',2<]-J"XMb&*#H6NBU&@XjgX",e&XWTYnT.nkCa:8uX&dP,DM0HE.R??(<<<C]F_3ieE\)SIGJHXZBsnY8?O
%$`"4CSrn651qh](oO(S9p2EOUD7&e)Da(lPA7nmVct"B'_'K2YXGq;nO>EW1eKE((jjIHtRS)'S6L&'M`dgHt8#]TGLEqJB$qu</
%@M6o"oH4H&B%h0:Wu=8sNAYn"!Vh>(`>@>);"Eh$P:bi&+rAGXf(7mcBaiJF(b;gS3);V&P)e)4k$]U6:<%q*\q9VqMCptlG.4&_
%gtar?Kb[L6?R6bKL7jmPc$?^ZZ>ZE6l*oq.O-W(=$gLWR2Z'!U&hV$YF]1'*",GFWGS"WSMFsFE'&%Y^YIfi`[3i>e1t5<uAV_`S
%Uotdq9*]MN$?Ks(%G,Z)^1JO3Mck.R;CP"YpY=hn,l1'=`4N\o@nIJ;8::":&S==l)_>$H5,ThDK142N,k?\]DQh74,rek\(5Ng<
%QF`)Xj2TNa-Id;%/W;Z7WF$u(?,G8f>[##$7UBDAcPMCr]N%9d<m.EZE@frhB*V^4O=bOmMs45tE;qpi?dfJjkA/q`j5Kp$kr),L
%9@XYtrM4Zm]j&@SYae[T:sMCq,Y`=IYg$s"!s,Sn]O208,K-*mh`N!^`"au(&PAVV>uteg&WrMPEe?jm6":?qfG_M[c:jrhRiGQ/
%.RS6CJ.oKt&2rM+39HiQ?IN;ea28uB*Am)TC%.b$r/p"B1^LWi:%j_9dO(\2hDgRT!>TI*Rr\ut%Pp!UR7mB'8XL;-+^VX?N6)qC
%+Qj6^P#JMAD@[?N`*atPF6FI"d3g;0@oOihcpQ=RRVS3Z6A;%a`[NCJ(;4C+))E'EQ?QU$*'ar(&sm)mL*5FX)aYoQ&NiII0o(Q'
%6u8oT_-e%!77K]1(-S6@&-.)VM&O+YbtkKa0mD#r<R*k9E),d9]RrO]O,&)j!,tWX'nInI`cHjR7QkEo8[_ikqCK`dc`q-o8H]!;
%folWb,k\oKQAQfM&E$G<D)s!^lD(e74XB;`C3U@Nm2TMH;eA7j4%P_g[09Y>jgI^E@PcS25oP>HPM3'O6_e$T1-%>k)BsId\1/p0
%ROnq"O>A+`A4Rq`3@g8G&;+7'ohH'bZ^;'@5&E=^L^'Af'm74tT#;I&2<oK`\[Z_p$oZBH+X!nD(oSJZ0@"*J.pGu0YkF_1ZT7su
%X@Q>+K"b+cQt-M6e?K>^8TA"cj)(cE94JEn:?m^!"l3B1eUbh'FJ4e"gonZO13?Qbpj?$pWb+8Sr2N_MQ2l">IlB5)0oL9hhnI!3
%W(Oi;'iB<s#*Q6Q1si[C41g.h@T[fQlVj"`;$Xn-`8Y73/X]!rTc0Y.&Uc(ce4D4qmDY.*_%0[EV0X+<3(<9H9-)T)<Ej$ufEhGG
%L?-dN;NfM%[4:a$rEaVe`YR'*T7]=H8ue\Bo.";/Y"]Gs,A<2GS0[,cC!_8`Gs&'j.E&;r`H@JVft/6hZuN":O8Y0g#so>&!1#u%
%-K6#^GUMY!81e+o((=B/THZ"u`r'k)Uca_IH>&-P/8j]LJJjGK+p27*<.e[XN0YY2CSLuWp)"07:LH#7rQ+7q&V;fk'onaqTkWG+
%VaEL=HBU#l.[5tOWN+#@6g!G>S=M)\T-n^*!.#p%B%HL`Mr^?Id`Ufi.^YUs9,D1cnO9b?A&+NX&fY:W-oOR>mYIgem?B1E=QnU%
%NL,W>:5iG3<nC#D@NsVO!(QMXBEpt@91[*lJ/=+u^LFGte>q<['mA,RL]nBP,s].b,:m0Xf9d;6JjYRXoQ/8Ak9,95>VSTdju)cJ
%5c=mr3O1B=Z'Z"+/gK\g%\Q%r`\Bhk@iR_V7A1bZk.c^hk*S#;J2Lq3G)^bKg14uYo`4J0MXD,3=>Z7sV!(k"XR'!tB%<Cm1BJ]a
%5&5BDRiE-QW$%+PLrfbE0T;fk(]iW^1crVa,i.e+_[8jTlaSAT-"AcGWE9S<FH?kso:a/lDrL.R7.Fd+=OF*&2O2sK1sq$[hFc>,
%lI@#O)CN0?X!ha#,OeSu=EYN8.m]96V6M^L1(n0V56X9#hD:a)ViN)$>VBek(qaQR<H?p_pSm3I*):M]H8]G/('bo7\qmg0B7MUD
%52njRi3=!>:RP*+$(T$\"bZ3*6H!)s#D<RMYR#^MBnCiUfEl5#_+ZsZ<,%7AW[`U;>g&s`DE&dJE.4mV`0aY)$nrk0SV$=M1P5b<
%$!f8%)&NGpN/])MYNm+W3=.`W's7i20=_&p"d_BiLWK1$6*B53p;pdgn#XgeEhoCrN!h@\`T]3SBspT*%HqA]k[EP;Phoh+8#fE%
%S5S[h"bb,EPDaJ#NADCq8d%rl#%k"?i$E#mL+oMn9?:)lWh_t7XHY=(0E`%X8R6.u`9Q=RTeT1C%1s2rQA<</$#gF\aQ>O4mu)?.
%#Md57Y<1.g`jo#4PX-:/i-U"s$6et3i`;,n@hfW:F#8(pe+G[4]=5dOL"<N;m/"Vs=$nQO=/!MG@.ktoIDo6-Kn\RZ";7K?q3-CU
%dk2&AUS\rG%V)7Ieb@sRd&99$(,8EqR_D.u-9M[_Gg(GlI;G]K0PK#5+e&e#,>=7H^K:_sc]'/H]@]31m!-mTFL"I@,b"iV>XcuF
%`ISR<7tPU^(f!\8X!04%!oHmIgBgVTF3%9:TKfGOVpBT%2=jr@*6%BS&7b'*p88]K'`g+jd@I!tPb`!S4=Y>/;S+o/cn1rB;tJ]W
%3!.ap0]rtpLgAKG;.0$0Y4;?cb6I7BFpM%6E!L0Y?'H0e42>U"@_0!9r;?WVTIqNaPI<'-aWFe%,$0P,*Kj_An#0JbY<4^7ipH4^
%.qW?2BS9*DgJ]m!0qnKE#b)m.c<r.9^tcmL?1j^3_jPnjJpG/pd801DTSt7),[jCS,=cSOo&9!0,V`^F+*5"P8H>_c6&-dWI*_LG
%">^sliRT5(Zc6guJO5=I6ARX0crbVCTUok>@9T<bE/l&f,a&J8LBn8=@s9j':c78UZEUH@%>\oaF#mWmK_*9DG.>?\eVu7K:$Y',
%d+R?d)WG1lInaE-S%spNITA;VCj>dpgoJ^o@%/8nL-^nJ#VU+Bq1.P&Q5VlmEOi&g6lm\l@;1?n=,MTWU/O]Uj.)[rDN5DQ0P#Eb
%4b3J75<s+`T9<1W=?%g'XN*ubNU__=\%5^aK#E>pc!Vd+d"=d(kg^"Uk!2_6Ge0*u>MIteWkD7AJjHEur-GK8,)f9us.i0N2=cL5
%Or1E:%\$CZhN-apMV,r`6o362<99kD!'u.9-7.'B_7p`I2HYb:jeJkb+c9u2J"NVJJpW9/)Tj/Gb=jqiRM-Q,Mma\V,tpt23ZDYp
%X8EPT"Ast?OH4Go5%j?Epa+2A8;MHM"Q"mWrN%P1bjGi+!,"\#p8N&q;-smKMaADI?pR_<Y^feYF%&9e*c,Eq1?OdWFaEVcK4J^2
%R8"'.IFU'uYEC%2_m1%iCEV#a&\s5hT<%1l_Q&ce1tC])@o/3kSq'bdS&G,$!cF"]6j!RJke5q'LmNs#8]l>NglSTF`oRlL]nrSc
%[SK:)_Ns0VYsZf(l#P$Y/YQ!Q2KU?ASG6VGCblh0pb.U1?kNdl&1e32`\OooO\=$`^K/tp=p=PM`k.>jY.d)gX7\0ifa3+$U&u5b
%)<]3G(bPj8M6.V73b/a4SIN+I2WHd">;19;,^olQg67NcAgI+We6[R.L:$$>gYF/eJ;>+Q_m[F`1l^8+UM1aL09_uR`T$Tp:g"qJ
%<4rjL"Fr1fo=.n/b"#%`Jrf#H/9/I@&Q+'eM.iUK9SZGB"joYM>sBZi\qJ`+?qt,8Ic*bm!?LTYL/5^hfn^O+iqa&66SYX+@<jiN
%DbOaG.^U+&q2@[.UhZn=`[6/[/=Wb$=HtGqE,")J1_pOiEEr]pq:tK!;7U:Mp=W]"<8H:rAV'm42%u,D</Q:l6da((_,.hoO_5S#
%Uuk;)(p@BUEC"hB-QPm5X)fKl+![fd9;BaFijPOI\q7VYdZ"4j7_hPMSY,4)K-C)G?6:PT.:$5L&P.GT.Ql$&W=uCo,e#n4Y!MN6
%!plg`kU,S.6kPIe:YYNE:mOlYaoh8K>1oTcBX!bdJ(HRI-G3.qn.Lrq!Qn,/N3*+@:og2O$)>[H1g<QSWM7;_PRo*+[='j(mFgqb
%%[\DOo]i?LqZ+FJkV-nnJUc`-$k6E`".)CkTm[Ms1Kl%@pQ0LS9rW5X0ibfllc-"mfq/!J]pl_qR$$BX&P[+VFH=tl8(Lq'YaOp!
%&2lO[V<.n`_n^.N0UG>1W,A?i?u*05ETPQ;/b@C+&GXY+bb\ap$#8u640W"B>$+83on3K'84c;q(+I/:`$mZt%X3SF,./qt0gng^
%WMYE=TUOm,c)(ipWL>.b+f&('\jAK6++e5o/+3l;.nSj:$T'>..M6-Bee1skU0],GPXSb3B+_i;aab`)%%ro1["HH&9fXSH0\%Ek
%;TY.d+ef1)(YJ1d`_M*A'8-U)&&AmZ<#jC.p0uO9%8qZn@3R^F#?>5>.CBe77$r&=HT1C5+\Tg/Ou[pqS5Bo3&J58GklP!`6u^=+
%7<A&:"ehBnY"7ojUGt7i"soCXS.g:YL\K*)l/ib4iZ[<kUmc*d7#g/9c.srE:1EjL4=uU4k9Iq7AK>37M)1/4d7n`g$6<PP<MY0q
%S>5\AfTK)'8]X`7U:b'8f[!BRjm)X\rfEoZCZRg'#Y1XF*75L<*Ki:+9fe1PMKLFOSduSqa<?o1Y#,jSSg%,]Xbs@0'QfP+KBptt
%@^O2&L79\QV0`^YOjHtF//n5aM_Nc0Y=(0S8>6O\fuhf1pX.p0"t!HBCl8>+55Ye7]9VTq#:B2GMj*Mj*IrR,^!h/mLt*cL8CL3R
%NL_BVO[kVpPD']5#n3X%%UTF%h&)V0^uM)R-mHOW`&QgfD9T$`7=s9+("nNV=U.CD!3H@iRN[U:FBaHW?U9PDe7H..Vt('FfZS-I
%,(,t._O&9=MXb2pW,][(,X/O!g?!lkAHRGP0uBF-LCOP;E9E5ZC+tSfs/(7L<(u(j6UN:)Zs^d%)XIqqP^XPg5"mk[?#fM(QiVMB
%]C?0icBp%I>maEjRIdG`gL:e_E?eG<h+Q>8#`<"oMDG3l:_Lrum`<k/?e0N/q?@[6e6-)fb1hWB;=$G6A<TpFoJeY?=Be3pX(H=n
%C)ePRW2Ia6GP_2OAX'L][qOe+'Dh:f#:758P1UF`!uOgZ'*gU[.Rqnd9#/*D%Cq6Rej/Omd)\=C,-pD\/]^[_UE`_\>J["EO1e%s
%##);I)5^f-<9Wk0"pAd&`@C.REFc9Y*eTo6Q[,Adm6$X,,gPgV/;D#Q1NJ'f.>5M<X+Jq)G\[sH("N'(=CKILDmjWlOK6%.lMI)/
%!T&cmIPid5(H=^!OXe4'W!!e(aH#0jR#+Io^X1OV5U;3L:Ci;5M6OP1:6[rQ.N]MC,96Xg_*UQ6B'M9fK]f$4'kptP9#h>f=]D5T
%<e$9IZT!!Aql:au$07.SFWDCfD2*V,_J2in?=?GKBaA^('(DE(h+fGdJ2J:E#2(7q:#`XY(u@^d,Sacd?qf%FH-r_7=gFt46!8l'
%$"t;A:R_lRX)8)%;V^1L$2>1`"+`=j[;b!H<-aR&^/qA;Tl%X5W62%Eb6EFI@3+FV7@8-5ASQ9@'7tIsR.)[A//*?T0T'S0*aW?h
%ofX4tA-Sp^<,&^P,W!p<K:B:OnL@+&k/o.H-:IpGMN5=Ol^963db],^2HJ5_jCj.OEZJ:cZ:-O9,E-G#Q79^)-XJ#AC?@<f8V5OD
%'>SbTE!Nupj?8,=]L?`M%;,,7$6LRV#<8YO&L"$P`iJCa.HgcZL`0bsGTCa27kp_Y/%>ghI3[?pkaJQj5.+ef+<?eO?%<SN#cerm
%f%8U(UUH"Xd$L(-LDY6T>HR1DKJX`h0r18fitr_qHQ#\28+HZ9JJca$ojr<WgTigi:8kTo7\=Y^eA[Z)@#rKqHgP>/I"k+Ek;l]^
%Y?A*.iRk[p:apau:q\h8_.DqtW!TX@jp0+.ejGA(4:N;^M[(3.fDul<`2%Vc^gNfM4d67d;`rqCIcLsS+DX\gKcII"D"^=E*rpX$
%)R`^1<.*&8[?=P0e&u4P%Ie[c=WCnb'IfPR`^J-4C)lt]iM]o/7NJ_)VMt.1?A/_sh.RDNJm&5(U]Hj>^bk"j#8gkCJ/*>`$U9CV
%`+m/N2q9'G3@9-_SFK4a7c%ZX$SUISi=MJ81=YOu/`N!QjqMOqH$Phc=L4SaB7A#L[R_QVFZc+#8Q3rQm&Ao:P!(Nd`R_oce1LeC
%R'3"X5'H_\3>4`8#RsJfq/p;T8Q1!tO3E0IYbfP<#4SVATI=n#h@LZ1T'/?_;SZ,CSB:C0+jndpA;C;N:t5?["$&&7HR@teF&\3p
%CXqoD+rgfGk$0X-@bt[j,8.kF&;W:L=E(*uVr=;VD8cP>MPSZKGT.uD#dUR9rV"Hl'A>`@iJqG[TnjO!*1^7VMa(e.K8W<)&)KX*
%eBpi\j8;:>a_$&>r'D5&UE+(b`$"I^#2(_n?8M;SALeT7,CN]*bsW-WB'<pG_ZutO,$GuA.'CKO#eEmU#%@N'(s"Uo;"W_E-[JI"
%XA:F=O90+bUeiR0C^:8V;nI21%.k51?;Z9-oe%J(.X0#kp_2%:S>FX&NE\7[q?2!H+4H/[56D$@^b91Eg877"+X+7nQuZ`lE/9gG
%nkBO(DNUC*<OLUOX6X!.aE;7i5($tM$p&Wb7=5^[S2$d"*YH<MQ/kaKll0[$enmA&ar976>g#s+WZL+MjrZU!id^,G6qH19@G)G/
%M3(D;Cio=:SHp:C5dc,)lSjgBA[$"A/tRZTLFn?5dccTcWH6Po\g=W0.#/UYV@g/[&CW49S3%9Y%9?759=UE.<FHMSM2gt"H;#Q7
%gRH=8UP!"1e6kro,$IEf1UMosq2<`q9Z1JU/9PpF&$2uF]:qol"BK3H$Un=t]F5S>arg^CMZNRQ?s,U"K6$^o(ss^a5/]AHS@<Xf
%EZi#VG(NR?K(_B;cR-K%,nr,(R1B`?&aP<lfoeA(jrunBJKjJnM=2!"11%9=+_AL$q:\uPY_>LAAs)`1;pZ,`=@<cBF@6%>5ISr_
%GuUbD+raDeCl;G=TkDcnr<*/'/i)&"aap`8G/([-GW'AjNRRK-[a_`b#RSku:f_?jc\HhHXFh*Fc[KR[3RNE9b:s]_iS9r+2FdLs
%V'X!\e)]Ft\o(J6;-5#3%fop57(AqGeRgRCeL$:bTdp+aX`d[LM)_nF[[fm6C(J9om0&gQKr4WRr(b@L:pX`Ejk4ZX3Sb=Jr+N_O
%$O0'8W%,h1^o/u*D&kLV24-gZ829$HMOgaobD4?+5Cr#c^eI]Pe($G:EGIE!12``pVDL^n&j82+&_[(1`.86_e>-:t<roU.*l<+1
%-@BIa1=<EMAIL>]<02Xo-=1Ba'%5mSO[O#gnJ1%RVE8-T7,6-j.f!67qe9l!ja=<2B5BrQnV7.+\nth[9lCOoGI/>+'E+:,
%"<EMAIL><GT%cod.ZRkTI]FXtX?JLPY&\p(lXT*Ci+n9Qh#K1mS,n8Pn2URT0MI*Kc?2\7VP:1/C+<4^)kM)f>no,85sj
%[APtl8KFk^C^)`si1G`EL$_m?:C>r6O?'7ZE"G]oc!J)[h'Ng8.cl=1a<8A7;$ht#l,?[E^b@"[f[[]37#s9U<);\pDt9f\J8jNU
%'q/noCJ/5*71af_"O1ID:#"T_fb3>6:ut`RY]49`Fl7A\/>5sfXh#;h4O]cL8J/OO=p.:oi`pG2dFA?JR<Hh&o\f,Ul_\i+-cW2D
%(E4Kk"q+D'K+Kb[4N92T+JSueS^g7]=G52)C9[H_R?+KNb.s8p9.`;/n-@@7m7fI;\fllrJJ?uj+j:#I9ZepB%[9tUj)+rU)?=JI
%Z^;t3M-`f'aC1TL4j,9O^ro@6Kr.`_PF#t=U-78nVWKP#[AGleA.4jr00=t#8j_4n&*7[O.WWk44[Zr]4Y:p8K)0*CEu*jpg">S+
%0B`B`Flj$%$B`,>l(L_V@@n`i6>i6hS5J+G>*@R:FRu+S"t&uIV9lOb,"a%VnHLKkQE9Mk8/<=/K!:/`9BbQ?\-=9G3"-Ap-?CUN
%<%5?O*.2`5H5!CA<)=:#Lts;V*]K4-W8Q;QLm4s>=F)jS;'JW?'BYo%-%EL\23fUXM!,0Gb\rWHAY"^0==oSCdWV1?nC0`mY,qc+
%&2=u/OoY-ad,>%d!Fo>K&(tbJea.QdZbaEgRRaW@a&RP@%P)oqF;GT(5S'so$)?kZWE\ZhE`eToALfM:!P0/[/e'c@0E@+@WX$t&
%"LFW5Bn7@j<O1Tn$E_Bkh7@%rduEc'Wo/+h`*n;$orONj;)$sb1'#.Cj?6-n&4#oI!f9U[DRoJ$&-kJ)U'j(\Ipu)G?hH%cAt'\a
%h'V(bHD[KL<tUXVIfV%oI-7P!g3(4p;5+.nGsuG68iK[Q(4,?6`hiaS8^:KT@a@;XK=QqIJHeAX?`"VAW^R`ufVuOd$=E"-/VTh9
%!Xp[Ec./T7%YIBuD`p^.;@L1j;MaXS"XPf4PJ51sHKn?gRtqu1_!j<a\q]IbA;f!*\;0g<d'GQ4Jg:r5gbAYpfX>NKW2CNZPpI$E
%$Qd6%,=FC"':bm??e,<!!*4lkJjcem''sLl8AV=3KHYA(L%ljj<Bi-@!?e6$fCSlM0T;4>U$bp8r[QA!N(VNOWYs/c`<\p?W[:pQ
%6WP0F+MC2fi\4u>a8%H#r9j*@WIR>T0iTNp&*R)o\&TjgnF@H(;C5EqHCdD;;]]uc2@"pnTuJKImRP)0&m2feT"4);)o2%RB<CW'
%_ie`F(Y@Ma+ASaKkUC8O5<?TVg.`LO9.W\naEYg9<h]hhhgcW5Qlj^[lP&#\$LYRl8Qf>b2D4[qcuIJHQLq-d&7:/b`h7kJhu!&%
%FHNaD;:(9(AVS=fdpdEAW!oV:/s,-&"./",Nht*$`8)KNF^4[A!")J<.ts*'E6*(>LqOfAY3B=E<Xb5!WCsh,FGPWA!KTKFKZPo%
%AU/0J.>nATd"R<W5^Q=[Bfs$3(OL2MPla[8Wkg*^3CsPajm&?cq<t9fVeuO<X]Fp(@WeH!a6O%Gpf8@=0eQskMK2blX!,f7Wa/O/
%@S/=\j34r&8dlQ@eK,sB!dm\":6@)`3cH>?EiOi:_lXaVc>U(!-\[XKU/%dlWX4/KPe_*da#mT1fM6rjPd,N\q@Q!0NTE:!U#n2l
%Kh:c'Q&k.PPVr[.NW@$<bt(HS,bZ8&F%%BU;ut2YpW,MM%U$>61[4!T]'WPV6^N#hTPk!3k*\po0K[t0)r>$T(rtu:lDYSp'?umW
%XmOf\0h\5Olarl=`tT69E$C/`%SUI%TVH"NfmD^o3]+Nc;%b0F%skc20Ud4lP/#97*_kn$OY%D,j2nUj+Dl0;n<h8F5:RY]=X$\D
%!ZJ@0b_^`oGq=XD5jH-McZ=3OYkJb/@FqtF9SLa8&/jE5bB_7N@(O')=eM_<+B7`E\#@Gc1gok4's2JUSj'D)1'Qr`NcKV+&ZrlZ
%@<>EQ;;P#'Xo[cH>4>rpW*mk?_Sa$QoJ+UsGD55nbY%)Z0W:G(.8/7WJ"Z2=MPqafG!BOX)6f`D+B0m6R]tn=E+9VIo<=o7.h.qS
%%==tlU#\>4drS`Y9<q"#Oqg'P=?BVQc1#$nMQ,ij1pj.?eTP5VJRo.0(#krLkiX<0E\1<G'<.?elWW'd>PVhcf5#6</3Xg9THYY2
%4u8t@7g;:7&Q(tSDd;9[*A@XUjO,Sg5Kb92[[K&SQ+1W=$Mfc2X)^Qi3mCb1RbA;u5gqU/&MD=+Dug[S5oR4mMq0R>0Tm\!^GZ:U
%`1\60.$%>K6ul0AU=l*;]u4X-]'%BWqdi6E$G%SSJ:X2"7t!AhRZc]Qd)@8!&#'utBJh&k.E?^r]@C,V]e/%MFT`s7\VqZDC-L`6
%Ha>Qk$aj9b+4aFI:DOr&,-hE0K>RMW&4T2B>G']/?j+U<r9UB4KogD4V^oJ&cON\k=b%a%BmmAhgdWKf$'3u\&R>uCVN,D[DUGJV
%6n>Z:?H!bc5Ja'YWQoJ'IV'&?D\c$dp&?Lf:_R5;4=Zi<&.fSnJ3DOCiN1dq-aAE.XleToMM`h@42hn9jl+=$8g^,43CYmm7e[ok
%.nE[%(IApP7!#gmaY>>DSq=,-(YS)%F6J@m:W^JE.1f#r?ud?VK$l-1F6GelIO']WJ12K&s-Bm$RR7uJ/Zq@WMcS\mU3NCI&Jk.(
%,&19sNO<@dI&TmBZAS9>0k4[o?tmL[;t7XMTQP5?W0dtgppqmu<8F]6@$Do-#YE;Gn](7d.W-!A88/UN1mH6%^'M>S[[_Z#Tn]A9
%"[P*Lg=[^C9R,oYk<NP&*AR2bfFgpAm0/;[UuP;$l+-F_VOF_)WGLM?3K-"AO/cUQ(e>N1cR?0F#pVUmC1s\^"dU=hj:M]),n3Rd
%ZBR&*'E]nu7<2J^Z$DC"ZucI45u(IOWR(6>2&hMg1((s_0_NeSd6N%EWhh"\.,GR'kpX!lX<^:SGrc\^KitYdKW%_rF9%(bMXi>]
%mZF5Zn-T><I!1V8d->"\ApU?r**=V:CQqqsFW.#X)CXR>fJQ9Dl60gA"Ce[&pYr5R0J2KgF&)K^lVh8.dBZYI/B4+f7eT0HN#WHR
%bldgAF*\KA'dL-R(q'`DJgEg*PTm--rqB9If5mbYfMBUn*-J*6fYT%e*&1f&>f)+m/<j!)P2SLbk!qG,K'ZZ?>R1E%Tm=-[7*^rU
%5nB_GWu`<+-7oK:$s&qiE$]@9ZWIZJ_V-f"dR2=<4GI-m1K/YMQJE"3")#"pU*7X"@N>#Kc7kT-!5"e![!:D!.3"4N\qQd]86[9Q
%@E;Rd?ofnlO5*V>;5Q41[)u5o1`NBq$apIX#/?sM>*EL&5m^BIXQ6qTKQqC[+q5FFE[5WW:RfLWAUsV;En*YgbosPP6msLa-$+sG
%AASn??KuTROJHPPN>UjnOmE8*fTME>,#KgRAb]sl,C(hQM/PI5HF8@4MHECeoFBR_:mt[u$?)E7<WH)d]3cL4r]+mW9nGP@;,D$<
%()<>L=<1Ao@*F.33[_53amlCSOK8_BZRReRUr4H+O<eEk&8MW(.;5)q:*M-%%h6o2[)Fn^g<]l`YN/PsqdFJXV)P@G%[YYWT<)Qb
%!'1grRQ0VLN8C9,VGRKPor5I1LArt_5bjs+>P4[9YolgZ)k+r2*]\tDn>;%Wn`,WX/Qs-do&r?&952okRsOlKC,i&PDTqXlGf^0*
%Sa-PMio:t3M-+2_<(@A7G*pseN?dGj8=p5:*f[S_pis.KX9ZEK`oW!B(6Q8NfH4;O-6_l9=tXP$\/G+'C^<JQPU)Zo&/Dd5)g`.0
%T(=5,hU]Ss1A(`I6u0qV90/oqNFS+[01`,iYui_GTsTK5S=VcoQ(Xe8jc%OhK7n?NB]02l&g[B1eRTt*aO5$2]g52g(7,IORTjB@
%a\I$F5nP@XR9'b&'=#*B279"MrsA6GFqiYB]8cLq`%8]GK5dCKBIITY",\1QEENgkR#0to&5dMDnq>'uJ>ePUkLhmm5]11>HIRNp
%*ARqf!9T!0l=ofg0s[#h!bTu6V6L[Z/7qd$1J=TVO1\I^rfTkaXH=!;*cEoZ#AafYCdE79_.[$90ML%q`ZM6X88T(%k]"maHtg#M
%B"bAMdPO]#2U_AKQjh2nS/\Ot<15<d/NkNW#TICi`mBZ_fL_(++@&`TA+L)O'$i#jMEdNXW.Gq=)+9fl6(**%hBlGR`o<(h$]R.!
%&/aJqO#1gL#W,;%C(Y8plX1<J]?iDDR>Le,(3+jf+qB1E7mT=Nd;_K&TeC^O*Wc$&o-E]AUYDa#*4r=6!0aCnOVT)0<e,kh_0Z9Q
%<5lgnTESq+hhd1mnNrsSJ[0lbWPrTu3e[;B;:Rob#u/ujZ=7t5J1Y&W,7r5]"S4$DkZko<hD]!m'ZUan1!j-e?/1Z\R5J:@Q>?\]
%P!qgJoL5Zd-'X<Wr<WNV9W4c:-D96)D1i!U'JR'kj2umBX;8dRFf]G0J6gN7^c$e/f&W-WJ0nUEK^)>uE?h1ldGimM#,B'1dn,='
%BSP&D1Tc>1`+J+[gC.a6)p4pNO<0I`HQ+!5$:ic3`eqcM[7,ODkP,0Kc#Wr8,3rl7qeMs>Wu8#sIbAOH21?:eKEZ/@0eB(JAMur(
%.F=q;4'I0dIQRZLSC8\,,`Fbu@qo"ZAWo*R[EU-W8P;C[L=1<GGt7c^%+o1"Hh+!+O?e^2h[GmP?UG]A[?kh;K%`jR*;*o7NY=Yt
%']LnsZCXo"'+c](<O'%f2l&Ma\]!S@l\)oP:hgW.BO+L*P1Dl+K+UgdYM3=$L1%qleG5h\MAt6)bpp$GA@p8LQJpoh'gKZO+YBL_
%F=5?')EJDQf\VuKW>8BbL8Ll9T+"O#N\s0dQ)U"E<mks+!_JoIhrTQ*Lgqou`\P\SC5I#ZR;fD.!']/)JK-RI"F$/#`p@npVlkeM
%#/9L?hMZJ'mG:h/Jg)afiTh'-+8'ulEQF!I=p]/n*4rZ"db)9*6qM0N'$E+J?=7bsq9i[)[=B!)KAdR$Q\ZM4AT#MAl\&Oo[@R-V
%o`9q'&#nQjIP?620KrYT=lg1G-=J0!\/jKKgcf0$@*]t#6S#H(+ER$.Y!S9b0C!f=D%iX]4S;NJZon!_\`:NY`ipKY$We/<3:#Fh
%)-ZRoKG1_Q.cCBNm&7M9lo^NK24?+^,1\J:WD+:B\/@"5K:D@rS]'[s4[>'Z2PuRU6:>6u$BcG3PZ-WjC9*W<BY&?fec%*<RMG0a
%ZehP#3&ELt:U/?Ec^18t-PO@[-C@DDW-oU`^rmJ:3kuTDOChVP)VV+GVdSW+W!<@<5j@ngpMo65Zeo);>u!N2f1%E<$"-o=m*`d0
%Bugj^n0IALSB_koV!fT?;&t_>**uee;]Xp679qKV.8M+]D9?A0aM#HC,fJhB'g+>EO96W(cXDFT=;+(a$8]$[gS)9WAS@0[#_qY7
%*\IU8=D4pjA'cJ`@NFF@HG%$4mlNO'JS;,cNGHn'B%M`J.T+k:kH=i'Bkj:MFQ!D_;DL]2e5YM3PN((64s0.GoS>_#_Q=Q(UpI*C
%a=n@_*n?soX[TC<X2*`A-#6#:6j`$UO>RqJQO/FK'*W(W):>?"T&B*1>E,q9S;!s9b[K_5A][bE?!!)_2`$8VF:RU;nX1?sH3u-L
%)^B[:H,,ti$#k#-3b7f#']mEfG/tW^ik'/b-W>$c+OLVNROds_mk%7k?h%`'p2MfDl)4>K#)Eoq_+=([Q?trdSH0q>,!qG[+7W9)
%1AoADYPBqYiHF#;Nr3?=PM<*\'"U"BYTkEgWqkOh"T/nBfsouQNJh0k:q$cf6/U,-\S?8k*\\1E&Y@bp:5gk1QDj[:Ch2*RZgmgQ
%SOQ$p&Y"^5_f6T6`H8.:9(Y54WlO9<Atk79dN'j2U=b/aa4GFTZ(nQISSf-IpBF[=/[5fB8el?lG(m=P&/-XKgerJ;E0IT%I9I/M
%$]7)."[-%_B4Se/`+7ZBdTjgR\>d1NpQ2*8"3Z`LKcE3b$CAe3Y2Eh![<moTSAT?sqK9Fb$;AeU+J]LsO?R)Xnea35YHq%Y1k7f4
%_pGB(^#98-OQcCKKIQRg;]>]W8&n3+>.UO^<NS_AqY5/Qg6>p[`WeH:..#R)=/IpqgT^TH.jQDLcNL/Qf\KdB<5A);OLgED8g>M@
%nH/n;O;sN6XO>$hI187,0KUa.YZl55+>j0_:,2@Ep$/q@jc,:X)'Tof//]B1)I&X].M^TcQ4$>K]/mnT:fT^cB+=?BYuqBUAP-7G
%[?B(L+$DS#[i!7q@*K!kF:Rd_0p7a*D8S;j-D5TD=Oo80hSO4;&fCoG?q=nOGYK#?>9a\69_'IV7mmGgq$<;33!,0J#]$T\`5cod
%$.^-%_B[H\f%l-Q$)&2tL+JBDnfBj4RV63L9QLa6W,S.`X<Spg7L`13XMOk_+t@;A(3PW3DJCWRB]R^s=Pj^J4mN#%4X0)mWt7$/
%?pk-Z@X[k2/BVSSB[K`^^C26jUU\d@LqkMbRVR%Fk@Jsj]4Y61"_A.*OTroCc"%o)aC-VTMl@O3Q-^Q<iYoth"(3p^(c;5X"nGlW
%7E,dpE,qouPDL(3f*u,h>1D&\3#ed!Bdkul8S\U:$<XU($XsFa\oVbie$8WQ+/X["^,KRtdU%*T_b11u5ssrDGT.B6-M4YWjhU2_
%9Hjm99#f(E5#1b!jLg9CK2>u[oRQ*FZ71@13<XNLb7cF#e\g-47"kT/eVI*""M0-t`2;HG7K2=H+i?7f;MOg$>Rb=K*I]50ZC<l&
%99bH&L_W5)bC8BL3^DS7Dm0&RS79UXP'SMdDFbhjn-"PR0L'.u7YdN!B7)J,9e!.aE)`9:8?CSXngE'GUc7O+N,'Z1ON3XuWfop)
%-U#_A23MSiWseH_@!=F3O0._[8S<?<^6$1ZF9!F\%gn.N,,'TVlrXKA,)%Bo:ElMb"1TAFD`J)>m+@Rbdje0Ng$$i1=M_`H@I9Eb
%@LCoq`7i!clg3^k<37ju@4Jid^nlnQ(,0H:bcH(bcu%_%%X?Za]EX"u^1`H<#JmQD98Oh'.1?^];?a"UCiW37U<4K*Z;X)6bEco-
%6r6on"qc^^9bC0T3.QdiATBbg?R5EK!i\%)M`6B<4@c+moDCXZm*4HsM!.eg:Qnp-=D:`PXOkf:A9Y@R!8944@rBBgB)7HU1I?^c
%^*I.?&@S&'LEKmLKF>)\OmUpTCkc+'c&ME@if#DneS[<SMDR2^(LT=)X(L3cNL]OrRUmV&B]a&0M41C_KHI,5@SO^!Pc1Elg/JrN
%kZNIFgP4s$#iUc$5o;gi\5:Q`>%r8Z.i*u=*Xn(.`H3P^^)Q:1LSYlg0O[:KXO3u*-$6J$,H?j3U2],Bi6k-XP,$aj'85Jo[%P,H
%@V1:m5YE^b7."YJ+fdZIoPc#tq8:-)"*FOOHIs'e<r6M'TEcO:'D[e,#I*:Gn3-Z-/Apf#B?e5B1]^8!;*U<bCX(3QRNofPf2A#'
%8tBD>[=PU-Sif#Oa^K'8NRd1(7&!n74VsWQBu$Lt,Y(gi6!,8#m6'DM3seF>%"62K3&su(HW;t(K^L`%C9^I/a<_;'a%5J!Eb#jU
%0'U2r%!KWu9a7\%V*pOZJYUhO\=!f!V[6]e]gB%M;DTh8*u,jO_"K-%%h;;uEc)_"]di<9TjS\;daX%%2X1DqN.CV0n[aAa5lu`D
%:O<?6ejR#[ea2!TepO7uS"IVZ=I#'35@/t+bQoVpjK35q*u02$V![d<:d9fG)O)9;RSpaEV8pN5_k'>39+!ncGSkM/^gZdS$q:mB
%T0!3jW;"/SdGmZ2%]t\M&_Q:/EZ!!f</:TZrdXtMi*ZnhDGgbAbN>2@ie#HBV'n;hlZH,<&T`!;eYYe>SM&9MdMQDp*qg%\WBoP`
%?6#tV'!-)/(rPnYX2.lD/@b7D)ZlBA5c)IQQljDC0lR/`"VFXODSqnR,^tck2$m?83&qJX_?1j2ekJ_7&,_B#%ht`&@G.S#NDoD6
%-Jo@B4Ato6R:,Xu+P5ABOd6AnQ6!>CqsMH'?p/2*1AVtWCQg'QHCu33YsX=X(&h&@:3+j<W.sV#!f(#ISb)1pfELh"4!WeXA^oWW
%_A3`k6p]E!!p^tB*4Z/N3"mO,Q($[W6uTM0_l"ZhQD(+rePW(iK?q?;.`@^JVg09U4K1ed-9Od/8Fm]/8#eh/n/'#H;6;X\m1(3<
%a')P$B%CW_)hoS3H""b-LAH1rQfg,]X-+C]VI`8(8si7'.>gb><J[OT*&bZuP>me-YDmQP>cYpg*L<::,S1\"9:5ps-ff/=-R+pN
%&]bEu21i<r<au]No)hF6R8bq;O]C%cUH(Sj@(pN-^U^M1g[K_o'KBeJ6"_fSSMDk;WNsN^=:^4%T&ObF'_m@X`m.]qMN_,pH<:Zf
%EZ"NI>P^mu0H13c1HL<.W%o(W7FYc38Aoi6jkG_?nR8F$_l5<@7aJ"nNp(j;)CB,cBW!NYrHNb#kpY+tdA;:+`0lG'OGfl>eAiet
%BU&Pen&$_M#TFcb6i>\SciJ=@Ag"2)+KB-i;FUs);\r)]VUZ8AR<kUa+p.RZ(#ksb\ih*e=lu[se#5h,ZI*6Al!c>]$m_h]f%%l-
%+=J8IiIkUZo5cCj9RuVn5'T*t7D\<oH3b)c,ltLqIO64=1VAs']e\4]a0qS4#:/@61RjY?:15oj"K[gu\<]8s#XV)aq3oqaW#@L8
%Rt3m.E9BQUY8bJ'm3)OnP/a@fY=bCZR:8&5K6<Q*.p7`LB0Fsl!K/B62mL=+0NAhAL!bZYQt;3)BDT*nWFe^+<3F2a,`s#O`Rd&A
%YuZWp%OkS/#HG9\GlpQa8$I#fYla!/Jf?YudK`,nl'_).*,r>,JfE1;9VkS&_OO`S#A.Z!&rhS5An?"71AXK]gKq6p0XO7MR'&`!
%(%b<BU'7R.(c*@R-,gn_O&NHW`[QL#:Wn8H`!s^$lj!9&4cD.S:Q4[_)Klm?1.-h05DG^QMOjqFO<MTF$s:!k%i8)HS82ro8$hT$
%moj?I9Eh"=n0(nff-IBXaYREGa,eYA3ueI,g%"ATd3R*j7nA1S/-N:M543#69E>bC+4"O,@id4$F;ZOi!VN$dn2bupTCE5jYbM09
%fCT0;Gb4o-T6Zuj+A-[cQ;;"]O=7:4c%br=%@\YG<p14qbU3\?K+64NS=j<BBAmmL^2uVjFZ>;3iK0.9LT$jN*KSCpVRe<p;n`IL
%#+Sj$aYa`;Bi[W7nkr:005u?mXE=;4s'rX^np-tFWaD5meIjQt[SOsM)T+H_.hSSj#>W[",P#peaWN4;;X>',(b7sS!JH.]:s-2n
%)`/h)7qsi3SY?%,KC.'N#kMmU8^IL=KmKlJU.]OrG<?_ZRaE9-HXh0CZ64Z^s4@i:HoK=Zi5tmD**/<d@)M^IO(nZ;['(`!>_B0r
%b?=H67a7TD#_:(W\<%9]?$_7E[[Y9=#E3;>[ZT'Z18G,t.FFeeJStNB+erc\H_RMrA)9U)#UGD6D8"!H>+piURtX'3h(g9h"3Ust
%S\S!6`XX%,TPK_d;6_K?`O/<J%i0DM$.0<&W]3_UK54TB+<Um,=u6/0RbT-P*@=r9`\^]^/C;;9=>R@-@ZTKZ^PX749M>b61`6<R
%8B_4gRPQdRUfFgS`N27p`0fGWcEBM=dX4<'+4l?K/G6"?G!QS^0On-\gSX)Ec'_j*<tf'6iS!JK]gT4Hj_E=*EK,q<:1d)8LLF"M
%^7-8@\=_5aIpp\6o@558!g!;fW`KWH+%-OATV=&\i,Z^_pjGokXe"<d*ZW=nN;3@(!Lur4dlbMFfedDi_l+eh:,Ma.&MRd@+I5sB
%Z2YSp7?+@%P,[Zb3t@e:@.5J3".0P6Re2usTOrr:BP"9h39H=WRRWmGe$rtSg>[e70^>#+;Mg86X&'g6(@$`2N+5TW*tA$K,Ef*b
%@O8'cFd35+7E[*'#llr5\sLl[CU?noSM[XAgX`k9KT]eeJ<>_]7Od<u%ml;N6_B=2,j7cBJHI$hE0TE+?4e>+L/ACngg&/$[&Tu0
%d5WOIo6:c:nic3kek`=t'&=GSnc_hj#Xr-C).^Y^;4WF[B*//iVPi+4aWR=MSD>,$WXsU=-.MS@/78Vm+^>L`M4p=)RQb-;eeBj!
%`bT7hAZN-U_k%jb=Lp[jS2D!j.'WNIb]Sg:c;69+WN\Xu$CNCk%p0J-J=F![gG9fX;Tr"qOmCnuC%_a/dt$4S7RZb+&^M>&^n2-H
%'XRul(o>)K@u/l4<89fQ1fU6j%`W2_'l#qpC73<g4o)dTBdi#Xj[RiDqW.4[=1#HT\uZf_(e(JIofPG'%8Xf5`6L7()9-Je9Qc;A
%XbTD>#dUGZP_r?!Q2OJo'$lCQ:\p]T*5NIs6`N9!iX.BGE2KDAPVnoT&f#PB.8HK7-i1+HVhCWa"iTTU;,W"Mk,-ID8l!?0`a5Zr
%US,oG+SNb&B=]04c\eW5Xg2>u6\musa:JrsTqc`6e#8GiS.+99`)\5cgTd/a`?213W6uN8.IAr2'IF@,=7*O;]\aAi[b6>8H)=ge
%/V7I9$H9h%JEJ'9W?aG'+,K,WTt=LQ1T!f+D2O,`+_$2KYIYV!U=G&=JBpD)X+2,-EIS<VC0*fEp2MJ,kd.&n&Q',k\O*9H7YG;p
%G*A/#(SW7hP9_8nL5;e=UJ/$?lN$:%1_<#@6JbknOA$gtB9H?4P4+2J,cW#^BT<VN&k&IG8J=hS;^kI25GArEs!p?Q6pjs"X*%1U
%:r$pME3p,)H,c#V2--k6A8<Mmg]b-M>7]S$Ai4H\0pp<icn"D-KF,nagM0m(0]Cprh1I(V>9)/E#mSI;PAk(KB#j1M7@.DRZOi7T
%Q%D#D6F()>"K'Ol;!9'O"<u.&aoa)Ks'LS7<Ns8U(B>bQAL1+=_PtruGRFbeG$Rd1T4F\=Q=7LHeM_;sI%\IhK@@o>78KE0)MR)r
%@PS[+4L^4M@"/+uV<Ug8/eJ79+9[IUh&aM9\^j0uMBQE.3T<E41<YQBWC1+9[/D+V^`7\D4iCl^$DPbccsd-*:^Va:-_e<Uiu5iq
%Yp41@P_`Z<$q=c(/A!LEYK?'C$^%KIa]0An;__H=[q(:R0hrKfa:M3eYc8%$b@,]a*BuuEU(.5Ulrn@]M:i7-H%];JckMlk.G#</
%\sd>Fl;EZ!e;VJW(@?Wm&7F\Q,S_LQ>!j26.FB)%92ek$dD_$3<.`Tt1"-PN8knK.S8O3F7@F8uh,JK86>)/c):r+G0BCNeD[D-"
%K:L%Ie[7"2=AM>=?DA+1EF1aQE5RL'D9-f#,G#Mjb:LSJG#,6N8#JLP>u7k)nT>_]AG&cTSg''Yq5F4h!Y^YG,q<-"e$ENoHD4U!
%<:!+'#R;3s"F4B4C5j\b-4YUleYN"O5Bou0EmhH"`4cQc:KN7-HR(21aS5J+]J_s2rE$lT^N0QBAduj,@^Qk+E&"1D]Ln!;iEjG"
%[HAUs2rGaui=,jhErB=IU5GYYGRXFD7,5jkTHfLZ*\65=[LaeF@)#lOa6=brkXTC30\4Ce;H71'OI.^FLf4h__&NR`jj`K,P`^PX
%oKtn3Hr;e3,o0V^bjbAFOGB>CJD!Z#:?l=EOQ_<cdY2(A10Cd+ZU2HS-6nV1.D3MLjD(&C#S$]Z4L1/bbf\JWktI482WubH2F*gg
%gn?uPb4\W'l":d(#@8i,)4CC2*0D@oGq*8I`(-ND!i%L1k@#!pJP[1[."MQ]!X&NUAb!\=jssRD!hS*3(n5gSo$".k?D,+'.rkDZ
%5;rIBj]j,A%D`o0H@mp-1n8G,0)C.)\gCk56u44K[kcg(@1Q#8)KN=;=UuN94;bt`m@BiB1o%"f$mPs@NVZsN?mO.c*:_t(Sf-W;
%-B0,p<8VD"*#a2TfPF#GghRVD@e?[cgER^47$O-87*?TU7eJ]@H0i8bC^<*)X#,i'cd;mWQn<8*cKK),C`O7rP1AkAHKlu`nQe%n
%DG[MoO-te8[\G2(0J,S9K*@>"S[Dj22m^dT%5pmh(m%:gfE(M3Ze"`>\D@qTm$8PcNF=<papm,,3@d8P!#1)F2@Ir5aEd0GM7l*t
%3aHR]plX02,'Jc%fA^[b3jKB#8)l2W%Riab9%"E`;eoaE(g_fZGi8guM,m/U>@S6c>aEEF8/$DfF__FL/4UIbJn-+J-%UEodMI^u
%\pDI"1SYgC;R2,8-Z>E5f0b!a7jOW`bqMa]Ej_LON-"WJ2atDZj.\rfW6!8(%];e"-oVZjKST[Cbaj!3T02=_Ho.>XO=(!j<+ErL
%)8PoC)\aZYOm,V*1e<TCR*H1k'SZ^DLSBS?!UZP7!<Mpj%++c%Mo[km*"6s!H8"2Lb/9#nU4+q22'k!E-OWVQDgI?#dHY=Q,s`4Q
%?qdPm.COJG#t+f.)H"Cm[sh%?SqOIX&T)O1C9q&-RbF.:QfNV+@$J'J%6EiRBnF;H?I8o1E!l2]ZCgq+&%]&Bi*(#9^'@2.8m:A;
%B`YE%<[T/%1mrR9S":V3N[;K0QaG>k2<[Re)4!,mT$.Vp<7,@%N\2W19:n>qnN;<G"Cbrn6meWqN-A=6m-1*p%cqM9h&:/^SL@&)
%it%_T@sP<@2O:H0@g)'.'-!0b.%9eIK]d^1.A!$R;k*[h\egNrV'+/CF@EC0(%R))8O^ppJNJ4R=+e^k;ruNLh2`(jWs4u(+YL>T
%-72AO0.+O_%(7#/jPs_h]'7bDO@5U9T%q\p["(Ig4un9>@*r"G9q*Wk5hD[(nj.gGQbM+[b[N<V7C#$h%"F\M_2-sUXmZ^BK4MBV
%9(-@cd/#5;_PUeRiarqT#36g^2n>kWMJ.m">\nR#Xo=h:5G8*5XdCu*)EO*H@X_f-$n!AYHQm3\HUa9UpMH%-BmDCB>*oGe4kVX,
%Z6(480c5"W[YtHs(<*)=;oEXq1l9P\_KDs_9VP/)NpaJ"SSG\1O,q.;--eWD9f*qR_aPbtS&+-I\OT<d-l`e!XXTCg>%<E^\5p=%
%=>WSXDI_QGNF3/I>9B+.T-bRE/Z0g8T83q&Amr3*=j/*^ng=8[S80Tnep=^$TLY7-eBhMCKVD%9@TbDEk/aDclPKo8p%h%J6hLSC
%lt7Y[R`!2rg?<WgI6(b`*C;BHg]5n.Ui7Y)h$HWV:JO:A"gq3YZcC"CcflD=BF*XTM!!ZQ!]P:>FL17I1_r;EC*8RLZ)/<[^q[bJ
%WDCl.&!4]rObo9'%/SQ81"btD#%#9R,jMI_g;Z]`c,6F#jp:$8BLXB(*:2@C8C&YE*oANiS"s`t7DHc"%><5(_O&[r31D-c]+HPk
%F0OG\)Cl)k+CZiqD-;oiE'=eMZYpdfekgu!X<rBWlnh#?aV@Pu5^_h[.[?\B15ET8Jdd)I,e_[j@(AS'OQn5NZ=>.+4h2QUD#XC2
%5Nt]<oZp$7rNGtprNjh"h7imMc0I%NhS&m]pLj6p(O%f1rSlg4QT(E+J,8o/S$VF=f1/Lu>3JJurUWh@dH?`*o@<S"rQ..t_]Lo<
%cW<?Xs8&A2?iR[`^->4[^H)-Ts48:)?@MFp+-qaA(ReG`QG58,LfSn#KSn8+)>)AeqSINYj8kljo^a&NURrJDf'eq)>$eP^Q7(4&
%4lPMqTVSGOBSPSl%gsJVOK(naH3C/2.8>Ts+>_DHB9^9Oe#7u=B8SKK33ALeQ>*-C)./#F>\g#]RcPSC7++(%ZtBuR-qhY1`2YR<
%3]ONY9%>Lj)KX?[6>TDU":HqVK&q">NmbjDQ#Ep[T6od$*$tldZf@4)bHW5gXG'2D-1B#DeA?u7jIG@Rl%/Dd][B_'B!W&\R+RfD
%aoAd\.5$1i7OD:k'0#',,,TFPAF4q//R;dKR,k6$',)2(e`6Dr<YBNm8IS/qb:0.nOT;EG:#]lpb55#bM5\0rkk#-9?"sg'O]++Q
%@MZdOM)dsIB(DRaUPFP5dr[%DLr;Dm*1IS;68T>lQH.m?P4`WSE%D7CjUl'5-Y8-k>hDQD[f"6!24mHeW$2m2]u8FI"6GV"O\7?@
%98=S`/6EGLfjVHf%A2\5SqRFM0IAF+?5I(D1d^M:fk[FrnN"%p/OUts/[B$Ga^1oNC8]1#)1rTmH6FmOE`Z`"F_FY^?&3%[[iS0$
%F'>*Jj*Yq]KgSf21hlA\c_DPEgh!k@"68dGU@ddj%WC;TB1e%Sk[BT9d1S==REj26$_?kJZLm(o4MaFb*6Q2j<=kR#8SH^1K]PlC
%V(^OUY61+l.-MgS\Q.^jk8Tp+SOn/VEcH=E@a=O)#$5WH6)&'L0eH"^*]&ZpdiPNbLWKR/Ko3JoKa1Ya7)R+`Q5++SK,jD)m6SEl
%9Gbo9K%CX],SPN_dVY*8)('CRkR#h6:`M=@"*t4Sl(jr0!Sro6_?4;e6$ja/YY83kQU*0K]@imNCL2^/,_8h$0lSQXVLuc`!LY"9
%EC^PNksJDoqqg[-Yt9L2-`MPgA/h!h:?u55E2;AFj'o,[a5Ed<'%80$Jr)L?fP9^hQngj3>WY_4[5"!V%&q*3r4WUmXb$4?bS5V9
%Hg0`/AGU>t1,ciQ,Nt;#-V$S^PbQLh30Ec0PV*]=L"L_#*^ji*PNSAb9!!oY5`[P:MDh.uST'DY;K.I\+r*\kPW=DKSKtee*#;#8
%ZnW$r&X*fDBehjF15A-*r)F\/W8g2iGX7WWMAk\i=P`Li027>eC(F0ZWU(2:dk[%/D1nhu#^IFp^`u=/1@9YJ(JX/%nZ1!S,\*dI
%Gmgn!6V^#kj6.Z,l@kgele4>nTKR7K,S<2MJ!A#c&C7TpCf]Z8S*;f(D'SDKRe2t;,I44t\I897ogF!YT(-S@KgE4Q"qMIJPiWbk
%?6Vf0<GCC`jED*^ZP1t^*AEIYLdanY'_'\\@&Jih*50'D>YdsMQsI<$1%A[+OUFOulg3t=E]=_=o2mdeK4U/]9]Ri:kO[D[eY//t
%V!gL$ap96OUD$YO&5YqY,*`#E/NHn_B#-RPF=G_]`1"7mU9'L[7jH)9;ETkL-OZL9j[:F=eQNJjWB(D9Mo/%qOaj_C#1PZLH"-*'
%n3(sj19m-Kn5@o@^jNIV!SOTrK^>Zh^#/rCZ>\P5bU?@5bI<AI3bf0smN^0gAVUR(lVM-XA^)+1T$K5X+*P-HVP.Y9g")?/Ner>F
%K>Cn-Lr?mN_-\&Z6EZCd5#9+'ls=sVW9#$rf$0`/H=3^KDC1[_8-HUg$Cn`5&;`nX&6#hT1g<T93XIRh7VaO0TBs3.N//D;n!d_+
%#g[I9VC@1sl\),*#nuX1%*F>@!FbWIP$F]\GS<d;MaI)efEKbmM#etZH8.n3[KTehp;(<gZ\s\c0oMX*EjNlMOkpP54@lVQ-&?k<
%]U*nKPt9]["_h\i]=;i67HGnea@J0pWd]t\,`(Z]q\+M,-F^t9.Ie$6+B'n`[=VAr=eqh;]P!Hm7ntP%"T`=_IZ&93b"+pr(ueqT
%I]GL`[QTJ;eEtd5qmXePU<G00=n^K$`*d*5.Xiom<slo<d*;\1eX3%2eM)*u6W1m%dPmJCOh1a7!XH*d/=*?pA\_2V@[U&SnAaRn
%XP&h#He&?kkEIjS*kGl>n2IlBR!d0e94bImGt;YgjH)&,QLWY(dJNi%Vsb@S-IDVZcPG*N-Rq,rX79Q`/ClO0=(*=?Zpc4?9B"&\
%5+Eba'j63BN4)@?#jHnbAr%o`ZtNrW-H7RX[<qQaCgn:/!*qaO!@e4\iE'IRKP)FJY8.G83u0]ooR3#;F:@6d(Q3k,]c;qq=.O4K
%#C7JMSNt/9:)^rddYJ7FH#36grJH9%^mBaL;So+uC/Oq%%uH<tA.CdN>\6a&[n710-p5ELjcIhMK`^)-pLtq?j/"+1@OJ.L2=&$n
%%Oa_XUQ)VHOGimnj@Vqq9h*l]?FCna;'V2k`fS-.,<`CbM6>$/8Ob-F3B@qW#cp1I1eXP0H3^M#Za=qC$`,XbWg1`]+/9W]p\*`h
%-ADr6"hU70/;.QO#Hs1,?b),RG$at[)2_t'r)Cb`V5PQdQPF;QAk<l&-^3hbFO;pKJ5fL=e1R%Uk[&jcjBZ/U^,`*F1=WHqC2.fh
%E2V9AVbs)RKq;ha<Tt^@H6H<C8`CR$AjjQi^`:P-l\K^kFC-EZ(W'k,F%M?+'q"GA]*4$.@Gi8H#>,Bn(mZE$K[-$_&:J-CRCH/E
%JRp:).)]Y+TWumBbKIa7IlmqT2!*1:a>;$!YS^[,Pc!Z&Xoi6@-Fjgc13\RGJeR$BE`TLaC[%,dkXq":b0luBJ\4dD%qHjl+>o.)
%72ef>b>M3b<dqbt(+i`Th?Dl'coQ-+C5.\b3QK"j;T6gFTZAOqcKc7aC.Wr><A\_KQHQif"7ch(adf$D/\@g^lBV(+d_V,B"PG'U
%Wg%(ZZ:.oT0$M*5':[3>a,o@F=Jct[Y)kNcGTdh+IcPR5ae$Ed@!:Z^MiRqDapTSX.L1KF44]XWfn>m;-F==5kfuPf=m0[-hud,^
%0QIMm,4HlrX"$+D0LkmIT6!c>dEb`[OlbFNoHMY/]&FuK1K!Z-=9RQ^M1"gF+*rM[R(MtQS=7"PSW"`7^a*?\HW\Q@YDX34mKm41
%78/_.c`0ah(b2_In"U++=N8-)VVrLJD'kZ>At`C2;Roe``;GTs0]+k'ePTpK(>gO1Pp2J!$?[L]H!YV/PpQ6$;I0H4'RE_>JKoa0
%coC=]-8HJVd;5KjF<YW_Ni<Rgj2Zi<a=Ds]5>5cS9dbeXYkVNd2)K:4jfgJeX5LuLEtfgrf%FKio>5UK<Q[Wtn%97XRiR>2%ltM#
%AG<\/T_^1f@8iigTk;4q=.KI7Z6h?mk&G/I,`[S[oWJ)ZjO,ap3Y&Xkg6.ARR2F5b&%l\Z0Zp5JL!Mt,<sCBh`ZdTq-<a^de!I,h
%Q*-i'4*mq#^c!YclE03+pHrUD@VV2Me9"/7V9u!k)BmZ%"#%T+(R).;>#G.5';J1ubeE2PCVlQ-(E88$F&F)R-"?<t+#Aa&'!9B#
%jff%Z;SW"c,)G#```Q-=`3r#%^$:fT0bcP!F>#KpA[K"P/VsIg]/aUMj*>ZT-]Sa@*4@nIA'?,Ha?"D6R"e5?/:/%b*lOnObFA8m
%lTr0=ZB@qXg)f<kBb+`.@4?_=O];5"o]C6=RR34s@c5*MV2R,3a2/O!rFZ!JBdToj;n\?kWZ`6@j]bl2`Qi!,UGO,-X+c>#9X3YT
%E=%dAO7dp4[n[G9]8'InAsZM8d`6t2?J&^Xp!Jpb@%Q_94:;59b%>\a>i/$j]-=BncN1XPM+t+j%460@:<(d9WM8rD-Xo("%H7b/
%-lpV@*()D8OYQ.GI$R3$,)d_RU'F`B\e'9+-5gLj/0>JB&S)Y3qoI`)@CX*B`:"f^Tm6m,*soY2%^Sr]k0["[=tEr'2madM[ONhd
%-q&1Y>VT/h'#)!)S![ZF`FpJc^ofCb6=cpWGEWJ(-g3jeH9E.L7M;R<Ss3Ve9O^QZ=n6oV,suNqdMg>[luCPs\-S[B:^\g=;6:gV
%%i/.b0(NBbXVr@SSQPMX_Q)`+e.KF6fm5&eCmbcf`YFoB')@4r@M;s%($!A!NA5BQ2Tp8Olsuq=.hiZU$R>O7mn,F07&G=9K+m(O
%KHA&af1;eU^SL>F]Etk")n5QIC1/n[o'-Z,Z,JZ`Ei(4P[`<ndZaleBJ>18j($rmp[45Yt&9B1E;/kd\$qDf3'4OP2.KV<`nW^]u
%0e+eL_\?-5lscu]\J9CEoX9YB:C(t05BaOo)d.pu/qn4Ab_kY:.Et8`-6j<?c8\R!l)KA*e'Z:^-_aAb7mr1dC1@`f(1In0"VI=$
%UWEV%];gf]Ka2cMb1)UE4d"Rm>Ai(:4^7'5os(MB7#'-#U6NQ'cOtl.0Tj=Bo/J>=X>3A%?P4R73bP0g9-ih<%W&Su/h:#1XjmAY
%f,pc\Z;TGB]D*e>\qT?"1k=AEKl9*&)n`n2=h1u:3j^u#)]0dH-:+g*cp1[0mPn)Q"4K%m;N(;PBkBf?LT,`h0L8b194gX[&Kr^5
%<g.G@6$?3^"uJmQiSR_ZnL^gqddK?2M[G<-,#0s*pZ3*f![>s!\Z)cIA`rF0GQcbTg`ehMAmu>sU@p;)8W2BKh2_8s-\>pT&[M%F
%3)V*gf>##4q>K.2c2HbKT8nttc='?:^\m^O^\s2mbrbaA0E:(iIW]qDFS4+Tr/:N%oWQI<qW^#;^V,&D_12LEo)/LXp?(`0LCa=3
%B1N!ofB]K,jWBPM!T%+2oBuV;m6;]kHtiBOh;-c%2Wrt(?G4(b\D\4^k[/deF$KYdrp0Q=mk4AWs2d@sZ[Y18QS4\#DuR,skV$RM
%n$LX>Q`okmIARlNB5st`!8?l(4$B!M0/!:smJVpqT]*>VZXttB>qqDTme.?D=>>LO[ProdJ,T''=5SFQp9jqi<%_O]QjYe*emr[5
%q9Oujq_)jEh7R(6pr2WAa(Lji)s1H+IWKf7G/`N#>5s!+<-F6KhS6]"k2"S,c2#>6=%@`4_.Bms[kaYHgFr"Kn'@Wdi6uf)Ek=PB
%?<sYcm#BS#@kim4q;I0YF8#=e=*GJ\n(k:OCDb?Lp=l#U*Q8ObmdB*%];meB)B*L/3oRtt\TrDt]CYp+^T9JHF5rP0k.-'?pC7&<
%9_.2(+(?/;\DP(!>M5FfRh'G^m<<EMAIL>:>I#o(Lnqq8L,)SUL8"jnXe#qmlN@]\GHXc3Z+AT"AI$o*AR80.46\hqc#[
%]O,kg51(c\=S_FdroFq4DiR"CW,asYR=cuu>>s/j<A#:k\UP#fm`jS@+s1.ip@e(E6(-I8AT!3eje0faJhFNaLE:QJK._Apjo2l^
%i1S9l"7(!l-0]HN-F5YBgi7Q>g>'82m(P[X`Pl1:Y.ji-m.Kc?5IQ2^+8$Y".9Xc@!Uo(V&bGDglX*opjmnAMZV&?-Rr<(Khd!jA
%m\Tb!21]U:!WR?:md8cPZ^WV>TA=D.bq"h/lST[PobL]iRQ7HpRU0q&D-D-HG+O':^3T>Dir2*M@?9mRms&_sH-<m*nZ$t\EW5Ck
%m-^3irU0CAib$HT4T<IR`9m6h41W%MH[k`do,BpIf)/Q:Hb=cNpZCi?ilgr"X#1+;c0!cLf+k8B'P;Z?HZa]0YMXj,]L':T!<'->
%Nmd@2e,Q:Zcn5%K]BAV*hgqKi)E1soaQ<GLGWl%A`DZWc(=hBVC)4FaeGFBXZ!OQiVMt'BgWQ)`dre-RT7=F\I&RY6WNt^#'RF)U
%940V%hYAP\,>C'<itL)4S_5rmkMs2p]>akP4:?Xfc?M$]"uY+Cc0!>lboGa:k-/G>St"o_*;j^C_\t`3k:O!=Zb+EXDm(h^f/T#h
%l=G?WcLR$@l$*,D]XkcUrRp;=p6NVV\=ZC_a&mXPk.Y\R6[s/spSSC&ah<p^emGeN\K@Gc'Se*/+%qXbLFAeXB!_.YT=eVk]5A-a
%[3`F,jE#?gHhGA6`Pm>/n:r"S2I8`,L4?TT<VZ"Kp#WeC.tWN1g@Ej^=(c;rH$cK&^6DI#'m&#sb`"FVnKZ(?IPQ3?p?oP2Dm+'Y
%[,R5%5rI`=`cdr5]`dKjP1%M(,fUgkfAB)FGQI?=X7E?8rHb[:\*VH>hEmOJVn1f5*!Nrh)g;0h*4#J9/\?Ld34u0BrSO]=o_4&2
%Y6_E4>B9UbG(]MMiRr:&K$RL8V,+fg]=GJ^qXWY2id@:Lq=l>l3bj.HiAp].q*"`![c[<hN7IheX\HL]DU-4Z&;+#'[bVISq.@UN
%g7&n#c"-P0[J@f'g[D;jc7c=RDXN!qp>'%=3UR1Q3I:=qhtZf\qo4,g&UVlDV[qahF1:JAV7RI;d_/KG^0+lCgE.L2,JtpIPK04j
%cK0!$GLUf*^TH4J?,,Z?17._Jmph&8]g&uT`FN:9bg)]C_+8JsYHHGFls+CG6dMpL5PsD"EG"fqhOiW+4JL8E>>oPmVXk8IBj,"R
%P]kfMb15Uu`S$-YnjSU's39r"nDL;1[O--<HaD`*S=Q:e]70LGR"KZ^F5q+3/$CpUYLe:L8ZQ]&A&TBKmh.h`>9<g"2eo\t[O-9D
%_)gt@q7V6*H%5XV<E$6$><@BdoVC5!>12e597_$LDQh[)X5%NlR`3t)jn1)-p@D';JenF2G2*Ri-p+,qS\LskPVnZX(\>Te8#!*=
%L0_P2LIdYXMaVj,70CkC;CK[+\Al4?79QraqssA!@Sr`LQU<=HheRZk<.d>j7<oJscmm>LqlqR40.@ZJc"-P0[<[n>./k;2(1l#>
%gi`&YU1.!F?W&GtR`Xn(O5'D2PZ<(8lW=%]`hK!<Uf`j=PM):,s'p@hp+`FVeR!\-f@]4lmPfCIWX,fG:@'&^`Vs\l-QU/a8+<8B
%$U=[af=t]eR'3u,@G&!,=CX'H+T6*sYj@PR@rIX;IN6MSJZjJ1?RlAq[UKOQI-n`$%X`+Hpt@Ci(<SpJHWf@^"`<OUWu\_(5;lS)
%#\u(c=?CK0"'>ATk%_4mkj'hq[s)[\<\2)ElB9sR#N4PZIJbt&LMO8+#nGSNE/cd$9QipO75U`4q@BV.&uW1mP/\:"nGCDD?<mI.
%lKj;0J)kF]X\@qT>A1u"?/E6.G]S0%I'(PN8>tu0cL-1e$F=/U]<2%H]=>S'(9-5B967nFq%5nZ^*@0W%o;A=9='%O)SbFim/\oc
%LW#)Q.a=/UgH$gD5OtEMGNAAdhsP1RJ"J<B0E;&bb<HtpRN.[]BcnLMpp+\0dEAQSpNKGU[hGCGWmLOt"S&[bc9GbTs-D00(ZodA
%H+nHmRsh[0B_R^lDm-pfICn_%^O,\pTDBN&fes_a$"gZ5>[W8jn'B>UA*2%/7U$Nf=3TX60VFCc`%>.$<m9O5H#CjBn+JrH@9.m7
%,-O&>]=e1;qVlr5leSVnn`-#+T"L5e242(',<le8ier%,pX'TCOaF]!_*EcL^OuQ1s89Vr1*kbns1>=JpYc'7pjWhRhnO:`LUn`9
%s6nil*XD`6IenOsH6a#?E1a7s,S*)r*OV:$\GbUKo0)kr%mJm3((f%<XY3aBBlCB8efjqLip,h&q[ZKilLIa.hCf=ucO"i;`U"ZF
%?u'f=$E<Iac'roq02?D'(=hS[%;U4\MlfP[.p2VZ]/PYX[.[I:2W&N2\*G]LSs'u_.'9)g9@"/2=NCsfZ@dkt?m0l+;D6Qep-MCG
%XqG:VB!Ne^[WOBokUW;(l0dQjk-IKK]_H;gLbJdMqq5=/Dm(e]5J+OPrRpSog!-jPm=Y@R)m#g:0P@!UfB)\;qr_]CDB)$Ngt!o*
%/#IB-[J[4np9UacQH_8$\7CTmP^)7i@oIh"MT2VNlhrs>CXhtE0$]f=rU-#%rGE2d<`pLcs(#mh_2<Sid=-aojmc-TpP;#2`kD`5
%fYMsBe)hn1K<=9bll&ec(g[],Q\48Y@!Hs+;?t+__=2J!]R7"tQcq!iGYot"gKtGFQX/0-%u-F30YD4_/&%N5T:I\ZIAtB<?<`Lt
%*72VoWO+ajTYIj-g79oWHn'ki!1TWWD\HqrboCmICgmiM_leVdXP&@2fYghJPYY3V@`K\tGQ3*(]"J(Ag#]P(XMa)._[LD6+3+Hr
%dr%:.#:-/eIN6rqi15?65>i0OS%%p<_"=oDQp+td&*QXkoVHCFe'EOBop=R-]tXIE:o*4q"f1?nA`^,2eiI@'mu0fApZ_#p$:V[8
%;5od!D<kl;RDX*9F(5]0h=^*=oA&[*c'u0YpN.AB/mb_lanp8EJ&cX.8(7S=41kAFQ%<5qR?q).qk0Xr3"=+[pTkC&d-iVpY6m_I
%G2%?3HsAIqD_K[q6!;*B:$:0l("6;195ROj=uV9q6L>P"g0r#]J1Vl0(ji1fP_0udO*$i([puc]@)lA*LP$UWB/SLn]CQ7h`kd\?
%CHHUA@iXhPHXb#<%IO.2<G^2O5C3_5g%g[KqQc['&9hdMpLdRB]tIYaM_2^@i.S3//Z&>oH%1*hrg^B86fFhaJ%WEtQ$'aeH<ZSS
%Y?$2FHuo#Ti`C=?s6nd_c1r8(k<\u^c0]n4BkqVuOk3uKh7UB!h/`DlZKh50NH]Z'%LCAcTHS[C%mWJeFImtUFIj9McEg!)3+2>?
%3OPu!o=.jP8ikecXqBl9G?j-UF*DuEk!e9A.g:u0A+]O5fUu#Oq"`s^]5QP3/N7u?NIienrFD0q^$Cu;:C)9P9iLH"-$^L>V/%pJ
%QPAqsC,1,Ln?lj`4!*gtAdZ-llntBuYFj#+5<'?`Qp]j36Xn`b'*%Q/ZGaAACPo$bpk"Ip.jCtl(0g\pjjSi7[p-H!\smcfk-f>8
%%bf](Rlb;)#An@*g>!%'nY".?"E!Q<=g`2Eg^;hmK0$ILlJ.8V3iI*)FmbpJ*RqWP3qnaT:ju"$goAK_NB2:M?[tJPDA'Rbdf$B(
%gXgqd4'V=7*V?n/T[1<Y!XpT4kQ;sFp2upk$1rRB#&`mL,nB^;#@**K`0K!7jB`>fqJcKj[UY91>/XDiYl(]^>b.Gcq6M:hh0sQk
%IX1t2^TOkk^:jR*C5nK^gMQ93XfNb\2tq"II.lY?rJD*A?J_CmBAHg>Z<lNH\d,Wa?F4hDQJjAj?5@J6ms98[CG^HbI^Vi[fP%t#
%KD,8QX)dm:Zlo/6qj?A*\VEE%f)/S8RMOX?\\6rOlt^^TLVMKmlrM'J]R>"uk3W"Tm@i<OISaGPVJ3+aj172<de^hf0-'2o`t`ge
%m(Y0VhlZGQ>0Cc"Cfu>1F)eVcYEe8<WU7-k`)"_q0R5jIq"0J;IVhj33FmX=.0m^`i)H?DrKQGFS9IpOrT.D$]=kaW,oJiEaUS\B
%)!ja`5*(26&6oa?h*7QGpJI3358DiDc8!k%:p_$*d6T,Ud0K6=nq4UkMI)eZs!-jKn+`CD$0.7oIs:Ug@)RL5]2q+BIt#Zj5&UW_
%g=Gdt1Pb/O@lEH\G?Tq%o5eb,k2:H1+4fq]5AuZaS_[T.IsZUKWdqol]oXm!E-K!HHM?=;X3:CtT3XWrQT70g43Z&prGc*6YjD5H
%\7Qh+rGc)K%u82Fk8Ed7^Pa25#p8$DrI5d\d=Q`o2RoeSc_o`Uqm.B:F;0(I,$n:VmK4Z\IN]?;A:W>DinWm1khrAAq6nPGAC#g`
%r/oJ.4S>%<5.lCCkF0(O_gT%'49'D.T.Sm$J>LnV8*f&u?b_"b7+5IXG#u.s^Nn^Km5CGY=T[JRL%tH8)0+ED6[OlVEP+VSh0G*@
%j-P:Zn`d,K>FEe*r_E9ngV!K3$o7A4@d?3E:j\=KB+LLXj@Z(:rUH'p*5;bLaWBfncJKbZV@`-$b1#rOXiMjE[p-66!qf34LZkgf
%p#b9%=-n,8["%Gr%YpeoVW7Q[Dlt_lA$,]LU\2'=Rg8KDS*dZ+9n)d'k55a:a[TUe,Bn5hB)?YnrbXl\R6NC3I6(qHCc+2*<?/jf
%puC2`c.b0m.Oj`BR=B4RO,jt'G$aJ3kT#\t0RUsC[OXok9HR't%Yq]FIW]e[E;mK4oakU.BgO@8d0MqA#h%$_s0[&Xs75<['glG6
%-IUM\]tO7?dL/Fl*%UuGMa(M(bl@7_TD\CEoY^_(jU]u#gg6,llh:4*ri&!Ur22c!7pg]Ee"=;-r4h[`GP#81<DTIa^AQB!97_2[
%Kbe\QP'=UM7"6KNCH;7VN.-D0Ts&p<s7JUC^]&;JI=IKAIWP.2ri\D0]mTOS5Q(.$a0d5nWqmKlRWJ:)aLrU"K:[K"7$"NDYORF)
%iDjR%&CJ6bXU?j8"7uMjp>0,dR_mH4pQ1^_C'b9]oiHS[2ppokPU!Rf93^$%OYgmV<$Kc/Hp9ZS%`5:+1NcOBX1kY8IB:15YnF1M
%=#nf[oH,jDV7Y:k[4P$iJNHWs<K4O$0tbZ32De1PN.g%p<EF;YDOE\2>[R<\lHrt7D1%V+ViKO&=)1on(jqOHeW:+M@t,3pdJD:>
%9MK.3gT0Ia,^Oo>9:!$R<M0Zmj]f=)Z!+d5.`^Ok)&Kk7]K^;u[TEEln&mc^k(MZ(#*3qJ)8Ma'k^C,uQhsMNq6R:)l'Sp"/P@d'
%L+g:9r;?KKocJ)?LS=HT?dgn-H9"O"]O1BGdHPF0<R8^i[ZHh?QF1f38#'S8PeNYYC@$T!V=Vg)TeBpA.ETqX>5gh5>?nTgj=6qn
%53k%8N1"AP<GkrPl-j[)(>?&fAbi2S'5^=a4F>e;T(QV?T;_q-XF62<cWQ9GF2?>I/N@Y8eT)^_d$HK\UJmkujaUh[Z!U7'0='6l
%s0MGP4e!b<H/RP0r^1T.YSYj2qZU;oN>]X$*^Gh,@s_c`79Unj71O!o)#a?Rk7W'sWqr`,b0:\@'"BA[T.f)Cne)h,-_<:ZZJd'>
%SA!K`]:>GG.6k!pb1ti+WMccbQofe;[D'#TDsi00m?]c?esfn5MqbZ#K`/X*GqZnt=dsh7Sd(`S#NdRJ4,49^I?&@(nb\\[*\PNT
%]arG+=XcLE@oWp&d5?B*UJGj_q6fn>j&ZlE25lNBFPZ`Wc+WcK#N"u`b8d@AThs(:EX*VmF-K"Gc;!tZ?H#,IrY8WW>-^h<d@@tS
%j(KsD<303>2rp`MG$Ufg`knT7V^[mGfhmHjJPqL44*0I'qUs0m2Djgag\f:unYO_+ek(5[7(tP=?X00](\.s7?M;dnr3k+h/n\ep
%$DYr3e'r"0lHSSl@9<Clrl)Y+?iB;?o@8?i:V):1&@H`MVcD3mC7P0s`h#.sQoF\/4W6c`iL-grVO%#dhnJq?Tnlaog%'FF`61Bj
%8RGG%kK)5L.)(]<ZY)uH(=e%uZWlVDL7@@LKri8KT*N^@46$OK?g#/$2Jaupqr-@(Olea;ZpAphmT--W`n@W)`K#Yd(g6_cmp<,H
%$a+%JlZnu'oNSq/5M=bYT_IsgGneF,'Whm:gYN`.-[s%t;u?aTJ,]dQm*-(<&;U(@=a4_r^,3ASC<DPeUe+XKI]SmEF@)nOd4r)p
%DV`>HCSo4EB--fk;YG8(eBWGBFjT&,l[*_A%+#p;Y(fN(eN(C1NjbT(<GLOQ*!7aogh!pb8%5tr3;1hKE0-\t2QJ`23N&ln%@m8T
%3Ss'uM]IG?pM%_g#MXLUa;[4uK5n7>^Z(4"g<pq4R7m_"F`+RkL-GcT=;PE!=+-XK?R&XscT-=uN/+86K)&PZPO2jOC6R3)Y5,>q
%]pIf^>#Ut43Mf`:Ft:!AhqFr7g=(!.r\on\T=Y0CdNmO(ASG60(sK=0D%=[KmEI;;#nG/Y#g^Y0>po71_)1Xl4[7P5==ctF;ZQ?u
%3W*\r]P<LH>-C7PF16+sHO>f%:qHsMBbnJ#S?qJqp0@,oF<`4#392+]>.jFbGoe#QhgQ&5U:NP_Vqm:f%4aA46Tne"551P6SqQEo
%noJa8iH%!Lm'6`=q(<HVgjKRk@i`KLS?5]!g%'FIl8^pNUl#u6Se26h6W[u.AGAd[\,6_+@3##:nfm77f!rUR(Fo;,pqmf3nOu<&
%lW%f['H*O1L=0Wrk@;kfV>88Dg3=\)RDlm_L<aXCjhi+tg,^k,#bEf5Nc8JhW;2IA:nCgK(HZhSeV,l(/8oEMfV9K+oSt8qYOs<N
%-<'fC6Va5lZX&U/W9$2.+-Z39LAla`c[Y"udqQR:e)B>YgSSm1Lk9c615"H-<&kL*]BE(n&tZ)K.5]gW^.7MaTU+bM.?3c@1"C$&
%8W,`QlimaAFPV0A%lFkhFVNa>cQ1Jp#(c92I9Z^!(JjmsC>hkjo%)fJBdMYAo.P!C[s$5iqsKB3o_``ja@[Q&l0%#H^M_rU6e_PK
%gG@a\4F%E\Ia6VdZ##-W\jE`n]>,54*r4KbgV9$""o0.&I31)\g9$uk?QQYX*5XC.c:<n]UOFiHV2m^L?T8!>9=kVX1[7SPC"GX0
%!a;Fea"k"LEl>Z!mCfui05Aa2FP)ZDpa8eekujpr+n9/DkJr`KT+G5<pMHb]MPfOB1\!g;7fH8&ioc3s6ce03qJY4$?b/qVs'$)W
%38:J/<79dbL8ITA^8p2:HfGJmbidU*T=ZfZ^N+Dmj/(]rI>HNEDmS]7H2Sgn5s17oiS7i(luOf):<GGL9n&&`E+<l27]9rW=nP;;
%*PZkd,1qh8o1SR$h;cu0O7,`S6;B$`f$Q$RH/2^"*mLJMcB5:P?eNfZ6a6mOmDZ@lnqtE$CA'&W`_sMF71UK"4*_?XnR]rbTm]M6
%I0hK`!N$jh,5&+j`H;qX\?.j%Zc!=pZ%Fb=4Rt1s`f:A+PHRD"92U]N<N%/3O[XHS-'(jG[fXHmooBQq!im](WVBSL"c?/9e(M&P
%`<]%4nHqe&Esj.P5cm)Dho#f`L7*L8V&^LfE$!?0a#SsPm"KU//j)P**EGIn`ubo.L&bL4S-FFhMa5D))u@mt(caZ/:IZ[E$04'X
%26Q9^WrW7r\j!AP)Vd/QNuo;cD8:f%fPkQW1M_c&UMY@2/ZniRm^Kk&HV_j&@1J#MGb[gqSQ@d/a850!3V)d=EI*"6+W#0:cFDEi
%[W%J]iW]fkdE_Aa7t/V9Tkhr-g-35Oc_o+'lDI^&6#`p/O0<=-ckI.</p;'7<Pjc@X8p><mTrQk#h,dq)])#sVHhE-/.nhmfkn/c
%@/'$2o/U+FH_u1<YJPZ[]=t8"hq[7!4:lG$S1gG=[u]U>L\Jt>4`TB;`H-qJ5(hPrY!`*HZNK`1&[;7.NIM6(Rk"GtbMc)QabhBs
%c#NB&bX&7;S>8u3Dr1Y8?5d`g05k)aIj-ZOf0gNgE0?9W1=%P4R6UQZVFIsg9P^0)!Puf;n^Gtbi<MEgfXs"6o!>t@n3:Zik]k&u
%p8:_Xs7DWeXo)o3IoOL<EIAEVPW\8VJ*%CA5L6[4^[^Y\"94]0psf;Hm&//LZl<(n_jPl#mJE'-GhsU1p@3SMR.^IKG5?fGpSKnE
%VVh07O8/nHVm;MVKlXW=Z1b]"c9%Tc51>#]EAZLm0/M?pnJ*b@fg9mtcTL\<3=^A_-:%n[(CM*\q*TC)B6#!Mf\'\sKPP&>emV,0
%mj#?h'OBjt,HGA;A(U3T*b!pG4oRfnH*uphaT7H1"E+)dCH"NLa_1OUdjm"kY.CIu3U1(.Ta/bDOeD:YkA@=a`lR1/pkR4HB4&t\
%*kif:oC;ps3)AtRFUOQ.icWJ>bPA6YdbfQRAq0jDE9"f9SRqT1Wu#nW\'cEqge,HOq[Q2J'cR-LJ^M@pXtnlQE\?q]Yp5=$r;0=Y
%]l5E6Sp_n^h9%)dDs=3S5K&b^1C+(%#Y']Qh,[G&SU1k9I"mW9&ZV%spQ:Q:X\VGN#0#qdb/:r2b;VAD,_$nC(H5E^K)4[]1@3,3
%8'Y`l1e6ZYk[D"@X`T$[6?h@2bKY9S]3c?7jZ>Y1V&smPk\afO;a58,'lSb&A6aPi&WUf9)o@T=@j?qBIVo>&SrJ8"if42g3QJcB
%fXLtp`'u_\UH*X:KH7rOK08<0)aX9g7,s^;a[Nf'mlu6qRD=l:0,rl4@j?pH\^-e`^44fAkM_^Ik%N%u4WhLg0^lfeF7q!Y!)^_G
%HgHA*;8mJS?h_C[H9@hiq',dAqr!5s)8MGu#re-sr"/F^GctX'EXcIBiAY<JGiHfo9Qc2GC"G3.B>,%3+W2-3Rm&fHfFc(c%-BW5
%F!m%&>CHSS2!(%Sod(aI&S"$05RSa"NkPN+1llU7esm]aDN6ohUEftiR7p#`3<t$LdGE26lU/bjAldt0:\TsAUL;?#X]L7$lqn)<
%_Yh?gcfnf\1EQ?hYHa0<dHHRF6e-Lk#2gI[P**A.^fW]Fjsa7LEl&Km?_#l[Qf63NH?3s'krjgJGPsZ_]bW\uC#1l&R9hW.#0RfO
%YL>7]-9\]U1>]aTO86NY`"_Bl"#KK,@LPO[hHssBW")b.I8<Ba!=u5Hc0m`<Dk>@c:@jrFs%h`Bs!'+9!oU*IZsEAg6q>q.)Gr['
%CB,m1\\AFM&5\?dBYHhtN@RIp8jZ"NG1\`EeeR?PO/<?l8+RLp>4+Vn0lR]!h&O[b$_gE2BUmAQ*^b>=IJmsMj#`9D;d\tZHi)lb
%?fCUEpq_sdj#<KdaBh%R3$3&3EAhIj_m:>5L`2Jt:&Tk5.V6RK(;d#$#l%rg5Q&ms?I*=^SF-*Ca%;.%btO(Dg7ajp:`)RdU2*ZX
%JYV0l9\DHeT9&le^A@.=q4<)NrGs%?55<V[\DlGk,)"kR%>mS=Y=!:)_3[b#Er*G7!8boUS_24(LW6'/p1]F]s3\oQfIWR*rUU("
%dNn0ds-4l507U,(J"^(AYc#8lqQH#^o@Wu='s`<LrbK1'7mh[r)<^I;p,CK'r85j"%M^DlqEOrt[3EB\l>M1Lo3_F\QbWM?#[-&U
%CB_i`)D,COJ@.Q*+MVm*rp4RLT-Gb1DLDB^GO^0)UlQ\bcq$VIib'@C\^$iGN6C(lqS3#]C[m=tpfHKOZQ6dUrSN"2)?(EZ;>uI#
%X`@B'Cnrcn:sUh]`kW!&h,b6UfjcFqhB.([s1l/Eq;8ln#;c)3fDQ)hE$$;h"66kf&9Y]3&D\nQWrp1aD!M>_:H:chb`Pf;!PkMj
%8$M-GIm<qckXYP0?Wr'B>,Ch_rr$bboo$T_mcV@aV%<M":Oi&\T7?V*Ig]#ZO1!ZO*tWMNnnL(EhB1`_R.;NB1$&B<j4-enna=Gg
%Yl*9F/4hf;*?;Ys`P;AgpclVc9pen%>*0n@ak$o,-I3AZ1NRhJIOa05Qg]KG`]@L7IC5)N.*0]C?Prf=s&$hG"CN5WWZHjoCOXra
%"YeP<_Z>.I?T)7t4#Z1;cS"NSS7uPMNm7FrpclVf0niX7gs(EOIs>:3II]/`ni^=YBX?<-f'SqO;u>quLAqk"$2@uFUsD+DPG!/k
%pb$nP]h8G/k.Z-_`a>sak@K]sVt82(bh/tgNrC8>]sU^JFFFo'3[a%S]mNc].Y7s)cg@QBjB+`C>Onl8lP=92a/\PUKDKun+(OVJ
%+D3BjkFud@K]_=<m,=:^fDO4qaB.tQO0J`nHOQjXDne"*O76#+nQ`_;4%:KIOWX#7W*o2/bW5[ujlHO2d`]NCGhHYBH^>mgs6UWB
%s&4DH_J6jmJOQeTKu:fDqco9^(^@L^l+rq:;ZKNa+E"IkOA=)%etpAuSED&-n#>*'8*Ea@.$7)[o.=e1cm>:ps*\7GJ!bkDCT$a=
%/43r4gir!3@ZgW',MkT4ahtabqlTb*Jq^^!=r]%DNmGcG&O)(Q;4>4der7*q?D^T_e,IJdjlIG4;1/1@gIh:C@pAWW5*8()s62a^
%!i`\4r%V%*R:>WY:bH<0lW1$Hd3j:H]Patm`mSU10>91=O0SH13A%X1hlRWiL4#E:I?.B9;kB=OoBs_9LZ!7t'&mJnGOKr<rf=G\
%YTC*8<k+<;Qc4h']M$!U'@4^Vo24Z9k$o8Ap=Lo<M-idQ&[pOg$?,7!6(O+'WLmnn/8/cLp5NWMM;qu\mFdYFXk,_e=JUs?2X^1/
%O8gFU;_I7q[6:akPN-m&^H,WSgun:o6mD6QHVX7e@HW6Xo<k;V+ElV>U]3=NNl;3[RNb`*+^Tj7FM8-2NTY7'dI4u8?F8HJ>"")[
%YdWG<@JE([2^MYhoZC%10NRh,3P2+-jUM$*#[<Ab.Y&Rn&*UTaUSfW#Ld#-JYt1Kncd+J-C_'c+K_]goS=blAI1oG+\4%]9K'tIt
%=cANc[Go`Wh5A/Y<Bi_2ZQK&E,V]B`LdIAF^G,YJn53g?&g>Gb@6j-)`e"$r?=p+BT9gY[UZ]DjjEXQ9Pjr5KIkq"aZVi>(cmB]?
%lcZZV@9T>tk-:[\%aF0LJ$rKAf:)jDBmg]Zq.T[JiM04N]9'_W2S8e`Vj88TPO<V2[R@[coDFO;=<k9-<Kj&9s/5mq%=Cl>U-n**
%Nmr?@LAEEmn,78pNZ1fea.H(lOi#Hl/)dOW2YrL(71AmtQXJ^oGoHY1fV#Yu,Kh0ul?p=*:JL`4L]7rM$0B?8%ILA=i]nAmA*+&>
%jh6`,*Cc8j4TFcPh.'IFnMc4&&\dKHGeG=LUE(,11RWu<GZ='J4D`(Jbn6"$)iR3]g0bN%qL%p"obZ^c,2:>Cr5BJeo1Pcl>f3nt
%nYqk6&5TIAE*N<fO'06\6]pGS/$Vt*^U[Y650[UX>8t!:fr5CmL?^['Ic2-Pr8ms+52g2FUTNhXo_EWo`VD9hNd1P9n,KofJ+c%c
%^Wt3lpc#\Q-f).:#ktuK%t7;@i_+jj2o@fYR,uP&I+^s;=#rUHKE]'gFOO#q3=?2&EUl>>I[ui)3pS!Zj-]3uB)&!Hcg]9)I6@Kn
%YaRdQR.Uh^_A?Lg0:pFt=8D5hnYOe&rVKY0.$6T</W6[kr\A)HDhQ*R#]Tr$r9<g"-aC6Gcc9f":CdE2mi27KJmO33#8Zodn;b5M
%3'AMalo2\q#lc("^\morIeV%Crq_-E2)pm$rU57/$PPGtDu[5c4$6=;eM$sr=9%<)^L^mSrshf@Qa_A\n,N97J+MFP]fZC3P&h^n
%<RLlOorF8QfCYiEi3o"3I[HQ>i_M`VE<#TT^O#q'^OH4Smsarq^VtEsT>1=$O2(FM9*G8:hu2[Go$q+Im^Kt,n)oo7@@k?Nr?K\k
%'<G`8\X!]SJ,#JC?HSIAGpK+t#/VK](q9*QDb-7VXqE49aU"1[jCs-87J&!RI@W;)N6_5cZ)l-7#-a:]=?FkLL.)UMW($d452D!N
%!?D=hCGX!26ja?1DoVJNX_*n`S<:%>a&aSu+OGBFo!T"!4%;c<&0>pJp!W'?"uD@XmJ]#\:`rF-F@$.:f%i9h^B'Bam2H#=YG#Dd
%:0<'f4i.Mq-];Qp&8hj`+hoQq/p4aIWt01$E5l%ebXY14%nr:na`=OJ&3=>aPVcf1hOne<n3uOu#Vt_]ZW2Qn8lU7_cqJ9kTHMO!
%^B*Q/''r"TL!1RMH&=9BA+_^RTfNH*^n&Jig"BN2bNkfOWsh%k&iK-k<Z/:93:S1TJaiHL]-EV_DOl[[Y"&I]@?@+[BNn&q3b)3#
%UDenp;,#\eKW[ns#cOKH2BV'fdKjFT[cf5V(-=.#a^AlhHLic.H+1,3N+Cq3X$:&fP&@_7ak,$4W3lHCNXOWo-`U6a@cCN/Q<f\,
%LS+<@''Bs^i+fMJXo5O::u+ftZLA7-XX0)`CP*b-fW&K+EdK=hN75:RlIi%"$\`G\T+:anBY&cY#K$@U-3@6D$"V!Gc7?figC&RG
%I8j'SEFK[p>2IS2Zl8+_:dpS;2fr1r4_&!1(gnnDD$Q9RmP07X*Z2[dPBs21LOK_s)+HE_+o=eS`AtdbXJ\;B]MRW0)^l;m@d8XC
%EcH'0-DB*5:`qQg!hrfb7+SQ&;`R9sQ''tPUe`-$q!]'9YkKD$/IE@mn5:n0I.7r+$A<^E8<n>2D`\lb>@@V'b0OAbfaDG.*f]h-
%a]b_P\2qd(X*8X07&f03VZ&Qaj%dJeYq46%_Xb+u%F,14>)/!XW8\gelml.RjZAB4H(m94-f>$(<m"%`Aj56VmCHWC>3H!Ud'*;N
%SuZN%>W"mc<N`QlSb9.e0C]<2Q3#3T;m/h,ZKLl2Atb9"s6*"j[_kL3^/g/a1E0PR%aoNrY]u'=UABhQa@k88M*,p,;W1@N)L+g'
%FI'mmEJU^d!9Xj6H8TK"mm92bUY86+&p=Tq%r:(^1A,)CjDI&rRKL@GO5<Q4*dL9FhK&u,/7Bko(5/KnTTI4cI4Wc,o;[":8J_j0
%9@;'oQ^*r015IeJ65NUJ0q(#MeBRFUnu/?E/6>rckBH?\q0h>3V%jMfn"&]=3PLFVXO_XAr3Ato%#Oc1-)#EnoetQ+',DVH,CJ;m
%7C5A9e`;#Ip2B#/KuXegDkjOOCP5YfI2)G>h*4E@=e3@>MYCFP3Agb(afhS8go\jWJ6G,#4iP<]O<YO@:@uRD[WLhnH+.LWUjf1U
%.@Itfkb'QB^8$?r0)dDfoS`#Q\REeb5hr+3KCPYmmt`rGCV8>(XDUbDPg/FM&Z.Mn.*lI?b0MU)[.JEND2eP5OU4Lklr=lnTb^fJ
%\A;AUZ\LAeS-'UP^,qA4]=ad+4W^;JbtsUnHI]>EGN.+;BC-D"ce0b[d`qj#Y3-P4T;e/M]J''ujS5K&fm7'^)iC"YgZ?h/X<Q3>
%.^DV5Yfn?B!:'3\$&&F:/;&D\_A9[1j]s^_eD+hD34d<6Co0?YliuXJ7mK2.8)h2!+?*%#o5%Q4+2QYpflQ653l7TE'O*?>$cA>.
%PX,RrDG&0K%(6\9F%sXk[RIm-)?m)C0k!/!.0R7U(BUW)P*WBTMmg<9`_>b;Tl"WZ9YZet/@7#Kd7)H-/Wqk2+cb,2C@MTs]?MYH
%0#MN?V;LePIY76KSZL#Xi!Re>"X9$NBNBua3)bYP!3tdm[W/r>?$cm>k1rFMXc2bsA46pV3_l17X3eWGZ_E>uN-n=J?LOVE*"e4L
%9&ZB26kC;UarU79/ENXR66N'$)]#"jbDV`2i&:8ob7M,iiVC)K?1rA6],a)dME73(7Z*dAN=e);/p_e+\!I?e-^Isk!tG=f88oV_
%ao8]:jEONJ:*UQ:DEj*\2S$,!%DN]\@P#mCFn7YZc-!)lQcSR<ISr<<1Kc"1J"Bb:FR00u"AA(@>.Fr1R,G((!$>Oi#m-DM9toL7
%f5cK@A/"7@ED;SY#k#-o"34oa7J`+k/@Ou(3a@DM!7V]4%aWGK/^,OGY?KEL)?FOl8`q@Y)ukB9I,,of*V'"K]DF'2qI10$?baJd
%jl>:u&_17gPL0(hIdBit*Cp6o]4ag;+-Ro>Q,a&>OZ(IC]QiBT"e[&&),QaaF5gAbWrYm^lJC5.#(]IiF!$oNLMM5lE4p,c+"rn!
%D.o>JAOK@KG6QX5kYtrJMTG"kUsd5XeD5Ch<>fpU5D<T7Q`.?_LnlsZ9!EnBDXEjH&8`]H,qfpb,TiA3N5[gs)nF[6;VpH'o-hm9
%!'4J<7#XWl*XjfF``.f/7d,lm:bC;DX+-e4b(^pq(WCR/68Q7o+/6a%O@[lDF6onZ%FL&:9^$BZb;/P<MXoD=2+D(dQB!V74DTbK
%MCem1c5E5iOoY1]H0A`<V#.Z`i2.3b!JQAs/somU=Wu9RJAW<Rnekm*IK^D%D"2#HpIbZA;iH$?<W:LqJU*LC)dFV<ib+P'-lU[@
%BIGq/]Q>lfXDp?hkb?L7KV9gJOX"j8HKB3nMmW![Y7icaNm'QqePRj<[K\M[6bq7k(G&V87`#bG6<eaY'U3j-k#WTHaNn_1iZ3p9
%Inc.m'Y;5)d),4"gD^fZ/O#*-/PAsYj^6N#-nO79<;l=,_XWKbBZ(h"3dHOr"JR2K-mG3!c&n(l.B!&OGK+6\lX,P)EQOC5^?+p0
%j+i");<)$$m_F`8M9`611-Gki(+D_0&RML'`XGQ1Q#\P+AA3-+khV@Gd+%a&Y]p;N4!rYA<A=bSi6m-S#dlbN$".XcL5)TqhZMW1
%9G73RX>h8S252\\AO?E]],PUGQRND(>gWB9r@UVGKXdCobg6Hca+ZJ!p0uGD7c"'nIR[Jsk2[X!X>3q;@+PQDiR4Sjc6T)ASt%e>
%SGXJUOR8>3@UrS$_B^[o8(c4V#$61*bn'E!cOF4LhQ'Eu]aG1r.aP(8guSji5Q*`K1Je=me68]hTpEhjf"S$uXh\^<E\Pjf(NP[`
%*]`Enp@c9jT9aAr!Q$^_KtJScLJcP>@u*W=Q?>i#gmGu+g"fnB3FQXC`V'QpFqeE<?=GB(I5.0ue7)i8e&%YjIGTM`?BXKP[rX/Z
%KNE(B%P-4m!R<=HL4[(8ijhJ![3h:5<tZEUS[O">:2lAGR!-h&@tf>.Ol^f*ZBOg<6?8@I>t-SYDQ#Z7TS;m&ahq>d?X_KFVT(+a
%g$*kQ"JC?1eJ4R"EL1,2<ss5jIEkt=AcLSaX7Nuu<.3o]%Qn74IWDGpb]A&?S_)ZF4n2f+&*f<lK=WWl8E,Kp6PaS&J%'IOf1JPl
%:8'FVLT+5"iNi9bA4T?sX3#:^-mlTF+JQ#U9iCTmIG?=e<c&5hl,_l[0Qo8B#YRHXc@W+dWMasLH7>mh$fgs6-r'%]b+G@:(ua<>
%$ap\>ChB;e,7S?Ph@oiWnF7)9.-@WuB2W*,FAn^])PN.I_;SF><W:ZkGpIIZp3aJe$C%c;LA-k,ipgF52^L@^$;YrTYCtIO`A2cX
%=RT+`)nR)`DrOTS9'q;8/L?G7;=-HB9=H+--He=gdl1A"cX`N![.odn7<i7rm+t+^H-k6hV!VfFTe$??mE#%\`RBTHETmPC:[PC*
%DUb!+,1hA2eeNJHSlsAGd])@R;T&7L)6>bMfNK7Yk`'%Gg;WKYe:]tfcj;+3"03p/)\dk9mPs%-JbclJ-g29LOJPjc'/p'm.`Cer
%rJ6^_dPnVskoFVXEo"g<eq'Q$/m(56aJ`%Ab;mF+@;8rjfl9#h21]VGYojpu!W*)KMl8Rf%nFK/!ISHY#THkrZDPDDY9$Q)\7f&Z
%n6&6!-/m&B4J5,6V&qB'eJkZ3)OX5)&#kd7adBNR4b:5/DCeoNRM4apNm8hW--,=F=EZ`<aH?9F]JLQJ34-7,Nj_H%%so*f1VNX1
%?%1WhfDk//bB8+DhhCj\/e1%LTpUN!qMHRRcphFhT29Ru55p#1<"1M9R*OZ`ZFmbOjkk/FJ)W*RJuL].W^1?QS&G[2XL;Wo)bcS!
%.LQaL'k'2orLWV)4Kd]+1\8V*c[Pr;Q7b$5lc>nds)\`^-ILLS)buZa\XQ8pn?ZK@2PHK$P2B9-(hlX1iu4A2QPk8`(X`b,Ea1sF
%GMV?Q\;Ke>3(co3\69Bn#Zq*Fd8_ZI/@RPMY[E.cS2GGA66X.1<'1aOel=rupChBO8)_9)"+d<:E+8D:3`00<ojE1V?O!.#L7Po2
%l?V&Cr;5FdOAfBV.nun.UOLRO9P.]C.RV8Z%1dPId7.[q5elc#@;0I]`>ub/`iL@":\dZEp73d`P+$%"S+:1<YO)fR,=gUQ^fI6G
%OUM.OR4K**7VV>JOk##&lG$,'>9[3K"Mu!0*0aTHY!<S!gYIkAVjiN,2B!<AK=U0D4f15$N.,qWJ0he<k#DsOm@1YeU:9h"-VOSj
%=PbGk.FU;Fc$"a1rNS;!'"I"7%qtEWF0NdlJuWCA#V5'b,G.tlcN#N&,fFkBAW6-`#ac[,]?E:Q7pGto&6.R\69d1UZ-MWW;(Ege
%6&&&kVM]IXlt_VBII2A%*#9<\N.DiSOaP<+G[;)b-G"_E794Z-I:qHakKnXJKa`N!V+l(bXYZhp,cG;[hj:?m+p<p3Ma,242KI>8
%f&TGoftsl/21RA^:,_Srle5mBA`^sMBXd&XPNqaQB4\nG2F96-YroPW\,gS7AlZLA#<`Mk7ok#>YS:u$;O@Ki0RTD9r^?;eE0R_>
%fEmI21\e#W+[02-c?oj0P^4^Loh$u`Z%&+e&'e0)[PX6*",<K1fPu%?:^$hN*2!FuC1lYA7a8e.`Ii9OiEa>8]`YJNR!,V.?/cS=
%ab$e(g\r[_*TmV1,4kIPAs89]A1-W"mG[)kEH5tXoE^&$-c3kD8tumh?>WlBS6%*7!CCD&Uf6]6%&nLq@N$CC74BDNpKH.=l@OJ\
%C!8MlHf=m(hcM-O8]h_,7N,a+fZ_B.-Q4G(TOf;R!*iX_oIo^A,0GSc.E@*p@+PTU!N:Tq\Z\K=),^G]4i\GNX?dkuJ>np0X<"Z8
%WQTE]+SXE:E'fip>Yj![.7>\[YP@1MO`pr+d;s?C9"[JtNp7<-p6?UQ-S+^]C!@Z\!(-6S9]AsM>U\-)"ns^*PEqtV$8n"5GUC,f
%d:$lRM$gc_P;#4b>>]<m7q)HX1K:e#(@Wm38B.<q9o(!c&o*%P1$[R,C?@[DI"P)%Ao)i?kPPZ:Nht%Dj^fsbBgDDX`BC^G4TLRi
%CO-a9##F/!S9HbBBdrHQ<i^"VSJ#@,JKEPjGoc5:JQsK$/(cc%gSQIN`YWf)Y-/lf*N6UDKh3Ei,M3+hCVIrQZ]rduJ4u-'35R9*
%+QfOa$-k!+M7\o?&.'TH#0j"s67.R&+h)ic!qO`baH(rK0'$NhrCRLjF-)po]9$pb#RKhX,kahcW?C7h:^M4WDOLokTIb=k_&SHl
%RS![UgZs`7YE7L$9Xh]%LS\+a2(<$c&is.5FuDGO29R#OoF=Lj5o?dEWHF"tp:H@_qY(-gl@ZDC>AJs"_^Ser+=*R*4'("i,f[SJ
%::^>3B`=fAm*RDXj!rC0g]*WLZ.2u,Uj+JOCZl]^4j^a6.CHmQ2(?j>NX-^c[2NU',>,ZJP<S=gXQ-B5%pNDbBcJ[2Q/lU#e]+PO
%;='mJCO+LOLj73kp*Ljt&i+O$1,C*g(ZE!f..NUO%"Kg8JoWhL'0\KNeWa`],Mq=_3J<Z:6/@H33BiHj3TI1S\EU&#muMh5(2!Y+
%i41c,efj869:.60[`0\Vk=H&F<d#J]ClSFg/"0Ld.@FY=N2VP+q_F=%J\BIsbKUdV*k?%SK;<!#S"Q\SYU.dtJi.T]NPKN^o*4M4
%bFhB&8D32\"mZGba`\E3PL"U;B-5bs`kAP5=D&F5-kKJ\lq:jsK(Kurp'VUf\ZsB?is*l79,PM*W:$_g,DM%e]u#JKTkA.BiW'*6
%qYQb9VV5rsU,sKM]KphEW"Paqlp`4%OLK(he#8/65o:EVf&,6p9EY@%Y?cs&WNBli1iMsKHcmE61<mk&1R\mMph;GM3SZsEJ,n+B
%\hSl'?ke#R=>@0F(eRT3TNfD#fQJR!CCA%Sf!)O1KnN90.23#4ekGKE],r&.e-Qh$I?Gs^P.%"mq]5M?1$#H'"4[m,Ebq;^K1M-A
%=^tgbUjfFj,Qf<L2(+d5ZcZK,,*RamU+D/agb7%AHEYkqoA@r$;W82c)Pd>\[u@#UMdrb'o*9ukA--!oH5#/qbQpO;($3@L4o;(s
%2qtH>`*H<NDtf"q(O77WI:Q=hRWehB2Xs&WYLeF&"0SaX\Y9SX!8#4i_%BG]L#:9F(35g>W,Xu]X\K]T)4liKr773EXFRs-!Neqt
%>@3om>`72r$8;_s<Od@U>jNK9LnumUN5F'LTZahKZYmi1/UK3(0%B"flk%GJZaT[]0#I7@,qRsif83Hq?]%YA$c%LbC1.iJrP_9s
%"uW#g^5$T)C6mr2^,J=+@E`Y,f/[#*=9Lm\l;P1l'U&ptNiYpIMA3-Wkm8c"mrlud\O1HTh<_q2V:[]II$Lf;l)kT0$]+SIb<tbe
%!Eh[oR_o%k$25Y`YGl``'6/nVW+ST@(]e$#D,<#3PVr]X>2cG*%WCYNk/g'PTJb;tR0nSs!V\(]PVG?\W']Dq0A_JgNA'&s9LOoG
%9gVg5a23Cb.F>1QJJRMJ,oj30D4(p%+3JCLfZeZ#8mY[8'27=+'RQX^)DJM(_l??4ODFbd&_1ELTrpb]n>e-6"E<OXR/rO#T]sOl
%$<.#7$He=+DX!&D%b&r_)mTOOL_-fR?i^2C9t-L9'1an_p(KaQh0@C)%^9iKZ/X[f`5#i1-Sb!FM6m)i!`tSSKf8<@XM#I37/i#:
%GC[C*E-tlhWj:eUYaFe%nFfX@?p@k'anM%/@ZG,GE"IUT].lE_:M@[TRq2&T6e1YfdOg28[9D"-9kT$UU3C-%S5PV#AWu7Z9JQ.U
%6YG3pKiar8AqpWaXHtNOOL+<';cJ&%qfR3j(q:8+6OQ&%P`8*c\<Z9l<Dn&;C^T%0MDpusFVgU*9tTJIUCE<b8f$jSP5S]V6VW:[
%ZVq1+^MoVkX3$*V^<@C4.1LPu8#&p=iJ?&5N.]k$M)&%.#j8I^mSDprg^$%D].W24#0Z'3I45jiTS,-GaR!40<6ahH3`))t6=*C)
%-e*m3<dj3#SHEJ^Cqn13ihY,2I4LiUjEDc&36SibKV4U/k61!QWY.R(6B"u4DHqHo*%sQkEJ<M@Pf(T'L-Rdp%=spf:rOS5*E03e
%/-,tqYIOB3C4?lNol84d=FZap'aQ"Qi?])Ga'VH8]sq3tc)^qcBKZDVFKYDE&QfrE=R%,!,k>+pBLPYp'ZaLmGjQ^A0s>R)]0[t]
%[`?i9<S3W_:XM.k!E!:%qsC$"i9P"6C"n4rg70$SE?=Y[Z!u4OF>ARnT.:U!NMOeh>qBj-&JYrF2tt-lnkbbUI,#Y45Md"/c?@rO
%[SQj&P44i/ItB/>Y*EBOL'O`k227q`ajS`9i9"7V!H>-n"*F5T:mEN<dlS;H#f<-gV2U*.n%Z0hH`OSOAn=iBCBN9iU*._F/*%E&
%N-ZDur'<%8mHV(sNY^R$;)u)HQq7#5Y<`$:H.rm7?,B<F]TBuWBJ0JBK@[u<:OblR+#:t@l&QCj!/eum2fCE\4?%l62tDLC>?,5f
%_nk)sQZWl@=q>PXZF:-,DZ*U*+d/GH)4^fsl!N1$))k>>JR4903$R]j^(mKc4S8&fE97i#5n<QH)+*q?*gc_]!o*TU(;ul%RTl'l
%V-HF>8r$Eu^/[)3@^h?'d.q6/L]@ZTSKka3b)PV\A;iCG=krJ=+<,VWK7T%Rg!)+T>m04t7ZT1"/f09,0iIX2*<F>Deqg=g=XL<N
%3?:nsBL0g`(5,$eBpE+cn?ZMVWhW7Q)Z*QE6+YVBi:>]HJ0:mRKOcCS,rD**^0n1*F166lMfuIV&)kO),5S%V>7$-8eAF`&8;%^X
%0S,RiJM)VGE+\O$,n(h:T'@hlpd#g`WE^nR6=\on/*rth$7,?+5UH"ZlX<jnVCeR2UGCum&[ptJ,U\fmZZ,V8Q`Mi/E$27:!ta\g
%53=T=\-%i&mm9^@%mTkpRG23_##4%po!L.tVB#ZFn4(<)'tKoPOm7W>!Y5"4"Q9s$ar8`'_2mcqjhS=*_i9.L;&03;Z*nUpjd\9f
%;V<VUAaOT?Tnl%sM2A8>LHr0ME'Sn)87k>JrDCR:5-leikGY^nnc>&rnY:HG5tGZ<TDGl6_uB/TR)IEf:Vo\@;X43<h?%Q&faJ+C
%CQB<`qHK0eCPRX:@X(>5Y2*$tHGa>>R*CSdA\/o^O;C=(H4Bn(UP*m)7*c9QJX+ee>fHE52YsiLHS*T<gog]DBX=_uF?Z1g!1M*S
%?R(316R2/&2TrgEJ0[m.I@C6%\BB/MCDN'k+$pBd;/s>(7EFM@[m!JDl#j[_2<kBBH0\kC>+CR&E<LW<.;Wd'M;/.J9oWo4orp<\
%Aq!'[e?Np_^:DmWD4ejj\`m'X:0J]O>=?0h9!hk0^V'XYi:7M]b$)=q)f/j#(P<qq;WO$i:/H;(pN*t#N(2%1<Q\t;OF_t[Q<TOt
%3huO/KTK5`al2d@%;V$e@LMi1Rqo39H]uDp'Fs%QSqqQdSV'N7[BkT*3sInM=B(Q-dGit?NQF+uTab]ObLsd:G'$_'^5cmCZGA,i
%7G=F3JeV(1WN3ZL7k6Be>EgH[;6I5#g!Q$2,gcl96,&VRB0575n-VC84V8EXRtT<nm1."EjL(X)6Of?cQ'?t*7$1l)m^nc2X@%OU
%oq%.=QqoFF86hA*LmVi&qP./qcrceH"uHR:fOjE'9X5!c((d!fVr>5j^-o])K-Db;WdeQVp\!=')c=-tTM1cOG;AR9N0Wrs"5f65
%cKGa"REPJ$!/sVK[7(r9\>3CP2A6(o&X]6/6=K:jF2J)t[@-5hXfh(nq"A62[rh:ml6Ro6&2Ln0@-gnE\,BJH]W9p8UY,nfbGedG
%moJc^@CgaAiV[^68^2i`<2::--!^.LeD9qkg'HkMe[FoU_,eWDmQj(\ZD9/d$aDu]TtXc&iQl8tBX\H*Ck9ddYY//Ar$<;]KI-K`
%IM*1eWCM2P]bWJAc7c9#kW-5-9@t&)Lq^m@<$)ZTD;#&OO)7;k]JEB+<=L9k](sasYZCi9;.ue_-IlW_mW$bIdWJ@m>QgIM,"-8T
%eA8Y0&M4I(54)mF-'aPs8-+!?8Y%97kBj#\!?G4]ZK%OFrW5ra>L:[@@Au:Kj*&]/jpo5MXknB&,no]%WsdVj&%R,m0eUcq?ic.)
%/U.H+^MoFL2C<1TF$s#H7K.=uL/>^0mhT"n,\mH^;Fe!qX9U-\l#7*qFUJ?>`%i05a&HGjMEI5`Gl6XLPH./u+V(6&W[:Z[9U+nH
%[#lhGXYr(aJ]:1k.E*X,aB'fN#3D7p#pM1^%+hV!_?.qHO6XRg+Qlbj0"dY>O4V6ge2K'ZPjK?n@BObO<*+9M,.6oP@_(lhdoI1+
%<.(5o:GPtSQ35FgFpAB7<:#KU54pF#N)C?I7u-_',<qjCA%P?'f59K?)#uH%?D5:/,"P+$2:boE<hWGL!cDOGV?(D(Y4X2DOS=@/
%fi0AMf&R6dEVP>^Yf'js+B7e@>8d;MOe<ONGM]dD17H&g_?Y#^LtO!,)!.JLferqnYfjG(-V0,HA?3CspH'f6kT2:nW_>X]eXqum
%)n<<ddTV,:PIrhsCFZc,0YGX)XROT)0WPI]BXV*aP0\/Y$E<TSa+u]"N,[M28(bPY(SFkE1Erp*][/,>ic8@T7su5-6o-Ps3/6d$
%Uc*rYX9K`!(RgD@/*:L\8`sJ?LZALJ1.Xr(iI!(.@2u1dLVeX?ZUhYkg0nLt222MN<CTe^>1]o)0T10INZ:ud,6uMPPJUF0j8o(e
%/4aY3l!Mc?84Aar0<4M'_L-uS8;E%ZQpJIlaTjjM]e@`6>"e'/0kU.f;:m07=Ag5H,TY)bdEIa:7?.`eZ\k,Z!NSBmdHSbi1R(f0
%!%@t8i7)Kd&5k\)oGK;q.4LM+Jt*'Pe:lR:>L*R=cU-aY+G=):Xuu;%`d)AuqY_43;?tOo6q+<QhV$\]n:R93Aq1:1ab;JFOQRaK
%.[,4^^uf.iOFi2lLukaZ/">0^EC6j$Ji&`*irQO'9=Jr1-ebCJ,iBjU\MVBMB"BL]W#,f&QWmVBIMVmubhL\gSN@%_M_"2Cft'06
%ip87.dVZU3-jc5L:9`PhIL.^TCHlhn:fRH3TmA3k#i!TT\/Zd?0Us437(b-$5q]:MKS91C@=>)_Or.EA97*_)J.Wpu&`j&>62D<%
%J0XCsE!cY0p2Ym!j\N^*Q0u6nh5q<qO"FlFB^3:`O:3mb42$\n+@.3l?#'V[)Sn9F2H@.P85Gkp$tB&p!b%F[ZU"B!OGeH)'>Xl4
%SkAD_9UQb+kp=ZLl$^]1=^lnD"4tD_J&W=MW74.D>1aSVh"13/5QPb?B^CI]13CEFD4?ca`5/]YfJpLXC_:<O(@W!(6'#Df?=,C.
%][+42BOGo,9=36=#^gknk!O=V#!sgJc_?Um!7_Z:bl&1%<p4ER3JKN,ltgi5Ldi8*>6U&Mq4A0J*Y.u+-QnhLYI\.iTB`:5gOOrX
%Tbi\fIVflu$$ATE4`9p42"B;L9r!Ut?HNih5c$7siDuCMe4*EAalO>neNHgLeS*JaglM#=A\Spjp`0W8d(,s!TNd#I$/Ht'mg)!I
%aTR+4%8!5RI+:Ka]1jB18G-H(D4[sIrZYD$(ol1Z1EQFaiR5*;<4&6FK+aZRCDH?IX*2PE->?K_$3?8bNaLa?Zad-<U/9L2]t\@f
%fkKe$IA(U.0TKPa$n.X&B$#3u+$7>R1",_ThGcoY`+=kd&=.LdU;NN[*f?m2]M6UuHOd@*BAus5gdb)L+BS_o^)-lZr$BlGkt^l1
%#)(9d_SK)[qunIGhlDOslJu_-38T1F9,aI/Q3J2&DP[B2R><E3JfRJS/tFS&KR0=rY_jnQA/4]Nif,p<\L\,k@cOqsXZZt_PUQT3
%?5D8Q.WCFN5^3ecMKPID!AYq"?k0Rd7<VAPQ4OA21\+phk`NADWGV#B)<JuCqlD7#],$`m.=M67f,8;1F-Dlg<1_:c+_3alcZ2R7
%5e$tGA+kFL/GiLK]tkOnYBHb>6;*:'*flVIj)F*)6YVT=L14FUHJnUUok.1#dERD$OH2C9',Mg>NG1NP7-BaTm<M-clK1XL!]Q9J
%&R-"H'JO!NK/>lHQ'3^J?.CbLk[oKsR:oHbUoB_!2Fr\l\C-(s,>)%7i((lg(lb8HJZ%skP,pX&J0pul4cB#NC/ZO.d!%?fV)8^>
%=Mpm54rK!1:u#2/'o`KAc,^1d5)>THY;&SY:r9se^?F0'5gT;&g7OX1kp!9Rdlo<MRM<n[.W\^JLdnTO<2YAJ#=N!`J1i<J<;"-Q
%/r"FtP3hGKc?]0hnrTFX"'JL^9^j,J*FA<^$Xg<_q^#fNm!4-W23g/f#L,/h&W.T>c'j;2+JFm]<pq8r[:ct=LjfYuK%%Vq$*6'p
%?9_[/muklopODoZIeLsWs8I5WqJX.Kc7J;]ofrE?(VfjZ^\mJ;rnFWjqrM2`o#!+Fr66mEqo`@Ejj/GNGMdfBS,`$UhZd6]2PL$j
%0ui[rq9AAX*=V`+)h.[*^\d?3Zao]#VLW3),l+pAs)n/40Ci8gn,J%:nK/IHq=jnXqtMg*m\%o+insaQZgmL5rVl87o@s<8,D9"q
%AWd#*(AWs"2PMod)i6pGW"0K+cD'Mg&:r[mU=)D.^::3LfH]ZXeS>sS#YGj:;qWmX3+&Ui'<4Gr2$c_%c,/]$@K%IW_HVO,SI&:3
%8:4;kHFSkMlKJ8C98?aOP%<"mbcQ2(5Y^WD$@D=^QL)9L6Ks/:]C9:sU*`_mUU8Pu)FG\,RrpX&^@#01I[bS<86^Y/F=Xm0\pMgE
%X;aZ#m7(2l_I^c/)$.:)YH5YjYSp0L`aFV.s#FsHKTp>(,KmRVY+$P0;="H38OMu8!9Q'*MDE+./sE-49gU/'1.LIp`A`b3RMut2
%j&P:TZ>r1QPT&s78/,@$fBu6t3@LmhPUcl>6Dj^=7UUKGF,E>.bT5#fqJLk4_Q+(62?C-lW-UGY+eBICXN'Qu:7*4t)PbMC"FIqT
%NtnO,Y9hh.m'n2U5;1Cl_'bb,(KQpa4pMeq/TO^a@0Jb][HYZ$ZfnUHM]ae7gQ8Hj'ckc&9nlq-26aDD&pekTc.rC2T#@Q_6@dKD
%,q`"#./O\*hPN_74Z:SB2>2A+d'(?'A=8.?Z<;cL%RQZ>dsXOM#iYV&%(p&&l+)B(qpi`jRapThI=/n>S/$)fC2?X=.Xlr=4h2\!
%?rRsFXNP<E-khSZ<">8L*'7H(*Qr'5/m.`0quM,4/BhkXM7p]qm:Ad17hNV6goGTsj1[gHZOH:aKs_ag0B#!%NuHV>MbW!#W.K$9
%]F4_4X\VTQ0'V(%2T.U1"r):u)Tt*:C)9EcXU+"HaKT5:4lqH<CU3&0i!PjU;bkET'iM)hCj4YeP2JC6\>2,i2Teup<rkHHR;.G^
%Pp_&Og*&gq_0T7egHqfXfl(WI*(=)'#TY:a6m^5O,YDau""[oi583aG1.3>e[TGu:"5KC<EtQ'GBEds0h#q.9c,`ca,36m71-;JZ
%6.^Yo)aEo,E-F#7`X@J`DKX-eXgDMZm0deW+uq%rkJGKKRb^Eh"KsI08rno`Bk!(Rn'T1deE]%99UXqL[*u32cG]6/.W<A&>]afa
%rBbp;(KW>0da%&b`6TWK.0M(ueY9p[iZNJ2%&s<a2>AFF>g#Cf/1Og/CR4WY*0<a_('EgG%m-6d'4dB$8S5MYfHm;G>r'<Eq:o<P
%p<Uo0)*aFt]';KfoDb^KT5Riccj+HD1NWqO#^MlnEe<f<M%,9,2@oj[@?QGb:fo?b1PgAppn>@$XUkh/9pl=r8VuN,,%YIsC)7\n
%AUIGCr]qr#XZ<!/NlauMFD>-r`NX?E7E9X1Qn!5-QAOVMAa1bSXB78ID;k+]m$L8WF['$=$J.HJ=bNlbie@fJ9Ok?!n/NG\Q#6\)
%0.'?U;M.6e'?F>n.)PDd7-u<_WM8MF%!V!%4uWLe-_lHMBK=Lmg[6!8\.G\8,tZV14/+3EY4`Vt5:k/XWETL/PHI05+DR3A0%Q4A
%p5Z<+mHW5q/CM`'KaWCl(G`^uTG(4/]`i)gA@BS'%@IXrP$`ckq@X03/GG0B.uMYKp90;g6EMQa9d\7m+:On.$Xh?B/aU&PA[@JE
%9J>LBO%DU3RU`SM/+2[@`!`\Sb4]QgD/t>9cF>(24:*AqPQs`V!(IGQq$-%TV?e9"PDbjeG6"WKG,dLTN0Xf]3'`OGd2q4S6Oq:q
%,<=9Vh5jT9Jd9YJ@&b4XQ<L(MG[WDkOiUR*b50WLpQs$<i%`[f!c[7>coN<"TSXkFK'gD&>S@(B'>SOU`G0C,aQ<k1BbGQV+?hEr
%,7skZh;R1(+rT;R"!`oBU's:$I&IU;WQ4*JfojE$CiuB_6*s=4nst-O/9e>-kbSG.fEt/1]MDI@dm@EAb[&%@$:N?$pbW0C&m:+o
%_TPc'%_hP1$@2ueQ5pDfli7j:(%K,FR7FqQ[SpUE$+g1a-oOY;J(^KFj;6KL6.^LeSMelA7<0;5QZ$Kbmm>ZLZ;a*(/k7<HUWE0c
%Gg_k$J2Z^V)!qajKgR$/"]g@`-I'qjhrInEBrG;<ISO(C>jh%%g&eN;+CJ#8N,YgFbB'I]YppZBXlaF8c=AQ_Ns;gWMN:[GQ_^Kn
%m"2C&UBe<RX_lE'V$6j,Z6^G?an!*D$^M<Ao:<56OHsqZ+<Q&g@O=B5adg(X"2r3!Riq?1E=qJ,W_+&#rk=L#g]1sFW2d8U-?WV.
%aC@4h]ge\UX$n6c`Qd;GWO!?5^_r8Hb"j:7.P[=@M<2?>dTpq"gV<P\l`/"q;<p?C"l09*X'<=K"C<24T5f3:W#@"k9P^u9Q:uFV
%am.o4Uc!\9=9]SbU[=+5D2`4n#&L#b5FR_5/*kcgCS,@kKJ3rAdBK%nV-AQ_U5RjidI4ZVl4dg-I]dJ.fS4C#_Hk]/QL6Vk:GW_E
%gL51mUi=J$W7fg!OZCeQMh8L!(R,#$CJtC!gFqg3g=+1sR2Q'>V58oYIV8,$rO$_KCmNXoo[_V<5=``NMA]e+6,C2!:@&M;?u:Ei
%kkglEhBe[E/>\WIgF50!fZZn&eKG,j"[[QX'Z!;=RLt8]P19?)V\F?;@q+Z@p6IoPk%WZBh5(+WPZ0)h@)]8)KN2Tq%%a13OtLn[
%BPo,>/>Zcbc%YFZdH_^0Gk4?`r#dS\rSD!%KGhj"f#jm/NN1D9&];-6@X(FpqN2XVb_-r'oLr,c%n$%oc1o@o<p690B].`k/#K.T
%__tZB=TE<Q_73N%Wi);R4p#m:@pI;5MFO([(kA8c,E2F9?!1n&bPNC6Wq*ikf+dttN.T8Bnpks??NsI('Z!FZic0dm#g\C6Y!9,;
%+EY-^g,J!&7/+0nMpm^3?LGAWRq@s`mT;+pM#@)!`#>fpT;nrId$T&IX2U0N-`c#:E2BE,N*n+^i`1?<".)ej-J7c+*nV!ichs]?
%8cUoj<O,3P8``n=WZ,+3,\Lf&j@qsTfu7]UechE4dc^j>8:mD*[W^DR,l^K%Y_\J/*#tBK'L&2572/,__i_k<*$Go<Jl8)<:_0g'
%]nUKeB6Vrqa9Lb5^2RcVNk/W)14eRGhg4D<*m_:BLt2f(WT)^]MP?3GNci:nTh5@1F:T2oZ:$\Q"%-mp./>j6fL=Dn\V-F)!P9V:
%D8't?'c:8E^batU6GK6)\64XIam'5LI$(1[fbpZEYeKEWTO?0e6H*,CXgRA20r]a+cLb9]_F=XMi`G@H36&mRZJ&]qaN(P_$$089
%2W57:WYa2M:H=ra[W1!*f!MhGbk?.Fb!1V;otC,H!H6'<6nbACLRQBD%bW&Q15Wg,=Q`nb">2'M0P3N[GLfe4p.B4ab[i2hQYe&#
%;Q7EUe!cu!K[1EaMFP[#qjk?q^>PC#ZCVrQD@\u6,]O$rdtkGKW8BO;.pd6Xa;pu["&uiB(IGGFk.+&QIhp]lY"o?r]2fAd/47_(
%2Y2u+bk%.2ODp<F0'4!56;]7BPp\OqZqT4E'_3%03P#8;ePlU(Hn0mfTi[A]\?-KPaGO.t37Q[c[Yp>)aIh$fC'-Y+F[m\rS?d,_
%<=i=G0kbVV+fdW%5X9qXlRe?8!mXa$D*C\O3>@ns"8jjcc&]*c.Q#<P+bUgoa`eme1(a;'a@efo.4l&c6_pUO+S?$n:Lm[hn_U)-
%rCSi?d=D7=1h>_s$ROP%J=@obL<Q*[V7YEW^mDsWm*;VlP`VK'#/H5]!NO?L9qsM"S?D,`Dabs?jRNsb\8V\!@YQ:Oot&$mqkeZe
%C*hDpR`ep$^L?69`F+h`Df_DU\_Ip_]?0,H8:,7el4>:]0l"n&_mge8<RDLe8&lE'1i693F*;ul2J3g$`k]gh1269,V8Hdq'\50K
%U$J+LI!)K%5p'A<$NsM1.,TqmW?Mcq:t$/J'aHG%[9lDU1U>tD!A")<l?:i#k+6a>kG#]T28-Xid2i`mb/79BAWNQ:.R/KGTg4"3
%W`-(;.CGUAJE<<P=P=477j`fpW!?=n%NF=Q<Yt09;j!FT83TI7h=/L%!]$Q`?/Nr]ZV14\DZ/;9$GKH!/MSmdk!iGGM<*&#d_f[V
%`1*O^+CB+b[[<dgNBQC?.JPrpjJ+d+h,C%[&>r^ABlI@_nkH)7jiA89\s,=o\."%K'SH7ell3Ij/f*3,7_2Ph+26j.k0:Pio/,sc
%rD@Q?f=_S4.?R<)!fWV2C;!^M6dXUf!o"5I68dZYLE7cC"$ta^,?:>,CPE7kD$a1,l%Z#)O;S(X]k^[eYctg(j%KHGR(K:u:f,:^
%aVjc:Hd(dO7%_^r[@bi%iR4)f(!CZR=i&Se);/%pT1We6(fXr]&_4R%c,mg5K/o@/N)p";_t6odRDm*R2c0YB7_]7.q+?F3eBiX[
%((qT0!]hlO;REBTA?T;GaY$-Cfe:?@T^bBQ+cdTHG@e%=Ba.TJS&=`+Oe5/-,)Wam8qdKniPZA/;q2i<r_M=!U$a\h)Q5ZjFY'IX
%MO:HARo06!Q%o&l[T(4-U9$nN/M`P1h?tMQ:J^#J_ATdCZK=L+<VU99ie;^Z`dBp(Q4eMSFRbdQd`L4JQdfKIMmB?_rK6PKBEjDV
%K?/m9KKFD`9SHdqCauT(6oUHF"LdY.LIO%(/;:*`+QGQ#.D3,NEWl&AkW9=jN+iWN_TYgH9pW_g8sI?*&<q4u!F8.:Fp6f#Il9VV
%Zk:FJTHG<F6]EWGi4tctIt]`8MG&,2KjB%mCU77[$u8P9r2-MUDqHqhEh5EToeF=`U*>h.1`74u=rk(7HYtfN#:tY3A8DJg+RJVF
%S/$*!\%AlS=/MBW4[Kl0/RQ"Lqh)]M76\uW'MAqu'^3F-0PP&dObau4@3Q@p.]u+CbYYk\SWdXpgebWMOP,AT7N:.2d\G(8'IjK1
%YZ2nlCsj]Xn)PG'DAQ\::ou+p%E^aZYu[gKaSI[IJ(58Q03B9WgMhkQie\3u"d6F89V[?`Vem%Xl1I+ErDFhJlZM4oh5uKO`@Lj,
%_s47@+Yl7;d-1rHLlL+mUkF&SSS,51"Bh/Y&:1Ci`*R4Er#!YE/8D/BC?YVhen`!#26"f+`FS@^>kg&(9Oi\]98(o+pm>9mN_9H.
%DhTO$lmYFCTo7K?mO1!u;N'<Z//7*JG8a,H/i*t.Z8n7>muCp'g>p^?VC"OBM$01,6A:;a'\gV3.f>/I$3J/kAXEM_qP4q2c2tI@
%oJU@7!&Bo=]lRQQ'm6F8Jd%g34B/8'N$KnkSpODPUhN^d.VZ+B9:VUFOA(La,#=P&9pUA>%_I;nMT%ej&'IXMedG5)![&S1$$KKG
%NQu&=2HX?AbbO+/3VQV3#=SMR_MGpt3$k--!Q%!#j@IsMoE\;;G*C10f#+l<aF`e]9!*"Y;Eu1iF\/[7,m2Yl=Fu;[1Ks8'S*\:0
%V"`Tf8$GgX_L]p]r'l2G81BirH?OGq+<_;LXf-_VF&N/J8IDL1g0IB?#2LW>R96"E\*\H!P=SX)g[k/U&&W<.T;T(T=#)Et@O+kY
%,A(rOl<,S`(2cL2\s7Pb^_?%l2D>]*>ad1r`oSY$jp)&#Q[O#FKq]U'%8uVF&??^-'@5@fNiH$scC:4Z4P78t'P!3,iT/]n1gM&H
%5<8c57l.\#j$\tC9?5!7=r\1t^@h.&!b#?B:Z;+Rg%m^cVu?skMDor$dC;IFj7X8MKdMES&I>;X0ek.Uh7#opZe,IPKti)['t*2A
%$lHO3S-iu5*&W]0OU[Q$BA.F2WlBIBkT-]R`+lBqN9D4$@];<Pf,X(@"aAR'0Wc6-Zq;.5TMnei$QOg)S&/^WnD*R:l=WQY`k!I=
%#hn"D)&N12VS$@](hR%E#Yc^FQ'aTqc[%ACis6cLKrDT[Ro7=KHKUD*K"O?]dQ;t@[Vcarm$i0_>"N6C^[VDV3Z;98G]#)<r1f&j
%'$1p,q*BATLP(\9RCa?h<JUhe@8^M=Z)/>Ra^p+$RFrD(Wr@#aD8rgUP77naB?&Y]7t_4#=4_7E[I78"&\a`1DB9d>Z\O[AN]Se!
%d*M18PmoXK.:soj_gHD,N?n(9lGPC`8*;h)ipMM_VhTIU7^OCkkrZrV]H41#e$kf3Ni@T1R1W2p$nr38-#>KHR1ggBRn0u3b4LSU
%Ytkt]Gm&fR;NNp9oRlAA1OemklTrV-*Do`o<t8rQZdIfoqZu[MbHq.'3XH'YN=3OiQer]XWMbqrn"R4CXlFqO$2RT1mGfX)BgN"Y
%m_4Z#@k5E.*_mcLG6E"0"n'MK2E&05CQe6E8AX$/G:[8^rrB),KY_*K\)FpQ[b>LN0)#pLIsi,BP!PN,3!*n.&SVhp7Eg_HUaa7D
%o02/Lq>S(k0_hT7,.f=thnA"0rhR4#+)n/!YP0q9QF`Mj501!TdBQ?'Sj:6Y8jY5QPkHX5QOMPh!h(UMAmJ(V3;1lrU5fNE6b&@R
%"rpH-i"0cPZZ3b2\<[rke&#;*if$k#1e\KfD64SZ(kTUfT2b#P3=QQJ/>^rWO:@>>7[%OAnNF<h-qk902#S!,#ebCo$WMX\ca>4W
%WIekt\r'o=fC'kNGO^T"X3&IeXFVN-"gfUNO]]-cFW!NgN1qPI[oGr9oI/qGm]jm4OfY.\aCTO[>N!M9(T<;0Z)$fj4kdUMaDnkn
%Xi[P<nTG]3%F[De7=F614\&^J&PE\:.n#?U$R[n7C>r%>Wdpgr_,X?0,)sTCTlj'ZgL7F_eWf/a34X"Y&O*tf:rJrn*$?",/+Ka[
%2=M%t:WmWtOe\oq2ZHU,PCM0=T'=I@]G^ck/\@Wa(]F0(krT=LFFuT\qq0S8bFNR7Po)J<V;0L$MQ=?r8:$X+)9u=L]I"QoB7q":
%DV+Ld1D$1c5df,K43[-#=jnsl6spXi#<!BeD;HT^UT$oST'BKb&5-q?&!EF7!t9;TT`"QjC1T08^<_t,7-h`A!ek1\n,V4MI?7Yb
%K3"V#DtF3C#=]")ksF&U[,"8s--$q=(VGD_E(n0/kKYg'$FX$!ICbd!dg6c[F1o+g?r/5FK@b:Y0-aY8((Ik&\91!-?uS%DeYQrh
%Z)4"=ohf02=c9'c@$c!=HSkSA!XOs!'paq>&/q$;fFr0q3;O)nQQ.%(nT+cBY*&icK*j78/sr;Omoo?L\1.mVIl;B.7u.]s)m<9e
%'plC2M^G)VKu"kDclZ&r(aub5#b>AGmG<e#4H7K<9fH<&f#$)jUt_i%#_UT$6FE6<lKioL_7jE]0+j*,mbBHTMOdn.)q@F@'Gq#[
%DDF['jio(IW(i8[b&(_$fXWdk2,16.+f/q(.[kK?)@<+LkCUVBG*H:[;c.`=MIsW:Ed#S0ZtkJCKYepA;?6V8ZZtobN*]6WU,l1g
%Buo%,Ka/Y'\[5R:)\Z(:o;;4l-k:JDmm*anh)[.DYL@3`<EY*2>V6N0(rRG'WG`YJc#+c+MCsrVo:0cQ2<$,M)F;lDr8]0c_Of2n
%F45:@d!1(#Om*XNj*fU,SeDWlo/jc=X5>dBF?6\+$C\*i%)bXlr?rJshS^Y?7E^62c&k1)hB.)Z);bZVESC\aSR8h0iP)7gFd<[&
%EK4@dN7-u*\bA1;'('SPI&ggtG+<dZ'R^5nI4:D"N_;,_\31*^5d,LtU16XIU6^pG-j&EnHSekqc_pPZ]9N/5Kd<>KNjI[$2cR2^
%BJP5P?%7r'n=7'or#.WsiJPA#H[:2ndn7cm5O%&F'f">j<P"9\QS6P]c-e4@]C$^*Equbr7?E.[U+?"hl_h5O[ZoP9d=PT:kNG.@
%?S8aoX6!'t,g;TI"sER+J>pAZQkf8bhh6,Lp@,Bs%8'-k3A4a5?BPE=\FUq;H*2=R%8M=+YqXl;I<NqrqbTQ1DD,C@V7R't>.T`t
%Qd#Ns5[s-0&kkbZjX"Co8:BIr;,p=C_;C)f#R#XjmEA1"nrr#'I+\cb$Yh!qX(IF.!]7;F_i5=hbBb9o)*hL#=\<iF4n">>IqJ`8
%\?Xk7!2Kl)'\"beP$JDhFp?k)E4dkI&QF31;_WF<T*R\'Ju)E>GnD4O4!s?1&^VM7r77]qhFaB\nt.67YG/J'+l,TecEoZU.BVnT
%H:-tg/.o8e)/jlB]Y!er*J[V27qdTV5kqH#%Yk8MhP.Y#;2;0h3PnQh#BkE).DCB@^a!qU#18eK6OTt3P7-8#.3RHS]aW2io:C/f
%>W)=1N)A?5@Y]&Wqfu+Ea?gQ-s0oCn`2p2A#S2+YQ8WGGh.2H$-`[KCL2/mQ=YK@Hre#M2dSj'^')W,E-fAqWjV4<!<"JKVG4i(&
%a`JbtRJRc7V(Pi&/W6tS>:/b9%$39k,W_&6^MO?1N1("'9g]a"d,)D;UfgqEiA,B]g/lM@$_Wq\*3C=FGlF53.Z-aPnb[2Xa:(cQ
%B/Q%7RL=iehd;^uYjS6S-U:"%Z4rIn?:#Ma*4A@QDo'\V?[!7@0+a.YiULdedXBWH5GFp*g)].9E5J5MRJ;i`C@oQ/)C8WLIh\q\
%L+W9mYo5LHT7-`HoM9aop-80ij5[lk:a5oZlJqnU,Q4DrdMnlU[,X`jq>p7WVnW`SX3-p*Rn=1Ikr8`;r+M<;3"l=o``o@B<rf-p
%8eO3g"2gD"_$kLlm@XkhpR<QZ+R_5td+t)'F>[lrF[hO@g/';,kFqH)`IDaVn-.HN706!f.,2NeGLbRqTJ4U-<MFRVOLZ?EDg;!`
%Z+t[%+qY(\(p,ltf+qTS1!%Ke`+onqGWK*#aSM->_mti.afQ1M\STGXdp$CS'"T#SFpe4pN\2)7k\g-6J"rMIoE6Do`hMb89\Ps.
%33VCjG0Oq78(Cli1(arR5Y&#%J?.+V$Vi7(lAHZ\`=$;nDQd_u"eiQg_!2.?qL*UfWsjKKHRdE9Q/N%C$LZT0,C(L)^+N_q0-<]]
%/l+1L\&H\"s0e88>K1?;QX8VeqOfc`c5(iWFj%+XLdnE;&A4W_?!"HBi9op?f-YYj.lFh8]=O>=9/BMiL?*6C$SPMBdk]EW`rdm'
%]K@\GShZP0@Wb-#])B`u:YMaEInl_o>4"Yd?NAA/.rKU7ZT-F-"HK8D_"<*74)GR&K[8@qou$4";Y.#OX1',h4u6YBmpcLp/r+`b
%(BK1e)"j10:_.-?",-K)==>h(T'sR2k"o5R*IP$3IT!/J[fEG;ZSp`#W'N4+eRWj#Zj>5gp3)PABG&`q#Uk]6b#=4^"@cDVV<"So
%nQBZTP'\S)5W.*oM.43#FR)uMK-oHV)@1q/Z%7]C7jBNDeDCMfg:3*]kaeQ3%8nk$g#X:2aIBe%/*h%)>dRO=\!e6V(UW:I5iX5=
%Z@oDjm/pco?bPK"ZZb^G:oS)B%@3D2X>oqnGujiDD)?05_@#]YYQ9^N8C].En'30]&(iFqG;h5_\NnS9MVJSj??l`>c!-9i`:m<t
%*:E?7Ie!e%'@UaiZ\>7Q0'$=H`".0Pm()_U(qHVJ52(MN;Qh=4)j\5^Q+OU-ejrmPoch0@%,n^`//A[Nn5YZ1Z+iT@"$Wg^!]@OR
%gc=^A@63Z!f32$IZpYWX@FN4"l+)46;bZ!.fd0</2?/1X0-t@sOAoE@5"fRrIC]<RkhN]`RMeNUj%?bX+K7h(!upS-mUCh57`TE5
%4%<?+is6^)eLH#_m,c=+KAcghFc)]lOe$P)#JbDEeZ"?9%WS-A>?UgGmU-9$T#b+kf@1='WS8a"21c?Ca./s&rqdENVqA*r@./*X
%fA3d[1^[0CQ/J>=WiFlPk'%2r"oX^U+'r^8"_Cb-n-+mUrL6W8D4</NR7*,Nk)'-j20G1G=CA%__k3iS:+Nr>>g,$YQP3BHfJ0Hh
%m7CTlB*:/VVHh.B_L.sj-@8O>5ch22Gm=%3OoKA$JU`i`$$?,HWJa:po8/J'[#"m%$V=2b=iq84&RKG4!@";/ghMp^6X9)233/dO
%#k<^]G*'8*`tk/2OhHn5"4a]m%1Vl1?+HDt$oUrm@;1\SO0lg-AWSHp=<e:FnW:Id/I,\6C)`nFO>d&&Mjb['(.nA:%<!KAYH9-n
%=>8XL)PP[6E(dH)<#/fP$f+1Zph%nH6i]>+iZc$?YHY"=dJAFYB?f"4Y73;kCfj\.UNc!2WloD4."Gr3NG\gs#6WVVR?E+!FsF29
%)[iAuUq"D3<Y[[kpK>M4^E5OX5EqcHs"<BOD#LMCB+J#tKrAY?[]ld>iH2Eip[S!Z?PV>pT(*H9F@$iCDnlRTkWXN122\7RB*AY(
%9B^F&X4@\DfA(0EBd'LE\HNYV,>@d?A3J.<R]^=AO9:l24u!0pEc.MTHE72n\Wh%,\&F6ljJIc^\EE3Tjgb#)^E%>L;;H(`M&5ZY
%O_)j(\@[)F<G6%n4l*f]*J_,!>GQmQH:0]ITaD1qEQ.G-5l6L>XbtE2;E?cs9`4a0FnF&D[7B*ZY(P'b2,6HuU$Isan)fGWObq+h
%"D1q=VmlCG[6dkpd],.:oS4Fk4KPDI<&Vq4W:c;a689nkbZ>A9$<.ks#an(\NVq^>oH*k(``]3EKOC#d1[DZ7)mU&=MBjG_:NKQT
%itFNFR,BfLD,DqO'Pfi"_bH@R:qORe]V175if;=b+b:=B8AVO*FLqP"gh>s2?W:bBIH6W65uit,=pu,,."j9jKPS,E<uZGt.XGsZ
%:8Te@duf,iIHpl]P-/h)#i/+u_(o6W(OK#MV:n$a[I@^n0c^I?/lKoG@m(iL^ok1`O#V\QO=8c7Q35V(&.U"W9n7=ig=38l#`Ts<
%2#o8p__q]L1t2Z^W>fe&L&/S#GEWgW!a#&L-4hbs<0?;"&i/[h!FBt_MSA<Qf$k3'`#4"WK*+Sc'-`iO41N$1Zf&rG]:1W!">k]>
%[q<%0/UE'V%0$GPCna=:?AZE6;j2&gn#uVZ`?%"V^94/\S5!a)#Eqf%EQ$ah:Y;Lh,mF>,0AUe(UNJ@c!_C(/k;cp70A`U?X"@3n
%Co1JU?g3MQ7YbjY^Ee9S/sZeLLY/@j:Smr&Y[\R<WfFsU!#T,<X!n#cV&DWo@pI"feC8?E8"-H\5j@NKgJO&48^2c;E5o4bMe(>\
%c,(p6-!7]N>f`#=\H:'Vg:]jU[:B9S0<G\fSZ2?CeZU3FEn^Q;dUi*la`9Wqr,YCIr?S<4NEb]4A-`hIU\$d(<bN\F5u.d&]*F8m
%<"hI7gFd&9Vo\*M$VmgkV?ej*(sGUR$_]tV)G3B-X5Oqb9LQ]P20L`>PZtT4c6:pcq%MOA$8tb\/B.Z$C>Jcd7("3!X>j;-[RTg/
%![;9cbV_FH3sMPk#+,^7&=cPF7TksL9N5a"&8"sP!(h<qO-%!1$9^8sVNu,YY3)&>n!Bu\!g%X1f-]Q*kAL\jIMZJFQN9OC[4Xu5
%SrouV%3BsK?Qo?Yp[MRb/;kBVd3Q4\##Zh43=lIWiB06fZ)hF<'3k#BMV-o2SGie\flOUPW#'D.&4YJ7+F7@A>iNW[CPcqZdoBmq
%8ZSr'/`gP`@a:k'@jG#Z<*D&\rEUoI!k;tM#nu2srV4au_LLJ!jZ9=&'1T@N#%Xs9opFN/74pjj`U6LKQP&H>Z*On.;Q:+Ig218Q
%UG-`8/$QR:0(YmQUtJEX3aX5?b$>a!^:0#^i9EVR&0=X7OMo75So7I-Y[Me/%A^+25)QN&L<udUH0>>t(o&-n-qk`(7;J"&Al*PQ
%.L4@!IBHk)i^S>d`FN!Ocmd0%KB-=NmjoRHdPpC[C;)D*1hVi_\gHM?QbX*<G=O/2d=!0Rd+.![eYo>h)7A7IFOO9U<jT37&YO0p
%iXpN,XZDmJl>E`[Ri6go3>D?*#,o#m.ICP"CLm+@75D#Y1G2*L=/>CVF]I4,$$IHkbj\ZK#0#L1qV/&H&0%2j?A$0>H;IMlDOjQV
%#!T$N.T2_mL:h@69\3H'G7d7PH)Z3Kd.ieP(55(4Z6YOD%knVUC=:RbP-iT$m[[$4d@9BjS;=Zd]!##Ip9g"KSOMJSmEgVM=HaXY
%]:!FaTm8AsqZPlIQ+):jXa*c3>5Rr=WO`ta>'#@5^osI65KHi96_W3HOg@BF^l-U-'&m*-,;bOC5n60IG9M/\-BJiI$]bVfFko:_
%V*;02am+_=YIMM`=A]c,HgLoGhb0#6:&#ec)6cna3J/59)1HD%e<?LMS$E9Bc([+s]IE2HGPl3b#cS&(RDO+P?jqW66F8-0=W-%.
%LrEO]=T5!PDZ<hC)>%>t]gjr1c8g4M1Adn;cUmMh>'XQ7lJSJ<jKJ_n1/i9k(h-WU$a?JLI5n)h,e9[3K]GM[HELb])X=]CoI/m<
%0a?=..AX]OP=7Q]aNAaW#@?J\B[G)R)'I_a"ja3Y'Z4N_6^*8`"sP4onSje`7A9@sr*'&ahGB_,nZ)X+0\_/N03S_2IYc@,q7#!n
%K"IN7Yil/9%6X(U*iH8UeZ7MN>fjW9d2>:<V-ll^!WZg$\OWU7gDGL3p5G)bLBQuk7[69Kj+U?9T;mn9-F9TpN1[80252M5]3U!T
%Y"beom>Vh-+4+D);Scu!m3SoL^/0p/'ZEpOTr72GA0j9=TLVodmf*^&49MH,$M_*am#?DWA-[W7F]qn!X61cY5g)X)qG#e;h_DCq
%hJd#=DO-D)=#,;4A`B?rJns0SW0@a`l`G;J!MQE12?+BF\(hQnNDA0HK+A_FWgPXe^5@p.*G1-8p_9h8"r[3.cHfc\2q8Xp;`!R)
%SjuhqMFM8A`\(rqJkd'BXP\aE?,_"F`6^.VR"['G%El'[BFcHkm@2^T#s3dK5[,o$/'N&&BIuDckf]P'!d-<i\o@TbFQ8csCruc5
%Dc3okks1UE$.dQ8j8i"UXX*7Ape<PIG;*O#0Xb]UiDSCIrQuLf^K,I3E@djVZYW#R<>B\!;*>&9,YE>(".8SA6.Hac`f!1>fOZp[
%=LBiY^'GbWL*$/ik>g,5p-R"Q98fI[lB`83#>a5Bi`&O(oG^\p@,9Ydag0.#MhainbgjZN^E]7Q]rT71_7Dl;=4"kpGK&&;@[Aj\
%Qr3JXN+D@7=A8<?AdX8@E)U">3X#;fS0[1`g^RRh>$mC^Pmun!lOa.`"r2$O#O'=u*ViYs`BqkoH6VOFGg!o79:3a?^bf''\A/3P
%/I$/NS5WoY]N#U'l@,k"JXetp1/F=I9gXgnU5'INTOOi&Z=3BlJEpNN&>#$=4nhq&Q(W?Zp97Iba:TWPGN05O=VS2Y#oi>+clAI"
%#YO[>G!3j2#k)75=QZGgM`*o#H+j87a*Nn^7DA&sL(!*Pk+KBAW01C"5Z=nGn*[!NTdJnLYPW?mc8"9#3>R(B7kI8.D`_.DR2=3g
%a_9e^M'S(fb9_fe;)=26nF@'4QdM,Q@QKOK.,%EhN?C2G]4AZ8lG.9=AD9CE;VTC$DM+(YL>KR)302FJiDN/Vp(iS#U_16?O,W:<
%`i\ePe!38Zg7.j7OCi)!!]Hn)!7FJt1"bT<msnY^l#HCP`kZ(a#7$qk<+6BgP*WTYPL5'(1K/1^('lX'rqd:\N*U78;HZFV`dL?!
%#jO,J-cJe%>A%6E?>r4`9d=CX<sYkJADbae5Fg:$dgBTg[n7P;,q\WL0qWC"G1#PW,b24`>7etEo=M,YH\$u$MWb<Y)BC%mh5s;Z
%4H((^<``E&NS\Wb9j9kT`V+:%_6o?$Gtjg%AFLr$73hFEH6+DD2[Lik9X8BJUJD`^B`<EYD1qm03(2.lTkP)e2J<_!>)p6t6%FS3
%j"#%B^Gdcb/icjfKgUP(ih,8?1$p",&EMj6"DbIC9=_WsZFU3j7(o:.Um<3<rqi\/Kp6La`R/gcE6BI/bgcQO?!p521Y>iahQ&5Q
%]20(&3Q&5F2'@a:kjEutR)QBs0[:\i(BNW3]:iPl0#VE'\5TcP1,3)%.L>bS,H,&@B>n_jAq]\.XKe]Tga#6&$:_k5pcdif/d$Pi
%W)H^"hGl)B)JC2H3aNaK,@EO=C0de@/MW-DFlTc>=\.T(mKC#&bnCKkWd)G_SLkXiMc@G"0P%Q72W*JanYSThrDNY[oFN)9G:V;V
%o*_OsI74^OAG$,eD8c3"gor7$YB.#9:]V3YT)NQh/N7;Ih5/>]L.4<MN+H^Y:)3hG3lLa?^PP>8ME9LiV_R'=$]\%01&m6V3601)
%Vh'Ji>9I,7?4E7J$cJ@Q:%#HG'W#)QI.@`Ko+UkPL^_Vb=?R&MnKHtjV(4hS_3WsC2;VP&l6%&jBoF[j=t?9/<cR/QkSZ;g7@(VA
%Hf30)UgYD8]^H,!`="N6b^MDOO@Z]0GXU`/;&?22gRAeHQ[0g6'g88ojqjBANIMP4YDtd4"h$'B40*"_\XiuM?9'Df6$U*8@^H>t
%/^LASGt6`(U*S]>CHr3JnO83cV%uJ\Pn^9Dnu/QMJgjYI5C66b!A2#pQ:jkD?aZ:MOgGSO[QurdcKcAnQs?tT`E5>^Zl@A+jDK<p
%r"L-AV?QHJ-k$,e^7+@Q-q7<92;\%gOHUBEnmDcOF_c2(jU->?o1&lsJ(h.[8sE]t2;6SCX;tQ"Gm/%D#o^3:C^Vc%aF&7iiNl=k
%E$.e0?fDZd*1gGpCMXWL6W!Q^G2oMLZNsS'CrnD"qmkdLqb379rC@:T]@0oF,L1'N:0;D,VE<H0MFKj%G`U-B^'s:gb9%XqPg<,K
%#<aG"p#_2_"paKUrK$WAQ)'ZiDl`4<]W_[`)8C<0RA9bH"b=b*=>,]=,@7h<]^E9Y&QkVb7M+Z5Ke9;NH!^Ae[jBHrBR1[;6Puu_
%+r_+4o,Y'&miQ]GaP438?QGJWs'0ft?uII3T=BbcjS)\j/R76pL_oC.F\-kc4p1[M/q,dYX'gjq4d\5n^X3<e7%rrY(*kt4F*4l"
%gO[4-S[+qL$2X<1rrP#%Q?(@E1mNt#f!)4PS/nXu<R'>H!Ot8HD>I+55Tn.pVY=YjQFe:4><WI794(/(0^7p99%],;#+,rgEhrr*
%`j2V1HYEeNEsF\e:6W>M9AMDRAHfT7fn1kqb7?m=`(f31KY_&(:=2@Y%juofLjU`hP+.uYST`NXZEX\K$hT$OWgGZuNEL(*7`N@.
%<Z0$[G>GoB*r4bKh#^9aH[>9[aMM3h7uHc3_0k-k(`(6*Mp[_<(F?./?HcjQZiloh#e2%;Obl7c?s#8KlO(Q]-VRXC`)`i6R7O3n
%.<e[Fjm6-R0[p)"]7JNfKe0q3#Z[746(eq\>/VM*o?KfoE?k.6)YCFM6P!%!Epp<OSuRjB^'<$Ce0l^J9[TO%7f77RROSKWq4o(k
%3+u<F.qj"#PU0(9'lo_=:RB)-:>I(0;KOHC'bkRt_^QkuV)3h:E2(HK*A8\.oQDg)DP%./;ump6he`$d.T.aD3%No\U$=s&TfTHq
%5S9gVOf.E:an_>r1B[Kp-12]p]3B8ZW)Ou*7CbBl.,XdX[!/IY'Z*Z<hEMk4640b6O6BB5aoC"uhpCGWjXM_t?-4UD#^P$<_qE7C
%dfE:IZ6ks4J0)RTbBZa,iB_S[7`\S*$fP3SrF_s7[qf`19]=Nr?kO.C,NS-2!p5(%'%d)iF`W`!.=TmHq')2dQ<AZmMm6)lHgh%b
%G=A35oan!BB`abBQA?o%mJCBCJuj,BiXOn`UD]X(iakD4/=k,58fkc[2RsK!:bAg+?RE%=(dO`f9R'd.K;jd3$L??U^B"Zk7X3(E
%QeF3ME!B<?S+DZU@OqfJe`%ilRlf]ur`mf"XXVDqcaV=u*:H/-Y.CL#'Hbqhc)P0M-BGV`42_1)lq7d2]gW87`4:%Cle*SV2<r4f
%2%0be<''tSmgKn4hOa!dUIAr'74oo"RaHu2XjrH(h-8F6_mWaXh&tcWV;=K5f%\Pl7?oM=e(*R/95Wf;9KRpD8RWgkpG'Ocgs%b/
%jYNrH62XuTT)m_0]=KK9_HfW)n.+W8M*!@fH?!9T8Q+Eqa,ltr<>;2^gg)KkBA8>9XP'0&.N2p'nFdc_M8!GPdV^-Y^dC6$V(SR1
%+j(_Ek6N0GjjJB.&N4^&M9H)R_"g#fPV9f+7"s8>$)mB/,\ON$n;;tIm<q1Q^d%K*M&9%5AnN:+i.i\s:1b)i^_N;-W_,0>WT(rs
%GS-IH*att5*`!1WGiTjL8FdclR/=*a6ofo3QJ2FNJT?!-DWV)?WRPmYT_Egq:[Jk/7jUcG+g66\O_.R8jT9s$"n.7Y(*s9aCei,f
%YR!ip<C,-qEp54<PpJhLZ4(.P@Ifbqdj0Vm`.!2++G29-D;LWK8*"t\QP"TQCH19dRNa'iR,4iI@s8YD3b9TI,\5th,G]_!g7dhC
%9qL/,^3JZZ)qG:NE._$'jiJZjo&a?4/'CSr5C']oc=.?nZ!TE+q`@'T4&<3(m5SPTkAWWBUHeEM#A_Xd;\Qk/LQJiR%HT4:@&"q%
%@SL.`/5qIFU_W_PP9JOf%"-9`B\c[Hm('"iE!sY#KVV;Q&REIX)2]t7?I:qfoI2ot4miSE13P3u"q^_9S0,4PX6u3F@?1iA%NU1\
%;nY8NneUd;@DoaP!ZNS^Ek=;B;#in\M@!%]'LHs>lC7W)=V'TD\Z-D"r:m\,6eFhKJG4I_39kf53/L+SYSp%'(WTZfROQ;TY,g)$
%V`tD_nOb;QSh$+JQ$<NHH"l5E<lh-9g*A]7XJ+q6Z(iMX\?6-l6rSS\LTLaseWf<BL99@Tku)D^;N&r>Q[<6BrkqSX43fbtJtqjJ
%$MH\@>9GLXi&:3#Rl?To)OGX]XsYM'.3qd/2_b<++I"t69OY3-FOBqPQIO*OJiS0B"41L[]*O/OB/:FfpU+)tWar\CgC]`5J7s*S
%cd?hNimfS,H+SajJVV</GZjG4r)aIO-7d9;6'n2h>D$6s..E>R[uas^/i<pqK-dal(,M-6@F@P_[]_6kR$L_oD`HK_TY/qe$Qa'i
%'hnL@AUi]D'hP+H)/-n+M"&rMODCg<5;p21.Q&-@N#Y\2jBgVV!A-_*e+FaB0Po;?14aI%BsY#P)u[+5F?m=F;EVgS(U^W$,gA8b
%LGZ2fku)A0^D%:\2lsbM[33D"FFp_Js*XJ=,#q_7M^IZ2pQ6gY^C[YJ,>qW8V>Dr]%2Lt@.CB@hKRRna8FX`"K!@C[a`(jYB:NOh
%T.U5JZ#B($W7B9l"fc-Ci1qsRK7:rZ%tp]AM*RfY-+7(^iM\cf4Qk&IJ/dGp^9pi40Lb2U7Ko0l>7Y.3Q@-(gWu?*kU6p.bi%8WA
%S'$3;`fcG<!6HMd.=O77VBn1o\sC^t"k`co9*+<YaW0CnCc5*PT"RN=>NNL3SjQ>G!R,Q(DC]E(7EX2#P&#8-BKd.:c1FD^CYk\q
%,m'<l.j*e8h#DlthIP6(/@c8gPo/*5L3DH>,:Q(`EZf$n_ZK-Q$PF/Q"=Wj@b?&39-R,-#!X-IIC)1N/SmQDZA(tfW)=6`"7]c7V
%!_p!%DeI;lCYU)o8g@Ga^YX;/-/*_gc>qsQLW;:0mg@YRq>;DuO^'>D]A=(*-""N6N=TRo(gNc)p!Xue$**4H^,("5K_p0oN7pK+
%ZQFVTTUImOQGD:d#EOZHYj,mV&RpMFf5.WSb''5*+E^uf=b0-*!Htp@5$36R&HO^4lY8]8^W%:AW4_a:[<U\3dYAOYeYs60b#O7/
%_QfQup,/U3S2SI<A%>8)'`ci4F7*1`ILThY"O@(:8e4Y9,sR)C.bh^-g_t%f:<BZ)_96?5XO?n:'kU]ZUn4.:b?(pc]n/1q4+jiD
%@.4nL=\^<C5Z@5Z(?r(iJ`aCclcoI$PPtGF4'kZH2*S;.)#i0u8K[9gEp?7'*-?Cc-$q&iX.Z@S^<`B:/_A^-EV#@7)/_1KUe&j.
%#f:<EMAIL>(&7[Q<.u]h@GRWg70qc9?aFE\^4/%rfk;!<i"igdMAQK,O6:g36MHSgH0hl3^3Fit3l*`BVn9`(LCe2s";KRl(UMR>1
%'t!O=oK2UN7M@JIFA^25/_eSOc^>SXi^qr^DA3MZ4G7+J/ejZ,%H9K$/''0W@''o:U;Oh2@F$aH&I+5a]0\f&-mQZkc-V1.Rj8\f
%[n/`Na$_pj(C?@`+;$a,Mj'5^K%<E.)dXU&Qmu7ub8!7[P_cYt_P&]ob)L>09Q%lAbE)G_]\eQoNfY?2aR:FZSlFQ9!*p/Fp)%K\
%T`dmjQh9(QFY&1GkJoW>38oRWTUaIqVMhj6P*LX_?jQJbhZQa[nJ&DWGM,<FCKY80E&FU4DC\/9O)d&:o31ESN*F:s.c#sD0e%KS
%;?h6]^h3LHnT.ke'1'1r1$H)Sn;N!FJPQkAK6J]fLUsZ]9n0hocN$_134pBj"tSYST\8iRLQnhcETic/2ggAUTsmT=NT)_-6-u4+
%Ho%404+ut"/fJH4#"\E^D4,2pR'1]1`@R#U%A^H'*W.73<rUVKqT+%XZ!AU,5QCOt`odjB/Yh+I,3ZGeP!QW%YY4P:0'PJh-?^W\
%&dFb%'h+2h(L_b*(e.Q1SF4SB@n3S.A.B1lF.4$UbR[LiGm16]+>3<&C+9l&eg8@W`qJp5?SiteB@i3SK7Zna$?6GnZhqXc!V+Xu
%$;]4eC.K%\@.7>1P:@I#0WG'"6.&2+e_\rY(f3t!ngJaDk*$E?:I7@X+84KKMc,<`<(;[)RGj-5b?GhZc.LYGQJZr=*Usm=0I31+
%T_*O/3#[8<*<gjI$3&u;!%*?[FL!CBA&FS%c@ImWo31V;kVH83LLpUh-G#Ag8k$2!Y't_'NkAo.f+Ss+lj#_jQ$=7fl!0,@m\su1
%<E01p>nNSs#IQ2#aDZ&%6F@QKi(V[]k6M[/+j4QA@n8k6p/K."#BBOU-)GGIcGo:7Ztkqu4*I&aXd0\fkpJj<0old$0?8uI3t/-#
%c;.k;P<XGe++2!mHsmEG$>L#NZbO0CBs-XlBb9AF/UaQ<Wi/MbL3A45=],qBPpK9g#SD""5Aj[D'Lh;,$5,ZTZ>)"jE$.%p[YQ?8
%i%+IsMDZfN]'K4Sr"(*eV>?6us$Y4s9))oI,&SJZ&MfTb`<BpQi:q3c@tI8iZtMrCf@[npiX.h[/>/bAr<S]@6<R?C>m0=R_6&-.
%cau.Vip<6Ll5E]n6a3H/10%cU+k+bhDnPkSF9DQKO9ZB*#aUV1[e5G&3?I]XI_Xn4a/F"GX(nF*j&#U"-ca@Q%!0HD;mI6/;kg#4
%$(MKW9t[\TUnM6d]-L&dG04Vto6?\5c8L`88#5T@a7814]nmW&<.&#g"IEUs:^&F1G&E0g/sq*1s,sJS]L!>NO7?n\0ePp;j!OJD
%2P0QmmYP-uU4",-ZSgm]:@FuZnjVoXM_Q44.8iHqrF+PC`lID-^(Xh"<uD3\7q3;i&Me[LqdhEf_&_;s`ilPL;iV`&XkriA(,u'L
%-iUpem=aC=fjY\U8aq]=&olWa1N$A4InYT+/M06"$o3T*ODL@mo#b3H&8,9S5?2*l[)NXm[3sTk:ceOO.).N4?#0[Y0$Ku&U'OAP
%UI*IOT(`;nGu5EBl.:lHn:n1E(5u+]K#*^r7[c/D5TYB4J'SIu06#qm$5=j.CiNHAQkT\S^fM@&BJ=>uC'4hkB9;RJ5n]sf]WBlf
%&m(G33RZ)RQ:Cc\RRY=?`RF>%M:[6\!.5+597ieK#<\*^jA[R[!c+k5S);OBY`m31in?5@QW-J8RXX7sduoL&?-h[M+q[mDYLWQ>
%I5n07hj?Um<-HuJZ-2+QIGGlN0gMaF\VCeCiuS%7Ss5gX<-UM7nFr)_7e">\?fBOn0T1%1Mg+^Gi;36C4.[+C-K"ea;ssrUE:7PF
%AhjW`Jb_sY`P$WXD2TZ@I5YN\.VlZIOF_h3gp5F5Xu0_qf*T\S">)(8Ngq[8[(7ej[<6t(U$>l4as2C?,\+"0(;L?+VA6?Tl7iR6
%$asq.m;\T.2(i'd'eM="dCn\)TCEq'Ah!3#N/JF,\3);R/@YGqdl(kc\W$mG!(f!5f.`+rOGNQIDtiPE&Ce.G+S*ObJ9TReA9.$L
%P@bt%VTREp*bc[G,C1$_bdS839Y?XB-N#&9bri_s:7<Zbco8d^+G&q>HRNLP6[h'$:0ms/N8p7"l]_7`>.th<<^5]mRF;6^?kaGj
%D8o+`=82%\9r-o)3HBOQ3NBbS\4RBFV;f=r?[DX<FH0hCAj$&j-MgP^'JAp"_9bsUcE-lif*R=:;V7[6+\Tu]7=87HJHDU,0:U8Y
%gY*uU,(NW>iR`^9"I9%9/_J0LOTVpq(spK&Uk@)?BVmPfODIM,af>#@'AjokEX%H?!r,FE6P6B\gO"W[,G#\g&Sbc97s^k2Dsomq
%&PfN!BN+7H>']>71#`5d.:]UUe4P;3WQ<f,a=^$"AX/?$SQL@E9gi<rV\[=]Nc?uhhQ`9RE.m60&+>M(Qn7FX`<Ca(n3fqinimP8
%+$>r<#=V)(>m-Vo9>k-8Uk)PU(<_r:0pqpA"pnjT>]/QB4ui=16F"mUBPE1!K+B40_C6._Rq!Ii=8#C=:&4p!7,I_/1Dh\8?ji;q
%*A`Cmq[J7/NJO3;_%^Tk+F'*@6SV.9a%:Q?iuD4l'n=F3!XuN._32WtIZUPhZ`2:kk+ZJR;li:o(AorEbUP]"8m=Fq]u;HK6$Po.
%:fuYD=[B*>6U[^4/d3b(8X8]V)P;C9g/_nqjq(r()@:h?C+6)$`:u;F;eek*%Ir#5d^5stK:o=cs#A#IpfXquEeb8/e4ooTn`FKp
%\0^PTK@d+EBO"EX&FB$'c9OHdP,#L><uurfh\toqc5?9eA4BX&o>CHHC7@_@G[q8U=*Y%KbQs6nHY",j/[$9iJoa;\;O=4\#\43:
%&'nCL`u%Li,]:-QT6mG*o2A2&pnV%6NG(0YNid:!&5,8NeW++Se"B<EVX')Kjf5JAT(b*+';:F([2]_$*M"F&0l+N$k*Jm/H/Db?
%4f^pW<6SMq[J65H6gjE__%/`8Tuq8"PG-FC?S\]o([#@$1*G5G_i-sa@4ooUPMZWVOXV*]U/[,2BBf'f"P5'Q3G2&]62'64M4WJ<
%U`5mXXAm=s3uG&,=$V@$K_o1">?5Fr:D(V]mlLG(3$R#9.EGn8&WEF.GaBcRr7Pa#ghrCj_\BUa6...R\rpI:#?E&LKo<LNIr'6g
%)A&i3oXmZ1*TURh$HS0!>R0e_;Yj>(cQHt1A["[D-6hVs?e?D(^C@@7"b1nR5"o)>l39onF?'74n^l0Q](u?1&DrT@oe&sj;-/^B
%D84!mm'V2+rQ#O+lg'C][[P.sVLs*U"boQuN8+/m_L)uFH%L,e<H*m\ig7cZ"MMmJR7g7I"\W*CR[)]+B5TVRm:591oPb[qipCE=
%7<$9FL6rp=a))d,V!@S(XrsG=_P(tSKAn/g=Hu<t\&E#(X.3+Z;#Y8*%"4-0d>#0-Zif.S@lpq?=Cp]T2^TX=A(gUh_G8G3eONk1
%/p%p=p-Im9al&cfrP3T'4g)7CfYsd9`(US5%%Ghbd'VAb+gP^qRlFV^5Sc_Qk/baFDNt.ao5:^a3\_;/,26?gmtDLV^lo%i)J5un
%2JkK_L?7ZIlR2:pUKtUV$4cTKO6p6gADPrDblN_gghI%+Ulk`-pdgbb@S=q0&19>u/C]Lg2iAbS;!H.G880(hjE+DW9felC?4u9U
%/9%*48I-@l&*94J_&B)4VVp@k$?d%/nnn339MH5-g\>]>C!ic%5djFmDP\:MJsT'k#7hSglYY2r>#4hc\N]=FpMu$8m?1feJh2Gp
%]QV"B@7XHp(:llP^F?!p%1d_Kn3_*l&9;r<Hjg1(HX_qN.0.0,_Nr]eph6)3BIk&qmDC3.E?kFNK\U&#l5joSoP6kaqB"9+q=]j?
%N%YoN)-h.b$!2Of7f;1cI"Cg4A4sY%lIc8I#@RI$h5NsB-!>_#i\L%9pC^l]W](B0gZSb.jF/8b;Q\_0##*BBrSC1()T`;K5V%0&
%LH2bii'9ot2;T(!TO7?KX`DNH^l3tJa9!0p?P'_)WN`/=G@S7\pQE$9=&-!8:6*r8@Tp(sZns,'Jt&'O:rh#"Noe(TbRsMb%8s]V
%%t^QSUtW$aTpN_3I#`rE#[6RlE:&6JF%_l';1HW^[-(<JEp_,<<j#G;ZNsV"[^<bCe*&P4$i@l/1t*H"1A1#a'BfM2&XGGY.dJV/
%81$@uKX.@KUK\X2q[,jCCakA9FKpJ@>ES?jNI;dBD"l\=A"ont^nijo=q"p^+.*un,ET:e$21"rT6)RDItX0SeJ^-bkN0_fQCQ0j
%G`af5(Tgf.Yj58@'5n(=2dugGs)':N?P?CnGhp2TW@%]8'mfI<4Ob<+"`Bf6UURLWH5,EqCh-r\%3h0aMU%T&9`DT^b;N9j);MDY
%ED*<FKfHo)5<%nNO?c:k(_Ier5"C>-"<Fm@)%V<)DFX[`raC:#'L>&dU[5qZbQu,Fq?EtBq?LkNDAt%dcSjkim0hP^N8qabg8ZG3
%43Y==cN]&=/KmV?6JHe5P#B^14(1u+e\iRp6TsXce-**NF+#c3+DkeaKb*qN3%\<_k_:'h%X<f]iV4[KVQNHUed3X:_IAc7U%l00
%1BV^+6'fRR7\=Hk<VMPIM2FkK;(76/<&f=sqLMuE9G6Leg#n3p[8JLumAZ==9mH^>,[#&9Zfb$c:]3QSVpE;;d<MN7[V<#%Au,1s
%'qc$'HjM6qE&nQ2Q!ab*)5lWJZ*/Z17oYYUbudmKB-NbX@7.'.q3\J<3TTU.I=_9ag4J.1irF4u\;oM:$es;mCBViS*\hD=Kc&E:
%[f\@8+Q^fa[#X3k2UMIq9(4)$(G(Y.s*+6(r!RsFh\2*OTprL3*JcU#N[pS,+^f,eA?SY5I:+mM8Ae\hRfUBLnefn]0T)23YFf3l
%6"WuH^BU8K->#5b]Kh<apGZaQ81[.0VZrA&`]*!]E&+qC3e(%FGu4o3Y%84<$IOT8T>F%=C9AW)ZCIU)%I]b56`nA$#e8YlR&4.<
%8nn6D2@8KDRN=7ummoF3)CaY'k%3NF<g71`cKap5X6C1j^Xf:'CVU(E=P,8#;SEWD1f<"rgDeO7;87[YNbTZ^ThQ0s?a@s#oB0B4
%TM<0>68"ng'N!%U\>!h=I7'sQgQf9@-:C*fBSmF`cJX_2G=2:].@5]_Uh3t=JYN(bPlsn95/KH1rau=^06#HsepXrti/>R$>&+%c
%&RM-Z21d1j0`W0,Orr@<9-B%->EU1\[KmOuHj%P#K,iX[_Z"hEoud%b]&j:3bnk0oYA2a0``C361f>laR8RA\`V-fk@TJ"KXKh=n
%XF];Z&%u!j'TdIOqHHUa-&a^DX:UTt%KFc`7MQu2PS$*qYeW4`f!FYT*8<m2-L!r'l"QBT(@##kh=`!["&subQ/g&h6)N<^1KD2[
%31`X:$U@3'\gcXumKjquR0U-26o;K;8lJ1AoqKBrM))^&?&!fA7uc7[BWBnH\Q^`E!fmp7aZ\a.2YtY@PJ<?Z:Zc7Ac)DW1YL4[3
%jk4<3QmY'$)U]>ml2g#-j(%D2EY\.?Qj<Hp4m7h]r"^lSD?3oPJ=@I;&OS+s[klq7i$pHtBR)&sa5!;M\==BW>3W*s^uH4+gV:\K
%Rsm%RYji.)/AoRa2!()!$2;MO=YuHWeD#tIGDaF9>&^-hZn4sECEdphEs6*3).`a&B'ASQUII,l(/f^a`hKa^jABAr>$2UZp./&.
%$42>'PR.8uWhCFISC*rY+6HpdpCdlc(2l[h7G@F/)n7D@$f[7CpFWNZE\8++/_`LuW#m$?e`H_hpA.RI_Ms&6m%d#4p4lB$Ia"/N
%Hf6ap3TYJpRJu@lA`Y]Zb<#OI;"_TA>Fe?%#d&cG\h2:u^(N/q^X1W:6905-*[Y/n<$r1C)"H.B<PV+45J&YU^-^@1<$kh^"6e&:
%AJ.U"#bL7T"&FZ+OoBd<du6Cr-%(O,F3M%KL2Hd+"em@)2#OBQ;[B/Oi1m7gFXWJKB\0h0"X&@>@U`10Nd_%Y]M#N5$alo4'NYbQ
%%A_3k3'16N+4/LhH"n7*D0qK/CAr&6E^Z\e.8;6'CdMEjl8@>C_a1YQR#Y/C8RDZ%ZGOAC=dO7"%HC;k?@)GE<-q<RQ72BU3.,8H
%<#99'W?5t-g0,\DjAQ6k?K-JH^U:Z-ch",R!#k22L<_g&E6Z)^f7+(rcM'3-W;)74C!WH_@f0XTG,H]b-nesF#].2#>en(W']56g
%`Y0V79]#>!+96YCV=WeV[%=#tL7Y/0.=g(I2XniFE`GX*DgblA"FAbXpDI$dlKJV`HfGIOcTNFh)W0NdCsb%bgqJ$U,8mA):3p26
%lTUrUplE`4cK9>5:D1E8n*q"E=n**MIQq:D*?Q.(`$hSM%n"8&4r?crCZdjfY^ps8%)M,,*1T1M+aht7VGgqj3Kai1T37G1Glu1G
%Zl20`lN^[GRQ6#H*Tog\r_G$qm-^2T(ecZ]X.3Ye`ZDY3c0u6-Bg\`9Ad3WfCgU@,f[U:Ubl6Fpiqk7FoBa"c4B(BU\`&*7Ir28s
%$ZR[-*Lc"o4kn3Iq+.?\H,\P6gMrRpck[T;=)GPe$?Guf=2?XO_JiV<Aa)h@hfa4,XFGZ%!>=66=7Qq5I7QMV'I05iAGRSg*B@+:
%HI$HkbQ'5d=^j?uS'ibLTQW<:RY-a`@>u6Gg9@\as*P31Mh&ln$=V6%UQiX4lm$[29%pn[+LpYEp"TTX_)22=?06K!+^)*(poSd,
%#kH!M]@CLGpP78[&)6`6)E:S$]tN/WB8kn^f@?EY89%bbrQ#ioDjIPBW_Bl],3(19hh,!\\!B8'd!/Wc_R;:racDumn6Yh/2VhA3
%5cV&q?=K"O^@ZLGN6PQS<7oQ.[H::;8h+aLChO8HC?tJqPI#J6'ge1rd;sP0n4JJ/XqOE&SMb:ui5r&-GB$a<X`3_#YQL@]@X7H*
%aV;aDb3XRZ.h1sE=5WC)-dnI,VD+#^n&bi!GA%RC*psm3JZ85@XJXi+?*B:Gc)CGK&EVV&pd!#5.fk2$QO6T')#:UPasE`oo.b<@
%[T5:YB\<6\)#*;OTRTkI`a^VlB*Bk.>JC/9Z*nT<I5fWe^[G:-6-i/-bDMZ\SL3'Nj2A\C#pdHu8oM5E.D$N'H/=,;Lt"8'Km897
%L3*3,[798Z`9gGgfg>$a+!LUtl013N>9.]l\UAP!mGacF(_$-"qqA0+Tb?57?L\q66dVm)&X1HG5=<!W[(.aHZW4crk>TFQC%>-M
%92jqFK;$3dEm?Yhl.8!m`Cg[sBq_91Vk*qojP5+pBX-e'OaAh&O.L-1lS0un-&ritmr_Lufr`-1LE:O%-j"pg3uW5e330Q'HFB0%
%]=^d36;e-^`.#gf9\*EAlis!2TAn!^iU,n]DSdDf8>euZS'THWM8)4fL4R[Jf*g5WbEntM#JNuBI"VS,A\<rtkW(;?ZYr8M4rnc+
%n&dAZT,9OeDm3Hepm=5CG_qGD)9IfTXakb^r.E"1YlH`QGWBs'\-A>FY$D$YFK9pV8T)eHHZj3Gi>DB]/`fe.OR:-C1@8h-qFaKe
%b)2XuUK*d;VeJIqM(Q//=GO^+<&92]s6]8FjBNfH+0Vn/e1!q>>Hd5MX&*J%f"C;:+\8PnXI$Ve=*(kZ[uNQ,$VZbS:=(*nKcl^&
%Pq>SHi4=),B&ttT5eeqk_[6\'N!/JX/sUE\>NYXs"O-TXRZ9EdSDb>Bef"%>1R<[VFPD5dfHIN?LCi-V;"$0Bd)?cqk>q"3orL-N
%[+4%a'X;[tW0-f\KBrB>[>oL;3j'Qa'u.B_KdRc,2t"-]g.o'.]S/!<=*YUhpGFds:`f_4r8T!4fa7!nK?DV0`Us*Ajk.?Dfj7-s
%puW1*#*%6bX/pG72!C9.K^B]32(Um--1XAARh:9ue0*34rm('XnPf6?2fGuH:B!MK[#rJ,7W,iD;Tl/,70CLpd/GmMmj4@u=hr<D
%Z$m+,3iAT!f+\:hi<[-oa6J4t:%;)(r;3VZD3U0?"cV_SRd051$KKVAk67ZJTOkqAr=<\_s)Qg$S*H)*D&VbT:W$.sjlIDr\h*u@
%"d!U'05DsY&QWg`*irS#[>M*sq4!<@ZGu,X2e[[FYX$lb`'f:i3)%fN+q2rL^8/0\@X+5KnWPb_rZ)WgOaM&P*Va`T>A:Y)^o.rZ
%'S'')((DMWd52\5='@M&A/MW$TW/Jns0/:`UdV^=_/+lX:;1P0W^B8mW+[iLW;k=tkm!R04tbZFF-lN[F'mQDDAD6+OCf;kW[pKm
%SLo\+liT&5CaCBZEG00_nj(s9ZZP&^gpGQofM[FjocmJiFM&QQc^I(!r?!ceT1`oBG)FL4Nt((c_Nj$hW]C\*ddUNOIg94d.eJ\7
%.-Hq29KWNhpk/42S'E=_Y^F*3NB%\;9?7ApIO",QfF1k6K')A#m3YX^Jh8F&L`9SuPTlJn^XTI9@u5fR\*\PEc4C&Vo@'3MT-f]A
%<4XUM_X%PY&"[_:H#:BS[hLSe8$AmO!)N$#)m+9W'<g[NC!a75Pb/tOf(\'_T-^qFp6:$:6>$]1M]rum:0u@ckG^,:iEB=K=q,eW
%C1?=B=j3NY[LJdW`qnXe(p&K>e:35]e%!6bflY?#&mP_LCFg2Y,D-NtaOc4\Mk5OR\u#RM(q#L9M<8Q\VdThV]Lt&_SO.:=4-)fa
%0$oVJ4]fj-@oZ-*![nS]&<M(;"Mkj/cXdA/JXGR/DQotR3+3f_9CXi_G'WJ>DYUp6/c95.ra0"$(X.fn2[&C6\/g.?/u9HlJ"n#/
%Jl]?h0JJ$d#P<%[EM)"f/lKcqde7f,*^*627.^cQ<"jM6-M@R<(h#!7_UJBp(_iDeM(6-]0k6L]rh43_.VWZg[s*2CQ$kjM]!2Wb
%82:N$Jh4,J"c$MqYV^"05.W4<M/(cm@"`YpG7L_].D-iQFRQMQ-"D&KnccJcD2Luj"i7$F"+oOZ,M8f)bM^HG=A!!:(FTAORj\`'
%`AImig>ft2SqL'=#r*rXQi>]t<JaO[I:!,rnDJp&bnP.2?UcN"YOkNM\b/qZ!I`T84<ZeA]A9Qana3LSX[%8YXEjqPfFji^7\WS+
%qnbh:`K+(N3f^q,[WoI(dbbSWp56W&])Id8,n9#j)@52*&3Qq6O&=Bd"8@*S*OMj8G;a/[@44`6EqBlO'ZR8s'I4!"2O(iS1l-hC
%4iCT!/XH>=r6.DGU\uPJSd"pF+dp=V5pFNC<Pc1)VV49(4#RnjqDDQ]_?(hc3L<H+Ol32i2aOBH)@uq29%UpB5.Y$U#>AP3d2Bdd
%+0RhR?=A._+h6W<>bR'+K=.[4iBsjGZ;Yn:n#g."lRO*XMTSA<aIP1N:k7SgoK%X8Y#+MaNAce,$jj<c;go8a"ScFJ5]mDf&-+i(
%cl9@roQ1[B/RcPsXR?Gs&t:(d'jI6QG\.R1W:Y.ioQq&R_Vh%Od'"e87pVf6JqS(J3Bt=L=I,Y2l`D1hs%)Zs"R.FYBa,29&gMs"
%Cti*AY?R?qku2"GK<G9=+t_k'&pFJNH0NlXfi94U6qa?RaETA2Hk3RMagX)pT@Y0!$'GD+VDCtJ3AV4NS++I-/rV:9+U-,/_V+t>
%pq#]W$TsG/"phMOAkiJ)CC%Ac;1\js$4QLJ7i&"(#5>hdQ$[j!fD';oFT=[jio5b5_2^$i_%?u&lb9X.W<&ltlMVK<9_&O*#p=WW
%-,Nb=Yp4KEV$'7+6'R],+fnN;^,+X7MY#73-@o63XK(8Ye?e$cBD<&Sq;jd@_T+ohMu,M@K>)t*i&/3DWjK_'ZV$7c/'mTcUfleD
%fVEDX-#0$E\5]kFcmP!60IMG"$c5gU[U7gO-P3<8(,Q,6dq/JWCJ7.]9F=&7E_3Au8oPS7Au6gp;k0#uR^KkGD.5Gc3cJ^]IDZ%u
%BouNV,)0[k2!+kcb#*a)0uM[DdTF'RCsdR>-<1dD(>Cu.fLGRAW`O"dY&eD'q[-D49uil)qQCKL!kW<6a*en.((OS;Xau-iGLbrp
%apQ)R[odWEr;sR=f:^iB,=GB+_sa\Os8@*=+-@Ld5PBuLruMLdhf*<PUSXWqK6XM5a'9lV9Yd6Jkt.m7c=3FC&p$r3T3;Kmc7a5#
%b5)j+'=D?*]-N11">'rQ"[`pU2a.2KP(!,D!K34b0_Pu+B1RVh;'-Tp_it[oec@'Xd&N"oLl_T<\=^;,[U8B>>Ke*,+P_piLlU`"
%80J7&\ur9?&objiH,gDA57upo&Yu(QrtHDP]"fa=@PBFhqBorW.YC%l;)so-5n"AA_CJ/5Ol<GaneXj+;/CSc!X8.d+G#s*lM9Q<
%Mu_%B9SKKj:<g3p#fMTs-H0hG"LkDer4HhBP]A5`6h34N_]4phGbUs+,oD)eb!Y+n<q:23rJAjrU/pgA&-NVP1W$V]i%\^9L)krN
%E.s85oO4*L#r7h^/a8^p`Ln0ZCJ#OL=+IZ>QT-hrF:U>HNU9DJiR"[@ATB!,EAu_Ve-37m&D3&h]UiS#Jr2YlZ/ISq"?XICn-M&0
%O0`O3St>\$_hnp=pVFHc'0WccqFLKNTOUj3\Lm"],1uN^[$O]dr<^oWnL2JQZ`_*h?L73;EZ=lCSE^7%9uWDdW)QA/2f8X$U_sD/
%R`$@O6<o,_=BuZl`Y57[ihej[G@4E*5HhdH8s"4P=sd>:$8!Z@WGh=F@1R7c/-5=QKlBG5/N.5fDO0H4/op=0qB<F0ZDP1sPsHAN
%B[j8qV3g\A23PYa@nmNDK+!;n<=H_p0+s7Un/7qi*AASK/,$85VNf;TTq,FGUP9C1hegcm`?4T`]M$O@6C""G??YiV4K:HfA%ndo
%P/KajTcZ!#!%=hpgRmbalONb.4qp0#k8rYV^+MrDRZH9PZo[5/no3XZ<MtIZN:-5DQYYnd$h7GK[f.aT0;K1A?9'_n3`T&0O[2$b
%nRfg"GEm.ia(Ko&'c\8dAC+u5_7anZ1Tgg%l7uO..-RFP;39iUjKd't>=SR9#$[iC4.J(I&]in:ibi+RliR^ZD2ne[mMo^_TN.+d
%hr0efnE@D0&JWCL@$]r)5:*t[\B)Ak2LS+-XnQjTWmqSoq/Ll0pp/H-;-!,;LgT\+VAl)?Zb#1V[<T<"kNhN,o'pm/%9u5^3nA*/
%Y9"fKTFZP0NtHtqcgjD-./^4[U4*<4_`d"KnI?FQ-&4Jp0h:oh,@*Xnk=KjaFs3%c/V6%%Z&egc'k#^"]2bf<UTqR0\5hTP@51.Z
%T\;W]lk5j8fp?dRd2@qQ9+`OGeT#&uY*KlSaCC5aNJ4opq$Ic@cTSA"2QOgbI`FP#]gG)iSo@4Q5Z2u3O9VZ,:\F'V?3?$?nc]BH
%3t1^;XX2>$J]I[++.#ol^SE$Jhr+i1gnJ>cY(2+6o?t.*^gUQTKodL"04.'(*kAr0Wa$^_e?\Z$1H#]f6dCJ79r<6raBdJ7RT^gJ
%qf!so>!M4"\@eNa(-Zr5[8kk5jLWsT(@LK5E#Y7FV0YdQTfUIBB#6BNq4Q2]HjP9lpK4O?dleM<Y&B/DR&%#:bY6doN.W"Ql*1Lg
%KXg/i7fjg#]gc%qYma]aEdcfh'F9c8;)`q[,:W>5>hu7]O`RiE<gHZ_H8Fs0WOlXkm:rtK%-Kp)fI)J,Rk#%.mrBXT"EVE(KYe2%
%n3!_74rPjuaIYTE6_6Ja?aoM)]s.R.q`]\lDYk?Hq#jdo"<X+k.$4m?%:l4sY>Ct7J"na:&)e`4[6^2B#qZs=3pJTo=mF;S&IQ[S
%HL",)Us<(sAY_3OUs]HqN1(h%-sV=$iCZO`h\5AQ54iM<)B`_7:El;Z>C>riJ?G-h>c$Q3p!MbO+q3ZUUEI-kH&Ck^%$7D0KN-GI
%$cV9KF)B%=o:%!LGdnpAJ3cehqKOgu!Wk&VB-N$ce!#g_/9U%0L'-!:*U.lld+/piQ8Cp7J2F;hh=0&D'L%*rO*G\W6#=No6sdbS
%FIF$(E%:E$nl6!^pp,/$dClh.k$okEcMY\@<27#u-VbVL%##]5ZC*So5oL0%#0-QQg?t!J2IO)WYWE'_X/KY.V)4:L?S,]BJ.'ir
%5Y(V"iib3s%`)?`JFnnXrEk"iTcXgi$'&!m35^1IoZ&_Io*b`d!$rVbh@dWDOsAqThCt6p]C]5+/GC4#<T3^^,C8/0e?2G(&tS)8
%S0#,/&J[YaO\C+$<3'h/>rm=TBDt88&ftEGE7(2k7U6&c3d"SLK3e@-/m;2\5&>3!"n9%HhsI/mas)d54uNc%ltiAg@r=6*?>"9#
%J1_i_Jcpom6b5l/RrP<K:Co52_3_C/>`;XL;(i$^6PR`Jm5gB/&-Vetg;%fsp$Sicb`qR`n18_$^ZS1$\`:,(.fh(dODA[.NB9n'
%8)=gT*k-Von0&OB:*5D[.aV0k\;.i/4JPjU-^l6OJN;:8q3=B_#H'9"gT1G[LS2@S9&o@THKbC-;li3-Kf8AA^_LCBPYflVo[XK#
%-;iu2=p"5V_s)TMhl\p#c;p0dI"`G3J"K-D8Es#IVV8jVCZ"O0B(\8JqVPAViA,`2RA'\\'S'EiVYP54AgEg:4mF[rC4kV(j9M!:
%e.n1@\lPS@*k+hgf9rT?GpV`pDunOo"%mb?LNuW(;"Q(&$AkEk^&m=hU3WlO)p.%)bgtJ^(>41QeDMkLSl_;R^2PKZW=94_P0-N(
%!-(^X%#A:+:]U8>,q"@&`u0mD26Ns7k#"g3B;@@?n2!!rMEJtE!%]^e7=jZ>7L*cH.d7@TNM/DUa(RBi5sh^O@k='^Lem[V'YN2!
%kVZGC$&]W>j3'lI-^EoKO1%6t-k7GVY9#Huc+piSE59G[Hhtd9o/si9HD5;.8`CC73h5sPPe6.:X7]$[eQ#`W#*hTfLs3@KX3jUO
%fT;-1Wa74Vmd@p'Z^2G#`<[DpWc@*YUj\ut//-s4;g_dBfkpEkb((E9Np;6Uj0D9198T&i]-X:-UUnEk==gL,Ga>O`%aJVQ?<f)4
%UD<<FE*73r'GMpd&ncK@.T8(T=kC7U\'6PY'Mi\P%R4j9aHp`EFknW^KO<cbpnSN6@=MS\\31g2#F@`u8R0Cgk4^H#Nn2ilAdm+:
%L-L!Rro[!Vi!^5lRU_9[*=eu6r.YG%M_N[R806*WRphP9Nrt0E767^1>Rur?]+GL$;P*NiNi3"=TRDhuk8VnRX0KO"-F8scHQ[FU
%#:q!uNd(*L/T?NqDt*\><XC9Dq[Vm'Mo2]e+6ph"p]uR"$buqe+:"tu*]#4$N+0^LMjM/nMDP\8`1a;O=C=MnTo*.jJ0)rck3'&-
%69;3mW%K;cF!rWYpVs4-:p_'82%Kuq+&0?]8?=>)nYX;F77sp/;YZ_O`bKY`>d]X@W(Mo7*NOA4&je5b^BWjo>d3(UCXqAlhjn'u
%EE!@tmbR*f`oAYl+4?NNNOu'-LCLY,p[6Fj^t3_N]%SI]<uBY/`,\`"$<oP3=FC^XGq6CAS##bJ0#tS1eMGFJTqRMOj#B?_+k=_V
%H>[Yb[D!%K1OYVB9jRTE$BdcVe7QacNcDh0[?_UG_P(B'fe*1A@<.MHl$:g.<kr"9Jq>]J14_Z*M+h`DC]pbf;b%?KAmofg"eefj
%q\I=\d9=rGL32th-pqrZ^.-C9-D\"EaA9?Ze=s!"(0fA\K\!=^\Xm">HJF'qHa;fM$Tq=%dOpr#PCLQ9?a>L1aUa`]4k,[=<f_hR
%QOCkkh53(]ek("_;gar^_Fe=:<#:o`*$J#R68bT;Ll).,n2_8%"S3no7r=BhA3h"s&\KR,6GO-t#M4?rEdCb+NX2ZD+c)C[+0Vp+
%*+KYR8_8>T1M1kYP"%sSW8,)Won`@ERP1La[XVQ4bi7X8hV`GMIuMa@A6^FoRIYd=m--\ohF5OVhLgq)U2/lpIWtYd_4@D47Y]H0
%RYX(8B0ei7[FPu!k3!MN3i(ab3@nhc?#.h%jZ!0D'hP2YC2+E,J>u>e.Q>t7=eN=[FX-7lRUXf%8Ik]0\a=otjt?fBACJ'/7PMEo
%I9KeU?aFYE`)4oc%EH;kfga@m700AVZ?3U!.G&=ccP"1M7n:oZiApNGG'^/nRWbpiI&_i`F9*)1/2,\!^;GW<ElF2*[gqKL-n\(a
%-Dl?1<Jc8?gr#DSX!/mu8(&Tl5uL1Ds8EjRU-3"%Q\?cWpLB'q(\d<`XpV?Soumo*O:ou\6o*=TBM,?Y1)NbJ(pP'g'9VA+lfnXN
%>H/WO];sc$"#:&lg``qX.b@Q[TV]6Ke\Tt)95OUVV&;9`5/AP$P:Ac]UZ%:]&DhMlkLsV^0Nt#ppKu!CiCb;`qsXrS8l9.(X'9HD
%BNX`7!uXc041c1r,"nY!_&cim>jK?RT^gWgBc^Pf!d5P_58]P^:buM#E?-I'Q*kiN.I2.L^a#D^:kE$iK;]K3ep-M<Oen9o>WTK"
%O&'V'63JQ2=/>r_*[-Flk>2`M70?*4pd"BK\8Au4$[.&L&OM$-4I@>u=H=%c#sR)-]Ir?7b!QmpBC:*POuA'bJ]?[A<D+*X/'`j!
%!BotMDD4Q:$JDpVXaU^LUgAfeL8m[mHn#KpUrH42E3hET!Sp29V^.l-GpnlU8d6]g\rgT'FjHdtoPsc6_=)NX,sqp9qcYn<Pl+@e
%IH!3<4Y8W>/B.4&c"r52=1fKBhO-N'IO3R!8tVPhfkJ/`]QHnQB4gS8pDGZu<g4uGW/MJ&bh%jOc@`/=6'h(X7o5M)UQ4!VCl_Gf
%ON[A(%#A2YhKl8Q\X>Mnb*IE[J]1"eh6*1dJd*R]/8F5WBUq6Wn7[FQ:.eBlMktIe`-6;2*hFBhP&\+apTdnX;+eV@_`U;%XWX`=
%.Z@(8Pl`\:4#h:VJeH?SS+3WS(<((O[Uc-?IErr7265bABX'aBD::BL+DTL2DETaq4@@Yh_Vuul!#mXEq`5M%j(prd`43)'EZ)FM
%6:=/)IVS3A>bLK,b*O.T#g-!5>@M0sgnbRHa\'+l)>5WrD6H'dD`j&3>P1a]aQ"&W.,#$bceaoF`RUHl?;8u&8%GpM6Eh?.=DP$e
%/1c.Z10LmCl0G^BLT'Tgj+^*:V7=WqpP:u5T4'R!_T7)i0cW@!8Y[<+cGZ@4_&f9h[I8Ued$qq:(b"3ih'sY#CloC:d2ZXEhV=pF
%fg0L9pp',*>V<WhV(S(pq\99kU?nk0<gERp`MR6(qfG\'.cCHI("\?*9Sn$Q`1ho6V;P6CJh**9Ka"N!+?1kY^fBBu.$<crjG^Sc
%p1tDm61:[^9<^?2"_7@Y=s!M"kE,.`.,rkm`1h<Y>%/uX9ih=#YUM\;3^CbLqa.&_a%U]Q*,M895G,*)/^&8,D\Ztb'T'g[[WO>o
%_f(h<qq9"?[c:3RW-<dMR;mr8Y('#f>$f%D%)0YW%F7<T^@o(EV-O<0MUD?F=nL8-TkLC5Xc^]O8uj.ci(63EIeE+@5/m"mn4?>;
%P_f,.'opYNN#Q3KE@3kGJJ1`F>?<?`N;];?Fu't=96SpfTHk14!/98,R-;!AJ^OGL+"7>dhHohMJ75!3QQDu2c?(+s6,c!T61b<X
%M^7%?dFY?MG;b'^&bK<k;Mi[L(t]-6B@VSFm$]Ab@\r&kDo%3Y$k.rTjM&H:T[p_Q@c+D>8%!&?ll4,r*#:>Ohl$VKeR<51^>0YY
%Rn-4Vona`(prU?;eK*OL]<U@bkZ,.u3]9B%P5@[t(E3r*5133AlY@3$M)P"iW*V#>g.U4Z`.#b*BfTpg9!HW,a9B0[;PW_Yju\fd
%67#9;*kq`s,W^uJ-cQt7L6=($oAa)LQ'A#UJe#4s6m4J2LdbNr547X&8Ra)#lSVIJJM@e2`5*QT.&RSC+ef?b(pT7'pahOf&)^S9
%TtEN"JI0[<mRMs@R#lb@f,9u-[AaS7WO8e6Wp^er+4Cf.><B*gg_%RFm^E&'A=k9k;OK$,IH?Ta84$0&T?h-InX?`'cJbEp<poL)
%^@slIF;69l^p-%/,HX50)8X@qF]qp^bkEPi.P:baH=Q^28HT:iN?,Q_%2sieh@G'm?4Z'danmlaQZT[5P0u!qo^4pfm0^95Yqbp+
%;[Q`E3^6=*>Z-u&Tb/d,OZe)B]b)Vj=Gbu+O0?L%GZO&18ej-XT#8i"g;S7ih6097k"`b(d5YR(rL>?o9l:%g<&lLEN/o6%;rW+u
%%GPnp,4+b`%,Nro$rJq3EF*-*gn0-Mnjo\Q%EKRD!I0AZX\ifp9@2>2fn<6l'O,hOV+VS"U4MQ`'ZW=:"ICbWK4fMF(d0V!_fG>g
%[jV0#jI8sXdP:o8V_t0N2"KNCO9lV;HhN7O\eB1["Bo2K.BCQ/1a,S=5W3\*'R9i"jC@ES9njPK1:MSLB=G=($D49[c!*i#5%WZn
%[AFp8klF1Vnh!$Mq"//8DdV?CJa_?r^,E//:b`ZE(EJ%R5*kp=H`UngQ7AriP.fc^Ys]GRS[72]nspm]48^@8LFYq,<6-Hpn>t,0
%TJI@X9$]uXg3S"0\?bG.`0HX*<f&`a[Sb9I8>fT8'.KN?)#fg.kLI:P93W_sEi-gI,?Y?ih=K=@e2bu3Y9@I8hubtT>VU''!MjhM
%Msds!YbPDaA;$]Z7V\.D!mq6&S@WNd;sj*JFHih\^ijE]Ud7j@Fp]a0A=:iZLN*%"!phS:'o4Lg*@938IB!:87J7Q\`G;4r4?!PI
%fQ3sN9Ba2&$&sK;,Te&VG)t^:SpRC%LW[+WAW9gn\rIH>X.YND5TM[14sR!=,eCWE6rOpUJJVmi8AQkd'Yk,07\cJ:9I6QXag_:F
%Q2=(TgJ_cg&3q:16f\oKL<@<@C%M.p@dWU(IWPHY9ac2D)^Gl,am!kTRr7#n:N9r:,+l^aU-rY![W/'N%L]*!<up9BJsK!AmjEp?
%dYbX73E"m,!IYIhcO[#:>?nbO,]$=#(Y^.AV-/@fXL/<::n$j(SKJAGSm-u:+H#0,8KVs:a!_X#+U4G5PauV&NRK\]"=o/FM/GHr
%[Cf:mQNDjm,1G6)5O"dB$ReIKd$Tgjj/Z34K."l($([FBF^aAU<Y$V;Ond!q$^Ges1FdN,XN-LQ\TEn,U)9l2.p<1c9$*57?]4#_
%.]F46&,.Uo9(AFA6aY^na=]AKGI]tKT,)e]hm(:]gK5sWj&^E@Ci\F&83/XX\)i:DReCED2!X4SP>kLQY=8FEJutTf/h76R6h)Rd
%;nR-SQ<iL.gu66_&a2)uICDQNI&#)<PnQuiM*Pa9Q?:F:;AA*mb`gR;-j^V$=JaH@g_Asani?7&Wc!\TFd-3eE(,c1'"EX@Cs[Ts
%Er9p!5aOUBJCHGUHmi+K:EK/lNcZj\a>*?66C;T3+Ukj)*SW%e;)EOR/.>)aTkT#[rZ,[W,(T=lClZ"Qe3B`u;]_/Oej]]j"h6r&
%R\iMmi%A)<EOU<'V27U/]o+C.nYj1fM6.>AUQSFsj@<HU3C+^XOd^i2,tdn@E@8KU-2ZIF4!nWI.@;PfpCiUDj+fR>rqJsnft5uZ
%@XK\eSPi+j;ga,)Aj41l>nWIMR:JpGjT7][Z3Do6LO3A'*HmbTK@"Bn.V3%L0#uIlHW>,Rmq@jmNET%L$pK>-(Bl.BZg-"hNXEhN
%AsB]Tl>l[:E<;WsK?qZ>qD1U>d!I!/ejXS->%3-nOh@K*4^V)<ZU7@W2(?U/6)]kUd\0P4.440uBkJ)YcaTu+S.[U]d(uSGa[%e?
%r!;N$Stb.B.u%Puh-s^lkE[TXFP0$U)Ws+`5As.]\qstX30$0BeYis1%DHU$4pK.*<7$_V<-W#Ne-o\?.`a\M8a\J[4W,n*f\@6?
%S)re*Um'?>$52Hu@NQEeSjN^d.*15*QR+Fj6$CP-d6=tO=/_9Za29d"j@C*FZA7^?="3pQ,e%9@=:Ih\I#<FOXN/@GB'W\fAa9cU
%'itZ)!oM<bij'q"Q#D(Kee=pb7qAYnLl:^Pd@'p*]D7AJAu%)8!?dLq@u)/n3Yc_?d-OcMMT0$D<p!4&EU:_V&&*5c4b.F]UuQ5'
%O>*tYN;.-;bVDUS9("iF8Q\nKj`"2#.[u%[VltrcSu[11M0$^+]kXY[/h=b<]S4r6on#YuDm%U!q#X9E?TnV;fA>4U(S>?^#M3VX
%0lkG'i4ZDdcIYgLg"bCUgG.P\Md@86%BV<^g0i:.c@cd#?D*4<+/08tf!@6If$ch799`WA&3O.XK;SCU[c?IUN)-;t@^<WR"t49[
%m1"TG+Sce?d;fC4rXba78bJSf)kE_n*d@OF#Kbbq[ofPa#O?J)og'K%f0mA9'>g:k<:.5Y&adet_siN!+D`4?IVpjBO#/HrTm$=h
%?sD/S+-rtdDTmtL8O`":.sM<NW.N5LGA,JCZkNs*5K*RhQDokYqZ7<a]!`@s%)l<#.b$4FP_^:QGk1'dI%2))'-:C-_b6O=^W3+h
%X-&s=$CG@"1PLc;*&WOuR).:R>i8O5hCe;]-bk#A8f*Bp$92e'A<rqEqpE<N5>(uef%c`CDp:+@g"u@G-F];Wo.d/AOt`.SAl\e3
%6$c`3;s>XV+\YR&(b)K%GaaV4JhTc#4FXWI&iP<*Qhq:g"Y$ssg5-)gq#"?]O)m[@"Lp6;bV8:!3[(.9Q@L'oU@\fD$8f:V3Ns82
%K-]0jQ;$q8hm<;B&BVUfXIAQi.['Ej]bGW]H16CPV,W8OUe#WGJb7n`5n`P$f^1:_4miKLd<qq)Ubiuq`)(M%RKrT.jhh:>'\\^Z
%47]b\K+F:OQGq/J3=g"@\VU$$@/jZ`<se.GNgr/[&lgOjcKA"s?R;%$m%br?AW][loPI>4pZ#<Gr7[$(\i7TI%M!F*qqY80FZKQ0
%VSOcbo^Al7D/IH%q[AIE!P?a(G=P9fj(Cpa_OXKV[qBrCo,1.0kqf9h2ef7cp2O(Q&RK1Npgj1lkiU'J;P(sD#(\(-eDG>DMtJAl
%S5%f6jW5Bg8Cj)WhdHfl2u<(rikD!DJbBFSh$LnZ_`$eg#VK)*^I=DT'k,&F($_GOC<3n\#(K\bq;hjbn,VZc*M4gd)c_.ZQM:'a
%A('Cfl*76Pp4^@l?8dH!M#EW,_nGNF76-O)_%<s=9X1EO\>EHTa.NbMc>'3g-MpsZ%u85gb"3=*q+qba`"'bO@/3*1<[3KU(p6aZ
%0g)e1[tPHYe--@rN)Gf`4>@lX#+6dqRM>Di@Y\H`#bEY+r3KqWV8$!X8EQY3Gg-OB\W%&Q+m'='[dgArIl:;t:.p#T0D1a`Sjn-!
%MRnI.*&a?_G9]`8QPG34QD.WY.ZlSrimfMH1d+A)j/'c!MHNtk"0oFm+?-kGF)0cDq(Z=.r6)^ijG2GRUH6"8Dgco80A^iQV`9;Y
%=1iUVqKt(5GF=Sc;fg#qp%-F'o4$&*S>t8m"DQe(\(CISQ?2@no1kDBOHI"t&AAEU?7!6KojXT-gU@%cSt%M..s-h`H&%r<MB:BL
%,ULED1[g)O<;\$fjAN78,`Xe"9M9[JT]epUCblt-8NhoO3?`Bp2=O]B"W\>LD;(;s?Y$rrD<#Sf'tFds'f-a"3a/,RBr"FH#K4Sd
%Wi38&b_DRY-&2bD)>Z^ISco%360)sHLn<h!F'0m*Sr+l;=sdo`]P5N`4);AN,X(\^HE0;P>:*kIB,sFPeMm:Z..!Lq5KZod)F]ma
%dQFG'83ge(#u1,Kps<[=fAt]&!oa\YWE*89gH3S1/2`i&3^8W[4pHYV[%0:%mF8QG<%EpW.TPs6CVX>ba9im"k&10-LK4$b<]t.A
%S0ai.[U,2=eL"mL4p,o0MTQ?*hXQL0/D:/`el5sl/#]HFAqNbRFIp"RWgu"EE7N*n8i']F*t(/KI#eg?b"S!E;mZ4%TVCZB,;rcL
%F(r%a-9^[*7j_JAkJ;WO2dTXh/2Plm)>*;<DOpr,VnWPS@&EQ!!F30t=PJ9:ibu#T%P9MNoRXR':>d%8lsnMk,HOI`>oF>'E>^`X
%*7"1TD&BAgSS[dj93&TDe^3@3@nS=JFC[m)nX3ktXlul/!unD,nT7plRGO#LI?.;o,ge!rjQtu9iFVK-936)2E.R*5VL0@VWQ`<.
%/SHUL,1T2#QnW.Hf[1BsR!h4C]]A0((Q7kuQPf%qbi[Uli6`75;ik]/5m1GDZ>.Q<1?8!cr\`^4.a$Sgf7cg)Z.Mjt&-ZC3q%t@e
%iENtNIVr*^al)jP,2mI6VO&CVoO\<\fDHDTH9,j+NfA1GQX*$6lrmD"*YIHOn1C5_><H7\W*SAuG_!^OUJ[Rh,HHE@]cVPM38h^7
%YL3MYlNOSe_W;(4M&@W0WiFb.Jh"KR"dNgV(eP*337>l*CkO0*6Y#\cG)Ml6IsF82p5!3o"LSj:mn"bW,AQLdH\Yb)I]/[b`\E@.
%>%s[ga$l!Z7%GjR1iFALOlXg:\")-^-1]><H`O/fqaBnEB5!k'Oh8<DGjsp_-k1KY:%o$D"Dqg3852$fW]39!VE^paD=k2;!.9pq
%J\u(2'$,U4%F$*"=):OkW`rG_TE>"JXd5n\/q\AHXH/%5hD"b!fEd!o>H=gK4@t5eqWCt9NJ]2EC>;'0ZigK*:;#d+3>8YmU!G-W
%<%h[1APUeh=^h+/j"^#LNoWM=9_#de\>Z+79;4Dm:[GWAdrh"LGD$bK2alfW'(o@%$Le9[^7^cSZ9NtjL!>q\Md'=KX*B:$H+?1s
%;kJ[=6hl;)Qnhn\.W;.3W[Ur5i%Ji"eK,d.jV:A*(1"GFd4\gC/uh-kdA_g%=<=Q639rLoHE('`><'6ufZh5UC!+C.4G<#<l_r^'
%Dh0l'NmY;BH.'fnIr2B]%1*(mI*f3D?VrEQ+dt;ZjcWfQT59(%G"O'=;B+-3&$&lH]W=3WNKNp/qlb#p,6<^\Jj1op;-?.)1TB09
%KnW*Vpf-,fGCGpoEs4SLLR"G3hQh;S\=cL7dCc[Zje>^Y+)*BB?@B*)3L%kXrd&k%=F"_P]:22I$A[A3l5I,Y'c^HL+chK_`=A[.
%r[R,N^>CURQdmr^o59Vdf?:T+e[=[5d_Z*;rW%jOZ%+(-Z&Q\I1+]kqeqY&%<+'?=4&0)C0<*X7Y:=f"fe[akn<Z1I7NMuCb':?<
%HW>"fHf1/%/i8oj#6HJrfGc37N*7?90>r"3,VBI&eI@tifO'eb=[["],F)n?WrfR$A!q`H5D9t=`gDY2fgXac2;tnnU<P+G@i0?n
%5?#.ZN?-t2n3#^l+6s")KKd9_#e`MC:r3N**!K<GKZ_.Lhs-W^S7\]CTccT#0$j.&$T5:X,n$JH<b/h"`3&%W9uH;)6Mq!W-u(u?
%Eaapc@3nlZF#ucENdZI`-fG/qr0\=OJ^Hs,\SV"EU>q3t9@"u9G(ZUMc]Sss(L;9f0F9_-4M)((?j'V<hJ6<g<h4tIY]Df?^7'1M
%NuM.rqM`lELq>0Q4[;9r$jG50Q1$.?8"(Y:Z_+hG:_ua=`-1]'$<d"%es3_G=(+Lr"U-pP\m_?3]9:c1?4jC=oWS8]?iTug:]K$r
%q)A?Ahu<Firr2BcoDeRZ^HV\YYQ*_1s6]j^^]3nTr4E"$n,N*bs5/>*p!<Zsc[Yc^?iJqIs6ZHYj1kV`^\^gAr;,%Fp@j(6B4'a!
%oWS8MJ,R!/co(gjQbWOuJ,MJ/qr\jrp2>?ITDr2HqH*^Uc[YPemhFTEhaIJ:+9-K\mCrYek(!:>qJZC"_nu:8rYOV(s'd0jr8YQe
%fDkHOnSeEm\,Z*P^\r*N"P)$%o9'&uIH9j][0Q@KWtlth%91o5p2>rXM1'/b@S=;t<j@483g9V"OM/J$Olr[j]dFO&GbDZ<5,u&g
%50>:nm!ZA\:\W.7OR;o7WiE#-#-JmIAl!:YW6X32*KT#D#o7lRQKr0"ZB`_?71b%uP\>PFd7"AQW^d0tA<DZmHL#o[0TqM]D8o8*
%#r-asHcS"Cb]R=>?l;M.+ch4LeO]4ZA_'FJ6XHE'*5\H>Z:G0c%`OMDetTo)l;F"f<YsJ[^n>L_N&Mu];n=)?2(oXe8[a`a^.pla
%M7i#rTLaVZ9^ukUUEA>$Qm0"R<M5,*DYFm,PC348*^$PXpdM\K/MJIe_Zg,f(M2f&F-A;R7Nr^_KII,cQgtMM7$crk^6H&34,Cd>
%](<4XY:TBF!\X!&4AqjU9b*fo'GNS[loHl\WQW:+X:Ffk)Ut)"\^n#<Kl+hs)>aJ&@!'DV48^Pe<%?mQ\]Pe[TXq6I2f$ga#t,$\
%D51NHH-$&<ET]RmY-EDiUuElfE[8=u_DIWC]!GWcS5nf"bsJ.B8GX36PN<LK&Aiok2o*'Ja/m!Ak)pE`#4"8=#eR,'fS!K&'p-s^
%5;+fN^R9BGj0mM<!Uo/ieDV#58l;/q&-P+6n*tS'@a$M\Z2U'Z^/C5[7G4FYmIr_XNq@BP*hK`1c&X1LBTAqDKSS\]biRHB#*NS"
%/</%%djlJt.M!Y<Z\_8XLQCUXmC"%U(N;#;?TJXKe:;1Kirj+@):+gkQc>?g6[-KQB/3e.BXC`=1iVq5RZS_M/M84&\o(h@VQ;5=
%eRFBHs$cmlcC'Q-rpmq5j:iaSlV73[91UAeIHjZ2"[G(D6CXPmRR73M`"G-9CGDbM4;q)4?]Z6Lq8hS/5Si!:XU:^>$g.&.rqHo`
%KqV[B>%\b3VI>uOpFUeieQFCe,T0g'=qSdRB@pXi$qBKk_F5$j&Htu79M^;R"(Hr-Z*9qDoFAJQou))X.>uV9g2oE8^\aIipCJ.[
%]p0Wkg9Lt&#/%pp!"M<eOb>Cg!^C:=-\\9[?UIGIJAo\_S;mg79Q@6.,U0$<'B@CdqDI9l&RF<<b4>(X]fu[iR`@B$SRN!D:>4SL
%"0nm6n6Yk/"Un2moMGI'VXi("1p[+['%PJ273qjsk55'ONX`4n]FU>,H'Dgfp\6,!d,ku3#5q-hd]B[[4eQBpHR,qm5]*l-+%7!M
%.$LKP:]+<h(C&P8B94Dh?_N1O!dm]IpSL<*q64[7PZ/c6,QCo^E_Dd[*ie-LX'sK!>,8&KnPOK']:_buV7&7e6W4DpV^=W[rT]j!
%:JcpT,`Nm!9<K!!%c3)c'er5C,io9A:=;?s;It$]hZ;]!dIIP;`N#je#c1eC>qQ4po4h902lplW)P2_CB2uDUo7EFU(R_d+VB>#R
%aTJ(@8N.*:"6IUUR5GH<C8j-%7#YZS^j,P2KiY3^s,cF^ksl<<Ku37_/gXE)Spu\="`;.Y,dV4AL\LPGcBDqE_l!O?EFc@1<2H.-
%;"ShrFAeunRseiJ]f5AtVXnol;Yl-t7LIs.'fmGRDD\5TrckpI+]<l#Z`>h$Eq-Xl]4K5<Z!sBI*?NKGqHI@>fs?+M+P6r9S!m-@
%cM7Mnji`Qc\dDW6&:$td5n(Y4g:36ITD9(,_a+88lK)$D/6H1['k/,dTNPMe+e5c$?XQKQ`EG<2(dOOR<VZd==Z):O<<NVX""Q]o
%qS0.V^QmE.Rb(/rcY[M*1rF]VmEIo%qW>@[-0Ou?NW)$`fgkgN9LX=i$O;i(TN;-l[5BQ4;m).\,MeBJ->'E=nu*UG#1E*@Vr5MZ
%!j_"41d?FA+J!D]#4rQY0&Z]cUI:C"MtdX6JYD9WQ`,4`a7U1q?bRF,VP&he32XksP!QX.=.Lc2!o<_;f6o\#[u1sa9-3<.g"=0Z
%Sq'rfDW:f:D/%Bp<M,S7jgdpu>.\>`=L-4&e1OI,9R]\`>ddYi=ICssEur(#qL<sPVeD7<_sVXn[0WYc./n5Dqt`a9%c3M%Cjq9)
%E,Baq=`ip/1.g?MPW]r$faRjEhFNf!dJm,_*X*2t3ois"d6Q5`%3(g>I^7<m=I3L`ft@BlkN\9gXBDFL_[r#ZYc\RZ-c)2jrEOnM
%B;n(,=URZSjW[A?Td6<tdqbEp$_mj^&7>:"J<J*j#+*"TS12$[b/nf6fIj(Ehk@:A[2FRgVgGGk?HP^&4ZkC\WOXd,J]"PukN-sr
%SKY+G,,(5lW5/L%J4,c&gJ]AAb0/s7832T<)q9#?XH#i_UP_4+lYEHM_j8NcT!#uF(0sP/a^DE.ShLOUGm+Y?B,%X*j!A"bEYsA(
%S)p'.4'3sIGqfe]o]5jIW9bnT%"$ktF\NXi"JWqUK(S?XO0;,'109[b&>1)X7r<(/E2p!"-+K_)aVi(2[.b=tPdLM_\!5"MQ"IV=
%6AS1t6.gY"WC%`N-LWn%acFRj$?8'AOB+KC`_q1uD*\6o]ZR+peg<F<'LI]V<Ya3Bq9G,L]H#VSdR=eUoH'-@c&rA01FriYn!e=A
%cq4MDP<OaOm-6!Vf/uGl#J3HWYOG:[5!"2ud^Pttg9,GVQ1c!;>4a6IY*Q6,rBSL\`=s(/H"SR@UFcKUA_46DS?$'0\ns=lbkD<G
%G$D9._"er6Cmd?-[\7eAN`gL;mYl?:@lR;-<..#qqau81f]J.gqfJ,F\ff/fbDE$$*_/1'N3aNMHU*@D]6WlM7Nb)u)'=%mgbC0r
%=J<nQVZp_J'Por!k!!*<9qAt/Q!4E_oK9sd'1fN-X>2qh"MVhYk)qiLi-H5&9!gTVme/53V0E8TF8H`..rW\VT6C."p>-3%/,S``
%llWE[n@saA!07N:0s=dO5$-<#7mND4'&&-1:uNh\pu%Kr[GdMGaG+gIc.uM6`mEIT]g0\/FjjAXs7t"Q3rHT<MuH@kR>h`TFgI*Y
%)tJ3-XgdSjhD/BF8?^k@Z\e%edbr^K"HmKBe8*8)e4$32K*p:-X>-CB98*D&F?)d`J^joA#6^r]E!)]e>tK"X.f]^FR0_=jC2pM0
%e`Ee=hW3'F2q**SL%GAoO`"T_+V6$aN-aAXOq$D]^_hq/9HqCCOKCKa1(M>ZkoXqK*MR`4DHO1\i9K;?+nf6E\#,Dtnl&Hf@#Z'i
%PuqhDmLD#MHlI@pN?.Q+5jpW#UIlAb<E^Ft^8QlG[[b:>1ukp#``5lXim9-hE^V'SLP38mJPUI9iE6cd!Ok\J<."ji65eQO7HoYl
%I5L,e=qk@&S]o4W\Xb:hA)W__$3;i4\Ta-G6t;uc)[jo`e+V;HLCWMd7ePTFj3LSl9-s>Eij-BR`&`#.HOno'8V:Dtbu`QaP:X&C
%r3tgFDF@VLpL(Go`Zse+#;EA1R@BHR`jXC!M$@J=^__7@%MFEp*<EMAIL><ee;[8XC=!UT8h">L+>Pic=?>D7p&$DFJ_J7T
%G_Z^!f5Ii=W>\5"Wg!lHXFcmk`4pE*Tr>9Tfg)^<%fMF(E1=((?Sns"KC,$&G*&/pH?fm4__!YZ!I-,j5'<=#'aKB?jp<?Df@NuL
%+-*?rn;L/8[(B2':QRL$/$>mJ%Lmr"mif\Fb/PDR,ApR^c21[8@'iNB!>ba0n68!V_,Q9o+S`"si9.C;8E#1?S`..)3*j[88RqW@
%W,_$f6PWf)3+W$Yd?[!-E<6'S^%h2`Skq4XNH.[B&Acp(\t3<M;bTqb%5V@%I)+PNW5VA[:kQOn:bGE^p;;XcJupBTXfNB&Vro0Q
%oLg$i',6[j=[eG$Sp;g*Ucb0`34caK2.Z2\>s%n\.Q/2p$B=tX<#p34R>,1V[lh;=+,%!VjDX31k-#<sTJ]NT7kd'g-gPo:MZG+E
%F$m-Jk2bP_[.4m&+VBL0I,+&]2@TZr1PuBd;7q=V"U3TFA+?b"ouqgZ2c%2_GdnQke7thK/\B,e]<b?]^1\SG:KV0A'L>W^Er)KI
%`]__\4EJ)Y#+`4:/gX3`)bE;B*M^UqHtBTeV'r+pRPKnd4t&'pn#@KQ3@@&\8=Vdj'300ni;!#QW(me$^AD+t;NW-X+t<ucSg&+#
%>Xd(=la4S7qi]6YN`$$=7HYS?feX-[Q)^jm*T5_,mHT!dmE3>$krNJ^>lC$VRR551@GnL19lgC.hpXoY0Gic4jB#^HY`B6XM5m`&
%#qmGA%*$/FXF,gB^pnCs;B`N74/.'.pk`,Ma?SR0W&?rh`DLg\LI$98!O;<h=HTt]dX[E2H4o'b)_O:YMR3WJOtGOB<`3JJ+Xg5p
%0CC2_T0B0WdQ_-$g2ma$9Zt7,if2P''PPF#ER$_T0Hb&U$J>q@8g/F*j&KAJq%d*pnKWplT9!@nGD'&e4V53l_6?-&rU7&k+'^ph
%.?I"=3'm'$OGPNB\2iiR8>$;6.<X^<T[,inXLOB=`LE1D2e]8#OC+De]Ej-&Y/d]^:p'\BIZT'?bm\HV*p^&>U6_M\+D'2p(>Idl
%,T_6kaq>oJNdH;SSjdbu`tX$c:7q\=@shIGU<X!cU'a/Knn(Y"R%;W-jhq<R-8i=B[l<>VePA*#;t-Bu[QQXFTRW6J$(CR5H5`^X
%>slI!-R)K\/S[B+R`]$r5[U>NLU:YlSd23Z\%@7k;J??ZaR>CjB*'f@bu)Ia!(oM*DC>ju?q9A[16>U%pekJ#F`WhB>9GefTjP+V
%mjb>)oRZsM7I"G%:V/g:D.O<POKrs7rC!o4O<GSiG2ZiP-fT\P6./j0p`J.lH0*6rn=$U?;cWnh1`WJcG_C]QcCTH8.=RCQ2O09G
%mVd]j7\oB"'4CWWNr8n+%@HUp6Z78YngKO&"V"n_V_rp#kq<'f;hEY2h$?/rO2C?\TNr,k@E5-G!%[+uY$;=)0brU)rQ63dh_(Fb
%'bl%60/\3!#Y_AK-pSPr7Eh!C9mC_/^nU5TKELp_NVgEo2E47_\LrM5_3a%!"?jZP@-=Q6(<X'A70\95eO"D2A#T6Oo<H.1#^X/K
%K[O3L@W>X0P-4&J?d_ugI()eJL3eMY793VS`CiH2<".3<Z[TkrYu:4.q3i8A'XB:=#Z9$O>KkMAeMl<Lke3Y2mAe&]HJptkK4RPg
%LE+Hik_eq1U2rVQ4h43Zc#*r$9);(r-^?YWOQISoqOEfK3PNIFU`7=TdA=M&$tf#$Cbq6Rc3kU<bl3P4n<`EgF&/Y`qk+AEgX>=P
%\W,Z:_#glMnJ[a\"#,FE"Qp)u,5ds#L6lPEa;2GPd/o"EjV[de+bf*/?fg&fUDEa"A)Ta=9Y17J78VF"$pFpO\,rZrHns2jO*2h2
%U.2201#kai0`@iaf?AgR1oaWhq?h=,f;G<I#(tG22b52/?$3.qmON+)CZQUH'Ju$XHj/Hu:(/F#rZl<_rdb@c54l*8T7#jG1Da3R
%#opC\%FoFcBk#.XMF4)EOMRcn0si_n8_d9K#+fToSHM.7b#QL1V5B=A@>]77^=HNiB:uF`kOb^Fo0KCZ-o%e99m;p#FKUl[&&gP&
%&%9)INlP1f`WK:%/J/$hj_?tfe<MHZ=tM9k1jZ=M6Y[\>m5U.M5r5[1]kC$3KK=_Y9-iK.3cd8WE5FSYEo_Q5Rm'2,;8$h1"B2IS
%EO2RR^>Bhn?3VsiY-PrY545.nR)1'JGT?)r->%86=2jU\F,82jUCo;t?'AnBs*L-)/am<<Z03Lbb2jNsdrWK%F\rGlK/6FGWX3A1
%/69=1]$q&U`aCC0#@n-X>0Z+1B9-Y*>M`tqQ]Ml-%_Ri3P+CI1p^i*_B7I0<ek_X;Q3pf*)#Uj$,C"SD>J&28Tks6CNCDiEe-H4b
%2q5#BUe_92_]J8^\1("$]L%ThAjFE+m-3Xa<rJa2AX?=i0(9X"J/;Y?[JZH$TkMkHXD[YDQJTQ[3?!t4[N_W1&NGrSildqe9ioqj
%$M=iTouMN%M8:j8\_.&R8Z3\iK^F92RsjR#*$)(8iMBca?gs7@"i$8.Bmu[SZp]P(d@IO>?Ttf>eCd"Y`enh)N.rO/*!Ok,_$fY@
%e[I"P1,F:j:_fd-KLMV@2QT4Vleo#$`t_W`SHNSa3hfShPrG4&Nd),la*;W5fB+[G*@YsA-P)kG<$$fq^bTU#a>.)q]'o2nM&$W<
%:N'ui%oXE\b.RMm-9]CQVGFXh"`)TsNtkfqjbr?2^7l9?U240q07\NHrM\a^eJCKOfdf)gVhkI8,+Z^a#KsT\-N`&a>9$C4F5c(W
%'m%Gn8-^)AF<@)nN'2HDXma%)h;S'_3j/K-?L#PtI1IN[_'ir13^6(]F)-mS>g&jc341[hcG;t2d<!$M)'-$'qJ8t=eiSE`'RQ?O
%(&Ud8Hd+j7d6l:jie2R\,Q)OfD*@k<lOh!0'p\e!?BSTNI"7po=VGaB!J[Y0p^gP-5;&u"_%UB]s"]l>BM\%8$X<dIA[6(\*M*Q9
%6p)32A6F'0G+l[f/*`R'ArkG`fT'gZUf4*5X12PD#j6r$@(#BW[1=QUT`Hu0o#^%V9eV)$)VB^lN5TM)?ms?Jj=_2mAsm\^V[QtM
%bS="@A<iEOcmkbb5fo]pj)eg%Y/YP7NNJ8J)iCj6UmE.<)-O6#h69hI&#8(+Y(b2lg!U'eVf@[DG?&SgaL;f5eC-,k?/$\C,O2fu
%']qYg^P(+AH)%jQ;XkaN)Y[m,64J4G5;.64&"UDb%61/Oet8\WCJ#g85]:)fZ#)Z/NNNutR:cYKXsR`mD1R43FqNT],sF9c,$c^Q
%_I*i6-ui%s5aQGOr;VXfkp0bnf:^P0`$jRs7=pq0PiHhp\hkc8e*OiKF/$)rDMo^HClu(_5+Y'sHZ885pp&[B$2Z@7=S3T)rPgI`
%'Y;WB.sDcIHPQ5Y4r2f9AH8V+%YCG!B5L6Z]+)@@A(lM#)S!MNPmPk/Sgmtfp`03JQ:CG007YD/:UX;f\I7[N9.$:P\-5;kngq%4
%FFWI86AeYq^@JmKr7E)c3]J;EB^^%jadKIU5j5>D:lQbcbl7c7=Ud>m$/4`1Vb.),1]8.j-q?VGZ>'uK!/GM)d)HaOfj][r%\?hl
%5;2+Y5neq2W\ond0]3_GU"Z3nBEL)393>'Z0!mnT(c67<""?Jh'Pt/'F>#@hb@\E^N5r:2pjmF7!,IMta9lei/N2Z?:pe04?sSNo
%]#l#sGI3o$dSLS)6_6^("H-A\%\97WAK8oKbl_l2BV^WlHsZOH_:YVb3Ssd+2sMHB@NnRrf_D`,`dEI6hZC;RSj=mjU)`bA)0.8A
%JPo6fZh_XG]*+3OSSo/+a>5\%mNhm-[p\H*U27F02jP!mY/NbU>QCC^2Iu/fQ@'/*B^=JJIlFH!h=%TqCBKcgrcrn[fB*54*^d^"
%EDVcni9pT76d$^I1L'I^8eK-GhI=-3TU:daQO&gZ)<-M9ch@BTEf]_oLgfGM*;Y=,^eHM/\7BKZ!^u!$?\%K9IFV;JWfdOal^(tk
%FclgY6=GjBPDDV:GtbXb3<sY%f!]qffu$HACIrOY>Wnsh)'s+Q-a<oQ=&Dl*&?&A5lYr:Fdf?o-n>RnHp^0Fdj\eE+ZiJ7G(/Yrq
%fftMN:0NOTM%fiQdH%T5okZ$K.qdL/a+$7*.-"KR=-+[sjt3BNaNYb)RbJd)RsZo[4Ug4?k9[4Li<Q@W$HHT+\eGPhF"lh.M$;hE
%1j`2#%bQh="bZ@c5I#K)%>\N@4oDHVK/"GAo,UYD9],rH!;;Xa+(DX'7p49B@ZXRBr!]<7&B;+rI._,Wg:<fV9^UCE6UfJ9]Nb9+
%<s5un^0JXoa1+\p<eJ]=@N<R33Bi\?$.EOfmbe-mN)6LAjM)rRoqGpPS".P3Nk)B;?q:CA"IQ;WmH]i^'))hMd!j]<>Hn.Dk2BW-
%F5Y:WD,iLp.Eob!%Y17r3,`91P\:,)K>7,XpqF0:#J+qhYs9ai-\EVlO"WerajP\HCjdUl>(Cb;ps<c0g-R)uW%0n,+r!m6C+sJ@
%mX[J\c8\f.Xo&S9\Ep"Wq40$8NeXXOOB.G?\AQb(U83b_1h+"G_.WCnRLjNNe`D+[`.7jE@RPMa47pF_&%ct<UO!sk#L>p-b4e[I
%%hBEDiuAVU)nnls^>ZK?n[XX%GiefK=">9<dM)0UB";]iMo0(;+Yjmo-(k84-ao^gJ`dud4g5AHa-k(Jj-1<la.l";?D4N2\sf-(
%o]PN0#<j4724m$%c.!?=DE5:*VE#/tEb#6"kRF\ij2q*jS8.--,"t-=r3$tSXb7=$e(:hKCql\_.G-7/0/@kIMt+NJk=UDOT#'fU
%Lsp+OY8n$2nq?ie9)SGg9X*SRH%<k$gm,_5<"XTTkJ\#4Virn4LFF)US:UZ*;FZoBrWc"4%9/Vma8]WJ?Ms$dc^nYFp0TAP*rckO
%nc*5ErD3U\3sAF<A$u`183W[-<>A:p.>eI)-k,;^"sOe'*<;Pqp'EX?9RU=s)$]$A(mW8gUY%`^!>H[k'NgkH*9reT_4(7sk1*dP
%b!"P"U4K]L>#:lUN"-A$L\Z(.;Bj20O"R2F=#`3.m>GG=NC&WB7hVOu`JQ7XR6k5]\B`&\ad6D<CRNV^NfU;#i,hDhI>V$2?[rXT
%J'*DWC4Ps]"->P4%N;*).2`O`ktuHf4Zq'L;Hl*S!b]V=9c5j6JGK%@dWnfrb*+?:DiC;GU'bj/n9A+4oC;]9o6&`gcRTQ_B&9eI
%"-0=.3uM>1`hV2i"CT32X=G3s6m7[@0YNu*4rI7rWI[FWYNFaJPS5^!?!DKY9C+UmjF+.+-WC[BVZbk,G8R'sd4B]-3,6deZ\%b'
%p=!c=m2JU?rfrSdVlFGZJ^s9+iD;>o*ip`MLam-?p,6,KJ?bL6dDBM`2cjXAMNam/&QLqRU]=eP/FlV[ian2VP<AQLh#5Gu,![ui
%d2g]SSm1(m0X7ZR7$SJ>0<mRjfaRC$oJ]c6phg]!26J(X"fa<-J>n.aVc%LF'l4$YRDc%ok='+6X`\;id`2&fYs+"3`^ME0/<PD\
%\<-[L-!gP?i2@@3da<[S(5YNB@*k$(>.(<4'G5?tPT22KUgYU"UpoH'BGLJ>VCp\.J3>0-#>L;0;MT@p[Ye,\8le;Z^J!a[Z(h-M
%gpL9VX8im_ar*RH<UT'GZ"+!NZYVE9%qEYB`o)nI'a#O`?6&/@nP356gHiM-(EMm".h_n(WjgW+FIhk8kh^21FL-J*n8dgA[hA&"
%iAMbXC+1S@KY'p:5gS[hD=&e1WJmS-gE2SN2VTQE_#WQ!mcd;.NOnUX>6]\q*s,DjH#ca%4$2d"*,j88QM_;p`fFJ=PdN]d;,H/U
%[D'0'nfG*!?Y8(\aRHSeA>D-`*O8hd#=rBa/>nn4JE9lpGl]#]n3__gncHF^N/nhbU/XG[\G7I0H"?@nL+\ut<5\f%TdKF9CMYZT
%;PAVu5MHWuQ$LVq?ZK6\V.qA7N)\9+n^,N=18'=I&@82Fg`g0:PjNeS;.d^Q%*<9tnIn5Rr<DkV5S"T&7T!C:=$RP>OZmSYY@f!3
%>NM#g@[NFudj^`W/'XG"B=f+Cj*G=8lH%U,+]&J[o<M1kHEd*69H_1/%)8ahhc$qYC%)ntH5)k9,s`P+.Kh+T=;/k[5;'ii;io(d
%nm@hK75#&(nC/LJr-<s&,MlW2`I[=_O0F:li/gq]&bIk!6-R'^'e"X:B3*%S61nNnnd5n``H]lQX)WLd^%apQ`h1uNrM:X(IosmD
%m*/,;EX>A^c%]$]?PmFB:qH,O5(6Ph%0*Y"FY1J.DRBar%ljZb"BAl`<3H.=#W's[FjT04;$$[!RaX*MDW0=RUOq&`>oiSt3FrhC
%VE<_YDA"/6lfg0;eeK=J=EG'@C%`A5RUdbeY-#dZ(//S]!Dj#--i=n@CElC`g?.(naf;:==L9;)9H5lk?s<&'GC]%TitisZS#nc6
%??.8N'`OQpmcpqC\CpM0a21]6h`2RH?Z`tG6XL)Dl`tn9dWcM.Y71n`/%aA4j"BS"d8#M\gJ07\\EqnFcOdI13C;*jQ3E6;$?Ft'
%o!.CQA3pH>8G]35N]u.')'B7:$>tilIDoH6:l5T&=jHVaKcP/R<Y]^K]42(S:d+^!*p$Mm'mu`br.NNr:\jouRf^8VC%PfC:0Fp=
%4fl[3:FZK3`%=,AFI0O"fD99NBIYe5qsNa+7V%pj!:dR9EDIJbH._a)_hi1<NA6>scch>X4A+4OH%VAVdqoS\Tn*Hjk^14?+['7*
%g5g4'X-@>GnW]-:OdNgKl("GgK]hV8`(d;Rd)&$_Qj=IQ;/s$[mYk3`8LoF.j,@c]3?EBh@?c#e@WRS&;2NZmC'0?8nVp70XJN/U
%qYcf3>4?3\/htm7dT8AI=bl(*oK\P%*bO]3]aTNFrmit7?bfEnaP[it66b-H:?OGVgMp6IXa/1a1&?64nIGfjqp+U2Dhokt746F8
%?2E-'PCRpn3IK5/W#Wpdi.+V,W'V"S/o&jq`qirYGk#hU5#"R1XWHa*-U,DC]ecaDpuMe'#4''+!";oqgrW:ta6]%E%u#q??h-X%
%Xagr3,"tWSR0uI7Z>S+ra2U*oI?G($iOrc4=Ze-'/OXK(.(WT8!?7n[Rf4-ml8asj`M?Pd8nZ;0^bXuK[)SgEFcGHV&4[>l,"H.p
%<s&r\eq4ne-(QnZ!9ED#RSZLT:D,,>400oRI>^1[BHuPs?G4oRQhjW\j[?Q-`p?C!?H&!ra/DH#fOtX]:>!4./L"0Z*Hlm9MI!*m
%-`jo%p5b'Ior$K^03k_-I`JUKaXiT%nNbasFa1[F#\[SE$JGW(YUpB^H+CmgaJsp4(bJ2n.k/iG-b3R'?IE-`OP^oiGR6u!Q]Eo9
%64%0@i8%Zt$miR.XT=YIDBkNG5)J3Og_(Dg2tVjE&QPNSNO[T/2+`a-XH.4T,:RIcfK<"kPeP+$YEM>4<)*5gnY_j4C6?U-ds#I$
%5I4[!s/0RN!/1)"Tk!^^^<Z36:f)fV*$=gh(<7LP2G)C!E8tV;RZsh?$52JQdZ<HQd.:Ug^<7.I$B2%Z,3"`\O!$?+jLkatj@t@;
%ZqJ`he\-_!/YG\B:_L=KPKetGT6tTLJN;OqpiDb6_G^A*;$jKhBXpa2-OC>Sb9b@d(("`+3*tpJQl0,PNT\\_6'H-.Q4BpH<`f@!
%VWGBYY1U"^PMed=_PuZo8?mgA0P,9,VS!+]"^>&d7kp90&[k[ZUmR7I;!b5PgtoNMJ:3uHQ'HJ"VBls%f>#CMUGN@2F1A_,%mTUC
%T`'"gIlmT&L\DVW#n!4^M?P*nT+`-F32!(^`k11AKho?sgt'48\o,0"IpTeB_"B-o3<S1WB"V>0/Jm(jlOE>k.)h<1hD[OpFg"[7
%*&Db'J8cqKUL0ls*=(ghO]aW>co;s*.aN*-"^[4%>?fQCpiO]gbr=lndZ!7GlMC7JYKIq#;S4et=dYNDPfhR1]Z8uSPqcb5C-lhR
%bWO'qLnh=6(S?HV.9tsZ%c>m#6=On!G#74QkkBN'D.sXKeRN]l;/fNtaK_N`^&QqMo7Ij>.b4./#$ZEJIo1Tm(+OBe,=>0@lR44%
%"=W)YARI=JWt.^Y>QP.%1SYU)JiKK*4.%)$AE0%"1D]Zud^E1$W%6/h&OBB0il<ZB<FOkbbAkF@(-QT8d#<M!4[D`BatK$u/HEna
%qi]^DSOe/4*8?tU32Gd2"BXf!J3^$k>.8buZ5&Fo>qqDDc,L"Z[Qd3*M/A-:VE%kcDKS]M&7h'%+ZS*H0Dk-6%GFml`XDsWWCdF-
%M*9Y=WDT]=12-]4aA&*I#l`f!F]Dm:D5g0dWi*m"E1M^3T&L58WC=X;b0G1oFSm_.iE=8bEgAp/.RBd6_W/>\I,M^:\!VGjMeBBA
%(ZKV;e_>)*^bZ37f2MmFPr>cF7+ugs*a'h"<S$ci"%<fReFXA?Z^i2%#?Q=3F?f`WYaY#;TD(cd-rAjFQZYE`84^5ADp9eVjm7q]
%7D#XXVLkt/@7ZaHEIN8'ca/k`4aafUH);!^#RIjSZg'Hp$*$C=?60ik1t8Qh\S&ENi>H5<^[Wf0OO;T5ckV9iq\fq-Y%YKcRmJ0+
%MP?c_jW'9"Xg@gVZ03b'\Y*Gh`54%POR)F;D,.onRt&PHq^nsI`%)p/*Z=KX&\E$A[cMtKm/p^6hY.A_.:Tg,:KRI@p!138;G!+h
%g->T8D\p\@]ugHE)Hh.?!rrV",b"^#e3Ka0:d<gnQa.cXTiANF.@O]O35j^;-'f4mD"*dagOT4Q`j%*1=V*&DS98Zph=o.cYt=\K
%n[imF7CB?So7A7.f"Op7+&4/m,+UjODg7Ld:1Ir%4)0t[l,QH]%Z6Y"Al1NE8DT=X$$CTsje19/F8I?(#GBn=g`_ONM_^_P@>XV`
%BJIh*C?,!oKo0ic@uFkiXUt#r8dh.#ji<8,aAiE/=B(p]^U:#t*!$&8I]t_O/au_cCA(>tQ1U7!e4$TRdIY0)ES<mcS[JA(XP`6I
%Z=dgS36(fF09EJ^X6ICkB%iCi)G"'n@M!Z(g'Q(QN:0-l]sOF>+75!Jj=Wdm"<g;\mm%&:`ssLJhTR1KQrB:SQ(V/mS>iG[9g^`M
%(4>tALthjs17#T%4c\,,'sk?[q=\=k&VW>Y-E&DR52L9sZc5S#PaE.h*oPKa4Cu`8.]Cf17UNuc)GZ/-7n]*FY"dLa<<m'!XuSp)
%aSeZ,m=`N)*`M3\o,^a!=1*;VWO7?9<-&&f,s/3cq^S!Y>LQ9(q7L\`&p;"\Ho4=k\;pLF8MUc;FN*4F;#slO,>K"sHd$A-jX@DJ
%VY+0:%/N4n!atD<T9H/-e1-%#MWQnkhRKk#/hS1#+;S=^jacU;m43"L4a+6_ZiNH\WjsMZqdA9MHsX<LhU$CnipL+;bXmGU-b)L8
%Q3R(BJl60V7W9,&HG5'=[HK,c%e'-sV../(W:$fohg]S/(eW8WrXSq?XK1=FVUQX&0&[Yg@?3MO6e8"g$bXjRXLd.(#+@e2dTp..
%9=c0D,jh5uTFZVKSlp6ml?KSkgaO*"#Ne^k6.jaO9SK)(`j,k-:C=(rB."?SeT,5U#VSMm?drPOT(e+Xj9m(2T"Y447d.mY=96[H
%W'A^c>e:lLYu?s;QQ/U82gn'^Vb!LpfhoONMFcT<OVS/#&C/n!`-4C#`EW"pqMdFMfqRYF/o??!SWJ6](X<BsM6W\^k)Wp/g3P<k
%W1]%TBS4mHhSHi!g<n451WnE_Kd%MAUgGm[V(rgE+RP^aJ?O.^cY\4HJ3MCOh\;]D=\81`r&SI;-I'R^(I>D45hAY1=D!\E=h9k3
%%9r"U@kKWcV$Kb75HX?%=$#R-c5-jCKZqR/Z]1o5Yi*SY!I>t3V);9)8c4G_Dis?fHG;7RI6hTZ&I<nU09h>[]_,;)YS-q.m15`U
%0cL$eKA`BBqtpqOQV\Dirt:&1phl_0b2Q*diI80Z&T"Y2%qJS&lTjrRUph1V.U3")=5>05ed,1:$<-6rD,&SK\3l=F_H&W1<Lta&
%V#_kXLGLl-X>frfI7^%&1<7?`fD^9nhR1D0j1#kpcNa"`l<_7.o"n,`qdDhoLnl<^RZ[qSp6EZTKOT'q/??@n;-fDSJ!f+W44];1
%b^K2!W^V)0Cn$S9eqp_/mX/YX`/593r$p!5LHA%;L/\e*+TipLGg]7&>tK3`3LV:s<#M31Z<7R@mR*1j$^(<A@JAd$`9GHTSnMKA
%&,)qWmfu'i!DU,U!eH/gR+[Z#]95H>C=T=eKB-$\0J*<;!W.,CK,G:>W@/IX"L?DkfQ8?lY]!lO*RKA,]W6;o(#J@=JO4<Z;FiT7
%</q&45abjO2ME92WpVm9NJ,Ih)V]2"p'PmZJo8qkfRm%CK!^)TL?8jDD^QK$aB^u!W/%,<_VgB5b8(R+;4*RP9?*,%,fVAf/9%%_
%5^H_1ZT6#lZJKOFMhuGZ.[N(\:Pj(eFV?_:O#@#rAMVkoT$M2Ki6hHFob:W&c5rm$/tg<:l1W"c,RW`eml_;?<!LsA1b3!KKM8h;
%7tc[SYCt9%(#kVbbXU_U+D_9l2[1HH.$mLkY\Ng-`<dBN\a)\MQmG-[,/"_MgQ(\A@n<Hp1b=3tP<<SFan]U:Ep=^SSi?O1">"em
%mE9Go:J>)_Plg\h9u.F/)Nfaj?]>e)m3>aS0cVcqn6te/)C[qF?<rGp#T!bn<$jbiFcI(kblj2e2@Fm\S9*]%ANW+j>Bt8k`Tm?O
%#,kA2;+3g5"Vb=K.#%OA$m@#!TdNU@7Bl3t/rn.`A3e+Mn=!V=)r(*X^b3DD5A6TNPV^QId3?%=nB0d#M3oNB;g@-E88-YqXp]MS
%Ak'S2DG?/O$oX4.HW'@DR\ab08hpbH[P'se7<6uVO^.tB"j58kCm/Usj'IVu?JSRr@?DER'RG@bc\ah3!5Q3U9hsIm/4,B65)=AN
%*ca/a[h;<=9/#WB.a1op&=usu*s+&ZP!75=j*ImX-kg,c,d%WG4Ilu0&W2;+ZsY1)[IPQ%@3jm5,<h[I"K<FK`\u_;L9+fj.;%,H
%m*d4<"WTn<K$:`*l,juE*CqU&C1B14d0?a!@m'gI4;.tA@S+8Y&j3WR\P^jmFkG8Ij;PCi[30E>\mX::V("tXEn8c8#W%g*m/js>
%73rR!E6F&S$&<1/eJ3AN,@QRj/.OD>%N?YT2Nopc3GRhgM(B'I0M@$l)*+d<G_o[h"GC1JP8)_B(66XUe?&MC"Q[L%q:ILSSM0%[
%_DaZKJ;M=6QaTNkk@1F47GfMDoQA7oX3lgH6<])ga>7\#k[ML+F[Sl+(pOY!1'K8NY_k/tAVSM<%2/8gK'f]sY8^S@#8:5q2lZCp
%4U6*=q!!n'"drgj\cUkL-@&0-Q;hfY5&(EE$Y=3L_b(UJ_oG;NPn_8ii(P^&8KJi4aqZNq49?K-NR_Rj,IG<2"ns-;EotsdHq,?P
%RTR!5V.9>Ui-F^I!uZV/#$G$knKK?1SXA^+nO$'IBRD]T5CeMSTmgPV=Rm?'JSQ#^RqUdpe4<P6*bSf$U!F>"j-;J&W;n?uE,=KT
%,L\>%iteD1dWZ!u.?1,P,4A8?_pth'Fu8]%[^`\SLa(ZU5Qk[>$8+oQ%143L3RKdFH#"i[f\7_Kai@s<NNlImi+/G=C$*o\af/+#
%;e/j%,PMUOa@,LX&_UEi).WjQjckY%W#"S0q/!Egb[Uf4(KMBm)E7A@j;IKRN_*)':<nt^S"7A\6'Xo7)ut!k80q^'8f7o`Z.Ci>
%5d%93A0C5&aj&n>-jW?,1D(5OcBCEuO7E+U?\#APE_eSAnH1J10PSgi#rP@aU!CQ<'KMRb]399`=D#F9D9/1tC$%b8RRR<%04A["
%h@_5O==Jo31nn3fSAZ:O]jT=dZQ<EBmA91]Ii[;=?X!/kD-WIhE`=q:7%?IrH"Lr3gh030-B1dsXQAp<n;?L`:eWk*]i^dSm_7AA
%mh-9b9*n1h(b0di801b/aSdM$"Gf;TmN1hU92SX\7^LlJ39g[%W@KJ"p3k))1hHha""lp;EaoiKrMS6.pK$#Y:qS[/ig>-3.?cQO
%>`5hGPqOMXd22W@N6fh4``.U"E<TK3[J814#u8DajH,aCWa6lBg+AlMP`?s:7`2;^Z@INSd-9Hsb)"88bnMXs/=qgO"b"fFAn7u>
%ENhs>9/^@+0HK?()m8*Q$[L1Innh/ZF!*)])I]_kZ6E64$+]WY16bY4BF#"+/bDo$8>.FDB$/FPGV5"a+FH\e`n'fS0KW%#4BRhh
%dVLV3lK>,=br&ZcE4>rm7.C(%Tf<L<7_"r1\3l0/B;UU0%TF7-RqfW'KC^/D6"h>lDHVY8h_.<:;FVu%]c&hnOK-ED*=p3\E10+3
%<KL>R=i(`.++-JM\'?<j,0KPpP-#To;1_FS8L7lBI;]TlC0EJp+*"n!<?(*m%Qj)f'Y51&Y+Zli^N"2oB[1ou;5LV#,]<dLQgA/-
%<n0%&=TCR*6W'6B&13lA<PmOo?8-ldD"J=`K"<$K*Ps_):J=O[3Z0nOM'a@Qde$\Gfa7jR8NqblU;LE2mu=D!T(^Z(9Ln2!_aPEQ
%)C1S9%n[i%j'WluaiHS20^^4m$i(Tj;=d&oUI$;l)d-;WN:m%O/NsdCW]Wiqqc0>rGJu8a"[F-2KG\hl#jSZBm0E^l8A+A\Q<Rh*
%"t,R3b$pJMchFPli1%Ha7;j2toU<AHXEW&f*1de/">:[*-Fr;e[YJ:?U9JBq))4c/Zq,juk%O:t6E)ho9:G,6oO1R*lP*3.T#_lH
%-im(Jap[g&!+-&@Q!bcK.P+U*B-o'`X#7'3q/!MHWC.39=o,"fKI'OJ<O7!P`\I63Pda&b.G:4)WrEH7_)oiAPeE?Dgb:@;Rj^7]
%B=4"\0P;s<&\KGAqQ<$W\fd&]>or<:?:Vll\>7"SYVP2i`QaXiOpVQ,73RFJN'bhj?,8Tjogt"Sh6bg8r8Mp7&Q*S1pR*t_$;e/?
%,gdi*>:,WgPnfrj3@^T`m?!O*8#iYT2attlCn)hR.`#5R@jY4)W7kYQoR>;kA8<m#+"$@*^WQld3]%rqk`S*aWJ-7+c^U]/VPke7
%;E)\/%=LuP"f)^j,gkBs70sG`S]W'>&pYP:5Kq'>?H8d,:MiTK.bP0ujKM/s-h*US'I#g\)3^iJs,0`K3<kd8Y-(a/"a"?3Lns@a
%#=/YC6C3E3:ul$L&jj$1\0j>Fr0!nYW<cJjUt<d_dN"U*r`fsLa_*3F!G183Q:T&/<\tpTiKaA5<43?nA1KrdA[eOWdsT+)iNp6,
%E&2pJZN61s\YEca`#ops=_cMDlHBp*@4g3@LoMBt.&"<7:![^a/V"8D7qP!4^0tqKAo/Mnb6oo_.G;%8"B2"+.6<FURsp%c8tR$.
%iO*-pS+ql@;n6OIE]S,H&L`NoHpi9Jl7k^V[>,j)BA?;kZDCJIG",+-(f[b5)H)$HFeDH00@.OoGm3Th,*1ZF/4hI"0FrkXK-5*C
%%o_"jes8(s;hM!K2s!$'Pjq:`DcOT4j2tUqTgEUYPe-,[[D\&P3%,58B/aO98![?9N'//c;Oj<A?0JMUhCV3<]#BoAb@eh:a@QaH
%7h:Yd?]EI;^nEMUi%5E3Yh#]+/r$5jTkPc@(/$S(4_Dafn7L!b5k&4cI>AHRMbW8haW&>eK4ZcKYQXnD@.;XT#]ZG9'uJfsi*Mgc
%F!:_'-rcbobJb]1Z$MTJ>5IIB;?A2(5l6e;8XqW&jI?:8%Glb/g&[nc4Bu"igL+1k+]o%YKi+P968hnu(@MEPZhbG/c#@IhOKHA<
%rs93iedNo<HP(PiQiShhQ'[-1:L7KN81n@SNS-=?"E3Ip1aPQ!S?`RgVR@u1>8;E:`0\:q:h4F4-W^_31-7]n:aHs;lbP'pN$Oo'
%K;p-L>X0(VX#Y=^+s,4tS8=a*Du?>d7a*4dZ6Tk>acLj)L^<,o1Vt:j,qHJe=sJo*!*39T`!mMMbnj++%68Ih:l%5Ob]_fu>V8US
%E/?,@^(pb45og`<OKP@I<<L%S:8_;sKaFpiK<f'PB;\]RWH_o2!=B+LXRX`-]SB2miit$24VDn:SClON\SsZPqj>_K$Z,\kJY%k&
%G,&9*,JoX-WH0jg\fEB4(Bu*!(lBt'21!KQA]REXiUP'F1Y><?d*-b$>Z-XEYk#$6*DF<afeL>\*oD&t0[6$fFDuSgpaq&HN=qKJ
%b&XP&MY+&>/eZ"(11^J#@9g<QPen'mC/_4\QR"mn]K:f#=@<a-SXI+_iZOEKr%E!^!qj9p)pD=OLd*W!g8`-hfeHWk%@X)ni%Dh+
%#*E&A/OWj<`Ba5C#B^jDH74&L@]YfV\JY.@WGot#AWX/'oVa$`=WqL7#eAsa/eB5b_-0eDSCH(;JF#Bl0UKhtZ'Z(qNku?$f\9:j
%kp@4crRU%\FGN\T/s:2"2p5preF1TSQHm]`Jp6d"G)l.6#p=ci(tm/^d:13k+^L[VFC80phhEN-&1I+F,>j--`.[2RoTj#fa&hf<
%8,:,D0%;\_2uZ2Xl9<oO^k?>GAfg2`]b8%FaeDUgZ6d-sfgHu_h[\<'7R4<`N:8ko/kUO-:.\@M<M^cg`:-Z<l't"'(b.FhOo*5/
%<V#mJ,7bo=O@'(e^fR@0"#-Si)eNN/IV=[s@D`r4!?nGVU_fQXE`=*e$KNE;?lh&f5a%4*BH8o"iK?Kh2HU96D6[af\7cg4F\QG-
%gqH>:B>KquY\ph(9)=:6c-[SEpne>iL*FTs@pS.$YT`[H1:frOoFg<$Q;gnM%59@JA<^:7ZtP[2,"Ak!Llo"0jB4O4.;[!q>"?nL
%Jff*kp$lQ3Z=_U[qt^&B00#'ZUnWC8,*%j7-:_fTaM#SeahtA5r2GjQ%$[E>Ef)[:$4%a5Y:;,XNt<,o)<chR.ZRX!50.m;LqFEo
%N8,/7Q'IuF3=?T,(atb&H$pE:#Ht3/Uh3GKT+<.j:5!.I3"\&Mf+N[EQTR%.mcrO@is6fG?:9Ou&[g8Qm8LX%="?NR0`V9UerQ4A
%0W/V&7R,ldTmZKX.QNN%]*U8YeM<<4gYEAbgpIJ)q[R&B@BX+(Yb);Aq!@]@I_is-6BiYFP:&^m[3gO\L#a#d)e@*3F3XJ,eFkK1
%52Puf@Eh?afob<&)'Qa$A^#l5=UO63N1BF5!;0T.jHm3D%/^n7I%:EGAF5%(Oq0s6Tkho1?s^@0JL74.a[kWu_X:+RL51Eb*DLG\
%R!YTC_)-TDW@/]V9[U!j]ip1?KkHe#<nb/?R[f>S*")(RjUjR15&^4TPCQbp3'frEZi.7&"K<?&R^fZ2MSk+A`";E=/\FN*7]R&#
%$"P8g:oANW5G<'f,&Fo22/ng,$5_E'8\5lt!7_8Dla@C++L^$<]\hQH)I=Cnjg9PU<&5.X+*j^'_cal_RqE;DUatP--:/m%^9SB9
%)GK3pM9:<EMAIL>'+PA.[a97Yf<)a&Gq2ld'-tem^i69S)b8'%iur3_Ep'i-W'+lo+g)^tic`._?0;tEON>KTi$j9f>,^)K
%!M/S"#b-fa>O.qjHl2Qj-#>'+7JD+s7ZMQS9j\pG^;J#RRH>.XX;o1l=b=U9o"[%!GlSF^+H.dI6hhVL-?7\e@(-dl*M_+'K+8tH
%Aru@^XYDSech31;dY&F0jn\eF<?0P0+_?h]&?1;O&]J4JX)Q^;7.*MN_NAqUEb1gT\^CmYhSY/ZDlH0bem?>DMpU3UK?hS=98:XX
%On.$l/HO'>m+n8;RUodV5oC^oYN+=M6(cDuBL`\(7j5c3V9@??45s?c,'VSo<k(q50U50&FF`9<)='*R%tkoho!;:A/VP%k<0/J6
%k!UUuFp`f,JM7J<0SOZfP__AR1fI"nKF$[WX+dRPb*>c[5E8hPn<"]hO;D(K6mg]cPSFh2$-Tc1Jr-T*_:t<Wc6&1L?c7Sf=kf`!
%8<G(Ma!T<obu$aM?0+U"5Tt_/+=ncmKhr=H&0c((PQ5pW-BHAb2M@@4<.#e,qF';N>2i_@!dPu[9IhYB#RHj<T"Q8N0aQ1_UDnL4
%6=+p,PCBULX"\`:7ehN`P]pDmJIlWCjj)UKBO:"i=s'Ml*?N($r)*OY4<Vt]Ds.=\CbnH%3L::.KAPk:<?SWN#48\sWhV]Nb^Q`Q
%>MG<!36Wg@*Z9p4)qco!`D'+s1M[PZcGp]hbqWa\f83Rq8I?A%as@sSZjjjdP_Wq#-9;;X3b`btMsnpI9knSejPh*(lHKXm*<E:(
%q`sd_(CmR?_65g>.HId%#m2A"=lZ!X5ZscfqU";?^6%"mHhRpOToc8/7B!j^'#3'(I]9ADjKo&'C(pllkk-W/5tPB>OFMeY'Q$;_
%5"p36G`7tYmOeiNenV$>J@GY4Tfst52C%&;&:,(C$-im^)Sj+-AL0/G>Dg')8g(ua%:eN3$3S/O6muL%ZNTj+1j8NYBUZ!U[dK!&
%7-?hd8!Z!rh<=NC#e(eeMm-g9/$W54BJdCV\liT)<palPeK+gb7R+[lU,.T&X(VYMU<+BJ.a+Y?"TYgH?8$uF$e<Rj2M$.p0mBnF
%0p!Vq.HSMqY><_0,*0Ij<$eh<KZ+/EPpu'0p6H:4$@*%IhT!IPk$I/&4KPSBIA=kU&dVMBYY9,9aI+KmaOZH`(cEQ,ROTS9.8)Ku
%;-o$GZX)XSMZb>ICdlTf<J;?NJG^Bf!19BJ!BM\Bd&CC`;m2cQ-R!TY>UB3,ki,[="O]e+:->5?K:Q!r2Ja+k'uVoZAsAtDB@69V
%5c>W`A>E.#ABqR2Z37`.]l+nt563:iR&CK/X4B,aV`>G<Md\/B&1)is!GMqL$A5-GKGm71Z#n*6I!.k$L/m>'cfI`Di[i+;b\VOQ
%kB?pC5^=6/2<0P8H&k>('-O_O[DS88W#EGX$DC6T5:!O!NmgVk7TQ:'^`A<LaIU$phHYa[HP-6?=8OP"VIYbujA>oZ_c=A5c%9DN
%_t@?7'9ZS:p%tD)5iOA4<bfX\cj&G4i^tV`Tt[fFPR8=LT>ncL5T$"18d\MkJL@033j!<o,If-Vl;q.)qZp>>?5X@[lPns981uNH
%U0&b+;D5h_WBr@fJ:uB)!E)H+7kTeZ7hq1a0D$q:Q>$atNh*nKOJt0pHmV?P?knC3$7H=t``,QIp;4ocHda^i1:qri$\<+i:J;8e
%(%;*,8Ja.A^M6G@;)a?(aXY8@RT5:\I$F:Be@q#2[9ToXV]cC6ocm_B10#4Q?,p-s:,so!2fb"Q!;J-6m>)F(=llAS<1`qW@uFOm
%KSB9+#V1`[Pptt?g;$O3d="t'')sY_VPl[a.5DHCTKhmtbpB;QPdSaS5+(tF8\*EHD&[5+'Q_Pd'I#*_7CraB\d="L(D?G@YaZ1.
%T;tgUY;QgFjmbC<<jacaYb));p0dqFp.]r9:\D0FG%Y.]RZ._K8?)_3.TKq1RPLA!9_Nk-QH3C)m!7AOKj(u@]0KBu0egNj9,WD:
%rXFuMntd.WG)cWsY#B6&#r:2j8LpA<jcER*VBq1"?KEo24Bi-756Tpn[ZRWl_N8@9EKofq[>-\X7U$)n$?d\8EKT1-j?9SsFu3d:
%DMg,FklqZ)2+i7"YgDE"+%pV?2A!%WZe<C1N1(2%$0!*b.gP.//ohVkEi+NGA9bGY2^n.!B/)-?pSR6^r<e15+_Q%ojJh`W!>;q2
%j5TBX3/Xs&&K=\$NM"9L0s_G(O3Ef@PX$)Q<M^2l^/4IL$RqV)Srk=^ShUomk%Y&6k/D,(_,d=aWl\n[P,Lb#?*NshME/^/OEHY$
%kG&KGl'WsF,A^71PAFE"0-k*;S.]8*(0$Na$:=N"Y,l6+)7>s<"%;/pXG+2eV>%U,)CcT,'r%p%gQ]*538%T\f"I#d_o#Z0$kSMU
%nuc:(ZXU2$/<R!4"h`h\98KA]/"4\B^!PM3JB#U:1A"FPP%)jQ)2g`].XBI=?uoks,sYd1Sq9rc17obf?EcJ\bgSM!jiRMY\u+^\
%Z7V&;/gKP[a>mr\FTh[":$e\bpeI2&M8J]X#cUfjD'l%&S6SUXd4C8t9#eK1*2'a'7QDDF"c,Wrf3HP@nkL?e3tQScZ'.jlKgH6`
%&/%ilX]cCVC34M9Ma,Ul9HdFJEF3G,/uFI=6O$^E5aRl"B'$(8NQO!@7a/6,AQi=!.SR:!"Hn4!(^rEZ,P0'(!Q9,<rQ<i#9+RMN
%Bkl!r.]+fIfo<41/(fl57]DJ_:K,kc':#9X=YXCBGf:g2`sqpYE">W`h*pJ,VU26D6^cE]P=XBDf-PD<0E"Im@Sp^X$*EerZdAt(
%*#Ji%JqNVfRg^A#2BfOkWpiTd\5DI1Z$sn"m&8^#LRSf4c2/4Cf#4&kaoPNE\KCN9(m^E^+/Tb,^.U<:DiMDJ`A/MA=g05n`=Xlr
%U'iVnXa2\rVV+%`&I<pM]GY'=\7>3U9T/'J6`7OLZ^$,69kD?,O&S!m!0>g=>9S&\$p'Z%f">]'HE1pIjO+iD#PT9Z.-;+3+V>68
%XU:7.,<<](P&Zd\\iRR?%^9K/7&;."SUfNUkJ```R2K#UC1[0B;_hn[O_7kG2S@@AW*'rX1"-R'`8#Q1n(Plq%^qpjHe/]>qd'P6
%+X_r_.ETJe!TKA,CQC`$moqRTQ8F=^l9GM&FF(qKi<kC"A@PW;eRC$a[Um!7`t8O,'LA\lYE:B53hn1o;#uDl`@OW%!Hh[@8G#%f
%>1$5t[n&=k#t6^'TCBZ/L^(H6_5&``<Z&4lEm::*&4MAa6f1HLWBq%o&i@r<co=\L$gbK+f^l*QU"9Q/a_P5aKD*BTAd+,5d7E;!
%XUu^$0/.W<UC!^(9OQ%T<'bR_;(e09>E`,X.Nfs_ZG%$%/IKn+lubHaKF(2.Bk)s(DG$o0WguWeA2'/R5_9^D`&NBd0L6YOe&5CX
%BgI)mc-@Bo_o:CcidD+@p-ne3?o67D_"XX%nLZHP68'*0.T=kRKa9"_1J0a/L0B2A)<",l,r@3Inp'94<4s%$C=)n1YX"92i]?%B
%@&6J!@GkT+nVK@HO/<YH']<DObqrM_[ck\q`8D])&KXH:Ui0.iN24rjF)0mV:dM.5%+O$L,b_<`q2+uDcr1GOoHOFJ][u`o1cd0R
%m.8L:?dPNd1UJB(fPJ*`,`UpBKT-dZh)V%%KoM<ubq^r^R>BMMB&m6jg@g)ANR"r6S$tde;&Tb/PS6?8TGoSpc&Rj>NMI`tNh%5s
%c\"=a2Bb^(@6jKTDDXMl<!0+ACU*p@=i[8]D42M<0ZVrgU^tDgWl!%c=J_kYb"=(elbhG5Jko-b/9Xf'hXM$Q1KqVaPdmAW!8Cj*
%&]B1Z:-*S<F,6=&%Q"eTnTpD)#0mIZfnibV$Vge\-Itfm16bcJo<2_1V+N[!UnV"M2Bf/L)UKQ-'k5j?.MQ@.BaTH#\?O*S-eZY2
%JdJmAMD:QR;2('mP?>0Fa7W\\h%Y\s=m(SjL%^?bLfj6c%4Cs_OLL.=&VlZ!^'^HQ67LBoYm$ugg^YA[lZ97mfUh#d,g$So@LuLh
%b88=+Sb,b^YDEobH&tlOaEoh=BAf?s:e>q=]9(i,mT(L[#I=^m+W[X7451'-aL^-BoroIAU9lU(67Ba:9`u"o$8X\]ah!jpRcd-,
%EKDP;$0f_>%PP&C7-YOc)bI)%iCj^d/6u<1%S%ECTeJsc2lJhGS`;kq1`KEp&5LUaOe9_mR&WG^;W,H-2UNrUp,m'BG/#hn&\q<!
%L!j4hC'F=/!g]$>&(i,jrhN*tZ4Z.JecN..kCj3hcuC7BUM%*6,6Wrl!/)K579a16R_p*i@BLDDYCt"(6K#gbJs.I,&0a!%QB7J.
%&etV?-&!Ue'k(+c5Z@'@_UL.\LhI?br?+ajju^D`Ml.;\;^DAK40jbCK\gQo_&Yi=L=f!\:d<SI8A[rA)p'l_jC=Q8M#Z'>jDL0H
%)91*F$"H\(hIp^B3\AfP=I1J<6g'fb'5W/oiZSBV/$hHtXd4YiFO&9-=Ir:O7^j/eMgXWIR,J)s<Vd]G/p.&)L?XF=N@TuHP)R)$
%>J5h?`@=<V?*<cc('hTdSD$r-*4R;*'%X%dfu3pk-ONj&&tV3M,3WUA:dV`g@Za1%[K@L![OPaXYkU@1Yp9SGLj>5VWT7k,gR)UQ
%oM0`EYp?tZ]#`fM@PIY_E-G"iM6/e:>sj!e4mPZPe5@Z\ORA@"V+`+8/Jbh;F]Z/:It6XR)u5e@\CQJ?`>.H28bNcW^"$]u,uckH
%MV;VuWrS3RM%WD*Vu`!)Pg7h?#kj&#n,u<sZB\Lg%NCmIjpX)ti&6`KS(;gE90EkDL\(2n:uTj3UR`Nemi/=E]pYF5b#BsIdKUem
%^bFi:,ZD+<W"MYk!D"B-1\`icp;D^EaYkb5-D1PUo:$22dj4C_k4GW<.>k31H>q8\'$RYu5T]/`("?8Y`m\0^<`nG1C1Ki$d(Q)$
%V3#VM4R.a&K6slB6*Hm;d<+p7i&)]O.;1VK9qroImi<:X[^tOn3Ab]E=<_'7[Mu^gM<*0FOf[mB+AabS-^9nsg71dG\9C-DVJ/Y4
%1#)Bb[2'ZH.KE10KVZ=6=[p:eYR+$/GFH?)R\-i"9asWs)[UQtRX.aC3Z85YJ!M4]Vo5+9&m&)TFWV'(Ra%!&@<]q%R-B[[&s>tE
%*7`dPTr)]m!$^3b_R8h_E(Z:l@fTLJr;P4ZloJMGi<VP;;Un)A-sbZjW7NEtScX/Q]7][).\s#GE?u>8@3stF??!A16:7:idPHu/
%>C>In3I@Op!(-+AQTk<:qN3Wa%5M)mR9``hd)1bP+m^Hc:;UTg*!h(A-&!$(p$`8R(`M)KX3qk>K7.s="A)?+[:C[*g0+[m$HoL%
%6jWD+^n@H"6K7d'JC[=hN-Ndj>:0J9.\/QIfq?A'VMlSJ,T`@aD`Tf5KV#/"0p!]_1Vp@HHY4!X&OcNL&N!(>?d_G9't,nYF((84
%?3VSS5`uLT5V@giFK,/i`_u8kp83(-.OL(T=XNW9Mre%1bji=D28K'oG[]2KjP';RM;>sN&AXg>iQP>2r7He,,1!q(`(\+@,LnS/
%+jWkRm.*CD@W-?_6OQg1/%m4O,;4CMehe&_fu]KK:,=^p1='@L>T[)NA1M9TMWWp(j`j$+3Ms4M[_Fb5#7P]bl<-q7N*WZj#]8:K
%M+fMF8#Q/nK]Ip8?@^9imiYUnjas7Yn#ds*ro<2KSQi?9;i/;T6Rl2m1QaaGCDSsDjH>Um8N_)c)Mc?\JX8[&ZX?p7`)Gm)<(4,H
%9X,d[,JFDMa)2&o*$,U?Oo+6bfFIQ56$YI1e<3"^Co]nHT'Vtb9JK+&o%;U$U-Um6R,Ba@Sd"H6-?KAuCX4=P<s`np0U59H)AZ^&
%K]*&5Fr9%=ab=$OF$Oj"!m@m2@?[ruVG(W_"(<<]D^597*#4oXEQh4/9H_A=6t^s:EWf4aotmR@[81U*6'`dcM9*)ZGI]Ue4ifBt
%aJn)L`0%/M+=WK.;YSaK9F`ge7Z122kJ0akmgL#:9MnUSKhjg@ZXZ<B#a5kZ5n4BAEbFYA+GP;JToc^ARq6?Y.M#rDluPO;GIg"l
%B/9_YRT,4\.qkY$$C*[X_O,J#.hR\!<eA`*\*:=j-=rd=Z?sWn,Irdu)OHWC:<EMAIL>=<ui5A2eb[%8`@(Ynpo5;]ZIj
%92-n^#lEc]ks#L"1-ULJQ_Iba>dQUaE3O+:R]6EZ3;Z.=Vik+i-1ddOGk?&7L1i3;TG&CfMbBH0Tlj#9fcd,PqUe])7k6!qm"_H"
%@uo[IJuZO?'s+sRRg`.$S2Ph0^)<MjD;)E-gNKPP`-:=EjLh<:bY-%H<5'm9,NpYB*5>_gaA7h-@eCA[;1mV>>>R,dOjIgRTS<[!
%?"$##Gtr8!^f%uUk]KmX,AF=G@]LD5LEDsipD]`[h?i=(X"P[g=U.dqE%o?"+"(XORG6K;=Hj,^Oe[HKC8Jc3B,?B/)llS?:W]+E
%eB7XopD%Mpa@6MRoarf8d94>B*6\oCODdlM<&C>EAN"5]-=L"0I4i:;^-f]ka7="&010X1;)-bYi,6lbOX:@[@VjrL0qjd:]6P]B
%P`KC<BI&qUq.:r"pEqsh<!,+M..=qK@AMCY1c#<Y6]-T7\.3MZ#,Y9k80k!n9PWLLcH6Q@ndKtnIk2V'/g+R1eM+PJ0aVNP[]RVk
%@T%F7P*)kJjI2Gl$?6g2<lmb^!FS?">A!0Cc>HHU$&1nU7Ir`&j!]ZYiIDD-dN^nP*?>NtMZSquFVW/)gc<M\#DXh%(koP$!DFF(
%CXuBq!R0huKXcBYT_5>%UtW2g$'F;Y<Ht96`lgo\+a8Ml%]M].$MM-;"aQt\Pl$4:f,M"),HO.J+CTAeB3N[Cl,)%M4[rN,&1g]]
%SMN)l+3NdL-:4#GFL@R!QYc<RaRod`aO]Ic?-;X\jr8rj=<NVaZQ7X`EC>*E<E!Djd0?/P+TH@K-Wo?(+=^[ZS;i6MEh_@R]WFqm
%K:Am))KUR>1:NC8%Ht66_/EbP01nN^TY,#CBT@m;$KAEBH#=7&8VLEuRR64qY-A[8#'pWNS,0YJa^2cqJmrZ?o/NgWoko)a\>$lC
%d3A`Pj\5L*)=FE>R'jXH5>82)aO6e?T555qXn,.;0'1*r'$4+=e2.G0-]`(BTVeZ&0NX/d0sj;+U_)$,NP&,'_A,D'Wbee0h@F4o
%W3j%"Rs$l9R;bKY=rIZ85s<WCKDD_8fu8mQN?iZ3;)CuL*<MR9Xhg*jElZK[,"on#@I1Ci91Y@^@`XHe36=]?6n)iG6\85C2J^F\
%mYDSm8:]jHJ1$6"(ht*O&!e&@:V<)@;[OaW4OE(>gMa,.f8Wac3#D\ZR=;OLN5d2?RDE-!OMGs>P?tpM`jA"po'"&ak\T1B7$A-S
%NkER]=H8_(3c9@UN[>'u[&X;iSV]rY\Z0RBY/hHrL05(sYWtY]r;]Kjc3R`K,c!>Z82KdQC*G4,i=QbZl]u%PHS'c.$dfnsS\GC5
%&ppNtI(N\n+jAO%L!fJXZ96!r7';.c'=s6[fj<d_2]4EkW$88GLuOr4H]d;\qqOX-0uSOq!*KU\:PW3q8PQ!k/d\>^nCk,3/^-Fj
%c6d]$&!()UPnD^'7OYn;:/dlI9b@dTS?D+mQ8C:t1'JMlF=Ks'"qYh(WGgJ2"?5#7VD\R'869!T/Y3i@1>S6)T95"fV'FIb>+IC2
%$BR?/Gl!"N.i'6g7XBr]MCL,7Vdr*,RYj?iR)QkCqaJlT&m4A]T+\PdN1`_n*6*`Pk`;eoU34@GjQ9EZ3d"WJ5+!%ri0fRjS#/+W
%k?+eg@7,q*8=pinQ'9`I%l5D$<S4oaG,q:HTVY_XF?j)YOo6\U9[3pOe])cEhgS_Y&l->LaE3b^THisX--LKYS:(`lE7rTYd_Y3m
%(&DI<7E4(6i!HB*Tik%/Rse"U75s$&f1XL_akGc;_[J7@0bJp"7gCAQTN$]HjHRQhPEWPRWXdS?35+D,Aq"&'XoJMCd5H+>C&sH%
%lAleS?_T_(TuBB[cqFEgE^-(]2BFSmTLC.G*qR(&;7DYO>dXOLV%AL,ciOh?6RY_uBgAqHab7K`81CaZ!JHFtUSuh/eh\fTOP.oW
%0jtGL$W;4^i^@6!(5:bN"Ve*tc%6^2VI2Q$X;`9_On4M4`0)fM.5bZ=)>@U]5hsX5CkcA`g+nkW]W<QW<i1kEm8CU5^(57aZPSr<
%"(d+.XZ]mq'-i.%.BEUhJ2&Q4ONtjCQa=agC$75_X9mPV[/e8,R!2;6J24m>Q!hP)1)WZ:.tit+i,(h:FAPka!`&::MGA.4aacNg
%lGfX0>$Y`o)31qmQoGYm.<X^0[CHtDkWP\DPki)KF;>\r0u#_(7$K4P(%.X(2'de`+_$S6=sMFe:e([k0*HP3N$6AqC5P^bMKYoE
%G5&,\3mC&`eKcLHIpH1`;)pQK;^ptKfmrOMN]FS2^&050JjHuKB,$18:.F)7M;d"7*$A0,E5*(X9!n&aQidrJ(5ac];gTEY,gLAg
%j-^aZBP-Et$k\P'OoIQTmTUj:#u%?$lSqc9lI03K12)*O=K<1;-mOtB+:dLqrPn0@E8OncZ]8($-7uI]6tYpS,ong3Z&oIFohI,6
%Sdu^K(,%);a?0Wd),O.]7m\lT.9\S'eq$p_E]uQ2X--.U)PnS!<3d?OE*j>aJ@sB.g_alU*]?k"LE$OGNbma%Fi!ifEV0N9-q\G4
%%OW+$rUN:1q$<?YdUtF#>lm$J(!nsY$#,t^AD)ss9uJ&NCB.9YA;GAUAAhpaB1.R/-pVd<EhfgK,o<9<60X1jVNA?.?m\u*!(X)I
%EQg_i)jBKVV"GNAKBP]OmXS/b,4=(^$D`PfcSck7cNZOBB&!dj->2.Y[g/5XfsF<kAa.tnNm[)$#BUW5i2srQU"Y:4A#VC$>H]ij
%PPLBK]tP,e0,_'8;@.3$.P679$f&`?dn=B]fod).^0,3U`BkEA@#\?o@":^f2R3_9k_3Cm;H^5X@*@t0K`g/^+S-iu$etu7)a'W"
%pp.h$,K@PTX6JQ]i>J36UDaE<F(oCr`U_,PfP/%<JM8mC5SQ8apamESI"f=Me3PX8:eCo#^g%DU9m5UZ_o"'0O@s*'k"Q-MbE_Io
%)Ur#)1)>PWYZOb0lR8H7/&CZ.dk'Hg=+7D'itY&(op<]TrTIlXIrt:[le@K'msf\4p$g(i:Hj,Oqr><3cYfq9J,Q40]079#\pSb4
%^;&\a5Q0U(n[0NpZ3$P^V`qioh9GPHHW9Q;?Tp7oPWqA-j$,\RHN135,CLo$'3Vtn@ds(Hf"tQ7fJl1V81+K'o^g8.?iD=cg6L>U
%\\5PbroEA8G3cN-hVUHpVpV<hY56o?BC7]C^3e'F0Yr*?rU3GPelK_QlaMB%/q,&Jq!10(*<'="5Q9Ypog![:*d-5/q,0ETn*R=Y
%fOTKE8/FM*ppY`s(QX((07Nli%l@ru!BWT!FfXVcKZ/d+1P#aGWa!Ng7/ZM:fRejZ6OE4rTO^g47m%l]R7WA)&d>,U@[2+&hIC<0
%46j7np$/$@2f-2QYZ,)$#P)#FoQit99TkVIL^tES(&E<Z_Y[BGj=Gflm^\jqG+u8Ydm:Nucq_A\VSl2Eg1]VIf@qrpXlE1"bRFa-
%(;3]q-B4@u4a2NZT%)]@.OQ^>Y=`\p?0IUG1Do<^@Gro6$Xt1B*C(;-1H],U*IUg*kfKn1nusA<9RQ&_(3USt7>d/Eq!;K2XRDT3
%Hq/E1JjQ@!Bb?#@!*Vc1oU[+per;D)]*Qb_dY(:<EMAIL>'nZIissi*_+4!SXZTDSVa*+/#5I(e[WqHDXb9+Pr+GJ1ur=
%K%3V<BRg<7m-EI`?iq^BZW/D$UGl;2H6/.'0R?8:%*@%Onjp*BjH:5dCR@WsfAo8cJJXLX&2TodVuUbO84ju6"A,^g;EQ4iTa*pP
%B]QdW0O1-M_Q7$S8h?Xb>oi^_=H,r]S?4=HbafPrFW_)#1E9[*g0%)+$[H.2N_&R]&U)10j(6"=T<5r.>T2Q.TZ+(egisIQlVuC:
%/#53T#Xq"[ZT97f-$KuE[>iic[Vhg;KH"sIC1?q;D.;IrXb,*+$Im?iop@Vq"F`\JdLorF+X)gN.qrn[77sOVfSsZP]LC?ZUK(FQ
%UTFhm.^U[ICBBr%B8aumXn/u:34r?:0V^1f=64J@M(>(+oR.H!/CuR@popeWdA8W7R4fgXTi2*)\DW\kkp&,qk@KD,#RX<!R^EY3
%HPlW^K&"(s2e1DS%(/J^Euqo-k5QcWVV6.s[?205+[E@!$j<G^,Hjq6/d'!/,ZnFbGcbNBFT[gR%r6KUWe%gQ&Yu3i513DQ@V1p6
%OV1iL2su1#U*W-R`Tc%U`=mn6G;7Ls,8!q)VCWs@.pu_G;!Sga3h2t#S'blD8qf'_*bU.AN^YR'r$ph1P'Ie[![(#:6B.6RLeS,!
%9Ek7*&'e![=Wots4P-*23E5BMn6FEg3_2Vg)c0;Tar?iH]KK".M&7b]/nRgUV9$%&2:'t,]pN`.HDcn8WGkps\Jibm8$&8S5_B*:
%;!2+@r`,W!)MDdC0Sj]oQ_N*a%5"4dSW&-8=Y%@d%'G1al&%QeF:VZD:9N-n!c$^Ab`&-XE>W7Y'6GYs#9G0Hi-i+-@Cf6l=)SH>
%Q4!\,fl0J3&iVM5)e2f99SUX)LVdKH#)qV'8`:bPLlqBsENQGHBGJGof(A09Nc02D.C-mXR>N/5irD==@X+cM$"h`_915"P@'b-*
%K4.m$K1hi::eR:][L*7:eoT(0!Ou.rK7K)!Up,t#JR.T#T5iV7`5eol7Q\<&^/VYr(#nIG-sS6)rRaP85HMaLDqSppDMu!i_]bMP
%6),WaoU)2L(!$CO^6J\'5Q0>iehkaa<7]5?ikg?Q#02?$s-#$*;&+E+e4<Ep*[-EBJpl,2GB=nu`Xi@H_+;Yea?B3@HRd1!OHI1r
%/Hj1_W@?I%n#BXhc5U\[MoH2V=<o3NEe<Q<EpfI@U'i/,0i+<*T-MGd,GGc<A*_V`]d&bu]i>nGl^b-q8`h^e(Ps%$A2t@%&,.o+
%(ap2:5DA;dGuF;Y;:j5i3pgc68Zj>Z!$sX`\8ju]NHD1H]o,/!74)t*5thP$1-pL7],r2.(l#+KWK.>0A1u*..qaAJd"rl7HigF`
%`8A,&5mp/^,611[+;7GX+Q-CS,T$qLd03iG5ZUDQ>Lq./g'Mc`5U%nD*405V&@m#761@KBFA1IH8>FlU0e$uA1??RVfLMH8N4`P4
%l<C*cliuC09ZoK2_H)heD+8GYg4j?VG9icW8)rs[jA=(?++r-&/-g1l[5>1f.jC/:&-p+RedM6p-1^[:f>C/=(,?(<%iWF:S7Rm3
%YQXd!Oq')]V%k8IN"T9$IP+ThNMKH=(?qN][;6N#4)Q,!4g/k.Lra@g//3D+\+KlPP6V-7Mlt`ukgXYu$+j-b)1@#RPL:CM,j)UX
%]R+Qm@\UZA!(uhVm5>5k4&d5ughPi3Cj`bHZ&>8iO(27S_n!T2-!j/.N'r`qRe#9sjrQ">_)3LBPC'XKZjs$eoJhJnj;#hC0#Y]M
%e3h8<K0ZkFj-Q]<nb;9a**5ERL.<9WbW/3ib+o21$;Arm=])J*8_#=,:N"J1.i^6$?*;4SLGq,%4DVP4iIuN7<#^gVH\BVtRQ=TM
%3ZNc*^%.BYa_]7Mac/6h`=Qk3!Yc@*JcZ'0_3gS#$+=^S0k3,:[W5X<TGc`&6qbn4eIk/MqfX_^V-t+WqU2^\95:l*XTW3GZE6TN
%gqdC3MtFg!:Q);BIlWsLO:_UB$HXR<1K%DW3R]8gc28A?%7gIFG^+u;L`IZp8">7!,sAb+V<mJ`!Dg/TTaV"#EIX)FA*!>+PleEQ
%M:HeMOaos<QT:\G(NM>!$nWfT8^'I`=V[Uj_f1jrYV!u&e!tNO=bIl2dX$FA7:#V?h+0IMYHD#5W(&!s2#srm'/;7<Z+&6?ed-1p
%jeS<d`l=)Hmh4i1m<1%5Tp?fPV.Xnf]e!BO)_`li""&CD^S:c98Qei5l%3<+gRSO?1lflYVCX!=:]sY:\M$]#nqEsbCF>)iS\Y_"
%fuCu>j-TsNgq/8j5dASLb9q$P\ai:`:_fp^Z(oGgr]&n6!jY^`Xjk8-+DY;gSoQ4&J4fUXA+Ie/367PC<\aZ'e&)=<(mKRYLGj95
%c."5j!,8C7@LD6_Lg>`goB(R^;:BX(C8EZ@@QKcd?^WpuJ.49#]GZ^QPaEaNTrKTqRCc>_H6nL_`4lOQ5\^FkMbn<c7<=es2-JEq
%fK'm;m.WB#(Q>M.(\G"B1L+1QKpS5`;EQ;C)tX3mW$KjAWcg%8J9PHt,4hAp.SFdb=AW9E!=djun=moKo&5[_,2k5Z+"8VElXVi)
%'sH;mFi/d,&Q#<.L`j%p!-R<t+q8s'!8L!A`(h4_,*s/i+;:!7+F#1j6-RS?\Z[sF^oV%!:hj6ni0=T=EH]dRN_p8G<71j#<Hhu6
%j5-Bm(p>d^5I)=T<2)%-gTL?=gTZ4No\n.N72N0!E^k?iNhh[j]?+N9P!/DSLsYJ*c#gZHquZM>MK"Y^LtrXOJg`ne&HE5?0L7@.
%)"K-jm=fG9&HigNfh.HcS5*U^">M6S`#u`9s&^HcJJtGdSU1bY`PQ[g.9fAc:hd09!3ka%(CO!@_FDDK//c>EKF1Tao`%jeLV09S
%VK(=HTc"(rU-UeMY1UYO9\".TOgdlaVEQW]E\3-&hZ1D.])fnRY$@h*Bje5VD[S;3X_1Lnfo>eJ/t,Ulf@?i.&lrW*dcR7XN'8?S
%9ODP3b4>niB+ri.:G/<s%,?D9)u>@%Oi2`kA5l&1*JM#,5mYXaKom)`gVsY^Nn+!A2CFsQP\$RYjl`M&do14J"*KF20tk-9m:s#!
%B^onZXesFf]0*&,\NO*sK%@L5"uB?pR\;c#@FXf1W$XUi:P8S#eooc6K`P*i#NYa!%1+c:CAWsub/%8JD)ie;85n>@ZSB;iWLDMI
%W^L9q<73fER)_`A2_IZa4Vs>2N:`$bM-*P9!t&+mFP!@BmEP_R2S%*.TQ@qN9.4/spE>92SdeEm6s!tJFl%/J>=^b4)RAO,)MoOj
%.Z\>4S:34DY&%HDbtRIK,WLqR159O)[WY#QVV$##%3&nO?W<QG3gDPh.1.2JKMend.7G:ecaA:<%Kn532[tFB:p9-c8(M($c3kN]
%O+fCuE,;`Y+C#o@Y_*E61lXZPcALaL#Z-m`#,*ilAQWnV=Z=ApC1?a33?"FL65C0qm3A/1\hbdu#BEW:>mP04oa3mUO>/%FXN,tb
%gpCeWf8WAG9Bmno_rkV,ocQ[!M6XZn\/WEb_]2)nQ$7/W$i+H>+N$8*MXt-^.)WW%c%I5mNLI-[/#*Ehrt1Zd?0m6`_!r.@VpN[j
%$U[Z@SYlY%'=g>/M0:Q9E='%:1QEd&fuQ2[&tX)&<?a-\e.O/Ui%jrkW1C/s2HT#Fo'@D(Iu&XdHP3I@@486jgdo=;/^u*Ue>cu,
%l,I$W[W*35,iQGrr$BNWB9H@OH"[teC,UK:J!G"DX>YL8>pkj7"NBqD0D8J%*uq=8C*s[UJa@uFP<G2=PIn4uR[uOn]d[.PK_?hX
%`EGeIR*mXL)WH1OaJ90\L%'b[DC$\5K#EMC?l#/o3fN?\R?BMMWk.^6]:m.#%!bl8KbR:/5df,,g5rB9TL"]sqi`e!=,B6MFqs]c
%3`m-:>`Qq2%\n=OG#CfR`pco$ck)F&%XXeqRnE`AJ[[rrg'9F-12+dY28TZW=pQ8mknPC\YfseY<lJP`VJe8ZF[H!IffYa4!GuS!
%S4mWdpkj@teT74"n&X3^R8LU`%aQ"m#$P*.U`*!7EDM:[)$m3`e@7n-<)R&%+oqBf/$%#,l"/_n%kV2<rmt4[PRJet0!:d0H1plg
%]>k`65JMShIIZC_j6JjikG=hHh\pYE\PTaqj+["nF9+%ZCYR\jOR-i3IG;Q6O.SVH]Y!shrQUOaT3pITrqXO2DtQVFa/<jJigK_`
%O.OJ6&&&CkZjhimR@F>H`4ASK_/3@+RV7h2BG(>+nl%]!>+`Mb7$W::"M?1:*nuP](W)-XKa\(M:T7?rjBMYm%b\V3I3J:`om`tj
%o:j>a7^5sET5Nhim;TY-:[UICWlQX"Imj[=&._3r6t[!Ki,.H3VC?Kn0<32WIb%!dTsHZNNcX[0PP\-KD2L"a`=%5fB!J:kU?*B\
%-ZgO1`O-#Y2FqLDC1H]lpO568s0U?(Gd5s.hE+BVA=%sG<UN%GoW/k[-'#Pn,a09lYrA1u4.k;X>3_8>F6rJc*(>X)K(4pY==c!l
%!(S]D9q&J(@B%Lra1:rRE;6fiB"Om2+qQ+LUA1DBO";hg!gl`'Oa:GQnuPPkkO<`:\bB0LppAUa]<AVuSoBkC>Q.11,UMF'::BI)
%eu$[FjE1rnGFX#0DMM"\D)Mhtddfr#A&,cdeWMUR0r60>$%$)tlE!]`kO<`:\bE"BppAO_gU/'Y^"p*7L*S`D0B?@J@;G9]ep!Uk
%M<c]6ml:(@Qk#aSgA('Pi:(V0Ng,WY'Vj4;Cb>Lb7G,lA\=:2/pP*Y^1i'r!5cA]D[[?GJa`Xb/&A1]R9^YdWF7JQh/Cqh2H_s1c
%gR_u!6DDM4.>%NWe+TTNr8V^E!rTgc&2*tWK3)!RkJ[;4Wl)rb@`"@!+.QU)`V..u<oqo/>UaVXbQbg0q"!YS'Hob*,0Y4oLHGT#
%K&dT?J7,+o%&m5i7T29qYIW:\*[Usi$FLGu9M,;ceL\L9/!r7t#V!S1IkR5"JgjCHI[:$H&i@XBGj8Hp.!Wd.'=7Ch>U.Fm3`@3o
%D(/iuqq?+LSCT7"-><q=Z!3IsJhks,;L=!LKOhWknSkZ-YZ!de3ZkM*'@+G;2&mYiA6kDN7;!iCafBt$;/%H[mOcSHk+E1c9XA#m
%Y\A=!f/cR_9jd`ZNU_WuQ9ka$J5aeVAJ.VS;1dpMe^T1#)oJFOPR\&9\B+=:RP*mtfiW*3&ATa#`!k,qq`$eJ:u%_?c_@Y*T2#/_
%_=WX^XlZ=2.5,tZ/ls1",ct.WC0;V-\,nJO\6DU>8dY2Lgmfg8d9@ttHPM4/"D-Lq+&7doU!SkNftGMEJCrA6CDs1oS4s0D-F.s:
%0blGkH)+ej:5.o>$;)kfkub@GW:"b$gg\.UQDm].0KFsq1&IHh(+2$W)Qb).>S^<Ha+p3'>C[g"okV6&acp3:[dI"c,D7nqc1G:g
%RJ8IJ!CIos[i2&&&39A[K4I=uMefbPpf7f7nmc0#>(W@cD'=[p#_fU2p1(Ro0^Fl1:U+@\Krti'b$$@K3p34_U0Qb3fU?b9e;<j&
%P;XW0.*tR5S5Yldg&8hAL=0)h)oXr]\fk:@CJ9IpCI7W_Mj(W^FqDM^0hO-F10d],XD;)-XnttL1q&=VZpBi)9kU=]bh3b;HF./%
%h?;N;.UtMb2UPMuiB1_&_:=1f/W6V@X5&j]Jh`L4'WYoUUQTRRX=+Qh3Cu'lm7(=I:*K.\\Nlu-lbBN`<C<g(<J?1=K2:5i>*PZX
%VsVUXWM+aHC:tr'/b?&TRWKF%-RZeZp.O@2\K,]7?:@&=DM`8,:q\i:ZK-lsoQ@Vp5WXcbND0i__%c$>-"*$5iNcD$^;rh#J<`)G
%7+("=?*p^o'(1Ib[A@c0=W@kgH_<\GV8]haA@X@?kS/fG'#[gEk\O0sCHUT9gY/qEZt5QGU*`=aMi@S^4;l9kS6N3qC9)`*%1`n>
%Afp"fSArd",'<n-RU"U0]*\F4>a9>&KZg?dj;o?kpB8/c\Q1X5i,QsnZJ@("EaU.mkaPU=@g!p5-51XQ/0KR&9]WJS0l0m(k[T2m
%'NTp/T(YT6kC8BV:kZ^bp[;ib<ha';X_OUTB,-9?;p4++Y/b\sm^m(n(L',[>;*EKGNN;..gEAk1Gb<iINWK5mtMh/N%(9&_]AV.
%DcDk!JM`q,j#""Wi\[+`5n';%70[1Z>*5>t35Djp1L*-+0i`jdSbg[:V3)H(6>,I70VD?(8c\GVo\n^=Ka)C_e0$A$#cSn\X$Bmr
%X'!!liglVoQ2#1MiP*Xr*Bs+8k([kN*b.2J5]t-3:oATXY]];UH;C,R85JumXDc8fZ588OARNU"/C*Cf>j$G\FhqL'A1C:MKp40T
%C0bktH$?Q/Q0@tHDY(.NQ9s_3S8Zk9c]e;q&ioSlHd>Lhak_p\O1Wu)m5ns<MiCJ-/Y$X?R8190N[jDc`Q$_7m8+MkK5RUWIar$T
%&3\5W:hCfp$t0E^opd"$$Msp>KOq>7=!iX:iL%g\-h!)63C(d@\J$UYlqVG2E[K^dIs>U<N&mL$fnf3#[3.!KrMi(m;NVr`OL[RL
%8r*I2X$7;mk*8s0a^hM5-qG!;AC$]"Q"bNrX'OE<(Cn-"U=4P#.b)!6dL8ZQ?h"oL,:;q?%UQeJP:gEH=bg1<:H>OLlMa^2Y>pec
%B%;3E[S@[4!?@1LM4mJ@\nc=$1.Y/n5"u+r[K@-0c>/DPQb]2<\/2"D&Rd[EgM+&o%SX]QWMDd?W<06VMn.*tMb"g&'!<Ird"X%2
%8"Q!M/pYeZ:AJ-I@J@DA)902#/q^k2:pnN?Z[Fp@fjiSKY<p"Y$5Rn)"cQ>LT4d(^%c`aEf=rD$6&OfF1@1D[n5FS+7%!cC+@aMa
%\M$nY&d5(NU/!^g6V"[6n5-Z5CK/YL3-IM-h]f(3h/'(cMnBQ@5ke:*=64H:mZ-s/DJ#GZ;@E<3+/b_WqJf()2@Bdk!$L^<1L#8b
%Cmp+pNc,S;*BO`$d5c`,Ss(s#e%Y=m(bQ*7T`K7q<dC6:LeuUpNIq4Y$ec9Si*(fGDHl2.NruLmqpBpckfhBSD71Nt<V,pj!DtRM
%\/[s!lr<Q\P/?"AqL9HJC3b1D'9;XLlUbB:"`MsP&Sl`,Sta$pI?BnNC#<\fiVkSjj`>3:+Cc;b.ThJ)FdSl:XDU+j'5PM*m#`fa
%O2n4[$md9":e\h[SWhMYZnEB:W2I%"HO+6GH@\cH`7%nb\);C<mY&2Y>NE,K8#a[6bSogQDH:t?Z-nTtkCU^aT?PKdC\IAJd>BP\
%VAL?"aO_I+oZUVVV-]()%`p\+O1ee.4[&$rYou/[(K4iJ8>XF6H.AL,>1Jk2MNEubXGY.!;bjMrHhq@)H>RiH;P>0L)<$d!3('S[
%YrR[tF1>/9`_EkZCel[Di_8Web(l0TOfbTWkAReJ@M@&I4o0DNfT#0Vl^YOTXBKZYD9rg?L+m^n3`].JcJkC6nJAD"P\1#'AT@?.
%,&)Q1:G#?%iOqm$3fPr%A!*-mhY.gp?[HdTS's?X>\n7:N<b@Q@TN>Grosq`!p%U?S;li#K_&6>mRIpC"53S1iF9\YI)aj``mWS[
%B.UDZ5mMZ0N()C,^;FTR/^Hk#4.gL6$rq0@N5mdnM/`=<@m\TV<hHX%>b1Eg%&\)WCk[V6lPpob9d\@&#/eaciL$W>&9Ad,iSN.G
%>i!E=K'ZF*cIGXfW@lnH)c'nu\W'\dE#YQSb03FY/Bq_ZL%D(Nd./H(_S!m"UsQ'h&R8ujR_ibJl)2j>E_9.fh;O>%5p?_!V)E)5
%L=98",d^`pbQVb3aCLG06(nGGdb"WnVBs)5STLQn0WurT0/Ok2T5ccd'^8%Fela?sJI.Z#<SHD?[44_PI@;(3p$L`pc=;UYkqgUQ
%Bi>kpa;1E<.N^k7bXcCSo5MP2r=@r8G+3!NLYYcs_6+0>?aa4:N>(:11X78sJDADmKs;':kqgVD>bWp:h4fk^MT'5`/(Ad"O>St9
%0ie>hc:(=[aSoVdVQj70(`#JDSFhI'79Ae`EuGjXR6im'`iF'$)GbVt:?,fdF'7ej1bMaBFgL:),>te;AR&>pRB8nkNo5%ug3\f@
%Tge:bR8gg5c?#l5E@NVJj(u47SDn[mb;>*9j+;]R0MJ[TkU3B.#+P:4hko,JA..5h2DqlcnhL[3V@p,+=,+-lao_upR^FBm@>7QF
%(m6<`AeBtqH8^I\66sV.Phusr.-4:H3:;RM+%\5a<L_G^1+$;]iN7WMHk*!&Pq>JE<G^gNBWPW%Mu7gLNHW1H[:.oeSREY$b(PDK
%@;8AoG-*^^-pT@"-D*Gr.;eCrX<!^\j,@4t-72f&NoTKI<g+0B*Bo+8:3IhfgIKZu;-:t<[S2lb5+J)dm']"+0u?Q?.)NqBU"oI.
%S*M8eY$@l_,7a[LSp33Mg^7-W-:imbAL&Ku>$mRdY,+![mtRL?U3uerN>/IsrllnIq_o>e0Pq#l9j:;a^0)QEmA57O#`F2]0Dk1I
%+=l]>#tOH-_IlW@cHI'YG.tOUc%,YP?NCIqSf$.qc+tj3nY@Bb>_Zt?LfOan1+kqVq_lh0.@Y;7*&ZiI#o?j6p!WDMG.';p@Pu[Q
%l4B:C%)m+`YJOr"Z=5E/eQTDm3"s3>c?,ORWZAYgFO]L7;\bd3B\IpkRF6krZ+)b,UV:jh9n`&(o#&GW'CP#FPV3\Q;-<]EY??,]
%l_BQnEPIn+>T\%?.9PkGl"Ue+@I;@9dWK\P0$A823((bfLRjJ>pS,@@@TI0L37h*^hT%Xq6R,V_kdjhF&]nHkSQs!Rol9if:KMFG
%@a;m6c)gYJl=;-"4#oFoEa\@t8pS">9UWNpq]ed(URX@51*\Z#BB'u*[e_s"iSKJOjjR2qa,.G'30j:0jN,ZC^=_`VB"D:@Ls1uE
%-mNea[M,;K1=,=q&\O'j#Zb23/OLK1J\[X1H`pG#3;QVSWaXOJG#54Qj_h?Z_4=^S=+LH4;Q,HkA=V)V+L[nJ0QZc]FK[U7'NX8H
%0S&+\FK7E<s!`ZRC:!eLeTsqJ]/$[Qk+-raP^$#6pD%AWjIO@[3A+m[?UE`2m]O)LWV51+%Ls&mV]WU`W;P>lPQnn?'J;=F-FkZT
%DYrKu3#!!>`lIBlVPY8;pO9MaG'GiS/BsPO=3O+G#^7eJOjED\@<Wn8qZpVH6D_!BEklueq:qST\Sbn[G;;V5QuK;YKLJ!bLZS]4
%pH$,1KlTp0RAi+5TLdIt46P09]hP/`F.hjff/'0>p#gA*9\KCb)Y0S/kBH,VN-VGTRALo1G6ai3]&r\8N2bLJo6cO_F#op5dL)O$
%q#[/(L:An4&Z`EkS\k"q\",=?(9Ns*5ZK1@'.k9PX!p/^,#.lPGW-hM6oH(BMVl8FX<);a9V2flXmY#Df9k#2S44HhNea$t<]-S]
%fV.7b[FgjB+G@G]<#Za!H,l&I;hV$Q(.5IL][O(m.DFN!$5^Q_JdIuNA%^-gE0T:%LrY:?/WHUZTWQC!%D0t:Ip-7t-FIR,RML-c
%>!G]=/7B5?r?`JB44DNE)>'Gh.'n%-$gBBf\C38pQYLSO221;f"qsRNm'827Tk!PE@:pT%nhHI=q.KE-?Kr*VEJYCilWDb;0rEEB
%jV=Pp5?Y^S[$ld3>F$#/'XFg]q)U26+1@KPA4X9B%BH-7P/f\MlrCRaT1*(&Lt$.QDOJI79i&3!iML?JppPR*q/ARhFu\rWY9\NU
%2gHnA)CNQq+^=1k3A!2UdBqG6UoPPlQrM?fL\8S!l:X5/lu8I^[%*jPiH"#IjE<^'"2(]_H"#EW_m*Q>jL*SOMLbt5D5)slmX(40
%Y0jpT;CqZ[?\nR6<Xl!h8cPG9'a"`E\lAd'pg+7WoH0UfSeDS%__Xb_+4dtVCohrf>,P:CC[cf47AbJrbgIo-f':AANe[=2k5J,a
%Za*=$E#>aS)Lq7Z$36Mj,MB"K;A<9WCeJ&.ADXH/qJso]lbgmTb/H^k9c<JFYFCGm?aloThU7Kr+Q9`MX7.Bp@*1u!`[l7hgDFSi
%5/tK2mdi&k_"D^bG@P=DD^/js3n["=0l5E.ghf&Z]$Dlh"14,0>,+bdU%^Im;]-'Tc*\4-FY/?6BFiE[5eV!W;ONF+,0VRADiqL@
%*h`4rrC'"h$\qr?-J$gV6/Y)emgPVK98DnGl*gO2?5_.OEdB7fZ6bSo=s<KTT2eH4-[0n_`nBfAM2I;d_uNsm_PQ"1ni8Qb_L*A`
%&S$/CFNH*#lSoFHh`<"1U2`N0XPDj39&RV*e3nJ4Bc,^Q[DeY":;jiYp3NZo3l(K4%^_'4)Aqhr=umlB?Otu$#^:^0aQHT4l=D.1
%YuP5&>B+<?&"t$k`GF3_I68+MLT.7j[$*Ke8agmn:<pV^lq$F+I4^2_$rqJoO\jkISpD3.LLcsD45nV'48D!h7]BlKef(XM[T7S\
%EjWItlZ@qM+kof:[<)%_.af^mgSTKVcH,alm/GpLZ#@LRkJSM(,Y+V3dDSVGJ:M]Q:+N;)Oq7B"rOk"^`>omhgp*":*WO4[;gc8(
%.Gq41Y8d<RdTPd<88"T6+e=1uKleC4(=Efk(=F)aRgDlaD^Q(-;;G17RoSbgqA.KskZ^SjTPl!ELVAIaKYE.^KY@V0`iZ0oKt`7p
%04gg06953s$gBeG/Yk2L[ucWs\"NWS.J,3f1XNUmc9U8R3Y-Yn5?r"ANmP8.m+J;`[uem#ha0@+I1,:%oIZjpd'o+^5i4mh%t+iM
%%.l-I1XNUmc9pL_%kI9(UbgB,ONp5N6953s$gA)l/]@8-[h+VI[h+VI[h-kQha0F-I1,QRoIZRGVGNlmnI5%A`&#cLYU<+!kZ`jU
%TPl!FJ\Hh_*r6Z3)<b:\B:s/dSqXn4GR"![iYgraM!(IMqhGp_-H.`WhdX2-nLX;a`&#cL&<G2I&C3`$69G?u$h4Yt/Y)Bb..f+0
%1XJ)&nPs3U&YX+dSqk%6GR"![iYgraM!(IM+eF8!Kl@7m(;b@\g\tUb*JAYAqKC9t"8Wem)<b:\B:s/dSqk%6FG.A^F9_RWdWt%\
%88"T6+e=1uRICpe[QZgHD]t[&^4gYMqA.KskZ^SjTPp\]S:eOQ\O0GjZ-&G=L#*U%d404Cq[E@Y9Z;Wa7YI[?)3LJrb/i):JR(o3
%>%L*Hc71(0K2i*hr7!3+_GdLoa\T#oqa/d^Z[\NLAI`i]qoRQR@;&f5]F)tgAX:&890HBg:'PB6A_,6K<gior>HZ(megb1hgOg?l
%n&-[XrToJCjs[E#4#g>R7!PrYM"$p77cXfT9Lrft1*ShnPnEi..ML5JX%Z(IXYS7I:TZBS^o(3N'4)]jmAs=M.+a).S[$=jFN,e_
%.\EugKQP3`K:Y'1b+*S.:4BFiXJ7MO1+,FJX>9*$&$*o8,DK4<KR9cDaeU\m9Shl615Ep#.W^j+?4SnJh(5,%arD<jX3A:6eBkMi
%[RGGd.Y@a^[E;n@:9?9p4Zp-plmKbgg!M9jR$qcT3LPN;n8OIj]1*dB12AIr9a"H[>%Q'n;le,'1O$]W<gg@RQVrlFR?MVpX*_4r
%XOA=FWlA2Bb[F8Gou6>,BB86W1+(j\AK2?\M4qc/e\MRBKP4F2fp!NQ,7("mn,<SiB$Y4_nN/'Q^LJI/479BP-i\K!d_8kl?5pu-
%nrJHpF[cdt3)W1_jr#n,=-]W@q_!P*Mf>rH'\g;i4ta04.\l6+L8Wi?R&Fs>5S3D`@7Bnb0W[(L/g+2sWV&"R(JP2,igR7^_`G+;
%0%QFjT(4ETW@X&n;#1eHG1N($9,>'@B11r82b[FJ(rF#\iZ^>IN+SHOE87^`1eqRCjFg]W,iOJ\B1?JLADnA,qJ>P&NAE?(+Wm<+
%,f.Q?j35lCQo&OE@\"r(8'Wg6'H5aT/-Aj\R9d_U3)_Bj"%bo$5cWFAKP$`'n(?n446'!h1'WlM3`$,mFG@K*>%2.YC'b794agFH
%i:&cj\Z27P;Qn%t;iNpS]q[KEC.obu\Rq>!6GKkOXR]ehWCFh2$10WU2,mjZ$8&]Sb&f_,&)3$e`@3Wj9=9j&mn]#[R^IKj\0t.W
%aJR3RNscOP[M])U'WpaVc4o<@7D5G?KhAGR#T)MpZ-/gtcA`j;OSqk+@:"]1EIuD,D)H<Eb7d">1P2IEg>ZO[O_)tp,$`\uc\I8j
%U`dXFF[coLFUZ4jP0k`<Vf>0[$<8#biu-NZS6=sk3$X.231+Q[=I*rc_ePB310b5uZ4>>2rN\:Z],0F8Qmn=]=Ag`?@AY`L+,/0=
%8608`KsFpD72mrI1<'%*]UPdaOW)u-@kn;CB(lh6<-6h']1b)Gk"-Xcg-qK>__+lUUCk@OQW]$C?"V6sYfhu1;6nEj/u';ooGhjD
%,>tgAA_:RoWP]o:;p2a(X`bRRDq`pe_h--J\e19S_C2`8N`3D<-oI"Dr2sO$qGD'&F49$G$:h^%2@u>O-]i$GgA#UgR<CVL@AOE6
%ejK)P0RgTD?+>Y`3uSh3Nc%j2b5I>L[HFn(kFIOdX?N028Z?k$$Rr`&p`81jP5+oQ\lC'HpH,t:H#tZ4,`?UX"gH&qj(c^--lU=c
%1f+!I`mrN(nC:L$BQ:7$C8"n_E#a@"\QWi6mho1Waa<r:,;Y9!IOA3$Y29h1(>H86>>B0P4cnh!]G5kR@O+ieJ6sa,(YRY30]WW'
%.QO^1Nc>dOFt)3DXqj5/R%[6O-gq=>9tNDsDfiOk%klm$Ftt_LC!RVe^S>`>UDrs+BakjM3uU_N@IuXuasl,@VAK%n:$]LHq\:uj
%&OG9GTVhZI'Gf3*>;kAQ8KsVTq\./CZ5"DB::I(7#>H]ZWB]KM%7G@8*Udb,2LY.<)bc6)cb#'VU6JH2e6)(a?)U_dl`5!p^K;kc
%%p'_LV-)h7pSZ69;mBOVX<)/lUL$s<Ps;*OD*pD@pR@>-q#aK+%VNaZ':V@&])I_8eTRull;a3oF;dlWrg<;5J3+$\PB4m5RB58*
%rLDIF`kqj,bZhVb9k>r=\aIrpEO'i44#G-QXPLa`\GJ&(\f<).>16NDlmme5AZrKMbe3[#A9nUWOQf1tFCI,oXnu[<p=."B.X>c]
%mH,';m:/7a]+R0/RjFg48([@Slm^0?IQ0R7Ec.MjWu^"(B\IfO&(aYl:RgmrO]O)Fk]/f25mf,UH4fRB_,EN`dltCmZb0*b4rjJu
%2/`i<"AQMbbb?l_cE9k,a_pM2)Ejp(<VCsN&7N<GVSOflA(L)r+_H[mh()QQXkRPuR]j`'ZEKjuGXou">](aDG:jeu@@^3A3!\''
%-SFR:a`cN$bqluWBlQSrbaZW"7CMg5:&3e>S#%ao\QOX:"1Hd)\BCTMC92bI1_T_MI:TkY:,\9oc.d"H)WSEC:)EutGhRN-Hu&PR
%+c.(qpJ"tJHQ?g_%t/2Y\gUm6BR)Zcc)78J9oVms7eWjS)^j=1Rm82s:A0fM^A#sQIi_[lSRL7,RQ-Gq.3;M;LrK[-dtr\tO:f"T
%FB0cF*UR;.JkTD/iWWJ<4"nZ!DhE'qa]%r#KgbXR_p(NS54#Hr8D^u9)j9`"*c=-IhtGoe54!tNnmD[6`=8Q+eEb*fa5)3Zo15V2
%I_,+ZIfm8kpd#\Sc4@5%k>H6OY"4HqUlE9'k6G^LgkKX8US/OU)'R<$Zqd[4GggY1$2Ft,k3ARsVc.HR'8)^5nnR=[V';@`D-uD+
%p:8ZGjYRJ%fb^*NB]!T`E7`'eHu>$<;i-WPUY'k^?6"'VgkXp[Qq^dsO4%j99MGOC\[,45!tc(7C$h:!O2H:p_>3%N=PRtLlPY[\
%qaW>V`]>\q_PPS%EpMY[e7Bn;%JI[t-Ij*p71G],!P.8%PB-&XrIgVcpZe8Qjnh2aIr=_FF!5AC)S%)F.,[Fe=pbnqefKqBd`5(i
%^`T#Z8:5i"n!\;ZC3-j\S(DMn+cjY4HlXO.`TqWn]pe6"2BXk8rU%:HVcRGU+f9Xt+#A[t?S?:foKhHK\B0Ing$[]$M@R^oH^g@p
%,mrhKD%Z2q$:DO2+Bm?*Q[9\D#R^;Od8G,0(P8"/Xk4q!h5@mkVoC4kc7**eeU9UqZDe^s[ip-)X0tLF,<H(j1ZZ8MX6pD@+0p=X
%9_%Jj=m"/o+;d-q5&F5=%V$s8%>RdciPZGU^6.<DR6&;jHk^MJgmrte1lLHm:5"2?[F-<M&><rbg!\]e"#i;a$+K)0LY9`8WS*&C
%#DJW+QId14%#e`?&5t!9A4mH&Gfc[Gbu05<'oZ18'@,?q:[>o>D<B[bKqPdFFlOMZ8H7Z(a[H]?M"5hRo45[Y$fC\R\AsG#q#W%@
%57ftAi+IVD*=U8O<=@k<foK]Pf^jRA<=LhE[$,cNh1"Y5]af=b-A7NUifiPh`AA[u7-,NO4iVM?8[1LGG1)mk'F\[P$?()ebg933
%k.oAXLG#^j.,=Lq,+mpFcAd\>VsC(<fsM&`es]A]N;,EQKNXfe^)a-7883?)4Z.oOZj\6\=B@jFLr@EXOdh:%j]H+d'oZ]aY+kk0
%ae)>QE>_*(LO&F\iA`\bB$^EXk0^`_W,$M%aja"=8j?!Dn75pA!^JGf=`MM/K:*1'mbFoSB2<;?2r1mZOEQFX2sLSl%"p,X1&9j9
%Y%)b0ZkN#tm*>du8oMRl:JYl,#!9[fA.@$EBs86p`Kd$$B4UmMG=0(k]5+9cXNBnK:jk<<e$9uFXqu$tm18^)E2J7'+CNW((AWPE
%XbS[i\'qg_6('*RG:MrpqO2.hW13^9n@P]Xis&YRZ[m20r!:?WWM<L0^:S2r;_1#/XYZ4C$Zp9+`@@1s#Q\q]:A-!Lc>?1g9g,uN
%J'mluOH$8:XOn5qOh:n.:7]h<T5<tD?2!O<p3q><G\)b\]_LMU](ii"cJi7%gHqXSN)(MZ&3ebMgBs91FITILLAAR\\i(EJkN='W
%U5u##:#F(9@urO^H`ZZ#;GZRU)(oNDWA#I?]&;n&?0%,8?0\+9oiN#SZ2XLEO^:+Ii$;N&HH30&h+9$go^[Q/!].oMDA$]@%h>3N
%s1HcAP,^nGX]RL5?h:VkB`-BUPMNG8Zu,_^EHUI+gT^^cebadMc_S(^2FIaT5)ms#l.OeDp;7"HpsKWHPZDlErbQb0l^q?\c/)md
%b11M12*[.>^o'60)=uI.QG&3l$Z1?^:O\kdDKmJn/EdQ>qjHsmmeh#$/Gk9@kqBO#bir[2Ha6joqb[WI*JLVdr7Y?uP#?WBU7]H,
%Wkn)bA.<7@PFMEYS!,_@rNH$"4&A/$=ge/GZ]8VOS6XppDcsFBCTFqh8K7k+6V_qp9@l2G:WmVZ6HAofYb)4ZK\\c=DMV?D]UMF<
%Wcu4\CH(b3QjN)^q^fA6dpY1rZt@LbrTi1>^7Q:HI(/sU0[%2uDDg_RA@LFhUL:^aB_ohg,puQ,2(TXk,c)MVd7ma4)#(c7EIXF[
%:W\<&/IA'B?/no?,l)DZDSii/Q[[_^Y.LUK^8//BlR@2/=D657=nh`c[m$4C&l0RlLTZ9J*DXfF[sd.VI;P"9Hg=U>lsEEFk?Z\*
%+sBcoGC#u"G$UHdGE!Lm:Z[e'nnH^hC7Xbh)>?"#goK&ur]A?\Dk[W2m@SGE=7]k]f$!SY2r#3AadfWr/7)qrm+qQL5L/lnWU7pe
%Q%[J!VlM'b[";jio3L\;?YVEinnha^g9+FK3,S#M*on9l\ZbC[-]agYLJ$@\ZsRF>(=h%!jMQA1PB:/04mb:&`[k=d))G.%0!E"s
%L\[sX?:sdd?2(@AcL5E$f9gQ]?;LOg0RLQk/pk$VcAgVjg[;Y/O8\Y\roS-KoBq\-+$R3FP4r8=IsLSY,4cW>^A%9amDW9oqO*Bd
%>s:AUhr"D-mdd^=l^rWGoD&1XgD>>eO6):DIs1RjHYT\^YJ,C-X\\fRP<]&qZ`!O.lSH6&L`YR,C@;FpWgl$:IXHQhqt&!dqjTgl
%Zb)Cs-au$9f:Q;RhVR&dm\fe<O8nJ@9.f`WjSs\u2uVa<[kE4/J$omBceNGfI/!E"0;!XcoG3$0T7?HjVfs6=hqUA276Gmlo(TT4
%go]K3i,=)7%,WpkpX/@Eg].<EZi@PLHMG>6]_/&?:HuJ*GjkWikFZ,Zr:9:HGLuR*^V#&DIK0#5qso?r^41ETHh4lHb@dI)>j8#u
%g;`+-MfA4QiLPm6ro%npqtTC0(QYANo`t:CIrOOIQLU0nbD/=ZK8:Usr:GoZa89LP`gL2`(]E@gXL?+2QiG2$s'K#Z^3n0\lll9%
%2U?ku[nW::GJAS+o?5nG?TbppjQca,ZYb<Kdk'PWDgla(o`"FTiff/`-hb&F`Vm\*"$cV>qWb&]MEboUi2M\3f$JV_.W*XQ5EW\B
%UjU]+q-;rkZCZ6-aL28$n]uE'QVXb+?c7.Am(Z]t[<F:.PBh!<&:J]m^J;2&;)t`j+5D<dOkd%.eE&?cfW4H#Zh$X*FI4l8XC#64
%d=-?!h`VR)V<>>$5$Etcr5fg*K"lKH?a,pF`+A>BadY]tk8aN<-hg?ud9"h*DRY"COT1W)R`;u<CO:7fD1(.<CJA18qc[=OSsNB+
%n'8Lr49#!Nh`SMUhX'd`7jVM1p:5ZI0<_:%^?JbW>+_FY?M4A7i\!.&D66ET?)tC)]5;onjPe;:lg:;5mQ]MjrSnn^M-fu.cYd[9
%G]IkLq/CcPYLg7m:]IqqP4/BZgL3P(HM9p2Z;lm^hgPOhP@r==5In>\2C9-BSK'S4(OUnq[s:+"1OjWJB:h:k?N'lLr9V,?k;QK.
%X.J02ro3)!7F,n0iPiDN?/@fYkaaA*e^J_koh]/E=FU8#qB2Ana54A>H+*Aq7s'+QgA:QG:SSGm`oK=C3U/i`heDt3EKleX=fL0=
%]`$T'TCk.4*mG4?@/H[7J(M'@l;onT[s18*m;@E!s2i1We),4Cf:0T7ek[AOQ:'p,G,G8pI_3+7GPfoKSM&dqi9'jAgWM8G&UO5a
%5M`4!a4J)^MV.mgcZRmPlb't,LPrXO/u6Rk=+[Q5]AMH8$F(EGbN8[3Xs*QmgNOapD#K4<j=ba0OWSsuI>c#:=&4n7I;o/OqX3nF
%?f(0WKH9+@qDg2sn$:hm"^EHV`g+tl!.1%YNbQ*JDmr?raCieoJA&JH&eYBM81V(0K%Id4ooJ0NH9bj8qFgYV^ULuWj2*87j5T5<
%s7YpLipdPfY<S=Go_.ao12pHAs22f6bMQK<on(od]?2%04`tF"^)]Fe6.abmpLXBB"_Q9'IefP;U\AZ8'*$20qoXck/kVK5+c+,o
%IHVD+Zg5H(k@Q.rY5DA16s%f<2WZSuFt]:sh.N-C0d(3G_'"?dK'J[^cU&H!9df20AU82(A*UanH$4Y<?g1VgbMW<mq=TB=^7QLV
%=+2UUIFXo-A</:r*[uGJdX;g6U[4=I"`D*Fb2t2RY-R#(IohbZgL(DC;G+dn]Pb#^4g[LVf72R=4@:J[Eg!cQgT#SrgZK*/-^-['
%]m9/b/?SB%W:0'b(&hB,Pcf==#JWKC?GVIC>HDYNhnk6AMY9tO<SsbR;S\f'^O4ES3nj])!,PTt^!C4(]Ob-5D7r*H\_`')'\;DD
%f1Y$U>.jSkq=T+'m5f5d`T@DBg&M!JqT")q^AI$Pa+)p%;88_>?XmEp0(n)akN>BXD_M!TUXJnVcB8%QIIp.^eaL^qT,hA?[b7Ch
%j\=YjFN^Pa#A1I9+XG!9[jWCN\.rneh5sM4\-6us2/Q8Z@^a$@a1e#!q3J4%YAk_%^\RDlY-5R$b4&%<EO:(@-mn^`3P6(In(6H&
%S(G,jg\_HAh:K:aINr(RCL4[2V"Lp.ea\gnX8C)PkJ)Fks5\Ec)de$T<B)Ng?+l`d^/cZ.G'A"RAIe$Kq;j9?UZ(rP;ZF>[j.AI4
%_tCeBk+d^pHh%'`LWbJ=hgE)-#$Y=*4h.ofn^AjEs2g9YYef-3:YpTtlpfq@@R&J=SmPd[at'X`YI9)&I^Aq>-^RmF:[a>Zqo;Pg
%J's3]T./qFc^(=Xq;D*npX`TZW)\:iF8+)2)lqFZNW4!"j3`+!pYjBQogu&DfU/^PnG-G?!r$!Q`c6RBGlR%,h=^_fH1S@tf<CU6
%?^nZO+4mE8<WJV7HN2:`T<>rW=J`&t9M9*-8*+HC9)2rr5`IG34T1eKgKOL6X/=/9kLTCjf([nXaYrb4kEh7%rpb=enN4G<^;K)q
%SMg,EH..,V;>.j)dq9ITahR70(?V=`(TEFXK$/NbcRuEbgDpOMj3.>Wmo7*KDXSX9K#L5DIsV-eHc[A4miV6Ah6Y9DWbfYGq<[g5
%qd%$>cgU]#F*IM"8B:GK#5n;ImJT]Ga8ZT-k?m%-J,Y/;m[d!k^>G["hkIlGnP2WY`'jJt%*?DEf7!D7MA?hLh8T;^`-fV<BYCqL
%*S\e&qW(/-0;!*RIe*t0oPZq)HT:$dhPTbt[]Ma-q"Y`8%l`:fheDQXjFn*%W](GI^#7QqcJU1,KU:TdUGMr-8,(Vbpu)/JjS&O6
%\jBoSV]K2/>^g.JJ9Tn_Z!r4Tann="=j)DrI>leWmJki<o0:F555k+ds7uZ=&*$mbn]1<0IfJm<5PI\Jj+"f7s8>11T.'B9GC?XO
%H];Q!<@9LPgKJYYHfA5!YjXSD5VCPp"3r+RX8M/X-265&Ec6@RQX'Q>aaJThh5Ag!:[=n8f59g')F(>aD?&JlhPM\"J]qJ;%r?_0
%\,BP1fcJ:r5Eppq\;7%Xh/CEqp\d]IZVj$bcE>[hD626**OAr@lYG'E?^Uh0P@&46qqp#[dO-tLT&%]HdL-L#++Mbon-d'GB=l"7
%nbpkDIm/7&ZTbKtp7nq\5t*`2>df,m5H<M"YZYiCY2p+24G#YDI.qjlmJ-9-+s1+I?9^PWZY^e4p.@Za:VZ\@hgBN7UJ<E3CWYJs
%d5`M;Q4"8o`>='dKbIQpjk=m"7/Z"lW>BKds24+TIf@7Pc[V5[qkkMoVm$$3*2[A_eb:*hmB-E[q9?hMXO]>:j(rLN]D@T:?FKUt
%duT"B/q1D1oA1!JD;T=VHg8;6[Yse3?>*Gl1OQ)Z-hNVW+$$]a59p>0P4"J+a0#0cqL-u^^TeUNX`;/egT?uL;T4En=s9cF@aaE5
%G%I4n@qEW@6bub_#%?HIqJ]Be1O:MU_>FXs0t$bg?'i6<p^&X@X\@]trTOjketMR2E9lK=qbFo42l<gEdnq)?nOWI1!#hJNA\^a!
%&\rP&D#_<n&fqqOSR%\h9tC"IR)A?G].ZYNEYiWZm>i#F>I00T#?n#_b`1C]M"kIqVo-DTOSEKtQ@\a,L:Zh8o]3&-%AIe)d2\7u
%CKi$Q)AO+L\W(l:Jc7ZJ/Hs@SI>`*FkdRc2S*J*,m_0T&)O$t$699=YA3_T/,Um&2'$DfPmVO[t?#G([ff55*eb6ZuL2bSNh,eZX
%qnQn^8e7&JE1>Y:JIN(@3`RMqe8jl,I1&fa)oL/FfBK`G36[(_MA&@:,WM-Lbg7OB@3o[tOZ67fm9`*V@?tp-qj6]bR8)n^]HIC=
%,k8.'<2Gb5&<"X_W4K"iXf+//cF/>]P>Y2F[/\G.;j'r/#IA1H7]>C;cpc-P$f!7Bnj7FN'0]Et5.n$ihet_>^23fY?hMfno=Pp<
%_I"'Ppu/[GIuRBNVghClg'`<(8#4`3R)Mu[nF;$>Fh_N.V0Dj#'GFBos!<$Z(S;jIrhoKL:Tc9SrC/V50r-uLaa7jXHXBlOrPq^+
%>i&caCAYuhcS+a:s8:m*:%&;>`M[XhI3>lC2gn,sF\k8HHM,7@laqo_2SC+>+=86,a%f>D_/QSo]&iVF&$LK[n1U-;?PmY#]>+@@
%LVJpd_tbBmpOb[n\\6`=G]`cNE.@K4(Cu-_jP:p6hd>YihbGo0kUju!=.eC*5/+eGe#+aIa1dl>H^Gd#DuJR;c[YPAbPUK&YXG7f
%ci.A;=a]c;"V1r"ejrOgm)d$>YLHQr:[bGnVO)1m?N'm"nOn"g4o-WUmalC8B/b$q[gN#!C#h0b5.Q.;"^:L1%:('*Q':3sGDcFc
%:3jCQ\.L3bQWb7[1X^dGM=?jM$@i)2QerS>g$cq6QZc\`nbhA1POH<\pKFKYh`nji,CuAhgH'`XRJLd%S#o$7[JKgQ>0>0@qoA0@
%r-e`ko&lairg+N"fHi1])[PpL2Z&OoO!S-2-,s5_r(cV8.8#b5fD]UX5/4i-h<-H!`Jd=%'+GA"[Ujr7\]+CcC!WP2JT3>Zd[)W;
%UbBJ-9cB%o(UiM5V[o8C+a;FChVW-nh3Vm5nOMIS9`eP[-VqLe^Ueki9HLBR\,<FM9Dmui57Qj):[ao\^1b^Cp#g73DQghGl2Ah.
%+9,*;!-dSk>lu0$msfQ[nGd_,SP?-E9V^LSCB"A_B,kk7gR&C:%PpKT-A.-ocEG!G%G8Ueo=T3a4n%452.c-?3k&9Ef`F/KXSWtm
%_N3>sD"Ra--,G6as8Bl"f'ltE\@dF^D#HV<HLUqAqtN?Ac8S7-^Cj%$/=G_<s-I]']_JoM6'%e5%<&]br[YKH^ZBL1lmqk)(,b%M
%-/<@u_XDd0D1hkWnb`4Bl2U.Hq_ln&=-+\GO7W&BQa.pq^PX^!O8lDV@kA]iFcY)QQl,OH9T3\5?7br8=6cGY@JI5@_./5c\e@p;
%BkteJS*<M78qrMA4`)j4H68Y04cJIPr]Zj.X81`RhHE#m0/N7dq`J)&4P'.GIZJe*?c^ajeW%%ZYJq'=0lX%cCoE-p&5Fktkq%7?
%,RNjJpoHd:+,;W,(H@$*aFG/dS)0X,!Y7/.P;k[*i$Bq'e+'o_g&QY<+8BIX>IY#T36^!PQ1j#b/VZV(r<N^KcGoq_f0_ArP^:`e
%@=F;>E7o@>GGJNYZh>#X\WKBTq/cbQb?*e80=gnX/2`a.Q&\W+fF<h:]l]A0In>B`oZtpH(se9&E5%b_0=_-R&I`[Hq-qinEV#NA
%W+oeI[gUW/ITd[?&s1?%]J]!>7*dssIY7&:D1HdD)NG$<EMAIL>'V4pF"*kb]l9o>k/('si-7Q62'].KemI\h\=.L10X
%,&h1n4reS>US2#?#R9[0L>,_iR_70(.c3Fk^abKGnb!p<e4Ld>X,=8IXUZ8k3qZL:9ga:ch\p5br,B<CnOGs";!N<?&2%:k^'[']
%hmg]YDuV]aib/YCHLh\h<1s(R11\rinl8K'M7aa"&!9Qib\TbL'+fAg:q@L/DW<GK,R:2p_S]164q+*3?Lc%hPl>Q_lHSYJ/c\$`
%b'$=cG?m<L$Ki3[*uNH/2<UI6r:f/s_"q[BO$;X]4eLX,h?01U(:IVgWq[Ck([f2l3fuAJ`Sf,g#3HQbr4gd@qTkVX&#&YDXmeuf
%qlNouLD]lO<p5%Q(jVHTI+RXkcillYr@M:,Ks@6o;k"Bn2YDg92a3nT#]"Lb&\a@fqaMS?-(h78<P)I4r[otcKeU8YGgV+\9`8*]
%r')LXs.I/o,'dr"_J'Ar^l1Zs_Fl[m"o9+3$EHF^%7H\L4:B9q=s8A!JWX1V4l/N&A@P_.'%0h4<L+SeDjs_8i^hcFdP;Z@jipY-
%4cLetD3P<5&@Zq1UsJGTn'NE3gZ-%gBsg#"S@"$X56;G#T$%)A787I4`N?Lr"UKH&Yk-KP,du:'p`1RqhJ826e#,;V>N9Y%]tpV.
%YG)+gdI8hBne$YC!#Y>3MJ!-fjT=5r0la]F_!cVO>H6SuI\hXC@K>H(.@tR]"2/4D$E)3P?apD3)(*s'<nn\X"u&gdFb%&&O&qWV
%DY=l1Ug1C*S#WhfMap#:2";=Ql@T0-][1_RcG7#'7_"$"5Hho*#nZbCIS_%L@*5dDD^<44bRH0b)<U[f?@BA0a^pX5e`n>/i$L-^
%"](c72kG/@3,]GCG=;-[6\&:]-P7[pL:m/FFR_VZ\Vdj!ODZB+1C*E.[(_]&-iQ,@POg`KE,FqGeoNRI'`:MO=GbK^aKfHVoanXu
%=S`:#25EjJPR'[*h1c7Xq:+aMb+(PDM9\DV-bPVep+e.^s-!([\C'sn+'Nk@S\E'LRHSnpP2I_@$t.p7DFVraI/5`G3-Q4]E&5g)
%?ns_G4PYuMRKQUMI4YSm.@pXh`U$KhO.6:K"6<$m?SE%hg5#)U4/a7gQ*EE]5.jbt6uf(Y%p6S?J$-sV^L)rqIL:uP\BN]AlYTmW
%"Vhjc3#E3uf*EclU/ZOJV)qXQ6!Lu\"FFAOA=>7u,1Z/Z_qC,lXaD>[10(&++<%qa2YkIM=ZRhlmF5B2^-\5C"-&qTW,cfF=s[c*
%CY!<hD\U*Q&+aRYem#T2Yr[+'2,oe%DbM(_VkSEr-8dAS@<r<3Cd#l/[M/d/hD"Pl3F:a(:FjN`[aXot4+tFWSkQf^>.g.&^OT\/
%rU:?e(Ns,/Il^-9d$:<e5)$L\FRZH;Ocl6JLY>9tjbs'5U<E_p5?mSus%nZ"JQ<T#T<`P-%[A6^Hb+_r_B=%#nGc-Ha<[GGE*"?3
%`l%e,#OJF=%c0:s]"6?RR58h6mA5SCi3`Vf?mL'=&HpsB\2E/;jI]Z(:O6%+KL9a/pj'=kLAr6<YKMTK+:,+<QP<*8OfdYfI?+#2
%0+9ikEU3ShVgu*l7C@V<*:2uN?YPX-Mm!Xs/`bk&^'t#^b/<n;k;PY`n1Huq3/^j9*_R.`.eW]-hU&4hBngt'h@KZ,BI%NSa2X.#
%nb6?NLXBdVpa5W`hN#`/&o.!WP$S3>b0Y>#eMHbQWW8EkqP)M%U]=$KorUmta42&D],eXP$K99$2*f0BH.eO3gh[hG0JC>VJt0`H
%d!'pJF<YYDV`-G!Xf^aSN*5CT,lcY>pU;s82hn'(rSJLc?M@(b>7\G,"M5dNJ<>m%_g`-4*XlNSQ1fQMlIPVFS:..V<U_rb1<C)6
%^]/_7Dkl4R62tl5Pa/SImcl$@69^pJb/j<9I-4_J?ZN^[L=92n+\@+Sh:;M)]WM*.V76b9bN$X>=$7+NIX9,NlK=Tm%[Auuha_!u
%%qcg,W^1sPalWjS1jg6q+O#)<h,!+PRbK'WNnJ@5&U1P\7KA;J*o>'PL)LV5+:0Q(GAi$+b/PeLm*1kNSi$WG!M$b&?>cYBDr[FH
%k+0Mb4Y+^ZDh+LKN2=F>](o+'&5keDl\;HQ%WV^mT!t8>kF"7]pD1/](s\1XEVm1?`DEHsa@QBY-(pAR/7+`3]pB*0"%3(]qKYo1
%]'!4s3]r<Nl&,1=^^Ch.2aR%`8uMBl?![3'dEhRkhCuU7#!N%Sa*WoDT/O8l]19G3T^t#7m5/:Zp0OaRno6#Dhq5XQFL;$kq8NC8
%,kR)l[H(l%H`LI?;]:80V[,p@*IG1s/9Mqe^[P;W$*i_HrAg7^WOUVdpOBgV]?,P/E2\\:0;\]1Rm6Q^ePRCM?,>jP"B[)1/o;$.
%HKB?LnRnY5*kHYV"bg?>ja7F?S'AR]#/]cDFEW.qeDna0K>RD_'r#&rLN%DAio0NVaNG>04nNrG?eQKkME36u1LO[k(UUn'UD?DW
%'kJH0.@7&.oqob+:dTI<L;*"L?f]W''N'4hef2TQpq1b1m:Hs<'g-".m\3N].@X`e>2A198c!:7C9sO?]<GYuLth&#pj#/[CNfZY
%GappQnfI!;:%_.N"Qn;N6uf=O>#I_DI\##Lh<q*Ns,rUB^hm0$V9F7agfiMS%DlHLqkfU'pUgi&Nd&gq493h36^Db)aA7_58l_,&
%:>!4,N:(_^Mg>H&62)\oo1>nMZQ73OlcA[k0^uDk'Wp[%mP:L8a8$=SJ<H#\X.PlK^PsrafFeGs)6MukF?mS)\+Y!H]mqG\.t\G3
%0phn=!H^c!.c9Lc0bG/VD!(;65ZEmFq.*HLnd]ql.Z_;%XTG(iXX17D@3G<UoAbSGK7#9Vp\7=4qH<QU6_+l;%GCia,crL6Sos?`
%J%q)BcH"[KQW%,1F,st'\f;;M@TRpQ/K=6^.-qls8-P@?/3B]"IlY==i@L5^l#Rec#LX5tMP3,YKMED(J(<Wd,l`.`JAS^[b,N2a
%Yg/s?k[g`E]T@B3=uje1n%:Y]ks<-%Kp&gGM=-%&7Bh5pDk.cU%Jh_P)H=$e.33Sc$^E>s:?[.d3Ba<-R)0AR<pGa%U56-TrF`1F
%p3TfV79.sB:`U8N,-qk,PtX0p;:MK4F,=H&A`qf(L$J%sY>[m3n#Tj!(i;XrF77TRkuH-Rn^@3'//8Qc^'L'E%"a$sO:J&aI![s?
%]aR3sF#flI^TE[S6m;QA-'ScdQd;6%+;EE#oC_Rk_RrpLHU39k&]]1ijuj0`8qXY09(+6k(BUb??9d@Afe^H[b5gu6VXC[B>7EL2
%Eu"es^66-t^RrLD`lduGjdHRb"aM>RAp\eT$1csmorAeQd\tb"lZoq9M^<<I1bdOk\5i8hcR%D1aEtJ:[FM/0I.N>4I_$$hFNs3)
%(*oOP^1%!)]$/\UP`eKK8.W5Zhsu5q+0XRKXf?YkhLSRMaM@`To?$pth"[/1c5ShtF(P9'_)u.-fabq0jm2c=s$rsW>?\VC&oP(C
%LZ1$Ar[V7pj?:<dZYq8k4U#P&h+uPli0Z`Jkqf]P)\!/<`g;0_aNq?f]TXpHkt>ge4n7;sqR:opVjDT`[bKq/IBVQLbKloKc4Y-f
%gH\\i;^cLsp2%!>5@iVh.t8_1D`3&mVo>RX?uP(Loe_=OPW-ViPiI,]hX7^s0/Q5VS]os9Mc;gH`trCG%ES3pUm+_ko@;kRSu5OR
%<lCc/m&8Woo@6V@RQH#HM>GbcmsWX4PlB,ks._IpUH?cO9)T9QNip>sils7aeqr?KFltD7s/RC3pO^W`M&`Z;jks=q6QGd6%8AN;
%]GWH\Q3l(6(8td"Y$bP_l9:O4jL)#f$ern^p`+O]mPqiW\fZH!Do]%mQ7"OV;HMD;s!+X<dXEC"maL%ih4Nuk=='&O?8pu[HP+ID
%/R$o3:bunqMCj%<YSKBLCnFnW!n.UA9d4Je/Nsua#;]@U-R_n9<_l-krb*G3Yn^aFdtgaGn,d+9r-f<:*e]5$Fee)$Yig/K6LO4a
%G0&W+J0l&U,PqjJp;g%Yja8W?Q`BS$3&2,Z?JG)Q=+:&R&CNnn&tm!;7q'g,rEgU)\!HUj/lOpY>S5,#8d^#gP@(D-9n5^#T\8;Q
%hTRf<Tc<*:LTAD-NRq;*E.MmXZqPXQJV/16'&8dOd[h@Q!#e:4aT2Yq_PGq^q,M7]$;C6J0g;AIYT&E-XMP7dmp_uhXf6IHLd&b(
%e"-uNX?.R]c60cT=dZb"a0\^#BSka_5f55@Y#/XrQL441^E3l((;f@r5MQ?B/8hL9j3l$eBk0rEi+OAgiRQ_KR,hm0C(0cloR:[!
%"QmDaQsX!j2UC713H=kpFU&e'`.HY[!ROZZ7Q8>(7^f]T>R:2iFXK;LC#<>cE;NK:F"ILH[EF\o4Z%]MBU_CK$$7/Sa*f..10+\8
%4`6*`dA=7n_e7g=Me>ARB#;fUn0@WnVG3I&FBn7E6B,tHJ.G@[V;KA6!a(=V:(pT"Rqbj_%2;fq?TKU]fg$M:4Gn8U"i2R@X@^A'
%[>/EDpn4PWU4<5#kg5^e.mI:$AE\CUC*msNI,(80W4h/^GQq8h$["l^#raJ)Vr;\ec#>'j?@;AZD-eXqr=sVKJd+L1fddA7N<IHH
%Y!=DWYM:?7?K`p!#3/&":OQ)u&Q]V]/n*b_m1Q.,1\9,#?4NfcIY=cH7RsUl*Yl1':-]KNkjG>T1`Cii>(c&'.OEViDlg@Ue9YV*
%hL56pZqjhl<"ib1UnD)BiJO"S9iXijB7M>Va'9)1ho[9Uqu*Va%htI078"%Lhbudm::H.Jg"sD!Dct\MrZWgl\ZdT7-Kkf/lq(W>
%nS"3sJ3PK)Y'(MQL2nhI3uYY5p3:nJk++DQU;(BV[7B/g)N8Aln!q!+eZ$DX8\9-2LsN$B=(6u?S:\-*]2>OA%ULS]-#s_j5/m3$
%Qst+[cQEO"2Wkf1$X,Y_!EH0oFgoY9`?&AW`[=mn`H&k91-T#o.Zs@[*`Don$9,B<WFJ-i`M[LKn/W)@%A6'<$E9jj!k?Ar(i0J"
%&noFKo\RKF^m4eag)>_tlO*JQmUQsS*99\=I[F&+kk)5T\AOP&CO?;^J9;<K]&J60<cEc!?eGh<Ba;R5!^*mOSZ-+K]]Q5HXPfS'
%m7F^<M(MGA[=UN#2/4!`R:+/hJ0s>D':l0l[L'G@JL@R!TEQ$Z@M>Nuc?d="36Fim_mHggbC`n7[W.S_eu=ETMeN_0D19&OF*bCP
%B6=='kT]j,THC5^em^bVX'mqSLsmhI3*Yi`8>metI0VN+(]aY^,6?C?LNcNI-3+(42^=Bd\J#CP.66@iNP[rBjX+s:QIuuZ;hX_+
%X1Df1pj?Bh6fnpc;A4)]ZmQ_`O:LBO_TgT@;;l;ER"\mY;(pF#>BA;%RVIQSZ,Y-\QdVK@pnnV'3ZK<ELm`ls;,eG\UGV3aeSp:h
%+Xg-O5La;6WW4mFG$OhD)PKa),rY+H]\4;@@T*tnOX9K1A)X%&\E?F]eN@/iJ'+FN?5uVWo,MFEk:sG/%T*I=06Ibt!ONSVn9.Xm
%np7.,;$^.CWobO;omtk(N59rf$:<BL7-P/(<E1_XT8"$kJ-H'C@9ARsjFF+,&HLM6+IXcjUo^6*qe]OM&D^7S6\HE^N^4(B9hJJQ
%`Y8tDhS<brDi/APh9oTA8hP/#-%)YM!">BL:r<f@-Su5=SP"fUZOfCn6h36.7/BAZiJ=?p"U"[tZn5u7(DF==mj3$Ods:&SC.jXB
%V+ZejV#t\_Oh8$8.RXAgW%;5V;!I11\_:W]MFMHc:oX)\^g0Vf*5-M,f6@I4+MB')<5Wk->W"*mRjXN)P-8#&7!#I<2i?GTg^.^`
%<Qip'J'1>=#LUJ;2M'qkOJ<0/=-HY;][OU??HtBuKmVQ@'uIq_<EVY@\m"'.&\R,<!]^ZpN?NHS441A@W$FL3WiNHpDB]seeF<OH
%Iq`ng0I4*&aAA#'WgeB0H%u(.[XuAUL.8MBX$oDjE^8D>#au0/)7aLJC.qQ[>gr%E[G\(hIIG"/>MIZJ!uQ_EmeJIRT,T6E;rjeI
%HfnDOcV"skjCkRPSUf+!5q(bK7"_c"L9""F3KO=*[W`r7=)6kTTGMF4ZD@?4,"Oel9;#$k9.8IXesruf>!fTAPL<L!H5od.d=]PX
%1!Z,g3RR[L-s)q!nh2>?*j_/$Z)aZ_;0+IR9Xp<I-U;au2Yb#)<Aok^I(YM^5WVe7CKUc\r5pBI5<onW^k=]bbT#L[1oMkSYh;q]
%f7uI`Rk!CTh,B]=X]?W92;?r;O:<Gs]87gS*7ZPZh/T?",oSBi6)s>?Gh(%b0n)*9H(*ef<*j,TTT;sH`;?$K:eeqS'EoiC9<Tj*
%%*[q-B)$+Qo:I<(^Qmb.B`=bPs,u=kbc*Dj+InKG5ruTc-3gj5"D?L/R/6X[R_X0]*2n16;rVl4Sp0=[Jn(=_&XD))s'KhCO-[NZ
%F<1QpRU/tXNf`9M)__uH:iQILO/?_0#LH$pVQ/QiCi[IlZc(g=pg8:;NY6Laj[a(33^5iEUia'?\n-/1Km-H<*M'&!WhI[P^_E91
%a(Od!>hB9\(']iu*_T'pBba;Kj;76AU^!DHaOE2%=EB(tQ&^G$AN+&rBU6t?ALgA+:W!g6cOI?+JpD=S^XUrjK8To\BQ1bj2N9[W
%@H3c^$(O1F!8R-ZhLFN^b[!FU(J!_9JSs1)NXb,5f*>BHNsq\!kaGEOg04>q;r$Ea>BWFSipP5lNK+0`T17`B1ZW:X!E]^ZEn/Za
%!@A]C4W^XKX?pR@D>(BTp(K'0WMgOI]$tB?g#8_t=t(u;1k8S::*);.rdTn'*!Pc8^IN],98WrpFhCcaFL'M@RZ`NPBn0F?25BgQ
%cnP)?TB47m(eB$=f,m%:PWu/W`UM.`/n6&^HNJ4M,HP(a5ft.N(9c='iaD%l,oWoX`VNNRe<b(r-geIo*7d0%p9SEjZ&:u,#VB)L
%BDL(XpY7dJ=bWPVW]i3Wee=IiDV\FH'eJ,<d$&UIicf:1p'B?bF.g&u/.K;^A0!DOPN-%_3d-&k*+9"7hk-Ua:5l^M&f*X-D9^nD
%g*)F%=kmN%lW<PsSp'3F;EosQPs`FMUaFf_jk9A'?E@R!*`Wj^kA!)pQR$rb/DY84OID>TQFsr$Wae>(7jjp+&X>EPqhu]ED(\.%
%"ZPNM?juUgFF9C7qBAp\m*cE(ki!3+Tt*e?eO^1Jm1EYVoY%4kJm(%!?>CJt?r`E(IP="(\It[C,7LUr,%r\>TL?#!'p;VT#;cI+
%RpRm.LQ2M6pll=0"L-HZ9unBumbf?RSAkV)?^%Ms;^Y(ZV[cu6UCJtWa<')_fFu=k<_Wit8doJn-Zm[)npMW`ab,U7KrU72N(nt1
%Y(lJ<J@*"e4Hu:@e?%eIZ>[&BJ0I#E&Sq(cGUm9Kk#j8)Yup:G4K($XR,#X+-0t09]Ot:T-@iXl:I70\ZKN@NTb;NKG?u5,'cd@h
%3hYBKVpjml,E>;$83cNe_OLt5_YMm3jr4jeoej5]3L>J&IcWqE0GA/N<4PX'JL3q'F2H#qah[=R60DYIBM3;)`AO)Ok6WGh^9?pp
%JR"KbicAMB+W!b9\7-SCMZrpY/os.E7GR9d"hXF?kfk>:l@H>F;^qBK+UVog;[cO8B#iCI68QiU(D?nsF9Ml>S2\h/X!K_uG\0U)
%@X&<l%lT]<,UuEo;Xofm"4NQfc@enD94/^?;2rs%.'QqP;j^8$p_1IWZ&f4D$E<$j!TPbYCG-^1?N;(j>jX4p0"G8@"q`?tlHrFY
%h__(N==CHi5WB-,M6G=r91LX1R>eqJUhPcOU<OB2/,5<aA(eQu;%+@00sb7<Nn)a:C\^s&%!K`=C)kK!VI4dD/\#&L0AQ4$m)@J/
%g+Vs]@YB-Kl\1fIG0g!AD+=[^/\r<h,XYtd/-V=U<M_j`7]nW#9"JuH.B`+^Z<P56<\$k+fIf4JDM2gfEQiA`e[Z^m>C'Y\1FYu6
%8a'+bc4LgKdnYVBE9D%D'h@0a?1e5eLp\/XLYSLF2`p>m[R)T.=f0(W5CRN0GhG:D9'o0)X.F((ErnjsdDT]4_>)Pi]Y(8/iMrrG
%O"7M7-=62M>l[T>@.3t7mIn^4IFPA%<nOVUhMq$Rd2!+DQSm\&;2-hFI27"2'.\$d*PdMmUf>VQ7r&=>l6\h,=KYb%-b5*SmW2nc
%3)nJ`DWchU*8j-p+**`#,EOXS<m#4s'$3,bSL3CE+Jh*[2)S:e;bOf3+!>=\JJr6EatECreh],oA>:bKKp/#&'2PrSFOcfp-<pW5
%(6a1aR_XQg9M:C>G;F3_W[MuPT;G+8"ZofYpJX[+Nu_>`>h'P7EgsFWQOG01E2E&lfA=.+)eZpjILQ21_:Zr2$RMQ[Zk:(YH!!rZ
%EnqXr[**#S&'o93?qX5c3CPs3S^p*L;*K7`mb2eaV\J/%*[-_]RGghL,tO`$OXiMA%1j4d-C?c$YR%dZq1K-fqJla#&Rt`MC`73I
%:%]UKnbNCH8igk?%/VOgaof/^#4e/$Gtpr-9cW30Cc;Y7EqP4RGb_!9R08k;!Ma)"mXC=MG^pmgNGoG].ts'W.[_\AR!2\a,:,kg
%r:E>=f0j-3J4i"IZ]HaC=Ue-h2]#:u?X3Y(b^`l-oH!3r4Dbd+6!](khpkgG.Gb<?H"7^$0O@8Bi$-l[X_'$JI@jd4iZF$JP[>22
%AiOqB_%L2?-J2@iJAdn9A)*J3*,enNBo"CR:r6pjSG,_t>90A?P3+2p?:4ZJYC2kpT#57OVO-[eb*&iC'f9]o\IHgthb<;ESrX7*
%X"2C/D;Bl#U1JJpM5qbG75d*UPc`hD3Rl->Y#XI'_Vkk4(ORoL(-4dFZY!^@FPM3VVE6+Y;Y(;&J0"a&QLds4"$['2P:]khpoNG7
%&<N1U!oriKaP-#QASk\6OKBIVjRHeOKVa0`>S)u=!_LpT]h#?=@dU(1cO!Q)C!VU;fYPrjK(4TZJrmohp&92UREMD"oL7C.V[X&?
%@:;c+`fU)IR8]6=1SI7Co2HPpY_SJ''"Z@clYrF+6B/osUS)Fk!0hTcFEK-i;Hn4gUQlGoj70&`^dVmm?[8kZWqFor#a=fkT)>[T
%1f^K8hXHaLFAF^&cbu[p:<l/,)5a>Q``>Bc;b\TKkYVI+_eTC;bgQ]hWnY)/E2r"N])IrAZ\P`JnO\tlm4Ps6\%u(dh?h9me"RAD
%kGM]/;Q"<!pt0.oOU%D[S!n2m2&dt.5nbN'>P,_)ToZ<n-]oT+hA3*tcFIirEeYPEh%"UBSTFA'kg\DOQ0=L\;]TGpkstm:X8Ic(
%k0bHtM*@Uen=06&fpEV)9'r;(PS\p)YlI(PQ04!1CK:\h*REm^?s'b/lG`H`*@,CqAEROe8]NP6-Us>Qj9lI<SDIf!Y/eDg)Yn6c
%pW[%OcR1O1rCt.MCBF+!HO?M0_+F$7@MCefA-\kq#eNNMmDp.T%KjoDm5V97Kl4!_^pVa/339gWK.+cQYq[(1=7IUbEtc/l?e8+N
%qJsb?rbj.<'Wbit&u1X&(/qScPI;@f#5&c7!d8mkb\Abagb1S:/A&"+%].n]GlZ'p(N++(77n[)m,HA:3\]2sOU8$4nD&<((X*TB
%'e7S1%fnUe&r`dF6-96;mMn0kkS.W+%Y185dIBg49\m=?<Np<6FDho9p0EN,MZjUSEW][K*>5+Q8BZX.ldj;2'5]NeD=.F5Bg8*r
%o[I_EJAfWtgXULUZguI><8Kc!(^/q+MVH`[mro,Uf2M/3I_oBTGMa1kMOWojc@59)\;/n1o?D;u3-n*cl2&EFjYcGLAncc#U#3MV
%c(`qTq<s^!^XIPd`JJAKb0g43bE5+(IYgZt>MXf":uC7':E;WI622gS.1l\SSCg`*mj"He#U/u#Bf;=m/c.5/SJ4bC,5?^3q^.2&
%rTQp*:lHFJH*Yfm`=tO4Z_TIDhu<KF$UGa7!C<56,D+RSb+tEK-]5[Gs55j5n^tRG_8(bM%tE/<]k.F#/7n=kaJATuPYrs9c6**)
%CNOb1E+-(%58/ZH-4>;-Q(L1T"JbI!;$F+NN<8V48GU@^ARZ$"lpfWQLgs&8$+P#/iD3QU9A-tJ)uOYA-t=Q7=agq?b5D@1-U>bP
%q;>&&<"e8[SccLl@*6r/>h\Z)cT;!g8IQe)41l-@+-u!Xfl4.m<4QE<D%sP3!5K`,%K?L7A30lj!];;l_MRjsq5BM8I@_'QgDrP(
%k++Jn\%S7JRKIE:1C5?,-_dM09p$eCU(n-I3"n'bKa^ho"`lbU%N8I2f.Z';q\Vb5k>SDCq>A)CLkk`Lee-M&-\&uX'n6*Z'Fe&E
%T:ApF>^iY2F\g8/+<?XXb1bBIDSkPoIMT/&bOr[Ljno0r$pYOBqqt&CU(L'L7`1Rn;24:^Gd.t_q,QB'j/A5=pe6-j,.i'1la_\i
%17YSk/SBt0:]l.a(qX:icT/28.'6^`aK5$8'>`>4gju/3H3.5DVFXRp&_oU?%Y0q3a'_2`FsZ_$%uN'57_aEJQJ!8;MB)B_=\>0"
%i4R6I3^0Pu0]Id@P>6L"P.X]coAq;O5J:rEofII,+Q0,T?pnt+\D3tg9S7Kr([keUD'blQP$X7!n12pB]SW\*(HRGYS-?Fo1)OUg
%r9"ag,fO2=&)9B24Jt94%O(=q6KDIXB8umgN;49!EaTs,j;Y35j#F_;&>A=n`k.9V82P[HPT%jTF#^d$`:;7/+jL)R</b,5/c[nY
%YIt<nJWWE6csKc$ZZ:WHF3;9g*baVj'ern(!Q=%74qKO<)Boq8Gd0,2K^33*/V']0HZ[e-*/U\S3-.A=i[Pmm`80W7&5O[Y5eKIF
%"7]+K@0Bl&ap<0`-%mrmbm8<h;[@(HAJ*(%l6/"8h]kgr5TjmkT1/m)Uk](C,H)q7Ci.Tk,a'`'d7/A<`&<tji<)gUM763-,[oMu
%O^r/B6dh9;#K[[h-7%b7ECaECoT>35bCPnZ<!Fc^\N*R4'`:KBD:OcgBjjD5Z9*VbotrK&6TV>1NV(Ao%PjuFDIhCu<0;09CITZ2
%8042@iXo<sd58-=I-qQVkY354;0.L4h0@Y0AAj\Z[,hZb.rV^3mB]jLk__l-UZZ#&(t!pP1_Nmk:@sM+,*4Z;+#^_c(t`qh&XsUm
%`qH#d@sh=V)O%k)hjEY?<lTM7>r3>;"i%=J.W::=A#!Z66pX<`DIkZuX%;,->Q`K>?j8R8l_&m<C"kL_92#Pa3SNb_a]RAg9icac
%Aoc+N9p;I^k2R<Kad>ULiA*-'nR)\5JeM(aZM60]+5*A;>t'sSHug/`Pr`KkWO<(Z9V3Yec.$=67[$pepEjTX?(2USD?8+O2O4']
%FKfdaZ`t@<Qu29deElIsAnEle>F8!X^,E<grluM*D%Uho^hi-i_0O6.=-GIBgkW,EkmaH4*FGRP('CGBGuD`.bfdi]ADSA+?4.B.
%_/bUUgA8lp>ULK3R6SWLKfX6,9Ba83Z1-tQC.Z[m1J8l-G$(cO2eMkB*a=reC:U[0(N[a)kRU:Om:<T4,%K#=[4TF1X4Or>>5bf_
%BFU31[P]u+li<#&75],:6>O-:eu\;DorFT$>"*Q$[n\6TD;>IlYP$sr1i:f2dpqu?^6[%$k^%*^Ej0'6F0a\h.9=Kt;C<*b2<X\8
%X`p3KC_Q*_AR"=RIG8`ZK3NHa[R_7?HREXQ:9dd6*aUNjHQj>DfaAolg2!1Xe_]PH,PsI4c.c;^9%f8QcKMM&d`CC*]1NPoT8tt+
%]n=q!g'J!Sah&NS>1n/L0p%Nf@ihU81lgCL+e9BWkAkmND3L1K$9MlWm9_p'KNW>]Xg70gPbjsm\.UmKjh0>k*p%k*j`!o*/tA($
%Y$fsCF9r%kiqdU%.p'((g:Kc5_;\W4(pE7=XMAsj\%Q_>h0:^Hi=:.o[QAJ"cJ?Ie_;tU^Q3d4u5U>.NiNh]&DJDh*`tEB;k^\H[
%E$)]h-b#PcSd*jk4G-D8i)t@V1XBe3KmI$P]ATNe_j*/=gHmaR>f5Fj2Y&#t=3T4DhGna*nenc%aQKO%L-B3c,(8u[]&tRJNeHl%
%T'c?(3Kh*^WAd64FtSF\Fjgt_P%#'K*_Rie0bp/@YRIhG%7#kT13PV`a06)0%guGZ1FscF"fs?*3&\_71:Bc@)Rs?;kAWP"F5[]k
%Z,1SPhC%0#H;jT@Wm^YpDmBrbTt2Bq^LKOIa[)IGd6*f:ZfW+0aA3\gYusKR`c0fimdc-#[+1fIF;b$/VtoPC50X?4JM80:f@"pi
%fd:P];t7AOXS[kbEo`X_jNd.g.ArAb]>"Pa.`qRf<39'c;o=lK7IA`'/+)]u.@PC<PAl1Jol;Xq&]'h+GV;/C_\`kAKjp5PV:/Wq
%([(b@=![RWb]C#mCF[>C(70%fQFAc\CcO3XYli)V4J)qj"4$^T3=n_"&J2[NWPGDggBQfJYQ-2&k1iQ=qW"Pc:_7Gu?qokIKB$Rp
%(-IIf1PFIVA1/4#bX85[-kd5]N[(p#Hk!rak\!!tEK2'u%rjK7pD`7"Gag"9Lc^mV8&E-I)u7Q81QhZ<!Lc8a45X5:*`%/$NI:*)
%dGd&kMMb!7&D_7n2WcNeHngX.CpC"rK#G[pjd&8[h'GRIm5^LMInmQ:ZoR(\2K8r`lH9@WQ;;9[[mg%=O&)2=m=#0Gno=,RPDF:)
%4qb%?O4j;=QtcohenH\^`K,Y5/UG)s[lNK%m@[9MpoKU'JnLU67(n4lM7I^+RC"gfLLeajA!8"_ZCW$OO,"D^E.h/cLZtWjNXppm
%e[:>CWL_5;:SV_YSearqA\'`l7+,l6C79.oEfgdnE%Ed;p*cM8Yc3'%EParG&]s+V<bo$ZG1Ma!3IA#ZaG%BTNEemb=V*^Ya1Rtr
%J*+Xs*7eQh<?(,FcPl1>_1<bh6=eB^$aogN0.cl@o&6=@ZO`'gZp1"Tl/Q7ol3Kgs+W2g<4VTQXN@/piQWVs6pe3N01==CHe3EYe
%AY_CJQ'nFa//MI$"e[;?>a2!mpQa)tPZZ:t#31VBefXR$eL+MceWd6mXS.O3D%/4C>W&Ao%Fn&OHa,Il:53oSW*u^tm9H=`<K3:0
%qc[sl+F@Ou;4'cZNJTNtBKGo<gcj9LVNZ>tMm4+gqai'\U=.jdX6?ho>LR.lo][9]kte!W++ACf3:hi[o>t'AQB-LFg]@4dST*ZE
%f]:t,kD3iU4mib(*Ck@-FQ/V^=/Rk07_2.B$b%$rK-,YQ[pH6"]5haWp\Z4Hl4oGb8T.?cl[<]bT'0hR(u6:AB!-^&T/dA?)f.Ou
%[>uQ[S/HLgBWTSq^LF%>Gfl8n_dK<?)E,TpdW"Xp<^t<R&\_'rWZJD<8Bteb1$+uZ`oRfD+0QW#LZHdUUf7\Nn^8m.XA"HBDDWe#
%>GoS`&gO7V:1U6@WjP&s*b8eNPW-E]agZ03<m1GjmdE,eo5["@So6g`0JQj0V,:i%mC:`*m5,J_ZI[QNfap,hH!"n%<@C^V<hCa5
%=rU]3a>t*:Y1Gg!RGHN^XDO0*@@bZaLPBA!m6/VF1sqP#_anI!UIEY?<:(4)Z4"bU1co:\+a;1iB\Xhc=5(^-h8[1tUf.MRA]!;)
%#Tg^CorLj&AT7;/RtoWT;e9jS\t"^upT>^,0joJ'S+Q3F@7U->YhuiDn5rY?Hhk2\gJ;Y*f2F'tfe>Vk\!/(j!^ZO*Bfb-"T/696
%&'U^G6bu"^QY$[Dc5:M`"@#G%2a@63%sr@NcB#:q4Qn3.;'u'gNFum5_t3@!L(!oDA5[-:$5W#]7Wn*sL8S\7.<HFGo)h*?kk=)K
%G7AEiTd]berdbtIpo!fZ[sSjNDt*cigIZB1)1KddKRV26,>fh5!G7LnQR6F(Tadq:N/\Z?^^HZA8.&UP^DOq?$^;qC$ATE+9k&cr
%JRK70YfFX<Agi>KjX9ti$T"_K@B6eh(fVg_b(&BR/@\l6H[ctge5.j+];T09-[8bho+(n4]Jr8e"qQ`h?C%ArCNpN>JGDe(-@oW4
%S-$VXG>Ju0J8s78YV_$L-ne?EKOON9+#q.>l7iR"O^9"2<lHb!MY%SO%^*Z7+;uUM&.5QGPZ4*W5UJ%86YS3npaL`F!,sn,Din@l
%Tg2Nli^-3O$7.,.2C1'$9@+(TE=cG@$D^8OTP)abq8/dE)+qHY0Jn3l7JR\*"G(nKPTWt85_jdj5lf=f>j2`?9VRblMoHS<hXZ<-
%UgIsHW)4E518#L!i$J;-5Ck>o=<J`_/I'qWN-^CjWA@j';$J8p].=C:8`6D2nLF`K`;ZF#pn*%05[t_Z`K4_'!X]pIN<0sES%Ae,
%A;Oc.]`E:YE6AdQlt=Z*0Ee/46fEPIN)]9B=Wf_V6G44ii[T.'V6i\)Un9f.!QQ(C'l]Kc=AkENT-[oMTEg?bc;J4='7p;&6:jka
%`A3)r6CRV2^_kS0'FL?B0]o3G^L,MGO<q'b@S$N%m5TaY8m>\,36`"Iq]H2qQN_uW<,GMuQlrIB^ecIEI^V/),&!f)YUu/MkqcXZ
%DX\FAoEKP=lk?>D92QP'8c[5@M@/-%.g9*?#s=J\!"R/4kp?A.,#rj[5Q>+#G))dAr:>t3Ih#C"=t;n]j*>Xn@Y%kjXFUqpJt[",
%!2GH<q:)HiPi.lri<d)NUV)p%LRJ'!]E9*Y)h7s_#kIHa"$?<)Oo~>
%AI9_PrivateDataEnd
