%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Head.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 260 260
%%HiResBoundingBox: 0 0 260 260
%%CropBox: 0 0 260 260
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 1 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -260 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 260 li
260 260 li
260 0 li
cp
clp
59.6729 74.9204 mo
48.5049 90.9194 56.458 119.388 32.8828 143.846 cv
31.0948 145.701 58.6011 225 159.331 225 cv
231.127 225 232.644 158.1 236.485 136.064 cv
238.674 123.508 235.48 74.4389 194.693 54.9097 cv
138.971 28.2285 143.257 16 103.608 16 cv
104.522 33.8515 82.1763 42.6806 59.6729 74.9204 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
.651301 .62591 .384436 .281712 cmyk
f
147.74 219.622 mo
105.783 216.845 79.1912 199.268 63.9131 184.325 cv
46.9072 167.692 39.8145 150.783 38.1733 145.499 cv
51.9868 130.133 54.8496 113.32 57.1679 99.7085 cv
58.6743 90.8623 59.9756 83.2222 63.7729 77.7822 cv
72.9424 64.645 82.2324 55.3555 89.6963 47.8911 cv
99.0132 38.5742 106.66 30.9268 108.307 21.0625 cv
112.749 21.1869 116.53 21.4998 119.889 22.0168 cv
121.676 24.6084 122.222 27.4046 120.753 30.4522 cv
108.966 53.7978 71.4604 66.0264 75.7466 99.3779 cv
84.8154 94.0015 101.711 79.2846 121.708 79.2846 cv
134.582 79.2846 148.742 85.3858 162.921 104.011 cv
162.231 105.066 161.613 106.185 161.076 107.372 cv
159.575 108.768 158.02 110.145 156.433 111.5 cv
142.798 99.1975 122.225 97.8474 116.421 97.8474 cv
115.484 97.8474 114.932 97.8826 114.857 97.9126 cv
110 97 101 94 101 94 cv
101 94 100 104 98.9385 109.202 cv
98.5288 109.626 81.5029 149.653 81.4233 150.136 cv
81.4233 150.136 li
81.1577 150.34 71.3811 151.979 71.3771 154.766 cv
71.3766 155.139 71.5518 155.534 71.9494 155.948 cv
71.9659 155.966 71.9827 155.983 72 156 cv
74.1211 158.121 81.4126 160.95 81.7573 161.085 cv
81.8111 161.329 85.1967 179.276 89 187 cv
91.3501 191.772 109.39 195.734 121.546 199.137 cv
121.548 199.142 121.55 199.147 121.551 199.152 cv
121.552 199.153 121.552 199.155 121.553 199.156 cv
121.554 199.16 121.555 199.164 121.556 199.168 cv
121.557 199.169 121.557 199.171 121.558 199.172 cv
121.559 199.176 121.561 199.181 121.562 199.185 cv
121.562 199.186 121.563 199.186 121.563 199.187 cv
121.565 199.193 121.566 199.198 121.568 199.203 cv
121.568 199.204 li
121.57 199.209 121.572 199.214 121.573 199.219 cv
121.574 199.22 121.574 199.221 121.574 199.222 cv
121.576 199.227 121.577 199.231 121.579 199.235 cv
121.579 199.236 121.579 199.237 121.58 199.238 cv
121.583 199.248 121.587 199.259 121.591 199.27 cv
121.591 199.271 li
121.593 199.276 121.594 199.281 121.596 199.286 cv
121.596 199.286 121.597 199.287 121.597 199.288 cv
121.601 199.299 121.604 199.309 121.608 199.32 cv
121.608 199.32 li
121.61 199.326 121.612 199.331 121.614 199.336 cv
121.614 199.337 121.614 199.337 121.614 199.338 cv
121.616 199.343 121.618 199.348 121.619 199.353 cv
121.62 199.353 121.62 199.354 121.62 199.354 cv
121.624 199.365 121.628 199.376 121.632 199.386 cv
121.632 199.387 121.632 199.388 121.632 199.389 cv
121.634 199.393 121.636 199.398 121.637 199.402 cv
121.638 199.404 121.638 199.404 121.638 199.405 cv
121.64 199.41 121.642 199.414 121.644 199.419 cv
121.644 199.42 121.644 199.42 121.644 199.421 cv
121.646 199.426 121.648 199.431 121.65 199.437 cv
121.65 199.437 121.651 199.44 121.651 199.44 cv
121.653 199.444 121.654 199.448 121.655 199.452 cv
121.656 199.453 121.657 199.456 121.657 199.457 cv
121.659 199.46 121.66 199.464 121.661 199.467 cv
121.662 199.469 121.662 199.471 121.663 199.473 cv
121.665 199.477 121.666 199.481 121.668 199.485 cv
121.669 199.488 121.67 199.49 121.671 199.493 cv
121.672 199.496 121.673 199.499 121.674 199.502 cv
121.675 199.504 121.676 199.507 121.677 199.509 cv
121.678 199.512 121.679 199.514 121.68 199.516 cv
121.681 199.519 121.682 199.522 121.683 199.525 cv
121.683 199.527 121.684 199.528 121.685 199.53 cv
121.689 199.541 121.693 199.553 121.698 199.564 cv
121.698 199.565 121.698 199.565 121.698 199.566 cv
121.7 199.57 121.702 199.574 121.703 199.578 cv
121.703 199.578 121.703 199.578 121.703 199.579 cv
124.385 206.503 137.768 214.478 147.74 219.622 cv
.706416 .679713 .45539 .455665 cmyk
f
224.691 172.045 mo
223.793 166.633 222.647 161.284 221.184 156.014 cv
221.587 151.752 221.808 147.407 221.808 143.115 cv
221.807 131.351 220.142 120.007 216 112 cv
215.125 110.308 207.58 102.405 207.114 102.107 cv
204 99 198 93 198 93 cv
198 93 194 101 188.265 101.269 cv
187.967 101.351 181.376 100.841 174.921 105.794 cv
169.518 105.964 164.168 108.014 159.331 112.718 cv
159.742 110.797 160.332 109.018 161.076 107.372 cv
161.942 106.567 162.788 105.757 163.617 104.936 cv
163.386 104.626 163.152 104.315 162.921 104.011 cv
171.235 91.2983 190.026 87.8932 204.338 86.0371 cv
203.785 76.8671 200.877 68.8078 196.484 61.4746 cv
232.411 81.7298 233.019 126.832 231.56 135.205 cv
231.174 137.42 230.824 139.965 230.42 142.912 cv
229.289 151.151 227.86 161.562 224.691 172.045 cv
.349798 .306889 .220768 .0452888 cmyk
f
161.076 107.372 mo
161.613 106.185 162.231 105.066 162.921 104.011 cv
163.152 104.315 163.386 104.626 163.617 104.936 cv
162.788 105.757 161.942 106.567 161.076 107.372 cv
.387335 .329625 .260777 .0726635 cmyk
f
59.6729 74.9204 mo
59.6826 74.9204 li
59.6729 74.9204 li
38.1733 145.499 mo
39.8145 150.783 46.9072 167.692 63.9131 184.325 cv
80.5532 200.6 110.615 220 159.331 220 cv
219.842 220 226.723 169.853 230.42 142.912 cv
230.824 139.965 231.174 137.42 231.56 135.205 cv
233.07 126.536 232.364 78.4907 192.534 59.4195 cv
171.545 49.3696 158.745 41.2065 149.399 35.2466 cv
134.708 25.8779 128.024 21.6148 108.307 21.0625 cv
106.66 30.9268 99.0132 38.5742 89.6963 47.8911 cv
82.2324 55.3555 72.9424 64.645 63.7729 77.7822 cv
59.9756 83.2222 58.6743 90.8623 57.1679 99.7085 cv
54.8496 113.32 51.9868 130.133 38.1733 145.499 cv
159.331 230 mo
67.6152 230 36.0898 166.767 30.7017 154.09 cv
26.9145 145.182 27.2524 142.482 29.2827 140.376 cv
42.4185 126.749 45.0171 111.491 47.3101 98.0298 cv
48.9473 88.416 50.4936 79.3349 55.5727 72.0586 cv
65.2476 58.1978 74.8833 48.5625 82.6255 40.8198 cv
93.4062 30.0391 99.0136 24.0503 98.6143 16.2558 cv
98.5444 14.8872 99.0391 13.5498 99.9829 12.5562 cv
100.927 11.5625 102.237 11 103.608 11 cv
129.976 11 138.264 16.2851 154.776 26.815 cv
164.415 32.9619 176.411 40.6118 196.853 50.3999 cv
238.853 70.5098 244.211 120.858 241.411 136.922 cv
241.056 138.958 240.719 141.42 240.327 144.271 cv
238.426 158.124 235.554 179.06 224.562 196.959 cv
211.097 218.884 189.149 230 159.331 230 cv
.757687 .679133 .626856 .856168 cmyk
f
164.72 122.319 mo
152 97 115.509 97.6523 114.857 97.9126 cv
110 97 101 94 101 94 cv
101 94 100 104 98.9385 109.202 cv
98.5288 109.626 81.5029 149.653 81.4233 150.136 cv
81.1206 150.369 68.4643 152.464 72 156 cv
74.1211 158.121 81.4126 160.95 81.7573 161.085 cv
81.8111 161.329 85.1967 179.276 89 187 cv
91.3501 191.772 109.39 195.734 121.546 199.137 cv
125.218 210.514 159 225 159 225 cv
159 225 189 213 193.676 202.531 cv
193.934 202.353 207.561 196.051 209 195 cv
218.986 187.713 228.152 135.493 216 112 cv
215.125 110.308 207.58 102.405 207.114 102.107 cv
204 99 198 93 198 93 cv
198 93 194 101 188.265 101.269 cv
187.785 101.4 171 100 164.72 122.319 cv
.760525 .68719 .544793 .683497 cmyk
f
121.703 199.579 mo
121.703 199.578 121.703 199.578 121.703 199.578 cv
121.703 199.579 li
121.698 199.566 mo
121.698 199.565 121.698 199.565 121.698 199.564 cv
121.698 199.565 121.698 199.565 121.698 199.566 cv
121.685 199.53 mo
121.684 199.528 121.683 199.527 121.683 199.525 cv
121.683 199.526 121.684 199.529 121.685 199.53 cv
121.68 199.516 mo
121.679 199.514 121.678 199.512 121.677 199.509 cv
121.678 199.512 121.679 199.514 121.68 199.516 cv
121.674 199.502 mo
121.673 199.499 121.672 199.496 121.671 199.493 cv
121.672 199.496 121.673 199.499 121.674 199.502 cv
121.668 199.485 mo
121.666 199.481 121.665 199.477 121.663 199.473 cv
121.665 199.477 121.666 199.481 121.668 199.485 cv
121.661 199.467 mo
121.66 199.464 121.659 199.46 121.657 199.457 cv
121.659 199.461 121.66 199.464 121.661 199.467 cv
121.655 199.452 mo
121.654 199.448 121.653 199.444 121.651 199.44 cv
121.653 199.444 121.654 199.447 121.655 199.452 cv
121.65 199.437 mo
121.648 199.431 121.646 199.426 121.644 199.421 cv
121.646 199.427 121.648 199.431 121.65 199.437 cv
121.644 199.419 mo
121.642 199.414 121.64 199.41 121.638 199.405 cv
121.64 199.409 121.642 199.415 121.644 199.419 cv
121.637 199.402 mo
121.636 199.398 121.634 199.393 121.632 199.389 cv
121.634 199.393 121.636 199.398 121.637 199.402 cv
121.632 199.386 mo
121.628 199.376 121.624 199.365 121.62 199.354 cv
121.624 199.365 121.628 199.375 121.632 199.386 cv
121.619 199.353 mo
121.618 199.348 121.616 199.343 121.614 199.338 cv
121.616 199.342 121.618 199.348 121.619 199.353 cv
121.614 199.336 mo
121.612 199.331 121.61 199.326 121.608 199.32 cv
121.61 199.326 121.612 199.331 121.614 199.336 cv
121.608 199.32 mo
121.604 199.309 121.601 199.299 121.597 199.288 cv
121.601 199.299 121.604 199.309 121.608 199.32 cv
121.596 199.286 mo
121.594 199.281 121.593 199.276 121.591 199.271 cv
121.593 199.276 121.594 199.281 121.596 199.286 cv
121.591 199.27 mo
121.587 199.259 121.583 199.248 121.58 199.238 cv
121.583 199.249 121.587 199.259 121.591 199.27 cv
121.579 199.235 mo
121.577 199.231 121.576 199.227 121.574 199.222 cv
121.576 199.226 121.577 199.231 121.579 199.235 cv
121.573 199.219 mo
121.572 199.214 121.57 199.209 121.568 199.204 cv
121.57 199.209 121.572 199.214 121.573 199.219 cv
121.568 199.203 mo
121.566 199.198 121.565 199.193 121.563 199.187 cv
121.565 199.193 121.566 199.198 121.568 199.203 cv
121.562 199.185 mo
121.561 199.181 121.559 199.176 121.558 199.172 cv
121.559 199.176 121.561 199.181 121.562 199.185 cv
121.556 199.168 mo
121.555 199.164 121.554 199.16 121.553 199.156 cv
121.554 199.161 121.555 199.163 121.556 199.168 cv
121.551 199.152 mo
121.55 199.147 121.548 199.142 121.546 199.137 cv
109.39 195.734 91.3501 191.772 89 187 cv
85.1967 179.276 81.8111 161.329 81.7573 161.085 cv
81.4126 160.95 74.1211 158.121 72 156 cv
71.9827 155.983 71.9659 155.966 71.9494 155.948 cv
71.9659 155.966 71.9827 155.983 72 156 cv
74.1211 158.121 81.4126 160.95 81.7573 161.085 cv
81.8111 161.329 85.1967 179.276 89 187 cv
91.3501 191.772 109.39 195.734 121.546 199.137 cv
121.548 199.142 121.55 199.147 121.551 199.152 cv
.745937 .710445 .503075 .593713 cmyk
f
71.3771 154.766 mo
71.3811 151.979 81.1577 150.34 81.4233 150.136 cv
81.1577 150.341 71.3811 151.979 71.3771 154.766 cv
f
135.708 212.764 mo
129.167 208.582 123.37 203.879 121.703 199.579 cv
121.703 199.578 li
121.702 199.574 121.7 199.57 121.698 199.566 cv
121.698 199.565 121.698 199.565 121.698 199.564 cv
121.693 199.553 121.689 199.541 121.685 199.53 cv
121.684 199.529 121.683 199.526 121.683 199.525 cv
121.682 199.522 121.681 199.519 121.68 199.516 cv
121.679 199.514 121.678 199.512 121.677 199.509 cv
121.676 199.507 121.675 199.504 121.674 199.502 cv
121.673 199.499 121.672 199.496 121.671 199.493 cv
121.67 199.49 121.669 199.488 121.668 199.485 cv
121.666 199.481 121.665 199.477 121.663 199.473 cv
121.662 199.471 121.662 199.469 121.661 199.467 cv
121.66 199.464 121.659 199.461 121.657 199.457 cv
121.657 199.455 121.656 199.453 121.655 199.452 cv
121.654 199.447 121.653 199.444 121.651 199.44 cv
121.651 199.439 121.65 199.438 121.65 199.437 cv
121.648 199.431 121.646 199.427 121.644 199.421 cv
121.644 199.42 121.644 199.42 121.644 199.419 cv
121.642 199.415 121.64 199.409 121.638 199.405 cv
121.638 199.404 121.638 199.403 121.637 199.402 cv
121.636 199.398 121.634 199.393 121.632 199.389 cv
121.632 199.388 121.632 199.387 121.632 199.386 cv
121.628 199.375 121.624 199.365 121.62 199.354 cv
121.62 199.354 121.62 199.353 121.619 199.353 cv
121.618 199.348 121.616 199.342 121.614 199.338 cv
121.614 199.337 121.614 199.337 121.614 199.336 cv
121.612 199.331 121.61 199.326 121.608 199.32 cv
121.608 199.32 li
121.604 199.309 121.601 199.299 121.597 199.288 cv
121.597 199.287 121.596 199.286 121.596 199.286 cv
121.594 199.281 121.593 199.276 121.591 199.271 cv
121.591 199.271 121.591 199.27 121.591 199.27 cv
121.587 199.259 121.583 199.249 121.58 199.238 cv
121.579 199.237 121.579 199.236 121.579 199.235 cv
121.577 199.231 121.576 199.226 121.574 199.222 cv
121.574 199.221 121.574 199.22 121.573 199.219 cv
121.572 199.214 121.57 199.209 121.568 199.204 cv
121.568 199.204 121.568 199.204 121.568 199.203 cv
121.566 199.198 121.565 199.193 121.563 199.187 cv
121.563 199.187 121.562 199.186 121.562 199.185 cv
121.561 199.181 121.559 199.176 121.558 199.172 cv
121.557 199.17 121.557 199.169 121.556 199.168 cv
121.555 199.163 121.554 199.161 121.553 199.156 cv
121.552 199.155 121.552 199.153 121.551 199.152 cv
121.55 199.147 121.548 199.142 121.546 199.137 cv
109.39 195.734 91.3501 191.772 89 187 cv
85.1967 179.276 81.8111 161.329 81.7573 161.085 cv
81.4126 160.95 74.1211 158.121 72 156 cv
71.9827 155.983 71.9659 155.966 71.9494 155.948 cv
71.5518 155.534 71.3766 155.14 71.3771 154.766 cv
71.3811 151.979 81.1577 150.341 81.4233 150.136 cv
81.4233 150.136 li
81.5029 149.653 98.5288 109.626 98.9385 109.202 cv
100 104 101 94 101 94 cv
101 94 110 97 114.857 97.9126 cv
114.932 97.8826 115.484 97.8474 116.421 97.8474 cv
123.611 97.8474 153.467 99.9207 164.72 122.319 cv
171 100 187.785 101.4 188.265 101.269 cv
194 101 198 93 198 93 cv
198 93 204 99 207.114 102.107 cv
207.58 102.405 215.125 110.308 216 112 cv
220.142 120.007 221.807 131.351 221.808 143.115 cv
221.808 147.407 221.587 151.752 221.184 156.014 cv
219.448 174.349 214.341 191.102 209 195 cv
209 195 li
208.854 195.098 li
210.485 173.984 207.407 152.009 204 131 cv
203.116 125.698 199.891 116.491 193.631 116.491 cv
192.807 116.491 191.93 116.651 191 117 cv
183 120 177 131 170 131 cv
169.702 131.027 169.406 131.04 169.113 131.04 cv
164.256 131.04 159.951 127.446 155.391 123.851 cv
150.831 120.257 146.016 116.662 140.135 116.662 cv
139.123 116.662 138.079 116.769 137 117 cv
128 119 120 123 114 129 cv
102 143 91 168 107 180 cv
119.828 189.374 128.514 200.696 135.708 212.764 cv
.778714 .698634 .574182 .771054 cmyk
f
206.118 190.912 mo
204.744 191.748 197.219 195.308 194.363 196.659 cv
191.238 198.137 191.238 198.137 190.827 198.422 cv
190.077 198.942 189.483 199.658 189.11 200.492 cv
186.602 206.108 170.663 214.701 159.063 219.57 cv
144.294 212.992 128.063 203.047 126.305 197.601 cv
125.79 196.005 124.509 194.773 122.894 194.321 cv
120.833 193.745 118.606 193.152 116.314 192.543 cv
109.101 190.624 95.7412 187.07 93.396 184.607 cv
90.9707 179.55 88.3516 168.766 86.6963 160.286 cv
86.6406 160.011 li
86.2817 158.38 85.1084 157.027 83.5537 156.419 cv
82.6772 156.076 81.8359 155.727 81.0493 155.383 cv
81.2134 155.344 81.3823 155.305 81.5562 155.264 cv
82.7642 154.984 83.5625 154.798 84.4722 154.099 cv
85.2637 153.49 85.8496 152.662 86.1636 151.727 cv
87.1035 149.302 101.719 114.89 103.278 111.687 cv
103.541 111.227 103.73 110.726 103.837 110.202 cv
104.381 107.537 104.9 103.768 105.291 100.59 cv
108.146 101.442 111.481 102.366 113.934 102.827 cv
114.465 102.928 115.01 102.939 115.542 102.865 cv
117.337 102.823 149.252 102.667 160.252 124.563 cv
161.179 126.408 163.149 127.495 165.197 127.296 cv
167.252 127.099 168.974 125.66 169.533 123.673 cv
173.739 108.725 182.918 106.361 188.015 106.282 cv
188.212 106.279 188.433 106.276 188.679 106.254 cv
192.946 105.986 196.326 103.373 198.678 100.747 cv
200.376 102.444 202.245 104.313 203.583 105.647 cv
203.7 105.764 203.824 105.875 203.952 105.981 cv
205.221 107.164 210.652 112.986 211.616 114.409 cv
223.167 136.965 212.929 185.675 206.118 190.912 cv
cp
220.441 109.704 mo
219.397 107.685 213.146 100.873 210.635 98.5791 cv
210.646 98.5679 li
207.533 95.4619 201.536 89.4648 201.535 89.4644 cv
200.404 88.333 198.797 87.8203 197.214 88.0708 cv
195.634 88.3208 194.264 89.3213 193.536 90.7466 cv
192.411 92.9531 189.803 96.1914 188.03 96.2744 cv
187.896 96.2827 li
187.86 96.2832 li
183.692 96.3477 171.235 97.7798 163.606 111.718 cv
157.24 104.323 148.067 98.8599 136.589 95.7026 cv
128.496 93.4761 119.644 92.6392 115.287 92.9014 cv
110.71 91.9585 102.665 89.2842 102.581 89.2563 cv
101.14 88.7754 99.5571 88.9775 98.2822 89.8032 cv
97.0068 90.6294 96.1758 91.9907 96.0249 93.5024 cv
96.0156 93.5962 95.1094 102.603 94.1499 107.642 cv
93.1094 109.794 90.9517 114.809 85.458 127.685 cv
81.1001 137.9 78.8877 143.13 77.7505 145.893 cv
72.6006 147.173 67.7983 148.929 66.6182 153.054 cv
65.9629 155.345 66.6357 157.707 68.4644 159.536 cv
70.3198 161.391 73.9751 163.24 77.3823 164.698 cv
78.7505 171.333 81.4224 182.93 84.5142 189.209 cv
87.1821 194.626 95.8867 197.457 113.744 202.207 cv
115.199 202.594 116.629 202.975 118.005 203.348 cv
124.99 215.368 151.453 227.204 157.029 229.596 cv
157.658 229.865 158.329 230 159 230 cv
159.63 230 160.261 229.881 160.857 229.643 cv
165.913 227.62 190.357 217.401 197.292 206.336 cv
197.679 206.152 198.134 205.938 198.64 205.698 cv
207.864 201.334 210.85 199.841 211.947 199.039 cv
219.356 193.633 223.944 175.416 225.767 160.17 cv
226.939 150.359 228.769 125.802 220.441 109.704 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
65.0547 120.105 mo
63.8101 123.963 60.9766 126.502 58.7256 125.776 cv
56.4751 125.05 55.6597 121.334 56.9048 117.476 cv
58.1494 113.618 60.9834 111.079 63.2339 111.805 cv
65.4844 112.531 66.2998 116.247 65.0547 120.105 cv
cp
f
184.121 60.0825 mo
187.632 62.1094 189.52 65.4126 188.337 67.4609 cv
187.154 69.5088 183.35 69.5259 179.839 67.499 cv
176.328 65.4717 174.44 62.1685 175.623 60.1201 cv
176.806 58.0723 180.61 58.0552 184.121 60.0825 cv
cp
f
76.1733 109.552 mo
75.2837 112.308 73.2598 114.122 71.6523 113.603 cv
70.0449 113.084 69.4624 110.43 70.3516 107.674 cv
71.2407 104.918 73.2646 103.104 74.8721 103.623 cv
76.48 104.142 77.0625 106.796 76.1733 109.552 cv
cp
f
65.5259 134.582 mo
64.8149 136.787 63.1958 138.237 61.9097 137.822 cv
60.6235 137.407 60.1577 135.284 60.8691 133.079 cv
61.5801 130.875 63.1997 129.424 64.4854 129.838 cv
65.7715 130.253 66.2373 132.377 65.5259 134.582 cv
cp
f
199.153 74.3604 mo
200.545 76.2119 200.798 78.3716 199.718 79.1836 cv
198.638 79.9951 196.634 79.1523 195.241 77.3008 cv
193.85 75.4487 193.597 73.2896 194.677 72.4775 cv
195.758 71.6655 197.762 72.5083 199.153 74.3604 cv
cp
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Head.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-%N&K]90(O*gfg#=&!U07NTlgO#PP^NoP/X66\DrUT?FB@"?kp@RM/S%%;Sq!.MMe(7YcHhp8-c-r`N5C`M"
%c!RW60:tH*2u(\WVtmCJ(GDE<^3TV@l+=!:m>)UoU+cQ/>PJt5J3O^\IeD/k5l10FGOM1<:cGe/qUt?;pY7*WY?f6+(]5Gg?8DH:
%?&l<[VgisfM8/o?:ZBpnIo<KLpJ9S*Y`A_kSuB2c<oJg3]8mPR#c2+6"ods`_lm,=DQ3-eK&:J+lRg-744_kq$.'s+orF^0=0MW&
%-gp9pPE>BL/&g#CjrU6IaWC-n*Y&4Bp;Pe83Fq<XB@Ss(UHP.g4P`.a?+W7*>`(8p&$^@<Sh:VHp+B0CP!/5Cn(?I^&6ij7*tQb/
%='Sr%*;]%)p:TJOTl#3?iG6="XkpCo9U3N:dbs2NC8\=R:fG?:V?#LI3'sU'QcM?O:NMW&Q0[(#MV)f[^A<@em.eOhKB7]5Z[;J\
%Vg0\m"K]K7*:\ej;h?Y$n/m/Qr/WMFIJ<I"],,Bls"f)pnC,74maoe"H-3mPAbP.7&*8T9bH(7+5<So4jZ?e.=5tb)n_K8/\FEpR
%K9U<=>L:p$5[S_54I9X_/PeM.2]g`QKKk6mGa5e0?iKVTD$5:fp0Yu3lQBR(Rg+uF6g>raXg`5_H1ggs^O<`I_YDl8GlHutQVZL%
%V_&C"E1N\Do[j!OI2MZ14nutY$j1tO,fb';$TiY1B;kSYH$Jh6kC7sNr%C:KqOc*+If92HY^s^HGJAUss1rlF%LMr4hn=F:p,B0^
%]S#']7@iW+B5bM5rXsN:^4,tM.Ii(*&Es/b`'6A4qo@mgJ;Lsbq&N5ZO0>#$hu3Jb=*IHTIu`m<9-_4Hk5"=mGk5/)kS%('hHe*9
%?js9.4`9*D'@%o4!&*Vi2FoVVeRBP$K@X=JqL/_jlkaSIr:.bl+$]:p^l[Y%h!ORf^A*".[i2Cfs,ZHZDUY5\J#'RCmdK!qJ$%Q$
%iN(GR-8t+kJ+dYoqr[S'V`1eu'#)>7*ap###1iV@boc&ViudnTW8bF-jCO/:e^*-g(LFboMB>(Z3WH71oui]Z_(Yifm@3RYq<@30
%]QsG=LPN(jr6+]4],.ZC?h#eQkdNdfqVaY-n,=cuaF@t#-f@nl-bpgs-hpVjQJq:9W<2k?:VVAXFIX#r:LAM@U\D.nl[O9\mb+3r
%WGSftUK[]bo4ROK]S5JFp!k_pj^6sge9756nb`XH6<]+e'Dt$!r;@B1,$"Gm+(%_:mgSii*JM),]n^\>f#h3f(&0E[;u_<fpOO!p
%5`?'L.D!=n_1#BCqNa:Y8<T<i^Pe,"]&L;=?N9isb;\<dqcrMU3q1=Z:L?>74&,"\\,S\Nb:pF`DH?n@!1nb;9?,4ToKT![m)\dB
%^O&eLJH:-#Zh38'RgnoE#GtikW2Z]`rWEkbo@I?!qrsm9.t<[N"olmE4:].IFGX4J8B4J$?bUpXa1"jZq9t77=3%f-IJ]qCIsCSI
%j_*dfQtAp&.ScHba[XaGnN)R`c1m@d`YtfJG&r/4]@bjC0q[oArQC[s0>;il=AE0T0RAX6G&i`%&1c\jk,R?0@KKUE#OjhooUUrU
%n5k71:p#;@^p-Esb77V%e$ndP4F?o%fAD)TmeOco)P/Ht?iT+CYH#o.Bd!rj=*/K<(lEYe"qH"Jcj.h<J"]rKDggJZ,Y(Q<Z7lCT
%PUcV%)OIr11KmrIQghR1VL;8?Y1sgUFSLQ3]-cA$o#;je<8H8Z?P7+?-N>T>;mtr@c.=U6VR^YJhYYO#s7-j,\%<IVnt&bd-i+?,
%?@r`.p$;'Q]R.pjRU6OpdSjM-JiR_;%N.9?C:)S6(&rUP+FaRcodi<lH1L`-]K3cujm0[2qt01j+J/TM\l(gLWN^n'Ya,Vj<uC^9
%iFh?]r?3*'9SIA7U()V4+*]1qrZXN/)R)`nq"*',b3e`ba,g5inY?-]nY=m5*:f67$-F..%@#W,T_K-,N*48!8uYcNPQlUI</sdM
%<6jU&gcU$bDR!.G"k<>dTL:9/Ai/":r%om/b+TlMSEcNb%@#W0W;$Q8)>u]jYS*e.QDkRu(-dRtKQ:hjG11?`D0Oh1fFto6l(H5m
%epBXT;a:#GIX?#=IgCg#IhItS0*[t.ZpR%t%S%ur.-JLmi>0Va61Mtq,p(MIP^E<m%3c9Q4Z4h;$rqhM':&1Z7fMOj",6c)Mrj"p
%rWL@drh"\;'!OKIo`*:>6-l"lm]bt"oo])0pQ9b^ohbN6]n?;+UmKe&mEMC(;hU=4A=@V-Zr<1::Br=F"n_h>_Ts+B%@#VRUZ.sM
%.Z"Kb\0@bHl">;O)BAVMq0]n:_ofjr6b0%fIm^bd;EPZg>:K(9oPaUBetFsmU%T`/rs8"3],c2okte25V/q=(HMZ[k/P`e%-%*0H
%]Q'cf82)MQ6831/Ag\M;%O6qm*=fP>C[O!.r_qAb>QVpm@I`0`>7W78-XV8q#?XC'_1rRWY_8%[1>IQ;kWoDG^kuo\%13L9*,jh2
%Elf[OVjpAZ#rXDPb7cFnRfoLS?1o.hLR`lH+]DeZ7\#B]Q7&9Cr2LWq3X]:%^kqP^aFB$74FU7JLrUW.ai$Pm48u^X[INchTZ-sX
%6:ifi5lg8a_5n\c?;EDac?gX]"r?*(DND$(Dr<-*]*2=.&N2VoD1lW?R>'%ARCu;qk-Vp?_H^][05"\I*DW/0R^3(%Fi8QWF!K'@
%*ItuaBb4ra%_u[oM3I,ZJ;YM"KAQb!Nkho^)kINu3bu2>-9L+^gNXZTrN3]B]t&\GIfdW5\EPp]%[dC=>_9p1O@p-l3"%rI/l9Re
%#@;sCrs&0\med1Rr<BmK_#DC1+-9RAhFC=iK&(p,rSM"&(#T&u\8M@Bm,6s7!>]_X7LL<riZ3>NiN1.?k2>La&$Fg,M0+LH3l9Wo
%32\6r=;a"^liGtu_.e&p9_&>1qZ*\(27/W]3!!NA]nY]]]nI%[_mg^qoK%pSIL9nT"D;\(O`dl=DIA])0\-Iib:AO!L)gA*'?l*P
%[rD>=@9-de?tXVLUC'(7_gV\"&b_eh\$-Ol0T/HRQT[4YUBU?@IR)tG^$FZ0m,KlA(WUcNq/<JE$EOCj.:GpR=;`aH_Yud;T`QcC
%B8q'j[`)g*P*BW+8F]TJ(?c\RR,#`CnGh!>/gi/i+JsdV/u;.=<f.UT!7](qd8.+^Uj+DM$]G7U)_5PMY]O8@kZEg7.0hA/M?u=@
%6n&sG/n7/R)t9OUN\o$d]G8Hj`a6iJ'KMDqai[K'!Ts^f&#DoBn]UX[mHk?&XsXbs%_!IsXcM4`[e!n1/b\&!ml]6<oA9?Qq[W>R
%[.>p:]+<V<Yki4oIGh9?YifC7aKD$,*6hZuqS?7R\"j-h^t%VN4WM?LI<[qCcgb*+`\96B94W8.S\t<N;=W27<O[-$P%,hS)olm%
%]6f!#GE!gc._.RQYD(bL)1OM)p<YI1SYDkugkk':.#+bsrUlGlrgB%frZ`>XPqq!+nJrB;iCR"dP:23^*h%uM7H=fM.&s9Q$]pUl
%/gAr1QN%C%@O-i9Jf%')9;E]iQ5Ss2Bjm37GUoMW1IX![aB#7Do%3-P6.A+s!($QN"jiIs&;AGC;?dMb&J_`9.YRQthEZtWM3LA*
%%i5QsX,*0cheGcNWaDQP!OGW<9GKrr]3+#i2%,YMPr^jj0'0<6n['$2oiU%^0pNFf4t*X#_ba*0MVkN3F]80Lp!hJU:d8(`"jt3:
%^S(X>;#jT&m%OP&-e2"W5:rT463/G%Xl.<RkFjAYGu>#qnl7Oearr9ikFiMPIO%N.T4j?^IaA@nTM5G&!ZXB`>V+SjqN"(a8k0Y`
%9SI&684QdT@gii&3m-qG]-0o@6q>snlKiPq_9iJc.'uPYSb6EiV4r:-jJZQCP6FZEme"<D,>Se4;Ps9Je"]\L^N'1r#N0t4cJk`B
%K!=AI[(b)riSIH,^;H&N:cK".GT<l>]ij:j:pFp^nsQ_+ZOlhrK&/'<c,/9JkpBgKC,@Y_'&cN+>6o$pW!rcE#\GEB_3&PCR#,0;
%lk4;RLfe=R",3c%94O<m')m,(TVI'iNJeGd)(loaY/gZ`.Zr[5UI7!J%!iQ<piBq6/Le>(O&FW]eOcQP,h@s'1@5[P1"m]%mZP83
%,!3d;M=))2/&3*AD7A_X@Gt%,$k%m&B5H0nqhE)/,B9Lh4,EXV]R*b*6mLI!K6Rp3(s0C'hFFr:E%_fikM;q#-1<2;:V*</:Mg9<
%LJ'>_1J9Dj-('*'S%DGKFbH_RXHJ5r=KXOLb'>(._%n9)Q0nQ<`2`HTV5f28<LST@O?T>upCnHCW)8raQcU@fqAGU::/RR"BR-h'
%TZ4>&JQRY=3:t-n@Toj%"tS`/qI`:K#Ac#V9s50sA1dL_)Qq-nAEu=87XQ`?ZKf^t+AnL]leSRr1ZNAibaG^lZTf-#-b^j]"!/+A
%XG'dGK83-s"/q@\$-VZ9+T[pG;P#unCEEiZ5E9>W7*c,-6kE,HlOh4m&P8K]In(;Pp-bYA%3COn-Nh[=(1/"$VT<s`E!68>Hl!hN
%1l;7,OMIljl>[uE3]b3AnaNcIack,klGV]C&0jIh(o$'O/\hCd$_d6dih7D>B#hDdDD%70F\,mL>eaGG\e_#C?1L`nXq"b]TRtNf
%Y;W!4ZrW3!'tW((2-r1H>co,M2d$9KC9-O%nY*30%5mu$;j2g7V*H'_o`QcPe>,\T9](*FaJWYL2"d[ko;*1!Q?7g$H&]X4SCa/f
%Dcm];=V3l$-Z.m7%\0kMarIOl./l!mR$l:%MgE<W:46cV*(<ecGeLM!S_Zt'1Ke#!m`XZ51TqIk*9q7\PD?-$C9dI\WU^&<#\!n:
%%BM7*;\UQ9_%[#WJpF%2lm`84]8)^CSc\:FNHqFt9j)D$"bS-V8IEu2blahlY$2R4[#E>QMRaj0QQuS>KI%;=<7n4FU_FEQ<:j)Y
%/#rDA'KT+^7-t)*@6th>Ag\29Nl$NQj)-3><H]gLjWTJj(?Nn@s!S7m[j&K@d&W[)KKG'+gJ*d1-Hp$7kb9&QX>E";P$#(iiK!9t
%,2fGV=%ph2[.8jL:S^7L$3b?B_g)kpl9P6%eYu/g4Hi[^C/lB/mmsuup$,5gEL`7E[Q:_M1"3ZFBkMci\8H!uSnS1cU3I+X!39Ij
%!d8nikBO6O2?eg$^*mhJFXkAQYV)n`hLl")GO'XWTPcQgF+,?,Gg-%Io3K#u2Z(F8Q7G5>HEX'VXb$(U%;])Hm-)tLR+K.`WRA?^
%p/]"3"&[YMX;b3FFVXBa@"1sVXFM_ap3Q^>ZOc4bTbE'\d:A:rM_s1aeLI\El!]u\O3k+*+#;'JfV`?iok^C0>,g+slE,79qREq5
%X+,q)i^Qga-7'MZ?Unl"R2"K[WOd4BaaNC9k5GE>cAM2$S9E%p;\#QfNN-=2V(G<18sr;CME/=?&_AdrmXD+s>c&=dcZrF>:TC_<
%fqOA9.dsCQ\':n]4Nj&RQ<,:J)sJ.$dsNXiVbs']U%nh[:EN\,E*C6Flrfi?G303N2U7dMR08D8"b6[Uf84*Eo@#_+4D"64Qss^P
%FM\%0RCUMYcBlf?Z7mQ>)RmHET+`)^B-+!VY`km5(57)sqQ.>LnAfA-j2!7dR2PAX%/P/>L,\aZoTj#5)-h9rmGOjVn6V]do=Jc]
%2?5V?]La(YGqApo7^e@0;-lBYpY!(G8U`k'R3/s+anW3._aB@QS&r-r4(=S+a')Pd_(]D"1]7gDY=MKG2ir/EbZRT<Bg,tG\N6O_
%0NKm1?<T#R$9i?.aEmNV`I,uZjGhCOG&EB'Z5A3NB"0HopH6o(TZ4@,pXKV\>aCQM7Sg#tk3ucG_pcC@@Tb[W'O=75'?FM5ge=_2
%FjZirDg2nme!X>a1NCjBe,cE.)*%=tjiu(!ECbq@cUF;dI<&+(9_1K/<Qlst,Yln8Zn)\(hD\C'4(T>ZSWVd$SXlLe",$ru$$F+9
%*FV7*ks2`fNY#V":@ps*XTE-_'C41Glr.GM'&+((S.aMAL4s>DQdi$'eci<*%6K_V$,e`0eg";\^f]9=A/:u+*FNYK.cS*SP83=9
%/--k:SQ=;AYq(rRa]\.tSLKqc?kbD54FSP?jk]L!O#\0XjMW#Tf9<cY-S7L)q7mc=UH&QZ7n5-OH=a6EFTJN>:UP:fnZd^bWM0'G
%1N.Htq1U&3mE4apYsZ@)=Id/;8N%jYYhT>]Qq`raAn"+<:P0A7r)0E:@V*W"`0?t8afDsNhBP`i-b76Fdn^\QX]u;7h#e"lm[AU\
%HZ?Hq]Erpni:*\g%KCG(aH9;cNe@XbB.&jI/GAbsSHtop\!4t;n_"oh;r-6>]l89<;Jb8`#F)n@[9pNcVLC]+q`@+?_n.)bIT2r*
%*gb4&Tng3`[NWQUEmV^B?GCsclM9%5Ih^<G-:-:ZXtV42k\2.]!/!uXC3g(:i-YOMT*/o_mJ,&G,H51+%M"5n*Tc4hQ%MGa%<bLn
%OjWC637s@5c_1BoD&aJCO^Z;L/KGNk!6GX_4Tf1aer@t,'C\'g-WM>+j,>B%"10,#P;r]OAif'O4M<$VSUidGCHdl+*Z6`&j,joU
%'*]52?-g`<"`Af:G)NG&fk`&08rT%f.A<f!N@H!$=XUIK@[P@f;41AfafMc4!AYfjZkP7!e:kGE2S>!@lD/G/P>#*bT4Ke-c0EC>
%_aZpLpk<B2!ij'7'^p!IG\W#,3fC$pATj&h0aMQZ<j9O\<MT#_a/#01'hrb<:6*+u!ZW[DRC]+4TeiDIb7O.RPN"WmiJ'sD,(lJe
%#P9b4Usb3gL!$PjqA_8alH5M],4Ki['F9hVC4bUh[)D58p'?"HkT*(=J9O0a%V:/E;/&#;8>GO2>:@i)MZPGW5]A;c"@L'C)S?uV
%\BsAj7]doWg_oesg3@L,?Uo0;--:lN<C+FDCo@Ed#6-oN\`u_[=X.?L;'h/H@<9s!<?>P'cY/DJG`O(8fGWY!4G8Ymi[-gPIalIk
%MdB7gA&+^UWLtS8J9qGfeWY.T7(NA%hk4t@frq(HpB2N370s1GO+K15#1[@j5/[%*YRdKpXYXcX(e7T2r351\.#o6:'lH&l.1+if
%)@$T$@'7TkZO!0C4H`3:ru;3+_`/VUCNSog6JFIef!&"JnM>id+[t<W,E62m'8WFN8]JZEM'Ljlm=J"O'G'uHpMBH<A^2'%Zl%.@
%i+O!aa;I#W-]lL5Z^hsi3.;"Wq*(0%):Lf%`*e;e,16FS!Y!Yi/l8i<!pG"RbSbTk7f]+S@*U"(?u7sU)&'D,9k>_YZSL[@@VXY6
%JS!@AqC]G$b2hHINm$,L@@Fs0O^&#*-Q'BWA6b]ghf/lC2hsjr_%@MHQ#>arM%sXj;OGCOW!!fSY[7m/:k#taRokD?`6$9="9B'D
%dhsAt5r>6.+cb;(K7TI"L@"(+Y+cIBPlOA['HX]$3cHDLWq#"r.I`'HL^#k:PCWa0$ne2"Jo.aZ!1Off?ipm8@":KE<e7]sjAd#C
%JB?Gt)![BcCGKs*6*sp:e-Z_rZ!^`_L+GVh_s*`:CdmKd%H\,+HFXq,CRLSH]YPt)^9=]N?=`F,oi(d)+T.j2$S,Vu]$r.])_a3B
%p1@*P4tLi@4oO]a^\`S\[Z.j4d]O?#dNX_+?0CG0/1!@YXB#J'^eZ*US&1damG3-gXk=A<=7e?L["KhorL<PRL_>T]KUla>_lK.&
%=/V^Q\CD_RE`'+t:A@-g')kSZ7Td4>RZ#)eCY+E^G%E*[%u6C5'W%Ei^)5tUQjf+k"J8!Ak+6Vj^>]atlQ/qpi^9.&]cX1mHlc6G
%Q*=3\CAcH)?G1pN\^+IYG%t8q#!i<3E=K7=J)JkcP4'nmpmp4#/E(*LV,1HF/;-CkeP,O2V82GMSYSb98IYl=i(lL1)<3?IW>OVl
%:I;VYA<mr^[s#%eQtNc^QG^D&:qk/:`+4GX1<CW%<-$X4.Ap0##mY2bd`r@!C5)lJ?Y7FFUWY:@(M\=N*r*XqhbK0[CA/sHJ:sFO
%$o]!g+0JjRYpW_IJL!>\*95bj1]id@c>ggrUimuh'PNo>4GL/oN?eu>"O/JCEHRDODBr*hFdjd6C3@Tt%(/L(SiI6_P1kL=7mu&^
%7kGu8'Fu#.e2fMTY]0FjSUG8YT=OP/:;#PW>ATjq9=A`\,!Kk$=hEQUrnrh;PK2k]W<I&7j?!9gCAaPl25,jM5!V)P89o06;_RS7
%rPu\GkNLshJ*[,d26J'Jq_;bWptTi+?@p="oM=g?jd';b"^l5*nlutDGf4I3#Gp0*mhOVrd'`g9Uu>Q"n:;KBk&\7ATZ2>-q_@3I
%F,fW[P-I]E1$lqq5unrq.[h2?&eh3[`"UJeOm<:p3C\#sO5q_LLB6u;$EP7[Yh=PA6]Em0HU,"ECjQA)O0\c;JSBt`%`cJl?S(RF
%76Hl4A,YUhp58W$5@aNDWGu,/B://uX$";rp9:jK0^0TYF/Dt(@-sQ,a+u6'iS((Ba_B9DXYa9am.!&$mC3MUh_Dba94,`9KJ%%9
%#PgDGb1:Ni--T]Tfe8%^/:dG8W!.K7'QW*.TSk#6HraN54?Q.XB2qrAPTEq]W.+ZC=ObTOR90pr@8Zlu[(/G]qK'?Z4ST"9/jK6k
%Zk"SIRJC[\Y>sTq`\m_XotQ2TeWoPYg9+q)V%J`cL(N$o*E/\J!CBRGd"NXI9_,E"n!Kk7:OQoS`CP"knM1HRI8KS1$TkQl>ZT_F
%G-/DB?K$PagLRO\Yfo!u[[7O$U&N!F?dp2UL=s")*b7CI0Wa>9/S&<4@ipn\Q98<c(e_gHUU-aJ%-@XV&?,5kDU@BN6!&A]Y0YC$
%=U7JW;)1^,l1Do&Dm*5\mf^N,0KntO2$)b,QfRCj'YT[!0*Iu)*.+8gVNjhr.8,Q\HC.W`mJ>$uH4[9jAKb1]r/PaR-.',<]Q\Fq
%\G>TD1FQoqD_jVf!_3f5^d<MR3fPWU9&J9^Q$LR4rl!XJi85%=DWJJ=>IIj.LBt[!(\g[(Z:j@F09c5X2RAH6N.1.fRMZ8bLRDj)
%D<[]qr_%HlI>H.lHYMg0\Fc6uT@p>UM\r-]%%0'\>TsJRJcSPYODb2b.$BZ]iCf\k7aoYU>QP9lAF[^$A2@Pb2@R(BnDhup*;>=E
%=8?!\kXS9UV7:T]^n"sQ8ZGlM\ChS@RB4f1F,:^Un-S1QMQpsn=d\NQ)*p0Q^TQ89+FpPXlGm#qlr@;TV:f?g+JDoTpNT.kX@u-A
%AP>:R<V4*c5Chmhf#WJ4L&fC/+B\ao<cI,j*grql,0cLP)I&NF?BIS^Z(e]@YXlSu5s[jB'2hrZ!!3W)cX-<D3/Csl?V5si`UB%<
%*DXLT-,#nE$7B^M\C+Qf)OtdBbesr<U$>%]63V/2POTpOT37+f$7/r8:qHeY1)n#,C`4bLR"+$Z:ZBfTKr'haDCu'e[5df?B\\;]
%DA%QX!u7UojR7d/:'/`QFbZ8>7%F/58Uj1fAh,oiNqtqt=;0\:%BX'0T1u\\lI8"HlMH%edJJ$jMscea-*OJFT^BKhI(W."4H^e-
%I_CjJH'nYXR0#BD.?HD!Z<Ni9F'b.'U1d:cqEKd(F4@One(RBOXMu7_OQ'Rs`iT^6BW8hBTGiI\'UD'eE*DUMB$opFn/Rno%jjn(
%!%nG[=X4q]iGPJq)*h7`74<76:DoRt>ga"Eb+:jk#K+>.0`lfTKj(7JN`.rTYZG$s(:hd>]G;CWG"Q$TUm2<6'4rlaQM@$a/epsV
%O+S"pR/]qunikBTCGaEh:;Pe"$f%@q*,95?OmIbW#&J/D;gK+c[kb\qL+KK`KOAT]JRUe-DE<uffN=L)9H)S`iBS''.bdgkfM6A,
%<dGP)BGUet,rBHSTe)@'jsc3ClL,p[;98/k;!ErL72E_FM-n*iNG;G18R`ST(O[tO1uXcLD;?tJ-uPb`8V/_%;4:4d0Cjp2;:i,3
%XTEK#k%0]APi<m)"fK=eCN(]`8ugOo"YX>fisAd-Ae%2u/$.Ns-]W4LE0)CIh+QU%Kkhh*LoW;3aaW@]&tQk[a7E8p':`HIDYF%s
%Ic&i'Mdh$Yg>aE<AhG1$`;P&L8@rnsI3[,B]clO;F\j<cGi*6U8<PK``&f0b!]2?'S)Q\@Vck(]JbH(_J`%W,"IW^b,g.k-8AmsI
%.G9eAho6-()6&@0?`M-h8Qn5N2P^p/(r`=+',nd=6o@R^^.)+Q/>@^U(X@U7A;jom8>Bo93:KEkCDbY[+TDBtmDa9T?^<PnmmL@/
%0uEjIh'fo)L8#66S1o![nkO!J([+V);bNi`+*cm,pEtU:M>#u\aJ/Q/G@OkDaCI@fg'Ndh1J2b;Id7'M=mX<NgQ?Yk06ZDs2sS`L
%XDR^uan:\DGddKFeHbKc^\3\XUpMjT2iCqb-_$kc;a^+jQCh;81m>:(jKCDbfUfaP07Ab.<\m4O#5t+gkq$\RdHl9]g9*84TuNo[
%ojEf,8W&*<mua..R;Ms$]:7<aeG,Z>`^O]i[M\Qq-N3LP#Npda=mnKBQ@Nq&lTmF1r4)Ws$"Cn,^m!F;jZNl.'K\J8KJ;;6&54(6
%9pBE<jk;s[9TTUZd6*op;*`S/KmB,!7;knNo(\=m>tL?.-!LG:@VJrE-"CXf8et.EEj4UU,E^bP/iP#Ok0NFtBmOWf?=WRbR=t#d
%7$gWI/,\=q$t_-XA:;W81!9XA$#87m!43FqpQUSM#,0$A-^f6UOj?On8pns?CG^+m@If]?:1hG,:g7EK8'5>$C=I@G,Lpe3Qt&d2
%CmTk$EK$4t*8aH!_(b/')84Z<M7&[/^c4oDV.ksVGJNi"XnJ01nsW7o=dFq*Z$%kI[([[To)1*)]3!#FRnM&5\U.n/od6S2>[P:W
%]OtgE)U?/nAj.D$"ST?FO;74dnr+m(=E04d6\sK3`>`uF_B()WjT)MC2uA&J^V=]RgKpKFp]p6K9D_dgNUH5cc`n]@AK'aG=_DZo
%]D3E.$odN(BGN[NU.cN\$\S-K:#4K8gY!fPH,M/XQN4o/8:9uaW#9rng[.c-NqSXCI$l7p8]($C-$`bPkN#]k\K8b=::?AZb2:HT
%\sU3Z^?g'SMg+pb["Gj"[o:;%jo/Pt0UMMZNL`/lK!B6n$a@Zqp?cnPDYEF?a%lLHG%BLdYNrNB]iQlT<*5W:-hTiMl%L--M00n`
%l98lomdA.lg6^9K>Xlh6nPlq<Z2J%9o-f8m]VbaT+48EaSEi=7I!S>_d;L;D)[1WLZ[qQ%6XNg'mLF0rRiFtkX2b1DRpA5Ljlp2n
%Zcg%$c[1/@5%efClS])g*;A_N':qd9*XR(Mi^9>:o=tF8oK5_9@W#?HR`Tb^o"sB&9KpQM(E=X0!4uMNi=7)?db+IC^&_0)_Br!)
%dkhbI,JIFti)G@SQbjFr3@1=8G?(__X&t:"Mi7K]#]XJTbBkc!,ePSL[i<d4GQMe,g=^@=5"LQ]o0%qt\ep;o1+#N2^l]eJ9f%9f
%_m?7/G9\nYRkgb1FX:FiV8ZUd;c8f>C")HY<NH`25(J_?!h;2?J[?$mqK03gnn+(sqN=LPK`c119K:[^r,lYkn9XU-p^bhga``ln
%HDQQ.+#ukM?^TS'h2-Po<ibWG3Ph$*W!)"];f1?l;A[W8$[:a^AeX<*mO'WN(jB*-W5)\+%MR3sXO&&eJc<H3Bc"m*Q?OL#aLk./
%1q<qH.M)-=6E&_ZgM6FKWJqkTN<uXNl6SsQR:B7_r^t#9g[N><TXdhP2A3Ku_0e5S/\)-)F%7.XZ-p_30<:(.N5^Yflr>k`@]Ksh
%T-VQaXgL3^*]qL"NeYaJP4l30g0mallQo.OI@#(r9/d\6\_raVFlf9A]`5"ZmI"W!h]6>=mrq9P]9>>52-[>_`(W\85mS-5hSSSn
%3m<4e+#]DIp-7\ol46GAL:>KVhE._Z5Is()gP;\EHi3):o!XUlV-6bHhNP\@o"?,Pk4[!RrfuX>bK]T"FgM#Ck7ugHCS_h7am(pq
%bYjD[5B`3EYK0Y$g+&$TP"MbF:1ghD?F0YCAMnpCAMk[KZL6o/<n4NM=ghJt*'(H1"h@neSI70;/e&hH(4ul'.;l>#(`Bc#c.3^I
%3>G+^KiPs`l<ZsI,A#BuPb/27:)#WeTVs,,T'0d2:RSUf@-e>g)JlGn&Ll?ba;.&R!pR9i^'nFs'uKMc"MVVk)"5Vm2d]]I4GC+i
%IeF<?GR#[ao'@OT]e+Y4`YEiLrB'[+Y-N2NmibGoZfgY1QIk^Jm)g*WW^X'5l;4ZiCW;_'k[e?E=fkQOH@;@(;@>QB<i1g,JiYU<
%QtCMRFO9RkZ4T::0&CL!;N%b8_<=`]eA)29.d1^E_7H<31s;9K,?;O5hXta,4@1G<,`$1<;J@OZe6BW2OZICOmM#H")?r/<H:3l?
%""uG!Fd;K=':R3u/iLcjkT_Pa;pOiHQZT5*p9=,)Ea`bOnqo*7k%ja*d.07\2<1,#qTJ[YbNYbDKjf+EqncchSp]%o^KUZOh=3?+
%ab-;=Js:70#kYARhKOEar(5dp[TX4-ok_V0T,1A(@boVMkCAP'Y*Rgr:O@J5R:g'%J_?2M>M#4J1!3tTq+B$>B+CP6cu%A'V5Xf$
%9rN&GPZ0M]c5F>Y,A'<_XlTsMZ_X8m=8+=@7IQ6u#nB`cG`)2r<50XF:8GmEB(.^PlP*&Xp=S%N%(s6niWui[:fT:qFbuQU&a!jC
%P[U-hjn'+cp]h!5I`XhR\!DM+>e%A.n:&U?SZFfq<SqJ$EK9l\L*\b^)mV2Y5FT>SZ[?F%&Bt`[=[Nn?Uu3F#p:u+nVjf;Q"aK>F
%VBIpfN)Pq*CX`-BHdW&%TuK8Rph;p,2=0TSCj$D[3!M30oscV;1tYIMeSL\`>"s_2MDd@1Nf2W&V(^=D5^L*JE95'HD)^Td<&g[?
%2Q,lq-<&sM"`>G1h1(.5qYIYn`GM)\NdUNY!dgpZ6Q/6IarDKa-aU!TBiF9'::1M[nf76X7omrU;KDukjPU2*5;Tr4'OZ$/rlb]-
%kG7g;Pb5+7SYO!S<;KSV46X\tQ#d)<!:AhSq[;$(04:jT>tZ9[jn,4bCp0e0F5b`MbaJ&Uk-Tg\l%6KXR-o(1j9\huNb.,^ANk*5
%#$@EFIiDKc#q>"Q`]*NV61-5]TOM#_UlEC9Ls9fM%P(T3kJ?dacn[&c3g6Fd&BDi>K\b.6:&IgnRa%@$B@B!Wh<T_`*(Hot%A+86
%3p:SFC>*"7G:#,Tkb'9SQO/k\Pm6Epn[<JuRK?:>(&-aFTa.Z=Sa0J"[oA,ZN6%j$mIM)l>#oQ@s%4o4^Ss(TJOe9.PkSNS]lOdn
%TOdaPC[PIs]tB;M-dO,<EMAIL>?:qG<$6YZhX$=W],sP,7CD''mHs<TA3HVaK.%\g_KooXPP-loTF*DNhMF;nJPY
%"k3mXnRbXA;PWY.3nm'sp0"#1;ngD:Zgqr-qN3aXS[id/]+W;si*/!<3lt70bdsX=[mlt2bL_)fG4F5(aNKLTq]]Wq7*V`Qi)VJ0
%51pjL<CSpAkt6jjF`*ln*>`u.f<MY@(-/EmZO!POP(R_'h2T%rf\342[YkUtP"Z@/O-f'#$ghWYNDq\pO]AKcoHA2aQo;mo&8OT[
%MZ-'sB\5>*()&mP.2B+"%)GZ?*W3GZ]D8pV(Ap8Fkab%G]SrL"flB?I(^*dVIY=JWnR5.d`t!,G`OHfI9Nj#<qAPI3lq7\W;.)cL
%a>`+08Sh1Jid+<Ckdnl*lR8$8ABOFS#A:c-8Qjg5An:4Q@HkCWF5=a0-b<<!I0sl`9&GD9hkZR#6qipk@=io/IlFhjSt#$$aa1W`
%Zb?!6hq0N0P,(#41M4>/r5QW$\r6\O0C5E'Z\gBY@?NA"9"(:%bOXOpB)GQerY*b_+p5bb0>RAN^E[g*GX$YaK(!R8<"Ftu&2:8_
%Bj*u28A)nU8N"Olb`NZ"'*)+./0m.2oMpgQ;#$hQ-P]goelo-=XXD\)"djH.1AU<;ns<7b8Kp*TbF2]ZJ%!P\K3TC'jO/:e0V7_i
%<f[f=Ju\%/8%I(cEV=dXE?sCD."\*jFMN`0roPRC0mJ+>MOItnbp[=!?/LRG](8=_`*C<A_HPHWVVsr.=;^&,RbcJ@Q$.%O<cd^"
%p>n2D69d#1?B6Gf;Lf$>*A%'[`kj"V+pq@\=6=U^gZ428?c*<'L"BP(>DDnL@++m,5'FsKNXclProQ1CDe@%$XR(b/]q!SJ%'3:c
%8ERB#1TP<Nik:]Qh06DRf;2RTTuM*$jm%k!a[!-)JLr[rW(63QfdY,u4`$b6XB?c@f#X(5B?M28Z,fg$Md`Sbe)=WIXECDAdj@9p
%jh3@knut"_)g#4na]dcGROm3oN*$4>he'j=2%=XYQbBc%gC.0nA5(O>0BC"(fO*gf(HfqY4O"[)bniLe(HfqY4O"[)bniLe(HfqY
%4O"[)bniLe(HfqY4O%O9I)49Z_:*eJdsQJ$?gomZn2+MA2##pB:-3]hHP&,3A"##\)46jU<6Z=_ZUNGUnTE'a[AA"?&N2Gb7Su($
%ZVdNX=P?GQ;JY%&gaMHB=(<!#N?W;89XHVR%I6rc-&^:-8LCTu4%#e$EIoa1jfP5Fe%X!4QJ4_4TekR?Wcs,2DYkB`#upBleGDCe
%@!D7k+hMn':1CX%%85UP\tQdXE$+]R+j4qVA(EiP.M"2Rn9CTW&C%s5<^43HCsI$MS8<]EOqLI$]ZFYc!WBk;0s#r(7:tpI^\rP.
%g!YMW\l$B=f%O?$41=J3hXM-#*eO.BO0LN(O9GdS5a(SbcIdMJQ/eaCo/%EcM3'[b^\d"gfsrKQUuHaE!te[IKub1A@/N+Le,N>+
%4eIj:>L?bm)^ep6[Esht[a/BYRk:=K`uA(,`P:UHQbt<CNT/"@,WI0V)U>h3S/7K<fh$;3N8-O*]?>)dg"oH``s%OF+EhFA3TS8I
%^H?Je4+D\YL%5MUNC&V=;mu8=9[!0&/&4ePDV=jQ4]5#3/^4%?ZMQ@fVcJqIhGZTEdru>&:](C*+,oQuK^K5$^t[0s]9ULO+m6;c
%/1rSWgh*eHY;u"#YV(Un)g*d*>p9@EC>.&tG#3eb+U6O4D/m,qO)47=H\-HZ%c6%b]_<AFEHTIo`eu[K%en>+X0P!D/eO#.r(hYf
%`i2L@o_4]iWa)n;OOnBbbSGggIe%Ls(5R`pj8Qoe`VKn;C^XBdhHTu+><]dNpnbt`21GNC58%&q?=YeSZ.md$\)R?hU5N+JiYB>_
%0ZKp%mjJ(ic0_ZIisBY*Lh!^M5CTuR]4$o`I@92FOo0DNOXoXZ;_$B_V>HdG\Vg>M>+3d\[l5^5H("8PS+XH&??\'un)jTR\sdrZ
%_dRtG_u'#>.q7(M^_8oi"7L3]]tqN?_,!&BJT6+a*=c"+pmlg%jt.JM'#cY4]Ye/$@;hsL8`mYb%Yt#[>F*i]ZDp9OpL]1fhr!r&
%gDa1;Y'fHh.i5P,^HY6dK95?^+u9)u2P#roLE1L5*^5"1:uB*f_ep;NFkn@1Yj)=m4sSWkbW>);W,[f%T@)t7Q^noup'J^P5dEb.
%rT/57m7U^]'&C8i\Bjn5Xg.:e`;:>G5,7#e./4)X:Nnp]cZrHtWlB4u0$pX<o_]Ip5F8ladTl.qQmLmD=j"]][tTuc/s6!s8q0)d
%9K-G:?]GXM:uaT7i"O;nDN<'6=l;LtHj3[JW7ZiOU)PMh+YJir%uYD:']f<>1p`FkmGR;DL\:W(4&HMbj79):[(sj9^cr4Uh%Bdp
%^uU11%H@8/LlZR]Pf'Nn'lKs'0La1+M/<C71605/h(ubrEDuQp>Had!.gbp"Q90Q<79%:<9[&<1/t^><=1T3iYug"r'YEI5H%>S8
%mcL%^j9&SPqeWC$$fC^S3D3]R[m*N28<#\ifj$HXHH'0!5S(taI8'+'+MkU[n7!5Y?!qU$LtBZ=jF,dno&^VOObCL$(tk)1n*VrE
%?MnA2U6(PDlZeajm%CU@]%mQT>XsErqDou2rm[tC[N='R.96:dmjU)n/f&i\/$+Ri](G;(J73F$,u);JOVMj$+$ZX%I[6s)ooj#8
%]$E,^d=&ljq##t)!Yp+2N*nH64s<nKHP0Zfjf>#gH2Ypq04)D?nbS;BT$.MGkMJCNo^d"bGE%)rI/Da/J,89UGOM-<IJrrg%tFTV
%03@_J/,uBbmco>Gi-0Zrr9-6?_93Qp^\])PotTF_Ig+P5Y0tkHY5[s`h::-HNSj^"nY7H@-^Xi,9)hn<%58/M?/6+7kNgW'\4EMN
%h(@oDJ%`jVIt;Z=nH(6]]M\h;)Xp+KIn(Kig4RSUP%pWjpNZ.,V]TsE=20(hCsu\gm'68c5nDo.F(^uWc8C<T;[ldj4`m[_DQ70-
%r\1:a?_]S;\()G!KT\9:?DL4/GM?:k>Hknice+5!*D6doi1S-V!WqKW9eOhiT">C'mkii6XQRqg:%rng@8=6aG)7jEXIGgFS#-9=
%U[5HaqeMFA[nU,%90JcA#CSDdjVmSYfa`8@DL$/.0@?Vfc%$?$VWiO=eYR-3m5i7o>tGjs;U;6)6&2?;mQ8YYIidKaZ>+g1gXfO'
%AgH\bBtb"%aZeNN0@3VNp?`p4KDe$L,PUdbIB!P9/\-5mf[!O*oj"5PEVKWV1EO;-OZDOfHTZ`X>4oRIlC7!qISKK?U5sX"TYG6h
%Q0BgIFM#i'pha#`^?j:Hk7W3m,>I@el;QENFbV]WDb"fA0=s(+\O>>f26\[!_RA]RSRIFnRi_35';M-loM*d8pj+LGVSff[/T7+"
%oOS]2SkJ]Me%k*Lg_k&j2/D"cn3LD>1$Z;;h_Yd_:A&JQ["kCacLC4])>%JRURk-NFjn'1(JqoU:"S^&lZU&kqJt&GH7mT(:"O.`
%Ci5^pf4_&72Eg9I-:U.bkaMSXAt;QLc?6l3ajoc`&,L<S7m34&Ml-OB2if'Z7YFGH9U?scK1+&T)+79]s"/0McbKs=8=c+2UPc[#
%XXo=_q=f/';O:?<md<+^cpmdV>e'2jKbaFjM85F).4bmMgj2LZ[[1Q;HimF=N,*rdm8Hl-d<0ih_`L;)%OO%)8jj5/E[Z<?_)W=-
%"qM=bhJbht!I.MT@U]\W&0bdcn1YLRFU6N=DbGemU^nKc[u$3/Wi+u!SAdZkQ:Q=Xr+_^@Gd]Rei@TKOSQc*M;2%03/Cu!rPg%s7
%Aj]+\.tb$Kl(d8dTa$ToZG%WDFC8N%K'itEcYj\Y8egKLPoB.@4<E`j@GOH`d,WJ`:jqMHEqi2i,2)#.pas'>=`oN5^(+/#YNV-C
%FH,(RE1O1UhuB,35<]6kj=u@f!9ICn^V<5L4k"h4M0WfW;Hog[&_?_Mj[Yu<MjOk/8G_tUp6Wei@!*fO%9*ll6.So!#KIZ0f17R!
%=,O7FB\9R:#lGuq>s?ZF`'ime/lc)i'uVc,q4sbpr5Wl:h:nOoGkEf#f^LcXTI8Sf:equb2UG`QOm1^=>rfk]HC]06Jr`6?h+&e-
%T7cb=kk^<fIRaY145(lLY;$K47TMhJ^q82a%utlhcm.7<AGq%m,=HcGLnQ(?@eWW$K8UJb$5C()I/6T^#=dK^c),[b4i0F[-eML'
%SFS*))2(t@%S&-;kJ6'3APo!GV#okSG>Xo`LP`[Mh4%%!H:UT=e&>Z-/OURrjIX3q6Bhdt.<QfN^^G/S"25jK!bj%oPVDAsL%nE^
%F3@d7Ycg1V/#WUUZ4&c6$:P^Q.`B2UI@!q?Z`(gdbcATcZs[YmU-KoD*`KP:""Yk`RI9AXOasmD`q:Hh7-VKs*)Aphq\3stc]c;:
%X>;eU%O>oZ*_7RGHY"TjcVA4'0a#`J`e5M16>Z/N6;0h?b`R.Qd!M'ASRU.39$+BPBTe(:PSh+*g'8HjQr4td12HA0UME44@ru#e
%K3!7.7K.#j<(@!u`C$gE4_@gq_&6[Q%.(<e^uOWdF6`L.^#2X#HI.Fi:K?Ur9gYYfl\Lq6dhsGm0kR'p4NNqX17\e<bau@+&E1!Y
%23D:aNUp*3*h#a0b/rX@Q\bqZ/BA,KV\m&`E-U[NSBRXbnG)itlZgTqB2tiD50P%*Co1Ek`$_"k;,HBWoZX$A*%^SGoU8#J&MGgM
%6l4RDW)ZI7coeV;PIB[10crBV_Q`)E4bb)7=Nn?:Tq37sq(rpJWfG&T*<tRAg@q7iqOGN2'P`Om"J&MJZrBi$[Y=(dkVU%ajF4A]
%WVK]-KgB-m$Db0okjY<]FODlE@ca1l<j.4EQ!!N032o**6PjfE)sdQspJo.L9U7F+K\8jcSUbo#&n)KBfE8<!+=7]d:`Fq;mA)*j
%0f0[$UY/Q7Ub>m7+JSSW@OON!Z&7kokh\h9?+;(WhkL]++dUK/=@MFhbTBV1/Tf%M$Vk#e@hr=n>01710oM'J:URR717QeNED?<\
%?,j8':k1tUs+E6/dSme&L'7E(O7c<NYp"A%GrqmRhAg?1H9S]NZN*K'Z=9P0=;kZq2rqQgM<UC9@Rg@@lkpTS'BrmNDWant5oAj"
%6"2kn<1$47NqfFUAP6eE.4ET1;$/6sGgLPu#&pX'E)EG4"E5[(3Yc,p.a5fG@0@F.7;dLkiGqWi0^(0D%i/"7]M!!6_o(o1Wdqt`
%E8NROS77ZZ6&1g&ke;j)Z?Ba#6ml1BdOR+bS@h0$+V8KtA_94)1=7E&=$RQF&*^?EGrW&5&&b:@"DVm^Lic0j+(5$o<Z#N<R93r2
%/](U#b.t[!*A^jYGI8_s<JKUWbB\dj'6@!8#?;F7<U%?9PY4[ZO(HguZ4jqYWao1?9qqLGP'OAG4i\a?RAT-R)3'P/9jh<oLkq[2
%<X"DVCCO8S,Vk8oXs(%eaj&f>1D-KF:J=Jt5`(GQM_9V('C'r2=iMNnX)ta2%OXEK?ie-$.*i.U<J,:s+3;F\X([D47E!8f?O!?$
%KFs:f6l:ljVBHLnHPdI).Q,"LA<Bj'#at1WUD[mRQqpaM,..mD%Q$.Z/pT*B<:`818C`$Q4quV6-UQ'uG%/]k"AI-XMh`8;P-u@>
%Kn_&2^YA95*K6fG+?PAho2dDa(Dk&K!5b/h'u607LG3S1QTE:Png);WV<MRm3bB4=3p%KILWTK_b!u'#BaoGN_RhdG_?&$BD$*!r
%9V:KIFp_2A2ARXVq'k_hg9=T[GsLRF>`I]WpQ`Lcp0,R[`52M;g&.,`0rUUL4JS_IKGJUn>`Yfl]Q@@,dcY>8GC0LagrN91%F2$>
%eYB0KXXojXg47ZQF>gnl_bJ\jN+231StlTIO`i_unORr&22&!44#l!iiPPRCSgJQ-oD=Eo!lgpan>J3\`oj0-*QVqGaYf4_U(1JE
%F'RL"V!(!NpCO[1<g`LV<d@l/8a+B+M(o,kBE0dPd!8h":i-lZRd$nR@*KkdUc<]A1jE"H(-H,qQshF-dgjD>4O_F&a2CkmB;'A9
%rIq_h!J,G//HUAFIJ]:M?0t<q&VGqOe/QSSbZus,.hN$(V*gSuJea^HbDk[(3X:P(&6%e%.3gHY9HRdf3sC>QUVSBN%A:4!+bH@<
%mnCUZ1OV"%/&ZO5TY#.>HWqAlbAr_k8-"%6oR\tO>3Ke5+b<P'LR(@HIFs"VLrk1[HSX9QJ!@ln1(A13A<A#Zn=;&Im)`W;eV;eQ
%ekfA(LPNto`74o.'pBBejG4(.G#Dd,Kt$W`+S;JM$*Y:mfTEtfP&@&T(5LX+gQn?!)&=u,;%e_'0NgTj@3LQ2TqDWTOYr9O?4T*e
%'e?^:Bu9U8^0r4!SZ64nDAIqt>$#4!cS/a;c&5T/Ci$Xp-B]cm2&',sDk<i(BQKF\7]iJ%%3?;o&M%%i7h%FVjIF"2H)d+8BT0ql
%[HDDH_9RL/[6\Y+WO]FqVud]=2%7O:EZf/*B=Sraa"0W;Kb:;.OuNQ5gt:PZ&&U^A>pT10I2?@/\MCGS?6_Maagl=i,Yh<\]b&:0
%-lqoQLuaRi_.lfX2^9,bOau%;OXt0ABG)9l1Yc2!%.ThWGrZ<Sls::iRiAE/QrP(A2f!4h74F(q\Z5jOG#NXkj-.f2n9ltL@O$#P
%p"B\o4qgNFA0S)W;'kVB,^OH!qS5TGHgX37]6KRf#U6MC7,-E^F**Vc?o.PXh&$(I`Zk##+<bBL_0Fau4u$?.03m6l<aLf.?u_6M
%Q,D@C@12@:Kb+?k(llZg/4B&.bIgm5V-;Y:<eEaQ\laN)Po?YqSneV1V9>ep?HSp[(%\9[_k:jc%99=Of1M;0(QE757dtuh-'/%u
%ACd^(kfes@R&(YCD?"l<k@PSA1<qTd1drTXAl6=nLIqDUWtbtA9Ir;FB_WAu)lnreLpR;$^2mr<jbLmVZ>6":$=\oZ7qa%L*k>s$
%2F\EoV^pe!9a69220KS))=IA0DnY)^BTZpL(eWqooIH`eY^Nc&j`V[,MM\pgE@ZemS/'=4Q%uV#JS#^IU7TQGOk(`-OIY#N9BYmi
%n[OrlP2l^o_TTib@2ebOB&$6-JmMt^%!IfUBut9pF"m=#I-'6]q^Uc(b7oY@;p@Ekeq27_s72V3j+fNX2GFFEC["92@!7a]._=bR
%!P>!8dt,c<^DF$`b$AHl#efaaHR@!kkq4A;-Y8"3<S%]mZYUE*rLR^4/[Q]ZFKo3Rf8oiEOXH=4+aP&&e5ak+p?]Zkn3Zu3raSS&
%eNmAIFTrXLHW)0D*$<EMAIL>#XA8E(Pr=15B.e&rV<%rE,Rh%W-C8Sr+Uk<on/`kb&MfN*V_YM(=-9oYmP`\)F"
%Ggk0\^XJ`T4%XBc`'&1dTOGp7O`](ZOU\[reTBA5d5!"74GjE>r@/FpZ3(`/')tj="!*GR*"sp1/nVO9O>fR;YiQ0p;;j5X/F50T
%R(B4OFg;bHr1M[f@fV:hY9ITanJ;H&RBZr#LCF`Poc`FoZG-)rd"pe<nf"oGE\=),iQ\ol<i++%d!;LU(t`n^P.HYumb9?ik%LnH
%Zr[u"1(j;4M0/ZPjiOPh;%5XJ-'`+X0,;5Uq?Z`PW-TL7GE!,0>c2$:U(K=GXJkXP3H.\7/PXJp8Y?>S9MQVc1agiV=1\54`,gPt
%?e'q6g;%_K'2eQB<p<Kc[uf^YVAb>d;3TOl*,/\aA8l#m^_=Wh(K'`7%[P3D7%/BU%OT^-Z2$@I._JeJkZr-6+NCWG"c-pn"C_^s
%LIs*25#[adfi^`aNd=UalHMgCRKO*lOS:%Cs%/80?_+tQa98D/584'5VcD6%&]lXLNpKiK=eI2l+gG/bLDT%kP?M?ubJQbOS7h)P
%hZ>AG1;%GA5_B?bk9e'HF]#rs1<YC,UtCH5e^>f+hVuX9:)UM5+^HWiGYV8q!"mJLH?a_=,71HdO("q2?K`Tu-.REPV:?@3PLlOq
%\+$Lq_sRY)W(,T#&biF;H\b7.:*<3/_!6"LONC.K_P!BSr/kKcM6dQR[T7MY^q/u@8$_e7D39IqFrVM7@f9_k!,c^eb$A"ahF'NM
%Xs\U[m5T.-MP8)T.3,ua9TWWGXf*/0!<KWR@SqTKc/GlSG35KNK9%o4PY:",U<D71W5Forb(p]/9H:sAJoEPuc%qH[;IuR`_aufP
%b55"S2\m"2NfO6E?lUZ64-1ir+67m9nN:9s*iufScO[p(W\SA_S2l=on<9!"@po%REWdR^F5l+Sj!q(A\V`&X8%)e11O#T9@_sp0
%I8Ti;;UStin4NFBo*^9_(;$g\4'=CbNpuNm@\8I/an-u&B[APX@Je^MQ"sIW0t/V[>T7VaHonScG4Wl:q!_%s1GWP7Ni=r<e[#gb
%;c`6qD15USPA4^)C0ODQU/t0G`u0YH53:a8AdRAE[maIYpGTK--[fk:6jjtc,U^9?3f.^!1_.4h2FOAh(E)(c]@VdCN5iA7aD`WI
%C.&VhCtSDOF^pG.^\;3iU_WJhbqNlTbpa.J9Lfbc)-*8]naTr*8#o(H8ZF?n/!ti5i&A%1l_0j%#"EW;&LE>TX%h@bKKZVLcXM"@
%VE1F$8*"MTmkm7m>?>T]Bmgon^d\nmO_1f0SQH71:.VtM^!#C8Fa1pZYUc$j%o>eN8$rp.XQj$7=k59C6ncFf#G0#$A3/_1_$R;t
%L+;7D&6"nbaj$dblG\[MGa;ocS(IG-!R!k3<1I="8TGlXFVTr>H0KEXG]CmQNjE/l0G\sO,Z0c&$4U/X`_/F93#2RO,aFBu(R\RJ
%!`D.59!okI'Bi?59Yto(TYn#]!c7=[U[bSmh(9a,fV".UKIaQ&Zes\)V*:7HlYNDl1`$!+FJ#%IDC2>IKW^b>EU%dT.=7$'r6=7l
%rT]/&])(7_CMG#?.FMp-.j&[F)B`WQ6ltGHNa=B[,?i/ahP1j4_%"Y(+UX2S!?fYnJ!/q2#u].N,MO%q<tIdTKQT`G-)aD6Tk$;W
%>Z@YdW?ZYdH[.[a\u]f[_-^Aej?=\m7Slsd3P2URJ-E#(lBb(P@TTW:8ST`?P.8J`-Fadd(/9([G>j?K:,XZ>>Hn0cVX`"9Y,.Fj
%?Sob'?S)c;E1(*=3_/%7":3VBF.,cY<D[k<(ef\.;ERJ5Y6c]$MsCMHNU3_>8],gqSJ](7%TJ,B,noB/77U1HpbEDfbtdBSB?:u:
%+l<F;k:>liGe1C3*JQ0p7N?TY(_e;-0lBd6fL?K:4B]I]aiWuE2WgQ9-g!^6$nfQ6M'25WqKcncSV?4<Qt3VffM,%]'+VL.b6-_:
%C`-sIG]R.6*JbZU`_=b-b4m=%McStg#ik=/C5>K86We,[VV8T#J-#?Z3Pk#?`Cs_dWF0V*R(N.%LnTeHOr@#g1LCPEE3YD44G^*:
%[)g+te^2u+NiZ8tb\445MSk7h!:ngBKL[^7)i*_/fK_oYQV,;G?@4ra0d)*LR>30M85<\IlJ!SYTn-ECftd,9dUBt]UCf,W""Q&p
%[lMHJNuGSG6^;BrfS?':Jn@90DMA]e>9!)aMS_>F@"/5A*0l;cB3L,V_'GmcI?<q)4Q4LoQ](WPK2tRP.@+6E#R$Kif?h[+=9r7G
%]npCIO9r]d?-u(M&u#Ng'$*k)m-;E^.C_FIL"Ht#F:o+>]+-TNADUgZ">h9X3mA5kg6-9r.dJ1#/"^^CFjbV*Cu\8^`K.,1UFc&7
%`br!V4Ka/KXjH4R$W!!J@kVAtih`e?),HRRFNCeC$TMA=`!gM0KkZW0jFVB7X?+cT-[6-QA;Ra!E(bAZ0?"i?EA`Y1\k]\A_f+QR
%CGnAX[C@+E)_7P4S_;IJ'B,-D1"<(hPF*?S3&bQn2_R%MC,U<_;YahN]eI&?GDh'+Qrq<!lPY=mI0^HB&r.Z_0W?[\49IBt&;NM1
%m$8*kBV;m7`FMP.58.nT-[dIE<Te.;b!dTN!FJrjH\%G23sKj+UI*`&(_g5"2`ClND]%,*$&:V<=7tOl(j.1B:s2/)aC@%=8,tqg
%?.GH#+qIVn#J9!#g/_2d/7&kYR&fkFJj%+^c:W9oM[U++W!/o@0p7.7`hZYhga6Al80t"9&.&!+cDK:tJ9P'aR?@@ra:U2L)%(6e
%m+O;rL-UH,)pA&GFZqOa+Z/lY$1JMf$*;^R-,C+en624,;`%u"!Kf\dR$a>n:_9sF&t<AjB-#\g3XhL`1Bj,=GEH_raSf'%j"4_M
%2aJ918&4%C"nHaI7NI,[fF/hm,cRTt`+Dqo=%4=5:PjSPq(+_.b+Yt)M.GUS:K]c\\0Nn^(?9fX6["-$QmC:*Q>7EIX_Y9^N[1bp
%1ZW'N'i>6h\ZIq8$[&uKd@b%X")4Uo[SH/mWnj`pI[5Q7k3\WBCuE\rE/Q^!X@]n:3iMDj&qqdH;"S-lX#Zn*LVqT1%F;<"?]!Wq
%j4L_pU.O-+[kKI.A1(gs^pi6pZPF<l[2Tku&&=iQTf=QAfG-0IdGMC$hYct9Thmc?Zq0L.fJ0&!2M4@'rD)Ug@3oUH#qK&^0nWSD
%==7Rq/W,KA#(\0G]dn,b?(.Sg`3+DK>Bu((q]0Ne+_qO@VhGZAjK/rpWA$pp6rX[[cH/_t:bCD59hZA!)rE*XGt"a]H8%DM+lPu"
%%f1^'Z.R*^LeYaE,!g8UP?C=>6o,8G"<WJ2r^U`8ZEH?dF2(00'rkac1&^/F]Ot#emt;"Q<(k/oJt</%.oE'u,a^mo&U6;JGFg"B
%;+aRjg`q6akV3k#U<&j6oYQpi.]AIWU,.$#K4BEd)f1s8E9&L4StO?DM.`%mB6Qra>;ssCc9[Xj)Qj=k'mi,=FMUG`@4HRToHXaU
%0l,>CI$[_?e"0:V/M-bT.D8,,Llq%<o\(l,'<u7<[qJ1"*CW@'JsDZ,1(K%1qOf%$@?5^#3C(=o%]6a7]&8j>lP2"-2(MKc`B<?6
%PPHp[^n=A#A/mH=$/EZR*j'D0';,R`"u@)!CU'AIMY>hi-[Jhh1a)SCk_'-%SQQ)Hm.?f]lJ>uo#J4GB+Mm+Ig_7!6/)<MoE2<fi
%U7u%&:!6LB-8br"n#^jT3ki5bIOR0WlK`c1[\?GCR[&nVc,k381V"+2Wm%p$@37LcQ&/sKTp)ui%>-;ArGG>*S>np>=a/>L:6'@h
%ZYd?q9JM/1Z9`Rea>GGCCtX!Je=t;K4[H'iFof$R>2Q([hAlkr+D2l1@HpgcD]C$[ERmFXUuXVjoL.o4Sohsth<?ps-gbZGpS<*i
%4)K8cSCa\WEoP7[VP6?H@G;HlYC"\$HZj&f_%6Yu3WhRm?.eKplp[K'CsuE(V<+c3)&&ibHBK-AC@<2\S\:b1[5:]Z:@,Pe"Ho0U
%[:>pp=Vbd:(ne0NEa'r_m#+>o2>I(4Ps^3`oj"5$T\uJe"V*-K.&+$WZ>3Do+1Js7&J#[)8u.3m@-9ii\(@qJU1eP(:6`*2=92m;
%L!_d"?*^FI3"LckYA3LH`>,K.)$>oE$*R`:et&23<dd%2ol&E<co:8YZ?+eMGEo,p'cd`/RUa4/=2GX[g$hg_6L.LRZr2NY^ET/d
%QSf:7RgMY@3!:rpJh<00L^d05.[8M22N2>ZN/A$"k$cUg-5aYq"/,g@83X=f_<;SF`2^)@ob]\')"N^H#9/SPfP43@La:m%8S_-`
%S=+%o)qb74/50uV9=i'Zb0$`7M5D30e$-!C>T[O'Q]++.p,4s$_]uDu0GY[>Iam-sJfI.J1&Za=bXd@P=0Z["eL]qFLTjG\F>i#L
%TBiK#I4iu9@gofn.:e%o(7'`u.7#CMQ&l:r$>)2(d(Uf"0JYM!=naV!JV!?XkQiAKIA5muk[)1>((6n1Hl*7n<5X7HQtus-juccP
%Kt_Ac66"p4FKB,nG2qTV'SVL*M@5^oq[(_F!Oum6EfojcpaTl]0^>[K`7E5WoRJp>3Luk)"F^fD?R^&J!@![_NXZLo(1ph\Ls]9U
%<gGEldkOT;Xf4eAWG07<e5un87O=B)HLrs'?01`.4Ycf57FWmb:(N=*$4!7D4d@F;1D+eR(<\fY,bOEs7'%P*mUcdl-<#G_RODpC
%m:cq%EU4KQlfaS'@*g-5Y>Zo,nrj\L8=GZkOi>O@S,2<=`ZX1@$mWjLZ(=2kVr[7BR@1Oorg67MFs<(qctmK'iepWJ[bR?c2JJB_
%b>WjO9;d<+7Mh=rVGmY[_s5VV,CuQ)gYmZrKVT.rg2Mj@cQh1upgVpdW3SA0CSPP^T,6`2!hXSYa3eF4F99Fd/Po?1;"aoZaD22/
%qp,Z/cd>E$I2Y6l"#CnTHC,>R7:/+)$.Nd?FhH@^l*P2a:'kHF-$og57u^M887+,/(BqeJ$;>/kJT2Nq_"Od9)/^5^Ba8$/]_fgI
%*iG5-&oWHs5=t<rh;^;PJq0$XKT]EZ9(+Q;nFS>tI'69BL6?Iq-&X#VAA2jgLTPVQNp,TkbQ_u5f$,__(BmUBap1GpBKbZPi'8-r
%I>qdSKlc9n,s@!f*(UP#o/G9XmPm=\YZh<rPNXbAS,j"o4CnqoLp>/3QDP[0b/Z\(%cM2JfVaZDNcV?1S8%%n7@OQOOAH,gK1(eA
%KmB)`C?l;`jMQ(!NtFcbZ5bYHkd,Am1Og?7,Ef0_l-4`U_D-'I`7]nRJ:f?`^m/@brl-f@*heG*&Ue2?nLA5>O:\0BEe"9fhSsUK
%%8,2-nUhr$[O2K*nf/o#2D%;c"V#F0?DI-@.'mn%%Yn+o#?$mj)[]A!OKd*ZI@KR_%<d96Aj-E()hDLu,[ZUS=2N0:)eBG:di-I`
%Ag]#3MkYH-dKc6gQn+VMW=/(=elhZ?E9"ouN%1E_<O&,T!>H1p&93ZLij\,gYA]6EN=p'i,^]eV9&otK5D!5L"+U*I9*8Ul=KUO0
%1q50CO+n0JotG"_5f1AmAu.3%$k5I3[$em'#$JME8O@=:JS1@V.*e3)8pEeA"$+*VlnXts6V#PQdYgX^'HUlT5^>=lA;]M8,X`Yq
%9B4\:hLJ?'((YHR"+!9/#cM[-9FbU$Xs.Z54B`NH7\YpRV_D3`:!"qS.6mrG&./u=3eg;Q(E"4`pCDKN+,>OH1O-gqB214p+I'_H
%j'-Ed6PeW.R@&XN<3->aH&GRNo[[U4[3XfAOG;@@iQ9hL@D&rcJnU=3Ftngb5l1+l2S(NG_dmX#Q=%3kH+=jC!F@cI;0-?:+C.(l
%b&Qe#0nEKf(:p'K8<afbh;n0\bZsND'@mII;GB3QBO2Cjg$D0oW_+3ZA\4D7g)Njb#/K",ePOJ9N^hNWYs&WL)2@c<[3jXDRZ=7#
%=-?i<BmK!&0ub7kc-YjHoq8T4$`a_B@;CjB'>;]E<:ICN8M]j[aen&HLQ<dM;M[_QEI\KmI86jjLa:-FBs)`@2BKE3:rg&0]i//O
%b-\Fp>?Ek&jKsUtm1oaP\BMs,n]"dXNesJ!l_(0YB-G+:qLGIEN[1r07*\"q7Cn1*k*2U^<MGrSd2&Y5q]r>=E_+8Bd-B<Va=-I]
%/LLs:B[O\r82N/e>Hh-`s"4MJ$g,*_7"(AZRW>Q7,0+4Z!cm]k;tGS4JK,2`it,A,(T9icAVMWU"_CQoE)]FUWVDIG>`293Cl&pl
%.0`jG%ad=-<T/3KNm2<(J$ae59OHjpIPNZBP7[G%8lA$5'Ir;%W1"IQ(Idn.p`d#mdO\"/5g'^dY4-4"`)Bd/k<:_@3AMmBI\!7W
%>issB)7#olE[WQIEh$Lgm$9MCn6aQ_'XVt^6r>k#HGq^"=d/S=iAd3V;B,n.7*4G*-tbfE'4":eeNbBK_L3<<?8RP%&N9Q]c6<G^
%fO`)Vc<)ut%uOn6Fk,j9DUaa__MncC<7Q,MJ4)l'@Ltkk$o06',meUer5-3u/A=e2[a`P#B/'a+2G^*[YBD($L54`:/*<g#f3U(=
%qlYV.+fD\,r4<)$X62L$*M9VsM6FNec2dGi=*C\7:+#`?6S9W1!bPRbk.f#qrkV!NSPopH&Tj02A.Th\m:,K2n)*WDF.c(MK(`\-
%"Xc3Z6*%]"cTbB%I-?aUqPh_[nT;V':ihLEB`eX&2;o@+9V@tkDaDKXBkkcIbXB"6!M.+2)lAXl?3L4&q1&lt+PDl3B"&[D+F2`7
%V<J\0#YjE^XYQACS]A6OW]3Ujq.X^d%\)Nb@X_J&2f0U+c/k,,#XRK0JGNPLVdTc;53&,cj4NLW:c->T>#bomUt+\cQi%,,]NX8J
%;)<G0Asn(hMep5D.UG]15Tj]PODkqi`1h/Q54e&L^pVKc=Qi6ki'Wjl!:4qBB,a<a[NHTfn>G5NNf(q(](rI\i:*8I`6d>WC!17t
%#t8d3:DRr3j4K2$!3Lc3flY*9S`Be0mr;+OA`"gfj.^Hr"O5aXA%6/5kjT<Zb+b"dIGmPh]R!G-T>cN?ZbeFG#@Q#&>I*;>^Nn6C
%.Jq'3n&ZbCf17F:COt<*@%m\%g\1GR8`&q_]B1ftPSEJr%sdJsUp088NlrX"W"<(MM1j:FWCcp4<KT*LHNZoJP.__17(:d1QlrIu
%720Y]1s&"rgZObHP]DYF<Ni74N]Pc;>;m4J.*J@TaHI8#/KRpHWC[SWajWk(5ojA4<Y66)]7]1Cc+LPSMT^GAW6.<Ier>B]'pJ0/
%-9usq8\V7$Gnof[X,Oa.1FDn*Xa)]@G>h6pasC*5?2JA0-Xk+:Kt.oJ0hC!WeRTKWE8H8%92TFf?"e5"-<`7cZ8383faGj.1/V>I
%Kb%=h>Qc>uVEAk^6(bg<gD3:lMBslYG,@q>N@'E<-GJLcj;7((VDn1/V@@dZC3n6n9iOcJI#5RRH;jiW]'F@@C3n6n9f,RoJ!%H]
%H;jk"PbKtCc%%J2b9jfl`Y[l<dsLbKVEJ-e2bahZe-*,5f']@fS)2XZRO3`mE]D8n-.,pn`80p<VQP3$CXua[F9MoMUlq*r*1pOO
%auh6RK8tb)oZ17]BMDo]eNg"3abQ"WQ5X`E"[k2>VU"qh5No+n)CUWelYRAr*?-De84'Z%Ru`3#YT1QXX=F$'B2>n_f,e[^]HN[Z
%IaiFq53hs]\uNurT/TK&nTn-aANJAJl0=p[R`<Q#G(8F<O0#$lR^bbPH8\J.qPIsu'>8)u*Ife\S%(kQH8\J.qPIuQZ'q/Z4\(rY
%R^c%hH8\J.qPIuI$H]SfhLHi'\i+Pb53gfX4m7At2ppctg]]jj0OZX\HTs8c8[a9f!D,?#`XXRI&4t-E$S@CaT`6prr7`h0.[#Bm
%al*GPal,]PQft1cBc9\\4`?fbCt%8u4m7TJN!%2>6&_`KqlCV5jIg_[-*d&Z>;WcE:`9h/rh=4Ad-2/eF1ib#Uj:p\:4i&a)Xb9!
%W/Dm2;33(0eeen!5pp_]2D3D1`6%#MA4FDt;NpNGEe`C6T;E$<PYm$nUlc:+U)+Sf%:#X,!\<0O`C,TP,He._q7S&@k)LXQf>8r#
%mTFBGYa+4*d[eC9Oe%V]Y.!mqT244t8f*D^q0FB=\d3_t/*UNJ]&ga%d']H>e!3YYeoY`A9QY'=9VkF$Jm9efRB>UfSN9pL%^+9l
%o;L---[b4$-2QiIVXoLoMlcLW]+V\TeZ0?J'1!msR(FKBfVl_GGVWk9!On]0SK[j,PG?)k-SsrZ*g:AA)5?#JOfnhfRI%s.$VEh#
%/cfgjN.%DC)b\q/;WsTFNYXREN-O1MFArQU&KT09/C]gu(*-\pX*pC&3nZU`L0\rO]R[V1>F[bW\uT_mEBc!4_%'"X(cFsf>M1:P
%=:0s9i41nsPE#q0b'%!`;co9c?A-]_%^Yk$Uq6etl_S$'Q7*+&=W0&dq:8,H':N0`DKDhHd1ZNlk3O?`#^aL;;KSJm[&(i4dJN_9
%a5ZK[PZm]:6_5THG,!(:3RdU6G5b'FhE%t%U0kT%A_Ip3R[gr"aZsGJ8@"@4r^Ga71fRHd2\nhh'G3]h<\3ba*o,u@A/L0!=Moh2
%C^i5YXBL=-Qud$$>V2+S"Q-mJHn:9C#+D9Ik]2_J\X(_[S1U!0\jVDl%#Q]8Kac:KM(\H5pkS1='%dM1*F:9lKsQarcDtNeV*,$4
%Zc=*6@B):@N.U9q8\kSh3N6_V!3jIe"E_IP6%.3ANh"RGjV5Hu9nESur'E#?MV$(@iGe.*-TGDV2MoU9EJFpsJqO4Q51_O&r5d#W
%22s!B\84$%<u$jP4S&5i0X+oTY(#rq\!juA2$b8PIqHua)\skB0Klo-W\m'r1*D,!VY\Ut%-#r@^LAI$h&N8?Z9HA_m86>E02or^
%W.9^,9csk&Es0MmK@:UfL\+"7K+0@VT.!d3)k&M9&p\pP9b'cH^<(np5>Y7B>E"c/o&?+FW`cm3*T9Y\`db`d"mDE+Z5278K,sh7
%\eiA:DWl1@%FsM3S1VB@GYdt16li)CS7*upBM[3W`^I;]$T<o$BU_tmb4c#^:Qnc.jBSdlRj[s+*_D`E_@hJqNV<0WpMGi[%*S0l
%OhK9tPXXX<,LCgJR9#-6>8i?]6^O]LnmTj#gt7m"1[J;8gQD>)2.G#ZERgg)Rfn=4S6^G!dJ]#q,[nXGEEAAhdKLnT%a[#K9+GR5
%G;Hcr^RMf$(ST3Z5Re0t/&;t:[:l!JPa&Jm?Kf?16XrQTb$L92n(i]kL8clsO^'L3jH\0Tp7/97K%O1sS>`<$^aETt/&!Q@VHNga
%?,Xpn.4t\J'i[P[Xst*&P,t8E.*.9XM>Wq*3[,d(Z!f6\Y]uMH$h%c4O24"'Pa2*fhX%!2NUq!N9FQ7#3$Sp#ki-EW4D>7a;[G!l
%V\=JenD*_Jm\IiZ/c6aD1uFP)oJI2)U5Q4K3;-l,`'KnHR%L+@1LIqNR:B3uo9Jq2?k'C)S$'?m>O,_',b,6p1WD;L]JZ0;]9AFD
%:?&4MKcqcC@S@1G&.AV,N*<Rr;J,bfN2%bek\Fror[QZ.B%-8'76KXdn(LA+P%4TU13.qUW6mVdQar]pL+I!nM'c@igr4Vo`1m6q
%am$/!gZZ*-j]X7lW*,A3BN"W2'Fa3nHMmmcLZ4BV>CLOR@hsTYehH=Z(iENacCgM*Em2Z`QBnP$Y(`ftIu8JjMae(hS/PrE=fhYP
%[cTTWE7<h%)+h2pn-=pF[a$:U/W%cPkl^R\lCP@=Ee=u+!(oldnW?/dUs7o<_r`4T22a6*fFZ./>hG3K^2?Lh65.0F01#kV\0YVf
%BXED"p'Rjb""#6AEs4$?b>PKl$Z$_^<g1J"CoM6NAb5':70JU,LT;AYWH00.:Z:\m[Lm#M=(+ih,cWa/ng/6I'Y`q52,.G.3O&m&
%`I2]_b+XWT5<i3#UsnYqaUZ1J^Wome',0;(7$sJ3FbHO8&Y`KGg_?#@)7$U:AO.[UJ@\4@35a5WK8_jbYpg;RK7#34LD;q%iHNAr
%@XSNuDP5M6mMpjhfRR2mFt117ZSDIWR1@r\HBW;09_;%B:n4MP!BP6.Ui?QHj\,qGpl+'(6qIur%'KbY5_cre9jU+14MkT\_E0dX
%3tmfe=XP>TAoa0s?:&l'2?AJo3eio.3WuXH::fo3(:L%3+n.nKKj@oHZ]'Q03@Qqp^F+2#%E??n:'i$m=!\&<D[Dg-M84o]2+m&:
%D/dX3cYl[N7[bibTF=*:L<2P6r_k1t-_Wq7.6VaL#[FV`B]Tr<`,cj'i`mmlL=^Au'[K0S1;hN!-+`_Z0ZgE*&9^pmV3;*tm!:*9
%Vgtc?E:t4D?C\-oOZ$c:T0"j<G_o0Y)4c_V?"\T*eYnl_=&f_+12_NiF,@Ukd72.?A^cL<^mh2+IG"a>^:`t_1VIR*=^g>[m=O1G
%$%,34$Ld3-Kar.\s6_h1;<S7?T=1q@?@RBNqq\`2qW61Mn".:B/_&hP5JDBUV#TpCY?sMBp&+-VpnPmtl@9/>hu,u^-ZN5eb#.uj
%p\:rl"[Dr9d/J'NY'tQeO8nRL^4n<>mT7c;"D?J3^7rA/kCoO5`c-$%L.8m]Od"sU;g39t]>RR_ld<CQ_#I4Ra2DMKEnl28Ld6NC
%HeDi]&R_7;oWP2P?n,!6cqjQ?GOFpk;L]#t!(#:gVCVO0\GO6dl@,H@qO<=P_'EJ?W#u\Wi'@jk`l@h<edsM[(Y;@M@j^3_^^oV0
%e\X,jQ=2=qmPk1Nmr_+/1H1*IJ05@M'WL.b4R._nQUY-fjDF"I]m-$W&j37P%T70bF@mKI/`n+R`S;=R`Kee1Z'\&Pd`9aEP&W!'
%mp"&t?/_7&"&*gb(AR^TANX5'\hX+U"pso$EWn=T1!C[J'L\jhA4"4O7msqX8#d#)Sg71qUe:]J]I7m=r6rOdlf4>/3%TA2@$I97
%$3;Sq%CO82;`6h:3LpdbE:I[e[>Cl7Iauhq7D82bOPltG#]B*)-7ne[dQ?S#iA#(1:NS^&Gg2?I'Sm%-ofCnt3f'KO;5^g7)\*RO
%CsD15b;C\9$tunX@S9Fq,p_NmqLFSMfd8RIF4F82,?`248tf3L]><YKVCoqDiKcfTWRTY%4A:nE&W9HU&sPi1]&414`>^#BWX+R0
%jF3(Kc&FYh)rOkn-[Xm6VnN_=&(@,iMb&H)$Fsf*l!4OWY]ibdis$0:cO*NO#SR#6"$qE]F+g8]Zk1Ym<\0/t6nD>^=Mn?(jd"4q
%_F2TN@Ptd;WtWB66Q@uLEm'uOM?d']"Q$BuWXr[>ng%dGd?0bf.9.\?:iZ%G-+r)=m.3,;%H]19-X/dfWX/r,(1J`]fqeKU:Xos7
%AjWJDhNO!#c_S>g#-X9HQjFq,?P83/:E^XMW.fuK"VK"<"`tHjHP_!iOF!e5,0q(<rIBaU$-l.81^]WpU?,Q%H:FhNcoq7mQq/oA
%IQ0jb)BD@:9NdPOo"ukVo>=liL=#OV4,)R[j?k-Nbo"qKAM9,Mq&%;2*0r!eVRKlr/ht++n!o";ka:$\%PkjrM&_V>1GPOd_O]WX
%\t(+9^+>\(\:6rF0B:mW[/VnQn:eHE`>?`dQ^([K':uDR4D>6-_dGMPaUfY2V-F,+fkhPFg6-!%oR\cg,"['kbq[[`,u-OGVfYPT
%8'dBkR4FsQM-,2m=1^>N7Ooj/VsTlT[&7FfjQ^TA\Kjk$9u0Na\eA?r=N@]j=!Q*A"k)5"a"l&-)!VUYcbr\e%pH;0bCH[D5-H9D
%C@C9N[4uAii6=9s2NeGDYAlTIb!O03H;'"./OGD!(H"?\WmZ_3'/<R:f^hG^FCEBbi.6Q!#@:(Z.?T6rM,)[th'nm]#)LPQDBa\Z
%+dEU/@3V*oLhIW(VRJ)eoAPWbarEu0RhNA67;I%?Kl_%W$,,?SGh+_e@AE2tWh`ZBJf;nP_uW:lSDs"JL;M@V8eA_>qM*:ITH0md
%-U\r+35&Rj/Wh;uJ;e(&j%E9/#Vg?/O`(:cl%Dl^5T]^ID(TZQl;$LraX(=G.A4u[b38&dApUhaLu.fMQ?g"RYJeR&DHl^HDU]Es
%'tWA]gLfkJ:WgIh0;/P3'Wn,\EEVhUZq=h?/%I]I)81B.ifmH2Lm@(f**'B$B281B".;s=."GiG%$bAk"@]ihoka%Q3>.]67hmd3
%T1EIuHaYA`,HuBk#I\6Mdmjq4k_f(Qe`'t*KejJX+!A1qnKL+S`H]B5b=(DBES<[r!*de4"51)8(!1kO7.7GU0^D$d.DYG:OtX'a
%l'Z>81N/=P?m#'!2EFo3Q_"c9/iVpQOm&,^?^SsB-A%-30Mf)S!dl3?I6,AB8Z8PE;lsAAi;K/R8&NGqTd>soZ2:L)6C-';*p5t[
%2`#.T\Pj]+&Tb>uY6jjHkXOtB>)\l*Y=\[e.F9n>a15OO5o[e!Ad[s/Oa@FlW8")GG(E'(7a".4[0+ftiZBq/Ycpo?"W$F(Uf8P4
%p67-UecAj9Osu?^Ln7aBDB,2s7$diDCs'Q&/L#n+Sp%"hHWc?Y,S5>_<1.lPrt$oA=G#IZ?Ui:MiY`e11Eh?k2>J#%o""rk)dhb[
%?MWlI4.UKI#M^s@\n!"hc)tj+BiC7jN%7Yuib$(qZ-U!PAa@jNoUDEs)3)d]g3h`-4u!d!As!/uqEdUL\(qrfcd@)49ASG44[lYm
%.A%O,199'?a^c^U<G=nB"W>&(Mm1AUbEd`#R2?mdpQZ8/+nB?+[>k]U$qBE2Y3KP,[`q(RSkPsL&>"KFV]):C8n)p.@T:m:/:L+r
%[QdT<G;cOhA`4cV--6fg`)rQa7V0o5CT/Z>MBTs<B#5kAEPWGY&<Y<_9PhQInc"b8T/K^P"c<ta@$+,6ZG;3b6.\DGWM/'Q5l6e;
%NXYBOmo/p*;J+Pl5u1BJ*?qpoX9X;diOK1;[:A'cmY!BRl%W4?Miq59/7XAJ0B0%8HV'OC\kD#b%@<e^qOhFkir]W!CMqB'CFor0
%jARS`\t_AdHotsV7L]L7?olJXR+One8fEXXiZ%^KX,CJ'H(^U*L<.ONF[_O(.TYV7?'l[$mZ3l,N7V$I3h#-_AR`n6!!g%h?.N1s
%qQTEgf:![dRm%6X+S3j?r0j!Ijd"6T#Y>)^'4TLI@jkEJ-[dCQ?>Cu^f<pHkf'9XI@7QZ]AaqGn3N61baOoIPh00^>-Q*4^#jTB'
%ZD(IpZ<,3aM1qd^o$#];YQ3@.&4r`V=V4S-0Y)=i.nr!u<%ZNt@D:_QISIC#&s,UJ[dJSZCHQ6hgjP#^X;"#^)0($DmoS1CM[Vs-
%4"G<<Nd)_ic9u-n_:Zo965THRDU5V$8_V3k'U(a_?*dkXTF*fL/unV+RK4*kefIfka62R"nXRBqLf%k8>j^*(CYrlk_R*%N9^6gU
%l#nK$A<!;eAHLm'pQ/LoFR5;?k<j@VAjEpE(m*eCd9.<I+GWF"66tnN'.5N=Yj\)tB&b3jp6(=rS;Ku.fn4UNUG'7CiMkD9,hI>3
%i@.g0`$_<#SnYiCQS"8d/*ns(Elq[";k6XiA2@Zs*G/gj?AKd%XOVLEWG%a&&&[[#81,Pr48Mq,8:3],'2tIY63XVBW?:o#`IN\:
%1[%AmTq@1X:V8*5+;?+k9En5a7FO.-C]$TmF["td)eX<F1Ta'QBQ@,o5'k/?K9h<:i"ahBjC%FZ'qs:I/`[VEI,tEj(lk/h<=5e$
%3@`mb@ZeSo4:P%eSK*u!WbOC<n[Ib'LanLL4eJ>U5t0CFISqYkdtNd+2_f+Aj2"jE<]";3jbmPn0LeWXb0.[T.PXo)@HdAd6WZqR
%<.e:uQ8L5*^^OaYQU=fK;PU17%aAsrPR\'horH`)jl5s',/.YD8sJWeg6qO,@Cs`pg`"JXoGg:"kD1+,Qq^qd&P(g>e\p7[QV*[j
%gF9[2OrcMtMloo:2p7Aj=!"2Xd]nQc<utA(8:1'a"ZrY^,fVc:D2^\J:D99u*[YLX$^`;@LS"X"pq`Q=W1]OK>e8(r;5=C&r_fJW
%$:7FMTM49(VBb@uLeUCUe2(a=U2]P/nIAF_'ms:1G>$6&N"q`A];_4L/)X[680X.=eTm[%^!-&XN+/s$^bi_B$PGAYAliq6<@ej9
%VSbqQ-mFfdDk,XX)<b)@1@8(JJs61j/M5#V5%HAtAQAnD26l=C-$rsAW^UI4.VlP%6tOKX7thgFFjQ<XQAFFbj0-[e&$/'0,#=N0
%b*OJI`H(V]D73lfillN2c`/I.1[],MA@rs1,trT!&nd$5FsrofMPcZh$]Hl5ea%73EE(k2&8d0j?fT.@Z>li.fl14sS2<u+b\-3O
%%stSETTdUa!7P6>1'f(2@.(*Y3A:`4`KQ6BPA>#U#\R.aO>S=AIsF8kKn\8hHCMI704#tS<12%5-?uomM9pbR1-OCiR/0:7:JJDi
%#I4oAe#nP8MlnMs\cF-T*)-Q-(TV1OOP$q#0<#c"]_aN3C>qF7]g^Sl/#A0'4A5>&S^Ifi]3n;Ld1fR-"kh>\ITc9a?rZ@iD/;OH
%5Y)S?1"2[Sdt'$RLE1e)lAH1t))3C&D-3qoVAlg0H:,Ulf(bG'+&%!(?<p6=@5,\pQAsO6b2]3<U/QYhVE:)d-kCt,ns%O<CGrqT
%07'(_aAIaU-46kdf8(5V*'NcC-q#[$WZR(TPDO=Zc!JZGBLSDL_Kb;(T3"Ju7p$X+@8(=i'C!0=M9(,K*(aJb6kk\W)0o*Q>#^'o
%[:2C`s.?t`l`Ke=dY.&upcn8fJGm;aSbpjHZHIEd#P8)@h6"M$+AaW3@[i:QMIfrhlbAD?M+hfe[qq>U+a(d\*]bcH^:?_Qq?2NB
%Bd0h/26Hp[ElT;E=WNS$RPN!tQ@)G6iTDX45/R&'N>j&jI*<_i)8NgFLan5oLjNiXiU91\[L:L#-caeC-*"jadn!j!^'g],J2S+9
%W8U,Q,sSu%&!"nF9S0MX;?)L1hFjVJ=%O8TTl54+a-B36,$-OgY?uX\@DdB:W0nd*.a7;Z)!46pOoh@D1(NF`U<@=EFHr-t1!CHi
%Eje.^;$9A":r1)cq`W]&:^Q,deVi[ij,iQe-40-4'2G1a.O$1hG_MNlInE:4&*pp#?S*4j#(Ab+-:TfdB[fDm0:3A8VCs?G6<)QN
%(+]pL#R7/m`q%t"9mfL@HbD4g)&'sajr,Sdol;]O7m)COQ0<W!;->F^F'+7%XG7p7TSZV3plR3J)^l[AU60#A2^3A45^+,e8?Yh;
%^b4$"M&&NM9FP6dTk'">;6irO!(eu&SF?7"#]-?9-sgu;l_s8F,O7Z2M(]P%%"#)8"=)pQ.$_lnUK?ZqakM\l6R?JP(u9UcqX+f$
%DBjL+eGd:am3UsMLrL=<8?u/P[9H"8a[agQ?IK;]b'G`N>HJhA\^]Rfd)Y;J(tR[%l.h!ujbaOs`CQkp?6k,[L`3G:-Blm@n2OT@
%)O%+QDIJ)m_p?46e?,Ri&2;OO?Iu%$1[Hhf*"5M?*A;/6,nlOm&<RqWYL#XWE2b?tPoelZaPNVG4(ENC>`0IoR`TMZf>:\31p>2K
%"K?Qm+H+<s\pC0p(PM2PNbD<qkVC$VBhPN#OIYdG#6ai;E'jr)XibQM'B]j7ckk8RqBT$@c<&H::-&N5/"n9tNbDF1SAd[&4lhXm
%,UjihitBNU`@Q#oYDe#4K;@UV7Z3R-&H5chJ2UTtGnr@Km6TjU1^cgr!dCOuB&QGWVZ[-S7T[DY):27s/0GXg)V5+seff:''PiYY
%4eVAJWZdV&*OoZ:&]@nLjPI.]5Of8jOB-16]dPUNP\Ulq6tj@AOAU8@au;9>bIK<s#'ekYP>r1acl=t;SU<Grppe8sP=et8%X=M!
%!oiStYhkD+S3$!R@`*(RPP6G[=aNs.l>0=F@9ri_5f>+q(=,:_TOf#7SM/i_0]Y*!(EK)GVKXGG6PQ\%_O*doJTYD\<$5U*^>JPY
%L*n_.V_4.LjeT6])g$FXPKF.O)S$Dh\PKloo+Yu/<&'O*OK\D!dU@h8)Xk[69Ts74.T$g/LhKfpD@=Y2g3++\?>g9]N_[oaaJbm`
%?VYp*&9L@!B4A&g7,fZriBg5`KJ&Dq.XFoU0Gj8pkOct^0S\>?LS1;/&A:LK]9ca>-4s&Fi6:ZgIVEU1P!No.pBNDF*DL$7&m\Q-
%<YN/NFI61lL`!FCe;.]6-Q7aYiKb/NEiZ;Qf"mo\c%/^IQ`S2CIbl\!TcY4Z9iXL,It^GE4!BL=JVe-M$+&j`,<_OgVoDIAJ2DD:
%Ek$Hn'rQ+@T;."`262Ji1o?m[]r.e^LU3XSDHu8i8ao;lYPO&j!L3nFE9fXB\k;QRF]^c$RYD(o6GqcI'e-Bk4X4bn5",uG^ndp6
%!H*nMNG0$o?=r$5BLXL"%[Kc73=JmN7:_HsM'tcVqoM6_b06ZC_*u7`%=N1$*gRl.5WLF@,"1lcN3F']0K.[).[[?c)GW\t9@0.>
%,u7_nP%d9O;14k2hC:/kbU`T2_Q=Je0bHQ",MCfhkQL^PYs-^mR&Pt.#@0d@*mBc4VC=gs-SkF.Vl5S@HQI8=kE)NZ&u(^R>Sfg#
%Pt.=TK[!U0eUClQ;N9*CkU+5.=n^1qZ=Dq>E5JofI]9=!^aSI_%op(9+\J*)@X=^J=6Su*YK+Q3\hB7rJ")_+d>e^.8gYn/,^IV:
%WUnoIq-9Jc!-Egs-.++tVU3'd3":'Ae#VT#h)QnTSFs%&J#T]q6kuj[6.lWQkT$f:U.He%<l^o#\5*MM!<ud2r3<T.ClcF:dD!rM
%919pJ6]($<E^]Oj7aXaN,$%PP1;fq[G.Q%3%uIHWO/TR_4c$atIge+_7,tgplBYkP6.lr!bb-0>E^8)O)S@:EcI5f?[j5)2Tpt?o
%W,KjT#>hgq2*WmGlB:,3KK@m-Yq_d&/@VWP]YgSn4)V=Ca:](6)')#K7"i+7.ZbgdM!6YYF]"9TM5n(.;8STc60'0.fn8a>j'h#Y
%2)&-Dma#?*ktj%3H;gC?4d_12,VG*[QWfh7OQX7O),-K)Mdm#iXY&@&$hoDFC0>uB_5c-+A?-5BN'iaF&Y0<P_.jXe!Yq^I]/2YQ
%bN`0.d\-5c`it^f/cWdX7-/4+.I."C?VVH.ISL=o%d3Y==j.1QMN>jBguCP-3hDb60*`/;"0oRYG\X#ZrI25>G&%?o"<CHu0UL<n
%noL6gIsCS0ZcBh']UKWbEH`$&ApAj9-)g"dbdGt(#=ID@L,:jQAPV.qKrA(-LaG#8?.+:[/oR)C3jZD$r;$](82_%lHV:]#g`W4?
%NT"r">%q<T`QluI9J/t%/<O_a&.ipCaKoYLORsS<VQf^>7Gf9.e1sAeC@W:)QO-:D`HT>t6,'8.;%aj9Q_pbWD':hK`1CXHkj.LS
%jV#*6)3tOtEYbq?3>lhV^=_1KJP4jA*sXD`7)^7@kJ=6.6rtTs58t&lFfN:ATe;!EA3?6cF?Euf\+UFe+@OcDl&.?OYm)0=)@)',
%;D&kjofs_\Ot$T]9;D-c#LZ]pY!>PA+Xarj!E!ck/9CB8g!<<F]PXE<(/.4X8%De68@T)bU-6#$A]3*0^DjWL7G/7sf+!t1du=s&
%ST@!rlm*4Gj0GIW&=Ha[5-"jlkQN#95#=%BYY)/Lff+m3iWALR7tKtIlm\\NS@9_+o2`MKfnHq@5rDJ8BGf#4A*mA4)</s?%0,YQ
%-de-XEJYBAgGM7A9lCuiTk+YW,%nJhY2hD^:p;V#ZY>st%DH:h*HOJ,9Mf7fR947%/%6gf%07;3Mu@?V$nQ"PVkULCYH[dN3Mf24
%9#I`-^i\W;GfB'8<#lPokc@CbC%R\omY/>ia7fXD/W_kUGXgNne+@[=dt:4,gVY%G\'`&C^%qG4OI]BK5a.27#+#aWZ8PJ*6dCLb
%<eE(>PtQJoBIZl?#cWl%)$:Mn*Mm5e*0LSN1,'/XMb,W&d[_ZQQta=/X_Itj2:gD##N-7>(12%@6clY>::?67Lg2KP>H`r`Q8'q/
%A$!KBi&pB48NKlkRtmE!3ggKolO)?e.(\bMg"gtoEHh!S(04!!I?f$s)M292\)u41b.`GtVr+L-4^&&</Z;<:g?`TA8@/rWe4n'C
%%NJMNj$BW?.k5[^8SIDj^@"/ueXG.1dSrH!Q6DuAOM5nb/02r+.*SsD2-eIAq3jV9(:33-V,E-=m^^:a8bAEaM0F:X@nIJ;8::#]
%#dYYq%2Q!4T@4L3aeJ!?&pZi?\(Ba+&fdo>$UfqY93oT=EFW^e#rQ%7$R8DQlBb(8QZ2X2Q^4kbOdDEio*$<_Dj:lreM:o0]F]"C
%Z\,D*aHjBr`UkWJ\Gr(pYA"5po<n#@Ms:/)kr),L9@XYtrM"Nk]j&@SYae[T:sMCq,Y`=IYg$s$!s,Sn]O208,K-*m^O8:JM(;t/
%1R'r&]<9^Y+sJn*j9CYdK#S^lYVgn@SXDl[2EA#<<Mnh0!@Mss+Dep4nUc81GtjLA^6nm*l(D/a9nWJ9jc@&F5_G.KeRTcD/o;d>
%if3QR(f8U\\2hT[j"OsfOh%9+P(\*8&`Rr&adATL!-7K=/NMqXf'OVi?5W#qHjh$J1&BsaBT*9e!d]Z+??=B^9W7Grco=H?f:j&j
%04X.]_X`_fSHF[V?K-"l`t0Md?pj.r.Y<Gs1?K-FK8=A%0Gr##Y/lDih4hrr"Tj3m.T+*m](sI50'4cIYf[hs(=&o8Jp1%Im.$0U
%!<A/E\K[+=_K`61fVf$L/BN&c;dUhRl8]XI+_fjV072-?RABfub#/hI&cu=<V(K_U6[dI-YD5Ld8I\GsVk!qVCru%AF9`+NV7$@s
%U"gE?;JaAL.7cf[Bi9;gChOM681"RS?UH)7S6%"J5dQ0D"`e2XM[X=^/S@M8&7VlEU>?49EY^GSfr%P7!7?>gXeO*B_35^gVFFk[
%,o;ncK#,I[+p=]!&HgRYi5M*8DIn_U&_8#1@lC5,K,(Ac6I8ek!0\sbN&k!*+=W>OHFti[D`$#Vl(kK'A'ho3;B;.+6Pg*/`ViEQ
%/OO09L!N6mUM?)>afe<:BF-pOnTRpL&[R:;p?arCKrP7q;%^2@B-8(EUk,s3gY!k]&]:nI?([PoN]I4/,Sh2[D94%Y$Io^4MWdt>
%VbUtUBJ4fc%XuKF.b3RP_'s\[=R*,^X4f%m`(&tR1aJF5P`hO@X]:K:m.[ND1U`sjqAf-Y;c4o)S6$j9GAVV]mm2j_i$9118qZPq
%al>sh9He!W/$>ombMM>WQ]4'2II;lT:(X.M!jEYB0bNR[`k*pK7V_2AZXD.;";Ppm2<L7c,NXJecpLIE?u\i#$I!1r&fCsjTc+o/
%@^HgU=dpGQZc?1(H30j%lt9?pM@c9qXDm$T#YbDn4.al/d[Po(:n_t6:K9h!Qng%5i9X-Oq)k'd#1+UB27&=p>pS=gOB,+`;k_G]
%>e2UFM(b%b*Wst5Ok:5'6ErN`ECWE>DPq=H^d@e@BtS7]F(42[[(9k<&ejL>!AKE*7lr+s>=(C%"M@iqq%\ffTjH:CY-=tX6S+%D
%/-5%,RpkjX^;d7!'HD--Q_/'m3=#?fkDC.?1U!Lm!(&QQc'aP_NQ$!!E>_oOFTDBV23&Ou)rp]r/dh9E1&j[Y1G;j/#$>BDACm1b
%TM;lG=:YCmU?F57NC?3K8;_<XJsH;7_5s*S@n$Gdh.I0M@$?3e(DXkc&2AIG)2=Q$&gKZsP%KK:JY[YKGZ_fG@i[K3U1B30/JX3&
%Q4@q$BKjq5kUl_GLa`6h0iiC2N(mkA+\f?&J@AFaXa2j\W5],sa=>=Njp8WL&=+=@bXrGBE+pk^PDhmIpeS`PTD+<-e`l*b5M&P3
%'aARo;O(L2qgIc)4bo4@K:WO@P/W!a[P$Pk`>P>,R#cJ'$$\#?nl(hg8HS)F8AS]GU_*'^jp@_K%kN;SI9PPN13N_f=>1C!&QRO/
%MlMYB\$"h+S/Y3/".[-Q9O^[H.0^V$K207,j-`2agOu_sM[YPq%E:l#U"=FHl<FH4Z3jZdpQo-8Lk<gAiBq.&ZQN?>FSfOPk''j=
%CcTp?9]h%r\`.]LQSdGBV$?Rt7J&;*8kSQVRNp?2$Vm\ohu^c-Oi^&&il2iE&L@K,!<Y.0!Uu8Y6pm>?U*X>"EE,m[6<;6pFr5gf
%U80D"94IR5qTtTUJqf'eB$8Y2li];("(ZJD^\:1/N>(a=man"CDtDb`+3G^d3:OVs%ttSj,]Tk<fOGX/NcR%$0[o\S&\GNl8VPa+
%[d<k.VH6,:Alk\D'O^V^c[Ec5EeT2`klE&!F7Ua_8Z#d\c:1Mc\35dTSZ':&*=.WSFCYuYPc!\,FDJag!0&WkO0R#je-H8\>!HRS
%+``6-ZK)hf!:dU>->F?DVii&2qNO`5M1B,8^k8]OH+#[D,3)ADXT671a_Xu<-!&r..VZN1WG2ogM?`]Ce`l]DO.p7#O??&'HOXGC
%IEDK\ihI]*7Q%bU1l.;/Xdhm-c:2ObB+DL!1iR29>67:pgDnfB5oR"D3]IJ*T$9-!5AJk:4kC0PWZCM89soll6Y?X+9iFa;QAE;J
%J/m(8KPAlCKsLKKCI7+IoW(@-9B$tWMs6MI-:tk@hIOU,9G<DiBl\PmAj.P`?PR"u*RksCAHJ72\>-*r;e.kOO'"(HpcSDl*=\8'
%`/>nB8R^\Y/O2M4h4>l=/E:58pEg1hoeG[WjG<9X*f",mK/DD1A7a>Z9fPt*nQH2Q#7MMDD]7-3N%QXh<%#s'A<0,6Jb9R_JS"2n
%L*!@,*(1RNFfYVkc$W:LX-.ECRF+bG\A#Nd-E(Jr-k\Q[r)Q0Im.f0h(UFFL#k)CpA'Kb+YJIp;bQN).,fVMDdK8Ch9eW-#][Ti3
%LMgC8C)i@kM/ZSN+OuaO+J7lI'%/a@B=D(i`?u'3?=FFQJ&)YWS*?8M'`f)qMA#7FP'(8^@IgO<(7cR&+a,Zo#VPaWOYD*hJqZ53
%P:)siV"iMa2kb!DWX-E`eT-,P<_a5r-e<dqU\,ON6X<7l=L<h-WCZJjnpNsn@g\ruk[UW5;7M6$^f?S]B\22"$?I.;7"OpVeZ]V(
%[&Cacb6Ger6UVCF&*@H#*.,qe%&-0p&*ae)V,X"FZLh/r#SM_;6m0Rk2$fh\pMp+oAoAq_4)tC@6frhuQ5iqL"a>JQU&b)?3:s.&
%#iA&oc:P75WBFA!/$6?++&NP,1BeZfai$5'H.kP>22]VT"h;;3R-.PmOF^hC$`c.Hkf*nd`1YFVoO)*-a\8/go!U9O`rf,Q1$D?k
%r$_ZU7Wb@*!^u\;E"p767'%Zq4F:s:eck+)$/j[Y;H;I!U'Y7<G^f(Bo2*#'o2nX6PaMt$8eWR9)%52V"TeTq@f_rb_D;><!hHS)
%/SO:poD,R)p4?<G]qFmJ,e0nYO611?XE&K9+-YmgL.fNHo)se@^iNs87U-GV*COO-DMHCqcKc7i?*K,,6)+'`,J4o9]be(X?`Y\.
%q;ldD8JrYFL=i`40IolXG490Ka6LjDVd?X<rC^*j8-2dE`%1p3%-0N(QC%/>d^\4F3?_$8n6/3o?s"UVkW709PZ0rU&6d\H8YIa[
%G!)-bfR]YS&ns[f4.tQ]E,.@@`jiPXK(Vsa.V2^'%e1Kr)1`-s7T_k7e.AUJC4idJT/4Z&.h5T+$TK,c]7\+t@5G'EJ1mOa?9B^h
%XNWktbE&ZMH`/Lbhn!DDEaD@80RYEsfd%E7G<5=hK8A#^M&YJT9Hc"oDs=']BlJ34,iN6D'jta_),8mE<(L)p=&N5l5MZ_b8Z)*#
%SRsQDhpC]f`%e*hM_bD@crGe_=X8-aeX>KaQddnV>u?.NF:-hl*K;K,;;l4nhA+3(8O.QO?:$/(Pmn22P4J(Q#2Y"LWWQRZdNLld
%7>\9,+9uZ3'>7dq3K%KBBd-Q!(n2.i,cJ.i#.-PjS`Tr/`/J%$EcgaM3#E[UXZ5j3gk)5.X)?jYa<IuEibh5D"QLPdm^jp5c7Ytd
%QZh2P<@HTXT:pJP(FM":r%FeD/WK2'gA&aXCCrJJ4V#=UQ`S],+DVhH9aBen8JTc-F<#,!<\giuR^6R3g&_=?RZKNJ#+_D&?a'Xe
%-)cN/1gEgY&;-3h,[ulk$qsoX,Ns):#U`N^;Aqppe=:8gW!TIQY23up!1g-(_@f1-'r3@eF8-oW-FE?XYeH9"m^k`ZihR212J=[>
%`?=GG!^Kna200P\4.Zjo_Ti-rGt0rPm+R\FOsa1&![<n'fb5QE0R86JI_jYcd!LB6+RL=S@[2h']#.j5R^,6E0-a.9#OS0VbU]Mj
%6B;CfDL(^5SKh[e(hPP$msm2di[c;5j>:-bl\PmbR?hN(rh:N7e(,7j?$?u1B?$Si;NGmNcZ]'>3Odf!,#CBiS<m3=9o,`&S?08j
%`SO1F^6"]C;<*%W(d?L_G*of!fFcq=GG*:p(0*dAW5:PUDi$%P7A#5A2(QgRG)ZA.eQAuG^Zc8#>-M$;7#,_</&Ms)dFKIDUXu<J
%aIu_FK&q8jEJ#6O3UReC60B[`Fjd8qK;P<2`#N#hL(L+CL,Rgl_("2aJ.N#-A^$lWhms)7%K/mU",8)Di1@&%*HWA4Snhee`Po8&
%Yj9cQTXS-^%X_Z(^UgqrIbk+XVqF.KkDiY=Jr6qJo[2;o66:D/.6^m*_1TNg9(27\!83@:M#odLC5F,Y4ACc%51dfe8l-eCD,ueD
%plKVp,Rr*]^oeQ\(eqJ>\G-f'Leh<2qfXE<i.AYt"t[XS=X&6->cJXu7uWlaX?tnkX0,_LfMuhJ0A]3D1]#V[!!Vf'Z!RrpdGs-6
%&!i*0gMUaSAV(]QnqmX`_[`O#-AE%p9jUm@4XOckT%Ir9_mqR8MGTW@P^2E(C@&Br"D6EpeRA38FYJFlfTr8U^E\u]nt%r6[,@:Y
%*eM*l"Vl1"b<^Ecn2#s8LPTbE9"-t/9J=stOk$HLS?a&'M)O[61=Vlo^02DN/GUmO\A<h^hYj*8(e#'a@Vl,:.<"3Rk>B33g0Y/R
%(UbL]&EasF4UE$pLIjlL*Miqml"P6m$=kYH2"oRKKP&"6l^VAjAk@]JY@p2JL/Y1JDA>YgHf-WW3I&am`F\5+ZmTj:+(I#>/t7t:
%lpr9.%TjIY(;O-YJ>oK]GK'u**dtAS97U:/oi9o4WX80,<:m5:0tb:.HAb`7Q9cgaLhCl6O2+Fo>5X)_*W[HV4F_ZJ$qu?!20P_d
%a]8/P!$:+_:C%^[@Q%/o5pJK%/Z;5+l;r;JE0<XiWbD&QWA])j#J@h*6!chUpf5q>dGAIP==L?6)!r'8"HnKNa-2&:4R7/g\HK"e
%6oE%4@;6Fi/]I??1*EP-P6,6_-I%H\7F%&b9aJlcN(cLsk]tj/?sIh)d#m<>^6*q15s5c]o3;[dbtOBM)<H4Qg)AZ\glACnMQgpT
%GFd;N*aOFZLJapd04mN^0,##5Kn<l&cP]2,XjY+&N46Mk".%0He64>]p5ENoqL*>+Fs(*;8]b3t`SRBr5Zc?qG@3R,j]:*I%bScN
%qO8I!E)s+d.n_cGK/LNe[UVuKIQ?p:&Q(?V=\BC7Y+>Wr98>U]jWO"0%,idaK4lrs2c08tM*/R>22__p>r>qg(QUieLdol2A&@P^
%"$;APm=g.=^stAb*Jgl/9'fIa"qT.%RFMJN^k8l6&T)re4[!PL3!OfDA2qN;1YukH-R>MXJ;YT$U`S^OE88oi$9XIVkRPO]*i^lo
%bMAK/=`Hd9>r)801T)k&*!CUnVVGsZ>N[e!.Ql>!_Ka=W71JG*Ko&FTUP8!P3=.<BY=)*0>qeKNQEhQ8[,l#c?n<@8csd5j8/%3H
%/HCES<qK38TM=s(,P[B["G<d5[2!`/<jC<sQ$`Su9k`0cohb0Ef@<Jo),DoD>rTRbd:I<Rq,4#-?5XD>j/d7UUP5IT;&aYnHC?V;
%pNRE&OEI<e<"!FIoT9>c-3DYLb8ju@h#g^=c7Jl33Y8=u6f(dt[,dVDh/^LcnJZ\:@4f-FYD#>[d`bOc4#Fl)D9OHWf(4-T$<XX+
%5\GPh,dp5eC3;Y-?qF#;DXOL^9ThOn6H.Nmim]R^*LO%"rB>E"i].Z=L^4G`oD*N@Z:R]:3fhL9T<P08p]g5ee=\43)Vu/J>$gDV
%,>u<R^7<=;#pI^G11Z1BMbqq"_?2eP<-otV%WbU1U2/<K7NrfVN94A"QX\f_U*C;fJe3aJ"B]W>OGDef[;Ek`k_c1a.!Q[XBaD#H
%>5e0=M%eT/7H'8;AYFns'-*QH+cAQrgk1MV=qBIVK@J:?9B5qUprR,u(]6T:"/?lO(;q?`'F*5FO^9j;WF[Rs(aFH3&'[KS'5$HA
%_nu_T'I$^#gaoVH4UAVrap);Rn/05)N)902V^HdaBWOu]SWEndn>&l26u3,6A5g*_i!f5kqd*-NL%R&CC+=GM/,0e*4.N.pn;UX0
%*;Te^"s;<Q&3g7"0#0,M-JFbQjrknUWgIg;gMht7QL[`YAT!X!doA8CUXf#<EMAIL>-=sb7P_'2("#gSf7*KQ$8r^q2Z"nmp4
%F6g7RDe4oII]d7\86(^i(A!TW2=\HQ(a:5Df!+M!LdXc\0/blfp.baO=eJq`?M0f-i\FUoGo)N-a2=4]'l!:`<9bIrJSn&\1s4cG
%S8#pC1W4AC(Zu82<.i^7_\0+a7TGgD>%6OhM]U><i:^c'p#od%2`2G9Lko*Zi!eur0)d'f&ed!f02!<jhZ[%_Vd3.G;$&8Tgk?rT
%I9m*cFCgO6S&L'X<Nn"qYX,hs>)_YY66BZ@G0\i'BK6W183=Yd'L@'fZ"K!Qck6\RXiuQTM26:C[qs/+>AK?AmQcnXZc%%n&K*6)
%m#\M\h0eUk<S0V^IBuK@jtZ'*=tCkS::_TuZQIrcO?BOB/!18i"h)CO]$R59j*lQ0<Yq0>jQ&SZ%(fm'$p;"8K8n@O@CF[o<!WfW
%a&#fP2p[8*WiUgj3R<.g/NM40i6*hhK&PLT=Z0mfL=$XmenK,C10lggTdVH(UPAh%.3dQo_eQLpYA+_@L<pZc\Q1dAfgM?6/DEoE
%4gg&SZ??:CrLbTg(r'#?)UrSkDL&X[<_g,W.R+l5@DTaX!`e>!";j#YXf\p!\H]BR%k.]f7'T\@AGNHTnL"lr$FiZbA@`Zq4Dp.u
%*TI@h?qO)A,11Ia*KeU-6CaG$://<bEYT^DjXGT/@)@*l/n9maS\r?F=``/6/HlH:DW6B$0-OWH)bJ!1)IF\;A@_;S7`8#LV37J$
%^7XOG=O%0+J*&XG*.JgpW7qNreb&R^E_";m1!u'(0OSZm2=4mg-:Gq]=g_kB>[MN1cOLShjU*f`F,3r$AT&_Ue9R:R`FKdi8n//M
%QitBfVHHnQdi$h0NGD"`.eE:Bd%_m7ZJfuT4#3M^?$hO:OOUO=c"9cS!<-]b;:o3OqP<%$':FIk#7&'Zf^.-mg2/V8Y>S,/JdaD/
%&X?`@P$Vtd!=UacNEtrK7.b`aBD$@1r$Ho.#REAl:nbEXZaNg[<!9JX7aOgUe?f[VQn:%q#utk3$6*Vi>6do)bRuD%f;a2F_)&d(
%OqaVUQ:4XB0?s_;(0s'WSS?]'64_pZ]P_IsF^i!UCU!maC9Tr,3X?<ra()d!%l,VrdTMR:Jji5DWWmfWSb_IVTF>)iZII<T"b.((
%g>5_kWZ-P7R7Qk='#H1BcD8\4m6Feh3USdjUB-JM15e$<Pj"t)],\g^f!CkZ"h^pi?>(fg0mSW,/_[OJDJK0F=_i>i7E0j,s0@9%
%V^.LDr`&;4U*B>Jg,%f:g=(Z:/Z<TB5%d`!MiFjAp4<cn;J3._U_!jFQ6*%oS+\;*-fh.=ar/b.W[4']8iQdD@UMNp>7*N?&5a's
%MDnWtZ".tnB!MEa&VOEm>XCYf`AK=U_I7uA\!TgfTM!&6kMV8.d$nDpVh1Z47-kstosfk.6NtN%6)7a[`R:f\NY53hSM:)F./D=L
%XJm`g6:=lT@"+#U/aH:C`+jQ3PIBddKVo;0EbuOnKo!8>P6@Q!Kano<$lI_sHcTelWFjK:RLKIUcod1kgooufFBIKPU:Ze[!.&&7
%:DdZ@0ATeBOMe*:/6e5C#ggiCF_SVtJ,sUo:d'mE,a`TNQ3G!$<lp4X.'t2IFmFng9K0O9W.O%=&Vgh,f!AhG7DXX%aohhXP4pNX
%5QK=j$R@L`]&hX-6C\%lMD"B`rJ<NlIFYt)RVcf$FIRq&ka@U">t%FJrWT7Yl^b3pC"EO_7#;CUjV=Ut.c!d8=m3,s)k-m?-TPsF
%LckjR%H]El"W@BSI9BkbW"r[oA&n9Q."%_P[1n'*$S\r\2cl&$3t*UH^@`nV8MTRD9-LIA'pQ?n9_p4hlD[;<Ca*W`!Q3r"ko-E$
%P:]gFiZ?r86I4(n$'I]pEl7B^@b[p!TUo1H=-c7]/pIr2N=aW&9Q6;DHs$u!!*Gcr$PL,S9!GoV+pG$i&uN_^(#k.E<rKUG"b;i+
%?dJ2)_([JsLUpP)qPu.X0IfX-Wb+M+'dFPEW1%N7$s@RbJFjV6Lr5U>*p:S*n[lhGV?.6E`)N:`5+u>[hB&:;_96J=8XW\ck]mA4
%9R4o,fGkUVKGpTk\Xs%487:s/HfgX'C]_p=S@-(8%83S_?<hj#J[gqJTVna-Id[9'C,]k00<loW,3'u-=c"2=IM6I,@=CU_XY4LC
%.CeuL-Y7F'g:5Ya6cVbk=OO*.5^XG%)1r!nJ+(Q*dRClZ6pWW;Q5(a1:Q*WNTe`=1\Nl<6$s\\MGThd4,4>.5Y2d*#"]0/Q>:GNT
%J4#VY7dTN(E(pRAZ!sAr9jQ2$V*ce%$?u]J,#!`J.Eccg9N`ocL+Z0,!ns1`9_73p\Cc^tXqYP1<"2;_bt1f(-s,S2ao@BIE9\4s
%g1gdE+]^NoGeBW0L2Y%F&E>NhW$l;$^Dc[5QeF<N+Jo;%26LbRUr7ca>O=.3/&934kS2-jVunuR0:,kP4PYf+i;?%pG#ASp,jN=*
%V4-no6j_'QGg+'*JF*K\6lE%,U7VH3gfVj#RVGSR?C1k6<;D?a9^.iCh%?<IUDZX[O!N4"A?MlLMCO/0iZ>W8gEA(+BdUM5B"-S'
%I-C.H*qZR"aum"E"_K,DH,EjF"lku@76U<QFJ9XOU:!.r3+bbS@=;5LjHtNc)GW[t`/1J;*HZD)0RF=X9o#Ti/P;bulp3:K"@a%8
%61VLqhP2O526mC<B"V4]*MdO)"!7&]qIbN"[Rp[[<BrneLcM`D,`dWj+%Vq>k]r/*RJ&h]5OA64L8D,B)'T0QFu_5O(eiQ5,mtEp
%)2,#\m1r]iN<Upt]qRA:e^G>#7&E4Tc<9Rr-DEAB/<"K2+Nh<1`\ekZ7ghM^#o0Q;89gNrP!5agor\j3JlrE9&I)6V11eR2rD0ek
%<tTS.aR5sb,6RY'%"sN"6PHo@&oOf1B:_#gA>MT`bA#;P30]r"FF%i*E_3^\=0c9_'Pdr3W#=G"7PQb@OG;>.]\9N(0sE3[Y!O,>
%K788h+kL0,1QdPd['%@WZB\cDF1p/ubMlIJ%Osg9f/#3UHXn65'+n5Do;t[@Q34#BL0U09lt:SJa&]MnTG!TN5p7?@@T@<%8#I6*
%5Z8VR"DGB$35VLp$8.tq+@tPDER>]%#"Fb$j4D;DOZ:iX&`o-iN^uq1+I_,cg.Rs%XF'r:Z!V+]a.R2\%iDf:G1;:7Y:2J,-XBDD
%oH:0MNE/DuQuZFJ\Qcbte_AO>:e*c+P04-JX6*?p?uM@NI%e!Lq89d9k/i2W>=@$bEDUo;'>b$SAa>8$&W@j#SY$q1\e(#7nSlJ!
%(TW1P7i(#RCg(0t!4S)HC;T#?E(3nsPGt=GVH^Fm-dbP'P#j@BlqakL:T?5+Z(G0OopZ,KEW*Y0*n1=['p$N"0Ejju0OPF"9fVGs
%<EMAIL>[A"j;pb"9)<dAq0S<A^V6&neO]?u$3d0`bdjFtrLRaKom/92.lPfKG4j@-M'h]05$mo-N2B*nc>$(0KQVP(:'j;bbW
%Q645B/RSoUqS+>XRHBTC9T(D4nY2<CU*uUH7>(h.O,06j7:WG;&RXg%J?0qX:s>:D.p1N0^f3G#$"t>!4"BAZ5/(ok#Z\>a%2e3J
%4@54;6+h2c\r'_20iH^m0Fd,e$t"Zn/R(u``)&g!d"3A[!U]69:F82I_fMJ*kDgC*<,@DgoC`7(1t$@llL\&%-qd9:Mc;V(kT"3*
%\pIF2FUY()TP"tmk(Jj<EA]RRZi\F4Dt8hiYEFVe2d&b?8;[G6h'0B[m+As@1Kq?kSKK;\cICHlXI>-g#oNnU8Q^:PaCq6[+P"mQ
%3#(h;p2h))CK?TCbrhPSjMJ!#"@?(dBf^dn,";;C*qr4Q20afV3'KDpFD,eP3]"HJ%h:-)^,8M6dQ3B8/.Ij5;=#3t](?Q@!!4lX
%bBrd0.[V"DW];I)QLuQ/[:i^B-l-=;"s\Jg;aq7ROQHTBe2b4!pPP*eG"gYD-,C3<3D'l`<WDqI`ToN^H1%Tk/^Cq*6u69I5`V3b
%/2#mV.MbW)HcbcJF7Z`.;*f@W.P:i)q9V7Z#U3p']L!.8192H;+snlI'+@p4no,mll?9ENb(R08$DiNJ_0J_d!b\D(W%GWN+:$2,
%?UR8EkJT@D_jk-flMu+?U1j).Pu;KSEp4?k)B`mRW.cBr<Ij>DZoiA%,t+0P1t*peU!RVK[P$2fU_WfjZ>!$raTa%VM6iB.SeL+o
%3K#_)E6FZf3M&-o!QtW=F6'?^(n*g0B8BA^<(l?f>KMOA,/)L(kTEIEGT&<`.3dFQ[':o'P=;3FI*1s->Xn9dc4]#XD"MHGCDF+9
%1?]ac(SEUsC_bfEY?Cs'%IJU]aS=r2jg5<i6ps>C2-<gs3ad),AV2]>P3JLXU*dp]/jlog(s]/.d#es!Ud1*1]g/YJ"K%i#mQ$-"
%W')t*O3eZo1i#G`SA15"Z0f0BTp=K-eiR,3o7"!.m/Z?HZteU#ZS?c]p7#Y+FOul=hAZC6_uPdG)/du[QGeBf?u0L/l[7b1C;QGe
%Wl:#\U9nTRI*/bt9eN8B=)@0l2:QS;1YnGYOn"T/J32?pMT!Oo?kT`]8u[gfL@$\$@@Kd*CQUdq:-9G1\afK/$!7^aSW(`ffe"Cn
%Og.-X[S9*H't##r&@`VRO),(jbEO9?V@XKRM&UdQ]q4g@WSZ4DSMKjO;FG<CAP5(\ccos_$nk/P-*^Q,6(tZ=Ngu]PJsE"%Q0!tO
%"G;ALY\;uCU_4sNZIED2<63L,>.m3mW>:g0*h$;FD95OrlTl+.L<Q]PKSe>dd@/car;QnF'fub>Ze\)t#\"QW\j<j/Fu5F?oDupl
%_76S%@a%73GDN3.;0bH<$:%S3P4NK1-m1IT/j3r8PRa+79*"QM;>%;@WYel2&co?(^s^jA&f*8PO-q<CZ=i#D,C`:]#M)En4HHri
%]+[)W0t&4:q-Z$2F;Pd/.H?Ih.&8a.Eca;)281nO*OFVS$#(:o!?5bN4tHnM6d?-A+tB^[PXZ/j5/P=mKJ>=D)g5->9*ilPp=T5Y
%ZG^em]&HNJ1om9=?j>#T<DY$7J@EXdM7GUAfKpfX2?X4Ejs(=?-j#B,Slu9OffBteDATm=+Yuufh'*?5Hc.B&paF5(/35CS-1jV:
%a!9NVI9r^T@s#1p2TK^9%_uVTDPRG,]G2RJHLTdA-<5&*\'P2_WUIdDXEYo9X[$#T`RJA)&fnjeM5Y0^Q\6^O3*ELIDnqYk^_$ic
%EIrHM/9ELP[I0M>'8g9*L)B-Va=_@&[`k0mGtU$s7EM8J>Pb)CYS,)IU](As(+*!YC(&C,L,Qo"*t"B?Q>pKO,?B_3e2l0h5JSo8
%.ps;^8o&H?7mI`kA4#r:TXYS2MJ."sG_8k%r9:#p0Jp[^YF/EX<FW:T^MO-2N=FuH(J'Vae\-s&>(s;!*b.Y![+Z_]X)n1G7EolT
%INn)jU]%%:Zdk#c)S^)HYo:@aBYo?Yl55giPVO1%^(b6ZOrZ-iCYp4b%*iRLAeIQRMD;N=[B`@De_9."R\V5Q>XN3fWk9R*'aAK\
%g-'^YHUseuKl7/heZYYTZJ%s]"$u=N):!Oa,f^36%LV_D'Nrb\KF%pML8n?\QlFJEP[*3gLil#?.k!939!kb"GY)\DKu&SBYh*`o
%@RDhY-0fkT3lT?[`+B`eBX1oX*6[j:i>"SsBd#((TQDR+nIdknbh1d>0<#^bKSHA%R%Fd`#*-)X4I)b-V,l#``<&W$Q/C\WBS^.J
%MoINqDJG#Ld@"sXquf3q2$5jTW!MfBMi??XGc*0l:'SH#(#Fjj-gfbim"-E4FQ)O"f^JK[I-Zk-F0uY=S2kb,O/9+0PDGCQ&g`iX
%fY##Zmdd3rZtf[mc3D0:\h=XO?HV&^$eGQo<ba_fcg)Sc?QN+a>CC/^`aO8%.^I2=/ks2RW,Oi74h,pW[_\P$@Y[upG<k<KB3^-9
%-XP<u]\=c$h0A4YU1ei;"`hYSZ-?]=n/&5[luCUi1GcDtd0V16*EBrWQJ#,XO4ee,PA<!Z.RgFKLf0^!b&B.G+pPO7/N\g8&UJsK
%")U^Tj+N6*fG#So"Xp:i:XiDqM:$G4!il3%1c-`8SQ<EsPAt8N@\7,+9:mlP6;Jq],qUg9hGF)e9`ZnK0^A^KkX(_HI;<SiHCoeS
%)l"GWquW_D+5Fn@2kL!AlK`KMRD(M(_+OdOEb1RY[Rii@."s$WT@S+eBI8-\H&1a19]kimd[9;BY:/[N.8+pm&T(/^6UpJY*[Dme
%O/h-3R>2a^6%'1UUrJiGg/46*S_jq=2(_1J1dj$&2tLu;fYBh+'c_iQi=YA]n(%@?^@f4(_\@pHC=Tfs*GK\&=Jc^rST4)os-BMd
%g-?H\+>+s='MhFlmQ)dlrU09n^.j[`;^C.%&P+spl@B@:U$Yu'MVD=qR^WVT%UHA`A4`#):'FdqR<4j9GVr?[VN;C<Rc+P_0=B=]
%bK!bQC<rE)ITi$Gp!e-W8:!(EGY.)&*Y=sc!MIK+_RD7p=.9HM$'tWnf:bPY%nBLBHu4YO+YLJ:GBbkh#65C]OKH&;YPm>+V"b[C
%k%()#6^)tf?Y"\s5S2-BT5M1ePh?os&h>.O3V3nJjWqai*;u<Z_\I(P:%$(UjtaKLC+#^%dfDNs19-fCh5u37!]QT-4JHg-bQR33
%@4(2B=u]Tc%b:_/i[r$2)oILEQG=,UIth0q29fr"IV,KTO\*LRiq@>]#$`n=lAQ?=c]IGf\\Ut(nQNM6"]Q2JnCRH(/ie)32PegH
%U?g[OlF8.k_-<l_mb+bI('S0AKofMR:BPK4<jYZg6$#>s8&q8m/+ec/QpFEc>qF6CY(KsDbA[t0b!*'--rS.&1T#,:4QQoF`_a:F
%keT8!=05iM9;0-(h#Ke,6k/Ag`[-[3oCk9Rog/cU6T5FE]%.g,huJUO:I\Gah___AcuF/t'W\'7g/?I9`1i;G-`8Q\'l7]Jdu2(l
%?$p40;nNR_ca6#:_XL6<8rMNMEM*B)O+>_s<)T.k.JFo,#_D'86t)?-`p-4DJVVoU&V8c4;[99.k])KJLTOX]SLJK!<6Poii+0s^
%.+$Vp@Ss,?rj,j3lIj]6(4iL^5r`Y12iGpQW%1g3)Qo@,^A[E7_77H\"qtcI5biCN#DnS5N,9NJ=Yp/ubJCKU&b$R56]fACTEdh>
%9?(ba"PT;qLgcDnM*<.&&BdSJFN+oC9L)^$*@<t9F'iA3&M#sW4_WZ2Lm/I.!GTXGBJ]+NF0(3V7a*^`!07JRH,DZeP%rPJji/4o
%+"a4MVIi]WM!q4,P)Vq;SQkDA6a::[?`$<r4_kd!"pBm@!)fn:rgZF1bSi'ZWo03E7!98g3YN*M77;4!cK`&906$YZMmB"W+YqIg
%fqOo$,7HSk7:n7h;kju:K3__LR<ejL`Y*pDhm,LrX0KaU@1c6e[::u>_Y^nFN)be)=qPE-nhi#\-89-C+]\V:Q?,>C+^'"aD07o5
%*oY"Y:sAZgbXpO3DK0pCi$W2O*<C)bK$An=2PV7:I?dBW#t`7+fuV3jI[Q`#&:Y6@9T't]]L+S':c=*ENcfW!KrKBC)i9C??aCY*
%C7daME.Z^Tr2%JaS18(EAm3?=^4r]I/??\G$C'mhNq<s\@h<*<d.FW9\>g%c\(TO%4u&s;o!"o2MM%C]7l!1-U)2%7=c1TMVtq.#
%b\'i]CBkiE-5dFH2h_)4p'g1[3YCE+a=$&R-%tQZ)>6Wt1M2X-=iT_nHmRDcNPT,j\lmIL?BHmOYV[@#<EMAIL>+;$]d6&J4l
%M9;$<BZgBn8*F.e!ZO4Ejj(4?'MUK31c/7;07I"<EMAIL>)aEZ;#ZR-$(;>q+^[7!/m)ZnuCG4;[3>E,l2rCdhZ_6!RbR
%7Yub*=0&19&D\$)#CNKPe3l2T-6Q1INYL.mTt=l/[<*T@.ku[4`;$3cVa?oh6eO4TSAd,or)SBuRZk]06U"g$;3Q9SSMBhMn1[7P
%Wg0L$X:hTk.Q>pokR=c/=XF.9R=[0&A,nE67AH9E_'EBNY\XSLc29qj/ni**g!RlaDAG,t/Z7`#HP(6e`^.#e9pd,!NTUJt:=ZA4
%G>of"r3p8Ua>99q>#h;/l3?4f.C*'>0FfCD.@R\E`Zoe1@+]YGoIu!g`l0'Z*t9i%oRg1JT';,$U8\B]@qm`($DHnm]'*R:L+5la
%O;2Of.q8nA/D,b.jK'c"GW!#'RCVCW[eKo43AiCemt@&G@4KWeM[WHE>;"!sc8%Q<$qPh30rtAGIod/i`h:krffePM#O4^kUr<?c
%\Mno^^*:bi3qQE.0Ha**&p;]"ji1I[[#*q$0u11rQB5!WfQ:t>CpPKbc<G=V)bOVJ2(t*UQ=%rA-Y44cW&Da0'k.^i87t#f\Aq@]
%OZ>cNEXPW;(!p9P9bjA6/V.(V;^l`Y6gZUkH@['h"MIHSn/[-e0j'V?CK;kr,t-\T[4-!*"6aI9(JV*[m"j08MV2Wra#:4$8]cTP
%e'FO'7TZdN\n3AC`MtcfWJ\q]_$VE[4]o?LiBr\l@DUFL6+\udY$!Wo2N]lcl+uL0E=Q$GV"p(<TMI?FashTd>L[9h+2;LdHofbQ
%iiNDr#JR(bY"@kA1#oL9=k]%U:FdL+*%c,sHj>k_7)\7@5oT%%E*i0]d@*EQZXX.3\[<EsfUW@H5g(5\5QDct_:20dJT]WcF';hq
%=f<#<-YU22.O&=sY-m!5XJ"#GqGA.UXOY5&_(@NXr0A.SC7,3?*$WBVSmjSPC6f$)IVD^\ZN@)TfELjK3A^pmJ?p@b$I3Kjo81E3
%EO[@S.M*uJiDt3=6dWCnC$C2@1$sN9<';cOeY@+VhU:I(@Y&1W4sq#f*WkDK>;XcKiQ\Z\Q>GHlp`HRBX]7i_6YL>B`)[Cj,:7R>
%OfZFWlbQ/Q\e*D2SS-lB9;lp'h#b2R*bK%qSA'q#HrJZAU6[4K)IHkm"H=XMJ-opuKX4Q4gmRBNp1a:Y7+X2Y1q>Y_'P6bO(fl>-
%1\<Mb_k%[pLs:UNkQ,B]e)<h4a:S?&==^CoB"$O["5m;IMT3jYD.qC;Ua[t`f[iqaDtW[+7WDF+A)+BVa@chTW?juQag2?EEe)'0
%0]aPp-2D>_S-mjt1.[2J1QDU4U1#L7_"WN;NTEP2F"%1$6+BDR2V`$U!dB*fBJ>WBjsdSC>AuD(-kOVSc0r-m<H>Q2TG7W*RRb_m
%b>tOb96$u5nJg$]@[`X.*4H&7Tt[dQ(5(W-<ONsg+]f/kIXb!4W>9*`^h?4#:`*2e,//joC`S@7&%m;!@nD&MC;FlSbGETBC5sN"
%T8A:/)A1]f$[t<I+)X,t[9@[OT*93M@P[$(BG^@@a<V\cs8.lAQ6h3DNdr;Pm3Urr4)6>X<b`2rZns5,KSZUc,t$/HLn7:';/[%t
%I(0N';,c<=O23C=e;7m=,^A#+ZGgo?(c;L1<<hdSA0;GJ%cM@mYiGO>Q!5"hpj4)F?A>1^p&[C1#_J3>>Riu^&??[Tb/@VSCl`(1
%?_I]Mj97?/Q"3Wu1;!akPRk9i4W=Yn8HS^VfI3[:1j,:=63D2i6CG$dN'H#`"<o.O9oE>C]Ke9lC;`4"YjPPt'KOHb[-[o1;\X+Y
%M[a'o.(0MY:U;au(<;L.DnArmOpn9oMsj1\2\:kMDa7g7%b2Os[.k,1ED?!.cM3E-jU,K$aQimdDQ'CUL1&Fcj4QHNCc_bYVHK<Z
%TnJ+[Z(;KMd&86/$/a"Mj90&dpH#arBYe(iMZb_>K'k,PTf>FJ<-9p-7,1Cl42;r)C-.DR"Eg<%!H!W3N%<Nio%SJXAJQ@)g[R&c
%/FI&'%aTS64%0iH#Oim(MH^t&DQt99U9lo3fWpQMgY^kqc9bV?4B^T0YR7R*<6J@(N,9V#8h]RN(pmnqYF@qD7'iC,"bs5mbT_>I
%S%13V876g-Rk9SEZr#.]VO.V<HXahWplU$f-b.$NrY&YfpO(t?qON)dF\766alutmc#l@I]E0o/g-[f8Q=*jh&+m3"+>=<a7+Z^9
%VHogJb8L3*!<Ld1/.RD%6E2'#K'm_C03_ZGZER(!'(d)]C)J;K9Q[+6&Y6m66'8gqojtJXnK\"YkpFU:M#s`,Bgu)q^d-U;+X:Y/
%gT7JEOS0_T715-s_`]8t\?JT1%uar"$s<`F(</TgQUYo?OAI!tXU'*)qVMaj7oLVU[t,ej#HS^+[6fd(ahckSB*_^2LSX9@UN;_)
%!W;cjIYd7#EFo2Hju;8'U9k+DI`LrV##Ihe8g;+uk_cX828=(\%-iQIW%<r:#pJ0%B38u^BoI2OG]m^=8KB0GC9X3@%6>7!"oo;l
%WlDCnW]Ds!9uMWKdKnUYGM$+MFAZGPnE6HKVW;Q$)1et!fEogT_#jd]9enp_p*?WI<b^pG!j==%)WFI+Voa(##qHqt`.1s*`a;Q+
%H\A]B7'"7nQAV4:HC,F_VRB7d0jOHSE<M/U.:J2_,6.t*RG_7iL5(esW`H?kebbPto=NWM-0RU_&'42!3g#;r^6IM-W1b)0bURA^
%[!]>CZ]"$^6;0R*3^(*ALj#OuXVr"YOceG@/8=r7$jt+#'.!&@F1gim%,,.'q?1![Z+j?4WWD.4L]J8je:eB;DYOrqT9FG6Vmi-#
%LN4aeIXbu1Vp!pRO"c&A:"nGXlA1f@%=Vgl9Gnn-j]$3c^VsVYj(b8u._>/1"(sP0I*(aVXG1+WH;&'M1IH,g10/!,F3&2m1JcJ6
%gp0OhEPZ@VFj'ib6q)]-W/`86otVF,YpTC0LA\+ja]1d7P#\g!8d)#cAX'e0!$"5YbOMkq>,[ia)VNAPCKpm,8[1=l8B"B2'QJ[/
%+\0qR?d58ikXZkJ"!F=WQK0/p&KrES=Uf'$>gE2QWhp*M3%.F@Ih(f8h<E\F:TH#Zh&4]4"],4*7::3TAAe:ISVA%q!s(0SVqI?W
%+JKsJh3Tfp+o92OW9L-]^^;2N\/`-o6S[CS]uRpj.:O9Vf?dXC<bF:E4ZJ,*FFBQ:CFI2!@.RBdlPT#[Tk)/@@=`G6107-jYU%Kp
%2f64^.i\sqbYd8[leeCMp'+FDJD=1;7.C-eY8L``T+>BFZihfpV8?,?LtbR%gn6GAWmP;]j"u8n2'6nIbiXs-r8+Jc..b::7[NT@
%jETN3g08#l<fJoZU=K!aSfdW[Vr/k1k7[CqA%!_]3_$j7_kSL\fi2]Y`@UT&r[/)1k<t:7rD*ra?k0=_<M!rF"diFPdnsDn)GOk$
%ME8Tg(XN*`rRh*J6;N[Eip5nY.7%'Wdq:WdVrB:<0@e!<;!2-rHfFQ?h9V9t`RD.o=g6!k6m*^bLksp7dCOpD<69,T#4M-*<[Rn%
%Z'fLK+0:!kdMRT_m5,ar-4dpomC=1KhZH6Sfn?GPVHb,nJn"rk`PERFk9/i^,WGcGM!.[/]3<H8VFrseMMMri@YW%/\f&nieVK]F
%cTWVTJ/ZCHIgeAQ=%%-'E@9,sZ_S!A;%Y@i6E,N*KOf4KPE]PnU*dO:,Tq5aLo@kZ9XH)2!:54m&OSlOhHA9g9HgEKP#oJ_mt`#1
%WZe*MNXsMM7&#9)'2@:].5"(SU),?[.mS#@6WGD4F\]kO^hm1mZjtT2`fdeg<j",fCF3*;"h'V!&)Li/2/Y-'R*\s^/*bH6"jN+_
%C0A3Ri8=ptK-s`)*7WRH#09,pE+KbrkNZ_LQXHDk&<Vd6JiS3FZ==dUVU0WckTPBX,0mdB""B'+?\jnGYeY)l!%"4J-'%B0WR;N+
%$R`K.>td\ZA>754@D-X12K"(nTZ.Ioe&\G\V_)hH6?!SmR0kX?gS"V7h`jZ2\mqL.6pV.c_'*9s#;L"s2%d6*86BVeps?+dZ6Em1
%d?h.T5DU.1`u[b^0j10$-o3Wc(]b_C![.>rW,KJa"u.asaBKf6U),tF&K)9m[jCc+=WM?&E`Fj)aqJ567]Y^<%.P[a`BjqM,3fV<
%<<6eo9Q-liVasY66%FYp$]Znq>@B^V&5*q1+u:M+\jB@t1X/U!b0c]I&_Ya0*Dd4Im/3<J6:=7X8/!-mH=LW&aiYd<GVPCSE4S8G
%RNYG*l+E/-9i7fZ;kTmiM@M9&9[P$DR`jfH4F?cRTs/[$1YJ,+ejTT)M5\d:1u^@\9;_o1(t#Z!R8./.;Z^Kt6-Ns8e=(`+g4RoZ
%2&d9;Qaoai%u'3rU<&peb'tlp!P)+&ZF.8+46E1`Wmsh_IY<;lJ6>U.#?IVNW:FBU&?&o0RSWX@pEV7:NB9DlX.\s(<N*ESdXm='
%pKu'9j.k7V26?=0=5[T>O?=^f4$aMr;]<QSKW#aU26:/3m'6O&g]KRL6"!\F^QY^s@]1q$K>&!22_l66aSh.^MKk78KS_\@YK#n<
%4CfB/8<"M,>5;:#8h:;^',C^jl2`c0K)hSb)dS%N;D<lT#frQ4MMA4N!m@2RrZ2pIPS4-Q]BRj;I80tZIBZ8R&F<'p$eBT&#l'G%
%#ioG%06%B1)Q"!2"$c78m+!k'j_EjuVT:,(FI*d_T[$L(:`q`/qo=ciZ\m'BYEY609R[2+8.`R!'ep<[UJPNQd?sIqYR&Q?-k%Mg
%^]JKVJq0/mG[LO;Q)k/jcYRcb9g&Q'jjcaP:gj'`3.@YFRsqGZ/sD$Ei/A>_6'uO.:,dtr5>YD4,V5C?c(tA?k'JlN@0ot%m^>1B
%TqdB)@p#qJdkd4h'U>%o`8IOjhoc:a,^Q"+*aI>RU\r'+&]B@f4)KpDK5Up_c)?GFNP\eV?F=g#K]AY[;U^J9\V&)(rl-FKh:J#'
%@:s&7``_LcR\1Y,"V/KdSLp#,UI&"eH9FYl?0*rXb=9bdlF#b/'+r*)e79Mq,q*]9i"<Z/(9Hh6esW>ML.MdU&kNg"+Ppe_HJT=e
%Rt@-c;WF`29Ee\3JNFueV+2MUK(-cl6+RC`Y8oI)f;DWHMbP0UniBF^%M@;rgoW.u<W_T#]VXAVBLl,pRK@\Lm0a]I9<]$^]\kTn
%$7Te,/>@d?nlQ?8U<frZXeCMiA0E9j2Nh;X\7[CAJ]OdjQRjgHRNWn%Ms,TYQK`WWj;2r,dV7/=UQd$*lbUcS7\=p(e4VYO9(\("
%ll>2-D"NU*o2%u6-nrKmM+@?_SY#"7Z`+FKnLmHkFrZSM>VEKNEP`Q\meD3_e;/\;m-mMR3)FiJMJ*UK%VcglE<!]RNUO0/N2+D/
%:m[jL!ktHmN+b$47q5Ru'%;<8^_H[C[hp0gDC/Bs7'Qa3PR<jgMl2n,'Tf1(2(Wt;f2Ug&8Ga.L(8n5lS+XIobuoBbm[";N=mMEI
%__.2i=:F1(9s>(n0TQJ9=[-o(/,KIPjmC=e!/X<>bM4g(i0DaUKLCbGo.G@<K4EN<"Bb8YeiLAF*GHkLZ:;]pRGjSaqJ=j4V.=Q0
%=-%I9c<iAY=ID%/M<GN2SWtgS\0mt;3EoUpL)Wc5$;u?QK<LTGgf\\;931^?!d0<U%8&i!:&8>9o=a?%O3Qg\:&Yd:(Qt,]+OMKW
%7B`O&nh!FrMOZ[/GP4M0#+:sHb,P-IT-*K%%5P%P>]`V49U:9:9S?+WCb`N?a2^)[e#Q=hM)_7m\Y35A!JO'lE!TCXU90NG2H@VF
%/jW"hK*!taIBdRU`F>d(A9lN=rj$!c&\Vsah](nda_EJohT_F\oi:R79uV4nbpoY$a?=3SE`%l3d1chrf^(:qrAdN"63Cb%0l%[b
%F=;RUr5Ldb:/#?p7+;eBN%,@q[7.*ljiNHq@1nCiA?Q!VIY']&@*mlc1*4M?'U*E0&]5X<,X/[Ef<l)BM(1MI]'FZaWo<*t577"H
%3:h5Md<t9h(Q%d$#*2::*\H'?E'RO5VF5TCbpCs$>5?l[d3LHVc$d>cJiU>WI8KWe>''nTSBcM0+uY[QL#F4f0omU%S+)>TY,T3>
%ld6c+*`8)!6[O(iMC(oOA\j238%&h0a$?T*QgaekW[E%%5;Q0c*+i7!X\Nf::Bui^6c^R;)o.nm'[B+B3roZ]_.b=2TJe9=p6_6;
%3IN.?%:MF4d6'Ngi5RC/DH:&&`o<me%HPnff(9"HpfFsfodf[]"6=EJAE)S`K>)JX$oU5*JQHhBM)q]1[W)mZo<`5;3<K!fRaK58
%#CA?Tddr$Mk;Ne>-7RYuka2(AJWP1LpemO`B6.Tt2e#3j%[Quu@g1g*6u8^!/aNBA:=m><mb7r42:N`KX,3GY4&+(>EeVsK&77K0
%8nD[ibb;5\+G8PU8aeJs=Dj3MUcJjBm$L@9%kbnic%#Zd8,_YL5<J`.^V9=.TD[^+gZQ>cHMu#Gn(rNdLQe3sZStrXrT2_)c`_68
%J,16g=!s>IYE,&u2!@HOrTqSmkSKHpo@Z&erm<h(_]Lo<cY).`s8$*_YQ#ZNDiW8nDianDs)gi8a"KXRj-XM$TnucZr1sE^[,OFb
%.T2aHdZ8=o#Nr4P#CLp"T?$0)qRN>/rI4[rGg/Po:TEH$_W1<^Uo$IlL?*OhK+CmO<@%o6JlD?,VF,ipRHUsj8n$^ne3`.N`>K+[
%X6$Tt)C\:uf;-0[[87R^\;,]-3[]LP%,m)LKKMu1.RTq_;d6+uh%HP=-_1]UF(-C3`$bqX05>]q-C1ZZM]M`N6`8ot0drS5QUDh6
%L@DMROetb-QU0-<LJ6d!k#:MqnrLH3;^5m1Lg<^@9T8%^)aoQV+L.&cQHR4+3MD8n>UN[$TMA-]Qk(NhWo:n7+h;psT&*)6Nfh8A
%FKh8Z3f!o89lCY?X&\#3U/",TrLhUaaE4C=`'dAN(rRtWP7hqm6HnLT3COI2A(,NKZ&$=\D&f#XV*[!P#Ku.G7EKka*]TI<l10Wc
%Z'2K36n[gN;]C4,9?h+t*id<"9J8+XoKYF49]XV0g<uZ"4q#pDHZkeE;o@cX[V$-P!62sNm<'QV8gC\a`a11Oc-NX0D@iB"[`G%c
%-[VP-WLYC)AIM=Ql@h2=jK?G"ld2FG\e*!?D`s?'kHm3sa8+pE$AYMBC!teCSjtph\]\Z_#KGLm7HrJ_*8\P2cBK$0d)1r-d1S==
%REj26K2-]3=aKRHSm\9l%V9+F.Ypd",dckS_Dm`JksP@0Fl:[T"[/Y2[u+G`I"Sc"1MI@g/e5'PbZR-Mi/15e82P01VP-EB0r*qe
%m@d-uQlKL7W4:S[G[Mgj1n9M5X@SNojG4=\&*igs\mXq<]U!dhRFk`.DM*Vg0Ti,"?XXr^`Bc:CTSFYO:AVL<cn4K-4>9eUZj3lG
%bEOl6)8r8YRiMjIk#Id<&f>1!".+%:3]J)oOQf%MLX`Pt5"$^&n'+TARhYX=P3U(U[^!s6[-k=,8,<ZN0%OcZ$skD-0jSfQR$][c
%XgX7@.HBI-[I_=7)fs-:O=ErBn(ZpO)L0TH9G%Q=Q]>B<VN8'LZ11O*6'iQQ3?n<s'Ra$FM3O@$FK*>4`7CFO+CZRf4P*h8@TBc=
%H?:T:ZjAn\KL<,5JkI@%WCTXYbTR\e,ged;0Go)``],)/9LcCcan46/*!56m=)BL^Ne9mlJh2*[2:0ON]&W*_RM=)u*hqFffccBR
%AKjLq#Z2ZTniF407uSF!g&nl=RaQ!3=.b=@`_]+q(1NJ_S;9AKfgCeM$U#6V[;k\%Hf,<(,'n;0.$;[qSmj)"XNH;Wdq_;)7bCj$
%kqS5gA5<5I(\RmpM=+h8j4?a-K5e:.*\.(:b;1!J7#:'-d_P+hmi8-9-a8^o?lHWgMiu'onCFsSVhne$]tQ@/:J*O4?+TqgnR4,6
%$U`&%m-Ibn3"oWi=>LkJAW[d&j=_#+o@eiITp##CmllHJ#UA?('336f:e<YkPl/KP3EOaC_)oh^.FcJXR^t8KXHEZU,KMoD4H!f!
%$_?kM6ma)-=q=u>3X[Bp_ae7Y5M?2AlFauZ@dB6I4a-WVZ'JS_;V8+eI'Z3Afr`>Km&;PLhuX>?\)%nE-rM84[u/*bC(Eo(,L[$3
%LC"RY:h8m'bo+EjcSRj.G.6/%TkD'lerq29M1OT;_-[h2`4Aa\?Prf0Y)jG`:W-T+??pLgnI?u5Aklr$YZ*#0'?a]-JmKK%cJ9=*
%ifOto<3->LQ4gc(JkWQ!g'[R%4)Y<JU5&OA-5\MT1Rt%N$6e[q@B"Xk`&a2Mq9TZaAe-K8'rm-M_8JMOW]S-OTI*8>30DUH0iGG3
%aHEe=0'f!4<#g`toPBe^9ob2)DQr1fEg8+?e3i6/bk%i&ihe>!9.Sl<NI**(5N,""Wps![EuZGk7(:sD!/*8@/P]&tQfnI<Ep9Y?
%Ua").*Gsq%U\$Qe;TUHq8B^U/S>0%Q+sOu;#8?`6eUb!m8`Z[bY;Y(KATef"AspiJa*nSPNIe/*Z%uX6'IQ1.$Y34HL0@Tpct*_Z
%'i.EY(]u",$/PdpBXM!%X5IR);J0H4-qJUCpa^,<--r0*!R`u>9soa/=Uk9<Xu[o%o>4BC/PdEc=tr#_%"r2>2JMeIJ'R;*7A1$p
%If-Og](eZ>-<EMAIL>#3ICO7#89bA9B>K=ic@3jLlaqE0/IJW>m'E&C1b^`Io"D%*ZY7-+S3&5(cQ\!udi5B/2`++>!F?Cn:&(
%jS77LTlVkTbqXX=AHX"UH2#9eTQr1D7%gSU2i\!R?\k7<`H4in_/@\W$GUk&p_*O_KV?2JKhYI^Th&oqrCRTWNe(^N4ssTI:7epe
%&#^m=_uf%^coQj^h59pX--,Q('*YY)<7..W@"SOgdFi3n.Qc]8INuDO;0J/Np77PPLKN%s,`sf,n`E@Q/PDi(paaH7jW[48Je`jE
%B9?dTZsE-.Q$SbimW,"CVG"<pH2D+D6L<Rc4O1m,+gtd&Z\sO!D"ur];<'1I\7k&jDip_aL<<;Gr*5*t14?6+'/iM+Lch))hkZ1-
%d/p,<lD/U./40(D`$=^PMtG7S/X2rP/Q,N:UOkS82EZuqC_(iTh,^A1o]e`D@$8G_.*RL'?%=rT\Ef)s+1eWhoF#(ajLM)+M@.T%
%QJI-\N%f44[f`H1GRWWR2PIHh.7@NGFsa@+F^S@0-jh]^(hd`meFKna`#k%k0hqK9$M)DC*#><-SC/+"d<2+`b9WFk8`hW;8T<\D
%9$V00@uY@TJ?C.XV(<Y0(s)SRq*6:4!)h:E=/<+N0p[]S[ds#iZY8%gnLb*NK[t]q<&8&)c8cZkQ7?2N!_#TKF^DO"<_ELaXJ1hu
%Z!>jK)3A4X9'-76DV6=a<NicrPRa:,,02+i<i(aPaqePC\hF3s9PcI?*CH"a?q`AD>p4!PnK8SNIcPR5ae$Ed@!:Z^MiRqDapTSX
%.L1KF44]X'YR*=R9kY(ekfuPf)0E;>n:<,j(cd:G&bqNJ<L"O2(aFH`cT.oZki\q>aLF0]?3GRM=.BD1-452BMZ@!>OUiN16XQMl
%k3i.-Ve.Q--Y%rf0M0p[*le$:Fn&qcI>(a?LriNX=k.4)6fiI\IJ,o.b8;)81n<R%bp9X:/6",m.^obIR(j4@jr9Yr3m-g1iZ!TX
%;PXpkTakOrk>c.l=d&5!jYcNrTgtpm3"h#WNMA+E,,9]$bn`o;-h<#PQjPQ*IS?]#]MdDU_j&a$EfrBlMsn&P6N]7gLQ.-s"ZL=e
%UC]Gnc40rK(T'j-;AM$6kDaH;lprCId(*O.]@XFqeR+^SRf\9S2'[:Tl6PST23D$#O'?rRE@bXsGMRHjQ]?pLEO_jsQ<\7M`,Gp0
%i*BiH;(b9gI/XG_15Hf((@goNdIMUN5-AX%\r(t'kc*8<gSfe(\!ei(:SFbDWEFODdi$,h7YbksKN-,*]F^iD9Zir@7HoFP7[<h<
%IEQuhKK"T;2AG-5Uct/<`84ufAeJ#f>++^!@2"HmIdt<E&@oc=Z(t?UD2$npeL*^o-u?3R?>;gLBQITd&cj%tF4<UtoAmnd*$MUI
%Cj"[UQpju9Flqg@9pKBCHP;,T`20<RS+o;86+etYP"hb+*oq-O%qIA&.=c#m<;Xf0k&_c86BgA;i5-h;bV97@_A$GWB)N]`-ELZT
%]oo7m+W3?&>?Q-gc=@Q_1a*O5Knl6nZ/e`$$'Gs/kkiM?SK9,$&$KNg0!!d'Ns4^[Yf2^hUEt3$4P9g^('L.mc/dlp^5k0q79hF9
%"*U.HRp(Xf9^"lLN2^)\7HJSc`,O-D`#$cs3h@'GL1"O9A(7"D/;pMr(e&1.&$2(>+o3fb1=5Aj4sGe-a=LfoOi,)RE.O")ha&SU
%o+!*3&q!]LAtK(JUS/+(=Lpn<BLS#"<EL#6M!>&O@]%?pI!<r,QssBXd70(F?"91Ra@[oM-6%bITa,`*ojk]R8XOOC;M&a?M'9-:
%amZX&%(5=kCG5jra&RKE,'^CZ/[5.@[;roC=B':MaFdL"C4phT@aQt<X-t\<8,*j4l_2D)ANCYH=(JI+j;1jQ9l/'RN$J3Va1tj2
%/DG`\PEhH8:eZNqN9ge_/(&k[RI8$(:3>Y3H4[\n,Fr0=P.j<T-H(4Kgtm7SL!Au*X#GRI(>kA"R@[sC*Wm%Wk))V.MDDeEYSUea
%<Q6.(k"PEAi6o`8N`=FS`0*kq"CT1..=34I<*YfF.BI^F`DeL3/+O,5D&(Tn&E;Vtlu6_uZ47+E$K%YH("3=bRYb9D7P6$XELa&W
%<<nM\UQ_0C7%L39L_="C`Z3DX`$AXr,!,<cP6WFY<>gu-&+!2YW?pSO,.m*KgpBFp[`g2d@oZ*V%&M'6.>Pa2,mI.R,)_<?E>l]q
%1.d4-&gGpgB;/I#,jT1TCppGu.p'*[eGiq.+/>L7;VRJKpaDE=L$1*FJD?<AD`lJf]Y8\p\[J:pZo`jY(a@?S6*Be*5ZaiN3)U.e
%dka<gMtdBgAjs%]WIC0f6!!PLAk#*!+Y:$%PA_C+G#U1#1e@c-hcItj/Z^VRmle:@$Ak)n:,ELUjV"Kn,0s]Qe<dSKF*;7[K5DHC
%OW@1IZ%ruhH^OH$7IJ`EBDfpT_(qno=aDnE.22CGhd'UfiQ_2Kc2#n^?bB2rLFKA*o&ChjmVDe\n)PQTrQfujc2#?BlGl-c(]4*n
%?cW3Cm'k$M]7,1JJ,eg3o]H)JlK$4!n+ikWNdgi6bDWFkO"\KQhjf2dmcNm'c]3H<)ZJa@S!1I$:;8j)Gl?dkMHbb/J,/i>S,&-;
%?T6kkIE)<^e*A[dqs<t;Qe1]/?B9C0o(m8h48P:>`ttoXI/=_04h5g85P^Hio,h&s2fBdP9EAAQG)YZdl_!WZQG<4hc[Ts)oiEN5
%qtEnTr:PeI;57Dk`r4usPPB==*5:7t.6Mbdf.$W7G>n!Kb9Xe_49+nR2]H@\LAO-)DpD_II'mB@g@".Y7_EY%0t_uZY23I`rq,@6
%Y@h'Ai7`EWqg7r&bnoi@esj"Tmk2$g=#A\M!uZ.?qks&)akO/Z]K,k=n'8(2[rB8Im<:FuX6WQ"c>QH"]4f@jhf?=nSR61oLUUIU
%nfYZY(^k`broqP.hE*CVr#Zfas/o56?R@i2e,/:8N;1$:IZO<Bq:/ufnWn,^X`ej$5.uF>k4J#thOJulebSHq)/YhhmcO-)!1-8^
%Isq%QjQ?<`L8iCo42*AOXtRQ(IX?OMmCI\@;6Xs/rW2r?YKjjEqW+<lm?K/f%]\;MPrkpbJ+%K.(ol?q-?2lDG-/o(0ZkCm\c("<
%^3juWY/80HY0YuHFLcg/H3[aDTZ\?t\L4N&%5$b0B7E(Vq"1VNf@SnB$b+ZEn-l[pD3NUrI4%02I:q#NIMhpA]U3LXGm<a!#4:gF
%c0J`-rUp)A`j86U?CUCu?=-sMHgIU*#;s"s8ces8o'Yo"o964^5!HUWQCBfQ\(=hdqs,9np+:@<o#AE#i?Pu%HL*fh])M9D[q@YW
%E>tM=?2m(6m7H1*%C5W\YKou$?(AQQFa`rVk2^:#^>/b1cGUm8I=f65gXk%YB-/tHq3,)Yn:JSQ$HG9B^:8X`YMXa#\Dhd,;#'`n
%MZHH)m:pY\U0?EfHh6@@l.Mu4-eftiX8.=Es$P8.>O_N6O56.P!^kj(DE8KknUKE1*riQT.K3t8HG/2oNI<`]j@uu5DP,RXN:D\h
%o;eA@D+9>RJ%T3;WXn!l5!)P<kkRKU^tsDB2Y5n@Io/D,:LLrHk,IT-+6M\;2\*&XmJ4qkC]B_AhYI[WgKu"kkH-c>*NE`kjq,#G
%+n#(g4D'D\22Cf,f[LRZ%A0,^r8c,ubu^6E2d7X#Uu'+Lo:%t7S3Lb,3BW<t;[NF79CIX@^0PX?DpR;<q2AAF8P2)!0A?$QO,l>Z
%6^5n?A)kI9R9j0+?i9K>g"HCA0\#Q@+Od34PJ+F>DOA<bNp3/s*IilTZF+qf+US1nam8PB>M(&:mT)Q!/l>cTcS&&%G[-l*B!u,X
%SkMVuQ:(?HO6fW<hWm+-Is$0ZrT=X9koQiJc\mc'R+IobYNM,[\0Z4\^qBY,HHpSf-%,t[l\c39USi1J^ZSk!?!d"J*!Nrg)ts5>
%C^?>SEnUCuCloR,b%5d$jPN=Brsp]jIr@!k;gEVZ3'b;%2jg1DI<BRQFa!<>fu;Y/2EgF+:Ngm1I^-^PnDWuNs-$a5n$2Y`Q03#/
%:=sPi_A9#QrrGC26CRd\_m+MCP0]H94I+O\=6FM:^,S,9]0*^:q<c;U:\)VI8,1EX]g)It5Ms4or9"hm%Lr1/&FM\cfO0G[12Pq?
%A9>:(bi<D.eDE%bk:V&8/'91:m<@sCIe1$CHa3$@I7n>KPJm@N@&SB,/8WL?^@,:@DmsT$62gB!p#VJF4`7^/^"fiW2qM9jU$LOZ
%iQW5JVsg8"am7RA2s5\Hnug98]:.7S5.=8r+9*?Wir/J\h0s"WmkGu3oe:7a'9ZD.R`<nNeX#6>HM[(#h027/gtJEtnZJa3EIN%R
%Z6?"%o]5$(\(=gce!HrDRZuQGjM%8oDHe(mdV`,b3P"qIjP(-m)$7_#ad\9aKs]XcPePoi,Q?g#`Po:TpPU&-(+32HQ^b/UcTftd
%ScLP"MfsnEfa@uNoJo@7CmiLG0jd'%8<cj'YjWB<$g?<.$Um((?D/d&MLgObPWc:ADn0"HjlneDU-,Y5LKKdjf).G)##?K[AJ8"p
%s&bLu+jZ]CVc]08PBeX]c*N?tnNVP+PYHpPn(GQ$(1gi!l9d,FhXJ!KHa3$@rOKqBS3EiQPV&)E._#ESo[5k@R$p`rq#O`1Q[AP8
%_=KQRGTCtekojq,SW3ECUY+Z6SeZ_50E1qA?SS.pk<#n%m+YucYo#V=Un?fS./gDJIs/t:57D,c^t1c'YE]2sgTUec^38FM#JTmW
%ptDq/%*Ck@4,@\Lbgt)'O<1pcJZF29=$$\'#@[#8eIGjO\T"t,,P:dAr7h+W,TE'-+*G[h.0nei)L_B<WeYAs@0mFqk\uN!SXJi)
%nqLgGRIhR["XG^;G9rCM@-V\/iV[V,(OrqSq=aD#j?2kb=['THIJ2due`4%eDad!@I<KZ&X%aEF9A<`hp%-*gGop+5qEQHX$S96t
%$"[2eHM]R.TtGka#:,N9'$BsgaY0h>A^CAV'$>+EgQDL]\0IWdJ%MKGs#q<MO8o6DnIJ,3-c]eB)T[[V^?h>&oXkMGrY$"%h8JJo
%.Q%-8K(ufLkD'nos"rL`H_6G5nUU!g4*u("hh1N62]3^hQX=+7*I@MLh`\uWr]u"A91JCXZ[7j?@"Y$603^(.?Zp\_2]++Go%6'C
%c,&jFFP\^,a$Q23bouNL5!7u^]/>AN]/b83AA%YTieR'\]mgtshONJNFa8/:7:LPJDraCi?[NWMjAU.gI,Nj&1Aq]NIL#oA_>jD+
%Zb5E"h#[$'^7FA<s7bIO[E0He^Baf-H`)"bKQ6M]*rNeQT_(bri8?fP-qPJ-^3aVJg2fX._<617q&Xpt+"VnZO?V"*r:8[uZWGa[
%j(5mWElRPG@TS?PgTufrlYkWE`Ja=PIs!Oc]Jq2;c4)SBqh(H=%pM'3V]1cFDmeGie?FLh^O,S6d!)rolZVk_kC*7,dd$LS7ZZ![
%)'(8=#7K3k=i`M7>gL2ZFsn2S0U!O$A-XpDo/)YfT(:!qhjg6CI57V:ZVSS.I/`<p:]>ZhI;sH%ad]WgT3T,k\U)c/IB]@2!chq+
%2=p0Dr3)+RVPW\*hV#>G:+-'_IeiEoZ''aKhBoVbZ'j`UkA5r6-=LVdYpmF#7H7LH[O6@e[o>J<qt8Y,O@e;VJC3T[:Ne+%DP,jj
%s7Z*skrrd4o-_.s4!.C2T7p#+?p<:[o+CE,:3/YnNN!s-0*fD<_=2J!]R92t4;_B7L6mPOjWA_jaikr`(U_,arT.F3fjbK`@!MJY
%_oqJdUZW<RZSr)a:K;[ddMAYAQ;[TG)XZDIkKaWT@P8-"+,_:)D6SjK!!2s,2R*Fa@GemJ[d:m4Cgs"<dJ!T?Hu;qHSh8C/`VIb#
%:<jsS@3$E04apN,rGaqB0c_=U*N<1gs$1Jb]8*k8f6_U)@!Z,b9HULB#P=hpqU2O,kmmurkgQ_1?=/2'-rTZlZLTrI:*$Y2pI6EQ
%o',K[$:V[XN/-8I<VkJ<Nn0LVCoFebNV<6qlok*Srpn7AB/aoOhOM:9aHE1LSc:k!nV=%Fr&q[h4I(JGgR4,d3d<]TEmW/C)VY1)
%B\tV)lqV0a_WRQ(Ii;O%X<!1#gEs0of\N7&l_E2gM6G45`SF\7SP:JKREdbSY;ZW)C:f*=@h1DIEj0%9+e@c;gG.D<Y[_L;_nhA<
%\!l_`]CQ7h`kd\?D``$E@iXhP#<TqsfeIA>2MESCG<b]`pDj%8[oc%"mk0*>H?8Z/9t@fMj'>CEFG#S?a[#1'QQ+k-^,UCbp7"!R
%s6ndse`dj7i(j/Tc0afme!orue&Q8b[ql/rmSE!^Rh"DZf"7C+QZ#X=7C`-Kp%ao$'`$R3B4fugmDXD>D#Y@T%;fQ3?bX(bg`ik>
%Ze`l:6T('slR`q@H?Z]?T"n-+hQ!K7qfI@:H?=cLYYQ?R;?s;!%4_"hB#3Nf(d1%c2X"WgIfB!-bnoh$#5`hk7`"8?I^2PFhKM?(
%`rnQYV5a1+P.V9ZdeBAu+,ln8ZJ!PGbAMc$6!mh?F7q`WCWdFc4lFLHYCNfNe+mDTHu4]6G]K\VS&#3!Pqh,9XRT*7)Lq?BS`_*!
%`U?\g4m6q\A>EMrEngBF6(9rLEsHRMa_Gl97"BnJ[i\O.BQO&0SKESE[fM3@TZuk-S(k/=#A%fQPduho>'3[)%dMh8SNEck#BfGP
%X)n;`IJs#gDbdarN4mj)?MJ0+c#&rg^?]k9H0KC>VXq^PkFPm8L(10/!K^aa3pO]fqBo/Fm`FK]*:PZn\.VRK=(6rKHTo[>qZ4dj
%NPXsjW&mmkrEm=L3'$r\-V$M7rqb^Jr8ua`h.-k@Hh6XD(/(+)gXWjh=A]"=gnTl#Gq.G02g"P\j=U6*E+8OtaLUtkmJCDim-:`;
%C['b/_kFej?G,[Yf?;Y>R]ilt\=WF1/`H+NG'2`Wq.`jRre2*lJ+6f\ELl%D+7*`kRk&<'H?ukgN4[Vd=0pTlC\@DWRkIQ4caHU7
%G4U=fq[ZD2F1pBWo.Fm`+T0H7:<EcSW3_EP^G_O9QaStcO`:Ib3Vf<=bBKEq)V/q1oAQT[#FIUq976Td3Qh]KgY?:'s-Nms-?-%^
%qfA%b:_<=&`4>b0g"Brn^n3=7?iTNZl`7?&Rc++c6!uj3G!A7>mXqN.Ik7hiI:K8^U$-1r70f7EHQU>,aq()N;"WNEPWssqn,8IA
%ln%pYnV/*Dh%+AIL55[[>H\!5G'NmCW-Ef;XNdk-pta2'b<A#mT6+iYEPL]+=FA)i3MbKU@Ct?:s'Nb.(9;fQk5=ABbHjoS(S9.R
%G]A3(LQh="m4sI;EK;3-I=E6G0]d_4*4P:PiGmX/3W9=IOPBU!iGIBM4o3"/NDIrRHmgn_Y<pRU@>[in5<e?\T&b`Q?7!+RAZ`H9
%!iA*!(pjCf/.:8=?m6Z4!LW(ti-e*js7=TE7X>$*nXj%2,8r`L`jkb=f>!;P^\s08TVoll02G&])t8!A&sqNuT&5"K`:fZp4>29S
%\O([kr/A-khOu<XOn%EkJ7Q=F+G&uE9#eoX1@8U5^],\C_s[*h5@KY1^LqnbJ?S#9AC%s(6-h1!Na)m4ir8StOK"k+UZ;aJc/.=u
%^SO!fPE_usNS9Igpsa3/P8C0idgJQVmB\IlhYcO6bssPO4@Q/e383;[\N0KFip8n=g)k7@hUQN8+J+pVlSn:8:B1)7fuV?_jGj_j
%m-,LrIs[X)bTE5*;`\L)ghC^5X#Q>Vs/.e'=)Yk/;Ft:-chn"q*:1Ahj29rb]b`c&V;Xd:h/4P'KB_Eih;OI)?X-hU1n;>T]H0Bj
%5>I$r7iK2K(.%f=0E0e>c[UB9g57%./JIC?hd'Uf@J%DJ2(t3nbHjF*?`jA!iue6fC\5OEq!#8S?X-s"e*Hk"TDdC_rSkj^E^uaM
%oA^q+]D[68dI6>7grl9]l[A]YE_Ct*UHo8J&]qZA>bXHGpU(uhG!`r0^sH`Odf9/Nj0/J(Zu:uQd;P*Fci8,XYK(9?qr<`eoV/n_
%?WQ(D;0/SJ%(WjB9@1#+J`uo@kqsLo0t[oH(F$kcWHBA]0ei'Qk[(ih_U0h,:dtu':_0<S5K<YR^QEQ4[W/ZsXW:!Nek-t$&B;Y9
%5C+UrKU9Yjl6)=XIB3EsXM6<k&hSj)3RrTNT/,3qEqGZrD&^.lmPr7R2<IBHoJ4&4co@"&0g1QM];i2VA,8UZ07@^1kc`EX9XK'0
%39;mVL38<g2jmhYW=UfWQQ\&bN%q3,ZUeRgcI2jXqQcD<D;36!'->Wm]!eT4h,qZ:Mq;M[oWd+?Z5g,NrKQcbbN=K9AHqmPEX^mL
%EV)F:biRtFqu"S!5CGiPj(^6jAbb@?IrTf2mdcFj2q-.NPL&J3Hp/%g9CJDjZQ?$Joi+'r_qIEuIuLdVr(!MlaHSk`,W5;3kL00:
%^>N!"4XCtl:?*GW]VY1>oMfDoh.N9jN%l!J^W\:q/:HLk<6$bZnP*.5g6.b9p=53Mkuge_nsMP;A*noMIE?MORqn-]DS^=.072qL
%k[TSMkZI%dr3&d=Y2^5h,)]/C1=8gp@14Pq*1-;<Zn4&G&o-%Yij+ItO4sQ-]Cjh-5'R8Q4"h*aXTspFUW56:8$a4;.V'UdfUL_q
%QVM=X$U`O>(ZkmM>ijW.I9Ksk>X\>6)V;.!&2Iof<aZL!i&*QI_,':'o5*Gp&k>9INL<@0`9YRnK4jH@HTpu[a:+s*M4or0jV'Hd
%]8Zn`4En#j3*(;%Om"G<ET3.Tne$j7@i=&o=Xrf@U,g^YY"*;9BSBKf%Gp87<*Cr8<hQ;*_8[DX$,(P'O25Q!+7f^j`+-:>Tptc6
%NI5EY+5NLn2tE):+'^`dSm*Ws_cU%^6HQY#D+ZZ-4h//^n#l]2O+5Z?IepCGci-S/iU;KfNQ]hG/$^,S4c)-3@tq]R4V*Psn"C](
%9<,;BU\f0*^VP_0jG0&bFc8_t^PnZ=QuS*9)[E,t`qu1;//2s$G!:qX>fC^@l?HTAn=JmuO+u]nXa%\B9:pQr"7(U2g:6;WK_(1%
%RYWr3-O&I*D4K7GE+8&o(,+jnpDfG!#C2U?0ojX$ag\iP#(r;k=J/Sd_O$qf6B"qu8EtLR\p$8pqb-iM^OL`g./)T;/od:$=G;O]
%l`nDLl^pPb'9\#MO(76dC2u\BhP8e2`h'JB:YIU-gc!`go*H:m(G"Cs2AU3f,CsLon)+fA+>N2>Kba^$1VW!T(-sE<a.#H?XnaMW
%gM#=6pl=*t#)=H"3+Xti_&6L]Ke=AVp=+'4M(LJUMrSGF%o+.c\>YKj]!mSPB$.FsIAGA'`+()6Y@J]U]<n<]W/Y`Hi5MB/q\mMh
%'mWFch7WgI+isr9@io^3qN)uaON!_`r+O$^S'Y)5:,PdWAZe(M3N2<>cnTt89L!du+1s*#c2bmXQ5n(*h8uXXCXC)XV-8Q/X"7MV
%j-qUqpRrOJGtnQb)sbKq_oqQQ6Xh49a1$T?j5nXrU%*CK`2\3MLm8Jt&Sn[V0Q`);[Gs*(U^j*L)J'2f2N3RE#UX/pjOs]Un]274
%fYq$)]L-TUlZU32l`+QUAS@Z(WU\<:O6OBqni1qjR6KJEBfn#'Y?Bq:EPI,=Zd8ITp`UYc%-+8h5&B6`pS_js6:GGT_T@\Ji89g@
%i^G/!Bm`Ro0[ur7\%0YE$;KDs\kod010FN0'1822O@]&%.c:FA5%tm$(?sK_JbDXcbHo&U_K0F4'm#^'1g^&-3ntjdcIZVIp<c&R
%Z.!_!_e^Yf/-?h(8!F6o>]/BS>--I5n0EC:(+-LK)-RYMH]6]@>t$]Jk+6.2a5UJ=mMtHZ=nqnVT:='q)]uX\\(4lTh"a:`_+qkk
%Li7TO3CF:UU5qb.66)o@4)d.l!f*3fo*t;"$&R3':+q9VZufnBX3uDIQe;K9;Y)1PA@;QPOL<:0..e\VFjZNs^$-'5Ka/L:6_!]"
%:-Q2NNujC<:=FjYma4B_Pdq4G8hS!0)nfMS3I7BWE<"=&p,q'VEa3KHn(COB<!rB)J[:<E5TbG^r&gc\^ZB77o:g>B*.i4?_0KM=
%DH$-L6;4(G]3P$c>*I&;hCgP^OQa"VSE!\qiR*[>eHe2e3D@O+RReBd7-]*lqTqZr1Bk>UK@kqN,1>`.-TdDH11o^/bu-OL3Ho&G
%.EKrm]+b3/HgfOd=`S&.]c3@=cn>UaLXX?A"aUe#[U?TS$q@tP:BIHHRNYX!)AGc1s5(:=Ol(BJSE^\VboRWG#T=DPpW7@B]L9bN
%`JU9K0_3P1'Ak;1:Eb3Xlb@d2I-ECH#B3*Wj(tdY;b;n&)k&8La0(#<mu9!U0Q4.i^NO)iIc]_ca%O<oq@p2mhIb5LoD1\gH?SSL
%H8mk93W#>)MM%85-=>q!m)GVk3?ltZR]Q?1lr#P/&J@+@<Sh&6a+QT>m<j=hiY?K;ENtO9iQCKC_Y7`pA'SoH_N-5E#g]-.\D)pQ
%FYJ]p`P60]\DiOFEq,&/5+aWV%s.8PboEF*8Z[=H/W0Y,*7uV2Y1LX8(-B%.41>X]EX.ZX_3bBIH'h?:a+W!Bf7K-]-qAt@j:QWR
%OWA_hj"p;/_Jii[[@pZ1p#IeA+'.?_Q_KtK*]9KtKVYRAL83\;Koef4GFd1N@YAg?9L!q(Detr[H/+?Ra&;UT]<Wi2mBFaDk.3c$
%I]ZJ*EZ,.r]g\UTonNIH0Lj=tbEJ%p=op^E=hRo1_eMF:^95fKMiN#qXiK&580@=D?Q1U<HY2Y!EO9SDYatgm>5)6-qn4@hN%bK(
%)n]%Dp!,'-Rq'.BIZF=cHuXI[=!8B#DU,cBlF(Yre`$cA<H7rb>oi_iF,-ni8N6P,>!7XLp/O>lJh)F_ID*@t0gJZ2i,Q#3H`[p(
%`5;q4>jCb!A:QA9qV&G=_D^Lr^9U/"mUir]&qSk[p<MiC^$hB[nY6H_4mi*K*e&<oJ+5e:F)o!:b2,@GmiL==TCg'MZd2G.(*@o.
%[1ur%p;`X)nQ-=,I\XnIL%nr=s3B`qV42#O,tfp`7a+/u@@X,iJFD@tmp6q$?8(bP7=QI:"C!X@1-!Cr;46S_a3:I0?tr[,`3UK1
%fmr"g:Ak#HJ,5!so@`\UL.Qi[2ZiuZ0ee[LQ#EqBKL5O%^MITF:[A$\qgNR6'fCY,lZg0a$3%-KZ_bG.m&P^;T:bo.rn$M72\+Q$
%X1dQ;^$GB?a[\J^O@sr6T-e0`^YV^mnF5<-#2f(mht&RPir)i"P-\!1<]JYOpCjpRq<(WInW/q/aA4VHB(6TikLi1GC=f+9^;mc]
%Zq1f1aMgJ8<W3i,e<q9]3-KJt,iP2ZZ?7#4peq;3R\J!<FC^XVj]cj[O"g%_*rdt-]NTHoe::%C!k3TC[2U7alna+El'7Pp=5D=!
%*-F!RcoY_H7=pO#ESf:#@uNOAKRXC>GkUh.m,8m]h!Ms3OFsK9[r=o7?H4V.SBj`!J,f5erq+f]j2+ifAba!?(Nn2MUdH>D-"6Or
%LEsZm%03>WE[3?!X3A&JgRD%KR*VNTrtNVh$f?!cL*[g>ZB\3Jk4DLYIuGaJB+P9@2&IdJG$?+@J6Qj7a0>(f6?TU,?4!%RH8G-&
%X1G%rYclgPc-7NUTAo1rk*f1EYJ0!%O%Qu.kHbbhUN1/BYhY4=3^7h^4)hgrAuoXSqe8?f^>mHfrNr%aG7+M@qMF74GU(EVj6G/a
%5f-OR&8m.A)dsfZF"?JIfE_aDmTAtKF3NrIbHhOD&0'Saetc@07bBG33S&(#Yn<4-6o7Z,^sFrgqY]<m"MPL4D1j;4H(0p?pR-*T
%pJ2ae4P[6[*6ic6a%((jbW@NHSGh,)5ZDak*rB>8<&#B.kC)j,*fnWl]1>0R55=A=#4Q/6_E"Ce.HDfF?<Js($t8GQDZYG:N_I\s
%QWTVu9u@"%*.oCB(r)P(3,7(X`B=XlRqId/O4mfe!Wk1r>j;(t&GCpb*s80i&5#IMJ-_T63(Bs!8[p&eII-,/!FI_udI5As?U_?r
%m:UhSH/Hi'+?:HtltgENY-Ygn0#KdEW.\knI`0(Kl!MXj*r/[:Z&hO[r-12#X_2DLc>b'H\LRr5jBIId$5J?On:^*DkP"jcG^$^T
%?/'jScf+8o^W35'@Rp%RQC*;qjV:Q@%UBd^:<OKCoZSB"Gl-Ja\olne.F&Y5B&^qO0N@)ML%^Jj0\+nOKX;B]Ye&E`Z#f[-)1Cs1
%^O,VQ;BGHpdit\?;gkS2ZL8qt=4)"f4\ZF;!*5V2IhM`8TGjlXI#!(f=$5uA.rN>t)h3_[9YEDEGVj8&31@<Af5gmGNe)g"j5t%#
%>7c4W5iZsH[@CGsn*=?X&DqK/S,.5>hqKNnIYG6enWOpbppN*JjV*7;l"2#MF%/>Ub6a!'0mVR(J%Vp^<q\a$N#IM&cNaC/:]9_K
%S-HscNph7`Uc?cG`5ni^H3jnWk[<,?CJqTlUZ"U7ffD-3a8K^ZrV;QQaM6X43KJ/%I+0dYio&W:KKoAtMFHFR+h7I.9;MbO]r`T2
%pTg'fc;+q06pO5.(8:`&OblS'3[=La"WF1.O'2LJ-gKs"q7eT<Grid5!m>igHBpYdN*,W1Qh=^XhY8hDX3WbTpPZea`o@$q`BU'u
%j^7tY^Ho!CYJ0gfhu3+I^]*1N^BYqJbLtiN-I?E4dfuo@q3h0!iVJI^3.Hs]kdg6R^L,uMkXZd_etJ4b4N&m:EH@[8qZW>"IIk5R
%%G0T@J+Gm`^W^lrmLOgIAi'?d'VbLGUKciYGh>Ps(8.7-/<,2u34c:=fX&Up._S(5=o8B^rqh3:hB'nnKAZCsGN!h0URcCBRh9eW
%,@G`DREL-PS_*o@1\n2rO]$Wli`fa<\I`A`/:KDO&c_frQ6<_=l27H/qn$58l>Jodni&f.^n:Z!G<c#rj6Q^\S8,78=`02/Dg=2Z
%8FK@E/kgQ=02=M-_0GVS)cZ9Tg^20M?,VnZ1EmgMZc0]Z_Y?@57ihUZ9ffDtO0n!6"YZ'PMO0aA4C3Ilh9D;TJTB%_EaKj_BS,Su
%s+TumL6"qAJG&KXYptd2WAQnW^2dQI>Qk7;jJU7h3V2$j@m"p;cRW&)jr*_Kj8TN0<R:RX-G<%e%/A<3A_(KI'//8'j\pK5c+8=E
%A88mZ_XKe-2ZZZ(Quk\rd-JVof`W?1^lI`,]QtX=?a.9-BodD7o?KkE1;+S(0E4I7./%/ecE5NhX7lTS3SNnkKm!CFSr-!t-.K:J
%cY>R\Yr6p0lAdWu\)[K.>;$(=K-+A!$4^i8^!%OH)0jEpSWA.U+86VYce)_mNICh\nUH:SlAXn8f;*KL,8g7@^'k>qq(h..kGO[(
%"GGaqi<h^712=toSGi1h)#l8f;$a;Q$%YOLAqe.?C\qA!r>5aK]A^TN4c9AT;?FSShuTD!b#[)iH8?W.^aL-OpYuitb>BII&EgrB
%0Ud.+42_*NQfk"'i8sNHX/"Dp!(?P0I@$NL(>8.<inZD\4MH0'K9oItQ4fKd(<<`KH"[&IiM2.&FLmjjN4:]NVU5F=rKI!)b6(c2
%Q[cHGPg;D!<H%8a4@]@/J'p>V"VBms@oRW/]n\qbPc_#kWR^>,H;Bji/QTbk>/%/r*WG6SHRSZh:&8`ZQQ#.#7e]&HJPCF&I%#7T
%?9@4!CRkkIEEVJ\HZ-q(RJ'rn@4SJ$j5XFrn]:>TrE`/[+"PW,REq@`i?.Ek=^HrW8,jI8hKF-Jf*pqtZqREVm#9@>(Al*,Tu:p^
%!.VFf?0ccs2<K"ba2WQ,mJ&o`17a%]_]Dpdq3(g8_lNB/7dfm<l9lifeU('aZ,=ijJ`h5II6d/3cU\"\p)T&#NhqCWD@$^9]F#nI
%Hl)VtO2HN:^CNh\dVeAa&K+Wbg4''UiW%?`P\??2.n?GQ=OB@qm$;[V2rr"&Cd$dY)'CG9X1W=uoR(3NdAJM<MtH.XIU'e3p:qtA
%R"'n1q0)BPRt1(Y/LjU1I$\itif)sfMH:mDT:T+sB%>5Rq:LaV!0W]M*^l^EpKnd;BD<l[83k8eAJid?MK'b'K9t7?mDmD2n`or=
%?U%m1i1&cKf)KRaT4b!&<1lrDY,tLK,)9l`g7*j<`H#WT5Pnr[[04JZ"pEA#($Kp#ra5Z"E^D)Q`eCkSc0sOnDSAfUgBlUIjbf,^
%T9RA7Z-BF]qKDe/iM.<q:'eMtI*[TRN,RIH!R()JT8q!4"7"q:a!Su#@;(jinC?BSr\Jp6K73,9f_9KZBR7FN%;JN;4Rec<cKADJ
%Z"r(.*WB^H,a9rt27UPALa*"#b%X'nds<+^3t1iKIK$LhG)oX_a57d6BD<ghX#k>Wg0rr"PA)G#8(D)/EC4VY%R9sF?n?WP#.nlU
%2l$DL^F4RIfk+jXDnc";?O0[')P3e^Mm_@(@QkmFWSZsMHd)=2+,V>aH\ma1rhnM)mdj+ATeDY9;]3+LiP=Iiat[`.bQ%Qh6A(a.
%FSP%FO*%NT'[8k6s2BaXDu<u?aS8OeoP4;n>PX1pg+S,+hR<`>,U)]S5khnLSq#%+jpK+p>r6s5=?F@$Spu(MVm2cRplHLDs7>Cm
%9%S.spi4OOT/6BuIVed@F0EXa-W]gd$F@J8,^e:1qr6:VJ!JpK'jk.hhLBo1JJ,N"f<VnimOr)DpdS@Z6lASOoD.u(RFU]pI&uI[
%iUFVWOl9YAd*qLa`+&][hU*,QV^PA&fAnmPg`Q$38uB;s%:G>agY`b?)sSN$5<6LP^ZPC%^\n'!J,%i<p:pM"lOQ5X]+;[Sq!6i8
%"('r9qr(?Ml20=uVuPr[n,N@Rmsb:d"G>\5p`K@eBE%=SJ,&\drr)9k(jLAt]Z)URqqI18L\hP%TE":#If1cQ9E$^\_>h^!hjue,
%k26.:GJF)PB;C$GVskf-4LDoZ?mY-_[</(3_Ug@3;P&CC!%=;H2KIip\=I8ZWt6Id+(-jUjT0XqX7)]O=ie[Z^uu'__aiD.!-[o=
%@4f]7=QI8YC4oX%c07D#%-Tg_$6WY8"oqQZ#b.hm.;UjX%o<;[(N;?jh!GBj4=>Ip4Eo#f9HcO^@d9f;bm&gCZYtP/#GM\^'ZcG$
%SPVV;FTX#aD;##O7I;#nrobRAdrE53Pj8n_KpGgl42HG\@3O]5OQ/Bg#='H!B,h4'5!$<(@'YG[P]Iig6ALMTOsY"c)h>_;&M".F
%iW.'d#`$d<KK?+'o*A4=VZN)=<kdGkoNV]GU;B7Z,6VDATs7q=m3muU63M0>B<d#rV/D=o]!BJO'=l0s?as)T[&fX(&^1iQ1QAJE
%5qO&8],gti"Vt6KX[u\^iHZ`^!uAIf1X15;gPTcE2$0fA[*=N>QS_$s@PUQY=ZjPP>,:0^U?_"r`Her!JrF7"PH-oW9a7N#/(W3?
%%$l(//*P2IX"f[uTT)B&9>[uMd_SLWFC,c&:-f/g(<b.[:CGPUInj.#?]]+VM1f"N"?Angd$0gWp/\C8Vf>S2#@V\H"i:Ik[e>JJ
%$5.;g]08+d!NU7SWa5]fY7,o!0:='/.Z,cPbQ:,SqmhVcj2b(dPFFLQ=\M!A7;/b,(lMa*^uVkL$rEbu)8Jq0H>*G9Sd8j3_e:[X
%j>2W,k6S](T)OOeU`/u06&?-fdj3G0MfZ=$8*LhCLm3`.'bJ?)-h=A=k4'l.#iY2CT0V3%@9O'N-F%8^aG>G7;ES<6Ku!ha,@daT
%l[@O8Lu&KN;nEf][DuV;8$T9PU#<.idS]^C]pgP%X<(p;O)mDN,XBt*-BJe)LS<W8[^?ScPu5L^bh1MUSr5[g8M!XO-g_%Q0WEVJ
%.nuKN)Fh=kS$gTFZp;Cca*On[nT34h$!4[S=pQ:n6hM`HTuSo.3Gi[@kL8[tc,)H;YE%\:(U>;nX2WDQN@dL&Bff3p]L<-(I>kDO
%9:JB2WRI%<G<uL^RT'`uWo_2CZGltddokq;@L_l_jC@aU4*$Uj9:2NnZFESV=9[SH_O=2]UQ=8jRN-TCnD848*[_4jj<l*"/h#fN
%r")N.qXckET$-Fk%#oI6M<QIA=HeoBT\>=O[%XpGG/'A5c(m?N=3+inbjX=_oD=VNjHM7ghE+4ZjnLSKY[$3Ko@#=c1<^so%Xu;>
%oJLh]OX2P>^^+40D3?*s+RX"8]QsB5eeoOf4b"E)<Eg]DZKaesO5BhVUmom@XK-U7jci.(cUm1PVP2eIc8#Bc-XbAb0U/dO@a,:j
%3*U&.=Y*'Mbe$UH,kWrn9?E%JHZW(#?f+^I0UhW:k-,!?1NLC)"+[n(;,.gqAfD.'F_eYC>-)U9]Te<l*B_1Q#6EqsCea_NKh\(&
%5uknT#V[6T(ZihHK`S&8fk@n3>7%Wk1k3YINBDW5*5?]C5N@)Q63E^N-9(UtB6doZlR_+T4[><V7-peRa")C!-dZgtG[L"taRMc+
%ZLu(F2A@m<[`_&(\V_([/'0!C__R9P!a*:VZ;=q`A^KmHfHM-.]MHci1q+lI'A+L?#%07Sl!ZM1+$B[-=(EBQAAi0^o5#Rjr-SW_
%qde$8L[,54C/\rj5VEOQmSa<5bW-Q@).g%#fUQ_4`m,X/oq#n4Uirke\u$&*/mS`qI?0XJ/ZcN]$DJ354[s+SS"qgN<r@$GcY=._
%o<h-3bS#6WOADJk[+hhdWS(jmNM?&,^;4`,%Uu>IIf;-)hm6F9#UCf:5HCEoR7bs/,VcR3%1U/<)G,H9mj>ne^JQRTP8,fLlSrb$
%Y<r8$JnHtN1\*7pHbG=]E^]9\Q,5]SM4!.Pno%XQ8,%[%7eo!`IQ:nA?H<O@\#E_AL:C4hO%.T0Ml^@sgE$imlY(0qPWZDn,Ac4B
%o^[<G:Cl/T/5\,(H/agj88t-uCGj6(3Z"?K3H1iVDEj*\Eg!_6%DN]\ik4rnUNZ5i(%@k9V`$,hgR8#9H5^tp;G"`tA#eJg,L2r3
%rT7:o,@!9NFHXEVXT:]&ZDPb<>NK%+OIsVmm'rnmFf2i'k^_str:eiSR2S_@\.$IZW2X@KZa4`]Z\cN1f(=Sa^g/E=:36@3VYSq3
%l8mr!?b?1b0D],lk$Pf$7#^uFIn'+_Ij$ZJcQE&U:^<.K"!6fn<^4<.i/)VYOZ(IC]QiE%eY>[=A5Pl@fA*O0EO81"hV&nQJP=/e
%.rXA"5/([J)U;C9P;_i`Q:Y+H"BB\JJF/76ZbhHiJU6Zul!j0jjhXrNEpN"g!6*GsA&>_lOH.jGgtLq0!mt&(GaT["q7'sG/Cje-
%V"Dj+]]]0:'N);4!'9@e#(=5)qSHuR@$^$BFH%6[6',XKl<bs;Z=/MM6F!(&TpbO@[!:Y&,jMs$G#al`k\`,K];[I,]O)?*2<'"Y
%WdHX4;om.`!Knq@\BsC[RMM%dp'>s-@fVao$U3ZG]7l)Dn:`8_=Wt^B*$J-)I6ER?P-6)Pho_2GO5\sYY%mQgHHnYs5ks\C*oFaO
%JKmAe`=a(Z_\s(<-p$b.F>8cVE?k`4Th<HP$q8R(*7:7paB+uXD*5]$6nOQH';:-FW0QBfOJs<)[_h"S"cJYAO[*>*[&"R*5q$lQ
%nF?QVIS>_9mna_uCSCs+:aMtQDK')`G[l?0`PES<Dp)&.LdeF$,\oJsWZV5m$#:_OJuDr!RP45u5d\H;7ALTl3>0a_j`FjQB5V=k
%r`gJTj,t_KI=o;(5@amQ%SI8C"#%Y>3k#_WQf%#5825FDjS>48JI0%gmR<SgG7GM(47Wa00"p`,Y">:tA!0Pm'oUuQ/e&)k:qPbY
%!9U.jBR":a4Vk5W<V8'+GS!odF*o_9:V"*"`]1_n*,rE\pYG^FO:DP91%CX)#==IZDkZP%@_G0oOm-,fYrOfM"g^mg,LCS\:'fQq
%b*aO)`fh(VT:7rUTHQZOUL&lf/&G#@]:4hN&R`aa6SBTYp^q+`>rn8C/gsDo4APRr/k>P0Iui.h@nN%fl#EX\Vf<Q%J,c+lK&g=4
%q&".8GTQZIltUn*U/)^eec=[^-l2B.p^fnlK@<*]=Oo'(EFTKJA9W*OiIk'!(Y^1afMR8E_U,lq"/QR<P(a'O'nuNi$'09QK9tZp
%lKt*7%H5jlS\%J!*<Vt@`:PQ""0-Su@EWG<8Ui$0Z.3)$rofp2ZjAr*cV#e4gP*h"Q9(>&N,KDnX6f70OqDb&jhUpKK]C\ohKo,-
%X*-<W<p>[9^'@F6.d0f:Z)JO`)7G%&E%C@oZU2h6]2)MkGt"_/bcr15`cV1?<5"[^o#Nkq\YXZ4]20Qu-.<'@?YY\@'Al=Ief!1P
%:bMWKl(c=GQZe`W'a$(X02%&,UpV+g"q#F_D]kdWD,!q#%:d<k9)e#iRPUuaBt9^i7p$+5EY_W.00R"\eOs%^>3O]G-1Lf7"F2K&
%lS?'/(MkBYfuQAQ7N0^*]I3Q8i8Z%Q:b6kMPh@gj6[ddNd)O#^E%=%$s3.u8`?fBI2lif(96H!`P,.+-@,<ggB!*`.X*l;$*\!E.
%bJ8Fb.c!)G#^mMEH;nT+q!05dhbic&VG"DXfj9UYE+56L\XFTSpp=0pM_>rVM)&slmih$[kj2p0p1"(,XL2t:^(2l"BLtq:r)5[n
%YoC*I[K%J+i4=&"?Eb[+9,"Kd+-A&1rs5;_DI*kYV,CJf=VPZ"/p9a]-LAitT4FCd.Gl(d'K]_@0!\.\k1?onT<0]1M^5&BUJjX\
%#1t@KIF[p7>C23:N3&<"TX;lGMa'PZ.G3`%Z;;fREmB\DZGe4una3a<2^fubLjDG`E$PSb_E5+SKMBF.6(bJ1&EhJUD'_;IpadZL
%P43%-*n2C0dLXudl4$fTNQe5PmOY2)18_0m:XIH8h,s0n1k:\/d9.1Jb4,PLRX?[+GE^?0N-@>m8Gs5WCRg7:@\_8uBr4s\hNXZ,
%h=^fZdW1PC`5`F]gVoj62AD?o?!ji^YrY5haX<<XI#,OE.TO@o2\AMed+!C:R^mh;XonO.]@?=N<<@Xi?ZPNPJY1J9N-g3AZ585q
%>?VWI,Mt-bCbNcPBBG346C3Y,Mujp`aJFWJY8g.dU9f-=h4rCRFd!+>IZGIW;0^]5amZ5Pq+G0Ged+7OKjZ--MB-Eqd?(6U\;#/o
%8qt0aeq?Q6;=Z$9Am$tm<^9.H]j):-\V\UOL*g@0Vet7_:^F9O)-2@hfWY-S17cLQ2@9u__G+UQL#PKb0"3U7P$fk($st=bVhUbR
%LX(7-3W/d5'9H-sod`D,R%o,?n!VD:*)qZSO'NSM'Ht.5UG^lR_U?rDMC3q(NEO5PDdfl:Cqdf4eOXGH"`MPQ+^Njj!%W*N"d&pV
%>$#c,P.X6KnNp>Gg,lT$0M_T@8KXa=9e"40_f/FaW@CuBLh^p%-'K)&O[pNmPR3\0U\=KAlnJ:hI0#XALJ-Ju)6h\]"X^pj00!AQ
%cLqZb']i*8af+$gI"1LBRfk@Sf`O9fh]-I0?&8">)A-#q9h'SaV>M0\AX6u<K>0_jA%Y+er<h/Le]>ncQ:LkVeYe(L;HVpp+G+g&
%r?+HS,FFURLQT_VX06rT6P%)?c3qkKLu_LC`:K^*2:2RiD!QDA5V5Zu57NV]03ou*fi70[f]A3iRXMEKMT/+*V2VOhn?-&%ooM&A
%;FCWL`AN0"=&([C1$dP()(%:,(6F72LQh#pGf/Xor)U2?dD-pG%>Y*AX5Z:koJAZN\P.dE2He9b.Em<E(d?6@+XZXX7nGd3eHJsj
%Z8:;E@1l3S1FSai:!R1\'$X^$P`@'^6]or!ChY<2#O#F9Z/b,^TF,X<'feip;PgI/e7i_t4=F8nb7qVlYS!7G/c/iE'cb>iRG[]s
%EU5YDi6Rk<+#074lm=ec?Yg5p(7UM79;+j^"cip.GA-=)G%f>S"dF>fAVSE:8Z1>K),g&mghB0Wd@uj:q0O^Z:3E8TPY>T"6/2ih
%q8T.r#rW+9]5J"Rdar7@Xh_,44-BTZL#l&/!aiR(->%e*qVaTK2HUTbMUpM/_cJF&`I9h(ELbF%!Io<HaICLPedgEmP.<3fgqF[@
%:<:k6qh>QIIhF^F;6<a_Za4T/*J%pHH/_GTUFF-%<2W9ln&1<u@ZfuR[+;sR!,-otP=V9i\`UIWYmTi(,UY2<q4s/68g$dF-66@G
%/trD`r$9M4i`=Q5*PHg'(?cPa>@<Q`/D`q9T^]!%q34^Lg*B/$4$q3art'MpG:Tg6)kVOdoa7/u+`^d;Q`$rfl%K@o_HYCiFC_eW
%B>J"q5\CXrp'=;pME?C>lC(tT6o76tYs"9$&S,L7",S&(j'c[i#cgj?Hsu4"-3NbM)aN5e02)]jR,/k=MQ7.G9DRLu?/2H6Cq?XW
%Bo`D[PhF"2aEWMo&Tr_<dhX)?7>[+1^.SM&BKbEH_0c[e(j,W["9%j8O`j:=5A/1LKFW#Fd>uIPdB/)Gol<lJ2PhfV_G%\W`>m.[
%@ZlF3nsY&aGG'5[m5$m?9,^nuaem!7qK"&e`-+%#n28r@](?J)q`u(&)l[t9<qa.cr;?9lq<EU=!*af)$,\2r?6Fau,Wf&Zh'>A_
%2oR5Th\N0s:#g?)J0Q<OLq!C=).KWkhVB#?K:Q"/hp?\E/2a9TOh.eE"c+KLI,*5-gJD2k^#8T[qQO@0k?1q/[Qg2/3*<mE8`r%4
%NDXFO]n<M"1c5O`ic3PU2u1%HOHkOBUpasu,(+T<pQg4E!*-f;j`%(D*%fM]e<J^DpNLE`2D$P[HgrZ-a)NM?S94llIF[Z^EGsLh
%Cqp6;Xps'"#eKaCk)-4AB+`IR(+-0M3;V;HrCL'IXuJ%iH#JZl,:Xnd^5P\Y]i09+]",K@1#9)FHn8eAoFrCU7e5AZbe3f!Gg<rF
%I$\Ek7gg6'JoualA@i3*algA.@in+S4!XHe^#[Jd+ga:7g`3-IOK`)j(sItN/fus$VHq%E*_04C'cJf#2!Yl/bR6/DY/J0P"tenX
%(><N<Ko+**N:I27@7:o^.^^4T?pU0m78BU-iWc)0LhRdU4+`:h@.pu9nL^"O72)de[$8u4Q9,Ku]S@e'1a+Hs2@[Yh>J=^5l>2+:
%QnP9/^tEs?`Kj`I3ZtC<-KfHYrHr3;%#\<,dS:Sk6@C`&K@'70A?1(Oo->d7R\4)"Q5=C()tlAc("R-#ciAc:]6J@4S\TkKOht72
%?dGM\DC]3l(<8ErE75rr2iX>8%495TGg?6h?*<qLS5VIUmZJ6-W1KM0mU*r[JY'%]o*9uKA4![_iB=?&+R+i"Csef@!<j=?Q&E.f
%lLrJbgNAYQUqT;DA]Y"(0q_&hEZG\7+)l(eE1eNlQiX@LW<1$:'c2=mLV1"7RqT"li,3\SG\),CP4MmbNj/ZVhKq/5'_=368^Feh
%c(b8kL5s.$=lj*G`$1MM%**SskTVY7M#TeDEnQ@b\kiQsCI>rV@FhfXcpa>YYo!Pf=^3=P'jY@"^rmUG%5ZPO/s,pnO#RS`51b/m
%h3NaQh#_Cq4&0:7CV.m>-DJdO3iZ!3rlAe%/BhFf0.^Wg^o;pQV=hlPc==CPFC,A/+VZ9G(SJU0XfY<D?2_b-c/gh<BZiBH+eKZ\
%<>KP[Hdj;5DKmA18thgo+WCRV+N-1:R1Mu[!a`;(e.IJnAti.`,=gO_fbgW(aRgn=?=fKZ`hs#@%/f5+0IO_I(N,!A'/?p(KSAnt
%3)D=5\ofp+IJbB2Z5[Z6%kp5:##:l)(p?fYbD9U/>_7u!U9e1A:c<*I\jQJ@V]qk(9JJAH#Shf2i"6"FH$d@M2'S]-WM0`!!4N<X
%N7e42k6o,SabX@Q&&*r5Yic!s(;T^Yka0ifU_I<'))0k2gKg7o]RC[5AHa_1"r9l%HM71+e(O\)flOg.J0+CIrE">#*f9e,+79,U
%qr.=Y>$Y^#M-r'k]A`.\4^_%eEmA!87`HFq3FJJM\8Q@TU\9?U#DeLTnWpKoXgrP3iD-M@ZsV9b+KFXZQ"395nK95:6p.:B?(s&U
%0$u?Q!DR]]5ee/i_MiIjmk-)'[>JVi(n7&t".+1IQj3/9N*`n@K`&[h<)Rr:N,A!iYhWoDece*uRs&AC:FN+b54<=B:FJ8uTi=FV
%hW-)Lc/GK1f:s@HUG)3B#/a8ojO%X?9eVAJ\hGaDi"PM0_<L>SpS[QtDM1b/E[`(a9dtDn:Fd,=,,mCCaR!40<6ah@F<E+d+g3fH
%8CUB9#`o>^+W"RS/ODQTB1Pff$G9oJL6LL_JnLBj>mLHBHc,qsJA.j0!V@,N4i?&PhA=3?7HCtqnl*\>A9($[*QNoM8ia-80V]7k
%l:tc-)as!02ie_MQ,Iik?*aXB2hBKj7eIeiGsZ4:Yu05=S&KNl)>U'L!?=.%I+\cVbp=q6!cu1mf9u_&:&HH="3D@_XlO<\2/Zi(
%8;4qSnFY#S&c6V0`Jq2=&1(?q6M4,Tn9'rJ"PYatiq7pQcEfFp`er/gU6@;7-1=/4dR_M,HYNA:W/tE%\I[8SLXHXij49n'nNqI^
%gMMl%54r,O_IFYc$*%gQ?33qa8*$d,/=f?^Onul<[s.uhiaiYIfk]Lbl5"-4(DX%X_EE^ON.h9#^\_n]eJ?<//BW+OQ:rS3d[\$<
%_&]%C:ZH<kJGIj#?CLui"^F)<`uMm]n<(IHT>Z=jA#i848W4IcYGn2.$u?GX'.h"7GD)hE$fus3T06p&Ok\86+H.`O['R3K5PCjS
%US'eCQ"^UpTXkD[f#-7D/07.!Xl%8L7+,W\:'SA/Ir3MBgcV85H7e.:GY_^?4_5[\G<IqW$2,KsqO8>HkBU<ImLF<u\L_DEH]7*Q
%,t097:dkh8=EX"8ATKD/G%[N?dq0-u!`#:2]0OMukbMaJ26'g7mpI:XH7OW!NY(_k"9=GnfrWSrbGi[=@="m1M@JnSQ8;s.WQGlG
%#e@!I.5@[r[O?f5*0F8>`X"eeFr.'h99"?QEBq)Q!XRr3M-@R7XbZ.JWdZJ7AdY6J?o.7*$P:r^2b]?8(sV\_oPW?L69SQfNJ+p;
%Jr):"X+;mVgm%!_L62_+B9Z^H7]u-3WkGZC^J8J/LNnMo.5&a>O9:b$?S`aH2.bWQ1og^Y6r+t-Af5VUGqf]I)=!CXV$IQaFaF)i
%SNatSVAOLW')+-!"T%VOc]u%(L2/63_hWUkXH,sSLb69e0KKqGJ\r8&?FUT_!M1&?*c\5#VoMNJO6=@@?s1rtj.BA:Kn)JU/Y#d#
%k7+qqCmIB,>7F"8Wk(@iC;M.@XJ)%aIkF98M*;Q4A,4?Sr10US(CK^6Qc:2Edr,d1>0<`aGVo<8;e$G5X$$b:=W.dmkf-i*bu>GZ
%`O/3p@/UO,![MXU91]Pqd/!(#6K6UdNF4Y#QBpoY!;M6uU1\A7!rVs@QA[\g14keB<a<TEia@&a]bYiLq_LKV1>]]V4I`&Fg<6N\
%7,9fNPd^]_hdbspDO=LQRfM``bKY)^b`SsLOYA91=1\``>k$="`6c!'?Fq+u@njEB(9>DWjGtP<qbpp-dn7grY\7cNBs.,:CZs%@
%K[?Z-hiKt)Gg]DMM]:D(S9\9r14(V<9k!h79M+O"F2Kn0#T\0uNfD0CAU#q3VBgH6Rd$?X/4JOX]j52=_gkpn;F[C"a]L'2:!Vs]
%)GG\3@HFC,So.h<HScP.eI0Si"pSUVZFGS)!PHArU0Ymm"/oSoJV"`75@AO<Q#tF5h):872fPH"BbnnF_ch4?R4^I6(=j-a5VXjr
%o^[SJ&[\#_A<!@L)6YV!'9XbFg&12M8e[A$"H>Z3Bm'#`a'93_iTIEmU;Ch1mr-aX*#:D>?#ht5$D>n]LY5il.!5lUc[FENKPa+"
%Ah`36LmS`?+?lPG1a3Kg\gC6)=(j.&lLs@*&DKn7O@`Ja<<J[^n2.T)d_k:f8eFVZ'M'^J'tBj-bAA3>P'D9j+^E8o.GZfP*Vnr7
%UHK$VVP@;bZsR%7)P`YWb(J0sOLMIo.MA-?YM=L"Y?8m"9Q[Se;#Q*\.p6*sLjX=;ImR#lmXBp'AROe'f!kT]'tijq<F8J1EHGr@
%AN=pf0\sD10TjIPC)2Js,.3L^iNaP*_!]K$C*Tf4%4jms&UZqE>0&kjk>e6,mSTTF_R9WS6jeEE$V.gGfN'de,$TH^(*.nH7Ohi'
%rk"fA6b`7"&$ZOXfNb%O=^d>H)l!Z+OOrkPeH^b.S$mk-A#S@*N*dUL'GX%K`_[mCe0n>)f0Q*q-CRntc+(ekb>[$/<Xgg[@BQ76
%5okob(=LX0ldJTu=],QOJ.r\7Xf)op$ieN!@MBKVA(`knVS>A1-48I,)k!_$1N(fLSNBgK8A4[m`[QZ<W\.sD4QQAS0eUcqW(%ce
%JleU/<>h]8<TAV;7TB%]jL;,3=/]N=G:5">Hj[MN,O!t:=4RHfKll+==$=i,;IWd7FiRHhGFCWQ0j0`.:SIp//VjE)WD[FlCq=NT
%"]`?AD;_o\LLmFtPNI4V8<0r#6EV]E8MaV\\d8`u"$NXl$$Ednh1\p&W4-^coaSueNbm$t%<JZ;h:5f5[L7"#-&uO+\qFAZodo*Z
%oq^]:V$ai07BnA;q:)T)0hP<m*-;jJ%'`[gEi`kQl0,]1b],7cRHV:rZ:V#D!$2mplXUdMhi&YO'T<:Z]%j]b`<u6D1:CdnI0qSI
%L:r*q*YFZFM*:gQ%+RGX<^pSdJ%n6$:EtC0.CIu6ip+-99BKY7=)h],aDQb"YD"8J77"6N;`DA84;$s);Rg2"dDFT%ccGhi>T4$J
%]"a.phOD)VGr4YkpDGl=<l6H;aUc3]O@bbg;NlKo)cK:<CrgRh'k`13:4)mEa1(^C'\Ue2aK!<`4?sOtjiDMmLI!?b*J=d?OSq4;
%YTXA^>mAakbt#\eX.)Q#3S;S"\H%;uJ#3m9DG-#l'?5$ZW.Cq:L129EIC-o5&8DUk:qhm]VC%D.-aK,+,bmVNjN)oDihXp/bWI"4
%(XsP74f*Wk^EOCi^:^D]O"GZ%p]gY^W/)<VHm&7YR0L>XcrV]\_[hY[3sS.S13kjHgiTgpa<c^U/q_'_eUh<fTb3:H#uu,?Lea0$
%kZI+D.=(u\=S)=T2?G&`,6@0,^7_<u:c8]%*q5mVbgqnX+o"/-Q)b"GIoWuk`iL$?cR,LGIrQd"TmKUQ*nR:BkSJq[L7M=VJ.*I?
%7C<%[Sc]HeRG9@t2Lc?S6h9-#<Sf47ZAGq%2q5Da85grlE@/leV->t)'<P.J&TES]S/UHL1[MADXY:$CQWkoQQ&IIko<of;g,Nnm
%EMZsP.I.%Mc.Q=DUt]kE-cI[KZ'lrVj`K8XK=po:QVmOk)le*m#X"SZE(,j.&\ePa,$pV#+ICYa6:-*]YWo(pOr7KF97*_)J.Wpu
%&`j&>)+FTGiOj\>+E^3g!?n=$poMnl2oeQ:A05$s#gh(JZ'ZcBQ8(nN$*PkgQ8QH.#hB9aTs1Mr*C=ndJM;_Drf07r-06\>rYTZs
%Dj(o1Uct/<m?,eJYn.=$ZkP4-O7Gu1(h@@c3.jQL0ZLmqV%suu6JhQBi5[j/NBopp]-3OM?0\D+P>[W).(5!rQ?Ri4,0i<U`:l%N
%M,lX#;t:RGM>7VXS7V/M\:0:(C-K]keNaCqC,Et<[O^'PA!m-BU:ismT&^a6kQfB;@'(8h>6U&Mq4A0u4CmK?JV:=:@B.fdj82e7
%`RtG@Zk321_E+:I78uV0E#m*'SqA@rm"Yg/8@!Ih7l142cK'X_BkC9_6,BF/IaY1_Tdm37X(L=>PWp\qW@5[QJ3'-pF1G/_X':1T
%$/Ht'mmrUc-56:l2^FNSMAE!reYWkoA"X(8gZAf[Ui?tsE"(:_"=."0=<q-T=Y3Su)NLGC:"?WYGhZ61K10&kTPl?4LZ4N8=/aS!
%DsQ[:e5eorD?ed.LnDm!KpuVa;m9g;'M_'&0I5r.f$2t5P:hUM:"<3/=LKUf@;,ru3o"H05nUMp^sC5QHO_gUB5B_"%KpKFa-!r%
%d;b.r0`HrEQZOVO7KYti.Yn4OK:1(uh?n1Tk&_0hI>['EfXR^X`ark7;<h1#@%)qn1+_(O,VMh`)%e"T.TC\t-k3>sh+!J>DT%[\
%AQag=2LJ^AT+ta>$j#=m2VI6P.WCGMA5Y(1b`7?JL,GM=2Zsj0Dc1.l]D/'r_8qQt2B>@(SUH9G7^iceafP@b<_i$f&`p)H>C7/m
%B;F)'NAHjBEu@'h%:iM:ChX#cntrX-(DP'l<K5jCV#$?,2']I2CjK`!QP9e,HmB<cXaHX#+?b,XlOV-rRb/TOCujMT(Ca1#%3C#6
%Qd-sAg@L3H5qf-AJK)_6Loh/8&f<?I7)D4cT9CcT<RjKP\Y9ocG2AC[%Z#["$lc"DLdGZ?b8$j(;t-Pll#*7n3&1't];W%Q1MBW:
%>S/3X"-5DY2MK!QI9.Xp>19/>]1)b51'SJe\?f_@bSWU/5e[bOJ4r9q0_-Et**tHA",=b4if7+C.p^*D/is>GbM\CIGtf/T`(!r6
%95p(*VB-;&8I@!K3j2LE!XhR"/=@^i.NGqt#4dkI'a'jKB7g-pl0ubUMg9.PDatB#NbASeiJOG&D'C2=4/eT*$&].-J^]?8ruiRh
%HZmShka.A[3'FoRXo10?p!ZA"[b9L:e%b]cJ+:E0s7#nms2R?O1s,TDpqM=WX5Es;hu<&Qs7+\_J,$Vt55k!8msk,g&&7tqhgbCT
%5Q'[<T6%aH)$Bg-p]($Ea5?mZ5Q!-ij3K[^m[&CskC7,ckNeH7a`o--f5L]#]`*h:QL`)rTYCDXJi3Su^M'hNU>l`0q\T;/T:V0p
%o:$9jptO^)kFWofqVN+bYA99XM///\>Z`0lDS7Wo]%CY[iu-TKkohN`gf4FB_/U1:^a%H^)Q]eIWG5;/eOZX5@&gfi0Zcs4SgfZ4
%6c\r$>C[N\#Z[*hU5d6PD@)"&94n[Br[#[^YF/Vf:<l\D9<%:"CKRn5Wab4`/#']DU*`0,XL(tT)FG\,RrpX&^@#01INV,4p[8^[
%'SAKAP"He$I[tH]U9#>C7As0_EKo^pE!"*GA7A?i*t"l#(o$B3<O+s61;K=>gCXQuk=K4hA@'^q+<qBb$;ptnJJ#Fj2=SThGc;HT
%:Zr'`$>r\YO&r#FaNFH4;ee]B6(mH4]QR?hb!An\Tj/aY=Y^Vq&QkX@SDoVN6B#J^e9<_B$1Z(\^tV?"Z>Z/s%!RN*Kd\H2FE]$n
%G"#TG9>3j!#Mcj<-Xj%K>(Z^lNZ2BLo+J_N"7[,D1+Q%Y4pMYm/TO^a@0JarWQ`>Pj4!,E"545q`:kEV@k'O:F,I26\=GL1bgK&G
%klithcS1:i2g*cT3unbc<VE1h['sbsEqdR^bksb2MF@<R"6YpIYk.4h"cjXV1:R&kJ9'NZ_Y$23Gf9s;Es#eLS(FiIe`Z?fHiaa<
%c@Mq!3AIN."`j=tK)(jt+q1\.V[9%jF%OZqJS4^<m8u7kG#9VQkHEGB`*qKPV@spgjY?t$B7p;N>*OBaDJIlaRIh6JG7JCs=-i=r
%Yt'L`_^=b>gnFL6#L-O3W3&igOtQ"Q&N7Qf&I&sKX2$,C:i7):9rTC.L+Um=.6e*IfQjk.Y(-g?jb/Rt!NhRV*3)\2Qfn3u94FG9
%!t7`nF;</dr(7;deX$t&_dM[e&`dd\KFlY=hpdfLMKX4g.#;/,:$7j7,cY,6"VriI0?RS?>_N11_((&$HZ.?Y-/a3->31-s0ofg$
%ZBTlq(K^A[R9`eST^0D\0qNeHhAo;)nNpZF>MV&Go;4m4;Ft'e$:Jp];qWaAr;H1>ad_LK&F:GU?.-Y0YMZ+9F/dc0kU,WY=E=8o
%`L1BaE[9B/-l6/n5eg,;Y)Co!gt?`JYT$VR0!!h:dk<2@/e3I<Lg_!=-]:be_R0fjjYBD0Z`FD;#R%+?"6n&@/"=VlE0[(fB"<3>
%-)6-!1;eHaCTi-cPPHKaX"maXrG+@lru7QUG:[Up<O]kK\qE8$4O%_'623)a"H]Gh0'K)b$&SiGNgD?IY`:#[DE41GR\`A)KSCAi
%#n)THA-tc;=-3a"\=bG6>LAi_+;51.*7&NCA3dDpg6QK.NJf>K^4O1#WCsJrZ^R5[X44FArP;H;aLh.43)IHX\t:HWL%%r2a<K-8
%0lH2Ob;:"#L?IWRFHuAB1KfV0>p'^P"L?S^MQj]G;2\t_<4#o%;FE8kII7^ii0(E'(2sbo"h3=%_'S`tb1Re/AlQr&ER+7n7@jc?
%$gJLYWm6mimu'oH,;73l41\J%X&nP8]t1jqiNN/I`]"TR!q0Q9!EpY/"c$Z/e;O.1WJYbF+r2N3o8[]=MU)B.dT<deC+ZE&&g!)4
%I-:>/(hBp;T-e#J"CHj;,7\`_T_@_"QM%Q3.k^_M>s=P9V//Y?$`6qoI?uam"C</&N-.j(,`Yh(Q:bP%;**uGCnu*/RGdfk6_8$3
%@D`Pu4(L<9k")iaGW:Uj'02p8@gk5m4'8K%"V6-mMb+71NeoRu05O6W'%mUbfkt(Z40FW+_I0<J"Q7G[J3#73TSXkFK'gD&>S;O1
%!p?(:jFe<^%G?2,Q(i([h0]4m@3M3rS<s.tUJQdF*_E(lBV7t272^V(peW+T;if*tZ6bW'f]k^HJnUSGj?*+'=mdCL?u$(GWtg_<
%Gb:k_-97>Nr=Dt/>sDeqgYgNp]Luce$4&:PnN$BiP:Gc>gfek$6L4[g^D4O5(QpW@jg!VK)bW`2_!IP2J"mX06<47qo=9$";P<AG
%a_B-CFYfnYIeQ9)a=[50]e2f'1o&.dJIn<n4+L5)pL(?aKGQ_hS-u8(1R"\*&AJOmr.`o?i90A`on0fe2erdX!WiR;`!@qsZIe,-
%WHVh#c`oL"Keo,)pXK72l[Y$p%[UF"]m1OAc4=k5>MDuA>2UJFD^*St3saNL?.9Sh+S*3T.7Xp>iOLVj.I1?(3IT57a2Zf0FI>G:
%:Tf=Ic1BBWNC4>!cqJi&>$GiAf&_5hkbsQBRMkWp>a?+Brg$9LXfr&ULc!S.6m>I,G+$m;jh"$'Q(Zu?!_`#ZM]Ei0U"4Kc$WSRX
%IX<mKcl`Ll?;"Il1)ii%RFYtQ/Xbl6PL[_H8?79S=p>edU[=+U4gIn$dXY%Kn/u]7JS6DG7Yq<[-A3NWL5CXj1F(]oHk96C2@oa=
%;@ASVqM#'lk[84fD&?n6gf<0Bl!g)>1S3(_U70^Dae^/R4Rcf[OZ=\3\gWQi'?U?U[Aa5!m?dHTrR&cjQl5s=V58oY-K=MI:m%2-
%pGHq;Yg;O_[HTSb]P%EOc`T@75<OS+!RUN`LP\a^.3t!qKU"gc&BupO8N7h(<%aN$)LT[#LlNZNZk4t"*LD9?QK"p'Rhes-hgkW=
%?nQlN2jFHc;Gs>>Je_.r8ZU!W)+ruBOHfIj.*\m4NhnRKXLV.J%?f=#(f=#'JlRL&B#LF%Bb7_8MlN%DN=7;JdL?f+\D1P<-q=0f
%$lrrs%<n80>D;?u08.T.HA?38kd!#M..gC]Y.A&2XCacVoEO>Zc(Nj*B)2ec22lN8_pu+*ZJ4[`@Ed%46%sa#s+7Dao"B6Jb6+0#
%kmDWq-Z^u/d\d@Wl=GDLJFdILUX^!E/``\:6"k8q[I:tVM=,:\(T/:E^"d\82pEnKgp(0j&Gbq0M)ClF[O%0bR.t,/4[3*"HP$gh
%1`mqF2tf#,N:4RDe!Dr.fACm^5@$JE?_f.P.%I6!/a?+-8b/l#=n4sn#V]5E]WKPdXA1.RQmroIN/d@V<?\.3fs^cKrfnsA+XPeR
%/t&gU?I1!VbkfV630H76Xj/P1'cUsQBeEk[H4Ah%.q$sV5rP/??QC%/ei9a5R99ERDnU1.&'q]3Lt2f0WUc[gU@bYFViNf!<%4K.
%mK&fS.U`&]\P?M!Gr`Ig7G"WOHsmk5\^VEP[Q!gaF#?HjVQ+DCmiqTCM4J$4#>i07<s[qRN\Y503rfD5Y/<MTDmFX%S+l::nrR,@
%>,-U(4dUr.d=Ce3<Vc]U0l`\93U@OW3&W8F=R7s)&_a?n9[;TNZq/2>$I?R)X)J`9i)_!H=O$c,JiDb0U6j;]92/u(`*?3(l\:dF
%npQ^s8dWHEra</Q*q$,C<5Fi<K4HaEh;*l;8KB7X/"0f*3qHL)E;BiCOs*-bbSaUk/F2N$U9eE]"DEZ%C8VY\)>hAak]b/9.!LIg
%$rH1P9G[&$>jD4'lW^"#'Lhe]ZpE4='"G8'1uTmNIu%R*\'0PZK1'o<a5@Pe>7n2(_:bQ"g-26m[trA`dRuH_;6FTU,O5-3\?-KP
%aGsFh37Q[c[Yp>)h;6u4#*>a5nhW?aI`QK%GA*=1$!BuCJS3[:obP\,!Ttp#2P3=V:hWl<3%MSbF76RFc#Q)U+id=J1#te.G5Se]
%'_jCsH&rmf[WmM%"ICM6'k=mbV$j$+gtS!tLk_"ES<1HkT>8]nUFNT^B^'DX.55`SZH9+S\I:/T2D_(\k4S5L0F;P`b\;1]OOI2m
%iN9&3Lg5DG$9.B<:WsA=Y$Q=^AY4;sC*hDpR`ep$^L=[4iJ[QEh/@A(2&Q#Q$H_+7nh[)TaTG4F$B,ntUImm0HbliC`/YbYjrff5
%2J:W#DKBEPD[ai4nOAG_+?+;6i:*u2At5e6q;*qp@jZj\`_sg%6S&%9W^m5HkX\c'<ZE+Qa1[$9=NjCVYQ^Z7c`q@(.YNRE:V*eb
%4)q'oGAoo^g/rLm@>=n]($d>IR/73o/sg]eKi?J%*Z;J5EIHOMMV/?ROE9W0fDt5Hm9n_=MSrabngRK%L.>hhO<HK^&QF+#M4`4u
%?X$^M(Zj:K70q%H=8%cGU&.1AO.6q/eV]B>I)SD)g4\V=4[p75_:Ros+4Z5:Se<o537V$/+48-BUL.Yt+TfDR*)GBNpkqZ,Y2[X=
%Jc"hDd1'=,V)i-[U%%ba$K3V]e%ecP8**0?pG-]`6o*#;EALLp-S*"1KBS.p/dn%]^.1+.L(\EQ\</7:cS\'`[RHjh9K/%]%%7d(
%cZ\\*l7o=+Q$gm\(cJu#Y>A'<-W*1UWtma%f-^W'<Kn4)C!N)4[$FfP4f.)us/<O6BG[QZRP4r2(e-o9mq6^InACp\^geK3Fk&0/
%eRA_0Z#KXuj^qcUP_X@j8Ib'"@3Lu@ne\1GH]'pUp?Io^jUe$^,5_Qe0PQ6r3=7+fHa/PZ#$)2&_sh3!T1A)hMR8X@Ka/d[`"K@S
%eU,mH=73h"GF<7Ve/V=bSVH?iZs2Wl-C>h_V?!].]:G`4"TG]dGSCW16Ls>Sgf-8N<_(SC%(hO3b^iU^W`ahi=A%f=jo0>0FO[%"
%?W)fq0?S$Rogc_tTo%jg2i5V46A.FF#>F`#+t]_P9&r+S[?Vl,U:\M?i[C%k"l7-ucSr89Rl3=Vjn]VcY0(C8VXMKlTB)ImM*$tN
%NH$*ijj(d/.Qen]AZ.g4TcP[+.b#fo<XEt'@5'7dFPM"gCG\qSc?2Z]k#uU<"iD6WRPc>nC_q?2"=*!`m7r(gYc=W-=MH1MK*2<s
%bHpAd"*UW0(X<BB5m@UA]Eu"KDS(;.$h%$r:Nb,@0R#/uX#p32_!=EQc/]R3%d"Jpn?iLb7WoIPaJ%jIfLUt#\>mEP;S!@>OL#./
%W82X</46>_G0H+MHF-hD6=l8^ZpmGcUecNhEF[e-M!]_-4eZb,KG&'SlAVetK=b)gZ13M/!P&Y`=]ITSKq3/3V[H_Q1hqhoasOrg
%9LF+TK+a#FY8^9ie?+OF`@Ll*`:Hu95hFfo9NscLK0!54KR/ZL-aEgY=deReHE00I?>Dh((sJ>j[F@TWBoCt:YLdhqPKN5l9K)6W
%l,QaZ?)Oqq@U8HV$-69*?K:'HBJr7<Ehj<DQ7th>0aYl,6:IRZLV;/=qK/.)bY'S,'IH^0c>SDpCJ7HOIM3#?bJddb-X^a0)lVS_
%H.lX]bi%lq7sa^j/0t7i\+pd[EJFGPj<"2mYGaO2-s61/ije$2A0ot6hd@;`kYXp(Pnc=4O&ll=&rJeW7^]l*+0Ni]VCBfdfY]J.
%QM#i.:"BJg;R=hmC4%A+P6.1C_0Ii;1@eAT4%<?"f+Y'XCH"qrqi_M!lV[:*#%da^kU)$Icci>H7SE"=Ks@niTim^MQm*E@*f;7g
%fH;PYWt(e.=ZEhVjs+!,9]86K5^js3n;pO!7t1[O4%KV[@4Srl8-CQ!,_abSr@TE@K5Qn09ruX4g\*<EMAIL>*erQ;5#5s2
%Xs@u1I7@\i-&h'7FK9&RQrLST9Pq6omgaSO*S^i'6BOoN-/5pl7F:sl)Md)Kh@%;:ThM233"TP!_e.mg]A(_UWj&?V\Ka\d/k@_<
%2M[K3L@8eCmmk\'qP3,3N"\KT/0,D!CN3$ipt2<%%Q4)>-gEuefrUg0Gj:?/W-HdX&b3rpf=/4;0/OOdFEpQW?)'ruh]d'a@N-4C
%4d@O4c%d/jcMs4\/og9cY/ZXQqCluegQ#POmh!0B<J=P7245&)6Nqu)c2';QH=aqK2&AZ]7Ko@']a[k=!3=AHH:'W$^i0%"oWt9<
%XB&7F#:h+d#$H[0HP!%1@Nclfr.8NP7)GQ@j,I*&ojj#\+b7oSU)MG:1p_I;.Z\jcKci%qRIh!I9%E/VjN38G/@@gAnOp<N:gE%3
%-pq;7[(^!O-2?,3IaioBi?0CQk&(BH'r&N2QrM^(XQ`/mEnOi7B(fe>nEEKtN'Vl![p]<M<32((7&[ng%"s_AIEuA^n6LJ'k/dkT
%_ambI*L8ABkP+Ad`kGKJR&OWWN%D"^!p&OoM.;bE<%Jp.6U)E5C7'3V76;-*@3<\aHF2+B3)*u\=G$j()T$3R/-Z8lF)C%#S/=!:
%7'aKC;FH52[g;7]!od!)aII\T(C1D*CO]1H6--h^;,]DT%OnHd.m#[_dRSi&=e>#Q&\f32Ui?Dplge7)WVG[&gBFrOfK95T-0S,r
%oDC"GBgN"Ym_4Z#@k5E.*_mcL(FJa7!dA2`)k5ZVF^C1^@I<<;RQ`-T:QfT]()'PFY.JPO4U:"f&+TJI`t4^KMNl-n?un_HR#Xor
%1Nns?n%/I@k1&kWDl"_"\kJ5b(!uD0\R1WRrY#dLj7V8!a&6!fV^KOM7E:S'Kn3YU\/5VuV7iO]Q6D_m5SJqdo#4S&'uNWb5XBf*
%(NRWL")bqLg#SUDCc8Ne[EL$48RquQ/<L:1L+1!?<JVJVeJXuFJ9O1TTds5b;cbE]^4C*V@O`;u[j(>o7b]bb,e@^dTk`_s`4`,I
%W6$43(@G-f[52+M)fN>$O][9`(PAk!!@uL!A1*)p<h`,9aJ+nmc-@^u!dIplA5&&9Y4.0L128)D*$kbt(UuIU#9gGMmO<#'?+UPh
%2LQK9P]$#7('h(\4r<mSK>ec)l)'7_)X^@oLB&bq;28$%)/sd''HIUbMMb;ANNGP5k-?!W][`8.L!NfPK-$_K!/IQr8/^hsKU)?3
%Dqt;Cp]gBkc#.W3cjYOL%-K+7ble8iUGLBhC:J[rG!Es;oP.s:%uEQ(;c."30XB)l45V=L&\EH;YUr48N*C$,2i\7m_.%RnRPd/Z
%/<-m.c0^r@@0Y_T(9"Z&\P]YZD:WgP*`;Mc[[RZL2K'6?6MfUU:3E[.GU7EWq\D,q#B7:4b&^GH%t'u=/4iF?50P4l;^jZR>^,gm
%ULU[E^dNRnhJ+QAII,oFU+G`Cq1>qIpq7gDhgo8UDEN[j=0TB#m*iYtntSqa/;e;S>@:8TJg,ZUh3S^*B\lV0$oDjmO&'0U`/X%[
%1u)3^c+a$&0o;u#;Eug98(>;k-p8]gEid,]`?PLDM84FGP*,1HiHCGXW^RkU/kNNOQP`Su^kECXmTnRu^q\WL75HlM`6b6PZEkIK
%\@fLY1]`;oQt%!7_(WceDRVsrm&ALDHS\Fg1c!*.h/JKY$YQZ)DBi<u#6!%H67&L5bde5WKseW_.!"(+-.aA`4eatuG>8/Xn`1),
%(R;;I:&/fJa<b#X'$SB/5^?Uh+e_hi!357X<2?^<NUF@8YhZ]9!H>j!dhbHZhd?2PJchuM(J]op[j<R4JA2A:W".';V>#o5aEL8Z
%X1=IT`5@:Rf]La)0*_5l5=8AD38d0:6P2-m3;Ag-fBK[&@4*[1@^,c7N'Z52QMZtfBt#02?(Ol`juL6`CW';e)M-D/qW&sa_Of2n
%F+]?bbB/7gOm*XNj*`:t1aX7XkBDMZ-?:'+P]UA3l`c19a*IsLUPXG!MdLD^&bQg'"&kX5I;6DZA#(F!ViS:&A`osCCq)-t[q.P7
%D91,jO\o;Q,)3daX)E1a(0AZbM!W&!6Dm1Akb(aMeP%_R\31CP/EdCG[,u3+`=Sj\JaoAg:"H!C@LfE;4S@l?a]H9+oChVlWesWO
%FWS&A*loMsF74#:/*qdgAtuR&q70oO9e.[Qr^d'$<Q(j#BU(3Gk2jdlJ$ZKie?4,<%[Y@GF^3_](P&2#[@b,I\#%l7O#$#d]9N7A
%pX>tIG.!hc?CEO6%"L#t];'OLZFIJX?\ONs>/SZA1aj@bc$>LWN<hDQU4[#+O/ng^K2Z,N/;uJRTA=Qe9`9V)_X.NNY&ma=7n_H&
%>d_h?.BIq19L_5]93-.??e[hG27&^c;f1#RoWX9tAgH7d#+t'u&Ik*22%rEH`ZB[7BW\o:i,)`ML%H)3-Aj7^PjB/XQ[R(6qqoGR
%h:*>YU^./=ZnBs.*9;hfK*6,!f./tIrNJthpZ50R+:l<:.Jpc7OR'm(%g&2pNSh<(d+]O_[/?g._Q0['@7^-hdt>$>)?[gem['AV
%R3b2]cA8sC0W@Wl0#o-#n.AitQ]'aYE&\jFKq0B*7au:=GfW[KRK\(cMQ`ZSn-D/oK>:u86OTt3PKDGkUk!L;p+eVBc?nF,Uc%s$
%N)A?5.dC7=C;$caJ<61+oN-q$.Mf'>,l2J&s->Plro7b`qdf@2i916BV]*6F>*CT5IER`1?hcY_LRX_hj2ms?TEhs,L[@[1)r]#r
%\+m'LnrnK?%;_I(hN%\Xk*\V*,l):]BOTCQ\8E5%(kD>B*?`Vdq?WFE@o-$cV0#qe!DlJj-_"_/a.E`>Pt:ncj7hA;O?GRE330Ru
%f3C#Jpu/gN6Zj2J496)COqhJ0*[.<kULCc=HQ-j/r48rmIXuH;$]fKFORAlUr=L%jeF!U7KbJpH1@_Q,e`b=(2+kB#r[_*F$sWGO
%@Pqnn5Psuckb%Ghm=4C_`p:AV:a1DNp$r'1,Q5*\OkpCmU)?=-YofX1kk+Tu@T@\0F9=FOWJYk"4k%bB7`emZiF:DPh)ad<^^5pb
%^;`oCf$J@^Bs=?/X+fNTIM9)<7dW,Cd+t'12,%<D.psr%7A=NNC$22\2MJupY2Kc4d&)f7lt3r0b$EC3jFrA7\;EV?'Xh9,2Cn#r
%Kih06g-d.#G0@NBOh/YX0p`NnY!Tc*!S[/o!!Z@5TZllB\TcTbastf'bI1A,/XTpjr.NBoa;"RF*1<E37XXrLIpL?VLZ\rPOiSP/
%a3Xd633VCjG0Oq78!V<LipUmj%,h6[H3[(#pqMk%La>lGYsYO9iCq8l_EHr.ZGKm\>m;kPL[\2C)?)\i<0ETJGQrh:7$p@`,'s`(
%(GDtiO!@Si-PutI`Qtmj[[2&>`b:F"?Ju].("*P\ZT7Msh=BnYU6/BJg1ob(q)hdfd?Gid^+1]k\5PtuW\)IhXf;kOD#Ynp?L"ZQ
%!L?tsSeWKl&PH*.3nlE'h]Q$A:YM`l5(O.tWa1Eq^c&DZ>?h=#1M'?+-rGj(e,,!jZpcMX[23[]5Kqt6'LmLU]Cjf1ILp;k&0h`J
%/G1cO_*119N-b)#XXSWE@D3m#b0-(5qf%uaPa52PD`-3LQ0NZ+TR&\8B=AO16FB;o't*bPf-BOth@>bb3FJfh$S,*JO'8%f?rWq.
%8rd)FV&Uo+OG`S)n:@(0NEH;Ee>b%\ea;4p)VBK?MC5Y*.;'+nX8;_/>8rpVG`?AoO46gCG.C+%o&R7<b'KNb(H"VH<c^X/.gos`
%"j,D15Rrb,m6SgA.VS7oq_:@4SiQe7=D%><m)[$0EHQsHn0ZC!QEYu<$#BU:4&SJtbe#A3JHIf'jP>T3)lSI=[%0KL4K6Oe.j,ae
%D[l#%^m\KeoQ?OBHNs==rD9)sbDB[['L1e5ZKI@rbR.TG8F2Hkof@]8+hf+b]=KD6Pcu(kKm\9>+WM;^`M-cF^37mNp,=B2bD.r^
%YRW]=4rj9VYY9R3eUDiK<e+/3&iOT3Zp:VDN%'EchW<E:9GfYeJ1N?"C5\0Q>2MM:BXE2ur;b"&"UVZf$mNlt,_6*-+K6ifTag\q
%mUCh57`TE54%<?/j#L;Fihse&N]]EX*5J_QaQXVr\V((C\]O,96jSR0%d/ooFnrC+VE25mM2P$hRef<!Fk[%l7dsi9A%]XQU2!-;
%.@)(\m+nQZ.\U2>]-8S#Z4]8JJZ<I/U0LPa6er0@'Y7jW6JmUW$Qo]NGG,/o2I-<"-HSrKJ6*SFBcrp6/?i$jf:S+cQ3[k]Mj^I4
%Ehk0'4,.mU4fQWu(r;2&.9?KP_L.tUHk!@q.[6(h%pr/o5@$P4?fGfE?,(otjM!3?Ht#VFBG1V685b[PQh#c<KGRm;eId1eaHlSQ
%EXQkfe]EHE:aEB*=?(mL857%"(?P6T'jX]/Y98nVQ)bc03bOcUE,"U2!E6^Ko.%%c[RNO*cM`aeHe!.eA/X0WD$EUT@6a,j[K+i1
%<9kg7i\+M1m''m3"TY?peCee6YshV-L>Bi:Trtdh`)cq$1u]^l>d(B2][?Sl^"4uCF$2J/&?qjN+cY9n<5!>b?*<Iu]7P$r4_"I>
%Rt#l3HdRhcenlsoLim]qh&W&9R2W\SO<GE=)WbVK5DHh<X^BWElG4sI&EN#KQ2+Iu-Ma"<I6Y[-X+"%toGK<eeAStqg]%Hl5%D3K
%-Z`]7@3o!J5e_n8LTl*L;F&2LD!HbC"V\Bj\8qcL&:C8;MWP+#JGJCB,7l*^2Er*8_8^0RACsk(iY>sC%\"OMpRE7$3KCf.DhhTa
%L$lCH`!^h=D!eI+II/&P2AOk9\%A!8j7%4kMfe;iF-j/B5jP\q'uQ(7m5%Y8Kf@:6;`[$#Y'f6plKI=]\hK80en>T;,:VZ[ose>]
%nSXA#*LV.GGg^quDN)ti;Hfb,gP5[^eF="_I[Q_abq.jT2Trb3296$%R++`hYHXi<OL$Il^BT)M2puo8r6Wq8oq$<IfgJU+@qL,t
%q5?#27GVGr1_pUC<c@"3ZbBh!dr[`1jf4tj1O@S:fL0H&S;;Gl(Ot8$cWoC$PG08&-oKm7a;C*nJB^3Ue.rr'.d)D`SuOY9;3#Zu
%`C@A'$cT-*"?.H&o\-Ue"Ylhkq)A#M>0*!*.`WEcQ?%mp[Ob;#9m)b:o;FDO`_WlM6dJ@<'#JT%lg8A<eBkKaU'1$i6TRoL*11o)
%E%'se!i4T!#"PG+qg):D$fJ<S7Q>om_`E?$(e(A6CiMCZ!J7#F2`-)!aeXeFs-?K(*uNp]G@(2]Nga,Yh$Om-OTYncW$Xnh^NORG
%04r%!IH>?!JO./->I.M3Y"ch;L4=dc*[52#k>bM(,+Nr3d59$U(44sJqmqLdMAoIb3ga0@Mpo08Se2F6,iKjAV;(0QTg*ZUZR)Z+
%+^7g,S"'7>CGTfCLl`<$7i`7dT"4Ia'[$W46gW2/@_"O7Z""[=We(;c*#fgk,f5JemAmL<D9^7k_Z$6HO#1-J!E@';gX/#KblAeB
%j&rEO!r19@ECTtN!r,AmaH4$E6CB<%Pd"oEaqT'4KPoSf;uJhqB;b^+Z]=?QPi[Enpa],YrO4kZn(^gMP9J\U+/Sud["<#6@)EW6
%NKf,'8,2H%5p]7"aQYVk:9Bn\#VX^FbY"rETgTb;8lN:,i>Htt7DU`:Ftu9]ZtudT4!]<-g.'I:.TJ@n<JX?g78[nMXslj1d'HI7
%%fqeK>A!IBJB6J0&EUtW&"1ONCJ.q_>Z9P!gdkkN:'6Y/OH5X,hFd]Gnt-1SWofW:mhaSZYm+Y@f-]Q*kBHC8O!1q97!V5P\f@E'
%4]'Q#&gh/'6MP[Fik:(!DSU3aO(NS#N<*IU/5_1A,VBP\*V;FFM(SN176O0bSGie\flOS:bQXLFKMhIQQ<6Vrm:X;fP0qP=W\LuY
%<S#rko!CA2B4d3eE,ROPd(K'rai4=jC'P%Yhc;>d5\]Mun*4p&erKV6_el]9,O/u+9^ZNHO8^hK;r$l<Okn[p9e"VH3\=O&nL41h
%T5O(ib<1V]&r.X21GLk:`6j:^CIGEQ&#4:LHB9q9a&>&FEFg$62^nbM/4&3I)rB`N>Z&HLTuh;O5;7aFl7n(2oRWO>#&2(uC2Q]+
%+BkeSg>shDl6e&.ZPn@3=[o#/n)Ab.q,iut6'#+=N?OrTF7e!?.9Gfo"dCs)g/?=H,nniq=C"XTqD4U<P:U2p2ah$?Enf24jg)j8
%AetuhoLs^V\(9tA%7",-p+$PJDp))5_e.K@l<T<QGL'l+MI*G6-5b95E=Y_3\AafnXapq=_&]nW<hCKm4B9%KFb\Rt<85'6VJ?1(
%fQ7iWc0n$V[Ki[F/Xa5,-MA-d0FDLk8RaFX!bu7;+Eno8,]M8/B*or!Te"qH3Urk'dlBE#E?TF2V1=2&=Ya'',9#nYehol$lU25m
%k^*`2VYUFl(3p8n?1Ns2l*X=%Q`HShqi+"b#">t3KUc%#&umMqKL;<GX3m:Ii``u>1m*ea%RBF%OLl7VOUDQC$.a5njr`EGP<9j1
%^>]ct@7>b=o@b-ocdd6A-42/4116`A$37HMU0GX78j2uAZ71.YjaNWQXR`6m;q:Dr*TL:UUi!5?m79Is!>pGsR5>6oh8#H@8bX*3
%Z'GodJ#J@]]"FMF6XAp)N!@Wh8'bOS^XVI-WP)2E%gK0]Y=a%\7jpe2Kjg8NZiF=!grhF;P8iP0(KtIU&iftkH2PpJ+^SK$.d7e&
%Css(r?i.s>S\AWGI(083nkFWdX[m)`V7&&l``[lP;`-BGhn4M2aD3o.j^q@<rNa1<J#.?'k?!*Ihu[LPn=ot(V#;"$&b0dNG+7FX
%VE$3=^l7ohn,"0(&`Am=i&VWCEZbQ7a0I""$38T%j;t!!Ddrojg1,dM4<spkN%0Hth\R?N@`X_+f`TpOl'b^!MW1t#f5,YJ1/DLl
%KH\RbMib_m\#6a5U;!SSDFJOZ<p3Ch,&f#!Pm)K00nfEgK8Y9]dh2i^>Aa(7#s)3gqfgkZ&=28>H&;rn@JW1/g$SThM&#c>,O9KY
%N9%L3!V@iT#L#?2F[lEsHija0j')1c#ebnlq5;lZjc5F;FNP@?S3iEqieO"4,NKmo;$!;`o`.1m$lnX3h">_e>p20bA$5'A<95WE
%KZ:p^"`>[N<)Ls;I2L@sHrl+?DV:JdS!G),@<m"rmGT=`!T'8@@;"e)D7tb/!%oWS<USW]qN<5C?L0K=\o@TbFQ8csCruc5Dpfj'
%0-(=^+X)TM"*oDq<@\B&(k0q-[N3RoRAl6Yf/;sSrof<n?Rn2*3#5Bf=Z.IdWX:i!R/&e,68JBm5]4G7<5\GG^W)0uh:Q9rEEKV&
%UqR*Tr/Miog@M?.2NWhmGqUb$$G-Np8?(b942N>#Ms30r0%%UF7jlJ8D_H;W5"o_Q)S[L"MJVNJ0@^4m\:Q%;K^\kW3l#,[67k[S
%+(;^.&Y!JUer!?818.uO\j`VgW[7Aj<]>9q9UTc&c\?M&F[n\^j?gAR#O'=u*Vi_u`BmG_4sK0g8Bns^9:3a?^bf&a%FO-)3tjB"
%B9KPLPWd+g`Wr+cALJpfOj$bdD3Id@/GHAH#ZT%i]@g.*PH_tJ]HantaPTBa!5/KDLRg1eNXubn)i)5MU6nC$f,X6PclAI"#YO[>
%G!3j2#TmLceGG'>(2X1r>1D9#*j"XiSOP.#<Q?t%-mpg+jP<:jjIuY.Jn9>BPGX)'h.15K3EFgC-3oHt.3r_ne,F#N$b>g0>U(,g
%r&Kn8O`n9gO<eS<&Y&SNK!!=9VW4&$U?tA)kQ0B`qn5i3lnT/\2+%E:@5\sbqT.O+4#i9Nrc:n+0-G[UoHfLTc"cO;iV&hRf$W_u
%ku'Y\be3+o1b6H[YUQCaOB,Nngn.eb%-3"5O/joP"!;egE0+76lB25)[Rcu^&Xr41L2uUe5_oa<hiCFO8<W[bUp@qU!1E[K8<7W8
%l=EtEIgH!b!_I>t/kmR81fanJa<CIT'E3)Sn(/;o?LO.7r:dS6?cRQ.]u/NV9Do'Fs$Q8;:Qj%3ZsKmH'iC4C&W;(+e_SB,$t@a.
%QdlfOou>Fe*DKa?.*M=(Y*SE?E9>.r"DM5;3SYHdJI(;0RtMFp-J:66d3fA$)#3hm[1W7^E1g2#bt_,Pcm7JT*1?L'!=k!GYIlak
%hD0('Keh<6^LN(tNVmko[\__c^PL)n6g8sTr_gP5'[oV'/I3WO[0Y;JR?MM]62"/R\06]%re4XU"Lgc,P*XAmfF=."0S>or85tu'
%nmtW/kW*G'Xo[d7[cm.0qO#7.5VUJ(JJWke5IVTsomdH:.IIo;YXD.i+Uc.M_MF5@7o%U;:K&*+,X60J#j["S"r*#J=n+l'UKe#K
%Y>D-!#]C`b=p`bFn^r`LkVbcI]d]lfhA<-n/MW-DH/h4d/j6PH.1ZsWD".X\P>/hi5\J8ofn,S[/Qc,i>,0F@R#I0&ER%J^5P,L&
%+9G6m4[5'tb#_:'>Hbbkkj?Uh.h9so@fMlC;EKEQ[RYhUj&g3Ec5N$$D%76!-C/i=A0Dkp6X8]KIi!mS'm6r<O2cS%(*&$GlnsE8
%N`=e"K1WFRF1W:XOEClCJ7)/jEjnK5..)'8POE1UYM9p<1-F$Mb>eP':E_YK>r.$7dGjROSju'2+B0lV9J27XQkAOB<qsP6O.L@N
%Y&Xr$_uusAZYb?1o=cG#(GESf')Rrof5*:Gb9I?2];%"A($Fu/#gZ/70kt`i."h?GV>eKLLsK_S*\p1@kUplRDTRK@M5i.N%LPo!
%*\G&BXheJi1FcnQS&^82U6'9PGm$"TChC^_W<H.E'uap!S]Mng'Io?>]r7BMG/'bW>$Cd&bs[DaD:#`)UgrDsXtbOM27ltLoi"5i
%!W3:KgS5r[_,9\6,n];-)hqa&AonF*#[<;Dq@Ku@;HV"m-1KTih.;j%lljTV"t9JX@pP<I_'j3.bB[2O\-hg7g27N[C4Y"Q<c!oo
%nn>mQT60%lW@oP33IGETiu4D?jU`+i=M&QPQrt^'pL?jV6!k3f8%h7J4>WI5ZmQJW<TK85>)<oY)lW!2[_JOKCoNf5WOuAgGXnu&
%<02[WO$X>&:?VpBB2gTF@$c/'f&NhWXW1ec=nW/.=R<PVhn$3(.'IZiN/RAg#Wq$W/'dJS:!(3AbV=\uD]rpYp=\.PGbVjP.%P?k
%$t6YlMel(#S/HORO-5lCGoQM:X5id=DhU+Zp5VK;\lCjAp%g"g%Q5`P.BYkk\h5Fmen9LjkMh8L\dDhZJ_dmt5/6uai7\iA.$0rG
%fg2'"-3;kmL]aaHJE@:(f3A/W=*.1?9i"]u-t!Lr]$K'79#`fN6=o\f[+i!h[X^W)ThY"!1bB030lQkA^b'"q!L\2u:]*_k)i$46
%&s=asYV_YG[G4.Hf_ck5UUei!DrXai%!PiGij4&YE3?1X`ONi=(h!uA5)VKNki!cu#Eb(d6?88g\;rB.iYUHioI`Dg=OO>)DT'0(
%f,5)W2`4tFG>GoB*p%F0!cqgGO%4+0,(N\_Ls/GJn"mjJhglU[ah*JYP:@@pX<h1H@<`+[SFHkuSnj9Yo^Mt"U+jH'bXRZT1"L)9
%AaIH*1*o]+Oub(*cYB5]e,W@X+c4#J0P6R+H\<<)E^6:t8s5#ql6XePbH`]jdSTOPs8<MJ-20#Rm&Bh`l"fYVTLPnb>m/^1rfXkn
%SL\>j&4\H\:g?H!JfJYD9c2f'1=SDOr%GbBN-7#8R*P,?^Qf6E*G5u7!88Tq$RZLnFZbe*g=rI:9bZdnUT4GQZ95as;NXl9_lPSt
%:j!qP:n:]s:f4.TOdQ:#YWJ%\#%#GMknfH;9^#Ai'dp[?(FK[Rq`^PD%Mb<spmL"9-\GV_;sftJIgJjKkDoM$-HFA9\EVos,H(#[
%jB]*OC<%k`SNM#,-(I,Oq3LW<oYcAq^BX8.?(\aV,8@9plul)tO=@hG'MZKF\9kNK?,\qAY"o\-TJ";3pg"ud(Vt4#bjn,+N.jUQ
%UE$S?#SKJ?l<,@"/ko_P_\*\jYC1mo7?A'U.9=/K4Er^#@t8f,kY:;hITH">1:+rjOFh4]h=cipeh^b8=,5t4]W*rY]'-/uNjSHS
%[B&(cE*9Nu2i64E_AQ<ED9b3ETm.-oX2U>?h'$_2kCQ`K9i6)Ih+D$7hLU,]F\OkF5-?-"77t$`*cFhtL]rb72MC]<.3oJfg9n"p
%Q[IA_+"-!06KdUsLPf"F_Jsq&_i,A5*FaN3L1Qkg3@l=if,'Gcp+@?)[I;@K#s!,$a",K\/BDXN'R#!(*;-%Z_+EVRB-1T9B=XLK
%Y01J/`IdkcQlcDa6_s0qOO_rg7SN:]kCnaS02[%9?KasYc;&fu'hf5d_L$E,$dRH!@M3I"-b,%g)5M&][d.MC#c6?@>.D7tHD!G:
%ELX6<ni=QW@(EXN/CitGAMOp"Q<jfRo&Y(bW"?Qd!B"2CjK+XV;R/ROp-*CQnCN+560BmJeC?uBpurB!-aUnZ.asQB@8[E<Nl/-]
%OD".j4$E7]@Z\AMkD2Qimm+bFLP7(tBjr:W!2(&Qa7!Kqq&@H<'#?M1QK?kHC8]uQ.T(L"H/6Z&1o%'$f2sN#'MV:Nc5b3n]M&Ph
%R^u3gKUob\\k@;`a\aj&VhI9ea^6r[PS9Slb2khB&]A)#'%hJ:_B4p+rEMh]/ZdoaUM7j`N=^[hjgMoZUJk1P5XIq83'tcsWu\cH
%f&XrtmYa$mYlWC2HW=I/biQqiGk1!)fIP-<\NbGLhp,][1^9##nDOl(m`s94E]]9u^c@poHh;)<FiZLt1_pr/e2Vpb3Et'2mNMur
%.afn,eA]\<0"r#R$!]5'2BRg-O-pW_]4qq[a&%2oKjnFV/j%mFDDN"3R5!9%h*UF+Gjp(8&W1,c=T0.&o[h/h]/<gIlBFqMNss8C
%*J5>8]fV/C(@iMT`9,,\`GDOc1RA!@.jd6h;@!un*J\NF>oYTJc7=V2k/X[%*C@cYoY0_4ce<[h3/L+Sa=*&1093BW1fKG1?!"((
%:84m#cu*%W0FFJ[S?'RiON:'&s0P*ci#cg!S8Qo^:9SYpr8P:7'G=.=koKKM1Z[`E]JFC&:/7-rPE]70@:/H9rVL`08CcQp)502J
%Ngm^?=Q<?>0lOgP;n7,#daShoi[7;3nQcPg-j\qW1J:h$.p]O]ONH!?gPpY2pd"u!401BWE;XP7&aNl/kZ'/-&6k+N:d[.Y\`DF<
%GJuPj/1,YSK]H?ha?4]MmJZrb%j*,KbGRXmTBmb:`_-aE*c_aR5h64urZ-MH-f1/V(,M-6iETm$Y:R/MQ;o$`E"85.7o(JFXE^fq
%<&!^ab'r/NQ)<_@;d:U3&AIuTEcki%gP4u%;KWdu(.M@Of)^ltc.17L2'.#sic-,S^-l\$WM1s9cZ7HB]2W'35.mhrncqbX:"q,A
%$XpVG>M1Z`"MbK4Dq-p9CdocX$#itdnoTSc8AeLp?[denOnj]ePo>e%Er0nX`XN[alW%Dr*f(F)`J#GN7k>\AA8mQs?(Q&%+b%-6
%[)ThlbJVncI"rD]4>LkJ->j"pVj=3a2;1b-$=-E3m32g+c*?Ck^Nfp!KWfc=U[E]Hp!fo?PBZ=9W);(0T]]P0W-](haZ4s^TVZNU
%qgBDV6>cm9Opn>b@@#SFaBmI2Ek>.Mb:Nl?dT0(mL'jCZ'TZl[Oi^Wa088R#PfEdd\=5jMU)ubJi?[%CJrk@0&t8'A(>$2`G=r&e
%+'1YGICNWm/Iu:2;LJsMFNG3bg[D?1L1"($_:%(uX+r3&qW$U<Ff34ml>b)E*RV8]IJtAL:0'$EFlC,PgmOqXd`Sg[mN-umdMh#U
%fd)BJIs>VtZoR#h=;A5^\(TA^E8!(!JP<@,lA0s/B+6u!ab:9^=LiI!n=rYncp1-.(G@9MWJ"\'oX_[oVd'?3Rs?kV\Wc%_Slf=Q
%-+1h=9_tP/flMJlP(d!Bj@d$F`/gZ2($07a9Knb*<5&Fl=r3Z#NUgkH$&S-[m::)F:!-J.%lkig+%'q+2UkL$>qLc"O][\NcZD\F
%0gZ7343NCLE//cHpD(t(*4^jo6/FA-9T"BJh765=@flhX/hu\2J'Z%3=J-Oq6df5CVJZe8gY'&`XN'>^E@GkZ+kDl@,jLYV7pkR<
%H^'iJl8<o<,,_rh*YTb![\F,e\/k%qdjBl6)T=4kVe:9kkkVTZhqR4!q5KqKMPna2k_Kj/0&S56Er6+bQ/>,%IQPYnPTf+RH!pQa
%df;7/73VT8<8%]S?tCl=R\rel7dK$qd<>_f#qWjQU*M30DEic2h)gGhiW--uMh49LLEO60(QtiR%HMVs-)B]i@EoXOU;K=\Ykci;
%#_Oh`*l/uO>)@HBKe<G9C-[E\]qm*DpEZ>/<k><pFh;<SnOO:L;b1dOc1#rrTIXQ0PtV+4/eVe'KgVVSU`F5u/%I+D/)T@6^>G&9
%muX`$bHG?Y*a.?gJM*1VHOS&8"V[OY\B*ja[Uh`-31ogcjFS2*K!KSbX=i'<MH4IIaP;BK14V-E?Xc[Z/t480%+EE4XsQ%o6bK,*
%Dn)5+^.iaJMHe6SA\(g.a!C1l\hp'32YM/[S)iSPUp0;'6I3LPcVSikGUDB*G]tni?9KburA?B#SjO?3(F_#uHD#Z2R)UK8C/GsS
%`l*kOTg/'aW\ZKnCj\UDHb+60FP`=>m*..$B!sTgp8>/FfYT_Xdh=OI-a]#Y\9%.3]Z,BLJ%]U25Mp&_@=A.H=8VUTL6d\aB+U.f
%h3((4+g=W-ru@R7<J`<u@5Q%>X!iX58I+BjhEigpS^K8].,9iOh8l*33j*=s0JG``Npd6_c_*D$A?'6[$tPILZ(*SC=s1Ik7*=-*
%:N?hSI;":Y"huUBm^tg\.P\fFRXu!jh04u/0cq`F19t=dohc3&9:0mQOd5$@7?><2F?M&olmsQPq5HM9jU+glKi/:V-WPCi=RI]H
%]4T9&I^j/Uo*kGNGA&>KJPc8,Dg0+NI\S$(B#=SF8d<c/3%AjU@>dlnJXb87Xm^,Z!87?J%tN1Bn(X/=+g[hS\&+7qP15au$p)IT
%6o[rV,66FD1@Zu/p>Fo^+k4(ifU!Sc0#G[?0nQ(PP[ec@erJcW`+uSg[-05dR*[Ur>g<b3\[%B?hq^;.Z\Hj[/WBt:\nMGV&P3:^
%i_Lmr-enpJLZOAk*=9b7YnUe,H*]\Aq2PRQ!ZOe2ckG."dmMttlacBlU9h#FXq3Zo:m:M\FRWDrR4]pj0u(FGihcG/1;uOV`#^9L
%+f$Sg]aD0s>8^XE-njP3l6.Hp:_[KmojiOr,MjmHa*`@hP!N'^Y%uc8]4E1f;#1_*YXsE-ZeU<B4/^H$*C[BU(8_ile4X;&G0S^4
%@+lfXAJNjOY`nFG[C$P%F4YB0Ap:(Zf+?s^L&$l_M\VNcH2C```fm$+2t.UsH0f-OC3OMf%G<=nLO=r%K_"mmEL^fD>r6K/darq-
%k2!l_V"RGaN*\FoKLX`TeR3&BWpK1@)G6<?91$)MV:d!SnSmGJdl\6q=5`,1Fl@UGDdj&F'n8_mJICOP$7__Ua3[L>hdQ,ZIZd^m
%GD+1L4NX-fPFNA9P.Rs5MA;Jm[TC+F;V?7QkCE7gOQ)P%Q(3YUhkDt9ks\2]`VsJt^TSu5-VSke(^@=A4cA6@&K0JBb2l8UUNVai
%bn2@j_1>V04''hk-?#7G/)R]IM-O&=%8[\]YT]+OQ'*,rXG[Uq68=O5d&r%r72SbL1uI,OV.+s4Dtd*8VF#&4rVu5taf[3uEZOp*
%0s&J+mCn-@;N(b>5gZ<]"kHTZ'GRML..[D]=(CA?)N#`#-1W3:bHs6I?h`s"Z"DZm/D\i65>XE=qb4cA:0q`S.5";hgI,I,*sY>"
%kBR<23f.7aB)4OTCj9/e\"HI^'JQ\;1=KY_+V2?,^K6.f"5J-U;Ft"Y@uF1j@?M4)"-5.#d%F\0eMIV0%O:.&7Q*a+I@7SE.7T\Y
%lmeBmAST.ZUYG!G'RACN_X"r]:dPB[-,NGA6g%a/00,;T^seJBRc"$lNhAuo(5R%\>F#'Q<RX`koqLTYa11I2r'FjNimMU"^F%:!
%`?^gf$?]aG.lqja*4j28"VqKad`#NbUl:Z1@sO5`Q<)u7[(tQBgC3,!J%kW)#@N]ip+(pn(WQ.<mruDl(eqaC,?sUu&%HNcX_)Yd
%Z3BAdBijaDRXV]6jPVJumBZr=g20.J%O8/.Q'TNX;J\:)(Q>(&L2i,[$q@MFZE(G%NtJC*44WoCh\OV,bI.R.!tj8pgC<inR+2S!
%50E)rLW1I;g\c%+<j[UK$(j(BIbK")bZ*HFBieADVqbnn;Ju)eVe+4ip,5MPa)uHl4Un`r%2-*5@s5%^%7XX(0Z%bP!IV65qlHCX
%V@s7X;sA$6O6FqO)^!htm)1t!D^AmMr"c\+H?q[-<7)/''0Om(a:@iPGL*bpZ#F^dGUpgu:?du?_EP\A5A!.Ad[APl,%)*eB5PX\
%D-,'Ls%fHQ:UtLgg;CZ&<)%LAs&^fn>l1A<T33nX[+D\*?OB:F7PQ"`g)/VE^U'uTpN$E6T<+bRmgU]o*>9^gA_I>*'nn<-jBLBo
%-<DICA0ta*)al,p"R'V%k@@$l\?Wo$VLKd2PTE:%&`!A/9+%`M=d7uS;nkV)j>7X7"n7k)L8M,>DsmtDL-5bY@;>(L%ei;uhVNab
%7Q=)$d&)JLl)gBWAn5GKN5jS8.qPlF:B^H*$4R:g/[L*en\k8KE$K,MSm37>4N92g5HJW#1*3-6pqqI=1M3!FYO$kUds/a,*qJ2>
%KC@T`Ae,64P\@l\B_"r-=8EU(kNn$X(3O`(ZbP%(6<WTHV[-K)keWV(#5K3A8g,\R!G+MEAYS;KQq-)2^!g=%^Aq@nDuBB5$mp;)
%KF<`J#&p2*rd1VpdXJJ0VMG>,%//AF,OmW0a%:T@'0\2=/,kn="Cu=1N?sQ1ocTI=L[=d``'rLa:U1=MYi(j&1TTm/;qe7fBGPAt
%riHh5SFTin!4.6O't<?X"Y!AIPu;p9`uE(,g3u^:"2P8Q')MIc;G]E1p2Sqa.Hq5ghKlUe/M[mSB^*OoY-IGEfBgT6+bd.=TK8/+
%-nD]LH7GuHl"n6pWs8u5r#NGo,gH[I/_[=l#GVt[@5K>`>@a1c-Fh^kL\Bo\2%TTRar1kqiX&AJ@3!bB8iQ6DN^-/;g-o&A@7>lP
%Q'nsUnJI540r_UBr,^3XBRU>o%aPj4UCaQ9_6aoZ?c#&jjc3fd;kQ`sVX'5OV5g[acT@-47]=4#qfkF`G)(o++Wg!6kOY%$lZqAn
%.m>K;f8O!.VA=[Q:rrUc3r%!%2H\WQ@htmfrfh"8]>_&Z1.ppF)@5.Q(MEt/B`d4[9=jm5WG3iN/k,4L&B\IRF6;OoPaE1GdITEO
%<5[2#LTZkY<\QM\>lb</+R[9!=&s;!:Gn0ShA-%=35X=X4_<t3N3Ta(n)?aeC$isCP'>O[=3J\Y8PN8C)!HZR#_2okgtP%/4.QK[
%<$1C[4%R+/)3APK\IRsRWV!*+T0h&C8n/Cl9LR10^9BI'IVE'%BDhbFp^gkrWZ,&C^r,]?YeQ'mjN[F[L%luK;eIP8>;X-XAD9m7
%J@0RR*45ZCG(%aRghMpu[.#I^K6@_Y69PVq:/X8%0qX8/`fWlmN+;Jl^Wc[K&eXRLU#cP+7\/ti*lp,r$bc6go+?;I$j+"aFN20W
%j*?:$m4j?3kRjlE[hqDj!e`!l7l?ms#$C(=/ZP^#7LPXGSEtnAe04a0NVCtffTdH?NZVJ^*QBRoC!>3i*eD-)*+>c-mHPocO:!!D
%K6>Q:IF>XgIQqeM]l)rdA/$u*8:K'>M.OT*=iU<C7+Mck'6e4CaC<6=*'*Ah<pB@mmuT@ae;9cA,h\h<,s?%f]>0VS`L0Ib(.Fut
%^^S?6q;tK(]18c@Q":ru=C@EX0c?Cg4q_N==:FQ(LA5O<#pAah>!sRCe=#ljP7gG&m)6-7Mk>1_bL"le-UrcZ7Rb`c'i0H88bO&8
%\4u'1(epkWc."T$=Ig-0rtZh!.g[Hc?2gs2'AA`jN.,J7M([H`Z?;%M/#+45TkQ)sACPIBMfl7)CAr1p*U=jn^I+0-8Zr@gCT?@;
%SF7^3n]FEiG:<9rb]M+k+mfM`aK6Ons5*:N'GSDWS+c^!+\@Zj*Z[$f@;%BT&!8R"NV"/6Ef6cpB-9%7VXaL#=;^l-Y4K"R-!Y.h
%0uBM,NPF*XhtnIm=HA-9Y?X5+!:0]E5H['cBtm(%01fD0hQ1#b/BTdL=d63%I'WJSk*9cE>O8J6d$+E77l1Uq$'IP&HAQtt\9t5?
%C?;j+"9e[trbdunmNhHn49?ANquqXUZi&J`2-RBdQ4ki(=%]^7Y,t1?:#RsF@1k?*l'P!,=:3\@*S@R\[K\0cWs=atbKV1^jV8qU
%YgaZ0)?nG>MM<BMU;@!lDG#8Q;(C1k+oj(D.^iIsR@BQ8D2LfXO[-:XjbP_;7`cF3WAK/1kY:t7AsN#p;pN=&-kJWl3I2(hWo(L8
%KVR+70obg)Z)^qc4#RWG*lf3U?k=oK49=G>RKtK16<l`/2Zg>gYIkP85n6*K<(R#pPKVFD":`[4r",9iP1a>`;TAR-'MB*_rA7*h
%aQP7,6e[_Pr\s^eZI^*;0'rcA*]!@^Lkg/];eR^dX-ZD#21&UL_aYCWe\,\cC1g3s-N@?@YeY:&@e`;+4opsc'n,G!@#k#'ZX+RW
%ACQ5Di?@4p1'A``W'j7)1l/LZ4`@7adD)dB'=8Zh-QZoT)7&Kc:fZXB5j"lf5R@mL[OjXU%q&No&_8Kt6&@c.nB,Ws2A#6+&p,rT
%\=KlOHPQ4h9ir*227)Ai;NLN;Ee7Gj\\=H*\jMIAippER-'UpS/*;AKGSQ67!b`u`b\c-.0M)on.lXF(DZ,_Bl6&`cAGYl@WPJ^o
%P=F^^ET2S:,8h5Rhr0$Wei^8FPCsKeq$PfeRcLI/787Ir!@1fdJ@"E5"Aet]q5;NV#]0"_VV%caEgu%Ek>qXB[/D:]=,#*`-p?(9
%^eW.T6aYSVBd/aoCPJ?7MMWVTL'stYXb0K9.u1XJWX$$_C3uY4GU%aMAmD5fU*bg!di-63I7K6b.:h-I_qdQd^t?T=3@k[9],0Zq
%i=^N6q?78OSGs4J&:_Z_oQp^=25D(>]6qeh#Ziq%nMWgNo;,WWg@cojVLodVkqX+-aY7H4k`ke@=q)paH.n'!V$3G0,%_*E?D']0
%pG[HeE%EDQ:`s3o_4b(,a$P!(8Hj%]C+W&Pa)rqbDFII=Y>4%"AMm6oW&W)\MhZE"keU;0:]sa8Js`Em;+g!^]h'qKe^Tt3ltk;4
%KhA.oNLRgPoXDob"O[21L#,`O7XqO7Ff,<TGiE,C]Ls&%BdD(oDJNQs6oRR6#*M$kI8GWJ'`27#c4OXBU^YuLe.?ZB:J@FM8%<<g
%AuSg>.&AqJd"J>oKTtWO6"mRahRt$rOip2'R+Uc+1/Oub?W$=BXUpLm?H@Z1BG8sjoj"UHpk$b;+@GN&4(*/)j%2]K^kT&V8$6;-
%(iaDV;Ij3fKF*2o;TM>s(qI(<!Ygu:fU'uC8*FebXSKW"Bu;7m:bn,M-e=59BW(ma%\"<.BkntKK<6EN(p89di+_X]IqqW^KXRa)
%.i?MrK0M2fUC+1d`$,<gBbQG8Zh'5P.]3HAK@Gel;F.WnJm-6*_7Ar#Lk`rI,"R`$AdrpPiA6WmNQ1,r,1EC.mpctH=&5F,6-hnH
%!^0RU=*cn8PIE.JpLN_*oXjTOl/bj+UW5tbc,8<D,J91ddd(/fR?oj^+^N^";9Yjr<MO>f1erCmcD<S!<''G^4qNaqZ9YpD*4??R
%X14c<_ui.h=$*2N^0%+Ce/lRXTdCk7(OOmI/n_o]!Th&7T5s&gA+*2j>XGG_/gQN+NI=:CX6o/edEs*r8TE4f]F8N:(3%DLV5g4$
%]9bTYkf`irRPb/g-'Y)*2<8PHLftJ#S")"u/#/O4Z=#G?H)<Ur)pJf(KRB^0YoFS/b-]MhN>/O(+^p]0Z8JYU*GF7A49fX5.K0Kt
%5Jb\jnn&f2Uhqcr@FkL]J@l*RFPgP\TQBAEDqY+EBk2=R.7Es%lh-lhiPp*&STGG!r_'b?J&RWDCZ7,q3OO2EnC]mjc^&8lW5mHV
%+NN,D56[m:NiF[<AF:e$)>/^'IMp:J,PD]518?mMBZGq4[`ee:N;\G!&YY4+Tgg"/C;+JDoHf_nnLaLS8BQ)IZ6ee<Iuco-@oI-V
%(6QHY,.uMO)Ns>`3-^?CH,e&(:Eka0JWIf]P[ZI)R1f^=.0'qg&JG9BX<%L`?jJ7$\h<'8r^2Va(`Fg3lqhj:D*O_n>P-ETL-."l
%JWg0H.AB]3PbOCLbkhupa]>SOnpD9?>!(N"<;T6m"otqq^R&8nGjl1&WLs<uecYIR5=fZ:,Z1?<EVR\PEtc#g2n'BqBPRq/Ob@.'
%0+Ng3>H=h'N)0*Hi*_m+Gg?1%9oSAWTg(qlkBrPo<d`TQBhtLM`Uq&/XA7)2TcCj%OOD73&q^NYcr2\%$Egf3L]#KU@M2>sj<+Rq
%;GY<H[7[&'QG*fMZF<f`OoAQ,,Hu`*ZM/de#Y$s?`cc9Z5lBDIihc/s?GR7;,p.UC4qJa3IS]Y_7-Qnm^\Ek.'5-XC811CHH.*k?
%SJ<7:m.V`.@u)4Shcp>-rFTbGh$<6o@-r8p(,F6/K@0IQaq:N0+e!4ae%.spPUkG&p_m9Qk,?r19rW.#+ROk;RsP@39pck?ViddZ
%n9HPY1oeS`Vo!O5enM/2%mC3]`*kG(52N]7Oh!3Z+5MtUGE7X=5:N1Jc]dAA1.T@o"$6CgZ&U-K`B9r2+jVJ,"GHNNMBc?rH/e5h
%mC:>HLlTH@X+eepOOM6s$C7A#<[HScP??mDPe.T?IHk,P,eQX=j<GE95i!g-aLU)T8TdPF&^2K`4g1D4GK_[+%k\)n;bNXKa;n$U
%*rtI.V-%#:_m4=SEu&.r"/>-RKScQMV@[-Q;S,2VO/mp9qcn\l'+"Z+=W*p"Ja,#R6.?Y(^GX(5DZA3-r9[LR^>*X3Qeu&s.rsQU
%Lda.cf/.]1k[`(pY0"7aTVGb8NL?UmRkiHp`B>trTH9,DmRD\pU%PQVKk4t1P9.?Ggro],VRpjUE#"\a9.]YlQ[[r?\@`QM68C7#
%28XoLZFi=D9Xh)4Ek-tqf&#l`h("+L0!sg4,2nfm,#](GDOiY&E8XTp8dSCYha1CjGhJd[6=b@$9%&E-64Z`*YkbaNV>&H!F2G&i
%N)*7J4P:;"Jr>HY:re+$A>X9Q2)VkDMJS^#;=i$JJ<4Qh:?7/\^%pthbK!dXQ\:%ca3GR6>$38I@.PILh=kf.XZGLI(T&7U6\J^6
%@A+=q7sjE2q=aOr9/QtV:M#5SU.u)aQZ;$BpJ?]tG`S5RnR,:92`+FJhuM'Y:6#mhE'eNhJ$%;CS<\>(1+8WJrT5WKT43.nDV\>`
%$r"'ID7*Y=Pj.8'_Ut3ICUY(28slls@N%n#P<pgYZq9D5T:t%fgH:ekfrA]Zq:+qWXJ;XaS+;0@(eSTeqi"W;,o4B(`_.niF8[[i
%;rE7t>8'laE36P6"]G$@o?;)8[\^LP\Kg7M:.d^Z<N,[h@nmb-!-%O!,EDtO'e`;Mp(DJ6G=b@s/:l[1g"[ug]uA!/^hN`j7P6H'
%AS:fMY$_<#I&3ZlGA-!S5G&DfZB6@!K_EK#3B9-@^j-m97gM)AZ#-M9kJ#`r\eV"KeYf56XqW,WM1n=j#-3H:O589GO6F*;f2A3,
%`k-ATE]k3PE"e;!CGuUPi$7po>LY;[/c;TYFO,uJURcUZ?X5+1pubmPk*EPAT`,Y[@7lH5SNAm?$$2PQ81JVuFqO&eG\V`;6^*8m
%q[p@T=;&Re2OcaC"*jHa5/)o2VFWKrB,p0ce#X$qS[_\U_[Y/UUuIMBh9+(LW\pO-Z'Q2j77efc\lMR<ju5el?5MrO"bcM.!W.R@
%AN\P0\37uV3$@+&BF8mnQ/Q:2eerK'N8riQDS)[&4[NN^0;3!i6GCV[TrZeKYYd>sSU+>T.;ea%KVGUh"?"[mOc]uO0\/;Ec0Dff
%2.lc,";4MYhJYN[9%]Z&Q&j:gs2oW2fMKG<2^U]Z&!*4;r-=1OI'o<dD`Q"][q'iE^AHP-5CE&0@=cG]Wefd[o"C2I*Q`:u?UH>7
%EBY.Je%0+9,o6*Nd7Dmd]?<^+T0`Xfq2ubYr3:HH)sLURj]4sPV:TIu.bN.ArGuj__*"E9\k\b;6S2rEB<!bAR9[h0mf%3P+L<`u
%-p.FAWP1$"UuDA!4.*JQqA`q6)Wi$TYZ`(U?Kf-a/,iW+?HQ4P4mLC':O*kiW_>(t!7l!GTSNU4-Le;F!to&8L?>1`Dj[Th?4"I;
%Y[?NP`UgZSBs)M2gPuk[@!(I7U;1M>dF2O,AL>=,(lRk(A#\rUM5g]j*@1-.0O,VD8-^l'K7Y;B&/Bc's!9@bWjF><820,%5ZJbt
%raAEKLku['G1SAY'<J[P[ml)>cF:GcjAt]u0NKdM3\K$@F/SYkI:&(gDC+B08JR@?01+20V?(B^1Q-)"'Z%FlR<X5oQPundO;1g$
%!n@CE^E,;&Pc/gcCnES7XPsY)W]sR++mRsOKN.HYZ5A9CW2`+u3;lgohZ3gQR@a'fl23SJOi.<&ruh!tVL=:O/0>u6d*"`GZNQ7A
%m5M,LKMB9tATY[>cu,]6)'Rc[s8=[^W6%JI:VF:Zoic+Hi,%/?BV9`iq.\-7:Qn=[I)fB!_Q4#n&"^-+9O%/-:J6:]Oe$(RDi\cR
%X`iJH?<("RU2/FYIWbjrn%Tr4'h*C_\l*f)c#j3aG/6]ic4D!iTHs4E9@kZ?McCEPD\&EH8;D%LkFWbfl7,cBp2130hpM6.8=$i7
%/la[G"P87B)FoKBlX46JViQ]SPbH0G=\B_uN64[+@+dZEp,)-oflnmB[?Cm!MkDg8_&/gcXa6S+X2_-'Z34PV>!X:5>R?BABV*BY
%9EI-&bs9q;;ICJ\6me0gQ;2';G`.5S#B0CNmj%bid/IB:n<%[3h%7WpEOQbl];00ar/G`U0F#,u2)Bpe7&cO"9-3lpI<#YHFHgCk
%^.Q>MVWg[Z(BE2hRhul;/;/-o#Z%__@Sga4,c]r]GO.*%56MQNeqR-)G'hK!I6PfbE=8FR88--J0<cN9LL=Qr#m]>UALc/nW4%*U
%7TcR5kD8MAL=(l7ijm%I>=fda[626JHQ2>rCk6S_!u"/sd)4MSB1q:+7p+mbTj=<"(oJd)$hW@On$R6p$hJTU;E[Ie/G!0DCK%-o
%TtnNi_PdqT^0%b1_X>F1P)SAseP%""7Z/XU%QfXtB]nllCd-D9Y5#+aI!Dc>!aF:iTNjs\9Rf'2,A6OVC=S?>n";0PZWR)$+^&.A
%G?EH=;GBa%rehPD)-OKRP3F$gVT/r0ocuRh]VV"L-4V[[757?!q9^%5FmEWH2@4+WPT0[q?O0un'-ml.G5DI@7)L`;8KW96a8^<T
%Q@L#6i0<f-.H9Yt071nfHH(2Yc!pg=Z3!?V!fDZKJnfmH\$Tu&o/P%enkdD^3`[g8;clA.L@P,\CQG&39HPbf[sX)G\p@q,2AJ6"
%D#E&h+IbDnIs[(O0;!b[3ud2e7...MJH?h;'e%[Mks@9M`QR(oRBI.t5mVNr"+1+!bs.-O@P8pgC]Q\9<"T_ITrIN&,9X6_1'ca-
%2ItRqpct^KA#.uq+:*ULgi?Wg2#E!]-\;KBLEkmcHmktZ'8Emsno#<d"@YE?dY#/,9`t&6.'8(AB%tMtF_fWu3YcOEn-BRSi^$mD
%3:8IaT!D/dk>hoL:g5-TF'gVFE=ZSg>Jg).;JsjRU;dJkFP>8F?<HudZjD2_$:-DAT8`2V6L:<a?a/;<!$90D#3CS%3dW'hZs8F/
%X"o,8$tA:mI;OO^5F?,l98%),3H]J/V?QoM?CD.CNSF\-KMfu?5QP#bVE/joVaZY<NZa-5bZ@slZkYNC6ludDM8Dds7,@/*&n\*@
%QnpknMKjtX5`a8;XA*NII_VE37l]S]obW2sRXt6<M':Vh[-n]%$HgH>dZJ-GY-fe)D(NYqngArUOa!$[e3).<68/t3LEdRCD?@?b
%KVp/5^ICL15cB-9Zl-),?.)NV`^V6"U]<e7"]QBsOa%7sDH&h)c9[]BfX2c$6<%hC\20Pe1#C>%EC*)&a388i"lO\s;oFp+.F4BJ
%-kG\`<E`Dd'5@=<m>*5hh+#t&C=h;@39OC3p'<UEH>%f&oUj%H%S,7!p1%4AJVU0aORVH$+Nss9.hK&Eil$!*=!(=aOjOsgN9WY*
%S[8,65M,pe]lEf,Y5!e06")unUl0qhR;JbqR#\:=&.GGEDdg6W=h0Sl`H?p(.[:X<Q.+"LM>Yp[dd?kA7>X2SXa"><`[*Z^emB72
%I0%56q522WZMX-r!u<C$pCe9t3TF7%'ml"#l,3-:cbc*K4i=_(Wi,ra!Si`aRj[b]hIN4eRaGVu>aI3D0VTXp,O?B/m!FZ^meMkK
%$H'm@_FR!"s!\_`1r(]jLp;0Fei.9D8=,?#0ai'iDFk&[86?-fkgW(tfeqhDM"X<DD-Ja_1%l:N'Hl-h'&/s/i&o?$Yt_6U-\/@_
%O6>,'fcA'Qfku>(GGLKc;O!uW5f$Mb.Za+uU<C,CS,<hg6/dX&j:MRf%`'g-U3?4E_$JPH3c^,,BTMZdaZSSL*>PEbYE7/e5-4<;
%7f'FeEHQI@ND#s?gZ!t=#\Yg]&VoA;ScN9If_-$1-&)SQbKYloMY%kZYgFqE*&NT-?Z.[96/BIGF@_$i-Nd6hn1gE5g>4Zo2`i8g
%PikKTSXK#n:@u'aW$C[?J^Sl7jgJdO!);?=G<+p;+(N8mHXp[8=X1/V(O,d#h1iX*[lQ;<A'25R6H)p`\FBj2WMrK"9\2A!?nZO,
%L9VX3aLW&40lbZKO#sSG+O\%$P#PuV`qHFC=sLCf;+l@ra,WVM_ds.9f->qm,%Rsh.m%I#]%p"[C]+lO4gJsHe!,E0ok;m[[\ZQ?
%ikk%gh6A>Y7DH^t3Z3T"0-ZCPnQDRU*O#lu/,$85[T(_8]bgh8ldCpddh[@;5bNNYmufbQ+^T!YH<%q>OYTU8bNcRd;A,B'J9if_
%!>p#\na3q$Xs`FC3%0*ik8rq^\h7qhR_s$W>+P1();KIbeS4%\/DFW^knlljAb>m-L]\pf"ZKo2PT-]VW/ANR1f?o(%m.4H=(uk<
%?5!H4j7#ZM#CY<BgcK^:m0`XXi&X-ENMq5%1s+.dA"Aff7f@Q:8Y]_>X5@?$"k*19\D%+iq`p'b]>oMH<fUm,#EF#ciKf/eke/AI
%@:'LRC&[j*`&k?,fiLY[jmWBdlt\B%Zht.=]5e%LZ'r"N5DS'fV<UCFT8Y%;S?Wj9bAt=/pQU`lFZ/^p7;?&1l+_Oq0L-`Z^)nj.
%$(p=3\<-Jk.n'&h\jX['S[tt`XlM5B,Nbq2HYf%qpN`97X'J9W7>IOf3)kFrHWC,RG-+f=glBtb5+u#>qh3'&'nC'!33u7WQ6]h_
%h02hBX@jg6g8_0"ZG3*p!9g3&'rJCC<cl&B"N#Kr?o!Yj_OHP%2#`k\.(80?#2RD(gi`Ut;4$2*No`'Y3$Sr1)"5hqBNdp!^B2=N
%)'>2LQG94<UZZSa7i[Q/]ei'2k(o)</HcR]&f(K&\X/MA\47P-ZcZ??0K#\SpP_atqiL\`F>m,]G5Mr<&s6PJ@\eBM.[42/+4M!0
%^<e;:T=<,"9^EtMCHh3UeCPQh)RmPR>,ndu(9HP$@W%Q&/UuY;1$dgO6d.Z7JcLM&.h9u"/b'7-J0(,&p&.+B$^GRT/O#NDN\Skl
%'#;fMCNp"dbM+I_^-]CrGQk7]nr"Lp0MX^TDNf^o^0(gm90m^aD7,d9GK3kgG,R9*;M4nONtEcbSNnDr:S$8Ea,;8^DE,MnM;.9i
%OFiFml-he+oVq#bK`O"qec":t-,_Nu^2lXuT/0A+_1_5S>XXTWct^?8m5[Li,l`h?&3ki3r'6td\UnK$/DTKgg!oY,V^n`g_#)K[
%_mpsQUJY[/b/Md5s#hfC\9ORGKF95Rh_S)V+)m*NJTR]&kfRb.pu55&8la]RLa$`K%P@*XAdI)`/L.9=)9!lD$_[lbJ<0Ls6Lcd>
%*t2He8J/esL2@,OP(M3Bd4b\+p=nh&+_H&U;7;N"Bt#8$8mPB*m#a,:2"niq6hXLth(WXi$D5>%+eH'W?s@V/+Wd`5gu\TA:5Bl=
%:-g,@&r8:JMq*2U-H$n$!j@&9@n*I]cDnr[):L?<Th*q]*#Dr>2!PR))ki_r"I4Zp7WUO`fP79jM?=0/H*JIn#H$L36MT*jY;I%)
%dC1M(l#24QT>#*8+!UUhNg0u3/\_l/IJXmGR#-$2p=AEZ(Qn5X(C#<*^^jIfZhTQqb&BJ_d1?Z>[3K;sN*CV*\oL9I2q2QN7nL,2
%CTgXhX5!4f6J=PO?@:kI9aPXc#Z,*6bIedqg;=1[QDX7lF<5RiEi&\>dplr%`ZuWSAIjBK)Uat:cX>>i/8c>)=/&$C+H^:;<cMWu
%:=nngG#PL3$)h=/`/JFKn/W]]rbNKl&;@#K&1!5/a6>)>h,S/,l]):t;HNdC7QS^-\*[E,&fGgQ9Dk;AI"6@8%Dg3dm@P>F*.c^]
%Q17P$nK)k/hp<0hNX.".NMad@J7`./DLQ.pg;YQMo8TmS)Mb!"7nht3#X.s4<%6Cj]0[Ik".,-A;F465dp!>VR7*`e.1*po%]/p#
%HFmdJPq@R:>9o@MWQHbo+8IlOV/=ns;d&=Ckga/8N#hlcX3PYj]dosnX8J@]o)_VGj79R>r-XS'91'b?-N1a.Oh.n;BObpJffWT/
%K`98=*H;"`B1?fd[-W=k*RVbeV\\L_iqDqR7DAojXn2UAZPlEqm-M$u*HTg)YZ1*k^I%BL_Goh/Qbo-$Ybl,0$.rZgYLfVF\,\ai
%jG"6/36e_%k#M-%I2#WG)(s/Br63EM,3=JM!1&r!U(k['3bUTP\HH's;q4`/<jN_dY]*b%IuI*e!_rf4#r:@S<:lU[YAB;TWdug)
%6i:F^ru$Aicc6pB7N?d*9.%1eB^\9UTbh5,koUSao,-;TW(3ME.(I@>lnU\kWl<UqH:/K;E%/4Z1]&T\>di02-IPRY#GEiPjT30^
%*]9.R4jr>g(a6dj2NHRTPY\ZWB`cL=aepGWh.BHX"M!D+<!V9eT4+:+3bBFVMb7DJG)O\N#Vr1F(b1FKSs,94j_Uo&Xf/FY>)t84
%J[l^__or.]7)^Ea?0fYj2QLAP4Mf#ur49im?SWFWH_l\jF$(>t<!4DHYOfg$D5:#D>T29=JrrfT^Md7iWkfE+A!-I@r6a9k5Au\d
%VibB8>[pIkE&eZm2nU7>beq:r_MIM\BYD*4(AA)%70B)>f1TXFDFBCi/Pl:K8$)gmq0fEoU%LfKYSnn%g17?+AM8]he0W(X`d@OK
%(-"_Q2gGA[)=7O;GE@kf>^"`tTg!9FRfmF@/H?;(`XOt1]Om6<)9%D#-8.NtOrG]pim?47V[,2&+"HN/'9gKC^=_VRDd9/#]Tc]V
%OlB1uM!p=%S"OG;4OjiQ-9dm.mi[(d_AR"GQC#Ni.hs,,$]n!IbcL94IHpA\1K8h]WSMXt+9OYf%J6!7@(eMNWl%l_SYhN/.N3*s
%D04dsfi.po+4@5V>'E^,%Mrm3m6.3eJZm7]22tRloa!q:NOiAZZbhu&"]dk*BtaplgMTo9*2N+r=7luGe54L^MfG8#i*i[Op[m7T
%->S#?Ie<62]4m-e77R!4b-fp-V.8='ENS=&j3kc\eY<U+)T;qKk]r3BcO.0tA5H[2?RG8T/`DP'2&cRWf2U/8M-0u;/-6LH/<miB
%[%a=43>3jWgpA$-ZkM\c;Ec6gV::Tk3$2JP7He_`IEI`m^7nTe%_NaFC3787V,l>^,BWu:%3UdO]"m9g+3"haFh]`GH6i,"jF_=<
%2O.\_=K=b/jMMB6q!Lh64HC2=aYWm3r8KM;U_dJ+@\7'?iKi,RaIZrTidmi2MC@V4XlFqc"GVt-^*DgY;Z;R3LnGXj6-P!C]E14`
%(n:#2]&j!PQsn!>6IIj3Mp,JTf@/TcEk\c!s1h`:b[S8\]uK1+rtsmbP[,`O)YAFrp2ifbGOa&A1C-Rg0_8e)?U^UE\$?_&WIrH-
%W0'U+=jK]DcF(*ZIA2p[;287Y].t2Ahd-oPM(19mq.FZre0aYb=dt^[DQ\Upap28i_@gWV[L5"80ufI3cEk5SHl95DC>Ino(u=LA
%,V>YWb8RQCNg&%YMc?jW,AF9UJCl'Vm]V@cY"$+l/6Ob3`P-%@$R7K.*[=!N<guSqd>%ok@`\2)B(3`n^/DpJ1SY"lm#"feFe9T`
%Vh[ej7/+d_B67_'bSb@D0HuLh-r+C!5]Vb4;VdSOO%EAW>dn&Y'j<i%R10KH-)BLua]NULm?7(r\ff4'5FC+kj[<oC+[\KuH_Y;e
%2p-reG/0+=(jAmE6%)#[J=c+&<IqP6Qalsk;_q=3I#L]0-Y7[)7fsK\5^4^WN#=Y:;$;hY-/\A3@/-dee<+ic7rJW4MD?u@i[(=M
%2#fY!Z,FD`8eFU)3-h[gp#oDimLIOj'[(piP8BSdre%cqc4Z\"M?-'QG6dY0k2MN!XP5?.6TS;Bl?-?!&=&4#)/ih9Ft$u/Qca4.
%j&6h`&-4igc]aOpe29Q=<sAsc?8R1XNPTjh>diNU$\iJc&-Bbj[`8RbLN#lTd8&l7fOmeeF_p-V0VMj]NF@5Xl#8e#aTQPf_XfJ,
%`i#Q8jg4MYd)JA`@AptK.K%.$_\F.6T+58$>%?\-.RjfWX*L;$GZp,8-C`3;(4f-t!NfMDUheJ"_l/*N?FsDSF?i)*ZuKd$%sH^J
%I\XO<6r++fnVjX8/[=Ms\O#l5alQ)7k<c(Abr4rGB+M>/C77:Af@Pc]>58<qKf"T",SldUpOe$t?9sbF/4YefKWC-ce1?ZCU6+or
%#Rne>C6Ns7L5Jq)j^ndOd'&[p&XZ8Qdd>3!_+7t.@SnA*5aAs6&!%pun"s(-2JSi807c.P-9OCPas?"jZgVJ2'!%7I0q+$QA8kSR
%X\14(il((,Lgc=$ra>09I_s3]IB1"Lneq_"LsqUPl^_ima,eU,I-0*?)4"[^G&:W#W6HGjoY&/[;9ddYB4tH"W?Sg8N57'MDTm?]
%qp&h,eMhb1XY%'imjY<CEmb$jdG,nGP@a?OK(2lk9(EDf&W3_m2=sRgJhV*(4%#oO&%dm3X2/UNeu$Y<!SCW.ajeE8ZP;hh7)\]L
%*!3P!g;h%8V_aNZn5@9XBe=t9m<djt8`hA1aaNW64fdIK1f_D3j`rSO6osuc\XX\-e]5&I4^nF(5Q?n>[m`SF/+>ii7KQ+52e8gG
%`fRmE8;Z!X2D=R&4>Mu@Y#B&79Ci%=RlusWE<NX`llV@,C.AAe^E\S763W/mXr5Kh3nQd:6O>#*1+oCKGZ#thLq')_K!PQo?UYH_
%Mc^(3/RpdYp089]7c(#d,*B)g4bY"e.1*^RZ2Z@.6oBuf7U@rI/^Y=3V@h0-fOURI*PfU^]b[ns,7?HR&(#k1c@8$KofAu7jPMHH
%VEf`-(M'9pKPf&#SU]O>4POul26s<.J+YU-K\NYZ8U+F!^r3,uEE'hsi1JUBO!ZnM(rp<]>pb!mGt8jRCMP1N'JNE^R%QrGKH0dP
%h(*lR_dCD2k4_atinG[tTlDYoUAM]nh21IfPe=KnhDXqZbDf7N[#XLUWTY[);,cNAZ-h7.Bd&f@iBe+@m6gmV(ma$bp4Q>[h!*%g
%Xef?]gunn=CO1lr5Qc`#l?$Ypa:Li.<TG-+'H$5V=_d,O@-lSrbW>0:"D;rQOEaa])ls5.]X,5$&6Y8K;@mR8o%mKOj"QmhpBFPR
%8mO:t+?Dga4tuAkTP>;kCHK'DE'_Ep[2UJWF3R=k$h2TI#VC;\c]ZorT]YprfBi^R^.*.2BLAt`lGN1f]YqF\GFsMD8A&\t;QrEs
%`.;3JeDS%7]ZPVg&lGU?,b;Au&Qf>9ETb::@b":s`HQdL'lfC;U+Kq\b.!NJ@`4u-3Zi83oH^[UQhd)JZ+sIuL\$KN?u*"UkVY#g
%$Z[+>P:?A[iAlt@RMFh4m=g?KHAt`c13C<boFnsa88"nrNB1%mQ!;d_XSIqE#_u,<1(KR2,-9a?X1$ufF(1RI:Ds##2r?0\:mB/d
%fSi%$1[`mUCp_Bsc!Z[[5VdF-.[RrU,Gu,O+QA?qlde,AX>K):(+gDjOLi,Fq^U#R<to:N?Rs*!mX+[Q"P*,a:WO?CoX;]E[H3X%
%dA)d!@on;P4o-Wn)2e_NXaMpQR(g,BXpZ*@lTKWI^!SN:LVG.+atH4&,cd)'[=D.NUbPiQSX!k)5J`;)/\(bV@,:#WC>:nGRpr^f
%TTC[o8WE9!`,3d/==-\a,>!Pan8O>m_@fj']t5!HcTocQCe1RU"CUY<%]Pgs"9+._1Q2S,<?`NC<3d?t#,Z_J2L*FO/mU"Hch)W?
%H=EC7d*6(VT(qL-Dc2sD3Bh??KL-H%kD=cqW5i<a.1*a+UYA'^8tZ?rQF*+)5nh\2!O=dW99Vh1Cd+^+:h5q*Qa,$b[!Yniq_2fR
%>0i\AQC;*81CDDe;pN&WOtJbQaD1B[(iNcq++`n8cG#`m&BEcENYa>!BHK`krA@W?NX!>"$j#FG+rdl#S$Ge&i,I3b@ad\?nhfhh
%8C8Cp16M^/L3gZGXh3#fO9OEBCR-3eWn1.PZt-L8PbGN4GLd:KRQ3,\`?T#hqK?eCr5ET@Y09AV[`#h9BITHncZ__ZX@=1?o0t00
%YhN=h8Gu[W*hm[<DMNNdpL$G!G*k6V6BSBERK]Aq.Z1,rgQ.e>O%<9fkuY%Z7VW-!9t,H@,Z_'h6q;T2_Vo85aC!NYVI/pC+g9Ur
%_9(OmjmD#'Wt1WfgqZ9%NB;'82O_JZN0Hr_d6V[ELcu+@="W,/g#Vmk;U"@$]=&Bg.jIDV%f8B4$):C[La;*grZI*2#@=bF_$&d6
%"puJN-)6mR*c<8NFih+B,g6/"kO&.=]H@=*+Sis4X>DUD_FKR)"0?1VH?9%@9ac2D$RD-5:7hpGFCXhD<;FZFEA6>WH0g?gD;J*s
%lrtjH7Nh[Rg+peErC%9oC"*-u;[e?JEqf(oI&WF:^6lL,SI/n7j9ug51m7c2\X&-?>FM18Pou(%\97QP+t;@bflm+QPUjCR6;G=h
%4!c9/eAgaD;kbdq_MLKj1S)o[;G'34MSi`sq5XHdru>-+F^tK3fpEnc.b1irYun+!H(c)jK#Pr&S/u(T;r_?sI`tEuS8%>hk]ED[
%aui'?%;S&k"N*gGpj":5_BTV1,t<i_Bg\Nt!*9pD2!%-2G.O\QTQ*a85"0TRC2m<bX`05+aU+06q;+NfYt2E"-X>b!U%m$VRod(L
%^\1T^5:9\,8ad;g.:V\I3tpZ\?.Lha]?(k/_i5Z^cti:M?ABg$7PiMY:G<4=,)N\p(AK^Vr8q;X$t%'(C7S?U4n;<u>pH$u$3sQA
%74g)h038M3KgRPiT.5<V,p.UX"mD]D2NSiK4j`sRZ^hNsW/bso@)t4uV1p[D-@cqN]6A'AKgaAIFYbb*eCE\aEF/Q)Z8!g8@jrE_
%"83:A.Z!e1#"g.Vj46R\rMg<?9smEG36p2Y4>$"g$Id:]7Kt[9!#@56ICug&@Lt!q'uKs+b8r;N7qT7Fkcn#YpC)'i:9G?\OOOEX
%b6;sFf)3H2,5d7Th?\#>*kL1[3HQJ=Qsr\gW:*ION@iCW!WLpQ1#/#3j04DFT/gT452a[T2YYW+f(8ssUf8,tk]5GsE3F:W-uPsi
%*k@`*(jrh+C0Mh+W_`Z*TgC1?8:E,<OV$kDBD^oU]V;A;6gkI3=b@c=7DX_KXdm:=VI'ctkV?KGCsD5OJ#4FDY#&[XDTk9'c6fK]
%Z`Fb[6J)^JQf@pQKJFcX+LIZ/,3)WOL23%(@ZQ&6.T\(a`C$F>kP=?u;d.U:9fBF)$a3Rdi<8-<?mM?LNU6M+d?&jbm`'r-o6!P,
%qp'YVh+1>q/KXtfW&>$+?FVBg8LSg0U'@oTOFMf#?[IT:`hl6FEVho:Z85WQ=3f2oE(gO-]3?<GEfDM.r&GD(E[L,tik_A"*R9f#
%OS$>dprC\b5Q7o\k<sXQ)FcTHEcjc7!p4,9os,r5Q*W[=p-AfT<)!iG/q0rkBC^]CjtFk8UHcg`K-\k^@[k!(@P/FR]kU\I(eh)e
%W_)@VcP$)<<pi=g*Jo+4V-W6+^9Q[u>@CXFcltr0P$LX=;mk]u.LK%*IH2%RANa]!k+V\fpBqqbZ5cPW'd'.BIXRlOjdRXjD'#?!
%n]R8lptD4Ll0@Fld_U&75"9FX*IVkM]Q;dU0q5XVBfjoCBi-D8lB^5E9H,hRJA>kUZQXPbBZM"S6]:PP9qV*Cch.mH;E;P=akFt'
%c;^<W#dr#>b<,jPhdA,TL<S(:<figTHF..ZpP2"]./?$7fI2Ws4RVtj"3J3M\%5>f_!8)O*NTdeXC!p_AP<C(/m7R]OV.!)QU=F\
%Ma>#nBF8r0!%BpJ>V,#UpNs`bW]OUVY(jD?1ICZ2X1QY8`6aN>N1TeIQ8*IqXm#[js'HrE%2XNg!+;qDQ51SO`?T1FC.UZ@&i\\;
%m%^KMEFr`W(q>``QTR-sa.pL%h8hKK'en<(9c#RjE!Wg*c,'b:bZs(),+hJ?Mgu=dpNkpR[Lak&WY&rGAWd"piD$`!cjf[(KKGO*
%T=qr&ouu8r)UHHq.UYi8neX*_I`*@7X$\3]<)@8&@p22i;&RC&/<geWPO-2Xa6G[>9u,keapdD!P&LH4P>Wr#d^CnQpNe^VY=[]E
%:3U3$:eBpiJ02/Wfb7^Cd,`d=5R:p0d^1VQ*E25dl^pQ?],-Z2;uKB]P$Z)bX,^/4*S#c)358([oPP%Ai][C7"k8WuHFsX<7YAKh
%fT!oA:,Z`14gkW^aE4ZhC.QcBq.YGT<ZVRtg?KQcU#_qK4</^k9j]2MLQP5.:g9>-h+k.M=32m%.qo<..A/;`EZt_,kG-=C`ilL(
%d_/5,K;7:N^F\^ELk(oBi!:P*PBWmK]phLD0f1?a]Lia-*\j(D+%6>MU-"7K]u<k9Wc:8.h/Df2(cBb47<g+F\[q#Q)9\2,gmZ;5
%#(\"+Xeh]6YG.!tesb#/GZBF`(>s]oYoB[cZfjaVQem(f/(q0BdqQlmh%n"abjW$^E\)2d4a(Rog8H&MXPoA_?faY(qT^lZ!'6Rh
%Vi0n2)4n+0IJn.q;R<p?d@5t2Bug9E0:>*ajh5+4;mgP#X;([O'^+4+fZW7gpmgn;L-ZP\S<]X7HH*?\*Y+>XV3:mT>eqh+L`MX/
%g#\!iX,7l.V,X$alp$fnN"hIZHuO"l8Zpr]e['T;E(puAI#4ElKFpBoND$Q^qFd72^u73>F\fN9gS>X_lu#`?<r((=X:tN#FqWZc
%K3K&c+e@lI5s'`JNu<X"SF9'On3B%nIGst+f78i^Wt<\Rf%leP/GpucAQC]sVL!dI/#\T=2BImN+4RO[r_%&\U!;oIr%_16DgiRq
%O*i<3$VX#3aB\8WknJGU<YNE0r])uoHrbkpVtmQ0E_b<SWWm/sZ#57b]Wga,-g)LkjE/T@odh-eZoV2n7D(iB0uWp,lOQ*A@GFql
%LZBInnu;>m?;2kOgBaO@7MREP&s_KflsLJ3T^i`E'&NjrFI9&.?1K/=8XrE[\l#;hTRRLO?7%CpD&;aR*Um@d3&UM2%RbID?3"^\
%/L,#9!)hVRG',G1+9U`?&'?&I>DE.(SgT(R4:q<mW4ALuRd4(rOs$NV]U"C4/%ue`dI">H2'fF7gA+d$k-d[tL?/<sX\3aI<M5V9
%@0#MR-k8n8-ptOjE_4rS4VLShAq004ros9e/sif#"kPAVELLb59Xc_+oIPWq.VF'V$A7;NQ>PNal_aC,<[hia-^ZA$Xj21S+P^/]
%Y[#F[XKY;<_lP5qs%CFC&AIH2W\6)`o,43BbmdcHCb4EJZD)$bV+)VX8@58bRh9#0$7^kS]!@ki;/dX(]$29BGFR1s.rY>N+9G/2
%;oEH2?&C/Ekk#p$c#&>\a3W*XX%CPsbE3C'eX<XXfc*_j<g&k=UlEUO?E"$Ih6`X_%!bHY"KD'K@r;$1OMAIK3hs;XL1Mi$70NaP
%6A[tmD+A^/?KW6)g[.ObJ>'"Q+i.h\FHXJXmP3B!g^4hhYkM8Jb(u)6;/Z(l&^cG'*D(bM<'$a`>2VD#)k(q*S/Q,T<Gen;oM/sF
%%SQZm'epm%hS=Cd'D3]$=X$>Wmn1'MQ#G`<Gp^/C%Tps6m\:D1_E.5"V!"`H*(R4K.CTFERm_?Rb0?qS"J1[Um8E:>q5cRc7>#*!
%A`[diEQQX\<h\],N<CnE4rT8Fp_L3j&!(A]iMmO*X,-P?_qsM,/U!puA6ne2<9gHaV'e1P;-J.1ns+6t9JVY_ndP:/n%^HF'+o%^
%GVKA`?tka,'W7'r5tp_PT*bXIGX7T5kW1BT"YoP*De>["1/^d&\(?ZU17>0$@ONNniRgW&AKs;l\=;^P@>RiqpS[aAn;`$J[2Fi-
%3T_q[)p'.-jU?(;b1EX8ChAqR+taaKjH.$2eATueY$>D5q;N5*&UuUr<+b<Ya=#I)XXY9L'"*)+d#.2sI=(L7e"ef[!`meAK!eSp
%)Vk2R!`(\C'!CJ[D%_f54Dn3GmX`8b&8L#%D]@D!4KhkQb#>bt*J$lNY./5'lJs[ELXQ$0jLQN*RBR8-l8i3&k)!-+j*tC@A#3Mp
%\8c2g%6OSQgGtMXfRZ%g-P@fW7p%RO`u11=0HU0&h+19TTn1NK:K(.YG8Ehj.[AZXrhes55As+F/mO5'VlA:TlkH,P`Ust&kH%<Y
%$^a?6/M:fcO/TJ!S=i949#g`or4u!!),:O(2cCFDPcd^L(HKBJ%uo$.OsJ=&Gs!Of\j$CtO<$Zc-4XF0ONgu(A04?gCnk,aiU"-"
%`0Zfka,/j`?:+Gr,SjMNaB9XGN,,dP?(RgG/(]j\@7%j:ca?X#C>f]NF?.L1e<(2lQ+=et/95UC"*p9a@\kHeADQMDmEf5-PT>nH
%Gk1HlD5'*0P<\07M`C'%F^q3Sg#T%EjObQ?HcDN(:'MNE%:%.[/&R@pg\aU0"*._a+b*5gq6PKuBR)Pp:eBDJaTNs=NN2;><TDZK
%Jn&LYpe_W"lk$0L07s`j0/l;:-$>35^EYr5o8-d]X/fn`+V*/C,d^#hRNNek6e";5>9V4)T9&0=kk%cn*7F53T\ui[Y%i7b9J:@;
%b($ZoB&pdaY-Kq8]dc!n2=PS6lFU(T4g:r>?uMR2129\4@tQkDhH7L.rDnqD6sU(Zb2OqM7k/#t.d3gaeX,+nkEOUT8H=)O*s8L]
%E"P;3BphAYOi59qlK\@BlInO1:Do)eo#2bWUL$S^C1jd+18-7hlc@.T$'g2]hs?b`*.rf0\Q&W7`q-kO27.r]$`d3(AGDs%=c3',
%aCJ`WMBWOYM9Pag/l(#]TUMi**6G=N]n[$g8\Jo<):mg0c]e\L7[pUI,6CkS=.GD$l1kPAD@ffqBY]*P")<Q^^hgCrbI",V5@eU;
%^4P4=-j8^lF=]LIfCc^pp12Cgm&dFdNmiO-0iT;.Klg*f378k!_^"BQE&'A$M<j5,,dOANXH>sJ"/B"MiNoOFniJ;(^K_E?rpXk*
%_gh^`+9&JDp@&!u^]3?Or57FlfDkCP5<o2-ci<Bns5X.T^]3VLrqjJ/O+6oes70NHro8M)j.H>GVuQLPJ+q"`r#bt<Va(4!J,alq
%qsqY>q=fC<o?[H<!WM6Ms7W@_s85+\iU?d06(W;q]>*mBJ,\p5_nZ"CJ,Ge.rKg?/q1[qfrq9^.?iCFTh>GV!63$%2BK18:kPo#h
%nm>)cNPGE.s7W(VOam,_^]!Pls6bC7DoegTT9&oRlQcF)>'lDcZKa\afg'7gE<"M!SlE0OdotSODmC>7W'_.>US+(s.F]=p]!hfP
%!D>u\)YrUP)XAfH(pit1@_B!i%f];6$QSraW$hnR#MX@=[LRJh9fOf^K*dk>.%YOiL6PG@EkWqXC*qYAAorq[-2DSVLr?PVJ^H>H
%bGjVCcj3ktU"i!Tn#l;*&A=P;&no?2j)?mhrkOe$-gJp4;6!#Uo?i*g1J%dQ<n1:_KTRb>LtEbeq'mo=9>V/IdK[a:1SQqL$UQ!m
%\m&+X2AIjV4tq+'ms.&E[QkJ&G]gu*I,Gkr7:+Hc5S#0s!)u_a3-]YcrMr+d$*(")EG%b;eK9K(,NU*Q>rbJ8@UhB\&L2+70Z)eM
%L56sOS1R<6[]kAi##_#$6>r5t/Ab+I`JG[L8;.bbHMBbZaGMbbg+A^0!M:OICo,:PLj%EM_!h>DX_ll&gU4ZOirOnMki1!?n*l+:
%PGs?[[Am"3'Jo*;=3aQGoIrbMga+fDOd?1nBX\KTiu6[ub>qjcSW9MS8r*\hXssTJ(i"2[=@hK^eU@]A[f[iXl`^*bkk?=Gl76O#
%-/]!fj'9VTm&IGQ&8p/GOGq*G997=R6`gGc_A5@?1#cHAYS1:])TI*\hIC_]`OJflCIbA:`dB'_-^1i+7`t1FeK"2D?p-ViPDUGT
%r6\bB=+!nJGAB5;g-S&fCQq=8fB>4El<qqh4+c5$#ml3[YOEOZDMnK)]7dP,4A5IQ[%CMdJ_FDL1["*[_V?6G.o&(1j.urM96E_!
%KFgeQ#Kn?FDj.jJUmE2Nk8^ntIjrD`S9%V=\3cjgZR3>-T41>7Q%8V.7Z%(hFEN\09dI>f#MF)NMY\G96/]LYL3sGYJs&e.F1"bs
%qLicO89k$oB;dHlePstSBt=l:5^G<>/WTs5`EN'\WP[Gf).AKN`=4'BE@J'[&>W9!k>MDKIae*_GH/TdE1r,QcbZJ/N+NhWCpAtH
%ZJSaE]SC;ESuqW]d?ZmC&QS,9!"M<^,P*.)l(X)*Fcs-u>[Z&#(+`d$iD_XAcd[%uA(IVRbXD@"M1,rHqM5[gXg/rGYkBH>OI1u`
%"A?Z524?gCWi?'fF`.[rGhW'mROZ)4WVkkPOnE!?Wi,l4GPqJukOWnFCh,#lIF,(A,9NDda$9(.cR%W]r#"\HS00;Y*1O0&.-5#i
%-1#.aV;:WAZ>cB0Z&:6qM&&W%GL/O1#\p'4d#4@T^''QR5BY)4$Ee1/c_?_4<ZFYYgBhC0;:S,`jK"`k:?ojCjfGo8OWBu$9Y*cU
%TI6o\!sXkmFuG=%+%slnnHn'2b)OE2G(Y5TkN!D-'3&OD\DI>GJsP9h"M]%mEOBU2-'DQu13@A*NhEVA3(-00ViQr$)cFmq0iQ@;
%VIcar?CCpL&`49R:6Zt9B#/1`7.k+ZN:>:1AQ4hW;Q:o?n=jQql0-6P-!2S`9Cg[*O0M2@f<>TMd^XdJSDBGX[^cl<"78V'(F>sO
%!/h2AI]m&+\R)JB%&!>Z#^8UE/h:uL^&q9b+ed0P;;c"-B'0pqD5*Nt&?\:'f[Z@!3I+kreZuLC;lNAS/*LgB[ie:Oc1WZ=?mAo-
%C;(uTk5ec7QT(1OAL'PbM)7p2KKh?5NI/6i5lD&a8+C9P:K0bcgVaJ96T8BRYN<4gD:N>20ZC_7/tEO^@%.fkpFW%:fuP;aSc7Yt
%l1M2_Lt):9c$-qaf8?2%*#(r9+7U$FBKk<YHS]7+IXPLDbfoJ3)Z=qLfdFY=j=PsV8`Duuf?k:_Ir.oDr]FDX44#5o=HosII9iX=
%ojU"FT&_=[0jU:1#IXBtErkNI;`+Q#=g>K@IGi1D4htCB="k$k8_660Ki<ZFoa$itURaY))4BOs4dQ]k1q)Etro#@HC.,DoF:>X:
%)!G0,FK40P@.HJ^Z\4md"gV@\!am9rq4j'thADs;DH[7AXE0%Bm+Sa0mT9"@R2)C"bSeD\O/IR*TASW[RV;:@gaB*jAA^q-O8Iec
%',`oW<s5i).7T_;a.Gdt#CTk);ckbY+4L2gfIq0l.*Lg`+(l8#=2PV+/Qtt=DhhnV]-<&3!lg;/7qrJ"SuX8R:2uSV"_+Eo*:3H9
%`SO1*-<EFRa,t#6%^VV6:rIulK]s_G,WKI`NG\qAN$<Y%IHar[_pb*)4DkO<MCt?<Fuol%Nn_/6@+b2+%$ma=^""MajWr"jhO!i`
%4INdr@HN=9nN;NN&6H0!:cOnGi8Et99SGI\f]6prq)er9:*SBr<?6%m;U1<#j2us1Nm_=;5=cIQa'HNq%UDr.<DrDlT.gp^3J&$g
%F.+3D?1Nf<&5[)g#`4=J9MWiiQ`3N2E<r@QYQ$@oSi"@!GED9T">Q#WZ()B%:B$Wo[Ot!$nI4sQ1`_&lIn_=kf%^nN;+'E'jBfEE
%M\XBh_>I!f0O7-sojfo"V*;j'>[::Xm;J-]kHX$8Ho%*[`4]r[+T3bBoIBdN*0@eS@#D7-;`d",aORXu]<M#&GE.LAV9#Ns/tAT2
%GsnE^j@G-3B8V94]_D*/B(R?>PrpOA%RnLLV1s(WO$raMFBeIW"6=Sm9X)KOjcF\n&8T[k'Y'TI!VhR@b0n6C(*7p;.4V)E!=^@,
%Z<a!$+V;eL@.[X=a`Y(/DkT*]i;=%.[S4ZGh<q-SZg(<C)087hZ*RM,hKP/M]\IjbM"p?1dZYK#Spi]Ye4\hXo'jdG,Ee.Y)(@nJ
%D$rt7,Z/.*?mX,<H]j'1of=WI%@E*VLYU\*,W,,ZaZau6;YO2i#7X'5_`#AL1_`1VhdV,H6r0A`*idi6dDf"HcnjtBai/lEQeStT
%Y!RYYCO^j0&'(o$efT?"$N&$.&,p-D&u`h'geYqQ]]7/'kQo7NZ2L".83Q"3MH!A$=u25O*a,_mV7A[O3_44,Q!V7:U[gDnQc8u0
%-jUji?)0ph.B:V/LJ,3]+X$%<$sRZ#Tq3]&6;kc!Z`2W7WBp4+,Am*YODJI?dQVeO8K`i.,#Ojb:sTg`"8L*9'W#./l?&`iSnq66
%.iY"!"OqFu'7j:#(RP!:MbE`o^QsPCdYR'RAX4fd[lNWmn6,QNiGmL0))-dX%9[A!hZ9T!?mQQ.b^ZQLD0=g]J0jL]Y"l,3@dhe"
%gPUL+1fR"WhASYE3,@4'*R4(,[2`sl7t"fZml'[1MAVHD_Z8^ilj:6m"KP,jO?=9*:)lCX=,JCol`f@99Al!SmI8=*42@sB1f_;B
%H9qp*QVo)nK-Y)[4Kn4eF6->5rn$lc?mrls&HG1c46g*=g'7cZ8]URnOkGq>s1&<t8JBWuf8`'cDD%d@mttQ0?O3l\3VRq+?I',,
%DiV&)-c*pZ=H27rRO>*IAUQ[n\kQ&gHWEl8rU6&r_9<1%>O9po0<o3K!T6$s"0'tGr?\,t'_8YF%k/#rKW1H<;RQrWq&,XG'2H>'
%^El![b?1^';J\J!hHl8?C-ZNl'NnQ`,1Hb8.Z,\k';j^@5cuIFrUs]NR96:=in2smq.4.AM7n[uAJR\-.DlVl&V-3?e;EB>`BiOk
%GX?E=2'3R8p^W=a,'_fnW(KV5dK!tSP8bidP9hHaffVT@;`99j1?*<<(,84^KEU'=nfi#Q1_bCs<tLaVFoVYpQ6#q7//",'lUGI\
%nVV1R:?>l,qt<[Ej3X!B0R`i=61^.u[etE,$h+BJo+bo9-e+)[UI6:MrfWce(H8td/'6kZeGu7R1HprN$.NEkKM[:p"]k#jCOKLj
%+`"sNT&\Ggr$3$sbstEt1$g8DhQ;\sNY<%&0fJ.P%Rsn$9rYs]hl>C-5?5/>peqm%K`$Lc\t[pF/VD[?5Y*7ircSnl7pti.1JbO$
%4$R-"4::>$URt,kTtp&!l<mu@/pmsLXS,c[+=.scS$gn'J:!*J_-`oX;@*\1ULWkuVXr%1nun[7h;Z9%7Zfki1O>BL43_'=>prXd
%BEmt9VmZPDRBiea5.tlk<MFS<.#UoOLgJ*2s5<*6q\V$14)3QeMA0)-UPs)+dQ!-\TY-B"X+Z#eSG^5[E:S]gp<%`W;RNDoF_[ZA
%<T!*#MMTHAc(#,Xi4!C'''`Pr.5k:pZnHO^.^1<^f*KTlqVXBY@V6tmZ3Z^me_6;)L)ZTo"^h3F<(^/Hfh;gI4]t3pGQ`H-d6\@Q
%/n?P&aj7Q6@IBWo*;Vj8O^alB`]3bP2%Ss%dOnm[XcV;]*DsLda9jj"U;+qV'[%8Mi:0R,ZeDPeE46n+_BFZ0p)t\M<LS8OGEQ!1
%U+m$:8XZ51Th,(-ihg73CNa.2Io+cD9"ZTO%sj#"C>P?J)No$Gf4(A!l<o->TG8:mS0m8BN>IR;.&j%I[,$dG<6p>^PboM<cTp^&
%(W6+EpV6:q^N8s.\j0G=e6mc<>p?Rs"hc,u$'[_d9-*UU<8Y$gP(bGt^05.+ckk,^IV"i8+q.)B*]j`s[eB%c_>YZdAgkRh3/;ns
%od!O&PI%Z2E?0p6mnVF)')M)+@.;ZeLi36[jNkIF'VNlDoEJ5kk6!bB"X&*Tc/`-W4/9+(M#g!k7;2HGmIduVocmdpE.!";@%lUt
%gJ8-JT]r$QBB76bL\oMJ(s`r)m;PdF0Smrlj%d'qZc0'&cftkjDOUY_epBjNR6V0hF6%<AJ.ds9RuG5V3h"ccQI6"F/426jK';`0
%/J&lDcnCgQG8A`+Y!/"@QL!`iSlU7Dd6>B^5Ts_(&o95dd_Vgm'6"Ba>HO!d/Fm:]qhg\<@_Z<;1)IcB`nDt8!H:riqPe#b&Is]H
%9<[3=qht6S7(TGe2d(<$;tdL&E$[GVIXgnmbW"t:9Q*9>fct<"6ZDatr<mbsrW3(t#dMp&A+U0lFjbfWj,8Y;3mY_=\JMeS8T[F'
%]VkHR>s\&9n]M2f_Iu53YbT'2/cdlrCQT?[7A</P)rBogOIYC1j/'p\b\:I9K5d^R2#->:=Ca^R0K"64C!X58FEXu,Ka/?&,4ud.
%5MmG=\/mqq=%+P/6\h$l/m:gPd\U(R<H5InEBt]>OA\uH8QI`%7`Gr#TaoM:]7+Co8CFEt0fR)?\>&D\a)?M3_kLJ_Qe,p>KnE;=
%>_?jM_);3$V&aS*';EZ$\bgY+/X(MfV$(ddnILQ+LIHo-ie/8j3un'@1]+NS<W.lT1e:r8Y=$TC(f0kQZ58a)5>[<24$R4&+qGuN
%k*h7B&(MB"Usl+#2Ok-mb@LAR[qYXtB]rh3is^Z#[JHbGH0;0[JqH[AMFQ#KW2=R0[RU/$4`o%jE07Yl-0i+ZBPL4=7?4;X!oG^(
%AlW0Ok2hGsX!U:E"0D.CrDe,('.R[,>3m`n<]o!3&R>U"ej5$f&3Bq*GKg54$XCl9^5[^K[?3tpiG`6Tq=&#'XFFW0h%*3hi"@mu
%O^h:g?/u;sK"BU=Wd9O>@[E/fL%"9%cL0];cmEa0Ba=-tq^HYN3O3sqk>BVscS3mn3"9fR>>SChH[=2frBOCb'tR$Ph1_/'>Uru!
%P&n,rf]kr:*a<(14t<GY'Hq-4X6q?o]FrS;gA^#^.-'?0*bG-]N/I3`Tc)2"/m/WG@is+l9-iJC3bpa+E5FSYp;lp:2]Y8<;8$g*
%$,1KjQ'/F_`;+jtr-0*`gq(oDpetWNBEU>+V,e&F,oX+7i(jAdNt=?u<?`c7CGP&B^P@?fnnlAbM^G]0*nTVVRk&Z^P=hr-2j9l-
%DI&hc=RLDdFTT3A"B4q$n.OG3S&ko\5+:cUEoJ>)`1>ASPf'k?GZXD]#e/+U@Y<NhDVq3g+QZV@ca'?()9Vm`=1?IZTklHG`Zf,@
%#mt!krMkTILq48?3t4+t8`3[KYY5Sk(nNlr]Z$jWPbXaHWlYA"`")"2m<P!^gGTmC`2&4f8NgMP9rseanYg9)k4CA07rjlB6(`eY
%SEpe&kSg&Y8:P][K3pTZG7+gnEl`O?>+o=BS,<t"kXr?=r^QA%s#_5RqE*@gc0qurNp&[>T$aagnmGl0QOZ(-ScfBTeZg$]"EJ#m
%qOo3;hS;J`+]RbQF^M*L.uq1:V@BR<`!dVdq\W`VhS=q%N$*aM/8p>6`%_QC>u=!BXP1=D]L)tYI3T7:)jMI7BjhmdI3]]VgqYG0
%N:f>O&&M+LC=kCQYCgI1+q6pee9VZ0>Fh1d7c*X%O`d,,#W63H*2[ICV!XdiX!ssqmmeGf._bu1L"*.`GtflP/)4k::mGM4I\4^2
%!m`3H,D/p+b^l?1%lV8f%\=6OSr@.EqsT"LgjW>`qHn3?)is`?&D<%j)?G5>/6@<.1Cnc"KCfZDH_Vc$\rfb;S8Vb(L[;90$e0go
%0)%j03(F_]%7H5qBs0*DH5]@MQm31$&(jcV:r:;$"HB`9rJ0<5E[>B;3C0Nq*16gtpILh89,LVW_;N?HSYZV\Y5Vufs"tEMlG3PR
%J@75T[:3U6C4K#@0YfrZ7[@19pAomjEr)^%bU.:@H!VL^&m@l,-kA#-1J9h>k=@7!*D0cs<r`\iFL$b-Tb]oq%7lROHf-c#):=3C
%oO`o@(hlSl2pllu=%'3T=]XNO=&J8dQlPBJ:<.D9/3Jhd[@E<1ND1?;Ub@r5)O[iV2\Q.\`/!#PYi@<;*d9BNaTWK`W!P(B?+T?%
%j7G)O,Jk9NHF/<5c4n&Y:iY'F<[:2$r#n+:>b;0hE!T*oB;lt8H`\6+827527#dNbNJ-D]m"]m,(*A1;`sRFB1BK^@'=3M#5en_E
%)W"I?QL`TJ_#OO5d&MX:-18kWF:eaLs0&@QrDH#1C3Xc3?mCB;e.-,#;6Jqukq/J09XF^fWNiG.0,aJ![sD.lj5V.HH`mSJ0k[25
%Hl$e(?`&X^<XC)"I82HG=_R8(fGfoJQY!S/U&0l*dF,ZTo24:$b]ci5OBht?60rLJ4CjD)o2jj_Yr"@7GlSNjJ$?:`djl+9U_96b
%Jt`?o\!qL_aZl3nT!"F`1A?_La%%XWZUOGPmATlE1Ntp2d*eHdJ[l"&bUWuh=Ud>m$/,N[n-:qS+/91F84Zob*Ps)[&.[^N+/O9$
%HF:9nSmCeLDrs&H'1NGHZ&pDXMtp[%b-A9c(PUWW;NtC9][?ld%93T5.7aIef]]'n?=MtrM")Zk<*KKQX/>ap6"*j'X5p*39fD&E
%86TIsYjPnn*7:NZ`&Yuc&3=^as5;fpJ\Zi-?,T_-@O^mZBg;ObjANV-K<,ZJcV++!0N+p@nm?;mMU-IA-#M&^pbBNE%b6c\'&4V[
%kS<KZYjXpo;sCR8,pH<OW-8)@gd!PkR"</<KA%PT2Lt$(RN!:j2;J/C5!l7PfA\)?"upR3S)=",S;'d-43W][1O3>LcXTZqVK,!f
%&+b6;S:KL,kk@"<'n!Pb,t32VETg4a7"Bc7qt[D0Xc>=l'_ULkna\4Q/N;Ng:$Ig8Ru%bHMlZL&842h&!dugk-$.8m,moEc"I7QI
%G[5j"U'0\'rpi\h1L"7#k?,*JfOugqaVHodX@:%#f.!qZO)F*CUa!<pCh&c/FUY'6$1.:MHk<5OI">We(;D[OS75=(GQac>ngIp.
%ZiJ6d/NR*=%2hUYNh`PaEFOCDB]@[EH8hW9jYI0>T5qj?gj&+eBTa4s[kMHlNX>cJ[P-u*q-o.C%,\A]P"t&eK;<pLi\5FZ15:ZH
%P)cQ\K(GirCK-K-fWS*TKAsIFj2C%F2B_;)?e=f7C!ak=5NdQ-A$gHT!7%rX+#5+d2i;m]BOn/[IKm8r+@ESUI._,X>.L51RC]N`
%fiH)Yjh9BgO1mkB\[!7A(_74uC=+d#c%Ve11!I>]&:HJ&%L+=)KM'N8s!=6p'MPVNG7'hoV#%ZF"5bHfF/cbeh<T%(-J^[m5l59!
%Do)8TnaCfRk.o1TaV35*Ukh`r%uYdaSC]LR<CHB"K"q#7pqE$o&(g/@9^RcI/Gel_a>CLcE#$rj&uc2=X3e^[#c)VnX66>W\;a]t
%U8io_?W-<C!/_Y#HoLXQ<Dg5ri]^GJ*/R[^DY;)3M^0IH%r=fV8SDWD/V72=YR)=!C]GC&Z[WI/a=TlWpT]?8r"_Iu/`J<XE:A8)
%/q<n3ndpq:<O?kO.)J9*;"4Xk4c\P\9@CW506p_E6q^7q)jo`ip!1[9c9`c]V(N#fi;UYQ1j-lLL2RT=K@4aU:GA0*W?:iaQ(jp/
%8#WB>Wkh!D$N+u<p]I.L5g?RsW9H@;f"K]n7%36HaJ;m`:NXbk*.?K+#?N>p-N^):\"0V^5Grq9L^p9^SKKM_@M)SFeO[]''r4ia
%XPYc;^PXPBHN%H^66`/SR'3V%33?FF"$(sDC3["cr2BbM,,0XlhoR)?o[h5ep!7,'.YrZT=]@kdoDWB")%C,As6'DkrgNcKr_r9j
%IebW4:60ndXmuF(+oV'cUu+agZC%YF-#.AEnZ%Q25/Jbr?H%Oj[&7qu2>BMsZVo<I0pW=NE-dK6A`6MQ*o-iB+f%uC'*e.#T[&[3
%;eA<^P^Bs"74`jaVs4[/*@>6U3bX;9#h%V*as^Ho9j#:KZd'-TOJm.Y,U&%F?*P^75"o?FcZQ(f9UMm1h3#6-%U1O#l&8aA,ceR!
%J-tapaM<6\E/!32%R:T/mV$+;-=s$#^siA<]k@3Jb6`;f#AbIo4HD\^1*#3QGTDuCVp[4?_)&tfg<U\""ZRb'Ga\DLpl0`:pK+ea
%fU"i[pDp="N=]*p0,$E)?B$-OC(D@17fYk&&rC`ke:noi6K2:n%@B[I>'br&I\AAO$[95WSDbg^COJ7=MXWVrM14T\<]QJ<[Z.'U
%>Wk_3J$_E`6[8J2'!&'4(cl8j(@s!%pDIl^%QT10`Hj:-<)O))U]ecC!QJSJ:L1QB?XKAs.;kH#7V)%h3`Sg\^e1t2e&]<M_`ihM
%goj+rBOerNe&It`3)t(R?<&2\W5sFFdpX^WJ6a/IXHmR<@GE_0kgd+S%0fK@rUWVrhdjR#ENkZ%G/F(Rn(2^j0)aHCTWbueHr:Im
%&$7t#6o4\oKBt8SFSuT`;%Y'SKcXd/82Me4K0g2jeWMrJ%CmeK=Fn0p1&3$-/QY;T[%Gkm]'h<F3f*@9;?Ze#<Ch<oNh;n!3m.?h
%Ueeoh@n$("\S?2kYl[Hm1M88D<k4qdh'I]8VOFr]''22A)M:78=UZ:]]K"95`]JJ5D+EoOHdP\c1G]'iTpn.;_C89&!%RhLD;=IE
%rn5034[sPcPj&)a0(VAY+r/IUE3Hnp:`egpAaLr).?2T[IIgjq<`Xg(!Q\R&gQR.+<T0jY$lGah#lf<uF-+F(/*ldH?d`GMr",pK
%O5J52B16g2UMf`V9C%Lk/Y=0oq90)kBGs(ck3MTe;MssJiWFMuO1X+qq-_CSG&dS;Hhno$83+I<,OJu1#LU_J;Pb8t3Qp,V/7"oZ
%Q,I)u@CElM&(6BH<(N\AcZXqKAR>,A0.F!l)m^0Dn;G3^0FiPU)]\r_Qi_9:1lQ6JegfGI^8m4(%pG.Jb2Oe)ILrj:QSd15P,>OX
%7t;DOQ#;G)]`Z[=8sbbUbH.Xt(.HQ7@SE?)h$(i=]sGsE_CZ`A![t9rmreoChX+U/#nnP[)1GH%^Sbm>e)J>tF/.@%]hM>D3X"Lo
%0NaN"5K@K*m1pt5)#T_4+5)8L2*@0pBn'IJkHO\]JoT^pi";.!+<2"%,`R8.,)Fp](dSl2HtQM3+:,N<.?C@qJ2iLEg<jWihLD_X
%+^3o%=hBPL5qme0Q@J(a;qjkp"cO+:C3_luHCj,ET:t;si:`5Y59"(`C58A-ZLt",B^gS5s*^.r:;[+:R+qe]\;L1nmer6q)YSG-
%<sf\@[ta/o4?&pt8]7$g5UUp!:)dHAGh*qXm]K9loPScW@6EgKP$D4RHnFee;$MZL2"J$"5q,D=bN;s4WJ\^>7-h-q*S1/II\f-A
%YY^&Z@&d$Q&0$'3NR+jRkmh7#qi6G+I<Z_O6s[(8CKYNY3#6q5!L='AW#Qt7Bm^lF+F4s+FZj]1:p$t/)o_;_@KfguLs4Mh^9p:h
%7,oqin-W(LX4"6i/XcuS7$.%8ru<?aBe_k2=Ne4fC8s<rD%r`HWBeem#WqD>=d@BD]d%G,(l((4=JH:P1[TVAYb+`Q)G-Q^?Sngq
%Mk`d7LJuVP:_2kHiGU4L5/\b)QRc66D#@hdJr_?M-@]B;LQH1qk7Y[A38<Mc'I*+Rkq/3b\hU_77!=PrlVpO,H5*RUM$k(L\)(_,
%)-V8E)qFD3,%Z;6+VhkKGs-bW&e[%l)9G8r\J=sYf8Gl5K_OaH`(d/Nd(qs_QnT:i;/s)2"0E)?,b>FeNRP&Zk/=i8TP0g7fUQH#
%<$HF4g2q]W5M-[g)J9nPDr4j&"4)#OTVQ<'nUs&m=!>Y*;ZfH^HJ#3TV$e2Gr2gtfI$i/-J4G\.8RjSUHisW90[<oQguutA0`$"=
%ipR6.pH,hlaT=l-\E#d<G+h)*O%Va-79J(?^-/3)l*q`nJ^f51U"64XNV0i4;3qh@#SdN"LX`*+s4$=)Cg@.Hp`d4H3m'Y;)Pet7
%kR".R7'd>V6Cq2bM8'1cV5S,]0#Ei0C?WALiO,cAoYKM=%Tf:HKAJjl0bgS&e'D#%Z7#lFFL:hlA*l,P0p0sE;P?bt7FbdaK%[pp
%3($-Qe#XREo4Ee'N&,i<<dhC6_#-*85mH%C`mq[]ToPq?)V^$-5Za]G><K`fKK4A;V:>sK@-9@I<^PP!Ak)ui_7#!J*M\>_fU)o[
%[#iOa7Q4:e%_KGlQ/&A%YcDVAp=;*Z&:Z.#E5S"2*IDLl>#LQ/+mf+f4n'&C*%]pI<-Pe4V)0XELh>GMUUQg-UQU%\(p'1lgB?8a
%\6"4gP6ZfU"?DpqTo;JIAdE4/U*^ug.'Y4[[5c^uZ(n*[LZ0"HO[$f[Hg^YHp?*5VLYZSPXq/,,Yk>7qEFbq`(n3H6QdWA<kB1g%
%YuQu!)#dKRl,H2Q++AF3s5cB'rZ.7`!5eV7?8;<BS^--'U"0K/52$6GKKnhUAE=ZD%HV6W,#umP;(upY1nsk4ifYbW4I3.5@!edS
%!n\beI0?jt,7O_dNc+\1i%@/K:\Pc_5+kK1:kG<,nj`.]LtrS:d5]rmpiD`@@EF!FY/B'9O>Cd;V8a[Xit>f<M`2LrN]22])Q=I0
%HfJo9<H-`(9+h'h&Pb#2!)rK(8Y?cn&<'M[P&EMg/rD8KN#d.tWT4-,j%,[s-.kd6[H-N)=Gr.a'ClFJ\n]1NI>\,d8\1ddQ1YDt
%9;6G&btQAKiuF1dK3eFpjSla.^E*=N_tOQ("D(:F#oCa6:2%$$J<KmOF2JQCDnKjJf"O.M2^[=P(NZ(U52&_D!2?)'F"=(uHnqO3
%0EVdn!T1NLS_<82>6@<T2.Hdm2FH'_"\+RdNRXIhYFAL)_@mZ6h'rV8Y%LC3j8-5i_Bs-nlMrNAC[Fr\CZtDd3[dG;BC)o8/\`r=
%2E0\[2Rb2t&R/<UqC5q9?/C2RV5:[]g0Wr#s,C`K@!n^,+;3FWKi./ikkArb%,-b<O3n];?rT%Ho18*jr/"dP1Z*K;W09A?h-``o
%s!QKdEhULO8l-0t^I:aL0Q6DZ&!h`?)'AcqSRg183<URNR1eaQLg$ogHE<8=Un5M,(mh+9X]A8bSPCDgaO%kdSWN-:YPOS1H?;2_
%NF\`VNiF4C*E[]('2gJ`NEc?(r?AZ>/gXDV0X6V]a'hH@cL\Q5nSN'PFrY)V>*h@<G/le\()n@bg\qarj_-Vfg9Vg_<bKV5A$hF(
%j':3Nlo44'Zl][gG+^aY#sfH7LUqaGm3R]j^XA0C<Tl^-0)tEK>btPao^Ce6R]/#k9s$)J=LHgie2\gf]+c98Mar#P?a$!]c\@T3
%gB1Sp=$niPG]3U6HJ[Q=h9>Je3cfd`a$p-VCh.:2`oj.W&^XXcD6HHmabKpOG[G3Ya?kuW-6YD(nM(okR_/&LIf7qb8$Q!?hLj$[
%TYQmIlZb_#+^L==)6J(f05WALOD%/^eht,@AF*FHAU0j(T&T?8ATTS4*cTsbcodMSKk9\PgUnoT''4OePi0tfYL_d+Th^;cQp()]
%X*@"S<5h*Ibj0'P=2QA^TG%T'+qF\0FlD7t3Cg.?.Q7;U%\4n0(K;s/=oL-%#))E2Ae'dm2ZJr(q^k:!#TUGd[^J)CXN#.B<\2Gm
%p9k(@T6cj2ZPT7r)F%Ntmp/$(!D0^))JfBJpV/gM^\98i\8*A7<Nc+Gj?uq=L^\X<.%8`[:*D"l5moBmgt<qj!eU2O1<"9-c,L5r
%W=:\)eK)h2YmWliLU4.[V#-bJ4uVr&M<f12=@KZ[STh/7D;G*)0535m.6XEtU8R^,>uE\KS$e]6I,s]<M2>7jC!Dp0MkHb49kQK5
%\%pck9C1X@YGU)`'/b`t.4dCU(UiK_PM180K#T0<n5(<s@Om+[.+86.0(i>g<cs[Y\gGs?$[%B5)$UX"-eJrqeBEI:mE+$+Q<?<N
%8c*)#RIn/U.%-CKCM,66KCUr@<n270Mf$ZKG^uHh,8[m#&135MgX:Y@Q987ol9;]kSpMZq%tXJN!s*";fc+0cXdc#!1p+Cu(-'\b
%R'eXHkUKaO.(trc0bnu58I<Ln"\2oFEPk\![>_B3qE;7p%bE+nd+rV)<ZdIFP@oKKdYOJuXE04UZ=[6PCl0eRE%YFsEe@AR>U%]M
%N#j,T2/4(SBSj+R[HRH"lMu*Dm&;3.JTRckKL^BD.:b(W@1,p&=1NSn`O/V):WV+18AFn!dRV$SEq:t$rH=t2jS&*pnq>m\8%s$l
%\[<kd*%B'^!F$orP&,8fc)PI65f[k3[!q>.5ojWs!,R%]cS])aVG7G^<j]ohpVrCSWf\u%UO&@=R$ueO@E\$MFMuo<KfcR%JkOsO
%q68-P0ap!d>oQGH49AA6P<_i0=\/It'Y(nd'It[oJht4tEY?CBTeGFdHsVVJFHqZ]*<(R>EHZd1]r>L49rn1RC+kgS#.9@pG8RrJ
%@mGg?)LsE^/ED.\lDMLG=[7&&BC`uN/b+.KOgKU%K0BG)HY1uSX5[\*q%^)!OtMp;\7(lQ0P,RMNFNR.?@kG`j!53uk&Mo*L`[!)
%_HcF?Q'$EQ491i9VB93G*s+Yb`<oVJ<$/rT4'mo`:7:ra;CJA)g,CuU71;U4'Er#`>;KuVQ:>%7dIDZD^,M;Zbr*01N>XWk&U"%q
%fM57,o*a^]4=%=*SF_MuR;jdU/,?J?i>T^/Pa)QpAipth%^A\V']n"N_$ILqUYA8B$BS2`^&-uEi7n0N,$\qL+6fPEP0_1OAFldf
%:1cO6]d_1b`[!fQqqWJRGELlm\.M-flJ*!62Nl)0+BMO$M3Gn8_)Oo=<c8q/bXqB3"b.NYK5Fkta!trB\kZ.k)E'c'c`pRK]ogH'
%r.6K:o<YooO+>5SO8>?0n?95`E.5!ciVsStrcQHJn%a.&DJ1gD9OHc:I&'^WESiS%YI5H;!5sN1#IbY#*O6QAe.r.QS'#^5l3RCK
%iCB!@H=@&[iU*Gp-h3m%CdhM6Af<spENK&UIR-*K*R`:1&j<50K7M(P"M]QCW3?F<En2Jf\ZC=+c%c3DdQOnJ_`2",W?%L5e@>ag
%mLN_R(F&66Y^942/`FasI42HUikaAdX#9]52["GmT7W&Y?W:)^s6bU@i^_bK#4YLFA6L2$K)'Bth3]'8a<TBfhOE0]ObT1i2`_S6
%hu4D;^^4PpJ^_5Ui$Zfa1,e*ZW'aZM!'(NOJ=0X6f*jruX'Xob((i1B.(3J"hg@k(]6lQlDndkP3_A&p2W9.R[o;Ws8)35%Kl1,'
%0/mN/OH1*F0Er!;[U,fF=oVl\PILU**SoEF`ZSDK/<\hc5$?Y1(I_t!;q=[%m9@bsSHW3L(?#BC]-aL)'El'p>*QR:f5ErgD[M>1
%F1FQ\1.uYI_%.5,&CQtHX<"h0V%LS%L@EE^RCS?;)E/8?UC$0-3u3OcWo-:@(WS-VC"L6LF9?jRrGnY^g@<:IT+jQ$Z"u?qW"LCX
%HPj`$qjXAD4-,0T(EMVO!_;gZY]$L=0J9d:8!>d(&heA4/%5$oeV+ig/V<J[msBY5-cRUkY0c9MA_c62j6^I,,?\Z>@$q=o)WWn5
%%[+a480cY\c_oTVS4Kp];nN3<-q.*@&7g=N2bt4eYEhXU!/dg1aVH%,:-i_l;\K7JW-7FMKC(`(fYMK+PL^TmVR.Qr>-Ht`?0eXj
%iEhaH0_.=>U#F*lQhG</7`(pL%Qgl#^oWH=mmL?Z:J$_kk\)&-PTmO#>GqqF:24kgaYp+n&d]u6G]1+C@$/PCQAAYjM.%G=P(g)a
%je7k>*jtRLr7FTJ$TfZdA\Tt:)A2Tss'XlG&W#rSQLdA+/n+Tf"P<OJ>Qn7KLT#U13-\0icW2Uf6FI`!d`O=_.u?+Ae$Vjtor+^P
%[3\[&19K7dX+R$H`.lI6`=@ddg/TRXQb&8TGgBbVmQEa=>JYVNU/d=AA$_,i'2%Nm7ML3h1H^#cCCc/1/Y8MZC=ocRe^C7dh=U+n
%KNWu&4pC?aMFn4.kfDuNUE/?14tc2=VI0iGaN?020f8$iENp`Ieh(0$$s4e!7Zo3!_jWkLlkToCL:fAJ'p_LJ=[(NA+!XSqg\*`c
%5#8mNJ&h?_[hDS*U!Qf,//O!s3W'=e,@U&ch28@9d`<ui(I8/sFXOdIUQ'N1Vc)5%FTiGHh&+K]8Oo"]G?;@nA493>ZJdPfBM'89
%_\""U*Hb\e'I2%k,#rB^Mi[3N"\trg)[MM_`Tn:sfo^t4Q-15gENhIP"&S(K(s6jCc?Jppi]*I0-F9;:'oN^[8Yf<jf-7q=M,+SK
%D522>^[54nKuOpj_^=\D59>"<fA5SH?Qkl;#G2I?+28gFeA&s&+[-!pj[AF9@?!O3FE@!e=4'SiK@JqjGQg\?aKB]&Ek7+]KG<,j
%W1;jc)6*HaVX`MP@CSK!^dSPNX2U'*0LUcc'bGrj)&@iM5[mFG_`8OUUe%sq`[.`IN8Hk6A2(G]!h@J5@iCG=6noP]7klc6O49b0
%$!HJ_*M(I)";.gI5Z#F!9Zp`b\7%nWV6FucX`3AW$_q%g_`7_Uhl%^=A<[LeNqU)R9>mG:,SVU9!P*N.P*CLtV?sCgX`38_3<Fjs
%PMD,SD_X_NY7M<!cA-nkMXaJ^?BDBtp*qsKe,`+7c>u:#"cZP34S!di-"3]!"$<j"*d4dh%ED9SgTY]ANi!eHhn%p3<C9#kW-<g4
%Q7m7<\I/0A^\oC4hEP/jKh1QT0(Js+Nq%VefY?IE"8kFYLVu4:'DbY[8\cH8S>f^$lGpfW[=0dF)E3+9a$dBpl&\XOn6-G)I'"/d
%7Z#Di%%BA!+qNDL!)3e%0he;6W!&H]V0YdHDJ@>X,/_p>(d[tGg1Lq."3Q/;BIPl(K%YYIrqQ6XZi\4eCIK=d>q)0meINQo;G[Jg
%'(eWcPi2.S&.51:>QJ<ed9l`"_1<_QH.ZTX$^_l/9@YcYY:/(;VDU-?-;S%>a%=F0.A^Vo<LhDhHVD8(kn9m..qEPfdof-k)9H"Y
%Frc"o%Luc^;2e`_-Tfb-%.r=`Ch?i&9Kdrka'S"hR\F5<0](5"XlT"[6_sP2+Ugf230Ks,oK1Cl0?NSUqW'6>S;5X&i9JYVkFK5?
%*&pTB1pZp?7p*_^dA>`ZJ=@CnVM+/ES:soYW8E)Zd,_j;3=5)b@$i^<^E5pb;ITX-r,3I@['@M4GQ,,8O2l%;nHe>)In*$clJU?L
%I]d"&$;or9A]AKg77ejhlH%'EhYVlDnY=ccA9?;/?`oh;2I\BYbM1(oeCrmWmE#ZBc$E#^H'PL/irdO:oe:]m>()_,J?_!3R%1%Y
%:1H;$[(1V^8(R-QN]od5nGd"e?(3)gZ>7kb=_@7I;WjF-_(g)mmtf?C>Vg%1`"<D_RX+*()-1%.5`'ei<AamMiVrNiH`b!;4X.23
%$g=1U7q`$)Y)N#B[r:/b[BUg+[KV,JkD#27:3]nu-(:W4.\f2n2-YX-)F=,>"i5sXLWN(.T1FCq,:7!mUrF/Z$WhtVP*gh6a7rb>
%#I9f6M3E:CR:!eF0W%IeK7p-tT*0/sE@i=ZIFo;WdbG>TP6A0u"eE=WL70^pGdJ*Xg8A@TSW\.!*WM4,eN#4j`+TTT8kPKgj8$V#
%#_(:tNR:s#+MB,<aa+d%`8N/=9CRNe1U-.0E$kqa'>Ztub_TGkgd%:Q%SpnkEJ4IS7%'*88aQ+Y:h&`);.cED)gO"97U4r,Gt8)1
%i(Y2,82%egF`8Z8!$84]F9<aIfn<>!L@$O?h+pAmoLKH9-DddRi35QsDi"`Wfo-T+rrGI6:G@,G+h=UX&i3C(MJ(J)#ahraJ-]H`
%MnWtAHsM=o^2Bn8Hr*UTJ2J@-gloP7,\EoQ\l;MI/IEK[0S7XU!7[Jrlmlu'@`RIi]8JYT)a:[B;-&4QD$WUo=F!na`hQp?)9/hq
%eumrV>EtKEAdCk6%1;62<E%K18ZA.neLIo'pU+TZ>D&XJ0Q=>sm7'VU)SdZad2nlM;^M,oDhb,474k3q\cJF>k]=$Z+GCpcM<7j$
%mS+f5Qg%I!q2sUG')sX!LKY^VYlR_UObk!Rd@REXl0SV&h2an]N$8M3A-!Kbr,]af=9`#)WT'(&@4p_:\fQ9]P[IW78tXT7aODN=
%<eYmp:nS6<:*A]1;5&gJ@.#gWMPkA"qK"f%&=j49:b\_)&s/#VMY*cg8(=loBBRBE.mYS!`_g`>I"Xi+B71ZQc1FSbIn2_*M1#Lc
%(aO`kV9sTYNHrB,1DK4[7+KfhPYb]WVIrub(;OI:g`J+$2g[&2CLca8>\JtlAC#i>9L6p$Zl.]Or<O!Bil'bc8Qo+fF;%m+i"YpS
%QQ4!aBEZ7,Bcn@EQq6>X-Zcu\MS0+@L`fSr>I!6C,:sM_TSd%'R.:Wd"(Pj6niXRPgW$BJ6B$Lh16Ui(ZVW&/Lk&t6WM29qksEWi
%%Fa5l.`[+:N(8JHWrd/Ya(?e.0HMD?[MhEMfYjjGno.DDW/9?+Q*"2(KiT)eF4%M=fnnR>8i'"k82R)GK^fY&;_t<"h0Z@i,G,!%
%B&!_IW72HI"A;!=Nm2AVOmEO04B]g;g.I4L@^_g:YPJ)3s6D)L4=#e#S]JA#,OVnt9>^&7#aU9L1D+:Q2ZY/t!QfYL6]E^9Blc$3
%_?d/85DVc]>HrL<*2&=L>mV&C`876\9Oae8aD3GHZG@L$U`BM/bep\#0Ft%G&;$/I<Yu!g"5-o[/`$m1B_<s?Rc2K[OKHA<rs9'e
%edNo<HP(Pi!9+dnV"Uqn\c!YU%O;i&6NMn<6+p(E5CQ4n&n7k$AJ*EdBiV`g:sXJs+UaTe,&>pq\RIO_g%e[#$Gf8Q'<K'>OTa.,
%;H6,S$cq=Rgnmj5i&[D=@Jpmem2&JB0cucA&5dp`EDHcNiLe2ME/730$LWpoU_g-m=D8*4)`'J^Nca*mOW0'`VGE9V%ZU*c*^B_+
%=DW@:Ee@p=!MPV);9b=*por63Q8q\8W_b-KR&<>\Al]()V`"kGd!4g!$F0t)4EbPU8"qWTl!$5+/tC#<4pG2#iS33]TOgK0d2SVo
%NX/GSUs;]V2Ebfk;dhB.!(AVtTs97WN/RlZ*j9B>Q*!G&rerTUBc:I^4!#0)K0bkK\<6n:HfM'26.;oD+;"r+?kN.:>(VoDq:+&-
%6p%/3/j`.0R'?Xga#11E@$eOBK2&U;>EXO/M]esX724_NJ[ml/2NUVq5J5h\1^6\>Nf5i9(H=?UFk't"S1n(KeD,r>0#6NPO]d>i
%6qj>oSFQggFVf![PThk;%`FUB3/T@)e!c+`@jPUm#i='t'p<_GR%[0g]:`KLK0rKIIT"4o!2a.5K"5,.Nl=773J&Ck3/q2MAMRKn
%pG9\h'<Xlj'_cJG25cQRrb6S;87]Q\@[!mKe2dj4EPQ]<$OBf>JlC)McSdi2.m1)qa]&BMSoc62E?lAlNl>['<crf7T6>kU[W>k:
%JA(Y(P#1l^PL?af\<.Fg(/):a;*]gu.X?Z!EO]Rl',;(e*G).L!RVN6_Wq?8..p^[,tY@#./dO.+'nt`3i@cjQIt71&ek)LJc,K-
%aJ0E[O@-%[!Bak-%D\?CCm`F\,p6Z+fJ#d;!$p*]oRaDR%e62l+<Q/"DD:?@>(d2ca0^6(?r2g0p^bDIZjAiA'/Xj,@^*D,]WaF?
%TYi+O79%\ooPBQ]0Z,gQe/M!u\mo#b#2"J&0,\&j;L1n_f9d6kPRXj='P,.a$V&UM"-0,dg;oLib@fAP$ue$ud\6An3`YIXS*H2I
%`C?U4?-^dJI)1A=LTVQTas2Y)F2O-o(:_t`.=%;TQFQLIThLu"U3Sc<(3B]j+8T>=e.b)5>VO5)!$VW.@$rLT:/0@Pi-ISd?WL3V
%^.Bp=l3n@893ttZfYM&J?mMusn>SR0#p:@Y&Q[h'<;OOcqC[&*@dWG2hC,X2,F$td4N"2^%jI\Plc*YC!m$0`-a:G`0rktTVIc_)
%Lr4mjmIO:tXNsWGAoM>(8<0!3J0R0j'eK&!i*aGdKNc650"g>QHbP&-Rn;U2\Xo<mS9cFkDASI?#d&gfb9JIQKgGjPDbsUu;eCV=
%,@Oi*Xt1fRSFj`V)k(.`O@q<L`n(9"\KX5eSIGE3pAE8E%eMUl0Tlr7^bBKIMYOi'0,#jD*jfH'FB<F%->q:!!eQG0D[`"[[;*N'
%IKZVu)b<ZMXs_\+*MsOBWJe2on&B$#)2GORIhE`uZ9($0YPF+j8dD1J5t'jKql,\44'*XaEFfefdpu9a#l(2ucO!WUrIB^+XC7e)
%Xa1%(%LM(7$eGNaZD7MsNV6FOklhM]Fk"J>BP\LD0b^>fjA:VIGR07ndfLkkRZTl3a=BCpc5Q+3H3U'9U_L6mJ3oiUCaG/]%!YgI
%EhN`<PT;dZ$C8fL8qA^?1`jq:f&#>tQ)^VddpS3p;H[(^'F?>9>-g?]g*ii3@It>H&h5b/R5?`:kqo!=n]cr,)@cf2bNHerLP`j_
%d7/0ClbG2<%XYPqAs(K8HTb@RgUZsfY7@7L:J*u.8`[3u'MDsC_nd^A;\fr?D+?6q`DK"t8#Au^#hQ1+<WYWDZ=D7T@$@m/$01?E
%go99b:<iq""Y_#+i*#$;RL:PXCk_pI^)Ql&&`Nnsl?WSW[05GZ3\t$/Vm<K_K0gUh^I6R\Lt5o/dKB,be=^mRTI%KdpIP*pUQtml
%&bbXWYaRAl*49hl?H9H$Jj,Ysd>h0Z3e/LQOtKOQmmqSTA0h`7Z/e*"_,MK3o`2bh1H$$<+7'R)RjHb6H%ega"V*n+joc[?as%u,
%C]qsi('\H=_,=rH:mCg4U3Bfn<1s=o5/+cQZ'#gP@2k@lYgcJT<[Rcj.3qlVkQuoo$!!_']R=@qq,=$@73^AL[8!ZQ'TC^/][/-'
%VVi1Ok"0P<&0Qe%$@mgUV>pu6WKJ)X;G2O#$GijcBi`'$?n-W*`VMuA?7iFc"*RS9-M?G!P6rZW1?-4?PU<NZD%%Pu]69s@[\0qe
%`D?>M+En+h-u9_jEpa5^mlITR&\OCE]\!4tegl_Y*?FS;i%[+4,OqH"'bgFH2aJYBXJ+aC9*ghLn'IAU=Z6KNZK>*k4G2=d,gtO!
%W!uL=MCFUFN23N-0JLHT=6F5G+;CB-_RsD*+O^<<hU"tpcj18NB_:":"TK)FdJ>C<dS;,MBm-qCmkrusr5>Dg)RASW#TqutO*)]+
%VL_bO39USVI`:hJ:m[`;:ld:)i]@O4'rmo;%3O=4PqEFsm\?&`.4_(^p4tk]WYRe]BbOX9!(i/XT_T7+T,&2&cZCB@PO+@@BJU!T
%QbrF;@pBH2KdIA))pMQge-&jH7\i-s!P4Br9E;bX@"P]dX9PnI**W^oM(Jno`X"8%__cE?^jntpkSON\9#+IB[A2&_e(gU5Rlm+S
%coJnSAP#8X7^"I4\7P&5KJff?!j*k+BW`1Ci;dS_#OQLVeZK"HC-OsfU,.T&X(VZTr%c<U@_%G'!R*3L?70rc7i#m02M(]Yb[%Al
%)9A8SP^'ITl]YnK&j6#";jEq#!Yu_K<uO'Kfir3=..A''51YLIc+S[W(7TI>\o/8qB3*(S'$)h&GFa#;f+_U*1mmMP+sbq]9&rPg
%I37^9=HqF2j_Vu:Ldf1D3OU,D:lR"?J+QW,IL[k.bYPVHNXG9<S/MGaCoufh"R$Ei-:o^V(-erJcr:=Are,;%:96cn_^:24(k=L7
%TUo:3Z\[0<DEn8LFc$liJI/!F"M+%cZ&VdFJQ-l)U,l,P>1<+-NM/$W#$\Zh4Ufq5>GtOUOsY\t7"V[caa1UB6_Y*liq/fm7X:^1
%?]/48L=^j&7sGk!0*ALlS/\W)p#5F;F7a`_fd3eSS=lNV83B,TfrBAQ$<t>S'FqW?&WF<M8,A]*3(*piO/XEjPt]VW>RO*XV8\<U
%>#<EGpAe#;,hIb9:R0K3M79ipBf)E8YnC@cY(WGVos'dT<7&F/WXi;7TlP%4'S^!'4H[QA:?rit%5(8/qF/Te<'us@Y7(_soaU\(
%U_f=_;)FJ.W>D5V<0.M10esJ</H@6[/6&WM+W9si'PbOQ3JqnY;cc:d\O$.kC3!8V<Y5a@5U#MGVG\=:0<D&'lgAg&/rjH\!nj0j
%%B&k+p\m`U(KC3'%7*g(1;pY4Q'(eNQ5YBeE1M`VqLoUfbOql_)SuY;p_p:mBW<i+M<J&7g*rp'9QU8D!G%_MOS@d=J;:'G32nHk
%`g4(:KG'DU7DOIHP!Gn?@qu05m1I0$0h=!cLBZ.CD]hZg[$71YQ-CUeg-]#tXPnfRT(V54L*:N55O+(l,["a,Y(;7D@*nF1_EJ,N
%"/53EXsHbo%E%7II@Y)Q``0"CS-?+l@1^9?7bW*t"I^ul7&1B3";9b=8LRuBAlkqs1YMAR=sA<AEiLbPJjVjj.'XD@#t$h]PR5D=
%#r"l`oT>i<[/c$K`%=,<-JLUr`>Ma3=63:PBW!u_QjqABK(9F49d6<>A/^#]rf=I?=A,7OC_+k?N4'/e($nKRiW?rCc$MENg/0er
%G#Q1ETc4ToK2LX/#0V!bW!+k,$3u0^5\8Gm*4f"RTd2od5gs`R%mu*k=NZ1-(6d]7TADn[a#sfe8\JiO4oKQ;i=&MH4E]M]K1%?>
%\G.E)h4]\M2)]-P1k'/;M4'(U]k3j8QP,?aoD5IB<8W+tkbAkjFMsc5-'C.j3Z]VM*NXe.'M3"HYZ38`d,_NcOf/XI\R=YXe`U0M
%3K:+mA5]RI(n$tPE3<W5Opm4B)pJ]r/;$)S5UJSok_c7pKPfl(C8U_AdD9T(0WN<$XC=bgN7BU<@8%U(KiQm)X;u"c.T>_@k01ZZ
%>(R.omN_;*BrK75KcsU;WG2I?iXiJM8=D:'[B>dKRnt[:Z#Ru>Wq*+`=u0m"N!49@i(98ec&ip2!5'/?JlNsLT-C.=YWAlJl0H(S
%,U<X%?QAm3#E)Y92<V$uS?uG1R_CqZ04hr.OB;`?&0YbWC.PH=C*OTJ\b(Sbd?i8/.&UUgMoifs7FEUI8kf=)lq)7Q(T'D/&BZ#D
%PKMN\-F13L27nrH6A)CD'?"X-o2lgDlBMZ*fjsC>I1\Df0FMc99<@8f`N+*?at`0rbk*;<hID<kQu)X*9)9]=0[DHnRE*kd"*8.l
%pFC$+/Nmo)VQ#Ti&jC0p,U>?ke*:bi5bhRT[&9m=\V\I*:0E"`I,u(J@CCBKdX!Cu"*7[jRJEihqD@G5k#D.%Eb>)-E$".n%9$6M
%NK\pZNA`NM5V2sJ*5jXU13f2@K\YuD_@'JY9i@=9ojal:NoIc:biE]0k#2?uCZ73YjhX_<pVS$Y`KE"V6^;-8[jmf>/p=g)CZZjM
%'3B3]*$I0mHCRMEb?]DCm%&\1!o/ruZp#@O/C)Jh=\^\n])+6e.@AU!1H,973N]f1ZX#*`31PRqAY1hu>'eCDGM%;aV*E<J0M3h^
%*-.%6OGF.QXa4e.O-m_:._mI;HZU^;cB;%JZ'tJ`SD?0fTeW70I+$C@)0A?&%-NS8Q?94r6r*0"W'R@r#3M,lEo0H7f84s[@,%Zk
%GFdusUBp_+7l__]@[n&P*Z6(aO_7D(Grgq[;6Zjb[p/.5i)AT6)gDm4RZVEROMHc'nY?m;dF+dLY1>9\n__IX2BS':`KJ1KcDV(t
%<!Pe$@MU9#J@Ma.,ha<TB;)<>-.<46Z"D@P)gm/OQR!A6M-l\C-.PU;L2.8^ED_t!E.He-7iYH3c(GmZRKJQ0"_!cDZd5E^p:RC-
%oGZ[88n5EY&fb[&)pJs(3;C<.&L=h-9?kl"W%F4''r=qsF#=OG:"G`k"!\P66nKbO9B"JHLgFYrCQS?/WD'?[jRT7F)PGgCGe.E2
%'lXIW[jT05N.nVN3_^UC9OgNpYM%S2D7Grpk+[io@7C!HTJY%/_"XX%nKU&tAc@!%JshQgMj#("OlQUc2>`bG<'BKB7"_6\kj^,O
%XP&"Q@2sm]^P2s(/f-IorGE0IWh^@V)7N"],*)r_X2KWV/bdt_2-6Sa=!5O!Ad#CK3nZ.97n-DA@5j/Z08Ac!)N95.+jc@65T7:+
%=irIOJYYOu&eR+=Vj`W<o^g?2?dPNd1UJBHP"\`:m[WRSRiO0)\oTo($QFp5RUog6m"j<K>fjT'6\Q&\CV]P=`],89/O;5AAPu8+
%%>"f3$273Z%qnf\q+0lEOF81i!9f(e4bPkrB1Hhr4ht"uR:t/^!IeSU#2l8RL[q`:nN`Ipc7'T$hSD$Gdr]CPPW7pFg53ZUS*X7I
%4MC6mBBT`!K%DGk6'2/U^I9?&s2LUOkVhQ72h`9N-"t0g#5Vt#fdiP_\-`s)s+ORC"!n;B9SW\8HBfDm>Eh+t!mpLP5^s%RLrIl!
%Hn-Cu9]Elb:"X"VPAFg!h,j;_`b:Bk.[3pWoHrkqh4;`<daY.Wm=$VY.Y[MO4,@SAGW1f`1<eM7i-nM!1pL'MS.hO@M&VpPiHU$(
%$bblE]h2a\Jle,6jo?%5/^\.$Wm%IKbi(C2SqnPs&d'sGZoTs]:CA"1Gs:967No]D)pC!m^R4FH0_?KJ9aF)^h%$<;)&K!#;%Z,4
%Y&#<!]W3K%1-';dE"JS>D0H!rD;Z<YUcTMU]S'!);RR-<HqCA1YU,'ocTqiKjA]QpL)XjU-'_!BFG<=3bsiH+#cHMjOe7/j1mi&P
%$@I)og-hhIg%.Tu+"-(c6#)FBnAp,606B<e^F;d6.UY0''VS:IPpgFjFIi`/kY(82FsM^`$ta&Z!dPq+?qT?G>MUVMr;\PbApQbo
%'%!3b:Ob*bi"DirigC][91[8RM$em[Os'$":sW'>9VclaBsNC7,!cWr&!\G0p"_IQZQ,N7r"udcLPWI]4G(YUHH&?nR>FBWqWd`P
%+<tJU7<'kO!0r[OqgFa^Kp4E,AVRIs>f'T!+CGg=2@Q/MRB4R7LK];2I'W^%MME7oiq=tuB,!F0(8`d*NiGmM:?[gP/[Q>iar_e!
%>i\L/T"-IqrF^lLVCmjD?n?+K4#0^U-1W`LVV:,m04]'YIE&6h<65.oP3)-\s29bQ,MXZ$C`nBTo&TTb5qdQg$0dQ4;;[]'LPi4V
%I%HEGJX]s+j]6`DdSi3K8#Y**K@;[$LaD#>dROM.6Y8b`8KC3pM8[t]#p[F(C!(ab"P)#D-"M&=b>Odl9<N&hgJl_dV"o],cd]`n
%!h#<pE97FPX_^`R"3Fk)Vmkrm+G<A\XeL+Zb:8#eW-O51B@HoP4A+Y6aJmHeCs8Lo=.3spap_:jk*S.2NAYpLlqA?OB;qd6c/sD%
%JI%TK,O#>FI/as+gje?Z=sk:TAjNA'7q@c_3u7%u'1hX`%Bb*^So\R9_5XTJ*J>ki@'oMR-bh[t3,rrhS3@V!'H_mkSa!sVDP(ka
%Qfq,AF6U(iR.MRjkaDngH*g&HUJ'fWd$.GL2a\X/7+K,d36"RoSf]ao]_CPp,?RQ(ZOpsk(&@eb0jh(r-YNOtD.8?Hp"4HG7\hE0
%ArL8+k6KrLPBfK/$)#N\Ll6QA6Yi-f1^:.$oNDXIW@e[LF:j]4Nla$pNn$))M<RbkQtEZ\cH!0G/L?W2:7g\^4B:TY*6A%\_C0fE
%baBuEnj'ATBO7RMC/P5**5\TN5^S`]OE<NSMVcJMrfa3sn-D:4?;K^F+5j%BdWTb4Fg1`RQ=/>?U(9b+icPqe["'p2\K(:B"jg.4
%\oPjoW'ZAV)HTgF!TEL@<cHYJJt^K2A070M6>Y\EH,m5a!(-+AQTk<CqO0:`<AIa.bcWB%-!KgBUn0ui7bEZQ"#*Op.m/)b*DUFS
%8aO@9F:b,(*OJpNq2]:nkiUl(i6^%1Li<PGYDEhEPDMRq[=lKgQ$i1^,/hCk&P'Tp\TT1pFJTXd3ZI^9G(]It-Lo/Lp;XB-#(!/p
%`'BX8cNMFkm%$iRocRG`Nkco#a:?Zp`q`<R>hI8r0Hu9>iadcLaQ66U/p/8V]$r*#HP<,"'pD'E=si_o'[*]>RH%B48*mB*M\g\&
%\m4"(,lXq<c^&rC6TD;MQqDe5<TLSiJ=)2-k=kQ-9upfXJmKLE/F8@6eBl'3l:N-rbp0E+G\sq3](AD'ck=,VK-><"+1nYBSL]Rr
%QS$5[ZH+c&h.6E[aHV74L.j`<MM8<nY:Du0,jbf'>*t@Go?!5)Y.YIX?GkSES&W6S!i,P<E*;=cg34N"@H(<=<^'jFjcBh8iCn3G
%AF4uHa<ctTq%J_+ipcF_mdm/YQ:Z(4"aCr0`A=JI`JV<fa]RX%%$MFA3'87^2>>;fYcZc?)rY@L:a:!#V@at`:NfKo9JK+&ni3pf
%`PLH2`<;Mnp+0=tAh_R4>C/8.idAj!b>^)_BNS0+R_k92%LIFcEab%-3A<G`H]nl-N5`k4j&*ZIl%MCb>QB,S&7afF[eU4W'B$BU
%kff)YNW@54WN^)'H7/QG)&/ihZC-MRCNu0bCW7Ql"d18I>`:MX&ZmIW]b\luDsbO)JADC!?*/1H-4oJO(N=pA1TVCa!T(nh1m.Aj
%:aKI]XlhQB1(`^TJ99/b15Pm]f2\VhRRX]LN9:$(7$B@Qr#o7&'XVXrU&Kpm-W66:#jrZ9A(DM-+r2(tJjkh_1L&>&j`tl5*KtUk
%kOD(&jIXTm.1"cXS':-%4rQ;6Kg6fc)b;nQ%&=SN(h.sZcc_CacOB]jqhM8GApb.DQ_IbaS1uFd"[TcJ6kpQUfe5Yjnd5k;*g@.q
%^9,Bdc/bZqI-at>hb"to[g[D&7(TZfZ,K3SX\n=#4uCq1HQ:oZ<:lHT@*Kur!iF4Uk-=!JjBI9DHIA\8gTYO]2FG,s<aspc,tpX[
%S+&S&4bC+/ammR9JGeJ1-<@=d;(053C<;Hg:=BAH1aq1F/4b#WP?TfH<3QFM?.Ik(C9+quW^b@oLPG,D,KHk-SR@JaqB@\eFT=i@
%#IE:dEDfOmF/Q9T/BSXH@^'CNdU:_\@JW>f=7%#>c4h9idJh[uH3PUjeOR.)6tZfNE\K).gk=I])(J+%8'>^6i[-F^Q`2RO8Ss6%
%F0Ec*Un)g0c`o:D&R)>bhoq304NbXq^8#1)-NPU$9&_8&b9FKk8NB7)=p[2<AeMoA7!\@m_l-IW9qH+ro=A4Qk+.d5)GHunO[r"f
%R8h"X,ZG1tM@=r[;O7ka(E='r:^igZ-39BZ\@Sf`E['\<EMAIL>^.1dN5;WZ*cP\T/'Vh@\td*@:gj?<e@#K6:(T`aX!r-`=P6R
%]n;k9oAE1E[YSDA<EU(JoCh*&b$#%*/"Zq]:0%NPB`J6hE@kS@.fT<CCG8M9Jk;eQGnVFEj]:?"<"(E)eV;ic69V+G6mt0flP;l7
%+7:65\;hP[$nIDY8>tA.)87XOoe]buOOjnX#SsCjcD_GO&#Jt#ID7'Mh(T\e9/_+$k5#0$Z?80GQ^1U1K>Ue6N]Cb.<%3i`7loMK
%Yh(ecbT4c`aU)!=7l_<9D)N/!bDC29o6,)A*8ErQ4>KD4-M(>\Zba7*>4>apk6i_0(mHtH'Ug8Jh35cbPh\/'YbV?P[&*h/d5@5a
%@A'N+hSQ8KlsqbW?9uuJjE;LQPKM]rftrUVF[amjgr*XU#0o:A>V3;5=6#&b2-2>O;Y+Y(Gd,CBHkXY^k=*Kl]$rPB;^[XmW/d;j
%W^0oB?t*f)Ce0A#'bGYc<.uj2MF#p!Q+UH4/hSX@XlZEKR(]C4&4pV:LHJ^IUCYAqlCgZk[B$2F7Fa6-6XVYh#'29a,%+t1Ol\(;
%$fem&la73PpV`.0d>I%]l,K/N"cZ+sFdDsl,bO_'.asn*YXHkU6cfGn5o1c)^hkDs$8l329^D>$)$c)c7OgeGN,[roG-qVLk8@'`
%]Pk+5,c82Ro]fo15thYOf>ulTU9T8N&ge\1iXV9B"=rt^7`2>MRA3?*-10stHt&h55d'B._]ac`2p?l`K`s63dgpgteJQ+qM>4sK
%<27@om6MCo"Ug@kaK/TsH1&(j.;OhfT4e*c.$3(C'2?HKR,N+DbY(YAdIQY0C^2t>*=S@_;gk&#<N^_eQn_&6=Y2lAkH*L/e^c:"
%m\9Cl@_Zc1X9=lZ_(O<7-;lTnd+J902Jhjuk+=DAPFD3G`@p`'LhpocPhf,1;kc9H&M17f;l/P=%?.)b@9+\Y2_f0:a8pS9WGgJ2
%//FTjpejg;5)uYsO[TH80pc7pgj\`HUO,9i*8JJHXM+s,>>ire+?Du<MFlW8Djs\2TO(>]+<8V"i7@iN:o?l#3ngK"X<Af:dPZfk
%@57VrB6sRAL6]j)WSDT1[$#hkWUGFO2Hk!ehFO03ELhFG!TGOWUsC9XS[YE]P_U-=U^Bk\<d+IQ-Kq8t]eo7Jp7o,?3@!$S*SZ-n
%qn]#'(_(O0dp8Z!NfLk<1#E^+qpb/PkNXXcWngYpjHp_9X(aofQ?%M$m`nPA(.@%=_R3Y^ZB2IA":[/OM%C@!015+ln^jfG@H<[5
%Zl:9^Nuf=9;W[P6J1%\p6?*/V^_Ubh;m-suae^V-C/0tZ_ded+r4L+1O&B3qa&'=>!^-DfX1)aLQL@h<\qNi@8Tn`!KhT5i/Kg03
%KQs5X7aeq)$3gTp;I73)#RcE4KB^Q%(n$!Z<,I5;Z@rRO*]H<G<9T6,GgZX#<uXCOS.\Mdoq\jq3jB#U3c4>5Tc!H20OD#"Vdc6K
%\=F3Oc%f;691hp(fr0j5=h-ZA0(h/*p-CTnFk'orCgblKZUHr11AI9q:d"n7B#Ou&ff88;7P[fgcs\,#'<q4H!KP,":[/-4o4tQC
%N*'%+,DgBYZZN/F[P;KL[c$'ki"R9RU/Tj-j=i<&"P*j]!4pA]1E9gdC#VBtS5M_gBU^Aq@g0%e8S%h0gfEF/!M<^MFbGQkV>A'B
%;n%KlDF9@b'G.qPgajH5%e)RdV?mdgN^gtmos9U"GF&jSodLIt%ZHI_0_jtd8r1X#=dVfQ+3s$DY0es/2R8*VJg+MDk0kK%W40\m
%o+B$T.=?@t22A!;ADW[:l1=b-rmVu-G7sXl7"q5cYf"C"jq1jr,PIQ[>*^+9CEsddGlCK!<mQ9K,ahU:g2VGpH`jLh'N;*2,I2'h
%kU$mNFbYX'[q3>EqJ^acisDHFRZ/("INgB=?m$IH7WDB_<:NBXQR:"5N&*M@EN!<;!mQ84&s1J`F,C_B^@`TaNh"Ejaf<!c'?88B
%`DfZcQW.c%nkqi"A@.n0fL-RX3"AW<+NF*>KCRLOlA\Ft8oS`HPj>]c/KQQeR#`ku0-IL9"NOtO)6JFGj5uHca[2;A^"k['0sq3^
%705iX,JerF2,^G1o7u8hB.:\=0.[)d&'@-JA:h>f4%P67"dDq(0TJ/DfE'p+`ukr0BP.oRbQ5*O:"%Pc=!1KOHZo%Tp2&)3VT$6A
%DfXEWGCH2(SlqN3;3oM0E!/<D*1]RO0E_Ms-0C5FI56!tonN=olV(=@9@EN[:,I_o$CJQ&#qbZD&(nmRk((nn16WI%/ZNdF>]iCK
%?_]>X0E<?BAIXs!75"diO`J8RP/05VQ:6mc]$*YWNWnhQI]PZ%'E$#dpP]q>bH18,Mnq21UIS<3W-Ko1daf%gjcc%t^pIMo%`C(_
%.8kH7*DVIS,r@(J=pHc15U_W6E]jQ0r4mn4&!,+i>Srq`+KR6:[\mk:F)8s#&Gj7Q7=6+j#mFN>G2FaV&luX8h5ZF#(`bQKYfuBn
%o_&MRIrbDArp5jIop>R9gS`r$5J'%)n)EQag&@K>pKF`p>Sd]UaE$e^h6S2WQt7>),5^3/B1mU0FS*u.>fH0m="$4\EccZ?^V6),
%St-qkT,o)Fc1QSdqXik+>3Y<WeNNlfG?:-_\"A(uA\Lrcqoku8Y>0"RYL3@;ahDJs34.lIkGK^)Rle4HTB=2M([kY"YQkS]B7@sV
%6#Y!?52X-N/%9k0#4PE<`]<t3kC<Eth>TZ_4-'l>TD;eWrL`nhs-g2Wn'^X!h1,@#gR\8bn,Dr.05BXR,8Oj1(GMDFk^bFe`28_U
%eZH1&":PU*Jp1/1P<2D!oTSU;Z;=r,655Y>M[cO"P^<f@rT&u"03l3eVWc8`hZ]oZheM*ojl`GVSGFL&WH@YB@;\upkA:s*=RED(
%!$A[Od?q7`,-i#K6$3S*Zb3?<mG([i7]/e#,9H9Uk1.CMeT5MIRUKAu_=29ePIX_sC2e!S'9UWP8oBdS*R8\S#'5*2&lF;17f8c!
%hD%Z8,c-GQjut"+b@pF&GhCFD9PipO(A8LF"cAAZq!;i<XRAPu%GN6-*oJeJ98e$s#BI.2HHQsIA#(N><E15RJq8;d&jU(lX2H.g
%:[1Ws!q]l_*LdPm%\/%0AJ77aR`*b\`fa96T#jeS3rG-&J7M8jfGbG4)F8$JD+6SrfK]aXCm@N[B"E\pj`0#a[>/;0.(Pck!@<(`
%/m6#<%26kA,T[C21UrbSAirKj5=fS/NMK7u0-MXRDNA;85fat*k8qFC4'72;]>7BS'ld<TrfrXI,qqPQ69^f-ap*Dee`';>@#-pY
%B.]hhG_#-.7(X,'#h$X.Ge=I!`!:'S/!i+uJJp??9?h\oaQlgT""U/c.2\'lZ&MnGOG1fI.'AS/[=&``!%1-=W`r2f]LnYqp]mjm
%[@:A@QqWjQ7Q>tFM._g;PUV??1lpRj+%b!7OWA-]3#U>Z=GmA`N5Eo4XS6[4!YlVjon0<bQ:WdXke[W9mU`.+%79BZ(I$NrdJ'Ag
%`>Wo=_M;G47Y?XgAje()MKgFkJ3002l>km=$$%[YCoM2bW*0#9%nr49/3tO9b(\'nJtWs+0[<&UWMTIK-"fO-V(e_Q90e$gUba]e
%T3Xo-XuNXl7GcMP$g_l.0<r-EiF/l"AA;#\OeS(^i_kPjGat.h-&dtK&mBlgmXP"%-G&sZOkGQ6P'GX+/M'CR($*3"Z4-_:betZu
%eg^)siPiW1,[RdP8qf!](2)TEW>5WAjHfmg@3-V'9r0KCJj]TRXGg(-B+2"="oR3^B3o"L?$qn"RM.6-62ME[f//=bdLc0_-n"2r
%)k5QoH*8)5di6\7/*aa.'g_/ID;C,nRoAX[G[975,=1dD0L)Haie/-R3Z:ta$6&Bk:eQG4G\]%7&fMb6`AfV)c`")f>/3PpA./Ud
%g.1A>afmP"hk?imQuqTR@IUCL+`S0UaQQ2(_`5f%VUON%<ZWP(n>VjK7qJ9'f;Q$bbTGt'`$)+O\:N&_(n4E519cq`LEaX/%C"nR
%ZIQ`/rJ3Yc5,_g=ZiF85$1]/1k[TWkH'T.IEe6a:O(Bu$ebR#to4J[J0KQ$?<Vh4?^h0qH#4l7<$SH:ri`0Nt=rD^&s!32CWXcN'
%qg],.&ff#Q^ohh"c_ra.ok$%E[E&dZ2`o8Nnq^Tg+fcWqLt<1'+;+1tC[]Q,GLD762H`D4MWbVK*gPe(^A8:5Kms!-Dpu)uApI-U
%M8cB"fYh%YS3"W,bRbMVP"e89*<e]@#t]j8M;1^@A-`;6TI"+^@8/?1nEu=&'O<MRM;]ju$MIkO<E]1DOu%qSVqeMD&t7N;1%'Eq
%c4fBH7k1,s><5a[*%t2MPe5B8'BR6lN&!eIX"^2)3*S2KU>,2>f4M<.3q4o!J1b97R=oSF7`Ql*%maGn/a9XoW?g?XP@K+U@^&+a
%0=#@5R>O&q0#EV^Ys:qnM_.\<+"Y$rJ/AK)3bVD5d>]'X-k437TES"]Kd?0pWpPKJZl:l9_+8]Xcidlm\9Pfq"A(;'4,s#RX$`26
%b%QO%WLcp=*Cp;;,/u@*2I,Yj6R?tgRdQu@#'ij37SJE>"UoYh#rpHp[JBQRFP3BV/P9))W<7otW/[uklq=uokY4e+0Nd$L5Oc&1
%LWQ7R.t<MbG(d4JB"p(N/F)L4a`3-%iDo1iWj4pI4M?L8a[kD,;?nCI)T=Nob9db?&qg[P8I$&jdcr)qMEg?Q9nn2O81LI/M'j^?
%0qkN;LNhN9,l2kDD+-k+Q?Z[0\=Og]nfV>TLt*9*q[EG&8_E.m>%"?AO*nsC87PWiPT4n-GAkmnd5E8:grVr)kpE@`ku4P9C9XK(
%iQ7*cO,RO-FsT/=X*dfSQ%["V`/$u`^+7IA3d9"X,@n.YQ,NUn/3nMD:^S-kbIXXXP)4sSG6T-ZPPE+T;s\on)s^M.LuM9%4`_+-
%fT$_L;dbp>Y:*PS"!p.YnPXKPH\I*5NHdS5%4-fo%WY]Y/-lQXA?<G>qI0@E,\*QX6`<$cK.hl+fn:`t7##8[\]#+rcN4a0X=:-$
%+=7r7EOi=`22E-V)7>H6*OOlb5"TXOOKqk";3Gsp5bqg'A;ch\R7i:NG.&$JOib$M)04RT(6KZjpSj:?kV#G+&8^W.;+RQfSrqH)
%E/)48PH4W`VZ@*sJr#*0lhEe$%j3bDf"n>C,`jBXlVK'^;nYCdQje%_J4bAsS&FT%oohn^QStJ<(S"'0BJKIP`*tMKX+C]S?M_D$
%Q:1VP3U%`o#]nWTA"kK>&e3[4F&($N>fP?N0q7$+'/9hRGYA!PNUV*hMu#V:-&?AZFRO6B^SiZm:m2XIG*Zkq9]GS=h71+B8(2m"
%GcVAhM!6Ka%jhG40h>bo*1\H(/3p)1LG@u+(kKaE.L5?32G%mpED@6b:t\o-%)]/ShCWi('AX(%U`j=>XG8ZE:e)gj*ERn2:-<r^
%S7JIr>H(,[\4G3Cop!-2\U!,]NFVP3oJiEsogh3elb9l&deS3VG\C#f_qDtAJ5es=;>Ul_WXpqW*=IQ%&e73rVGds4NTml`+.G=8
%*#iE@GuGWL.T)_K(m?9*-#\a:f.3P\+?C<b]f8'B5D".I;UO:]K;pIjq`5.#e>Y;ja9Bi%_di+CE[e72/tAMeQ^UfN4K<a\?@\OP
%a^2<H4i2nW'a63Gh[-Kl8reEYH+=,99[kA]`t4TYLI2R:$&<eC:77;@7T3FZRQZ[pq15@\:k^KSSlrTbFlQ8D)Z%lBE7=DKEm.Ld
%hiU%/1-mo--.4V;FgFp4lV08kl^4usWLJ!+AisL2M&(q1%d)eNm;;E;2+NO:OP\a`:rfWDqU_ghIc;g5'FJA[&k7LUrU4T:1a=";
%W_cJ?Ts?PTj*]T/M\SkXbe#$k.Q)i7R1C!4[!i?a8M4t<q5e_f5_M+"K11l4_iRrnBJ3IS7L+O&lIedpMO[FPR$:ec:$rLe(ZZYg
%Ua&^te6qG\_aJJTKptM-]6*,%jZpJ0G<&IQ>W+jeP\6Db$ubVJTd_25r_H2uH*Mj"jYRa=Vqokm%&MBG+-m".a&)o/H&Ip;1tBdV
%>YCgeDh)2Rh4VmmGl^ip8:Oc\cRZ>.A<Bo%i6p_]M*n\^"uAQ&]RX8@4EO*"<s$^TV%+2b*g+oO_5YG<$Y<nh4Z9<ef+ZUE:GcV#
%5USUEWkXRFRTG(j*fJ"D"\5K,qEV"Kl/j+g3+\M6g%`)YEdGaB9U/W$XESBP'%1GTE?o(YKk]!3@:2E9LfI/2#&gUaP`W_M0Uc'u
%BI5T!3`VH9LglYILU%lkbJJe/N!"&R'p<)f(f^5ID3]a]cFj-a).k7lAD*?CqkRbgRT;j*HS+VH'[1\FOW0W4St6<^C.Z$>6HC'>
%"3u#b+R'rY'';7\hBi7p53Q80*??,F)JCIXP<9f&52\a:*/.&!VIR)sEbmRCc^h![VNNZ3/-GXfB_l1W.MW\r$OKR=]8TEo%;@q,
%:ot(J3A<:kW[W4EOnYl!.U1V*.6nnE&EW1IV8u$j\J?ld!C9?99`lOSF@=40r2.:9P:H1#3Bj6>J7F4c'Ze8)%6MX]_UM77s4SHC
%E!k(9+M/^h=X@:(6a7X]26d:H3@^Q<d?fX>OD/7PSZ-r+8@0>X2??_34!Zj/&7eJVOS\"RHeYnrbHDkKW0c"/&?F%`5"<&M8_dUS
%@4h)J8ImG1O(41oPfg[Z:`V8VA6;?-ESbI3V/!Ie<FJ.o*E#oLZ$<X*7'9P;',G_N#K*EFU.//,5>n6'XHkU/cSMO!0Y"\a3TF4n
%UnqTB7.`hd%OnSpe.VEuggt0P;*EmZg)QS"aKp4?]d4!K8[OM5#?5JS7]M2WnbHRcQ;Audebe-D-?NmaMkU"C1FaJd9(6uR/4,!R
%oOS9.l=ctM_=S=f;'u/HMT*hHDW4?2J"R.VG&jr)YITC]-tM`+DU"E;EcJO7;mb9;ii<l2+]UediY%J-E^T=QORFGMZLEu+UOm).
%T!,r,O]g5.<_q50]Ko#PNR.!HXpSgG8l8A-Ki9')CMIuM,c_#DUW>hUCMMiT/IGcET`c@>6mOo-/Nj@(:ocNCA&K(e-/"_D5sqku
%@'W?P]t"9-dhj\mfi4@GNE.kuY4Pdn!3SM9CO-/t_#cHqijK3=[M_e$&S:3rAM\F'RuEEGPX`A\L^+eoP(6+#Fes<dY*LjU)5(9'
%6-6[qeaKKQ1.+llNnbg!k*J*_Lgm(T.P%R&*R`-@PAa[@VZ#u\I$Dk:a6em?c`\sn?_$J#^-9-^n'3R^p.IoI4,\Z8E?7<)@-78e
%L-fP0#*QoKNW2-]QTi)F5J#KqG("\Nqrm8?S"&6sDbQ5=Yg;33q_S'(gK3t9h>@,;jrlHL-:^QQ@C"XM6?&OIl:obO*u]`\I;$At
%Id3Q>0TR]'V@huc+nV.'Cmb5N696<?bjnmT#Hd&+s3Z&XJ%iIWqZ#e5Wlj*gBV"s""b&LJT-gD-k**taK>etUr@:JW"d4R3U,0@_
%cO3P:X?kh_m]+H?heFCP-ET3"N^N9UPKQa-D2L"a`LTCIB@3YL;GZIX5G8IQm]K8t[=MZ!f#HQdkl#aHq5LPln=m<&J+"pOP@r9k
%?KgBBj$_2(ZS&N5"nnfs(OO&n\)YTlVlhXT,f`L5SYO8fU2h78$l0:F&Vm,CCQiLMkbM3oA+)DHIQk=?eCA0FV+DjB-RYY@T]D.b
%/7)cu:NDT/ZSoAEB'e]"IE/&UO)FQgYK_H%)^B[G8QKd*,70PJ*5c=!^9]_oeY_t[\A%"MqocMFeCA/scm13(W3G4J%BYqL)2]mq
%#t`d?[2E.bE^,?F3tAqgQd)G22Y0f)nF$+.(EJ\6!I>pPEX5ljpT3hgeL'sq\A%"Mqbu3rl=r*_0bOVAbdk$e#bQF%<el=8Qs)(N
%5C72!LY`ufk#8;acNBl`FkC0c"&s<3CE:?#-=J5s;>tM.V^!aRM)k<(LgmeUbN^oMUpUoQIe,"DqXRF3q?cZk@RJfHq,]5I=uZ*L
%I9D>hB>oNQ%5p39E^bcjX#MT</og.m!KPeB_3[qZTR_"H!LW7$%m5Cl:%2$g#R1a^O:!Tem.U_8heOjQe\bc;@%e>[M\+uNX&a`i
%XC0mm!><3hIu-"sJd/)g^I'=\JRWJ@R^@<9nCF3YB'g8%0h);(Jq!%nW8Q2">_c7I`RZ<:JJr]o"jaac>3K"63+7&b/#N.s1'.GT
%LB)E[]EAXZK9q@tH'h0^n@Ih"6l6r'k5*Sm5k>b1V)]AC!N,+a$ll[e@;B^+i22Q!:NcRPPQamS'#:JZo.gWK@P*/l+9IVGSt509
%W#!@)!\eebr]ZMsEW%H:iUL3d*K0Y*'[3LU[*4/Se"l1ZI29YnbIl>`fc?H*ql_;&]]Aq#/C&/62.NUbJU('<%odVo)!g!]nq*-7
%Do9hfTSqYf@B`f6X<f7s?iP(SoFF5p=6JWR$e<4.Zs;(qPI%5B+p<\oMKLCZ:@_)jCmtdZCDE(9Lsp@j@\]C@0l`,&H0hCp5lqYV
%,U11n+.]i"\[KjaqiQ7"ODaAe53ttr!ri:*^Xg#V9Gtd9eii<^#S8IGhpQ^#0sP/o!\,6\D`((&+EEia%?_0s/?[P.?`_^^no\G5
%>(UZ3D'=[0b2fXArA`J$R!)&UVc^3i6<=@NAM"[6SS_4Z4*/QO;!T*RU/(>WQF%6<7cCI[@%5T9'6<V^[\7eqp.a-%a)s=O96-$6
%0\Vt'SoXogj!pB<N"iZ&_i+=*NL"\rMg_+CX[9&B`RM;H##cL:lKU=nE"eb9'jLDZ@,7iAC%Z;:WL.'TB&J<B#H@#)'mUF:E"@)H
%*bqtFc*e?"NiNr7JLi1*,X%tXfUY@g$$1Yg0A:nY,JAag$TO\*<5fl*16+(Q\bn9Kq'm\tT2r'q,bZYkSa'UHD\AKKTBIq^$g"JL
%n9>,nNp[Jf"Tk\=a#B;h!Q3FU[KW7_Ju?2Z+X!.[%TWXC=Tc:,.S_!jlc*?"<?^uMkNejZbrFOeDIZa,bq+m0e3-W6*Mp`m[))=_
%m@at!<@!Bn%05U.;cITri]+umAKY^h5#aP$8^%>_^GJ`@dXuBE<_@5J>M&AkO;oX2I%fN\+:9KcMJG<EA>$,X.mrV&G,MpiUd0YG
%c<iWG"6?.,?p/*`Cb\R$m`8N3d#V?6N=c.-%28K5<WL@'^uOYEYZ[Z.WY7d+@23VMFR%42s&0mr7F:FOoe)D;Z!r1K`CBNj<DO=#
%>38TGVDrJ/A_2$KZ1WaB,!b2<G332-.giYl1Gb<iEZf4)08?"")&6sn_]=([DcDgU9%/&d:D8'u:88"p@d)#a14ZT&!)041i7XtF
%/g'JJ)M4!?o%7AD"">E[BBLpq5T<fuJEQCtiSZYK#&TgY+EfUJC"h(&Nj`b*Z$+O5><AMbn_(@gAY6e"]]nJ#%HA!@@p'7KVE%D,
%3jgJmX^ei%HZGTCHFi9WI9T1&,o@DaJVr&^SCMShR.$Hm"e2on!n205AW<Ni/@fN.>HM'TqVA7-gb"8`OL>kXgDK+ahG5DZ_\?qU
%W?U8bCP1qYX('8tBlL?)RF2pQRq,PEgj2JU#_Z[IDQC2QMT\;pds/E<ZP&(3B+>ULXj7DqUiaKtS5]iR"0#mGkYJ@_'E[T10%6E)
%3"jsGF_Bk=m*=8fDJ*ujI\+O;SXD3IF4+\Gb4!<$XA&B%r/d2$A1`JAoskc'`8Pe,RmL)fd`Xb@X;)ZT8V3N"=VIYf2%N?b.pMqg
%VKZ&rh1r[/SYn_j"3KjZem_b",Tb"BFP'77@I-G6.[K(X0>3IYr%pY6.OF>O<ioH#l\)#O*![`]U'f<umM`5NKB*1-W@>We*=0/7
%Y8PZH^n,_Z(T%6GYT1Fo;Jj>1;m7lS.?Kiq^fA!F[on+kM5+H2#*Q5#^#3[tQ-_10ck\OP/XN\d?:!$%p92YIYME/PK?feMaJP$_
%>MOj[Yn_A*W<LdLCS,87X),dGl2$a!M0q-k2=-_^n-rV/&I+kE@&0<.q_G!$n\>i6e:tkZkQ%al4;?CaN<6\AFnaqd/^R>jc'Esr
%i)>mAFC,3"<A%KpENFAoWHIJt/STQ[4oHqZ:I+3S<^<kji8SW9Zdi-pVKlk6pbqVdgM+H=kJ7&f&0ABo/..Gsi<!*l%=%&LQ2>9L
%#XrT;4UAj4TKQ-_Xq"]Y>DK[O%b-4b7ft67"%*<3ZfNU9R(&RpLl,nSG8-<p;pCeq']qlKs2R1"]#Uj,@>g1l=Oj6_n62BHqV&*A
%QfnUem#/e59ZpaHJBQ,<_qWa57n/MD<F'<"V9k"oZF](B:@o!rZU_0#5&R91<KsS^TW!agF9ndUBN[6M;-1/%I0aFsIt<8+'q6"@
%\);^E0#0oZFa363>&#u3XTlt"CXoOs9U_*o>YrS-e\(7\AiVLb[HPG*l$*3sGM)SVh$Zj(:rIBF`d(bSjVmH!8J_nQOV,7)+pU*,
%N,i"fV'fVr0r9aOjKD@m@']]03ON1Qo^pV4oucog"ceT!a9^,Xi=),\?o&L0c/B-deXL;>nk4M(TfA84T6N6!WFr8>6T(B:33c[i
%MDH]c2\3N(h0PVjZ\VV^D5&99"104Nfq!j4M8#IZP+_t>HZ>+Eg^<P.`S(Qi,\3upB/HdB42K0TS=(CU`(]R4hgn_`M]H\)j(u9Y
%"Df7l=G3'XD!K;Q]`EE1N5f#2*WWWQNLCpuliqL!2@gTCL2&NiVDe-c@5FMF.Hgb5S$@L-f!j@lZfSaeDIG?H`_[t0T'Al32-E2N
%)/&',`e^JJXtio1!;U6?j\BrCWZg"SROY6^(rOYaiKsh=+D*MIPE7A[hJ?G3OM,GL<H212>&6&8=&;5J3jWge=0jW=-"]WX@VI1F
%,+Kb*Kp3tD:hDR;220'"%)IY_Ta!DlaL$CSP>d'@1P_/S&n"fI[8C^?NQ.A`&mDD>k5qJp!B[&r'e#nqY&48ddh4>k,9<NV!9RT5
%jb:]Kc"=&as6N0Uf,:?7/(gu'12\.6Xb%6K2-!#K^?oaVfk(M)2V;h><.G8l%q(sQ\AtYp3&;29@'1hY^&ML\`l7("fu'tMUL`!L
%bcP-ZgbES'p,:r)@L_7u>qV(IBPKHcbfD)^Te$sje5gZLA&_o.q9I4"79KpHfG5e37[=RK'b&'M`itiN`Mt#507'iD2PY7h1/e4S
%dC9F.UN+8X5Gk!:>'.BU7cQh/<]>r9CIBY\Zd]A@&W-ZI6ZBe!/Zm#+?&SQ7ZVp;pD!0#%e5Noa)JAZr^aVIsFd[!LZ!OY9!^j:4
%MgV6CA[#=PZu)glbe'-We#T;)HJ>;/Yde"C#3>m%Kk0T7m*NVI!JY-:KkUZ7PFaX.98MZ]C\V&O6ReKkl>^0br^`2$X]h28OYQ+S
%%W;O#j0K*k4k;%sHe?iS;-Ce](A[h++kd5]p'pV:l;Rq,1aX3o1s+.Jo`itK<+B?]lPWE24"\t9RY'Q"Y!+1e/!sg040mCinY#3c
%a4TU[':I]@P"pg@.9imC-9Y[pRfZ?n3t6=c8`'fXZUPdU5&`;`7$X%B]hPm7au9O5RG,*dhRj/XTk,+@&h-PgH.1A_>adT;@n"&`
%2iSMflKt&M>hV+u]W*lBe#%7;:d8,*/G5jR*XJ<bds(\,]c\iiP(C#b-b@X*VG>;MlP?q?7*.\#,C5+JAo>E`D$E`iN@,a/C,/JR
%7+</8PB%NBpLEf$8D1fDkK&m+($tk8XI$MRZa7'e'l0egVM0j_28QGrYI`&SA]7i=#A[I\i_0d@3m_`><82RPl)5-N9b_AQW7&C+
%?r4'7)kDgAC",)>;=cKE;ne\[(Z8b?2S>O#l@lX9o$#7g>p!Vdl]k]IlcWk;c;L^>Rs>+jXPK1AHKn4ph/[<ic4I;3@:A37]kAk"
%QkgSlcGLQ)g,L$V-"?NNmJ"as,*E%+]lF;N.p$C)rHdR;-Ka*b>]nBI9V&9'EU1LeG6dgSB5%5&ABNsJ=<:m+)Web[QSh[I59&no
%onV`4>[$Y9hGhJ.T"(>NPJigR,<qCg@OWCdlF*$(1.QL4N78$B&k8$rSm)mQr\C>pkl&(()S[#OQo04:<Im,U/Un5'l9lDj<\*.3
%e(qj1=X7(""#j*pe8(.c9(PhJ-Qtg>\Ten$H_$^#]lL2(^dt.=3d'*GP<AXjF`p!O>-CJ0=).HV@3K!I&5PpbWHU1d=Q?YF@Lj].
%Z%TEgRC`^GCdrBK_12W@GdF.U%WjW2[ODZ7LB_o\<[P?s=(_?j`ipm;6B0=C24p>Rkdm+]NS3:DOLd92.p/9G(NB[4<d]\3m=B/!
%!:)fM2`?5Q>E6!q#i>9Q7'9_pgJ16,R0n*UL![Z<Esd!?%ND%8:_)HHR;.M2.oJ4dcF1,ND\Hf,%SZ`rrlc4=qQi7g)R_b`.UM4P
%B$DV[ZiP>=)mDF1NRr=::X-tBpM&j;f1<an"5?KqX-)A3o*gF.QA(9n7TmH#o5((;$Y(YHY6AhS<D&1((0B+g#-M3n)I<m08kgF1
%1O*[B&<'D'nHR2/H8'2Y01L@td(Fp(BgWqoc-0&5`bA\'_^P]5r;=usE#NSn<=-9"FsQAecm72:*piqh$+l;YC[=sX!ZXfdd$Zd]
%F"8,19COs[mDG\NNNUp,XW&#g[YAh).h'Cm2Q^*1TM]M'cnr&%Ui!k<qJ:AKT/AFg+l&@UdOh98d,@_oj]i"Z)nd_[f,II1Q*Euk
%d&W,=!5h,b>k'kA$i+G$ZX2o.a6aKgb1En#:W8\rqit6A_LAn?$Y_IWbI;RDU+<$DOmR:!%,PeP`KDBQ+0&L1,pZ!EX7j5XbPpbq
%@REPYq-qMu7V]Ng?A5t/[)'65C[/gfaP5[d>e]=)p#L/aJ/;^:Hk^'@h;Vr2NuP2k9ikU20r^[7%fM53<-EHEg#:S<W&mEa@!CZa
%E''\M3kR"=E_RLW3VJ@CH1`pZZ2A1H1f7uhpSCF/f'$!W.2r/m.YK^hK`kArUq[M.ME2"qA?58\q'qL`6]Fb/;"q%:]FPN$=afMs
%6Ii@3@^VRuM6+P:M&2TRFYc[cA6KpQ;`^r!7mH_afL!d4n7jZD9/Z_,72=`ohNVQY^.;+3/[I%:=-7d[+7%`GBBUH*DGVs'$;i#!
%jm1W0?.M^Vb5-i<FV8";\Hc2U":_Qn(p2:?DPNf.b8:a7\K;T,"E#]YEArXGjYij%V=d%#_9c=dGd?F]l/&T4R^7Ep1MNiI"MM:$
%Sk:jW>HXNZX/f1+7!lF4!c"#-=Xm,WmD.L=p3$.4_ZP9]ou>JKn4`5U\Q*<PNTClX-M6EFO=ai'C4#3VD0^ma,PhG2S>:Sd'$))q
%OES+Z.O:"S]0-ee/9dXQ_+&dG3a-Z6f51o.9he*E01NigCtD%@<5ZEeB2eg^Ajko0:4V0Ch+juGI$iBulip0=]UWc.3k_,@9,peV
%X.%B8^8`tENZVqBY]Hl2N)9GVl*_(,oPoYqXhr$"Oj(jHKmIXUKr.(f@5B?9bIZqj]H[ZY-I$5i3>b?VGQ9,S?XIt%p%5O*Ah@MX
%?$@P&Xu9(XMe4,8bfg7Cp"BIc#>d"[N^[<-Ibaebej5=^=/2At23[-:@eQA!S5U"l3Gb2EP]6\1m]t#g]D$^e(-O#P+X]8g7>iK^
%g&qpGBBlje#"S5-5KnWOC,F=)218KR8`A.No8ft:9s9d,jX4*p't_0dVtL/C2o'4\2h5\q2h2-<hghn8hghh7h^Z-VI9Sr&C3<U\
%Dm`b!f/L-Hjna^24_rY.J-T?:gfQJ;Dh>)shZO<cmkmSd2`Go-.H\n;`j0`8"*hWS%J\ZM%H,D%%G3GQO)42nft+lu>e@^ME96r)
%HFVT]Cg>lE=qSkWY&D,-Y&=<lY&=:N\A"Z_f<0#lf<0$/Ylh.I^t5`-bh9CEIB2;Kokj@U?/PFgQ0TA=G/3d1rNgNmF(<n7^*/]k
%!S97W3a920+]$Y2p!&KlT^/**)qR$!CY-$&/m[?Mc^O/=^pWJI^ocoAi3hkiX0ltr"*hWSc'eR\VBb8;hR^E%gq(3#?G!qF(Y>g.
%'jS;`XWFA*RkmdK/RRhu"0QRDfuiss?p&r[E.(+:XBj]`Y$KpE%U7ODNo\@U%6;.W(/-PsmD_MrDb*j9G'sJ&G(!jmZ`WO4n?qSO
%)q3C2Xb&(T\(/Bb&9H'"f=,leR-P](_ohYSVJ]UjLKr`H5;$7!Ygh:-DqlnPY&?:teuiokf<0$,>e.R#*]DV/J8<aq*i`Gr2kK"Z
%f/FbD/(_6[0H>+]UMu!k5+n/q>X<99Dl].+m64O",#`KVQ,/0`Cp??2R8F98etm8l11NMLPnAD=Q,*T1CLmoZCLmo]CLrfcW)dO+
%3n;AhlDFEA^*-M(dPn]A%r,[e^?eMKBOt3H<4+L[BND7ZEni7uA3QrW1&,PeA,<=718]_0CLssL:o/f(<b<lfZneNM>\dmRhe6T^
%/Db&OTOL'WPnAD=Po7aGXc.dcZnd_ef30%Ci3cV*^uJ.u*Rr5JEBQk9!NccFZ.@'jap5:/@l^3I5u4Si195:si#Adl58mNPZPpET
%Q0H?"C1[5Z;7/.`0"!I88S)'Cfg?9aAN#UM<b@7#e$,g.a_'Xb<bBfGC1W^%To,.I[rpUPXUGDEXd-`LXgKp-4^;c6QBS&NCo`iS
%9MO'[XQEnj4DaN'Xq=m?9OE^X'1%nm=rBDnM7(OK<\(NA[0=:qb4K2kfAN<&VB=m!Q0G26lpQhc>+*X*;XKCu(CoAYZn_h=Zn`*t
%ro!Su<sL*P'-^]g1(-277hC`Y]_E^>R6A6,B'br,R]6I^9$]Z/qD>oaK!_.PX]OZGAGJaD69?DjR&S,);pWON4"-Dpm9<Mq#hWj(
%MXB7fWnNkMl.;"LV-;S)JVeu\!''MO*`gUY+][o'YmZ@fX%C!\_IXBI]=;IT6Ij@4^eQu?j!hG=P%(]i!U37=::o!#e6B6:VO\=G
%>Afh5/"qPnhhrLmg6nC<9#,bn->=I(L$+,(Ee`)N1GuLsJB^WRX^/V\7`ICJc*L(VbksBE'Fj>k2Pc%MVR.='<B6f]2hIkePuqYM
%Zr#$^NVPi+V`KoSFWXL[c4jk1k(%TsRA+4#^rN-p-.L>AqNerQ?5TUeW4d0K0PTJ?3^("X4ftPeSSi%9p/(ICl7`ER!^Z"<DPhu)
%Tpt<:WgaljLdOG>%1en$6rFA7\s\-6G(<ZMN-_`?1k!Q?+[$QVXr5_Up(%u1"9WF#-Ts'bluY&C3)Huj<B4!B\!H]E2AZS<aV5Q6
%R:;LMBq24cJ0p!b0XT"Wo*ckMdb(V1ZK2Cqg)4lab1bjTE04]eNZHcSW@;a,<[S&"f!I6mTeOuikaZ1*MSuB^&Kc]m)][l=XAdF:
%GZE$[Tf*CXRjjY7*95E3,a.;qMG6$.*p]$(gR9SI$eQn?MId9`<6M/+.@/7T^nrJi&i[Wn=ffZ<%B+T@U3pC4o;\fo/2_SSlXULT
%bq:)5WT^^iHC;TKR]AW6UolVnWa9:FN^eP!0@PQe?Dc\\l=DMJ'UH;oAHiLRHB,M<&Xld?851`nQoHXiWET.8WRo<aU<%=Z4Y4<M
%UMe#HSM4StjDjIr"f86H+Z6r,d=;ZISZ-K&$W1?5QcV,@G$MROX/!q$<\Dira!UJd_.5]>Y>jU2jqPXcZ_sQbEELSiP,=adWqV&n
%V]KYu/o\-t]\Q..FY=+O]P.c^\lFpgZYg.[W`2E^7^q*3cAmaW!uFf7*SqW795[6sAaZFAH<;u+QI-1L9dRoK"$<5<b#O2V<3UM+
%c0$D5l2^ArY,Z%E;r-CK\E\*i"T`8@D7e9Y'USXuWEmijU+bkt7&g\5HHObJ>A=EnhGLH<dBLX#M@@*"(R1u>f)f,hPKCoS_Gl_:
%f'JAKQi>qr*61h:c>2,Y#'07FXMKCf>OOGo?pW_qCRQu:CiT-:DW&rY]#1T#[,.j=fR$=K,)??NHVOB`.!/eEY1n>n-AgTl6X4?f
%aB#[=4-M8pV:.8H80JKqS>HD%3emn-C7&i6jCU;`'%)kd<RZd7$]7noCfdjp'9eo3Yd;)H-hRu\2Z-jcRf%G51Tr^QY,ed1XG6uP
%W[b0+2>-6:V:0"jOb@Q,bn?9S%`1pQCI;d@rdoFGG,mLn.b$L?NLEk^c?VgnD8=-OCj7n&e_79uo8I)4/s*r_Q-7]Wg&c!S:2LWE
%`Cil!B'gAWM;+@oqW6@7VDW5PC0rEUS%+egWmCSSH^Et$/mY>9]ZS0mf^&:T_X+7kbuO>a#2@ClbI!=M]lk]D?nk;h$'(nGea+`N
%bC+M^Pj[=5iI`0,S^Xt?hB!_Q9f0KWlGtKA1:0+agE<?LBt=D5?GR(h:=DD=oiYTL'lOS2WL>bsTK,_.B#a6"#r+M"mE&i)l5EsW
%YED%V;2?p`HBVaJYVGJ<<JXf!<f8;0l*YCPKeZrZ\p$%VhUZ=#FuPQ/3Z$&+Tq>+X3<p?V:Mr%4OtEbVRV0($drO9LRFd[u*O`2t
%28$9@[8K*P>cU20!UK&Uc"59(2>THY.LH*#+/u-MeOEH=GtU`GWsBSil^TVO4>![VIN7sZ+c.(qQVHnaV2Wp_<M!s%G(Q'a;Vj`2
%o;N(89qilOa@c)God!a^'51q!C*2^1V0u,+o=.sU'[tpi`!Y*p4S;/TN=Qj_pO[u?fqiOo"L4^boi@0OC[A,F*Or>*FSJre#=tGt
%\h#,205b\DbkeQTL*1d1Ec4LkLUk+k[!l4_4/T#2>iEL5G+BM(PgkVN-e>p.Ep&QQ\^PlfWV-O<7C@bii5,uPiGGQTfrdl:+_7[0
%bJF`E:A$MUiuH=%Tq;p+S59*jC5Y#f-^Be.E,,+?\[\5G("Wlb>C8-tH<1^@@9Z_lX+"sB^7=Isg\QL>MH7FAJVu/Kn?tVt%XO)h
%=Gq#4n'40GU2AQ.M:T%Op5im-4Z,Z.jpk-\%7K"0:#"(<Y(q/#KCdW8d$a&bIoIAbqN#$u)?$pIJ6ed!E9lGbe8?O8%@4n4-WM,E
%:(1lY!P.8MPHsYEjb.AhGAj#;n`:h(4LZ-H\Idn7NSKX^"E:%g-j>HDLFFA)V2qrrJNku>OkBNYdPP$OC@c'#_P^Y#8#*D<D3=B]
%RFbeDoj4%2[;Pi[0BB-L<X?UTq_84K_\GS6%tO',`XPMD`P'MpGuT0@Dt>+*!ncpX*Oo&u<4pgJN*2)+mVTdCj-KEt<1S8(n<,.L
%[L6+0.<f0`GcBUX]F.a6GuMd$oB'm.^5f5d;=r(-!PF-AO+UHAL#uHQff9LZ*SJU%\srmjg[Q/VpRAE--L7-b:7[%O#.D8)-#k_`
%MRE4%**,g1<ltWELOnDfBbD-dB\QhDFE[_Oh!C7Ob*&6brO/M6YLp],Wki1Ool)#uflDlN)9KH22Rpr)U%9T^Y$a<4_Wi1TP;96-
%EAiFcL"KW@rB8FA7T`sWC__[',%p5Dj83/590]\9G>;_V#GT8>qcTS+#K,$fWWhsY/B9M"m!1VV.Y\kooOQf^\"#KrY="n\k/,2d
%n`op\1!Npm?MPOfMQs64.Jh(?Y*tQ5JcT`Wh4#Dg1NZTU3H.sOThC0H"W.-piJft=\N2Hm1L3[sFtK76LEWiJN7^/UK^"F;Ib[[o
%951r.Sr"U!lS=](Te&dfkp!B93HB2G^9#L/WIWAJ`u\DjOo0N?f#nVN4LC$5&)4'tR6=V]R;:u/UTW!1X/VN2.t'32Pp^(5RW'B_
%[]ESNa^A_5Tf`!.<+F667br1`%V+39TWZj?PqY#Hq]r%F%+pN8?YRh8]_CT?FJUq#h:%RgU;;G#OBD[2-<54UVRYXMFT>Vm];p1K
%lu"YRUJH&gCn'ac2Kdb?B6mFXU](6h(=':M!ouBuPn*7F:PVX^C:D+8;PhS>i>ob6:L>p1&aC9aTEZl3`,V%nbh9eB7=Ko,h[Hnn
%ID3=0@T+fc='K;]!u>S)ieg_eQPoqbJt;76J=WH"/#Jp(jhGJL#KT@LU_[1MYpQYO^J7Je3,Q-K8Qap"U$[8VeTbe[IGFdHQZaI-
%ln&!\n^jYV%'n]7jioH,.KC-T(Z0:q'u0NiQt1SLpIIa>1&8pF%n640O;.T4L8h?a5BV+2!a8k6As"`qnb&V/G[t(r'YC<%,)a%B
%?2qZ67saRDH%b<1d`spt,uOl*G#.*FRI7W69gUBc28R?uO7eO/ifJTs51o;Tr+rZ'51r^I^XhFUjI`!B4*TS:jfbfC:N/O^ieG/j
%)"_faUWLr"SuZikANZp9>;tJ=l+PR8-bcS9:_0JG'#]jbYiW(a\TS'=:!G\nr`[!.7fHP-&,Kmr;-Wu[pYG=:elg?=oIi#\(#8Ul
%qcWVsgX71PgU-3UofHN^\()o%IMCpWppJ^?L5ET"l5n#B2QPtmb4TgrJ8*NqjSW`UkN':$]&g-kH.8b^WQqOCf.u[!Cn6dgS,'2[
%Ac9$e>N_e'"K`ijhQYE5r>M=)6RDU.f5WMYk0K`n8(J.&Wg`g1,RmuVHK_tlT55kH^Qt9s(%q9Bki\)YXS]DT7D%)'1ZP>#<dtpl
%lppJEXa]GkosPbBhiGWd\&)sE009K'LteGo1(WjkpV-KDH(+G&c*M+]&jN;n=$VMbW@B]KnLjdM4C&b!Kk3i(]D>HLeQ>AmMY-dn
%Q!;QR'(Y=m]]hI"@<5VpkU`0t+.&m;s4$+r-1UT$l[[&%/;qsj]s)b5d(cX3W'-DI-09:$]kj,(QPoNBB&(RroDUhqhObu"*pfLd
%,8Z..aMn.!]T<R=Kc8r/;F$`%fXhr@^J2?>WVObFPf1nIq\>6%,8IRgXmbQ;IUAlDP`7sV+fsk@C'g2ch=dMT0m8V_]m9:<?^DNE
%Z`O,YAtg'kc.@,V0:j(_,n91C,@[uq;9H+jf?jOIXQQY7\gtn+]C'7RSA3:X+#2e^^7>&W@J9[7=nZ8'*#c@IX@I05g]%1Rs6rfk
%]_2!kmCT::o"Lmbms"dUQktWOd:\6FCaK9Zo3oidI-]8[dFh#[\(@P8ag8T<hYt=2:QB=2pX^51e$IN`rR9f%ZdXi9hS26;q7$=s
%DsmTBAa\FOi8SqMoR#J$?!cE;pANNJT?5i#qYo]MS4e4lo($U"gr7[IZi0dI:J^[CqUi"5?bcGmC%oW:]0H76r\-S)^3K,+jQZF%
%cS!NDAGQ/Bmk-7W\N[^:Sa67<OlGiRn%S##q:._-mL%j2r9fcTkOZGQIeI.Gj4dr>_]*Nd>X$p2hRS)aoC_OHUPknNp=Hp;Y>=lD
%DnbGY*P[#CFEVj(eb.>?f;H[ck\"rehqEe?ea;DQa57r_Ir?EWF8&5Jk^S]_rP=%T=iPC+Nc&5!K8:UsHMRKuAGIe"3:MjbY$Z";
%e[G8Nn=Rr)IX_3;pGL-uL[=3q4TG1cSgn)#If&3lUU+l^O*4<[Y>:e8e&S%l)2E_5gU#)hUU/e*g\nZGY#B<80[%7-hVB8\as?t0
%ScY66l>o<7lVs=Nhf9q>a@Z35-h<tNb@Fn&R:,W4h#763iokc>ZAWYgm!i14[C:>3aZMJ.5kd5;a&K)YQ$&)]i;B'MEij@[QQKD9
%Qef[ekD_B51M<dl^YAeAI^nufXandkr4LWOb7Tb!3?n_&>;uJ?`Jc-eCS+=m&c$q>p^]R!T/r&$WBo9Vq>MN:/%ZtTGPbJ8m^R.^
%SH!4rn,E&R?I#./;h4>^\q2=-s/UWjlZ.9Wa'0XN]Sl,ts,(,HXS[C2Z2SKd^-96s!S53V6<[>7]W0LCn[j'nI<tehfm^X_]_-1Z
%Q[c`b-]neopu(N*q>'9odHuDWn,'Gtb<O=NjBKBgG?>HchKo'$[]92r>>HltYG=[<H[7Sg9IO:43DM5NI"_!Y4<F-_Im3e:Vmf1:
%hn7l[mi:/R*qukL]Jt1cH@+98ZXC:il]\+0gUl><OK,J@H2g]NcffiW7fOU&PK+^dSR0RSaq:0dhHU#onTjcco9e=r0h;1Tr]AC)
%/WP'I`6'c`cLZPar)nM*$h+"D#Nd\^r:c<8=+,7<H\OH^eF)\`l_h5DQeuaYqjYDaesEk<b+Wo4T;hiAG8K$<G&LdM>jQAWFutoI
%0>9L%^Uq87?_#o!I'NY8e,:.Z\'H\s?+/2<4e8($%e&;+TD[[3q@m!3HoEXl"SUFDo.p)_s5dMAIQ-#pBQ2hnf?is;]/R5(dIVcH
%Dp=h/Y8XD%c.1Lpp\`*6Zeoj.m*o+VPqq:up+AuXdsOG&K(r/pc^[Q-VrEFc+^lK5Bnb,Cr:P8`H<Y\\&*ViA^6_XqBE%53G"s&B
%Fs$,Nj$2AfjB(s)T?8I+rpP[PgtW(JhT`D)NNQ=**jpH'G,c!K*RhnNBKt@$]:'>T_LK8:N7okMhg-<VF#Fj1K!fgk`-_q=["4<`
%L]`(heT5?MK3cg2;XU^cr@QE^FDk*,n?TZASkVDl*/XboWsAnAmk'?EM=W8l_RetW*O\Wp8Vnrrn!2,'_diXC\(ZnXr:4HL/#)La
%g[185b'3UIWBp7!1@L@2B-uG)Yr0[Q3@NgO)m=(\o%u_kh"gD)E,Q\3+Xj0$m*>PVHiMG>><h:`Vs!9_7o#55H,D;M]X`GhB+<GN
%IpLi9rICa;b7XDGNi;GkGspnskB69\'B^[333pNGLAP%3EF.(B.]_j4[,798p@gNCpV,HjI!ftO#q#ncl1XXQPNuVs^bRT>X`pAY
%dX/m=3V1ra3Si"[+r&W!dA3,HWm#_N/,Aj"mjpN/<on6]0ns&t`Cl^;[X5^Zp#.nDh4*7"jsUZ;m,pogE*p7WEV3s/g':.#^M,a/
%=0r;3't\#mircVoq%+1u[)XSg/\!CXf]'=fb2!,fjWVlI7UioX7*#;2hYA[/h-T&,R"+_aGPgF`;Ajq?,.;g$%n-Xd$Z'H"GQ$dh
%X7Q2RgbsYtr;)P"`1NjKq\q-DT@,5SH.DHEnCdK^`Qaa9fB:n8>lEsEX)R9?\p?<QIB=(,G'A"RAIeZ]q;I[qg7pUI7q_]0:G:4&
%0,e^Ck4-^2IdtPZqKUlVnSQck^t%\g-^U_<efS.(hB(M@^Nau&eb.dV>8?kdUZ:+PC\ruH\E9Obe$?4LbAZM([ti-%_kNZ$NW65]
%Z$AH:4?n=9H$-^%=&48=dDa5hq<;8[#@$c`I(IR4[]S1(C;nu.(&u!9Df*_K13iE"eA2#&[W1(6G>Bip0D=!Vo&d"-=0S#5?CQjs
%DqCmh<oE([H2W!fh>U_lQ*+"^/YubqQ('BUjk]KY+[1:!qDK)O3V`NdC%KNYoujo=MmU"JAtFI"FE73??_kW4okJg_eKYGRm9enN
%GK3SSme,\lc6i@J4ifrO[!mhpGj*0F'?SRL=uY:XT3X+'^#PUJ@fXJCd:[+<=&S53dHS'\[!_#jrUoOjq>0[Y08kFSSR$2PjEa"^
%**n%]R85T%R>'kikkK`+nUJI$q6SP>S*/dSG9tN0AW-P!Mgh):4!DKSea;B6P(sfknrlSP[#i)t0'_UFl9"-$Ru/0b7[s3$:Z">r
%\sbj3EJ5G0MbbsC%p4AFl$G/R1FKeqStFi1E\i2-^@U)@%D[VQ4C#4EB@e#R#>lcEJ0"Z\;u>_EVb]MO@*IUGH\,W(dm).XpHQg8
%?[i!j?_3ee-GCWFh])Rac0`.@4?`P]s7,maqnJt#k0<JjLg+]C*EA3!G5n_7o2ddA7V?nb:TjHL^3dQn)d].l?`Z`kLdaXk*^h#4
%\XYh]?(A.i3CumsP[`1f73hp0^L*;^93lRVr;A4Xl'E^jnkF>#&'fYgb]<_;:W[%$T+<s,s.01]V_.lKk'-D!o,krrs-8)rLU,qN
%je3<1EGa3RaVMY+f9Q4!mrP>&^W&nEX8At.q:.@g_&'!KR.6qsG5l3)>C(UAIWa4Z96Vj655hJTo/iMj%X*K6A,;C7s1H%[=e?42
%p73u-2+9Hu>ipNHh\irm?rNNo4-\@0nHeQ-n)C,Nm<EW0a<sJjqqKjgg\/aqQP/;kro.#ZnB!Dm(IO<l9E%,9c7,HG\\KH(p]>)R
%i<5HJ\T'eW,6%)G%(eHir+>H1O+&nbq=!mXJ,@LSoTe=T@=$j9IICdphY?-_VmlP(Xe2InRb-PHork2T[.j(=:OhDJn'D$Ho:&q<
%a"Cmuq0&'=O^?cM%4h>b+&AX^PB>cJ_\hp!A7/$X4js<h2d]b'm$d#pp7`tjdV*hGK\_8e;j5^7$B@^+Hd@Di[1$c:lT*SqS_qZi
%d$;<?Wci,s)6W/oLa/U<:2.LL\W$-<&LgT*i8jiXQ]B-<mRN+?PS^2N(Rh.0;H^YMnV+ZXbVR4`bbmS^@QpXlTD1+cUObrDo;7@&
%Jk=fT8TA7-Mt8PP?F9XMhDD^`H!Jk]])&@RQH(^Jpt8kEAe@Sd53Hs6Q6Di82&A)CMc[!oZ?M6^1'd\1%`7naG9go8*!\m[fl?#Q
%a(+p>*KCg[nVb<i6s#P:WV7_e6]T!GJ<Yj+pcK?+d/N$kDchI"YOHP#0]F8$1I'N6EmctR_K@3M2n&P&/fgBQX7,?C\g5sRV0^*B
%lh"Y(;7I>2jsRR+qu;'^1DH"<2.(dP(OYdKgF2B2pqrii5+Hi/+*$'e%c1er]#0?;F\t1>XLHc1DGLI;pI>E!HH.\\XWU(2?a@d9
%Ite7!pTIB=pD0ZT.\`)>&8q\sLJ<-Y\OD^3H?js]Z28A:`U9LrlYgZaT?kq8W$OWWo?@`&K>-TA>3hioH[5PeI"-/5mO[+EqSE6<
%?7b.nOA)Ii8)\[biMZS-k?#8+O"S.mIe;1GQ^:e"YEWs7;hUETI&V:@J,5nr[#WF,Dq<S:dEOV-`G.]K3mu2Ra*VfIFa7a5<b_Km
%lL_>9?V%;CDa2"MgjOKj1J=AfbQ$$-P&%<WD+qQE.XZCg\!X^hQe1i'&0I@!LHbY&m!5Gr#XX,`kHeSon-^>PhOglr8"R>UWOoc&
%4b%q3]_qfoMpN1Bc^1]-li"fmp[^K0a`c-:h\A$[lA5u8l-8=XXHdEB5?kY9SPO`VEI%K,jn%6;oZK;jrm'(WZnGXI?e@GG\,/T7
%k8W+!rd*E1521R2]6j#-o_.M%l.P<<j^BTXN4!2ce5))W^-0&Qg:D"TZJ]U%LTo"T.(QYCDn).4VjQ7Bm@h@=^*rdR5J(m!6`Wa%
%<c7['?H.<KmR5:-RPmg*FMUKES$2-_`mE$NhE5Q!]3,^$i[5g4^V-'@klj;OVR-$C/tX/TICd1>IWj2SXsM@pX8U/<VdFk#qhLrt
%qoc0-]=dFDI-^1clQb3mm_,_%jY-Z1leamS>OLkX.!K]7^i^[D\$k2ore#E=ND/Mtc*d;b8*<;:F+3`KZ`g!n5MLU*EO,U^rqGjZ
%:TT7[XlUr%Z#R1,[7g:?^A)[Ijm@EPh3u[D^!EG?Mhb4i7<L"iW;iLQ7l'+J+7?o"T,j;qDYI]_pN[LESR1Z,2gQP/o^h>;/:M]I
%kF8qFq!I[Z1RgUYR-QYkdJWB_Q$TD,gNbg7[7g831jA`DrRnq,h9Kr-gU=lri8#?"I:,VCB0X$k5!"n\]6Lo:i\R8UP#i`q3U^50
%hHleWAl`bGkur0c+)SX#K9'p<CsV5h/&O%HYItCse)1.EpTL3;o\o5/q2-Ac(kddT<V[Fq3M57$L8iMnX8L<t4ugS[M*T=.j6H6N
%^Y.q*Dr5Y$mSC2WA^<GpSFq+"`h?JeDrdHbSj%RI7kVu:;F!(K8+RA,3;m_Vf@Ngsnap:Vj>Q3#p;)g>N8HM*qtbE>Vn^WY&&g9T
%GM@1ADTTfck'F,]j\ML5qss=EA=d/a(;-[)#8bEncfZ>7:EP"*\>=$FCN5+OHuaN*rH=!`ZgQZ4=0osqLU*jUg%opd?QX]C>,UUs
%HpV!N!L^4i3L&nIfIjqRg@%4L/t)VkrkKT[X7PrcJ*?9Z5I@H<`/'3ImUk&kk<J`Q\]oY[s)'u.n#kDNj1"I$H*HIgHKP[<97m%,
%5I2MXkf`D]aqq]@+.V(=qNX-)eh?euS]j=^=5h1WD"r!7/,$:k<lo>;\*b<>oqQb$T3iTSDL9[ZUOg#)hd0W]+5K-=EU8#o*F0[t
%.6R)^2q,=kk?Xnp*5(q\T=t-Be!&Ois)fu8fL>p(d"&0lj&o'E7A\aa+$FsAPPL.2cED-#J$M:#]/_jE%_#XFTMDM95fN`qEYceN
%05gZeh;)H,?](#aNk_q!HB]4AYJ9G-*NS`!hag5Z=dIo%TjW:hSXj*/")mLbm!r3:4`!!#h]`eJ8T9umpS.PPKlr:;>Ct'GdShF(
%;l[T*U-=k]*aaeiik,"Yq:.SSf%cs/f^+*.N>p<@DtamuZC<L57i04$Y*]**Y3_m.q<e1X8e`n%mcnj@<8GaqeQ_BP&Vo8XR4itI
%XV7!UZ!:$>f;Uu_o\o9"b&g`FRpYdCq:Wab^n\H'f;Hrdhpr1Si[4A#.HVqiV0hY*ldN2(lht#K@:.3mDVO=F`rEW[q;U/(l^M7*
%&mt\*[p@>>MjldL]P%A?f&gT?n[7hcB;\T&pkSLkgnjTh66Z.*h:4ah1+2UQDsYGj8j9&EB:$ZrLdLA-NO22_k+qe:*T?JULkgu_
%WGGKD=r@hgh7rdDs6rbA?bUs`[NNoq4=7VGoD&.+I")&'[_Gm)7K;K38</!NC<RXA_e87AA)5?DqRP/>mqXB0I/2rt_>J`kG4HSL
%7Jtu#rM.kY,Bl'AqZi+)eSG2ERhT2]T'm^YIt&g48TB&bMn[c3qu`D\FarZU>IQ1ZnZa$&s2#!>=nEF>C7d]cZY3[Yn_Vs%B=OAQ
%p%7;1e#uM"*n8-qO+&uR"Eqh`Qd;jrp[<o%rhXIrIkMVV:O`%e8>Z`AEIWF_HFCT_l/g.Q+9,KF!-dAe70@IhY.r]JRPO8_G9>>Y
%m>q7<PR-]2]>hhO8"r+E46lu0D#&^UbKP2s/&-bTqZ@Q<mGX[+lX%6qpV'',S+MU\]I'aC'uu*9UZ2T>@fQ5LU4mO`:P\ssaBj;o
%_q*oEH:@fQcL2Pk:Oh3tp2[)2=]FYVlLsjTIDC;4'q;Dr5DsiXmr+7(#PO?31]?CWr(hCAI*e>KSlY'W5.#(4")UB.IZ12n)uoHU
%roE_IH07r4noYAQ[m.CS^N&Mq-/Q5mf_)[4Frb`k/\Ca@H$\tZ0"E$0qsiAcpT!"/V*aqQE-Bc%c!W_`U;k;N(WK&(]D5c<2S7u:
%oE+a1bF?uGE7S5K;)"P'htdFLR-uS"Qu62fO4>]KZfVtcq\^O$g@X@PcW2*tV_KinEE.GY<]`.p])(^iS&`jnnp1&mQ]HE!eFLbQ
%c_$,Nr8r9?cJ?H(^Y-pH*Hp#A2oO]G2il;_0s'g;5BX72$=*C-JUH[ln#d^]FhR>!^#QLnn'OL`4G-;orUlUlU_p3ap]$cp8gEN1
%FfrL(Z[UGZlV/>r_=>']]R"_`TY.Gif1.4cl[;jT(X8#IrKm6gM*-/Cmou\+?X>pD(-SHm32&Pu]"WH7Z+$'$%tFAcdrdgH,?icE
%ep*82TI9K!H$k&2=LOa+UT;Gm54l[p?eAB1pJt&3\ii2l4*fRf2b!\sjg/0J$\-PSDeeq54V+7XlljL;E*"]bm(\deZIdFY;b*;3
%7%\Hp"m,u1I!Hm*=1?r_GMP>>a2\uT0A(U5dV`LC`^>$oO(10%TW@D!I7"M4.e>GD%)'7@V=RW%lb.^eF*jdF?!XS'>h@<d9A>)/
%nrK\1gTaQ\HiJ=1ms=P(*40%ZY3.OM]K1=e:E=#rc;*htbUDNHo&0tc?fuXHOqWPs!ABnkf3NLg]dn=j&`.bVs5cAb:Nk'uhn=A"
%+(G1SE8@.WloBgbSfqR`095-V`lBOas+;-UI`IrJ^Sd?`8;":\2Qo1;k4o`e^->9coAr$)C%\]`*uo-NeEtsQ]<6d1Fa8>kfdi,T
%cU.TD9538]qOYI&S16?Z6h'dI47D:=puSWTbN?o"05fqoH?Sa(,gW!N?!Vr*\TR#h51_[c"o!gQhnPfdYQ/V0mml&Gjul26E7PbJ
%cbn@2mrZl.80JKACmGUMdrB9Kng\VI^;nj\2e"P7Th`5_+djZ)qjZ'</m^B9ng%Offc=.L(.`lCR@6TOZn`FjmbV"eh)md44[6Nh
%[nKKFY9"gMbR`B]QLC42\3o*00CmbCjBkmkb'E1\9<C=JO3[Il[4cpS)W>&-C#1MkZ+R-DW_jcH[<Gpdcdp^`bsU)1f<^S<r8P62
%dVZ\mOC!"s]ga!65;p:/bM05plYrjc*^)^ergS6]i;BCPNROobGiSOZT0D-1EW9jhHle,3nc.M&GBP?p,R]*oF65\0E^i'NN'Lo-
%)'$FM)%6^CfY'A]riPH$am"(_GOPM"cHPg7<d.Vfjf4VM?$E&BWHLFjJ+2?p,XT,-oWPlhQ1e:[e'#]pFM!E[^00EKV>HSTT47CF
%on=m<Vn=O#HL2FT"o<4tjl/G8?P\-?eBm;Q.4BAo/`n!k2QNI?%JJOC3kO4-l'K&4mY^`0X*<kn@0ik7p2KloT#^S\073_6h8_7^
%01fGO&+Yfa?5!+?-:8.d58@`nQW&oERE!dXTV"hI?[$"pSaNf)F88E_SaH&kIXa41h_G-#X^X<u^KQ2l!0C(=:$YTMY$k1lr\cGk
%m!#*CQbOC;X$L*Q+637(qIVPODndV\c^me#ebT6(QdtVL1-Y$K02<<V+1,c+q97X7j+7lCjKqITn(>XCNChf(&gFdnZgFuU;p<c*
%iC[R_bqnJL?Y\^%0ZuG^V9N@srP^I^(P?///h=iL$W`WqPtL;`?fuq.I:tgLXlVRQY]'&E;ce:o-!WEEGbgar9d[k_MNMt7*Mc*8
%p[3S?Q](c8^C9];r#$YBPCa^`_4MIr\bRofj3JnEWN.mb!uGi*:*.s9lK\SU4>Hce">Et[;"qj<0D;^&Z]<#,!*B/(*1fpMGKg]u
%,cF=)YTDH57=ZV,1TBp<*IkNeB-@-cJ&c'P!e'j=8P+@hfTMkK2&bC"95iu'RFJ#Kqb)C"2m`.La]^Jtj.HX2dZ%=!L&tAgq7^o\
%dRc)&oQKuW"+0gjR(./HUNd?A#h92D"dkm#^o,qS)/nSi12kee_sC$cX8hjZF(=)SA@XCdb0@a#".s0,ajBB+mQ:"#:OHIHYJ1@<
%ObKooa[5IUJMpii[;UUW2Cm50pIlKF34'K0WNP4oZnLR^cIO\TW:C@@o(=fU$5&_;X`:*Ch:j'=:#8+ap%mmlR7rBP,/M>@SA%5`
%N;+T_G-:;Hfjn058M7k)J)^EUT"L/`o<]6s2k9\q"=**h?%6'S&".)X?]jI0Yaq6'Ea`=d>iHCb2abQN/nJp3_1!7#oiGg(-o/O3
%I]Ji8=r"o#_k1&-k0jn`UnYMEj$@1f]2?ueP>!_D^3s:?MA)V\]KL;;&,;A6q-C.d*jGWr?G*aAJ=j$ZA3d1Y)Ym7e"k+caM5?;=
%8reIsKQ\"ph[]7JDkZi#;67/,^b2b^`dsQRP%fB.e[]P4QUnXk\f(iXNCfcn(s-eYrfOH48,jdMr"il7Dumr_m[LJAEj#m]F@6\s
%D2Mb>$@?fpTZJmK[qB87[s.;"2lq:]YZ'sV(K5\:4oAm7\CW5-QeVluI>OM-WoC,\3MOmB=Im,ZA+LYKN1K\P(l&/rm#4pie,i^=
%bqe^VjWieW2qEqs(^Vt3RPi4^gJ;Z5cqV81_*PlSKVn>X_nK,d%(B0TN*tDp)<&r*)9EaJ0XBU0`$jF4Q;J<,J^H5^NX<1^-RLSg
%,+FseZ_K5EGbN"0Zt!#]Z/U.)1@-lce>)E!8NXD)-<#=g0KI1NbN(/m)cI.bFJdQ34>XH*4qCS7b'BjBd-"(I?P]%FHe2`\LK?!I
%\+Z_fLZd:u_0#-L$M.5$G+"\ojC\\aH@m*kD=#JW'JD0sLuk"XPKbh#iVRh2>mY,2pL36Ug&(T8Q-&b2<nes([Q9U1Vq!rBCKZ(b
%)qGq[3F8e'\KA'`>kniE=JaVg_5cA'gt##@e[<D&rMVTY]mmMgB(^m9o2HF+q-JMa/Tl+LCLpH\Xn?7BASphh:$XqM'6WuEeb\'/
%SumW(!'Q2"H0`*H'>3!V4mSuFjq5hoeWOe#`n#$KMDm>\B0PP3!Y%/&j+Md0-927%9mu?rCseTj`0R@:51khK6H,7+YSgZEZ\[;7
%h3r"m^;^5_H^i&elY.<=X^CI6D<#?jc1]9r?NH+&<DUkU4/Y9M>:VWG]??h8g3lcMR@T<O?gSKTHF%DCkBD9c\7+_QK6,^eJf_._
%I3$F9p[;Sb:,&3GG=1bt]cX[Gg*c"AbeA>T*UK8D2X"NmLDYP]3:C`X%c>+QF`=hgPsaEo5"G7r)0[@Z8]-[]eRjh^1$3I4P[M@J
%Dr';2SsG:Ya67Xu4'nOdWfG>7puZ*VIF7:_N,$Y2o53M%'l1`*Y&]TK`*@Kg(hZle(S0["`QHkUqI@g+>'AijZZFMS*D;I=Q-fn!
%%foJHjkOt[Y/!#=k7ZO_TIWouFC!#J>'/jVNKL9qNSh6,F8*]IFI8>!'D^MJH7S*L?2I(M#-FRL*b4!AM0A'J2*(#`]rHF'3YT&F
%Qbc/&S#_:mh<jokmSRCkE\EWXm+s+J<f\ak*dU.%?e(p98o`2%dk9k+^)K.027OKKmL#qRk@MmP4nEn3_0Fh'WoS:>mlk[:q.TBB
%4lb@FP7nbhS7!FcCX.([HbUUdGp,B>H5H20UNaJ%Ie7nJe"2V%f?_0%c8tG40C6W$lSk-D:AJenlW\QCheQ@(qY&LO!atroB3Wg1
%BC`P2BOLf&:[^&[Iq?-rq<rZ1_*<;Zdf-_lU(ac6RYsGH&bcHnE'Ort\*okQEB./,g/:uX]Q?6s^`lmQ"V/ud2X!dtQ@lJaYRDjj
%)[$g*[b[K^]+hXXqo)Sg\1F_I+_sB"L'%&E+Jb$6;CH\/4bYBE]b#L''qR`7N`,)SqD;U9DEZ,L/_bljOnos(Ycld5p=/<1K:3pf
%.fddRo'R-@iOE)fLl4,Q1G7Qtb?ShbnD&OeXgqH7g?'FEF.MK(/fu0BVjDccolSO;H%T7K1i=`>HcbC_4@Wg,aJ.id84bHdTrK@G
%6;Op8K\+XO's4#6$Xgp"533Gf;8jt"mX8.+c>2<qj*UYug^3\FjBHm'P1EL3;Dse\8?G_-,7O`R<dOLe("qnIhT.2@P*7]D:d]-O
%)pe(_l!DO$g:^GTG"#Q]4JN=2\Em/a:2.cEKehAb]uk5GcDFk2W6njfUD?d";146jDUj"enZ(7l$!uZ4`N#5G[PqAA4c=&.@lnA<
%)YJm!oD;]NS?XhW^2C78qseJ9Si:-`J;Mp:bB63HAMXq9E:"iDUK<>33X&Sqhe,#JenpVFM:CiR>+H,odD"?-7,K@&0<r=;I4?$,
%N]aT$rbNEuDVL)S9gKEOrbQQnNu<RVr3kh6a.FHr-j&q`I,hjG"B(/Iq!GM6;q[D82uS]m\Ckcr=s?qfL(_'A\.T7[K1V$Y()rfi
%l7I+Os#AWZU\h%IR<BqRX/eHX\tY,7UfKRi-&W^cRs[h?D9:%GkQ<N<i2p'Ol$OqOC@(\Ke7=YX)tpn]aA=@q4/5UY#)ur%VAl"k
%E[)TF`olT^lLgrW>t.W9]l3W_=6F^mM1fJEk'"CNAHnFu0$Ao2Z)eX9T<`ag/V;+<*5cgjh<3JR=KRt5):e*uG;i18F)Tps2",>u
%j*BJ@H-Nf>LT13PQJE<=#QD.C+5;0s7W[-2bhD*%d9V%Q'k7bMm38>P)Apfr^>r>(pSHLuO!j"R)D9iMR^F5Eb8u[XQfI[JWn_F8
%q=8;V_VG!Pl0MH;duUsYa``%.(@<$*?o\n"(f)\iH*eXjQ1spf<TRkA>;^oZ4]J'J:\u'ZRiH0XAo4YM1q*S&e0[B4;g4"sD#HhJ
%<U+EYFAm\$D&!t_Z*W!%L?aE2;oQ(W1=iV3WA\[hc"bm:BhnDd6-D?+gKM_>@^u*X8CUhtAn9;TSmks:4H#Q\'5.0Lj%2-s!7T>n
%P?-j4F]^`[8N_DKkBa=U'q[R.2dMZ'pV%HUEc:SRKDXLMY<FZ=q;`\]/)<<ErSdFe>M<#%j<ctW@o8UW4cZ*<nZF5tA@a51r1Vjc
%CuRUT(N]_G?K=",kd>/b;K(Ar.SjM\[QRWXLWS"Mn[>$AX]G9Yn)fktiSD`PiXtd;Qdgms]KA@FLOFMud-]^9g7A41U>"fQo$uG:
%;Qj++m8:JVfQmeqTmF=h".3M,a#spa`I*.\qkpWmg5t-NhV+$^YukD1EAdHO*%t6QqF&E<%k!Xfh-mUV,_W/4$i+M5iI%4LG&(\:
%e5h]72>#Ik/Jm1/>jQb\o3*[B3+(L-0JIp?X2FjNU[*8Rh+[FFL#uX#(2ZE?(;&0mIi1u4eE-X[)o#PEhVrI1B]JLIM9cl:0"9tH
%V-R>M2%6ap[9j7.f7faA=jI<Bk_<_#[S=1"r3BK&Qi_e`='st8HRI^[DAaTWhBmifp@d1?A9T*86p%!07?d@bAkU<R(?9YZp%;Ph
%rI9dJl.qij=T;4e0Yb,AGtPOi>3K%LW^[1OAFZY)p!oO3"F+?<YZ0hgbGSb_NBJAsp7^6[#p[4Tm)Z*Ac$Zp$me4KUqJbs='WiM=
%<soD-Oa($/\NHq\_rFL]rp'*K>p#+/CTL9"lQ=:Qi_@Dfk_jUnY"8T<?P&`m5VfUh^<OOD'iW1-I^^gbmg"&[\Woh2[W%t+NL.VR
%E2i.Q-anf(P"G7Ve\R*:NFd=RX"j:Rp-dsjkY4JDRpBK)j?ino&LfHhA$<a53R-)HM5WCO5k5[4)Sr(^q?^2:F:S?d,NB%[&)"Yd
%g.R;6!C(+EV&dXrIccR3[R8%Gm@E>(da\@P`e5oP^7<HNBG74JHgL%B<3Z<aeB<="?6Zu%oFg+gnBGV@*J]S84.6-d6EeVVF6-e'
%H6!0q^:Wk[3SUHDaociVOVaoMj@\lt."m4p,!^I@HC@%"iQCtVR3Rn(W3:BX&[8Ac&pO<%!PiBPDVm-2P\TX99RMjgFbeoI-#``8
%Qnl**WU[eDD\m4G8<WfW]UJ`GPL>DAk1EL;g!8t<iV?D\P[$7e>_0#\HdGCpk;t<3PDBob=DEpBm9W+d%:*nd=?."GPW1S98mfKk
%C!*,HfX69NMJK;8"EkKNVa>f>109LH"9*:\WDqd=)X^7Y6n0:dFjb_5_Q;5-5K^FEP$1*GU@$?<Qljt,AD[+(!DA<n?5p33;=;N5
%'p:&<>^5=[.A6'*M<0&lZ4Z@lSJfHAYXL6$hKU6NO=gNf*E_nWq;I6EU?5I_P:k;_.=_2JcGnc5H'$rGeeaCFrOd,+NL:H&@aW9\
%p%F5]o7SjB=%Q"S0[M=Q32+\\UVOEMFmLt$8XmXDNFtRqX>5V&KD[d[S&VMTR"U@cl$hN$:We;j/XeJej*Gc)h!7u;YcKd/!C]7T
%HM9<71jV^M^"aT&<2.6V-*9t$o5*\I\BQ%^G/#p^NV&$g'^"e0HuWAd6b.k+".8^:kA)Ha]0[jLh9K25\D4V<>Z.Eo<X'%[rP%>r
%Hhp:Xl_RsOr^\N]>au.R6so<=c592,aVL[+r5l6c3`0-'X:p`YhPrLBqnm&]Ic0>ll&XW7gG7E9'gV:P37gBXl;6*P)7O\I8^6>m
%6[HO;]uG</os<Rd,:?/578DnlAbG3f7q=4jGfcW$(5#*U<U#uRJ9"Y,]Y>3Sb#>U98PKOAb7t8W9PX3C:Ft`HI)mY$Eia>R>i(g_
%dH?t,fEW\+VBk#X.n4^@(^$jCPa%Qf8[u,+N:4f96b)^UZQ&3qdsMbtp&e#5q;$@[Rd]Ao0qEm9.dsY(F6@[7`aS$Lk]GX56<<g!
%$4hh8e7f_8RK.OP-#[u,4F5Hq0*NRWO1@&gJr)!*lco!HRV4YOrCgCsh#WnAO%jgo'\T;\lWZR5Pf%^\pk+kR0&E-h#XhOUI;5XM
%O00,Jb*Yi*0/qb2I^OV79VG*Yl8*?T0A\Wg(r2aqqQ[qZU8^+&WR?-W]5f'$V_QS):KK0BT#j*AQl0$4(uWp]<(e-W\9enjm'+"j
%4;YoKi)[2M[(eV[!?l5!;"6WZ0-6;\En;j3c>+Y0*.qOc7^8<gA>3e[6gg,f9YDt1^KF;kP03nG7".:j'fD/<n"O9c4U<LegjL4C
%Zp:X$EP]cLFhG:oa+MTq+pFu@hID$maBe@,?;7bK!/&NFncK*$RI`senC17^g%;5/Ku?oi_G<qE57qQ:AE64:YMu/mbjq"XZDius
%_cfXWmnP%i0[A+p0dEg_b"Z]4O=<t,'F[9hU=C[2C\3A=%='DH)^bW*[Q'ZaVeQbKVn5gS"k._[E._d$k"#_891],jBd/oK/u1tP
%nB[/u$m9sgi?LT.4!tS+j\2Ka+VUXZqA[(;Ag=[FhA?16M/r,2/E<$K"<cn2i#+_1:7i[@4NWE.YUG%t:%WQr,'QJZ=T$B,!Wi=_
%Z.3o^UBl6Lhm\M2:7'%-.SGR7RI\"41'+b-MV$;.5VVtc0'OX8A>1=2FNVN.$lr*I,]f&r7hL^9S0gdtf*Ymsh^[l:L35ADMUm&1
%?V!4Hs"(#:ZYCL^6fdArSWG(\/LQ\Z><6e*O>E;!&]e2jq1c!H,dMN[+1QeD0.'F@EN_l/+rgcTdLl`K,_b->i4J[6EJ;f(0I0D*
%kJe2:MS`FmF],[CPpKor(Mc;(+%M\47V&QQL4mF.2l-Yfa$2e)%'l<XR'Qpd,U-"1!P>Tmc8>GoaJi9>Rmr7i4NfaMi0+<B>(b;U
%Z?^-pcjCfDo-\<i%O16aoHNmUcmnVXBE^Le7;`>Xi*BXmig15cVs,_F5+*00">4RUGVm+e0!8RtDNT'PT0XYH[sScQ*GUUJk'>kV
%-6d8k3Tj+IX>(0/DFgFU_/1K`XI`mq[r8nDQ0GOF9`c09T;*fOR9<=!/-`qDO5uh@F$QL[jRZKWY<8f6BH-D,3)\(nUBi1::s.uG
%J-k+R`Z1Lo4*Cd^[3aNZG;ej#?&s$7H7\XG0%p,+p3!q;fF&XP(h<T-==TGAX0*sYJ*sp6_ZpKGoDQR1060W:3LF9G2YO$6haJJ<
%a08lEMB+lIKCQH&6R'5MqBCR]9_mKMT11a=:6>Lb,('ISh[8q8&O'>;c-R]&O"VUqI<28m'XnN-mM1Jj=>LAHhA.c/&""!!nTr;]
%bSZpKnVU:f[f=*_cbMPgrS9RAD;$;8Ea>H-P[,C[ScM[gQG:PQNXB_k\HHK[_oUt39kW^].JOF$k6!'b@U-i'j@_Wp3Dq^\^?&Ia
%WpipoFFe'^[qq\C)G%,so@3+]KqBUk/B]]=kM92qT'A(o0r,'2(XKeubUui[%&"Grcs?&DDUeT9o<qRRn#i4_Z8`8mCoKDHJ-?>M
%7)*EDp.<4IlqQOe\QlO@lpOM.9bBA?K)saQF%Pn`A3#-T`uGhj(BiSW53Z7H(c^>2aaF!iH)!40A)Pp)]L[&:RpaKbe*asm-Yb,f
%8FbOD=tjG]neK/J:do;58$A"b`3t&f`FoD#W8AYBN4BJs(fF3Y(:r_CS([3p-r%hk(^=Z,k0e-I:]AD-9e5]FqTkgq@OW[5U2KH'
%6-27^]XCL[Fa2jcg7*D[)4r3PL8@5B<_Yj;HiJ89_2$`]UJ1m;]Q*Rt/0>/$;gDku#6Id2kfm)-Q0uC\ID0h#/pEQZlot:XH<Fn9
%`U"R[S\Y1*>=cN,&0p.MX3E31Z0[mBbIZ.>LuZKV6DF_DPJjjI?VH/b]AM-!$a9=U29lAa_UeU)<b!ZiIA=n#aIjlX+>Y&6^]qh:
%,?]>aZ'fJr\=O=k*n/09\t*VlBV>f]_#7]u92,mYek[P+_-5?4BqJIuS:+FGo:12#[-d`R'u="Wq/1j@67Bq:g4&D:,Z/23(qQ_.
%QH._K&@)pH;<K%,7K_8ILm0XlZ7jG$b\b@@?eo')WB-g$Kl^dKpK34%lV1_Fr??1RGkHAC)g)IoC+qg$2Y%tjPO52&2uEF!_jVs-
%eW;tR(kc$8dVp*BBBU+"!1T0Fe`F>,JJ=a)L.;5X9f.jHY3(d=EeA'Mg^/,qVD7c''2>'V!/<NO3U0@U"53c"OC\[/<7?JsbDYZ]
%)>t*L.j^R]>Ab;2qo$E$(7SPa&0>ab+cf=7Ml+t[a?84I:V]M,q@'cN0S8s0IKkZlI!+E=OCb^TZV$W3CU:,WG]-!u+E"T>Oil>s
%(H9pm.X,?Gl0N<p%tUo*e=4s03Z[G$SuDea;k>WX8%[2uU)%&5U5P7aGko_*/.H=8AV^Y$,gqED\L.DDdh8`BD,#mcq#)U,VQoqJ
%9=UPP)pV<+U?Ou-@IlcMNa!*p3QQ-ga3%Dq&l'5VOlYXKE\t`ZjnC#E]iGk[$`lSa6$%[W<1ae#R<aa%<M=\qNod$i<n2K$T7.)?
%Qf)tC_=U[@;8k*F.p6>;i2P-_dNL\Dnr8$`:#2]#gj(Ek6u;-jJ8An"C8&TcOB^i'QqV8m(PES;d/^L-ZB[itmB(O`0AZ64d.o#<
%P/EGcE]XmC!>>k6=\qA%g,FpX1qk][S41s"_f5*aJM/\2XIr5HBgULZSZG"*M]'O@Gl5pB#W26QB9Be?f6Z2Bb,82p["Y)hlAAW7
%Jq(qWlGZN(r*^(s(Y^0T^RT\P`20ZgH<e_FEm=CJHD23H96c_eUaS4IC3X`/<A'AH([pMLOtLW1V(O-O^Z/7.TQ*m7T<K;B&_V<R
%e\PjDTm!BDb-pY;Z];Co\6qV(-oflW@lA)q1S]4fNpnX4I)[NrS;l/ZlG&DV[9;+i0oC\,+rBR&r("<VC[S`>I>iCC`h@Z3"$!j2
%VME&KQX%7b(+5EJH=N?KknS'8VTQ_FI.SWSidMY-bO'p(Fjs1q/']Yo<$VCHG9rug72&YY7[<AlO@+ACn(5t1^0oqV-C+2phO/"O
%]SXfbRU0^i14"?hHe,$@A3fkCY&d!'#M]SW7Qkq8&0aTp.GJ\A+d:?$dnK$R,+k.k'H.SV4GR6&#?]4`YYei,g?[=4pD"P3ZD(Q$
%Z-96@Kr2D&V;WsB'0Qf0`T.o/_>?nZgHq6I]fb^c<K,f4hY%g\fr[3Ok:E%]o*!@PhQWg`qC:ZVcU9QVgaW.nCHnic&'B>Ir>^1a
%Nj`#a)^WDI9fpS6Pd$ru'RNOtj'L?IbPmt0#-ua`Rj!["GM7Y1UNQpEr68/@7uHiKJ<A18feLrX)WU%F<Ng[8D(pi')[9%W(*Qbn
%OE"OnA\!D`(^Vp\R)\PXV#$_^G\[Ab17i8>j*W_8gq^T\Lh#.N:+>@B\f0L3<rj"VdYF%$TjX0E_\Z4e)qJ6m0-ZJXk^hKG:(2=R
%&PG-J0"9B'U&n-A(r,WsDg6^<1u:C*`RD+3Kh=`ppUeeA@onbq*cr-/^m6kT'Bj6^B/OLfMZE_9<$PmDZk$70H+E-HJt?r7-Tp\=
%VPA=6qpOIeFdoO.PDc`mNiBVu98(\CA)$JSMG9G?qXZ0.[nBU;;JJBrn@CdFEteG_0!JASP-V95#5r+<qdj_^RaWU<Q]IW;,LbEZ
%0Q1b"g,MkpZglMff7'FYoYpH[X-X=;b*m3/l#gT2BQGIoL":C3K3I,:Z_"`cFq<dhP*)KpkSb>p=\(r%WSj&0mb5HT-WMjN!<Z?F
%qjBAr,+,m^7^'!^c</[(YK?A;UY=QQ\D8%.2f=SBN?:mckkIQ!j7u*R#bW8nT^'6k;;QWtk3n<f"/nc1SaYG^G+LcGp`#n3-7QL9
%eAn?,*0Z-BK@5-2XO)(.N2\(,U]*W)0#uk_l*DZl;dln<T0eR5kq]BlcHHNU,72>(O@LVtP?pjqN7QDC`r6Z3QP.32dBFMNqqc(.
%);pt:]8+"`0raR1;870DPP2S9)B:Lc]u,fATg/r4V@i.4AGMeF)A(pc1789Wn!ibgKE</:<QhMJ#1IY;E\S[+Sko8g-8i=G/tO]+
%R^]CXUo+=AnD8/!3mG9uLU<<.R=*^4nQLO0^NIi-6#/ALl'Dk_N>m&=ZdIOP`KN]C?:e5*BmAg7+iFf.,cVk[l(%)*=iG_gU[L`L
%]I3A-AiPs.GVqM2/&ba8A"n;q#(Mp,f<"5qrV/+qn#TlU;#1ocU:-YkTC-;C89Y.0?Za;VNSLNr^/??+/7lEM:7h"B,O=tup.3<2
%a"pdtq3pbniL=#"([/U3+W\'oVb+-<"Kk1$roB]'e_GH3pL=.,'Pm%>D\2$5!M1Y/`Zg'),'N@o04n)8b7^c)gDd`3DrqUGWEE[5
%??)Cuk\GV+^?!nf'N?1Pl\kFC-/11U10j)&_2?3rF5D"f(uYhoK*hfE2^2DLZ&njU,s!oUP<>O\>p'YFT)Is+=#Wef,/>7\)UUu`
%;d^s="bPmtcVK[N-!-GNGHCnIAt/m_g3^q&5i>>J;.-:=7<nl"2fQ$&3Ge#0Gd-I/o"*3bcW3WkZS,VXBqTu?5Nl!dQQF-i/<@0.
%)nXZn5MEMZB+'OW6qdfVk[Qr0iq3D=QObTQ5>+S>,lIFjTM@,?bPpe-;n/Wqh`UhU"-DS#1SH>?k0o`CE#C[3$pSok:MZ+@2D+\1
%CiUN:H>A'9no[>_l3p-S,41B9FNVbF%/>ZIq/;g>nC?!TptTH2a?5Z-,*V2BP.l8(p=T+]hUp0Go%:F^m;FoCT2(W%Sg*kj*qApP
%i;lFW:>]=QNKlB1o/H5Wm[nJAfJ`I%e`cq1XC("Lr7+ANBDU%$]CbLir6-d+*ao9d5Xben/PG7_mZ%P0*>qU6MWRWn8qm%;IRN&Z
%7AaRo%,hI9J^X)!?k:)jaB7Z]"MSml2FTIKfY_Km%`U\i@PJI)cZ2aQa<jZpM<NiYiun!RIY!/?@!PH"Og>$VnR<\)#>d*mr#.f7
%Kp#IpWCS^4PuNDEB&End$#tu\%SB5@R`#buSM-42A<4bF8k0SV=<m1m;qaa37/kCN3o:[)Nsq;<M]orkN//g5V+WQ4>L&:];lMJ`
%4@(?qTQjTlLb>88GM/6(7b/fO9`6Q=AAP(ABM.r3h@gQ_X?'(p?=3dsnrTD@2"@X]_D\]kUG_.oT8rl-TpP=la]$(;T;;IL1$:4<
%I]\#7H?s$Wc6;a#BLsI+pl3BZg*#mZ_sMjCX-96[bY\97KV`Uh:jkI>1ke/:c1H5Wc?R^-O8o%1BJb3$$g$$Kg7ODskEGmoZC9:g
%cVGohr7(K93Va%/^i.?tSQ#IrrNc?KI!*$Z[o8RDX7^G??,)AYoflOMX$rZ+1&1;bQbW:&_JfAar>[rTL9SRC.g5fqFhO<;:6PGL
%fZG^orWDk$So&^mnDm]E$kNEoeN_[?`G`B!m'cqteG42YoCM63&,c)Zepem/8KRG^;?V86URsF2)_1kl'KV.9c[R/V?h&htP#NO)
%s7$Jr\<:fZ\6TXAqbmH$j&<T;rg#NNYbjBEIE_P#N00t:-Gj;uhho>?0IO87NQ<hNH2PtOHnmpW8FuT.F(lbH<h/L;IW]tYY@RE`
%YF<U`K3/ouqHl2qq+HoIQN6dEn]7%eU.O6EJ'C(qL);uCd.5Lq^AR1)k](A$QS(Hm/H3^9Pj#k8PPbTPn[Q>0k2PjOe4'B,ZRFGi
%@?:Hb`=[1KR"u,1ph="F2LqtUo`#jWn7'o7YE%947Pg)VroRnFO(h_ZnheF[o\kEQq)'&R-re%^IS;po]^!h0^PI$RMH[VHOi$8B
%q6iKl7oWEUT>-c09>=XD!#].V6MV'-q8/f_3L1G$OQGT_Fl*77*!Rgild`_0N;ZJSrM@;X"6M+pI;qO?=/TS9k/$gmm.l>[pmG_^
%*Bk&%Vo=#u]Nm)QRrb^ZP#9'pPq@nrg]3(4aOI35o'nS:I-n9s"0B[]r0mL="Fd]M[^uJ'PkJ&5`j!4j3\(0II:M%3kP;i]]6]n>
%bP-YT(T42<S9XSoJ1ob@^*`@$le%2p+$Kc+F[':)m#,r4^-kk+/rq(orucN3hSP-An9R3&CqmFNY<QWJ4G9pA4?Li4n[YOC"Z^rR
%#'g*:?B%]%qX3hB/N*hjOnX`)&*Z(\<dR8EX?ZaC/G*9brl)X0B&S!b_FkO>OKc,Lq"##?BA9'IB@![;Z'8:b4:!opr1jbqjbMUt
%+A7'q_[jcepf[?l!EU<3>;<EMAIL>/E#0+kY/a./OG:[*IaK5C`[K>'0[`pWm3LRj/;q[DRK\bKc+EURtS7JJ[_MS*1j5J#oIJ
%ItFuE#qTHsP^,j<rsPhT$8PlU.]7eV6]9/<We_HR-cpmK:pdau0o`YQ@k)Cg`]0:YOHnTQE*g+.JFtSn<Ih,OB`"\2ls&06fOHM.
%5N-5QcT`[,-nE+%J0G1TT&hPBJ,UUk6&Cjd90d!>)Q1[m8IgW@$4RuQ!HP2J&bmLJ0>NE2:_b:q1Ap_SJIAcPW*lA7<*50e88:t5
%!WN.Si>d@\"BMA6TK`qh3*m22!tS>QU4C-i(oqB'"E7?I3cK&Wb(<6@_5rNM^j,ql"Z1HiJrZot<e9.Uj>8S>CB?=`YUgQU!!>4u
%W%"eVnC/T,X,7(ckE288E(V*#!4HF'Z6]\1=YV>p*8i?_)C8.`W-j\*A6=itW2cq2q*[>q1AO[Egh]fM"AVlJ9Ns9O%ma.[l`9W<
%O5^5Z#POoX.&sAk-jMN*8HN43MC?=K9J6(%b9TbAZQ2^`8;et6H8M:d##'>1+ol1R&Ji3^8e>qGg#iBd,[a+W<[n2W3#mft=1BB7
%>p/3<62Cj]EFtS5"T#Ah<oe6U,cSoej^k)/VdL1b:D3_DUa<2I@</"mL_@K;/0J,RW"BUT6*M)c(_Ikq1_:9g;/d)^(1h$VN+6DZ
%&24[p+-;/KU(Kb8`*@h,J6<"NO:"r'@'bO=_)RN[R8Zu0TGnEEK&^gD3&`4;:oXXg%dD1N@0glnNaif+0ad,o:H\W;FGl?.&/!H/
%i.-D'Z3#K36'[(G:=b#S!(`8";$@A+F;W(&@)J`FBL"?`1e7V$!U[*5Ls'GA8eJ=@:#VjjodH#*,5Fe^;'Iju[8A604K:38EC$7i
%7Mm^s@,EpX'4/\05TBe](6R-Hj:eU%YSK5&S/1TbP&R'<J8'T6LpS9Q+>YM*.O\7761R#F#HAoS10B49e7q+-(Ie3h;#h2-30QN5
%&5keVE9ScT4D4of;n2&3ZbUbo!l)'BT\0D?9'Bdk$[Dp#(/UTf;1oLp!pbCaO?Mf^\d6dT/<Mer5XS&\<?rL*K,Q7j0qD%&J<@T\
%/c";2QG?<M\gUB/77KoPdqB_C68p3k'"TIcbK25=YV;Nu-WX6/(jNl:J>YID")07*i:7rh'OrWiSY%Z/NqQU`;)2YcQm2(m2m,a&
%#d%W>3g]rpH-c]R0S#1.WXGt2#.5(T8f+-DYQN+>7#W#IV5V6GY5q^L69?l8$<]"<j+pOL$jtA/!om^4U6o>E1b^=%Qii@;h$"&&
%CT=A[TU,]l,nV\\"G3YI(K59-P!tOc'"%a&aGEMsahWB<AJ0)%r./S`5(LYj,\S2@#XD<4'3P>=<K9BI2%`t*4]jWn5b%hOP3!5h
%MNJ%b#IJ#:Udbe_'=!0J8JK;3!<u(SNl'0d-#a*dL'EX+4W]hXk)ZrWj,"N6-q4bT"F8d,J/6TL.N8+K9J4P_&;c13!\+q)jFDsl
%r'UH3MM/E-L?440[87un*1CcL<&/b0e1I=N=gZD(>+g:@M)%WcH8P";d'QXNj2.P9J3i0TQE1't'(FmLcC>8b!E<>M6_Td1,fC*Y
%!=>.;""XWC+j^IKJ@Qds!`'Yo'._iMWlRg_ND;H>P_^6/6c[l1f;o*iK*i&;@@BY[1)1mJ2u5H%(<C,jR&U=@$mLu'CUp@OPd;37
%oDt+A*A2"V7RWm`JqbO=&/+M7+I3"b-+lQud9s(a9gD]u"Y/5@M==IjnHWeB0GfZnqF)YdH53$d63D\)(/#B&Pr>:H;L"oni&V,K
%GkAQeRTi`C#ZbJ..UW<R+q27eP==<EMAIL>"Ig[4))Qn*TM$8&6cU[pciH*tO?!pi$8P5]U/EiOdg`bt!+65!&`-um!eLol
%Lp.:*<sO[^7!,iP=YQWo_g"gQTau_hAMYeMdGdeE>Yl0(rd];&.'=Pd%gF(?)4@k9W"('QO<JYr'V^[-Qpajr!A@h#KL`[D77VkX
%$^O01W33(t7qdpu1'eL>*C)Xt)aGQW?nAkj"nb_iOM?HmdS[bYoTdcM*N_@^7YhHVMZbXA:r)it@9!pBJC^\DTeNFV!5?i)EM2SP
%@07GO9NB8K&Nfi0j-Ud!a7r(kn\[7"r8+EToL6M-FW[_Ms(Kfai,/Zucf4Y#mQ-\kO!:nY7j*KsF'qlJ*)ncS@T5YCd-6(K^lTQh
%,RAtk('guMnfGl$7*L*J(:##Z5!'K9/..Y7*^g7'4c(r@O(]Ba9'?G@;P5QRO:m1O'o`8OBGgHj=HN5'em>`='9bsr>-tT(TV<KX
%T?!n59s#tMb-VW5,?Smr7j>trF#&FIfHBa:WOF#A,_T#L"%&9j7R77+PX3B%WnokO,+:(q=U"YB4GbqO&V;O'Q(pqX67?ROYThJ^
%'kA'(0860!EsRa,YY#)^W0,"'C_mWMe:MFdZpLAcI44Z*7:[!s-"sXli_MkYX8s&2-;@mm"BhFL5%k^ed48=0LttTE7>"F4Th:OV
%,<Tof&@nPj6=gA]h56;W";3h6'p(F<K]&"e."i0K@`9!RYL+$s/1O2t9X>U-Q,0\Tp(Cu4>nJq&d5[j&4<5Q<od1,5ER]=:66Yn$
%:dq`ZfE>["dj@Tf";6Cho0L,Bk`5fgV=u7.`BHNr&kRs1q#sh<&IX1)#qDTFA77S0-pX$2OA]$Krj/]p[+Mk&\QJ:7]ia7B@b'e`
%IS`W'P=fjcSQ,TH3_:Nd^SR1[IpXY8ZVpAo(J9P++A_V:'.!3`4N=Kc-@4V)7&1jgMh9^h%>E;!(?nYg,);a/UBY[L'$7"N;RXCq
%7R4H.@C?VgRGs&2_D\!]YfKN!4&+CQQ6[ZM`/#;7K`nV&nAH&7,hb.n#'s0B_>]%SHR\TUQku9L,I#Cs7&FBD:SS./FclAf[0IS)
%i)ufpA0G6`TRATE,tJdpe&Cqk6VB_B"D+jT5VtdGUe!7FAR^q/&4'ER9t^<E=sYuV,!k,d@D]un3CG.Z@52R0A83tuX;%DM$J((G
%=%K5XP.Q;[W!]Am/>*;72$5rIV6H)(_a_E_!s)kNLf"CTM&>IJ?lqpQ(BF7C8UO'4O?Bm=UiZk.=HTI]Jq-Su!]m%t5ZJQp%*90-
%)iW:>$cJ#SPlqCi6]o?-^abN)dYn;4p?6uS$D\/C7Y2Os-4Opf8OUule61:3E'>C]+u+MI^sW8a-io;j!?[;W&nqIW8W1Y2><puJ
%7F_Z\A=@q8Ka:[BG#Nr_-#=9/'T\6*#Z?_g/QaHaQpZ[+<EcS6AW\=;$J%Cs5m;:bXBBl`GZ5g,-s7FQ<C>G/K+83MU_7I$qTdQd
%o,(\AJ;IF+!e_^S1Ftj;0k:^&cqO%!Wf]Cp-_?3-**E;41FdJjZA_OK`AAO(.(2Yr8,:D&Z<9m).k@>C#Dtig-t;g"&Y#t5R24jZ
%_r:tuK`Y^IU+&>-Q6gN'C'Ah_A;l+c)8aG\!5er2<#2ao5ncJ!j36%>c2gKj=B_$%1Vf:[CB5U\?9pB$?DKGBk^Mk&qOrJ5@F\*s
%2$\5#/@ZKMg*4WHGuPQJ`h3QF=cT@@)Z"jc1I#?a7''&_+H5_7egq'*)RXPl@RFB2A.0FrGt@1Mi?XKfZLGX7=iOntnNW*d8Kr)=
%'OY4b^-Pp';)IMaOB`Tt!pmM_`,IFBY=jK3]YME^a,E2Q"41]i'k2tb`eZGPVLls[`@"ee8ONjm1=6?c7-u8&Pa#;n_Z<.?$fmll
%%AJsQ/4kT$nqKT$!3%u0n,Xn9/8,2`Qa,2"abiFY!AFc^lTq<M,8j33,=>$iT>SJHGnNrZkh\_OB0k0Q;58T#W"/>X,"`'"Kiu3`
%@mL0X70SI^'T:o53Isd6)kcrcJNTB3H6eG?<1^Ou1+?h.<C+r@k3hkN8F'>4ZBj>Jhp9b,1_.KN0K,n@])eC3dF:>?*Hsn@jtXXQ
%0b[*j^iS_Ld.!^-UBJAi[:E<'@=Y(%Zk`KH23OIp*B)+lJ0&,4BWt1"#QmefW04e3Xfik'`29"1UP>1$=AGr'd..rrX;dR]!K\Da
%29,0R8+JcuNC[e[MF7e[&G":**JoB`OjapOf&m1\N4<75,&rV#n4:)(KRr6([SMotGYUoR>,_^f,!(P/)[TV4DFbV&!KsCMhLX1"
%n7/i,+qm`pa<]Zn8G5fZ>oMEuU1Aq'\-);%'@o$38?6/RLmE1gPuWjH,`tT:(En:)6mL\Y$9lC%'!J1[XI`@%XPq>LqcKr.K0m_d
%?l%Z8/F]E:Od\XfP`W.2P+oQM@/iV5X_Ba>WE[`0+TAHuIL+"HYbJ+'`3]qeP,>230r[L!5p`4=0p5(Y@+'X(UIpIe&e2N$)%&bC
%8AqS9[(LuoB4bh/.>,.06F_f9)UYqd5QR1HTYtr1W;2=lR2]#!kQBk%Qt"T<`ZdO6?qblKZcGW$Q0?aE&j48a3+0dgN24ZaFAA*8
%F:F3RCPj3l&Sd.Zig)'ReHH[)Jh]1s5!TPWcqG17ANeh;_3C3cD.LToq>ja'%N)keP%enNgn'*5+JiK"@'&P>+<O-%Sd[E9h+VmI
%/]\pU9<dnX=s1*09o+[#X="eF<Il>`fLtM/kS/PB,)PW""e8V[<U!V7"of^FBS`L[?;'LO3C5GSn"bZ4aJYtRAdN7X:<'"rZ.uTF
%MXF5LC$Csc'U;-/TXJGa%#`5>Xdl;9FTUta8Mp/E&9Oj#OMh0gGh*;Y0iV"Ar^7r!1)>F<B.T]m^p1*nNAVqW#r_nk=O9c>53AA'
%q63u)Ei*UgACI;]\4iAk?8c'r&5SSpg8aZM9gi-MGXH:61IZ=_;PkdNAQhi[S-*u#`8ce%d*ds/$mYg>]8P*.@X_2j(<hau/;96d
%'m*Ab&ZdONeR4=Z+:Nnab38;Mh*hQ[<g'`%CJLhd%QX7#=@k2`-`KREFdC&%\D\ZBEK?>SOW?hLJS#7M0[NUZbpW-31<.EQgsXF\
%<i3,<Yu2-g@B)e$BeYH*,Cf'9V1!0SY)"o+Jp%R%24BT#2o5V;RN*0e;s"Dom^&X?;FkoJ@Q:N0a7u:nglY24qA'\TOGYY&)Of`<
%i2Pl:ZD^UG#&@da(bAN1_BuT9c!lr>U*DOPKH^OV'./MQ:Ws8K73E)9R?#4T)gi4Z;m-I1`)F&D5=siCFFbEgA8o]m-&Yl;T$RU>
%:f^,ao>u3QIOpe*(4C8,Qi?GbL*1FH9:C2U5S6o\"i4/0\HPDn6\Bpn%>B%UXNtD7/.ll"W<=\q%nk6Y9hH]Hk650_6p/Th,[Su>
%j'-"b&IDqeKa(da693StAV_W<7$WQam\=0@$0+nriF%Sd;PqXIVb*Uec7CA1!"?[\kpGG>)aquDei<TlH&VgD`k[&rG9>I&^VsWu
%)*jO@2ZA]uGi6p`Z:iHI7!h0d3RgJc)ClsNa`&sg":Z/Y1QOtO/E)agUX;#1Agj;[,V;Q/FGd)kRKGF4%TH9B<'%^Eqe3Lc9<qD_
%6,jpg#h!7TVjMKe.icU//921A^erI@Wuthm,@8?[1Mlls`#hR,*YAd0^IpirZT5=2`F:_rWgF-jSUs<k/)[S*Jktd$ocC3E6$gM@
%F;,Bu?86L?<[=p\1np%tZ+.T=r^R*lC;KOt`Y+[3.0:O1@G[bU/#-NjN<%4L-YA$>MK$U:+TQL4P`gS[)aM(P-Bl5caIHA#p6)\&
%!P-_#"o`]l&3);9Wt"!iQ@tA1C,%pC6*LU3Ud3.b)T:#.'QC5kGBL_PR5HStCFaKm1>3P\C_STH6YV[Ij/*hVF)4]b;Pk^DG;MU9
%fd)h"@,)...LCYsk(h/NjO'.Kdj1+0!\u<p=q%[C9F(.`A.NF[5`/FK%u>f1k;E7*[2:El&r(30-4?n?ZO>QY_Irs0aJ7G.(+dG_
%[\TR8QtC'>&qaEg*6/"GqM^;*]GNI,!N?$^8Hb%/0OhiaKZB;`GPm,(N3;-W5Ie=]q36]h:PoqqH+&FI^mq4I,Uc[.5q7b:XF>^P
%`T]Z9A4[tZO?tk%Tpp^^K:c6?,6b?N-VBQs]*&6qVB+Jo.;:tlR9kloK9q/)2iS:P^-($&p?,(r4EJ+"s'ojg+2RC6a7tX*bB,i&
%mFU*tAN*in'3fG&Bn4*`Y%7P0j8T-.7p5c~>
%AI9_PrivateDataEnd
