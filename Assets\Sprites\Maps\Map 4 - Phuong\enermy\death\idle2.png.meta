fileFormatVersion: 2
guid: cdc9a40638f5bca4a858f5df91dc1ab8
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4243389574673483943
    second: idle2_0
  - first:
      213: -8683696539369610142
    second: idle2_1
  - first:
      213: 3569995279447393958
    second: idle2_2
  - first:
      213: 1193457304162379288
    second: idle2_3
  - first:
      213: -3081069324102897983
    second: idle2_4
  - first:
      213: -5318655889926657360
    second: idle2_5
  - first:
      213: 8080958448749634537
    second: idle2_6
  - first:
      213: -3199906342245241482
    second: idle2_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: idle2_0
      rect:
        serializedVersion: 2
        x: 0
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 957bdfd0aa37c15c0800000000000000
      internalID: -4243389574673483943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_1
      rect:
        serializedVersion: 2
        x: 100
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 264e59171005d7780800000000000000
      internalID: -8683696539369610142
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_2
      rect:
        serializedVersion: 2
        x: 200
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6a68841cacb2b8130800000000000000
      internalID: 3569995279447393958
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_3
      rect:
        serializedVersion: 2
        x: 300
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 816a13d4813009010800000000000000
      internalID: 1193457304162379288
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_4
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1ce6e7026c7dd35d0800000000000000
      internalID: -3081069324102897983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_5
      rect:
        serializedVersion: 2
        x: 100
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0bae166c5a65036b0800000000000000
      internalID: -5318655889926657360
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_6
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e3e966f4e4552070800000000000000
      internalID: 8080958448749634537
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: idle2_7
      rect:
        serializedVersion: 2
        x: 300
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 67de3f3b226a793d0800000000000000
      internalID: -3199906342245241482
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: d22f25e050c93ee47ab6c4e0ed5d95fe
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":100.0,"y":100.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      idle2_0: -4243389574673483943
      idle2_1: -8683696539369610142
      idle2_2: 3569995279447393958
      idle2_3: 1193457304162379288
      idle2_4: -3081069324102897983
      idle2_5: -5318655889926657360
      idle2_6: 8080958448749634537
      idle2_7: -3199906342245241482
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
