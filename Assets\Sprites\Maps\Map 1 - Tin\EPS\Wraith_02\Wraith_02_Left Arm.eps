%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Left Arm.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 4 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
31.9238 52.7373 mo
31.0029 52.7373 30.0723 52.6494 29.1563 52.4775 cv
22.9238 51.3047 20.0615 46.0264 19.3535 43.0928 cv
19.1973 42.4453 19.1719 41.7725 19.2793 41.1152 cv
19.3535 40.6572 21.1436 29.7969 24.4385 20.7354 cv
26.0957 16.1748 30.1855 11.2637 35.2451 11.2637 cv
35.5137 11.2637 35.7822 11.2783 36.0479 11.3057 cv
42.084 11.9453 44.7861 18.4385 44.7861 24.5889 cv
44.7861 25.1514 44.7695 38.4541 43.29 46.293 cv
43.0938 47.3359 42.5703 48.2891 41.7959 49.0146 cv
41.3896 49.3955 37.6582 52.7373 31.9238 52.7373 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.0318608 .520439 .874327 .00128176 cmyk
f
35.2451 16.2637 mo
32.8594 16.2637 30.2178 19.4717 29.1377 22.4434 cv
25.9854 31.1113 24.2324 41.8076 24.2148 41.915 cv
24.2256 41.9678 25.416 46.6855 30.0811 47.5635 cv
30.6934 47.6787 31.3145 47.7373 31.9238 47.7373 cv
35.7725 47.7373 38.2734 45.4619 38.3779 45.3652 cv
39.4912 39.459 39.7861 29.1924 39.7861 24.5889 cv
39.7861 21.4297 38.6504 16.6094 35.5205 16.2773 cv
35.4395 16.2695 35.3428 16.2637 35.2451 16.2637 cv
cp
31.9238 57.7373 mo
30.6973 57.7373 29.4561 57.6211 28.2344 57.3916 cv
20.582 55.9521 15.8193 49.7617 14.4932 44.2656 cv
14.1787 42.9629 14.1289 41.6309 14.3447 40.3086 cv
14.4209 39.8408 16.2686 28.5713 19.7393 19.0264 cv
22.0479 12.6748 27.7373 6.26367 35.2451 6.26367 cv
35.6777 6.26367 36.1201 6.28711 36.5596 6.33203 cv
44.4766 7.1709 49.7861 14.5068 49.7861 24.5889 cv
49.7861 26.0469 49.7422 39.0693 48.2031 47.2207 cv
47.8086 49.3184 46.7744 51.2012 45.2148 52.6631 cv
44.3115 53.5098 39.4229 57.7373 31.9238 57.7373 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Left Arm.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9ON;h^Sd)]/q<:%WJRj$OfS2Yt.@E(1J^'KMoB@Xrbd6*)iZd-_=kQe\N&imR:G.d7r$(]Yh`JRL
%md2s:hZ!NLmf37ENUPig8IXi1jDsi;'_hCMpV-C:?N&mF\+`+1IeVj62_X';q"af204',)mHNHmGON<FiQ^W5cemUWjSA9'0@+Qc
%oCDS?GCR1a^4-(@hr$;G+6VP<^3]bKp>,U)cTg3LViHT'\+JWdI1)[!$Li86fAHT&%?Q&7o"t^r$.M]:Spp7"s"h+jjJOV@mgK,X
%>q[(EnLhl9DqrV3:I"D/5P\0hq9=ucc2[U%qZ5:<J&)dUf?u/H;JPq\i\."4N4$Gs?Q+C#ib(o<%i>.c)]t+M)TA$#1G1'MV3g0]
%ersT:52Ubf(I.ICh#-Q#Ek=@2aJ\RUgiH7j8+;eT!2Sc9QOm?Dr6alhUHUa6C`gp!Qs(!lA1YFNi.kQ/`Dun6`=E[o*suF5(ih&"
%A;n>bI)?@UboOhRLKOAYV;Puba3Fm-+,7mm[rBt44r.UZa7.W?dpTcN)mtc[9+`[l28t+7k*ur6n40I%.DSGP*M3esVbEAc'X2eX
%GLqJSQ`3,]on^LGI0r"AO2+;N*hkigWH6\nFMo4QaX9k_j'([A9Cg?F]\qGX5Z7=pp(%)3CaXql'#%1[YU8k+P%1-kcadZ_'7@/s
%*t(hb*hVfU=-$ih>A49(XS<9t`BVn;TXjOk__5?qrmeJ"hEV&0lJ:3EU/)hZ>^/@.o&Usbo,h?B^`J1"IJDjIq;q(:J+rdAmus72
%'`@gd#<1H5ci/HZDs:1STJqEcm\G([bm7LEGl2JB[f$Yb%la#*EB?Dbq&.rFM7>#&#3IX:C[7%@MS;lWUg=$"h&kK9J,aHEmW04m
%jR6Ehq8;O]+'oR!$6_RWnCJ`>J+Gns@:1\Zi03//cu5LTLV**]GQRjX3!B.PSfQVPjW@jCr6l0prn#?Vhn4=i9^;-H__5BqJ&%"8
%K)^N!MsjueI/g7VY<G4$\XLf"oojUW]R11W0Ksm]qDn)HBc-'34RmXAhuE];#N2oV?@R_Dou!9]oPZtmm`i*$It!6Ue4V#NqnpUd
%J+rC"WLLUiG\L9/5/J(f2%:M5gqX-2q8>2\7>!KiZRuu&/QdeZ??$=%%fH!4mpH%me_;00nHjq6k9@S5Dr8>+B!C,1DArQ5:"Q92
%Y2@Z7Gl&!n?G=r+nDUjST1hr64oNJUZ0f`hDbfcXi$<].s#e/!GRKK/ot0,)?cPCpDc"DsIup!?_Jar#%R0CI/QVfEd80RVmC,<0
%r>kh+Dr8>,mLMO2qn%'5++=1Yc+AISH1UCQgQsks`uFm@%rT,]O3tRhN&gHQ/cQZGL[P6#5YplTJ<o0o^a-/dprnf6H22iX@S`W2
%qSNj3C&++qp@?+ZJ,-1S\Va0trU4+-q!d]7/E^qhIeM+f$NHd-_q^:8Deoeq);7gSpDi\k2uJG1jUZff0]Bc&d'Im0lF-C-dQ(D[
%mr_[hZh.&ls$Js\g^j[!<:Gsr"muopX1H]fm.!N-]EJ/IkfBY@Y3YZPO"XkTqZF:QeNTc25C`Y0mIU)$UjdXerrYFnK3RAZ-,9lG
%)+_>LgcS%*"'W^)]_q9b#PArO^`QsM?N"lM+msbdqU:(cHN)l15lQ-+p7#tbIrp)"&(7E7s0FnM"7Q`1`%=-JA#JLn?bcI9qr`J(
%QWGhXNpja[p0V_:mbPM,?_ETkRO+N(3,Q)L)bj\ZlR^18j$0ggCS9,1c2FL,@W*@,I/a'+TAR>2p$J6bEq47%d9EEOPDf'f=96/p
%<s?>O'=aGiJEP=QcXcs+^DY7KC(:(DN^XRaG.<^,^]3fg`Trl*r:@B>?9W2/iOSOYmEaY[Wqq=2(?/_@746-8Tt"TAi4.q[IFP@r
%(O?hne<eu\En&S]$Su`tWPYjBEIGI6TTup6$*Pjgg@t7.b'h)nLL3T]CXV#kD_*Y0eZBke-)#Kro"SWmAXhO]^3Kl;c'?3;c=_V%
%Vro244aZkJpRFi8dD(&$(3GY-?[hb7_b1cnr,Oc[T)(2Vb]2*RaN-YGUV6B"pm7`\q6qJRcgp[k-]]@#lMGajYQ+=po5@rdGlC(o
%8d3>4_?-mU=t2?<RP4R\IYY)Q2pl#F5jeTl$=0S:^YtmQpk/>QBYbaL#%+f&dRfMW4FZo5O!"<rGR\R;=;bI;8sWAJA/MO;V,!k,
%JK"(Y#:`4lLNnEC-Rrd)WT=3C((1"6Nf&T)TQ%g<p%VD,:^Rp9=lI!/cN^4E3RILD*MK!rS=s2\gnrimn/G!@*kh`h7!SWc?7+'D
%b5,,2<;:A#ZU*hT%e+n"TD3hrru\517Y)K(nGfh[$[ppp96'bC^+]>(oe6*P4McNX`IC5UAWZpI4t"+mVl9U$r4KGp\CHN9HVp`&
%kNT2Adhf(\ep_(84SRt;VR>EtnCM$S7dhOiaD_\rFU^re7joa/EOYAe4&Gi</GnLdcbB5[jku4!"QeugK;/K`KsjB9UEGd>>3Jcm
%i>>>K'$fGogrf&AZ;1hpL(<<1.j*A'Ic&T-Nj,)WXmA_s\^DtI[<nd9H_L,tBr+:%SV:*TVsa_i]JH`7RNYod!OcJjpshulEe&0]
%VfFE!jgN%leGr<TqC]0Al1J.SoiuEDB],^pe)j]h&$>MJGl4K\g9I4&V;6=H4hPfebtr`P]c[*MTDRa.%fR#I92m<p_\(&eE3!d9
%G5ZQWEh[Ta4[SC0%^um>T`QlhMd$sP'W-[ME2IZe_!8TQXdl%(m2O4Lb4>"Jja=UDDJqZPQji1g\s7t%V,G,NFm7_n@)(%[-9GQr
%DuH)2s!;EmCrQia5een#Q$h]P?f"DZ&uED/UZ2iF->*'R$M#Vs#NAe*YKfrs`jPRl&Eh3S]aAb_rqMWE_SOOsJgI)C58aE0hM%I-
%H.P&]THdc<qd`st+K%)9/DsKf:1N?cb,Z"o`WDbF/sYeaS9P_;-0.Y>GS9QlfYtXg0(bmZOWnb>%"\oYUP&-4"\1nNKO8$:$LMpf
%h+G"OV%bA+YU(_DE*NGZ*[6tS*8:!9'kTE"FeWoP!c78ZWAH#g$G;*5X!heW0GCD#cp)u4'?5ukfFCr9?UlLL._n;F^g'\)_C&a>
%,XS0^JccN^K1F8p-6AQaZEqT4W$>CFINbcZ:k1hR@F1#gYU#='p_.XfO0An/kn)%TTu'kj&Vn@Z@,R(f*Its.ho:9M`>TG_,&m,R
%4P^*PGJO2sB^rL8]>opR_lqYMkeJDIh/Z?]TF2ds)GUT@[_P"F,&jJUfKj0T5!j<]SfO1q'\lMU%!^hk7na^Hm>Sp`08'R&Y96ER
%Wi0l?'j;o?`6:JZR3Qm'_+B19_Z;aDWRpXu;Dr1771)gH:CRc9E-:77N='i`c_=*G&IPCd$Ci#2,G.X(`TP[k:7lFiO4Tr*2dU42
%`cuXjb(<UsCSf4C.^GLf/\sK220?+R_^e5Y%BgXT0=k1lLWMU)259DHX:8GO@nh&K$^K;[=9lJCTLN)e8"0HIo?VJ21G<g0o6let
%k./bof0%B>k4hVkI"[oa)5c*+R8#HC:1j/E1YQM3$a>s#]Hj.,9%#02g..=M)bKB6AD9fK4A,Dln\a:[1j((gTTR6UJVSe;#lMo/
%`c3?=jRA"=OM@taNS".F5-i7t!B%lD96sFV>bK)aB.<S@\h$Zfh3<(Yl$!l@&^S>+b43bt=r4,WrqH5KE`j/]/*Z2C^HK:Yom,fi
%7iuo.;j.+ld(eo9;=r`Dp5ULZ-inWBoB21l@Oq2J-f!0=-jS,l$0.+GGN`5U:m[Z`nYoN*i#0s9B`S$e]LRr-C(0or)bADD[?&[]
%c/oUASd/FG(K),R@B>ZuGTgW1rZkrRhPc9DOuR>Vgj8lh$nQ3o.:tX*@P1L[M4-iF$"2H1+&r^kAFP*aW<SDf2L(U_K?Y>`R\BS6
%k<,4^DC=s*dui>J&-ZVWp+*)S`0(KWM$IG@.$/`<A0PIK!,VYi/u07P;$p/H]R[`Wi:':[;Mk?b&`Ut*F%McmQG4dZ',Eg+`^IMu
%T2C%A3FZ*\(dB$h;h:^!Nuo)l=O9%T6$+63UW\eL8Jl;d38o(H@u?<s(.F*/dTKQ7nU%.<bDX+O&TWdXhUk;qp97g`qcO1kTi]9P
%,E<i,RgVf0,*%s\(`aok:eI8EnKSW7nV`+JAg1n7@3UU(#tp^U#nnLDM4'W^9G&mSd0CiS9N@bd_;YQTOX*8VpirGu&g1?+,-2YX
%ikhgSLQ0LfV'VO:((,'$<$:NO/DsX)W/s[]]R[e:+u9J&(TX<Yn'KT0;@g71E:H62#K"Fo/_]Zd=CkL<-5Gjte>kFnq6At"^T14&
%Q)gZ0<jK2I6]dafCcBf\b^2@l:+g"E<9*%:Fu?qE)'mQ#o=4Rp`_6:Ff7:8K]cl.A,GFfZibT>qQA&EV@!O=F6u4G!Z3CnBH%d85
%:^'_O`)ILM0;Q\Yb$s=/78jsq<@h.DFGQ_:OKQOL+cJ$BdA7ts(.1m'k@nkPT$;J8T_Aed`pZms@8WED2tl8S8eA$h9c2*uo9dEA
%Z(VCPE)uI7+:`R1C(cHPBB*Gk;4.p2=-S&3iAo,3L`6Y5O.o5MfghXq1pMY;k'Rs+l6H_fO\CY6Ap*?!<"01_/c:m1clpC-JO29D
%68f1:(YRXlk%6A@o@T0CQ"AV,MARt%iI!&;]:k.F<F$'ooRqNnf3^NRfX2:?[32@sT<!:Dc>Ef/.?IKEc<53?a:?0PlDHEsb-"X/
%6BU4Kqc4[CZtIF^4e!&_G*mI;S.9XA7lmi;N,fLfG9N"'O5,CqOpBqZIt8C_G\c"H'L0"*%5`/W-p@b6$Z]tFaThm-_G\8]pm-FL
%F(a[=ITC;iV![C8J"U&9^T:\0dI!94\b9p[n"D5(.<-U^&!_(8`ndJs/_.1c.UQ$4:q^bSd&G5ZHD'k4Ig.s7\R[ISZ=A[7UKCp=
%OHYG?4U4$g\X^V&_pUX`ne-`_mcC@Eo0.t%2$T09Ln/'r%M`TT,l+2\^o#Zba9+qo#YsG7dW19.=6=hi5XJVi:\qnrmgYl?2I,]a
%cIK6\Rr76iboFh!J44_BIVAXM`qL)`6o#X'ZT(sc']ji0=?Xh$M5:lanOs.QZe/8r=8[#V`&MbU3-!!lr<`]f@3Pd`E_PVag%_Fb
%:?A)Qj.HX>ClVQG+fio6oAo6pmQ+USrs@HkhL<@2`_0Nq3C+*4i[dX[cHP@tXO@Z\[a4aK8(qN%q`k'<Rl##.GKF_@HRHr7@CJRh
%S^ZN/bJ*5L@oW=gdp<'cb]\s5'AOOhG),LI7_>GKr]!T`KD*6j,BPRAfEYnFkY;suZg,f:nr/j;RPV-n>%k-\Fes;&AK4+t5)T>e
%0u/;u([O--a(f-]!3``XE:o,qXh&YLF..e:Fb(OS/UJ\djd,OUFbI!E?-2Hh73ari/eKJ"/aLnd,frfAg,!GB\)aKIii4k[/P+)U
%FP)b2*f+fH&EOg$fEZHN?A@2mjTh#j8#aZ52-/`a6`M'd1mO!?=+diHUc#XiN4AEpY=iZI>EH%!i[.5V8L5T9ceMEJ,+V9cYZVSX
%d#-"sB#%3,@Y76`d3Q%lN\6I;6n2ddGrXiFU8a_P]"[YPYX=VFoRXqbeoKHpJ@5\f'l&ZjZ9AaDrL[eX>hHoEH@jd4fHLrB]s-TF
%Mk8bWaUd)/])#Q"\WBO@oQ#-8TV>(pM-!WQeV*3VlhklJ_n_+N0jIql*A%>!2Ka\Pc1G:D23G;$h^;TI[-H]P%^:MJYj__Z9I$%E
%iHQV\`gM5N\%opE*2.2F82*mE2Fdj4'29$.coNhX>@;CkQp@Ap/>.fAcr<f6[GCtk$a?"K"2Ia,U36N"pAtCb+:pdq(Jt&`n!<rG
%ep+qSACn5/eU=\QLROE8Fs*mJ9"*fN`ec)6,BlMGk]6+-fp.Ae'KPV#fm8C;aR\;8Opbo*E45VK%1'=0[0!Vhf/24I&>>$8KXOpT
%"6lFNE)8;-BakCf[bX8tM"qR$*'8'Ofd\NB'0BjNnXbP^9lB\1UHpMVQEq@*nN<)cWZ5)OL>_\mBj.1LC8G%S<lRE<V1dg]V':gc
%3t&\f^Wu!04Y7dHLD9r#"s;(9!s'H.NF=f"mt;]$%!Ku+FtbJ\$9E^&X0@5'5R77]TEF,_i_$ouE\IIuE<A0[Md9GTbA]*+"l+X1
%`,Q#u0LH;<5n3D%h$iH[S6ZIX:oEnn!Z@rs=_rOroi&Jp>5m%M9X[1<$c21Z_ZI>e6ebpLYL^Cm?GZMSR0qGgF]BA/iM0F3e9K<n
%?EEJa@U1Wg[]F7*2s6I>#1WFuWa;jm-'3j^1p#IM87O/rZ_"A&k06MIr+L=/-MYf**KO4%Y_^7:Sffi9%'3]fb\Yn7XBq>L"j/CJ
%mT@"cXjaRrQXGY%>[k-@T/J2XM=m08/'+.qZ.(BXepB=@'l7=[R4utjS2:b29q39qFhFIhQ3D#&?8Y?E8tASBb.qHsKn<i(=qs>\
%R!R97A=HJ`rn1<[Y8?;Kl3APB))u.ai)LB"2,,Sm+]s@E%N*d@)$PtABEZAUi^919=Tah`Hu]\>g';6+ffjtnJjH`/>]\"G`u7Y2
%KCJ#KJ<^B[Eos!"Hjk4MZZJn_"uL+!QZ*kcg0Ci,Ys-28*O)+9m$ZHRX\uTM8P"E6<0FO*Ke-c?,QtE[3O1Q5anfs!HN`%L$HR4#
%ZNt79'<B,UX;qPOJQ/q5TUc3;Nt,=X_@HT1q5s"s'WTL@A6uh*P>e[?m$hdfZPEs((VA<r_5#Z0eAB5ZYg[UOr@B254.?W4m9#Z:
%M/VEf\p0PrD.3Z"1:g_RN&'u,0'lL;WKfW8'$>2+_#%8NOrOIRS"ko?'#2tS;_=N8ODLH>hF#kdB_)s8MItTS't'g.8Bs\sX'/^<
%@Xmc2*,/r/b%.V+@PC>^Ru/E-ZYap=HCZ+H,k[d`D5b.8[=V/8>Pt&j_G)ilZStOVC96pHb)!V8<_+`l)KAp9:1X_u;J;4IYYEg'
%gHf%)g^CLCK!qftjo"3<5E#9NM>'6'k9'L5T7lu&:-rlHk[]-G3re,K/;EN<'C[PngMue%<oH!sZ"4<#nJaUpiL?o&ebloO4oCGf
%:;sR+aFaUt1tm0?]h;g9i]W%)-SKg]E2"$!&A.J-.\TI3Fpi&+\k`NK]("!q<IU)bWP`1PeCB#(@h6p=\B#^NMeD"I"]foX4j]"c
%eZf@T6sBWWa7-,ec_;EF];58>RN1X"<^*Y^7VeSgQIVR;Xg9lFR:g-5o@5='NI4puZ^lF,+^4c(p0iPs_$TR_,#8+gOnB*9JY4-3
%-E6te+Y,+%=-:8(l'!H0?Z+j"66RlrqSr"<$tG\k_afoq<P%=baK.2%d0*CL'"6QB)UE%UI3puO//TOq[V%a$&K=A;EX0So20tP'
%9rC+3'RhrCr3VRDNb!u.MEQu2\-(FITR[HjdT^'MED,Oj,dp9N>>&l/)qk=$"UumjnOb@W9bQ9Dl,IXDL?LXIJN*j3h8)]jPdVu_
%<Q_>/1C@l-MroW&.-Za)[fpO%TcV+L(.56H6=HSGJWcW]lkSh,/E7&GKu1DUj$8^81=F&NA.7s!Q/YbtRjGS.a;iDU=i3Lm_MJDH
%r:)2@>@Z=qpgc*c3$^JKiO2WhQ4oicpVG`>HCb3L3Z1`ZinnKoEpJ"QB=&bPkJaD_S]1d`eoUVjiC4#@Pd9qWcT_*1o)%eT]s#>m
%*s,+8Jm\QBCsY9:nL>>jBGXJ"M(GC-%k\/pZO0<g6^$*:U)pfe#h2N\9@\Pa$(!c\-VccPZi?h_mtAS+&*,`2R8;,U+q+_/hr6Z<
%U^'r+3crd`O2P)FUdnl6U(biZqbE=M:+Ge4:0d-6*Ye4>0V:0s)G*TIj@^r?O.1*)k3,=pnn6t\(HMU(8LgV?MB7nm/a@7'Ek4*u
%]QE_Y*5qQgV\=O<\O@k_D'7JmE$flCkLlHFV]V3KB4;UZ9f%.^@1)fMiLOUBBeNr+e]:)i1o(7)C'U>8N)n(V-rd:kZW1mWZ.k"i
%0$D7$89VrC4lpQsqP*1'T0*Gk_jE'i$&VP8If]eJX1r`N'\["4?)CmAD&64,77Z0&\;ppIW.'J;@E2)ift<@uGO-RI6CJFJ0VRQ[
%*.*Nc;HiI`/qM0XQd3%+SG==>2pp;aCe+0'ULZUVn#.AcalT\"I0C6V2sEm-f%=9:^W;0jn#n,ln\!ReYU7=Jd&5\Xn$fRbc>)RN
%Kr#$A!M491M16^EAN8]@<6Crb<>jb_nCI;[^S;bi>0Joe!b1!&Q^>p[2gfmVI!Nbt0CLYM?D'0T7k[KMm(daDd@o8$fl\"p_q?_L
%MIL/hMI3&Zdk6N#Ms!,1*:#kp``iejh*\KnR'%a(FcC)%L3Yd_m)[:HAt7J?M\Vh!i%<N9$VR6<[]6Vu^+M-F_q&staqhl@=H!R.
%Fj>n6QuD/n!@R_D$!J`_6<N/\fB"W@9AUG_EJI^]q.jD9e=<]NSAh;-"SJUmYOEM1U\Eb%?rdBQn@,dA#&dGU,%MGlrt$u_V@5D+
%Kr_oU'0A3b"[$#(7.=An:KpF*8b\PZ@%/&HD"@6%]/n:`JBu$[[6m;i0mFdCbtduhJOGgh5Gs90Gk[QlhEsX1[lI"6odi_MHMdF3
%S!W$fr*DoQQt:Q(]#n\C8cE$=P_Hms#6I2O0YT(]4dn)k"+$8!HuS/j5s5G8L_e]C<a`iX_RBs5$c8h[L:@ZQe(dArZ)eM["BGdZ
%%2ff`l[dCdi`61*X&B9A\eP<u!`=Kbd(Dmh(<%Ou1N"dC$OT-HOII6s4.1a@'Pft(!AOejSf(Q4il[D%',4&8&7OOmP*g57J`;uK
%"r7i/*;c:W<Lddb6DCq*;&f,/W*mlE[$d*=1n"ju`QTj=d:N5VVVbuA%/h8_[,8jp1)jYVbAPUo0V#qbqbEZ+AF@0*n-3qGM_I0E
%Nl81];q/F)2@\Vs26&kAg.Tm3kdskmjB2%RZPC&!LK5!Xn>'uBIqrHn1n+cnAN3U!PG)l;a:?,EM;5^XjJ+@9'<>TBSF^SG+@*4Q
%Lj7&(atoG4;+O.'Plh`>d>rl4Znt)_Z$1oYJO:#f.27o8pENoO*NLP+Bloj(((-joTr\`2TFqrZ3&h4%A-3Y%LX;T;,S_D\>+U[V
%.'',tJ:j9<'Y<?pW"T#-^q#oCW^0A48`7P?2B02a3774GLj=me>Tb5rJI?`/R^KerZu?V#&j&sF7'"UeQ3o^@,.WUO$,J!1i++S[
%Kmi"%Y%?ZtLh*%6GO$qSa[r6O3/QP/33%^)*`cM\"mU5djPZg:Lni?_rjc'8B>mkkc^63r^Q7BsA-WOT?I7ecmU.D;aHJT7eY2!E
%JB/5=,74$C*$[1_F8R'#fqBb/7%p9i>Sh5U3sj-&B43^[V1l2"mi1,l1LmQm0=aQd:e2o^TM9H^I$'La!rPZVO=q91q=r>]0?>Mr
%A.EPd]'MU5,KL'AYQ>2oK5CW)0E^>T!',g#N_meW+E@gIS3eHu!(lDO&jC8@-qE=-S/&"TPX+11'gkt)=J07)Rc+FF9O:(b"9#H(
%,923ccR.<D%-BcM+TL-8EEGL;W5,QQ;]3CSo>'K'/4%)YUP&YG+BXFA:=s*(bd(^fR;XMMLR!`]Pl#$JeeF]%4D[dS0F+;3ba(go
%Ed(*+UkW*`_as#GE`ETjh*G"Q<K]iPW>b3Q#TGW'hT-@T)BfEf@"`'A#h9@D_l/?&4bO#F<^A4_biETs;Zko2nX5MX1@qK=&,7]D
%qUF0LHJ\k;i`./)#1<KWc?u;pHppq2;g+^@<E5=S1gum3dEehHeZiFdU8q$=WlCIqZ-Xja&X>E"Sf7=mTDciee;_$Rd]O?#QC1NF
%R<EP8Os'j,CF8?LCm%+fK:[hO41atPY<@,U/dIoWn_:q[JG+V43gr>KCB5)lT(rGk?M7a>i2ao##.^%X[UBI^N`RbS:RugT^]Xih
%lik8/SV<sVXQO+ZT966H1(e@G6'j[Um<uqU8%^dWPrM"+!/`kQ)!!D5:*3HP:>)]<A&%a&%4'8JdIPmfj$GQ[opmCRq`nppP9/!>
%I3f379-EFii,Q5hCQbbH'GR*052ou6K:Sf.1R>'HcEBuAL-.%^j/=>F*pI80kd,O&Atul&P@(/:Fi\$(8Rf!@E4HaW\<^&gX'u\O
%5q9@[MsGD1J!hU`:G-(Z5`NDJ:G72\2,:4.'ApePBafO3E9@F@q(1)]>e(i?N9`9]KNDRsinD#[3l=t:!Q6Cc?"B6\78L<"bnnQS
%)CRGL:(Gb3/G545eDr(\>$iZtCUcs",g.*ddbuZ]b>AX=9#0@PRSjth(F[-d:eiBh,]I2,SGK\eJFZl/o<\5Z6aX.Xm4GYJ,4P<:
%[(/^s;K^Gp0+\&6)OFgOcRfh@njU`/WlDf2&D$aLA:A!5E/ddLmeH[W^\*rWj-P8]s+3,2JlL@<VL(USqr+?OTXuYKZn+gUQ1Y_1
%G7"X=;deHC5]<j]/%AN>lGoQN*q2%@]me2Wq0JaAEaQ3OGHPphm2#l$G>;EdX"i&6E\cZf".Umk>XLKKr7'n68ietfCsM".H_Hn[
%"-4a1PC/1-cLW@[HPs=PL$\10lj;;`C37'1mh*2kL[a\`]gkoViD/&"FU7bk&W`cB#C]9JK?;<tchrCVYG]BS[@c^/O@m>)ScUDr
%I%jN37"?#"TiKi]V']H:Zm&9[PgF*?iOWKZ`sHM$<P\ToiU'bD3]>)-`Qtg_FHE\9OTBs$s*d2JEXo@e=B\3dkpUi*kEM!J6H?H)
%k.!tFBF,\t#?$WN?T9^%4,sH)=q6J=2eA,V(M[b2*TS7&,J[)*_B-)LUWO<repkOTQb]fh5Hk)=0jUW&_Ec)u9#G".`T2tg`/][l
%ijR'de\a/a_j\8o$Mkt>Tq)s=@m8l]f38'Ekhd2"?N6k'][?+MH/n&LZcP,9#k(mJ/D4Jjm"bEVs*[ase/2fPRK6/49q#8,`'21)
%-M8qcknY'.+.^O1i$j3pqnN`*KG<l]Yr8`h:FWJOLHk#pV^Dt$HSta&,#^<prT!Ok:XfN+_dAV*2cB9?DP!Z_=j@aBp,pMl/"*8j
%[p8mn_dgDQ@m&SUZ1+0FoH7I]D<$YPYfi98D<&;kWV:<(hZ7Aoe?@VkYHGYQKFg;-rWmTa<S3R]>s8%h12g=NT=C@0@j,:(o%o'P
%>G"C;fh:,`Q`<sAMZ7CYe7Z%SI5E.0=3dUuE?K=iR>0.U%Z9A_FI1VrZ'fkjD&jYKrc7+9G,s7>7OSp;-4U*D8-5('pq27!k!>+8
%AW+c&h*<4T;(f];H]>V/,OYLEB7I2-j)mBM'oP\]h^'WJ-ug8t?&mljS6Kk2$8n,:+a',G\^bM;^g+dW`%,Eo"S,j;fJWU[bk:Rd
%+IfRLXP%O7'TY\tN:kJ&`C3pu4Z3KW2uqrdSro'adue,8/WZ?-n3E*uk>c+[bpumJ%dk`!fL65(LsLVsAM65;,'5(GDAf;"=7J>P
%,)6kA7;jP]Ob/GPlilDnctce`lN6Ka0H\HH]*.s]SL/f`h!&brc78)OPRh?bG(V<I7B=62Qd8@\E"]2GeeJ@(Z]gM,aI3sr'M>kN
%TlpU<Zk/do@ahh\CYXLrM+\?pQ9N-p\Ia_&AZskLptB9Sg'U.?hqmi&R,B7Z$rLYn3CC`q)kQ[<'Q1@R_j@=jMhj565-:M9XRh@,
%2H-1#a3T1k00=KP06ZHi.s:r=F`A(3s5BF5P'TYl[59C;b*7]1)brA,qA#r+^Od^RnFG%4;PI`;+r,c#3D$4Pq?\@nS;Z>K3/POJ
%$6qFs."E$+g.:C95Y"C9T.GXmK,MR)kaHMq..>NYpLaknUu*a%ePZsJ7C(kdmT-?Q!HaT*"DTJh;Ba:V@LVk7&8:<9QrCP5YY],B
%eFK>b2>1dCa;W02mS4\,GK8OI9+j!gk4W`Q]C.quX(`U\l-H=!Yf?Tj^6&)#^:6WG]qQgk]BelVZ*oP'Q/)Y588=8bnaRj?QX;;7
%'O+f78uXfWW89>1=6D-`fPue46J(76'A7BBPB/aQ4d9fSGtNMuq5/8GX^+=M8e6C_<;K-:Pq6MM"YXNQ(HJ5K,V]`5\UL6sBr*4H
%qnP6c&iN=qFus--:/f<CDEKS1\s<M<'o92A8M6hU;P6JD4RoVkTp.(,@TOAYLq/Nn>]lDo%)oE/Zr5%B%LWc@U"W/r;2&e!;Q74N
%C9jIWK-b1K$)P.667GNb[bCJfqgE*9hjLPLb?erLpi/p]^W2?KhaeL>82NVNLj<_Ue;,X3[r]Eq+A(J_C9I/m"EhY6"M+48]cr5o
%oM)Lc>:/X+P+Kg-#]lHVEEUhG^6eToWlH5RH@EV$0mU3<s!,p%b9PQ5`c68roVP2Wca"ogUk5I6bgVSGQ4d&_qs?rH[Rg!Jp*7a!
%O`r!$l51P<5'3+KkX'Xd,l;N\YNDC#/\tYc;a3g!KDE_UMlC9Ygi2W$YoDG,7jcuQd8"XTobKLL[k@0n@UG-`L3@/^G3![&0%1MP
%pZ:3m;WHHu5#i]31]PlbEg_l,+e,U_:sQ^p87$@8^-YmBkp8lA$;/"sALh]`9A`Jnq%i3BTt`BJSY`ISWNUCjpS=eUS\Yj!\C[N)
%\gdIC4-f9'l[O,Bg=I8Co@:@,:!XSB50'H06h8V;".]RE1)lB65nKju,*VS61<u18BTXo<;^Y<HK/@/d8ME&c2@+'1h'<O%U2n8E
%'LHR[jk;s[9T[;M,no!(-T"e1neXE;F=!4!?4rs?P;6gI!0X%0)X#UWVZft5rjpoC9s;gjCM_k]4FO[^\njuEi<RuV_tK:MmA1G"
%,SbqMpFd8kmlH'$.E5C)EI8IfE)kub,;'@Li"t!Yk/P="/%qLtL`56^9_'QFrZnL`:/E:9H\shT;uL"_&u?2\\;.a^mA)0=S*7gO
%jkir)obYNqHc+0JPQ!/^rfL"U,#57#p"t5h^BZ#71HmY*cs@@#AK&AU:80N(1VbjPQWYX;CmtQ=Vc?(!bCYc\%p>\Q>Ome#ce(l"
%BUiPo&E:-%nV/'UYf@&n3<5l]?L@XGrI3WV\%OoknW2JKfCglIG]gKEM&e%3)*K88T]U1F90B/X1FL>P0q8Afik`2s4F!H,Bu4/4
%#@W\Nb,ueB#B`gX<:Y'"lNLrnY1&ka1Mebtj3-7@V]F!)n_#gOk;);[fYqpS_<ikhQ>gf=)dMjg/sg9W8`R@F`,k;#H7b+(m.$Wp
%]A0f`4aUfk:Y:eXGJC<,o(?>&\l3$ShOW$bqC]Ie6g92o#:9&U@s?eE,<osN:-3J6,u;KYfm:U1s3/c`&6eUq!ouKlHulH#L9RR>
%L6NVT,r`DAR0(+mg#2CLAb8EVVqp=&d$"Rr9@`eZFt7Y/ZF;TE`N@@._EIAcU\^Lo"S!7*[?#F!Z6ne'':qd9*XR(Mi^9>:o3CcS
%G"Hh=VYfYE/:Pk)qikC[,,)*\jHF,/j:KE:f^5[EG8L@E48EFqh-W+h;/A$GYJmuO!h>-ejAM?H-`7(]X,OA`mcLno\&d>u6&7>_
%^J(^ng:`i2>;%jDo)__G/OVZn1+%0RS]$>-/J1pj)[nSA'B_F,GqX>RTr'l=1RW!P]5d;BXoc:VQW=0!-CraTR?F#_8XjT6X($MP
%pAor3XIj:(5["HqoA-Fi)c`6h9<PB4%^`p:E3L)QlctCHp#6;I+/[S_=h_?qj]5E=AL5&G[map&.&e9]B!p>&SeGq^N(WU<3n;em
%Q+Cp0>duS^2OcCmE[Pfto50_M)&9YsYW6!IF@0Iu93S*qk8"#Z5A=FMFILtf3k%KC4&<1Dk&m:8*1,F\k'!#Qk-"X2.L%Xmp^78_
%DSmj22lY*e8MkFV)U-gBUn5Ml44:WuQ+"=7<O4(-MA,>V1`g,,D;9KL>&/7E4a*"#*=\@s-1*#9CW`/tf1V?)qC_(8=%&^(J33WN
%GF7qAfm%l.JT!E#H,Ofl3,8bd/HiTDDN#9ohA.Ope$0H+`e9_gn@G%uo0<@6);FQf^7.LY4t:Okh1g.j*imt"LC[)7"sVKdakK!C
%FW9-RI7Nk:O&+Sd\`L"?Dd2'mpu(4MP#Ch1oPN2tHLpk7<dpUoYB0T?F8HOU(F@Vu(Rs+/kamqj33re9^3?AXr02kmA469IGsur@
%>3X"&ZFZ&JZ]*u?O.FMS/+OCL[S#r#UlqajPM'c?fBb^i=cHch=cJ6AXS[:O<V7nAQ>KY`7uhFe6#IK2k<_iGd:(>,W7RlD8bj2>
%VOOeN?(.?cb/]&cZDB*83uPGePYp16INH3KrFc7d?Aa1D7?V^/p'+/![adf7$FU>J78SY&&1gou9VMZ5;?'*N8ski,&2dj8/F@hO
%.3r>H$9-RnCi&b+*Mcr_Z\S$*)5l8T@b8<Hs+cM>4H-=Wh<K?C;s%mT?(e8*VA\K>OPE%[Jm5e>[R7a/lbE,hA<!\AW+s#S<b&+_
%SJ`qV7`Ld?.iZs]c?`VrRW(P;I"GY:h6NlC>\8p9"[ienOJ7?PQ6/f(:":e)RZCDK(UnSXUL8WYTmVqEWY%duTi?sSEg!2"0028M
%2.t`!_287'L)s4QaM<]BHsN;G,?;sii'''?*9\Us$<,7=C002krV!Jd_K'(#mX1#(?7%j`1hc\*Sso2nI$)X_BKV\I)7f`_=In6]
%E#>2p+8,!(f)OXqpmUi47D3rGLpahB[HDDs^@PRhDu016eTt?$B.Gn?^5,ArlaHeB%bOnGC:RXF[)ZgGGt-p6Qn@R/FaZ7hUF\kT
%C<W#lV]*1FL8k<dbSn2P<Iq.V2(jlOWb0O:_j>#SCrj,oSDe(GZqIS<pF]-.D_jE\%Sng(MY:+N^EW)@UrI.]nEC9g6dq)Z`ji+^
%pK/D$Q4p957\hY6;g[+!Y0g%(EdA!lr$):K^ZHX<CWerV'Sil!<).[$c;ib8AT<b,5_^RD"WOa%Nk[E#LM$A//0LB'7'hUrKLs!K
%$LGS:jDN4iVn&W]^N:$NIMA!;Re<s/1lD'L2>;2K`.f3-eDR8#W7A,!(+dL[c+B:6>'!ZD=cc=0+P0'*=fltMePrL068]Wq-L3;(
%6T$!$?>D;06]Ga%bp[0mF#7R%7a9J^B#\7o<.f5"Zn$)2:&,_u%``Id&N*_=BfDC=:,YVr5CS/HeVKo=$(@/?5'0P3?`2>LdV57e
%)5uXWk%TjWT&ZX/#cc"_$[dD8G]P:Kkk+d1-\S+Ug07I(\lO!7!c(a!;jieR:,hXe&`<_tK,Ve<D3sou[",8R5u@/<:S"@9csl.\
%h3RkQQW\.j,'3b6S@26=?2h8@e"+dN4J?NAYmc#SbU8Fn!37p;Y64)>+iTtrpd-VJjK9+*.YcJEg1["8f^'sDY[Va'*_al_/X$7;
%1MN9_EI9<3fM.@X`"1Cp3A-jcS*l^.?Dg8Z1aAVQ)a5RLFO9'jeW`!RkaBZMU4\n>?e1Q%mTeq"9l=37.UDk[7X5D*i;-H8p3F8?
%l6&t0@mmJbD4e;)d`iMlIhhcVs'di<Z=VnLhJP"q>P-&Vjg)kB-q\)9ZfX0\VW?B7Pm*&2Y]\^=%ejVj]UE?Z6Jrq6ZT_>t-1'38
%ACcOYX(5pjJAh;'H.@/qaQ]q>,BJate^AV5T*oYb5/dP<fd:RhVKOt<$E+\9j(2)2;PWY.3nm'sp9d*MVL4XRBB@h9ocnJ;3cjC<
%G9n$I>"NqABuWam3@^@u[l!^-a>;MHN0!!G3jGNTMapgr71r:QLtOe#G!sTTF+Dl/>13s,,;t],Mlj?W;s+qMd;b;HE#h#eZ,g(-
%RRKb9Q5kSTlYbLS`n!&U[9[nlOCWC9Hb;a2>%gF4DPW`KM6(0/V%8E(^pG99Kif!L;=le^6,Uuf[$^>C:8s%,@gHsemGP/uZ($4p
%B>M>mNSZ,6`QJO5OY$gFM*Wgqlpj&3(a%<SHL)b5;iI2CcD9!2Uose:beBn2)$Mq>("'r%PLuJt`:]QecukP3ekmpOaHY^r#A:c-
%8Qjg5An:4Q@HkCYF1lU6]qJ``Vr)sgV(J-aP?LEg0jT]j$2.O#gYBYjDQ7n.lh0tqp<mT%A%25-P1o-gR.]2()\8G%1(*6XY_sRI
%._70d8]GQsfliZ5bC=GunS=-]:JA6T$kQdZ!opoiNm,W)rV4bI^:/ep4Z).b^poQ)Bc1n6nJ5%KeJr?Z7p9.E!3S^m88Q$)Wto'9
%n4A=/.^X)q[8ta\:TV)60In!%p21E+?)cU)OW2AQ:"$NXJ;ZO[JKc0153!tK)P?(cA+^,4-`DnTHB^LN&SKF6)FreXKd$d`70f=g
%j_oTG&VJ]EUo5/Cgl.u)eFgL'X#j)[9$Cl%.',k.Bc9aBNeJX*X4^4l=)$j[AMrX'DdRB\-M52"k4NDn/HpqQeA7kd0,>nn(/_:S
%]/$9_ga5f2q&3kL0"g$Zh(lY##F!`*nZb.sE-/U?p,C%6H8@W8@qA0;m.I0'@UOm.7l%j8Qs!].=7<i8lb:QU].V&h#W7X3/m!7%
%8d:0\%r@XW7Qk3Nbt_\rl!aHoB$D5sZ::^m3Et)0&rS;PZP]!32'),Hb?AQ$3c4Ft;;M\e/.ZOfYX+UEQE3N(XN@c]b(rKJpha'5
%>LPjXEnO)Y]$5cU5fQc?q+<9@NW"#!qMM3HMPp9-Vtl4Jq22*GMPp9-Vtl4Jq22*GMPp9-Vtl4Jq22*GMPp8Z?XN=[?h#s[nMHkF
%j&\@f=$4`/4O"mkRaVb<k"\;Re<m*@=U(a8%oosUeSL@*=aV6rD/Eum;hWDSYu-aFibT<C,F17:QIOVH0K4m#iRM$6GGE6K-kktV
%J&D*sBZVIkG*7^Jim,n!Gaon7ApJ0jk-XT_K&sF;6KlWPL$B\PRbRfc2hC8-T6e7b&aYNa3b6hP_+C_/^1$9\?8n)b)=E,`+Cocc
%04sT]X7Gb3%$(Y9!n$ob@eI&8aQ=K:a3"#[0.F`UbF:VK(!nTr&AoE2>%H3Uo@qUCQRt?+WuEp4Dlp\V\mrA?m2c5Bllin-IZ1,^
%JLhpW#EjK/F2m@kWZ3s/SYt01:P?Bks6be\A`oX5PD\f[%n'rn'd@GKK(8<":]G3iGC5COg5>T-gGnGtX+ale[!`E7h/!Bt45R3!
%0)e:`\/\P+D\PDo-c%g"eSr5[k.IqRb>+dYB&r(lgKet9c1Z#n5;%1K#-gP%b99Bar8iLAhYHL709etoD%p_YS$J;TAm2oI=ohrE
%Em)a8muV;\DILhgQJG=c410saFAM]`9]$kh54KHEIPeA!)S.R5!cL<llrGJ-L7I")ZadYQELXdf^ZMn)_n$DVDq)=FG)6L[X(b)q
%fKfi#Kc"&n[)M8b4L2M9mg7j]3I#n'nD7NN`mlZY)_]Wu3o!(IY0$eX\jS)Uo0;W9(oJRGdI/`D<OliX+KsUMR4SVYr8dN+/.`?j
%`8-,1MtJkufbUmSgfsc)><]dPppJ,F21G?>57ptE0<uGefPPBM>Oh\DdD[V`nHr]@(h;"NpQPUEk&sh5nUri%`3Itb+$Wrdh6>#k
%^<H/^8H-5ba;+?h.@"\@dX=m4>fH[a/^<JOgD^j+]Zg2cc2-9#YIg/!GB869>tmK>iNU&4iP,6YMO:dkn:V>Ia?(AbDfDpSn.X)i
%?sN02_dU[l^QT2b]HtI&U3NQ9G;q-!m#AA1aaA<iiNRf]bL1]3<ctA2cKt&dq]"t<]jZ'Hb/`APE]*akrAijZKM50COm,rOl%D_p
%m%Di%nEF/1q@5t8G/M+plRo`&<?GoI)6lA8emUL'NGIVON*[U<m(>Tf:FPo?OrXnr0>E-Q?fKe+kk6=>ID5GiNHJSPhRCT#)?%9=
%_,"`h]&RdXSMt,\C2=^Q!kl02cYmgqF,@LTc,K+1=^pfcV-GTp>::r`&ugA_oEUVuF<fAmjnS<H26J/-5-lg.k4g--$iK1QYJF4&
%.CS2qkQZo]L^PKK_]g)'76/-hbq*FI48:kj@=:B8:=KBq\@a7Q/atM'nB\sn[tK8_0KEQeKD>EdihIJ0jfg4_KcB:b9[PV9@N.V<
%N2*UdpP^augm#LUX]Np!PluG!b8ih/+t@).->#Y)Qd97//)?-EfILuIMVW<V4M^f,GB6Mjnc>l9?+/>ML%"p:OsMdZgL$a=UrJsp
%Cn<O,HH'0!5S(taI8'+'+MkU[n7!7/[(tOu&[.2YanrMfjiqGh,7/jl@Ur,`s*'eqoAU:5&quR7?,sQn@L%mpdHrje`H=$4Vlt09
%i6SN_DsMuAP#B70pRX^Hl=.U(]YU&XUJ$)S!>lSR:sq0OT3)-'romp]l5,kW<'P_AUAH.@'&@3ELsM\R,>c3hbm66!q%H*&glqLO
%.)5`F]=1"Ss6B!,=)WTgjN3o'HiD4K7mHqSrN5VINpja[p0W:Jr54;Vn)$<Xo_kDrr6pZ'lc/t_Z+E$S1ALO7*^BK2J+7QueZ]"3
%s8(XBgV3gW:H.P#-TD(p:[[?apr'@cnbCLqL6G7phWV^hY5r5YVjh<eo(d"b/mP7%LRp*_Jq(JQL4=r!;Xjf>*5#/O#TE2*L4;]5
%]%Ve[[$#0s!V^j-<Ms2El8QJ,%W'VMr8b-VX?dBTdrT/kJ!t-[;l:P8c9(5f`NuP$62KE1pAPcFTt.;^?mV&MWUhTFWd(tSFgM;[
%]_@B@85E`05oUg9pp>Q:\UBZWFK+HVQ25]nmKqHcBUblGI+i"([P)f`0(l<8T'2Z@""^56[nZat.._>G\42g22GF_YB#)Sg]H^uL
%i;%*1HNSVS0(j#bI*afHpSUhng,@cY`gF1bkpg!(febkUd.H4CLO&jEj0MH\X\X6>qF]Y(A:7XM08!T::Nt1o[$8"I]7]NZs3L/T
%rObmtl2c3(mS5h:1R2f?fB?]aIYZ^;a"LD&:3l[UO'dE<IIE[%-MjHD;a&arURpB-T3?>JqM6uaT&s/Q[]2W#G%oHINBZWiVXr:%
%;qes.L,6)Q7rb7V]/8f`7c1j9YUu3Y[d!QD_\Uub4m:'97QoAeS#-;WoiSXsLSqko]PVuAQVEEbY0f1&".O(GB>EKUXEA0k#CY:D
%W95(Idko-+e[J9%*f_j4_m_^>d$IIcHEPZI:&FXkJ-*/r\j#MlD?kQ`0/rj.k8JctFFB$sc#(qk.@OtZ9b7*+M,DSVqI[No=f2nA
%F5<ICjQ^ok#CSV:URl8n7FQbXX2g9M,sipaVFqNm6)&#e!hq(5s->V7B4-HYUs<cJ;8okYXXo=_q=eGh;O:?<md=d]TT/G6\F?jA
%'isNW,Q_3@V6DZ'EU+b[fj9u4nI8U:$a'N][806Q7G%'<&+:4C3LJ1?.hP([aeA3D"m9-P)E^Q(Gf'um"lMuBL5@AK6AV:+_8Ah9
%:ID(PHWqu,-%4u2^.j9><'<,%i%4SqZ:*,.jB()p_i)Kr$#f(7n(\!.N[jB]A[r"ZTk*R$1`JdP>T%pr9-50=#rR6<PP$M9U]UuD
%&Th*AH@F%6<2)X%XOMdpj7,[l$L:/rJ0'ImKBbOZPJ]m_(Wd^_`c'8daf7^mn9([-HpsB4U2/gRKr=`Yp\eIOro6p"7ikTr$;dmJ
%qtm8sU1_&u0JTbj:21/Y4i/+eXo&H(F:hI1'1nhQeGe0(*j!ZJY^m/@--/-io&(>ejVm@ep.@'*#7WcG"G+<uYKJ<iQC(4Ve>5e'
%[t=C/24nutJcBt4Z$Y63*j7ci()stW*;%02$0k)L`Wj#t/i)8[U+<.O7^&#I[c!nl?2'J<`TJ@,!P6!TTAS$^qi8)jdVDO/Srm#L
%4n.l^d"<QL%.o?6'3RPmmg'Ms&Rm.[M:fM*f&1#VYl]V3+eO4\])s9qZt@cKJ0*[AdM(uJ/5CI)r<u"B6&-\OTQB%1m]re:P>VGR
%d(?7%_8AZ)Ze_*Ck'BQhH_u_9-<c3bgrMX4'ndtKk#1J2KRjs!kQcK!Op'30+=L[JRgS\VMW^;7p0_6Qg/h)Y$tL5t3#%6$5ZEPn
%"oBpQ&nY!tR@.-1\S/h16:?AmjufMY2$[5[c84;.0d6X8?]FA;e;2P(a#KZFLA!(Qr159m2d'd2j;^_Egc^`ii:"fbJ=`=]:gsX#
%98j0E7YTLQ`J'\:5+*Yc>bUmCHTK-Gc'=H'HdO)H0e"i'kHtgSShR!'FC`t\=%:er/B6nd*!WP9*fBmMi2M_>_MX+T/UGXFr.?fB
%4;lda07h0k_<07n_g9.i.eKAEl^k-'h3s!fGLmE]AZ%=VFCNHZ06iR70kEB^J=&K@FWeP8,ASL>L)]<Rc94"NC6b"%V(NDl,$9p?
%=ACSq1#VQP#<8,;)<(,Yh:D+^lcuM*.=@LE(>2Bj%#Ib:LSLNd#O%RE,</f`L$1CF#U3S_,)@kY02\d7R\>";?RK^<AR[iB=G:2*
%6[B/,%^DCDF$`#MlLS^DE)[j9'0?WD0spC8s7A=0]dVAGKa9`=&8A,8<,E#rM:$$da2n3gUJpcjEXRN:ne=W7H-kM=e``f=!lW5E
%afJeV0e9Ne@;o+m-AO59X>\]354>P.h3]a'l3XeXN]I40/gS:^19h'j5<qmdetFu%#MVgGFCJo7\4<m%i(7:]cp:(eUW$&]BOV_.
%U!^Q7B"N"NVS5PB6p`Gu$aGT9f9edo<*JLGKbp8l(rn2;R'i*r]OM]t&YEfP@q@Qt^Z1dV2nFr^YFtrS4T5Y$rhZN;6OX:r<?CJ"
%"CaafE:\6H!PLMGVY#7LeReiB(1bBPYHTk'8oD^mM%e>m_o.N3RFsAK.ONT[9&.^gWe)qQd=T;Y1oN*j(*2p<&'C3h&5l?!0;A_L
%<:HRF/.LN<a5Nab!smgWKQSc_jAhS&J\qaI*fJhKLr8>ApgMNV$6Lgll?S`GRYHUR$WR4]pYii':-$9=q1B/1o?hsu>@d-@P_i/D
%$D^a8auF[#,XNX-^<3[0C1O&3[]b3666=QboC?j0%3C<spBf@-]Fa=hZ9H)#9rp%qgbZ4T,$KZ)*gD5$D`_R:`p[Y8Wi?KNl'CoJ
%;^UY".M&O+U8lXoK.3.S)BR)#'kJb?'3*)V`0s*eb9cal,C5A=QnhC^'/CIBA"(3IJe/MY_5mtZ8(SZZ*_9fW03gkR0p8$DnRj%>
%Fe4the:8/L#o?kMPpb)UfX$N$**2<9G8sA(DSZC-'2b>3^8hIuA3Z06`E1G&4c5&Xjh;-qRg:5]5DV!1CJ@/=lLln[C/o#!_8mh"
%FqHLg=%>(!A20^XL.#bqclHe=dTl?LPc3_k'ToEg/q#.N.o1hejm9pSSHtUSfbP)Weg5/nD@6nP[XYdH@lbGuY<d=C*Th9>l7O]H
%(BK1FeLQ/';%I*h.PGq/cfI`[=%h#e!d)X?`cTEf-4c=;M3O"b9@FgSFG+jXhLl!(_qPBER$^(`WU(=tQ>A*GK_&-tCU@NP#4d;k
%>IYThc0d+^H.m$[dh)ri87qHP9#mTFIdrA[iY,M:WH14s1@W_/V+@5VFl)WtG]G#n_)43UW`P7aC:1?T&o.SI"45a!Je'eC1GF]s
%bBT0kbXC1H9Z/1;L"6g8]'eKmj+e$>Vg+ZWp!)qU:(%no!lC_`YSj(b2KU(>Yp@pl$A^`b!RGb*;j!gOK\]#>VHRERoCpDVE_Jf4
%#"smuYX&AI;2<HO14X,/kg`@>XTE8JR4#u,l_)(p.^@nTbJXf^nV)-Gp>D?6Y!+1uC-)fgMa(QoCE!h08'rO=2q#1m.+d"p1_qg5
%W(Xh/Crb<Go=>QS0O-Y\kr2_,=[1\d^7fV@#RN5>/s\9PK#[FK#-q0(pcBCaP10Jh9,&QUb4<h'9YSji_n8<0VZj[/[_K)4!P9m!
%WJ:Vl2bG`:Z_E0!Q5^Gb[<lRiPgP9R=,WFSaL`g`NVZ.:9IJ9hf0C)h+p-Z$6Hl#02Ge7h4_bF+V0I6P$pn2NYYd,XYt^77+CliG
%<`=5nT*mt]HCOu)ObXq`9J#Ed3c8rI]sDRd)4t=\6PUo0kQ[74@iZ>jfGY*Cn/GLD=mbmT'Ap$'/!;6,NoV-+K%?nn#3oetkc&\^
%<r!.+7b54S7E`]:HY+IYZ;s>4(&R--HAXU^q-hcF]%5eZ_%<lG'mnG\1I]Rq9Tl;G3K(mjXAaK&AfU1(7o^MX#*kW])icCE_,\Bl
%X=FO]<dU.E6#b%7`)mk=H7+P*YfVk60)@7Q[*V6^YMOIH`g/+gS*i/.PF\6@<^l"97Fqp0/:u;)+,WVWa:t][V0b"=Tbe#[%26j8
%p4Od/TeOBtotq?U)5#O0d)#,bZ+dcR\r'XuEH/>)L/X>"fUBEEn7FV5-TaNf63lEB+tbcOQWUGNT^4IsoM$[L3Y5<>c:alJ=/6@S
%1pZ9s7'VY?)3#a?)c*iC/MosVBacn@^Y+%o5MK:N?4S=B9J@<E>LFmrX7Cs[43,LeOU`r1Z*&+lq_DtRSZNH4+;Vj,R>$>cSna\F
%lFU#RS&]8_BL3LcE3<)+Fie\6!O["e[DkiW'WtoZ'RUb)%BcDieG3:I;W+:3Xjm\@E4k8Z+-atf[2GN\&J;>`A%CtkO654K_a2:&
%-A&*c`5jLpn+C*%^Y*@U65:.A%icMe7Eibd@hBn:!LrQ5R<A>aZ)[.oB:"5&@aG]j8:a8`>+:LK`S'LuGZ'_+Tjs'f"DEPeo9Hdk
%n8,JQZO[Hjo/HLX;JOjufTL9e%WfTW?08Y^6PRfQkFTi6fU5"2$boQbjl&aG&Sjp4m?4=;q2fi+bUuKX!C,Q)iQNtbq@,7K-kS`u
%Z6fc;alKhi\#VM+HX=GWDl.rJF/NT,6F*S&*[,;bkd5hb9u?Ef)^(WIK?Uaq,!.Kja_2Dpo<NmPd"gl$B$oKDf*CCi0aiZkpmF:/
%k/)3VPa@T>6Ii)OLPT(k$gE*n["]?"OW(f50S&IZI^R61al<pqN@4!@BJOpSAUJ_GI7O=B-%Y>(Xfsj=MkRh`O_kR?al5:uLKULV
%STV1bCr,I5>YQJ`%GNDf=OHBp%'bSrMp?2IT_G7j1Z3>K#SZ6+U+^>`6(:qmPHC]Od9'lp4!;1MC?".8cE!K]\0g4,\Rs2BAC*k.
%l@d4_>1@:JCSYX3&g8;bK[G5`#rUB:M+rhV.C"Gaa$>ZAQ<>"[>7npqU+t<k"KeWl(_dbZR7L"W<1,f#MKF;DCm$N'_q8%QZ\/%2
%"8^5D","Jq#KHV%E`6+JHlct>E^P,3g+bT(;7Z2Y>XkblkAf]J;bRVd\Nrem`tA&_QR?i2f+cr>MAhlgr4k]]rl"BgeY&iF*ljGd
%$al=Y?Pn1oqJ]Cs&!acH#:Q`R0a,m[RiPO/Gk;ucVN7%?B!*CU"<]%hNc1@"l[^QlI08K>6(tLI_cCV^3KArpHjm&tWIe?qE`uBN
%7FA'UC.OAlXtDK>0achF@Y0LdGmFYuA-F1%]GLm;C4Y#b*$Q,(X!%$e\-*@QfUN"2[=864"i`nUQ3gnHbDkkiU(0[V.<>+D,8!WO
%LEst<P)oX=8"6\Wi!s:L;kfn@J$dZSlGJEg(D012/(Q8u'U4^3XGXoRnr63mYc[$GU+FTrFN\TRQ4"R:2I`=F"Qa\<iRPLf?tGbf
%6>f]f6aCH@PB(U3j;Y!*g\R!a(*l#?.W9ink"n4R][r8Kf&+NPdh+6T&bV=P^9H%#MsAE"dE>bheu)r#JgaTnm[ArYf7lH,,W-s2
%V,UiF\cp19,sl%e9go!%Z-pQnKlgP"\clKtGHW7EHBYeY_.GchNX2oh0LPoM2`'QuW]"diSn6Le507sf3Fp+Uj7F#q?6:N8YOkjp
%%F9P)><K/=5LJV4fFp@<d)!OgRh*XfE?rqCD%>'(9.+:=M1-e3@8B0K)Sp#Ra=4)D;4Z[A)g17j.(C4S#[,2<JLiJbQ/N`S'SI!;
%^WLdBZ!\)C>M/+o@5:iN[<g+<6VdEuaN?6Aq`6_=Of(3ZE2[ak<Sh!"Qf0/nY_/@Ud17tjRBXCRI4-Q/YR>]Pqk<fh6O/P*fS5qJ
%)E^N7p_^83`5,u1'G!%aRC]VNm/"l4X6u?ZPnU;0\,j=7=;SSpj`,Zm-fQ#3Z?jR@Rt0d,Lsme'G-X"jXS,4CZVe-4-99I(.)Y)(
%__e"VE&4hRDsH($/Xq*V>Eb772ZocX%Mk4k?8\:l[QGI`hKU!6dZ]RtDoCII__+Dggb31t'k0$E#XgT;HDJ,,8#Y<l0l59qH(nmf
%23?r7#A9p/M9Uu/:VumO0B8hXU<Fn5(7#\/^_6,Ug@k29c>3S-\ZL[N7?5nFD@=<&fs#*D\n4m3d-;kH2PTr7*m1Uc[CNY.%=;KX
%#b";j=5H]RKa/tN?1$K[EI^)4j)!?_9Bug2,ab?1LjrCsXl_0M(cJ+T@>=q\A4eTtEQ@!jf0Mb=EZBlabP:/\;ulUN785nf1;(kI
%r6Kn)U&Z[g0A%O1&)"OOa+:mF\hC]UASB-;YYuAH4UdLP8Q1<IXUX;:*]b8K!CW)Ca0:KGY$qD\SjB2V&_ID3WF@2_3c*1%=;(_D
%GJhi'fRiN,O;_r^77+j+gnWP@E1^#M9Rq8o;Ge>?ILNg69ft@ue/q"Yc)3OD4Kju47V_qfnY:R)r?'hmqYr:X&3^fm[&B7N2[^'+
%1E@[#EZ.HO(ZZ/[j<s]#KjJ^cZW`/O,=qlk1W&%F3C\U"1r.0j7a;_O&N=-(Oa.7J&o&gt%h]Zn.V7lN*pR(g%YQJ#RmZ\3ef;42
%LmJ]Q'Pu(_ljR8&X,?abd[8Z%.mfDf8u3eNnXp2+`KN_hT'Y94,+t,/9CPt5J>-a?1kBGAV:QNCEPPY6"#X=1(miO12])S4[H*i)
%dnCL@>q-&B+gVm/&c-9pMXoaf_.k4lWdW<7*aL.J09ZJQ+aM0tGu;s$e0hs="DtJD\b9/]7S1Z[_=.3BqE1?%Y"=DB$]5OO*A%JL
%a[;$AdiPr[6k<'/0P#40]=j2LB@huc&0-=\Z#:hBH`+TLg21Jrq(=!sm5]>''A\>ZD;t+OGV<8jg,Rqo=%ESU#-ors['VU&$1XaT
%GosnJ]=/]Q'&aDQ)PX3SXta=7]gY]cqFr].(eH3E)^OPu^+Ef-UP>QKPN^4,!t8FN,;'KN.mGVkE,RUU2N8r^'cSqIXGc'l@PNC\
%Ft2G_N5il2H8RUBdGVF\0qT8S:NR#dTN1.5aZ,Fs=c<,7M.gS\Q;]\\]T%#8nE+D;$gPM7N6uL&ESR=Dm_r#!RKN8W-aB<^&>Yd=
%`6["(I#F^QNY:!:J.G03iTusJU3\MM.9IZ;Hid)'o@W.K52)Z0P&6(ff)Yrh-0[:oZ6^:'=%$udR58@TYYS>B;1jNMJ[bjR'-b&7
%n7W03Nbopt"?N*l_r70k)+-1J0hDG8&4E($#]=KN2J/6IO1UZQ\&g\q]csn)Vki=meqR$;H;e=_=+).A^PZ'*oAtZR8tE4'Z<6Wp
%=.Y?u95ZfO=i1E5/SS[K2X&"780)`e3([-kBp',lLMO1`gF]o]]@`HZ%C3L4`>Mr/>Hm(qEK)gp+4aWNN;/Bt:Vk?Cl%Ou`aeBVe
%;p@m-Nc^e\[,d^C)Fu_)4?]\A1$N#3h2f0:5se6r@f#bV6+9#\#o\Xf&ODd`(IXjGR72p!+<5RJL0QQ3>lS9=,bgof2WU&cQ)^kU
%[5d7l')[85/BGN?b*3p!])GR#m</mF0"dYm!o5dDm&>ZUSc#PbE+=hf'4Ij$+B@JWRKt0hTX1e9:)T1L.HH+enQ9Y/.L?loLO%*D
%H:u'qj"P,B]88t2#7&`[dC^;f_.R7U.rB0f0-?u&;-=/-qc[W2m/b#7/#1>0I21fKRH+b[2[";@RX[\t_R,<aI*F^E(t]CgHM.Jd
%M"1[LCXSC(LJ`:JbigLE0oLQQTaXUg9ku/5=YkoZ0q>Q[D&s^P:"bqdbL!lB2AkNSWV#]rZ*Bc@%$`&Fn=0Q*\d5'k5u_Y)O*Qmf
%;K-*O.D75VOO^8I5O*V3+&q>u=.r#;nZ\pm*EHDXRQuf$^5='q_Db/OCHBYHNJ5q@=q*t7f:a(&14tH4KYcHpSWAIc>ki!Z151:d
%;KH%ip&`c^<Xh::5\foa9`m<CBGn<KS7U9I>@8ING%ZZKP[,\`0W<MA;3o,Yl-4tGf]0/'Do?X3>"$a`m\!dOQR/pNYm92kE6q_q
%Mm_7J$]G<u:pj,i'fbOe);44l>>9&8IAB]<*-k_`FV=$X:fX"$TnND=l+1:LB]9=K<jFH4=DM"d@`'dTN%kNf-m?8SEfaRqV#J<8
%H9>DWM5^:lD.k).%>C^6&*)`Q&$;fb8h3;/%mA4[9>jXYg:L'Q(Si!7;gS%m<2K7qFQ,[)O_Pp/!Vp30O"1g3KGR(/jq&lGML^Lp
%)"%\o'$:'4ef\L1c]l`R(9LZ:Nd\k<Rn)t>4mfor-HK(:&Q9$_(3H`JN@'fr#3eY9j[@<EMAIL>;5=@A&kF,J?Q>NVuZ
%7\5F]i$Ael!QJr[Be$r]X%[hk2?P=u;0&0i;nCVmM=,KrRY9+.4a=-^o4ap,2A-Kud;,F'8`5S2ZR4VBP\QTZCf5.ZdJ''W92pN\
%adtu*h;U+#RjN687`<tW7&1r-H"eD]@$m`#.\[gSR)bd#oNU9^MJ#Nt][Ci6U'7f6"cB4F#@1mr5K3gCP_k9VUF%19>C"2_'i'&V
%o\d\r#(p;8G*?%USt#:=cRPOk"J6U'Ye[n-M]e+PSeFiS>O:cc="F\(R%/_S6M>Z#>A+#>oUoAe'^Vl7REE^KL;<f%231,FG;n;1
%`<Jt`?-h,'Pj(TBda>3X)38P[2kjB=fp4\c,6tJ/;/@_(&m),Bn7A:X>LO7`WXsL-h6`l>UR^Oa)m:5[bi"+i&7#]bNQMo=[TtSp
%..g50:o^g,J1[)<pZR0P#=9*FR7K`53`YA_#Dlo[F\jS;q+`h2&c/@PSM<^^oUMkDFu@`q;SAHg[QJ-HCX`bQn`:0X;'>\=m>99C
%4m@P]HY2T[&-=@C"$Y(;f^(0U7@i@QdoIEajf@)Q';]VnOr4%1miE=:]qT/I1f..Hm(,ppP*oI\A"l.i(2H)NTX&BS>:?'/Maiab
%pq,aT6\IDk>bbf%i1P8t%P?onU9b<7CK*#1\%X@)%i6QbUk[H.k<]ikf<XP5BQ8o)PdA\<!FiBhd5B$OZDe^$%u2L8C@90^'s-+,
%"-1Y5Y@(A]SX?T]I6.N'Dq9cl2sWl;F$*Ch.T,"P3Z:OGa+QmbKIIbDiM#aoetZ\gV$=2R75g&WCE@RU'olgD#\%ZS>:)]K)k^G8
%.'<5[(t0B?'cCW-_k:4-0S]*&63lq?Ht:-Hd4*:q<F\nl0leZrj;l2M2AQD"eXdTi8\UUVet@+E1XWQfZ30/iR,15tFGYd<PuRhu
%9f7a)@9k\"50ELM)1AOOD01_k7q*]N/3:lMW4>>"3JKM'q?MG:OTpH@@c(dt=jY8$@@7trCqc)sf2J+7>c3@s(i5sGa99mB/.\"E
%Jl7=Q+VR6Xb\Y(fI7or8A89JsmU?"p"66T4&Ob:J7*h:J]ndma(.LUc0GjqkL&o=LfaIr+H*RtgIG#fu2T35k=O2fj`l7Bq$5jct
%(G&#K';h0!E7Hl.,2ku\<Qr$^e5^RHXe").!HEp:3^Xtqr#-os;W\)a6mQCb)]kM#bdnW&UT[k&/7De]A"5M^;iRqYdtW^XYmSR3
%8hLMDk[bgHb3MuP@e<ZYW*/b`*Z@0U%RN+#.T,DA,4+YIQ?b;X51u?[=@8J2bnAZ'V>rD0;:IeadhM61fItCud4%)lTL[L<Vc79h
%-RF)#1a<#GMj^7sp!5^Z6,3X@7Zb5l4b4YLakPu71N.:30p1hi6mPEug"*aQ(ST>p\MqoPdO5irVu1_(:&ur+/h4P.W<OQ%eY<jK
%!^hUL_ZA-j8Kfdpi=M?I8%&fq`NjJ\AT+<p7aX+X0@Dc^53\GqUWM$L"H$\nTVO'2liW3.f,.!NT,5A:UR<V.1jVImAi@O!0I:On
%^3$4cW7h+6Gd.e.C"4&k'$/Knp'QP8)QSOQ^1:n0$B!jYR*lZNj"N<@m8^tXgaq[<%`!C8pQJ65:+6]!h?`LuP26GUa(HFs!@@+>
%-*NPuhIj"Ll!5_5?CU^!-/[cU(.f:_I+]>H>N]N:dEC42%Gjj]Rob=t$D)!t&!"HQHIe4oJM;cS)TdX?kJlIQH44:DO;c:)Dlsc0
%EHhUp[gXZ4d:G9s!u=LSar!AUA!n,a9fp(RO*tga_Un:]:Dk`(k?J`]e&]Lc2A(fhTaVV>M'+keo.j%jeeu!>LZrb!,D`j?3G(n1
%BB^OOr\fj-,[1Z*RfT:;A)o7uZO[iP#?N$u/&c,uNGD'I65+hnqT&Zs<RBGC+\$&(!"#1.NTVK(PcSNW#=1%[:Di<V3Cl\W>)(rC
%)W2IG7d3cHB&a^J%^3_ZUb8o1'V9+`@A0In2VE'gli;7SHjmMC!ln21^)lOAKUHC0aqttn24@gp:/VK97IeNr%lR-9=#BGF(YG@^
%Zg+%<`etoV<BMY0@Qe]4-mh\3iY[`qDpd<Gh[lP%-.pF5p^D2-a8qr_"MYWTrpX'W#/W:'>/O-LE*Zop%3Mn@IES.3KS<p[8n!_t
%)M#h@1Q.;&Z?-hEK\V^mcS0!/S2'Pun;;l(EdZ!^AMT*sNhWGJ(Qj:t4NuZ5no1nU'[;8$LcXge`j6i$!M'EG*ou)]oRcWG:u<gl
%*69m@7FnVeZn.f.6lg>E@B^fAP$*T=J-=";'HfAX%%!J;1-"@`+7KIu%LMlBn:nd*S7Hk1."r&K\(oJg/Ab1<0aU4G=:`@A(8V#q
%XinRs8]Vbl%Bj3WPII+"G\K#<d3L1.@M0i7o]b=NFBr@2akSEu^N>>'E]'ct]Q/+gk0,MF\ouAaj4LVH!jfluF?=]/-n1h#al`tu
%\_7oJX:o?ERWMTX]I8N@fo:S_Pr;O_Fu<X73LlfPJ@Qu+TpYKmNMm_)I2]=G%-_8:9t&Y1*DL+J,eHt&o$eOeN'1jCU"_Hj6"N7r
%<ZAWeef*QqHC_CAi,N+eAE<-@9uU$@+&+Me&;eWj!#qC'S6/Js8X*H.>k8kN4E=`$Nc-@nVmn:BBk*:7Y]/C=Ba\Y//HesjR>o\T
%NOSBs'1:a15u@m46@td$!D`e;8S4lVS:@5iF:2Bu.7KH$6oF5(/P`YH8R+^GOH>Tte[+M&;;A]c:)M!tU4lO.BP8a%@'*u=>mrn5
%b]t:eqCEF?j]o2]NlceSjP5$SjoaiM3?BHd+gQDI:lcaB$r]=sb+d2?W0BnPnJad)(7Ade&hPM1%'Gg7U32Kr_Xg;<S(3&gBE]!8
%^<"e$A7U'7+md[BUkTL2<6i*Q2(Pg_MWE1S7:`<m=d7cnQ6H$.iRaBOVm&5.I;%JXA4h%C3_,OIbe^,2A3gmj:g@\q35;WL/Siem
%^S3_:B^&KQU21V<MJ6t6-LYliEA.Qm3".KtP/Z0Pc@Ae?\sNu.JOkZOq_Ec:BG;[9D"pGi71/%ZbDor"Z"uZI'Zo"mWnG/CW$*[E
%)\&+4Wp:K&M2Y'G+G;3^A5GH_'')OXKn&\rI$n;WWL<_2@MI+(Sjmp2m6]#*&&\?HbYsM(]j/ML>m\,1:2+_CG6s4/cZ9j`5%S^H
%?[&-4!Tmgnlr`5;9^iLG>Y%Hibd(3lJ!1WGK8KFcV\Z9WkT]W?+?lUT`S=8G^Ys+o5`qWAmDiN?:2Bua<dRo?J#J\.cm?s0;k)n.
%l%6O@jqLnF"qVnm_Cl=c5X!TQJs+I.e<7S7M/kf_3en#"9H?d>#l:1a!?(;jZ6Nl'DDcr0o![jWdj`iZR`\c;AN$;EO)`?BEKoJR
%e(co&2T`SLr3DQ*g,tCab9_uWK#V^/ot."p_[I<.`]"4EZ-=SgBU$QFWY?^MX3nU6q%;D,[DH@HB(.(i1Ad4Nfn+WP]TOUm5gIXt
%i@5=o7GeAuk1.M&CS.B+d#TfpLG$[Lf^U022N7'?5rq]u:W'da'pD42o32I9iAkt9Tt?$-QITR\MV_7'/E.emYnJD`=oi]Ml%EP.
%aT98Nj.*!i^.hqR7GkNZRV0g&iL]G0_cJ68Mae?PKb'=:l9u/2()M/?$HJq^J5+L0ac9I&k"/dQlk<%,-]8gI,HWf+"a3[nM$Bnr
%gZObHH,H!8R_r*';R#URjK0Yi_RcL%/rO886=:NU&k9X6-KRTr7FFHf&L;p!p1!M)h-NT\K!5k&;7q2$,Qm^WT21bLasg(d]pA@Q
%l<KNhh+S6O/;qLm:J.:hGZ8OX-93J$:FjfrFb'9l7;0X+%[*SFoAUB30)+%o.f1c$2\quc9ZGDH!:^@Cq6#nC<PUu'1A1puR,H`-
%k07R%RrYf30!T0Zg,E4s#tsD/bb_7u6-9tH-G(CUo-Q"nMGj'18rKH#d@q5>SZu;??9#*QM<a(OI)p?i+t+ThjbZ>8qW*pu[Q$<H
%/90(C1M'0l1c_WlnJru#^Ihik]r?K<D8<b`dt>ANU0<H8C\22eS,,Y(?&snDdt>ANU1sK$X7TuPS,/aO.6;P)%/>FgCng60I$HlQ
%]iY5Ig9[Vue+B;/9eab-m8!M`ktV,OBi53eLVpc%'"nH;g^SaHlC+'LB$I_T=)bB9.Z#%an``K9beqjnk_Rk\O1a#%eeFu_%:lK-
%Hl.]TW=^LS5Xb3Xl?o6&K<D5PJ`;KuhH&%VZ%:nj,W&`<q)F0;b;!hUWfg_1p-^EZm@e^E)j5L*7m5JfURpAFX3-t1H.0,;Y;]He
%Vc,aJXq(<*C!<)VpBOIca;Wd0M/.44B@HLGDjltKd@7#\nJBZ_M/.44B@HLGDjpA>X1;ruF/8&tM<f8`B@HLGDjkjNO=Zm`%6-,A
%CD5=`URk3/KDrK*'5`cQ?-kqJ'*^h0pENV%A4pSJOJF+<m*K2COjncidc0I!qJYuL^[6WG+t4q+*,a,i*,a8INBZWiVU8Ajo=!o%
%2Z6A!K3m3N3066B`/'kdc[+PG%XN>*&j,Qj7:SC]V;Y880?SGBSLu'@f.00QRMQIu,Uab:iQb)V\i<"l"^-FbNT&5&F>=)5it@nW
%S1Lg9Ae]&62<c/.B@uVCC!&,")(#&K)C`KLH=9\&^a5F!Yk#J49VeUIinBiehb/Q8mO3[4/W<$\T%17M>:H^'h.aM=fbq?`C+J36
%q6\t[FKnr:Y@eKHMpU(fOtINN>7Q#c?0g0#?.ib*9uki.7(9&8<6Fh[aJE@;fkSg`WKWV9i&[u%O#jt"_P`u1UQ;!_3aXkVa?$N/
%/"p0I]fP[>&O\sA\I693NCmH.-Y%<2E;OA()5;%13Ek3[137@%0tE%(+o8N"nl:2tPPpkX6&DPl-(p6_YqL3E@qj[m$@UCFdGh$e
%&[-PQN[d=GU;OO/Kj)pAU8,nU[4bHL-[Y%4YcEeQ(N>^XMfWMrGL_[5mYAb.fE$g=_^;@*/"<=Q94:[$4BG%!dLh%8fksH)$B((t
%M_LU>@FebAFLM6k]eg/B&k0W!91,R4h`=>F6Bb'iD,+(eH6N@+S=S^H@?6dOMQZQ@[M68#H67Y.=M-e];N!_NaKq\f:)#]NB:=:d
%c9KFp>=V_!oW+=W9\X#XP>h3!)9C#&8N&?D^BiVNL8kU?`hM[T,%;tJMBtI)K,_Lo$mUlKCUd2nXb#W(<NGU"F&"<WX$q&gi4E'F
%O/6jP5Z@:e?1-)ff93^sZ_armQUpXu61H8$OPtI;dA9#.s)@p:!]qR._i)L5E43$kH*o34[)_*Cp:)>.9GIee;(u8u.C^K_-T,`R
%!7pq*J1aqQW,ETpO`QtpSDq#V8^Lk6?cZeOnX<T:*0-TX"K/<R-?8i.mVlT6ODr"Rk;oK,+'BJ]VXYoOQ`Z#-WZolQ8,gL<#55+H
%ePBNK<jNU:#/OgGccgZS_]jd:VWZN8[3c/K"pFGL<<)cK6+kk8=,c4AS"nJ/egK.I4j#2;AI=h='ejX#$%u`,No7EUE8'=JOIk[N
%Z[l]K'BQ686Si=Y,1!Hga[(d&2]6T+#>0koMfDWm?<i[p2(LVn,CF4Spi!U=!+"&WetRM$Y[a:npVIco%@qC:TmFACZ\<T/mti*N
%W.WTe1TZ95mE,4SR-<+H_.M(!D3N+@q48_iBp,KbS;UBjoAlLm,7-/%=CSM6d6:q(^27k]@6$M*ZFAPAZAAEd"Ia6[dgD4Y9-[;S
%kjigf54!QL)nQ`!L0]^Y4&TFb7VmArY'RHbP<dKNZV/Uk\t@B6,`Ht0mY^R*\q#I]!I@4[ah]P$DC/HKc_dkl74@dU:d`.a8oR4<
%/Xkf+-)GF_(OGVecr^mXZI^APqs6q%_c3#u8?N_Tnj_kaHVW0,6#8)J:/k.Mi"NBK'jh_0dk(Kl0&gHr'Gl>`$EBdh=-X(N8QJW3
%'P'Y=7/fr%*>+G%fWZ->=M2h`/+7/TO24"'Pa2*fhX%!2NUq!N9FQ7#3$Sp#ki-EW4D>7a;[G!lV\=JenD*_Jm\IiZ/c6aD1uFP)
%oJI2)U5Q4K3;-l,`'KnHR%L+@1LIqNR:B3uo9Jq2?k'C)S$'?mpX[5m8N7LjB8gV"H##<VFseYgSA\9#$U[Sf`0_Am+;b67(q"#n
%Us/IV(e7B)dFZrir):&9bbj@,Mg3;RhmJ[5--634A`Np5:nr(Q0699j%9_%g&l#Z\\;U(gM+.@lPLF1!\']'9bI(Nb;6q[EdA68C
%.2^Ffp%ehQ%`KO5\,>/.a".3<X^TT>1#0-LSO"p4jHu3J/1jj&>mjNsrY4n^(6'*Z3&ShiZ<7.)D9[-8iMOT)1QlDji=?dkD4ON4
%>8!K*dg,)BeNIVZj9?n5!0icRiZj/Q8Xm\WLWrE3C)1B2Yp#8>\?I9uI,0rZKI29k?A&a6E(e1VctE[#lkNPM#>@TbjpG']QDIjb
%(>(IFX<rd"fi$L&bN@'SM[147%p(\<;s-?;T>K=dD'R&$YJQf[85sF=j@eEq."']HCRVs;F(,g,MYl<GPsZ-2Ise?#8Z11lP9#;s
%J"<bU-76O/LbLdDlNg"O,=K#n\1&n_11Y%RbCWJ5!-O8^EJCG9"rVPM@oFY/#5M<F%kDl)_s`\n`VL-th*@sKh)P\[YQ;5cm8SDN
%An1c7$QZTood8X@R-:#cTK#k)"*ET<8E1&ob*`fmn/8p/Llrrm).!R=J.-cTR_45AG_F*AKm.S:G:"ZUZ;*Y1c/^@p]nGh-D#sth
%FUTe<F9l1nSTNbE/Sn#E6f<fu$bNhoB,M#@E`-jiIo#F&)N9RfS.\(dY"B+Wh@_O8'7g`EC6["RgYe=FT&68%NAO]N5oG3S%?l'G
%r239q:I9oN;0q@u&\):Kd)meVM<?^-`OSeb%Bn_u.%Z:0A;6kuCNSg_@$4Z2+mbqd9.(1sf_&-Q:'";\iTrGg]f94h,AkST5'LUV
%nJ[sE1--:5]?^83X&1ZIY,WE4A_d-\k8JbiU5b,\bGGqWJN$:6qQ^F[I!OaHApW(3ZGOV@g',/m')7EG'].68.im]bs37_qUX8`F
%5An6L]`%!frk!+KjlarSI!GJdX\b'3J,eg$P5bDe]m]eUc-($CiX[*BRfak1If1Y-S]'@Z)b9#AJ&[!:Dtr^,JbH"2FRt]cJ+tC8
%o*p*2f=uiR,rjYNjr=/(ke-DKb8?6LbL">V<oi=%hpf!CMI#5?i9fabceo$8jHu=Y;2uTW'+lWhE%a:H3s#g)Jc6DW###3c&c.@!
%i6M;oF6ZOe"<]*3cu`h`#^=%.:>J]f6@UeR/=8hA"Vi:,(-KZhf[r05dM`1!jPlU7I`t2B)Yi9][9@fEg+nU5clEA,i4[G"G37ou
%*"_2iKHc[DM+8Qul-`,'J\8*iZPRO9=+Z2bd6YpWK2--uWQ!X;[?s#XUpq$u-Pl;E?MZWb9JUL>jaF1t]IR6nH@UiIg&aL2\r;D;
%<]>b_aWP$k?`p'A0S\8UJV**MP>$eBm>+;Wo16:*bEF3oS9,(QOV`^HR"'FL@"%t$'Sf+Q#)r,:73Nd"Y+ZUKB$/-61oF7R$SL<&
%Xu8"7d%<AqYG`7D'GA&8(W#L0ab5@sVY#mo6[4PncP%=[(1&fX4p<,3DnSniOUbi6En"gjfnKUJ-<i;&k&Ln<&Hc4<RFkE$C'i",
%Z9FqV8@D)!dTgopa%4c:Lf,W9cu/?`L8bP99Xpo1?7N@k.M%_R6H%lI]EDf.ZS$L@DXiGeZ75q91V$k&:35I?f*6JckfM\kj/F\"
%l,0m+nKO-(X?u=m@X?XW,jZNbagf/Tgp9<](4sIgeNhJ'hAi4;'.nn0QPI]<-4ro$*S0_G:3gD-Q7rn,JQU1"n`n&3cO`Uk]%f%g
%WoCkE'nLRFKdt#CW]Wij<HR;u;G's21K9l,7gZBn(Ak`R<eL4fkfK,d[V>l\PD*,]4e$QniMkNsge9,=c[53pi0b9=_VgE:)`&Y5
%o1L8>m3tFJ'Xc:a\KIW7Z%.jDGOIj/&lR'J<oj(.[i[<O3l+rjM+/Xe7'p2L7*c,r"1i,P#SRN!bR`m8?)%LYLac!&?@Ea1C4=f.
%`XmHfEi8CgPC,nh]GZ?*U[/0T+<P)2Oi%eXc1KSFdO"#lkacck!?Eu.`^(sH.?O]fE[D"?8%95F?FQ,/e!D@Z&GNBloOY]4Sb?F1
%'>@;PZhT-?kcthZFMdYBR4#+f?l2SX,T/p:Xd!VZ27r7m=BgsTOBrL<;^<ef[lJ&hXi+r:Sgg3Co?m0jBBDhM=!CZeqos$'UX@4U
%h._3_!UV>h$b:Rf@kJ):?rJ`m\aDkP$.c@taONh'8qHVNcsrL-8doitk\&nRfa"Y4ULc^A5G-O??+'EIp=bn@-Lg/f?Q7+@Vq3mK
%&S3+r*^(jei"dKUXG`Mg/lh/SKo)1t54!P<Cq;=_,k+&c[f\91R?YcYZp^g-r15@.63`7k-uL5\clnYc7q!U00o0K[;l7#T&+eb=
%1s-e01n,/i<^geHU4mZ(d0[MGh0@L.'k*m)$`^"'-A!/\`5D[F5b-j&\V@uL)+kISY53muJtO-]1ab[2JiLGKTe2BPEIa!ZkMo35
%2[D-Y%?YQ15O!htIR"tLctt@Q-L.#E@?O:!=AhDTm>]N@:!N<D<=uq1o.9KR-!lFn2r(k(e(glPngAOshqMc29i&pICbMIc4;=om
%.re_^bd9uPAh1DQhAEqQA`AqSR>@-iQ5io3gh0B+*Wqg+g*hJ^D@De@mK>tgn+CMb1cZ290h2s6Y+J6pZ[9,0N[(cPW<]kDZI;;S
%PN[ntE7NrU7>3N5'-A@/Wpq8r2T9p\&'IB%/90B/_uO#fk6'DFKGDUW-UdF/pFto!jXJ"7PEN,S,3bM2dPGg&;ICU>MDdLs8sc+\
%d4RB?NfiOl.pn[cZmLB^hCqa5;?D4idX=0r9hD:]MGpW`CZ,/]$V/+VH_DaN8$Vrj#""X(_]Iu0R5Zp9$Fs(ZKb8C6)QsJ>cKIac
%"'UYOLp'DK9e"FG7)uiEpd#k<Lh7"^=cn%<M5]1(W-3_[b!sQA'=@qiF5e8sKWq[VD$:^@Wh`T<U@sg'-Ir3!PEIART89(A95Y`?
%.R]U%o5)Rb[k/eXMOsN8C\G%PX%=H/E2YPXm/A/;'!VSUqnA#Xa2BbrQ=`GdkA'Q>dc>NWL,M6@>J0OW+4#&WBch</0^+[V^c&g-
%0rI`CMDD=oNJa27&&g`u,*f9/a3nmPNassrf4ji!>Pl?<R$^;P&EjK6q;6@2!'Nj@/M2*l0D#t#ZDDq`8B\)Do-TZdJ6/Q^*>`8]
%;D5m>ZRf;fG%>&u/DjMm`.c:11"JBD1boo=#J-q!M:jch"a`"e_F&PTHCel-U+(rL!JsZR1U?d!-.r2^rS?/2?C-@u1m]I`&.rDp
%_AJP$A$2YsKI_ckVj[pY7)oI<$pd9JLG5t>$cMp@npK.n%2:3cFFlq:Z,ctcEX#eSfqjF@HNEM4H9*goO$6@mP=V*M56O,@)0Lbf
%@^-sWNeQX;Lt%H1J,Xrk^8s9!j%Aq!JH#^Y%!3$(lI_HSP=4.7&*O1_]17Li5bM41Met'b(<a#[f?ZV'G9W2CeZH:&R7ZRd`^?AU
%N#V)>Z[Sf6jc!rciKGCYACaSCEC,OanJ14n_9t_q8b/P%;03*RJWM2h'Kt%<25dO`8>r2o>1q1Aj%RSdP[,+hE:AS.7G]7s+2'(:
%0H/2SP]s<K4Bn9cW_$b9H!)=d6n,!B7B,ProUp5S'GN8^;nZTsfs<3IRNVY9XdH8(/N5N[^#H-XoCZc*6PRU>3Du_GBI_V'f-_hX
%TUZWF)TP[7*jtX6N.%h)J<$s-&ZsTY#A$EOD9GCY0lh<DHB;jV7o.E=9MZ6,=U=<39sUKsp9KJ8MT*G1<-adM$R.jETL>)[9i`O\
%#pHcqEhs(T%+V)E.`,9"jfdpq<!)XOa2[M%=Q9QBo#XJI@\k!cfne\KQsUS4/MR[PloYi7q2&1IhFo,:/B21&<p;(UEV1kubL/S6
%U6tlS?t_I`(ne[Nl(VY:d0uZnZM"P)SD*:]is!5!M%R*/Nh5T]f"PRW,1rc!-M9HfF?>_"[RTe<14aG$HUKDnH?Ms1pE;T-)2PF"
%#3*.;3fCp$kbA`Y&ioYeLm&ZX`-3gsMfFpjHc\\qVrH,0mN>hYnmrV"T!5@D/a"F@VcpKk]SuQ/VD5]c=.YL(lnN5Nb!"(J\sR0a
%Ag@RA6=^L2M';bTDEDqihI[_EYW?d"P7id:I8Dea'W;RPWm`LALdd#HO=@42NL4=n9!Y-tLqum".&nC(2+L=V%Jlf5)bDl8-Mfd<
%F/b/.]&QL8&Je),eWHb"%6A$V(]n"USOt!tStQG/DR7\kTM!<0=YX)A40+lP`HtRsK49tm5WgVnLJCUb/6,f3=o<+r%M8akg[h7Y
%#S::^qoMZ*-dQ&9dtq4c+)b9i^5?N]A@rbQ#@j!';dLET#\'ar@HdC66\hLal9VO7RltQt5o@ZkQbuk!;PU79Nm)IoTJ$NQqGln%
%EiD1!MQ^0B''3+km0l++fIh4_<PueCStcYNP*$uK`UlS;EE"Ps:!>1e<6B%6%ckbT4_!&E?)%Z`b9gl*Z:a/R'+\r_ZDoC:p?XL:
%H`/&"dgT.old+859P9V\LfsNTiIk\93?^p3p(Tt$`K]Q6+VRA2)Dq6t7'@VaQmP+eKBIn_;--A+=9VXLS?[qqfPP+.;=,)rY#'kL
%De,^TbjsRtVY(3S="BF@U`DLWZ`BE]51`fY'aG0:Ag0duP+,ana]=6p>T8ukUS+!q7_J87X(Hl[a1CY<-R#.4C.lb?WA.p@.V2HZ
%oJDEf'9^$K4(o4/8nAGJN2eFSpo"Jg'8rG]Yb_&BIK%CUpn/\4'7$mB<@ON8*Gam[^21]9--12fc`.P`-15WFAUH@/8d'ES&nd#@
%Fr8LBU1kA8K(;T5of*F9NXf;'Wm+*52>L?K[LO%D%G.<5V""L!SuL1#m+%[p:,7S.k$%)^#V'0mG#k1S(`]4qRnPb>H>WKaB;du3
%RM*hOh@FO5O`(N=c.pP[YF?6+DJ+\1Sh,=?1f/ctcaH'8^2`:6e"Q;aXMjBX1pqLHRGs570T"e","O\p?=Z?6do]sX0:q7\-PK=?
%N)AsoasO8W=&XCqm(63C3Cn=G0i:D=J>0ZsQ8\WbSkgnrMYt4`9F)7B&iGtWkRPo1Ke%t7SU<Ng`J<T7(m7@*(tP&#/[@f8_o*T;
%`h,@3K1XD7_o7iu%*!s2fpLTm4:;$@SH7l,mR5W7*!Z3/^h=e`Cju1dn=$.$=bs@1#d"inLtC!5DXYLl`%D_kT88;eM-AEkWLiLP
%Q#&2]j5Ij>f1&h3=;b7M65SIAk]4/#."2;&8NG3m<0'cl\0t.$&H\)1`<&IVN)(+DKs#D!Y`_'PU!)_GjO]Vfk-a8kEe9p]JG9E-
%M`#C.V!p*kpg=*nmNAMT8g*Q0d=o=K4khSW&"ahGF-W[6?VMb.Z$dEXOT95H6rCjY(eT7#+.jOBad<3@)5@*D^V/5g\#tlL84)5)
%rdcS^LdeIW1ge#k@)I`jSl%:09N@J7j"ZLN(*j&OK4GtG0gqA[PisSK&Rq+\]&daV<h]kYFJ4;CJWG/d25(Z$\Z-Ip&'#jDa\<E)
%<EMAIL>&aE\WZK_]4eGCeTa#s0S0g]]ph8/Eb:f4q4f0k\I6pobBbi6/gB\"\QcAUQ'2<jFS%V!&_MANXHuf`1j*J8f
%THbLDkt:S3JuC5dLcJ/I'C-eI)ee513dp.K`Wcd*KkP"biJe-!44'OQ*7F/:1pBVpAYMW@&JKd5@jkJhA!F'Y+[FHa,3M$O2$-u2
%.Z2iJniH(5g;pj%7H2LD35Q`;35LE\BTu,"j3\0ZE:UF]ge`hG(IiUUT"[]^(u*-''X`FpC3pCN%&.`p/<]@a:$pM.>Op/uW7UZn
%T3)W$@B3C\5F4E9"o?k6*lC+Q#G'W=TNom\Y8GAXdu-Uj&Q-I=fORnV(`l2j87N9tcn-d[CA<(#K"A?h)$38GcKi&"HA4R0M1^A9
%WoHOmX(7q["X9+>j9.Uk#FE"W<p9XVO6dm5GVFl9kus$ZdS,B?],&[YVKm=@';X@TJLhuilKXK]?P'[34/,;dU!!]HUb/_&6ZTo%
%*Y@C6^^VT?[OT$(9q4;$6!ma;l'"qH3F>?.46[:rY(W5p320E`E>JG5ooM8PPGE`AN(Y^G'XP`W^G5up%$Hq:)(f#Q624s?!8;+k
%k?6_"Zj2jDdjQ1b$f%;uR"[?LT+V\@)I3NYA3`djY^fP8C0VFj>)2$9;QMSWp41;pWeO?5G2T7B6kF:!P'a+HrPUqE6<^Y!oqKC+
%;6E?`';5FO5Xs]E/)T-<0B^2k)W5=V:KBd"6Z^o4Hhh\YnSsGq-^CuP*:Rti$Ab0Q@(mX53H`t/`.m*.-LX_?U\:;+e_-\l_RfTG
%JV[6l/Y7TH6-D+O#@H8J0]Y*!(EK)GVKXGGM^VhcKJADg!pd_ATg6J,r4(qc%8UH;:L,6"lTo1\2sBr:-^>9)1iabZnsHr?o+5]+
%<&'O*OK\D)cs_V6)Xk[69Ts74.T$g/LhKfpD@=Y2gia7l?8mTm7]5E@A5q!AY6Va0#eHbL1U5O:,&n?JE$6Q@65R`I'g8K;R#?4I
%F*_Gj(rPLq+Xh":6oX==pMt?-,iWO0SGAr;:Q6qm15P>cnGn<XUM1-.U<<o#C8,p1%MPl_&OSI;)R58$5iYZcgiC8<2VS"8*Ba,N
%(^SZ?a_K&^kO&*`qPhlm<#$,G=,:D#';G7BR&FVd0T,9^e$DVqC@(!]85kj(LV"@&EB6V^3@a5tZYBb@A,<1UH]%Z?3"'K_`^SaU
%"J6t6WfOY*:jI'e)s:qq.ZNK<@I]:k)BtCO%>S"cUr4&@N3KGZ*'O&sPG6&$\2e.[VEoTSR<9ID)6KDg=KXsQ0MnGZOZg9%U(C2i
%-PIdU1Qt-0(CehNoYD\q=F-&2jg6k&&A8<'_HQ`QfPJI)@(&O?3"_R]6#T)S4Dgc_V@=,+ojskDX1UtW-ho]T>:8!2,'m?\ZF;X:
%$1^/fIB'b2;\^en\r)_8Qm:2l;hERrAF@];;&b/]][/[/6^GS\LgK@c>nGCb*c:0T2IemLqgCn+2+hYWRh3VNffs8qNf?!8$-Ucn
%V5@](USq6V<8$ht-saQEl%K1DTs&OP*Y2K@^'skuD-"5;4$%U9R@#[lj>"9Dc>tr-F*3KEcj@RT7&/+r6N8On1d!`k-dt)&E)=P7
%lWr?G;IbuK.U6P:Z5:7%3FEjqP>[4<N^GI>T,96`%(HN:F(\M+*eaurI34fBMEXRc'%(j`M!0t[U6.i3XJ-hJL.d`sFJ6l!&m[:8
%;/o$O&:+$?(e.J\`cKna/]0o-Z7/J75t/`RJ>T/"GXk`'Vl`R/9!EP*2qL]!hj,>oTP?2sIUrB+.Do^(Fa,`P@lUl!DH3$JLMNZ/
%ggZ8*,pH5B3eCn!#]h@n+@CAcdpphlU*9sY<H7?!KI\d0a?bc[#N,3o_oT);0N_m-cXK*&:bSbn;]Z0,R#G/n/rj)$>+.:0kPb;e
%-I&^40ncLVQ>WRR7mf8(<o8,*.^fQ7lGF@sO<d:`kCR`Yg;L$WKkeOY:!^V^qGjOP-p=7YCaDj"Ik_O4q-%g]H9g,L'*bA,;o=C.
%H%'3<@tL!*,;M=SGgDb;B;Xn9_8P1qX</@P)X0P;h!LbO%,m&9K.7JqZ<C1U::'-pDn^MjppZ9-ZQ^^`5siBn%hQ2Qoj"#t[[Q,^
%kaUFjjp&NC#s=pnou[in`($Us3Y9^#mIF.f<`b2d+!DEA-6co;C%RK+jHHPtHu;\0c"jf@RG+R\A]]]RE\/+0@W!02G&7MJhF#3q
%VB3>442dLi7S$l\?3BaR5tN'22pMs)4k5OTi#'$^9uV#Y\ToCm7,(%8=77Q(aE>S.6nje1L`Og`La2W4h@9ountu:DDuh.4rW#(l
%Adour)Y*XVBQ1%O%7UkN+\X'>_47@F'8CaI0Aut!</mK`3)UT/YVFIDJB/kpU6/Fd,U+k?6EmpU8j@VENRctpKS1CedQ8-SonLOC
%-Vp-.]Muu,2VM]XFG+_"X7_!rbMXu+K<&$B9We2I@I%6a*?fIZ5\W76(a7QmpB/3+,1/G(9*tib\Y\Mr=qHTui">)R2=^pO'Ut86
%e*%r-d-c#j\F.2)1?u]$445?l`J^'gLsm)<!sOC5iLDXRN^]'RmbElm^FjsJ4fK?d87Ar4"6A8,[S\#J`m?8WVMjP.:'1P$q\22_
%juLrL/*>D;#Gf6?77NMu[:[!jRj-MsJ.]0@T-hLl@Kq*-3_T#NPFn":C,D_CN#Djn7h1&Ngg3S&&J9W?WaqSgDb8knfXl>)-kOr2
%B`j$$jBXH60=No(QrL=RglZb!/\KN$O"U^j=VV)sfgomtahT<?iuC5CN+cr`#W#>gZkne#'(q'BqUhZ"+HbGV.[J:%M-q\5dic,e
%"S4908-4j?p)D_c7aP%CNSSRS4s=D*"ZJW04F5pr$b:l1`A4pq]JsI@_t5k,$EOc`duKNcA$rReR#.f+nR/W)TKS()bKcC/X_/Vt
%2\'5(KiHr2M8u;5k[eaT?>ZM))^p44Xnp28A^K%\]%!P#4su"O$SbB3PRQuq@<FFKKNbs];C@fa[N=AmmNYT2M/=dQooj)-+pnS>
%CBr,4_461qS/P\Noph>O=btmC(7h*t$5),X2u+_U4q]I;DD/qq;T0@`hu#fo=ch\KMcHLtBaVge;=ll*]V27Xlgf-(%TT5,,"A`3
%Y2e'BZ*^_A2-tP9,>MS1QUsi:E:sp2QE[i\Q^?M!"X510Pn9).1298uM`8`16.Z]Zb?XRHH5.XZInnGDZie2@ReQ2o3.GMN9iCIY
%[7":b.:,^:QLH@T-WMFVZVVu?PFO^No,?@K^ag!M(dn0+0rNeccNbt?"jC\6;Q"fleXB&tUumfpi6ap==@3_0`p]TWP:4[&#?JU^
%OPD/?KPggD9mAXXq.H5L1g',7n4-"SbrTMVr9#V\j>[/L6EX:OI3cG3M("0=c6Hga79ea5*!;5=N/^6fP.jL#k/B`Ef52dCEWuTd
%\O3abPcKeu6c;)AfSTAC3X0*daU-?3'oE.d&@K15RcTZ.iTHkRMFt$?0,e0:b_:c4*EP<-&Au0!jk,V;83"G?d"ta*&!:GI+ef/Q
%gM1+MR"\e-&WsXnO(Sh'!8gbML2V5+P:Kq_?$'J>1XI\C;nE5%TFff:+l<>$*7Jst+:Ekm>bSE"&"6U`m8g)J9=!&g/D<J>^cu0!
%Ud6e.r@aD3,"O!_\5Z+jNerMeW9mn%M)pe<ScheM#M@^ofh^iB&uJ9<!L?'_/J=o4N:g,2L1&H*p1i=.-s2R)%aMeC8=hfD9$2Zr
%$UY<_bQnt%d`>Y<MBj@`?roe1'?&u`AjWe!RuRKC!U!ZUSZdVm0a:@3]&$@<0&6Y_NO)%Z88BCRW^jm`1KiMU(0)Q[4_VlP:-Sn4
%;H8FDY7q!a=TTI`XF`]8**;fX>H6T/=@8mJ\fsp9pXVoY@'U8J.&7XQ.l"LASQ."#*u0a7=cOcB.TN/g!1h'c9asV:,2V"^n$!S-
%&o^6-e/k-2A1Su8[&r$,/n'PQQ<iePe7<OJ!H9Ek\<q)*-BV0(L1jUU*ssuM;G0<&nRHS.6e528*X<-LdJLgZL\4p&<nS`V69h@k
%\TF^5j>:PPML8jI=.Pkd"ngF/TF[QU.d>p)$5p?hdmO$=>%@[^aZ*0:FIu5J:F]o_0l'm2S`NuI/r)K)gaVGs5GWCQ&-WbujcG@V
%3/93EM7GE?9@oa=K2s7rL^U@?^Dn=E%jZcK=s=0/>C'7V7cTcT5ZeEEi)g\gV+A\]kWk_&`^8/g(sQJRZ?fE%i65S_j(:\m)Yi+P
%O:IK2,YWgtV'C5b.^HjO3Oo6)B1gA1adb/;*f7%fFhI,dq'Lm(Er)43D'n5iKc*I0Of\9oYi^b[L0,.Ic&mfD,-#]GA]k;o\p$p0
%AYY\-,?LSh2:IeW89Y>i;7o-I6'9k6d]k.W`F@&p=KDl&Wj[,*?*t__5m1^);"*0FMjO/qB#UP'OJ=f6>k?9%aBpo.b`J??%6Xn,
%WkiP4CeSRpVSdu.aSMt?kRXh9bRiGEa3Sd'W!A=X04LD2Aka.NNS0J#A"J3/fihlK!'H.5]>QTC%Ab3s]%&Mcqc/8O25eKR!/S54
%)IC3+#=U6pLnc)%npiM),O$ICJ@HoM%P\&WpU]n!K$hV"D@/eR(en0`(;h^EjFYa+W%l\(pj3TMr)b[SFD?b/AMt]+[,HPE;RNWQ
%22J3N#qs@Lar88*][RLY*<s_[&>X8Z/<#dO6lGbq_2k5_N9AaUj%)Dd93N&7D]2N=>^-'nlM*MtPFR5r%@WI!JVUZdi[r+eTSo1u
%GRt1+/B.0YVA)T'kOD*Z@(W3GnWr;<TgK>AJffE0i#a^t!h*4F'+Y[Tg*2HTMI7CTEbV$_:'^rl+)<2X-C,>JN/0cti4JO'TYPD2
%=3O);`kVdg,K\<u\jgg;Jdtq;/&M0V/jMh@A^BihZFsD&X*D4(Z<6][X#%1(Bdb=g6B#/h3fR(,D3@d!HJ'Kg(jRF(G?E+PC6Sn"
%e9Z'7@C+&9U0M]u%#0QN!L/+cRHjita]Y>]bULpP16o@!W=U4'AP5)n_*h-u(Rl_UH8p=dbS!3/6j0jX@5)j+*5hl`\W5SEU><fi
%p[I\<::.KMmaMbY&4[`8?JpT*LjYAqeRS;TKnSJ+boP#M4\ir`X`774Ff[1,rikY;:lF",kV*1-rQ0+fZ'AR&iA`uO`+X&fU`ddd
%$cCYrW<Sj0f]Y`rrV%EkWmHksN)H+An'J0CcN5pEEung5)lXDX>.&h#AjYPaP@VcgecFcOj%.ZPp.cnq*qUnPC_?cjVLSnQdIV0o
%+@O53TN_GOPEOV!a@iVM[oCe!JlC2>ne;PsO/g[n1EK!04I/*"nTuk+)#KQrl,?.]^l3#MVe?Vf.9\`m8t:mj/rgon95*85]J]qR
%!4Fo3q1CM5f5tsG(d4=']RJA9JU4I>_GiVP_4#*T-&0\TYaVGVXt:6856*^B9*s(CD$sqG8(WF8e_sSbUmK"].MpPaO%kd^41r[m
%=d]I4@FO<2Pg"MKke8O`V?a%UfZF=NGB$,S4f?A?-6KL#*q4IIP<0[2j,"RD=HgVC;-SQuGe6+19N]<TSF0)a5jD:pTudo(ZTSf7
%N!="+ouP:W0q3-T&EC/0@La53QJOHsB,Y'C(8q_5,KLp_A-2'?_?.p5SII_]brCM1D!P1*U7:tKN`ROc4>NZP/oZ5],>:q_3`%)D
%2YaD/GhD+bdoag@YTg`P'ob'iT"YF'W\Y=@-:j)(&>TWLbF;g%+F0r_"qPQUDJC/RLP]tPg*"q2U*-)le.U1hB4n]C,K0lr).$b_
%@"6!'OIPL4A/,+\cAE8#nWJ$)HsuNHDAJcc0d1]rfEa,%@$*-h[=X)md$#iKm\KX/FuQ+0DcDBtOD6ej?7VoZUD)rXl-Xg:]V2JR
%"tZ4\k#A/N,Pf%tp+Z`u9_:&>\L9R*&E'Q[8[N:&DhY_2(8=(ik\=4U@Tq:=k@oS,hBAVJnd"bA;i\QR6jD&5XTrtTdo*3k7ToR;
%Ifp'D'I"+LiuE`ug&!aX8[C>gU)c7F[iofW.n^AY]7N,[),Yp4C)hQL(PM7O&B?%ud*s*fi0DkuOcQFGS!ekeq.*s*SilB_R2SM+
%dfNc]Fj0HXAB;QjXG,=G89A>EE+72W.XJ>'WamGNFa;^YeDl*-i"Yf1EW22Q>9c2V!!+9iD`A*`AKk0<EZRA&,jU&%q#kYT@]?Bp
%'@DK;AU&[jORl;[>k;&j.$U,/*D-tm4rFa_N!07]94J!i7)C__b6"6>$nZ/Rde!:<(&!5[!PP/r/gRs-YE.g\4j7qiJfg3?!/=.d
%3V*6*9>GUX.n6$=f`OWT6Z.Y'30*;?bZuoZ+$lXpA0\`)ah8Lu='r$P/c"XSG=`YpU5%=)XY;8"j*'p*>0#pOUo.P(\G[pW<_*9l
%0>oCB+:R<n/s_jp;:;u4@lY]**:R-"'OY#e>3J#r+d>qlZk-EmO%H#t+a,u8;qQXC3"fpjLHD0fdr9(\@`Gpfrdsf>bUR1+*Y.]Z
%P8SoYX[ZDVj3S@:RVIJ[M4ZTIAh+?l,mu\;)KuFG7Di_B(BIl,2nXB7Y$%0@2O>Lcp\%Edi4jEZ7-$IqU1!gooWh%B<:=qiR0F8&
%\Ipj@X-c4Y&Dki,#LhVrgdo<&a=>!RBJZ*eD5`Q!@'?@_j+"WP0To,mk;pjqLq]S7a,+:u<u5KP&tl94cSJ/X7&qQS/Pi?]d(?"P
%qrng+M)lPP`1t2u2SCW*jcb5c!54%dFsn:JCaJ3#FUbti02r1h"JnJ9Eg%,GCHrBZW[E,0k].H`QI8$ukV+n@"A)F,h[F1Fp,e69
%Ar%><qW[3-!,sVQN*C;[+1V\F_cGU/-':W0?c:cZ((%uD,i*ZC.\0j=PqMI80rn-ZB0J]OVCrEkV"e:b;d10rA$tih2:j$gVL';9
%9hnij3C24MYaZ-H-'BaXS*2ia>-2=?3AbBl]X.hFIP`3hBc(rD>("-rCmUe1QI0Y7@6k<"dZK>SatPMh$BCds*?gqDm7M($d8P+d
%WM(Z;VDrq/W<.1AV?om$.92J4d*CU*TUO6Mqni8';Ru5tNJ`olK\2s*6kt?Q<s\k<Ok.`Z7.5@UTF![^W9#&e+L5c]c:K'N2$o(<
%11a7;W57NupA:5^;kWjfiBc0bYf&"+#Bbg8(c>lWe6b\48.O?>Q'bWFXb7`-k1oOabr\G:Otho+:>Z(bPf-]m`5-0!5h6S\gbMUO
%h#'T,W2?[I'"dQ[:<nea8YdEj"J>)?]lS)&\OE09Lc@#d-CYQD@lRV42iGA5(k!Q'bG3)Q#Vg^08(_80N+5A";r*!k7p&_*BqPgr
%ke&4M,SYY>1=WBRDqQT51K7kpHN&X`,\b(u3=m4JdARIDF@BDhA;soZc6A$Qh$TJ2K>e:?aQ9pIe-)>?#h;MI+tPPSh'D.@qD<jB
%m4l!,jpFlJh?-(iFHeM<L4/A3bVf#Cd0pkT."clT13Lrl1jH0S#o4oC@_f,Ws+9*F,s(OHao1Ma,l;il@%V'"[%09f2$kb\o[66>
%B*ikEdA6PJ+U(XC""Cq$QN:VU`[2&^32e-\M"!5U%f'3Go.!%gi3lcW2WI0U6'3uhlN"P/aC7!^<oN%>:a/)G)=H7Yr.,7LEZ;4g
%T4a#MV/`JDr"q/'S?N?WO0#p9H!j_,^gDJJn7M<d_Z:,"H"4T-%&:kXW?3Z+'=ZfE:s&bif^Ob&]>G,ThZefGP>,@b*'GW\J=bD2
%4MnWbAr9EO!gIum]P?fAQiq$V#h<AXP'&I1,1r3UbeGBe`:BK]o!I>A[2sq65m8!GQC!6pG2['JHOg_AUm*c#c:fnB<ftq4)4[uN
%7u9.hBen2/rk;PRV(DEog:4tP^:a>/M(jf4p;GYb``(a4_9Sg5dBpY$0J@60mNITJd!?<]1(TS)/36B2ZI@:d#SkE]E+mp7mh(uh
%;lk%p.@;CrHG#sQ5oWS(MRle-)=2ppT]Y0D*[siG&#DFGZhkGpTlA\DS7"ho@;s#^i3`RAg&t)<ot<.^=S(aGD-R/'.^-&P5%dT?
%2l!*Sc&:Na:=*4@MQdL=_*gDk,Bs?'imQg>[.O\98`KQp6XjFICp(#VV,+8:#6sZ'7t3k0&.u7:\d]S:H"]K4>$&Je0uHQu<N2_C
%T;:3-%3=s;C'T"cq;YO^LIQW[@:5q9RT$RpQkDq.6<p)b4QfkVPKG1Zbm(162@3<Gph_I7[ug\_KpWPkdkkcF1b3U4/8@LJ(1C_T
%)Fr-&joaYD>CheK2u#iDrWMmZ@*N15S2W6(*b:N(eS8l5>Au[(1+L/HJ_tTP#C1D'jr?jYg0DjT+C6BAHj^,$KUsuY@ToU`f,/FS
%Ns7/'JZ1M,)XSEabk1ejVX6X]8s5@JEf.P%&V.$Y,O`EoVTQ$JG9*oI<V/41Rc(8P7okplOUU@X,MS5ib4*T2f8,6Oq93$*TQTo1
%E*YN[_(Gt<cX7#sd2PK+=hK=&7nMbB%]WAoM!:r0Wk*E^3^>Nr`Y]@q#9;AnN+g6=mK$DHaE8W:jsmW3/JpT@a(89fhujS-.S$l=
%FnYI+D;l9%jX%ef`guX#X#_CIN<`\sW@%9t`eNNF``(fX4@"*5#4t`?R9f6hCsInOWpPts-_*2>1$'P1j"f_&qq*s%Ti'dZ%Mm'"
%(+a2[FtO2&arW-609/VCfm4rZ_WumN!OJ$`d%W*f6O1fo_$2K75__*M.Gb+[(hc>KAHLR"cSpFtGsa:,>#SWK#Ak3F]hulV^tsa!
%m8XZ3+*:ejA<l]WZIj:%]H1>oLR9_`$Km[/d5-e$a$!TcC)43S#HXgP,CPVST`S@U>"#ckQ9ZO+!+mE/9t8sZ#1u(H739[ic_1HG
%RP<esF\#Gk$/[=I]FqE12]:2`9DD[M`$UHPFh0qu32MTp7G<&oSu)S?X=)A_h_L$?;M\KsO![uWe?^*@.QEW&:Q/,T[BMWuMa.O0
%lXmnm*PQdDKFf=o"4MLDSXF<B'VEYqF5SE^kq]sRHn%!%$Cuo1"G`9j]$H0H0U+5'14;J[1pm`0U;/#g[6F+G%LgmZ8\2eZeYD>3
%28#%WLB4/1=3RM.GF@ZNKWlR%,#/.S>Ue`J3,!/5E<*QoWAFt=L.N;8Co^UO4`fV9G#2&G,?\.t,/saUVcLmu:Drob@Zo;p\'Q7?
%FOJSsrt'Xb);k\5&B;M!LRJB$Lc^;VG"e^`N.#Ho$3nL@?e.S#;8e,*3nj)m!@`_Y('cdU.rc)ODNqFmQp3_<Z4/#mIJE&`*YTIX
%;bJL6!#dsKe-Zd,_?&?>DB1\=rPZEO<+'.JbD;V0Sam3g7.^t%Bf(4t<XjgJ_4AE_1aBrb!.bOIbF"-pl&^0]8?FA=*8*4iU8=]6
%'180lUulVr/[s-mqA(f+SJo0PT33au%\@&?T0rfpbc$Ol`:/!t3KsUB7[gl1gPr\J7Q<T`1/t'L7lZG&8'[Ruhg#VBOuI/Yp-h@n
%,a0#`*IW8^K#]E=/KiC99k//\/=X;1VkE'Y9B(gcqBAo(*;+l#8ZXbkiC08N`2kc5F:/oJ%Koa`eJ#h5Y%C.'Qpb+ej85#]dn&Ej
%AodS.?l1g[(mBc3<mfh6m3,7(,4uQ^0ps[f*F/%8/$-XkW0/Q['jC#l&_B3"K*AhA8:\ro_#5`b((]&jgjLbDoEh3%Yt[=CD&ou'
%#VW!E?rW#V]oUgP8T8>LL!4/k6c2=1\tWE;A<*e&a:<Ee8@r$a4+rEKQRm;N=g2eXG1J-Z+d'KA,^o7uF9F02O[KH1OMBJ0D0/;i
%<N;>*L6Mbo@THdu=nH\u5sVK/bVaE]TPTA=05h&d%GP:NoR*#X[4N+[!q\43e_8:>Tq3b"?pr;H?Wp)tF<L@!%#&(K2eSG9;8-IQ
%M/_c;3B3G0)jO56QAEpU!1+B3DWkN:R>b4TM'GCO$G]oI*L1Fb:+_cKVidS)6SV_*!N(=>.=N,K;t:jVCmf0d[[^An[tH4q)DB![
%JPcTNatX/UC5cA$7u_NuB$hYt%(M+&VlIl0@IrPU+SlXgP8),\2,8DO)ND@f)lSB<9i6g:cOG%c2EqOg@"LHtkuPr,JF&PVn6LYQ
%h\iq_2,8?*%p*>e=iWB7,Ts[T(L"S,F?+7b&Y!Q`L%pepcIWX?2tWA-ITH>p)V\X[+)LrpgX'p;@DJXU)]2Z*gI'.)/]Ygu5:49u
%X[1LEO/\NY+jXcNR]UUYaY>t?8?eC1bS\YL@_&ALL%nKb;2uEU?,S4P-%KA9'K;Im<P_@!A0Wt7)6'X[dn`8jQCQ$NgHk;LKOT/m
%.F(7Xa(dK$5n*hhVdW)(H^3l[K!u/!%Vct'"B/^7=)Vf^ai(qd3M?a7a=p%^Jmh2rZ'>PJbQp%K[I4dVj,G/2e+rL&r2Ch^gpf\)
%k1\&5;OL>n`*OBK1RV(g2L&]>`4.-n\J6Q0S=+473*e73W0CVhmR7B,oVP5YKW^d[6.*G,(L1'aotY[H?1S@cG^JtlWL_RrnLDf&
%#)K4o!YsoA6qeV^nI.Y'W'Esf2:;gI+n_iRm!X$=UFUjME2C`U9+Mg96$-de;ba90#i]D<`HOo[Z[b$!1/#(YAQ@g$gAm3;5ha[<
%k"&FFQ]`G2(aiJ4E_7&8Z4_JWWDpjt-!IckcaLB`q28jel-0t]"/c">%cW,(:q$0e\Jp?^gp:_@ORHmZ$_Fi#^)?d;7<qTc[Q<.N
%3l"=X\H7p\,k+u`W"Nt"Vhgq+S#.WI<;3GZAG.J.r25F'$%6V=o^%9fP"8Qb_9.T\X1L'K"c/0]llt@#qDg(/JOpC_",+D1>Y50"
%$LU%^?^u`)"D^"4'SRoDpW0AT9r&g[3;*V%j_?92?4D%ALf3Vnq.E.<T<;TsA]bQ.\cMm'>GQ%;Vd^cLbG1H3>pgjZF\n39X2$2Q
%bQX@Z!'MrV\:A))fTt5Y2Hrd2HQLdT0JJ1B;=*:^<?IpP$==D$`P_o+""Sl<-p)kBep;id<[DA2MG-9cDMG;e"&8h)JE)7HJM\Lc
%/E^qTi!3hYXE]@a.l(D>Y#rJ]U\fWm38P3dfV!"C/*9qJ9K8aT5k#nAcVb2T:d(R=,1EYuY.Z'u!>)?r!`.=<(fSmb]o0)IV?TW$
%8[s>C.;@I8`2)F#J-nKtq.c%8?#WT(k^ZZ>;;.kpc.&&5*]q!=.0+@NZ^-<t1XOla/u)L@!RTSfl,A0fdD\7lC@?\i(HP\liFfCq
%6OS3]or,47TMkAcO4'I2_eumsiqchEaihStYHjmoHBc]%U';\n4H>]0@6-*q@PoM#iI)sC/<,r<8OP*g%)4-tXVqW5BuY4*8OVlr
%#P$LR;9cu]B;rq<o`,5IeR>[)d4:jm],MqlYc96_L`B88^'JK(cc]>h8?)FXr&tu,j!8]EH^1;>b4G\/Ou/kI"uK@8JrtYbaVM^S
%'o)uaDioi8N7mE7ILI+/Hb<o>i6Xq%eVWJlqGlhJnVImkI)_M;M%tr?B2HR3`uK]U6"*Kd0oD(i;=K8B(@SM@DL0J1n3"Tklat]O
%A=Dr*[C7-3;lKqHMH]A-%WJCjPK*G3Mp`e)?F#r`kQ0j*TLL9j8+bC/`&=!740Ns.<gTYB978sFS@!qqIB!CfQf4)^.,asr5!:)q
%j=<\61h1<h@5@Yc(gS.3"0.as,WWcXV)XKF.J+<o[mtBXfDR1LJlCDr@:TQ^ZCEXYDhj!]3nT<)"6F@aOtJ:+@NeXb,7kSu1(6iZ
%Sss02@Y$=W@A4L`*n4R9%r];'Sm6B65guEA0FDPiP!VLG3i>ULHg8GH>C^Li'tf3%h'h7PH9rOa'o,)6bLiM_SCRRQaE*qff+`h(
%&rbF%ZRq=Q!sWSL`sAdI+4Hr_,R^=Vgk`\7C>n`&.n5'\)m.aa+f(3u'.]A`_B0F;0O*Hi%p&e?'-Is"XBpa4"H[S43DX@f3R)eW
%'"h*',Pj0l%0=$b5VYbbOqJj.W(4p6;2H#f!40X-EY#TG<c6>ZH9$q%]+t+fjW-MJ>Ci#aB%%+2:3Q2_[hoLm0ftSFlG_sT1T!QU
%HZjDQP3Bg[Y3<t^"bee'Ln]\-;jf`2kZD9eT(n`a:%)8M7*3i/?LA)X-6@d"Df`T^;gN4i$O4k#Z7J%-gKXqFk4/))$AWJa#Sa=i
%L+EmW@UD\J$R%]q3\\qP*8NC:I>!+&odpVO@5B]-"uu!jT(4V'Y-1J6-u(OD<BJqeW\PM&=F>?^CRhE&:Fq>rJ\%AHg`K6YC[bFC
%h:+`TGLMLd5WXo@Hrq%3=S%[E0^g<99_7UV+h@f_'"T:rWodj7P-t-.BKN@@l3NO.:DlY,lmEYV(!D@ZCj@>^=N/TKChUp_KB?7Q
%SuH]X3D\o3gfEO"Clto3`D.i4%Mhnt]GHN9C-!IHXm()<?.WOn=TVl_\EFG!q8n=qM\XH(.ONQS(,s<8I[SFQ>2(]g:IR@D#g&^b
%bUDk@JP]$.<)LmZQaf`p9Yh1K1lL#4nd(8XAkbN:/fTF0530hcg^oNA1KK((2?X?5F:E5C9=#=;5Xdr"`i\9q2,"I<,Y?"ZNBne/
%W.uS>,LmcrSCE7+G,K(.ELO3$q/aQ)R1Z'CY":``i9&Zh*tGu]j>\0%0hKcFW!51a%1-LdEF.^Doi>^D9=I8/!Qdkb,7(KA:O11@
%/<0HlD--=aFd:17"UDJlX?kj=n@g9'&Q6g'-hef%5r(Md4)/m],TYn/MQb(,K9Je@i(.Zf?S)8eObdbhj0rtq=%&7OURJ&a[-B$d
%8o9_m0a?u?Yinl_1Yf7-=bHC:"d<s)^PX)^"IKpbb2tA\7"m=&&%51llSdfqXC5nFH2+eA,"hW*0a1U'G]K=;M'kTL;%:.bI0\.,
%;AJ8Hcp\l#goX7j$qlh_TPNUW[X-gN\tSRkZ>aF`"B#Png]s^LZjPk6]$HZaae::o$:js--*3NsO@Dp%.hM6FQ1q"$SnLQG"[8!'
%R[-BPXVh^Xl;N57,!AcHQl%>Ug9A@l4Ko-**?7-jk!D8GDMe^"[jqn)jiBPNP-[HeEIGEU>K&<pg-[P'AM]&o_QK1VVd<i2+Xq(j
%&<;9ap;eTp*kUAkd$SlE$p?)K7+6F.!Fcu33#$L]/j5Gu-EKWc)gHH7UJBX?.[W7$;F]"h$>jk$8<:8!)Co!BA]QfU7"SK36^*uj
%,toT'!6]8Bg_G!KhP1Z7?%Gb4P>u8\F$3RK7cT/\!jO"E4Z$\+csDfKBaGc:G=6d=MX<8OacT&a:.923<\+:q.b$9q^gWA"Hjc!^
%V!&`063/'u*@*\Q3Z+EUhPg_b((/q;,o13c`b:YB9)'fXNDc<n#[.4ZR&oD_phCO;$[U?n'7S.$Ph90HXAfmlZ"AWN^!J2QLI=])
%J:?dRhS_q'88UGCD8MHD$'?4(LoRKTI?`aa*2u'o;t:\o>q$u+0FApmbSM%]n/rGR-'Z)Rpl;o\;9l*_]+u+tJq/5^*c;+CZXchX
%_l1eZLPL[W$c3%!<Mqra8XnEb<^W:Zp.V1PO>AkW@9;C2e/l#/G6?pA=m_,\8Fi8f#CVsqb[hg1DJ'Y8?FNhT"s]IP+fGFiQAA]]
%E`N@2N_@P?[o>\YBh]hSEYJl*#&<IU/OPuC0.ECA,aiIF!gGYpH1![9St!&W6O(">8Y!uQVD;NsTRMo+oasQ4aR'BJ8r@B*S74":
%0LV_'qA^M$.2ku,#!,0uH\")`Uh$c%P_r6SI[\GN*m7^GgJ3`7X^J7T(/b@MKD#A2As]X:gZGLN!7fHaf7o66c<F&-TMIrie=)"Y
%"fDH'#?<Z!1E`+2M(EC^]]q6t.tZ`]$4S")Yt,3IM:i9TRR1Bh)WmWT8;c+g6hCY$ED<2dFMR]&Gnf"^=<(N/%;NSojYZER/(kD_
%%RmEFc8lo>GA+a*'L?qk.djFtInU9tkbmB?I:u2'a:lRJk-C";NhIf4Bqef/\0FV((<<=83mcqc>'7f]?G;.4:2-LCF$)3aZ2nq-
%3^Su(p]aD9<`gg$&]:gnAi=JcLj1N"Vo"9\@kBW'*\@EY6*Go\q'!@=,VLqHLXrn*#eZmtM])_97fFVO6I@P78Z!q)Kl5ni*^m3p
%4@uQ^p$eVJrI>U0RW@)E:?ci[QmBQWdV;?)<X_eQ/YODeVp8Zr-tm0P,;d4Z+WW+;p]7>s$,9.k=U>hK1n7Tb&5oOIAV5d#Q<@Ft
%PMLO]"GoS0eO%,@Cp%GGD(#=4K"Qs]KsoZPUHJ`7$S^BZ>\?,:8fIPVil>G3ejD0(UA6l=eD-ub+Yj3D.V-YH[LO"5Rou(![3/(s
%9Yr*+H(h0_RQOT//1$.'=`n+dli[WQR?Yb9P%iSoDj?I"Us]?\U][4/>E.G>)oELsOPP:<RMIpmPI6YFa!qd$)[/5_*:t.VR]VTQ
%n*>K\NK\mP1k$Mn"1'06"tL:Nk"86=%gkM;`n?#"1ufNu;WB85ckH-*r5,bXEsfM<:ZaL6TLN;ndA9i>-D,gtad.dg32G1n,>L-"
%#!(fj%9t:_NDlEP"P_Tj=G5K_F:CTLU-?1X*+4h`6f@K3OH3OdS<-A,M]RmK^jZ)B8\<"+2JP.jDQ=8_A?'OC"\?9@+Q7Y9hLHqQ
%EH)q2J_AOs@U`BNOQ]`<XIs$;Z!)to@K&Yo2:+u_PBECs=IhBY9,HLl"LL/)4:2ib(U0A&9pl=,0a'iPoN#K=2^M[?9p`L=)hF_o
%;3<GYd:l\hje>V%JcH/l.Ohb3kVkjJiP5s-9J4f(1>ibF758NtCYKq=(\ATLN?G&T\YUODM,uI+q@c]\LaY^!\9>O$W%e3mdU<u]
%@C=]\/"nGt=h3EJ".I5anu*4ta5cIA7RsSt;)XK?U2a@ROWW+65!`';S:I6QqUs<rg?@4)7Vduj2Odg.?Ni8p_&@G)b:#uN%1uqS
%Zmj+W(>uSBO$Ep4Zq)tZUCP81>1Hg0,`ZVVEsbXiOq=di1`fZR&\eYT[TW!h4K1Ihd%\B9"/bA8:^1(i-/='Ep<j@>]c!!n":tfl
%p^E%]B2l_N"A;*^/2#-UktXi'EB1ru+n$/1RRAH*=&ci.2:03J6rVb(2HR.,(pO]r^*oQ37Q+_^MC^U;Xu)/M3-fhh:N9Q/$RmL[
%8TVP!c3#l22s*c5KbU5SkpbaB"-e@Hbn*Z^.V5TE1gI:N&ng<MGp+*cR*)50N3\pVi`pRP*%X=)cSfAXLCG!7SRmp<qd&'Xl_0Mn
%YBtut,2Gi&2\irt#Rar53X#.HrLRmHEal1P!h;'eY3iJN[VldUdTWB`1(B]h=PR2r1miG<@)^f,GX_?Q@`D'/RrRCJ@Fn+gBE!1)
%JE"a>Wd(QdN8HURO&e\Mq89U3(^Upp#Ik#J:Y7SX1J$lZMX1ho*;kbrOQ=?<3hCQMKW#hc4]E+I:VB$]GVHQ\C^9sVeV-03\p%b@
%<]iG%"KeFk35@:2WpVT'%qT^>AkStVFUV,^P=?F-7SLbY-G3n;nMroHcsr`..:eq[;&GdSUk\s3'`!U>C*bU'Y$F<#5Dt8."hW6b
%GA/fB9hjtQXE<^uj_*f!KWq6YVU?Duho6B"EWrcZ0SV3U):l9Op8Rd8=Lg0O6,5-M3rR0Kgpln@[oZq''@6<Yd0j7^=I(JLoWV%.
%1.3QR$2ZQWF%%nLHc\WSa($l[Q^5Wj#C/a8=%c]=;i#+Vnea-&YOEY@Z$B:P&'[5t!!qk2Qln9;"OW7Hbj>7N.'I>&)PeO.iOY/,
%9>LfJ)Q_j61^T4F7sM3Y0C[!Y:(C2F)QQlu)K_e.F^^\1<-H9lli11X)JB4YG_*Ah9Mmo0'tNY+3;&'Q#Q$DBi_P\UCIKB1m&C:G
%9SKoFFXV-CO_`f'YW-aHg6![Y_Dr?ANCM?-_Vs$56(1,0oP'%-35![?UF[L"@B)J3)#@rL1=T7SeKa>'%,rS(_g4Rq-3o'mJdUDT
%LgFp>E$nll>[<*pR62HX3V'QunqO[L0&5F-PT0Zo1de_6MS/Y099\@:]U=t3>RX@'Bmu3GiWMP$&q%:PSks$3J4QbtOQ=bYr03BR
%5BHI4^'n)Q*B1MR^R34*[]5mOmS*rQ#g'#[rc]:^^BAF4DKnS0,+&\dXhmcY\$U+W=rj[L9Z1.g-T?bn,><Y7lM_22R5N6*4FUh=
%;3=Ggk2Q)RS-E4Qh<Kq34cb]in[X@A.-%^W`sSanG2^d;2-k7hBP^5;)b&AlJ^na5XFmu[7GP4T@4_i)6jk0Ie=Hm"Uo\7%eq<+:
%@ZO8MO&kH^)))FQ)0`>Bf<Gg'W\;7QA17>jntS`!#LAdSjmeQc3#P*NStcPRE*Tf"*A<*J'6F<^W<5#R:DfE\#0l&k/CY%:i"+Wt
%FR!+LG_-#C\igp>8q#1<#!g5VEDim(DJ;aDWtC7R5+S3%V,sn+5u''?qfVFoktU\+/3hPi8I<9j_'psZ2jXU]n9bA7DPNBC84\%(
%!'pl97jSMC1bi$<K+:#fNf",/(*,G,9gFb<bWK^THL4COUSD:Q#R2YL`^YO!Q6qM^"nnSM_G\^_:9b((eITN""2%cQ<^L)[;V,li
%AG,=[%h?T2L,'TJErm8N"$B,5jCE)]nBI[6-sdCLoo+F10H>[4L&jFSP$'PH'*ch7#Xf)b4A#/Wd=>=_Ju(8`!+Z/JRd2ZtZ(-/m
%F[rP&8G&Qt2^2-d-jHIjbG1'0LX*.Og))Qng]KYg>ilL]Le+OC"-aa?E.>6N8t)2;>pI(Q>"U[kH$)$rGD'Fd]"^q"#^Y&OKFA,'
%&HS\0hu#KmZ5GN'oMiX[",Q:lmW9d"n;<%a=rl.=R9nh>)&?dL=[Q_q6*`p8fgcm"(lQUI.Y^D=F9fFfL,/E%fSh6`NPB%Ur)R<i
%EPiuj>\_;iU4r>a!uS'S2)QW(`p%8Bb"Z9l)FCM,Q7V3N=@0=]SD3=Nloi4@l(FF4%@NEeQegZjP/0r@coM6b7Ko7f4`*SInG4Vu
%#Qp6Q0sd"''E'aJmihZQ<1"Fai-C)o8t]u1JK=@Q":J0$n2:mkiP/t^@amSmDVtZ&.JQPus#=$"nBJ>)+ajf$_c^#qh[#J5ArcKr
%V.1'G7F:\4+1S<\d'q]_lTR##=Rr\l*f>RhP".8i!MUCSO'.@f+fG/ST?LqH$>TkdPpR@].lW.Z$2+el9f`mHg;kZ&YpG(gU_3;l
%:GZUBjB9Lf6@:*3*K@mFlag6IR9l"/A?[m2!(bUl6=PpFW#u[6ej50aiJG^@LhOs3DF`np%K[S2G3.#V$5?2mH8IHZYV1H-4!oNG
%6FR!#2XnsPHasSc.h$KdMQRY4Bqi0B9Ni>jCRu2])`YA1?FD&]_'#m6)Pb5:YcH;P@Yen/K*/he!GWgJ*#"F4E^u#RPgJ6lV-5nl
%Pa.V%6t1OB`)@fhi1XtT*7!O[$%Ap?C<al[N?KCiOf6Aa^Wm[C1O'`\3n;C%A6'";)O29-209&Uh(04ip.W;i>;C_s5,6MacsW"i
%8Q[m(X_.PU8/F,AK'[/]4#[P:TpA&eUV2IC,KiB\gJ+%Z9o\<>fM9tsR<MUTBDf5N)9=%k\QW"-B'KTZa!n;rP;!ImC+5T0<Q680
%<aaRg@<rbWGiN6E`fClf<:kfD>+Xu97A?adK4AXN>md:o#.j#,Bne*2R#i%u1b)uAh(737.cdZZS9Qp$oT?]FW4)I(@6$.rW:,D,
%).>#AP)B`G4'GD#Wm3@_g^$IM0JUcOSc>f52TDE?^=b[5JHtI.^8/^K/@d]ecTWSsn2g2E1GBqG^_1e>:Z]T"1UN#$LbZ]Q<2PaT
%B@q<C?u3.s:X30bQlAY-[5fn&ka#T%"Xdk"n>J7e&J\ad(.`("ca_"j_N?iG+*%`a:)2,^Pt`!e]1mi[/5n!<8OMl"P'V[!f`\u]
%HLMr-SM=61!Gb^b&LBj'kd;ROAcq72_A>qMd-QS<b_k(DKuZ*UrCP+`6dq*d)b*o4MRmCJfYsjIoq@8&3l).JTKQDi0&]0Z5[hLX
%hEeb2&[#7o]/Ga.+6gW(-ZKVsV53pJf:WX1!m2s;.pLLn"WosCqg:HS:ZAJ\AI#rHe?2C-0eq^mVbu02_G;['`Z-RLR!]-kd6tq9
%:-ff`]l7<MGgVsI/#-SWp:7^R`j.Z@U4AOo+FjGOR"D<,'E[U+3Td]YTX.HD$;Sroh,He\S6U-CWDPsZ$hU2N7Tc^HI=ka=d.i>3
%LME4[CE+P<YWTm>nC9&tY5H_&!+lC2WNcin#BFJd>QAD&KG9s2YiZ%ETAslb_Gq^RNl/!gJ;9e?;ps.t5@`CL7AtN72QB:-Pe<j7
%2oGcYe9t(=(d/#CosSP0jhdKu#;+WCHgs=&%pH#m5)7M=A;RE0kXJ84&0b82J(0+7*-EF)iB!%8+dZ$g4M(oCJNV0%a>eSc2>Dp1
%778_)O>Xbf3X?WomJn!I`!C"PB"0YES*e\oBo&p'-u!^Ee0rJt1GGm;0kTW`*eumSWY(^,cd;jCI"oP$.ZbhO"gM*-lca1W_[!u^
%eeO142;6E3iCHmRfWhWRIbsAO\W#OJUrKoYfo_U6k#+j0ifZeHiopGuNA0H>'d`ZkURV\C.PaPMV.UbS6q,!k-@tUl\Js"S#j,"f
%EeA=ji,-!S(^0:)5g`sdQ-<0;'t3%6%T":8YFFEfY,gVA4J3Dm:7=]"REJX/bY)rUXRR7\"(I^sGT8XG5,#]qEu'DfjAiW0Q:%ep
%LAh+;5#NuVL4Ra2cClql$q5OUFkA_fZ(?492If=+QtjHVlH?T_1MVpp3hM7`#oXE0kmMr)?6=&GMR?W0W?2:`-=Sakib0/$n]2so
%ja[4*E6Am27Rt6*s7R@QJ=X(c#V.cgC0I'j;*r4N`KHp/F[T$&SuB,S5X&G,\>ZBEO2kXoH0W=).MBC1FP1-7(lT@8M(4.#FYXL6
%8r%@F+kZ>_SB<Ds+T.uFSk_XM0RJ?l63KSG!%s&!8Vkn*04eOp%(=3]XXE<5$$jA2AgNjDQjbVK/*-l9A^:R4:m+)X-8l42##3oc
%T[3\o\PVGSL@Ic\Ld7c3Z"_UdOsaq?=H3)`*Tl0MQ6O@PO1&?0@Z&#sCp="7'ub?)oO/MIg#DXlrbZkZ=crfNC[iE:j9@s@oaPT9
%eY/F!MAG*#.]uA"^_,H9XB]28!-t-n`XT?J_%9:dAir-[S8F&>`\]/D.?q%j-e@m?)&fnN,N67;R@Kq/r_^_E(G-I4D34gqCX?>j
%4VWZbJZ:?BN/1\0E<l:!<,JOQMP/8D18#(rK5h(,6%3#KUWh^2@lp69S]./Dd,EZ=@AepUk5[\V89$-^eh-U57?eSN)I)p_D#;u!
%el`8WJL:hQ#ZfnAM/%>,bh0r*U\)tELI#^!r45HV8MnU&;IBb<_(:b1],:uTJ-<m4Ze<3_7gg&;7Z)IV]dFT.:7pc`I"ElMkWeSp
%rP0ePVa'BX?Df')P+8]:do[tCat#``TfmM8AdOb<qF!R>#m0^+6Za=rB\;u\\/_b,*=uU!cJi4k,c'-;.d01=j0H(l/dpEuY<f>m
%Vj9VhbsYX%("&G^'GSXs>[KB>SNbO&Qu93WSg^i.f4J!i":V*n'/G0ZY`6dt"DR=U;`*Hh4P`@[L^U6tqns>kb)fU'@:9S/Ec(FQ
%%12-Yg5KEPW;k[]HSd%cHlGThFMMXb?7WDHOrXB?%u.<#F@aR2(nIua#iO#np-T8GNACZ]1;=-N'r<qCn3+[a)F,>1.u=P`TI\$&
%f)36Z1T25oTX$O0,_UE/SmenkVD6X&j4Tu?,^#UcWPNUI2!@os)ZWRQW#ERFW9VTo2\?E!8L&%Un$0?&kb>k)iQjg!9Zbu'1'1f1
%YS3J1K*MVERUgkHlq()pXjYjS"eK3j1rPk5:6Y#%)HJa%L]Od3N5)#4pBY>cM,oHf9bf_toIqfH9Pp?QA%:s1iWp53;o0MK7K<o2
%1VpK]$k4@o<NTX`X,eV_k4bhGCX@Vh+->=!FWqPnI4E!;:d[%_QrVYEC&)XfAf&kFKU@.3G'\<b&a`&u=uB&[,9sa_=jleJ(_i/%
%-:g>gkB,Eb(pq2.oa/"@@t1WG<<L5G&HaS_W!aWUh!bW!T9FG6Vmi-#LN4aeIfEdUkL89EO"c&A:"nGXlA1f@%=Vgj9Gnn-j]$3c
%^VsVYj(b8u._>/1"(sP0I*(LNAj`dL4XN%bR3=On)(RM'3TRWGOf.G#DHWgE38l]Q3bEnAU*@E'<(E1,H=@<C=;1[(6[mUBjJnqV
%aXUG!,m%#m1<Nl(!)B0Sj`K2p/_!HANU5f926HqQUf\XFUuJ:LMR^HSOJD$d-Y"P23`8N,!/N$C,rpc_6jSWX<!+$tQa8lXWbFdV
%%a*3^T#83g&,Na-djg8DGK.";!E9%c&K"dXg$jGiVkM,`5_+gXBm+&CM!-4!pBH@%7,N4q28(mU=91eqQ\/@K.&OVR%iDf67?"#3
%)B*DElE&"0-\jCAY"o^D%E0.kCq_B?SYEeRPg!5Z/5WK9k).7_QGbfAVCh91$jVD!F4>EpqKHVA]n+Zi+LdUJ&c?mlEs<KNkCXTT
%/O9_5WA)j>iq=!"\'Q!%e`a8jE>h)rRZG!_B(JMgId^ecb'7m9dIi8Y\NK<qm4GRqX2Z#HdHX4bO,nV);t(F)Et*GM0j>;i*@"qW
%iR&9iCaq<=@MF*!s-&/TF/$[<rhh,60F$-j.SmI^!mE3cl)64GNMa$M73,foPNVt&IVf$5TdNA3nbI<lPT['<kqeim;t60YQYPG,
%W,jVJ4n804m`3GUiosUH/D0"p,*3Bl6pttWBO/G2WoQ,eK6K>\eHs$7Co1G,#:gps1r`oq43[t`a[>PtHgV+,pV`/12Bck-.9B#t
%i)kOsEGu0@3='m[#nF`U,!&]eY#b.Ql&nf2+sKI3R%a)ODFV!H2<"huZOb/-TLNqVT7W*W("&P"g^,h6/1EqS<!5js:hlX8+^(Al
%,j![4W!Vo<#tUU1,&6No'.q<e!';;4`/p(BGEt'2;i'_,jAiGF4/)t:C5=:,j$U0VOf2[96rWhZMNcGKkf(^E$IBa>:_h)eSJ*^l
%E#TQ4XTN^;Z/fb]Pq*9rRY_c'JaMOLK/]3$NE]'b-8ZM1MY],;6M'6G=r&bb3-[dai5sLb#PE4k!OC&t>a^<!YrZ(9-(*jH6mQ6<
%?k/fjm$J!..C:-1HAEYoLpO8T5RQNN(ZJg;/9'>I5`',VM,V$*WcSF$_Ek>1(K8^/RAdE&QnM^PNLM%4W3O7u2"GA.e#'TD+h3Ar
%9Ec9Zm@jTJh`jZ2\l5@s6pV.c_'*9s#;L"s2%d6*8=4.Pps?+dZ6Em1d?cV*5DU.1`u[b^0j10$-o3Wc(]b_C![.>rW,KJ]"u/m>
%aBKf6U),tF&K);+DG0D5Z9$]+jJl[0Pm>6,N)n7V)<+ALPD'c-7+<0WWWLUhR,1Z[:Q_<KK)c:k(*$al[_dD5+dOrA7:o*5FbQcs
%At#.!PbU/p,I4F?4.mPrf_IBrKntW;O9S$DoZ#8+Pa+UXnR<i1i,jIm1ddd2ds319S"`Z?VEdX1'cigkRA*'g24)JoH1^H-6=K1&
%B<k<UXc!21'2bITBZ+W?QVIkB0VW2!17c4:VZb'r$Se.,;etgKBb+5\e8/*3?,eL"4VXWgLa%10.fZK`"QW13b1>kMm$NEsW)bhl
%q[j1P!Ceq4*a"D*UWS@K6_J`_Bg12\hNP%22!t@KYCaX?<MACn8IHm94ani`NK7QKeZd'^>_.r=6h6%8m0!l'9PFp=&u&jFf._^A
%\]=OU\-I&!K>=Kl^R)""@]1q$K>&!22_l66aQ&<$MKk78KS_\@YEmd[HH7r>OW$$7[IUS%Pun_G-;4^)e,sG>#6@.O27`p%UgO]2
%&<JuG'Fe3%"dVA/r&ldq+X[J%G13LUqNq[:qI#G-+kW1k(9I,/&Fc:5#ioG%YAal[)Q"!2"$[lglsq$@E]%ku;rj,O]$$!@;!0!G
%-kMoSrR5EURDn??=3=*(-9hT&,R@eLMO4\>;5ge9B[$6sR"T)p'F#7D@"Gff5sS(G4>DB^8lgRpB/VikVB1:No4k!9-ntQkS3GD_
%9g;Z=QUsP3E5mak+A=^'Vd"t8I\<dF8Q[e]Rn<X^N;$l8_\0&*ghh2b6UqXt`\:bHVe7F&-n6shLqul^^6]EK8b>&64LhV.7eqm4
%,DZZVGM3mh#2]bGRo'dl)MPF5]kZ[&#ff(?V5=pREX.q.rJaeu]<<q._ToqHNMFjA1dO-6$QP$[3a3k67tn$Tom2Fc]#nf9Q]7LS
%e8*<=-6e-1W6$qk92OJQ_'=8=/QgWLX><G#%?hV5,a'X#5ePLGot2ZT2[).PV8cGDQjU?D"*[#U8rkq3"lYHcJoh`J?9;h0Y>;K"
%(RmC5jE6cE*?re:\6GG_X9@,%GY>M5d?(Ak1^3<sg(u;pQXD+GGen$e'iN]8=@<C\jfo`P6m#Xh>!ihDObK/Ffgp-Ti1@@K"X9_E
%?F%AjBSMJ1/>Dm\>GgHROC-KL8Z,9=N)GgDXX>d^*.?g@;(pJ0.+'h#Z?)PRWR36=bsNWtU;-nQ-Q_4o%sf9=c)*OtahK+OfPs.\
%E4sa*`<g$b]$h0!;BlCCZbml=iBZ@q.&'-q4/cRQ_#4oK1qq-W16*7X6X>?$$i#nU0VXQ*+,sDt8PK$'!`SHThI:`:\sZYl&B+\f
%;^uI</KS4";*^R>e?I`1?W3(7+mi?!>F`"QCt=ZE2J2>&\R-s+AZ]<l%_G]/?nmI=1r2)?_E=FVBE#i%Y3Zh3QCFB4!>:Z\QFUL/
%^al83$&KKnk+2.A-c`B"$*`P<X`\^p3mp]VAW;?j1W)(Mo\$OF9?H,?-dQlscX/JZ=ID%/M<GN2SWtgS\gO1=3La-[L)Wc5$<$lu
%K.#,M*iuo3-*-kZJNC`f#,NE!-@IW-q4;\ck@X>8-GP-"L#ceo89>;Hd;*HM]HKjEOY^Y.cJId8+EQ"23V@Cdj29]Q6)Ie@Mf+E/
%a\pKDM6I;=/Oh\ppt1==H8R/t&SI=U2fpQ%TFcrU%UfWFZkZKfVFihp2<^ud:uK?i06SqR=:B>7/.WZ:?Y4+.U3CJ?H\:,s=m`'6
%],;9h^.P!$WL0iu=Tu26\HgAR0'\QCRc5G+RiOu!5A/)6BRic,Akf%T%],5RhcL-$M$_FuL`59E;<?bb>GPG+*mBue-@oi+p'e1l
%#KUrp"s'9ej+4:#1$Rg71#+KXicD/>?0_KSW04K^]<4Z%)K,Q7UG,H"1J_Wsc,\+KO\"AAn9AL3d?6X"#D?)m8n>E^=COFrQ*1W4
%9bN*5f_7l\(k^#_#A*q58OlV#l,^mm@RGuT(bT85o15%W#sSaZRL5Rm*pkE?&LBFp[$M#kBTeT^(6#a`Z>%ZH#"!1?rm9h)4i&B?
%R>IBp0X?Qo3o*o1%N&Q.O@poZJ2I?VS;Bd\!YXj`dnk?VD,fH[0eq^0qF3Rc_K[9!H$H(!NEmLZXL]=0`D;U7aJ2n>klh@^YAedP
%'D0WRjD]HVioCi,^@c^Z2EZkJ=)fdJ3iep^E=u:CY\CC'!r3CrdE+9OHT!1uo8/LJ2[f1'.GB:!0C_a=+n_#fV)/M(k/btg`/<%a
%/GGpZ$Gn6JfLf(Hf&I@4c\(!=gBO!%e0"alM4Y8>:!dcuL!p>>?TPbjAS?eATs(@:KAQl)mkd(-DNH?D3XUl5(eI:p0#o`:J,eb/
%qU%P+o?gY>lb.fJm.4SnYCCihqMVt0Dnl8Lg&:lgh2P=I\G4P7bpNC]rr)!Am/#`7&X/7kqGD;5r)7h33@2Ukp&.[9nrE_=7umW@
%eIV;ero`k(IkJ+sO4)EoNrIB)F[rX%?[VO%AYRG'-<?,f0+B%u'jhZK,.0uHrH*\+E8E'i^o&.M4@e;mkD"`a]B-enf.=;j8Te96
%G^2G#WMpGt+o0s3_M9S9WY0t+_5V`Qe"K"I9_?jQV(hErC*E,8if"XJX6$Tt)C\:uf:X5;=t#8?\VCQTFA;o)=bJ<J'(RlaWXO^o
%:2QCsF\@t=S921NRhI$_6B%(8H!ItV1pL$A>O:;2*]b$kM*^Cm\0U'SC^<7@+js?3qm$d@n0&H*VtjSDT_p%<o[J]M/As.fFs]]L
%^F_j29-CL%M3c?'NB*\gl!,T=H_=,,=-sLFH)fiA`'n_>pcZ<U6sW7p6diekMWdAPJ80CoGZq6?4qr,`lL,NM4b?@o>!l!E</lU!
%7;u.6%T/%Mr$U[k@H#iq>tQXl0i&b/VF7Bpc,>=tn[G6'4^/F:bJA:G7%)\!=^iQkp5u=+Ir\3,B@7j$aM^b*0fASg3M]@0dL4dW
%Ta*ahecU8BR-mnjk;]n_+T_Y&M2SgoKbu4td!Agf>3TpPNid7.d7J56m$W('(Fkd*C%>TMhuoraLs(G+Po+WaRT)Wj,2)jqLQ_G&
%(8pU\+.YgYckJW;RX<WWW.uXB)6NPHGe9q'4hHHd>Bn;YP2[+#=<p\L6qqHoF#;*-VnBMj4a8\W0Ms<T1,2ZA6mbqIg3!g43Klnr
%MtY?[^nhbQH:V%R'tNW*#%pA;dN49N*+"DfZ[@eL'q9$$B\H-RM\;l,N2"A"LFVOsEe%;dibhEJJX%e6E&*/Z7@N!S7smjS:p1\a
%"`oa_[DXlC`8751)^KfcM&\C!JorIU#L2mk*eHX<l/uM@\"o5W^fYU'*lp5j;qCkX&<#C_FsuRC=(*"=@$GL:>fqcbi,DX\V?^rX
%;T!DK&=(>N2`jlQ;W,kmWZMo-`[6Vm]%7uCkr6hr=KBh(Gc?M;FNM4(@a6+)g,]G8N/ne,c4`ifbU!?YW\>%"DhC\ui&q`073L5h
%3dLst^7;TiNGlZ>]&_Mc&^`P6kd*<FUq@D<91<G9[be@r&KCQ,\`Sb^X.h2mUJ%kd:$>G%*2:+\>U#;$78bmP%\[8XM=**RKoma"
%MuuDbA40Xj9TQm2+]'e@e]&6e\.AsQOQDLs#'q.fGC84>PN'.,6^1e)42gZr9Op[5+mK.r?H3iNLlnH+HQ`&O;gN<Yl8(0:(oh9+
%6>Z4S'@Fi[HH<@;Tr6n]2@_oa&"Fe76r34m-#smAF6j%!<Vc,rH;/)-#Wo(64m(.JlmZ?1_EL4Jd9V]Dr7X\M&FPk#6\Ra9Gm8nf
%#SDJb)E9nUSV-S$"?'-_9OelJ;:73']JV:nBd+BaYAXm%PEHAeQS*b]Hl,Q<_GKea41gatc@je3er7UV=l]`"q2</d]_5`UW8p)*
%]2,+U_MJ!N6kqK\<)S_^-%Bo-c;h8TnBNdp$?\,/VQPk+X5q(D#dTI)c_B+76Do2kO\&*Of#(Rhc9f\5n.Aga+74U[F\ZnIf_Qk@
%O'TInXJH]F'Qtu\h^hViFt5<kHR_ZA\,_HR>;l^AMNl3QXWk$1g-_&#aRhgP@7%mn'\Re8F2kHI1X47$h/0T7kZMi4p#Lm<@NbGR
%n<X8%Z0l]&(WO4O/*)E1'<r@c<ur>rGmBRV1FKLM==TQ)M.J=Q5qe9#kADh=ifOto<3->LQ4gc(JkWQ!g'[R%4)Y<JU5&OA-5\MT
%1Rt%N$6e[q@B"Xk`4@LPntAK;(d55O=/[V*";C[,:d#qE!B!_`^kn"VN01D_6'dIXE*;)bU>e_fU1^O^CiX'^GCq1PP,hOgU)js9
%AKFo!&iXr!>$CGKDVRl^q$AE(;N7$CTAq%#,.d%<"<b(nD/jSg]L3:OPY5Yk+;Jl3l"DmA)5YZAP=fFQ5['c:iZ+dM&eQRI31-Ar
%WXau):Eh0/HaUup/[MK+1Ws$h4n]7<fGM,U+,e4*6?*XFZTpu;AuJ]e$siRg<:Q4FNFcMY0#=$m!Ne8<jXPKmYgf(C-57`<EVV,#
%2C6\@FTKoNAPKRf/m/Y]%B_`=TC]ot)(eW*;Hbe8,Rf?;Ho>^25(ClBHUXV7[!qqZKlupB8l"/Vr"hGQFUZJofSC)UdE(TNpp,tl
%-cgX'(?$C2X\q/GH6AdsU5B7/g)-36nOdih?@<$I=9^TI=3kE-$:.eh<S0@k)9D?K77-0t7EmQ8T4J-G"*sX[@\A!"a!/l1V]3[-
%hT'(Q:beN)7^/@=U;Cgi)nn_T,%2nhARUdeE%$lQ<56jKU15=6K?jjjR?Qg9dK:s+&$\A[,d^=?5e$!+>05lNl%t#E8ZHIK3j=;?
%$IRO?j"*:Q,(:cIdSY1OdIs,tXbU$.80j<P,k:"f%kUGKlge,?;9D=fNWgp7CX43L;J<]!_(,+/mG6kjktIFYV=O?#k_7[&]m6-)
%+[^(^mUd7C+kcA$l_DI$0&r[s=2%+X+rMjp(%QDpC<6$Y`L=#6;F]`hV"uED1lW7n'icWhMe*L3QQR>p(KdJOc=rs\[;1nng)Lj_
%WO:]!e7X-%-eW?:\k'#2L_WUH3!P'S&[;]ta"m'DU>6XAf*V;-0H0Le;I`-!k'P9h5pAc8369j*dK2OcjiB?RZXtXS?WhP!XqZ"d
%=c+U71"dCWCp_^_Z>d`(O+B#[q=G$bbd'2Z7Me(].Jfe)5ZsFF/qoM5>p>Hi`4)#%eSN6/\toD<P2qk5F=:WAmo/<MRE,G#9qu$W
%`_sj_<)g5iT<F\PbLV_u6$O]_%\0;_+ldOeZ*:Z9nT#0h15`:,\co?B'&[:8NLYpgZ7a:Mr5)Ji-`D%.0'TE_V7i[Hn>$^`2@6iB
%AJq=\;&5o71[r2u-8X+*,^BodBa1moA5+jdLdck:q4)!$-a<iBS[lo&UB%Cd_TARq!T;an2<A=++_S(e/OTrWo?_+c3a3Eq5aEmY
%SAM00HqD>X8e)#`OZp\GSC;BJo2JRt\Mjo0#kX^k3!n;4'`:!-!Oukg+$B7PStH>Nmef)CAhJBW@u)lo8_E2IQLi5ail.@5Jlch%
%PG?L2Wm`eZb3S'ak^`r(0eeK!36+[]8S4)fW)f3`DA=?MIj5`Q2bn:6PhD2<2[X2o80rjS?&2@L=.Mck8\UCQf>&2V*gKLY80BrM
%Nk2l[N!;a.>:9ZGa;^.L.`Bj30qgZ!8n$Hb;d)uG8`D]\^=Q*h%GZ1gCeXIN\8f\3Kug69Fr:"e0O8FYZB3HqDhR^o7j?'-c34m7
%]rjMsrlT?8'I\@-)6q@b-UENhM9NPVgtDje@j*A9lb5HkFA.@J__&R1jC2UGL,8<$mi!^8E[bJVo2!Gm6-P_$Ll\0bQIj%Hjf3L#
%X6@TaSuOrXNCo"QX1@6g&=SKDR\^6HSP`'pFQg/&8@AB2$SgASV!OLMPeQY*W)>jdDgCf?n>=ROL*HPce-Ap/fR(i_9O`lI.s"uV
%C]]aK07LD:,.EpoQBWl(XUol`qIF7JKhXJ]92.qpD(]Wt,/E5VNge%6T%qtT,=E<&XUa,'Z\SsocJ1OZTb8s#hLqR-EG=\.-@X<f
%&?h%oA.trN#<?#B!uXd7$D:NIeUB9e3F.H<ct'X'\?JOR@gp2Y0L]*Y=[,EF$*2]XYGA.48;9n8<gI$r1RZ009ut7fiLqVt/3-[7
%K&jj[I:([0e&/uhiK^LtAX/bWZ/E82[MVnjZm60L-[Qu^Jq125R9Vl+pX.!`Lo\L9^i!([;_)j?$(Zm90ssZSW-lmI(f#C0p];@+
%a0Pif&<':nN-[:PAPn]uUI_NNJ^q%Y7,N\i#+@]u7g*f7R75=`Ed1\'0!GrX430g'hSJ8CK!1'Llk?%;1mF[L9:*^/XU)57$HjV#
%;-a0'b`nNKmk45ZVO1jJg5$?FCWqqQ=XiIgA-:3cH;LMG:WBGtKIM[(-.ius(_V1]r*EUL:n+&`"u@BK>dXehJgVMe!m=A=/'$Cs
%72:T94BP%Q7Yk\)(-RkcWqc^=KPtk=*f:qAVLtR^7HUU<IURr_`7@W$33l=i%%NU-Z5jcD=_Ytb`C0L<aF;LJ&e-7Ol!E`77!C8W
%#I2H0dcn_(krU_?6EKq$m<t=)fZ]AgH36]SnP-Qakt.c>0d+k[Y%Rs2.'m[mHa-8Haqt9"S;CPd>WOm_O\5J'=OO1+!/u.bAM6H1
%M@7leL"-[FR-J7#7@k[9XePY*66,oa*H'+kf&;-PiL:Of@_,r>1Qe`[aMifgN`/rhlCcOHFE6u0W71AN;50!&Qp[<rR)DW`6tcL4
%o"a=h.UuB8JR+-g[:FY\A2jIQ)g4cuQ8rN)X==O=Ter/cjY5>n`8E(<A22Io:5^g,9TnA$KZLRHRHW06ah!oCRr,LTaq\bDofq+%
%a!dmfe99q+5"_Yh+aOM*+Lbn?)pe-\YHM85Xt?U_m>g*/L.J[COK1K#d$:RlNYKCHoSem],5sir=jcSEWo#*rOHoa,R?:<6#YV;M
%jP*.c*EA%!9p.d:pQ]cH$gW`CHdD,S6K4ht'@"FC3<cF5aE(I$lDn+A3BK)>_);_2a:>,9=N$#o4UcYb,'S+;,Q?e]@$u(h/3Or4
%P_o80Df9QCIs60ZIsLX`j("p,R*tmc5Q/]B^Z0h8:B'e,s7j'1?!]*grU_K;@XfKulghg)qT\8CDdQl@hnQZ`^V@Fp3P51I"98#H
%d6IU&qW_\ga5^`]GF.4"]>&UfcUU3,)Ld-ZGP88cq6u\lYO=)]\4&CRQXAY*O+3u*])Ua$$i\1ro$74#T9'!5bGE[W5A`=TbLtE-
%D>!khlhbUmBh$KQghI^W[eokq:ZgDib<LE%^LB*7\?5"sRET#>bi>n=4bXtY9]:co:#Y\NJ,T2nGMf!?m6<^ubL\9,6B/E2RG2ZZ
%oA]]IMq`G7YEptmEVnP]RHF-rH.Tgg>i*5$0tE:p5@6@7?/pe9n*[o8GBInHIduCIO^ENU=fh4s9^(X4bPTK[oB/+.1nD,2mZ3]9
%(MbpUU\32?bT]r6G[fR#]tO3kh8TD@[qt@KL@_):[O+L7n\^R&]6O5@C<_ZkL>/.lq!_O+hHg0Lr;Q%9Wt+(CGU9\ADW(MB"91'=
%]_LNBR)UuNpFlPIG38AX_cq3Zh8S4'rU&EuN>YI"Fk".<I<TmanS`VahgKis^>e7'DZ1S.pW]j^K$0YHY1Vl!_Ue2!amJbF[r3qg
%f3O_=1^Y"!5!4@Fq8L=BUX'FMgs/XVTZZZQ'_Xt\9*\Jb+%)<?F*G(6LKD7(]mP0'k4V-0>@D)'k]4;.bH9]r#@6bjb8k]8s"5ZY
%k]6^=\m0PQ]%j=-m#<WA*+".R[r)hjYKqW:nX#070>BYsIJ`u(m@`3(I7IU9<cVQ_41R\rI!bni(DhS34JVGJ;KCp^gR?_Q=7;`-
%WW#@]@%1'0^R`&bqtBUIk)8D8\(brpMW=DV%7bfZ7@(Gq%VqZ5f3<Wlem4?-k?bW9hRpP3J'Vuaj1G:PT%dT.Epm&A.c4O%^UlP=
%qrM2"]=kbb=gKiDJ$LqOHi<k;DK"CgT7,5e`_X$#^D5-;Dr9Ba3BP"No).ihNBqcpq6oF.7;<9cR@`;Bh;8+rpYS0<:i?V)5HL?L
%ZoddL^M+U.$\%j<\bkrA@-G5B9c#s5q*,s-O8iCg'`YR0FZ?Rn`NC`aIbeV;=6m=EA,Q?"o&fpnSPeV6T5GKQlGIhGm!17<SepQ&
%=NuYGr^loVPKh/C"Eo-)p.a`G3%r=TEI\eM_OmGV*mC*>NHSF1q/]dsLUb#XXP&Blk.*2O^YAXiL/7bnMeq\FmdgA3l\kWt%ctBM
%)./i=r$,PESUVT^0(4:>G585QjmY7tV\8n^QJR\5Dqj'ohd"t8l%2rV0;Y64([Yf%a!VnH+j/ugP,%pub3-=gCd#ogYF*S;gB1Mo
%amKE0SD;t-I;sINnR!gE+c!^iQ]S]8?`cj6^,p[iq/4H7T]*>fr:T1%fc"5S*pT.H-.HbM*;1^8A'KK#aDm<69]!FqqsN4nX4ok@
%GbfLZ%6:YtSS)FcJU+r)['DQG0aHk6pX($g5WnD54KDc&hCJLUqfBN]OJVKH0)-JX_62LH?,%53m/DFVmU%+ImqHtF2es];I::PS
%6EL3Qh#<l#TRD*1p)Ka`o+aaZPAecbDD&*+]XYO4?`-O\rlMU/g)j@?%R,>jO+4I;q)oO(28;n%;_PSKF7CHmTN.M!pOcti=`LP:
%joo$HR=0?tIW>2W];r:0MsH:jqqmol\!KmN42QK-*rY=^rqu9"nXjU8*]2[:6b)I;mJ:Y`DM=9r0@[UP<nPpqLWBA70T+WGE@6ul
%q4%+![GBt`X7g#SYPO^3jh8V0=J8OZc`LP/R?nDio4DPfd[SF\X*#"9=52KVZ`EQ@IX"VXffYu&B6PkeW3k@2Ph_/\Hp`%Ff%:e8
%h6s2$Fa8%J[N_l%dA,$;F1Wb]G\Xgl8_r1YXLLE+kND<:hn:E"@%Pm'G[&Y>.urHO2;h$RC0T.hn]&kdKTH/!a8b+Xg2+O+8`!aW
%::jtaY?tYn?iFWee?%oc:>cIBl#r&PFM$""i1%@5lS\,\2qt?EEHZDK]o%C>r)8U5"TYkL[mMF`:+0f=0.@NFc"-OeI'L_S.''db
%f.Y,e&nTrE;-n4h_"3m,MR!GhEUR@6f'Lbt4?\I%PUH1d&<$,RRbnJ&?r(6/?A'H.MR2]!/B[c]MFCZ(R=0?t?=YUErs+?K=`k!o
%S\)S6=HrED'T98/1AerKp,s1&eR%V=<I_!Q.-E64(1ka$p>sm*QsQ50N2]sfQQO%$[^hb%%A($Y_X"E>qZfm$59',a8$U;N]I<]p
%c[Yod0,PAblRc3UG&8tb=H!_\5?e@Dq]ru0>@jlXLGEr/CELIGb(6au=FpW^47KW[?eYt&p<1(%EY-i/QFM*(RsO"-#Hmc]c.*<@
%g5?Tp%UAD:ec)8Qg?]FJH40`>?!EI,9CTF%AG=4IGdp3bIuWaP)Qd+n__jJ[UQ%'nh,.JjN@:"i_lA7*WU7-iZ%Y49rpm6/=22L2
%qr-i_3?)of'0=VY>-UVb2m4mk>i%SMX)]EtlFVt\qd6GACm`poC]EiUY&Bq$.QHA7*,oV@7$*N!#ashmdlH@7C`f"`Y;pfR!)2u-
%n`#'koAnE0N_8>mr+Ehdq7QX74'MX@B?T2Us8UdC%nE6=a(9T9aN#F;m$_V[Z>077*>I>s`949Q1qkn7r9L6ONu5mO-2$JUq-6sl
%Y?g='+72,Cr?U^`lgJ`K/pd<]hEAoWr^M@F9h')Rg$L(%=CLRno5jnuGr)DHf#5iKF*K;GBO_NlM\^p[F*MSo)ftM\]annGeXs=l
%X3U,cn%ZQM\6m."pZco!3$u9cYN0F-B!:(4:B\E,+s+jSF@:N;hX4)lclpm,i;_Uts6l!Wa`m7SmOHsSpjWhRhh1t'Dk5%E0$1s0
%qtH]&_>h->ra1s4aTEO<B.G:MK?na3/LEPss#Kk%%mJm'chF1<KXF>9bEYoX)m-SlaF:Y/]"7k>_c+1Yr6r.L97u8G!o@0:]8&W'
%RU!<gWk3ZMk2"dMPM;Qqosn5C)BSidCJr%W<rVOTbp"dB4dDM]GF\EpqFdmPj97\=X(V(Q1W0b=Z@dkt?m.U6aI\dK9=9kpOOChC
%7+m3\m)c5Dh+T0lb%H48]=>BHClo>`5$K)fb<Og3p6WtWX,Hl$mOhgqG'eFVo;l<r?pEBMlY%fmR:lPBH2[11\QXk0`t0i3FUH<L
%l!.IFs(eJ0ChgRn1X.R*=ee,+*kBhRP[B6@EN76/5EGa]p&)F[oAsDqqrM2">Ju7req_:1fpYI9iZ;L7+(8K$r0-M2U<fgD0>]Vj
%0H]\hq$?_&X19=a``*J'Q\48Y@!F[K?CmP6_=2J!]Gq>5TBt5W1Q*)Ve-a+eQ\47.QSm-BiT29m0AGNE0.5%XB+.=<Wq*OK(8M`O
%2"=EmmER6G0.iB2-O^KWMBNtT-Krq5"IiLLLKt.S=9I'WlX1BAp>H!0Z.1epPE`i7nVG$;IikjHmsfh1q5b+mn2Thi6eh:SnUSQb
%)is"[M.q_ea7TjckMO7>e:dSYD*/pp]ds;`O4`%dqU,CmBj8XMo3["GjgSm+DneLNWVK$b.=,-"q(&\W%U&Th,80?k3r=e$Ofqhc
%.cj.S3a-=]n/UWHR)<9TrHdfmpUYeEk3^,Nh1iP"f.1"p^>8VBnJqQ@Fb'M;m_2A+@+TSaV&%6<BP9%o6hAFum)(4CqodTl6IE0r
%[ba(ff1b>]MfiH)eo4jB3VIa;FOnN2>83pb9k')Y]1&-2fJ*kDb6L,K*bkc<@)pnESS%?X)!qLZ1"OS-@)po&9m>L_r5"D5Y[d%R
%c5c0f#<TqsfJ,oa]0BL7>BS<=?@mWLrZd34Co8o(h:_W&g!t"Z0A"-ObO7VS+mXSMl2G&H?f%D>B._n832t^U0<\Df*H/=Af&YVe
%(#V43S@uW2e_S4EHZsIN^.-4id!f2?4'uFMg)EH@2&KFSn#h/LgqLP)3du/1?/D7W_XdeAa<(-Zd*Mfak8tA.kCnHXX:Z`gfK;r)
%D<oh:ma[2X:A;55"T$jnQ[%ECh-R:1Ogs(:_;iu]4[''.<EMAIL>[I+i/9re=X@,;\g?;KJt?f-<;UZ,?3`j^)U8%'
%"amG@bh`JMXdRCnLO6.k_X7YUN@4SgkHasq%YY_]:q6Oe=E^pes*U.ana>M-ioql*+Ck>,8?0GD(;ET>oUsCpQ7fZ7jrCb=mG+"[
%2L!O1%bf](S(jMm[mF[hBQGgmRla#=m@-;K!]T,3cL$3E[pl*BlJ+[r4/d3*FmbpJIA@(3?]mIZC,1.2c5lcuT[9g1`9s[Js6KCP
%oq1Ztei\0^II8EFF"aWq_[7R`!6DBm?"[^MrLT7)gO!1^"$YiJ=(2\[-+5eDQc$Ib*.X"8(o+8MEE*>ss6DUFBBOdDlps[Vhjp<F
%hZ*)KX/qq3mshiNA#7b"gH'O/lU^Kh4^'k<ICVh^U4]qA\*1e62qOY&QhZO-61(0Y/KMK836RMP`S:*u?Jarc9jaKaXV(ap>&W`"
%IK/]7>l?0)#_)\8VDqB>i=E@S^D4AX`AUM0Dn$/O)Vsi?0=EaM(%F%thqbt+424mE-o[V;'\L07)QV&km`iAN^+ep^#<MBp`SEWQ
%YKQ6UX*fKLEa'Je<Zps<>V5tkV7Cqf]<*OP'=7p:SkgXrrSiuIS'uKifq3c:JM7`seL,f1V7@?(*S!W/q:G(QD_>'^#*48>_h&C'
%q[o";j.@:of7Rc2%k-.[<$gO""a7S#9_BMBm=eAYOGSGYJUFHp@'>9Y(MCVbaEmA<roSUF39AuRSUE"*Z/<Rh'bB2oTC[ocRmE%B
%FhH;LiAp\#NB+qJf&H],>ap^5ptae^28&5#>@^`HGF/']?=0c`@'E]cj@]m9?#QCmrV,jIo)AArQd=As%ZfW[1ULG;*7m;(]Dadm
%fnWj_*7sPpiGmXu#/'Qri^[b#LUV:'ho:)O+<QIMm]+Y*BmAV9m)LIo`Eo-lQW8Y7J4R%SLdWm6@.o442u=FQpo/&CD=]40F'p2A
%jRbZ4rFM3(miVS?B(+)c+)]`V5@)lR96Vmo.JDcKqR>S<Dr25U[+Cc&ea;12hR?Y"bpHb__"U_M5!FBagc"WJhIC(9Y7Oo^*[Ke@
%-(ndr3\/Z[VXqF7;a0P+DuUl29Y@mf\rREP?KBqq""+Dc&nk0mr+$)R0>^Sl#CEfK2or0O7(\fVoXn$OZ3g9m(@Ae#rK?"\3br*9
%\[_OejkU^>+1nu!iFmr0q#9WLX,I![COb,Q]rg,Q62^Li]5;SP?bO'3s2/0KO]V4s*?C0nrZ&7ogWsBl&'Sj^:>$Ff%1Ze]LN!Cq
%G^"?&p<B$u$9NLp?_$B@j2A<qQT@>l#@oIr+#9na9%!sna&BUf+`Eu[>obbls6b.qKNN\l&Bdj+Jr_I7oWuknmG6C/]QnA5OI2>j
%o_!-@DgoIn_@STF9)mo1:R=$3^\m^O^\nZ@n3;Bt_t>st=60"brViF=lAe(.ktg*^=5X&M-grtnY<2S$rKhl];e`ouJ$e!/+Pg_)
%Kq*t[?#M@C+3FC`NkRQLh]Q=jWupPTrQjT/s'06p+/Pai*;uN0l],:)TDe$c5Q/]DmCBs2^;Zl$?%*N/Db&EPB-.6'j/^UD4<e6]
%'j<4XoWM0Y9Ir"SmuM5Lm?O_$*1_-]Aa,nSV2*Xu/&.$iU"/ofj[a73dakgL,Rms.Q&%7OhiCO.KD)>?9HBAiW\5kf[/?rB9!mki
%j])-)In8>dC?[<IgTpGe\Du@OeX.9f_lN-=6Y!UL8E`\S=*59rR6Gau?hrfuO8^?hcsNX/WT1FGIJGO%LDYE&WR[(;':8A"7-eP)
%@qn&_S6e)9,LQ+`>-bM0Y6%G.0AjY#7VgU^j]K/Jj$IsDpLb@dlr?:3qm&u03?`Vj52++i!,n0up[uC.mPih^cbBCVA%ui@>:)O>
%L+g:9r;?KKocJ)?LS=HT?dgn-H9"O"]Q\7]os.04*3874=Bij:]QT>8BQOZNd[(V=NIC<,F?Z*L9)gC5P[ru</cVL,/\G85W\XG=
%ZVE<rR#/8AV3Z7i51']%DHWjBHrHAGfohYHp3g),T(QV?T;_q-XF62<YBk<VEn'=6(>Yq#*4""Fd$HK\UJj0)Abf]$7dQI?[.MI'
%LZLoN\d=tr?HRS!kQGOqs*sb!D.g39\.6/1TQd?(!UP^ojS8d_R>i*%Ql\MS`aengTSg*E\=4JbUbI,la`WEp?WI%nR<#XQL(+/M
%F`@BVm@B$R3365%?>l(=]j4N3<SqfqiiB-QeYR\0%t&6/]Wnm$gk&nhXsGI^pO0cflWqiWY-p4O/35It+krD(#kp@%FiWr\H?m"C
%ch0Ia[q2E.OH!6lX@Ln;%F-<;C$4+!UEn\f=P'3,.aW=uh4&cUit&f_c^$R`,t"At=lg,SC1j(H#b@IMq7MT72#QZWnY7DR`"pVn
%6QJ[$Bo<M'=Mm)YUTJ]FU!UfrVE6jeQ"o"*8=4s+NWqRdj;n*9cM]XXm?cT*bldLde+;-^Hg=)62</%LSi7JV/$%P'dEqg"FEZT1
%=hhi;F8@`$g#'J']g(k`s80S+(]3%'r)`@dj+#E5r.Cr]F$HdqL0!?i-T1V9q80eD8*\JMg(NH$/o>?kcg^>XIgSWO.P=oIkBR^m
%s#a5o(DYO^ZLoW'Dr^i`=VHKXe_oWL`1r+t2p^ju=c0aS&"`ZB`XRCbmCoJVCb@16iNk.*Rd]Uk7cF-hMDWgB41;TMZ1[J(OgVC`
%d90McL"A)5ot8O2rRUDe:UYEnk[T!.*l[[N7I('([eIad%t;Ka;u?aTJ,]XMm*-(<4q@1NZgd[9I;O#NqN`.'8<^2_q,f:el%Dg(
%UX_QXF2E;eZ"BH[EujU1Z]om/4LOKKEuHl[cMAS&&HfC0Z`@LI@Wfu"V5#VU8<oXR3:#q8Q+P8KoB>oAS]?mS^CU.nA[?.3SFp)t
%CZ*^gamm8FMbn*q8hYH6Nm:eF7c$+o1*4^_-GSQ!dG!"fj6JF`CE7gXf'm=reYXt9ZEO+.rPKPaC4=I].q(Os/J-.P)Op*W[Acl$
%HOBuaGq)JX<i)e",H%p65"d3Vn]bqIeNNlnmXOQ$r4+>nS^'"=Xsm#U6H<K+[;0hS^8E;qfCPK&)Ju*.7+]Pc;pN&7aa/E=eZ`/9
%>c.S]F*%-0Z?Xn@iNupFE+3<T386S"BBRh5JQnS^Y;#.@HA/\"j1XYG:tTd/4T`!8jhp[P4S6A`*2GuFI?rH%`l:\0:;a=@c8L`E
%J.XLe5PI&X?J#Oib76Mp]3*^,pFVJfFV&opCmDl^QHoq?bo&uA'ec)DAd#uWmjcS?$t&8sQJR2,bj(?-3Ho8Z*=gO"Mm&K/%oZTM
%1CXYn=1*TXA:.i)8sf4@O$WY<o9IA'nN>AYMgan"S_Lm$LHEA\F^4(>oOK'biPot9o?DE;LmJo>(.Zo/FUkmI2n(k"S[l$h0n$/o
%Tf&7lB,u$`/0;3G*(DOK=oXkBcSkD+Cjc62Y+)0o44it<\qO(0Dk"Id+:?[sap`SP;rp8Hm%U,c]Ho\P,H&n-8,44.j+CO,Mkr8A
%47ui$9dVIc9SP(2l^=mp&)a/D+ZX"$!?aLK>>IA[36(NU^3\5b<"!UoOLH"gCFf5SRX]1,]X=6cgim,]UrSnh&R"?c3rM0gmaphF
%H:[q*@=..kSgVkCcHeL4/s`X/>ZYU)Eu4W2cQbZDFSiQ<=iBKrm/(`Neo&NuY04_kPsZC#b-:ufb>CLHAa/68mcUqJb\<E[Gt)'g
%0't^`7R4$HnlUY?7#ndd[_;EqQdi+)M@TLqY%,/d3^)eN2^I7=lMObik7_;tpV*bO>LCrJE@-.u^rSa^:&InOIin57[JlZi_q-LS
%*5E;ON3fXT:F.S6DJ6'"5BIXQH#J)lLACC$qK"Sbi)af6J%RUcVsbjRdG&L6CgR-3fPkl0*mP4<c9CEcS\M3cD";)3L)YItjbQaZ
%C9m,cYWI=*KLCi>PqOf,<oDHP:[^rh6;B$`f$Q$RH/2^"*mKGtJo9Q1b!*]6B-m!>@Ta^s`St5^HX(R0;RQ9g&"fUQGn4udA^)3g
%I7^Mg#q_&9VB"54orcocFV['KXZO"u`"!inK/!`rnh?WSNX;bMY=0[/;2ti\W$Ceg7<[`<O3c%Z$H%/^:CXuEX!`\%pfN4b;%qbZ
%BDG#S&X&mJ'omn.Q9lXD>'3HPM%S#B1]?f84#YCB4J#uBEtD!t$gl**'YX3AS3>C#&b0WBd.P7iB__9:JW`efoUgglEctQVN(hS2
%a?F3Qa-HU42';m+f4Xfr5eMs)j/9o<..A?D2-^@Zbh?LbB\OS:95B!Vq^o#Mh@h\7R"M(AhC*OLc\-IFV4V'Y"0n7/VoNurEZsW.
%N*EE%$V+>ODST'`I@PB>aA83cFA5B=fq3'Q*/<-JrVR"?#`eTNl#hsdRUQ?E`K'>n9%X`:'`beoh2BAT@9W>&j5L@!PfrlZ,m&3J
%]?QWIlo)@YEdNisb&Y21W\E>YY&EUa2u!+!%`DR!kGtNIXX!Z(iV`j+4`TB/I[!Ze%mGX"$Ecbnj,4$0B+_BI6B-I:i\sm:)jeIo
%DNYc.#+OZ:pR$S4_`15]!8kKqhRk]%]O'I)MZ-"U#d0"@b2=Ne7qJ8r*&F3ZK;h_M'@Rl_Cd@Kh:Ak#HJ,_m,k'>f69_,O&&b'.e
%JU&NEI#IqP'[f5GAO@tt>5te[pP\@\W$18*a8672i;\(o/cCZA58j5\rP5?)Iq1cliLo#:g)J>mRr7+bI)P%q+1nJ"\tA+EZedq&
%:1*V^hXUm.oGc]Y0n8-[<6ij4(<Y;;a4fR&3q%1)qb7%g8':"$J!ntJMm%XkcPF)j]*>D-g<P\P9nEpA/f$4Bo(V*7S]Pl%j30pb
%/=pWdb19uliu]JTckKdJV'E$OSa/Z+Z6I\;guH-[0<\D^0KJ&'N50:;AaK[m55U9\G']urc;h@G4\&f%:hoT<&k.DtQ^tUnp)#$8
%(ZF8,#!Dd/IeN7?.@uk:pr.OL>e.G"97$+hB$o^fG1"aqJ,*^VnE4cN_H*4=pqEd>lD?W`EoU15G2IL7!j(#SJ-mu#6K>SuHonpQ
%)9't\1cB9SUYIs$/W^p;E/a7fHm6FP.u)"dAe:g^LXO$ND^@9s\`Wt=B@qHrVXJd(\WEemcUU/'`U(lnT)6)NhqUngr'Pj;gg"+l
%lBAD2_P<:^aRa5$k,@&R4Y1fPQ-R^phAL:Ej^u0:h(K0,I)hSSoYYPfh(9%Gl6hUm&snoDX%tXj&Ir5:\.Nn#KR(efXe=:P&WfW?
%Z/@Y*R+J+!B,-4F2Sp'.8Zg9tY[c=,@WF*+&X6Wj1p/9h>bl07iU_hs$2d3dZ4S-<2Sr"Iqoh(eqj3:]*cBY>Non",o+Ba_o+$^@
%B7I?#&6574a$/,f<=B-%FW\8ta3/+CYt.+G55=)5#4Q/6_G+Su^CPT]]N:+6?CN*33W,`_!)FT&R].pE0^Y<`j32^V0I-b\GLHde
%%6L;oLVS9NBYib,Hcn'-J>V,M]"S<#4_!cu!,*O/da9urK'!Z*d@-9:^s19IEKl-,09_7V2s9,F,22qUB71lm^6o]r`m[(]k9sGR
%SI[i\g,o#,KP=kD!YK)<(A!JTIai-aA7?>S4rgTRZS_aG\<2a*'a-_kXi*>UMF1-`_Y'sNqt;eX\Qj'Um6AAKnE$gfa/7Ht7l/CO
%#<UBb/tIke(IbOV157,=Zc=iX^:97"^@/uSkDB?tn`"!d5rCi4B[<Ue;ltID81O]OX)#sEFRJ!HUMlad2ims1b(K[@>3#h%<SqN&
%m!FQ6C8VtI4pPj'/'b]?#ld)])QV54LO:"A>k"45DT22drt$]JH?!ilB\IuU1iIrFjo5-:"qG+LS"g#8ALqa/g>VK,l^1'.lX0N+
%hf5`eqJp6+gF%LI$=D)s%rTl=^YrEcp@8O+q>OtJB6-O+4NdnaW%iSQe:2sRV6!e<65VQk!M9i!pHQ/TE__8HC`1O_^6*&=@cYuJ
%]nD'U(]l&]R$9T$eVT[!F`P29pP41if0f*6rr)Q:H&VXY;nLtFK/\]"IfD>T(B(NK_;to/.j`:$NEQD!a^AdJ!It-[kj4DZ,1M*;
%6R=W`B12d45_Cm"qmT#!55f9qpH:9l-)A=3`a9f6*Fn-%<ljgSra5-[]ajSSB:Qa6g##V9nc+(3GGme&QiI':W"o-I]>+=3c[YJc
%?iKnG+ConMXq`>uBXEG,_ejbM6%7d3rRp)"5DKu'DLDBVqtL[Bl!qg2F#*:t5Pq%AlhoG^\ErV=s7I15MdQD$^DMHhmI8Bhs5o6a
%n39a*nlLcfQ)!>I,E5iVHA8[]#%lcqb\Yq<a&"8!5P?Z-JT:_M^NjT*B'd>ErQSVmVB0UVGLR>$cBgPcRrILj#Ft7qrjSNB&MddT
%oNMf(R-8e84`cS3AapS3+7\69D$cA@Q-TF%Dh%Yb?i9#05QCC0IGiHOoN/g*q0/J1Do&FHM)IFVM)"`VY@[n$rI)<d;kCoeVJJLO
%?bOr,msa$ubK!RRK$f]nMgtVhnUH4Q_CJDCXiQR1<;f[MC<RAF1NRhJIOa05Qg[C(YDi#t`;A/kBE[#ap+,2'#Tj4@B4lKf7?"8/
%Qd&Uil(#3fp`dIh^5SK3VB"XLAK?U`X_c82nh8&`&O*5f&%,@<GI(\mh`id#5'r]Bq&V2]dO3eNgG_X"p=lbZs!)n)nAU&PYBA^1
%A:G6KAacL?BgBJh=3neZ$eG'bm'5u#hYDt0VCtF=0t(CYOjX!L#omSa=770]"4_VeS=/uPp/3_f/>8pmGuisJMm^rB<Hg"CQfJ)O
%+*FHg-VXG!(M2nW1`p'/E?;h&]]Q]K8%tFQH]/\9fOI1+IJ]<"ItWk$N9B1HgG'laN;W[hN!^mf5ZK>L]KEu8H?m"%0=qX]P.M,_
%qes5)rh'%^&lo*0#V:Qb.i]U:gZOI\a"Y<b9b2-dV#ZsKJ8_abU8Tcg<n=*n1S.T?b2-(-+-4cp'DVTr)!<uI_._CGD[cZ$^-)E,
%b=Nee<?<!E"G"RBJ)@*#Ldh%7Nd1:l-P,\&j')NdRt\@:=r_=3r/:)pKTDj-KSUd)VXlCj-?tqJSPT,1n!BAZ"@hqC?Yq=TZOgqh
%m;r(DrD3Gb\;^8rnHVo[.J=eij\iOmDPapllE`WV?FO#G@qkbMH?8j'@@7\17mR^Zh,t?m)>Q%_q];fRVE?N(k1u6Q%RL"-OUs23
%N4nuIpcm3`_g9C)>2`*5>o"r:n1K.XfCQ9,I$FH<q,iB)4Z:J2in)S-KWU$pWm9mO]I`?$ZqLR1D\b/e(Al*,Tu:rT[e=OZ]%60g
%CFiB(*ZOkL0?=)Ip>.hhYO-luSK):mY[0K!I"+>`:*_T%^#r@e*Hj%@4C;55E-L/OU]3=NNl;4C;F2),KEZL)Kb8F@$[n#tE3O^V
%LEjKG[tTW%%4bh'^\u=EJ*LrE[*%WuUJbO%kT=kh90(-6o&C:i*q@d$PQas2^2Dupl,k3EO&o@5dc]6R^#ItD27R0&oC&nC^E!1_
%9gJO5()85(^!!ot0l$X<MH:nG5-;u/qY<.DrRd0*J)gS?U/9t+YDiK/s!X>N7*+q1(smScER^\KfuG"[?@Ii/T)\Mjk8W?Kj2KUL
%D#>AfV-$]i9f?hRgU,%1>+kTOZ(k8.c0&_h[TP\@NWT>sI1kc>AGdNXV(se_kIU1A]D;E'Q5_K@mWKfI`.!.67<tsHg[$tu?]P.2
%HrNVEqV8D@h_W,HB=^rijn$/mNlZ2BlLXeWa`?4Ba\)>C?]=t/HkD!Zm3UEZEh5c#/2l]D8asNnVE1>fj"o0`NT#A?:I=mJS>!:<
%%<3(i8sq[@<2fQ]]#0=ZMW^TP[X5)jO3kq6_uJu9$*h5$l\li"N\d)XEd1RNL+KPPG:q5%cNZbaP;cU^XhGeR:]3`XZcG$\.t@)s
%n,-@fRb.$[FaWU',K,cr0AMjXSH#U=i'WXfGUp$rg:t4)kW[=''UImXlr:uE.7?/bq?oQ-j-Tf+q1M4S<S&&5pO"Nd9%=O"s6Sbf
%2u_/&_"b)hoK)urXo)lDig\nuYLnJ7:Iojjd/@RdkF]&M\Y>0s(JS9$W'1A9k`_WVqIqKn!K7a@s7>F>69T.7GoX0h`si!S5;<!k
%F.\KmO0%IS'"n+APOMRRm]1!lmBc`aY5$^cpAThK'.Au)HS(/V-%;8ga#CkE*^q73T$NumX#%]Zp,jFtq;441ci1`LT1mVTnGgR=
%rXdr6<thKEKV/5*2]1N2jnb<.s5!-mq:f9Vn%T()o"Y/%YC9os`rGEjpNVJVWPA;khu;oUrU.-gm/PtZ8bhkb0B<"\s6jlTqn&2-
%!!tYtcb"4p)m35`?pHaL,7ACIH/j[ZBd*K:P6(DbJ+p")^\mDY5QC.Q^]%Gbp]('4J,&Y_s0tJds7+o`c62TTqKK^Qc$qmKr/^F`
%rp:ea(-ku6Flljc;#fKiQ@12X&EG-pk1nH.l4h8*4PLefjqF`n!0\!G:WUufOc&@"o(nG,$]J\Q%j,@U"5$0N1XI+r71T6o7VLuR
%dX@K"d+K<[?9&34;IHr/l;S"^Z:c8dZ9at8S)aSQ3mS0"kVd\RR#.4tGgOFZ9;;_2bX;fIqqDBD3O1d>l%BDTXF$\9GR$>H[;5X9
%jC1/F=FkL@&)r3PkE'OH>/lj>oJi![A;sm+2&<];39.)CetZA3YZSahbIPn$U+CN'1JQNq)]X?$+FFcI%>)e.n8)*4RhD!Y"#Nq\
%"Xn"AEdYb)HEIB4;,JM.!,nIg9,g<O6plr?&bW/]Q^p]j?/>Qe=_5roQ)Jsmp7VoKZgoG*JT./o6k.>.E%>"Kd)Lj_Wqcf4Yt-Af
%9V!o=oBN`'1Q<0GD/6X.eKa_=k/5tI)PKj](2%-gAoTZV+rqgBl7U&$\$L[FAE&fR5@R?MDITq)Z<iJr=PD$HF_ef,F`u<g`5K!T
%oKp33".Y$5pJhjh'P8=i/XE6@?PW^Cqrm=I_Cs!AQ:/Tk%>()o2CN+[Er5<lbW(U:#Wa'C6+6/&CQn[<fH2:d=K!bS_Y\3t!`o+'
%AAn)b/&U9!9@aAQ'Nprn"b`39Eu(mtR7($%jQ6nW(/rObF\Ne4aGCQ2PLJsi$;68D!R*C"QNhEm.2Oe!k+YR67#H&=<EN%XJ:%Cl
%fKb9)("4;TQ]"3mDJ3-?b/B)CLgmn.\N_(3Ef'iI,JU'HT0V3%i@T`A3?_k\U6#rjO3ku>`E*m+&3>[ZWNr]$D<F:"'fKHqr7l?k
%mTqjp?OOUcLK0OuZKLN/>Vf6WG`\mhcL&-*J3tI+!5e<7kRGi+DQQ8pAQ810'D<.k[dkGHLujKQjNY39=s5CjRsCH[KnGPrd*%N0
%IcZX`>Wq-1WYlGPEeUpfXES6(M+>Xl$qF(I>Jg2VH>Bk6a0^>:,U`Pq-2C^rIa2bGW6D6tNXutE;*n6S&JfY+;>WZu)P!Bu\.anh
%CmJ[Ah91^DA;pgR:.3[6=bD)<nhD$4mOLo^.82k*k>78`e;'=YI0aTu!$=FO1LYRi*Pm*K-$c"/b[XaGM@0c:4?pguSPS`:YN1Tg
%)+10)<apBt+P$Z&Xl\^\P`F6ePT5V?;+Yh2Y-<)qn(]SfqoEPG.j"E@4%CmapbEbl>2pKcmG'!eL=!]@3;<qu<c><m2.=l5`]a$3
%&t"@':.79`hYrMIX[dW^+"1[ced/7VqC(dZ]75i_ZT<Y[(%8i+EbPH/P$^pN\QbV9!4@.$H]!UE+@kcYpdOhq(mYpFoYnJtFAu.&
%;mhj[FA(Q=E1]K:jl4T!LF'L&OW4#O"2le6^Q*opCkH/lDN/ikZWQ%PF=E^,_95Zo5sT]J>>ouW@Afro.r/o'(lh9Dnmrns0l<]U
%#&bg3*n't+%d(j]GK96#QhN;Ma-R;L/`'."e!bojCMJ*2Vmq-":>qI]r`/91?GZLVAk"HD^>rpgj,^c^l\3Gs-t5AWQUgA@MNg'#
%a<J14163.7/3PO(\=m>pfOVZ0?0%`EpSg0*jsDB+KZDs'Z'k=XYT'*n2FNXsjH0F&!kE:TQtOUbq?Y[Gads^ff+AMCVK`*Rjm_@q
%AIu_KRo8iSQ$q;TCGNoNCjSr8$;q1>_KHDf#^2Oe"RP*CV/-,@DJu"#HgM]ED/BoeOH^#%Zn<<F7F??3Otn+&9[[_+iY-ug"81cA
%`;jY2U@^O]6#U_l`p@5A#&RqPG8A#a9:KFt9"`349%3j]R@7Q$j+c]<m3P#dpVQ]5;#^:H-7`Q!eoBE&?\SL(Yo6J.A4Ab[Bs2_X
%7#hOC@\n$>F<X7e/'8n-2_='cHU8jM4f!7;W+:/c1BcL1IXN*n<oIPZRs@er4WW!hLQg;`Nom='U?>QeK[%,e?=(tFjZs?k*KWUZ
%GU#r:mb,?UP8pd4X@D=lB<("cdg0?F/)/VDHZ:>I.D!-:S]YR+e]K5J1]'c:1b=_gPjMW:<'ppTHr]EtW0LR82&Dbt5;9a74NqT0
%I(r[ajT>5ah+uCfDXe_*((t^sf;D.]d[A)*%72tADDGUSKlfI'bU*Ot>18NN!GNNFk9Q$+s'D@I:htqTp.KU:j[EO9_YhUOGbNo:
%r?J>Wr]?hDJ,IrkLH=:5c&W)kV:CXq)#;==<MW>F,;3@_E)ou77ft74I$.l=Xp0mIm]DK"#'L-1H9J1/ErNLW%C#g*UlC::U*hH9
%Jh"D`i,R5FJ?^UKUK*Y&0e%6h*aI6!"#,R6HYI\6/f?fBNcin'7`@"mD'7OY0,(cPai>\PA$H4tqn1Nl;VpH'o-j$E!'4G;7#X)o
%4j4nUO$-fXcA:L31->$kT.$VC@0bhH)M2OH49Ou9BQ4M-&p<$'m=[;a?YV<h];[I,]O*0Q4'[aO1N>64^/,3c&?q5:5(L1:=h5+r
%%_4/'c7Ji&J9#cb,e,9lBg?+dV-=L".4^)J#&nhI6p;h(hQNc(O=AEre%K*Zb7p`>7Xq(]7q%YF)dFV<ib+P'-lRh4d"S4Cc:`B0
%XX-FPWZ"l8T#RKQ)R\3P3#MH0<MYVQ$k4S!OrTm6*ibu9ODR^&lA'nl2(i"4UlC%dHH*%_4EkQ"<LR9?J07'`ZJlm^idXOa5SUfJ
%FOJ%oIbPeTKIoBG^FaLc6`V;-CIm#ukt\OVl=3Zp'Euj>20*ibHu7C[*G$U)-mG3!c&n(l.B!&OGK+6\lX,P)EQOC5^?+p0j+i")
%;<)$$m_F`8M9`5F%NUlE#FQ:$LmfsHF^kV&pe&(5BenR+<:k'.Z?Bs!_6kUd*J0t`M[OJ*QNi+FW86tL?uB_6L8RMu"5B\H')L4]
%3$(!1NbD(*s-1,7D2af1%_27,FN!&)Joj86Mi#I_4V7J"#GM9TaI,_23gXf%:(BV,.[.gWG$V<c/o<Z19G2+#abf'(ao;ANfj<>b
%-AIe.Y&\!&4@5+K1q;PW!jV5#.h-<Hd)fCM;b?G0_R.I,Wl^*Fgj.tao_2:SaH35eh9&):Pn!<ESmJc*Y6E%a5kA:&8`E>7gZmW:
%\EMDiNAWtG>TX?!6[T<;W&;R+O*iMEQL23P@P2?V=CO:!7n=HN,OF&[$+bG;el<_=KIbS^@A-g-iD-dEmm%(1an+';RHC)o=)TT!
%/(op`cD5,@6!dL[>cq$g_5+i/d+IWjoqQ__PPi>*K.\#lm#-`P^E]&8N,KDnX6f70OqDb&jY=Akfgrb@Z1[r.<paR8XO<`3^'@F6
%Y1Q]TChcGi\c&R_4lSZ%LpI'Ke(&_M;5ug$C^7eRGl?]&]pS!Ra?$N8V0h4.;'&E[fq9B\%?btqql&=36^nM,AR?r.D4VFkK6EVC
%'o8;S?^;+78S^3ZXVJg.pC0O?[B3#X^&`\KY>CI#[*Y>'/=*8Q-6Aj0W6&1]'gD<_mT(uP[:s()4r^>TrC-N<["5fIme6lgPp`'S
%-f7J6R1.jarC6;H&L8deX[6Yu?^u4:SC\[QNJ52O,aZ6fT7qX+dn8>cDc]Ya&lu[bi4UD8phXcHs1Z&Y*qF&Q$*?^TAbIm`hP3\%
%g(F0.%eZdM:78q[3.=B*n!G``MJ%8rH;MhIH8Y^r[TQ.Z0A00T(WO_f4bToHV^6G4n7R9F/-P\6aS4Z4dt_.IklSe!mBFQIe<\f4
%f7<2<'m[We=jIE&WTGG2_tU@]+#bhYK2RI8.>oBEb$Z(D(T@^fZChk-ap)C1q7/eXSM2&OBp/XC@WqXT[A@m[D@$)=#eQ&m&T#TX
%V19\U$L4=8a`Si7=pKpW6a1f'/Q&ERbJ&X`_UPl]1GJ+:C-ic>YojqX/c,b"Ml8Rf%jTb"@3@o]KZu6VLTDql'^?:L'T=S""dNO"
%g^u%t].q@s?6;"/jYS`Z&fsq"4d%O:KmU[gV]-!Z\L,0:&Y4VO@`Jk5&uBZ(*1:`^9=*#se;pa7Ook8dn%re9=$Nn3chll$IpHd!
%:/Bl]&>.^rTdM`4PAjf7Yp?S86Q!omP[&Tp^L&Hj_[AC87UT%q?M$*rH2"0<qGVU;/(n7>?`LVZ%G#^W1%#LJbMF`USlUO3bkITg
%#qTsX1N*\g:!OA/,=$6Q0(l6,6UARtd#!=gX3rbGP6hU<IqQ`Tr#Qq#G?$!8c^(1eiu4A"qD.N2caP&lNbaI0c,o9k(#dQt4-HV`
%M8r5e&[9+UfMsC%QW]f6"WrF!S2GGA^8KYJLZ40#Z**ph3%3JrNl$EqIdX^8h;Q<5qmE00AEq#-(qrZNMQ?jOj4'>BlQ?-)n-R18
%Xnn;0;p;0EA2K[t_AlO0O=*jC3h[4AUl<lH57:+_mo@T?A'?E34d<_:lh6BeP+$%"S+:1\CKh3dcD,IAcp:Bg<cC"gmpR.E"h%:^
%]6@e.@%99B5JjisZDJ>tO^U3tZ]p_*:diiWG9chGk(R2lne-MakGWe6JZcoSBD2R3U8r(d\XP?^XZ$V]H>?l,_Bb'XhC&Y<VK4M`
%m0;\L@rL-JE66?Gd,+BV@$M6*<Qc9m<mFE/55<RG1P!V!E?C2-N:Bl_oE_dL4#k,J>95`WC@rmHT,<kRmjmn1Z6*Vu4<&603Za`]
%[l3YG?Me'L#RfNL&VPu$;`;Scnt*+$U'mVr&G:j=1jq_e+9RE.R8BihWGj8G3MPnt]G&/-XT#aRE"Gt.]Ui29F"KLX*,K*r3%qGN
%Un,fQ#!QX)Rg_s,O$/dH=L%K&_tA;b^,'1@GUm+/-k47;"6+jTWd%;MHC@EGmci*H(99GBKc(F0_oEY]:F,B/7nGdsSZq<7Q`eVU
%mg51[nsE9Yd;O%8l]l#K6;9#ug*Nl#f[<TB%h+(9n`,Y1o7AI;RW?81-S$NDaI5WD_D]AcFN'G<d5q=IJSE`9h^)UFOXroTGMDTG
%fPtdAd^62"0q3Q,;]G"[mGXu8_-2H(DR_e%IYk:KLXN+g]iU\PpaAM(+!>(u902/uaH2hqLhVOT'W$K(heQA=WXE[@BH$IOia*H`
%DCgU3@$C)YOY,NkqKfV9$7BMT8-/c-Q8eaMI'&2[+`tj]!Thli_?`CA)?B6,;7=*eE>%:kn#=&(RFe]XAghF"m9'B#`rU3f%1"=l
%Bu'\#%32+-crW?@WhkUc_W3/*%Sp>dnVtPDNp7<-p,sBN-S+^*cQ"nW!#J4D`%[$@WH,N2V8b!pLG>=+gujSbOr]T7Od^B"D**R]
%\FGb:5Dr`CDfr^XHaUYWTsDqqI^=8D)2!N!+k9Z;$p"KhJ^j6H6I!/u&:5:=/$9a/T7(,?$7_'1BSi)!b>skVJF7uGHZ1K]J;UH%
%oIMs1IY8#iC!T,0QJtYI[CR^:.1u7fkcm:6f<N%28F2EY%oXE6\=IBP$V+MUjm_J!h6jGYL('#K%q&;`<Q:Q*Hk+ebM4+L_/`7c6
%VEG72.^OuDOmrth,O,KXVT9(3-q]0JEkCd*VnRra.%'HiI]h!JH-$DL&F$e(F2m!#BHJV,CTC#1+Wl;.,bb?PN1__H>/;2<hbpp6
%#g$l]1#Bb/@Ckh@c7Y7a<`$(NNf2"[Y__[_Fp.V=FQh^G..%7J]WP3!'Wp_G*VoBs"nLa[B-sr=^G]L[A]kIgOofVW]^!30MXYPA
%;`a1PT?cuB*o-lEZrc,@rl8QZ8e4$3MV$k26f/aFJ^,!)WV&EU8U2Y'7/49keL2(aY36%uJ's*XL39#Oj^IJ53K8[QX-+IiUY6Q-
%15amDHnFg-Lj73kp*Ljt'!c,B1,C*g(ZE!f..NV:"otT#VG\]T8pgB<k6Ns,2WFZURmU9a(Y$-CQK.#X%l;KlJXaC=p&;H[CiP&c
%G<OQ4gc>SoHO4a^e^jKuniUVpd`RTFT<6LJF@FZ90-sOJPS"XmBjX>M.'PXVICZmtgETeeI5'ISOZ#L$nR5(hVhBTC75XEJRCAF1
%peFat)]be`*MG:>lje71H*@/4!uo@lf+f8Em%O^g!XjO,M/5J>IY5LdTjf]7QFVCu!#g$;TF@$TQAt*A8i5iK0$F\d>ItLQ+E5`c
%h^gV7IL?hF!?!;PnlSa>5aBu]7^,RM%8/-!iW`gr&6jX*7kbMTbeA6D^8898*RY#`'Rao)_Z6<T/30I3&K7$?9#.N]Q2q#d6$0.5
%/&<Y1I^!9pGqbJSC-f=deZnL6(=s=V7BL!4V[!idrM!+.jB5pEZsrd)6d7Gm"t;Bsj[i\'IR+KmKc'A]T`OpKK]k\ICKW%S+B#"S
%f%IBjA"e,mN&b#\PJk2Ab$/j()-.(f3:j'gnsoUGZX%K[k,It6/h(cFIuVj*l!)0ScVL)@FtnJCQqSjBAYV3C4jK!/KN1lECseeC
%O`.[4\_B-DgG_W?f1"Q;FWXa$KLRT",.1?M:ACa&[C%;^e:ALqg6a?L6F4YS6Pa^1:oJ@sjK;5pT,=/PjS[Sqk;NJ5h?C85_/=[@
%dW7!Ho-hBSC[4s0HE1<;4gBg>1FS3r"YTj]`)E%G"*.nnMnu+:L"b=4*Pr_'<qLpQkTW^hd27g/DfM==<V3-_AA/%[R&Lj%J^e%S
%`+`'>\`S8&2Pk#t!F+-8e!T%HD@.j:64j3CW__IM;bSfsW@PRb@pZtYA3iJ3I(+/<b8m=hk[l2/@>\nTd.qa@[NrQ3i6n]&/e9np
%16-7nVe3l/.30E(-hVA%,%;9Me2&=[MuYQM2Q3#T8t,lgXmji>qTM>`5!3KuO=Y==WG!@<JGYX@ar$`i<$Ct!([j_Ea"H-W3<[CT
%i,NraGOS`[VEhU#ikR+'AO*QXBZk_;r"I7!PS`@;.X>`*9E5cg@unE\#7Q;>kn4$(\pC91C,3b)f-,N[k#*\4@3/Iib#8:7nL@-K
%+FmZ"AO]Y.n--n?D8^DAY5Tb7^n(XM0<3fLkl3q<&IONr6kFXMD`>*EHb=e@_.<d'+U/]TnTsp)o@)Ck>V#bd$Z4T`=F2bDM>Rr-
%mf7_3iV9jm?"K(R?np?(iU*7`^`GFi*+[]!l*_(SOs01U#f>rJn>JLD&[r$e<d1$d(1AiO@^o@;-10<+(5OGd:HJ+s<B58VL,MuS
%]^Vet%Q+:hRdBOZT(TCC2fb4THA]u.`V3T%?5EeumG"'>jpSN#.'.).\o3k`SN`NpG@G!kd0-S`kY,gH-%?mNQ)[GE^7Y@17RM%(
%;;%oOdU+tP1#:337pkBdU@1J+;lHSf.V:JLe@r^G,rR`r0N3Z?A%k?jckEYL;Ehe:Y[A4tK`Xc+RRRFJ]ka7?g$M661'o2d("0K6
%\NlfC7>Oar+9t0??fhd]ajdYF/"H%g2j*$fj]iTg!5tJo3<AuBWW^[M0R*-r*6S0<GQCFkRI,H$%O7/V!pM[%"D5eB'o)q4kYAr,
%E5;!qRtj;])2R$q>dFO`++_rR7eIcW$\ZrGi\B/FS&KM9UVbT."E<R0nRWrLK^4K[+5Go>._jS[e_a6\o#5Ap3P0Qr>F6F/JZq;/
%hUrjb^21U2SMic!<*8C6q^42p\lj:3[NLPNTeOH2`8ejpE><RNL>Yl677\XPZ[<Q^gM_J!"T-GlYa.D9&HG"2%Jh18$#aIgD)'eC
%\:RheW:'.?)cRW&7SjoBD!.oTX?nZj?*+o%59`.L%DK--670q$78N=C?:?ArE$o*DY?BQ6GC(@nBfT%49UMPZ(X?bF;39r4'r]gD
%N-ZD;q#b(%gJ1KQ7gjNU</u)]-5&N<X0Y&B-oSNFKU]o)+b`Y;?3[hCXU2pi9=ob-j=C]"([7Qe2K972`7e\?GVYHZ6lr@jDGVFO
%3loZQ(Lb[:Gaju39b;@2[k_\U)hf[j-u.Xs?e[@S2btiQ?AnS<V8`&..&@s6>n>5.Jj%2Kr[(!G50r7eIo309f<QM&7G-D$UaY=;
%ZBdPQWqL:G2TMP+EY^^NC^(L0PSS]`CGSG%;DQ61eL^-4!4'65<!G"`*`V$B]*RIFl-\VBjh`]77Z)Yn,@EM69gj>ODd)h[)/)7D
%U'3-t=V)?;UfE.(_U!fKWfAi7C@#/]%I%KfK?JHtfN_#h6<nmdU"N-2"bR9B31%p*d>mqh2&jd[H0O-=.5QXV.#!;6;_"/b4'D_l
%#eAm;fHj0-4?bmX="Nlo1b)G7<!%`mIZ.@\6cBeU_odN[_:Qnd>tp/V/m\AV8MMTRcs;c*q-KYHPZ8"k`cX^h+KX<"&Era@jtXib
%"-C%]67fK(55AHOgLI1J'#HNeCLMWb3rj*UNJ>i[L^G-18CbRcobgr\BeeQY\'"4Bg_B[2*ni)BnL?Ea.EXum+P%;4"E%RJ+2,\k
%FM"WFDDdYTW&QMO2;o*[f!&?V[3/Ti-1j\FA,4?Sr10V.HH?Ck)TUEmr<=F=V0qRUqTf*0e<n2&C/6HD[`4-YJh1Rk@Vi$Rp4s<V
%^I_#$G%dg"L0(1>g-(-d;Uc9)PAFjt\<@m4k0-jocg:ZRe_BhCM4PU-_I2A\X4Y-#^aj!RCOp*WIK^!DRoL3$o@Xf]crscR)B#=k
%)NhK17Gi%!@u/T<h^3k7mW.9%3=AEJDn$jAo@5H[*Z)K^@;7R^FhMrUl:(bflX6u$S;f%="FGVIChm42g]A6cNG?`2YVcjL*iMrV
%^*.cbH`Po](,r[/Qo(A<4O1BkHIJ-m]:e4ph13Pu:'dQ8UcC=;S!WZaK&f45k&\t$+l(E2C,6b_(KW&h@Km%(>&I&pk`"fY_!5kJ
%<lScsle:'fBph<$TY>WR@=(ql-1"5+]YtQ3Y-`6MC0*K4R'D;&2]/GFl(Z//D&SUVi'JC2/=*Vt[I!+W2LL0#0,r]bccZOK(SllP
%93pZ(3*Ma?g7g8EM/nm+c]4kZ+c/[-aCREEa?J:7\MEJOfo$\:4el/3oN(>h_RefM<)S`MD^As<8^+R$8n_?oYX'rS_qq`i$0s$p
%!JtS))A*6DdJBV?Y0^9=pr*P&KHO?Lmgq7r.ca\sE#)H$blD*'+@I);_'r8).>_*`H@@KUAHP>l?J;0k])prcHNluuT?cX14./1o
%Jh$M/^U>PCYY/rs_)6$\7ppoY$\c.cgV4[bL!<"5CN];YLXD4Mm$=gR:Lcfs#,jS#)&R^cokhIp"n1_8a_:B?iRDb,b:%N;5A8nt
%i9VaI$UR^Q*a[g2itSSg(;'Xg%'-gbD[<d<(30r^=dC@fM-,QCQU``&V!r$)n6BiCiW6>aOarFNfqRL[fgmD?d=:nsGfWZD_Lq0^
%W*sarAS$Qg.9sa(\3omG51dCW#d$"%!QW62Z\oYb4.&#X$66Sql(Miq^dF?rBVJ'Ufo17t[CgT%<!!m8`)),#(t?Cn=%#^!Y"q+s
%AP"2D!J%csjD?2cU3nV2XL(&km*r<,BIqV>_""o<mHbgYWhV@ff3E7^&6FF/l_-VNE0j^5cu+*Lod_O_`AG7:`OMR,<09W@?5jQc
%i";X)[j$<E?+rW/gV$g!&WnUN=@i\7^9SXWBfRnWJ-DsRh\Hb68h@.t,+J=C%5.5,`FMgU4h5d$(ZHQpW+n$jbX;aSV_YHV`Pcff
%L,iEoLRnWHMG6?uY>TBLT<?rS]9bImFm!o](17>m<#7m[4DN]+=,[%I=u_O@@5!iaA:$d^Qtou7e^^4W81hKB=Dkb3Zk^P8'jk%e
%'91P?fG0N8fc;K";m@ig"IL![H7_Q_o3+PL0eXUX.P4Vr_s=c#ck$LQkFG<NMi%Q9-tZs0Fue;9is2D4b-X1'CH[OijLnr:2V"^I
%G2.Da;pCU#I@VC"7u11n&V^*drqmtN]Sq@T'P7n?J/]2\&6gBiMA(&!G+ld^Z_#5V.nBkrYoDOiSk/Zt:q3[(TYu#'7_Wg!g42HT
%r[oOCDsGnQCsud:RIS-];@,o`^d>BX17!<R)cK:<C]K0_j,>\/BofUbA%_l]U6s?L,%L-C6P(&?Off4I:hV1hRk\Hg@[Vs%/Fjpr
%d!S5d0h8Q@3X<8W,^084/8_)"^(cqgNl+6o9\#qNO`Z#Y_oNC($lN;67SqKW]j4D6YutoJZJ4K^JZDfe68(%aJG`XPen]V(s*B,_
%.O#7DWPX]We%n!&[mW[/!9,T"#!t\:C4VFa.[)*1J>]j]6if&)YUJ/9=F9*A9f%S$*`i^Nl_K=c8li"X4qX>tU),aY?Pd;q-.Y&)
%>P\S=9-4IJch@Sp-&&X[HOkCT/BjFVq??S3aWG+A@oR*\LPaC5_tE#1&]*E$6*XVFhV0>)r()Z&"(^UdmR0&6JhhShp_@iR2BP#9
%'Jqp;/8peXfu-@;)f"4-=ImCM!OT[9[rRobg8->Gi*(D[#.`"fgaobVCh?*.\u9:?qcSI9c.e!5eMuo=EMZ1(`ZAui?\O)_9Y,(f
%-jbUXgp#ULIL.^LCYqC@.f@QrY)VNBQjk'YJD(S\5Jm?@KS\4`Nom#mQ]P&$,HR5H$[SSWi3BuG9#(^oqhXfnk90^1"9=;*")nSO
%"Bb=2U=.I5UG;2K)1a:]qUe\8J>V023>TcYmIW-IJHUfLGHDS_CBW6cfMpB2+[(5\1Pk;a#_ZK`pD++3^C*(##u1UBi-6`V7lY'l
%/-J<L/-Sf;;kD)D"6:?0('a^n`kXs<()kXg+YC4>cT+9DQ%gQ.6SKBYe_"QiU_G*bNig/2h>LoC3:d:<EMAIL>:c,qmZ9R,>-o
%=n_j5oh4(>o-(_f"/2!6B@09aJ;Tc@<F-m'$Jb_S'mOeC2P\%:Pe"Y.(H<K!Im0rY8-UaM$"^ri<2ghVpXM?1f`ie1#^Bk>n9X=W
%OT-HoTD!GO)%IL)W11#DHC*2'C]GrUaU.\Ie'pkP548!"W:$?/Y"#Hej*m@[/,Q.k_s`+:R\Wn)cI1&k_:4-UK;<^E*Mm:ukM_.@
%>#E\!1p#I(U!6$>fP[0+\<4dbo><:^Y7m!sR:D@uBj+UJ#+QA3:a3iTnlc40mAf!YU+X(teUTtOifh8]\C3!Hl`)@4Rn@ALbof(W
%nWS6lE0Et>$F\0/_O5)G/(J\-#cGX4f4S:0D5n^0>82js["B,7leChRNl85JS!a*H*+YJ`ZUa),O@J>`5Z-+qM\-N??W-9SmR5rG
%5j1nnX:4&iG]J+84g5uu%Uq5^(:?+`Bn1A0)ISf@Pm3Uf,-hUbFstH?qaX*YR*4.SO;=\/Fm'&qO:uNMOio?8O"*&>&]A;c=I1cH
%80H#@Rp6-bnO[t+Qm)>R9RmOCN#NC2>@bd!98E`*)0lcSogml$O/?u/PUEO"'1IOkmg9bsb8*=!Y;=V,&P2Pe,fG<U6IsWa/;LUR
%HP,VL`+BYmr4#8O]trVrWmDaD$&+Z^I-Q[V'"Pg#7tf;3CQ,2>6$hJbM=lW)!jO`eU*h%h-mh]]nD)s\-3N$VqNqG.]Ri9pE%'*X
%X(PGN,+d$*-\2kNhaeU^n)T?u=#H*W=VV@tGtLPG4qofj"tAj^3A.ZY;7aW%[r_%&Zu%XW-3g'U)bM;,GQXCj$M5n'\#E)qn3Ps^
%aHb=5-t3XDrqg47.Uj,89I_h&cm4_AK8=eB+k9d\Ih5kF"G>YM^jSeSrB\#h%RobScos!JRM<n[.W[jXA)%UIg+:!;'IG950U[gg
%P*I9m(5Qt-"$>]:N%&:`<X`M-OYah'Lidm`46*j%anl62F:AHaE[l'lJIauuRUQM)ZhE6fWA;k(PZAkNP-mmA!'(Yi&i'WgLR5$K
%^PmI:E)F8@0)o;Yc+j//07Wuuhu2iTr;JdaXT.J`n>fpomdL/G\%h_l^\[AbJ+_"RJ,0'#r:7>3r5E%Br-*Ybc$-quDnl<hOMu,O
%b5W&`r9g'3r;P<aJ:G;l[m0W!^]$<a4'un58B*$?J,SJ9QV[W*T;DJ'Io&B.K7Yu\_gg&trJQ[tkf^a9fDkV^r0q&Cr4Ckap:pP3
%iRIn8aF@NSr'jp1%X?8gifF]9qZoi-;=l!O1DE(1U?:'5-,:QuLT4[mGX5030UR`>SSD#pG"noe@&dtLAIYl\'e,-UYE%MO"aJF_
%:^'-NAL_uQM&6Ss6Q3kEIn;E[JFosi-YKC3V4d;"XG/#_Y6r`eMREmGeh1^#$pmQ)'90hPa>`F$H@=VW])4HQiqMp#T5Gs!6$KL=
%9K.un>!uIOCJ_@t\Vu'o`0Zj_&=u)>>VmKr"i^U6kbc43fDs5c+%9!Ej=,u"T_Ng=SqrIU+iq7'`P@;8@^Se+\,\24!e?r$5h-V)
%TSO\n6B@4nqcTe\GoMF2bOaFn6RW1.Fbb!k+A3h%+WH!o)9aN>fc^Ptk!a]r^coT^(Z=[cr$o7-\^#/LGZod+)QtY_E[E)lOjk/_
%*cU;:SE,*)9<c1`E<?[H]L4CUT5YQP=CFho3S:&l0,>IN#Z@(K3sX@JBb>@?LlD*ejD7L9a)k)(SeefAL6,Q,pL\'$?X&aZoZ/>q
%DC!T\!87>7)=;9E[$"qZ9N,GNe,];*bXRn$W$r,*F9uf%j!+Qc^*A<sK.ueP&Z4^qcGI1h?T"n1h8f9n)K_JO;.FZV;Xk.Um)c@0
%.Q%a@DW61C-5tjYXd.W\]3M6Sb9k.[QBJp<Uu3eoCt$M>^n'k4_)%ek@8cMn]8>lI</+)?Orl)n_%9-jiB(M0)V9`'Qm1tFQ>Y'^
%$iZj%:J6j'l.Q_-`&TOV'PSC&b9YAAeHt3Bfgdg0Abm_el90Q33>f7--L?s113*Q*:<IXVh1rR(k8.A91Xc[b'L]ILWJ#0%<]F</
%o(UZ-L<P8BZ@ptaq]i4S^SrN#'QnaHE+!o'PscjZ.%s3CYXmtV_Z!A4Act@XWsF!$HW/());YV*!r8p8JhYU.L&N>?6EX!-Z]iNO
%F<-aQnPe>G%]/'-Ts2S>DOIeq2QX'0g$G_9/L0Gqc*<_n7G`^#0=[a=d3VMgnbPEF31"siL=&H@ceHlC^UcB<`t'Q8PofuL(gWUr
%^JH$(`Dt-OZnq8NJGbDNV%BHP)kt3KiZNJ2%&s<a2>AFF>g#Cf/1Og/CR4WY(4dTN@N`A[@:p7QGY6=.ZeR\0P#9(KRGki,Bs2p]
%kN.nd+3Rnpom[>?s/.lLDq,*oLT[4)^4a32H(*K.K(.YRhChNKQp=(q7UdbsQerZp!![m#mqOA[pSCQ;Kqq*lKPm9>4P9W00&rF1
%pCGt;>reM4NleM]8^5Gf(cF"PkkGJh;l)+c!cG*P9O6bG'hEhb[ZG_#KlA^WRa'8N>_VZE8]W*[)(TU':s1[2lNtnb&Q.dNM"ehu
%aU#(8+RBC[m=r1X_?X,?g.+TTjT/2T)<FkM!5,XG><!HV?>75mG7AFp#Z^ec-teBO3@2u(hP/X9l-V'aeVbqS,e//ab/J[)`no+N
%\m<!1PXTo/]Pui5r'JC^'QcD,em!Z*9P+[j_iW!E0DQ>LC6r$>f@/U(D+3u?5YLs2(.,,LeP-P$>TaHIlO.+Ij=U-`&[N\V.Z>6b
%O=LC?BrO)s/%rf)mm4V5qPA!uD[WFB;203Rj-K6++IqLhp(p14J<<2m^&\57MXXVm0+o1"<P-sSGQdNhOPp1&c:*)1LCH6HDI#aF
%^U86h;N2&*@/[@anUTIgUd9661ZsnA<`mNDeE-Fkb%ikW$3K_YBggWBcpX!^_=h8N/r>($!]f&A==l-7fm9>A9qj6R`5dRV"QJ=3
%qa`H("Se<t92hg:P5c",if/7E$^+[+Gm&>a&OEctHh*_Sb\ZKVMI'JZ4BMVU(L0nG7He5,:-V-H<S%aLe+O%lGaHKT1iCuRps::Q
%<<YNW$FT5N*8(@!IOfqIdWL'6jft?`)bVTg!5b>grW3TX$:Qj1]Z1'*2s(BCrTrUP:j1ZRl(m3bYAeSVIWDX8&IQc2npU!I9][9s
%$eg6\c4F=CYf+)fb-5:%j9Xa(.1$`K4ap.0V-NG+_X^+h`IL5l0uC&a/C0Gf`N!SO[[OO*30Uml58h6P.6^Ke>`tPUYm\Vl7h9R.
%>MB.,h*uaWU5;8gcU_?GqGgf%5SLV./EQ(]4q's?faB"i,Gk?TZ#SPG#TuGKTgHi#A.e+q)i6hNW`_<]C2L@M?'ilP+<C-[UX5O]
%U@fQ5Gad=8fa<)aN(>bd-PD!Q]?@9N/DiURm[B>6o'/TEWuf;o>-A>^+7N=?INACAqCqr.gUj_Af%d.+;:,p(P,[lT]0TT)m/ZkB
%)-LAr2V!4`hdn=Ml)4S=,>hM`T\BK[SZE**+:P"'Um`1cf&22E@^gXT].P`.amfgoL.5?%[UG]"E*%1OV0gJIdnIa*Fr4MB&Z:SC
%)L/N%lsgJ3B(i3OBR?X:=PS?s5;o*6G0/5681Q8L1eW.-n"]kJ3WVjeM)0^27]3C`[olX@UPNbmG@H'3#&?R.10"f_FqonghT-;I
%eFRim-eFi@.jMPbg/<PVfcKQ0Jr`ee,7Kj&Q@J-J`?k@TFMr]"b+ERe%5f(7/-V-3Ue4Je.rP#,X=Dt9\>Ic;Peb($W\E#3#bO:Y
%'WSK[p)8nV=%5/3&32OUdD[pJ1Q[NrPu#rD@</q&@MkG9$kFd`5;GT]R;g'7#*r3\qClGu(64ePZU,HC6j$5k>;7]e)n*k'('IBN
%0Wrq7,/n/kO+:NR(md%;:>/E)$n.]Q,E2F9?!1n&bPMOA\'+LM%_[aH@R`X1KY,9gq(e>SV[-UD'9<fA,f:]e=1\BY7t]1I^2)VZ
%mh4I6\h6`>3a7%?;X'QO!R,_g(Dd35"6F'QVjqT1,r?qi7OAucoOO5VJg>B*cNpCW,$q[t4)k#fAV0lpiIUrHq94>KWe=lFVctI:
%e=H5Yc&$@LacSK40M:1MUm=0\.<7.C/k-]tEH?`&+;RY$#SCubl<0c9J-I<aDlA>]JRF*_Km;A*%kELS[5tpaV+X&r8TPKEe8#=C
%>(J![KPXKAMZ(u9UFt3#9Ye*u>H_;dj4PasdJRB$<FROmP\RMUZ&Ue*<%4K.mK!DUQ7)uG@#0dUka8-'?$+>[bP/pV)0SRigDbHl
%\XK9E;q3a]G7"5*'f!$TJGQ+R682d-kaiTfMV31o"p00cA"d?`[FR[+3EIP-K$Ei*%G>D[=o9at0[./4C4k=d+3-hs.VDl2LPn_B
%<\Lb.SoQf[D<%p3X_DQnq?Y7[fYjZjX'^<I^`.;Rd7\2jTr'U6"kKiuF[OA3feK)-8dU=nI;h'J6c^*EE0Y*uN+FE(QOR`T=t7p[
%1p*24GD3Jp+@5?M:.&J\9/&HYUgi+G-3ZX91rT[,dtkGKW8BO*)=BhC`CqG$0L2K30NknE_`Tm"kg)SQiqenlCa,Um?7SAVSqHio
%.->fu>Y$iqrQ?15m=s_'`4XO(b?TY-Dj)8?PsRa3X/;&F>drV+^30D/\F0)r.Rbp3*9qCX!a[0aO?]l/'"OD(R.<$p1k'UF(Kh*i
%Tu=#Z\\NDCc$G$gi&p*abbqsl+>bSQg"CY?isLer1s6%GKH)qmG9Jn#&<)Xe?HUqSj:pD9lt;HLdN]eK>b34a'#kkQQ\)00)FJ0p
%W[PFJ,cIVYh>%60bdpAdks94U*f4l&=&JH1Zm0j7k]q.JgislJbDae)3ciC,Wh/]\<'iu*5@K8b"7N3@VT@lb%X3r3lCFl=<)[Na
%Dd!'oQ",`Wip_DdgT7Z%P]e_g#Xs1H]sC/+6^Y[>qC+I\@6aIs'Z!sj.*ENt2QO;I]=nTMPb3!#&hG]"(>h1o*cn$ZgXm7PSp<AI
%H.,2pU6[R[]@7b>;FMKX6q=gTZf1uIG4,l`;O`dXhobr](3R76^4MqUG:r*K?IR(ZW1c!Uo;7h!*0s>]nl3S.U^1E`'Sne2<i92V
%;qM*llAanqel^W8J4CqP(n_+6SIQUs-<@s&&6'q;!g6?A;E$]nRt(pH=kC-hp?]X"a/r\f>/BsUZ*B/tN_FJ)+1W9#0;OBJ)MpCC
%a'lhG.,0p3]ZMJAKi@J`3lsm7@UE\8boh:8V>==ZY-Ld;C1W3CgWO%:\."$]drea^jg]k[(CLpiF]m"+1Hg-/4jBIA"6XCVVu*5U
%j4CsQ*%=X[!K*JefRVo7HEdOi5DZr-fR>$-WHQ)g/P6EuhR[)B?)QkS+c>drW&gqqQ9AbJJ@<t8X/O(;[TE#_'7_`[;6M)K5beX5
%4hPOWnkI^`!'?H\+5Ba)N>W'4Q;,[Ja^Zq]gM6%Q#u)7h8dlr=](`6c=nlDZ-c':8,B_Nj4kX;!-F%Rh0]f/bE61I.e>,S-Z`oO:
%RHX8KZ\e&=/:7%^89R9+c[Mke4u20*Q7GJALNbD[0u^r.@<39W/FrB<=[\C\;PES.,b.t'G,fl3gAbma0@F,Q>=0#QMQ4LM;oDou
%h37tu,n9B]CoN<de1.38-5fF9_?$0_Vl+N5i=&p2=`_Cm)DXI!T]XY"lh9UKCf`m89V@Ho=4*`L3m=B8CL<<+Xm1spY<k$Qe/:YL
%'I)>+RVDA)+t]_P9&r+S@k/"tll_'Q;G[_5rDL%R\uY!5BiMIK5S&%K^/T<Xo%Bi'Hgf0SnkGFgSP#-d\.FnqbX5FiOo5P]>&l<_
%:.KMhWhnN%jRg+[/&Cn+amL_g-AT,ib=07O`g"W,KRc?3cc3N$S.Ra0L!Bt)MMb6rf48R!7Q>:2p=sW>oP=cmXKW_8^S3ZoolIh/
%gKdq:/&Y@Ll'O%p>3-isrB]B7,+iIfMId!5$6##7bG9\S(SaMtng7%@@SFa@KCg&!_E;VlL>$L"bVm*jg(7@;4k_G]Ts>47>;[VA
%-@LbdZhkKB*Dijce.'P^fE$_ObcHYS]2W(@nUfj'\IDV"/"ZO-]nY%7CGRs+,P#Q<"J$5'6RQn'Og+K]og&Fk(fOFWKtfRsACVLC
%Fn]4@@0$[G)C_AOA*99H!'sZJ3eZ?>0<s+I&3gBj-&YslU)J>BW:cU_<D=sL=#!hcO,H6"RV@^,/.l2]iF?]O3c;.kGq1^K0U$OQ
%&\;ZSO@iE]0ll74:-i9N_qo)YC]hoP4.2FOoHd2l)Bp-J-D.Fq2>R&B,e,BA%\AJJZ#+<M_QOfN+<\-JQOK]?:O;Qr/EiJbD;I]O
%XMCX:T%uNlX?1hE/Gc#lLPeBk8@"so.,r4YmnK"_;;iHMejP7Eq[0,e;Qrc)</[RqK>Xj"J7A$ZfY_rfektlt\F?'@\CkRN2*@(9
%&Yc(Ok)o7_K`.21!E"?-0VY`u:.`KDTQ#f;R40/pClT&M\!q2g\jb6b:r*Yl&AqCdI?3_3)bV-=_,OIcOoai.;s%M`S@#CZJ95@`
%,BS*Y]D2^J.T8<g)3Ea#jJ3JOngo0SL)l1n#32`M@Nc>n'Ts0],M^7+AC,2ALPaRWF5*;L0N0KU]fgkkp+ZCKnp+DSF!^EU,j,%m
%Vp&:Kogu5"'lHsA\gt=s!hNX%[t.E>]JR$ZD-W[,DI-Huq\"`.:W-YK02.OU+NUS&c,=[H[j13<'kkk"H1*Ls-X1^Q\^hKif7r/.
%-"DFh]78El&L>?D0ep8J[pW^]RC]jjkc<?"AM_IUjTni/eG3l0hXkAIis$*SgKTmYRLG3n)qYh!,1V+"`%d10=9cWiDRlLmI$apZ
%2j%#>RZjM$EBCH,r(j1Q#I`3'hr49hAJLrVPuRX<^7fj&ok+/k6bTe`m[=N\<FbB<gtNr?"gGd.lt`OR)1[jYr.+WH;8'6/UlIuj
%>;me2s3T(t/-NJ1lNPOrLkIu?!7L?f1ihq=JC#%*)U[GA_:MYXGuVs*H('&7iQr?oqkbC?6GYc&QY[\8N^ah<^$B^S5K'cun?LsM
%kU>8BK91JK],B*u#R1ZF;IKg8VAFH?>.`0c(e1d^rc09$`_9I8lhp/kCEjl4.PN1Ol)!Nj(#+c<h0d>h=+_B(,c]l7b89gBqfZP'
%NG49<8OcJ/SLnM#Oe5Q"%`I0k0h\/:Kop#'GrJ4",D#)@a7?kaXX'AuRm7I!n;sOkS5pM8"sKU(__AHc'2r#,cN%ICl&iZ0ml"R@
%/HqQK:Hr#aFN.,1M\jeE\0l%Vk/oVcP2pHOpu=O>r6=eKs,9\SJl(J/E;&D&7apV"PAK]^[=kZWb6bjo'U4=+5&Me[dse%HKQZb1
%?SUu+$'_74#MC2R.pLZYlisTBiJ?IYY'=@"_*:2JDF#&3c_[R:._j\Q0J<)2T7;8*@Z`s5ni-\e&J8^hDC%s(;"1:n&be$>C\dtC
%kV2e1R3K6mT\jd1Io9VM%li(!f6\GW93n[.b)#OnFKDuV:ei/:(U$>-g9VcH3tGI4!DSi$Ag7@I4dns8*b.0r%,_qCaaaJV#b45X
%-J3"-qHE%+9bLS0$;70]VdYJPC2:5=`=NaOq4p?Hb@uJd@4An(JS^-q)i@<fM/T[Y&LWbpfRS6+"q+9D4^4Q\gj6+tiD(+e44@1<
%"1`jk+hES=N4bXM/C8dkl4pJ".2%PB"t!:%YI1fC4`FVJ1t),/LCfBT7KR&IEg.8o?%?%JG(Ie(,-/=kO1$\mp8[sG1^1M1gKC1.
%9^`8$CM65IScd*J&:pp8;2D^`;O!mcU[k<t<C"sJi2rq+O[`i]cl-0W!\g;LX=;?I>bS'iXpY`qCjA5"Y6ZZKXl@hln-pL9bAMDV
%UT,7ks%n@^`-?l^5_IRj>Ca+!/\@Wa(]?@m>2=r:4VDnn5tR8P5n+lRP-rlq^2ckV.B`)f+mEDJ4@4[`Tt9ZG`=Z.X\NRE:MdI.W
%P8:+&+ZO[*A*fgfdk,q-);B$]1u#Z;5Xn\=gca2q\cHQuVB.!AZ)>(@,66f8./)9XL\iVo8[L\(,!\\b%hI#]gBY:>18mZup]d?1
%4"s8d8/KElU$Y=\JLbVkOs(4eGMnjC)p\PRVdOirfr'1r4e5F&T:l^<$Ol;gFR\"%pZ7tgTId._n6"i5mO&k,bslh))K(XlO%nn/
%M+M50Oue,!ee&OT)5W,'b-;:H8/hho9rdQ:2^KGlhfYI:!?OHQO=M*uTGjVs't@Y'`k?.<&GUH22`Gg<7gq<^.>kh>Q+4K?;D\E.
%P8O56TXN\9`AmIse\hk&Y"Hlm%3*R@a;iZN_aS#d49ErJe3_OU;`b;NR!L:<(Km-+;ToNhm`tc_UY'9_rTMi"S>(g'2e0Vac%(s2
%?TpciH<ZeTHK9@NBlZ'>$-63M<'g!=BI&9!SdQ%OgJO89Mu[Ipi>@Jb^V+Z"a3hAALj;D$N=mQA^m2l[LM"otoS`"UZ)&U^bN[(o
%EbW"S2C;n$n;ZhW:`SsZhEaj&Can2grApKm4<04$Xl\QHN'Z527&1+a=k`7u8XCg!8+W6YKJha>4rHZ9Sc%Opj[)f9nD(Ak"#M&A
%:1UpO.c-L5%4<22kBDMZ<kiFG"a+::irLu@lY49B+tQ`O`.WFQF##=;GVDYa<-WoZWF^H!b$Ar?i3Mst(,jqap4fo<?nTaPl]G%J
%SKdfW7o5r9%9RPV%+@mB]Rg*0#RPo.P]2XU1,?@&#X;-0pWu@LNP7h5,I^JP`dd,aZ4>=2cpZF`/cT'!$0\kY/GSrK<G`tL$mA$j
%n"g6;Anc#2`==`u`q5f[4hrmI&X@?cd_@Rf@aK5(8f(Csnn$Lh=k]]L9k7OO/=4-:=9LXo.(Y!@\:/`SDG.ML+,+u!HB.ugqrL^;
%I\5^raWuH<=>DSp4;H%='JkAjrgU,EarZ"tTg@pV2<X'Oe4XgI)L$Wu.F6O>L;9cA-5K&iU!N/;E?>Mng[,0gY&ma=7n_J(@G)&S
%St&p[ZilT?:O1&a%8pQCIoKF39CR.gepKjrbM1<Y3Va!NH+n(tS-:b`c+^f,O/^AA1<scBL:o"2-]e5+cVh&QW(QXDRJh'a`cgAC
%<*L5:d'HtM<e?h-N.$Slr04O3pLBVc?'"o?$ck\lZL%0,6E^%iOV@i@&_UBT#224RAGK"7KPsfD_iX;\IqRK+\s1e=%D?U67>Et?
%Gh[S"m%htl32@0N67],=C2@s@I>$Rd!\:%)W7u2<.mHHCf]eZtp^/JNHTKi=5qH_gb,<-dRU#V'Uk!L;p+eVBc?.l"7$?ZH0Lfoo
%MB1+OmcR;]+U_"P>t/TEMcjJ>>u\Cb9:N;_DR.7MPLY:\K8iC8C6<dlF2T,Ro$gWnKH8;&7-'?%SDoA]eH7tE>s3""WF%V@pFUiP
%?*4<7(25Y79\T@]+D7:f!'M5`/M15`;9m#qJG/+G+O/pB#aU<_:aTU2.kcEf.WM\nc11_B:fS'f]&WP@@$!N2F#^52%+e`<`;&l6
%lsqc&S3-fbdZUGgo^id=LuGR$>'gWp?MqC8(g@MdUKVoTRt,T&2=$=ekb*)hpahs)B3$AYj09O$EMQ&1Qd-\lC6W/Mr_]ad%J4:_
%pb2L,4@o>kqi:42?OS?Fru(+^s49a:8St7^6D#gsimeR<-T_3S+0hiY)7fjsIB'$4<R3Mo_-FB9_UV3K_O/i!"`WIIk?V1j,\pet
%Ou/QB0")dbiTbk*"cV@;7O#,We!I4l%C48IEXs+(jm,#?p'3Jtp.)gt,o6k1/MPuu,"MSpo"r:,>UrG0iF(h6Hc*HsL]nNm9dBdJ
%U/;;&pgZq"_,jMZ86$hal\1I@ajl6L:\co=ql#t=.aaL([TkZVG0>'s"X'Y*ro=e<5K-gXh8B2NE_aspN,T_igju[L;seR2anb:l
%h\qNT]1-<T(rCia?LM\[o\tH99;),jLK*f\=bYT\)IAO-9\j^8\dV^)O5Zq<1Al:_WC/C>L*qX(\7\<ljGD+X\p1'bHlSQc4Mbi3
%iof!EgWR,ZA$j9LYODo^l#^@?fI+nT0[cs6ARb!?Wm/@"7\>J'!jMRumBoO2ZF.AE]=O>=9<1PdH)#CrJ!VpcNSc%bAP(omkS)>.
%'Ko8tXH$\n.kX(qE)52dj.;khMJk@52ddTK$sQs>n1tfN!$tet%SZ=-^=*e$6qg,*eIT:$$E?!):$f>1]4T$N5/mlr>n6NO/cl<T
%0^:5?TH7RAP";`nLo%_\hsu.::,@[R;X315:%jcCFhIrT]q)R#3H^<Xc0h8L8rI*5<Gr,bKGJ!/:BAEh5^@*aL;Bf^]p'ME3'N76
%EDbf!_0o3kX_(le-YI@o4@1-%eGpFdMqu2:Vjoas37Qi`\JsVS:V5.>;D6/FjW**tS(iZJlYS7Z0Lr<Nc&I_kg2]9JnW::E+<254
%+-J.SY9)+31[erVegMY8A!WN/X>orYq+t??KWX"A@0nHoT>1Xb\g'XSc+H-%&3O@,7,Hb<,\=`$9pcLD6@]`ri2:u2dZO=]:7VpS
%oQ?O>TUD0RX.Lsph01%:=s/:@>@c'Ncc!oLe7h;&,@0SW"ET3ChPB]ls)Eq_<Ai86q09:gWat/r9GWP@VaoYlp,=CE^kY'[6^PE?
%I7EEIQcEmDW$c>WMPRH_eq#"Pqi[hD\BD3OQnZMJMr&;Ce0Z?<[(^sSctH3R+?%'7g'Z(5,3s4pgFYqt=@4g&C>dgP_/uI()%gHd
%QhECj#AUZ(T,N&:o'g$H5^8hDVe]i+(,HHH9ie/969)o7K$73Y0=t`^l-uh.jp6lA$lAHYVOIn.LTP&];H#*'(*0:kqqo)Nnq^5Y
%@a@s$-qlp-Jf@+4=q^^.@uZ+Mm+Df;'Y7il.Q^8l"cribRo"]RRkC@j6uWAh;QhcBkaJQW^e,2GJ"BfABk:A/Ersm?*Cs0eFq!\3
%HW$3t)8V<..2NF,K_[g4p_mZl<%'!Y+2=PkJ%1$E5KhS6ag8QPLg*J-T@dsURlCu!P8%S[WE#Y*7?0M;MfF"RZ>>?_4feINM5sGu
%Z]iW#4LSoV^e!pqd0!CS^^X$/=C&J+TqHI=\IY%(*Z?`oA_0E3ClJER@_X^IY?DFOWms!qa&$XLM;F7SKIrAZ'7#9W<r<P1'07\3
%DZKiP.<RQ(;JZr1L"G`2D"ZUIYQ8u^`'np]?=A&#eL5;j_:b:C[=O'_%BBhMj:8&u*7<KB]$"tF0!&+_i)jS`!qn\oH/6Jl.n)0?
%)Q9\-mp^%u[R"4_E^,e\$3)X=`GGLU?=sdL0nmjMZnh)bfL/=Pp;H_CJ$8]K;XNfg0;2&eK[Kf(PHX16pm%k;VC5LL$KOkDEsSht
%$pWA!C]eApWYndl""%tW;!/&U+aBpLMWP+'JGJCB,7l*^p*m&_3\Z^iq0%6lX;Hk:YW.'@kA5/"3;7I.*HJD$GR'nD6dr4NEM+?7
%II/%UVn3VfQgS24Nl0UK/^gnB9tA7*!p]<-4g#WN*,'9]m2"P@dn8l_i2H3<YNqt872I:KdMbpe*_@6ID"Ps;Po]>Un19g"@tKms
%5EkT00LD`RWk+?oV;\78lnI@+2'&Es=lr/TQ[B@%8JtpMJf%R^_UM%G,cYV?#Np^:d*s01Sor+C#Y+VEZb?gX+WJh(84$bT3+qnB
%5-Z>n#u8t$(?MhsZ,ZB!gdGk/F!M12)nPelmeBI,V\I\Fo(I4<TK7dK!<M;9Cdu\R<B]V\0i=11&AkX`Y2pj!1]piF,C=8nN)LVA
%6![&`90#s3kpl/g,SXI;A[]f9Jl\VPYRYLkQWeY-*5IR@0Wee*S^bQdUCFdd/__LF+O?&U.,/cMg)\&)UpCAhDG(rH#bflj6-<rZ
%ZKT1a"tMO["PTgoE$^57^_V-X(D&c1onFe)/X]S#jkg]N&N34kT&&'$InFl4V=l9`iXESg_#^l2'-`lP41N$QZf&rG]:1W!">k]>
%\#A+1.Aa%).!,/2pg=a]K(DV4`L**14*lD?G8>P`%/[Vj7Q:6HEfj"9EQ$ahiO@NRU$.*t&,t!ok;(FnX-qY(e\[D*QfPHO[7H.Y
%R5R/(J^nPtY`?![`1;tS$*m64Nu"ujRmiJ6dq$KSg/S9lkI[FcRf`(b#\ogo18^_A:=P+[;Q?m-IF$AcPWDqpV4#I5193jZbbpmd
%Y3<V1+VBVS@fSGh^fa[LG6k78%!h'5[@oeP4\k\9#bR[]5I5H7rGf+_:^[4Y,`R89e+q!8olMsbOgKO.TfPaj1O5uH)-"Za_@\g<
%3iJiG'KQ,&_5S/J2;oi#@9JGtif,]NP_O3[__\TNK_#8)S]hTZ/_^gN5[9\JW7seX-7Y;"(iWU.]q8Jgjrn,Sb]N^/Z]4A`*I?Zu
%+ENMPah?Eg^mX,8iQ#Z-/4q69W\f&.1Fk21Uq4W;aB`_<$@B7],m^K=>=-)c#eOiR&>iRG[0aCcq3s^3hhB_UALOb'=r1`@eMAu2
%TY\NajXZ!kpG(N?P[uS%/=H_9`ZTY=1^ch5dNhj%`<\&O/b'1EpkhcqSk1NVloEg8e-e5R#c$f,&3V[1/ob=i[6ushl)HJIUfH"$
%QL_=@0kXGO0bUK=.P7(?r[sI_!F3%a2'#TeirdPqrZ2K7,gFHqq1KAtZEo0cfAm\R0XbGX[9taj?Ub06`m-JU8uXGmBt&d6Ne6\I
%G#h^[M@F`<od.jp,LH6AR2($XO%9]M0PVu.A;26iT%Y?EPEe8fU'*^bB+R&]L3H?H8T%`'[I;O_E-2WPQ6_DdcW1G&#BJ31jKKNS
%gfDQ`ZrCCIQ_1cb5M-WL%7$gBU[KMTidGIZ\4b_Pa/f$UTY"fr^:GZQC/qosnqLT8M!bY"?702poGuI]8@mL!5<HaDV!+jbDYmV9
%>d\feF%X%99R_,nf'Z6A5iE]?r<`YVg[^U&YaIASHIj@mh6Ruc>e`0.ZQ7RrEK;D*cAMqF)k'D5TetF!<,YddBG!QJo!q<Dk3Xs1
%"8G:"-W6DjL:h@69\3H'G7hgk/@CNC$>T.M2?qZ@DP0b$:J#SkiaG:CRF3`(cKNi;;dNWq>R9.e.3S6Cmj)D9>kq0>YXSYK3`omC
%95%W?lom_;7,@u3\)p-47O9DihFh+I&D!9pA-8'a$N)#JPmrF^9*F3M-h&$f9F>bkh@Z2I`ukV[p:HEu'iQW6KuX>n\qV9UI0lT\
%S+m_bp^A[ade5Go!8.lt<RtmtgoRs?fEf'Y>E0+V2HB%a:QeH*"#ottjCXZ9Kq7Lh?gSUPNJg^^U@f#3(^*QdQ6MX3ZSK,<&<!rD
%Yl$k*gt(Rp1[)_sHB2e@STFH$AbJcVSs.kY[IV2M2:rt<*-R<)aPTQt#]@p'@*;8h0?4d>clLpc,TdZNFSsDP6G0u'k]"mX%'iT+
%8:BkQMg"rYQ1=ASbX<W(=Xe7WlaAL!Nr,nCnYmEFMJq?:,igg(c>p6>P^]#Be6hGL`VC1^$7>T66q"BrV#<JD,Ndlm[gDJ?R_tj-
%7(\&\mrP#=SlMt;j)HC:ejO-V[?lIk@1%Mp2;9A*i,+E<::h`ikum&+Fq^XnT^2-b]bu_o91EM(W"X3_"-b3k7t6PFD-cgL5S[^)
%7"86]CGqf!H\lO,0,2Y:MrH.m#LtC3Wbn[m&ML3<pC+Dk"/[@Oq"M4^GNiX(&Y`JB_@g*:02Vb+7Ge>_5;eke3I8T*ot";'Xq6fu
%oP.#6U]u-L"=e!#,PUS/pD+<d$<QgpZ$je9+k>e@D*0e2-4r\OHW+aV\mM%^.9[8?,>:,A?B8A6;;Dt=4aQ:[SjuepP"'+iA!\$a
%TMn9>laL:j=7KQ*1%`Wn(+hUk_t*0PC?$R],QR"#fGX]b>;+tdJBNdCPrn_p^?g]l!M#;(L$L+B!7<cAKY\Xt1l;0(#Jt"BL`u1O
%*oL#J9#;X(h_^O0_jVI$5G3u@.Y]Nt3u,$8<cm,:/>DdCCd_`/;@DA`oamV*r8gDX<tKi?qGA%d$<'("AMnI0[4nDj#T`ht*r1ZU
%<EUZ5MbeUObs;N_blfUh2GYEQCoW^,kr5Cj;;&@PP@^/kQTLSf9krc.^\=f=;t"J*9X$,\_A4tJW1!'te*S,"bS2K%$(b,j74b<]
%Qn(chaFn_.kn*(5EfuNrF#Wkoe?BZMWbaZhSs=AWJ1MM_H@W*`*fJ(Jf]=RCbcE14%Z7_8%'bJ2g]:)IL!5tq5MDN]!IUB99lH:b
%\@Q#tAKVeN&^429D3Id@N9F]h&?$EqpJa-NR=ljji>K2^alG$J!VG7&*k'2s;5$:jE<t^282LRGq&Jird%%Z7r=L1/cspmKn1j&j
%AR6@bBHs2!U=L+Q?r`Q[$FD!`F^2h<E\VW3hJH4`/de7#=N5Q/daV*WE1qD]NqJm/ae>G@Q;";K/P&]V4'K^j(Z64RB][Qs,`T1S
%-%mO@1Q>N+nkO@pd?`Kq3f^0fg.k;#+?2WuAA:(gJ6aKW=`0544R4mBkE=ZkSs>i.4JFuAmi+sni)-CK#B+c`%h6#:e?ocmUnp^!
%,`MKqOCi)!!]Hkhb1aNG\3IIN(rulI*`q69%'1@6F2GH*SqZ<"gjmL[6<&Ol[n[<aQn[KU5RjR2741\(4/jd90L4ba&Cb1s:3K]f
%Igl9f!_N.k92pY%ZE4[=Oib>2$%Fq8pSSbG?>l)ar3*KCT>u>nEJHo$LkHF(Fm.Y_3uj^HD(Vb=UA)[ZLiO>I:aGG0BWo7P4YEf$
%*3MR2Cu*KROA`?3*:'h-1sJ[24N#`2q%Cr3)1!@neh.T\!J!Ho%=%LO"!nfahJEGhpY?:u=D1?bNGg-3ENt)q#=ZX6hO8n3"E?'K
%n]5*nVkZQ`n^$a[8E8)MQWS=T@g7M>?]#sram(db>eMsl9+a#\I<FFld7oG=%sBn\`1+jQo06i>,pCK?B9gbo#q]Kg)$eJ*9Ph6D
%,k3VFSQja7XWjoQ-/Z*KLq^Bm?B-YVBE5P@[)4XR?$/HC7jpY4HfS/nqD<ut0W_OX0^KGjZDs<-5K5h>APaoR=9rgcXBGq]5kEX&
%Yi[i1-BI98WgS+g9>'<)X^,1XAnCQ:[h%U-"t,mb_uZD\(M6;VZ[5K:["1Ao->sg0KO3sD#HBlf,-]U%cS))kp(]m^T%+YHfUe,<
%(5ufW^D.4nR6,;92WW'gj[M[1Ybe%EKa;0kg7a]6N/\=P]6\i<LV=LG[SO@e+#$d%i#m,OYB:`G=BY2OE,]lVU*<pe'Ya_f890p+
%/g<_uQQ)QE6gO["I3B`N1H-),i;rHC53/T;.mY:D.Xq%/-U";Gh/6C`7t!O[)1eAhKeM6XkSYc9.`VN[9OJ2hff%d`D#97>>eE<+
%.C!6B[pMVR(md`,0Qorld/!5*Aq]?D?3k%0O!oST`T;=(?_[/\jq<-;]ANcEDTRK(KrZe;:gd%eFkoC3fs&k"l7Y!SC</mPkpE"B
%]u9FeMZ;1XW4]QVk/F*qmKc+P!r:N-"Ce^ERpQ1W:!qBe\=8<f8__$t].`E933R+V?uet#_:)0hm09dKhJL#O"@p:u.5h`dK>J2X
%mSn6JG%I;fY=7O'a-[$n_Fj7qZMW1S4h#:=_B-SlaT0;PG-$T7,oFgbFu^(K#?P>?6/,+c7P,2+_MZ#X?QMr:m#s%A#VUf`E<\Ec
%],9/P,V4_Dn6t_(;,Lp;I$W*E*(.**9+JMY"EC%"AN\]T$)B%kaEao+$-Ne-F9gRXd0MSL9&3J'/47&pfH3FA]pYKZ%XLlu7=moa
%P4U]\NGm/9S<]n86Yn?t5/@VbOIYiF\!B%gQa-Tf)=-Mqdp8r*d+F6Ig>f%u@!UlOU!"C,YjK'K\dP)X0.TY-*#`=aj;-G<g=88m
%0g@kem#ebs+20cgn,B0QVSV+cLd,ko^WGrHelmS+jrdt5lZR,_=q[s`3r1-;59/0Q#lhM"QoR@@ioJfl7rB<a+PE/@$.SZ:*+8!U
%/8,N8e@`c883Dg,*&KfQjeBgkD':^^_c4RF;5d/@cZZpD+`/b9"<#qc^.d!r(SlN'LB2:$@2.',9KHql&Q;*X'R)O<93c1^V6R,%
%j^']^mN(5Q4&,9(/p5K:QPJG%Ae2=8ENZRa`D,<2KY_&(:=2@Y%juofLjU`hP+,].^s\5]h`?Er,n_IR<G^-ik[>h2,uat)#g&Cn
%&KVE\"")p?o]C^dKt@tX']2Q9q7Zqc/<[V$3Or4WkMb'bnR<!GV^:8[7f^ej0f%.sVm9$%OjJne:7/7dM60VK1Qb@f;spFkbLs4.
%@Aj4$Fp"jVeS1cMhBTNu:_5%o]/K1Lh7U'k;4L1jU'?&5g!E/%Qe]4>Yc$brVls%5e0qH"MkFh(`Z8I@R:B0tjHAZ-e2`43]9uO)
%ToN&Sf;8"jF2E)VXA$h*Eo3,kQi0iN21Tj@lG<iR>K7*XVn?<WS(ji,5#m+;;fMUkQB[8+VZP-XW\.fqWY6lk.dAiRne=5D3Bo<6
%Amg?FQ$rbec%N72cUH9?EcKN(^@/sRmne!QKV.MBIl+Zh'NR<Iqk$3=s06(KTufT1=P0^AL%!-?$[PR:mHSF'$$j00Pc[Z_KNl`+
%dL-S[]/N&?:"YPe*hdPL@(0odJkY.RU1bGli^`!7FSOgVc/i$2DD!h/amXQ+3;]!pZht@NA"h9C<6$J?A5?"<lK_@&bE"@Z)jZ:-
%B^P\,.Y<=m9!Rprik_B-ScRh^g$QAcMpG!:+O'2Hk'`DpgV+8`fLHA.lL6UgD-[XKY(CSnA*a*uE#QY#:Kd9e>NQLBTLrs?h@WkK
%"l[*t*--3>Pe>hE$X0CeS@<]\DP`sbhLU!T\u(u3+4lY"WaMbPO1-Ju_ub[l7Lc'(70j4<\TY*$Yu7`^$_"<")Qkqs3rMnDN/Eie
%Idr+\CKG:*OjK%6I[.cnI::a3K;X."rRgl(YbW7"qn"BQ#-Vcb'R#!(*;-%Z_+@Km7/AW-Ga7"uD3rSMM?WNC@=AE"oBbaYJq^gq
%R,B"1Ma3)%U@dHXaHM:iX,eDk"f`uR::nK8D(d6@Ap8j-BUAh#kVXOtIDlJgh*,!mF[,[u(\4:6AI[^AKTG&3BtRQJdEB^D-GVh+
%a+8hiq;!AI`%FVHNE7>bX(?ob2";7>$:!4Z5P5[MLRCuTc9dE)n"0kIR70B!=qmDh6A7u[bW65)D):P#&U&KbnY@:Y7a;U(,i=0+
%\IT':PZ*I,Lcb[)%S98$7B#T6!Hb*>Vo$Zeo@-OOHW4$q#,oHI5X)3ZEj;Y_$f5kJ(F9BfCei,fNMlru<##t2jjIGWRD+I3KUibL
%&12m0FjF%n8Vm?8N^*eQb1jQ^iK)(QIP)l?(=mHAd549c+R$SB\i"Y40r'q@!tm)(Je:np?1$!]Z6i@bG$hn-Nt&6[pSu"=d@H3W
%=nBqaE3CfEjo4W"qK*JlU+pmN_T+`']0"?F3#iEjYWp!#p?5<NeFe9mVC$W<mmF7D23>G+JrPY0M`#WsZV2sb/"8HQFtl/&[fM??
%3EgXW,($Jl528IZ0\%L'obg&\Tu^^((%&n*T>H6<j,Qq]A&n.Q!7=SmK%@'Wn@mo>5gCW11d,phEbP.6/BV2<H\;uX7-59`i7l4B
%W?2II0u<+>6E!Arhpd_/H6BE:<OLcIa02<[_Ch8Z]^I0*?kVl?SD6S`i:C[^i]'S)1Zb"]FgW.Lo]I]HfntPf!6+&)-/O2@?<[,j
%'l!sQE0K@)Z\GJua,Z&T@_3s$!gW`G*OZkk:k%iV42]Y<jE)h08ZB[BiQJp>It-H*;MAa/":'9G$MH]+C*kMmi&:3#Rl?To\d3El
%Xq)lf+XBq'2_fiEWK93f9OY3-FSF#t/sV#BpIi>pLJV7i/3ZDuo@Zflq0hWF^+,O'-<lGd:]obe>Z74q1u:NGVcc^aFpZ7m&!;/,
%VV:qCYoZ:cdoejE])3i,)Y?opJVP4n$h76];M.4u(aJ4NR#r@!Z]:'PI:]t.[&eSe@E<N6X*ZVlAa*\tkf,iZYO#&5X/:!I'E_'#
%clmf0IE8fqORK2=W9C6)m\c0ll@K#$>:lMe9>/$QO9[!OQ^no8c(n(2:*?X#FI4OXf97joPW"W!q53PHk+sb;WibWS63?)"of$Z9
%-k8_Ei?%_-7fT[NMDTbso]=-7':E3`=K)l]/@@]tYS\nmC3Z_c_4KB4M;-u<%5TuabV%4$b7HmF#$PNoguVD6UVR(X4=^V2aMc=g
%Te7Al"&p[go'>#YU2\U%CS(,6DnqA%'AqNBHVeWD2CcN'h0K>_ZK/&T=k0Ht&B2;'"E-S!h,O+F2O9$O#"U'Y;XqY-1bH8;f<p:g
%/fDohDCGRC(ds%.1<_d4:ct>_6mgaO]F^Z%:n@^3EL3.C7DB5@lD,l\mXif5.M/'5M&+^U5Bfa'-m)MS`dP@(@4dNoA<E%E1bkK3
%*i7HVYOR?D<uT!)]<<9X7-W%<R;O#+XMZ`<'E^hVMRoP5gH-:dPh(NjZZ0W<*_S$(!kEXd7S%=-qdPa3MZ3.*c(cE:k<`1uI_!^P
%ncu'1_*HpX]lca4+1%fcKb-+D\U(ga-j5<^6_oOi`N'&..NR/JUA)K'nmY5DbcB,3@[$;8jlMY/TGa,TN01**=5&5.1V3orRhWX)
%du4WS[Qt[,CoD`8Y=Y4qk:"+5,q''s)LZsG;D.epQ;!WqC%phA#^KL`=pd4E)O+:IF@YaR6G%VgOq3W3%egV(/"B\>2AR!5neQTm
%NCbnPUcXo7Hu*2`fd17Dq!WL\T*:Z<3HaFc@\5CQf\3f^XN'>^^]7f$jL.b86_[lnIIsd.ZU\`?F9`WC.3`\R"1cIj8:^*+?j=+#
%rHm6$Yt&a&$KUGO*ZKF>D!8>dq+pt=,+l$`ALN(e)Ad@XD)m'%ONMd1RGSK0+(aNt=TAK]\D;<4artr85&4W7L/9b/&2I68Tpbr-
%:8U1eC-0cFPi$LYR$/Tes(CdpFuHmsld/_ln4nkpI05OHNXVG/iQ3*Hka%8Q_5%R6k5CjOM9(`gW<38p"_f*1M"B0r1NRcfXB:A^
%]H[ZTj&sHCFMb!K&<,:b+hQ6A?tZ\p,I\)@FDkP47>Hk"_qll^g<QUYEQB@M/Z2bh5l)f776KtlH;^J7Nm^?_A\tCC%I1oh8:6cN
%(?QE>*s7<qJMU@qIT9)$/>I/t1pX3a3R6KH,+8\]r.pujq[M<C$Mhu64G`n<eg&Am#bVRljrOWJgBf2lF`7q.3j*<c"?8Wk6.c>o
%'F!TgrioG(Bb[Z`8-adO&!4_mS&WZ)*;K0dhl7Wb<0O5<..S;99jZ';1-L5(jI5@/*OiDHA@b^/a!1%j8K,t'Jkki>h3rNWA^W!7
%4W<u.:4QtfMui>o$(o&GMrI+[UQX)5?cG/-`fsb^]ohW3p:]KB%P_%Cim9S"DYO_5K5X]2d'QaHL.5sio#muRr!&9HfgWg'ecMh"
%[4RLm)Y;EqI2Mt1%m[ksYOB5%o3(lfs6J-Lrcoe3C$_Jlr)`4U4,9,amY=A!i.EH"TN_WQ-0[X\<$#Qt2QLOG<j@P!W5++ST*0nR
%.p,F<&;>;[!?J!S=J4s9Af:dC_rs'M0RF#j66F"aKgAh-'[(4Fa$4t!LpWd[5O:985,f.R&`R6nL[kN5Vo8q&f+`$l;W!2uRLbT5
%):^F?rjIF\U9LZ*Z'@$dSW`26n/^=\g+E+cj"&GM]3=JR6Y-D"P<6EcD#">]kDXQ.Y;sY8m]`m>L*>Or)KhbOIPlNA2[\h^,5as`
%J5-<tl"!hd@0<'.(j?;$<qeN*/ums*R/j:AWhd.&8O%LngZ`f*gIk:)1$E(0E0B'A7fXP2e*kc0m(pSZ_l.j7E?59u?%e>I@g#**
%O'MPSZ?A>R6p-2LUIgLi!dTG+/NGU$fM<7HI`cReFq:KVV_iAQ\nNM%>#lKZT*uXY$l`_a[MPW\BN3*D;^"Vohp9Vm^>_DlY_@-e
%UF/gC?XEVOYNL$b#\@9^IE3EYWCcr@G,*Hk,UjSAH.:8.Lj3_7ESKKG-t87$OUtDJX6+s?jT9YG(,a7Xpej8>@Le:Ul8jDT5KJY!
%WW$DA5@HCF')n8EgY;O;'4H[Ef,-d$$F4_eb"+5NEbCc!DQb_Y9DTRq&2Y0\?E8MYdhXNJ\Rh9Vqe'hant+k\#Z<bOIE?0B3>cOp
%/;k7/':F.^r8P]1V&K+F'6t-d$Y(&7\#kE;S)_+LE5N`H9H<46eJHL!QI>NA3DM3cF-K;p-1+Z.JDE1*(n:B=Nf]"$(#>$\AD]tb
%6-eNP`c[SmA<\SRA"@?s%rGbo8JRjB\A8@Bh62<E_D-pIjKS<#s,p[L3C6_%*plfuUKnmgZtn9D/"O@)LO?l,BLQdqa.*68o2KF9
%CF)3`cDMMs7RiLq]%R*'Q-&XF`:&B*hN`i3f?p#j9n\1kn9n<[Vf>5q+RN5Tm%e.2XAO@u_j]BApJ_!\0A<h6]#CTr]$P*f9@t[L
%::o8lpg[8NWC^jO/rfAZBOj'H:H:1j(ttBZf2#u1gT4a^)&-hX^*k$$IAC4a-SMoe'GmXoD5&`4fqtW]Z8L!fP=T;%VOu?:G_n8h
%DF#5o.:c]ZA8%0m%EQGjnaDm>oDghATn%.2`YP<^_B^A1"s0HdTg?<@X'm9H>fs;$,8<")nU6h7s!^F)+?@lj=f+[d1qJ[,7Q_0q
%3Uut7#f)eR^-qb<B>=mh-Ds`RbNqQaoD:NL/#cbrc:u3$Oc.SdkpL)6OutShH9PsIrh,TaX#ZYtfN>_%%!AB7]:rf%bFs:<>[Xg%
%X<?]&"Nks.5F*[3<O)smVZm&0an.BK-iWqqpDncU3n`&-nS@1]q"Hl,'9"8Fs.6@eSHmp]T9E;33XFC?o1RffHad*iB-"R#6ZHKj
%b8N8*B(X%4b#36UZ($JeS`&jdd?MN+$5LGqN+SDF=)Hb8,?2XYC3elYQY=TSgB*a"&e`i?K09ENpO[sXco<Wh2V]4'<aeF$Lmda8
%1ZYH^a2gs9rLA%R"i;l$$Q.'(rpd?(>0Gbl(i+YtC%c2,7;\-j0rrC!Hi7\8Y#Q=t2s(+S=YW.AkIZSW&<j)822k%q#E,*[mITY^
%3tlNYUDnYj'PF!?51ufjDWmSJ\[FnLTHCdmrgC-;iT(:&<BU9EPWm2,k4'bp1&EDd%KV30&&Tg"c7#;OA%R%nIr6GfJ'+&BE9/MB
%DW?Vn=;3B&m?\lDB'$Kb_5oqLr2"N-Tnb4)ibsdlSbe=Oot?t2i;[=()F=h-TQEkh`0Y0W":#9@?rX]P`[_Ze16Z\N5l?B^5dLVd
%,;XAu)A*0q-#F<@:jNu7WD#2%r\4JdCAlc6D\*_qN(%7Xr2$!YW2o%G42S./cN-M&RQ@#,?J*i`qp3q$G=A.f?S7m3LsFdb$s#l!
%b$_;6\PcIdAo39I6:3rVAX8E%Sled?2BmqDai2ZP@gqtu_tIbHmqame@AEmZVD5]QIq';mS]2=$ooT2k)1:Vm1j`&`UteD3a^;pQ
%o]HGAFH%mg+i`3gheG!A#TX1\>P4`r?P4==DeLSRW5&Y&!MP_ai6=!;^e9$P^F"C1B]RtDnZs'+oO%tA(`>VWpPItGQO5/.-bEcD
%3&"aQfpsAX2&aM,K\DNKD#9)SAcVoNgJ26M@nAi)jRLn'>+?-?FgjQV.bqkgh_2=Y:5E+U%7X^0QKI'cnW-IPCCFT/nBLpEh@WF]
%\k@!io%WA3"V!tL=htBR')MIc;G]Df>957UV5,b8HgBA5[)gJoU[fbh=C^7eY3D(Z6OEk\-GP5IfpW;o]gjp9@.e40*OY9@if"e?
%UX32VApC:mR=`hVZkVA5>k0Flc4B<gWG<RKiK[trV4QV0)+GCGBnRH#2+N`R!URe<+sj`cN\b`7;UUcKm$P]nQi(pNp1Sm_Yn!B?
%bG,WN[_]3_Y$[n0Lo9$*/9hM$.EutJ&\D&n3Tt`Q,LhUjN*MAZArk(HAutghq>B@7HJWktRa2?72.P7$WPrp"$G!oS#N,@""tR=I
%9nlOZ^SG#QmMM*=OATbJBYEkd\POeAH6Wtj881T`pa]>Rb2B,[*Sg7]R-0u"3>j,T78EKf%MmUa2"74iAXaDC<chDnJj,"fiSkXU
%04gbamgB)/*+<[=Shg!T``Us%GB'A'+,u#kj&X8`9S'6U<;Rq*=#LobKiJDOcGJH;Z.\@i5bRrbLuSa94bef1Iu9(:\IRsRWV!*+
%T0^uB8n/Cl$q/BZB370Qh\atSN8Hc8T3s^Z\j:-F4<2+hrDb[#Dn^]P`&AOXHqOQgVu`20gO>"\XL3D@ouMpn<7f`)afPaS*!q\C
%G]sWobT.,(Ptsuq-fPqUGErnYI:Mm]^Wc[KE6)6>F/`h%@u4'\]<]PWbt_^tKgp5WC4-5BJdM8:N#ajG%q%5^gUJ[O]#<n+2+CE;
%^gUmsI7cp[:*P$`>#\:%N#"1l3S=_8^P6IdCV6i/e6>O*clG?Z&j)%P.b"0%dQrX*kfuWu&07V0mHPocO:!!DK6>O$5AAno^CtFk
%=%^O,QqVM[dTG%N)/*%ilc0W@1Z2!_Xg'",+\Qn;YeO>;htt-?X)=jL4n2(Km2#*&:t$)&&k8GVPW`#'8d3G#4IpEiAA<K@]#lc<
%p$74AFD5=QWk>_bLKD9#?oOPU"e@LQ%!c%7V1q$0P+5KBZ=#&7h-Kqk"MYA(Ut.7eNh\4?qkW4cng2[i6NX`rS-5V.<3;RW`YtnA
%U>TBg"=jclq6p3*\L+[TD>Z?4Pj"8n+Bq@OAr_0>b\$BX]NBQ`[o`lNV</:Q\RW;qrhFI)Y6RNC5Ug#$o-5qI.:*?u./C?sFNrnj
%m0?DIedk&ZGK7a%OL?OffG4S)XslpOmSk2_=H,b-WI9SUd;Gae'9QKM>s/:A9798JYFgB"Og"47:]"&,Q,XO`Lq)^h45tVEiujpS
%OsT"+orJ2@"#,1a(hpa@emFTMFM(bi"B`%r9;BWKFl8Is?SJZo.lWFg*V<mWpCWBOEe5Q5MI+_>:+dV:O@jd-:IQDi([CHiWT5+.
%`_L9HSX`"(f#5Fc,i=UIP1iQ9fW0%h":OSWG]bO:.4\.d@1k?*l'TLkQ3)5%#G/V/[l$]b>!7[(#tC0ROB9qOUZN-=.!To$0qfme
%U7o_Rgm%M[7]BXJM?$ke`AS)KF7YR/:!mYD^9[Zd+A0#keck*"W,Gb+5QgXJ=QV<<X@K5J9QU=Aq9`CRh4if?H%_mBCb^qA<3^5f
%34n@/EKB.>Xa]"I7&ST`i)6!L/I&kJ&'U$'Ok)hSPOdQ%[E2>jr?H\pT@OHN^?7jo7S77tmp6-1n(+2+OjZ!?\dP0_^*lSOH\0CJ
%Y<+dMcsj.Dcs,7XGj$r,$<grKoktDVW7btP(cJ[="U>W-4JuO3d$XTL-&15j-)GUro#*[q7I*AE;#Ik,YDiZ?lG8/CT^`=+@YA0m
%@b@GA"@P-LTODh_9bCBQJl\Z5`8f?$,MRPI<Fu/^2RKrQPk)1Uks9ZR6PbX22Yh),`6!&dYQD81XUW>HUn\eP5tPIi&bWqg@8YS'
%(g-(N51nA3/Wo34;q/A^cU_/W]hXn-LEN3!0aGA0X)NG!5_EVp(M'(EOsV_\D@l5s/X[K4Wss5tOI@I!8YK]Oi[@YZ8PN0i@3:0^
%GIS`n2:"DX^oH8OneJGH-,[.W-e'RS+Fka'%<2$RM;[&/.0%eb.'X]:1rV@!QJ2iB5%anYJc-mB3=BdgQ\Ynt&7h<"=3&&SAKR%+
%_DPgu)8noUID^b<WRenab*>?L?I]/5'X^*B<C[%3c+"5d[QbW+h.mkNp)Pl&VN>ND9t(3Z!$gaPBJbe22_NI,`$P2MoEuG'3WC]'
%+T;ImUi-EG4*]4T70t-[R.)1U%mh&A9%LT'[LWuC)q6Q6Qbg1,L/B0\VmKOUC7kR+Ael-VHf2G;*ZQ7jn@-$EflP3qWYTQ0Qq;7Q
%4l-O'8/d]>3P4XPM=W4i;4=>3c`:!S8Qj<e@oP>Wpn3.HEj4(2'X<pn\$OIdSj2oq_&QJ,H/6snie2'BKT2J=6M.`hIRi)Oa&Z+^
%7XqO726R#oGiE,+`pkMAUle%Y\ZL.j&`#;8*L:agd*!TOrYJ/hYS<'uDai1]@+bgrKT[+%CAs/jD;4t0$:E"%j!pK/'oH`^*hC:k
%p[/(X$!f.qSVYP)3<M_HPq*35efDWd>u&WF!$G\jRa9N1JhIn3BrUn)L%#.4f.`e,_uOWKMLK(dQGB64[4Ou_5[/mJJNj5!L.8=_
%N;s8OG.l<VO8@4udGs*As6;lh^=i:KJTN!=AtnhCUP@#%d,Zunjo`&T>[t;;%il#!V#;Fj<qB6=lEcaQ$oIlL1HMQ*\nW,!)[mC=
%"(XQq^<<cT6ou1QJruKk$#1WE7;+MlJ0C!oB4d,d4cN@AL%0GB=@VsEkU).EX!\IGVWU6_)*M"0=.1[$WC=ojPA,3o]?3hjSfY@T
%d8[*]+!Cdc#Rhrj(N.[e5[(sml$:V'1hA<%;OiW5P8K0f;')C@NetMDd#b]7:Cf-,+,?sEdT%%SXs4\1DR;2tn69bg0If;D;aO2B
%W"n`;5Id6ZSd1GU`_>2=`ufYXMW/YcH0CbZ@Kp5M`W#7s,4_TP$j/E-i:8Yi*&H]X<-92o=]V$8R(8oj=<kWZJ225>L3,"4a7)d)
%n/7`HBVq.KYD!lWHfD!_NZ?Hj^e;b($A^f:>YOJ.NJ0h/m8HOmjIk#\:RJ!3qmL\oDNPJ"Y76ec?t_9?1Q5)WXr#moUE!ki3d4KZ
%mA+ehK<L)Oi7s4>O,BI-ErQ&arRZ#I`kROXdQr!Ph7@R_k=>A#RhV$U;S5I[k6[s.-FL#:CZocG9=u"TMQKKCBW9_V`PT(MIg#$n
%)`FA.fh2j</i0L@LN\XOC4\@M1J2VWh?7uN#`o=(*(l>F4otFkKJFFZjh=7p6Zr#U-0jlIhk`.SbuYK!_?fhQ+8F/5dtdrKC-kP1
%0C(iZ0U`//RZ^NmL.We6Jm^/60Whc8=dC$""T3M2@U0dj:]DS-%L`g3"Zqqh9\U(kl4uPUX<A)^EU].o(Fc*K6W96?$ft`P]$uLQ
%6e(lRXW;:LWr8_G!ruJthb1.G4SX[$i1&rfPE<g'e#t#(2'<B9R>SW9[So?C;R?dL@6;md)s[S2"T,=OeUGN9H2qogb8<A)UPgHg
%FlUsnK"^c&NNQ<0g":#BU!tU4+4akCH8Z,l!/?&`8q*!A<3K,*ac*C20knhgJ,nMQ9'=6GqSbI@@IQ9lQ,7h;gpdGR3qfM,IXS+[
%ZK;.`bo"9q-S!s0(kMIma2CX2CV56FH:Et9=.f')%TC%g[4\1qTC6.ba(GrrF'bZ+`-LT1-F'*VPBcd_M'7"XiIG#6Fo?2l=P\`i
%d5*tXTnNo^dpSENT5(7i!>"/0p,8Hc'?ZSV@/S6M%)F-OhZ8UOgCL^m:iO`g)7e-"r-q=ITmPdV?k(ZH+Z+8$C+Pt(^$H-LFHN'@
%@5A!LY&7T=3`sphC%uTf(#M2(Sp/MEin6(W`si-^0@jZ8S)jq<+mFM_^"Z6Lq22#Iq8D%`gMrQmBFBiY;;5uCJt)hYl6P9plW;Mo
%5)Di`-+Q*7f"\fJaFN_u?$X9pHOJGHLbWqXBYQsL?#g(4?gj;34>T6\'S+_S^bqr:M!YD1?#pR?4Nroqe0nJ1rqR:PN<Vf)P6_g-
%;0EMf/Bg;s]sLDcH`k0*cl8$25qq[j+tfq?9scS'Zr3)n)g_!l?FB%@rQ]Q]h)4"4#7cJj6rSA*F2o[DZECnL15j&omRPF@P+!1S
%/;^Rj*hf5GMe%?rDbj*TH<6iXZ8D/DO'U!8AUdt6>r`(u$;9P.(4#CB5OiAkhMP,Q)49*G9=?7R]H),^CFPENT>=:h?8Sp2E^j-+
%=)rSk4'k47,F$tECY[j`P?8Qki\7]C6/JMNLKCDub6'\.MQ@ekLKXR0<uP;OX.OR*'5l'+;7PK/P79H,FkL16FU\%<B)4tX8Dj.2
%X@Tcnj/DFd_9k]&%r,Im!C?]kM(%Glij"Y!WKkf#"6>$]`=U/l7E-EYh5ehOqFT3TO1c[-'aQBpq?;t<[Jae-\(Pcul\VGV"RWe+
%_Br,-8C*-AL&X=bOEgOLonE^@P.REMnI?6qbeM73iY>2qcKVO_)O[oEO%T[g'=$GBL%nS_:X?/tlA1"fp+<P`@\XkrB^Epg[9F#)
%-%)u2i\N'>TMIYe-CGNB6b3$$E@c+`Wk[sXeXddV0lqOQa-7CY`DC"Gfk87G.Bs^Z11#!hM$k&-.]Ore*o$T(.'\&J)@N?I9mWDa
%XiIk,QT/"1mWDFu\ZJ3;LDk66-j"pg2]?re\>sjgHFK66]6rJAa2YiuZ/=\53pS@UFotM*%l&*eGrn*GLN!D1`;X5]juYD>iic.5
%!Pu.!4-=k,/O,8U*+ZOkqA02CdWiBaT)3OelW08/kic2qQhe%<@c<-V6#%S'A,#WU,<G7'.RHl<Q<_g%BK[.FE"e;!CY+8Ud">Q1
%CJ2FX[.(!UdQK[pN_k]4`g-+MJ'N1IDActUEBsI8gOZ`+mbH3h9pjCO6^$3`pp"]R:UHS<`K>oCa!>+IYT"[VYh?thUoJ;q+1!/c
%.8'33^'>mV6BY[+?,!an\0V3C<'7P"alu24"H6ik)<_2L'ohN_3LQ;m*'nmV<4K7KcpI,I3,#EG*c"F(*\q:d+Q%BV$?^)3_p`;#
%1XD@H%\Dmm43H2AZ,`2-ZeZb]AW5!d/N27[)n/S=Hk](aa[(kA79qnZC8?8E,MBJ5XNF^fKQ6s*bkU9*,bCgFhfrImYU0:8O3ja'
%b3t_&+b-SR[j+/&IpCK@Foj`tUV(]mjX_Kpan?Rrqq*,@^3PF:5iZqYb%ikKcqeWe>;[,*FiF/YN$<P@N6R0t4DH_$p&=CM@e*I_
%1cJdI,pIg]:>/8%;Ce(fP>o`[+]q/p)RP">btFGe,rs/*o4(>?3V?'kV@[\U7HiPl:(OkeZB)<Ohi$b=`a)Xc?t9Mk<aVIDN)RWa
%!shr.@mLM$pLhH0g$OJeoensJdpc*u)S2qB0@p3T=Z"5'82l*d2&MD\X*5I!q=\7"U8oF:n??9^3'H@mgReKtLmc7R61p5qmA`Pk
%U8NrX7>G&73;/'f6M')#G]RC[2nL+*G0!nP\<[OB4c82HjGPlV6DEgL4Acr(+\.,-3sDXqWIL:'+(?G44_Grd`!#DnmG4ql;$AFq
%HWT;.WPEVNRG'\Sh2-360^S\2:pcLLLNp=/7&mm*<Hkl"JoM=F\&ajL_;-Nm0>,4.>8Ub<.TXpj&h@e%+0Nb[in^hef)2fDs1Ol/
%;!tUn,o;!Zf\ear=42r`T)Ptui/2OZ*am._XM=*rFr.4%L!+u$AD[@l$-O]GP&$'d6U;qjq[KSV@5VH#0Y1U-:9$Ri0Xbd(K\bQ1
%6h]>7hc-f.CT.Ji?ebQJ!Te+?WB!&;-BZB:a'%DS=V1WISmVBjCs`o7.V,W@??9j(jOu2\eW/D&\#N&(F=eHSc]4YUs#lu:"BV`i
%%`Gh-c*[`LG/6_^m3(:ZTHs4E9@kZ?McCEPDbFPo=ZI5&`oC$[\dHt>i2C+[pJ>9",ceP`i.Np,)@o5ENMF@"A7uJjq&.cmb"uW_
%X<e>u7V*jP!fT7Dp,)-oflpS>l:D+[78OkW?jtDB_9>DC,sISpau*JpO2ubY,ebVF,W2%lc;]r!_:Vs!N.[,2Us_6_F>e`[84[k7
%Eo6aX_QOI:J,4^,epc3[=TEP(!ejoX2%]bAQ&S"-c#,nD0n:\F)L@5[=j)8]ZG_i7VX<\;4DL>]M!STueqXC0ir3pa#`HZNBabK(
%65(YB-'sI)WHK0<+0,#YPI>X6HahU3g#o-3)n,fjU!T#LD!FO1Tfi/U/=6o/ZFM1\W.Zc.N3H(IcCmfp%'M>lh916M(,Ufs[6-]u
%HQ2@qL#oI2Wf65:LTu]^QH#&Jd**Y"]0I5f>!r[bhd^u9VQ3je`tPto;anVUo3M3.2,5GaJVEWI;&p^*Z"PqIh2O'4)8f6bVGaVo
%<'PqUfP*H64,9_Fo;TIlMVecZLY&ORY_=Y0b%2j@o[b1$jB)9SZtdY/4Qq[C="O<@8=!=>&*nNceW_oW^M"ecG(K0/dlg`+NsY7I
%,W]U=5OWFJnA8';KB25<ND[RBYaE``6,A1S%Y1($&A/hB$'Q>W0>2n.BK4!Yl,,L!&P/d34.21deD&T1l<>o'OMSB8!7%e4F;58c
%6-+$q$&V+W$E4?kLph+8eN%_*O,n#lFYjM*(d`>!nbU:AO.IRi+"IK'I#S`LH=\Lh-&8\`O6ck.9qA?"5K3WD-Qpd]p-fP:cPd]M
%_"!5,*C9B6WZA?#j(3ALgn6,2r?1rRScM$*l69WOeMDL`?N10#'QI)2Zf7;&LFUi\^63":<F9Ua27)h(9L$Qg^ZYV"Q61[N#OTN[
%iV&*N+h*l(+,JL^QH(06?/XpcnVut`#Y\,&(pXd=<_5Se0>CT.H5hZ_0-rP5GQ`g:ko`4L\AS)db!+XR>:#4qD2EmfkB?q%HZ\3C
%ee6e0<6HjZN=iDZ[&^6tn3p!>i5b+f5X`@U10J8^2^.n@1`5S&2LrhfNr_L2/=WUd6;5Q;?!LV\&4D!S"u5\2`9EQ&]cTr4hO`[E
%?VXlIUM)rCNL)PT*@#dE&ReX[a^YaIR_"_EdF4i1,Y([&J1W4u&tuG;Yp9*IO=_/9)FgNgo-MUWqt\K;E4"Zk$Br7;a:ZCjW+Wk$
%BD<&Kq/p2%Q<X/>cV+c#^8$*hCn^?/A86tEB9c_;=.7l[:/1PJHH\In,ANf`EN(G,TGA[d@;Z.W(PJ\5CqiD)*^HZQ>I59P2dOj-
%[DkZ]c_LP//Ho)l\qtY>/dM1*ihDCoY)QkN\f99_E+^qS,>k^f8?>W"gSF200>PengG3LlRu/+7RD-;eH?gi;,s?4sFUA!J/StA3
%BIL1@TD`s^,("uWVB4E_./i>52hBd]FPD8'.1$5#U?4_XT=_+F0ho7>k,/cc%acVG(E%W5_PmIODk,(%s1/+^fGf$!mY\dV"q(5A
%:n-_kUSOQ_K8Ai!Ne*QUH3kW3OL3`4J9+U;iA'3Iq4uH+<pt)G0.$H9F-BqsA#uQFIZ.3V&WG?49c$\HmQQ$bJAmC,ppo%rSFKjP
%)g`+;-f?jR+7d@R't%],L/\mF!4hZf1rKtqT#ahR,:kTo*!oFtHAi*RQHVn9LjjH*JBmV[Of(2-*ZK^JYjo2`nWH_cIu8q0%<.XQ
%0jp>arF<LkV1VXd+RB;(#15tD#Y/RX7\mSqLA][8-7ddCHit)C4[sBP0V/?4I(>7L*m]RFU$A,2ZOOo+q0:0pbO1F2\M9E!]=+K!
%a*uQX9C<emW'!T,Ll*giigbhETQ!;#no+UM?!u?C>1NWW,NRF`,9.'-3/e]O3:qB_k-7]*UUbeTjae#XXP/i?]<c0miH@q86[OK!
%EVYDXER2u#bg4r8'Gc:4r@KrUL=31o5c`[[cn&p]]u:6^'n-:%`FWKiG(`-=o6$(ba5/WkSfZo#.6;"p#G6mEk],EU8(n('^b3@V
%(AJ7l4%F@d]:=!.O)a3]4YeeWm&?LildA3o\dcnG\,*=^#AhX1<(nTu)il\6R]>GaFM.b`p*,i2(opNq!'<4gDV??-^oDuWs(Wn>
%H<DD+\[blgZtN<0IZ)7,Mq2iM!p'"[TnitWD!jnX/E8'g=6bU<!2,oe\HU7,)^Dhp<Q!OV5/IsLB(tSN4h2-]#_P4(%"`JU1mZ-o
%KuQ6FU+VY';t/U@&d--U<BI5%On-s:/W]b'4P;$^M&-4QBCu>9rPnE.3QaD.j*jWQ-'=$X-Y>LCQ+WR:3%Ns6Jj+pAU<!;tZ+d%"
%6>\H4h0cUk@icNCG^\+Viu"P<II6.O3nZh^!9.IEHGPTnn]:sc@8o[)7iJ?P`ePSh]rSM%*1m65P]:poZ%G5W2Bj150n;,!OI+>q
%6>f3QMkWe'7NUmn\7O0NGH`pXZ,.rL>_+`kXRuS[51!cqm]iG@Q"(r?-rm)!GISr-4F/[C7d\Ab/C"4/MKjo"2>bPK1m*l$fuqC7
%;EGMQa8S6hb>Ng[=i$`J0$ocq_i:iM&itKH(13EiI!sgL5/+2+JOYdUenb,c?6B['LZeDAKco?\Hk7),^MAP%ShWs]kFK2eNNM7s
%#:&B0?@-LTd/6#OY15-MntJKJ5o_@>2pVgN#J*[->?ef7pBe6bga>dMVuTfoa-g5-Z6]6dmQN+qnSq.[UogZ.-i4id%Y,V1>[^eD
%]1(m5&OkE#];8U]]\,C'XmR-C+g\[9BD4nN4oj>k/YKSaA,-NYcH)Vc1#AUldBnp!cN;G+)3`5qb?n//d0#WsLr7WMQVMA$mPc5I
%Rg:#1-!LX(G4+H^8DGHS9WNPp"B1KlFO.HR\.?+l7X$X'r#YK7*.?YC`CMSr<%,:<5H$$@I%aIS5&[""Q.ULkU?S.W:8GTf!k4ko
%>,ndu(9HP$@W%Q&/Ka@:?=1RA+_DcZ6'b0QN37*@%s`V'T!;XJia-"tbcjf)!?8=Sbk%t?b[7R4'kf0d'qegP]BKCU'tEPKYBD00
%XkRi=RiFiic!!Y@:2O]i>6=/2lgFD=[`-OsS-Sju+!Z:m4FrgI3AMAd>]@dh_[T+\4E3H2'N1$T21<&kN'?o>[kN?h3.T;&?c$b@
%a?;BUe>J!q0UOp%dZu*oZl8F'H[9]_P5I6G6_"E0q&ceI`37rDP[%a@e8+oefV2E"6L:DLNpB$uN5\n"Y<0"rU$8Z@ABks`<2`]3
%71jLH3'Yc'.U!qsW>ZtN.nb7E(&\9g*288DY!\,ZLbO@^nWjbe]8U3F^;CVQYR!eiG=j:#=<u!8p%0h3Y.2_AlKrZ2N[Q1r+@q*+
%3`:d%*d<jRe<+nnN1k"oj+sKcUTl1Q(p^?fk_<N8;"u`9%.U.;!o&U=$1VE"k-bKPEiR=B'JpFR!Fo!3)UHQZR);n]eZZ`TOD7S`
%4%*u/R$NR97G;)1,_Pc!HM3(.DTLR)D?Ce[WY2?W>Kue5E?HFV?7^oJJZ3me&:V%^CJBLMoIU\@qMen_Vsg)<k<+)ti)mO?e++G#
%N<2N*IYJBqd[R6'Gm?1_V$/>d^+bjT]/2)fO8+amb&G-%0)0/4Ngr\%&^I\$HFS!M1PUtj2V^F4Ufq&mQD$VcY<C`<8@D4^Q1Hm[
%YYcW1Q*Dt5DQmjd,HdZ9o5A^lFd7!DN1-m/Qq8-YHiR5/3nIuGdG#*hj=G0dZqEoC+@.3L[OlB5g?I="`G`.qnd27(Ur84,)=SXj
%@K)d/i#iA&KciqQ,eu_,&`$`XQDr:Q':u&'q"K_hQ7HiW\WR<)0L;j]OZ%'e4erP'b5Ue#)gmGF8aJ<.inu._9t5tmpg[Qk!%43Z
%hZ:3A64/TIXmI5).1Gfk=+Q;t:u<RrnTTX-r'.1qld&k1NOq;l)4;`r![B\<otZe`'`+,Y5^_ZiHJba3l\i<ZKiD$06rG=2R0Dn$
%+%N38/R*EKMs:f&@eG[#SP@j&_^Y5\R,XO3+&dZ\J8%breW+0o/FkH:Tee"Z#F:D"(7V!Y=/0T'Y"\QYVsAm)pSbsF"'E_VnaD8T
%juKh7k7tIm5F20]BQ#*>"2:8T8/`kfYh8K,!6K_+eEBut4[q]uM6/F5$=iA<6:=^Sa_21Mot"0<#N2ME=A6o.j$sg^a.H&Y6hO;M
%]c$>hJY0K@;?RT'e5*]q<]'&%'/6F"PuIrEa&!f<@l:mW!=P7UrPs/W;F$?RD\<c1N/Cq_`L-^?W#GGO28h^$T1i0fP;A)T&=+LZ
%/XA_D1XL"d*;!^a4oJm-GuUtQ2VL`5EL4Lu7f^J.Mm9eap_^Ku;?V"oq^<]\Ve",1@Ns'OFJ&lPjT4<)*je"UBlR+2OC9IT8+&pj
%-edFW^H'E6-(j"*:SS'&(IVdSGge:EoWmPFH.X\uKc/Pq5/Ui5KuUH.KE]'aC-1,HphCFBi3;RIO\n:3G_VUgCl=1UVI&T2hst0,
%OX,-+f&>44-M`h8B\Y1NKV.;Hpnf5H@==\2<c94AWo2;R3f-LU`LPhkUhG`<B#Z%^B6_kZoKPR'!a#2IV-=/uVi(!ENPVOTJn,Ju
%$Vq'RY%Qk@YY%:'JP4]BS^kqY100+=XgmIaJ*X%5!*gb@X47+Q3PfI#n7n0?Eg46K3qi^dOB'>Jq<\qW7.N9]s'N*cQ(`O0q$?]8
%&OCX3p*=mab:Hfs*^Boii*>f@5tYZ%,8Q][oDgJuW+JA&*J,Qhd!_jg(J[XJ,IV,no%6G9?IWD#7WoY=MF6VN^,Jf(Jtgfc<$Oqa
%IJK1<5pr2mVSX9EnjiEtdU!!NU0MbPI8B5T2l`$MB#gn,FG(ct0`;mU0Cci(Dm!)qXo*A=N5SR2`o5ZcjMfU@?QNV^Z`/*![-hn,
%G2;"<$5/_3VlZq!@_EJ5XrDMo-te5aSI2HIL=:Mb>Y90sk,445]W,VSUP=@a>)_K0!tSV<]Vjrd2-K6ln3>Z8WOo-f5=J'8ONOua
%Z)(sd06+,U!(/^[>Wo^<?R&6>e07%Gm:QUX2)6R6dk;C0Z^1+![[N2e'+mD_$aWLQ?66Mh?_Qg$V%k(h?("4ak,-oV6I2^]4d8a`
%WK\<a''I0U\Hk>.op@7*DWDOd@SG_9T'8=V^r*!\gElf7p0WqZ;m_AOaUaG*E.Qj`mf;_6i,JcPhlF*V\H(2[Kf[;1)&@>)0q/X%
%&XhDlNF-?35^F#.a:7h\FPRMjq&"m`m>2(*dV@`>b.^PWpfnRtE^'!c8@-:nLnGXj6-P!C]E14`(n:#2]&j";R,JoR)M`R"\P)dF
%gA=pr(O#3g(orNsXEV&'\=EO?_u,\'/#>1W^/)Y<m`=oN*kH_',:Ot+9b-Z^kjpl;QF\89g02X8jAA%!g:hX1-#0%U\W,/[gej=&
%1"NA8=2]<WMHlPE$'(J."<J\$dk]+k<A7WrX0h1Z=CP!seWERHCKbT<=^NIe#"N=d\hD7SXq2Firfh&t/o%_56Rc_TQo8Sn[ZRpt
%;F#&\9HA![V%Je.e,Vu^-u:9F_@B;*B87WuA[V>5&UcV_2oh<\`7[3_&oFUWM4[uQ3>*10R-Qpp?=e&CaM?1e`T(T9A]2O\aL4WG
%OJJ$%^H]`-kg%hlA\k)/qj:p5`HD'S;5*ZXRb=AG;WL#uaQ+o1)c40-4?"/aZ^8BWjb/jV'J]gE$_k'd6A'()>ruH7!"t>U./$al
%;`UKu]@0bfXT8V`[%*SIE[99\"E@>Z7s-Wq:_X^&l/[,?-"+)0q6%I4_dE3O#j4B$D3r>fN;[`*1_rW++Cg7XB9g>)AE6dPSmgL>
%<<c1PMap#UUCF\XFFabV56qJGL?K=N$lDlIYGD'=!MJYcm1M+KEtq=:b@2$JN6`A[?86UPAI2G\DW9\T)4;$:;:JTcJ@\4kj9fkC
%RJUNG>pOc)]K\%'GLM.@m8jREENI/N\`+2r8L*)\j'nW04k)aobBjah[Z/*oeLchc1'-/Q5m\CT$dOL3"`55W_*\BTH8p[5#&9q6
%q(r*KfG[*Y^oc#r&gD(^-RG@M,;LXhOo\1]DN6C[TVtjODQj.BHL5TAd,B7&@k#oj+7-FsI7AEVMk1QYiYc%N>@Ptp7RZ_;*nl)5
%hu&R3?t7o)IQ\NF9s\KpqYI8odq_C$ietr]_9743\=X17IkY59(>VAOJlP<WC6ln2;5er%^jo+X2Y"sEOROn^I0YGiV0n4E8TZN>
%Y+-Jj"ssuT?),E[@Z81\<^'hV%@1Oo]7('6o5;F_Z_L2LDE%H!-7c>n"W/IP]kbp5;HoKemBf_^&6$0"1\]rIG-Yi>.W:H^Ao:O*
%.acl#;07g?TuHiOZVB$sMBcCGppp4eOlII(n*k9qdf?b,8'AKh(:bH0qjAb]NOb>uNuVg#;MBk_O`D*V+]MV7d/sCMj5aG\lY4Ki
%OVQ:UT,bX(@0Yh?Y3s'%^/7A\nH`rBhD3s6g6CR8JA$)r9S6"m9Ap@P7/S'p6_N"6q`%^OFCqh=216LPQ9j$o]eP-bZSIA\h`G;n
%l?=(>Q*A;R]U2_DS_0eqPb!Y\gnT4LC=I_3-:)Q&cQW<*cX4008AI=LL?oVp(pZ(:.1nX4/I+L@*gt&[f;[g`a`eL(M8D%W!NeE[
%7qA+Cd0QRl,`t]t!:dubUH)?[>A/70*VNaU\<N078Yo*($4ac44-42dbO.tSnd$FH0-G::%@TGtgK>d7p84`q/0?i(8,i8io">Ub
%3;+2ONkJ1>d1uV[&^W/_A=cI8^uUr4Thqn/DKdZ,?i,6Nq/k2'RAQ0tX,*5H#rZ(NNPZ784]-8!rN,&,Y+#\,Y%e1qSf&bX.[T^9
%+Vg:f(mVpu33(pt:a3#)*c:/m6,2Ytrjkf"C3iDhWbZAq1).8C;4&%G)=4kG]3"c,LVJfB9J7\9^-0=e)Y^$?]@NPB6j4cWH#Hu2
%=U=6&hi1AbZjB(l.BrNr.TTZrO,\%&*XLSorS^)!02.0##r&HokL(Ribam%`)>qH$4c#5NIo6aa%KrN3;k<>m"rY;A]kSRL*5TG9
%=s=d4f\<2q:,V&=+2PdCD%_oPH\8^kSTf69&0e/q71(6@?JZXEFW;_aoA6DIoHAA-KRi7hjnlj#A(%h2H=WSl],7c^r*\p3M=3p;
%.WHrk6th,bJ!S$]F@lH/b3]>E^.*.2BLAt`lGN1f]YqEQh=$'WLp:^5cA7/c#<&^n+Z0V,1%1r[!epJUP"o6!GY6b]'OoMe@b":s
%`HQdL'fnoXat)]6LlNn97N$2ZX3Sa)4Zu@f`FZdGBe1r=+t3\(j99,h@E:iY5qXBo11d((4Qc`-M^Xh!(F#M9)W?-UkSbDq_Uf[i
%/3SIYYraX/G]A/PRfD$#E7im$6fj=6CO"TSEnPjbUY,('U$jL%=JeojbNIN:5@-/6fK!cTbK;nqchA;JL9<r+&ib'SB;?4I3@"=1
%[8&M.NL=]>:,4G1[GA?@)/6lP^:o`g9rD+:IIDXWVs*a!IcS/:YUuFu4/H<JdC]C6s,(\;@Un[4V%f6H^+b#']")S+FTH!%]n]Cf
%GO=Z0Q6UYPj!h8`OY7(j'n@.0CTcI>Xh%hsT$]V'lUF((ATm^Tf"<%LW3Vi@(`uN.-H5X)bW_(5_YuG+XN"COM6`M_[nG\O8B_r0
%j<jFD!->*X\7`5Sr"K,!#<&KO9XZdTCW,gM'@RDds#alrQS'&5BDT=Z4Yb7$FRrbH-iSDdRr#96NfTYiTrpBGhZo5K'G+)f7#Z!g
%SLB%)fNQ^<1'^NE;[1>2*$#e9N6TDm!TKIaELBH_j0MqD7^0t?ot:*8fhnYXT6eAeNi;R]U6YY6Csm0Tnb2)A&aO?sqq?EqoIDR[
%GfnDtNqW<RX=,(uL4MUL<dCD^E_nka!O\m#@auB+ZU7OZ#sY=%$^;eZ.DYQN%6@uCWq7iQ0j,iWZCOuKB'tmV4)Di:g1sn@k'jhh
%b`SkRSeUGeD*O?a`G_*Gbi^[@[b,Xh9Au[n^)N5OC;`CrDhsejpctqbV#I/4:JnYD[mN0?Ha'5K4%u.fTube3I0H;T$At`VgYZOg
%KH9ub.L>B3bnRS9)<E8Uji:JtUO<[3n218.%)is;)'Y+So;[g#QE0";_YTuS4?)cV8E3I!90d,#hIf@^>=W=o=$`fr;'7TQ]9:b?
%U6k<%:&qJV%QORCDIS!J;(5$YMeA-Kcs?c,auYZD>8Sc=8>XSHFS9Z(0r[g[r]OEPmlbSbFHdnu$qu<7F3I/WhknT[$Q3`(5Lu=i
%`tY'r"d4VMj>:/k9#O\`;eAQGT>D0a7;&p725=QnVbV,h5l=!VAWlNZ0ckP^12hZ&fXT=E\;-D`;Cus<.<S./bJF$hJTj@sBe"<V
%f2Ai$9CjsD''.b4%F>H"Zbr`Fi.bJ40j<6:3@R=[58(iFEI\bG4L7^HiPZhFR>snr-ceq&j"u>Ig$4'-l!ZtM#1/`s$1afg/WIe[
%SZ(]Uj)nem,"d#d_IMUN;_;9FNQDB]4VaO5F!r%VXDY`*c8re77n4n!E-s77[k$<m6.P@j2?[dQ:r`W2b*3R]g?GV8J]7^:p_;kq
%8W2>*.idSJEl+VfIWf>YAh6htdg9*$:agcLo&c&M=IJfiU^n]Yk*+XJ3RS2A7:OUMEeH&5a'_l/0Cji:4%Q$rkk2V!8aoI.8GNB6
%Fs[b=HU)Sr0V]1c@%PTfM>r;',.>lP9KOl8+d":hlCfPY`VEdLr2XccMY'idO1jW7YMfJPo\9XKo.9=l'C7(K4fT`4',YaaI8NA!
%ABL#ig$TmH:*G0FGhl%%TlkF_;ma:6GLUJe!7iC!3U/aNrun,MR=Q!mg=Uo_eB_,6d&q,Ai5/dF^9Tit#j+;`OTCM99KRpH(Un\W
%@#.+I,ZG'H,;-<m:tM-"Re:@Y^-V2*O/iEk,W.mZEl$P*F[,DP#X<^ao>`f<c+`#:420gs:9c*a?*DSEq,C_f'![+!OsqT@M^G-_
%3G?f(i6=_LpJNnQT;OrY;XRsJUl(_l=#hYXU"BL//c\@;3C740bcY<Yd.>SGiL#FNH*(b&3W.fk^h3K-k@@Wl=X.J@^#:5P:rH.&
%*>ZUg?KAMcDc84#6W3P61!%.Th0+KLT=)S_G_`f8$**/ied.XEm1_ejRh@$'CY:(ihL^%Z`Tr?S9C[#BC,&6B/?2bD8=)J-We#KW
%o;/giS!n/0LH#ZCF_)B/q*@KTk/,3B:DjW;7rLlt0P%ocW*nN9ca"W94_^71!0*l07%gVMfr8M)Y)GdE1"k1Sn#B0/!a'!_m>#-'
%R4P*_kEf.K-A8."Xc@e>`fs_0Y3+q@fLNl47<:P\,I8QXJPYfsTC_jQa"!Y/-uYZr\mg?Z8&4k%8%n6q;<:lOV"H!"T^(PKio9,n
%F=&AW2pWo,N')/=80X$:re"7uF]===6EsTD.g^i:=h0<N-bs`.#=#he?ubc%-LJ*>p5Keoh$SdOTLB89pRYY^3)d6H&1S#aK72eo
%Z&L:Y_O]hK$g?>K+2+`W='bB0pZhp@/DiRhDf^upET.cf61M?AGLP*gZ_W6>(jq:t1->?hR!Ik-Vo3E.DPc`&n#&UVCgS2Qa%RF/
%qU@g:-6%!GF[Nl7+'P1IC\k-1I'/MYQ-(b3g%*OfpRH<Fa1XMtaj?hYQ4F1P+il3oA-L)Y5`_3Yq!^ZO$RF;E[b*!c`cp5.9*UQ)
%7;m=>_B*!\h5h.J5e(_(bF%e%QdqZD):?`W3Ik@D0aLth4YTq=(km+)osNr4_R=<0oO5<LhT!\DTrEiJIMjf$.QB//iY)SBekn8u
%o7R!kn1o.VG7O\Gkuq8@0GKSpSYu\mX[q5la,BU1cGu?84V<DBAXP:e;!<.:-%QCI!ZX+1(91W\aMK*3=F,e>eBD0*.[a(dZUMZb
%k.P*3Mo>`$>W!_MK"Eqt4Pm/SMkM*T]*W[6@m><ee*EF8[5=J!:VkF)l&Y&HH_bc&+?e!6ie.XH:#m*$+[orZ:U<km1e^d_O)m3\
%_C"43o)8ZIj<\%R98Ra`+It(PPQeVb^F"Y]3o*LB!l-Dpp]723T(lK?-FY<,!^U=Xes%e`6H5>Y#3c*R`FRLu5?!`@'6oDBZ.3q$
%fh8R]=&G)@DE5]_Z:RmN=[3EfWQ2W$D)<r4E2-9!N1;eh*dgEu*ti63eBj0AnC%M//NaZ5(8Ham"E%.k4cF1>EmrWtemVsp"Eu7f
%Nc(arW2o!Da-)UOn<O.@+s\ID:jbI/B)LA?,Ma;L301`Uh_3jKp5"6ee)1KYrOXFc>MD!ONhNFJb\%%PU$1=UO.l]ojWj3J]4-[?
%!qq9C%'9+r>cR"o\CRu1?>1kaAGSkK*iW"/'04#SYW(,/$f/U5Tq%tEJbf[g#n\G(90hsMZlFY8Q:#fsm:A,d'9%-iCt*<1>fchl
%N@]i?_QZrhe40,0_;d^nr(TB9Vscac"/)E7I&b8*Pi2X%ojm29DG6T.SW-C2Z:GTX\KNa7g">nR2:SRQ+1E4]csWfY$d7&Z'Y*U2
%E]s\-G_GHZUOrX#dVn*K.%Q$)A-Ja<+a-uNdH1,T[oO1V0g);(SH)HNO+e:3fEIm40UUq"Nu5&#J]naG9bR<<#TidqSk_MY0F['S
%^gG7>7R=Z3^$Z2.F'GUH5[6NjgOcmY8<l/Y97B@_DfCUY5GU*K:d^F8VphCP<T"7f8@1)dV%MDgY5oTG<WS9'S\lO&'n2Fl1+O;S
%]R,1`$OM?jnSnF(TS?J@?Xe?gD-gi8Qf2572nodIkK(A+cCiArT:AZM^C3XUBDCZN*:d6l`-1`\cfCV%g!pP[^rL>boqa5:f#`3X
%*4[>5Di+c3X\P-_&b"&GlN[+R[f4)&!5s:cDb/aD-D,%6le@YeO=W.a9P(el^I45/)*?Khd7/q+M=1Kt3hdJ/Z)ZmbTg05S!o&Ul
%*<;V+U&p[#1%Gj"*F7_#B6"(cgX)-E7aq7S58H)L?(FWjFY,NNqk+oQ\7Ok7m<K?>_ZQ%9KiZ<PWk".4>VTbGOk2AAAqeh\4hBI@
%l%UJ=43k;J1q+bdep_]h''u)eR97QJL--H4ic'nhALKL`/h%A31Z=")p7LoUde.D(m9g!8#GVu)+IQs!R;QH5kqR'U3RdS@(d+*<
%jS&/5e1HCn[D/r5V?@R_mm;Q@BG!EGAFu=kq2H+ia^^_2"=b-4j=pqtoe^Jl@M7i[qI5m4OHOcW]FW@9fc#%4;91%g[&.lh#*8Il
%5B1gW4):SK*i&549%R_SZ*El>mg(,1oJB"LL82MnZ:c2qY%BGOB3YeJe)g9.#M,-_hla='V3I\1G^?c\bZn5oTPsdjg<LD>\I$*6
%VhF>#XZ%58b:eJFmH)\4f1!!gDPIlT#)7PenbhScG/V)>2,:X8OU+Qph!(_?0No'ZAFN.gTqh_GOTdo74dNH1U6?/=,n\4TLe]5\
%\<[+0E(%aR!1giMhf-_[UqEe-lt'\cYUl*BL^Ae0`XTp]e=65EM>1CD`O;Dt+?.LS%G07K$h,-'YgnfsANhbn(O6CWVZeG.E_D3C
%6prCIh5nXVFTQ.*Q>'m`WGh]Uf_:Yr[un]798aeOhBKeZQqmn'B#PS?qr=Zsrcmr/nIk@NLV:m7]AuN3aL^NcW74aK92BB*UU9A;
%N$6fQ,kD9dc[`4h%_Ufs+d26XW2@Wh7\Mkm7a*AImg#Hl!rV4ni(O]a/"70dQnnjCFs+1P^V,)tOJu1g,InG_E,$W\Z4W[qX:#He
%DNWB8:[^.(N]<=o:`4iaSo#a"Ga1(ti@838I^#5?HAK(AUlcnm-MA]mT=AoqS`Yh430Lq"B"'NWY!bIAG&LB(DP*l`1PSqHNu#[#
%(iRnHV*VMgl$</c$Ot48W<*T196`-6;t4`=8kSBQ^ce(@")Q>\^08>,MKY,8eE6`iFc4Z[=Kcqf<D-;h,-7KFITtsGQN#K`%p^4V
%Lq1+b:*B'!c`)M..-um42b4H`JcV-YT2'Cm^mqP@M3I3L%'WD@+I$=1"QRnud#I*S;;ZZj'I>VYGDR"=0XmYJ2cY&^]d'noGTk5%
%VI%`W;ZQM*Y-28o]rXk/$[D<^Xh\3Z:,,QU8kWc')>=C+#:hN?3%7h`9Mo>!S-f0]c7ks*-oUpb,_EFanp",WFajfR0KXq_9cUt@
%+IfbmAKOHhE`#HIFo:I7+pcqh<l^*Y$!tLE$l$.mbEa#*19Al9gF[n1]5i8O+!rgl:pY@Q[9@f%O"NruOufn\jm;&#(3c&aa`qbt
%^Kl'Q)oM'4emG8/W<Omn$8K6`7O8((@q'h:[T-'=p8PVXphndGV\A.D+-q7sR[D`52+EjiX-nt@DNsS*Ns)OW<>.FAC%>n\J$loi
%2]%)WpYKd3h#K1YE.OBb/Rj<nk"GP7J8m,>h-U@,AmQauejP1oQ9XrhW9BN&[VU)g$;$[D&0>>dU'Y:thKWl=M=Yso/0V-Vk:`X4
%;`V"+58UgZY,e#J!9pQ5(#U`Fb4O"mEECfsFF*Fd"--au4<QdEYD#[]"Blr#)fZu8FAiH"$fHr)E!:pECAkk@Bl"WKX!^e<4]3/e
%DF$d\r"73QA"8.'0qgjkWB*)PRQ$@'33eXo.dJAZ+W1c"l(kIFjRnaW7:6$K0;&F6)JVM_%c.4KZ=;"V6'$u:g?b6;S7W-K$C]aR
%1UNp$TnZaTA8MO2O^tuG;BRuE;t0X:mP4]hD3?m5JMB:2KM&YV#:TjZa%ko*UT_6E2h.\m?spA#E@tm2<`GTo<-u=[\/RgNJ!Q>W
%^+c??_rNhOfl[T$,KT0@=]6&V.iMm6:?bl31"Q$/EV>%j/@T]"9'UYO)DMr;NimJ";)luF.Fld\+X@1i^b9\uI#UN0[?WW?QLsGn
%Ad*odI1kF0H2Yo4n]DAlp>>lAVk8J6J,[cDrU9dRO+7.QJ,>T4qsSu_rlLf3cQE2Ls8.<GnpgW*?i5u"rmUoHbFeh05QCN%++O+d
%s7k2qr*TKaci<\lIerk9J+Il6rC7B=J,$AMr`=UHcPkh^J,7XOrRKg%h`pT!f734$s8;?^_k6nR5Q:K)ISs-PIV&=.^]2pQfDbnX
%2uiA3keI*%%0u>dr%H'ns2BZKq"oR2?iKT!nR)=]lX0SSYQ*`l+e/>#PPjlbP/!.1?K@$G3fmH%YW=coTQ*-"DCnS!;-2SRDIB4L
%7=^=HIWVAVF4BF1A&\RAE2r[)*9YmP7iB:5]akk<*OYJPN:8mtPu1Bg>Rq%_T%#/`=5/CHN#PHpUkp0g93Y$O=<cr:@o5&0r]6e^
%i/28;(N%5;EC>TM*[ZgkUed>^TFl\kV/d)d]UM#SP\2ZZ,g_WC`o_(_r.-B':*?kVUK!&4(/tIA%MeI]Y#5[")6.Y.1%Fe"l2Pj2
%&l)(ED/bFm,H=\T+@S:DU;i+M4!L;'5<q\l7*r5Xe^\%q"*D<mGtfdk4e-W++67G2mY(IMIC:bn+hK$S&I\q`7`n.+\h</idufS+
%@B+ql`Z5U&PX&E8Y&%MO&Ia=Rmpg8[l=IISOr<9"),_WgUE2U\2R;<NCd:W?7V(T%/rL:lJUD%8'uL-[qn[I#Y^IsDP!"PfW88Kr
%ds(RSfYpu]@9E3%JRX?So%#f;[N;q!"j@gW-*n/e4!L%<\O=o-Ofm>c?FJk9k]A)X<@f%^];cR0VH0b0`'PYdW:i%"\/jdB=b"[,
%5eeCL<aS.6W>8`R]oNi1YcH349t4bq!3^#3IHe$4b>@Li9&/E=6P5!q6kXiBF=g=uJfs!"HhZ,D_.$)dlarok?K6A9.q`9gp;ff#
%4c_\KT>P0=Mp2]2XNaX-bA=1k:k$CJf+nFN>b`K<TN:S[;$UMA\Kg$I2BbAjoG*r2XLtlXTpp4bW:-_f4IDKA+_U_\N+^G>94?$B
%_7S][1HSoXE3rSU1t\S$*Jm6?C*+WT,HmSHTPA2`$Upd?[NJD,Ei1fSL+<D,5&5ncFe<,6+dITP/WmE5]oZ;#TM83=brlO7FHIT1
%-.30^:e]eC8<-&$OLPTh#lK^ub:)jS_0hjD,,V'J0N:tB/#G%8g2/=Xkq6W^]/h#IabteY3HY8&Iten3WoHu^Tf<jk,8-XVR%=A)
%aM1jt$Q%M/ia<;eM/cWPMeW]m\0q0%7F#h5M]QqRlDBh/-naG[D@BF^/**dMnC#@9l=A_2?*#sn=M2ViCZpbOa6ePAWU.<RrL'CP
%_lc0I!)-Hs%Y%UXPHh*3N0oF*6k3$lo/p#QJG6W6"u&0A;a^)ET$"gSBOq'W3NB"WLN*1+cgUZ=*uc\@dQUdakE1g"h;]SJ__d^A
%2lKMEQY(rIm6tg!YX^)g7"6NMAfjHeX=00C+%cB472HkF+K(>N.Z\O8;ZVcqK7]&K3X3L/;3=abc?d'-La)#=.?<EMAIL>
%aj01k82%0Tc&j%C[/^Likn39F;$(]6+qNIF)F$EOS13G2jIqI$A@[.td1>f<Uje?&b,fR>"2Y5qj7<S+0e;OHL/K)O:7"[eUQuL%
%2+ZrRLd22aX;b']$Z%`2/DTka;8FaloW*.V9/=1/7jQ=&BunRR3cIL*Lj"YLL.[V,8i>ZO?M"XPbDGRP4[A(I6j@i71%m$Z)HMD[
%)Y]Z[Ui(b)]5";<+@iI#7>Y_A^nQFTheUhSQeS^H$m>1,#^9b*Gq*n"E^f+d%,GEA'R8;dfssOJS*21`,Su*PY/VI)alBt6QBa<L
%aQ3l1_ir52IqtNF$]O.F(^)$f/8@?p3p-N=d[(c8FCd9p+_p`,%:[Mg2i>)jS-2fg#s\P+W@H%WG2B;33rfY5/[ru+X%n"l.de$m
%l%EFEgRQp,DZLk<In#U;aj)M%?kgR0M:V]D^ADf_QMeDTTKrA/dZsm%ccidA(OAcc@p^%tA"25f2>V@sAKTm+;;d1%C#VCd(c]l1
%K>f\3:V!@h:UO]DM<X=M4fT<fM=`&h_26LG=aj2:+/]K;Vjj[e-kD0RKa[sZ3O:rhD,GMo^lWZ'8ebOJZ,4hWW@s=Pr\]m.;b7H%
%s$Aut./!&/Lb]5!k"i`hVdm5B`'@ZRTIW,XDD6>X_^oN2Duo;l$5=s19iK"d/s<KjDH[7AXE0'K`Mng@YO+.UPbkY/)9+[t56LRC
%^W'J7dH7,pE(.1U<kdQ8pV!apAFSMo&]j*64Mc0,I256l5UK&aRdq^KH8_Pu+-j]Z)J*&PN5Uo:6F%Ude"R"WppgCrmj;CeK'YI!
%k7.(cm%17+BiT/=M$7@eAc\FO>-W8QS>k&b'+!QDo`.Ke.,r&q_XnF4&f`aA7Q1D[7M3AN^,2/bPFI%aD@:$5/t-g(414sjXnrT_
%D4f'dj?MI)NUftBZL$Ou3D[n4\,qGR)c"F0Js70a>Xh'=BQAY+S9i:VLpP?#m\H2qK95IGU2=!g%UI61nT8#=ON(-9XRus86N-[/
%f[lfIW\jY%`>:L$-'7W#CQUk:Ul@@Yr?4[f7S#Wm@rc<b]M(aTN'e^NHMrm;E2FNHW5u,5Js\gq=AIWK[h_/O.C'-'d&]ORO)'p"
%,k>0jG>$j`%?WH0Hg0rM<[__fo9Z%*)2Pqhd2\ML'RLCo?XN)+bdGOhc"lmp>TsdSXp'Ur9o37`iSWaI4<\@dYdLI$*-oc&^K#uq
%G8r_51bV9UQ?;?o/U^-ZT<ff[SgalloA9,LE7*o<LL)`Y%[G[H-jk^`Tn[Y35XkIZH4n=T'iW%ha#J:p#^OD&"[3MnV8:^A27P;;
%<\C8RnCkDHLN#_l<ER<U-,7t3ThGa[(h(YPQJJES2fSbN4'SRk=bqKoAG7RAAFCJp9Yta^a&Uq+PYkNC]>l[>o\ZR[o\]n8VdE1Y
%p!bs19JDi&4sUrp9Qi`5RZ&gh<"8"r%Nr,+)6?ZrMBN7>^gq7EjQ8Wi6;O2dAX6O<mIYqE`=YWWm"VqQOMKHb?nTi*#Hij%,=d(m
%.BraV<A#>O\D4efE94,<p:B;)PkUJgfrRDc1CF#Gb9-Z:Zm:_;^:Bkp-$dIp^lf$F!F@*NBJZFPT(.OlTg($t:rck&/6M)`gmna[
%KGIdg(GOMjDq^*ET=I,=$N2U*UAkp-B&gcXh7VRtE\-g@qR],.j`]<$^39PJULkmN#.M&h3WgYmAaNDDPt$a6aF<--!M?c%(r0dm
%KGFRM<06WZBhRR,Lba%(dlYF(SV[8GEcJ@[d&!b#^<*-kDQ!10RU#tgB=r)&cB7(lnpSC8Y]2F+%*JVt2T,!H"g%9g3%"Vl*ihZ1
%*FQE)/D18IF`NHQH_8kpgIafl;d83a8O[Qk(:qBNmV%?)WT@5$46d2@c,d\]/RgtWBc1=:c2eVK9&`pBnQA<0WQ98Bi$k:!6igJH
%%<l.^52"<nc]#(ufHrFd^Ogbu@GFLIBrDBG.*h)6j"<k2EhPT-e*s$F(?UJS(r>"44/@9)U\%*%3;)@f%o8Xm^:SRd3HB0ijaL8>
%)uYJ(E]NlB#%#X"]5B(`EJ2Rt$1P6[eHl/VH(l3Sf#KSVgQY#3MWW8AXd72UbQr(Am++GHY\;rg!BPIpJ^j&4rk]uu.=V?-b!#BN
%H2&RW*Ri/fVF&^gFg,)%-QF%BT$(>*`WA=4T9.(alKsRIW^bEBMQC36&IB_AF]n+6=is>jTn"nlAPJ*A>!iE^k^fu9EjLA9hF"RZ
%iXoK7DV1!M]NFe)D+JZ^3'HJ_hWsK.Hj*#^$OQ;JG&,%sN:sdE*X?l-W1%akHrank_="1:m,9_ZERh(7;aLPo-=-i+N7s9mVSQ6=
%"!IBFm-)c,OmogC0p\,3(oL:mpptnN--1)iNs"7Sm2lSX5:15mHADsu">_IafBET!,M+?IKNgn"n3uGUFiT\>:0S/-=uG?8m!DX@
%YE<aKBgH(QKsAKiI9lsuCVnoMMKO@\Md77o&r-)_$"[UJ&`g<37`EnU81Nt<2@#!Fhgqq-p7@8`T;$3Ij<omlU7p6+/VDXnJIkV3
%r9YabNkjS:B:a('n=/QQf.0"4*,bDs@-a+>TT5pP%8/^!O%!cb,o_%OaY@uhq)Qqk8CB[&)K9,@r!OTL;'5\s?U*A;rG$1S7cu(e
%/W;Q7G8A?N0,ajR5I:&*5S7h4P#&8c!_/G-?+I+aXEeF5J"&PA=*N'm0\psBQ,m\TUPs)+d5[<cTY-B"WJ#fcSG^5SEUme2W60Gh
%a1%s<F_[T??/Or+M@AGZO]bNR'Rn5k*ru7Wb$/0B9=>6eL,ng%'V0"BB$Ynp==I6]4/?eoTL=jDADM/l@l*OAIX_*&N%JaC@R(;o
%7<=-9j,FhWM7I"1-,8^H,Zb/X<`rN"KU371B$UGKonQ]QBU:Bh<lf.?Ni68^:nbE9ID#PU4(](8O1H>kcsg8kTPcn.R7V6C2]udl
%\.pF0V)LRFH.jAEXDC!jjX$9jUqBmdL(H;UYi7molPqDP;AI"9&8L)"iq5\I2!hX&k9du57Y=a'=[R43`[_t2l@\YCGjWG1aBfUo
%[Vh[bNSnGGbcA^e55tM!]`.Ah4ARSk<ZD*"d-\&nCl<0b1(l!faHem\Q[ht]b#rFUa<KY=bNp1X<EmhRbVELXJ`pjWi6-.VQEEbH
%6L0=GTFB<0*;3C^P>hR7ERq#+F\"tb"gsab"P[o,nC2N8DZ?*bceP`5:hA\A1)4pqTG(<*P*b$!c5"%!#c_DlR1t3WV7dodZ[P$F
%#fjEp[>6>C@D@Ik+:6Ul.CNd%6[t$k0V0e0gYnUl?iVhlO#u/9VFGQBIT0BF?Rj<N)=+s[odgse@WO)%Kj2lbT::eVk_tgJ^h"ap
%jq1:kXh.X`WWcj!)>YY#rQa46RC`5cJ<bDlN+](QG;G."WL'SWn0b5/B@gMm/u`6:2e#$",MQ:Ya[l1jg*s#FFiEaW,2^QRJi2'S
%WFY!#%Z8\-YMN>\J!&_oGp@hWfs6OIeX&^TM;B44s5XWVDqra2nJ:2J\"O*;+j-]=I^-\O#bie8Cj"PN4gU%.>o7lHb[Z?@(Eb3A
%\W9:'7TXu8p+(T.IQMq9WoscSZ%@S<n<6LQlBFaW>L>"]h=#L(Rp5uVP8nI"paU(7pG9%h_8`&32#+W_=@@e5?tu'`.F/dR[b9?Y
%?5+DZ2gYt$T5P2Y>`U#PXs0,0TgR$F(Teqcl-_,:W]8brkaW[dOA\u(lBg\SPWaVDW.VJ<Y2>6SQ,_?:U*UVt'LhI>KEsZ%@-lYk
%l^OK'+=4U^].1;pR4X#+7J1%mbG@mg/u*aeoAHr?=G%+KR?m=Df'r0a;rW++&E+X)&(f&)A\/PG/om@;Wg"<i_).bMUP0_447<V>
%baq98prgctWl`Uin6EKUZ_VZ;jYiV:)%4N?,Fa.0QE96uD4JGh#.rT&BjQqk8$dmR4[,OI85SsQ6&"KO^ST^QHg%-_i'KS#<#0\3
%OYg?!4ooP[J?gldXLd(ehrT]lX$2IZ#$C,Dql>6ZAjU&R1"`X@iN=_CO=l/TlQF)9=-iI!UprqY!nVJ@[o(Mn8pCKsnP1V]*r]D:
%IJ"+EE:cMi@-3*(jHs2G-;VF_V/3)]g*pL@/R?GoDi#bWado,"ihFj2i&UO1l?[L3Wj$TEkbDdP\r_`*=25]PX3<^ilK<U-X".'V
%X$k?@BGngt&iVE-^?;2!Fe`3&XjE%4LgbVo6:7ldT.oh.LcS$`YlrJ<GrcJ@H0i$"]S[!>G[>R8C)#pB\a>_S_Y8.YCbc.PKQ6G/
%Vh%TUa3Y68\!mM-\qWOCfeMK6^GUJQ$s7CQBa=Pb^9+A+ZVnt7%n\dg`qA>M*ECBTTN3?>L0KirO$<Y5c^&K*el_PVCZTOFRd%@l
%V`LeFdeIp(a<mS'ZNSGlc_XBe2f?FjZPcX81n2Slc5s,[>J=^Q4c0hq%\"h\p^Xs;p_&6aB77$:k"jZ0Ti</r<Q!.gUbs%4r<QW:
%c<HuYnKETTdVVD5'k's@o*D&eX18861-YkY1K0IPYAdnf*mcW(Hb+H[;nj_NfqhZ3#J>m6!,_+KQ9X+^(en1jduA[jigX%-GJm>2
%<mBl="HMOmq1;+YRZec/6@FKs,8=SE>-`cf3-09O%$F<Mbm<)(_DGGeRYmlE.NMjDFmoJt^/OZJZ>H'%Z\jcVZsn-(CugpYY9!S&
%?@*;5/H+XE-kaU`;+2)J4S#LBi$"CoFS/;l*_'j40G\W]&B7ktBtROE(h$9\LG=MqA;if63t2*WR?(\RknFi42K'f.Lk5.BnsGO\
%Dr?='BR<9*?D6k8]L'O$p)s;*V:J;Df>06SOTpp&dX!$%DOm3"7c*X%O`d,,#e:-k*('HGD-o4,!'B*:aQ!R<`3pS`=4<dTC4RA`
%:,<YrqA/Wik]Zb?_5Z0o(n<'c%ets,YQ74;n:WLhA/&8*J,c"rp:C[`rrn1hnT`rCaEHXn#iIdT\b[Vr4QL2ddQ>D9A1Rru`'%Y8
%ojr6d*p^-t4db]V/nl^!bC[KcQ)WGM14$0)JXeDa0Z"oRorZ9,@m5La=d8nQbCnqKqU4p$&JB$cg-^B!^PKC^QN_kDPgOidg"k2n
%9]eR(B8Rc^E"-J2P?b.F>%\b'+9mudZHTmsX3b54'fBTgfHUQc[V]4ul-NMcRDf=f7lWH3qV`CXAZ\<+]"n@b==9RD)eos1d(gjt
%@jGB5'?0\iZUGl=+&D-cTglAoq#C_WboT0,I20.bOKi75V^"3B'qW#YCq[*Ca?iHh%m+pISZsJ"')rrjLO%FVC;HfZH/+87,8XUp
%U*dAQ(f:QSl9!J<Vhi%J0qq1M`*tigQ#J4uMU>$W$t;=hB8u5`kiX&9A-]SlrSZ@=_Lm@h>U4fT)R!?`E<u7RiZ6e5A'"JL+NQYi
%cuV&uMfO7US*Xm7=-`R9d#3"K'IintaALrg4MV\OOS&R1g<qVV)6;`DSY;#WmSZ-:M++reLJr-],[agK2b8o?iN1'D8M,Xa=rSRP
%+RM<?Fc$#u?_N.#(NHPQN/t#XPu&+LkNA*k$gbP6HZQ7WmLfN[I7Lc<riAO[Dg[:l>`]]AY(>mQa-fscRmf;d+H,!Z=;m[,492+\
%+"XPFe4m:aO@jW#)iD?T^EAr[R4AD3N$I.`<8hHefTSeMnIef]ekX7ZcJ7de6>nH0.P/-)QlRm'Wi^o]B[CAkZ/c5q/,(cu:l9q#
%A^qr//ISRmPj2iDe`Jo;G.mpgRHFiM&;lAd>:N32DGX%:eVme:H_HO*6AfdkOB6>8THRGg+\@?^L5ct+m\S7V/B3T^A4#_mUYNld
%@3[2eb')=F1a%oLKTntLBo+^`H;sp!kYWeg*+`b3"!1#[ZbDhPY`"l@>qBRd+mnJ5UOj<plS8eMZ]F5=A<^%RjM.u=V.Vm<I(!^)
%r)d]D=9#W,(;UGZNF(!b$Mu0@',A]F+UcaVEW0(q5VQ*'J]!Y'g*99LJE[ZAmE4=LU_cK@+Yp@"OY?5Z!+7RH+ZkunZB5l=IN.lk
%_FUM2I)h[J9GD;WbMW8Q3!&2Pkk@.@(!IZpN&dHgd)"M<Gg3cp/P4UJnYV!aTjB9!2,NjSLc;J$>5tp#4iRR<PUs?r4b[(T9e^ml
%A8_4U7,cL"J8FLT]Op?&,X@p8r$#a]XcrK/a.c;(7Up1,[a7Sp,TYQ=cU9=]XiXh2<!69`D=!8q*!7u9HdMJdQ4tACk%@.2'-W;/
%8>V&9<Op'n&FGfSM-/13:j-Udg(^TZTo7Ys7o=Eh7"8o-`;li\6UA]pcc>"ZU_!u7?0Z/``&di4[WG'brVgL!'.oc=08A+H2s!Wm
%-(MuK#:]NW`F7o?A.04E$B/ZkC#"UVOt/S$O0Ae?ijr:Q`8r.#+R$gPq]&\/#&OeCo,EKO*VLO&J1A7Hc(7tl.(>&BMFP%sjts#:
%A&Cmj2eN"kQWC8%=/Km$aflfScq?j6i8^#-YWo:,TjukG(op)<A21s,(U?`WS)ZTt.#u7H-%o+:RH2+.&To>.?fsh7E7fnL2__CZ
%M#d.7o=_loaUY5Jp=!@*6M6C,d$\>Q!pt)0>m"!cMlA]:JhH)rNkpmR$*5'Eg)jdQ/HJV(-\VR+1daJ='Mm,;07lm(9*82hRZp?&
%C'E0WBON:l;EL.,6Qhs5g43n!+kOG]gk2$h_MdkpIRZIrEmha!bnVd"@b%;Z4jn8<5%)m'2NlEX)HngYX!cPQk^,j0Z"O(V\5dH%
%,/'$Xr8CGe^GQ'Q$PXRCPM5-eT1C"YP-?bt>12fITtRKC5OcN`31-Hd%K66gV\MP*e:bL.O;Nu)!?OBc$_'9\I`]2dYmlZt4MUCQ
%JSe4ENpR,WF+_ljZ/\^.RNp<^go_u8"oUFg,@H,L8"f"sF4>:P;<sOB)fa[OO@bRk9r)K.8)_R8*@Cl`kpsYHf(,H^Y-nrs"m?*R
%5o"`KSt:MEDO6qr=314qA+7[O''>`>\.oVgXnmAV3I^o0,T=o>k4&P`_3.#o!/m2R.uh<d,5h*%X$celO36[Ya(/-\"9#R)N(E.1
%c_<05a=@5-s'ktnr94,in$.??0,@<Drs5ass1+6*X+g2*KI9l-[M"m6F+''MANZ#n9!m3HA0Dkjkj&4OB=1NK2+uF8i27+dQcCL]
%(*:H_d2bIb65lnNH4\TLK*%*OY^MGnfV70]a.#,menY;jr9B<V/g-h,/LH)ApS]ji(;M&o+Z=m^Q&S1k_]]=`XQkZp06@rFKC`>L
%qT=K%Y2tu#F\FY1!h@Dh[84*NlN*sRreCpKPlh03c5hGcl=)M@PEJ08GEa-pfA>G2%]A$l.,c%:p?@n%!"U4?S8pZO&lom\145kq
%X:\Tc1STqsWBZ0q\%,k4ebU8:5W&3U]9QeAbt[6"nS/g?!Ea?#*!$5T80ldJ:`H7Ej(IT)OQl%Z!Brl#BBVt4_VOWC^@3pOe@sM`
%)-)@S'YtaX%RYr!be6%A)d7"jIZB++<u"8`e\^'"OI3?=66]?*K7:eGrZ49_,8T>K\ej\S&5c"N5Y,OG%58Qg5&pGff2l%%$Cdla
%&Qq4FUqf\dnj23H:H%dQUVJ.m5,Z$OWLXf"4V3V,fQ.Fu$<`Om8jjC/fhZTK=P4,VX%"BlkN^ek0:1"IC>6'!"/ZEW;l,@uY[GX6
%fLajKiV`]LCV'b:rbN(kf3NA[WJ?Ff<h<Hg)?'M"p+PTNrS,+bEBpK+EPu7IBN-SgKd9/:8>m*"+W&.FU/N5RjH')E>g(o?5D,MK
%hd=n!+Ne'CVc=nrb,l-V$ea&1:\mJL1KIJ)S#\I5D617hS2%rcjg'&PQOH3L.ba#OdG#ZbNPQ]h/-ZSi?6&0*j.3jV[\NN8^$)OB
%]6@mU+U?*B(EEM;O0(>.nTOqSj5gcn9!(MUcZ#9_e1XBB9hoif%er_'#6]W2L#!bt=Oi[^;C0dmedDi!-99!;,bRV,po,bG7e[F9
%`oPtrjRLO<qBRF5lsl?OLfNKUDeG<F5&d'fDY5k[l\gNMT-4U<A]?NnL6&b<])8q",5do)!0N5V?=lM)Q>Q$M!"Sbm6\(;>J5%U\
%fn\*'#]:Ck$GanFZ,n5+=0Zj;M:Qm(au*C1!%mWX>Uh!qn[.*p=IG?6_&Q*V"`qm[>f&/4*SSnH5@Tn[i=q*ZV.n_"_Bs^ne!GmM
%99aiV:CO"TI[EZN8McV_JU5ee'Io>Z[*j^UlLbWTHRiB":+W`g*a0=B=1U]qbLHbd_UlL--m@0(Z'.KS"kN+tcCib/`=9ij'ELqt
%MmgU>VeerRil-IiUY!8o9i/_g"QV'e=;/_W5>K+4[32GQb(".!Oegs'.@!D3IpV*sYkM1MESu0$&sCTa*"iW)NID+"beD(&U;5qR
%]4(e2J9;["]Q+[Q7B8LFj7`J!oaB.cInJEpZ3c-Y]<hDs^RW+1Ddk-q"//ZjMsMt^CJs)Jd3!9+#lV(qG-/e<@qW/b]d1EC)NTsZ
%I?cuk"Wb-KWIl+]KF6YoX#Q)G#!'8J$<dg\otkbP8sr);km;I;2`,C\Y#d`$O7(SRia1"gfZf.jW&%"cD+4$'<7hps0U+^%,i53Y
%.nUGsk#ns'MoL\aMcQX-Lk?I9jS1S4dUW^":mc0emQ_\U(WdijQ_Z^)Q0p^T2YH$KE0TC$5*tA3J7I!n1pBqM]42*q9OiK^@*h;M
%e[c[/9;kl^g.c%$2S"F_RU,1eiFLmSfu/NYEeIMUl<]a9`Ah`^4@5^14/hZ\RMH!9K=-YBX(G^]Y1b9n"Z0B-o/XYt4*t">ZqJ%&
%#(]W/qnj=`i1"F^6q-tZV5'1Fq56XH0><c4H@UmDP%Gkq=FBVI'`o%gM;mWSmLja,=as-Z]iJmSn[91[oZeb;`Gi9-4Xie[A^aj7
%-Zrp<NLB-ZVIpn7;7tI1/m,41HCKn*B]0Q6EC6BB^tXqX@Df`!_4M@bTCNlA6n5_^KO[@5S=qpZHZ]\TVGh"r(kjLp@4^!(,0&Wr
%QFoaN)3c)<SYlq$(3)*0;7uJ(3QM&r7-NpnP?PBh_58@+35_\BqsJ2>G`kEAR;*+c-g\[^MMg1`%2:P]@Ag7c_V!jdm6KXJX>Y)_
%JlG8jURC*d-od!e.]@<]1H327Dk+f('t!EK[gTPdWlPm0S7gY1Z^[9^gkf*c<>FHH,FTCH8rN\;)K7ggd+f;]V,IE'Q4PAK'a9GL
%e>'DN5S1jLF;VK!N`8?C7eBYG[B+C$fZf_".(4tP!"3\[[L@O+(L]oS<HR,Wn64E)+^>%5\AQiEo:T1KQ4*q3BLdMQ/_[p3pd85V
%6eZ8N@jJ&:6C.Hk93oBj"EORdd+0i/BhGC[o-T8<NAGr=<dh[>n-6NJ_:,d)6,6U[bmAM<oi+N#`G\kDkkZ2n5gi-UgSqIF"?WNd
%'%'oZ@Wn]"ZXWuk[^GTG)Zf*oR*C4P2cT>eG0V\]/lUVJ]5:UB7TMHCbqB"RJh:s`].t_>Lml7,oU$X_YP?Db_:$S#Ff5#j!KO'/
%j)]/_D%s\oH3YkqIaE#3k5)[)2Sla==K1XQTeA]lk&MlF&S<^a?pbVc!C8u-9jqS5P$/lLPTpc^,M\FaJCu5.NlVS+F4bolXNAIq
%g%s?u3&8s+-*)PS.[ST8pucd?M6.@$)#fb=Bc&pap2B<iIqQ0pri]8[!!r$X6H/0#r1)ZSTua:C/9)*E6l-P!2VG#2i.cQ"lR#WV
%P`[Y<Si!>AW)G/34I2@6TKOP369^8rWc7/-gm]<>8Aq-gVu$tG>8.SrTgV+8jGbis#8^._38J-?F%-gA,nJ0W)4%lr+D37$-Q4*q
%JdL0YW!toZlNV874Kq_@HfH!4alk'UE;t'A0S3^6R$q)L!mL!TTV*!tjI:&f(J0r:YlQu)PfP"%:EpK"Yd=:'I2#Ut'k(=c7Zn('
%"?,m=nIr<gY;(QMSH51joPQ1ijn[:sG67e!:/S@E3(G7G@!=gT\A.J$DhnAlnGP\A:F^8oDA8KrfEmP'*Dk1Vj4c7Ui>_5(O0rnT
%XrAJ<du/.6\3AV@B0a<,ppO4,I\odf)LMPhL1D:7j8H:4Z[7\+kF/XW6F@)d()T>01fAB=]l$`AEA^?gT'M[N]NidK[`7tn\HHf*
%fBI24N9$#arqAi9Ld]9rm'70Shas*8%=+#ZmE/qWf%#Lu_T]X)#<>CLEH1WSc#*G!dh'@j7);/7[egE,edGudD<<2!O&(ha'r.u`
%mWnlEpYS#E^@T9?,C?#n_3Veu:X00RLHlc8K,j4>%Mt8TGbJ@meUo/U1YN'u>U,Xnk9#Z<KcTXlh8,ji_=UhU)qsZl@pr(TAS\]*
%]'OMW@75hsIdD_e',KH>*&dq.e@`ae1BK?+(p>>gV3e4%X`hN4.^edr,uT_A8_@N:%PrF=gO_?d%<PdV%7f*^eg8Y=h6;sNL01mo
%:%_>MQ)!;.e"1CZOE5b(o,r&8MoNs+?hPH3:tgs6?9:uQb(KbWfL+Jb*47P"rCT:T<M9H=\bs?tEKsu6*41W^Wi*l7h4iK`@UQ`5
%UWt;KgS*j+CHTITr-IHU:YQ&R3nja6ktsf*?[-Mh/UH8\j5%rN"hRcLolY'rnB=A!#&&BZg)'khi7msO,aj$/!(.1R<e^i<=P$KZ
%&'f>l-3I=D)lfO%2O#5j59u_E[78A*JXJ1WGX8SXh-.Q*XYp7"[hi4=ZlelVd-/-p=b^@(RJ%>eB.B['q=9(-:)W[8?u.E<eSX.V
%A;Tlk*(k0aW@QtGBRQ.,(#EBmcl91J%=t%eNNR6a<%FHTeZc+8\+-._j8lhQ>rXfKcI$C^MTpX.6YD8%"Vo4$Q@DSb!Ki-I$#Lnk
%E1;@!O8Ce(O9chdXhD-MnRFG"UU4K5m^KZRi%C$j!SZe(d9P_0/,m4tB0%0W,YDS6+92>l#NtiQjT0RbUq&/[!!:f05/A7qh!jOJ
%fDC^i`%$ua2oqOaOs,c7o^5M;*\]J+FGWQ&`#?\%<:#35dc%E:5t06C($Z-rKo#UQ=Z3n*BJt1!U7(t!E,n2G_TbJlrXA3dEQC:;
%7Gf,rajp%D-$KS`Zk@Cn+Q?0M$#Nj,bGi1(i_TV,lE7#>@1tMWeL<%-r>/&L,Ep9u2/.G9"V2oB.CMRq7oqQYW"2WQk*Hr6)D)LY
%k^Io=!N^@%$jN\,?lM6hHcRtc-Qm-<N&?ciYW&HG@;eX@>HuhQL:'-*N5[^;#<Y9"eq#L##7-L296t8bg2`"#r>=;rcTRE^F+PEM
%.C]5/l/]"%m)_H,j6:91@0_$&DL.F)bR6Ap2"j3f06sNGLrP/Q7pdH[S"Em(Q(hI&p0&YMelI"0rE+J9,#=d:Z:^mQGF_s>nZe-9
%@oI2XJ90sYPoJ==6ac.o7ZS+;)X?iR[%s/\[HRH"lMu+/pCtFdpVWE-&g$EXVX,2jhCte;NOAW,'jDDhD27b1?R:)10Jq9=[-PBp
%'I%\8nLX6aDk&'0\W^8!U+6(7dgXki)*q5*\(@^=jqRsSf/t6il^0sYBSuklIAkIl?neHQ$'\JTAL9s>-S+nbR#if=ip/EA0K$Bu
%6F]0Z[FDWNB7j<-Tf))M*'bbbkL#cHPLm2d'0LLBm\<,L`YSKZ'Y(nd'It\t^mX,Jq%;Q*>UA;Ug4oC8#VQ"g0gMu2Y$Kq7DsA0;
%djkA-r=MV&h]Ap^N5Bn8a6(nPKUlZi@f.#=WVBd8d"J/DF`m[8nTp:Z[!t8l-3Em(1[WmrnPW_U#Xe7$gdsFC[e1&-VJ"[!H.T&k
%EV1I1)_"quY<n.Do8?l?dedAD)JK0u_l"T,KG"'c.9:tA'H%9Kh=54oG08>'P!UVbDVrh8.:O]/<YVd#,a*s:7+),])8c4&ZgjTi
%9_)6/;8;XT9j7b9Yf[`cSY1Am%-IUXAF/L(q8::t3$HJA`6:<2J21dI`b%=O2%FO17JZ8N@"_aIdB+Wq"ir.AhYi'.C,c0_#T#!X
%1/7Tlq0_O$2P3UFF/EG6!XeH"/HMZTh`\hs(Fb;!Jgalp42Td1mM%AWPMAs3HZ)1`N+6C3oqLu7?/3pqCr%1uq]Id4p/n.S=k>qh
%;o9pj2pS\XI])$"Js=7u&-.Jl0GWX(4mMK2_Z-S-Jb-"(LAre9r;68\hgk/.Wab7#_GCBmKnrG<O_[lZj,::Uj9W2\a(J.mrT#+K
%E*[.eGp9$h=j\ifZUU%f-rgDP!?2pJ+llB9o-LhcP#r0K7m6F$rGQp7GSq8?!'MdIK4q9[#aRs4DPgG>h?\9W.3MMbEfH`b1qPjE
%S6oI!SX`o48S7l2@4)kc,\.=qhq(\$(eoF#S7/1pf&Z!>ph'=NfSK<Vl:U3rI>hC0<JX=/)q:2a9k!l(ZXA5ETX8(-80oMM8tpc"
%)Hs`0kX\*jqeJ:r59O0!S_h<f5B0G^YmJ`E*Q%(BaDp,=K"6Sbbn*:Ql;b097(=dXYl1/SHj)\ORr8!6rO/B_'PRY<HU=.hMD[X@
%G9o$GAtgm3Q^]ch\kmY!nB)?s(NV#1Z<Ofk,G_gL^,ij34i^ZHN6(ilT<.DO.-[Z%T!E6LKgu7SmM'`ul5&?s![]XS(NpCPM$r0O
%/uZ(4JVFk,dNurrEnj$IRcGB>+%T'JBZ8%n!b2OBM2$nSM2P.`f7ADB^51SiUkA#9'NRWSiPdZ'(#(XB&q^,X)?VeLn]?nlb%/T!
%h*@Z#P[*Ta[*HO,O.Pf&*Wd,T./+H9P$c)?V+SS6=BOHuBgh]FmpO+XIUp#$UY4pMBju&M`&N\c>uks#Mi>-78WRDQ3J(fhW,-mV
%nNjBrP"#qDDr>?=+t$9irY4fd(J59>(BbVKpruNiW_c>5.0/T[^1</S[M76*A6BYP6+;n,ZKP]n:'#ML9%Ke\#+(pg,:Ha!j+aRX
%$nO]0HV?%UFe5Rrs5^.PHT*`*iDDgpS_':j(97.7TY[>/eF&JFPL7cAN$q,0eb<#ZfNm"j16QL;7L:KbG]1+C@$/PCe,`'3;"L%S
%!+/d>o!$p*0VoGB2i2]dVrYGU2b$moJmRR]Cn*3]&Y<A$3tS6HB6[mEi-O:FX!#IE&=!bnG?ts[UD8]1'1>c`]nUR@O1&nQ\sA2K
%*_CdN[3\[&nm#=ce\UOWLs7-=5jY.7Y&0FZF/]NRKiK/[`r3oq/YD/\=uZeWAL*gC;C=/mfJB,Xfje<70I1G=/Z>S%%=;D59jDa\
%H?U=Fbj?%@J@V,q1UA<Wb;*.JKsUe4Hgf[!7s/C$hTm5$s02BjkSnK8"m__ImI6*#!b4uX9d&eGT;KM'PR=@d*f?s12Z@ZFqoLk<
%SEl<J,m6b@Eap%eHgKN<f6bb"+(M%VJqn.KCI!&n6VOl)`e_1Nj@gslSYm*'(eeA(.[`&4=QAl`>a#<3^.%Q`$DfBl2mZ\oa`f$R
%-ar#WcffSeOH4pqI5h6@O"i;i=S$",eDqfl^`"YH$lZ83/>Q8SNLhR;72DYjhh#O`b$:,I;p!fJ^).M$`>AUl8%oRD]3CZS%6+,U
%kiU)_lF^HLR8PF;c1?N\0dL$JE>t(RUf`I\U;WpD$60Fb:/&Pt0+MZh8EDTQp#A;VVA=\-O#(crKi;J:,u#(@AMonKkZZr,K"LUB
%'XMS3In*@"[6bmsd'7<a2HX9%o`M0'3NAs.*&"hmf?IptEsi]a$HAeXBgAA@C'@`1)(5?H(-5*t7X0n\V.hGl72Pe?;*CLS'9EQO
%35VrBkshOYAB$2jjeV>X?_p_cV?jmq9aislXfs:"1g=3')59Cj.P:e)%L&1EEJZCWT56D2ppj0Y&7B#^CqV9?,AE+=WW4F<'MgN_
%<_H2A.67'[RSqBn!u!H'GHs[rP7!fe)5e);?*#cj@FJ19K_/B_b_6g%Bq*`[TX_=M9^3oK@F%lCc!i-!)^l=3OqS'1\HnuDH:"A8
%%-F_+BSI0P_3ku1@cV-'l7en5Zp[;i4&>\u/B/3:^Aq\*EDJJ@Ylc\FYj;`RQb%^9V6EeYiCF;NVH-h'Oh==b/2L^E<Z%,ZF.mDE
%XT6&,.dem>U5"p7%(-\*e]UOSiTX9!^$i>lMJY#K^O-pRq"HB;=9]efcOlEO#8--cL1fZ2NbW^KLHp?KNLVkU0:<CfHOtc8:Mi\Z
%a9]"0f9?8A6Fjc"nH@+0%6c\h^jr6b#R(5Q:un;CcF=QG<H"t->Ca+f@$j!_&ua[mSte"ZBGtgj/X9ONjq(p!pNrt.!iL9YQGkTU
%;H"nulNb59;G_^["pkJqj7D:W'VL2jJh#++9=ml:@[kbSSVgb1PPLZ?Ns9p-NJ:W3hQF[.k]CbnAV=]nAqDj7nB#?LYV`pZO,Oe\
%#CA;>A5e4s/GA]!obMKpI$8"bb2m.hFW`;_)I]-nTd]-#Z'U?j@k(Iq<JlOX(iLjaH:/*/M(hkWG0*:l&I`d=;5nPAoLU_u2OQ[0
%P]P8h0N#A>=Ks;Y3cLGf!@%<PQ;F*+6p'+6J8p>[APPl7Q/S`#is+-6SuKY?fHuKPFk!p4!L:E,5]Tc^[T>!h-1E`/Su;F(X:M35
%HE2Y9P.)&R/9^M$Kg%/+Ehl3n?<FKj_^J,f^EKR%Ji\hY"t7`"27;l1FqZ3>S6'a`H5?\IlGAf:%s[Wt^The\B[1ou;5LV#,]<dL
%]Bisj<\aJ.NCVLR:]tjB@']#R8XS8s;s*OamX6?q6/@SJ6_7tV0+B:W>mM->mm6('b,ip8buTpg(nDM"%]ao)'mr92i2KMca)k9%
%1L.`:eKCh:QCu/N5$$58Z(I0&,6[+7,$;q$&1TPuJ35;,SDMetXF_\a/oY*D0[!25P6A0u"eE=WL70^pGdJ*Xg8Cdiie9miNdhPi
%3iD)*(p1W2Um+3',;Gc#XFT5AIGZ$XFTg0/!aMgNd8(aR'"S3!)J1><'n*NGWI=n.Bp'?29-^f7^np1,aci/1"@o31H`A@bqCj>&
%Fc%S':ZRMJQ9ZZInA7:5!d_)QKGr93YZMp9aT?u?CE20G#VJ"YQ)J+U94.FW:]e+G5m6dOaQ`a84,!0jJ1ncu'.X"iWM=['=Xe\J
%+h=UX&i3C(MJ(J)9UZ#&+<l%>/GcNGnT@u*3-[Q%S]g=i=<+qp39&JJGY?GJVUD;m@W#&Z@1FOG)5&LK<b:S0qhglNfX^e&mP7:#
%a[NmEhUB,7<e.Jm#Y'tZGu_#e,N)l73'ep<;/nA`8<oJ2Ahpb/)Q1]BVV,(<<tJ>*[;(u[e,*=TP-)RIIMfRss)oVDe"?_HEg#(m
%$`3@"roMDO1o7,sFJNg4'!S(Uaf<,jJs[gP>=H,i&Y_%O8kYDVijf^%"O/n1X(,#R;qd@j9""d`=Z+Y)$2h@VKtaXtIki?D2F^d7
%g/]%m#$am%&ob02P<gI"O$3hAY\?sidKd/[0Qg*aN$q;B?SoU(J7S.8`(X"+A\:FX:=,X!_.$Q)M(--#J=.,]a@phn^F,9ui*aC'
%aH\p7MOWZO=g`\/QbhKOT3U$!aKXH'ZY2$`"r0e;oo'g\,X?#mg9la:Ws'oF4L/S/g9pu:pa%]mJbBP".l(mMm93h0JZ!&)Je:[B
%^#SKLpN8\e+Gqs7)4Dfah8E>N3Q8\o-&;!p)?Gt7-F30b^.(+PT$6kb.iDgSLF@CD>Huhq8P91FW+))MVYgM]^t`&*-6?L/]j4iq
%arHc/ib@(IOPCr[MYX0-g<A8K/3>_7\s?JZ%P20R'&6\+E8ro8;c/)8;"/-&ctlipjPcc/+q9sib18,OZKICj9H"^-441[%@E$nF
%7PhVE(a%-aF7(I9(qB^<Hj0SmBNSY=W?q&"-D6jkB"!Ugp4bk>/7gSRV8Ukm[[R`0q>;$.l\Jpi&B0Q&jO3OcA>$cE(F*%6p'llF
%AM,nBj#earI2a4$VNs+f1*DOKb4NPjPd2%)2bB_CN%T9"@)=%YKp#f!VN'Z,@M94-5U>^s-nu71<kpb`<^I'm!a&kFoF5>P"giM-
%i3UtC88<ke_aCj>C]#P2'O!;_7-mA'Ylfedpk.;4Uj\CW,_>##dpte/:\6hNs*-Mt8iJ@[V_!G2.BQ5::3>Q]['h\k#)]L!S&c>^
%U!0B*!DXdT.;j`;Ql/IT*@b&X[(1]<Y,?.%`"&s!knquIZ.FHP/4Ri2DVl'2E`mBX#*JT<4CpuA/54.mmZ)Ri(TfZ!BaK7eNL?:?
%O(A"0T"/hH,**[.ck[9odM^AjWYVh#r^`DC(67[]SkFI`EOq#$)Q?Hm<M<9uGfHZaAnG%NE,$%?CV7#TOA-;GYuCN_&p5WuhqGV\
%iHXMY1!ggHhtC(P/K=21?5NEkiJ?u6:NC>Fe)IG40-s*lW<!Sim`H.h0JX8[GW`\0kK6gL0)/&,+Iu0j0t;&/BQ?V;@H'ld,fAYT
%SIJ9!`dpN+cj]sO**W_AHBgj/P7]-r7>Y7p6&f?r![qIC\7Ci@'(BF1XO*7Y+T3JiC1@iEG^<og:59i8e+jNXpAtfOP.F<+)S$5\
%)Q^MbZ$n<a>`0B.L0Q7!g`SA1d"R9I[$Xe+i1TK>Y(on?-H:lZR>Y-?_?,WZ="5)W>U<%kQPG[,#fma9G<rp"#H+:=r1m)'!G(A%
%K"5,.Nl=773J&Ck\BJ4bAMIEmpC"kVI4h'(\9A=Z3K:uH5;O&iM(G#s9S.YNn1)5=dVH&P4;JL2&-S>q?(PnTTN?AG_iq&*'KkVN
%aMf_JW#U^QaXY?_r+UBM@6YR*,M6d],)'RompO!g'!m,\]j;$&+Cl'`U247h6T.elC>/bN>n)UUJ.Z*8a@3)S_C/!5]h.GnKDP'=
%9U4QLBe6O2$go7s(<PH0N]]@r$TTKg%IgAsCB2@2A&Bi+cqfKQ0l0rkH:QB;,m>GaSQZ?Pm>61:e4iCQ8;f/fl^3tK(CZ2N_$^"r
%*/c_+MfsiPDH=uDgK2H(bmAqA[MUTjP.mW&F0p]?^+#`3:o)`iX:*;R)c6P\UMEh$02Ru\`?o-u9H=9SEU/50FR4O?X:?p?VN9''
%Tc)d)S>9*N),KE#STd_TEj"&2e=kq#I*EHIaE:BXLSQ,KP=rfUg(q3MQmm:tU)%!ZaA=3Y441^2@LKc!LCa?e->EnV+YMD'cjfkP
%XKgPR-.f+&)['d\2mONfT5Q.o/.?0a?s*!r.`;gg=oh=A5^[8V,9flJ1*^VNfhHl$6%,(^19h>^S^#2P5i003))g$D`<FpY[2YIS
%$;a2#3.XfTO-raN:=CuJIcOo=]_6V#3S%gZ"#HIIAL(o,G<_&UU/fRmGCl"fq\3cOj'6iRB6On]'bbX[FJ<#9/h9]%-A1%V4/(lL
%Z3e]1.Dgd*6?V/uB55R_Y\#0Am)ZH;N$-p$'4WpW?n%0b1NLLe!n;6:pf)Gmn6=Q:;_'iCf*l%4+,GGUd@(g?H/W'V<^:9;"1\IA
%"ooHPi\iA6+R#3c`dkU$WtnCg8!&V6J]&8iHEp5q@7oJr\nEVfDN'=6EL9+e?*6;!8&j_7T5cJe`!L7mZZ.i/;&U)-&4`(saMS-W
%,/8u@XQKR6XhM#30H1%E3k[?K$&q:bE2K;n^f?[+<n^o3b7lc5ePRPATWqc^jJT]sJmn-+)I>7*=UW;?2,c>-[lp6=gf4V('NBQ_
%!JNZ5e4NPP0YoOu*<J.ANZ<EMb@4kU^doK*LNAB]2`P`POu,J9;'!QNZBk@l4W^o[/2*f*&o*JA<\Q8?.]>@H]0O$3EubbsQ+)?\
%)@cecKKWMIMm,kiS8BP7^()($LLH6E_C@<EFK_SBZ_'moq<F&DI7E'bgpfa9.n?j4Q=+\<cE'kE1;>ERC86OQ-`0Vd7-g,o:;W;6
%V*6^!:p@%f=d$dO;XRNS(<9qd0oc!2!7)!BeQbY5A2=?5e8.OXR-"`V?'!pHQ67/=(PXI3e@\bL5tP`#^I6R\MUl,,V&.M5;1m09
%PElW@h*'l#NEQCN7G,4F_J5[]iOm[cM7eja:BZ)(_`(%=oqDQ0n4r6=<%5_+>p4g3akg(.:LGlA7s@&1YPF,_AN)>`*g"N&9<t25
%$HlG)GiE:05gckNUn<Ws=dXmLg1>41ZmmIrI>!,k\(*e[RcMVhn#o6_KT\n:<2P$)`]jWIQYHk@DG:aJ[L+%j-=;&qVTAQ8>jW^9
%m*+01<C$tHC`gad"i&_o5g5/G80C//5nsA-8PB/B7hG9E\\836UR2O?juu=T,++6pC9]r7>ZXSblVN*Z\s$i^`<u_ie(CuC`,,o*
%]"<,1:0Z_:^ICF__(1lh)b/J2$-KkTlTg8E,]W!a,0XBZQ0+p'NpQ6<-aPoJ%pE<pTjMmT,dPZ5>)p-=mNV!<g@?r`VP%5GSEbXf
%-*uL,.9kPi2E8&m#-SSuUoW0&0Z=K&k@`9e'V1;+Qu.j,'7@tdhF\=t6/iN:?JC4m?fE54T8?7XM69f]]>aN5mj;B#:DSpoL!Ldc
%5?I'q$JYki'q>F&#7Z.$nOO57N[<38$VFN6J[.TWoV(5bH_Jl!+o1qMAT5d!W4Y8+^UR5NTA53-b.A9/W=]OqJ'&5R=]j`Gdu?0m
%C4W,(-o>6;pG%a!16]>.+KQuCV3\;OJu:$CQC2(Y#'pHh"D7m?%:J'Q[1['a>7/Z8d9?U;Ju!c1&?][H,n.GO5b=:0*IUeSjsFs"
%ilM>C+L2)97Q!gWH8g.u)8[t@minj$pd?Wi$lW,!N,DZIa%:Ut8[[m"VqM[.QKZO9s!h3g)o'V%Uf]6j<rbCld-JiF[SG:0D$'B=
%R(LEeJq8m2e-EY=jRoKq:=ceGS>nCn$!;!t?"r4pB5ab`5(L[Mn[UhgD!k"Y+h-e&2;]gPKV]K*J2ji.X2h-i'OY<aaQf5YeR+N5
%\q,`oF\uJ#R^4#Zj<?n38i\(pe<>J&NI^pH<WJMZC&p@FK$WN-&<LpTbrJAB6#r6dPp2-Yee1Lq"^M!4K16:M,WR;>Z@[>@g=-b"
%q^g=2(BWk+,d^,)'^7IU'M=H+hZN18g'LU7R>L2oZ"d"^q_X&EYSa8&<@RZJn0fDrTiRkM1^U*H,T'1@XN+O(3;Tj/:@G`m;4]b,
%Yrai_?MF79iF/Cl%*4lWBfPR$&tE<MoQa]p0b@8]cmP>*(p`FD.sW?eUN"!hMI>L?cTr'GP3bV<O.2G9!`d>b*QYVgC56BW\_2RD
%(:XU"3[)$d>^r`[%S47/c%;Q4^pt(a+KgBj%/$TbTN':RIB!f^!MOOO-n3@s#+csDT@Pru)I/LDod_VP9+>$m]eUiiei2NNO^;]*
%7DVSol36B.'dU\PFEm=]J.to$O\:>`8H=rO0.`eTN1$Fp'?#I.QB3OS#i:`>,-Zr4YQ>0LY;)<jgj3Bb`Emb\A!u.:R>*u8Vb=pA
%]u%I4U!%EG<;oc_&/8:VnL[n?QqXj'8ARf4J^Ic='IM#rWdG]tq`J$445fVY&U[GF*[./">/3H0+;Osh"UG10pSHdhU;GoKaqAE)
%O_+i++i?nRb>C#KRtJ7R-F#ihZL99>:Ol%NQtC]//jPdNI_Jc>(ZG!U*aI2Q1ON$1(!k0`\WC5*SEZ(qU(H">VP7>u$/NCij+W/7
%$8)NLiH&0q==+uA`T?qWdoO_m<ahL%YPDTL7-puL!hO/s2DJQb@;T($/0UhP,SVq1ZRaPu=kWC0rAO^/eR`!)817k.<rt*bLhE#/
%!I5ghk/`lq(tr+#J@J?Zdh<!XopsHe1Th.ib-RD\:3khR)kO/7jW=P+Mk*Gj!76<rd,J7"H=a=9=(t-.#5S[M,]dsVi4\89>Uo,S
%b,3u/0N%&"/h<,_imU[;4`3YKK]?MD60!TVRZCB+C,V;%L^.$\(-#K@)fB%47nOUF_eVHLL%:7N((ugWds?WRO$U_")4iggiu>fI
%NJh_KMkbfdWG"Q;a#&6sCLUm!Gd!M_eYU2-f=\ht9\"?:Nb4!0jHlI#-'FiS'Edr;"Vg+<Rko]!C'g(NT?Tj"0&Y`@V9`,5F(CbN
%*TS\RgQ7eoM/]V+PELcc9e0h0"NJ5+!L`E[TIZI['=O<-Wu:@ZJnGMn7Wk,W<="s.@l74u\@KroX0t`*4!aWn,?Y('kL)AgR,3>q
%"OBG\f.p?<`F'ilJlC2$AjuQF98,qn-F=l&Z>^JW"___BOQri..R^jA)D,I8g`(LdV-nA?`[(U<L/V"[OSKSFon(_&1J_BPPp^,B
%8GFrmG&gXT^I.!Fk'Ro6PI="`e$hi##mPbn=ef4E6IKIfeq0)^)p)d8V(WO@^<Dm43@3]k9Fe56ZIj2oV@]J!^M#7^_Xo2W6Fd2;
%;&NRn&t1=MZO8e?OY?!lABE\jQ?*Ap#c0AS8]`=<pJpg5&N>1n$Z9iTXQF-6X)s?D1$WMt["Jb2F,)95D#4K1qB96#cS6E-Vq)4u
%Y#F5]XR3FfYXt7[3r2JF[$d,>"!;TmCp]%M7R`1-1j(B/d@s"18T(;A6OAblp3GU<=PC?EbZ@NcVaZ@rVogA1R1nf<.6@_erC%^F
%U1/"H%('iXJI)Qr!JoYVSuIpi_qZU"G"3.Y-s8YuQ](O3Vo+PS^-2t5-)^Re^gTsMk,[9m9QEAaO\dSXo062'[XG3>3>ROcQgL91
%O+^54'=m*.Z($k0O+T9T]CO0*'HntQJH0r?BB+mtG:_(ae6,O$FLq@tL(,Ie`@@@l?737%o2>1WATAu7en0Xki::)go-'I70ZS5&
%&VFPqE>80n'18;])9F$.>bD.g&_BC:ZK`ut,SVj%?-tJoZfi#K-:;fG[^Y*SB-TcuF%/eq3k'dm6I!C2LmhouGc/+@OPY,Rh^d?&
%C@5A?3pPXU\us9:XNELK4?PDb>^=o]>se-=)[/m7H+Tm_'VN<0bIE(gO_elbM-@`0kk#4>j/Rfa_1)]sluS7o\0d-%S5C$r\-22h
%aV?,4J@Ma.38,FhB;)<>-.<3Hd+1G+%:7JaW$@ati/,UP0._O1g(R_4k_Y3Af\Qf(oGdn1Th<28BFZl7RB5"9[NdIL39DVuK!f'o
%?>'?XXf&t',9"t>P;OO,[i\5edD2\4PFNAa<l&f6!>eJ]L#E[i'nYWRP4bZ(SkhWD.XPIIP+XG=Fbh#T%\Uc0]OQb/3#P9k>_:Jm
%M/`ac@"L:'9.6*<3_XY3^;iY.1H("bM>(SU8M4II1al(?We0\2UZ,p,?H`8sOAVFG,gt\JAk$K)d83IIK0-r47+X//$'E9o$5Re,
%9:4Z\,Kg6/kdk$S6MERp^XUkO;)3$Gpd?/4[TCB;,mQbMc=W@mEgH&!jOh>.5dnLS9%?F$6G1_$C#[*.GF_kSW+QDdRP_9l@.5@O
%.E;i$Ooc(a7,S';C-!gu\XMFa=qkk/FsKhG.;I8$PJ4Z8j9Cd*Z>R?Ed_\K5P@g4!^R9o1gUWJk8VRQoGh6'-[WVJ6M+]\RAPu8+
%%>#A/=Q/+noR>F`ApAI`c\5.3.Ip;#[e(-nW'6Ca9b/qC5UMpW=hm'YOhLkkbk+^5Ao8+,#7DqU!A;$=:"2"j=pZK-qQP-P&;PQn
%()P;AeGU9]*E'`>PdmAW!8HBT&]B1Z:--B9<=h*C*N18t_Y"-0B11E'S"1B%$;L[8V"G?T9ra-S=R&n$2K)IP<!tt7VZH`>:`pP9
%>3&nUiRT$L)*TSJT.hR*G$ipp)5C=O<@F5JdadTo4-rB6THKA*W1+8DIGt_\g-"5O,ejcZG0-VB'3_jPnH>DIQR4??fM^3=fQ.V3
%55-O`m0R)jU7)0T'":8qH)`X#+u"dh"Y$Yn67XcU1IJ3+"8B0Q8BQ"Iqls#fP#.DpC>t>UHOWk-+HB1OIeqk1VMAlKP<:QL67Ba:
%9k4l(&j`]@\<FjejQ_U67-np/';L5&B7lg#fL-opM]cKFXP>8=-&5^D%gor,<+3.g`1Y#F$S%2=j1+iDUtM[J*8tu`/F$7bGY++0
%`h^D*$e6RbA\alb/R.[f(0N0EaCut`1Ean'Fg#/gR2.IW^!`_#:@f39!T'@0=$4lsfSf+[)6W5f!_/7]63?/@c+>*09$`VRe'#%Y
%aU'*f`E7^ZP!&i.)GV;+S7[tL$A6Td+K]hhg3G._'Bu&q=tFo%,`%<U+A5>?IZf#C9BR#8j+p[[$[[)(3kln1hgj<-;n[Xa/'.4q
%'ZI=.69^-<KD9qe%&,Z%ap_co\V.5"9&=5t(cj7t8Q_;c::5b:PjON24S1lKZR-9M@OK@9'Gf!A9Xd,i&kU>OdU(ub^P@2WaQSLF
%8$35*s-[<,ThFG2+kB1>G1r(@8hMjTF(VFXY4Fsq#N'0a&edOgQB4[?Vr2A-"#r7P<1uj-npHFiQb>]lknXnigd@Ahpn;/t17oq)
%8`44DH+DHHYk40dj<N1@T:op/,*mnB=+4Rs3Fl.nQ./).jH9L#*>:o"J('SR,RLKM>L[DB%DO_Gf+KWPPI*$<![IMrC#JE1bfVqN
%CBU/;n)Q3clm6[uX3M&=..hr;b@JdGJ0V"<M<&-q!(]5PM,]?nq[7*:"PIIoaX]08N_N+g6ZZu02KC-A)_Yj'V[(K7I/PK^\Fe6@
%-V3:pg59jQB.%O0%MWLA7nTC)a!;<.Ll)P(@n?7p.lFt11K.?6"SZ:CQc(G2G*YffF.S>)?P7j%:7hi!MYE4/7+6dHoTR_)biGSP
%L"Fn?/Dn6BZbdoIekC7/aEX.O$Me;TUQ296Ql"pfl.,`:RP'%dpDREGFEis,K5"<SqZU4e&sJMREWD\`-0"$kN>bXN.kO928Chp3
%&7.&:6>7B(RkkWmo?N-B1eh[8S!!L"n80(0.Lqnd&OLp/A/Phho1`?W=>Q=6[50!k..<H78eiYrjW9"3TL)0/5j#6rLtKS7\WR;1
%>*T%n]hGbH;X$C(R(Y3&*lSc1WgbR"o4!+Ic;HSD!&4`''&auo+AmnSWWH8J%nWuU8V@h35D^Y9EibTA:_kpTUL(>.=pi1T^'OD'
%<ZsmhNK[<Yi'`nr=M\:p#RtoOLa+GHE/Mlm)mon^EmMDjg"YiURPcN3)(e-df0g0tYS*l^&[2m+CB@8@H\lZ:jH:.*(QXC;,e#-q
%#N:nh)B%lXB-g(NkiS&X@7"2A+@>i.Bd/6(A73b]/Ko+]j`Z5(69\'bEV.GECF?uI<'&FTfs&L7VMlSJ,T^:6hDdFM5gNDu(s!?d
%R:'0_]sEQg*^o&T#b!$Z\+EOr_KmPA/o5,tOF]rA+H_jm6pNA[;KSo837k!u/aZ803[$,]feS*p;4KD."'NJepQ+.\Y]uknUW^(5
%9,!i7M`T&g6e?df./kZ"7;dGULmdIRN`=?[KKM1:dE=2^p-puVBONB-V46P,4YLU63'-jkA\?J#%1AS:0P#(kF12mjF2aEmA]sUo
%/94F"\(L[D1HT!;+]Ek4/<!k^>nPdY&&*`&LSR"@4rLUgd'Yf+N2U.k75A!W(I&hYUF3&7?&kM\E%;\S+$FT)O:%2Z60uK_efH)7
%O%Vg@I.W1(8`T=8,lD4A].B0*e-b\!mL!8-;ln'pkhdWJV]6IuW%nhnLMG*4Z9N0+pYnqmfcAQdJs'tNLtRu_dtoM#a[[se89S%$
%S6AU*)._'FF_q:&*O'[&S<9iTSE)eEKn]cF%rsN/Po]&L6n[iST*(;>9-bB5(lL+[(?RUG'/'KUF.ddi"usO\fD,_#'B$BUkfdD$
%\4%h/f'5cKNY5`FpYo>=CaL@X)W'T<R^#C=V^'>P/k-7hA1J!XMWD4QG\\r[LDX:Sd+(/^C'%LM\:A.JH<MtLiYbsX-I`V])%dp/
%E+f>,\cEqc34)Lks2`tH-"kjcX/0?uX9E&>V[aR<fbCf/8ha9sYrdNB%%qY(!/t&n\.Z'&@3l$qZt?`^MjEPJM-EU'O^,r!NH98-
%"i:(FPM*XGO*]rm@(\K/UVVS#J/Cj,Ug])TWBW%7;EQ2jS+!9R,WO'D^^V0aIHX'8/IrI&m+RFGWYF!5&Y,%dE$FnHFEmFp225ck
%Vk=\F*K*Qmn?#ohbml/$QF`n0Nu#$%:<<\fF-#AY!_$Hi"`*P5(ksFB:)dOd?]"9gL(1KBibc<W)1m4Cb0IgS@A:am:&%c_#hfV3
%a"Nb]nrmRO=M:T+6@g\dQJ>$p1gNpH"6ltm+Q7T@DTT[l_$]CK<J)NqXm0Ar)F9-#)QrsscW0B9F.CbsOnl'\.!dk?G5LGo\`h7C
%k*iP'dr@j;![Og8K@S8]*[V0XWkG1ORA'd4g:s4`ot!Af[!E6QDKM0u,)9Q+.$GS3@q915c3@"u,Rj!MC!1ElgEGA-lgePU#]\+L
%b/lhKme4jEBd1e<A,D:H$(W3uRoL(kH!M2B8ogo#T</7Mrf@8G'#<p'H1:`eO\p<oQ=[#Z0C*f#\.16o#,Y9K80k!n9N:MTZSFn>
%2#-<$>rj6:5f+:o@4S9J5_]#OYoQo0\joI;=*InDb_];h'O+4=oI7->->)O0g3BJ,<e@#K6IG_WZZ6aYr.)q)M97`[:GT-X#pH?#
%Mhd_.Gi:&d*?;50&p^cb'u6UG$l$^h!+tm=(\UaB0JAWA8Yp-H'?J(1X7/_2`g;g&,C0-\W`24q$MqEH"]<Bsqo=q)!KLRlEWZWU
%)a2Z%-8jatWSfX$)a]ut$)L7QE]@8F0G-(=Fs[<:a[Rr7>^sOV[P$b1;MH!e681CU>8>_)W7R/2MBh*mNcu?6g'4+Y+TJ'l:9_T.
%JLiiHQYn\s7a$X-Vt5(PRN1*NSWYg"LeQsAk%96i>I<=t:sTrS+@/i\A5[*$Ld_X`K2H[D,f#QMS7t\(\tWsk7DhAd_5ufm^"LM6
%^q>8nDoqB+S/.C%C"_L'%Wt3E#&_DJnp+ZRT7YEA"8&aJWFDijRQ"Hio?+^::9E;r=cW""V5?PG3dG7KFjuZ#"mTh''14il6UH1$
%JB/\+c?YT&H;_1P-1;YPMi12SCUn.*A.?tY,VDgA6ZL87%OjJLCNWGu5p?nBVjL19%IXjq%&_4;8fV;DTL>*HoL(X]-(.=qQ=saK
%b;(#4Z_XpBh@qXn6"_eFfh"Bl`Eu)UB##$J@gjXoS-N>YYc#7(OC3e]0HR1_QL/mf/?Tqk&90D@SYt/k0P^hfRCmH)17"<qWm[r+
%2$Yif!*ph%PPg];#qMMl+8015a@q3IGWDPXf$DRf,f,Y-H#j8*0N!ZO=7q:+l5:h\@;=/N/9;u7dgpgteJQ+qM1jAjM_l)0CEb<B
%i=Q'XFiuMc]bWlNKua_pe:(1#,kTAK;mEfiTn$9-I0C37cS!BVrWHI26RMG'+(fkb/)N$mN);lA$P:-7C\MGK2+sRRW',mfD]9fL
%_<#:5>A@%L.@DMr)0\hGfj27Eq5;HiUo+**n`+o?Ci)%oh(MN%U:K:L/Cd]KYtG0D'ACpE;\!otVTRD$>%O,uQAl=8c:LqR_L"gT
%>#Nn2/1N'/\bleUqh_0p1E(;B_a,hXR)\EL+CrqiI0$'PUut^ek]Cl<(=]Ca#Vnq2l@(PTAI0o0ZqhErc2\+s'A]l0rK/<fs0Mbr
%DGK;jUX9GG?rGkc"Ie#@l&EVA)V:r%2Pc_"oQfT*6C6r$LDc5M$8.6;G-.F*TS3i38I*2P(H)"iB!=4@@FBg8]7'R%78"I,F!9"<
%U4SDc^UW?5h?mnf3^-(I0m_"L5IN18ESG#-AA$_"T)<m>!$@Kmc;hWf_WqSB>VFQBX?Aa^R%[3,R-9#;CBoa3?8Dj[:,u!0nKXOP
%U^#B135+C5BQd]1Y2BH8U2=<'eH-r*e-.Cl]GquFOQ'>\Z:k8XfeHQ0(\)5"&JM4SBUUO1X!i)uREe6^R^a%UrA)m)cB0o9GbUOZ
%b]STK'+>'hV7_DFZ[u>j'inZG:q&rs(UXPL`N)qQn5f,oaaq/SKGQ3_E4#^W_C<=N=\ft%WY;Gk4Cd;Y8V^BB.V.-``B46B4!Q_S
%&$+3GRB5I\ZSH9pNg]94ItP!:Z:-on3fK:4lrnbEW5$Wpi+IX?o*h"Gm2KoKERa)$=JkBi-A")UHbU=m=<-h+DLaLW-7cog7HJ_=
%_Hjp"dp-15S9Y?r.BTfjOCPQZ'Xo;i,RgPf9^5][lTm<H@6J>a5S#mZVdA<QU0`loUKNiWhf;qXMh_*+k/\*2?)bDZlIM'@D28d^
%!eM5\Rt\c9!M0P4(p[[n;<LTgR9Eo^gYLI.lSK9:U5=uF/XF(Nn>MkHM5ca3iG=PC)BEm0,CBDa:crj`dQM:YM%]S9H8gNgDbqO(
%Oe(^G^X2.DrM_rMMb2^Q&J]UA:IKoar8X1^UD1ml,A`>^6\FS%P%41_QYRJ&l9IF+L[9kuCBZ#c`NrlNMcGuR8kT`!V4.@;U`^:3
%:u_kVQ7D]W.@S(KU]qo3?=E$IY@LZh<1fFK'B_`SktR.re.W@1A3eNT`.PsKMA?Z8ocP$1SBjsPWl.\>UD+to1KB3JOUm=$5[J12
%c]#:G$_%ES'utk3&]D1crG6kh\9a>g<(_++%a0"K#N*5A@6Q$9^4="dSkd/CbA7>5*4LSX:DU8&lg6%YNi=md.M<S-*=5L5X[/V+
%.'3LBZ]r@e(D_Om#Gn1#1-t'BJXW74!"H&`k@fqI`[#T&#(au66mnXp")7l%ESpNB9WAs'm-I+,fq;tOMgIbIjK/7HqEQu%X#-fN
%/=g#((nL)6n89EUhFc!4(&MP0=R$@gXk+A$11-3QfBYg<m3#.RE=\ej!l+cND/<g1.4rA?Z=6\6(EL!ML+4#;3ERs8,L'd/Q?ppe
%TXf+.mPG`C_+V?@SsJC"6'qC4_Qgn)^sAu4H2*^PmMR`[Q<?oe0*]bdN#pN>TGB.s/Kn4[q+5HJ$TAMj70>G.-uZZ6.IfM[NI7SX
%(/HUiV''<EPU8>!jQ4:sF0m&+`o[X7lcZ9pW>a8K[:CfoI:9B)^.u(hJuo0fmugi\RpGjSWf.`2*Oe/G5LV\9q#'[X?d:8f5.gXM
%m.B`)\!N^o?bCT*o(g8Xf4WuU\c(RYj-.*eZ^WI/oDXsF7q^s=Ph!ULHg6g$0@+S*DG\GN^O9q$:k&H*H[g<gI<4")"%'&2rarFt
%M8J"?^nl#$_!86I'[hj-FSb33m?bT/YD+g!LDT-OB+Id60%NdEs5s7,^Xa`K]BZ$>T=I2"GP-bT@)2`Kr`9<;Ynh>)p?"$`i>ifO
%5Q2P`nGc\lnV;>c"5+\WlF&nJXaL!&lX0\"@XgS;_afL^peQ*YoF$0Dj!VL<<JMTOa`*R5Jf6$%beuA@;r&5F9GXn'.`p;VKEk:%
%RF=s7SVd)ilr8dm;J*24H9p=r)!E>ke90a8eMfp8^A3eLV3!:rh?oR>7>8:FZpiu`!#H^hjqbNl+0!*ZX'I=n^aIm;BZsJ9(G)PD
%W5,osX9XBS[Tgq[[*`S2]+GQt=7^1dWp7.&1%n073AG5qROpb>OW'`njUcPn.Chlj@Bl>pZT1/pYqG(DEFL??jNL_>B+QarpDFPr
%/NsaW48PSkai.rP?m/@>pdg@ihUeIpSM5F]==Jj59XB')@3+8X+pNA>oUd`T@q9\M@E&VA?m&gG"OJ%4.WP6@+^&Wi!dLpTW".Mk
%S:BU2'9,FT@Oj/:Gql?l-:<p*GgA2O"CmR!0>_Yh7"^4-)FU(B'#@/^af)'?(5k-#/VJt(Nc]pI&3;b5>V.?)-#HM"aU70GQt@71
%j?f6m0.+McaES%^+ujhbNMJ,U0-NDMrt%f15fat*Hrg<ciFis@m3JV5g9LnP[FN#)2]LnK#fnB7O,cjUH?#&_DdjAs6(jeAAf;4C
%0Pa0(pWu(5k91$N07tqJd2LNWPo?eJ)Dh1^c$0kfku?.7/#4(4k_bIdi^M*0%([-mF^D*8,A7-<&M-RtX,2a9BbLmSBGIUl+p]ok
%/nU)^!i'p4nk!IM9h9=9)aGZK2[IN;K%[Zb`EG*dM0,#3NV-WU@mGafJ3l6?d+BNu@hngjCH/Ct+Y]ZBRjXOI:0cJrdJ'AgjVije
%I_a/?=ZU&Ta\>0Z;J6c,/H`!(Q^U+$m^"25'!8URV?Y5t[trBYb!),A'\kA_12@8P>3'/<6`9a+mpZ*&&A$)YfuVDml[;lW#/[9m
%,Hn70Ot1/'%"a9@pKHb74cBneHB^u'Gr[*?8V]FDC!qj+=[MV:PgEPq)>AXM<4EQ/b&MO+\qS8.0[c`=\!1Irha/ZM77++&Ws]AT
%dDO2lZVmXJ(eBRkaPX6Tnq'-uM*lrtWUp@%ON<#I_JW4X$W=!MOTVNTH]M_'n-[A(ioA.%bm>`J$scl\/ARN-KRO$GaZ8n2VD`AJ
%CLj37hFLq[U:q"<&_*GhgU\2f2lA:MP!TacHP4Je-hDC_,1]N1F>TpK'l(C>b.gPe/S%jjK7g]O7rg&)NSREL&+r];5iA^Y0I_XT
%GcWI]G:;khM_:P_9th>r-#UR@)*Ngr(hH:fktidPGa-:"0@a#/eU:>Fp">kKB;9#G)X"20,"ZI@ig&gq_OG2LS("p#'i8qgV!]?!
%PfZ.g?RrJ`XFd7`(.Ber0&.Q3`@/$$iEgU:Y,\`f5PS^e9]Af!aqlhI(4&B*%r=.>DbqNViH67fVR3DXC)>hOb'K*DV)mD3mt3SE
%8ZG2['=e_-[;Ms=[%\`tlHNF,Do^8GM$JOAH__Ae=maj1]RFN>E0A5kC[\EaGL;152H`D4MI:JN:;mQN!.A6$^osC(D+^]9F=n6m
%jV-`.kEt7/2O^UEe#\?48BE1bO!);rKV\uHAOl7N<q70GJ3IWQfd';%=hHm$ja5'>c4"3gFdK0YfOqmu@d,sIc6VD>El3bfOqM^;
%pD3XFP/u:SZ66Iej2@qh3C2Gs&<.eFgS/^u;-,+:<ae7q80D.n;)B-(Q69?]]&Cd@R0%[_oQ%eA;](??:T?N&i``?RVhI6c;[Mu'
%*&0I3\8ju\N>u0-KT[rl3)i9s?jSUdPXVpbm!]"V(docT(ul>E\X%2,i!ds6Z;Mk)5bPP%3PjOQ5mp/^'5's'-9g8F!RDB$J`9(a
%kcR7A);:93oKr5(Ep5h50PlhlROe't!`r/1]J<3*G&".1_q7X8,r$'p+\s)tQ5ZZU>6=&pPKH0AR*C1LDKIqR$04G])i!rfgCPt(
%:0-@jI@)"3\amd#23C/D`E^ppbTdb\&1bZ!egifs2:/f@f9;?8/YikEXu)#MG*YjoN[6,hH9W9P0M8C0\1.EI35rB]fk_q!P&,,2
%(!W,m;^RlUgcQkVq\7eBLqK#V1?NU?6/LZg^ja+>&(`Bfe@V0&h&!*:<nu/FL$P@o-Z\!&hS$l5'"4LQ.*3R`D%PuULcS;Ae_joZ
%LF56.Z)F56Yq*IEY4>0+`W[/'OlMjb'ha_Neh3?%L3+4`If^gD4U[>mB98=qD3JNO`5Kd96P*YJO=-7e]soJG3h,G8:fqas^/a>=
%MX>).R0$<%gT;l,X/,$G(h)6jAZp(BYuu`!bj4c<)&^c_@6r!00ihUYr@i`I]%^ERA3!g6K.fU>fn:`t7(/[Y*Ocl;m9+s#KaWgq
%@Q!(%/o2iWA3nG0c)@bSN9[&ZOHo`nSl3"3,3[TA-&OP5NK+c*RS0O"G.&$JOib$M)04LRk!%4Gq`ZmCPAZsS1mSC^L"ol(N0Npd
%T$-+H=m["b%7gIFijPak-No<P!3qrr%Sg"LN]buu6Dt#^G(3W1`7uE0;W-+1M%WQ/i)0!?c"HDI\Yn,;-MHV3OhFDn.T"3Y7GN)e
%[hP!Fo-%VPZXI?G^$(,+,oX?<Gr<k+6<+s*WWB&@1_HL5PIK'!g'CRF;l_I<Uem=!lZ1<nmh,t^BsD09%(1&k;L6>Zc-&(uWZ.9m
%7jbeXjgW`j6JUj>/o;Y'dKU)BQoR2<9N[r]%hE0qI5(ec02rG8R;A%JAt1@'TX(b9(ku20]NaS.-b:0tq*aRKXulq"EHP6tfVus#
%Qlh2VPBj04WJ?<r679jsj1K#B*k6=PQom5h%^Ms+1D3LSR$7.pFM@HpBN&Nl9WbUMV^Au-2@uUL=]CT\#07W59rD[,1o=KY>LMk:
%;hpcNK?PW[8g@7\AdM.u<0ZI';7nlS;GT;`UOa^6c#bk1C)*F*`8hH9*`1(8gH$3>/0%"?[iN`,)&@^.8o04iZU>rRC5U&"pBJ<_
%'bun*C/p>lZDaJt4sMN2<L2\O1:ls@@MM;MXKGH`EJ`tQM^U/sHGdBRVp%RWUFE9hc(=+;r4l2>W7!7c4L9'Nl=ROH4:Q`'*4Y8/
%,=b;tqOJKH+;:!%ENiEgWoZrA\Z\5iR<;3Vim^\K<bB"gX:;gbc]e/aQ..h\W];iTH>f.Ml?K.+s-iU?:HZUc6:(3sWZG&DDE0Nd
%Npeq7aI_W2X@XF6HmXad8qqn`T\"s3Q2)8^8F'o[)p&7o1,'!.":[b\NPM%jQGSA"Y<pWC=No_>";TInPq!M/S]ELq:3rAaH$>e#
%5F+i1TZX6Ik8qN/E=[bm78UTQ67M?]iftk%%5+I$AHTTVhW&$C%/F^78qH\c@X?+Jl_^Zm-):eIYVW">3Fote9,LTM*$p+[1BT(X
%EU5G4f90hH)rMNB2c+dMPrhph&QA(6Th`#g/0Zj]&P@k+OEAb4lI5r.4$[h0kg&&7)1lMT;DUb5H]ZG\S8(/H@PZndJAkh>6mSXu
%L5a7%M2i_ICB?io8s&$e/=nFQ>1N]N5a&pna`PJD1*OZ2KrRWD3Ba:Y9U/W$be#\;,W4:NAg!QogeHK53AR9b`2][TK=@.s1[3Ba
%F7ng[W)=(ccDIl\q5KPW:tnbDZ39bLj+(Ba\04T+3_`T`]Ge$@-DgF`&k#AHX),jn9[nKjACe+;)0sL;UuX+FC*SSj\0UV:>Yo,i
%h[Q9>SuCS%I-m.q(RN'D_KtAF&">Nhpn2\ZGB@fbBqUkR:@`34c'&MN31AsI\(p?d;N?`km(!dp`)JK]MIf,E-aVg^p,/,Zk*ZQU
%DO3(O-=^gMP[oB:`J[eT,q^kQB@T`l97&,ld")=(.q0nLc6N$f$]&C[O:nL77u.?1.Z#;Z)N9bXR6N>*a%7kuZ,,GJlXJ\)*Fur3
%/"&VF<\t%(SAK@n,eO5G@I[=<\iG)5-:[9=7tn:&U#F@]X(2W]<E;m:_849q+P"0b`fV-`kq17%Ulb7on<7bDd.6MB,jH\`'DNJ_
%#9P2GbTM,[YSIPdK=OjD=pr7*Uf?3a`UhnU.f^I<;-0:^>!T%?m7C:p\G],N.u7:ig-6bpZboA[JpV$1G_r0#[_Puh,WsQu89ci_
%B.SE5Wa_ll1JUXu;+3rr5N?_$P9eMR,sbgUQ_@t\on8%,3AiUSBD4sl6\GUO7kU'Vlba"23FYIX``:-g_a(=q82:r:Xr9_2iY'ef
%+'D&K-A`J&LLq-UVUH@#\>c6kdY-(>LuEcfln,5[(&e_s*uq7HXe!EXdeY,6Whr]uQag[k45&A5Dh;GO<$d*q'<2Zh27MQXP^UdZ
%,`_au>ZbM[92GfqaVCD!Bk0,EB[r$\Ji'HCHtbN].\$[gWi'7?`Pb\F;ld5J=WN#27Aek4PaPdnd-"e<@6[$^PB:MqA$W:*Xe(KA
%11JN;5?4&+=fLGck:+*T"H_+&<iggEAL:@\/J+K&<,".l5(G5uK"N\P2/$]-`I/JiD0%H!K[An\h,U=eF5#.=3183Rn.kI9',$GE
%IU+C/8IZ'N)+4[B=6P($]_Xc+>%Rb4k`A>DMfaItbDf&Q<k,?=ZKg.pbdD=K*,,GCp:m3\ULZPTmVmK#q=sg.eYC%L=!s'T%[Ye.
%c:1<qkAk_AaaBG.Y"Nq>?U0f7*=t'3mQKQm]e6mgg2ZA@lcYKm_l#HVQ\UVsDSEhJI.kR#&+B;[c)j3o5J6Si5/-nF5GiQob`U#p
%O_KCK!u-@QZrS!?%f*=Q@c@3graPfT4agl_<T6[scC:Ji=_4"R0I&,8L&pG01f%;s2u]QoZN#=D+SGcp?WEA:&2f;)'$^>rd6=[f
%dm\V(r8]6uS8t>WOY@-I:b7dnbeY<E?$Nkn8fYatlLL6Z[6Z;(=rVh/Z1m;#nP`MY+q-s2+>/g[1hpF[F0)"\Ke-$'9k^q#9&s.p
%HcNDcj2pgi?^otBCEjg$=aDF,,_W1<pMhBi4gcoIeRb:R?!Ze+a52h(a%ouHZ:L#!!C9O+6OKg2/8)2_D_0meo[MS?Wt!@pXu?!6
%rVQ90nP`MYop-q]5X`X%)jL8_KuH\)VNY8\Sm%W=C,:'T\@pqTqrb9^eCS<GYK[YBi?YJ5r/lY(b/m@M[d0jBVR5&NWNXQOqV$ns
%T@3.+=.<2VPI*G=@g[Eu$$.[2AdDR<+UNL1D5L@'')L<UWNXSArXsCH]%X6t[_o`/]9_ZBS7I!YE\<-O2><(&4h'-Uf@QmQ^,G$K
%ZeKonC?*nRK>Y<`3)WFX.7UAUd98*'(3/bXO^2=Pn\&r!I;61JGp$(s%U7/'Q:\6#Xb6Dl,g><qb'snN4MgRgIFR7-b>m]&MtUl5
%,j0Pqr3SY%0E:+RT)elr)oCR[^N_K`\u!aAM`RB@<m6s@:9O>cHXRX*j44C:*gX<L_sRiFI+eT)Uf3>)GrC;Xi/"TA?>9MWTSXOa
%7;*Eo;$6.Q2&G$0@58Ce:^`E1U]do*[0#,o"ik".cmNhR+-G\ed(kn5GK</r:ok+%M\Zg1hYP^>XF5/qaDul$5sKPs<,h)KY/<'o
%L!c?b2,M1CZJ\k%5muL`:F?QBE42S=0JW\0</1Hq5"r'_[_^-cfKIG(7X/3\2BoNNTXd7/-.':.>\O\68q:We.g'GODV/,s$,#Lq
%T;O`!%*AXM:'ok.Z[%sl;R)O\3Caa>F;1dn6S4/ulb/#1i/6df4in>GcQ\hdho4@?i`3gR>"-Us.(K*_>5Be%N#P+@W\]&.7c+,[
%#n8Pu^+N4N^ll'2McIskTZ5hCA#XZfaU;.$]aZo5Js(j]4(5Er!N[Rq"(2)4A+U9t?WZFM%1C_*AuW><OdaMI<1"8L_g99_'f%.^
%Ug:E'"ls;^j'5maE.TF8k&a;P@UPe>WDLA_!M#QP0`+.FKRS#[Ti(\q@(3(4fK\^rf`M0S>JJb$ND(5.R+=EBF<jDB>YEs3@Ud5g
%"]RV.hJ//+5if0!)C-7o=^8'<oP_]F-=8]dUa>fEd2$cd+Qm/#)?DJ%6+\D[;lj(oTt<9&!XXV001pPT$grVK#tIFTBYQE=,LVOf
%>KOZ=V<Kp#5+HI<h5#JZ2>V=I%Gm7Q@ku9:-8a&Z`;;6kEWfgHW$N:Pbcb]bXD:r)oAXO#;V`skXpsZ!%CLMdqF5+%FBP=Q>PBIP
%h5ZEJo;n^dQs`!fi.J'8WTt<7GnP@@:N_GaC9d'K[MKQOWtr[*/169"]qd@8SO2<BF+Hk8g4*6M]nA;g]dQk!*IM$<;CDC$T@PAs
%VM+['WqDN#1s`LoC!pQ0/iUZqqFfd6\YqJN?3P7;pC)cX!K0FO1mZoG0HY#`D$rHH"]'8>6U3?A*39:eZ3GJ6<EqSter@BVRmPV/
%*3Y:/2+P6)Tl+pQ2pEn6L=A@_>ofc\e$?m^dd<_;%TOd1<!irqCf=;M4Q8?`^1AF%!^9UEN#q_F0q0mnL=1]k$W]l:`EUs`d#_f3
%+a1b;be+l/:,Wj=G3$V-?NjN-g_-hQeSu+\`'JI>+:i%nlBaXlb`[]BE`N24ZW;W3!X,9,ini/lJ@Q8:S:)D9.l8f"JuC3$Jo.)3
%*STY*MY!tVFo*9<7<0q[bg3uTW1L?f7+"rVghGRWk8cO*9nsNh56r_?1bW]Y%Q:nm5\iG;;e_9pC$U'^Ek5j^Y]UD!m+o<O5tW>`
%j]&agVhr(uVbr's0f0lP'ng-"?n1gLcSj$eq.Rgu'Qi\T5NA&!;FH&\c<p?s2R&JIcen--_=%^?g,,cpltR*[ZE()gg7hllg-lZn
%m=Zh)BA3p:5(e!&f>jI%`\BTqZ5Go'F.<__C0(gJa)6ucEgoFB])-An>=O\d%)ZYLicQ#`1L0YF?)\3TCW!3jm')Sa`DZA%Xr?ae
%X4E)U\*MUhMkMYs/]IEANUtrl-^)PZ$+BS-&Wkf(BQ1Y#<G_#l"rA5L--]l)8U<82D0Jr6%Mg"->I@I-*!b:pj)NUq,7r\U*%(+.
%Sf,BVjJHEHW>('hWJ5!Y-^RmAJaG2TBhoe1`&9m:ff=T=3BQ/B-@9kp*=d"k)5FVf]f2\`?7bTgFPk7P?E$K(6:b=QUC\X(_rQ#q
%W-Y;,H'G^G-Ql!bP<E]uPc*YP'YD#s0C4!t,L_(n$e0G.U`#9,^1&[K782]\*3=Cu,[F"qZ<"5PAqkkC80'p[nkpdu+'5S2VX&o1
%)B8u-8tl[@.de4X+G8_XoVj:TYT4!/SC\\*/qH1QE%alg,%*E_%8q&\iDZs4<@O$L.c@>_WS=MgLr</PmQ+q`f3[r(cV!Df4D5*A
%hNgeOSl_"^N;`')fP>\2o*dZ!gYkjKe$lglrNI#YGc:kr;JVnOpoEc$a(sD5@5(,laCR0o?<Q2:\.Knn^=^7m3S3t98me5Y9YSF/
%2mgU_JVW6rX\h>:\,t:ucL<F5qNQH8V;O$%;Ioja=$^2Yb8qX`oaWpg:-GS1"Q.h[cP$19Ujd(_"=jI)m^=oUI2OrFLDGng"&JZ^
%#/#*hq889ObgA6_*Z;j!YT=_PcI"-9dg?\Eg:hkEJ8[`#U)mP[3-8A[[lu-erFRmZ&iqdin2;4U=iJ$n2BXKHJ_a1&N_VHM:/[Gu
%:&^dNV!9#)b(MoAk@mk]WkiInG3WkTD4[L=pt-CC=d[38)o1u!f'p+*WZu#YHkfh?W-.;jI8SgG4"'lZe_)0K/0FUbZRSR*;c$H4
%F#o')1,P:k@an+(<$-^[fj>l\9?$&4bq6GtQif>?>)0R>Y,-;Od:t?F>cV`_A#1%\kCU^aDrtWg2<2=p))J]p;*#c7q]0j,B(po*
%1)7&6iYoSY@P?>8:J88Y/W,MF727i!8S+4"E\BT;e;GbU'[[Uu]jNUEb3>`p:Hu!c022s[i7POV)<#-iS0?>hCkcE&3SXY4H;cCM
%gQS2%=K2=W=!O2\[L+om_jQ5m)q^0p:ge2`G."pS3i?R%X&dtCB?1<k6`@:P%4`'']Mp1E#Boh#*`'<]i"_0X%h_G9,^?=F)HZ6q
%i+=h>b`5,$VKUkc^npM=%QC/fYIW<JC,E(E6AScI](9!A5S>2X7Up@A%g'1DNLCpuliqK5Fq5B.L2&O<VD\WoD!DBN%gGADU7Ifc
%=gYonB'_+q7@FINZ*+R9F0cb9CU/J&1Y)P0N`bT6.Po$Q_<6-f?0^oY6ZD.#1/LWB$p3Le0hjPlb\R^UD=,F$C`G"I50_?^.X]-?
%MlgUO##9g(\AtZ'34u)GEiN1#?tgt:&C-@%6HTti>dG?(RR1M!L>Y>i:]m1FAD'04aXKN[RD[,d#qucSD:G"SUTnHrKN9VS\Nm,t
%^s6Uu"\NKuX':,2Zigf^8;ATXJ@QIfH(bi&CrSsAO'ZpNSM;%mK&)kFoBp&;.S-g;;`,9TgWjEtlO92Mc.LK<Rp\@1FZ/8$Y;Tn:
%88*UcRUGgfLN/]:c#L"D>nolOY.HBsEntc(2@a.c\SD>(21W;^\M0>CWS*4n,F8[j1+^SHXlsT@N55YgSXG%]Yn@o+(+&oK`eVYC
%L13ke(sM#Ldgo!eD*;])j.Kq[!`l(9&,&KQL<nC`Cb4(<l=Mj_'P_cs=Lptt@QR5rYZSh&5ViQ;HdcXc?KbSB5O=Ym(LT=;k,Frp
%S^lA^hZcjGWNOEDMiIC]]b)*MeQClG'e+d[qU)aNqD$Yi#MZ,Q2AEgW2O/dr]i(\<:`^/f,\on_LDi?N0I.;_<$NeW@7Y]F6'`WR
%<c]:7VH-+QeP^REeDoD=r`MgiZta`0bSl\JmI4.A4[)U(+0_VA*+2dmU<KU.;80boMKZk]ID`^g3i4$k(M1Gq9JsqT$^TCj")@q;
%YqWi`N2EtQHF5YW1+pUdoeA1+_&VdoOf]sh#Mg@`F(M,1SOiGI!n-?Z?n$H(9p5T:j!MR\G,M="YgPH;fW5[kET[F*dfkJ>ZL)M.
%"fQf)D6050D2#ZK'-oq4)2g9pV=%mrenuJPS"R#1hX$_$$9%SNbm'4%bmpqWc/>sCSI3lmed%taeCOerZZ#\XhlR]*-!`:1p?k9A
%R;qLC/R.R(f]jG&"3O!FG=Z-(#415Zi.PaSN^u#F\kXI7%@k_R$jHBHo3Ou@_!:*].GkR#r5,P%\qdVu[F#_iWQ%bif[ZZ&G$TOH
%I24Cbf!7D@bBai"@i3g[DGs08>rRt-q?YqGYr7=`m8&?a]a>^LM:61g2lBOE0Wn?gRQ(OkR%"QR-F.r+NDj/io'Y(!+g9q9R&7;U
%g9OXiWQila]RfJ$DG)HBod7t%3Aqgh#heJsfJmk()C60J6DMMFg1$h:Zb]Wc<aX3kYJg<T3KI(OE<T.ONRJ]Y?+F8TF)G%HU:/b2
%eF?!J2]a#umge[r=3B"[A.k!r]A4'Rd4"n`:@Ka*1`N?Pd+mA84)hRd28#oGI6DR3)lF<UN^s)Ce/*ab:fXuq*3lI8H7Oqr$Zt5b
%=u#X(:l+niR#7R2lU#:_?;T(OfqVLMiomNB22/%D)-j:%_Hj(OK/p8/Xo\iGUbJe"+QJ^;"Yi(F&X(AWX3f\glr*&4@oWPa:XSk#
%YjkX!03)ZZj/Vd8FEX?i+3fFc?%mN"@P>Ei!L%^O'4nkA$'>k:cJ1Q+ZPgPeK)l2_nRb:`>ZC^hDG#Q=Tn2$ZPt<UO`b_:&*-R$(
%Wq#Vjk#4&R?2(k_1cmf<;O8_O"gc<n&U30;i]Fei&*e<c@,4?:`YQa0b9?0Tn?tP-Enq)TFu&`^0sK+TKFVmj6hQ#WHYn.j690cj
%Aua+..oJ4dcF1,ND\Hf*%P7JRrlgahqQi7g)E+0S'sGo89ZBeCC'3g[2IJWQNRp&^Ve/?<42?$R`eeatD[pK(SMRp"T#$.@(,F??
%EI==gl(N!I0pNY/_4*e,C!kSml?_!rN@RoEX21:T"MgJB>Q&b"5/"oS/CW@i[+.<uq@n#9^o"Ktl+!3CW`9_$Mnp?m$omhUkX)f'
%2V"g\*Sth4l4hSjemKQd!d53"+N7EK$CB[MJq;4S1!Hb!_-2F\HH>KkZRq0>fV#@iK7k8DQ/>UZMFhYgZ)3Z(XS,$1aO6&Wdr44_
%jGrl=AHk;Le'"g7`1Vsr3S^U5SBGGaUK<06o`c($AaIbqoI=>]?l68g'sD[]8aaW%06N*BIYe3flfcr!0W*<JjTVE`h^mYM>0Ymo
%%Gcp*$L(T%HQ<rUG?[_eA4YL2W`Dq&ajK1LI;e&r594'HLt$+pDjeF49fLh-;"r3mppPRNqJ[F"4.O@Q.g2*W-I9WB`od%%b#Wfk
%Vrjj@RNt+QV5pbSQrM?VL]54*U*2Vglu6c.e=E<9fH%L)DWc'pLR7uc%nmqH48+:>GmuMq0W2`nRKtd%q^@W72*-U?_4n]a6Ib/K
%90e&%qE&[GR"Jd">6XMQO$ZsArSU6@Ca?Y(O!n.ZkiY:_Z0K<.T2(EJ_8F3DZDDSf%pHDQZ;%UQZ7U4VC0J%U)mjDj2hU'cWpp<)
%<Igii;j,]-,"7X%bdQ#-7HRm*XEXE/WIMFi6+OKUA6)1tiH9"BNcggkfP:p.B9]uUGCd.2gr'd]]S9q';S0GuHH(t"*_(1BLH#@E
%Phihg6i4BQQtC'=O*!'teeMM-3FKG-.m*p@pGfQXL(j49-9=me"f!YdXKG=G'(+Wn$)j2-=Xm,WmD.L=p3$.8_ZNkf1L:bp\$3JE
%BOZtk$3Cn;\2/BS(V8F/\o&JUnB-R$a(_[Bb=:W1/#;"gZO098F14YKS8m/#'Pt-(WhWQ!G25(7D/*g=Gs<3)`ca.[c8=nd3*5c[
%59jD\E<>K-mXA%onmT\j7!=3[(`qjDVbXECieTDUA?:V+^rJJ@l9oe)%!HN2SIpaOG2:`#6Q^MsgF>n0/7^rQ.b&Mi%\!A.VVS%]
%/aK9pVhaYWC8r.gS:b;9.Dtc%UpFn*kV+0fo=&W$C+Vf<?$@PfXu;?l`F^:XB''B-?5rEX!7ata/;>'bj#0lg"rSbhm8c(t<?N5-
%k0Q-KS;L:gagNWn)+X19QQ6V)hY0aSGFU(7SE+>t/XdMj'0P3CWRFJ.*6F!6[/N4edGhr2&dL<-5Oa_mTAn$?8bmAVHJ,H>+WY]R
%$gEK5>=_7hha0BlqA.N9odusaY&>I(GCA4.ltU[PY&>I(GC9k`M!J897pOq<3puh[dbaXKUqQ(u&AFCcCJ8$k6H>Ej^"G_5iqaG=
%ES%[]NjufW[!-p3ClIkYlWZnM>i.k/iqaG=ES%\iiF9^/GC9kAkIbqc$gEK5>=_7hha0BlqA.Kkf=-j%\A!Z=h"<7p,Hbr.*H?K8
%l+$'6poq?DKlk9+/YqF0D]p-@I1,:qlW]1c?/J!(]=s!/GC9kh\Ed@T7pOq<4Q?2Q[Y-)ok<_;jqA/'&f=*FHVO6[RLdt_-69O9(
%(?0>c[h+V1^4ggMoe"+WY&>I)EIA5bM!J9$a'ITBLXl&lMFH75\^Oi>GguC<`$;F'+mrXn69O]4(?,qX[h+V1^4e.7Lq:<SGf98L
%ZA;1+R#%n`+WbiU$h9&=>DPdSha0HnqA/'&f=)<O\A!Z9h">N3=Ifpg4Q?4Gl%eR#(?,qX[h+V1^4ggeoduuQlWZnM>i.k/meR^I
%&_FLRNjufW/RaOJ^4g[IodusaY&>I(GC9k`M!J897pOrgN\G%FPsJIndE3</TXTWaf?0&a:XbrW<8;2!OPd&(Z"$rTR2S,j6`GF7
%N.AsU14nu%177!(Xc.g8Xc24CXisn'C1YE:WE*V42q=OB%;C]cY/pcj<%HGoE[Z(.Q.[,f<@cOXB%+n]j)2CSj&)q;_&#0Mdr2"H
%JUDW//$m9o.NRl_1'6mN1'/NGap>`1b(s`8e&hr@9Ih@_:+I"+&Zq"Ba0[U`GIL`n/E!X2`k^&/[iO#Alu>0Zdh/\jC9c=3TZTd3
%1*I[cb+L,j>+&*o%JW[KduQuKb%ug8C1[6]Y$.6B7T?LKJYQ7X1T=%u,KIYUXj$3Qe]R7KBpj-%1"e)udE:eo8?L+g)6.?f]<)0r
%Bk8t@>HJ<gDQB&-letkp1JWhYZFs:N2`4Sc1X<h6GhCu=>pK&HRB@<"-\<i:[5)FP<\)YDbRlt7Q0Gli06OI`)96TmmTqEm9o#U2
%)(4Rn3CgJ4G$d4\+4J@L4D8:G')[T\_9:@p3!1OjXoqm]*Vgq97oLK.#Z520YFY1c':Br0aP=Qo2Lb3MQQY\9<U6JA3Ec?D0PG)E
%D).,N_+d';6lqP$WS5(SRK[)sSI)`O$XPKm9Y\Sh.Flja#tu%L"ji*MHrrOYI&Pbe?l'BT<g.hIKo7:2G&bc1KrU[]^eQu?j!hG=
%P%(]iJ^TlUK(r)&;fBcA2!p6PhK$NX>mV6NqHiN-e';'M.=h)OXe]rg%A"65a]\/6SZ/#_"DgQUD!gMQ1GZZfn"/&T_8pQT9[TLs
%C!t^13tkeXS>r(c<4EMkeZ$JB]7rU3'.i95\ieMo<3*W]VORt:36VEhPV^.s(^-J/lBP19Vs7K\-&+<S92N`PqlnMaiH9He76RcL
%?Cbs\)LElY7s,ZrQp?%4\sWd!5r[9Q<AtSNLr9+iS;A/a/h)K8h+V@j*T0pV'0-:(R_C(O8<<KacO.qNh*2mD-WmUMF40)8^eQg5
%NWV^c>Eq"\G\^0[BQIUVYI&hNE[KH#d/0]1$(;\D?tbm8Eh"8YdbLn5ZK2Cqg)4lab1bjTE069Sk76k0e<5"dX-8/09fjR@$<%t&
%c:LL'Li:ERnPP[\_(d"I3os%r=65ATC?&^5R:'1;0b1ll6HO1$ONBOWTCgsj9^,m!:,*D5F0MHD2T8A:FFF4ZSrg%pJqYt]a.a$A
%HC8/g@GWkA921C_@%GMm]%QW4P_r5JWgAR>dbOJG,^$L4<qS513>qp!]hAt1HC9MCWgB]qU1B[]?7&S&eT8ZONF6'YS5?)LM'`]?
%BW@AI>O(l:OqVl-2\1gbeuTg1Bd\ip45r5Bh1APT.*h:\/kKE.NN$dkK;c$+bBS(po7#^TXc>;'Cg<j.N_S_OK9c*@]i(,f/m0!N
%0%5gf76f/$.S[GqQr99@HEc59aN\IE]9?.X*XaF0C&!6s<D!,eQ>T\or;Amig5aI=M516/."DeGN2n86USoql;a'NWN2pNu_j@>J
%g#?C0<(FL!iA_%Mm]lRM19O@tn*rK"bYWNY!EWR0i<n;Ih<As;9DsFkY30lR(NrCpeHYf?Ej`eS%7.rpb#NIgq13ON)0Q#jPcMdT
%!a99$aiOWnW_#-Rp-tBAs)8d7@jc?\2=27`ftYg:n=SSn95:](+)!&[L=$NbPmn_HVE;G::$]LHqqc*l,D*ScGYS'].!/eEY1n>n
%-AgTl"u`:ie^V4AZe3\JSYh[-e8KV6"2-bo^q1L#Rm+BN%@T[Anuaa=..B]g)N,WI*L1A[9+Q4>([ImriQ6hh.os4)rK_`:$$KU.
%eRInNl;^hh=[+a&H^.@HIT$lb9fG^E8iR/:8ZWApanc,2WpNhkl;a]!k;6pSrAs*R!Oi/dak?0$-EFOaSF[Z8\6C)0Gq6qc<8%Cf
%lV;:0S&NR_eo'jZc&"NH&c1VA>c'Aqj^qlIgX7LX<1eI)H^Et$N\8d$?=dSGm.L6e@.r++jskXkKC.gGA_P27?+U_10[,\`JtqRU
%1OF`9Eo3H1Tf]_Bpgcm=k;;S>[tgkb'4r'<*dPS"L3CUI)l1hB9m>OZ(#8<@2!qX(T&kN$na>>%Wd,6`Ra\WJB%*\8IqMiOp1.:;
%\jj1$X3)L1(F%)4hB\I@Cs;a('4Su6fMT0,CY^+eBtg$1n*EHTS^6r@F>o=oSQfNFAa&9?J1bY5N>5^[oWXg^Js6/;S'qFNRs@^g
%g0oVVY2PpoY*I^Dk?8T]$eS9_1RFeH[Sf/$![Y\?lm(&F\(O<P2/e^)1rcqMSUe\>VpRQgk;eS4&TF`H;mXb,E0QJG]ZO0Te5#N(
%<QZ861nUf;eg*;Q(`>b,=&6,aRm82s`dMC58,Jh1kC(u4VS0*g%BcURk`$]'+5g.YVB@+&IFoB&lt?pC'r:(44"Ei,%T\Z$T.B#R
%P,F>,KgbXRh9A9m5)e)),jucJ#%r=N]3A(s\'`p7<.3^0&#QSsbTKMLh/,8.'bn8=%;TlWb@]pOp',V-[=`SZ?jGrc[aaJV=/=+Q
%1=sETY_1.m]aa3c9?ZP<d*2h5k>OUIg>Yi=8HnZ$:<h'J-bI5TeLJiQnji2keE^C/1_8>CjfJ<hD>S@RLMc>bbtp7j=pGOCgu3+&
%<16F<gg#bS([kt/'ASBAS7kX_ArkAVN`/mQNKKpH.B%fR41o8h>1rSQDKTp4RLVCMh#,a/eAeJ5DDpC2ITd4hf4P2'*Scq6NC9h`
%3,E_8[1ViFTJdUJ97ArOLQ36"<0OpUj4o0GHgGup&&u-ken'irO[0+h>fR3bfhP1*_FktPd/nf2qBE3;V9q-_#&852;k)*+66;+u
%q:[(aMjJ.PHSO&tBo^]uo]Kd']Ulc>+Y)@)O.!n5kq?pJoDu1%goqq&)t%R=kRbD=[s8O%!GqM=%NL`@(%4!kF4Og4HQ2#OJg3jo
%<1S8(n<+S<[L6+0.<f0`GbO&sGSdCJo<+08k2"c/^<WbO;=r(-!PF-Ac]_A<L#uHQm$n9hNbi9M>u$!FrV4UaqqY?uZg`.f8N9S`
%ei1)am\`gb(*A)Dk;H(2R`0<.e&/t?XYbkAS85Er[rch3a3@@^_N!@;;#@bbR_<0_.o_/a4S5jc?mP&n(n`RGFZjP[QUQOiV1@A:
%N*:<*gMI7\ZJo_<@Kp`XiEYR3l91.8K2ikV(02%:G*1g2@tSQ+J.<Rt`#@JmQ-gY/*j>!EJYjFr:[b>"aB(-WARO0^39huJ)VF[m
%hHDj5S[R,ncW"sojLog0]X+!f4\i3Y*PTHo2O,!TA!o:#ECRcp[eK47Ne3ZtC2X9TZpU0TAIf:Lhr0PW`OS#aHo#%ZF`rbiPgbjU
%'1V=.Fi&7?%mTN\B2**NR@]E^%,3P%<2!Y.c<MVQ.n=J0esCNI3>F6Zjk)r\(!1BlB"\U5:%tMmp0g0CCM(n4/9C46DU^O)BJVN:
%H)9u,-@%@WC;POpfs5)+-%hTpL@3jg</j'4*-K`t4,dm/J@\oC.U`JHI1hb$n)ZHngJ-EWhp-0^86:*>gH@^1.N/_KK?P7T1T@Yn
%1;su(Y9V\em&o>rZFeeCN]u,:Z>FK?>1[TrS`ti58&GAq1n881"c+u!;EDJ^4IpY,cu-n5V+R-\_E3?BT>"pC,LeUM5R&]8`,V%n
%bh9eB+[<iXDl-^=52TX(Ypl!Te^g/0J-FZ8\7-2rV1sN1?mg:;@-?Iob,OQMq(J2l!O>Q+Tu@3qfAng]lf=:jbBA6Ql`s=,[eK/h
%j+Rg5naP^/3d`3Yo@gCSgbgN8j-)toKSKVR:o(2a[EgPAN*f@950eA5(h/OtZ."qXpDB"FpQ&$i?Pi[jlCrYO>*i6:>)'WXlb5!!
%A5a9Y44H]]Te>c[s**(W?9;%QQ*i!Q@oogkCNcE.POM0!%!&26PWr6riH+"N3Zf]+6i*p/DYUVgef@IL/c<`[CHZMJs6NG8\UDYi
%[9m.O+5hSp`Q+YqR@ibXZLht"X>E\?FUIlXk,6^UVr>r:N3+&aP0NX+W8C@>?VardkbkC?B=)#!Xm@24O)NVB](rc&]A\IFZ_08@
%T4c/:\F[K/Zg?;PV*EjTe#&>ZmrDNKj!]SW+,.+nr7Yp0P=1#R6L9o\WqCGtkN'4>\i(qf:U-OUP6A#s^ZO]g:R?N2otQeqVe,1a
%m,-$*=gRg;X1-AGm.,Ql`nmrJZQ0?iF/[#kq"6<Sf$AD%O/s:p29bgd8/aDc3lRd3@QtN\?SAPJ`O]LGkE4;rrS$Q8_KW&QB>t>:
%Xo9qbfall?>5^(/oeqgs=SbnIm8c3'VJ-CNTIEF"GGjX3(-KUMYC7Hee*\YF1]*@1=l34TeJ63<;fr9,iEc;"4C.i^fT+aZYk;<#
%.D'G5DFhOg=1S"0+XM"Io@q]L\D:l0rcT0kDX!XckMG^XZmf_CFIaTg!`b!('>6)A?1*2BCXbN/2^S9:f=bjcmH5A]"(p>HQh12<
%bhaEd$Qe]-@j<qoh8o8Uj$>RC$<<nIfQJ%>=cL+AR/?85+.arl8bh(`\fl>H'sq,Gn*9%a?_d<o9OgYB]6Yo7gXpobl5R@GDs`PE
%@r^4f2U&IMk-g[k74ip8q!EIJ8NN*#ii_TV5Kin$CaN?;jhC'Cm<l<F\!$T2]c<I3k8F!rB&&<2;u\r'hB*uc*poS0,<+`?aS!14
%msjj<s7E4uhmp:Ep\\"IGB3<BiEl`ZLUZ_Z?XFf0Gio/N5<cWi41i%Ln*9!*MmN/MS3K'HIIlsSnlS)grP@%?n5Ncn:VV=*VGI'e
%POJJ3nb(.4s3*5qM&t6Ike0o=.F+El5<1g9pupf,k56n'lql.$nDaB<m,@3N`VJ&Nn)'X"qU`&l[EN"#s8Cq!QGXY[*rbqQl\G.G
%mSE:W4aQnjjlb53!4%"Odr2Fn5/'%l\m(*&G>Ic+CLaODDgq3e^O;(0Aq'LE`KTupJ+rbgSXnX9iZI$%kL&tcB8?@C:N!p.HusCO
%o&\QLiHI5Ac>MM:XtL=`p;+ki`VGiZ-]c#'5Li7g?,tpk6T@3BHgC15DbelupQ*%6\0kk[5KM1K_3%5j`ih$jm_88t&O6'OqT79_
%nQD/tj;6:oI^+B'gZ%hrs()*.-`:<o?aC/faF:kKoA<iU;s(.>[h$26\bQ#?n*oi=k5=gnK%na*j7mPAI;s[VEcH>KGBR\]lVniG
%Af:Bc]4\%nZ7<RkVnf^!qV'<*F=OEADk'?_E)OF6M0uDp10M(l-J5oF&&5lj3k7A"FGOiWm(Z]t[C6\!aZMJ.5gO?I&#iSg.bbm3
%_=]aBjBCZ@0&O-I0=m4*c5UWIDU)@jIXCd$qr,B+]`CIOoBT^)/Oqf"jGXf2Dr'o\/7p`D>Pi7-MTGhK)_(d&-hh-6d5,Oa[TgI1
%aF@7l/_,(BQhKjWD1#UgCJF9sqbgbGSsNB+n'8Lr(]<a`^U\Kt]\2>IpI[V;p:5ZE0;#\$^.M?h=eBV.07/4,EJKX1We?_H=r2Xs
%DQaT>^:cL1mg3q4ldb[#YOG-E_DaZg\9N%>IW][TG51g<Mi6mcYG^I9^Kl%c&[pPFk>\miqXiMQXtLkiO8f"R@bmDJ^j:$k?QJnI
%iAZ$EkVN6dr+?Q/:/8S'Nh>,l\NN1E?ZgeikfNYVe"0nM_o(4NTA83bm@DR%ea5Gj4k/+?\t?ab5D\L`Z+!KR+0WB#&R?JBZi5C5
%Zfo;>0tGV>Nr%)uG?Y#P*:]2JQ"g2ef73\jZi4R%U\Op$XC%4bmjYBBqXj^R!cJ@Ch8Si#*o>^XJ'C:HD[,N0?f-8ULXMsj9`=-o
%]/[I:rbA!!LH"'Q^O:KLnh!'m%Y+BSF_ZAGp;L4Rn<<>+YdaLTIJDp_%B^'fEmZDR>/o*Z(VE(nclZ##c>a<0GM0]WpO)@(N$a%j
%CPhuIPHtW:[\$mp^O,_An_)HfgYZ:r0>``hF`tq9dG+"H>lb3'Q#WWO3.S]c'#u8kr'>_/_.V&hJ4jnJr<ru7iCGKh;<"d/pu=I,
%2Ed]DJ,E>2rU9E_s#N,^r&B(GplGGsLI]*]^]$VRdHeOhahY%7rQE)>bMVMuop"0k?"FJST%^Rmhprl6V/,GLA(eUZ?q)0VjgSMc
%pFk)1^LQXoF(2kC'&#U,\)<B4o]eFmoO-9lXd#_brn/mNQ1W:tI.(Y@[!B+T/9<3D7'.XM7(fuOqBaFnYR'Ql3431@M+=13]pCM#
%NfWi@h.hH6`>?!rG9Zm4[GP<dV0r$=4Z1;%=k5`6p<9?0.(2Uu0.-qY\FI`(.fZ)N_d3;6"S]@@(4oFV_=$)sZHjTGTW@CnY3m@+
%'CSLDVt7?sY.qK[".fq3]K!\(*_Yh^,O1OWU;GgQLsf;Llep):c3lUOA;c]^pEqq??I4-NX6N3_?OCJql??$n6q9<=(4^N7h>^_\
%_&K8Z=kDE'o<WT'6Xaj"/U@`)8rNRRb-19k+$-Z-`T@DJftY9bHgdH1X5?'doXCA.e'IXF#L!S1V=&hUI#^VhB"*g]$@T,F*6HO_
%7k[hHI:YnFkQq&E9c*I9DZVSc>?5`,0Y<S\+X%nRNY93\>N(GK[mEsO(=4L+Bq6.TZi'tep;Q[L:OI;3M$mbjc$Oq#4+/<<iKks%
%YP7li<:Hr-GTej!+(&<Ef2$JBr#YIt#Mp&BPV2DN0A[f"qNnYUGY.`'k2bh$MsHkUrB7<4ousIUk\dtX2oC'G&C5NDF*_C-rH7r3
%0=eLL2dB8\50ED^fAE+siD1QQh2_!Lp"daGfq1H2:P6&,@h5`EHZX[]r`=9E3?)3d\U8AppfuVi7";@.h4C3Ca=GPR:=k-;IPZBG
%l]]f0l^N=AIT%cZ2m)l8O`GnlZlEr)]4YL-S8%)Z<r):]e]6;)<i8ZOIl#Ta2BLb(G^aeDl'+7KF0uG5r[g!HYJN2X$fp9_p!)%?
%c)nYSn_HjjbE&jOiSFD(.@(^Yc!9/=FnV0.B.Bs(Q&9<*p%kWBFAXJ/`WrC9r6*QuM7Tjt-dS5>5G).ET3FNQMjnSGiE%CfY5eF+
%Pk[kS[T?Qo12(1tQ8cLT*+eTq>X(F),laPC=_NTApC\Jm"ddn/*hStN[Ta9D7jg6&%jKrp(KZYk^u?SG?/PR&7(t[n\Jlj+a!^$a
%fnqraq8JPeU#=DS)nu7&h>TpmZ2M05^HK9?H-ZB`^Z">Ql1s`6'61X2/C"!9MuJ![rZTEG+h\L'm9;:j%kZYt(QYY2+bA6NeBc7Q
%>@5gP/]Gu<fOk-Q_fm/m4C18NkqRAF5CIp%GJjQq:>`*p>CCVgkfLg"3B-Q=k52oBWW@@mRtkc[nE`2Gn0EmEkMPg)1]-(M2eV$\
%HZQX+I=FgeBIs#0UQ`<RLSbB&qu4,@qp*(a++,bTnYpqifDa5`Y5e;>jkm7+qr"Xgc@6SdOH8j)J,&m.?%-,JrT!p&li7"bUd%`A
%hsU>'rG4?Soea3.LcTp'3ICV13+:]cP(6f9_l>eVS?MLUT9n?6^Z=6r-(!EXo90"0VRt/$k<GG:q0lMFhDE3tVOZ4eYCEb)'E8((
%L\65kO5e`?b<P;[g$sjg:^Moe2<t0/(;HJClB.A.q(''Tdnd5HIr_13.DNN-4njZp5?@\mS_a>!hj,=T?Tu2;\##uP/jB*9\)F5G
%UE/_+Do_Umli)2(lt1Z(H.e8$\d&u+TQ438Oo8tQIY>s\rV'8to>W#bj1VK`?"h9&a,#+jEcH8HGK.(>r:p^,n_S`bMgtNsPY.LG
%aKHn>T1A(/;!)$@&RTp:Zb15YbH'F2*ZYiBG[I>c)\7BTq85_;>6"MEq7hFXea2pn?cul(jBI'$DU@88\$+*Gc<@W)S/u<KMsJp.
%QM.u)LS'%d.sH5frTF0`C2MjK+a_AFX0dfWg0SGpiFhr:1:sh_(G7^=7(Bl_bY+,eOnQi'q9o,ECW:;Z7=K)YYetW9B.@W4LUC;/
%VR9m]k5.MKgMa0p(ih%7.`WZgqh4(_8[91g]Ef0??)@;.?C/>rp^(gjCRN&-qu3esg7e7j[J'+Z^?p.<`Y@tPDtc@='9AA""'jl-
%SeuDYAHZsX0C+,WLP//%H06Br=JssQ?f`uSE;6090/(1Z/VL[NZSa6iH[+l#1Zh/uk7I1(g.mEYZZtK)KinW67iCT'm*O!YFW*Y4
%UITAj<j-Y*XC>.:N9<7aTm-.TH#5"cN7bTVV[<;;emd+8)8b@5g7pAo\g=.nQUqB7V33+CX%ZuJ"kpXB#-Q.WD_i>fo%)OOs&B>o
%`XQ:_3A,uf%O8<5a@Wm:DXG4W%9P\l-CWG)m=MSHS+#Vrc&V+3h,YNX_GZ7)7?&QPQ]0.M,0[qseefTNUo*ImhZ$TAEVJM:`c##o
%(1H']JT?qK(o^+R%j;^P>u5ZDa]hVZXIXJ;h3V"u9cCdQpQX_Pp;-+X!$tV8Zd;.aU'oa,A+Ds./e^EC=FGGM/:G;4AH/t*)ntUt
%qsD%pVo&09-N%i=oRFZU43/Mg[.Y2<rn^T>Gg,/J%D4nJ=kt/:8*M&/,fPENp&E*>qrDf$b.iFIIX,PalK9?GMB_^oOS`kJr?i@[
%s2jrqajoA!obWCE:2[e[4aa]\h&s?)m*.J5A/>$=B@L)ijk]7O-^SIIGOM\T3uC3k+Sj&#V@$/E/$!K3p?9E#0CJgYEq.(]45Tts
%7j%_2XprF$]\X1uEd;Z45&fuOmdB68o-\aI5NM3UDJi5A\Y#ad(\F$]\^Rb-0-8r>?0P9'^gg`ps4oPmP;iKuX4ojeF)l@!b:X^0
%J+fA7BCWt.;]ZY9=<fgdfTZ@5pXE2Ef2lV2dn:M/VdH&ZHEM.k7pk>qrS\4`r;>j->%.FnNEkYj_m?u4U,C9Ed/NPO3@TE)VtanK
%b4Bbp\PNl$f?/?0OoChGK^ZZ)r6%%F^FdO5YJu6^G+Ln.]t;"+"h)143^s)1p-b,JEl,!FnG-_^:U+fg$GC3Iq>4@<[pT(/'q!m^
%_+oG0U?pd^fZ,;P[GL;N#P_5erTEcB'!M9Sj/u1l'"`@FP!d@9hq>ZGcHa3&lFF?jqWKHW_!mmHqoeR_elSY1pJQ'kHYSbYIBFoF
%qhd50)Rc6kT$OV/<q/NEW5#r)?gkSPe\(jA?5Z<9>NE]f-T@r9pI436_KO,;QWrIi^O8>B?XM\qSigl5)>`r_o8Bqf5!@2Mrpa'!
%"h5da%h605\bYg_DO%1hmrSBB]?f$OrK$r\1^X32iBPXE5;"6_lG<.I`,%S,a7YM.'(b.qGKBBV]<dubPLJ\oRIlQT-7qXDgZ+L*
%`7*m2hSpYM'!695fubu+=7D`TfH,K9>'G,fdJ$k%it#IaoD?.\Vk<\24mqsG7$Xnshjn,GrtR-eY>iKlHi,BkO3=lA'Dbi0kfPG@
%\ha<WOW'.LCK5ORZSPK(K(k%8G?Te#alC9o-WaY=3Vi74nF?23-]`,<DDrpniUm!+B"OV(>b'\hZ-/?cYO.'Nj*;]<4F5Ms8)pNi
%?X#CW?eqKtkARcj]^rtuSL8X5LJLZ_fBq'-l2KpC-0Nq%n<_&7c/%R7iYQhhgX3hZ,guSTGp!$uFa"=Y<oBt'L8!PoI(10sXqCRb
%LW<j-4kuW95.P@>=`"\98,<m>IHUF$p$ofbG&n(lNVMK1pA.,"Y+LVBRdmIBp$&Z`Y"ugHIWDo`IU*395IEApgR#%ahq:(s\,2kZ
%+5`UET&"d8G51^r06`3G*hD2ZI"$NaIV5s<06*#[A=M'Z:9M;k\UE,Al&G`<kj6^mPKUikB5>gU;:hbXLlSSCiLmeE]37]0FgsMT
%(*2:MpX5Rmc>LCso+Jsn]r.LRm3fYqEqahGnF-/"+Qu&5Ab<0UrQR!FrU:it*;rCt%.1O!GAcgY^]46;miGYIL=*%!l1O)nW'#P)
%M.hbBYA4R)q\RkVT3Qbap@ubKD6%\9Hk1`*DheQ6kDp.G?41a87XRE4jXK>eOQRZII/((nkIZ<4]<<<+HanQGqR'#_X50AbLOO`"
%G50unG.r`-56'X=imHFsBB+Ud>b,1`=hFQl++7shWcK-q8e0`,4AtF"!C3hJbVk\E^9jM=a1MEKWk]WQI+M0^%NfC-k,r.k6iV=Y
%p#u7u\)7/Bn#t%:dVl[cn'?cs\#X+SgQqd,iQ4%,?LbpNKj8!,&sLfiLY4StoY#]SIb;uDh:DEb^C%F&IBR"Sh&cDT^N)Q?@<j))
%I+PQ>&)G5Bh"Q,][t['7bP8ERSQ!k5h)!VIs$kD\4oL(1l,dT<kVMDmn_nrNn&DO.^+YC?l-/VoToDB_VEWlJ_[ikag'ut=]B&2*
%J+q(Fo`l\'[%H,<qeDZ*rJcrciX).um7q\<9E*26++765Oe'.U*m]6)rG1q'ZRtR=>$6S0O$@pbpuM)6k4UaclX>3iFa%kGF>k(e
%K,srdPK+bOTD#R]qsN(KGPV%%gBV@-(Uq^t[6L2;goXk0EO>bd?[V_<^PQsr2n]C95HD]ols"i'Z5_e928n5Jl!AdaZjgu6+1/_r
%4Sn-fFpDA"%@@+0h0ijulajCcpJ\L"_`3U8)8^P:AnGZ;Zd2dhr^q4HJ]Z-M\)*I\<n3EZ\(%N,Fgkq?IDp1:!5>fDMK]DJNs2?>
%CZ+]pJ3T*tp/Je^rLF4cM#e7p7e(ZA)UPD(hr(jRlSiEBEaP#7+PYeDk$K6,lY?JXTq72a`B<K?HjR>)9/ji[.b6fb18'N.TN*'G
%_#$IE%6>!bRW7Ei<dVRa[jFAiDrsE#jo\MIeU01P3e?)E)O$9l:>jiHo2:H9cE#K\=27L;ZjQ68aZ&&cI_9J0I5XL`RNXpIq/[V^
%7UN.b^W0C`A;(tLErbHBN';:_cUdArTK<"Cdlf_k!">KZ4hs+^MA:geM&IO%]`h:,m(=A>;]Ukt"9*a<F5I99Ykkd;(#3i/d5U-s
%F9IHGnX_+Ua.(mB/)1&MI+Z8pY#kVlRWI`#4UrmfEW1:n5KWU&nqR-Upb(UUp)1a&@CV#\H2[o/aXNUpoBLAZJ?$9>1I7dn(IrRI
%]BJo4Va17^H"4?;(pHpZ6Z\b<PgB;nYp[9"]M`o;Te?SHISG<0O*CSK1$7X5QMQd(+4Je2^AnWkclks,+hXujE*qR/nL^!f:dKam
%qlC'hVY\CkPS(1cp\eMYKW`]Md]TP^Ig=tia0^\3?a=cEd)XX@6U%4QAY>`T()<FKk[',!D"$*U2;]OqgE,N:n#Y&U1pEF\].0gQ
%b7?'s4B%BUGa$S3<KUGoJCAi'iF=;>Jp`s0:[32r0<TXfhR/iUQhUPAs6X?KX3]eo+n.G[CUbJc"o^QRmtX+La$A&";Y(kp4ENXD
%.rpGtZd2I;nJ$D6?X&b?Jc!b'T,q<r1#JYSGji,j3T:-?K_\:Xcq*B=DBJlX((a4Rcusf:'"sdPiFNM[>PT9/-e+TrdkdDurb-Zr
%^Vg32$ZH8MU/*Rs^cUJ`k*sKdpKI3Y;,Nm'cp&M"5:l<_^BZr9,2ll_i]d(F'T^Y(XeS[%%M'd(\sucML]@7\J?p>#FneKI*:#Fg
%h*(Si^V1,afFC[GAb_lo*cJ5EIfMgZ])oh//AC08BKUjK;S2ts"-)T;B"1q'!s-2fL>*18f\#H=s#tMe3#lgr5I_agg'PG:Ts"#3
%Mpp21$1/54Fs:k<$@j"n03Wcj$D>nZIcSW@[(:eTG,#R=+l!7c*>"gP/:K[ALCJk]M$q1EOE6cqrFFMF-JRA?r..<c[FP2=18?RY
%gYWt<iA'BkTI=R-s-$>&1p9l.[,>RcYUJN<;"E#ci4iXA!A%^6-gf7Xe3)=r..MqG<5q-RbH*th%.h$IO5=gM,6&1[gnM-IT'@Or
%9Ko%lB]]+1^@T$dIcU,(%+Ef,99B!b];rhDGe<?9Q/W[']cFdMHIW&tn#,:FGN7J1`W:DGr3PSB*akl8Xr=*iS8:rN6\CFp#f;O$
%]>J1V>'#%EdnKIj4Q;4l5;3'Ti:/TFTrS2X#*Q4=%/0etJ2TT*?m!+9nZEge%q+f0k44lHI.Vb8RSOQKqMZK1IK4t71)_7te'1Zt
%KocT)<jQ&E1QWR6i3MbB-d-lk\W<]da2.\\mdE?"M%k+/MjOVI>oaj\NQ._5l8)ci(g-?Q\'K[.dCkTEe`F?V)W/p@#^DmCq1I/o
%*7ChHm8g\ZCAb^>!c&W8Vh$KdLX?,*l0"&gZ/$p9.aTKdrKp%_fD)`lZTZRQLC^u"!?J?C]DS=P!#"#*r)RjDPjoss<?2T8o%AdE
%P*VqHc/r3\YCBL.R3]_Jeb&TDBVtaQk-k<M"5$!=o?@\$T'fhN^Vo8Qm6rmsQb:L_gR/$L"k'g)Q_n*b;_%d,oXfg[5,Fn`m'rSS
%^gaYG[+IXR"%[&8a0Br<q.EOf5OX<in(0poh#W!br>G1;`Bt%3e%Dl4Nc/'kfFHm`Z.(O4>E_nl,bKrLYN;!a<MoOfj*`@"]a#dr
%*DTi"dI%]G8%[:jqjYuElc@,k,&/cUa9:9eJU4$70JC*\EN(5^@q9>`hdF&k^UWOsW\\&Gkq.9pfA6*qZPQY+"!,E\m@tqqA2FHe
%l<Y%HW@gU9SCKqim\T.IH]1,h[p+iqA_(AM;<sfe:_*H=+%hqspPWG@piQNk#G>7.a[0Wpr3cldWhT.aK[&i'kcRFeW$cP5BtiYV
%3=-FL[KCrlNkHD>"$^#1nX:'J/ed[Q*$7HUa=37SqRMIY7iuf+blO^R^R'?)0$"R`Q9MIZ=3q4,A[(/nk2X4+!F6O<d<@X#9gh:2
%c!3ID80I>-:.Bpbn$%kNnn;gf.cBF<>*cf@#4a<,LAH3ZaQIkg&&WjqT68I_eXS>aF[(DAjmReUBAG>0mhbQMj`k##H>4sW)=aG"
%f->lLnto4_8;D-a7K,OTMf;fP]L2AilN+^QB;,5sTArRc+>&#8(n(P&CP2`_='^kP@P//u39o?6SJmH;2$V#2f1-;:Q,G3'kV*gQ
%p#0#L(BIkHb4:uRRM0iV@i7+AN>adjZsT:";UpZd1t^2,<4]e#9P<dWdt/d\d!Y&8:k(nJ7pidK>dX>cmS*sg;;HsN1fOI2pdQ.%
%AAW^!M%._mlH-!jcF8!!e`r?!VF\e#ZsG8MqhD]Veu,e%S"S1!daSp`ObVlQ\4a=95t@W-O2Jk_pUKL:l[hHiEkZYj==&7^6Lmp5
%:p-U!5Blr3='.N-fDj&2VXC:lDeNMi4"a<I51;cr2"Z"(nYZ`TQ%7UfY@#K^m?s()Nr_F!9\j+b[i0'XQRDp<IC\`[r],o6#>_M[
%5H_OpS)/%k_qAJ1*\6![2sOHnhM"ha0<CLV:#en)%VVN<U&IgC#<a/E*f5:,>Nl'n.66;`+I_1OLO]3Fm>;Y^TE"jDos`7YDJF_p
%)A0bJ?qu2i%nl;9rRkpcai!][:uri1pBSnio8gHPGFZ6\m'u3#e2Ia+B"DY;O>PVHSkh,V;O)_WV$AT7L55/hInKD642)TRB7eNA
%5>_:("E]^iINtF#_u,3[?_Gea1ru<p>Q0=#^YdK2M76<WheL%nA'^pJs(F>eEqts)4a-kmHNg%=r6B(l1m)6sF>P;26J_7@G7WH9
%9BPiSno$MZ_oAmPV"f*gj@B5U4:e(j\!p:so'nM]+8=H:Af;r`M.[q%+r*o<ou5T[$Zc&&7V.h4I.b(/k:K\Q.fIg5mASb2kH!Jk
%1f*h>@J+i*I%Q%WlM,kpkC;"e7!,?;>,hOue'[g_NPBaO^`#+YVXF#/k5T0eqe&[dk<P'UXE*f5fo$q`5.W*)*BpPA+3OfPo+i`b
%c)tGKdH]DOrT:Mfrs.<&51gjbYO4Asr43u#:n'h]1<Z.*+fhSWc2TS+ji3(tGk`9P54AJM&')hDW:RGHpX>%.9H\+cg:^LYg!d,b
%'!s+RP_4bTHKLf:YXAV8=NmIa+$U[RE\,H!h8+-]T4S/m<6LdA*mR,:&uO*/O^o:l5oc/D13F._TVmFsJmQLc(d',o(I/Z+Up.`O
%.WLWLZ$\%bA]D^9gufb/3,Y>%i(B!o!$SO6N,H?A4Xl;tbVYh"Bt[4]V0MMeZhlc]3MJ3^Wk;H%WrS,7+VKF:UD\;r_H6<QQ;e4M
%Q1(tmiS<gnG8Y*"h+)j0<uf5$`"F(b!#ieZ8.RZoA^[aPng_/L\L6t7'UnP$npr#KP;4C^qk,lUY,VR)U5u"n['Kp;VS*Ta`A-$p
%/.>G(WSaXBeG>pr5U.m4p0i3ejt38Ee+Ir3,Aja0)+W!o%%5;$d.hGK<tb@tB/o62iRQ^)StosJC6EQ/q8;"7F3cq)Oh<FRbg(jS
%Fd>oodiM9")Y>:e1dJs(.d`n9)4K0#?eXQ"f.GmMk?Z+7/KKRcd_9ge[ipN!i`H5Oj3;)J8eWHD4QPc0+d6W4`T\5_.mCFCk>4ql
%K;#eV?O](b(4O2.M)RH.T+>d#G&LsAFAi9i)ia1@(r;I7S4sO5dc@E5IKNa7Q^:auGu;r2-q057I9LX`]qu'ID]Bl<%c6EB;%O!u
%6O\'V#5te',MpV/[i/#An0(<3ViF6sWAr8_)I`C;_[]9F9RW7YW]mV![)d?N1iF(\nZjos70B4D`/THDSHRC#.mU$fKA'F$<k=+C
%?sO$l>_?5hV38-g.hc!)+7AmJ#+6EpoA5k_oh2.7n0I*(_mEC;BW(tFhlc92Vbn[DlNPQm/ud"jTWOdHI5GAd+(A<Fe[N\s(`jid
%JoUZg]3k$+W`"En"#SshBs5TO/J/:1%^RqN:0.Ng8ch(=E'T"5LL&boSS)h(:0l'>/#uY._P$rJO55#g7$JE8'+*5!r^;UR3fosQ
%CkJ>-8LKc9Vt2DtT;8GprDi^%!s[LPd&6:$\b++GF$Ud9/>esq1ueq&n::#^Pk!_j+N?F6k=1<NOW-A[;5p$-Oj86C]a-IJ,d,'%
%I/i10<2g5DrAlB=4*!@pRNr#f9FaOSctH]TT(J!^U@O35<UYQ]Vj+A.a%C[8'+k3RTOXKlU.aM`i6$D?Z_<54]=NEepDIiSXrOA'
%:,`_Q]8+Kjn\c)*DC;.RER#`Zl"oK1X$.T:cc\+fS(>BP$Wa3:BtH%G86iDp>JR/ai3"eGMk7MT[8cK='\D,QM5UMMbV;jBV74o]
%V%iAg_Ets@d!4qG\96iQ0'q5&W/-:Of7;f*@X,E<S$!TiTre*jRl3.K.Gu_)AuW#gfsG_oILN_Q1W(#@2I&l*M]719j3b%1h-c'u
%AOEGFM0rDlCaN^;R6(pP66R!+>-Ss6#]RSK0eb2&n9"E)!YAc8YjuJ=gVC*5h1crHTarh4?Nhc_1YDXiN^e5hU*I/Sc0+m7S/_`!
%8kLiVF%3fNer&mifh?6%'u#nB)WatN^nJE0G=2&A\=/`j<X(0?(TSA1T/+c.T:\#M"pnT>M")T#i)s]q6s0s,"l2Us\BQj6!,,b]
%#lOaoLrd7W3U$*HFYq2j7QH$1F5SDp_ET*gk)=8u'U=<#nmI\WLl@'d''NA^8V\Sp;7kd8AI5"1[&$F"fn5$l?)_KKJLXgAPWuA.
%E*0D333F>HKVS@jBQ0mo_Jm-Q73DR>":.9%*k:clLoN!+]I?-'76$Dl6:5mbjp3*@VW*@:,mYKD(p\?<h&%"t+E5#FpX!T02sc"X
%aPJo>^Z&-Hl9kUV#pHtALT&+7%#@[.j4;H_d/9&S#ndhsi0Vc>(E^Kos.I*:431D*T'#e7jIi;IkcUpYO>&NPIHkJGct6!9C0qRI
%JNqX5h6[_&FJW4_13c0]G"5N+0bE,?"atsV!299p!?+LI%Y<JV4`jb*lTUdsR]_g'f2DY)['$`$HD<2B-kTH12jO>32KCa<4.@lQ
%&:%f,!6t>:ZKa9=Up&14$V_H"55%g@E)gS:QVaAn>515g^70TQcHpo:L>^>D">20sP425IDT:ggn74pT1C#?,CQAjY+gF&G6c<D5
%2Q7h17s,b4"jQ1ik,q!/NUS05JDW=9G9242jJlC`5##U/l)s3L#2_9$B+#26lDE,S5VI4:cVNEmdKta@nI(?RA6%jVeI,KC4BK?H
%@!,W!BO-@+85\)3Kul.X6K$qmBgn4XQ/i..68#%QBGY=bCZ.)Ne2_I>7_5L\/a+?ufKF.>I?ge\)F/ip$uWBp3HpC<f&)'Og$(3`
%9`jN8gnQ9;("l@3=:><GG*I%L1m_lCmZ+-\`Yo2D%fR4L7gcMJ'Z#,7WJJIm2!tdObe[apnNENgoYHF<&e3SOgJLXOn9LVn_DlD?
%l:YBiflekP+(fai/:#RG>`e`H,PGE9@0[*:C&^"k9eK#[K.2c.`jTjF]4_;*0eDBrVFl-`@,0(J+;%u?G0drej*r$,X-([,-HQ3W
%&O&PQ^C'P8&O)YRR\>(95/%0Ykh6a=nJ4:3\2Q,6AH3YCBYEc3Ee#V/-!jC#OqGYo8j9IO*UWm1gs^NZ?[nOM6(b#c<NN#BA^1\u
%`B$6g5Z!$1q.]mJc372[1e)e,BK?MC08eY[W_\k8bAi]!$E^T)MaVS%N@u6;(K:*n!bplB`E&7N<Wc69GoiD36h%oUTJG>M*VYeV
%=E>ug6t%%!h&l#E0-F3L$t;p\Y$*@VDWDhF.9"JumVY`%^H<$I6n14L!>"=gOF=+]g5Cf5Lk)BNY;VXW0bHeCM[H!?^]ElaR*;(3
%Jom6<f[WgP306k@,Di"fo60/koDTeq%:?E#J[C`%-H/.8n@Un\l0keXQ2%""=k<[)QshrPEl2'W<[pR/$]noo%TKZkM24p1_r,;+
%/8&]+;Ge)YFK,!9Zf;pC)O2%F(a(#oZc>d+r]d0lG_'MoT2AtGP*<^I/c:?SO*')6I3cHcpF)fH(@ECL1]fm8-00G+#Qio[3hqg)
%5#\O*`Ojp8Y([#?FDjNdAQ8g>P'9Np:q7S6pSN+\\5J5j[6Jtp2I*q4`0-))@BC265=$R3q[fQZ^AI1gQ=!n)08">/8Js5Bi]EtK
%Sc24Md>TqM_6VlM/QoD0raa#'Vij$lH?V0"V4hfY=[NV)j:KC+7s<gCrLtL6\hbPY$IXu&_P5)XGrN3Zm1=U-"Qd!QNN9HtB<uPe
%.0I4Oi>B#X\B00DEpgMZ-Wi*n:9"ZtP,5kOMU3uGN)^i/UkGJf*:A-O3i9sWnEeRH<iM"l9BUF]`Zf4eEH$([.:AR"[@THoMcA5h
%9=$Sja#^:h98/PB(%-(R'>.L^7oP`W[;%D/eMcDPX!S)=@X`m7LPL/U%ou'oTZ=iBd%6t'M3qNcVuj2+?3t5EL'1J'$7f-qBPXiJ
%,`[/*B+c.WKC5b?.[?6t:#ao+X!W?o(DtG5#p,Vr)+5UJ.KmkI#:6\cRcc3s/eG+[^d,#D;2NkV'.'%tOg<G!hVCL>:(8jK-:9N9
%eXJTbO:qGJ2/u[d_?c`Y(3F1)9?iV)7E4R9S'\!BI;)>7pI.jpW*B8&KOT8&k/PuA,B)R:h:GjV6QfG\[%#Ea3ZUc`$-NA;LTGD@
%Vh1pfN9K*q!5_2E]dJn7Bh4Ii0$FJDOU;AZgFQ`GQj%O@lXY`mn#mXOEAk#3"<XPoLN@,khXJ>2fKC\)ohe+j%eS@g-b,\2Fbc/H
%O1`q0NYf)/3NPMB]:8tu3Oc24&1p.IEh/ki_j[0q5aQB90mE<;JhWPgiLY,%!fKed%sRlh*6]&Pkn8^b:LF^).XI8r"h7_mPR1@2
%qkKel@8l2!k_?$74Z!fPHHXp*aWsZq@6>S&3jG4gbq:NnhhmKkd^kJIJ-5>2*i:P9[k#oM@cX4h'a<5a-*Rf(rAt1m1QN)b$jR4N
%]bIBr!F$I<KD^S)7\s7VM."nGo*GOK1kZKRSm$q$YE-0`WG3=1;@8a9?7\ddq@m4+af:k[)?]2]^'UX.QIf8(qeiRgffVt&')t*)
%e,sCk,nMWn"cI!<]tB4PjpY:R[J@M3$Qh9\Zig*tCZs5>kp[D'#DV7D-C`>E0\B)WAd@^h5GTkte/5XA)A2b-[#p/'UYZC6-(7og
%iQ`"8/jSGU'n$=jU[E8\Z^-'<,SQAQbh$=&B>(#0QFW5qqHHkf>WQ3n3%e![LI49TY83np9r*A?>QnD2<h'Mj1u>SA1m+B+bIm+W
%_Z=W(JK-0[Q[(J6l?k>l]tS%aQp<pp$p'_P>jV=k\B=MQi\O&hoC0j9a)83QF$3CCQ=CN+Yk<,b2XVHUkA?8?U<3`9+@u30%E9QI
%(^Ujgck#>288:pFTn%p1)s&Y.6KWP(^#0aCCSlV!H"&6U%d)J,1_=2m5*GJ*2WZl`AsW4d.A9G20Sh;->p!$s(OkV8&fYW,iA7O9
%d[o(`Y@u?"WQ91VBT1OD9IHg<%je#GbdNRW,=D7%n=A)nS*UAiJ'`Y-W:-GK("EO8_ePk(VKs[n;H8nI&fn5no;!..Ph&*dWeO0&
%Ed;h3"sHM9m+aQm\#^&Sj,Zq]*j6GhVQj%=hMt@RgKYKO'PD_/30CEN\Zrc2lZBPr$=]_4R7M;^X$*<UYNpFY!cUT0O(*k(j_8f=
%5$J,/9]_F9E\Y6Q3(`^/!/97qiO7UABNBGumLm<M==?8f6m,64#8S=b`8HhG80o&YV^oW^W5'<O`XD)0NQc<l`nh%EnWXJT,3oj0
%E#Q@!Jcbmc"&O:J`3L[QToOk:3^rMb"R+0!NKbTl4uf%`l,!T.\%:%3\g`n:`k9mrg#YD^$0S5W12StceD1O+K\crH>I4DD94gb7
%`'\LW?(o[XT=5K@c!nBQRlJr%5dW'qF8"*(p"R"MD/aiAV^30d2Kr#.UpNL&em%+\3DWndEMrP[?-&H#4t18bLRfF"<?F7n"mgba
%o:Sg7nmdqPot7P-+d>ia%CfZaStnYd.1s[d#^Tu(r4u+:oO+M]"lKBO$8a?hX)*(jE,h)4<7n]S^6[d8UAY.gr(0I^/.qMj>T%$5
%#B\(_l])6\pVo3(+Q!QYWd6\KYM9$(Du3iB-r7bon(3.]CJ>&b)nHJ`P.]8tj4.\C8OKYsQb)>hFBq(Ab&TPHj4p)Erk0n2pLKUi
%(jN9OOQ5W;/mdjgaTnM-K"OiE>:'=rm1<VED8">fU#WeP'&B>1YiM\X[[b!NP,2io;;3L_r!AtmHnT36:moVe*9jlW1UtcLN!^pK
%-W]n[gn#4<+N>j",!k5<#;-,1!DhjZ!`kR<dUk2Ha"3\Ld-B;Xere<)5\kP04H*L,MJVGaGZZn>?0C<%a@"*tl-;J4)-iuQbh(CW
%DQ#E3aQ["G"H>M&-kY=[::$,inOkce98[TS)M4>".7:Fo6^m/cG*]Plc0.7?UX`:KAM2q4(rWKG:s(+pI2EXJ+Z$'PHp=eB&*r8Q
%VdJ74'N]M[S>*NaLHDnH)5K@>qAWgEZf0'[k!E?!8uYAIgVG,cP@kgUE`Q49>HMsq'@5E?$f@uI2Zqen1r@PL0U8%Um<hlaH,hWC
%?Lqjjp;qa`(tR=@<Wb#NM"!<ac0#tSQbLL_>Vc^o=f$X_Xk.Mpqdp6t+.<@'?r^HcWmXq:=L@Y1\Un)(Z.7(u+kc!c-,U]DSoq9)
%EmL6CRh6]<2[&'C@j@5g>RS(O0j)]T9k]=1@tEDEo'D&tg'"W"L]H0D["oK>2@41dBF)6ZhX,RIfiWfHf?+1LRrJS4W9mp@#Sc^d
%2\O%C7T!3:U#jeJQ#l9G:E-?Bg$\7a.TYoi_eH^g%++DB[T#;BLOKf#Hci4V5qIe\#'!g0!5`1R9EmQC2Bb?Foulaf40XS/i%'`B
%4Z\$@kQm'df28/a$GtCnTD!`N?'J.B(>SRA[r7f8ife@8]/f&Djm%*-LYMFfNLfBNg@<GY$Qb!#n:nABgcs-b1b%fR]pE#peGW*%
%T"2QTWC5[2W4$HEk>*.6#CQQN_m_9)s(3o1L10&IQqPM+XR>i3MCTVZ4d!JKi00(_Zp$p*1)P25&t"N4'nYjin0H:(l.-q"><gFX
%VJ'`=kKb4YZh>(AZ^GqQAkAYkQq`:@J+!?p2uBT4+Wi)JL#E#]Q=.C<"YPaZn4+iojkr+u;Il4^!&dqnn#bNbCdpY,]jX#(IsTI;
%R2.mcpbSrPQPr4Mr&I/0,p?(Q^kG2^#PUhWDD;/n<Zh5GX(kKf/ig]X)hcWSU;gc!#$6fnllH?`VrWFo&ZI=;1S%$Ti?Zh,88*D'
%_k1N8di%TLpD/#$#Cf-BVIkG_qPhb0"LN18q@3K@TsjD#n0P[an.LC,i`48::8\.O>';oI;JCU`mTHtqJDZZ9G[PJLiQ`\[r1E3`
%iYpf(>b[>`m#iEZ;+d?R;i7:5-@-E4nE1=d_aBOG'([PREn21/(j!j&g"LAZb2b4#Ur&`g8C11djiKrE[UhgCeD)o$!-e&raD6,=
%28\dS4TVEp:3Jo"Ede2C\Y4hb?#V/t\.d(2V31AgJ3^S!3#S"INIk+H"\`shV.U?.(4<^^fG4G7(H-,G[]&!@HLfKIEWl.0\ZF2W
%qloQ51E'E6JrscX#><D8%&028QK+,q\:6F!#!OP+_L_c+&#UThN]*D_J,ZsDM'Q^-T+:dQ9:LV3ZHHrC3iP<iO$fP]LDuG5N2h?0
%jM]@5lR#b`U*gBiDCc/r"NOY;f(C-pg+Vfu;p70t5_FYp&C\]Nei7pDX(!"dMCtM$\5QV9bQ.YrLT*jE+qIGYIs,c;GFU6gOje%E
%ZX>F!rI^@1&#Qh!:/(*pXte`\[_OSnaoLNdKJ4s=3'g/Z(P,_+D&m65eFo9S/_Z;lX3q.o([JK9i@hF`C^&f/=*:\4+S;02n<O_;
%<EMAIL>^2d)a9*qNq6KL8).n%*;9TYT2)/mV\A`8U&!#?73Z3!<3Yfll$84)\i3Zsrl.h%+lWbJUO8Kiiod9i2T?A4o
%R_/rcF6G\&:Nd89&5:Q?(h$m@]8UkdcYs0/K_\;A*qT7B=Y*D>9sNW2Zt$DrVI6*p^5I\>#6Pa`m=SNb\8,OClqWTfSS&7%oo36.
%'<k]GS;E/6q11BCMX`NUQ&/Z^X.0teXL<8)Yt!=M6ThGM/+Zjj^M72j''m=;PsQ'%eTM.51@h?IfsZCIQ6mf1XkiY5[a!f/Cd5#h
%Z'0ftl*,F4XOHY![*Br_9fgJ!<n>qk!CGs3)LFLhh=p,H1(=m[*P68<F@*u`d<XN:QHjF:HZd.lIBO%'$:S6rP@Se*G'p,Z[:39E
%G*HT\KiNSs/"\:;?(Crt[A9'_f.CH;h-^[g:HWVh2_Tdaf(rZV\'#(tLur5#F*_-^@*kC1^eA[;&#sqg&`hst>o3.7M72`(FoSE7
%p,j":Ycqg)PtWC&NtmM-5#knboXS"od?bV4VnT=CRe:N;VpkB]rI]P]/["aS._,OmoUA0\VE^NFWBlj[9FPN'BPObqU,)o18M1E&
%<aH``<d/KO-[1q$Ul]1UN0]ug[(XU#$BCn-:rV6f6d&KO)>\6!dkF?@B81!dGW+n`;;?tQcEQ)61t=?1U]#!50ls5,0leY`DY.O7
%I\TS1MT4j25;T1#5-(s0j$@0\GH)NBX6&1t>0K"eU>*j/GE_">KT5heah/VT9t4oN1GQPf@rgbRVC=Kk/-(YYb#OV%FD\QCPtV[[
%WKE5O9RUBqR&4B%C"at8giR^CX(Khcl81TCY*tcXZuNE"mqJ3OjH_O@Zc/%efCB0je[]7(m[Rp(o7X1!c0J?ACHa>&E/Taqm.Xp(
%Hm7,mSB/5m^4gO3KHm(W,n56tV6h\?/?Ms&BGi+>.Eb=136OlglE"TC4LD[dp7.CDV>Sn0kD3?;Hf\qciQI@kQ3^Km]e,PWcM]&F
%`k#L`XNd^1m5EbLeD%LAZF:!HbgI9%%:`W![-cW5gMt7l=S5n,jh0EVS[<EMAIL>?FjBg-%1H^om<cM*9lq<q^:>G-L^`8#(Kp
%(3'.B=+JVb*L<j7G#4<G`o2&XRuZtm13[8a8%k\ScI%Y\Nt+8&K4WT4gQ\"!VpTF,nntAdPVW-91V&rjSJAS%4+")Eg:>-01Zs&;
%+E4sJ9]MXWYel%!*CPN-/!_Yk8U1bi5?X$*X2JW3dNrS1b?65%>f'%X\jGR^a-Ce>BNLt!kdZ.(ZEAVY1E\4aa\0ulrK7Gjr<SNF
%jn(rPW`4g@*^jiaJ=&15/MIJYac+b=qMNiA)=J5QZFo9JE^5Afj<g1n:r`H=U*uYe:AMBX'pT2Ml;Z#_8#anGaA_u#hFq.=ieHS4
%CQAt1]eZ/=AWXrjL*40#9a+!fbn=u*\Eaj71HmI1@M3gqNiC\?-Y?UB*18$g33r7SmDOl^?H5N&+X:37AZb=G3@3=k_o?aURgaLj
%ojV/oFqqPcU"JH2/'Eh)Mff\OCG?e^>"917_F6Q.)IN#597D!8gA'\6/iMs1<Ue";4!>K))56$`A;#4`\.J>d+=0qGA58@`Q(=9Q
%cb*_*C]KbSGTC\V]Uj>Z,<?7CY<^TC+4#,uH-)u"MOu'&g3T,_1u_@Ho?gK7g*t@HRP9(`IA6!Q/:,kd$n^Vugh[i%p4\g_]:Up+
%5%gN0o(d,IX"Vqo%=R&i+mI`-=PE5"-4bQt12-P@8R(!-D5<j4pil>#.H#<&Q2uKtcjjT1WEc)<E@?CUXtKubfr-DLn[(;YZl$(+
%BYE1m=Jrisk^IS*Kh(g8<1R&;d_ae3JR!W6ON%-C/LPb\612fk]t?E8@6m)1B5DZ\U/LtenT7BV-A,u#1"=JnL!O@k8g92R9"R-J
%P[,qJ%(f44Guthueo80R8`@3s?&tARMds]cRYd0NGZg%8ZDV1HF'F&iegY^8EGGcQM7/D]FEDH1*b'ei%)nId@2Z,j_Z&3A[[P2V
%V5-=.=fmU,\N();(Lm#uYb%gf_EZW+'?/D$SX<EsrhLjp4=5K9:*j$[pI4"&kGOP537Lm@/d:PpWM3P$V.Q"T,&JX29%lLr6g7Ko
%:8Jc4#Ktcqkq"'!iF\t0l8+j9\#!X*FMsAC3Wt^blHYFV5.i`8W**K"M[*GK>kc`.jg"O?S/3AR>s9cBMuZS$@csfrM^q:3!q_'t
%<!69qYg/IH7G$0k/k0-#6*ZW]mRikLAM4@dJ@Cba>I(*iBeh*kL)#,>DC43$7PU8`+%i,9L$eZV8p]Vq=!jg6U2#sOE;NU7RSM#U
%L1&M2%hjH&`l6Yi>1RT]e>CF]Y\rnIpq"m+&*iO/,rDk*I*uu[;Lq<ZiSHKD@;Dl7:6)bpP+HicUX$[4ZMK;X.qG//>-5Wdho=A0
%ghP40ADDf\3F>If\"<EMAIL>(cCDA/]6Lk+m`$h(h]:9[p7@P+T6m7@\a4I-Vd\Ho+11lq@&;Rq"EVQ]3M+D)4G@$X:`38R
%K7e#;(^cZEI%b"O4HdtJ2AttE36-+qaq'?HBO,=Ua%V(ma#RKo*j7o%_M!s:LQcP^G+L646$c.%FF9N/R_$u0EJr**r5LP9\^m&!
%C3_e_R?lAZc^3J4q'M;WJa%d0%HE@UbJ%<LVK(Kk1p'6jMB_T6j'pVIH,Fo9<Bl]-[B?t3YCk]kHJP8=M2g14RQ"Y2g"Hj?KQ$27
%n^BB.UT</&_!g)V6rq(G't_08erD$VR_@)>ZhnLMY<-,K5!B,>G3.)Yn^4,^3"[5_">=-(H<2GTiY:qQbsV&W<*<D,XsYGV]rNR#
%^IUim-9_9?fP,[of^DiYGV(D\^&1c5m"RHogF3heC`&dp-<-d+&8YDh:Zi\h3J5*G7=M69$1K&9ji&m9'##c["[?,ARMu+s02(ub
%T:]_$GgK<&K+`PcNH]#E_t3BQ?W[%&YS#ATALu44S]^]%)W%&cLZnT7b`D5nTuWA_5';)MKWOD:oe5;tJq%Nbs#=pEM.Z['k4$XQ
%0Bo_?jhr<e(P0>lo.+BbGQH5a![hYZhuH2*9+_RaJd[-BBSm$2"9D&g^kG=9$jeRhQ4l4r>`]"S"(e_OHkahT7=?FKIE=[-\"\`0
%Q>h(kRK25piNEBo>(F&r^I[?7rRG]t?_bBOc,*ifU*pqQTS5$H9i<q]59[C7>7#lo26@Q]i<2s(C(nXD$tpi)P3";j%*u7e6dV01
%DBk<(9ecJ]"`=tnaatjk_[22&IGm>6l6U*F\3qGEe/+&lno-tuN6&4[![N%(P!Y6icVlB0U3Mf$Rn/^=Y'mJu&idr&'EFqKP+fE>
%-RFR.06/7Mic'Vf=P-#lRB__)<rb(]4D<h:J;t5gHqAmCJ36*;!EN,5O&HH[C78O;d)qM.rrgCN'Qn-L"irlXJH<36RV7:QqpGmJ
%#`RIcU?U)GkfP%gJkE#>8RQL<&4Eql]p/b1!#gEW3"_<)K"`c<FIZr_,Y#Rn$?-DrAAMN5HjCdkBbiB+OMUJj@]4eZ?GVXY?l4g5
%ME!mWSD'=LBgLh0<a#XmTdUL,)lTc;<F0bsoN'F+J33Sm?T)R/37%/c!&Yi$M^/c)S>/&pje,JT*6AN6-q'NdTIg]V%p4iE8<W#J
%/0Q*2I\hJNZ4?m[I0+CI#;'OoUZ4V!<F*5F6A9#RaA^a0!K@.T":j)%W(jWiWdFe&\BLA[7@"!oJ<p>:l6K1`NW`McGg;+%KH_8+
%(p)GVJ-lKmMH!jAJ94U$i4>r7*#JZ5!N`r6bWmYe^'mFq,n`XO?jNF&:gh'n5W'+'&4tD]GT)_HN't(^Mcj@$&54(C3%:m!G#B=S
%U>je%Mi,DN_&`R[5VmB`'R16JYq/iafL43<<D=@&6]F3q31LpThGbKY*<AR/C!s#kVA6TML:d(hOC\G&&0.&``;g2F:TE\9OOqIW
%!.uEK<)!N['<&8<r.cao@$SODJgSisA1fpbEF>"=bl@lm'&Z#W63IG>5Vjh=+nDg?cJQ!V!Z)bDA_GU8>Zc`&NWIib17D7BL(mGi
%<E]eA'aOS8)R3Ls?BDl,ON0e@KmcDRooPuc+>h'9MB`ZpV`(M1!_jl+">i9o#eun(2KCpE4c_BEVg8PB5__rUF&Mf>gq)dqOd4:P
%g<)_#KbF\!&?@]PoA\JG;&eT#21RLp(9\4giXA>7W!Q-u<1@P2G]QD2A@P0:"O/&:Fk6g0X:c6=d%p>npJr\"O[hAjQFQFT,LGGh
%_e#1JLk<C*$s)A'.s.YmM2$d<CM!2`>ZLF)nmDDH_\Et=Y+rc2`I1mj?a]0+s%:D"J-<f%-ld4f^M,MCiAX"p+IH?KTOiEPET@r4
%W,qtqL>NDoB4W/\gV`gP38=EA>@=@5#8$`C6)OTFZ^</(~>
%AI9_PrivateDataEnd
