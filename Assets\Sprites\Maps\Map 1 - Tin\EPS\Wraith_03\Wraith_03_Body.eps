%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Body.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 128 128
%%HiResBoundingBox: 0 0 128 128
%%CropBox: 0 0 128 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 2 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
128 128 li
128 0 li
cp
clp
39.9175 46.0127 mo
38.6968 50.2861 55 73 9.00001 120 cv
30 120 50.4287 100.055 59.9175 96.0127 cv
60.5596 98.0615 59.6958 104.877 59.9175 107.013 cv
87 79 84 89 88 111 cv
108 91 91 62 115 53 cv
117.484 31.04 102.262 14 75 14 cv
50.187 14 43.9175 32.0127 39.9175 46.0127 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
.651301 .62591 .384436 .281712 cmyk
f
23.0234 112.019 mo
38.5738 93.5135 44.3708 78.9385 46.1142 68.1259 cv
52.2791 60.5443 60.7577 51.1945 68.7349 50.6133 cv
60.5504 41.2099 70.2465 26.7387 73.2325 19.0345 cv
73.8089 19.0116 74.3981 19 75 19 cv
80.4037 19 85.3992 19.748 89.8248 21.1829 cv
90.2659 25.9507 91.8278 33.3656 91.4238 40.1156 cv
86.3337 36.8325 80.6326 34.908 74.3792 34.908 cv
73.5948 34.908 72.8017 34.9383 72 35 cv
69 35 72 48 71 56 cv
68 56 67 53 64 52 cv
60.2816 65.0139 57.4399 78.6757 55.7542 92.4843 cv
53.033 93.9495 49.9295 96.0755 46.2163 98.6191 cv
39.6699 103.105 31.5522 108.667 23.0234 112.019 cv
.706416 .679713 .45539 .455665 cmyk
f
85 63 mo
78 69 73.8105 70.8682 69 78 cv
67.6016 74.252 62 73 62 69 cv
48.5981 79.0518 42.5127 96.3145 35.8628 111.259 cv
46.0591 105.356 54.6655 98.25 59.9175 96.0127 cv
60.5596 98.0615 59.6958 104.877 59.9175 107.013 cv
80.1836 86.0508 83.604 86.377 85.7183 97.0859 cv
86.5967 87.0254 86.5894 72.9336 85 63 cv
cp
level3{
gsave
clp
[-17.936 32.8405 -32.8405 -17.936 76.2698 73.9937 ]ct
/0 
<<
/ShadingType 2 
/ColorSpace /0 /CSA get_res
/Coords [0 0 1 0 ]
/Domain [0 1 ]
/Extend[ true true]
/Function
<<
/Domain[0 1 ] 
/FunctionType 3
/Functions [
<<
/Domain[0 1 ] 
/Range[0 1 0 1 0 1 0 1 ] 
/FunctionType 0
/Order 1 
/DataSource <~=f;<#>H%T&>cRi)?*"#+@''D/@^#h4A[)48B<qR<Bs[j?C:+$ACpsBED7KWHDn5oKE4Z)MEkMGQF2%\T
Fhn%XGJX=[Gf'L]H,K[_HH#pbI)l9fI`VQiJB@ilJ]e#nJ^"/pK$ODsK$XJtK@0`"K[]u%L=H8(Lt2P+
MqA"0NS4@4OP9j;Phc]IQei>US)5+dT&1[pU>I@)Uu3g5VVj6AWSf]MX5H)ZXl)GeYMV_qYhqu(ZJJ84
[,"M>[G4\J[b=eU\(Oq`\^t.l]%(8"]@(8+^!UP7^<UP@^<:JG^;k>O^r(GZ_8(Jd_7P8j_R>2s_QSm#
~>
/BitsPerSample 8 
/Encode [0 63 ]
/Decode [0 1 0 1 0 1 0 1 ]
/Size [64 ]
>>
]
/Bounds []
/Encode [0 1 ]
>>
>>/Gradient add_res /0 /Gradient get_res clonedict shfill grestore
}if
np
36.9531 113.82 mo
36.2646 113.82 35.5654 113.678 34.897 113.376 cv
32.3809 112.238 31.2627 109.276 32.3999 106.761 cv
33.1499 105.101 33.895 103.41 34.6475 101.703 cv
40.3398 88.791 46.7915 74.1563 59 65 cv
60.5161 63.8633 62.5425 63.6797 64.2358 64.5283 cv
65.8369 65.3281 66.8804 66.918 66.9902 68.6885 cv
67.3013 69.0518 68.0723 69.6709 68.6035 70.0967 cv
68.7588 70.2217 li
71.2441 67.5146 73.7368 65.5791 76.4751 63.4521 cv
78.1147 62.1797 79.8105 60.8623 81.7461 59.2041 cv
83.1206 58.0244 85.0239 57.6836 86.7227 58.3057 cv
88.4219 58.9297 89.6514 60.4229 89.937 62.21 cv
91.5425 72.2422 91.5659 86.0684 90.8774 95.3203 cv
90.6719 98.0752 88.2642 100.127 85.52 99.9355 cv
82.7661 99.7305 80.6997 97.332 80.9048 94.5781 cv
81.3477 88.6289 81.478 80.2549 80.9927 72.6123 cv
77.9443 75.0166 75.6865 77.0283 73.145 80.7959 cv
72.0981 82.3486 70.269 83.1836 68.4106 82.9648 cv
66.5508 82.7441 64.9702 81.5029 64.3154 79.748 cv
64.168 79.3584 63.0278 78.4434 62.3467 77.8975 cv
61.8813 77.5244 61.3643 77.1094 60.8398 76.6406 cv
53.2427 84.3135 48.4526 95.1787 43.7979 105.736 cv
43.0327 107.473 42.2749 109.191 41.5122 110.878 cv
40.6772 112.727 38.8574 113.82 36.9531 113.82 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
44.8897 46.8144 mo
44.9644 47.2432 45.123 47.9434 45.2568 48.5313 cv
47.1504 56.8691 51.8804 77.6787 23.0234 112.019 cv
31.5522 108.667 39.6699 103.105 46.2163 98.6191 cv
50.9707 95.3623 54.7256 92.79 57.958 91.4131 cv
59.2568 90.8584 60.7295 90.8809 62.0117 91.4727 cv
63.294 92.0635 64.2666 93.1699 64.6885 94.5176 cv
64.7417 94.6865 64.7896 94.8604 64.832 95.039 cv
74.3867 85.9766 79.0347 83.8037 83.6279 85.2685 cv
88.2724 86.75 89.7915 91.3183 90.9321 97.8037 cv
93.4043 91.9414 94.106 85.623 94.793 79.4365 cv
96.0185 68.4053 97.395 56.0029 110.244 49.6172 cv
110.525 41.8506 108.212 35.0176 103.481 29.7207 cv
97.3061 22.8076 87.1914 19 75 19 cv
54.792 19 49.1426 32.0869 44.8897 46.8144 cv
9.00001 125 mo
6.98732 125 5.1709 123.793 4.39112 121.938 cv
3.61083 120.083 4.01904 117.941 5.42677 116.503 cv
41.9619 79.1729 37.271 58.5215 35.5054 50.7461 cv
34.9756 48.415 34.5571 46.5733 35.1099 44.6396 cv
35.1099 44.6387 li
38.6684 32.1836 45.2925 9.00001 75 9.00001 cv
90.0776 9.00001 102.841 13.9932 110.939 23.0586 cv
118.083 31.0557 121.289 41.8887 119.968 53.5625 cv
119.757 55.4297 118.516 57.0215 116.755 57.6816 cv
107.299 61.2276 106.129 67.959 104.732 80.541 cv
103.574 90.9648 102.133 103.938 91.5356 114.535 cv
90.2207 115.851 88.2847 116.328 86.5093 115.772 cv
84.7339 115.218 83.4131 113.725 83.0806 111.895 cv
82.5635 109.05 82.1597 106.396 81.8037 104.056 cv
81.3345 100.972 80.7739 97.29 80.1694 95.3486 cv
77.9624 96.5596 73.291 100.373 63.5122 110.488 cv
62.1606 111.886 60.1231 112.373 58.2851 111.738 cv
56.4477 111.104 55.145 109.462 54.9443 107.529 cv
54.8667 106.782 54.8657 105.917 54.9062 104.798 cv
53.938 105.451 52.9219 106.147 51.8687 106.869 cv
40.7007 114.521 25.4063 125 9.00001 125 cv
f
8.11671 58.5147 mo
14.4995 63.4404 32.8535 59.3633 40.7339 53.6035 cv
41.7725 54.4277 37.73 64.6025 42.9307 72.1074 cv
49.106 64.3281 59.273 51.3027 68.7349 50.6133 cv
54.2021 33.916 96.0488 1.24805 56.5923 18.8398 cv
33.9443 28.9375 36.5019 56.4483 8.11671 58.5147 cv
.651301 .62591 .384436 .281712 cmyk
f
14.1746 60.5766 mo
11.7018 60.3092 9.59026 59.6519 8.11671 58.5147 cv
8.11671 58.5147 li
9.58969 59.6514 11.7024 60.3088 14.1746 60.5766 cv
.28455 .2121 .220493 .0271611 cmyk
f
56.5923 18.8398 mo
56.9191 18.6941 57.2397 18.5521 57.5554 18.4133 cv
57.239 18.5524 56.9198 18.6938 56.5923 18.8398 cv
.781125 .709987 .614252 .890623 cmyk
f
16.9552 60.7181 mo
15.9905 60.7181 15.0593 60.6723 14.1746 60.5766 cv
11.7024 60.3088 9.58969 59.6514 8.11671 58.5147 cv
36.5019 56.4483 33.9443 28.9375 56.5923 18.8398 cv
56.5923 18.8398 li
56.9198 18.6938 57.239 18.5524 57.5554 18.4133 cv
64.9324 15.1692 69.2993 13.792 71.6691 13.792 cv
71.7658 13.792 71.8596 13.7943 71.9497 13.7988 cv
62.7691 22.1543 54.5517 31.3623 49.4214 42.7207 cv
49.5928 41.3164 48.3603 39.7422 49.3188 37.7217 cv
44.2471 43.0058 40.8042 49.0644 37.7515 55.4453 cv
32.0368 58.4948 23.6788 60.7181 16.9552 60.7181 cv
.706416 .679713 .45539 .455665 cmyk
f
32.3721 52.4785 mo
34.4111 51.6465 36.2739 50.6699 37.7827 49.5674 cv
39.5991 48.2383 42.0791 48.2881 43.8413 49.6865 cv
46.3804 51.7012 45.9854 54.6367 45.6367 57.2266 cv
45.4736 58.4375 45.2803 59.875 45.2212 61.3877 cv
49.8901 55.9658 55.4146 50.4648 61.374 47.6035 cv
58.5283 38.7305 63.2051 28.8242 66.5205 21.7998 cv
66.8081 21.1895 67.1265 20.5156 67.4375 19.8408 cv
65.4668 20.5156 62.6436 21.6162 58.6284 23.4063 cv
50.1157 27.2021 45.6421 33.96 40.9067 41.1143 cv
38.3872 44.9199 35.7378 48.9209 32.3721 52.4785 cv
cp
46.0122 76.0449 mo
45.0986 76.7598 43.9531 77.1426 42.769 77.1045 cv
41.187 77.0527 39.7227 76.2559 38.8213 74.9551 cv
35.8853 70.7188 35.1787 66.0566 35.2021 62.1055 cv
25.1323 66.04 11.7549 67.6377 5.06201 62.4736 cv
3.4209 61.207 2.73096 59.0596 3.32861 57.0742 cv
3.92578 55.0889 5.68506 53.6777 7.75293 53.5273 cv
21.353 52.5381 26.3013 45.0596 32.5679 35.5938 cv
37.6377 27.9365 43.3823 19.2568 54.5557 14.2725 cv
66.7852 8.82031 73.2866 6.92188 77.1572 10.877 cv
80.9194 14.7217 78.4375 19.9805 75.563 26.0693 cv
71.415 34.8564 68.3213 42.5234 72.5063 47.3311 cv
73.7495 48.7598 74.0791 50.7676 73.3574 52.5186 cv
72.6362 54.2705 70.9868 55.4639 69.0981 55.6006 cv
61.998 56.1162 52.3638 68.2607 47.189 74.7852 cv
46.8472 75.2158 li
46.5996 75.5273 46.3193 75.8047 46.0122 76.0449 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
121.256 63.1103 mo
116.286 66.7363 109.332 65.2646 105.169 58.5947 cv
104.356 59.207 105.289 69.4238 100.657 75.665 cv
97.9395 67.3643 93.5 53.4932 87.4639 51.3467 cv
99.6934 37.9307 77.9331 .553696 100.652 23.3604 cv
113.692 36.4521 113.471 46.9248 121.256 63.1103 cv
.651301 .62591 .384436 .281712 cmyk
f
94.4233 59.3799 mo
92.4973 55.5998 90.2842 52.5472 87.7937 51.4762 cv
90.2842 52.547 92.4975 55.5985 94.4238 59.3789 cv
94.4236 59.3793 94.4235 59.3795 94.4233 59.3799 cv
87.7925 51.4757 mo
87.7904 51.4748 87.7883 51.4739 87.7862 51.473 cv
87.7884 51.4739 87.7903 51.4748 87.7925 51.4757 cv
87.7844 51.4722 mo
87.7823 51.4713 87.7805 51.4706 87.7785 51.4697 cv
87.7803 51.4705 87.7826 51.4714 87.7844 51.4722 cv
87.7762 51.4687 mo
87.7742 51.4679 87.7724 51.4671 87.7705 51.4663 cv
87.7723 51.4671 87.7743 51.4679 87.7762 51.4687 cv
87.7682 51.4653 mo
87.7662 51.4645 87.7642 51.4636 87.7621 51.4628 cv
87.7643 51.4637 87.766 51.4644 87.7682 51.4653 cv
87.7549 51.4597 mo
87.7538 51.4592 87.7527 51.4588 87.7516 51.4583 cv
87.7527 51.4588 87.7538 51.4592 87.7549 51.4597 cv
87.7465 51.4562 mo
87.7454 51.4557 87.7442 51.4552 87.7431 51.4548 cv
87.7442 51.4552 87.7454 51.4557 87.7465 51.4562 cv
87.7382 51.4527 mo
87.7371 51.4523 87.7359 51.4517 87.7348 51.4513 cv
87.7358 51.4517 87.7371 51.4523 87.7382 51.4527 cv
87.7297 51.4492 mo
87.7288 51.4488 87.7276 51.4483 87.7266 51.4479 cv
87.7274 51.4482 87.729 51.4489 87.7297 51.4492 cv
87.721 51.4456 mo
87.7201 51.4452 87.7194 51.4449 87.7184 51.4445 cv
87.7195 51.4449 87.7199 51.4451 87.721 51.4456 cv
87.7121 51.4419 mo
87.7115 51.4416 87.7107 51.4413 87.7101 51.4411 cv
87.7104 51.4412 87.7117 51.4417 87.7121 51.4419 cv
87.693 51.4341 mo
87.6928 51.434 li
87.693 51.4341 li
87.6846 51.4306 mo
87.6841 51.4305 87.6838 51.4303 87.6833 51.4301 cv
87.6837 51.4303 87.6842 51.4305 87.6846 51.4306 cv
87.6761 51.4272 mo
87.6756 51.427 87.675 51.4268 87.6746 51.4266 cv
87.6753 51.4269 87.6754 51.4269 87.6761 51.4272 cv
87.6675 51.4237 mo
87.667 51.4235 87.6664 51.4233 87.6659 51.4231 cv
87.6667 51.4234 87.6667 51.4234 87.6675 51.4237 cv
87.6586 51.4202 mo
87.6583 51.4201 87.6578 51.4199 87.6575 51.4198 cv
87.6579 51.4199 87.6582 51.4201 87.6586 51.4202 cv
87.6228 51.4061 mo
87.6227 51.406 87.6225 51.4059 87.6223 51.4058 cv
87.6228 51.4061 li
87.6142 51.4027 mo
87.614 51.4026 87.6136 51.4025 87.6135 51.4024 cv
87.6142 51.4027 li
87.6052 51.3992 mo
87.6051 51.3991 87.6051 51.3991 87.6049 51.3991 cv
87.6052 51.3992 li
87.5612 51.3824 mo
87.5609 51.3822 87.5609 51.3822 87.5606 51.3821 cv
87.5612 51.3824 li
87.5527 51.3792 mo
87.5524 51.379 87.552 51.3789 87.5517 51.3788 cv
87.5527 51.3792 li
87.5438 51.3758 mo
87.5437 51.3758 87.5432 51.3756 87.5431 51.3756 cv
87.5438 51.3758 li
87.5084 51.3627 mo
87.5079 51.3626 87.5076 51.3625 87.5071 51.3623 cv
87.5075 51.3624 87.508 51.3626 87.5084 51.3627 cv
87.5 51.3597 mo
87.4994 51.3594 87.4987 51.3592 87.498 51.359 cv
87.4987 51.3592 87.4993 51.3594 87.5 51.3597 cv
87.4917 51.3567 mo
87.4909 51.3564 87.4901 51.3561 87.4893 51.3558 cv
87.49 51.356 87.491 51.3564 87.4917 51.3567 cv
87.4832 51.3536 mo
87.4824 51.3533 87.4815 51.353 87.4807 51.3527 cv
87.4818 51.3531 87.4821 51.3532 87.4832 51.3536 cv
87.4745 51.3505 mo
87.4737 51.3502 87.4731 51.35 87.4723 51.3497 cv
87.473 51.3499 87.4737 51.3502 87.4745 51.3505 cv
87.4773 51.3319 mo
87.4833 51.3254 87.4883 51.3198 87.4943 51.3132 cv
87.4883 51.3198 87.4833 51.3254 87.4773 51.3319 cv
87.5029 51.3036 mo
87.517 51.288 87.5299 51.2736 87.544 51.2579 cv
87.5299 51.2736 87.517 51.288 87.5029 51.3036 cv
87.5505 51.2506 mo
87.552 51.2489 87.5526 51.2483 87.554 51.2467 cv
87.5526 51.2483 87.552 51.2489 87.5505 51.2506 cv
90.3297 46.2856 mo
90.3302 46.2841 li
90.3297 46.2856 li
90.3313 46.2808 mo
90.9643 44.3653 91.2945 42.2755 91.4238 40.1156 cv
91.424 40.1157 li
91.2947 42.2756 90.9643 44.3653 90.3313 46.2808 cv
.706416 .679713 .45539 .455665 cmyk
f
91.424 40.1157 mo
91.4238 40.1156 li
91.8278 33.3656 90.2659 25.9507 89.8248 21.1829 cv
89.8254 21.1831 89.8253 21.1831 89.8259 21.1833 cv
90.2674 25.9512 91.828 33.3669 91.424 40.1157 cv
.745937 .710445 .503075 .593713 cmyk
f
100.652 23.3604 mo
100.63 23.3381 100.609 23.3173 100.587 23.2951 cv
100.609 23.3173 100.63 23.3381 100.652 23.3604 cv
100.553 23.2617 mo
100.549 23.2575 100.546 23.2541 100.542 23.25 cv
100.546 23.2541 100.549 23.2575 100.553 23.2617 cv
89.8259 21.1833 mo
89.8253 21.1831 89.8254 21.1831 89.8248 21.1829 cv
89.5412 18.1188 89.7203 16.1476 91.1818 16.1478 cv
89.7211 16.1487 89.5421 18.1186 89.8259 21.1833 cv
91.2072 16.148 mo
91.1989 16.1479 91.1902 16.1478 91.1821 16.1478 cv
91.1821 16.1478 li
91.1902 16.1478 91.1989 16.1479 91.2072 16.148 cv
.781125 .709987 .614252 .890623 cmyk
f
115.834 65.0195 mo
111.497 55.6777 106.05 46.8379 101 38 cv
101 46.0845 97.222 52.5502 94.4238 59.3789 cv
92.4975 55.5985 90.2842 52.547 87.7937 51.4762 cv
87.7933 51.476 87.793 51.4759 87.7925 51.4757 cv
87.7903 51.4748 87.7884 51.4739 87.7862 51.473 cv
87.7856 51.4727 87.785 51.4725 87.7844 51.4722 cv
87.7826 51.4714 87.7803 51.4705 87.7785 51.4697 cv
87.7778 51.4694 87.7768 51.469 87.7762 51.4687 cv
87.7743 51.4679 87.7723 51.4671 87.7705 51.4663 cv
87.7697 51.4659 87.769 51.4657 87.7682 51.4653 cv
87.766 51.4644 87.7643 51.4637 87.7621 51.4628 cv
87.7597 51.4617 87.7572 51.4607 87.7549 51.4597 cv
87.7538 51.4592 87.7527 51.4588 87.7516 51.4583 cv
87.7498 51.4576 87.7482 51.4569 87.7465 51.4562 cv
87.7454 51.4557 87.7442 51.4552 87.7431 51.4548 cv
87.7415 51.4541 87.7398 51.4534 87.7382 51.4527 cv
87.7371 51.4523 87.7358 51.4517 87.7348 51.4513 cv
87.733 51.4505 87.7315 51.4499 87.7297 51.4492 cv
87.729 51.4489 87.7274 51.4482 87.7266 51.4479 cv
87.7247 51.4471 87.7229 51.4464 87.721 51.4456 cv
87.7199 51.4451 87.7195 51.4449 87.7184 51.4445 cv
87.7164 51.4436 87.7142 51.4427 87.7121 51.4419 cv
87.7117 51.4417 87.7104 51.4412 87.7101 51.4411 cv
87.7044 51.4387 87.6988 51.4364 87.693 51.4341 cv
87.6928 51.434 li
87.6901 51.4329 87.6873 51.4317 87.6846 51.4306 cv
87.6842 51.4305 87.6837 51.4303 87.6833 51.4301 cv
87.6809 51.4292 87.6785 51.4282 87.6761 51.4272 cv
87.6754 51.4269 87.6753 51.4269 87.6746 51.4266 cv
87.6722 51.4256 87.6698 51.4247 87.6675 51.4237 cv
87.6667 51.4234 87.6667 51.4234 87.6659 51.4231 cv
87.6634 51.4221 87.6612 51.4212 87.6586 51.4202 cv
87.6582 51.4201 87.6579 51.4199 87.6575 51.4198 cv
87.6459 51.4151 87.6345 51.4106 87.6228 51.4061 cv
87.6223 51.4058 li
87.6196 51.4048 87.6169 51.4037 87.6142 51.4027 cv
87.6135 51.4024 li
87.6108 51.4013 87.6079 51.4002 87.6052 51.3992 cv
87.6049 51.3991 li
87.5904 51.3935 87.5758 51.3879 87.5612 51.3824 cv
87.5606 51.3821 li
87.558 51.3812 87.5552 51.3801 87.5527 51.3792 cv
87.5517 51.3788 li
87.549 51.3778 87.5465 51.3768 87.5438 51.3758 cv
87.5431 51.3756 li
87.5316 51.3713 87.5199 51.367 87.5084 51.3627 cv
87.508 51.3626 87.5075 51.3624 87.5071 51.3623 cv
87.5047 51.3614 87.5024 51.3605 87.5 51.3597 cv
87.4993 51.3594 87.4987 51.3592 87.498 51.359 cv
87.4959 51.3582 87.4938 51.3574 87.4917 51.3567 cv
87.491 51.3564 87.49 51.356 87.4893 51.3558 cv
87.4872 51.355 87.4853 51.3544 87.4832 51.3536 cv
87.4821 51.3532 87.4818 51.3531 87.4807 51.3527 cv
87.4786 51.3519 87.4766 51.3512 87.4745 51.3505 cv
87.4737 51.3502 87.473 51.3499 87.4723 51.3497 cv
87.4696 51.3487 87.4666 51.3476 87.4639 51.3467 cv
87.4639 51.3467 li
87.4684 51.3418 87.4728 51.3368 87.4773 51.3319 cv
87.4833 51.3254 87.4883 51.3198 87.4943 51.3132 cv
87.4965 51.3107 87.5007 51.3061 87.5029 51.3036 cv
87.517 51.288 87.5299 51.2736 87.544 51.2579 cv
87.5462 51.2555 87.5483 51.253 87.5505 51.2506 cv
87.552 51.2489 87.5526 51.2483 87.554 51.2467 cv
88.8223 49.8232 89.7177 48.1365 90.3297 46.2856 cv
90.3302 46.2841 li
90.3306 46.283 90.3309 46.2819 90.3313 46.2808 cv
90.9643 44.3653 91.2947 42.2756 91.424 40.1157 cv
91.828 33.3669 90.2674 25.9512 89.8259 21.1833 cv
89.5421 18.1186 89.7211 16.1487 91.1818 16.1478 cv
91.1821 16.1478 li
91.1902 16.1478 91.1989 16.1479 91.2072 16.148 cv
92.7057 16.1706 95.5324 18.2326 100.542 23.25 cv
100.546 23.2541 100.549 23.2575 100.553 23.2617 cv
100.564 23.2727 100.576 23.284 100.587 23.2951 cv
100.609 23.3173 100.63 23.3381 100.652 23.3604 cv
100.652 23.3604 li
113.692 36.4521 113.471 46.9248 121.256 63.1103 cv
121.256 63.1103 li
119.609 64.3115 117.743 64.9414 115.834 65.0195 cv
.706416 .679713 .45539 .455665 cmyk
f
105.169 53.5947 mo
105.473 53.5947 105.778 53.6221 106.082 53.6787 cv
107.46 53.9346 108.668 54.7578 109.411 55.9473 cv
110.756 58.1025 112.503 59.5068 114.347 59.918 cv
112.933 56.5352 111.802 53.4082 110.764 50.5361 cv
107.612 41.8193 105.124 34.9346 97.1094 26.8887 cv
96.4717 26.249 95.8906 25.6787 95.3604 25.1689 cv
95.3623 25.1846 li
96.3994 32.6963 97.7515 42.4912 94.1982 50.1211 cv
96.3569 52.4229 98.2119 55.5723 99.8916 59.2383 cv
100.122 57.3408 100.583 55.79 102.161 54.6006 cv
103.034 53.9434 104.092 53.5947 105.169 53.5947 cv
cp
100.658 80.665 mo
100.392 80.665 100.124 80.6436 99.8555 80.6006 cv
98.0107 80.3008 96.4868 78.9971 95.9053 77.2207 cv
95.7549 76.7607 li
94.1836 71.958 89.4014 57.3428 85.7886 56.0576 cv
84.2114 55.4971 83.019 54.1836 82.6133 52.5596 cv
82.207 50.9355 82.6411 49.2158 83.7686 47.9785 cv
87.8032 43.5527 86.3911 33.3223 85.4565 26.5518 cv
84.4961 19.5967 83.7378 14.1035 88.4067 11.7881 cv
93.2256 9.40137 98.1973 13.8115 104.194 19.832 cv
113.773 29.4482 116.88 38.04 120.168 47.1357 cv
121.727 51.4473 123.338 55.9043 125.762 60.9434 cv
126.808 63.1172 126.152 65.7275 124.203 67.1494 cv
120.805 69.6289 116.679 70.5586 112.594 69.7656 cv
111.295 69.5146 110.037 69.0977 108.834 68.5273 cv
108.167 71.8652 106.97 75.5488 104.672 78.6445 cv
103.72 79.9277 102.225 80.665 100.658 80.665 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
[/Gradient [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Body.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:I-%N&`7D)=BNItrEK;4?i!9;R`NPk_qn@j2Dk;COlM9ai^O5q@g%0-s4hZ.:k3]WB^,g]Bo_$b\T)K=NoCV>B
%cX4D6o9ZN<bGW\c%DHoYC&[Dk`QRGRDr/3SqX1@g4!M\Jkf^a:(;9qei6Ofp?iBU3OFI0+h)jg*e4o>%IeN[=4gtaYCDX;N"b4Ur
%QOH*'QXfF0miS-Miidh)PBcctT@BqAr=eoMXQ0>4-Yff\<VW9;mUO0W6>7<QJcGVqn>d;=>:P$2i4%J9HAaNQNqKb_!]5aNrTYce
%eZ6IbM7YhJ,ih)A8hkIi\VKB@ZA3j_LI6TiII[#Q%bZ-o=nU&8BMW7G:Nqjqf5=A#QXWY`6S.)RVb;]@]aN>TA<6W?4*L?Z`)^'g
%a$]LO'phraLOT,N4YO?lBNlQSpmNmaX7n.t;ro^=1cRLlg1b@Be5Yt=.DRkUN_h4c-*iTWdi;n7jZ9i7,.8GZY<Lk\4+=H^@(B\&
%llLWEBc]ks!NY]Q8,T(4Pb<q!r5W/-s5T#Uhn=I7m_t[Is%M<4r3Ysfqt9Q!T(4OBRJlp'6hqo'o'6/N%o9req2SP:eT_FMI.sBd
%DYt=.+QZi((3K4!d+=g&c_VLFb7fs:NQ8IliQpI4?6sLPQbPXm)hB(ZmODfCFc\9O9`mr^+a&tAf%W.@]R'GJhnIq$`V@554Fd%K
%bGXg#dmc"T3l=kss-eDf7q)#;@o#G\0]H/0C(/^uZ=.eDT.RlDjOq?kT)\W*o=t4ul@2!IqXsHh_?LKghu3Djrs.:!3s0tcr2]9m
%5(EI!f.YuHDn2UfoCT@J?U'#H:QM^:^3iO8@2]:F^HD#s:Z)0Aq)dMEDdImJe+D\$k4Ej^s6eFne*inR]COCk0>;`2qB+SlQ9+jF
%2VO@V_c=BFYaR^mh<ks%-+6XT_qH6X\5\B/?k$:``Td7GE3N]^IIY>oYCHJgVZN.H,2DSShnJl30Xht')ZPl`^2DDlY(*7VnF#c%
%4b'TipYi1%cR(R7Y(-.\D#<Aff;CR2E"]n(Ii`#;]EZs_o$&9<\)SS&4/h/NDrbJcnC>kB^_^=i`ekj&qDnSYIJ(dMhn^uGrp?3D
%^A7$Z5J2:>k_Fqshu([hX1K1OT3q+AYNWG[])(AM7IU4)k/`$eq'Y)un0c!nTI9M5J<o0_>,_Suc;23(rcFYDf2#b$o:u&er9JiZ
%rpJI35,iD%ihK]WmIOuZhRnR(r:SSnroIMG+4PJ$g[Y1HgHo(uFqad;#Q4N8rutJ=KcaYqq!SX`X<!#+XhBL3%bAd,SJCoPrDW`F
%aS16!_Ea%er[GForP<q_2u8h'^.=ERJA7#&I(J=KDI3A=q9(_S@/F3r6TX_[QVUsQI610ga1)(5i6*ga2/ZIsX8niCUeA_P;tN;I
%AaIM0GJAK$T7I#j"1.XZc^PC6h?Tob4fG?Xn,<G&f(7L#J,AK*j"#VA7Krh;qE,8'*H[-BH:Z(O:LCh`n]/+7^\Q?:B33-_hgPC"
%X+,Bck2g7>A=-t#B.?ZG/]gK2Qgm+p1<oTTm)BsBoQG;*WOpg;#;Mk>?iA0;QS6($2-<U[/hhU01j3o%h@M3qfB0*k7q)O$'*n"h
%)YBSZ^[P<WR8C#:T>7^&6H]j?daP\o?9ET1r7BhUqsO9tp)9MQDdWP(26X%+3,'NG#$5neg_boq-3as,ma)3`b($*dZ#>'ID'VVG
%%C.YipQR0*nf!k(q_MDYl^.+P,,Tcas/t,n4EU@PFLrmUmIJ7@'3*fAJR9%)QR;__G#Pj6e]l]!5c2)2=Pir!YQ+/-"LkuZs72a[
%#q,Q[04'8@EqSbe?G(X16CM6k8((=CL4VF'RLU2qBPOp+*Yn^$oH8Veo(=Ou5C)hp;7ns(T&o:QJ,Am((Ef=L(qNU219%L./$P<H
%9ii2,>2a,X$N2+dbY3_.J>*L&iu`Lo48pEf"<+L7jnSGu8':5hlc5(cnaWCSqsgH:TcEWcapH3.apjTiih6&Z$B/-0:eI]d3g9hu
%%TJHb:/oO#VVF:_<,Ss?#6iWQ)PBJJWFe!:<;\R&?-8i^Tq(D9apjTik,A%Z)B&K:9!Ar)Um@toWYRgSG(amNkB><lgMM/jpXg.l
%D!@V9p""ISp/=3MRso^'#Cd#78:M8:$nhgU3j_7=*"GIg-Re/qc/BG]d6[<C!$r]8&X7Y?57JeH83Jj]cjWp)T+dL]nOEN?EW_$q
%IuEFL:AtC=+TA!_V@p*qIe<ZeX?k1$o^8sK+n^uo2trG\3V?5G.(t94+/G7W6g`-mrDSB4W+;?=R']eUdSC)s:^-LkGf%cEapjTi
%Z_QNR"=f680"<+%;<__>%2p5_qY>NQ3Q'"Vs$O>nU45P@EDen5pff3:p[iD$(=o0:StYj&r;R&iEP!6;;!CcQhPG:H^3]Y9n1@DE
%cm=(K,J8#mEC_c53'o<70P2[N`ss>QiuJW=3c".>J,WBt(gI-^d:)S$\6BIoegFgPpBOeg\&!h219cFlb;.k+gZUp-9>HF#KFJ7r
%oc.o09le?'+K(`%WX1R9Cs*JCQ^\,QR9fM(KSQ(J/Kn&$%RutHGa9_K`Vc_RO#dYU$G_Mf=3,lImlZ%uDhuFV8'/\:H9;,8R^M\U
%=J3Y6,sk0Uj$<\3]YUZj?sZVcZd*n>I0LJE?uD%0X`*,J34f>ro+<l?c>A5K_O6,*KUKMJ!Tpc)Cr0FhZnH'"gDs%[>FMl"ZZX33
%Jo`0a$lsl7i+Wa9?4Ob_DMaS?hLLM+J:l_\muG2gOW0l%2`b``'dc$"VV?>7DZ-]L\\5P)/UmiAG.?cq%1\d-a'=58(6D0#?T8EX
%:bGSarWcb\49#NY548=Nm/7.#^VKihh&h_[_qmmYIY-C*JGnA5E>@C3$+OmR:@-p"\,d@d!_*P>i8Pc'?/_m,E9?*Cit&<M_MNAh
%pH$1GFZ?9KS6-Xe5jSO.gqd]J"&/7gbkhC@Zo,4UB/fp%K>!$&A\Ed042\QK@IuU>+asc7P6>N;J<=uY%rPd>:GC?)3m0%4I"iXD
%@M?t<bI;HiT4(c.emN-5R%k!M/]3#>&J=`.9=M,=cS18IfU"`YfV8WD.XjYDK"^L0/aVLR*#HY?JGF,f$4<j<3%891"BZNogqs%T
%_k@,_;J1u41Ub86I$0_-,tRQ(S/1<-(RX!uO7<2!="YjNEZh63PqJn)WE+ljkQ',R?-uHT&u9b&NXLdLQQ%Gf.Bmo%7-Ia47k"Zn
%?&AqL;+)iM9.j^>ZlAM.0oe*9>eCfl"iallN>+C;_o^%uaoUZ1rstr!+n<bLg?n8:*3LY#(Cmnp`3#*THa`NRY:]J)Vscb#C\-E[
%%/UP.g9gH(m(3e^Fg1^ZhgOFc]<LI+*QYCQpDhF]])'6Ea1%iI4M]M1Y!!cB%XiRc6dP:(nA[.=TM&g>jdj<ccr'>`VcC3L.["9g
%CE(<4Z+G@39QuI@)Ci9X]s!.FDAdp]3qgoRDOmMs)r-;1GpnEIq#0lN_>]jf_>YYtV3NggC%32Z9&W?@_ilL(U`Pt^kXFP>U+!%A
%#Rc`Y`@`@eOd?"V$XtB=Z%<fa,YOpUig(JI+_bAK@X'DtRPRAdOiUS%Yk$E1cU$&F;?0#]6Nti#kR`HoB2SpAL_1B14">pY!T$)!
%iJEdd;@aQN,--NfFPgh*R&XMhJ,uuR'hf?1<kDJm;DeiHXHbh*QS.C(;":n-<V9JM].oQb'g=%M5f6Dn=,Pn:qK5a6B(a['5>XRI
%!<plfeA%[WAl/P.$1+Af*[fK6PWpao"@<,9n#bNiKBJpXKR$H'H1Frm*6KETL?G6C5(4bL+J3>0:4COqTV,![('5I>3*\#g48P",
%`C`W9QV<*ELe($<TOSB?1-3SI0Y8K&oL)4'Om;:RYee]kr@#$E&tS](W2657T&ch[0p;c8548'Ydj%Wu4]80Qi7"Gs&&3,^bm30S
%"O?#)*J1NX%^`t$#N=N:pqFY-`)&E)+*\d.KY:OHqbXP9+nD&*GI%kSQ@dsEim#?r&EtNM%VdgTDA(o%FB]#e.]<dWD$X`#N5:Wm
%@[N!Z6gPG*CPZ)+4TgD<cq@l8YS-q[.%m>dJrP[%(E"hFgoe^"E%7"/+eqF`$Oh=ofD'Ds74ft$8WQd%f%!?fCF%JjI5:1%W%m!I
%g$Y$:I3Lblp5/2OfdjiWDJclHIUV23G74k8\n>j29`'R``?TAu%QfL@cFeL+GXNe2]RtG>Yo(TpB\l7dPAi/eQfm2#67)(IhG:_X
%?r(VCY(oY(jYKO`74"=L+Ifu<fl_tI3389r3*<=@m`Kes<5B=p\!?@[6c?"sH)7No6<0J-WfTE;jS'?*;lEQ]!gh"ODYb6:_bO("
%DJ4="F.gOZl%)rkAiTG$>@Ld;70Y:&U\\Umed$"DP>/4n6Y%eB?lH2h&S12hMai(Eg9U"h4<RLg1@*brLGRt._T\FQ1UB:Zq^9*'
%LBAS26*,cBn:QKiV$!ZYr<^[6!?hK1Cf6ie+DW&)q`g*Q=%(@>n3dg_g$96kgC[#I7=RfD;>*DPljY"rP:$IY+r4N)*N$o%=E"EE
%;1'0$-8^SZU6*0jU?Z_AVE-cVOn%qFPf]-:`TkD$j:EP%-lA]=j=S.JW=,EmT]2Xd-"b3->])0[gN?(o%U?bP"hGJD=^CSn=fr6@
%+eXG6;6MmLj0VjIAeho1>r>qH?!ONR=>s@cQ"mA_YjnEl*t:+()cMrPXJJ0&jn/a,$IkkD)GK%5e]=Y#r\X;E-1drJ;3l<X*I3X\
%8t=M$2fDh35?SR_=XS(H8.M/rGfh#.[OD,1nsGikXG_DCCJK:jX;jZ-NHi.EBq='R4u6>I_!]D,+#C)6W>t2;b*.U[PA7Ar.&lb;
%dg0CbWsu-aFAil#28Kt0>_76p*pb?]oXfmEnt#pM6&g/fUeP!1QN`!g0hD`%kfj4,I]clgTX9[(abmVO<L4s3$\Tf>ZrG\XU8/J8
%]2\E#nLLX8:_YrfnjMjIIUPP\$YCaucAobr=Q=9GUfDReMrc+=\/+2Q;ZA-QDRUh&h9`3m7"dXZC>2IEX!Z&%'Bl/1Lo`Ep;j(7i
%;rNc'oci=nLf[9o9=48BW_]EUTaCsA9uD5h<U)jHLXi@rHp!Tj`G/Bq0(2?A@eK"[_f;32/@IUFh(dBh&SYm);7LLX)4kOBO.pk.
%%fiY;P6,oRs6"X3*&[tSm(Olb.Wm5[b-3Lt@GZY>p35hMX.X>dY&f%:96e\X4S)jkn2C0^Fd=OC`Hn_^]rckWAdrQX!UT%#.[\i(
%%B7de#52*6=9DFP!3@,?Z#pfN)IS%j>HE?'lLoQ'=0iG#6(nsEXjL[?2N'EHUt$rsG3NG'<fbV39cl.7[FhBi$h\>2?<X>[G3Z2R
%#l;'=<NCV*<8pAFD^HbIeR(g]n!OmH"0&kS8'-2CkN;aF29q*M[[_N`EDm@B5`MdMc_W3cSP#;BSr,<EMAIL>/Msc%mk$4c"e'E;;
%ek`<O_qCcA].39L^phRf[lhj"kE]0o(`V]TkL4f.c)4F2:8.IdRSP#-6uZ>Kl0MQC4*K`pDDoe02OO]*R0A*C!S<0?>k?E)ZTMgm
%c^80Sb]L]%f9s1m_p4Unl*;ug^`'\rkarM+EG$>k>d0EfeJ<jc48Q%kEU_"bk2X9.a-dh3ljW`6oS,<sL@gdB,T0j/]^=XSC@GQ$
%Wq3i<+Z9<XZFiaK(3qJ0s$jGX/Q#h:p@m7Mn4A'mWgOs=mE,.0QGZ(Jnsdb1M*#16I`GVnFMTYA<AXR`j0)!MLc:E<4Npr$WG+P)
%a147,V(bq(Y$P9bo*R[!<lfU[VT!r/-fg92mWNK/k->hV1hO6M<qe`*<1q7<RJ55=\6P>_qaOA]'B\dlR)@Cg*+E&1QP<^r=qet`
%g"]9$4a#/F'Q84/Vph&:,VsGgWRLsFjr$/P@-,YY-uhe7L<i;Wk^@JI*DH&hN`F:R>9ltocEo,4(u@2MnN1;#e")WXT:MOI]*!]l
%`!LS'JK8/bT'8MM%?;!.!D`8Teu]!dHkYhr;=%4lLD&>T>FZ>ZQ\L3.=I7O;7+MOH-4N&"=p`,*@+HQ)keL<RIUj4iPoX7J5q:m,
%BTj1q0J!5I+e;Zb!3n&:D95rNPFX.iNF3GVCcm.l5NU_lSrYlNrT(m:(tXt_&go7obPAphTVFLP<C]P2*I;;"5]m+-qbOVAG4Dhi
%DlKH)NAGKrjS=KSDsb^5Wj+q-VHBA(%nSZ)l*/qFX-cVKCh@-D%o7flg&+aFc!k8*E5\!7`lY9l;r/`n:+*TlIFCt+poRd0s4mba
%[I0K4h0X.LXh4se@GpXRb74Jkh8k^TQ]?_(HtOgi>V,UAj.-IL?+6'>YOU9d#uf[8\\2s.*E$_38dCTo)ms%b*2\0Jr;/QAqSTog
%omTl4Q6)C=e=hZ/>IG]mm;gRkDdL2-qWdn.=+:q4'dc6!UTQ9u$g;:I*rppf&ScUr0&V*WcCY5Sg?n[^_]u-jKFSKHWZC3Z\<V\6
%NX^/,d#l`>a>hh7ER1<ZgMIoKdue(Z`@W)E5lf"8BKZH-8(N[FJeRW8#:ELp_;gJ&('CobJWi(qZt;A];*;N<(7g":,ATK*GodhY
%S)]VC!ttkSN*XcRFp2c5-?b<K<nV::p.C:/fI<MpM2`u+JlsJ+9k'L6-rHC3#0EsOl2`X`9!f9eYgeB;AN%2[TB`4m%$O@f#bPZ`
%eBR:"nA@^]2>Dl=.KZWcr=n^>??g4?TK8"31M(`hLbT0NPZG$-)-*I#WlLp(ZP?2URSJmY@0.Du0U]MH*2,0_#0RSI&!Q!6S)K)o
%E$Bq+WX&/NjeU=`Q\ESb9)9M_U$Bdg66boDXqV/(LMDG(*ONcM#keON=6LYmd=?F)$4^Wt0MuODd7C&!NE[gX$&\j9HpDX&A->9;
%(E0q)RCLW+K1;sfakRd-&DDAH8RFu/<@,bA%95^0bA=*Yecu.=P.BjKU0/^L9i33q,[kcE!*((JLX0Xq@!I_W6.d(6JNSUE<8o5f
%PCD$'/W3q4?t7!EE2fKW;1Nus7+IKDr^=<t>2PumK%[?#r:^=7pdGJnd>rl8c3Dg3YWftt&[2e@6*2qZ58ZmjZ2CicW[NlH7hoo&
%3%50j1`?T*fO1l(*4#pC*'BBS=T9?EPJ]@"*,BZ_Gs^7F6e8J=;"6>pR3>#W6mI\$N>%,Cm7G=8V26GR(%$TH*#&qY[J+M/_I_".
%A@*3#/Dt1ZLr^Gp)CLWhk-LTr1H9gdd.okm1E"Y+/];%NmNU'YP6)0D\1UU`56C%r+3YUGm7./IpL)8kZs[`3eeo./.8=A%?*c<K
%VdaDqCk60J%f.fja3*K#R#]ACp0jE)W0(<f(FL'14_.fh4Pt,`+#esE9Yh;X4I'gI&;_-6mS]FjI]*A`ENid[RS\IE%%C@R:;k9!
%^B=QKA();?6U<Q%9HfGN-A-l<%#4-3"KnI1'pSbj"r&,eD`Vha"fh+NIOXc@0o-#o;3QP3:(/bJ-A$*=m/WUm4CA7$:gef="'0u]
%_rOf6d"'6jKc'*?WG=819gQ-lE7>4*\?TFF-\a0<;o@jjc"oi2WX\)"Z[0/#&o3o'1VCOO@_T%K\mAq7"nbglC`rYJ<!t3a]t2]A
%fc)V(8bQalJj(`.PWf!2_nZ)"W4%\s'[j-"("+:dgKj"+i%-09!N]<)WAmRr"`+Nq$FP*Hq6ss3!++(%r6l:Q#k`,mIj.K5"qu0O
%q$aH5p0XAB6K2$u>g#RXauJ>f8P&V4D&ZpF&o0*4'<GIe(>G+Ne3"^K:/[=*')?"$1Li1.q@\/[Q(*-CmJDc2e?=3i+c-_@^A!)L
%`pn=Nh,M6dfrOX15C<&-P35#Eq:hlgTc!t>bf)H`;Z;5KgIf2`,iNrfO0aM#jd&5UnZ`W<!E:ks8!O.^%sF*WjWl]#W.@2+1hDWX
%S>KbO7m.1?ZBJEl[+sBW3aeQ,c<(SN?:+HC9+mElL<Cil9N&u@,H%&4`?ZG%EC`/Qkc5R6G^l,-X,emF=C8#s]m/HG.t9W<:9333
%KE;d,hD!_iXFIa#EQ+M_I0Pl(&9*SD&(:eP$96'@K9FrJbYk*F`psth@Q0H0+:pB9OMo[<f"-u72:QK;`2M30=#&2;Xa9+dA4^MD
%Z5aHjZQ-B7Uej0Y85nj,[)9cF[rh>G*IR"p><)/-HZ$*2<\E(:.?!u..;%srs7h42!_aZ-j/3ZknWW;6pSm\$,1V>nLA1c-i$%3u
%2,:ipkP$(uet8^uTDu%n$eDh>kj?E`/`Ccg%=8C2=4'7;$^FqJjVEBW.EZ^;8Dk)hnK*p4YImqU^HpKFqiHPRH,'Z"r0Yh+Ee,.t
%q<c4es3<L9-V;<59uaCZWs)XCaK&u`"A):[Z4Tkpa%*9W,j-#d/Nf2kN#r8FcRZ5CR)*A:WXUi2JF*Bj*jG#L]KOF6@gAkW.Q\U\
%a*t;##8H]16B1Z59A(iTchra`Dl:Th[@^mE407u]leB>"5/X1?=O!77Z%KH2+s/&4e:_V$<[=SCCUDN/?*VH1"L[8>@"65dFtA%^
%QP-m24WWVL!%D-i/XA);M.aR;B!D?ic_2-<%8k2pRt<8M)"UmQjm_a^Kq).CNDY"0)5?utULI(Z,A(CBn:sHsS'pF#2<rN\hX]S8
%e@>GCK@<#WQtPQfQ+p@Z9,Q:QT@a)Y2H<hicY0qN_3IJLSf0q+,Bcb;Xp#(oq*5aN^bX$LR_<3kr]KkISJEHlIDj;W[.Zf5h_4?/
%rc;4fUYE3I!dTJ5RV\2(I_mkp/D4Jjm"bENs*dgte/2fP)L7G7pqj7u>IBQLI>iSJl<3[g!FmYmqiB$c[P4'Zr'6q'F1OYqK*>QF
%q7/[NK4Q2Cj[@39q4uIQLV:B-]tG'bqnr'IO44;k_dAS)2cB9?DS?[8,'*$qQC1c/c(us?Rd4BW%CYRY-g<q0k*C^3*Ul/-I@!4V
%[di5!U#P4L"4!R4h7V#1i!LGiPQ;0j&a2'OYUb-35??nuMu+03F+\s(gq/^C/o93e*??=((L5lS<QQkas1D%jPM")C)DV)r1ML;<
%a;efhTsUYK@eh\5-2&K?1[h0BUZ9d@/pq9?MCi:j1,3>!+E3/_:^I=[Tg?C!:ngKI)MKha!ZFcpK5q8LglTJs5Ea99J_Dg57`19_
%Rr[:7_Hc286)#GQNKrMKr;q2'AL+6ae_k%<1hX*`Mp<VT.)REomjlD\l"=G-T4S5ZI/^]h5D&sA;Q:gK]sCd[;3kgTNIcal%H$1`
%q62Up.@)^a=B<fSg\_&?K3BK1()[0)7t>+VTNK$mGC!ekY)>,HRVHYA%XUVS.E%,#:;A%W'*SOK),*S05m%BLi'2SA:Z3FJrI$+(
%()O_amWCJuB9i+8MCP.+h;Cn@d;!X;jYIQ/S:mmU!=KXDbtc9BA:J2GW[X0O/OJh3:s3YT9<i3,5\'eE"$MK6%i1Hp_RR,fN7R"8
%6_\Q:`jnYHH4Nu:nrUU&-9);o6duu_c4G!`LF<$8Kq#4.n2]e2/7M>0f/9piFac6rAu\f,po3`ZGt7>P`@e_tEgOGML4Zl63q;_q
%mI<_rbk^p$)WYH2jCJ\Z32Jmk7*'7c[`s(tcASDWq<7i$'$X;"RSIWG/[?:=$p]\sXVeN*TVJ=fMh5[Pnl&Wt?,:6[e!WY.>JYAo
%mQ['eaT/gb`+G-J3/+u0C1RBNjV4E8brfQJRW:tQ:b$en/KG6%Zc!.E"Vtj4Q9%e&[)$'c&R"0]TC?#pd+^!l<KL66PY@ITZs2O:
%9"7hGTfrrQ=C_=p?-)n*+0O!ZY;c]4T.P#3c>fe^#tD36FrRZn[86$]f\XdGnf+mE+qfL0XTsaVW4V:fc=19hVDDps;hY<Jm&=ek
%F^dGG-!TV:/>HJ^k`sN%=bO(O6CF`&`i-Y184Fc7#=51F3YY^"b9hZAb;ZFu%4@!H?sN??:sal&U9o'dXJ34]DC#V3^d`#rdD![H
%RRLN*K!u**fpJ*t##U89\.tMLo<#j&PUi_B<u8N*\W/%BFdP/t`NQLdGFge:@DP6u"EZ;B7hr1Xg`ruEX=!h42b8#p,9P61;,2(^
%[COub]DI"h2_U'Uj_9d:31@C*O:q<VYp8YY)>9%X'D.$HVq+.n\Dhq;E?IiU3"k\9,R_tp6u@e`JXpOq+:$Vu)@(kD,SFXF"d(@N
%YTj_r<Ms@phL$8$oOjs<_L8C)K-Y1$1tds[LgUp``_:%S<tj+4On]hp!O@LcNaFaR%ZiDsPp23f?/WL?]rYXIdt%5ne$L(GH$riH
%LqFC8;9&t61NoP[_AeA!(imL7l,u/nR9ZCHO\IEa:4;&cj4p78/goA6>9f'o8)9/AYp5J"aClq\rllk=p%b]tr+<2eDeqqtRD%78
%bMB*]O0c:NZR=?d;WHHu5?/f41]Q/jEg"kj9\h7?0qW9B`_6i`X''m`aj<`0er[Yp1h1n'QG2uCr`RJ_bo64uk,5Pm[77Ls<GB(n
%<.u6WZIFLemNj:hC:m&W\nAJIoC$QpqblI(^)@fml.KOekEtnb[iLtTL1J!h^fplWorSJP>k-kBK-lBl])YW9Z^:D/gedA0HkXAE
%"%"\]E&LRrNKe2\jY/tMS4<*dl:1kHq\SC/RMELn!QsFmP&r!QlBQ/l=Giq`WKrL>9ds4%/^(tnZ]%:m,IuTgPaXhG];Fi#H$YIp
%gLLr2f+;4GV69<?F#',nXHW</;oC3p/17B9Rf^r7aP9]HVAY%*+.Qm(4[hOVOXfla+0O#8NJGVWH97N,7m_'PD&b!c26'O&*KO1?
%SRRU2dBQsm3l`s]:2kS:h$1cqqdoD/>9qd+h>lR]/XE7o?JY_3VmP/jo#YZ&-"g@1k/>um37I_Ud*)0eXRBI5f5E9%cfXY\c./;R
%2K=-GkOP40]k]S42jsJtn.=74`^*Y<A'$Nmgg\5OAe#u%WtfR*i<ak$@$^u2g>)&lh>R>e?#G"7g>a9bQS7`58N")k><g/gPa#mP
%qduD4+n+Q3Qo!O%Z_1Ifb_\)lCbH4JlSis!T5A5%=QL-m4C8-u,9ZNu,GJf:l#*slbraWPddgt4r$D`nO-J^FH(U:-mH&X2pGpo%
%egl5/Qg%uUo(fTKjY#EG_p]+PmbU\V+-ueJmD<,bP;IINO:0(BV(TP)hq>K^?+\drgDrBC=4iX`GJC<,o(?>&\5QgQm[_i<fqfL.
%W$KrBfW"b@S[l?W5A=J"4)u2b:W68cHGoN,c-E'q>4J0cjF'E\;ff,95k4(^V+/4^oPPB4NR%g?Ih-s)jgmOdO`,7_IBFLg_esYn
%ba55_`Q^%0_EIAdV>?^qh6,K\]Q)`-]1dGraa#/i0o?Ki:<`kKeC*q6ac!]@nS9K/Kuj^\?SM:$[p3m.j9"[k;1q#iJ4X8BPP>>U
%I0uH#nP.J/KT5bM;1MRWNa)oii8g,Q@batg^9(ir`[rh6UEILCoe5d9Lm.>*/Dk>=?f27'KkP,23VadH!m^Nc@d7DN)Te?d)$<B`
%e2GSLBb&I\'&APkW,9\9HIe@3M3\omThJ$Pk+k_\McI#Gr;-I\OpH$`+9g1c&EqU!-HhlQQK_$pXVMDR]eSJR/+V;_'uT@V'Au)K
%X!QLp3pW%d/pu_&fmI4GbB,-*V-0q/KBEAA18>J0^J;1ZZV"L_-SF2B:Rb(G:V!+-9Ocdc3MQY"%67*If<R_mS-Jfln"j;8Vchl1
%oHEfUU!r'LBWF8>B*t>U(N6A-$`,b!J2\gV!i_M+@:tU#XnsUPhep=ZB7o>-D;.V:V?ZYNm'bt!ABZ'.gr;r2'>LV,4u!g':9[gI
%G_iI[=FZJ^F1'TaI!Y-)G<6]qb=gR48o'2B!O%h<U%DXA`@]nRPiREt^=JR%k4/GO7^p"%m='8L28%fe!U`_NGh6cNRM%Csq$&!9
%Lj9B3X6D;KP'tt^T%j:4)lc+FFW91$o6Kf'If/F/jD?!1]']$_Moj^;ns4#^WH_58[`eDoI$r_BF8K%-gFkM/J(2a6n"3['LTZnK
%$mht?fJ6)SNrRa')SRCk0l!a?hFf>tauc$9Q?gaS6^6-rH'Qp9#g9fOB3SB^11f-NV/t_MAejBMTl_p*<4Xo.jB2m84J]L#Q=4=V
%^,9,)=QEF,5o!E(MlM+A[?$PLgJ`PB'KT+H<.Ud,+F0]NiB:c'7Y.g'=DG(BDFC:.]-(;\70o^T1AN\Ym8XG8M\,[_G$qs545YEs
%iU@Q,pA*B+\DnU*>fsC.E;-g5<>em)q=X4+_mmk?XhKQMA^6mN,?eF"F0I0!oVr\;JuD&MG-)WhR\Q4;Bk4V@<U9=#)G;[KbZL8g
%Z3]a2?t5YYRF+=D9?9Tt"M8ojAP4Vlc30n4!Drq4Uo9rmjTk+'daKaWRZCDK(OF]B+nGu5I#oJN&P/al<)IT,2Fm&Bc+?d8]4r2@
%@-A1kNXWSA^t2T+dnOL:$bFt5F.eSm-qlY9X'O/Mp=mL-$d47+\]ZM;pmSDFUI!)lnC3D0k3F\`M_(ca.MmiiamBIknTj\&ci.\X
%qf^$3'I43D?40ZN`?<OJ[Wp^*Xmmceqq5^IXOjcnRt"a4V2BD"s.0<NQFU1:(I%6Vh.<1?Gm7kRqJ9GEcGh8j;<KGu+.P?=<^F2t
%_j@5M:c4rdV,:.@bW0>2nr/qF)7Pnfgrtq5p0ZXf0GV8_mNabS`S`3!?W$cZ\3#fkX-pfYKW\61rRbUm].iaF*fD8aE^EdNLt!_c
%C.=So;,E!m,*^PDYi6`A3;=g"pto9SnSmIP9#CH[LNg&`1fo+#Q,PcK!u[E['Ptk13<!Uj%aFR<=[4f.Lh7)n#a9ju'\ENb?(>fD
%U_Vgi8!o%hBJN=W<j#0_8+&q^`.f3-eDR8#W7A,QQa#.3CuQ;Y*7a)\o[$u!9inZ8hJ+n_N[ri9A2l2Vl^#7]P5sk?)ShUSf='M&
%Mc&Gp$JCrPDjsF[3u>DXS94cGDX6WCeebf4Z8qL+LC<IJ7LWu6d3s'`ka]l2:8L[]dlQ#7:s3q(di;LRa%=Mf<0MqNq#&A*k"pTU
%l1m+_KV+I^>>fp],'JtJCK'IrRI?dfpK#QOhu^9L/*noo9>?"ZhKLQ"*1,-,(:1]KIa3XSV_Vo1^R3IcC8;7&Q=7ARq3!+)m5gPa
%:R([n;&NbaVYk:=4XHnkZPhZI$O+)AKS,.j23Kj9-R1O]8N<eR?(VAN1I93[!:*=Ik!9t/9+uJI,<#'ZF_NaT:7Y,R9Q[HO)K?Uj
%E]/;;=QuL!Q)eVG*T*:/gnKFi>[JgO``="J0?;hiNGAk`D-,MR-\`\H`c\G"*T'a8689[Nb-oW#9PIYC;>i#UB`=m[que'D@o2$3
%)>29De:Nk:c`F[$(%q,QlCC&,KWWuUG'JMFc&4S?/Hl'(1@%:R<l/`;87m:K]LnKe#=tp,QWK0uNLUJi;sL6eS^Q[fgp?J)q79;S
%Z;'_,h@rd:TZ3Hifh%17UZcM-h:lRjekPri8']8`iTj"naE?XfAaW/Aj+;3o=Qq)$jYH.fB&4GO-8/`pmFfHg(F(Rdp?mj+fb$*B
%c@K31V#"V;N)ghPLo)[SdaYg]G'%!M.9sp=hH8j?7a3-Q,"iE_rH1'e;IaL3g=e/?$qN-6fKEQ8+"ajp=Z97'kX8IV]$cAP3FLPP
%lU%ZZSLF=R[@gsk3*K#N*2ccSRP=q([<D'&6L<jlb$"6&=JL'IFn%5=?=_4[D.4&M(&JT'5/r@8M&nb"bnT2MPV[$]*9h6(#.#s3
%GXd5nJKs'.#:?]=jb`:\M4j/])m,l7[ZN#ZRiS$p-73;@X.,6RkCoc$'$bV1PFgu=c1NNqbq"2aRXLB'7M7F]MM)<0?2`U7_K]su
%G:2_Lp<<6YB74:!pdpY&kg,Xhg@?0nS*2U7ke+-X*mH1G/;Hg:Cb+1^Q7^:0Djg,gNVcLF[u(S*?bHI"]Dmo8al<9od0dUsKi:,k
%i^1GjP,;q2E?Xif;^Bm5CGA=_ge7]QM6`5qI+).K^-^eB$Ct.0h-+TrVRN.!Etr*6jic_o=34fAjX,i>5V?hqJc+1RNt68NI$%(^
%q8.Bjdo\b"lj9\dM]UtTA&R'lS]6e:B;#T_:=0(i6M[+C<5d_f/LbFh))n=h.<U%lb3I7cJJ*#A2[X=tB:=PgWFK@mheI=5o2XMT
%H!/D&bM/B<PR7;mLj;.PCeFdQ#^*B8f,G*]E/Q,HNEqt$.;k=sML5jh=;P:^NG]Wjmt43tbP9^1%lcUT5l7&2AA+BOQa*h@>4uu7
%5;BB/N?akKDTo2bM2V+7hKRl=hHcIW+bUcS9p_nB/gUV=g'>(1&>L;OULpDX^\"aY0!f/L/6O2resn=-I)I4%fdqlS<_\?rq6JS>
%Z;-ne=HrQs/Bbrb4D`AUOX4e*+)A/uY;0Q!,^u3tlddo>Z!@"NP&<'`239Om>J?1,CL_jb5@<n:?gomZn2+MA1gf`/?gomZn2+MA
%1gf`/?gomZn2+MA1gf`/?gonEmILKWo*%q]"]+aS21EBjI)49Z_:.1lV`F/&C$nF>5`KKhOC_g7p/!V^>g4$ePJu-.gX_\A2+?0A
%(`Pos.*b;E2I^AVD+UjH"*:69)d\M(E&WZeM)*K(n2IFEJX.ibD^Us_0('.-K#\2'BU6,^C/4XY.p-Zs-%sQk;lJObWk%M8j<d^:
%pJ^PT/:u^*98Dlf+ZP>/au:l"gZsUb,/\iU$\U(WrJatWfW%'H+N*5jZ9U8HL04E@!jXi,rGf.A_+RBSna\#f@0Q92&&b[VSeda%
%kPY&Fojpc,_WjT"gKSH3JaV0nH[Edt0RF)^G<a*qn3@a#Zi`n6q`/0XBiU1_ZHgp8pa8Db]`7DX94d:X.$KPW/It6[PnbKYgk,(b
%S59/XNV@fi<c(9PX_jSMe^Zu>>1u/qQ#=$`WZ?"[72Puu4'W8r`&EPqS!2d#`+h9h)KT4emU[cn4Yq-MN9r70M.V)nN?8s!,(nOA
%g#\;?>Q43/?17^ar>3U$bVfecVG5ad-Sm@3/$/e)27[;]\+`ENICCon;p4ULPatuH@X:iG,Y[)YW'9_fgjUr_mh32b&-?c7n*F(%
%pP,3m?F./S>]H7LG/*1H=2VT\VJjtpI!1>PgoG-Zo@Q>8hM2qQ;maO.1-DkQDsn"h\N+n]KCI`9W5d@>_](8WN&o`],Hf/j&\:@]
%?Lg>JTbs[ORF0o!%]aWt-9tnLrA%Rcf(dA1AQU&rqMk@[HPYopPd2lOaa%h#mqqmq;WF)^^XW&SC]$_.nU$D;TZ:qKKAML[B8J6I
%iJUbCA&B.Ip:d0kG;m"CU5^P5Yp,4INrSn%qX0)4Nq[+NpI4Z1SOBWR:JRloqipMi9'.Vss(H+10AkWZ8EFF.pMJLU/pQ3cjM.2]
%)sD&%q$r2Q%1_jMs/q/\b5kE6J.K<1@J9s2b61&g>R/:A4a/kZg#.)F_4.oEpe)=IFR]irKnR.%eg?gRr];+nmr)?4ccN0s2;XfC
%IXQ^`qepCTh7<*MGHY8qrr2m:nIIs`Vi/Xg$eTPfVB:!.l$N3t/OFjjUCOnEi7)n9>f*KO[/,_`T/#Z9S"RbYEd^E\O'Qp8+P8%U
%qZ=kL"nn;?6*r:bm7VQu'&c%3qm=AS0+mYWjSOmb?_cO,1d'Ua3'QM"fr>tF"hR%'DU@uZ*;s.%DFnoSHXiA/qdh2m9AL@Ub.0q*
%?Rl!TjY5MHFalD8]KhJB8:aPfMn:u5b@cD+]^QeDjeH9(mYPhSfS6n$E]bniiDPmBN6odH2Re=BUE.q[KC;2k5`NsV_UAQ>ZN1"V
%9Qs,8a9%P=Q8aWgh]Y2+ED[4HdT<rP^;(F7,:Bo3'[&HC.sB86ZG]JjqeWs'Sus?k"'Y-f.2Pi!lS:1>]"*2@>@6W$,fa5Rj&560
%HfY:Z8%Y73.[(BV?1,&,?7pW_kN=CAiSXVcjWQMLggkK^I-;GdN<)0foCXiOLOoZlHGYSKgi\1]<?kfhe!'NCkK98iX@Ep&?=RM=
%K"nq@TR$'o6&@p#b-'c12R.nSf'%8rr,<Ycrora=./6`AinJce];/ErbIsh#W4H+#4g2(:o'"N*>6'Ds'pLNab=>UKXag89VYXGm
%-Ikn`RHQiXX@2dh1ZJ!Rc3RD=8MbtCI/8Lm/al8Y%@"&FroWl,hnF@6m`"KV5'\n2`1DJMpsIF&U#.a,h=9lVhu;l,:Z&j4rq?$F
%7mHqIo=nPP^TRKq*kSHT/q`'[jmf,7n-:UJ>lF);r7Ct#TDfR=J%80Q5J-d4Is+n#_`*&=X2g9m1n7hIh^eY,m5;[1jE,N7,bM8=
%GMsa:^3$i_$i]f;)u^#d8];(AE:?I/+DAP$)?(Xrk\:V\dn6Wk#2J7P=.Of]WF,Vl.BlNjos;]mZN`RCDpOtkr]8XFVG9!NSbLL-
%MS#jF62JKlp3r"\d+hZ?YS'Rbe9R0/<SVG,*GrUomlb/>dWI)mBS1pYY6tW#gGcZ2Q[8'PeA'IS:U$EXgGIaHFb`!1^=XaCe?a%=
%U2QJ1Y`,mF2XR\Fk1qq8M(\I;RND+GJ<t&a,@rspq7>\$J\=I<H8YjhkaSIH2,opN9t0F*drm$CUe<k?V?\-1rk%VQYQ&lq\&kZC
%.;rR#l'mg:f;89ukU$+J+2'p4C"p+DkRR=dr/TRYbOhbTTXX"MV.2_gg"(5$5/uOlaN\u-6:L%$Vsj0N"_cH\^8Z%F0D[QVF,<*G
%q1J[H79?@i8CQrea5XR.%JmA'URk-N;3*Q)YH(H5qplY1`4d82M8&?)W94eAo@C^c>RK5_j)p).[V^?;QVEEb0$uUP#N4pQCmtpd
%^9tUjHI/!QN-[,(2baCDCN4"&iRFrOB>=b#fu0iP&q/4ca>qe[:"(/D/i^bX4(cj+9oOBNdFV+iG$R?6gGW?@H`l*(TBOHm5O,dI
%h_\WB&[8f_\m2"uB%usN0:NgO>bZ-@*11juEBDeN3[@e9F$8a1JK=DgU+_u7]AG`"oG\At5qlT%Qk'fm_e0NDOoMo'OZrq(r#p2h
%U5pel[^ECdC?OL;'hM\/agetKPX$2HQZ1op(DU!7-R8atntiQEe8adt&_UKVRIL_g_)&4pf!S:Bj[Iek@1Vf@\N-&(cmo\o]ZHl:
%E<YN1O;^5PBR%E(XTdWSnVW/Pn3Vr(8\r;8o[b6ccb1nFg6+XA(uHZ,7]N&KF)Vol:g#",+nD[N`o5V1aFq%pflq<7F@R)>TX%gT
%ZqrN%#FZ)FH*.Rig80jjT7e+JY0$O+0[bN+N<m*G9Kg.%-n8)e4'$+3qsep,CP?mU2*d3iP<nl4a3!9bV2JlF?fSWZnmp8o9)(GH
%.DDNL1=7t"T>G`bg3s!6[f?+:aH=LWV?2uth>[Bl61dkF&LQY:d7J2+Y")US09mW1MCr?fHm@n)S(_3fbCe?5*&^VZ]EDA-=WSPm
%TNi_T^g8J0nt9F3RNV*dlui+lh\$U%Qb$.1rDjKEYi_.02Xhg7/Tu&Zf&P<$gg\1QLkD:D-UBn0aOib%fgVcb?ZbBDfB@A*FULA?
%d<-P!=F:V,';4YQpA#^L[-q[d+MGcOkS[M,eH>8H#<b'+`B]Jt1Qh>4qMl-G)'O`tP_e-WBal[q*%OkL'(Y9Kh&R>PUG\Ot!V?Zt
%Pb)8iAT>kfk[tH)#s'':"J#XMF-f=<Ss0STLYbIB%NZtfc?=+URHl">m7nII9sbIO\<2+F.gT!!bb`gC#Ql`ud1?#$,RL9?5Z#=r
%2\k=6(!eIMmD6O-['-,<(s"JrE$uE'J>a"e$hdk,--W(r1Gc3AF4#TAKnoeeb]un<C(@J@S7fF:@mgCP^)GUUW"Gj.O)[8k%II*,
%q)qLdDmI[Da>o?h\U,H]_!dP^!'NECTu7@%QP^BjN!ci+M[M6SI54=P\O,afp2l0lRk#f.p7b+o@T$\-c>+M04CLj-kfBjAYDfUn
%=cCaR3=K*Q4V[^i_,D?\KG>$2>4e1jq$1PcGquVM?361_K?^CQL\66\<Tl^jeid$,]/9nWm]IdDb"cT6kerj>?LT(M@`idF!&8i_
%l9L$O7b0tZ%6)R.SU5$&eLDr)93ikc7'Id]Yaf1l@_gs)%ra:V1;`);]<1*Ff:>p4;YVqi/@([])%rQT%S&gQ&(!+j7W>TI$di_k
%&Oa:I6kEX;?_URM2+-uV^/!CVbJ\edYR8:2L\)F8*+CYgk(J&$eE>:gi2A[P-[$>g@qabPronJ>HVp^n$5%AX+js7OWS/,n';O%S
%Nfj4X7]5B]jVA&SjXH;Nnt<mYWmODY"Gi=iP$,I6@THsS_r.?e9Fb@P=`(AFI,7p:].mI.e.ZF9*H_G?>tBWGA7?(^IXmeRX@$h)
%%^qRmkethME0"U(_3;TDTnA0T7uO&DdDRN;6_euMc$&''9RVscLkJks(Le,QY;(MhWO1"m$SOJb0ofCU0l&(nH,hHs,!OQ*`l`*q
%J&aG6YB1Z2?:+`/H2J?(r(BiUMAQuqX$"s#$,hSViTBNp!d\pl:$MK#Wm(Sb/]`g+?=@Q,Q/%Je&gsPdL5?fD1UC_!<(s*?Q+3AX
%<<Zg,UBQM=BM`+]/NVmX*gA7Y+f(c!?UbL#W8Ktj=W5)XNl+;N"q\S8#jYHGaf@*+",@Ir4;PKt&VnOan\_$7'L#VaeakDm2%:).
%B;u"?n&'S.S8sKYncp1Ak+het[`I0^.2/7g'hGOPP]?7$8VB@9I?e7@eAt%ECgR3KKKQ'Nk2kR?)E\RpmLuP8Gp1TZAUT+%Roj*l
%\S#B27C3>14X^F(hKI+RNSi9P<)jg%dk0_tVG,3"<?>+66rg&g#?*602*@1%/':W^-E*)5M)C.TQV6Jc7e@[Y0P-]E-Y,%d`\`6p
%"X,(=Jlrg>Nik6=4cm`9?FYa.@O*mfj3L,\l9$dYW<";#&h^a$.Sm#3Z"Fr(33:QYmPpa/gk#\8-_`^FHrgcsaaP?KMm&j,H5$r9
%bC(7m2A&DDILopAes_=YeEp[@e>h!uK98T#m36)XO,dhVa^RG:%>iRmTKCNYUq,R"-rJ7`-mD[W?2@B&=.T[TbLq_03tX/0ZRm59
%X@q8fg_Lg*D#\Cna)j"u?A%Se3m:s1e6Q;n0*2DlW`K1-U)q1Y<F4r=T?EE@YEl&T"mD=^N9Q^V9HGPT'.FmNQ_lY0km6b;]Eng.
%LUR`j0JS!I;qWTr/_O6n#j8+qf4`'*%HIP`[r=3ZX5-kVIbJQ`dh)ri87qHp9#mTFIdrA[iY,M:WH14s1@W_/V+@5VFl)WtG]G#n
%_)43UW`P7aC:1?T&o.SI"45a!Je'eC1GF]sbBT0kbXC1H9Z/1;RFVqL]'eKmj+e$>Vg+ZWp!)qU:(%no!lC_`YSj("2KU(>Yp@pl
%$A^`b!RGb*;j!gOK\]#>VHRERo3:<j3@:FUK-eMKfUr8`W'f`b)*kT(oP180<e3,`b`h&QFjT&HQ!!O;AR^Cjpe3ahHgj]V=!&)K
%[@N#o`V12*g'.]:;7$W(M=g1:b&t*JNC=7&l;DOOgG*ok]WOXB%$qH0q?^25eh8tRDs//h6:+A>MkuBBTJ:kVJa='brf.2\,W1,3
%&qpUCZFht"PL&m?0Wu&:Bn>]dmI+n'^b3f!l6-2tQ-p&oXD"<6jb<n1m2fFHAb9F.eKQ=Bo'XV1U]++'e&aG^23:7]Lqn57&@3a:
%%9+9]O'4nN.39NWK+M)lCfF)/Cm-(f#h_e+eJ-l_kCV'E*[ea8jC(O['2,orNa!K@mqc3G`b/Dp:dmr$]"qqQ=GMS][YBATr']+(
%<dR!n`)j%mMPLR9,Pd(NTQR8tJ\"tuH>6`F<Be<NOYndmdB^%=Ss`-DXOU-P`P-VdStaJ0IMfJUXu1H/0LW!U`?$np9TaAtPN8]@
%cD!$3ldVN8)$`8#;:PlY6)E_pL8,)TE7HW^lcOd[eXkl?&0/"f0c!6ShFZ]8CiX$<M`qOW/Td-1X7onUZ/s-HVL+<dA1-QS<KT7<
%dIfgOMkG19LE_mC1'O^El/%A(kX>PZ_aLf'4eYGd.*QXKr@l0.`olG?FKg*\XD`!XY$tFK>hlEci=r+6[cX_*]\-$<ac9.\e%tdl
%Ll78B-.ZiV./Q[`I?TMl%RcSho9/=V($PfBNU41`;=*:>7O*`)`Y33)$dI'n)M?;iYBF:JHQ.mel[a_Z#mTF09DZSK<PEEhk?*!i
%dXfA#el<2u?\U/2ZP>e8`*t\MZ`k"_;_@TEI@"\go2lG3:$?$ic;eVX/pXTCTU%4*ep&Wr_H"gHJe*LB_)52_3aXC;$=#En[2Qjd
%NgDm)K9BB42PW&],%?jI/@YT@d@?\;364)"7*lX)(n!hk<Z`[YS#o^><&(ZI^nK^o`,)M5(*V*=YkcS8=dL3OX3.SfFt&5!"s'Ck
%'R8]5[FlBsS=QgA-T)bQ3nDqPJ7c=[0>-t0B;7neI:+Ik^XB<I;T&4IQB^-HQn'NK1<6L:U2X-mpJ`3Rc30SApka;L%nBf]=@7+D
%QRn]RLW&4^G"Ii-p^&SaB81-I:RH,lM$SEi2)3`T-Dr0)?-U4<oCA/r5OGMA4"XAFP"dh*#[%O%Vu=eS&rn'f!801LA?MaFfn%gN
%7]YaAQNc`-5"l2>MuI%m4t(W3=Wm@Vcg7&V(T+g;]UY3$W`ECe7*Mr>8G@J.271_feBl0d!F.k[X$rUN*f&rn4Af0GPM*8Mj228h
%48P!i#uh.DL".&G%.-cKCibD'(<7R^bVF&&XG]5M$YFVBC*JLMfO!7NMG>g;Lcr+NFEUR_]dL4Y!M:#oQl$&AI:6=hg:V0iCc+)-
%`YuJA!T?aO'E:gO?L9#S2%fT,21pW,K1=9d-U]l>U/o`G,=Te?#k9;eN:s;CW6#N?CeFUuM%+Ph#'*DEbds1pMEp#0NPUo.BLRWq
%TWkZH6t5GUb1+O6V"94JPDfM^`kI>#*Q2;)ctC?:(`YFn0\2#DS'(Teo0=Od)p2sGNlehfb(Ve;10Tr(O$i)u@U``k%8,nHNjs#>
%4IMtTVU/XLeAt&9#:@mh-^H-`#":E=30mO8WP):/_WW5kLSO-[W8'6o5]N?P+c&VCm'*5$UZ]EMIC(ggF!ZA]!&6KVI++Tf%p\Aa
%)\C=)$@Pnr[V>ofED/d^@p#R#(#2-Ae!b#"MFL<@6ZSuFFcjM";#jB''3mrI6TA0p.hHFW`Aj5a$cM2f&?5/&"eg.V-%HiMjqJr<
%r=au90HM^K9__:aDIdApFZAYXOfLD_M.dM],XL:RI/G?Tg3e#EG9@Qh')bN%Z^k,o+%@NfLcD\IZ"AQaTS16q<?,>0DbOnsD(Q87
%AT=<<>CHse>fcE?&H`Id.%ct7YLZ,C/'BI[Eg'rSP#Y/EP^A<9h`,3"eq\@P+Q5=\GlpGkL[*j[]oFABr]Z(-Nd.7UW0^KXKp>\C
%<)6hY?J`Q.4q5+;7%kNAf0nE+2XDS6WB#M\V#m^H/O1X9&IO/.,4\6aPpXd+Ut])'/HG+NHQC5ZS$YTo2VW4n7#:Mk+Tf0eR43>[
%[<WGY/A%MC8[d[#+Y/9gVl+M'qCk3aV7316GQn<'ie^,"M)L.,T%KHd%Lt\VHr)GP,De$gF.a"I3[l:GoJ:IqHO@X;#W<no:.^_-
%l3o&rW%7=(_;/3]a>uKlA:\a!%.?HU+FK:J<Q=Wd"adK-_EL0mPGu^+eT9jiEl(UA<fVaef?"V\4O&_86]J.Fo&_F9$m4/S*R*;u
%IY\<VK_7du6V0k)?,.uH&'ZaE3`tag2XFu%i&SZla,#-f%OTZ"Gb@@Ke:m:"f2o+\FiW:;S8`>\%Iic']%G7XO5n,>&^SO_eN9Yb
%"B<]6S5*4lPI:3p[83fjNN-hQaba=i)'3E1VYQI3S!bRM;r8@)g"b?;R[j#\)%\B=Y`PCIWp(e;jj?'[`nM&iSm5fN,OSkWFtjoq
%INcV.&??C/+L5G-,Lqc66N,c!@?Xa9'/d\+JMa8a3&;RLCh?Xl'#qt=)'>srAL=CbYcM46Q@Rl>[]bI,AfDT/D+0[?Ei_0=>7gML
%L-:IB24Nla<ZdI5&\Zfg:i9R@c)()IfE3']cpX>9noeIo4?+MKCA0?="4Z9IB>^lS>!G7q_&Uulr3NDA2.p(0YS"PW3uiJh;j-C$
%3sI(F%N;@/j$6:@`[<^C!iQngC>rB^iGDo]Ig.G;"]02f_'[9LaOX?R%cjJ8IGr-o6T`SJS\eLm@?<lOROU.-D&=qrd,A(4+Hf\b
%L0\>5'amS&$nBeb^eUOq'f-0F@=o'1RL$.2)ihqY(3I$9e6mn]&e_A**mK.dqipqb!j&_nMUGVrA'fXAijkJQrXcYU5/jM]H)9j^
%;Y)i.+8)ZVroiWks0&DUn>/[R7BLsM=_N>Qkn&-)dl;Ze+HL1Z5C&;nkQNn%X+_rYOp$:p,Q(-%@jFT6",q&+jBOt^E>-n<R?sQn
%Tn\a'+E:8P;PoK4U$h%05j5Mf3<XhGc&qmRGSre9_<1+0c2\WaWi08ONJUrG+sa.e<(c-c&";EW4BXN&akf`<!]o#="E26\.%8.C
%'=a?3H@=0X#CNp=:r?jrETnHgK+T529?q6,bu@!^V.)+r1/%-WTt_qQ-nh_:X>[Afg*[?md</pn1-&/CP*\k,(DRrVIH&F8co*tN
%CS)n_P_=Dtr1:*ncf=9LRWKCXTGV\$d:,q>4N+`mm9pr*o^HAl6RF1mgY8*>G+G!*_(iG:q^-p)GO"Lnh0'R&mp_I1Sms]6d7lUU
%-8#g^pTeA+NT89+KleGT+<*+&9.T%l&2.4TpE>L)H`J;>&Ir6O+e!0/)?U2r>N>pjh\7l'OedT>6"99AgEMA1C.N(nM9^K'^qdoI
%ipn4to$\cjpJsXodl=X:Yq`#i\gS%5('DWjV\1&eBP\Zg=(;=M%:1M)o6Km%7)sfd>%JbHN$C?F[A\#-LkZ+O3Fr<5gIR)'DiR;s
%TFS'=(u=<ka$"2$?49i63FEqT&dt-/6+Rq#r+p5g[jVUD."V='pi'1RcCS_nWX"`TK(VFH-h)dFO'OBtUCCBQfqqkp*?YnuP$UL+
%>@Jb'7J%pu3C>+nqfG7C2+WN9LeAjT+gS-RO/Ck#fG7p`T[QIp*:>l/(m90*8J9C'+R1dNE,/+P0rqC*goYt8:"Do)'sW`[,o&YQ
%2N4rgRi8oPlLVOb<O>)^0/#FblAWE<`JU"nnmjjcEml2X)D9ra8J<ne5o6CalAuCn8[@VPPFUPk>a$JfDS[pX`P*%%ON#a$cKI=B
%JdoJSCTQ4aYh%fG`,D`)$?g#WcV0!f(<'+>C//5YUuU7+-/ZAb5cWMqkt0ut8b^*gIbZL^&n5;^e$!Bm](I4;cq#.6#f<CK+S#rb
%/4(<XiNQNEjJ%4bqA5>A&cGD1+eSD+<)oKm`@2Uci!4X<61+nWVX,/LooBGs[pr6RiSE9#ke:J5:F."KjBpGm)e7Bc#X^0KW?RDN
%6m)L5STRqRb(r/R+Wp'.a(egY8[*G&b[[g(>71'@LH-m@9e8%^31q%Ck/Bju4r]\hBlfbkq&krYb!I6'QTCUN%fkHl).c;*WuspR
%,\JO8+g>b2r#5\PVJRmICY&="^kU4cZR<OUPUE\=PEl>9N5B@=7ShEj=Th,"ARJt^>!kU"RRq=UiLG%(oB7lEb0C]+L>GA-_'&JJ
%V2r*ANSO_GW#Phjk7l"d72U.UU1,p'b`eicJeOVJl!50XX?Is0=m*-dTIP`.$71jlLHT59Yi:hm"BpcY<=IOm^1!10doihj/@;S-
%C,hBd%aC*!),s!I%-G"dHn<e+_FK6E.1E#;RbKm.(T;M`CA:p9;Ob.6)R+c_bJ5^h/@-\Z!pcMb4%g9blpB*P6*9:*Z5a?MD`0_u
%*e,J^,E?9_,)R^e#Mm80/<QKZ4TOAkK?dY&XOV2j?p"FAWeKH6^gU<ca@:[6C3oKt,DAJGaO;?8P@2F]WnIjk-1j9ql\:[tM'oB,
%ai4`uX^RcV%C[`@`Y\iQK_2Fg6$?t*q@gL+nSI;5U@*64TnTR*'V6((N0C_7[&1\gXUn>moJe3O&OWs>mkun+OD]HDo]?mf9'a=W
%`.s!jeN2mro#5&[=!BO/08]nW+\ZJ_!>3R1g_5&\P5-3%6--e.Pg94CX]MH]IDkcA8+<*5mCa/[aAOT%;[RgdFK\)l-B_a@kss[n
%7,3'F9L^Hk.<(@?$)>OC7#N71MU;f9k,Y.ESqt\M,n,e7`b_kn.`%7J9R;:K""0[;Eh(r7?#8=bpGrOR6><qN4BLnF1slIG$&>MD
%[Et*_nA_P4'C]:"2,&?T.CZPFH`-KKeK@>eVpT:h3)%8f$/M<fEHW$..0rs\)TXlT-L$/?BSJ\4Z0),c<]HK+:?d@3>-:r6+P*VZ
%nJ(SfX%_ZrQiMao;3>kJ6mQ_%YB[Fk8PajX]GSk#&?Nig&hD[W;kE2UjjSb<;kk!AR!3-S"L?KtU8`Kth&Yj>7=bT*1N6.2*j9Yn
%%D\5M</ZWcI5NTTH(!.nfY=oXETm2cLARh"@&^?qq:^]]SS2%bg,18<ER!Q6oVs\$;MT\]+BBhH9nc@%C,HF6/G-P34`RJ/TkE1U
%)`g"%M9`R"at^jD50*rU`?<d24L&JP"]L=Ght.&J.bb-Z@VL7O*dDlO1kD0R7"hO<Kt_Pg.)oBp[??^AACD3cDgKhLPB<_c7%L]c
%RGO.m`U'tn.<AgjB#of^B:+PXabO_/PeQQW([ThGXTq3!XQU)f5^-YCZ`k6gK!S0UD3U?iC1g5)^?J3ukc;CD@!)+"T%27S;)toI
%3Y=npB%;ouqIEa1q-\`qZH6sWFL!P<4kAQ[UK7p;hUHV'Ne)e7hA&#WUn`ak2TW$u`I-J;Y!bBWQme(8>(`@R:#''uRZ!oZ.6:>O
%5%=bPbn`e#,^7A-B&GckR_5Z2,8XFcC1dr73[_Rl<EXNT^0:3s#h6Ul-UQ^l'?SO>Vqb:H\lWu1OD'X\Md?]g0P&'gi<cVV`R7?f
%@f;1#(15WTk\M^gal!`Kg^(>^L*/B>\Yr>.n(BCl2)$L8ba=/O\!UP3it$eQg`cL7L'=>NQk?C=U=Ni8:gh@+MB?Xc#%FVr0d;d.
%1FK:jl#:7/Bl.AcaK[G4M(\$*%kB02![a@N&5U3TAo`<al!6(@<8$$i(_)PE7_i,JOSMapM[4>3EQ7V]qD'9)#$o3^buD$BP97Xs
%1!nU$07C"$1AA=Q%ui<r7]2=SU0C_@.]3EG$roV@La\YDJ3j'u0Yti[TOlrsp]UlA?_Sd$Yk<jE1SK.-7p%=$K(<>(#Z(s4Nao@4
%"ISdk+VadY71*mfpX-7;&_i;'hbt4E2C^=]MX2]a1Bl^:D9>;aMrLe)Fht#Wjq:l[0@L$_&Ee.i4qdWJ9R`h^#mQ2Vc8[^E3*DVk
%0d&qm[9-SK\4jq&EO;S]?)OP/'2`6SO;jHi(?j/h4EQk?Vt@pkWtnR=kW:paH70bdDY-o&d*4u2fi]ltBTRVI1D-mH0Y*Z!Ya\;e
%Ann'=IC?%&grH[,Pc^]ZEe6T<W4bs*bfERPoH7j;!_*q]6Z)+\G8c*a$S[M^GK5"24e.\Emq67hiqd./g"V?SQ9_dYFbqb<WS)<1
%U=l>>.P+Bkm4=e0=rL[GHC7Y?6k67f.uXUc#&f8!U(NPT?tdG$/j583(ZL?aj.a]Q7'DTED1#ZcD[:a=0croLWR2"da0n6)<C5@D
%(a6(;N9ib^KKhs_<V1"tKFH(@K^3&=fG8o"9IEZCq:5"E+/[Me0fgVBV3UB^$m.?-#pF`=cs.8!a"&j?.0de#BEHqhJn;Gpd&.n7
%p$hAsQ[50"*_<pUgA`G3%sF#[4:2-^g(R(b.4`*X>f"n$;"D:XnJb*7ad"E?/<K\50c[XIaZ-#]ZSNQ5SQIhS$ss]2eLDX\JYVn>
%,Cor*#kp3>#oCmH_j0j2`c<1ZPgA5l-bA@-#f:Hs:emb.E,Z"4Pk(%nJ0Mug03FZdHmp07fJnPfiF/?6=uag<.WD8IM\U(Q'L>&'
%-k$_SFqj(MR]<JGE0Cg]iB0R@EQN7FPb?@N6-fCF+H8q#(8a"m7L@,V>nCWj/cZBa5QFlCc1i70OEcF*r^QOB!QM15[6m9>b/aSr
%3-cIh75QDqcbp?G@+_&)d)g?jOP$oK_&"rD0m\i-US,iN0fU.ZKcN]i@VmMUfg*D@8,$l+j##Bhm[EqgE,>`<V363hdTm/8bA<Ct
%0MZC?]9o.b(bCR(Pt<'6RUE:3ih\935?hdN2o1Ii5;K6`:0,>[iWN(OS3Y"I*fJpM\%TL!7Q3rDR?R+Xj=$,m5lCt;.1H.5ZRu]?
%D.9o-T?uN-`',)'AC^RC3<KQqa-`./*&BDONR;>R*uSVR.O5>2,mU#McD!:.i[Nh$#nQ+lP-`>B&e,jM:J3>b"]9N3]`[BTA<A=0
%#E]R?e1HYm6[/\o%9Pa6\I[m-jWgG4=Y`"!A\*c#?1V`44<',<k+9Ft4Z"Ik&>]@5PX-]_E9\8m3F?<9aPT5?%=B-.ma'f#K:b0G
%n:FID(Q<:CW=N-bT4DCDL/>,rM!$?^!Rb2=L"DKL1I)jb_h9CU4,*4QC'^P*;VK)863-9!_qF7b`"&!A=]g.;''84i:^;7,kX<[`
%@=iUgS1DXbA&e^aNEbUimNPM+5]r::'/FX:1JCmue4;I]XJ0u;$tlmc"fWtgUm17]^1q^K72Z)H92n85lT#k\1WK8J?tU>)a4-Po
%YA$%db$7M]V534P1WC8Ycnn,!CY"-\IOqQYFG6hS`_H(dB2cY,^*LhAgEi>HV]7mhA4e!XRDf1b2"`l6RNRbA\#-^u2;3<9\j@Fp
%;YEk*=X_b=,&^oK\<55Ud^33aUu4!IVo9TniAi6>_QSTd%\/4U46m!?"uqdi:(]Md9GQe-\sQER-<H%=dRRN3B((qnMTR(c,=/AC
%dj(WtF1%MlN1iZJV]sO89Q%[Mao&afHnLfX4qdNk/o&P>C5Epo!=?iXo1[C6dKaeT8<-&Joj4:46'`CcHk_f6S\n;RQ)*CuJoWFu
%c*/Kt:o5(7.60f^**%^UOZ58"!WCCQ+u"G-VtnHUpe4$,V^Y1H,uU\3GGAnR>[J2eUZ%-P:.qoA]`B]Pn@?E>/J1ceFe8KW]]I5!
%m.b^`/p!G594c1r-H`$ld)p?W?*dbdUqqOLF&:63W=hYQ/F!u!ggj.9hh:#t$;,:T?kOcD\"T;mV+u+e*EE9)'FFMF"FJNE7Rl&"
%M`Qb1`+,,S^Go`VJ3GBEZ.2j\J%JQF<X:W&/5oR#0`ht*Q&7j5<AMXgI$Q&!NUdJWriu5R^"?4=qk(hUSCp#am_7D/Ql-s"qlQ6`
%)I7^#M!_eeG))U9ipcCL(p0Cm/9ZE`iZu8+4ui_h_d(m"_E41D)[s9=PR*XfWL3]4/,BJaolX"]<(G;(k*&)Y8@=AdT=fZkhp-cs
%4U]u)]bI!h"9u"%Y!2k^/b,N-Rd6k0bJhp6G=0V(Y+uLL5h:Tq(c$YgVS*-ZT;AM5Ikqc"IKjm('5X$sQ?j/85NMu&gjs!+`gGRs
%!.j3Jf9`@5X<BEcZaS"m7Wfd4QDrjl`T=#.EB%ctGDZ-,.GqLX*&X'U&gIO=hZ2qc8Y(SR/GP0B&rpIk91%#SO)%`E8<R#KHXH=g
%\FF<f_aMc-8XO)_,qlP-ql4le9`fm9SO(+m*"n1D&bsSNNrdir7q&)5?%<GkOoqrK7@\33h:[C%R)5JMG<%-"PN_/<%YDT>jq:%*
%$`>SWbpF.<V*8&#6&oG&8cZ4"k%)pIa**UJKYPPf9p^9t]Sqc_D%SUKbDbDOm@_?u-n1'D'(pqhOn.d+%=T'/G0^N9U`=nZdr!3e
%=9%DS>GUU!9>;j4(gSAg1p7(+:P+E-W,CAQP9kLlQgC0Y<)Z]!mlog7,EfpTUGslaA>;J!'djV`^S`F?6WZ_)5jT(.JP#QEn.XVa
%7q.(S&f"9VH-df=okpBtQ;G^Q:0,&op,_JDPAK&K%&,;2`Zn8OTXT/FD5S^@-mh_?d04ZCq--:$`9[tsUGh`'&h,I-fqU%o/6b%K
%SlSP)c^=e'#q,POP3,c[6``':#1&[pgU?7hB\Z<BfdN1h20\Gg/Y.P@Etmb7<6$+mMcZ*JOMc>W(+r%Tq/i=Ld@"[qLs+(GXCX%)
%f?-L(E>C`K\T/:HOI5UWTa-#:8oIoRSLKl7B\Ae/,rD&1esQ5mPE9.I0:UhJ5FOLgh5]^*?T(b57Ihc$ZKY*^UZnr%`N&/g?T-:g
%rV]1]ZKY*^G>jCEgbo8KTJ)euH5^.d:JL<.5FV:^C#(U/kYi/=Ea-/B^2/*os(Ii8_TLgkYEVFn5nS=Y+.Be'-*CBlD:0.=XHH1b
%#',*;;(2%?oFEu,[a?=<25<$_ku@hRW[8fWU0;<M#o!ruI%!+RBE+X*l=lnN,-SIU1j3].3$J[R(2lAHU;CpZS$K'Tf:5"O8mcqD
%*V+qq?MICD-2OS'&*p5N-\'u0/]R*N<L;rLc_daq^1eKAVqCs!'0WXU[V=_6?0^*"4m8B4,u+$/Bls9lZu+e8?0^*"4m8B44i_;T
%Re@_8\nU.ZSa+lb4m8B4*@iDYVKi9(%-lQb-2OQQe+B=5@GO>@j)Pt.:r@t&OR2qe<$g31=p-,88/jXtQp;oST.[K13eRFd4S*L'
%+K.g<MC"#soHf83oj"5PHDG73<V3q@3jDD/e+B:Lp+[%&%gOh`-LA'Fplg?eSX7:?]5>sJc9Q)'<;MNYSO'/HLs5OL&<Q%.6TPOr
%7r,X+P+sXiO@;I$+BW,5Zj*)FJ_@P$B+lYNl]N+hRnHJe8')Wlqg=hfbY^h95*AB-Y_c[ge2;3%V@'DP0,G9NC6#g4VWIQ8@%'07
%?sX59:qC;ID8920.nY"(01L+Sm^_&o2gml,G:QrMQKIM$"R73'orb*K`Ti`kha?"A2GI72=^0@[oeOpsBekr/]a8_YQtNjrZZYPD
%jZBtIlcu8#Dk?Ht-^X%l-_&Bt%PG'4/*oml<`Q=ur(I6U&7Y'ZY[X@?fAOc<*<b/Hg3/57RnetZ2kA0_gNuVs@:b7D2b(LgY\(0_
%;_0=/ost:5Yql$,/oN:XgI\)W3]UC!F>DiucdP`;LdcDF(UHq2>$,lgq6'F&$L)*i]Gl%E;=akWZ>FUL%-Xk*0us6sfE=8Yd:(ZQ
%[;KQ><+@@.Wds9t85D8jO=T(&M,%JVZ?d*Pdl>S*/!968@'5iNTin*<QtL-SFQQdTM+qZ(.D!d'C.:u%5a\mA*$OY\o1@>PT$ruL
%(IdfQmiRIsLJhm7f*lqjeCf[&LZ_@[:Y>IjR<e,>MMlrV&s[08ltUS+4sU5u3?ZF5`qU@$)pqJ^6/5QVI8NB@-!IRlLNmd5Q]ki'
%P%"k:C9TSBI'[Q&raF*OKlaXmXq?'Ub4)YmXrD&35,-4`qW(gX=)bTI(?cA[?8*eA6kqI>cR$0P8)PJl^Ihl]`-$=03#0^sei9k!
%jd,TWr66:,7#.c$^b?+m$R>llK]f/.9`i*Kh\"3=.g>T?j#D'So9E8P(,ZC:9_/Y&Cai[HCPKa3e",E=2;9B/ho$NfN!^1-R.sq,
%o^X4W6e+#bK]BAg:gI'h)Y1u,gVL[SFh7FkCYrn#6I[-&9`LM@05amfE5;,$R]G!B_8q-,?CU4Eh_9k!>Q'GAS98n;qT8RZ]<k'G
%-+utqF.r^_%Q&_j/C"hI2[iGc>T_B-r=:fjN-q@)F`(m4CeNjbPr(KLZ1oXJDZ2mm;/S*`9$3'-d]-p@VKRVGD&WHQp(X08>0C)k
%D[^gUPNZ<i"1k2T9f5n2f5nCi%cKRuUCq^2)KUY@OJ"a>T,JC!3\T,:g7W:\Gq.\bKKMt,<KhL/b&3g+PBnK_jp=Zh2Ds4OMqtio
%c8.#l#=eDf]209*+,iJVNFc9:Ec,S:8e,-u:<A8ScZ!AqK$CN8EGe;3/(KGMO2P%ZG&.cs)KBrbfgPQjHBNpnDoZ-\r^[ie0KMJF
%#VS3*V;BJX$N4l-$=/<O^B-8LN33:b"P9A^DGX6R(42_k%h+F4E@Q<0;S@8jg)Hp+`(+jUfTTB/QeQSmV?gX@H#fXJZ2pSRc*$iI
%GaW16%s8I;e#'V\4BG>g27fe]\4NuP_P85(RO.NKP3[p[FN9-DkelCcmOl/I]flYtKMY_7.snU\hD``hq(OPeA`J_/@%rRcU%5!:
%1eo@$[%0taKu%ibPIZkqe$SUs@+0L%daH`4i/')#L>pjLM%Bo3^0lDIB$Pc)'5!8%[E_k!UOtGBU2RX.`c%u7ac^Tq%*f^AR.AgE
%hX"!4!]c+4Sed'J!/N?a=?nPG#Vg)A/kDDpo.Z2r>b\F(jnPlmGF;1CCDZN&+22M<eOu&/7*<RSNhJr*4C7/rdMfEaaX!<crGWSA
%'bZH$;^SEg[VqqqQBuBpFJEGF6DI-2b>L$e]Bf+tF<$0&Q>cT87?1LN:0PQ.cXXJ!d5lnd?HFoPf2u4V!kK/AgANJ[`@Vu[W,ZR:
%+_7kW:YV?$d)&M8F(ZcG2SpOif%W6\(q(\NVkt!O];g5+cH$Ge#QQ8)%E(aD8Wu&j(D!"!OOYo)!+SAA)fT4YMYXh0R0ZLi41kh:
%T])2q&YRM]K%VZU;@](QUs'RPQn1n@qCSpc=BS?2T<b<!WKteu@i#_6-6B=-:o;Nd5X0t3$ee(/X^6]A!Kj>7,fl=f.8flGVq*\Q
%!Tq<IQkfA3.4U)s:B(/n?'A$eY9uC;48q9J,tq!_<tSkY]%7OS3_,!DZ#N]A%V<`,\sESk6icKjFlEg.6OYa8KZ9l[&.S34S0(f$
%!/BASB8cF+"M94_DnC'!NaW$-Z.l8MMTs09o4L*tj6B@Um4cpHog!Mo@Ku&;.?MOaKU">:"Q;._2\@?)a>1C<j8g.l<]#(*NUR47
%%1;Rbr9m<QMIi?/8=IY#NdD)g[G(fq9'R6oL?T8^3]cN>%pR\Leq_$S9c%ti:!PL,.cmJad/KWh4Z+&m-TEE&W*GT)%)!rpl6cWX
%;Ud+ca0rf5V`hd`E>iPo."X2S=W8^ZbkOu)&>"OfH):/WR3HUhVP'2Qi"F@Mb_gJ83#Z,@_,*6-IE;mfMjNYUPJJNNhEoj`-'q@F
%Up96^aIE=/g;DTZfR%VHQ:BSm"b#g_gu9V@oji/"Ed"#H)-<SFRTriMkf^L/;9pQbk!U.+VjC+*[K:B9*qX"WYkdk0S':N-?"J_L
%)mp'"ZR##X*@\Xr:DZ(5IK-q::e%-#*.HVU\p=8Ina1PXroobFhn=C9p9d_Xci<e"oof<lhn4F:o[WQ:YMtTa];l#XUji%S]q*)I
%q("tsGMhGqrYPM)Y<`!.<P&%[eNa&>]D<G[oXeHr[h>3JfD1='$Cqu^EZ)dB@2U!ZDFI<lo1e&c<U:>g"g:GjIKBI9JK,>e3Qc=D
%EZtPg$LVJ\Bh*]),1+*!hLMRj4[MC'#Mcl)HV45cL]@Qh".HCZ#N>(ec`-eu:?ZUCOp.&*Xe:86`.A4S&/uSO:Kg-$N*KUD_2k"?
%7ftuGZW7I[86^hR"M3Un(qeH<h9OdqJcgTeS5?#E-h4,[,$c+$23>;4C\Hb)k">-I-9=].FjMRS345-#U_\#aJ.u'OkJUgNa\*^G
%Mb1pjYe4m3PBV^r<Y$n38R(-9;j[#nam-n80,Z%Zs2u/,(re7V1f+aSEfp_@[iH(FDB>c8G^5H?R?K6(3:`fj$1gq''<I7%Rc:R8
%aad6;Z7#jO`&6$g_bAbYR-&K_@)9g_h:3jL$MmamrtY4DMaE#^js%=K1RYJoG=q=k2h]5MofUXlB]1g9mSb5PnCHoI([3&s-:aWX
%So$G`CpGpnD_BY7&2BisRY`URWKrX)-gLjqMI`4++O*nfFc9i_K.)FO!UU)`CRD[U_h;5$iMBFfX7@r9-:BCKa\,GG3U;8elkXMq
%+KH\?5*Pk$jto$RHu4CHM4X'n4.c:8PWhcf)D<9@`[^&@+f9H(8Aa4<Oc15Q`'#V=@3J2.;[im.iQVsl1=8EZIp'$-C(ddNh-gb2
%l',7=@gNf''8Y3*)QumEpC)&kXNN2:U+.3X>nM>%<iA?DV95!d;+3u].]$+04@@*0F-Q0?Dm@XC?o==TN1Ok]A?.%"4B[K:]P!Gr
%5<Mq-Lmn^ImWhSl!^)YJ7i8m:EapGLFFHe,U-43DKVB5#.MkK01D\>7m-\0C5sjk)hMOF&nFcr2=sl9[.FBPSMMRR6L$0DJAjH+W
%$Xk6qOSRiT[;0k,&M[igf#nT!=NqA'aV[@j;sk?KB9Ed^Lc8g?CQuLt*opf^,B?n\Um^(c;7a^D!kiXb.\L9`f/UhS5:(>87aAr&
%q?V:sfB[UCD#1GJ$KD%"S\C&,^XN2*Get9^Fr"F='4"XJO\U/854bR-&.F3UQm>%TW0MB4]OnWY(i)d-*RY>4G`!d.kr-RUZ^!N;
%gmukB0crPSIV,a9IO&CqZ!ZnL4;t>le=F$\'%!55]o];(2<L&U%5nAGOm>baR:%&YL0T^+V\D[GK-&3(=(8*i6pD>Tbs#gc*\@K"
%rqX3mkc2=_Ze*3m21[^Lr7X[o4MAc!LaWWSoB7n@#.FjmAmNpYE2cGW/kj2gojQ/S@FKob-\2L8\fH#MaeV<7U*Vj+j*.DC$5jsI
%UUZ-d6AJN,*/05[`ZV!a9UXbC4NrR:dO9/^d;2SA=r"0iM&+C=747_bGN2iX<.p7>0;t%;R0[Oa',(Tb!HF"3k8F\#B.-(@]':4q
%$\EPhd[Ukg#ibJsKsjC4_uVWZSW>Eqh&sYW2+5.br1jumq#Pd!6asJ9RA`3<_^(S!Z(un2gFK%oS!rThWZlc@k?6s/9"YceDn'Z/
%Vn#Z+jB(Q"IrCge2a&SlZb;#-n5-KPY/Fnj1Xdh5RB>Q8G\-O8Q&/F?AKk0@>B3]jEh>AJHOOFKBqkZk\M7'H\J#\9^=T^%e)X&-
%`Z/""\9oK[c0j8^30(f2V&n9Xa\Ep@9VO&k_c;VI'c#"r9RMBWWlBthfG*?`5:dJ1Zc$P[&Hci4T+>Ja&lp:PSIiKXhSZE"Q)=W"
%:J/@>MPOmf8^G%48qDE?/)'2l.:%6d7(_6A4%>rOY'aY)e!^OlHG_kq8dY!D8GD?c2^P/h.SdBtXb$4f/fY<JmtI',*l[QF)@0J<
%%ssf_A_tY,.HF6\'Lp<sC:kn?4/3b*%qm\0,/,Ds2l/dg&09q[i<O36,FU'kB1;5;,bf1;U=ob`.2siL:"0Y@c\[.m&Al:J[1ufF
%X--A;LtYe9QrjSu9hM9:INj)M/\,kCWXr>0ca,&&hmS=T.XKg>f'Hs*<g,f<iD=+:g&4:V,[q(3pO*o:O,7Lo/BrhRcILu[Urhp8
%$uLB_[s7#85ONXABch</0^+[V^c&g-0rI`CMDD=oNJa27&&g`u,*f9/a3nmPNassrf4ji!>Pl?<R$^;P&EjK6q;6@2!'Nj@/M2*l
%0D#t#ZDDq`8B\)Do-TZdJ6/Q^*>`8];D5m>ZRf;fG%>&u/DjMm`.c:11"JBD1boo=#J-q!M:jch"a`"e_F&PTHCel-U+(rL!JsZR
%1U?d!-.r2^rS?/2?C$:t1m]I`&.rDp_AJP$A$2YsKI_ckVj[pY7)oI<$pd9JLG5t>$cMp@npK.n%2:3cFFlq:Z,ctcEX#eSfqjF@
%HNEM4H9*goO1nECP=V*M56O,@)0Lbf@^-sWNeQX;Lt%H1J%?<=.&Iqe#qS\\J[14L;S7gh]'^kI)XB#4%l@OK0'S"W+cR!\4KQ(N
%-`j^0<00nXD!R,P9_%Y;>o5F1pk%`]mpTZIfdLPg)MrCnF0@3VXlY+K;AgZ39@O\RMQ'^?DfoK7&@k#G(,9,o^$03nB:"H4WrO.#
%`cP@!\`q#G=']T^Hu8^67"NQ@8+LG)2O.?5QQma^:elgH+-*%"D4^BnN-VP%-sbFO,$M&!)MB,:R[1q1CuhC,AKbQ=YBJf3]UiXZ
%,U'*&2p$qdfum\R=!SQI,0D]K\%i&U$UQ,,emq3C3Pje-9s->3N)I&S;hb%)\+hiFRRf[brRe^Y<NP_\20/F;B!G5Bj^c="EP,Xi
%>:hT=`U'\E@63D%-p&*!>mFjib6hP9,J$%]SgA(mHVmK9>]n;;*J5j2A@e_nlY$K!'Noc^!?@_u"35Ha>9QJ4V/WhHn#Ur<;$tc1
%oeNN(_5,hZ4HhX[(6f,a6ek%<$.H.`R[ao9WLEqQ@UMH]X%-f24G"OkTa?jc^J(09^r!g,Y*k9=\CaG,no-'2]k7l@WK+^ZU5,XM
%f90D%`J:?n[1]*jG'KnSG$VO=B!/AUUrb'0R8%f^d?;b1%<Ru"0mFjrm'BaI<MuR*[QY,#'-Wp"=R>6M<?q#;'hh;DH4(J+U837r
%<u(lV1<lI:8VV[@#!_XP."Y9[(blmbeJCs78?D=pdF\2a95U3L?-fkjeY:/gFnk/4K+;'/L4E?-Vil\GB2sUo\$qGtT^n?U4CU9n
%[T4iYMWUj^Vi!nU"7\3-?$10mAuVf\R)ImZb9<lN9(mX]2?Xb#a8&<+OA6TMin_PSfoHAJ6!M`qOd%MRl+n2_h'juf];WYE[>(C,
%@gu]qePcodj]U0=liJu)EEs&WNCo2)hDmF1]i$mB&Cmsn2jTBRF,Ka]`@+TdPA"UOj+m"WQZ6e#)q/jB!LtBTpgH(/Tgg#UMcUR5
%$XYAp2K,MS8kF?]YV!FUiS5tWc<bd\`_"2lg0Ao&73_-PCDZ?1;NVgR0XWY7'Ko04;L+XsKeHi*:`Xul#iZ)9$DW2^b&ces'1\0'
%,kt#h?7H489nG-OkEq:..j6ek"]Na;Z&6E/gVu<CYugF;L"n7?,;WA.QLC\_>-EHtYp,#ig/[<U[e2E!k;%q*l2oH,Ah6L*OIKOl
%nPlbi>al3JTqI347_E_aMe8V`*QtfkGmR7B-apB%p\KujW0CEoOO_:@:R19lfr5aLq5UOTJe=7o%j)Nur7"#.,$6%<?]MlV4$KkQ
%SV]I3`[Y>e,SeMQ41*[qrJED[Ys5:hTZt!U`KTh<R$9;dWC`U'o%KdZc758`<\H/P,ZV%k+i[B1X$O*1^42+]X3&ATWb94l<H6GH
%!AU4GP<J9OSqBcnVZTV:XihiCI"Ai7o&RS/KGI?=mS'dkaW[0Sm6T<feGSG,n.ldWKB[78DSC@`UekV`Z^Th"i[7%p#0?FQo4.&)
%76WTn)BHmcXA6ASeh4P2\Ea?d6ub$q,[ILCAO0eS3khCrm.0SnPc0H9./"Eb'<dWQVXc:cGBZY,OR.QR@74$j72KK-Hi%C!-\G9i
%s0o6>JH6okYf'4Zl:_Gc^`I[kmAM[%1hXag"LV[K^6ifZ(?l8?HW]<(2LR:Dfm.]pV1l`&1KcVF[jdArIY^`*k*bX>etG27lH<*q
%Z`VpYo"F7$Os3C>nsCi4Q<b_ATQ0V:A"sUG9W3Cc.S_>5V)EZX>GDpM1(*'8Mgo%N>o[9a7"m@dF\eh)hBjHD&TB"@8cShW]if9M
%a,q]rDnuC(2[8UZY3Of/es0OL)a0i1=RL&9"p9Ec;&7nk1fZ*Ml<jR&/5Kes;nZbCHk*,34jubejiKI5aB5'BU3^q(`'nt8M\(\L
%.i'O"@=4M7/9Hk%J<enE#[LKO,JU*L19W!rP?7O)$Aac4]ia<L8.$FpLj''-mLGs`U'0I^)I<,"dBi4,o^Ku-2-5Y"nC9AD=17S7
%-k`0^Kd^[q&OX[$mc/q01DuBa/47p:ij\[1kgia3#"e#_hVW<!'!]NCN99HX*c0JJhD8mnLoC`&]0D$qd=S0EBTS1*3JQbgRM&o&
%,si%37CSehcUnn&OY0Te1%9Y<[O+t*3*5c^qWTu83k7i!Hd[SrB=`sk8Msft-LIF';Ur[2YNK!17]:m1&#Y93$pGU&d..RA7$$R?
%8tQ[FFd`J,_`!*tl(tYtpI#Z:dd:B0$t7-YEPAu<M/g5a9@+93TQDK++$ijA9>heci[a=<#TII:<'#KhMoGr.LP9DPRGj,2h)iuS
%L)eY-P;KL:FCh18d1I%E(Lnjr@UkY(P9E3>$mOjr4u<=F.4U5,SFS`+qpNQs:dsBeKK-jjX#RqO3&F5RZLR,nH>rG`,2?"O)Abke
%?_4uY6^ZWLSeSS^Y\JLk3Xha&L+JMSPYLPr:g<M<1oT$uK"nT49Z'o3#(J7J\-hl5\NbgkG]u3XiBb#KK3p?rWMUJgTH^+bl`nR-
%oJo$oQBB04.\U'K+OooPn-!lLWA4U81n<0?C%Mg\9G.R4'33)2RTIU!AI:gNa2fe'O;qNjb#kJFGWrEWki'f(?7jc<7[%F>*8.`=
%(,F.8o,U,s>p%-318:omWU^4$]=oMY'Gn@)E'W.*W]_Wjqs4>LIKeM'!beY*)puU7K,F67[@BDeQ'Ls+['Gl*_;S"baata@PoFA@
%%p9FR,<SO)aNM*3E^qiWSGFaG+M]+63M4]nV%\MH<Xk^](42VV:.'A)Gr!X]Og;dYl&8b)PPlp2JY:CjYY,2-PQ_F#"f0-q6*,27
%?7!Q/@pfhGI&guf-#!U%fh;P=(+He<Ctqha_^qSu)d=sfbSoSH6oknu0EPc!*jP:\c1l<3%&X,t[eF;I6p*;]NI<0]%OsXpnb/bV
%<fI2&kFmENq[0F6UEufJ4,2ClgX5*\a^NcW'=p5/'OOsD^GQRB$F@]m:bZQcL4/6C[j2B-%G4+)kEP3OMW,2$l]a*#O[*;[QoGgO
%fSo:n]Y<>r%/-ZYUi8?YD#X^q?JqV76AJ#_`)uF@DtcB5RO2$.A3Ar=]ps1H+bI_G0bMmqR[d!7`!R]W'nY2Wo!5]p`3n3$6^XXt
%o-?(@C!)d+hBE).dGgSHlQ)+t$:)*4P;AnjjFW71e.9bl4Rb?oZO`#Rp>R5DBG!XNdDbP8f!%`adl]V]HbWo+>D-MugYMf/9RTF[
%(m7:R'ti'bmhQoj<]M@YC`YHC)?5Q5TglQRKY?/Ul[s>!kA,,]<;.]>0q4fEb=Cd:7TWMg;"S*<P4_S+icee)RVfnm]),%E-N0pK
%P62q%?)AV0?<"o.*7<rJTQAAX+ZPJGVcrO<lHA-;W0J;<H$^0OP2**%i`\V/_'.Sp:UdLT4AHn4c`!]a6Aa'.G\'<9CdHAQgPSeS
%K+BXbC2AE)@s2@N`4M@=[kX?!b)(c4&p+Tc'm^JFUK\oL&`nHs;5,g%.:dDF0O7$f*.isB#<>UU9="oOHOqF'Kb'-e2Ok+dCUAg0
%Om*MN-:Gu)(5cYV&5cD6-E=PtMZiVY5DO4D`*,i&&'IN;Fp9unIV5`XoepE^@c]mWOg545jLT%td/&UC^"FiLA'gePS\mHu.@c(T
%V%2)\&UicgVCl:5ejHk&=0tr%Zj+)JEDDhuZaT5QGVbko/?N8o.Q/u<HGr7:/UrT)jf=bT.)6So3qqt1YdNoBUu1'AV/*H5K;Xlh
%#r>-oZYShgGABBDk9p>$+DaV+$B6M,0*=teS\t)73G3OGauj=MX4purX>3Y,X%Z@`_A>6<XFk1a$B`kYkU#X1"%IeAf)U<eX=Du"
%E'^l"oC>f,#K]dd\um"b4d&Z!P)+oo'SO\LBp,#>FP0$UCZZikY%S=in(o/(N!dsWDO\4K=D1MhKaBQ6Lq"GO[$1d`Ll1f2<QQg!
%(3f!)H3rlko$[V=/S<8RN1[[l\1F<`pI8s-V`)`#l4WQ)C%REiX>."K6qTp=O=UuPm\C2##VTY59R9uDA7;2^DI&KG-"[]KLiN.H
%]A@cL3Go97<giik=AM!-$``'Jk<TV*@hsoZA(IKB-4T&3DLMeRB%Q#Z6<%_n2q,XW+hUuZhfl:S[KbFM_\M3n,Xt2bJo\*L`2c.W
%C;_`!cbQj_<N7l(4eXTH,u2X50bI:Y>&qc35hMD:#,e(l_hZ-TIUEq4JsG=?1-5/Sc^9`*VWc4>`83b\mi.i:ks<)AmA/PV_?3(/
%<?fXu[?g`\B$h^WI.Q^pD]S:Y77u@+KRt'_+DqMN\N"YI28R,U2n^BZ%=4EUGXN_?Pp)lYND,[\+JR'6je^8(Xqnc967e9o7)[s1
%lle=/-VItEgmhE6f5@iL[4d`eOq14H=tg)tgc4lkg(5OCLPRB14`o(qLbZ;rOM70r#39?IP'HMfEIO#<WG[q'a3^Ys.XkUCblb3u
%g(sOrH:PM6Y![piJruQlm!)8pS?Xa@G$*[c2r2N*$P62*#O$FeCDbILLsOC:K4CHe0RBZ*@0me^jt%NK(uh[2KF8&_21#,Y9OY$=
%`k9:l,(l,<7TM[6Lq'5f_;<TgrTo(l<ei1:&phVjmnc:!)^<Iq4CRsT;B]u5BTiVK&^F7VecQO;K=>M)Y$)\t[I3L<Xa<ogKZJ9<
%V<)f/KOj9$:B3J57-h?6.2524QD2]?5qI>58!jh:bSR?YCeFCqUPH?E]@Tp<\d*T)W!DMQ8C+p`04&fF/ltjY'GMI?F2h<D(.Etf
%F$ISB](ekhUhX-YL"UVYOE8n*7WE)Z6aOk%/s\"J%1![79D!%(k>6f7Qs7TNO:Dp<),Va3Rd%t3M.AsR7aPUoRLfRZ)<@d\/BKCn
%IS*A7d?)d1CD8HC3<3B*HR2Z^6jPiSP&2,N-%UDOn(R<F5ZeDi%1o+nGJQ,Lgu@s6;T8?K8(qgen4IU_^)1Bn<><ej,H\d."/DrW
%;tQ/-U8cZcbXCR@?9FZKe0@<F@p?s'6ukes=>GXWJt3<DKA9oKbeJ6I#s;hRDeQ0i5td1pG/jXYKS[S`h"X_:m"(?Pj9d(IbM:\7
%ifn!_bM*jaYWb$B4$o*je]-`[-DjQ#K>kERp)JrB4'-U7SoNQA4Fi,#;LMmu*f#KCQc6(P[?7b5T`"&`l:[2(6!k#b;Nj$9RLg)`
%`&)U81:RFAe^\mUC?^UJSQlf]H@W32cU>/f[]q[>:Sq]Z-lf%d*AgCoEtSh$Cig-<F'?:i&A,n43^K_Z,.r8?nOX@A-<fEb*lOFD
%`>'$Cr#ij-890@%.adE9Kjq>9qOBt1d*qB)H+F-6.a\;0,M,iFnS6i])Jha"=5LfMPF),WdaBG3]u6`nltthF-22e<aN.R36tE^L
%RLq3k)s$@YlNje-Y';\7$5)\j-g>F#!TAF.qt=J[Iq?2c;XdSl6s[PU?;0gRn1#t:<@!g8:aSbtS3(QYRbL*C.hOEO6i.-DVqc3_
%%p%ZsR*uJ?:@#'`23Whebp0d8UPGMIL*UT[ha;$';4:2hXF?E`\P'^Eh#[kOn8Q%uT5e2dA?deb5=.DEej7dC5A0d:0+uVSj@V;_
%8$YdWLk9m34>R1@Zk[K(1#P%8fLTO*l'ir,?;-#sh.ADhB-;_p4%)_:\[lp1LmSt_]+?&Oa'aii3?]<96BQd>U6aD9ZTJmNr#.Ce
%;9?607Dm<9\Zi<Nj(d`W@[NLl*;_Gn#q+@:3jFS#+gR%e7+<>g>7CuFZ]H[m,#Yo6nSEaVi1!?gn?$SWEi"])M[lh/#+C6eC:E+A
%'I%ldjBDV7@sY9`A:ZG+.l90L5ncnsh9+5K8X=[_7=.QDprCRL1h\58s+RaX"K@%SQYL6X1!rf>[GC-l;+FO%PJCnq!'=C?q^L>e
%_GmKd^euSi7A:KcZ$m&.&58PB?7m08"UgL8_:dEOaV3i;8K#lk@;:@`)8RLaRX*(ZOX,Ssm&I".K".>i9M"kkdh+i%i-_T(o2Y9*
%`h9;.f6bWYV41ksEOH,RBhcN2odG')-Au*]U.U*H#ORo'l,R1#Wi?X&Pp4!h9-C!i<=h]Yj/O/=9D'6`2+0L)/+OO!Dk0i4-L9$4
%6>CMT&p)7C=_6R`;8K3"j[esC;AE@:cq4/ke8W>WigkMp%Vqaf!Cp*WmD0Z2`ZSugD9[6I#t)Y8lr)H5-A/'gJ3/V,`FN1.M2XbY
%7'e*6<5>Js=piuA+,banZCX\]3,!8ghB/%EXQQUlasOA*qDf#,V)+qnVa5)uj9tjulK!mUW38AiIGg>h8IsuaaM;(c*o_On+q$A%
%`f\]Bqdf8B;CPP#=i6-_.`\i=3RhX#N^9<7+f,j*.%gPkLK$sc3J?oTbajl"](RZlZm%e)>]>\ujnMAl+;sgu&i!MDd\?k")QuO5
%_HNlBa.aD&77,R;#P.49(8@D9BFap-eWV0[B*KlV2^:Z<^up`UFI;\I@2`)n*PkZSOThW+aW3?&;d)@\k*uJE89E+Xj)l0[2`[\!
%e(7Q41C9C#bY7#SAA-$62+JoM`1g(EN^VY7;4ZGj><]:<]lP)F)qCGq\I,IARARnj\jHIVir.77MU[rRN\phj(1Vg4@TB@.[Cn*W
%Wq?o2+s9O/HKSmB]Y(F&.inkL*6%%&RX`UKrkc3TL?-QokV7Ta%;p6!@o?ek*DTj/en6!gIrLBWL<.@oI[5mY4_W&h&_kM;es$3d
%Qn?uKjooF^XYnZ3^nW<h95J+c:`>Lk>J4EU^M5I'k4G#Q!)`Jn_[G,XTZBP*E>1f7*cQjB-Cr'\&@\Ig+_Dk]?8ip+0`"nl9n=]1
%UIe+t@^r7e>S*>72(!VWIkN"'^JKk(SJl!:R8$3cD)gIU<,J[B%3hfl_@*Z,ZA(@@0/\DoLB4^o"=[Ro$f`KW;$R"unFPg+j*>eD
%37?.GP#d:Q>D?*'fA1nJ3e#E6,fcQjK;N1!i3'2?GY<#\kT].6*^g&N9<nDZ,t4!4Gs"tYfIeU@]VTm?kg0Eii('CTpkOg!J0l&U
%`'ZqX[`G2C,1s#53?%(8'>ab40/Sk0'2+2`7`:runD/>$:uF7*/*8#X@pjqoO_!Zu?)'Fs6&2PY(#f*;(EfI1Z=iqDg"n:$<BT*O
%=KM=heJVSO1_c.!+_Il:S]2u#2G'kK]ke9Dbe0+e407R829LMLC-B(WYZYLW;6IEK#"(e8!(t&Bb]Nouj=J[ik*0NcR7^_L</?X$
%ZDARr@%o'KMp7De4e+\mAr30(,(eM=1`/:T%cRKA>j(R(d;IutHarrq-ed<;GNsr=La*CW0CV=P6p=2tl3m;fKnSJ+boP#M4\ir`
%X`774Ffm=.rikY;:lF",kV*1-rQ0+fZ'AR&iC/%mM:(,V8O8MR(T+N9;[mUTj&RP-msbR/UC&#E`$hHrq@;>Mn0gQ^$dN8KF!EPo
%6*&R4fh@2A@?N2;`B$83DOcR;`SRS)a+HRPP>*5DfbEr6.XI[<%YVn_'6Gb?E)JGq'mYWNpm+P+/o[hZ6_:"QrD@\uIE&%\R4fi3
%;>aaQ(h6pY5J?ij#aeJKmM$,),Xj6H;2MWAg;TJ4D@$cBRb*k@&\p;[21L]Wm$\EE!i'CLNRs4p2Gh`-6^VVD4'5GKU;YF3!ERY`
%e$UL]mjB4_FX^afZu)Kfrofd^\N9]SOS"]\8*+3+jfqAiDl5Ns0e:^@,A`h(Ss#M&$/JZOfQ7@@#$[Jqjt?)cWok'6cK$2cbQC%i
%IW7j[AN]K,$#<EU3GJq`%r\p)M$_B#R+&`?!_@_J3]&kVHa^s<R*d$N[iS\?2.dL3'On]j6HfI_`mU=\odljGb6$4NEs1/@O\B.G
%6s*h7]<e2dWYl&(Kl%iG5]efSe+p!hS4@1udflnXDM/=2=U0h;c3D&6&.^l%29j*V!\;.b=j^?XiTY,sY<"Ijh$/]j%"biOG^7c]
%-R:@QeDR$)Q^\m`jC'5PQ8V9VV'sD43jjVtcILV8ek#<;6\^r["5$uM"6=(j-!<ZeKsRMlHG!eE(giYXX.)O*85+iq$oW\F4]itg
%?EIUD%eSh=V`Q?A[G=QUg:&Ou0M&UA?a#_nK[GYLTCcg17c$sC&d&oO@<3pHqjDh=&c7rIBI<SCWa$8++:)r:Ua4YOpp\,q"q+;$
%Huq4s6np:Yn+3hNE:4?86hu@[R460_F!?6MdFNr]KP;l8B;7#+Ps9kR+:8\dno5,UkNmU:^SKO%$.4d'^tXs]T9U^XeQp!3i/B)"
%4uEg5l3kIQ;2B9+/8/c/(@Xq3Y/j,_K?[(U`dBPH:/@I)OA(WXZY9HCY<(hA<AJ)m=Ot?(%4&M>!]VpuEtpi?=B.,?VRjEW%-mAu
%'6"A+P;n%a^o\3>BRg'p]f-o7nM1\,8NG+g)j5?[\9n@f2mH?4p5*HLfGL3P46/Mg0%W5:&di2^6\j+.M,2..[UMOQs3b!L-B[d+
%_6Q==/40f6\b(:KCPBHp<Br3N]F,&6lKb4U=q@[(S`%O63e61PER$[ti=bXMfSU9-6Zr^^foV\31QM(<"`kJ2i^]*/4kU*Z7WRrd
%mF<"g8kXWDh-En_Lo;ITCb*_%e$pD\Rb"b?1?kc>/8kJ#PnfHIeI+*)!!iZec\pn]I$?H?pfL;4&dQ&b&Hs#;L_dSIF9j0m&1rRd
%oj^mu98FT!jq;jq-]UT`d,>IJpb^cpNL2FQY.n@-'XLCLQA7j*`aVE16E:]acT!dMWi"KXCuXX%ZIt+f<QP:P8)s$1Z"VdKlQY91
%kGB&Vau(]UcfXenlJ<R3$iIK:R>@B";A;fsMI&1IL?b>T4OYDs2-8eWGSO/5$c6+/l!t];SJt/q(g.#E)JqqW[ga)#($q3M<r>`P
%"%JI<r*6-;%+.$+H9ZCCl0@8e5QZ%-iHZ21SS3-m\`G,[%=2(i?pC2K2c3'iMKi*Fg6Qr:Z@!9]*h\kHb"cM8K<:sI]<6]DiJ$QI
%Q_8F+:i9->C^nI:>4k9:(gYF]fi"80'ck;kKHLT$m@+,nc[Md;^^Mrm=2,a23rfa[LN+h>r$/[YB\9=(\0S?cm>JKDd3?E:LSXfb
%8u8kPWR@VK2eEfIp<715h=AP,N"l;MBPL28H`T+hZK>>Dc#SktYfS1A2(-H`#'SIH\O9=aFUK2p>H=%5.u38'R?t@4^lKgQNeThI
%W5TS>^ig$!@2TO+',JM/,oqhEU^R>=Qf&-1g_%D[e?5%U&3VnKRMopmC5T&NRjp\$C7;A[OAZ8H$LJg^#c\35]J$@i,aJ6Zd&8q'
%7#2:U92mX%HBeJ,(2;$`7J)Tf".HGMJ6LgW%O/MCjdbDR<0Mi',uaMjL`/rn@)$&&Fpdl5+?W2A#aSShih5_D(SdjDOS[-+'?NO%
%W"S\oWKkgT_O=]c]6WQF=Yb`"biq4IN06/YLc@Ch(W^nHJm5)uR[_&?#h?;)7:Ojh`Q=N&.#HI6SD^a0]m%aR2n'&,%h[tNeRT=;
%C()J*mUi_J&u.,@AU2cb+!HB"FbCb^XuLSm2q8osFG*(_OTqh)Nm:j<91%#R+[aM3ns1;+5=7;oX64Et6]r4dPloDW*6lWF6s$a<
%8Clu^kq$j'eu`>*hHAVVLKokO7SV6TMGTkN.`.i-kU&"hf9[Z!BTJhr2cjrjpDkAE6:Vp"B\9dH_H]IC'$YqMM))SR.n^-'7t]un
%Qsap=/I(S<oIoFq<h][<=PU)40!_*n!;:.#:><<(<g01#/MDN/G*\4[*_hMf(/jWE0Ta^EHl51m@r.k>O+UW1L^6cij,hTp0s;3(
%$#Z54A2?D[*>D'IZjs<UL+GgCD&"[P"XW/,Qf5Yaare-$0=Lt?n9L<*?Q,7\`qL(?jpA)MprKINQNq]!>PO-N&`/I1FZ;X;4)aIe
%K44EI2t_g"@cFhc)G6Ej"h2.=_="Npl*lN._dnt_'h==A)5&@c)u"=>N0tm>V#Y(u3Nj@5/LH#d2aVZ>]jr/-TEr!&..cm5-*b,G
%N]\ogAZ^`\Dbr_gSag_shCe_b@dbHaS!;?<O#,7C[8gbFC'LT;`_iJTHC>7bVn]E97O^uZM`,H,ToXC^-kSq2`1C'M^jO:o37rLU
%0q.f;!p52Ra?@JP#$OfS7cn+lO[VcS6$@Pm*MEPf$4`%Yp)]nF0Z?(cR"AI=U$!pE&@CC1X/K7AOi\Yh$u-7no13lSGpr&OSDD4f
%%#)l:j9.PB#_N4["g8+!eeVXCWpcL`_O=!PCuZ-N=*]5EpUP#?]GVI_C0h<%E.Zpj<J4p[#q"Z"*PisS':0JaTrgh88.s7@*YJPg
%@<7F!."-,94tqLW3;O;?Knu,@e8<P<O$a:)d^o:??8.m[;3`V7rCO$6hTu;lYBH!DO?\3EF_J.k)$X+KX#WXrKdhSK+iNmri&T,p
%SQ%3BCGo$!Z:UFP`,A:']o<Fk.`A-uR&3IHU03VbA5O"lZ3VZgco?>X2BNc;h$S8"G`-Jci(m/["P,]e_Kdq9j[hr;?ELYj[KX.8
%.\e1=)u*^:Fc]*0@Er;GWS<?!&B)c1KQ!PlRF$#0;,"%_n/C>hn'QY31GH5&8u"NoR@/LGgp.rV=%Q:F=&-"Z.0@jrFQ=HEcVh<=
%p9"M+Z&8N-7h_DXlc@53%EW";0XC'_;heXS]LG<=b\,Li"Ke^PJEtYB%X'kRHNli^Mr#Qpbi0'G5"["0E)=e'6;1I?=4P9"ciKjQ
%29)$Q3oIf6@RPR:i#Zk1c!1LJCT\<Ie:PMhFBiF#!A<jaXE$^5?Y</qg*VhG+"nKA1%("0>1EocBft<_82[Be3AqlO1nOi1j8lgo
%0qa<ebNa,WATLnB:i2PX52j_bM^\nI*g\>Mg?7&_%#kV?Fg*Q+ZX?YJ\T;2R;YFdtH>u]3bLS1@'4)L!&"'2pLDF`P)EJ^n#91&&
%!?'>(Uj*9_@%\5tPR2#%4PpEnj:/pJCJAZ"*4/^^p-p>M!.,@t[T>UkI*d#DP#]NNb@9'4n9T1Z*+HVr.@aJZ7*di.*;=.*W*.V>
%*O)-4N:]8>KaMAHBG&dH=os,J!LS_Z1uWH[(h!)g'tH+.^P36:C;Q&keKHM)-@E:mnOgG`hIK^r0B$^*&=YL4eEIGPECq0kMR<%=
%Su)S?X=)A_h_L$?;M\KsO![uWd^,Ei.QEW&:Q/,T[BMWuMa.O0lXmnm*PQdDKFf=o"4MXHSXF<B&tdGoF5SE^kq]sRHn%!%$Cuo1
%"c&B[]$H0H0U+5'14;J[1pm`0U;.re[D),q%LgmZ8\2eZeYD>328#%WLB4/1=3RM.GF@ZNKWlR%+t`m3>Ue`B3,!/5E<*QoWAFt=
%L.N;8Co^UO4`fV9G#2&G,?\.t,/saUVcLmu:Drob@Zo;p\'Q7?FOJSsIi!R>);k\5&B;M!LRJB$Lc^;VG"e^`N.#Ho$3nL@?e.S#
%;8e,*3nj)m!@`_Y('cdV.rc)ODNqFmQp3\;Z4/#mIJW2b*YTIX;bJL6!#dsKe-Zd,_?&?>DB1\=rPZEO<+'.JbD;V0Sam-e7.^t%
%Bf(4t<XjgJ_4AE_1aBrb!.bOIbF"-pjcFaY8?FA=*8*4iU8=]6'180lUulc!NSA,hI>\rQNfDnnc`EF(#>_R[:EkBHAlQeq@^s19
%*(kd1,L,#TmD`@5,FkBA)(JP7,FlaN,A0aKmlZ=1adY.=q][3rP0&WANkW1?_.+7Z(6DVrVQmW>Q;-5TA'jbM-#l?lI0c*!7t%%7
%&hAr^GS+R,1$]6&h87.A!s-q[ou+(fX-lg"-BXVHq#7#.1_R=3R9Vm9R)Vi0`Zub&<AZ[&H\dTp8A2r09]jMr#M40N("R)#<(W=i
%$8$IqLhiWL_#dm[,f""HiI2LmM[+&pm_0G]qLm4NfTr*"[LQINKGW'30IfN<hFHqc+ulr_6=qb>Tj]-S?-t6.1<7sLA;<6CUtmTl
%*C<069,9Ub/6Fi<4)5S>O@\7[P.l2M3eA-*8>;;caPZ@)2EE+E.b..%6V;oH0rlr!./LG\+J@7Rjr-6?:c?5ZQT##m/_a[noR*#X
%[4N+[!q\43e_8:>Tq3b"?q/GJ?Wp)tF<L@!%#&(K2eSG9=h\=O7(@AC*1T^SNCkT+9>p$;J4kb*2tX?./B4aE`/u3b"^nL`N_22A
%V\[Iadn!:%TpVpP!)l*qMBBk,Pe72X>GbV2/Z4l_/YU>5`a`+)0WIB<3<fLRNHo=*XAQ.:/D<IK!>eW"<&IfBCgR#G7%XNuOs6<s
%`W_E-UN+>aJNB1H'"(:&ggEW_o:ljE-Fd!<DP^SAaBj-!h_gejSUnsdj)X>PE)kJZF^.R*!hu0/U%M&LQh*ZZZ+7l@L`]dfI%:jh
%;dZNbB=73+\IFkr_=jM3%1U?"#!D1ZJT3Q\4ga"lEa2<1@knJF)H9H.R$n<nnn_@/fdbU?*94AX"DbQbN:@l3Q>>/XL_"VLj_-C%
%7BFp>!XL,=EMI5K(!Lbh2H&S(OfHPdr>>0?abaiDSb.q9.!F2`j")HuXB)YFe,Y#K)Klh'=/eA4GfrM;TZ4f60Ej@]op0cj9`1rU
%':L%r9N+>*(l"h&>Bp4dN'LlSq`YTThY:5:SI`KWT,IdJN@RtAY0F%"PmJ0km'RE^P6j*`K-t8(N,(bF>EQeBRG$/BdhT3B9'NbK
%??G[1DlP'Okdk=;'`Er2_1b<Z%npWi24r6J[hl6;g0's`YL6TjJ;>*p:k9*C`74;oYOI1'$D_cE,HY>c;Qm+:hH+>R\cO8^=1Trj
%oO,KW7#/4[e>2Aa+C6BmI&DWO$]$SfZZJ9*<^i%\%FAbgUqOu]NAMr-.JYTOEK]uGVaLP=/"mcJ8l\PkU32CfCn$o_^M)2ehJA</
%@*67N+PnORAXboL]8<K-4e)-Rq4@;ei->^qS"\/2Ph0ASX/iq,".[l/.j-9/!jAGU.ab,AMWbAWBps'3Fh_Z_AtRMrJ#F3Od-$]]
%cQ5K=7&&ZM<_b2Kh1_7J5]d-;(BKlLcWP5s9S.4#+S78J;C\VCE:5g^iY_;7=Nd\:-p/*R7ohcj,5"dKZ"FUs=&ZBrC8dmqP\MSi
%5:]A*F:E"8984#*'`aWY,$iC''EbUD*lXrB\esD*K8No<"]cdU0#/_5k]*<6"WRWa[aZA(fi"j'o=/CbJKZk2@Med>q?S*Bpkn8f
%-746a=SWV"g$)/=ecY!;.P]+IA-)gS1NZn@DusDI*#DQbU]Cd6SA/Wdn!$hXiXH6saE67L@KFr;mL-&QGXKDXK<FmKQt&uIiJ95i
%_1Uf39/R8GoumJo9XeoMp5T\35d6sj4A^N::/,*g0"tqe)C4,ci<FV*Uh*U>PE(+n.en>QX$t`b_*(Yi"'0\9BpVFN;d1#`m-M`h
%!5qIS=r(Sn5];:cN*_&>5]:"q(B^[+(\cRe]`SUKk[Z9!T>^j?/a1I0UWs@%9!*"p8o%<5]@g)<lY?YM0+X8\b)1N1;*(2&)LT?i
%3**VFl+ghfeX3k%pPmP+1g5JV&[C+Fr0l9HC+uTa(!i-k&ZlE^`+pSc2%L@jV&j!p!uq;AVnmsQYCI8PDMh!$Wj7:,E^YWV,,eMV
%/mA?!V<6D$qd_C>Mp.m_<JLK&b6*\qdY+V<cre6m7LH.B>6I;oS/NN`73^_FAu!EB0kC4MnWp/)6Q7a[-_aE#=/i[L-VEn!).-8g
%<\/Qg5o!=iEsOZ/Kf,RD-pm?7_&!_d]Z6VSjXlt;o*f96moI>hBt>,4hJs_kV&&8DC9cYX^noaZRd3Tt8rLc8a^S;F#Fs\))HiFL
%d9GoEgL(BV]]r'k?'[B$p%?[OdLql<W=BJHL.fOZJEu+inq_CMY8C-`N3-ZFalLo[FEONUW+IZDj9u-Wi](78Tr3^Y4bN(1mT0E'
%SD%!N,;\8>jbsdu4!\([_,dWS-tcJFFCFDF$]tK3JZ&j<@:ICMg^I*s$_3mu'<Qe.VZtUtGRH.tih@q,RLW>M;,D=s'GAR=3E>Xb
%UE5r:"h;!E_+TU7N=m;9-`#^*E>ld*N#aCj=l$994QiPa9d,<A_0CqigEJ($@),fiN6hq$0eGFTZ68GJc03?I(+p-,@K\[*d>FJ/
%64/Q1Z!s[Zr%V%MZNVEAE"ubB!A[NllBURG!!tTMo9?,@du5(i_&HB<U+]HV,sZYW.->24;W8o-oa$s9.^>]T@(*R8m_O(fb"\<r
%08J4t[fRY_]X%ik/!)O4Cg)DCa\L^^(1Y0?AnOt:]bm;R`ft.RcRt5\&q*m>'g4RSTTFmA;&""Z.WYh8*@o@j"(0NNl!941-olY$
%b8l/]`']XaD6EVH8oK]*_4VZ!Q:2$mS'-?&4J2UUiNTEsJRTA@O9K)glmp1f+iFU`-O+m'UKaV/O73[l^856eChAFC!2j,J90NR/
%ol\@X7;W7&C1k.I<G&!,MbgNiND&s:KdDj1:mZ6hqkK!]%3>nP><7T2DOPPS8B9,0%r4K8CH`@Pk.RROBjSJ'7$h:^6?e)KeXT<9
%Z<$u=D<Fne*?h`c.HAL"r?MK=@enB(/['gHX8&*\%4t^sd/7q<1GoXgk9Z[jgT"MA/b51.fEoON_'3WKpOZ799a@Jf2'&m/lWO]J
%9;&<sL[Lh-?X1[[=M[\,@M5eYEJ&)hQcWL9[JdE+<4%:8OSndqbgIOM#fT2"("I]t'"?tQ.C^OfAeFoCT#%\^X9kiDAReQXLV-`S
%)rf7Z`[]?bL*-McXs&gEBpO>YW!E[W\92&`L8f`d,g#hh0k_gBoh\&%Um`NkP<+M3GH1?LV^:Z#5A0>L)=]HFR`#:4DN5b1iQu@4
%NmgQ9P8$09C2fruO<CD?f1#+.Dft`]F?@taOGr6UK(,f9PV:[YV(l>Um:uX)Y*+(9i4p+j2+S]$hY2[W6>G3Mia)a487q$t8$+Cs
%UdI7Lnb'ldi8g"(3"h_3<suf\,bgaspar#`("$RWW2AepXnkVr;Oc`IMuieh/GbRq%!V&NemSGg_.6gNYG)7Z!G9<GZ8kSZ;'?A#
%K5D:I\d`osCY7?U*e(5iL_@Ep9S$1b?6#kRi^o5A<5WTqha+W$<!@OjFHL/lG:SRk_\2&ckSe`/Xkl!lDWpt^/DQWpJQ)[tpOObA
%XTCM<m^)"FEbGiJ_NFi,KJh3udYqm["X79[nm3$L'<R(p^ul;,e$<Yg2'_*RI0u'9U^)bp'4V2=)]"fu#;uUbK4@Ej>fat;/J4]A
%G,557*19;fE]/_4Y"r,=X#NR@HO-LBglC?OYcD?YWL"H:8.&N3KHm.\4n?^Ja)6,32!3R*6][*Vd<+5OJ=&ee%Rlq[8e5L4$#>[G
%"s0>QXAa>H$Dri7PS*PH6JJ"adNboL`emf))=gLnOl06fd,ZNH#nolb^p7/iGD.',pLm#h=.g5fAC[+E>)u[id5W;E5V*47%h0Bc
%ZkoNA=osK=*RUJ(@XH?lEho,$;it=P'oP>5$8kN_E*B%chnnCp.Co_e:]SOK#Diqm:.AlnGO!"qKu1;gM+(UG0q+qi&jD1YUK!R$
%JegTo->Rl[^01]hJmL=tK\s)L,dnI?.i$$4XP$ul06G@X+VE-bTV"9C2t:3/;VR4GS$4/?66o&MU8mL.T;?cq7k2j`Pe=t<=,Mec
%bQ>ito7b.0ge#IO$"Yb-^2n1@Q8:#3Y!p<`+<VbpLDrP?/=BT'@9.!j_aDA<"_!#!W`'tAUeTYt<^W:Zp.V1PO>AkWBij6:e/l#/
%G8'&Q=m_,\8Fi8f#CVsqb[hg1DH@N(?FNhT/OhAK6;Rc[/J5BEjJiP@*LE'^DQ/:;e!alPjX_+7%Fir4>DBM:?W&ea8M\tl"=S7j
%o%\:Q4?$o8L(%r[P;pV3VD;PITRMo+oasQ4aR'BJ8r@B*S74":0LV_'q@jqq.2ku,#!,0uH\")`Uh$c%P_r6SI[\GN*m7^GgQ%8"
%X^J7T(/b@MKD#A2As]X:g[.uk!32aKY72BLS@>"(6(`r]W3lhf$;Li-%]X>!AjA,B'3WiGGh$@r<Wp<C'cK,2@Z_?q'!iC122/g[
%1sA*1OVG3YL?K6'igOIrk^`8+ngq4t[lCbC)q91hbA#g/<jE_G*K!ll>]hqon'HO4/;!<d;VYKos-OXrd4`D>qTtC-O;Eibc"3H,
%*(*GFdmUV=ECZ6//WNVPG+csR[-ENC4hcXh:2-REF$)3aZ3bL53^Su(p]aD9<`gg$&]:gnAi=JsLj1N"Vo"9\@kBW'+"[NZ6*HJl
%q'ipE,VLqHLXrn*#eZmt6Oa)U,QEl8TkKjW,h!I%68rmo&#.ZsSg;k@HFQnNIR8p*9t>(3Vt@NF9Tn?<Bsf3%.jc[/QI(cC;cg19
%'XY09&Xm*hO:E$Xr.J%$KO:%6XFpp`)G[=l#V"<`ZGAnLbH*;Ka_j6iK#Ear[?*'SRk%Z@gPPs&+Ko6ETl%6mBFdDQ!_bNYf@>n9
%P(l/npb<VPoh^U8B`8JR2)d!<LeIi*$<_D@XTJafB%EeK/VqguPAgh9?7;hF(/GYi91^=8<ZP9\4$g_m-KQtgA6S]N>@lkKWV!tE
%l%J6i<b$?j`\>CJA2bThVM,Mt@P)0hYmQ377R<%E#JJ-DAufFmq`5lpj!%kUNSfN1^hh*&_+I+l[?Au%6,?(D31DUVAphKkC4$<g
%HGLY,?W`V=N^6bEadcrXe-O8@]!Mgej:h#@feP8uLD3s+`6!bL+NJl*JI=o>EQV4<?oqQt6eLcmf.^_^lEa^5!HsM`"We`hq3XUk
%"f[0hki$ahgpbUfZtkWg<mBdZD:JZS9\#-0!/\/d,[:l:)pkB10('KY?oI\KR$\UA,e=^=/#\h=XB)&_Qp20_%J5h1A0!$J<p'qE
%dKMT3!A.&#O+N)2"g$:';eA@9%)EJW]bT5>:!&](dj8qS7\D/4<+JX/F;e1H\S0Kb+MbjI8kNq$qT[AV3:K99PKP_a%!]^*;2e]!
%g@bd("apPj,=lNCDWqF?ig!39rWEoo,"`G!XrVF7.Z?T_F;TaE=R@up8uP?u<pmH!J>TTF4R*"4EPK)od8?r`QH`p@BUi+-jEl$0
%?_OED:;BY9rG8jU1jZU!d9>RsbpHKO<rl#tYXTYN15f%B6c>;mD+_+/"aKG3,ET`%/^d>0W'`;:<jbsdM5+8/S3iFH,_Tq]NCT7X
%`37%.D-"7r05;"([&EK=J>rB&PX/>s$$RMj]emW0Dhd=4!Q78I5(b'F)3llV_9u-pM[OSn\s&:M>Rn"kaEn)e-;fWN'brB9%C@f+
%d1l;cX]90C7]]"K01r.PR>ok#+q%,R.ue$V?9Ps\a#GZHKbTd>UcDaJkCFN*)tPB+6Aj.:oG$n\!Qr3_<f!ZZPd8e2)Qm0bM6lcb
%]FYNl9P)URB%]m1E@s9c%[Na&B=DIqLCG!7SRmp<q]5.Tjj_Dff=f%JOm]%$Ru1KJKS;%+SHA]%IZJCfEal1P!h;'e0(#o#[VldU
%dTWB`1(B]h=PR2r1mrM=@)^f,GX_?Q@`D'/RrRDu@+S"fBE!1iKAt'AWd(T5N8HURO&e\Mq89%#(^Upp-b'Dj:Y7SX1J$lZMX1ho
%*;kbrOQ=?<3hLWNKW#hc4]E+I:VB$]\21QRC^9sVeV-03\p%b@<]iG%"KeFk35@:2WpVT'%qTd@AkStVFUV,^P=?F-7SLbY(;+3+
%nMroHcsr`..:eq[;&GdSUo+4S'`!U>C*bU'Y$F<#5Dt8."hW6`GA/fB9hjtQXE<^uj]CZfKWq6UVU?DuhlS?cjU,T??jp=31TYLh
%m8NIPY]=7'K7I=%FS^XeGjEAUhd1_::-@!l7PAum?s0Z!d_dtWa:P3;,ja!Kc5"U"mj/KA*KS;^?<S7G*TRr(>qKV:0-R',a^`E5
%]u#CI`9'o34>cYk"!FPG@=m!2'1=nh1UH]-TtI76BTRrSL?iFm/d31rBX!(ucm1__*T=]C^VI>8GB0&Pd9*LhQ_5SJXp`EPU9ZT#
%?2rg1e3Fu:_@+;YA;]BGWQ8?m`MqaC51m@/&V#[k>:TbHB3.6X@4AQHX$^l2Md5jSJ]4<UerD;<'$dI!CG_5-'+V2m$ITVDU/*R-
%_;V6!RUu:S$WHG\e'Ln6P+eJYXr^/QAe6W[*7b5O1H>I-(f^8gB(Tj0J32^&i9E@FaQ2;.b*fI#O>k+sGMs$-VW7_BU)C6o=6-dH
%>FH8@im""[o3uC]:\0JQ&/PV(RGcOGpOk:`"%6TeKHu=:ihr;WpmImao4>GKmC^O]q8HSc[oMf@FDj3J7n?*Lo=qmPn4V\Oo7fal
%/qTKVh($VOI7jt2Wn"q3^dPh(G]I:R0u=Q*U@-?H/$l+@NQ9B31b(8;fNd2QNmiM/VH]TgU?<Cl4[1lmM)&C1GH?=P@Mf"^?dg=A
%NlHfq\lj.2)d",i`&`,n<Q%&T("T*E9XG@O9C6H/:!/@eBg$,^./)jAoOrrMOT@7oagd8*\^#>:977]G+[aofQ6/O&3I&OX/ruo8
%_9!O/pCobMLZ>E-lZaimR%PB`8=c\&IDNGS0kgPmB;&XF!.X3eTi9Qh_[@G,dI#$]9Br"D2?5duNg!q[FA#i<>)cYVn`Ai=-,"ni
%%<?blfWU4:9Q,ft@JQ<l84j_j#u53>]/^@#_"//"\rB<S1EklZ!sBd/*0hqRd_o79#s.Q04";GY>BK-Q2$#j81%VXBlErG0NK/l6
%+rHh$)&i_t>?WR-("e#%$VNXp]#5dh<Bgb%%Idu7=UB4^8mT?BP1@%64ZF:f(s65qbmA'*%e(SqP*<.f_`*K#TQiJ!eh[Ba_K6%r
%)$]_@87otg9pe1M1Cu8lng)Uq6j$oo#aWaq!g!]oCTbRna)^SuX?0mFB;,X5]+L/<689Sl=WVGB4`l]AdDh!1jrWGXiA,=R7mp8"
%)PGkfKDVE4<798Il5K3Ke5EU"aK'AtI:ju%XS^35LnJC_6HF\-&J79cp!',8.%=A778FZr14[H+i_F,1!j@DnY16[GQg"\ASnV%;
%PVkiu)C/NDPC8*-LU"oOVIF:>6?fYlCFE*lJB_2*d.gf\aDWpO))j+`aea/S-<bY$2h8/J;,]")Cg=]OV;)2#XWbV4@**FALa=>4
%cDdp@a5/%gQk##LhN0`hHeBt\0,aZj%BUl5D-!lDd*lTk"0\m`M(g)%))E6-370CCo,cm%6A9.$".j_XV?G'(&D46?:TuNQ%rW[)
%&cQQH,=_&6kQXcn>CsU(q2\I.%,X`O):I,U24S6#pfi<4h9;!fU3$f1X9b8$p(LNX!W"^$Heg(3'MBL5e3e4p;t<J6-U@<Uq(YMs
%3oj8Tifj1S8n%+;UXCDT@c`?+^VHEa]h'S\>T<Kl4LTo>SM\5Jl"S/=OZ^fh?&5Ze];%**CgAtm3n#VMKaa9E!s"1(:e7Z*#o+^p
%a#Za'.6LIQ&eSsu_!MDUb<]NGa0+3c<e7!e7ZW;D$MMFLCW@\3;s8*bXA+)Kc<bBUeI'bFVP5RE>ahU/!L&hhbaR\rh$-XBJ=!CZ
%CG:S1MoI/b&F=/>PSg-a_Vfqt<IgOZ-H?67Wn`^\]-ML-CoN^q"D&lk&<Rge'_,.Y'6kp0-ASr&7=sDHOBQ_2G[nh8CF!XMc`].E
%h@(H[LebAK[$XjFPt'lTH4?E2FuRSITKQ4-k*?E`VQXYi1GkK[9+mO"@B5fG#grlN\0*jOg!7jZAuOh@gh8>bcH%uDG.S&ZUoEms
%!8FJV2=liCjGa,i/-C/e1KUVLe4e[@r&@+\CD+X\6nLjVQHFG?cO(*8DODQC&Djo6K^khRKo1?+9$9"rYt-_E?uepmVFr*)h*;c>
%/Bq"F#nsURNFl`QTpl3);2(MTKbLWoQTd3l*7Yg3+=sFf"neDt1F#Qs>&o`KC1k]n=k-ZPLRS=dmjEi)T_%X;iRSjQg,HVK4n(_-
%aHs7nJ0jrEn(CcQ).&*4C'Fm<$!<p^qPi7PCPFap*&.Z-QHBh<l3s/b9oNr_jN2[?%Wb!OXL@7;'&E-E\_s8W"!5u>3MJhK_?/t<
%k8!aD8N8J<kX07(af,?qX0S]KFLNPmMe+A,/nq7I7T(n$,Fb?M8IrOFYU]on118?V-)-%u*5+8Lc"mAC:&(II'q-/*RTVBBXROjh
%[Qc3dE+o*9F2R5,<Dh?V"hRm%VES"b=XR<["LJDlf-PeM&h#;Ug,iuW-GhX@B%JB8jukl[r$9S(Wg#@?nIkJ.7R/s[De4k'XPi+T
%D4aUZqFS?MT^9JGPhIuDh'ZeSp,#EH;YXtF_jI2&%[6?h17F$oga,KY]betP*\U]<BOu@G^co5n_\g!a+UVoY!s/aYK.32!8Kk;j
%Rq#3O$T\Z\U>Q0Vf1e\%bfR7nS!k(6\j/JP@t_79fJg=:#*%O2i_V4j@#[Ol(rfLU#%,W\i6!]i$=Hm7Po@8BINXEc^]eZm6qG/)
%%;S.ipc/i9,[:N4j>bp%%sS@`2^L%=n2$q):kYj(AYXQ.:<?_/Hp]IS9\nlBK%WMA>]5Ni=t[HjG0lE>],1L"oq8_8k]7$39pXGs
%JSrEE%O%!8pj5.`a\`kK)QrZ?*-)@.cEg__(WaM\N/Z5P;1ico>=?sR"^0jnXFm0cj:5#F9T3pkC5+e`^"GRYT8)Q5L6<lbB8rVo
%4p/^&&7^Hdk&K&FU)L;)n.\#.c"B'WW<*sS?P%!A[Ed")4+.)KCL,>T=ecFC)NHZ9KcU%/lK>9UT$&Ob8ns;hQ5t6"@4em_-n]dG
%+^PXt\.tm2UnT,aD3e,Z>"&Y[0RCa$5ZBQkAJ4d0OLqcfLgVi(+?'SX#i:;?NBuLJ:D=7M;TV$i<acArjo>R:q7?D#f"+KAQ3Xdk
%^e+,'3cj!>P^+]QPr4$.H9b0q&s)(=^aIVjG0XPnS@m"q_`6`"@G($ekp+`qC`"#6kSsr)b#$"m;2;7u&aP"7mrg7=%neJk:49Z0
%H:H/!7-?AR0L6pM4+4s6kNg#[B<bKq0(lOnc9b3Z>TE.*hB/.RU\oWA1#9-gYgF%:`4rtg*FNMs"i<<EMAIL>!`\Fg5Ij=rl"S
%W@-s_,*3=:+YIN0g\ijoAY(q;NJ[il)dTn>/dA)j5bcD/?(DB,K$ke^/dQV_5GrPF!XO;EK?)/3'[0mVLCcaJ&qJ0aB+eZ2_b6EM
%/!K@?D!NVDJ:tRCB![ZE.85J_J<f[+f<T#k5q#;I?P=@^<J./d1Jh`bmT_13op5,<H\aq_Up4X]X-+94VHAh&B-#RZ8sU[A=c^!s
%^g`2<9l.'89GPNi@J"7LB%c2=OoB3AW`bJcQ##n18`U(Bnb!?)XOX#56])Z:JL[fohS:#d&Zf5GX8mNOi5Q56oZe"AH=f9$#FoXd
%i5RT(;^ms80IPHCJJfB66mL[&=dbeQN;d/([MLM4$uM+,VG#9A"[*QTBS!09`f,^X8T>"1j&0`lEJgF7dk[R*EJ&67]0UmM&$sL>
%"(OE!L7Q;Sbg>>%cXE`-U?;M?A4U\j"4f't_aTe*k8IRud;8^7]@L*j97\GuHjEd322Q)DlB/?rj%&@1*=d_Qm.b^eBTd%p1Shet
%(4o[7mrpQ(V>pp]O(YeQ0om)"]$(l[J9k!BGTB#schmoI=ODWXC\jVu4ANZ?calkC&1'>BbGVmG8<d2+Aj^q/L<U>^/+eCAee82K
%_1q/7:XHK7ZZ%p7?TK=F3h41*E(EO-6WDF'Nosth93i=A.:7[5"&:G#jI1q.1b5<,GUY&((V%1q<]Y]*s#JnGq)hM^eM#1GC'b_J
%gF$M7NFA<APmGgp"/fhK&UCmAA.TPX.JA<Ib`s?P!5cSi.-8n@%2ol%,IJ9&c;=PQ_=hP7K_[Nf,j9d/6G6E5d3UgJ473McbNP%P
%I;<rMZ64Z^J):bkI5fF[i5tmD**/<T@)MFM*n:8UBO=>!\--(lQF#`Jc'grS&-/!7E?QLD]CZMiD)[HZ:)M=FCa5q>A4R/q;kcRU
%!oEuc6:TLmp-_nnk.HOQ&4d^Jg\MD,CP=AJC_m!iFN]o=%4ianFTFRt)*<(NJ?DK+8&_&F("Xpph-&Yn-:C56WT#bF%PD1$K7FdM
%CX)V^C3#A3G*ls.(t2BfZTo&-@a3ERM*6UZr[Npn0ejuUd:<C<+>FO7CX19<P#g*?(:/=:&p&Nr3kke<8Fll8I9\-tZ.,-2gBBGk
%_hqScD4?j[2J71F>tp-sLI\7TpCbkkPcIgE_DO*34..Bh+,3')q/1fFrI0cnrImO!XB2#Z$U".7Wa7PjHk8$JJr7.dJ]eM;_XDQo
%DXI(>oAEI5AAms*%,c6\S)t=Fc_a:g)b(h[F/$L3b^_.["0n@'Nr?FH/8AEAPY=M)hJqK?#(nS>4b2&oX@\IE%;KJ\N`O0BN+3@.
%X_Hp33fR?EW@]_^%C;'4+I/lrYR8nmAYC.^_`ZM's3Zq#2']Z3-s!WG<`b-n<4%U)QN2QrLO2$f[RLW^dn(dr\S[<V5U\'^#8jH8
%BpKqqn0$d?3qTI5PQW!U,]E`O'[Dj]`%*$b`%\4mc'umZT2sK!+W:SsC$s;U?+rCKg\PWL7A;kR>.std!20[W/q[tb5HE70j(oi'
%`q2+"1)WLPK<tFfL+5UKGR67@1^dCd6jT-3=(3ST];G<>\2?^6U[]6@CYA!S`io]'0UJpq'5%cn(^b)n.WZW*]ebns==PjuY!V9.
%aTIjI3T-coGC,$r6&I\f4*(9]1sOlBC)W2gb)?V?5N4O&%/BE[N.R?<"f#%!C(,*/M2E6o7RK%ZlsQ=f:nd#]Y*Rc=d=^>'[1$V1
%\D6<=I@hM*HUW9SXNRV`L0:n[?I/5<hXD5l7-n01Smfjo`)Oduf6V\KKHQaV9rXLK?J5?h+n;)$nY#pH_%Fo-25JAn/Zo(0$YMN#
%93J%S/5]87R,[-$J"Rn<InJo2;F#VX*@3!;CMrtWVrUgiAaH7^3""s+$XumoI\cjdnuR(^eg_%c3J7ZKKFAI?)`h/(0T@PBcH5!M
%?J4TH?op9qXn[M`&60WYYb[h%K>Q[mpo6K$KD0_#a>TNL2:%MfUIB.'5bU0I"7R:`]T`O(huaQ3.WeamH,aqtVbIbc,"0XOq,K?u
%>-o2f'Ca\1A/.q93:;hn8*u1Ic9CY!)L[p9+X/Cf-!)KPd"mdbc>JeclpB3TGEN*)c4CRFGEdrMA0$1@O="F-8B<kc)NlO;oh=p+
%=RmK[K1%Rb'[?.k>(T:E0PR8\HRd"RkXY8SjaRHtNG,8JA@9^>(^db^$]`eh3mUq![`6H)VJOMqMS$ZIEpLQ2bcJf9-3b,%$[P8?
%RBW@[EEb#Q(D[uQTmQ:iC5[`@3l(f!BG.JL_X(MJXlpDAEu#r4Yu392:IoY&K>81)%gkJ<O$LWd=*Ynlnc&u:Z3hAWm/is!Z`M*M
%>rYiq!cR$Cf<1%2k`\\>feTWFY1glIO:Tjn@;m.\2"\0:X\f@A+sp4hk&WTu!;$'m.91S.FGVD:!?jEjm.MQ6c*\mm<X]:-bn2%d
%Q!%/+9I9O5QL+nh"6S5<kohA]<"NV1L))#lK2M\;58!PtR+EJ#KcPFhUdXjLB.tN^@dLPa^*]nU/OZ`k-<GIZ9Z:]<gl`i;`4/o_
%+[\Z2_5\t-/ViR"Fo2p[ME2$IY\V&e->"dPk7M/p6W7NMUg-uWl>!2aWCo6t;_oWP>-nCS6%GM5P>m@:BFSZ"UdT,0/M1*c6l.Lg
%;@-+ma%S;-.5dYTEk3^b'PRnrFA(#t"e@)'A6a2d]T?dP^*OK%%WWon;V/Jc?p,):HAtCak'cKY^^VN7[NX*+N-IsF/2N+8m%.C%
%O%t&+\tEZ1ip/@Dam,Q24[g(<o25<Z"XY?n8lW6"QY4>YoL-*uWS!8.&.UCp$1blHe/D=N/0"`AX%Dp)Ie'(@jeQl$Lj^!PSurD8
%G>DBrOmi9BH"hX?qlb`3Ich&cbSlX7`+gZ5iaPMiGa/gT_nCftCQCX0?\3VILGoL?b2mhf'ib\8`FI8=,2u&r"!+r0nQZYZZ@stE
%#Q*%r*LDOTUE?Hi_cSE293$g;6"7]_+AP(m#'1=uP[gI]:dtfScjp/fo1@>kPuqGi1W/)P6_8=-!+n1'3iUHI5b\c)8JcuLa^DqG
%NsYpF0c>OI9]7:8+]lS26"o3>rK4cs`iNF)SB_iEA:s=@@KY,*arLflVelD.Oa^@$H/b*0P**l4^_$kSR&;,T6@9KI/2.tuINB9`
%)$kosKb@K1/jKup;AKcO@(h`R+Q/[4cEX?=+KrGHkQM;%^CnSJoOH@2<R,1(ck)^eU,NA,9uH3"ETdYMlC*Ap'=!ttho^=BVEHKH
%C<543B0CA/E+]UF[<HqOdYab31Q31K2!TE*JWW@(F2W\=HY/M7Qo'D^:jo7$EG4jD@'^^cEN`4VMp\S+DNAbl'2`L*&M83G):dVH
%jg"\$ZQ\[/XO9'94\$0MA%'1H\>$;uZ?9-f8li-Nkc(Vq`cp+W\O!p[4D#d(fT)D:_mBR.%2M(#GLh@gh97pD1tXL=@6c$9@L'gj
%c!&G?id+OBZWh0,228)\.lVDPjK/!0"&KQcfITQp+m67c-MI;Cl?':fj%!EgM:a!2>)U##jiC&%*K@OM3tr.'.S3RsNid`G@X&2[
%ie81r-<_RHD2gu+E*46_+\hU>eZC_$0TI^&"Na,r9*4jhV(WAtFnLn$Ak"ReUgt(N&)K&^YD<%MN^u3JRUMGDjD$i&);"'cDi/k?
%`^P`U;3@F0*0-"b;/RE^#n[=fQojgD5Io87r+J"=+YE[>WQ'r#14ebf2CM<;,L[[JDibklYaca5-k$=f%n.'^!nopL!s7m4(ngD)
%(6NUd3#LonoNo>"P_Tif7f@#EC._sh:DT=,ht.]%UXdW-8qJH,^mJ"c;f)!r&r6V;1no`dDZ-#]4:,`:,22%@eRl/%2mkY]0?O05
%_'j*t&p2d&dK5=m]V5_@i=b;C?pEs)mW<$4^pUhgq&UYU.;>23V?OV1>+Lb2d:D*&DgHBh3gKb]?IE"IQ_5SJA6RPTGVV'[;am<1
%41^Xb0)b'tNn=)B-4h]6-8H/R@_B%qAInM\F=:c0ldg#bksRh[msjiW16t";DI^eg5";1T0sO%/LS@@I9R+8fS`6p-4LE4;6MEh;
%Z4nRqKWaOHZpMJQ8Q([,BAh(lkg^esdo&4!IeWHOh5Wc<<JZQNM2B>Pd#3ab$%jg!\+oaNL2M;p.O5U=PUAD3.kRD*M%:))$<XYM
%;U_L837&u8W-hG"`hb>3/[JOfFce3c)T,o_CbVT-D<lP$h&okfV:)sn'=>JG?EgDrY..U<[/mfCbtThUOWp7R+;^Lf7]Sada\QT#
%9Ij^LUP98TO-ng,#LeM9c(9_0igs$/iOcNh[Br&a0eH'#d@T\>@+e>;k84Ug4Bof-=c6b;bpd,3A"(@!<oLX@!@CYiB1I/OO%qRf
%r;/umSe>\-c6f!@S`(cT^<PU+aP<O:HP?qqX4ENVDQUIfH<_t-)_@F'#.M/3pI6hAQ`Ue@r_JH4TU+&pO&*]K'!j8s`%:&5,^C&P
%&^E6K3,:\2A'cGmEb]8@%mnYh.BtE@nb1%i60f?n[Is*=T0r8SO6gL+C7<&a._=0pXnCq/nT>,JA9%cS'9L)!Lib!e_*0gfV&.WN
%"jS/&1Hq^T`4[><-R,"7-OSY]V:!PfX3k&I'WHXGR`1_%J,oTVQ`#:d*dW>VDN@Ir]OK6smZ52=7ksK_N[7P8(">,oU2$8X9f>Ka
%+$qbEg#c\gVF5_od..$G6/EkUq[`W<h_YmB0*tuXPMg8;/F>p.8BntkheT;9S:ZR!1ppoN+2R2&\D)Gdi3J(U"CO+Wdp]-\g.e_h
%3W\557ouYuXcPR8ob_6/gE>aO7L.*G;MA(?Tog?O.I"\1(pj%!+9/8]rUa_mTDdAcrr&$fr-S>=f2o/*q<$MfYPrK]F*%';8u]Qf
%qt'X6D>I'.j6Gpjo'H#]PE,qH.(PuKfH'pB&n4'%k/T]47t::Ya8bba\+F&-C]*k_KD*Pe3r86ks7sFtkk4_iB7Cm7D([ZMC5fna
%J;@6R-pg@;P>(iC/c+D<<Ut[$DtW]=rDl-a1p#Sqs#",hd)M:7\=W274Xd;/&=Od<h8A0g'N.p;6BC&(4f.Di+(Q_pm"A9(GT*uO
%pe5ZsK@=R\CmdbMP4:Eo05<'Q75OAj+Hk#ATKPZPU+@H`'<B9;khssV.93iFC!9.]K@l\2#mN+VK&qpE*MnUh.c4\?5PNO(3(s`Q
%B?);1a0?fcX?iY]P4rN![I>=Q3N)[PHD(0\YOp>#faM=+'0?jPq;2C=iY"duA>80@EFV`l@QN+4efLd=dM:ns8Y0ZV0l]ae*l"V^
%P,Mk90dIbjVhbg,!E@O;`7&IdpS"N!;1CgooE[3XC(2n[;`+b6Mr:DL*24!#q+jc5_r00(>tQXl0i&b/VF7Bpc,@HhpfgV$Sui6X
%j^>Y^U.jpL/2<7pqoI:,TA3?9RC%`!1,76N9FX-]%Oi(%oRaMnk^h'3ot[.>VYem43KEJE6i_`lEG0])Lk3t[XbX158pBlJCkWfr
%-b1h7BAUa:W*F)8aa4nX':a@lNX**F1$UeVWJ3#nZM.Q6-1NuI:>^%T3f1_I.=.F>pT3OT%nZ1E>3UV.G#M7aFq'59n<>^T`?k.1
%,Ytd89l$VI1sDUk?<id;]U1(6.$[/f(o[id'.XCV3ef?6A5W3MIX)sDZpI%hWQM=e=$>I!XQW@36]m;ElO\-'cS;q=P78.!Ru(i-
%;0pG7H;&[aOrbqTQk_+=[M0cbaV#%1@i-`Ej9@X(6e[0=]pLF>HN^MuSk1:&(8]b.iA40gW6n[KLI-"-9Fp>1#c*Z5/d?Dj3XH=U
%'aO,WCr8#>c!L[[kj;u;K!'A6VND!NfuCh(>aQ!27g3FLd)I4dm%t:jM<oSq'?):%rg]e\0p-<hZ8et5mD4p6_i0M])BFHsMF^F-
%g^>Ik$t?9>@pmISG]E+5Z.-cu1,,L2'CkrX\[2Rt1GMf2K'fD0=-2\,XCZ3q'1U=pa%hYN^*uLCeTiA#V'oV!1(1A[VEKafa=#:k
%=-Qf%D1Na6MA9tpO#l52mA8_RM*rhVOstGX"Zo:tO#[V<T!U3^#SE74D.tagp+hCg\boGH9;pkn'"f@1*CUG019Bp=\sFb_F1$cO
%[o@O,j(Xq&O>]GT#DBaM/`73c8"''9mYdt7gA'32@ZiHFdCb1cj<5nFO)alTA+`/a`H?#;I*`oo6^!7sgcnEl15Mj\#r\jTiQ_"`
%d'aO0<_[naUCl0X^taWlJ9ms%(shubOhr6r/VAfcJ'.<j"tpQ4qPlAkHLa-&l.HAZU^id30#XK[0a53I/_Fb](e:cJ%G-f,%;(qZ
%InY5,9]X&eniAkEPoS)&f0%WC`aDs1"V^]i_tgt=@!/F/4R'IRTeaK@puk*\1B^ZTNX-q\ED+SW[9<"E10k6019m^ZN$taf^sNFn
%NYKg6fuZRYHo[<\8@@$d=/sP4b2Vn&)=ij^^jnJLE1Y_Y,^bYY^.bW)(;(?).koj3;*]:T1*Luo9/!dT6h[Xk"Z)%#aRZGWZ&[O*
%P5E>m6J5d6`&\UcH<G=/Yidgec\L=n[V=r.Dmo?"LLXd9pgETX"<t,=;t(UC^Q9u,L!LF;(V<`i*,<+gb.=")=V!%ub0@l1(Q"fO
%7tG_?#*F.@"YgbX6*mD@Y*0tAD7$agrQ3?S_2,%`rk8\nN:77RB:;/C\kZ?WT.mO1APbQI"GGGq96,`di_;,3%u'9YYm[Le&d(m>
%oO<cDD%!X[m"<LXAeu1P@MV+2j^s]$,J>$IH&u:-8eCZWGVA[$.Zq6?$e!I\GB)YLMTS^SOcaCk<;ddA8J'9$pEos79l>lQ;rT*L
%5c.dICB^\nZUdTUH-[meNgs'($NecIr"eHDQ'$kn0uUm2rDn&KCjQdTWS=IJp2?@)7@@<@ZL"h<`*d*5.Xiom<slo<d*;\1eX3%2
%eM)*u6W1m%dPo1]aBaBVJHK(m(?6lh)6hVY(_[5hhDkMWRSCo1B3g%0:0CN_ThADgItOe^alg=@<(>&pLQkh5%LE5G)4sVA^!E<l
%qHd$4!j*RECp5NU;9=Z!NW3I#Z&XCG$6'SO/K.bt$==S>i<4G0a<DU7ZrJSG8.WWp6W>e672l=b0j[GSh5ek0'?bn1kj"QE:a$)P
%^C"0m49ITm[7KN:$W1t=he*fma$AL0Yo%02b6npGWmcsf5joLu-VuOgP=DdG1nqj**ZMg]^ER)7n7?$lP]/%6>#`8b6_\'5R=ifl
%Q^O'#XjR;$$4jC,3@(t,Tg6*OI9F[3]_"TXlm,T<Af]O^J3n?9g:598\VT-6Y&ooaA@-dJAa:KT2%iJk$q\)ldR23@kW8]81.o*u
%dBR(J@-q*m=UK#t4'8>NV7jKG.%TU#X]40V(qgKm[l9@(0k?(lf\Pn'+XA!jG^\*:atYO&H0&7@.$C:p;^"AlU4u>`/(TBuOtn4W
%-Ik'>U1pt7WMhkW>qCo\]`dlH]>,V=F&!*SW*P>2eE3^f:9=t$UH[FbSn,<1kp!fe&1a+nE=<11,%;?rN\Tr_UYU^nlNI/Q(_(aC
%WNi0!o=\_E+;#h,*9?KPB`T9m33l9BXB>nX=-fD6.n(I\Q6M2Q9K!(n)_JJ1/0+3#@phu/5.uP11DYbbkTZo]7:1-W.-iY1V/VU.
%910D_ppQ?lRa"s0>7.#R8PU^-LGB))5#je.M(F2,2!Pfi/9QoJU8"dfI+9cO[XPjs_CM=&i#L8U)2E9nKsdb7Q\.Pc;1+qd/Vqr?
%!%4g\jBO7&F\rA((UfJe6&f)l8RL!um3ST]-_1OK8[qbLA!F8f=c=uW^]P7>bY6'=j?4@/$t`S^RV"[<7=cQVS7JoQYm$Mc\]Wp,
%!3Gb.aeQuZi7s>MjVM`"c=@S,6jgNsct<QRXUBM!9,0okiDTln,\'h#LB>IdTAak:BfGm6W[KXjc(Gl(hU<2e6(<^_?cO>j=&%b.
%a8XFek^g9+J'P$]Mr)PmU's^.p:5ru5'^GH=E@j.PoF1Q'REf&R`80L?$r8-d(hC@7PP@MG\JtU/2@U`b*'oL66t]3`.Ig)jfOOu
%40nWNiN*`m6r9h7)!-%n=]C^J4%X],Hr9*)kld"LU-mt>bF?Rd?pq>T;UC95iAd]$9T:,-W["\][f%nVosC57Nl0k'mF9LuNQ5I;
%U0#0j>NR>%:9Ck?^upF9;QH%9N8Ah)S2WMB8KMt+R)n]<m6UDHVSf8riBtkG?*63p3b1rqf$37+*_77Y=m95dh8uL','4fBl*VfR
%)`'4=JKK-g=CIFe&IY4\6EU`+`f*?6r(OK1*i>"!g6Jh8+$4eKp:rR9+mQ2lMYC%_.Um.Y*n5:$0r<9kgDV6cncLP`BGZl<B3J1/
%i/(=-IV.L.hDO7dTo0@Kpc5;aV@p0.R#dZR'T#lQ\oQF+,,nrIE"PKU0k2a`<FT/Upg3X8ckm+ZCq>t%O>>NpfTnpUa5%^mOmtQE
%YnKW`/.Pl)\1qS-'kElGGt_-_)U$ujTa,ROl1-0Md!?8,N3$P'WO<$l>M9/s9NVXp=X;OH1;,\RLo/O0\/;`[8L\]h_6G[_=MJ!'
%8"'abfMo&MWG4tY:AIqV?*Q2R`$e@$`+n8)-W;QV<*Y%9`#&9aZ<O5^M;?'"=gQ:$2sFhgGqWtgV#"ar*\^C`5b8,[B4Wm0GcpM1
%NEm,tMs#WHVSo?4iW^&W&JnqlQW-iXS.dWIoEOgh1mF#5"ca`CRE-6<Aj4D4.M5.4>1!GQAR1,+$^1uUH^9]uiWq;tR!n&bYV9<i
%,N<p/M9rnfac?R3'rT_45kAY;6S4TVOW.I9A6Te7722K-!3V\<1C;qZoHpH&(Lj``,@K,j,pBQlb.Gb"U0$PESU*F:Y=9/%*1iC&
%>>/Kslrin+D'_PdHI<_UJF%:+*k2KICetKj5>eO&D\!U`*%pei's/AtSt0!U9Z!gl$e,d&`S#PcEbq-RL;jsrWtP5^'k.h\1DSrk
%50Yg$<]8-E@-hF#,qBZCi<pn-617E&TL>Z(Z%E_G\V)$B0%WXl(Vcd7d(B[ihA`ru?#!e(47_YG+L/mEj`74GRC:.d7'!km&V-V"
%Xqg<BQs,b0#`(JS]dUC4Ei((HoEhdu;\]<MnT`2_L_pjCVI9h4*Hj0fbM7t;6[Ds*od>DPp`DTQRZ4Dd!LLY^.;$P@_3AOU=-Jc)
%$"k6UWcZcWUZq"'jqCUikp)oj(UYuQ1^c$pQ\(%=r6aSVa^%odUr!5G'P2%$pn#QEqCOYnOIe%Fb5,4?&gB\W1/Z2&Y\/g/;jbX8
%U3W8`0WRm1iKo5=[Eb3(eIp!8RKK$rX38X#D(G9Df_*95W3"KljXr_hV/@H&$On5fADnKRoEMM&?<43\W=J<Ui!+p,Zq6@[4>bEM
%WB6+S_nbo'OOPKR5akj'lsZpO,tsDcr0!J1:dbR^XjuLe7Z\WjMucY95kt)=EdS8\MtDABm\3&-V2,f0_gT&uWdr03(cV,!XmDQ`
%)UDe1L0J2Ddn20#7+<8)4_^%j[G)T!nj'G'.70=A%7eQ/rVUlk=+>I9IW]^\p#d,EPg3]0lbEAm8&+qis33Y$a%ua,hgTWq0tR5O
%O8d,t55Nn$?_@;jpik>nchQs9SY+c/?XKA:q=ZuOoZ>KVs)r[-2?*-b(uUnT0AbNT^&?X3cUYl[]QnR=\'=/=k81>ZS6^,mE:bg`
%IW[IHOoEtW[pPct5Q:<OMuPt&^%pd_];sOn*l#AegWtoZjjWaRX$>M@`EuCnhW[hMjN7>?ebRJE7iG=A(:P;o]%-Z?4.>7arr)Fq
%>^ZM#ali[%dlW&m@91lk=-IVgH2f/(h(XV&pNm7.S]#XXJ(rcn]\>2uYKkGnr70&HR@rMf:Zn5=f/I&RR#SQOq6gn()c$n2?\kb,
%QbPbu3Vi!o()FD<hUVV^]R07hqks,Lm-VEC/]SS8GK)n&*;TTuHu\`Nlo/k.rnk>oX4&X?nP8se:%%<Ce!orue&O)p1;UHTmSE!^
%S'Kupf"4&#mk2T]T=o`#nuMN1b<#Yn7d[s+eF/`\cMmIMGqC9?m^:q^rc!>Z(iR&5*)WbBSZbhmhn;$5qmSL3?[m3I\n+@#-FCD\
%4?C8h'%^K_]fEX6I^<H4Zg!M=bH'sPYHQiThe]nW+))FjS%O4Ns734s098:WW,d6]b,'_?G>RB2WncJnSKES9[t.*j<4Q;8EZ9sX
%#>K+91q>9sS;aLfn[n,Y[g-2VT:"J$QTODO#@6ahlZ,A9q;p::cRqE[lK%X22p]?XoccL#D.hTKp0HQIcV*0K1n>_C^*>9X)b?R:
%HauFhH[GA\=(d"G5G@U`&tIS9]:.=f([iDlWW#0mDO48:^R`#aqtBsKlBdRXDSP;q%EIl/(=rJ2LV/&Y*4EMbYBTc_DS$s:cb6Kr
%r:97.c$.rt-[aNqcZ=Gr[sWfZrV#Q`druTUDdH^V]tHG:g9NC+J$LkMaDIR-YQ+=hp!q_#9lU)B<ti4hEKe6o:TlFShu)e!?TkQ:
%h@^O:^"?eEdWW6kgkMbn\*r1$H#e6)l06<u$/K?Fi9C9g2!>_QrnV)Yje'?8pPkXPKm<NfpTMX;CflSpfqDa@[nUAibnGBO/@O:\
%Ft$r!iVNK;k'3@-io_D%(n^*2GHB-9P8&=,cn;QsSq5.6Gjf"(O6I>1n#^kM3mWS,3VJF7Cf#KXZK9g?AV[aD_pe=Ga6%Y_%Xs2u
%fnMN*/TZaF]ap/AU2J4!4TRJ6"CB"/jsNB=F+3`2jk7nEaZQQ3n`'K/%k$P$g05&Jj3ptZ5K)"I+d[,6)uA<q2h+#dQ$ebCcM!1i
%4SRm2NoCW3F3*#/YZRc(f@LhRHujK)58t%(q]End.GeV(/VP/+E/8$#VbY8Sig=O6?>`<NIrk2Wr7r,HLk`Olc^md/f'DpcP:[j-
%-`*5JfU+%M;7#AGID@)>;lTNfc.?<.?X3VV_fNDEC<6!U@gD/(Vp+HeV#tVW>WORBIu^,l*+DL3!RLUOK?rcGh!a\lQYDd&r!>m0
%DVBhn^DIoA-(eFcrtelb`i!k1h%oh\OPBe&bO+3AMtL$jSgP"EnAc!8k>u#]>[<n+I!TsUG.LBY]Dn(#*OT6jE"@?kr9jSq=7^bi
%5&',(>I+udS<gC5.I;Nad'Jhr^=[D+SrDa:AMR'C[JAAKgTN8*:<tQ=Y&8]8o(A2oEnh9+kNLcprmm_1h`(OUM^X@8H[Q1oSh4b4
%6Tl<qE-X4MgCWN;1aMAT)1lpr4RNsGP5b0eG%\ScgSV[:pT)"S2>]u)I;CT:4W,$DAMW0;Xl$VB[c*m.q<%No98"?FgXTVJfZK]L
%/aq'ZmcO$$N.oaTcfF0[lgpV;^M\<uB@C/2C'1)4VhU94N[OArq;#r'ZfCeI&A<B,[t"cVX'JH[lM>8=nN/m^GPUYgU(;-Yd*d/?
%e%.NR3]l#Vd;XDPEVeGU/V3.Zdp5\jD)cQC:^+<[NX0@.X/VUoHunlGL?T+tiTB4G7aXP+mV5@$DN#2-o).KV>PS*7N\u`YX7OM/
%=8qA)CBVr`R-d=(7&'%%Jp.!\,#PdBk,F_$So>stVJXu[Mm3G-mE('VS3F,QPXEF*6S-BO=O+D2XJbX]*+DbdfU"rVb!0Zj)cVG;
%&rX&P.I;O8b-R2m:7kH9b*L.3Q.P/$`hN5M5V1'T6b)l,i=LpQY7gLO(1hFZgSV[:pT+9u@o6!n;_6(WaZPhms*]3-SmkC<VS2m-
%Y-H0`I+mM(Rj,KT47VqS2kJ;aI+2$[EWj\q^[N'>rqW`7:Z_0g=;^aK0t(2CSdNolEs%[+fVh"rAZQu.13;Ah+2ZXPJZiomJcf@A
%n?i@*HP1_nf5ClT+Kca'[=)Y&B66ZT>j]>W5)cN_KXHH:G2R*?Y4c,1g?K:HHjBZ<I9VjD9CS"RA=o(gn8O@Nr#4,'2;58;LMM"A
%7^pZ:G"6gF1nm\?O.-1`QaXMhaF5i9\,L@G\$LuW>b$3=q$qEdp['N(?bJZ#o).eC>*ZQ@TtW]P\bXc&%#UnKY."1P@\LGqX#teQ
%'Yag;idc]fX(f9GR]oWF5Ht;'aoMh1HfO1Rf8m6#IfKdd2sfqQIhLP!cf9MeU[e@J)1R_Is-)s`7is,!ATo'6LEO=tRqVH,$5NOR
%']%T`kADb+ra^WU[nZ%@(rlS4gb*rGOhC47m5mCVFF`8$o4U'#+4lid?b[V-q![*'`i&f%I^Vm=n-ZLKP$DCIS`h_20)f4$GjdR.
%/H>:n4E&K\mne,_0Sd\8R;U7mme8n-k1dL*LU=ZRI%Y#_pM]&Hn_Us/)c,=\hgBQlG-aVHH*VAL7fm%F(6@NTF@:N7hB"HN%[hbN
%s6mf(qk:@/jo=-;nDM$Ojb*U-i;^4,*W3YWs6k<4Ff9t5nGaR-^]0u!806^I51q>U*dk\KC>%<^6dW[4HgphPI(g[RIr:?3cbKDK
%h:o8;f2q!D]\7DGmepTrL[Ej&Mi6n>hESu;o07_I1E*nc,ll4"1VEU;SZ*-(3Q%a6OjJ$ShO2ltJX*usoB">C[nc74o0)8bqu5%/
%omTAkdcMDf/7\tnfj7h&2SEAA^eAo"(jds#*R&%!1YM@VPDPn%ae.I\SR$Y!Ur9T8`^>.QkNbqCH@C9^G4WhFZe(gS^H&iNe?=;)
%TBu]#B7i)5lsfnE7XNV4G%^8[gG#Ohs#IiAGOnqY_-:gk>41$:o;cpj6l_Dp[+!Zl?\tLsXA[f:4SDu/T"b1"lu^;oDa(sMFT3`#
%'/m<qnCN0eHAHYb1r9!Oh7]A?WNjdd-?_'bmBu`qpL'o0+8a&W%u-F'iT4Hl3>c'dp_4bX0*fD<_=7"+4M,-9&]9N1@G(R"0YHbW
%#'qu9I#-`mGN.$AM=]C>"4F*I4?[BddW8'\r?.)Cn+H9bLGlCR#)A8Y&=tf4`"mlRI0Z7o!n=IC?kZc<i;bd@:P!3Vs!g4A.NZnO
%C#\.Mjprj&O8?'jO5h9G/GoE+*fua!O5l8MXM'*+\+AFngUQQ43Zp+>0*J-_f[g6%dK+c1=3'q<K2_F,=!t2T^H!"-H#?d\)c3FB
%=o!Ch&j5u3p'l^"?U)OtDoF4)D0JKcb8`E>>NJ?OI6I:qHX2-sjkI*\\d`T'?>jAr:[QI-T',l9e,fQ?h0km;^qT3[7s#b6SP;+X
%BEnu`s"q+O)+JcsZ[(O$:Yn<q/Mble7BC?ifHT\M:tG<eD4&7CBA;G6XPqI%PI=0JS+G7$JiL0o_2bc*RQrmc0qOJ%oAo9Um2=P2
%[`m]<a4OTjpSm!EK,ru[a4Rkl3S6%rG)V%oA'aKY2^b*3Pb,LZq=K"DIG%gUU8rL)^H]Clq<Op5gpQ[T-?Zo9`qEr9Fkt4ME'.Kt
%]KQ/:S$W.hmd04\Mb7rdrN>:+FoCRLq9A/6[[^R1!G_A,o8?=un5ej\:%$1`-fZCUr9jE/90S72m_(E_me?Ea4a4@7N.H^AZ'm?'
%>[MB2<k-iNB!S,!aj'LE$[9:r=ZfL<V_)i;e&Qkt^cjom5mQ&?O#*a;<mh0eqI6ehnSsL/*s*DRYm`jHHM?Q[G8Go15%UMIo0GUo
%,ZX<F+lQp_T'Xm)qdE'&#=IbQ:Wa3f:cd:rkDs155Gb$V/Wi&?*qH^M$Mtn6jdoPhf;@g10ESsH`-XU,;uFnuJ^;IUmgdqD-Y\RT
%H(WCR&(olc.A%Z'\U-*[/p4^Ar9g6&*ONA02YUai\IBau6(?Vi.3,_*rL;(C6^uh\6?(<6G-0#+fVF-T[suQU`so.3W,i2dm\%iG
%e@C2mpPdY5s15pnrT?*e>AhI;lZ+ZV1XP9m9k*#Q\bs,4\P:QK@H_dYXW#(]e`QqRY^7C5@flTB.0K>*E?u+iQj6lQ-\s:X_oY%`
%k4SZQAF.<DFtqJeWO2Z.?X0+kroK=g+^KSGEdE3+mD!NoKtb7rp0rq+:##(#gheSjs((QF`q_`^C\/Q,GYdlGrI$X[HZ/!#HG9+O
%0n"4f=.cmBn<W$0?Tg(!p?_,,Bmn6-f2l5'H]+2/pD:G&E8Q&N.;/ZI$\Q4h0"/8bnEpg128@BN[QZi;fd+SnC;$ZND2>TGqsM^q
%-`:T]VffWI6%SuiVQm\gmZVp_[T=#hk!1;*"#SN\`V?Wrk2E0(c<+W4^eRZ(kPXo,-AR>`gspfIl.H(acc6ZjIW`pk_XTPYfZueE
%5aVitldPi<V)]6Cr;^j*r_I]\R5aU_N:tkQqtg0Sm"6WEIrZ*&&#.kj:pg+mC=++2m!@QVl9QhE)FDh;-MlR(nu;24-S6#q[fGM#
%@a]W'j?,L3ra*72#@mKq>I393s3$_os-Fb_rXC@6?QO!(CCYf;F4dc/O234"RR>m!3BS'aEGH%uGB;DSpY>dMn[ZX(T"I(uq<INe
%;qZjPofc]Phu)Hor)()MiGmZ5LLo?ba"9u1h]Kljqf$d"pIpTs!Y=@8^3b)DpOA6QY]&Q#ZlgHJ%IU:Do5U/We(KeC3f5;1,+_jB
%mUo\gmCe$!:GE(@J25;7pApY;a)M)+qAsn=Ojj>UkV&^DJ'[o9/a!]TT)rNXqkrc;`in9K7hF$GAFc'7UUq!13_tT*E&u<`^M@<U
%bpK$K_"V(W5!FEbgb/'BPK1ahnJjeL?>P_=pDtX@j&qdn17d>&PO`.+H2F3faMcuB4\rjpnc?&ZhVXu=(LTb9"?LOPqImt"cb`QM
%"SQHrmuH:'*W8RPe*BJ?a<l:L\*1pAeBd@_U9jRHQY15cF8k;H)0;CD;`Gb6nBKaYHuX0"T3L]D[p&68[s8P2bJa(CG?:\lI$/=,
%ANb'(LL^%"NnM01c"#lHbj6dLZ=!#V3.t9Ob-V,sS!0n^?Z)j=Y_QljK+eaghWjW>En]B.S^5aR+fB4eP4ZT_n^-]CaP/@#/e/fF
%hg8Z?+dO2t`:>.4huel[rT"dpmbR'@^3XY8OI2>jo_IsYYPp5%@>L?g9)n#+T?j^lJ,/VqIeP_riICdtgWM7Q0?EC#rQWZ;m#HPb
%iubh,cbB5FO5A^l^[C=[oY92)SkA<sr6]OXJTRYQ$TSg@]A?ee5EbcK*.;us^HoZ^BA_-erNGbCIh16l8*T6H8,qcP\tdm;Z_.3d
%a$4mt`fFG,hr7LM280L0\"cUCZ]B\$rak=O*YGZj$n[f3/H'-pSV_n%juj="935%O3oqF"kb/dW+kN(@^Sl".02mn\2NG5K[3KD<
%>,d,kg6e8r8$A_@p9OHe*Q7HQNtid@IT^,k>'lZ9m771*D:?\U>9\A3]`miVpQmc"7RZu+]@tWaN>W%Nbhc?jKcAJ_[$4qo1$bUL
%auYK<(/Gr`5Gm5?%Pd`*;l_&V-K^X<&?UU?`mf)GIZ2<m>2U$#erlkHH5UD5AUFl$)m*CkR:GcuN.^O*PK.^^?M.72q_"?OJf!F[
%SK%b,EZDd=#BRGdbs$XJq+kAmYs#8`Z<W,krpgl'Vsc8<G^V@W]g)=_Y/0;]+MIF(\M<DiqH?\Fo._5SQB-:X*</=b-qrmc6rIM:
%HnLsc-B3$S\>LT>cY<<0T""30oMfDoh.N9jN%ktt^W\:q/:HLk<6$bZnP*;dYpD!nm-@c:T(QV?T;dH$eiq1/7k[AV&3HH<U)Ps"
%:+na9I>kEQ*Qr.Gj1d@/\;aG:M]<NoObmsBD.>g[lZ!f_BXB?H]Fnu2j5RA&16:/fUV$3_ein0[*]l"c*3BI,i1n=MGXo(lj0("D
%*Kt?>9;aYgKc(htH=:,Va&S-?hl#Stb=-LpX`<`#$R(.'GQim?#HuMeN'g"BSS&lHP;j1aCs5R&o=Au7i)r,>Sko8D14uQN@]3d:
%q#l[5d)H)3T'PC'?DKdurFJKHE61rPM"[1?$uR@]4OB*75%%f(O4_7p$E[>J][);1ZsF`S5^.j5NA],tHLKo9K@A[AjptdS7rQ^@
%2sm:Xq9Y!`\igRAj<M;)`ciHLXbN0NXK2o*jBD47V4*Gd>i+pnfDjmO5O(a'rRoS2s!?"\pVQXDq_T@f,.fSdPDD"LB';>(Ri`V=
%N^Obg9<,;BU\f0*^VP_0jG0&bFc8_sJ%UfU2d6,CY&#):iY_,sp:O4]2H;d[NLa=nET$,,?&@^Jp7pSkIk+jprmKt9jhMAWk-]3T
%+UI6MV_g-5]M*l:<))Dk`e5!ukHT7l=2*jr$ol>74G\!9k"*5:75>^uPf<cq@M6BXV&*UJH9kH\F4%\]=kmBDn+lqZ4%-=Y-[(?i
%I")kP?E_BbGO083GX@S_lK!`mP+(1QWqRp<Qa*Hif9>o%(,q:sm!M,(Z]CAiD&S>q$=30Yp36!3FHDl4V*Krhh&0R@/W1_dU>sK9
%/_oPn`4"(#lg]s%Or>=#ki7oX-jOC(]b\2$m..YHg6gM:ZS_NHdtNV<(`*qX9)#h4@jrqS@e>d\P7`k@FM*c^]\fP#S]5>BjWP&g
%f[g]1>2]^,XO"D'!Q%3e%ALm,ifNgV8]i373Iu_oh=5PIkU?k:U(^[`Wt&8lM!Xop0n&[f5C!#H\gmU&!4*X!aDt!.E[p,g%&ph8
%>(kb5/6n4qRk4C0Tc8*!:@^AsNWkI"?B-BM'$Hrk/KjV/3OEX:HsR&_.UOK2jb$.CRd:duh'$Ct34TrL#dr536g1pd>c^CK1".H#
%*f,_EDEN*I()cSfS&W#OEP.pDAn5'o(8\Sa1E4ekL#m=HS2,N*gY@$Y_!YtR3FLY2TYrthQ<1j4kl>@@*0n<pXc3?O=Yo?Z72@P6
%X9]F8&GTA?BV[/<Doih#]`u3q9pSalqM(2I6(9N[jUX.%Pdq3_<3N5%:(ZBJQ6Be$T#Yn"*K907'g<8%9:MuL]A0+Mg.0QL#'%l$
%aC-0;':%3a3LKCMT:)aY!ui9$-kFn=gq(0N=LWoG,iLhl0Y+U5d#s4=@irA4<@gLPmTHuBlou9Ec@PtdL!RO&\J`%6o,`)+&))DJ
%EgI/"q^B[Ipnh\i6c>WIqH0OG`,Rhm>c'#-R.&roH;VS-=@+3SV`T2J7&6Y8j%5O(6`>'<&)^)TQ\<Fr"X)f,]1e:&i1AuQM8&WG
%9n/6V4NKc/g763,o&2`]m!q3?RN=S8Nul`Cb[jmWEY4n7l/gpNd0j65qi>X$kL#<Uj)Y$Bc\@=R;YYK'_-+c^TF\djINHq?ht"2,
%Gu;.1%RIY[i&n8Y\#FWa+f<V_?*8Jq&u83]oERQ@6__mNE/n9_LIXEA<[3F$j=hlIeO0U%VptD+oq@9nB*r\4#B:"M8Yjh?:Ndjp
%AB`=<S#()"F7.1m;u$CGH^A'`Ot/V1DF"5q-3iPI/]@MeNPpE##VH4=Q>F_hJXn?7'?VR0MBpDqmoYVkhfn\KX;5$ZC*k4uTX-Q&
%NUNB^m+@B,f@JHa3>4W"@-!s@-GEL@T0iNXlb@d2I-EE&!F-5Y38:J/<79dbL8ITAZ)cg=HfGJmbhMIIYMC%H?b*`Gn^?ot5"4h4
%\(UE,]`*sGSXhg@*Z!=)q^g^ajflkQJI+:/Cs!?`T%.B%+(:LfR?*HN&[(]u&j0puppBYcI>M%HBuS%PRY<8qHOJ`+Gr5%Oo(D[@
%I>$e09&/gQZ8riAjPjN($a$O5.Mok`iU]'npFmCjedN8'HqCDf#q_&9-I$Ap?n&GR]"$$p#Iej=QJl*:i"8$fZbRdrdqS09YWDS`
%1pRhW85P"$!j=/\,;4>T2W)r;iOo";6P%%:EgVa9.YPGK\gurNIA@]'0e0mN*!OWiKK'&1]HU/9G;6tF@AY,1ruE%kE#ps5e$5.N
%aKAd*?Mc7u2:qd=r\XoDHJp[pI!bbQh$s<a@>p]sQKK`+$"P-a[!cLM>7CkRfCbP[;%RZ?p%[5X.0UdRCVuuVc`A-6Nje<e[^>14
%h/@T+pR[a,_Aiu$n4'Z%GS[C0.XcaD*;OULiq3#K68%?SS8kU[D;cnD`=3Za+s1m&3q?53%UG9]DYD,YH4IjZX+#L$-NqKdH@9(/
%J>S^Lj'p(&=s`NkLD9g"eD6>1!+B(([;g2>D9CsfAVi\.c#0j6!kW*ST?lbJh_XBS7ht05g@\`7r9bX+dXcZ3jF$K^]lPIV4hO"&
%dSo*_5<k97I./$'<cB)5+)_Y-?U_d,N>hg2iceE%4.!jODN]10Cqq;60aF\4EKWWh^4-[+HYL->]tAD$rIrIWZb=\lPefpTNljJn
%3+b@>##h:#-`/cHfmr"g:Ak#HJ,6->o/uIV-M^g$#l(UC^c[c]]#FXm$L+2_ZD!OJXoDJiqoSM2e.D2P)u#=bn:*Tm/cD5Q?Ocr5
%r5>N,^L]XCER8T.m2]j-?+98152u*tO'U7An#&4lXS:?$kt<)SHf!\mIcGc=k"iX^atYd;/<mIUNjZq+FPc80pR2$XO-KC[:G^a;
%nJ*b@fg9mtf75`=Qpi[^,R=G3@NhKrIgf-<9TB2p43O]cZZrX0.9hQNNB]jNTeX-[91N#S4#\#&-%pOcp\9g.jjbiIE!JLW&\.5/
%XPEluB:n_C:9V%K\a#KpkGX:V9't9l6]n$O5b,<kp)#$8-fH.7".k)+B+IL'ERW?;#TY^([r1/fT"L(dF?,tUQN$mNS,MTTiPpSR
%3m7#@=!+B1*pJcB>=mIqh]W<1@KQT[TH"8sj!sZfkk6>;_o@'F4o)!PmToUR,<%/^S;kL8iXnK.r%XcoSO&X&$rRI#]G89b2HFA5
%a0:#;+]Kgnn!0!6Xj9L$#0#rSFrmL/Q>^Xf8cBE2(H5E\JpR))7sJ"]*I469clA<TUPIrK[*nc\fK07S2r0GclukT/jQSb_Y0p+X
%H$$MLOK"j3Y"Dr&9?,M_NGsP<fFVqR*?1[no5cGGp"0O@-14n)@o::Z!qK<U08Y[-7)peGYn;-f]8j:g95Fam(D2X*-?*2@r9:PC
%T-j&mn7a+0aj8_#'Y2P"bHEiDiB5o$_]6pHZ,]@R+3Zf#m[X7/5!W(emN2757o[kGi2_:q=HDXkGdBq*m6:=0:^6E"AAU@%q'kAN
%0l)($nCe:Zb2G92!-mMEe4b"AKk]-q/AT@^(p\\^]BOsCL)j.H6X[X7MIS#pg09"-!]9#)`q:W#lk*_l#!`P>P`EHY&N#PfN)ftC
%Nk@CK=O&,m^2t]rhjoD7MJ2J5ch\@H^6q#E`m[(]B2`#U*@kiji>m8E_R)!]!YOVf0BO.5r2A4LaJ:A94<1BPZS_aG\<2_[3,HdI
%'(Zee%b]eIf.YG:qoeKsEkT)ns88J+gKf;i9s;NrAt!oRG#kuC;K:opk]L(Kd%k=J=N9)3pG(S"SXADAEF:,b>RWtW5$VdIW")b.
%I8<Ba!=u5Hbo]#kn"%jM:\21gs"EJ"rt?u)!oU*IZsEAg6t`o+N@:DDXah/Lgn07L%&h/q%&QBUN@QXVbn[_Iq2IVX$Dq.")a'QI
%+<8X1+D\Of(rsEXN,itrk:MKR;4qni<4U;VVt]kfI>I>OekLS(Zg[:F?baJ4RJ=m`qVsN)Mo]3g+ZQ*3p2N!'jMS;,/8,oba%cNQ
%lRHJb.L*bgJI)$ei'75%%`p^?N3g@p4AO=a4<kLaT=3u1,S\8fQji4f;AL5]-Xt6Laa#u9:]'G'LZCu)rVk,gN;MP.G/4mumhR)E
%iU9SoqR-mMjTf-B--5>A=ouL]BP<D$jNq!32;BD*Il&o=J<DlkYubW\9pR-4r5NNUf70q&%ZFk>/0P1_nc&2dGO0V"r;4\6?QZKG
%1rQH@3.L[\^SAg.s8Cq!:kA0js3KSq<od?>r;H)5q5[m&n=/<fOS&e6q8WFb\;nnbSSRAdKD`[HqMW*QIh"".h"^[5QhV(3l!qg2
%F#*:t^\GalFoL_D>k[m9s7%bP(;K\^s3pbdCkqKYn98e/_gb#sb24tc<K;rlNAP5Jl5@Ui(molLqauH'HOQb!qno#E5;;MTpt,!%
%_2LPPD?%I*KN?7N;+B(215F[WB9(XCoD7?o-:c&d3E!uX=>95@#d[bO^D[!+s6`NQOBei.rhk8;]3kU'Da+0k2a@)QTE".oJ,&-G
%fDf&oWkHtqB1`_VhstM?OPZVh?'t1.`\HQ_#Y$up2Q5L2QkY^MEu2),iAa2DkfstDrX7cd$m4#[%u=Bpj[WMFon&OP1W@P]>TPO_
%RI94\pHUa*;7WljHmV.&^Al(FJ,,/J@frqFm?>iCW&6eV^4M]AKA_[c[.uIiSssbZa"H\'`QHaOS+Z1D5Jp*%rX&[!a42:@ps\87
%en6WL0AkgO;gPjr\p\X1C;9ecn3=fL!FICF1bbK>lbn##"<J8mNpD#(H61(5YeK:7O*.Alc:<,IhN%SVCqYiQU[st]o;#X)#KR3h
%_20)@gn'BmN[<2^--p<gDm;3HXQkg$W41ge$m,XL8&E3:]D1.37qb*#%,sBs)0jEpSWA.U+.%c9BC%@G`kMJipcl[Q.u`ZN-1e\<
%&WHX0?\S`JGaWXNo@(m$!lFHtE!;hV))YuLcMm\oN;o7DW.`]O'*=*MQnNE'G\s''Z6-saG;h[pF.s@6P7i\@m-'83>1q'0<,]e:
%Ri,d)jaK<YIsOG<9Z$Yd1)MTbakPd(m*"urZCGX3Dd2=SJOoitm&0_54[gp-hfm#NB/k0apDsPm9it"iAm$*hL&B3jJ,,.g<Q'\:
%/ga]DTC0lZl?q;MGsAui,;>!.s(knZ'\HI"[5[XnrF]4[+;Hia-j<Gg'RL@EE[b3q[o.X5522:_H&L3,Q<"B?nX\WgL2TDq[5+Mo
%6s5`mnmt;b4][,.H+\NnG>Er6Jn;'J@3R>iN4nt/r1@]l@6J-O/a)["/rLH-GaH/=oD)L.50A90Hjp$_O3R5fr&ji%YY<m3D-\rr
%'@J8!ZVil[[$enIYtQQc*!F'L/QpD'4mfMoS.>8F\Bho1s6&$]leK.]#M+!;4L*TIYDu[%\^/<s'8aq7mlAq=%QbI04r#cQ>jou8
%D"O?4hm(hl=A"D/Q5LW[.>#ihTs*uG*%IT=iCHbuD'>WL_[9N#n%URS?i$7&Q.%6RV8??^5N_<uEg/NDa!#7^DJ'*[#TqE9H]K5O
%/BiXI/2,G@lhOk,.)#d3G9b>H8'KSSd=5s6b"oLF6qQlR`k%(cjRA%.+-1hRQ!*eOF$B;-++PVSJ-SrMo)0BEru"#cR(l&1nmrZb
%HAZ\R.WSj-VmIO!5K9be^OPqS3IAh&N[J'2ZgiO:Q"!UA2VA@??`$7%dfQp9%m,[1d!C07a#7]>N#`<4V"H4ao4n2qiM._T!P!$S
%c#/"1eoL@ndbKnM0.9nL4TD3>eqe0DD`I%,pj`+#.N-oSGZd)biFsL)5A@q(I.*sXEnl3AGl4hg11<<#A0lW22l$B]bJ[dqHOBI0
%SL9l_Y@<.<P(Xae1=c&FM_0*HqmXKC%#]f0AZZ/\=G/['/u`jEcc`]Pa7BdnrHb)cm',;U<TEUnLAr.*BcK@H,:B6,Al#IhBdea,
%eaCGpL>:8uIf-Ie=S/Bu(3-CaU]71*."<#cAnL.3I.9;5&?=kMHu+[eZ/2<=o9/Xqa$/26mP4$gHmk-tmj0kaqd44W]$E/M"W=Y8
%Z;VXO"l[8qKlaM0rUPFtT5_14d8kD'H2]5\o4;Pn4eD\mLHf_T?i8"?Nu<7,rlqSCAImc:_EIAAK3qAEHnj"HDRel%ZCHHA0%d4p
%^;B/6_LeA/]'-&sS-9lf3;K12h](ihqpXSmm6>7Y?i8RGO-t>Pr]d]W5>ai>92<A$Y2#).bqGl`hVAoR>0YoRHo6Hf\[0nn\6A;R
%DlWO&7T`?7#YEhCq=>n7h&an_CAq9Es,.s:rm_-7%KDJ?k"jqVI5,_&NpCsAODd`0^mKS'kW`A,/EA<FJ\RoXJbYH#C]?<E^\R![
%J,dFTI^B(1a'X>kiST(Yji:a.k3qgIs8*=Vnk\0;IQ[SDrdXt:Dh%NYIif)Rs7:a,DkHj[+9)8ks5j!i,@KHoan$Fcq!Dua^)l]$
%+FjALJ,&]?bUiQc9EbB6++JXor0r31qqqAG5G+);ELt%^s"i%($*7[.\X!8\PC1Vr-gs]R!ZD'3B]^AWN=4ciT'`XHLfAkSaU"44
%NWYLeoI0%,+#$P^R,PqQg!TkD'#G&6LLH;I5H5=DqDP8K0JK0$<'1slNWFYE+=<Z0W>)e0<ViA_ZRn$(&@pPRcphjk4Eo#f9HcO^
%@d9f;bm'r+hX5;,%->G>RV&Lkb.:0_R!-8T;7i^?5?#^63_ie.Ed_AD/0u(T/heD?fG5XF/42%4/e?=E?,IOmc*AD8p4:<u2Gqag
%*ldeQq]"";m<F9;"i;(Lf)WmNN'KesMJ.d1T*u"e9uoA?_XdX0T]\qO',;9<UEo<_l-S)^[\Geh66O3,Mkk3hgESru<sG3l(r;[8
%\^IoP]@mtX$&%V<,AUCHFbKOm7Q;l*WbT6oD(>okB?[AN<9*HudNrb2Y"A9l4goVnpQ@uoPIJM.Q`=1N_C8Y6IX+P0.F,Pr'uUJm
%HuemMX@Z5j5.b8uIRGe>mFHZl)Y81`g/u;"A1Y>>*Lqe:DfCFYk7hMJ>V28<[@\5/&cR_%1DG:(f!Qb9Ao/+?'Y-M,IYfVbs*i(K
%e$pgTHf8chciRVX5$Rp%,R"K6Z<\QAM1ATT!ZCO@o?-Rsp6%;M(.AudAc\UerL?<WES^mZS_UlOb>RW9&K7\N`o>Ue`Ap"+?=?G&
%F2+SD]d3SR!nkD`QA[Zs&1Pp#<u0B9,Fg"/\Ap'kKW(7RTtXbO+G!DdotnJE@RR>K.$crWBDjcMcP\5X^SOMo5htm<=q!(><b0[R
%O:_YW+J(7#_I.p+j5XdNY4ldC/oQI"WEfn?g1)esUKCWc!/i4?dS]^C]pgP%X<(p;O2!^C,S]H\-BJe)LS<W8[^?S_Pu5L^bh,u*
%TC2bI/5Hs-DoNNlouKbSAj._C[Hi]Q$bZrtd'*5LSuZN%>W"mc<N`PAelAC"*;(U!+ic><pcJ/[,U%N)hV'OQ0eKShA>L*J,X:QV
%Ia2bG=Jb<M#\!6=,t.D\_BLXM&Wrtd:Fb-+A<iCZW@Wu%iXf8Q!KQE7okN&#+%&4=n$F<@$+kj^\;2Rd;]f&VpQ)Oj;rm/;2kq^=
%bRqM^>`-Q.6595e8;[c^Gs"=0'!4@rc7A^b8]K+jQ^*r015Ig(N`SNS(Sb\3FkuD5098_o_fCG^<lb-`rA;8"GstK]6LVU/e5D;[
%P6&iaShiUu&h67h4R/*F2_7:&93)a0_P.-k.]^T:!D;"u.>d64gV8l2i!AfIlR8jg)BkAG+,YF;m[k`[XA3//`Hs89*1D@P,*+Gf
%j_f4,!O+ZePE(<iKQRiA?GOP56Aroo3Vm4AD2Or.";;0.PAZ4e8r9_^HSZIfQ8pKnXb5E5ruN;#;>sQ0S7W]K,B[e7fikIp9tHS6
%khp$NiZO>_/R)/&?==Kp^oE9o&R[RR_8qsk.#]$%)K,%F_X&GjT9a3Pl+?!N^!$9jhrDlbM&,QVe7Hl^?5V-P$i+6BO?!bXC9;VA
%?GZM!K8-GFqi[R;N^)flXVW=jTnrNO>om0F<M[918J\:,cTG)3?j.!D==>l@1?`p4FHMJZhP]KE>@*Q$$#G_^"3tN4e&-'B52$PK
%l\b$2J;cGs@%Jr<Ne?ttkTb*_%i9(!eu!Zlk_8!=IEE[c3@@0OUC=RARr_<D(lZO^:GiJW$"CbQL+*8;9,@Y)@a\.$=E`HRB$AaG
%,IFRI_WY1ncd=pl;&%:,+t<*q_Bcfa/NuI6[l1&8A[m`:oR"J7kRA*;lEnc^dHZ(3%7D<^)#.84*6qNX"rJI;Z*b#M_<F!E4990Q
%$fbHDnmR0mMJ/r2S2TD/Tmq^(8VBUMpL'H`H5B$2Li=V?$MVId@X/Kud(a!<+5IS3)=8T<K.rMNAP'cA!-N]-i`9F)[%YYYF@J-(
%HT5Q=UU`6[o%[u=Y^(H?bNl]%4fAAe]hT_H]TM,7cBA-A2=[V_Qtp7O\_(p!B`tZ^Q0i>]]sn]q#P`JlDI04B*g@n>:#pJ05H3VB
%Xt5h`r&LVm6L`s78jBjeDXQ>&k/79abcm4+!PPAPpKR#q.AHK0pN,8]'$ntY:4`t7r$^!KFCqe#ZOW>W-NImrg%"DSWsaU6`8Fd6
%.\7pTLhB<B,`NcCIa`[u)_)3J\*guQMnU$^q3NaBLs(4u5GS)@^ChIbkE#ReVuoVa!Y9HHWup4Rns(H#86Zkr@XZY/`M5tbGYFE.
%-!Uh$23,+94m6FLL1Ep)ar?W;[j0H(UA?l@4U!F$XGdFbn?)cGQ^eE8&2[4OAMaAS)(H/7bZt<)fq=_dh3^i)%t166[C(]+_Y^bc
%?7'K*P)i7*`aTBB;Yjs&hW(*s./%NY3o.//T5V1[Akq_CdFSZs`c66In`o![Vm9`d4+\B&AU6H@q2&bsV$/7qY.@e?Q\NS!0ql4Y
%0?_"8'?Xh&e_$fM+09QVjCQmB6PWf.XJDrHR>2)VcQnW6eH;IL*=Fm:1[Pr)@_k!t-tS*oh_;n*N>)+)!@;)C?G;[RbobuZJ,TaX
%VAOHbbb)abO7N&MeHeh_5I6$_d4eOO:)'7B?FlLnei4]oFAZa,NC?^O,=hB<EANk0d`*NE:=SDd^n!CjouQY80HGq!XZ8"9XJCdX
%#[>DSb%(IEe="1s"%;tT\6$=1;>`)(&r8l>Qr:H9N="R'$,RG_G:^K\CH!IR[E=T4l-eBu5LmeT=;)8ZKV=ek?fWkkW/2=J81-lJ
%,c!`Kl+ZN0grsm=Yr[!I,tl,PmbmQr&*Z>gGDmG`#@:(qFDRd`*HK8p*_8"H2gKkLB4-=pP^AN>o:86;mj9e"YXi)f""pCkL2`f3
%;<r?=b*ESt98il5VIjLdC@jZnD9?*`%l96$%CR9o_2GRJb2&%4`pn34J:k)`]jCs-J.NMqh3tV%a#t"'QXL@teEp'#B,&+$EY&Ri
%R4Ljilb]2([-<f28Z7Ur-]UD]s(?q<%&Jo/0eoaLWN\W/Tt<#gjK,&A:tV6D?>k'o3gK=i-qp[(:,P%o-Z#[A^RsLRL+Fq@<ZGkS
%loC'>^OORF6#n[+Hji#,4HI,uG-sJPdAP5.COgA`Ag))K%q-/03";+5A_jX42o`'nFuPbT:)JY+Ys#sa*An]Y%'?Qk:^E+?/J]+j
%<=nRtlf0#,i$EZ_H99U2PORjS`aLIR0`X7-9Y\G)&&iV#;XjmRr^oLcX0Fkdd6#1$rZU?s9BHTIU7,Wg*O4sAI%ud.ATsK'N0kP6
%O4h4i7S:R&Y?t`2NeYP'rNXiGi!DXPIB"eU25BHlrmemR\.?jl.JrQt?(G^OV<TkWFqAJIf2#/lMg6q9XRhlb\gKZ\jY=lPY$8Oq
%2+Z_`<EMAIL>*@\ZEKO"QE>@GLT+2Q9_.geacMgq<hnOZ@8?P+7+AWH8W(7@.)WP];7=k=F"4JO%q@O3O3eTjj=I5:?[@$BX-^-G
%ZnQA>>Y]JhaA\4,QdPcG?n:3XB(j>*;K4#cpI)b.I!c&'MKu8LRFE$#h%])@'A?P*S4RbcX8H,1k<#fAmJ0f5'P+2gn6V8W4<D`D
%VSp]d7<u;EKWnF\/A)B:!31-NPp>p,*9pD#Saa%``:5phIE-Ho<]o,0d=W=pd&Zt.JPVbh[.odjQ:U%JpDuY%gsZls558Uf#->4N
%8%oKeSj(qe9,A_"C54BaUUJYf\HZ<JY/h"E9RdSK;p@gCL[U;":;(p=(mIX,+hgdK8Jqr3+,\R$4B]nqFNOgHVoK7Xm"?Op5&G>u
%:<[U*[^K.QK8u[5L%tVV+43SHqPr`CKsinMDIcfF5d:j=EX*').G1pZ[SSABe".AeS?eS+4C1aiDbs&N&bUnJiCHKgi#Q"'njIG&
%d-!gn6Tq.VU.Xuefj$$G)8VZeH!h>tZ8YWI<,]:hBk286^dBTJ7l$qnPJJ4``!V$'8Wdd8jS0^c7?O_n0*JcG@l68%hh@D/VZH(a
%aRKFA]-hqA8i+0Oo;_`$n#l]B\]0jehSBnugVoj62AD?o?!ji^Z8to$aeon#lBJkZ%CRMg,<2VQ0A>'hI.OKCqR*rbA70"Q"Sini
%/-]lc[2n;a@(Hc=O&C`J@6=#XVK"L`.;h=*,DLQR!H)5`rXh$DEiE9&*"%jUK6/%=CkaJm;`C-.cZ2^7#`GGA!?Wn;<>p,:[f#b8
%$*K#[0D"W-*"?Tn<>-6D!lAJ'\CHf4kRdS'=3JF+"'2)6;Q0f)gtY<\QpVNAJCD,J:acJ!Ot,Y2NHNcIQg)GsKqenlf+;'L%OWU\
%YL#;0EhB'N^ZL'0jCd,EHO)(^3W/dJ$-8lGPb]rf`JeQIIEuF>k+F2W%/P%9ciuj8!W$e#MtL+j'dKC%)mbD-%k)q\=7hQ$8nsq$
%DpFhh1nf^+HK<-b8Pl4j-at<L?)/MpKLM]M,HCq,e=leKgQK[I+qR4riP=9'X+O[(MNK<`_QI\Mk(VBqK8MGEP&Snl5F```5J&Sa
%&m_$9TZ75;ok&bHDs[p-Qis5CjGea&Ai_<7G(]AaYr?P5\E%64B^^4fYfm?h5!D/6KU$VBOe6#156arPjmdZE*-B.=OG)eEPBo;M
%_^A[&j)=3]69cnM!F4N(^3,>Wo6:s*ddb]V]X;DD[I86sYjcK%!])k]q33c,]I6fTFMSR+EP;F@ko4q,3&7!*-rel!m+n?T$(ro'
%[8f1C2Zu=LPnDn7*^%!u?PisD.s5CTdolj@s-8cWI*;qe$q**M+$5T=e&-7:4qDg;(+a!JD&8@VC$$.[%Zjjo]Vh3iL8Q8_l-Rj*
%X1=6E1*@?eO0aYsQ?RkSU_dDd/-W>Z3n(br"M!eQD\84[&^PbLRCc9/9=o@=CFZbLPhlM[+A)L@j6F:M$?/u[3D![XPu45PVm/td
%TNq"9;tLR87UXrXm!$QQE$Ph=G'*;6A&l-J8CKbP`,,"LPO'QCYs!I_,&%AW]&E(-HG'nKmSFgEW:t&U%tdbu]?H?S/ErDr+@Blb
%i(i<_l(jOTq0O^Z:3E8TPY>T"6/2jL&`eoT0b\@9*]:5LS4[/_Au%,p.d\9acuCA[^VJGRV29PCaq;KR\jdM@<)#1LhH-JF_lj+q
%o5U;teDD8io&_`m[=`X9>'F6daE9?oS<0QJq]fo2'V^jh=$3nmq\J$#PX_PuY)B4\JDdA$O^c@jQlMk`2stq7JKk27O\1L&,Yu'X
%o29'5eAIYK>41uR,LMO,]X(07f=$U9!#n-/o@(2aoi=S`goCVT7EQA]n1<Ee_P#hf*;Pgq)gmH=muC4#rBJ5,`ncMf`l;X5=M4co
%@jeY,]"gaN761l[1<2Pf'g4fO<dT-.'F'=H+YrFfB[Tf6$*71Zj=2Im&SdBoOh?_Mq*8DEm!+oOn:b77V:tFUcH@Bm,``GP[4b2\
%)498$gkr-@3D92+P?ltDKl"?#\S7*-B9h/gh8jhsKFr?ZB%%M='1Ib>>J<`2q.MqP8&9Q]q&"5N-Nj;cA/MoY@:3umbnH`d,VIsb
%)?H8lje1OJSr2JX3-KtBUXcJjC`abiRX^-;OEnq71HcII)H];IVSkCH67Q*UFuDGO2'Y/(nK"J`/5Vdah+4Y8['JS3jQF8oaHn!=
%"kX_N9GgOXncHXePjDL\p\HDW^F;pgrO,6cBH!q`"c,/iDf>Yc10gX\#_M'D33JrnK_0f]31o"6_jV9P,"I<IS!k%\OB4k(\G_UT
%//Zr`<gJNc:ZaLYHX2otBHPdEW01#*LU`ZE_h`Q-p$>E0/gR5eUG=4qJrB-SFHgmj?3:3+rh`R>8<7H$HWI*a'.n(>q`?]kYS&Re
%Hh"'.Pr^]rC):j<=2!H4pP(9F"Dkm6g5s"&TR/>#Y.31(-<Bl:!0<Qh7mC4Q5O"'&'d$&F==>CD,1F8b9)$"tH[WK9@bW,/m=!o&
%+e,.&pom3:'CNSEk1,7,F67<j287PhfLCWF=@_RK27pbsI,6A<p4836h!5<-/U#O:!Z=@RM/5J>IY5LdTjf]7QFVCm!1J(fTF@$T
%QAt*A8i5iK0$K6u=.)%`2eX@=A>,jn>F=U]a=qO/*`gcGW04d?#Sr)Q>3%N?,&Rt3"^r=t4+`7gB_JhAnL^"O7164]N-gGr\(G,V
%eV>Fu1a+Er2RSd"j:Enqi9`FYXqb@k(e*<mXB9PE=9g,E_VhY_m7J/G(5>8[@;2VI$),(B.$!l"R2d`#5<@:f_e#KLBHa+W]Nfsu
%;]4(dL`p[g)\NWKFC9.+3oN3?h)4p!9K(k<1?NCm^hDT[cm-u[5b`?eE$c(=]]LJ]O\SNPdePQajhC)b1&<POWu*mfja&Y2:maj%
%KK%jQY)HcS6DA1Pm_fM^^eh/gm@;\nRj5E.,4A*O%K<i5CC^E^-6=QlX1>/pi7^0L8'<D:_akqL"_S=VikdA@OI,Zc'((qhkj!ik
%b4$r-kI8/F@g1r,pM:$_k%h,`Snmkb7f`h`ePkGlWH,6[L.4gK:`+TgScH>%dhDbHORCR<MaPSoQ#O8l,chSi<Jp'ZkAs!BL4GES
%gNm9rV*t"^7#:rs0N08#0"!jsZjrQ?;EFHsD-:Q$0#3d\j##o-[d+nB,3g/DU\`L-5K2\]RsJMWY`Qg(-e&HS!r6`s4SW[SeFk3#
%*hb9:(ju1SCb8h&#dK;GXZgcN\C$U^FJC;4;Nu.PL&FQU)kMjf71](8#T4ieF:nd8'1-D3^oJNJ3o]Y`9Jl$SUq`PKClHi;oqqo[
%>DB,YCH%DDIuhQ&-c0?o[j)QK@r,fJ2H\*9:Wt&\J@Egd%2\_Il"`[kcG$ktIVb:K?oR+'.XA9RL$30+fOKQN%@C;9S_5%SB+ZI7
%W_!mlLmU*oG"UAlA,pg[Ql-7n"Z/[>C8VJZ.2^29c)hdI2Tr=,-P*qL&?t?Ua9ODJL_j,KMEiTV-\KKEICkVM6]]jf,.O`4-,dT.
%.:1=aIssGZT.=Gb-`hh>p#u?UHSHRur"1%=!.4V%ppRkAA^:WadYtpMDh$jLHZ27i90Sl.E=^M9Yg5Vj[lC*ZHbtXBKF>)1bG"3F
%F6OIXSQr9JmFQFqLaj#XqNS"5_*cI712Zct6uUpeU1"$'Rd0ABEba=F7qjU;h,V[r<q374,EFN4E,"4\F>.(Ckgbh_a=h@c<Dn&;
%N,A!iYhWn!M*94R5]Y9p`sD=iZ0cE*.7-]be2]Jo7qSY38:TU2`JfO83;PI=aJC^m,)MW=6^s!i3lTle!1sNC'[D?60!Sm0>IY8+
%U521Z-W_0$1^qfg(J%7i";PqD6D3]ekWi6R7UDeB;XG`[!/V7/EY4&Z@f*3DP4W:\^e9Ds3;H8opguP<Kb+6ha*7ucG=<#E]LS<A
%WG127KID5c/B,b-.>O&-%=rV+Ol:mg$M3o2Qua`fieW#d4lbZ0W;T/$f<3d-l]8hr@Nf!)LWfgn[.pIsg68#FFhsp'>)rXnSL_Ej
%"I>/KT_dP^,PeQ9X8\.MQ7lQKak=pSgqrE>$3;.X:Cik"@UBDZES@3lNs^]LH3A!;-F4"VlhM-r02-?mf4(WlM3UiU&Cahi77\XP
%QgCR92kiWjrT6:G9--\3!$@Merl5DK'"6O1Z[E0+,RYC<*IbQpMhD;!XqH"qeqJ8:ISU/Fr<<k)`s,o(7U]o4JSu8#T/hcIZ6A#A
%;I6]87GmJe<EglXZ7+<^!,X2_OQQ0M,:2q^Nnrh.JE^u63Aoa/J4iDoQim*r54VcfJQP`bCip%CM=EoD3s=pc`0SsWdfS-o66Rim
%&!1.oIu`7$d:a,-M[AF#0A@`Ea*n`NNK-_UOJ,*KKL"k^D,qD_+lq3m)A&$Q)247Tn*+367#9cC&k\hK,!M$$'JT2EAKH/A4ZN$>
%p)o.eRi'E5H!KVf$?P7</4`&=2Rb4d9"BVVV%0P`_guPuRl#]M/jF1WTci:Cg!9^tDF>iuA#rh=UOEFeg!E6#n5AR<oprRMJ<#s)
%R^8-Qb(YD;EkrLeToOZb^l*/skN^s1Ih#=38k`Y/R*L#O&(4+XK0djVIug3u8!tncp:\BFksj/bq!RA(YR))K![c2='UpP)#(md+
%[57r/["#95d>X3ShJF-/W.tbUOb=ge?t(>cm_C",VE>)/;]%V\X9/UEcL$9^RED?=+>idZ]YY^>Y!@%h3`('.o/5Q:_4)RSd6*b)
%:*-*!74.)BGns:Y*s"'u1P.u&h&_)3V[kMC.E3BY`-i'!dH9rVGM4+U[?O1k7b4aD;nJeppkWT#QZGS!%U5`DiNW.[%e-imn;(<1
%BX4A7E]akLQ)T"n)Y]J!]E+M10Hb?3])(VJY-M15;UV\%&df@$-E\[8!mMXPM=p%`A=)L)iAd.L/BcoS(oWdK^nl%,@eLaoKU1&K
%_f?1__c(%X93Za&ajP-)7ZVd%[$f&\TXa]O&OLsl?aW6g,r*%V:Y0J9'9n_<"PfWKLaD%UEW?XQO,ETNX](<hf]+E,Age%g['nsD
%[P'L?*d4^j=`2Oc6+UGR:?Sr4$D>)FUP_@kTb2Q#;"X\j4GGi/okac/j"<::_(\B^/k<dW3+)%sjDi"?+3l'o?IrsoUM^'s\&sq<
%/^>Pi*5UL($JV"M@[2I4;kmN;4kLZL0q#%'(QNo2o"a6V6gZ=?CqhZ<3am<(D%ZH</(&N+aiCsn&%JN2#Q`Qj*#kt7`JY%dRj+K+
%F2AiT]H=V`b\0>E?);plNYiU@C8j)$%"8'eJf4A5LSnA.efIQ/AG*e`NP1)69bj!+[0aLb,GJIXBu/r34\u]BiAVUO.d<,8]58IM
%Dr<r$dO^]jLUHJ^10ph"6uW5.!QDFed`W"r,bY;Lfk_Y1b]mu%R#j_Hb3!>,;cW(YYYfS5c7ZCu[UQBFYOHVc'2d_tc%]_.]7[=?
%rB6gKC9Y(4XW<-E$B2`e7$'*h#fKC`4<>(J5mVENc5rC):t.@R@_'?>MkF$M6B<:oQ(j)8!KPCQf7Be34:U0on7>*C<lS!%;TJ*a
%%+<aK6Z,<R[VZ"r5Y%#PY@al#mMYk]r,eqjFu"9gjc#;!rS#*s!aS]DjU%lJ,;>6J!eXe7qsE:#,3NR4PD+bVE0jui,WiW7X8`ib
%1'@U8*Cc9EWE36[p1m[tmXC@Q5Tr!2V`)F7=6p9&\p<2,+d8JH/-n[5H?b.e5*EK=X05K"Q\N>8=>V3kKMBNNkiYs?/+-9,7DcL`
%QO>8?=Y1W\[UJLD\88$AGDhKrA0[AD7/u)hiZVZUKEp+ETfY1+'.YU.]U$&^7c4d0fD+P<*tVUmg&d:qou.2fG=Df"LV\X\\<.i4
%V?K(MTp6sn<0PS:a&1#Yf0La&l$3e.LnNRi0Tt&tGt5WKSW'kM'%-cYrX]6&>O"T_@1Hsj>?&s/#:&Fc&;>smG'+9fe^58BCJHs3
%L\UbH6;OtFUR@-)DN>=D=nMZM(mj@r!DoZrQkiZk/hTb59].NIU,d-\I]p>1KaP88fAV-LQ3=9gG':jH8@[6iLd>9+H^lA`QIMI@
%MT?"=C@YVA=(`!`!tls6j#Di?W]J!<2Vg(_Jgt012K2pT%EfA5`VCRh2P`I+q`m'>$*ILAS$4#VeT1lHC4B.I._IgZ/6X>[NqrrZ
%;2f>;L^eF-leU*^QUjOSXNPJ"e.F"nMQa)CM8ni2Eko6/mQ_CO`NG0^;npc!-q.-,8-Tea`!5@9c`#:NF^#"7"dk.l4,ZmlM?<'d
%A"ATEcec-#-5fO/</%p7ds@\]K"I9#C4ULT_d5SIO*U3[m$R[)lBMgmBag2u\:Imk7R/`T%YYG/%6u&SejqDsP8TM=%4msSSlg=C
%aT.3#Q]b`%kK?0MMG*L%PkN]*#-JDh;r-?M,k"I`W]V2@YZ4lGqbRa*_"9Gd1I5J;<X[DWg/$tn7)fEH#:[rSH]Rk[CO>=M.'fC,
%$^jQ*iW4cV0!*ZEI2IW(1Ef^WL?quP\:uOcF;[bJ1`q`._A5%p'CP,@*0Rpa&6Q^\p87s*`c7BEJZ5)m@N]W<[)9rrC((tV^5AR1
%JUBVt1+rrDX<U4F>d^jK`W@@qW!6I^,dsWCYNJu_,-kUC.f><Ghr#IC>G<<R!:_9/c6fMnmgJcQee5PdN7n"n'ZRG6K?6jL@UmD9
%boFm%,"%2h[j%]*kYusIU=2g.]$Mu4L_=JC^YM@%UjOHGL$]7O8d].9+QpV"8>e^V92sCDp2J+q55PpC&^H>r@%7JeFPm!LNY4_R
%].r^#D"ObBF2Q9Q1I-\Tj<=s*+O>TsONH=u`TAD3+nFMK<Sf47ZAGq%2q5G9Yr[`$""8e'DWN]NZq=JkX33"MaE;+QAs%4JG=1HQ
%<f1f!eP+)tD.HetYV**N#h@QP&o.aV".=8cW#A8p@5Tu[\!ioj%.`+T-Dg3Y0i[V_&9uc'#H1ooo!+gT&BV,@03e"-0K5]>8!ks[
%(7]%n[ZHia?*#qV%<Aa:i&Sa^J0V-3dK+@odR6>+FM73b-&PdG,Z7;5*;eAT7Y%Am9+B!n>6MFaL\M&c,];05Y96p\\-BgIWcj[n
%,SA1o;L\&S#X\9(_WY#YSrrBWbcf6l"ur2Hm?*fgYn.=$ZkP4-O7Gu1(m$cZCLPRS@gkJ!C8!@6'[hR@r&?(F[a:,sD?qRVQ[3E$
%U_G*bMQL?KjkZQ;LbhiNl<a`YJYb:c,qmZ9R,>-o=n_iNl^,,\k(E$K%><FNSjb#$!%K81`[XarD2.IL.MI&JFuCR47W[^lD1gkg
%ro\nK&(u2>+JC>gQn!6rpcd5&EOGlS/HXd2GZ`]h<'oD[*GSR".Aak0GQ^NYm9H5-?>6?R/N4l6Ue"@_Cl:PF>$'6;<5mC$eS3eL
%o4MW!'kR.\:e-^k/dLZcF\=DGU"^^S*FVGeRG`@8KHu_<(8PBpV@RMrlG[r$4Md"6/n,!ETmE8[Urj[8KM0e2b,@k\MZJ&gCauVg
%^tZ$G*A$Km]29"c"=-/kI"aW+o!Y]Y6@h:mpSZ7Y3t:_DH(f2@9QpiPb^dP:(C!7$5-f=?d3n2T9M;oj'h$a](Pf6%K,,KqZuIa=
%KGU#khopLq%e@tiY)XJ-d\r0NJ/e4XA-@.#nRp+Y.Gl'Rcr8Pt%,k<5Er*Iq&<'Tt$#hP=]TFR,-=mLL"ra=UV#dH])d,W^]TTo]
%.91/U@*;L1&SA([2!c<9S8]^5>]'jt<3bE:Ot,dJ<+-%<QaOpn2ckQW*-fGL-ulpL@7""XUk1TTAu>TrjmB(>7r!kuH#,O).QWRn
%#E9t'4P=kLE$qYjPGtiBV=,MrF[fQrRUg%\Q00oGW#3UW9UT?>$(@A12N)P4LO6D'2]0/[%2VSsZik2k(M]i(b"#hY+-hlfbBO$8
%i=8m7W!UC#Gc3#;/nfCPnb3;g&(O#K*C$!F?;A*62j-$S^lU;Cal)C<2\H>p41V(`<D?!96r]g]gleQF5eK8h)l0Qnj9TEZmGl(O
%d0ZVpgLq9s$sL#r3XZ\X&^Y&fTP)ZdF_qJ0,N%%mQ.FHJ)R5#cs1L,k'sWY&A1SYH!/c#`mt3H@ppHgPo(cM-jd(-L*@to:ET\'7
%7lOM]ll^[-K0'G%2.]HC,,,Z#!Q"/2l2d>0eS[WI=/m9)Ir`GERNi.hlu`DU9[ATIb`O]tIu&2kjIM@5Jl0+-@lo-0[&"pa,H59:
%;\:I%YVg2/MLYgr;HeL)8Cf,I"6;lB9?7g;*aYr!IOF:Ndm`f8g"l]"^](^Wpu.Qhq#.DrdiUcBAGnalhnT0@n,M?bIeVUso]qQ&
%r9Y#eIe`PZ5Q'r)?iA7<^\dC?Ie3H_3T'jq!IjkAJ,&HlDu\!&IM]rmk8SmE9>CAq^U(KBoh0gG]TW2n[sq[is$]+Eq;Zs$?iNF.
%RfE.i[+BfTE<j2UMgtD\ccEm#c%gfDmI1%tcO`kM+,g'K[E.gT[;.KL$jJH^TKYeHRd;j$i5^"?HAK][G?(5c3Y]t,d.Tbp<WuEg
%CJh$R7Auqa(MP9O?djgD8n7=jj0rn,??@Aj!H!`NQ+Z=H:l*/r9OkU*01dK2OM4``j9uc$(Xsk>)O@J;lFFO8/5*ZY$e-H=cR&c7
%6t>_\RlBYLH@=VW])5s[n,)iQ+"Ol0ZV!%Z,N8tYYuZMmj!uU>17bj,<PpZB'=.AQC"YrpZbT]$#i/I:Tu-$egNM@p$(%>@",f&f
%De9"3"@:m=Cu[9RH;t&cp-O]McL+O!%:Zg+#%d^W'mbP7gs8-fPj0J!'O@<^mM\DP.&o@n.g/uV)%"t%/C.^!EQAV5`\K443uRpK
%G[qEED1b=iG::[XL-*?&;QD$=G9r:e<9fEd+c'ia]bTo-0NEHO_>[oa%5H4TM-cL!pkiLJ'kTn,",<S[(up^4(%;c`CqTpMdir[Q
%"<,fbEBqBBl#e[93CS9ugs-g)An`O_oYOMY1CRd/`"dFt/k"2VW61e3a_5T+?/s)a3/j71D(4T*0H4/,Tr+ofb9Md'@@GsK#XGrh
%#^1VeYd7ell+'*NfJg!?'rWuD7XU"fTL$UY&2c?nL(%&ZXmF.5o/.a](gmM#od5o;0$0M@nE`?/p.^P-V,X?fA5SMDdqq.^Yu)PN
%6!.1m<9dBL<P,5Ehb;I"Qlek-/a!#KGJkO;Xppl-QdhQ8ER;A0C/A-,Q6]ba[<&htE3b<$L&Xb!$4iBU"Vt#dacTHDFRi\6HhuB8
%0Q71hfI80njN;)<NZk;H\k&!k,]H=c*`<jk;E^QSo^l[o(!]dMr.VLFK$;)M,08Mb$mDQ\^PU>sV8hkYTHJDL24[s*7&#>]?!rf#
%rM&(9)57]C4]k.g"5KADFt;L!-lUBT!3u\b,>/?!O=T@%2M3:o@0L*"B>,2WeQOp7luB(1VJP(1=g]oqObDeGN1:87a(fc6Gn]!F
%K&%ZH9:AuCom?=N0(o&M6.W5!cM#/S0dGBX:515_#XZn_JK$S9!U.+>NB:iejJ;kP)k"mLpOZU"(F+ijhuR:(`SASpPE9`8"*(kF
%'%SW"JLgjLFik,g2a?YF,#"(uJgQoIk$aU8&pDA*n)^[bo)2AHPDl]f>[@$+h>I<AGWt#c&HWGJ4_L39Thch-F'DBN5(q*R1Pc'3
%Qir1ImPn/Dp#::p!^DpUF9oa'=g#@MaV9VUY9FL#s1Z\OVHEeLO??#@.R1NcPC\jug6QK.NJf>K^4O1#WCsJr99PX@X44H7#Y3A[
%nu`E-7jbik2G5-3's;rtZD]Cr%392O`aV*JF*'g]lIpu\/?)8O!b"kIBfFO1jR0`M/WuZ&d;jNT'$7BM\P%=3B$/P(8H.`egih<-
%`eD3/,c[buC,mTc$.^[$7!`s5!/PGs'Zmdt`e"\4llBB4gXW@s^Kt2P\F<RK,3'UL3ZWL`h'0<qA@<>@`DQ1O</cf\\AZ8IAj^`#
%\udYM:M<=,d6ae>Uq4Z6RL>G-k5#2t'R:'NNPqn,^'\@>*9;uBD-B?9P>VA>rN_7r6t5!el.tdq%'HhX)7KZ#O)2gnME-?D>l^0q
%.eCO*;:T-^%pAG,4TJK]WbmDZ!:p[<%aa7+Zj1t<Rpr_COi21?*NW3b^]WpcTHo4Fl8"HW,?Q9'D8oK97pURl<*g6!R8slL02Egk
%5Ct5>$jsEJ963]&dnTnXD&/g4d#c'^%B<<j%5_4/W#R>@A/QW5J'V">:F&Lg":\lJXXF8_"WuHX9Kb!mad,=9TrS8)jWnZ=pGi*Q
%"J*613)@5p89qFcGeg]iV)G9gRW)\kWiE3D$'4Hc7dr$m[:n@$^[RpEgHDSu(iH"HcdD7!%^9^1:_kc,aO.RPO)7tN=@0P%&G@CD
%\%32?AJ+SV#P_*=&[FZ4l9r,BnkBB]^C-`2Q3uj-jTpQP*6h^PD"utE<VN7,Po;u10!I+t120XqMVeQ1B=5"[cV^`...1s[Zfo+?
%0IS/8M!@Qli$63u<RCe?2.s77-?J.[)IRe1L8thRC>,/TiU$IB\#Hcpb!SXF/4C5/f'52:!B4:i!C.=-="NIs5s>]!kHPgsed"hE
%fja!?IM0LG6H/=OWd.q)^6&]@+.A#s=DOs3Wk/,MC*4PiZ<]7RSr-oYdH5B#eCb1UOJB5ZoI1Eh"-kOG3jF),81HsBo1Q<@%^k!;
%@7jY*$md"j<k!N@K4A>WIqH5&(K]DI4(rJseP]6"<L"S/Z#'Si\I:*V8)#,Jg.\3oU]D2FJ7^(laoc`Ii)$0mKV?cb(#(T%d]L=j
%;8mGEd-nQU;AWoBIZ?iuDSnG$i@_!md_;(qI[V5?A7OhFlV%?@EA(=%+00\Ld"CPV*2m&1.cB!/8O$6n.m(2(oF9Ih916(B34t;O
%Uo"LD-9O?dN)!ZJeV^oL9,@Q!BGC?]2-:gMR7$^/S%MDH<EL_$J^,E5S%Am#n()VBeDO!.LHCCOUKGc36Ns7KQYn8?\T"7nL0e\7
%qU7-IQ>ZM]<!ja@8X6$[,,0CI<gE&-i+^i*PkN]W,59&n+ac>.5?+SAILbZ^N2W(@_('iW)6rD",4XBH>Z@i<C1M9$U`1M4EOk=A
%en*?lc-TlGgi01GLjFJ)mY&Q:HA?38kd!#M4b:>d[+&pE-EC8\35*/<<\l-_R[H>@Fq%qdc>YWmK^AED7Mt`<78T[bmiRNT]6s'2
%eabt=[WI58a-/M#9^,IY0=?VV.G?QT_=u@Mk;sT8<qfns/pnK0\jF/lTll$`ZXroab!7c+.!Ma/(I\9#87IRVS8AQY1a]r*7aFk2
%\LE&*V>1tEgt9QGp36fpT>%B5^Ac[cqs![>s#a9f5_ljAG,[R&HqGZ(-T)V!8^>\m-#`*ag!"2\eHN-HUR*2Iii,HDXT0P87fJZ&
%@3Hp7p:/=I$8:W6aFRgAIQ3KMaET':i\kU:KmFSblSX?B4EbpCKPXKAMZ$Esei9a5R?"L>#J^N1q=&3aN+d[EPUas1XXFP0lW?ZI
%*NBh#9_X&p;\cMBP[(+>/GV9:fL=Dn\V/=T#UX.qaql\WN8\=eZl/BdRa=UlR+\!@+JlY90F)W_0g"7n+S^!u"p019c'qP2V:Jfu
%%lqDE&X&%GDVU>5#*d*1dKf8GM;)RK_/>BrA"s\!ZMIKd01GOXnA*3*A@@K@NjYadeR>na]2\c<85nEH0f@ku3Lo<Pi$iXtdbq'E
%X3#KObW"30T!q;ujIi6S7mTJ=UGnU<if/.$,gS(=I:%7k[8cEEhO;SQVRqS"KNq$gAG`&=i8U-CZB!3[K#t,8.%)AXH_qBl)>hAa
%k]b/9.!LIg^u9AW-4BR.;I9U.oiXsc4FBb_D=W8_m]6gW,;Y^:Lglsbb_:_&7!CB+@(TjX'f!lFZHHL)[LY'o:#^5;"\>Ymm-CWK
%L_4qK)/jM!/!;S,[=F>e'%nm4ap`W@2]G#R!m8jUE7I30/[Q^d]K)4<eKbqjKOgPE#YDgWb'TLOET]S8<XeQ:kVh<Ch^UJ+"X/O=
%2p'+%)]:X)g\+OjL*NVSAnO>df]!X/@P1Gg>E/.oGXL"A]!its&Ii0bL*1pA!H+c>mA;#8GE:H,6!&'VDRg004m702!8Mf?@*=MU
%1bQI+VlP-\>>Y\=\bq*q:>CDlg1#qE^@#t'#kq*D9'-nj=*3Kt$7k!cL-9Xn9l8IAE2b?5GE^+oR._j;0SSbB;&dnC%/[;2'hn]l
%4,$cpM1i;t]=r9S1Q-Wj?j;HH>(#5kn;%GULPHm/j$]`bpSJPrrC#:I/7k>1['GahMDS^sNc]te`<"17mL>2l@TFt5Xah.8Nchs1
%+Zp`j;VemVDgG,Rn)/rGi#J[VBc'mE^nVI!W^md@Bc.950'g.WVl0\[qYeWrldK_o4>I4B0PBTnfDt4qMZA76N<"##aeq\/)]kDA
%5_ddile>QhG2\&5eups5i/;VlWZhRWZ7Ggnc-Uk^>rHIFhAdO@q20g1.1+6l+GL0e*IODQnkkt1='8BVLf7T%?,Ok?.bb*-<e^nX
%3cR7kkMS!7V7VU<`smF.=:E(_FK#c';GaA(;[>=$Sd'YD5:Wl&\sIsm`Q>IeN2oQ`X=#O6r.?dCgJ9mt2;^3Z&jqMb0%`bGl+(D^
%agS_<A@`B</2I@YSnhD)O&9btj>[eQ.-</I'bln\BL2kcbol4lJL5s%YG/*8898*5-%2h!!:/g5+FsKemMa0R:F.i+$n<ujM!EjC
%k%#&RGDjL,7d@!^HE+pm9OcuWC!+]1^aTfYE0D3ocb`"$?f_i2'A;DKp?Io^jUe$^ijQ%91/;V1j=#PbmDUu\(g&Y3%`=K!INBYj
%.+2-G'cr$]'2,js$bs+C^N0@.]$"W"T]V%o2m\;M8\8!mpAr`1HlH"aie8d2;.Yq"-&;5H#g&j>EF[sd=rgae+m^]V1p/Ll*n$fb
%-grTFI,F?VB1k`HWcjJ-nB)Ee$6#H5Omc:`"q:!%Wna14KKFD`9SZr9Q;_cLEZX7L.h/uS+GY]a3!c?qGHb5a&5M2YU&qWkH:R0g
%<3QDZ&-R]TTB)Im[1Tm\m9W%Sb$uKu$BN]!1O7E3K<_XbO#EE79"93SK430)Xf_T)cq(ZWpQbuH_d%!AU0q)59!a8*J1VNMjJ]AS
%JUI0FNX2\DMAk-b'n\m+-Z45'gi1m[p%#DOYoT@L!m-^H<oCF0\%=>(WPVRsdP4sj[!^j#ljj0$(\onO;_0_l;ijpkQUq;T(e0X<
%*"'PN,fWegKqd7oF<0)rbSn!bG2"/rfI&dZ""d<U=dhZgOo)"F6s-6\lk#P&!':G$#)bSP#XnN-$F?&J;3Gq]iE&Rr=<<RYE0kF\
%'-V,Ydj="j*g'7O,^>Jd8H4*7,&uoTl@JTR0W$>c$@cJd>no!]Q<UqodqT8HD.dL>PYf]2o1X=X+=hLR6>r-efP:qP++sBP74iX[
%%D<k5HF9ZN-C<DdR&jPR$U:MWkuKI'#^7o=a4R,/>S>\8.l7i@9KCN,I7lP*DG6jl\5RQ,'C24/_D]1\^U&2gU6Qt94-GS:P>Bet
%Rf*(1&mU_1#>`:6fJYWa]/fJi_.8qLgM)`@imh21r/KRq?$+"(p5bDam#/TPme!2\aaU")+%]M;.Pb=hJk=FeE)b7nD0Ce`S;1NF
%,h*I+N/BqZ_:i^DRc7"u4_uog=mnG>XR#)EFP1@?Ph2=.:'tYU"m6$rnVQu@3nPZ6p\!Srl.QTimJ`o6fQrdl`7](7\N&qICgu/(
%P[7]6)bV-=F\.68OoaifVLjS_psl1rMr_NU=etfGbt"]]",$cE!Vq^F/kRkH1"WI!#75r(Cdl?nM5CJ]J[QF@gr2ZlE2Ltj,$*T3
%Q^?l<Jj^NohB%0Mp+Z7Gnp+DSF!]/N0G5Uu9,N6JXH1;'M8F+?C:c",Z3-\##IT,V:up&,aYkbI&Mu_A:ghsZfBEqlPma>#HQ^8,
%`h%62B6QGK:6ids[<_3Z;@KkUiT-Hd45/'YEQMI8PS'EF&L>?D0kop3LL]>]2nlsj+H3Ap.@X0m.7^1_VTrOGnYRraeJdU@Dc'eR
%1,ohrE4R#&UE\507H0iV2WXn!8%GAGI$apZ\##Y-inAr@iQ+J7gpg"d*P3NPiV&:C'r&R1<;VQeTn5N%B)S/c]s?SaRL>iiUOpeO
%h?GDYJ-?G"K48t$iSk$t6U**$=2unlj#*VoVHrOQ\.Z_HJBPE.cn(#E'NiEXa(Q5,3VM!k>Tp*[rCo5?2l?/58%#F,bP.fV+HP26
%iPrTG8#m@[NSjL35TPgh!Ya:.^d#cA9(9+Er.#^Yi?0CQk&(BG'nX7gQrM^(XQ`/mElh]t^<\5$g7OD$bns>^frtH_.b:J3*D)N#
%?*hf*^'E:&^2$NTq._ScNC7sF;>GfmIeOfP"o)@;1R3ohZo_Js"N\nh*HGDp7Gib\oRi9=?h:G2*=5Ht3L8k$BI2?mf`Z=tK*QZ?
%_J,8d#,iOWBK1.t`/*Y)P3k]+?<qQIMkJ??+T@UWbh8ndXSg#Ek9hpY$#i\ieBO7G8nO72cHVOE&`@SnBDZ=`&eK^7(P'sf^GVHT
%5<?o0!UUFVGJKSH`-/SE4:l\SP*&%d]5Jq7fSZo$LQ_u,Kn/Td^g5X1`b%/CXU[i,;R#leKnZVf*tV#Q0K>8p.bELbeXhD5\VKtX
%lXP`F#"?\p#d?`cUtfo>a>8U'0854l%hW0lp[>TR#*5cH('o("'YeX.^FHGc-QHmAlb/@>V64Es%s"aXFKDuVaK_`q$,I`_g9Vc(
%11\iO)b'9u%LXkPlRmG_#PB6Fas@.NZ3<0UE)+Mk1`E?5/L/D+&m55B9;FnP@#>7sD64SZ(kTUf+0!U&XLNeK'EoFYM80rTP%-k1
%,D>:"BUc&c6oRh)<6^r&\/1DP'fdNN+2j^"W-5&=8@Yh[6\t0i(W/S*PEd"--P?Re6(=nNXcZ<%c6t`,G[#<$9TOe.n:kV^&7X(.
%PSApZ?tJN&*!`&"]>q*RgD9oONd%lg3V?7^S](W")"reG.%G)'A-2u\6m*r]*?n7/'W%bGW+mTI'i9!+GUk79Us[gEZq`7.@C)]Y
%PiY=n\Zc<%_WM^TMV,7u"-[.UefVQ'd09%LliER2ke`u1U88^PI1\2?CMqI?Aib&47UhN79PR#LO1Vq5$%!ro\jJk8)f<.W74TFa
%gK-.e.6e`hYj<1CL/b4-;"iop93urd@7?[?2*F88`$[-572UP_c?ok9Q)#Ac6-U&f&!Q@'o/6UmU^qA%h!,s^W$/'_FE%t@HJNIu
%nm$hr@GbEV92ZD)O0T$&P3'R`o]QO8o;pGd)Ff+L0,Qc)8iT?Xb''KG7_3T\)ps-@3VFIQVW#O9G]<oA'?NG)3`"j-SP6o?(khPS
%9:Z?GG]coQh\snZV@R5B)Rn#4-Ra;87Wg-uDNKX7:$P3)8RsE93e.D"#kr$)lN@8?a""/j4G'h0C^-)V8Og:<hfYIW!7.8naE>,5
%Lr'F&+@5&E[2B8J]:E._F2LKV2[Cl#jpDlBDm/4KT\.I7a/)Ls;;DEWf+fhL7uNZD)`F._OZ=aSMTqLM&WglT=qb.:;ZR)QXS#WC
%Fg*O#obB,"]0#`phIWQOrTH^m:'$N._9F4+;;RhafZsJ!5YC]Bq16k&#p>Z`=2Bt@1dDe(PT5[Q41aU,&S,7U,4M@sVm!^GpA6@Q
%FrYq"LRCZb'MZH@!;Hr.Gj`oef%kem-hD4i,c$dUO\=c`DEVi1`%p9jgQS<ji'Y/0R+g4k\u#:H3ph/O&slhG`F0X:\0Hf#&b<98
%gCKK#O2Bg8_$Fhp=1fG;e=HNuV8r[a8e95W.]P)L!Q%7%CSBa9l-XJ06#h?$G\-Q[L0^ch3[)4*UglP.lpPN;oV5[h)Mpu*VrZJZ
%2+RMmG^m6k_Y7J?52$4-SY*?0^jU!/.EPhbUXp`cZoUV_HP'?:mKVHD/OLYdOVJho82V%i$72;(P)Kfe(Z@kVj;tmGc.`;W+\F6d
%j$.TA=U1L+BHgB#lH^3.J:9T*Jq%-(<G5l").En<^W<+%IdWYn&_I*i=7ioWdfW:M'N#bB8,@G@,)/f^X2%BX]XjuKQia@4eN7ra
%>lLa5&`hlEkR'N3qWY,l/`em<1a20M=?cUXm\`_F3(oPr-'L#i/f?ni.i6B.'JkAjrk#BearZ"tTg@pV2s4bi[7bO*)L$WuGshrt
%aTeZWQrk2BKKd$2VRR<AZiIQO0Y>b=KcVgOgUBp7^c\%M;*G,Hd8.`E/R?u[2GdDu9'5U'2["^s5MDMW-U.VqcOVo8b^u:?U.f'X
%^]Lm?_LN5XbIAA/N;B#J9S'$N\'VJO1*C[5LReS*C(6*)2kV:+\X)Rc;%+h,J'C54H9nY2YY:bbj\K,H*_MZ.T-k4k.;<.I0\pF&
%[k8,32b+l?s(R)SD//se0VD/OJg"k;"RGo=hN3%29*?hr`nQ`%QTh>-O@o9RXjG81Dr#AWmbV042^.I,OEeiBR@4;(ALY?F$G`6.
%%`TX\f\=aSCgo.;RtDDOmb1tOkLY"*4!L#Nr_$^Zg3sbuU_DP0S(^2@IW:[K[>MpSL.pgKebr_-Ufa.]OtulEL#8=93UpHX20tt'
%-@#Y;b)kgokW6PH3qJ:XKBe"/go_^,-^ek*1XjmlD\\D,N4:aTA^9.]%$39_.o;GODk!h^=nCR_BaA2Kg7*;:1lLZE>UGIs77ifb
%D)hi_(]g/R]lQ=8>c9-.8)<_4DD9-QS2m+i>_[#H(h3D^?1su'M^b4t$#.LZ8:#SDr?L'fA!?OH!nk+5?cCZD=<#f-j4sj6ql!YI
%6q7Ki42PXhKV,D@r$u87qX[7;&h]_>Mq'5nQMn-lN>h$,If(kqk<)<4+8_)gqC`&;aBs4@r;YD?s8?f1G=%qb<iem+ZB=8;6,Ag_
%IEKp]#)#iB*3=I`Tmr"6K]jV1eQAr_`tNue(+jqDQsND9^i1T60S<YI4"/!Hr?bqPN7r1q,.fQm,"#Z(/)!WK]ri8Ig3Fh-DsjG#
%a>]cf+p$7b450brWM2+dnqVM#Q:pBaD#'g=;(pWDD9GX6(t@@SOoXf#dVDDD()=2OV-c@cdSRj3M6'MK5+Ht$9er`Ap]R.Pm[@1B
%FkXARJ.t>Ii32=Br"NpPG7XH"jdbZk1W!uCEX0I%QLj(JMXst.^GR!2FcCoj-j%EXJ:"s3>a:mcE+0,Q?`]>=?^MQ2'^cj2VX?F-
%jaSRSpFHFfr]9^OP*`1n#)"V=7s/M97-:&o_b#9(*1=B#_'%)RI\-MI]q!h47XMf?$GQSAODY+1qh0527Ts)l]ISW;gebg,#+p\<
%!jMQJ^Y(7UPA]hg;Q=J)X%1->CVna'/nKcQ*5o67Bq,UG!*Yqf#E"Gi&M0L`7O"]G-Z.@Qcd*6C[i1gLm8)GRA:S`lL[7T/U6X`R
%oR>d]9bC$//*1(8Y^3?8)"hnEemkIL,%=7g@K^+pWF!M3!21.I/M!J8@]9uo3C59[QRgDViA>U(/Yrs6C9k#meb'tKolep3DC9%L
%C5s>bC@-^l#WbuZG+d&m&U<79MIV2-W5TVT7RcU=jJ^&F=bMl)E\AUG<+@KoaQD57VamRG]A8rV"(5(/`A?0FPVYhSK_kS]>8rpV
%=QI9NWL)]4EfP%ufknW6"Sd)qA]YOt.Is/`fF2G@@aFc;<C6R>mo*ib=M&_]:lPON+_$j2_MZfBOGBR>C86'l&lrs9JIQ"/8#f=H
%H$:PXE56(B]XrtkE"nqM-:ir6+Wqj25P7N4\f1ZcDMcio@=A?Jqa:<U@Hft14:^3^dt*1pB6*'FlEk!d&MNf<idTf+a/3BkF]XIe
%<Ai86q06@u.3'.f`M2>U&\*\r%1:&E@F*QPjmHP!,FAJ'YI1*rW0o,u_PW<Nec>=gq6o<1mnq_;_Ls@e?^<6dabZb@qY"&YU[;9;
%Jjra\-'<?aK1L@!P3!H)!N_u^"ub*9\;Lf^]/.,4@;bL7UYnF83>'-FHTCZd^i0:cOs6f>A7l\gJEJ8G'#4i!*C'X,qU=kO#?Ng'
%Og2bq.Qr`]-7],5LTP&];H#*'(*0=*VCQ'CEQZ>XZd:N9;im:&#*GUFbl5Ku#K>?%))s'eb:[0<9(3"G"9#i.Jo-u0TUA1Z!GN2o
%$oN0m:fq,%Lr5j_Hql3(P+32a`ljLEB^?SeSbAj3gLC@F,,JPb!Q'5V'dam*'Io?0p((M,j?Hp8i1K%E;?mE*<7#ZN-9a0X)ilms
%d"C\)Z4V=ZBWmV0:Vm`lMnQ'*#j]`mZ_BL+SCO>$c"(]>L]KQ'Pb8!V\HaTq\!JXkXKL@':gi)7LJ(PV%@H^nCY;!^h]<]:&C)Qt
%?8[Xm<NBuhbp)r">**Et7qB.0*7[)AXSC%@PIo@'GD)/X=2QW/(3*!%h*j50f@iL!&V)5q`gfa^bVF:JIW4aubgp%F%5\3(kZLHO
%(U<5jEWdM[]$"tF0(^M"+PHBI/6#h1o"'eadJ:&W`GUa^^J'XCk"Uqq.?3mSi?(Enbgru_FnK,W;[Dr8dE9,&@?[1<[H//`+R)s1
%+:*_rN0eJsml#(`)h9d`h-[5kF3t^%9,/lA*CBBYs&laAppoO!)b:?6c(I9B?ikNTGA^e1Jo<JOVA+a0TC-m8%:[92k!qccr"khs
%/Q$[ZG:s]oi*Rc*IsH.pe6_RP6SA'6mf6>42#S=Nc.3B*g=P1iC/]\So[M\)gMqI"\jcJ>_-P(C!a6T1EQ.G-5^S^`B]4q,S[<'<
%doGKC?e7fOM_7]!Uc$lDnjpn_KmctNckd_kk3spdUgo"?hQ&SVNo#uJ$<:A'ZdS?k+*&4nYaQ&!fdu`heoXK,iF:3_i[^+1Kdoc5
%KLDQ77Xgc`da0'nc8t'^e;tb.lXS#$hd4^*84$hV0PC&:4u)@2-907YAn&hmSb<u8C=j&"PW%?SR!3]CfBr@39Nm;\S?:F@\m7RC
%7_/S]-`RIiq6lQ0+d8DH&4JV%>nn=;=tZ&+2%n]+5gW4D;0TgO#?m:5p9$_2S$2C71Dh_*80k@XS7lTi7Dd7q1,=]pJK;\L$K;/Z
%U8(h/7iS7YQL,81OP,\nm;0ssC%iE!S29DG!i4T!#*&KE[]a=T"dH,%Z>or7p2VG*ad!S*"'aPT?9n9beJjrBZt*''Q@DQ+\do5=
%ZocaUTB<)O<JsR_Q14/D#>h?G6o;[Pjo/j-4BX:\;s&]NI_phDeF&fO[qZDqD#ZnoTg[*5Y]poU2f)sVd59$U(44sJq\o4m2WNsL
%-b6T"Vf2Su,8s`W_*@]f\'H;rJ2]8gZ<I#l4V1VV>E\hJCGTfCLk*>TR)'VI#Br@WQRb:O<HG85"?i%<Vp165[&_!L1@s>KdnKj>
%]\ULShC`:FC?i7],G?rQeJ7;(p=+7"[>Ij<8.NsjenPdNo-r,a(B)k<;Q[lkbSH"LY#)1A'IBhaZ<G3f0;0>onl.i7nC_OUl)`qA
%'kKCu'`Q7=7fWE^k<$0&m:6J9#6;G5TbU6o[^oY*]o\(<KHkkfl_'CfOZ*=+`nFG]eV'9*$t^fONDP5.Wa?Vl?0Qol!5eu`db)\>
%:i=spPM$GfT:iVi[#%CT:&>]8df\[c4CkMP>Wpj&rX]W$ZXl%5!JnDe"$tm!ndpn5W8mC([q((jRQZ=l3XnW[5X('H,VSb4<4Ai<
%A>Yf"6@JS>@BUnj+BAj$"-FV\NmkZ`5DIf/&EOXplN3c>4]oJ4KF+'M&:WGjGd!g!)_g'qUR@[!@mF/YBGQWHV4",Dh/R!0Z/m1O
%J?RTN:Q*FOmEW.l\U]lp0^?Bg'$J@TIO9@m);Ugc)KSPTA^,'@:Q#m-B(d&JpGNZic"hfqC^bipp:@+g1=?Un@ED81h$tHd\K7A>
%ga8L*2*J'saXn.``TA8rh.ae:%s7<nf`k/"eV-%9>+"h.ptf@qm\%`cM,b1D+j]-=AWEon<RKq@QB:%i6/MADX=9B%a\&+B;?ADG
%pYsV=@;[_sZ:\eNSE17i@>oNVTeLm;-`t4*5JuXG[T!`>p[&pens(0eTKO!k/Ga221AW8A(@O="?^JKXfl"MY">jbO^:GZQ!]k5n
%n3taI8T\=mKQSqW#3SIn7^<n#X&A&T:nHdWYD(,AMar$a4;CP=Fgori9asA:%/)1[:iFNUg;@ORaQd&h>^+M+>`)jj-mfe]<H/OB
%&A_o/R,SdBg:r7C:47`Hn5SqH"LfTl$!YpT#SH:/X[tL[mmGs?#Gu8gRCe8ep(Yge/pSZUcKZs;!p(mcA6DQ_LD@4P:mLI-ah(b1
%HENRrrD$UJ$)n9@Z=L--`d(=.Kb5?,1HFB:$o"GP:c@606C0g0G:4Fj]pGdh1]*1((h'tjO)e:S3")uX37qC@6D6+_`3jh[BHeIL
%*.Zb(+a5F`q#&(I3sjX=E7]LETkKqj($5/%a/tH3QT5pocdd6A-42/4116`A$3:&H1]6$-M.m$/osnR6!sQbt5]0VJ;qS)7pr/9`
%/4.dn<j-\B+79,f6)\+274^m3c0dC+@P&ZQs%jZDIr$jcL:Ya0)%E6[Nh4#0I_F)K:MR(tdt<3ED@I99fF^g);.5i'!A2*QS+$X&
%'WqKU=Pnh`)6?6O^f2A)o4*TCQ_$f1`ZX"mL"+_'Z=p-p=W&=L^e>G/SX&4.^.iHQ..gZ9i_q]k']6QS8B>R/SE4?[8Gtk$3C<@A
%EIR"N!A7cg"6UED+RQ'4NJN(B_%T`Da;bt$?q1"EpO0cREN<F5`oBZ<3=tl`=LKK,L^5$%H\<<MN=j6GG6HFhM2-a26'Xafj+U?4
%Y%CqCA8([%2KQG3PUMDB/mbm?`Ej"PYqD(#6\C50UGtebj-kl%U.W$4p!76(*Yg"-=DXoo&ML3<pT1&c&25\5^sAGe#_Pkfec+C6
%j=\Z81@"@b$;au-X'aBD=c!mfL*Q#,ZV4XJ-j/\`Q7@!PC>BK#PRYD$X`Z:OcqCm;Iu<VtmKfd]Vek>..5Zl0MVXaSFhGUC;$!;$
%U^D8O"qGg*meVb)Xq6th0r;R6]4^4UBnSqm]56lIK2*0CXP\dF?,_$<`6^.V=L@GI%Ef4<O:Bd(!O+_Z_Tm5nP=]n]@uIl(3-Xd"
%IE<.20D=):JkZ`dnOtVpja`^oeIqbGA'VfF^dF;0mXl_99#;X(h\9W7Q=K/aetrEg@U<:([OYg:Xm!:T=@DCdfn`%rb'#Ue9W%J)
%M,HOU!%.Wfh-JMU9_%V<e\knH_N=U<FRVA<jD3)BNp>s7Ro>Ug5m=J_MAE"(oCRlQ<D%u;)cE#13Si*)ZHTh:6,D&EC?6?u#ePFb
%Dt6>6\jG?<'ho\56MfM[aH:_#AJ@9hbI]DO)iH,k(Sr)[)?qW2s(dNW$b8?CK-ucrBqLP,cFo=G6P0mB$fRNq+=k(E(M>kL6I&8S
%7*\*gQ@g?H0TS;GBr]J6%<]-J3tjB"A!4s`VRK/A>THV*16`0fLm8TkRtVcS\VHG:TPF0[bbk6(KI$i]*k>R>aG?u%d&cVCX,N,b
%"GA/(@?)mr=`f):n\,.NOf=,/,_'[_@r."k+m)%$?6$Ce,CmHqCQ!MCW_9c(>&afCqAhlG*0PLb<K`7AW!ep]nP5MkC(?H(q;k_n
%M4#>7C8Oh39hbG$7n^D`!aHrfXo(2AUbU%q6:__Hdtl9H(r96c2q)b?7F?:QDY*qrP5X79"*U(<Ye$M.qCikI9IbD\p>A'ms'5"3
%9_I=@VbdP8\EQFh;kqL%^W]:nEUD"tIRpe#Hi].'MA/Tf%,@IZBaj'S996,lhfcBfFuC_,9'eR58L.)N\toH^qcc1D`e<UF_;Z_,
%`^5j^Dks\W_>p##5Y-qj3!g`Y^H[C#$NeER:5J"e]t+@g=UsD'Rdd5]HG?U:p3Np,iI@X,r]935*S>8qd;K"IFm.X4fIf"qTI\b3
%U+^88-$7(;T4X3ERZTn;g>fKMJmjg"W,N@(Zj!7)C`g]*G)E/B.2pMDUj\*^d)oF9Sm?G+_HFZ_P"c$"IDX?lJT7]ag'IX?'$d5J
%6Feg!2MAQ;jiE@F!T*E/=5H>]LL>dlF30hJ/0$9a+Bo4:I8]16b9b`po,-j4U;'\)<iU]n?aIo*9-Z;.61`u8pPig;>?GleSJ'_@
%)/#02,4%b&,gC52ZO4AN'3`*Yj4#D.:#R"P`5ctDaTN.L#(gj_#eUEk:YQ?OZ6;T&b.^f]__Nqe.6#ZqT`d7>O]/[Rdi?BFYf<ES
%YU=U\=W]=JIY`9VbJ:uH)@OB`UK&h\)JC0rZl"6K6AGLoZt`U?"#'2))]*Lm+f'*LGCn#Ijp@pN9N)I@S8r&Ha,@5(L=i@p)fV-;
%`lJ`M>O)4n5$.h0&<6#OM:>M=,!7bio.OY1;uDo`o/)fkMUW+S.CS!'%68Jr\`#CM>?WO(N2>FbFg;!T'.s0GQi:81qF8U=aFgh[
%#\ChLkf-p\)!=H/*Aj-OX#7AEnIB9"cjRdXa\D37L_Q[k,!Pgof(ln*+X+`Z2\r?E!V#juEmrI59-=7b5eY1"$u!Y'G;oNUj9`P[
%)1eAhKeM6XkSYc9.`VN[9H[b#BgCVP1=Q;Z4sOrSp+$Dc1t?bp6igeAeE+@&]_!h\37SnANp%'$0Yb-q,5rm9IdQ81^<p6WJZEFP
%\=QuC0lNg'FL1Ut:".c\(mQg/jNUUj5#Op1EJ/l0Ma^D1#uYM(<*i"u>$5KY7gKG:aNn2:`g?QQ^tWd2>5G8D7#@^Vd'gW`A)b/(
%0nRM)ZWN1Z;a\.tT#9CXBEb0h.%Ec3JaaTmL>__S+rPJ[(*dWOEpH*:Jk9Ao2bRP.G&,Ea-s,%gQsU;!r+Y?hQ70RODR@T4E8Md7
%]<@mp-:+CBLi$S>(7?h:-@GM9#N+$t'"Vc^],9/P,V4_DnA3h<UVb5!H+tG*EX^?E%QWT6'?#/_8.+@gdoDMtc>1gkZ.qT=GHh%r
%2#otZH[&T^APe.erX_UkB7:P8cuU\S#nk]XdbcT<9'^ItP#`'t[4M1X0._9O`c'[:#*`1L<5s6+L?Ykj@=0NLCQFqtJ`W;0"B4/D
%fP\*-mk/jZ]W<NPM9m*>8T'ZM$&uoK?6:IJcgj!^_cOn?h;8Pa%JHA[r5r#e-GR#nCOsDd#Os!?[qg'6P%]9`oPj?<VQKtr9!egZ
%a!XrY>^IY%"m\mEm*%-if_.]qKW,25B0a4$nLloZe2\*t4\`9sY\(>*)$'8'<AhBbR9Z?N2Z62266MB\P%%jgQ2nm,Dtu)'FKqjM
%.o:`T5Eaj$^2,Q'pC^P2?th0mC:OQ^cs5aDJ7J"(akO>T4i:Q+LA5RlSiG?SiF6%eAQFX#Q9NpfTR_f]QqE3_//FD<SAK^Re%9c6
%@989gaX0GHbr0pr&i29aCi4"'#4A'$S/uJLC(A]A^uW[[D]V0nP6r@n3D&bS1mm_n&Ao^&4-RR;&duo/4u6M=@<`+[SFHkuSnj:D
%qtQ7]d@@DXLfmH>YZ?r;@m;<'.<3Lqe585QegiR75h'[B@^)@sV=u$>#GrIP.+PJ3ifABl[*u<s$^+K=I,"P'LuOSEQQ>d!IB<&u
%G'QoZUol>%4;-nA]>.LB1P[coP=q@R;Whf6Qefk6LS6n6k8uS+E)+o##.-AC?1,>1i=qIK;OYF-3'ij/`tJcW]j8dMgS[>$<Ifh,
%9\ZbUUf@+CCJ69Y?-"-fVa[F:;V]_G:XMOno.uSB#,Q93gc%c!18.@C<Li]E>b!X8n,%(X4&fGliFdK**t<*B%sL.1^P!s6F2raM
%=9jgoiMM6kN1@b_OE\r-!8gr1$MpR!mgXrY[DA,IbBZa,i^'qY"1d`3!FoB$rF_s7[qf`19]=Nr!1Ho-pIm<R^6!Vj?(WB96!]RF
%n@RQ>M^\OuI7SA&Q^0pNHf3YjMBF&VX<,6#STjdOB]f$UCYp44&L<$.MNr)k%j'!b(a*-#HIH[73<8[O+5+jb@:i=fX]l$f8S"K_
%"1UC^Ye&#oe5c!tF_omk!,,Ak&7s*k?^GP,3.i][c$;f%'G&'+[J26>0IV4Y58U\!8*dX&hVCe22r6iL?$/k%.6amZRtYBH-BGV`
%42_3?I2bLsc:]d)\ot3&YsN=h7[UE_)U>&)l%aRtA>C49P:s#!,%?%6,(UU`r$mBeqSK7@-SfDlG(Ym?_if'(m#P69%IRgu@J@gg
%;(kNi:'tF$qR(NSbO(1qb@d-[K(rB6*s9#u2SS3;<aU%g0u5rA(P*.*7-Vg=OXCH\G9=`ZU7rJi?$E^7S8Bo+Z"A33CX_o$AFUj!
%d8u:B\kk&#l@/;\]!)Ej0]dA/O5P%m]U0"pZ-Ra&OH&Jm#ZW0)K^ZAI+:5":C*[H"D"*</0R6p^Wt#!7T1Cfj&/-P@,9s-E(h"5e
%862.EKF3o@QusCqjaCu1ah4*QOki;)NLV4/8\P95ZRj'FKGTJTa@,(i5V#Hehp&t.2j-lnS>WINgNdP3i&rQV[24iaYl^#J.^Da$
%Ukk<^9oU!Ld8#+FX9OTU`$?^^\?+ZF;BpT#Gn>/M9@+,Vns<AN>_S8mfH9Me@^(R!c2/.QLg9E314jFQNI)6$cm9SR"Ad0WIq;*I
%Xi,dDMO\(-Pj^Ja+C%X4h!Db"23&-!c+PU26[7d7H.F:LKq,i^[$:HoBChn)QtZsM)r\[TT\Z1%=W@p,8D#$$=*sRT5BY%+fP2n1
%2uOG2[B(OcFcOO1']!4e?/-u0Mf1T7rK>:,q+qKIb`les<K$4SUAAg:M.qL%?Ku#'<JD]UO]Sn#$[i?mIZHQWOc1=(*s7=J,1YG:
%/U)?[V6EIEVH_)+aQi+RJggaDmh&%5&Xe9=9bh-WZCb.9KVis/b,UusS,+jB&*.n?fumh!iL+ip]BXe5^(1j:gn"dHDPKEM3B)G-
%S0b&#Em4!`>)dS!Es\LFq="rq!"-o[9"Y7_]X70(/(>",i?l\2AdqbtNZAl1`HFs(,b"SY4)?daT`!T5GD;sO`T^9<P#@38_O#U^
%s*j>_'$V>nMuYQLEAPKHXRRb0R((tcWS@_mBkdoHnWP5Upaua^1)9B$1UAO%AYm"Wc>]`sKXkH--M6'O*<o8C1DSm%9^;$8YZD[&
%=^+3^=RH0`fqCO3BY4G6U011\FpZ:""He!!VVD"4Z,kA<kq!n]?%0);W#76GV#-o[/AE[BgM[T3Wfu.>A?e,i@FihG[]_6kR$LaU
%_%1gOH&\ChoC?!^kf,iZYO'lT<Ire$;d:U3&AIuTE\/$e]./HO#4U,[X<>l%"Z0-BS0e3Ef,+!c`8a20I)ucF/5nu?karS]#FDIV
%aX8`6)bAD)drn@T6C9CU<q]00!H>sQ>P4!f#G]aeO*_5/s4<'a1jg&1jbLnjR9tbPW=^R1[m+NGc4]B>.CB@hKRRna8FX`"!\S9R
%P2XV;cT')Z5@4UuA)H,(;6-J/"F*%%##<<U&,I_^4:jt8CPe6^2`rYt8'S=$3PThADM53?CD4C_hKfigR$bNj%j=CX[Q<XJ<]0:\
%>uTkkhL\3*"b\rU92[AE"FY^H<imT;%^sIJh<S62D&3In\HDq`@4+oo()8_H-m$]@,*VG8?3nk#.$"DQEOUfR76cN3FjcLqGH#Cj
%W)$)AVn[n-hOiO=5S!V"nhKRMr<d=c#XapC@fWr`f'KbmIn_j)]@%O^hW*n[8sK>M3*"e>n\d,?*lWZW[A'h&p!ulmE39QRB&h/V
%4dK-/"`j>SN0)Wck055='`Gp2S#=O,cGQZ\ICmb<qLd7U?pL\/bgK91:99#%1q=0l"tNbHQQLq>0Ljt#=G!=5<<4;/[IGQRpOPp`
%9HHPrrMWI@3Sr\^jlMY/P[l+heuNY6V%Ar/G*1gT.!LGe\S+I/ijOtCd]uBHITU8A#hPo00dS">DFP.2a]4rD3?m3"6Z.oBe5#ZV
%"1M"6jp9*!lOeH%d"9Tho&121iSMG"L%>Xc*D(Lt>?JGV(bZ5#d[eQ,4uQfkG)9?Trd,^[`raMQ*0TH120E-]a4@ud@l5S\O$H$q
%%rn#CLIAfglKE;;ArkA\`P/QpSr1UYh*CB_0d<joE8U&-s.2,7m*T%M!dT*Xa+g0])u@6Brfs,079>$\GgI$KC0\PS7Yd+HAk=Ba
%^E$lHaq1QQ!l>Q*G)fJVd4,JPetpeq(4\(S2;LB#68Me@2ikZt#YcH;/Jj+$l&jYL\(s@6,>>mQ`!H^C_F"mkWXHr!m07V%7S-.s
%K0o)g!9fYl=]+B&2S[rd@:n!C8D%+&GAr6g*KfSpk:Jj5]F/r,ejWk5Y9So_fIq-C8o@%8@F"@e)F/r_>e-Yk^hU0dS&2'!I0c6A
%ef%m*N?O_oYC)6uqgM'/]L0F,/PjqhWf(3EU4!BZ\];MV[Xnf^'jTg@CYQ-;!j#1DOM#^h(Q*fB`MOcl@i&nf74Ug,5nir4@/sje
%^VlG>?'\17fVFr=>j[5]L=8hYjUlBr]Pga)WT'5LFd_Ysk-7I)Rk>.`MiHOtd3cF"1*Q"rKi#,&/pN,f(na*Wn-Qa#i=p\Ga??jW
%+\i#km;`ejClhm4Pg:5rfYh#HMF5RW\D]C=-b`;tL8LK#D^]^'=3ccM>;q\XfL9_t/N65oVm.`(_E5/('>k(R!p&\HnG)l2?K467
%^KreTT3(NE_YX2nYP./2%4r5[>X?0iQ4,fo^b3b*RF-:VQ.X6V+i%Pfd63+.S`-e$jC2,.>)!=9m3(o2#8S`"[[G"Jr6RO>,D\L8
%nuN9.So0+O\iZ3N-<(A+JOYlOc28ir:tCCkLf]D!m>oO#Pck]E*31O@OPG7#]r8o*\]Bqr%?mg$*#*F_FpnF)5:_$ocD0Yrq&k-R
%_V:YdEWmkq6E(-fP:F3o/GBn5>r1V#5MS,fH%p^b4#@Q4:oFc`m;D^QD\V9W`"<0^!L4(je%&>$\d^KJ+e1"J>2S)U1M6MO]jC8>
%A0o3[$>X^!4kh!;>c_Jfk/<pFS4<a%S:3s.[#9!2ZC'<tLLZ+dl\Z,5P,"=3)#5=E&6,->CsL5XEEn<E%CV324baHq&I(l<15O0W
%VkC?SS@)M;BU>$f2/\jLY*KYdnL&t/n[]0m&'GV`3ta#ScD;)IakQLag34OCbfRhre7P<oo'T]-/T21eQX(bXaIoEQBP;RqcnbhS
%)!&pf*B;I+;F*KlWha-]4\e=186)a;($aLM)%-M$/N[_7-`VrdbX&M(?0@FMM2Z\WllV/_F02*7_N0P3(p[?m_@!+M0n<FX8?T'd
%+^sOJRO!0Xf%@jh@Y>b%EJW5h?9p$$BT!FfARLkA:9oB#em@5)jVeWZm&h&h)If530OKuSZ,1N)US!6P4S`-'"+N%fa)2ZiG1X1P
%OKJ6OHT&\LH2O$hc8DUTRQ"I8.mG"%naI:u>c77f/;BZ8'He2eLVfe.r[t"?A;Rc@A:%XO%<<L#(ToR=,G@fqc"[e)Sth*s4]3FK
%<W!lR"YPn3.B%nH*(09>hdQ,ZIZd^mGD+1L4NX-fP?\iNPUfLc_B[X;FRoCMX!O$ZGB46B##^Sg@;.#8@c"S-bsH`='"Z`1Rn<Xo
%ReWF-m8[R3b`;BJ^iW"UHn@@F:Z2`E2TiKuka.;IX2a9H*7j0tDjU5LhmBm5>qIs"8MJ&:!:u>;jG:)o4j48P4d/1D&rIH5-<8NB
%G=RsV!S4p>7jXPOXC'WmPd;DAP`C3biE++BMu@,.KB5-M]1eGUo5T<1Z`hCLC@abH%b&HKB>N?8Gmhd5:''=`Dl,;S=bJp8&:^kh
%V$>TB%Z*>f,V1[M+A<INCZ#ef!ZI<(HaSIO@L/GloC*CD8CmMjDZ="$ckG)40KI&#LsJDQdFka6S`<D\lT[jH$Q7$@#XA+pQ$3sD
%ZKhk:gQMB$CVHE2:aYl8BCAQ<G0>p+cr[4*SXe-n)gGoFa8cI"-QX2;a##'O8d$F)rU5anF+;`=+i%mXJ/StIk1Te2G-dWR+sSd-
%S=:iK\lp+/C\imk?ITth(jA-8?V&C1S9h="fAuq;Qis`iTakm[--Ig96he)N_)t#LV2IL%8/X)@a7QGW+1Z.7M$a.kan.BK-iSM\
%q[T8E*UQo<n^!0[j6q1S-Q#RlrY%rn)\9HNS_dPpkTFCDbYMqSmiB3/S!fcS"[9>Bp6@1E-]$VfjH68j)OcPX+m^^=]3l>L0T93E
%@k`dGQ<gn@IuI2mgSpK/?FrU80&'Dm#c`Y1CcC$WT^Ftm[CP+-rtqZ7gUt%R1ZsFY$1B`=IbK")bZ*G[Bie@Ym*`+gZ/"K"M(J:a
%l>>le_sn=2ZX:U$X2h&b`nS1UZl-n6N:7d-!2$@f5-*kQBh-%\<4X!f,L1qe`lIfKG2epH\!#,J^G)*4&$NIC9bdth/2EhDZ3+B;
%`1TLiaI"qI!kafYQVQe/r5.=)?pA6fjjYG]OO$30L#s97;eTuKpZpF*Uui1JF1'c?^3j!PF^AE+p,C:qdc4t@^=s=F)RRO<-!qZ2
%!(;&&$Y+.Rrl1u7[eGVW_#MM+.QLXUFS1j9QY@CR]F(pm>,5BM$7Shs16\s&%?5D\!X(c>ZiXkfq7DUe:&dUD-p<M),.RBR>7tRK
%B2Oi,VLF.[aD!1lU@Ck3_bjLYf6;mR_k$sh0K!u6LO11KDss=!&Iu!6BN(EMl9YZ^X]p`+UFKitfHo[^[?+/e(!I@1')Eo+MUEmu
%&j?@5U4uCY>(3":[GdTt%7%oX'Fuk02a1_p3WHrK//jKqD!E2DeMR'0SS@bi0M.,"L7lY\_QNMYURbG\Ma`GA7Q[_A'?Y>#4`P!S
%4nt2m@+<b*$;l?b_RiXe7*Eh0O$M.Mq:?II+j0.1/q,."M&mr$66d_riBED%H`;PDI^0?YL][b!S)$(;?__V27&Z+7O_J1m@hK?7
%Z0aB61mZXD9HG:o@HE0PW4V^o<oFOSrk=e#G!auqC+tZreMB0Z:oIH%CCFT/E5\kA+l6U[1KEZoa/@dRO4EiMne_X%^mWR$-^TG5
%6*u^EG"N4M[6]hGB1p\t2\1k5DZ'/IqrUsSO3AVB#[fUe.I>=1S8caLfVGta<fcD/i+9X@))Wg4_7+F/n5A'tF\-t$;g>VtBnCtc
%IV=4+*V73HH=iFu=)eJ$:i0_DRmso=?ZQ?B'$st/.oLsL"IpR3LZY\u0c8f.8Q753kFUe_r"NT\SfsE?QXC08Bdm:OKRnBj*Q<]0
%5aBbkf*eif<Lnc6pGG,BTaouIisEt4*?qr1(Gk6tc`VY#chjkKo3&oDAuo/k[J65H6LHMh_7)S6UZ6us,>(].P\+dEeZo?69'q]_
%1W*JE<RITKJ8Hq'i`i4Yai(@G*GmI)R-0u"3>j,T70lbrTX&k%B])0Xau1[c^ue$>FHK;QO'Smk2`'hWls8R7N?aBVRY!Bi(<0NB
%7d/i"f5C!1Xcdnl!a!RDp.<!$BJ@d8Wt7NeS>l[92.EJY!.g=&`Gc&#HSQ_m/ohub:K$FQSN`=;JX+4r`gMXQ?[Q;D0RdG8^Ld?:
%_>"RYOK'7[@.8.5'_YVUPe?h+=GUl8mQRI5gnFh(,hmd(!N`]Pk2#Dth=X4/7?&NgL_*u5N&TMa_:H))?n%_*QqtXRF%E-,`\I=G
%/$j^<*:h\)FO="+&u/3GAOF=%S]#&1RW/_>$Rc@+l=c64EQ7q]R2]\-,o_`N0RJ&l[/20!i4.].g].`</&!XmD)#3b/E^IiPb?<4
%_/d2LVSoXu5^-IL-$C-+g04!cUk9/lBkO1@,U$(WG4cFl8;.P;_)gfX5&&en^E;*CYHY2O+&gt)1fea,@bU<bQEbm4OYm6t`75b>
%Z5Bl(7h5ES=o,8(47I))l9nEq8)J?.HS6O;VO6SPVb7M%[e_.m3_))rpNMTtkpjk9fWRpre'cJ(<I&A2f2g8A^hu%4$9IJ\J::V-
%LdZ'Y1Z]o1-'G@KeEA3ip`\HGIMiuljMZk9j/>d3=K-QAng2+Y6NX`69lCZApUGI>1tJ%b'F"LT,?RO)aLC8HjdOP.[IKk.6__ct
%J8CdNR&3;<8j_!E0ZK+s;\n\4Xjm:-.sJ'"hTHq&[-P*oHY]V^W&F5noHIU%UY=3WgWaibkqTSoiBqKgM"Q,8a&Gk@5H.A(I;qeU
%UT$c<$ta"sA1FcRR-[<='bS!qeC?\nRTIs_I1IlfI!t1'g6W^jBj<BEM5u*=,/3G%:'Pns`#l;?f-hqtBNp",Yl3B(#64)e`hDG;
%Ca:?!eXHcTD:F,,p3sijP-%tG#5n@`m.]A2;+T!8#aD'MFI7^(7gX/aYK\@9kSJ7C<iP/;H6TcG:4>Lees,PkP4-D18aRhsWa%!h
%::mqQ5pqBLXFs.t);MaH6]GKOJ=Y/?Uh3?#09D#f4m5.V^omB*Gst7CO<S?[egS%P5r#&Z9]Ehi3,V?8?DCK&6UcP%7U&-7;jEi=
%oPFrRUXM`?Y\\iU&g<3s/]8kTPnqb`eC8;P<`tJ[_jMnU&2K&$.<LJspZ<'B>-l1)dTbSG1sU!&%Sd?aa.U,d#U)5Pg4;n2,.%=V
%EW*].#giR`]X&:<5n6*K<*91a<q"T^BR'OE6Y&YYAfF.J7@s&jd\3@sdP<Sh$fI-?,a"Gp.\j]FJEu))!-gV<lajRiPlbIKf"XLK
%?-Q\SgTZfob8`pSpZVnh1a[[S`3<IJUbqM(-\bp/U)PBdW?P6F]NuN`ACQSFiBcK;1;jI9,uWCO+9Ikuk]seOoe0A9$!N=Og/kb<
%)7&Kc:fZL>^t7Z$2H%\iX\02YT/^At0H]AViVQ%c!hm!tCsOq:S52270bT]RH<a;XbSM$p/WG=\*bBS:Jm"q\^6kYD6]De*m-Ar.
%6@#r7QieGi<B*/KBZ)`k$dtB0U]l[A)`2rNIZYi>+A:;D5p(F`(rAaY-!0sjS#Bjb1+F!Qm=#JnRV2&7,_Uuk=7mX,.^*DJTh];%
%!@1fdJ@"E5"OI'4q5;M+$#K*qrp(@l/@QMbrCHaCRJmi)WtsVYl(LQLG%r'=U>6UTCM`r/*69SHQq3ri305W?3Z(l-Eer2FH@YmM
%g+Llc,(R7)=rSU8[(f(\BanS*4sNNkX:Xb[l"2%B?u4iZS/-nXbldTFE!a6)rIT`8c@63bLqhl7oNQn!%c0$er,3GdiQC:PLj3Tu
%XECdQpXo2L5n0T2)3!ll(f^WZ8paR?VUT9kO[f7C4SMkAoF$o)mf#]%r@o)2)F.i\dW9U[*D>]j.X7A!CJb^-Y&/0dO90lR$73,+
%\soa4*cDa+6OL8j!(EQQa@oT<N)JAZj@tdtWi!e[@qWC`%oa`!CtsdNB*X`(`[g;P%>eLD`9>h,<:_"^JL<?6C)fH.d._5.VJS<%
%\;>*lq$bHWO2m(g`VpZFkHGZ6(M1sj?]?1p)]CUn6%s*SD4Bl268bnra&Or>!\/f-Q$@BhGBT7HY='uZM,)PJI%5a3rQ8,UkiiT9
%p-NaE#CqTI3]-<+o`SX+Gn4Ga4^eSQY$jFOnJDO676`OB9463,fG::h?B?iaE#8r8#Tb5R[RPcM+hM&pQUcJgdHGb_pqL>q,mj[m
%Z/%d>6$#L8[*udu_^5S$g)<Q,?n4$`'0,\_JZ$2^(\fn&QI+44A-HHsd%ERoIJ((\AHYfE?89-\XWO<Y>2VCdh?>,]N=s`-*Xf^:
%5)`hd#0GiIX<BV.3&sK16HV0-*1p6sTUR2ZXIaBOaR^O`N,EF//%qI,a]"OXHa7i%qN31q?N-[dA^"]64KP\>_JA*W^!9$:H$Sor
%'#^p>@]mYKW&]^R7'\3Ocs4WH]*gHk-*#c'92puSm1Sh>I<IK(#C8bIDJ/%a5ACR];/6lp=sDbc3o&J=E&mOG%j^m$+_$/-=Sl#2
%Q]S4F,RS^Z`]bX6_EAo!3]c=KM5G-8)01`J39?M'h9f?:QTJ11bBkk/2<(mI/N?Qo3Yik<GpH%/B6(/jYAAJ8<:fIEUP@r(M.Qr5
%6;,jA&:'L"R+@`G98sY<ViWUgHJ'4ed#PgB*eUnVSk;$aRpe"XR(O;+-Y_=nDWj/I.j2#]45JL*+m</`oNSaW(9&AdZ`P_?YE.:b
%_1`icOE],=FSg"8_YjYQUgGS^^q%nY]te9!1S<30RL)I;m1O>;)uD(?[i29T^c4blVTcnPL2o\/V2%U.):aR24%=V*,o1I.Q:W@$
%#jS?#O=ao]8s(^^!Vg;\1+>EK?lW$6-eh0Tnb6<]1Q4k!HjCtBb3t^9P>!&g+DO58_o_a^8VGJBnBjWjML2:412"m.;?0]"6e"MT
%Hre`1E:GR"\h<'8r`tI&(`Fg3lqhj:D*O_n]8-L%L-.#Uq+_E)UkhLf;i4J)K`27"a]>UuL8aW.>!(N"<;T6m"otqq^R&8nGjl1&
%BqPP(:42lY7XQSoNKci)#/oIFPM<c'?VG>^Z2^&m*U/f>Cb]opYhrVim:UgH)ZU,TN93-UYuqRLIHV2ASlmmO[GoBL:4!o0/#E^?
%YW=odKlR125Z!g60n=CRbC(Zm]%Y0"/Un31i(GVl0;mah3-nu.1Gqgt&XL2Vd-?^Q4fY2_hp3EJF+D\NN*t-KlC.Ie#fP&_HAP#L
%CHR1pH:SF`gL#OC7#.b6P<B4L]$KOmrp_&Jg&*JN8ZH[boLE_1`CE&frJ2V>]HeI.Z_4I.9b"0#?lfKQ?2)]4#$P^p)P9ke3NM*^
%HJ)W%?"?8^P7<IhOd2Vb".R%S21DuPC]U3eT#!1&Tbm&bP/>*!eXJu"GR2Q!UDd?PF"]Vn7o.9(BsX3.+7GZBi:\q=+5MtU2n+\%
%hC5%/c^j&u1.U(."$6D7?mZ7#PF.,MCQ.X/8fHPBc#]n1Z72h#j1*9>BFBiY;;5uCJt)hYEd&Ln@:`*Cd5s))V'L8/oXl>,7?I![
%Z'/[Q2te$:I7%X5F8I96*Ef"]f!qCI4Ef!Ycsgk8dt,ORUS,YP"FlVsjC*$:#F`MA(dHjYlteqO4bte#<seuj-FF3qq)D#,YkJkL
%)1(<Y-jZ^CL[$XqLUTi%:tN92!O_a&2>HL42LW,C\LtWXVH`9W@Fmq)Q,iM2!#rQB\p?#C_\d!/BDebg.oeK>VH\t"UgZVRXEC2J
%d@[:L^+3&(=W2ZmlEN,QhTPmXVRpjUE#"]492+p7Q[[r*34p!"68C7#24S"?MWJ]S)d61&h"hG&@eJ`*mu4oQERAiROmkt/:fW13
%94s5'*-`*s_Hh[)MZ[m_L%u!SA9lg";Inkp4j<2H\C1.iHM-*SF2G&iN).j%na:4q>F-@RX(0L`'%8d$K(Nrc)qEtm1[;Sk!/+TU
%Fs8k%mt8CC]XO/J8SG2S<t:;s7JD-Tf_i@KpT-0TIT!So@kCC1g4]gC(.1G8b.%)YRi\KBm[1EWB,I69[e44dB]I%gLPco7S#J@'
%fTb/#NNL;^`P8AaiXnomcKWqqNQbO^gEhKXM:c`16MqPPdrXc^3auKsI;>hL187Gi(DXs(HsaLn(*`f)ZJma-*]o'Y(9a35r#*Z%
%Vi>PMT<7tW5+gP9VccMa05\i>iaaIBJF#($k*25A,8`'kc-H!k:ecKSEc/RHXbLW9>u-QaO.L]Al`i"c-&r]pmr_P!3NYWeLDk66
%-j"pg-Q77U\>jdeHF9*4]=c<Z6=$CW>eo%J-tg64_J[/'r@UPl1/DIoGn[>gJmCYNfR*m$1-C?WVo+%\KSV]ep`(kmNj]qSi-=rS
%"<8ESW/dJ8E]<;bO=jM);TcUNN9DW9Nn>;H(,A'S:I!^1j%SkmaK:Qcf\/kVT8O#cM9!fMCP!DMK6ON67f<D;_Om8qpZsk#68A$i
%Ng/H".E@R]AZdtjT40fSC%8EK8%UZf:=BYD`CDIced^Z=rEToMs-A_,aDHRt#EEJepmb=6Xln`FLhM[o]Rh_81+Po4GuuF(2YV0c
%*#hj>ZE%1IqESeJJ8m90"f!KFA6da/ais\F#h&$jh,6KlFoq@F\&u4_/TH,JYHU=,cd3NoP\XiT)233&oFY#cP4[R-`X]Bm5(mLK
%a`eUIFS5JF2X8;];pU=\V$.)YpCqc3+K1Fr$&rKIL9X@YJg66?7q9Ol7j.=ImJY''WeAVuZgnmCoO%D81H+=5&trV57/>i:LKK*J
%V,:P[^?$mfhid@]qX.jSr:%reKEW:_X!F./aEL6oA!U7T'O!e[o._+Onj44`&X.LiBVatm?0*BR:T8['6aeuFa4a[5UiRuB1u]h4
%ID&Yh<l'e#m2pE>$K_YJ`]CAS%(2V%SgjCJmRb3,m/F8:+>/h*PT>6lWp`S!WS##-%]6):5"?HfL,fJmXHqUm-Mjq?MLW%g$^kbY
%US7i4P<J#Akc23ohS?_j"r%Xlh#q'8?pGp;hJ$B)n-sFR>m:%XS,G+_kp\RFrdsGU5j=D_l5RRo<J`Ct:TM$lR1(J%(t-m</KL^l
%@X8`34,EeL@CY$\iIr'k7BW?r'nNak,qtWb7M^"i(c![>ob`UcIg#-T'-VD_-?;FHYhn6nm0Xk6TVAo>AcBFi.1.1ceo>]hg`m7)
%^)+DCb,T*A+rEdq&ZY7Q%7jusDilV"JoSdcnGjdD2-+q:K]Za_WS2hbm*)n]Pc0?(Zf]4VcG1bJh\07Nr;2Y=MGoHai>klt+CL_R
%X5WMt!N&Q:r6*r-GHo#'&7Fm'Ir<u,\An([8#cE5<hT'Q=\tsFQ*>"*/WJ;OqUM6D)Ojq4ALG!Dp)`]V^-_q$.%n/O^I"VY6'#W6
%Z^dZMm.`DS^YcKG&hf3$!;bXL[\).e4n575k3V:h57OWdh(i1$\4q@OWh`5<ZCr9<X9k>FO77.Di6'Tn\Li'GaqWXc"XA.L6Zhf$
%8m\R9X$Ao!m8b)#ETOT![%`H-8R7&8f%r!Pr]N<7l!q<2i.Npl4f5IG<ad^<7#iJmh9WfEb"uW_X<e>u7V/A&U7^5Cp,)-o=a+"h
%l9POX7d,tD8Ti&Rm24&pbZ(<B.:ih4_7>._g:7h5>L5L!@=U.77e+LN>7-<6:$nAaZ?4e>dE*o73bVI[I;e(WS+aU?_g`Fpcak:.
%h;`B-daoK0MRFil`\436&b008$m:C/L8`=sHCnHS1``E\[Vn#Z_P6=V4taMKfW*ZD*ojO;(5np<(p!pq-;(")/e][!Y;3mX";.ff
%L/\1sHd7n)50CHV$D;*(9-%D$+Dkg4L5CoOo,WfU,64Zo/dI?>:6kUp%#.h#<HB6L]Toj$?-<=l\$sc!>ZgF:UrkZ>[#D/r8/k(U
%68OB&m07-\(*ZB7767=?C-%f;Gs$'T1$(Cs$@.teXa4%]'Nj6/7WitF_+sKLN,3m@aV[3>1DRSd@\^m_8;EbU',21k>;i,=mB3s"
%+?hng4b52$#uE6!\Q++X_FD8YO&nDr1#R96&^RpgqYMF=?L`4NC^FJC`oC)#qo/t4<d#'$dlh;;Nt:%38T^&=9;rLU%PMu=nCU6V
%.2ns@Dr"+'-:,J#<XsZ+0=]0CINguO!D1C8A^PIE-mWCEieWgM1cn)rK_;;C&G]00q'j]I`r0Zo02kD$[u_1U'PVcl$'JT9J-_6g
%/'lC5i?X.g7.sXI&H03p?=1A=kW-NP[R+rVf,13^<sF"M&V874L1ggBCl/hff=T\MH3ZiLeJM@C[FH2^P0KfjVjf8DaQCBb)0gQV
%Aj5GaT-FC8@NRjXo?X/BfSf7q-_RB..O)aqRgMreU*iLo%ajC.ik7kB_]dLT1",Ls&-S:@7aRc6`;&O.&j_,:kqr?D$cu4ij_G+^
%5@"+4FGE?<f:7CWOE`*KPC7@rq7WfqGONnXW,?5:#S8H57_\6!e+e11hVFf&-&b3.M9%@uR!UL\5ZY<>Ce[dlSBEKmD76Y"Y-.?5
%_Ojrc.#P1Q"Z-"skl)pIr\)=DIW]="p)*-6o'#Fs&C^1s-EkSe&e&gA,Df>,h##)ZX'HoSe&//cUE!"lYOTtj@#'q+O"]p9/pWDA
%WM'TFPt9b/O%G?SLbd2@=sC6e2g2]l7Ej-le:Tj>lMD-)W;V*V.lc7@'b3>-3MQ9tlIkWm1].NJr4;EP<ffSAm"!$9m5s$G6;gWL
%VNs2i<lqmqV7@%<oc*HLR$ckRjVB^L[(')#@\2uGQphjM5jeg7W<q<D;32"oWo"S/#4,(IMW#3"66smQ!(<s8GL[1oK#kkf?bD"H
%)QPcN4_B.1lbK(5oR3kk6c^Y1h(&Wp)LVW7O1HbA4YH#a'K&oHI<W*lE_i]0+.kRH);Rd,*=:3NreH`(27mC20_L7eE^l,5</2^c
%BDXS<iP8/e*(^"<b=WRp<Zl"h`fBB909MJgX,RWVdTrl`l.::R5J=9#)11E3a'%qn^P0OB3"g=n;,Yck_8hI23Tf"[T>eckIm^99
%<[Zgq<(_Cof0#Z1BYHaC[C)7_Hff=1?L21W9m#$Kd=DmgfCDW@;B%!^U`*XG6?,4?_9<eC3*:NpAu>c[84Us[h$tj`Khq?.%H-A8
%&5OQTJ:c#D=O$%aU5_kb.2^HnXqAY^EV@l#H2#r&'S_V!5"7Hb<rWjA<>kl(X!KC,Cjh)YMKJ(g>%M-FY:dp>)@6f!#h2>>feqj2
%`l^%Qi3?bHQY0?YLo7JZh+2>m[X`E`gKg=d0Q,"QoBWXIrYSLQA@<GLGO<IP%32jQW]7e/ZUa*c:nKC`j8]G/%l"n8Np'(V;*]V)
%d>BufqVPZ`C.'R]72g$%&L?to5I3T),>98U\U7N@JJ#Vtg6/nVZY3=s(6/U0Tfp86?u(ZfImNTf!1(:b.BH/N$[sUp'>$<=49A^J
%Ne:q@qs,\#32i<KLbXe0`:.\PJ\][X<GcZC*"E'Sf9Tr9*s1ngS`k(K;+K=BTX8$Q\?3jc_a%7JAUbrhe0!_j?@-3ndb175Wi\o!
%.Hrb_h7;$tYocoQAeHSj'$#u9!)@`-+A%\T(fuel0T=P\5h;K^Nes8!#X,GmJ1'SSK!N?&;'VO13C8W>[?oc[TJj:gQedg^`%ALn
%*o5_+WtIA7\;:G*X0j#,W^kWWDLQ0L:Gcje#V-,6j[s^"##3s)pNZMY]9>N7/s40%prq+[%S.**<:"1G/1HDVU[2$hU5Bp>hegd5
%!!M$=nd^4m9T[g#!3@u\M@UqCQoLG_DlK]]1(aNZ>(?\Z)e6gqmn1+XTTA')kR!l.bV/4lOL`tO2]o0Ipl5c#4MU0gnQ@R%o1Rgl
%M.?],"EUOTon"-fj-:RbK9oAC7iJ?P`g\"']rSM%*,c,bUiCW+Z%G5W2BmMD0n;,!E9#Y;aGm-G;)HDBaDIJ$[uC1R%$F)t40O*f
%Xr0dj1W]7ilord,hP'*r8C&]LKWP_(YJ.n#$00ES;$b%Q*5p;'pJo-n[&_k]`hj7QH'Q'ZS7hX0(j>!Hb>Ni8$N:lcK9hr6S\?nC
%d.*+'Ge5Nilg:;7nTLH4*(#eAenb,cSfeHgLVN;u$phaCGp=peIb,!*4Thm*O8*>"6Q01NBb!VV;*FZ6W"ecM*bX)>(D;#@-E&&I
%%$J/&1uQQ[eLHg<dG3V,e1,L00H$Sd:_\htB:#PJ=h]9F5t']P9dEJ;WiCEO^S0`hkbHB?VR_6%A;1"uX?D*U/63-s2@@q.0qV:X
%YmCn#jQ:7X=edT6eK)h[+E9l""cMo2oJh$CF<9re)!90>\6Q>&o@PWr8VrHe-DQoNljM&MDiIs,*;Cg2!4[)'?UE`n&'mN:$43T,
%Pjb2`BQ+u44cJf@WlL,A@4AGjVp#C.MQ'Em[=>DDAbeTh/:=$MCFc[e4@IX[8#:+H,tQeq=)+]udT`r9F,IM:)R]KkA&kZDc$i1H
%A\E)MD@p13OtVuK*gsJ[#+T(kbclqm"]I]U6]?<IR/hF0?Ge;?>jb@$o+BCS_BX$NeXrtbJaTl)lo$k[m$kDEZ4p8nhH$,VFEdRX
%gLB?5c9qN6@fU)Y*=.G0:erb\_-Cs#-H")9U@R%&UQC(dXQe1sAFuku\qL/AZDRTUpE9Z`41q&XLM56F"tKns7HDMnXTlGcSt=_p
%,lc**csX:SdEr6Z=0*'-/i#KQ2>u4j8h(kN54mFb>`kTI=Z`LCf">M1+8kl"@Kr%Ja\&L0$/RT@YL*#pG.Aqj6)o[Bgh)DhT;&*b
%Vs[\U&XP>1SO=.8$piKibD<0(7PTbj6>44[+EImS:kZ)EP)VI*;+*+J@@I:WU`e1nWu(m>qsAL$&2QK;-tB5&m5>e[f+]Zj8/a9&
%HfB`&ql5Tm(q($1:ZKP.:YK4CiJbGVnR.XE2@&/^6KC[W$5s5:J?c%TNI,5'b[E!jlITsu8@=1I;"iE`VIcJgOg_/JRK`kld<[4r
%^as@*)Hj(L<:3dC?7?@0MG%+aJHJj@5U$&?nS:jQ6hDr;5l0eSs+bN]ki^IBQ^/"=GG*%f+o:mg!`0+e[Bd9dq\Y82/GD)N_nZd@
%FL1b$T?;,gGRt5:[3K>4(O-\C1Y%UDU:m7SgS:QJ?2?*>KfU9%6V*[okRq75'ZF,JfF=j>Fd[H+<I5S!a3q?p4l/KdHn0BFqpB7l
%'#l1=f]<?(6u?$"Fu/H,81!*=/fb[Y"Go7t`>1Bjm<%_Lj%Zeq4.qIn&i6P!^itTfq/_FGK.&hsJfq=]k4d>)f"8!21=dHZSu&Ub
%HU^'>'t<R8$+u0WSL&aY*Ss%'dY;.@s7XbB5/`%@(/+(P0OE:_9B_9u7OLukVT<E6E4gpp?^[.eE>$/_?5L0T1_)j#g#P'Z`s=j'
%IoIlJcX!6(m=EI%,UoFc_<J'@VG*`2\!.$T,DX_E<4uRU9U.2]e6=8_86%R,SQ.kVP::@let'rMB1/p=X(Ue_CteS(%XV&HdBO#[
%O=S+'4t_$uaj9_&^N?X)U`W^m;O\I)Cs7keS#7JEd,17o"86*qbiic%VB)5uk[C&lB((hV^+1U`DY,HphFO)*HoJ*g1er4&T`p!L
%AH15ZdBCol,nKaIee_#60m-k]e!q&T_m!I..n+FZIS,I=eBBqe7F?V_b*5%g+2u`n_53QNT=L@fKQH$C!1$%PLl]hEJYP8'f`bO?
%?.De9B!WEt0QC7PIuMF5!k\Ze&nJZ0W9?)@?J2SNeA.EO+o^0'd!nM-f(3lNOV-8iZI*,2,W>hm@_uP,`n]#Z'@-1dh4CQLTTcl-
%X+D"G!8n85j$#(Y5"<Gd*og;p;+B\D@B0^YJQ\'EFO..V)h=r8cqJo>8;jTZKFBZi3-%Gc510()rQ[tu/c"`kHQ"3G%C_ukn>;`r
%kHNDio<=]`$G/fOMtaF]18FB@(]e]LOS[dcbn#j^@0I.ASf/O#'@2#m3P`)%)!.i7?$S"\-5]<p-Y"0GggZF"R>](U;F^F9mYjpk
%!YO#8H*MZ7@8mDs7O_\_dOXJS/PCuP3+?JndXHp8HCOp*,:RRM#q!nW`e4i";^$V-'9^-n9KXV[31koU4&'C.]9^D]S_S.dV?gp'
%$un<'eagZ93a/N`q0gOdq&A;rX0KR#4)E&:p-e+L&!u9;j$!<`$QLAd)nc0R=i/>.\V#:J;E'O26bQPp2@7fJ=o`A(MAs(/Gg7EX
%15Z[%9O<$qAiGVB`1`rej[`9dga1iL4!p(%I'AZK^D`H<%PMC[@AGlI2WB'nf-VnL*D\PFKSkhf;[VMS/"CT+DL"cX[8Pu'<856/
%6?_bO[81'lAqnG=gpNWs1LrbK?\A8&D-68K[Hau]mnU'K3%>h^pLuJTi2QQ+O'pN]br[XBn3HA%]k\<-n9(#%L63sjSdIp,NOiAZ
%Zbhi""]jNuBg)kV,s+Qee&K",eMGuX6U;eg`c7`(LTOT`drYDRXS(EEdEa1t].3do);.A,f4N'dNd3k-[?^oR.gKiZLDGm/K-BkL
%8*spmgbgi80[hZgpp6:U`-*6B/2Ims*O?$_\HF8%[nudUdYbLlB-IMHRrX,e4g.WT:d-$eV::Tk3$4a;7He`%d'#RB&k<XOgjsZr
%]]\;aSh@G)/fR6g=D8Vs[#":p&*!oQcL)+E*n\HJ+<QELbIt<7A8%u0S`IZo$i''^M`glRgqsdlW::8iE9gC5Em6dh(jn);fhd`.
%=_,]YE6JZK'%r$H%)Gma3?PULS/&jP5rB(&QjIn["aJfW2W7e\blO7e_hbjVUiD2,RCf8uVnI$1Hsp+NllflkE\p9kJ;qb:s"RQ]
%0bGe_4G)eo2!jIe*:MmabBkq+Jo9p:^b;G.Dd6GF@V1G@"q;tojAA&<bbX?*l\X?Yo1g$Ccq%$>a12*8(%Ii2:?"P\*&Y,Y#-'5)
%0=0G="g\qWmWMkA9<@A640hG\pJBUY86Y<%cu%1CVmLU@]FRTWa35I*(QO^1NG3HgJceS[*qN*BO\?cj%AaYkHJ;T.AdAauM750:
%;l*?uK@T+(7J,PpI$i63I,aF^Df?iB1h1]+5gF9*@]oTPi=XKk12q!UEMp@iLl(LrD36e#8L)Qr5I4rE=iG,)%Xjti,V(LMgFnNn
%DL0jW#)//Umj00A?X]>[oa=Z'+O"/<Z]l."_R-#!+n,\S1i;tS))9_QfJ2k45t`P^4k)ca2V+Aa:0W:W;P.b1Qqkms3V"m_6(F20
%;&p*F(]r[4"W0Ou5KXTm>*XKe?\%+=HjPII+-j9-A7iW^ON.>.f":?8`X;a+.YX#+"\tZW!E`=K";Z1dB5*7b55!#*9E>*$l.BdG
%;c#2VLuAhaMsIL**L,RM&hp,&'oMKRqV>5L(<D97BjNN;.:ifeb&65!;]1u7!BU(mWLOnR%4,*L,U(0c"u*58)"3O/p>*)`)iTD9
%#S\N<*ZIWi_C<;k4(MdQ=D(A>Tj<Z@hBr5V=fW%l"E=RN0TZTK]G'ScmTEKuL9Uo!*7j)YR\LSrU(Kn7Jp[Rb;esTl6H*V)G;[^1
%&l6sgi=N>+#e((Mnd&.o$BVDf`sjrB9!@&&$T`B;GXNJU\6Q8m55(E#4kWc^YNb35/[=LH,GKC-n+4MJo0kioNG5<dp0^9Gc(:?h
%lSSfKE<l5l1l)hk-CG89Q%>tW6'Cdi7u3IAZR'=7J20YmaEVJ'#%U?6M/S-"KWj/>JIn]FnhG_Ad4odUEcNi@R)iCZa<C90"<S[R
%d,.9kQ>O-&f+3FVhG&6>G*GS<m:P8!A0kie!+R]!Z,pCW?sAt"(r_=f8.`ZY'oXs#A/X?:_nK.-,'aV"9MdVsd5MEcXH%4m`W")p
%NQ3;^QXZ6Tl8LirFlM38k/aEN%B\R9ZECN&DTC:VAr1eC`^@fla.BnBU#C?ogk80'<E?bS,9UZiqNad#RPIcO"eUr?_]E4MG\'^c
%kN`%(oo1^NO'?!Bo9Soqeu"$nU?^+WX/VQb866RV8jK?q!k$V5a2YE_1Qus_l/SGE%&12VN>X"0LZBfV4X9[%IYP\i65kn3'DDRQ
%nhMb_""^GO\@[*@YIjZ1Rg$J[4brrFgje-P,69'V60G((`fRkk"94C8ep4OlaOi,WG,;2kanI:u'#f27MJt6G7qA)]%3b,L,DfAt
%"UI2m)9jltNab@t%C#"6`oWH'FX&Kq9'JL2Tn$lebH=HS-X4T21@."JWf9&6O[UI&DgDa;@0,#-jMaoVmtp8JZ$prI342.U<;@uO
%$T=EN;?uD^@*P8V=hu$90=<kS8)NM,kL3S;&XnWKHEuV/5_=t#A1.?TYjI:1oCo#?-bE+tZ)i1u4e$q[E2d*bQ\AC@NZ_;U?K.p/
%Y^rFQp:*!-iP@KCo@mj)=.b8DTfTVO+@?G['nsbaT\>EI[X3aQFnj.)ch(0\inG[dUN(K-Z"r2.:PAOg8tK72D]<u=j[(=3>$JER
%,WYVP.Y\u1Z:gDi7IiRNZCLmP6AR9gS,N^JXLr2K$)N6ad[=\$9t_7Ocis4-lR@DfTb6orV`UA6L+mqU0!Rh-#Qf0YX)KZs`pT+4
%K5h((i9W)NJ`$E8W"u?1:@8=X9_od!'LZ`t\ZoD[@59Xb%Amm)\rnmDB/7d8KO]TJA*g;a]TW@KZ#<<`"o+0ooO@AsR7?^'0(&G?
%A7c%EH!QkVB;Y`!,aX?Go^&O5/3h<N3/`+W5)A!LB8tO]0bM',o>iTl1QM#"coFRhifK,+2&_`QS=C:&demS):is[#I`)uX8O:KL
%k[97-.+h!,Z"^hqLD.A'O/]Y5f0##r`_7YS+H/FUlokiGLb?b_`([&pZ\7L(->QYtgi#tVFO=lWrK3cm<8e3lB;Dl`VECfOd]VB*
%^15I'bE3:(Ht7%CG=5QNe!;)Bh'Xt8%?DD'^2em<$:VORNm2h=RW6?m@A@,Z@g7k(#Ekc2;7QgiXn`m?I>]l_O'Cp,ZdP6:']fJX
%dREHp`p'NpK0IILD`Yj@DP$gZ7U=$EIN`a[gL@UV[&-GU@hH9NGh(a7OC/Gm`o7"lCPj42:TgbY!lk4c%j,3JcK-eY/a64adND[-
%@hj!Y+OX*]s6M+$]D1`o;6NE0_Gm1(m2)prED<]pA&*[G9PTLs-Z8P3%$Y1)DFl%"6?fGL<g]U4VB]DW.GQ#.S>02J@jg>t)W]d1
%=sMLT,2q#G\WEi4ABSAlL/kF&'@Vs%&bDN#"Lg!!N+`""(d0V!-0XJGDbp:VpmVu[>`Ol73MEl@o%tCba'fi7#o05i5po9t"h'`(
%RSk+G01[T"Xr.6ZU,]8d!_hUF&XTKH6aV5ACI\[HH+';'+"]Rc&D<tQ0X#P9J>>j<E(*V0;R3)OT9$e^D.d5#4P35KZ(7R]_CKNk
%obU_-3>r%$,1Bl[M1WfWG;j*(T(QDHh[*aJ;GXSCjWI!5AB7.Gd#jOGTljF_(:$E]__]7H<A.tPPY54s`)kZ.>:=*5]$hoR+r1_n
%`-c(9V+L4of.J)3mAsuZSP-=(::fVp+j$TN*+\XFRG!B,e6G9(cO04SQ0WENZpD5-/%W.+`*$RS^H9VNoKbjUb\?sFClbg<kuU/Y
%7VW-!9t,G-,Z\fY<%-%6&r3Y&5SHen5^jmJ."UMI)P!`<?#fVSTiN6jiOOMp7N-L-&oBHm6t841Un`S(Gsi`2V^[^p`a)/O%.P63
%SLOWCN!_QUjUT9)%&6^^^fJ_mr';$B&%lQlJdKG6$rjJiK@8GjXi."fq>*lt\\IgMI;+NSA7-<$TC=\%#$s`RcPJ@fmXD5rf8h@A
%>7)XD92;)ea)kh]"DT1($ruhB/BpTGQG@]W<D2g!AA+uUrt'%8jSSq.A&^??aP.fU5klsU%o:L^Wb&\(9as0S$=oN\bM:O$1/=Rm
%_X];'pV9C_CBrC_'s&%2ikYn'%:'D2Q]RC8#FcYeK^`lr4NiVb&.0e6Vo<b42PhZPD:mX?)EZLLjO1r%NXVR8)\_:,V-4d=LiknN
%EE!3%1D`oo!tu`VF%iu`D)GC*K>a@=:?U;pbX3X-\f3IYW$mJD&fb8:l0V4u1boT.9H+$jE2[7I(21jIAF4J=WBfL6(46JHYt9l.
%==\4J;f]aNCVD`iF=^=-dg9*$:agcLo&cn^m=q6"r&NH!ccRX:9kWOGCHW<k$se[L\a7b4&LWPY,g^?!qAtD;p]cQ5asSTlEfian
%B?M]8'Z'srUnu!a/[`kSQ6,k!^oieO%Y]P8X?ELk7D2qT=;NSYOaa>*Z'TicoXs!k)K,_Wfjb(?4pgPHUNO=XNAs>I37lq^dR)7r
%`,OJEA)/RZ&4!>/Z?pDD+d*KanF&W#W?dG%,]&Ke:KUQ3[QcQE,$>kO*`,(&D*Ye7/V4X[3W"]Gj]Ml8/.ich\BM;^=F>+Xf+*NP
%S0pXWYWq-Q`s\`MRBB\RQL5LK:40U@TKlBg2")?:>-uj.*YI@d&,Ki?[por>D@gk,-Xr,c[Y32]&+J`*VtskXLa<b!9G+N\-p=]V
%6&YF;:DWRaX=b(hU"=7dgr!Sl'g&>^=uNqu*k@`*I0?3f=5*dr<dji-L'jc5%@1pjF<h"dce1MY3Nm+2`6`kK8_0Q$>u@g,i$[^W
%Rd!lU&A$(U!8Wre_?[Z"eTQZ5<Dd*D<558M$E3+32DHVbEhrb<0?*b-#\?K:5ZHVd'Q:(Q;@M_:X2Q!.79)6$TO'`KWpuIm(o&Nc
%e7<&NZLF=5(JYO6%hV$/F(&V2Vi66*Dm\rPJL9u27g8e8e1nFkoG"9\'/o0DC(kA2-7+qYGU0!fZ@\0f5X`e2BFDU"UJJX;=EZ%7
%kfW^Ub@O""g>2%MLMJB]XW)qsVhC+e)iNj%3(IRJdLDi.MkBtCpgTT8@?e5rURF_^b^K]-T65C5mY%P0X\ZNVOCUCe[T\?opq@B3
%aUbtP^F7iqr8h[T6lJEDo=U*@0j,puWbEbf0^?$FRM<VsS:Z.USq=r'Vt8=hF(X<snPu\F>R$Y8/4aAS')#9V3J<8Ve-l[ue=^i+
%<Y)\SDAp!_d4EJW!`pE5BD:^$N@`s"dV\E74&5_5+Xk(uk&.d,H%pE[*J9E894g]O)`-r1m?F!_e#fttoDQ%pQ#1Y:mU`7))SPHY
%#_D'qHD;WjU^o'NZSfGJ;a/f;PY1&!dRr)&VK=0F1q(a>T<HB^@/iq*"%-[7,V)F""a[Q2_1t5Xaa1+L73)b`O#sLT+IPal<nAee
%d<4/qo`6$<f8Y4s'1/6@=m^3j&adet"1;K0#5)Fj+Ae='p3`@iK)+1"*jDd3V)QlRn2+08#Ydaho#l93;upW/iY)SB<XVlbc4BXK
%'nY=^n95BCf(s/r!Q%Yq6?f.>"LK@miu'N$F#Y7S7eT4ahOrgR:Q=8KE8_OLA/!l4M%Zg=_M*(IN[_lo6O]i(joV0<q,B=X"FTFF
%f1U<"h/X,V^oS,^(>Q=ff'nc$iLLSCTsG&</n7+mHdhSS]sT\gG>4df8(&LSMpA=IV(2c7k)d[k'c*5jBtL\Bj'(T+nfht=m$,JY
%a6G[::!i!uapd1^br"@#2[#pIg/d?G:!Z<,RMG8LF>lYpQ[%,\d=<+[T(lK?-FY<,eqGeKCtmU<M.V*M[Mq>K_<AjKePnb3P,&mK
%po5PR@E5h73VKWhg"\J]i"F?An1:5eUt"F-^c!)JMqr&?mppK/dUS2C#mZXsg)Ae^&Up1AW/Y!_]oKY<Ifm//_B-_)dpaOM:^bPQ
%ba*lb$"H'16<tn84I25=KXVXl(Ek4>KAuK"B0##(<ZLA\,f?SehSf-rbbqCU&-4Sb_%B*3pD=RP2,#7](`F+k*[Lo*r1\Bghhqh\
%=EI%f\<f1hH]@V'XS#:mWaeP&^'_'^B=Oh2,]CEH.&Dcj@M%2_eQP#CGd4tR'5+XuEfm[P7$-aN5jtqa*.c%o6ZrOuA[GsZaE/?+
%;7D7!+nPRgZ5h!m_bR=1)qC\fO8GaC(\s.7^@;UBJ<&XZJIXNXDNs+2psN^8/5OJ^=+H:eMm>kd._-]Y#hS:Q@L^kqn&$/6(F+^:
%(38=5/P^lg:7fK_W59cqFW)D\ZI[I3+?:(H&)`9p(rZrh6ZOKbr+btb@Nn:R<5i%)hi&85>(#Vf_'CsOZKaU=$gq$12+d!6,C&:e
%Le8eVD3HN;IOT=W'udk5h%QsR[^lcq\A$^p;)a)+M0#$EB8$3f[jW$(p@O5;\O22DXAOeRfEYJ'g6dcG#]N@Z.$)]SGt`p<XosDR
%6Z3"g7u;'Wl2bd@&3e`OG_#4%:B\gGrKgVm7[r#R0*<*_k*P'&aEI]$i+2J2bSBT::8-Og'@@3I7K8mJ6f+k0Vpi<31HD@LG'/Eo
%ihl+V^6E3d3]ZYr,F@KYH,YDd<i=*@M"bQ4E<E;o[f4)&'1S]*;3*!kE0=!l>]d');Y#R<bnkILJV*>_@&kS`#Fd[QPns\,pb'pf
%_b;)-<X$JD>kh3KDJX#5kjQ@Fb9WrARt^c-Q9rXeXP_bA!5O2X$u-e7,&/;,.;2&[4-tuL5l(/O*"obmk_Jjo$2ib:Lhj!PHD5/W
%D>fW75m0^!FgB8L<3\5^)Gj(96P;q'SSoffDG+NAI.'lWfJbHFe<2Np!'Lt662c`j6b.J_8ZA=?'2GK<IlLC*e+IM)m9g!8#GVu)
%+IQs!R;QH5$$t+$Ei/#$/M/o7+h*fi,]D;aW;VFh1aSMA)6M2/hgsTejB.kOq2H+ia^^_2"?cE)j=pqtok2o@`$EW@oYiXGi$GY5
%qBU!G/26ONNC#8VU,A4nXE&*Kp(LMo*8_2>*/qbdd`lT*Imc`nYl!FC#Uhi1&[+L)Y,JXa\O<89IPS4sG_@;E9kr6^Nf:J!(>/05
%h&G7H.7i0YI:UWH>2'qdVVQo5aAX:\Z6\-/795eH"o6orTgL+<$@Q</Qb1F+TM^IJ>U.u8*P'c^a6@b/&g:ZHC-bBE=qR<9n475o
%U]FZt=jY(?@nA0q3XJYh&qQR/kX;0EbWC%ggCYIT![R`IpC4$XRRHm].u!ae\/TG?eb.@%']'cYe'(4nM-lR)aaYkt5$#C[)lAq,
%C[@k;jm#4%.@ZZL^[,5/;mL[PY`glP"@dJGdE*dqlOjeS5-hUiZ.JEl2uYPuI(On3p6t$)V*urU/J>]/Q\1D[KWjohT./=)1Jb:i
%^IuN&U-r=:ggrto7b0H37\u^!>.S3P*J_#gnB,st[5,id_T4muCHM][GeSuE]/m)Dif3VX,<O':b1HO]WA),C(NcX-DYf^c`%Sim
%MHoq:6>H&fpA-L2-;udl%&F(USD)%ED1j8@Rfkl<9JJ$arpY@@SIs&n#V%(gn(C19LM&Wb)p\#^l3Ee]V2'jo`Ypi_DdlKDeqknY
%=D;bo:dk6+LeF-V7j@CqX1,*F1fi,o)+,p_7u,bjN!K#*P1#F*]r@V8h5``dRXe0td)`(pqY'f(2e!TDG-cL<&$S>9MRbn.D+1L]
%q\@:P\a($-,B0U;'h9iQS_OG<&>C%N0UjXBnsMa"!/7k)=,V?u($=`+Yn(2/@Ae2%fX0=dSU[gQIASuI?r#=1`(:t2@@8Z3.)YT^
%`hVFb'1n$)]J#m>U4/'-Md-=fnJ1pO1_X[rJnYJ*7/f%9R[0KG<4k3*WY<]]Q`q5ZM6:&&ek-u,UUQbd7$1*]q*Fqp8k^*t$C*PK
%jD51o4Jj;I]jtf@QBrjoo!)2u2u7$Wf!n*2eAMA]&$f=GTBo"p_p0#*.]'_ZLqM9dlkkjI_*]WC!<H9Q47Xs/7ts%XQ?rj[\8l*3
%+!rglE*jPgXVUo!7u*je8J.KnI5Z;ZP/Wt<7ku,jdVm`C57u0#i3N>34A7V?@8l[YV)M#J[>]t4?0iaFqc)AgO>8`iBa6ZlLZP*.
%hQh=q9u(W(C^'lO\5Bi,*sqCdS6'[NT:J7Hm&2"0h@2GDd^EL<JIV:rl*s[B\2CPLKJ_Qq(CFOi$WWO[6&1jE$*Y-VTr[;DLl_I_
%U)2JI.[itc"&]6[0pYWm_8aiHC]]IbGZ\%pcc?!Zdn:^&8@L+$JudnBWp's2I68,7\l,CiY9gd&G#e::i!@oL"4C-*[B\tI<@%cp
%,PAd5Uf4iG+4Jqe+J_3D)\f-R@778iO.[PnN(pdSUJ1ZCB)aS\_82L8+N@A$SR'L0a<mFEa_6r^&g,Rnq1P<g[>^N^N6?YFj:JJn
%aVI-Z`mWB=Pjj?*7n*\UW%>m^B$VIX(^qAkDS7np41[J5iT>tC$aWc0A2q3(Z5!0<Obo$MaC"H2[7T@o)e00.Sk?jciBS,_&3-61
%]*=%:/T1P]M*PRXn[RF"TYg#P1U8YsT)e9%U5GSoJH%qN?8G9,D-Dk&L8GGFA8MXe_Ib8)_,V^#$XM#gY31eN-adPumjb<s!NKWX
%f\IiV.</76Z(]c?=D`nci(=t:?R(\%D.S\XdN9=N$4NP,eWJ`[H2[Q%Gk\hbqQGDCa7'#"s7;l_l[Sa/?iT..s7UsBj,_Mnr8I\R
%rZD.&s7k@KouFiCDh%eBs8BDkrr0\4rhnZRo`+pimskAPrnANEpqo>qh&lUijh&;(07Wc7O+5RXJ,RF&q;K@Yr51burU2njo*g-K
%f=q"*s/t8XeURVt?iT=s6M9GQQN-UC^MZ:9$Zt5&o7,,:rW`lOmm$ggj6(nHiA^c40E0G8r'p\WhgPHm5Q(IEr*ROBrl7P.p[sYu
%,H]eC(Kn-hZ#!fjE`><aGZ*A/[a=;/IZrU'7NI?d)9C\$8>51EdCh%J$Q.5r,9NI\@i]+pA+i)cN-j.$iC.iq]U_ns%U:qNk_O(m
%lti0\M/e.S(2&rAla]ATB,Ztd,ARDqmE[Q!83p>-LaLi;V#lZpS39O_8P#H`@m_JkW<hT,icmD-O=8F_*H@_q)/uA;XDB0[JLe!#
%F<2\sW=gL/W6.M(Pg[^qAe[U1L+:td9ROM7X/muc$)aV^2E8^G&_/=:&.4iQh3r)eBlMbuJZ0<.cg*m%"d1RYX!=c%M\n-:*n/7s
%%k$3,#C.<%45p0AT<1ataDM;X"9ZN1O`F<NXqWgOfGKXE@g(/t.(!iiKFoD[:iWoh?.m()+rEjnhR8l/H5UT-.Sfn$6Rnn)e\_X?
%7TQ%1cu-tD#l)VE9(bXrhq5Z]n,RI1D:]3N\%CSY`-;k2STai>^C.Y5M_;QBV"B63;KC/@B.#<X(aEG&&fUJb9`bm5gBW=MNU?US
%/$=h+7oEP;7"\oLPlgZY<d0CS7,Cc-B<ZA90L@bZrGs\s1<f#6BJ.he$Y(2)*ar-H*K+m[*e@iAIU7>:IP]J7i(M`"fI1>U5/L':
%m5b86_"2`MNUmO?`V<>WCIbA:`qp*DcGcUGOcf3]*d<u9#P6noRl!ADIl-?:'qZ3&h-*Rg[M4hA*#3Gpk3+fTlEJ.4X.n5n,RC9]
%_K/,WD.h`m2so=q@#t`bpl0_([C_B1cF6GTbPf8hie0*L?>=Gd<+RldZB,V*.T=bZ6tAbn2oe0@UaZna>l*jtPbZ)U+#Sd'"KL<o
%m&bUN6OKdq][&IXT_"DNf-0:VhB$4JgPhjJT`frHD@uF_0%u$mK"5'"f-#J9N\I)PZN.aI(ojR2("7sq%>S2.Kb7]jM-\BP%uGpk
%ZoN7-NR5WN,Lpc_Y&G&mEC9k-!RksJrrCSB*"1Y2UjP/`a="u9bq'4hBXQU"6f>[&k;QrCE,VS.MRMS3E<(n6J2d\>Ns*QnV&mO5
%lD.5jB`4+N0OXDd$a66_[bT/qF_VRGdJeNf+2b6F,&FKa/;HHkrQ-H5-=\79&huXGc1=n76/[!+$t+<`rI.tbqcBB3T._*chism^
%qS.6ps"F.qrn>CF:aqV%6+9%=UsQ)fr#KL=k$eh'O;%?1Atn:*S[MUL=S>h50(r/p%<!gDmtls.FFeE<,`XZo#D_U)J;:!'R?MnS
%'tSk3Zr)r`!DTomB7UdH.lO3Om0'_SW4K_MOKWCG40';b/edLVk/^:mZ.akFV)=tMi1`U\8H)b/M3J132sqeI<H)cS0rN?j+2g@R
%L#3#5R2,.:5e)m\3hP=HZ(b.?7nnYZA@`3>NT:@q(ooqh=/)T'8&2I3PD'HY-8XbKls5dN_K4rVYTfKe`JJV'_T\O)"Np"h!tcdU
%1rM<m]MM3Xj<%_#bV:,$=gA/<Xe\;Nr\=*pkSR?O8/h5jj.pPn:Fm3fLSKFl^"sPOVNmup<)E4.m@)=C:L+iY;LKqJM+$O'bU)um
%mbNuOk4g@oZ4F9_M'+M:-9m/6T_CmbnoF<2/urPpbm/976Xl@SZh86R1MME&Hl==lG)D`4M9^Bs'+A*</3&C+*"s$<8CM*7paObM
%cuMl):ld:6/19\)<Ko@7]YHFI!uCB.d5GpnBb+%Z]j`Ub&.Ds9'0Gq?T[OanOE:_(rhS.5$rk/.G/^`nA%,f<IU7"j+FPkV9,O)s
%[\J(@=hS0L(Yl!2qc]aZLFN*k\<X]qN>b%56sLug_(7o/b;AHZ\Ye^([:<+GhXkrQ*Yk/@&#<h_:Vhn>[8hZ`Riaj2OA7`7"&L$O
%Mp%OZ6tMnJ[Q$'N@#]:k&h!am0k*YJR_TJ+Oo*jC<0Dn+>XC^&fT*`ALT_[o,FDc7A%5h>b"sO-5ZaGdYsi8P!bMbNKF;J&U]m"'
%e`Dj$^q;BgXkg>m9NgNa[>Rs$MdOX6_m\hL.s$GbcK!MArQh\;'Q%qV0f5[('PeCih"SO3OhlZ/73J(liX)d"m'A,*!&Eer8&C/:
%qO#\1O%Nj&&XI!Kn`>g`Nk2$S+F;l&qh!$rDaC`C0Rh5/W>BOH>X_4PP*9Z!R),IkEcR/B=??i7jQm]Y3&X,7E5[GS*pb(E+/'sr
%6.9Uk1N6h<i>_,Vh\)I_e?@i8[tQS+2bj*nFdKM;q[S?tn',7&5M\tlnkr5gm,<(I:X+6r!f*0_:>7RVPT"(;7RCO!;LsIP!/WDK
%hPMWDdEC]BaEA3D2QRdc0'2H040C,*@k0EPrNhcTU1=eB4HF3[9pO98p'a,n-F@fH,,(m5`/M0M#lt4ITl-/N3Ai<nVBTls+cmbY
%rhSDXcr-%p,2XOM@Nm[=3[(+PFn(J=PX\?KeC8E`n`-"l3k,`9]h8Ej-q)?Om\R8!=J;f5b[5;8A5Gl])9m0":[nHef+`B(LWK@Q
%A+CjcM*tUo"h5;.X0H2Bg06$mr+%L`W)fmgI%G"TX&iDMa2(,2o29*t8Jb.KdX/q'OrPn_p.R3&T(bq`i1O"E6<i`]EFN"C.%=/c
%O:HbCKWi`tbC4Q=$-4(QijEgJ4'IHJkYhgn/RF).JX!!h.4dUZc;IV2CB>DHY%=Xn=*=k&1*ILJ<cE*8/_a(b9bKF8_jrAXZ#$\W
%.E/A,o+@#VZ=5,,jlCh3.E'\KR5=X,dp@@kK$_t"-_,Ks*$HXGas+0%JR9$U`KdlKa%c:J@=f99!JK&MJeb:j0a&X/R#J/&gt_c>
%apjq*g*cX8EjPcGRP9ae\mRJ(21PhD`W1'O=>"SV0HN7.BS)[sq\H2:4;`(JI?!1-o`oY6mC8PSh@_K2ot0+?_Z"rYK7B)NKE=&[
%[ps<@j:tbUrtugXGqQQ*(;`GhBoLuk0,:UWa'F`W=Qe2hSOji0@(/`]@'WZ*r,GBRs'-uY*Vs';7K4O2bef-.\s]$U-2jBCl`E4t
%1W@En?b?@9W7d$,_<p?@74D*;.IC%]Plu]k//#oC")T9f0S%JC8l7!Eoimt>1u8mk0qunIi:N&6@O43bAePss$<UT6]',WJrcRTS
%dtm$OAJXYK;A`m'jIDA$'-;pgP6#/qd?'&opg(1U<B_,;b^W`MV+kV[/6Np2F`NHQHgfNl<Y?#]%2Dd/[g#YTp0)W`7l1'9/]ZSt
%;-t0o]++\j+pMSDQ2q38S:W30eEn#aHreY-/#&RoGflp!;1R5+_qc,[:Nj+_1F)5u[L#[]YFqK6b1:DUV,>HO;4R6g7nUG`b$k%T
%#C]]0VBDTVbT6AZ+mjnS08kou_"2op>2YYn9iU"U4+<4F>Y;Qa'uNLj+duZ]IIDa-1YX%\Pk@#QAt9&fb`1-Yrmj2/N@=,Q*n5$6
%G\5X*qttY2-ClgC]%O3:#%Q&rR0*.Ybs6%;B7F?RMj-F+>icl%*khnY2bg,b(I[gM1n,j4)K,D;GM\FdJ.KOZV")*DHR5,\!N@YK
%A-M:W80D?BbA(t?;H>K[5kRtlNfsKU@[1=(Rl\_Gl`D9Q&OlCZgqkSZ%Jp`fQV,c=2P`<?S/ud_pR8RK<dY'@kZM9rIu2ub0`[u^
%a,"=lALk]9Z=G4>Rn$Rub(fkZ&[tEBkcRuR!cA3f&NBb/$GXS*#Y[ZK!.mGXNrE9!#uLqMAVjT:@@U*Si9g7*LEOX2?OjsFm>HVZ
%+!5@qo+2_r&'U\sDl4u4N*N,E,$rYT\@gXQ56VSg0@ZK$baB#6r;sA:q3I?oj:tr;/*tdK,nR@/go[ZFk9mOeoh[;MBMFRb2HCPi
%PG:WIbXHfeJuK8@h]5555h@E2cUQB[-'4`Q;HHhU]d;^dN^/C^6XDj2N$C%OjISls,IX'X#0j93hY6F.dU;AaF[5;cG5cWY4VlA1
%Cu!?>l8F=5ZEIR8`'%+P,"[I/^Ha=r=<%/!?U&tM`Ik:PbNQTHZ_ap]n'47fqF+cBV]P.7TEmGUaf"k^Yu[)A.qK<mDJQsD.ldCR
%4pK7%NZ)Q7McNo<1-2O+j%/Hfnj<&I&d"eKncQA:ahqh#B)IR+I!_;]V^<m#orT:<Lq%9Sbc/qnIZ)c,S<d?V.5k:pZnjM^OcQ7#
%>U(qLl@[-dXC>gr2g)'l7$"K]h61@!1])`NEE?sJUD!aUP]P4lSm*:TZuCYb$X$,"o!9qk0rnM&lUmf=ZHbu#c#Q2'(U0IE#e`i'
%e)%$0(Aor"p?@((5?,o7(;]$r-teeO44r>j=4iG750!1;,ZU5\]='dm><l$6i[ngkKkDBY,3M=O]9(+CfnD<`6"-*.nYT7hn2$l/
%2r8cgaZ%phZ-PS@0bhbh#3?YTdq$+&)0WoWnnWL;<NuksWoq8JSpcUVHQ[FbP@8n;r@bh_Dk0!$/s'ZbH@<EMAIL>!H-&9/^p
%lBlJjV>-:]EjCd\bZAS:]WRAi_Nuq972UjGAI?.&[q>/dA0eNgfE',K4n`;+75mdQnB^C7j)IUZ]rSTe*!'gt^f/Q+\pZ<TT6V%t
%5c%3=%A]`D#BZ[I5k<?magn-i7V.hu",KVX>SYRCgg6&"=[1oI#fjEp"Vs-W?1_(4-]eH]cr]=?^FMiW$CUH);;2T4PQq9oHL\Mo
%X^Pu:;P#VD/#Y+dB#'Au?5p^/h%1lZ>QCE"jQ@kbE+>b:D+RId"-k#J-p_sF]36=8PbBUqlZ_d"aOJa1!j'W#ANW?Q\XQ_)AZAF0
%1?DC"N11-`V1L:jL]O'*CgZ/]Ipg#nRA?GWn=$U?h,45tWGMN41@YVT7HpXMn&tYur)b0!D/=-RAJ[482i@ZCEeENK-Z?eJistUF
%G!d>(,A]\60WM)MMm)+D!*0\@X6W7=)13-IG670e0TVI1GU7p-HBB_;APftt,ILluZ2!_]AV<:!;=H`)h9XR,.DTPD[BUHp73ii@
%Fi0\S!KrWdFhCt)L\Viji*bTjFW.pK!%aj1cS.5t\]V5^ZYal/ZPV`gip8C1I_IgYE'8dkYDQ0ALCQ%c>VqWUUeA$.Wo@ie"';/d
%?:rm$ai_WBSn'C=3Y(LroW"h9\7.mi4tVi+.=QIi(m'#j+HV6Z",5-CNH%I80-$M9LED*6=HCkX%QTd\d='=:o"FpG-mQ^=KMo[o
%KGU.Rp9<Sm>04^>g!2DOET)N&+7n7cN$o(Y)HFpQc<qJbp<gb&Zi-U5Hg^fj,k/S<-^nd*c/&^'P4.:k:?W8FE%YZPPMmaV(NIl3
%03P-+E<\cLltW;lH$SMNfn+htDc#16./\KiiVDrOP^@)Q%QZO$&f4l3-Ef+l)O"-_,:\@Ko0Z<^jlPgA4OJ9$M\3i,/%p%SnICB\
%a:2]3G*O'uT"A#l\dTFXK++LtG5.(kML1"/E%O<N(((td,=]K2E+1;Bo4JB2Ipdl'+*n)H+,hDa`!eMK!iGA3_1q40;oB6JKS1at
%Q_;0KOk^gX,VCS>!1n6pV6219<fcei.f/?MUb7<q@)?.?'\$.FRND-C4fn'ZGHVWJGG/Rbf8>lLj;`%J[^o>]a6.hUg4?c^Zr_+e
%S$EW4GpVh&8'<!E/nKFscD)rZJl-N*-8ab3fd3]HM!g)-?Qog6h-&2-'gUa5eK`[oPWFPRO_qos2da.N7S28f@Xl-M/m\`5\$!(?
%IS)-pLEMARe8d0MgJMGr2uG,_ljuKRO"(GiS=5C^r!FR>[!\=I`hJf%/%9<4O`SK6F]&Mb_+YZ#C]Z^]U#Dl!Sbl;4^`3;Y]Jh&B
%T4TnT^tEojeViP-*lWpkVcP=&C!Q4Li^48pSb9r+]`jt@8&/ZXX+m6X8S^)Gq\H&(S[Ur=i^FcD`7(Xk$GM/k$:a<6n2p!!Y6!Zt
%-O04=\?HAPkSTmS?+e.Z?dqSMV,WWt]SRZ%7G)Z7J!Oq*R^<^>)tem'2mKZ&'_mh)h5S_r$67/fnn%I?9d`7mgGZ%I64p)Wa.jIt
%*o-ldRN0JL?HpiK8qK7G;HT1o0BkOmJfil!qE*@gc0qur[cgUu5$.9tVq6rN\*'=c$5#4A2IO"$ar4M1TK'nA_=@dOg'B_%Q23`h
%OBN6T"Vg=O33,'!pM(cp/.1f!`\<"'r5ALoLdo<;=fm4qaG!W`2j&=&_"eRf9W,oC&D^pEp5ifIEs&U^1(nupp)s:GVGA\X?3eeA
%6lM>Ze&P^jO11PaCZGqc)-Nlk[?:"9S2a3mPXsOns(fdAo_Xck'-N*J2,^W4&bAs&kKW)E<M^Q'7-Jgi)SrBbNXS[J0rO=8ou.E4
%(ri/+@dEpc^Be_lWGqg^37NrBq^mV?7m;n4^."?gT>JfHSn=2CZ04m$EZdZt6Uk9e7Yg?84q#kmBu)R"H^piDrcVBdE8%>t)*RZr
%O;$a^7ei;^'U1:,+$np<7901";]+j>-chO6,88u@G)DL!=9M[T`=e_1ip<]CL#7bL&>>k:bd[kQdOa!Bq(*R\14D_%,\pqYF`SWY
%ZA[1u\hfhk)2XNl`^#^68lA+Rrekl1M5T;od_e\4'%kDC5_C5NCa*\o7?Bb:M9m>"L$hJqb(g/mAr)NcBhBj6d<O)@7';_V<oQSM
%d8kq`XOB-LbE9.#/3['AbqaH0&ls\XYYS-`1Lj'q,lX=PLQV(;eUi"go!f@L7kV;j78;l*`5>7^Q<,d[*fscgaL^5=QlDh$rf2lB
%B!e9BKB*Qj=l]AF30FKCdehbkK4g5O%g&nQOSg_G]P;-`/C.ikeDme.(plG,&7h@Ekdd.L7?drW2HeT"re4?].;tai$m5NRXW#8X
%igr9J'lf?-s,,OY-T)U?L*q0L2H%%Xm;V3-2^8g1hVq,B[-DS!%,*i_3(["@@A_Vqa"phE&$,mX5NZ/u+l>^.X'XiHl<>R!7/-M'
%V9T-hYKAeuF0nb#C:Q@i$OK8dcWZPi:E0)j;Aus/Qp.C7#pC'BQ+3c'0K9h@A+Poi5S<NJ9<1Y>"n)7#g^kO9U$,/?$^(gd3CCfL
%.jfhk4lbPsBIdl,@p;$c?ZKc'#!ckd:4^P`"/%)a#E2Ou$I2(m2Sgj?!=nG9W'W!IP*VB&0`PImM"lp,]aVU.S-RB?pWo=SNmNGV
%")r);M;T%r!(,dQpHg%4KG<P9C?iA:KC0&A5gUq3kbdQMq@"soGS)J:!t2%^_Je0TJ1RH*%7k"A+ls;,>(Vk[K+ukN+aR+F34nMc
%@qSC>;TZ"Yjp:;tVG\4'RD2DliJ/eIg)V5&+IGm_2<k-kj,6gsOT50U7d].O*Hm=[`l1gpg\PR_(;UGZNF(!b$Mu0@',A]F+U^)&
%mC69R5VQ*'J]!Y'g*99LJE[hCSVq:*/I#^$+VJ%<L\M]o^l<2[i`kt2YbKBd1]?-K??Es&RU$jDo9=\FhYRb%!e-3(lXp>Mr,V-[
%k3!G6%_fW6fDP?A-!;Z.Tadc059;+(Qa`8'$f5VjQYbCB=ZJrN.D=**:T+TT"J^`[h:=2D'FlJs?g,\1$<3995b5ZCEN1I<MLftU
%3NB^=Ql;/?FAf@&(*._h!df1X<\B4YJucpI<[j<g5Y@9V/f$*W"Qj>$IZ[filr^6\;t[X;*eA">ge,MJ7Y%dKMe706K16HZ@k4Qs
%S>[MdR[&P?bJ1JZn9c5T9+''3eYTEl6`e1DWGLfq7g/l`gBVnkHn`ch9na2%9AGM*&!HW$MSmY\a_2E1[#.X:2ZfgP[(BEIf->+J
%TK$l@3,s2!%1'Q8f04sQ%GDZek;s6-3p]"+!7cCD?u0_L9>Oh$,?naN1UpEe)7"\NX$?4Hl=+pJlEm,kg[P]cjO$`hbg95-QRc;(
%(`*e(C=4QqNJ[[Qa,=a<a(LiDG;)8g+]NJ/q3SVG:b];+S1.?hU[%0r$&m)u7"ADYoXV]laUY5Jp=!@*6M1lIhtL$Z%<&,B0kD^T
%"hp@m"5nAnMN72=/MVNmK(SBr#.pu_^oq$"(dTr':cq02^'HJg>QAl2C/Q5V]I^9>646.(OP6\8Scca'RO>CP"M+VNSoP@,</0AN
%G;DImAN=Ha`I4ms3>/AFh<L+E(J-qo+4u`7P0h8g>/4;8)/MW0$6*N7bFKL;kI2:jhT$-*n6:!E9:Q.m%\Aa8jmSg8*[6c/MAGRP
%gCSEgcB<*jm;X,T=cs!gI[Q%QW$,B$!6(=:7UnhV:`WGa#=:!J_#Xm^Ips@c3oogcD\$Z:dW')!0UNf(!lp<o6#2KW=)kj@Pnj(8
%XJ-]7SA0WDA'\$<HCr$2DPV!lJ5"*:AOO!9_9n%_IR'g-j$\DSI:_'Q>5i$c[i."uMjb\S<3Z]YfkP&^pgoNk<,gMD`qs)]Q!6D$
%%eul^La^R;96.;O!tpUU#a20<d^)1X]Mnpu'%+L5AJ'Cd9BE5Ne!Q($@\>rBb(t/rIhJ$o5Z[]Qs,6\GntZ(84idEp?S(Obs3=j/
%s'm8Oj\;Q.^c[1uAM[.Q;#nhW_E:]^=ab<M%hshnOc$j_'(s8I;_4C)K$_#!!H<@Dnn$cW)#9%1ogX-5W-CVH&_,!V@LGnN?Hne1
%gs'C]pup.A5si^N3_[JR%<gVQ-A(maW%uL25SM_Q`N2_jSj\RU+UM#IlA@/HF;;WEo3>Anb(R;th&W>DA#dW^i)E1II?IT:%mYjC
%hdp3A*$@=NO4!f0l=)M@P7kn!-s]HJ]ts:[::56=?6GR#dk/HA%l!FAE]TO"<Z/G'I&$YA&QsW?U5tY?5DS.[QZdjiqhTY,7!%7j
%>8`1Cgbd:pO!YGM[KhkU,6/l#M8ZtFC;UKE^5"l]RBGroi'"`T/U6eYahb)/Z/.0B\tqpgZVX*gS0W>h5P%^P*54Qt#%8LZkNlo#
%q1JXK(>ccU`9\s2L2LlD%6=G05;sf&j:&UIlcB]88Zga4psoK`MbuW"U-A>;X/eF<`R<(g/7)>Sl3[q:SbL6j#:j@7LGC(0NV,mH
%L_f\5m,UZphq!"L:p7`f.k7hH@X"1L$]kW.qKqjZ[kR4g,ER*M[V$));@a=Y8ep5o_5;4VPF,Ik(mHf3g4si2D6i%6qid]<gpD+J
%;U`sq2mPZ?e+IV]7qIca'mbtX,13?Z$joSM<=>[42rq-;@mu>i1&3-0/_;b%[%F&ZX_VAHp+4AD8eGUd0V?n]MurarXp0.!-fUZ$
%UAEVD[Ai2'XLQBBdDT5MZ?CAW$ppCtg?-hZfqXYKi/^2?",AK^%WnZ>:0r;%d<_QoFI(D"9Rs^AhBM$Z???0VQE<(5p3-IXj5gcD
%Wi\\`cY1L;UYT$XGQqn0El.5#</%L[T#0,bgG]X$D.UDRi+nK3/5H=^^9@r/F_&:drh7Ja?X=`5n]o;R,UXi'A![$(eB[$lDeG<@
%U,^Fq\,)Piou51A^QF4tk(@>(%3KBWFnoo(7JJb1!PS<gY4c5ZfWqWM*'iI(0*E+@#M@N-P^QY.K&C5sTknEIqjh<cb#9*0=KV$G
%hO;(dJ//gU_M@"DL"+8drD,\.Ht+V,i#B!8QgbB&L[lf^bd'*r!cb\+N*B$/QpSFAGEuD$(K9m.=aUA"&,WhBdS)#]Nllqp!F?=M
%HF6/SSq1QnV+\sfBi`_NMk]B,2i,1OFhr'bE:!kq#aV\D"A!C]^"O@RHFp\19Fbc6PeY-kHjO(D[\RX.@`,b(legPGW*p\JneJZ5
%+59m^:;^o8lfsf,0?l\D1`U7T.Z_IG4Mm.fn#Z`Q_"GJh,b[L`,Siohr<u=1Z`c*8..I&VY#q>0`4?>#?F^BpN/?99DVX*`=2,<K
%nPe#AU.op:?gZt4pu)@AIhPJ[(u0O#F]Jgfqd)Y1YJ]`LTmnu=Y14m]ULia_-d\R3Hi3V<qD]K.DVV'r/1U47\G&?_CKTaS>q&aG
%hAWD\E8Tjn($mi)BCG%;*h9FDf,i'%^:C2.SrIVGN'j.-P%uRB@L5[:Os9F%Of.Rh:*M!@AFlnTZpeIoL.*Q>;T(kb"iR\gdN-,h
%%hHHTTi<-mr?`ARD+2kna@c1I2k0[5fJq%D4.j]:/&8FRlP-R&8@r@;.88dVE!F]gX4G*#h>Xr*dVe,9Yt5[8C.`*X*qmhr!b!EI
%LE,7fUq0VR4]G05'>fPJl)Xe'-2qU55W2`f\2A'A<p-KB@T5Vg@\1mXG1bKS.\jt/8"LhWWb-`_UC;N+m>RuS5G<0uP!4AcB`5pI
%;K&)*ms89gXK-TTD^trS`$L;-1KmLhr+*s%cRMC2J)=PFd"j7E%"UP82Rt2hLcfO#Ms7$T%*gSk?2*uM@m5e%HGfiBR9OU9Yma>h
%'X6-VL*C2D_BQ'#@BOpA=?NV(MhtCOGmMHDd[<gLK\,K'`(dkbd)J<dQnT:i;/s!ZmBdC1,jl)`NRQ2/#$?f9RAqZA\_3K_IT.M5
%p%Qe1C'+EC<=<8q4cIOmn#FrnB/t'qWO@>%_B-q%&7i>NJKcF(XOOXI(gHph%MV+NjqPT_8;3iqROR*FbW3m1\$$QoDM6EPYnqmg
%d(\Bs?(_X(7Ae_u4R7*g4a7hED&-P[7r34aLNmf"84;TB"6QsB7:U`u@+6KUM2kh,iZ[tCm6;-bYlu/fXY30o+_[jp#bY(ilNCn5
%Lh;!PPjfi9&c#(->6[9QM_HH:3kB6!GV/5RG7P]OGI[K[*qW4IC'AAAo\Wg++9ikcOJ_3FFrGj(,k[PG;En]k/(g>!%U^1.g*M_I
%8_%e:$upr?L6'?SY9t<]J-TUBg/Z/C]/cRFUn0W8+?7/:U?cUM_3Tb"70@"YALH+jNKoc*aAs6>$,D"om;rE8>Bl3gH&td$Yj9p#
%L.]1tc>Q@]bEV`hY@#tsQmc3hRU,j.%9p\nm*(jT(._0Z&UJns)u,4a%[M!5W]Q&Q``/CkAUIeldRLKs6Xj$#mne+'F8^.q!_C:u
%$WNKNOcX;96.?msBS7;J&;Pji\m[;O:%:_RfB'93*/WU^Doi%,YS%pGA;1";m+uMC&kD[9[Q_,KNm+j>A+N%K<;IM_?72Wf>;Z_<
%iMD-!G;aJhpR<]3cfZ2?DlCST&Dk1nMU>f!lhl_U8tA*2',^@8F?m*@PEfVX2jBs%SQ%IF7_uaBdtmDobm-pNkI,Jt"?Sbe0A+pq
%:C&1F34&hjo(AP%ma',5IE/2qBTaX*i9,p/&Ou*E_C6B+orqYK7M?G7h1k\.G>];Dc<*V51dHj"'=LV3gS'B>]IE?c8h'1fYCc:f
%#!LteTAi/%d0pDue>/B5ljgi["kmUJ+E"0L@KX,qWT4,GlQU<M]@u`+7ks++&[k\U,aj`ai1:Iu:Xh#2`W4%)>uf+k\coJfd5_)8
%VIlNAN_$<q_CM%d62BLM?T\2__rhEhf%DiE\5?ef?mdd7G!JWKN-E/NKho?sgt'48\o,1Mpq/sF=nurfc2^6s1Z#6uQLW(]U;7df
%@#-d$`J"'$MJ%N<oU%G.J8ce9V?o_R&rkD28?\L^BUk&_Q,gN`"#&hY86_,<EMAIL>`3+5L7BT"5KBf*D*M9UZ/6\'_hN;IN
%<.U%kG3(Gfl"WL[--X$EH]Y#0TL+>?89gcNU*5-YM1U@ND"eZ6bnm,3\&gF>5MeOm=,_`.fgO+;V2t*nI_O-Uj,'Nsp,mMNJU&4'
%Sic+cgI!(m`BfN,F,,[pQTB5f*\7>]>!D`?#0E[oEsP8'6B';7Rq&jXl&eU3TJ!WC09-Z<)F60Xb>+rI7V$2l@OIt+YjfAI7$#VY
%c'p7@e56rkDBKl`D*mNaWc;f.rM'"p.j\)QJ/)"Oq)HU^27O_E1VR^#f7C'jOJ8J8GA7YiA$uS.8Ip:)n%u.G%:>RTm(g>(O?AJF
%dU8U2qT=8^*_Q=7^hX0i;je1>&ppRH=4(?k8G+K=XK',;&pO/gK5m/c!Kt!WMlP@XR%L8,k@CN+Zb`T(APO'[*Ij&QGd^u*SE8'T
%;4/VT*]2a(:sB7%gV62'qeJ<`H+n$Qd)BqD7KUnPA86Y7B;^_$crdSjY=FbtP6*F_?>?.S,!KhWK]Hqkbic7['V>ib:Ob:L;/F"g
%eiZJ4"q#bRZAp:H)q(k])pp-IT6th79bE#'8fe'QSJPd[p@:>\4dob=s+rOdSK")#$8g(>Cb8[&@0.FA]mAd-#qeqp7GJ!P,#Y>U
%1X$ndj&N`8&<a\WMM+a4Z72:Q]1RMk#:PEkTXq0+#FimsRZ17Nq%'ap/Fg9D@Th"I`KV44Yt6_V`d4,XjNIK?,=hqIgXb4bg:17E
%h'fC-QM^&dY;hH8<If8dimV/mdM79#g6jiiZ9?Q:qo@UoI,k.UZTR0KOYdlBf2iD/q?2"M?&GM%]Mf).6I5eX1Nt`KL4CEG`qO('
%j;ucYG,YqdQWW9MQASrZ8iiJb/LB4s<2_lt(*`1IAD->CU=nb<0Bc`Oc@+W;[$[Xke6XeSC/'=66)nb&DDYW]jR0g><]*V]7/Z9q
%Tg_\&3LBes\c&(^VV&_c.<jE4%:aMfMbX5*mJ4u%Z?/eZT2`WoM.DLG&GDekb8TYuPD]09#&4=@4t$J9Pg(n4FfpiL,n(0id?Cn,
%/@2hgP4m($1[,4b_CCt[%`%Z+1FRaj@4fe8A$M$h!=oaKFa2cB%>7;^U4Gj2F<+hp<Qj/(cT.-FkEm8"Y?.8KSR#.L$rs':k!@OB
%O:ItP[4qRC.U!U(:enHoV1IFX&4C$GJtju^\7$l">/lq?r>s^/M2YSAh>R%Wob!$CGp^T^NBZ3E0!Rmu8"Hn5[PYc[Ffidq<^Cb_
%<4=Q-KLjU#;+ub2<&<+d7r;mU8$T=rQM:^5RC3_Lm2;en)5Kkia^=/l@2.<cPs6ErgEHOpI)GH9TdE`Ke#r;^?&6u2gU"s7IUaJq
%`gu1'l:VkIo8jC55KSocEI6!2B\o\@B-*2A`AiH1ckZXaq,!]!!)6/S&h'kQ_h!2R;M=DQK[`:'?`3.=WQD9G?+Sf4X'08k!"L[5
%>\Hhm0JD"&+Xb9(?IrMaC6p3!BbIB/E72[#97csZ7^KCDd1(GQHjOI2Q6m6'LV6)`[@o,5!J5H<mJ?gqWFJInX8G%_XP)bB=rk6C
%SNmN'je0Ld%7&QKAg&nT;Eknfb6"+?4d.72CPgYqN26FD*qUU`h_W`a>no$`"*G31k^g:-<]4dc*i;WW\?RW;c''93.o"A]d7SWu
%(CL5[>jTo1]%9IT^jBnH2_`bmdHP;,@KcosOg/lT*KNZuVbI%A.?r7%m2LF;Cej$f;%nhGD<jobU]j4CK_)UdNUM+&P.0]qoc%i%
%ZpbP?e6rX;$-Hn0i>dB;UaCq>r\-)`Np1X3?e-Z2_fuKuX;[&B\2A"@??TZ$88FP,N,VEF]"J6I!'+WE]7)(4^<Jki_Sm#WOZn(=
%q0]892P3UFF!bu=:o5^_]chp5epEA6/lEO!"Aq->GI#8_4%5+D,khtMb\]WZN,<*Ep9n0.R?Q]E9E)F/Q$1.Mi4q4Pbmi6HP02:Y
%$,_aNm+$Q'UiKkYeQ@JP"R=RN"Z5(^e6hTrJ#W*lo334Es%sp6p[LFsis[S%Th4$4(lF>mj9M]@>`8.j?_l4@8+><pnUVYp]mbjp
%'"G*pLQFu:30M7HTNNL1SK(4$cl0-N/_*%A%t0;:<n)Fe%jg&0`dU59c\hA$6p/I)^rP(QJu'(;bnh\Ag@leF!YLb5&8s9B\k2Y7
%mOfuiA<9L1/McHi#</cS$^(-+fKH2*"9jKV))R-onX-2Uf&Z!6&Em92$T92XTt6E^G;&n2h&<*t\-\Fo9iIMp@2:-*"OW3s+c"/a
%.9##kf]\HPQL6[m&s;)N)Y_#`pt0edGB(MrSC;"b_D>&EY^-:i*-d!ADW.c*K%R$FXdn>_iNeEYp:ENZ'oct_h/'k)[\2guB`j<n
%b\,K,^"bLAqh8ol[]a73;TMr/TE29#1g'[A:0J,nRQWup$fJ.^458X0.aF!o8lM@`lk!D7,:5]PU/>_W4<e0PVjQdR%hVPrY;Jm7
%&I!1J@)BP""][H8cog`!lB3s!R%_j2FtTp5Qp>h<8k+_$4&#%:6$Ja,pmAUP%-R@RllI;jAB1LM,/Uc)#J1LlfS"--K<64*H"On.
%\9$^Y"]0(fgC4j37*3t#7dDW'EpNP@iuqgHO)n[JOo,pe/QE.t`PVK.,ABOl?Kr#$D!+<W$Sd5K'84\Z+*;ZWi<?e<Ke=K3Fb^>b
%Pe*:T8Cj1R=CqW5L2isX6*P3h7_535GInSMO^e!=GQt`UMQ"*/a8j1t/kU(/[PX-KOFCZ%/Q0<1LkH?`Q\Z*R<P(X7*GjU^^lFtY
%VPFV]oV<Knd<Zt-n$ML$(%+dF"fX-FRuu(e,V]>eh9:h28,X9J*bQa2**$j/d,7`(C/dcfalmC[7[,+3C4oD"J=Y3R16QL;`Xkt@
%[atu?)D!X(Ji_g\$#1Lp!8ts'_+,Q4],RVhU@AH80("m.Qi4o0CIHOmr.Btb7NfS>CVC%JFE%geN"K=`RrT8)\/_76@Hc!HUAq;0
%$]8[N7]*PuN8)hJ#bUPYNR`[X3bqB_>\%$GB3#8DcQY9<*(-u_,.TU3b06h;,h+-\kR@lDJ%4?uXm3X;6-j!X8Z%[#$&g;+N-fg#
%=P"IuA1a,Op!g3R:MOU\:6[P9[irt#0!PeOWsS_U3tf%SF6-Uc>?RfChWBfcM?L6Ub@h<DGLJP)OW3VXVo4*S_JDV0PO0i1!i"Kp
%!SFmB5%.8r\[7ddSaH8.!]@6"cTU<0kA0ZJN8K(G!JSRm9+*VD-_,B0TMj7#OM1mM.X*DS>"KsF<*r12B4#kf>;DU^gf#ZhQb%d*
%9Pt8t=$!'d]=Scq=Caip6FYr0.<Jl9SqFV/r,gG6Ti0)NP-0VNN?U##HV&dPWVm%<,C&aiKjo@=Th+:NLN+;McL_)NGs$h%\/,Je
%ff^l#j(($8o`VDLPi,=9S`REunK#f#C.m2Bd':-:GZsQi`YX`S-L4BW("gc<G?jY?&;1>j)uHob%4TfA`W-dHJZPqEN!S=4JK!dM
%Bj7a7j3!;0j4AMY8cd$$G&65U6u>8%g_Ed`nQSJ_IZ;s(Dm;"OT"a0["HV*m`m5Ns8G<II^j?TMd2Q:S9]7d56\9;[O_p9\8@BmD
%qY65ifGY'81/EmD5`QGqA.0HsaJ_7bLmB>tTm(g+2NtIAkaejdq(HZi0tC^_$9A4\<S/%kSV?k`g*XN9&H7Y-E_JsE:td@21FgA$
%5:K,O;D&#kGmE9@$d-7#E:%4j'9>rkKY:]41\`ebm0N"m+sPe;n<L'lrt,&2$0u0+3f.fXO[Pc;K)H#nX\q.>oB8ia/uI2WPg/m*
%<5RcU<5UVq>_oNH*FL<[[Cq<FU>&:SD#"W$m]3#,nEW<Dl2?MT?%?b3F,9krZ&Sd_A,hjH%R>$iCcLc@s!d:[?k=L,e0tS-hXIqH
%hn(>TmL/9Hk^-gs<!QN.+UJ/FJ0BJTN%iHufZ=dSnt;N^d%hdl:k`@I/]"8sWSWf+>8m+Ia>!9E7SnQ+p=Y)O5Vh7SL6Po/EX6OZ
%j6u#G"cAA]ME^16;DMCT+#KQoq,kX&?W9f-5!K:G0WN*WA-k?o90\[!crB(7Md<Ml`I'7Im:!)ZNFl?=<C"Y>=T!0%187C?=[oa>
%o#`#Wi4a;i<JhMH\%$OG&Q;.(e$%r^\*i1iq&[GUjj0NC,(eWg$nWcnAa^,:Br!lb8nV*dYgMm/rpZlQ'LJGf0L%XPU%AMHi&gWN
%ch3Q6G'n`/(1#3QC+sc&BD;AMh*V[BV*GsPr15.7ig/f&\dWH0TomQEje=)q=tn7J&DC#kclO_gA0'L8%&<Pu(n9)\'nPi*N(bXp
%,l?ABER0Q`CTML`kTt^K82KPhfnFmX*k;VTk(%$_@g#6B,s:J6aSaolJI(qDh<lM^S$r97e`<qPMpYXIi?I>+0>?:]:JLmu_M]_?
%4Nl<SnU]hXHC2BGbWa4f_Ut;1-DtS%e!NlLD/N_\M>@%.UQ:KPe:%h[(?.*!$9Jp$Rg#o3![;1r?X'@-5cG@c)&*Z'ApXFtnb?AN
%;l(DK##lIeRl9-X9H6uXZ4?e^m(I1uAOUVcH9]TSs216Bdo\=_<Q&B3Vld-?'Jr,X_56@NY)m'pR0Q9F0?_S6Tg.>iI^cNBmLc_u
%afM;;lD@a`"lrg!T@iZD%t?-+C'#bXmNmAcq%nW[o)R!#T*0/sZ'UP5I97:-^c3f>'$,M:%/-io/6r8GCaEW":J6s!)hrAb,tkX'
%KV7E:3Eq+tP#?m?IWGCe>bNKmhismY"Oh,fS?7"_:E5rL<=)tKAgXT?&DRRir<.)h,Vu0\"uhWoZfc"ad&u&(c&jF'Pf-E@"sp]Q
%&S:*]]TIOI1u\QYR"n'p@DjE^D#qJhl4?o1+?WifkQXLq[L#MerIJ,JL7a,iQGa.T9M2uZJAVl5:jBh$ZnS!I0.?p3Yfo^fZkBrX
%asEF3;Gb$(Xor.iCC,gT2/podc-FoES1t.;m`]gFeIrmpVo+XD&oLc[B'P+#'pRhr'q7^\XU^Ho\e9Oc21<dr[&mNu1rRQ),(=`j
%cR!Lo'p,/$UDeK"rb#,s*ErQ7b.o=Km`][qWurIiUqFgH[R0"2\Pf#`F]gt+20m`BfeZ+$g!ErUU&?Za[f3Bu8.?KQVr6qZW1&eJ
%pc4e:C$`-k:e9DmcSkEDB2r=H5thpR<^HI8658[)8tmjC,B`"<U`%3\FW;*S3U*Ik>61>>>qrI`-L_t''M=DKM&aHs"^J_1cOEBP
%)=Tp`jp"9Y!6`bUV$%aeU%G<#5c-/R[2P:tIaA5<!g7qG<2t+,\Z_1YKJ)N8Zgt_h<"9I<H6WH;<\tpTiN`?Q<47=@"h,[Wp,"49
%P)nh2@]qG;]9tiCZN61s\R)UY0LuB_lR(p8)Y4*-SQe?IeTh0f-kB,)i,)+p1)qLE+)'Q9[j&.:HIq^S$bYfl=YPFVW->o!ke?"G
%Lfeb,;aWB`2\"Fm;aQ0['T\]L*6&q2"H_?!D#A?M.PF!=q\<0f`4um\nC4M@BJ<U6\3URI8RomrYRX?\eSF7)"[(MH7>Of[8Of#Z
%A/5O6"qa\U5;$\,OtM$'C@H>iPB%XFb'52A[jp=UEGVMH"QQ36faOOeAW;h-PF@ATG*>sMp)?N"m,;V_k3PM)ig(pGqOGgoQ=5//
%his)aK#c:8N-kg0R;janT].9eQ6p!Y<@dQ1LSC7iAMS,!`7/8)R14*0r"f=uN*r5SGb[YSEc1Y?1=0F^M\.K@qZ[ZT0UJ8ZRJVMF
%4m8)"(OOjUj>^O@.ur+S)<aH>>Hth4JsJTU>a7ROe3p:.=?ht=;qtn^1lt9j"I,:/@k1q)9Ih84.fl_*$H<.Q\H5sI(\#Q34%O+@
%1W*.0("W.U==COg3/Q:GaC>Gd\+$<P]8/H_4=-/KM"r&X'09e"i9_SGW7$[3kEJD3GNc,T7sa#b>2,Pu"%BD1)p&i4U$PL;!hA>"
%89'ip_O?Sie%r"5ZHWP`EBPpQ-WdNJVaFQL`SUAY1E/&U$B1oJG<A3a/(qPTSe2Gg;mM"eA"(JW+asnP6*@TB9[857>rQ```Za!`
%-V+;o-@.:g6*kM:Q4%&XggHO=5U[m<WoCQ#K0:26h8DqfJd#JG$=ul[(j1Ct=VVd;b):F/j/!tNa<h2_fWkArM7OD!?[:g>2ur-e
%6#/$!gIhj[C^;Y>UGKe"iQ7\Pgm"\t8IM*p"s\7e'QY)u9:3N98AO2@oZ$?iXX"(nN/,\)opE77'cn-XK1VFSmU+(k-Vc]A1sHpC
%&tqPl%c[KL,q/"X$I0tC%NMef\P.Rj3V:eI7X/N.^n@nW4PCJgBM/u"ILu^jZ%sSd4tq3$h$0&,r;/F#J^0S2>r(!P?/XLGlDfuf
%B'*Wl/TM<2<ci?iO]c;mX`!A9GKL`BT:Ums3^8jjM/af<bf*]j`k`rPcOI^ie.B9l0,p._6UW&o0'Y![1*.D?"lUhOlif'H',/U_
%rbrnejH9V4P(E4(Pq^F:Mr9%r32=frF->tIiXQqZooIh_1D3hEZWJmHTdu[$r]%`2*_Z'j3,#S>9JP"Y(D-3G:Mk?&.KTcT=p>j:
%l8SrHN^ZaLb4.?=g'tdNc@tcgp;W^q6JY.;"dne<`2kbC"E4WL"A+ge(-#n>'i8a/@&NUHBW/C0-)g,I\1H@9hFQs?V'/AO?@\Wm
%WC%RsD"<1ubdV0SpQHh_:?Yu&G6>diH4@ZWAo*jYBdFm2@b@odoT#*^X]#e*e?P9hh4Aku8&K#iJ[>[r[#QU2E5A37Zgf,4H-do-
%R-^NGRhUM"S`GLmKRaQ%N>>A_hpA]X[YGa7.A5NoqHP',Jht1He:4%:2(GGh3!2mG@\^40VM]ta\95Vu3d/;0<X5!@bM$UZcjXkK
%:=@O:7\)#OhTc&\jOe"mU$_c*_\X<6Fm,hKXup/Q3B%qWD1P3s9]9E;>2m[lL,*c&al_]L4m4B5_h\4m4Z.qS&Jkj2!e?*C,<3nB
%Y2]g-"D8'#Z6,4$Y?-Xm3Wg\G\u;f!m,5,`^sie2kOL;RJ:M0"=FlZ5kn?kX]+gZi[BAt+5)o[o\[1L[h"(c5d%baD4B_br`1Rdr
%B`6Sa':>Y.n:'G,!o3[c)uApIc3F"#b*8$Y/=uo4Gh+?HloW#1L4)a%69?"?nCcW[DGoS'A03pX>kLYGG/6p%6?[:anjJ@STo`+,
%GD^?Xi]\W>H\ZT8d9/rWdYI3i=:LW,Xi$]_4IM<fh@$"<$M`gH)>GfMq6:X(k\DMQ-6J%oC"A^^iq(%#9WWdCW$N[^]7S*YLpk\t
%!eQG(D[`"[?F=;V5Cuk!%4$/NXs`gS*N".p:dnTnGB5[4N5N[58WZ&D#KGp9T5d3^TN!7oaII'2Q)J=@`ZYi^EP]ZJTT12RK4R1N
%kd:3'.`#p(#jRAbpa7m.8\g17l2[um:'4%Xp32TtH:\38"#[@cC06&NDR8k^a$A8GM6V))5]9UX&kJX?V8pKeL:`>tKn?]*!#5"j
%@k3,[@).+q3Wq!u*IWeT%^U^]J^IIS*[/bYh@u]LWh]qg2TKd9XQ@/^O/H<"f$Cue8f74\713Bm:<bN;)OcS+)GeQOp53W(%KY%7
%i-&P_9(V9)A0X+bFjMg+%"#Dq(6R"=HQuN8gUZsRYE#!L$:Wa[6p<+kYsamcEPON$B<kA0+M[:pk@N3Urf1]V!5`mEWKk8\$os(J
%Xq%/;5bNl<&u0KFMRPPlMrY7g*t.#?_HCf);C:$)!HsY=`F>8dkdhhOK9m]#8S<c.o2Iq8c(IlT_(QD.(pgW:^@VE#*B!&NIajNh
%Z?r[#[96-&ZNto%m]`.,QoVg#19]`l'1nh`i>X#/HI2b(GUK#lIIE!+6B!lta--<&##fehf)tr*ce'k&s'R2a?qZ97)FXmue5V$D
%]Z^Zf/NZ5ofa-5_=W!f;#)2%Q<X#5Y`l`V:eNsuhETQ6XrJ22sT7CclR#rXuZ!l4U%g*-gKO^BK%uYB>S[U7bRm4f,G**:$bh=CD
%5mkVJ+7g?52sUY.IT@Ep":-3M!oQ<V;h4?C&eKU=?38>J$Qb_bbe]EL2oOC-N?,iH[H+n/fLa&[2_(_ogadBcM"<:KfV98@2C9QX
%gUg<Y>JV2,NIDiZ8NakN_M3%SkFR4pY%&OZpdP<t?>P-hld\_9Nt-AYn<f[+Oa,=NM@'6_RNS#8f$JH],m;DaGA,1;X;d@H=`LGD
%hBTIV%&CR::`CCs'N6%j)9Lgi0JLHT=6F5G!#a3>Dpi0;%%6$rif2J6"cXXEM+76J+o%CKb1:2CO`iM-8[Ga/HuJdtOu2F4`j8/+
%T,\%rZiZ;FhM["+E(P[hEBFl`ALAe6MkFStT7R6h$<>GCL/6obUruBJW.?&k3Fr@nhB%OER,*7+(hjXdQN0U:@-UnPa[ADqBD;=f
%8=4kQAdn;B>;/4[L%0Yco8r#Mn$Q>i$a'QWP\2<;,L[S@?rR\2E)qM>!;;C^.q'X,V;U,G!G6,7Ae"hI=#>F?#$sp;<gC(fcb*t#
%e@*Va*g#S*@&%@+PMXP]?\WMmmp1T('-HJ.-%cs;"7rTrT1A9g,h;U>n%9a8/^]tRr`-<,2h&uU:9f88WWMChKjH-NRS;0cD%cMM
%R(HVb+K2MmSG6KU2g4%<]qu8#<2BhlJ:e;C(^`8"ll@W@P^#r`:]BikF)TP7\R&?UqF2XP]E@d6Oq^\Y]@Gpt=pcqdor;e>1k/5X
%oG(2_`'JYh9$VUrTr$5$Q#]:bbbKbm]bH0PbR+@AP8D#3!BuWE6U6C.Q(4$<"'J^d\i/3N!q3GRl;*cuC1)3CCk;qM!GIT#aj2N$
%=_):N2kUK$"mQd>/HM*#+?.$4era\pO:"cW<!S:\CRX+X0bfB(j@`S4#fAOJ(2=SCU^8/r82up/ZQ:dU&Eq$3GX)#A6Q(,%[[dkG
%q#!U[9/QW#ABd(m>XRg)=u_V=0B1+aT2h'I"d%tX_2GXbT2lT91E(rLmSF%mmdr=6bnVMWMcbtV30t8N8g`dfmfbs</798@$]:A2
%*WYJY/l^g+ZdUrXnWl'@IZ=CU-bM]L#sfHo@k0jETRk//+1;66F)EBT9\0K?87J*^p!'$4(NkQ17Eb8g+\Pa5L4mG?#(/8U$YE3<
%2KVR)!1fuU*+`]'5Um[*!%HsLV*XPqJ=TOjbfLgDbYa`6ikXe)oI;eUs-+m$7:6kHA-EC;S]+RnVU>jQ.JAMn*+O04c85[04e3Ne
%lP8jd1W(Vm.Y"E@8.*Ur6r-j?CotCf8ARf4L=';B/rc1_<V\J)$ZNJSGJN1<,P\@!4Hak;BE9n0J62dh-T0u`fF*KH'a,nL-.:H:
%QI&E<$WM)@P%5b8d]p1\U%SO8'naFgN2bK+QL.DX!+D>kq&I$T,42)*D,9jSkuKu0RL:!f+IN/,&;=LZ)@,XJ8E^7)*A8]);fFW+
%$D1f]@308&k0X"k9NsAs?A3P<]]"Ws&@!nJ$Re7#&no#O_V2/'=@,!h8LRtIaq+#o;>0oYrsaLb]8R+-#mH:N8g#Ghn]XlX+>5"U
%!r:8+L3@kD&D)bSH3+,H^([%Xj3$kkIaSkO.8N)#LA+`#nde/`,,/IW)M*qJO'Y0HP%,?riC03_Q@Zc%*Q_Pk+F#iB+b>o%1hP3S
%$J85>Q7aBa"$gV*+u]ZYJ8%k?&5]Q>e7'K";6urA5s.D.=V,9"/\$ALE1hkr1;)Sp>:AsMB-B!D1p&^8n/+P%SDf<fV_1G]OQ_I9
%>^S?pmK/EGU&c8Ve+d'<++hlr:E!(YEm<pNL=+-#KVFN.8FW`82mfAnKT:fo<_"ZJ@#?4$nfttO?G`Y_B=!`!$TNoPkqf<:8mNea
%9W3SS;YE"j9[?,:pTVcBUG>_T[6aq)i08acGQKQ-RXPpYR:E:chHqP-"@I]B?.aBf.mFuR+e@Ql%^&fo:*XNZnct:$[`asm#7kSV
%1-i@1aLgT^R2(p>AG)L-#JmK$-7pC%ULKcq(2cHFJB#SP5u_c@92$6UA&6q>GVXB_k(pu&&0D=YVA^TK.:VQgX2A\D^?I-YL@JEq
%oHMs(='eLjo2Ta)g*Bruh]C13P"Sl>%jin?+_`_nCV`T@NF]EWdMIe[3(sZ3o?qk"])\06@?Kf6RK>mPOfs\enkKb=*IoqWC]S0i
%Kdq8>PWH*MCP#ZJDXMoBX'V2:aX6!@jnZWHRm38D2jYiQ\QSGP?))G!#*52?,fY22E!8>X4q^FO!%.:W_i]4\]eD3s(4<lCD6Kdk
%9u]"id9"2_c0<n[VJ]S.m23nm,_hMng*t\TL2u_47k?^)\DZ;T@@n(c"&i?HOnl!``bsA$m(;W_(c?[]H+A4b(IsHF.i,#85g)V9
%QuKhK5Qh>^SiR`Ob\][aG80#p6kX@"L46@UN-*]L4Slrhre>,N(U.$UDu(rM4CqI@UB6<!Ac@H7`O9\b1*\J)kYA7JO"r[pkA3YE
%k=#nE8Ro:]:Q.XQZ;P_fJEM;8^3F\=o$$GMb:/0@G@0(._5*rqZh\B3.k]V+p:P*&3KZ::H\O99n:Xs4YkaAR?gRkbV;8'+j!Z24
%!H+s$BoA)nP/)c$5Y2^6p\4),.0nX)1>`P0#&"WT_T9s"K80(Q;g9uOA2n4d[%pVl==s83e0(#5"(IGALqU_0+ch`jHPem>DK$6c
%0raK],[C9dV^)dK$kVmLZR5.0QXQ>AJu/'IO?,)j</>!Q]!`-8O_b`g:5W4gKnN2STK'*#(mt*eJ<kVROb^o=`JhX3$=]lb+gS9[
%8oarP>tYI14b1kq;LeGInt:ukk\fm12h`KFJ7Ib;qsZ-R%+cJFih?^eG6+IbNX2SW8n&o^#9(Eu6!1"Em0HAkNf`-i(k/l%P?qo'
%)QfpQbRlu^NSeE/8BC1R;Yt9<es8nQMSZ?$\XJ&,-L8nFOc^!7/?mFr1-j/5+F9<dcEfo"'$<$QU4A^8-o#1e3hE$$Op\]%WS8*H
%$m]LQm*/_NTP:977O3l/8Am,=>g+4RVe)SSgO\o)6#!&l'mrdq'gIiP3eQ+I,QPLiBlR&230JYs_/j1KZ6q=G8MiD1Jf_*'fWk<U
%_?:)]=YA&dqf3!!h=%YR;KZ/OP&X.5-h:rT]c*2TQ@&!(cP7l0K&kS$N'Ohf#pL[DA43(UF1\"[6eul3AY>VU"?J<,8V7g.9*KJa
%N+>f3Dp[^P"OYO#'7<*(_TgGf`*P2fc+7trn#e>Rb!fIBhEfT,B&]RtR2<W+N7cnAah&fc781@0N*P,KrC^l3U7!WtE[#0.[&f^I
%H`%i*d%US`!DW?BOWk;>oTgQ8*7Y:7UZhfV&kGg)-4>p2S9`KD:.u0E?r&FUl%9kDIfRSP."9pm=k+gNAtJ[(G+rACB?0!OEU,f-
%L;$&*G`raV$kgAL[n$Yl?au:0Yt#7ZS&,Y7-"oo>WJFFFA?ur-f0"TZ%uFfH$oY+@%cH8HWQr.FM,Pu+/Y<7>01%=hIQu4/%ALop
%*Xlr'-MsEG.q<b[,l9B@0Hi[D(79bp26#&K3L==I:lpcTpX?ns4etm/5t%3?GGEj^G0-VB'3_jPnW+QQQR:&6fK]p61+#laF0CbZ
%SeRG8arN_MSg7)W:/tn+";YsF+U'bkBI*ig8[Ofc(q?IIA5]!PrsUdm3CL9kN+8_X#O>"AWDNpflJ2rILZS7S[*T(%Pi9b3,0[Zf
%+t"iFmO2DQ]t5I^Y\4-j-m@F"aPeF],+6OV,]"QJK)p:=\0T#!&9bmhJnU.?]@Ebd.Z]/#nM-+^)c_D5S5+`?h;SPZ]$Xbm]#9@c
%W_ITXJ3?\3[bU>1.8.=9&Jll,BN>a;ab4/I*0_:?_!!U('CurS!E#EN"n1YJS4%O"#+(0!?j,^:,Tn_nh"3$o7ZN.i?:;F-*O:4b
%(LmaP8bN9TC,g@K#b"Ic.L\4.K!aA@W95TZ-IOulDRP3@,!h1S#?asip!]mQ\TH+.GoTD%XGj[1T`^FFHcBT:RE;XQFKZeQ$8O^t
%7<'j`+WnV/62\Jm##Ul^apb%Z\R^G:,6B2XA&DP#;^CZa_9ko;dh?PIUuL@I1/43*=Ge+N$WEKS8(FM7QUI``/SQm/Ps7`%\]g<g
%9q7AZY3Hn%)lb`t),!J_Z."_l<,s#>4U7k<jZ5n`/QpIIj-5?LIH$QS8Q_nKA/>>-3"_r_"J39i&bNA1-m@D;@Ze^PEX+g.CfTWh
%KIdG*`0tt^eK1seP`m@'@J8$D*:6&1P#-]jc3u=\b_i&++2gPK0$3^aH4BC_RX71hP%s:C&i'^RAJpAg99a%IgWWV5pIbE>M;(^o
%NI35Z3-/WD<Yd'D&X"=887p=H;16&af\Le\Bu1QEJr5mc*/kd9M+9Fcb+R&Z!ieHc5gj1q=(X90S!60mk#C,QiaCJb3D1S[BK#&>
%916;X%?aJJ`qOBp_Yk;=Up\O/-qYk!&4.^Ho@X]j'e%+alA&F*\-Q3`O<QiSj`Z`n_.`"7:4@8q=giOtklZ)TP)s<N%k];eQ>aTo
%,`,GkYq'-bUId+*KW\HdO[j1241^+5,u#hBHo$i"UQ296Qku[eo]krQe&;]oV7P7aI7KV\)mQ5:&Xn]Iq/SuKJefKa-C0HhO6m3j
%7!`lg`^n['Gn?!'OXHC%!OOj3EA?p0A51O;`o@"TP<qlHGgpG>+Me0G4*?k:E%4FQ`,qmp9V^8YA(U&&;F7AR*!qX)TqcTMo@FV.
%O-_kJ78bHoQ=P09QYCJ7Wdo.ufMn_2MC"'Y\$M%>Zgmu!:#.KJ!0IS!H6N$:T9=jT\ta"nh1)d7]7D"l0T@<^a?\->$Q*J5Q_Rkn
%!%e@`"ePmrnTrFh%+%P!H_h-K.sS!2_,+cTZ;6o#@M+MRF7r)j!JK5a07@cM-,DHS;KOZtJBY/F8OR:3(Q`qU!@u$!8N;g!")Ohr
%0jbH\&AU,:3f'H)e&Yo]S%T"8TUSSNJO<YBain?OEQu,XaVYk:j<\mf?cbm?]Pd9oIDjMd;t$7_5@*NlP0Tq+l=K@&:#ZbMm#>Kg
%%?*uWM1HXE4VKBIEXXi<e:X]j*Jm@.O<(3iN6@XEF((7AYsT"VNft>)+IBu"kYbd=TeH)qdb^eQP)@@*Rk,6J0FjCU-#/ItY2%Q<
%nNsELQYYED`/ddoZR+MTQk!pIcB#Zq.f-GPJ=)2-k=kQ-9dk#q'"pE@!Kh,2da5jK-nF+!J*_HoG#"]o5?#Q,c-S/.Gm6VOB$VA<
%>;82u9N4/>7bS&WO^hS,g%P/sf`;0aRJQek@I3">b0[#QK/0hW+.X$rMjU9B]fdTT=Ft'O@PRtXJml$.`%<%a4JB]]\J:$f:H`YC
%Y/N2eag===G/:f7-'=r!6RRf0_iqUVMfr5M`N^-hC##8uVEm7hGoMsPaSD69.tI@u*ibh/AUE(@pYnYefc8Lt*/A5rLtOjOV`UcC
%,Th;dcmYHM`h4(\>R&lf?sQu[qM>^73]99pVWC,ud[)C,bC)<G9bBs.-_ib`jML?MF!)"SpEgcdP0StE.-A7r%l@9g#>\Y]^`g_N
%k9lcnJE%]F1Oj#r(877f#<#:ImmKCFJ7@$rCaL@X)Vu2U-]FD9l%h*WMii`RU(mc$MWD3&7r'b*LDX:ScrE,^C'(ZcDui<eVtD5h
%^frji$$?C;S-Vp[aD7kf+OgiC8N':V)8uNjl`,mgZcWY8RI>48`JIiIU;c>ZG"kTOM?UU*.E_i>G7HpenL#PjJRKj-,[#5q2(Vpn
%-YF&T7B[rHN'A6tm!]:nGOoJ`7^Cmn,t4kR$RBltklus+PX6:CQllA(0Qr?J%/\'Aa'M'TplK#a%,*R+0EstQ+DEd'Vuf=Gqdkr*
%U4T-q%o+6gnC<o3R@Mc`Nu\W%>NnN@QL(qfVCUt2>C>MZRZsE&,En\'a74SR;<(W[k)q.odq1B,=;P96'l+:Z253:(UMZ>U6Up+;
%`4VomCm'n=,(+%/8'k9b3I\LYOImN9`9N'7-^`.dR>>FpO!1Ks60YO_6,N5`gldMjJe^!)WWksk=k//,C'5Q+A@V%d;g,\bb]-Zj
%7-:fcT]k?BqX#@PjfC7^SigL[&'#PBnS5@5Be\r@@erGgQhQLqB8R10d/MRtH3%YNX,h82ah\TZoZ>d'7?jB#6cDI^BUK<u\4I[,
%LUetN=`&q5F8+doE?[\3a3G*+BK>n&R3PEc56bWpc@PW8!mYDC`)5sGU8*lVl4t`tJ[Reb+efUbk!XLknAE>H]0Og1Gt;M?7-:&*
%U`(L":.QY+O\De5PKrG2nh`/tl)?<VOtIkc$cT=Re0L]M3FoQ==-L3gQ[#rF_)3sFpn)2VXch+SL'+Jelg:(=9?[IM5m8POne)/O
%s#`82Z%sJu=j9K`UaN/\(2t(D5@0IP1i)X&;:[c2>koXpm;,c&P6N^;(jc:&M\=u3%UOd%*Yn$@RRWl>gh,YK&Y1sc?:Lq(\J$5Q
%Us_*R'r:RULe9kd5K9i?&/\Fj2j!+V(`C%-At&4s<:H?M%EThPd\*B:+u.(oGTSUOZK.cj102m>QYc;gkP>;+,gFd!]NljYWlDk`
%/.fjl\[$R[3$OsQG_]`%2K7e&/N+VtA:b9''SJlYbf0\8*EaOWkNs*C3Ojo,WRDh1179N9bm*XFCr(&3cPKK@0IUir.C@F*FsAO#
%d$IH./?-E:Xf6hZco'Bc_a%#6XQQqIlsqbW?9uuJo]8%PB9g*ZZ@s$aHprAdDW4@c4>P\f5oSM^AGe-XB#hJTC0k`l&!hJe3ONnf
%JV@g:;UZa^SNO3A3bjPN7baH[Lsn.R2C-5M$Ac>llC961MG`&18_u+7[o:@?H`O\k79Rif'9]l4Et?7F>@=4:=!:d`Jf4q:W8d*,
%eSSL'6XVYh7rp.OEl[Xj0:V=FR)FD)%]4:8)"JjTcG%bLA&Xph7O@9"`IWU$P=\GTSXUTN&sk3l3"N#+iCJI)TZj`!cm3A=Ac&#f
%MsEh^6l_/)k:(R3bZ_ip54`41dka@Qe#SBa#;Ie1K9O+X%V#UE,YUCl,Y&oM$3\</N/$(H1!,L[9A7nsp^E@Ni8R%KAiMq`i#ANt
%%EUA\:oBFIU\MDoLF&/b8#]uS;Y6C:^guC9!%B^"b]Brk^i0=b@2d>X8V&_DWifb>eF8;#\S+HH7*Dp[H4PK<*.n8sm1]W-]ulUY
%(s'!q'kF]P&Y+48hO_/foQeO3@u1)l!O<>)?OV+a-8f5R>S8g[]FIKDZ=m\G*RqB^,JCW>@7&p8B=9]/AWLoZP\=K]5"db59!$LW
%%%/Q[0VpkZVC#iCQuqV/2DR!&+VpF_pc>lcopr++A5PX<biLc;[pOfD)9k]q3/D,Rdo_#1KXPDf?[s,0nJ3E&6rn2#SH\[i.K[0T
%FM?X+]I]2!NqOn6CWHd=FNqod/4P-1>pt"X89)?5ctUX:mXq),/js<Hje`oT`&SdgBbE<LS<>"CT(qZX-1sQLQk;oii&kddQO9/f
%3\#P29mg*-J+X6DAhT+3ctft+5?P9,UL?BKkq5h!a$"nZRdUTRI@AZ!MWQK<[o$FmbI&NL/TOh>KN`id!/oX'MgfNRShIAM]`,j)
%Q`N>aehBIZ#e=FdXk>d4r:Pq5=<%TlP[oX6UIU*<<&qW;i5t>u:gCgCYXI6]e>ia@EiBF+B_$1g$rVsiDD9(#.Gep*\[oSSNCQZa
%Dc'&8Qu\qN*D!dndU5T6_FVEMFfPo2$mFKT@l8"-)$Q4T81Ca842OGqMSf!s&<+h!!ie.E5`,7M0Ka>>Oek/(MaD[>!YSQR5UN!6
%9V(jPZ,)u5"eUCTGI^ODqCibifX:<YSq4DB&Y.(#<GG0!9mPEfN*9)h4OC66X3gK)D\DB9_arIrY-hmIp",TLr(M&\ScP%\DQmt9
%Nk1_alUaI!M-s&nhQI(k/.Qnp2*0mOBI+M#W(]YniCY2OmVa%rH3X`dIKE:7#s%3\MVF7E=)-/-7.mV2ooPR"#j]SF#DtR!L4%[\
%B(^HiPpM:2s.7t[lI=uuQ9%?#FepGQS[p\#ju1/67015_ccmZlmJtRcA=@Jj>h&Q^+XZi0)J@g\&RqIu-"=eYFGqVbB?OU3GZN,7
%`)KA*E,Ud*(*0a;a<%u#MH_]=\gQQZMoH@D*-&W\_M;2kEst?^5N0+:dcXX/W?X7f,jqXf$6ulckFOaD?)Nf%A'1?R\Il8>P+bV.
%$ut]>+g"?*^s15ddij)!l<6G-`CAG%Zrh:28=o'RJ:k=B^EG%^`"%#S6.Sp[IkPMei@!X$#Ei.J7.i'u8;<Re1dC\[(1R+G-k8+R
%7#m]:78"2g=QC/:(jmj$&<1ZnLKPY/24H:3IQqrBcn-f0K.T>pC'[Uo[M'%<#_S/t^k;uf>d&FCN3=!uDc29]$%WCf7=jl2r!9UL
%RCN(pK$]kKl,9SW=TL>!/Ki<%D/Z3,D?B6n=p(]IJK`U,i(2`D*%PY#aJ8W8b)Mu/(4_MJ)\YAr$#4e!)4@W*pW=ooR)duABm+a;
%H49qD/;R%d0aDq(k&Fc$f0lVUp?WKWpMA86VM]__^?L"#GF&Q7iE;sJ*G+J?LN<s:_nisA+F#CkG?!pm\YH,_UAEI,$^LON[(T<a
%)A4K,qK=3m_!aL-)q,dG>!fT,`H"*^FPPX\<IBe'AAJ8)1m^'ZpdHukjsm=Adkib?@o-oC"::I=4:0%@pOP:a&*ErmZAs9$cH\2G
%][C9Ybe`L?eHniG"2tn*OY!-;GWsK/M)Z&W7ZpIUb$4:Q8j"bQjZY`e[MT]$P]jla4ikkbH26t!PV't1Z2"I<qJpD!WWk?DP)KR<
%0.9jXd,X=9%+n1Vqp&Ir@C7WCXibmBd;ebNbB/60G<UuaVl/G@cT^/lJ$hMTIJ1SSo?4K7092WrmD%KWT]I@"\cab[?qsuS@%?;d
%]*sV]'Z;Fig&n7JO,oSVIP8H&l1rf'gY;]"`Vf)hoQ^E&pulOnrs./R-i[(:=6HUKl1_s-]"GcUr.cfoIHn;fI('(Oo]GicmB<+_
%S*%\`4Wh<r4k\`Jd8g4V4<D4as5J\p@)0Ct9.gX![++u#s44BjmN!1\5Q1uPnGc\kM7`NI"$,]l3b3Nu#Pn#<b?t:[@"6mQ]8q19
%rZ>QmnuK:tj(H$'RDY%ePc%pB/uWd,Uk27HVRdda1D.g0P'h+>G^;m%S:5MG<#,i-</?-(B!H/u6+P#icqj).bJMf1DjR6pk(aZC
%lK]3<JHqcDd=%1R<4_j\0K6&uo<o!%X`i^O_#V3:WA.I3?;bD\W5,os,ghY</[P2Yd;ffZ=`;(%[JHG[R2[oa/UFJcQo&-pG30#h
%:kN8[>SN;SN_43:LLd7j3SDn27NuTG6AOBcS/ER=$j%c"T+M1:`@'[(5lWXFTi:&;=U5]i4usUn[WSggMhAT&_=%CWe#]-c==8-q
%%J5r68B+1t/lPa_`B-56Bc*hrNG"M9I)U[ue$8rp/-;i(Fp-tM]NE+u,J)>7*7SW-Wq<Yj_>fpp?GVo`@(rI'@hK\Je#s9#Y^mG2
%fIE,L3LX0.=rt0/i&r>!7`j0b&l>lh@/2K`_iA8H;(apn*/,\R6Sk<'j^!QZaY:>6K(sX@.:20Q#SG7)mK$"nK2qEp8ZXUE@YrfT
%FT4s9HAVGE!qOG=eu=71</W]?OWj:gS[F8?i/(Su,.WI3lkondQbf$`?k'bhahedc0pBB%N$j1h5t*r@6E60"iFikYW5C3RTJ7rV
%:HlBgdKFM5=:Xb"PNSmm-5QTs.Tl(i,Vos6/S:2c!i'p)'OYIcQ4%ngU-HFL&WVB^2sr#E]lC04[:hD9BE6Y\Xbf:mZpc/b-\*+>
%A7gL$r$W\.c@HjEdC^Tg,@:.U1RGfdU4gK(T&>84]kS*]]#T5?;PFk+?rD$#"o%YO@Hr-+=hqgO=?t3d2bm?GYlqUc;_DV3((@Ut
%K5hqbJu>XKj3i#Y66?k>WmEGP!6JAV@$fB-B!++WLiU/+_\X.mZ=F0)HPdgToI.BVnjZrDd>FM5nMi7MQ8$RBPgEPq.eg^9:q.-+
%b&MO+\qO9.5sFK]bM^.S9H9d#ka9k#k!I!`1hEFSKe\Z)kG??l[ie(LA:XAj)'WTcV(Vh$6j2"A)*5%e!;f`Wha]"tT-72\S^lZ/
%jok#!W-o&FCRRY^1^&70rLi;r^pmS#]Qp]KHd`q]<@dCIO;1([)5=6[A1:HDAF[7HAOh@/qpG>3k0+J_e'Ys:.b&\ZNIM]">0O""
%"t+IHc:lD;C$^S#Hh?r*`^&I`'sJp8X'iiHIYX*S@Fh:(Ntt[qhrP?;YY5W[C;DlgZD?`,fF<.k@)-D6Q$1;4bH<R4n&b,<ifI,8
%ge"9OAS_R3aUjti-l</*J0iO(XTmA?a7PLS8c;hd"^i'1r%RB_V`DJF[t,5rU#TJO(p9mlSKF0X[CG0>_(6I28cK;Cf\W^C8UX],
%?UC6^GSQXK<le1;rL8KnarPBaq]Pis</Ug$j@3P/#3TXc@H1@+7R+S=;Q%]p#Cec8KhJ7D48'e'5HKKjCVe1jH?WN)ZTan*P;Q==
%-qAUOK8LDb!;b2;J6tP.gN9P85j'o.?Ep9\V7FsXd>UG5Z(r$;'^SF:76$F6/0Uu0#<376NZehpK9D<jdY+[?H^ZB35=S+_bqM%K
%`_9+C51s4AQ@?(!9Hqb"2f(ClJN*hQSP*R3S]d0$G8dlcUXA8G?*)5*7M(Dn<k33H=!mRj5WA@Mco!U(">"j*U:$^7Y'?=R:Qo:f
%#@Uk62`a,oGmND5k=MSL;5]S%IkO;A3Y?]q7SI@Ebd!FcO`]8sR1Z]S-&=c;furDdg&2&l0[*qQ)-B-oXsHIPi!ds6HDNrkp]T.@
%+t%W&1ha`'E5Z^R"D%#d((YZTC>fsDHMVQ\6WOQ:BSOkAeJ$*%",Xh/UH3O];pWa.od%@(e_X.^%FV1MN0pCuAYP5D#VQ)MS^M's
%PCjLLous3fg\=CKqMs6@oVSL&aI[Q&o<d&'ae>)l5=Sg<6!BFmAhlY<=>8tOM/gV^n>c/=PDL4+[g^lPOi/ii#GQD\`GQ$K9cf<@
%#T;R24XW!WX-:%Brh=5M9SS)Na;nsF`"Nj0R*[Y+D3^TG7":9.XGBPuQRih3J5(Uc-4^0L(J+N0ose*-JgXunkiVuD$46FWR$=j3
%a]!68"hO8\0La`X7%2CRlAV`\LStm-XX7%M2Ro?O*P2r.)KG,$mPpTb>M9X^0GZ[?H!%<s8T$+_)H:%4UKRT7.R1M;,!Cr/-`NQ?
%XFlnM1\!0gcB(A*&jK?ma_.qc"Xe#rdC2Tj5nmPN!c&.e0i]_2CDBg^P:60JF-aK`^rJpM+>F(4(4@1`ZG'WV1;Y8GjcMok*nfQ=
%9>uW^FbGj`D^J1:\NFRQZ7((5F,M3oSd!Ii>Y%E85u8mF\+*3-f+u<Kfo4XB,f2i?@FY(J$1U!taZ@hS&DSZFBs\f]/QairN2,cV
%6W-B#oDOmskO"t&oT4K.26mlS:ECEqL5J<^2L7h"+&Abk<3/o.^VFPK![rkXoWFN3m`Z90"#gnE+]-sW4BTu7B5snPK1ia>9V(>>
%!0mUq)'i6)X.MK]E@CE]HU6HUlpT$[nBE/(2'>8.M\P`GoY#TI0-r,-+#7F5)`Gs.(mCda6T7h"'[ph^9Vi>"DgHb)`$jO\/bQS2
%@N=d2N[*A#Ht2Bj]h@T+FTi:4JBVgk93(ShCe_aQlOi"$-pc-i4mFCPQW>/$dKU)B(cXQP9it5[?;.PmqdK[P?`4sO'0Yt5Inp?M
%@"ZVYeM%E<JpDR56_NXT@-&',P?`B25_)-uHuq&c.^I=aLOrV=]toXGbg4WBp%9<"k2m8BDB+k\VT4OPWCPr>?M>a2T?c%ecU40W
%)s&J9"*?tRSTQ&?dR9dK1]sNb3AXC-BWX+g4(ts]9c^*k%2*`KUFD0rPpfC6N6)_&OuGkXlOA<c8Tb9.-]#\&p54f3,U!K$*`1(8
%gH$3>m.]=0?8.h63KD:Z6ta"H.apX:H&>+7DpU#eTL(D5XfD+.q[O>E0f%Y.A%L5<g&aA4+L)9J@THR0aLFW%S.MMC:2cNOO4>\#
%f#_S4X2h_^:Xa-/@%@O%knYIU7IO"-%(Cke@2pa%DL2;8;/)EV\P/SuR@kN'$&<.,7,9DYqtfo@=iZaY:>M]nA]>B1Q>D])dqCp?
%6WZ;7W]<tAja(<EMAIL>'kqtK?MaB`$Kat6d'o`l^->>q/p=2DkV1bOf;TH:HZ7##IjArip7osHBO.5ML"!Xk>!]nGWkT.J^
%L_;NU+nX>n&L>c.J@l5Ap4=T$$m;KfcW5j65u&d_9M4S2RbS!90??AD84T>qWGL`=_aH:%NhhoY_TP'J!f/f)XtZ"sQg-:sa3M"B
%35_59lD,,-s!O\!]QfpUEa%<X7bUcGStT,Pl,05PlkQ&`(5^QugLWFe[3V7A#5H0i%B;OPQ-3,LdU1S[bK@2MXE6),;4(!2>Tp`0
%FYXW+BUOuURtpEBp.F7+D5ipIcXUX\%I?6L>iK.<ggd\%G<IemnOu;>T'/R4;"`EQ#WFMi6?Ma"/=n.Ig2:Vd5EoY?_R$LKTg\^e
%!HNJnT::0JU2IV:$\>*CgW@1O-4A:up4F@#TMcK/WbBH2XX2mjqK2^A]fK\NeF;\c._BT"aFBk"Vg_c*.EXU]ZDiq=@<mOmK#PN*
%%b<]4qMRlY19#)gfpJH0<6;4T<MDm(.VTn;bW_32NCso1aLdH<N3nKd[%/QcjQiIt]!s#3$-SkA4DYXl'U-fGhU00%Vm[-+UuL&M
%C"IttDhq"B7*FD(+m%?iaI_-b8c4rQGneGT3)=/*Z0c<VR0i[:#u5$CbY2QlN7JbDX+FVA0f7,)Pe=d_E3IupA8+i9!-KDMP\ZeR
%+qLsA>!R6p]?+t0&n4/;8rWR;K+WE0k"e<Ij9\N@?tFsZbJYr^fS0K=5]LY0ZgFi-%R<"-kt&n`#KnD]);Q:-;%njI(nbg`brqiU
%j=6Q\;,Sg64;.$NZ;#jGpPS*^U::q-%5\m&*"N,:VCbu#h3^UD_uiJ&JNWg,5f]AIbk)R_]@)=)X:*6LKMo?>:9#NcL^`UJ/'`Jn
%+Lu[-2\h(71rc\"E+CeVfKV.CWos`oF,gcZZ:i]CMKpqLoK4^u-a`j*\0c3<cZ7%)#pu^o-ot1a_[5>^P"#iEU:Ugb4/s[p=IWeV
%"2UoCAH?[?@P\oo[#TnbZlUnG\c#0KeJY:h:%N2fF[;Qe(OU<hJ&sH$1J+[H4>_sm2P#;hH2Kc^[10%[N/S+7AT+9nFVZPe9N27s
%i3ecO*LI[hUnU]7.>]E8R1<bjo%o$B>Z-giW_OU&Q&qF+;XF1,oLqL_(.>m#R?<1S_&*_Qi?0q9bc2/LHJ)D^3gG]Nm=qbNB;&*u
%4bXSFI6ctpe_-$j*InJcVVIf.R2dQ0A67aO\i0*;XHoGp*Sfm%cd%>,0`4mU+soqf-@;=^pdkHG*H-X4FEV7_UD9>k'`'>jZZij;
%=aGe'2ctU-<SUWKfJ[Bk:.R(>g0J9i(8jldO-;#P#[`Am4Wt,fZsOW,f)R`eCec00:qI>c^IT0kmqJG+"sAV[ANWS\6QkKU$3G0R
%AS'E"6[G/EnkBME[/Y9@O-7'OK\c1[q:4SbMj)uA2b-sHh>-0S^3/MIZP_pug$%MJRcIS:X+]i!aH/.Tf5P!km.1)<@e]]kl1sc-
%U[)i:G$dA#+/<7O]X@HGMS")8jPtU]q(XHg@ZF,J*LQt##T5Fl/kd#NOX%//IcS7W5?1KdQq*E$dtmTB<9hXu^(VF'=i`9>T68V]
%Q$i5mFk?W5><Aha+9/FBbiMig3tb9cFB1TM((#^CgJ:bOSl;q-A-o"BHeg60c2udPQ\C4mJ.fnA='_CcY76YsPhObO%_SE0[\'SR
%%)QD:GDIdbN>bpp!p_4U+nONt8Yn=)riA$WN^N9UPO59Z2CXO77LYp&oLp>f+]K3Nb91iVSTW&N/n:jG@W+6uG!+H,Wp8#jj>cT>
%"=5h!"BS<<#)``-+:^ncetst*l)2*Np5VN/n#+F.W]&$P9bN#5NZ0l)q4e`A0?NS2a[PL?g@V4">s3uA*J`M/%@^.9D3ir*PgL.1
%Z?j\9SM3Wl$[Rp%qTde'V)GlU_u>Z`n'BhP;cT;U6Bec=TFVWYD[CJ*,g6fn1dVguB5PXdS(,[@gs"aNr@)SI<OGtiML]n(j"!0d
%+;l8SS1>^u/_m\HVc'#:bi7-riqE=*Cd(k,=Y;3;"="o$&q1rMYV'Rh_nfO#?AgKrZSseK[s9X!Cd41gn2q8^gjgtg3C"nB\^j(E
%ANJ&r3QqHircj*5$o[,%"D#d8B3CcRdcr2Bfm[+r[$Q:9dnbMo(E:6Z:VV35e[hI#n#JL>U/ihD%;qKt^$&d)`SS+IJL^mVLtS.F
%0f_V+J4$aXHFq9$7t`bh_$`d<NQdJ6(D!bf(!qH6\_'pn,F4gQ>)4^rPY8,N!F`?eH%Q8k"F'bZEPI)4m!AOL16Dj,^#@eK`Iico
%J(cVmTAUVj%WG5u:/q<)c?7H5342n*9rBkqUfk46=Vj@3?#!._$m?ge4+A(b!<B,e!'23.!X=t,lc6AXQq<8+\f>NSBU(l)@onp\
%o+l;A3U!"6dr\Xq:>4O.[?_GEdmp$S5(3p_!iSj#i7,)Yfa4LLQ_6?/'8^fFoANOu9#=:_=.&tNOt:VO/@)hU>n#!pn#%mT@r^&l
%!bbK%/U\C#`XSV[eX=,Q,B*Tg6G\H6?e:5-)aMYM.Xu.Re6"eEoR=98edT[WL7:!85]U=Q<8+AQH3qa0=$IZArBi:8lC=VXa]rX9
%4M73E]tpS_KFf-S/,N<03QWN5Z=PL.578AGI,2[HRNTl>S@F3&rDZLF!ULH78OSBf%h^,Z@jh_Eg(+:d7Fe7%_nhQhn/nn-CV<rn
%PX17Bh4p!j!9o&;dW`*dfJ#nuNi+=0%D";+I@@hRSIMDHY0r%a=Jpp,qSplr$*e<R"$(K"8C$MKO0ah!BpW@T"tS8&kfNmL]bV!a
%cE\jdS[jfg;!T*RU2J!IN&Jo_l&Z<);`\qc2@oO?P[W(UM&Q?&mY5CHXt^e@>8,5%5+=(^)MF6i9/&K-?P,rU\jNB0Na@Fb[u7e3
%!tD+eSET7)Cq'esfo#I9$!=SbF16HRcr<]C*&I*#eUo\XMi'V'4Ldo=Tgpb:A!aYk_QdlR!m#CCOeh.mNHV4]Gf"o[82&\H=X1>-
%k%_&"pgKohXqlK?:YkMD-+':FXk+hl.9dhAUrtH^YD04gUrbZ=*$2<!bL&J(Tg1lNihXGS.q"h3h0+=HUYq\]C'Y(lfI&0MZ`J]-
%:6juLpU\aR@T<[s9DH)7>;K#uXkQh*9f%^&+he30eIaASk7-"geK.oPj<?m]"p9KpMQ2Y+*9U;1$q*h*8!h,\`*>"e9Cl[2C9VNj
%Usq<8F:A`i8S4T824V\1]0bIej1)OmT+-`3\2(E$@!/L0*hJ3Y3kYXVTe)iYZPD'8P(EpZ<WJ)B<^C3u!+s-e]r;\GF!L0:73=E<
%1sJ3Me.l$#1/I'?/b?a#e#!D^rNG'HE\(6B::8`QT!R^=_GeY5Bu?I=%G5huKb(V@k%MFfc"1N_2S,VJ.m#OcBt"2Dlae*W\n3p(
%\qW<?B&9I/eL=5UR@bi"6C^LKG%0[,;.KZJer'+Ui!4LFdgVS(E_MHaa%l#7f[<<J#Vap,$Qm^sH3s*=>sFk=VU38YTa&'-60dT,
%D<Y`WFq*"TRWuLZM@e\"G1cL+XM^MkNnc;uiAhFL!K8kUktlTcMIGg11icB!k)c2&Z!-%N&YoS/EuArPB5ocGANeoqW=(V"6Ns?1
%+[qBeC6kA/moHL?LjmtA2HGa0.\nW7nhuL!>jWo%aC.iPpUCJ7mVXr8mokaZSQ"I_7VD.d[+Ze#)WB7/W^Is<2X\"g>u5>B>u0M2
%Wf9Ij\sYQPgp]I'`/ug6LN$-?1^B1VBW+_0W"]=!@kQ3$g`2/75kWg`JCQ[7i-$G;FZ=4Jn#.,!egGl&UAS1GLrsgHb7@IL8Cg+`
%YKrIF+qC5Tg_?LJQ!3=,A?FJi8.t9"c^HO&WHi0rY%[>5S).;iF<B$7F[%6o)b=:BYnk5$-Lj%rVScT#c<W$-\lbsM#&5<hrKOqG
%.S9ZIC:uN2-QL[p3$:+F(Sk'd2-`EOaU&frU&`)S+CK7s1S,V<4lpW)QjQ6si;hG"[0;=]_7EUB5\d?MX*1=WZhbjY7mY/$#tW&o
%oKr14/#ckGTIh^>=Y?\!?:!$%eut!>YME/PK?i1o7+93+/ZuqBC]tP;GQD-NpN_G,b%?$cY.kuNjJ*Sn_]G'HhSGHg@IVqS7`r9I
%02q][%ntqM/NE]`[Q(`R_f]1YJ1dM`IQY=J)M`XFV;O$%lnX+ZX.ZZ]LFq-ulP4;P*C$OBZT7mHVoK(uSL]t_k(aPY9BD\MqJ+Y>
%%kT-bRAr6X[aTbRkE%q+!0/$nX26[[qZ-TAJ9R#S\XliX:t5475leV&m0Q"e.^80G$86g$qYJ`U5UCM[khp=/Nj=Ofm;,$e9ED2h
%BK/!%U,beRDA;nrnjuhEm`L2755!s90!<#@FUm\P]5CrJ(O4[NJQ\Ao?>I@M103nM2:@VL,Nl'W9rF6@:g[7I>0i&:@6T&!MA>`+
%C:^-M2$HEK4EZEu>)(b:8W:6\L$D#Z1[>DoI&Yc2]Xe9u'=._/?E<SD`Em::(8V6&^m43](mAQB2&DK4AMl.,Y%d&FadB>EMUM"p
%?LP,a1V4Eek&CsU+g1>k/bX4ENO5K7o6_;k+c7aiKTjP(GNE"S8uWS)>SLU1WDEsLmAH1TX7Z&5WqG*<O96IAB-SW`pVRk(]te5.
%Ngdj+@-9K!+nt\@ALNS7r[7,'2YCl$CimR_L(bM.Nd"<-Y9+6\?,+[a=i@=?2UMj%!SWW?G,Jb;_Mi,/1/qXm0<XiJ0*Ob_ZM+AJ
%$%^VrA%1;$DFW[-lrYNF.eilKSEjL_E&/D&bJbf9&TPT1`!g7!FoLY88-7ksBH3<'!FWIgW;4iVC*;\boC-/*i&9V/C?%912WFrG
%%gI!Z)%/E>IGJKT/^Hj*)Ok_gL8ps"N;]1rT\5:!E`-Dc]%X;B;;=G5<57P?5!3]g:<lEb1fe*rfNCA-]?qs]R>o02ABUc,)^c$!
%O$bY)MT\?9@bkj*^qMNBf=7<0dh?#O]j$J[V4!9:3'XDN.8N8nb\EY!Knb+i#Yn>0'sM@=k4Q'CS.Ys_3Ln<U!)k,lB!%I?N?b2D
%Zs)/G!i9W4/#0?WPg["u>CbS;"AHd*LF>B;I4^WQ6506*1H*s:oO@<4`b8`bX#2b.Rtk"E+N;VpeM(+_<]9"%1ScZ^A'Iq?0H\([
%RrOG"br@HkRUGS2=M=`C*2[=hSF,:ni[EK&BBE7g/U-Kf)f(p_,d@RC:bEP&OW\%O@[Lgm&24R<U<M>Kb%Aj79JG@l))s&qJV.N2
%dVkq0R&j[Dehi-'4>'\5+FMS3oro<nPEM=@kXOSNXGt4'+5COG/C[eeAi*6sC3?kMX%%2`m4<]pZ&!=KAdGR6VUfKEe$Z-'3%>Xr
%^iI^oUS#\oeMs#T##=^9f4F-aB"q`W"GUAC(AU?eb%.im[;Dpm9X`<f[Ma4(L>')8">&aOQ^0p_5d6BP".^H)pbY7`B53pgYEDTq
%8^AAe$'Jf+pH^<EMAIL>?gmcYD.9;R3JlVF,+o'^ekXI)<O&$Gc)ltG&)d(Xs.!,#:.f0%_Ca\";2_<[^@gZZG9dh\iZ>`V/
%>&U&MTGe3U9@%9DgV-^(GFF-D!3SL4L+A'N&=soUeV*IcX2Chk2EL1mWj`e+<[[AI%U6#.P6Nu+-G\;XVpS#%oSij/252npI,JbN
%^^uL6^!0l6auB:A\f&Xk=IV681=VQ9#:!-H?7)=h@r'p(N>:tmh_\68^:%qqYdJCM\>qMdd&'emco>/M[//57#=<"ZN7GZ.(M"gt
%1=1AlVYa%KFo%7QG%b]7JW^&qTiaB%0QR?iD_l@L1L3Mh9no@)Lh[$):bG,nE@TfQ?#QD53GQ\q""V,?l_Wn*51nSfMUb[9W'd`Q
%i.>67?Zhu0b6D`b5?c6F'5(\if7@faV:rAI8On1/0;;Pd6^.6@7[<DY7V1Qf)YM?h<'6+fb.EM:!LpW&n7j/J(KNWZR?`!.4pFEQ
%XVTAfejikdl7B>H\reqUc.Xa@V>Jub=Y7['j%QG]3]&`igl#?m`O+!*EBgV2f#Cj#Q\a0.[+[&#ITt+"<,Z$XI;S#'6X6hMW/3N9
%/%8US]Znr_7NC#$*dt+"lkYYNpNriUSDY,qN\HF3YW_FXGPS&e3,O>'g;[9HXVJ%t79(?+`3!jG,edM(q1<gV9tXA*;fu<d&^h&.
%d@E*%<u:))EjT<<\Y]r.QSB4@mFR'=poU3b=gM>3$H1>-q.i[,'5:^R<l/.=]7W+bOB_qigdl-YY<g/W/IQ"p&gAThM\i)!f%J#=
%/&tlq7T.L1;MVZPjN6#QZhnIeH;-H4jOm%E,@A%o%nc3!Fu7S,SR$*N(1nbBQ+;60Ai"t^"p5>Hil-pAL91=+eW^9Q6NO5,/!F4]
%/$jX?VR.ct;iQji'q^rBkdoO!B%c.TH>2Ne]:pk9$>`ajKpV0mh<kF<I=uKSTj)@oAA%tLp*YsE'.c55HI!F1BbXGKQd2<$5j5m^
%5"B<HKP+nsc&F5O2Wi;j[HD3:BQXg/qYU-5R#LmJ3a<$CSjSOMo,tC%&f]!oLHum+*e`Ts"^XsKN0&76ja>;tkB(#Ndcrl+Gi_&?
%%C%KqU9A&'3d&n=h&9cQ2-PSJLFU`HZ,Q-W<PXkM;Sh2I9kj`;`Sdlt3uFUDiXM4<ojHM=DFT&uI91tgBuAD>1NQNr(:obi*#W_V
%q::7=SntF;KUn\;;K[ReF>*lrHF+&^8_Ak6?Zdi'JE)_"%&T"mDC#P$KS]8^l\?il)HF%6>F-98>=5sP'o$0qRl4BtaCFB2:c,J"
%UKI2YUsBMUqUmWV*l.#O<Y&Zf`7l:"Y,IkGdH3p#$Ie;M-BcO(WESL<@lIe1[)7q$>Ya@PBkP-/+]$i=P)QT>?;F.\:7IN8Peqm_
%HkU3O2p4-[/#"Mh>*7oh?jtn./P2-LE?B5'i2iDi/ja^j[f'.`K+3q]c0Hr;.B2QXHPPh[aTgXgYH&2gOgi6=?,#Q[ojg.07c$"'
%a%'%9dK;Hd2tbQDoMV!Xg(,p;S4O4`M'l8GG2h36=GEb872rHlEZ<fZk@>&Uogu<#"#e2K;Bi;67S)Yu=A'=L'=bLq8Qhgj8<PP#
%X"?_VDRBV3aJ?Q_Q]a?7f!89WT6Ml>-/&k>FO*RrqriQn25Y[P\;jQB<qUPAA.<E'*gA8%l.p%+k0Z*+9$9-i^/_OV("R'O$>p'c
%VGuXfDQ<b-o:dN%qBA@j[EQ)/>"'2oIB6NES?[OQ\j;%_.>1*lX4NWUg=C+=@BVLUS5F\2Xo`AH\;DLZ\;+RY;QI;:5@`Fb%jSX2
%9cQHdLX!=J6[QG)QtC'?O&RfTedg$7*&-3l'qPH[>FaCq_[4=r'>5@??qa`rDE5,`T(G:`9-g0:[R$\NJnP>][:<Qt:ECRMqO@2Q
%TXoj%Xb)Be<`]j"d+b*W'0^<BQ`Afo41m>CDfJr2l`qgu4'NliDVqLVe?/048;u$c3e;mBp4PM2<FF?o%:5p/7[Vf&iA%dATaR`+
%YK,>HJ+\a+Q,F!5>.NBZ(!).jK*F,2R`s;eX46B_,.9J+:>2/8!PGOUU/#7J^guV/eJW(Wj/1Q\Yj(0T03a-eQQ;KTXU9Mpj[R^k
%d^Y\ZV.J3LM=e%.9MIC>[<#/<4[\gg1$Qo_4$$&+jo"c\?*.\:Ei>"O0mN;'>q?bSC1an^l*V9[#EY9eA'rf!)RlTT2TRR\Xgb<J
%G(H`_QqcbNF"1*;-ekLXAK8M??+)o+CYq'Zd0lZ[$Bh/]ZtNpEmGJ!]9V"(`@j(SD*1+EU2;0n=Ca;FsKhkXl5I1,B^K-M^T3/5I
%m.r&SM[m82!FECP!F37N!Bf<:#(5P/#&N]'k2=DTD>eGClE4CL]NQi;2n!/VqM%+,CY(Lq#@--h?b=#Y[W['4/BtTS(gZ$LY?g%_
%3Gi'Z!$6*-"28r,"1WB""4fs:Ofa%,>LtfaCY(KM=0?JBLBecQNU%Ne.jEZhoaU]MoaTj5oaTg[f/N?tg@Yp5g@Yn"G/3b[ptGD#
%3P/=pD[rpThc:9:oo6.Z\\=bH#@-0=5:b>Eo^:H+%@OR5\@s]F=mPo2f\6"8Rc?]c2o.GUDZJClD[s4YDp>WNq?Cp+q?Cp)q?D33
%qSm9P>i,N&E.(+.euiokf<0#l\#sY"f<0&U`O5$$K.GQ_e\cKW6a9>>=*3%6LlL^>CUS83".0U,#@-053G[5*J8<aqNl0H@N?buS
%I7'$uYlk`VoaTg[f/Fs)>J%TM>e@^)V:jGm%J\ZM:""E+>i-N+\\>0eeqBE3gq(5%qnZOp3#bo$DZXf]hlnL>\@us4CgGr&eCUjA
%gaon:D`FZGY&=<lY&=fQ>i2!*c1MP(g@^G\lIPWfVteF-N<e[ip!!Yp6`t>=mb$&oXc29I7U(*VXitPSEW$3@i`[i7E2Ag#G!IR1
%WM'FKPe,8b!'Hp>!P[.-E.Y:#1(LIo16/QF193_u]h\GcC1Xhr;qh#H+g,LXS(rOLZnbWg2PmRa1@*u:Znh;f\lNE#m[@q>^fDuG
%nC\#coP:iiPg?dAR036Jd_T*;")tB-!I-j["+759JS;:=aoFPsPl\VMb&@_jBk78cd)o2A?"ml[J6,nl#ELh"G"=-)!7ui[D7).#
%`C_Yu.W;^MCu7i8_k'tEA_,\gi+oGg1E*-U=V%';AX[TsX\:MqJr:K1J6$8FWDkC#A)Vr=B)=Qq1'd;g*]Gr2fdq!J1><"J3mb7a
%b,i1V!Y8sU3$9`R%#8_sQ1=2!8h76:'W#@[Xits';Cf)BK<BIOihKd(2HlN[(9S<ZPrDka2K,FWWFofN*mi'Z?!N`-dE8Q1Xf:_X
%.oT4Kf?10AB%&bqZnd);3.N*tauJ0iAHEMBJ_`Yq%`0)CMKC/c,4*Am8N`I<k)Ya<p=.faWFd>)WXaCpC[SIXVs1T%V#A5H]f%5c
%bs*t;7QVhn(e:UaVOio%G>tYbgUF%l&[9^/,hp#1i7aT0l.;"L`@^JC^dQ":!1\:8&(@k?V4'8>0-F;t,5!,eQVC$4inA/_>W^Db
%Q$0%E]=o!+.@.XX87*QCXe"udZJ_UN&MRC+R&MRO_7B!Q$9.@2B:'=Yn(8nF"4h]lW$Z-a76=F']7cntZ!4qN?GHPBeCCm!'$U83
%_%[@N8OI8Dm7\uiEa1W"$9*Qh4-`o[f?bpYP/_mBb<$e:bn3!jU7=g!36VE(PVYnQ(k>DWHR8IN\@s89@d)/eB@L7tI[Ds2I%?j6
%=\ih4=/DD.ar\^^;&r,,-4k:%l"1nTk=.C/j0"ln9#MmGh1i6_'9SMGQ#7p)h%"DUc);Ed6.<;R%I%l:\7SPF=[/Ft'+Cu+SFj<&
%YgUdPj1q=qFlc:phHJJ\gKYTS.h/^\c6U"A9&kNl!1Fa&^f2r(i&G.WV5[L8op-uX[56fMe8'tJi?HHUROYOF;ff^!Qqdu:f!I6m
%TeQ=><),3=,,j1.;n%BH%D[>kX->Ctm`HCreAf=H1J+p3Eu\qc"NbUCOI^N]4rLE'RF8d!Q!u(ck2<cuAs0"^'<p[,a&tL#f#u/4
%(pj=HQV#=9-9887eRGM_*<EMAIL>^b]^Sa0jmWAI\)+A#jUL:LV;1IiZTf?;ZOXd=$T$Ef`#>Z"VcVO,C=G4cg]<?j?(i"V&T
%-CZtGTW),[D5l+08>=1>>!4FN=d*aA6TJSrm)\3D<!u;uUJiReRA9j&4?C*[kr5?R<\G+Lh=eL%RAQ_gG0e0>3cJnS)EVdi=V'19
%Y5H5.,1nl%"tC=bL2N85j_U.<#Nib$_/e]?2ll!#:.hLt/S*d6@qt4.8`J(-28iIbMaiD<[2b_P'1i?c;#h>;(fuL,*(A0.:%e.c
%Am??'4+&oN,\%>NLoMk))33`bkoQ>?/`Z+%nfFs62MfJ!p]af*mar(i%^p4U\sspcomp)FkL6(WXHZ,Q%2`m'eI$(L"HHQJX8QR2
%@9h=k]Ao?#Fbj?h$#NR#H#*ab<Os("m#@\\lA)EZ*'9Q=YN^i[aQ@VBKE#&_g2!.nK#!;r1h.BU@eZ<D@-][UiU/S*pZcb$]quYZ
%_uZmOVP>4?Q.L,V2W(6#JiBlc7;5.Iok*X9[e*e%C%@?P*bq4BE@,]:0>+?"[cu6WZU'_0>2E7R-TX."WQ<_pT\C-l0\9]DJ$DbK
%+!,:oX(!s^o=/C=/V78.@C93u%J?_NY/W?llK$B-gM\>\>BdDAV5/`_Oi5BRWiSECbcA<ZNGIfZVA\PR.TUtio8%qOJBd.+GVt]8
%g<n2k7PJ]raM>HRm:mQ^!\*G1D%`ng92#%lm3^#5>F1K;j]GcbRC7.LY9_:c>Y?[21X?iKe[C"1bB/HM9WX,bHG<.tSpEZ+?[C.D
%oAVMGF'c%U#p!\kM3eceH="jfA@^Jsk=;Ptm1hh=d9Sb,`u+4&d;,K0`E,Q9dZgHun`YU)T;)kJH3G+t3).FWXO$H*WH`LR9@]aC
%"N>(90JdSu:Pd]/BMY))b;HjD8rOIg.7BPAUId.>fU2@/.`;b#Yq+djf=FuiA%nCSmqbD)NR)]*=#Z7NSa0m/AZ4aTJ0D$iND=@K
%504`/SE?6Ak-TZPc+KbjXg#f+=7F?n=%]TrF/95/"g"],RENr4>:C:E!D2e4qhUa4<Z?m"o3+_;0s:38"<?cQC9;PsRejH9!m\ME
%($f**>8aEW?(NOB<e=Cp.Ba?3@ctU4Om#=jZ4d+fT4BN4!@(NDqac@Kp,TP,j(d*mVPC8M(!]b5E.X#Zi22^/*=rgmgiR9UL`Yr8
%(Fr&(2-*b>$>cdd'g=Y>!6J/@Q\>q9Mqa^)ZTbIn+\:J%^9E\0F*m4%rRP.6B%Ij%n72m<ap^U/l?_KAWF2rW;mZ1pDI2]m`SQ-J
%E'$So2Kk/h02$`Ym>=j\67+p%)EZ"sqe\mE=^"9JL2'2n38`98W\!"E1T<Y=_Pp\kk@@Y-VjcY76%4`QVI$GU_E;Y8.ehH=D;*Tc
%Ib9t#@"tCUY>nuZ`>)];8BjG]Odj#9Eq(ad3l,=[cABuVCA*a$F#>hOh[g[:SUq8<gu:cW;9JUM]$qlk2]Yl[3RJpoj4$1MR;YPX
%/K:qZZNdlKY"GfcU=>hV2P3r[KI\dUbQ['j?VehAA(h5hjR1/MfAQ$;gRd/ZEA")!=X/-$&cit6f768tj.)5#1?M$4^`T#Z88N][
%CRQ2XC@bot_P\P#O%*^Vg*5QmAR9jTeU).ne@OlTA)m4rbmIm(F[(VrBt/acoDu1$gpeL6f%f"Nd33aWDi5al!a+l-("auN*3V42
%;m4/@pGU))"C%&QPdM)\_+R!sYuR`Km.q&?&hauiL0()6Z/66NjaQeZL9IpuFVI!F;ZJHopX<g+/YW!#[@L7SdZLBMWlmsfZlPbf
%a1*#1EONT\j4$@SiF(mh?4o1qB<W-nKVt*aPEiGil![u0<Z4@0!);%%RdLYnbKWdb@VE=*fl&H"]46gN<.Urre\`%<ak@o0a@KA)
%Yea<T%@oFVEs:#a2QcCe2CYQS4jIYY;i&]kWP6F+_O+@?3EnpNI?Cl/<q1Ss@-]1@nB._hp-A8[]:UC<Ftc%dY?D!7a_E@?_Oai(
%Y%">\78F]$Fm[[-HQDZN'c0?rMc\40NG1S,djop#cT)N:8rEUfQr_H<9#8<OfC$n7Jf-Vl@h&R*G3I@ofRcit=8:k;.3\K)LrFHj
%qs8'g.EA]#9c1-Vpk5W*+*$"0:J5E4St:KV%-oZ>WmIi%?nN(mPp84(lUtSs5m<3^3S%#R7>gE1l'ERcGUaN5R'orB14-1DZM2YH
%7U;dA<`:fA=8HNDOs`Vo-6*;K/Lrn8)9cnNFQ]5b2tfpDekNXA0W#il)SBsfKNZ+E-aB_kJB_2V/+#?Np\I"6Y)ERNH[6K51h[."
%R5L(=P-YfW`>)1P"'D&ZcLEsKh./iBSa6j>2V1H9W4%?$oKo^Ujo>o-cEr`9j>o(-'jc0WNghfRDMP]D:72@(4+_OE^1O_s1_fpS
%%9.q8':^U)fYE1f5JjjI7^#g_n#eUS.1_E=D(q(Q$aa_iK2nup49QoT45$Y7]0LjH9YF9GJ(`lmOGt_\/[g1n-a-C^:/'n7Dmsp^
%=:i)3g\^iIn]D"3]_g0\gO1WYo?s%jb\0VmM!A:>TB2oq!$H`4(i'Ve@;N\"AUHhG*\kSTP@DS"X2fb3?*8K)1j',*\r7o$[CQJD
%hNkbBl_Z:ZA6kP+WCtmp-1'psD)sFe7uF7NoOJBs8q_00IS[g.RI7c;9gUBCXuuO8nP>\dUP_G8m1@uYo<YP-IaXif^L!?KZu#YC
%RC`:d/SeC9D=r@8qjA.jRtgS5jJ0iFZX'lJeW^(Va]sF#j\r1mdkWAJe,uN,T3Be%'#a+Fj6G6+h$s;3([$bJl`l[[=d/3sHML\A
%3nE-V9%I.n)tYd>j#,WfhtU@J>MIRJ[bI&2AG&$&elt9EG^D:TZgDR+$Q7%sV1mPLRoRY(6Sg*$IZuk'$un'Ni=f,68c@GtdmGpl
%r7=!qjPN-KbFA=0e]`JJl>:U*#=3(FW].<WCe$fo*@g[5r48:&U+U-A&V"]iBK7`7Gne'[;s'3kp27sr9W3G(QEj$HL8gb7<\sVq
%WV7q#XaZZUYC*hU]9m/Zl#,VU4)`\S4UPc0ntrn.'i1R/$alWV8054;ka4FoVL+q`>b.bB;fZL$-/l2EX.62"QT(i-!Y3JeoOE6Z
%ek`Ok\N+*c?-Oh1C2@7=AtM#m[I!QrNiU4[\hkOTg7FU?*o!h:lfQj1=$JRF8fH;e7mn]_d%6iW$Vf77qH%!ulD_9C.B^;NQ-4!Y
%bd\`Zj'IA.pKq>%rZkY95lVfF\\&fjqJtB6L392Ipl)>NJ2411G;jTX8-AN`6SnK4\aFQ.njY+-Ai8IGc2G4Cp6;s02\NuRmScd*
%2YKBHl5Qe7YO-1M>Foc6d[F<>S[i;YC/JKs$#qb.EKXAoQ(Qg6bKVT=9(A=J%EWbE:$_UimBDc-G.f5jo9U?hj(I._OAl/ebhes;
%b9#l>_]tL,,?O?B^\cPGr8X,^^N4i#hW_;Xn*'V'h>\'<c6N#8.5)El5s:PoKPu@K5<A]:kk=lFH[0f/ka`5orq,FJG(4op5<Cqi
%hEO(c3]k%MqLe+_PCM9cnCI#WUWhQK+(!_\:VZr.e`HJkk<E5=opL:+9\gf@P=oWY?='52`EaHH5ML\Wiq-c2UQ>@Wrq3=AR!4VJ
%gAUOPbD53LnE'N`-dR6$IsuYITU3u3rp/[bVq7g_[nFCV5?@^<jkUaG2o#3EJ%`^C'>OG:m)APa:S.ULrqQ1gs7#6CPKW.dh7M8l
%DSFePHusB#p&9:3an<dTqQ6*4\`:.pO3Z$G^V2nCqW@EP+8pS$jkIDS]=NErqrGAajmVT3Ds\T=)>+O:H[g"m;3pb,Z_Q9ArZL5+
%T3]1@3,,!r)$bUFhXufoZeC_t5PunP^L%t1;glnGlll9%1=&1q=Jj5"c#95'F*.-Cmd]_unb)/<Abt'./E?5<IX?6`I<;oEG.TuJ
%a)q`t&$l]BjLtN^V+fn;Q76c%UV8SWM,'mYqR%"$0:\91H6OGG]M-n!QO1+%a5-2:/rn6O-bK<McMlG]^@q714'BL6&*X#Ts!?3q
%7V,jM5.LN&f7d*_WQU@)fW4H#KC_PO[$``$XFK(+aaUaXDm+[jp&),IB?LmI5<0qlpff]TNl?r(<bn=%?4cr4jTNIcp_82B4M%Ep
%A\7($PPf>2coQZ#Hu8>tlc/,LJ(F8WJ$7tqm!1Spq!MHin;RBsrGO;o/oQbplf4\rAbLCs9eRU?ID/6OJ$hIIT3h3kCD(fiCT<P(
%f4plPh6O^X]D^])^YBLZrg/Pd5@*32aG#[=4?In0IX6!U]ANSArp9"B:Wk3_p:i4AX9`$K^N1r1Ps>6@e'%>5l-\C[Sh1lp$<U&`
%du,4g+oE+:#CY`a+9's"gS<eI?h1OIO)@^RlW7@uS@ghp^$GJ_0\jA?l-]1k?13/3<S-]PJ#KVb<'Pk35D\L`YpW&>=%f`;7$i]'
%f"[9[a>&+*]C88WZi7</h(AXI!qDs/2N5iBIedne53m,&iLM([_",IKr&o5HX$/b]4i83OC3TBnos<E=bATD>p!$k^lc\&/jA7m?
%cIDkGG8K$<G)rkJ0(q4<]7W]F(LNg%hqMN*02?CK\_)B(g&;DXgZOpuY.)j94'e;6%^4c@TD\]Pq@m!3HoJNA!djfsAJom(s(\NN
%]pnCq2E5X,CM<J.h'9'goR'rigIp]\X%$iLF*2Zu"$cA,lo/2N48"jF__C*NNhMe!-4/d0i^4/=F!_'\LG2ID&dH48<oTmm?[qgj
%GGGK9JDU7p#Pn#<b?t:[$gRMlqfql(rt0E%^KSAN9J4`9InsV5Q7ht*DOKUIW`lenCAlq2WXY)Ol/<5]+)el!c%rgt?gf\DG4"j)
%YG6<fQ6K\Ap?nK=Dkht@&E59dTF[fDc87[doirs-m`OGE&m\*'2W[F^]=[4Y*.+)@3?`,P_'%aN"jsO64A3#bDMh@3VO)Q"ajbYh
%oBbsK^XB7XPHV^3g#%\Nqos;2jd5PoI=#^l4`XZu8D,d\h++_-b'l?#&DE+F2HT_C-l_UFpF(9n%<`jBV$u"IlV7*PU$Z[pS=c@D
%UDG"F0P?2r?[d-=qj,MGXMb(N\@q_N9C%ohlDGV!l0n2!dT+WZi%HJ'.l_NZMj4bGYAiL6UA\%lePBn/0R`<VDYEOK\%h_!jFe1A
%$80fpPIi0n_2a(X@$t3e5*r,?aL!tNg,M+K02j&(XQAJF08H^SGkZ"Lot0ZNSa0;OgJ3^tQ[/*.kCr;0ms]>&No]\1[D)kPK0T5n
%dQb5p?E(I2ZQJRe,95Sr*S"rZ"TF;7ZAA%-=g):,*TFH(/>4Z*/M:.H>862e!1N#S>Gd@tErQ:`qr<"cYC(J>N=01jc26DeO'bFh
%pa(gMlFf@o(&T8[SVc.pa.KQ\l`YBar"8Pd0>&P/,[K9aB^u(d?MV^DNYdJsYE(;R^:Ls@hqmDkX*_%eWOh?Zh$j%cJTu*W\"Hgb
%<*tt6I`_7(]fA8;gOF<kU#p[KE2t[RDuT"E:\WJ]b8-.WZX*a::+nfE4B9aXE.*"YFM%SD^&?R7HRiu6fPch2D_J<]/(WXIMu&KO
%@HX!7aieT5Q1OmMS5kPuhpg:@*\Lch5J=]jh>>`$hgKTdEd<-1kP`5_I(#P_bF!,sp3WuF=LI/G>4gCc>V1ReInL7+cX@d5%<;.7
%o(lgFI3W[E\pY$7q;Ja7]R.7S`NfOuQcn\BqsX:R.][ZVMJB,kohg.b7<SR3I:180?_4Mr`FTOn\8U+!Ec1hrDZ(b2Y-sa2h]/1l
%rok4JnRoPhhE^a)US[p)h":"ob20bE]\;$as%<m.<"?#P,<?mW1&jZ4Z_,g.2ofR_NNdVSF+"I)YZ(2ifo)-FH$+4TD)*]8q/1\1
%gEPN[?N7Pbq"`SHk=4q'o?#DQ\!6\61RVC%Y)\Ma\0(_egmgSn]t&D3DKf11S6r_g?af0"WOrm"/5b4eaf13FiluJ'aHi,Qn@abV
%\J+WVo;0]!R/TREq5;Qq3B=ZS5(.^1*W!rT05_;&J=#MIZ0X7o]8p<hh4AXCIkn_7gH,k[UY\+"Z%D_D1'Wf68GEcYrk#*%bra7C
%Ie_mOMd,NSIef')D>CsH55a0s(E`YPl03-W,Ph-6q"nuJhrEnqnULH):]A-/>In@u/8pTZ`=BJS$M)F/hnD+#9iGs?Ap=1FHN2fJ
%fDE>Aom-JSC35M=,kU:leDYK4qac4'+KTrV&sE^"R(Knb,U!-]bE@4K9CC3bhiaIY=`U_&FSE`kiYUW!pQ*L^*67urfQYMgQbSl$
%Mgp8rh=AAP`t4R:b!4':IGbXtTcrgog8On+/Ui2Q/+r_qk=Y.@)CtKam^"tJYHM(>^+fN_JfcSNq`jB#d^.EoX0ZhN\l1obfh];R
%hs$)CYT';We"88ikO&5mqI5DhmgRBG1[%<X&'I2Cmu6%GYUM1(*JZSdnHeQ-s5Kg]m<Eo8aJVO@qqKjgg\0n'<taN+ro.#ZnED[8
%(ZQXQQMc.CSMR'Ml3DRP%rm-+]PmqqX2;4`&K-):3/kI>:Ob1?h8d95rqn_Nf>%5q$Mqn`jY/jeXhK!8BTN/^c0=W0<9pGrQBnqg
%>lD@CL\;d<qUof(mXsVnGCSY\I1fF".GlaU/^eE)Tj`YIiKJNo_^o(QpM%eF6S^?<dQQ%X]JNu!hf_`kNP-_J[kugpO3@Keoo`RG
%9`1GC8aj>,ZICXPj&44"mdL-el%c;*bu`c$6g3KT^5P#aMYCC8iiYC;bDQr]0m$oepFff5+gZE`,^N<ofVPFk:-,6kRb'E(fZT"\
%VM'd$eBbXl:L+3tpbXRtJOg1?VTC`JS\/U$ihj's(IMqDe:(nG>1sZ7/o"$bA(Ej0V^Z3Pfiq)b&SN64Fg78V?>?k^1sX>r<aXNA
%VOX',I@NdcHKR@NC(ULP7+0b.iiL1qrPjVfbEDWh@KUWf3\+.t9gOLRK-^OI))$gE;G9FSLY0>7rQ8FYo/pO$;dO'bZKW(!F(C!j
%9Nl=MbE##7BI^-uoAL.LAE/s?%5Ct_&*=X]9-J5&;S/e*5EITe+^J=DeTZDWVsfq2d^R+9(Ie)GZJ5<]a%^XE(7W_m7dR78dc"TI
%[Jk5o19e?>@/s^Er0#hB;Uk^mS'6fRL_0UEqXf/RES@<7.Z"B"7Z_iI[!,kZ2#1dNk=6?#c^/#*5!HRWp1F'<,-?H#iet;&g9<R#
%S*m8`%a'-ba"Pa4(UribZ7W".$m=k),G0NtU+:U*ET[isgY]`Tg3n'bnb%g8YEuu9]R"RoHkVtnXG#V,[1iJuQ[[CE3]k#XA*6?@
%h^XrJJoGk.Qa)GBio]OsefJpIr8d/8M(r@.X`s6H\@p[GG'(&]TI.k^j-PpEYqU%=]1"2sIOVanV371sbG#OiRYuqbr;>o8ds!&\
%6+QU?e.hi/LTgG"le[ceY%6hcDGJMbS/!^WJ,D'%7D\TWlKEPDEW!`Fc=O<;Ph&oi/VA@YK6hibGp[=g\]E)4/og('hd0P/Is_"N
%Ft*C<#=%(uRYUTJC%=oDVs=!.e8`$]h<_>3h;Io'pU@d/:JPhhStDUFhDu1jOJhFkgSB:EFRe27c*PhmO8f+<rQ4;^o4:j'HKKng
%EZSD)BLPJaamFQ1QWT$tSVYM`emc%Mk'XhVq8VSDL9Z&#4fun'5'_-p[<21:IcP+F&'cf"+.[hg^NsmC;nhr<QYY%KjKm==^Ua=A
%;<c3rld%5fl7_%DkPG$GQhA$.mm$jJT76anBCEq?qnL8O=r*m:alNa#]AJ&3hQ@FM4LTP,Jb[ui8:N71hL5CJMt?,D%kg]Oh_5#d
%IIO5DE:R1]\f:`PHW[G(CZim%p0ZXis8B(qTABAqg>4sI<RXZt;MP&JH#Y+9BD9gQ]D^E6)&II'q7&(.ip?;H^Uqn,?)LTeSh-U(
%21*&ckJQ2k3'4JBoD>A%b8;hg*a!Q?b.h5Zp0Bat4\SM(5MMh/e'[RRVPS/,lOVmb_:JpH[!q_DbN#?RRD$koob3r*eW@H?i66K@
%o(MeJHo87C775V"UZIls/KK4\F(]hXVp[8Zj!(Q#E'=*c+*R\Vq8XTEhHd9'Z6;M!.,.LEq30!9]PN&nI(dZ5rSk_9*.%t&>W9s#
%s4tq+++37KqJgpE*@cEh8`\[@ZSrB-b'kT(I/<3__`P.U$Q"3<`Z?^)o]#N?m9F!_onV"S74R&^iS+T%<g.p]nET>gAq,8/W<1&\
%2AW4GDQ$h=&2<q2HE>c\cX&a6lLO:PR:"J[]W0kE(WU/AQ6#:P++JUHV.BeC\AJh@]tF2M42gNfHger(,/2/".k4E)rn[N38)Km6
%1u4*dlC&n>s&D:=iqrl4jUYGih%q5V-4<L3nqY?P4TEJE7XO0VX_KbVi*jC4fi:Q6O+(5=B#)<6\"9gJfBoa2>PI88fZQ[ro7baA
%.K$-:fap:<AbUB09A\2@SD,^o<B*+l0DUl/jra8soTqFOr6'OIf[#"O-)]#C\pLg8O5_%*IW\bGm5a?""8/8#]t?Nn\p_BSf_7Qn
%\iS%pGi3jbVG">CX%FHnGl8GN^\<N4PM:$TBeOMZ7KPZuJ?,I*78-lQV;#Q'4aT_`q2>!nYASi7\j&9g3B3]B[p'!.8ud>*`VsS^
%eGo@8ijQ#Dh>R#_V'mn]pcI668%EnkrFLEE=H.h/7^>Z>ZLk:*"m_B<m.<mkGXl[R#K/sr@B73L(3R,XJr;J!(Hob^XA_gMP$Jic
%Sg*-a;Z0l6os*$9peiXQh_3U!YEpuKk)L0\B)Rp/glNS,\!Je6h/Wf_\i;lq<NL[kAXCE[n+P@7U-,u-='n#)odJ!bDmPp%*.I'_
%\b+^B:2"3J\XYM.WBYk0dD*hhl*,-8]]T+_Z]!pra%I_^:S5D3oa=30'DngEo8;C&4a6hKl.O2OI]^>gYN9-#Ab13>5;-ogr9ieF
%k1>m:=$QRfe[,&1k)K["MEn,J\S4:BnQkhc'FLB*OIfXeFP0&1,X7s6lGm8n,lYt+Ha0*(%r(7\jmfgS>^AX[mEnuq.=QS5`;;XU
%@:O^8gXLjV`^<5iDa*q\IT-#+h`Z?kOCs%aP?cger8M<K5CWmlheSdWs6&N8Nd<EV//ujOBBii6h(0<;\^fm3CFS.gor"Fur-e`k
%nf(Fpr8!>1j0%$(mVMG)%iTB<C]BB`P[<(8miOH;IjXRIo)-oOIA8as#;kb9qU5DQDHi_(P;#c%V%*?dkUR_(5(3!7d6,26hV#'p
%\=(P5oTSUiY>6emeAUa8IPsLqG/n6^0<UB1mts=.Q#2b80,MQ&s7M#oR4@^frV>".g&Bj+gR1o'rj,]-iP=&>")j7'^Y[k:1@<!R
%*n8F$O8b`8#iVVl`l7&[U-)@9lN92LrP?fkB"Xq)?bq$dJVo*NnB[hO0AlS@5G<j,qW8$$81RKTn_riupjo2B5Q8!&iM6Dbn:(u%
%X3Z:NRQOr\aIgBGrUT"5YC#/EGP[q#PqK7X:X@Prlt9>'jU2$oW5n`5p4tG/5IiQ@:ThqA0Xo./Xi]jBL#/uMUfKS(-B.4Cn_/E%
%Y@H;"oARC^NNI\sGi"<ff/r!EFkE+(fY.=LZ\)%RM=U/i:Y$l'8,l=20Xq;ZUobH"+T)]20Cotub<ocQI>>C_j3B(g]%;_DW]k/%
%oBB12:4Cb2-Y)PLHZ<3TMS%=FfJ>1!:>Om1[Uc+?3dJI<H^ZYGD+p=*?&MM..Icrg'H$-R+O/X4@I#^"O5/3OQd%Kkhn35)NOQ+*
%56(>uF0U+!IeD5)hA<T4iQgW4`S5h4]J\m?ia:L-Q\c26le3;Pq9dHNZZlnZqu';rm(Wbn]iC^io\B1B:VOR"GjXAXg=CWCcm-lq
%;L<o,Q5M,@CZk6&o0I*O;RagA[s6#ug2O`>6Td=l2c!JXXrN\XebnRnA:M:rlM.[\EOPo4hBA#:YEjV&o\uppLX2]MPR<3:k==,_
%%kmYMo'50E?WXhA#BW?_qp$,KOlu>s^+tbQ:S,XnF*;ZujL^12pruIDlMBA[8QiO#B4WGDqs1,Xs1E`..l#m6Hf^5Wo%rsL3mSk1
%/.Hgro#^k]fd^e9gc]eUa\DCFpJbO'5f]3-_I%Gth1+%iF)u_VEV#?qB.<3`7Vt9;ci'&t%Q<P9qFTM)K=f:sbR)1cmX+3h'\dqP
%TA84nl!mdrCF@a,F8F>Ui7,^no:>8^'Z)RgRARTf4;A+'Q`]Rb9J3MD?JJIL)7?g"q^+,A350e8_QDGc?*E<W@<B<.-M;Kr.=CRk
%?;H5En*Rkn-MbdOq%(+3Y*VMW^'h+@Z][JL1uC)Wm.f^Fm\fuYlgFeh96\/2c1p2Fb"JNe>$[V!SUC%g$7F`',aA+F*LjE^=MbT/
%2F?\Pm@IV\i*BXFhKS\9UZ[P3J+$9=G/(WOmjcA3`odW'^45=Z]GA8peTBJ,q:3IOdR2@Lh1XjWPI%=UY:d;eSD35,`kLLTZ0M4Q
%F9!*7c2ISh>KsIo8/9FMipM*e)X<mYfnSGDd*EL'5Wg'R[^9+eHhT\;k.VFkq$_E"4V,Xk3SE;[IXUal@?i51d`^pc3j_0M_<5PO
%H.g>VQJB0W5.Z#gH@(*_F`3\8W*_bS-<VCq%B[BT`o@POJ%s'7bE7;9Cm)ug*L9lS4<L-]rO";,cl*L'Z>&/Kd]pHc1Y8RcLYBAA
%L?Il<i9$a!PNtY.IrTI5B'Ha1Hq:HOpKf))c_$S3m,dUnqfOmK*KM86s7#D='6&4pf#F]T2]hqcag3A,5O>!qQd6mr./r5RZTij.
%kne:*^.WZ%8U4N'dgnZtQ6'=:GbFOhO#?LS*A+@9\iY7g,F]c[03fopHe5GaKN/]in(;Q%<Lhl<Ps>#Gd'NVPT^pa[\#3<N@<6GG
%@3a*(0A"nCRm")uhuBYAT8-nA2Dna*b-E+<rUM<f(:\hiAaQq`g`5\GJ%g=0iGu3)41a"b:dK,o;J)t)0s/[+-5:Qs69>$H\?i\K
%a]!s/ZS_F%5Iri%(uot<MYSdE5Tq9Np_BO#2ns+b`@CiMQgn]!H/&AVh04">Ot>k3pKIM;.(lIj_23c\KCBucP40S*+O'JJrq/2o
%ZZ'r3pR+&5E0&D$X>H&J^PFAFNn[p"s4*c,F<BQ3AbF;[gu!S*G+Lj`3ihlP*\XSSm::UQRpV["Q9K!46LpPTO2hDK<68j:9]*+7
%,o`&RXX$;P).qW3)5ik&-c2.e?CPTMh7qL_rF)"3AkSW.s8JKr4>-oVG[4@)E>P]1XX]UaQ+tZF_;@WOM/=ij_$BiQ^G^522dO9*
%7h(M,;mXTa3(k]@cEMqcB_U@HnA431?dg[GHi^uOaOd5\7W.^2Km%>t%)_j_8A=A(:"[5d[hU9Ol>$!7!hVS0r7Zg8ZEBkgLG*!#
%AZ]ScMd$3Gq/:7t3]5eP6hlq..N-'K?j@bT*4_M0NOSBO[=qril'M0,*jHLE_]MTFo!?/uI&4I&J2UPV4*ScA-GA'hnq4TCV$B`G
%RY_pf4XcP4]L"@fW9VY%SKj=nH?nGX]9aSk_iMgUQ#de]KCD:=7eeFJoL&aEh8[Z`,;5:,_u:Kb?eSc`n%YD4q_fV@L&"\c#4<T.
%1_WE7AoD\H;'pePcdi'pRg9p$fUA0OoI_1uj2G$q(:[k\,-QF@U_/ALHNCS?.R$/q@XdZ[%AS_/cYt1;581+0V+K5V2lb6<%<C._
%rBjkoO)CK0FN>dAMVQ&4qif?^04JYYqTbR)G3,(O,!>EDhXBXL$]R2C>=!\TR4hYrE\?5A\+TBTM.Z;n=Y:3=WHg$2p8,qY0(FBH
%<.*q.k#N8aLZ.-?LDGRfj,<B8MTM\p+fmX#M,e:S*T<\?b$-eG1rd-H-2fO>,ST.kf0uVtnVPEC,M2cW:H=TqjXTKqlTaUPHeS\a
%q<CTW[+_:;+6?c^?#l?8$H].*(ar!i2\G]L0[KP1=q;(]=t@DBV7#/>0Jd&kbtkW+[gi*F.G$gd"$rtB$$6=q=Y@`Z$J(I*haEn4
%Ao&M@^t9cW1Y,FkQ!oW3#g0bR1!E@#:Q>];"m5@KXGl?9m[`m&2(qgM.UD7I>-GPnheX2)>%R`"4+X4]Z1CXh]G)L/%6>B-ZPR4i
%IcHa!o=b7c:r%sZZ&438i!Qen`dP1@A3B+rRdh.<6,m!4-5h=1WQbRWkuT2k#^D``F<4rE[q^o]G060aCjS5g**P*B3MQaIp=l/U
%KmVZ3C8*75>"6^ji>2%'7#"&C',,G1'IFkS<1'.uCs"#(S<$6r]`s[HZop3ZkLRphWp^6@kLamEHt?etMLgE*PkpDQk4ZgX@B*'_
%cdW0qS8%'-,1C#i>"nh_C1Zhar\X69*1PnBh7Jui4Ooa'k+sO&R6eIA?q,>Z4#7bn9?[gK*4'Ui3T_o@s3@\r_(>1Am(>-$5G,8;
%q>S3FgEasCB@>qL\n?4=X"SV5=p$k,Mj5mg,?B@G^VM[+kqu_MWjaUi2%j5<X`7E,C/&O5hlWk\]j]/J'h)oPQX<ohh-@p&)3mX+
%I9BcW>PiTmHI'Gr?%$Fp4u\jAlei5<Sj`.^Fl%]VhJA[QF]2<3\BNg[P&8dieo#Pd2`$q953UW#%68(]KR;5r3<c$LII$?<d?hqm
%QDhP^fSnQO`Kk;D)-a)HLtSfaRV6KN`WESqS&]++BcF+T?gVcKj([Pup<"1Y@2[h"Ssn>U:46ld<c07-QTm@8qjt)L3u]W_g*NWh
%B%OW?n+M!G_q7llkHa7R4^]r?3g.2Eho4rQ?oVrjL>#XS5AY\R:WgYEIIXU)fJi\Dd(r*[0j)HS]XoaUHj<a)_;4fd%XG8T*77u'
%glZ+p%7bSBRlSX1R)dTNJdKK2FarS!=t\YPUY!'CG^Y%?3]<2\S!+`_7AjoUW]>^W[uF<cX:]*Tauqd!F57;K34!Y;kUbUC<mISk
%+*0WqZ3+a3SVteX=F,(lN)%`F#^AB_kH5J;"id5tQe:^0?'/<dlUhrEU+9SbN*OnNr5R9T]/Qmj_lW3naS.mM>:KU=g+O'tkRChf
%(ul*$Zobaua^KD[<Sb:9RE1r2$pN+q>W&&>DRViPNs_A>H^SDUpHD`eH<eS^ckB%IWZJ!9ML064G!Spth:R"Q_8XK:=%IHr05IHK
%RE(f,N9>Vg8m[;CmdgXRO#F2s1Z#f(3H=$&$21X5k$7YopZq@5([uFr]9r=6^&.KoUX.tgp3aNaIeH3Neq).l@4/cn^TmERb'.&s
%hn!n(?H[*1\$sp'=N9fY[O(m"-@b?B#1ek7oI_ro=,&f+s1VPLr50<om=-'+3W\QH\Hi<XEF1s;#P<Oqqq_VnQ]lQb/Tlc_9=Ih0
%/;!Oti%TReK>-DV3@Hrob@"ef_.A5;[_!2hQ_O1]^Y$[4Q\EsFA1\)7:nV,:K[c,Y$De6CVbe\P(IAY7_G^`i;$t!<T*DCOX[cNf
%AQuAt;Er&7p/N'dSf/Vm:_P*t"UfC3]Xn<iE87QC`5NXd)40fuAZiFAG](<nf%_`VD=`e^\^'e$Q]I32dn@EBHFi;.]YQ-`)E/B0
%]ju0j*i#tQAC9umUnj?C:fWY^Tr6QW_J<kcMUkOVKk83L533Gf;8jt"mX8.+c>2<qj*UYug^3\FjBHm'P1EL3;Dse\8?G_-,7O`R
%<dOLe("qnIhT.2@P*7]D:d]-O)pe(_l!DO$g:^GTG"#Q]4JN<S\Em/a:2.cEKehAb]uk5G_5:K%W6njfUD?d";146jDUj"enZ(7l
%$!uZ4`N#5G[PqAA4c=&.@lmf,)YJm!oD;]NS?Xj-^2C78qseJ9Si:-`//,9)Qg9Hpb%;iPiS$_h7^!LDFUG:m^<^tsXP5'j'<0T/
%ZoT/gUOBQ9M7ub,?=JJTqc#-7*I2,'rRimugpS#/Rt;p(rRp0g*]"#6pheEJO$>jn;$GsKq8R[n#c&4pn_7pLVQqXNDu(?dEO4Kn
%[7$sV%4-*bE$ZE?#Ep"</N,Z]e6D-'s)G3>8,#rq1[Rm.=''m;F@@"L8Zd/\9,9JQ2YiT]g6.rmcj.aiK"#&0V=UO.Wj1Ft;NjDT
%D:n:e,=AHcmYjIY(c6Q0R;QhIb)LMa)5!gjWn3GPFW!>*pV,Hr>b0JP-5)b]RG78+Po3Qp].=Wh`3N9+IEL\8\%#,?k89Mkmlo&U
%a(iRmcDYUs\[u!*SpU&]Vg3cu(shLhcYL8f3)&F@[Ht?[3UjM3pt4^Z1n(aW@FcL?MseaJXr)M1C)8$FdD1%QpEd;[]e@?rGs(Js
%e8X[/d]EEB>4uo/],[Fj;rqV,cBhn%&a%ZB9P:iGS:8-<8@I:-[qOBl"O!3(`HYkfb+nXfYIpbLXsu[%gT((=m\0KiJ(iMAhW.L3
%1M^d.U:D;K9PrdQ4`ttIb1:Ka=/FbS6T=\IgK(![)EV/_CoBO24B4M5-=cH>RVMF@g8p%bQ0*0Q)77&o[]PgB/;@@>NC9nVD+5:X
%kpaW[bHlH(4`R?+4.]tMKOm4@FOKG:W"*TU4,Km(jBV0HY2XLmm\Yp5nD<nK=72&SCN/0XjOFL45G(&kB;M)QVE__BGY<BUO5+:/
%I4P`SVoR36+Pue0L.jPfQp5])K2'#!oR]h?VAHcP%sk?]UXWL,QcbNEQYfM5k$dg;=1kZu@&oQAcFZ*uH$D:>]m.Kni?bnk(,&IE
%qdJBR+*@B=M0>fsOJk)6LO?KfLt&KU6$q+a%^t4[IAl#jgT\o+Z/Zm\lXIRmP5Wk];/OBl7rRcbcWDa&d^U3B8<0%98:;]ChM7:e
%kO($,9G53Ai._*9dVqBG(V;O\'5FShX;ZD#c^'LVFtRr$Oa4&DI[mTZ9*e3ujm"Q"g?ACi?8p>\CnWkUUIEqaQ,W06dpGSn20F1o
%i5T]d]=7Q>,2.9gHf?i8Q-6;?<bWB%>n'/;D!b;UqfO&0FOmca4M'M)MJZ:/4MP^`5,J>']W3]*l+N;L>ITP&*,cM,?@2S9<O+]I
%A"k!ic([f+3O>Pn08jg4YPS)ss8Ds9PEWL8bH_&?i*j>_n++D67C:E#)HDb*hN5^Sb19Y/X<NQ=ecSR!?FkM&7a]l[0kqfA?(<V>
%)paOl.br6LMregu*3Keg:I3A86@Q>Hi-C*EV4,SKk<;#r[-=bX+56DX.'U_V<4)Y09H`1kS<(Bt-g$<Yn,*@*=U?>eg[*ZG4C'[q
%%-)QD;Y,?VB9ui5Ztp*t>1n.']Esq9Y&[uu(b1q1?WJ2;.,%dHB=o-]5AO%_Sq>feeA'Z_G""Rtb9S8XAp`lEfI*T-=t($u`skSr
%i^4q-FA5!%<>s]2Jr5\1KU%>ZhK\E6`J8scZG4ftoO:&V`F[dI@u0hRnoa3^^bWo_/I]@`c)t^nV-SH7lHN;4rV`a_rPH69FHPf6
%_Rn`qcgX"+)0^6\JRo,%1,$t=K/)V5'[6FA_NX%sT:.2>g_E=cSApX@]dKNS#O+WAoIfenI9ON9k<!:99JW9Tg@[qN[\?1Fog]6h
%7XK:TL?YC2F/NX0F!RbI"K*nul[Qa8Z'@n?&)MQC@%0HFe6h.3\M$)pc03Rq)sSs.\c!g:Ik$D?9?3#BYk<,LHE#kN`sm8+OhqN"
%%<A9#9B`FMlcg4r7m`:Ma6$IfCR@1X3A_)T2K2E2NKoO>Nl(BeW"25Wat\+U5>Ehrl5SQsIE@UBjeK7Zg94)8FW1'GfbRXelR-&H
%4aIP."'^i1)dbU596&X#6a8br*P&UF<lL71NuThgZoonD>gHG/6N*i=_tY:]\;Z@_T#`IY$S91*XEfqUn\m:]o2E#[0%0_JJ,4lE
%`o$B5d,7":(QYKm]Q#`eR1j^PdE(PEp5)?36nune8P7$7TNAF<,,'3-JTJlsfP<%$2Mk!Dc\1+VAJ!r.q/rVpNE;`,#o2@HrP$IH
%PR"<o1hSqkp#^)#f;nUR=`<j4Wdq8EmVOXkoT6e6\N-1i7$[L=>Fu\og!jSPRl^uEIVhaskn.>QbhMmeijMC58dT-bA^\npYFQBr
%$CShU4)9]3Ep&0J8X8FB)'nG=qiYa^kp&-j[slHq,HIb^E@M*J^=^8m0pZ(-/P'=FP@`p!U:<<f-R-gK<GrZHLX4cR+O(;F+/OIr
%AZuUoJDtopY((!4TrsA2DWf$'R`Y5XoRMpINU_2XB-G8a:6\S8SCg]]lH]WokZft6P!P9`#*&P]dPni>Q)`P_Ik0fLVdqfORM8_e
%.h5%:eEH:kGg\8+OA.@h=Q_fW]IuRDcS]RE%n'f"8\?!DN9nD9L:(bn;IX)_iQh9U]c[+\iKJb`h,joNk)5&)+*\F?n(*]PHoICK
%lO5mVH&jPL47=[T%QiR`VX#:&ZEc,aGUOP5ST_iI$#nElnn.XBUGomV;D:H/QodY_R(.1BhCPk.M0:]4c_T+F*@qlsZ0C\5,Fc&i
%IEQ8nEqg+IJ'cW]`Ok`O!\opNT=Sb"UT4O31?R$NbDl8%?`fq&=6L9^qTKo.Mt`2G!r0)l5B_9H1_jXL<I/S(=!>NJ1a]g"kpdt.
%1RVkd1F!h>#J:t-Bh<pf=-"h]*>V_=k@1q;_J47bp,@a]?t?AA-@TV%`FH2=Y+!>NH#85(A$AVT-l-lT9Je>HaAoO8WHUf.2p2Lt
%E`le&.'L[tTgj(n>uXM)#9fq4]<C7e(2.hA/iX`\Nh:4AnK;nJ8:bN>pXig^Eg?Qb=4:t+QGM\tq#Z3$b]tHmGj6[@CjK((6K0Fo
%i@ChbT*Ve-130/.=7K&qApI!gfi1Ou@BCg<GGfEE0[A+p0dEg_b"Z]4O=<ttkV@fV8lh]$YL*`,2rpehDk&A7Zc1%&3)tU4E4Qr6
%>qh7n%QEV&DiT3QZ]kX/RjKc:^l?jb1Apa=.g&2sdIhKF*BPWfL!#fV-UoFR(Q::tNQak:,J>c.PnW??a0gk1\e/%1-OZNta/.8J
%'aU@@`+`<fQMF8GFX//T`NNDA/3^?@rZPdPk:T7>poqMO2i!PKOfMHJO>&HOH0\uBB_)qA[BG/)i"ru$P89=<Ng/!A';oF*E'bk=
%KCh7*@-m#460c*a=9e#lP-3uUY(fm*3q)2=9rI'E[5<=jXlF;ThHZE]8q`)`UpC$A7a;3YB!\H<R;4/,O?K48Q>Z;HoIojUUrd08
%oa&]nUI%mcU-bEXn9Ypu\,SMW0R?WO`6%E%Qmt?mpe%S^`YQ>[A8LC;%k.D:#a<.sCmFLOE1"eFPT\TQkD,$_G&q!PX?e4nL_FgE
%DsWE=&&2%`Wg;/4;'n6JH;6?mUd;c(i:OEp?MXcr6:^YFaW$_%M"h7jaLiUrML7R5!DcRTgVF1aQWJaVAR'u=oT`aT9T&n%Mjk4h
%9lIIu/!u#pA0'?9cj7`F?N@#lbn82_n!i0%KWR#m]SVS)V?,!P!I>#h/#d(PKSeR"mAa6b_pUIM@).4\;;*e3SkpsB@)>ES::tX5
%d73\PXOgOF<,/e"G6*`RF(Z2\6lnr<?%ce+\"#1.@,?oL/]69TD3m>Rp>)lA3EnW^q/DVSqcic:\b"G])hf,=s,^>4O^Y;n.p[Ym
%4.tPb"KD4r@BUKX,aTKE&Z:W%l2#U;"6LHOX%BS67jo48b;(@&MkD^Y#g\am7JISMr&Z&$+!H$;beF>PNsM@oNb7Jebf:M"8)Kj$
%(WC7/N-g9*(MecadG*Z7aoPFM'<db8`Jian'sC_.BD_]ZpC*SNlgNt%kgM.eQ0T@-V!J.OnI13SYl%5HFYnUVc6]Cp*_&%`C-##V
%9Z;M>4!,lu(?6Uq6GBgJ;^,4?j'.OtBaR)$5SnNCF3F8;X+:sH3D[m@;ttYNTX+LdH/<s,j/FXU)+/`.IIZYs_!Ehac>_=.&\?0A
%^kJ4aE8GbQk1-l'<>L?UPq<^U)t759T%Fk(eioL(KY<5T,aYQTMOeF[]VtsfHZ4M)A1f;POt>d7m?jZBoR\3Ta!Y/N'Eb6@-_6=5
%TPTjiG7;o'L_hN.IM8gYDVG'`j$ZTf&LlR@_$Xeu8\`,E$``EMm-#1IN7aFlZl"eL)>2`OO"pB[+%%OC`'P9RFu.kn#cC6s!F^LJ
%GV8Aap#Q-tC"!H;>lEi^O(p*k@A7Z/["=Hdl?R67o]kq_41$s>4>T/oU's\KA?D:#i-YKIHQe0uD50f>cb+4UKe+;OY>]^VGm=[S
%$g>i7X-N+f9RXWsq_!*Brp2iG&:.Jlgr8V"CRlUH!aA41ViW+]m&SA^^Tm-nPKUPd(l74l=%]4kifh<g).-'kA\6j,jGTu3.l5HS
%N5F0CE].DY%=Ma.(W;smV][c9q7m6U2'Z7MT!=]$6F`U"M]U?fZalWBLZkE>I),\hhFgKO4P!:Z'u=,(GA3[NSt':k@.Tn99!n!_
%4]T=#Z2&9j1.!D1jk>1MdNkct)@TnO*Qqr0GR.FYX(^u5=egO`/0\X6^[)>L.W_*LgfO6:7B4W]F:fMGh*'>_om5*=9u"c=4aq^j
%S;i7+=uD\=C)_HFWlariR%+&%l!_gM_eM^a7L[/8&8=+`N&,R6.Y,dcV<[VVO?e\!Bu;0G[O.G4@9<%j-FprL&ak<5HIJ7-nVJU=
%"b<9I7ZP%ngOOGI"L@B%QYLinFEu7r5Ed/jYYp(0]&e[/]h_=\@^4$$"6&e4"F4Mb0:GX_,P,_m5(Oi!l1:p/K+Y2Gn<Hc0iN%lu
%LE_qaR8qB\=ds9)^j@;h#'HseN)\*b\@Ac'<k4F(9BUOmA-d5KT\ZE@e*0+6p.Q42TCRB73SKbl'=I&h(L<j'_omffAnae//ji;<
%O[pFaE_cakVBsl9g6r_PnG?(89P<s"Qgf0UD`C8IM5CNOLWp&-3SoWabs?iS5$"iOQ#O=%La4nqaO*tFPi%[\oNG7^/[54*#L*^X
%;LM/*A`b_1<JBQA*Qpq`XfCu'5Q)4^/g9EmKB]:_UP`3k=1Z<]`D`UMUdOPqaZ&cr2KbL(F6?an-=Kin!d$f9;@bA9JDMdM`ai!-
%^L4^9L^'6+Of^1gk\mkEIHd?dL!DXQP9-%6P1u+5\1mGLT+qOrVL>dA7,o3ggf[$>57@c-(\I:(bmLm9SgK[bgWA`eZC#&_MoQ?6
%LdGT)FV_M\ENQ_+WI.;i6X/d=VQOV*.E&.NX'Y-<b9,jHIBbr0oZP@c8mM4-YK,I(D:c^Uf+Yh_>/I>A,Xiki<l9ijW,dGW]6H8!
%QZVb0;sJCTpcC[I"r9./n-R;.'pDo.>odaY(M8GGX),ss2=!e[KaSL2MLpK*5A$[&582:?G(aeejYK*ZiG?H@;?)'oYKaYeNDV6#
%(:dOKj?Mo=?ZXQdl=7K-2\+)b$/jk_RiW(">]TX$>+A!\lA]@!UfXq&ROSOan^39=Mu-ZTXLA^hZr'&-X>mEYd>[k<hN`o:(/6K6
%)ca=N6OrMV]jJf.qgs[NQrMA[HY8p1aX/XTBR6M?bNSVAlrf\GOTE#MG+BjU52_EnACP+5$ir)g:>'M(#apq6Rq>PW&c$7--YPol
%o*i<O%e,o5&>R*;[F_PHm]0p'bDP/>MIfhj0.bAE.h-.-R-88"(6`BY#4F@_C^XOkp%!@RWabV_pU8DkPhgXQI*Rm85#:+llO@'u
%TW`CmpImD"`&iZ[=m\_CrP@-i[%CjshpM15oL?s,F41Wj0-r0K)T?#]aLKhZm]sU)^O\"[lm>+Tn"$leEmtSMQ+qUel:<XY(GPFU
%4$.$m94JriZKa;cW!uLAAjbLp@1dPA$Q7"P?C!+oM3/?*M.u>E6`U/HNjg@><lT24At6%OO:-;O%<,"qbUMYD9ihFelO#m3-[Rhl
%)nO8AB)"\B7)O>orriH1J/u/%(rYo=+]9b%dcIq6'.WK^NU\NKpdXBnT^hHd><FLL;5cmiF2&*p3HbT)l9SK[#%C`A5:rF#jfF1g
%EWONO:,Oe]`;4F3T=9^Z-Kkc&IDVc9CcZ<t[P'5h;n)#F1q>%3m$PArYXB>AEjcE1OP"[>W^!,mGe55t+f4=2"g?Zj4u]a]l[idr
%,q8^`kG>r3[9nMie"&B/?9W84N_FL]Q?c]8f?SXSRZ3qG^5`S$V_=oK>eP5E<n?o?P?qn1KJuud<+oQ=3N2,c2=%5GA+tN4+rMi`
%NNSDkSV9HgQO7'0l)kI>F]&AJ(bj=D]Urd,,(b`BD"RQRjG\5@n<uHm0WON"K%lh>pVr1IZgje`*Z^l/E'0aP(BO*%!35Hk($2RL
%DuT0,'0nuAmX5$eZ1HaR+l3JfE(S0rV*,kok*Ru)+-bpZ@>1j7AkXN()YG2`F#j6f9qOS:R0nQOqZJp>Y06H@4#7sHM'Ne95ouEo
%8pVq_0j7)R)uERl>Y5Ec7`/i*msD+WAUnQ.miLmt`h^&`8,^^Z:"nq,BlM/+oF<\J$O'Ef0qDDo-HkkJec:ZXaCGqM^2NuT+Va,?
%X'0Ch240sibJeiSH9Uk;RFL:e\9PUGC>QDUOIcrt_gBG#lSPe3*n!rTBD-%t`+o[&rmj*q#-HQ$VdDIb)E7"XB;;r*M^O>e^QPa5
%e*tXM6bj,L,ao`Kl(%)*=iG_GU[M;\U*kpU-6,SZ?;1q&8hnpf9QZ?>i32_MRPd7@+$W6kSb9#q9##sVeCO6`e(q)/kt^19Mp>G2
%O^"*k<uN?QV+gX<aYTN:A=Xda*[t7cGim-u?h8[uHrJA[!o(1KU]^AV1b!U$^pONnTDVXWRQI_N?L6R"@?'(S%AMYNJu=I<Z,e'#
%LgA'kMcl;<Z37&tp4d#CRlQ.*C6f]&<*Z:]qHBN$0BI=s"k<kXqI9J+agR.n$s.b"Y[T?KS2Zu\7^uXu5m<%7)j.5a=NGp;&tPK;
%8Y/bi/s(i3c[PQQWuIoC#dD@E7<Hr1'UT2R6(g76o2?oAajoMB?#'GP).t(dp0P$7:cQpB'\Fk/-kEE+%:&7"%bl:O*j#4%r7pLo
%B<*<Ffp=>g1b_1e+)Ipe9+U%_b9JV%`ic_tO*5Bof`GSB&X.bnqNqhUGl9nhjhlu-cVfoR#sUTsW0<:mAUjC'.9t<I^HDG5"-DS#
%1SH>?k0o`CE#C[3$t"16:MZ+@2D+\bCN:E9H>A'9nodD`l3p-S,41B9FNVbF%/>]Jq&c/CnC?!TptTH2a?5Z-,*V2BP.pe7mBQ-E
%]Wn*ljfr`FgYZhe5Fj2)4[n^_4Q>\)_ZPo5SDLgK)`qhQkATEEgHX.PW,:nuX3p`C=hggWq5]_'cMXM/G3Rc[q3bO64MhRbJ>rk^
%>*dHHg`6p>3sqL$'[;OUeI(iAreS5?MbM/h*PrCV"/Y%!_&bpsOg39Danj._fbDgr@h1fIF>3Dc'T5O"He)u<MJU_ZRtUbC4Use3
%mMA8^A1`ka4)u+0-sfT#k5YkOKjW<l[ntYu-5R!-BTe6l+Ui`a.:LNlcp?p;7?\VKZ1EZHMsTu$6Kq&#O_S.upltK7-BUMcLX()i
%i1iO`j^O$bTd-9\@-LPur0HrLi)FJc!`,k\>u8*`(JGXkS%J#Z#UB3Y9VMG$;6jSM<A_,[2"+!A7o>hS@P=[1*dPiP.@GE]$5"61
%cdA6Qa&g<1-.#;5\s+WUnkWG=.@B]GBtq*j$TgKX)Wq\-&%j/+)Z7Td++4\gdG]8_q:3S`=)OscIDXFA?Dp&Z?Fn"G\n:fNI:kZU
%cc:P62Asa]cU'X8XnJ[Qr;!/'T=cNDoCLFf'gM@S^)I!f#k7f^^un[H6f/M@+((RVq!D&BokHX[D:`]9A,Y#:^:!1R3<ii^jRUXR
%<qpMOU9B3)b5aWJ?\Ml-YHF5`dn)<%+'X"*c[VVebHhXR!M18=PQ:tR\GC!S?2Sl5bAR4W8tAbU1gdHco>"_JA,RcURHu*c.)dL9
%GkA>=IDb/rMaM.-kP9$1j"%[%=-Dj$omcp,/$:^6+)]\_rk?n^fD($N`8=JU$df[OVjCQd9JMFsh^"S*p02\7*T<gFWt@>r.Za$9
%SPSCG'5@H6GV1#tc\?MSh60R,q3;Hin5`K`+#*"I5lZBkOhZ#6F:3&Y*fk\o,b"@bpXdX,@>L[7%k[g/k0&hEJ0(VCETuJ:`8ZhE
%6B2!-3lta5)3=[lj=^G":0lp&5HAgq*qoB4?Q:fpNJk<nK:hi@YQ82RiVu,]JJY)!Is&]d2jS_'nM`tEIS@X?Q`!.KA.KbKafjA_
%8"g7_pLEbDi`1KBqutXp4)40&6joKNkM,tANi8d25JJq*Q[Z=i=CcgL'?Y6ZOY7[]SmuIX#?b@S?MEh2NPb`2FhQM\;6[eQ[D,\N
%DjL2K@UC+:qpd.KQ[(:!'mkBA#_(nR0o$95qe7inJ[^OhI<2PKH8;MQ^[bqJM]7tU*\qjfZ[DZg.fSeA1BXZ#CODWK1B>2CH<X[)
%F\;P1De%F,?6,=2iOZ;]s)l'Dk,s0WqrR:!lPHIt$(Q81npiBBGC7ZnYP5:@:HuJprp,"UjOk@4U4#V/^0D^d3;u54oXokkl/D;[
%]%DIKl!M3Lr2=?VOP+WfMq9h70$)u^@R/Mjl.,"iU@8(&J&UQ#-M"cXdR/=UK%F<@N:l?US;$s/L&^.>7C-sGC>gHF^.?m[)Z9Dd
%]<gnNpTnL5n5r:5;B>t05/'pLqsm2DA0FTm^;Cc,s7fdlWrZ9BnQe&T]O;+NNg0INPTeQ'asd,qjL!5!_*<GK_-1sYF8+9>pFVO;
%_qmWG41QF2l_c=A&4X;2-9[.Es&_ntrWHeh'3LnS..`UVrsGbS$8R"e<DER[L)-1W<"V@aS0Vn>6+"/f0o`YQ@k)Cg`]0:YOPS\D
%E*g+7=RKPkXT4u"U$XK@ppdWkZ,^$;J&92$T77,M:u`\]$OEgRA5W2Ad&7.[9pVK86Q0KnKH133U;9&DCIpf.Q@>'K''p21$A5E*
%6Jp7NOuXb/LPq=kWS"-R=ddaC)m"VN`1X+Y(W0eF1e^ng?-NFT%gXBWM_cb$?K,'B)eT23"U(F+E#]AM"EKW'JtaacQ2n+.5uu>H
%fJt&7AKEo(MJ0h[nDE2sJ%uOR<EA@.;)#!-24d_9R6`N=Jn!2:'b83Z^i10f"T+>br1GL5#dg'VkbAOlO>mV:6)l(J!k/mc-W_lB
%<`g+oOf/5Br<G@L"lG$,.`ehu/]b8/0FKJ2$60G#Pm"C<"h4t8KKWL8:^#hk-Nt4c5&kg<3A2O+3AJThXS@:fZenS/?4L^4klTa2
%0dmAsaKcKo'jcCk5unRTW^I1E-o_Eq$uCR[#7lto3Rf=.a*e4Q*uL>5-@=m3+t@]PZ:aqBPXC<8ou_5Q,S8Nq).@_qZREV2N[9@b
%6/[8aUk[YNEs!j2;-=L,c,2uADNXk?'>]K>&.CiIH4:&\U]B3`a32t?5W#c8OC2s4,J"?ud;crn8J<L6TVlIc`1SYS11^\DJp%$q
%RN@?RBGF[<<@k-MgCn9'((F9j.ursaJmkZQZq-HjHUNN22"JG0Cf*&0-h'Dr&9&cR.s+Pscl9U/$HmQ,8F$s\ob9jY.Zt);_IlF2
%aT"gNB7UQ^!HLIp`;`1PM5:<Z'<mll2[Cb)W@E-a97Z%fm6okfE'[MqO&NPu/]m7LIfr%*2$#/gK;qIQ$iGS'4;X4sfTcYU`t9O"
%Q/k61/9da!TVIq]gBK)3'_(#,8(Be_'f6Z$X9#eA"o3jW8e1m'7?bL"W.O$e:b_Z9!KWI%I?l1Ir%=AI+gsr"cbp4`Pt^I1!#,%u
%eIh2hJ/2*077Jp8X(n.IO8pV[:P',HQNIm'!^Yha2mTG-&MX9"**Amr'a*2@a5Gu@r[Gidp)LmOYZsabgI-t[=^m'2aNL<*d0PIN
%ai'r?$]%lhJE<c!65p%r-B<k:JBAL?!d>uA`X3]s2=)WA4&s(^Y`GYi+'B)*IOSg#&EH75-j:bS!Q0S52.@t#:^$6CbK1qc*=c?f
%LZ:d1V*HRAN#c#1!J>rQ,jH3hIY0/g=WnmrWI8eB$">U#9Y<ej&p\4;aBJff(_A;3+!_%:*k$S$,n(*%,/=B?q1*Wg,pnM@O&N;L
%dobUW^^O1B1>NY[%=raLoK^b4*!86(dNU3W.qk&J_=VfM-51i9UElkHO[Bp-UE>fHDB,>a=sVYb_t+l<dHJP;O_bUR'RDa#NZo#V
%%(&[K,=Mea0:?Vf-(]'R;1kO($463DIRQ=Z?lg=B&?Q#o7D_8!A"uR!K!40YQ38/Z)_@$+MWG#$7S+I7LQ4sbM&&>uJX+:t<>G!3
%VpRtnLD@XU+bkep7?Iha$n[oU&k2&<J8%Ua'FA4ZDo!3W8/^52"q$>YB8c@P-n^h,:da:%b`Kgo&e>NYQ(4b&MW9P_Q1-;MBc'Hu
%KLdJS/RtjadOjCZAW>n:pgP?I)^)p0Y7DP(YDU>>>S,\r65<G]_.>li87J'Ep]M0EN7/&2?md^"]em52%1jsHU0@]nY"6>q.X6(,
%TTBL!MaV2U1tXgG&+G+HE\o\lRP55;+Y!kmJO:T2^47BK[#68h"=erU8eMI/(t2?'^]63F.'a,;AO-UK!m!j,EE"[5JTVR$JgOkR
%`*WP$,PqT8!B\)Q%"o0'!/-!&TI,J5D]oksN=ot6Zp8^i>6%;^$]'i^27Of4A-le."I->]]emM3aT@D)jcug03<e1_lkC/coHFY!
%3^abbKF--1.0sf*6ifCR2rKlZ%29:rb42uSKrC#DqA/RA4EY6CJgK';.OjAR=8_,A.D#%9b!Eh/K\dD(WJWsU,,rLJW^n[+'(CVU
%(U8MnX]#Om^_[%8((!#(093_JJg*dVd6o)pJ/SqA#[:gh-6sJ+OGT!M=<OJT(>pW;JJWh[88*f$B.5TIWAW^^.!'uY=B#n>SZO/7
%ljL.0>SGI;'+`?>,8N*[Pg>=2>!OKQMFV7.KWRWY74AM7Ym"*+1&n(>assnOT"o3%fVg>`E*(KkbWN[S"H3^r#iJQ[&p=h35bs]>
%4:RMW2uuOJP(\NXPlj?4E*A](:_Y:-bFgQj>@HhEH5Fs<iH,]&fYO9'!C%]e68-J>oP#:N(\.T5,=iE5j'a0?@,OW_"p5*h`lS6+
%'S-Mn1eOK;PCo)$1l412Q5V"9Q@n*]6QXVXoP=T&6P8i%DmG1S]EB/s.S4"gkf6Z^\j7'%F@%+k'"X_HkV.-j8-a.Xcj8Ss7@4DJ
%2746N]SnGD8pgaE&cb0e*sdEsU@uGJqLo\[,!HS9#DcYPn#!D/;hJ^*&holL5Wk<jM&Jc1:hRS1)KhW6nU[43*Bc9k/^jsI4D@4P
%UlcM6'3W17Gip(aFLG7J21m^s-(eiU6a[K7/l2Yi@0k^iio-Sd,1'c&L.E=@1kMqW9R`piL$aa#`J[6a/>L\BkW")WZ>B(s;1TY6
%+t;%?!6chN=BV:j+eFNO1/0aj>-JBm"Q#Nc6(!HqLa-o,n<d.TZ?Cm^$;9K@^]Yhn`,h&9O1I[XU$d]B(bbs2;+^<Z3iV,!hFJl)
%)tFbe<s4]g;l!)rdBeA\7ba^C!R<sI$k?>Dh^f?E`0hkoOV@hIO-.igne_E2On'IL13F2%_\*Or@N+/@,&!B24U=`S.Yuc&NpHiN
%"T7E+]GJGH@VQ+A&)a>%dj,A"0h;K8McM1')WN758@sPr==(dOi<bUa('LNj&k?t4*/FUrPm91*.OJCiD+>]sPOo2-`Z`=J6@^'c
%oGiRt-s;\X-IP/=/X5]l+b3"Kr!>LA$=s%0Vuf:"<2)RlDb\&5&&on,1PUu[N![ruT#<FLM+jH\>._Y@fchgP5U7$5_KN`>'DM[D
%P+tLDR`W/G[a#L+L2J8g5^HqW!C.L;?VMdHM8Tl9[$*HgR0o+M>"G6X(P!RD:='(Y_[a7g:aDj+"jpd+77nONL3Lo7^Z_O;U4Ji^
%Y'HIV@1uJ6,3%X&)a$h!E![<OGTQdAb!J<Dc=j#C@YP7/U/<3nSphCtieb<aej_nn/Y<Kf%*kt>s(IN]Ol^H`'"#/3QFdW@duL%l
%ajBh/eI<+-bG6[VJdjC@N0=p-O9C]uJZhhdU^%_Z#pa$^cB<b%kQV0@Q<#\Z&4sim6SD4R74])B)#$>`bqqlo=H9=M'3Goc6KB?G
%5c1g,Ce$bgL)*1]$4#8fk"O23J\YJ"bAMDf?p)d+4KXp#p1Y;l'_L8o\M48=M6K*.\j1!rn/T^E^loCVX,-+26r4LhZN,*c(q`&;
%L5PAfA3,5H8I")Eqq:WAW14,EK=$>G'uE;''Gf(>87k!qS<]%."u:@el7da$hP-[r@E_+nUM+1>&7?c?(ABN]<!iC*Y<]TE;31SV
%+/RlQUN"IJ%bKj!^j-@X]S4DeMZcI>\iAV0WXd:j&.j[2_6fkY.SK>)0QL20M08@JYrmm]4"uE)*.Ul$(L$#)N"1"s(o'qm$,'_.
%.eO/Njq+.&qP1\h=HF28b3t3,U%j,EJ9"r;'M1;]TZIQl;Sr\L$e^q7)\i[4O-Nt=3+1;>YS6D&O\(Cdj?_c;cuZ4T6F!J_R"=ca
%rKT+eE?m!l8HOGk?D8PSbF0oC]te+A:aNn^U#eNj1<QDe_pa2@q?)RB$%N_[^mbL5(-N5=!C``*!=8:!kE3!UBkia.#df$_+p8$d
%!b&1!o*BY&SpE")_4lJ4Pc,.fC.0s8*DeqRl5H^TZc$Z]1;+W-R8=b)>$_3Ua4$[hM[3:g!Lb5!!GXom#cp>_cu3A&P(jlHUS(g6
%)aFj48P,"2.0qbK:^D$%/]<_s@\k9g=iF't'uE[A)9hXs!_>oTk-=?l6a0Q%+=SR(OVEt3C32<MJ8d7TRft>[,,MiA#,)p@H^"\q
%KXD2Vas3DgM'7`J^&tHo6%rKu"NgoC8g4i[5oYWINW[`G2)C(fYRV,CZ33@!We\\?<pCr.6USk%6'ITEA:[d(E6BAFfR]ok;BdZS
%+Rl;j"_a$$<]"lC9r#[UE&A1/!'3E)dO8;WZ*]:j;5HmEW(<)rqDq>P:]iGu.ZlWG<`Lh>`?ZH3oGULE<339N[]KL/5=$tZ(+3gY
%LdU9Y*P[a'=JRB,(k[br1YNmE@k]7P)KhJu.7Si;"@XN)(!tQph';Om\;*Q!'#[+?</!:5<MZ(a;>`C!g^kmB7e=M),(`5UGXDeU
%V-o34aQC/aSsP;iMg58V5]Da^)U5Y6WAMspOgabdap)I2W_.hmWj/FO2(YX+<Y]=7&G?B%+H]9U6=U#PIM$6SK67?_G^'>uTKNRW
%=Yhn6G]sF/9Vb"rP:VjX9UeqbZ9]Bh[!]rs3-%Y!kbWE*/D-K:KiA+OU_YK'j9?[i-BUWYN*qT<"hpMI6r2t0j9U*H9bm,mTp?>_
%#L=b0(5aIRK0DoG=rm_C&=kdU'[5<G^a:6L&Iho0f;T<WJmsDo9sB"GSH%Cfp'kR2:W$.07NqaB+Xr3.@)T$Pn@V@tALt3.(31(Q
%<]PEK<Nm)[@>Rf<nV8HZpQ>Pi8u,W`C<-bH`tK]V&I=1P([cr1*#L9N'$2OYo\%:%ML[]<#Yec""G>&DC65cQ?pR^c983;"BW-[[
%hunl<'7dkBqJ$</Gd!CfYnP@s3L)'&YXkUe+c&DuH3(SlnN&Rb1bE:dnUjhh_4mRlWdp39-Mp$EX.]%a)bK24A(h);I&"0D'p0c<
%4B=C#;BCIM+*.JXqT[Sb*iPDfpjuFOZLegn57?Xi)'TjFbeV2-KaNu*OX!*s@hcJaNN!H%3=i^"RQA@X8#mnXDN6T4R]IuYoGaZ!
%6o2s0=4'\XYr[QInKFWq:ft`Kn/'kB=+(#Y@A[``Ufr;oK\@_/$6"KDAg=a'PUY7:^&U-q9%'sJJNKaa.dVjHB+nBT'ZuPI!-!E!
%R5qoUWj.^iW$`l^jKO=,:c"4cL3s9h=BSJB/Q[KJ2&]$R71.@+m0#sVaW55`'Gdp5+Al7=!rh+QR0TmOoggPsF%R0cPmBnDLUBEO
%TVN<";B:M*@5!a!ZPDtNUsuBbA3DJb.C@=%P4AlS;]#g<LFSL.4H0UHE!7LdP[oUp1^Onc&dis$dD.$d4Zf0@;!;C-J>U^1WAO02
%$V-hT(m6?W:MO"ECpF1'%hZd^ENAl!#eERR/&;X@!e9i2$An+O.05q$8PNC-7$<40I)H.$3*Ni0LKS:"(2[0I:O8Zl"$1s]YW"j*
%q5e")#V6;W6crLb#q5t>YqiGrR.d`A9n0s)da?AkUu]da@Xq-81E]b?/7shtYLMbV0I0btkXRo(#B=]279U1T?4&l+5rK+"7jD[P
%>01&DX=(7Sf`p;*eJ*_eP>m-VGf1_^?p:Uo4<P2:^a^fFJ;12J-Yt63.Ub&Bfcnsa=erS6<hg@t'&d=X]OF'j_i#TflIt_[9!2dM
%e')Vs"8MLX>Cl7u74U8Fb4/ekN!TXW5:V.VI8o<QLI%_sJoFgN(24X\VVk]IJ"r1ZCYm`3H(p+MZi[.mOVW1nG_^7-'hg:e)$C.#
%nT^B1Di\ke'lmD,i_A_a2]g$R9r-c9Jh=%OHSLC/R!'h`13]Nr2@IY]K[U']"F04m7CifM][]NRMVObr`C:GX&s,Sa*J/>5:eH8/
%6s66m/XR8e$fNr85pX1k7_D#Y>.@LhAe*l*ZUGU?F$YB,gTY3:CGMl,Jkn9\/PuVgKYC-T@R4)<&JTj"-2EAjn4'g?[8#qcn1Heg
%;\;k??]75T_b#`3BTD8dUF0SdTTmr9,ZEoD00m9a6?`"JW19q3RK=TIMH\6P@a:fVI8I`:YBcFsjo_.<eVH?%)$kW=k]#HUn=&,2
%I#/AZ:3Z[:)I(:`[/9_T^8]d@h2Z^Volb0)c.7AQ$P#3,E&c:2bb'C_5Cl`%4o~>
%AI9_PrivateDataEnd
