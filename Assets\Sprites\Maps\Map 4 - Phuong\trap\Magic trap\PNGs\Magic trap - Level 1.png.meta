fileFormatVersion: 2
guid: 923f58d77bf2a8f4b811cf9c51d32a98
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -460100278702687741
    second: Magic trap - Level 1_0
  - first:
      213: -426917012179599173
    second: Magic trap - Level 1_1
  - first:
      213: 7573963305546292440
    second: Magic trap - Level 1_2
  - first:
      213: 6412681355437025522
    second: Magic trap - Level 1_3
  - first:
      213: -9167168533238919709
    second: Magic trap - Level 1_4
  - first:
      213: 4586176262907357701
    second: Magic trap - Level 1_5
  - first:
      213: 6070332774980758281
    second: Magic trap - Level 1_6
  - first:
      213: 5191687169946619234
    second: Magic trap - Level 1_7
  - first:
      213: -454259800005375333
    second: Magic trap - Level 1_8
  - first:
      213: -2184840807408459363
    second: Magic trap - Level 1_9
  - first:
      213: -6948527762784998565
    second: Magic trap - Level 1_10
  - first:
      213: -8451934468355760499
    second: Magic trap - Level 1_11
  - first:
      213: -987674567612252191
    second: Magic trap - Level 1_12
  - first:
      213: -5629835879513088291
    second: Magic trap - Level 1_13
  - first:
      213: 1302855779236290019
    second: Magic trap - Level 1_14
  - first:
      213: 6279640327849729732
    second: Magic trap - Level 1_15
  - first:
      213: 4155537537240140520
    second: Magic trap - Level 1_16
  - first:
      213: -6984255074401396111
    second: Magic trap - Level 1_17
  - first:
      213: 5711342251540680431
    second: Magic trap - Level 1_18
  - first:
      213: 9089118467127665936
    second: Magic trap - Level 1_19
  - first:
      213: 5720809333102997989
    second: Magic trap - Level 1_20
  - first:
      213: 2905539565914405489
    second: Magic trap - Level 1_21
  - first:
      213: -8200253773685430404
    second: Magic trap - Level 1_22
  - first:
      213: -417673300971509099
    second: Magic trap - Level 1_23
  - first:
      213: -1412264843923703510
    second: Magic trap - Level 1_24
  - first:
      213: -1749211666783074363
    second: Magic trap - Level 1_25
  - first:
      213: 7818897296349164626
    second: Magic trap - Level 1_26
  - first:
      213: 6921091032014570052
    second: Magic trap - Level 1_27
  - first:
      213: -50154475932545363
    second: Magic trap - Level 1_28
  - first:
      213: -8643329721339669100
    second: Magic trap - Level 1_29
  - first:
      213: -358933323145308079
    second: Magic trap - Level 1_30
  - first:
      213: -2615957618762129328
    second: Magic trap - Level 1_31
  - first:
      213: -7305970792914296066
    second: Magic trap - Level 1_32
  - first:
      213: 5504705921192537261
    second: Magic trap - Level 1_33
  - first:
      213: 1172449676093191820
    second: Magic trap - Level 1_34
  - first:
      213: 8182676248035682347
    second: Magic trap - Level 1_35
  - first:
      213: -2484046415013337173
    second: Magic trap - Level 1_36
  - first:
      213: -2626015772518706783
    second: Magic trap - Level 1_37
  - first:
      213: 3717534203334753231
    second: Magic trap - Level 1_38
  - first:
      213: 4995292273891458932
    second: Magic trap - Level 1_39
  - first:
      213: -6244479454398523250
    second: Magic trap - Level 1_40
  - first:
      213: 7095431004451191637
    second: Magic trap - Level 1_41
  - first:
      213: -4827722826678288225
    second: Magic trap - Level 1_42
  - first:
      213: 8245143488162627954
    second: Magic trap - Level 1_43
  - first:
      213: 950181291150141281
    second: Magic trap - Level 1_44
  - first:
      213: -7839317705048823694
    second: Magic trap - Level 1_45
  - first:
      213: -6978868138073686785
    second: Magic trap - Level 1_46
  - first:
      213: 5379878740903064432
    second: Magic trap - Level 1_47
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Magic trap - Level 1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30a3ed6fb356d99f0800000000000000
      internalID: -460100278702687741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_1
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb0c6744d39431af0800000000000000
      internalID: -426917012179599173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_2
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dc1ee07f7f1c1960800000000000000
      internalID: 7573963305546292440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_3
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2f8289887bb6ef850800000000000000
      internalID: 6412681355437025522
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_4
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3edc72ce9cca7c080800000000000000
      internalID: -9167168533238919709
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_5
      rect:
        serializedVersion: 2
        x: 80
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 50aebe3330f55af30800000000000000
      internalID: 4586176262907357701
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_6
      rect:
        serializedVersion: 2
        x: 96
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 90ba3213f772e3450800000000000000
      internalID: 6070332774980758281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_7
      rect:
        serializedVersion: 2
        x: 112
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26d0f9d69f39c0840800000000000000
      internalID: 5191687169946619234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_8
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b9a80f06e1522b9f0800000000000000
      internalID: -454259800005375333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_9
      rect:
        serializedVersion: 2
        x: 144
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9124a78cd2eda1e0800000000000000
      internalID: -2184840807408459363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_10
      rect:
        serializedVersion: 2
        x: 160
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b5ba24b80ded19f90800000000000000
      internalID: -6948527762784998565
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_11
      rect:
        serializedVersion: 2
        x: 176
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8e56cc9162b4ba80800000000000000
      internalID: -8451934468355760499
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_12
      rect:
        serializedVersion: 2
        x: 192
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1efb8c3b6331b42f0800000000000000
      internalID: -987674567612252191
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_13
      rect:
        serializedVersion: 2
        x: 208
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dde9fd1881eced1b0800000000000000
      internalID: -5629835879513088291
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_14
      rect:
        serializedVersion: 2
        x: 224
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ed91faad6ca41210800000000000000
      internalID: 1302855779236290019
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_15
      rect:
        serializedVersion: 2
        x: 240
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ce53b27b93c52750800000000000000
      internalID: 6279640327849729732
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_16
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ea00068f5f6ba930800000000000000
      internalID: 4155537537240140520
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_17
      rect:
        serializedVersion: 2
        x: 272
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17ad5959301f21f90800000000000000
      internalID: -6984255074401396111
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_18
      rect:
        serializedVersion: 2
        x: 288
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fee03cd8483c24f40800000000000000
      internalID: 5711342251540680431
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_19
      rect:
        serializedVersion: 2
        x: 304
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0156692f619032e70800000000000000
      internalID: 9089118467127665936
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_20
      rect:
        serializedVersion: 2
        x: 320
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e1f75f37c5646f40800000000000000
      internalID: 5720809333102997989
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_21
      rect:
        serializedVersion: 2
        x: 336
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17abb2b47dc825820800000000000000
      internalID: 2905539565914405489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_22
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c776c6fb3a8d23e80800000000000000
      internalID: -8200253773685430404
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_23
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 59aad6f7850243af0800000000000000
      internalID: -417673300971509099
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_24
      rect:
        serializedVersion: 2
        x: 416
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a29b7216b90a66ce0800000000000000
      internalID: -1412264843923703510
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_25
      rect:
        serializedVersion: 2
        x: 432
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5c70d6cb14d89b7e0800000000000000
      internalID: -1749211666783074363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_26
      rect:
        serializedVersion: 2
        x: 448
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 254772c2aad428c60800000000000000
      internalID: 7818897296349164626
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_27
      rect:
        serializedVersion: 2
        x: 464
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 442ce55cf97ac0060800000000000000
      internalID: 6921091032014570052
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_28
      rect:
        serializedVersion: 2
        x: 480
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dae052615c0dd4ff0800000000000000
      internalID: -50154475932545363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_29
      rect:
        serializedVersion: 2
        x: 496
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49519faa969bc0880800000000000000
      internalID: -8643329721339669100
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_30
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1589fd75b00d40bf0800000000000000
      internalID: -358933323145308079
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_31
      rect:
        serializedVersion: 2
        x: 528
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 050e43ad76042bbd0800000000000000
      internalID: -2615957618762129328
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_32
      rect:
        serializedVersion: 2
        x: 544
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef2e6f4c54afb9a90800000000000000
      internalID: -7305970792914296066
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_33
      rect:
        serializedVersion: 2
        x: 560
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dacaba1ced4a46c40800000000000000
      internalID: 5504705921192537261
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_34
      rect:
        serializedVersion: 2
        x: 576
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c867e8cb4c0654010800000000000000
      internalID: 1172449676093191820
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_35
      rect:
        serializedVersion: 2
        x: 592
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b286134a1b4be8170800000000000000
      internalID: 8182676248035682347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_36
      rect:
        serializedVersion: 2
        x: 608
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba72c8352f4e68dd0800000000000000
      internalID: -2484046415013337173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_37
      rect:
        serializedVersion: 2
        x: 624
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a1137071948e8bd0800000000000000
      internalID: -2626015772518706783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_38
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fcbf12e59a5579330800000000000000
      internalID: 3717534203334753231
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_39
      rect:
        serializedVersion: 2
        x: 656
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 47fbd8e5bd7d25540800000000000000
      internalID: 4995292273891458932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_40
      rect:
        serializedVersion: 2
        x: 672
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e88d95955072759a0800000000000000
      internalID: -6244479454398523250
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_41
      rect:
        serializedVersion: 2
        x: 688
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55b3b94c7e8087260800000000000000
      internalID: 7095431004451191637
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_42
      rect:
        serializedVersion: 2
        x: 704
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f90cbc1eaab700db0800000000000000
      internalID: -4827722826678288225
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_43
      rect:
        serializedVersion: 2
        x: 720
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2710f0e5152ac6270800000000000000
      internalID: 8245143488162627954
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_44
      rect:
        serializedVersion: 2
        x: 736
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 167e93b99d8bf2d00800000000000000
      internalID: 950181291150141281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_45
      rect:
        serializedVersion: 2
        x: 752
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 278622de416253390800000000000000
      internalID: -7839317705048823694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_46
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ffc73df6764162f90800000000000000
      internalID: -6978868138073686785
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1_47
      rect:
        serializedVersion: 2
        x: 784
        y: 0
        width: 16
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 073f336063b29aa40800000000000000
      internalID: 5379878740903064432
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 47608138893efe44596902bac70723e2
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":16.0,"y":32.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      Magic trap - Level 1_0: -460100278702687741
      Magic trap - Level 1_1: -426917012179599173
      Magic trap - Level 1_10: -6948527762784998565
      Magic trap - Level 1_11: -8451934468355760499
      Magic trap - Level 1_12: -987674567612252191
      Magic trap - Level 1_13: -5629835879513088291
      Magic trap - Level 1_14: 1302855779236290019
      Magic trap - Level 1_15: 6279640327849729732
      Magic trap - Level 1_16: 4155537537240140520
      Magic trap - Level 1_17: -6984255074401396111
      Magic trap - Level 1_18: 5711342251540680431
      Magic trap - Level 1_19: 9089118467127665936
      Magic trap - Level 1_2: 7573963305546292440
      Magic trap - Level 1_20: 5720809333102997989
      Magic trap - Level 1_21: 2905539565914405489
      Magic trap - Level 1_22: -8200253773685430404
      Magic trap - Level 1_23: -417673300971509099
      Magic trap - Level 1_24: -1412264843923703510
      Magic trap - Level 1_25: -1749211666783074363
      Magic trap - Level 1_26: 7818897296349164626
      Magic trap - Level 1_27: 6921091032014570052
      Magic trap - Level 1_28: -50154475932545363
      Magic trap - Level 1_29: -8643329721339669100
      Magic trap - Level 1_3: 6412681355437025522
      Magic trap - Level 1_30: -358933323145308079
      Magic trap - Level 1_31: -2615957618762129328
      Magic trap - Level 1_32: -7305970792914296066
      Magic trap - Level 1_33: 5504705921192537261
      Magic trap - Level 1_34: 1172449676093191820
      Magic trap - Level 1_35: 8182676248035682347
      Magic trap - Level 1_36: -2484046415013337173
      Magic trap - Level 1_37: -2626015772518706783
      Magic trap - Level 1_38: 3717534203334753231
      Magic trap - Level 1_39: 4995292273891458932
      Magic trap - Level 1_4: -9167168533238919709
      Magic trap - Level 1_40: -6244479454398523250
      Magic trap - Level 1_41: 7095431004451191637
      Magic trap - Level 1_42: -4827722826678288225
      Magic trap - Level 1_43: 8245143488162627954
      Magic trap - Level 1_44: 950181291150141281
      Magic trap - Level 1_45: -7839317705048823694
      Magic trap - Level 1_46: -6978868138073686785
      Magic trap - Level 1_47: 5379878740903064432
      Magic trap - Level 1_5: 4586176262907357701
      Magic trap - Level 1_6: 6070332774980758281
      Magic trap - Level 1_7: 5191687169946619234
      Magic trap - Level 1_8: -454259800005375333
      Magic trap - Level 1_9: -2184840807408459363
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
