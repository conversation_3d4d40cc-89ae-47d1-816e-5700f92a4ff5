fileFormatVersion: 2
guid: 76711d5f91d996041a19f77946d81e83
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1580441614216361810
    second: Enemy3-Fly_0
  - first:
      213: 3768569184673848441
    second: Enemy3-Fly_1
  - first:
      213: 4554274250909831551
    second: Enemy3-Fly_2
  - first:
      213: -4082173696017322263
    second: Enemy3-Fly_3
  - first:
      213: -77624808259697874
    second: Enemy3-Fly_4
  - first:
      213: -804159970561975091
    second: Enemy3-Fly_5
  - first:
      213: 1418816952933761602
    second: Enemy3-Fly_6
  - first:
      213: -529208579966824279
    second: Enemy3-Fly_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Enemy3-Fly_0
      rect:
        serializedVersion: 2
        x: 16
        y: 6
        width: 39
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2577c9b464bdee510800000000000000
      internalID: 1580441614216361810
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_1
      rect:
        serializedVersion: 2
        x: 79
        y: 6
        width: 39
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97cd7fbe4b5ac4430800000000000000
      internalID: 3768569184673848441
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_2
      rect:
        serializedVersion: 2
        x: 141
        y: 6
        width: 40
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f7150373d48043f30800000000000000
      internalID: 4554274250909831551
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_3
      rect:
        serializedVersion: 2
        x: 204
        y: 7
        width: 41
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9eea179d6a43957c0800000000000000
      internalID: -4082173696017322263
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_4
      rect:
        serializedVersion: 2
        x: 269
        y: 8
        width: 41
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e234cede5a83ceef0800000000000000
      internalID: -77624808259697874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_5
      rect:
        serializedVersion: 2
        x: 334
        y: 9
        width: 41
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dcc901474cc07d4f0800000000000000
      internalID: -804159970561975091
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_6
      rect:
        serializedVersion: 2
        x: 399
        y: 8
        width: 41
        height: 53
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2424be06086a0b310800000000000000
      internalID: 1418816952933761602
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Fly_7
      rect:
        serializedVersion: 2
        x: 464
        y: 7
        width: 40
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9a01df2b99fd7a8f0800000000000000
      internalID: -529208579966824279
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Enemy3-Fly_0: 1580441614216361810
      Enemy3-Fly_1: 3768569184673848441
      Enemy3-Fly_2: 4554274250909831551
      Enemy3-Fly_3: -4082173696017322263
      Enemy3-Fly_4: -77624808259697874
      Enemy3-Fly_5: -804159970561975091
      Enemy3-Fly_6: 1418816952933761602
      Enemy3-Fly_7: -529208579966824279
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
