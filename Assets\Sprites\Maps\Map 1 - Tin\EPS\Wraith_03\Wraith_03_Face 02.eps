%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_03_Face 02.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 8/3/2020
%%BoundingBox: 0 0 160 128
%%HiResBoundingBox: 0 0 160 128
%%CropBox: 0 0 160 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 6 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-08-03T10:11:22+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-08-03T10:11:22+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-08-03T10:11:22+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>232</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADoAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FVG9v&#xA;LWxsri9u5BDa2sbzXErbKkcalnYnwAFcVfnHrOp6n5j1+81G5kkur2+mkmZ5Gq3xEtSpNAANgO2I&#xA;FpVrCXzbpjBtOnvLR13BtZZEI+XpNh4SmmdeVP8AnJD81vLk6x3OoHWLVNntNTX1W9/33wzA/NiP&#xA;bIofSH5Zf85DeSfO7RWErfobXnoBp9yw4Sse1vN8Kyf6pCt4DCh6lirsVdirsVdirsVdirsVdirs&#xA;VdirsVdirsVdirsVdirsVdirsVdirsVdirsVfLv/ADkt+eUF5HP5G8s3Akg5cNcv4jVWKn/eWNh1&#xA;AP8AeEf6v82KXhfl+waNTdSChcUjH+T3P05OA6pDJdOiYyGX9ldh7k5aEoi+02xvk4XMSv4N0YfJ&#xA;hviQCliWreU7u0rNZkzwDfiP7xfoHX6MqljpjT0/8rf+cn/MvloRaZ5nEmuaKtFWctW9hX/JdzSV&#xA;R/K5r4MBtlaH1V5Q88eVvN+mjUfL2oR3sAp6qKaSxMf2ZYzR0PzG/bChPcVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdirsVdirsVdirsVdirsVfNf/ORH/OQD2r3XkvylccbgVi1jVYm3So+K3gYftdnb&#xA;9n7I3rRS+dNH0czEXFwKQjdEP7X9mSjFIDKLa1eZttkHVv6ZcAlNkRUUKooo6DCldhV2KpRq3lqx&#xA;v+Uij0Lk/wC7VGxP+UvfISgCimPWk3mvyhqcepaZdTWF1EaR3tsxUEH9kkdQe6tscpMSEU98/Lz/&#xA;AJy7IEVh56s6kUX9M2S7ntymtx95Mf0JkUPojy95n8v+Y9PXUdC1CHUbNv8AdsDhuJ68XX7SN/ks&#xA;AcKEzxV2KuxV2KuxV2KsH82fnZ+WXlTUH03WdbSPUYxWS0hjmuHU+DmFHVG9mIOKpXaf85Jfkxcl&#xA;VHmEQu37M1rdpTfuxi4fjirMNA89eTPMJC6JrdlqEh39GCeNpR33jrzH0jFU8xV2KuxV2KuxV2Ku&#xA;xV2KuxV4P/zkj+dzeWrR/KPl6fjr95H/AKfdxn4rSCQbKpHSWRTt3Vd+pU4pfK2j6UblvrE4/cg7&#xA;A/tn+mSjG0hllpaGU7/DEvcfqGXAJTREVFCqKAdBhSuwq7FXYq7FUu13UTYWBmEInDMEZD9kBgdz&#xA;sdu2QkaCCxy20/StcEhtFNjeoOTQ/ajYdKjpTKwBJCtpl7548g6nb6zplxNp8r7RXUJ5QTBTvG4N&#xA;UcVG6OPenTIEEIfWn5Lfn3pHn2FNL1EJp/mmNSWtQSIrlVFWkgJ792jO467itFD1nFXYq7FXYq80&#xA;/P8A/MifyN5EknsHCa1qb/U9ObvGSpaSan/FaDb/ACiuKviWzsbvVbmWaWVmd2LyzyVdmdjUkkmp&#xA;JrUknMvS6M5bN0HK0+mOTyCbJ5HuJFql0CfAoR/xscyz2Sekvs/a5n8mHpL7EBqXl3VtIC3TEcEY&#xA;cZomNVatVPYjfvmHn0U8Qs8nFz6OeMWeT65/5xl/M+983+VbjStXna41rQyiNcSGrzW0gPpOzHdn&#xA;UqyMfkTucxXDet6tqdnpWl3mqXr8LOwgkubh/COFC7n7lxV8Necvz0/Mzzbqk7RapdafYO7fVtM0&#xA;92hVI6/CrtFxeQ06lj93TDGJkaAtlGJJoMWbU/O9nW9+v6hAw4kzieVWFPs1YNXau2Wz02SIsg02&#xA;y084iyNn1d/zjL+aupecNAu9H1ydrnWtG4EXchrJPbSVCs5/aeNl4s3cFe9cpaHtWKuxV2KsN/Nv&#xA;8wYPIfki81wqsl6aW+mwN0e5lB4cunwqAXb2GKvhBDqGvavcX+oTPcXFxI097cuas7uSSSfFjhiL&#xA;ZBk9pbB2WJBxRR27KMuASnKIqKFUUUdBkkrsKtEgdTgVvFXYVdiq2SNJEKSKHRhRlYVBHuDgVC2m&#xA;k6fZSPLawCORxRiCenWm5NPowCICGS/llqNp5jtNU8v6xZhotne2fdQR8JKnYhunTpTIXaGC+dfJ&#xA;+r+Q/MFtf6dcSLbCUTaXfoaSRyRnkFYjo6dQe/3gVEUh9e/kj+alv+YPlJbqbjHrun8YNXt1oBzI&#xA;+CZB2SUAkeBBHauKHoeKuxV2Kvl//nNB5/rflNDX0PTvio/Z58oA300pgSHkvk3TIp9PicPQsW5C&#xA;nfkRnVdm4QcMSD3/AHvQ9n4gcYL0DTvLkbqP31D/AKtf45sPA83cQ0w70F5z8u+h5d1CR5EaNIHb&#xA;uDyUVXx/apmJrcFYpX3NGu09YZE9yJ/5w7eYfmLq0YJ9FtIlZx25LdW4WvvRmzkHjS9+/P6WWL8n&#xA;vMzREqxtkQkfyvNGrD6VJwofGXkCwN3qM4XjzRFpyNNid/1Zt+x8fFKXudt2Vj4pl6Lrflgjy7fS&#xA;yvGES3ldhU1HFCa9PbNzqcB8KV9xd7qdL+6kT/NLv+cRZpE/NG5RWokulXCyDxAmhYfiM4540vsj&#xA;Ch2KuxV8rf8AOZHmJ5Nb0Hy6j/ura3e/mQdC87mKOvuohan+tgSHjui24h0+M0+KT42+np+GXRGz&#xA;IJ9piDg79yafdv8AxyYSvutQggVjWpUEsewp4nCSqS/4ltbhivr8PAEFB9/9chxhFqqzRP8AZdW+&#xA;RBw2qqksiGqMV+WKo621AMQsux7N2+nDaUbhVfBbz3EoigjaWVvsogLE/QMVTtPJmrJayXd60Vja&#xA;woZJZJ3+yiipJC8vxwGQRal+XWveVrrzEIrfUJBeAOsMU0IiSYUp+7bm3zAIB9shx2i3ofmfy9Ze&#xA;YNEudLuwOE6/u5KVMcg+w6+6n+mAhXif5QecL78ufzPgN+xgs3lOm63Gd1ETuFMn/PNwHBHYe+VI&#xA;fd4IIqOmFDsVdiryT/nJX8vLvzd5DFzpsRn1bQ5DdwQoKvJCV4zxoO7UAcDvxoOuKvkTyt5lXSZG&#xA;juFZ7ZzyBXcq3yNNjm07O7Q8G4y+k/Y7LQ63wtpfS9DsPzJ8sxoOd2UPgYpT+pTm7Ha2n/nfYf1O&#xA;9h2tg/nfYWPef/zDt9WshpemMzW8hDXM5BXkFNQig0NKipzV9pdpRyR4Icupdd2n2pHLHghy6l7t&#xA;/wA4leQbrSfLl95qv4jFPrfCOwVhRvqkRJ9SnhK529lB75pHRPZ/N/l6DzH5X1XQZ24R6lay2/qf&#xA;yM6kK/8AsWocUPgCBtX8m+aLi01C3aG8spGtr22bY7HelevQMp7j2OZWi1RwZOLp1crSak4Z8Sce&#xA;Y/zKk1LSZdOtYXiWeiyyuRXh1KgDx6ZsNZ2sMkDCIq3Y6vtbxMZhEVb2z/nEDyNd21tqnnO7jMaX&#xA;qfUNN5CnOJXDzyCvVeaIoPirZpXSl9JYodirsVfDX/OSer/pP84dZVDyjsRBZxf884VLj/kY7YEp&#xA;LEgjiRB0RQv3CmZDJu11JJonjt5OSKxDkV608fDEFUJrD8NNnPiAv3kDIy5KWKIhatO2VAMW/Rk8&#xA;MaVUjnu4DWOR0+RNMdwqY2nmKdCFuVEid2Gzf0OSE02zvyXC+v3kdnbPWMgs8tK+mg68h9O2WiSb&#xA;R3mT81dM8vNJpPlC3imkjJS41OX41ZhseFCOdD+0fh8ARlcpoef6z5984a3bvbajqUk1tIQXgUJG&#xA;hpuAVjVaj55CyhIl9aF0lRikiEMjqSGVgaggjocaV75+Vn5nDXo10fVnC6xEv7qY0AuFUb/89AOo&#xA;79fHJxklhn58aGLTzHbarGtI9SipIf8Ai2Cik/SjJkZBX1f+SHmlvM35X6DqMr87qOD6pdk/aMtq&#xA;TCWb3cIH+nAxZ1irsVdiryLz3/zjJ+X3mm/m1O3M2iajOS8zWfD0JHO5doWFKnvwK1774qwN/wDn&#xA;C1SxKecCFqeIOnVIHap+tD9WKbT7yf8A84ieVNJ1KK917VJdeSFg6Wfoi1gYg1HqrzmZ1/yeQr32&#xA;2xV7zHHHFGsUShI0AVEUAKqgUAAHQDFC7FWB/mB+SfkHz1dR32s2kkWoxgKb60cQyug2CyGjK4Ha&#xA;oqOxxVj2l/8AOLH5SWNws01pd6hxIIiurluFR4iEQ1+RxV6vZ2dpZWkNnZwpb2luixwQRKEREUUV&#xA;VUUAAGKq2KuxVRvr22sbK4vbpxHbWsbzTyHoscalmY/IDFX51anqsvmDzffaxOKPqN5Neyg/siSR&#xA;pSPkK0xjzSr6fqkt9dSwsoWEoStOo3pufpywSspRunaellCUDc2Y1ZqU/DJRFJSzzHdbx2qn/Lk/&#xA;UBkJlBSuFaLXxyIQvwoXLG7dBiq2W2NKkU9xgISu0/V9V0s3C2Ny9t9aiaC44GnONuoODkqHijDb&#xA;noMQFRCIzbKMkhp0I+FhiqnBcXFldxXNtIY54XWSGReqspqD9+RKXs35p3MfmD8sdK16NQHEkMzg&#xA;bhfURo5Er7SUH0ZKXJL0b/nDjVXm8oa7pbGos79J19hcxBaffBkUF9BYodirsVdirsVdirsVdirs&#xA;VdirsVdirsVeGf8AOVP5kR6H5SHlSylpq2vLS5Cn4o7EH4yfD1mHAeI5Yq+WPLtnVZLhx8LD00r3&#xA;H7WTgGQRljDYWl29rDyacryZmoaDwqKeOSAAKUwd1RGdjRVBLH2GSVh1xM11dPK3V2rTwHYfdlHM&#xA;sVQDsMkhExQBRVt28PDCqril2KoaezD7psfDAQrUVqwoDsBjSEUqhRQbDClSuFqle4xQgZxsDkSl&#xA;6dY3a3P5BajE5qbK7WGOvYtcRS7f8jjj0S9H/wCcLP8Apsf+3b/2NYEF9N4odirsVdirsVdirsVd&#xA;irsVdirsVdiqV+atYl0XyxrGsxQ/WJdNsrm8jgJIEjQRNIEqN/iK0xV+fOta3rfnHzNc6tq1x61/&#xA;fOZJpD9lVHREUk0VF+FV8MQLSnUMSQxLFGKIgoBl4ZLVtoFne4CUmcAM/sMFKluu6hELY28ThpHN&#xA;HCmtFG5rTIzKCkcC9W+gZAIRdulTyPQdPnkkIjFLsVdirsVdirsVWyisbfLFUvn+x9ORKsrtr5of&#xA;ylvLYMaXOsIvH2EAcn70XB0V9A/84b6Z6XlDXtTpQ3d+lvXxFtCr/wDYxipfQeKHYq7FXYq7FXYq&#xA;7FXYq7FXYq7FXYqsuIIbiCS3nQSQzK0csbbhlYUYH2IOKvz38/eVLzyL581DRZAxWymLWkjf7ttn&#xA;+KJq/wCVGaNTvUYg0lFxSpLEsiGqOKg/PLwyQusCc2Eggry25BevGu+RlyUsTyliikpxFOmTQjYh&#xA;SNR7V+/CldirsVdirsVdirsVcRUEeOKpbP8AZHzyJVNLu4Efk/TrQU5S3t1cvtvxEcESb/NXwK+x&#xA;/wDnGPSTp/5P6VIy8ZNQlubtwadGmaND9KRqcUPVMVdirsVdirsVdirsVdirsVdirsVdirsVeI/8&#xA;5PflW/mfy2vmXS4eetaHGxmRBV57P7Tr7tEauo8OXcjFXyhoOoiN/qsp+Bz+7J7Me305KEujIMgy&#xA;1KReYrSNUjuEQKxYrIQKVqKiv3ZXMIKVwmqU8MiEI+JgUU+1Mkq7FXYq7FXYq7FXYq7FUsuT8dPC&#xA;uQKq07yXP1K0iXm8UYijVerNJI0n31k44Ffon5P0JNA8qaRoi0/3HWcFsxHdo4wrN/smBOFCb4q7&#xA;FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXx9/zkZ+R8vlnUJfNfl63J8u3jlr23jXaymc+A6QyMfh7&#xA;Kfh2+HAl5Xo+sCQLb3DUkG0ch/a9j75bGSQUbqlv69jKgFWA5L813yUhYSxWJwpNehykFiiYpSpq&#xA;pqO4ySEWjqwqMKW8VdirsVdirsVdiqUzMGlYjpXbIFXpX/OO/k1/M/5oaaZI+VjpB/Sd4SKrSBgY&#xA;lPb4pim3hXAr7pwodirsVdirsVdirsVdirsVdirsVdirsVdirsVU7m2trq2ltrmJJ7adGjmhkUMj&#xA;owoysp2II6jFXyD+eH/OOmoeWJrjzB5Vhe88uMTJPZpV5rIdT4s8Q7N1Ufa/mwJeSadrzxgRXVXT&#xA;oJOrD5+OTjPvTavc6JbXQM9nIF5b06oT9HTCYXyWksm0jUYTUxFgP2k+L9W+RMStKCTzRPv26qcF&#xA;oTGORZEDL0P4ZNV2KuxV2KrJpkiXk29egwEqgZLqeY8FFA2wVdycjaqAUluIFWJoAN98CvuH/nHf&#xA;8sn8k+ShPfxenr2tcLm/VhRokAPowH3RWJb/ACmI7YUPVMVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVdiriARQ9MVeG/mr/zi/wCX/Mjzar5WaPRNZery23Eiynb3VRWFif2kBH+TXfFXy/5m8n+d&#xA;fJGo/VdbsJtPkJIjkYcoJQO8cgrG/wBB2xBpKhbeZBstzHv/ADp/Q5MTTaveHTdThASdVmX+7LbH&#xA;5EHthNFUjdLqzmKuOLeHUEZXuEIqG8jk2b4W8D0yQKq+FUPNexpsnxt+GAlUEXaaSsjUHcnoB8si&#xA;qLlvoxELWxi9NSOMkxH76WvUEivEH+UYSe5L6P8A+cev+ce7q2urbzh5xtTFJEVl0jSJhR1cbrcT&#xA;qfsleqId67mlBgQ+mMUOxV2KuxVQv5riGxuZraL1riOJ3hh/ndVJVfpO2KvjnT/+cqPzb0zVrg6p&#xA;9VvU9Rlk025thB6JB3RDF6cgK9P3hbAlnOn/APOZ1iyAah5Wljcfaa3u1cH3CvElPvwrSpd/85na&#xA;Sq/6J5WnmanSW7SIVr4rFL2xWnsX5W/mLZ/mB5Ti1+2tHsW9V7e4tXYScJY6EhZAF5rRgQeI+WKG&#xA;XYq+fvOn/OWlp5e816podp5be/i0u4ktJLqS7FuWlgYpJRBDN8IdSB8W/XbFLENe/wCcx/M1zbtH&#xA;omg2umysKetcTPeMvuoCW61+YIwLT0T/AJxo/Mnz751stabzO31u3snhFnqQhSDk8nP1If3SojcA&#xA;qnYVFd+owoe2Yq7FXYq7FXYq7FUPqOm6dqdnJZajaxXtnMOMttcIssbjwZHBU4q8d83/APOKP5d6&#xA;yzz6M8/l+6appAfXtqnuYZDyHySRR7Yq8j8w/wDOI/5j2BZ9IuLLWYR9hUkNtMfmkwEY/wCRhwJY&#xA;Jqn5NfmvplUuvK+osqdTbwtcoNq15QequKscuvLfmK0NLrS7u3IPEiWCVN/D4lGKoX6rqDN6Hoyl&#xA;h/uri1fupiqa6b5E87amyjTtA1G75UIMNrM4oehqFoB74q9E8r/84tfmjrEiNqNvDodo25lu5FeS&#xA;n+TDCXavs/HFX0J+Wv8Azjz5H8kyx37IdZ1xKFNQu1XjEw7wQiqxn/KJZvBsKHqOKuxV2KuxV2Ku&#xA;xVIPMn5f+SfM1Tr2iWd/KRx+sSxL6wHSgmWkg+hsVYLd/wDOLn5PTyco9NuLUb/BDdzEb/8AGRpD&#xA;+OKrLf8A5xY/KCKTm9jdTr/vuS6lC9f8go344q9K8ueW9D8t6RBo+h2aWOnW9fSgjqd2NWJZizMS&#xA;epYk4qmWKsB8z/kR+VvmXU59V1TRQdRuW53FzDNPCXalKskbqlT3PGpxVS0j/nH38n9KkWWDy5BP&#xA;Kv7V4810poa7xzu8f/C4qz61tbW0t47a1hS3t4hxihiUIijwVVAAGKquKuxV2KuxV2KuxV2KuxV2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVjPmX8zfy/8ss8eua9Z&#xA;2lxGQHtfUElwKiorBFzl/wCFxVJLT/nID8nbtqReZrdSP9/Rzwjw6yxpirK9G83+VNcYLo2s2Oou&#xA;V5+na3EUzhfEqjFh9OKptirsVdirTMqqWYgKBUk7AAYq8a8yf85W/lpo+pyWFrHeav6LFJbqzSP0&#xA;Kg0PB5JIy/zA4nscVZ7+X/5l+UvPmmyXvl+5LmAhbq0mX07iEtUr6iVOxpsykg+O2KspxV2KpP5r&#xA;83eXvKmjS6xr14lnYxELzapZ3PRI0WrOxp0A/DFXk9n/AM5d/lrPqS201nqVraM3EX0kUTIv+U6R&#xA;yu4X5An2xV7Rp2o2GpWEGoafOl1ZXSCW3uIiGR0YVDKRiqIxV2KuxV2KuxV2KuxV2KuxV2KuxV2K&#xA;uxVCatq2m6Rptxqep3CWlhaIZbi4lNFRR3P8B3xV8gfmx/zkx5n8y3M2meV5ZdF0AEoJYiUvLgV+&#xA;08i7xqf5EP8ArE9AEvHZbC8S3+tzKUjZuIL7MzHfYdcNFK7T9MmveZRgoSm7VoSe22ERtVCGa4tb&#xA;hJoJGhuIWDRyxsVdWU1DKwoQQcih9Bfk9/zlBqtjd2+i+epze6bIRHFrTCs8B6D16f3qeLfbHX4s&#xA;VfVsckcsayxMHjcBkdSCrKRUEEdQcKF2KvDv+cr/AD3Pofky38vWMxivdfdkuCpows4QDKNunqMy&#xA;r7ryGKvlrQPLKahavczyNGhqsIWm5H7Rr2rk4wtkAy7/AJx21+80P83dIhSTjDqLvp95HWiusqni&#xA;D8pVRh8srQ+6MKHYq+JP+ckvP8vmv8wp9NtJjLo+hsbO0RT8DTiguJAOhJkHAHuFGBLBdW8sJYaS&#xA;t16ha4Ur6y7cfi2ovyOWShQTT6U/5w88yXd75V1rQp5DJFpFxFLahv2I7wOSg9ucLN8ycggvoHFD&#xA;sVdirsVdirsVdirsVdirsVdirsVdir48/wCcm/zcbzHrreU9InroWkyEXckZ+G5u12apHVIvsr4t&#xA;U7/DgS800DQwpSSVaztvv+wP65bCLIBD+cLgG+jtE2jtkG3+U+5/CmDId0FNdL0i4t7CIcPicc33&#xA;Fatvk4xoJYu0YTWDGy7CcqykdudKZV1Q3q2nmzn+H+5k3jPh4j6MZRpS+s/+cTvPlxrfk+68t30h&#xA;kuvL7ILV2NSbOavppvufSdGX2UqO2Bi9p1nWdK0XTLjVNVuo7PT7VedxcymiqOn0knYAbk7DFXwn&#xA;+cv5hN+YXn2bU7NJF06NUs9Khk+36SEnkwHRpJGZvaoHbAlMrG1S0s4bZekSha+J7n6TmUBQZpd+&#xA;SyiX85PLlF5A6hzApX7IZq/RSuYzB974oeLf85CfnfbeUNMm8u6HOH81XicXdCD9Shcf3jH/AH6w&#xA;+wvb7R7VVfKflTSHurv69MCYYWqpP7UnX8OuTxx6sgnvm0gaJLXuyAf8EMsyckl7F/zhdCBa+bZ6&#xA;7u9gnHw4C4Nf+HyhiX0tih2KuxVxIAqemKpDdef/ACJaXH1a68yaXb3NePoS3tuj16U4s4NcVTm1&#xA;u7S7gW4tJo7iB90liYOh+TKSDiqrirsVdirsVdiqncXFvbQvPcSpDBGOUksjBUUeJY0AxV8//nj/&#xA;AM5IaRY6Zc+XvJV6t5q1ypiudWtzWG2Q7N6Mg2eUjYMuy9a1xS+WtNNok4nu3+FTVVpUlvE+wwxr&#xA;ql6BBLZJaLco6iDhy9Y7Cnia5fYZMC167t7vVZ7i3JaJ+NCRSvFQv8MokbLEs5sdRjm0hL5gVQRl&#xA;3HhwqGp/wOXg7WlgdvJ9a1dZXoplmMjDtUtyplA3KGV+YNL9XSuSj95EoY/Mb/2ZbMbJLNP+cSda&#xA;gsfzOmsZ5OA1XT5oIE2o00bxzj/knHJlDFl3/OYi+b2m0Yosp8pLETIYwfSF9zYVmptX0yvp8v8A&#xA;Kp3xUPnbRtUi064M7WwnforFuJXxpsclGVJDIH88WvokpbSetT4VJHGvz6/hlniptlX/ADi/oN3q&#xA;f5t6fepEz2ukxT3V3JQ8V5wvDHVttzJICB3offKWL7acOUYIQrkHixFQD2NNq4UPzr89eX/OGi+Z&#xA;LyLzZbzx6tPNJLNcTgkXDMxLSxyfZkViftLgSq6f5xW3hSB7NRGgovpMQAP9Vq/ry0ZE2pa/5lh1&#xA;G1W2gidF5B3Z6V2B2AFcE52pL6i/5xE0K6sfy+vtSuI2jXVL9ntuQIDwwxrGHX29TmPoyDEvc8Vd&#xA;iqD1nWNO0bSrvVdSmFvYWUTTXEzdFRBU/M+A74q+KfzN/Ozzp+Yurtpmmme00OR/TstHti3OYV2a&#xA;fhvIzdeP2R28SEpda/kT5+ntlmeK2t2YV9CWb4x8+AdfxwWmkFpur/mT+VmvI9vLPpk5PJoCedpc&#xA;qNjyUExyD8R7HCCh9kflF+aOmfmH5YGpQILbUrYiHVLAGvpSkVDLXcxyAVU/MdQcKGcYq8J/OX/n&#xA;Ji38papN5e8s20Wo6zbfDe3c5Y21u/eMKhVpHH7XxAKdtzUBV5z5a/5y888WuoIdfsbTUtOZv3qQ&#xA;IbedVP8AvtuTIaeDLv4jAl9T+VvM+jeaNBtNc0ef17C8TnG3RlINGRx+yyMCGHjhQ+Lvzk8j/mnb&#xA;+cdVu9cs77ULae5lltdQjWSe2aEsfS4svJY6JQcDQrgSwfTfJ3m7U5Fj07RL+8d/siC2mk6dfsqe&#xA;mKqvmvyR5o8pT2lv5hsW0+4vYfrMELsjP6ZYpVgjNxNVOzb4ql8+o3NxbW9ktRDCOKxL+0xNan78&#xA;kTeyU78v6FpM1frcqT3O/wDo6v8AZA8eJqTk4xCgJjq+v2GmRfU7eNZZFHEwj7Cr4N/TJSmBsm2F&#xA;ytyczIgiVmPFVJop67V3yliiW1rUmu/rLTMX6FSTwI7rx6UOHiKbVdA1690LzBY65p59O60+4S5g&#xA;HaqNy4n/ACSNj7ZFD9GrWWLUdMhmlhpFdwq728oBoJFBKODt3ocKGE69+Qv5S62We58u29vK2/q2&#xA;Re0IPjxgaND9KnFWJH/nEb8rDdif6xqgiBqbUXEXpn2J9H1P+HxV6l5T8meWPKWmfo3y9p8dhaE8&#xA;3CVZ3alOUkjlnc+7HFU6xVCappGk6taNZ6pZQX9o/wBq3uY0mjP+xcMMVeca5/zjR+UWrOZF0p9N&#xA;lY1Z7GaSIfRGxeIfQuKoHQP+cVvyp0m8S6mjvdXMZ5JDfzI0VR0qkEcHIezVHjir12C3gt4I7e3j&#xA;WGCJQkUUahUVVFAqqKAADtiq/FXYq+fP+cw/M89n5X0fy7A/FdWuJJ7oA/aitApVG9jJKG+a4pDD&#xA;P+cffJsSaQ2vmEzahqDvDaECpSGNuLcfdnU19gPfISLIPc4PJ2qSIGkaOIn9hiSfp4gj8chaaYv+&#xA;Yv5bT6x5burC8hWSql7W5Tf0p1B4P0DAV2PtthBUh4j/AM4x+ZbrQ/zXtNOZiltrKS2N1GenNVMk&#xA;Rp4iSPj9Jyxg+ufzA8yHyz5J1vXkp61haSy24NKGbjxiBr2MhXCh8R/lT5Tbzl50I1APdW8Ie8vg&#xA;as0zltlYjf43ap8QDkSWQeofnD+WeknyvcahZabHp2o6WnrARRCHnAv94rqAteK1YH2yIKSET/zh&#xA;x5qnXUNc8qSuWt5Ihqdqh6K6MsM1P9cPH/wOTYl9R4UOxV8Yf85XeZbfVvzNGn25DJolpHaysN6z&#xA;OTM4r/kiRV+YOBLyB1a2qnScj4iP2Qe3zPfDyVU9HU9ONvd8JLf1lL28pFOSn4SQckYSjRIq2csc&#xA;ogEjmjbXyxqFz5evtfc+nZWnAIzbtM7yrGQvsvOpP0fK6Glkccsn8I+3dvhpJSxSy8ox+3ekPZwp&#xA;Jo+oFvtQtBIn0syH/iWUDkXGeif845/l1a+c/PR/SlqLrQtLgebUIn5cHaVTHDHVSCCWJcf6hyKH&#xA;1BoP5B/lNoWorqNhoEZuo25wtcSzXKxkdOKTO61HYkV98KHoOKpfrvmHQ9A099R1q+h0+yTZp7hw&#xA;iknoq1+0x7KNzirAov8AnJP8mZLo248wcSCFWVrW7EZPsxi2+ZoMVeh6Xq2matYxX+mXcV7ZTCsV&#xA;zbuskbD2ZSRiqKxV2KsP84/m7+Xnk+f6trusRQXuxNlEHnnAO4LRxBygpv8AFSuKpV5Z/wCcgfyo&#xA;8w3SWdrrS2t5KaRw3qPbcjWgAkcCKp7DnXFXouKuxV2KvmP/AJzPsZ+XlW/ArbgXkDt/K59J1B/1&#xA;gD92BIZn/wA4yXGn3f5YWckRDXlpLPa3XihWQyKvtVJFOVy5sw9byKUPf3lrZWNxeXbiO0tonmuJ&#xA;G+yscalnJ9goxV8VfkjHNrH54aLPbR8Q97NeMvZI0SSVq/ICnzy4Nb6r/P6CWf8AJ7zOkS8mFsjk&#xA;D+WOaN2P0KpOFDwv/nD67tU8x+YLRqfWprOGWKvXhFKVkp9Mq5XNnF7x+bH6PT8ufMVxegcIdPuT&#xA;Gx/340TKi/7NiF+nIjmkvnT/AJxEt5ZPzPu5VWscOlTmRuw5TQqPpJOWsC+x8KGL/mX570/yP5Pv&#xA;teuypliUx2NuTvNcuCIox7V3bwUE4q/P671C81HUrvVb+Qz3VzK9zcyt1eaVi5J/1mNcQlX0O0hu&#xA;r/1rxgLWE+pOzmgbfYE+5y3BEGVnkG7BEGW/IM+n1nyzqFg311op7W3oxBrVT0HGlG36bZtzmxTj&#xA;6qIDuTnxTj6qIDGNf89zX2mvo1jbR2mkEKqpT4yEYOOh4ruv9uYWo15nHgiKg4Wo7QM4eHEVBIbe&#xA;Xhpl4v8Av14U+7k3/GuYI5OufUH/ADhppbReXPMeqkUW7u4LYNvv9WiZz/1E4EF9E4ocSAKnpir4&#xA;I/NPz3rX5j+fJpI5GksEme20S0rSOOANQPTpykC83b+AGGETI0GUYkmgpSflXeLal0v0e5Ar6Ppk&#xA;IT4c+Vf+FzYHs41z3co6Q1zTL8mfzS1f8uPNot7xnGhXUyw6zYtUhN+ProP54+u32ht4U15BBouK&#xA;RWz7pVlZQykFSKgjcEHAxYP+dXnqXyV+XmpaxakDUZONppxPaefYPv8A77UM9PbFXxHoPl7U/NF9&#xA;c3M1y32/Uu7yWsjvJISSdzVmO5JJzI0+mOU9wbsWIzRnmL8vrvS7Rry2n+twRisw48HUfzUq1R45&#xA;bn0RgLBsNmTTmIsbvoD/AJxS/NPUNXgufJWsTtcTafALjSJ5DVvqyEI8LMevpllKe1R0UZhOM+ic&#xA;UOxVhP5xfl4nnzyNeaKhVNQjIutMlfotzEDxBPYOrMhPatcVfKv/ADjv5z17y9+Ydpo1oBPYa3Ml&#xA;rf2pNVFK0mQrtyj3NehWvsRCQZB9W+cvzG8meTY4H8x6klk1zyNvFwklkfj1ISJXam/UimQAtlb5&#xA;v/Or/nIoea9Ol8ueWYZbXRpiBe3s3wTXCg19NUBPCMnc1NW9twZiKCWff84qflVd6PZTeddYhMN3&#xA;qUXo6TA4oy2rEM8xB6eqVHH/ACRXo2TYvetX0u01bSb3S7xedpfwSW1wnSscyFHH3Nih8IyR+b/y&#xA;c/MqpTjfabI3plwwgvLV6ryHSqSL/wACf8pcBCQXov5k/n1o/n/8uNU0m1s7jS9QiNtczRSSI8Us&#xA;SzxoyK68WYiSRTQp0Fe2QEaLIlOP+cMFsS3mt+H+noLICQkf3LetVVHX7S/F9GWMS+lry8tLK0mv&#xA;LyZLe0t0aWeeRgqIiCrMzHYAAYofDf54fmxdfmH5oC2RdPL1gxi0m2NQXJ2ad1/nkpsP2VoOtahL&#xA;z6+Rbfhag1ZPimI7ue3+xGSO2yVtpZ6heLKlpBLOsEb3E4iVmCRxiryPToqjqTkVVdE0XVdc1W20&#xA;nSbZ7vULxxHb28YqWY/gABuSdgNzih9D+Yv+cc9K8pfkpreo33G+83xwxXL3SkmK3WOZGkigBpUe&#xA;nyDOdz7DbCr5rHI/CKmpHwjue2BX33+SXk2Xyj+WukaXcp6d/Ihu79SKET3B5lG941Kp9GFDOsVS&#xA;7zIJD5d1QRV9T6nPw415cvSalKd8Vfn/APl4Yh5ot+dOXCT06/zcD/xrXMrQ14ocjT/Wm/mr8wrr&#xA;6zJZ6O4jjjJWS7oCzEbHhWoC++ZGp1xuofNty6g3UUb5G/JL8x/POpQ3EtlcWmmTsrXOs3ysi+nW&#xA;haP1KNMaD4eO3iRmtJJNlxCb5vum2t47a2it4qiKFFjSu54oKD9WLF41/wA5Z6JqOo/ljFc2imSL&#xA;S9Qiu7xB2hMckPOn+S0q/QSe2KvlTy75zu9DsZLWC3jl9SQy+o/Ku6qtNqfy5k4NWccaAcjHmMBQ&#xA;ZVo3n201WK4s9RiW2kaJyrAkxuoQlga7g0zOxa0TBEttnIhqBLYor/nGH6z/AMrl0f0f7v0rz6z/&#xA;AMY/qslP+H45p3AfcGFDsVdir4Euode/KT81S5tw11oty72qzV4XFrIGRXDDtJE3UdD7imAhIb/M&#xA;Hzvrv5redrWeDTvSuJUjsNM0yFjMwBctQvROTM7kk8Rt8sAFJJfSn5c/84w+SfLUlvqWs8tc1eMK&#xA;/G4A+qRS034Qj7dD0Lk+NAckxezYq7FWPec/y+8oec7FbPzFp0d6sdfQm3SaIt1McqFXXpuK0PcH&#xA;FXwX558l635M8x3mh6rC8bwuwgmIISeGvwSxnoysKH2Ox3GBL6j/AOcUPIeqeXvKmo61qkL20+vS&#xA;RNb28ilXFtbh/Tcg7jm0rEe1D3woeaf85J/nRf65rF55K0kvbaNpdw8GpPUq91cwNxZWp/uqN12H&#xA;c/F4UCXhUBnEqiDl6rfCgUVYltqLTep9sVew/lx/zjJ528zyRXuuq3l/Rm+IyXC/6XIOtI4DQrX+&#xA;aSnjRsVfUnln8q/Ivlzy5c+XtO0yP6hfRNDqLS/HNcq6lW9aXZjsTQCgH7IGFCH8g/k95F8iXF1d&#xA;aDZsLy6+F7q4czSrHWvpIx+yletNz3JoMVZJ5k0S317y9qeiXDcIdTtZrSSQAMVE0ZTkAe68qjFX&#xA;hX5Vf84syeXfNK635qvbXUorB/U0y0tg5R5Afgln9RUpw6hFrv32oVL6GxQ7FXEAih6Yq+Evzq/L&#xA;DVfy+84zSW0TpoN5K02j3qAhVDEt6BYbB4+lO60OIJBsJBZV/wA4t/lkvmDzPJ5k1ex9bRNIX/RT&#xA;MtYpb0kcKA7OIlqx8G44FfYeFDsVWyRxyxtFKoeNwVdGAKspFCCD1BxVhdx+Sn5T3E7zyeVrD1JD&#xA;ybhH6a1PgqFVH0DFXyp+bf5H+a/LfnC+XRdFu73y9dStNpk1nDJcKkch5CFygdlaP7I5bkCuBL2X&#xA;/nGP8nNW8rx3XmnzFbm11W+i+r2NjIKSwwFgzvKp+y8hVaL1AG/WgKvfMUOxV2KpB5t8geTvN9uk&#xA;HmPSodQWKvpSPySVAeoSWMpIoPejYqg/KP5Vfl75QuHufL2iw2V04Km5LSTzBT1VZZ2kdQe4BxVl&#xA;eKuxV2KuxVTlt7eYqZYkkKGqc1DUPtXFVTFXn/mL8hfyr8w65Nreq6N6t9dHndNHPPCkj0pyZInQ&#xA;cj3IpXqcVTfQfIn5deUaSaRpFlp0wFBcBA09PD1X5S0/2WTjjlLkEgEp5HrWlStxS6jLeFf65M6f&#xA;IOYKeEo0EEVBqD0Iyli7FXYq7FXYq7FXYqpXVpa3cJguoUnhb7UUqh1NPFWBGKr4oooo1iiRY40F&#xA;ERQFUDwAGKrsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVSTzR5hXSbZVj+K7nISFO/Jj&#xA;QfjmbotJ4st/pDZjhxF5P58/MuHy9cfUIUW/1tgGuJJD+7iLiqj5kbhR2zd6fTiYvlDuDn4dPxC+&#xA;QSTS/OGtai4/SuqaTavJultcO8Ey16fGIl4/8EcyKxx+mMvhu2TwgcgXoXlrzNrekXUNnrK8refa&#xA;G4VucbfJh3zD1ekxZomWPmOjh5MYO4elo6uiupqrCoPsc5winEbwK7FXYq7FXYq7FXYq7FXYq7FX&#xA;Yq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXkP5jay9v5qtpJKm2spBNN/kxxHk7f7FFJzqezsQGm&#xA;kepH3udghcWAPZ2sfn3zFqWpqZvqcM99bLsS0SszEpXYngFA+eXYomcYQj/ERH5uZfoAC3Q9S8r+&#xA;Z/LusXB0ZLMaWgkuFLLLzR1duQk4o3P921a+2+ZnaPZ89JOA4+Lj+HKvf3rISjIb80y/LW+uJBq/&#xA;lG5kM0FoslxpcrmpjWJ1UivgGdafTmBl9Mo5B8WGojyk918o3T3OgW0j7kClc5/Xw4cpDq8gopxm&#xA;GwdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirzPz7oEM+uqbmos9Q&#xA;imtZZB+z68bR1+850XZ+e8BiOcaPycvBOh7nkX1rVtHvY9M1ZVXU9MIW1km5Kk0IHEcZVo/psvhX&#xA;pQg0pmbQkLjyLsaEhY5FES6jqeswjQtO0q00ixunrdQWJR5J/ED00iVQQPiZu2GcjfHOUpkd/T5o&#xA;ERH1E2mPlC0NrqWp6ihDzXytYaYqftxs4aa5/wBRmUcCftYeDiAv6Y7n9Aa80tgHv/l6xNjo9tbk&#xA;UYKCw9znLavLx5CXVTNlMcx2LsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirs&#xA;VdirsVQWr6Ta6paNbTrUH7LeBy/BnlilYZRlRYJrXlvVEh+r32mwa3ZxmsXrqCw7V5FX396Vzc4t&#xA;RinuJGBcmGUdDSQP5f124hksNK0iHSLWccbj6uoEjqf2WcKu2ZkcmCHqlLjLZ4o5k2y/yX+XNvo/&#xA;G4uwHnUDinYUzW6/tU5Rwx2DRlzcTOM0rjuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2Kv/2Q==</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:5d358f30-30d1-4bb2-9a91-5045f29422a3</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:8493e3c8-070a-cd49-bdb0-5a77a9e4b53c</stEvt:instanceID>
                  <stEvt:when>2020-08-03T10:11:22+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>64.000000</stDim:w>
            <stDim:h>64.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=247 G=87 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>246</xmpG:red>
                           <xmpG:green>86</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=42 G=40 B=46</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>42</xmpG:red>
                           <xmpG:green>40</xmpG:green>
                           <xmpG:blue>45</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=92 G=82 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>91</xmpG:red>
                           <xmpG:green>82</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=91 B=179</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>91</xmpG:green>
                           <xmpG:blue>179</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=107 G=0 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>107</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=115 G=22 B=161</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>114</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>161</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=231 G=204 B=255</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>230</xmpG:red>
                           <xmpG:green>204</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=163 G=102 B=254</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>163</xmpG:red>
                           <xmpG:green>102</xmpG:green>
                           <xmpG:blue>253</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
160 128 li
160 0 li
cp
clp
58.2861 92.957 mo
50.8145 92.957 41.2656 92.001 32.3975 88.2773 cv
29.8516 87.208 28.6543 84.2773 29.7236 81.7305 cv
30.793 79.1846 33.7227 77.9863 36.2705 79.0566 cv
51.3789 85.4033 69.7041 82.0986 69.8877 82.0645 cv
72.6035 81.5664 75.2129 83.3506 75.7188 86.0654 cv
76.2236 88.7803 74.4326 91.3906 71.7178 91.8965 cv
71.2813 91.9775 65.8477 92.957 58.2861 92.957 cv
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.757687 .679133 .626856 .856168 cmyk
f
111.386 92.3516 mo
109.306 92.3516 107.102 92.2119 104.795 91.8838 cv
102.062 91.4941 100.16 88.9619 100.55 86.2285 cv
100.939 83.4951 103.466 81.5898 106.205 81.9834 cv
117.572 83.6055 125.774 79.3262 125.854 79.2822 cv
128.278 77.9531 131.313 78.8428 132.643 81.2637 cv
133.97 83.6846 133.083 86.7236 130.661 88.0518 cv
130.286 88.2568 122.664 92.3516 111.386 92.3516 cv
cp
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_03_Face 02.eps)
%%CreationDate: 8/3/2020 10:11 AM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9Y`S`+N!?dbQ(Qn`/W_NC"P[V:CeCGHV86b:YNOA1m$VLs`Sr)rZ?`e0/4Q2"R4"?B=FLkXK-))j
%I/!L'(U*j`I.Mi:HkhONH4k8/8caD1TfmksK-1$;(Vb:qmJ@te^NoP/X66\DrUT?FB@"?kp@RM/S%%;Sq!.MMe(7YcHhp8-c-r`N
%5C`M"c!RW60:tH*2u(\WVtmCJ(GDE<^3TV@l+=!:m>)UoU+cQ/>PJt5J3O^\IeD/k5l10FGOM1P:cCT;K@A.qs1d_TJUHiOp:fGq
%g%CNe#Oc]7rQ8FEnFRBDos::/X5ErQ?N'>b\0#-"rtddpF"ON2&#FFk,Q86Hb?,/Mhj9()Xn^^MiQDD>h8\&NN59_>VpuEQ8Sm]o
%5Okm_rV]OIeaY"1kL!=fWFO196s#O7ln^aD!^,k.\#4;gc=>2#DpCk"3]hStH[NDJHb-^$A&9<snSUHp,!>6uTWTPegrJ9V]@S47
%dXjY(U;sFH[-B[DgB[(jajic5qKTm/K]-.KB5s=hLQSFLaXPb8A)!ZRMI0'Y^6rqn;Ah-MI<;(C+CcqRZ)&/>h9^QB!Pd:WMi@qa
%"oDp+n9[t&:g+0-L$q%]&&"hC?2WYGrp';$?[Vb=mF-84hRri#qW?b7KbrRKT7?HsIf[aZa&a>)&)ijVkN@>>qVB>=]Gdun;t/hZ
%+'imH^\A!^hpT!,X&jkdm\G([bm7LEGl2JB[f$M^%Ti0MEBCr5jVceYquH*&Pi:4[^>LpZ+Q\C.8_RBehgG%\l.^^bG(1Uo,QI92
%eMf.R^3`VE4XQOSph^Q2fDH/-(cF'2pnY94=O^,EXYBNO2:4X8KR_(<K1.p&o8Dd]q=3#8I/`d(kAQ%f*8\F,oo&h\G.`TJBkR!<
%^\[B[o=)J[rSei>\.Ge6<;l1.f?[g@e)Tc'0:rk]R)X^V02MM`e$jgSrXP=KS!W$k9`=ad3W7d[^O(+to_[gmZ+MpaMf53%reEGI
%V2ba^o.WGtWa7&TclRCLpBd`2>Cb>u5oZUkQYNc8X?l0T3.nsK-MEA,0E1b+ooGa?";U*)KYdWcgD=3-r`6p:j8W!4f5:B5ooJ_\
%hYl9BkHD;j5(%MLfu,>qp!8)HD;W#*qXinb:;XL8cQhe0f>'OV^A4Q%G5QuA#??0GNP#Z3^A@XEpqhs@=RkEA5Ngq3YC?<<48@pp
%^O>_02a;`hLGt89rUB=LRt#^4Fnango.0h%(&dhr=$QV\khSJB*;NA^rre8frWMuuoE9XNcnbN).ocT,q<?MWT8o]+qWWoVIe94t
%J,O<3h`^kc-ah3!HrTFHIK&ThS%#Vl^V9Q`^\fV[KA$fV4.cI[>=craDM$d/JA8kY?_f0dnDfE5s.+_4[A-R-[J,d._-2,bZNB$\
%J*Po%q*$K"(l2&*5=HBu5M31)7c1Ml(J401n9<9B:KLfQc0ISYJ,/4GCnIbV8-aV>o',J<Dl62*q"F^mHl3-t`pHBK'u2VeFNM%<
%MZ*jfN#d3CmWT3,1GF>t@$Y-sH*\[NqnVj)#;u2Hh4HsRHN*d,Da/YlI-!$0W8QQ9T>puBK._.:&*_a2l-#DHrqQ3O2h-/Dm-=B>
%]5R^koabut]_i2/%+?cl9Z:_P-.uu.11"TjVA]Drh,jcEhJVa7FVfE%+Qi:oX+,Edo#8)A#0Y-3"\*Em`_J/7)e'!Kg0>Bj85.rV
%iW,Q?K>*Q>(]J/''<3qZ'0$b!B\D?:HM[?kMe=]8s8)V2h`ZJA?GgjGmH\,lk#M'l-S<9Qi/19i)aeVk`5#g-*VuF3q(M@ip8uZQ
%NT@%;5o+(jSl18L?K:U"Imtr(r=WF<`3f!thj+(jLCV(\DV(\r?/A[oJuW$:n4Q+8dc>nsDTXlN](u1+a>=\CbBolWoj=;Bi-+kS
%?f#iRiC*_)`G<gdY($?*Mg*KM8Ft$`8:'RE:b]padifq@9o\l#UZ,XA*osLi^7OHL-VkdJMH@jMZ[D`GDkDLW!lX[P6WYa.-5@;8
%"kHSFkm@N"/,Tf]Tg(qtRAnOBd'_hNqu[I@a'Zc?5RI._++N/D8A@<:rV4c)5(<-R5CShYPX@W43C,Ym\Ued**#)aHJMAYm8h$#t
%LXP,,J]\SIM9<BaPaf^Sat8qETR]L="-c>&eVpr*MP!A790(KHPZ'_D\UeeU*#7?r,GW"D$!*dbe2!:Ue`,c=N_iL<ghth_]Bucu
%*d`L*/KJrDhKnY2Id6*Fdr,cbi"+cXktcnoiPhHgLN_m:_r4/J"Qb$@3S\((3[jtETYt>$Jprd\Va86%WQ/TSR\:)XF/!\=hT4SZ
%DP6M!Dm>hfae(g9`;fGT<!&B+O8^5i2<\2,hAoW[Ucq]VjuuZrV]KRpV0C;dKCi&gW4c?@Iu7&8Pt[AOPBL\=qWT++Wdt'j:\Zq;
%\UedJG#USg5UB:D"]qKV.fE&E6%_O35G-7\B38=3Ijhd*oPZ-::5Xf.Ik(^%T)NTa_E,'D1Xpl,ru[ptDRVtD.fIK=]6'([=&4;c
%^;]=P\qKNqKK$%j%LGT.7gN]9`p?+RfO9GQ]Yi]/B?bkec[X(UK1j!>3\7uaGO&\VqW*S'rW%l?2Y[hM#+!VU=b`Db)a28M$!;2,
%YX)]kT&WPX8M9fAjHNS728R:dD0^`pEg'lfZcbObn;n<f-"JLL_2*.Ecgo#\QsV`2;.ni\!B:\j$Mb5&S]?fV%Ik:r.%,<NDo_lY
%ZSjug$e-#d6n$/g4:$J.2ji+tbj1*tep6_ZcbSNZbj?"NoaQ9PB=@kV5/INE\W&:;33t<-0XJ5;^ml1-9pj+_-KY_Q]>LUh964-A
%[Z3=CE,HqiT`Ztc4:MA:b5p8>/TL@:)fY<LE:@)ih%;V)El6Ab7VD+s_IViaF;o/9XhT!;[l67Mj\N\E%QF?`_')b8QqO[9!ceV,
%$YSE'ap#r?s1g.(kMO)3a.7^1^#*5l(YJbtqZHD(f_`b3:U'^,Yl>XnXo]Rc!V[&=M&i*+2o&lt^hD[/>eEG-/"AI7c5\rpr5&&1
%30DMU?:T7P%d-fF'.^s)LcrPC4#kK&J4Z>_GopFZ[YEr8$n#G!Yh/BWXLBlmB,J*&N*_8o`(tr$P2H`Y0X;2^_$u@o$-)g,B6f2n
%c`gnECk?qOq%h9U1X$3M)XOO8F&ZSGA_:oD,/W2Xkn\I%).rCEqm*+(HaLRp`=Ic0Y]jBcKme?'j5Lmd0`MS_6%QED-RGjN!%Ys@
%>M/sg3)kDslE&BN`jWC9:Qc?m"=[Z7dfOsX!gENV&cSF!b2m/&%`EInd\ScLeOkr**Io'=/,G*'JeW.BEIkCQEb"2^"dTXW#T]u9
%.-4j59//^[MC8X<a^km:Fosob`f\9/Mmq]U^t<G5OWoR$3*A763T0hns.ON`,_"EQ]=kn/_t=/l_VqqU\FjigcZUAr[Dh#"oUQ56
%D;KW3@Gen-Rs*YBIJ6%>%T^@3]+<\>(\SiM"5.hgStfF]=$QKFpm..p#C-.$[>/8ETc;<jkW/VLhACtOFG]@e]Q70s3\%l?[-H4k
%7<6U_D<(CCG'D8tl)o"o6R>O+pU-+G/`X$(`ub)rX^?0lK0+'8&&h?[^V@K<fKZ\tfRH;aZr.)i/\]+h.6NH%GS9,"e3d.H]nfoZ
%'Lc":@0)OsfL5p)ddk.]6&\2d<Y-L4A;M!ggbM5[,V<h1$u\R6F/B3i'%mU,<rP/Y3JdN%$6Z9sVuV,V*MW'!9Zg`D0quO.7thL=
%^`,g!4HXYTlJY&qA/-h_mTWBl;tZ%_0OQf=JuO@CC)8C*Wb-gF[8Q2Md\Rp!.\=)CWZJ3f2l;b4_?U5[BGenV9(ABN^M[fDXIbmW
%#O3ZeTHIF*qDT,(/./%"JR)6jj.uO.nu"p@5UaG/]bNYTn2s[(0McF,:VNi`6TJ\rn2s[ea,`Upj:a,naU]9j'XT=)6@crYLPnTJ
%8,oim=R!odP,YYo&a9Q/FH"%%7Rn=;AiHNBT'/^,;Oq)'2E9E`+,)UEU9d#l[8^,OP8n]rB!S)Oa.48rHK<2A8'3Y1I!YGLiAZe3
%q#HqG^fgTB6P.i(J_&HL+P]$chffp#(^^N-"-:?bYVur;+"Ht96p;:XDAL+G;UCn%]NM``,2FNg6&d529d6*aD@;"i"goMH%Dg8A
%0fY=j$o@EhLg;3B%1ruMB:r'9H>2^Y<l,ph"`uRtY]4[B,/]+egD*ik:*hYXA<b/IJO*)k)LlY+W$R-VktqfB\iP[iNPrE5:Sp8V
%2+H=FgKh+o:LpBU51,oFHNeZ3XmHn[0.\+.NaVrcpPeL.C#?/)GU/=!@0c>ZR<K/LO(`'nG7a3d(*e)@c23<?'(.(*o":GkW8udQ
%qakuGm&@,&ol(57HtE\^W!'GfATQHP]8eQ[LK0KK#KsNp>rhA+.bMW![^+_=Lffp+Y>gc`W(N7M("*>Or13t"$8j;3i%c1g9g?O9
%fI<iL%4r*6%b,_]4V5f`%#:F!92(8OaC&S,<<(1`qLHh%ZKY@uLoeQ:9Z%T*Jo#jJd>2^0>?9RJ#:'XTB"_FAE+[m"QoQsGB(fkH
%s2:8bE55s-Lp[d%IdiGuF9"2(5@KsC+=,m.:!/Y)AF0J-s2WORMWjj9Sd<1^HT_FU4!KNfkjR^Zb+;.g]tqU+nmpZ]`8YR-_\DnV
%$a(TpMXSF"KI64r'G=MJ<';kP<(D^'1*.L&13kXd=IiQa*+C0!"[dC%]EL8;FWn+5<'8[*`9\>-bK\"H].V4KJa6C<5hA=ZX%V:u
%bLOEZ"B5pnlIEmqSCYj1X?nkXX2`>[Mp'WQlR%O_;Jl(H2EitkUS1/,@qno'[1oR7]UiM"JXTP0KBX>Y)Yqn,T;K?0AA5Dpb*&p3
%,Hih(M=We"jt06c-S+1(l_JA;aanLK:RS!.(<'WM*]*DUF]"3Ec)L/jQ(r#m&[/Z%Xg=M=B-sYefR91cA*.nXPs[]/H1EY3;D$H@
%,ul!o)F81I[=K'IDMl<,-CBC89/&$KK3dShIeV2ehG^(1W%mb*Zmk>.Z=)$)-:rb,]m7ECcZO")Pa:i"=YU_glB3E#JZr[:Q3phr
%Pa!A$Q\o*!IRQ+9MF&[_Su@(Q02*d(_<`GafhpB5CY[0f[)t"4Yrj9Df:j6gl5sTRm8enL)g[d5Li>j]D7^*peV(JL!aV#mOd0Nk
%8eR&*.bp0"?3EA*nVW0KM,o(%onaA2<2&6PBfJn_8f_UFYW>d@O(V@j=KMqKjW,dOXBqH330'2c`E#L;4(^N4,402"$J-j]__Vb[
%;&$4m!FYS9;SZ&rru<\X_ks)gh%un^V,!jh):g)kbaWRerbe9F("@*?Fj=o9$'0A(B>09?hI`Cic>j)e=RT7s[l&-]XF]agJ/u=,
%"nOdb+]V^t^tpOXCEVm\J9AaZ[]T[<"75JjM\Qdlh-SG-CA-UK.!/q&24EmZAs$\pZs'<V/j1c7l4FrCksL39(Am`?Tq6,N9.T\h
%:;-<15p*SEC@r+"9&)u&c(kK;qWg]>4d3a0+B>o=.(KlegtZ80``Z0qQIAShDTZB%W);+2\[4f);^kYe;_a%D.H4NCdG5>`SBLP6
%qDh?oqFO\13*T@%=63^&ppN3UQ=nnAh"#1_@jBghI%!j-q)%Bc.5[Y^oB?@8#YqE[SNVmPa3L3kc,P%X-Bg\LZN+qei&]8ECOS<W
%QD_t+),om\R8+UW3\tu*f_Mg5>o&.t=Q@sKrGs/-m`Ycu$[.]?g7iP)8"Z&*%fVj?HouZ83&?2XSO).nhXH9VOPZ.Z,_M#8[iO7'
%%Go9,FaeNd`!2l(Q8aZ<,*/T#hoPMr--D.dr]V8;50;,@2=gpZ4W/b#Ed27pIP^dXECUGdmo\n5/f9>EC8Tlsr"TMFnHQ:ZLH0-!
%29dN83*1mB'ZLL"eP#4>?GX\,$<aRh[$^=XKY3H#h9PE8>S^h2`XLJ<lCMXW$IkG$o=eB/Qci9IhrmXS_AjU*F*JUj6S&W.P+i=U
%CBE0>S"!!WV_/mPiXJ]CF?249A0t&4PrbSf49NVFXM!4(,n;BOn@;(']he'qK5(>*Z&3g<Mo;UKR?kem_u"$qI]#kL\hhe3Za3SP
%GCU6U32'),YTk^?1E?b1_%@kB^knZ(\lI[IDdpC@MLCBK:h"c\Mc1hsd]bD7l]PG:aLWMFUgZbk.tQ=Wb^'C"*>g<]O3E3_Z3+np
%84B$bD6b?+L*ZgFUsmI)+Ef':/NG>1Z82Zj&WC1RNQ7+@Vn?6jo.amQT:D8Nj*FU)ihWD`3?QlTF>W0RlKG1C6a+]KW%L>Xs/D=0
%/j=IJ/N*17ETYN+4MPjrX^-3X20LD#'T3Y"+j#Ha*L;%['se41btMs;iG"AtRpX=F=f`#LNgMTO3-dB*b'Tr+kp4m-Du,<"^Qj`9
%^O&5>em6b.HS5l(EYmSc]h!*eeE)Gb\8L@uW(TXt"2;)T9J.+XqXI=6d&pSFjOa1nNeFBLmbD,:]acJLE2<ZZhMp6m>n0fJ*rc,/
%3;qglOEs-k:J6blBBmi2h(@BCiH@6FiA[rI5CN5&l`35WkmI#)lqpiOP6LS8O:QlXV?f%B!uGo?!9[,Z2=5R7ki%ZR0gZ*M6e.3;
%E88UP+iadK-g8WbFCZBXaLLE[JF,6)NTnNgj5Q@7MBIF[#.0tQYp8R(B>FoLSV*-+OPk+J'T2uqO2/jW!Eq9"o-g3e&rY&NS<5\$
%a?5LlREb9CLk,j7$c]>^B"J,R581Xt<i^&F'72%F9'hehe\jol&&G?2fHr=HAsB5f.YD9_=S2Z^&1(M;(g-(?gbKgT`_U_T3JL,&
%/;2>))<tgVAGBT.TGm_p1_g$ki-pQ-3%mD%6Vs>meX82)7`6-s2+fCOif'nbGL^2$c.RuO3DCkki%P0A'pq[#;3'!@;1iEcpkMF\
%S]u+@,rh#Q:XoRDQuZ)=`lg\[=!`8)R8A%f9Hb!m4BDlCa?qi$o+"r5FTd&E]dr^bU1h>(*sIET5a\K,S7n]qp;SZuk_)A*(-WkN
%(,CK8i<p*$ND*Sjo0K/&O_!kgHO+IN\"p4&\Z1g'_hP06i+4fG!Gd>f+3@c1,gi]UYbn?37Zt!dC4IcEYet*182.]sOWa"Ln3A$]
%7OiDASk203jH`6<.'PGa:UjA#P:45[!i"u%3e&>Z!3L1d35Or_;#Yfn%fE%(KBPhA)/"Te1K^:.q?Y`/d@f&.[NCI'W`X^PEE*^n
%NM?h>*Lr>sD]4Uf"DfdDA<'fG:C\Gf,HQBTK#qF@%ZWD-p,o$cS0;\#.&cnY_T\)cE2&K7Z,R*gdInjGiR5XVmtu9S-4Yn<r7ZM+
%H/UMk$SkloBGPQo$q0.b*K:_'Ab4U#N#<U"!pG"RbaFFST,(n?6?eRUs%*4qCKqZ(fcL6:^(9?7]CmJ"c?h8.&R)Cs*UAZ@g6AHi
%Pce.,o2@BOD776cC'T/'hIC-fDbS45D#5p2?dqV!Jr:;$g`A4':S.$bK9)Kh,GsB)%%^)#F,7aA1Nt9A@R!fnME'6A#>HU2?%u')
%6RUmSk$N]?Lp?3'OeEm9:o4fED3Me$9a3uYEroE?Ll+fT,QeAagG^-4P609ZE5**3_\bdoS>aQF!%.se:_UZ'fQMd>$m)VR#:M8k
%TE2)YgL9ZQ$Y[=dlp;-Rb1&;:\Y^M'/i[U"#M`E8XV4'fTW-oBc=XL/F4CKdL5ETK%>hL:bXp[L'/ATM='[Jbp4E,iIPqc::TB*`
%Y[1UMPj]'KS)eq&-UAi?3m+&np#4g,5ZHK->mb:IM[F/1a6g$NFE!P/SH)\@JlE^:]E1!F5D@h'p\\7%-NL@6C&VfQ;2"d)mlqC1
%'S?]Rc[:IK0DhqJ5p:6K6*B=DA7_;#Kd7rARC8*ZEeuXiM$_b9'8u8#;JI6_.46Q:5DabBb*at%,(0b]K?DG%jn=,A,O3lDUl`NM
%1>dFC:uh=5W,=(nVsMt/TqB#E"3GVV[J]FA\BkY9"Qn\^Ah!bqX)00?=-E&7\]7jia)XhH](fA5M#aQDjJ,=uf`sT1O)gC%%+N(b
%HC?V/>u"<Q`1roWEnSPc.p?iXA0!AMBP/lOHS;(?R3d^3!lJT45:3AjY`VjZX'u\Oe2X7p*V@kj$,"$sP1<'h?*#^/pg-)PLR#WF
%)Q2-R7m[4_3H#aP0%N6l\[Z*EZp"R`GR-:K[>/H!+pFLsMEsi$7V4*r11PS<A[*1G*"WCE=\.Q;,&?"*U`/MV;.A&?OK045M,lc\
%)R:Wr"^-kAZrrDm85^iW/u5IhCt`=VoNOBRedalbk:N3K_`bK]3\-P5h3:?Ug(^l:^OQ0t*s!^LcgapAeG...r;5"=2j%iA'17.t
%E:At[i#8M"h"1E(J*YG.a3Xe:rrm1C61jk\d$F13iJ,#KQ)j,@fS.I"_/]q!g_Ak\/gTqBgNX6$[o_E:\DNrpqgD#Q\NJdP21Ed4
%I(;M%p\a_pb(&Rmhif<qR3lFrO4Ai`2WRjp0nLYt:t^XPT&V'I&X%3oj;&fY;8s+KjQ@7[43V*=!S)oCB>F4kC_,1l%,N%:rNQIR
%&sXJtLL\)$g&m4W/>:)[dA?1EnRm<jo!_QX[+`h[i>ksEL@sqBAY3DZ407R<X4Y8B4ctd;=),U[8LotQW\mso\?=KEEIuna'sPG_
%GWV5*N_9[aet*BBFb#^Yi>,(R]u76a$F<h7mH7:RoS+dT^.gm07?%&D\oN%?O@%6mWl?GMYRFD-pee;pRh7H])Yh4EoM'%@JK"4E
%H>e)u#ARLmp;(Gb:s:`efud[&`[-RZqTnJW$_+.Hh.O*#a5BQ7*)@"eZ4T0q7+VKnSp[1m6XZ>C@EmX`7t7-,cq]L]j?DRO'O(C,
%kjK=2?N7R;][?+MH%EWF#u.kM?pA(fD3eum)tlYtM1<:=6#$h7D2g[!G^Nq%afSaf`'21)-M8p8Z@FZqM#BhV8R!%U#C[dlkrIRm
%`PI//o__sdS\_TCN,kY'P'k^BM8S1VYMS+BK`,>*j@<qd`o/Smdi0`^,r]0:64l6O4NgDKRH@D5'[?;)VZtoKR?R7c??=:VZ4?XM
%iP%sXDqfO*k"p'hhZ7@dcu.]PIW,IN+Nf4grWmTa<S3SNYobLfQGrfZ]=Gbm*U_b)HPa,,]oER7J.""!RAseac@9.Iogrt3+,q-9
%g6jcP%V-&*dnncH6*^?SmLoC.S!j$!f$SL%2]"Za>K%@46CAZGE=fWJ&uYp8R*Zu5"=R+m+cX@*3!##9SO'dh(2h4QNuuJU]Kg<N
%DPQ12VbK2>'k+99ZQRYVQGuO:kC.tDa"ifWZ"e6a.@N%qUIF>Y09kE/aS-EZfCZC'f2"2SCd*>SZ_'M`=Xhm^Z/407\0t"Va2UA2
%Z%h`;N-n`qNYK[(Hn;>^K5H&;bsAIo#E&ukQ6SZ#"Al2Xa?)O?:Y/%(0hjO6/;Ln_LD42dD^SqTJr,)2L(#E)Yb2"")XsB4m&+e6
%_>_05#)eDs.JGlnL9jKu4[g)\ghq)-[:hEWiUWCn)br`MM1C9een-;i8[:Re(tB<m\/(c]"fPujJ>LmT;P5cE^k&)qV$@B$+(<r0
%#Y&TLkTS?`$fg"f"MG$i89r;6$PT$5Sg!A-0kZ;%pVW3oMSdV=FSHjKI29(7`9S=F_`C#OBJ\2<IJhUhrOep"l!bH7.Ci)X>sgp4
%J"?@ZRJ(kN5N_li,7X3!!V.uLEh$#MCEd;u38<T..d'7JS:'sY&;fPJB2,;3P87;\3sHhH]iL(r)plu1K1/'`#5K]!Ks'@@f]gVS
%f<*sk2"r(cP[jc!$=^>42OqY?9!pF)Y&]Crk[pDA1/5<T(FYP3JX-e#JQZFbquR867S=c"EnXp)[YQJTThfnb1?e+i\gg6$(J6$u
%>hUe"4XF>_P#*5*+fmEuT.]2QSe'FdU0!k:^!@J(5Nb#Ue-%#L$+gZ!g=iIs->[U3I/+4S>S.:Yf%'rBHIUbm->0Tg0%64FZDk]l
%-MCEIUH5ZNhNG#5/=+[9#kU`&g+5Qn,b`F\(1YkO73nXih1MBK)]aeeJn5V1K&ms%W.pMS1b^QY?mo^*02^H7AQD[`7(8<^ARM8i
%RqQ0i&^@&QA\hRSibZi^,_IMa7GJ5FMttr^:s,&&Z)\7=`*C=rXuacHL>l9:\N<S,jfmYc9`hnm]V>hc5jf4cAQG#nbSqfgK9;5X
%W@0,R_:m]2qq_4bZ^VcMp@>T27i]cY5"LP-pXkUOjUD#UU'Z-5bZ!i#7U5JteMrX9<ud'@7$7^e;PH(gJ\Y$g30470;iLmq58"8X
%`I^XH?eCKsMlK^]+`G)S)u;?g%H5$#O$Sr9Ga`G>,=F5C94K;*L9$/GPXp1]WW60i"33'b"UE*c_+KhZeLkh!O'^BkY%^0!s(U2H
%lb'q_CII$9[&@Jfq&-eAG2Wm!+UgA>jRhQ)fM!pES]I[o7&N6<b&QiF#+$"ja/tImpS7/r[b-_aG%@rn*<0W8kkb5."b*f1QT)>t
%mEAo'aQ7<hT>t*hmb*fi*NARFpReGrg6mHX*B?]*A#=,Z\X6/N7q8ENjOGeGZXd_rBktdsBpu:iGtVN@(B:IJN+NOq47gp(g019)
%ql7#/BrXLZ93LPr*;>U1"Z>-]3ScLGr:tOqWI:c95@9D.f^ru7jE"#kjkC9j$U_5VH/L7TiVM,XCp*PQpH]J2N*Ur]Z.[<I&8in>
%HG0\o^&]CE3&mBUf1*ojJT@,#'i(NL2YEhqV#:\m1sLp7RK2I>,>#J0/G2WD">%O?YZ+98d\`6[R[3&Ca]V(W\WL-77S:ptgKi;#
%g-!'gk%uj[heS$#Ro3>__)WCW>h7$nQ76:/"dThO\4m!;.:hiq(XidZN=Sc>o:@.uO%>Q2Se'Fdk/P="/%qLtLa/V%`\rjA`u<L,
%l&J96j%_>LQKZsR$_?;p0./`nR;RXceGV.KYq6T(05lA\Oqp^>*&$c2ku1dQ1\p)hR?Fm-'m!jO@[q.\60RG-=4VT!N9`EhS7cp;
%b3%t`:7Jc-J9-"^b2,"GK:C)47!K"(ET8/lhQc,Z95_#o'JkLU\(-?pm-oZk:9_nB?u9fTrU,,ah_)CO4RMYLgrl&XYG<)mh6!;Z
%7nUo\c@*%EhN2sQ2p<.WfNHQPG322\2RXD+lh-aYZd6(d8$)9;;k>WCL(TOBj:P6Fp2mduS`Us[-#*018UckVj[Vr6\@g/0YP*!"
%N;K,]-gR&4S',n.IJJ9DmrB65mbqjbDg-,tN"=,1]67U8mn"EZGK9qthbk7uO72Gijf[G+g$Ofjp+,LN^:Rb(BDHkW?JMNa2YTu3
%L[<Fs4&cjY54$dId*CG&Oe9!6V]E=,P,I<=Ccu8)^LlY+#d&>I!V/FKT;aj&H_C0+OA_k28:!3%pr6qKh7n`ZW,g]7hT.W9O?Qa*
%s'[juI-/W$_r>9pHf:Rjr_e0ihJUq^'](h;eQjLaPN4h=n/0-"5,[Oo"`!:IP2sU;ZM6;@n&".>`@mU+P$9Ou6ui<O]-tN:7_kh)
%q+5r9VET].ReWao.)JH$]-tIo#Y-M$.<'<"5V_],=Pm/a(?6/Hq,%^_bkd\#<l@RS;jM>\VD%EAhEnTUh4/kS'ERBD&i"M`'THWs
%Nf8N66Y`*&r\TCC:PnL/B%/_M7Qp'0^dLhR[8<`r-?IV4gVp0p+9$Zg+\;erDib$7,ma/-DNj2,-Hl$-QW=J!g>839YSI!"cALTR
%iX,)rVU^js-Csg%)&GJWODMWb[q3,aqpMQHgVIr_/X]HB^0del\lkj^S.c_,bbr]4bbs5l>UD2nYaeRA]7q:PTPI](5>D=ZJbRSF
%P1(uN6McdhQ-a5c'=:a\jth]f]8O/Km"J=V2o$@G<e8anNj(=[Wn.-6JoA-WPI!PrS[NA\DGu2`E->H+&MiL/1i?g"ObmFG)kRt*
%'dqY7BKX46)lu7qPdst5FZqs3P.M_DQfTO\AYlfPS:K3aPYh3/lf+4J0M6B%.pqVb61]@$<MS=)cZ[&bc5PR.NW=8NGJ%_"@SM]I
%NdQHj-H*QNf@=u1#K]/G.aRL3bRN)EOLbm>TD?-D<'PhRJ+C#Y:Yka>dDb0`+*gMeUf=uSIG<k15JGA/Qb!&Fc0^jejZi$Bq=i2c
%\jP(A08>Y?F_mJB(rq-m,Rn+CV\lgA(@NQ7<99[pB$7i!ruoO"r4L0e7"f.-ca0aRB:6&AZdLjSCpOIh]21O)@ObY%3q=g+&KXj*
%?HY#XCu:\`0e@>pP+W/_Jjn5'N7peEW9l#u%gnjUDQ5s!8WZ'^c1%;[CSS\t)_<l!<];4#Y[*>BJg(?GFd[Up_Y,I4j[BH&Ilf@@
%Q(;j-Z>b$qBD_Vn+5HAr;D.(a6b%JIqPoK)pAFc2l=!q/lW2n1S2+a'f+BU<k;h1[/UCET>X?l5?G"dI0?qnfbVZV/UT4"`3fTsO
%G%RgL)-I)b@'DB1m*+[djX"F^I"GY:h6Njm@:t2>UO]:o#uXaLg%'c<60W*L1!N4gJ.''VgQ6]6:d.D]eHhu!:o^J'HCH.%gudE?
%oNa<_E%pp?,"DiDADlc'j67mISHW;8O?(ZX<"u4"hpj@D;Z#iijb;g5f%f@%?[U,MnBmojGBdlB1\::+o0nto7?n7ulK%Ym_tZ@i
%W+UUfrdDE=CC6406a$YBj'iS`@p?V7r7:Y,^AVlU*nKZ1MTn@=U/JtAXaeXt)p;J<[gSi%q!Ko`a?0R2-%35)05"dZ`MiBnU(>Nh
%5-(@2XPlfN'r[Qtj3f?TC?D$BI!J3*L5]?[)/a%X62RkR?'pN+*Hr,9Smq1'mB<I;7GOG)NRc82D*li'RXXV.ZX"DmH&ooVA3$?`
%Ajkns2)QW&f-i&b4;#a#+^"7F$lo2.J,F4:s*2%mFX?#FR);!gELNrBdE:GgommNED@/:4@iTtTItqY3cn$<.+%3Gi."k1.71/C4
%r@Ui(?.')Leg!jUE&8H1-7d]Focs4,$"?8]I;89#I+2o`eP"Q]+uXELBTD@t?$^1gQ_ph:1iKdj$HEluCbctH[<5,LW(DO3H4AfT
%Meh'bbf-N"8;1kTf=+JO`gmLm5Yeoj$Ff^$D<m,72VB)GlGEf!<$R,SJnUB/5'0P3?`2>LdV57e)5uXWk%TjWT&ZX/#cc"_$[dD8
%G]P:Kkk+e@'>i'em4BbO>nK.gJ[MKLWQ]S5NI*2=#]KguaVFLcY,,L@<q!Yd5*!hN6&XE&;*X6\Y>^iFk?lGI$T6?9FiuJpl$USd
%WcfE+^aI9Pii!9i3`H#9YauJ*bh7qnk_4WD<Ph&@YSp(PNKXsDh'<t@XKKm%l'\2U;Lim::%KKfO/3/pW=bWpTt3(M3N(0"HR?>U
%$&pp';E5[[-*248j3#oZ]oMPTAo?!^9s-,_-=\P8cBmR.ji0.]T!g0;`tLfN=Q;Ho[>o1)%\X`%94cl@/Z,nVhPgF=>Vpq/COQ>G
%X>)-l^PmL%,&0Eclt=o)]8Co[)>-ar9u>Iu'0EikgZHHpRhc)*5ah;=&h@D"*[J'U<*gkDdfUlHLWca<lUFX)Z70opg.pmR+n"$-
%mX3h;D:te03H#8''i<r8U?n[Fh,/:&M=0c;53o(nPp9r'g$nYZETB0%jYD$+Q>saMAup*;UN2cF9/CLG4Hl,8p\*t5Z&=a2(OPmF
%bj:.S<Th0">Z-!U2+FX*DFDs=(2<'h69@4\neA6:B5]>A<hj7=;NW71iVpD-GH%jT9l4_`*<mdW*:+_"d>o'sdB":dAoo5t=C(/Y
%iZ'XhLijllKME6[NEX]5EuZbYI#_,T@a,aARp%oKF!5_uDriPFGHB1RKt;LH5qfP*J2T+fVMb<X7j$d/RRE2WjCX9h1Wl,m>)r54
%+G8@6'Ki/+L;8pi_ul4UT(9hmRRGMFPE"-j.bi%/j38(ZHkV^L[aiXDf(6c,G7!HDLIk9`X606mguam9&'_g[/L-+>/k*V[ql]p9
%DIGXde[q%<H@'7Nl*J+/>Poa<D=hJbH>WJ;LJ;k."a")'7?;$q?'`ARA%%Y=43NrO^UMG^V8iH]VHZInLp(-8n_J[bk%+"2r+H8k
%gVTp2)K?p.>Y3DON4=soM^ag(NjY6!/T)m8Y!cD`Cqf/7(13d)8VKnhZu3"R<mt8KXLSZllgeWXgT0BgV/hbfDBP9']0Q@3QcB?A
%353J1nU+b-heB/5+nc%=d7'+:V_R2tq+fs@]X(E;6)Wps';>?K:F]iCqN_gQ_i1'^e<IPY(M.aPM*Qp.N#RJYW&%/cM:Ada`,@NF
%WVJ%2gq9`Ngu[qHD9@N\=N%in9t$ku&VN2Io]qKj8[q2'XN%EeknLVK'1KWi)6=4L[mmh8n+XYoojrO;NX$OE/ti0fEO+HFNltPX
%&M8>iWLk#0TuN5=D`;9/f>PXT0CQOAEpp/(Yn06]j/1#M*LDq?@dO8%Ar'<FfMStRQsY0l5*oEY7[C-<.;R4p2*PK3V/-pb;QH\4
%]mNuK9Ne8fIOrWg@@$"`D&tV!ZH/3:aH&2][T=?O#SOnsMKN\QkZBHaBh>cOYStTMW\%T!Kse2#cCZb7RP/oS06ip=Ga^:1RP/oS
%06ip=Ga^:1RP/oS06ip=Ga^:1RP/oSch@0dVtkqBq1>O?oGYL&BtiM>^>N_n_:+otn20k`C7rR'?-"cT21)9j26LPLXNBJfQu-V3
%l^B#6ArM_DC_UNW8/nh@RC8pB[Xktde.&V$._$EdhF*cFBD<FsVa*ljWOJ'b>ShU=7`"*f=Z"<+6T(ciQPD'\Kb?^b^+!%M#h@`(
%2f]S1hYX;HHPXHckbUXim1>f+C?6D#^?p&1H'.&LkQO;=kPiK&I;Dn)?BF.95Zo4uIm^;dFog9dj8M$krno(KR!<n?a05XAKa2'h
%"&Fj!g2h`?q>.Cn#@VbML@)+N[m5.Rf=elX+.,1on%0ZOBm:a.#hjhThu0(Mq6VWl9j-Xn2Z+43Dr-!<`MKk]:R"GipQc%.ZH<N:
%2/^8*GL!,dV8djG,HJ8I^",):]'=%((G0Kr_b)jl6Ih)TXJ1W&d2aip<T4t:O3L78:1?)NDiHb1/GHH6kai[&H,b#oh*eT_S&UKs
%*g&bTd-PeoOY)'.i3%;'qYgla&S;u9)UkR"jcc*un!-q(Q^Z^8o@nVfR_E!no;Q<qA'.QK+u-d/HkN=F*`Sd,$Z80GflmbY(C5T$
%T6GH/PC#[BD6I)I?EUf_@(Tceg8eXf#h(`t>X#bf"RagIUNu4!"Re\dXsr)aF;Z.Qgpd"\n(\nq]g6o32cSi&QJmf>Bgs-2Tb1@;
%JI#O*(,,KUp/$]HOj*%X08EQ<BG2Mlh0tbD#&F.tjYA@u&,jNHKft@e'2$pnZK/[-r:dXII&ZX/NF-Gur&&pKY5#]O@aJVoWBkIA
%qm/[,YOKL]KX"sE8UK>jqR`6O?.$Y*hH1B1@`NurBRP*B-HH!=bPNLZmnP'.cmP_jY'iq:3@/_]qgYt0T9jJ7:o*Y;kPI1'HPa?_
%?Lm!ur=*j+omDP4q?D`LjaU@MXQ=s[I)$!DiNRZ]K4TY,E39<'qOTbgVJP``q;BM%1AiGUB=`>C_a;5s88FF5GQ/ZnqtIQNXlj4p
%LDoX+%_o`miVg5EXR9?hd(<@3Aq,;[Nq`i&e'g`rq?:[r+k$r`kk%#cqA,d=K2IE[0Q?<g9.jZ'#?lOFXO!8VfM,3&)S&&,f>fL*
%0+dI`MLC%dLB@@rmi^>N\Fq5[I>Mp8msSALh3Wrl/b4<Z^TUN(',mJ1GT<@IVJK7lm=JW:=#]@=/WP94EOVaSfY'"4bl+i3Mkt;!
%-08AUoe2U0J9%#p`4E]'+'tV[Felf#QGqZ"TB3DpIpVmYY4>Gp%JN7L:i&L(+?:JYiG^6g!Cga7.@]).f?k)oqn[KN*h^O7:#Tf;
%QW\ig(8e\i"D!)*$-O:+p[8Y8jgZ^o<Tu$h:g[UfQB;j8#`<15Kb_44Hs,I+m!Z<K78HttliA\^r03,<N-1PU&`$\UQ`[H509ThK
%aDiYPbJ!HHl_E:\3L.\kZnD++=t^]+n+1Ud]^TihQL^+UGAa^sDEd]O\&Nq>P54sAhEsS\F$8L$0^cNfhPq0kNTnR^[WCQT-e9LK
%J$1T4-,JKpA^J*bbFh6H-"Y]u/t:KZ@r$1u%=YgI#64=<:)*W*<UjohY[(/,puU@2BN/7gpmtPh'7"]af.@!?iX/;HL?0X1i7:,A
%9N1m@;j6JocV/GQAQGQ&KGj=U'5%FtpnSeDAj1W0W0[0'^';_]5C`;$QZ$6DIeVjp+.kZ`'4T;&H2QmhhpCg1s6f[U]5R`AbGXgc
%4b*A*nY73*r:s^&jBl!f7uuKV!LLXkrpoj3GPHfW/$=?q4oXbs_,((<[(fkBX.N\rj$1/VYMgG8qpj^s:Dut\rU6$NX7dH>(37!t
%,EgC)WOm,3:1(S:e-5kuN=GmhkG7)@=?(Q%"qWWLLCF3C3-;P5QY@br5QgHEhQ'C9jOCA^pCu8W]DC2jm9,&_^piLP2Lh95$<*i@
%XNl1V;T7Hk`?>+f:j]rHEG06j2cpA4DsFu+QR_F3[>q\'JbPb@)sKDL(::nDO4OO<b3b['U.[Xn([Bml\gLV+Tl$7dn[cW.CgZNA
%?0YQO4jc2_#?XIKDk$IP.._>G\3u[02GF_YB#)SIhN>)an9j'_hS<Hl90JaF+7r:Vr>M\/gSW_JN+one0%-Sf:"+_):&j$nR+M@#
%^>!2KqNSImYOp(1f'Cb3)>')?\rU0jV6oXE4`gr+IssoUk@OKh&("O$pX6==WGLn26m(IY#NTJZI\@s,b1$*0quX7jHf*=3d22bM
%lFB;)p&%+*eY4SC=/>)(SR_3EWWmn)V,>PbqPZoQcI^UE[!h6KB4BbYi^@-sC".3\^3OL%>:tVHe+#K:(>C`1kfY;B4dYB%V??>\
%=LLqZCYr=P2Eg9I-:U.bkaSH#c*b&;i,EG^\hi8s^2.9.F,@VF)W[FRgSSZq7i.]SgDr*6b9f\AQg!b4ICh,)J8LdYf0.aQJ5>h!
%VB>J9U2QL'f*L$a2I3K59XAS^d$IIcH7mT(!tNO9Q+3CNIN=TPgUSBtjhQn&0$tJ0s4qT75Pg?FR37kL7Ec!nT&DJ]T#gq9hT2=?
%)?,^Y0@<9)GM?;('".?"o89=rWFXf?F\8<dn4U91h2;UjoFhjg/br:H)!\X(OGI^+bKT_cXPauJ!:/@:U@P&EpooNtMVK1FB]t_;
%+Pu0Uppbos/k?Em,W*0mL=QFqUj0ks'R1chd%oT.,l6g<Wor6)$>i?V3fItj+X9u_brIbAIAe]ZoFUiK2WO:CEFcpjqWp;Q!4np4
%HVdkJ6!>\)]*$<4O??SK?gZ93a%o#2+P1.P9,Qp/_F=[a'rIS[1)-!%clFVoi;Im_d^?s'OW'n5Tuiu(cK"!TOjZ\(a05#;FEt1W
%&1)]EWYAgf-(3s8\PM0F?gYT-1Y[D,$KT9<$<$uJ[;LJDN?EPEqZml,dcdL85h@6^nX%CZ5XcJD'u#h&ktUOuNTJ"A9F`#?+C9Z$
%UFZq90i5$8?=9rN@sQG*@ndO09D5rEU2PmFe4X)I[!jN]?Wcnk7iV5ak8(\=4mVY'@rAR1CW&M7]T]"[1h:Eq]Bg`j]e?s;MJMiF
%l+.$?L$6+/9Ir@4km>`0*W,bp%bc-67?Chc)YAuiK'Io2YCdl9nRA.][$f#3!I[Ion;IoG8P6qhS[8S;.uk>mF:%d8SaHuH\C$a>
%mf7ZeInEM@YHg.L;=lXMW<=6%>_:7P_n!sb3_r?OcpR8r3X60/H"X`JQ-Ao_OERO[^F!-?38DS<Kb]7h'BW3X6A0n+iogVp5EZ/t
%:`q/pe=-g8SVlTr\+X3:H.l=r$OgP`F>stl/3m0jKa%?+WtK#AM4'c5Kt1m5ej;cU7-6V/DD_Z$:sLlQf.eHMD-r:g6.C_=J]BaA
%L5,CNhY`rmM)SRMDWDd.g>"iQ8Ds6b:k5..e50TYSkG"K6Uld^i5LD;ME[FQnQ_d^/:0FsWm-flAUDfM$Sa(bHKV[H0V0tp4EP,b
%i)`D"L+L@.]Gq-XnfE\de)73c"N$%O?7gKIYl',0_E8^gB.nhVdD=tpn&'IfSLX9)&*oged+t.D89`Yt0kI7=8p"Od5VIAD1GNH%
%(P%l9F*/VBBd7C;_1iolVp7F^QYMeL`MG1#%jG,;'3J!aGVS.Ged"1&5oJYOp"e3hQKuhr:+:RX3u6LC[g\*h1l%j!bQm@"73aco
%i.^APJI!H+?m:CZZnG:WPa\:kA7$^$d:HcNE*.g&a[:@_5n:/eF`Tl43U63M0Q9u3M\A/\a>('j63$R;MnLOg(53#TW=WdCSlBP7
%fnn*jI`KP.@\ZlnZZ6G+_X\C3EI#pi7FgUt@fjTk4isd3$Sa0:i*gS&I3s_%k,^Jn!C#tQLu"FLA*OB,O,c7%mk,m&-o/%4?9^\K
%,1`GG!!h7M:NU&Z/@\3VV;OpPZHtm*mA%WsJnSU.f:gU*O(Q"C7Addh+%/FHTaS,_d+=XLSlAnWNeJ:>]GS[?\<s[e`Oo[r*>+oY
%Bk(^4WP@J^p/ehaBs431M`gtFC&Xs,GXW#Ipf/C$T`egln8RuB9EDgefV\:gB[p\qNYZ0E7'EgO`_(-iFbpd\0uLqgr0c4o/h/3r
%_+NM.V5f,$da<ZbF>TpMOVSu-9+T12edt_UU+`8G;H3.Ykq!;%<la0n8co@W8q_=:`8jpcLOm>K0HQ/-pkZiLR.t+:?O@l+T^$fh
%e=),Ih(r%3L%jQ?D,*+1,l*'&0D!S>&R/f5`fu<T[L/g3B$*)YKd8u-g:k_6IqiMg9=D$S/j^W-A+T81@LXB78bWP@i4KWcOKq'c
%qG"%Fd+gF^`ZeT>Yo]-\H#GcVZGu?J9<,!#bNY>a$<&;6TILJP-DINgK7bV>Ql7$hW"ikkHkTK$Y/HbsoueZQAg;V^7@5i)cP&&P
%(LroL^4Hn.5JaE.(.ZspWZF]A(,&o[@PO%8_sq@`9QcqoRacj#)$q*b"I1@apXlg259*_gC3LXmH>^P#KG.4S&&,(Kj((B9MoAcF
%(-RP2Ola:c8t-)2+?tZ9(i)i5+R8YS-u)asJAK;NWHJk/iC?BofOQ_/XG0,X)pfU=E7/"^?BhDNHPDUBoe(Y@Xt*;$RcWnSR[j_O
%T%Oki2qB9l3Go36;mq1bj't+e8ZtF'&09o:<9c(gMAH;H1Y""+02W6gg+"auPgAG_OB5*(blW"20ZJ4Y4XL]'qh^Psl:]SEpiM15
%ESc?NTYoLgI[HKdc`-7FW0V6N9SE"9ghX9*XTn?Hcs+!u_q"0*Im<>.P3IdV!@5#I/#?`P3PTf.8b!*=jOC^];&OWC[SipIdlQsm
%#!BGYTAUWX=4O?A=1)470T!1<W<)4!I_gf/X;MkoggrT>%q[$QE[Ke.nk<g)<F'ff[dh7hrDPgQkkaH9'rM4o6NQ.CCY4Tl9,fpR
%do_cK,R$RV\q&tR\>`$HIDYCqAlGfckP;7NZlG:VRDBMG#].if@aq+C[LAh_-@.Pu=f]?,6fps[p,ZmVW_+gea>,Kf;LG>^N]hWa
%PH0b$V:s3]jl6.FDf#RXmjM?Br'^DT/*4@rRDpET'6U95QL1'O@U<+[%YdW30)Yo'1@(8GcSoqU'HnWsd)AmP>gSj%a?XnU$@bQu
%;j&%aEJ[!b6!<'4KZRm'@[L5Ql*N^"htHfQ116I1,5%#tpe'cSD-"$jflBF]V03A$M_b.5rFuq.!I=EMZ;LWMM<SOhi#9Z\6U]io
%<J]=l_$6nK[7@?mWY>sSP<k#*"Z@hW/%3P3&VTMH`+fWtj`<`O8WONm1\.apJVb]/7`R(j[s,842rc\FA>l4J'&?s$I&t4P(0a-l
%%t5^<,-/BuFQ+fOCdld*mu`"87*mtpa["Z5$9bf"`[[Th^rVr\X(^2=EmIE);:]J1Y%$9N9i'1!.R/kq8_4kC/f5d5Zso(7E2L]:
%JP#E3.;.$FNHK!^gPgcUrMXI`g7V>bCVs!Q._'41NcjGKenWNO2[GFHL_pLE;XYlN"ua#s_*oH#Kbh6jma_1e`7T*A@ai"[FL%*O
%hl95;W9AONP!r2.k!Ka&*LVN0TQG*A0(^=gY@/VXd%8T03##u7[sf1R2rjLM)EQNa,1\\3<`_#V2D/mufPl^G&mKEY$/Y$Gn79Qe
%TBY;2?GsfPl4WEj&X7\jP.o%+=9kQmg3H9^iemAZO%/iuKd_-7.&.AB@,m*P[>!c,-B5iRmT7"Rr4S(Yk4Lu+c3/di\0ZQ4\gh's
%[WAO_*9k8u*pY;2[kGW7eCb0P#%B;,O]beBjJeGjXhIH9\9Y.[LF.,J!p;$j<0o`!&5Z#d;/<h/6/a<U9>QBLgYa!M:7\SR.KqFC
%fijBn"34%UrB\Q,;4Gt$(D/Ap0p^Sno4ZFo1)shp@j5TD7#]?aI]F!Cpruhi'WRO&Gi6s<04Hh?)iG8A$.sXgcE,2"O&]cC3kH*E
%(5%Xo64,hqdFrDq.aCdj`fli"#T[^I,![s>pD;\4_6_N5cTX%Ib[9U98?$%p@S=l23m8<PFd_8j%T,29S9l^r@qPkf0)S+#Q\Qff
%7B@Xm[7ch2k<n6K0tS^Q#Q07ALGQh)kU4TaUI56uaI3t4,a6]?hlc*Lm!8EL^Rt4"cCL7M$H]06H%`7&U!s#&2$q;6D1HGk%PlYa
%MsS7G-DOR_c-$035p)s+Rmhf[>T]3B`%2IGj(J'ZRL@SM:gQo>$<=.0*?o+K/>W/+di)D'7;D$q_#UV[q:-Zb,`b=b1nLYHTZQQR
%L%4,UoH!nNQ3=4?[)2):/r'(s7^b<F,`C[o+(gqLG1OX(YQ_?pEA`ls2/KI4@n!;^1W1?g/ipTnJDQLDc@F5t,#!rJM8$3r#&R\S
%9t$K/7:V<_m""T&WJQ,&40Ngji0oIKk*eTRO?k,UW"U?oBMkhpXHB5g8q_F'&OuXt,h-?0,W6tJV8NR!*WnDL>%V'aCdfIbLrNiG
%'==LN@o-:[B-K'Q;.]$'/(45ZYt;q8%V&j7c48he%d5]Y%1E^c*#S!/b"ZGqnTgc>b73PjC<,C?7-2:TEuD)PS]@Gq9JJ9+k6<5T
%*Cnno?(T9f>Z5=>.V`K<n,j?gp_1i7=+d4cGT9@+cdZ8NIUVj\keg9j4B1_e+/$,=`=PE^DK],ZiR)`)RQaoDR^nSH&V-7=3kf<'
%X9YnMoaQ`?#)0dk%pQGmjZ"T]nhI5oV$_)`b@s[.'LecDX!'LO]"5u@`[H?bL^>].j<5KoP8/m1o3r\5WWcl'EJRS=Xb+p3i"A;6
%@<2beec#ln'Gq@G==/Em/2sl@ME2aMUr_)UO$kf2*0MH590W?=*c8VPJNJr":5OEHrn1&IW"1,9>so[cY?8+r;c#eiZRQG<au:ZT
%^p]PaMm>=gdOi3;=tX?1fnb1`&X:U9KNIK6J<a]3$b8j6&':iJ:!1/gPAcsHD!OI"=HN$BWk8?VRR?c;nm4g!=FVF1:.MtD7FXu1
%qnbt+/ZL2%75&T<>G$Z+$)17U\n`MX>oWJKPLQ*fQ;W:aknYD*QO&:22\Gj1a%@"X("-s$l5"nmhn>%\lV6(W#Fk*?3[2N=_[lk(
%hTV4rWnP+BHBqi4pa"U6jH(*DO4.#eG^Yr%^X?=`2*:sBD"5H<r'[$l@8H766O*i<D*BA7`0!HS[5h'=/Y/+<-2O#hL-5_!BEXe:
%+fD-Y7!46JDUasGU#9f>,%M]9"gYp'</,T>;%Pq4r@6iQ`.C-SD.0%YL<3D+f'58:$p!8o,:ggMmd&p>7%WPZ_4ekK<-`O"?\XVX
%_%()F7R+hEB>XM>oV&(V__0l4mYBp@$lpmB@iKTsB^d##iFBei&I4Na;*<3uBBmA-Z0NplY.A+\<DL%^i!/t"@U8?_Q,iFRSVUoh
%b3C!GDZZqO+\nr:fZDkFYhCJTbXU-kRcHl?TF?l;%b#gJ_p-C<^8Z%/Zu#/IDG<n&g^JuS3*8^HHI[*Mg[]"tHK/ju8l%&p]Ff@k
%%D*I:EPIUo</B(\+;!51l]LJP*2.iM`NVoak)#O8e3KI$*1WFW-8mZ[4:1'.]o8"QM_^Jq>?Fo[!DC4HB\V-+3O90RjL-7)(-,Eb
%\1Iu5AAb"XkaG4g6E?0ggPF^%H768*f&kEW2!fdQ,%r1F??nW;(*9!-FL?I^`<#8nN3cqo/?L"eOC4)`+oYWp\!jY)@G$BBL)gad
%OX\Co`#XID?3^p>aDi?"/E;q`;ZYC+(H(_6bO@EnnNR,AM#sV8^L38d4Cmb-*XD8al+ZZHPbB+1_bOVkoIrh1-;>3nZo3l0Gj')t
%";"-S*Q'Kb]8?cfGQsFJ7:7LiV2C[ol**I/@nd*Yh@3!8@gfkP5\dMh(C:CLE0"VG_0nq(0EKbX8k!GFpbCtr2t>Zs;L\sX24rmX
%n[qcn(od>#a,g)I%tA]o]DpL46MOHSe"Tl,hCLKKb]JY'b%Vj/?B@[aOIs0U/bFW6Raj_=)G'uqS.Y`GbqTj,U&d`l0ljE>Lq!cW
%MTe^iP>FWgHB>Y8:ao=5n5/lMFs(m0h].6_[-#GX5eN&KU0r%cB4h2P=B3j(PK(PD=:p_G<%926M[)sq/"S;\nmq3d&cnP>=G""k
%!>J8gTa=ss/\Sl3N!$dt*2qgO_74TI]YOjbXsN6fR:`Illr#R*%%3r:NmM,H<0S3N$!&"u=7)3+oB84iI$ktQ#g#^db);.;TK_mW
%+D@78b-fQY0hOdH$%C3%f&uRFED8J&>u6J>ltA+%9HIZuT(]CJ,^O^@J0C,?h<f('41e83KA[SON-u@*gcP3%e8@>Uc$(naC<s/S
%R-5k:DA/?A_L'rkd`1h>\jAFh2is#eTV[qD9o'UfbkhskgV),KPmE/LedtQ`F,^2&ln*70f5HP7`(3eBg`S=qnSJk*)`8luQpbT$
%(gu92)f^-7=nucoKmTkoZY:C^Xk9#g?sOJ%&W6\NZVq0k?o0bOeZ5s+N!:r#Nqp?_I%k+9#-BWk:WANeaBI*&8OPoJ\"'aTj)(U0
%Ji?1E>YkZ+@#SuEN8ks:F>&@ueJte(4c'?eJ0<YV+b$jXi`d+NEKn9<"FN0Y%qj4q&ClD)9YpQJi=gIQRE$orn^.QCP&+&J]+,AX
%/kN7?PqB6P^*7k:c^Z-lJ:5#'M]*j/$,e-OT+K]+K1AjZF_hGj+jfI!*Y*B7A.6Sma!:t)64e:.+h4p+fpGdj5OMQ9h'VH`pPRVC
%SW\jP>8p%7kY7&m?M/SKs!h3Gc'K6<.!<O9aC<+]?$S2s/@k#,B+5Yr[\S`tg7Si&+Eba/j!7cMUe+>N*3RBrD7AGhlsa=Z29=cl
%(2%`[Cr(k``\l=`Hrt0(1ZC>o4p=$SVe7?n-];H49f4:Q3RVnbdZSNTC*;&CnFWOIa/"&jG!GC1"UQogM!<MI#2T+e,\SG570I$s
%>nD8cAelO"JT06n)fj=kEW(g;P)+Q6g5o,,<2utDfA`$Q8FQ_qYTWZA.oUU"lM=#)[bEMb\F=BR#^"rXYbr*DGOhs&_i6D99SB+*
%K2duRBHiN<K$Ls-3FtT"VN;=3`FH<XW[(]Z*p,1Ykq*'cN5M0NmNr\e*ZRua7.Xk5"eUcGXgGW5^3l#76tIMQlq<mD[0H^#Y)G+\
%pJm;"A<J__g^r,GC&SQm$I^o!o/5i]@UMQ:lIcj/+j(I#X[u0;+%G4s0VbOZ`[h08Keb>:26EPpB$DSZa)K<b[!?a12J$M./jo(O
%gL--?Vp`Ui`5Z\H0N='__/ftEkou'I##VWC4Q]458\p8.VY%$M5ZXKis/bfpHr-(q?%ip5`5A1PG$&`UBEBd*q^R?e#i@=-Y3@Lh
%2ATKJBJgc$>_cq4aUBYn&-d2[G<Qf-E8dQXb7k0/9$M4DfF(Zj=Z-+."0'Q"2\"ETULi@!EL'akDLlq,fpsDr;g+ku_4XhM6s1"T
%V`,Qd@unA;]b/;iBb<dt\:\[-?C&Z-`#aGI_*M]c/CnPp/2/,q6.J;A<oC24A7VIMD)K$&kE/*)Eo^qsek*)U5ZMY*LD'HSdWNH"
%d)6SuXCPfFZ.?-S`.hJ1).O'V;*oS1j<2&k8c=KOom!q9&lJBbg<`1;)@B7J+3)E,+'MTOPZEU=*dX??Q\_>=[!/",/k@q8;gS%m
%<2K7qFQ,[)O_Pp/!Vp30O"1g3KGR(/jq&lGML^Lp)"%\o'$:'4ef\L1G=q%IMq0CXa$*u/aWl&VT(_"I'4e).La`Kj$bBE67MA?I
%KCgDXE['p674+doS.rJ'qLm7+/>>Rq3Q:39/b;t=,>U^?n.GrG!+Rq>1mR"?<@0jpRt27!-ooT@PV0C^U3iG`-D<8$&"Y<FI%'8N
%NJ,2KoNLHM&pRp;X9nYSV&H3oS!7,ZoY"1LM/!SHg!f/m>9"u!dtDVdLc@JRL_KtB0+DG3%$U<L75Vn'o@S,7IOcC3EMmop.kT@'
%=qGIMTQl?>crDFa@n_D=RAe1dC*F;#Pug(OiM7hd%pfZV0F66=2i/::\LC>HXR^'jn>XeA$d:]=#Qi]D3RQ?i`U44Z`M,VLaVhT9
%A<0g'Ka-I-&#r&e_7lA(WOto>#jgGQo/Li>GJjAWCp;F@omCUFR8,Rh*UT;Zn[N1O,C3@XD5kE:EY'`7`MPsLEO?\h?P`LoR[uK=
%L![0oV@B(9j[<b9Gib<%"s7B.k[KWh<1_aJm=M<+Tl57/;O-VNCpC@]7h<6o&=Pl06mBLtWC#K%8/1*cK3CjgGI$Iico4,PN(LV0
%F.uF`eP,F'ojW*unru/<;)@:OQSV<(J`O_W-V@b?U^Qn_cIRUk"BKUKI%\*Uficsc3l0LHXYrep=1'h29&K7d1%a#Q4YSpD7F,^_
%TW"C5Fe&d`1UCi:7")?lepK/:A_H.Q:cd8GN?2m)pX(LH#]K"=7)qR/615.#>1-89B,bC5W+%r.I9.U6/khL#%B[IlKdL]@A^*Zk
%iY7q3B:lF1`+BCPCQc;?;_rA6%S^9[gl[LF=LBph*1[aL_5aRu0jh'ZFp.*fX:q:Geqmr#\5VfF;;E``)Fu^*KLN(t.2]ALU%!`d
%ULIr6#-iVG2N'5(*cs:TNb!)7@Vu<Q-:8=`TNkGEXnQWKdi'&<).q+1TNb)DL@GL8#O[\$AAa#H.EmTJ@o;PHE[WX&*Q8OI('Chl
%Ser&2^6t29TQE.LdBkg;"6<ROhF*]PM2.@]<<3b-22J/=>r0#Tb4I;(IB)XNFGtrE@O#833lq5t\O?W]L,`]>#CW#&#(s'fhe&lk
%d)cf,R2G.(^6_%8;jmMfVME/.CnjqM"OSeP&!^(F;MHYE'1<PYCqu]l9:0*tC='9S@Jg0/j"Jdl7+h?(c^E.nLa+Gd7R?i((^-dl
%!QQqJ$j+XuZ4I@kfcl,.`;Ab#c\Uu.`U>5_)&j;,/4K7GVO7L)eMcM5iC39:bdq33<HF#!SX8RAEJSZs6QZX4)CE3sjobcS,\J)9
%*h4e*\el!RY?J"Y@9f[>S2a\NNNQ4/L5Dk6$HS3!/dq=8AKJG*lVi+=oMpgJ=nJ7+)EecE68s3?;<VU9m-H9hGr/&UfpRXu6^ZZR
%'%fpU!&HK0e3;X9'^k#60?Vkn.655;8(:kC<+b2=-$kY0,69$IR@uP3cT@+P^]bQEOLo!cJK@S.g\3K[DfP_1OmMI',YOf`f"/d9
%U"!nh2plHi-l&&Gr-"B$!A09`._SNT"sSiQUD66"6\Dkc!RV0E+Y^:^&=8tY[L;WEZ>i`QDkbiQd5F0AdPh:ml'RO,ajfMWMITk`
%Uai))",r>n(e44k(W)f>R4A%iH9qgJ2T#,@-#ALRFJ]_Mg&1,[Yn,;teJOhiaO3]S$AOT[X+ZSKA8!S&MW5G=dM)fD0Jn@J<P^HG
%/EkOm^j:8"P=n&6.1[]\#jm,)h?U[5B-%3W-1)!bYEY=/B<L1W>q#MTS+mF=hrIBF8J]Bpfbb3'k@>R[bLr9mhG1/A=,&@8SnaN$
%Nkj%gerdb!0SK<Aab1&b`o0O9$tD;Z)Tq5]XqTW2GQ_Ha$n*LNAN2XO(.rkp(dJ[CR27DK42-r?I;Bn?fC6"?)(cik>ST6IaMj#p
%&W.^)$kro&+?Kr%1/6<M)7[eDl#7[gLq$tUAkLE_koaX<2NUBR>78oL!Mb<Xj=Z7p%i53N=!3#D`Ff%-CIgQS'7V8'a8k7#d+TWd
%7V1W\$(SYfk9:h/XCpMt5qm+N)!L$@84[Q#L>WFXi#'-sktt8hitraGYJLdF0!R;KSnoNXTqhKCfl7qs!-c\8%(J1sa&5LC*pKh\
%R:S;!(`9/.RU+-4;@F=0n]IX<2GGl,2"=H[fUo:PlC6_OM]Wg:4=@1=4`LNO-;G]C(luXSdG&!b&>L0jfZZq^%!%TR!MFlE^D$nO
%n6)(^_D\Z&_bXrb?"+lb+0t0[bFF-:Kk&>!@?)3DJAu7YMiS:PF;9slgl_cc0P=7Q1%CLtMM)4TI*(&Q15UaGU8Oh(Ob+uDbhdpA
%E3dP!c%AQC3pN$NZQZs^SH^VdjP1"4K(9LMh^k08a>.$ieuj;*/=qhh*HP"bYTB6r=bot+"Nhi$+?4KPQ^f]a399NMn-2&d$28*%
%L;D3+A9^BgKaYo0gsh*1_G;pA#t(Xn&&e&TV#]b^bJJ$da;H*0Y@5;3\"1B\Bf-%@,Ur+i&g;etl/?H#?"/T2gak"kFG]R/25gVi
%Ece&<bV2>oG0ZB5fWeofZc4S=4*Qf8.A48ok6[Qeg)26='5F>3FB6f%.#,(]NIk,p2H%\@2.*aOS>$FB\L"8LR.VGYW#9)n)R)"+
%l!nI&[b!>V9kCIQ]2I)Je/;bT4_U:XgGS:_ANpr2)nNo;)WN/jS[5<REF4l>jR&+)`D[6g9erOVM9OrZ)k-Vn=A9>uPZuUAo,;I.
%Ea>B!D^g%2\1EkYA!+7)E88NK$Ye<H"B'3,NRQmH\*t@N6fHW_;$YB5Jt;Q8:BMS.AuCHTJ#8*F$XLt*7?ac?2<gkf::'[d>Xdb9
%OHH3dZg!TLgppT`(rjpsKi=+IBI:2rX^qE:$pGKOGoqn>//L2*>dZt[aaaNTgj,dZ"6fnJ*[JcLZ?e0#O!)US;a(Q2U<__rPY+jD
%)aV"MR:b(a/LXO+Y*9p#FcNK=o10jjeU[X4"VI-9aOC&tA:,^Im*h2pL(Yq7cFZq2/$:O_jO*HcA>c8,PhpqV+o#=$f9V5jYWC*Y
%B\mc?8p:+n8!gd1_5/nt5ni?V>\N4Y^TQ6ACXV.UU,aW8ku<M6C_l]$Bl:4'mr-'_0En:4$o!O$`S457J5Rc+Qk$0J8;`)t14m2Q
%'I+i=pdL`B$KA>6NaJ<]Cd9'Vak5mYftZ@A+Y/3b%@3unlPKYZN(V&3KIRjM:d.Mbj@8s!J,`VuR9j^/RJkH@'[CNd7X&5,6HfiK
%_u3W[;iRrn>f?>A$+iQ"BlWol$&hp2Eu$i$AT8>iGFtB+k$bFZ=Q,fEn-t+W84j$/Z;f%tP84%KM>B92(9/VC,4io!5,?oOgeZeA
%iG[mEOe)aDa'K*JC]]5iHWQhhW0[5*--c*]S)rYoK"I`='N`]e&0Pc*0+=764k&8>P0_S+P/!EAE^'XeLC#k4*D='OqOd<Z)=Bbq
%,@er821Rs_[d-T8.Fo'!b#qq[2PW<oEQ_=$PiD8_gc-93ah,LhZBT:]4H>lR-&E4c`btkM];/3>$l4612[RO0bpB&sPfL23'I68N
%dCh4/&_qW=_I90Nn0H45qiVcKJpgY3OsY`FcH[Qk.Lq;(h["Vle]32UT;/X;O"S5'0$+.HnKQ7:N:(LJ3_\L._n-m'P=]lf>6i"'
%3HL%N]7n_"eRfJPhGTssiZQ:H'.!W^!HYR2KO[*c!Q*aSS>OKD9jYh.hmNfZ]4K!$cZ[[o/!Q8e,Dl96LIc^`U'<i?$bL&*@o<fP
%VWkO"a/a^]Ub,L<M5B*n7?"C3<gqVA]-kE_"&ubso?!0.iCe@=@B(b*fQSG.ee(J^h,,/Y7C#c6Y_j)#UYf"6ntE*.Bd!*TP/&>i
%1&-TCY9Pbf=G_n`hE5J7JU4fO-VGlL'ITqf97%\%Y"?'>-3d>57H9LX-.#jYWjYii*#+U7Q0<f\_#1;Y1h(V5a\eOQEJYBrqs71F
%I?puS$Vr9"j6'*dpk4iY=%W!5i#u,=H\5s)n82D$Ai/qQS8=E_XI?cpeWrtIOuWkj.B6m>ZGild0$*"a#n*)oe9njSioj(CBZ+Ip
%/Cq*CNb`4#\i:'>Z<XpG8k;X$im<<AH)7u%Nm8>3>dWikR1^E4%Vnaca%=?*d[*[Hl5r_>n8?eKTJ)e5Q6Q,;IG+7irm>i>j'pR2
%TJ)dJRf<:1IG+7irjsUJB5X1>?dP-6F?PU:d\iu:l5r`I[^9q@*qZE<B">&"94'TjWIHa!$=W%8QrcnS\"JPhi=AXgo+KZV\Mi[Z
%%TZ.I0`sF"]dT+j=S`7C&_lN3\!k^5_XsjZ9N@8e,>g$MiWKUEBKf.]n$@Mj<V81F*0Jk-.N1B,:`PUNArASi#>nniL.tfNnnZOB
%BVhmqe.-ak1,&R(k7W3mo7qHgR?KjRRMOrVVQ.g]Wb9f26/OMYR8`JO^kj+86eaG;M(\I;RND)qdul&(dkj,b@bN_[M(\I;RND)q
%e"1&,2beXZ1u1!.R83EkRND)qduG0+A^e9m7g=*QiY$[hZY=a4s0-C8c#L9%HDk8h?qC!?A?(f/#qZ4AFJOm:>"g'%RSO`'*1]Cq
%D4^R4>nBs\"iZYB%Jl7*NI!5)7m59WROWX?2P&Kt^M/#@$i:bGP)"Z,;%rY+0DrpXn'4R8kM<R[ChKCt*%LnUg!68>,t-mA70lHU
%_BGHQjeF]gH-&o"8'%DI9+\0Z**Rtik.ke<U9f]?`HB:]K$C,=h4s$>]6uk;)<N?B"<U2[-.kU=/F".`6rO7i^6ab7'K&Bc>LW.i
%8MJC0751p!;(18,g,<=h`>,42N>Eg$,PPG[jdSH+[_%eW(/+8l9EK>KqKc5k`F<u5Ja]:J4e&<!S#iaVfQrtLehideRGJ(W<6R.&
%c.P.#ZLT#@Vfh0"V`.EHe<<Ptd?@;f$O7JNk7r.dFK;EC-[mCo8-gqo6!1BhM/2TkbRO\l5A`JLoM$]'MjmIRcL!bAf<@"u^DeD)
%"^!S6O!tj/LjE>.g/o6i6C0Ws[dBm;2k:p?DE8[*]'&=V'#YtTS;UOK,qUZ/\pRTV150hUGMWe@lDSt/GM0DaG6W.jh3d5O=DWR0
%OQ\B7P$5?#8ZK$0(0/.kFegB*&]g7)[R@$t-s=4hUM1][m?L4d$S^1*_pBr3'[3m?A30=aPJee)\J3l^<&LEu![,_?8WFAUM#!k1
%3mVM5Oc,t.nb8*`gI!Etk\Go89Btk3Oi)7;kpPiqPK^[^AAq!PHATeO/Ts!3FWZctbT)^T,@gY0^l<$+WZZ11MS:J_L(^Uji^5i<
%dEBWc&kT?_$T6QTBYIWW$NDgCeAAb",hE=9HY)tS)V5ET73Bq=*dr&\<EMAIL>?T'JC:;?_RlFT'$_ll6Gd/f3]i.'B2'c=I]_2
%)f(sFbAp7&%/sk"4/0VlEF9Uedfknd$c6s_dj>*'NUI/:JsCraVK!S9VtnIaIZ'Wt%ArrT>db?MR5Mn@f]c.M\sdIKQZ(jn(IeQ7
%#>BNJ@s[s5Q(_q2:Vq,)C8e9b+q8hYn*OLj^H,[!jL&n`HQd,84Jr/&S/4+8;E/'1DTu0sJm`+S!3],iT5PRbZ`S\q8C"e,9)W:K
%P$Qe:N07MEO?$/='*jsk(`)9?NZ/2#/ZoWKM>d`!=@PJOXu\KuA%!]Sr(<CtPZW<s!B5M$UaW\h-RIln2/-'sE`5':QSsj'IX;FK
%26CFA\i0gTp.9?ZMuhb]l)#B!a6P`iG_bd[kY[0t8<#XI>"I#3XA=h4DOhS[(2_DVWB?I7#st41WN+(N=CjEemR]R)0_9rD#+HOu
%o>U.P(?ZR$ir^NQX'I-3a:Cb5q8P1l&373V&g`;c:T*9kSnGR</&>iM[5bW?op':1VFTXG7lrU8"H>CQoPC9!)*LR.LpuEI."A$#
%8/NA?B7uTkbYmnAEeSs$FbMG7TjN?h)Fde[.tVQa1cHg3AW+7KM[,!VME$m7-E5k6$geC%@H&NB>3s?]V9A6d>uLm2Yrk$D:"b%m
%`+E(u=hIG7pkQG:QYBB'"J.-BL)O>t-JTkL4lK8"(!RBe43;V57(QmG>4c7n/pA,gd9[oII6o>4op*5D7IM2YgO,A*oWiI+*&,dZ
%fhctB)B,Ld=P?LtqSM1$mE+-TNG2Sk6*P(;.;A$^+Rhc8?UJi"a[Cr;3+"@(75JSjJ-e$PW*8@7kL%*a@aR,?FlTs^_dl=u(C"N2
%CJ9"h2K*ad-\7f+;,7Qe9E<"u(b?dH"G@W"BG[JgoQ_9D:G1&HeoL[g[_'b\iO+Sga:I19PNjI9![;4:!ln-4EuluU4_Y@.ct!b`
%Z@9<ApBlaGr@*rZBA9oCD9L,1>.RS;.&#%an7=$cd,"Bk"Zd.4+3V.ki$-2m!%4mWd-!.=ZtQ!r0<aH(Q"qp&<<6A5;=4>sn_t=D
%(=Y<$aq1j\s/0_YF(jO0`ORdfBH,n@)!c%0;%-YASQ!uBkZu_%BkQK2,m444Q3Q=nKqq;0`J]Mr8]YW^AUq$/Oq"q*-,k8fP"^NU
%JfS0N%+&rd`X'q)#JoaA<[e8I7T/@U<(rUS>l>Gj1p!c*M,2f&fi6/Wc*<04)A<W_2^hVf5+nObgBDF=``B:[S:F.0D%bo&i05X;
%fi)[T^&W4`'?l@2i"VGD&LW'WXKG-mf@Q3,KH5Q5Y7`0/^oN8F2r3d*8"Ckm\(J2>.YfciBALa_:*K[\NB2'6W?DH`F[BS:,_u[#
%<mD4+GQ>YMOL)q&&;&_'WOL\D#03qK\(L(IHGsK+r:r,hZuPaciHCUPRsL_e;_\Arj:k/F$GsrR&;'Bh]pjh@,%MQt#P*BRHRkPt
%Jjt!<`s.6AErL80-H38c5U=V8.`]5Umb2LZ#UOU%/&QH7?6,SYhhNe"8aAb%M5CZm[O,W)$'fO/E+j_sm%,,SM\DTn5ZFmY%#3UO
%0_H*1&LhIZc=>VePgc5]U,\,h<tmq<J?03DH"QuP:'aW^M7c`4k"HAs@T1Xr.<(.,1hO_;:`KqJiF"(*M8!MTUW#lJl,d=r=`5JM
%U68.s%FWo%;IiN';siQ12=/"9>>$2Xb(;g$Ao@,OSU*%P)*,=SqM_IP1JP$P)Y@UrqP`QiI<2-BN75tI'3g#H9b7*+$.sWNj%X-3
%f<2Er`f8[U]ic!CN/eV/dAI?mjM(ZMD^_C-%h:g@?erBoqY9crDu]IQVsiJ*J,<EJa6.r"nTme,iGW&;Ier@*f2qK4O58=e&Q6C@
%Mr%4gY5FQ[o"4A\?iLL_qH'?gjXOR7m-5\mfj0"8?i&T^TN_`J[d/<0Itc/U5Ila3kcbJTI((:IchXI+jskP2UOq*mHbY+2Tg4Wp
%aW0EP!BCaC!X8>(_mCZ(5S<3e;/a6LTH9f!p]Ks>h`[h8'LVqQZ2m[$p4I>YMs&Ug6mqUc'?Pht`)]]#,%AB@*/KT6o-^X`?q)cO
%RGc+Z`1ql*c<73%B;@,s=9@;u!"@`MN,E9G%`&03*_n+]acgu!&Wn]*Zl7$?au+P92KL0h=;)^NgcFY`2Omsp4]73K1I-gJTlXZt
%O6!-uUW"M=<`n4fR`W1Y26-tLkbKg"o;ToX\7B_lpm2C%k5XNMC(+U>4W5`):-$FLLtdrGF>o7[)n\:)U<H2q!$$8]k_/c0LC)PP
%026FgoOP`c5?S[%;W8U0O^Y,'Hq\TB-*Z=eea@/c;[*-"U]63tiC+p+961^#hO.&>kW>VbbT"E/e9[_MGdQt/+nmDs&U<Va\r1YA
%6^+9?CX4mY-oTbId5RD-:l#t8@a2iWRT)pN`fp&r*C[8.C67SXX#`_UQ9ga%WIT/EcS\C%OC?!XL%&!>[8hL>,SM@;TB%:HMk"q+
%,Beu(7$(C\(n\qK)KV>dDn\-FZU@U_`Z7j'+\EiR4cL8?"Ci0ecOeR;%O>KOQq54+-K:U3U9G1Sp8J7Jrp]%U7@@g4Uqa!5(,^qN
%Wp[*G[rfbCUToK/QjL]GG)]QNL3nY7C=_L43DgM%VQL3;@k1M%`_tnib,D$Z09oG88tMa9j>fhW]slYWD6Or&=%5uoI'Q`c&=tGk
%;Vn`OpE-!,*Q63U;YRgO*s%s)JKkS0.Ch/H69;k,'%*:;1dY%I%@%;FRMcLs.!G>;oH5=M9Q*@k06m?p'<Mq<Q'>qr1.=US[6jME
%:c"bkp4Y2_@*&7bNnQ?OOGK:0,jXQI;68o8]jqPFk:(T^^GEue7)YQLW&;_(+:n#tQ4Z,.;:8-V]dR/qIY$?9#8PT?ZYLOkJtEZ9
%+<tN.5^e;I_<Z5*8N)!"1'M[tMG@Nu]AI?&AN\oZFZYL7R#*FGYt-6)?_;Vl)@2)6FA.i@7+=i>#%"Xj/r[1aaU[P=@6?uAnoU@2
%VV+S3G4&,iPaIN'-U0Cg69c*fqUhupCEAI9:?kU/1C7_ja^3'=gDY-)Qj0%3RE0Lu\kK>gAOEUiU/MDp]0VKhU+7tJ*MT0_ULkUT
%Ari5Y=oWkU96A_@bLpZ@d[It@F+.sO(S4snF@;@IOEpJc#_Q?7(pbi0HFQqN',N!)Y55BcKs9gYR$5;f$/H*(Qt0)=#LO0TY49,&
%3e[tRSPe01"JJ14+caE-11LmC0W%N^=WZ_C)/5<qP%Za$FKLGOl9l$CetNh(`OkZACF[1jgD<g\X\8jTRYck'ML[\*hfB6\V)+>Y
%HsQCSSHV;@9PI7X3!=5I\O'LE8`B^fbKe.pUi$`Qj;K6""Fec*U&9F'n=eT?hFuFic=)l/^fXK"SaD]id7qmXBN5YXlgl\!E[>%K
%PrIL,k0r+a/+P34_nW\*gman"J_=?r$[=('7p"]UigFMG`lqJ(FhBJUpWCE'X"k8;b:P`SBXuHZmQCb0T<qGqakR^_akR2[E8U!7
%ghCdS0Op+mA6_<\`b+s];]J;8mns[4c"*`5*[]8LmGY(a_28S4/1*PGG#dQkH>JK/N$"@BaB@N0JVX?8T$IYICC)MSqbDt(Aaa'Y
%K?@,Ci?opY@R\^$f;*'9*i6Y9l4n/XY*:P%+ei7[bT.qU03>kAPA*_o'tn%IC8D(.7&9^%N15u,:DKd2^"1!r3>[^1PP7C:.Z\'5
%;[nC2V2%4ZM_]3_dM-`"6jb1$C,K<2kag+\g]t2'1D*uXqO:BUBo%dj^$pY4M@n)Igp84!'G=9P7oAXTM3O!cLF-a@U?BHZA-cUP
%T@sW'el>;@6q$Dk>>2+#XsPGn(dATqZ^0B+3`UterTDjuJT"a\$5G_k:2BR/)QQFPpi*/elg4To@M5pK_>Cs*R`E?&4I0SG"QsOj
%/^i^&$&1@Pc<IsPeK8url*Lu2?Pmn,6suKif9k$a-q,"TS4:KBL5061FcjhtWFP0O4\%7GQ0YgQ\ugBi.SS&1#70sUM#;2'21V9n
%pQh,V]bc!PAD'jE25FeaZ$DPT9#.LUMN/'"I&RojQW]V+"ZnIo2O<e^+6#WJdhlj@)]BTQc3%A&ai&"bJV$%Q8Dt^S"[,X=7+%7;
%l@?gOWYnt;O:lAZT`4@q=>.k76(1H0-]!S`3amuiSh\hp9u7W4MGqcQ9;:Hu08:SnTgP4"KE^B6.="*oX9K"G-T]'fR94!N%69'D
%KBuK'\%R=c.6S[hhFHJ$lq+"sYTJIgUl7?&Cp@(Q!lr0!%n(0Q]Kb3YB,&f(XTTCcZTq[5$7T<"='bQ`/<+e=`!7257@3X4c&fpM
%'HYi\RM47U(b3V)<jm-@H)C@]atAVk#^jI1QM3)<O)1%;(lcHmo!qg<a4BfH25q"HQR1pS.)f/eaLA`74c%l83K"`XWSV_Rag91/
%RT0=tEau\^[G:C_hlEn;3PID"(ns:XRj7_d0q6<9UWX74"=PY9WXm-HXu9)P:r^0!;O">"'+q@sCCS.@/ksOK@5`.([Bbn!_cOJ=
%0O@O1&s<gUMjn6GZ0Z&*Q7ERU9+g9RY?PB8Z3;D79P^pXVOV%7lR#]%E(6g1UoD4Y`EW8/FA4#'O+(p$r@6BCc5O7j'!k[hL2o'/
%pc.-6ZE4l[L15=,i\2DC6Bn$t`3+`tQ">Y%ne']LA^?OHO1cqgJ$-@p]oGAnGVWQ(m<(5f.#P)hWW8(*bCS\6JZTKYdSdU/X`r@1
%eL6,cRsLLPmpOT2:nGcI7176@`ZaanZ%VAo/PXNGkg[k-F>5Hla46rk0HNLiNR3>LfO#t#@UNX%3S2Kp9jI$2(O7tTQeo%Roi+Fg
%G1"3+X_UM2oA[YdXBLHV7QW`]Ge5S4*]$mM@FF5$]?I-5RqToU`!/9Ha;DQ$6hXZ`mAa+41g;&VeVTA_67d<PWN)*UJdNj%3k^4S
%@Sp#fn10Ct%@LhTOX*:Y'8)p%;%u;E8rT,GRd,g.D'[^-M<O8QcD<GElE4c'M/(H!X?(b$Ld;7t&OX>aXAd6WhQn[uZ.iL(.8]i_
%"WfB<4'Sa"<^h(/]MlcnM1KmiS?=n50!eO-C/!W*e)_-@+=2@u59#33Y2fGU<(Nnp)t@fTf'LG0Z(S44`^n5g_t7>qP4"R$LL!kg
%W*7*cl`l2IQKkFRZ19G]=E]65aLEjXX@B$U;=>KniEdrR(5V6WYoCm*lT%C]c!lncf1E>*jmg63REN)tS;lbnM?(p63W(S#^/-&7
%_SrR4\V2]%17PaOEht$ECtKM/UMt+:-"jDD9&l^W%rJI3WZmT!m'R4E:oR!EDj]5qIu9gE3iP58beb/M:T+af"U+@J4bZa!1$,rf
%Q&28,%2/T86n]:.QmP+e^iN1>e<LT7Ne46Cc<*Noghl'lU=_!k\jGhNS<4[>0["Z=1Ta`o\QFt^+RQ3#388bIj`;<Y>]kU3H8?<^
%Crjl(i-;8(.'?:"O\ekB,U;/*]1"O&X,^cFX1SQ_r=`C=NdH2q@T:LO#0:Ogc_jD/]^/UUaF%"=E5@@QP)#)*1OeV^-P7mA^G!W7
%R2mAOK?clV/7Xqu:QgUsl5eSp_g]Rml.DV.<R>@5gZ$>"4\SJ8o$er1&:*Y>3bK1%/#sI'B9K:HjUgIN^ssR]%,\h>NR_S":t;o2
%\uI`(S:I!QeB&d*@a@=fRF;26]@LO)7)$Il=a>$q&42:&)0[FZg>igjWqIQN8o"@rIWu5.\&%\pF#RI=9N%fESQS\VB^lb0!<ZkY
%[eVZNN?Y!`FH$c@+]bLH'5JCi;quKt9VOM8#a-f@ca"[uBcUPY06VX8BhHmnI2%$dY_cFR(K5UFkdrpQ'1d;_QnY6`W1j[*V7li5
%k**W[d/VkQKYW,"-s0rU4*@D&='uP#qKC@)+.=up8:oR">[L/`A;g)7Lcqj0-_!;eo^'6%R*riZN0u49Z.kM`76a2<"S5<2/F-+R
%#5.(,UY447Ied=B1eEh)&:$q`>0F&\0@2\)hQ]q6>UEE7&euc-j\hS]R]Xtp2Aq5=d)A5QN%U-p18;XQcFPCOH#F@Yr<WEfHZ.J>
%l#EL*h])'7i/Id7K<%'-V0-@9m7%WM"dZc)K>BgG=Hpc_(JPXfZMN1&)9W\]gf/4F.\B)nN+Y7p<1\JYNK!N:>"La&9N7'JRCFXU
%I1ZOCCW-?D.#^q8M1r*P"gF.O#,06*Gi[J@8`mGAF7KZ&:BBtt^)(S?&4thj#4]t=f%iVk8X)Kq9;;lE-[N4,L`S`?[cT&:*PRt3
%qTAM;(e"M5Tb'X%-XnM"d=$C]CaNX)N+IhA:]0om)Vqtsarh"=bPOB(6m<DWP0n[Ug4KhRc?i%*">NnbDYM1*03XGO&.OeB0ZO`1
%?;mT!Zp]^Y^?Clhoi[+q0[bX^-3i>qg"(gK&Eg'"9KsPQ<$sUrGYacIqFjs&.B1<;`2*?18ud2>n+HTnfa'J)oAMM\R__Lh*a4$/
%PisiM60T&W\k]S>C"Pm/0YK</pRZ'X6b;+>SKZ4E+O2pt0JT_sHO2am&X9Fdmj!Y2@YpJi6@<?-"CVC,S4^h_I*pS3iNNlQ/2OpN
%TmI$UduZqCI?FeXM69OZ-otgHn6ueP95KQMFYX(jRR-"^a+m,l.n)nsRT!N_I:!mq(TL#RR1O#`@hg@.&ViiT6oH+"64ZiJ<eMDA
%;<,OWm`X('D[:D'for0(%p!O#6&.`_cB(JXS[,6N)JVo/RKiGR?LV%IK5&tQe_ncM%Te3F2YY7r%e!0U2EWBhFRUeM1=c"@&QD0J
%E'6+IPWjlCd5'.;6mZp_$\TFI*hK$B/QIOUObWJ=/rcH2O%J>=@%uq2;=c9*W(_buj;</r*g2.K>oB^8!7+1/%R&Sb_lMm&b\5Eu
%L*O;Gc9eCRFhT(k[Hdot6q5\V,?;H#CtEl!PN"oq)%K2A>.'.HI%NPEaKL+58cW^.INt:@G7]D@Ya3`cTsA^QP#cbS+ZN^52-Fld
%X)d5Op*&`FGD%>8!I5QL=RISLM,Sn8$j7Cm/a"onZugoha/T0Id:1G.^hIHm.SlpE3)UO%4.S)6B<j`J+:pB@$?jOAQEjm!Tn(Y&
%-A]&>_5jA6@gq])Q#BY3Td9$@/XqS>hBF9?NfEpmJ!bqs@Un^mM-6TYMb#3^"%Ig"_BW*q\J\c"L#Fl(e+TB7Jr#3EYd*ZXb<a+f
%-/P4mb,THsj#e"eDdS(gG-p>[@E/ouFuu/*5<6sAHU:eHMrG-1lF-sA:1:SU%LNClbHUG_nWnhOY5u9!n=28I6W"htE1IW]1_L(q
%$PTE>$(LJ`T%>n`M8;AuOVBDqSaG=-gC'NN7d/4?Kc&!]ipE>8=OX('M2[4;]pFom.n%a/2^W"&>a&*CP#t"<ZU/V8=C0RePIU=4
%CC#!'HCdMo,u+2UdT_Bt.p>%KHbCX:<riRC@WEf;O^(`1KDL/0L^-,ABD-m2gg3)G"YG>i/\9TJg_r)-Ga\kMIdd#(@uHEW@_96:
%J>r!N)FK\OZ@]QSkhVuN`%"9qONjOi/E8tB37J5CS?S>aD1/VumX/j]K^-9^2.lIOG52FBf][(M$B"g57'bYU;>^nt<3o;d7<]j<
%fq^hiG&dSYWQUODh2A9mSKmtN'm-T]2$Bg3rD`)G7=@1d"grC2,&^8/:Qg(T7pHG#4?/!ukNUPtR`DDFiYg9'6mrWc%^2s)M(Rp/
%@9`7NHLLq^7?%aqCWpZiPBcgWlS_3g=ugT,>_:70TL(:UHZVnK+emaf-;%6L-A;j\)Q%<6K1#.4oH4C]Bk\>+H4OJ<[IAaPfnNGA
%ClR"b+u3GUhLm;&g-Uoq)^:1)b=)#(H')K@iTCbiSQLf,k[>@NhTeA6aR>)\WSYWg<NJ=H.T5tJNa+M;$!KQU\MlT=D%,.iTQ<)J
%/;99!^!u#P+JQ&%g=.Eb`d4TWqhg\SPh&)Y+jS38J(Bo+)fLHEQDO7I9uR<+;6P5CZK2To\pP7A$O+%,?ful$*k'XJRIpC2XE9GF
%FRW6b0juEN@s1S;kAJQ-l5Tu#'8.ro7\MPjrailZ\9"$D/a)KG6(-o;GBNdUNE%=*E0UaP?MHkMJ9;Dt8g1K$%_]f/@_`7'i#:O7
%SQ06J'J>48I9p50qB-YJ7+[\KU.6tr7M6<<MLpp-/krY#Ke@^CFNo2$`>.c+jPB]CQr,"FcYZUi$k!NeVD)E_'bQc7R[2C[*DpgJ
%[OSg6_;Ys\>VABk>CJV`Wl;(8RIO4B%$r9iiSKBMiRDJM<F+T)/re_-M`!tiLe,YO#a-JXbah^a%,jWKO%HO5MC_G#`N>%2Y/C_m
%#;OLFI+K;J,KHp;_('MU(@+PIH9p)NniZ-PABZ6;EQl#Dd\MFVhp'JTdd1-W6\oJol"4,/2hY8UlMD&l`RZ/9ICN`+P/N1?>Yko2
%Rs5[H:lX,(ko!T(^IjaqV'e*SXi+rL0.%km?V=-s%g`&Q_Vk'-CA;a-cO\6/:9[WD6q6>\mhsUEbS5IcR+N@6omO_V,<b8b]:H<5
%6[0a;TLsmJXO`Q,dk(@5Rr=l]riP5:X@S2[P>EsN"2lcW4Vqb6XX6"EqPH3I\Nqli!fn`t]eVu4Z'":6%RbDa]=cX]e^`[27u0U>
%M29egRQ]^9q.+1JT:=;%o@bd>VY4SA=n6!X>RQ::QlAPe>oSBkpCK'5V2$]"%PaiH&^jaEf7:M-:_GFq1X6O%4k5OTi#'$^9uV#Y
%\To=k7,(%8=77Q(aE>S.6nje1L`Og`LbnbDh@9ountu:DDuh.4rW#(lAdour)Y*XVBQ1%O%7UkN+Y4es_47@^#t)A5QZ)sKW^]ck
%S10CSfU18M5\,ssdE&<m&eUI0+kU!;V4To4`p.LH6:)2CkoC+eH:)52P:+S'?7P#Q)f;lg3l3o"eTsKtAagK&_,W!1VUk_%Y]VVA
%NY&a=TX:5,N0*Crq[$XXO_>c%-&$rlgf!cI/VlhKn-JW:Re_P8MG;4,C%MrQB_OOp>k_W%)>2q#*U0!$il0(n`8q?+JV$5+E6];9
%a.gWdGOB,=?Pbq`SlC\BUbLQ+!HN((gF*O`@qZW<e&?>RVZD@#I>OOilPs[/'m&]."4CV0+sY7K=u0Hp/WjiT^^+-1:D72F0nVS'
%SL&Q8`D=T)2&]@2ArQkh,6u"7DQfl$L^6:Z<O,>o[j5oGls2]e(C4f-ZiS$"nh-<,QWot$9WH_dmRXsL(1-7M`tsBpXGR(JCnrqu
%A71Y0EJbfX7PqLkKUJcDg5AI"M0_Q\I;I(DO@\df'hd\N7'Ml+BolOmK)(c$,_=!0q[ICB,A=']`pPedT+J:&Jf>e(Siq%uKj6q)
%@[Yts?(A3Z@<if%"kEqABh(_BZ.dhmbf&#QI'?^Sd%3VPA`q5(eh`>uRg61%6E9LT`8f2UoWktaY;T;P%?s*U<dj5[1?`L>?#%f"
%?P>c#Kp\`T8d9LtYdt_`67lJ?WKTtA>7^4GpR.u2`&<pYqT6)Q&VYl0[=`)U?qs$sNfC4#H;;Z8$t?]5740LK6A0R//Nub#cU:F=
%gOg>AWjkCTRib%c"\EU9==*g6VGKr%7A#db)g]*//l.hl:aK9f@cU_X3aRdb97os;n]G&QEUm#B9No#\Z^82/KY<AX]]>Y;&/fbo
%KSZ^q=Yo&kG_;M,F<*ZUbS;e.=!>HZk9PIM4!:&sFl-M!kH@Rg8IAe(QDj*c`Ja!:&mg:RUl'hG(<Rn:P&lCq??7%kGU;>Q@qZr-
%k*5c1o>+%(!HX^'e7m`][:U$`WHfaJGk;P(erYQeq0V>L,YGDbJ^pqZ-$KYTTj%M*'-</Mr%lW6ROn0OoLDFWbrTMVr9#V\j@'+g
%KNtK'qabmE'2]<ZS7filMRLCH3<gIY)&nFV-%1t&b_q;hYM2ajj9u0QF,+GN.9Eur%h\EM?l.g4kST9+-RV;l<%&C+6e([qCR/U"
%KjTJP.4VkD]kDX.1D^gmG?jMT5Rd5sQX`77+Q3GB675eG4@GJiLPQj:Co`-'?l!cO7RZMW4e=5:!.7_%)SXbH:TedoGL:tAc9S^T
%:@/h0K0j+0LO9I0E(nRmJLhAPF+qV'4_S:r[Tf0r.`kT7Z"OY>"93W!P6T8WotO=hMBJ/qi)2.D3@5[0UUGM4,jIp8GS3"'*+3DP
%Aj<+R8=1m7"]u,n[RcWl1Y,>f)M1PEg<4%pUL6AC3B6mS,'i1\.ObIg/PIaX1F$"482G0E/!>PsK3]5b:(WbuQjLkuD)q`S#+TTH
%FhS3S`#(<gl?Y+o\9&Am2U3']+K^J;W#c*qc=CoF>?$H`p7NG43W0er86$CV^/>*$A-lerYmLP(EEq&SD5huZZ%bdsF[[@(m]D]<
%^gnIs:e32+=(6%b3iafZ4t7CLZQ)Pc<N8AY!'5tORi7<S7D,sFhdJ*9-/aT:W'**BaVZ94[&r$,/n'PQQ<iePe7<OJ$#h8s\<q)*
%-BV0(L1jUUT*dQ#;G0<&nRHS.6e528*X<-LdJLgZL\4p&<nS`V69h@k\TF^5j>:PPML8jI=.Pkd"ngF/TF[QU.d>p)$5p?hb<pXf
%>%@X]aZ*0:FIu5J:F]o_0l'm2?0,2^/r.#SgbJ#65GWCQ&-WbujcG@V3/93EKt0!;9@oa=K2s7rL^U@?^Dn=E%jZcK=s=0/>C'7V
%7cTcT5ZeEEi)g\gV+A\]lp..*`^8/g(sQJRZ[,N&i65S_o4CC()Yi+XPR`o6,YWgtV'C5b.^HjS3Oo6)B1gA1adb/;*f7%fFhI,d
%q'Lm(Er)43D'n5iKpbM[Of\9oYi^b[L0,.Ic&mfD,-#]GA]k;o\p$p0AYY\-,?LSh2:IeW89Y>i;7o-I6'9k6d]k.W`F@&p=KDl&
%Wj[,*?*t__5m1^);"*0JMjO/qB#UP'OJB>`>k?9%aBpo.b`J??%6Xn,WkiPtCeSRpVSe86aSMt?kRXh9bRiGEa3Sd'W!A=X04LDr
%_4!-p)RCa%`k2:gZF.\u!-f2HGDU/f)<EMAIL>[:![-blCNolL*#,r^+HD&0aTKgBbbUF>!TKD%42_OQh<W(u$:Z-'
%\1%t8@l.o!=:DZ\O:+DHTuKK=iU93'nJ@(fU/PQ<.^KFqU(%@?Q)\]OX$K-2:3]'*;:[Eki2)14lnDgJJfob?@ptm<,g*oP$@_ue
%B0<@m+%Q1<?IGi-Gfu2KjLX+7;oe>eSE(OZCs\Ct%c"I7&T9,D!+!Nj`.LUr@k!_9ZD:#(30-Q:#2:GXLpo3:#T\5!&cCkA!rn@e
%'@=[LR<XOddM>tb<<TpV/P"jVE'bE"r.S_q@q&"a^k`hL$GIJ*$jtc:FN%Kq?jpj*2NeK^Y%q#",3Y+g\0AT#j8q^`>kSRJ,28Rj
%\Vd<>(Q1/cZT;U>ObR=#0>u'LVpi/Ofd8.*TU.7)LRD4AHAC#jWCC$09!&=7&2oG?0$4nfc<uGV&O6X>R(3]WQ^f??_OM5j+]F5u
%OpVf,;RWsM&53caFg5'^X,#/Z`[h]Y6pr_5Sd&[P^d/q_S*U!c0A:95E"@0-mR2Dkk>(tP#'%S4mfe.gKZGNt>CF=/X,q:jUUc#F
%Y/cCO]erP7[*H'jf,loa)JV"=$6>uDZTia'/%1Tk/A+La+>K=S5:K,YmD<s'C9#,;@GL-9p&4>EBN1ZMM\r9I:#DXK'-7<jI)1)e
%I;YiM0PK#5.@UX+,>=7H^K:_sc]'_X]@]31m!-mTFL"I@,b"iV>XcuF`ISR<B7b")(f!\8X!04%!oHmIgBC>PF3%9:TKfGOVpBT%
%2=lO_3Ju^0&7b'"p88]K'`g+jd@I!tPb`!S4=Y>/;S+o0cn1rB;tJ]U3!.apYiZJELgAKG;.0$4Y4;?cb6I7BFpM%6E!L0Y?'H0e
%42>U"@_0!9r;?cZTIqNaPI<'-`?/A!,$1[L*Kj_An#0JbY<4^<EMAIL>@]BS9*DgJ]m!0qnKE#b17Tc<r.9^tcmL?1j^3_jPnj
%JpG/pd4ap$TSt7),[jCS,=cSOo&9!0,V`^F+*5"P8H>`N6&-dWI*_LG">^sll..(0Zc6guJO5=I6ARX0crbVCTUo_:@9K6aEK2/g
%,a&J8LBn8=@s9j':c78UZEUH@%>\oaF#mZnK_*9DG.PK^eVu7K/aGZad+R?d)WG2G^S\Q/9jgD75:[Xf2EZBsmT!mHYhL9TL-^n:
%#VU+Bq1Rh*Q5Vl-lZ,qaLce@a_pSagXqV'87&Q?4`jn.Bh&7_+@E@pNHN<pNIXp3I^EficY]*X-=HAfM)rh<iDfrDM"GmGiS&%R5
%Ta-L/dAn!5b^l?JnT6.t\$imT<Vofc#R78ooBWf)MD.)rrf\D*f>nor8n,T12/H#VH9[V^.q0T!&C!Tb;jE9X*<ZDGRZp4P#6._k
%gL)03P`Mu%Kd`ferI&!1#jr]WC++Qc0/M_BCJJCO/_YMIQS:"fl>2]]YOY2F&4@ZC5sDG;pmf$Yj-MTL,9bd''6kHOpTq0dYdu?p
%".Tr,fVc`_7t!\u/dcSnJd"m9_#dqWbn^&1HE`edaI#aMf'1<*%[*egA2Z$Toi9$p^Ij4h%*MgVXarl!7K=e?IBK:L$`cm3dSuRB
%NBo]KGQNmkD\D5/$+Mje'+GKqUA'D;+C6C(,q8$1F$m5c(me"upEl3+g--*C#TL\QF,-9@dcHq<>!eq,D!4]a3:Y*mfN'O/n6[(A
%^a'Pa+]oNDN+HZg1RF]ZJ$#pkZjQ"#NHZP^?%%,X=6j:[ZP38(6jGDN1<u:80O+[N'3d3NEldYk3ZE)qCrUI$[p\WU7JmE([3lpP
%bt.99W4i%:9kh^&\%>;U!"h*,L2DWIBc=I58(0M"?R@r/MT+siU9[)uW-TV!#lnBVkBX>FP`M!I"Wtno=lOt`,,,(T'%'%tR1>pd
%$DN7$\pd<[FU>>5_-Fher5=^g'0=nL%A/AZZOo"5`Ut%KLLO:5_t%c&hcS).<+n/+nen?[8C^ZYN(s5?=Z0K(YUWekiFq!e1_pOi
%EEr]pq:tW%;7U:Mp=W]"<8H:rAV'm42%u,D;Mp(j;pic8_,.hoR:dF+Uuk;)(p@BUEC"hB-_3q`X)fKl+![fd9;BaFijPOI\q7VY
%dYe(hB#$qmSY,4)K-C)G?LK8%.:$5L&P.GT.Ql$$W=uDZ,e(F^Y!MN6!plg`kU,S.6kPIe:YYHC:mOlYaoh8K>1olkBX!bdJ(HRI
%-G3.qn.Lrq!QludN3*+@:og2O"f'7D1g<QcWM7;_PRo*+[='j(mFgqb&""MPo]hd<HO%@&kV-nnM1=S%$k6E`".)CkTm[Ms1Kl%@
%pQ0LS9rW5X0ibfllc-"mfq/!J]j&31R??KY&P[+VFH=tl8(Lq'YZ^C6&2q(0V<.n`_n^.N0UG>1W,A<h?u*05H0*DC/b@C+&GXY+
%bgA-."L\#V*S;u1/M*[UHGY7NUnf4IMj.XXiY!4sLH@g^OkQ)K(o!qj<)Z.Y:ebs&AlAls<)"$l&5oMN?(mh,%m`(HQ1p"*Q)&HX
%"WA+'PmFW\CPaMF;6LT4as*sUZ\[K.A3^fO##NK)g-K9$VOWi4R;*?GWFX/C&5e)%$gdTq`_M*A'8-U)&&AmZ<#jC.p0uO9%8qZn
%@3R^F#?>5>.CBe76s+NRHT1C5+\Tg/P<"$rS5Bo3&J6CgklP!`6u^>V7!%r9"ehBnY"7ojUGt7i"soCXS.g:Y]D'U]jlR>0iZ[<o
%Umc*d7#g/9c.srE:1EjL]I]*^k9Iq7Z&Z'+`"g=QkbbonKoWC9.agQscI$o1Ce6&O,\3iV;;STglt<9:EqT?)In8">[;mBNKVMZf
%Np)A/%`oX&-CmR8`OZd8cT@%(jH.Q)f;K!::D'Uj<^di^"l1Hdi;,3^)!s&biT:JBl+R7Q8E_I3((L/l7@7lS=/$Qd,Z+d?Ch;k(
%qe5J(',)oL[`*c&TDf#-h+DeI",m',`C9Ll%_t9QhJ#&q`+foaUueG6`m,4faJ2>sa[-=U"9qbMLG&bNDN%:(i,MRd'G9;<@N=sL
%2We"k,/J-&MJU:fXFh^2JCXaEc&fnX\uiigYFn:2l8%/(9DJZ+m,^-5&O+MRiCi3/`V;W+e3%.S&g(8!mIJ&qEXRBe5/Nf:V[`q[
%E9E5ZC+tSfs/(7L<(u(j;aVuI[,@6O1tWgl./c%YI$ea@]&Mp.0a%'dG2jXhSM=&r\eLg]1ZqbJ\&BUHj$osW]9fUO)&0oq'P77a
%Td5rugl\K<^T@)>oF3:KW&%`ib1hWB;=$G6A<Tp&m;Z`aZ*oOk<mBQeeMp1.=\k6Tme$8(as^iCDpcQ6-M6ir#:758P1UF`".2l0
%'*gU[.Rqnd9#/*D%Cq6Rf0JXnd)\=C,-pD\/]^[_UE`_\>J[#<a5/$t"/]2`$r\iQ.eJ*W"+iG$@MO$d3&9.hO$V%,@id'O3tJ/N
%M6m'rMkP+CN1=9G$=RnhWo18bhV&$)7J4>M<`ntWRr)C3j=7@:qE@tTJ2a6t?d4Mf"]!EKUl)Ab.`"M8ZQj\tB&_7u0=:BX&4tmW
%dmISP,&FEe';\L-ap/.?#Y0A2YRhDQRCWhGiGiNs79Wd-dL1en/?2T:X$91`fc*!1rRDDK!qOC8\dj_n2T*=&@5Z)=Y:q8a1k[i[
%"?bjM\!+r\@*L$u5rK$JPPAuDVRMZg8VNNG(t1<U?8O0QQ7Qc;:_mJM!ZFSTbN8oeW`t%7'Q<N$"T/SkJ\[`;=u^G4W]\BOhlG;Y
%d'hkU<+T$^AHT^5ZjuZj,0W'+13Ipu'7tIsR.)[A//*?T0T'S0*aW?hofX4tA-Sp^;JELN,W!p<K3PbdnL@+&k/o.H-:IpGMN5=O
%lWG^Hdb],^2HJ5_jCj.OEZJ:cZ6:*e&Os2LA'4;aM:[Q>Reb02P+Zsj"E7X-gqbTJH-]k(Dp]LlK3>j<_FI2Y!=l,47#:*-nZ'0F
%$p$X1U;e#`?3_J&;2f0A'\ien^7":PFNmfpT3<pnOH.Mc0#.W3KN4%Glg*p%;;9#Dki3u,+i^)C<i\8)+cQ_3%&kTH36-d6T&ru0
%OZYmQ+9qI7I9BkD[VX6H'<:G_d3kGEFYVu#f]+aK?MiM=T:o-@\IKCpCDJI93-QuK<6?76<%lNgE7oO`C;T:gq#Rgdp!BEb:I>@s
%nR-tb4!6nZQkXH>(_!dGLGAmIMWYqpO1ZVQA?Di5OFt!!c/,D["(6%6_tL*cM@[;$<jKkmH<KUG!FCg>MaP^T,),X2R-5E#NT2'3
%*&WZ.VutW"P``]#X&+AK)_Idf:eRIB1]T<DQmQ)+!!DR/YTe:'iRcG-=A9Y'VE1M!a+WoI1L>piaI<TRi?`k'*+G+DL-".a+\l`]
%:6DuVa4"#'Re6eEo33QH71)nmj3\!&>'b@5:BbBom"l<([MBXVFqNrRI,4/`Z,7C(R1M\QBOI:TY;;A:>2n"VA.JNj/R?@TJ5<!G
%7A83"VVohl6MD@[!f$:-9VtSkM5HR^6PNXUU=L,%aFU&=1J)eXUO!V[X+iPGi@nXrkDHeao.C[sOUr:]5f=taJh)O;$jWnO.h4gG
%,aCj8gFu2dODWP5[qAI+cjhN4G9e[H7DtSOkgTrBr$gYI>YNRln.lq<N_WarkO68`2?UVM(W%"+m81,3#'$OpaGu,LR^#X?>J\)l
%l-"Bd-An0E6Ur(:4s6[5dGj(rE@($=d#Cp]#kNhl5dk*SU/+!:d:5LQI;8#"'L79uKj#X/6T?3Xg8tHiTV'm2M@(o9G8X7V\PlhN
%T8;@_/<,C&goB>[-b(u-QtD$H)'El!IMjrh[N%%5$)pqDPU"q;6aX4bDdk,9Mq"9"Wc8u[Y&=msf<Na#oU45bl/2j`nOZ,f`f02-
%6k&fkP!^\&Dkre*\(u9ZCP;;#/OMt,HA&VlJ)m\fs..1A+aLDKAR@hN68EhkdF#FI,'_<5eH5<Ah_Z+8_6V1pGRQVS[Nr"HUCX%r
%kGSo594F_p%PQg8,A*JiVH^PiG9EOsXI^8pE&BGO_BdPB)rG#pQpq.d2j::4#Lg2^_3dVY[#,'#BFf+Q(TEagLrk$IlJVLkYeiHg
%MCOGq=J=h/'QNO310C2"K*9gX-JYeVAat9[b2(ZY7%Y%e&DOL3hUY!9Jac"[#tC`sgA?[h>6K_I:rmHa4u>r$X=Lkn9BOl1MsPBf
%MR8j!^-d^tGfaEMPQgC8CM0cd()#l$7FF,U5tB:2&P7)m%JcLlfLCYW;5IHQCe-"Z/S<Oe*^q1F$O!iJaBTr;#KgZ2rUORY\Rc^r
%S=U7tV7*3e8:WmYZ<Fkc51EMB*R)TMp.K2CM%0YqNhI!mkN:!o1!;M7Q3arpMh@5X\krc&@>Pga"E6pEiJ&CSS&KVPRlE0VXoQ\R
%g/5s=im_GjFrKVE4VX*f'4C)1:%?-rSW_TtB$5;>X6$^/\g:>Ofm!o5#"B?+.`W[IW%i4-SMePIQpl?m1#O75SZ4$[LWt`AJ`p%J
%9i<7ce[XOWCGbGr2c'`X>+udhSf@$PCjm)X_ut:!d]Jb7!8/kuiO]:-^,'^GCL$9I*j3YSZ]8fM83h810=607l#PWO+u\'J'(I3i
%SZagnDX&a;r2fVj1q1;A51oij\"VdU_gk852DD;2[tg82>)nMtIS>Is>D&qi+&as=6CuH%2-]2=P%/l_OC0YAQnb1"`,[GV$h9q9
%UDtj4]80H*9)udR-uLldWe-Yaa@9oL1K%5@V]UO4/NJq&\!,[2$,u>dA"87KNn%l&K,OaZ:W&40p+"T?"`KgM*7Qr-#c>CLYMR]F
%PDOdSE^CFMOD0TN"N->n@OhssQk=#uC>UD5`u@7DVt8k+q,9XG\8d30c*`tIV)"_gLqP]uAhp'WD=GJ[M/c7gE[t&?3&B;ME4TME
%:as(Yh2;c7ktRG=#[OnjJtda70"A.Ll@J1n]B0cQnFkja;`h#nj&K9@%1lBh"Y.kbLmU4Eit[$);1XtWC8;Rq6L%Q.f_asX7oo\%
%i(B?4Q6%UPKBU\UQC:e/&B)a[MXQc@AcZluAX73=b,WO&[fIBVJ@(YGXHBAJ0.#N;0M\pFjch1OAL6t8;lPYr9=/TaSo&OIo,#YT
%e!hpE##+iZ*Q1#cTks=uE]4RF\S'B_+lCb?(-HM$I5CLUMt1%fCj!-%FF]Q9Es3hC8DpoJ:aOi&:D#`52bd5rW:!_=am;s;q%"2B
%'DgK^kh1TEP"8Qb_9.T\X1L'L"c/0]llt@#qDg(/JOpC_",+D->Y50"$LU%^?Q=^T"D^"4'SRoDpW0AT9r&g[3;*b)j_?92?4D%A
%Lf3Vnq.E.<T<;TsA]YK-\cMm'>GQ%;QXV(<bG1H3>o+_JF\n39X2$2QbQX@Z!'MrV\:A))fTt5Y2Hrd2HQLdT0JJ1B;=*:k<?IpP
%$==D$`P_o+""Sl<-p)kAep;id<[DA2MG-9cDMG;e"&8h)JE)7HJM\LcN:XM0n=?jW<kL_l'q)4/="Iajd=":q*,gWmCs]S]'lto5
%-6,l0+F'!\B;l(:W1I-7Om1FKfA7,!":/%MJHtI9bc0j-hSn)_;hLn#,htZ2PdKd,@aad"5QtN`I'lMW0"<9$FMKB0..'psAnoLU
%O.G+Z'S&0b=j'.u)<bokQdI>1!9ignnmXklki*XF[Wb!q$_=Aqn?Z5IT`r-?HIUVV:oX,i7qk\T@5hBtEIFs^jQ0gu='8Br4\B?#
%;1fAr*lgn)0HHNsYo4;Mn@fK\Q:=N/,U/NnL0kVK<Xf9+[,SX%,bfFtK6V6dW9.N?Zde$YH@Q+5l82V)kS6pGh+\J1=B1Y@7?4;X
%?Nde%f4?H4Ut#<gI\.%'EKN92]un]/AU/f'8X`I5!ue3W_+6lBA;aj:MGjVQ[n&n,`bb9,5DG-S4l.q/E+gJnl9t7Nr@7L6pd>Ep
%53#;Y`/7"01T4d*Et6%KTWfcm(e)OE/GMO5Mtbj[\%H8cGb3lqp"a@b1/7N&g05'*V:5Q074n4[#ImaF8TT<:7;3>O03QLkoRQP&
%:a6A>,C4-R@2hnVSQ@s'X38r2V7n!3:0PMt51K\nbAbQ?'QF"t?QP=^neEAVR^'9%0U[;lN33YU$.[6'P+5t=;B3^b.J+<o[qBY#
%fDR1LJlCDr@:TQ^ZCEXYDhj!]3nT<)"6F@aOtJ:+@NeXb,7kSu1/(AESss02@Y$=W@A4L`maPX5%r];'?<hTK5guEA0FDPiP!VLG
%3i>ULGLV7D[fG#\.sMB*\l$?)on5/L.o(^6Qa&nH3NN&-OQX:+XsjK.,oEh*AQpE+"q9.!NtGOr5,LcH8/=T6\/B_*eAC>+<f@%A
%2d3JM6:`7s-WVeKKKgeU@(*j\*j,Q\-U9$$=M5@G$m=NKEh:]YF.2X9,^?*,8+^@b[fkoT!6#r&84n7TTcP@u73tu6!R)>Ra?A/c
%=h.*[l/aV2m8i.4QZr.rDZs&$R8#1c45%^phI8#Qa6YABW>D<Cc'T-GmFfH59;2s_]:p[n'GSu7,)RR>9P*XbULT#2Hf/[#1qd[%
%&g_0$HFY7SRWo+$^WlEm:$JdB/Jp>)`Nn\MD0$TaR-2p_.ibut,#G?B)R;WPM1$[q/;3d`lGPs6CNRb,pC[C5f=*RfL!@gQ);qmF
%H-+*7]"Te"TW$*X<:+M2W6/p5@.EIrX`:>3^9Ak<"mU-REdonXXheLUF@uj9hG#f/!U2RIn6o%i@*M3Y_n;?.1!QbCL$0/n9'D9h
%W1&o!9@iBXU@#UNW$=]R5@kfPYb,GK<%K,[YiV-h@hh6"[%Scr%@UX!H`"fVjLh;;F'Fo&Z:2Kj'g&*k4'4nqn6Rf+WTepi[&2m6
%G$U#WAI3Gnim8_<o8=!e(+Yc/<(s$//T-ZPr%a]+[C0EXT8Ieg&X'o#R9A5g"/2*<W1o`!?/i:40EjDrdO\ilb;2,dRPb,2\82Q\
%q2cDnE^MrMbub.<g(5Mpdl#&U0$R:5!>PW$)Rk`behAs8OsCp[2?o/ZTcT(>O&U0gE`*.ilqZ);ij<7PoB2)21*\se?'BNK^s0-Z
%4relCa`0B+@ZmGj;%7EM)A9uQj1<=flE.5GQ>VI=!g8[N7M&l`TCN/4ZS^eP[9_$!el=J$(*(sNZ3H/<_Y]o97S#+8?egQI"Ng$.
%m&8HgOE?;6/&&`"*Lo6t!(UMIqJ$&@NC6_Z*l!Cb\g!S:*S!"&U/aF7<bdi2MD8LgJjk/eS`,V-bSmS?/=;C^s&J]a-GZ^-<&TL\
%+=`$FHa%A(=goP-@LZOGc,6_"'iDhlM(,[O_i*Q>9KWW%N,%")mQ65*OK'EVKJ(]3k&+am@p#Hf#$iH,ZISj7cjbDqP(btr+HT42
%j?>7$V,\Z$d!`A#8.KJ;;`N5*/Rcm\LGWLE>_.\IXh5H=nO+lO/F[pQe?gbFCX%\1;P+#$($U9Y`l(uqdY7q&idjfhlepDm0c0EN
%F\>-$\m9$c-qED3P+3!@NU,%nga\>HdN4!S.(<^],4''tEp=s*)No[f"Q9W"B9.EqiNX>n"-#<b`6b8g9/N3K(EfT;N^H[AVp[q8
%C;O/MZ@PO/0^I6EX+I<\+Ct[=UX1nHN:_q,Ts:=,=GD3eB-__e1c2bb;Skh"%'](6a^&>qjuo0\dM:sK3E^L%^=HfECc"o.-kK%g
%dR-Ak"768kQBo=[GXo-=Ze/TjOrS2uku8oEB#Pr*U-_T'!LsoSb.$lM7uFIc.hoehfg2?"X-:r!l(*->@%m(l;.>[=BoHY<TS/A8
%f-o1GLS+e^LriiWMm$0t[%LLF3T7HK08Zf"b=Y4.(i)#HfaF?+E_(j?&*Tq:ktSflN7>:Gg*S?WQ4V/8MQ!QOhe+BEc]Q@k5:(5f
%es.Fg$aThR`-c!5$$T:P:dWi.L@:Y%TB2"=Z>3<W+UH/Ek2Ac@3D8KLHC0t#p$`MIBgJ+oYj.I..qqqkMq(WNX?BK14ld$sA(hYW
%1UT_ig"cckE)ui[)Nm31`^(^/%!,PVYhKm]Mq>(qEf6&Q[h.$hBhLiG%(WA>9ZO!kO]s:Yb-\%6<]LPAAcoG`_3Q7Hi0T[&CQ_@L
%-ilr1Ps"f2&C8U(U`k@pc6U^*[&FI9-E*O]=+/$6*-+,.KT`'518ibs-4CLNV*7<Q8W(O:Lk/;5a^1X"/Ld)=&uFSdL7dT`P/hU/
%ppO\-="@CRLkV'Zn"@>oV:fF\n900/$@ll)pM3.95NaVr7F3D+$>Q]ZU4UhpX+Ml@N#@^Nk<p(C3F6hGOqkfP<eZ9gPZT17P/Q\j
%@SJ"""V#P!8L-alTcn`8[^`2;)`lpTPrstAh^o>?oEM_[iGtC]PEfqh5_l(6'3C`ad6"_m1lJ$=^(g=mn386(B_9JeX$3m9(?>X/
%6ZZDn,KEUohDA(]IbYO37>p2)SfMKb.BU0j;ie,UL/XQ*`Z1%uQYZ`Y\W0K35/%&]L_Wr_Fi5tMLRAV+4<e*ioVlfhd9+SOcUUXD
%HI1tg>^jh5&]orirLpW9&d#88j\UTFc,bBF6RZ-8,fqlsW6e8ofRrD\S0po$[N=k$WLo7r>#R%XdOBT^*g:,Z6!>js`JOs/V'U6E
%o[FVW"9knYB7qTK,c$&]$U5f"+Q8Z`-7bcVQA3lR%73[p\8:Z]gbNY<G-Y<_MBFWa?7dEr`9)s.e99jO-ci@@;.uHd2VLM0ZN/G`
%_KE8]Tqn@m."2TEAN^`;-CV\:7*U_6$R/LLU-b09BBqe`\X@U);%'+?a<Y!lQktX[Vo;TRaAS0,QrOBW`_MW"Qq;SI/=Deeo2Zb)
%9.E*/7QTLR][bZ*YVD*A"kh-*UM6F>YZOO9J#ks[[8E$\_a1KrV:k.$otF0le$o\98E9GQ0eaPeDQjaNBs"0fH;O)C2U3Y.Tc)al
%P8[dpJ6oJ#eeaVuSn$+^Ikmp)"Z6?;Lb>f]1AM'c9t!a>`?E29)KE&+0ZYsjC#D;iBUnYC,1t2l_%:e%cnu6"M>HQSEIcji:4^GU
%5sAo.EBu(J07#T!!Vc!Q,4-;D]2c0tG$B(h-<#%3/O8<t!"%80pBEIRMMo*#(77gE-RNHE$]/a)apKVq(9<_a&EZbV<<Ko[1g.l@
%MUctS[WDP15X:5@bbJR>Eri=ahisO**()Eu5]9)7EDs/Seo]#<Z4SKW()>NN(S-;G:bE[Z-O?q1Tor;XJ:9;]&J"_8^uVe[.g$it
%=qma_Z*sT3G!TI9gB^\\P?U'LRWp+lW]pO'NJLJ#Meg?PN!eT@-QRr8)-2+/ZkBAOS!Ekf/"gTm.$(r)HJG^lD048WS7V:/.iP>9
%(AV:-j&[XDSeLT,=[*km09)R*B!g.aA[@6Dpf0Qm(9CBRYfri+fb!\M5b!r'BE;H:rWm%R:5^rm0^;JnWE$'c:#`,&2Q+nI/ICAS
%9&ie1*V$0a@%5^7ac"k?!\GmL1LR53%:tXO?G[]gC+mhNesdgC:&o91LsMa8H:B2S5d3(E`T"biORAq*&skte$h@UiWC^'`F[a8B
%=p$>b7Poh4DJKt+Je9M1kDRb;DIt+OX2mVkm7QD@L0"nhpb>MOBd!YV4)'_Ad!9$=p;VJO[?)Rh1d/hUA:QFSX;0`o/>Xo*P(kJ&
%8E-`K/h]U[*)fusQ<AB09:UpcKS/A6nCao]aC.G)E+`<ab^RSoFZFS"n9(r(?DKYspSMC9'Od[C?`1\gYM\AELRaa4PlU!kYtKEp
%NU<3'PdH`)YtW't"Vu<K028<L$WqC,(gbU<M4!g2k%7l.0%dR0gR3Otn"BIHO=K,P46&70j.fV:<W6rO&k53AgBm;am1Zo;p'_Qq
%Zm**a1'^&(.O7%EU<_rX2BhA*;\'U3b>U'U478&E%blQLE-5TMZgOBPTbWV`3'll/&e;.iNiIHMc9n")UY/No@N@h#g8%T#Rh6;g
%\-6g(8UQQF"8!FD@gX)$6kmY<+R(=?3CSCk[TZ7]VJ?uEmE_N#=i'O5l<Tj;6W4Zq'J!JcI/+M'1*J2_JZ89rg37<>Ge&E/#8ffE
%dL>GG;u>)A'Wg(,ocW0/.%U@q52Nm@m'g&'QP7^;V9oZW!a(a=]C`=%)DX]04:1nlVK50^l$LN><6&K(LR4=H<!R\;;--Cnb"IP(
%>OiUdEj+sT$*ebT"V8j?j8]JNCR?5%ecaB\_T_BEFssP.)&f:oK&O;/LJ,Gf()j:4(c2P[=@[V>mP"(3pRN;$5VuNB`[0h>Zg5I`
%Q)O&.F4^H$1c<Va]'oc@.@HoTC+=8U8TF$>T+CX4HGlF"iRWlOdb.V2A691Ucg"?^efr_Y<FBh1P-lN-U4dS;a;W=!bCmd6M1RR<
%N9J_!:N1QXX_(^"cGEP-jtu_ZZ=Ko3Y,b(@Ri0.;3f!BYpe2.t4U)Y8N>]F`4Q&uE"Lu#Q*AVn"MK7ip46-ijnQiZBP:8KlOD'Ab
%bnYNkpQd/FYBViNqn*c7o[E^^"2k@p?mbYjb<;QCc2`u/&L#8iO8(.O)1L`3f6eI=E'%uY7t*1Za_b$>$/M'\fFa9DebnlR"oK"C
%ft?8UNJh0k:q$cf6/U,-\S?8k*\\1E&Y@bp:5lC[QDj[:grc^4B&iD;4,o+k+uU7HLZ:5LMWn/SPj!CI<6rl"Atk79dN'j2U=b17
%`n,=SZ(nQISSf3KpBF[=/[5fB8el?lG(m=P&/-XKgerJ;E0IT%I9I/M$]7).#!H.`B4Sdd@P[@\Bs)LtgeqL6HcT%WJ`TCa64TSA
%L!/L*=)]pLg-%k8cJc9JrAn6l"X`F;&5jlj87+&'Gmp.Vf3,$g)SdH+@Hc4KhXCRO89B26_NObmW==B<UZ8.P/`"j@W`C>[I=+%8
%D+Up=@tU:X'_^kP/(:!ID-27a'p92aB7a&clu%gc(%?SRUi)rjdS*3Brr>=j,Y'-fX#)P3?\/bMb`7PdlkZ-Q852@1;p7@?]mB'>
%H)%-DL'Ru29?'GP`g.Bu8d4p\jhapAXtHsb<7MMr=Tc<>CfWl.R8VXUX^q+lB+GKl/VZ$J=E=N^SHCuFN9"Mcg[NI^#q0*iQM$2e
%[kJhS7"t_*(m8#W?5"[4edOqTM`aT'):<t:5(]UeNW<Sk_OBdEnZeV]$L<^SiL/mqlY]+dKMW(J6^C63Gn6HU9scWaVRZqV<4L-k
%ee+$DZN_g:f&6O@&X>11Mn2B*2mnld2"Kqe>2L3T4mW)&4X0)mWt7$/?pk-Z@X[k2/BVSUB[K`^^C26jUU\d@LqkMbRVR%Hk@Jsj
%]4Y<3"_A.*OTroCc"%o)aC-VTMlE'^Q-^Q<iYoth"(3pN(c;5X"nGlW7E,dpE,qouPDL(3f*u,h>1D&Z3#e3fBdkul8S\U:$<XU(
%$XsFa\oVVee$8WQ+/X["^,KRtdU%*T_b11u5ssrDGafFa(A+sOjhU2_9Hm/$9#f(E5#1anjLg9CK2>u[oRQ*FZ71@13<XN,b7cF#
%e\g-47"kT/eVI*""M0-t`0T=77K2=H+i?7f;MOg$>Rb=K*WB*4A2eS*QREl,&1`@0Qi4j&Fb%3NhHor53Q7/:-1t(Sglspai<^(/
%@"-<tN".kuYP;WlRo3?Mi2AKSOBB%:j=d`M88lq5(tV5@+ce2u<%#qp:O8HaCF%4]<YsaG_<ttF*a@7@P0W]WHm+-=kl<uC*Y]6&
%7R@98fWboa6jc^hSjYtN#&lXjhf9:\fWcrNVH':&ZI,G@Z%@Do_qHgO`"]de&8Ms:XL"8J:pn.sLT0K0!LG.5>JR&20sg#Q5du<.
%45a[#neqqrqP#]:*!ElW/L?/8VD(!f9,4*HZH4g%M(3_Fa=[eT/R,6O'1#cX)+5ai2aohDiWL=FQHA):IZ`Qt$Dc7C/D_67nJ[7Q
%bk(.ZYr@;j+eho;4=t01@&6`2Z!Ke1OPS/:!GKdnN4C5:R-?+>bmY%.q3$DB6eH/9++eT#'/%@d7^dBEYSh192)j6ILbgdrWnhR0
%'PV;E0>Mb2<mA:P)EHim29S7VdEL.@&iETH#X;(H`L;I"-rEXb[&#N!dB`nl[TfVa.>0W4#%@FCE1s!J[*nM=="FtY4d[3fMs4+F
%I5fMA%ne]X@D[]!=JK":9B^!(7T:OD7H)1c_55K%,tGA^(C83`BgI.p`68KcJX0POLt^O&5ur,ol/?5>o)"8P"mGlrHFPg:XnL$-
%5n?(S-h8Q8%UdDliHt85=bbP%YF:"CB`aU!UOF[Oes`:,1e3K*Y,/FWPWH^ZCD13I4aD))P/H'O)QTucM+"iNH7h0+dssrr8WK_\
%K<RU%gNgb$FqThZ)>]G!E,qt/p8Vr/#i0>(e7+h<O\6U-O-.p"jN&\3?.+Ao)!m-rRLE<)8r4r>!_9FgEAJP!:D:HUHA,s$Uh3ZO
%4tAb(M=")2*ZUVtk1hZ%H<&HP6btBUV5[6jCt8nn(]j$?icPPLJclDgT(WZJXbh#AWnFe:XEdSP2F!%>YUUsDJ%Po4RLr@ka]mGm
%4tHF'7GI"STmdVm2(1QU25PIj9)"/[LHWo1Q5"dOnM'*=J]$P1R#E@95'I=];"',0U;r2C**SaN,I#LrjY4^B</:TZrdXtMi*Znh
%DGgbAbN>2@ie#HBV'n;hlZH,<&T_upeYZp^SM&9MdMQDp*qg%\WBoRR]Jrm6,Zs(<0o#34X2.lD/@b7DRf\rl5c)IQQljDC0lR/`
%"VFXODSqo=,^tck2$m?83&qJX_?1j2ekJ_/&,_B#%ht`&@G.S#NDoD6-Jo@B4B(u7R:,_"+P5ABOd6AnQ6!>CqsMH'?p/2*1AVtW
%CQg'QHCu33YsX=X(&hMM:3+j<W.sV#",C,JSb)1`fELh"4!WeXA^oY-_A3`k6p]E!!p^tB*4Z/N3"mO,Q($[W6uTM0_l"ZhQD(+r
%eQJXqK?q?;.`@^JVg09U]Vn;9-9O^-8Fr5Z8#eh/n/'#H;6;X\m7n`'a')P$B%CW_Rt`.^H""b-LAH1rQfg,]X-+C]Q=WQm8m"_<
%.>gci<JZtD*,^HZ-`JN9?^p.V\5n_X3fKYM,S1\"9:5ps-ff.r-R+pN&]bEu21i<r<au]No+OQFR8bq;O]C%cUH(Sj@(pN-^U:5-
%]B''q-udTsK$IW03b;WT;e:sGYT=A)]Yrj?-1D66NLdBm'IU)ioe6ldEZ"NI>P^mu0H13c1HL<.W%o(W7FYc38Aoi4jguO+iknY%
%L/VG3NLj!g*7=RU1ec8Pcq[s;qt^)-doid&U.bG9LpIYV+W!Y\W:re/d4#%PIn[=&+`)6'%.S6h5mQ%F>Cj"VK;bZD8JrgB9O01e
%RT;rNAEo"tM[8C]<JZR%l?7XcAsoPl9VWfJb=7gMVMi9h0p&=h=B?jPJY7)jLC8\Scd/TGZ2hc!qr%!"'EtfZkTN>*PQ]obpmHe;
%c1"UXp<$om*8C;s4Wkn$R+\PdEse,i-"*Qs^pWO^5S-O#aOe@"6.MX-gC+c7J3+RiI(a+hd2jJC.;<U%oW$sUROM8^hH>VV\!!T/
%ERpn%(U0:$E"cGl!)l=k>o8!YKitiDI\_JJSSFI$5il0s7hW@`:jdZn)JaJok)c=VGG'h/OuI@"G6BEt(b7jX(V"HP/\NqCPF`Z<
%c2hS4+h08Ob#A]o/BitDHsh[u/^[,aDH8)3.&4Q/Z0asf#")T?JE[\g=$[A9+^1P=M_QI9<`7]RrjYq^Bq-BGo<\p>6el'S_/>@l
%f#'&FpqYOBQXONH-Sojhp2ek8U?FUf%iJ:#b(+VmrGW@F`OX!SI5K^Xp9&NR_G]=6![!"oGYOo9^3X=+oCu1/BnsU!2*69C,ap!I
%jU<RlKS<eLanso<Kdf7gj5\g/@EbL#MCp^(4J:OE$XUl7oJ8_J'ist!pmSq['9CW2oWt]!&k#;dfCEXY','CAT\7[5\m_oJb6iZj
%TCi><H*AI'c4`&%qCGmsfU!'RYRY;c0H:^'m1:"SVrN'Ug/=8kD9A3<f76fC+Lu*Y/'Wt8/7&[@j9;\>S!B!Ul$lGe=[#Uq=S5<C
%W`fH.de3tb6"APZAL2r8pV84]E'tc8+W84P<Jl34%!-"$.Y+`W#"lE_H@ps*njmUdYVXf/DQl9m&n/K`,RHM>[8=%A>'*=Grf0'-
%fJ5K5A`"DsZRcC?cenIa9](gc"*cT9$&97l2;%C[L.=+O3ndXJ&M?@NN9./:IC!N^2ZuBWEEl$?nLcuB@bXUeX:rU[453<k3#PVH
%jcjH*WPCFmR#k0&VEIf,R5o10('KdifXAN3hHHV2!h4m.#Mh-]B*6<_HsON_BZ:N'(nbZ-q,+CE8]A)0@s8]5N=!uDDlPckY(KAL
%fcVCA`*+trJstI>PT;)S--!;kln$N$XB79dF^gEj]"a+I:eXOhCGZn03(V6rf:"KPKVVVUK#OhJT>[8W^!88s#L[ubFs#=m<%R^=
%r,t@8c*1YX;;7u0]?QaL6rt%2o&8!\ftBe\)s(hb\8[FdjA';Ydt_@.8A:Q9H,$!3AUe.8%dVXmciT:,[Am:;(`u-SYb7^CFPq2W
%YOMFZZs,W*h)mZ]3uRSc3s.HJp83+gq6Oe`p4cF7`11N1,;W`Mn3AP&Jn+g4%Z;6,$RJa!NHZ@%]><DuD@^,4"DcFq;M1DUJtnU9
%1?4`hDJP\dRSMn/<GIF":;a/C#"U$i'!+EB6s6Gn`\<H2iI<]FB?`uM.Z0LmP1<PV0GQ17ga)sYpu,P>Q`\Y%c#hCTDJi\=$4oge
%eH&,hI[[*h$Tn\TM@TT"%OFD2$4.[P&V[P:#P!ZW+Yh2U5kF,Yeu)M*9o-eT=do5/=I8JOhmRu;AWc3%R$KCU$:$!@UWXc!hTtID
%e2SBQO>/oCY"T$3rS[%VKV8b#2dSgF0?jQ!!"m*0i0>.:l)s86#&[-Ij(Q)[``ff3X"itW+j?eKbJ[ON0nob!YKC+jB^Z?U1R8@U
%PcKnV@e,$qE=TKmj5"&#@$"iXqMnh\%Sa8gOI?C7[PB(ZmuM_<!!E4J&>PmE8AQ\hQ5W-G?D?E`S^C8n*]k4:!E?#nR&adVJ+V[n
%?7eO4%k]fPI,?<lo3Ti$''li<S7*28^=VuSW&HgB0+g:4TP#+J2)3W9o!KM[/Kr34;H#&WO]V6@=\(Y&C-:^0H>j".E"/f;</W9B
%befd.l;H2H3_NH%&Z3'_rZC@Ep7DtDJeSc'?UmZo_O?rX_^RS\D]*o$-B<[=pO_$2O!!V8SieNpT!c^m9b&<J)'M@8oS/CW'hkAp
%NcQt#QWLUV!SEFWF=[>u"[nWq(bBC!fq:$E#ek'"h;J5Q(p_C5ABcpll=^bL.o7W(mJo?S+YP.X!VeoJLC2kfC7[.cdV2WJU^.Pg
%<.<BBL_d*^g]W#u+^"uu.$"17cr%D!R^8&:JOZ7XAVc/"@*'-U%5]7SX0\:k*4lb>iLM4pWA?q1boLAi-!:I\#Gd1n&eac\P(mKS
%;*C,U$C6'0oH7!?&OgbgLmB.r'hk'0lCLUf1&;Kq=SZ;)H6X?.,$D$h,)-/:_h+Y+T[#?Z]WJGZKdg4^Z/RC?S_SP\-8V]BMi#9'
%`e%MoIjQ^[Ib[uIie7O@:Zri^\_?&9^.p@I(7Kn,X"ZV:7k;.Ro:1Mj7'%*Hi=h@pdi^>ueq6:"nKSTbel<b^62^aLS?.;GDS#ls
%nLQ21C_oj3Y/"7$agLGD.f(D(<ON>%Kr_Y%$fhCIFr6`=4q0YlH#"+GRu#0cJGIt1"i1'85t]ZJ)dOWp;EtKt&\C2^=`^^%W"h\:
%;KKd3An@8C0iNqCP\'_X`.0lA5^J7.!Yn+kOeK`"pn/<$d8*/Y"6MIsEFDAo$7QS#kS]?#TGY9m\Za-?BFBs'MTJki&5t;e4h"sX
%2_cp#>0%7PDm65K8BYFf6QJB[Jt`_V/F6PZKSe@ho6H!kda/\r+Xu"XU:Qe-B'N9ShUIM*[MJ7\EY)XAf-D(T.U#3.#V".?6VC'Q
%%ai7RR8eder@ub0ECrsgNLXmD3^oJ=*SjV8E'\,o%N3l'/hc;=ZYp/(Xb3.c^=9PC!a&5dKjLMdVg2W1-#gbXSo)Pj![F9L+Nk1&
%2S5m<gnW=>X=gI)W;*sJ?*\c)6r+C`p3]j=;h57rPnK@ae`GI=#<bqdDCY?)'SHS4C4\RP)RW&"%h".XokMp&ZCecW=ZL3O!FfC>
%Kr:t<`SH^n9]Whc;I17``VuoHNSh8J+1$^_&[C$rR^ea5(g\LV`Gi"hrRtbBop=SXaPdR0X)e$,>X]ApIIS0HSVXe<_*CNYG<AsF
%MejP1`5-m[?H;.Ne*';:bV7Vja&5oH;370L+G`r,W50GV#j'_&&sSN13V5[(mX.nhA0i!nrljm+^lYIa(kC:6Y>4A(16C^P7FHlt
%B/3SncD*_AG>(IK(K5R8>KG>KFV#JdH%0A.Q8'mk,H!=d51uU@]\!4,SoTSf_(",'I\`T9I%GSZS7H1$1N5a6!9U8:M%Cb_.LV3T
%N1YkoGQpKC5bS;rL^Oas[$8N3"'Da;2$eG=ZZBsJ&]cK+-@14E_lq#K,Q69eA!M,'1c,>JSELF!FOP3?5of`PZFVAkd6qdtrO4Q>
%PA(P#YSqPkeQED'4kppmHL78pTsUPm`!djp5E5MG7URLhlQ#HQ_O_Y,-ROfq-</l:@iQD7/(eZ-7GMYm`a\S?Q.mN'pI(77:.38G
%>XnBF<dF&G`bYoB'<<t86I4$^jd7W=.'DcMO_c$QZBEc(+aWE'YOPmVEc+^W234/]&@S>\gFt3FH%Q9]kaf+h>rT+8,RlY*@Mk1t
%Wn7@B7^L\;T"*Vkh!C=F:Q8d4i5uMa!BDYniW'[G2`;&l<QKIQTS^?GXZBa(iqSYf=X&$F\CTYWD)nm7=@_C'h$uk@W_1\R\79;]
%YS^\uQ$D6kT8?T>@9[4R*F+oNC.K"dDKT_(4cCTn0gW+-jH@;ACZu3p7U<]<JXOmo)(A]Vmt8)Ca6On`W$@I#Rq<M6,e7(h?H+l!
%`1*K-Wc:n1B2iF)3/;"==-T206:+(k$0FuF_GBUl4?o9/F\%F?8ZK&UJ5"mIW==^u`lN8Bmi/&l$nQ%H575;Z_F%0gMh#tI_gm:7
%#6njhQUPF)2%GAP"MGW@-08Q'Ro)BgZ^R&Ep6,ZWfJgL_n7Zr#Z'$5e"YcfQckK\H]`b'[1l\GB\QchG,#:PZYDBVHA[U3mmnIZE
%g.QJ$>VO<(^B>R#WO0HM&?fc!D7G7E4P8!VM*#"r-ZCYUDnZHI<(^?VNbJNtPZB.W@PjKsd@20s]8e2QR[:D4d3\!I1RXOd&t[;U
%f2U.1bDn2$K1it*UuQUsRL-pTXGKV=LD]K]FP4Pp#p%1A_R_V[=?9<@o(sMV[>a4mRbLrMWfIaj6VM;mqOqTN(DED/7AkBH2@jZq
%QdEF-]IX5C/tjeA/Zk+Q8Wm'6bSdps2gR@&gpC$U^#/edE49SDBbfX%>@M'I'_GiYCr0S<d4IOG[\90sN=&7q)<JsLh+A&'7,ihd
%Qi\%?L"*K/&e9!i3[u3c\8gbk?-=c76X:t\K?!DI'cW+q?/,54?oLVWH`?XT`WWEIOr%paVCQOb6#AmT6$&6JHDH@o369'NLXGD5
%=n34E+Oc3Ne#8s-kp,=Lf"lH;6F/&6L:CkqH2+No;CsIrAkWSr5dVnc"^(I3j>dEs%aUI25h;ZsFE<<Vm[,?k\Q-/BFb!%_ak>'I
%f:u7R2uikSR&@ljVmD6B@qW;BUi/&64>QYEaW?-<_Q#I>Z<M)sYo_N>f*7-[M8=&sqn3Tc_.DqbZN&CJ,H0^Gk2R,[Tu8Jj(GUhn
%,Y?jG?\EJp"jU;4p)*FXP+3HEbC`VJ8P1NA7_CmiA^EIB6>aD`hrdg!_'AiFM43Q\Z\iXCH>@pi@!it]/t(Jpec_df-6h7P8?`V"
%6g4c^;1Z'o`/N%,)f]d%o8/:ciJ@UpG%l')l=r-k5=K<0O(PYo(EgN._W@b(Tna[3LYF$e(U"\JXnuV4iDC#rMIT6-=Tu3mMl<)B
%9p<g6aG8-$::<18ZRM$.%s0qK=cbR6dJcMJ`&A-Ye4[ZFdNgfA,>5W\.oLGmh"M^;#cAeS.nsk`pIB26%dnMt2Roi^"p%h#r,gS*
%Jp*>.S5+V0da;&E6Ru[WI6@cPr&.)lVpW>$K2a#E;.sL\9M0VM3Q>n7j2XCL7mHt*d:`LPSU^\#(]W>Us7Oq.?bUg[p8^EJgt\F5
%\Ph]Gci/<@FdrBZs1#X$\NG4e`8m[Z5>(?V5B]Q3TS/]Y^A7;[hU*GEaROaDqj.65ru-CcpRlqUP`C3>oRHkD4*j&=n_##)7%4QY
%X;^+P%3VA[+,WmWg*Dl2I#C0JDj<HCj'2IE7_dL"'b0L'j1iTL(>="2*S1rGY6Q@2'ga]%^O$fFQpRf&60JoNWi%ZoqLd![T:Jdc
%'2PA=&t^VuBQ.-,O"JgO;)5j4rC*iBU1hP0*(848$++=N=-P#lBjt'[&fmLr18X9%.#g7OC_3'L,JbKl8[*S>];%3f<97?Ni9).B
%'o.W3l0Po*i]&iLT/DE]<V!j4U)#q',3LeT8e7/9jHZ;T0q1Jq@U`tZ,lcUA;Hs9[ND-]a->q$6782l*ak?i>>.VRu0Zd6&-RK>F
%e`6Dr<YBP6,_i+IjV6*.,_0WUPB^95nidb1,3nV%SK:(o9<B?aEiR,%AeCPtQpVQs-ND1:H5IWkVFub7,j&h$fT[d!U7!uC*e,\4
%f!G%bYoHH(8!Jk$PU.Ys@3:okpleos5*P\:74)VmM5d0Rart=?QX8%)%R';aYTDH'_fVLh50'okK*VH2-4Xmr$SbHrU%L,5>3TpR
%Nid8QkTmW+p<5VO$^FBP1jL`aE.V"A`:4so,t;0q-IpIs8G7MuTd2h"`T]J07jh.n1dt3(k+;6.l648T7OpVUhJOf8c`Z6r(7oQD
%Upu(!L?+2WW+jLuNeSEnb)=HkK,=+^';]^t'-gLCA7?jY?,mMrR4LrPg+TW3QW>Nu+]Z'QjJqA^S/%SdHRNoSGTC8=ni6nUCpH@$
%BeqIQ,ta497?;-n<RSeG!4,#2h3Pn[.p8V*A.2s:RbT#UHq8T\Y8>XkF-#7e`&#NRhP,*2,^b?'*&[R"lVJu>Rcsi9"G([Ccc(EP
%8LNJJZ/VE>ODS*sXSL$^(;8Pj:s_)'_DIFk.,,TKPRGsViM_9#&)'"=L>DJF.Zo-tG,!Wb^n6fi5:3[q9bsXLY<=knDjYOL<hj0o
%;A"cMP'b$`S3^u!FUUq]+"sdf-^NJR9*U,M@3U0X-C<<!HR<?^5d7qDoEm@%S@+<8Do)dkjOhQdd5RocZT:mX]G.287:m^OhR!M"
%)u8R*k)'ePU+O`e9<k3ckYW]IU:h7G,*>Db;DMenTjT7)mP0L5aHJO%!?ko-U+K*:a'Rm>.$q\`@gR8t7"Q4c\YH*S?A?uN?h`!E
%Gcp`Ojd+%!EBGfd):D!1qLF[ZYm!7!j=&IuaF2I#?5pZU9M@3r.[mgXRlob"oJ+W4Ei`U2WOT*jK+HZ<B1NZ_PP[k,>R'^442GN/
%5.m:i'A>@]cDjHsGr-^l$CJH0%o?&D(ZQCJ2GtBF]4LJ.iD3UJ_;HV+6sl2`:/??%+?_f;NRS!XfS*Fl1o>f4V?K%U"QS_=Xl4S(
%ZGGnUjc>6/K8EhM8mZ.C\!"Xsn=%Cdd24Em]O2q5EB'TB*ioj:S0raZ.9&bR&KdRB@YTD*aq:?.05enBW\t7A=f+ic:+"QAPn@Sm
%*&nj&ererp1cnk(PV4C>$)uL%pe'(>86Hor,")@W*Z.`:(![IsT7l[<bEn'ne:O%(D?]0l,mq_274)P@Aa'@7@*$(T[U;Re&m;qK
%,h>h>89,f??77m/LffEV>;'d;)"r?Sq>%8,br-"olcll0r,>=<"<S'OP!C;DI1#tjp_Rt)]^U1jp1!d=K0tWB5Zp$e1@'Lf6FiLd
%p*(+@ngJJF`ZPnTX<=BL4_V9gcuft3U5d`)\3JYl1Dg]]eDE4$O:e)CoLr0h;:c+IjV&ulYmed1L4Ct)BM#@'<P/Vbp7Y==+a*lD
%;.@=tN*_I*Z>DmnN.#YJhs@)s8Bdf@X5%N3A>FF%,nH0><!f9?'(9D'=!\J<F[927nLe_792YKk-SeSZQ`?\<+:07'jo,B*[)m0)
%E_<u4&'0u.B<<9kE>IeP1[c3Xc]OD`AjJ[cT&8NIV(rYq.>BgLT\laZN,;?7-ZsdDao3luX!fdI>a@PHK,AD5>fDamj@Z+*$//N)
%UDbr<WZ$UEHUt/]9)?OE(qE</-?)5Nc!jo]0*@M$'4X?p_AgO&=gKQ&p8eX`c7*P/4`ne\i,<Sb1&7=UQHEldnrV:WaWY(8/E='.
%U\FS*:>[Q/9q_1>T+0Aj-Rq,rX79Q`/ClO0=(*=?Zpc4?9B"&\5+Eba'j63BN4)@?#jHnbAr%o`ZtNrW-H7RX[<qQaCgn:/!*qaO
%!@e4\iE'IRKP)FJY8.G8HQd-al2*"VkSVCQ0,=Z7H[-b9?$3_u*9d>fli\f8F4(>;P=AOHc""YXkZ9#<"hlO(OqJ6n;N'gAH:W,d
%,]iH8iA,KF]N'8C7H#C*.#3K,.2:u(]e:lrNgH=cLn1?!euSrm*)MI:7N5F^+W'Yfad%mlRuFfE]PKaLU.->`N$4$:7s\iO&m`Ob
%8Ob-F3B@qW#cp1I1eXP0H3^M#Za=qC$`,XbWg1`]+/9W]p\*`h-ADr6"hU70/;.QO#Hs1,?TH+p40O$i$q2pNIOaFA;UgfmA&Xn@
%=].`Mae\b[>r7NV+;Im([>ST.\sFN\Go'SYDpE'j%(0l5RbA_rgo7^?C"-dBd$*h).UAiO%q5^+,`Y0!7RZSEbV@+CB=.Q_I@[$<
%#brQh,rJ_&@(s,:(*m@BJV1TU-nW5E(e;"@SMa%`qT?*"EM]T9b>L`*LrmY%A##ldQ\?[`GRTOFb,8=&B?&r8(gZ3eVW`r@"(bab
%\J.doGDSkB'Ali2<jNZZF@uXV--7TIktoAbhP\L$16Da%RRXt7NMVZ?<)g5iT<F\PbLV_u6$O]_%\0;_+ldOeZ*:Z9nT#0h15`:,
%\co?B'&[:8NLYpgZ7a:Mr5)Ji-`D%.0'TE_V7i[Hn>$^`2@6iBAW0Nq.#ZK,)0fOuPF:YPP.V$0R_e=t=jr<7Ypf)dT9DtB,dm]Z
%1KR_,'S'1i3-eI`!1_fKVYl$?aI-j2K1J:P*r=*RZZUg0A?!Ge.=@#gQXm1tjA<W/kqd_43UK:#:DH=<]c$Gdd!N@`,\Su2nEX0Z
%i2VW]nCSiZ$9^(ekKc.GF4!&e6^3-n_ER]Jc6ofR(MiFh]Q-A5N^D'l:=C%0YD_kU#9$^a^aQ)='JF;MR71_]>$4b6<J\TkNE@ml
%\@g?hAk*Wo.%HPHPm@_>&><EMAIL>^AHJM.?n.\4A;5]gZZFa4W<%?+u/:^T7G2]Tk;LoKoS6?V@S_R:P`[%<SI`,Sn,YFQRM
%\FDOp9_9gEEQ]>UaJj<*)#1`t?##Y1OO6..K_&eG,0>J_"V]S&Y!&`_b&l8M."%s2Z--,p6&2Oubdr)Tg&m1A+Y17l^,rlI9SAGD
%/\SUR@Uga>1[kM8)Cpbe.:V9-:WSeeba>YkO.V8pf>I6#<P-[ZoOCBeeK3@4,t,Gs>k*7M9gqb#dY""6.&Jg^FB]uc>(5C`)!td1
%p.Qu/+$;9XeBdJchRb,I.<Mhg#X9^0_,MWP*qM+MQm0r#>ZVE)5U?#\+"i/d/S9qfV+PPS"Q-7D)&bkNP>1dL'2h;h$epgMn@4s_
%-8M/?2g40dOi:b0"#n4NU4TI&2ih[Q6t=o>0_?J?/R@5O%pBM'Q'%>J,1ZB0B1mRoK*b0O1lJ0`:Sp(K$dfZW$l9!^2CW3t+ZL77
%#jXG8k=@C;'q=KR1fY",cmdkpZ7C)FP8L,&R92%'oONh*LPe`dSG5G.=*Ep%0X5tL-#7[,jX,C,3:DPQL/]6HaTL\I*ltZ1i2g`A
%(XMc&[';.@C@N:JgQ^@?9&*f@-fST/m?/qKT5u32PMRFrJ;2MBXM?On,81rihTTV8<$fC0;Cd'$[B'b`G@?YcB$^#[5o`1&&[RN<
%*YsJ<Cde121(ft!0_pjhX5Yu5"DAhcAZ3Sr-#u@'Jo%L)ZA:hH(VgbAAL?BJKAON\o0SBo@Kc$9ZQqq>8pNAK5kGCHW)KlIj4-];
%Pf&[tCl:jnM*4L\YH;?69i"rF)3@"ag&i/-P7;8XjX7'nj6a[WMShk(XJ/]>`3U,8Sk3&7I"=>q[l@S&7?/tK_9f\9F&aF!YX3/8
%CSkP\fr$h7cH9bP%ZHs[Zt+l",(*g05NlC0#*d2N06V</4."<Hf]=%9o\u50*L\o1epHHN^.U%oAk-1@!@5,KVHhF=,h)H(OD$LQ
%1]gL<fsQFF(fRIj"p$GkU)+!":<msWP!1%TFK-Mb,9BY6$PB/n_DFqp[DOVoHC8!"B1QsAW;_G]#QJ:h>I0H738d^0-H3'R/st'\
%jD$Gj1#\)O<0aUIBJ^a#gjC=21jqcVkb9utOCfX_nPTqGR6R-&0Y+7,QrJ<<Q_I/*);c+\9u#ee*R_9*:0A[>8j[&:9dWIm%/)5,
%GR>*e9JhgZ&8%2!1Am:H\^ibS]ilU_=*%cUD09!lU2?b+#J9sP"dPA,Y2$udWRNLfDkR3mQ7RFDRGQlpe]Z+C-.S^F&_t/5M)GZ\
%Q%oRT13LX"@MkK(&X$>9+%S1<RQX@;]7^KrP$]t#&rHs'WO81BgBb*[MD:bq&d\NO;N>[(I?B!Ifj.QJ]G,@[H+5Mo5SXiU%caG#
%XHf?_9B-n+_f1P#E*A)eN/63*UaIa7[rsjn?QKK=3CV$;UZ"b3W1X+XqW\T0cED-2YQ"IZL!htTC&OA?s6+Z6o=oPus7ki.rnDA:
%01K#-rmAL5had]-rmtet4oX:1lSJP;=6HJ?kLWc_Dn^YG5CISEe%]0p>`F5\0a@F8r/5bXB5`9Co^gK"(N80#ghh-/ebR2M2eQ4I
%+rpR)^L,g@J#*+Uq=a,1rl:X;J+;E/V`2B'IWtI2bi>&AJ,@pH^O/)VE:rDK^\I-:`B4%?QTU]9\2qBMqab3(f]'>L9Xe4*Hq1O@
%`P6p%Mi4dR3r.RGIsU:]2p@-tm:nEE-h!\G[i=K!m=%jiZ'2[miGZ>tJp1EGn^Z=gT>.tj0G?:un*,4@rqbcX%C=H*5MnBYIH-1C
%r<mW<mdg+M2^a)<gVO$D`k0s^Rs/ebGlP/-S@s>pZ/rKiiQ`:]O<`XISM*apT)Ifrg&HDCG'1BM,Mm.LX3"XQIsQ0bF8FNX>:0-2
%g\pC7@ad?MeU!Pen_M/di3W/R:O_unH!0>1aJf&]NV`T/dFk/K2m6f2hnA`1bTG-6*oi6hKHk6>X7Q#Kot*/\BE.\fjQj"p:UeP]
%dtDm'T19-r9]Gp7ZSs6jf5EQ@AN&B7I<fs`q=XF8o:'UKjWfb:Fd)P1i*UqUp>s[L#?>ZF<4R*IjUS<G6'_=oVt7f&jYefLK.=?G
%<GmFdm\%\hT-j<oS!.h,/q/"mrq<#.m1[r1k]-o6m\%`dBjGUi3PR]Rle07T?_+jDroR@nX^6ZnT^?Qb`4kP!l$;MTS#5epd!+Qi
%A-9q0GV;!hDf9Q#s6AsSk9i*X9nMujHroq9io8d(0@`:c_<k,-ppZH2Dr7.Y>lF6`g$Q9(]]$B6hSRuRmuY"sRDY=Lp@Sn;fko>N
%n%%'CQX@Hi]D7KAn:^9,ms/eHns43UV;H`MG5CXdjNXakX1(#8='PC[r_I\a%_:s?r:Ss]^:lZ*TF&o9ltV+g,]6L>k3;QXop^I0
%k,ZN;cWc"<[hi6W:tfegPM6Z!o(Ln/02G?h5!;'^2PF@"%m9e4RKhT"hnOO)^AJW]qS$suTb*:boD\FXT++H`5<IB\rO-G,]6PT=
%R`0II`%GV[5?sgfh&L>h6N$cA%HCB8pl>%#H'1M?#MK@ThD;C(&+BITnhGOoqn4j_:$;=+0?:eHJ.68>?aa5:^[1I@Dg:"8o%6'C
%c,&jF4khZG\7H.i^\?\A6e&4uUaNsTT"o.:h#-4\DpE:8\idUX179N_2I;p2r8H`7;1$LFEI=3In:t+l5NMD&E^P>'p?'\6[$"3_
%g=^r&T'(QMe573[Hk%#5*u<&llMCA:QZ(0prb$kWIfBM<2T;0P]qo\c*"Hs^s4$<.6QD;l`QJ]^phro6T>0-t#17D!qUoqHb'JJb
%EO*RDqP5sS%3+nL12=6;Z4D1jG:N8l^\R`,l\k'nDg5%O:^q\VD!\sMp69hcVf]^+R7deIp!;$,SjP'n1Z2DCD"&<*Sbr@Kk;)Gq
%Jj83BfM1,)mh]J$YiM"kmf-aS`;!:a]A"P0*ch.-BY"(gHa00b_t1eI$N@QOo_e!TPH1ajMbhYOc;(k5@J>8WX/#BH%IScXTATU>
%ltV!1`@%cMDejO5NmeWL0STH9!.#F/g_UoieWZ,C[S5h_]JdMcJ,AoXam%L?g"">c:VM:@:Q@NRIu`sql#6OrrdEfk[to'<\CZLT
%n(GP^1XqUs$^W69]+&0i:IClUSfp(;al?])0'R;hCNoKA^MV\_ID>&O;R;u@hT#gfeWXEM>:/rihthc5D&WuMQZ'#[[m]G"ZX)t[
%L98,!hXV.^mqrVRc<*&Ss%H0o^HTCms6GtpFZ/(+X>]RYF_^^Hms#K6TAT&04$.nP$.5f/E=8.q[+a%eY-+t:*5BtOGF.d4,Pp?L
%"tbgY26Df.X0\!!)%EJk(T5$]h@c3i1u6]XgkI'J"]]NrieW\>]:-ma`A]1%DOA]\(iE+*C_SIsjfYKQji//-0@):([Y;J'lr^SR
%\N7L\FQJ#OYt"3kJD"896R9f\-toO).2$48A=e\SiX&,LCtaVl^DL0?f:H4qb3AL)76+<U4N*u\M<0-.c$Cj8cQO(cJUKPsUAV@[
%[m;lr,-`PcR)8jCU-&g/mdrNYU267A;gE5OALr`=!Q:^K3eVk+((RStq^PGb@SuiR^MV\_ID>'n2&><G3Q7_/RESU?p"u[6jse="
%Ctd&Ci#95_bGPf@[jf2[YS0eCET?;"`((ie/k>E4n-aa9n"9W;nPh^0JV@Oi(1W9Akc#^'4?#OiO,a"B;o\ZB-o2+Bp*"gc*;J6B
%,rkD%"(+.Z[3rfBIf3kT%cZ2.>h`o"G".8Z^srp"mDHHf6!-2SCBq"ci+gSlX0!G:cZe=)hR=faYQY!)9D1q;!"[]F_llp#>b_a$
%E4AAmCNSmdA9X7s6mKjMs0sL#EG<$8M<kIGJ*RX.Ilk:;_o9a,TC3ocESo(4q:4f;0@-gCY>oiu''eehPFGor_Ot#bmV8ep-Pjl%
%\B<8*6Le8f.1Sls]:.=*Y6bTur=_78TcB.j^,Jf9H2T.<rWeN]G/N0sr$T<$q)nZ2*KC*nanU5\s.>7"3;PTp0"]Rt4p:CehaF7&
%<N,DZT]kej5'Kr!nL#!g^MQ"r`%_1giHEoON9#s'Cu<q!UBCE8S4C35ppe'9qYKU+_gh^S0g>7@o!;UWJle>qPN2calpQ?VEq018
%_gB/7@/6/0k':_PIYXZhL,h@3b&hD/EPM8,1#Lii3MbKUi<(Cd]mg\[N;)c=gN`;Or8t''Z^WNZbEI?!58*bNZZmafU*JY"p\'^5
%GKBD<pjWhRh[c:>/cY8LJ-Yi9/0Ff)#QLu%mbn+.pj_0_X[=CnKE(](s8<2!8+HWbpW5lro&0,q;p3a`)hJP`hh08Li:HnTq/_2\
%H2coln(sYL]"T(9j40GOG4-6Z3c*]F?!LWaoDU1FSH&DcS'g05.04C*R,QjClqDi[ar*D!LkL5Xp!0@f%BTK`RkKQ2]6.;dS*rB+
%g\7%8YP0i!PW$kO?m.U6aS*bC[cun"#%k/._:XC.lsM/%R)tqtR8!7;:]C:Sm-O#$+XN_.1*5HH5@8p2fCS<dZ^XbISA"p_qT[.4
%UOMT_rG3:33ub8hB,BTXD6u5RD0P^![LI:=q4I9%G;kSJ*5(r&U#h<Z1qu(a6c:\c908,-q3<?Ha""J[a^`)^ip#4$cVB6ho&^%5
%7e_D<4(34-"FpBZYD5[C5nfbQg"kEYQl>iQCgmY@d9`<'E"@,\qlt'5ojE#)(g[],Q\48YK2:IBn8^4q*"4M#^p?[G(,<J#'o)*/
%#K+_+CS1pMbD-p@Fse=oT/`IV0d(nEb0uQ_+?d2ug(2VFpKg(6G%_P9@;u!D%,koQ9l'h9fQXQV/O0dB%Q:S'*tDZlq\4jps0(F'
%X5`YcOX;^+DuO]rrhp@nn2Tk+_m8`lirP#$n2Ti,c.`%"E]`/A]T'P[UB:SHo0Qj]J:0SR0-/E+G'-K.0K;LnIWb5XqO9j7Rcg!%
%^B&KBOHmR>,/KI=BAZN6o(?KToN&'Ahfa4N[`i/I[8h+Ohq)B,\_$M@?bS\8U"f)!m.Ei6r7(04ms0pX6lk4Sg"khj%.)`oHplT&
%e(Kt<L3Cqjp.n'QSmc/R17K(Ds5a.-fHOhm:rM'GM(QU4%V8ofh1eLKFg`mic5Q+e3bccZ^,^\V.>UpW*UFViTF$j8*TIIj1f)tg
%d@Pn0A'5i%G)X,bFJ%9X5M.\#G)Uk(R=cO?@)k7Z1%rhOEHAE79^0+bR8:T\d$MGb,.`)Eq>8X1PJ;"Xc!;:WC/UrKChulO>1^IC
%!6=]MakO/Z]K,k=n'8(2\#9FRg@&co=4a)$SDTi#B$7_r)k_A(3ks<h#]I--e`=GlF##Ab`UBtU\)a=*n(p,^l[Eb*eoPL$cPU>/
%'<Cs"c1U06Bn`&DDC-=&RG;%c\T$04S&Ot%IbO?n4al+`"74L3)g$UYqDa["?mTYdTeA;3*ZrGdnKG,&+3X0FVg!'nG]tmpmo)+O
%4s0E\9iCYu)\P=]kfGGO[3(^qFYK&os.oR#%&K04If<Gmo2?"mg(Kg[k942:VQfLi?g`1CHu5=t%jU"J6bLI'1WA5[*r)gqna9eE
%J!/CXS+l/0s*u\MQh'B,Qb+Uim8B;*`pXDkgd.DkBQHsMS(f%e*439TR)2.YgV<"N0XSA"-j0clDV*UtL.s]WF8MlZIGE2LK0"3Q
%k76e46(L*9F3dsZqlf>Of7/6jZK5]b]e9&r56Gb?gEcJuT?GPRTA)2n+,2uRgo<)F:jih(%(k+g2^Zm9OYUlO*`q?OO*SO+G_3F<
%4.P*t@:%h%5fR[MAf!hjO\4s>qSgFql!HV7'%6I)-`?,dhu;pg;s;u*D_JP$d^7i5cJ?Klnk\BhD4JpN\OO?-KD-X0dGK]mZhHr7
%XljKW+4Fk(GP,*3#B(Iip!rZnD9oq&T%;M@Fa_7%]XMP\GCQlu"8C&ARr\3UZnV95mp_4@"Rl=t=7b0O@I)CkP>,NAXk(+QeV72,
%\*r1$H#e5>F]u8D*u*@WBH!B+m66q^BmU$BB+B#]2F'X#?&\fkd6,(Kh&2-0)q`9Ss.!H1gZ6_cQFiRr5<V*DjS;p;e$%&"7QB8e
%*rGcR*!e?O@t!('X#,T`YTG"5h>bUB0(T;HK[KeqDdQjYSUJifk>)QhlTqoT!K)%B<%[rVJR.^`;`tCT:I?Nka@+q_5X&1sn>(c4
%k8'`g5tlUnT:BP,_;C/0oO.$7BPTLgjF>j'rVs1@gV6%nqo`JdDU$#dD@TnEiA(,'>B>Vt4bq);.rNTTm;<Egm@O4T.R(=Dh30e\
%2d]j^D(<0eUu'.Mo:%t7S3LbT3BW=Kr35Pbj1'dln,).TAKMDCj.=B_:Z-fZc?Be;j8SMp&,'jfMAG8?Z,(i'H9Mg;Hoh/p;+L.%
%:W2iS_>Pp#]tG49jXJ3B*Dg?NkWSt[nMh#o7,J+84*(1iL]0'OqtH'Lgi=I`n0p'2]mmWmD>a7tG6@q074*M01:l4jYOe!g"K1kh
%p:DZ[he_:t$%1&6ioB'3`9m+*4aE!F(XE+-j2D3/I.'of5+\$R-e%ni5:I[tQacu$K1u2+g[bPq,p+7VmemT@FocdI8tZjlViQR+
%r@Ap7:LJ,7rpKP5e^:"b4M>X^L2=`)G)Ms)5lI3Z+?F;1I<kPK2s5*COtl,K3ebK!#2dYLaVPb+s1-\ZGcUM?G+/a,]M)SFH$=E-
%a!\tu?Ig?FF?!l.kEu5&dl_7A^s&Cu.Jr4%K0rb[Tfi3:]fEWLqrO,9$@@$[5<.V0n#[WQ-E$7Ef_I'-+*-Xi.nLgV*tR`KLV3TF
%io6Wir4V:d&7YHT67b@L#X5HudSG>^n(rKll`8W<%#J\d8>#qMqW\Td(P\l6U%D]`rV?A+rKeW+k]^C/+9';V^V0R)qLY5DfAHMm
%a6r.-5Q6d,qWs[ns6/;+s6G\o:]KN_p/G5aj,]L1$D\UM>0)OkhY$"DrK4arjlj#aq@*L\55UZFfDcb=rV)t5F-M]6Hh;geY<.%n
%55X"TJ,H&X[3bjYqA,-\F89Cg]cCC1Sp_#8O"X/0nVE"[ASAUcdD&AW$SdWlH%/e?D_GBjiTQMQaJ4Mo_P"C;dZ8A]XhIIh;VNt\
%]%2n;@;D\POXB5fd4=H(]IZ6F#:7(M@g2(+rFLhNB[sf?WV3+ePi7Yj>"dARN8=V[SDq^1F!G.=R9aZCSApM@hHs;bFV\jgT/s,.
%I;SZ@YL5>C9Z9rND>_p&efb7TA83ut6H%M`L-W+0\rWiT/7NtZS<$(a05J\X[_3)+iBFuPCE'=^CG0`[?C,ZY?/ba%UYB,J;ne<8
%ilqeGb7*h+j;>\!j61nTR*3`kpA'ouJ,4c*a,MBI0#K>lq2<_NGF*fl^6s`4T6sDci:CrM?@"j0^!Z([4]ThKYdTY6+(O[Xs3^$I
%18U$F8WA/f\TC$'2"WN2SrsLFVst:<hW!1rOK4>SqlJ8*0kR01Qap[uV,-+S%GFp]1;53\6sJ(pc^sj(B0BD>BBm9n[1BS:3Qofe
%/pKFf"imaY*9SINI>kEQ*=L\_s-7<:I[m-=A?j<;Obmt-Vn89NNlsD]RH\4+-.K*<`FTis*jtl8GO)N9IIIY-G$Z4L=q;[j7uqHT
%Nm;u)@pD.IYr@*d4R'3W\M.M<e$s3^m,KS9NpkHfo&V(,O4LgaV1k!s.kN5*%UPnT)#T&)43ogFFZ^:GCk^P>+C8-0*^!)nht)[H
%7qOFi:CMBI.\EBMFp:n7`,f3(du3igU;(ZcM+`Rq8(@F&YmbNH/<tB0d2`'l2E`rP[*n0bNSEM\<8(`6ZiVBZ%Q;m/:[#KUHONYr
%s-Q#q+We6Y&)FStDra89roj&<\<2j=HK]uKoA!Ss)90ha&5'**Dc<\3keZBZIWt`TIf,sio[BEQIJ.%=%"BpHDnJjt\g"a@hNNaO
%0kTW2o8u!lMS+^S[6gn!c(k/XoY84u>4c.cFc8_t^PnZ=QuS*9)[E,t`qu1;//2s$G$`1&X^)EA*Nq!@'%VLg*:Igb$Q`[OgoDDs
%+.Us&4bk/MDk,6D`:7fE+<7D?',qT[/FeYXj]qVQUGCamcS?0o(sS1jX66E5;]6$cMFmRUP[90nl30abcUISgB5rEX^ZY@[mh!GZ
%SCf,?o)l&_d!@j`\0TRWN[H(YY:b)k0<*&&Xa6%_YSDN-H9GugA9$p;a'QG5qcN[F<^1c8YeW'gGOPl[O:mIWM&$-(1VVuU[pVKO
%7)tcQ">JGT(9q^5m<X0'OIl`4,PjB229N`Aj&Ujd*P4FONh1B&MjZUDj%2uV%NXT(6=/p50#lgq4H%TW8khc;<mh^,[dg)W<cf7u
%L?0Qeh:U<rI4IM(qM`$&+?Uq&+;-m`%Tsm:U`?\-PDLB@\9AU4MTVp<M6+Qo#BpP*4$Ia[+$7p^T)Jtub.]Ysg)A"P9k\Psj;QOQ
%GGu@O&)!Fb80U#bre!1RVTX$Y`u/uQ>/p*C'Hs^i]%d?2O-`5gL;m$V+BIU)7'&R&YpR'd6[+>h4&2p_*C?kcaq/l9(`04b9[%V#
%[0P^NOW2p"\B&pd\4DY5olr]$bo"XNI,\^n@qK5^YHU-LSl;MIG?YuHbMd4P3FLY2TZ$3'^&iaS#8k?<.Y/,"*s7P7AtoSIBu"%V
%KSW!hM_&1ZdZqh]F:<e1%/!BZ6'Su"nV2DI.'Jim%l5r)\I=<oYG_Lt&E/h=+_6_,r=EY'AA*Ai.Qj%U=#"p06fYS8U;IFLlZ-9c
%&6qXh)T5Co_45G"$"9t\"ub>b-kBACfNt2OgVFpDnJtc"gtH:P8OVsN&)QP#NoMl/7bQY's&aq9#Prc_56&pk-F<WiLQ3]??^ojH
%63Q"4@:mG:4gQ+P#$''-[0gn)]m&CRKD?"[UDGf3LF"3HGD<%3PDHSfAqo)+`3EQZ&)^+*QR&RG?kC?tP6M78iXl!ukY$unoEX'd
%K+-h7c$=+Q+5HHX&'.,9l+qH8kTLsRcEOD2,!:91*q*mr(V0nH+5LPTUE(r,jnA;;%2DO4E$$#YbBOEeiV1=;6e_3;IQW_`U3(/j
%Jo\=_`nn)We3&Nu;oU*+En/4Jg155VpWL`E`6p1<kNK<64PI'E)F7USLFF+@-8NY&V64I7%p`-p;io0dM#T\dn'\OFEhnKX%Hp%K
%`dk_#7c#fF8o3.3i&38+agShp3(7S::`!hB*5_=9S^/VQ]N+W)O#UnJYTSLd@eBKe4XVnFEI$Q#j.A/2Ve;RgL;]QYSKCMuHpVZ6
%hRAbf/"2pQcGWL!)#5PiM4Z<h%j?Eilb@^0I-ECHs)1=Oj(tdY;b;n&)k&8La0("Qmu9!U0V;]BJ*FrYr6!:OO-bUioH@Z[^!3G#
%k4O4W"n=jXLNA*=Zd60A4"8c$2Yf(r_i;Wh(nmPOACVc1GPpS0M-r!&cS*,0FH93Z5JAf@$9>5u=@]]:j`PM#HSgf'4%I#6Ht>ZX
%&&fT/[hm6Qa=A&+WnF.P),'Y^(Hm.&mc6OT`KrH'K_?^sp*I's#+0F=Mq#@F(Y;gVi3r(/c4W3``"'d<nArLh)ET:I:;.L'/4DQ+
%<2#>f7i'd?QTRGdh[0eTeNYta$ETg=VW))#(+;Z.9P;\3'I+kl`@Do3cRjf4!k%<XIMX6!)eSs*Q$-u6_0J/\*p4Z3Z5;5[\F:'C
%G$"oV*e-TU)?P#oE#SN=/HR4?Ds4qAA*!!\4;jQ[-'7/Se[VnlX9Jgclh\Q6BQ2F64aq4+ZiL^.A&G-Oc`A-6Nje<e[^>2;]&3)4
%ml`C8KK0n'i/Jq_G@'7\*:'rskK(u=`p*#!Kj;^03TF/@gVQfgM]4?L76At"*Vh.*LTWWRCA[d04eFq8Wh3V6"i7IX5/+0S5Z:A7
%\_+1#<XCkIYlZ&7\pH.L+AAhbCg7rgRrUMRZ:(ARB"-G+J_dXccf]D`n!Wb:UC-TEmJ<\$IWLR"nNaa,EkZY<hEI*@*o8QBoYc01
%(XFZsp^Cmf\.7necORm#7)8XT2?"c;DlW^o/r)+9,V,/h2TO560aF\4EKWWh^3pO)H"jp:]tA>"rL(k@>SCh9_+?hNaum-mB)_-]
%Ql=L90s,H@"p?%6`(s2#LS*ceA+Ru"aj9NI_#L@@U>lutfUkMSrUXAS[.:-fs%ss:`Uu[L;"C^JrL$=JIm),Drl28d%K-8(`2E=D
%ANtV*T\[-3)p[6.DVpe.`:FJL[#at1^o[9k\'l-U]JmO?2uUb*I*A4-5F%f#/<mIUNjZq+FPc80pR2$XO/(r/I[SkIMm%XkcPF)j
%HffnRaDoRb2@2\M\QHqOareNb3g8S(a3G0a,[;!][LPYCrt*##VW]cg*Ea[E+1m9jnd+hNmdVC:bH"VZ9Fc:M,B2C==h=]t:Hmg:
%SR-#uF4DjjcVYK7$p@_<M?KK:3,`U"0h*3;_EWbZ4SsGRo'd);SsKu_`HcLUX]r<2(U%<a?-[(7QN$mT2uABFKGt`9l7BjI?+m2+
%\s3bNi`6\8hfhTmXOmN'$B-%GEL$@NOE6QQNu`N<j?J<<mtFD#oB[EQl;g+=GY:OYr%XcoSO"+F5VQ)HmC&1HmbDl6k;-p:N)6']
%]oRh?D1+J90dpX8<$(aU>J@Y9,l*5j]"#;^'\#W[O^V&Z4*!M(TfFU37M!\uBVk<A(V0^X>AhH]g\nE#0Al<Q.fH`D7s>=CRJMd#
%Wd#bI,3.s`NGsP<fFVqR*?/E,o5cGGokXc,&pGmOc8k$0^r@+F+?%(K*R2uA,O?9@)!!]DfEfVQ+r]&I81r=Js7#9jcNJ\BI+\dd
%*#i=^aF31jpAN0(3"NLb0Lp1jm.gqTK6[^AS[1Q6#DakTh.%PnLan,]pppuBf&QRbcPPiDh$6.baoJ#l9Xq'\k(f%b`-WdTO_Y5$
%&&0Oq^_958Au\KT9Y7015P(E_%*?<[h=SN\_i?/5@5-TlR6,]DhXV$cTII@WXtKfaNu7aK!*TZ%FS$#uTQu3#[-$F'E-(ZV*@2le
%b@DgnbrPQWM"Z;s=W]ue2mX^KGe/[s*!L+1CqVU:@uj6L(?QtSISToPT(0m_HB!VPM+2DG*3.Sr2#ugLOc8'1"NKFM0i+M&O;"4)
%rSlVH]aSg3f:-RQcgNlA^?b6ml*]0k9_6QGcLcu32d!dBHC^?V2!LDkQLX[fIE_G6-`/k)*12R!(Lm`Nnbtk%7Wk)5m<_0'$$]QX
%C];tsHh331Fdh2MqL:+.qO_G`'*)*^VsUeY+Zdb0eVNcV=9DZFcM^3,a+.p+6Lu5gBj<BE;A(a2[DT^EZE?/GGVj8&31@<AeT1[E
%Ne)g"mH/*->7c4W5iZsHobc[Un*=>-+5_(>S,./<hqKN.r!K(B`(t:#+2?c>7/$0X_>/&VO0^hk+.hQo7+@SdDX'Gm=",Y#Yn0A6
%7)1/Ps7*"9pNfSeibZ[04AJeEBP(`7dgj'EKYeR8(L<61%NGp*@`m9Dq0^tip=3L\acHAsm@WtepVEbN_%d2n(*WVShk4*Do/[#@
%++1&P+K*P$$/iM`h21NHEG-mQB:lU<ocK/uM9XHdd6B;2+AK`Poj:ignFG3-rti9."FCJ,U]26H236"3=s<d0j1iV"I5Jl4T5jku
%(Onf3`W,nrmneT+0E9AUU/p>Vhu)m1J,-/&r;*mP!$U[?F0.]q5bfW!)mHb@*TS"Hf3eICq/Z[-c]+"cs"=;3Nq>&:*?*&@6i>pG
%2qRl3?Z=]Q2ugq5i*-'D?i3^[ca+Y+qtn/(CbtuJ'VbL_UKciYGh>Ps(8.7-/<,2u34c:=fX&Up._S(?/H13?s22U#CCb%D_/K2e
%G2YVSIKmf19d/eL7j,BGJ6)u-p"7h+]Us5!9UI'$Hinmj`K+EO3WFR+ZdFj/Dn`R]agFb(5Q.)/s5mC4otrG4qeI;Ko:Q$Yf>%7B
%j8TmbC*.5tF$Cf0^?*m]+MC"X].i><N+:!2#f`p33of%W4(7rqMcc-]/ikt6B8_9>L%0YIO.%c%kd2NudZUdM]M+nG?=r?j]_ka?
%)_^(Njc">t^A;F^f2.R"n%URUTDt[HD[F3?'N@4&X4I3Sq$LN4a+a]k,/7s.]QS,60qM\%OZ:h4ef:1PoC7<bQ!t*&'"FD7"9.f&
%bkgC3ioPO"r6],[gT9-g8#H"W0+@k:HkZ"gJ4ZAW4=3Aa3q6MLDL.WR"O);e4H.$_IF$(XlV?W6O-m1R599]8fQq>.aQWOKrcLuY
%eU.+j##k^;qOcM8dE&SGR^V9[NRoc57ZUR*;$Q^hpclgMb<X?\,0r^2q?Y?dQeB80"0_O&o9$6=iuV\6'_WA?A)DcEr1DrC)WSAQ
%a``V6"=$.dQVkT[H'raOo?5;Zgng?m/+Bjfde5:\cMm\oN;r)?W.`]O'*=)"bmK5]fCla!q_8LuGLM1+T;M[XPX+#7'-Nj9XfP0T
%brF$:bhc:/+-4d@5#EMf)!>,UY+\KP+9'<p4T@5C&(aS<JQ[`KI^njp;oBmWI7LqsVNZdZn+U&\E<Q_iJ$_19=<*ros"f$:XeIY#
%0s)=5BtR-7cDUW)pWL`=!i,P]YI9_e=i[u9XaMCC7fW(0dKEHfZhS%'^,*ab6_Rc-<n=BP+/;X*A9m!?22nB^_HqV"pn)'bX`IZ\
%PJ16O8e(SOU\c+5^ns4ITB=snf+,X]66sLm3q(r&D#Bo>mpS@i/$3b:nLf_74a<<B0.*9O($D@9f?M>k,j_d=4qbVjr\JB(Pn`*+
%21mq5/Q4m_hHA4T=R7!MKG)>Ag=ikZlWUK=XKb;:HYhL#59Ko<=Jf?Kc"hZ69de!O.XlNM[^j`IeVQ]VX7r.LZbt&lJ`gf;I6d/3
%:IkG1p)T&#'K&=XmR+Vo,NsLkqAR]=5bd.k,J@d"FgQr5.q!5/s2+8TmVb_)b3>1d;IQZj+*2Tu3Q`<3it/X?nPoQP#TqE9)b3\*
%((ku-Z=8q[]q1j3(tft?A-heqYW+ihrPp7Xr,dihPQ]lT>dG7nPAA.<O1S2C1TPtRc=]B8!D$Z(GS#QYhg])4TAcQj4sWI\0>i=k
%98iuK)8+0gBg478`^e@)s2jl*-n+,pf)K:YT4b!&<1lrD_Ub&fOi+OkmEK-2W>qAQci.aeG-3b/"Od,pd.>Z[5Jp!bY'(t8X_i7Q
%qbJe&?7+LqKPcQd`TR%5jfq9kZ`>E;+90[e;&f;!N'rHHG9!UR/e.JT52Z8!L`A@H5*%pt/0fkB9if,4qeYnsiJSQ>CG3N<Qh*'1
%Vm/O)8Of"12V%VN)kWSmT+CrXHkOSg\Jj112oL8&b/VX;lH4GqY<.[kUNldk@CsR[`oCtGs"f&O1%9KZ:.@;r*+q#9jm]5&h0@S6
%)k%umIl8.%T!Z<XDY<hhJ*nu4T1$,U=nrk@r6?sLWc8Ko;TG\A1."+6o"o8>gHB0IOUjmDj/p6hC&cBD6MG1@j4N(aA^=+_i&+KC
%na[(.YiRP<L[tgBM]Ze9f;HruUFa]W;0BhK^NfdVNW4nY)?4i6?H9gkPs=T3rBe$WD0tbB&e\OYFMA^]q55cV-KE9%R_I2N;C*mX
%or":.)Tu8[Y`4kHan+i2oX@s>JKMpKs!bkci^\`QB("^Ls,kk0M8H?$E"]nKb'ier^:h[-mtrd#B(%suRQu_bfkX_q/bbSdH[I%h
%Ak%RqLuD.%h`H\Fm<Llro=o>@?i8RGY7pO`r'.KS5D==R`Pa3-6I+-N9h>F(dr.kHhpS@G6GDFmp`&K?Ld*o7p2>$NX'bkb&&8,B
%Dth$DiA^4TrU8VCnqZ-#p2BI.s80#"_*<A*qg&$frVulShu3Nbs#q8.s6'F75C_h!J,Qjkq(I6m4TPX<:S-25`UA9M_oK[n(?EgA
%k3mfk.l0it',/4^gCM%\DuS"rqnqlPpRfc&s#<g5hu8IGJI,A_c<SNsl9B[@>VG3\YriXr1]q0J.?*#,[PoJU7op!3_.3\l'CmtF
%U`t,L]Y;X["\.[&TiflW9*<hg,HMo!%`9IqIT.NlB:nV0`.3jLP-p]f!$VRNYW$'=J6'!L+3mF0al=>(#8$)]#^P//cQ+5U;[<oF
%R!NK=@"T=fiHB\i@473`I6+'R0'hpEf\_5"U3;NGhq([Z*@IpR\K9]4cBH5QE!Wq,KofZ-+dY5q]%%%qCH1@U=luKSgBT&ufOR!D
%*s`lB*_Mm**H0<65klPq)?cINi7Y-G2C0h;4(tM"$;_:H\DI;Y>R>SJU-duPhE8cDp9FH4d81j:'&ZMp]^4u9fB%/%PuS$Z<f*:>
%?E4LgH(9*kSj7Puhq\mf3O_aPI+KSCUG0bb<4&bQ9V!o=Yu-LuY:(`74$qPpC'7M`Bq#WS056(pH!BnU22`Ik_.9dX"2/uHik4#K
%5t3X"ajhK<-:p/Ng#Qr6,?3BFAB+jk_%Y?L2DO_Q%EknGQc%/H.Z&3R6?'>7QI^Lf#cMX<6!<sdg")<WR'f5A;f#GhB">SiCrU2E
%2YClbTS:6@a\R]j;)I)ooE4'#2\Q4mquee7H0:IOn*PfoZp(K<Z8aQH#VGN./m=8+6`?a$bo=5FVOk%I>7<2$^UAd:C%iI[FUnmg
%VFeJNJO;YWaUFr4eW>2)Ol@?h("UhUEF,S`Nt%<Z;)*2[ICV<s?'EY%%OdDV;#sNFG9Z@^a/PRQ^rj`:C!n@?GC4WC@G#o>Rhg^:
%.!R0WH+s%\6Nofi%A_.XhR=MbkcM&i,N\*kq<WgA235cRL1IDd"^E'ZcdfY<Ati8p+B*c[gMnV"r)a_J!+G\,oEJr\D66/oAQ810
%,PIBQ[dotXZTM*TZkMp/<%DOVBeuCOX*.dP@P.3P:$>[W=O5MRI]f+_i@7o2bKbE8esCW7*m4QG]W'L/$/'BGCU;C.["!0C@G>4b
%.:9-pc^Bcp6O.<e;HUS##uQ+?=QH=GP=2!'4fNBJ!"D&dC-'Pt]f8F2G^sjjAA+Eha:BF9P>U]lG6p?bM2/KWELH[-G&k&4@Eei2
%j%"#7/<@FgGji1r!gYO(+q$_8>YrN%q*(*XSU&aIXJCaYqk`N>YffX4:Dq*>*2*b?]bjH_C?fF*H[/T=*0r+Vh=E`&bPXQpg<tk6
%^%]hEV]9&5"'02"B7AM[</*egl]P)UZ[/,.WsO[on.EBPOs8U&mV;=*H2BPk[_h'\o=%S![5C9#X$2Dfo^8=rh5j@Z?,_.DV];.T
%U?#D);!q@s)*k<qoUb($3lhm@<@@WQ$sU=9`Oss49i=2S[?O?m2KVb3?f+^I2&C`X9DZX#N81Z#!$H/Vrk\BVmrq0i\i&>\/QT>-
%?-6c$n-Dr4"I/)T2<F!9G$=aQZF35QiQ)-H?hPrXUehH$$f'[BK\8_]5L':g45V(!mp4erp`>LC^h0Oe9tfgpO?7GsTX/g!bNJ4+
%?>4&&>1`?QhTs]p-/P!d%!)8CQYa>hR12U/VsX%g4][^1AeeGoG?F!iC&iC&fRuSB)4!S'>@EFpPk$FF@qtG>/KA&.!&i;t*d32-
%d3BbTcQdGd@?;G+K6ao,XH-b+TBnT/E#mn6D2Kn_k_8!=IEE[c3B*es;$Pd1c+,02%,7N5;iQB.Js`d5_\@^YV$d<(G=bG1JOS>M
%2IiWXN6o!k$)V'%";Y1-NTd*M]G=),Nt=hI)R]>CU#"joAs_I7MULtg#/A"M`(nnFAT3?9YlYM.&2^H+H^q''%=m)Q=PGfAS?BO.
%8Fp^C]N.MsBX/fdC)s4spf6P8oN>$[A4Ab[Bs2_X7#hOC@\n$>GTqs'2rtPI)j^'B4X#narXB62P3#-V&9+-8ojrq`L%,#rSh>@N
%VVM_(CIK8g;^4`WXi:X9G8Is*T%hG@p(PWOQiRElD^7<'ZXj)>9ldnF&uUkM*OhY1!1R6HY(khl.kqfQiOX<*b%BbcfLt:KAj<8d
%L7,-+KKi86-9Ci%9S0,6ENS"!,YW/XE.AHPjTGV-RO6p1Q"rBGI^09&hRKH(Es-\kd"143hBIA=B,4]q73+!J1&u%r'o-F7kn[kE
%K3,2>H]<P4Nhg`Z`f5J_BteJH\rQRaO555L(O\"Fo.T"[L%95&s+K[@s%6u&T0DtHTai?!aB^X!eW396q9%`LZ:LHs7UE`HNWN%>
%Sd_RAo/UeBaOL8;,'@hk$%kL?jLGfHWPc,,)7\P3rK!;f\2L]fZ8*W&_Rfkc?Z[d%77Ca/>Z0fq;X3[4\>kKKW1Ur3ThA?h:.3ML
%'dZsJ-b&eC6\mW5m?3eB6B[C&7`Jo.NK%311+D\^c$:#d(''o/5U+2m6k9Ki3Ff'W?R'[5EN#2hk1)[m:1_<Q`f":f?(i-BNXsOs
%BLjE6\iFq6Q\K0B/LGQuojsQcCINMb\/M+s1/#Y(2P)co;T0CP/*Dje$V:>FKKSl]!7H:#88qLbdX]3Q9,C8SZ`F`')o1El'D[S@
%Ge\h:@CGqch>=%#Q759d[jlmmkeK='o*qi75k20QBG:nk=>U__G$_bI7b0O?+`pRq0u?a$mDtDL,JEZY]LCP!K\)&!,Y.!fL[(fQ
%A6T/(qB96^N=-LPBd;RB>G3W3&.2%B9(OPD&EN"4>4E(kn\6DC+:;C`3bVrG^[7!e_A8c_hj1+;iUSX:8JIKZ*3+J_A)VZ$!An8F
%>b+!q?NSUf0,0ODN93Z%K9Xj`YWH>NbOmMWm^Qe!Gi*Q;]CA6POJq]Q@<^YKQpg+<>+(ra5)U,M`!A$HF^kV&pe&(5BenR+AG#<2
%&SUHU?W]DleK'iinu<N0-ZEe8.>6CqmfY='2+,8[U[NbN;^MhLCK;Dc_A^f;n1p\eb2&%8`lf&$*%pH'pO3pS"%$6d4D*f.d,Vt^
%BR@#JH_YJ.C-SIEDf]q1$<EQG7^231ST&;l+_Qs7)c'3KInotRBZTniIPEUZ*+9Hqa<U%b9S4k0Qfa'K_WM-,GOO([P26@rBpIj\
%&+9"Akb%hqXVn'i?tuglj%rB[,AZ2cMcScD+?:9PIL%r*4HCKS#((^TU/+E@ec=YIbsL7un&$PgNA8?r+3V6l.W3I=PO*0>Z(/$T
%jC3Ih)ES9\CbILXNkQ?)0P[ksP\K)O[6F%E"u"*ie_<*N)T&Pa'o/&j`rKb=BgY0m;t4+ejB<3jqC;l3X\N(bH7(\o5>40&P-Mm<
%W6"Nk@bJlu7No=bmOf_%0fN&Q%+%=cbiI`bf<;Dn7oNo_rp98?GQL]-TB_;.]su7irMtW6i]cP,OL"oSe(&_M;5ug$C^6Od>sC>L
%0BTf-ZHd6'l(7Z&PQu?QZpEN6)C4jkpE9pRo=nM?lt`FB7]0'#[):Cmk]#4&=Ycd!XjuLA<YQ5(2\8j<[O*I"L-G#?$ELUhk&b/(
%Hl0J`G_Dod->1.ppsdd)>J69OVM0$eCm6:?(Ziui#)r'>pB=b-:H#OT^6+.,+Rb/9,P_$>&esq<TeiZ$::_n"CQ6]\`8GSb]INsJ
%\3,p7Bm[R"2$HV)3l?;9;Xnucn!arZ.YXe5g/gW,O^a`l%'93%dZ%Omd!'K.8#:CGf9R&in#5q#e"p928<`/)Np3%6q.I"X6<\cY
%L*F8aDKbIlloaLuB%$B/^ebdJqf_&tnD`]MXgiHRihptP@Q/$pD38VLFmFpt@*bq;"R/nW&ZOadisaM.(MkZ5+7r%--LG8d<,0:A
%/Ed5$Z@Q#@mgY.>??_q-fYDl?U_EA-"3=B>gkcu%IMLl:j*np=&a:6=oCS'O2S!^9<pN#u/f-&[H#(nYf]=)]TeN)jkNo^K[[Oui
%el\"Bhgg--kXEu?_M9@EV#\68.YJ84'j4JnKNF558R+)(2#&UHif`eBHlgWYUs]d@M!>FN3K;7ZKSNN!O(:pgiL[j@8N.D9Em4p]
%1Il)?Y`e$h-7h6L1ZZW)(LEPi%NJYX#=mOD]8+'-VjUli]IlG;]BfK>V<'a$M'#'3DAS)"fMG8YFa_5l(hfZuP#!L:GTVFM<2tZh
%DG954:g%,\XR^9jTO56&]jU,"WbP@6iFXeG5HF/gpBK)XJBEJ]Gt$@\I)"kB*KR82q96K+h")V<ZDC3R`]A.>cTdsfR7VKYfS3>$
%AaUlY(B!N#YtaF.]&\'8-:Z]%k.$-mhIX^a_hE2"RN0r+`*G<I4g#tk3f[+e-*EPd2$-fD7Wanf+Ns.H">!Y`!,#$^TMPou,o'4o
%NHNb^qY`h:_GPPsa$=%34.M[c^J7ap9KcK"_jA4&WF;^mTmC$g%f_"Q"Q\'5rCW8&k,9@Y,a2AuK?=br5f3\p:pa(;$IJ8==]h+2
%=)eK7Y;*)6`DcaK\UA*`T;:UPn><i9oP;&@bfGoj,d8"t7HB&M:F8^<[SbbjG@Yi(SGM\QfTo(Z'S.qB$jU@0S1@E(eTQ-]p??1`
%O,ZUtfjO5.C6*)S%\oZP(LW*&Y'Cc#:&APjWp$R5_$Q<U+-nhm:s*lTB!^<dI"1LBRfk@Sf`TrThAiWfJY6a\,JqU6aiJ*0i\LSX
%/.&R%>8^q`!<T,gkUpr%nBmOd.g-U^P0cu59<>IG?.;KfIZP_:NN"*B&,Vfj:1*m)#D\"%-Y$Q(66r2/+eBLhWei?9>EKYlB]%K%
%oJq$NAg'O7:h`k!9C%P9*!rXu_=sekT*U!XfA]Tr[>eGaO>XRHB=;dFE"*r6Q'2Z)bdo\MBNb/9)qln>iM?<4g0%R_OGsgr2_5Il
%3Gq)%OBE=eks8*tdubAP.kHCDSePd%/.<VuZTjSb!urWS(j<cjdgZ6@Cktk'WAEF_"JstsB>'JP?0pGpoRh4MMCf:6pOFE#3HeYu
%(K:E#$O8)rUtSssY>MX1:%9?3EHp(h7IL4m"K#J*I[>9_8$ZOhm'aZcA&l,GJ]N6;LtUj!(YMCV@XIm[AXqO[%R#F%i/+[XLXN+g
%]iU\(.Hf6<IXmLt/ErDr+@BmMLmUZQ92S<;r4SD=Vn/NCBc?RPia+VEeMe8c8WrY_\<2L_-T:S$dYUF#-YSo=!WX%&2uO,3Xb\_U
%FNQ<]+kX3=;?3_)?LFjkE/#'W8^DBG/o+:=-X\E9-u0(T;!N(,'Zb\[+InOi!S7uC=:3GJkUSj-BB1)LC%RqNY7e%cQ_QT1ML?e+
%bfNJ);d+64,_1uh'U-%k.Mk]7<l$qXiM>T!R6llY4.]"sH?<9D:8.f-r$9M4jAsc7iK4rWc4#tTL'9\t2j,mDBG7,]VhEd^98n1@
%KUR/s-/[VnXr/0#aZ7+fMIWWlEd?>qEZ3:f:iR"`71TBohO:#>LVXZ!#Nspe93R37J^s%S>c1!B;kV2)87'@5H6?8q^^@MEo!YMi
%[IJsAQg:]-7gJ=^I:6$p^^0Xar0jfEj=KD6,Tr;H.?8?T3/G+Z:P*Z1CuT>C4@I&B\j<Z:Mi`di7*$t;V1<s#*qH(6IL,`dFMAFj
%r>]@G^b2G;2?H5B3/P>39I^>D&6&_eKU\h?,:Eqk<\R&(\#.S=kk;T^gFM>C&(>C!cs\AhDGA7jTS*djZ(9,LI*H\e0"lncILsNb
%7VWs<Q2Rj1^]$A^^A?b=^t+r__JmY`(C8,qac7*Z2i7'CMGnE-/Xen<)LHQ6kd\e,p@^]t6Ypn2$7T;>iu7d68.TgFBM,D1A513&
%OX%``9s$mQabWi5s.g1;%-ol'bJVmIEuG;,=!]l\8$t):A/)XgkZXSN0d<3qT"W7dKnRYiW6C2+`C9Q>3D-qq*s8/CR%PGEe:c/n
%;Dfb(g5,KIC^sl">Yt1S=cSR':sM&d"E+(9fDFUbYf0aq%=##b\TJ1hpCcVGWibdtjE]1jUm2rj5?jntk_c5P?V71t-mL2ce%Ue%
%:ge56qJpYr[R[LSq]P-*a<p+"5&:PuP[<EMAIL>:b;/;/5#O:"9YibVB<keHn)X(Hq:Q!2P&[ZU37cbO%giFT!;dD^Jft!r
%L&F?W?u@N:q7$G^lk2eW33#'@^kcEn=g)dQ?Kg(rPrJbpMuN]fTrI`iR-5%$+E('t/#i`N'"cNs(?B=:j><k\D@jekZ1U&"";:gO
%lt[Es>A@s-+jHK=qVH7JJP>[?jGYDg5(4E/HOdO31*Ah>Li*W0,oB).%os)j%u/M2/a-6?3jr>9inD8RW&6-nF<MgW<slo4Si)_D
%`9p-N)Kc+8=eFcsQD?*G_g[BT64A.?cp5!+SfCr^n/tW)E7W[n1TB7]*m"D=@a,3knuVC_N6%f'R4(_f3VNo']A@=Gl25aIQ^#"4
%^W,KPoIXS:ki.up`V`#1178-/NS]?_3&?-t<^f$R7F-E3o$)>.(NJ'VmG%R__aWG'FWXa$K]\i@X\CoSo@rqGrd\d"9<P2q1VgCQ
%<H*UiET)-6OG-pd'P^k`H)hUu7ttY$3PY7K(b6`t>sN0<m+)t#4kJ[NNrf^k6kul].MmG%/7RiP$DT<J!U):V_5nSr8]tF6M[Qr'
%!BYrj_Q7UgTSkM;@gNOQ`H<A)d?ijVq#[d/VHR(*;Le3KVa8SjRdZs)PaWnD[I4ROF;XURgNAMl&f(M0Vu"p15K2\M]d#$@W$2Bq
%:jN0MjL^uS=RHaV75m;I@4AVo11+U.6+SSbmK+U3k40'G;*YoDXJW6%:r<1P`bXkqn3if+*pT.*1.D6\@N3fj#c15rY?#o5Lr7tn
%L3[ge3,";ZBr8aTFhk]50P-3h%B:!7bDZ*<CuYo4!u$f,Cm,E1iN[?p@L@5C9V=g8IgLns7U,!Wk(b^]$?e43UQUrZ`<6kL#K7bK
%X9Qo8m',af!TY06;+*:0@]8T(.f=&)&cpd5@:fKc$>;K+Vud^LXb&;QWa!'/]#/W4b_E47/0Y5`3>d:]E76D9h?;8#Jc.tJGrOUP
%N!$09Brj:=#r/Yd5=eI0:#bMgY/&S`At_p\#\u*5,EY4Mr>f9a59/Q]WiGDG&M,8U1VE[o#OH(2/GYn.<m20C%Ltf:AiGH7m0Sb3
%Cm-F,EiAij>e7mnVcHIEYN[SL<V2:[L/Y)bV/4RBM^`7EGj)5uU[SW?h<aX(T(TCC(NPh4HAX1JI(rF[$Nl`iA8%YAO0Z"r#.G6$
%[n>p\T2!-`=7J`n[-jYEZ"]R&EO5aE<QW*`>dE81BW0$lA9KGRoG:8$AMr/fd?jY1/u9`[l"DoR-61DOKlM)/,#O(e$K4*je,?8Y
%Kc?^5@+V6&aC(FkTJ)!##F(<>"IZr=T;pT#63jM';95eppk7AjdAd`U]lUu*OmSJq6_(N0[VplKSbt,$&8+#TKb1U(opc#192u&W
%98N+1aGh_=MrOsh/Zu>UP&_D-g^m#sW?Ero%]VKVVIM)VKI,Q,itXMf5j_?V]0<CN!UDDF])k9/1V=t7p\7C\ZkkZ:YaI?k%]r5$
%LX$WA2?HN]#to]"LdYQb#-FiGT)s;](U%+t4m"V>@96!R_Y/5#UC-m=j=)![1E\0KFMId<)1:6k4De;a7[l2d.F'.Z2cbl@P>9+/
%jE$#!UlgmE^5JiT2kiWjrT6:G9--\]J<*oerl7+;gY$IKbkA$n8eN3:kYG#de$TgI`pc6Y\&PXo-<[o`+5D7%2ZSjqH[sCJ"0,7q
%G'^'\'g]>CZ';L)i;H`CHuIONb,d/:F_sC58ee']I>+0mS5E(WRm.1U^KnN2_pJNj3\&1Y*"rXNr"Y%F3c>sHX%TroN#,CE"RTc.
%HdJ56o]bfMmR'Hh+T2o3(I4*cl/jR0N9<Yaq*lEeH)mt3AN4p]o<Ktm5jRBG<q\F;aK=@B-q)LI3hnLh4oK/C-KF4p-.$E>+%M"_
%b.")ZR>9X)&'fQS4ij>r-:;H1cf7oQ;.+`XbCUQF;00pDU6LSW>3W9Vg:,5e=R<J9(-93H"qfe/WH2Sd8@]>'P`2k:22h>P'P'RL
%AMDIC.D*-%[YoZ@bimI9eE+P$4[t<XW$>\1`u7g?-d0HY5H!Ou'eO*ec@U!`,-<VZ&UkLs:j&#%;IeTW/4d62+'cOl%M3/NLQJUU
%\V+QNbngf>EUK.<C=/'mmigJr<(((mGq1$mKmL6W)Mt\m$7>6t2:.&='@+g-Z@p'S_c)!&&jJp^16kKY5\TN]G_J:[>_0hb*2mD^
%Aj,Kqb-Y_:F&^/L4Mk.TNoYla@M[6QlB:h:#S)N1HN4,<hT%aU(j>rmpHs`Co\h0i.s$%G_Or>_6bX>5`3]W^(>`_,X<Q8[/Q09$
%6bi_pQ68<,<`rqD.JWEq\/a.EFh$s;/':2o+P%=RY+5(f>Ul4S]<%Q@2&&od4>Ch5i=K\R!"S4r"j+t:Ys6AJb@d.&mi6K$90U/>
%3.>u<2[B^Aj\pYpA(Vlp)O"9,m7B:Z9hN+6``o+\2OiYjEU=>_:k+Cd@-u<;8IVfDe$XN0=`;r/"LFN$Ztu'2!:k`?=Kg]`Dj1fo
%Qfsj2/?mRVMQ>CDe4.4(IBNgCM>J#1#]3UBSBbbG'L6_bq.jN&G%a;#b%B=N%$8ElX`"k<*HsM^E6U_tk!Oq*0Ik@L]*N[enCEB:
%-G?S6Y"%4I.W6=Y<f=;(-&uC%0847;/!4&OU8_"l\K826f$$nqNB6bBaYt%5Z0h.M/iT?da%=J7Cl>5N?&>utiO:c(!F/r?<EUi=
%@tW%LpgS3<7uD]"cO"@TnpYIXS'd&&\4ZS`*&94pe8f>F6t]K6L(MeU00)/O\mb!R>\p*??ut]mCi?VhKYdc&`=pY%Zu]%MTQ\m?
%A&mLT!/mk?/m>pBVD7lN0!leH@QAd;bDr`?_q,JP=>m\.Zq)-X?]ZJ306NO=j(N#DF,SJ",g'+F5<G=^KHqI%,KR?7o5)HP&ROGZ
%YU?0^Dj.F%WnfsCQb4)5A>m<W[Z[qXJ1)%hc[FENKQS[SUY`?I$1fU#!JtS))A2Ql>(770'cN!o_1:Z5&DKn7O@`Ir`r^ikn2,NZ
%^mE,dF&4+c<:u<roY-n9E[*'C"dCJ-oM29sR^#c)BAblBdi$tM-ZaR*$%S.P^U>PC!11(Y_e&u.2$+G5==DBVCi4L5XYpi`XOYl"
%$K%HAm$=gR:VB."#""'R%#hmBqR0bsJp6mWA2G7!@QCh(p"bL2+\Xi`4.KY1,H4fWMMfnMWeAR0Mn2!b;]F[XXP3B]+kR-)a?Aa"
%=dBaK@NdYN-e+Ij.K!=#*::1CRk8U4)g4f2Nk]*LQ*<[Vh,!#R?[IG2f5"7$k5<*=MOpS9\:f9MLZiNd9D&=ZeYXqVpt/W#^cMWe
%=4L]qVOJ[@%!r`Y&;=/RBMqP_iuU]B03ngJ^dX?1-plF\5TPfa(=CR7ldJTu=],QOJ/"*SQ.1X'HbN#(*RJ5;/3E:!kq<U%\HVBe
%*1!V"1N(fLDXJ(;h!2"s=u,%hZaugcn;us;&EE=$!Z%FGf7I'hq[5p)CLa(S4@`BD<obpcBa>GipMPON7'\.I-$9q!b'JT4O:Klm
%?FN2H:+V$kF\Ds^VoWqiLrW:$4h5<l(ZHQpW+n'kbXBS@8fO`2enS^$(MJ1!O]#usML:\P@o/agQQS'Dc#DM(-7lq@oVaC4!-2DR
%m_Z$Hrcg"77stIa7G47E)s1>U:N^gC,uP+h-C3C=3Gh3i9Z>Udo-@i=C,t5-D0MX,:;sAlk#9%C`$e&OF*'r+2"Ca;4LV;4FPR]R
%RK5+ki"\:H07lS[<F_SbAed@,,N)$,AK$X7l<96X"aMcRRSAJ;BMHdV(Pe'M"AA+C#=A(nZ'DR9&IF>GF6@BJ/>FZ<+FF/JJ=%po
%NDV<3PAN,F#eO@=GClV=8@I5FGe]I%JF:6?XNL/;>Q#H8^4C(c+Kb<\1!+c@\#*W(#/eWCcO(2R$+#M>Kk@As/GUhUVN<:BG`7G7
%_?d&&IY@+CH3&ml0dFORb6tER+YMsNGLVC682LuIKk&2Q7;?>R_J8!pGbOqh\71J1O`V(VF%@n`+bPG9'[E$H'?5#QG:5!Z22e9r
%UE)gY:h.QH;.TkFV020O!jd]LjN''P3HF&j3%9di7NRY`bC\gKP5b.PMH!">ntHK+,Kr+AL3/f]L`,i3OF3]7dea)oV6tuZ&n?+"
%&;=E,JL1!:7FK"7'?qMR?KATr0rp*Z(9m)A&=oEJ2FAo^&!.tH"AZ'W![6YM6(d\!QBFSf/BE+p79u:_Zh9:8o.3kD%RTqjiQ<r`
%@[tIcV;HcB-$A_T9N:Y!n"%^?$:!0GAHp6&O^&`e%s\WtWA;$I$>_nL%Ol(9J6:YX\$CjM'K=OQ,Y1i_0ciS!]Q>[%H(nTe638df
%O@t42B;W;8M2HiqH+B2/m:Xfgbe.f;dCEaP]=?9ASZRep0@&EP-Jc,BL6?L)jojG@k*H&M^%NZU"%DmD&ZW;qJ?[kRK$-IN%)_mL
%6H&2Q''.K091Z#M&_;_P.uCN"]X5.O#oE\`+&O*K*2Hqm!'%iE!N.'e@kVI#;>q8W<p@?Qd+p/pqq+5)J>V1]%ET_;\'t6L"qi2%
%gpL/oX:\])@8g/fK_4mcbTNdt,7o#i3=DX4#e@IS0bJ5Pj/M)\_3C@mOC%JtRbjr)P9gFS1N4oSqIbb<LIs-TVM/<lc95O\#o)=[
%L;n*:-:9^sf!\.!8fSgTKoNG9b8,*[B`UG<)>"3#k4JmFa8<N-N!;3']L5u;M84lOANjXkDBQ<ln16V7"Ag*^_qZmWD2%E?P%0<s
%bKa]iKP6TY[oM@2T)\Vjio,D`IKG].q5PCRpSi%fZOKH='me7ujk"YfPu'Ehd,S0Q5ngFrh))^aih[XB!)u"C'5jY77;YErRp:EI
%?/6`*>`De/_Ykm4<q]0`L>RR?"J&2s#J)4KRa?K2oT$/NPYCRJglMY2c;tJ)]%cj#M,Z.:gZAfXi@eXDNhLci1ukju)(nVk91A1"
%2^kr1\lJiM]4uO`DN\ThTF_\1jS0JX1lL,".LLm3g&&s)OIaCOdME7G!ojQpb^dQuBU3Iios#a3(*d+Wj%WNQ6!UiH%Uj&V/NV[$
%j.V94_:uo`\*"mWHmW_ga*lI%$\C7_kDKo``a;X2P3t`qBL(cT.3'<r`a<\*qRq3BY"l]CK&[<m\>0/ZASHZll;eg2RYf*@/D-XA
%n']l%7]7-7"fnR'6<5hE-JjP'Qcl\m.Q2aFg=ha`7%"*Aj^k+pXoe>W!/1)CU0-"R'-KZ*$Kb1^;.R]]D$c>44!P9EL)u1UPhO^6
%O\q_:]$Hh>]k_W@R*t_)I%?C#4#^<19'*UB*CA?+r@E`2#_ZE411h%&`NjtneJjGJp1dZD.^[.(mdXuT3"B"R16hK7@joncU^*K`
%=FVZ?)Vo3X"8S<O0l[fBf9jRa:NSebd@OE=WG[a'.rZ`d'sUc4:B;\P3(O]>=?3pddT'#*JSms@Q\cK38XbO)@=sfsN[nCK3MLV,
%$@[[+O3oAE(jPmp.*7@q#\YD3d'IECVDa.F#CTYu='F(GLc$a[muoK)#fW>\;pgCtf8PZMmt6FAr#13P$RP_oiJW.*"8&ZjWr6('
%PqM'22snL<Ue.8mVD<p\LhHoa5]o,TH9`/k$2B)b79)Q'iaOkP.*8%jm$riL+:m^>0p!,=c34.jdbuM@]DsjZ]O+PP`L[=%hbo<K
%IaDmE)55\KeuVG6!YPi3C%XY6=aP>8M<L:f.6'K'R)""mk4R1ln[@-,s7=#)\%h_20E;"^^\LZTs+<nos5X.XRpZ-Qn,DVH^\^gA
%q;KqTrTb!/rpVk]rr'$MoiHY<mWX0\gHU).q>M`MnB4&oh02*]m_A\umt3V!I)GE&l$%XUG;aZZIL2*pO!Objo=ln"s$]+Eq;Zs$
%5Q6:e2?33]BHR7C`uXgJ?i6sSHq+,.Dr:TNmsb:DIYMh,s&e?sX@7R_Y)R*gc:6fT&*cN8V6_Rr&4E18N2Z@[PqVPT!qqF2<;"ta
%$\FOq_,PU%+O1d<f7YX$;[pSUFgEd5GF6.dXRI"`7;"rP"VRHWN#qC2L-#22kgJG@^Ef6>205hT),O8DPes$)L*\pb\kkF2g+L$?
%];S;cbU@>9M8i@_ph$'kYHPQ=Qes,2,19NM,i))\K'lr%#OBAA;S*NARW-sM>Xgtri[ZN6$EV7qY;$lgm!k%b$6`t"i5kKV\EbK_
%;\XQZ8^\/4&V.F9]:o_=NQYksC6MJNWJhGc;"5!)\)ml`r-/s&KWNtT18Ta&#SKRmkI%pb:_&BJ%cn$`QB5gqU(LM!_49MH*HT9m
%U:si>TCD+I]M60m(oeVeGDN.c.')mLOqX^KP/\oV&A]E,k@94Fg1q#g(HZL?`m7<M6LY:oiIr!n\^(;1I>_>EBikBH;/LW!k:HJ1
%p*,<OpcdbR9L[!DC6N'B9F^>>NJ*m0?upP-K-rCRN"DX=:5K?X!m:L1['sbs;NHen9o]*X"8(n5)d-dHVJMRVOb;$Cp2jZ'4$pG@
%#Qq7I@<ug!pjVi)X(M%8HQSk6(h^f-L`2`rPDW]iB2:.9%i?&$!:+Zo&B[Bc*0nG'2TJSEBudLsYm]-13e)[O-4@06FCHnBB7&;D
%<EMAIL>+FI:15^YdS'@qfV?Hod:*c5.&pYp2"-$GB^=-1UeA:BKFMorMua+=KuPkXPhH9R?7Q)&3&5<<:/C[/gmW%>
%(q9Vom$&/=EeW"X,24-IM^5eB0.prKQ]3cNdoAXD:Sq=SCurdrO`ng-=g24ulaG/S>kXXj1.T`X-UPk1-W*Gl&r@6ioDjf:mE^Ia
%Ta]<SJ1Xe6nGr;1rEKKo,?fYG<]<=38i*c;J-`\@Oj$QMF3o&;_T%Xa0AIRA^[XX!O'CEqALhV"?2K3mM+A-!aLQ82XCc[=2o4ef
%Mp`iF9:AuCom?=N0(o&M#ZKlq/a`22M_fANN69oF-l60Y5eg,;f>Ub6S$Xtt)"FrR>REd><4/ZDW]18*&;)H_G7?_B$J.$E2iEP/
%_O>%!&-l`DVo:oIo>Aj#_?=!fBTcM9*d5XR@@,4-?9Yi"^Al<&P4;P?X/l5rS,`L/cfZ752Y;RTi='!Y+$uR][[\K<G@CZKV%!Pm
%;OA^R0bfqsmgS1s#(@uhC+Fsh;,a@/6;f!fW^p8rR+fL_D&M4F]71A!;/;2)[pq0,2k?D(D_Z'5AC3,<%Ala'MIiOWFIB__/M#CG
%;-Q#.'0E)=Dr"2)Y/7SB65C`FXgk&k/^mh'-Tjmh7&AXgeVhN^]$qi6p&q\P"cBOr02]@':9&!<4N!_9V9_j9hT=t)+^0o5i3p6%
%2_Kb5IRt/0o8]bAS?'>WcjpL@<9b6Metafm,`%m8Q&G=2N4l!%Fh;sB.#[_<H/ZVIq1XcG.-Q=`=)S+C0psUKF$h:l]]<.%TYA@]
%'?e(agPY"^PPo9kKhuPVFd#BR(C7qAq>qKjGmh6e<nT%"*n*cmg1(cBW9+quoSU8l#4Y%GQa4;n7[mXkQSYcM02KGV%cm^h'S)<5
%9YLP=Ot"6QioFPqp`4l7Jt;1d2P[)M)%P$R>dO`\fT=!+i^ci;5W:4D\DRC0*gg]_K;B>t&74#5GpK[1QEi\sq>I/\)L0K5JcK'O
%)mg)G2/&`*TO<,N1mc.qlich4B!GNE)RB0*<LQ<]-A[]jnHd(J6$IQtRRdX-`P;c?Y:'Ja"Wo54A*S(08[]o8#[8<_6kCs!Sp]]B
%1Wpg*/UD+3$+9nu%e%l2g9f4n.W\\\MuqD6%6dM&]0t5EJFG<.T7cSpWIc2.X^t#?$Tl/CDaf%mD!"SJO6op!=Mc]+*f<[Tgg;rf
%+SeHL<R<UqCM1n6?BLb4d,gbMoXbZXSLbN&YKILbbp`/]%%_S&*MCZ'%n8WJGi;JTF+KTbQUu8ji&<YsC`7%iJ=i$+.@7YnZF3$"
%%1!f8&L6)rampE#<ih/_@&l;WKeo,)pS.b*OlGDc_^2:])S;YXjQHV^F))`%7VRF'Qu'ma9!UQtl@S-8gO/2N1Sgie4iiC#U6nN6
%;DP0=f`89sga3=*='UD>P,a[6W5!6r!]PX433$_1[7OYj0Ci5j9s7E-c>(a_96'R4D!Ghj4`EldRIV#JTLoW)F^k17O`]3Fm.P?]
%e@OpbI+11fC-hRohWi[q"+f'Zq.JL>%B>/>/^rE)X!L^#@C#a>*bc'k0:'Gnnn@nF;B)CbU]Hrui/_IqlH6\\le86T2'EB]5r<E?
%G3RT9KBQdRH1\^$Xu]#)WLrK[1:CV]Tl=(US#(JVj3p]@c#sq0&Lj5**bmV,;e-A[qP%A8is"NEe_3t'ch:$.Z1bQ19;VnY>%st0
%RGZ]0@"0\t!1TfB*PM-JaNp"=[pfQ/5u@(=\eFtY1KYUS'Dan*PBR>2$VP1[n90\L"ZA/7E-6s;;h<::*C]HpWb-pXJg@aQkpaSW
%jHpd1Z+]6pK6^Y0I!7#i/1";R,$8ic2do_>/q^(#L89a`!1I,K*D:,!=EC/s<:ma+,Mme[%R3M-B"tgB#%7^VOgXbZ7\cu*6c:Z2
%[hp/aTRLF+%Q!'u4$?Nllbegt<\lp[RMEE4+<%cah25pGfs"1OEu%$1D_S?EN9[BSVN675_\e^rN2"]H*X3acf*Gl87TWV/q==Li
%[]4^nju(+@0r#k4OZXbXba6-[5"4s=Rs5ns0uC8kXR"2J1^G,i]CoP*0%'X"lf,:HZ^6#Vp846hPWRHS$R1(L26k0%`M`"d+Z(O;
%d:tB]i5mH-"^GbU4sQGPM0RPln%e&6QO3B_L'RG$jYQp^WY0glEKt?<G>e7$]BVkO4NSJp/pWXW>'@1)T67sr]ek$9(Qr/RW3+m!
%+3D8q,qPc93sS8f.tnMtEY69PrGX$%!(TSuSY4K0.b$he;Qh8!j4W6G&[F\_XY1Z_]V@+3XA#:tc*q#i[`>^k1nsZZ;Q;^c[%\sZ
%Y@ASmG,S;(1Yi-;Jh0b/^q^L#-V?R_/p>W"CG);mG/,_%A",,V?j29$-Lct=QdDq7")\mKGd=nI0ONlh@WmL*,-:5o$n,5bYu%]k
%>&,_d>>2&iJ2=m)LTo0VZM7Gr!0/dRS#eO#/b3mt<5MlU]1mO'VWpVCcVk<dZ#*`hX>Ojs%^S=SV=KZj9mn[D8\c9c371B5_P*h^
%\3m\1ZqM\537dn+.=oZ6+,nn!_k"28L:r`h,]ajGj^hZ<[8cEEhR$mn-K297TV<#Rg4&4&W@p5CZE.\.Z.fAB!(i\Hm51Oh)s[0W
%er#gL%W[?g_#Tbg_hHGgL3[["d@WjNN9aDod]QS;*m9X$ZH`:l'_AtK/gMD^\r/QQO&4>`=QKk7(+n#/&V[kdI#b?08Mubu+l>m8
%7d*i_/8f6W(UBbN'FsdoJaMHm%.^=hQJ-kM=5=J1#,c8W8em"[6k%ch9#t`W.M.]7pNd3YRXk!oGFMCWG7K==`;i+2Dt>3T[gLB#
%5mSXS:%CZmN=:L>*:d!h;bG5cbRga&4\1!Qb7iQ0A%XbbHG>TdBJeX3*=\Dsb!O3n'B&#pPTdAFB^'DX.IcFQ0tXdW,:0/KV#bPq
%H%7A#6IY/B+fDAe_HTkD&\7gm'e\9mSsU[Y>eTQEbJ't4M-<J.V<7?7j_n8sDD>6=pY4J8D(^$djDHaK):IAGKK=bm>`@BWk:l>E
%?<&h9f?s+W%8E>BgRMR,\BUF1^+h=J]M)LVhrsQs$\c!J=6OsB(15mt=jTYT_?,:R.a%MF=K4oE#h3sMB#OepD=*Z%p5Eh_<ko&,
%Vn-.lLUj=Rh79J&H;-']:p`a1PObrCH.er@Mong<A1/#HXQl%NE([6E'OWr!23rCfi/rkh!pW'OVo&_KM:I<,`)J#E!g6?A;E$]n
%Rt(pH=kC-hp?WDVV-C\m["@b2@nY%hK"99oI3Nt/L%MTP,Osp[365/@PV&a\<F\h8E!j<S-i']9I.rl()8rKiDAlb]XF*qr90)PH
%^Fggn0J3Id,/p4(/GfWh[u84KV3*C\d>)YNVg1(:i&hJHFFR7pf6&_K7nP&UWW;'kUfoEP^^r=]Q,*+2N_mtZpn<I7^d(^Tgdm^.
%kF*P]BNkO<A\'%a4Cgn]A+/,O"tXSoj>[eQ.-</I'blo'8TI:KiuYP<51+#L;XJ,(9Xlok;oS=_@92miIn$lseu'.L0F!dZ7RVCf
%=*(\:_dH7%/`RS]i\3OG@V.@"fM.UG]$]u`<5-f>-8i0&KnTX7Lk#sCS.mR?+PEes6F$tsGkR'c'57<*<lcLU_-L2[A"%t6_r`X8
%=le`X\qhPIV,H8<83!l-l;[G2[JuNh0@X8S>6?`Q[#P;i8.fpnFAiP)XO7[;`g3WL8!FafZ:0M2_g=\&jQUC6<&hcr8(LoN]*^b3
%9Y><T^mM;Q`_o,VQS(I#><5nKp6&V'F\u2IW^$'f$I+)uB+R(#Li<UfJ>pm'KMBFNNTfNqN51`"im:5NMks7X&S-8Z3/.*-7uj8!
%$[i8SJh]tc:mh&M^<)PJ;i/p>(P5QQ4J8`Gfrb;plH_quLGg@0*>HEoI]/AT"WUY9iieH@>B.dQXfuROF)@5$JmjWfQ/?[`k7,$M
%=jF?DV`mRP<E+Y"M^CfD?*!"OB?q"lm""2ZQ&P)Q",&hoKX5+tk&qOVpW><KWCNn]Uu_u.B7CJ;]u[+>S79UVS-!!K*[ZlJ-k_Y0
%-;7rZ9Z:![Q5N<HTl\Y`JK&@5*AZC=dV+8;>'dPYRR1R/%^p+?KMD:GqON!9f^/`V#-.?L\j:V^,h<=Q)-L=$qldS?is!U.EZ;0#
%<]gtgV*HfFQtb%]q6Wit,/&FQTjLD;CqX^KTt;'0Xa#Q7@p!"O/k!^W)`jNu!O5LKMj.&aZ1CY4J06@`*C=\0Qe6'_L`_9FP!psF
%;%:2\e9]BkW[="aX-ftoNse1LRV?Rl@>XJW$m0PKeo=8hqlS45P!.nt,BV?0boPoc5\cECKnVi5p.f,^dq,Cb?8gu0khVA;7%8S8
%`mj)42j*%(>c:I2P@E;!5_WQ"6pZ@U\I:8:?(^cKeQLfgLu.8&)aNPK^N]+(^7t&f;6U%bP(sD;Nfdiu_fK`GQo%ME7jSUFAr4uX
%*g.t7:upjrWl\/#_-pD!5dC*>lsZ;RbBh"(70)Bn0>UDAWR7-X^]k/tL3XK'2s:1_qgbW[7i9Y*';qXI2aFmnMB"'LNufGo`,;lK
%d<_1')-O0a9<%U/6jdTpZS%)7[:Ff6'MIr"AOc"!H]qagN:^Ggq$7`c;`KjVOP^>J!@buC8^FOl*@(c>YSHj"CMmlP+\?*"6G&/4
%aIq:`2\W7`kI*P"AeJebH[Ga`m:#c!jR]b0k"=d48;q(/@Dj_"><PWu`56B4;n"ON%X;G'g]dsLP"TjBgUK@7d/9QpiNfZT]hl'<
%\3\8#&7SYWB&Yh_gJN*nfQf9ZL'N',OCIS(24Rq+MPb/VmjI9?rb*X&A=Npg@d06s?&N4P_md@n5Tl$/p*i+R.pG4cn^JXh;t6_<
%,8rLs@,0V5][12.+9LMD3@73aWr!Qg@i!hWW:8DW\Y@Z&QiSEjO]*FJQ(s(R40jG.q+Q,F<n4_K&A;RRKij<lR1Xj7:+Z'MrL-*e
%ZJb/>$`6pVJl_+;'m,<?@"Et"?rNF$)4k&]5lId_hH77_%RF<'2reBId?I]`"N-16*h`m/WY/7.k?1SMIiJKBXq4TWIo?]":@C<N
%6BE#I9R6q_a4>+ENIB99;CdStj#;De:e.8g!me"1J:A7F6mC;n3'V(0qJ["/069&S]*cqn+\XBP+P-0/34Wk01c,"Q>1Hu?Us0`7
%J$NL6o#t2TnVC"mjm&:OVY30228XS/aj#HjDY>aseT$B+$i9[I]<]8m1r[[LEgRB?TTMWk:7X?kiTU1d<8>/@o$fA^`e\d`0R`+d
%T%Ba&AGUH_a>#Pk2)E*`Pl^$`Y(Y&S2KN?GF;)di:75EC4n^=V3feeF>7*,uT7.@-GK*E"C-;h?\0GbRk/njlFuF[EE?>5\ApLh7
%7OJ4&W,VhtoC?)f.eRnHm9R)bY]Up>/jXso[s^A-/5=2.`ne+d@r+TdbeqO)oT"IiUVL0g_T$1HBMDBR"t<Z$qMp92jr[u2JM9(M
%qDMT&gK&Y1`oS9$o<06&9JS"p*_1`4+sJVUXb`CMRl#i/I,7A@/`4;Z*<mTC;e/r5oXd&Pgonk9rs=J7nTo,KA#ZN^jb"PR(EL-+
%kQsYIXs=.&9PMgnE6429-_RH5CY$I(Jn&04,jL2Y5(lS>AAA47":jBk1)ot'NGEeo4tWi^/C$e?:;[e)e^eIYM]jM(o1=WgQ.$:a
%_GY]."5+6BBf)5)'Am:=L;G4`:"'W(N6l?a[Qk#+oj"!'SLUR]VQ1O<Er@%GF;M\/[[N_f`>TR;;*\pF'T%N:?^Dq3bU>LRG[#;I
%9TOe.llMXT)Th8)Md0jq\HQlPG(Ida*)#Sbg8"$CJ/lVAg7"Q.'Q7W8hCuVf?.;B)eoS3`'X7eW\jF=`^2SZ;TI0Y;XU6I;(gIdj
%-.6IC'jB4bZ5khR>"r.+KTcdF*T?St%Ro`PaT5l0YS,hd>Q`j`o!IaQ(HJM,@Ik!u3;I43m?u6na/]4pf<Y9&G0up8R_H\$:H+Y.
%]8rMB[bU[*=c%].;p$\OV7=,6KqsJqOPtSpc>0/ZIn!Pfk&sI[0E^i:$WL?$Yf#g6Rl0$-8H^@PH!k]eg#d,Q17Gf8m]!A>"gR\X
%ocUf%4hsla!6U@`TN3E2g\WNn8[L\*,#Cgr%hGm=gBY:>12-?;pdSUEG$pPRO=ldb6JIKA=)tD4Hbi@b"337RGjZQ(XC-B$fr'1r
%4e5Cm5M)C:"U9)n3d>MNq_FnBB$m7jNEbT;#mPU[;-f'6)f*%<=!&_8OgRaF!-VL8YXbHq-\*="<ac;K"/2tkKg"t'KVRFDHcaY9
%*^;$6lRF>K25p"Bi.;7RpT?q,KWDBis(=,HIY8!SZ12Qd;K6u/[:fW!;],GK$iTV6Mh+2fLX6@)h!WEH`RCb*dr`Jr@P++`6Fr.[
%LbLgU,ZL8tXj54)*UGl=q@]L#m`tc_UY)!B\5@*b:4Zi(i$Mt+;;P!@O4r)sX5$aQMk)s$;Ua]A(fI@<W1mF]UQH,!HS.9/Db&Iq
%4V`)ETP4&r.qbbr5eDc<7joo$-3_3B0oP3)c03^-0<-89QA3%(<I!G1OA!4YZnW.]1iI,)L>maXnIB)Ef<*NMrApKm>*X54aQ;Ec
%N'Z52QMQo`eEf)fWp'"%1sZIu=<kQ>UCfoMa\Q5@0'k#M4?%,t!0%5>':EaC3Sp+)k;i(*356->ZMIi/7]-ohWhA$KcTlk7duSP>
%lV0LY>-o9:dN%LMiVg$ZQLTB"'=Vgtg%f1e)t&W1>%]9m*p;b[a=+/cOaFhf.+.GebdUa[B?,if5gb@Kd$I'*kI;c^KA2%J"8&UK
%/im%O/SZ06Kd&5MZU^4%oR)N\[nE\b=%EDhm5PLQI7%"MMC([rdId`)"OF'Z`NQNK&?b54]r)+V19I@2qih$(;%\G_;Pac#H24c4
%h^n-r[Hl\S/sj?<=9LXo.(Y!@\:/`SDG.ML+"b"aH]J)hqrL^[]3='U9Yu1VA,nsb%[\.cMHN50s'8_CAIh#Kd%GKfRe-(bl6SG5
%NB9A!Pild/g#1?r'+:RpctE+.\.8armIgTn=1YHZUS[;OG1d;>%OBUh/\tui;dM4[K@C_*+'$kP')\<qL-Y7[gWll7,sM]"c^0WA
%Z_1p)Y\Nk;/3m.'1Yat;\?#q`P6'05T;?)-d.>OBp1O*6NuQi5#=+/S5XFdX/_QuIA7klP!65HL&jbX"1'M`sr.tF7+]pd:T,/)[
%]joL)('2\-`c#-$kcNp&/H9F9n1&1")U_p`mnC>lQdWIZTf8e(aV$n_TCK@6be$N?dr@o)I"YO&"bbdEiG`I,mOcSN&Tt$5S0RqG
%F^V+KQJsEn!'ERa@h=Xq0XSWA0tHf@Ooj';gWSOQ3q%>!a]4Q)_AK]Z.X"?1\=Y^fKe,'3q0uVL8B_F!MXY$JDeYD9e4mCHIBn1G
%Bt2/-OS(@>i'4/1+[%]J+i1q_FTN%3;,6)l9D&8qDH@A'P>!pPRCR8+>O$5tfWn&KZ";A?cN#us9VN2'o%UV"_Pha!h)'d>!:A#m
%5ao[b&M=a;TRFe\]E"t2`6nEiNp"`FWhOaf*P_%IK?V^'E)q#tY.7qrnbmU`#O="BGlf7e,tfEGkp;Q+_dRo!p(lGrpiTZcr;trV
%(*;ok+PfQ4qBJ$^W8/%L#q,XmA`@&7X3mP/C6W]%r(3tZ)7Jn(`+mdeJ+hoPd6H_YgAffLN0"!J5`,Dmf>moPNV),c7X?<OM0;"<
%=$N@Lkk+Tm@T@\0FAp;t3uph!M'%]i"D8p/eTgDEoa9Uk8kEJ>CLFZ81ml*3[N1Ukl@'BM"m#W:!P06!Q`c&PZHc!]:E+'1lTA/'
%VYI@^)SQ4+X%35)!G*3Pjf\H,";HT9\4W"2"#?2Fm1&Mj+Vn1@9dBc@!2F]"o\*a^4U,%lKm4%.!`+TP'3UsuI5psu27Z_].@4n^
%BR6_\;aP[qa\KZZcdB^@#i4khmVe>2Jt?A'gK7E\Qq:Mg*p8fYc`[)GiMa>'6mdL/*9cVZ%WGEoEP[JX:joT9Wd*L(_X/'IY97q2
%**E0T6GF$aFd:phF&+*.WA6RZ<5C^ZE[4FkhUf_>lfI0JeE%!+qR60Ep>>RaDT`man^8rc66GiSc.Sh0\o)fe'%'7X*eO-MYNs>S
%!Si,%p=dG[AYfTTC01.MCh[KrfoY!kF5`rFEa"3DlZdq+Q^;>R^b`tC<NN?Z>dlqcmn"HUlH`$9;6*t.pC"dIB#P5*FV\UNPe_:'
%*LP\:I&AQ*M==[&Y(#$:<GK8@EV7mm_e2H?l$\NKhW7QeiZ`nL$DZI`!pZK'Fau1'&.@U]b4C$@boB>KWIrMSfiOI(H4-Bl4)4qI
%K,QQk<)$t;8rI*5AT%gqN#"^-Kn//"TKKVA8RPMo?:p5]*1t2^,.VnA%9-ghO=9879"/]74D<a!Wdu:[()r&HEAXtQ6iDGkXc:M;
%N8@\Efc]sNS6bR1NP)fkZa/HM_F885FfW(#EC!fH#;gn8-VO4aeijW\p!l-12/Ch3!0ql-emOo6_MWELO_;XOeOK.b-*6$R!ZSr=
%N`;PnoBo:=n:8arH$9pcO:*>IUr^4\k`ofMe%>kK8:5?PcJ]i5jsQcd1GQRD5?h7$+%#D'5l@:aaIYJcM349bjeMd@0(h8W\RG:Q
%;Rrh9@#]ii:bVAYcOc+pa^WXO"(*p"'Di^_4Q+,G02Rk5cCMl0&Z1ckI'".V6q^LA&iOT3OIk3n1r[C=SmB3d;=b=>nku2?#t^ud
%mB_I8gR$Z5MU%bsTp\2W5-AFC$Bf/B1pFe;i%?pTDKP6b^12hm4c\HO>-6e^`a4Zf4;\P>?Dkm/)VI+UE<&*[=e1EUi=H5$Ya)'"
%lje2Xjb^VrF%uV+Pe(]5:a>b>Fj*EllG$]REJiR5!qm\5I,-pn9qsBG=U?-ATW6]dAU_*2=jUNdq;J09>u5^@"CD-hd(]r7fXC#G
%RRmWR@BKm_;H$X'M$8'[3;e'EkHqNFai-qPGWAV9N?,pJe"5'#HU_s!C!(Q<+O'R_@C#VA#fI'3QY1hE'_tN@&GTnKRR#JCQ+Dcn
%18!3t:Ra>j[ZD27Pbn;N(uYYM*?[,[afPbM2T580LpJRWM@bh>Ad=aYH#0a2);,t^4]!^<gs@1)eWm0Qb$YnE&=1>7rCB$:=pE(m
%Lc*Tl*tRU_eT(XjXgTC!$>Is"[&DY$CltO^g[I+!rRu!QLB[9$X:\'q%C[$U6tP>b]?i]_Mf/f::B\I#,;-jVoa722)m(3LIb&>[
%p"DYu^kPjC1jZ&0VRdaKN<4?Je`\@<IkZD$ROJQqD`@6U\$7&j6],+7DH3BoJ"L:WqYRNBpI"bEn/ju'bgu7JFnT2X;i#bND%!CJ
%)br>MINO'cqGjffmhmdP9IGM@rs!:YMXCS&<hj=8E`Si0^4\\*Y\MGPF3VrfO;=RZR9M@r42OsX9@h".UZmZu#!%50kS$;p,NnbL
%X2d7TP"YUoIp9#,:2ZNQLO7;V<K:iJpY-i-'@kpiP^!4I)HlbBKY+it;h]sLdaR$1gMqI"\j\Z)V+5WK(ojgu*`ib7%^aPQeZ.h,
%/#@lN_o/`c=%<dL?@2<mi&^Y'?K2_f*kG\2nSXA#*LV.G7UXe9FaAC1;Cb^h-*9eW97PT&^r'nJ6tN^KMcqe0+;C"p^jH;?Y!@]G
%NK?_2e]89H7IQp7C7KFh70lBY0sc<7\KMp>UI[^\nMFnO2EnAjNsSS1f,!JR:9hh1528P?"19Wf_Ag)].sH@F#RR<*5RN5dV:u0(
%k!huSs.WC1kTd!q$`\O&d:/as7j3Oo,@&FB]@tVi:sj':Fu#<pSW<UelWXVXPB<(b6KhHj%[Pd'^Y"J0/Cim&WJ)_3N9N$hF-0oQ
%*:)j&"*VXpHc-6OJOI?"[N(ZXLN"[+C]^Eed\_1&6>-KNWr[0B+1<n+TqNg[:>LFN.F\U2\=:.(&@e`!1kB>^Ul$`'QB4B[%e.%k
%;tNNI\#+>Q-dM4.)&t?1Rh3^>g;3_NQj$9OgYJ\$+J9<_\2C=@CsaJ$@/),E?kXi<o1Jr"R$qhQH@O]</G?jsp2pfRjD7`c3ga0@
%M^/3ZI)7M\.k15uJ*FC_)6KB2&jZ<<3mRj(H1='e>A]K8@5I;npe#2K1-073s!@2^G-O_+35cjiH7I`HK,r-K=*52m#J^>O?VLR:
%.+JT<)WNTMTVcV=4jm;P"\MQtgQ$le8nnQHJMWn*=sTkLDAGuu/]#S8i^/CTbnY1uea=HpW[.`YIVEYLm7b46WI>lHPLKN[=i^1c
%b&T`rr.QMXrqi832f>UmP9EKlO4+%>>+pd`#!F+4miF%R;bd$$DnD6.SKau(/3(%HRNfCD@k]F</qajKC+'PQY(gS&1(a]5eC_n@
%:i=qj4,7X-jA0aL.+gqaZ5<[/W-.R,'%M_uZf54QgD\1\#`,#(1=t^im223K(gn]#6#]"^)eDl%0i=e#6^V#5!@62a5!n[`/+h=j
%Rp*CW]:)s@^L*_d$:*Sc>aphESA-NFpd9jC?Nq3Uf")ipHUakN1jJetI"h(UhT'u&Z7KUL7?cZd(eC2kk!1%OL@ao5a0L^;9PhgP
%-t8*eE;CeaB$].2UL(aX5T"Y$JEV:KEJY']Y9ga\:03Fc-*=K8[Zm,tM)h&7O-*7];/+.bp33Lk$fJ$*,YWSip#ADp$ibX!Qg:05
%:DhJ+)j%n-emi_Z'tF=D(V;at?Ug]@a3HSV8uXGmBt&d6Nl&k'YJB91]$-B7P%HLUk^8fB.X+n"q;*_jJu],:6[1`$6P:asH+l]R
%^kUfV2itIgq'`h3)a]mGkFiCp@>oNVTeLm;(Tft5RmG;9W>n<!oso8Ab)Q7XMTMq(TMq0(#KaT&hcW.oV/O`@eU1j4B[.TGF\U!^
%/qG$VmuCFDUAI:.TW>b?X&2S[12=AqkbbI3X_)?M,=(@j`?Y)8>'2Ure_OE@2`1ShF"$`4$rClc;qf-$f#[,^Me$)=AmC4"Y"7Z6
%lDhA7''qm_RGk8u%>qtBoX@no+>u>^]EX3[oUr"ah)_07%"(t%<MVId%scnNR'*i-mNRN*nl#?uT^aC*/..)GA4[nf+(-@5e>/uM
%-#'&'h)h$HU,UO]3>$6SFC1kpm9:ae4,^n0gS#.%YU25<Fu.`L6M"YopBe\q.rPH^>P"SF[.`bY;KXdK[H7bJJ6t_KIZUVQL-i9o
%,%lTjJJY(9-,d397qa(eK,fEq+%#Br9HYVq(*4.UlahWI8pt0BPLU<Y?ZCqKYbEP7p=]bm^6]nKRd`OP116aLEX"CQ1Aod(W@'o%
%2J!EcRRM'oGuN@pmeG=M&Q0.0155**^_d2KKP40>ZSK,<&<!rDYl$k*h#=Qd1[)_sHB2e@STFH$AbJcVSs.kY[IV2Me\OgWa^GFg
%A>\R`0Z:94(19e!qf#5[89.4E#g%k?oj#OD2:Q>eku#aX@LTP:;b2?(-]<0EOHoA8%_TkAd@d,.1I/IL$_C@<.>H*IL*dAI%7ENh
%j5DOJMaHZpq5sKD]qIF#nuDa,0\_/N03S_2IYc@,q7#!nK"IN7Yil/9%6X(U*iH8UeZ7MN>fjW9d2>:<V-ll^!WZg$\OWU7gDGL3
%p5G)bLBQuk7[69Kj+U?9T;mn9-F9TpN1[80252M5]3U!TY"beom>Vh-+4+D);Scu!m3SoL^/0p/'ZEpOTr72GA0j9=TLVodmf*^&
%49MH,$M_*am#?DWA-[W7F]qn!X61cY5g)X)qG#e;h_DCqhJd#=DO-D)=#,;4A`B?rJns0SW0@a`l`G;J!MQE12?+BF\(hQnNDA0H
%K+A_FWgPXe^5@p.*G1-8p_9h8"r[3.cHfc\2q8Xp;`!R)SjuhqMFM8A`\(rqJkd'BXP\dF?,_"F`6^.VR"['G%El'[BFcHkm@2^T
%#s3dK5[,o$/'N&&BIuDckf]P'!d-<i\o@TbFQ8csCruc5Dc3okks1UE$.dQ8j8i"UXX*7Ape<PIG;*O#0Xb]UiDSCIrQuLf^K,I3
%E@djVZYW#R<>B\!;*>&9,YE>(".8SA6.Hac`f!1>fOZp[=LBiY^'GbWL*$0Eo;ZRUH_KSdV+"3hoh,[UK;WW1E@ReOq@0DsYjUrC
%A6EO"`BtnGk3?E8h\"/9hUQ/)?sOn..qmps]4,LXYtLKibUE;g7Pa^,/>d\01Bg,[\1'$Z*<LXn:(hSkmKP<oX`):Ub(au!on\,@
%K*jQc"*A,KNdN=J@NfAr4cse^]]EOW--YBZ@%+*$>i5Y9('?N7cD`N=hC=A$Fh^u"5Y`FHRA\9`-D<mGdDLhb:bboNfeE7q^i_:b
%#g^TZSpMsN9$fY=He9blj9Un9]5\+8/;dT=KT5^PBT>bL"=<kZ],kHTK_I2+/G%;o72mE"4C<W,@m*Dj,%#It_hE+cF&:_1<(S\L
%TIF!_%sQIO:_WGaf6t1qkEp4M*",!\UD>,R[iI']bR8*DA@1pj7$:$njUsnCW0p++G^0P+b@jOc0ce;6'Q'bE7M$Q4?*`?,F^R-/
%ZL+;3WU8eM\%tU=6Z:g%S4@86E2fT;H](="dKn][7n3,.j&Yu9kt3+=mEL!,8@((!!Llu%!,8:K)!l:YG<ddjoXOa8A)OVlK7hMp
%.^=bD8P@iha_8QOR45U?$\Xm$s8<3i`\+\WWN79fj1_9!"SJ.6PN&I#Xg9W]0/t,AV@b\g.tgo5Z>XEnT?ca#]#*+cX\soROV[`8
%R*R^!]BJl<&l./A/Vmu3q;'W=]g+t"7<AY=NK0-rmT(W=*la*jX",b$7W5flVC`pe@f*[#i7^]M]I$BM13aK#U(M\]4V&43RtMFp
%-J:66d3f??2#k:h2F;m(*27,Gd'O&m)`3C!/Os-K+M^:*nWgS\h]%nAQ^fMD_P':$n^$a[R._#&#]fHVJi2<]V-I:t=l"ZpU>F6R
%;G.TYs8>pS6HZdAioiEl\E+;(Anq=c0!HUT)=4Fkn'r2d>pp!NS7,U^)N_m-FE]uubi2bJ)!?ppMh(B*h,(:F(Lf3$gaUrcR@(.#
%'ngpe&_+&[Zf3lE1W"ARelYleDNZ.N"f#I+HlqrnQN=iEe>]HLn#DX\%5a,_*NtHa&[36Y[BA#1Q5IT]\oClZ/>R9$GCnSYjp;4p
%<BT8kcB\jE7B0]!(c#9,S+#jlGgi=oI]<>hH3b&X]+s[fq1[>J^*3hbZMFXn2IcS!DV,[#f/ZuWVuR-=:OfhEQCG45mS[Xi6_b\7
%`j2s=-OTo4*FaB[ho2:-7@ihpe!P'/"\5MSR/b0;S7?-Pe%ifE/J'LVYD,^6"_,[9-?iZ^MUF+955Braq9"+/,/Z2q<mLhlHjI8s
%kt2LXYbegTNV=3M3XCL3)PbK^edCWO<STPWHH&ZHO_&IT*dD`bBc%+<mlSBanH+\&ZX:rW,gpK:*mqN:e-5.;p1B!@V-XF;7?8jJ
%q8MH>j'TafX()ZeJaJB?cK^g0Y%N<l<t5R2&7.$'(p1V`b;1VXhN9b#-qQDS)Gib@I*nW\W<H.E-,h@*4K@\,?j[XkcX'RF5`;h5
%jjMT*=)B9V,]/1BD30dGo=DpI-J,>.1"K,0m07YN\_A/J56XQTB`\,@$:=f]YN@kWM@ilgNOM%rj<TGU4BMYlh:c.8\\]E)r6YM`
%T6@;o&g\A`NHR/iCBmpL?:!=?Jjq='gVSh"11)(sq!=1s*6?RPQ["01LM%F`)OlEA&/7=0h)Hn,/4((7>B1/"IP*YA4qhLQ5E^Wn
%mW;LjM"=f,':'T9kmk/:U(IqLhP7E?07u=HnqRDtjmuEl6-fj6]fDjF_#_/Cs'n3)-#)J^gFE('0.]G1`c'[:k,<c@!MD2#er(d>
%LmP5'Y<-Vo"I<\\&O^n;@>eklT%?0]D2C(_RU1ch&;I6pa@:j&Hj;4bqa[2?ni#pR=3'FY58W05fGb1f.p)8!r8)1jKe#7jnRC=c
%/h)k4B4.@Q`=E2r'l4Jk#;aE`G8d04.$PahJn,+#Y-,3A4&@o8;`6H[6,2:9?[ub,;@].eVK,PL)Kp7qZX]9!Wk$k0!1.?19g;5X
%#Qu%!oY+#JEYkO#bD-+9l-!`L-Cm:d$+hq:!#%[iY-c!m=B<sm0.h`qmJt;_Bp,rFWI'B2X8o^cHa!Zkq&u\o\1'P#0Gk5B.68mH
%i?Ss>OcRIJEg'G3duG>R2AW!1JR6\q'<EMAIL>*3m\uQ%,<m0F>Cah>:N%9]=h$!@8FaAMR.!*J_]jAM0mZaZU4fqm
%M`D3<Q@Lb4!B6f/1==,I%#a7q?%JC)KF)FZfEuac'?QIuj\G*FS3?0QL)M1L2paU_OJV[mTaF^N#aF=SMtj&bhV"hUDPUT9K>.%q
%kTPgVmTk"g;t[EEf0K2%)?khF.2qj7Ld-nq;l(TrT@?4J#H`l&"`:E,ZG``D68#V:l-ah-WB5c-MN6epK&WnK3/+0AP\+kN:,KM\
%j+W/MhQGtbmF*2M.R6B$S*ce>77r7EB*FeS<*dQl<$t]`."X9=ne=5D3Bo<6Aj@HK`-h_uG:RP=PtE=8-l46?jnh63p'qh(6MQDo
%qaZ9C87Apd&cF^Hq4B_A)k%bR>X]s690%\:+n(k:31F&e3WQ0[ec[cCcpO1=3H%JB4G`F^#eB57+WTgRs/Zq$G"k\m.D/gu9O]2p
%KNrq#^c]tLijun4DGDrK`UEX[?Nej*1<U2J0bt+5%mD,tXsb!c*l=-o9jgo;'(H+,I=FpZOO_WE]KnZ)1rD+7]I\knV=XLcaTL2>
%-?hc`Whn8Cb9Zcn!sk-J$#f-M0QkcC@3&SH2aB[J#Z"Up;Hr8pcCddEF5@L2N!81Q>+ENJF,.EaT*ni!2+?3kq,[@7nIG3WWb]aK
%nSoT&X9s\snp#Me6S)W\^=3_B>GR?GS86`^SmCniUOLuu_gkQUV:,tJc9QsBmAURO>-rHLjC0=,)4amb>4)+<]i>M'Co_^5%736Y
%l4&tr]aZPp<;@^m?!TY-e7N%(ZpP\#'IG#P+!\G@h/%.<5-l^S`6X/5)8OM'X/j)ggh"D,mr2U#\FU?0([$LD,ir<[$lLNk755.e
%h:7*u[_8'HRb\:QU1"66Dm'b@kUT+NmInp/4N&ILl9]e7U9&onNpmWc/r^Y\@5IYGa@sANS?E#`q=;-l</=hCJ/YU\E`U?fY@n3t
%hWM3Sr3`.f`-?#T3t!JA5<KO$15c\HY0]"'<O+VW'gJ;,Mr7k5#Wo`lG[ccM,NjkOP4(ZQgkUS-aruGBg^YG7Pf$F?1FF;?V2"RE
%^$"-qC8]uQ.T(L"H/6Z&1o%'$(B?!9GXj<k-qQ+(F&H%k78RHn$DJl696Vhg/0P@bAI*VgPJ>>4-7L2`pA6oQ)/La?o=flB,1l_&
%N8RoB_[/i^'JY0SoN'cd];q=H.4]B\+X8?S+m%J/%UCGto&OEW`>eR(YXUm2)iOhN:Cb4)2[@6,=^K8Lag:Y`(5&iNrN[$lhZ;He
%H(%0>`t8D5L\(<_rRt$fX?Bc^:%Y;)6I(lSPe2a'NJ_h920EoF9J*u@<p5lDL0Tcon1dk9F]U0#kF1\R/JJ:#Otu8kF<O^s.'a\8
%7Jj=9VsrX=\0D^0fWk>l5kG_/Ib^Bo\l1584YeiUF:9p"p$OBR>KD/E&aPGaCnua,mGa@!!<f)G-9/\:#hA=CDsGAR4cib.W`F*1
%j4D]h@2I/hhY/2PW9]/q,T?WtC)kK**+t+BVB#3>Y#_<frZ"2B/$4&@Y#rtLiZ1SNV1jD%+d_n<PN6hlL8o14Nf*bo\gA+(:&X/o
%[MiCM;l8Fi[XKU%9=[Vd/Og+TmZ%2BQ(LTjS8TYU8)qi.m!i]W5[&3DD<C4ED^orf0(1,l:J)C#8>:q/@K8_\.!M8N_I?[4U@j9m
%WL#sUF7t%eTZ+3[&Ic_+AhqU!CTm5-a`Oer,:n$=dHj]$UZjDLX'&-eFpZ7m&!;/,ZJ,3OYoZ)&<gHO6/jDq9#//M_i$#XL+hoL4
%C(SGk940b3AgGF7Ul"#G?Wh8$/ZV(lE43qTH*\G*4Ot,pR.f4UIcXcK8YONLBYq`EnC<;UCT/F@2SCjq2&`<M*)RO5?JK8r/O\pR
%oc<*ICt8SAJT<P\Z`jiWrNi#95e"7$4Mm!m[BtK1br9Kp1p;Ss;Jg!['G:C)XM6R!+N.l6K!6g+I.[F_<EJ^K-KrYIOXDp!&@#+X
%;HQMeq_2ao(-05\[0C)nIW6N%nB"U\Yrd*ME+YGLS7RMK/d&5\n*]bFe^71sWb#YtUb1&JI``H;KVL_f5bcFJ[Ts$pW&`EkkKi"8
%)rW=)g8K@c,'6)F!_hIQLG0rG^06b$NOf?lbWA>a(:;RcA\W.n8]Yb!A<Qs,5Fb0kP+-c'9BbaEm!cLA!-P>QNcGmn)^lcR?6YT8
%EaB+!+XkY:k."p1Pr+(:ASX9I[C[P<T*g6`HD8A9n'Bf&!QrHJO)Z90='3Wj$sBUV!(EddZd\dL2;So08g@Gah7e3B&u@f7B=dV<
%UW57TG=:$ip%a6a4#rMAG!$-h7:/B,NWjIt(lXR-pu\nWJggKm2`?_KE1[J;;>^/Iqq;PjU7%R`"O3KQe.E8nQ7Yf`KUBTue]Yjk
%12WmR87!)c.hJ\%@!dae#G]R`&Ptr-?4F.8kBMUqQBunh/WSU[qOf%cnk.:t3=E)c3*,S(50^#KB,bCPZ0\1T'`ciHD=4!jr#3X;
%&Y9"[FaIe"0)@$<&_[.,jQ:,Cm?H>T;RkuBoIQ#0O3hBiG5:JM+!/6=mNpXi33Q.q/1>ne>DA,/1=2gYQUuR@@%c1!B"e!.H.\_m
%RP?gFj]Gua1&T\X[sY9sjQ(;0E0/m]VX,I'YCo+9=J+9AZs:0EqrT=OVmsYd'-feK5mYNuGlO?n60uuI?D0Kpb%!fbQOWoE-ND4a
%D$O::3gYEM.r&>)<OV#a,sj)rOj*`HOQe/\a=Td<\D;<h)Z9#:IFc>Mh+9ZIdugM*V%T6m0Cg--"JZ8O^ZD2c#9.PF"$G`eKSCk"
%cZi;`W:1@O\*?;#Fh<3pcE`*c1)/ke+XoH'N#["J/NU`kEO/.h#W>h=Gh<bB+FOoW,2W$ZT(M,23OLIP.MG,mGgnBA7,ecH)[S;_
%?3]U#=^pKU5GF(S=[M)o-lcsA)W+/)b4!K^GdQouA?S)UCZ"](#coD>[h_4FaNZlf15oTBA-J=pgtK1fL]I=1*e^'Q6;p_SmYBK7
%>!gV/JTNROqsZNFP_&`F>I&G*7$1uHAr^gfYo-:m7CJP%1Za%Sl:r)'Pc`,NV_X<.inJQh5[/&@b*Nu1]D43*<4>!t4UVJ?=Ek'q
%-/gY)\$Pm,or-sDf?F!mnDJuk!dSL=KX1d*N+m*;Xqo!MAPV%M)u9s"-BU-JX76XPmchl^d<V3IA5@Br0XJ/QZ9N3+aVKSSC8WFk
%E)1-lQ#Z!F,Ol]H;?-;eo.e@!%Ujeao8iV.<t6f,h9R5-27Y#cCdVTseGhPbc@`4:/d,@P6)IaQ+f+-Bbg-HIYprkHmaJab8'00"
%*$dSJD5K!@5P1!)#\MD5>j-f;'IFBm9-)2)S')=+BLL"pbV*uE"eX9MnI"flo;2$[jK$3)a(br>,SE.7\BTe#QAfXI<.2nTO[g>C
%]s/;,+J"\Z;)"'6-RF#/"U/VT[-+;k-$<=`e:Q2l(#BLfq[2_n)=s<cmi'M76U'KMRYZ#5'L0q,]A[P?;33^MiBQ*%<&0h9k=4Rt
%2Jge*/9(47Y4]Jf4Cbfae1\V;,:b4G<*5W"le1ZEjCT]b?8:YO54Aiu`S457cD?WKH?h*dWX>HN'uI]GW)XF!^5ij3eWq2Eo<NNZ
%(F!0Q/Ybmu+ThIG01ud1Ls6%"94``9CU8cC/hPmlgbbR,/L,r.:Blg=FW2SJQ7&AL-re?Z94:;Rcdq80hf0cGR)6D.Zj,7N_^"WQ
%p"]6F&\aCm6gRW8:!ttb*JL*LP(Fj_Ull3S`(TX<AVu*aM*4U#6:b+XH5.nj&A'\UWDHp<L[$$/F&3Q^fC`[(+X>hmqo=)66>/t;
%r0Y$Nb=e6//!So2!,PP@q(28KJk7CJBh)%&Z%]0r#kcVJJ4DKo'22V`YcIo<&GEf3[eAZCYH$PXc0e3?Y$o=1#kK?F-7c/5o(KEJ
%N$gj5DW'tWj?Z0cKW3e8/N%(J3$.Y0qL6QHa2i8g;0C96^6QG+__LQp7WZW$04ckiVF3kV'f[3>MsA0FOG8oEh3)H_0JCZT3KJq'
%f9tc`E2i&JZ/bVbOM-K",ZXa__$)I79gL'^iRn1VhOF/kFsZ=&Itbe/>^H&P4L-t&O9<Ca)AZ;uBui!m"D$csfD_4pI@,Df/0N+K
%Oe+9PceEY&X,.S]2<UjXie$DrpFjW<cRVtWnJI=!)WP<!0lCHMBB)R5m@Lhq,22d22d9c5m.=%f-""U9],8FOSg"n+fcT@I@skp1
%kr[k3GElV+Fu3eS'-X+u]O1C'(#6VGAGk^"46;k"e`r-tBlT?oIcB8\TUcYL,?q7D0aXHA0+k,,39-lYn],/gf$XW,b91jc"NdP:
%.BgrA#'&5Eohu%&8o"iD;1!on"/WoMO7Fg]eAYIMk/%Oq-AnJg3%@<ZkBS^GRhpc5R=<F@#]C!\Do!:@.2Z<8B;A*T#3=aPq"Ljd
%q>rVDTg3VG_%p)uKLnI;%=El'Jd17YY==V(EX)=\'I5Djcc`ou:N;'pOX%Q0ASK*/TP8KV;EPEWS9Y#,$G_u^^*NKqHb]aJ92:`r
%.'U.[^\Ri@!^!A$+KeE`GY*&=W9G\D3\>Uoh"?&D+VJcPoNKMCfnY20p5S=3:1^=d(qUath:KmP('CAtQ$XoF1>]j.iCW9:7YSn3
%!.QZTActp-o+bTF#d[("\9Jh)%^(kB^Z.>JB_(O4R<lX>"8AtlC*nRp'UW5_0(.O/Dd+KtlE^eL<a"1bL(*D^jHcXnEigKP\5$^\
%+_XI+Fl[A4T[dViWic0Y$<?7e;',Pf+rPI@[K'pt1TcJh:ldop>d/@7Boga0X`;`<XD"l,M=*6^r/C*S1.umWB3_N'`8tEY@(FNH
%T!gp8a_aGM#"Y1k^P5lZK1)feGe7%<L7ktT"HbY*"h_1E,ElV%RktNC0ibAGn4<-N*7:5eB>0p]B&]p,1uT6:C$+sYG1'@+L;!tc
%!src[ha9,F*qIc?E:O>9@iW7*>+ajlf(-fTZ2,o)cC>pe!MdFiCGSFp]=MALD^%&gb)u#Mb#.Q^_,ZXH.hrSTrR0`PUiR712<44n
%1=:l_UMu'>((BT[n'%fhq`7"T9+!`p.:d.\TSP8E2Gdf2nR^0pITtRf8d(6u,lDtmOMDL%Y6@"5.ro%+$NljnM0)R?"O0oc'p:/*
%3*EZgCZ6;o7a>D(=9*iMHRN@2I!`'$CW,fG,=5*uk=@6?lg=Z6+N!Bn#]5G^74So)2+B\.4(SiOSm!Si3#,T.(YJs@\?$<@U_E'T
%TS^b5p=/*SehQRs_E2r3(623Rr8G?Z)3a1[B5tcF)*AKEL\s*Ki-1bdPCoSRjXpU/rXs4;5A?cjK>fUBBFJL&X$K'T)OHpQZ4@HE
%P#*_%)d6DglJMcdJ8iX6jlTZ8i0D")?^&2((m-b+r!Wc;4kRV_;o_S*h0qfO?R+PX=YN8_(^Su/k>4iM_/WB0j>]Y1I)R26JH1=>
%B<YpNU`X.4phVE:LUjA:q@otAK*B#K2m^Y%T$gXM7W5QS8J'a@/JJtFAld8ni"4<"\P'c]<)TR)KZ5CsNODj@JiQ49JOVt5T3;RO
%#;JEfbENoCCI>c-UbjZ<o,q`4`nuf]hu0Y0;7T%#!U;#H7(bOK")`sB`8J&'#9$[n1'B[%Keq)9PN:pZiK]1+e$k)/R5;iG"I[XP
%f)5%:SD**;+!+S&k>gBT.5QQcqHfQoI`j7_P2P+,X41^['`o8ie&-6-k:m1)0o6i%q<+@6dJO\V*Bn2'7jD23K4?E:5K"0G:UQr7
%n2.(ARH]#ADRle5U??racU=^A7P2X_F%>C!?d+rl^\[&UHJWks>0lJq!Yr&-8hL&)iK3qLj5>,FZD#EYH]t/O`%N"ZZA\=[,T.0[
%dUC-=eh@nR^WCDILWG^<?W!gm`j9c5ItDGMl^Y:.V]^>*dR;FK@+#K[(KN$IQlQT9YU^h^+2[u\O"Dlhn>D(PMK^*@)&s6)ne>W=
%[g:"#B"UbMIK!n:$uWPshI-arXWZklH<j)rhug"lJAT:\gHW,-*=W;(+*q6<i=>E38-NPD-*Dmq8LaV;h.I9VYVOiU3"qGGB4':4
%#/:YNmfe4@@!n?GaOL`e+R!eB'(OT$Pe-\iYn5fSoCX_1Jl,U.Q>Jk*"miXDQMnl91[F@`KsmGgk,*cTk66T2J;i7YPeqe\IX-Ne
%.91>G52qpNT*2jKSs`5G9*IsnFdeTO;.jc)HL55)/bF!"I,aO,NC@g+rJ$[YN5ocS\tnW`/Zp4j!5bFLZ%M$]]3(=eFgo>'+fh,3
%SP]<$p^DS1pX+`*V6g?\nRFYc,?SO@N&N(*oOHSK]Y[FigXSf!5q[6r%/*!MpW;?bqu+<RHTeO@.G2V<-H&Rd9FREA_U,[@O!1f@
%^md;f@mCesQKH93rn/^u^!F=$''dD/4Gmrr"hb^QVufa==DF5&MV,<#J/OZNo<"Y/GE-kB.P!Ksf0-Qh<KM?=)`.dN#'$[]S<6DF
%:u`d/8r,&R;aR#(8?u&>7bfPD5XD5"K-7"5N0)LgEB!-,RX.Paf<E&-C*t0L`&s8sC'JI4:7?caM_!FOR)lbuh_I&db\lIU%]2[]
%iW7^n`O_=P8-rTLR3+On]WJYGLM/j$hrjj;#MmQDQF';64UA=n.eDBi^4[Y83EbPU(&t.qXu/?&rP/rSog?gRh=336jR%dY[\Ws(
%0+1u^"66.f#,AG:0XVFT0ZK+ECD(ZGTrWOUiR4nA_<ZGUA)uB+c8316E+O"-'dR&WSJgrZTAp<<a!4gZX]3Uf!':2@:[1\Dg+USE
%b7^.u2nPCVMcRN*W7sc`GUN4niK\0>Q8)d!T'H&VU<uM,O9Ab790US!jT>`c3j8(V#R(+#s)+)pmK$oNZpGP<&H79sX\"3e7D9_q
%-,#Y`<Om.%"a4q!^(o+['rRl_#o27&b_%sB#'MO&mUqQYDekHOQ$X&^Pp4b5Ka>@CT&=ZHPXNl-O+M-`7%.>s,`):Y-D`kd.[Oph
%^9hp_;O^+ZEjqn<D43q\+^*GF^8.1Zi0L?\=.);ik`)gj@9$H;@7E$"#p>HQI2^qiB\HNIU1R&4Uo0+k<8NLS(N/U"G[e;B2B3B!
%I0)$t8^LohlhJ9N-lIT3h+k0Z2>EtG+o`e">d)_/=["K'0!ODIp;DlZZ+anjdY(o()Ou%.n@9,=!<779;?*)X1;rJgL.aZ1J?R=h
%(ZM.FQmm"/g`!kcp!]ZYQXM*H95"mQ`)Cct6t>f6'jnD;9$6(@m>5%F74Z:_7f9f"YCcs5lGeYL;"-J#"<cp,7ETid6uY3hT^`Qi
%';5[EZ5R:%*bRqZ%bEX9<RsBJ6Z_!Q.=\cT5A4E>[U:'ccIGrIOk=+!D1+;bA8;K3RM"9!MjBTj*nUdWii"1.q&/H`+/C6N?(1);
%.0=3Nq^d-)YK(A)nj>@s0QY#s]FMt0!Anpf$F3DT8J;l?1^M(H/XmWFWsu?s2E1J;F/lg$>Y7sh&rB48'ViP8444Z3RV.d;pQ%L6
%FKLg3$#*23/8E<0#U%68".VMm,'`AXb!1;$7Z>>C/[8$.jYe?sc`[=cB]i#Z$&-iZ=KM]VE6(!r[uPn8b<DY`KPe_t1,orhoF2a;
%mEN\9Xj/)6YNcMNf@1T&rYl*@/&cW6LL8]R^0)[_[eGST'._#2G.J]M!<aia`h7H"^U&Lj'.LcdahM57bkWpN#OtmnP.i:d<C8]^
%OZO+k9H7+l(IGpi<ln[7B`#SYhN3K&&iToGmGp"%[pTn'.pbHV2M95J#(I(E2b_G7piq?&amO)mglY]p!_s1Q0oU%nZ/B/3j)k9r
%h"%0RH<eW;l4p$&^HR@0P1<f#TOg,N0,i\)UPE$45nS(HYqSna;O#W+j(o@N;)LLKd,'n(68_ZX_9L0?mB%e[gn!!U-W\\>7XqO7
%Ok5*i4L)+rm\P:OZ=ou@27GN=S"b?69-)PHSjStmp:k./6fu_*ZpC1122#3u_'u%GYAcqbVYZ$DNOLEXPt[!aMU3%`*hC+fqsK46
%#u`G_SOh#>\,o+pPq*04Z@.CQ:AiEBr*J<]&$>_D4i%qmpU&HJr?Am>VeZE2.UiB9HmM"_Fd>9(gU8@r!,W;p"Si=d)[8=2I>.X[
%-L6reATZ0/HCHS=qHqdYiJan%#AKu9)De5&i/?uBs2:u=NEB]PUK@W7ruQKg9IM%LI.at74A-N,,$?';"WIB6)2p%-/h&#mJmj4M
%J?3D6HTANkW(k]u%+<TQ5qUTki4JADL^'dbF">:t=bBDS9kLDS3H70B8f*#<4EON?`SD1i-8Q0IbD9hR,F,-`cKdoU4VMCM`OcQW
%d#K$RC!]hk_/m(l^',Q&4Hb'6lYQ3]QR@839qTYS>aeSKVI-/k/BY<HIP=QF$8eP-lI?!ZaiDHQ&`kM'SM1TH'me4EC%s8`38FYC
%+94>X*$^2?U2O\YlbQS1f[01eifCW7-#kq"4LEO>)1]aZo!a++P;iGJG[TPL>ZlfF@qa"<nIt>Qag,S4,H/'j.IRt3S[@<?%;B%6
%QI\IjL?8ia'Jmi.]?S^UP=COHX=^4L=,fX[6cc%@`jE$&2!g@!p_4>F2(0(7EE,@f-odtd<BeXA&FUt\=tJNhTSp2C75o0el5%;]
%SaK]e+lllXr*-UZCf*DXEiR=Yg[4[+8E%SW-p-Z)])@sdiVn^Ad[po>Tu\\'CWQ-4#:V0qJQULVLPDi/-/Ro\@RFfT[+&rDLA-sh
%)?#9/IML#1,PEhUZD'C"BZGq4[]BNo!rLZ1OnBHhcCm#HiZ1:67OXHsqEaU=+!#S]1lV(q]:EQpq't>j"J92,9f-\8LFWon+fF:#
%oq+Pfbm#7a`Mc.tCdPbb9e-2M$kX/*b'3IknDdf3I9id7rJ.n'?t^Zo[_pe7ZMf<NDTPi+!,L>Y1fs9]9:$kA9i#IqM-q"^P#SX<
%L)pi![DR*^g/%Q#!0@rp^!+'`K5VTflLNBHhl#1<qR&_b#K5RG_kjq8gJ7]\>LD"7Yp5kr@.4d6K_V\"h0tqkFT]uu="naA=b^_[
%?,-A+Lj-S:d7)#UN(eR&bn]7Ll``H6M*cIg_a%Vqe`-`)IAQ_Q/2Nk?Bk-_"^B"AN9.ZVe#G^r34YZLJ2+dnff!g3+p6Ma?b(&a;
%$7+!Ap:ek_Jl,<>Yh^7"+F`_bERX[uO'mm;dGsr>;]a%7eT91),$Oa:r5F/5L2]&cMgU]_?$_QmfZoA8GkV"H_s7$<3$:[V?,%6q
%G5&g48m"1DTnNr_b?tT>=68B,!>"/4p,&<Q'?ZGR@/S6K%*TY]rhe6@fj6o$$Im9;I-rsA3q@c(@:=ApXMA5$0jWlTF#Wg.9l>]8
%n4=QK/N,ECj'qo6d_W(]i+)KL/f!8N.@t1uQ/\2e8';>PFS]c_*F">(H0ETpn*8S<B$Wss7Em@>cJOYor.VT;\@?;.btsMNa1D\E
%_%DF>H7V]jdf-\eP@XXXHOZ)+*k@GpE9K[S3M[MfN$He)\`h,6_U*XI4(eb2n]VW`!S`i65mOg88_+l?,S+\Of[,@ijne.E5[4F[
%``!.a:*-5G(1nYnX&7=%FToTUB;ju/BF(L2L[HqT!t*/5IDKj@$UTq6rnX\7F1L2(0#-):=4G$B;X!N(VE&-3-bHPL'LI\pb3hdF
%aEY%5AQ*0O`=WtpiV=P-!kbr=BnjX>$5L"G),5SHXFg2P5ITgZRK?B@V=bIR4S5JBo4Mmr3haEF-EB"ZC/IlS4(:Qm-rO9ALRjsK
%>HJI:b`/43Mh5"k"Eg83EpB`G6N^+r<Odt)b&2oSVhZ;F'GATAk=MO)p/>St_cgc!2nf'*a#T:5>W.:UCjB)?ppibMM",H)?k5r%
%#@KF^DWuIir,,!K-\Ei!VSNL@-<gc^"LqEe6GpmR2,c+64e%M#1UOQq;*r1_5K('5T)nnSW\ki=5rpN0n_![AOG^GoC[\m'hYhlC
%EXd=qg0.OqAEl#!FrVn/I7PJ(^/]KsND8*^0Z`hq<(Ea]Te"&+S2?bb?`Q%6B-hG^$Ha,n^\J0*VasV0WS?]o$d7q,;f*<U9u(/@
%0AkB!/I[p6`qG;AW#C7om>REK:A>^Lc)8ft$(n?rm67.'^.slQRU2F4?Q'>!-BN;gBhlG"Gq*-@Qq7Z#]fl$&0HfQ@Ma31p(Ppk)
%0n/b"ZWiK)Be=>KLj?1^P0\"\/jAop;b%E"gt^;U,JdO%:"-G^K\YZpEY=O-TI9G+DIg'0@U_:S[/LV</dcMuB5nUQ?pZnC$J9`\
%-;FF2R4o%s2SEG4em#j./NN<K$KE?N3%3fme47i1\XS!tb4+YobB[0L&cKY`TmD@I6H7(4l,QG=m)_e),]3e@S)bYJSr3c^M9!fE
%lc(ftCj,1I#c(Z.r(1pE%c+I$lNkPPmqaG&pucH`es:#O$e-ga*@NFQk)NJZ_ORJ08L:gt/n#b):Jf:I(B1eX+-CVqCj`n4k"qru
%"`D#FO0:mD.9Z8B^P<r*6F'qk?=(n80&/MUGV:aFE]QL=3#$sLG#Ymq'nbi+3LQ/i]E8&C(_8,>BJU:*5Ia\4D/G]q%hgYSB4s?k
%e8U.Tcrar%5L*@@T$qQN.:\.O*Zm^Z]0X-R\3_!9N1O7aldW9#SC1GfY>E0[_4a?T-Od0DbLS`6am-Bb1fjPF$5B(B]\g6V->5Kk
%@e1AHr*SPkd,*C$CdNOQM"6Ubk[l3HUC8VF%i*Rf99`rdk%B#Yq;If5#TGU,=0Jp\+jlsT@M-%aB\0K\@$J_?D/;ZMk#$b%fD@YQ
%LW!!Me(hUXN]?h:ERI<'QQ!F%b"=7l<Yc,Y#=87MS@k1rmUlP^lbrWSjQW1`VHd%*\:u7#mN8J7dP]!6g%"O?T!qQd.Ma_<K5%?h
%cRXrQ,FfR3?@nIH&+nb\(&uHk05RXU*jo^dV`bJf<L/te!:.'!-l,(Z$,!@5J4*MQTm<k[):g:K3ok33p<+APVqdsD)8H;bgCY)6
%52\+h;i<>':]$=+15k*sMT&B:'`*#E#Y4Hra`cUkTl-^gH39p?Qr*iQ0j?Jn]LUFY/6uuCaH9oP:4oQ-,lPWDrf@cOQ3=N7Z8hZc
%<N6NL?(dq*<20@f#GrGNCAYM_(X?=75L+[cMmucKLI.G87&mNuFa(8>K!>j1CpK=j#f`RCM95p;cd'sa065N>pL$W/[b$R>A^g0e
%3'M-W=S70^e:0*ML"Ih0(Hj[4@gH.f4/hF0:e8KJ2>eJ6JZ7!Prh>oiVD7TXi8P9qIr7<6\AIh,p,1u/r%'!9bkR0r4(0pQ[0W5)
%$acBg\HoX>"<q`&;,N3Y:An)*:6-\.#2cl\#rk9XUs;bWG'o`H,#da/`!`mr!'YYAmH:k\-f.bB(`g;eiW)*O]5=q/0ho<\X/&&5
%P#-UN**V6L5CSjLVV4uFjU.P5'J,,L`&N$6\"0G2["`@%X_TfXV5fB?[q.:HEnE9lrYb$W04D(+RSa#)/kk:0b@f9M;bD!T,cn7M
%fSg5&a<P%:8h>8qB'YlqaC3Rfi[1+peXhL94S(2MZuuemg$Lm;_72`l)YI>i=,g3/D$cjtZpI%3[1WUHa?5e4Qiple2n6\i#31AV
%F>(V^gtCes;nCr^^:3U47JhR[_-\lLG2Ek,^Z0J?)S;5K+T.4BN!NVCL;CY*hB(%efu8A[$b/_Na%G+<Km<5Hob,Xs\5Q,[.@Sm&
%4CNoNBF%E&T4(Q0U*Qc*1<5>V7ehbM]r7a/*SoT>0j")EB^<YjclpLZU^49AIM<cVM8@W1$2`qd1aT/=T7@O).G["k-D^-gT[:Bq
%\s86%?-=&:U:2R(_%m0?NXZ8O6]YW=Sh5R>Ddb)K<Eu1*"Dski-l1DSChf"5o6:mG4XJ%Jn6Pm+1KGegJb=F''Z5P6(,St.9mdhX
%(r"ESQ06^sI0]CQ=7Cn0*:A!VP/auCijT!ZiN_YSCgfteEZ']hrUgO;q%c2$Xlqb@%bfq4(/'-A,hjjK%dWsYMNfrf^M"a7G<t_o
%dQL&oNt:%38T`=Z^>IV>iJ!sR"j,.VLf#"u-<$!OW7b#<!mi&&K(fgt0st-W([s3_EK'U`/9hrkbCjCE-No8[+&rL)ig=K\_!fo\
%!:/6Y\I&^p+QQcc"\"$@ZJ5SVDJ;?=5Up%IoD%Nm+W`=k+[frGYCF4Yk]qmHAVK'.DfLI`XpimY_LrH9lH;rQFhYJFrNEDTW%'85
%!?ua=n3@#78(-]73k.MaUGggc3\Vi/MG3h3,S8mlI/qeH^d%nhkX'4;(=:N!7(%[WX0TGe#Y8chbZ/J%1?4coI%\p61",MN+:*UL
%!^XSi'H_IOOpD];*T[+ua(&8,!j9"U5lN"I[h5F>d%qUN"A+OT.^<i'q7UZO[\ciSaUGlb9T$3=T1I`LI-.8,pLdQai".K&ng!2.
%JFYkIZ7"PX&:Il7Ua8PA!1V7n=lmjmCi.$2Mn(OAFGCPe^BnQQ=6&VD%k[91iYWfZ[[B,f4KTdZ$:bC0n0p'4.j%oa?5_Pq``2Bn
%H!KCIM:DV,9o=iRD?N&(g]^1nUoaI5_PRO)ZE!Y'`%f@aW$Qu`Q'Vbd`PdepZ'5k<B`q::cA/VjmJG1R^FRT&>F5P"Ts:VT:JoGe
%^RX7Q=QGhG":-(OnLa1lGRaKZ%Y<7(P,cqCLX.G1euh-EB9?eks'V&%JX36[K&T\YMF!k*.`.\Y8hUHG:"Xo76_6QAITt/-aA&#o
%;lqbdWA>t';9L8n!\(rk&=JrLGYE*L7VgTOZF,PF5jH.UWUpW%TVY(dKo6e6RF@N24=`o.;T&uV;b:K\g9X66jdK.KoN5r(b[;CC
%J#QbZrOL4YM<l`tJMeHt:?au5]F!r5;',IK>Zj%*&[_fUrnLMO$S*_=Zeo622>%@@g0TDHj,FU9nINC@2ZMh'qHo='G<beYJ+t&l
%)rGUL+/>[*]7Y@1C<'R)6;51dIHa>'36l!*q\!sJ3hKR\p!,42k0!N8KG1b'gq+her<+\Yo`[\qff)(o[?>t5_uYd]F>3Cu.VD;(
%C/H&hEl4+POk'OZ3mambS;<7,!k=l4q6jYsDd1F+)^*pp^5.g6W^n9=5.[o$XqG]nWHlMoHTr,&D\8e<K`;`*<P#sp,%<5Yg\N:2
%<[r_g##RCGQ9bol(;_Rm1QgY!*qC+<[o$sg?31<$SfiTPc0G,dqMYEtF%h:fG>?Dk-O@YJ:aX.uG6GCTh>Fp$qil+N%R]eH^;CqU
%-[;A\9-kF(Lrr*D#ZgM8"9")&UnUo[h[fo'?8KI-No2X$pkBfBYAf2Sk4E)Kk6Wo1'eS[=S].6@[qeST>$$6]OI%p`pN-p*(6.]T
%@H#S7^L_F0]5]dd"4RL^]F`_VcFf`X7"U:Y!jU]B0a'ST+Rf[rh2emV&>#KfGf,CK(&,PV+qm,J48MMF6_Ipd]X]WX(F%N(9sg6c
%:AkLIZ106`U9h#<c<fl-LN4@1m[WPFbtZ0C7&X'W*&ha9#G`4B8XdR@=@-8Ea&lU^e]&9#A)a@K*d;!l0gDkU,YF/ALfu-,lO#3l
%C8l.A`*j0o\1@[O\qP)kKIo)p-#T%3-8'?*f(pQ$33oEll'4ORj_3/J\##G\S2HJC\mnJF\:Y0A4&l^+.k_NpAagS2nJs;*]drf^
%5Gj[hJ>&N=25>m=.Go!if>Sj$^:[+a84&T1ZX.hF)*Fo[c==SbL6BJC=J6fk&-+,XdbCDZK^0-E(d8Wsa6+N(9>3#L6^j(lI'brp
%47[@NnPuK>@uTjG[?k[p<"&5#I('#(;_OpY4'jp%003*1fTAH\M=G2u2KtF++3/#E!#6Ip8S$1\be^u67Ph1L+6kUb5UbikH;Tdb
%)!&!JQWlrXGQqBI*BTcHa:ZMOZf5k0JF!@Ik&$+^j6dms-(n8#B&DR37.BEN-4ps7j^=D:Im56l_mme%$9!<'&?Y\IE2qFuCADju
%chOfrhbdDCkD3L8Kr,f[^9bE2NO=#CD!1-OT(;Dp>n(%*OZM'/k-#gK4J347hiAA$K$X!S>U]NQ&Ya^YQGYlFS+-t+CaFis'JW,N
%fHXZghrdRSm<0Y:(G15"`3GC$Xqb?t>e="7[\L+B:UjEc4CKs4%A;E>f'F$?-/"W+p<ab;[n@;\:"=5mWihX(JB25\0TCdsK)#`e
%#gYZ8S2RFemXE2<e\+=9Yb#f)/jN<^=HE`cb>7ft=C))d1\(\VcT;R,1p&(N?\-8q)'>2DQG94=UYk#97bj_KZ`\h1A+S-YM83P+
%ZA"AaF"^lei#]GAc`14n^so[\h(pquc9om4d7pKailGEYCRSEL'`2ht\QV5].Cd$adhY,kS[DJML#fhs],'i#,B+TjWMOo6DkT5;
%+_JSJHMO5_G^KgI7e^#e+O`IiSZZmfH^LV7.EkK@",qjMZ0AM!9n^%=Gt.oYR(YsiTWno,J9$(pbnLGE^em%>2'XRDHX,C(#*Eu5
%(06`";gL7eaJld]/F@2YEt%4tIFop'/Xn7%2XbZ2=9N]]c%NqCYOFZSP<Km6.*.uDdmK]jF^MWI!55W;SbkU(iGSN2UA4pnp]M\.
%M^X\q=Ueo:4g.\VaFBs>`"1fNs0Y.[jp<UUGrk4Jo3(A(aUnm&TmH#qY,'?FL1R/<m\J88I>jU:'.@+:;W15:Gi'2$qEFOb.N0FC
%aA4"M.pIDk.=eRcfNb97eFh5K/g:_f*MXagG'Su0q$`@`+,!VC,n%]T*-5u=`q,ZF,'^U@c%&)POSX2Y!WiEW<bEV11XhPJngnqE
%h1c$_%NXe[FO8SR2O60d?@Xl54PfMZ-58Xq-]`LmAjL7Pg-f8b+`s.hi,C1:+S&M=QM@b1hU0$B+Qd/`"j[%cN)ZR`9IERSRLFlj
%&@.]]bX!-US-I"2&_cZ5co,:BjfgfNp8b-(r1u63^gA[%Lk#l^nN]V>e`*bi2%IH,'S,hbL]*ioha0OM6kUK?'QAg*#3_+Z*9S1D
%e,`0$<4sT.N?q*32n`WNWHRO4!*JYPI]L?U`CBL0e#?W>E$RKd><"c^3?p#.=<a1,W2NbMR(@D5lH]<_g*Aepf&<>`JW75BjE9an
%mKU;s?X0t8BRSOka:$pGS%m]H\!9B!MgT=T[+X,!6-%bu<5$T:PQ>&N3Woa%H%pF+2E'Shm@GG.XFi07?+CfO0V_qYEec'1$ef_I
%;]uG;MmS'"0'h=nAXP")1#Z:#MLWs9c>psB]2eu2L*5+#8/lcC169'kpcWNpSDZaa8=_p[f2\KESo>3iEHt$Q!)>@>4p,l-@Ibq,
%dLK#\R($3%$4]@MQ^#MI/+h3g;5[NGmL!gdQ%:I,+t94aU$8ZuVG3f3\$Q:t,DXbF<khpYbakja'DVtkR/T:+IZ`-RSkQPEO4M&"
%X3KpTG4`PQ>/OqDXtrKI;CMUV"AJFI<m=/lPBt_cT%!tfB?1T7/ZC6I6ZoM#eN@`;S?F_I#P(L:iSXd=@0`%b#S-Hr)PH:ehA$Yh
%f6dbARmcjOF#T8ufpStM.4he)eu6DdkWQ+'7=F,k(fM@h:*c*q$io:U7Ml]Q.\He2;bBdjBoXth'5CcP&'^l:)UCrgB&I<>Q5"V+
%.03OS.Z4AUgPcWr/l^7:cl,8oP`O4._LmMFq8ZSod1Dr8,q+#I;:00^=;)H)2TD)A&5Oe'ZQF%4S5C7o%UVA0;O`5W/)c#:1&(=$
%$0QSc:%Uj0!gCPM=EHOm'e":R'j'^`oW,$u%LahDF:jp\3EDoCaKQg.KiPfg!8QC_G@rQ\QAQ84bZ*WD)+j]na&sb)4/_k\m'GI5
%)3b.\%NO"(DoQ@R?Bu3[?Zo(7@7GBtpTRb#@IPgT!?I]PgqC4$W^eT!p#*.:e0l_?4=/;D!`c+,ZEp]Vj_pVoZ72C'?la^O@Z`=u
%!p[@$c3QYTSaf8t+N",hpi1X?W"(G4P*<D?=3U,;Q5m$3`QT0pr="2J2Z:skXM>]!Qo)i#$T]8&L%l)f8/>X6Q&n,"<B%fD%3Gfb
%Bo@(B]t#1.6Zs[mr1lI>D]M[YE'X:?V9V#u$!(Pi(JSKl;>*9=S.A^e<ZUYk[p.c%WScY0J#s>aL@uY;,8Q&?2a>fM%Ik#;9,<$c
%O?j@Y=$1(k&fJZE72@P)JJDl5n;0M:hlsnNp-TS.-icE!=3T!%^P-c3Ro*H\OQKACM!p='S"OFLSn];Z#!SK!.?IY5,i_UY2i<i5
%5G[d-O,VWSg::r?V,<Kj1HSY)VBpn%-@QELQagG:44gLM;8,-*7cL?`XPLM9ac4>FO?]NqRi6KFism#CfC%Ii+b?i_P'+0*XKO=b
%h2(0eRME8Q3jAH%:@hM*gGVXj3F.Yh]rG_4;+!\m%E<C>^dG,ekOdQd->S#Grb)'[Z(&>s;\UL4<2n-&.37Gt8S]n20SDg0f!ATY
%28\E"/^j6jrN(O]7Kis(k%+UDee4D'(>/sms/(et<m8GG&dgt#p@g/']qVp/V%)\M#uHB7\pTL-M+gHST9,[cc_s96B.UKmjU&%r
%ATBrC..k"On+L;</J`T,e>-A*cR/[L.DKC*Q'3:BQ[>YYmAq?44Y@P4Bt@#U=KI_O:oF(B<J6XW:oQ'$G;qpVpk<HTV%==?S`"W=
%h?`78\<#>f=36%HJ0K5:I"d(oJ.AaX5iqX>3#iN0_a=tcTfudk^H':\4HlrDJ7d$p9I,UXGYu"ujm*NI>;]b[pMBl>WgUiFZb=3:
%o)BQ#0?L^rH8F[[GhLPoEkI<nO1Y]B.7De3$iJ]Bo69URFYGSPB%'0/&N^@-ZECU$YVeYSkgUaj=34l:qrh4AVC94LkGh&sX\i#d
%^]RPQ>#r2E50O)Qh6%]*0@gi<N_+9T-d-?3">kTG5TlQ94Z7o>4mpeI')7o<]-iVrenR/ST$90Neg_"t/WusJX\63*Z[S,]eil-H
%IL4>o\$4=[jjLMgh<%W^oUuYc0P#INY4EY\oT^qmoXT)eY=<M'=u%fAA?/<2SDYHk1gLhNWBHC[aUOCgo)/7&GSBH^>IuFCl+A*:
%XLPcY^N<J#rMX@PBS;V3L*MkdhEb(LFcjLZg;/U,W1A_iY?k9`LtYb_&DLLXmN%BDJD^oo4]JtXg&WeUSZWIRY$>H-T.\C03V!J/
%6(CpE;'-7&1kG.nP<@GlMh'4?jLbFY^'\3KiS92iJ5hCU1K`IR)aM;ef,_q7inrFc0G7M$/IM40.cX\c*W_882GE`+!Y_fuT_cKd
%S:[116]UIt+g%ibUI\Os;Z\$)gk?C=Z5?!+i3UF-=,\],CuNgqc$FbP,827DM7P/G;"k5u>WIRk(bZdm]#Fu4pW%.JD-=(FoSX7^
%'^*:P3P]s"84rC!:?QaScVtsc?o`;c6]eZ0N"7`TYHM-8!*jLr":XLN2g'a-nlEiO*GY7_3$S#VHn\MO!p!9WQmXbG!e?cB+UmQe
%niS5g[*M+-9N<i5C"A8#S#UfN?)*J8QOSDIdk)Fu5i(%tEMVZN]'U0G_<u$R.:FZsVe]m;CqAW^i:P=]Jat=r/l_\sCs,TFmGY0W
%i]8l:J%a0Yl*[6JnO7#jct0q/gijfEoa%jfB01^N,TLsFVIk16&qsT)7pnUKg@Ws+rQEI@Thr2YisI=aEp.\sj:F.%&SFrcLHt1\
%Tnc,OoQO2jZ<]#HX!bWV>O_.KBTnD0XhL.@8rQXL`YdhU>&+:U>uG*IR]$pV.G_h.EtX^V-J=>r[C7Pj1UeYL<Tnf'!2s$@ha37o
%R;8;#8H;W"-?/:5OlI=ZjqJS$OTsHoC"_+C\/f5ZR3fk4[?f"W2&@GLU@_AgYNGt(='!'-5X8/#K&71kbN:F2TM/D/Q-QUPTkJf7
%TmYm?'\o^4i^=is5Xs.%n:().$nnd?o^ut!`]hP1`=mjJ=N+HZm:0SXe!8%4,u7mG[6-iomi+2R1+U=<m^l!U=oAc(W=qbX>E@b,
%;,/%s:j",ar!rE+h5Gc%98;:CT#<nYrp*T&$"SsaOH&B>6h0:c--d!@bJB6>8*4jF[46=(,i>dR8/,W2"gk;jO!lnDUY2Q',#S^`
%+Ki4T1hTG)ZQU)19IU.38ePC.<%0A#V$"P:;#ma3LII&!(EdA/`NX!EW"+,4/R*h?R4/ur#Lr=c+$YO.%n%#MSfef>ld?DEd;QSt
%k^lOfajL2Vmb&BpjZ7"b,NW>1I/j"aeSA<bPCKW(S"`G1?EpVr.2mZ9jPS9I?EkN+Jk*KV"OE#WgiiWh_UJ&_7V2'8NbWeKWpmF:
%XY&p$-r>lb\'(ML4qC(L[;CmkL/qImq#aD"Eq$,RO74TQ^Of6rlCgVa\$'b(`b!p2>E1f(M$fb\Eo4H;=^^Ub>dBj8cs!`e?3MXC
%fE!Dek/Tm!4V*M>ci-HY#kdncIf^&Em()a:"`pR4fF07taUFmki*Rb_mYSYQFh+grA;HoAlB+\\?GXEPfJooIKOED*JJ9UL=*P*:
%r*s,"PU,u4p&_YYPnNtKrB%#R'4]p%mc1O=RJTGgTW\i0_sm'=B\>d_d]-+88Ae45#-.+d`nHCehaFZj4j^GWHBR&:qRbC9f^P#H
%!a]6/\4j9'7n<5]q9`O.?6o5+7SV''F&[a>#!srp238`1O##UdA/$Lr3qN+U5UOm(gc7+'fJSmunXUcb$E^*<jV;mA"X=AZ;+<+j
%lLT!ccV+s=EETJj)@MLd#UKrb\Nr6((^^j\OMcm>9Z:Y$b]s37+pmAuJI"r"Y&HTTFMNS%'=lE0A>r:7:ee&tV,_raR/`P68D)XQ
%7HKOheo8+[a!LUN6'fYmU$jLm"@Wr\9)3imj,_fN4a%;?S[*9sht+MdMP7(I\HIr[nJZ*F6Hod"DQVC18n5S2d8Frj0\78U02EKW
%ODjL,-#Cp>I.r*Z;tPAQ5>'e)iOktm23jDT0r&sm&SY=NKnt_f2k3du>1<:UC?Lc&4:@-F_2dm@"72",RDMQ!O+2O'<lMC;(Jq-!
%H1Dqu0$u;cKB[h:C,^>Der@ZUg[+*&8SiDdOJ0g*;.Fu!ELRTm)f=5n9+kZ_#@d2B=^d,lR?$kW>N,PiFHK'S]l-1(cjmiQ`+b3'
%1LGaR2U3Y6.\bS4K.gN9GS1HZ2l&FqYp-]*Yo#UPqo.cUE?:F8kI<\FcQh>#MNF,A+?Q\3Fet6#hLJ:m@,&"XSC*n#'.)!,>>&*n
%et<=`aoer;USGmT+=81'#A>m:31%2+1HrFY2,s?=4`B1\C@iqW8$4`284jC8g6-CE)8W40_g)(LrWmo/G4i7gV1=GsE4kB7N:sN`
%f0@<5<bn\?\h2SCc4EX%dG-bZ]cc2=@^EsJ;KfR5oK\+hU&`M3I0;T@+fb0NA?DthB8J.N?;"O;fk-L_6Z!d]glHQG2):<RM2C20
%WGl22gU4D"![D>]loC6*E7$h@ND,m!%J(K9Ack5YA&/ntAfF!-a1LP1]*CF/[6mDC.U]&1kq*HpTYd\Op,CMC^I1T&C'\XBAjdOO
%eRPM]1.IQIm<]+oY:IEf;HeYH6IJ^t?.Gh[OBH+T_,J!)HGX`U^kt:!lF;Kgr/f8B-p[@N6#H?e53TP$b'p:J]2I4I'qjkP!PAd9
%a9g?^PN"BM.RBfP4$tiYnIg(Dp5'L=_E\.Gi*nOcfu$cu8sar&8#k@ZpUWMAQ3@<M_)+PDO_La&I%*$d0.PQ@IdB#FO4:Oe-ac7g
%OHuD#(UiSJ^&&LFL*Z==*$Ipr(2FjJ)Wn7hb'qkarZPsCeGF]HkU3L2gRf]JDA:IlnF6@;>/%tP]1^!V"`gk";HoS)pr6Kq5QIYV
%*nUeIYiWE<GR:)mhO!jHQ[Xl[0bn0%\PLo#(=>*)F%WZ'BUn;;_bnVSUj+=bfHN)A/cLt53O"$CA,qVBa?RCReM;BdXHV"V.:*fk
%/n4L(&fumj/@A;7LH_J2LVnEh3Lrh;-n:+:b.=<i:ofg3WU[AuhPQH^U%30KZCquYaZ2cT]o;;e,B]=\2"kY"Vsk^r]XWs/_D6RO
%-d3q3]nrOpPB'l&\>j8(k!u1a*]Q_[Q1S8@%jT`\am?<rO>dl7!]e4MVe+O-^;q=4lk"_SUXs]OUqeo-m8l:K7@FL8#&-ZlS1[5R
%5/99`SV*)%+b<WC4j$KMDC1thDm&B&W)H4NAI)Aea=MmHR)]EeG:CQS7gF.XEskNE6cFh4FoCAeO7#Qj!eq[n?Qt0FVB!HlgYB'9
%&e[Wcf?M0o6kPjl8DrBV(+TGgO20n6HU@ZuC,G$<M.1<ITMg;51n\Pd":+@8obD\o[nrMaUtNAi1),f7]:RX^0fUWh'gdAfAX*qi
%i>JU43*)bqh[k"kb?_F"IBm@7k*:OdU3/]jLcBj.UZ[\VPaf@uh''eIo%5D!KKI-XG\cG?]\"N=$(XYbKNKi,`%,WMP'sk'Q/?"o
%M(f4KL8.W^p?6Nj%CfIs-hNEGaj@!:[)ClifQ0ntOuTL+rTC+ub\TaO/#Y(U0,u@Dffo2$XK"KpKF5COP%7i4kGKUqa:QHd]%6I!
%`A0NN6sl.0bS,X:q&g[rFe<,PmZDZN61oWK]##mN;ZIhS.[1oPK?0+(rp)!?::4Oj7bKBe'l\&ii^fD!,cYjUPP+OE1o-U32NHh]
%_blEYi6pPq(?/hcXA9!/q+$-O7S:qp25>c91R7$='@N*7ie#El+gkHj%SAcbSZ<l1IOb&B!T5tK!/lHarHMI#P8.Wk>Suj,gO"W(
%)0J&H;b<!k!A8_tmW=_GDbG+4VMNcaFMk$Oa3T'Zr+!1\'i_We?sRc+i$7aiX<T?E6<*d3-Y5GL$@>1L\qITg^(R!Y&%\7F-tu0I
%EYnchM)9t>iN?*6#7Jd_26:I"`aNEg,ne7I9_RC[fA?\oVFrta-34Dtqo4GqVSXl_1o03hPEgeY<DsAt`N$3m(k;CNB^<4/#F[Vr
%>h#h*qV\Xl>fBo\]jEF+<;GZKI@Dn:<;>QBd!Y5_h/r14Q@*Y""s5(fbRWJK/+7lQ[$]+oXgB,/rNtdR+jnXD,GbhR^$89.J`I!G
%#l,Y?csf.Bdq_,6F1ju@<=Zj?<Bb)BcYGUKm`"s3C*G"JX'gI`<o;a6W!\]+JJg%@nDOlY\sl[F8[6Yl)Xh[d06rZ'/UE=tMKl@#
%[o=HFJT+?Ub;1<sa[4OL)]@&KHoMP,(_"7s;XEQNj8)=,U(D(Kh[R"o^%>aD)XbVH#a&*)0#*!^E/c?g4EH9INBp4b0_7[05@g29
%pq&0Z+ergg!Q%Z\KrmZnP=18;EK;K.T#o$@;*EmSPqp#%WUY<tC:kJ<BrQVSp,#^hW5;DaW.u6ac,p2Rjl5Y/5C)h3f;j"ufV!bi
%k"95'b.Sl[]_S<*5\A9SM=T4kK9bguO(WKK)*B!^MCJp2q-mCTNtnIg[6Kpo&fnj-["OR9.ZC:Y-mo:a[)M&aRk%n\89qcGi)Gei
%$`onT5`!b$<V\U51e<-YUV[G'm\UX!b$bVO+Y66h@')\XgQM_T&MZhND/@<^:\d0>T*(52MoD^\I\n;\0@WX'Q]q^sfNq1(\4VWX
%k8$T1c4_J>/r\WSP18Mu5T7n6W_5sZd7"I$B;0ZjO'Fkbcpj]\.CLHsgH,+K8RP]fg`Q.GL6daR0*h1m-@M/P%cbX06S20sP2L]t
%[IdI>ahpkW_HRf/F?naGe8RWi2:&J.pnW78%-QR<-GA;BV-'W/0P_T>Jjo^b9%__'N3LXTkSUNCNH]TBA3PI,'Hl3,Uj[TEV`:<B
%e;ukHC42;3.V^&,eIgc*QASJ(T7h*rgaM9@S7W=7njWL?=1VL@o#05"NF2+i@=i$,$2>O,&9+BtcQ_fCM%aR+T[fSE]JFB!DN!F>
%jnDtY3E-S[S6hm!MPGobqdf3Bl6/Cjc7;!W3a0S%9'f"^T"a<7rU-'F3+.'6WfMS%E1!!:i&P8]jj7u8qeT,iA8![8loJ']Baq5E
%QLci0_Lo1`BnXUBQGgZaEhm_R785r[`=9(>E@"_9&=q@-@NgK,'M&YYD!#T.`$BKA!udr!j9SP?7t<m;$+M;cID4@m(ajp59+CnP
%flH=1Pr+Z7BI-u+_F\WTK40T3K=c!5.dEX#<N";DTCGR&B!nO6+-N?Ni=Lc=VpjIX'),s+ToGlsQ`*Me%@DCJ$5$"Y;>tg>nZ-,b
%<S9X=mNKZ20#cV7GZ]Dg=>g-LmP+I1p<T\_0$7C**pef/*U>0"TZS#<>cU'G%)lLajQG#4nll!>ZdZS>ZT+jtFu;Q(pt4uA^6Ia7
%qUN4l,X,c"D'"?`6C1a[mn^V6B^`BHs+)OFN;X+>@p.Ys-_I-R[44a>ESAY6TaMTb#mn.All_.[NA/hEh?>;gOr2qI7Xn61i`o5L
%ggCTh'n/G=KkHm`-1Viin6Od.ggEQg_fE^i#/-%_-YaPKe(k%E0paBT/1S!"k%DdZJm4!/d%2V95?ncq>9opj]&m8KafhV:M6`ER
%RMBb0o'"A&Xp@TpI(J/ad+jr/=SkrcO`'hd^'k?qBBP?H'Vn21AZ.H3.4GmkR;bKVr?4AsRJJf:G-HM,6W'iuQeD)XEXPN7k*OAS
%UPt8MTJ16GNa4`SXT6lY,IL.o]D`^&(bN&17^'@l^Xut[4bcs4g'D<tW2cs9V-LEbfpp@gSZAJ?K9UTX+p*\2^nN]O(Y]:3._jZp
%dQX3LY-)MP9b^j?Ehq.-QMnk@c5YXcKRbi,,g"R&]b[Op3rOlZf(*'LD6$7S3n]W%QdpBN?bq_'<\)A;F[ZL_qhL[(!7^7O684cU
%4[B'LDC!fA?\J4/X._lg%2sY&DRC.f"s3Zo2"V&hdU&8p`(=2<%NU<\rDX_*lU3*>][Kd/DHo'k3;;(RaqGfK;,;T%fL%08lc6eq
%lXW+`(3l]8Ue'D'e.>4DM)RGA3EUOZ48%#:*%G29q7.pra&4BCEHDSWMM4Uo;&^j@ffaL8Kb%c;:^A_"\]VnNr4jVu3\@\K[KLh3
%L$04]RRIbg=.lYKZAd!erJc;epqDW+2tD*bdR#MYP;>UXQY/JdM*q%RDmu@n'Rql_rO>Dn>H$fJa#1Pu62!1N7Ala&>.S3R*T^j6
%&Ic9P'q%Pp\H[G>BAaQO(D1C`GVC0]n]#6AkL[eT+C=MlSdGu&nG''V5hC=5/?!N5!^gEd8\D'<G.4-%03gf>)@V?u655G:EX`uH
%M:'\;4K@3]Vqn!%c_*G\fG[%m"TEbr8G7k.HJRFdeuBg,B!lW;U7t[lki-SZj#g3%MDNKr#B1s!oCOVIG&E/L1X4dVlJ+SN]B:&8
%B7&]WY:q"[\g9t$*b*Us66AK4)@fZo%A#$Z&@^nbBgNb%/J\=n,^\IR)2&,Ji,(ifM!Gf1;Ru'.Pp<6C^1+,hWftWa*@gA"+f[Ip
%4qfgUT[;9T.Pjju@?1=IemY>Jb)I;uO:3_WJ>a,7\jan4XAe==$7F@s(grbq[7"%t5/DKf41$6C:]s]ZmlW4;B^_*4%eLE]mb`-B
%jl[ZC`Pi:Kq6WOQQBMCJ!KJh3-'.E=TO2-6jo47?erM:%@6n3P:u^!&i;O;e?mhf[(PU"9.>hk4<@.EuRUNUZ7qm6;'!XkAmV)6N
%=.>-5cXCp1i;f/K.jrX='p>.<f#8EMmONI%E(HF<nH9B]HH&#2GN;sNNf7JT/T%)$1?2`rlUO<o!"C%Bm+k`B$\*u0bu)$IQL#u'
%TUhWOaeI8hp%-)$eep.,Nd5-1IFJ1r>g,aSihGAWbA:,n:rc6ZpM=8HdNUm*2o_C1k3lP@Nq6\UB7RImSI."5P&W>IX+%T-e^d7E
%9:AphluNZuZb*tYhC_hMgZIoNd>c$%p4KrrX[r]:epPQ6i]scdW4K_N3h5Xt9Z(Fa:!pW.UZqHWD*`<H3g-l>5bL?WU/NW$Li$rQ
%ELmSs/Yr'u71L'b=k/O2=8>L(2X[V<hE^d.>mL;(:774L6``&7D9-#u*5N\@V1]6$-0]tG$QSl[8L^DMLtN/U*@cohJlHlFVD@k3
%Ug4au>2i*7g!m#e/H/a`T>462L6"'d@WZdU2c3://W49uMYJ#X?0on%Y%;*5p^93FGkoCkJ3K[QE!&FFcj#_RJe1EQ'eH&aE5oA5
%-8qb>C.K`Y&:hsqI.'B2X\d-Vk`ch8Xs.kd3-`pAHE@a._%DRY:T.M8@sD)^Q.Oe$;2f!l+P03[5ULuX7:9N1O`<#.rKcY,U\`F1
%$)$)sWVEg\"SY>$?7S\NN[E)T6N@rh<gWQLQG$:r+VsELNeOZK?.\,VeWW2WZj5UYb$#;e&3<10F`c_lMT1e@#fZ/.Xt\@ZdP%kr
%I#C<,]p3a%/aHCR;//B_cFRn\B`D5WlRu>X]0H4_s7"qGreLO>^\teEnF-DP2uiCAs7O/)_p?ZDpsX`ij8\n(rpTm`TE"n/s5W#8
%l+d/JTE"\YrqOYYJ,+Topih5cci<P(J,7VYr:du#^FqhLRt(I95Q1N+]"m5$rSRVHo:Q$&5Q'=ZI?E\1rpF]Pn"9VcJ+tu&l&U>c
%Zi:ZL0>EDBgY`"rp?R2/f$46m+7rq?K8,*SJ,V+@YQ)R0hY>iTnSeHl2tHG:qmX?=e)1+brsdN.s7;BQFa`?>PdLbh,-)J)[LH3e
%LA#PFm>67,H"'FXl;k0@NB3:(16B2c8<N)6dCgnB$[A>V7R!i\VEW-N`pA,`(f0nO<KQVEnZ&Uf\-B)T"(k>H'WB=5&KQqg6O=NT
%QBpiL(UB=OYsf4U-^8:o<RF5A\uJ7'#\9'R,C8[&=>5o"5&J;5L^>e^SU8F6Wm#TncX7V]THTAJ:";f)IV%)%:.&?\)'$Mfn?PRh
%5n:kq'\9"n,jJ9p`)[].SA:e6E/Z_#W`-JO75Z_4+\B,)"MRVpoSL6Dc(IUY,Y[`CMk_BN%%&6JBiUg]Mf/8(O-=tf@3.P50DIuG
%1G(:,(W-FX,o:.V8op1MUdcOGB7q]<j-T<E5t%%#hS,5_#Wmj[5e?QmJ?LNt;?AK?j#g55"AKAur=fr,/aPMQii1,@,HMCGS/TnC
%d(.`4/qn/^Ql5c&6XY#UR61<ii`%DeH+D'8S=mHq+#du_b6^QAk&$l%?[9`nJoTs=#d1F\^SKj=k].r^<PgS[4h@t),+MZt--\cN
%DFjUAA!?!gS.NPe9fKl#+31Is6KXFF>R'LRnHcb,m+?tqp(`\>Cd0R/2SYZ*rSt?:rrMTNE7EhYp.A2R((^U2Dt!4>X^]L.0sU_D
%n\.ndP==0"+RSncC6%q-3$+Zu8^3;BrFh4fgOt@E8^-;JQ3NLa="K?Y,L\.aM^f5[CpSsiY_S;&3:4-p`tr\J%UV7+K@GEaD;tb6
%G8\_sDjSJ0qa5SH"_TSsC1stR<A\[a.@QAAMCgG(h7@K16Dgh5T@ID-])P4tZHekqDZ94D[,_oE)!aom#"qK5i-jlf!X1hL:I:PB
%-(71(9#M?(#&>rl4?ONZ^gE0C+8O"<&jR5b5HP9c)@P^!I/`mLjb9C38m5jGMl'pQ2"rSM:t8]!59q+ND-@Okrb'klV@5rY\ACZG
%gCXTc"\_^E]E;Vrbd"CGan-(T*;.6p=Xf9W45+d<N5,7YSG*R2`P%NX0&!T9`<Nuo`W-K7oT=e\!!5AsF=2d:5[QlNOFY<n/=__)
%j@jQroct`W0GEKm-i,j_iXoJS+j0BGLV5nUS!qAW':iS@(J`]A+p5*N$LhBr`WZcHlh"$ODPluk4,p?!<MFg9^\5VGYP<Np-cZGD
%<Sdu(r]J?.l$N=18SE/OH%Z*lHtcV`g)a(sKftUP;4ogNWfNk^=Hpp*]I/00V@/#mO>FrjQ*8QO;ZXV0K6u&j^3j$@.S;1PY`\r:
%@(@RW7h8U=QIW5(A1c5.QhVsjqd!#EXA[dkfqBi!el.bS_1$OOkU.cI09X>9QR,^jGR&.UjbfG#FHZ3I>USd.,H3F[h!]c4a_:JP
%r*i[U'nfI;i@^IJ[<]0[XW,hLEXg>^`j;]"MKqM4k)2pg-.<BH_;p/BKqt2m`IW&?_9A@&FLBU;#9lXC;dd97n3SYUY^]49N:\?N
%lp>-)/sd3M>L,N'Ne4^u/!ARBn&,$L=6E.3fAY2/fJkWh)62+[3[jj8Fuh'N`Qc+PJDM5h@afT`F")N)C*SFMRL5Sr@5f*'8q4KX
%r",hmZ.[ZpA%?hhQeerZQ<5k<j:2]5%fIu`h<`h/AIr[2MbVHo9]peSQLa-+/R8N3:l)c]M]dc5.rAN^f?a@#Yi-])$2*kh;\lGe
%X9u3A=O8V%Tb'uOT._mp]j`Ra0FT)Kaf9^ijK'7jZ7:HZ^O[+C7/c#o]ALbH7EoTob3-4A"I`c5[J"2T=ab?sUlofr[mkk=OR+3>
%1o;H\SV7/;1!hG$rn<eVr@(f<eWFai9G9567<qiC2?W2hWDdcqFTL1eAXX]*(Q_BN>m'p0'c6B=mnofOlpZ:*_n])jPtP(>_W1J"
%kq?BQ'N<@I%NNr/.ES7<^T1+oH%Bl3o]n]3NnZu0oHb#ih/$od@8FZi&[u`\ijQ5XW2`Wd`U[90@'/7j$nPQE'l')41+'c5'/&%>
%Dlh/#_)[Q`"8tI!`D\V8k$e"qP$/";DA<,\msY-Y6O.O&5W1jGF1AY>r[^*Q!1>,$BZ_BUi+rVt+)`DrUZh`W%$-!s%NY,:ok:#`
%X+h'Y/3d6M-*0`op?Z+9flM:.)V-Igl!]B>5$6Em\WbrFNde,rW3]GFZ`Jj)`,ZHeO&2"dT]rl/O+gG_Dhp&p90gV\?\?PY(=!V"
%1?E%."`0OS]'iJJp=]qkcdP&-BJ8Hkb9!7Ao@.$5]"hb*L@asuL!Gc\16GC*,h1b&+[@5FWU2-t46>RF+XEUHZ46([>ZO??h,2-4
%ch0.plX<HK@SbTa?]-P5dUY&b6;NsPXh95iaYO9,R.1fm(HIGid`q>Vc5eI$MS'd]6$dSM.9-"nAVRneTXlHX@KOqe3'hk5lGqL%
%'Uc!nB*_=phP3!;9/or&S.aUS_rQd9?J9aa*QhQ9q-2!Pi(>e2>p2T]ocS(chUE\ZAs;s.gZ1ld&r!h8$V2e><+@Ak['h(XH!Y(2
%ASVsM*"!iXJ#`olmsO:hBO6^8C?ZoK>'c6#]k0ihr>Cn(^%]d]AnWImek)K$'m=^6`G6M=Uk;qt_,klZ,p+-m"6nRf&oH5[nk+6:
%AhEkD!`r%Hi-le[R:6L2O1_e5[F(:jL9bUJDU-cd#i2qXG34.(/\<UfDW(GPFLFl6A_U%m.EAO$1O$i#P;+&U3iYB:aaGJLHYdcC
%r-^V!%p]`XqW1+C097?J$-(X.P!,I*`SEY$"!\@$Wf.HMH1%&`33:KgMGo-_?ja=`G&n`YTr-,lZHB;$:3rMidKu^Q-ru`W5Q]j&
%!cpr)3cI4j*[TM[.<kY)W_tq^Z+5:@_j.]Ck"e'f9RC[^Od1@h'_[lo+8j;Y'&[b<3p'F8hWN$"kQpB-8OE:c*\&-5*$>)VYr3T.
%*nddCTt*5m_AIT'XVWD=W,C<]kW^.-6AL%,A^Q!b0!k*,Zu4G,E'sWMYk;%Fe6Afn(?r5RIQ!=Z=;ot`Sj5&JR<i^qT=UsXC/?)N
%M)>M0I]5"$OY3O[1pFP6j+-&E&T5CJ].41_Gm/]u1_ZSZ6nL#LYe"(^_4*Pjk@T#nTTC4&3moVH0EgCrS5J_o9!UaQ$lD>DJ4X;H
%*O6E8:<)6P-N-:aX$bBsgC6gL!aK#9,K/H>p"$KJ6%3Pu!3B$"W"Nktg"erKT5Pqb]s/eg)!Kc[IT-UUZ:`:B`YUM;`g2h`Q"GDe
%dC:Z`:)l=V=,n[sl]?)[-1K"d3]nq052]HJRYn:6:+1aAW@0o,ZB+-Q<Jpq&VNY.C+LdPGGV%WJ9HaC*0_Z'?N+d$lBk*hSFP<=T
%qp5@L_l1;]G5O@8F/e5L='RZYKij,UIIDaA1MID:YPANpI?J0mQ2fDtd>]R?iuZE&h0<J$jo>T$-$-CJ_Ut0c2:5fJ)YaB[fg13$
%^n[U;n0KBtaj'uFQb)]Y>7Fc1>UcN9'L]rq$qht<WLgld2,IL;%.B:ooD7j^,ZUW'?BoOsL/h%`OSG/;2\]7-4<)B7e=kbS(iC!f
%l&4[=D6WtZh[WPj<A/86D7F/S+olP8e>H&_[c1\6g,u&3p\L>9n&&#Q$(g\OPBJl7o*Nj"IDo4Zae8a>loi:s8_KKKA]3tnWd.qZ
%$&hpL,2`MF'Rks1+IGc@!!IdP*;j&"#q:7@UGH/!"K+mQ/88ob">p\fEi;=j+,A')52,Sj+YLPP\/0XddM$@bX\isB&0faf6.Ze:
%G`'UWWaY%JN"<F&!ju,Z&8Dd/!Qp+q_g_@l5d8dl]9O,kr:Ct>Kmj=qF+T"L0rkS#g+uODR$<@^(e9q%UkT;,X(t6*<5h>e:SEnX
%Pc=g=-.>[ap6LKcie*=nLA^F$`V^Gp!4:kppBZN:r+*JNE\X5_@rCM<c('/DLK8l`1F1sVQ&g:JhPMHh@7JZ&08d4l[@eXhI4YBJ
%[%<[=s5rQI'G'qiQ!1:hZXl'UEd'Fa`#$Z#r!BE?Jg*ac;GKGufXN;4QY0&0c_0lQ_PMpBINo/TY@H)7K<IsLWt]-P>>rO=dKelf
%ls1>/,j44n*f6'cAYQ3m%348s(=fC8b'/?97k-9I>=<$gh,EAdR]At_C7U[C?sh?mcr$gYVW_800RMruUJb+B[KE&3Y[(P<W*O]$
%0O4q.J?'bj85k>t/.A%,UM3JRrd.i4''<Y.P5OSQ*;6Xg1/ZSe3T29;K>C[bOd-GS!uP)l67V')E^t4)!C%03+8P)h&(N!T\iAtM
%i%SiJ0-I^['.]6S;]64Q78LdIA](>l<g!)@>dD=Qcq92>3!;[mlc&h@l-nf0<r*%0;O`\4MJ5(17_J8;Jc^pV/^Co)('XmN=s<co
%74AACAA"qC-f6+`-[nI'p%&c3if<L8^'@!64W)bj^]1XAs*$)e`tV&h%\+[:G0!(;6JNRR<$Y9DOd%ot9@2jo:0-/kP2]3Zl5R*1
%>KF"l11#W"$.iZg@Aqb+4[7"Pnn*V79TO>K>>\$j"ctM+9oJCX&JC/;EFfFgC<&KQ;pQZ!rF:^LF!o<8PqbHS92XjS3`-&'8U3*>
%=3StRXE*;%(6b1lL>/2;Z0E\Udj]N;A)!Rp;:*'Mj9/3S4?\[&rN;i%q#ca.cPEpZE(smtE1ej^oKufg.F/+Gb1g2fJ#P7,B&r^/
%dPHFk_WE9,!WXMh5;s:1=g4ZB!#\<Y++1%@f"/Cad4LFub0VeElZX)o+o9!'Yp/kHN2KsRkI.Z%:e4a#KRTXt3lhkm'"`+ejV./L
%V]b4SI/FNAf=Z.d&pVZ'RjfifGoD]B,Wrq0/SEDD^_FOS-C`.IB(n7#?6`AAPR4Sl3CeOJl3>K,gn?[?"aLePfRei3jjP-_W:ZDV
%^aVQcZ#55d_+3I.-dXIOo<@9&/:\IU,$-CNcBsLXn:'J[X`#"Nb6eTj\!J!t3<Z3[BbV\J^*713]dCBP'k#G_E5DX&[DmW%!ui4*
%mTc*pWSks<%YC9\C9VbZAd`2)R3D_uUhZU7_(eo;^A6F>IKVf?F[G>r&$\)H)G/P&e1YO:?G9Vj<'rgcXs0pKS`1rg$7Z6oa]ZC<
%XB6qJ[!^,X@L?8(BnRmKeh(LuP3SP)9(ZH:jEB)4)SL)2Y/.E=[6kF86?GYA.abf"_4KF.VIhcA1*q>o\!'FS+@pPa#CT@irZ'hT
%1XZ_Bf'$"<BEg8;HF=^5<9FFF&nZV_#l%'c^:>W?Q$lI5k\]SVT\N"WTjWH(Y]u/Nb5n!I21kE&=qQ3Y,K%\\:Lji>oOreBOE(d-
%qiJ\d^TV\GO\q$bd'7P`+"#hD]-8Y;9Eq*kDHm4m+?gZX\4=.D85Z-^]pNdaG*r9Wi+lpj"WGpl?$ul;T'Oj%/q_"*K,Z)!MQT!?
%'_`?N^pS1n0a\iA!kna*GUJ6/(4QrT=9$&=#9WeER86"g]:"R.nH9>+g4j?%pG=Vc-H2ZT>0,W"eJR!XRNuc#/Ha<VT2#G0E94*0
%ku,u#pFsQJ"CGrIJ2>PRR<*](chS\`=-OP&M5EAXI6,tPQA=H>V?bl3.*#gJ9]L[SS<aG3K5riA0^;8pIB=9N+<!CEk92^DrH=^O
%OJ!KCb?d$%:dQ0gq^%Ep?(Etq-obY*4#q=Ep._0QTr?hV8V/DC^pr(H>i0IZ+"m_^>?BKd1`pqh5"soI!'H9b&ehU]i2^>]lo@Jt
%kY[2(i3(0XO8h@:c^&c2p/pr"CZTRGRH_7kVRi]/PJNY9%b1]hGo!I9BjL!,G[mrnh#js92Q^T^J[7j&X"A7rf1X2A4U9ia#0BX/
%<I>(hXg>dp(@.mA=A&h"0b"%cRh7csKrnYDm8169OiASb2en"tY8cb.#gt)RhdMT?2;FEA6!g8)1,GS5GkQ7N(9;m]NO$a,-5Un7
%42?cYk?@jYgJB_@,uuu$#to2GeJsQ=D-N.O`s<[,<UjWrqkgF[4,*M8PBpcR\]!DBcJ34:p`rh"7`+l6jNTm].?9EgRIF+WK.m#A
%SV#`jg,-Rj.U&Q4`TYV"W%XcG46l/RHXk%*+\"h]6&pP3MVs3^h!/)Y&XaM#$)e3Z/&ELX/fRh7LimZ=mGePSYYKd+7NG@+[E9F`
%,=aj%mpeJjY\WhGnr=DDhie;l-Rt0E.ps7%I:O3KHeimkX[J)&i28Lk\qd6o1hC"kAWr]q/^l(Oc%7h<L<#1Qe+,qL>@5ba.T*XX
%Rhn=Sr-m/cY3HCfea+$AK@<5>*0F"VLP+\mfFri'ngt?LVRc28Wergt.&;J)0Y.cj36%OP`Rae@s4QB)GBI0j$XW,<m<oKhMnpfS
%Bf\IJ-Za'c(L5.XG;uuh,0n"o3mi#_I2O'2a80UK&(Wgn!>KmN<u(X-@XH?cMpXo>%H<%d"XEqb?>Vk)*:"3R:DK/p!+(goM0siJ
%"BcCW6ii%CLA2<^<Bi+9keRpD=8t%@s(G3r)[fmQBP"Jb%3;#Gr]iR\HXp9%gNK:*_COo?<G%<AFP)Uj\YN0#+fi+\?3o_KN*s'\
%LU@X=8tc8B3:b2Rk"RL\"<,pGIZ23\TO91oHfQgo,\EHbb,5YU_TW`llXZ9G8^Jr]:-cpp:?-G,8$9jQf8ZnnZQ"e+'Vq':^_p:[
%h;,;6Rh):PbH?QFSCu<p4K!1XdW\]1$-9Rgf$r/')dV2F(aJ=V2\bC>@)%BEZ$E+RYYt!2bSp4gO[9M&Z9L!df+4FcedXU7RFWIS
%a(n9l5!X$phQ^o]#*#c#!h![(HEJT,&T*)L9m.-*eKmC<r$#DsKu.4,j=#"D^-J$(OSo-9B^$dPL=`*H)8mr^/k(A[=Hqie\@OLE
%!RpDMWH4fB05c[l[;:u42Em]uhKs5,P5)<=64*NAi`PjlTDcb^TbP)af.2%\`-W6m#!NgTl=Bsmr=4FRb\(cb)S6Hin711'll*CJ
%S!=<-nt%=9+SZrSCmIQ]PV7P6iX`IuPIV(d-qbnZaLDCB8+J496?I_?4$ng&0u!4jBDgC(9fb6Wj_4+)I*%e_*C/hjThp^p5Ks-0
%7Tg0[=:*^J5Gh$UC,>f*<@C@(ag,XnV>uBj&CAE1f\'_g3=_W`mlrJe3C+=3ii4ku-SXHoWM/%>fk:IEE_BM3mOkk4%i@-D#6QD?
%0GJ?1cu^oqEm&(-pl*8##l&sjIfO8C#07-h#;%jp.o<UkSmYiZ$+e8""a@>E3RU_:dR0Tohuns?;\#@k5@bbX1!Kf=->937Y^BdN
%s2!?b<B8q97lT]SMddl*e(FH#gr=_ppYQ%^6(U@mkb9'g(`@?F'7j%VGdI4d>It$"1S&FslGtb=../A^9XeEo>bZ&Gml-OD)6UqG
%Hs?ZDQ8W:?BU4#Tk\0;<9Rf"&8)<X=->N^IRJqJZn(6\)qAr#=hf2JT:Z1[?S3tIETJKgD\k3RU=5IE!BqUiSGF&T"=rH=VkE#?:
%CiYU,"c-ULYT3Lm)Sf'$HFRL'XTCoL0OK@s97>>iJZ=k:W9]dW]QKA=fKs#+7?`Nq@sLbV)EA%E:Y)t$k1RXF2eu!*&R7JrYo+j(
%g*a-1V,pMl\.GT7%-ql$0%0g1eF9Fe\+g:uqrL^B3j\n0VA<#HHJ(NCg.kh/WehDfQ-nZu<V:/V8h4Q,9uh`HKIGkaF]XYM7cl:u
%FND]IrgMR\G+3k5S37;<9'7M%`o.0%p^5&Mf")P((NV:oeVOIfZo!,/;\%H@:^)LB*'^]R8.CAbcoq<09S5&@p[]D%CeWafUB("P
%?QWabGt2#cX^[Akg&)8QC(SCmDg_4j>hZ`[XA&$4'154;]=_AFTN;9*3kek<_T0/X0NNhOaC6.4E]H=J]F!&"X7"mI-&b["RH2+.
%'D8)/hOC%V&WXE+G$miCaTS!561+!"-kc7`(!qE`Rp3,s_<0]_3cj6('Q$i/!%H.26k4a@b;aELY`rf&?A$#dL\STl!!g)UBX'fO
%JgZ0pduQ.JVTI8:$>b,:O]m9XanX4tL"\j>B;d=@r?;qQ3,1-`_X;t;3R0Ta#-Ie3%(PZ"62@'%</u^7_2q]Yr4'Ym\"][X'Js:0
%JAGmjKUNHn&(TcfY/GkihdT!"+$9,4T+APVKqir8%L:<MmE50!((Uojn)GB!GoOIh0>?DQa76^"Fa3$F-!)T:dF&^!#%kD]]Sj[<
%Ag&oHN?t4tpF!DCEIm9l^-/\0/B=1$;WEuWB?-c7CL3^;@_/"u(%g\SnHV6"#;r,:8'TNq]l#jA;5Q>aA9)2^IHV-CUZ9Jl9UokY
%as'MB)uS#"(Ti?'C"(\<c"t.h)Ca;k1G:M0p5AITQfgtoGFlY<OGMA#]WW=a3:_b!-VF=3##)8^:O.K06emsC?W@\eqJ4W^qWB:F
%'qBjS=]._RoDWAo)%BQ1s6'DkrjhgOrMQ5srVBkPX<.DaWU^"$+T:sBV!f![JWJJ6=<dQgQ<.'K!Mq1o.%r>fK,BDDb]No![bE1^
%82BjlSR`Rf.q;O2Mi;->?jBM]:f(P>H>ofa"pJf1PF>R"V().X)iFpKnnUOB:,4jmA0'gaX\"?lo0'[D\m=:U&@F0K<Ak1A%Ne8u
%@MLE%jCSaEZ/Re\%jbS7\ZTL9lT.5<g47WeZ1]VIH$o]S]`0,cp8S/E)[o!o^cQ4gnt]eM(o9aA[J0/QLJ)L?g^BVuq_Vm:huW!+
%;b?.L_(&6`4MEP!eqbfk)8Y?El-IE=gW\8\[m$u\+I[Sr)@ea$p?cC4bo2XT'+f[/8$]:2e.RI'U;6DAVYF[mSYcT$*P\%q+&G_F
%piUH,bq?9YE0L%(5+PM=r1"CE"5[IB.S$i;mGOp+8'_Ob+50Mk7CI+W1&G$=,+km4T`eE:>D,AgTR3o74<gf("k_RG0`f:'dRO\7
%\_gY_r@(L-IR[<,jH'FnrNXF2eVTVY*_c.WkAp;Q?\nd1`("rE'74VFGJM/"3Y>@iA3\&u?tV:6YL)0+q;AkRE7_UOF;Lmq<!0!)
%U7Y&s4]JX#*+T\WEh4)D9t/]ISOjd\GK'D(%N-(b>dHipq<^NBQV4phL#AmDajSRh$5SIfj^?Gt6s@!sY7qRh&X47c:8D;FJ#'R8
%DJ0d`9VsGu>(Aa]Ig8]0Io(oBX1!mdpe"RS<PM2iS?\gcgYB2>8ZVn)eB/XrW`AKN:9e=4]sDN2)@p>uOiH=?7MQ"I?!\N7&b/V!
%HeUj"0c(kt1b04>*ic^%H$#VG($OLl06FOM[d\>&dC0bTTA_[M!,<r3%>;>eN'P_R9l98Hi2M[0h:j$78iGEN6,<Q^Y3tOG5`NAE
%;d0HX_&i_XIX,s1a#cBKo,M\sA![$(X2^3-AsB\e\ku;]ZF.EVf^^)hO^D_U6cec).V,>5ruJCJ`!8hDN%Clp:k7Y04*^iKp[CP#
%On&RcN/Wf8%&-]f!P%qP2L/j%.Y]RG=&d*=5G9Fs"`%p-el&2lMEfi)JX(N*F'iri&Z'4i1lPD_Wq"H\2DUqZ,nRDZ&_nR=A!HA,
%hbe"'?OY%lBCQX%5Q(fXP!BNm91Dc_crWIJ`0!6";6OUTg0<[%EakE$_p^''Sh&'Q)J?Z$NoPFtVs;@U.3VLoC@e`-;gP\A85(/B
%h[I8udbe!2UhT<6id)<.m3P%=0e*;R1KY=6=?eFUCf?G:QL\CO4[h"3h!if77JqFaT-N2s$,qJ]$]hNFL>\.n+;+6%*'qf_`>'eP
%Y@gnV!^ZKb\FKt;LrFVg*NO1,s1S1oN5hLDli!Y&R(leIJ)?`ZJE7E06CJ@Ug0Y\0QVb9^h*-9gh@0Fj6$4crVf&mJH4N":U'+J)
%KN,,PE<nAM.Ws+uP>MA[Y+!frM/M;,"f$\#6<tW)3&tO_EMbhUb&.h9a6VtFB@[e?a&mEo6YZ61nX<u8af_be^(sCZ_#[@;?75Xh
%i+jQ/'&)L$Z,s#E<*6LP/PI-@=C>>%>'GM`UZ3.9)OmijOBurP3>DI]p?q4Jn$q'CLW@8/No)i7T3l]R5=8c_cKhc`;UTe)XKN'A
%Ft:2U<T>^r>1$*$kM&`^41OY1V9]^[HT=F>r0@MFnHs<mX7EOZ9k6QoECfr'IuZ_GZl'tmed?;SRXN.[F\mr&+Bd"]H%d8YE`Nos
%9"SR<@8K,ab9#bImhL5E@f1o.;,@#cA"*P505:&d\5$25^ced>$Vr5T5K#-:7Wb!-44j=ER:n'*q>9UFM+2$DY5n+&D/i>i&UE%$
%(;kuF%#m!"?-g`R@m!B8H?k..:"*qhQD&j7aln)hTHQI,8_T5EeZ4pKdIEH_XHAm`6Kt#6F+i`A'uWhIaO%dh'po7BGu8RiC[R-_
%(J--0C?(7g[5f:PG[Zh]eRET[ohgRmokFSdFRb>b6EI#fg\Ul9eQVHULFt=32UYYcEES(XR.NY+Ore6#U:gtf/cO<Z$=CSDM9+Tj
%kX$KIP"t0s+PUm9g9#YFoeH;iC[pj#g@q/o"l9ej@Z!pP[Cc(5""+-@e8k>Y%8f-KBu'hB?;ZCtE*Uni*KBa&M"0Kf!dpGuGZm8Y
%qbMTm8UG)P$h;CPOq#unY5fE@g:<E<&rtWo$Qf]l;8;1MfNJ:tW(!)>7PYd;XC^4b%uuWh\GINlFM`CHaKg%gN`q7opeZ(?#QltY
%mkjttMUZ\idu-f"Y*I:s-%TXK:MGeW%I+kRRim_8`'o^+Y-/$LUoak\84kpYJcNm@]/?:BXI^\8c?EAjkkZ'=5g`'@)ngR"I4k2j
%Wlg07X,Yf#<5"/4O,d(qm'4qRMO^nrrE;Ufe3!$p[M6>IHV[adQ0fY,L$MO@MdNu%9DjfU'pDcG:#^`.j6Q2`R1[f[;AHPFk$VC7
%9(R(]RugVVM_SCo/lklC=&3kMN4l%#$6[)>..@k6BJ(B<I"`3S1r-`8DAYujP?S,;FkfG?UO3]j$b7Q9J8XQ[N?bGiru?$^2/A?l
%X]Q\"&k>jRB!?QTE[Z?oIpOLX(o`trrSp[4B@J,fj+m*KoY4XCgj_gG%Y4'JQ$82dH=,iZFsnb*#`]Gl@#O%U)4Mo:!4[$U/knsg
%9Ebao(?n>0+6`O(45XV+70U\2HP,d=dROk<4Rug2.e4oR#25ndk3?9jneTRt=t#54grX.sSPMGZUqiL<1&O%)?=>:4V5T("*OK&t
%dZXfiUZE,#(lJMS5THtM;p:1Pk/R"+VP$MhAP38.5q=Jd;q#bm,?O56f1Bp)?/\b(.&>M3H^HQK/5ol-^4NJf`M8emWsIH0%T6!A
%="*cB,AusS>en)!`QH?7"k/WlOm?@b]2>#N_)Yb*PD>e*E7"Tq_nR5kqnr:=q?2Mt)o[Diq9KA,<ZFraTB"bsLr(Or2/[3eHsJ[;
%\3DY'D;b7)\"!Kg$o?bBSX,3#Q1Y?+0U1S<*UfJgrjp]0HB&k)+@*FNLs[m)WK)W'SB.e$;J7&0Eg>gtV#i<jZ`U?D+agl_gZFuI
%053mr7WnZVd+@@h$7XgLZReenG=lF^6JgaA]?1sFFZ#l%$>,mk4iC3$RYstZq\64(&Voi@80K]I+?QOl6M1.=rjFY?p$+9Cc7$3&
%8E<lNOPYJ*Sb\fmN[iTN`!0FlpV/,j<""sOaH</CMfk6u@fhn__(r54mF?ue74s.DUm]U:%m_14WuXlp?*\HDO;SFQ$>E_F&_%P.
%dYK"q2">&lUoLZgroLZK3K'K<<+n`dk+84l<ooZ?iYI&Tc[EoPgeOZZ!flZmH5oiTH@dYh1I.V^E37kW&(ui\d&rbVf4O%FH.sDb
%]Yl_I9@KE5=LU>h-O@"MdL_r7qNq0SW!;W>>@WD=g/K@$Lr:A&k1$>0&/hj)4QIh`&Hr.'i4:DCTXN@H7Ffjs*k9M4Bhr^RRFIth
%1hC;]F?9C:\+$J0*]_E9%@&'`"6&cVCHh7Er`dUZ^W!N^k6l:AeEZE\8DongNGC"qcUo-X!$Z8A?B6Y$Yq#'bl%,Q76#(]PU"R_P
%ab-WX)nM[@Cfnr+Ra:jT1DD8,ji]\+:aDjklaQ#A]5i)7_kLGf6rQD'`A"+2ni4BIc0&6PkT7HCi&jpB1B[hbMck1lk6+eZ4>2a&
%>T2nXT%l-WWUL>&LYm?ADTM&,)!D=ER!jquFB>hUN(kIV0h"sdic:&Kj4;o]Vc\&,oN]bJ6t5,?BZTjkX#?k`^!4=QbP`2X.,pL&
%.Z]P`Z&%5G)r#DJh"H<&2t)(sfb?/Gj/4F,]m=<t+K6X/@-o'9rmqP@n%HjGm2B,Ki<'&cTHp!*7+=Jr=&;-<hNtMkG+hmF+Xrjh
%#WTc[OsQD-dVSE>T\G"pf#FsH'j2(t3h1>0-/J=>oW_!^N:FN%=@KX%1%TuBa4@t#0cs.;\0@J+gi%G%EDC"Vl8raEX6IYtkmiT<
%cXp$61e?G`/.$HGXlZ5/"*=>E`<W'lQJ<X?qMR+t/7FD-?YD1?8o!D[$&+7U_./C69!&ZpTh5]_&EUrid`>Dll<%#Xn%Z9/h])Xs
%UjfO+F"Ql\H><.IV3U\9im^E$B$p4ceoBD5YFW*!%'8@MZEe+UYW0Z!J1m"iFfXcAoOcg?*u[[S3arYC+5.%I-]IL]i4ZLj$6SYn
%jlA$\p&lO0,O[l$NlMVX/)JJoZ'\IU"dP`]C7M9%92.c1&f,lkP).ljn1p<Bdli`C&f%a]](u&0oaum?AL=?*NBZ3=DR7>SPE_FK
%>9;_O(d)BR;_"`p'LlHGKc3^D)+R>arN8lH(KC]NPcd#80Za2`VX9&WGTKfGNUn2fnlN5E"KAe\i)FatdNpuVhtnmk,?rVjMtt#*
%(D:l+npE;_I_1X<cBDcYG3Fh$B;5O:<^rT1lLpV$Wle1L-VWj8Q5T=G[KOD"lTQ[j\Z&R1XVK*#lFh1K`EZY/\j7m]:<N[ePM90k
%8pfo>WG)HdLfAFm"ST[E2u1+*KtO,FqS_4%e<uL)2,A1F4tR63DZloZ8r_a[E:eMaB5>mrn/U<R\OshgJPb0.]>"=%dP6u`<W*s9
%H!+4deP3#)JmH5JOM`,b!f!dHB,5`cgKA[.1CQ*k@&1EGrg%XhW(>W,mm/#snNF5F]%-=[^U[8s`"1([FG$g"H^G.'f8X>RC7Z,;
%8'apWG,jZH$kZg!qS&oI/j8Y<f>nan!^(l5'TCD"/[5AG<De7A&%Qj>87SPKr@U*p12crChG98#dB]NtK+sH\dip@P;B_R64c/WT
%A;[k])tb.gfWmrlf3I#<&N_03bV,J&(_U=CDp9B?_R9&=OAS:['@6rcF1m3Mn[5-_gLlbYb6gZt@S+=F>YAWZ(.$Y9IfNjCet:[u
%-NJRjO1m&,+1%-k;u(R]9MCn9=Y>;>%=kUJY-?+orN^Mq-Q9G=*QME"p.>paidP(e;6G#fc(-L'@L/Bcj_#N0s'W[*B*.]p4A'Ch
%Y*8Vq1#=,+Z1NKXh"lWP+<h?sMd@eHK6Z^_ramJSC[DY&mkGa[:P7S4a8>cEI$[YE9E,WZ,5?&JZHD0#T&G.+,8"aqNXoYuPj:U(
%Br&3TCFWQiJ61PQAC_#ak6b/R#]3;e6b#LERDMua'NL_U3i)j'(5F\)DlbnnB73@CoJ4J+.b>=2MWT3C)5PG!<C0r45h$Q&H"j];
%V='AGl'7N;WO[`%)T]KS7%@G:>*fE)7iRZ(YI8_MI;p=@h6C-^T6!l,gil%.ig6/eV8!BCiU0q);%:C7\uE&)Qj)g9IA\hG*N9a+
%;M*E^(MgTf6.WN[`BE\o54Rt0/oUMFk;3@ABrRcJ+Pdq@^4;JQo[aL;7&jSd23.m,'b%M-o@>Geb"0,&6hK,"U6D;\atrB#5i#0u
%`0,:hI\GM^j%JHlbJP)S)C/"B_8<Z\!m,B35?-QW/qCD@G.,+>7+MlEA:97]lT"kfo9A+\edZR&D/B919e"[;ce#E7GdNp6SuD=-
%oG"dFLaM#BgM^dO9'ED&4^]J9e/H57%8Mm@V,m.5Lq%gY+K)Z$B*O_tb&p,)-=XGC+WV9;TFb55f>uqO`g1g3@RDakd"ncC@@hYT
%Eo\Ueo;f)`),KLq&LCM*"Jr*pS#,<BI3'RJr2B<(30IVm^?%u'F0R;VR]I*$Gc<c(Z!AH>DT(aS&VSJVh_\7s5p'B5$&/h?[p)Fb
%@\%cJgJ#snoE=\mC!<8p.m?,bG3e3WM:J6X)43bT7+DH,"t4qA;F:8SAWROTbS+?g7B5d^/l$%5.A1]<<1D(_;#<uiFoZl(auWTN
%BD.oV?-<\"ja"'Uk=B]PlLdi&L*p&.`k.KR$'4pCinVg]!u^bAK?mhhg0L"T$Mq,j+BjmGoekP/W*Ner5-%Dh,cA&7hHi-=CT,>8
%'p^nsd'Jmq#m'O,`?Pnq'Z#l0n>=PbibctshmgNAETh!4_8.!j+N!_S^5'_G&*sDA#d*haR[##iODo?KH+Fog8];l8Xi6Q>Xo)`?
%UD-dfnECi<[!EE'4*jPsXDqNK;:XXhZ\cr$`_amP.'@Ep139B-$,oa^lGfsHLKc!&&#^X)iuh$]EVFg6WoXR8METu@SiP@Z#K*j5
%!fkG;p:O_#0R]>;ZWduQ9p\rcmLG(G)D5^Fki[R.EAh_OnM\k##%*[c(okKIM#!"jWR1"?/dAYd7DWf9Qngh+\To?9PCV4.IDSFV
%`59'Pjdkf8E<UYt4.^6-kK;E=GCmSV3e<71j@ajYi4Qpq*;RSuV!l/Xeni;;PZGjC<S<*M(DG*_Ot@P.@L1?'JsKkJ/!S,Pkd-@:
%P)h$Am]LrnW/@$9A0]g(V>>F3ko/j9dH?3Y0bRccE19O-7WM(OPH!@R3'h+>Z=+K*6hE*)<Xn(K8C$i2B_$B^[oPJ?r!@?_.TT2F
%:XFtc$gn^,.aciL^10`1%@(e*FMbePS_%Ws8-3g"ZJNgc&XRqe8p0Fod1`L7SdiFV_*aG`rtZCVo,5WbSY47<Q2#kJl.4oEd7&h<
%%SDf>iXoi-TXa7=cubK"0V*2b83-Ka!NS'*1^3DRC^deNDn)a]A%/u<.toCn]`KDIhS\=rWLT4BiB%kPk/b#4_8Wf&m&/Q%^18Y#
%\c=AE.IC`d>tYW\4c5A!H_gBE>KB/"b[`X#*<d=2TSfS5QF"@^UhA]q9Pl:"\ZEZ(COGaai%n9KNl`];Jpl]#bp?bjPY.,d%KX*c
%;LoU$aZu/PX61`/\Xo!05hPfG2oA+i84U1n&J<R=LO[F+nP?Dr:p=9r.Q&XH!G!K-RBmVo]+cmZ4Sk4s9<%TqRd8/>#oh&aNS(IV
%+dT"<!91'@P#]CbdBldU><]ubq^UMAIeuVN]mm>_Mr*<82Y$f3QLTIm[D0&,6V20=X5T.[r_(2-]X`<ZPlr!HHMFu.hg=#koadIX
%k^22%4B7u$B.2XH)(aeT_bpo43cPcCg,6Pc+[J?t$FG.p#q\SMp%G?>.[]s!KrK!1"kND_T08DJC]f]jXP@n*<r^kMj57o5?@q1s
%Z%C#$='?qHR9Ut]Eopj:%/HXos0;<q9pSdL[U=S;1]<)5n!Tc`ZcZh)6^=Xq+@1fb+D;Ce<*b_sOc^H9+sr5(7%b(hg\\K6pH>$f
%EW@j@;M?Aa>efIiZfg`1IJNGCPg@[+`fu,^EFlG/XRW,='-)ipa?_Q0:q]C8Ug#=W.2bRMNFoLdb6#gKJOTjA;+<raC5,R;AGJg#
%>u3Ug@cWI_,'Nfr1dS#J%]8VUHHd0EA0DHr>/'?[C(B<_SjXD'ZRj4OH@i<t3AkAI-lUmVGg#r^j[^Oj&SAo+:3\CMTEBR3[)Q#*
%8S3/43k<]fk9/Gc8C`c-l5R(]FPuKmA0D2m[h=jdOD#,J',bt,1?S4N;:.)J"m)U"YL(L=cf.E-mko!NKYlVB6?#hK+n.X32\R"5
%b%#<"qt>!m6E=+Z=Rocb'9aNN7A6'*-@6lXjMDP;)+%3;QR7edH4TmZ+.uH+C8*o_mD`^OS-:oh]8JsgH5sPMG"DZ0`X*bJlQnub
%bd%qa%T".NLU"9;VBO%_@$)B8_AJark]Fr]k'$Tcq>5K5O4Z!3q_>!9=GHE6je/fPJWl=3"B%l]T!a``J<6#R1N4HtD!(Os^q*u`
%:VDO*<0h/V#sD;kV37cPLh!N)X90UoR>5k+]u7bjrWT4L4=nEJ5nhDJXK6u4p%#=>4qcR#4@r6?.mg$"nuees@(Fl%]U._FW5fgP
%,8g1\icSb,guQ.,3.R6V((8*+50sA$/b6X8X`aiFf9GBo^!$A-iCRoXn02:QP^C\FI@/8T&8"Kh6Y:$B/L"Wj3Edu::h#F"P",:0
%H'g%cAVpaLO2m6mQ,$."a:n-+0YW_'<h+p:MD/`bN1-PES@*>opLgO%80Ma#cI1>C,YT8jY*db70TNC[6?l-/RhF73+o4D((9Gp-
%9$qA[_)10b\VZggGnaHh$"K243.b4c8`!Gd?PgmK?5M?WVq6L(fFR4c#cu8?QK2WbBh/EH\OBZHmP"nu!d"b;i5I'4qKA!19<s3-
%V0J#`@sRHeUU1#0gYa?'TP07V1-="fA1Dr"'0U8S0_A2,L%6RIfa<_&j@[NCl`uSEC2-aabb!CoR\cqlB'Y3ZX7l!964OJ7Q4n@3
%+0Nu-aere%d?K9M&j!!2bGu,$$sEg0+c&]6\Q+e(*+1h(7XdbrfSG$=M*nq98V@l5%S]p,(%@F3,/'P_YPV2<$-L&Nm)f7!P\S>`
%Y`.]'8gR\,74&*I\fQ-Y.D\Pg6Lt2?S=-sEO=_jHM+0&2?th,8@]FUulbt#]NX8I0/dDdGZR4^_0_$#TUgkW!l8UTYWW!r<[OcSf
%<s%L+/k+j$q2j^U7X5lhc/d!(#'bg_[[MIV1Wc<26IDA:B-*HLF-<]fW4H#[&B[r.oOqF_*0\3?O6d,"2s;iSXGs>tEM6rh#l6).
%Jr*=0'Q$>6F[&3/L//93n4HU/NI41JLtj1-3Li$E&P.M2]E`aMkV5XW[@88=7_YQCcB\s2fI/<`_k<0!dQ$>)=9hH2os-QiOB58^
%/bB<UOt"MLEU'5/D@k)tDI$>OOgQLCm*s8.<Jtf9BZbQpE3E:")onqhMHjc$Ki72n"&opmiaF2k:_he3Q#0Z=D,L"GNg7KJjPFGg
%QI<BjY>."uU3(\,R*Ka!K!M-Va0*\0BUIGGdRq;,NU:/NFLRfb7>=.W8<V2pW\ShC[^4rcT&0smo:G26La\j2+r;^^+9QbL]bnQu
%n8C"5T>X0[o.5f]m;2jkV4KTc,9RC]dHN5g4JETA2bBa?pVm^in:VruT?oBVP9tpPP@1]AWfWEfY`N<8::5U`0XV&HZijup&Y\SY
%1m`k6n&=jlohC[>iEYIsC5!u5P<BUX1HpZBMk%ft);na@Gr\.eA1?(1j<C&C(nFT0n91m!;F4@-.>sj:BWKXr=ciAGI-EHi7?k5`
%E5c35UTqK8;1b3Uh._S*OTa.,;,qk3$_53/Zqh],Ltd%*PDrN-[9kasM>8Pc(57<E1q<dB#[u"&+ri^CZ8-Vo.6u8n^e.4l\Da6^
%q'>49=jFQt+QPY&aECj>>Y[SjE08@g^(pe5YrYQP+]UuhM?^j,)jtS@'IJ9oCQpE/4;sn3$-=['$)<#Q(aWKlP4tUU6p,Xm^ZS!j
%+pYP7FY4Ylgcd4;`\P!9QN5?$'YDc'auR<P4XkG.;S!kt\0_>',rQGZ,W3/o]k*^r!5i<#%B$Ir6mnDmd(,ejMf.Ta@<XC2;eX3j
%D0TlZfmknp!moo\"3#0jr2Z)*UfX!h%9'''+`a7^IZXpE+:8"^,pO+i/C$;q&q!7r[(s;[:`@MSKVpnQ2]jq%Ra-D1]bdWT8_mc$
%1QV<bMT&UjhTs?.KsO'tFHsBEG]GU&R:f_0\O;X5nk%-V>qD=0a`8`J$lPFj3?L*m/p]--1,a_*V'%Ea+RL9p<ZJl4#ncsKS4(FX
%WRFlJfQiQd0"-O`;B[\R#b=n&/OKiDWgjdtF-Si6+W<ta#f8`/1oHHJ^G"<1.PcnohT:N/3Plk.J,mf'c>IN`'FSL[lLLmK_fg_=
%3]n"/5`u?iM+$\V.KmedeKh1N^4N9=aq*7;OEY6=rJ<qlIBZ^*':tAS=\djiL%((e/-pqXS&.6IRj\Z`\&7QS/KgJ`7W=mu(Ss7;
%:.\:KAYC2>_!j[hl(",ict2(p8T$]JQGMGhN][N>1H?_u%IgCEC;>dBb(c;qT^N4s@aHesFg)EV?doHQDE/nb;?cP%3+a4/l9LGm
%5c>^Po&50d&N3_uI4[WS'<l-X>O%*e%(L^GI@g^NEedYk7240/mb-gs@>&Q^:i7[\Ccg3>c2fhrrX2?M.$S&Vr)\KkZEO@aJV";N
%m=O>_;LucVXH(8iZ>B1b*6f$1jWr*QB-P,%1@Jt)Z8>]]lHjg*^?,f`a`To_5Z/fHab@kt[4TS(`(\Rr4q%9;K;"5Q5o&MBHdEKP
%I"8uSYS>sS\6t4[2r:`cMrh+$>UQu$9;9@\<H))<q"]fp?ss6A3L'auYN"3N;_<Mu1Pi^;LH-Q=KO@8O$&co8#u7]tQ8>d@odtc8
%A0KjCJ.0UQC9_`d:[,lf[/aXR?k?GkIZ+8X)+i#uqN2:%aBP9l'Rb>:W\?3,RRXSb^&.=*=/gek^9]NZY\2Q[R-2>%5.`D/?Q_'>
%&S9>^jJ'biejSYD!P&C(#-"n59MrM1Z$fQTO4AZ;=>L$Am"iB\N.Bdg11Wa^=`E<@oU,0N!:a;_c7c(W)=q(ro=5\(3,9lL4nq95
%kc3,`&`s7D):PBl.@Cj\Mm++"\(O+MJrV$6#+g1DC/-X8%T3ls&0JPk:MSGj'+.OeUTL>9]iR32LG9Lkjh`7N1Hr#<N2(o*M)aIM
%bbFuKTa4QF+uT2=<\baD&-[`;Q`T-t,ZIrqAsM:j":4;"7enDW%Z,iB(TtV[AHBZ!p6X413XJ&/%fEo7Kb]^:egp3u2%0pi#-i2H
%5ZOg%$DdF+ddt@R_Hi'g&3?X<`E#Gp,0rI'&qh0^jAC`UM415;G!ou40NAiG$B-aeGSjAe^!9_3<CdcObT4K<WiTo)b0o<&9$Lha
%[?7CNEY0I%2'Cu7"laZa)0eD/,"EJGl#b9Wjuo0,P;P:-d?,ti*@-T)LYZ_PMor!G]b[K-m9Ftn=,5,r&U?mqS0FSZUR35uG]Ai#
%iCrArJ/hi[N%/.#p^>Ls@G!qW[j*"9U=9R&%e"D]+P="m6"COuX-k>.+^T[3qsS;7YntRePmK?9:*!T(.$gdA#b.rOjJJ2-*(f<C
%(Zm00$dBgU4nK`g9sr&KgFHBP&m@G5\8u,FP1aDj:\\\<PebKekhR/%2:K.^NAB:%>fWZgS3.`hP)][&%RPhm;O<0IGPJU588b`I
%/Rb:0_%[sHo`2bh1Y,%X59OtPb0Q6)[$8O'9uZ[Rb8^i1apoTTCfhO2=VsK=$rTJ6LGV@*HNXZ>DC^Eu.+=GWhVX>q$Lj`/W%:`K
%:lpXG9-HqN<$R]G$3_YW[Z.aLAskk/(Vbc;G%PHPQlnu?ffg1F"E1^MI&nOT"9K-T:^%4?85*/WL`dIRi(,ALh,0*c,H"ZXAdCc0
%[2!HZKZe-#@iZ3*"5m]f@7!8a&8CLY$NqDY+a8.r7\$n<:Up0s5A"Os(on,kqgfodGFc0n+D`=a`p,r?<pCDlD55!14:?596NKmD
%_=B#OL.i/>2C#RKF,>N\\9BEC(!s&*::dNXl6)-FU>L$Pg9r+5G(#7!7tsO^ZtpRA`DmE;1MYj2bfA_#c8!TI0,#uLPE]ba+fB#G
%ei"$;L"pt@1X[^+d6Y+F?$rjMDN\e79J.sfXB(n]eH!t=EpJ6^XMggo,l!Xc7gQBd\:b%uUoa+*BV6nR"b!??dU.gsOgS?qJj>,k
%mN`M'*f==+%DPl]-aG@U2)PX[@e?[9WZWT@C</VoV^,tG!^;QfB5ig-!q%ZH99a1FdX\h1/V-)Y#rXs%i[hG\A0s8f5\n17NDhF&
%JCp%G#uC[9H>OKSLHt&_4H"k,#2VuQ:J3uDT=:\c/8_F'#Y=A'QIV9INI'2\Go;BA[bg,MB#ld4=YseG]camhAC5&SEDEOi#!TDC
%?u(VC*:U.P/!(=%k#`0t9P#W&&R4Jl"P>e5rNp;!OGB@h0!B::aHj4Jnt_K$#43CnH^f,W!a.k,Uk<AZ(Y1Kk;i;/t\T^bSg;?MF
%?H.ZRXK0s$UPM5LaK,fo16%hK7\g"d>=6><=G2Xb'HnSmOeT+Dn2g33m3MF.,[<+EQIH>,74?OhS=`.V@-4@fJ;f@uk`jPOTi^uY
%P:SPl\Phu?d`#!d$)!>g3Uh]D%TZOpfs!mN<Y10kRoY,[T6\\._*Zh*-mP:5,%H:s(+%=emt#X-q[$*XJja`TZ)UO9I2YJH1@NrF
%AYK=i;;eg8;M@Tg6p_2(iNY4*Hb=Pi!/=M75gRDEH"&VMDJ(l?mdo0f_E`>`3,?Nfb&NL&F=3%",1V^WlpTC>S=hF?J^4jam.]nS
%U:cUk.3/*m"eit3T*9-c6BJkls.7??QN0r>P7GN3!3F)+]]_e:@VXlf4]*R01PG)G7dIjH\"m0#.*k4oKGPV0![/$GWKnZ+cn)Z"
%4fegs`jm7g0R4-j+m%'AI=5eF70-9#e1tB8aAT:[OO.+e+O<Z+Tc"Qd1)YakJSgi""N(J.E7uI#MGqNG9>fF`KqQp'e[:-#(rJAS
%:)ur_cl5O4PG</+'nf<i8-lumW>%tnL"-lah(cr,]5:@P!GtUK.VJ2bmep-V(F"gQ@S@G4<Jg+b.`l6?7Ed4U+KO1PI3H,%\R'tc
%K+ruT>I[^'W\TlA)/Di+AYai[^d5g@Yht&^%c>[#R4\h<jIs"=[X:_U@=nJO_@.V\@7iT22B^M@F<=A4a?RmIP>n/('H%m!+@!&h
%c,GN('X`o54rE?`9"A!8DB!&$,]h6r'LFA*8\0X6F;6hS02C.I_Q#p\pmec*C=Zgl-FVO*[Anm\Km=HHhM2+FYqPAMo2IA)D)LVO
%cV_fgLnr20TopkeU'S)NMI=aXC5HWcaVrnCPc09DZ3s&D&7^H>c;M[!"TJ\J%/A6a69ABPXKFpnkm8QdhE`Y5!9KkT`?HV8;dSNK
%W]Y#&_EYuU<bqe>!t/Du-2:NH2-D#-Zl*QqdoW1e3>WJ;hHSjSgh92F<9kk;"-d]c_95H-%gs0I\C/Dam&LVLWUal44qFEc6OA?h
%(93XPjXq'a'rJ?:_DE-:n*/)]GJ%Q30r_R\lOa"%Jr!Qin3kDtB<;.Z@e.[\<rm:d@?)ot-u0_cP"T7r,'\$o-NPZ)U"<ABgp$ZD
%d0D<MEJ@[cQ;DlsQW+g.(6FUoY=&Lt[1]bjT8c13cGF=dVU#t#3Ssm8#Kh5Hg/*m&$0'US-L!:BVBtb!6AE9)&f,.H8SG)*aM+'@
%X]\(^m6^.>?9&=567u=(rMlrR!er33.'1uX@I514G#P,Y0B1g_@4?a3LlmJbY*p<9@a^>hP)H;nH,Z>=i'B/b,af[!TpDAPM2a$b
%l(c0BKr!*Q((LIHoV;dgk=CM(@!PXqE*<F+I#:&ZSCV++:LEj]8,IY4P,=S0D+VB\C,oWmN_)mC,N/;KA1pBeGMlU:+KW#[OBNgW
%7P`\Rai+QCMPa5\6$X9!@AkcS;3"7C_*k',8hO`$ndZf^G?@s9`ssqg"U`fX6C`jpfr?>aX@,"6[eKskaq4+N!q%Qqg;VEY@[r!M
%$]l[3\QSGP=eg(tRMI\RAslO$\Hmh**tG(g/9Hahj!4.pH\%%>"T&q1]p^!#.+;.jY7s["bInkJ2D&;u"<)ssU-2(RMbTFjZ:;Zd
%=lgB23HNuKcP-#fd+=gY^1mpjW%sSl1d6a-211r--fEDp#Unh(`]9`(5"[_:LP,c3!05uf4MiG_%qD*%gDs8g+BdJB1bi]/7K@U"
%DVN(J3h>E@$`>lPHEV>n.l"RVmu_Eg''=[%V<IWD][D7%jF'Ugq2!+D-'9]"+Y>?.nH.l)#D4Uk^!eE]!:u]sjbmd>=?/1Ti&Tnm
%PMI&.8sr7FABO&\WG8F7*i!>Vilj"2a],]GntlTA>@mC,QTh*(YkO5H0t'%KMKiO].PZ2lP(O/lOjA&?=,6P5#+!JtW3g-M,Hr-L
%4P:?&18E9$bm5CA6A>n5E_f>3ReO?*2%--'HCBC'OS]OP>q=ROR;Jk`hDL;2^*Z*H^^20(9(['gJ9V:Eg-$dn\"tJV95)4.FasPT
%.Qlbcp^5([anSmC<=>pb]IhS9g:&c?+p^NGJ$ifAfdKjOo`?hC.qTEW$WblUKE0m37k8?>n)Ao<!hlQgjWcrK!7@2%Z8(TBWYIqh
%O^)qj&Ob#cgnChmMA2b;.M3St")*W?lXfFp<7p31c9a#nDNra:1CVuXehetOjbW%UIT[WK,3K,AhGCCB0.eF$Eb\U9>YU<D-0ngt
%)3G8rOZdAb'#,38goKX]3D5/7Ll?%bLeK?dWkhXrpB8R1,S^^o'%dlW%$n_?g'F)n7ak$Eg=o5CFpB*AcQ+=gVm'QuKPu[+-;jp:
%4\8V#EQGUcPD5%E<4,7?_NPlUV%$Bk.=p@"iCL=%'Ib3cGf@L`XVP-HPPj$KJSrZM<5@siPq#=Y)&L1&T:AF3?FRe%!o.q&RH061
%(-T3k&DTW/_X`g[l'hf#6Rh]<\N/\Z.%jQF#3obYU/4nOr8da8P>dM9l$EDN=Y&Yoe**G-ZHjIiI$k`RS"Um[`+Fq0,Y0K\+tU5P
%bKafM/Za^i99sk?c$58^/P=m=<86!_Y#2I4Ab2e_.S)]FPB8!>&9M=hDUIL>n4$;8s,NZl[*LOR%,6Fl4bNme$b4UF!##ee_l\as
%#\<=d1q-#%.)Hs6F.b]>2ld37DIFEKVGf&iq'lnJ-PAU'kcn5A:jC_NoJp*kl]en%;#oA.+q_bCj%e=?N>[bGiZ4sDBYsEh6j]B-
%@'_)[TI)i3.%46j&Y,OG9:13(m4+C$EZlaN>-nRgasR5gc9WVNI4)?f9K5a50JjS(f`*@(=V3@(Z<JJA1Eidf*p4f7XS.**QVCIK
%,#ZuPTLrAaj?!WMo+2@$q.2O((HTGm/WG,m39Q)qVIF%V6IWIb4Cm$`?:D+^c3d.-C%t+eVuRJA_Qb-B;%?nW-^=*CF.l:aZNr,a
%pS<<Jb4Akpm'^<2EY1L&72Q'i5[DdjL'U,ch2YbOb7RB>$_ei$10iI"(d\L:":$P](LpD8/?(dejT/4FZEl9@(UJsCkS@,:8ui8,
%:CbEI\gXp+"uHlZ@E_U+9]_Dp*3TC=fj"[b_?h-titIe%6WRkoE-PL/-8K/"4c`dTh!^J;KOLkA+>fFX>%.;,@[<2Z&(j%rrBZW6
%A0[VJ=:0ikSgPj%#KI.AN>CKA"qWHo)'Hja^1[mj6?8Ib8>tj.e15!K@NjdN:kGAdHS"7-E)K"f2G(&*ZWTU#;HFKCYm_>ohB<;-
%'7u;MOjO]HUgUn]$p/`<\T8DF+$iD/d\"$n9-%tAXF7G-HJ;7\]ISMc'C7WAB8jpPGhR-7^Q_!-RjcK7R[/jKCN?XO@tW-BRSLf4
%$t4$75FnCip'3&g+HW]ZqfB#FZS8qI3>qqAP6kOQf&HCD^@HWlE5=&iqODV4aSLr'dre"r<u>d&)1/d&H9%R]="1'$`B$e&ENirm
%WC9gD%U27&@@[[^?47\/"JI54`04h<"?l,e6:b0D/96fXm4]$8[jkdWGk#!7@SmWt(@t'[PJPp;[P>o'BoVa-a-,@MBfq!jJ\e;0
%_HeH^,]Cq&ll+sFX&2V)-^Hg)KN'2//?'T!EmhHlXLhGsr.A,^Wfh9<_q,e>(-dC51<<caVRP3Z=:$:6Z6XT2aXVfcW1ub4;/-)+
%4::7d0ED!V;"4ZSBX3=n-8um5608SnXMSg&=#R/MFI$)QBK#&>F&][=)JLRh`qODF_YlF]Ubu(.)+k;68-t#:a!@%Pa[?l2'V>CT
%+EV4E%<d*hL1.nV:-0di%4L?t.d3a+hH.h`P8U2fZE3<,8c&g1Q6c`U%qdAc[W539k1$]REZV=aG'")IF-9FYo?DeUSYPK&]i]ZW
%=-*)%?$oXs%EUqUP)WSlW6k!Kjc!J<?"28&$?M\M,$m:<$ZdNoaXJ)P;:jnq7_s>>:f7ADk%)QT^%7pU9i;mnfrfWO[?(Yl!b_WZ
%7.i,t1E)R69do)6P74<prJW$oC-^/":m_X<e6A<e\T(=*NtM`l'Tb,;ii4cDm_,>gUKUPu=:MPP7eiaSKV6d;HLWo":f95k!$]$.
%M/2!\`[OcZNWrH7m.\hp@+X$V%su<["ApbHJ5jKncLkoUYc!#bi75(&^f:YH>iS.#9Yq4Z++(iKcj0r#VFXJfhJMhCHfXPddK:J0
%O1b,4[b&!i8YM9oZ4P\H.0Vk]nQEqV&f,Z-Akk>D)"&+A:5mf2:Pa_-J;lZ!SH1Lu=49d7jHNtCAq`TUM]b>qH*<EMAIL>
%(gdj$C'fJ(&(5^i7#$b#-an+TguAZ=jrTj7*YDq$1,`0e(9'-9+HmaWJnA#k7ONd1M!!CL,Y=":dRR.LQ8:O1@=qRO"sPaO,UTI?
%=:Qe%;RaBIV-Z%H9sU3eN##RD9].?Ic*(T=j*1="Jb<aIZ<\!&CYb=*G(\BJ!Tn3A4'RF0@rJf5f_5_"Ach[r;a"V(ZIjiO0$e2r
%hA]1Od)(mu8l%bEaO?^s--_*^Fs:M1OUM[?bVM@lVREQ,Zk]?W8TQU`F$+CW3Ma'`]"]%l#BXoqb#sf_N1I2U*87b*".38RO&#8f
%&BPHU]aQf7]u-i7+X\6=qsI&fj?F_LY$rn.@k&&"F@\F:E(4l#AF0L)oiMh`1P(=)6/qD',i`GqeX=mf.HDqb]MqJ/:Iq`WDZd_/
%0oo<BO>o?d<*SdWm:"brM]:bhF&5g3>d1=C0mh8g\fes;prIkLc]SKL`+-ZN61c_7Xt;sB!4MN9:i[:@`s5MQ7&"*uliSu40U3Z>
%DU.1W:?+?"3c&9K16H!tZ'_C^7?.IZ+@:!fr`s\H8?Toi58e"KEk[=MbkIVIJLTbs6IV+M-PdjK97kGWcF([\18SQ.TOhLjVh3G1
%8$jHp8J0dEWn$h6j8^F%f/ON5*&\J7G6SGBVPo4C_?QO8d3,+m$\K)NZ(*h]_4<"o!#bK=L#TINfITBU<d>Gr`BG*`mLJsrnSACW
%>n`C,@Q@"Z"JKetYg>n.iBkILNYSJam%q=r3Kl3n:>3C0P`]4jT,SSuhG9Qt?#s*Np;0J5aC,[:0+crIR;N#1!/iuXPDo!UYWV"i
%o#P7sGX)*Rc=k3&'#p03CQe4V#DjJfdZ(9,YTk.d6IA`JMD0[.O?21O^)EPDQ]mVicQ12q0^;$mQE7EF7Np9iV'qCj'5H22<HmRg
%%e"J5qU/?ik2r[!N`*["YtahLm+I`&/_dLIQSLpE(!]Fq>Bn"9Ka`1e4+fZt^-^!4MrhbMaa':f]uY7ofPS&JW1Me#kVS846NcPT
%GX4+U*8H\eV(sQoPuZ'K8UXSd?.Bp:pn>iA^Tk'.S]i(Op#X8Be:B&p0UV8$cR3:hR9N>-=;1e+Tq_V0C8o&/B.&M?Brd,#:ViRc
%`6*Ffmjl%NF#HqI<q#S?`Z%Vh&Y7XL$KmnB$l&s0BUTBn\;;'R;shc3MXQX8mVVb7R$UsYfhm%#Qc@i&OX#hsSk7VL]g4t4&m"Zt
%nkn807=^Mjl=Ps?!8em[&.<EMAIL>\[nr]3o5kRYR+D$K=+fXm2!?/T;mWM.@5$+*CUPaHK'^dt>)N5e=`;>G-_uoaOJX^"7+T
%Q4oFm;jXk-]d/r_S=YP;TXI`F_TDjZ:2>b]?.%jFapZ$^.j7L`kFrs8XF;!U#BS<<3Dg5*$[`b%4C6W>,\qD=-qbQh?hqbk<4*(W
%U]J5B:8o%[XM07A]3>_:bp!Rj:",RkP+6G*<iM;YAY@bM4+V1&P_0=^p:Rg;=eoi/qhG7)$YLF@YQDCe\CQWTBgIB*$XZN64M'#H
%Vi_nQ3DF"j0"!l.,%ShdQa7$=alZoG\c,_!U38P7QSc>po?Do?=<NP_Z5r*tE<Nh]=]/bMecqPQ9`AC^9X'1CJJYoA4rY00j[cqG
%H(0d=#?OnQ']]PYa]r"e2Fb0$%=WBcHk?\u%a)tpKgsCn8<03@.J?Jf=]/BTBh_`DP,tqH<Q=YF5W@5:Z_P26Gm2=H')Q[9X6AC/
%CgfcE[\?t1DTIt2\K]g)KK`"V-%6\7F-D;DCL1im0<FbC/hZ,&M\O9tL=A%"V@F[#,!\k#e_",$_LTuCg5qg<ZS^j)&ecl0Z=IBZ
%(%Qfc7e/lmX1;r2;\o`fBW6]?:aFO(*'HFa,s=>XO]Wh7'm^Qfdu4cFQXS07Oe-[8O).4]MW;QUF&A88/^]*1(NOJHOckMu6;**P
%:*m>O;grllX=-@9,\I=.U>Uu]k/$5s&0l"B2ksnUXp'bG=M/AF/Qq,F3>E_[N""n6IPoFgNC=m4K"3j,!n(@sHM&0fe&"Eb>;+P-
%B*5:^P'iX;j@(?11:WjjEGGr&1f:KQ?6*-SOf63KX86)_JrCjhQj.OD$`"Tc%qA2M)1bEeL"+*XBh#g28VCY#49@i[h(m&m0*T.2
%6$bo?rV'!:K\i[Fh^TF0(.R]`KP5XQ=`!%m,aZ??$!l<'BWlC:WsfkU7$3`989'[<]q"(TqV6>[0ol4`!R_TSljc,7P+#4h=;%.X
%_Fh_OeM."70q6+hh`Q>t=%:KdejA&,23JTs8;^g;_5WS=Ce+Y,1/`YmPR\"6_\e&3AEmqg1;Zc:/?''r4X;P[i"hB47hKW3K[3K6
%aO1[ZI*G+u8I<HY>!.5m2[^D:)ao"8RgLVqDb`TNH6j:,EA3T->P8FPQR-7;K7Ad.)DHb`\n9PP/4=tU8J*Nn<WG(VE79ZmMY=k/
%fEI1L[+I4;OuW?7,nM#YO%T7Rn1:-37X1:\P#R+B8nF=rWXG8A'hJYO`TPX0`5cDQ:g7aP48\`0Wg<!.N!W1%!<tRbIne:;=2`c>
%^]tMf/Opg4ITN7>7Ts>O]*:B;C0fDlWT2OC7_GIRDSXjsdRh:/@k>J*Gs$HSrmJ4b%*1[_%TUY#a4$&Djq=sZ<.i$ZU2-7pH.d/U
%+M\b0"';Z?i5)7=%mg6;bJTpZGh+)b'0u'>%m-Wt7uVA";9FWKI'7)19jg"0m6=u-Gi1?QJ,mk+a9,6WXB:sb6Dm.F.5/OBK_:9n
%&[;MAV\f7SZCQ]F_W(>A#k!/^M>.3)$H,pO86B_l"jMs!^j=L4dm0u[`u2c+9>EZhe.<@*>a$?6q/usN;%+u*(7D8fAORHD,&GJ:
%Qf&>85):BV?5u"b.3#'up":U]kRm_$@;a-d*j_XEBlk>+3Kl"GL5mX52M(UO.DVT9jIHr$+O.*k,q-#H9h#]Q'&d><GZtsu*>F1!
%!)pOp,Z>[Q1;f\/F^ktfXXpf3$qIt%O>r>Q;rYkGCNB[Ed98rE#kNPrktEc:@"+k@LaZ<(8_?+9CBt7dieU%%C'@"T6lkCAH9oUS
%V[Q^_WM[K'05,P8Fa%2]/CTQ2:ebCanCOY*i.Z5.+I/*P*#87/ka9&Cm):p"iY9h3/YYdhE73ca$4O,6k$TrU!;@utY<_t;ME)n*
%b!>JcG\RWH$,md*Hor$O,g#D#%jBpr$M(sO`dLLU2%E3#fK'Ml^%esrQ]/=9``S-6.g[GbPX#`?QMnhG_sG)tY\*:1e-K5c_27gB
%K+'eS*lS19<)Y<W1iY]I`jJo8@<po_0Z\d]-!3q+AMHW;"Q(+E(A=-A7`_SX'9ts7O>7P-#+<$*OtK")C*5-b[Nc;%.>(@?#::PG
%b4TM>o%!<]S#J`b"N/7<^d`0&][6.qA=,oLB-f]H?udAATOQ[XVg6^PA^bNUT'r%E"#`TRN+3ms.C#8U)*,X:'d,J/]$Y/pP*=8P
%TJSnV6\sMWCaKuK^lDG-o.e9PgmCY$"Kao1&4B#TEX/rQ"E?["QNL4rHF?r-HIg1Ue)OcEDdnrZGBmR5Sjf-5;5VWU@df<CE/\[d
%OEi@%mQg1"\YH,_"C!N8[=\2e[(S@"[8.[Gq4o4g*`6)H-g.^n'r71Z&u$^'-@I]3Ul,C1<5KOgVni?:k%r<qfV97QU.B"7-@d\K
%"VhO5[L/E-iL%MH?q]P=/;u4ifJC0Y/@1KLSfF?Y-p=S^TIQ89P`R_oHtGIBeMn+N7O!XT"W"jt-(`HLYA/2&b9&72KNg^E#MLLT
%kN1`ZMq9)<JrIs4OM*<F\u/.sF7..P3:mBnP5:VO$&Q,(hL02'-*2:OVjfGJ@WYk'DOCrpo_&MJIWG/<nbeU2c'QK"]8,9N4M!S'
%n+,Peq=0s7pJS/jl;J6],f^hVSh5(HdDlelNBf5J*X3X5FL"m948K\YmQW_B]0"kSeq`F$VoORj-g+/$hnM0ur3*eU!ET_+^\?RW
%e]lo_hd7o-.;kdRo_?timG/5\Is?!M=t>WfoDXr[a2cr4FOdR]a3>Fh>s:^gW8O6.-'p*-A#]4An]bF/_qR"uJ,1sn_uG)Z`ua(X
%%qKacg`aishp\M'Zi'K'MsScFE;8Yd+9-$bO+1^OJ,8e='!s*<*CmrW=`[->`<eFYJd+lJ>D(1Rr9om&!f!dN#nNV*18`qlS<(b8
%7)jD-JeD\#%(dYM0tHQiF]l4poaTb-2W"L:9!QVMq+Ntu3MRK>M+V?8%l^KqoQaYp#?n`I1k:YCF"p;S87bSsK454>SH-,mMOTp=
%dE&4<[pQSO[Q?&r2^H,GaK7KGBfqsRYq!,$<J+5P*3"TL$b8\_Y%l6XVk>CI$YL,6+m)q@A'bAU)=AZQN]]lCoUo4eHIr4I7Qto^
%$QBD'!l[YpI.juYg385f^--flS3Wt1Bd&/;A$cM%kUenXV(-!9EY3i$U_&re"()uf"BDSQ/8)%$-]<sf!$<aC'S(?UU)W%QA/ja1
%3lI3!]H6$s,)CGo42<_K2ds)r*#_M'W:on!&[9(tW3_a;-bWM^Lj0uMh9^J(-4IuGSFB(ELZjo_'PeON#$:"T(`=tr(Ed6N/RAG6
%$mS?Oi=[QH/kGLlW=1m#F@QGj'ari+[gKfl)(Dg-V/C=k=8K"'>0PZW@[O^t+BJf`Y]tnDf.lZoVXJ*,6e2_XmNE3D`5(uf(e_MG
%K3#P0_A/FR4%FGR;'<%S<KC9+`CU(Y!lm?['/F9B-W4#=RpBRj"tG>:+WIN&="DuO0DsWUB^IQ@m>MjB=WpPu$cn$fJ>X53U`j.N
%bq4:)67<CGj]X$M;-(SJ-C/kZCVkT//JHqYJS)jR68N-9]]C'W?7b9$;3m]<l#MH69:Is7I3p0iehDaGfreLLQ]A`"C][H@S0FJ]
%Mu>C_IH2TR\BH'86(k2XZM$#iSrJ.HJSsA\RiuPT%lHd7@ih)?k5RbsVHd[:Q'N,oE.DJa)9A0`N4_Dq.Lceu&7L,A_eU]*YA'@T
%H<AUjA-q>9MuY.Ek9CF=->#XP)CZgU=QTN0.p;qkQ^jY$Tm26p_(J4R\d+tPp69.(D`QYj.+C;f+qA7(g!+3*;PgZT&0>Hof5-A8
%ZM8"&$_5Ek.2IGd6s(kB1'"T$!4Spm^UMK:.P(qF-\CM"'.g^GIEOb)E/?L$&B5.b-YLGY9mkf`:E#*-9>E/L8Q$kU[,s6,;:dt!
%DiAq=3H8!7Y3'4pS5]*nG+8OA)Pf]iG@()H)6Hn@a:3;"1%sO'ZkBto"]V.YGp)m`-".Ai=$iuf_&ZFbLFPqEk%oAF3FELi7Sh)Z
%N3oA]3i)WTNY39"g0CIj\dsBo0*:jE$NOknplH18Y!Y$)O%j@D)'iiY'F]UNTI%nj!rob8N3m_ZCLo#H<.W8$7?0Rmpnh\X,)^9*
%+XrT+2t2GoMJ((p?ri6$5XZ1g2QF7<.>F*M\F?I1[C#dF1N<sp3QGrQJ6TiR&/ZjC#Re-Sd'Z@=)40`GI(Y&CeC0oR9,X4)!hPF*
%`2+*cO.?RLK/tRS'!:foZ?WE4m%Wb`0IlXUm\:B%Jhqp@ck72I&-"P\221:"1?rEQE+)-:_oIC@r[2!5Uajk<WKC;:]>mcZRFsN9
%]J&lL(5+A\+1-];LtiZ)[tUQ(O#)r3X9D*31F34:_d+!MhcSuW0j_D!$&^&3>_Xrg=O%Y&R<1a?:5fjadH==]6dO-cUp3q`NRe*P
%K\f<,W3o+H$mft.`s]kF=g/m/dg(4<4j5;jhL6*aJb5c%6_pl=Soi]O<2*Vh"p%><(1H<dmcrb1[;m'l/XJ8E9*o,qi#s<j#\Wl6
%`/KHp$;KeK5VpfG[-Q-X+9b^eLcIX"em@;'9TbtO-.O.=,8-(MVOJ.d."N!>3^1pt?3WYqjnQ;5TbjpHntgsPJMG84#mp[+HUhrV
%YOFsWJ5$,&WW`uuj<ajo*po#r`Mn-D$^RU&'tVPf!"R/Y/E9B>,lu\2jJF8@aI7:-ib>DocHZ\*`Mi\hgtKWgJ_Y]#Zn1KoLr;:J
%`pbJN@f28uH!5menOW4&U^]_.1l@tDbpEs/$0Rcb1:YOV/KjSVPfO"!1G8!0"I0U4O`%1%M)rJS_<+&?oBc@3n@4e26@nlQ=\l,;
%iZQ$^;'igeS9h%Y@5hXVK+Cdf;JLc?KH,1?GOOWbSg`r,?qTtpH5gC940(D3\CJYH=n$C]A!_hJ/K_Th*6pB).Ip(d`/+'\.Oj6a
%B^T",;n*#8G%,BeQf\KpTH6Ub'>6_7eqobRW-W,rB->:(B`&R08cCoANU83/aRCUQ*\doY8a3UtS-"mi400i6X.\`(M^LDqhCB"E
%Car_OXUMBdSFN[S$c3PH(8s+u)L%5uDbKA+U?XE<^9kaQN[LS_o-lV)itgZ_HP@TqnDXQna:eu-W=*/#=qEL9B!*^+QSN63pgg*F
%f!<7-!Ef5>1:'T*.Zj>"7Wk'YX?0X,_T8NW%1TjVKCorC_>?DIZ5!7K?jbnP+DddF9[YmFWO7mFSO1`RIt?&K"Ajc,7sGQt+Zse`
%;0Z6*K8W^O.aAglad+u`7c;ea%hsN;\NN3SJ?=f?oaQOEQq+ui5+eYf@SuNO>7CEBR=f8$.^5)^L8/FND2FRLjJ9-',(*1Leh\_>
%e7B$?%<Y%BigSs:<GM_9%Brt?M&;AQfP9\iD1koiEfeLUD.ABPG7ep[=$;k;Tb\b%Tk?4,]hDXoBeV/7"q:9']Is6l8m)[Kl%!09
%gRSI='TC<><>GWu."j"ega=H\pd.?d[1c#oMI6WXg%rMn@uph[gq*lC5dJY=Q]KWkE92X7+U_,'`ssR<q;ZumEL`;oD5EP(%(,.#
%lUc<;aA<3/ED2t(*J^tuE@VWO^?Au.C37<X`J6?n2TaH@d/]l>Y%3%%5`f7U0=0qooNosfZIMQV]#f%dbs#-KQi\4?/m1%MW@9Q"
%?pYW/Z6FK;g%CMD-'3V;4TV5VCed=.h'^<MT#G6&'4C-6%+3sF%pgs"QAq*OMU$0]AL:Ys;G6Ns!,mr,OF`Q';leQaa8oUu3Ut17
%Ju^HD&_M\M@LHkJ8qZ:Q/eJW+kQD44oD5&/194)<2-Ah+>tS*:=D._8Z@riF.lL3_rs>Kp9`onba<+bT_[PT]!X*1g@MB*OKI2ek
%R4.gk-L6b9$kQ$fCE^>'5X%Y5HJ1*Z@[p=5!5%6NN5"-&6Z1p!HMq(!@h+d&"6?m_^<k:ZTJV'N1e?#JlTC`#<eN'(,L(,1=E2Fu
%GrJ9-jl)pp?Qjk!GX"dP81r!Oa:*Z*Jcm8O0Nc*n25+HK=!:-g<*bGV:skB,0WEF`"GTJp`WVBj8M@kD(-kA4`i6'&*l:<ji>J>R
%bof'gYZNU[0[",W%8P3e7Y7rt.)Qu3"f15;s+a8XGs\!Bj0Tss1EYg#%>l;b8KT9lWf3_G\4'jaeTR@XCPc-j#4mbH)q]CcQ&ATa
%Kph;SgI08gl^:^_FCVtE7H8F[\f,QSZB^i!C#dPl8tXH##[_>"b&`WGcRR-jSt&'ZO`:V:D5]$QKG+=$Oc^)<F:e"kNJ21oL!ZT(
%AD?=ad6;=fkiN"HS?P[t0DsT:@X8Y>ZCqc=DFe\@EOuj#7p8$DS?qMPoI^M$7;S#g!26e1N59ViKVGE*7Hc$[g5F(5oqt:Y")C!b
%bJ\o[N!"2#_1ol_d:3"F]#pGK0'[K<6m5!@6cm?d[SP;p\lH]_;l,T,Z%9HF,j3/NM5S=E,)$VX0es?aM5NVt%$L$@Pd&q'R"pZ=
%F4SH<YZVX^-h2VH@2roI&O4[Bg.@:Sh!H"Xc5R5FF#/cnpCNSp[>[jFRMDa3YDHj\p(7k+d!"YZ<ZPDU+V[rCWe?*a1SmeiNaeOd
%P4k+J!.Y2LY-nU)*ljjF!EgEK<!$qU*+Ar^WY-:SJe9IbAB7!t68--I_$#9B^2:cBF[2[8XBp*OD@R$l'IK#pkY1e-9K91'RpuRY
%0mKZ+%*ir<o,Qn04e)qd1C95"jVIL#/;1Rc$MXTf`0=@_*Y68"dtX,p2C:14M4_RAT9n]<EMAIL>/%lG9`N*OJ@V)`SI$O6IooQ
%S7.uSb3-GPOIC;RWsOD`)EC1tAjd;os2-'944sq(K&\XoSQo0)/K8_NW]2TGSV[5);m^o2*iDgr3oa$<SW0QM+lO#gP:6P8:+_&q
%10b$T*O-8)eE0`d5Si0Ci=Ff(@1/fPI[HDO*B-l;9G=f?!jseO?`LE`O_l)X#tfFq3.K(+;NW5j9HK?c.'$(7WP;?C,A*K1bH;Mp
%I_s(`Al&,%KHG$2ctZ&a%tX)#M5*3K[A1@A%?d=9lb1DUm>1JWai0]>'p-kiJK!O!>%Bs3_ZfR`UoG^H!s6Gu#,`6)<?599a@qF0
%A/="r6>u((E+XA03s??/HU-03lI(bB!2)fDanI/k-I3B[iq+^:1[AG:(iqDYgH)_,&_KYl0akio,sd.sl8[YlK\i4#a8rq">1)&k
%$&(['p8\s(OMjK,ig*oeis(FsHX^"1<h]CK]ilf)mh+*Vkm#M3`5g8i7YEFo#HW5(1[[H/9p$mD/'HUe#*f5?Pt\03K,*0?JnKgl
%:cbm(+rrEeY=&t1paRaDcjo>PgrVK,5h#`,ZS4uYq([ZRX,H;E-^Tl)[pPb4*tK/hO,l)YkGcF>>1.!W/<TDJZ##9qCtT!u*PK\s
%rt`DH:TlBG/e=T1S6[*u3k>(Kp@.ksoc?'Yo/G9G-@:"7RQeI_/0*+o,RS';!b<M@/gpjhBe="m61U^A#ipe;H1P`tqGIo*"K!@3
%-4tPT/(P_s)5.Z\#.$OJP,4=[r5[Hts7tf.lMmcsLahplkSb\u.Ygg_rR5dHSbFMp:GrT$MXFiC_C3Jm;(9n\@6!b^nG&kPE=>XD
%bC/,\6iPG=)h#c`qt\A0dnlN%$qC4]&BRHY2,^f?62cTlUcuIMY1JCA)RnGa48.OW:X5TPg,M`+)o?qllBu;e&ZX:8a,lnce@PEU
%Qbpk?>@$k14)`0'o;A97OYk*NmuEu]84+4)3ePPkOpH%!eCoR^S)T1V[!1lM^UVOdTf1(GN=pdZ3YBI=7=R^7nKPbjTSq;uCD2&3
%ni2EZ48.O'c0!JWS)q@b4SON&XPWS>+=8NEf/C!A5IEDKf-ZN!\@lD@rNU0&%CK=q1ePqYH&`d1buQD'1F,n&+Hit,egC.FKQ.L2
%GO3"-Us5g22Tns+pta,!Mekq;"11JAAN4MN4C`f+?5l,SiqADGp\V2j1t9Mt(C%eGk/<ArOq>:X3pbFlp0+t?pA/LscL5mno;U=C
%k5eHA)W"qh#:\98e:I*EJS0#roUj$n:3a#ZjP3Z[$JG,0WtQk,/=Id8DE]XJlH;EjkpO:Hb8\=WcbFcde[bqHpLP@?()8'k2#-"m
%H;Csc<&"ci0'#?MbJ\5`pLM!cp]3ql:_*l-hW.f\(d5aX-X$OG72Zt@ZZqG?Yl.mKZ)o&O!s'4<+gM]E!*ZF0JB:tKDQk[FK^\]B
%UuMfn0LYkF@#,Af3Ej<fQ/\3`\,A-+CELYEZ94%$Q%cXT"Z0X.HZ"q_RYB;qWI[$PZ`l\[Ani/t%hJr-/D&5iN`dD]!XR`0B32,@
%qr#H[2@h1>Y0lRJd1OLYe_gj5@46]_>EB6;6#HgmC:dAPOmZggCG,]IIHpFN`fH+b&.0h:3V@K##,5(5,tQ<Y8M)?r2EYG;WD!?V
%#FYKrdRC:1oiWXI!1Y;Ba?\!X+(KWuJfE53=V)qLZVfTeR!oU([ChdOjXIXi`QST=9']Kb>+,]HO2-_0ma`)@P+97GDpAn4Dm^7a
%^5@WH"C^4-*tF,+J^CjU=fohOK@o7NCRS1c)4_*K9r.IQD(7qIEY#p$T*jFI'nfZpdkOM#?JOd-EK^`>)5]a+:?<TBjOmn!ZoFGe
%csZfKiNT84gHKH'fY[Ca?,,+*SP5H2^tLpQFCZOgE]nj&/0c6+0;-EmZ!)e,#9pb5C(PJ8BlWf(!8kpd)/S%EJs9[?7[q2e6UKe6
%mep71,h0Cn_iqH09qb2^*24.jCn5uO!`;^5j)e!4)!]3=/P-mAbp]?P<7n**E3o/R;2<S1ko7H%DRq!+P&$Wt7G!4R3@?1m2.\"C
%-UfOOj[!lT=gG2=Z'T30'ggY-lHf`$<Yq0ldP.G#FK/Z*D0lr>L/mKUlIc/=0nfLm@19:YUh3_."^'k!'G8PVR9QqJB%ndAVp1*.
%Qn0VH-GA2JZed,ZShMKll,ictCLkrAEZ%lNVXErOAKhT+chqL&nl9F<5HS+m84/:pCQTs7L>cT!Ht9u;/sPrS#ND$0rTL-B7L6tk
%;`dW)$!eQqgVcWQP)$KFEBl42d:inLbIVZFWI,`IX_s)_=;N/pEIOJ1Xk<gE`H<[uh'B<d2l,hl!d1:SkIQep3p`PGm$^Q)92_J2
%9i8"^*624%Q8p<Edo7)C#i_Fdi0VI*O@E[9Jq[*,jLo>JBM^I2kD/"qB&8g&0M#7+dZ.F0BUe!2PZoCQ:A$mHE"BJ_kT.dQWY,?A
%@s3r.Z_Yb^,:Pr`U^@_Z^d>4[JiE?(,.!#dg_\_"9ac*O9b;lmMQkFU3_9/G&&)k;&D7$\Xo`,[=3Qt-D%u'4+Am)m.@esg?T,d3
%CBf(4oi&!d@]=cgFum3F5jLKf;JFGZWU"jKEe\1J@IG-$fUI.2MH"blkr`m^M8#'IUFpH&ck0,c/G*6u&jpC=GgpHIQ]HShALCt8
%r\NEZ5`3WLUZX:?5eF7c^rYF+_p3-,Fj`+HJKJXb6J^-lJ@=79V!VV-Dsr@^`(/Vnk<<DEn>;6k2N0f0pLuL8if94r%fM9Yle"mW
%:[E-n:A6`LDp8PC$f,nBB_"'>UH1$Jq&Vh-YZVnW$IcYLU;g)j9<L;D<i]8WIQ-RN%Z=aDUbB^d2Mq$1m3e.>TfV"VRL0>gDJjF*
%CGjf#3F/.sFD<4A[7NhIiANbIZ,\44G?qUN;4Iks:ZH`N7O8W%bT*M(hOGJ%&7k@'S>d4b"0$ucO!e!3Ktui<Msr)i7q*R5iU!(f
%_`9g#oh:AGSBcn67)l1M8KX3GIoHfM0\qfd6Ne.G7LA>FF&kU5n<r[@&2CAI3_olo`C<.f?mgA'Q)P&4_lS#MNF1?1c:Y#3E)`cG
%Fl50N]Y<GYArdeJituq[Ngb&^UWV<?;Z7BJW2._2;L51.oM;O=/$jB(!LeQA'ID-@Bi0(S/j]]_OjJi#!9<HsK[Y2una$tBdgu2+
%<$3G[40hGYm;g2\X@i^#d]*Y%eK2qO>$m<.&L$'n,]ol^6FPuYET<3pg;5K2bJd3%M\'<Uj6Inqf5@T(&Tfq?,H<n=D"/p\doJ0D
%;_+N+gA3oe?=boV&Tn!C?kQ?oVU_n@YeYJA8r6C7q*rRWkPDn$m>DOC`/340^^Y/BP/'HGZE6DY-Xkd.E.!itVfB%si_T.!F9'uq
%RD(/JX`->P#J$:Gg.>$"laE@5Rcqe2lF1)MdfLBGXuiu@=2LhSB^tVp_UEA"Z+P2h_a!nVaO)g\mLd@U_KKTH3_-24:+M?$PkMM;
%JGBtV-ecBo#cDPZjLpTpXC:^6YXrM@kSS.pO0Y^0o5*?`QnBA=U9m#4XFd6mDKPilZq3,;hM_bFH&6fHQY+)"FUl4@DOMq$n@:1R
%Z7sP:2@8Qi=I8ca;1OV)jf:We][WX1qP(Q44X\7([FmoK/F0cDZPcA9;ij\l-1&b4)4J`\@aoNP`#ccOF1aaBX5\$!O^%<70dkr0
%CP09DE@Z/KMdD$]i-KP#(^nU04;T9gI5N[uY*nILJJF`a9JSZsaN#<EI:eOt;U(%\LL?X-a.BH=(7oWBfH;3NOsr.B8>X[=H.8Ep
%(tFkJML^lHNJbjU.>Cp(4oMa&]eVZ[b,c[5)<""GS6;a;Yr[aeEk"IRg.aI#W)!n`iX"h!aIAAfOKG3ukASjh@MSUsh-7XBfX0qP
%l^YIRZs%kk/Z^%5)dLkJiE-D_4,YKFi%H/5+nbu=aF*<+!7CiSSf4-=c$L7!G8`t^`r[to^[D]M3qhPU=/U%S\_'Mq)E/ETN`i@!
%q("BH$BS>eEE'(G')#Oh\0"^1$tQK0LQ`38o,1%U)cLMWT/"_33>pc;1F?OqqDOsce3"P@9KqLh(og4&)7OB:-c2iWO)[HX(k'DG
%EAbTHd%&)fYn[ml/p"C>1oX;DBFHuo_`hV%+R1j[`4[`C3P(36#2+d[SZ@ZF;I,OT2X\FE\W'+)EuT2*OK"ErBq!-tL%BA.=j*Se
%$2#47Q9lg?7rX'*W!g3pUop>Li08br]>G!nJk5)PV:J;aRaYB5,c"VXblqe2SB2u)NC(a_95P&uRhc<9GLF$r_DnQk\Bp=>IE#-P
%;Q(C+>A"Q7#:^_E<%=Am`Mu2Beo#4ng:)^\349]f]?`G<V%'-UOUXM7;;Q]?&WtEO`o_l<q]CkdR5W*c&"BGV1:o.HmBG[rD&`ie
%T5b"=#0T`U>U[clmQTZea<_*tRMM`d<X_1PRTY':#[)9P-Y4T#@:,87NQ_oSC`812Md]N%`Mt#9003kZX4'%Zb`mh+Bfl_mVfA&i
%jtoiu3iAgW:?)JR<]5js1a44$=k\QUWS*_nl3JI*VV,`i!A,33NhGBcYb*]t[o7SA)Fqb`n0ss`\dY]Ch_*P3KqWjQ8O!fPdcmj*
%I@F[fUFgTYD+\P1cHF^bRmOp-^!a9!VEBBJd]<nn%R#3.KeB<5Tb]qTUh<s]-$XCqFnp<H]9G4-[,\kLr>m4_;RJ\O4CZa8*EEP]
%a(J?I8(h9L*5uZaou\2mB9@'"cX'DW1,dAAWRD9o?C5[kCRA4e]jC#)fEr1JL!"bg84qW!RC,1UE9tXLA"muU7c2LOW&7.qn*k%-
%FL2^\h;3cZ#f;h7Z=02\o&e^Q@Fa0_[h0DP7M63f#iX'a>[8.PFlhCa*5:YrH0*j=I4H;nRnPRoQP#HQBma%`\X(`]]GiVc($+2c
%WMEpIoP\p3m,&fDc*Z_o1i4ptg7j!LgIlBF&-i03*VS<45o.lp't2u$JBt<o:<s\[-=:&/AcR8#Ndq6C:CQXJlJNr>G`WMO0#KAV
%7&,Aj.$l+!D<^8<&67A%Nfrs-K``r$HYI^a)I4:LZ'&j\lemOZ1rX@2YJ=e]AP$)AZaO[lE<_)9m<"goWbo>8l)=@;>nu[8W7l]l
%9^,:)(gNR3;;X-g3l8C$q<$\,K[\`CnmN^M$>Z7(P*L6t4q`)\kJ`\_qWQ*t^f%50.m`Bg[G-m;kL%7//B@2=F=q;K-6FVmMe7r;
%XjGk_R+JXsa4\F+,8Mio\5s"La_#&M>et^XES=sT.lSY,k@WgFe9`uoU(YMK%Z`RhfX"g2:WJYJJlc>D_ItdSc?U3<WB$N/I>#c,
%iuE`Y(U)#oWS"WmGOTSZn`B&WO.a4So`I)-Nd=+3aDR6)^-KjKBZ6D&k!B>,UF(R)f9K:`7OAZ`Z=_Ed'ba*@orm_N9k8iZXd$H6
%G/s$p7(EX5F!J4HW'S?N/IQ%`f`@M!(SuJcf`a;Qa-m$f5C#&R_rab2]i(?U:TN%sZIu(?I5F^qF]7:f'"a/h"!m?r9U+pB7f4PD
%%dra)Mi_]"Y&WS(J\:m-Zgko)4\TSPGNcp&`el%.C#uc6VRT>D*bKeK?,tH*V<a^SRq.<;L:!fA6gKWDL^2nGiWjbd*X$XV_R>L*
%`^WoWe!@nQ(QeCi.=ga8Vf='toSa+^Mh2nd]lRRF+F:4+k/aA^\OWON3'i\>13`Goh-6F8X)jrVn&I5Z..ubB\9-DO%Q/O\YN(&@
%&:Np1FbJu`_W`1B?Iq0.RQ_F6\=]rg@nt(p+YD&$Pn2djh@Sj:$F#EZlNF**Fdno#Y\<hl!id^]S)!!-^$<HL'WK9F_n,no7C)@N
%(AnZ'cfjn2QKe'MlKXs14o6'#@5louFPP([Z)SD\`IruHcshE.5mV8RWR`omFPhVKPD#s@-:P<=3,."O'OPEBL!.?'S-k6ZA#tZb
%Ya&Uko@o%Y/rcQ,-`r;jL7M_;?WWU(PL?h69li9Q[3)tK(,1VG^X36qG5>>sKC6j41)gHQJK@154*6[-auVIN;j^g.GTr!)SVDjT
%FR2T_-L5W7\%/$E5;UW3We2tZ-'FLr@pDf$R81efQ^#@9[<rX$2)i];%4bm9A6=umFj(]8fif#O+iuptXF`c0mUK>(8)`5fK08B0
%M:6k-16[liY-qEkVCFY%\pU"CE#s9fSEK!,8K[XQ:W?[MFaPI=Q0ub&\J^I*V&h3oFZ[O?EiM-Y-QZ,llA;Eo@iqH('V0opOZU\X
%Hqk#n),KgVZiQY+5Cpmkee+'s#"NMS[05p(9%)>TXHFigrgF3,TA-:F,r"S%e>8r@_,7[6bU5S)@0upcSaKWpGTZ>j=fiY+6F3r>
%`G1o<!Na-/`:HOs\s]Cd19KZgPkL"0)!:fCS#gF3hAh8[WQH;F<Io_`h+6,^b:/=UA\+^u-8KAV@stlMjRM3f#%d_0O<TYYe*Hj>
%Sa0<Efc(hB'I5e!GndF@#eI[&A+WZS\;b[Oc7pA4EZ_2=!`RQ[gm+R]hF?##[+\"S1#&e!\E\+ml/&W5#ghGN@?"/FK\#EV&#hRJ
%-.e0DeMS1I]KY*q!TgU]9;O/5K.r>$^-Q(/G_"Fbk)4iD(,iW/8ZXXR6.glTmgKem9:P<[kdL9s09#+!3P:VefbDNp[?,jHO.mnr
%e_Qf1j35,1J79`dd=>8ME9O`%hP[5=R#e#i@Vf+5(Y$;cNcR#VNADae)CGoJQ+NSdJb_!FC3"KMDA\u.g0dZo-fQ]'2/d4R%e.$S
%%Q9GD".c#f6YEo>o;:7HiRGo#]PeOb?7%?JCDGR6jX3^9F*TPjHHV'-@tCl?9MKN%Xlc8]^#P+mS:>&%*n8Y[/Xj/-X)*M8+e.%F
%nLc^1aZ)((iQuA(]H9D@e2A(P%E<B,X4mj3CL3!?3r#S6bnG0bS9[kT)!,Gc,Z)L@I,8GtK6tPN&W<5(K92cHNFYI&-Z*5oS5F?2
%$JBrWaQDUQac,@02A/6ZOk>M?AH(Xs/P4)\X4F>+^:CHUI</g%qW>X)oZ`)<kaMrakaMrAco2]BTl6ue9rm\#qPMeH6f2/$J\J\!
%"+>6!+8Y-:(Ad11(Ad17))n;0"T%6b^6F@aF(#=Mk/'=Sc%R2%RgWHaS!3eg%=N;+#Q!T"%eS#"IH]G`E7/1I6Jn<;B<S"I0'kj\
%>hFVA\ZcTmF(#=Mk/%W#c%UT0RgV=)2]$Y1D_CBDhr][tqW>X)oZ`)<kaMrAco2baTPr-E6f4Ek6Jn<j6Jn<jLV>d`"+>6>#Q!T6
%KIP:2SDSn33PPZEF#.6^6Jn<jJ\F.Z"+>6>+8Y-t%eS$A*U'!a]?iM"GG&m"mm,d#hhSqj%h&#hMFH75_055D0(dEa*U'!a>NhRJ
%qp7:bGG&m"mm,dcRO:LrDpIf:h^HNHm_M;A^,`q=I<0iBqW@\coZd2[kaV0*co:-2TPr-C6f7$]"a&]#%eOVg%h8Btr!rADq&S[g
%oD?ln(Uh[L(NuHi0'ks_>hFVA\Zc0a;rJYk2EoohD_CBAhd"fh^:CHMI</g%qPFV;*qToa*I%1:<CZ'o[(>b8<ghM+e*JG='fT/2
%'!A>l,hoZJd+$En*D>3lAOs=DPp,tC.`2pID3[uWg+&lg[8gfk7'&08lbk9ZWLMO9`eH<;bLklQ\8.?IqJn-0A!L1+:p\<'UY2_(
%o=/S=8\]&+;=XHObgUKAQ!sW-a""E\kGB89X>2'V=Ck%5B9aY9`[^#cN`CgeHWD#IDlb]Hlr[h(fXK#-^:Uhrn,cp#AQ3ocl*alu
%bZ6>$C"T]5\?KJ.i@=:uAR*lmb&C.XXYQqpS)bNhbW$Nc.^L&)AsF7S\QNL`):>N@.\*UrX%B-g)<^ar17Q>'.^PE'RQ!*$h(7np
%AMt!N<G(Y-0so0\2@0&Y1-?[pWsfNpg+&lL_mNKVSeSkL>H>t[C5Q#'2$]W0@3a*ENS%EG``b3g#Zl:eQ/Ul-XYSq\S:l5iRB_[*
%;R]\pe[UUhh-+C0l/2eHA(N3:R8[RCdJLe@I*X:;ilNZ[35H[S)D#/7$rK`\G"%Q_fqCVM+e%E_L\!?L7dH#(R)nX46tRe6Z2'T3
%[012.X]hn:jJ;TAggls!_]g2%,ZjRG)8``$6M#UQ5cmU;=D*fbhB[$+=\^Fkku*tJ>n`T]YSR9Nn,0C4H`V#6RA:W1X]F&"&V\D&
%E;ec7V$bI.1C4<PbU=",(D'!s>9=-!M05#)VVRD,Y1=I'>0RLTL(]rZNkIEneN+omVBB]!Ca"c_fn>k,9+E9';!Ufum(umbg6Fj:
%]11fhNN\80B-g,'kI30\BFmiU[<Ae"Un/g/).sQjD@iZL.acJCDDKK_NVVKXU2;$llV9c(cD:?K]%ghd8P6ufnraqAToDgR[q&2A
%X[4I&i4\H3Ka*QE-RVu]e!fPZfOhFpLN;j>%3mA:#^*<(B"H>a[[C"LCbo4N3#84EL9lQ5;bEb"2Ns8@LZQc=,=le[e_ZHHUrV1(
%Z9kj?Nt3Vs#prfa3QZ8,_SJ,t^k!c=D8r!>]X:utYB(ih.o!ms30WuT8<#EB#=r5e,GFFi@;q`UAu.8O\>&*lDUo]B;FG5=1Utde
%.:j&#CT>D!_ZE`/@b!M,#_h&g@K=Nk'X?IR6PtM.%JG6@2Ag'rGr*`BTfNZuRn9&[SB#'F8I-1d%qO#sP:,b%2++u'iF&$ERQ6a5
%/ZW;,+e9^.h+e5VA<OJ[XiD[rBQff>HstTJ;bKcCXhlua*Gi(R_]^:nG0MY'eYKb@"%p2(1T]8@13,4WYh<SWiFnSBEnBY5/QWW<
%F1'1^"tho+bg;.WFAlmsRB5[-$.[N\DG-a1L=2QWZ?hnB=[b7.e5$oiG3%]5$k#[$UO7OR[No]?eq_!(:TD:_/e\tQB'fp\/O19@
%<fh#?A)g&4?su4Z?>7DT'=8-[aL$Lad8"ea6;qbLVUQO%.-u.1E3i`W^6:nLSdS-"C"S8[<Cm&TRVl+rr9Zn]g5jO1M.@/m;#l6]
%1TmV-O.sYL%J-r41TmULLTdM:j;'g?U'Ab?$-0;db13TbH"L=++RcQSN_J&Jpdm*H>WWO:-Jc*8>>?q^4HTF4]Hhd]?mHA#J/p(?
%#MEo:B_$^p'qA?K@U/"']+@`%X3%dOH>&N-aV/*_C,]r%7MF8Gn.:VT-hDkW[YTLA'7HWmh)M1,-!8Mh1mAWjBIC[>/t>efK=mnT
%j^)fkLmjWc+DY&WC"j,O!B"_Kc*po?JEF7_AXO4?U<VJm](BX^[.'122=IOIWma_UX01a[M;<$q256L_Q.(RVhC2?NdR/5O;@TL`
%_SQ8HSMWO#Fng`$L!/8SR9."02cNmAeomco'uQ;B;m2LaS^.0\,\2'cPSr;[\8,Q%#Z]KKQreF>>#VW1=\#>dpZf4JcOk+8iA<R,
%I$i%8m?m(L6niKo84)1UGr;G45l/&*T>YL7VY`'jYL[SX:>EC=1bQfYR?k4FX*l:&\]e5M_ou=H6DhA6L.i-KVA"krDqluZZDVRu
%=3=J-F>rZMC$Y_Dm8E/`1eV!Y#-R.>lq,:[^,\F+E,Ml?=c_tO1i9AFLNBMQL!=jbj<[M<3ZO3/OPC,1*eZ=!\B?sIg.&D4b7h8B
%c,!1UZ`4RYd'Tc@/Lhe0Cf7a69P[_\kaHs#fE9,[M##W%"uCplBtpi<bRb^T.p$bnS$Xlk=(BkKX"+p'Mja:V9k<b=H%e#4o>rNG
%i&L>LjkM39jIMJsZf@Q&=1$8cDB'[-PBX1Q#ObnNFn!]L.Z>FQZ+eIgju#tFg]P3Pi32j:k/pnF,gDf;CD%9?3kgUk!;(N)]lUWH
%\8"Q"#3389UN"eH#'n[qHDuPVAXeuE.;:[sjfN<?68:6rI\rZH1H0kQVQBKR;*Om?`';RLX3!&4F\SsuEf.KV#pSm?]]o42%YtBq
%hK247L`Yr$oR_0hj5:(YKTd3_OI&Es+QTNnAKcSPEHAIJX:On[KTl/UHUCUnbg!8fpY5'%W6JYYOukPR%;l`NU`MH9hsU40c9mG/
%l[%`.rW_eJjp)cWP94=spSD2j?BGF^883k&j9I-Ah1g$A]qQ@`)*uRD0M\[_Gf+]&&bug3k3/H/l`&Ir!PqH6GujP5:_Cge2HMnr
%I;[lT(trEQqa*%Pc+Q"@V_0%OI=i6&8fkOkMX;5K_*PQOXkV-XhR$_b#gMLHC(cWMlqU7]7,5kq[A0)E%dRF+A"2dlE6f-!V_q69
%+&9'Yj0Y>06_@(nmGO\f@3g..k$`%hWfQYB#5,OB\`>]5EpT0=6?TtgFO#m:H&nNBmE%BOgp4"AGGHGeS7kl#i^C5c3_jp6rM!Kb
%+-O?oI!oFHc'aFBg^M]pH!,*V8#(@D[M&SYX)ub&SEddC+>n*6bBrQN=%.?W\pV"0,DUOb05d<D"2]M,UC@O8l/'Do*@r<#D3fd>
%+RJj25r]euRXDJ-PUOK\pGUSI+Il#tWToQU">@#pYsYJ$%<2r&NL('D>s<qAgUJ\C;):+Kj7%2jg)*9)fdS'4+nm9uFaIeNPKfZI
%`P!LCR7!;s_9gL&PXW^IN$UnOCdss%-b?PV%K10Wl6L6:<kM+IZN<9](<`g"#Qp>bV*S'G6J_((VRnW'DKb4VB'*]<Z]CNRfpcf0
%6?R:)@h5o))9IUVL6*7oL)sCcO@98?,mf>W(p)pNm'e8[`eXHFN4!I/iQdb@[<nDGrJ*,.M]AoEaSSEOJd'I/I,C-,`eej6!%NUc
%OF+XVcQ$1O4?Oihc%3'/*_V9mkBYQG%96iGP?DbY4E^L,a*FOCKG!t<eal(:R^[=1ni-.)I9KX6m#S-IRM1p;J_NB*Rgg&fm\gnB
%?spA%0$S)\SGI_sGOcGXg;L5OF]$o[R_b4Ka45hd5=_1sKCd!HmIY3D/Q^D"65\9b#cY\N)M1:$<\&rC0%uY/R0?GAL?1CPjsuU#
%:X+&#iZ<=BiapqhC:;?Xo2s#`\\k@=FO4c;#">A,VC(nFnC1tK62+sd-Dq8a3Z,/DQe\)oV0L[8`t%.:6]da;2`AOWE5X"+]A:YA
%/2s&Ai-[WXGsGYL<S6d)_)0\M+ro[FJT<(pG!\rD-)]@TCef0*jC>L-U.WInm#DM)S"hkMgVMfo@BQC$!"T?Jg1UKi-EV!D&'354
%.>.SBo!Zmno8)m2KQTsqPP#2=SNi57Y+jpC7M]TXi67E-@G<`OeS<<P(KG>CBnG%W<>g%4p+7n2`F<R*+NEZf4p9N+7HrF'B1DjG
%X5:['UEUs62M@nQY@1\69PIj8gH/^XD:mV"f2mruVV(d`T+BoM&a@U%@'RmAK#Z$,/>kj[\-SB8:4)1[?cNTHFo"-8)BWJd,>BAC
%ENtG9'8OV1;XF$XT'DJ?Fian*e%0'J+SND9f5k6]O\K-PVCm-u(NUGn]BMj:*D(45V'iB2=-Du>'ROlri(jJ3LlH;8eIq0VIkLNf
%<:3;O%`u1jfc5L$caon1;oMP0?d!.fnE8`pNl/>DrjC>S!T25%\3)[&#+[rcd7!4rgKEZI@MAO`%K%Z6kVW:FkQrkk8l[0aLOMlt
%a77dVgq&&$jUTT"^TP9:.0@h\rbYp=3f`%c;V%8")tYd>it^AF?i+"">M[_7Cq&ZX8aCRTXg`Qi>TH=CBd^X'=pUtSmY/`[2QO,>
%e""W7%!-Gd+/_,e]pXA0cdIC8WQhI%f0&@q[a*"Rc1i&3lbD"sl^[$g?tdW]2l(P`PP@!$<_V`0J*lBdOI_m'O`Z6-WlA`oYuX$d
%3fi*I:MhG;^TP9:W_^6@*_FV2?e\Yi>Q$<5?M\R4pFsd>lu(A=G;65/GnbA0C/&(&<ceu9P22csZ6Y^[.Cocu5DHkTI@O*^nL;&r
%N$\",Gum(-(J3DPWf+)Tmuib'krLuhk*!`-NMl-CZ)gr`ADH@;hKmQqafcbDS$$7XfpnAODsQ2$F_m).HfJ%8llSm;kGdcL66T0>
%3^("iVQPj;]3:2KVrF_P\F-qpXg@s]j*nq*Hhi!EItu_S;fok3rn/7$*&f*$?B-DhE%Vif5%E>M4NqQo]B?`^cL^S*jij>him2,4
%M^32IJmh^??/s]^C=Jl]AM.\5f;L!s)nXROGiLdXV7h?NfQoYJ@Q(._0GC:]3]8@'jt!!uF%LZ5Fc`t52O)X%U3'Z\J)rT[<GF[]
%ACk,]Z?=H"B&&H7EA(ZprU2FeIu5kjgHU$02t"+Q(ZDV`r9>AQrP_uZhqYnlG"V3b\bZ6ocQohS_k-8n;?:Q`>fmo#[lusHS:>X$
%GB<Hu4aVG6m16;ujQ,asG]R"0StDPp+.f'oT3Zp=rls-VIm&8\k2DrBbemX,>i&1N^\?RWe]lo_hd7JeRCIZWm#U,IV6kHLY0Q4i
%/l*OZ+5_Qgp@rk_O8TIbhlm$Q*o;/5n>Cb?4tMba0Aa<gr:'TA7p"k)M"LM$hd9Z<EcZDL\((<34d!Lc]_caIk#_TPqeQ26i4De1
%T.L$BnY?+FT&0G2+5V8>n^XDpiLQGc[pL4@3k7BKl?ICFpu_,:k0<(;H,-m":<q23^:H>8rqbt\*rTnhj]_t6\A#S)q"WU-jmVT3
%D]\=D)<27e1OlY#.*D>Pq8qH6J)L,-T3a[kHosWO13S,/TD"31ZdKU.?i2:ps$&OBHMACikX54<AY+C!\P/9lVr']5qW.RD_nEt*
%e!m[6bOj2[;9&TRIX?NdF)l_SG^$gIa(5V'&#0R4ah,UB/"4E6/lU\c7X]"7I@3^NoU@5VO.;FfHQqo9SgNd7b=K(Ca28uO02F3\
%Nt;5lKDmgP^@p-/G;@@24c>ofr#\k>@8tf8_Lp$M%`.e[;-eWa`Em^0n'2kdct:R:Z[RE%Zi9H%hc^?Ad.>FemkmTps+qUbK"lHG
%T<aipM8S%;P8=Q(cPZStT?L*%8'e,"heqt58,l;=*88$l.sna42r64feYWlWI3gO:cdO=nG=PfPQ2gUDhqYi6hJE"ipJO+uoXTB=
%0@/rJ?Z`Ba>+_uo07<gX0o&29)lqt)eS;5]DV8><hL0m&>j_o:rTiRqT.g!Rl%O&'E.7fIip?6smWeO$j8&N$T9iLIXktcn/1FeA
%]mWLW<I=SE#?M_ne>'cA4'slm/mp\"c7TWP"QD@[4NK>Y[/[IfG:0<G4QgAGSD9^,Nj$%*I/N'lmV1!#MW*Nr:JPfqT(^GGWTs>N
%cQ6iX1PVO:0g<\)d:;oHcE<L?`J?,W+16UUptT*J\I[hD+2.TYr#M)Q-NeW$OJTU;Nm#3)nl^FJ..?rH%kM(`jmO1T=+,78GKt,G
%>eBTgD][ck>^_5j*H)/*=C9m48r(jTj\5V#]"@H;i:$rSSBKqEe8D$br8h#Ca*3Tra32N4+(&p#qXf$XVqYicr*@h#hE>)3bT/m]
%K7EI4PFFAB$Li7^rfUi2]tZ+N:QJj8eK&k?_t+)qX`4djroNVRefa_b=O(,Dk:EZgC3b/SIe#Wcimm6O=TJcDb*\DC+2HGqMITgB
%aNL^6(@[_:.:>jsQbWn0)+[E-k(M>Nr98(`!BHe`J,5a#rL`2Ts#LttIN1O4r5X;U_rG)U?[k\7kiL]_C#G:0pm8lRbMO.O2>='C
%oA$<+6u>Wl7o4]Jq0"V?k/./[^bb700>I0@HKTZ)ni:LSR1o0D/tidY+W>S$cjFf*lG91WH7;Xnr3KTO/*WHrq8(8BC%JF[89;j6
%N`tOP&41<?l5)/#>U@rq6g:ghH7][!e9`M5Ck&Nnl;mfPQXASjmn!-MHFplM9D\'ZH`Ei(1+dhum?p96;;N4*^W[lUo>XFm/,_QY
%L:dAS8T&['9b:2OM0J-&PC</O#$jG0k@YUdMt[W+/[HqAqXX@J9pmlO%fbkcmeR/>X^@*eT5m?1^WRio<^!U<^RR8:iP_EAQ8[O5
%_MRlIp/>-p5Ge<JmBM/N4hg[Xnab_\O"oUli',s\IbNl:m25O*/5tl*8$qpF:i"@^2BJJ2QRUl+C3d#*jffUYhYE%XrQ[K'kI.Dg
%D'qgPAR<$fH(hk%4(%I&&T?MGp,<(bn7OsUiX>M"93#&Cp7o;__Y-^3;8&\ui31cbM]2&Gou]<33)7)1$>nbViZak0Me(U;9XC8,
%11_l"qaYbp&*oe$o%!+B/qi=FmsTCBI6s48QX%J[me%`ki"q+7SFisKrHrHgJ">?V&"$]gnrEL/CjCN*`VWS[jX75m>T>r>3Oug$
%o'5@HH]MrL4nC/iL7>@$c1o#=+HLhm1NI&Mj)t1:0=e2TDbj0SNuss!MS#H2gnN$a2o#7LPN0pPZGlFE?EO=\B:#5qSVaTLi9'=m
%jR2f^]>q*mN`t5<Yhu%BhHet$=/`qE(A4<^_Ci"*V3g*,Q1N%qS5kRK37V+C&!p'E*aE!C*S]+ChL0G?q!-l*0?;n7I(GhcbF!,s
%p;^2gZYIIocdFlqF7B[qr[f^@5DO*GDhs/R=#ZJdRp141p"_.NbP0WNo^;,C,gQgm>cqu4+29m$do[osPY=_YZq&)QM[Ju.5EPi%
%02L?#i\Cb24DW[KEc1hrDZ-:]Y-saR?_9HpqXZ@Dim2qY]nY[3a\)H^\EQ0<.!t^NlM@0;r5TjhXrgh37!7:s1&jZ4Z_,g.\#8CJ
%PHoCYHZR>]Uo3FOo-Z&joD7P77WhlsoBl)1DO,ZuFn:s!i()ssQlE<$c]:k>^4FR"RE<rGFFA3+K)>(f_p/"`ZeO4^"M;:Ck5;MR
%s0>).9]I.U@?KgJ^3@E!'sH>+3Eqp[bkFlpk<S/kj@cPqPeYUXrlN'e+.ldkT>%ht*Vt[i0(3sG^ruA`>kXT+LQA$shjn13^L,jn
%m8G%@=`,:XHblHpSACU%#sVh<J*0M?H2HQQ%mDD(@'PdgQbWO?jZibirVP..+8k6:fl-d-o^TWma)CD&`TMR9+90C[s6oub/m1*U
%]+]"c8Tn'r4Oj7PgRQ$1X4J<`4+;3kr:\s9j59A&S:C&I4[UeFH?;[>n7lF>^q1FmjYN-gS1)>pb_7kSqTgffm\MPH2u`fB>lA6U
%]mSGfkLnM6o:l!BAP2+O5L2&8s'G\2VChhc14*+E=T1j'q)A8Y4;86R8.R"jn8"ri9D8/Z]rojTJ"J5Kr&%O??QHJAj/ZtH$X];#
%A!*B7Zhlu-gX=!GMsKt`>C*<#imXNG5CY."D7:Ic*n-`4qrceAS+V'8[-sf%Y=Z#3`O,P1qbG/F_jf*Fn!m_U`cH+>H^BeKd#R;0
%LjX5`I_<9SU2?MbH7:#olS!_[&"fB/HBAm>Re$2Um[W?9M2;i7aX.58'.5KIb*;"DnGJ'L.3IDkn=+5)+8ogIoAAg4rq/4ha=G$!
%_fY'GH1,@lhY=R`cgHh,>!L9;[&h7.lJG3\dGEoUIek")G^oPPS_pg.hD[&jkdQINbNZDddBGCOn9usDkU2]]U+;i8TF=MF\qf#.
%Qf_64kH:5fd2.)fQKEA2]\Hjk0?Rj_`8<aTHLI\lV3WapV4L?9htbOF%n#$>Y?IhN',c,1N&W1!O!/&J/#T\cQm3nTaG/QdFD9^X
%ijM\U,/#\?V9hIJ'@QNakmMoB>%WYs4J-*EMg5V[1%DCtUVW'.MPfo)Jq;cWWOUrRiiI.KMrnsM9hUt7jbqhj=6l^/@O]SYc<a_\
%W'E4[f@`\^][U[F.+d\uX0;.9`I$ZRcQs7V45LQW75,Pi8nWp9ZQ>2'Rl28&;rSUN%Ib\J/Q;(3L5)3'MZQd=^VIjc(ru#>G(.@,
%Q+k-3>'\&W?Y7%$l"$`6BSFW'>&J0"O%n8r8uqF7n<uVqGc?#2)X[0mYUI4"ioWb=>PeAJ)D`ukbV`aULDP@/:>[JSG5/tLkWW6p
%],u%J9F#M1.6YY,]nIm%#OAMZTA,P1ebtU2K\#'SE%=a^rtfN-:EFM/q"m!_(IsJ64sO'a/;Ub\Gm@&jkD;ZlIsQ6WEd@Frq8M<f
%PFHK.&"bN/.T8ufroT[h('f-61MSLoo'Y&mPDeJ_h7JS_bL@=X!:.V"T/"GUk^4*<clRX\]R+[Iq!dA8X!]9%/U_nerT%/W-8n;+
%$)^ka-i!?NmA>#HIWi>h?(B]!,;S.=#hLGE)&mfRDQg83oYIDa5JB=nYKRe^lbhIQm]I%lT%kq:W9Wrgrdo/BG,C7F=L>q8=*0@'
%.X?0C/.08@>e]F>O3X@Sq@6"@-r59B9a*cUiLAlfFmm[uf/.U\[q?LQc9'2Kf3c%]-q:pUTA1D>:VU8n\P0HdhYNqMAPaK7TUstG
%S^Ubf7/F6E90Dn3D_A4g^U\Z2\!2?l#2_VP2)![M]ueUfSY=g*<6+)iG'gl)G(\,$hq]=0H[L!\o'#H<m&ol_,[YpL=XWlpPKRl?
%DBJRiHK.4R3^,`SEj9F19@#^47BV?1m&^PN5.Na-q\&15G9E/RhqGbEe=;S[[pQUiahp*NB:RQO-QbR`]3[Pr3Bq7rYkQJI*hAgN
%HG"]IIbVVT,qdfFj5Wq]^A@]oO!hUgkH@+^\fSV6lg:12'3Wo4]QjJ<0>ICNh`cFWmbe%8pM3rkD+uWAYWr'EK6Iis)!@L9S+d5-
%o9fAqH1U"$YFf&^3dboBq"W\9s+g>5d2&$Kb>4EKi;1Nd/)o:YiksfCGOKs(VtdT250If8*Y-)0PG+eGrdUN0d-Nc?Gl78[^,+fn
%iQF.IbDJ8D]0%-PAT*p7AAe@7Po+,Z2fF38gF)1I3e..=e86(ujkH>+[o`Rb1:<a_j,V$aFSNc6q<q)HQ8^C445PASmO`q%1Sj';
%+'tM0SC?hqSfXO!N2VKpe[mX#SZBB7X,GNPC+W%FMb]RJlEGC"amE$]ZT4:K-I/O'OZ`R/^E%dtfgd,*hH@!#Zlu]+d'k%mr$Ri[
%GhN#Uq/M\grSiG;55$u"Eo"8!r#]g;o&/93_iMjeBs6l7-_YVYWP,S2.fBgio>/>"0A_h4=a\%5C[B0K6ncm#gWoKb;u_#a\S,QD
%1OT)X8)f[R)ErOs2XSh/%&Q8kg"HqY%;C@hEPUY)l`idC5CE%cbH(6TbPW9ei)T_>\3)p(ZKt\CqWO.AQM(o,`U,nbi9L19m1@UM
%mbK8BMu#mJ/=AoRqKIs,T6u":VWh]?>aUCVrGVHk)pU*^]DV[)Gkg']TXmDaJNn6[kP>:1B_@?L[&RG4Jf"=?m9quCJ+1WjT)gVE
%]Cgi*HJP/NId>iq#()c::OSp[M).B[B0$/KqQaaWj`B8nGN\\kIV&5nqDl<*PPGI^b5Ji)jWF6'Ud+`bPq8E8p%6X85K_<no;fdG
%CsTY81r/PTgT-g%PIhWUSG0^X:9O=/#Buc[B"Ct=@]8qE_:sG/ft79-pA"(Ah]59!gLstV%CeS^?.>Z=<OuE.dIS/g14JcM`V&']
%&!#i9\%<;g^YP[c%9iE4<:"hPV>`,LQZlriT&*iC?]i/kO2(;HHBb>*f3aQiNn*ocDZm]5U.ZT>;Qr?qcHiZXJ[a`a[A+L0%r2X2
%f;QSU'3qp/,M=deAK>O^QL(oXoM>jdWms=.UHYLmSmIQ"iOe2tpsh,Hg>"fpAWB!ci>^4YDiYU0E`aq[-^U`X=2j\gf6V/DqY#/T
%9u`BpmcmFm1crkNeQ_6L;MX/D>qUQ7]b>"KQnBc8Fm;aEIJA0#1@6T)bA4i(358]>\@5H.k3UKZ]69m8DIS*\@U@JeL?D=H5!+7A
%F'P1foP=<<]>"*Rn\6\p^\IN`k4W-dNGj4<gS&9UMqb-lGhEfuWQji.n[7hc7qZYRYqOO(9:%9##aFBdkcXIGQT9nPHt+gr;t<?q
%H@C#5%2kL=TTT*7bFB82]d)mM46"6!qM_k^(Kq\01:hu,_>iJ`_EWPIinFMtqt$39eBZMj\'+UC1YD\5knO*ZgClX5a[*1J1bV$k
%ru138WR/tn5(?p4q=_urrs\&aCTs#[Ot#DZB'eu?mMgFdPJ;U@nSWO;^N#ImF5-<BMp:.O8UH&mcY_(]Eg)!(AplgKqZ#,`o@;_W
%\_-('nBYXKGbOiQP<Xuf[2\R5g\$A@k&`cbpu^qDZca/"iq>l,)^U)[a+m_lDGH8]O?NT>D3TN'(W9*us1SkU$@I;PIX-*cI-/oL
%bSVH%=f_V0oC^VK]9A_fNd#53qr$9HC?G4[InrN:oA!X3o'.GTSr'*4"ZgK!_9Z\)4K"HO13ia$%0ujjT1/(;^[mLGTbj3jdgocK
%<h9omiV_d/"\3Q[r+FEo2u]`6s1HEZIIY5+jMcF6rZ'5Pk9!KLh>?oYe,Lu!FEN"UFs.^,T2ijr/,[_f*Z)l/'?#"kjN>uE>abVk
%YfR\bPJbinH><qQ>4"SE0WH4Ig3l09d2h*Ci0jA[);dm(>68u]9u*o[0PNS\pW:>/VhRTbr7[4`0"Eq?"&*)2$]:UZ-V+gKc-0OP
%(Wu]MChE$1L`8<$<TohfcCt)>1NoBS&Ei,PPB504OPS*\mT2N@?*Z]dlLq:[c:1SV(CZDg>CV;6o:=!WC"B]e=?qG0F\n_OB)jf)
%dt]_kQ6(<@A"GX>Rec*JIV?B:rRJkGpE,O\oBOLPn)M,1je\k=ImIcBc1CsbGMfgh30".:4q[`(7<pP4,>X2m]_<`@]pEncqWeXl
%ALNg9e[$(dEmk[:'Ud$9UdG1d'A)Z"O8aMH"%,TI=eBn*4>]*F<l>Q_Fajq0),F,cq^Vcm/%GWI4O_37?+]!UIG@!^$=n[qe@t)R
%=<r9.IB2E./KjC*i(^]RpV'<"W=Ob;I3;k]kNI18XkTNHbiYuk)BZs[*e1JVeTgXc,nP;+imrK5g&AMsr<ZN3TmY3O*<.#GBtO7`
%i.GpLlOO,<a8IU1@s$#ZkT.t-@H;t_B@Ymbrq!%<ZQDVYGfq)FFdfN%Oo-8-^S"bH#:K;+pJ3&q[kE$a!$U<,J`%sdhar)MT"n1%
%?_;\_U\;h>Y%PCU!'&>E]7KXU:1*&MZZM5FiTBo&kQ0=2kbU0=@Ka\<OY8f'4*^B<Go9T^bXhVil5`_%V=jM`9TH%$"`1YphW4MA
%G9M8;jCuIUI+s^;"_!#PnGIKlF1=O%-bfjHg$V>m2`sF/Xlf)2);uEMs4hVeIf:?ZbttdN"+E#jKC2Ck[iYH`#W?rj!Cf9L)#WtK
%+2B"rT*mj+;jVk%V!piBRc#9BAVR*F2#Y/)O3qn@7K7:P\OB",4\_E<KR>oNd)br?r02L(r,'jq0`:jAs!:88lFX+79mOA<l.su"
%6.[6YJ*T]LqWT=+*M-K`3'\Q`^Q6%^"5K7;A1h43-^td2d,8aPJhWtMmP*=Ch9,O.:=n/@jq1Cps$6Y+!KZ^2#X]&W0p%*S%c&)^
%"'XR/_*/QXGh!5`#B6Rckf`!ci,.8uaLot=ZtHb9p'(eka_6'l:H(M;'ga+=>MX8kb!BoqK?L<XT02_]jkc0g*s_Dd]"`TM',g*-
%%rr9_F:Dp-2\qJ:;"*KI_8)/I]2b$tWdC;EC;Y+B2854O&b+K\ieO3&5,H`tm8g\2<UNk:JN""/Vh&3*%\j+sda+EAh+GHQV#al!
%\SnAX_mS[=06V]2NLrG5q?D&Orb)&H!!+A;NVRF>_cH;l4<M!$!),TRE'nWg<iY9P/c3S<aR<@H;U\kHCG=8erap6K;"tLP6iQ[O
%%s=$&>NpXZc!U=*jsXU!?2iS+XDjju]XIX_I:r<7im1g>?+l_Z6EoKj^&s#4k1sDIi=96VHT7%n;n:e<_YErlgU]$X!`oEmE;DBO
%oikfeoM>G"[Q2&9F`KQ7J;L+/d/#U;D1Ud(U<M6NPj>>Cl/>Rf02"j>_=2BjkneX;T+l(nDS,N;S'g=:j0rA1CTZM$'^ub9e-^U]
%rMA$h/WHTV9h]?r-e,<T?]P.8BcjpIiV0Y;DE$u*?AkAmmn95Iq1$Gcr:hr`hu2\m8n#kFa=!f:ed-sXfpdm-I^O)X"JsNl@n]i#
%/;>0[3+elE`J?/`3=0aae,%>P)X!N'M5>F2J,dMBHV_X8LXgo`[\NqsjdEK=qgJJTILf.B%YY#d/kcNFZRg.akmC[_$a;`2+-Ro/
%R)Y8""p()^[Cs&%$`b$@<-lGM')qNuT+]-?_sh;6""o'5IIQD2r=o&/[0%4'"%41Aq>=\'Y3\ILZOFpnX<,amIQ/I7TboR=ZP9oi
%ARTX+3^8qDn0_\1OZc/-&R;gPoMgg1%t")X!e7eH?>'jBXL\5dO2+<ICiDX([GD1>2jRV(!S$>/*W402ZFcM[.,tLh+J2(5Pn#l(
%am761jo*T..[#Deo!YrkJ]ZN.-e>e1:7\?oOrBK2"jeJ'3(5&M,NT&n+_=U$L\If:nb6tFc?+lHCiV^NbO3@tWF0Vc4'RK1=.T8@
%T<&"kN>)h%=IEDnkZE:K2g%nj#9Y%^!Qs-L75nXrE'FZ?C/&idrT3qaD"'_*jgsAAU3L=<6Ui:88n"<+AN`FTC4$f9i[AW*'X*Wg
%*8*I'IE*dlQ9iKqXZKuiMsX)/hoHPc2s-:KU@)]9d,#mWSHq]Nj^)21Ve7l[-?:Z,)HNX)@,0pE12.r<%hC[uP#en?ANC?Os7S(/
%.EiYj*Oq#l2G'NCE^_n6q/catU2]F!a<Qb,@8?WgW)CR#>qE[gM3I)Q9qq3s:WB"d`cK9&GFc]P_`.\n>W=%=:h\".;8N[e8ZTpi
%P#dNe^iA5kKG"g5"Q<SmD<NC&65%X?DoL-BRlMdKSA[hTi-%Saeq(oS4U-2kDGd^(ab66#HdV%qAB_eCURc@67PK4"L/E>L@<.>3
%`d<MN=?(Rm,m#_,ZTHkFbats*0o&PD+Cj$1$U??$CMUgp7b+s/n_\0-]lHT&p*gLo\P]n'$YHWMI@/D.B/((a*+3@gGd/Q^A"XW?
%I<Z1,Ki_!mY*?A^,N\Z-c@S@!>h)RaF17A;jr(\rR!1S%@e_uk^ka3o`[KlhKMgn>M_UM_&n>4e%`T+:"3*hd'IKc#*:m17%d"iC
%Tpi3YV8cr;:bmf[X2-5#fapnPni:53g,*t-`H!Y@C478@ioXMS9B:7c/EK'R=LrG=@bJ!7No_j/!\4\t3EAa_BWQg;.p2JW0OXRU
%9>Ng(_&4NIW`M0];cpH3?C>eAK5.ZENTrS=2Aq4sfF6h]f[>AlZ*gd?a0n!(%,2;.HgKf;is%'^59#PC.:]SQ7Z5)@X&f_TBG`\<
%O63#7ch./?MTDk")$@57,n57QC,>X5S!G4.m0=&^6Xt%jcAEp#g.B\H6p%Y/Ug*AZ+ZIQjL>o5;0iUouZCugO6nNCi0Q]rif.^;/
%dM%+^1s@HM_:lD7)g6Kj!Qgk5("R%H*DAp1*M+/9o0LXb0!TC@5rEm3A"fD^Ibd4,"$Q\&(b%($NH-qF1WE(tC/!6F6"@W/TT/&%
%cu!S9m8d_$fGTYHI/dRo:J5K`JkR`r)<7S2SJdJ(EG@8i_@r-`'qLe`S_2/>qbmk9K2pJLU".tg6E(u;MTUV&'W4@..H@KaVP<An
%1t=_&R&T2Lk"U8rZ"]Y;;DZe-G<D@<VjPM`q9M>l`b_Vedq7drg+j>prl30R7&N&,[=la:4t2n0ZbjqV8HnMD''G+=)hq;IgfZ6'
%k0[Y`RMXddhRo<df+r2PV_-K]W*o-teiO8a)>k7WSnM:OF3t35=CQ)(A]`Gg)b\lD$<fV$Z%8a4cY:REbVUmj\`U/eh=d;fEicgR
%m]j6=V5nHoeR.gTXAWe+>8+QqaI7%$_UdGr[^1/&Z]'%D2Or?@3mk@,QB<k[L=T04Z[!lah8Tf'cPBdT-_$k,la@4XOgahXA?iOG
%rLn(mgnej<$"n*&.:o_fV=Dq#NB6ZS/tEL*_k!RC*o"k.Heu(Kg$?anc/.2a.Jl5,L1KZJ(!ek`o%rX#=IM\agQqSoE1C5qifn0D
%-s3p+7q4XK3'_rJE\-D@VW5&N2P#$g[-].7D:e$5)[9'JLJOFCQb7@i1A-:+D`F2VM[]&oSd\$tOE&sb7`e*!M6&5chDSZ^;4Y=>
%_kYW0B]ghrYX^.FojCpD)`HfSgMIE\h42eC]#F$Mk"`$*>Bc?H;=7bkQ>nQ@`L6TcrPUioWL<:\QGL]WRI)+7:TA01H)g+ZajX$c
%igs.jZgo#t<5@fP('e/pA7W@#F1gPXq+d1KZ]a["JN5LBID.MQD)O,!;\e=*%l8VK:S.,H]K2<npn0l,p]_9VbR%JlO<KF]h+IlD
%p)0"Viu+Ssj[`iG7(:)]T!+=>rELViT[cVg[`XJ$76:samdS09dC@,Wqm=aCg#^Y:@VL1MMDDjPQ04qgeJ:gjC6>+Y[%Wf'!?'U<
%ME:!0J@!VEL=8E>*MsK5DO?IVOYRppkL+?ZKGnK2@aV-BWH`K-B'>iGbhhI>=GK6NZTQWnZ`<(<Z/oGob77R]'tY8#(=(=%=\c2^
%<&hb63iP/A%?X;$FP&Xais6,QR9Y^^`8`pC'pWJ31ZZmQo2K6D(ip9e+NLIo+Bb^+TqKU26>pZAT$IXH50(<5nG-#c#/XG$k%fog
%45[[jPQ;Z:.CIn+l"Ec`bHgg)0?`aiIoK'ErMMhc$fEof'8T$mo0@a_9Eb3>b9#;35'oS>\.K1)*$]eagReq-h5Vn=ga*gfjGKcD
%TqFM]LU&@!,1QS85SFFjjuZ5gm>`g:U$SHncL''UE*mb0iPVfO7(g't2^3:,2,^`JZ,gUlgkf.2]bIGpF"Sr;``0$6CD1\rmE[%n
%@(DAP_Kd`0G5+Ag826mH3t.[19[o8K:f3?KLSCWo.2eZb@TD)\5EB>-QT`L5SAuk`CGSiEoeIi4CC/6#M0IU;8dc;t\RXGlXgQ,F
%om:>'RN4%3l6(:3`\=#J(h;?K-^LuL+dlHe4Ntb;:AZ2p$`RfEc6&ABqFh47f8eB<i@:`l$.3Wt,V%&I1pIU$LSXlBAs8pnd?uPN
%BC4c]3Y&!&\`PU]r9RB]?s4b)i&O#uD>^)i%X(EGm4M\MMN7>1g0A1T"BNd#k#+$7Y0>#36LnVh-T@T-794,7CQ(!;>mFPRcBD-(
%3Osd+\Ye?Pb3bIj2,DNo`dAuc3p4,QcDc?%-g(Q7D@M;i?o%m?Ko)#$(N&"k?49a?Y#sf!iSln&8X@#!9;N?o('g[2cFtf=L6f0i
%Qc"AphIk#sVl.Y0iYY^U'rdVl<Ba'<6KM&;0<7bo85qCO:o6:Y.#XO".a`c?9q4s.[85XqW#[3.V9[*8MgEZ_o@Kn`bcp'mRCJ",
%Xpc69-HUaHrV>O'2bSojb:_]GF04*D9k&-b1fMo2di<K7Tp@i$`*-%0r3Oollp>QjmBHXHD1C+qBNGP;FF.4uE?2X:)ETtGF4-It
%N_7o%W@6SUNSqCcqTRX!dtr-!\fmBQ:@g=/RhC?S59-ZRqO`mtWII\($Nkl5T-%$&ebn@`(_haQp/D3+L8?-B*,\]3reP\k(SehY
%5E+1DVL;,cEFt^OIS/X#e6e_BnbmEiX4>O[jWm/e.?[:sF!XJ'\ial8)-,>aB6isW3a40HLH41rqr`ZjY;tKNdkEL^Yk_^:;//,C
%Mqp3%>RHeIBN^ThDL#LRqQ#-Ei)bmo0jYgald.[*.';)A;GD&12Slt%X@-h[o]!]hMt<IIaZ*;Tc3BuXNl6"Y?,"hUmC#b@$-;K/
%?AR:d&q.XuUHIR(`7(k1\dg?9[$rG[[;M9g8\`Sf[:QNPq-'uunZ2]hJ&\E"pMMi(T,'OAVs!:pDKGH*G+qi?<oFEh\>sHCn7<=Y
%T%jf>EnVc:DAZ-amq("?^iU;\ra>39Q\Cq)B"9so4N$Ph[gPf45tGgX+0aUqI]5eDGOB1We#pI]>.!V+'Nf'j>I8jk9L+ASQDQH7
%$X3N-\gp?TEc*=HLok=(klse\R*0*$cMO3.M64]2f'&#k@\F2Np8)!E9]U.6EiMH<GV_Y77N=Y8$[M$S9bDlqj+mhC]49oh,/&M#
%`1P$Z=/b1OR`5a"8:VbJ^uR((PW^)RoF%enVu[0EZ>0)QT<XTmBM7uqgBD2=kp*Eje'&\=NFu7g9f6XiZIT!nH%ICK0<pp)@DZ"P
%c'DIlHE,k$e\h4:Ohak37smhQg*sYXB@Rb:9`Q%V0=BEVS`NGT`kbebh)CI.Vs'*:,T\5ZZcGKX]oN:]3iF2'9HU%#BkIgV/:0?t
%`$@:V0Sq*bEMG6cmuPbEN3KNHLWY]-fV115lOSe`b2SYkXra)g<n-@.lj-UT`Yk-hop>/_mSkOtR7<X\]m]<&2t_Ri:3%V^b[IRL
%i,"t'FNLhD*O&'3aVe>u@/\W!7,/3QCM,?]pOXi6E;hP>oas-I6TKFn!m,H50it.>O`)<6p48B`9[MpFNMRAV?Yb('Us5$8,%.K$
%/qJS[:-$^4N?Kpcm5P3u;:A#FN,=5,Zn[1cKij*BWhmhgfYRiM.O6V5XSZJ"ePR54g;]*<m0>0lk57>,1+[h"6!+[(+G115#o`b]
%L=F$#.Ss&A)LU&2oJCApK1l7QY2df(89'c4GT+4;WW`M(d3@=2g:VD6nDtqX8d7OPaVtWT3t$]n=G0L\)VnmW8a[dB_c6o0rF?^3
%Yq.Puo#.$O/,d34,dDto+V:!P+gF-KT#A9UQk@Le"\h8Cg'o9?2#:2:^$thE--^ZVXL5CIA)gX^6i<sk``A[qPS"NW^tZ=[6UlQ"
%dh9S-a=(?2=dA2s;N2Y+ZO6*CeA=aL4>T4+5_BQ5;q_SLB5'PQ>qc&TW&3U&<%pmc#1#2-?4dgAA:3<2%%h<k6L8)!nX!#UZ(*qI
%^In)hRl\r([9l.+n/bg8V"^IkpaiFd;&H>lbK_0"Z,KLMJ-R(bpi(32@#Xd9370!l'1Y)1G*<'7\U_;i6C1DljMZZ9?_W(J<PGhI
%Vo2h1b,E]ZP/iF/@&-7^lNW6B0D#g><c#!`8'[^S@%`RINPQthi0k9a+6V_;dQ@`@$3@NPKMR!'m8nb/iJ-:5Fc,Pp"$po&l0OU-
%r/'HW&tL--/..s9AtH@K'CgFar*u@1%=Ga#LIRSL#*Y\$/(J>A`&%f`25_J";g5Sa9>..fSHj#3L1"JMZXPr$SOTX@6sish662-o
%3,td;PK6L=D6p$Q@=<9kpp6V%Q[?o@!cjN*?OP>KVDSflA^DnB=$)0YY0`5^:hZ]9ilp?n;iT30-LgbcIKeq)b+'ao:%-9D_[th3
%a:ltTGo.0OWnOjj1Xm!JJ2^ur>0Pphl.#nZ_"(ppd5?,u@5YJn>ifB*#dR8//m^Is6U@#):G7Wgo>1cV%YGm83L,K6eUYNhd8SPa
%WFM^>QST5[ZqB=!W3>@>;>kLC*hXb.JX=R#)b'P"!j"F"U+D['KNDT04A`p8j3Eu_]4iF`Vs"Z'RDt,7%(mTL5<pGu]sf6kS\Cn.
%'*CQ09K\_*pdkb\O!!.uX)'4Zk2Dp6^s\&PZ"R;Dbpj@bZ:,pN+2h%hiJ]T8c]mC*5=0Y-d`<O827?-A:dKHdZ@R9M^`,TC+gqs!
%##UWsL"k&V!eJgF!"aln4a`Ls*n*LWA5e4n9!BND7#(%dXX[_iY0i"-70jla'QSCHh#nT]+E4uEcO_u[)eQj,j_g4h?"qD`XWr@J
%L^;l%6dj,J!oam$\2=Wo14RBZ#apK5a=A+=Wt/'XIo+nt*TUr::]:1!F&:>"oMi.Ca;@6hmlEc8mb(O"elRH6i")q_2_.:PY+08U
%,<%A]F2TBAJ`*[X(_#:57.5O-A0AX4"F@!=cs/B/HZ*4m]#Oa*8jdgS](Q?]NR")f7PqO.9_MihaPB/M@_S4[qC(DYlLW82[;J0B
%?Y;#.Sh@m.CnV0oC=&D#Tg@M9n9YapfDd&]@Gk`+O^$HE?VE^piK[F!S1X@Ea`(83#H7T:Mi363Z[!f`,[jD+km2T)G!C1+lHPmD
%h1BM[&OPi7\8dc%1AoG=27(eARO7+dh9Ikc^0/h/*iqKXVEQ':.!0UXGL?36#cS?V$fQO).eN<N"md[uXa2=%FBMFR>_Zbp6/7:8
%aYX0!i7.1/6kdboe-9;(Q8*H^$S6n7Z+:)'>VHE4m]#Q/q!gIiT/WIPo$hLeUI5V>\%/H%^Z@lLDaD@tFI<&>!pDPuZRB7YDC8U?
%#*K9?RWH>)#?nPZFiRs2@uj)LLJSAS,K9F@Mr=N*BiUr0)./\kk?p!I"s:qE`^nWpM^cs<FqckNKDqdHkhY-r0=%utgF"A$R-A<o
%an^^WU<'`Ina@)"-^Z$ApJ64fbp<[10SOf+fV'"ob;K?\@50F_2:AZSius]l'Chp>ko]\4gn/3R>%[B@,l.Hi_ae8:V^2;2iM4\4
%1NYjOO"Wd[]!a"Mc=V_cmL;j<iL6HYZsNG_4hrN:-!sG0;Q=OTV&i%$@Jg(?mchH`YCASDJh]tQWd%038ZWXDMLC!p!%5Z=ldZ#X
%P)F!'p`(1=dHO=b?4?jtX?CPI/B947$<ci\(QIK/(1/dL0<Xe^$Dc]<+(&%:ZPkO:QKHRt1CB0Z!F05_?h'`YW]e,fX"s9<3^^0D
%qZ8N1bTPH#m&e=Gi'G1J=gGHPjlt?JF3F.tO/Gp/*Je*@')@kP>PmDhIQZuc8ZPQCMGJn=iaMuP"pf`@.5'"\#q&uC,Be3o[Kl#n
%fb"C;b'eVUZh[E.l@pfN`^!a$=uXD`m5tPb\D#hI&XBn_5LhFf0:60,qsqS2TU],AN>>ob&Nl/;$76lZ""oeH)75sV0pmi%i'Ne(
%/3H"cOegC1!_`\!,]i+X.8'WlUb>U'RJM9Y=0lE`J>hS?;;fr!gL4'Hc+-?5Kf+A#%/5p(_fSd+j<Gf;D61')4VY2u.PeZ[KBlWq
%PA]89RZLTn?m#W^=H6E39#!<-3gFGCI^T1-.JE#nD,jdi2MZ(=5l4,8WU-CA,O->N0=1T)WdR1YMACJ&O%Z[87W=,0O[h@]Tn:I2
%J*>"c;=Q"mNk=E6_9om,0jGXn`fkPh5YmDlRH_d!@W4l"V0$p9X/u[#L_;=d9Ug6M]<<6l#b<6s[O8_6:Ne[T2j)N2=qm$Te%nfI
%A!VJnPrb&-l#b?I0UO?YAZn^O4[csJJfnWhFlkjDLK2C==;[XC"sb[8nFmh&m`/Rc;.UR0oFX3O<UGeD8\[er)gQRY,CXIEF_J$^
%G.*VV]0!j0KcZ^A,\X4_e(`SG!a+"6THDB2E)'tHYQs-%80>2Upi+A_ZH0&bRtW0hDbDV;C=f_@(7u;o:'#-iPi^u2i:9\'K5&8*
%o@Cjc4E\F[9m0eDUCAnn^_Sr?Kepe8<](/G<sE6:"1mYsp:VSO8HVA_9M(7%+qlu%.MJ!"2r<@g*`boeaqdoIm2@PsTLb4@8i:m[
%4iJ!qdKsd)dLCIuAZS2R6n_3aQ[<H@^>r@G\J<D11i>/9*c(q(ihl/9ICr!0"F@IC^PP^`dt(&\YWS"reGc1`)Q3j1$\?P5+AD..
%pB5M>)A8j2(\>L;U^C#]2?O)^0VHaL6)#7Srb=`c@.Vp7i9M*?E"8EdTFAK*]-d]k^5@`lJc,eC31C_*4s^s6906GAGVP,Tb9$2_
%.sZZi^&=r9_$3RZhh_J<XQbf9<;Dh#Ze#K-o5gn7o\@n1(j&XsV:.*Zi@NrQ/Rc`iGimqfTf5PZR'"E'fk?Y\^C%P+2Dm.=D$U=t
%6IT@N[p2:l9FBhg_,HJH)r&K>gp.:IXmg!WXZKhh*)>;U0R[)E]*\u[7M4pdT`Dj,L6n#fBIAFOk3&#UiS[YT0?G+A^mE'i#^=.$
%0l`MQ<Qr0n5(Oi/L/@fl?]Phbp$oJ<Wd0?''U>uF$,n>1T4/0_<Kf163.S[ZRo&u"Bq5VdYPNS4bsQ\.q;3Ek*gi7T4O,V]0PKTs
%bH%Z&mRT38TAPuWiQJ*s*^OHMGHCn,Rpp>ljnM-_f!EF.G#Osd6,2Lk)&jYR.h51LJmaHkj,%A!E.PW3o9qXd=a\bR4$5'ji$%#n
%"C/gFA_bD,jCB,=[V56bDTIEH0'8XsQ<627c^+cp/`=cj(a>d+@^NeIPSZunLf_e,;a-Z?1u[!n[:egR?g[R9"b"mS!\;2^5J%MQ
%5$9+]i3Ebr+O%/=K@_Y*pAH0TAUACU1I`nXb/og^DRp.Z4[I:lii6,9#.l(Cn.3nC[`:D&-3s;i8.+!Djrqe_E=6P;%Oa1a[\mjs
%lt>c^GtX-YHeq[SL]^&i1f,EP:PHd(>(8sFJo%eq;F,G(H/WI<OXjRtnjHGBDDCCm4a:>qAR(*d]UaTAXE&?.hCsC)PHfrj\f4!r
%^*9eO`0)jD'K:$P?4Ei#pnmPGf06h5)eXZ)IKFHS@I+`9(F?t&\EY;cBt0rgVqP]S[;?;)*aTE0^m8`498[b8G?P"r8)`gk\B;9`
%*EnCd]&c>SDLs[(ProOu$t@'3Xb*3*1W%(eJ;]L.VcUTU=2/!k:ZMe`7]+#udo?A53*r!Q>,qNubn+6564`"d23>jX069`DDA7@T
%@=:\>Ya[l4`Z'o]as]?b4;\Nc("\?P`!uX[Aq5LNHrC<]A5spSjkjVQp6[sKOQUP(IS2NkG5-rm@Zlp8^MntWq99WgfN0#B;DXu3
%/cLcP//gIg7_^QAImu;YLWK@#Dn^B2%PSs90B8js@_$;-k^B(+m\<p]0U7qAV[6,SX`0+Nn.9D/%_uQ,.e9`1'TTXQZo#]9S2fW7
%fe<4L:A#B!/]1/]1OUYL19Z+ogg7j3)5`bF?-KC9GDOIhb<H3oM0ClGoNmRdH&FI,k,`F=c#R%UV3>$"%S/Jges*6uY&\3<2dS++
%Zh!W"%Mt9mAWg9'.U&!t5Wn=W#h(@M(o\f&"@0F)?-Db<2a&(4\B.JsDS29C9D35MIXtAT,U[Fe'tkQQa4SZ@6h9HP,Jr6L8^7k!
%%tLIU;9<hDG/*sQD(c3[?aV3VouPNe2h:FQC4hTnmI^C&Fd_Qk+ofZL]B9V3>sWbYY_I\ogmYRC]gK>#ZHH2a7'J<na#.sEU=Sii
%++tPhE!@1sR/ibaUKhN?%E*FeNr-PTkSoM9AKTTLjJA-2.*(VFoo2'_SD[G_qi[f\Q0f"*C9N&WTe@,,_T=*[eUf?a-\N,RED!5L
%_,($Q2[E`6%>_#n'/tR.4_N1$6_e:L>cZ7DR*t@SH,*W!92+LWACrU%6g*P[R"nYNnm-X[19sH'T!)pRgX+MfTVQolO>QCpUPK/\
%45EXU,VR7X.*IX]G6i5DPQ]ePU9r.`m4@G1]?ths8#)t?b$<@,"lu`4:s>cqI@([*TJ4HBq0p/Q$le6(^L,_JA?W9=5uH:[Ptla.
%!Ri'5irq,AZXGApk!C"4b%\9V/TTBjno'd:CM0H%h*1![M1Y?b_RC<VXha[*T8L7s:F3SA\K=ORhJl7Ue3#eEIB"@nN]"7rd3(\:
%'"fB."T8G[1AEhhl`b?Y<>rBuH4>Y;DfAO,kU\IjUZ*b,'=Zu>VUsEO']_Ja>@%7N-JL(Vafm84ZN2\UguhE+bm4iU5C!gEj!-C]
%\Lurl$B\N.BgFhF*Z)nG@%P&NM462b#8pp=KHANm_'Y"5E#jQIjP>h\Oc(#Y?bJ2p]c!kY6uD[-AmfSE\<4_-0T34=$8&`iYH6(&
%IbIos$[lpb@W)u+FIc%D.0'Tp\puH!O30-ZdW0iVZ82e%V.gR>[0'ef:;b1=8)#?n=FAn#WSafP\<diq,^eGl.&+o_WkWgZKo:6V
%jl$^h+Ak!PW^X1Zh3kKd)(JMg%m,HV2:7H/%sXU%.L37BI"31"F@96r%('PY%3"[!A2tY7GXNbQYNd%=g5QF5?05_+Ms<]&pHhHT
%5.SADhWg*6a%JbOntEhQNSda#!K]-k]U&>==$5E`_<oIC5Nl8G0OLSr[bp\,\1O9JS'h25XIjd"I]?q%Q`jGq90Aa4HZk6=#s2Mq
%RYa9uZcp,&h6lb.<T+i=I<e@s<^At)%o$B'&3=(&+kQj!+EQ?TCe%aOlA(*5dHBr;`mL+kHgmKS)FG@%qm\>7hsD=Z@4sa*(InFY
%*I@c#nH0F5U1M*\%b1WVBcSC"#EZMZpmF@uLF-3u,"9F)F@hM@.0CVfMM-%&3lkR&GS(.."kG[kNF0jC1056p>XR5s?.rAOB_&;H
%%GueiY6ni#>n]hC,P4F@N8=tJ+8;nqRakX[]MV24d.mIa$(\eS:(N4!6iE]29)U!3=R%)1qI7u6]Jh-A<HUZ_laQp+#r"HmfS$Y1
%OP3N\Y0iKH*Zb,MTmD(6lP(fDS^m$@F$Gf\[J/q`oVQ;XK-m4$M(I0J9ut&g+>AZ_&+eUQQ(]89;":g-d^N)P31fD&iB'lDIlu9$
%KBdj^pE-_=ITm3A$C^EZa,,/>^NWORMt?OE`JSlDf;%;I:&rUq#BGjuGM4M9oC51j_f&C`[[ZZ'G1`t<o(MO3G&#Ek3X.5f\bs(g
%$GRU:[,^)\@utk=^']GOG^d8nh^Za\/mJ-*T"j7`ASo?>!0>8(WB^Gk\GCQ!=T7hE(H_9hM;G:3?B/*:hsfebf&)>L+s(hq.4"^3
%U\CARNKjGj$/J=J,el'm=*d/&P)=nZIt"VZ#7D?'L[Caa9)&?'hsrZ$O3Cb)Rah7YR.YfdTh>>TL/DTu22=Cn:3=)cTe]FNGg>N#
%1;?hbq02,O#KZY"o@Vl#eOt@siA[['ZN!6n3X,IHW/:F-jD/$$caE>bqN>O92pl!B'Seu*mS^FiCX&3T_gti`%?=)m",Z9O8qIiW
%mqn6;M&DWVq[tO\SPAA2+7Dq7W"j[WkD'[khh29+*PC\S8/o?HLZJIg6D1J]p88OMhg"9``SZnlI@c2Do[P)Aq8:ZXo!91?<mV$I
%V_L5iGgi:gI`QY.O"/1)ZGNo]^?s\98Fr):f+Tr2k,1BD,%KFVPi%R2FTDZsZ2?k@/G7!J^*QZ<&"oV62l@j!llV8A0+I=g19X/`
%?%Ld4<O2;h@!F0p>W35,QPjh!-_\6>bcBf!f;3EN>/Nn.)>6k2bl;)RA@)U[]e-QQIte:p40?a-f9n3Qc+OJt^Y/=BhnH,=IY03]
%ql>KMq+o>u6iZWPEuXZ=1%3<SK8[;jC(ru"%M*gSh=77j?h`S?Sj".rru)@qH1msbg.Nd3e`qJ=pljNjM%>3hMV#c2@]/T`s7%da
%mK/a[3kOD#*S9q+5n8>T"*E@!2P]"mhu+5dMTlQIU$mjN"OiW$eD?i<ldoL^L\;5`s576Zp`N-,K4GhgK7S6qK3s,]C5;fd2!a.c
%r?K!AbCb>Ck9FPoQNt&G66FbR&KVehp/q'E"qQ7R/=T6urpY>@3Vb>',&XcQ;7m:hWSGGps8;^$rt+.]qVOO2RtCSCs$(^3m175Z
%3;K`+3n>Eq(IJ-F;eL?;"+NEQ_m)>uJU:Ko+=IS'O=Uoo#OVVB$'kf4\3PVU2S@s.L7lls6*T."/K3]+O%^*i2W13,ON6ae>P0;<
%TQLm/r!33nJu]'bp\+muG+"D')ugcR!Y:4(rh9\T6h2eui?h.O,;@on)VtTZ&0Me$h5##,6aD5&F9fjC%oON5Pe]R%K(Y3F7steq
%rC-8Yi1>I<"QLYF?r]>X@2YMcG=W/Q!c]IhJT,Qsi='HN!1=*(%d,qW:U>QS"lMo]hI8SKc[r"qUkLTC'[Ep6Ce>:mLZ3OZd\&]Z
%%\u[Y!&IBfIM#D:4H"+I4NC`FYr!Xt;5Aud.7ReO+J_4M$F!9e#TZ1Si%2IY&NgSBd@`'YCMa)2>"=)O3$&<H.G@-f,e>9%!MQq)
%BZor"/RDg<FFci*K$s&o*0;F71*3#V$sppP76An(In`1<%e.Fn(J"l?*3+u%'_-sj'FSH.',S?5mKh_d%HL1j(Nt%\8*mH*kSD!J
%GA.(K%clIfK#L<`G63]ViZ<gi/n,qp?L%s..o6;BabpoOfG<\%Ab_0_H-h4RJZs*Jcu'.PV2:;o4:EnJXY#G5B*97HE#a]%8B3u]
%,eCrNoL8-m:QhY.0J/IJWJc0Y'VHrV_%<%n=WgBLegOq)/59HPNFXIJ[0<ubYQB$B$dB-OY)0B6^*3@VlLV526"gl2]PF5CG:uR>
%QHoP,V0G7XV$Xir^T'iR?00[F_M?D8;6Trp.kP&D'St<g8@dR6gbbMZ65)o;^n:fsg%5Q)Se"dS(8Q_i*pEnlhrD(,N'E?/mGICC
%CijFm#)&NCl`^<jQRL>O~>
%AI9_PrivateDataEnd
