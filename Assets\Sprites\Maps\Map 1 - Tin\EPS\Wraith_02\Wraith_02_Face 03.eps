%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Face 03.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 160 128
%%HiResBoundingBox: 0 0 160 128
%%CropBox: 0 0 160 128
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 7 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -128 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 128 li
160 128 li
160 0 li
cp
clp
77.334 73.334 mo
77.334 58.6065 66.2725 46.668 52.6279 46.668 cv
38.9834 46.668 27.9219 58.6065 27.9219 73.334 cv
27.9219 81.4414 31.2783 88.6982 36.5684 93.5889 cv
35.7353 95.2041 35.25 97.0771 35.25 99.083 cv
35.25 105.112 39.5596 110 44.875 110 cv
49.9414 110 54.0849 105.557 54.4629 99.919 cv
67.249 98.9043 77.334 87.3945 77.334 73.334 cv
false sop
/0 
[/DeviceCMYK] /CSA add_res
.0251163 .0509651 .105379 0 cmyk
f
28.5104 79.1418 mo
28.1237 77.2705 27.9199 75.326 27.9199 73.3301 cv
27.9199 65.9235 30.7201 59.2208 35.2415 54.3887 cv
30.7214 59.2212 27.9219 65.9244 27.9219 73.334 cv
27.9219 75.3292 28.125 77.2723 28.5104 79.1418 cv
.28455 .2121 .220493 .0271611 cmyk
f
30.5596 85.3203 mo
29.6525 83.382 28.9586 81.3111 28.5104 79.1418 cv
28.125 77.2723 27.9219 75.3292 27.9219 73.334 cv
27.9219 65.9244 30.7214 59.2212 35.2415 54.3887 cv
39.7059 49.6175 45.8485 46.6699 52.6299 46.6699 cv
66.2695 46.6699 77.3301 58.6103 77.3301 73.3301 cv
77.3301 75.4004 77.1104 77.4199 76.6904 79.3496 cv
72.6396 70.6397 64.2803 64.6699 54.6299 64.6699 cv
42.9004 64.6699 33.0801 73.4902 30.5596 85.3203 cv
.290745 .237675 .283604 .047303 cmyk
f
52.6279 51.668 mo
41.7617 51.668 32.9219 61.3877 32.9219 73.334 cv
32.9219 79.7363 35.4883 85.7813 39.9629 89.917 cv
41.6133 91.4434 42.042 93.8828 41.0127 95.8809 cv
40.5137 96.8477 40.25 97.9551 40.25 99.083 cv
40.25 102.29 42.3682 105 44.875 105 cv
47.25 105 49.2705 102.621 49.4736 99.585 cv
49.6396 97.1094 51.5947 95.1309 54.0674 94.9346 cv
64.3105 94.1221 72.334 84.6338 72.334 73.334 cv
72.334 61.3877 63.4941 51.668 52.6279 51.668 cv
cp
44.875 115 mo
36.8105 115 30.25 107.859 30.25 99.083 cv
30.25 97.6338 30.4346 96.1904 30.793 94.7988 cv
25.7568 88.9785 22.9219 81.3311 22.9219 73.334 cv
22.9219 55.873 36.248 41.668 52.6279 41.668 cv
69.0078 41.668 82.334 55.873 82.334 73.334 cv
82.334 88.4268 72.2109 101.328 58.6719 104.332 cv
56.6621 110.557 51.1855 115 44.875 115 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
130.001 74.9062 mo
130.001 62.624 123.453 52.668 115.376 52.668 cv
107.299 52.668 100.751 62.624 100.751 74.9062 cv
100.751 87.1885 107.299 97.1455 115.376 97.1455 cv
115.867 97.1455 116.353 97.1074 116.831 97.0351 cv
116.828 97.1445 li
116.828 102.626 119.33 107.067 122.417 107.067 cv
125.506 107.067 128.009 102.626 128.009 97.1445 cv
128.009 94.2598 127.311 91.6699 126.203 89.8574 cv
128.562 85.9082 130.001 80.6631 130.001 74.9062 cv
.0251163 .0509651 .105379 0 cmyk
f
100.781 76.3479 mo
100.761 75.8724 100.75 75.3931 100.75 74.9102 cv
100.75 69.4673 102.034 64.4836 104.168 60.6196 cv
102.035 64.4838 100.751 69.4668 100.751 74.9062 cv
100.751 75.3905 100.761 75.8713 100.781 76.3479 cv
104.168 60.6194 mo
104.168 60.619 li
104.168 60.6194 li
.28455 .2121 .220493 .0271611 cmyk
f
127.74 86.7695 mo
126.47 76.4599 120.52 68.6699 113.38 68.6699 cv
108.17 68.6699 103.6 72.8096 101.01 79.04 cv
100.898 78.159 100.821 77.2607 100.781 76.3479 cv
100.761 75.8713 100.751 75.3905 100.751 74.9062 cv
100.751 69.4668 102.035 64.4838 104.168 60.6196 cv
104.168 60.6194 li
104.168 60.619 li
106.852 55.7581 110.878 52.6699 115.38 52.6699 cv
123.45 52.6699 130 62.6201 130 74.9102 cv
130 79.2695 129.17 83.3399 127.74 86.7695 cv
.290745 .237675 .283604 .047303 cmyk
f
121.828 97.2061 mo
121.835 98.7441 122.105 99.9551 122.418 100.814 cv
122.735 99.9443 123.009 98.7109 123.009 97.1445 cv
123.009 95.3125 122.608 93.5635 121.937 92.4639 cv
120.968 90.8789 120.958 88.8877 121.911 87.293 cv
123.903 83.958 125.001 79.5586 125.001 74.9063 cv
125.001 64.7471 119.929 57.668 115.376 57.668 cv
110.823 57.668 105.751 64.7471 105.751 74.9063 cv
105.751 85.0654 110.823 92.1455 115.376 92.1455 cv
115.607 92.1455 115.846 92.127 116.084 92.0908 cv
117.526 91.877 118.988 92.2949 120.092 93.2451 cv
121.196 94.1943 121.831 95.5791 121.831 97.0352 cv
121.828 97.2061 li
cp
122.417 112.067 mo
117.553 112.067 113.651 107.901 112.32 101.831 cv
102.816 99.8506 95.751 88.7344 95.751 74.9063 cv
95.751 59.6328 104.371 47.668 115.376 47.668 cv
126.381 47.668 135.001 59.6328 135.001 74.9063 cv
135.001 80.3115 133.871 85.5664 131.786 89.9932 cv
132.583 92.1484 133.009 94.6025 133.009 97.1445 cv
133.009 105.652 128.455 112.067 122.417 112.067 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Face 03.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l7QEA.)O!Ifg@!sD.9ON;h^Sd)]/q<:%WJRiI?=@RpT;\Eq"%5qE/c)72d1XC*"`C74GZa-XC(j0_.Sm3ILq*jB=^3=tr
%h<V7O]`,h=c2[[YRo%nbUY+!h$Lg^0jl(j\IID[!pr2iChuD@)c*QkF4b*FukAT6?HiEEAV`-9d]`%PZ2lFDQI=:^NVgj634MCRX
%o9V,fr;#LMo'*K1LX-rhSA2X`nacMCS%#Sm^:PhT*>.=5o\j/*$_N"Xn9:VYYC?BV8ABQOmN(p&lGglZGP]F"s3KIIO<;)r]eB.R
%G.hG]`Od+*^Mu3i5Q/[\r8^B<k-mRT2ui&2mKZk7rrl3J>YD-e9<$afLqeVk1=cLkI;/K(MOuI85$[U+D1"H(BG=[&ca]M'QX5Pj
%=AO[Fo(hGF^\Eq8iT;53PhXIQ7m;onk+6po3eFQb"X#Z5^P's;k)X*Z+8+$tBDZ@#`RJq$-Y)E6#!_0@.^&>t.Xn=@peV6k_2q%#
%-Y!S1i>ZCmC$mrU3Q0(4/HnA.3d*E+qloMihSN\opfN=Y*lD&D:4qf*Dq%)`/k)kOeJ&,"Rr^d#^`TE0V#VT3FaQ)hT/'F+;S_.S
%hH^Z??DdFheKYReoI8uL49?a)HAME:VTrTXck1W7,bI1nNGG6J0?H-`o7CHS"%h<afK8lgZCM\M9DCia_4&/H8reHL4Qi0l:[lbl
%I4RA&H$loE?TjE@D5@u;Zf4&o(B7?4K''fIcbF`(LDSj^Hi*HkM`7j>Kg'eWET-<SbNIbFjZgNbp`U'\^\L(kn`KcIpJ8PNq2Y3k
%c$0SEIJ8@5DZ"/)+QZi('Fa2"TT;/`SkCkkQ:E"r)inic_r&@+]Lgo(c[UD`2L$E`J4m[?#&8#RYu/pCWKX^ThnJpKe^M#-rp;n)
%_+4Lhq5\F?5(30qfMG6qk3uKgs3:SjLOlk[K9&WWh$F%%)$^,<-N7XOY\W=Q_7cH?l,E6Ys7#0#4b*>$o=)T4%WC8'o#:V6bfE/d
%@/10*(Z2oRrq9Es?bD"4Et:@YePG%LrVRs(4[+>lngSR[BcuW;4Rl3Chu;U145dG`?@IZ.ou!9]oPZtmma[N!IsI'ue4V#Nqr[_?
%IsU^%l=b]"ngM;bX2#-9a>^>7b7/82S5F))hH74@bd:E0[9t"YHc8SS3H3WC]m]F+(U&>`a=!oIbM_!UrpfB6Y6X[$iMQ,UhKEc4
%meSO6KsuR+meLXH!?]$:rFBBtmdX)%M/?U+IslY7$E)ier9"YM_eWZI0Al[nr]:$EItK5bqaOnH#[dQ&%=I*QCTR<iK:h_lg/gLg
%q&TD'+5ag.m[G<5rr&=A&&*PgqTZc=^NoD0p%'.%iskt[#<1Q??1dQ/0h;5Gj`dtSp^Zd&oE=%TBGpd[C_A7Kci!S(k>0,YpA!m1
%Dgq2IQ@J`!cegn_j!V7Z<I@d3^OH1Kh4(mXTYJ5:ft[M*r.E]5F$#u.Yq;b_BK*cbF25QJn/kPn5PXcbK`B=_ce;6pgY088k?tF)
%eQIILdQdF)p\?r@6PtVV_=+*+g`PlaFe`m.j,51.$2B"tO\[o@fB)[tT"f[E%CXU5hPUH?J,/a0e$mf%IXM%m"$R-G>N9lQ^^B!;
%8@i?e4@T8P>/lRbJ+oFZobeU^2_[lXrj:>imsSU?e%M2rqX0Ce!Bu#3os^PW3kk.iBiOU%=eE_8GQM11QL<5,e'jX:?i8mMdr56\
%in;THm<@Ien`HJ#]Qs;j3Z4etd[?!AAkTGWoOY\\U.dBB,J12b._XZn?g99;p0Pu+0<bBJc-$p\^)AOM0,GJ=72e7u88j;!n8!)F
%n1-5ERu%5l*Y%s>`]s6f_+^fY^e`lj,b\e)/*Z<QG;#Q$I/_=(I/E@-rUq/$.rSeplntuHcT#V^%L-!VEnhLh4!k/lHg]5cc,tO0
%=0YPLhJFN.9b"B^"8A$0r:1cX\,.sb=mMFD\g"#":]ITQ(Hcg7KljpRh<jdQ+qmr3g4<TDC6p:I.s=k"l-8>U(Eh133@sWHk*LRH
%$A.#r(]X?';[FiJpFfj*(39#imp<YRc9dt,%S+rG?\X.mDOM%Q3G<07f,b\,EqCK("nM=;5<jJdn,XrPY@m\-+5]9\q?;]<5D61=
%OA%,Ti!QQ^6IVV)_Z/S=kR)_O%;#eYs5-i?6;@4a%qWd'`.pSgp1QAZ]_.CfHMmWuK7A@Fi.&UZGUpVQpe"GiPQE/KJ/'PV.ZFhc
%M#T\93C5aE'Dssi3Qk2M(FZahk\"p84Hoi")htPO%oS-[T^^87h[P#m.%SM3^(9H6jTOZRjT=O%h.DH":dXmF*T/5/YGIRWoUW::
%'dsg2)uOMK3gBep+5nSr+6<Gl.]6.:`\[PmCun"@\+p)OodtPuo]PLHn99/0s)O%tL'#L?s(-RQI\T\9rFbqOCVOki(]3s$:`n$4
%?g"=?<qEHl`4gKabPt:P`I6mP^M8bb75U1d+M-hVPjXQ.:@@+Zh5tmopj6-:aVURtoaT'[+8lMgg#!!("kF#tM1fJ,-oJBYTcCp(
%SAHSB_eloKh%O@l'\X!:b(3SWY&jNTV6q=P4U50DRFZDT<K(_f9Ioj(ldaHVNNtSQXiO8p#?lCc?:-P:\5iZIa'A,7Pn)mUj]G:>
%>].`%L"E@+R`b8.C4jt#Z(C6khSRXT9>RLMXhZ#^lg=325s)G`41._nmB1e\jTM'Ho[\@RoM\!KbJ>4s+i'DB:_&7*Pk3j7f?3LW
%lU']$j`#R"d\.Xu)jEYm?K`06/.O+&&UiS2j`H((HT>7i5rmXXQ33B5htP:hEk0u^H71Hbd)U5Y$XHB$Jj&DI]61r=TBT'n^38B0
%'>ON>e9KZ*(Tr:;>Q-8Nb5K)fQ!bn1J^m92*?Ir!nuEJc;cjOJXcQn\Tj3OlnQ-jYQ&0i&ia2TB"5efbSc&^b5f8(*rl)MX3>_7h
%F1=J*]U\];\CNr@CZ&=1W>qJKaK:\.*U&Fc4saG`fuZ8E-K7\>PEPG]j@?G4JKnIa7?Jf`i'nRe)<=9mj[T]=KKKRU\ule%l!1fb
%Au/*K;YXWm;Fo`U"=cN%0XEaGk?BF[DXcs[j[]R-g::-r:b!3)%67HdfK`jl:k><ip])\#)',t&X9u_&:_$HTJY%D&T&>;u^naPT
%5f!U/*i9.&TG(!]1)jb:%j+V<YQF_h<nj;JK0I/F*FW>I+@5NFioD.h&`D(\QA%\[YAbZqNF0K:+$MXaK*T,M#6fuB-pC)XjoK_<
%#L;afrSc,_CL_RL*C#9cXi&P;7gqH@T\r*P3kR>?'!Q2'H/_6M:n=hCcjY;Tk)asP0V^&+To^,\aDHs)/l(+<LBCig&!UBdP(Zh4
%iNt)A8eQuKJ^-#@m.u<t!5jQoE1g.5iPVL.cCg(0Sn=Q4)I27daQjc[ljTLTREcuj9EQ"h25!E&QgfpZV3@H6h:<[O4'cd;dWgcA
%lI(q$Ws[EMQShcR2=Xk%FX1bd@]P8cNij:,1V-n7XhYF1FeWY>f>gu4\h;!kkd2*TXbF"iGRZotZj)OIs,H05k?_b,jrER],kK9%
%SS>6PSfQ)GRHF8M1J9MtZ\pW/A7hCVl:GTpd>SiK0*ukp5\ZIY;j=\N,agTSfJCUWRH/XXnj28j_+/5)8=DghN?JB7"*G1MR3R^O
%9R=h>,L7M4^&g/uqcij2,%h,0(uJQG]h7Dg2<HtnH#K\*!]AP'C%!MLXo^K?=a4".hH<V>!]A:'52T3ib<ftjh0e[lj',ERQO\N;
%@TPN8koTph]FH#Pn(h&h4Lr97cpO3cJ,C'dGs?bp4\gZO!*u3n%QSZecK,IW\[`Y$>L`qN+ha*EYb0!`e6&HbOMAAX,^j"r#eei+
%*hh\@TOi-eQ&ug$3[ot';AS4e,%fqrRWp09itfV9LC_>=_FBf"VNWXp&iGcL8]7(uQHf>)`UH.t%p4s/n@3%^LD`/*]RcdpWfMDM
%Z\d[b'$i<RR@Ogb:_mKSK$$_;\WDu!!/?I7R>4Na'#kEsk0NE]C1.gQ&66RTkXuBqO/+=lfRFY[Y"V'HPdt?#HP/&AnIoY2e-l6n
%j_'Z"L;f#>e'U)h3-tAp;'CPqN>!=##80&.*(c]Sd7/m)ITJP,po%Y!Ukg4c7AV'?B,\Cppj3Sr[ts%'D00M'gSM6KKch(UFmM$G
%kY]()O@1JUBUOP$;5+An`5s&a&5a.V=9rajJlWL2U55GGUfip9+ek0f=\6[Ki=ZlR&0fD!'abL'5-Pkq+s`#+#:^u"Ou4i.RdhXU
%gmWK?aQu`,P5/[_&8+Z?_Ci\,N[9Piai(t&#Ri(9F+ASA3m.1,>s0D)E#T-#+!c4V>YNP>r=4]b<[DNZZ#3:L9%0@R4!+..)MM/$
%3iZX8H[@XRW>Wd#1S)@pQ]pds,,Rph:**tJ_n2C(nk;9/Mk=asjA0b)=<#j2ZE62$C6'Ra<Y8kFLh-UQfA]*7efl;R&93t:f[0h%
%&B.@;AKg_5M@?[>(/@jdA\h?b;ADfZ81F]*8fSkgkY`+#$DM;joH\dTp,;9jI"r"(?3/RNSLQfS$M4,6IN1`^@Pt(jK:.!+F1<JR
%oKWT*$Jl0/_/;tA@PR^q>jNjgdm<bdEj$rTL\VI#,N7ud<2h#;aKqB`<\W*pc_=[87):pI8WhQ)/mX746(tK9^<EMAIL>\Vs
%7`"X>gY9[%9L`_;4#3S:$p.]+_Z_cb%qTc?F>:)$X'qNi:bV/2/&q_'(4s+5jAb5^<DV3!XhZOmE[o^%CT[ioTt!VkFQQ'G<U*I1
%1AkY>MBd]Gm+A^Y1W&#Zdk73?J^3OOkB2qb/u%J\`6n<h2rU+a628B*i.VKqRe?m'O0*.!%c%JLpOU,3>T.qZJY8dBe?B%h-/ceu
%ft"/U<8/1(4$*;3.+dV-/(\BTqk[Gu]j($=Y6nr8*(e:?j]ed*GH#COp$AEq(XtEDY?NT#.eAcDk^:sRJ^/!_R1,kWfO=A"(>(;$
%jC(R)J^/6f-ZT!e1s^lg"JUHoq<@.V%q8Fr!Mhkf-Wus,Kiolsr[8)WWV$8^^_%iD'02#YgnOV0\K3bm:fd(LI^%s]KBKWOf/oFB
%odnCcgNZ"?#P(#c&Y?KS)F'DT]gN'e9SV@S:=Ug_q4B]KO7I^.klb``PC_gHG1G=^59(Xt:0]MKgi7MP630iKaFJm_PIY9`^8[W$
%q<,RRkWpJ`UD7pmWjD:Z3BN54%N"HgJG[1;Dn*l5Br`s9W51rn"\IL;R9Z2'g4@fJ)?9_-]Bj).a5:Km`\$X3fhGuF,hYEK<lhRf
%)LQb^2r/Y9C;4E8.j123NmW9,HR$u,clLPJG;S%rLM:tZSsX\SmY(Z<@-e*R#t&K/]pVllZ1:n9k#G1b%9BX7oM2QJK1Ocfd@W&r
%)"?[>*U!6#N=60PF`n\+IaP2r[n_$?ZOiudO<_;I:SbPVP.n&Y&D"BBRD$4GUo2<\i^C+aS[s\(fu[*<-B7;V*48XK[H=UqM>;r;
%>LLk#0oc[:>QY<05Yg(WE8.7;O&GBoF]kBWC><>j<MQ(]EY8+^E#+YE#'YV1=8q3%Be=Q;ns)o(,RGitA0g<*>O#"['8[\e94A!,
%E>d+3pk_mb0W?UlA<t?b-$#DE`@N5EBDbU`eWWPU(L@2eBX3gcOEerP0&%;kp4021D%U()a+:&d1Tc`H><#GT&A&0O6mA`L[bPE@
%ZPu"%6+]B`5O0^6ZK[HWc>FV$FD3>sC<EoP:q^P5^CFck30>hJC[8F(7DR*DN\Il/^'gtgZI\OB6n=9uSK,EG2K09E?9k&qA90^M
%O6"MEP>D2oR$`XRPcYW+-E=lDBV9"4$/u4okN-dn%*eqg82Q#('X8.:l3XiK>@;UT()(We(b/M7%\Vuk5bJAc!iY5_WrM2:dXTL[
%hsUj(F_tB+?gPq/k%'i"#]$u6/NK_KiedTSh,#K1S9o3u-"88kcFZQSSfoDcI!D7D_H@YEXhJTR!c:f:5]$0PNV5D$k[HHDX]=RM
%qWAJ2PY=PMK7\hp[h'Wjr^.[GcC751b"DTMc5C]9-XpDGg\ouBONX3VX9E?$:Z\r5a&SNN3.4Ym(9"KA.9<a>EKER;@CUK[a<K$W
%!jBtSZakEY$cp_("[SNl#;c!;J-Sn\(_5Ld@0[$i*AiBh$qmdU%UeBJm,1%"K771S!Us?6-/T"h(k@7K!?@WK/hG:Z3#DDmDST<4
%\-"XVTa&c7#'o0FL@Y_s/qk1Ae+k\K%+Oeu2F"JCWSL?%J*MJ0gE5d9@.q?D!F3..D(htllX!_ti;L&W"_t]]WXY3^5q<mhI*^MQ
%XR#Ie>q]9fXD9jUkEW^beG.Ro?EWN=OG6dmXP=[a@k2l3T!,9Ybt<H[@!F@Pprmb!O&`9cLrC:(+>_@QXB^J'CtGkl?CEPOat204
%=Lg`"e$="\@#6I?@^]7.2Cg\Bop/W/aI3pMe1N?(HT(1)CgmF50[Krm2"u`!SF5uk]V?I4"_q"C2+j?1SP#<m^g[h.Jn.;R'g[_E
%HIUSXRo5UPfiAKa(Zg)1#:!Xp3+Oo)<=uc!9e/dY=G%t2(tQnK"cRS;"NRP%6^n*Z"-TL.EXg4G#A1g*5(U%l6*lk^F3-A])GK.C
%nE,"HPmA@Mk+>q,%!R9/FA3e\1i$9]E#QmQ>d)GX50"!NYatm^RF`jh;RU^>8:djAP-Q![/7c%3"L>BpZ8S5pqJ[R("PP10aDkn"
%#\`fCX2\;D*=&m$<3hFTSHLO*%U&)6%f\chXmE_8WmBd;?Ff$Y;m5!CRqnn-((;o6]@]rrUY*rC_T(<6drFCG/0i.rRg\!<=I)(+
%DrYNBGaC.%@Ke;<\m1DW1@kT#lr*qaPs(QKb&J$/qcR`u)utS[UPo'`^X\3?/Y&VuCn0/76e7lCo+b)ZB%mk3[A0<ac:4G,>$DMH
%J*fNABk2M3A?@Fm0M#ZBMB]sED0OWYSK=7Goq-qTW4eS+JkhD3r13f'9f**]2bU,bXRO?SLq3mn81T60EEV*mAI1\$BTb'R:"e1R
%6!HC_(ML2[D-\a9q8rM`(@Q_dmsd1?5Op0c$1WA4K\lLNao&MfrkZ]Y%,%=%i*#-Lj5f$t_JdA&b8(fP2gPhJ2d'Rhq/>eMp`l?#
%MJ(V)#77VJ[-uXoQc-&FHOOae?M;n,6mo8`G2l(m6C";3(=_JgT+'RuiLL"Mr$5]KLW.!+X8NUM*hZeXOZdY!cb`NeXR^7UTFk8+
%@h2+e[/Sa>iE'fd76/qC8b7Wn3S(mFHs63&ei]9MX$+R.B5;NCccfMSX&[F`q6]!k48%#h[.YU/_Z]F['>>!73;KmKeh7^AJqE3S
%V##^sXKX6^7K1T0:R\A5\V9J[Ks4hG)oGH*N+E2&+&0aUl'd)GZd-?U'25Z'fPX2p4J1U)RH3_@@"_M5T(PWCCb.Hc*>glfkZ%[C
%San=0pt93JdmAXJOsTNEp,JAs%V22a!\tTcDg`qN/YtCgfHtl\/\0hYBEs2*1o*+L/$Uoi8T[=s)o#f!U?DFXiYY4/#:k4"g7m(s
%.B?q'.&`7[RLADTUcV3((8u5.H41m"%(XdmKnY*`8q,'i=0-VMN$mBr$L>]j"$ted8.RqH0u,/:MGkC1QJbL^F"k(6%0L/K-P?RQ
%a.p^nNFb',PJqe*OPj)mgIcHg:3\tY#CjALGn)t#A3?uPJ#h8EXlm*`d=->3(T6hG)AB>Wcf)cr)]9Jib0lJ;qqTZCn$`Po^((so
%%kRZ8If7OETQf\]<A)kL/tTQd=[ZmE>S=\]'pL&#,dDhf4`E[uW0nosWWA:r/pV&)S9RkBs%+Bk$&;:taE)AWT:DZF(_3db`H.26
%&$0;akbKRIM63@@O2O"G-_;"FSL!ft<8pcG\CaY@.-<m*5&$NDP*VW[QVrV]2rWE5d>u6-cc#]6F9E5><)*Z44X,/UeDF0bFD9f]
%l/[7QM!]sn-iCi&MbYt[5d2Ej9_9OSZTX.@5:f7K`%#)_B(jfn'_49$43r#s?GplbObY,o8-r>H%Qq9=Xd!BnW*#C!X5)-4[ga!V
%QApT[m"jj!Pe4'^Mk[8"ngN9(A%M:Z-pl2V#o&2urm'J$BT$/<R<DH7>uYYW*H`1W4sF7,qAHSm*B3M"o00b@Z&dBBBg,))KTZjd
%lm:@WBsj/9"=j"L3nmt]k(f2W6t^_K*S&2jP5Vc&UOm_<Qb/.K'hBn3Ua+]"@s?W*+s5s[^S86Vo4jW.RM=XET6Au%ho;6LX.e,\
%Q6'E<Gf'D,reWV"iJ>fY.>q)*AV=R1>MJWFNcMXJ1#M='Lij)4?8>=5nE"f_jW.F:]Z\*8J_$V<]dNof"4pOALMA>Mf28U[B>\g?
%MNuR>IYD6*]-u5^T'J4PX96E[D^cZM&'6kB\s@$\257Jtk2g9],:?-PEF*--<d:cu[oak-/<,D:WlbD=]#8Os>M!:5>7?j+0d\0t
%3A2.V^7EFOSEO$#BVBMS@=bIbQb+@mH!?r]*GYE6YD#DBZbA26(=Tu1e<fa?_Jf41hM]:%=Ucq"hpMiN]M/HcOBE.m6d1d`1>L@o
%Y4d:9fZ><fi7[reK%E>9gesk;]"_POgXM$ePHUn:eU\bBHj61F*?#9C_>D#_G4PA;,dLDL.6UqQ]0C6.mMc5;bsC'dB2SNUldnp>
%4obB@%mmkD4Ni*$<CW`To(N3Z/-H_K&.!<0nRW$kd>SAMd'F/?K-P"=l7k7g[*YDM![E^:^1@j_&4Xnu`+:'`jt>AFAVF55YX2`3
%aI"Y`%tlPH[iu)\SO1EEXV_<XL]S9Fk!e:6-mkXVEGLtG:]NP-h!@SaaX#Z7=9IOkLb[QTbciQ.=,nPtTMU5l#R_`>`/GoI'Xo>E
%Y`M]Bf^AsEi_1j='"tu7g6ceI"`"mMc'0cWd;0XKAmUmb0&sDrrAL-Rkl4JSr@Xt6fa'B=G&u5fi<5b.GN-OcF8'6t2jfM*KZV>Z
%,`]R+U>F/$Ba>NV;>ggJ?CJl!hmJ=ca.fK#XjB3k[#.,#-[gpM>71h3fgJXh_jPh<%!AmDFr9pJjPEl]++7:Z=;=crS8Uaq!Kuh8
%`(N!iT#igG67Rkq*2"^?IM@1'V%K/P]dr^b*/\u6Tnr'J+5p\m=?Y&&UY?Kl:`rI@ATdbLQ(X5V_+CRK)n=6YK^R&/WVC/Vnmp6:
%=WQ<&*6aYEcm:V;Q/JPAcIKR-%;O$\![l:%fuR13M2#A'bZeW9!YLP(>f]:ZXQ@^&Ko<1!fSM@/>'2I)<7o#$a^*su#X4XY-^2@p
%ll/04/L/k6,i]$B>DGdB:)Xk`8c0(<;)M&<Yi;Z2:p353#:Cf_*/t0ncaE%!/$>%^`a"r,I2#a04(-_6Jh@j]#<QolT#:r:%M_bD
%7!9[o8Z8iP(l?(uj6oOa%Q+P>$3^n$\mY-e8RV6S$aV98/W=h$hse(F)07rcaYiXW68D+5D3@!AZ\2j%i)d4;I%HGDLCY/G5s@=T
%F+@KAAbTUN5rN\5'j,u+C^[It)&VN:a>jZLKL3rbO],BC'H/t884)4#n:C0t3?#s"X@R3%":?NT(of($!\O#q$niH/nf!r8k_H8Y
%iV:<>2So*"0`QD"q9M6b_[:]T"a4aPap9>]#<&bn#msEE,n,k:_*\'=_C"#IQ5sb^Hpc:GXAo+kgb2%%VXQ.q@<3oR3='b<[S1.n
%]1WJV2;H%h7S8GuX)aTn%\c"bOure:eSpjEfVf\W7\7EE#YA,%,:!XbW+Hkg7QiTo+Z/Dg$;u^m->2qA)Yf&Dc]!78R849?^h:d=
%VoHI%q^0r10+2bWO9Xohjst<,/!?'aZKi4*WiD#U$/5TDISqc!NqMZM?$9+C29pT1Kg#g#W74]<e?BJ`lj$"UDUbMEG."9Qb^>#b
%["G]L$jj.A1Kbf-P8).XoJk@Wd>a3:mD6te3T/,@h\PnjZla,5d0uaO$^e0ib\)1nZN'1n?Ti9aaL,9[%85Vu]OCnelI9#B-87Df
%0+`<o/3L%B4XU@.N,1qGM.ctt]%g/#hcDpZ`:p.9''JEl5]Ps'K03bdl+K^A.=3q$kgs.SJ`OAf>5itc>\F>S^9[r\+)"A`d\BsZ
%Y9`(N#m<Bt4RQ6j9p51&Jtb'C7iVqcY`n]m`cCafq:03%OJeph*;(T&,JD;BI2;;-C_ZVanuEJ/%U%",8PBm:0%U.=f5f#J<D:7Q
%8EDaSEF!J>?Y7FFUJ'5i:ua2,j3-=UNRMY:`4%Hl)?ZWegpt7)5)](E="]7Sj8'00iDj!6\FqPZc>Zd'!&7)\(RZREd8MVao>PaC
%"ptgAPJ_K%9E!p&27\Tpee\\6)K*tKag-YrFLXrpEn!F=&hs*BVA+d3"c@71Pg`2H$!c+9VnR"2TY)&er%*<o&?RKnHcrLV#eYR<
%D(_!6<8Fq5bItZ``oYhB;[\;3I+Il?Wq#O%K_9b=1-`)"]>gfdGPqF"hthEdnajNgJ&*%)6)s6Ye%MEerG^1bd+oC6=r&Efb5)C)
%4,!g/.55Z\TXS"Z/%AN>lA)$cmdW/g]me&Sq0JaAEhBc;GHPphm2#l$G>;EdX"i&6E\cZf".Umk>XLKKr7'n68ietfCsM".H_Hn[
%"-4a1PC/1-cLW@[HPs=PL$\10lj;;`C37'1mh*2kL[a\`]gkoViD/&"FU7bk&W`cB#C]?LK?;<tchra`n#+0>[@c^/O@m>)ScUDr
%I%jN37"?#"TiKi]V']H:Zm&9[PgF*?iOWKZ`sHM$<P\ToiU'bD3]>)-`Qtg_FHE\9OTBs$s*d2JEXo@e=B\3dkpUi*kEM!J6H?H)
%k.!tFBF,\t#?$WN?T9^%4,sH)=q6J=2eA,V(M[b2*TS7&"2I\__B-)LK?=pRf&'kp9AnFoT@a+/R'+k#@A%(KV;X)(@WFpn@`Ll\
%EEhSCl=$+A@EmXr"au^5cq]M/0d#qGCTW"]o^</LYQ%j.hWT-b]Q&#afjpRWKQet`Q>A8pp-\e<s1YEtC(.FcblFZ+-;?(&@NT)%
%PBrPmF:/KRO3V9Sn<C_IIGb?%6Afuj=;t=oVj,f8_k6Rsdh@MM]pep#OXV3IraC-67qr+FiNLAP)_$(Z2c%lk/E_m1H_+fGPtY,p
%>H[triA"]90qN:;=SU*3H4[9j[W+f8fO[\-[di4Fe9a.Omsm2rlITmq='+;c6AV4'Ifqfl.dTd?YTEaFRC=a8:Ya3SZ&l2$H1/V9
%XcF^nlm;)kF&aZ.`P5P&ogrt3+,pQePr!,`*69+3VP1AnK4=X0h'Mb`ChDNs>=Z`A^KgT'h(#jh&^&%RM1`SiP!C<M54>/LGu,bQ
%fcp#b2pb*CPY3_(?DHrOLjMA*fj?<$G[6>V`M/O[GDSmU8mDU6QXfe3kE[';6B1)'8DRiUDDg/g0\<NYnOaZ_JUCNRG"o^ZF8[p?
%aQDflC\F5Q`FI:KA%f3MED`$6O,\\n%Y4=<ccCm,kt&)WQ:K1QGb@U!o.%)>jqXs5#Bpk!lllXO`94puZ59,X&NUO4[uAc"X*C18
%O[BJ\U:1f?8AR]8p4@8rkd;uAFTM4kQk/<5>loG?:(tB@mW:DtB9i+8b(h`l]>9c`U=J1Tb@`/>3!iT4l\^;%fuZc&A5/"t$7/r8
%:qHd.>)5pHZ"[I?2/^6t`2.eg9-7Tt>R3gNZI`r6Hu60:D2"YWhqi;_R,B7Z$rLYn3CC`q)kQ\''Q1@R_rmueZ\UI^5-CS:XRicT
%2H-1#a%q-@cT?HF/p??h.s:r=F`A(3s5BF5P'TYl[59C;b*7]1)brA,qA#r+^Od^RnFG%4;PI`;+r,c#3D$4Pq?\@nS;Z>K3/POJ
%$6qFs."E$+g.:C95Y"C9T.GXmK,MR)kaHMq..>NYpLaknUu*a%ePZsJ7C(kdmT-?Q!HaT*"DTJh;Ba:V@LVk7&8:<9QrCP5YY],B
%eFK>b2>1dCa;W02mS4\,GK8OI9+j!gk4W`QHf'7#=498Cd[sCu@?(*_F<U,EHuY,mH::J_GLtZ7@Vlj,/%Z6IOjkSOikl"Q0>CUM
%.CQ\MPu;Z9:q^L@YKg:JZ/ZNGKs&JL-F2Zb-g,M,HRRW0o9B.unkJ@l>J#]%PTKfHW:Z3S.Tst$$=;$+060Iu8R`SJErA=odn3Jp
%p47@P,]&Wkm<6?9SYhWeh0<6AFY+!X.M-7aP$CU4Ud0jfH/`1`6Rc)7`NCh<&T\sg\EYbh(lDZ<Br.&d*#9M^6a`8nUC,W"Uf)9%
%eRUo9##!;u&k[/KKMn$MD7/hVp\W6R^,&n"QG(f!n\^U$s+h[E^6'nZO_B@'&G+C4W"E+DDs)dl6'AtHeQh8d$0m<K$$,DPH:8;g
%l'krP[nP:5,s@O:&EYg5j0GZmI4t'h<KBG/o_a0'@e+?Wr^W^)Q:IrHNT9SokY788Sn1]X8-M_KR&:sm/1%#GpY1hoD3<stlpm@!
%,4@p'e1j!VIHW5ud=q;R8bV*C?HkS%>(X4OVgaa"#4n74(/rC;\`)2'@lLj8N_Hl+URg>3l7Ho!DdD:f`P*=K%.28Fm)\:+>c&t*
%m`W4dUrKasIB).>BE#;_j>%V768i&GTq$DkOM'_OI"e^cdS#\a'U3pob?!KJQbK"gnh&9c6@WWs3_N]/;d\c_mR^@34+m"HEN_r2
%FAqbdG:MK-fDjeV[4aE:mFA_&:!XSB50'H06h8V;".]RE1)lB65nKju,*VS61<u18BTXo<;^Y<HK/@/d8ME&c2@+'1h'<O%U2n8E
%'LHR[jk;s[9T[<l,no!(-T"e1neXE9E$^dr?4rs?P;6gI!0X%0)X#UWVZft5rjpoC9s;gjCM_k]4FO[^\njuEi<RuV_tK:MmA1G"
%,SbqMpFd8+hfT'';iIh2iqOWMiMsuN#@&$bcS)HKb`.D"=*crr&N.FFRH./lr(0lJS=iPPpCq[2Vu"$H,t]DBE=[DGg.>3Y2URG(
%b.j_0lS"!lpP,<t-N./Er?Is47@[P&lG*;YJ+lK7Ape92TtM_%b;=b4SO@&/B7Ob+0!e4UfJX&Y:90%uQN\HC*jS=,[cAH%T!=V#
%coB"g+iJ6*iXIt3@?2)gEWA]D]\<+lqu(rTh"L<Ga$4Mr?e`=lj42u^3Hh3'AosZ`K:9P`0Cb^TcC?<3`c)R7M$N>jn_e\MV$V>l
%*eZ\*/%^*Q*n3?T;9@!%XV[WV\P@)"c*5#lO"Y_KS9?U@`Fmp.T$*p^@i*H?#Ih>?>/J+<DK*:9]3M4Q-&q(_'$mh(la8R@YfCt]
%m;gfrp?\?L4CLbRh>OoKbL_+6l;!&@H$;U$lauf3%]'PZmZl;nNnsO1*Vi;(EnZZ!.![;6b17fGs(?KrL!Y_O'Hlj)iL=m/1P@pb
%0r3=_0=fL'aqX?)cN]I)/#+bt4RN^JKUs/ST<$!3Zs/s9kIm-@/8f%5'CL//+4]Q@-&B$lWdU\tPVkbQSFB48o16Z,'Hd1ARcf8]
%Z:h3U1u26BAFXF^hl#hH(C=YQ-D)P>,_noC^i!4B\AJT>grV3Rkl//UNaeKWGqHa9(#W&E,F?7]3&r:X=Cn"nE]:2@\4,Ol$::7c
%s(,[<dI+\Qgj5q;Rhp;ND^!h;Otj3TlQes,CPeFih-d2'QoN]sc?lfX$T6JVSB4_Dh2Qc*FVX,"]<i2!1[2Ofb*:Cd:>0g%=;C/>
%^CMH_AGoaX"k$!SR."']hPe"Z?ZL"kE1lU;KS-DJ?[#NZY89ubs"LPjc18PR.CZTT/1^7U\LfSC7nNBX2lkJPn=3lZAlOBNf;Z22
%X42iEikh8^[+U-.PWOFhRW"s+bc&<_K?`XaU`0Zq=je4J4g4XAr)*&8U!r'Ld((0+hg([@0sCK0i4.hP0Xn*I1jA&R<\]^3_-\Yd
%FrNWU]h*?A:SN^#e6p#)-1rP'gl]TnXL.)+W1#m&<h*R$U@Ae!D<'L%eQT$Fl*6H-m:a!\053*8?;oth]O%=cdW\h1\0;!V"5MQ7
%\\j[#aMut-%t(E4bTj"&`*#1;Ca$S7G@RGAoX7;IR?!!r0IF`NL"FjsQj47sak1cLnD,(1pHaYjkVgAkn6!s'3kT+j2@u#=9]#L%
%WS?XVkW=s:I<b$8bUn;jH/%n-_L!YKPE^rKUi3kge!kho@-*`crA(i(41?;nE)BNXI^YY]NUA7kN&+ZLm<_@G`K*U#84KD9QreBb
%Y*E<%-*-@a1<W4Ks4GFE^*6?-C9,<EMAIL>`4.4?ZH-Q=ePh#PSQ.:ZrbIX<Y>?0!)A_C[lF6(tc($'P3F=tL2&09mqMgAZTRZrkQ
%@CccGe"u;CV0j9H,$W4<Z]e8`8/\fujlbSqdZ5_Okb[JZ;'\/W@;iC"?s?o)UgUeW>6IA#"Mo[i_'DW\(\M\LUkD>("&T.9a3r7W
%R6hO@Vf6^JaMp\fhEf;\4F7>ePB"`N.<aO:rI;?P`P]4.eucZA3+^BYdfmm_@%'rA#Kc?h0"@1ID*-7Z])Gp;:Y*U%NN1QFCGhD]
%fU9!"ARp1V\$@a5m&ZP=V$Rqp_C+!TgFZWEa^rKU;*`XH"=YhaD#(p>g/Q<PVYNatE,fY057/0^&BY4aU)JS^)G7tN+=B1,oqKp1
%?$"R+'<E&/@-A1kNXWSA^t7-$3`<rP$LVDVb^hsPU+k^,Y'.dtg2@KP0.[pJk0DS7lXf(n7t[,biMd^@bSSB[MoLtVPEG1WL=OE5
%$]"7tnkma4CAn2"Niu$X=O`8,MHHo0?$kEDmlS9ArhaTu>HN\RHF@;Smu+,DZ#8n-kCAP'Y*Rgr:O@J4R:g'%J_?2M>M#4J1!3tT
%TuMK%GH.opC8`VTbH0Yq;\c7":uftPWG:Rc1OOI;b_D'\b8G208\=V0EUUbDoJWJ.i:%G5VeCQKodf%i6u&f8#+r%02DkBb@uSUB
%H]Q,XA3$?`Ajkns277X&i#;R4.&ZrM`BQ%qn4WcN^UO.$96'b'7HK3Sj';8:<Yjks%qJd_;m7Daj7l:=GA*9QblXX85r<A56C8Hl
%Uqe%a5jm4IGFD`>o1K<CksmJmWECk^6DK?$=*%.r6Yr^+:CVcAO'3")B>JUnfXu"qWAR2RRK^ei#]a4XSCO(;>"s]c/l\%.B!Ui6
%4B)EWk-ZNl1":HVgl,]C1e32\C"-CIC1\H_7PuT,9=k7<d?MCcjDaIO)Gu\5ST]'7mUUC7rSn^H;iMV4Q_8&\j$W!?p2qj%.\5.i
%Q@W52A"Mi(l8_USNhDg\]YN]CL,XkhN)%]+F$fX^WP'V>Ve]l,.'PUp5*[S=j`L+^*!%ZW4/d.(gNH_l717c5'Eg9(o[IUO!\J#u
%ghiJ*G+$=X/i)Tsc"RPCdcGDj0\tu@aV1[t*Qu5Gb'`3I%'Vckr][QX&lQs,N%afi61-5]TOM#_UlEC9Ls9fM%P(T3kJ?dacn[&c
%3g6Fd&BDi>K\b.6:&IgnRcDiLZg"'<mWE8d8Mfq*Zf+j"8d@M^>YTL=K:^3//NTiPojrPcj3De-fAsq/V:(WiCQuld"0Pu7BqMk]
%U^eSk0Lk,,g%K]L.G\6+q0.rCorhEV,$X3smHk+1Z:1su;(c'(N1skV1M^n"CJ'W3<EYu.$!Xl5m-t!jRoaZI=pY$hA_-`NXG#E@
%Qcsb_H'KpTpQ/MG0<Z:t)`\osG@XI2]3<o`l#S$9fr$f;/_d'QgXIk=9`_X'C:l+?:289!7;&+RhS.QRh3>*`jK(H_^4%>f>\C3E
%k,uWh=Y&b)29f7f,Y-rOl]u/>)L3[aPjrbK6moE"FRdFE\k%F0&MZAfb`8W)FUmp;;JY!;AAgt.A$M:7C$OJ?4$#"b'WtqY0,?RK
%;6pP-aHD0pJ)-*reC`KP[^N4H"-Q=fG)r!R7gn]ter<Wb3g!FDY4NlW%[]/m\e&XD-]7mm/SNcsNYW9la!hb.AdlLMXW@=^-$JnK
%jXg0;XO0+`\p,)Y0='\i2DGK7MZ!fN+618-7#rALAr:Rab*fV?Nq>1OK@uS*4JT[r\K[Je?kV3mT"&*j#eFoZd5jm2)jt`Zm,6)H
%0e$mBh2H/50._P'E3<7pXuE`RqXiN<]sL57Dc4bL,,T.<0YorRJ"'XZcV=*#AA-ikfj=NVDsS7S8Q$N+*OB'VIcFiM>t+jc(O"3$
%=[e\=0Zf]!-/6_NAbkdH1Oc<Cs,#u@OTFIB=($E"hiTp%]H9=>_=tBWWr#7j#T-Xk1bBq),i3!fUmbdFk"(ER"Mg9N$]3+eIF[=-
%'L&MC8VP`t[FT)=l\RK#!G2K;dba@W]doA;,]X!tX@X<h!H=0V1Jl!lhe1a*7cT]fBri@!p&nCC7?;Je1#hQq:?+mXZVUcjVs&XE
%U&</2.UqoDQ4u3oN13\oPe+RFATd0m5`*%1)hk\u1JPcDe?L^GD`,/+q'tde]WC8NjmC6.b(P4G`4R-c[\Ws9KK<6-lc$^dg`d)+
%@oqf7MVI3K,%&4reqM^[UK`KIqd@kK1-Tm%"-*j-d/N/e=`7omAZY6SQG&m>:CO=$j%/=A)`+O1ip+4DGMRJRGLrZn#bf7Xe)fkT
%9?BH(p44eO`2H2mW7`WD00O-DbH+hk9@h1K2.t)N?Xf.oC_jodX/<eJHs,c/=f4uC/BW<JQK;Ol*]E5faHEsPO#:(K<uJ7K'#2\K
%F__oZ=>"r78[fSA)T\;G/RQVR[B[Kl?XN:Z?gomZn2+MA1gf`/?gomZn2+MA1gf`/?gomZn2+MA1gf`/?gonEmHY.OjpRdC$_HM0
%C]&d^q1>O?Ju?-]:NP7+e(feYJK!!Z+j7[Nm%J1F\=,"T-B#';[]M-`CPo?a0fFop;4NUiD8ak7g6,Xo#3JBP2nU'0i,06S'4mo/
%iGVhj!\IQNhG,hG?/-;9"cj=-d4B2FeXZ>=<j:?p8dM!`VGOrN<--f$j<d^:pJ^PT/:u^*98Dlf+ZP>/au:l"gZsUb,/\iU$\U(W
%rPbjdCfMN_OQ#^p=Wj/_6`b`[!SO"'r]&U1i$B\:G^5LnYQFZT#@^d;:Q*HNF+4#^qQcq&@<J?"mB*e*5^.%r]tNGJQotT@],tNs
%GT[A"g4:O,rLC.gZms*j=m'KWr"C5lhLG:=[BajLPY&i<Q?sg]apO7gDSc)YcDU]=`]Ln(<c(9PX_jSMe^Zu>>1u/qQ#=$`WZ?"[
%72Puu4'W8r`&EPqS!2d#`+h9h)KT4emU[cn4Yq-MN9r70M.V)nN?8s!,(nOAg#\;?>Q43/?17^ar>3U$<EMAIL>'
%27[;]YBNMpICCon;p4ULPatuH@X:iG,Y[)9W'9_fgjUr_mi&bj&-?c7n*F(%pP,3m?F./S>]H7LG/*1H=2VT\VJjtpI!1>PgoG-Z
%o@Q>8hLueO;maO.1-DkQDsn"h\N+n]KCI`)W5d@>_](8WN&o`],Hf/j&\:@]?LpDKTbs[ORF0o!%]aX#-9tnLrA%Rcf(dAQAQU)C
%qh_omHPYopPd2lOaa%h#mqqmq;WF)^^XW&SC]$_.nU$D;TZ:qKKAML[B8J6IiJUbCA&B.Ip:d0kG;m"CU5^P5Yp,4INrSUbqX0)4
%Nq[+NpI4Z1SOBWR:JRloqipMi9'.Vss(H+1YM\308EFF.pMJLU/pQ3cjM.2])sD&%q$r2Q%1_jMs/q/\b5kE6J.K<1@J9s2b61&g
%>R/:A4a/kZg#.)F_4.oEpe)=IFS--!KnR.%eg?gRmQ2E^mr)?4ccN0s2;X`AIXQ^`qepCTh7<*MGHY8rrr2m:nb5AIVlRo2/(er1
%VB:!.l$N3t/OFjjUCOnEi7)n9>f3QP[/,_`T/#Z9S"RbYEd^E\O'Qp8+P8%UqZ=kL"nn;?6*r:bm7VQu'&c%3qm=AS0+mYWjSOmb
%?_cO,1d'Ua353\-Z;icj$[.)-h4Wr?3;U2(h3-o0pVna=pWHI;QF\Z4P]M]2^K11ob$hjnlgAf+It8NiOTM.W(3ahIQELJko?,\C
%.N]D[E(>!P`AYX<P2h!'%bIEp@"`-XZFM!'*UU)F++,I$!GYlr'V6(bR0E@u@CfZ05Sf`WYe38LrsnQrNf.p[Q+k*Eo`_4+)*+s]
%TNK32=L!4mQR-OlhUg>Zkik]k4eUnsT5kO/Yq?\GU79?hZLsLN7<7`03A/9g\QA3-IC-UHV_\'uf&\Mglh/-hI=BJ)(]NgJ;XRKD
%c:p3XRh%[BXYHK(AA17)pJ?t0>YfT!N-ef;Dq0Zq`q8>?gUZn;'C1d6^Q1+T-,KVnbFj.NLI$b`9$4<s>rJs?`n'?s)uS\V)Z8(9
%3Do:D<lMQ@^ii;KieMkVTi#s;i-X<g:#s]u5IUV$M^p2i)O?GcJ6W'K0hq>I9MMOZ4>YB5Q<B(4'5#2G:Ri#qi0.aUS,)[_Tj$<:
%p_kPfrUeA#T>,gEqV@(]IsCU!:5D(5jl(iaIRAU#r3t_@n%Sb\c_%pup\aFELC_-!e)u4pOal3^+$=X(#@%JJq=DK/hr-4pY.4>Q
%?gqG&)h.ZCT`+[5h>H?rTDumdHju_CbqC,%Ggp\Ql,P=Q=7%l\/ECpEKLe9k:>LW)FSa@J7:#Ddf@]T=E)%lhJ;&VWC&gO%I`_*J
%LdP6jE1'a)%5cuan%J#Y92>HJ2c#!A\so<Se=JP]!IT4]D"E^/9k!<dK@P5P?)i-)<A@%Z+4T-hUGt*>eJkTCOuIMiV+VP![^D.(
%@"J'DYoDqI/1kOfK,u*N&1G#+EfD^E4LnAi:=DABH4Ff@FH;JEbfTRtY0j);Fjh"Pdr^+OPl8%)rto>(D%kOb;U;6)6/N)qH&QsC
%C^3'eDL$/.YL02<c%$?$VWn'sW_;*DgZ'."FsUFj90JcA#9B94L[Qq#s*9"#af>"_DdQl9Ruse59Cl`A9D7U&H@_C1[VpB:*UNh%
%*P%foku0`1Dopg,a*snkXlgUNN5P+HS+69-MTdMFTB#GLd&QP_;r9uMoP:Ji)L^-,Hi6m$Y"Tq"VVC,NMr.PmkfYT<HEPXc4jnG$
%>^]6!\'Aq>p?a8'oOX@Y4kJne[g';&;V3lJ+(s58gSP4(fu0iP)L^(V!59Mt[d!;6\!FKt&,NSg;9q,rVJ0FZ/aa$,S=6kR%/?JK
%Ri(g'UMlS*3N)_^C"gJP"_[UR`shgMM,DSVqRUCu*Z%Yg[S>Ao4kK(O:\<bG+8&m6Djmhi&[8f_\i`G<c*lk&?T'Y(\OPHb3&'Yt
%ic_L%j,dO%cLmh`#BnU<NP9*&++EnP_668J"Mo1/@bscM%%fiX7/l*77fEh?n;#M=NBX@eZE9B<;*4eAY=RrA9#f[M:'n9>IL-f3
%pS,M3rJ!^J9>2)UL,s38,U2!d1,HWF.sT&k^q06=W4_=T54%7!+u]`?)L<\RLc7=.5-Eh6$W;T^#n:g_TsXk_.Gca5"^VHH1b!Yd
%M>hK#eT5Y%Ksmcf*QGRFOHHQ@jrBo15#dh=HAMK6RrN\]3&9GEIJ049!8UM+4fBp`+>&i%h190*8=h>aO*9c_j!UN)ODD/9-&cqR
%@3^Bl$<,8h)2_$#k`1oHn:!<EMAIL>+ct#t$kAbP;8Eh>Oj2^pH\ia,<L_@E3<Jn!DP"]tWgo'X$hsA)W1Y[D,$KT9<$<$uJ
%[;LKo(d[dfpCSb7V9qkOJZ_LFj"HZ>JVl'h.XWH)e!o&u)U!c`R2e+]6+d>'7p-lQA"['O]tdr'`q#d2a-n.?QgAkj7,JUjWL(1q
%C&M'D^TcjaN]-AKcRj<YHe.6-a4t.Af8,'NGUGd?BunplGM,E^HXCmV'\DPkdc2*j'VanW19pToUaJFZGO4o_3b[Eu(.!8+C!\ZA
%$E=?f]af@-`/B0fd:OTg#P9#[_D=Le,q0G<GL@C7X>j7Rcm@c%:NUeg`/W>aH3_eAr)7onI?hc#O.r7481!J=j:ZPE*6qh*eTk%B
%J0'"QeN%lAa="EC;or8T$#*1Wn^g/:&Vh=9Vm(aaA\q\E?&13(66,rScUX-`)@?TnN-\DG^/ORWo%"lK.o6$LC4UpeK+<Z'OTgOJ
%Z*7WnAQ?Su.PWaNW\>5@fNO(ZON<s4e?.j5$d*alq`71lT^Ae"4bt"R3q+!,e'QH*`LGcV,k35Ib`/+p8Xm<s&deJA)%+`sP8[';
%^iP>e@E>AY)n`Zl;pct6,b?OYSn1]q=LG\;Z>;1_A0E9G7dn"`!QGeMP_Y:C)Z7bAg5s8jSX76e<Sg**IiCN#O[*%=d&TTr)4#bk
%7[PP*qs5Wu3',]^qKZ0r\5S)&oO6QS'tRV5&F&UD,,X^K7Ysm!)<_57E[IYWk5l4$B<D_43,N2#,hR(rr7&94k#/=%ZFJCqi^!\T
%EWXE5"tp0#hYI4"1FOJ1^:r>_hK%FTeL?1_F2oJol'<L_M<8akPkb;BZY0IH$bR%a-4iJ]$VU]NL%Zg-Pu9CpO6l2./\4X"#7ddg
%/9sdI+ikfBZYiO:-V&SK%0him@rYt5!BGo.4m8M%B<`8,g7f-E-P[gWeW["M+ZhD>c0lL[?Yjh!ITC>p:fs:j.4u,l]Vla>FA.3e
%\L3fqCE[:9(-E"#WmBbQ`LPH_42C$-'sS^AAP>6qp<c_bi8')T(e#eF_!;5]8]3[>#2ilNl%r.DN<T&i[M1jg9,9REW-u#i8e=^u
%i]8p-pX*dnY/o,ek^uE^-$1;2&c*F;a-0+'h6HI$P8E8H"W+LGZC#j[U*JYr/T%T5-M;Rfe,Dh'1$ph4@$/Jo,0dmp'Q8dN!Pcce
%4d83J#Tdm?N[^V[#MYK7&$=P:^sRb8R-59N``U=l\Lql23bFbHOZtXK`^Rj73`q6n\PuQ?7QO_7R#V@c-ISV5Id'/OdMbFZ9(Z]E
%5`,mi1nCHDh!-k(<+u=5>`#[[Kp]L[o.Y,#!QJW3)]s(0*"tOFqFfe""]RYRP$6?(Y^<5sT^Y/#\'6A"Qm@1.iFP3a04Kj@h3]tF
%a]<ubb"N[#YE2-X7,(c\p.pZAFNO4kcpc#CH>Hn/:9;861$LlQ)OqTf*E(Gn=MK\-#+BlcelY=u0\1k30^il!9\/uED+R`m@6*.a
%?=D!p*)u#ceO3A:H>kS"&lZ\X)7?4*KPuGlfp,['W.)9fhJ>U+Ym=:*l$5Zdj'k[qfN/V.i[:*n_;aZ^6e)9qbi4=l81[5NF#a.m
%KW361;j/+r+sZ'@MkKWsqAt!LnV>"3h<^0>8^AtjJ8c^To>,Y$@%?QgEj>Bhg7h(L0s]T$6"q!@"QTe"#%OcL#XmJi#">Xd%58W%
%,h4M6-N*.B#+I1+PLYacD$.?O%8N?bhMW#sI#fIL[GI_j`HC:!:pZ[_=L/YAdkfb<iPrI)/]V_%@ES<4G'lsg,O%),%\gl=PtKlO
%7I_RPE2Ipbn0R?C9O$VTNq0_C$1_hF\ljQD)9/&(\]<c>E^,\l;13.^".tVC0$]r*!-EJ?^^h+-k;efu!nEHnKo'btML1=@XOTQW
%&:+^cA%:3?jhFK9X^jJ%@0"Dcr^uDl<6d.g;,Vd0$TW@47oT\!\uc\2o<ce)^&_#Ij<qGAk>h3?d8d;L(//1S5\^9[RpY[D->NfT
%0m=L2dY!rY]:=_&Ucd*E6<2VXS8J1C@K.$;'=tpRIDon$j%r!ce/C7*o?r@'j><1`)G's,LG%LF/oE.:F<@c7LtP`Xb^"Z>4>"T!
%FCqbq(]kX5J/EFm]Ia5a[Y\>8FOhqGk)ajdS@oCN]W)SP*jSJ`YS<^DLLUjdl/_,>@]?fB:q?AEQ++1&JThe[!e)ABqiu2-m7_"%
%K8I1h)p<EK>qrN5&I64M6^N7S'q+a4NTV6A#E"TS;s7[>,;*Y'+(QKU"bj^d_'qqbTX[B5JtO[`9Z#jHI5/qr`,o1U]5RRN>8aZg
%EfkZ=!.:X8Ji5,_k\RdQJPs+8Q:2[>fRC5aaah$N1>/UUPf.Z!%<)beOWbq<SLVSEa^>5$L>&ER:fWC.LWL[WeH-[)P82j5L^6=j
%;IVM;\+7'PoVBM6YSh`=OhenuDPQ5-LYFoFQCIC^(,Aid&rCK[=[(DF+cEIrTEi5o23HEq%m'4i/%-X9_WOJ(@3TBUWaqc?(58>`
%=(@NA0OSG9McI-#Cq41*Osk*''N_PXSu/E!F-Rh:Xe]_A*Z#4s.pB2J%6.;ISA6CqWg'i'N5dodkAA5Tc@/a#6#BGt\[CCp!Ynrt
%%*,r"+D\At\Jk[l/4>GN&ZKu;3!5Z*f\iJ7_a4`:,kpSP(Lh3k19p(M,<FN"\SkIn:/l>K&BQ+E*V5t\jGlpMC/DK#,8YZgJCcK;
%/81)X&f_67gfluOnXK&o3=R"q?4lLm)lp$,<>u`5CMYt!.>b1;Ws!?a;g>V.PL"@?I4sSh,B\k9g'Ghb'1q(l,-1lD-1/>jH>uCM
%^dfVrmQqW"Ga-S],-^T*gY+u8FHa^P1\[Bp(]K>P7HX8.6I#^dn[Kln@_+$r\@-I[]H$1OiZ@D8LUSqS(=_"E+U2C1*0FB+LubP$
%H*+eR[CCL`#)=OIYla)<2,oo)f+:bOQVhP2PX47Xk"EOG6/1<9W_T+gPpuqi)VJ;Rhuh"m*rSCHXAPJ7R26Gi9/2aerpsgKbq^8L
%S7<aVMF<EIU403TPPEC'.ho"N=ErcNn1S6/<Z?r.BaNF>kK^0P&!'_%f/gMX`"muccpA(%Em`TWN%3+J=2pGm$j[khlln\Ngg@4s
%on$hO7@np8.%.iuO-!8Wp;N>^gNAl6-L*A,XW4'S<]KhQ*lcJ.RU,]JH7f^u>)QKMEb^plS^3)0j'm1'Oq7<o0+V]MC!^dcECYik
%HBC'>fL[Ef-,3a1_:\AMQ%(m_X.27';\YBh2=h)V$UUcibA.`N+5-_J1`^,0V&.g9fhFr:kaIhAm".Dp#<%M>?FW0IGDTHFoZ#5D
%R5>_W]:cg,PSL3!>rtHuZ<9`mH_r[-d#-?"`I\+q5OtjDSk'oXk\3.V=$`&'ZRb(R$gBbLO"CZ;#AJnB)-dU;9oWt1Le&<%+[4+*
%D=+E_4^ne2GN*?]M/1aMfuAnE05$)(Zs@cs;ZOKY[@I>j'1J9*#8;Mo>7K<!C7B#s22]Bl]@[/C9iD`*:3U%3,f)>MpWPLIL*;1=
%dSVA[2UJ1!dodnabEN;&-mFWDX%922%S1D.r(DkUF?BbJ9XN$TR+[?^h@P[_Q7$?7Ku]jjYg5^HlCuGNa]>5/K4:%'!?B+nMOY^*
%?Q@F*`Yo[62E3DALQn:NqVq*3,F0:kCXeBd\VSC^2s2r"PrasnkF#dg@*]9Sl8js8DnA-"UUd8K?<,Lr7,S:sdd\]N-a*_C2=NSk
%<1^)&<;aVs,LD4@l?T>`2*O9FSVeSH7bm@7U;E\g^bpPI1c9>#JKb(K3H]_uS6-;,l093!cOC50<?JYa"3Y[X7-.o\V+VQY6C-)h
%C++s/9iaW%UiggB_2V:l:Cn1E7*.eU==,O0C$*AoQrFI3k2R#Pn6-!hGJPSU(rPJM+KGARLab#!.^;\B9Nc7DB,,PKl:I,=2]mli
%&i_.&hNrWf-U$12FTQb0':]^69`1*F<`'r.,eJjlZ<skKJYo)n`+E!h?BfA%C4U4R%i8esSu#Y?$aa0[n?NW.%3d3mRg?4K70+p^
%//N>j@NUb59:M/mM^RCAA_>RhFKltI3E:>,6o9\aiT7b5,d@83L7O?LTo5k9<;2*3<a/O!?Ai.H_RD]<]+[T+)KjY"gq,K2+XX0K
%JLmW!X4&Xc-8,r<K5Mn(7uii]c75XE>1G2)lb7]dajurg71M61EHFU)onO[l\@n:ZG?)%r'D3ogT(#=[j2`AoL*!7jE:E]X6%5Z>
%U2"oW"PD^j7JVcuLVo_9R>27^52(B\/r:g0/o,O$%U1tC,>.SaE=@1$_U/WI,=5-sQ&4c7-f(^OmmFqI=@C4k4pkhOEMs<75b]BK
%OS`%to8!QsYhHrX57EaTb,j-7jqr-U:I\BFJ_er"&VrQoM1\0TX/PUO._Z<5fn(6ggl6c=$.":t7Hm028V^Te/):`\0,Q`Pm)CsM
%AC9<q_SaEMI3K.Je"_/\6-n&7m:4aLY$H1jgo?l`D86VMmDQTgbq"k8_`JIVaHY4:VVVGTcU+-s=g=0uq-g[,WgSjHD)KFl?9oGM
%B,mfEP_Y`r-%&YK(2;IWgYA6foXPhN/T@qUSFiiG,dug^3:t5F&=&hlA9]?e?5n&B)WQ8i_l43J&O@PF@FlaSNB'?(hMYg+<m2dG
%_l/,CV`6Xl*DL?^<-]+ra*S[GXVPIpWOB%4?ilXEhpAg8LN6uG(s-V`PUpjV2K;q4p;htOMpXDOl@`1[Ogbq9fCVmW?*2AJokE%\
%)b&+AR_A98@P']%+mEb9/rKh]i>H:GY#njl5U!r`eO'1tWc'b-=i7HU]aQtBGTp(Ll%hD*-bKp-WfZ7+9Nf@_2l?_U(GBO_=dJ#`
%?K6M>//&1`EIP_Naa(tY<BLcPV:0kO`@4/9cA%n,=Qj1T6qs+"1h^_C0CdEJ=9p%4<'#b>A9J[q8F[*<eV_.;TM$"U$.E=8%`b"D
%Ye;!9`lAj:[t)t9K.[cLY2CAGIY7N^qHsB+Z^$Er=R]hII;\&TRABfn\nXFJWT/9#lnME0<t?DFS+$C8ju<=$WJ0i88>enYI(WTq
%J=,R4H'Hc;HG(G^BH]TdO':`N2`.Rh-o@(iJ2eeO9q#Q;84<W>Ch)O25nJ#LeeOU>TI-@eP4]r;;5$mbNo6W!jm>=sZG&=%Iqd/i
%qg`o/5t/WPm*<lV(9jg;?RCbh?*PS!WMrihiYE&ni)VsC2!)j99dKj%frtTt`<%HV`^:ELQ<JT_d23+9[5_"#AV+pd*-m/K+C%Rb
%Bemq46.r)D0GuW=nJn7;-E&=GXqRlpZEMt0%Hl,]@e_]=:_AkW9U$>.*C5,5b<XE44-aTR-"E0mHkZFLYH6(1l/J.R(qQ@WES/ZJ
%:maA`\394f)bK\kA-#%E-bJ-fR!3_EQj:G^\/>)iK"-$il`&]`(7O]$P1?.62_i]S38>i5%ts\_AYb=Gc#n_^+Dr;@1I4Yaj-pT5
%>+,+.N44/7+Y"'6@BC]AC?HK;#2!/@"L$\s1Pq6iK"40Vmc)tFL,Nd;C+tLiHniFP`AZ`H+L:Re+q+-G6YFk-Z<FLfUVXMi%O2_k
%aChC`*l2N+*3*(M!uVPS.NS,+nbGSnJ.,Um*hJ<83K^_JDj-!3#&ZG4"39.eLBVUV7_#*$XM>\/jqu-%C?St4<N8&e+*nP-RK5^c
%)++7;D8`n3$KEOcr<`7<<$'>VBd'J]PF-sp6,G#=KJSZp0&iW8=s_k!=EY]-&6tB)6RuAM+ac`N-D!4b-33pb72IgPL_>M:+?q$T
%Ws=]_i9Jr1@RHg;8N\!\EKDn#o,a)@TF!p6BnkQsQ=OK+=oZ!9q/i+-PMRg!)g=n(IV`j<AYD%?G'hd&EW)qOKf3:EGbW\*HQe:^
%06QBuGd?D-6_6/?^tFktAnZfL9S:h<(Q=PgeWD1=_/m+59=3N,>I'08.FoW"X'(t?Fg;/iIN-m@ZghJ+`Qa4r=SWZtbatDo#bNZ*
%Tf1Gk`8jJ0\OfH.H)Z?oZbb+Sarci8L"ZF7>8/@$`$2'ccPUci6]dt'3"usX8Td>[)44Q[3!@*,[qs,S"3'pd-;,QZR:nrOku4R]
%3a[eD/gA+BpqhX<$`m%T0rXC[(#!QGT]!]!\KT=J_n_j938DWjWXh&.W&#$?=q4G!*i"QW!XLjQWofNXH'go^fG0It[]5KunTpSC
%>c"nDlnJuk<k=c&A9`&Y/J-0bOVE1$-FM:a2Ylo0.M7r"5d2na2UriK]XorAN8.K#NfZiX:iJd0f;A@&"=d+"=O,O"o8/#-TrD[#
%J;5OK@'q=5$%<_Ff><K<U)C9CL_S"I`72/KJs\EZfGqdhld4eA@G$g8.C]f"Men#3m[m)nD\3r#$,TM!!X?t"OJe!jl52q.B(208
%8L4Nf2Oi.,1'1U,9)OmuBCS8f%WrLY,SL]8b1^:C-(tXP)"4LBZ6Ob7,e9eY>h`@o?),qh,]Oin+X](ELdDs&D;G"iW5F=M"t`aj
%81QR,F^d"#m:Oo<ZOEWfDr3BZ"-8b!>JsZV9tCMZ6"P8+-qr<JU'H<Sbn4MI*1l#'G`Frd9(YRma/29>:#ZYoKm?fF3tZf>^'+b4
%[BNc6h6=\>!!ZF2`E@c0Y)nd*rjLALk2/Z9L!#OU79V(TNWE.f)&u94:R&"$<e;Y>Z!o!u1PiGnFWra!U<"0]&;UtI]-7L5V+KrJ
%m&J+4Q'[<<hpI/oZIVis>50dHhYPTf$DXsp7.npeS@h5ueY9b3`,n;VnIq`XY=MYs%n7p1,TNf75p)1A(AkTSYt91;,\RZr191\:
%BSPK;i$4B.s(Dj.p@Ui<gi(SdL$I;\9YYiUn(H1H4(3^leIjU5TP$Rb9%7Mu$"f9J"C=jASYsP>P`h43pILo1HAk;"-o.\;]s)(^
%$>SXakR:e7i##kG#+MJ&*4a4f-YP;TiYa&_fb:3?XdeN\kFbBp%G-6qMERRY\mQcA0GV8:HBJEfQo/X\^.YG[C!S-f()D=j.(r#+
%Llq%<o\(l,'<u7<[qJ1"*CW@'JsDZ,1(K%1qOf%$@?5^#3C(=o%]4KFFMTIZf.1#9CK@*QML!QK-M(_@J3fU%a>doY'"F3.4Bg^>
%-U82K$t_1!em^Rp(%&O\:\:aZBgD0ed0U3)40o1og$(NEe@iih%X,gc5_@)q\0kgJ=1X(ii(=N[77G&,R[0qc9POk"hco\3FF8;M
%rDII9eCXA@D+0jf1b0U6RZm9OAo],B<LI^'_`i)P.hfn!676c\)?sOaqNEd"EN0A>BAH9$3]Y?>cF38c0uXPba9L&2+P3PSZ";Yn
%;i=!"oE;!?gBH5=Bml$`G]t8eJXhQbKW.n(^2]5_``WPSP*59EdhAEnHI<[poL.#^3b_YO]dM?gfkJW.k.F1.O:u!H0GObS&)3[(
%IGL)>fWus6*Xj3rU,1^0h1a,!_Ht^uc8KOI;-L]6SM="<VC.4oYP.C8dFg$r<_EN`ji"=f6m.nc<BDPmP@_#]K)mIF-F\]WahDH\
%=G+AH?9(Du;pR<^"ptid=5_GgNZpO=+M1)Xo5X*.'(KpLU-ra0%<a?\EI09Q/rCFBkX7H.N"mHn>8UY0ga/GYJb*honuCi4>[5+F
%SMB'dSF?kdA_i4=C=Td<;#b-u#^s$S,Wj$[jfT6H]qV9Q6uu%(oLbL&+WDI0O(^[+Q<%`3lW7W'pp0$JB2db[#)=>`7^-GZ"L/!M
%7>909c\=0VJPhP<`]O[@`<9ZfIirjk%`q4!//XqpN*%qQ[2pQJ!^kgAr*X"h&6nOY&Wu.D3GAppLGKT<CEko+NeQ^lF@hgD=BScA
%0[WjPGT,-jPO"u;mul(rb2,V.BQtd\!lgseb`A2s7$(o71T#ZKQPjKkkUQ:CVO@[Zr",0NJ^3C>l#T"Q\^,DEBT0bc3e^6=h88_Z
%3@-(bW"LjO7)@(O%,/1D#tCcr/aFIt0/,*Z)BD+sX\\SZ$/LM!_I?pfPRiC4J8,p?*0\Vi^mlb[U!T,X;J8G1MiRIhfPXslSt_=O
%;/sEMA3XLk.0*Fd;_A\h'OSh\$V"DZQe:#;SK8>t32\S)Ohe//nge9H0L/$H`j+L=ahk-;!AXGN`QAh5FZRO!UG^Tc3]1cAL`3=E
%go0;>5`]76TrYgiV'e`>[>:8?hs$i^6fB/0USe`YDC36Gc=323KB/5eM24$Bak\M!h.-jH;\MUeZTV%A5*#T9C`T(0'+B1Tm<)$4
%7Xe70#>$]a0X@&`C/b/Fdhi3mJ7/Sk44Y)/iKV!'4u;dGf,k[JhG-JE"Ia2097.=Dc'E\ibpJEJ@<p"6BB^,WdcdFRbEQ`aE`bJ.
%@3dN`GWQlVNK=hU6Q6MTl$6gO&76t-(KL/UH>O@5j7=IC6f=/gq)Fg:ND?-+[KT:''$<DR#^MD<AH-our&L4FU/.O3A70!o>RscM
%[cg@s3e<aEYDsFr0V)7(d4'VtX^nVCpo(Gi#jG!:k+\M.?@aP"0ea))'A-V+/Ao_a54&?OLNbtneDlea5iRdMr[6rYY^:c[;%QRP
%Sl"uM=m:@s$D-+.P4,f8hb`l<Y%3Z*J]sI?iNRH"`Y#nSS_;rg`4C5*pnH]G<r(YZ*3Eio#oj4<\?D#g\8Z=*Y,')lQ34+[I/`7h
%d2e!4&9c6IEY;Z4K/6o/Pc'H@'JYo!_:#^N9DR5\`Z]^=&,VU=_K!k$JMW9jXCr9@#SP9F@?:c#UG+@M<,J1WRs^S5k:qRE99s(b
%"FZRGEoq9BBgf870YJ>X8iT<.JiNXH!H;u]!k;-E`o":\`$7,/2kl?A0O=pb*>2g\7LkNqakM%mU-ZnI-@QL/_tJn;:7d)k`27)O
%^7YLed;+sf"_BTP_;Xl5lp0hKCI@(&"qh\4\o3cQVnij^f?D9cCEmdLQ/q7nmRijLAtt<9EE]-j`U"kh?r7WA9_r/G"ER!F06MjE
%ffH1J$#[ENO\s)=Z7=E=-\,[c"<rlr=SrrJmI^oC\l"((S:o327u82cl::>:@GhZgF_Mt7'<="gKEg$Sp<i@QX5:>0)+[jh`iKm:
%R%XOoN&J$;a<4,W+\Ba,3UdGu(<%.):C43qGTb%nS6[J*B]pjj>'^c0Lr.'r*@MFGMbMM\LkM_%DJ;3P_Agj9^u@Z*I6]G6*s)&Z
%P0&8+doBrGRRbQ\fgsUIpCmhtTdcC`-j</7$pjuH;o`k!q'(GYrr)q:i8]o<Ig0g.r6.`e(PG\r@!p-lIE3W1,SQ@qKSoFf4E"((
%OP[Q"]U"5@!RgHU-9QS..?'To6P)tTdOljY!_Aq)c:6D3G*pLu_X^?d-neYS3l!79-Ygp8';TF,>WIfb4!lXsgd8Jk(Q":VWgeC-
%OI</?@DY(!7]K$m(!VYG31J_P86'@KV/06)<L^lTaoIf5U)Jnlcd@Q;1"N6'42[.DVUf:):".Khk";p0,qVo`0uhjuVAZsNZQ<*>
%@pud=.r;=BDPG`CRgL52,tUS_0%j2djm^YC;[SZ./.SnXWR$FO"Hc*_29e"^dDdE_($bR[Z?M2L6CB_IGmb##NL[G=VVOQ]"ddn%
%/`(^[cS7-gIN9,Fi%-/+=Wd,i^l`/NMTGU+W^sVG`LKg_\Oohb52,!QH_85V<"93P$!KK]*Z;qfX6#"C$nYXB'o@ZWEXUGjk24/9
%F2=C@Of6TH;,bb?9B;HK83kIkB50Vl5*4ejEZ?:(pB(!nZHP#aUS'1'*0*Xj,Se'+P\ZCqL+Uf)1<&0DaWXjNW%'oj`3EGSk`8aU
%Ja&(TbtggKB+i'F9hB(JO[A0B)ca6(qP$M%%:;MH>+.nE%;6W2cP:nGM0KB.oA#,`/Ou[Ka00Q+REHRT.ROt.;%CR!<fZe;Rbb7r
%%K7lHK7]CaPpjde;6QAL->W2O-6*7h:l<-jP:0SH%HS!HFeXfYYSr=n8@U;tfS`3R1;\cQe6/Ri*kUe6`>Qi'oKeUuXfe,kgPm,W
%6FbENRB!iY&Wk)4<")*lP!K<6ZQqhSP[F3p&&jn"`KltGs"$XubiWV]pXLn7bj[=ADoFihkh=3KCu:M^462VALOYZ+B_=K#Z/\au
%-h2?RaVeJ-k/r]!hT5oX@*"%t7)1?H`V_Ytd6F"sYB+HP3eAt>T+'^HWa3jIjS^%sFq,5N=jVWaV;T9=0PIk<p%aAXM%<n?>X,W!
%/tO]*)er,f-#EV_Z6oLdO9sDmm4Ba^/57_lS!b]!1SAfV@)LL!e;L"".3FZ1%O=+k`p?_.#UF?#^,2Bi%ABbk[8gfpWq.(:9GL&H
%/jIU?_dSbA9?]%^q52EsW4?/r(a3Sj##N'-l86^m_.S.ZE#dV;&rK/(>cUI_P-c.`qiW&;NE7VM->E`\_CW#HH'']2=2c\(&d[Rb
%$kf`&OBD.7^h6u%%(Q!k$4Hgr!ds3p*.T%_ibZNrc5*4n'`Kr%b>C!^6oVl'&0.8tQPd;<3,Mf82PK=douSBfEZ@/<!h^8^-_!jj
%[Z_[Rr&N&:]'S24AR+kEMkG@$bS]d$%Q\ukP-SaH;.')0.Bd(-i+I%<pT/=-aoU-Oh3RDPol$G/7ERg!oR!m"d5B?Eo3,Pr,a`7q
%lF)+)=ct^kaf[Wt2'iG'mR,hl+t9TTMW;!52C\i1E?\i`HYQGBFB'Rq`#_2WQt.iS5LJk@,UNdI;%^d:#>HsMn+QQf$)O0OACYI0
%YG9B#ak"_T+Nh!'4S#9NHAb;DG&!1m@e-l=>u1cn^K#qVbjHT6C,7LqOlCDR4fYg6Z?9[9BuOW\S-]b!E0BZ>BjLs*`%#88n;I[f
%D/8!e`fhi!)s%^5k,s-?V3A,oNl#%Yl56=5nS5?;E]A!S_PJY<T<>O\3YO(`ES24f[HrV->+)<AcGG>f="](;&lYA]oUnudl"u?R
%1W9tC/NZn.KLV0C)AD"J'(W*qA^d.F8(.[T>;\S-b9j6c8A7Q#2SH)>1u7i7=>`8*b9i[T^V<BJ2SH*iacrMU\'9FVkfY<aT#Ojr
%;q$*dO/>'E=uT.dqU>YSgt(nh0:UhJ5FNpQY];_I.j`SI:rM2ZLSh5"M/1[IRref=X/(oGJK1:g'ZrNhrI^,dD=knRc"Kg[HIY9X
%Wl^$YW0S?A!X1tKT;Kp.fg"]#H5\2laQm[XN?&rdN^uLC`LDp@W3@8ZVQ;9C2.o^ldaoOTLV+du(OjkTNBZY'rOT#Qae(LdMt)*l
%eTEq'Zg$bJDjn\i.<^4!7$"FCD47t(Q[1Pa&,N/;8JAdO)BF;^D4A%)Q[1Pa&,N/;c`rq.VEIG<DOS(1Vg:6q&,N/;`sTqoC#4@b
%K=Ws\NBZX\1d1';?T6Bp\D.6Ne1b;b,^Va2<CsWP('-ufd`)bK-4ibnV^j?ZcCk1qchuNM#V)!h@`dR5IECG;4kJnm*bcNe'g6K)
%:2JD:1d1&VI>e9M_a06[$2^e?roB_rk9[D)DHes+1Jeu"Q2[LoB@,lU,'1FA"Jg>%OA:Dud2MGcjJ[t]A5SF7#hBh&X[+=U?oBXb
%fu2"l\n":Hk#.E2;0%EI5+e\\F-KORO/.mNCn_^]FU_TMW<+U-bJcl,RNnu;C&,FfR-'DQR+K2'PjV[k)mGUeMUJ'8MijQmH`jgJ
%9qP59h+AKAV>5EaJ[u,8]dr>VZ+B&4GL3ah:",A%f%+qZ]hEN`RS9PODh;4Dk*`@6Cn3Uiq$sRV](;lLRjWUKaYiqYaenZ`K8Zi&
%$G-q^<Rn,`58-=.KUB@oXOsJi20^lg#6D<+[[eT<VN[do9d_a[p)]E5=;dht)'`Jl=>SWk.2J/(H<gU+=;h!&QT(5=mA/)fSK'5!
%3Z7J!kNSoX`$.74Mclu)/M&roHrp2#KljVEh@7*^W-JDffsWka#'<r&R,`WtCk<[hkV-h9>.:e/Wj*6R<PWZuUaI/paHc/$7&R7;
%=hP*9BcLaPPt6*,0\8t8cn&$.bd0-:3qKJ;`$R=O']%nN25;N#+O&M1NY(o?q5!48cY6%7Mk]t9[h$I46RfG,@tm*=lKgp$_fM^>
%8*p/;be/(/`C7OfM9<1WFu;:&+-M3!*0=\UA,M8#NG,8j+Dr`f^*k1['!59q6Thm+bK2FN8[4!X2:rg\^0.iNs"NWc_RWhqf*K*;
%Ab]@Gf*sQ*+490kI<$p=.l^`_$Zq4>0,PC1U'_d0k7V(c,Oc5q?R;oii]=_SS-imJCReJLE_C`fraq3QU;KrM?l0'r"d4HF_K/W(
%-37M6mg/-/Q%Jk0E?%!:q8t.8MjabXVYPpN[M5p528eD*ktZ3/RV`\(DrM6C7Kj)'9RO!QHMN\gTkYKA_K"7DW3.To%=.#Qm9j>:
%3aM^F[;RpL+_hQNVZ4l1(H3mC3+.&MbuJP2?t;M&Y>+\^D]$F!Xa\5[cFQ#.rFC>>?.u'4P$Y#I3Dfej#F[mp(?Ys`)[<2lXq0cR
%rd,Sd7Qs\P3]Am*[O(MB8t$`a=F?;52h.Kr.(:$@-0<*'BiVK[;`hj_2N@`[HOA+WX^s)q2[2A;8TZVE!Su,eVAcJTlT&0oL[^o!
%;2I>)NB+m[8C3s0:Q:3KSJQ)XD9t2?4I,@A6CoO'Wl_eRAN*EQ8Ni4jo7SCoRi:\c`U;Irk8BTG"!`/n>ptWP%n<5f`\OY-3O^gX
%-&4*KVrYaeB=P2s_.M?-34C.*Q"n778)gO=4#RBJNB"QBCngeE4\<MH\)XV>J#!HC(`f8^KU3\&;X\4<L&PWJ$=/<OIf_JaN33:b
%"P9A^DGX6R(42_k%h+F4E@Q<0;S>l/Zo:[4M3$_4YpZ`>0":"c9bG=`o&N1rAHP+.RpGPqnM8AK+6k%VVG2%BGcm\XC33IDE0Fi*
%KL\=/1eZou,hEV?l&H3gd>,]Q4Lt$CpA,Wm'1,j$YO'LdHJ^^>k/5-1Q]3f\J;Go(L<>_2co@<+dWR\!(C&3&:ZbDe9[gihJkbi2
%8P<CmJLA&()N&$#-:/]jq1ADkRQ9sC9UIh0eN.%tNt0_PMn\GU)o&g&-;24d0K<QIA'/#^H*]Rm$03@lH<M.p![,M$?ie(d+N13I
%]-([^cbh_gEfIfAQK+UCh.8JRY?f(6Ie^(:<%gfY&1Jh?3//?Bo6?\h8ScD!-Bi,+ott(J=%Fn-:RR_:gV/Yb>@+J_dZ'\a$&p9c
%02'-3m]P.pcuCK5=MEl%(HH(.3,&hS5*+["7I(A.GpUM4>[qKI$KtZLCA4W^(Un(bTZ3s.L60CN5Ab2-6OPh)cCL(cgB`;E=E!Ma
%@b?k-S=2W,n#GeI4!Q[4+TS1B1_:`X,YcuD?9E%!6<:E@",m\OCqt`V.H]r^A0NiBm-l30J;.8`7Y!ri#\L9E8i^97Otp+3@^7QH
%lF4R+@:_;fI*.btVHPlrNa5'"R!?-P6('e.!W<_h/nc1Y[X&\N"@Su!P9=36VaTLeSm'V8"dfsm?rl%fVl1Sm3;M#jF\Op1^7O[6
%m.fmoPqXon?:BOXl!?k>l5UtY`PtVL3Kt[KlWt?K&cnqHe;5tT%5Gr&',m>a5r?ZlEdh-,!uQKBS1W<I&F9Tn^$X*"3/.mP`GVU%
%.lJ@,c_'HpNN4uE\&1Vke-![XMA`'3Va)/%&6@)0&;8Bnh*dJA+j9@9O9]=K>LDLH1r:J"2CQ5%o=m(9.@,)X,&&B*3US28f5;6e
%.&g]Y)5]djlKG&@5&=Y#=s!fA1gSbA2*&iNW0EKt6i24>pXESUSD]V3U2j7C0_CZ]W?2G*85Z.(*npmsSG)jr`G)-YU'SmAA8R^X
%1ZL]B6?j,8jH\.LB8XJ>Rt=[9JP/,'0e:P'ib3QG#=J#ToHc:5/RG2F:Bm$,H4+-tQ!?@aON+Gi,'+*YC(Fs\@IM1g=rM*R(A,-n
%F,SZ6e<A;%aOt^eB3^FbBl[5(TJ,?V7m5JfRhKXLS6e+EfF#9*HI/!Q^uifZD_feRG*Ne#DU^<&c'#kTFJSDd5$dCqp\c1_72^fn
%GPH1TlLOf;j4j%:rSu4r^3fVPm;&C:TE!lhlQuN,^O#kTl)WuSho$U!F>jFm8H%\sH96tqnMcKNDuP@gqVUes^B*`J2sKHP2uie/
%mcJ<[cu4@6h+DXp4jH"Y^X.8b-^QP-*L<C]T<-,UF2"V$Etu=*;8MSG]q<+O'Z`!`RA1`\+=A@0T\s."R!&TL-j'>tl4'*fe0FT!
%J&.]QS#=c.Jd=V<2PB<WILtprnXR1_k_/qI,$?UVfEs*!6nESE"0o+NrhMCSbfIa\'E"5SGVnp-3GZ%lN3m.59;"?VJAHT'dI#0&
%+ZLUc_[/a]GnQ0!,1<<M<WdVO\RmMOB!)7ICSHHG>6g_IL/X#@7qsc&#"W_[FA=CLOhC=+[$YCZ$Hr<I1O3'3B(Z-;S[8QL5'7_R
%Q`P'5s,o[br4cK&NF<7%7frqB.4:MpESD(fmcKXHK9mrWPW@t@J0Q]SSM:*90Z!V\V'un4SdZIt-]:;V8mF_81<buBO(h@e";Y7T
%fj/H(Wh;S!oNtHK]I5%MBeG"l4-KsYrC#ljo7l`O[@Sa,*\G$:aF+C`"Q"HqmNN[id,_.Sg9V4nMG:NkoZcJ9'I!ag=>R4Y-Ii`l
%nRR&58!.VO)Pjq/WfE4CAIt\7.\PVUEt)%aA=.=/+hUchD,hW(ab5Jh-[?$@,%k3NLu%&"&S+lE`b0>,`gl22S$*:j/CBZpEQ;!b
%Lsh&m:ZFkh_,uEH1W8pg"%h-,-B`A$M7@2P-nGLXIB%jk5J0!C;'oHPkq=QQ784tlW]R<@/Y2ITBImR9Acf6+?/sIliLV3QRWZ/P
%:/o2bW@J&=fEtW7YuH"3190QZ9;RlQP%U^QH&`MDmk+6Y)ls88Q-kdIT5<$\KI7/4PkT"BrJ.:c#B;Pne339B7t>5Mi!=s:8oDk+
%ONoM97'rj'NDS+k6fioUB#Xm5MVYrR4\/+,dp%st$f6?J7-atg,paI`9G,ZmXUmW?e-tQ4I3D90(c"=GUNVV,,U0*:aa3bk'W_%<
%0,[.@H-2\pmnE9q&b0HAC5oM8aFel!AOI#$PVgin01qRu?_DVg5m;ASll$+si28sQaN0bd:`nXkn9WncP"f;L9L7IKU=5J!mW[B7
%),L#oh9P!fAtmI@/CFjNf4U\^#(g8;*Kt">&[bN>J_07s$Y+$FZ9rq((f-9i4Pf.eBsnYPSU7)Q9$BfOPFn6nTjNXYII&o)2313W
%-Zu?SR09mpA2!$/D2l(O9S6'U9]Wbu>p`[oZ6<9od4#7IP<k7@U+7tJ*MT0_ULkUTAri5Y=oWkU96A_@bLpZ@d[It@F+.sO(S4sn
%F@;@IOEpJc#_Q?7(pbi0HFQqN',N!)Y55BcKs9gYR$5;f$/H*(Qt0)=#LO0TY46k5F:&m.3hs3A#XX;G66(]9AB#ad@8**GZ96?d
%1X[[m,ggF'kZS_'eVP'eX[OW0MKe)`f2RE_[P+SB>+#Y21_UN-'EN32^ZQLB94oY=pq#`03X^O_R*qK9E<tRrEeUqiP/IAVQCX'i
%8_kN-a>HB"$2gS46MUY-i'Xu]]U>]G3ep9Z!Fn\%Fgb@@7lN<SUK'bTY0XGubE[;u<o.fMRSS""Y/m6^%KG9CF)QA&#%i2g/`<=9
%*F4NFMJ&ed)DbU>ea^fGh('/:XNEr4/@X]=U?+R[](6c[I*tIb,]dIo,]b=]_L8V#F/R-B^l1:ROE(4e)OoQh9lV/(]h:Mm23XRp
%HC.*%["k[t#:h9oZ'b:dfi="Jl*2\W1O`FQ,%lb]"sO9(H9NFlXsL/?mQ2S<QbA/W%k:?UL7a]XN>5%2>a(b)H^"U/W)p@R\Pg$2
%KSXf^0mARE^KgTO9VsZZ<:kojWg+4T'9V_30lBTM4]S"eoi_Idk?Ktb:>Q@1WB?'o:Gi]hPTKNX0?Uqq8Q3^$&h/d,X4;GhTQ`ja
%EZ`Y;bX?hQl>K:GVBJ"Goth6n-q6$lE6jEs;b0=4*_!IE,Z6O(*h'#JLn5:[O<K:2I;1::=B?fF&eVOLCb`1)\=/XU@JiOcbZ]&I
%kYr_2oUfbp#/4pd.9,rH-X*jCeQ>NI_1tCC=_II>&X/"s#i>;ee3#(LiNY3Q+uiYfDilBM9/*aCESf]?YPrd@P>;"*qh7>&9E_fW
%FcACuMGf[Y`G&<,Cj7B%=Vn&RSN<'cdrQe0;7TGkYM7bYVS^8&Ee$D^LrfV)>6F(LH>dMsgO#Wl8<tjh;aX`#'q7.MX9?0cXm!8;
%_j>Z]G*upp;QqAZ@q5>Knf]VR5)lhuZ([;rkWI3lSP789)(=.$M%h#J;pb,86PN6dX:@d[Wg]'h%k5?g#O:d'L@r,/*2L+jH%Lhh
%WtWLOlksjreKeBSUc?mu\0O*fpgOLQ&it]&9irOpT9ABd`bM/3EM=*tP)2UCfT7oQ3lip2FP@1:T@22NiI1?Db+;#D%/V438]mu!
%`"n:(+E/kopHYNtbkok[E(2S<cq7\Co)E@kbBbBpXu,#Fpcn8e5ki'qNdYV)8[R=CJ6lWoHScQJ"H(1(f]Jo:Yq2r*rS4`,-R-*#
%h.j!\R<4I%4S5Vo\?VVblZ]XhZA]Gp]S`mPpBm35\kA1kNK0`+BF'8JH(aB+QEN;[X[PP[jjhLsmMKS/Su:qlk^e@>KJ)g:!W2(L
%8V>E(I\XQ&FlV0/Hr*NeUWCUkE?\"0'e;mV(SM)!qkKg?adME'X3$r=+k@&&3,$9Q4_qU%Z0rE?9qSUZ@i1q*QHG=F9gVdh2S)'q
%8H\(==OB*_EqH<,%qY*C/Hp6d9o<Vd*^(o5H>ZqF)N0`A]@DO-"$V$O%V'gB8B=+o05cpfeZtN)/6=A<QD2O.PZn\"Z3;P[9P^pX
%5u%]rFd"=M3+Jm&rDXBCi[oXSLSb]G-U0M,5K"HnW0'kN@MUKE:sMNM+34;ZfP67O+D0ef36Y12'(oiLZ"#L0bHa`Aq1u(a=^.3c
%Bb\PIcNchD;9&Y?SkU"9UQ/pd-'>T*#iO3=*ulp6[RuLOV/Z';^U*VCUD1O&oeNN(_5,hZ4L6o&&!RBZ6einq$.H4bR_B<[_9G5e
%2)#T^`R@s8*^Lm-d$2VV^I"I/^`pL6kuit7FP'5=g,Kh65&^bsWfo*Ji:2JC/SR3uGs'WWQ/lM`a"r_.FhR@!>[`[bdoYs_2V#c7
%b;LS"ck.I3n4F,BB6;n%;t2d#(l$Rt,Ft'$m-0/&Oeb6r&6#-3!C87doRfh?%;<[:"'1QorB^pAi^H]JTr9t\\nKG,:"r45nMASE
%q41/q][\$@>)BIs$EB+K4_i=e9>hD2MWe%9YkJPp.!D)6^&h;,0$&ptg=]WjZmsq-iZ/_kU^#gmEB82^3bGA.C<Y[U_f:JD8D0Rf
%D*rj;3o3Ye699DS,+_QR>5WWg>4>NK@jL*R#X'$"jI5YnfL6?=71VbhV]IlMePcunjk9!/EsTWJ7_'MI)O24@]lR_=]*<[D+OI<$
%2jR+u3E:IOC6U-MP%nXfn\]O<bGZ=3`q16N!1/WaBe6,7dYOZ=nS2NNOeP`co6)6X#3c`;[d"c"YZWkEH^.SL0:d?$F-Ta*M6poV
%+kT-cOdKaJg/rjMSn:$a4@797(C2B&bKgJ\d]fW'3[gjfQO]ECWmm]`?Qa[^+N-)CarEMkFGdY-@`'[_HKL/d'Z"Z+F,4S7OE-%1
%/:Aa!F^UO("uEAO^q!X2<$:STH(80R/Fp?S@]h[uJ2\--_Ns!D$;S$%K]:^iN#W#?6oO%a[p(]lPWIB4Y;usjrDQp(N_bWXX&1=(
%)PF!"4J.`%jk)kWaAOL'\6r9IahpV;Ln@fAUk0o3n*BRHB#b/LJ=XcFAZSMo,je],n*jm-p+&G+'7X&:TIk/%)_L*N@8\;j;j01.
%PdA8Hc4E^NW#(\2P,cQBLiIOpC/Ab:lEA=MC9hu\n8,TE'dntWORPr7hI3V]*-.3&/25[Zf0rO3K!;TPjM9[Z/_O[GRF5m"7lWm'
%+kDlcrpkM_$]BC"k\]I"^3JJ0:j"iU9^lT_;k\aTA9t]_1%gM^T:-7L+(QZuLS:]TMoufY>_#B[-PJe_@J(fP17Q;[Z?l$dmlm.i
%RUh@lDc&E4%D&"NZY&*cK$HZf)iTV/RK=2Z7CKSVDt\Qf@8u=fD.5hnJ<6_7`'SZeVCu1?4RD.`JM02DB>flgC;[9iKPlo(3Z9@=
%n1ak&d25Q'Fri95ZsbX8,g43o2W[g"LZo4$06ASI%c]7YQNp"O\7bEVpO_!B7EIl\2*koX\44,c`c6>Q7NlrCE;r<oS4qG,2Ga!^
%+0B(;5o:+Krjaqu3l9%\$5:#7Sj\pWbs_(Hcq;HCAtjTnb.pGklAH40>UpR<6=G8181\kB.tEo?0aM&ca!8?r9JWdtT9Brd49`Yn
%pg1%A6R7AH)8la^R!/d4,uF_`YDk14S`naM+aWqEfUAj/Fnf\il&"f>0T$!H6Qq=*:;g!"ZhsOYfM':1(s<K`TD7fe2S+$rPX,rY
%QM1npM*rn8-):;4[0IX/SG.u2$"BpOg`DMb>dPV&+;uNc@#cDA]V\(uBo*D=I*jNYla%3m@&+/F9FQ=(Z`OP!+O4!#R!q")WD7>o
%n=DJpoU2j+;GrKULe7KAPZ7=[53hrSg'BS*oAMM\R__Lh*a4$/PisiM60T&W\k]S>C"Pm/0YK</pRZ'X6e^o"_%9(0&EfNuQl+Gu
%]`oMI#YJ0mpCTf)YscfE,+_mI6.eF9Vpafc?SjZ;3%RHBb7&;Wkh^mY1usa)T;7$DU+6m/MN4&@]GO#X&f[TVSI>O^Ae6u[nYm,Q
%8l8Huk*4Iphq(?KKdm^B-H$Mi=N(S9`'pG#+rcS<d"@Ok(#am>e@73DqssAB)T(,rp8"YcK2pZAcs[KF,@lpIVeg+WL>%fOVEqnm
%(];3t0F_6g)Z7.qi@<3pk4';NiB?D[L'Qgt%XuAfL72EoJr5@&Y/_lQd`:i:>"0EoW.]%sJPa81dhC65V90?'dVrOE[F$es0hE>n
%9N3#cC0i2"Ppm9,*$Zip+Z['^Q*Epln9ed8d)1JjS4qOD7$D4<*8.`=(,F.8o:@)TXq,%T),XIr<;D-M?/H5gM@3]Z3$<'PeK0nF
%IIr%4^B/:$!OPl&NG.>,5n*V,>#(]Cb0'NP>1lK&i,C!lAAJk[b)Na[LTH9d&Y:6OjCMS*\YGN<:ApG4&7CU,*7&<GdL!c4Ws6p?
%M`oAfVkH8P4IJ[t86Om=FN,l%ao7NT5g\^EfHgW'b(9eM!`J'I+P&RVYEE@S0sCm^^/[%DP-b<MD'jjZMj3!Y2=;jk@2@:KNNF!C
%ArUg_,+SuK(]cB!O')5?7XR34L=6,ugO$45+s*2j`nnn*K8P^g]KN6Y<TAUro2%^-]E7]lBZ[6kNZg=IFu4JoEYu4YKW`T9`>@+U
%D[f+)_JJ`^'F\0B@6aPHL4g_R!<EMAIL>;-ZNJ3[BYm,S4&o-4U1l2WMh@cYn%@K73#/.9mBEgV6%`(HACQd%QR00p]kuc(9\m
%e"hkXN/)eof7OP`7*p0;-=WEKP>d=$=@#?]6?$.2?KS\K3;:!=KJ>MN:ElQsVL1Pj/OIf!/Y7g44qNRO!/(_73>8tp/u[u"rP2[V
%"/"$uqns=0(CretP9@G&(V?$'_^5skoAn+;rI/Kt&X#FrGE2t_oGpY98.o3/n5^49>BH67jbObZH.7X]r!+-q)W?tqp9N)s#OC<a
%f>^(SmRpSj2Eu-3cd5IIY,84lkeBG0l^\U+n%_=KgOrFGpR.UIqGUlO#=+`h9KCnAW/B%aU<c\&kF,C<E#R2$[GFj3Qbus&.lT*O
%p*BDY\Q=3t[=HD`6eF@o)cp_nV[eVt<7;DP6teQmZ6CU'TP&pp`TQI$#FH2_2SdQl].jcS_oo.9(sQaU`5-I'3er7n]nl%&/J3\m
%E^,s?;43gd1:V"bY+Z@9_8F]R*%7_]=bQ!%L_84T-cTa;:'Is]>/Q3<F:EO0U?$"r75A_9.&np6eQ_-(JiUZn+l#,`(),*516T>A
%JPM<a)=(57NL?eKH-fQem(j[PDO;'DINiiOG)Fuj[-*.gi>f-;:."SNL[0PJ^@Gr9Q`q@nU"\rc>,kG'"5SVi-HlBk>BXhq),=;"
%eh8lq)]2#Wih=M\5=Fc,<D5%[i!On'qt53d>5)UT+)eL62;6Ho;out2R3[jH20GM&3M3?n'S&pi[NlcF`YA8N!N4nm@60h%fn4o4
%c<9W$p6Q\u9N9t#'9p3"L!B_b+I\N]A9Al<M>P5A&R`C3^n3]hb4[5g&FKA)]N"Mh16dTt.n<!gA/("K,%;8j+Z!AlA>]f@p5nL,
%q1snYg4RJYO2irK68Y:gVJ[7e)O*s)_@.n</dF3bV856fi]M'XE`7S/"1l$LK)2qRDu`mr=n<luVbS*^;lIU/&KAWec#Tkr`&h3C
%^#KlDN%>R'P0jje915./W<*sSMjPQL?5F>ZR&GmH(F<=C)+4sD*'O)"2]bGKNIenqo6;&B8V_UXeXQ+hkeUfR,&qVH]%#0(JNWDa
%B/"%C/WLuB[C105/NargagCoYK2o06aAsCc(l'@k,!$O)K@M;T"Y*IrG%l7,VU_kJV*X>PNF=>W>5jG:Ls">4;q;&f^76l:rL=ci
%*7"-hZPX@W'H`Ib\]9':F?D?J?OeFV#%IPXnC0u>qVuGuD.<iI"<CHu0UL=YnoL6gIsCS0ZcBh']UKWbE=WZh7X0Hn-)g"dbdGt(
%#=ID@L,:jQAPV.qKrA(-LaG#8?.+:[/oR)C3jZD$r;$](82_%lHV:,hg`W4_NT"r">%q<T`Qlss9eK(&/<O_a&.ipCaKoYLORsS<
%VD.\i7Gf9.e1sAeC@W:)QO-:Dpq:ZrORK/dPQ]%RV.hE.>KG&&Yp\6@qRUuC\O<SfL*_G6S42dh:.?5.0D9Qki0$%Ta+4U0;=OnT
%Gqa"NdFF'!cUNOsSSBj)W!d#@)%Yj\?(Nag>C-Ym&>Eq3oYls0=G)TYNWVZR.2RuFH6<f>8JQijV,@*:")4hH=!/d\OH\OpJ>bCp
%Q8s6-m,t33hR:fY$`5/=UKjo+,[:Old2lQ#11G"S57CcjU?i/JlNTu)iPl/FcFG"sFqTY_EEQ0fLs.G>T@JNqoE((-+/k;T=JaZ7
%m$gHTEIi;:UVM!_p68DbcJ+I&Gq346lp@5FTGf4,1^rQ+Z$%[U$utHZK`?2bPNYTg\3p\1m2@,1L:<0%cn^>fOY^8o=)o2j.+kH]
%feh$K#%'*oN]A4&-7CXD9WU,#Q.m"pL&d1*`Vq\;"r9#9;pea2=.&oMS5Q+*V.%k%?oi<.]]/WWWfDlHFB0^B1jVdrpVPdpj%"[)
%(.]Ae4<s<HBmR>/l+s0QmGX)4gM#Q2hL']*aNge6TZKZ,K1cD<=W?Pf+_O2AX$N,0b,)dr1mP"0K[e%#N<+msN`*,m%S6db)41W=
%`?^h#ks5r,bd?9S<],Jp)Je]""7QU/M_DU1U#aT'-X4Y,6nXe9XlmYPdhnsk0ih\1E1V6+,TWqFc,34KSW!f3FTq.m'O>mblu"uH
%\@Z_\$S/%L^.LKt%7)Y*S+CA>A`#cKdr3c<*\E#Y(=]0-D>#t5,[-"<lDA*2#7df8nK??0'pUhj,dd4E?[!TKC/&MSBrIXtb7IN1
%87+GlQ4E%&'^!p/)Qr81I*G"E(:33-V,E-=m^^Fe8bA-YM0F:i`fqsUOSIrS,0YWa2d!-oI83]h#)f5%8`U=Dh-QGG95pgB/J'XW
%/Ti);`f6gK9rRU)>8VAN;SPn/\qIDV\?pq&NP)pbShCWmH)lUSXHl^>i`NiZc47FG+C!uc(XoDriVmn]^8;n^cI]^Ia2@^'dqt8"
%Q`;@sqFU3dHFJQ/@6(;2Tq$fl8Xe_q@@Pm$"p/+fGek6N7u01e^3MnEM(;t/,Et76]<9^Y+sJn*j9CYdK#S^lYVgk?SXDl[2EA#<
%<JKQe!@Mss+Dep4EQgZ-]V`MSO,#qd3b[)1e)3H'q'3odBFo3\Rd;9PV+j?D]Q"u1""N"32s2#s*+j"417.W-Ot\O96G.1\)3Zif
%5gCCE-)Y!bg`@[%M8;s*kKkq#UJFX@`i)]ZTSKK-2:jC?KF0pKNDies/UGh611i-i/FU+&3Iht/,qd5e$ph`:2M=i,,'\np@h').
%LtG`1K>B,"MN!A@/UBN`+9;26&iG';S"EsM@d]rmX.4`QiLJURGR&rD*Y8&^!8s69.frirN9:P-NI&siP&.T_oi[HSSn$.hOp:pU
%Z6p*M8a:f!//:H#+i'pXg2p"Fe44BMH:ZPJeF4`&g,ZsoVTaK]GE=LYC(%6[b&*7h`Fc3DJi!Uo-FHq(L.9"2A9)Y_2+7#REE,m@
%1g:f#+D40JaH/mJE`OFl+U5P.lCB%MB/('_IG/cG&.LVV.dDBr4Gbe+C=MpJEcL=j(i5]o69n]f0i1"??C]-s<jnq>@I6?BAomet
%=HTU5"F[*P0ZasKW+-PFP2a!Oa4jMjQGsgeS^\C"$G*]AWWSH-kX$H#\6k*'Aa$3NnbK+k<6]G/q,OC$/HP&\rbcI1@M\LZ^3tdE
%:mHPU.\cXp$m]=+BqT=fG'40Z`3AT+f;BsJUCMf9M8dGE>;D%o68_0;,5P-OWKUKmg5@&2K-.Cj9(b/WE/NKoQ9),1Wj^%sYn?gm
%%EYG&V'Ms)CK9F'qn;:UN%M!34pQMoPZ13bk>fU=?(2np8'iFn3(_,Pd\-JJnp"%];i"SoMX)h6Z?J=YC#_rS+8[4X'8.d,!%`n)
%9Z/rEnP7<!O]o?i//Y`<5t,$tNVV]289l<qo["4*=PV?"!](au6jCM3W!:;:(bJ0Cej]o8lnK9MT"o"LqiTEm+p;N_.i^Jm6IWa5
%:5<qYoHmub<%&d';cSq`L<Ra[3'1&B5#:=2!VAp)c)frJ(WeTrUm9E\<+n&oQS-KQieq?\a+-$;,W<T8:i).[g_!DSgF6>jYgC()
%)D<&[S/AdDXfe(h`'h1(!0#q9d12&`QB7+a!A>3uI`5brW)pC?/*s;/&.9[)97V?O7Td=9Y;%OK"c"):l0"Lbc9_HH\RL<Sb\QDs
%J5?\mF(AcY@keu6>Xm:W*C,*nNFR^a`]/F5MaBR?b^U5ZbV<nU!,KfEll,Hu[)pl;lN,p@'\t(DZ"PNp8^XZ#=kTtsbc3ZdActBK
%IFdlh2E<1+;*c0*&X*Hi@2MQ`0`o9FBm,:M8@mE4LD>b3eo=V29#bPm;R%'Vko^dqk!P,bhSS0.LuQI4Z(b0,CauatBqc%A]ToL6
%eZ1u(2,8B^<`.F%7c:+tYj=&O<e;HJ94MD#A0f=5IgUZ&]Os@1:*3"&\R*^a0R2'.WoUbGmo.9q3Lo.EoPDm=/.OeLFV/O@c2_.g
%I)LY.^gegZT/*66&ibqB$3o62Ko!2p%gX2%?kN>#dff]4YnPL&Jsgk>W6uGa<)j(U\<]eJgi,Rsi;?\5M(u92(go`?3X+E#B*JNW
%'=qX*1,&nj(`Q&$?Iq'7EtWT9.U3SB?ZH/k$8-[[&!?8(Jm?:Dm?>MXhcZMTj[_`n)&HcCMUQ:0dVP,3)UQYCdDO$U.D<W6N`;c)
%32P2Y$OE4j-5Yd$)eUimPR*ob%*Vr]^e;rc%:N"gQB8)a<CqlM=XdS/@0e3;P.K9sM:KN.6XuDf)BpAm/J!K='AsuCONhqGhA>Q;
%%_.:L?[&6XNH;"H."aM=_"Rmp'LLlE`2t)ea!r>Sk@j9kVX&4GG&NA($E[fTg%C+pYD-6)Y!Rmm_!FjgqhhN:$4Fo=#p_u]o-X]5
%VHk%a7SPbm*6(GqWpd\-Ti$N(/7Ojl1jt-s9m@GHnX/nbqq+H!@*kqH6T,T%8!kPpI^'@oT,U:pG-Q6@g$t_2l"#t`82^W6\;Hkj
%MZY+XNWe/F0V"BO<^g>("hpgr[M,12k)cKS5^ub(:7q&)C?De^3Ju^0+i`04lq]8u.KX3]UH:ns-qVh/GuWd>Uipb=TNa_bVrkB9
%E!3Di@Eosj&\FomUVQ*@>iYLPQ4;>bm1?/Ki==F<]-oCUG(A+"`H5sRq"k*56![*M->cs8P<ZW*7'7$73[DBahGLhN??p@M`7s6F
%<m8]Ccj6*f\#3d!@m]oi&N2d;S\\>RJ@US"]';:EL,8X]"nR8jUS$>h5T*A18A^f07ZQ1(jMTa>8RfOl53?s*OoSCPJdtM8pn$km
%#\Gna_lZF0ArYJs",.SqKb/=@TX"-d5s<]\_R)RNi#G&V80ejN%hTRZ`pR[,Tk_S5A78]_)A(`KkB+?d$0REgm;RXBWuGK!Rb!$6
%TsMRR28mBbs-gr:2LuV%rN'\6g$nVk\6#5h^c"Jf%"fZs&745cnc?j*/N%cdj)S)YLce@a_pSagXqV'87&Q?4`]6,mh&Ik-@*%gM
%HN<pNIXp3I5:*?9Y]*X-=HAfM)rh<YDfrDM"GmGiS&%R5Ta-L/dAn!5b^l?JnT6.t\$imT<I:Jb"G9[sq=RoO72VRts%/:&CZGqI
%,r&fT*&a]=]cYAj'sWfKLh<BBWQIZg!J1;Q9hM-cK7>DqD6OQTb"#ON657nCr]X*s"SWB<23^=mQ^Mm\2((/8(2Z459:2#CFZ.Cj
%=8=(3#bhl]+<TbhI*UThnOo=aOV$p$$+p_8qcR'BR,&M4!R?K&lr2rkUV7ju(POgq_1JQX?idI<k)+OS4kRsmABZD7lh'7P#KbID
%17K';qPn%s?R8&DL1Ms\ej,rK,CgG[5$--aKiKETBr]A2`h4@`49;@Q2i7.("PapCM$=4sd<ic.&2+^%P*GS%\KP)lN6<T!HP9%P
%D4cV2KIt+5@u$S0dcHq<>!eq,D!4]a3:Y*mfOcZ?n6[(A^a'Pa+]oNDN+HZg,F>"JJ$#pkZjQ"#NHZP^?%%,X=6j:\ZP38(6jGDN
%1<u9m0O+[N'3d3NFN>LG3ZE)qCrUI"[p\WU8c/i,[3lpPbt.99W4i%:%;Ep[\%>;U!"h*,L2DWIBc=I58(0M"?R@r/MT+siTs?ut
%W-TV!#lnBVkAdc>P`M!I"Wtno=lOt`,,,(T'%'%tR1>pd$DN7$\pd<[FU>>5_48@Pr4nFc"$53<%A/AZZOo"5`Ut%KLLO:5_t%c&
%hjDUn<+n/+nem4;8C^ZYN(s5?=Z0K(YUWekiR54tBIk&[j15Nko"!_uUN4W%m'F8#W4KHnao_UFCF17gW>#NbLSC,0Jt[Lg,L83&
%8BmF00j_g5ie$Zc:-+aH=6E$c5"8QRQ:@@k`,.kqF9R";V&B<^NI[.%3^;51#"/"l]fo12;nBP",EMn2<Ht*,;C>Wg8T&fG?%^uK
%"kZVKcqW$;La!lTT!mlhTe)f=PR$Cu['E'PcsS@Qs/p.q9mE?mi$Bcl!gK1=(g@)_TN45''1S=pBXX-0;FQAG.3V66CAVU.gU#dO
%*&'b(kg`I!pAoesd:(gf"96G:(`KjJ"tk``6MhtpB!b)_mNLi/Ro8M;@]FNaf8WpeZ9@dsH8g7l0I+R:,+@66kT6daO0#l-@5Ha"
%+Dc)@9$I[JL4S0&?nRR@;;FX\_:E?Ij3"&U>NVb6+Rl-4R7kGj'@c"LG$qob[BPUElNmr.OHPVl/5h4RM,S?r*UX3l6u#dq@u-_F
%;b[]Y5rHX7RnXWj;`.9O6:e)-FG4lJ56TMi<o+YT=-C^S(MHd<<$K6bXX0ta7(l1m.#YHEc6@STP6"D1)*feBBEsZ*RrV9p@&cd`
%Ulm0R6:;800<j?SN1C$`-O:72*eG\=WAph<m)>nP)l3Hg_F/Fk%][I[;JINMLbS"XpMShJ6C*R=,]iel327bE+sILldfm%KLu=S5
%MWX%S$:6Ue?'3c^7r`Q]$q_`:3%+K;&+?'1e'0HG`C%R`8N#1SL`41QR_)_hS]'_"H!27Hc:<]Lb<!KM'5&:HU7:EX'KX+*W^m4l
%3(N-`YpH+-PE;JM7=!'OZ(I^.bLQ2Cr#nW>f#iR-&<94k32.qW3[AM5RWK<*'^Jf(4<H+lO@(Y@?)&b14@Q2D=qtJ>.Hi*5#M5js
%`+Y4*%6%@-9(sA<,,(cj=Z#JL(LaJ??\rC1O@0uAZ^%MBm\@U>%8<ucfbO[5IJ=WNFt:sk%nuCm(FRn^3ro.7H`-8d&Z\K"Of"I/
%)EUU5,EF1j-OVAJ&f=1(*PMq)].l3@JB'r-;+-)8M/gVWg6bqJM?LE5.^Bp6Z4;bf!Eo`\2+&.Sl*^p8^Od*gWQT8<:?<!kYa=*p
%7JJu<KJ/<X($!>j;<*=08:4su[EAZaap.k)@tZe9%il-Vi6E;=eR04Wr_<?!WL13_L5&S1BYfF)1tWgl./c%XI$ea@]&Mp.0a%'d
%G2j1[SM=&r\eLg]1ZqbJ\&BUHj$osW]9fUO&JW'i'P77aTd5rugl\K<^T@)>oF3:KW3a,VPd_#bUY'pLa<majl#C<]Z*oOk<mBQe
%eMp1.;,<CLme$8(as^iCDpcQ6-M6EU%n_LP,dA`J"u)Y>-4Y2?<K*gRQ%4-g)fcI/XF\rdTTOMe6tF\B>*,;H7RsCB[Xpli*d`sp
%%%(Lp1JGV9WR0Z?%1([,MH/0.ilGL<49dYJ0Cq_Sg3G+68Y"V7=:Bo,B&j(V;@%mW<X#Z1nCApo.]Vs.Z,2r"he_<c+BNi:eb;(>
%!k]BcrG#XI05uO",#s;-:^JK.OWD7_0GBcgJ"`u7J4L=!T,nUI'4GsASLAo,<B`*e7l^>YJq]#Jc.$UW#gYjG.akp)Q&Z_WZD^A1
%Xo9TrAoHm`pK'Gt'?D91l8gcUgC+07L"2]f]u$"!dLbF/,iC]/\t)eQ!GaSi%C/QmR`',;0t`JS81MQR^mLujo:fBMZXcoHK!GY,
%'@8^bSi.Z-=5=4*Uq"6"'(@9I#6JZ^CZ2poWU_1,I'6RT6ed4I;3aujQ4<bp_`Gl6M_O<Jb1,Q_-3Wio1#Z=b==3[1@2.0?4hSdZ
%l[(HraUCkFWR>J+87naV#<-K)i`2/+b`u,n9o8pm'chQ)eh^<DV7l2FD60JHajC6(jZ9]QA;atP7i9p&/5q:1::s%aeBDRVOp%nf
%-\1O2i=:#kaEn+YG`1E$)U.1M'L#,5%rb@),!nt)NE=ZL;pYQ>&N.Rqn2fLCNabC<<cASZqF7Uid5>!^I;6UV5rpX)\c=+&&6:fc
%Xfo(/7r8p;TJ%r9%PdC1[p%;g$#)N[@RrAUa"]IlpGAHCO5p>Q"#6I(lHAO7[q0V]S5='hN'?4EWJ`21_&eplpY*[=q$`5icZQHG
%?F+$:_m+8kTMkJsTmC]PK$2^q;%m7`bR^);XG7R.GoA_G((W<:YlcbWM+R4QJAFK$H70HRVKffer5]h/6.VLY$9;e#f^+Si4ok:'
%1i0@AW;*%OC*fp>VjGB*)rUDQZ8fdM.8i./N/=-Ge2ZmD_GRY<NB1H19c<2B]F#Cp]$V_%"gnL08-:OZJSE!_%PY]d!A!_K(4H`6
%Lt./%DlQ0nE_Q7G3THBLNP*B;(1+l0_BCgOA>mrt>/Vk,bULukoC=^QY\$%LcMWu"Clq'6l?G/%P,=i,fMfVS,^X!RMQMTOWEfWf
%0OHd9II-IBF!cPO&K7%VoBXV2PG\+s*gm*p@7usW%H(0a5uHi&]HK8A4OJRHUk#.d3Kr\@6DCGja:JM%Tr@X@#BF4Np.WmTk,9=i
%f;deh6oYSlc+.:9`OsA^7jN`k+V8W#Yi&+s:W#J6gPQ*['hP6!n2<tg&7f"QqX'[a-a\M`_B$b@64kq!3'+H6(4XK:#8`T2*k[12
%WM>ZC`qbDZPKa(\pk:C+7Q]*NLdBfF%'j@e]k6V0b>p;N7f'D3RY`48bg=alLCZp)7'nq`;-\s)&9O\3%Dr&-0p$5hU$0Bi:\:%$
%=J&bX+U-6N8YK1@fFSP6VKV:@)<`IA]qPQ9lWm!0<:5u`n0arS3(p%*)n0QAoEjpo5,K2@IKg'_J7$;iZqZ>"6UGNf1$-Kbi=IRm
%jdR+0g`eV2X(o2)<nFg:OmCN\IJD$$)0G?NMtePA3+GF#4<fO#/'4J!fJh7&XkRa+Pr6GK\X&p5<B\3%bWg.!`;e+mLlf8P_m1p>
%&g<[Uf]_PR3tXSeJSG42eo)Ibb@'#a>s&92%pKZJUsU!P;WjthFA$)>;%>8=9I"/?+f8JR3-QL<)Q]MIQ>e];Wkp%0&f]`#oU&)L
%[l9MO7gHrAWPOri7'qgUB4qfqne\9lR#&k3=mFpk+';qlG"%Za$*;Ko(Q,`rGo80[PsGIf('Eu+_6S=$#3G8g0qqGLI>Dao3H+5V
%jZn&6m0'1^"RUWUSkXf(9.4=/1ERM^,1[LbZ6aR.bscfc"%Cqg'&Oj#A@uHX6ct"'o<ko*@1.qabp)DAVk>8JZ%sYdk_B#[IVbcH
%nu,Hg6oLhTfG:gY6I2Beq[!=-?"C+#P65APm!a49n8-_])lMl:CoM9N&/(XsTrcg_Se(Kn=U-0lT)I)@EhWZPQX_EH_mqe6ClIoo
%91sqBVoccsFPWmKU9I%E*WijILi>]lX3G1fW_FHN6<=0L>4&8!'6.clCcdXKe/jLhg'KJ+$Vg0/pmmT"TOuAhbHp<;Ek3Prq9aF)
%((?-O:fWNAJ5Kh3g,`u5CG:\?OCH!o'K];hQ4KH4Ife#QJ=E?*VQ4^Rj47o!A_]Nk9PKAf,^O@4,I7,BL]\=GWCaQsXo`.;4G</A
%9_coKAZTm_F*P2ECX+uDW?;5h9t\jL-)A_0,Dk&Xj"&'/9R"42Lq'.^<:[BLpX@b!bP>QCce*u,isu55Fka^ARbf)hmV"U5-MfG7
%#&FaKVL;._KEui]X4D*PlV!9.d6WAl>[bWs%M?lAm4H833f\6fR-Z"tBe08fPG#G51ogsq3[,NBEQq$SAY+>XHF2a$2V\dg7j\q^
%CJSpcOucXEfaMTq^cr8i$JVU\T+nqL+ELB>i#nBgS%X,@]1jXO<QYVBO?nUMU(Rl%dYk4iJ71p?Z)`;FL`UL4W1M:ihrIQB!TD$5
%.Q#ahes=I3MBMWH#b&kgR^U$GZR*UKTusK.@H5RJlbMbB=[JtW>'2JZGc!E"Os5#(Zj;Qg`OXpDU8nNr1[UW,keZ#3f1kY6:5i7g
%/iGs_%2Pm-#9dRAG`6;15t2#U3ieBDYR%40eR7jH1ETm'P^t>jQ<JU=i")PLgRDqUF[H]n""Cr_6C7qpR?Ujc*@Rs4a5$r51]Ysq
%AhcfE'"sP-OMa'"HBqL(J<uPJ$VdHI-83cX7=2Jf:!I$%CJA`Ua;?Vm?Zlu&P_??e+3ND)<90WFH\QoDHWopO"ng-ek:FbkZE_s5
%?I/^JlHC!)'I&+[dR0:5_`gN]K\SCY3MX/m[3W(Sl/k31$r#kp9;,uO7$L*6iXJp`/6Ue_OXrb>"CW)IQdF&]E=GTnE#9_i:$,>'
%W)@U'3V_SJoHm\`WLtY%&\CP64`2J::r0D,&1L[ZZ1Me0UI:>]-d4e*9)`oACFW8;&CD3mR,9)nb;p@?Z!&3fV!U2\iMhHe>[%A4
%+_!(>,Q[+KTu.$R"3.e!*jXHsWnI!RAqPXX1ll)^O/r*_*EMrmkUn2/J0%hg'1U[>;n(<[jKL*gb>r-T!d$5?>oIY`?iV/_<"Gc*
%$"c2IdfM`^X(90g'O$X`\oksnVAn>-<5J*ZLr0F&lWGs_U0thMAH7;eaa9=g+Fr_p"VR5@h/_m'+V'$172L3Crkt1m^?T!Obr.FM
%\kU$NoLqj"Xs,25rrQ0hq9E'"[-N<jU-o6fnqtjJQ#<J-/G.TJND0G0PFT$3`1DP:#'1Zq!Y'\:^.^0a</WBsYu>uS'Y_r9=pdKP
%"W1FiS#]&M*<qdthg1O<U_n9]V%D81$;+VG-@M-op!faY2A%hAJDkLLF:H]MaVLp3E=_LWTkA)H"A'eH\RG7jZ#/$!:erm=.n_'i
%(HdN*7Yke#-9+X]^8qQ!!3?Z_"c6Od-.q#bOb-SE#t*d0$LpP]X+"B`!^KB&YNPZ%?l18Z6f"dOrDf^"(mUm'<AU;QM@kd]<)'e,
%L9*?k5^J>V`*pkZO7Ql&ptas^;ZVY3@]3$i+4.2hDi[\Yi8lc/UeIjloK7aUVEEuPC^pef6B+iqglI%?,d;NS4`f(V2Lss.c<B,-
%L*b;j0<W"M5b(Fucr8G'Irp36[@9#(QWTIfOn"URX@*RY^B$2I0gDDAegIoB'\muaPHqbND-ZAlU#Vpp/Ep(R+MJ8NN'RKr^\?t)
%kosIhU7`B.b7'TVVo@lb:`3*S>Th-+"tml7*)!!'Lq>g%laZ@a!>M'X<XL'-i/m&Z&UQSb>gpMiX;E?u;jVT7kn"5b!Zlok#a8\)
%b4=<r;\fb2TaMO9JG,W?ds-*F0)"A#.g;@O<J+.FF-74Mb1/LPo&!@V:>GtV>HQg0`9KeuO4P&nn?"YY@U-n_($2Ob=$rSN<4f"=
%`0=ZB`gL\+PSc)^X$&pc"o+H#SK_2JFl5a]j\uVSLKcJ7SDS#!:'r.u7%IGb<"o7u."U%QO*S5BYakc^-tDpBoHSm>)p<M!6d9>b
%$^9M..Nd').;]@;*<D$XRZWj084#I+k))d4Vui><mZDk#*O9^LB%"k2FPEq5LG&&Z6/N$FbVP_h@!8i>2n[+30TOhSe5J"i-^lb9
%>2-BAA!TM)eosPYN[ZBPiC"AK*14n(5tAs&ZhMDiFD-!PU*N?k*qXJC?o7Bb,_)<L4dsf',?lj8`fju]6/(HVi%^:jIoJCDZUCHg
%"Z0b@QkT?hnlQ1fJ^f7%T',='@IGH=_ldolQjT=N+YpiIQh-H&_/t*2Z9V9V6)`MjDGc\PBY`WF.pCt44agd2A.$iJ*9I05,$KW>
%_W[j,U:ct->Q`Do[,Aij:r/T]KSOh+l!p/pmg@GgR$H#=@8Jg/;jON8s$5=Y'i@JWm!d):113>g5c@dK1h"QXiPm?rk$^VM<Z3g0
%)YQja6cjUGVrtK<QXl#%,qF.*Z$*@-Rc1qf'N<H]BkU5]X6mJ6"3V>@.`=c"d*GK?jBAWm-;lRTf!W"R[dqUPY1MBV=aV^Q5t+?D
%HtGi^Nsp\N,,0s0hRLL@3a`;4aJE%XI[3KCCc,u0.WO,Y'_3DC<oo'\FIKHA1pnJtJ>R+<,@$\6huYA0Ji%?c(Th+\?mJ<!IVfK3
%M*jE?;')_!Lub?a7'esTH\p49FjH[9p</Ei'Qd(/!<Y:$Nr!_Y2'p<-TSlC!*^hlqd:u2`;i^GnGH826HX"'%l3KpMEZ%/feU5ML
%p1A$_(M^RN5HLnrSh)k*7:Zi?#DMn8+cMLd[Q_5<_$H4Wq:SWG$R+aG:KMp,SfO=`Z2dC(e,!e[\;`mU'-=oB,J#(f9HDY?h4dn6
%M,nAT]S\FOItC+=;Oud,rQH1]h^k.Rli0uWTI.IUGZ6SV+Wr7f!IM%f_HF>k:LXc;>Kt'h'bsT_GD[iRb/9G'PY=1GEf=bcNUAla
%<fj=(08(t+LurSdP%.Ug49]"80='.*k0OQcT9=mi;]q/o^uR[5"f53AkKnXcr(%<7!E1u+s".[(1l!ns>$QW7(Tk=d7I`br+tW5/
%7+ARp)JdTRpem^cA/4?[@`H>g_:"&AVW*/$5jR@\;)&mXnT@buW4H8K^aM_8&X&Xnif39Q<ST'aOjY;&BdfH*HkCP0CcV/$6OcUQ
%$\E=#['N7dRIJi<c[j*+4(@DNYpGjagC"SA8B7F&drXcI9Jp61;VAj\Eu9&b*`^&+0p"/BSl'0j'1R;de^.FG$7nQYaWhG28fF/R
%A1:q2.0`mtMr_%FA+Lb$B^#kGJt/o';kNB\C,QtXA/'hG@HsO0UOi,j<(fmB:qS%,dnsqb=\4W1noQ@E$aX:S#Z7:mkl;3O']iPD
%g`o8>i"ZUWq!9-NU".!Cbk+Xn33Z6SegRdolSM);1f2%ZZ"oThe3hRa#fL7*n&8A/?s:pXk+(pFeu-I;U1L0q=H#*VN9l6n)*&s/
%RKrI`kO^)b.Rn1-0l.Mh"A=R3-q.*8rU6KqYNJI<Z(I2g39j*JZ%Z$T3F]_,\:b*d==Bm0-,XrNc&Zm7"kg;^\.Af(6h>7ALn#c4
%Jf[Bm<]rQ59NhuS(p#d[iC_hRB!E8sKXGG"V2(SWGmq:dB;YC$/\;rE#L7'k6pm.#`&QtuS7+&9!-UH!B^r[!;DoB&F:1ADOL@R,
%_N;)R^iWgb*kA*[U.f>@BpGGiBJr[k(Mju;%":e$ZmNn*K+aiq=ika2$1Zf@72dqkj[eB9Si2i7b5h3VjJn7XRR:t*LepuK9BHpm
%ab1f]^!u6/+A't*)DTSf,2!@2YpC][7A<b/b4*kb7e0[,'&RkIoP+PF'sWiUkT.#HTJX8s(#CiMXT5;SG/#qGr,^_8RKS$_U7g'W
%/1NV"YWA_g_3b8FFA?@DPN,Z0+^#BcB2rX/8;Tc5+A'd`+P%5.;p[5mS4$9)*ZCbCBT$XE[@m]J?cf(qp;>n:8oIPl*A4785?Z*O
%!-BVm20%4#(qi<69qr!*lVheA%KBpIJ4;a4\*?=R@R6S>2DgbC4E:ifiCt!9il<':>-p7QjjAW+QdVla2YHVue8S#)h3d5bnWF?3
%44b(%`5afE'!];GWK%gMm4bkT)b7h^OZjLT4;r"GnaU9!=:]]uN6;ab/L#J&Ys5V(9g[bQ[9V3(E&7&,fas%,.7l9h+Y%RI2teA?
%4m,C7]rd%pAa0MrLt7g6Q?5`k)oj3A?BA2\@^0Bm6>6`H3B_Kh.mZHOb8Hu["psO%dD?Db,tRfBWl[l3OJUpCH@q;W/M7u)1q1]_
%P+CsjK-Ei;19VH+-=V$bCMGs$rtFHnlmT6cFrTflLfo8m#2qVtd889<#8BB,j0B_`0b_gh+en*gjU%%u!)Xn4cEhYdJDAA[oV_pj
%3b/jU!R(p?e^NTY@UpoZ"O*kJ94K>?=NmR'AsQ-6*dOfFr$@UL=<ejU4Pji>%bMW<fRiJPK@.'Q@@4-mN'C@:OP2,(dG^bMpsNu$
%b^*V$UhQ>%Co$Uu0H$5e3B(&sW&.RR>'a$7&N7o]N1h-HY`fu46%GQ2a6"2(-(\&^'n8!:;$AfY1PdZcJhm-)]M5h.N5ZsZ()h1u
%+Y^tl+)0Y"&SR^*e0<Pjf"iQsG+i[g1)'?6/`HbW6lZ8hO+Mc'U>po+6=9A(49Fs*k=OAb7^l7$3HeVL!%(We,:u1?XT/[ZKD,R,
%WJZVg5RYf5^(V0die9e0!bDNM;iB0uF9qIcU8icN&t5o^AB!iH!F*,87j+JD$0G*hd(+\W]Pkqd.#oGf@\C1S]=9<C12FJ^/_LCD
%-&[Xsl&/<S8gkL8q@a!6Qr$AR9gHH2g'@q4-t%(``g$Rc==oD-lWDp@!5"oMJ8GE<XiW.8!DU4i$.YYui^Q<bU;aP#%7Z'AViqV.
%cjZtgAm,L@M9X3A[MdFK2O$_&+?^cIpGP'I'ooSFN"q?$C5`"gcLD3uRcahO7+T\Mp=Dd[<\mopr3G"oCA]QS#n,=_@ou2tb%un/
%;P6`UG-h7QrHJH#32]368JlRu`mh$>asM+-CR\79P*Uc?%AiTnnrEKF(pM8"pZ+p5+b:CD^)8V)^P4MbC+nNU"LXS.39nbM*%-7r
%.)]apA3H\#-6QD/Wa]sVDFetLEf%s_ed6W*T[Y5:dCQ(3-*;_6"s]SR?FRJ&%DdjcW:NIB'K<<0RoO!ma`aM#/AoTY.t3B)6<ZoG
%kt[]-209n,Z+Vl!;D"[M%8BVQ58g+&*,C:R.T7chXeap5"d1hq^WR#4&B@`<EMAIL>=1Z;a;!IV=1"$(1r#js7%N7m[j:0eC$
%%"-k]]GKg-g:aK<"[uPW_Ufs85O.ubj,apq[1_Df3Hf8#V6PH4Lm$=%-'i8t]YNOpntj10CB5s2#/_t&0+g!Hb1qp`f*T&iC-2%5
%lN7i.*`Bs]rF#QC@=5F3ZH=8l9u9H"E'(iu\:)6&_4Er%LK7r05j%!;>_OFN?I\MXgEo>EGj:usBR/kHElWg;NF=p<(ToCWERqfZ
%1UQ/h#q'C,<Ped&fhlmQfm+uuCG]6F7BC!T;O]McE&1rI"ukKm4,M8qH@R%=D+l,5KS[Lt'I5gE.&Y-^e5e)Vd<+aWWr6'W1b74L
%B"^j$EG0)rT44WiT.`Dr:F:`@9e_jh;#</JJ<qdRFau2g+jI7*27-/m:Vk25;%E_WJ^WdYmc2<HB>;.V\t"#BYDllX'#:eXfqjI@
%dZ>VEiCV\"3Lq[h8`*/^U,j?Z3P;^UVE2dKMRm!6;P$3CgQ]a?OaDce8VkWd.X5Xh+U:8/T>Lf2YpP9M'P:t?[mPF8bKq@@&Id9N
%4Br4OYg?b]a.Q"K`&bb^olu$HhKO"-!mt,P)V:W,c)qGt<2-ZSc<^J-daUN$ke[_GV.>JCWN!sE-cN&LHp62lkn/E&KNfr.8S;*f
%OC9WG4fUkh>DlbX<e85`9@f+SM%\+5+DWfs/e\_u-4808187Q#4Mg!A[Mi`Q3"&[PRD[BIbEAQj\um)GDe9P6koA7Vi\NRpoFt:"
%2Fd=Ro7/p]'&`%9FNEP%.*@[Um>s9F`HU8N:T!1Q5b].%2-8kHhclN`^Z*M.m,MPgdSKIu$kOclJs#!?/baoR3s.l[7>)qA52rK1
%AGDRf?h7j=_X4kT*VmZZ-F["B,]mrc?q3aW<V*u[#l#]bZZ3l-)Ag4`TPbKVK"e(8F4KP`4^]Jj,!<CjS/>ZA/5c.SfZ:..B&iD+
%4,o+k+uU7HLZ:5LMWn/SPj!CG<0,?WbX<>PV)q^C7Bm5MNj%]1@n1#r3o)3qmL?7X>@JVcPq(dbm0[W++=:7t\Y]!Vi$N#(qm7D$
%(DM4<$@9'GccCW>Lrm/bV7DV/E)V0&mNFsN#F6H#$9<@N'J>HE>f"N!CA/]23/?Oop$6iO'UYO45Y)oo+aq5;j=t?I?>#s<BE3PG
%L7qQ/HG^C9+OTSu#ZL#XVD\E8NfK<4[<,&GW`kBao^V2,Zmi\?N=BoS;:r(1Y=rkl[pf)p<_,h"SdA/+Z+IOdW.=%U+E\WgPX\!^
%is,iV+?:oK=J^kZqAOJ6@<PU<@(5@H6#$IIS7:Vhld]c`ar;?91INoV=Y_lB1q#5D<%>-P//OUuG'/[2TW3GPc5PWc@^?^4b*9Jl
%CF-$"5C.9&DD@Bl^mYq`kT&MH@O*=2gP0V^9gJ5hYcMI?]S5;U,Vfel_2um(nX;.^[RMBKR,gi5O,+"Yne!FDE<IBt&Ct*AM3$cR
%'<F9)Kh%jBXh,1-&je;q%9X`hj?7UF2:9F"RH>RK;;j9K=@YbWN>eJF=G-LG79%^b/F"/Dh:,?.dE&ApZ+_FsHdr"*H:6,d<Zu!=
%^k`7=`VS`C=d710dA!NGIMkC]7rbF_&V4"O1tV#kcHGh]GL*KA$d'A3,7TefR`Rb1OMb12(/lqE.\SrV`ANmZ#JaqF0kgJ:$fnc8
%Mi8SjiT*lu-5.uDY8b8Z[A^&BEAgR!do(&bPL^:S'sM4/(W6rLFm%O\Vdo0-5"l4#HuI)rUqQ+1LR/EuJqhhgn2;cK9^$18b(8/G
%R7%mRP`2#iI%9Duaa"Ee#GK#AkQ0!kA5`P@EsV-"QR>n&We\'GL^=#<WtD-#$$67sLeY]mMYtMo6ABEUV$uU(\/NYu3rDI?A2eS*
%QREl,&1`@0Qi4^"Fb%3NhHor-3Q7/:-1t(SglOX]i<^(/@"-<tN".kuchM$7Ro3?Mi2AKSOBB%:jA3!m88lq5(tV5@+ce2u<%![0
%:O8HaCF%4]<YsaG_<ttF*a@7@P0W]WHm+-=kl<uC*Y]6&6p_'6fWboa6jc^hSjYtN#&lXjhf9:\fWcrNVH':&ZI,G@Z%@Do_qHgO
%`"]clLp_ePf%J5`W*3Vs_c1`SJ4gR+/R[#TQs'$NT\I9(*Up?LGmsstIF/lX%Y@ugQ4cQ,;]$MDV$^$4g#SNN7$Ti3A>b+KQ7^Y8
%M4grg$mH>ERj+F2E<$M\bN*UX^I[iu"]B,2(2jWWG`G0dk4s/>fUK^p&CIIXSgLd9YgTK*=KeDSaR3Z.!46AG`nZ^Xbk)d4AqUAP
%HqD2\+`'%-%mde"#o@,B,MTb3fF5/-RN(W_`#2\fWnhR0'PV;E0>Mb2<mA:P)EHim29S6+dEL.@&iETH#X;(H`L;I"-rEXb[&Gf%
%dB`nl[LL^'&AfA&K.qb]E1s!J[*nM=="FtY4W#/;Ms4+FI5fMA%ne]X@D[]!=JJ_29B^!(7T:OD7H)1c_534:,tGA^-O@nhBgI.p
%`68KcJX0POLt^6s6<85pl/>)snqW'1#3bu'orp.TXnL$-5n?(S-h8Q8%UdDliHt89=bbP%c^KCcB`aU!UOF[Oes`:,1e3K*Y,*n-
%PWH^ZCBJ(94aD))P/H'O)QV,.M+"iNH7h0+dssrr8WK_\K<RU%gNge%FqThZ)>]G!E,qt/p8Vr/#i0>)e7+h<O\6U-O-.p"jN&\3
%?.+Ao)!m3tRLE<)8r4r>!_9F'EAJP!:D:HUHA,s$Uh3ZO4t8\'JaH6**ZUVtjP2H#H<&HP6btBUV5Xu*Ct&bl(]j$?icPPLJclDg
%T(WZJXbh#AWnFe2XSGBt2F!%>YUUsDJ%Pu6Qk<.ia]mGm4t?@&8_`FWTmdVm2(1QU25PIj8rnfHLHUXFQ5"dOnM'*=J]$P1(lTdc
%5'I@^;"',0U;r2C**O4#,I#M=jY3"VW=T6?quHg$^qg^Yh4tTaQd%7_`<Dfd8l9PZf'B1W,3IsTX%eQ\3aJC#V(ohj4mX-C;M2tJ
%]Jrm6,Zs(<0o"d=<eISf=`EJh2?Y]aJ4bf,0L(XdA)IDJ$R2D)gkI[.8GjH_CD*cOEH3%:Ka0^CXdbKN*r$W%*[jA*_m3-&)Q2XJ
%9thbdGbjbK1;W/t5d%Rb,:s_g/3@Oep>(Z,_0XI3Ab8!9eg=%+oftHF@YYN:.f?%_SE-UV;%;-&":htq3p50jYRBN#G"0L9bG`69
%KInB_M1Vi!"kGud3-#8&E$e%6.lG58M;E'@L/10Y/ON*nX.r-]#+pHT<JW@s:\-R4GuBXS9R)R=OQJ>=N`:Q<i@g"pU0;2AgDrHX
%NjZ$'bcK3H2@N*Eo"pE8%JB<n0@+2D=!]`D:!8R0PVAG-;A>H[WXqr23,F6s-`JN9?Q8*+\5n_X3\3GS80BB#Q8/bo:WV@Z:.-e&
%,*3asCBSRnXi;K&k6HkK1T=oV,-.pO7rs1^_0k):IWJd@\)@@i-udTsK$IW03b;WT;e:sGYT=A)4N6?j.I[Z:NLdBm'IU)ioroEV
%jY6&q\+Get@5\OQAonN:;.V08Ml=PEObi\KbI8?^ikn_'L/VH^NLj!g*7=RU1ec8Pcq[s;qsjN%do!3sU.bG5LbfX,+W!Y\W/jFq
%d4#%ThhPF%&2lQNLA870TEFP^bs5C15uc=]Ul5p1VCn/C9rfLb1A+&K70N2?.`=bMFaH4TZcl>qVGWK6AY[Eae&>_E(e@UDXK.P9
%5YsLp_[4/>kN>f^R/l1fIIM=sMLsLhoFN2P8cirlr(KGYAp>a,HY'EENbppF%nObLAi;0]SAJf]$!8VuE%QDp&;62Lnhm[L:c2qO
%2?SX;i6I$3?T3p.g-Yue-&kWW?BmW=1W=+I#O=)4<jEE"c?ckb!Z"]KDISM5@&bW`$_n6=0Zt60ch).f;pCI6W*G8L80Tu(MQ^q`
%A$$6k*)a0=%TIFBnck-LNaUrU@g0s>"?14sV%8;gdPKq:3T+^8"?32TR7a0+KfQE0&'WD!,oR*Ic-#)MAb;$E[_6@j?tbH$0O9>!
%.d3Qc6jlt:0kNi/8r>^G+/`j8NDft&SsBCoL`;:'fF@E+H5L3/T,?>I2!cd]A;1T?ILJ;,'Kl]j+[_,k)6e"`+"j:p37lfgO(R/(
%hR1WqQjPrYi'X^UY=V^:P@qlnNZY*aFuTn6ZfBY3U.M(^NfX<0=9rN$I,)tKQi\Ne5+^"7`]RG'kV?&["6r%Si-"li5N<D^@7C3Q
%YNZ9UnN?b95P$o^6'UGP/YC$D+]2PHRLSYX)`C?nXO&?kR84B]#90E'3'kHbcbee"IHc7^lYmUE_]^2R%T,M&3ZaWi9m'UkVL/i!
%%61a(P%uDUd]A8MjJAM??Jt[c=m>RHrl9,Ejmtlk<PLGeW[2$qD4cn%1kojH<[((]&"K@#8*&nUP!N>TV:[080NNq0!XK0DTTj5e
%2eY`1O47bE4%&u8#2>n%&a%h5P+VnX$h[\s7@3)nms$O>25<N:p;Z<dAO-<GrimPSphlT>^m"Rg334OQ_1qCr*n:8UBO=>!\-?4n
%QF#`JNLE/h&-/!7E?QLD]CZMiD)[HZ%N*O[Ca5q>A4R/q;kcRU!oEuc6:TKBp-_nn`k7.1&4d^JgNnpo[6bZ52@G!E\n+LZ#*okr
%3e<jKN#b#75h@:QULI#^MJJKs*\?e#';?Z,<,n@3#M62c5sPj7[:]=?27Z4*4&!"RN+o<E=eLOQZ"@b:`#cg=IhbJHR$\NKBJKX.
%OI-?W2.pS.8ZQS0MckHjM(ub8SR7!ZV"oQ-5->Qu=R&i\m=H7F@E,CC[S=qhRkq[4XsV*J_m@guH\lDpb13M3iYi]VS]m7O&&Y'%
%HoF@^EBgAMrkb<Kk,V:N"WmPV<Nd;p5):%`5t,)C^u^;HnGA`_><BOS4?/Qe)=dS/"#uqHVKmjjZY@k]L/CIZS7qC:,@J[`5V/gb
%A,e+jM],Y),u`3NG8rTS_;IsE#;@*K2<cip6-ThSdE5RZEQfT822s^mVfBm0[D`HM@FR"6U_4CK<hVRK/CY>C(rr*758s+!7O2%M
%`Ca1Ql7*C5MO%--&ccnIF>'Q@enCah3b`+`\#jWR#URIT!$`4CNDd[u*eYM%LHZSC8^NSd!XCtYi@)f6]HT[5%&4`f\@J.&BiRk@
%U2a"qk4'GRja5@`Xe/Ws,e>e/jU7^_&;n9e1<>4EUH/iAc3=>\9M_uFP<hT$3P.4(<$D+X9;q*_=hk@e6F\#J&jnD021<9UXXR^!
%NRuNZb>r75L-7PMZ#kD_3,9q^:gilpRHkRSSY9T6;IP,s'f&ia*j?p8!B4k@[q@Z;UmRpk,M0[te*IO>V?+60N/6E4,G$^,J3P-o
%-tiob0M7%u`XoVGW47Q,BW+C]*K8GI.b&jieM<UYHLcD1dnn&:bDiYhoZ?3?Y@qj2F]gTI0T&mql?Rd,)kMYJLn/>.1Q:"UR-GOa
%=q?[[&7e_=.2AW!/,Pqi,b>Z,T(G613J&uqL/`Gu`>)cmiClaa.;Lc2,qA0c;k,uM:\8/o:C00M$Ad$3USJ$$btXhhPamW?N4i0m
%7mW\m60sH+cZDBHSeY&I>%PMsLCf#qO<Gfo6V$GLVGSY[3>oKQM60MQ[UV,LMEb2D;5GuO;qbqD-qk\6YM+%VGeYV\CpOFNo1Z\U
%=p2kQ'T.N)!7*sP;b:p.4qQ,86?fi+B2"Y6g(Y,J6Gs:t?Ze0!7B7"Z!2"[1<WP(8j8Ka7e?4Vim,Lj6d:Z#g,,-8`EeRBnN<mYk
%lm=1%/jrEY-V6Sg$kZ?X8""$^ecFD(Bcr.`KY4Xe+dkYrcQo]G-/T:t85qrEd2X7&-&=qmP9ua0Vc'%DIRGhir`=X,Lk_p#<pQ93
%Tn(n%i+O.0o8Fu6CTLcLaOX%d\-m+#[iW3(b\GpB@PFL\TNBX8#nraL\(.d/@D]ho\d)$6[Q(4h'+BtV-K3)ub`:6$MCu_-B-AH2
%.g:"hKOi+[#Y^rbT[6((#Xk2*Pm:1us1]*MX'gM5/c\L+b=\;YKi=fun.lLSmCJ[B5063X/]2tpWbpMoq*9o[#,dSZMk2i?2%%/o
%`+1@5G]"<$_#40t9%BIN>p9VR5R7i3]01sQF01:t'goi;F2WiGAX4&c;i/5QC&:37J3!<gHAKZE'h"IPTu@<4TG.CR:IKU5`\iQl
%@Ro8^.1s8W)2uY0=E[nh?^'$f(*ZfpPH-egVIHoYDTNH.@[o!VO<L@T?r[r'QG`?L3duqh73)M5fX9WC'=/G9o*DUsTd_`a;Q_N<
%FZ%Xle>4.uW>Uh8/_U6e+Mc:+8Ld,-["^CK;PH+)QDU`'U5KgDW<B-rA#:.'Pa]l:3SbBlMDPJt\uFoOK[(8P19S/m?HAmSh\$9#
%#<@urWbYlCYb$XX]ga5Aj1]V-i._h,gQ:Y&7Q_t^Q<B$sm%.C%O%t&+\tEZ1ip/@Cam,Q24[g(<o25<Z"XY?n8lW6"Ve=$ioL-*u
%WS!8.&.UCo$1blHe/D=N9H4,aX%Dp)Idiq>jeQl$Lj^!PSurD8pIJIAOmhg5H#7pCqlb`3Ich&cbSlX7`+gZ5i*o;gGa/gT_nCg#
%CX5/pDnnMt_]&_ZjncYq7281<nJLnhLq&S`5Xul>4BBCYC`uKj_1&f)Nmg@md>ueE@B>`TV5hJ.+>I:k&>pRGK/j2Kb,Nc7-mJn:
%k_F]Dq4q]F8h@4ERGmUk+V-Rf!4//$S^b\j+OLFPU^P#aA?]I4B8(m19L^-@;g="#aOMte&/t?>G[r2NR$F_7e!VDPCsZHoCk+PW
%\O1r+PjP),e'BR/&%ke81,AYC3$LWgo2NXrLlXlf"\*"Acbh"j"/.EA:]_$C"UG05b2S6YbZD(g"?a$D0fJ\1jI''_]g./-=5rk?
%IVekcb.11a)M0b?oEJa8BfOp8>kA,0F\T`IM:bMuDe6R/_]G+O2<8YUZQ;/RGVFmJg,hGbBgtsV)+G$`)YLe&^dt3O3T@l/4Yt5V
%9H$2jW4pa#3&LD2Yh=tm\CW-f`TU;P[mrFGM't6PLmC-4N9Y>_o%8AMfar<2<bWP-StcV70jER4SC-"6ffCTn-*,Wbo[HlIj1B-g
%gn7th*]"BOCe%4.@9S9R"q)L"4);[DDJ#G2)JkeZ0HcKWYlj!FB!(5ZEP3g2fe#(Q)T,OiPoI48E`RM)!@S4lC_ihIORq0l'75.r
%Fh65DnKY6D7-kJ)Y/T5NFCZ]%NlG;7*K#SNQ)#jJVq/Ri0t[W>n\PYt'<RA5[`0LP\1E]kO='hVlIHl"=H4rN"Na,r9*4jhV(WAt
%FnLn$Ak"ReUgt(N:YmiIYD<%MN^u3JRUMGDjD$i')<^2sDi/k?`^P`U;3@F0*);K";/RE^#n[=fQojgD5C(WIph2S9+\hq^WQ'r#
%14\\e2CM<;,L`34BTO,e0V'6`-k$=f%n.'^!nopL!s7n_(ngD)(6NUd3#Lp!oNo>"P_Tif7/^fCC._sh:DT=,ht.]%UXdW-8qJH,
%^mJ"c;f)!n&r6V;1no`dDZ-#]4:,`:,22%@eRl.:1po&R0?O05_'j*t)KaW.dKGIo]V5_@i=tGEA3]Z5*cttc^pVtQHk2=;PdJXT
%dfNi)X\MF*BJT%P2a'0E*D:o?057P`CXA2U1+[2d4II(iWM7_)*F28AQTf\lj*CWm$,jufM+g<m)"4PJg!`(j*Qm'R\i&8F3`2"0
%`udF3`q!N^D(Ji?`sC0<-8m:N:c.Ep$.&@A;`m".V`+>[F>"mm90o:fk_`\>/*%.2P)>bi"*aH.Y6LDaVTPm!],r-_<Xgh/6<nV2
%9MCb(?;-#H(i1q<aiDF[FOR9f+c/1R4Lq.iB`WoYHu]K[^nRrmRPn0_JZ_:/jb!Un/q(!?RDiX6Me%+[)#c:#K+Ye4>MgCd#0CU5
%Q/9cQT_2=UKMBF-`Q]m2%EL\GDK8U8'[[U"\_-7&>5^HSIR4-I=tse220:6p`;IV9(f!b<p-dCApP^R4n(T&:ddgecZnMeSHa7:7
%ANrGO@kd-9C`*ao/W*+YVhj;lBbI%BL$MnIQpd40*Skj$3n83@+!62V8t:/fRqP!/L5SIr2M9$pF,uXR;)f]">#QWW[DijFENOV*
%Y\91:gr@&m)Sm7q[QZ,:5>=1j>#%RO4qS`+bee<2Z^=7GjA#D@37h-fXS$7&6'!G9WM.!f#XsrQ`3Ndhc&k_QehI_Nlh-#sL[#0e
%g!<@A1l%2n[*e-XqK0OJ3eU`n\H/c<8E!61\eC05T:CYa$YlF<B9.leT@5aYd1F;2&^A3,"`E\\k[rAqBInUie3G)"@SJL@J:E9r
%;kK_<*[$6m,82H.)"arNA$Foh%(qI-8^plH[>gBKRYOV$bn8$Pd#:`.3S:YeOe,?j4MFs[2bDErMggM$)@3=/KJ0/nE&L4PFsC%`
%k$Y_A2-(8`6,Zfmg9Mc\i-ZU$B&>MVXJ#h!=AA^8fP,q\P:N.uJG@R?<@U?dA.F#N"W@4r8:%2]_/b0-+k9G'AB.55H?)$3g&1_C
%J'sDWl(=$NqHr^iqe)Q$\paP#S(D$&]mKYDn'CFj0CE_BqSgFG/oNZ4s7Gb=2eU_YYE,&u[*XksqraT_UX(A4k,[s#qNH-qLHfhW
%T<E^:s7JUC^]&;JI=IKAIWP.2ri\D0^&6lj5:dIb/i9hJ/:V@6&Zti&#Sj=51?bVToS)m;aTF`_kiXu&7R&ggXPYW0[Cg+F/PiD,
%HG[kk5tOe)cj\%b*Ypq5+]XdMoE\4B;jn3p6"Zjpc7"C'VbmhYcP1$!E*=iS/^m6f1;3tk\CNuD1s8$eLno))B[7l-;3m<AM,e+X
%FDu!<Pc@r^2!1U?L"Mn4#o7!7"O#iZ*MnUh.c4\?5PNO(3(s`QB?);1QX`DX=UU@h9&>ngW.bbMa>qN.dKASQH)7E.b[r#A0sNMh
%P5fAB;Hs9[ND-]a->q$6782l*ak?i>>.VRu0Zd6&-RLI/X2j\nXX*0eOr0>lQ;^0f+otZlS&EcjQ1puN'2a7od-2-Q]$qX-,,]0,
%`%6M(&p&jqbiCuK7h6!JVWguf&W(ee3&W'TKkMec/<@O\-0s61i)gJdb9P0J:<F1_\Zh,gD>BB"C-IjT:dl^CHASVp#Ke3$,*uW_
%Q4?(I=g/t"ZbpmW)Eu3H4UVi%?qah4]e75gBnY'TZI_]mj)]$j>DQ$p>@c'mPJ'f'e4u5%1'T*cog2n)jK?G"ld2FG\e*!?D`s?'
%kHm3sa8+pE$AYMBC!teCSjtph\]\Z_#KGLm7HrJ_*8\P2cBK$0d).&QU*XPX1S(:L(-:QrAa.$hH%LlN3L#A_WZX(%P0g@A#g8Ve
%8mer4?3`*b:s_S0Ei[8]cSmg63g986jPoYi`15q1%'J5nK1+1#@To!E4_>BkVEImM&!I+>$5Ibh$4j4KM2%3K/1]00#!([1g4O^b
%QnFbQ"Ks,C8LF-HV;!-O1JHl.d1`TKTf?__#P8Q1dRcY>"1feLKEoMSKD%R=@@=F`08!C!G-aP%f=VI>8-5Q&A)L6;9F$?J!\uoP
%j,b1&dtakip:].9@ZpkC:/_%Xa>PqZSCYCIi(1Uk`l2,@Nkn@W,c46&"VYq]Z.7D[0P,XE\9=HGC1Ak6(fH'EpjF)d=p+5]Qmi*Q
%p<q>=aRnSqASc`-7aNI%:6(.E.7KlZE?aM@-s\;X$aKB%4c%b3-dOYOPZSZ;JKA*S'l?9u3oLY;V;MuC6n4@_.<>au4%XOT3@p+O
%Bk&+o+snPgdU[[jAIa93q5ZE>:re8\n:E38'K+7\Ye&l\?CDSSeK1I?;V<4RVe%&>g'COt&FqljJ4GP<A_I6s0:L=)j&_k08]FRq
%o+kf!LRa/a`mI/6eI+MTf<oVf6$qMu8KrJ$s!X#Q+J*'jfW<5N2q)P/gIBgu2<ciV7V#9qEu4NNlA=q<4QG$_$A3<,$m$nr.EaLa
%]L7S>X4"fJamU4FB.'pG3a`l<&;u^;.H%=B_+kZ[3-p!g\=Sq$0Y;H&A)bC6+r>usf@eiZjDZHYk-8MT#0S2DR)iVScKDS?X$\2r
%8`+q'Pn?L(7jk=(+eY!=6n%kh>B6mHc%:/*ku+IDM)BEe6s1f@N^f+QUj*[":)5nPbDAnZWjNn^;KWaQ(5Isl,6)<e%&e6!o>K3-
%iHsq^AR[7!i2*Y^Jb`o7!jZ$m$/&6[HGKbeADasIQr0YIQ>[LpFjqIqgde4Xapo(/etL19bF15A4eHG;54!0n9gd6QZ`PT>*"rFj
%#D9c:&;bP%K>0,>Kj5cSI%H/-fY-k6:s1qnXID5<otXJ!ge98GOU-8Y'KC;H+VKj;+JrU2BXX/PFV85ZNRh.?5M:<<(_JXTh`&B5
%&Y@tR9N)3of*P+3'.2:A)3bX`!lF/p-+Q?BnKsXU(PV,TYm[Le&d(m>oO<cDD%!X[m"<LXAeu1P@MV+2j^s]$,J>$IH&u:-8eCZW
%GVAZu.Zq6?$e!I\GB)YLMTS^SOcaCk<;ddA8J'9DpEos79l>lQ;rT*L5c.dICB^\nZUdTUH-[meNgs'($NecIr"eHDQ'$kn0uUm2
%rDn&KCjQdTWS=IJp2?@)7@@<@ZL"i'Lr%*H<W#reXqZbWTUc6AW\HlCWaY+sLSTg*Ui.dd,BjIN":f+Q=Y*XjbCHG7`%n#/iJl#f
%=gKQ&p8eX`c7*P/4`ne\i,<Sb1&7=UQHEldnrV:WaWY(8/E='.U\FS*:>[Q/9q_1>T.R.&:K2Ao<o^sI=fc+@Y/3Y]BT#>\Qbo)C
%HoOFK/$]Ec)/YY]&^gdObRdcJB[Oi89T3&9C\R*MfYfP<!P3S("&oQC_QUi-$-tks?S)mOG:[Khl2*"VkSVCQ0,=Z7HT;jmXubAu
%%eMq#4+W:RS2>iRU_+>jo@WOYq@s<(JM.D#UkD*te=uf)*YKIqaW,\'\BLI*Dj;A?;0[m#as!I$$3e"8n'al]a%K2B`(j3!CY"%g
%*)MI:7N6!n+W'Yfad%mlRuFfE]PKaLU.->`N$4$:7s\iO&m^j=P)N9kEc`j7&QjAqBpM*?oaY$%B5-fe(J8>O<%F6C5=R<Emd89Z
%9FMeJ$[+J@=:!$'%pg>8^2b)-mC`!A1).i,q5KIJ92S'R0.YV,c&sh+:F=UNl(Vn!!3)nXWEh'5d(KSPaLf53I</-kAZ/mmeC<WZ
%i(gB`:SU,.$U)ULX3sF_og5`fP/K('b_V'\J2rp8f+IDake9g=08.`7kE6`6.PTaaFpep;_SAIo%[7ae0e>i'$(WqH+o9?e1NB7i
%"3XS1;2E?65\$RcQC!:Mrcej1C!*>TOCslu@55@7.8A2+>mJN`9QDSPAF:(m"=LpcjK)rLf$Z,Rd$5lSPbg_b"*pLg+36hc5\_51
%MDUV[QDL=MXo*Rs/6]K2]F2S,Tlo95eI3=NF,ks^V2CUl5`nokS_#HLeWKn[X(^R!/=1KV#36T/OuYgf>BWSFeLU#5V1U.d#dR%3
%<@Q*>AW*i2?'p0J-8q9[NZlMkYY6j?>p4!PnNnc6r5e%HP<FaS_!K9F(*<]fPo!1:<=T#lG-!,7ZO&XU9kY\Jd%,qUZd@C:^^%/E
%@H8+d7Gpfo<`O/g@#X^q53JGZUS"HA,0RZ&kXD0=FN$huAtn58YmA-F'D^Rk5P,(A0R)^+3Akm*3uLDMJP!^BpTU/`?5G9Gg_7>@
%MjPK<SlM>Y0iVHqha\26Y`+-19Z&ksg.X8[bX/_CUiNLIM>8*q@(pW,X/!n!/A=t@.n(p"'^A#Do"=6=.SK?'Uq?oG..jH[!_3=>
%TlJTD9k6(7U=hj^kX=9H*)e#X`fCGVO]MnEI[JQ0RSFL9@I_$SC1uPFb@+nT=2Bl#jsWVmXg>p\kCr.uX-A<shK^?92`hXD*cs$%
%aR43<61n<V_ko]X6d:EmXuZkMA5-Ucc/[@r8/r"?l!=&>ae`JkF<#2_[3ZV.1GPDN+*cC>@?aCs$_Mo8XpedZN(&-l9XMGRVCu#Y
%.pYS.G4\c$JSe@Qe6C04mt]7h`7->$W9K7M8u"b_2+">)#@<56/gb/U[%d2H-UsBtR=38*f7Z):/iFF&kG1;/9#TRr5%YF+,[-W%
%b$bs>UjhjO71d#KN3T0XM/@t)HdsK2@jl0!l!A*kb%Pj)>7prXGB;8%`q/92:)kF_3,E`q`gB1oOEK^J0b'C]=nX0N4bua(QT+Ge
%eq83YA0m^9[6;UadN6J;_GUBY,,t:"l-8HZ1kd9o`P@1%8f;+ENeJq!qT\jsdS*c^VKsO_<C8N`b."\CMOhj77VP19<XTF$R:=:3
%iY!La*p`^GDOhgQFq:cebq?'PUlP]B]s,J;lD#YN^c^9PGnpOQQ-JCL\\=+_G"#XfSca/*&t<'])bfH`S;aFQ;FT\g:;h,")oNQ>
%:cb0_3/1gO,%K,lq(.H(6l8@-71ZJcFWpT69JXu]=["$c,KMEFp6!81_Jk'cLu1KF6Lt[658&?D*+kfCc(gs@[91".DJ2J#D,j[R
%;2FK=\72>Z,^ag02_i8kMpY"QJ6cZNKZHb7mj9!0:=+VSom/B"N?h1X4=S+TR)G*=ZfLl78qu'lUc"M?g#K(qE"P4cTGCXYU09S6
%*\=<N>i`^N=uAW/40e";KN685W$?\JZh7/UfJ4HUN@Qbc-1VEo`$Vp).`RU!)Iq^,CmOJ(fZ?`Y<[]<3(In+NhN_b>M+mYQ#:I*(
%#sF)MY)uI4IS&FjGnXZ#2fA)reA>g@jOG27@Z,0Ij\/H*D3"^SAogBc!(E;]/(fbkC/r7r+QcAiU>XJA)3.\E-cD0C<<Q^Jj!f<u
%@T6U"L+03IfYpfCF"?eil"q0cT+L#?J*_,i2R3et>RKAaQke'R;jrPJ9h$`^S8k(!dT$O3VPB?E:ILbNO,+ESeA`KV/Arf?$6hP&
%8!=0)G?#NE$4lK$Pc5thHQp)d\(n2THFD$HlXNpdL^^-%6n*o-Sg=S:@3UVdk&FRX=_3a)^Ec4MFj<CYQ:TTW*8#(s>un+A>H.Y=
%Y!5BAAYlgcGO\N[FUQT$BE5Rh$f?3+2gBcDZ>seRF_Gt%2_[Xo9nHX3Tn0@?h/T5-#,Z$dU_`J*d`dS\%o`EZ@=kTAQctDA,!oCH
%XX;p`K']FF$tte,_S<:=j',VmUu-NB(DRQ97@\$4m`R%U"[ns"E`6>qb0J\>nHcR3\4(R#c,:eq7."F0P8:]u],gDp:B\k2,@p&l
%E261WYCCnGoD?/CS,:Bu591grS\p]SJ,/>)Iet/cRs4Fa?iJ*\rT`rgl0>02q%rr*k[0\Vp!mtUIs_(hK)bhhjn\l:mEO>?%j1SE
%c]8"hYLci7b<I%$"2)8Dk2$!TgODE`q9nd(]=YD)D9fo0]R#!MEPjBFdD,VUk'm4QrR^uYhd,_9rf_QoBE*AO04-?&hY_)od9k2%
%hJ'%Z043Udqb/f'cJhmJ!4:S.GBu%%?<mKog\V_l6,RP6B?aud\mc_1h<cZhY[Rr(Ci9ZQs8)'-YJ0o-ltcX[WEd/D0c+T3Xi]@I
%o:Hi^pKu^i]6V)KnVc/bNmBP[2pAo5rT<\Mm>B#&[/Tm4WU(O!]RP/"c+C$7S+DOKYE&PGK?I_pDe26o[pS!!hOd$Q_5HP1j`Z-d
%]Xh4Ofc.'&`aT^Go"u.<k3VKSYO4%Bhn*H(f.`^"mC+r53f5#Ngtfs(G?8Qd1c4"=FM`grEqA_qGN\^5Ip$mokJf$?c#X!]mi2%W
%RH2=/5K#CUEPI#!\$@fV2]pnFg[?c_cL,W><c#un\uhb$jmB[lo8A+148\I#bOZH%pN,r`Ge%a9SJ,8b4Enb&k7P.O?VYLB^Uno@
%H,&dYI%jKBYk$`Rr6$`GhAgre:uuk<1'^fs\#-A_Wa%T`ErQuVgmbp^76\B\mHs#iK/0nPb1m=Sb<_KL"^Q!L%mB,s#@6bjbPcWF
%_*OFb#M/"b9@Dp&9kJ<c\`</\[(1=CfR829Mi5>T>^cG8g$@D]Ir#AG53b6#;R2J_!oD$6,3IYWf>"ijbN0V$B9eW92rE3!^:ARa
%ge7<!C'*.S!rhTRh:oE*B/a0[5J,a;Ro^T=f5!A*lRfE\202mk1qhc*gU-Bplob$RIIlUg`Vk.$_]I_.hsoLqns4X3i`5gBir%W_
%g"e=]qVCP``Qaj2H2Nl.M;.@ZGB/'%pAXERju7apXnf)Tp3?K&m`jG\`L#b#=)/5US'AE"Y:ELd.*V>]p?MA>?bcX7H%gQ3!;^-[
%*2VJBVuKK?TNgotGL,*3^B6j\2/^"hOj!e#n9Y#aMP^*O/ZQ^6eLYlLWV>^:A%c'\9c<!c\!K&JVsB:.5Q>iCpei3J;e=:%.J184
%Qb[?*]^nqA8"+6X`[A"F40ridcc:;kG)ZU*GS^;VSFL"D$u3/eRa%JbRQ8=Rc!ej\4?(TG3V_FeLGaJEcWapYB6T^:hd0[FYAkuZ
%e][a9SE1j_ddR+gG]e<3qm4IYm4F+6EBf`HO0S>+b^J7.LAp>pmS=Y+P'\YFXM8F%F$DkQ-k9*<5F*;N%oGO:c"?9=5'b+`G244L
%C.i_7aQMXXp?IRJMi7U=i=@s0D7kP7%KC03WpnitlcXKf<Wil@[H4SFYKk_oo(Go+Hmtb$/*=&pR2Kf6j%#,^r+,B\mG3!DhHo%;
%BuM=)K5/JYNU8hIHO7p^-)Hm/8W5Y`YJ6.ln,hTX=63TPqXn1RDqUc[]nJ"s:NaMJ3!siZ2XL:Z3+VdP>B^#REHk9cqnH8Yl0f"D
%?4gZF\)d8Oliuk#_mAM+"eVtP8tUKXG&q_Ep#X0C`VDQ#o'oMaFk$Dp_K55:o6^D!D9_O[(p!IT>+9iCh40B>+U5(.CqCc/o?E/&
%[5Tc&R`Xp>C\*J-\)1J^SR5W/gt_qlm(9rYEnh9+F7eZl^[^KBpPo2X,PIeh:*7>YkAJma96VnVUjJflIC$ef[QcnB7tsmr-BC6^
%SBKg&n#5V3Ip9;s]7/;^A1m<shT#tOH@Ki2MTNMQR%6/dK9>"q?<s[kfX]_mL7V\!J+gd$ilpNk]g',5GsnLj[\i+d9]m@pe$R-/
%..+H#Q)i)tMmF.<jbk1.rgVY"iPAIAD,'<Xp1D;23BKHTGQ$qm0aI<Gk/Q/E<aKe4?EXB"P#g>+`el]uhBdRI[ljX#Dq&CrD,'Th
%Jp#c^o6^H4o)A25X/9K'[rqgSkXi7![%uHIQ3,t!h-R:1=1R!b22ef1bNi.:m,t!U">9bBmC4/\;0G8l4+Bha-u1090BS0UN^R$X
%%D.'D%Zr1:(5_U6M[#cfUem:5E/f2\Mm?rMp>tO!`1fB!08EYo^XrBaW!7S]Mt/%pTiIV"pL@(G?W&GtR`Xp>C\&`[;>`XD/BXt[
%\Ehr;7E)!k^SFtr23Z]0*k1UC.'*&Nf<>$DNC>jW8[0[Z-F5A7rl=ZZlsi`6WkJ<9Y-H6bgi)\p<=rPmS_-/GMZ)4a:HFAMNo3@b
%(OlALYCFBU0OJb7_Q[g6Z,U6p5l0.p@+cn.`ni1TrA^%0"(1pB^/b_kD8d,-ptC;&*Ve>pn?lW[/X1mtp9MWE$ej)4=$(B/Ir)92
%&Ct0PY]er>#Hmb2c.-Eed+;LkDsuDCXB:(heKqh/%`#t>qY4i*&(bL6&fe(%i>QR'RI#t)MJ,DGoH.*;,Yi3c-&jM#iV/[g]t!q;
%e_(I?s2`lD>FEj3[a9n#]"E<:n`KE)q-'%&O\jq@SDF3S'kYA5G$FinGB&'./5j:bQf`fko-8j?Hq277+.gaYQY--)1k*][gB1iP
%%uD)-<1524[WPOfJ)ig%m`=SQ^YR<.s#sZd?N0uNQ@Cmj1c[2Cdm-)$nR^<?UR,!0n*cq5DC7Vl<MArr$0#;NSU\R3r[k*>0?iUb
%npKgc2u@:?dI&Abhd1eVqf^B)J+r@k54pl*ZY_IL'?k>I\\JP^hk-P4a3:#=NOC0WY*d+J@6bcQLg.5'XI-nHoA#^chsGho_lWjM
%7:(+[GBs5Uou6kJf"5"ej2Wn54aA>TCG:,.7XcQN`>A&8m\;#d,PQA"Jq4E"IL#oArqV%nA4XFes*%Ptm_SpMnGaR-^OPQK%Wt>Q
%rnkKb4:_AJrUg,qoLL%]i'(?o8KN8n4)-M'EVn(ukBl^o*dk[D/Kh,X>$nFcdb\ZNX[Dg"`RWN+pE-!\e`EF;]jDZtT+g_VMV1/j
%_:I\Y'iNlLRlBfm?C]g-/?7%@)V4EA(L*(A<j;.=GAe7:C$hnTCqerBDqA?"4XVoH;-H,XQ_#@DY`KkVA-WLq_*[k6UgC'Tm"L\l
%>p[W7b[aLED<kdhd8rO/e(r!^c"Ds!Gk'JX&7>G$p9VM=hHjLCIs-&+qQnqgZ^YV+g'@N.2HWIR@Eq"4YKQ6UpWqAfgc1%%\ZJe4
%<_V]9CAD3em:TAP/XpI'EQK-d.IkK]`hrZ#'TH%&f)'aZf;Rli?(<PYqqWq*qV<>RXKk#PrlN_ZJee"[U\tJib2]+1mh'tCNI;DI
%Z%MjcVp.`A#@#IOfJTOP0YAA60*fD<_=7$6U^i-GKAkp"Gkkmr/t#k\n=ir#[_Edj0>"<:*Xj\D@<gHH=+!#J5!*7>qGNTV]s]&s
%31u+h;JC>]6%<U:Zo_]8pf%[\!&d-8hBpmnRQ9^qfYe]$L0Y%R=L/MCY_fKr.A*F6`K!Fsmf**.Fa=#aZcd!0=GOq:LDegK5E5so
%VVQJ:%nKAUr&Cil^cVQKI\\='2fI^WJa$_h0R^mR*mg5`kY'YkVP!nblS$"9H[YeiTh*Em$:rRfb0"(BX`__-h\hPan'frk'T.:O
%U/EF!g=FYT1Pb-QkJeM@]'IsXkIK43Rl>7=mccXb?+aLcP5"@hs,Q:;O/N0YGBW^l.Kd;k1G?.<p-D&mE>k9AmUmV*U#04k?58?p
%mC)]Eq7(#lhd3AlK<g6dRa/1a/#KXBQJ/&][;HUmK\A$#[)7oD!*_Y>1%nEW-jMiR*pGM.DoZNE^lG[3%L5)8c>'rfG39BZN.W-\
%f66:aa#M^+p;DtW)VY/CWn=>(IeFEHZh#8!oOU+,+n!Y$n'A.cH[E:M(0c7^_$O9=>>"Yio)A4Zr&JQOL;HOLrci^r.dM>SosQ41
%?EF4jq<%&2`NS\^rnk>GS+AL0c@biFRbRWFe(3@u,-Shu\p8Mu]B/bbA_$=?)t3>-*>"eQ5XY7d++T(UkWJm4kWCL$S7dg0EPU^^
%Eb\h!k&IS*P]XLO>pI_Rm^U44k3hqhc&9Nb<XKn?`ouqIYX+o(nFO\EGMg'F>&Nt])?jIfqT14lHIj_TT+LX*S#4o#9Cb+\9%Rar
%0.GgpeR\A#i+pSJG!+PqbnP=cfP<Us?U'n5IrHgK0SdODLW$KN-3!)>A;PL`f+_"NnHBfj=%#sb/[kFkb,5JMDRX_uFZ8KVc#)XP
%*438/2L!O1%bf_3[(-o,j$B/]$/4/XZ>/:h\J;Ud#Bao#e@?;5F\q62lJ+[r4/d3*Fm^J3T_su&\Q+jH)g1T$^Art*h'@/NV>FW/
%\$,gRGIHYM46U`=6(`L<"W18Gd0DpkmHZe`'Bo1d%,B_"8f[=T&%E7!Lb#aLah8\Vp#?$_Cr[B@[>:k]@/40E\N2hPo4LQ[])D'`
%r:9iBIpHXaI!bn2eJ]sG[bKBD>?OIBDsl#qq<Z9^q@t$`]t?cecFUR[AA,ooF:W-L]P-Qf/]1__]I_pJht?P@f4b$Or+qW@YgIc$
%#OVD,=68^SBgM7KpG'X4EY!Z(Xnf*O2(c7^EdP](f[o>1%tCjdfr^'sH2I$tc.Vp3g-`F(r1Lk):"*3M`cZ4VV=fOV?T?DhO"9YT
%fmd:6^K]e-[$KJ#fWkR@kMg7P?RsCW;ql.`M4^Fm@.J[pn`^jVr7[[DF3+@Y;\"JK^o9Nfq^Kdl3:Ee(qTHX&GC+D98htYhP8tEd
%0\DGJHli=K+LiI\]73)nm\;6DIk%\gSR\Z)TkH*4U4Z/3UCZEYjTg,a'YQI>r^YOthsiYg'?2KirpK,W_2/%JG-?/crqr9^I,59H
%[&rArAe*/'a)&sCm^*c(kNCQ8be`cA5-3^CJ)2BM3kN#:s7Z:u<<7`cH6HX!i9kmnp$TSU=-rZr5J)<o/ou7WGF5)kqW#*J@G1=o
%EQp]6qW#(t*tF@lc84CLIht=I'0a'gqu8SBUBUHiCiEI0Skm63pLc]SkpZ8r7(fT6g]p6ArB_cUaT/Xh`4=RAd(rL`o50smadrSJ
%q'2g;H0R&XI!G\dcS^')LAPr-G5ga;5$PU&!DB[6O4M&t^OH'OLo.l:m&k6pIe1:ug2/b<Z37n.$M!ZN1ZGigL\;c6j*6:1](7$^
%a"IH>j3q/![PFE2rLNLf[s@lF(hDXF`R]HjT_9Suc5nr:aH]#SqVrqj3.;I"P!.HeS\II>9d/6(Pc*](>*2OhDmsEK"mVHH%aeGV
%lHR<(Y:f:PB`H_m*=bRi9ZQm?hcsIba'/B#7dFpY2@nff2qr25S,DR-c1rITP)[,S8+,S[bkC4erS)cB1OieEqfKupfP5@2X#PbW
%nAi/IS%!=e<)VAb1]QH.+!2l-mC^sEd5`=r@JGpeD-)laQp.+q*=dAlr9ER?iV[oGlQO4;dtChPU(Mc`&Yu%IrbLu:roMC?.u)!L
%9VekAH[GA]V&+lb3EGtm(Os$/RK)BH5Pa\jk_K3.b9*n%\\02bfBr>4r)7h3q+lM"NkYAhVa#I9q1%5Jn*%OAX.DrLIJKZ"Q3-;?
%$SCF-,k$&#M>g'&f5gN6)>sa?6XTdWs6G.eJ,SOsqu.uarT<>Dr*O[?HMQo/J,&8(Nb_;e<V7pbeQ2":OF'(##!Mi#Ln7t=?K2V0
%_lM.)+_#ha=r1XO#3J&\h()Q*3'-4Do$TlWe3ic0r%`/aeQ41iC7CNtY-fKgfNP6^0ns7K^)rn:#N8\QR6$/c<F8dW51X)+=GaJF
%-)]IXr^2Fp?7ii:C>5'2!dC0oWu?%(@X=;FD.oH*))7[on#F[M/^lrD:\\KR7t6Z0b=%8dNcSO(goq8Cpa/q?+Vc3@r[J&JQ">oR
%kq$L1cI2i]qPoi4D;2*V'/%J;FDYMV]4Dk$/7+Z_TtA2&AN=5'qC1<MQd#iQb7#HZj>)6EbL-35@4q;Dm.4jss3i]>O/orfQf3iU
%5!M8]GQ!<OqdJ^eg"1<T^0V;t/+9$5B"a_Kel26h$u65rq.!Pha+#p,L.pjTM58-TpfokP9H*a_i1?)?4f@B`jLd'K]_#07o\F/N
%ArJGXXhW"1lR[JD'@`=Z.a'b^R:o5sVV*oC/4n06ZBEP`6!Mo(aP-5LG&#\TS_ShWemA$tK@#r>p7kQ[]B+NR)KAtV;(o6-F<VCS
%KGKjSV2XGt?7g#P%'P?<oshO3ceb/XCmH'oJU7Fj%^B_5>"$6j!Et>:DF*K&TMX0R8Lo?,UoF]3Oq!9oRW%)4#4q^\`#i,k.@J/.
%"E&gWp\4,_+3\R]G-%UAn!H1dEOENfZu>A`0C>)b[appPn;H&/jFj]Wk?jeKMW8o@Ka+):#NdRJgOm'[T;,Y8r'%!ELC<ZXn&'3N
%<_<nUcjUn&4hAXOE:aH34283F-1OaI\u8;7K<?0^hV:aGIc)5r[`hSu)%-&\.X07-m%Y7MFDe,Ao.'aen?:L(d#qs\Mq5)s382M)
%'e@l)%;VKo;-,0QJ_AaIpSCb>gEU,2FupW/b`R,5B05jWd'TE`o/@2uQVPk2-gf.lq::LSEI$DGa=jW@3PV'YD0())cHQ,"7\A/-
%WQkG1c0>18=8h%Ve\Bc*roLn@pE0EfY2LHjm@F,geMOWOk<>lM-dEOj9mJrA7JC4O[nE2ojthC3pRabaXicP;np0*1b<O9D?W::n
%aO[+7^5;`nB2''UeUsKrVk\JpgcW%rA1csC4F$;O))lg=.ec*6@<5#n&+<h^1uHu_%Dn7AC.0%USK1p=lr`VpOgVC`maVZSkZU0e
%HJ[eTIV[+^35X7l"FZ)eaJ2j$TP/a=g@i853UnU`TAgsfrqe(1@W-_JGB'#-R"`$3lV!F'cMEhI=%JOX*rT1oD<Z^KT;/1(lYbP!
%'5OJe4[652G6je-4LO?SN\kG=SFo$++pM^[?+B[`Lt+N#Q<'=q<GLOQSGOS6gh!pb8%5tr30r(AE:7oAbc6YD[,*h?)E@D2EkL"t
%(9,_iJ0f-(H+*7IOZ.Ht#NL1\^Z(4"g<pq4R7s-m1OWi9iQm#mejW^7<F=om4-j.%r1()p_K]`t+C+Ut2VC9[Uc)BCm@#+)g3ZEJ
%W\r>[OcruA#^`iDpsqA%d[ko/pV1uQrmDP>RFmg5fBea/.s)3(9AA-FPjbT.48V%eo&&^,#$]^$g&,L>pgj3cCl>N"Nh!"ticB29
%fs4eDE*BG333b1eSF)o"j#bcYJQnS^Y;#.@HA+L`]GKB0$9Ja.7t;AY]U7"pcCstqIaa]D#g`H`2rdTLau#r<5@8RtpZn3<K^4ln
%F)UC'F#`aWZ3/DJ/f+09I1,+G*Q1%0X\u[^p3GW^3J11+BjN(0aF!c$UXHeWn<M:;q&PL-<-XjuVGo>.i@ljfpj-*B0SO(#e'R$!
%<?!nd`[@XWFBQrc(pr`g?^VBGN9@I;=mT@*khPl3526aAG9>Q*B5OZ&)q1Kl>1\RTH*Lk!<*`VhE,0ah<\`r$s%ReE'@YQZ'O$^"
%IF]PhcXJq9oBof=Zi2ZcT5<^5f_G@l?8nGK)`QTiWP,c8hc/MQlR6<K]pYj?+=q]`cQm?6cgJk'1u(sB8*frCQKM^+X\jln67+;X
%9^\F=1L2p\^dDD.`/Fc1k=W4TGYjIS\)f8knYEq!(>`"5D_98$<q=Kms5F6.CH&r;n%Q;'O#,B4ZI4MMqS^XtT>ObugAh)WB>3bY
%nf7)qS?q6bfa?&JKk<>hJM7!bo!:osIk/i.T7aH<_kgJI*R.NLLM\Ps0[(4Ng6\SKO.+G9*Smogs&eo!#3Yf_Q/^5pK5Lh!8hZ6:
%(nSf0Sj_>iHi*T4ekAMfr<SJ'ct&]=Fh/M)Dalfu1Fs8F](^'<gPVljZSV,=#=e\aDW9i:r4A'*U%R<U+22ibS--8FP&[p1o;SP)
%_[T>*qL)G]S+jo=DOAbRUVCn\"FfIpp;]hDGPWq$(VS(jcfXnMK]VRM8bI%?Y]KLu(Bk$sLJ<5Vk&=hVVAp35=\Vrq&U#Ujon5_n
%E0\a&_(srtcB%Ee]L#@D0%SE$>^>.0p`@\#baGS;ppm\knGHFG]=h<I#G[Ykr2XFj/+-Lh(Vf1mo/W$/0Ge*IbRrQq`7gR)Z5?ul
%JSe<RH<jcgZ%LfBF3AMTGOUD/N*5)$LZpX=9N[j3/JqWJEYPIh[Qh+>NkFHCM>:1[33W=dTiYXpjRS^=GiY^bAu\?qV\gR3._ZQ9
%"j\cR3!)0aS^O]OKQG%)5J5F)NAK#0YfAb[V<gWoAPr*R@?koYFalgW>,<"r1^nbYMa5D))u;5)(caZ/cW.gpaDoGh]s8`\!<Mu,
%5-ZaZi_?d$5;kukUBgD],K$?dc`A-6Nj^N%_R/IG]&3)4ml`C8KK0n'i/Jq_G@'7\*:'rskK&_%(T5;t'lkJ\k)<NHD@8s;/T/@#
%(%"\%Fl]IE*P*RDh<`IqH4IjZX+#?u(BheTqY7YA!)52!k@2L*=s`Nk`sR?%lL)W%J2",%Z#Oc:D9Cu<a:a'8S)%[L"Er,E4.k!p
%^1YU/pF=Cr[-O3-prtu$`<5YMana@6Hi$@JH$I)OdSo*_5<k97I./$'<cB)5+.jR0^PIF5B57@V&AP48hmaZ?FH`f<A-jU)LcJ!`
%N0=q_nEkS\btI*AlfF,,kg*do?q[@o"t04+.eDHTR/']"9T<eXR+=60"+lSVihr^M]-$5nT.N9?R,hn*,N+($>1)kt>O9\0OT,&/
%)d<'dqZ!LUrSmZ'C']+9nc$`Ypj\lJ97Q!TNs>SZ5ObZN+5'G^3G:@Sp4bBtVVChk+7[!20(%]t1be7oj6cIB&+<52n4g[f/KaQh
%hG<73EB4KhCkZBdEBg"<3o>@_s8F8)4n?e?[TRt]b^$YDS_>k19/2?2Zq92ak@,U0.Xq_"l:d3?*'65uP&O,hffBOUr%Wd2NR;<C
%3jMB?\\Y]U0BY:fEKC,2?='bF?u!J%(kLGUb1[;dI.A.>m.<onS?$PlH]G\)@Ha8eL*Aq(J2hI_m4h'O0>]r,#!Dd/Ie%H2'_1_p
%^Go$W\oeCIo\t#;U<E@k[.O*b^Y'_/&oLt,Wj<,kGP.\Jl?55L`iUNQ^@.-d=Kql,'i9nn`c7:>G'&'eM?3<oo].)]\Td":Nngjl
%UIqucM8u,MC>f]nAEaN>Ch/W2(uhY/(9k%fCHfYU_[+f7f)&"ZeCJ7WT=_*R.M)Ef/`5>WP5I&K/oIg@"ng8E*8sadGN=-06??+D
%N?4Ctd7WTb/p%>9pDS@ea6e=YOI,G2CmBJ%c(hn)JP?$\EE0X`7Zh]`d7-+4+`%EC_N'U0Nqp4#oQiCs;MdIN4Qd^U$H,gWL`eUm
%&KT-D@jC=W[N''AQe2Sd0-V=49]3@^q;\@`583gSn7<iWaNrV"'Y2P[_nLWR#j:04*Th%\M`i/uI5(-*\9%?kqZ'L4\U3[q*Dsdp
%!+5K:bBc:CnbFC-Yl!@66Q*\'OU:30k'%t%M-8"2o>)!8If1SnGiHfo]U'G3?.6"[-cb$0#0-,/%uqQO6u``iq<$A*aeSQq?[r,N
%#$8CM"GApIm)K*>+b\:k2^!_7&WY6g@+(/r7j$K27)5/67.csin.u](rVX"9$[LW'*?O8**jW,OV7FnF5!*b/`u^L"/^.!1r1*c*
%4QDPGobn4+Pf8]=6e-Lk#2gI[P**A.^dl,(R&<c#a:1qTIW98_?A:D*lclj9kP#r[ht?`roNLTpVKq?4B6hI[MF/1K#COCfhB!i/
%FtD\@Mu/n1qbBh)XuJ*K0.r!Q)BUT=B2jYcc@:`cdRq<ml<<fVcJ=t&oW?U?k0a3MH-lRZMjsdWl8rjLI1O`9MGfE^T7]"O($F@Z
%KRX9+`[VCo6U$J[*r*tp\)R4C5K4&-?CVrsRPl9.c!-iV\c35]^d3p!F#<&3Ld"\/EB;-DcE4#=orie"ml8lmICZ]Qm?9b`L!ODk
%#<1q/hf(1lHhi?QI!;oXZT06PT&fr?e<i@9l9ANT.4b:f&4R*s^aB6aiJ2mVH;4S&D"'TKP8#C5R2Gb,9.qG3'ciqOl'diZi+P(^
%;moHHkMuQGYPri=^?=Bls.%->%mIB/XuL[4#iYN-LLSZWlO[<Mn>0$7S3F,&JG6@7YKed2NC;fOrhW&^s(Gs&Od"(Hrcs&!BbL-C
%s/H]`kH>%FNQZ)s_4C_OGlN)T48+XK5JO<"qXVYP&0H>?q7E9t*AnW4pTo?7"j4UXpV02"a=lEKrGR2bMdQGRf=qD2lhe$+ThM-,
%&uH+?gS58L$]"&orr2gUJthGJa8^W2s$oTU(pXX2AE4n_p)Q*Dq6Q]5g"X.=l`[f6^\FjZ1&a;iiToD$h.N59J"RA7D]%:[8&`A8
%)RBG:$tEu[beVVS-,H09L/R:7Z"NS;N;F^sc!,NK@h4%=^T\*l[Fu2%#DS=r3o:OR46as4HuNof#Mq*o670p=;N;k&YNbX#!OH$[
%r3Oe3q!Yr("Ne&Dk<Jq"hu;mWJ,Sc\J,N"^q"(-Wk*J=4p>c/gS8'-]ZZA>Rk.]s-C`pF+Mg&UPP6%ME!t^Dm06P:H+"P1uEu2),
%iVX&nh:^fFHsuppiM5Q*!Ds5ur('Jp?LT^qVKeMsbOb:gl#+4_&!ZZlPPaXd$&^5bgN[6+5Q=6iEugeu5WY@2f)2iL$=7aJJhQ]<
%k0'9?DTX]*?.SV)Q*!&)lf[=.#V/p#r"T:!C)%0P]B<bV-SH,dhrEfsqIl@@qrYuLIe/9PItV_A^0!('3+0_k`-9l;*`Tq8lZul#
%&*#<Fht+.o]$krf,A-;m:IkEs[N8\W%L[FiSs6;M!',TD1B@p+mA4LW=@,_dnuTjs(2K\cWpV9fqL&2_A*[\KBCMZ85f"&$O"Z,F
%%poNIo3:B&?d6VS`p>MT(f4`"HsuppiM.=O\Tl5DY'^?s^Ul/:Jk<W!3WAj9jYm3KG^6e#0>!13Z9)_!bnnD"F+4ljlZ>I$&Zf-o
%nF5-q=(FOM!UaiOE9Hg,5a)h:S*]A-ZWc&)]%#Ak1E0mmjaK<YJ,2Qi9OaUR>R$sW0)9nin,)\R_]1dHM(0#E9EIIi^Urs_;oBmW
%I7Lqs-=@k_hX@qJedDdJ>9#.ENmGcG&O*3V8uIU3GY:5FGaV$l:\uO.Q^;Un7e,SID(F_SNc!<Nqc&#7s7:`g"B'4qND]e29X4=g
%.$q6)FXu"_k`\/4?FO#G@qi?T(Z-)/a4P_epgQ]=+W;H!L4#E:I?.B9;kB=OoBs_9LR_qXP@5Xflb%T5ZgiO@]nc9BXgB:5O*=a.
%keXn'HSP&<X>h;5^2?,hJ$?t*q+1EEqN%lRQG_IgAl'kNAa3p^4/90N`P/TN-ug6.X[R+J^?W&F)KRslA)fcQq=m1k1?EOo?J>Qt
%3]W4%dAu;-7dfknFWutDl92'Am'(Osi)<QEI6d/3:IkG)p)T&#cFY9X>JbtQYJLQ+hhAeaF#[,__8DCA[+do8T8")b[blI@pcnL3
%-!KF&b#,,l<qMG5];6M/%JauL>EAPo!m$8N.^.SdHFgC9"1kdIOZpIMT5,:s*k5_doC&nC59Kk$-D:9UM^jqo;\mt9#'=7TGkH(N
%l$7D`<rMrdc[P6$VLQ&f^k4%7q;r(Frs0^#10l#5q(`@l]u2l9'ff(VPkO_>@smqH2]k2tNaDckq2;BTanS*G9!P<[Rr!b[0@M,#
%Bn>!-a,XUh[%<ogfk)geZ%E(.Zp"ihHo1h`n57C4i4@;o]D;D0bKZAFG<95;mf!"[Mt0sp[b4ZET9'$8s/94SFb5sVo,U=.nCpM:
%QKNV0*0K7ceE?@7PNBAcPEkXf4raGjp`]q>g.S[=jZJP%Bl$UMP#.p79m&YW`i#^4kJR<*T7lcHXTj"%%<3Y$8r9-#0@P4hY<.]1
%[!;T'@CsR[`oCtGs!)n)'&4Kf)==YISB3UP_kUsjj'b;mC(<K-CbQVfBnJk0lLWF'gg?:1M/4uSnlK2rc7amOE=L5ln&9U>m`\>D
%?\A:<X$((nSe"8Qj/p6hC&dIAU&-[1EGTLAZ=g'jn/A=]\A*!3\a,U^j8"A'`K.I-ldP#u77@`Ek+Cgdp>Jdds(C4Fr\Hn`O+24$
%J(@?kKlGAmp>GZj,eauVIP]dN:Kbt:`g9sfIAJ.o79Iq&:CmC\QF!5l)^kZLkE!N7HJ&5f3+i%`)?+:+1]5G?I5LpfZC4!SR.Uh^
%_A?Lg0:pFt=8D5hnYOe&rVKY0.$6T</W6[kq;jX/7Tdln&2t0<T0C$!=l#\4@l=9JL]?'%%KAIT0>mf7Mto_b7per[Se6GFS)7V,
%J!]J.s87)UqrMaGrP/@NSBCm*pV6QY#oioRs4lWTSu;SMQ[f%9s7X(^19pk/#:JKRgV/X@s8B^Iq<dWWScL^j0mhalJ<u=t<AKQ`
%G;BVEMg=?<WG*OTjJ)bMhgYI'&&8%s%mU*f=24`]I/![:b<LIR]mj30q#C-_2eS4rJ,?k8roUte:]AC[$NL*$6jl\mP',nj##J(R
%CS%RV-r3)15SVHCg0tELG+S(1$1i^IIt5WHQ7(a-ZlSce)]<o:(nL8P@Tf$L%:CQEY]c["%#Z,#;2oN'UR^bi`.3lR!:e2d!0I>X
%1f^GOfP5To_\ibBGt;6EF<5_!PJ%Es4%;c<&0>pJp!W'?"uD@XmJ]#\:`rF-F@$.:f%iUq[0%J.g,8kX8"FVdjr,aHY"&a%NPp/5
%"U7@&$R*896S<MmFK5pkf4`0d5iSQ6b6KBbkDV;I7-Ka&:st$aG_<^6_C)L`%"c\an8)*4Rhh9]*Ag]!"Xn#bI(o^G\J"6s7>8r+
%]QjjDNVZXn6pmN!6.Dqb??ULXlgd+#asr$=5_uE%]ebL3Zm&Dj.Y?ntajXi293Wg7\K_*L^oDZ8qpCj_0QkC:cJ-jgc.'o_D6'g2
%eHi+Jdl+Dl?IB*jMTI88c/KUGZq.@2\k@&"mAqETRJ>k.O'%U=Y]=p+RehiVdVdPdT)B6`;&s1=Mg1PHA_3`1PHa8b.cQtM&6pfQ
%V6sYGU(RBpZO4GigKG3rZSi)0MVOjb/'I=^?o(R<[]*o/""b(m8U`_e&!I]<i5g(i2A6+lquee7H0:IOn*PfoZp(K<Z8aQH#VGN.
%/m8^MP[n`'8QZa,cR;D%0u-ohg'T1)h.(sZ7EbaDRab!$fgc+s_(_<\%G=<4`?75V$pZ*;Li]@1pK0k@&/A+ke<c)7[S<BIAp6Lk
%b_QU^@_!.tepm;dS2JR4De[WI&9iMH$Xq\7&t9YjN$rp"`Ufp@=qq>9``-)jB6(_s]G5UfG3)bOp-M!$Kf.9nLK0OuZKLN/>Vf6W
%G`\mh<Is!uB[GX1+L-g%kRGfAgKKAmb,F;?B'_%*JF]8M,aKg8PBI!cQrGU$rgPh;fjIglY*cWD"11>.il?AJT(-mPN-FDq\ADEO
%=&F_;90>P%AN4_ah@e,#dp&;qED/SXP+>q#P5K"Rpc5[cTq8]-4&T1n70uh>7Te`J/L'c8kt-RjTn#=$Wi!*RhMXa3N<_G?1XfQm
%6\Bb*nhGEf*P"hq.83WK=^7gfXFfm_@Eh*#_iNYJ=JGd&_0aM)Gaoo#Ur\.L'GhJSG^bGp3h"jm]TOlc&.]QT*-PJAkp_6e'.Dt.
%T3"iM;DV0j/;?VGUcBu-aC9;9n(]SfqoEPG.j"E@4%CmapbE_kWfj"MAX?42U>S`]#JX[ZqHCJ)a:]7/i$3X*,Xo(+@_$1/,k)=8
%7rX.Ur,!-KD2jY"WfQ-\^&-t`2ldTo=7P.j.>R&XPK76-6b-ElAH48W^ng]=&(a+K3Na^V+(JDU/7<\3gU'[Q5DqNY':94LeA^$`
%PYfWDT4JL*ONet.lo>'%>8QBh_.g?o0N,C"S#sZpf#14lRaJqr9Q:V?T$,]LT3'[`$/j*6>9gq&A:fpsHPOQA@ADccD^EUKden(p
%@,1SG3.C>eI`D,,KEa>%9lB5rcgnr?elb)Rj`>SPQQ39"\t3!mIa4VF.e176B9;/7\n(,\a_WA75&HWVl`#c+2lk"D\\g1`'QRG5
%/.2Vn=9e_AKe7=Bj[)@F9[,.]>3$O3bQ)IV4[&-9kG(NIl\b$2J;cGs@%Jr<Nh;A?D4g/Tno,W.;Jh:C)<ce5bC.MUKK1DI(%a,j
%&"@W7`n8u)[GFgkh:&e"l'arC]6#3dnW/qi/ZcN]M"sg5)51nbP/c]hn^#THCUY]1Y=2?$Q4V8cNEU^]cu,66/W0XrEDcQQhr$FQ
%#I2`55Dkc-moc_W";2E.T@M9H9I4G(!>6&MgX$M%Y*fD`o55bbp)T.^d:>ZSe2g?onZ1$Ol*I+AaGbL?dpDL;O;sCC`(K![pdD2)
%PWSAQ=NX/.KKr$&2eQFF9U6JL6bmR(f9[Xu'uH`nFP28kW.o."LWjlt:Q6&O[i-K-hHfdEH>YAHg.s7s0`u^\N<JEaQL%tZ8WFVL
%^J#jg4)R9B!&hBn>n+Ob=(5Q,C,`=pfrVh)+Qh42+T"b'1ecuC[ICu(-WUmT(sbiTqCQIDNq1bhUQYi#BFOsjAkeKX\&u+K+?u"c
%3H.]Tk6ULaTECrR7J`+k9T:o3LWbbfJ=9Yb4?H3F0UiolCJ@$,"Pfi:c:Jp5rr&_i$8,FXaF<RFX1[YS;4)k/^*7$.*8EcPqWkm0
%)ZQ^rrFr248Y?(pG"SeWh_F\eX/ed\Ak$+>E/k!U,/'F6+ba0B]<90$(9)Xr:Da3i,qH(0JSY-2J;?(QRG<uamc<8.)%fj,>lhAe
%DTi;(Jd1L[8Yq>:WOqc[Wob1L8_EMYW\#SBklT;\1>p`TB`t04%Il2>8FgcBaB5OM!=31@-KRD)3KG@TC^E@>CPG-IDh(bmAe+N/
%O&m1s`c8dH^3TA^PZ-8>_]>H':63aqIO//;%>dJY2oi)ddLaYT-?M3>AXW:.-G)`p"00KK/"I;uR5I6-`>3O&.80EC'EkXsLHYo-
%\[p]tcEpfCL]n#[HBo:4I`$i=^kmG.5fjhORnRPq)"7F`1kV)C7boX0!eel@jnT,2#YYpsX+Wsi0N<X!'V0DR\sPt<3$'5b;/%c%
%:_`<<&\VW3bdkATg;rZ!-Ye__)0[-T9M9^Yc`@TX>JUH:$Pk6A,6>Y^BM/khJl(`+iPPa#q72Jc]L,QoYDEmJ6C4&L>1hYLHXhZ3
%`PES<Dp)&.Ld`n/8]]W2U$@CtG/B](O<EOT=n>)]&r;iuTnmf"1e0kNUn$k.h&2]MY!5sbLlWCYAA%Bmn&>n&5[nqr)Kl?l9M'>B
%V6eR[A2*^F0q-2T1HbiHP@>gQRETjb*PFh[@<T?Qfrh'k5/K%"<=FHmGYCDZL"[R9">Dbl6c2itmt#=S-?&\?3$*`1U+K1Pkaum>
%UPs2?*BE%s61;G[V]ZukiaSol1PTjZ`+Is]:X3J_e%(,6WopJ0QFEn,jLT:X>hX;@R3^;%P7u!/U]3,96ag(iMhtm(BH(B24Ml8O
%)TZ&K0;5U/'>328=.`*2(DN`HSl7eZb7X^%hntt;%Y?76dc4/&A^>)_rV_(L$^--nk1bu]`:M%d@TV:d(Ob#MYm(KcBGU/np^fnl
%K@<*]=Oo(SEFTEHA9W*Ol%D>n(Y^06mjPhQnIedZn;BSjR;0"2d&_h<Vco^:+G6$Aq<I?@F&0ZR[i't/CrZP>/2@)1a+,Y2bB"dg
%_5+i/:tTNa%tF4HE2%gf\`o9^_RcZm=aijl3uQp(,Zi[TAL&M_L<>,L=pU1W4M:@`'is^8C95jUS\>>)!%R22?*U@H<Tj(Z(2h0@
%E)oN_J+r3m<:_M<T/j?4?rO^eEiNHiF*V;efA+e;n_GUS736U-ibbY\&l3lsZ>_n/74X^QXn5r:H;F0mX0k(5dl?P`b?CsgDb\<e
%gc#dAf=!"TqM%1]KgHZchEl95ZA9$$2\V7V'\)1p\-ejSRd;G%aK&#CIJ]Z?Ic$D5:ITG47ece3X[FNUfdGZJ_jLqg'E(DinI[#p
%k,gs_;I@Z?'sH@(g/o2^8TO&cq.I\.+YIJU^/[$JRHg4!]d0V&5+)(=M4&.;nnMM<X[e7[p+il/UImI?3C7)nTcR4f$3#7-j*+K"
%Np4uoGd`s5#FJVjDm&4-"[6c=dc*nh=JCbDh0TE1EDK6kpu'Hu$s7<&NdMpTZI"c'M#!rlfO?dS95Tf,^iet#]O9(XpHUdiHph`E
%rX&G]joo0i.]%i`7Bn3,@Wtdd_ObT88Z#(Jo"@>9YnL0r,0T$IB8p5k!a."<$]!Q#d4$;>"0<3!^6khm['e"u,I:PYMKZO<nOLY]
%"hU;B((0.rcIm1tAMl&g3t%51)jCtA7(jak3"f7!%DO'>9u1s<T+cF?H:1JTO??[E_\$l[pbFl@C_9S0#>@PYL9$h6k#0&`.]#Wj
%VQFf5`XW0&Yb]>t-cjURSOJ[D*&$dkaN%b;2u-.rCon:ccajU*^3<BH/t?O"p%,'Y]W\O=f9MYkoH_-[n[VpFH=B5r$.nhb94FQR
%_FQkajPiQ)rDKU5?FV_\_a.jZdFZV(P8@Ht`I>r^odX?WAm47T^?"!*!^EaQ)#"6`AacJldu(B#pjSp.H7<_q1N*\g2G-k:!KD6_
%;Bn5s*")%r5pp9=ie""<l6[/[r4)eO/D?b`k`Xh'!TlYc;A45D&mTr5Ju.+^Q6$KL8D#^4.7$R\f->\B[<.X\M2UJj7_fAPLosV^
%o&sa#U&(T>J--V*;7@!=P$oXj%JHiOCc[\+CD7iF0b@IP%OWU\YL#:/8VhdZL+eaB;o;Ad6YGi-*<,p+$-9*JqNW4&bY^U[pIo1-
%%Osie"8BK(BS.J-!<'X(Kr(bg'mBcb)mbF+\!=^]EUIZ-PLP6dXLoG![I"GX,=dcmDc<Hs/Jk;V#"^u7',H`(NiW_L;u_9OD96<_
%&I;m^E):/G.T`:!C2lilP83%J,S)BIN(_#bHi.i+)#`g8!)%ERg&aJI=R435PMY"0S1k7^Z\:>qE[U$\e8A*s5PE)k!gh^/!UW$g
%%[QoUWQYr_jkOD-2d"5K!%P<;/!&hc/)Ge%4;1kV<[0,)>FsO:qNB3$$;7&fR&H:3r?+HS%pPji@AIqnk%il?&7OPX7:'@+3o/3)
%?oVD,K@g1WhbZ#2_@u;j=a3;0`Z-rA:oL\!i,*]"88)kE[L.d:6K!Kn/)jcSLoVT&-<H)T:3ggg+O$+7%=HC22Alf^q6:;61I%C7
%T*IfN"Ub?"&8fj:C$)o+EBn,9]OqF3%8KE*Hjr!^(99GBKc(F0_oM$.:F,B/7nGdsSZq<7T4-E@mQFtk9-,l;S,5q*@D0`%Mu?Yr
%')J:mCFZbLPhnb>(J4P7j6F:M$?/u[3D!g\KhQm$R$].#:CkS_cS@rX!,Me8?)rF@GQgUC&8g<A^PcUB\X?";^4+":o!+\N,^lp\
%I+58Y\[NQ)4IZ$k_T!TgUCQs'D-Ecd;K'1kO;TV6PkuRjC0l]XNM,&Y8dUWpf<XgD(YVR)1r^KQ^!LEI=:d:)-K.fH>JdB3oisrn
%:MR@0!d+.)UORhR'N$31e":t,gX3FFOo\Pr%=n3'..EfCJ=4`'?<Mcukk@rLTr]9A6drX4U-Pl^JQHu[#$a*'_#0D30rt),a&ZU*
%_E3@tkk)fY(rm<5;4SoMNlIF-1!4RtNb8hG&caMF@M6cr0@sGF-<<DqN9R/"ka=bs6ouI\XsP/kgFf3%D#>R&cfA-V>N(\]!;_Dl
%G/46O`>_Ac'RZ/jT+WVQS&D7L8#VjT+(?n`DT+JCj0K8t?@g)#7t#sEYIn[?3Xp0(LjO(kH@V$Wp@Kma%'J+"]UO1<95u>`>$[>m
%PR'dX4gml/3g-0r^$Gk&<k*"0[nV"F%57(\>[Q;.h.2kV$^3m]VKW9Si[TRN/0A,&go?'E\uN;T:P*Z1CuT>C4@I&B\j<Z:Mi`di
%7*$t;V-pWcejFm#bdS`;K5Nnd8lGcebI%1gJ[sGm:Or%nbP-WG5;Lj66,D52'c:@+hqht*`c%j2'nkX*rt+WAo8q^>H7G4;Y_&$4
%M=%\p=N#A@47G*X3csm4PkOG0/0e#fefhouOtaZM]g0=0i%2YZ19NB@3+^8@Zk=r"G3Q&OP76F-'W9`8>GNH"NAJ-LoL0r8DuX<&
%`)anrCQ,'_cCfj)&^gcee<F-,4%=!`J7p.Da%]9c9usB0(Yo!]+*JOQEE8B\m4[TY%[O:jEjUmB@oUPu^4WWNF"XX.nXT:=9ti5*
%A7hSlTe0HY#iA?H8[n%r!)rt4j:"gY3;\%jZDW\,J&UOe<l,#_0:q;#3"G&/ZVKGUYBmlHmL<Bj%G63og5s"&TR/>#Y.31(=$kN7
%/DsC3j61,koG:fLZ:J:5oA1Bc7T;dQI2S=<H`-T6EcZN\A%H.lpfPTakUA]3&bUghn!JCnd<NsLTj0%iTV$*f,?(h,d$\WlLHG=E
%=+9j/"M?O"T@lm**$&DkZc104q@*f/Ko3_%=Q]0o"6_oMatVL_=bmQ4H`?n?DHNko:Tq^Qq$&G3Lb,p_IL?fp!?(#/b%!jTK.=DN
%NF2cP[ma&e.cLZ+08O0XdQ<&:!,2)6$h5g6,Xi*C5.$Q`mk&]"a>=u3:1usrSo#C)HaZ&a1(<Rf6oTk)&r`RR#:h^g.b4Vl]eF=I
%frek*D.-Pr)O>QXOH:Njdun@g,e=:rpTm7G9q1gTaXf+!Ji;t>kUU?-X[3-ZF6qo.Z#p*Y,"2iONI<sR9";!^3E[=H_+a!G*g&q[
%)qJDkXGH&p0@9*4,8<J8s.8YA'6h,=qCd8t(cnDhoj[pQ^Qqr+ZT$i+cAWm!<DcM-d@HX*AW:Z>LqBO%h]014(O8gACB,uRY!%;>
%)6#c;psj[EJ&&I3_Vq4;0EYQ!;[&+$Q?5:NV*3['HY!G+WtWKCL*qU9`VOV6'p/1HZPfR+YIRDSY/P/t"W3'YLcQ8fM7QC!Ni!]:
%E/[kK0&j`AYlg8qQbAdK$^G\A2C+e)B$L2V?%hG_M]q#A`H<A)d7*a6]UT#T0N0Ri!i`s0Lsr$ZZZ83N>8Gq7$a=4030!a9m5daq
%LcPe*W'5A$:GKE0V-]PX=ib'&'/F^'5ebu5&&&raQ:3_o&.0*4$ddhSecH@4`%D*:p#bfpDR<sph.R/m%jg"lCqZ_&%8TBnU5+&,
%KT$KCL=c\f'1-D3^oJNJqB32V?;!GA*-$aGA"4<:<!V3Z$rbQ&W^.@$IsEcmM=B/OgOY20@Vcnf$fS@0TOJ7MlV*s?=P/O^AA-BV
%q1/:'$(;3E1L?i>QP%1>#^eW*Ldi!X6U@NTW^Hit<IOu80iJKT*C>He*I'sPG@$XSEqf^)egd[b"`UN8b_YPMY,LbB?/:D!_:K8#
%p)G(JW8)JcY)#pX6=W4l7,I4`?(6/QIN?79"YV#'(@,1]/)g($pUZu4!Vp*/d&%gNHbC8W7/huQGC[C*E-tlhY-Q)9YaFe%nFfX@
%@#lci@EB`=@ufamYg5Vj[lC*ZHbtYmJH1RPbG"3FF6OIXSQ[UZ^S\>&<7-$n@APGNBdCs0QssY/%dD:ZKicplmdPauos08`9kN6f
%jR+>qSk?0o".,`mL]oET`j.O3/(&dGV!dm.'YG(ebDdi^h*kt^%#Y$D7]<=:Ja00#-PW,u:s>TXlr4&N0:\o^DWo!50Cr`f!Qpsh
%&Fn+e7NBU*EVT[.E*E_56&'8c`?r2Eo#E)]lV]o+`QFeBAB,'#2jK&Z`VuL0g"h2hKV^!Ue]^UV"%X8i@2Sh[3%,uNfO%S>%-bl)
%JX6hrE:TJhnBH!V$R#LZO7AMFd'lt#JA-WGT9TZW$SMKF'HdG@pe/b<A9($[*N+Y-9K=e&%rOg2KhNK;X<X/,i8tWp@3BHSFj1Cu
%Ifre>(tEiO/.bF3?V70.\63('A%s&E]gi'Xc4s\E!_\?G;uLVK4LJtLTG=m0;c!^^04%l)g!+<f_X<KmK^J=%cJB-+jW&ndSBZR4
%Z-j2pIklY[S'33*$dr^0Ah_2Len3`?0eTG#`XnBI$UmT2.@9Ctm5Lb+K)Ogq=A']-$NM9W#5sVWKXVc6f9"'ErDo*NQq)XX.\!S0
%:`%2hkH-J?68FI"qSg]LF<2BKAIm/@CZ$56<2lGFX(Xp5DKb]a_.N39]C0oDD5#1rU0JLDkG^j8RaY>7H>l>:NOS]c@/AX.CGXn_
%E5SDjbQ3(tTD[5B+;PY1>@#*?@_@f>->5%7gOn(q+T9M)TfL9tGp*&UO4lu9rot6<0ZFc5[R]W/IMC3>lD%=H[UVa3KL"k^D-!B?
%&T[3H1('.c)23tLn*+367#1h%:FLd#Mtf7Um#N0WT+0'CN!O^0%NmmTU&HB.o6DCDR_-JmGV';$:k[(F98ZV"+`"'pWb^KARq&_X
%``_O'Z5J=IJ5bXP/s()u3[kqVNG97'4+PXYDe1U2*"bta#69dGTRe.i:0fI\dmF:?k_D2#Q?0-iW:E@5>OH64:/#(3R*L<1&S[M=
%N:=/]L$G=QWNg@e%S/O[pWId4F&EZ&?3O#m9.2\s#Rq"(BnoiA^eVILMR:66`lLgAQAd9B_aVsD!3T<UEJA<fd"S#"<^gDQ]WaMs
%1o'[U0lh+YF;>qPp$7:NIY_(XK?K+mc]A6o/mgCUFu:SM5;J[/;9[-,m&8!YS5<P96HZ;2!TUa83"X[.!=3fD&G)8ug]9t$[imZ`
%*21KVPS*%_[?O1kL3tSSO%KG//\KIb3YbMC_\T4X\.QLWSsCK]_H&ZXTP1AGc\'EW_R&4@5HQ=AJa:6Y32prD)fS7!/-79b<^R1I
%MA$Y)'+TS*%F)ifCI,7!oT=)4b@d.&mi6K$90V:^3(,Lu%73BSq:#M-Aq,&?9O__D-_]n&iLl.$GRLmHas*Lgp4s<^^HkGqG%dg"
%L0)<Mg-(-d;UQ+dX-p6"CmP5(D&3rQ2>mK*[SPp0H=eQ5Zs[*X'aKPIj^Imei6`OJ!5U:Vcl(0,."M$^N[Y0HLIU@,5gf>Y3e2-(
%.`ZH@5TP'ZGn-]3EcjAT7\rR.(EnhIhYM1$RIn's],GeoW4[8W==qfBIiQ;]?OGPV<aq&=78osRF7"abTf(dMW):UL+aIJcWk(?*
%EF\m"8^e!hj(sXP2dK"`92_sb)n&U1kSQ=H@tW%LfEr\MQQnp_=m#p".1.0cm4'n1#1R[Y1)=HC==_df*75W]QiO2)atGb;h7C&g
%BJ)3DQd'9T2/I32\-0YL]YtQ3Y-bR_VcW8e@E&e3hdDtcUl-#W[V8FLJI?<cZWPKof!;'mFMUh\YatgrMJ83LQPZHRlH:VDei6j$
%n_0]k+c16)c-MEI1a.]Fcj2Eh]?!1hMRaqD_TuXd7"sMFs)Z`Y+KS4B91ArHReJL?9*_:eKCKbA#SP]#JXFQ4`tL0>KE12oC`GKM
%5UbodJ2/T2((iZ/64\Vi[[F]MPH;nVU8\8k_flE7M(/rEMPPd&rd,6Z_"IV1DG.8\SmEaTKQdU:7JG#b:T)CZ:?M+2cP\-Vg93*N
%+M]9<<utZ+Q6S)G6imIXI1Med*cS1)6mlbmFh`OUOR2>"Y5V51\,ld$0Q%aF.CK&]?gE;fop[30Wg@[>=]MEQ'c@;F4!7WeP*uQJ
%W(8M99"=67ZYQKC01$,cWbpW4Jut,ggr#8<AP$=)2h7d;>CF&Db+@/#d;Bm]+[\B/gatSdP)WGX')d#=M22#Y,<WJBM]+M*(KOS@
%mjrHgN/Kd9$6<.)"%'%_>JTb`W6C(f'AKIakWjo%2:=jO;H8&a'O#4F8N#1[HQ9H+F<+lnQ^n_D,"-8TeA:[5BPC#nZ]e7P<'hWK
%"U.@Kl9tH>TfGJuTq'W;%)q5#^H`Lq(:99*+]V$Fgoe1UmLXkTeQWQ[K4:4Ol@O2s6bZoIm<B,iLi`X%F0r]$Pe/h#6=;gJWja1e
%:L1\$"l$^XJQe>?$!9b>`P/W!TI?VR25&PQ&@<(n32\%-H*:sBP34GEMfS#Q)#\><"Q3(@9&!e'%?iHD!nM)ga/)4,QH;_CpFMFH
%KCX-PK/mtiFfV"9!&Mus\o@5(6ERlhC9g'1$cc-%J1?G=m2<0;Y'%Fi](6Q6Kf9[gD8k#0?,Ec>\n[gMXBmI`S9rm//W*g]8NV/Y
%Ag64LpE(U'Qoh27XGa-$Ghj,VRK5)USbGi:.2<#:nVW<K2Qis+1O_m:0cmh*bZ*2?'J%OW!BVd;eH5&3L:r*q*YFZFM*:gQ%FmPY
%<^pSdJ%n6$:Eojn'2<=&QFQd/cB<HTb=-3P,I4r?CHLhg8;`"0BI%uCqoBF"?OdFpPtXEHeXqum)n7d?:3D%&PIrhsX7GK1%#1)d
%00CL,bUNZ0R]R?idA?f)5#,X)fI-r'S.S4MmI;D@?e#N\A39Z5X!2'D!(d`g,L94/#,0C;#Si@CqfSQ>6P^HK%&Oi0O,,=;BGO2&
%?oNl%!YBrG+J$TEpIg&Dj#B`^^f4$C(.m+J4,aTA`fL5_b6Vj#!r7-dMg7lP$D^1P*4T`p+cHRo*g!"P(i*tAl!Mc?84Aar0<4M'
%_KpiQn5;\DbQ-XTjP+;coY*RtBe?$Z`0ag_6t$mY31/KCZ1c6bMIpaY_oN]fpl^N%U3T)ULES_'7.7901lDC/`-O4a1BJ`],Jak@
%(UTD@b%<rb"#&rVC`Rd9K$Ubggqb]g:hZ\6;_ZLlqJ+pc0B`oa]&/K]O(P.oV3i3F<g`tDF<hYgWJ<S@*hH'nScYCBSWcJ@gU#%,
%j]4:%G2e6[QNV"`M2_UNK9pK;1f(JO=\-=S)KKSSlcd/h,4!J@L<.Q?']7&&Ik^K&JXENYf\tg^]6)NSUJ4QMZU^oerpcX=P_?ng
%DV/ha^B>D7[;Ro";#%^AWi$>5h9:I$K&j8P1*PY"M5.Vgjq8<lH\D?kg.Gsn+YW5b<WYa@gddS64Js,!E.mfa:6F*O9a$+tIgSB?
%FPk@5&dd73(,>qJoj2B(#2CE4SNatNL`g[Nd^k$<:#HoqS#Y0c<&Yd&1Pk;a#X\9(_W[<7Vk*1lbcf6n"ur2Hm?-(RYn.BQ/Hni:
%;kD)D"&^LS/I_JgN.HaV/Ms>Y6<\>ZT6#T"]eh"F)hQ2YV4QC!.7K)G@]S-kH2!XWilk?A7<TR@"I@p*Pe9D*@Xc3YBA*.qf.V,B
%c%kZ6%>9$BSjb#$!34imk'i/#0@?aH3JKN,ltf.Gk>i#'XTli+r6L-5O+l9b"=3U1<B#m*;r&.#)[K39(X4`"FqL!92dD,aX62MM
%d]&^t,jj<r>7Eio1HFDpRb?1[:pA('(H\XDW/@Y'WnFm(k0M2!.E_-A+QAr6/s0T_\g<`4d!a@7Nj,:C9^l-=O@3t9>*+J]Rm+3i
%W#+3*o)q##\;1kZL?`m_Ot(N(&h^;f(+Kq+a%s^Pnlc40mAf!YU+X(teUTtOifi,m5.Xm`l6t._6K;qffkH87bR9,oA@b^DAHiiD
%\1ea4ok*O0D=gE250'.mWIh?j;&mI$%U*tae&F`e[@G%ib^FgJ1<jHJ4\#tfBiN+bC7b61"GH;H<4eqfd5MsT3`?@jmKPJ@_->m#
%d$tST]2*L\!?7@^^pkp)XQJ%f"4Kq3Q3YN,GF4Vp[4ecGWUaFo)I,d=f%G-iNa+3[6<(5KgXOfG"=4HWiNbApk27_.O+Ym=*_I.4
%.Udpp")Q$KSlAP!8X8E)EmV%kR$#Fd,H7EDmr6W51<PedpBgCm[<De3b[l\S`RbuA[<(A%V=Im2%Q+?VPpoX*L%SG?@G*7]QMmj&
%]tj]M[4=*ZZik2kB3Z8G;6G;"Q1@m<NZF4QZ%M_)76"at6nV8!k"naA?L#);j6tUJq4pN-?;[Q13RY(GNFZ,/<eijFN0rQHU#pac
%jGHQ226jf!6pB7K8k&iF2"b;0+=4H@\:2"pFeCUii((i9h+"/Q@Yi/?F]M9k!R8S#0cbK_%_#h8\1t%H/$3M^`#9EipVn%LK]::?
%Z"QN-.,m\0%s<Cr7R7%+,.JV!oK!24(Q-0PYm+7?]Q[m@_N>8p.$W>J0a4+6g^eX]j`cU!,QV7+bt>D(?:t?-FdaN^.dZg2nrTFX
%".:BHK)ZDHn</!-[UKkf8(`/JBZukY8Nkfr7OWFV2/<hndKU7t\FUddP-mmA!'(Yi&i'WgLDLV+DoB;;\1$2[QiF8hk26.(QTtNK
%msaq:IfHNQf)P>kpsh$HpCDcPgY_pq?[_gp^\Qu8^\mNgIeh`*ro1,2rk![TqV0p*\)73DaPsZ8jo9ugIeWMRIfJ_A^d$`GgRnB!
%?iQ\ASZasU,i^,k^]+/7bK#*pcVf5l+4QAOTO?$Zs"E*HJ'i6@IJt&_qB)>hJ+Mt:s6uN8rjNsmSGKu9q(HNGhhZ(B@9aXE48GP`
%Mm,%kB9_GMG79Hk7-2T!j_6:qW7;eFf7=jAV@]_B;-;7QX%VkdX%b]#NHh<WG*p%cVskIp$8nLalJ.Z(j@(G*-ks7j1#2Vl,a5rS
%DUgg/6e*fcYU?V_aj-r=JTZjY$SU'`C,1bk"<9g>-mX2HcDK4F?)u.e+s\4Z:G+IY\-"ghj<7+H(e5^kaq7YeD.Ub/)$.:)YA@ZD
%g`QAM#"+XRYAmL?4tsSH[N0Q4MI9E;"oQ0@=aAg*0II.s?=Fk?ajR4.GiJk<<!rt,3WbRu)\hi&<Tk%#E^.a3)UV%d.(VOEh3EY6
%:fMWe<s[_<)%"t%/C.^!EQAV5`\K44],CL!G[qEED3gu\$^g5gGDN/N-n@9!cUps","`c+P=Gb&1C(NT9\!HBYeSh16ahDLU6f[=
%^1(2VKh9\T!5cH=/rDKeJ;584\[_5f9oB91K#iad6I*SME_NAh)0Xld^s:A!kii7c?jAe2nd)jMVkZPTWdaT!p,4L+lJ=LZR6S'T
%+E*coi2fU%)'-3HF68U;d,m5M!`8;H6F]2b"0WPMH>[-c^AI\*X8:V"Lej[0a#O\g2CBgd^Hg)9>VKM@;T,BLjYS-FBeIMq3:Q9@
%V]Erm`%L=#kkpRPc,/g*S`&-KQQ_/I,7LNT%rDps:fe/5L,mHH^2mBaJb0Sh,PJ'TD:kYtLcZ:rf++7]C/A-,Q6]ba[3KLDF!;#@
%r)\GHE1ki9;".X#aMX-;bB.lI^Sd.S7"Hjr_G3*7+]gSrYfM".m32k>_i%0V,K`K<lKKdkLF6#sE->9Rr4SIP[]kQn+E)$=KV4Y0
%VO#G'[)3PCq<3uV6GZl$L*f(!,%g^@\VgTt&HFmp7=YI.:&;C']FSfZq$qon1qL</j9UR'.hg%kU+usZ#:=9baS3R-_=S["'_]&e
%/Q4QG3o$#ndK2p),R7l@>dudpZb+;Ji:VePg`rReRTSVCHh;;1p%f-BAGs.mmI[H;1XS[210jX+Lml`!"nW2qEF#rkM`IB2LOoA_
%bbYlH]M(bJe&=WBJ/Ca1GI%#i-sXq\gajpY[VO0o*loO5;Q%K!HLo&"-?;RKcm5sQ4RA5._Ch88]mO_$I.N]dPDgU,>[@#P$M4:q
%3M14lg7W>&4.N-?kduqS\Lf07s1R?s['[q@XtU0>^ZCK.C]*`b'0V_hM5.]:$INH"Lld?I,CC&[Fq;6WmG``+N,OU05DW%SE]grS
%P9Ep4!=d9^)])Y!Hj,/%;O:iY9herkY&_\mZEpU?OEYP,35h8Wc-f*eKkm<6/4/`^ie@fJ9Ok?!n/NI2Xtm^*9U=&%#(a!+OL6];
%K\_'5g.c`g7Eael73oe!k:Z9j\5B@qeFKL@\(rpOfcl.JP!m5V[@@lmCi:Kh"3OQu/YZ3M$O4K#1a<Wm)1+m*l?;jc:_r<Wo"p*q
%nd?>m:seIJ>AjOG&Xb4$mtOMj)<=kDLPJtK9NHL1[hZmF!A3Od=pVO%<&^49F29NRo`[#tneQ\CLfk=;'ul1la;'9[[+$(JQ!SH3
%<td6C`*APok\\/C._*>JB3YbU4L,jjR`&l>V?3u,!c*_Bd;<@E/K7L&-psS@)e$VnbWE0jd,SB<#EpG,,e-!dPE-u<C[GN+)AbMd
%c3$[:OE!si6$Qg:Js+2U=NYsUnj/+Y/$FNE9IgCaQ+oWNJ5h.JM@9j##>9,0r6%jn#o0M[V'2<=<@MrtlK0m:2.r+aN(a.3;(0A>
%,`.8KJLmK0AH8I_6\TbY4YAt@Q:)d&Wrigm,&Eh2485+FmN=j?m%m,YE!7M87W))Q?u;kuGF<(XP<`>TeuO(_aBB8h?A@H\n=:q,
%cpL?53!tumi8V3I3$jZ]6s?#!Ft7A[U"f^4/1.pB_j)>YZ7>(,c]pN-8S4Ej(MGGYKTPi<&Gfq:J::da:h[kr]Tpcs++@=75MTV#
%n03X!c371%>p`;2<pkX#d'MF@&=Bt%rF#']ak?fUJ:Kp'0g\i8EWdI5<ihGg?tV?0ZZ%74iI3V6/5gqeEk!AP,tn<UZh":SVUdX:
%VAAq3aI>`-f"S*kR'Go`J:d+#@OXeXo&7,4dj_iZOu[s:#)J8F:$FEO&M!/5;9e^\p5MK4F]g]8C2L@M<L5pQ%A]cE>2<dg]8'sO
%S%iN5B.N>"1Er-@ogBu(@(skM`:D_/\Oo\:07bEoW5Xcl;aPf,,paZ.<k!Mu&JV0i*M28Tfl1CnZo6*Ba]*JOX?NdW=q0i[1:UF-
%D1=t>ObIP6##nP%RZXO!ldfnX*,<uV0G"H9Y\RjH>D!GqMV[FBn%-$W(1jCmN^[#e_`1$B/uFn-PAm(-;fQG[k]H,a!TekKX,>,P
%:;;_8GeKaiTN[g7i!".,TbuV.Y*^+-Br.Y\AgZO,/l7\!3qp%ZUUPn'@X5X%o5p<Mr]SXWp4m:b2A@94Fe?"^4,%slY`r/iSO56S
%I$/G/)*n=LZ/Z2TR6-9Od722R=_[\uSl/he>b_JAAh*AL$ZJJXL'('LA1=KU'Z#Z2]ufDKZHKKEC.Pqg%=f0efMG07FODgu:nt"'
%mcO$R7j79:N*(0V%DiV9+p5W\OT?\D\fkN"QWjk/,u?f]6\h(K_k!AJlLeT,?E4t:%b%N+70V'iSPgZ7\RCu/AjPAI#3/n3T\O3m
%q?Y_2N,^kqc*S%fgZobdbg5Lq8g@^"5?]d`q;*X&jT1&faFnO,GVWV1;hei,95W[.L_s<LXH0MKYL+K"]'nA)iO_MP\#G;7Nm<r_
%!\+;s/>M"*?<e';o;d^*T(8_hH3fLeYGT_=P?#:4!MN-W)'QMp=.m]<RCP[uf^Bb#fFO=pr9(%O!'8%uZ:3S@j\nM/:222uPFS=p
%9A]BmZE,D=WXCF4NF#^pRp5u1J-]@8:H])-`mCM6RB7/eTM)^!gY!*-&dK_@M1N/GRno_!L';o5i?$>5Y"=_7-Gro?L57^8UKEpd
%Z?5DtCO81u-6ns$/>\5F9.Pldp,^b:B87r?1t+Tq_c-g;p+/*s#1)j\d5>\lhn/*O\0uU!I,P.M(gaX:Rh>+`$&pSpZT62o8p,hq
%^;s0g06@F!W9$&K"Sa=52rVs1=ps8H>j5EZ$`2JG&2kG'(;EH`^bI0RPLTlL76Z`k9#Or>qMedh>J6CJLU5Vk/j%XQ[T80@XLhI2
%0r1f;eX.9L>Ri--.8@>%<9?>^!to:87,W'dF&XTN*)n#=SpHfbhIN(42r8D5%$bs\g!K"lW$CDE7FQT`POR7bOR!1?Cqn4O9&Q)Y
%ZB!3[K#nlQ!LdJMg1`rZ`dTWFqE<n_O?5i@gFD$7ahk`l97>^0*Q`7ajT20p?4mi%E+`b');2k(6PT.)AM,5J,eAmo#*2U9MO<K3
%c5*OTm/m]@Mtih9X_K=UH)VLPJ]T+r/T,?X(UBeO&.\@k+oRsp1;hu)bm'9-JFYKX1k+R`(Kh)t6O?miEe-SKVHJu,"IuC&gO,H0
%k<N]]%.M0u2W(:e.Q#<P+bUgoagWEP16AZ']G,0AEXH\&#8XS`.jQFN4HXJ<`Z+?/J6::6/LcTk#hXt;ko9:!g^LaI9gN[c*X0T9
%Y/12Pg6'$,oXm-`DEJG+AO^>OSN5bQ<DRii.Np"&T<Q1l44"Ws9TdNM*H'QnhMGE-25TI+QK<$eq/.oh,kp'KArSpk["GWQ9R]G4
%9G6W,_?;J\D+3VAm)NF41rUM;.[MlHKXNnQ^2]VuMgT#B#)RP-I.)0%`.`f\_iC4+U/L'!e#f(p#[<7aN:S[EpYo;T8-=UFZ[i[F
%'o/W*CVPhD@TFt5Xah.8:3F0^8NZ?S=*H:\RpORe4/ei/no7AUo/J:lFs%iab"j.%-ucf"o.$9)%e/]V1G>n,]7KIm6r3pWT_(<^
%J=BYp1DWq8joU]AOiX%aZ4%D.+b$:#<-reFT@0iK>?\l;b-@q'OEIG#lm"GVpP&7D=i2+sF<'?l%a((a1bXCs-E(#8FZRW7FlL$2
%7*QiL[;aSK2Hfi&P@$=j[W)o"dr5fri_d:fpp+hrG.Uib_\R?n%bX_cU->FcDW!Vdr,dEf@O:b(E25lWc%'di;QU`c_#/hk"h($d
%,oA2!VGuM&#jMOSR6!L_$o-ENJZ:K]5bc&-+:XIog/ZX4<8/r!JpO($](iI*:s(._6-lSI3aL>bGZUpo.h^:#0nfVD/=W53nFV4[
%?%\Z&8n]t2aLh[#WdDLLPD((_F#%YOY5f>nl)>FS.T>[\8mpK=f3p.9l#;h)g<[W&mM&'e3#NpGa0Y-_/)eVo4T!+m#u]HXO@P<4
%387bUA"%t6_r`X8=le`X\qhPIV,H97$)a,oe^^Ya%;Ytkm_;SBR;<>^*76fj&()P7[jq@e5@cI8Jp`pOFp:qMr.SdG_g>fCrUr'c
%.\)&e,L0XW2d?&kd)d[`N]pb7M6W:YpqGR6cB\s>m@f#(C;DfSWc/((V;/dUE*pP6!M`WUp(S_+_)l$SoHL%"AR5hm8N\5rAHt4P
%P2TllWYT[m><UjDVf5L1"kcYa(]$%a!^Nf^QJBX\,i<-(go3\UN!hp#G91?ti=YT06iG[DohYZdV)FaM9"93SK430)j0&80UQMQu
%Y!^Vmf7F,o!EsO$Z?E-N2LgBceK5KL[la7;_Q'=S'X9M5)XLWJ[lf"T3+.MfH/Yj/*Fs])a.$Hui3ph<]C8QAn5M=e"&o,LZF[A,
%#TR.2dnENAjBt_Kcf+ROBk0_r[ed:P5:eWPk%kI&lOYA22/kqM[R4XG?5d\E@9n2d=dhZg+V(qq7"e#*"=P9L`r1eI%]Iqll@oj%
%@]`ItOnE:)Q`;Po?EcO7\)As?MC6,NM])3P_A4qe)I(G4-Gbm>6[C26&rBhr)c,,Ad11*6/,VQJlDeg&6a"$+Qsg#h?0#ga''_K#
%T#hHRA7_o2kcOCgH`[>=@k_7Ff2;^&[^8KnT\&M;W_$dq.OH+dj2-kVB"H7&'&g545/nA?j-%4O>G2A!Cs%l0kZbnDji0Q#M)tlp
%66,\o@pp<aQJ^B.CsgB;hAs^-[)o4]9MKtb'+$;7KnCa^;J[9kW54n"%48t$hG/FGMcXPBUEFlPDp]/8fKW.!YqPs2]$j0c1+pUe
%kEBG1^!1dV$78K2_GNmWKsFQ2'\lb$,nc(t>t-]@q]?mPP(E^l`BRlDgItf%$ViQ^f9*OMVo@0\9J.[r1lm7[DKKN9GTRLipBW;G
%H;<`=mY9hHJ'pW!4aMd[&A-'Hk-4$Z$WTZkV=aW$fN?kSX@ja2OUP'I@L4)6a@Y-d")(aOl+M93&SfGgiBV!?IcHQK;J^:G=4"1(
%KKp*WUAfQmRMouGg)PaA'$t)31?^X\/Vqq9g#_&(4\2@lm-XUq[>3G:BCaoMXQAEZ0G5Uu9,LNhH)kSWQrLT?7O2$sh#U<QKA418
%l?2AWQj2UA'LD>s#mHWCFmk%D[*U.NG8pmnWaR^RSDESr3`T;2R5I+Z]7q#PCi_SHB)%BEoR:i[f,l;_A=Npg@d06s?&N3e_hU$j
%DgV+jB)@h;.%BmNEgPJh[j[S&&;!M!JoQ-qnj8PVJI$qXjd6l"WK(1.N`g0cUSaAu7BQA>"%I/PVZJ\&7h+L%A>9fUgnibc>nQOE
%HcNE!Yo7C5,Pm-%rm[G1CC^$[K"GeNLfTKB>,;U+cm^Gp"V/D%@WfNsnWY+l5:`bcdOhFccrS^,>@bnu6rREs&E*,](Gnc/')ikC
%9V%bf4W.aE#U"5CW-\(ph/(r8ato)n=b@*(-G9NV%s7XtH#d@U9<F:ir5K6Il=`9k5bth.n..7T&q"LT5H1f?K91JK],B*uBJ\Np
%oEFV)f(@s/b6CTqZS0\brT-T(MubuMgTUZa.b7'O,#hrD#"$D\^1X]?poZcjGo!+J0G;oULZJp?q0fl\_8\=N9Htc0+'`dti(?fn
%%`I0k0oH@:08r"NbWBp9<]^q]^2kjOld8<[ICfgHYQbV`)QtZs(bfg0XZW-f,Gpr$[eIr:lA@)r"EbadnY[(3c9D3R,b;I=LK\W%
%_qR7u&@\,FV6&7Nm+a-`*s)@QH+&2l-NQN3E</$TpoWND]QB>(!nnf6*Wb:6oVZZ_a&Bq)N!r&.I2b2d<eS^[S30(*V]%#q=15?<
%faV,c_pRHS4!*&!59Q,,@;n,%<N`rNX1\_tEY%^9f=)n])&+N\,J`Gt8C5a5Au9Io&E<9V&Fm/Us'BdaI+1iP;\d[Jhf*Uc;I952
%37Nu?HQ*tu>qN]6,.n^4/+/],IU$/M/9;H#4j1Yf6T1JhaTPY.MlB,(:<b'4MP0sCn\>\]MaR\,-$"pcCc!'0E@iD=5S@3#$;70]
%VdYJPC2:5=`=NaOq4p>YYi=fJ;L3`nfao%t1mo[(hdWt;;b<^QVB;gMc:crNb)_E"&hjdS:)-_ido@YufC*.HFn(AeQ6I\lZ-AGn
%.-O^+81^L*8u<A:'HI4eXZB:+<>BV02q4!f,?[0BOinHN!8a#%.gI6(qX1=k7<e,Wf\,n2-rIRpgK>N3R:2_GqT-K^SPpX4$'A8E
%2uupAU(3shVbitL02l/YMMa1!4gk^"X35Z+937DqiT.isoE_'rE%#2A8e+OLnWo&lf][q^#5/&ADKK=6%(.%l\Vb&N_1>S+RH\t(
%N5VN"R*^_:2(U[>BD+mGFbeWpc%6irhB_D!]6u!b<'s<KL\ld%adYjURpB*`[sMP'KGA?AO1&fnCI3Es!nC<VJiWECctbAlZ:Sjb
%7NNo0mH/6e6:`3R_Ps]g"].Q]n(?VHp&K!>7X6UQPa)!oI?pZS=s%;f+=,-c#M@E7%`*=fAH]uX\of<A@$GVH[5#lTm^`.Fe;4*n
%+SO]D@iB5ILDO$r]oq`'WUnWg#o3#P]jVQ9^=8"4LfYt#r07jG+=4!u"ratrk7\Om;5:]:%6)AGa"ZX(0o;u#;?sGL3eBL5rAoK0
%bHVCi'orpYUr&^Je%M:J%C_\j%e,@E9",M$(fBM4"VW`\?/'qG^Y=$&1H0]7#gopIeBJ_Ch:nu!'2UZF_&j(cb?cKbYO)mI7W)\I
%2rbTJUN0%CbA"[IB<RD7,?bac:K'=]Q'cGDk;q8iXS!;]*!;]/Jtl04R.YF0SPrEbE)YhV/oMN]@8ir'Ea+s7<*]P2'=Aa5KZ7O(
%W$:`,BI&9!SdSogG*I]NNHZ'`6db6Z=3g.lfm(Iu6D=7LP)%U5h[[mrTD"3.89[^Z6#`g?!tQ4-@=+$KLWfj`n;[*o-k?"`mi\KN
%`V\*^3h:FVXMj40:#YY]0a;Vf=RKD5V;6TfFEnRhkCb(]&RY(U#bpbJ-SHnbp66ViS$lgWV?=bIg+>5'qpM7u`.R(I:@QCoDI\rp
%:2Hq^AnsO3n^1#pb/LPCnVB*UQ31'S,CO=>5?rEmqbZ^,gBO"8?ad2t^?hUC_ZA!#3I3hD8ukmdYSV<Pm;B]ZeM77O47SUKNYE-&
%;CE+aRZ%Ym&&JibefMEd=:r$@A`OVa_"c7D-L7H`N'B7dVuBpE$"?*ip?rs#>*GEad?Wo+EX;X!L$VTd*Ir)t?gBj<5W27IQ#V<%
%^Y'>VCcYar$`SFIZL$I"m/j\rhQbPknlHhJ\+g,7W(65fDoHQB\)F)q%Q=[:BE-*PEiC(oaRZ3=''@@`!s"^'GEhNiQl5Pf?\`c$
%p@,Bs%8',\F5@V57^.iIE>P(?4BFVd#,f2&=;Yn.5.g!tI4-4S[hga1dTqPJ/R?t0b@UaU;.8jU?o%SX=L+[eqUu-6\VI$W47Nc2
%i:".G)2b&41&*k<2?M0URY_)f\49p-)V^dEYW!JeFX'%P+*!2fYa"Ji8e5sd1]-(JNNL2&!,38`Y3'8:*.bOPS/CHinN&p$m)Ll8
%hNpG=J33s-VsO"%6E^&Vg=IOA3SO819*;lP;+JfZRNT83g7Bf@qKJgmeJJSCoaSTsTRDMU^j+U\k1ul[\Q#P]O3e6I>l<.^LU.?t
%_p4?&,O1Rd4Cg=L"$=3%'OO-0i":8h#(ahOL)(PE1)ZQ7.3RHS]aW2io:C/f>W)=1N)A?5@Y]%_''ktj#atJlHh;0W,-`%RU(:?"
%DfLtMEJdB2k`Za1CLZACIm)?GL!ltBL%Nk_&H`!Lf*N.`1=(49;E_gJgXX48,h%`lB"E<`/TnTJCg!RafJ710B7QKi?:"=QjL9!W
%_Q8$%h)'d>!:A#m5ao[b&M=a;TV<^Tl*d)aTUGjU[!nD&q/`70+<G[Gn%6CYHZ8+d<c].]\R:6`_faXfl7TAe!o?#;PPd-!kp;Q+
%_dRo!p(lGrpiTZcr;trV(*;ok+PfQ4qBJ$^W8/%L#q,Y)Q&5NcX@@[XWDUO/o-^d]A_=;<'!"k3rRprF7K8SWC&_]#0g>Sp!@@;$
%6[en4g.4H-NV#^('+g)?WujM43rOICZ)YIYTpPgg<"TA9Slm.WN"TCF&\r.mFj_dj',E1g6H#mj/ZpDJ?j=6qG#3poqG"d))7A?U
%5^-`74k6c&esT(sk>m4,3tI#CZ,:FjL2WY9&n$-pZ43mU=#:,,iF*e1imN)X>7S$L/d=i#EYF90mQa)O@,3iVL9"=6+B=,?%0AGO
%*j/7KIEFM-gq.<AB9d/QAQpXQ(/-DEIW\#/O=Btj3@[84>1_TJrH1+J*`b"m*9HH%/n^1dk>e%uWqidUW2aV9KYnBUKEp#m*@QL!
%s#qY=9->XQ6`PB><Y/*+L0"qd3t?ohX8EN8l=/V(ou63q6n_R1$j]jPc]0!e':JT;n^;aZ3&5W%JjH&.r&jjpHTAQHN:qM\'n-0a
%+l0@_p'L=CN3r9kdE&:`D%5V]qE.mgd?Gid^+1]kS1bf>VQ*.?3eYHFO&G,:hDu]calO724%0Yo(TSc_/Gp%Vaa?n<W)8%>U53"?
%6F^A&cF"Q.i*9o7VR)V0.1T&?(V\A\=Q3eZqd'0)H`LP\6o6X`Yo+3@XQ`*2@-hj4_*n?t'uhgUTH7"1&i]=iYu"c04PtqCbb;D.
%3qusDrM3=sDZNgUBC4]%@Rd<ikb4a%?LQ`l8#j$#AO_Ss9+O6X"LF;T.DVlQ]p'ME3')uiMi7,%n:@(0NEH;Ee7qt*ZeN/r2<]cj
%H]1Z=NND&7IiioNj"_P!&Zt2XH:cC7pZP)<h)8M@Dfan","M:&aG7'/3M>o$Rd]`GXF23*$;;&a]Uo4(UMr&M=_=P8inSJC;"Rd_
%m4+eck='?@@<kB)GZUF[oBtUa`W;PTo'RhO8bjZJ_M#E7!kn.5I[X%u\f1ZcDMcio@D7;)@X>#&,YsP1Ib"SjKAIf!*r4$]^kIHY
%*q<.3,FBF9^EJlS\gp5CWZ(/V[(():M2WF*a^WXOF6'<6'Di^_4Q+,GYEa4?He:Es1]]dICILD610#TR9cFo-8bnOk'GG\9FpSgO
%bs/'t(;"+`Q<<$.95(\\\c'liJk$/VG;G'!lUlb,0FEtX`&+^7*oJY/(iONa`kGr9jZT31eLH#_m,c=+KAcfR,t1S\aA1aqG]'fJ
%'bS;+Ur,3'7pWjq%X2l'',R"Z2>)T"YcJ-rNSq_*0dis"s*dDWWP\XYn"`(YZ_.rr-,4MgJuf(c,u@V/NfnHfO=]TK12p-iQ<d=1
%V$(-*3RGMN-.oZj!h)[`pC+nA]YY6RGu%q$Pp-]fSoWDIOg#t=Qt[Hl8#i;g@p!s/PIbSV#]<%&\W-cFq*$<&2DHYS65aM;n-)8[
%_Z^2B=fs*uoin8EY-0\RM2hA=M1Zip.EXp)G(oY2T/@l6_7,6!A<6C)LpO*VT&_4c/H_/M<)n.\hHKaK5c$#N6\@L%=0?-`kaYfD
%_kf)&58_]"k#GsPClOM\iCp!7<QbDF[G$AO+p_V;VKtR`ld%XTP9a3,'iku//=:>Va?M+r2V_2H&XA(%mR$YEf@g4E-3,)*E=lN[
%=4gL/B`6lGl^4fb?5mP`f[.6l7dnmD<0uVW\cA=-27a+h*t2PLflKtYJ`6$'C+RHOl>El=U[p-;A%-^sjC1`Vr(Qmp+YRHnGP+"D
%Y_jk4q710M^=BGZ/<dSl5K)H[I[+aHHcA^gM)Q?m\Gm!f_hV8O.2SinOMo5Vf=uu&LTl*L;F&2LD!HbC"V\Bj\8qcL&A6J67<=)"
%^jPd2&VtE=PPK<eD2RCP^>7Z^/&pQg,$9SHcAnhCa'S2C6SA'6msmTU(\";aAIguNipL-t'cPk!:NnI0a5QFL(?t#h4eeF`:k])I
%\755QTRB:E[.))'<_<T%a-BGJ>ogVc72I;ZcPfUb*_@6IU`WEC%Wo@Q_m</PNsSU.YMRM!p7A'\W5Q,Q5Dsk"qSNe1Y_ZN:/G#Ue
%90NX#U^XIG@7i$mB32Nk%HY:lV!ff/TrC7kS9;tG#U]@%Zb?mZo%P[>`$@/T%s(ciitJc5@"Ql"[7.X-dlsO.%>7H?2LS&ua>\52
%:YbIX86JS_eL>ED[gS#;8/HgKR,BiSPjT`gCgq3KLR8geX(ikabCq9"N<(RA4F(ek:8Tf2!Ts+$qUP]D,[]XmK2K`N=D#_he^@E0
%WA]eeUL9Ba^lAGUAp"5gMrpq4%8oR:p4sC'UXESVjhS5bKL8=DA%?JM)g$J-gV08EeH!+<$!B9=9uBF%SQM@X+5#Rjnq,=geJjrB
%Zt*''Q@DQ+\due:??YMNT9`\[44k3Zpq7(G(c@`ndJV_7rB6MO:R9bkV"p[<T<R;*X;7gO'/>'sTAY>rPR*Y,pkpja=`uXHZC&TP
%35`J5Tn23Xe@Z8<Qfd<"mD!:%6RsV\#Cl!HEu?)VDe?Fg[4l,A8odR'+)#MgblSK/VK,eZfQlW)02jD8&.Ys8)lf"Lnn8t<c=kN4
%J,si.RKB/:=!OYXN6@T?R_%n[8CQ*(!%5Lf*CVabW>RIr`NrU'J=4a=/6%$'q,BskaH4$E6CfT)Pd"oEl4eHTKPoSf;uJbo-`?nr
%/6u;+S1Fo0_Z($9US$Uh^2Zg%:57!i+/T!CBEckJTrLBImNE)!k%D.5.VTMa:;9qNC;i#(GY.7PPuot3#r4B&L2nXS(VkSC"j>EJ
%ao"tQf`jNnCkkCg\8;prU/G"??A7%=;&%Q8,_sZeqU)WDmLK>\1L"J_Q=5OA3,g(O+f$>W)eDl%_(*\N_G$L),h5=I6EYVbnNbQb
%V->@\3PmrUrSh!2ej-=pg4\8Br9jOYD2>:p;-oBm0$rB/p/"4')Edpu^-_U;+6'tg/oJ,Fma96R]eLW\P;s0[O5W2`lERF#KHR_A
%.ho8-5refT0!j_W/!s!+^=Jo8=CN/9\"<t691Ck<X2`qX<`sKJb0/e_S=)N0_S+4461`gf,o_JB"G;AaDl]1B/"=9h4,/-XJEtud
%(iGO3n3p.=0tEcf5Pem@VRYT9%N[^dTIH_K:oE+bAiL.3pQq@2kV>qCQ,I+PF81-NUMES([/&Q26@oT$_[mkbm?#9.8tKs!!Y@"!
%"AZJcD/QNl<SW3iIq)#u?jL8l@h+4W"$(')r:1\ec#!VBdXD"\G56t6oc9')<Wg]#bNM%0]6N4951ZQKd,WZ(@gHbn*.;s(b(U\Y
%%^^FM.jr_mAJ<W\=C"XTqD4U<S^#/WDFfTXD-os48$W+;`Lp8&9R_,nf'Z6A5\gl'.h#6mb>[-4B%G&g?&Ms3cXpF>?EP\'(tDW+
%O!T&KLs,;=^]qd9:?g.RL^d,pYG'^5MhLKKq=G<8-T57!9(SO6\*\b2>"e!1U7"0`)2s0&0M2+o6D5<#'$-sX%$A>&PFaCkUkPsn
%n$Jjb2q'cI&TJt9?_r;5.j<RhF+lQgHL>0H;KT-T&2,jn#'QW3g`gY;ThD!)42(@PpMtCtB"sH6ft^60,NPl1ThoT_+<9YL?36*X
%+g/dG65/T#j8d\Y)fiDs68=%qn@c=sS\hQ,&jju(nLH6^)-1jUm:B#nZL?;CXOT]>V+_*['2np^<`g5gCoZ!KT-To3#@?hbaiu;R
%$9M6.^Xt8I77dC:PTaje!6--`YBe'HV=:RFmPDdj/KZPD:`HlnCbsCl]$R>m%A7d&T"iKuWR5cbH_oM((q;WLZ^L=&N_kTC$H"D&
%BE>Ou\X-e5XaOnkI2uL(YS%h`o5soTDaV1MoMj6n#2,k&UqZPd76>q>b4oo\Atfk%X;'oX^=3pQ_Dj%s7b9_<2;l%jOHn.4p$u["
%GmboY6M.PBr/"ba\:E6BI.#u:m<9-\&c^Eo[gDJ?R_tj-/4fg;^@-%[@7=K*Y$tfb#@a+:=LKJK!<C"epBWX$)agC/B7ps],\T+k
%WXQIe?[*EG5tcrE%qIJoCLW8OMD$SgZe`T,%B-NaMWbRa0JRE4OQ=D=(%k"\Ft8HFYX4BX'@g>C<*>ka)FJo3%(&,!VBlTE\(^2.
%*-AKbp@#R=+Y:I[o+DeZ_t8AHjJdgE,-g.aE+HY"QDlA;AH9Ed`1`4@gEKHC)]hREf=I#ncr7HCIu<VtmKfd]VekD07rCU:$@I5"
%C]s4BoIYbG=qBO9beV'?cWraoh%]ui1Zk-t7od9d7VFp(/oWU4Vi#NGDo.=5jL\m!8(j?`/>QjtDHNJNFrfr'8p/CFYVUM3[.PWW
%!k=cA<4R'gkY%KQHa1o<lb`<.6spZ>2o8s_9j5L7V#t-],fC7<+:eZ:,=pJ)n=+$qmU4$a9RSJ(Y%^PbDFNoq'chK<M^\NT)j*Zk
%KcOH(%fmi1;Ei#!-N(g=<YmV#;`q^(2/ua+0VlDg*W1X5q5NOW61C(r:\h7(,,bLV8@0MI##F+V0O@A*oG^\pnA09iZ7Ig"iW3Y7
%P?PN:mg/*"mqT(#YV$L('f9nQOQgu<_&:f0XTXN1J2#H#&Y!JU7[+49$13o(+jd^;T7u'k]>:8+Rej`W24T<#3s#I-(W&$m5Z`+>
%<)\[P==B'^-ZT]S[i>g]KR)>R9N-YB+?KF6B0lfAnBhY%VScUa&[%/G8f>\RA-DLgH.sUo#V14'0a+LgO2Ge6cQk%r#k,k%SpMsN
%9$fY=He9blj9PeF\T%n6/;dSB*TMPSRNk`A5tV(0Y/f0-iBth?b.K]Md7-C!7BJ/)SlM-D#dbd^F^2h<E\VW3hJH4`/de7'=N7go
%daV*7Qn6K8a8/NSjA8_Fi!PR%$m_d\*A-?p$ZM)9[.<BJP"+0e&jhb[R78`PGmCt)n,nH6NajPr[aS,!#S,36g$an]@$c]MWuDD=
%&$s(XG,suS]7]\\Id*\h*iN)2d?c\A2^l3nIEenb*Mi/NV0Xb]H-["8<e`88;b@KAXgAqT5I]&XLshP0=5Rj'4:mDW:)K>M6a\2%
%4J?k%.UW_IenK[H'B[%OQ]R=TmI-jV`52MBq`mu=D:.W[<ITE'rFQ+&Uo'HT&R8EgKN2QgDeR!0cKb@BKsB66V%!AS/b#)]T5VMg
%62dI*"5p.Gmq0CZU&!Nq_A/dhMD@5%ZVRp=>FCU>XP2^?\rPhie8t@QZl54iC`dkS,pK?;1KWV)9p#&pRKL="oK;'6h'ANK0Zg0m
%O$6u[\DlBVeOJck_1<e6W\d/L6'HQAEob1H!ZVml?[3<i*D-r=S4h.l@c:.WKD=X%D+h@QIM"&kM?N=Kh-UMR=N**pFUJ8-hCt[d
%![g@H/Hsft8`>CL0)>8.3[M;tcJY!Ds'mui'#G'#<h]7q6Hb]]6(mDB[Vu[HiZl#rTKZJoM/OLRkDm?9JF^)':p1fnKW4<"76<%6
%e"s$qE/u]))YV7Og@].ij'c&";.8*)Z6>rM'[2FV@NOnl@"*ejA`JPl=02U@TQt?N>k'X;#&2c;W_K<"[)=@Ver?/r\FeX9Pt_p>
%p5aKN_9#1)-tI.Nm"&4T@&X<D"#fGL:8^!(o&JTWQ%0-Vp"&^BWMt<bP]b6c-Zan!Y;ep[MZ%=T8F:ql^HMPKNajLdpE#YQD%%)t
%P/"WpAAO_`+YN?6^Q(\=ME9LYV_R);"\57*H];F',8GFL?kTTmgb-T.A6qSU&6gJ?\_A<+P]jV-aZ$TP:YZa11-BVfJ\q]mO7,DQ
%>.PIP&jVAef6A/6WIn$,s59iZV_KFCF0+leD!0BEh\i-/Z8ZA+]X^TgJ\5apNj)d$Yj2Sg.gX%+>*h_n0Qorlc7@2pRh;6Y3b,N2
%4e`)A=-rb&rusY!'M)bl][:"]dhK9%6*`o:m$32)'6MDj01K`]f^b!DR.k,^:d[EE82p'$Z41G&4*F4JLrubH<_:AX.j.khW'Gh*
%U1sb`rJGc4C$DZV'.&gM6/2*rNf!A=`X!pCls_^#:CZ[q0WX&IO8bj\Nj^QD!.TX<&4l,d\IO:)KPa1rI>D#[.4jPr'):bCT']IZ
%Y_Oo^1dLT/*UBFf#tAp0?-^@A_,Zk(cutVG;%(=NY[WO7QU_]QqkOT9e<^fZBa;6ef@V?"LS4-FmD`"3As'a`c^#Q'G<:24HA04]
%6:_IC]@0cB6dBHn:0;D,VE=-@]RiUNn/e0dHl:GeHt2t[[@*XR%XLmc/'Jl2$kCpp:3<Z%X<anf@V]-?$!pa#\;]=V[1"IeEi=qT
%p#66=iajXcl].GIJ`W;0"B4/DfV2Q4kEQ3HPa<,]1CfBf$s.A0N<tRPj3I<HM0YAqE?DnU0(8eMn>=%Y:l%>U(!eMdRh.UlYp#Xs
%lJo#4P4E:7L5(/fpu.mKW]e.8_]5:UCP3ToTjYPgaUT=j[11<1_?%g)clD[>R^`($XQZ11nt)_!;8=*YO;S&i:/s0`:mkR[m5Fnk
%/KbU8B[ci69hU!\$fb2M$uh+3UNkbi(]GZ4NU"]!5.Vr@[(t*l<n24$>;AERDDZR+jDA/eT&D:P8URu8?T?ZiiSpa(35QdhI?j#h
%^k\+=*(2t+$cu+<iN\98`j[4%=,@(%8en[ES%WKl)&WsSRi8M^];Jt1O*ir]Td\1Mnatt[7@;"QK?QoEn"mjJhglU[ah*JYP:@@p
%X<h1H@<`+[SFHkuSnj9Yo^Q@"%'dgL5g48cKXHucfZur@,u2"73blp>2EFJ?&@EI?(bBB5[(D\_0]fB\;b8b78%f&ZBj%kJ8K)?X
%mCsa;\E)LXLHSWZ.t,*5,q3SnZIMB?%1=i"$aNX0'V**=W:IDIFfnbm8m1.Qb#$-XXR";Z_DtK*R/q>9GIcVRL/UXs95+TSi>bMX
%%DKOpK.`)Cc0`t&T.3*YS%[WVWCX#XiN.PcKrPLuK^-J_K8M]aO$#iA:!V'YRMjIB/ofda3+G:A>,t'>MfNgc:qlD9C%"l<.>4>W
%]mhlKKeq8>8+bukjF?IIDeO1<(`%!M36B5jHl)@Vfg`AZ(7PeC$L2s?TknRd-/<OtP$WAEmFOV+H<LT.*hdPL@(0odJkY.RU1bHG
%`SH`slmh.KXqFeA;c":k')\n.o0k>R/[P?d(Ls/cp>?$NmYX?Il6/jcdKDHc/.k\)g@rWe":=%O@c\QIK1@^["2rALDbB3aZ]YQt
%R8*+&'N@`+pQnH^TUGrgP_f]Z?<%t=?$'(K7p>ceg/[Plgp6(4e<W+J1T6JjS9G*&nK=?'Ml5MDK9E_@F$ZjKe6RFqk#V>8iuR@8
%<S*J!io<)*=\-PCnp#Me6S)X#[iokV]]>faCY--6/*+$+rOAn_aWW]3kVsf?j7ZH-);U23Ag3od/c7)OmJ=[s_qp*b\G!Tq/1HA"
%rq=LB_3:?/L#!h88%g<ZE)O4CfnUF'<UOIglggHdYonKJbg-9,SrN2`aQi(D,:7Yjo-+a?Q70F+0CgB2#&C_5XscbJ\@Yp`cF+OQ
%=L1**[:sq*Z?QtLkUT+NmInq2WVaem\r^6Qlq#4\#TpnEdWdJY2KPM`Ag&,@h[uk\?m%snfHgj&nS0=^[;>>iL=#=9523k;ob5I@
%T%WH]U_s$tM6P;W3QncT<1&oeXQ.ZgNPr%K8(d(&a-2)X)EC;=P'dKIjut=Q;,.c<OTtlg-ol`8"4-E]RnEnABN^X>kGb#(pS^LE
%/``1!!Q[c2=0B8::+;X#U*RR>bt2%I(.!dG:5#1N>e3RR(!0;\_G1Da#a<#>]&tPr,fK\$7hC;)jDEb?n42Pc5F2t0$Yq`\.%O\=
%&OPu_h"Ff]FK.qQ8>sT97S*>u[0(<>-;SN&?bDLR#7r#c,2BsDiLS0R=[?dqq%g:JQ?%KGCqlO[L1q'fNUYm:d-f3./93h4(r//%
%chHX!rR<"J+KGed!7WuX(NQpb+jH4..+kIh\(FLn2p9_1)g5f-A27sb'E]F5-!GNC,$L&$qJZh!Bn2:Ol8+#A6]o@//)#c45(s6V
%`uUjE`fB0,!2?)Vo<r83>Ffd`m#e!pehI`A3r=qFM?(_EKFqufjre87Kp].3B3d7.l.qf6.B!$>ISA;pH$-9)R@Bh`Q?EPGl?r*8
%%gba30*EZMkRR\[eL=pMI$PTqbV?luC,O/uc[fA1V%jNA!"-o[9"Y7_]X7/]/$o`ai?l\2AdqbtNZAl1`H=m',b"SQ4)?daT`!T5
%GD;sO`T^9<P#@2m_O#UZs8Kr4,Wj:M)$+c3j(>J(f(bJSbhI'm<:5CG1c9r_pdl)eHlW:pR0pLrbS1i98f+ToA2H.ebl(H/ULQIU
%0HcR4pueuOeD'$;.st>P>th[D^S-BmL3(IX1u3k122b&_/g5Mu+YjR7G'cG:f#*9bcLPu%jX81$TU-Q(4J_1FLp'F;)MBY;N0/0b
%bXdbL=\$Nc^9ZO'>#p'aiG=j3nn'iInlShh@+o6Dq\.ke8.#e``C5@giN)E6Y,-_t=(dVp)l9bm>#?*s?IW_@m$@>/q@7N_#2E=%
%Fu*i279PFc3U*bQWid]k05>2'8XaSO:2+#?e"td@IOQNBDHMChh@X3Q3\gK@r:A>9Mc=!$0:JOe5=D*ar'J=oNC[KgrAal<#)aJ[
%PZe/D6GL#A,l$FLOlKB,$tja/``\&kkq^U(qt_q+=rgTi^uO+=]ej$M$l&!dJT0*d&L_bsm?qkNW&`EkkKi"8)rW0Y[8H[khKj!;
%JB>Fr&7T0YX+R!^<T@uE2._g+/SM,Qb'r6fP*!ie6!p7k?0dkWAksRs>t_-`!b![i'EDk5UaA>oL!9gZa0V,RA@n?J_K-"*1OqIQ
%-q@\6gQ8I2RW@dJ#fW].Tp_KcER"B%[mYc;ETL]cL\hh;nd%UKl8&EE4_o4E^V?WbJmE'n91O1m>s2GLd[uS!eK#T)W5gdYG=:$i
%p%]QO6tI'alfD7lJq,H!Ys@^uK+ZNMmloJOLn]BDA+M)SpSgt([@_BDTDHA)cpD<h.&*b>7tbVbf"eW!c@lfc=LhPNCjqU_1Bg,9
%\p.A/87cX#P'G/sF,#UgCA8Fl8L;Cr]Wf&u957=-0^44IAC'LK9,:tSO>#_`:f)Y2;*iJcaAX*T<5-5`O()%e;1=K_gEJaiKmLke
%!4"+'7qDMOOIN!S[8UgA9AuaPK7_L=m`7O\BU@%?o&129iEjBLL%>Z%`hN5t^*C^d,QgcPF.'te`bPMdZX3S(4RCAqc'Y$4E\+J6
%$iJZ2[sY9sjQ(;0E7c>bPW^;WPu'uClSEmPQ7MZ_IIe/8;d<dmM3.em;1roDNq`!V'>trOiYBVbX#r<4I+ckbD=X=oWBF>V=)+0Z
%d1I[>>ej?/LJa2\s.?t`fsN,!Xoj/"LGcW9h)E\OMNNE/#b'8I^j=.T"@X^>MZ!MFeX6/hLo5t-61KMp&4+M2p3#9,)f(bA`@;11
%ijVj0Zim@r)BbFI::6Wa6(sB()e-1/;*D"4I>P,o,bt%@G]\rg1kG#H/dcgN,?',:j<2mi!F1<p"cZ+b_3eLEZ:jQhFp?,F1@h*C
%<Z=WX@:Rfk-P\S?m<ffia(XuOma/pI+orTED76%['9.%%KH"ZP4jgXQ%(:u1k6Z!6JV?8l&>90?>k(H\OZ:N4*#aef/*e-e-he4t
%K"q5>T?d<SgZJq>d?cuj4p]^abM*;q$gd!s",?IO';.CraQGIaD*\<75L)RI"YUPnjdc=T<30dJXV=;2K<Xf!-;Xp?,dq7PZABdW
%1<gftC19!SAr9h8l"8Z42:\6YDJ;2SFJh$X8`JKf1nZ'/p3&rqm,lKEES_9scU*4rBZpK@8b`_NSJ312*gN8pRMkJm)H1ekp-u1i
%g@pVA*+H&d`M%-$h!c?H#N)DCTkTk_%?/k[jihDQD%kT\>r:>Q/Wf7!gCi260rK2A@M[RK(/e?s#JW!cPs=rV4q\s$D:^K8c[Yq`
%p%a5-b=MB&,iGpQK7$%LmLA32X?=R!CfSr7Xq[obN$d]ljsP^glj=L?p*gl&J=:?[(9"gRRE!&?0KRJkb]up=Z<F-H:4-TY@QA9l
%[D8/C7,#ti+*JUbI;":Y"@"GsTtIU#1Dh?!?;n'O=a_)dB_,Yq-)2A*e_bOHi#q.8!CkY#Iqoo%U/^5SiJ73G1qrP9Zn"Fn02tE&
%3iU#d`g2Z>nscneq(9AOXuku#Sme!G#=e7Tr,FUlkVik6Ag[Etl=;T>:1iT*2I+Yo*,tN'YP#Sg3b*:p1_X7S;3eL>o0?kQ^V*B4
%;ShAB'8%oX*g*C5jp6K\M+i;SR6>dSDA`%P@@Ti=g([uV[hqD-D;)6SPI9EW+lY4%),5DcD'n0p>B4=(c.,dhq`3hUllJp6:?f=L
%VJ5++=^`"o12%X`Clj(u?A.%+;'g:4$8\.KiVWQ2.h:VJ'UDRj-Vh!T?i+rKkB/UP17[<p:+%njha"Z?NLhFf_5d>(%2!O'5Aj[F
%'M[q61(lpRB%9"?acNhk!#t$2I&S/Z0aC-gFe-:;+(W=!<<"]1T.l^3$%L0(`9j<oKH3\B@K8tMaM(mXR':WM:"n"ECM[Ju0g\YS
%$ck2,^Og0XOHrmU(WK%PJ+hI-]]\_A:s\PY_6+s$3>u[r/;k7/+#<L?39.umV&K,qM*141Z&JUhXbSKtj'b0H*MGXqQoNAKW\0`)
%/u9Y<F0&W`5`:ShPf;"\@K.Ooq5"<`CK"mG'%0Ub_HRY]]9?iZSB&g4lR_YKGhdk2He+2>?/!4ROt&XcE.\Pb]3kTjKOYQ,XuU=3
%h7mK%H&b_ON(84/dqDfQ+73t4mj^HiYMBkQ<n-iVbW2=R^JM1DCF(?;k>R=J,GRdIUY4NuqY=gaGJ^_2V-u&>>-ia+8EacmNlP"*
%gsJ2S45jSQbMcjNnAa':\]GpXnIN?ITBV/L0kfgEchQ+r94f/e[rq5]lqk>#%gC[`;7$UUW12r4**Gc?Yi[0[.`5;%N@&76/cr@I
%l1"-tr<bW3Z"900n7>k$TG1+1io:$<k</25P'N`=S*?9HZf,b6h1@Pi;o#p6a\7JpKKT/Js'?R8%<1>F$8ld5Cb_q49Jcfdi":\V
%)RN_^)FabM@H9PlaMoXB0D"m%`sB#5?%ojXVFLJ?\rD_>Of'At0BpDojD"K(i?t7ko<,rsS4Z6.ButbIDgjo@:aHha`4>-Sp,TQB
%@b()a?bNBu%QT)d5/32HNO\Uac*,7\B/9j+RmJ.Jk),1)/r/k7F+M:3Z#m7;&h2jPr)fPl<RE9YSeh)_A=I!in]t9MotmdU*UW.(
%poSOar;.N'#tC+ss.-:dK*U2XM/'/)F:b_]k1?u@pC>0`XX:S^%E3nG/8Mj%d6!<"Z;b#12/!7m72';MN1q#@EJn667^:.BX1%GW
%&ZTM+eagf=(=AMm[KS>eE.<T/#($d&l30NsATZWnA!#5XrjW:c>!gGdc^H"Am5mdL.dr_2X"/-cmP>%r9HnO%/;%!/[un>3!Ks>5
%dkH^6+CXRlB(tT@@+;Rk,Q,P$^c*dXNtenqDE<n^b\T#N&a-u<j(%tVSTnf,X^mBFhkE#":N'm8.a#mm_/i$E(I(9,pH3ahEoH'b
%n5bPsS^Jn]Kme?bIa!8`V*i$@hnq,TXKsfpV7*Gfp&@g`k0LSYFkXaIr)?C5?N4+Y*aJT=a.Grb.,&Ijj"H+]&#5MQIW#Y_eF2b(
%+8q]0s!N/*U%a9UP`?'5pl_ogb%qDl@.`[i@7lX^P*pu1&#,:J!q`nIs(t48\?Wo$^A\Fa:jNu7WEf9i[j:>,WqB^t^VP8Z_cT;B
%^gH@ER^u\:]d)k^p)IInWN"?,h$_q_HiH)FiA`\]S)R2jaTE,:[hq>!<Q\^N4h4J1MIp^%_+^OGa0JmEN'\`I,A8<BfUc=fgH<J0
%%mTE@Q?=%*:d"[7^$uuHbP?@*_;Arh@8d^4+-s_RmS'r:a!;s7n-PQ]2=*qb_YuqJdD[<J3'7"AN@#@5"(?#HIe;Pk@Y=Rf/Ys!n
%pjb+BPVUX,F3Gludba5lM-g7aRQ#]n/-ZbN=Mb520a"p],lbPW5\WI9"]N?j!TVGm*/UlH3)7nkSFDYT?h)/^+<Y1KBe3sLW<\hR
%4p+'.'B5bSegFUS3`tmpR$grtp*jk/?o3dA8qPe#KpqR9@fWidK1)Kt/VT4%;3d.W*1r`??`WRlj=q4)J1I=s:G)gIK4l@im?EdJ
%+RQ:D9@G(D%nSX*bh2C8fFP`4G%C$&63+X>Y@2@Xk(6gga+j,QeH:LUrXN7H&ngPKPbTB!\Rp)SDOH+(S?s!eCeC/S@\4arT2X?(
%m)(>h7(eC)3+[ha9Kt&`^i)47,me6$=f$if'k10Tc:5c_0P8?N*k::'HaZH9>DREHV-"dmBXJ;8V/ZAG#3%(U'FUK6<D#&QDo?Km
%FUi`SBm_g**Ic__':Q0f9Pb%*@%TQR^O0;4qsIYZ=i%galrl0Ppm[%*mdJ`C&&,?cb3g+Q.Q-a3RW[stb8//I[6nUMFMh@V(!r`_
%J7C4ri`DqUlP:_3N,'.!S22N?UVs7TM4W>8U`,g/5'3W;Z\$#MmKue;#B;t'\[$epJ%BWYp9*nW`Y$3;9gK\E$Y(`)g:K>lF^"Z^
%X5k;0^st3?I?N'E%0h2g'`oLC0_b:%bt^b0!nE'pi==9h8-WVY7BQai8Lj\<gSYZAYgV1A3"qGG].[t0KARU!pP_=#(`sqQ;KW*?
%+F#kp%K!2>9&=sq=GUlRp?=+3EJk#\P&1"U#-cZ2$CP?cK&3'0^/,YgF9p$!$]h#:B]f@?e<<^LPk$8[q*b.T8nbI%NR:8o1A`H8
%+`GV!(G02L1$Wj^2Fpft8W7:&1EW<Garh%W7qXB31S-PHgd'2'g!CsEVf+;A)?m&[&09;GL)XNoX0egs[MUT,Q?0>F8l/"]bON<8
%'"a;/h=4u*Z,9Zik_pCC%Q_NnG;]dn&gB2O=!'/K<9UP'XFngePK#c[qgh-%FXu>rUUY;XXQmU>ZJj=bA,TLRCBhG.]/:@70k>!Y
%*;4TWoLIe_!&tk]6r,q.Ceu"7Si*<?"eLBEB0'U"'oc)l1F,cZRm3>0*hC_ZQ>s-9(Ik.>A7XP#P/AeX0sI.lJ:c)C!YDjV/Ktd2
%l:aci;Ba/6SJZNon]qsJ1,(W.M%Lo/;3>rq`REm@U:l9aDGrP%7['fD1Gt.L<[U>P^VpJP,`J[Gq6tB=BX?FfDiH3n-$qnc:,-Zu
%'Vm!2XlW7uBpmge*NYNp5Fcc7HJlKl+\B;Af)qd*eaT!h3se9eEg"ci9)_O@?#t9^qgfi0l\F--GC1qFBmWTidpE&6,cT2@ULOKM
%\nEgX&!8R"NV"/6Ef6cpB-9%7VXaL#=;^l-Y4K"R-!Y.h0nP4^^9]&5n3+KN!.#nqIHB`s"(2@Ar,LQ78Ga]:InAYJnRhG,?r<d%
%?lN5RhV%c0o1HH3d2;B1Y@PE"&^<X_Jg=0M?K,9`/fuL'>'Xs"!C=K!^K[OtbMN*R$<a^s(BeVM/F5puh7,):]qeu".j`h^f2a-[
%VXU%^0F8WPFNgM&/-YC)%Xa9ueSPan\5j,$aHj@sUo(dKU^]^(Zrj*]fnF<rQ7rV!))G!Crj1cfV/dS+!asNX@[i(6\Tpd(VJ%fB
%2+.gVO#YU)P\_Ka7_B6KTJuE$QrV]^b]"m9!JHW2:SCuJk]hcUend(#oMi(G2fNs(8aTQ'0kg$$&.$[_oa/-m-:/*%&D%H:N<&@m
%]AEqg+:"N`.\qQIajk)7Jd/1L`;n(RAfF.J73X(75(m4V:!tdP\dRGZp*>Oq2*b`[f,YkaODiPikFrTfB=m"L0UAikgQ5B9Ze*_^
%Ch-s/#8-<.m>N\:'7[00=C=-lG^seKo#.5:r7D%d*:`3(]f$MEW!=!QK>o9L#XHaVM%oYs&?.a;hJUr?Ip'`<BJmq-4O+?kACGIi
%Wl0C;YM&1LPgJo4gX:`J!$M`)ojD-DeImmJW<0)5R46E/*R+k-k=,)`;%MH87<)<@Hhoj5Zk&<5bUUm1`>!l662)oS^?]oF6@#rQ
%@LdX>=5ip"J-O1j0$#HW,Y_CBgn<W<oh11@K.L/!:8]3`<-UkP,t]M`M+[8X,bm]r45:@rRV+NeOq8fi?Qjc/P<>,CW$r/u`#J@R
%3.'LMnC.u5J*K^d^]r,%MJYs:[i+U^IK`PXg0[0i<B-P/dR0!"m*r[/p>$>/6m`5lN&YAc'1qg*(K.)5#GWbK%XED$%rblQ>HHBp
%!RqkE3fLri7Y#1^A(eR@7STrqm>Bk?+9l4iing1aGU1%FJc8GMJfdS!@%j"[Ito)`^Yh][*e@9"hR"Y2hesAGiQC:KLd60LXECLZ
%J"gjBW*toCK5mFjUO%GcBs4Pe&2VS[7U]:+4?#+3&:D,UM<sYKp=r7"`Rl(QAelAt8]Lo&a;`P&_G*t'M=^"77$G-VJ7"ka7ETA:
%*e:$2i/<>5c02uE@o1)fGc99JkUk+&H';d!Wi7`Ef\*FF$^F6h)u!bGpBHpb`\-MSbtbp6IP,lre=@(Ci/*)G)TTpU6VNZOj4rYV
%"%N7oOuga6ChWM^[5Qr]YS8Ym!.3VHO@tCV&?+_'Y1g>+^*-H#=SYCb18IPGgG0h@`Z=gA#5O*\/eX_2c+Z.>7J`9"$6E(S(&iUq
%8)=9Lld1$^ee<GIjung"p4OG7\q]`#,6hPn=7d_=Ylp#q[RhpR0gW[00OIO4K#bs?m1H#$>f4:7XuSsi@q*^_<J*1L[T,)EY/b<i
%Mh?3LYO/Q^!?/C9&i804EfK&37g5LXIeC191eVLtC9iH=$oI`H1HMQ*\nW,!)[mC="(]Y@[]#]3GRfRS_+7:q"LX?3Jof\%&.K0T
%iDkmkZN`M9n/8'+$fggcOm+@O5rVA?>Wm?JLHH)fbG`hPVCT?MKumPcJpXeI$2P\;S+Q8#bgg2LS*"-rKb7Y54m@)AlHIG@^Q9RU
%f-ocZS;Nc!fM!QKnnq^8A"pn/d#b]7:(Qn4*f$l8kpg`P6Ti^?FLO&Hmu&KL7dX'C&o-t<_JjGj_<&o84L!U"G^ce8J*d+_MG$K?
%ab9jZ8n4J*5l*Va3''+]-E1n:QBrcX(bEs.7(I`K;IipI/gFP<G;3k9BuO.iLDJK.&[`s%2`Xst=%5"GB#/!^o1X5n2jtV/#qa%P
%ME[&:R2-^7O[ApZ&nY<#>GE+jJJr"FT5#J-?a08<$nWfEL=&THrh:?tOo?Q.5s*o3(a8*J3qQXbIk4J^?=jNV^2q!e;n-f,T>itW
%+M.M[?+:ttP($2SL*W[?mfbn%#P6V*blm^Lg*PS_+^V44&j!&$HncB^M&Gu?`dSfJE?k/gkq]"pEG3CKYI;XV4p(UQk30='_%*V-
%PTj=Z!q=MAAQ!s"_0D]7?FN>9p$X&X`>aDtY9H/`nHj7gU1'HRN'PmGP_NPrIhH0H$[acWU[S\W9]cJo=pdLt6c?o.4tGn)q!fuU
%\h<'8rc=#<(`Fg3lqhj:D*O_n>P-EnL-.#Yo&RSRMJ*\eV'tGlpo2EA4R-`M0*fqZiHD$N:[0\8O;7W*oX_tnD\s!7iM+m.7g[.5
%^rW.2.+Ec,Ht=]T'QMkbEuRu6[[+-<l'*>gPrKo=Dlp291Hs=kJ:h.Ii&P^[1bl!LVu@7oSb#jZ>4H06V`g%-(#ohjYW=odKlR12
%5iCr#(r3_d8>TEDUM[Du8R)/u"9=Wf3[^ibju@ZqUbfDNT+RC'%7HAg<7R-1>9>)oJR5YVIDuO^4eWb\hACh=3OB+PdeFT]@#:JD
%O1tf>a:;.^-[QK#FJ&#/r5X!mBCi`+-`O\&diYic'd)u4pENjOL`_$%>Qo9K@;/j"#O<dHm<jX(1=/dK_>&u3&SAgG38*#_D2V(9
%=0dDI/[I2DDd`/qV8gjTq,%,gE6ho0mmb?3,Uk9l?=mTL`TXIFMn'aaUfs=_++-t@8r(Y"ppdn`.Ht5b5Iqm4GE7XMT+oV`k=P2[
%RA8es!M05D=DG0I_)uffNd(/61VMrrQD3LTDs)&Z?2d/pE*S@UFu'ino^SN)AS?ct8N6Jk-FoB%mR14Xl<XQ<]LSRS,8L&?h'^4`
%Z.se"F[sOcN$IOLYFRHO[!63QEo8gO-jWf__VZUaH'fgh&^[h(o5b[TB*4mp8VDpU010cIL"U+\OfO-;qcn]7Tf4$DJmd;^W%PE&
%GaDqBoip#oma?`;pSpqpmLQJD+0OiCH@91W75U:]1j=oBACo@qU((V"g&4GZ))EdC2J/gjKZNoqchH?'k\g$j)-ZK$<*5FjYhc.[
%!]7glj-oDY.DSqc3\RbZK4"J35ND<8KSkj7ZSdMS;.Os!6C;Xn)i,IGRtY5tGOJQ]KIh2>kOKhUQ*MT4&P?r32/[Bk8ZW7p!D4mQ
%]SDgo\g^%5LSH,#QmVGZWupIrA"pX4Bu`6mem-4m[sjob@p>9?p/&e[^LX3[?o9gNZ\iRe`_l?P-@h1nb"h#Pf"LnFDTg(h8XFPR
%Dg'D[9Fcah$9%oEeii=-XIiI_n^n4JO4GI@Y0F1IM=RFTF#2kMf+Ue7#T/)t_Br,-d-S!O8H1k%OI2JLlAZ<4-$M^dCGJYiBkW0E
%Kp.+0qJ3K.k,cM]P!Rbhlo/8prY4,9F6g7q@UQ0`F0"[`U.cpN'umuc>e@BT<$FkD`AB'"TW+JjPKX=t6b3$$Gq@p\<R:+)WT3;H
%7no2Eba0)4M%+!f&M'ct@Mm43a^oXu^P-G1=HIT>,,uTb\TYYOW]ka#fr`<'1m^1F`4X\nalg)>'d%27WM0XuDVf'fOl3D%V<QfB
%K'r1^.1(hWcl;k!DPXVq@Uh@T[/M$i(R_J5ZZlaW0L>%Y#&M"@'.<eTF;Sue9#dm(B<1.T*3jJ7=83%%#;`e_FnFIU]32V;$qEhD
%b.B#.gKn?"Die/Dpt.b.pkXql)&7U_>G6p,q?O.c@KLH,nT+"di>R*>hLCI["f?u<24*aaEHQqDjEIbk5Q=*RUZ!OG+&K_lFs*oc
%4*Pd]'BV.-OAt4sm3:,TnO0DqbejCGpEO]3YT"?VfPKPo;H:2tO09b%.8'33^'>mV6BY[K?!a:])s,_Jih9XP:='5D)[)+0$PH]r
%P)@8%XdmVZDiPsR0%`MuBEF>P*4494O0p9O&'bV(ORG7fKfVA]iFt.")0,J2j5>c3];(<*'RP362;;.%?,d.Uh.!2)aBPR6'lciM
%r>hT1.gJA"Zqk?\]/GVUNd.lkY$[/+^DqgCPI1''c`7[:Ve5$8FQpP\*]#_8Nthj7!M]MPU6d9Z*MlV>3kj],+5`7K1&C<JO8SLl
%.te'ET;u%"lOmpYcZ'4p2&'4[MY"(i7Iib_Rh:R(e7bZDrS?SQjJULADSBXO7/fHA[#rY1=)O:f>'d:`XJ-^Eg:u_2]i'A5i.?mY
%A#SK?oh:&X>5IthI.qW,+L7/YPTBdQqgD$\'aGT/_6hbEk6U+t0]rGu9Am)5L&Qk6id]YKKhQX9UCJmLPc(4Ub4K]`@,5u.M?o;]
%!\\=C+;%XmBHC@PL:N2EK)Em2I[T6gPT+IhjuNaRm>-`/*H8CSW^I\uVqkV$9SRaG(@N<b&djD0A3W#b*@1-.0O,VDXZM5X#V9^f
%0[fre^f[VbZB-<IOIH;OFbomL.K(D:_TbLJYsK3b?^=;aTq_roPa>#'s2t[G'\(R,j+9OIM0#TeO*k<%a&YO@S5^*bnfh&hfqJbl
%4YX4`<g"XD6"<hD%kff;c:a@iQDt0oXK\ON\13u*GJj,Wo>f#R_l!9O8M7qJ0)DNO6,#t$&j/H["Tl^.V],OEe;'4O=auRZ0CE;'
%hdE-%lH`3%&m&1!5/,SfP+O%6KPA20FX:i.X;4;"lf-(oPRD!EQ9a(3/o#D=gf&-/8%k3l0K@h\-Q4C?>CN$Ij3o7d);fG"QnU#l
%i.L3/5LUB2nYSXmlKC)C#>blsQI0HfD;nPq,qZ]o%H[AW[6WW!5CO6uVTMj6jU.TaEWu2\_+6,'W:)'0E]:&:X$A==:$fMEM&E,,
%[\<<=N[8f_W`shZK_fV24qj*U/tYk'9JOAkTWlF5@50MCRZ<oXk?K`Q-o,/\&r.!m1(q%naO=m"_[`8dQXX,[M;YrN.cV)!!SYr$
%kD?`aS4Y5'!9Tsi"ObS\^t=8$\I?;"@+2gj@<Y@qENg%C<#Sn#SW2PZ&lKa_iV1'In=)fBJ,-leGA#_Ca6mq-!ejoX2)lO,B!`ND
%2D>=>n9./PET"1taX(?D;4;4r3Y@FAVC0bt!,PSN@N.)4(;a5hKW"N>2(1:Y'%M#j4*DM#++ac"CI>*%]=m@L4s+@l\-:59,W+V;
%(YFe-__<ep3+(@+BhD@Z4oln0;nR0"_2I7e7\P0+I\/0)<i^"]JF3%gg-sWqj&4M0UO)-4CuVdtETA>qF,80q:^2jo;8S<79^fB2
%2%0##&lLk]`%:LcqJ.l^,sm1UI:6*1#@B%99OW/*923FqokL@a@&([S8;EbU'=;h*=uN!fG$"TB+?mG=4b52$#uE6!;5V+!Y`Z9e
%0stc:d<EqPO'+%4rK&S6pcGSC&#lG%CB$^1_7'f2-^jD-C3k9c?;I;J`*8+uD;t'%;A=aoSKEhg=LW1F@$@7,6cfAuq1[`i3)A10
%"mkpjpka`8Cm$CWT(PMO:<<S3.mbHH7oR@XO*]Kj,C7@SISH`@Du^_A4q`_UO=o&da_kfX`"V>rcc\]7jSh@M;clA.L@P,\CQG&3
%9HPbf[sX)G\p@q,2AJ9#0NJ^rQ.Pj".JCN*!^>(I?Nc5*=[N8p31_WsY1_7<Lho86QSUAfog(f5H0+l/<&ah.Wf(0H%7[.Fb#(Sq
%`.k/+A%&8+,ai.=/2<`eN=TRr3C`j.!J3$#`s?u*;:^3Tp5'mmo&3.8!K+p\$tU;g#NXbpIP4$3G_Bt[5ZZRN'#<]sN\D*?eu`@r
%l;Y9]_';4@L^/2UiOHf#lq<u=c`Ii"Tsd@2kIt?l@XjG0YeIM5TBQlq">rZaVUN7*:!UVJ=PWCU<=#TBrFC#"'4V!.j6NQP/Y+0L
%.:KSG3dW%fXhiYI"HM4A_&u63g=I+MpOe<']4SRmQW$rj.!LhZ*-NRZ0le'M&OnXE4N<J^.8HGtC!gFR,7(#;k"f]JcrSrS&Uc4O
%aU'1$,>u_\&n\(jkD[a!,$s!?,]JD`@;TH]pA<q[5JeSY>UP'T,gba/7q0R5)XTW:P(GGE[D_159!5QPc0a*#=lhm#bWErlC7]*Y
%W@GkLa28LI0K4R<$c5gU[U5aO%j4id/_UWcRkEs'>%8e?B2S`(Q:G\D-5(5_1p+oW`tgUF1j6!VXj,(./1&SPqF2N?l&[lu,)0Ui
%2#79p`^WnF['Jp.2--;3>A_'rdjjU5UTR8R(YqR3?c)MH[m/.u(6Lbs2,fS&:R(iOf\,4Sqq6E,9/rm@W8'90gf:0Y-S#b7hr;Rs
%ldltOGEL8h+cP\Nr6F!nh`o`m&UWR+a'J60ha:gZ:n-_kUSOQ`K6I]TnQ8Z/VZR-8`N@r&G>_$>(V1[Kq0Yp<'#/G3mk%0(4m!p$
%=;[CgYTKVBUFun9=MF#"s6/mg`su4Q:tuVK,BLlb5?@,618YT2LHN'47-tl#,umAC0Fcrhm+XXbO7=Rg][_tTn:*a;>&?Yl\jcZd
%<T<@XMt?@n,nUiGNPC10KG+8_/6$>6;kalaCs1Bo`ZD^5BLcU_rtr3jK4lM]KHjH2,LYosne[a46WYoTiu[6%V)b+KAG\DWJE/`!
%6IhTiZgBK-.f"=<*L>Migs1*'o<.I/jZ2b?)K#R<EKA7P4Kdlh1;WHI<!J%$\rqgVk`:.(.r&/c)iR%AW$A3"NV42bON]pb_u<Vr
%E.s6OI<k)'d?iCEX1P]O<8j.>h-FIm@&SLlTt%"5+=qfm;cj<oos`_HLb)cF0b4YoElUJPW;ZB),90\X&[2_0M;d?h5i?-1=1,_U
%1d!r$F[M<,-fs8bYnK:'46fB66_LemI(:iu(W+hs9lu_c:AlnJS@(jV-Cm!q.sl9V+4>#i]5t"t@ujB(fTo+-QqA>W*W]B-)1a&%
%ia=<'0#95BUB4SB,!%&gg?W_`<Y\%!='lTO?ri(EDQ_Or/R$Yehr:o9Oj7oK!p'"[TnitWD!jnX/E8'`<34[I-+\D5h<(R`"81s,
%WaTa;POQL/ftFt,:Ca=U$\RbU&>m*r1fjC(<T6*1U+VY+;t/U@&d.Q[+csDj*@X3idFSO)g@t.j\K_pM`Fc<?<I>^GZAq3;9g8X#
%e6In33jfP-!J$=_^3s)"o`T[O#=VG`ggbb(f*-%_F4e`=X#%I7K,_"t3c,jHFK[><_oSj:SmMM<J4.*/7dQL_K67`*Ok>dc.Z"Jc
%[GAGe]>O.S+@$g"<t[UYdq0g3\fV#VP5u2s",,HcaqmL1*0!)33Or_NO*?[B)Wn^/*O\ukNR,db&&@T8/`uFA$eelH#4dXL7G<0F
%>8"rNpS&4PI6!+[q;4s[n'J4L?Ps0:_\5_JYK8b,C57p[=,nC[(@=Y+r=X:+QC82PIPR)5EQ22+0$F'Zh,?LLf@dqa'WGr_VZ5ob
%_>o#ZqgPUX,^bmgi^2XKXUs#=!$^5D^)<Q)``@,JK+W0(Y<B<ekkdKfY15-MntJKJ5o_@>pH:$W0=t8c:i8d=J"KQ<aF1p9L^HDT
%J"51<.SGpXkO,*#RdcKR`VrlnPPkqB#='AJXtZs]E-n\5IuV,H0U]mpiQ^6LDKXi5+9j#0Op@'c+/"@E/YKSaA,-NYcH$)%`]M0h
%2D+)*?\)YbN/u.6b?n//d;q:A,7[6(?5fM)F2UT/-3AsO#n$b$gri8[g`qjR=lh.ZR$ppeHcDnuIE;@@\soZ?48oM$:$j??:>:Oj
%ajp2Q9u5p!S[.a1l`A@L/cr`Ko<*fDH4AYaG&""jdT`r9F,@G9)OV8Gds-9QJ&[k$kQHH(Ualr&oe#\BPCO^Bl%sb<M$O,E]j?WC
%V6XUTo<&5#oCj/0J_'C48t3#G/eD??X02G68##CQ*G;k'6"JlSKp:]KQA2m!Ao?EFCC/flXne+ujG//B5T/eh)Fcm;=9N[gP,I\k
%KZ=b8dmkPb)-PFdHcYDLcZKTtBO'3gh"N)tG7tZ\4uHX#AKDVEMo_"g=q,_O4\$o_jBkb$ieMK9s7la@IcWbej<C@o?IPMeY,'JH
%mmFDkkDJVbB.q_)CYc8S&,poRb]*)5=UA(X_YYBaO7.`#1+eTB<"3-D_\-=(MUnMKQ5\6>3jcDTR3Ar=DAc<Zb:'7N4.p+E\/^X-
%KnSG!a4qh0[`94!&I`R0Aj@Ri80&`\6U5%oSW4CnVT0]gqacim)Zm+j2$J(WP/`I#Sp4\b^+,lsL2B?;ds*XdP;X7o/']K[K2nAp
%*.Z3=OX%goBI$[];<Di&qD]Y\0GSj*lPVKVm>3VeDT\?4M$c]*?KS12"AT'[Qr:GFg"ZduKePu;fSXE$j"JB&K`9L7Jd<3\M?aLI
%=,XiE<&d;'U&_!=%/,2MZ&*p[Xs#c*0&R06%3U8F3;,-:W+5jZZaW-=^=;&8$\Q0%,Yg!QBQV:%AN7E"X[M@jDdsAL!mJE5e&.G,
%,%GkQh&[lm.LT1Ab$Nnqs%o-EXt.5,p%GI8D,Ecs0(WtbC(DQa=IROMdFf=T.9F[L2*Fte@8-7^gpYNX_oKI>^"pnYS]*15G3Pq:
%,Y7!!gf<.^[*uF0N6N%eHig;b.5@T$#(21sQp7&E*(8bb&-N="rFrjcH?W@m`k#ZsZd]`iSrq<=`GI\<!^aU^SL&a1*Bq8$dY;.@
%s7XbJ5/`$U8B]qZ#ieoZ\:hTkF$u6`J.kY>mf<>5KbR)OP3#T].1Gfj=+Q;t:u<LpnTTX-r'.1qld&k1NU;;?a<W4.W57`##Gt1h
%d'o5Bi[]f4qc1=81T&;160^$78VL,T_q8,SiuT/,d^EuJW@W-\oP1XW`fC&meRpjqhA&tR=aj!AGljl=EI$8/I_J>Oj`,-VHj9Pb
%dZM,R)>8'Q7bKn0R=P:^F<,?"Z=cXFHaN-idN-`Q4JrrCf'ls2&Tlj45^P"E5i8Y4dQ#p6Kbaf#Oo;aYNIHT<F+if/(G(7pF^Ya.
%S.`'DY[4MK7<&<V9$%NNcLE-:ro5GKp5j\:;sP;RbmaNoc'0E.n8scnS@"Lr"erID*OMB9S:[j5WTklRX4[pm=L]pN5K;hX!@EB*
%";q6$j(tj.i_4kZ<"45c)WDh`qTm5kn9ga'CrES[]k!:qJT`d,YSUg;koUSao,-;TW(3MEp_]KP'9<.ON##oEl1$JH0(g'^ZB$ar
%IgTD>@Ns'KFJ*u0[6+Fgh1K>1ZU\1='dI7Vai9H)P4FI<1JNB[-i(.1ZeOh\PQ3YtK'L.@G&j+:1;<5;5=[ajd5eBK8S]dYfm&fQ
%J0*DiUjbC0W`PCg!f(>_NK!4\k;1=%TNY<REn7Y$r%/F+(eJ`q@i^$S@ZiC4MoNG.7ee:$N,L0I&_>S0A+_asW!Y/:lk"`cHJ87E
%b>M7JY*^)b[%H<<hPU<uP8$!9"I%sd@mYsoW=Zi'"\HQg/3G5;\=Ub5.C_acJP0/lSNLhnQcP9DL_`3ZT(KLP=p4osqcoug2oH#'
%j+LL'ba"siL!YObV"i5[0>H<ELIhCKn,I0hYVL=h+<SE@3?mZ^hOXk2FdYZ+^]VYt"3qXb)5KYi0k-9e9+S8DNM<b_X)CZDD!4&^
%d]61EU%uY)4SFcX</6gGb:lg68dFn"EG?.$XcB<J5lteE\torKlkRf*'=YV"ma6H=[$N1N:p.@8^8L\(bLas7q15Gedg:O5kk$^Q
%?daMuhH[)k><-nr7cL@[XPLA5M2UOjrKfaE=bF*LRarK3e?7PQ7R7)_=".o%W=nM/L9;J<\L;*E/"I$KIYQ=!'jO@SMWUgrpUmM=
%\>+e15$Ep*6j!s(Xh:Rpp,-"=Hgmf&:Ye`Dr$aD,LK(G$N\):;reP>A#8kCQnES[YrYf3cU))uRC-O6,Y)u#$R=:R>T='>"[b5I"
%#EO]IT9r'obp"i=c9g278h2sVVDF>9bY>]89087G.D4:,;b@KjM=]]f>_F.gg[CCTfAh`tNB?WIchfO@n8tY_G"FofrE&9#D7[OA
%P99Xl8<D\r?9JpI4>)^<]hN'?"P0SL-i#I5!kpbF,0tNROU9<J2LaU&DC#l.`T76+6gpaWSeq&ZQ:6T7$FgtJQmWk<%Tl,,G`h/j
%cDRG%^EPW]W.mSL1SG;^!094]-9Rd#3C#B4\0[;-+^S^I?M]!#qc:uYG%)+mX=@hehU,ZiEr(9Ei0U6F.D;&>I2qs&7uheUnr_6@
%,R)*0Nra\GIK&0I;MV`kC2QCu4R13YMTE?Z(:*'nN\KakEJY/5D9pg=1emt/58PsCi2EVLhuU,A/[O5;#I>gQ[d-94M`!rh^RO<\
%%^hi8N0q<f=p^\L4YD?6^$\hInkRb\]lB:DIZ,dt4Te8k,mD4&a`W(jP]J_ID.gtZ+Xm%YMclFHWH'HA_.kc$o]n'6&jrO_1kR/6
%oT^mAdu6]r3>*10R-VI5VEp#X&kc/]N[IP&7Z=dl[Ra_dUljiT5K92Vc60/6m;X53XLPcX^GIFIIT/+m'LGD__blW&k+Ki$]'s+;
%m9%\43O,)>L-S?"N_%,jb)!TO^lAF4G>dcnI6cnEQL%@HJ[I;#\GW7'1K'6\N(X^0AUIHI_fch%&*9E<,DY'4JC1CX]DfZFMNlVQ
%F?^`G$#*s_lM\3s:pAf!#;VV?G?--oM$tcX[Ckp6j\sqTS>@[#qpZuH0EM*,V>S.BSUu_"6O\1S]roSu,=u%I>lob&Y!N_L8\b/&
%eFFHK1Z#cRlr(t=Y*@b\a/:[J+TccX_,Yo$PVeV0foq(+0,hX=``Q9^Ac$=J0XN8;+U#B=D3#,O%cNZ1UE-YET[0;^4]F`EbBjah
%[Z+^D+R*i.C<6Ttj\00VOS56^QI"p$TT-kF5#gGn'`R*Mi<g'V:Bdt`XYD($gainC6$G34P7g[c4IF6X-):C`]&@8=j"2T`UQ-9_
%"g%@Mh3+E5fR8B1%P@LmDYr(i]6j&'ePj=a4JQ\<9.[Y`D@jL&EpnGHhjhb8YVGN%B+M>/C0DmR<*AK0d#'3\0;Ln0+L0!m\F0i2
%oa6kHB/r;c,Zo3-VIk+4'SVLT7USKa<1>j'$mbbobn(.hH79p\8.lq9[jrg2UO\2[7?&;P8&Z<\O-Pcm:kL&.Sd5GK)rK%;G^+Cr
%8HpJ`8=5dpiaD\[R5)(o$FY)6Ks>?eOs#K>.Mugd8bs$h"k^<,>9pC\J-j@f-2M@$S(7$RZoF\'[dfg)j&2rk`Z?2sDSC=:33m7+
%6S!DgXQriJgMs]p=^p3SnIS#g5''q*Knf1i\I%`(:h?IASe2Oa*O7_/2K(#].C+P$8Y0?T6IGJKTR>dBA5qG7,P$'^[PDb=mq6L[
%C_D93Fe)3c9JB,u%S9&NO3JhECTW")/:0E6=`NNela2sDeFpRqK-^&MguQ1n`1GNJ0UaJ"!-EH?SO>NHA%A,#gPL.hY%=V1qtZQ^
%]s0\VJ^X/AXY^YUEpj8J`u'<3k*f%h`"#biiK'6ok&K's.SY%W^0d"a-pGN[d&OC$7(0'8NJ.1h,Z4p`+J-)T1rnlh$OmWC_k(XY
%!@fD3P=h60-kH#:E)m[;X2n^h&ShBNALo4HSnk(SW7r"h,SKgJV[t+)-,CDaD-pV*C)hSZ]P<s?U+?(7,G8>HaZPtPJ4VVF/nF?=
%`*_MbhoNS*q>U!MXOf1$-i[3Z2['h1\f`MW+`)M"jS\mPa-0df&[?Q7ql?>,,?u^bEjCc6b7TEMcm4WdGRbS*C6(kh\og:,24A0E
%8RHleP(r8U)Mnl6ce.^lSg<CT=79JS>93\uhD;j0M"pZLa(X8!9'f`*V0G.pP5!P`gIC%=\^:_<erecG(!Yeg4MgIPZ-alD6VY>%
%5?nKaL$?rK!JZm6bWVY!0R#Rac?lOW+ud(5rt<iq*/J^2e`k@kO<KS:X3\rA0+*[3i[hKY6'ZC0ATG@"=83'Hm\4.q;Ne[O=:<W(
%=%_6eddI_NO]F.(NTE22PF6]70NFmt<>,G.&X;_dduI##MWgIN3k6)H6,\D`)^i?<]pO0`i2'`n>2[>k-*]&gHs+4,45&p:a^q1U
%]fN/(bE)3,n"SZe%R)LmX;\?"9&J#$Fu</"pYPKko3FsTTl/:fcf5m&-#QHb0kl'@C+-[Q4$V_^3(YXJQEmJ7$FWf$*87ReiC6U=
%&kd54YB_#oN0Qq+5o=l5fmO\m&RCLALm^riB*ln09\-:q<Sh&<kb?Z7oclg/9LqD,l\oqOg=3&gV-UW3IE.fVfT'q'j<N\]\Kes0
%hR<?llopP/Cj=1NpQZmf0ngfW50%:5nX=I<c=*JRemeoBHj5b-.oEh[NpSidO9-S<"CJXhBkF1rhm[:@\:iB!NS@Ns#,:LpOc*et
%BRK8toVZAKXs`:g:3p.(g"LI=^Za'8rNWE>boV9JTT$hlQIZa79]')\f*.-1oc?<4hQX=b=H;>0O0?X)(oq`ZV8+gYJh=K`F$l93
%oibmZVdS?E_IJuSH<N20#7IOcWA-T,\BVQ%.)WJUP!o)`Eir.T/5[23&[/GRma"/D;/Ad$]`_a2EXq7De'+/OZ&P@`WJX6T0^to2
%Q5[i(l,Iej@n(G%@aTFtmu,R,j#hp2*p`O<57`7-_OSg>E>']6ZJ[f?'KcB;ALZB?8&XV3d"N*GJZU6#-?IOe1hD4(cI(@A&J1Fm
%W<3hPB#J75osE%[D=,.`cdq^MX/0DEif&IFcBNBgJ>>i`,&k!()91Etmml=5[^30^-O%kD_XR\'3SMO$?@S"NV5$>\.ZTH;:D?2,
%]=TNXoW7e]q+_Kj&UKV<:b69KL5_o.cf?*tcBsD;4UYD.lfFPob,X&kYI%iH8sH$X-?muNGFCNI28u>tW;8F+m3qdWaM?'>>X4IH
%[TF;Hr<1?lq;%-C@j;JiH%X=W`!:<70_HdsH1=A4(h$A2_2<-tYTk+o7CG*g(5P/IXK^%Vj;]r0,>seGqN^BZ:<bJlkuNk-`6+24
%G"4VS)M==]fhuenndC:E$$fN>'r34kb1dT(Wm'gmS![pF.3/c&d0pVHYqUAG[sLLb"NRK1Rin3uITD=D]l_kL!dJ9UaGF>tLH/dB
%$BjDD6(1,e$+q\P6o&XgTTHL$1)fEDiDsDcQ[J]!A@[3^]R<U\&:Ad9fdUjc"=[9G^p5"9)O$Ba=Dt>aZ"X6!Aq;4Kam!m*RVj[J
%!#[U_cu'dm33I<q8sN'"&f&ls="s`hUFh[WhiA@D<\*b7kD5*:8;]RC4?od1Jba+3DD`l)I>Jl4gsW"`A$1OH?;uljr_u_)>fNn`
%7hA8^^;r@JQ_[mFrK8<.SAA-EZ;#2+hDlmn67AAfd_cq`P`i]OZ5%iRQsfrXr].35lGrrDZa'Q*W`70e`tDjP(GZ`*&8Yr2iq*3a
%Sf/\dn7/3eP%93`SsiF;?Ln`%0ict27prH2ZGas=VRHs1KulG^12R<H^nHblArUgT=Z]nh.aY]'"eWYUCt.H^``h=<^QK72c/p@9
%N^_*-3oR*54Zt@ek-Zf!YPf0pccOacd^s&=)*-a2c3--o(SYCnWlQ!%_i5Z^cti:M?ABg$7PiMY:XBR4,)N\p(AK^Vr8q;X$t%'(
%C7SEW4n;<u>pEPnZ(u[o:rS<<=b:eT&.Ig$MB@2e`ui>&qGmHr*fhcd+0OjW1=2dWdPZS!f@6-*_B`'?1>@!@fQIT.UquA/@L0**
%RqK]gnaD]rAk<h4l1c.#LKIJ@Zo9d;,$>ko*_t.a2P<(\(;Y^:S:/B*nh,T.QACLBUP/@TWb4at!YB9ZLd@sb<!`@G/pk7(Xu!sQ
%_sUPupBkpg:9G?\OOOEXb6;sFf)!<0,5d7Th?\#>*kL/mh_IsF*`Hk]CTAQ8c'_B#`?#Ua-mk?+@KEEOYKT:jp&T(iZFQ=-d@)+a
%S!;(4Pse.X4/]9&::Sd&"HQe^9+4!:@C7ZuB@Wp[gea[YWQXDD+rpt?\T>3?Ia0I4)DUm@E0<*BAQKR^!;OYr]Oq#j!/)ur'u8DU
%jTt8qD-&]UMApVt\?gINF-\]#.GcKRek:#^0g_:j/eIeb:Q]Xs'Z2fNC9^8o;3e0rp#7,mNDo@[e2'?XiL++E(1+&Q_[5?"@4REs
%(IiRrb%Sj\>'FVhe21NV<3Ygb.Cj5i-gekZ)pYL1+)3E#.Tu:66k*[pOT2QrK-4SMJ42rbIa>7b9U=(kF%k,`m2a$FNd/+3PT0sk
%7JM0[Ffkt-k&r^SAjauKTAgqWkOJ)'>rma<hE$X2,)%cJ&NE#@W0]XjWgr`R[h&H+8'P)$&/RQ4U,2U2GOlV^RutE)(4a>LN$5X?
%4"A6ZU"HE3Q%ZuXXP?1R:[fo$BQW?"KPZgM(R'#F/,R>DYAT'12Xhn[J<F203)d6H&1U_W9dsLRK?E"Q]l><IL&JkXZRc=h>]n,[
%h6.>GZ@)8>^<Ha:-[poe)#H+5e()ccUk@tMI#Qi948Rg$H0;P5@Q&/6dWV_de"TmOeLc'(R5Sa.!J/^5Aj\0o4q-=:qU"4a>LR4J
%j`>i<WDlW$c198ZpIs?3ENbDK^7@d0q&9_Hh2kbXaU43;5`_3Yq!a&hC8p:gfOCU()Y:K!QOG/2MVdVZKg!"BGHa9(;=(mA04R>q
%EWrL,0/B8mq/WCscV;k]Jo=A3Kt8>-<4J@2h<tO%WpYCK\f0GFap90A@?k3>/#3OE72):Y(pT*qjj"D0rtU.CGhRECYh!/S$P8'P
%&[i@\+J9I+)t/$VSmNrn8+o=bhJBckR=H3rKibdHD42q^Z?r#mA7XY\1+YT,?ts:+n0sc$KsO4`LVA,KEYk2K$Fjh=RWY%PI;=(+
%D)$VPl#+A/bLk.thXl,+D_6t3@o!iD,Y\#lR8lI`ODYPrjDGSN6H?HdOJ4Lhe;h6o%I`6D0Wf1R$(HIg99kog4pVsl4"8T0!F(O=
%+6l,_?^&>iSaOK'!RI2e.ie1j:OK90P?X4Q!>$tVC>7C8M;H[-eiu[r;_KJuVYZ^D,e]#M<un7G4/rJ1EIO0@l(&U"`+.`1TSB8l
%j'BGY)?`7ADD[Z'Z8j/oYskl"nT>/F?+PM1r3WTALIZq70^DLP\0:17<]i31/2r!BBH:^6Z^UTT*8OAn:fkaf%kD?#Dcn@7J]>JM
%(ag5EC<!HZ37>K3L4d`f^0dOaDN>'j3=V!VdQ^4cMV!VtKH3FT@_f$tD>[iu+$YShE3/!3AR\27HiPu9e7\PX9jF]k#._m:#d6WE
%<tG.!@dBs^.8Ak;K-2l_B*#[Z<OC00>#X]6X<gi!lkU^'/4&46]]>dtJWA^G6X@fo/_LJDa@=u$`eDY*\&`S<,7HZA6>]\_)K+o*
%`o#lFIf@/>4@k$ka/=j^7N!9:PtYf8il4qm[^4A.;&2a_,<]QAfn/^N29_`iSQ@C5&i:G?+ic`RE=1KFniWd8'jdE\SOR:D?;'A<
%+?<=\$1PkJN8fTE+h84kId`/',)3!02g1DBN-p<2"`\[R8WJ2QnHoiK/-4?JU!K(r,U\PKM[^cEX]UbUQV%V,+RsrbHc"Ik-Dt]/
%]A&R)<W9<RlNDt9FqWZcK3K&c+Yi9AP%=GQ;o'qOO]#NMMUZ9XMQ2?]J;mX2&CL:(g^+85a&8(9"tLWj1-GoRWI_IbnSnE9@Fb=C
%fNo3dJ]a5Kb<Ufpq.4e7nt#Ih3J0(gHu_9%R[HfV?higF*:d5A3h>ENT#j!(Z`>"?))lE)oqa5:eh?2,Eo`Cr]ruYld-=SU(%M:Q
%`3?dl>CYT$$0/aO,_RH3E0F)*a0fYS8`]53'N:qs`F"3`/f?b'`6h&1Bo29[/NV,*h')S*eKiG:0GQW/C>c9&/#r5]VBqA>fE'lP
%NMC2Tc#%.%UF1[\7a$T+mM5t#BNO@p/LCSMI;$bHJm4!/d%2V95<KMQ>H#*(W1*$Sa2]F"M7f.r2ClEUl-\n+>W3qeU/ep-TXcY:
%Z14#EOWNkY8:-NP4"Ge)MM(aC;20,_jH<D*D'kcLc2M[ZqPWZ2Ut`]giXSF,P!CeO\22sX<7A/^Y,\#Vq'c`,UIbm)]t>\7Mkro1
%0,E$$@(K#aT(1)l\@'Cd\XL98@?is0=\h;4<'hXYn6HQ$I>LOccN'MODSSnSA+;auj4YDpWDU_Y'!8/2AsGm`[%Fk>ZCO-UE4$Ni
%9/j1grNWTt`X`1rT_('0]EZ3`ilWl7]a%V[T46XH1Gm3gZBIXrAAVo_^lIldhKX=RP0=nGc(K"ROP7u^2shpp6_;^0'YiLDX=_2C
%<g&e:Vfrs!YY7KaW'I"QLBT7igCYI:f=AocFup<[@-D>m82+3ZSRh5oHVCl!nQF6!;78^2Xs?DX8Ha&XU(`C[S\X\D1<,pSe?M87
%m<umOTdEY4\OE?=.Q_WP0LM=k"g)*)>_!T?RA`(7b7ZP>)/iHZ&=%1pL=q=>)ETS,_qbR#_JEQ@o<NQP`<>e>i$;rQl0]uE3Z7$k
%>p9?r8(Mi4p=`aI`kR1tZO9<3Y;n4<=tY>=]g[NSic6^cL[Zm-[iZ[;^r'/`S,c#TN3h2mZYbpO%3t2-hkF"EAM$'MZ&[8<_j'VO
%CK6OCKTTo_[s/-anXPDC]>BITJ-6P']oa>sGNJIGn>RPGLDP'3JZeSG?4[dbpgsa,bs6%6qtBlk67,Vg>R[G3.@f,5\SQpH`Zh;#
%LF#Edh4NTN^cMTK!bNjL<20@*>IH"!B9cE!gGd>[on1MjdR1r!,E3\*2mF_2qa?]"mPJ8jP9T")7U4+&\.\UJg:^KtUKO=7cPTij
%4o8)`4Q&%F%?>thoXkH8FeO@?@(O-Pc)-/0qI?!o1Wl^'hQqQZ%D#B[m/_,Eg-n--=I.*.[jW8J9C8mlC5M6:0mJK%q?V`,CdF30
%KT,s[ZurrPb`f._a8`e_a%EnfET>DX)g;4-Fm(X#Ylf'Y:*`T7O0EA,O#&>Z?p+9+\riL2C,Kq$VPJt7/oh96`[UU_==2[^"Odj2
%M>V)Q2(^lnW-E<1<@=BE/n$>>&m_uVXHbmLjEN[NM'A4DpOi6nQ'a=s'In$u.'b:-RG]`=L'd69g#88CEE(-A;;'Lu1#S#E+`qJY
%!2^"SXG'IDJYWM]nW\jqqT3kK'!u=pS4K70-'Z`+CX%/qc@U"G=JK`to'A5=\T*'R,RDcd_/kTn@5!SQl#N"AW?W?UqK%EB=le:e
%=SjeQ"WGIG?aB$?qmQ0!l!^Rm$\s<u0>14S5gbts.I&)eqc)AgI(*q_dg:']&'I,R7C@:o\A2t>C'GR<E23%`58*+/S6'[NT9DP>
%/2$:Ym5;\QP.$oT4&)Li7G"*1`iY($-`$QUc*I-TU$DJ0kV2V&C?Z9A\m#)nprMnOH@`3$U8`f*TJR4)'M?aUE5XZCOZ8DWFVXkf
%>3%ZMVE3k$DH?rE=2f;^d2R$fSZVtMPqjQp7Wap_WU<d&&Ysu5XI*"$/t3na<?b`:8LgJNot5t_G;>$8*)Z#\Es,\J.m2A$=&=rW
%`hlb_;24A5fr&GNfJ>g)1XC&2B34[OZ3dF?&oC)N+3UU`q_P3e-[@kQ@3+XV-\lY89t!,N%;<=a6<Q;"O`AhWQ%^(4m$Rta"re3;
%RK2+p%7$VA<j6+3+`g/:i@7Jt?UUau$6jsc#Y"//9SY<V8)/L?RbL]U"f(?S]k]Tr[CH#g,m'(RQI;_nIS3Y1OaZKi#.Q0P'gJf$
%s.t`K0he=2?^b85=AstsZ6l8,Rq+r2\7VOH8`@1^f8[*aI7@geG9qC%#+U^I?rr\j!++A(W;I9S/]N%?:Z.SL:;0.@CPk:W,B$/W
%5";j[;fh#SM"^:8?N57A]^htMol'm,f3eOLJ,P^`oC)\G?iK3Vs6`,OIt.LAo2#A^?iKWRrh'5f5QAd=qbR7kDu]\6rT@PenfRl%
%lX,,>J,f)KJ,,0*rbqf(5Q1N'?iSm<s6X/UaSjT&qKMu4n,>U_r8#\KrVQWkGQ.?_48eL'pt7n3]Dh&%rJ//cbqFND<*t)aq:C,o
%fBp'I!aRbbj+#F*q[XSThZ*,\(r-"c'7Ss`s2DbPT?kNlq6/]TdpN.K^\ngV;ub06ro`s:^Q[4ijGU*#OB=D;=rX<()V5Ygeq+Wo
%.s5uW!ulA2!b<F#UE`3M%\nAD1);R:]1+t6\#)4/.)tV%W#3j^SKf<4p#B&C;,Ig.&%:\XD&qtZRrD]F#88X[G_abrQI;2@jF#?`
%0GEhl1:g"B=4N,B,ud:l#XuHj9Gg>lT.XV@,LCh$b!#7E2gLK5cX7Y^THTAT6eP!FA1?)dRf4fLG);CEUEL.`OphV'Wj*TNUEUbt
%L+Y5^\W/<L4"s2E*95;"l4blWL!8au9fC`S&6%XWS]H.a%uMs#I*J]B%dmkq!E8&:XUOhudLG_Aajg$A5#^$F&S?DFcB\S[JcXlU
%*9]1u89'h)UkU*30u?rr,95!DL^6>dfogGg$Tj/P+Ot`jR^hS#i[k<K-[qR/'iNEFi?N%]/'^Cn[ufoDGuYb9#[nIh'C&/,$,s<\
%FYdjk./8%;+YYtEoH!oNG&\9@?ER01#K7*5jH[%r(Hm`i0R!g4HfQIR#hnYJ_77MYo<K*47].K1XV.AEYcC@sqlFfk.2EPA6aiW7
%`pLPI+nXCY!;-.#B(ZlqQYO?jZ&TL$YcH346b$]g5V`ha00M5>%(-R)$![(pCW*bi@BUs<B`UN8qt;);)_B!qdT\e*jYc0N=ZMn7
%4Yfoac`?2q56pN5ELTr*=Q5D(>.RFQkVIn6JZYc!,hAoM'<jre7'@FCOZPluF*/,^iU@i7DVGc;XDQ\!9>M_ci"61c'<'_u4EJe#
%BhJ((mtqe(m!,MGd8HO_-l-:NNfj#Y+uAf$q$<B39aQN]/%-QgnA71E223UaW]FpU]lfrV80O7KpSgUg'Yr,H!O12ga_op#G.c'J
%l@X0]JcB$onptjd:dRi^dA[,jkW^e\GWpV)L@W2X9dQp/7\jb.j@XLLngI=7XHak?(C+YqGVNA$$oa,Ea/-;_6P.i]#uCe!:OtW_
%!YWPjKdP2/eeMh?Ki-'g]WTb%`t![tMOaF&bp*s)%i&s'KAlshlG`f661#o_,C&IIMahhe-jm>18T,HMG_q,Qd==AYbO(6kV`$7C
%mE[0V-1'nAJdgZhbM`tIIkinsO2hN.+pP!n$o1^\"aI4t@fXEq-8cLPl`'e<Mal8^Q(r[%^Bhm[a.1KEpt/ClPP.;O5*()-l5ot^
%nc)i\hOm%]?()Fi[\3IM_B,C8/G^lna@Z)&fEH2)qr6XZqo:1td's2k`uVN_,(a!3.\Ja`GW/L4e,7]A;^s?\:U^^/j?,.cEWXo`
%<41*[q5S&4Zaqk0*EV"Xa::s"$33YY@(a-a9]GV_#Fi_oO!SFrp]PM)j[N`T[gn#8aY!E;i)EaehZ;\&B&n>k?c7go0cr7bieVTc
%$nl[EWWCuk,]k>YqNr54+iV+0B(JG5IT_sDn"hH542FY-i]oNpWXMY?,-!:@@YC@k7A*Gs,9^r0'0UDpl0,[,#RfaB8`"dm1%m$Z
%)HMD[)Y\01Zb,eH2V6o$T`(\^U;XFE[$(iSI]o;7b(NAh<sI#uj%_-d:Lu/hD[7\FKJq.\i)Lk2k8$@#U7;&lXoRXC`/nIhH!H]d
%Z=]t9.>UJEk\Tq$+A-(ep+(]%,?ud7^*Pi'!F!p<2jJJr'LC]T<id4X&Dr<`pN?joqA$/`jWqOBMR&P)(YFJVb]jB.F:EtD_Yu[5
%'_dKqF@%[3D,0HQQ[fR:59\.qa*qBA^$?FX[f14:h/E1ujQCA+dcZe%gk:Hs\b>`\.DfG!(Yl!2qmT%E*`McpE%?0kkYd9tOO&&+
%"_?;V-N,ApU$;ok@oBnpoQA8!=>_m(H=$.VS<toOHbS:t7lgYnSSkG3TKFJ8QZM8FauI@WEGiY/OE@<k'H"<8a7T_30+U-q@PTFT
%6$b.T9%)@P^\ESf*)IlI<Mrd*?,T"c18*r+h:No-4or!cs5/T'_Eu\F,HF"&(!F*X``fug)47_T&S`]Te]$\&G%+QNrEs3^0[=r+
%%FkFU(2qSkRg2%+QJZ/2iZIp.Y9u)Mn?*%-o2@cfFdILl73*f1?-uSQ0j>aKrVm+f_JC,ZrND'+S5&W^%Fe&_^$a6giU//."joq5
%QSJW?H3&69<Ko>;36T5A1"8c)3eatNmj'lVX.SJ5aKnj$=\W-Lik[7aoFX\.*&P@;q/J*Gn.#)fDi(]KqDB8d[SWjbU+'`a;o!Re
%XnrT_D4f'\O[%Z+NUftBZL$Ou3D[n`W='\XDE&`ae*%3\,ngU(UIbV!$3DKi'PEo(gdNlg#9hhn7,+B7_^39jW&qT%a7FZff(t"U
%Tmkc3m-E"E<>pAOi_+8X.n;#Ie%\N!pkY7J:c6.a*gSN@2(OihaG4a@Uh?n212NO=_nL>VmuRZ,OW(KpON<si0mdbCMHo9]_UU(r
%IU84B(Hk*eg_.7s6fa3e?=*nf9rh[tQ>/^L+`Ao3>p4PJ52`@glLq^.41Kud9n$V*Obh2g!4Us-24!T%B+''M2Ao31po/FWg];.n
%CL(%hq`@;9o29*t8XH8!bp,8(Ak@Y=;[fJb]s#I6UF2`Op";=0X&'LtfqF.B'[9)Ab(c_u78]".+stt%[;s>M>)9TM;[QdS[/^9i
%7*<rs'G_a=B.5=*/HH;,f<S\PX!&%sK]%k&cjldffjW$H"6XU4ZXkVPD0@k7QrSA,cBpEaLkEB]o"LS-ib=Y$R6I=Xo\]n8VdE1Y
%p!PgOX#6,17RhL`9*1P2D-NO;6)%!KY`e1/4aGZsF<-[/##*%f6Z2=%&o-q&d6;h>;K,q?#9?2E_`#ALBPRSSI#A$f&j:BqH>#E]
%ZuP&tBi0pfjl4+4bO&L:=!>Ah[D-:K]XPi&B)U2&UTf6\r5&E)Q3>".<HDPkH:R?,=Akn=+O<I`D7\*Z)8?H;4K3#=4RRN+_)dqF
%mS)o=F`jbW![\kOcQBX7r6OH-=eX_A>4eG^at.qMUmi)#P5`b2oi<*uR:>6EYMK)^WS*--_6*+H9drrC/+$7_Plu]k//#o>"73O"
%(r0c2V'm%^qQ3L/$Lc*L(f=m_E68am!mj]\/OM-C+W$rSMd,?-S=:bPM!%d+'r'VEk8Y@'/)M^:%N?bt`_`BQ$dJ0&BIaRXN'hSY
%r.b=[W3l_M0>`m#!*:Q"#dO,<m;9g,-Q4Hl2qJ&0`*(:OmV%?ID]oY!0jW3MrX7XS'epi7!sTTL"R$8",D+GkI7TreBI=:]nnUZ.
%quJ_2I0k;,UE,pD@0a<mKVn*;?r&21>=t#S%b`LlF2%D:Pu!tH?ep(q+;&)j!AK,9K:KHPN=E#rogfp#pZ1+b>fRaPp"d%9c]O\i
%.JLG`=SEV`RBW4Y4p/!$@Z"8oo*Ch.'#lQ(7c840'@hoE5+Y'rVJoY\<\t%"IDLLA2O/-Kh>1$V3Y(LLhl.Jac2R&_GAmi`>-s'L
%LG-No/Q4+Q"d/_AeF7Rk'-bM9$!1%\(A)&1KJ]9%Dm)T.27l\b>cq[>()dL6EA:l/Sdjb,o$D;.#+16IqBEq`%@1AP><tUDL?sPO
%UI0d*p*0PjQ)GGBKbOb\qCJ47gj@:974Z*)UZr-a'"bP\!(M<WXWIdk`MiXoO:5CF*hlq7g#SVO,9E1h&SrU6!B5YD#TNgRL#:l)
%"K%n6!5W#Q7t3.LKPS*<fNNT0S5/0RG5Lu+OQ4]OK.q`Na$K<XO1+t3L^W\WAl-:rkm93lf-lc;d/2LRT^-TA]OVkeeGu7Rc@eaR
%"DV@2(81rkEg&+L'mAm61/b+YG:P2pq<>H2$M>$dp&k\5@oj7"3Hdm2fT_.Q(kpd)V$7HZX(k0'<7sb$:B<!uEn/Pc7F=qLp6NO1
%M*QkE6iM6A)+S*(aYT!f8#]X=G.l&I^KY$u(]Vr^#Nn^&iCfNBg1.t:d.6m]edgFG:bX,.n/$"Ap$:O6>K>f3:$7rAj^17N]#B<(
%ND$G)$8u*,C\[<KPf`%5!$5MTm6?+FfMqNTXC4!PP"V.[D6SA*Z^3E75PFk,LBs:/*B!7m71-Yb;+A%&BH0Tl:gV6"<C/rm:4D.6
%3-jY#X7G,_na1$gSJnGSf=-(9,"&k/A/5m5^i.V,-#".VXUb.QU\%?AQ6QQ8PfQ=%p='>.Dom_:GuLUHPu6)cQ,/1oRgD+%NtG>_
%p[2X&J3jVpR_s44j,Fh#&9B"13E/mle`<a?\t*u?:O$%*UG37-.M5=WQ?N'JCk^YVmYL.>796Y'(O`sq:i4#mGeFdPX>42hS%A8@
%h`tp<\H,!#`5nQ+>^Bm+BqP2F"fuN$8`:NlTh'O<\1%G5>I+\frW=(\9h-GMZtttq2eK^oBRtoO(Allc#6TndXh[Kd1`7r\]P-Z/
%U_Z=o@M@Boc\G@[q\m;s5j5Wm_ts0&^[q;OE:Q-&5[&0k$%d&M6tFIJH:K_'."fU,3p'X3.6d.#A0D>KYEmj#E3$F??^YK<#QrK)
%>eucgeuX;55M1JHb7%sL*p9c2U)'O3]Fq;?MsHI`HLr.XV@_-)d/uM=[^bX,(\ok*!X5O0&\Z"[eG4Vi;uoOQhTX7[]e_.=S/=M%
%;Xb0fA%92n(UJR4>R8BZ`s!5>-D:2ai=eb6ob(/mluuiaF!&DC7;Y+k;]Z?&ks^P[E,1gZAV0o`Y+WL.RK54mHf\XuOWZPP*Z"f)
%!Vs:B-nG?p;d7[JqV[9CL^$H_?0A@+L^dqZQd#+aMeWoJ@)(4'O^5uUWm>aU=Tg.K9O367;-c6P&<7aB<3X=\0qa_mm43S_&*t,/
%'<02En=$U?do$0>erO>e4MjB]HKiuWEdn[6o#_o>h;<l.8(D?*!dr*!RbsaM!V;:41t13@8+m4Xck(#iCB$U8UV?fE.7.hsn:dGW
%!/i\,phJ21cVHQ[T6l\NQfVMaiZkg;24rs=j-LAW7!LTKW/_3L\HLQdY;Wh`YD?H_]l&)$.mS-<UQ1ZnmkCYj"V+;jFW,Z$!%aj1
%cS.3f"5'q3DeHP*;0Ynl-CUrh+[=j(2RjeoNE8Oso@5!bO,I$OK(Q(G)(91(.X^JW%Fk`rPH8S*N/Se;'m5VHh"<1-7?ThdO1?]g
%G:=Buk4)Xu_*DG9S1h(nh?sRd._%r@$%m"IaACS6QSK_q`4NRP#Lc"J%<MT@WSNQ1/4q!DU+;Wk!Sc]V%VVE#rV8b7c\SmRXAPh,
%j:$e5oIMdBh*S+`.)sT(T909U!;En`RZ+oj_>rJV:?W8FE%YZPPMmbAVWHj2Tn=qUo)r6Z*W0VXhK`kEi1NRF'TAhti84bST)X5(
%EY%Q2K8tSB`.aPPaX`=I`["RFL^G<p%7>&7jlL8aSnP]M@`Al3U&0a.,T\'=Pd/k@G-#qk:F!FZ(om`ti7_:s`HPijg`pWdCXr,i
%'JubJ49?"J'2-H&5>,D_!u($MBPIO6W*tN&o9)pP;'*O)]%F6WH\rYLQ]Ma^9A_S9N%\h%:#_nq$K*r=dn/I"7TPT@3\(!i)5Jm<
%H5PTmH40!fF&.kn\KAcTMA3rX;03UXL2j>/H.^O4p0ap?ja(`s!?b<nV?fHi=`lr;jHY8He+Ug#0&tu6*bG-]N/I3\Tpbc:MRF3"
%YL3&:,iO)2CH6oa_Dlc5aS!&%*CGHV]ko\7-=)8NZ0InjBitC[)6ho-dLQ%N@OYsn3Fnse%E9\4ZZtCYA(QbQ3dOdtJDo'ibi=0`
%!N"o4N&;AF@]_bq_1=OHlr5?t5c?Ed`?C_$0$e-q4.T$8!,m0;H3SU#D48`1"(sjS<3MTR+!E$"S3EbhVDZ9'9%E\4,(4Inf&k`]
%-l#9ueaTmjBe>'[5JksQo2[8D9Vepa]T&AQi&0/jfV3ifD8;tE:;]du')31k/CO\^L1a9uDfP%YjX@$kC@Zm6Ylc38SnIl*D1g63
%PrRTu-VPbM-N#TZb-q5OB2Jhmj=9s,Bm6I8fc_on2*BKQroR)`^Mr?'(?j3-:ep[uKgOseW`o$WGJ]']9@:BC5EG7IrLIbo((d5(
%Li1,r)>smo%i/2bl(DPRN>6G#XG/'Q*#XN\BF0t`nBaIWT):%i$OVGb*-:dI)ce2>)*"_k0uB[C%V(GY,#rEaA4.Y4I1WO7'eN?a
%Rl?I"!??bQ&U=@b`q24lU:Uif5*Ck;DP4bb.sPB<&&M+LC3>oWN[ZFjBb8B<XToL"esQh[j:..f=f:MBn<dH?9\#@ugXj[N+96@F
%not3=Z)qWAW#V>)(<@6Y1k)Aq1$"2<mIDi\;^38"S18#TRc-a>UH<]e(je#qXma%)?#][.L$Sbp6f!""mm1JCnnA6r's_*C)J1&]
%1^E7cicd/D,9Ptf1>)FY3b4<E=eHg0$^MafiuF?R2]-m]g1pp24gIa(-d30]n1VfrPQE9d@O'9::mCq]mus&V3BFl;63*%5WWL92
%MpN/i<V>CHN#i=!.Y$DqjV.9Yr6K<@)UMM3\Q2Bt;^T#3#6lV7f#TW4k!!0pO\c#,S_<q5QflnMU_7o6Zaf53+O5rW^"W2'8r"%_
%[fU5^Um'('R9B_"1lr"A@E*denkhS%1Q*M(At"n*:&7EF.j!-`1PO!"^02n_>Wn(qOW:n=cf.kl_*;Eg,[j,4%m+pISZsJ"'*"K?
%LQV(;eUgT?o!jn29eNqp787_-1i5cP2ANPq[;G2$!?9`%L,i:HV*nP4BI/O*aWI7``q<IGoaYD%nLX?cB9*?0!j6L)r&JN)C2!f%
%:Ll]<j08:qZoqG$A=aCs\XJmSp>6]F3S!W%.^>5Ic7f?VLl#mi7u;T`!+!N/df1h490^p&Zl5Ok)<U+b7(V;\-Pkf3*)=2dPCRAt
%h_t@JH8KT(UmAkkXc8Bc#jo<7nmfp2$2H45=>c)aI:JO='&DaC8t?.Z?A6(DWa1_uUmn-n"<ZjF7]NnD"Ls]NO'pm,:^%W[6cN4f
%s38@*@VKYMnc1'^rlNT'&pM0u/A_i>3nX[*a+KSWO>;CY>p2Y>Qio2=N$$)1.oEWarF_/cOu%;DJC)`8!eIle(s4?@7Tg0[=:*[a
%VsjpNX<42EV+<"<<Z]-UpZb0mn[cN]euR&<nEbK$GqmVORP8IVNf?.]gW]Bp;3%21]-ua+OJ:N9BS.?[BJbkj8:^t4_+$@PpC\^[
%6Au_sU>$kE2+B8+#R\1W:2aAUON8V4as3"TUe)6$/!tjl9%+=a7OgL;fU3A(<&3d/3<HgKWRSmbk+C+Opu0>kVT\$AK4$Tof;/H<
%(aW1rBDno$h;]=mG)F;ZO87>Z\"1DDgd'ER(%%a"DpgXCYtPUkKNe`4NZW2q'$s8rFUX!N#IR(3C]Mp/QoZ%u-+=_MEJ-l%/RIFH
%J-.a=q2K=7BjaB!F#3dNE*m_5A'M.cT!j<%A)=E.$1pB8IX`S=iKn&H0gFjJnHekjJ:D1hhY"A.nYV$"I_<;J5fo35hH3M:P"hG!
%f)lgmH>M5De\;:a7&L<..\-W/iGaoE2RU88W^'Q]!q\I%q8HeJRf]N*a;g*rOW'4Y1"g=:*0`;;<mNmTpuka2M(4+Xa#6sIQ,g\a
%`h$Z^`!:kY`6cSekV@BU9+@GlEf3F1g+SL"Z(qu)m+*ANG#cjB`U0IrW4HtX^,2;D$.ZX2]dTL]2i%Er6BpZQ;g1Zo`(7Xs/KO,T
%!!$a1_JYPjL=jE5X\`?XHG9+Pqn>C:R(4ge6cC$:7oa$7H,K2(AM-L4i6]Dg\hCHK:0\iL"+kFgFUd6"#&OeCEt6-]%XU,A!0B/H
%qr&9;UR8NJ7^gJ%B5jmU11^:%=+Af_eW8CKWS;_?am.l&hQ))&j9sJ$qdn^gknjpC`bNmMl+M@0cZ^u7V@2m)h8'(;KM]dYjM/X^
%L4ZDtj%b^\**$@TDID=IHs2g?Fg$FlLqR_GRVG(Sods/C!<.C"`>j,k,'h$DJ/%ihC(>cSa#L84^oN:NPm)[)cgcE8!IjF)I1]RI
%2mZWYj%GJJiW9IMO"VeW$a;6M$UZ)!g8V0FK@8D^`?hNf\Fq60*nVjLApEk%bg3Ut:)ZlIT.=$(`2ZJ>HK<+8p5%6([\Sm.1pViO
%;@%[TS'[;7#3^SaT#BR^^Vu$$5'HFNhnfUcPIU1/`1^66h5at#%M#L=ZsXEZ%KG+>+*d0m*4K(6#6'(ndg@7%C-pdm/9e4?7rSYp
%1me"05C[,]RQBLa_m]lZ60UA;-Q('es&A>eMh9M"Ts!Bu2(:'>c`r@J87D8tC5tGI:"KubDCN./2in'.PZc.cDAga?@<b\nVrLao
%=,*9IjhIU^D'cD.SKKeg@L4s\1Jl]j.S-TK=heMVIMGpbp&)pFKg\>00jiZUE*Tqm#''jge:;pJJI$b*!4I\si9NI-KV/9iFbp(H
%;&T>MR#DA]TRRN<&a?t.5u^;U=lo\R^R2b`5J$m$pK53/(CR.]^P(25s0If9W.jl7KI9l-[M"n)]fjE:17me;VaPl;$H.b#>Y5uY
%X*sCKU:u_Q$it<mN:LQ=;=#@[!@/ha;7.&Ap%"aeo:4mSg+,V&QciK'Z`@#I#)s*+0h37k6VIXImnmY\/V#1LOo,q]e4ZOUiK/^@
%<cuBI]M*m-/'`2j./=M"<U0":2fmUi!h@Dh[84*NlN*sRroX^VR0-t)X?$=nY>JS!7VYiLkgTSXdUr@^b6<#r#HW8*&52`]Q=5CN
%(jE7o%3lNe)7_MMU]%8+=b&<&O-ch1LYq<^G(4*#Lj]RtF5O6g!uob!S^B<1]UD5RfH*/\lO'hC,*>D1QmQIOSk"!JC>mt4IK;XM
%$g#=F0.$qWmG;o=S$]Bll%CPGk9Rk_qjE4Cjk(-ZD&ftHl</$#KDhqghS+NYlm`a)cI.,8lgO@b1L`YqTR3r8$k\cS_Q1uu)dk3d
%oh6@LrVGfZ:LUU%1Eq`)Kp5I:"%MI%P#2H$mZVZ>/(<=n#S:ih87$(Ac`QZMi!$(md7$LtF.<_@2M:r8rOo9_0&s)hJp8(=YUDA:
%WoUMT:-jdA>K3q7N$S_*9Xi<@SPC-kGK'BR*\nd?H"O&?kDol$5G$?s^BopX+L*Eg+G3+W;7RMXP)-14d]L5l'HJ\RX!&fs`4+iC
%BT;^AaHC[e!VW2aD%o;8P:EA4^!>Pb=kV=*:\mJL1KIJ)S#\Fp)_6FA206bMCR$E]dp4_b(92)<.AO<rUbVY>Oi>,0bDQHcC:ob^
%hKH9_W#)#d1c#dF*i`?oE68h7d#QW1NMLalcSf+t'Qiu3kl)GPo0IjM(!rq_`k[W%>>8\rC#CG!c*_uk@+/.WJq=$E--7%5-NQ*)
%_QuC-`onM]s1Z-+aQ&hVodNX*[WIf4&?Eg3hp*W]7<tH!DY5SSl\f1-T-4U<A]?M/%:?/&7dBR%6(:;jJ4R[f=8OWmCt[B7%cD;e
%XX[nr")M]Qb!'EZ&A:ac<LUaqZ`IHOdY77LT\(Zaf5,j9[($onWK9BaTBuj!b.R.`?WL:F2eF?gi>:J`/bfu(<nSBdJ[eofqL*1s
%`[]a9^8oJ0^K?ZNb2Oe)ILrj:QSd15P,>OX7t;DOQ#;G)]`Z[=8sbbUbH.Xt(.HQ7@SDcnh$(i=]sE?6"C!i6"AiLnhX(_d^"^.>
%'=N3G__8%bpQR7D>.ch6DY3e7[gD>%LOcd5K/IY-;-QR:#&6G5M8=[Up*@=&:rD>d7/QQ_^P';0&rZqE"-8Os`_J2q,`I2-,)Fp]
%(dSl2HtQG1?rYs9-a3#i3/i^Jd:qCmJ$4;E>2rCM]1OtI0`Q*Xs4EYO=oM,H"^iHpC4E(N<C65i:[@d=`\+n)NJ$S$O--!\ZLsk(
%Gg+2G7Sr2=+/_B@9rA[2$&=0@i8+tnX^Rj2d-UlVMgB0;?8ARH15S!>'C]O^[Nr)/>d&D'^1)c=9ssV2FL,C<l:d@G>EO;M.;FrD
%@WS*eCFIAj,a-EY@T+#$)2n/gUkOVW((18!?KhsT[gKmI;h\,#f^bLYM8,pq]6`d)*]E]MEGuJ3Ff%5a^SlI.L;(q59?"+>$^&:_
%elN%[3&%DaFlP*R>%),(.co<Y$Egn);eN$r>aF7P8MpFP$*PK%]db%\hP-DL7Kc<?oZpCoT,%N>K=-ZmX/;n<eOD8CT^cE]cOk?Q
%fs<0'?.p/T#`Yk;+4_j\H:Le"-2S"FURq,R'MbsF]/b29F>tSk!qic=':u<;r",2C?EWKEEHe#If/1lcAFfJkFpNr%G:kU$L9jVB
%8Kf92S).U7Yo?&G9Oubj8r$TEf;!SmO"XV<gj5HLASC$GZ<+d[MNk(HMm.)P\S<V!GuTh@'<*hZ&6]C:1RI),n'rqERnf*>K7PR-
%@4]j$ibm?Q=t_Ol:'Th"C"I'?@biV4[*:RHc7qO5&\:/C3kgInC_C4%1Q9Jcn#k5mB/t(<-FJuI\r"U^S^$fT5S92$G3Yg7%#lk<
%ei!l-Aoe=;&ZA#_UNN%T5dk0%afXgOFVt(jZhT<-p3l+IJMon2RGsD$XP9T?i+2nqq+?h_i7fKFQB'^N'1`Ql*brA\d9*"K(j--n
%U+)^rGY1+kqiBI<POd"Q#7SG>dS]_C<hM.@7aHhL;E**1Pjf9MjeYLN!K=V4k@bs#7KqU,,J9=bRm&eOgYqF]GgIPmZl#FVPB,3*
%/4r2?*A.9_=?DLlN$S($aAP65WGA=cAI2`^S=r8`X?oM/E8C_;UiD-M`i?3X*0eVO0?@b4X,O*'F$^m=4VcCaEIb2[f=-!V/I?JV
%+P,1gJt:AHb_]l2%@dQ7fk``th(`)XO,^GAQklu1d&;[kc!f55Xcbd=*-Sa7b20^A&`PI_'/%oM^LL?bUbu#5GTlGO3o0Re`sAi"
%DH<bFA;?K!Ub!`ZMK_UM;.<M1mg)G:>lPYW2BhKgTYfbUTeA]lAp5Z`&V:l%)kqF;0UV>VY6MA`nJWhj4<8#XALd96fP/YP5K!Ku
%ig6nHX8rF\D'#I5D2KPmD:@.M;7Vq*`BUPi98p!AeG\E'"deXnjS72JGPXP1>$:m'.<,?@pFi4LoKuC:GAa?>!fg'Jp'?0#Z,tK%
%J/_#AdNhi;P`[Y<Si!2=!;)*slo^T:P$hoapVMR-r.G>nNjRCRb2MtI^g27uT'eHGI6`uETgV+8jGa;O8-]r+d5]rmpiD`@@EF!F
%3(;XDPVX*'PB^=QQbY9fe0[?io;9k:/#Ba7StVR2C9g9;S33`*RUH4FQBp\P"@*]R;$p:nbr^H6Urslj#G5ES\H'k&(bq/kUP_NU
%aj7B"()oglj:QY!6h(htMf1>c0hKt2Za(W<D+QQZ^R;'(BUlu^2;6Rk8"e<OrpB44+22)Y-Rd2D$P6S#Inj[am!t8u&p(r%Kl=V>
%4P%5,>rO+cT,2Pf\3AS?B)nZu)07=)AUH81BK-)($j#7tj.4lfY?R!\kF/XW!f0L#(tA[484f2^o1EM:;5"<R=8[3A(jO"e/SXM`
%(bff^fBHW$N9$#aqY)+KU,j\ka(ObE&1tXgQ)tL@O3R(7A&QsKL;$9IH'e7J7qjD)NjdQMc$prV+r[(%3/GMGJ8D"3$dnG?ptJ[l
%eu-RPMGZ&WS&flN^qKd)3V.kNN[i`Re.$RXpV4kbPREa9aH<,B3:U*=VUk]@ojA*YeFE+I;'K)W5pS"\KEi_ge7o09A!Lm(]o)HV
%.C#Z\[">mUF;eRUL*hB9KN&[%hS1$h1hT\W-4:9>5R<d)N*R-udS"03(4]rAF%L5]98V.C9PbsFEJW$.0s9HZ!9-Vo[GiSrUo:6P
%?.p%jR&toS'oYd`(7CG_iM:26<h*@sahqd>%E>r]J(\b$CllpLRt99m-A2VOW10!G<^q2EqSts<d_nZPLBlih`H%uI$1s(V0n2m!
%cc*\?KgqV3EjeWgn[EarkP4_m=oN4;,)\Sp*C@0r$h*Jt7F1j8]UO2\lAX*/ZXY52B/VM&5iZq6`X$6/fs]KWalH.s(q_LZ:h1.[
%2CBY-g\EQA\E^DoqWZ<13f<<]A5f\_L`%W1ji]\k:i&\3eo/fq]7"kT6_YUk>M&KKOKi:e'8F>7g/EBjWJp9,b6-$O"e''Z[MHL?
%2X^sX7!W,\-+L>3X#7G#W)MXqAe2TF.c-7J<ADi+j#W[HeUZqBcATN'3i\<@(CgqAF0ljh@e65Pcro@L6,aIsKhnEde`3^cS;d,!
%s,rTC/Okg-ga*A+>8oe-@lI"1QK<RBgHc,bHC(qD(SBh.5@*gJ<BKdQ8[\jBC]F2(^HFQ+oILN_CF%/)YP<S-6rT_6%"#*)Rb%.,
%+XjZZ.WL*rfd%F:K(rP*`H"YsL+4B?7.MYT30pCM(PIq]B3e/%pH?uRCuRTV%=9O+7CJ;KG:9o)*tCceJ8d<P.6XEtU8R]AoQ^7n
%guN5HnWCG9,UQ^FUbu5:Mr6=6K7q>L!6ji&A]G*`9tA.gC)EjI=D[gePaH)1Q)/9Epk/tq9-dGPKs2)!=qI0"O-M3W<uIqMV#"n=
%Diqdp"osb`?u>aDBVOq:fe__j_Ngm"$B6p,Skt`OU*e[r/2+bjni%Oho'A1KgR')P!'sh5V'DI$@\SEE:NR=\W)_$r$-suXmmH"r
%ks1oNU?i=AUXm[d24@Pe*9&,&"LAl@+'h=Y!YW^T,GlY<2(Q7S=E*n$&iV@qe2$/1lhB%<94Z6b`K46a?%#*b:S%lu8u%GkSP-&H
%gfej?VV';skL-af2cp7*-.:SckRf"96f^OdQ:UmenY<N[0*JmEp/DY(5W[@p_P993P^.m\#q5VMgcUPT>,h'?A8=9&htnnf,@=N_
%H'lns:E`U@MVDp'r:(d&_4BZ(ZG-+WWYhs_186:lDQh`qX;n;;jq0UX`sf]#('3'4e`Ru85,V-^PZG03anNf=[C[=`,;cEul%#bT
%m1He^Sh3r8D&Y6j9&I?q@,A$W6;EIY]WR<DWcfgg$OYBG`YJEY'Y(nd'It]Ipe5Z!S-8>O>p\EK@^)2TjAoP4@LNgL]5-$mhjZZ]
%e?mB5P6$B=*,'>SlOK\oT&@^_%7&WMAg&nT@QtUbb6"-Jc;IqRInREq`6S)&B@)EaIEtU!DH4?j&(F/fiYU`Pg<m.,B:(G;j2S?[
%URNA;b#I.8.9[%]il>-X(V/0E.O4[+GlT\Q9g8Ua,P5JEXJ!h<W'5f1GJ"iJSMKlMUej[qgG^rR71;W*M?5]EXdKkp$]S[&kHnm^
%4("E:XnkNc`XJ?F#TeRM6eG.154]0F!'DOOJ>W2E7V9Z^V,p9:GpO+p02q%[(G@WUYtQS\jk3eQ#Y-EpN56u/:7:hDT*p6ho?V9)
%'7`9p7is"KO'Tli;sAGMFA,MNWT".D!_@+AH\Cf>;B-*(Jgej`N\Ir84%5+D,kodPc8_m$Q=Ot?`Z`En,X:%>r!.ff@Uii-<Eb#t
%SYS"uCr1/O*)#[Cg4Nao7<j9)6r&161u<Q.5FdE,;X4GJHQ<RqVa0at@m7X0ht7FQ/372fK(&DIWN=!T"fl!I4+]`4#EKDh4ae?F
%rC:U\J^@;+\9i?$Iu1(FOd6?-hfT(2-M&CXr(gcP0X7n697?2;h'_n(@],J7rN%["P7ft`]H0MG4Mk-49"RZg5X@^?`+24O^R*X0
%b?!2$"0CS=.Ru8SIh"QN&8<5<h35UEpb<U[I>$)KG2$b+ISR-,c/kj2,N>'X&b>D-\@Wi"7lI:c*Lak;&S=<Q9G$$I)02--E^Q_6
%2mLcJ/!00X3YIS4rYEhq#o$(=94\qY)G<-Q<Lf@13Xo!*ESpkL\V5OX&4F%n<PEA`iUW<=!Q]=cbF>\sf"pcdqsCir7AiZq?jpV\
%?k(KDY0l,[FPF*LWAt(#.>gh2%[dN:[;[8ac2%YQSAbXclfcXF2[8"(0Hr#?IBp<A08e&"VQAN'i=Q+X3Q55.U0WJ<^gu%BomCr!
%nEO^?F,=a/FSdA<6q+"36_@j7f,4EN@05m5>KN51e"dS-L7T\RF)PZn,K'=O`,9K[3lm=M+g<H'jlsaH"@Uu:YR0+cIW3%A.f=mL
%n`7beMRun;n7u_n)4,Z3KA6!E8'd5:%l#t*b'aGRjO^VSWDYE&[KK*a1nkU9FU'G*o-;M6(k!ufn<r`lJf#fRBlrP?!2)AXBY<.p
%H6hh#&M,_hE+,8MHk:+`Uljr?kL4<;:8D"j3So<>PTm+[#Vo4.S!t%k\##3$J.cfoZ:/k_:f.$e),(I;c)P%n2\*:b'OV//E_T_M
%"R+9oTP2$!RsM3K7LDpg9#8U?9SA-J:L2\dR2H<lqBbL@c9r.GmkFOlBrNg58LC4-I2Gh=e>f^G/o\pfAX:%^[RhAhTL@hRiECk&
%-:^l;*%=1m@mU0C=n0X%-NuQZQ-Y0S-IFI4P['RR`X$?Jd*)?>NAF$H_Xh!W"&ZXSL*stHY4kZ$$?BA]&Qf#4m(I1u*640n\=^f8
%s.SR]Rl0Kb!/u+ihD),M!U=f_P\:2rdHkgGenh_3*]CT<NJ8_D_uB@(5K>o2191D,%o@e2OskLEb)2.%Gkg;YY3n7rdN",j\.V2f
%._9oK>d%?NfJ@pdha"238.&s5;T_G,<Kh->0`*\`9`i@sXWUQF/Mq&4WD65;RFT;p*23mI/B2u9%XF4'b@4%9*@6e.!B5n)XUaY8
%Md1n#Z<!7pnu9`_D'PCE\@:S$X.(`c>V<Gm/Cc-uO`5kU7Vl!_7'!8hi$%iJ253@5ITZ_>X`"7f]6$TneA^nX1PJusABMu%NPeD.
%9bJTG(<%XMF<Oe3#]n7jT1V@DCrQ:C1%0K5T)ddFDuq@-6XErQ:L%hqQC;(>%9OhYO$TNUeQN7j>,lpC=@PNiJnR^`,;'m5akSP`
%(_[e+(f9WSO!rZK(?T`A.r&]@955f=LeM.$eQ4#0Sekpp+mB>jgHYq,H7UNC1u_sc*la*S2,R&Tn1cn;e>.O5#gUp$OQ<[G/'"CN
%Hj_%4?W:'i=c]^#'e-XSOciYT(Ap6$q:+J$p!U,CD,'W8-l66OA<nA(M?GG]PJ=7([J7kQ:^AHdMe8acL&/=p@**%LA8)f?X)<pV
%2%l1<a/KeZSm4\VG]$5@.KJHHN\-c=Uc(MNggX5Xp+H#;4h#<6(8m#JC=W_R.X3dS>hUr$JaQNC"@aC=DJs"E<XKr@3PA*+o@=O"
%N@2l0dmtBeX"(p,4I*35jNsZhN.K*MB2hSgpC'a:o\QQ1ZFO"9aM8,).2A&d)M8te=[FL(\N!X2aV634+NGOk"-^399^3oKG)A=m
%Za9NV&e^?@#7e;GJa8POV3nl<d0qD6-BgGuQU,L9'^,_Z#\>8*!o&"K-Xe`YB_qr=l\Tto6&GRN,jO"u-s$Du/;9'=%nBN:1:<E;
%D6=Y49\A!P]O=Af(Wt=Y$P4::DW9Lc-u)j5^sF%*=M:6C)4nTNe,)aqXsAiulQQ6g[dgP*p5*WkR,fChJ2g'YKUs"j!X9:sA5]'P
%jZYhj--_V8mp2$0>p"`-_XSBK#Y'>iB.3kC#tGX[Mht3.(b0di801`Yci#7+"D!m_cm*1.MOLQsS*$#EY88%QcE.67pOVL@bQ^`C
%%Zm[F=9W:9WHJ'NeWru#egu_hp8+Z\B%@KCXfm+L%2jSVDpJ.UoF`Vr6X2pC:bBDgB7^d6^/ToVVW]=.dVeU1VK<N?2B$=iScofj
%JPHHJ;at;0C*olqZ?S-.(G(1k0f*;CiF%^YQYah6?ojlLDn1B#iB&=p/RS6?;[;<VV\-qk/1[*"Rhh8Z/R92nMM<rBhEi:VL_Q"S
%eti61Or+NN-t&'@mVIeF$"h4$P6["d:7;V]7/\+5^:?^+#7aZT5Qnjr&rP/"BS<uH`Z86I3?qg/L9$VjH+lU\.P]%.BS?*,ig'aI
%rdFma8VmLZk+DC;8P%D$>l2FD5F_rAXU5\65"b%CfYMnKH<]%_9TLsBY[hO(;O$Z3^ejl$rSABae?is?512$ZC?%Fu@E2X^TGqn;
%JY=+<^Thh]B[1ou;5LV#,]<dLVsIhAXig"Z1`c^QTFZ\(eE3`)V^c+mk:`4;Dn#HQo4;>p(>9Q!ANhi]O:*I635(**Zn"kCopW!J
%;N1fT$q,kV4hiiEpdFCGn#=$TeC^L(iG&o=JOiBa20RQ%UaC'?jB_mi5[qDPUZMRoSN[A>cLY?Ddd@1g`@%;]'G(\U)<gEm1%]Pg
%^n[/+UFldK<%LU&!fhc;q)bM9Q5Ha?8P(?E@lti:!Pp^C?]3c%K'>,n0%RYu-OroY;h>g?R0fo("F#Gs567jSA4G(sj9EbKG'I[&
%9G<':8<eBqQ,HuN"sp]Q&S:+H]TMq$BZ('\R"n'p@DjFig&cfDlJP>^"h7arQ9(UCCdKUK_so:+gJ:.-QGa.T9M/G*a'a[!h@^E8
%Z5G#hrg/^mmjteCp/XM#?+D4mU>Qa3W[eZ8B.X,r]ET."f@'K+J2;pFZ=E/\0C"B'Yc#C$HP13t@V/5D8d2'%7E+EJ(9<(#J?Gqr
%dtW:i2(c6O5M9=G7$J\\A`SiV!gIOhYY9q_8h./g.tCP9\A['+Fdrk';Iu];.AjO?j#";E$-Ki<I%Y1!"q()dZ`!Q2AgLZ6U&?Za
%Z2U:`8.?KQ`HB9DVJ<u)4jBIiWMZU?$rT%BM[b)0<X#?3`$Eac.al"h&=)KZ3r+or\J+us'8Ka3Mh7PKN_EEX(#[3:OZoAPDu3Dq
%<lpmP`0(:/]^_U2NkdF.C._bd6j3RK^4T%e<n*i8@4qUS>ub.3$MQsO1r40c*^ClF[0%<b0:`Z_"oYtQ/22!pQ!?#>2\IgNU'[4o
%cgCJ&Z3;52XTTdcC/&]3I#pZPlc\4(VGAg\5\=,9Y?<\!,_LstK+`K"NHmj@</YoOU?[%*8gl?<e$J@W$Xb_@RFr+XDqJ9^=W;_>
%nj3^H-$pDd?j^Y7U8-+agQ)p)K2)hMQD@O"6oV@c<N!?>??-s&TFhaYCh9b@E>4*HK-t_!+A7G+p`Ttce5e-M5bo]X)cX(8)H2*9
%FeB1E0@.ND\@673&>S0H*$['4$Bc>r5V`:\;4E\iZ"Ks=:Y4$Zj1nR4,Jh&n!B6$[1DL'1%gA#/!#8uWZc:)(6"uu-G*>t8B;lVL
%A^Zr;j!&+`H5F&t-kRZNN,B6I8+]O%Qk#XnTJ6$-fG0Jc[iu51]`d3?"E^aTZX#u)2[o7d.?eHr,t<gGnSEH^k;W=B,D(2+'JDM0
%-\aRAJI:^jUdqfq9>J8m`!8`k,/e_,'nj;\NdG?5c,gA",H*AG'9-8*@X4.?/U__\.KR#R<[dVSbE]a6i[,VWJP=c@TYrs_>64%t
%=pZ?S$"/Hacju5_(!tsRJ2aNIOjje@L50R[f(`$C.(!SGM:da-@g<RSn;"]$&T]LJ#UC38?A0_JcI.?S1E98g)]-[HbFia>XF%!o
%h?s]oYRD4KgZHTm_kG<"NtNiD1W<(m$>W*D9SZAC&T'-CMsL2+3cTcKF?5-#;s^Vn1sq-T`-5M(5b<)QP<hV3-Scnd,#be!];%60
%d>ERAQ6<pkju!\.,:eZYEoR_ToEc/T;5<;p8Xb?+YW2D]j9EdY!^\'0URG0aBJcbk"Xu.a<M<@"PcQV)n5o/"CnaM_7BNHWd[:h\
%'81VsgjY;P?&I+?[L=dYaP8r`>%?OG5"_JZ".HoGO^Aud#Z(/ic:uD=h6c+:0GpcS;9WGc/7AT),6<S?.iV5r.hQYhUJ+4I``U$5
%LBZgJ&>f`9Ud-[t^?KHARPbR#6@f7jir7J$:suD+#+LQbB9^K63=R-iSHU_'\'0/O8RVq8g(Z'E,mif"mg`/5M^ZI6'6CW(NZab8
%^O<^1W:uV->r(!P?/URUe5[`Uc%=%]+C0Y^CP@rk6tR%B&2B"WF35:=e6Tl_;"D]G=G`0NGf:pZYip/)PHcie;dWQrg*%S>*8^od
%"?]eEK(+sbmfbBK',/UOrbsCm@`]2j_8)mA-'ZAf&_sPuBCpJ'$NLM-I']>4@$=tL.M>l<Ze-Jf+cXbjJ"5FXNs11e"MlBuQY.6X
%kS5/mn74!M"]%/l_Di/:,fcYJ8QIjcl;)bc+-,MN4b:<>-^%=#;-*cF4<lTPTig<>'o.l>:l2K3LB?-s!NKuAluYB5=oq0<SKJ`F
%4q",2.JBdi%i4is"_d.j5X-)82L3,ejP)0[qoOs*-ZkG;Wk3hp.eO^11!S?)$Np#g@n[:M`i'AtV#!C^GJ3)d*jc:8,7qprUCJOU
%#smmESe%.rMD6%L@>dRD^gf,e;qD_)jRs>Z6?>J$LFkuRW2+@qfc9BfL2REt2j@RFA)/qS1D:;@kSf`79:5`_;QM>e@$rFaLg(\&
%BTri*,?Z$pUb(7`91(MOAt5[)pP_.5lF^?tPHA?>W'e&co.=<g+P3<fL:Hf1iT,jS88V#o-B4FVLFHtp.h<N?AE/O/jV153'g-m-
%J$i*OGRnf1+=!P_:Ph6#Hg0TP\OTsM!T'Q"=]tRrRTdqc)(6>f]Y4`XKBH88;_ntl`l"/D!g4F0#<#ZH&q]s4H":BTlRVTGYui68
%gf]))HdlLl[SQ;1V-3n1`1RcG[=6?b"K#lNH9^sS!T*]9PoT\-Mh)aNRE4gTmI#`A?F1%Tq[TjaYh.5pfZo2[I!5CO/2<J&:Wi@k
%8]Rf#$K-(N\V8CFR'>@N1VO1GI1&ia[s\Z+cktAr43NF^qQRX?Wr]N"!D@uKMO?97(I6.YnCi(An6=Q:FVGhZCPIh/HlrKF7;GrB
%kCn8N=95,-6o>l,D#l97`,/UI8C5cV`dkVOWtnCg8!&V6J]&8iHEpN90W"84,MGb:2b(^,31Q6/P@.\=iT0dHjYKX[*L+\L&Xgo;
%dj>[&Y"*6dasJrgMa<1Ci6AR!"#O20YY$Sg!J3AG1]@]?6P?Wn_UZG%Q8F,6d/HWe]q>D)/s'GLornMFh+$H'p("7a+G9++$\FRW
%7UfpQYOm'!CUe0O$pkci0Pk;#Kfo7f8g#u.n>o%>64_Qp4*'g2/;o(M!i&IH*mQ@ghVc&3Wh]s'RU`j,f(C7V!fOD9/2+Xo$FNB@
%hEk:1VrfCcL?9\N`ksOF=`L=t!#R2;gEem0/@C,T7^C9]cnIWlHaFDdOjTnM4]N`IF6ROK*O/KWMR[Y_Hrc8:M(E'ppnDZK!1D^[
%6#fahp`%[/!JKdi6S!^2)/q=llA1A(B,/J.+?m86EY\p65g/3^fZ=8VWk"3H_.EsLj1CEq'-TO>]<4YCf1Q[8Uas?JH)gX71FW/4
%n4Q/$C:*]_QQDg/UB[9QX-O0Xh*'l#NEQCN7G,4F_J1\WF7dbW]T-c'"FW2G2eIV6XcbVB*es%PeG#!U=%H3J0HEgS',XF)dG5?&
%><ZO[/pe_#&6crNUnrT59a.70?KMBDQ5q+GC]J'$$NmcZT\T6K<?On^K\^LQ:k^C%I!gE.@WBZ/2C.O$WM'=`qP$2-KnO3XZAh\b
%'s#(?p;p$eo1.TgBc$;IB@\u?$;rQiqcG,@@ke.ji%ZmB,Coh\"B>FQ,V]H.O!"H837:HM3GQMhF..6U&kp7t]i^m4Y,`koFhga%
%e>/\+C:I@)O:`R`V_i*Vh8TRcGP?")doPtTMkBWO-$KXm-u9_>3VNZ@GFeJZJ8Z]PkA;D"YOtM)l(c-:*^$==:Bcj*".Y(VDs.=\
%CbnH%3L::.KAKauWrT_hTO$->lA0)kQIeOY>6VFNBhU8CO[30eSdbd#gi,;=ZNd,eHu,M0*()&tSTo:GUnA;F0%&mrD$^+e*hr=1
%7&6AIFOKLq0>=oB2lTG[dVk+Xok&ka*$&46q`sVh71Qi#_65g>$0neuKtgd;,u7rj(+:\45p$s#`XA1tEfn:kE&.^><:0f;Di_gi
%0&;"kn[+/>97Wg-0JGsl&d16'Md0o+H2^D/a'>thL&PKnABeUH3HB<UCg!g(>'IoM>_82QPG=JZJR1Vr_-$e>"5#QXXb9/Q9>M!A
%#m?4<0P1e<#Zij5&dCNl^fH`-UT]!m>h-87SfX!W*+6Kn8X&"j?=/G+bMKi:CF3BPRjE^LMLfbE/B=bRNhkBWi)B(LBM3I0OFJ"I
%?pZ]<@nEq1g/J&8[a=>oDXVG1VG1G>Em.glEZ)ri[?Pp@am5.5No3GI\lcBI"cEhe.g'&Qln(@a'R1$,DuJX(F0BUN*j)KPIA7?c
%hEh^65aH-P'U&4t.DWaMOLZ2RoaP0:#h"DuP.SNi;o"O%I6fV)//"2kZ%\d[.]C`#aNn8uJGH/\TVE]U,H$9HPcI="8Uq;6X!Fe]
%=o>q!5l)neoI#lEE+'K?%9l,S0*$@[_)Sckjh1Ym)j"U+;l</0c:HJXO;m(ElU]_9:uTD^FeDjEVXZjpTeJ$P^2sPPi))bn6ENR%
%BbF6p82->^=c\V]8G$=jSDJ=t;k@sdHa2>Yn@$Y](s7iQ$X#9W(p9S*iI:/SqE1qIE,VpO6(`CNLpL)GXl-?i'FqW?&WF<M8,A]*
%8?s',in%b+!*(efq/\23EL.FkHF'93"muDa:.T70f3^S@!toNdZf(!PY][ej`0CmuTg#pu.]K(lqpZ^r!^c)*:f<[f%6Tck3)8cY
%;\(KQM#(Zc`6q#=RTG5scMC#C)X2re:__/b;X+GL1_h:R7uBu/dgJF`38d!q?A2q>$VT[mA*,2`"EEAd-"N8[`.+d77n$f#_-dE\
%d`u;OV)EX.2oXWsT4<+m,h!=Gp@Q:B>-Do6?^,XuQ+tf["GIUYr('j+Gjf%>0j"Y3"\*\:;lH#J<V\J5oZ-]FXfAAC"CH8P-1-rG
%!gZ:c&emUN8I]r0mfdrl$A,1PVm5>8T`hY).TiYq$"QXh[l,AY*lLZ##)RaF;\S658s(:H\;U?6l"hsqdiQZcAdohUp8h=31(Jjk
%rR":Q,0;TuFq#^="+uUM')!ca(^PiAGqWU*2@PMerH5!>C8k9C3t'p5_Z3*kX7uba0=_3HZhhjnQ=O+Wk7D<'WPW`,PhE][ZKDV'
%7/$K<P5>\fW.c1s$WJD`"%,2WP_%tOp'nhXq9g3L4*pu>f83^XJkWUsdY]E`Elb;%dh;L+YB\9GB.H:DLV+rG;(sbb5bn?h0-U%P
%]rUkr.a;YM5[V?fA1fJGqt.OV[F:/QS(QE#M*e[n=fFlIY8St<9q`B=ZmZn"\*10Oa#]3K%4)>?!Lf;VGc29n?n!HqfM%%/H[:>8
%NdDNSPBq/Q:+8d99Ut'uP,HTPFcaZj)Ah9t]=WSNpXl-RC&E#=M4'(U]k<q49*rCIY:?sWmL)H*XcS_DdPJ0sBP!mt@=m$E"Vdh)
%b*BeD]nEE-B<.2o"'f*8oU4uc/qa\X$n]"pHU;Vcbu24:*]@.>Q&u-s$;$2-;Z\jL6,dN:"GCXSghOk+Mr7`*J5KV7@MMPQ@]Y<+
%L'(Ph<^f'/X<$55CtZCdnSWSR9991+kFoP(WL[1s=@cJ1WZ<@k@LZL-,gD_'Ie:e"9rJl$Z#N+1X7BqdOKCqlAsdpt:V%6VNqBga
%i5P1"X9t*c'J]94eR:C4htE<_oOg_i,,o+8CeNfR9@*D`3%&R+Y,6,ukXuf_SRd_#JI"j%dR.c/:`IMi1FH7e'RrinOHT,d-3okR
%\T2;WV?##BkNan0]G#s=#N2=3G*7]`Y#*q,[).f=eX@U]URPIpr2NgF8?th^IAK]NR"h58]l1a3@u4?+<k_2XW#VS8a%SFKnST#C
%@']/EORrW_0[DHnRHN-/PrZ50qj"S&Q6*IOe't%oUeb3nUDonr<bFJu)Pocp4\]5*9iQjfam(,5NE7)Z3,[C3KmR2GlRju#2*miM
%*Qjm5T1$g?^5fk]]^uRVh\$Yk:2$2ZRZ03[iBT$r&KJ_FFY\3/gP"AM.)H?o]M2:jMtLSW_iE`j5Kr2,R-)Ql2f%F??KKE<0l-B,
%<'[^abl:M9f_/UFd=*nB*nrV0*snFH-?@';dds2gKqu!/Q()CS$q_Xm/_N\\PB+_7bY45^'LSe10=-pq8PLpLo1th(M@2@IG,t9k
%6r##Qg8oSKdX>`<_ra<=rBbirPuQHtj;pf7&J&tfp"2b%0nB^3J1X@Z*aQq"ZQs_!T(u-Oh)ORdQVa(f[:s=S$9BJ#VK9aJ9A6uM
%H'.tGC(Nn(!F[rq`1EcK?-$Uf[[j":f6%`'V5W_&3m'T\8KMaX&7<Mb-K$qU=u-MOe;$?T5QT#Q>/g`QN+?_)<7GA-,]n>9`Gr;@
%JVjC:B\D!Y;?kC/`DgJL"[8r=Tj0=1P$C7Wf+J^5&)adueDHGM%m4NJn`C&E^]I#h$_bN-9_soe"]9W^22@d5)hRH_5]<5=:/b@s
%(^PiuNRJ9PH@sWd5nehpjB'!>1r??u^!Hgs4c><]Los;/=03&8\c#?>NWqsiRH1Ft]aC(+$+OZSXphLm<*3[^-0n2/4eI8g<VAK"
%''Vq8e+sV9g`/S9iLSTjFNI''i"Di':Q.(@2CYmilSNUMAm/dL>d3ZO&UVFXi!/_c:m1UJHp^`!BF3UDd\upVQu?C^(,@@<eMDmb
%-8gf.aTpsSY3\UN"O]r"#"M>5j)kME]8n8F)DqIO1sgZ]\:s?p<EQ5`<g[FQ'(Lf@)JlU3j[Z`6CJXY/O[9=O(u->l!nddI*00;/
%"7Eh=SL!k2Ae$qJF<F)H,9qarG`f:r@n2Ds$;!YD!2"Qu.M7u:$,3'$iFZaniZaeb(>V"$G?;(Fb=,QX:+'aa,Oqd:k/D6c,5/G^
%Go&58pjhmck=q"W&t_-Z1t#r5:';#X(;3i/hC6JM[&<u:j,WU8,=NX\aHdl6*S$&m2jtdq.Y0--CU*rlR(`TGe+?I',#0dkklWZH
%.S/D^8M(X.GpNb+H!KuMm6mX!:Ne9ASmHZ^BBWDf"`I?!Jg(53Iu?]+s,B,)e8<DQD@/I%:XI&G%J7u&Z6u-4=M0q$k._am3=rc)
%1*$^(qGjP_[jZ6r"eau+JGp*.&;(r;!jVN7M?cL-@?^XcZ8+:[-BRU#^<MX7KsF=(hF*pKVfQ"h"[J02D"k'YWt`u0mN;d+@sVO$
%H+RV9`sJkPL%r\fje[eA2`?N0Tg0pB'asacM<tUU8YTJa?PL@iaV0M0Q!"iQ3Lj\PJ^"a0M#h?"Xc<hFdfM!JcWAod.'-fW)b_!8
%?JTBB*Z)anU(n+.D?nY.MMoiuW!H7&.[u5JmqR`MX;nb9:l3KI[Q>"X2K4Wgd[c@;?4mYc.9d(U)tF18*6I0j".I(Z3)rKO+c/H$
%ods,aKuWX/OYe"WJ0US16UZ'TX3c&jF,`bB%l^L_FNT+sY9Mlp@N6WPNJtBg'lKJ*"OZn$JQ-)X!OL1LaC1LK]1gE54>/[M-3['U
%>TF)^AIOaOI;f`1&/T2+Ru$>7-mN/t(D3i9LFaSG`E7^ZP!&i.)GWprRS?[0$oUb1[,)*k<APa4'Bu&q=tChQ8._'VTsCJm^2$G+
%bGB9;j<%f#eqgiWdQh?o4_#\HbXB2^IJ<TSXrtUa;\[9X#l,=7%H@Dq"AtY!AVRIs>f'TA&MOM0)hq,"9\*d,[EJJgH@kOF<'9e<
%QB]D!5>/93Z7pudF&[Q+F[A#ECIrl8YP]I4a[NA^lC$s)f7.D"DOT"`6>9@a9'gEf,+]LL'WH8dc3mbpigb"''if:iMe)WFb!0;X
%is>C9RLQ&h,^Tm^(Aj]-3"fMB&rYO4Q94l8@]Q6L',J>1-'mV"mP0dN1JCTtKJl9*ItFP@d4#!J2bFl(>Rm/:5p^pj!suI_c_p%c
%jPmCS4\:6?lJrS`39=^#6ci>d`GMV-I8TQr-AfkB@cUb=JV>63[;n6P<0Fq*:2eZc5mS49Z)D!KZ3oLq)I=WMb/RUb)HW,$B7Bs,
%T-/9FD",TsY%)OsS:S\K]8cT.;&_0$WM^!GdJ<WeLiDVUO\V"XpT51hhTemM1KB@-'RL(pWTBKm;miR&5WOQu5I==H[L!jME\fM\
%K;bXsURd,-3LeJ,'X*tSB7ZE/4Z,%3`CH_r"LTdu2`.Chf2bVcdp.3"=QIkEA4N!5]q?gNODfk9lA2HmH*P=`YFb2=?+c7,.jMCI
%GCU0!4:*j(MYqp+QnR't'=7bu2R,Z_H>GNZ,LVcS1Ie[Ql\"\YaZQ9("OQ;\KB\E\_M`LGN/p:6?h@M0c)E<*RWK-V,W4&0L9:>"
%1_F:UK5!5*UN"$?DCM8Y;8QT+JXdc2F>OJ<r[^>\Oi3d#+t6T6h+.&l%Al]#\2].9M5Q)$SO#L)FFY$^nbjh0h68"%&`uaFg@p[K
%OJnp9W]&N.X!QD@^?EIsR<T`@$SA#&C+`#iH3(AeJ<e;DGX*!A<7A@?$4Bu#ncp\TRi9+>B4S:YjP:S#3&9r)efI)t4nI*7hd>9I
%-M^D+$]s%!,m?H5p].9@&TA:>,N/V2<6,CgolZ*L+b68KNEkaJWtl'ns*8M_h-@T"*84R#&[('O2%d]M1,8!kQ]O2@,/obt%1goX
%*<"<+mmO[H8gj=S2X'\tWqEkKn]4>r`j,%<HD]-/5aF+M"O!h3SP97[+PP,$^a(q]%Ppo#oh<Y9fOU7[%Yr!E$k\TETbLet5.82T
%(JB)gUEpY=7Fj"'GcA[QX,nW4ErU5oI9F50lg\f#I7a?@l(9R'IVLjI<>[sod8lCM?3+L^qOSq^1H-K4*K\a7<5I3+?.70;<;K3?
%WTTDq1q^9OA6jME:/speo*gAt/Nmm"CrPaG+/jM"V]*(<q_OS<m-fo_!tu-3H572(@u%6<6CZ1mgNXQ7bpS^-](X:GO_X?U/2dgo
%i^X+*>t[o"mrJ&RKj1f113ml+6Rl2m1Vk)p[3N4KjH?_j!6@,^5)K0LBU<:.S6H#c`a.4RQsrM@`agW@IYPfKi;JssC<uj!I+WW2
%`?X@'kb(E+3(tP`bb"0kDG2cV54[@YZf9p3e7+$Q+F=m*CF^7cJ?:u(3cVlEQ=VN^YtC3+d7K,p%,%k:<H;NWq&CfCr8&^:-=a;S
%R0@ZHWZOIg*,$DEU);qGk9lcnJE%]F1I!X7Mbq\NKhW6/44-j*TGY)#bs^Vr_n01>R3!Vp3hf=/@`/gJ[\AlW=u[V:[?m(E$ncM_
%)P'5QJQQQpJ2=j*@D2/eB5Da[:e/<37!QL1YH\][UDlI]Cmr^+M#AQ>ZV4*"apZkGpK?j+bWSmu`CN;,-5C+2K&$`tAH9iC6SN,C
%i?Vl`@3laRHg>9Z=L:1dc6R^nX<Y9958jc.kgTpq/Vf%I%^VGD*#=]=0!H9c2aE=t'\-ZAqThf/MJ%TjoDLNsW#6Z[b])r:-b<u:
%OC2JJDDY2oL2ciZOMAG[Q-=m/iR*_:(o!FgJkg]8/D+m;&$9&NT,j&sX:K1<O[j1aHA2r(G/bTDpB;:[AL^f'lXC^*'KBrWQ[H+n
%5qgs!FW7dVdUn;:c:ZAd[=h,mn.<;afct/\PT7GC%`hG+ARb<^:5&aP:a<N5\C\<bM?_L7@6qd%SD99YV1KH/l`(.ZdTG&=<],I=
%5;8W)9gq0/%h:;6=a^mMcT6TNLP+8us#;GDS[hDZ)2n(&Z)Eti(A<k]S*RqaY2284LkH86B`I?u/Kl=&<q#!f':gG.aMILFEYG0U
%hJ$G,c3@"u,Rj!M)9ZqqgEGA-qsn6e#]\%JMTO^9GC*q^Zk\P=fY4+j672dMZV'4*n&lnP8M5/Wbt=Yr:ND8i"E8IW4FHh>aJc^9
%9cYbq.2E9N3lJ*<QqAqROcW$fWIdo,.l6681AL*B?TKH<5f+:o@4S9J5UCKm@QU_>FdF`Ffm3hR`10SYX!kDga66H6lV5UQ,t[BI
%K5sR<e4V46s")if>d2hBrcCHS,<\oSY^j>6r:,NCU03C;7p509E96\3YI,j18HTeXN-bTLlK]``DQc?1We*g=BmC@:alQR^NB.q1
%,LqP(#spkP!^nG!SGO'i,p2Osk.'90D&?[bEWZW])a2**IA@0V9*ML:g(5f5".dme7NY7k"8[I>5.Xo,j<YbYfQ6ul8!7J+3H(6e
%PH&VA(^n8:kH%lK`=_VGa#a]VDhH<i8nLalYej%a.g3n//<#l.iD/QQFda<*7%Mga7u0k$eoS16].I4U>Nk7.;NN"LYF6/o,@Ze*
%`*efl2@6U`!nXfV!+!u0QD.f\V]^-YdVj<7(KC*e;u0%fqmr`]:!4;RFQb6dBAb+sgBqoVJ`(:2[D!PD9W>R4Bp+p/>K&,9]FqWR
%gisGH(!Di1#%?fXO_sB^.A@a1eH.(PG0#Dkpst2<a!8!NfIEo,[46HOQ96TnBl>R^/_ifYXair?X#f=\dNO=;CotcE^+?('oou5M
%RYH"l9STqZoCS8/IYJJ<Y0ss:0#G6bMlGJF6XI^H#t@NtB1kB.&rp2[X<g*CUT^V`"]@9>=sP_+4E)?80uBO]Aknb,BqPd^Gg#g8
%Wl9**'d=,W\FU-@)J6,0.5R?,&Nr;+>n)+&&LO!rUXeJ_MfO8n-EIJ9q(Ss@9S+"(3MYQa2s+e:?qIsk!+2+l,`o)'b&I$4&`58M
%iYE<NW(oSH,unFX\`Y662YTbK(!i<S.qj/16"\p[b]Brkcu:.K6`]8AUd.98/0*QD?OU4N?>>(JQ=lQG*r[Fp%Yd[@V*E2t.Ta>m
%bS[+,X;2q1F4TbJMD+@<(U_9pqeZFG-+H&GG-NDPOcm7#+9Qt-=S3HlT/(&CReFA4O68O0[Im@>^GnrEjtoL\gsd*6%lU88BpC-_
%0Vq]Vg+_;(RneCT7b0C3Ql5Zs:Fn+lZ;Br0U4Vs'bHGSs5BrRC]tb*_Z#!heX"T(OQ5&uqLlRUh_Gp`$Q((^XTO(>]+>Kdgi7@iN
%n?PFt3ngK"X<Af:dPZfo@57UG_Uj2(#2.h<n^(F]C$^IDI2>#Pd;m_.7cGDGPosm60cT@uj0S';Pb%A!,Ec$BA^SZ''f6SG<;Kpi
%p`fT4pm@Q`C9*B^`4l2MU*YbQ\WH'Y:dIUl?^EV,'fc*?nNj%0HHW.&GEnsd9T'n?1nf(c3P[b$kH8/*?I;b2JCbl``KN.#CRCeh
%KUCtQl$EYm^Kri),GuNZ?'TZKWWq#735$Tqam?Z:>ep`N8))D?Ws*$SWJd77e)]W[(k`U<K&68NIHGu<I+ih`jR+TL\LEp7<Kt(K
%9]rXSE[bPqAS;+$S0>fa]elJGPu04PLm7H;G":2@5uH9ueh^oL%MFlW"Ft8XUL*3an.UY4^*nXN/YI'j!GjUdMnYD#HI>pISQMQj
%FkY^F6IGF60OD#"QXZP;\=F4Jk//[V-)IKOX=9oF<\=oi9Do=.I?7AJ]f:-O-l]O/mFe5)(`E6FY]e=mN*3E:F>f/jS-@*3Tu0:&
%-=H;o!Z[+#T%"3Gk#\r'N*'%+,DgBk$0l:"\#%JAEUXW\MRQW.MXsAngQSLPo]fMYbH*D8&_7U/#JtIQ%Je'u:Tm:WPpM:2s,0Ta
%f@8tbQ9%?#\PeP?C[nhj,]ao6`1he?`TLjlE-Hiq5qn$E#6BQQs&E"@"16li6;5n,7#X):9nWk!WKpUPY,f^U=dVfQ+3s$DcKLWo
%)qh91Jh<ooG>"&q,YC$6okk*Y?;8Zf))@TR-$f@"dbAs\bb\N7gbTNWH72Q-RIn]f*f2UTeBH54^t)%32"CKNEp(.Od[ItI3m`o9
%leZ]*Xe;%jI8qA)C9c\D&UL;V3d=m&)m9dMq`UMn>,g1gJ`.t3Je'?qTJ;=`A>i7^bi$pfq2jReZ3h))Oh1,A7KsQki/uC0F,C_B
%^3(P6Nh"Ejaf:kC'?88B-!:Y^W&IVZ"is=GiPoOmZ*9'=fPqmu&4)9@'bi9YK&ENX&fb-jjZ;dGMoQ2Gk+`IP$OD^Ul+C+uRME&G
%gcX"I=ido7[i:AiVUbaI8776r,Zs;8,GdFYIfNP79=`GOZB>UZGu+]k+;8:34K&R`$7Mf/?lX4fYlb8t8aA#Hm48<r3M71]*qs`*
%"]E=DB:BFFB7ld1,OP_`PI'\hlN>W*XO<o)Z?0u>,O"+Ekg\P?,Tq=^0m>&>@I@7_%J?jTRB"JtV![i??Md9hJEbh"cm!r=!S^`+
%*,o(J[Ud?t&<p@7Z-Wq[I#V=6?iWZbb8MpS6n\]9,/o-cFLGG==r'C*jt]`LZ+te'\'DnEi*Ve[B>iG*AsHG$%%bia4fUP/X]lC'
%[['`@T=cG-Kaj5c+HFi@OignrE(W5'qh"9Z&;(%;dZc<7K5?_Zo((bu(+mU]Z2"I<qJpJ#WWk>YOc0I;0.^/Bf@ikGfLiCF/npQS
%gQc-Ib4lIAj-Ed@B6s$55(@\MQ]%o*/oL].EIRZGb9rq<>lF<km'EaN0.sugEp(L'7q[$i(Ur"Dk3(&D+((DU:f[h!l)B^srcn8*
%mM;)cjNFI`$K[>@r97R[hV?u4c\UOc+t'[_4461U]o9@<ed->RGiD7A_UIbeZaF0>,/;ir\NII74MQ,_IWi,Bg<X[j+9-+pd_;d]
%C&OIsrD0U=ZNgn`Yh&j.TQes2Sm;3g8+,l<%,gURN-"cDXoJ16qt#C*%N]asTD;AKrL`bds&uZl+4B,c_ghT^4$<M)5Q's0+)0(K
%*HoAqgg%6,P0c0s;Me*qCT:TORu(6BQ/q8@KSM3/RF=s7Qc_XGSI/4jat-Rn08OsK?0mT_/mQ*dVWc8`hZ]oZheqBc\1"8$pIUNG
%l/Er)gnLsta&R@t[1`m-#4VF\NB(c%,'u$^PZnE]R<d!J92?Ldm!FZII\CQEG?STrLq'nWXZA1N]U\A71"XQR%F2(r+qjaPj;-F&
%`H$D3F*a!rT8*aop'2iZ&Z5rf)=Be'XR7lbZ&Hf3#4f!ns-_Nd)(k2+":>hbM=K9>Fg/Sq:#2AupAgOo>$>=M,<)*e[lcrfN0H,_
%%M`IQ$fl)e!4p.Ve%jomk1#fb"n5n3:`Zn^3<.(C-@?H^l":<k]++&u9WODfGW2&n;HJ/tU<KE!,7"aQl.6^\fK]aTC_a:&o]?p`
%QH/$'kWL1)7`j1##q4Io&3A(f0Tcc=A6E0]i*I%`nPghG8Sc;)Ub(=T+XXg=bE8nB#e?`#g]JB'-`\q9="P_!'mHMB?]!Dtk/U$6
%-Zb?s2ia6)(2mZI+nD:s[Ih/J"(XXdW*8CQYTD,HJZ$+_6HmW0)YD/u$Whku9`@pGScnUF(%o?LWC@5LUIf9s%_%f]C/f.dI<MH]
%$/e4U/D[ET/mbAK/Gt]3YhE)KB'uBsbaNJP59ZUUDGm>K'%.%f8TP7i;G)r,@U/5./ZPZb?)92a]pFX$WR)pI!h97*Clt,/cM&AT
%E3_\QUF_i]p2dq:a_!/RcNrM;7j)G>iM'e`)>DCQ6n'sM3$id15Zc4["]Zm%1Fni;+Pq`MX',<fq@I3[99B+BEZqct@&QN8`&s9C
%-`[.^GuB:ACLKgMQ@JtW7;!`jC,kd+`=_":\8VY/0/0\B_*-:jT.G8TlN>hDe6A^s-(XBAaG@lnR9@B>)(D7*GiepCAiCigPiLO+
%\;R1sVh.$5ggil]R#+0sX]&nJE/ku0OT]om.M]rg=Ic52CUYFB-,1Ql8qmUS;s=\*+;"W3<ea@c3l$.naR;9UXc$7_<Ylk>\`O%<
%Tk`J:kF*aF:)X2K!TiBIG[j[A2t&WIU3\&,eVa2UA4=j+A6\e;?uTkO(9\*NFsQ97=7^K:,1H['b%P[54s\IuP)Xr+hHJ.'KE_[&
%0oerNZY8][(9l.I)$>Gr<kq0/=2E%RIYX*S@F:p8C\=HQR^`YoAFq.%@?b0%7&RrfoI.P+0ATT2Q$J[$UX%^R(bH[:8bL"2)U5r\
%V-LVSD,<D1%"gIRP39ne")INl/cm-EY8(4@Xb$\$2a.#)j'/*TMCdAh(YuuTMr"3F(\;:&K0YYSA:-!$L_:f^Ct@HN5-/50V+jt&
%MTf*OAED>?/OC9(*H(-=+T7:!MZPeW,HtsSnM<dp+@h_5R0S%aMghXGZdUHNpTTH_aD/aJiq:2M1a0I"g:Q\1?1d*;:"Gt8&M#AD
%Vd]fbJ3L=Qi7G0#aGUgqWQda\55i>:F.lMJ.V;Y5W5+X6CMP'0h8As2o18L\S-M\s#Qspr-Y,T2dc0'o?6:aJ\n2eCaL&#tfRr![
%Aa)9p]Ii+H5Wpg\r\_*1@beMm"?;`:(1_St']8.ri4+AP"Hh=Pb)P8W.hd600VDGd,Htcjb3"/ca:[XXEp1K[MupR-O#%+&e;]Ja
%;BCG"Q:B;:Y>\$*^XlOB_/uLKRBgV(;,rkBUs!)@>\!of'#\7;@18SkR374bP`6&\]R1sU&u#bQ#&"$q;B\VZ!k#_c$1=5d/JtH^
%#b$8P52G3!cP"/.*-[jj<;uX]&tX%TH9j\;e%RA%H!ac2#8"Fie5#L*96AZXK/BTf&X+r5[i$MQ&7"qd-dlRa'"',nj^J]>ldn&/
%P-H\1At_.@D*t_<4&YSp)J<\0LLMt:)#W&0/V,o=odoCj&W(L`S`*T7Kt^J0:Tr`'16T/$oRY)S7PQc5g#VC"+r6-'FE#&EX\Rlt
%.$;L&`FK7ANMKJCRWsFY=j&_>]]8,tdGHgA'bU+Bic3r;H<SDJYqR-l4fg5G='Cr9At:$tP(r7<`?RNI\GfM!PT4n-GAk$4co+iR
%.EN[cD%PuULcS;Ae`?XAI!Do+<u/483-HT.SEkQ#@[2V`=QK6G;nad,4c]_gf79D?.Z"#q/3nMD:^X7oAR-jlai$Qe3s22t..I\Y
%LY2KG2VM/-pA\guXdO5A`D3eURKBg]Gt#hYXd&TKa%Os6lkL!o2FY-l]FJjrONYdY6&:\iZ1;?fYG<<ULH$bAKW;pEpen6s9K"=q
%Ai&S]A`=ImYWWAe;n,@hF:dO+PM%0LESZ870q2$EEC&)?PC93hs"@9m<_YtRb)HIbL/ecurfYN2;QK2$,q("%D)F*0]KL'T4g=BR
%,:h/KNiF3L2KbrHU<IfK\Dl-CcXjYsB!Yk61`ug`geesG"%PoELl7+Z$1$MK72!\*X`Z%_^o?356q1U&%o=]oCQl@n<^1jDHl;P/
%HGQ"kL)/6f8LubmZ7;f707h9Kl]sp_d[YEA=bJeORP@[b$;\,Mkm#:b6FDtL)i\Oao_(ts#dAVJrG@P`G#HKa''u&:b<==fb:EOc
%Y2DW@<b0rfWjsg?a$Y(2&sZ"^qbXu0%MWqhV7C^=?ui9l6::uHkFi1i.as;G+m6QVJ]JWW#Ap5kh@_\W#4,8P:21W#=i%aTkR]/R
%)-H^1PKB(;"ZRDG76k"]adln%UE>@aPBqja1mjo/'C+noABi@D0&8K6cV9J\),lU7PJ$>,(/3oW[d_Y@bXOT"d/^/(Y+c9+N2dFE
%Sc]@#"/O,/VA#:+NTp/jluQ*\R:Cln-#ZC#;^I<b(Lsa=]]>&LoQhhU&k(nLY89]Ao6WG7>b]D7U@j%G2Vcm*(h17`Oh>'Y/\@[B
%'q=1!kui81.6YYF\i8gVaJk70<NGDuE\p49ccgUMX";@)A9?d_`%$S#=^@jZitD0,5a7Qo]jY7);l@i.-_i,lGg1,C0gDnl"0-GN
%&U0`"S[o\V#>[]o-3nF7rch3WApQ(nVcG,m7j1A3N.JQC./3B])jkbAg#CLU3[ceU0X4$(6uLP&S4]4iG1@Q]a-tl>;siUF7f@\b
%j08"e*sr0,0h'\OTV?GSU!\g<m&L1N--0UJW?X!"Uj[gL8iQ'H8kga8ANoVMdJ>;"-b5?+)[q<H5m%]1UT;ZC94>`"=.s:\/@HB]
%:shF1WD\l-M%#T2cCK5?T\F8C+"O-3:utZaF,sb(=Enek#\o]d4L5r^2t/Hg8XiBGR"Xnt/G$mN`e@,c7&'XPQlbfB4:^J['1Wek
%9c_R,HV2!fe\WDN*5rV^R=S>eMu>C?LEZ9H!5BA_6'V\r"&1Y.0_oC#Y:HqdA48mu2qIk21pVkdl5J.T8HWdLG3CiZcNg%U9<AnR
%mbn^9E9ZoRP5R)p*h1Zml_/$K(E,&,TG&>3eR+5X[)2!l!Wsp,,cs3h3`bPKM5t[IFMu<W@0%V\EdC@U;ti=@h0>8%!_JB['1YAI
%=dGQM6Y/SuLfHf(#&gUGG9L`g1Tq9ca_?!Almb*EVthBm+kh,)Q\>H=)$`Jc'b]$BWr%a`b/%8JD)ie;85n2<ZS=c?8]`LoXbm8W
%>2"0KR/9Du2_IZa4VnfaLDOOfoQoL_^37YtMb@M4@D-a?D%_rkK?aYWd@4^$j#H8U+<a?0oY=>6e5qo][El))O?If]P5$(-i=sIg
%3)=/*\a=/^R7Y11#u7:o+qn*H)@mT_X+FU`$k;N;8mYnY]Kil'ajQb)">_C2#p=s(i]L\r[=Dur"=\m\%>7h[Y*+=_TYUnl.e=7`
%*7?7NZ,&a6jC7#$*FqESUds0Bd2[[U$Id*>],rSjjPT._R#RP:;L^bXS0^4KLK;ST1?d$54aG!ET(KVpU$dW;9Z8T]J`_PYa+Nul
%BoA9a2^[a<=G>CE?LT\dbtR=YZ\>g.VF_:NEF*%5,*^6`Z)NbN+>U$nXuPmXJC:j[MlABA&t#tVQ-f_Ds+jp\,)m=YNj#kC-#-;r
%r&sNmH.)Z4eXHAV/a<iKTf1%2`tF'43mdL8/In[N"@f=*e-1%6>'<k@fN=Va1BYIl3c7LjH4m@?@0it_bXgPD/cNQpe4QHL?AuQM
%W3b]6C\6H/+2T)CSKNF5/4.m/qI44.l=^`?@/>Z_4d5gI`StLE88JsW&!!^;CF;Uk;b;gASF!5VS_U?8dM''0]f/B\SN*,D]iYZA
%!X]aG<UPtgiZ).DG7kcra:aF4_$^L9Bk2C0kg^>(-#\l:A`G.]6W\t_8p$KM_Pn&:Q<IQ5P19T3;e0@-PaPbXMHd<cYch.Q@LJp,
%k<p>:oh%&19Z\>,>E^CI#YH+m4\t=eAbVqZXcOGPSXIi<7Do)Z_[2!W+k\d)djsVk(8,@VO/jsNS'eSV8X@(n2T+U_WUXs`V%MYo
%nGo<H3ofY[C;90NYJA\/-6`Q".ogu-P#oDr6H]d&'ftn"ktjrsV1J^&='8[Km>tZ:Sc#DiCteo8R?*O,#e@kKc',%ac`\E4Spc(]
%o]#B$k4-FAOlr!7S\@&?-0.BukDU5"2j"9VBJS/P7qb':7JD3l/q&-=jQua4amJ2R_hVm6?T^DtqTa*ln+cOqH[dAlGjhVZI/dc,
%9^@e-,El%r->7Th+h19_od6,h%ltoqSKIK2Cd1Wi?^jQN37E]TV@\'T.lOJY'HnKZ5?@S2[AoL>k<J)Pqp-?+V#J.+X/[oc&i+"3
%<IpdFm#uAilDO<sm3b"fcRHDS71#_SZ;=,77X*d5iQ7WJ80#O*l>kdCg;$phf>cZelM^%4gJ=<m'O`\Q#DlL,U/@,MT$FTi65Qr?
%\2IdF#%qMNNP4B%qq!4)T=2-6?F2WMn"$:fZ:dN_.aBkGjK'0K-2+p#\o-$:hJhKAo&TMnVCl!sa$njUUWAOkEbob#Mh\'a_pJj-
%P=BCmQtk+-EU*KSloo9$Zej+U&VuVa;/B)"7hujn0o79g8A25cH.8_:F1B!FeGH<$IG37a]s"i"SoBkC>Q.11,R)4Bfre[aRke`B
%k;h>TEU*M5m65B%ZV"noQ:FnVE;,)f9>V*+Q'R(Km"MjHUGchX_u1URhOhsYe##%hY3k`Hor]T%,k2!/6I+[([cUo%^>#S\S7chk
%a/SgR[/750NF+=I%dumH8.!L20j5Smem]N)#b0Kd2m[`*h@Ok&k$%7:&?8uj/S7/h,hKTX62Y:,B(@Djb%C:X1FuSf\a11s$a]5'
%<i6*FXec<s8"Y*r7X+Y&gReKG0/eL$T7?Lh<9kEMI_Vr$ag5HnPQi(sA?\LY$*.0f>8OeIi.@=*G#.VhdgdcKS9b0I]?(.FKJi8I
%$O?r:Ak3Y\'!W8_YlA$NZ0`SZ!s&b:&D9`L!3=Wu^f;nP2ctSuE6$i[_lmHu.GO<QM$,8dHc+=,`IicOs"lHG+3:Du=UqYakhB'%
%#FE!a-Cid@[O,\te?Fi^Gh,=16BBjL#m5QPWBZ?#!!(Om!1aWR!<^C#jVj.#AeH?$/ijp`>%Q&cT;E67h?'i:#IP4QRdS>6VVM\c
%lR2cmIIqgMj2[fDJEi@!:-G)58LP=#$'ZqCDTSJUqXCF!AH;sBS1sni-<]@-=_)U4]6HCQmjD_?Q<H`?7fbFq/s\\Z+QJ4'\*?XO
%JXO"I?GaKNm]XeK*a%Da[og$p&$"]A)NG]ChlS1X=:+[Zgme\^kcGMu]aA.#5oKI[LD-G4#l;7r(N3F>/iL-#RRV$IB2bKI"GcZ-
%I4Ac&nk?[^QjUp\K#Y@8mbn:C:sJ6l\Ac8=VQ!D-/chJ1KUD]\0g\+9Z-ZJIO_=EMC$XG9lqH(6rrH?=f"6Q.)8qon4mM5:O<0c6
%IFf5J)oa;!j.Ub!5oKif_pW=^Ca`k7NAgQ@SaM[0)ai$dr7GZj3HT9gBD(K/`C@_O9/8WKMR=/01;G>SZ!cl;Miub]F/FXpk84nU
%?ma6rNCYi4D^>M+PkLW/Rtn<ZV5r</=ed)5h2j512't\s25,<@Zaq@K5o;%s$&08f/#nnX%?%nO^/lKNm:)L;5_CKq-bB/J@Eu=F
%Z:$4OmnLTp<>j9RfgJan_/[]7#?MF62_?NM,k0`pI>=u==S68f()"p)(<(hU-]'+^hIrtG.3tK=(Pq^XrKfAs95(AV1PnG(gqfo@
%B&RD"BmoPp8nd+b>-CLa;mn`a8XEk1Z5Z!T?O@Ul<<EMAIL>!7)]/nb<%H6L=E4QRbIlRFGWH2'qPK!t@"LC@$(,9+S9CpB9R*`[
%I3j&jd'JD/MmbM'FP"+3WG\/-*"D.ZTXm0VMsGD:j^m.5#9:<G&E*ird+P:pg3CPU[^frlC'%_R*6'8dJrsE/-UX<<YluYS[c4&t
%G\DmO1W3ueNL>!G_TA\8g5`f_ke#cQPZn2aVn0gB5_af0d.O!gTe'S9Ag_CN9*PkJ8HjOk.VOb@^hB+F2iRlHD?M#Mn&&akL8@ZW
%FbNh7bgU,ia8A`<*>LlLs4t:XNl?nO2N*R[3NJ`"4LND(eh+-IJ_R>qn7(\g5t-=X13VefUFQ\YYqn2$p3%Qq?#PIfXr/6"?,I][
%B\o[1Q'A0;Wh1X36C^LIm_mJ?;.KZ8R/iUH5JXU"1L*,`0i`jdS_DDoV3)H(TX45+5+i#E-,B+rs)@HtKa)C_e0%LD#\c<!/KJ]]
%Y%$dmM77]G&E:$5LYG"FFnhnr1H17kG`62mJ=5OZ*U5Ja(q,I\7deB.%dE5LVtFmE6+11R>R!7GB5oiIjZMEVW=+a;L'gW/Kp4H\
%C:!!4nSYgNNTbT$DY(4PVF&:#*,j:cceX$oM&_ghHVYFYak_p\pUaJH[[f54@\^9*(3Z0X<[6CK8!O@5@cM@,[L7uI6+><f]5"Ya
%L`Xe\-LTW\)<+3Ol7&6&$C_-sB5!Z.Y,i<=qdV8-T"I&r`rJhHk6@I%faVh3E`RtEc/G3NA(:LRcUO-`MZm7OmUu>=.i$04#T;rL
%.ti_rBD"cKW-W$@GqrMQNW^U6G-YaTR**o@CrNPnnI%k#Z]jW;Qj)cf"O[]L?=0rQ_]gGt3"B!%=d"e^(0S7OMGKqi6L+9nqW-t,
%Uq^3C?CS*]l=b/WO=5r<8?979\:P-P'@<YN!5it&i9%]'G]rVd)M`ZJ.M)X($#Cj:%NIY9qbg":[&X82g69+mlAJ1Q"W>W5?t`g[
%nib;kR$TpK$O8k]9@uA1+DGo[f4D2<*`YpecF3,]VN7-L4G/*h'hil:"nY]>T4dr"*0$$-okt'Kq.RogfX%m!GAnot(jpH/,N[_5
%QRR?>#GuI7%\.u8a1*Jc:gCIe$KqQbh2fUc5deh*/HpX?QS&C(^'a_:>`QYFYs.-Df'+QiV![jRoWpqM!NFsG!Po+nMnAB#<aQ<*
%k+RLhgVEF@:'9?7L]$*YE9:if(*4@2"gB'eJ^u,HH(J&m:t543K)g9,g(XGCR_VY,:&*p^db!qW""KuX6O.P9m3o$&\"*taMAb>o
%'U=k\c'sn2k/NM_>G"mDj'A'lN43&9Fm0gW;<Oah[eo25H@^#3+u'JS[b!Fqp+ePqYr0slOX&IX=.2l5cCqtRDVNKo\RBp[a;)n=
%j/L]H-+bTSWoB=>iNs#1b9et/Xqn#J_9AE&ORT)UoA]0R4AX01\EUC-+'mYpS]5-g?or>&\p]N/1U<JX8suuSQZLbpk9\6;Z"8g@
%nH;&EYkp5'(6k+g`,\DgH!9Rr)XD*/?g%5/14g<BA;-4>PIpB:4bYYL.:!W2-%23cPO58E?E<_jmAfj5oT06:NJio253[hYA9rgO
%:n0Cu42B?aWWgKt!D\C/9^7,eh8lIbXb,)@34!R5\$6W[V81qPm!<gOF\^1*<irW`2TQ,:6g1g;%4`'']Q>I;"A\[M),GMli"_0`
%%h_G9,^=&Z)L(M<i+=gkb`5,$VK(Lc^h)rQ#reW![TmVM%M8ps6HiiG9]rV9J:\_0j(qs2!fgKERNE!/#'*k,*\K>Zh.E0OD&h?N
%B"[-a_*D[:[$.gIMq5AlN3hWV#]=-.cGi[,I@g:+l8;^0ZPdE;UrJ4efriaOD@#+q/U-Hsdga.5dF:H6_&.?=&'%XdJsb4n9L?0]
%B!ZLA62#15=2eaeCU)]3gd,?lS'8l.hTa&oa]0+Q:9tim0ukW2+4Z(0-`":b2(h%G`\It=L$G?4%W0:mIl<KhK&A><P;s7`fEel^
%CY"Kbq7c\3Tj"_I"\R;H:.F7r:eKJIhZEGI"^^+qUdL@g^c[ON][.LRO^_inO^<-XSM;%mK@?8=mHA!/.S-hf:c/sQgW=&tD2+]q
%eRi<c!_gCS!h4#G1YuugR?>i!F#Ur#r=@r82Oi`9&%aHSP$N9?DWSUo[]J:EgPdqF29;%;/f2M_Zj`FDB&jT@:c=4eC+CX#nU_TO
%0]/99\0rjn#KNR7hoPM"Z@\cPdhikl\CgJ,n3pFE@5Af<,r8!&^);oCE&JVY*;a5,=K+`uRr1&8'P_cs=JeQ`@QR5tYZSh&5ViPh
%HdgP\][4'"IQ/hm0>MSQ[+\=;a_I?jDZcn^mHZP#@Um/\mf\AR2&?!U7ET#EIV[\;rE+##gEoi<!["jF,@GMtZjb(ZJfDhcdFhr]
%:BM;ZVRB(n2/:RG%Vc<4*.%e&l?F`=W/uJ`GG.6aA4im\VFP/j]!fL\!WDf[6N<!=9#VZ+ok[UY0V1_I^V<6"iS*'@Dl6?3&4^6I
%EC5p0m94F900hB1L6%qRPd\b)XfX$3:>kDI@^q"Mp9PuJ9c+DP5CAEL%Qfg;m3CkK#aTLi:"*Xm]5^cb`HJ057p,!p"e;Y+`gmXq
%9G^Q;ASc9X07`VQJRbR;X`-1:Jn/P!7d<B6bA?&-]c4HC:!cuS+8/DR"^>U9joDmabmEj=c/>sCP7#eul[u=ReFs&RZ>]SRhsD4j
%-!`:1p[1BBRGH@1fj`dW<gP3P:`f266NT&&@/i+eVjPJfN^u)HVF_u#%@kO"2$N_kGV>k/@/"`lP]$b$IUUf#e&0klg6dc*<+F+k
%`0l?f2q;!cB@4"o/^)[J2OcZ>_miH`)_'<t]:!cj-h&::NA4CHh\%UkfS,=,`0iIIS'u%q)##K:9q3/A1H,P&9k<q6g.lp*jZQol
%V1nljmJ?MCZ;YD8h9sQ\JK:lKO]6Xp9q9[Q*&c(RZINAnj2AWE/$YHW)D06WO)8a("7?f,qL[">AL8#2$)q7AK'/CL[#TUMEjI+T
%gojt*cT@RVfne[3LI[pGln-N0ps$pFaXaSY_IPLGM=W?8Uum%_p(<`(d1*UniXL:6ML@^/-Q=Fi0D@tc-M>>Y4ttOS/4X62m#a!M
%([+U28XapB;i(4lR#5;FlU#:_Z(RmqZaWgILnj_8<t/F_Z>m;+$$VU$%d(tZ\I'=dO[<'O6,k@U$_fWf&X(AWX3a#qm!P7e@pK+i
%:G0lVYjEg1?E;B>gK!Psl0M$g5$Bo9FVj%'M7q,n##*m19U,KR-N#.m-ZaK#Ai"t,%0ddp`.oAH3_Y#(a-6eC_Z6SR/!F5HS_63;
%(6/`Y<9*"]_l'@M?2&NMBm+_XUb>I)$>,M!,+cTpMP'CC4fI*U366KNJj-["El`P-I-aWOPp\P71A.4nE7-.f#p,X)omtJ:m<jp\
%3JBt8Wi[4AUhIuC@r%.^-ID0#=S2@ZRF-uiF#mP1[QTC,hW8NbbuHO/aX+N3"1AH.n[d<Ie;OZY#<:#\=?3;FmZ:K$3Q!6-)/P,`
%65k7e\$&^h4k+=Y9Vk3OnDR24=u<[t]3YqckDD+cDX\u1!O-#%<rX2!cYW+UbOg6;D09bd]cq.(K0s@je+%,6W)h0NSMqN+E9-hs
%$fTNMWc\W[gioRZ'[S,V@NOn<:(h^n`E/U.i<$),)!FX4_q[j)lm6nPLYP,LChZS^#&RZ%8D"j'[BD7uPM3BQ9d>^%mt[U!V&RCV
%h^!WY442B?)>"&,0=,p8M^I<CE2TCM0%K+(X:!GB%477\fO\7M?,k`O@A=k2Gb<05r%m)106N*<G_m-pl^6:&;5Vgeat$(m^L+^/
%[$jM.[nJ;].V)_Fj)n`<52'1t-:?cVe?smdDUNY5fe^/*T*[//6u"QN\'1SBVYfa\W;E"m^+J6F52;12*R<F$>OG]kDC86A?"8)+
%'P/dXJQ$VG+&,'TAa-5l+XWI1a^IC'KZ_S84?FP'&5B(d#+U_/%;t?M0o)+k,hKC(8&p'U,-Qub:s<WmQ6o4"i4]pt`Z!Zj,fQLm
%[Y:[)D3f\ZRIlQYV.'pdZYWXW-b^>4$LJ'G\n<DLI;hNplGG_#\rKcD<qCE*A.@Bj]Z@I`8$b$EVR=")da:0@VRaIX2HmP?GD"jZ
%Wpp=4WW5Q\Td8ss7>`:)R;Jq0MTi^.=J,"AFaeSH6+M5>85i"Uc0NAYh:A^-YW"EbR?R-qj;Hal&Ic=-@uO<`\;bsWDIYefjM#Nk
%#i&=<`n_Kc\(c^QQ?N/H"@HhXiPh)bVYdm?CX2VuSD#C/$$pM'4dBn=[TknRX(tV?7!j/H!pX?$QHBb,-SWl,I1tI`G_#'t[MK+t
%/KERrQrmjB"b?u.]/+-;a.r$5lF-(#?5_.OEEXmUZ6bSo=s;pDT2eEs]=U3=j(rq17);?>U,uOQi7)T_Hq[7o0V:k1a4Tg7/t%HB
%*p9^*RqG-X,Yim=auu=qM0t[5I[9=)6SZ6ECQgfmT86^a0(l.iC&m/B3D_)DeA,JTf$kS2pj1G,'=d;e#p3B*M3j4Ga&[nCDT.U4
%5D4>>.7VLXl/V#:HSdV1N4,4.On=Y]E,benb)T(W]r=H0QD)H=3YJIYbu<q;[uefiEs#1N[_]ube4]1=1"TeS\%0(uOJDA4Dl,ng
%!nha+cCM;4;is[4ff$euirm*PA[_p=m/&+\H;5/@:3&1K(/9ap#p+'2e7g4/8+Ig1f=s7Z1q-m<EAJP*>A!ksm3d2MFfNj'?+""F
%l^Qre3oTo>GjkJ5E:<W-n:^*Hn=AkaYeB@,#C!=hqhJI\m`tL-=)7!q`]!0ghiOaMn+m7XK^5,cHe13mp9&=cKk9rrU[&lg^$D/<
%8$6b1:Un+>hn-T'gpt-"gICt#XaMY=Y&==7Y&>?9\\@GP/iF,.Y&A!ULh&U!2rK&72h68,2h38\hpCP5f/J5,f/JZ@$.h,oHe14=
%m+T"aF`?O?[X4CEDg^BdoaMWtiVLRnft"hjGPA7YaJ<sqoo4gR[]XP#m9SV20&VEKS)?f>DZKO7D]RWE^29H!f/J5,f/J6#Y&Ajb
%Y&CDmcLhY)$.h,CD=@<*E:<YCU^9PoDh.SbDs50RlWXU<Y&Cq-l2$-DRC7%tXuR%`Y&CDpcLhWS4Fs&BI7*2a?/GYM?/IUOa0'>#
%adq`:EdZCL9[p@kj!iS=,V%Q\^&d$$^+lJ;Z0N@u^$D/`q)WPElep(]D>17]`*Dl<leB,qbk6t%@H[J6i0!=E(?GkS2su`2f/G8R
%^$?UrMdt?tS5Xdd2h68,[gqh2h_l"7lPmXQlPgAbA(NZ2i]GaN*hYUH[k9A)Ed`RZa0%VWb&glSD[Z#)/$n,]9RhYn\C\k'ghC>M
%S^>MeC=p>IV&j>^!1qQ[!8hR'35KfBR1AYl1*,7dATic@HCaHhW0KBGS'o=&%5C;pVTCW=8h2^lj]q(W/$g<?e&lJr1\J;DO0p_`
%ghLC]I!#R%qJEVqPg>At1+2SFV1ZH^#2if<"7UtE#5DLT"4:M[PlZ+r.g(1&Q/NL_e&hYQTp;=a]@4oA!UmBN*BZRO[Sp@t'=]*Z
%j]F%N>3rUOXR`VsL@*`0A_7YXXj"N62Y^T/ZPa5$b3Xo/Zne+j2Qb287\hYGPS75grM3>n%$$#T>++EkWKfJpf;0P=`+62pXHbe`
%@*%<*1T5!?Fk[%jd)p;mG$5anh+fKLYD53Ab\g=;Q5s[NNi+531Aa(FMMG]Sg,7k\/$l4/ksSY8,HVBD\'rK9B'':[Tp=;QnPne9
%<b=R6F*3PN17RM>-W/og<p"X%;>\f1$RbEW>+#k=Zn`*tro!Su<sL*P'-^]gZ3i\a7hC`Y]_Nd?R6?aW7[+iZ%5Lm'pf(noZ/)>>
%[1]9`bA+^K-R[)%OuD#3RDMW\Sq_P\[bRlblF(V*bp))ASLM!o\(%fA`R:q5>tVT5>65HA7fdSgh*"HX*gUQl8A0OU[>*1h-sSYR
%N/MK&.Wk(%#+W%V1S4.c+=S(Q+-=3)YZX':DomU#G"+`8\RF2k63ijf(FS"F1HiX?JGkL&dPtVRcO&._Zo#-G4gU&s_ap5u?*eQ@
%`])tRB:1$4q/)WN)X*B>ekXqe132Gtjbt\E#aXmX@[UNN7QFC(jo\n\qESeUd$BE,VVKer,QfqRB8Vj,!,o8l-1oULqNbk60+?>C
%e6]Y7Qo&c0SXs(d:MZc/*kIc>m%NbdeR;Eg!^Z"<DJ"E=URR\AY+)C_`$(f0L8uX7Oj$"QDPf2F%e-Wj;(i]:VJiFPdaQ%kRR&Od
%r,<fT0X.6ldc3[08N.lS"%@"[PqtjSCK[iSF.kWiVRTLK'Y>>Wj':IB1bg7r)3)fn3)5iDWQcoqCjCWEG"*I1ZF77n>\'802M'>G
%>4U(YFAbB/VO\;0L"_;kEg"g!&r1KsHk`S=35uWaUn!kCY+1V@D&c5.;`)ZWa'mOj,gBm8LMj!Ef-gO(2,h(&E)Omob8`(4Q;')4
%/=iD`6VdtgA72*Iemm)3FM1@F^+dZSeQ"2e)S:WM1'd)V[h#B9FP:lN9Jr*tXq)eWX3IlOQ`83VaCM,jQ^trq0/N+IoeMg`$Hq5s
%16o_ZSg/,fK]X"cC$9-.M8KC`Wb(%QB"a-KMJR/(YkVffh2'66@E@fVj4$d28l_W<%D>H_J.3QjBl*Vb;;kV1PC'n.\9"(8UCDCm
%NQ\]XYt6`g0Neq>.p_t-3CRoIm-1HFC`BrcdZh(4rB@IH1kXi6"`DnOO#neemVO,$2iY8>>:#+jla@t68mO7lFK=HcS<uhoE"`C/
%iIc-XjDblVk&.2sgR`Dm1cp(BPpBNQm"h)Y<uI%I_9kD(T=9c[($%M_<SJXn^;Fl.e`F4K!GAZSHd_!iQf\Ba>gHX%(J[RHeHWCK
%Eco7-)M3uFPcM>42AfLQ_f"4-Xqon&(njYN-Z98h9tK#a2nE8VLSo)24..J"Boa+Pc0MY>s06IJf//*`JIequ`\=^;h.q=e>Hd8t
%mp=IhYAS@RRV"r!#AEh"pAIgT]VaMlq$p+s-tra<dYMZ'UUd_`dbd)[PD9kCd)Kc<B-R*>_7LU$Rm)+b%>I9Xj^$DYU2<8[MN$p0
%G'd\=b7%/>([ImmiG-7p's#HKgR`\5#^0.#eRI>me>oUZC4#6retpi4^QF1choue!&l-;2&fX2OZMoT(eTRuVod\FLEuGLgI\64f
%J3*fkPB5[29NGrLG0VmqnT]U!k,mFAVD8q.=EeMDfI^E5_N38cZ(/=F).d-7_e#9N/,/p5Z/!:(e7(W5CiQr<%WL_h4lKZn<lNq.
%?P[6L4ka-e:Mm>lI(2lI)#%fI63fETUmL"QHJZo<@_(9<k=:rcm2]O#kan<A^%_5>m5<;Xik$G=BZe]tGT;@#AuFl"gj0,7HoOjt
%m.P(;Ti?D]n4V<E#mA.[g+bDO/5M)TEG/-+CKK-OSplsk!LlE/q:;#<1PLfk^[FM56FL&+iC^:r)_$aACA,>^,iTjA9ZQBE10Ne7
%\MDD#.4XN=B'.4;VGFLWFcL`Z2fi0*4#Z\bEcf/Vf"$?-f""ZCA=XZ>Qn;]eICm$@C7rRY%u<i0^Y_Pm.;u\XFDK`6P40qf`n<E9
%M]GStl^VlFmi%@V_0F_q);NA*'AhiM;<fEOq(CcGKJ]=4n2;OQ;N0s15M&]:/N3\T;tg+0N,S*)C7iPsC870i[O-1-T&OHBJhTB=
%Rni>:J0EUBD.<^RUHQk&OJ-H[X<81dZ%>Yol]_Js:Yf:l'%e"]D\gRnYe,W4H1,op8BQ^5I-V[K[^C90?!k+ZK.6.61C,G/2]GQZ
%GPgdQ,DgH-rQ`mZ%Hr[e#FWAe*G!%-;K(6k8<Y3mott?N#AHiG%G<^2o4")"KG`n$4KZW_9uiGiaBpnaBDYrh:UQ3u#5McARX0C^
%#kP:fWgm'R[r5VKrNI;j_$icE?CS`)(JjhY+>jBe7]uDna2NB\d"uETNp">og,+.IZal]sU+143V^;KXO7Aq2(hNHZHITe,DE=0/
%F.tnaNDodG0].h7>&M-TcSi:Un5S[0&u,Zc@I'+lN:8(sNOaREnjemam[3Bp)KPRgS=B'9*kOSE2o`B>en'irO[0+hTeIkuM/7mX
%!/X8,.U(a@9j]0S^9fcBeX>-=fX^LNPku\&/Y5-ql>'s;s,O,Rg)R)7e$"3F=(S^)(d?c:+`J@&+;0)&*#CPO8:k^HD5?A,I(He4
%&8_FfpU1C`.MY!.CEQg3@]+Plj9U?AP3<Ib&Mq6r?EDd.<8FH7JOAFnK%'3<<8H`46<_T'De&Q'X#<Jq1HPd0&TZd/<Hj,YGAQ"o
%"!/ICb+-&5"@L5ie"ZS,@p'W6RlO4aKG,@GID\_;dF9Vrife?63B^Lo7lWX?%+e+(VuWK-G?NDN!Sh_%;<EMAIL>>Ks)crs=
%\%M%c5Xr'H&-jtR9g*=^PXRt&:"W^eFHV@64jGBcf\PS(#&ajR\K9m[S1T](]?&YT$M-^3f%\.;]`GTk@KW6mb%M;RO*C?)5gp3t
%BD,=5i%*K?AO+o>DU_Yp2/qd$]X;W=FiIaC4B'.OOn[kko1<G\:Z1lf5Jia(bH@#&Ik5HgS>6i-D#<SUlmS6pUO(.8UXBWX>0AY1
%l2M&3j=/CIM;a6S@d147lV(GshG"ilF7]bp)*`L1St;7rE^)]#6Q#N.OAoYp)NqX%lrK/%>c]UU1.s*)9ZqF1[T=`hT:5^6KH:Za
%ioS)Je?mGk]<TSnF/Mmekb-b]9]hUh1c<C=+]B$a"N-N?4"_DpCbTtoDRCF1<XDDXh7:FjFpP&4F]0U1$I1hlBbgf2`mX=_k7le9
%o0Bn8)`[9Ma`Z!=6TE/gJNT,W4!@O-nanknD3NduHqnk:9^%k-h'(P8rhrQ?Ftf=jaE=jM^lsI.mA'`mc7EPD-dU7f</0,on@$gq
%o0@OjE1,2,<'*G&i"+=&-&=O`PD)"934I"jE83C[f4rTTQVD:RA`IOk9Otp?*EudRMWN8R5[:OWI1!3"oOl.mF0kc\:$r#7W.NN.
%$eTdr)YNF7P`ErWp+?XZHoe[Kn#mYeR:%*IIm0[GLE\g`bGg=7'.%708Z[^-qU.kaclD98X.P"lX5AA3AUN;2L`uU3Mlj]gNrd@B
%o2shuV=*VHacp[cWJJkBFlCrA]&8^!?0Y9ml>^+b@K,^m7saRDFbJ<rd_S"g,tMAY]-m-49_[?,-6\\2)Gr[WrctYj,Sc+,>HgCe
%^ZK0mrRXh\hs4(8?T4lTaMpFl;g4hI%GP5o]cZ6_-H4NG+W0jJNR;;$4jR-_%MMh";`EW5RD%&n.AV(UlCV81`=g*Bg9BtJq71\O
%4tXZ46iWD%NGb+W3@LbSRs2WmF#SIolR_6/qM1L/)XdNAr@Ii6D<\74gPkB-om<uIDnAm9o-g^-jO`5qWSfqE6M+3K<>Cbc@np&M
%(9fg0MN=5e=ejaNR\-tijjQ>mqQG%m8_ITKp,Ca8^28@:osQm&K4$NHf,Io)c:PHSg9!p0fRFpM1SKQ-R7Gs?frCK"o8&f<k$Q`N
%)u-DYK`5bX.9]Se:P[1Wd[EnFp9/8@qI*u`kI]:%_^t\^]2c&lPKeF.NImcmiHViK8[N[9,XE&_a/<U1mJbb,qo@N,3^"c:kZe`b
%VtjCZlHuTBMN)NF0)`:C>rPQ5D+Sf\2Q,oMZ&kt#RJ6M0[uS/9`k6XL+*ATcE-tj?j>Y?2O1S>3H:gr,\KlX\F<(p\KOo4NU),`V
%Wq3hlNQ+nB=&Rn!h01KXr@m9Greu15Eo):ug&%m^_,L;RC?/:KO'J,,a'759;lCo.=:,nO)LM3jmLO#>H:fH5\KlWq3aIko,Sa"Z
%RdZ^.r`#Z_P],<>k&tU;KO&(`oUrp<Retgq]R+HZ*f>X2dsfM/(,aFiEU(kGeQkahhfV?`/=T;3gNV3D=5di,C9=Csr4_L2F.)WT
%)>Ef:ghW!Mru92=D^ESulCV!"2t+2E0&gBUq9_G)e*l1Bn+Q2JbB%sdg$P,]s"0U&]_gXLk3DH]Hb]4CUU.SEMqdQi?CuXhFn,(d
%Q]EekrV5m.nlS)gqoe<;bu8]V5Pam7Kg+a^TD[q\T&/8Zc-Ir+9(W\H<dUb4;Of]7IXH-Yp#4T[mH%^9fhG&]UT;T0MaurC=-qNg
%r;#aAj+"c;;TntSq7e$M_fC"ba+rRNg\P@WRJZqYY9-"*o#YN'<rfORrr'$<Y!9M2]Y7U.\9Ca=#A[hPkAPhA&&3FJmi&t<kj&!?
%s1eR"^Fmh#Ie40>]_D3`Z\u_RgZQmH5C@^!IJ1^>gV^45+(*h&poD+knCr_2='(`<]KLW<q;o.NqT1c.miK@)l.u<UYQ&^QrdF_.
%Nt:u\N;3_SV*?s0.*MD=e_/b[rZL2*+5]2qI_:)pAXrNaIF0^.T@LCFr9?LLs8VojkL8$6_]H+UYOh\DfB\[sI.Q*JT:]D;EO^4;
%FFJT4ffHt:BpNc82a=pc\NRPc4M&.L@ZThSgciq;n<<T"c>R5nr+V]'2_sZ66gs>?>,U8$,(7@#q-?eW;iR-c]'hDjecgNtc1Ub_
%PDdJ:q!PutRrr(bpOJgYA$ZF4o&bFcC/(J/Y7Vj]Ire.u<)2`LFY!/kp[Q@NUCB8Q1Y/X7?2WB)Gjt1iJ2$Zo]%9Hq&WT(9,6djH
%2uEi;),6XP5+B2/niu=4%mIOB4-*]GK<a8'rB^Ee%Q3p'qq(Q0\8Qb"s(b?$rqfl*cCUA$JFCdpJf"@RotPco\\-VUXnPFO7/"0@
%9eW^I5$qm&n#aq1VjBQ^R^g6b\'7nZYA:Nur,1=s+5M?jFg9d:hYl'lRm19?PK8IHr:9.<q30"fZb).5^L(5Oh>DO]-/\[O]#0E0
%p$R1iZ;pRqhgKb@jJhe8cRuEPN<oci-cb?AJWCBA0olQfk+>HLm*XYBq%mE.ZNk-h0$i]^h-<0nIX1[^k?#LhnEreZ[s'KOkGqR)
%s3?rA2>3!nkO6V*i]2;Nrn6D^[9#A_lbaOi^[HOgIJ>WGpM%iKs&o@slbA?&?4sM@f?\a@[pMAcDcSle+7R)B79TYnBmnMZQZlMU
%6U:%AIXV+Wme5cN%dJEt=NC:#mUJ8A*j95A5B"#h_n56<f'.:$kP`S::Tr'Xj@@;Mn,@^*RNGo7@4bs[PHX8UGNX7"pm8l[>Sg<!
%U/urSN9`]7nHuOTIX?6bM/*C2bP'chQYV([2#.Mc9B6="?X2QorT*,9]D!tM?<f^Dp?V&3Mm-\r?TdA&k[!nOY(20$b.(=b*5LEB
%M.9^BaQotV2tE@m.H"*/D$^LNiCGK+k2NRopt^;f2Ed]D^\^nmrL`bds#N,^r&B(KplGGsLW;V_^]%=fdHeOhbJ:79r@`qKQG56+
%l34TQG*Or?HKqHrppU4t"^PWQhNn*n#L'-aG5:OWd=\L-`339i0:eL!U4b64&eAuV&&!;7CjL'c\P^M-/-#1+kXp/8;nht2HdOr[
%I$Qc,"/8g.$mOP4&H*7)=]g?"dn)T:R9l$h=/OhiR:iG<qE*MJJ,P-e4M/*:dJ*7mm?M&$G;%]LKC"_3h'6LW9Y-9oCR"e'.c@`S
%YACB4mj]:EqKE\0gBLjf8gsqUo=W2ZHt\/8Y5c)IH\tS2k9O].[oISBLM(hHP9E`)/t[NNV=h_<ou8%MiGWj,l)Nohn:q\?4#h4j
%Mj4bGHT6#ZU]".me[K;.'JQE7Y?s,C:3HUg!:(>Fn<pA0G>)eYY.)H<([KG'_V3dLoFffA((^Bm^OGZ[dttaf2F".`rlqd-TDn*7
%qU^cMs(s,QrF'#[K6^n;e:Z(Cc]RF?4RHX=6Jl!/8+J2)d<@(C^+lC2EeOCP9c*IcDZ[,9>?5`,0Y<S\+X%o=71!D=X`26;[mEsO
%(=4M^Zq_'eg&;0rqq^nipO%+1N=01nbtn@[EW02m__4m*Z/4dK;8KHQiK_K/^O($E?Jj(Go4uR^Dg4eM<k>rq]Qh1clX/DHj!EfN
%gU$3YJ%3%;EdWr6?bQ(]*2]4.hT^"H/&\dMh4.f>_gGa=oh01@paqi^^Mi6/-T?Odq!IN7\pLl>Df>'hmOitgnTl+TD;"X@g:G<`
%:G2[iN\tI%lfV%bhE8JNG6QBdCJiF'c`Z=D(UrSn_.(14I.stRo=LLHj,3f0=3U,:n[fIrr8M6Ig621`rcid`Q&Ai-VjH!Wh-=-S
%FuB9<VT-=>q!NdXl\f&HgtA'qAanC1;?QY?Cu`#j*Zj1n?=2JOP;b_>HOY>tl*\'Ip[S(ob6m$:DJnoMr8lq7asp\nU0:0q4t7(K
%<6C!`"#[gho(6FdY&!G#bGNMpkLL1,edp3gDOYqDq:Xet5Q;iP^<s"K%9AO+['>,(S)+O5ZM/9USfV^2J('$$N'loHM2maNbA8=t
%cd$7Zm0D5&ZeI,k,2d*8f8iYAiZk)Qh#;GSr9<57DCa*t4hIN2<pZ7#Y+FNg$9ir_bEnj4gODjW[Vn+_cd&_3?ZA=0Dn%fWEr#(T
%F)eg=.<EMAIL>[,cL+h\L'mJRlbLDFguMapgTOML07lK9GX46At9MJI._B)^2QpE#qi>sJ;e.<aq4Irp*__?.AFhs5DhI=1hZ
%q:4:I2tQq6_9ZG$!3UY>E5N$Sh.-qsU.BP%*BuTks*]!9nYc,"06Xhn5LL5aMa$h/m-a5&B`d.>E,!;*pZUQhJ(_.&e3:lfs61WW
%s87rKml-m<nuiQWIhLY$O$Bb]rSm6nIU;I@j8ZXhs8>11T.'ANGC?VY=H-iS<@9LPlWS?iHht-0@Gc-hJ6]%j\iYh?YO\;-QJ@tE
%jPG/,Qe_/LP5FC=]2,L!T@R#VYNs]=21A_M>5u;8]hVB$"3_k<#M)+s>Q6;TX:Jm_+3Hqs*9,hRq`S*[ITWQs<pF=9lq1"0a%H<e
%I9(-4IEd*"$e'U,pt7^?s)Yn+4h?%3oP30I?DtC/ptOY[?g*GlZ5^Fsh]JutkM==NokF*)+-gsZ._7q-ZMl*G;<<7[c"d3$/T5pp
%&_up*lc2q;a&g<;HusOSEHcWC[XIf$G7roKs5HQKnBj7]3jm&4-%DOWkEJeH0(=ZM^;*lX3.^0kY,t2a`#mj&H?eW3:T?X,plkBs
%J!^34f:OmtYP^]sdhCX(H]JBuSU^P7o'TsdleCZDf!W"orHi7^g3`VZa$4R!T<J8Wpdq[VI2B2egq-1j8?_E7L)#/lpQt#qIWGB3
%LDKE8A7/$X4js>>2IBY&m@*E$p*.700%aUEQ;^GVpomdL`3`Q=gAG>Wf)?U=.u##NY0&IiikDf2=.qdPVfE?@?RH^)(\V_ZqYPD"
%LRZIjVJt\sr$Qs\cj)d<H[,.rrHItT'\7:?'Sf@E;LT$nNR,-ji$-;Li4<7Gs54CoNstU)7@LJV\Hb@@@Iq@\(VP6B&T6q-Ntsi^
%ZU2pS>Gj@&NSI]npg\0=_o#V>n^53)"7;ilos3u3h,f"C7,Q@d3?YBS\jf^T.PSk`*V1sHqWGN"'Q-D,>DuKL^P0)J<-L^>q5X1&
%9Tj9"l.fj5_+UaQ8b"9o=C-Q%YD[ttEp_W*`hC&Q[L[g"_QImJYJqRVJ^Z69,t(MFF`M7&2Deh/<1.DtbI%_d/_GL'LIkTRV1`tq
%EL7,,.8CR7P2KS#1.-pQ%kCdmLr>FEY5e@o:U5qkhnEq(c"Os<bSr<6:(eX18Q@6c#dKc`JuYkDhMQ&XmAqi%JX!`]de5bL:J!`Y
%+3^kk.%ufB65M53/o\"QaU?M,9__SlqXD'CID>dBpgr2upo9ArN:Wd9$=>HRn:Pl4rs#Ch7fF7%[27KdO$0mGa"6Z/^rH[*X8<@d
%.)"Z-UmR7<r\)r?T7;S\q4HPN5.Rn:oh1Da7?RF?O9]UUKBF"Ls7+?tZg5LVlRr_iT&"kcm-s6hGD#8Adi\VbLs/3CoTEDDTY,bq
%rqsmWp"fMGg^XN""%`Er&Q8ksk*8rT]Qj@j`?4MsHZo);BKP)Lh1m*M9!/W$J%XVKrpo.8H@=`.g"kKOj_)p.3e,5_n(lill#Y`X
%1#LA_VWZ5h^V"l?HZ?/`=TFM2/cU#R3)8?+^TX:.r_I19ci.L57[]nIe't`!in4\\'^&RC3B>`Us8;W@<S'1](U)7[=#];L>WdJ3
%DR)dIbDH,@)u/+Jp#EHsr`'lIcC%0=GJ?I[?D4(aj58soq"C9<?f!68eeON<7J'2&j&JSFDa"cIrhf2"s7GU2JirpB9R(:7q=A6C
%2c&:\mI.GXdJs139U#[#_QN4oeS"1AZ#8,"]kR08qc_.grp])0@",io48CZEAcHYf4t`!7Z?6]I(H20:Q]EG!r>!=XT6ql,mh@(U
%Nb`"T][&5`s7lC?D))":2kkl^.pM_6='MbE8:0d$i>D=+-@P\^:&=DiBi6m+]s'Ylh;#-lL"oOTI=&N]F^M+na$4l[,RJ:LO)CQH
%SmOSkQ,e*W>Q2=k&jQ6CcU7Q^c0^_$(Mu1%rqs[qe\t1RhTh)UnMan\\.`4f)L-ILqo=W,<MgDBca-ums%:NurmBVg-K0G8r9%)B
%`tO;pH?aKW0jCj.EV&4/`;KDq*V/dtmc*s*m/.Su-M-898IH.*p@N/Qnbgpt\*X^_LfV*jCulbs=7D*B3$*NA>'GDdiR_:jgE4'_
%kP&%":Z(W;Hb7bA&QQDfH?]2Gnum!=GbZashW*LLH9AaJ4/7ke\$!sGXXj!-+02[1Pud#=1clok,'\V$G,KK8T0GJdE-D3#k@]:W
%3.,i>o_DEiW;Fi]8iXSr]%q9PB31GGp=PS8B4':#]uT_,4a[*ON/ttU5J==:%_oTq:G;0tk.LH3_gB_uD@Rb\4^5BoF/8]2hj9kn
%+N%OrB=E)Wh2#Fr\SOb@Ium4ilW8N0ZdISh86UQ\4rZB`J*"T.!-!SKIlTV+/*PsFjcek/K/[T4iUY';6E]diI(VZBf;%.AK5)W[
%\9rI@-"bcT`$<NOemA.D(Y:V/?edcY>>)`qKC<1"RTJn"dH%LtSZ1BBVN)bS?9Gj0rR8aEPDd32*Idk[o'YkNbH&fJP8/J%/bY?E
%;Em:UrmTQ,8#)R1q2Z%>*a_*\EqoE=m@!/&)V#YOW>-QrFE\PQlfauOGDF;7j2^OnRe,LWZd8N]`%XsTRA]`Qafh>o4^C/I.t4i;
%oO$`9qU`L]ia70.kWSGUrC?AdS[YBgDXS(Mq9:SM5E+)#>H@<B2c"(]o(L7f78#Ieh.$G7IWb88g""+Z:S1uW)c`Jan'TJ\mI9W1
%o',+s)^16^L`]D\H&DDGjLL&,*kc7Yq.a=;m[L+]*ZgiBIj*;;qIn466bdniEVSW/g%c[Ps6L7T=-#"pFuDPZbNnc#kA,;Am_b*%
%;GE1h6HS0kKh!U+0`^DP2mWtTD-o>"Y>\a-F&h-..DG+\GD?\d&G)i/o0`P$H2[O#cd0?%_;AL)rajr,GMcbFf%s7]_q;EYLQBa[
%,jc=2lqtjK[4F%(4`Y5EfqknFZgu@i<fa]?=8>k#c0fBG.NRoMYOX4<bo2mlo#4]/lIfV"(k_]hn+,&6=+(s9(ADn8-+/*A)OTD1
%]"&Wln-(uNnfN;F>Wt=+0DaM5/&1MqiX7g=.t;6-0V,2Hp;?AoX.#%Qn:b.gEpCF>r9=%[O\!Jf*aF*,c@Yl3aCk$r:69HC3OL15
%ifam;k`<e[5#.0*55FdhnPunImAaAn2HAW5jO)G_o8D@9^URDWl^R4PZ`B;%an@[GiBKGOqSCGP/DI_=Ia(Z\4MCX_iPj?tZWF?a
%D_H"seh4Ztg&Kf6I.I9OM/hhtZN\FbXMa\s>C5_Y1rM%_Lpd2FA+f*dhq`K@ViNA>rr/qkJ$@!3jZjT;$mpT/_.57IKX^Durqbpu
%#pb,-pNq+,<O]U%Pt!pm4e64BB^Ul]DiMM6m1%p+r%:$,]RULPibiJo"i-=AJ`<8Pkd>rR^F^G.'DOG.[AWjam-dnmbmY"#SDrsm
%%A6dT5Jn?heo0>sih%R\6FOWmbb&:7ei*RI9"d35'm$Xpn>>9X>e6noQ^RgRfK^dt6sdRI!;@:UcHo_$Y*i9B@#`LuBPRYnI:k[<
%@6?5T=jGXZVZY-P:,Np.qFI5u\To?2;oe"j,V*gI<3=$d47eGYaS4C8d;)7::,<#@V/kpt_W[sQrqH3KJXgs#D]+@Ka7qFqk_uki
%/uk-83Tu[u#JfQZfEJANA6<R<Of/kPdi56kB?/O&>]hn&&,^iJQbQHCM#+an'd!2$l5RiMhSV`oYKmlrPKieX/)0uK]\B0?Xsa#N
%L3)U<4a)(Ej8J]hI?Nt(jph:^nD9,MfOp$&%`8b0bjupD9MT")f@`TZJ82aS$c-5.=s,ko?2F2U;m8JoDiFAnK/cjNL?%W^Ie97k
%?-2d%-Y:lc.6/2Pkbm[kq=W#t?]=f+$;3V*fn\&-bIE'<<)>iA;*_Y+7_h57bI+rYL-l,!g![8O38W1]VVrC)VgdMFc%b^Fr^>IS
%W[-dn!dQeC@_%t23b%=3eD.i'jp]R$':KY9YH:?&M-4b=COV_UZ/KQno5f"3=uJjOcJAFneYM4A-;@ff;:;11>n9r"d/q\!DIe"&
%'S8Rk%-ApY4NA8Q^*)n%:0.h\!!WDIAE$PF`Tni>2t/?)+8uKD1n=$^+898!FI[*K98JL$Hf'u)io9;Ze9X6r?fX-ub1sf2BBu&T
%DAH`r+_unjGDEO!%IbRcA%o`&;%Pg=q#=P`1e?PAJ$hmjD[a7I0i3)eZhIaf2QSuGZT7?4`k1b8/b$FTQ]R%FN<fQ'+`>QVPKiQu
%kVlW63oQaHg]&<H`:2XdmgPKN0*J!/rg)S!5-f>'#j6.cntD/Olu.=Pr[m'p7oAXp15Bfhk"@k#lgaWPe`UO`5X1#'M<+[6PNN09
%=El29FI^<o;BCfF-r_,Zh`]mph:%>VMEG8H*,)ibUQ:BTDWC2nFS<W2HN-@r!Gi#='+)'kP;jXOh]:'ek;*V/DH$Y(OnqGG7mmAM
%*U?(\ljl_uCqC]N+H#)6U,nsg;Ej@"EEm;sUaVi`IKql'1SLH=)K`:<NfFWp6s<g?*uQlS1#2]`EHda*h?Se0QMj#cp!3&jlA$F.
%D$UV_496ZK4UqDfV!K4\);hf.bL>re:Safl.T-G"Od8"inZhH@%R3FtiJmgYN,jQsI_&])\J"K4\Aam)DtKOfc1NTb.pt2NmGAVW
%\%]K0SX$\?goOCYK<@,-gK1RJ+GbJ9aa]*/>Vlr[0?tY0hTZPT2"%IM)'LBn"%[+p0F/hhd;ljgCi7k[DW)"VrF$ae-2SbeJ/rV/
%=H4fZ5`@u[/.Lea!:TEs-Ni_SQS@iMKDGSmh-_e#;h9^D:F5U'A&t83ldlA,a@o(`rHu3U8Z`72'AXMReNF-t!tM\TVa#IhL!)V)
%h;8b*LK/j$&19Bckq?ca!'19?mMh))F/[-"6"#fF\*)(iK'Mc*X_N1M0?7_256@D\62_?O+.%(!^?tL(7npKP?LW]coec.BBK+X2
%Oa^OS?JF<,V#kA[gkX@7iQ?@JNS,:`j4sgl2h2B>s'7`@5_)8:0g@<O3?#W#n#8/c^uVh_Q:T_i>,tV?4.lLHJ*2uNRaKRPKAhsd
%fB`&-NsB&/p!5QA1dnonV12F5ht7f9;/YN%_=PqTZqqm;qE,YH.t="Fhd]sdd,h#O-j&-Vk$(A7lUT&$_<kVk]A&Csj3&NsUA1]@
%7":g>dHhA&iUgd(dhWH=oM=L2p"A4;F`'"V_SjkNB]GP?\j5h";<E$^jg6L9]3++,=[K\17Xg9@\B&18.AC95Y;c1,,Albf_p'B?
%06G8<g-pR>iQX&8]e4#u/H''fC:E(s3t8s5mVc;cnFPWD]sI$AqhNja.'J3qj/MQK&jrpY"]8H1&1sq+`;#qS6!`oTkl6!g5686!
%N_dj"qs($.?1FTSqL;j'AR7O'60<Fa>T3d4(8NL/B#nm,iUEiqUS"c9qAA&Q8/Lq%c6pMr]X<r"d:YQ1kNb+p:`/iX3aOLJ5eB_b
%s6]Xg=[6+]p!h;^bTFil'5SP<6<7Ia\XS[6nCA]Dg(S-,>T#:lF$BU#(XU_&'S!%@f1]-QZ^K;*!D+hjm:91<nd]kh.Z\Rd=p<DN
%XlUtu0T^)+Z1QN45sD3!HW]]-rN(A!WQkfK:\k*nS=hANSTW+jh_,[gm<Dk#FD<>(@FT,YF@6[1SaGY`foQ`;W#qDb0J01L41q/7
%3l(LP/<le&o/mm#Uh)BCOZNi-5oF?ce`d>b<dR]Zds=f;C+oiM3###W?8Z[L(/J2q_d)F!K4C0a'RWe,-Q'E3:>!O;Ab!8>B&Iqi
%IVJ@E2(O\G:rf>-q0Fe%S^dsX"N?>YnM$D0J&N,JBMrD!X$5Q,/)d;(1Fghg"[HVMb#i3gc$-DGn9@]jN'?WV];JK#4W/C"Q2J_g
%NJc+BeD9j_K:e_MNK9F'Y@NJAeFu\I6obMV$>$jlmcN9IXS6Q;8ncEDo'YHda[Ho$s7?Z[`6.=cVnWLkp"b#gcXL<K?`O!X#8W4b
%H[G@O#?p5_^YOnlX[&Hp35>._`Q7P?M7EO'i/th)*O]X^_>9g)&LdC=2p;$W=kl5dF++(2c&(37B"])-G+7#ag&/Na^IiLLN;>`C
%C9ocb/3u@j_-@ZPXh#$h4uU%dGq?FXrjF7??iQdfhnFl\qp+Rs(5gH<4rF.PpG-Bf^e5RpO<1p\hj`H=R'.TYXf?Ygh>pr.aJ/V6
%o?$phh"Ws&BcB-/F(P1eK=NdC]H'NaCsMJsfjC/i5LRu6Q,pl!B$`aVqYqS.*TSrmGd%]<rauSdX1I2ekJf6+iR/W$hMm6mFQ(EX
%0]`+8H610DWUsD1ph/TsN:[YZk"7:fAo*giJ,?j07mk!,[1&?lF$Bkt3grD"o<iougPmZRH<^2(P5/PHf2#S_3uI6kr2q%F8;3Q+
%To.q+R(\:PllHNDZ$.M#L[u^T:AsmAZ]>`>@m@uV./!E,Fr/J1Y0^#ALG@X_[I2A-^I^7`raHH3GHZCtMjDrhIi@L"eH[S8_d$KH
%s(0P3o_;.n\`^a40g+Snqa0n(%BTN7+S0Xt7iS`RH<58'8(iZk#t9US)<9[2Jbm(a.G:pUfW]%3*Qrp'Z`=$5gWU`6*'@;HS6^U"
%%j9Ba`-Id4c5Ij==Oil?B=odVGE_Rd]'R,erM^iu<JIOF]IEU&&&Hn3anG-4[DMWFs2C13CaF!^[S*f9Ja&WEV$N."Ss]8#HOJ`P
%,^FGRejr;gCc"t%gP`>mE@@WGFA]DC/8HTS^%/-r4esA>m.stlOCu1dmIR\,aM`Z6@X!FH*TGOAV%=$>j'#n5NXQYpcT]%9<>`CN
%5s\(F"2\:=TR:qBm$lTOY;V*JS?3fDmKd_pVA.V0MLR8G/Kgf^aP>]+2N&\PKP'>Z/_:ZMk4_i7E.Lb8Zq,=KJ\?!`<]nrCb)m`>
%J..`Fl2q*F1?l0!pbK:*#u'?b6XhCT;&9l@YUMr>n.c="m4B%b0YL8(VqheeftN^;-qhLliho5JH-L,iM3n5*#\9`,LO5C.EYV)R
%`qG!-fuht&nRp1'4-m*0;Mjqk'kugSZf#fT?_FI14#nb6<72KFmgHb<jWcJJk!nm;2/;mRh!LkAW7eo3!1^<%DT2Yo51o5?jRpm6
%EefQ=^T_I.kCVXL:De@9bYmjtJ3'GF._0s@,U_6b6Am!WJU,,i#:3<fYf#cEa-T,<V2jNB@#ZJ<+`f0uOZ;tFHR8R7Rj17/drgG7
%U\=aO0R#5-aP$tgMXW5S;Th/Zi[\diW^qNrjLq"52Fdq6lBmKe^E9bX#l!'`Ol=]?0#iK/J.+FlAQaE@Nhf"q<N)2,X7-EknjCAc
%^X?8hi"sf^M'P6k&tOb77'1DV3;S$j@Vq\s#(37,G9R,cn_BOdNm7^f#ZhVfR)Nn^&Qa9\glN>H/H<_>%ct:mkuK/1r)^2][p.ki
%TMZ#,P[6b\c\;\8C`+#M3mAc\OG1u\)*3c6OtRp)j&o!SnQo=5GG/L[QbHbfUpSa518uQi=JUJVmrV(D_3V)IKGhiVH^),[B4#\Y
%fPWW`Y^p76MR=^Mo,S>[FEu[3NRYY-k2jsLU-FK,<A$TAA:j7=d?.=m<1M0P!Vhj*k:3235M3o+on,i=k75O:BjPN&+Sq;q?ZlQe
%X.u'UjdXHFU,l>OEQV_E3YO<`qN`+_+dXt@m6'I>*"JQ#;'g5u7j0JYEYPsmOS9uR>W2%LZCaHjMZTnZ`S/on0:09?@Oa+SM'S?.
%N)GM[l4lh;4*jH<Q[H<>,NfSkn68"R2i"T(!&D@1\>5o:ehPOOKM\m]a@W&4\`NQ(D?s=86[EMd8<Q'U5$uDWR,)[-M>;VGj3[%=
%0@N[;2PZ<`3"FSAi6t[R1:!W9S9rGMW(u(Ydhm=E/\'5ahIn4e5&uO1*d4>Y";?-:Qg;W81tJa@ql-AnGl]ZD2jnKS-*g6k2U?Kf
%`Wog8nOG#g>^Y4"'X4>`qeDM,^"Wo+/&QP@]%jlUE0YkV6D9qbW\]u;cG.r]5fOA_:B8(1?*KS,Ks*O[O[<;2AKom@(Fc&H9(e^m
%$kEIBn9PMG`=8uASkK+b/1r]=Tt:]\C3l*9e;%HDA>KG^raKk;_E<ICau)t2SX@_R,;_@]J^fs453#@kP+31M`M8j4egc'\(ph5K
%3RW]Tor^Ak0Z1YYfN<4Q(Wr/t.f.ToWt%W:)e"lV<KnI6;Gi#:QKtp,nu:QFp^b-2VQ2rd2(.l&F<XY0:MS8Da&Qqr84rIt`KfDJ
%P0fgD[!>StX[&6#T*euj-E)$\+@EV-SFSWPLi`W*BuiODGUq?BEtG*/?`@QiK.:*56DU>>n2O#pKK"Jh,&@jM%ABe1$Kn<1$j'rN
%5SSqFkXc7p=qq(ZVN/aIcp0(J%,H9>Vh""JPs-B?,N'E5:su1^(1[i+TR;B`@;K%(gt0DA3_M)GU89uQ!s&`a-E,B`Um!hCbU;gu
%mM5jZ^LU\@9Ood:'oqMC_\CH>gpaA;S4qjUI,>DDBtAheWj[Qt_^P2O@g(0@b[4O\%?Wq8lB':lL_61;=&Hu_ACU%798itjSP1^W
%4X3M]@g+WDh_GDdBjGg^9hQSQ*D9Jef<$m(]@L[QfPV,cKJ]F'j_V7&a@C=WdD`2T5jD7g;*=!Iaf<7kd_<P(1nHP_?M73LE&)*,
%QH5`E<qs?6^)MP:NA,H4L=eNQ6o>WS=h&*EB4m--6o$N/(^&4cWJ;g/`,$=7&5--U9d,7`dCm#kJk)T"d4OQu&J<4s`/VI)bO@S<
%=3XX&Br[9\\t,l;I(#]?4ZI_$S&T]'U<q"F+%9\A'363@(WLg`<VZ8%"e9jhDu(fB>s9-rQOt*-P1O_s1<i/mSDRXa1qDJeY5o$=
%86aP+YR&Oj)[`NR`>VAA%Wti(8<sR1hLtbU;m/!7$U%JEQ4i`Y\L(L.$S%L?_nn>G5d*V[U82@81JqpB-mm_oc3\$gC*VRSbLO!V
%OVmI0D(%oS)81n=Jo?@-bQ=3J1Sr;=R5`#"A4UdCBm6A:mcVhCA$1Q==&2\a[TrN;^@g=h.(M$?\XHV-_-s)g$Z/]%]0aCE';YKV
%eQ<9%8LcokU,m/X8li^-2?:e%>)op@3iEhS&Y=c7pql2(%K\HF[hZV\g&+X'r-q])@l"(Z,6(X$j2%]NoH9T`0;':jfodG/_i/3_
%CK1/95UJ0g/O!,sTII-HY8:I>Pi<\E01;XGqu:I"l.*u/hp-jN9J_PUdoQ$Zh@>;"*+0!ol"Je"EWCaQ@]Bdl7V6:B3_Slu_geFc
%=.Dg>ELoN%D=DU@,A#-Z)qK`*$BM&L#!U4uAQ,5O^_Ajo:X%Z/kf#`)=o>eKHRPe^j^g-BE,W*FP5arc+B-_t;9^WjQV8)7<'6-,
%\Q.%KDJZ,Q3"F)A+[=UG"S:'o^XUrjNJdt^B^d7\>W3>W6qUuI'0*]m!8PRMEV:4s.8OGLS/eBc"5UV6ALX#@SfaJ>54X8H,Lg"/
%d+3e+9Q:SI?c=%&p:j@Yl9[%hIl2cnb_WTR"jgFL8Znm[T.n@mn*!l+O>*:MO2_,?Fr]a$9LWf3ThBeLSaDJ?dWemqPlVM`3O`?s
%j8[l0[4#ugrA!Q#=n6PteFoF^#cV^^APOQ'`t-lRX[F0cO:d1T+W%>H@o2s7Y+?)KW'&\#f^Q6QVd)d@hS:J#<L,:G+;M(b:Ga!3
%rl<OU:,ZH7-gOPMdVo,G:!/Socd>oDBKo#LP$\O6&YmB]D3eO5Fu89mCf:Q)\9?23,]@3+]6%Nb.^X$d92@HV`LkpFj&L+]f8&e6
%BPHZL:GsnZ4?3EudubSYE(!U`L#.b^[:@E,9!me:IFm:L?TUXf3_+N;=*T^rm!Z(/7ERs(W:#je(I-;gQ?V)AG@O@jiLlWI3+)\`
%r$fYSUG>OS!ib4[Zg@9jX?%-;ddZHZ@8X#GherjI05D.PV0I6/J.d_%Pk2;10J6827Xd^F,knKU*EoVsh<j8.^HJ6j:EqbT-)djK
%e+;9_(9D8V1F@/PcCGqUS7/cCdYAlr!6G@Z2C2r+59gh,c1.WDG8aT!Mbcp`aage=l:Pr$nH:5-_))<EMAIL>&1NaTC(cB/!
%+\-A3Q&\[l/6`CUS/g&G0'&V5m/*JY.0-)cZ)4=(@hU/\Y>]FZ@(\Xh4M4j(e8:LTWb8b_6'3pH9/UomI+8fio.1Z%r`g:<*`<ko
%9!&c\P-rkAn:&o>8MIWr;+Y<B<l9PaW(`c4?-r0pefj8-M>S\TSPBDFF!/UJrIjdfi)EMfNg2rs+$d@.gW(%b[PT8KQ[Zf4.Zl'B
%arLrpRcIl#8Q2/@!ad;_dXo`G/YUcFmbo-G:FJ6m^MU._jRaoG0aIRb\OjLdQNG9V//[7_.S%`7&ft[:fP-QNRs/qS#K["D;XVOk
%4RjIdMbO(S*V607/W0.E5Pa9//g9K?IE?BA<^sEKHaZ=M_\9RdY_Cs[nPT93STmmd.K*8.`n8eP!8ET/C4p2o@(4"R17;,*4+_*S
%+YU*#d\TAG`EVTT?J7GH!01[;G$*+WgeL1'KQ2[V2&,PLAonUm7CEPVZ_3\5)\1^5r6i72"MQ^a`e4HI9$=ObRj+A\pF?ekcj376
%"^_mAA$nNST;nJ+/`^GJ(8U40nl,B`cF;fP.i_qMk'*X"@XM15E;.@u)U66?[JK>)d0E?<BT_Ac&1#M@?u](E`Z&^U?fq3J/pc38
%0Hu=#X\_]l(T0[f.lnYW#haZk`PH_4E'*-i_QN)6e(1=Q0rQ4bB<5Y;4WVu/Zt`n^8)Te?p4,iT<X\@_T%B&oR^LCE-(gYX1>t,a
%?eZ#qEQmP8H_`7FNqK/J_!r-p3?1Meq!rL533-#CCWi)P/kJtr;=qQ6JP>@Hd:7+^p<A5;e:RiR_TQ8t&UcdoA"iCd%PA;-H&Kd_
%Ps2tZ<Kq&Zd)9RU=3d!7>F%87-Gk7'EiG0majj+q-Zdu!!5EHaS0P%S5VI`K+fE:?KSko!nJ:_H8L<HC1mNk$H/0pqV%IlH^%=WN
%+DMZWN<2,*5'Q^eHXm]/>(%\3:9>IZ4O#Jd2-WhDa@W<^-G;)gJK&m&(G_?AAC3K>P1#c5RW$:^*HI/,V(n!7"L`qf1FI563og'/
%Ki1;1VXU2/?G#P+B>:["ML>@hYuah1PRLdDAq9k/TI!Sahl/C\a/20@i?`aQ[a_Tr-&VEaZiMICjlfuslK;:j?B!cgQ#-05Jn>oo
%AK:!(jGM.<KV1:\UV_jSMi__`?TASI\6M<pPSfmI9uAVc;uPH5hO";FPk.et#[`;%&(%pGW+2:MS<D3cA^O0(0+',eljr9,UQScK
%'t7P'CfkP!mSk<8n,nFP,r,%h&95giP/X<nW%)b`Q165Mr'ol46K?7G?L]j'9!/kmF?"p1S3Xp)3WR=H5V3rG&a=B]@oCN:6\0]G
%C8;9CpS$!i%MpE.T/0;c1[f@tDQOb(T3H[hSs,FRHr4uid+$m3Q3]ho_7&J-(@--HoOJU`h_+HIOAKq\N6C20p.ZN5g7VVEM"C%d
%:ASj6HagN8_J($UQg`\92-&OC196CZ)4W)Rfspp!5;/7<m]]s%GEQ9=fVgHoj=7FPZFR3:>iTji6]JaHH8Y5om,(XNL^3MeV6-P(
%>:p]6,^^D:\o!0q*>]k!H55LEY$9_0YRoe@>Ih?=gVReRYeg2?7[8UhF#[(mh7,3e[N6-G8#I@>f%sPM6!un[7Fc1K,cj'*qJZ7G
%"]R2;aS&tMHfp:nndX'!O;-RjrUJr(Vb'8O9rRfHCD=@sb*:p\+j[DVk9F?:U`RXbWA$`ES!)eG]!SEDfB.]V,;I$1i%5VX8LG\2
%7[m5#C[$0<V8U/_1oprG^8i5\HDu5ejm?W]9I^9ho!?'+WBral#KP=ak;Yj+9^I#_6jmJ+0[G5G;@RB2=t173%t+^$4qg=>D[St`
%E6.@]<3KF5pi,rnd]6>V&nMK`9.o]ljVl`6Jdc"g`mC\JKm?3VW9`H,(9u0eN-s1(D*qU86'D4\6.*1gpG*V4hAUPM@<oiZTt96P
%qhJ!(jQ">K&5(\^hTSSEh2T,^4jKX6QI3j;,h/mbTj5`QqPY`e7Db]Nk5,llEqh\E&,'4s:W8t(KV0`1k/m&7X9$cjn*;!%Zt@37
%9(/BeP,K0pm;J,bS;6#L3TQ`9]]aI_Klsi`^0UM7,JP7Hht!**^5CB!gL@g^-8=n7V@YWZ0)dBYO+Ll$*KU^se0Vpe,$-``#V<iZ
%H/*p=BJ'\N>,rB1CZ"&X.Z%;E"Bf`4BZV3=i\9Z`*"'i,SrI1V(R&\WX^p(J_kRMiVWdUuI5A_Dkr01UG%QD?25c/`2_!=X\8?JG
%dcCOU!9Vt,*dJbPb"r+^B]KbB'Pqg.KJYB]+n'Q+fi"]i#@8;N(sP#D)7B'u[Xu>0E?dtK;&/0q$>ltTXF.h=\_:/>QL4,.Xl?.J
%4'?Yui.,\UpelO4*(HSP%lS6.Mpt;3>GPtK#E34r1`>MWgOt*re!Jh>hn\A4K;T@]mEto)_+.=,rZ(TWV]bh]lIF`e[#=M?gOe2Z
%qYZ>Z?[OWsAiJn#]ia\5^>\4!rl*1IBA2&3iX$WYIdme_M:=c9Il/Y7DgJfsS@&8g3Is?l3)W>A02ZT9?Itf31a9U4WPc>4&H9p\
%8qJ1K8#`#:pP@ElKMhZDN"etYXh/Pb2CY(&9^nGmFtJl]Z'f>X:l69hH)fNr,F2YsZ]m%If`(^<LEN*/$:22g-A$3tn>)F%,YNXZ
%J'^.Ol.E/%V;T4r6aq3F?F,:mCBa^)`,n+":9,AfAdEpF2*5/?\?O`XD=V2(c(Wl7^5XpX0T'a@.WG?'(dPPtHn1*iAd]Cb'1*b'
%`U`0pY+/pBMcSBVG+\,P)]+)]<R]O<1BU^MB@K'N+fF`u\!U/*l:gXf0"_mIRS&N_?g+qLDS=/QaHm!7o\kkL`Tc&`LMt_j8AH1j
%X`OP66CmTfLaCdn6j_;+$7SKu`tY7#-LUVojndRM_K4L$K=i4qjM2CQ7lup-pcgN>/2\tSPbPDegp3?8d7T]RdYd-L5^kPhlCU<!
%LV"DLkN5^WmX9MYPCtm_T<0=*P>;*D7k4H4K,'AFfU->%F=lA!7`/0J%]!Sm+^)F(\.Nk=p)FLkT'o#C@X\A:"d"U64a<-ll3n'Q
%B]>:\<+O_/4t7S&IB1N@;:H@WqTUSRaPiV;FkoZ#&Ph%<0<TE0c%!YJJWV>RofA-Q/4rEj?8?$7"qcf1M_@Uadj%gLfb2WoLM6^(
%-3,d$5bS>^_4<-QSpA=r*<,co?f^d24*&s[RZuC6J@<X83]a2o0]GGS;b4Vs;K9A7kKcrZXK[#K)HZhN+lHs)O:aTF3WEiKC!bJ0
%478@`n$o%,75*.InKhfg>f9XAK,hj0aT:eG#1<pus*0]uUh+<(7Jg^&>.N[11J5;^N*Il6e*%lib^To)PO]>us):Do7[[&(:g,Ce
%mk-m;dG+bBC=3gYFEL91oD.L+E!7L'2Ki*4!2=bBUP%tWdqhGC&)Zq1h,&Dm7J+ZZN1@*a-<hi_Qp&SJ@A2KZ_CC'PgVP9/^5T9$
%a>Dk30&HtJZF1o]q4Ndf=%mVNe*Jt!D`dbdp]b%PiLeE5"M$!`#\l#O$-%@s&BqDq':^Y=,C#9[9*eL87u4Do6V&Xa6Ne;qQO"(V
%852M)ee3d0,Zc1ac1$sRXXQ1@&%E;tnS[b,8__NWUrm%!,%KH;M/6mA-M$#g9GI?S*q:4m4,L8&cOuLo4==ms,"jc6QNki"I[Z(Z
%N@.<1otmZG,MFd[J`p.#D;q>9H&+bm=sHKi>73/'71-tLdm"BO)jqjMJ_)Pa;t?cAY@6aT3-aYGM9,CO-M'-%1tgl#S&mC3Bpd\4
%-u&8?XWJmqfFODfWe`!J-LN[O1PsG$LFF'B[nRH]5@fZ=?GB0j=g_j6Fn;[`gW;'BKm'>(.ZK=eWOf149oVNHDL*T0;-R@q9YD@l
%1C[AMH4nHMVKi7TXN]FZBj2@ZLs3]li6A.Qc=45ed@&ZJ+108K)U-5Nk#F6Z2TBSL=8ffmoF[U\A(AmaZgsV:R56]UAM1>*@<8tb
%@EnJkYh/efG1t1]BkoKubPduW4KYa0T61&GaI(T0`6>m'a]"+s'V/@mT#B.<m\XV4178*KE("2IOkRK%9-h7u/.%$9>;D0([1qFK
%c;O?%_j6TAOLDOpc*F/f0Y^g=`Vdrcpt-Z?gI'\--%\Xoggdb*4;Y*eUE++3kPj2X+(Vi<oPBFfj5d"J:!Y:FCG?oS>&/^CAplNW
%?EDuEilaCZSFb=XhFH81SIARX@ao%-p;Nt>o@[/K)_7;-[Qp7,\&IL8"3B-CHo#[k^+@jD]$1ns]iEJ_>G%Fl[cs0+\#kp]cZ\qA
%>B\c8jgBHUrYIZNm.fq`9!E/nk;TJ*>H3\Lj`VnaDp9;,HhTKMg*cg!gV,.JHhP?$L:6;*m#a!\n4f\p#;>TJ8,-"F30L9!Mk^P4
%dSXB'ClW\N5BMj)gO`oQ-[E`USi8[snaXuj:UnSCThpP)UYWW0RJp+XcE_@CEk^TI/a(0/a?/RI3[3V^>)JinWdM?_523^&`HRP"
%=#@fI>T(O:G#`@J^?]ntm^`VGIbV8tGMZV\:%-p*FA=o8>28c*_2N*d<^+T]qq7_qh6#\[cJ:GHXmXN0k]7h><V2m9j62ok*]fR7
%p4U)JG>E7ORX7ZE^"N#2.5RRS#F"e]#)ef![&u'p?>ILH'`q^p.mJNk]_UZ"j)&gR`r"/$'tju^Flkh^hajA$ra,6e[_^B.Rr&`T
%?bYc<q3m#!`RiEp1;7=;^%@e79ZZ\)<X4(nAENfHA#AQF"V-LkXc_rG_&PK8=1LC8*=b(t1(Ei,N,nk2R6-YXA94cXa?^!0[P\Vn
%XB>dsKN$C,dnb[5c5Do7P=;LV&SPoE,rX76;f:%7YIR?U@Cr"hCm%FPTBO#;0.pV#*fXq:rDbQ+)5gA:1Hb"$?kV6bK..1JR)_jc
%)"UmtMQEdW,14DLrup?5_]ck9k$Hjjp;LT'YiAg.T%u2g4m-.]%\2#]9O'W@Y]:2&aiP"*;j.%K\-1[MMPK!7*uR6WpXf1d[FX@C
%9?34qP$W!WX3L3ej;b!mL.ca^01%H]08._jitD&1P\c5RQ5IbT[UME4B>q>Nn6):EA"Spp'Vct#S1dNT>B;;pP-^8B0t)VqM]km3
%21M_2!\"c`i@C?,]T-XZ^q:^hd#+GA^YAtF6ol&^'`1'r=#u&nW0O[o%t1_u+rt8J'MEi!6fluT)sW>uBBX.%p(@!f?PDCSCZGmg
%pLhB_YA)+6[lKJ*GgcCn,chD8OlsA-)sP$NOU4Fao;sV0od1I%aRcPJ/StmWBKtTUU:Y8LMYTnZInUUpH`ZA6'!]i!Z'C/`B-B)0
%pUQe6]h[\?Zo*t1mJRpHeJFitWH)7_o365On&Ei=pi\Sa$h=V]Z\qDrF<QgG*=fWuA<:[YQC$W&&PCAr-!0[5PYQ!@/)-+o/a9`5
%k/k-sE[D`B6;Gg3X4NTLosmj%rA,nq4C0jBK@42#-3DJ3Re)EmFOp8HaJ:oK@57`BG4%,]PYYhSnnsS8R3a)GQ"rD)HKt;s<d$Mg
%Z]4]f,5WgcG<+iKIa1C%Ap:CYT651\juANJPhu01n6<AiYE&Jm"OHn;i"1+9KlE)BlS+$]?$6rpc1fPYP9QI7a?6%Bef/\Ap_aST
%cYd/PH^=Kgqf@ZO5<l><D5H;DVLg+j9@BY\e'V+j8hTIAHIb$AZdJKOgcKTg'r!KE<lEM-<%1BK/!-`(gq9uYn<_73Pkc#n:!k&b
%Y+?TCRX]-heC+?]T[k\T[roR;G1X]Z1fQ+5*2WN(0Gu]P_kssVJ'S?<Dm`]DZ)\*G9s,G4R`/F*fUUTWT.O]gL:0mcjQWJfH5!EX
%enu*l^@7Q9k=`qa#eitK`Smlp_]l`CS]9Tqo1k3^7"7CG$d8t*\%3rkH!;ScKm.:;GJ31-C%m&**CSW,n;q<2XRioEHa&KOLlN!_
%2<Y18"+s@D;CciIoqg#Hqn0ID,2O[&\_q5pI#UV$QZZ+kDtl)/IlQ-JnKY<9(*DB,_m"($%bgkpXP)mR`QGUf0jCdhK%4mecl=pd
%K:QdS*K&/JoRi$0D,7hU2u.9>`-\ARUWS\RNE4;JkEsMVgpe>QMiX3]@!0=s^4qhLlfC*/9Oc51Tpb8S<3u7pMuABt0pQm#<q`_c
%ZfPZ*S$QsEE*d5]hWO4f!\-l.q=66:=_kWNTA+'<]T?sQdA!V]_IEPUm4UiKMR\$s;WF&,AJJ@e6FG@D9;cfG]0im-PBNt>*r="J
%`Zt6gRp:\^D.\DkBM4O8o<qoiI;Ci[+pVYm`/b2ue]RS[4+d!^G;3I&\a-eZo\O;Q8=(h02da\<[nIoWC%C49&#gYJb49Ru3Z6oA
%[u,5"M.5"Rd\DkX*B*)5rQZnq[u1T,0>F$BHHGjDHjU%]NTq0j_M]*FK'UCre+jo3cJYmU;,?Jl,lET"$ITSVMbc1M_$u0'$\Q4n
%j_4&Xo>@Yb%QZ)@nH9qgEUQS;F<4cHklh#./qs2$JgGcjWM(EaE;-Fpbh#d7^./D6?(P<qW5Ac]&k28mJ!!M^qXa:PO5or.$T.#3
%EMrZNnImr[l;s`McohM[GJ(Ci*RP$a,tT1MEtlSX_$*>$4Rh:(8<*^^!%0s%,^DsH(E>"^p]>O_YtpBJR!L6:RA\Z@n<=HI:-=o1
%"[]7"%I!^+h<!S['AEG7U<'7ldsHjt)Z9gMX+1)+@0@[iYYSS[QLMl9fLE,0T&eQPL?<ok5]b:?dQX6=T,7q7:BQO<QJl3NU*UE(
%`BCUu=ql>s9h>OoL*hm*lDZ]DEL$b5THsqYM@;>HcrC]mB]=F[0-^=qjPrX*H:f<[S53NPeB1&nP,4aH'BYTjI&%l%)`SoG3/;6-
%gr*,TMP:u=!M#[uUF,ts+^(EcE9[m_1Sj)q&7=^$F<HUBp#\69L#m#?V'/\cD[\5mBGNP45)Eg<-XF55j's@#Hb*&T:n&T6O=3_o
%5a+Atj@,Yp$_EM@#$jb$bDO/.aaKsqr$_VOlRLI!;I-Cq_N^,#;uo#70g:nX>97=6DE/fV@Fc;m+m+[V@2!$s&/sIBfF[OkH00L4
%"[r6`Es""Fj@,=rN1ZD,cl<r\Ktcf3ZmLYQ?WGuS6.c0:G0WQtk5K0?RN3Q*,#cr!9i0)s3Su$(YfZ/1VnoFiYG@pcgk@l=-ZB-J
%6jp[R$D,';c&3Q^:\[S6;1kK5+>9Mq&kmsfn-+'@J:(6[:'m8jkYFAnId<?3KgD%?1.^7LK&`m@87JTE1-eoMo`h/tP@l]7)+MOu
%/AiYtF0pu0K>UE8KSIXC$W0lW%k[sUV#$B&J0iYgD=Bfp&^&a:o[Iq,qHCSJ@'?A/^dUo5<GXY&@!tR0Oq`j_/:@j].O)Pl-`=ES
%UF_D9JR-\<M1#^JV+&`X-%`QZ:HSsAfYA=2m$=Oum'%iZ"]lL#9u7_;:LFR'SeT[O%rEeoaE^>!-h*@n2o9[t/-t0$'Ehp06"#!`
%K)ue6i+U&dC1m^V,,GJc8IZam$6P^0;.-KX:0A8G!?j42P,6\7.#QrF#`,:\WH7:u!0@WW$4]Q\JnbT9$f%GQPI3q$7i<5j(8WsH
%5%"Rl@sF-UER#C6.2>rE7F3Cr*bg2T\q8Sb-5OV%'&^RFZnTWY362.?TLK]4"UnWT!!NpKGi]*0[9<l>1c?N.a/BILXV=K.fGHI:
%+B96)>7<uCDEDP>W`06s/>]E=<pQbW\/NG,Q2_e;RK;"c8K<?`RcN!II;VGB9nR];;$.ej=VXq;"c1P-Y;`*tnt6BOh)Z72@[RhU
%\`#pdpH(6!3hf#=OoZ6E&.@p#'0N[qT$SnV!dt*i6h34i(e'#X;3T)Z7"]M74N5$[fd]8\`D+FAYPRNY/?@1K>LC7%XTbM7/2IQt
%fPVO^;CqOm;[_js;3)q^klOT_Brcm:O;V416k-9>6Ql&P*M'9o70Xuffnp=I8ZXC\@Z^`o$+2G,A#Z,b5Y,iAKB?$mJf_D0n:.;B
%O@Tg,V&>U<L*0_TU?'3)@R,5j*Y*?Q#e8e8QuCi:]b7u57=p)HJl=F_'<2&oMg2tTVBi/I2#r^m1)V#$4H,=k;+`-0WaZ"P_ke>`
%HpKP`Po,HS^W!:H%J=8VfLd%+fEKej`UbZ^)"JRc!"W?#C66R;S&DKLVl6%-;N2eaP+mr<8AmWgQCJMl8I@<unW8]ur3-Q;;*p):
%E3[D+]-BUG*-^sh(6T0N)Hr*fIr;&ALn?)dn.9+6m$]2@*H%6c\,k>l7%Ap+:ls/5\95Z1DD;dR)Z^%06-t-S;,djRHU+#(<[EtF
%H-MBq8PA9/83&PAJt]FnMnA7\a[:HiBJR*$h?nGIF"#mq=0`eeqH;Z6H8/G(,%V$L1s)d.Kj#?6n3G2ODo"se.VNNrc*J6?AdMI^
%XBJptj(['GE%IFrLcY!T%FmGX-=\m\>G7Xcd's)s:S+IR4G0(Fg+YXe$B0\?X7c^.n,b6@J/^e=Y\-V!<XajF4ef(nV54?oj\Ji%
%;Op*E\-!):jN4G_:?7Bd#+_A*@;[3lW:U_LGrrP6Vll#<dZ$p#c_DB3!6(SSoo+aBB@Z07YUG[tOPcR*@H4mhC8Vh!WUZc&#7YVr
%b6_>cMO]cZ1f8mr5X8CtK)a9O#9b*q+UVn?E\^a#@T?!`&iKp?'$YH3BEMsI'!KjLP_;b^3[[I;$4'0=9j/kaSd\HAUh;s`S2PZC
%'X!BrYAW8_l,hn3H>3?NY<lJu/P3*8GcepMrMItW73<q]n+uW%>#C^Uqu7^%i=dU8LG:j:i!CD8GhDB&:]&i-Dq'A"SNBrUa\qE-
%J,M%>*u%T~>
%AI9_PrivateDataEnd
