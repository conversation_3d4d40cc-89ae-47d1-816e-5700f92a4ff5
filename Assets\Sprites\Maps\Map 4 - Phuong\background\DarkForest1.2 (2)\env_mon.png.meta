fileFormatVersion: 2
guid: 826bbb6d43705d34ab22edd65de00ba0
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -5450098533584984798
    second: env_mon_0
  - first:
      213: -3898532288542658078
    second: env_mon_1
  - first:
      213: 382809935399234648
    second: env_mon_2
  - first:
      213: -2904528127585307004
    second: env_mon_3
  - first:
      213: -6668294397942542763
    second: env_mon_4
  - first:
      213: -215733659847922012
    second: env_mon_5
  - first:
      213: -22581420605178192
    second: env_mon_6
  - first:
      213: 2622312911799993598
    second: env_mon_7
  - first:
      213: -6074204233402466008
    second: env_mon_8
  - first:
      213: 5778815970166586585
    second: env_mon_9
  - first:
      213: 9185785334314814948
    second: env_mon_10
  - first:
      213: -38429231726456064
    second: env_mon_11
  - first:
      213: 6220343266002344
    second: env_mon_12
  - first:
      213: 8290907795128543390
    second: env_mon_13
  - first:
      213: 1572440475978350073
    second: env_mon_14
  - first:
      213: -6819010843158495235
    second: env_mon_15
  - first:
      213: -4312887815423232262
    second: env_mon_16
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: env_mon_0
      rect:
        serializedVersion: 2
        x: 0
        y: 143
        width: 385
        height: 337
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2252ac2724c5d54b0800000000000000
      internalID: -5450098533584984798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_1
      rect:
        serializedVersion: 2
        x: 399
        y: 431
        width: 81
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e13604f981a5e9c0800000000000000
      internalID: -3898532288542658078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_2
      rect:
        serializedVersion: 2
        x: 379
        y: 367
        width: 26
        height: 38
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 85c26f7ab93005500800000000000000
      internalID: 382809935399234648
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_3
      rect:
        serializedVersion: 2
        x: 427
        y: 367
        width: 26
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 486b52b8e0b01b7d0800000000000000
      internalID: -2904528127585307004
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_4
      rect:
        serializedVersion: 2
        x: 255
        y: 127
        width: 34
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55e48e043957573a0800000000000000
      internalID: -6668294397942542763
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_5
      rect:
        serializedVersion: 2
        x: 295
        y: 127
        width: 34
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ae6bae116f810df0800000000000000
      internalID: -215733659847922012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_6
      rect:
        serializedVersion: 2
        x: 344
        y: 31
        width: 34
        height: 121
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0befd4e4156cfaff0800000000000000
      internalID: -22581420605178192
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_7
      rect:
        serializedVersion: 2
        x: 415
        y: 127
        width: 64
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef442cd13b3546420800000000000000
      internalID: 2622312911799993598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_8
      rect:
        serializedVersion: 2
        x: 1
        y: 15
        width: 32
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8214fd6ae6714bba0800000000000000
      internalID: -6074204233402466008
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_9
      rect:
        serializedVersion: 2
        x: 31
        y: 0
        width: 66
        height: 113
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9dc3224e18a723050800000000000000
      internalID: 5778815970166586585
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_10
      rect:
        serializedVersion: 2
        x: 107
        y: 15
        width: 90
        height: 104
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4edc3dad7177a7f70800000000000000
      internalID: 9185785334314814948
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_11
      rect:
        serializedVersion: 2
        x: 203
        y: 15
        width: 90
        height: 104
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 00314fd31d8777ff0800000000000000
      internalID: -38429231726456064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_12
      rect:
        serializedVersion: 2
        x: 319
        y: 31
        width: 18
        height: 58
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8a540c68e59161000800000000000000
      internalID: 6220343266002344
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_13
      rect:
        serializedVersion: 2
        x: 406
        y: 79
        width: 59
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e98672a66b83f0370800000000000000
      internalID: 8290907795128543390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_14
      rect:
        serializedVersion: 2
        x: 390
        y: 31
        width: 68
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fdb221284e62d510800000000000000
      internalID: 1572440475978350073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_15
      rect:
        serializedVersion: 2
        x: 319
        y: 15
        width: 18
        height: 9
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dff300e44c10e51a0800000000000000
      internalID: -6819010843158495235
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_mon_16
      rect:
        serializedVersion: 2
        x: 348
        y: 15
        width: 26
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af6769de16b8524c0800000000000000
      internalID: -4312887815423232262
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      env_mon_0: -5450098533584984798
      env_mon_1: -3898532288542658078
      env_mon_10: 9185785334314814948
      env_mon_11: -38429231726456064
      env_mon_12: 6220343266002344
      env_mon_13: 8290907795128543390
      env_mon_14: 1572440475978350073
      env_mon_15: -6819010843158495235
      env_mon_16: -4312887815423232262
      env_mon_2: 382809935399234648
      env_mon_3: -2904528127585307004
      env_mon_4: -6668294397942542763
      env_mon_5: -215733659847922012
      env_mon_6: -22581420605178192
      env_mon_7: 2622312911799993598
      env_mon_8: -6074204233402466008
      env_mon_9: 5778815970166586585
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
