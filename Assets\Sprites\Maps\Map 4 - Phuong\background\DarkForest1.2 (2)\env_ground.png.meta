fileFormatVersion: 2
guid: 2ebaa26443de9834492c8c99e62e9873
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 2782836665789986762
    second: env_ground_0
  - first:
      213: 6637455614210474540
    second: env_ground_1
  - first:
      213: -5058560162748447859
    second: env_ground_2
  - first:
      213: 2929950904672563563
    second: env_ground_3
  - first:
      213: 7130633397967381619
    second: env_ground_4
  - first:
      213: -4311885149137203828
    second: env_ground_5
  - first:
      213: -5371819772058992598
    second: env_ground_6
  - first:
      213: -8428063975171573973
    second: env_ground_7
  - first:
      213: 7770080440571327581
    second: env_ground_8
  - first:
      213: -2384425090723788689
    second: env_ground_9
  - first:
      213: 2827619403745895511
    second: env_ground_10
  - first:
      213: -8031030576105267224
    second: env_ground_11
  - first:
      213: 7789899911944494111
    second: env_ground_12
  - first:
      213: -9110329931068465828
    second: env_ground_13
  - first:
      213: 3787543183262747789
    second: env_ground_14
  - first:
      213: -4273454622082669958
    second: env_ground_15
  - first:
      213: 8488042998240854368
    second: env_ground_16
  - first:
      213: 2768182140312440562
    second: env_ground_17
  - first:
      213: 5253105455750098979
    second: env_ground_18
  - first:
      213: -3851761324628544306
    second: env_ground_19
  - first:
      213: 3799021305408291019
    second: env_ground_20
  - first:
      213: 5243446140276464743
    second: env_ground_21
  - first:
      213: 8467375619425056562
    second: env_ground_22
  - first:
      213: 778527646811231643
    second: env_ground_23
  - first:
      213: 8537531731111758989
    second: env_ground_24
  - first:
      213: -4655879827129827720
    second: env_ground_25
  - first:
      213: 6714858745654368616
    second: env_ground_26
  - first:
      213: -2142948528512735155
    second: env_ground_27
  - first:
      213: 6790296377891678581
    second: env_ground_28
  - first:
      213: -6690829623150058943
    second: env_ground_29
  - first:
      213: 7173265673725745901
    second: env_ground_30
  - first:
      213: -4121915864541026036
    second: env_ground_31
  - first:
      213: -6523502346638532111
    second: env_ground_32
  - first:
      213: 5659905415623020448
    second: env_ground_33
  - first:
      213: -8109034803477463248
    second: env_ground_34
  - first:
      213: -6326159396874922370
    second: env_ground_35
  - first:
      213: -7287555722988221646
    second: env_ground_36
  - first:
      213: 3696912729871100108
    second: env_ground_37
  - first:
      213: 5960838793306249308
    second: env_ground_38
  - first:
      213: 2098896351094202178
    second: env_ground_39
  - first:
      213: -6804901023937508932
    second: env_ground_40
  - first:
      213: 5065935832426814805
    second: env_ground_41
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: env_ground_0
      rect:
        serializedVersion: 2
        x: 0
        y: 440
        width: 33
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ac73854143f9e9620800000000000000
      internalID: 2782836665789986762
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_1
      rect:
        serializedVersion: 2
        x: 47
        y: 447
        width: 21
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c220071e6bafc1c50800000000000000
      internalID: 6637455614210474540
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_2
      rect:
        serializedVersion: 2
        x: 79
        y: 447
        width: 34
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d83cf6855526cc9b0800000000000000
      internalID: -5058560162748447859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_3
      rect:
        serializedVersion: 2
        x: 81
        y: 351
        width: 80
        height: 106
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b61b75203d649a820800000000000000
      internalID: 2929950904672563563
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_4
      rect:
        serializedVersion: 2
        x: 159
        y: 442
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 374e165bb4915f260800000000000000
      internalID: 7130633397967381619
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_5
      rect:
        serializedVersion: 2
        x: 175
        y: 431
        width: 34
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8d056d5d4b1924c0800000000000000
      internalID: -4311885149137203828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_6
      rect:
        serializedVersion: 2
        x: 223
        y: 351
        width: 80
        height: 106
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a200101e0667375b0800000000000000
      internalID: -5371819772058992598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_7
      rect:
        serializedVersion: 2
        x: 272
        y: 447
        width: 32
        height: 33
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b27b1a7e770890b80800000000000000
      internalID: -8428063975171573973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_8
      rect:
        serializedVersion: 2
        x: 316
        y: 447
        width: 21
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d54f6783efed4db60800000000000000
      internalID: 7770080440571327581
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_9
      rect:
        serializedVersion: 2
        x: 351
        y: 440
        width: 34
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6841e16302d8eed0800000000000000
      internalID: -2384425090723788689
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_10
      rect:
        serializedVersion: 2
        x: 383
        y: 434
        width: 97
        height: 46
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 754a648cdd8bd3720800000000000000
      internalID: 2827619403745895511
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_11
      rect:
        serializedVersion: 2
        x: 15
        y: 415
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8efd1f2cd3c0c8090800000000000000
      internalID: -8031030576105267224
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_12
      rect:
        serializedVersion: 2
        x: 127
        y: 399
        width: 130
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f1c5c4f72b84b1c60800000000000000
      internalID: 7789899911944494111
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_13
      rect:
        serializedVersion: 2
        x: 303
        y: 415
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c5d1e00423b919180800000000000000
      internalID: -9110329931068465828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_14
      rect:
        serializedVersion: 2
        x: 383
        y: 392
        width: 97
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8c04d9357e009430800000000000000
      internalID: 3787543183262747789
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_15
      rect:
        serializedVersion: 2
        x: 12
        y: 303
        width: 165
        height: 107
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a72e207b9a3a1b4c0800000000000000
      internalID: -4273454622082669958
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_16
      rect:
        serializedVersion: 2
        x: 127
        y: 367
        width: 130
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0614364a2269bc570800000000000000
      internalID: 8488042998240854368
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_17
      rect:
        serializedVersion: 2
        x: 191
        y: 303
        width: 181
        height: 107
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2fe47ea7dfe8a6620800000000000000
      internalID: 2768182140312440562
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_18
      rect:
        serializedVersion: 2
        x: 384
        y: 338
        width: 96
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 320b6a17497c6e840800000000000000
      internalID: 5253105455750098979
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_19
      rect:
        serializedVersion: 2
        x: 383
        y: 296
        width: 97
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec47de59b7bcb8ac0800000000000000
      internalID: -3851761324628544306
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_20
      rect:
        serializedVersion: 2
        x: 0
        y: 264
        width: 33
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bc0800bcfb5d8b430800000000000000
      internalID: 3799021305408291019
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_21
      rect:
        serializedVersion: 2
        x: 47
        y: 255
        width: 21
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 76c521ecb7674c840800000000000000
      internalID: 5243446140276464743
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_22
      rect:
        serializedVersion: 2
        x: 79
        y: 255
        width: 34
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23732a78349228570800000000000000
      internalID: 8467375619425056562
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_23
      rect:
        serializedVersion: 2
        x: 81
        y: 159
        width: 80
        height: 106
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b95110c15c2edca00800000000000000
      internalID: 778527646811231643
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_24
      rect:
        serializedVersion: 2
        x: 159
        y: 250
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d8cca8b20e76b7670800000000000000
      internalID: 8537531731111758989
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_25
      rect:
        serializedVersion: 2
        x: 175
        y: 239
        width: 34
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87ef40317fdf26fb0800000000000000
      internalID: -4655879827129827720
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_26
      rect:
        serializedVersion: 2
        x: 223
        y: 159
        width: 80
        height: 106
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 86957abc378ff2d50800000000000000
      internalID: 6714858745654368616
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_27
      rect:
        serializedVersion: 2
        x: 272
        y: 255
        width: 32
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d486d0acaa7b242e0800000000000000
      internalID: -2142948528512735155
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_28
      rect:
        serializedVersion: 2
        x: 316
        y: 255
        width: 21
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 579ead3449afb3e50800000000000000
      internalID: 6790296377891678581
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_29
      rect:
        serializedVersion: 2
        x: 351
        y: 264
        width: 34
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 142ea7348e56523a0800000000000000
      internalID: -6690829623150058943
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_30
      rect:
        serializedVersion: 2
        x: 384
        y: 242
        width: 96
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dea1de9ef1f8c8360800000000000000
      internalID: 7173265673725745901
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_31
      rect:
        serializedVersion: 2
        x: 15
        y: 223
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c092cfb2c530cc6c0800000000000000
      internalID: -4121915864541026036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_32
      rect:
        serializedVersion: 2
        x: 127
        y: 207
        width: 130
        height: 27
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f1ee21cd2dd775a0800000000000000
      internalID: -6523502346638532111
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_33
      rect:
        serializedVersion: 2
        x: 303
        y: 223
        width: 66
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0af0580fcf50c8e40800000000000000
      internalID: 5659905415623020448
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_34
      rect:
        serializedVersion: 2
        x: 383
        y: 200
        width: 97
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 03f327b4fcbe67f80800000000000000
      internalID: -8109034803477463248
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_35
      rect:
        serializedVersion: 2
        x: 12
        y: 111
        width: 165
        height: 107
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7655c4cb87f438a0800000000000000
      internalID: -6326159396874922370
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_36
      rect:
        serializedVersion: 2
        x: 127
        y: 175
        width: 130
        height: 26
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 23b0c6a5ea66dda90800000000000000
      internalID: -7287555722988221646
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_37
      rect:
        serializedVersion: 2
        x: 191
        y: 111
        width: 181
        height: 107
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc8269d6a821e4330800000000000000
      internalID: 3696912729871100108
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_38
      rect:
        serializedVersion: 2
        x: 383
        y: 146
        width: 97
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c581366fc4729b250800000000000000
      internalID: 5960838793306249308
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_39
      rect:
        serializedVersion: 2
        x: 383
        y: 104
        width: 97
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24f55536c17c02d10800000000000000
      internalID: 2098896351094202178
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_40
      rect:
        serializedVersion: 2
        x: 15
        y: 47
        width: 34
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb9438892922091a0800000000000000
      internalID: -6804901023937508932
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_ground_41
      rect:
        serializedVersion: 2
        x: 79
        y: 37
        width: 258
        height: 34
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 55d19b29cc1dd4640800000000000000
      internalID: 5065935832426814805
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      env_ground_0: 2782836665789986762
      env_ground_1: 6637455614210474540
      env_ground_10: 2827619403745895511
      env_ground_11: -8031030576105267224
      env_ground_12: 7789899911944494111
      env_ground_13: -9110329931068465828
      env_ground_14: 3787543183262747789
      env_ground_15: -4273454622082669958
      env_ground_16: 8488042998240854368
      env_ground_17: 2768182140312440562
      env_ground_18: 5253105455750098979
      env_ground_19: -3851761324628544306
      env_ground_2: -5058560162748447859
      env_ground_20: 3799021305408291019
      env_ground_21: 5243446140276464743
      env_ground_22: 8467375619425056562
      env_ground_23: 778527646811231643
      env_ground_24: 8537531731111758989
      env_ground_25: -4655879827129827720
      env_ground_26: 6714858745654368616
      env_ground_27: -2142948528512735155
      env_ground_28: 6790296377891678581
      env_ground_29: -6690829623150058943
      env_ground_3: 2929950904672563563
      env_ground_30: 7173265673725745901
      env_ground_31: -4121915864541026036
      env_ground_32: -6523502346638532111
      env_ground_33: 5659905415623020448
      env_ground_34: -8109034803477463248
      env_ground_35: -6326159396874922370
      env_ground_36: -7287555722988221646
      env_ground_37: 3696912729871100108
      env_ground_38: 5960838793306249308
      env_ground_39: 2098896351094202178
      env_ground_4: 7130633397967381619
      env_ground_40: -6804901023937508932
      env_ground_41: 5065935832426814805
      env_ground_5: -4311885149137203828
      env_ground_6: -5371819772058992598
      env_ground_7: -8428063975171573973
      env_ground_8: 7770080440571327581
      env_ground_9: -2384425090723788689
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
