fileFormatVersion: 2
guid: 1ad12134a2a6e684a8874f3965364b6b
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4904060931984408531
    second: Fire_WarriorFireSword-Sheet_0
  - first:
      213: 1097052241410629379
    second: Fire_WarriorFireSword-Sheet_1
  - first:
      213: -1042619608624661564
    second: Fire_WarriorFireSword-Sheet_2
  - first:
      213: 5181327836484697304
    second: Fire_WarriorFireSword-Sheet_3
  - first:
      213: 9072091307349490749
    second: Fire_WarriorFireSword-Sheet_4
  - first:
      213: -2306392010017305716
    second: Fire_WarriorFireSword-Sheet_5
  - first:
      213: -1894708832912923415
    second: Fire_WarriorFireSword-Sheet_6
  - first:
      213: 7366699977686473458
    second: Fire_WarriorFireSword-Sheet_7
  - first:
      213: -7764981491358197694
    second: Fire_WarriorFireSword-Sheet_8
  - first:
      213: 810512240211234347
    second: Fire_WarriorFireSword-Sheet_9
  - first:
      213: -3423818697110367269
    second: Fire_WarriorFireSword-Sheet_10
  - first:
      213: 1499285857246109761
    second: Fire_WarriorFireSword-Sheet_11
  - first:
      213: 4073459748314283189
    second: Fire_WarriorFireSword-Sheet_12
  - first:
      213: -5269489878570228012
    second: Fire_WarriorFireSword-Sheet_13
  - first:
      213: 5645773020238216161
    second: Fire_WarriorFireSword-Sheet_14
  - first:
      213: -3418867535436416985
    second: Fire_WarriorFireSword-Sheet_15
  - first:
      213: 8404332561688078241
    second: Fire_WarriorFireSword-Sheet_16
  - first:
      213: 2498922528779838491
    second: Fire_WarriorFireSword-Sheet_17
  - first:
      213: 1444096994125233174
    second: Fire_WarriorFireSword-Sheet_18
  - first:
      213: -5220089080725606393
    second: Fire_WarriorFireSword-Sheet_19
  - first:
      213: -7031807366073298382
    second: Fire_WarriorFireSword-Sheet_20
  - first:
      213: 5410005170011970823
    second: Fire_WarriorFireSword-Sheet_21
  - first:
      213: -1871467571364522050
    second: Fire_WarriorFireSword-Sheet_22
  - first:
      213: 7734862016100379598
    second: Fire_WarriorFireSword-Sheet_23
  - first:
      213: 334592909229406553
    second: Fire_WarriorFireSword-Sheet_24
  - first:
      213: -1818894400158008479
    second: Fire_WarriorFireSword-Sheet_25
  - first:
      213: 4501609856398717390
    second: Fire_WarriorFireSword-Sheet_26
  - first:
      213: -7432831540960049113
    second: Fire_WarriorFireSword-Sheet_27
  - first:
      213: -4980048669866161948
    second: Fire_WarriorFireSword-Sheet_28
  - first:
      213: 8173339526930411951
    second: Fire_WarriorFireSword-Sheet_29
  - first:
      213: 3350760646591228236
    second: Fire_WarriorFireSword-Sheet_30
  - first:
      213: 16307728598430072
    second: Fire_WarriorFireSword-Sheet_31
  - first:
      213: -840994939869901896
    second: Fire_WarriorFireSword-Sheet_32
  - first:
      213: -3771119007739376577
    second: Fire_WarriorFireSword-Sheet_33
  - first:
      213: 8756418113305168632
    second: Fire_WarriorFireSword-Sheet_34
  - first:
      213: -3464657296522377036
    second: Fire_WarriorFireSword-Sheet_35
  - first:
      213: 4232190747824605073
    second: Fire_WarriorFireSword-Sheet_36
  - first:
      213: -6764551840999554552
    second: Fire_WarriorFireSword-Sheet_37
  - first:
      213: -1784975513250127364
    second: Fire_WarriorFireSword-Sheet_38
  - first:
      213: -3675091216472680683
    second: Fire_WarriorFireSword-Sheet_39
  - first:
      213: -2489430374634202124
    second: Fire_WarriorFireSword-Sheet_40
  - first:
      213: -2762440677337370644
    second: Fire_WarriorFireSword-Sheet_41
  - first:
      213: -3325531515707334911
    second: Fire_WarriorFireSword-Sheet_42
  - first:
      213: -3674167966180859333
    second: Fire_WarriorFireSword-Sheet_43
  - first:
      213: -6204609799786472866
    second: Fire_WarriorFireSword-Sheet_44
  - first:
      213: 4209470473251784477
    second: Fire_WarriorFireSword-Sheet_45
  - first:
      213: 3688992650357657688
    second: Fire_WarriorFireSword-Sheet_46
  - first:
      213: -7815741919274686834
    second: Fire_WarriorFireSword-Sheet_47
  - first:
      213: -3830370071889123185
    second: Fire_WarriorFireSword-Sheet_48
  - first:
      213: 3501082362127154679
    second: Fire_WarriorFireSword-Sheet_49
  - first:
      213: -1070368551428574527
    second: Fire_WarriorFireSword-Sheet_50
  - first:
      213: -7683281129174531635
    second: Fire_WarriorFireSword-Sheet_51
  - first:
      213: -7497913099252786439
    second: Fire_WarriorFireSword-Sheet_52
  - first:
      213: -8049278785306582317
    second: Fire_WarriorFireSword-Sheet_53
  - first:
      213: -6887270137552344815
    second: Fire_WarriorFireSword-Sheet_54
  - first:
      213: 3730438522977345134
    second: Fire_WarriorFireSword-Sheet_55
  - first:
      213: -5369804697258680598
    second: Fire_WarriorFireSword-Sheet_56
  - first:
      213: 8502501480965070884
    second: Fire_WarriorFireSword-Sheet_57
  - first:
      213: -4863277473842490365
    second: Fire_WarriorFireSword-Sheet_58
  - first:
      213: -9049664380470677528
    second: Fire_WarriorFireSword-Sheet_59
  - first:
      213: -622436962368991780
    second: Fire_WarriorFireSword-Sheet_60
  - first:
      213: -3408476400592916472
    second: Fire_WarriorFireSword-Sheet_61
  - first:
      213: 2935965203409687798
    second: Fire_WarriorFireSword-Sheet_62
  - first:
      213: -4964342189221567242
    second: Fire_WarriorFireSword-Sheet_63
  - first:
      213: -7633963068577370476
    second: Fire_WarriorFireSword-Sheet_64
  - first:
      213: -4765989794712932744
    second: Fire_WarriorFireSword-Sheet_65
  - first:
      213: 8488006700303112307
    second: Fire_WarriorFireSword-Sheet_66
  - first:
      213: -1059295366987352956
    second: Fire_WarriorFireSword-Sheet_67
  - first:
      213: 1623578526369706666
    second: Fire_WarriorFireSword-Sheet_68
  - first:
      213: 6519921053705386639
    second: Fire_WarriorFireSword-Sheet_69
  - first:
      213: -6729663148708633812
    second: Fire_WarriorFireSword-Sheet_70
  - first:
      213: -7018855618056735107
    second: Fire_WarriorFireSword-Sheet_71
  - first:
      213: -7202091154204182158
    second: Fire_WarriorFireSword-Sheet_72
  - first:
      213: -3191746942449540678
    second: Fire_WarriorFireSword-Sheet_73
  - first:
      213: 5950039674075746694
    second: Fire_WarriorFireSword-Sheet_74
  - first:
      213: -8444722477689299561
    second: Fire_WarriorFireSword-Sheet_75
  - first:
      213: -8914290602440123675
    second: Fire_WarriorFireSword-Sheet_76
  - first:
      213: -7423302892295114259
    second: Fire_WarriorFireSword-Sheet_77
  - first:
      213: 3775811148037459427
    second: Fire_WarriorFireSword-Sheet_78
  - first:
      213: 7221403751928692256
    second: Fire_WarriorFireSword-Sheet_79
  - first:
      213: 6819586172183786837
    second: Fire_WarriorFireSword-Sheet_80
  - first:
      213: 2020535866090774657
    second: Fire_WarriorFireSword-Sheet_81
  - first:
      213: -3650762844592107245
    second: Fire_WarriorFireSword-Sheet_82
  - first:
      213: 1949736526466859164
    second: Fire_WarriorFireSword-Sheet_83
  - first:
      213: -2280020493951924489
    second: Fire_WarriorFireSword-Sheet_84
  - first:
      213: 4282731640604092970
    second: Fire_WarriorFireSword-Sheet_85
  - first:
      213: 243487079525577496
    second: Fire_WarriorFireSword-Sheet_86
  - first:
      213: -1675214217001596102
    second: Fire_WarriorFireSword-Sheet_87
  - first:
      213: -8047594970978909828
    second: Fire_WarriorFireSword-Sheet_88
  - first:
      213: -5939653874309101471
    second: Fire_WarriorFireSword-Sheet_89
  - first:
      213: -5986811485329310736
    second: Fire_WarriorFireSword-Sheet_90
  - first:
      213: -63207839836775335
    second: Fire_WarriorFireSword-Sheet_91
  - first:
      213: 5090865770312087798
    second: Fire_WarriorFireSword-Sheet_92
  - first:
      213: -4619119848115420422
    second: Fire_WarriorFireSword-Sheet_93
  - first:
      213: -7087886686813852041
    second: Fire_WarriorFireSword-Sheet_94
  - first:
      213: -6990132740499694735
    second: Fire_WarriorFireSword-Sheet_95
  - first:
      213: -6867214773945115138
    second: Fire_WarriorFireSword-Sheet_96
  - first:
      213: 8421337564470047216
    second: Fire_WarriorFireSword-Sheet_97
  - first:
      213: 6230507712342741987
    second: Fire_WarriorFireSword-Sheet_98
  - first:
      213: -1899007000852105579
    second: Fire_WarriorFireSword-Sheet_99
  - first:
      213: -6760068534779731385
    second: Fire_WarriorFireSword-Sheet_100
  - first:
      213: -7682172948956111454
    second: Fire_WarriorFireSword-Sheet_101
  - first:
      213: -6310427782916763419
    second: Fire_WarriorFireSword-Sheet_102
  - first:
      213: 2396546481631340439
    second: Fire_WarriorFireSword-Sheet_103
  - first:
      213: 6672414060514436456
    second: Fire_WarriorFireSword-Sheet_104
  - first:
      213: -434646505345235327
    second: Fire_WarriorFireSword-Sheet_105
  - first:
      213: -8195862610593739896
    second: Fire_WarriorFireSword-Sheet_106
  - first:
      213: -3490179084566062886
    second: Fire_WarriorFireSword-Sheet_107
  - first:
      213: 7713251741885278747
    second: Fire_WarriorFireSword-Sheet_108
  - first:
      213: -7513526876293808937
    second: Fire_WarriorFireSword-Sheet_109
  - first:
      213: 806843710518643812
    second: Fire_WarriorFireSword-Sheet_110
  - first:
      213: -7396659506173683684
    second: Fire_WarriorFireSword-Sheet_111
  - first:
      213: -6956207221950393168
    second: Fire_WarriorFireSword-Sheet_112
  - first:
      213: -7909260988485863567
    second: Fire_WarriorFireSword-Sheet_113
  - first:
      213: 8891283137428432786
    second: Fire_WarriorFireSword-Sheet_114
  - first:
      213: 4201049432926994542
    second: Fire_WarriorFireSword-Sheet_115
  - first:
      213: -796418219213435149
    second: Fire_WarriorFireSword-Sheet_116
  - first:
      213: -4210928881695765634
    second: Fire_WarriorFireSword-Sheet_117
  - first:
      213: 1833599849901531101
    second: Fire_WarriorFireSword-Sheet_118
  - first:
      213: -250219711286975934
    second: Fire_WarriorFireSword-Sheet_119
  - first:
      213: 1562025673480118446
    second: Fire_WarriorFireSword-Sheet_120
  - first:
      213: -1312453236855542032
    second: Fire_WarriorFireSword-Sheet_121
  - first:
      213: -334369991925986408
    second: Fire_WarriorFireSword-Sheet_122
  - first:
      213: 482174252726052216
    second: Fire_WarriorFireSword-Sheet_123
  - first:
      213: -4638082007826782232
    second: Fire_WarriorFireSword-Sheet_124
  - first:
      213: 7910129002761369642
    second: Fire_WarriorFireSword-Sheet_125
  - first:
      213: -6282448016134533810
    second: Fire_WarriorFireSword-Sheet_126
  - first:
      213: -9086590093139962550
    second: Fire_WarriorFireSword-Sheet_127
  - first:
      213: -6327673350574820090
    second: Fire_WarriorFireSword-Sheet_128
  - first:
      213: -6034297959458783320
    second: Fire_WarriorFireSword-Sheet_129
  - first:
      213: -8542993173292700771
    second: Fire_WarriorFireSword-Sheet_130
  - first:
      213: 7237842143926533053
    second: Fire_WarriorFireSword-Sheet_131
  - first:
      213: -4431532954613786054
    second: Fire_WarriorFireSword-Sheet_132
  - first:
      213: -7552586321364348415
    second: Fire_WarriorFireSword-Sheet_133
  - first:
      213: -8570516755365591834
    second: Fire_WarriorFireSword-Sheet_134
  - first:
      213: -1085450987987908638
    second: Fire_WarriorFireSword-Sheet_135
  - first:
      213: -6326149930729528518
    second: Fire_WarriorFireSword-Sheet_136
  - first:
      213: -1919049914377541930
    second: Fire_WarriorFireSword-Sheet_137
  - first:
      213: -2217728056312426633
    second: Fire_WarriorFireSword-Sheet_138
  - first:
      213: 481190306849913867
    second: Fire_WarriorFireSword-Sheet_139
  - first:
      213: 6700627901348864705
    second: Fire_WarriorFireSword-Sheet_140
  - first:
      213: -3120347716891507223
    second: Fire_WarriorFireSword-Sheet_141
  - first:
      213: -2240330742683856504
    second: Fire_WarriorFireSword-Sheet_142
  - first:
      213: -15421244341633582
    second: Fire_WarriorFireSword-Sheet_143
  - first:
      213: 3710434576336177671
    second: Fire_WarriorFireSword-Sheet_144
  - first:
      213: -7049903259789278345
    second: Fire_WarriorFireSword-Sheet_145
  - first:
      213: -2838188013735187232
    second: Fire_WarriorFireSword-Sheet_146
  - first:
      213: 8017982809996213737
    second: Fire_WarriorFireSword-Sheet_147
  - first:
      213: 5600834148731941969
    second: Fire_WarriorFireSword-Sheet_148
  - first:
      213: -8365655806770982912
    second: Fire_WarriorFireSword-Sheet_149
  - first:
      213: -5124544916768604406
    second: Fire_WarriorFireSword-Sheet_150
  - first:
      213: -8277973031262658600
    second: Fire_WarriorFireSword-Sheet_151
  - first:
      213: 2450666221532319487
    second: Fire_WarriorFireSword-Sheet_152
  - first:
      213: 6707651686037732082
    second: Fire_WarriorFireSword-Sheet_153
  - first:
      213: 3302800255662225271
    second: Fire_WarriorFireSword-Sheet_154
  - first:
      213: 5305250507638347907
    second: Fire_WarriorFireSword-Sheet_155
  - first:
      213: 3533098906547083107
    second: Fire_WarriorFireSword-Sheet_156
  - first:
      213: -4287640764644614076
    second: Fire_WarriorFireSword-Sheet_157
  - first:
      213: 3605139221255763329
    second: Fire_WarriorFireSword-Sheet_158
  - first:
      213: 1914420635668657017
    second: Fire_WarriorFireSword-Sheet_159
  - first:
      213: -3531584464517380644
    second: Fire_WarriorFireSword-Sheet_160
  - first:
      213: 8467350303287042402
    second: Fire_WarriorFireSword-Sheet_161
  - first:
      213: 1696089850774444396
    second: Fire_WarriorFireSword-Sheet_162
  - first:
      213: 3832382276811933971
    second: Fire_WarriorFireSword-Sheet_163
  - first:
      213: -1056675874322564195
    second: Fire_WarriorFireSword-Sheet_164
  - first:
      213: 571511110284384812
    second: Fire_WarriorFireSword-Sheet_165
  - first:
      213: 6652864337927174953
    second: Fire_WarriorFireSword-Sheet_166
  - first:
      213: -1063380094898194773
    second: Fire_WarriorFireSword-Sheet_167
  - first:
      213: -8929090309205710962
    second: Fire_WarriorFireSword-Sheet_168
  - first:
      213: -7180285185111351380
    second: Fire_WarriorFireSword-Sheet_169
  - first:
      213: 2767013031267930678
    second: Fire_WarriorFireSword-Sheet_170
  - first:
      213: 2062375705440954767
    second: Fire_WarriorFireSword-Sheet_171
  - first:
      213: -5669052834954133162
    second: Fire_WarriorFireSword-Sheet_172
  - first:
      213: 4914235150016274009
    second: Fire_WarriorFireSword-Sheet_173
  - first:
      213: 8404390561027662818
    second: Fire_WarriorFireSword-Sheet_174
  - first:
      213: 1453363613652910233
    second: Fire_WarriorFireSword-Sheet_175
  - first:
      213: 4387847362150122973
    second: Fire_WarriorFireSword-Sheet_176
  - first:
      213: 6619206962356791163
    second: Fire_WarriorFireSword-Sheet_177
  - first:
      213: -448745084731457457
    second: Fire_WarriorFireSword-Sheet_178
  - first:
      213: 6326814564542513702
    second: Fire_WarriorFireSword-Sheet_179
  - first:
      213: 5535630000901821805
    second: Fire_WarriorFireSword-Sheet_180
  - first:
      213: -5950547938803945913
    second: Fire_WarriorFireSword-Sheet_181
  - first:
      213: 7426957573161127525
    second: Fire_WarriorFireSword-Sheet_182
  - first:
      213: -4242399084580865172
    second: Fire_WarriorFireSword-Sheet_183
  - first:
      213: 3877803434187271345
    second: Fire_WarriorFireSword-Sheet_184
  - first:
      213: -4476828498768373102
    second: Fire_WarriorFireSword-Sheet_185
  - first:
      213: -1549288399330245871
    second: Fire_WarriorFireSword-Sheet_186
  - first:
      213: 6224029749115100555
    second: Fire_WarriorFireSword-Sheet_187
  - first:
      213: 7631694480760461026
    second: Fire_WarriorFireSword-Sheet_188
  - first:
      213: -4239924424983915669
    second: Fire_WarriorFireSword-Sheet_189
  - first:
      213: -8532394458901187822
    second: Fire_WarriorFireSword-Sheet_190
  - first:
      213: -5251822398070460609
    second: Fire_WarriorFireSword-Sheet_191
  - first:
      213: 6819725018074726575
    second: Fire_WarriorFireSword-Sheet_192
  - first:
      213: 2967513039118778851
    second: Fire_WarriorFireSword-Sheet_193
  - first:
      213: -36055183865352305
    second: Fire_WarriorFireSword-Sheet_194
  - first:
      213: 3887639064677120839
    second: Fire_WarriorFireSword-Sheet_195
  - first:
      213: 1422161029800051340
    second: Fire_WarriorFireSword-Sheet_196
  - first:
      213: -8793585661595168688
    second: Fire_WarriorFireSword-Sheet_197
  - first:
      213: -6907045548920973917
    second: Fire_WarriorFireSword-Sheet_198
  - first:
      213: -5686015432731584440
    second: Fire_WarriorFireSword-Sheet_199
  - first:
      213: -8019118080690552349
    second: Fire_WarriorFireSword-Sheet_200
  - first:
      213: -3413993972802973554
    second: Fire_WarriorFireSword-Sheet_201
  - first:
      213: -8302997360241483957
    second: Fire_WarriorFireSword-Sheet_202
  - first:
      213: 5500793478268128905
    second: Fire_WarriorFireSword-Sheet_203
  - first:
      213: -3172261104233738891
    second: Fire_WarriorFireSword-Sheet_204
  - first:
      213: 3345040293889130578
    second: Fire_WarriorFireSword-Sheet_205
  - first:
      213: 7964830710912211300
    second: Fire_WarriorFireSword-Sheet_206
  - first:
      213: -4297443232201785437
    second: Fire_WarriorFireSword-Sheet_207
  - first:
      213: -709613186616307332
    second: Fire_WarriorFireSword-Sheet_208
  - first:
      213: -4334072023361232203
    second: Fire_WarriorFireSword-Sheet_209
  - first:
      213: -3989400793110436131
    second: Fire_WarriorFireSword-Sheet_210
  - first:
      213: 5565134686741911229
    second: Fire_WarriorFireSword-Sheet_211
  - first:
      213: -8264675866954580573
    second: Fire_WarriorFireSword-Sheet_212
  - first:
      213: 8331943208488444369
    second: Fire_WarriorFireSword-Sheet_213
  - first:
      213: -3226696838851309082
    second: Fire_WarriorFireSword-Sheet_214
  - first:
      213: -8661768078759473041
    second: Fire_WarriorFireSword-Sheet_215
  - first:
      213: -7431490092444900633
    second: Fire_WarriorFireSword-Sheet_216
  - first:
      213: 2638926577474342676
    second: Fire_WarriorFireSword-Sheet_217
  - first:
      213: -8698778638974473644
    second: Fire_WarriorFireSword-Sheet_218
  - first:
      213: -7850371535398243169
    second: Fire_WarriorFireSword-Sheet_219
  - first:
      213: 4572424453705521078
    second: Fire_WarriorFireSword-Sheet_220
  - first:
      213: 4918932890116361209
    second: Fire_WarriorFireSword-Sheet_221
  - first:
      213: 2296363131615173411
    second: Fire_WarriorFireSword-Sheet_222
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 64
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_0
      rect:
        serializedVersion: 2
        x: 40
        y: 1935
        width: 30
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d20c230b09641fbb0800000000000000
      internalID: -4904060931984408531
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_1
      rect:
        serializedVersion: 2
        x: 185
        y: 1935
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 30f935d7333893f00800000000000000
      internalID: 1097052241410629379
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_2
      rect:
        serializedVersion: 2
        x: 329
        y: 1935
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4cbf1107dfed781f0800000000000000
      internalID: -1042619608624661564
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_3
      rect:
        serializedVersion: 2
        x: 472
        y: 1935
        width: 30
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8dc73862736c7e740800000000000000
      internalID: 5181327836484697304
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_4
      rect:
        serializedVersion: 2
        x: 616
        y: 1935
        width: 30
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d3cb9706afa86ed70800000000000000
      internalID: 9072091307349490749
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_5
      rect:
        serializedVersion: 2
        x: 760
        y: 1935
        width: 30
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c8b8e1acfac0effd0800000000000000
      internalID: -2306392010017305716
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_6
      rect:
        serializedVersion: 2
        x: 905
        y: 1935
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e42e65ea54a4b5e0800000000000000
      internalID: -1894708832912923415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_7
      rect:
        serializedVersion: 2
        x: 1049
        y: 1935
        width: 29
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2fa51e86996cb3660800000000000000
      internalID: 7366699977686473458
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_8
      rect:
        serializedVersion: 2
        x: 35
        y: 1855
        width: 41
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2489c18697e3d3490800000000000000
      internalID: -7764981491358197694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_9
      rect:
        serializedVersion: 2
        x: 178
        y: 1855
        width: 41
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2e85e396948f3b00800000000000000
      internalID: 810512240211234347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_10
      rect:
        serializedVersion: 2
        x: 321
        y: 1863
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd3556ff8072c70d0800000000000000
      internalID: -3423818697110367269
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_11
      rect:
        serializedVersion: 2
        x: 321
        y: 1855
        width: 41
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 140ebe92b888ec410800000000000000
      internalID: 1499285857246109761
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_12
      rect:
        serializedVersion: 2
        x: 465
        y: 1855
        width: 40
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5bcff191f06d78830800000000000000
      internalID: 4073459748314283189
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_13
      rect:
        serializedVersion: 2
        x: 609
        y: 1855
        width: 39
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4d62e48dfd20fd6b0800000000000000
      internalID: -5269489878570228012
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_14
      rect:
        serializedVersion: 2
        x: 754
        y: 1855
        width: 39
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1eb78c936a0d95e40800000000000000
      internalID: 5645773020238216161
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_15
      rect:
        serializedVersion: 2
        x: 897
        y: 1863
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7287238271ebd80d0800000000000000
      internalID: -3418867535436416985
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_16
      rect:
        serializedVersion: 2
        x: 896
        y: 1855
        width: 42
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a7aaa1dfef22a470800000000000000
      internalID: 8404332561688078241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_17
      rect:
        serializedVersion: 2
        x: 1041
        y: 1855
        width: 42
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b14a421dcc4fda220800000000000000
      internalID: 2498922528779838491
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_18
      rect:
        serializedVersion: 2
        x: 45
        y: 1775
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 61c3b04a0967a0410800000000000000
      internalID: 1444096994125233174
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_19
      rect:
        serializedVersion: 2
        x: 190
        y: 1775
        width: 34
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 700747763a48e87b0800000000000000
      internalID: -5220089080725606393
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_20
      rect:
        serializedVersion: 2
        x: 336
        y: 1775
        width: 38
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 232d938e4700a6e90800000000000000
      internalID: -7031807366073298382
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_21
      rect:
        serializedVersion: 2
        x: 481
        y: 1775
        width: 38
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70d1797e903341b40800000000000000
      internalID: 5410005170011970823
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_22
      rect:
        serializedVersion: 2
        x: 625
        y: 1775
        width: 40
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb79728c8263706e0800000000000000
      internalID: -1871467571364522050
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_23
      rect:
        serializedVersion: 2
        x: 769
        y: 1775
        width: 38
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ecb2358c500c75b60800000000000000
      internalID: 7734862016100379598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_24
      rect:
        serializedVersion: 2
        x: 912
        y: 1775
        width: 38
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 959bbfa6a76b4a400800000000000000
      internalID: 334592909229406553
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_25
      rect:
        serializedVersion: 2
        x: 1054
        y: 1775
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16b72a21e2df1c6e0800000000000000
      internalID: -1818894400158008479
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_26
      rect:
        serializedVersion: 2
        x: 34
        y: 1695
        width: 40
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ec1fa08505ee87e30800000000000000
      internalID: 4501609856398717390
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_27
      rect:
        serializedVersion: 2
        x: 179
        y: 1695
        width: 39
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 728c1c5281749d890800000000000000
      internalID: -7432831540960049113
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_28
      rect:
        serializedVersion: 2
        x: 327
        y: 1695
        width: 35
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4e88874ce1053eab0800000000000000
      internalID: -4980048669866161948
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_29
      rect:
        serializedVersion: 2
        x: 466
        y: 1695
        width: 40
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa1b85bdef88d6170800000000000000
      internalID: 8173339526930411951
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_30
      rect:
        serializedVersion: 2
        x: 606
        y: 1695
        width: 54
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c49ad631e0b408e20800000000000000
      internalID: 3350760646591228236
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_31
      rect:
        serializedVersion: 2
        x: 754
        y: 1695
        width: 51
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 871f332facfe93000800000000000000
      internalID: 16307728598430072
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_32
      rect:
        serializedVersion: 2
        x: 898
        y: 1695
        width: 51
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bb4727de8f2454f0800000000000000
      internalID: -840994939869901896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_33
      rect:
        serializedVersion: 2
        x: 33
        y: 1615
        width: 38
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3c88f12e3b4aabc0800000000000000
      internalID: -3771119007739376577
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_34
      rect:
        serializedVersion: 2
        x: 171
        y: 1615
        width: 48
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8fac95c61eb058970800000000000000
      internalID: 8756418113305168632
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_35
      rect:
        serializedVersion: 2
        x: 311
        y: 1615
        width: 59
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4b8815c9b801befc0800000000000000
      internalID: -3464657296522377036
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_36
      rect:
        serializedVersion: 2
        x: 456
        y: 1615
        width: 58
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 197c7c1ef03cbba30800000000000000
      internalID: 4232190747824605073
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_37
      rect:
        serializedVersion: 2
        x: 603
        y: 1615
        width: 55
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 802c184d0fb7f12a0800000000000000
      internalID: -6764551840999554552
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_38
      rect:
        serializedVersion: 2
        x: 747
        y: 1615
        width: 48
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cf1a4f75a3e7a37e0800000000000000
      internalID: -1784975513250127364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_39
      rect:
        serializedVersion: 2
        x: 897
        y: 1615
        width: 38
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 517dcf492047ffcc0800000000000000
      internalID: -3675091216472680683
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_40
      rect:
        serializedVersion: 2
        x: 41
        y: 1535
        width: 38
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4fb5cda8344c37dd0800000000000000
      internalID: -2489430374634202124
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_41
      rect:
        serializedVersion: 2
        x: 181
        y: 1535
        width: 48
        height: 30
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cef673a07d6d9a9d0800000000000000
      internalID: -2762440677337370644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_42
      rect:
        serializedVersion: 2
        x: 331
        y: 1536
        width: 41
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 10fd5e4b4b659d1d0800000000000000
      internalID: -3325531515707334911
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_43
      rect:
        serializedVersion: 2
        x: 467
        y: 1535
        width: 39
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b32abaa83bbb20dc0800000000000000
      internalID: -3674167966180859333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_44
      rect:
        serializedVersion: 2
        x: 615
        y: 1535
        width: 37
        height: 31
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e5ad1d1b24cc4e9a0800000000000000
      internalID: -6204609799786472866
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_45
      rect:
        serializedVersion: 2
        x: 763
        y: 1535
        width: 26
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d170baab71b0b6a30800000000000000
      internalID: 4209470473251784477
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_46
      rect:
        serializedVersion: 2
        x: 907
        y: 1535
        width: 27
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8505897354fe13330800000000000000
      internalID: 3688992650357657688
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_47
      rect:
        serializedVersion: 2
        x: 33
        y: 1455
        width: 40
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e86a08d4228e88390800000000000000
      internalID: -7815741919274686834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_48
      rect:
        serializedVersion: 2
        x: 178
        y: 1455
        width: 39
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f88e2cab6bac7dac0800000000000000
      internalID: -3830370071889123185
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_49
      rect:
        serializedVersion: 2
        x: 325
        y: 1455
        width: 36
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7fd79fbdbd7569030800000000000000
      internalID: 3501082362127154679
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_50
      rect:
        serializedVersion: 2
        x: 35
        y: 1377
        width: 44
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c2b95939794521f0800000000000000
      internalID: -1070368551428574527
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_51
      rect:
        serializedVersion: 2
        x: 185
        y: 1379
        width: 43
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc1071255808f5590800000000000000
      internalID: -7683281129174531635
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_52
      rect:
        serializedVersion: 2
        x: 318
        y: 1377
        width: 45
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fa1a4a03cf02f790800000000000000
      internalID: -7497913099252786439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_53
      rect:
        serializedVersion: 2
        x: 32
        y: 1295
        width: 36
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3d2a6b777973b4090800000000000000
      internalID: -8049278785306582317
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_54
      rect:
        serializedVersion: 2
        x: 175
        y: 1295
        width: 37
        height: 56
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 119690bdc408b60a0800000000000000
      internalID: -6887270137552344815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_55
      rect:
        serializedVersion: 2
        x: 326
        y: 1295
        width: 30
        height: 60
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e6e5f7a621e25c330800000000000000
      internalID: 3730438522977345134
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_56
      rect:
        serializedVersion: 2
        x: 41
        y: 1216
        width: 32
        height: 45
      alignment: 9
      pivot: {x: 0.5, y: 0.6}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aee1caa041f9a75b0800000000000000
      internalID: -5369804697258680598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_57
      rect:
        serializedVersion: 2
        x: 181
        y: 1219
        width: 55
        height: 44
      alignment: 9
      pivot: {x: 0.5, y: 0.6}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 420e72b7c04fef570800000000000000
      internalID: 8502501480965070884
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_58
      rect:
        serializedVersion: 2
        x: 330
        y: 1219
        width: 44
        height: 43
      alignment: 9
      pivot: {x: 0.5, y: 0.6}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 304957f77ea228cb0800000000000000
      internalID: -4863277473842490365
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_59
      rect:
        serializedVersion: 2
        x: 480
        y: 1219
        width: 24
        height: 43
      alignment: 9
      pivot: {x: 0.5, y: 0.6}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ef2a817132296280800000000000000
      internalID: -9049664380470677528
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_60
      rect:
        serializedVersion: 2
        x: 617
        y: 1219
        width: 43
        height: 42
      alignment: 9
      pivot: {x: 0.5, y: 0.6}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cdd4cea61e8ac57f0800000000000000
      internalID: -622436962368991780
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_61
      rect:
        serializedVersion: 2
        x: 17
        y: 1135
        width: 52
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8007b55c5c8a2b0d0800000000000000
      internalID: -3408476400592916472
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_62
      rect:
        serializedVersion: 2
        x: 185
        y: 1135
        width: 55
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f4542e0cc4aeb820800000000000000
      internalID: 2935965203409687798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_63
      rect:
        serializedVersion: 2
        x: 334
        y: 1135
        width: 44
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f4a6f3c41d1b1bb0800000000000000
      internalID: -4964342189221567242
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_64
      rect:
        serializedVersion: 2
        x: 480
        y: 1135
        width: 28
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 49eaefc1907be0690800000000000000
      internalID: -7633963068577370476
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_65
      rect:
        serializedVersion: 2
        x: 29
        y: 1055
        width: 47
        height: 55
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 87eaff2788dcbddb0800000000000000
      internalID: -4765989794712932744
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_66
      rect:
        serializedVersion: 2
        x: 188
        y: 1055
        width: 60
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 378d3be5f157bc570800000000000000
      internalID: 8488006700303112307
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_67
      rect:
        serializedVersion: 2
        x: 336
        y: 1055
        width: 54
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4847082e970ac41f0800000000000000
      internalID: -1059295366987352956
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_68
      rect:
        serializedVersion: 2
        x: 480
        y: 1055
        width: 53
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aa2f731511c188610800000000000000
      internalID: 1623578526369706666
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_69
      rect:
        serializedVersion: 2
        x: 48
        y: 975
        width: 40
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f8a33b187a96b7a50800000000000000
      internalID: 6519921053705386639
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_70
      rect:
        serializedVersion: 2
        x: 187
        y: 975
        width: 70
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c236f23850f6b92a0800000000000000
      internalID: -6729663148708633812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_71
      rect:
        serializedVersion: 2
        x: 334
        y: 975
        width: 67
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7639fbb004089e90800000000000000
      internalID: -7018855618056735107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_72
      rect:
        serializedVersion: 2
        x: 480
        y: 975
        width: 65
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2793da9e0480d0c90800000000000000
      internalID: -7202091154204182158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_73
      rect:
        serializedVersion: 2
        x: 624
        y: 975
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ab12b520113a4b3d0800000000000000
      internalID: -3191746942449540678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_74
      rect:
        serializedVersion: 2
        x: 42
        y: 895
        width: 40
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 68114c41f89c29250800000000000000
      internalID: 5950039674075746694
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_75
      rect:
        serializedVersion: 2
        x: 183
        y: 895
        width: 36
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 79d929a06a15eca80800000000000000
      internalID: -8444722477689299561
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_76
      rect:
        serializedVersion: 2
        x: 328
        y: 895
        width: 35
        height: 55
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5ea14f05de31a4480800000000000000
      internalID: -8914290602440123675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_77
      rect:
        serializedVersion: 2
        x: 472
        y: 895
        width: 35
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de1be9c89512bf890800000000000000
      internalID: -7423302892295114259
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_78
      rect:
        serializedVersion: 2
        x: 617
        y: 895
        width: 34
        height: 54
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3ed8bb9eb30666430800000000000000
      internalID: 3775811148037459427
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_79
      rect:
        serializedVersion: 2
        x: 762
        y: 895
        width: 33
        height: 53
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 02ef165a374973460800000000000000
      internalID: 7221403751928692256
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_80
      rect:
        serializedVersion: 2
        x: 905
        y: 895
        width: 35
        height: 54
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5553e2ced7904ae50800000000000000
      internalID: 6819586172183786837
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_81
      rect:
        serializedVersion: 2
        x: 1050
        y: 895
        width: 40
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 180e64a3aa26a0c10800000000000000
      internalID: 2020535866090774657
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_82
      rect:
        serializedVersion: 2
        x: 40
        y: 815
        width: 35
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31194e01982e55dc0800000000000000
      internalID: -3650762844592107245
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_83
      rect:
        serializedVersion: 2
        x: 181
        y: 815
        width: 35
        height: 55
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c9049a96a0bde0b10800000000000000
      internalID: 1949736526466859164
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_84
      rect:
        serializedVersion: 2
        x: 330
        y: 815
        width: 36
        height: 54
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7f68269d07dbb50e0800000000000000
      internalID: -2280020493951924489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_85
      rect:
        serializedVersion: 2
        x: 474
        y: 815
        width: 39
        height: 58
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a2a688a7cb15f6b30800000000000000
      internalID: 4282731640604092970
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_86
      rect:
        serializedVersion: 2
        x: 618
        y: 815
        width: 32
        height: 54
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 813f482753a016300800000000000000
      internalID: 243487079525577496
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_87
      rect:
        serializedVersion: 2
        x: 762
        y: 815
        width: 40
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3b8fe89b8170c8e0800000000000000
      internalID: -1675214217001596102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_88
      rect:
        serializedVersion: 2
        x: 42
        y: 735
        width: 28
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c71059a0303315090800000000000000
      internalID: -8047594970978909828
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_89
      rect:
        serializedVersion: 2
        x: 185
        y: 735
        width: 28
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 168880d554c129da0800000000000000
      internalID: -5939653874309101471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_90
      rect:
        serializedVersion: 2
        x: 326
        y: 735
        width: 31
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0fbb7bf6ca29aeca0800000000000000
      internalID: -5986811485329310736
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_91
      rect:
        serializedVersion: 2
        x: 472
        y: 735
        width: 29
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95c5287fdc07f1ff0800000000000000
      internalID: -63207839836775335
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_92
      rect:
        serializedVersion: 2
        x: 616
        y: 735
        width: 29
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6f8b5ae027366a640800000000000000
      internalID: 5090865770312087798
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_93
      rect:
        serializedVersion: 2
        x: 760
        y: 735
        width: 29
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af2cf16a8f695efb0800000000000000
      internalID: -4619119848115420422
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_94
      rect:
        serializedVersion: 2
        x: 904
        y: 735
        width: 29
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77e6d309c94c2ad90800000000000000
      internalID: -7087886686813852041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_95
      rect:
        serializedVersion: 2
        x: 1051
        y: 735
        width: 27
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 17b10bdce4f0efe90800000000000000
      internalID: -6990132740499694735
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_96
      rect:
        serializedVersion: 2
        x: 42
        y: 655
        width: 30
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: efddfa21c80c2b0a0800000000000000
      internalID: -6867214773945115138
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_97
      rect:
        serializedVersion: 2
        x: 154
        y: 655
        width: 82
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0f1f3ef85e99ed470800000000000000
      internalID: 8421337564470047216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_98
      rect:
        serializedVersion: 2
        x: 298
        y: 655
        width: 59
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e35584f0c5377650800000000000000
      internalID: 6230507712342741987
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_99
      rect:
        serializedVersion: 2
        x: 442
        y: 655
        width: 59
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 596fa30b13f55a5e0800000000000000
      internalID: -1899007000852105579
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_100
      rect:
        serializedVersion: 2
        x: 600
        y: 655
        width: 45
        height: 33
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 742a827eb796f22a0800000000000000
      internalID: -6760068534779731385
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_101
      rect:
        serializedVersion: 2
        x: 47
        y: 575
        width: 24
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2ad1b52a760736590800000000000000
      internalID: -7682172948956111454
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_102
      rect:
        serializedVersion: 2
        x: 191
        y: 576
        width: 22
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5e09d729d5bdc68a0800000000000000
      internalID: -6310427782916763419
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_103
      rect:
        serializedVersion: 2
        x: 335
        y: 579
        width: 22
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 797295dd35e324120800000000000000
      internalID: 2396546481631340439
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_104
      rect:
        serializedVersion: 2
        x: 480
        y: 575
        width: 21
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 865e84f6c3d299c50800000000000000
      internalID: 6672414060514436456
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_105
      rect:
        serializedVersion: 2
        x: 622
        y: 575
        width: 24
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 18648066e43d7f9f0800000000000000
      internalID: -434646505345235327
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_106
      rect:
        serializedVersion: 2
        x: 768
        y: 576
        width: 22
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88b87a31162724e80800000000000000
      internalID: -8195862610593739896
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_107
      rect:
        serializedVersion: 2
        x: 912
        y: 579
        width: 22
        height: 43
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad811f54d94609fc0800000000000000
      internalID: -3490179084566062886
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_108
      rect:
        serializedVersion: 2
        x: 1056
        y: 575
        width: 21
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b1a2c3ab799fa0b60800000000000000
      internalID: 7713251741885278747
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_109
      rect:
        serializedVersion: 2
        x: 41
        y: 495
        width: 28
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7dc38284d179ab790800000000000000
      internalID: -7513526876293808937
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_110
      rect:
        serializedVersion: 2
        x: 186
        y: 495
        width: 52
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 46cc8b2741c723b00800000000000000
      internalID: 806843710518643812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_111
      rect:
        serializedVersion: 2
        x: 335
        y: 495
        width: 47
        height: 52
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c18500a4e59c95990800000000000000
      internalID: -7396659506173683684
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_112
      rect:
        serializedVersion: 2
        x: 480
        y: 495
        width: 46
        height: 51
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0b4d3af1366967f90800000000000000
      internalID: -6956207221950393168
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_113
      rect:
        serializedVersion: 2
        x: 624
        y: 495
        width: 46
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 177f1793909ac3290800000000000000
      internalID: -7909260988485863567
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_114
      rect:
        serializedVersion: 2
        x: 763
        y: 495
        width: 46
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 297a7dbc7ee246b70800000000000000
      internalID: 8891283137428432786
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_115
      rect:
        serializedVersion: 2
        x: 910
        y: 495
        width: 44
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e60487d73302d4a30800000000000000
      internalID: 4201049432926994542
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_116
      rect:
        serializedVersion: 2
        x: 1056
        y: 495
        width: 43
        height: 39
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fe422769dd82f4f0800000000000000
      internalID: -796418219213435149
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_117
      rect:
        serializedVersion: 2
        x: 1200
        y: 495
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e7b47181e76cf85c0800000000000000
      internalID: -4210928881695765634
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_118
      rect:
        serializedVersion: 2
        x: 941
        y: 427
        width: 48
        height: 41
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ddf17914951427910800000000000000
      internalID: 1833599849901531101
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_119
      rect:
        serializedVersion: 2
        x: 1517
        y: 427
        width: 48
        height: 41
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 24260f09f7a078cf0800000000000000
      internalID: -250219711286975934
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_120
      rect:
        serializedVersion: 2
        x: 1698
        y: 458
        width: 11
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ea0a2d5f21e6da510800000000000000
      internalID: 1562025673480118446
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_121
      rect:
        serializedVersion: 2
        x: 1845
        y: 460
        width: 8
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0fee9e81cba39cde0800000000000000
      internalID: -1312453236855542032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_122
      rect:
        serializedVersion: 2
        x: 1990
        y: 465
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 89b83bf83441c5bf0800000000000000
      internalID: -334369991925986408
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_123
      rect:
        serializedVersion: 2
        x: 2135
        y: 467
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 875639d9de601b600800000000000000
      internalID: 482174252726052216
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_124
      rect:
        serializedVersion: 2
        x: 40
        y: 415
        width: 27
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8e7aa5cccf832afb0800000000000000
      internalID: -4638082007826782232
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_125
      rect:
        serializedVersion: 2
        x: 184
        y: 415
        width: 27
        height: 50
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a283b191b6c66cd60800000000000000
      internalID: 7910129002761369642
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_126
      rect:
        serializedVersion: 2
        x: 509
        y: 426
        width: 53
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e4d817ea0d240d8a0800000000000000
      internalID: -6282448016134533810
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_127
      rect:
        serializedVersion: 2
        x: 546
        y: 456
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a451fcef372f5e180800000000000000
      internalID: -9086590093139962550
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_128
      rect:
        serializedVersion: 2
        x: 653
        y: 428
        width: 52
        height: 35
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6053844fc969f28a0800000000000000
      internalID: -6327673350574820090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_129
      rect:
        serializedVersion: 2
        x: 692
        y: 459
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8af38991afdd14ca0800000000000000
      internalID: -6034297959458783320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_130
      rect:
        serializedVersion: 2
        x: 797
        y: 429
        width: 57
        height: 37
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9b2d4bb8f0317980800000000000000
      internalID: -8542993173292700771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_131
      rect:
        serializedVersion: 2
        x: 984
        y: 445
        width: 11
        height: 14
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dbbb719051bf17460800000000000000
      internalID: 7237842143926533053
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_132
      rect:
        serializedVersion: 2
        x: 1085
        y: 426
        width: 53
        height: 34
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a3adecdc1480082c0800000000000000
      internalID: -4431532954613786054
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_133
      rect:
        serializedVersion: 2
        x: 1122
        y: 456
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1029a9b81c2df2790800000000000000
      internalID: -7552586321364348415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_134
      rect:
        serializedVersion: 2
        x: 1229
        y: 428
        width: 52
        height: 35
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e486e66b686f0980800000000000000
      internalID: -8570516755365591834
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_135
      rect:
        serializedVersion: 2
        x: 1268
        y: 459
        width: 7
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e742efc314bfe0f0800000000000000
      internalID: -1085450987987908638
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_136
      rect:
        serializedVersion: 2
        x: 1373
        y: 429
        width: 57
        height: 37
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a332107c7200538a0800000000000000
      internalID: -6326149930729528518
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_137
      rect:
        serializedVersion: 2
        x: 1560
        y: 445
        width: 11
        height: 14
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6d6684b354a2e55e0800000000000000
      internalID: -1919049914377541930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_138
      rect:
        serializedVersion: 2
        x: 1686
        y: 428
        width: 30
        height: 32
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77f8413351c0931e0800000000000000
      internalID: -2217728056312426633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_139
      rect:
        serializedVersion: 2
        x: 1831
        y: 453
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0c762dd8088da600800000000000000
      internalID: 481190306849913867
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_140
      rect:
        serializedVersion: 2
        x: 1840
        y: 449
        width: 13
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1c268f723996dfc50800000000000000
      internalID: 6700627901348864705
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_141
      rect:
        serializedVersion: 2
        x: 1966
        y: 452
        width: 10
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e50bf6a84c42b4d0800000000000000
      internalID: -3120347716891507223
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_142
      rect:
        serializedVersion: 2
        x: 1979
        y: 456
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88111fe2f0fb8e0e0800000000000000
      internalID: -2240330742683856504
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_143
      rect:
        serializedVersion: 2
        x: 1985
        y: 451
        width: 13
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2ddb501c57639cff0800000000000000
      internalID: -15421244341633582
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_144
      rect:
        serializedVersion: 2
        x: 1994
        y: 442
        width: 12
        height: 17
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 70a48d4a69c1e7330800000000000000
      internalID: 3710434576336177671
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_145
      rect:
        serializedVersion: 2
        x: 2114
        y: 456
        width: 28
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 77344635656b92e90800000000000000
      internalID: -7049903259789278345
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_146
      rect:
        serializedVersion: 2
        x: 2142
        y: 436
        width: 9
        height: 23
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0ecea83f90bbc98d0800000000000000
      internalID: -2838188013735187232
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_147
      rect:
        serializedVersion: 2
        x: 2208
        y: 415
        width: 34
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9ed330572e8954f60800000000000000
      internalID: 8017982809996213737
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_148
      rect:
        serializedVersion: 2
        x: 331
        y: 415
        width: 33
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1547b09dbf82abd40800000000000000
      internalID: 5600834148731941969
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_149
      rect:
        serializedVersion: 2
        x: 365
        y: 428
        width: 50
        height: 29
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0005b3ced5837eb80800000000000000
      internalID: -8365655806770982912
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_150
      rect:
        serializedVersion: 2
        x: 478
        y: 415
        width: 30
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a0f39789c85f1e8b0800000000000000
      internalID: -5124544916768604406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_151
      rect:
        serializedVersion: 2
        x: 624
        y: 415
        width: 28
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8df535bf26bbe1d80800000000000000
      internalID: -8277973031262658600
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_152
      rect:
        serializedVersion: 2
        x: 763
        y: 415
        width: 33
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ffe8b7db1f3820220800000000000000
      internalID: 2450666221532319487
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_153
      rect:
        serializedVersion: 2
        x: 910
        y: 415
        width: 30
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2fa34577bad561d50800000000000000
      internalID: 6707651686037732082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_154
      rect:
        serializedVersion: 2
        x: 1056
        y: 415
        width: 28
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 777f0d26557e5dd20800000000000000
      internalID: 3302800255662225271
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_155
      rect:
        serializedVersion: 2
        x: 1195
        y: 415
        width: 33
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3889e307a3900a940800000000000000
      internalID: 5305250507638347907
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_156
      rect:
        serializedVersion: 2
        x: 1342
        y: 415
        width: 30
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3675f018cb6180130800000000000000
      internalID: 3533098906547083107
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_157
      rect:
        serializedVersion: 2
        x: 1488
        y: 415
        width: 28
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 440afd2017d3f74c0800000000000000
      internalID: -4287640764644614076
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_158
      rect:
        serializedVersion: 2
        x: 1627
        y: 415
        width: 33
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 18537c85507080230800000000000000
      internalID: 3605139221255763329
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_159
      rect:
        serializedVersion: 2
        x: 1661
        y: 440
        width: 27
        height: 15
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 97335b3dc63619a10800000000000000
      internalID: 1914420635668657017
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_160
      rect:
        serializedVersion: 2
        x: 1707
        y: 437
        width: 15
        height: 21
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd5472204aa4dfec0800000000000000
      internalID: -3531584464517380644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_161
      rect:
        serializedVersion: 2
        x: 1774
        y: 415
        width: 41
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 26168382d32128570800000000000000
      internalID: 8467350303287042402
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_162
      rect:
        serializedVersion: 2
        x: 1819
        y: 448
        width: 12
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c695e86abb8b98710800000000000000
      internalID: 1696089850774444396
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_163
      rect:
        serializedVersion: 2
        x: 1823
        y: 444
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 319c0ec306b5f2530800000000000000
      internalID: 3832382276811933971
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_164
      rect:
        serializedVersion: 2
        x: 1830
        y: 443
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d9b374c04eee551f0800000000000000
      internalID: -1056675874322564195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_165
      rect:
        serializedVersion: 2
        x: 1855
        y: 450
        width: 6
        height: 8
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c2a90b9a35a6ee700800000000000000
      internalID: 571511110284384812
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_166
      rect:
        serializedVersion: 2
        x: 1920
        y: 415
        width: 28
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 92b7301fdd8b35c50800000000000000
      internalID: 6652864337927174953
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_167
      rect:
        serializedVersion: 2
        x: 1969
        y: 439
        width: 13
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ba63f5e007d1e31f0800000000000000
      internalID: -1063380094898194773
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_168
      rect:
        serializedVersion: 2
        x: 1977
        y: 446
        width: 8
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8729601caf751480800000000000000
      internalID: -8929090309205710962
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_169
      rect:
        serializedVersion: 2
        x: 2064
        y: 415
        width: 28
        height: 40
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: caf107bbaa08a5c90800000000000000
      internalID: -7180285185111351380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_170
      rect:
        serializedVersion: 2
        x: 2114
        y: 442
        width: 16
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6321a9111b7666620800000000000000
      internalID: 2767013031267930678
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_171
      rect:
        serializedVersion: 2
        x: 701
        y: 437
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f892434f6c70f9c10800000000000000
      internalID: 2062375705440954767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_172
      rect:
        serializedVersion: 2
        x: 989
        y: 436
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 65143a78b7a7351b0800000000000000
      internalID: -5669052834954133162
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_173
      rect:
        serializedVersion: 2
        x: 1277
        y: 437
        width: 6
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 95e557b05ded23440800000000000000
      internalID: 4914235150016274009
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_174
      rect:
        serializedVersion: 2
        x: 1565
        y: 436
        width: 11
        height: 9
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e323b7dfa462a470800000000000000
      internalID: 8404390561027662818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_175
      rect:
        serializedVersion: 2
        x: 1675
        y: 433
        width: 14
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9908241a1826b2410800000000000000
      internalID: 1453363613652910233
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_176
      rect:
        serializedVersion: 2
        x: 1816
        y: 442
        width: 8
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd5c7959de3c4ec30800000000000000
      internalID: 4387847362150122973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_177
      rect:
        serializedVersion: 2
        x: 1824
        y: 438
        width: 12
        height: 8
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b77a11689a52cdb50800000000000000
      internalID: 6619206962356791163
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_178
      rect:
        serializedVersion: 2
        x: 1837
        y: 433
        width: 13
        height: 13
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f4001e519bcb5c9f0800000000000000
      internalID: -448745084731457457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_179
      rect:
        serializedVersion: 2
        x: 1848
        y: 439
        width: 15
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6220357535c5dc750800000000000000
      internalID: 6326814564542513702
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_180
      rect:
        serializedVersion: 2
        x: 1985
        y: 436
        width: 10
        height: 12
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d659fb3282282dc40800000000000000
      internalID: 5535630000901821805
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_181
      rect:
        serializedVersion: 2
        x: 2132
        y: 442
        width: 7
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74e68a05d286b6da0800000000000000
      internalID: -5950547938803945913
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_182
      rect:
        serializedVersion: 2
        x: 695
        y: 425
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 56e8a26209ad11760800000000000000
      internalID: 7426957573161127525
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_183
      rect:
        serializedVersion: 2
        x: 843
        y: 428
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6b7d108288ff15c0800000000000000
      internalID: -4242399084580865172
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_184
      rect:
        serializedVersion: 2
        x: 988
        y: 428
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1bca58b8da9b0d530800000000000000
      internalID: 3877803434187271345
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_185
      rect:
        serializedVersion: 2
        x: 1271
        y: 425
        width: 12
        height: 11
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 29ad09a133c1fd1c0800000000000000
      internalID: -4476828498768373102
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_186
      rect:
        serializedVersion: 2
        x: 1419
        y: 428
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1138947c862df7ae0800000000000000
      internalID: -1549288399330245871
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_187
      rect:
        serializedVersion: 2
        x: 1564
        y: 428
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b8d187b9412306650800000000000000
      internalID: 6224029749115100555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_188
      rect:
        serializedVersion: 2
        x: 1700
        y: 428
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2e6590022b939e960800000000000000
      internalID: 7631694480760461026
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_189
      rect:
        serializedVersion: 2
        x: 1848
        y: 430
        width: 8
        height: 8
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b63d4561333c825c0800000000000000
      internalID: -4239924424983915669
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_190
      rect:
        serializedVersion: 2
        x: 1997
        y: 433
        width: 6
        height: 6
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 213fea93278d69980800000000000000
      internalID: -8532394458901187822
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_191
      rect:
        serializedVersion: 2
        x: 40
        y: 335
        width: 30
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f3fe193ba57cd17b0800000000000000
      internalID: -5251822398070460609
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_192
      rect:
        serializedVersion: 2
        x: 185
        y: 335
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa0403085c784ae50800000000000000
      internalID: 6819725018074726575
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_193
      rect:
        serializedVersion: 2
        x: 329
        y: 335
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e1c303f269be2920800000000000000
      internalID: 2967513039118778851
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_194
      rect:
        serializedVersion: 2
        x: 472
        y: 335
        width: 30
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f8fe4655008ef7ff0800000000000000
      internalID: -36055183865352305
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_195
      rect:
        serializedVersion: 2
        x: 616
        y: 335
        width: 30
        height: 47
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 74bf209b12ba3f530800000000000000
      internalID: 3887639064677120839
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_196
      rect:
        serializedVersion: 2
        x: 760
        y: 335
        width: 30
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c82c2a2fbe78cb310800000000000000
      internalID: 1422161029800051340
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_197
      rect:
        serializedVersion: 2
        x: 905
        y: 335
        width: 29
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 050d6a26e68e6f580800000000000000
      internalID: -8793585661595168688
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_198
      rect:
        serializedVersion: 2
        x: 1049
        y: 335
        width: 29
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a55f191bae3520a0800000000000000
      internalID: -6907045548920973917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_199
      rect:
        serializedVersion: 2
        x: 46
        y: 256
        width: 24
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 84815a8f6173711b0800000000000000
      internalID: -5686015432731584440
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_200
      rect:
        serializedVersion: 2
        x: 191
        y: 256
        width: 23
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3e1078cb79e56b090800000000000000
      internalID: -8019118080690552349
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_201
      rect:
        serializedVersion: 2
        x: 335
        y: 256
        width: 23
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e8011c1e19e0f90d0800000000000000
      internalID: -3413993972802973554
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_202
      rect:
        serializedVersion: 2
        x: 479
        y: 256
        width: 23
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b4fdccb54e3d5cc80800000000000000
      internalID: -8302997360241483957
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_203
      rect:
        serializedVersion: 2
        x: 43
        y: 174
        width: 27
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98e05f6268eb65c40800000000000000
      internalID: 5500793478268128905
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_204
      rect:
        serializedVersion: 2
        x: 183
        y: 174
        width: 32
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57d0c53455dd9f3d0800000000000000
      internalID: -3172261104233738891
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_205
      rect:
        serializedVersion: 2
        x: 327
        y: 175
        width: 32
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 25041acac68fb6e20800000000000000
      internalID: 3345040293889130578
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_206
      rect:
        serializedVersion: 2
        x: 470
        y: 178
        width: 33
        height: 42
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4695b040553c88e60800000000000000
      internalID: 7964830710912211300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_207
      rect:
        serializedVersion: 2
        x: 617
        y: 177
        width: 31
        height: 44
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3afb43e262a6c54c0800000000000000
      internalID: -4297443232201785437
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_208
      rect:
        serializedVersion: 2
        x: 44
        y: 95
        width: 26
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c715dc30192f626f0800000000000000
      internalID: -709613186616307332
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_209
      rect:
        serializedVersion: 2
        x: 189
        y: 95
        width: 25
        height: 46
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5b2689825784ad3c0800000000000000
      internalID: -4334072023361232203
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_210
      rect:
        serializedVersion: 2
        x: 328
        y: 95
        width: 27
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dd6adfb8d1dc2a8c0800000000000000
      internalID: -3989400793110436131
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_211
      rect:
        serializedVersion: 2
        x: 472
        y: 95
        width: 28
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db6d47223845b3d40800000000000000
      internalID: 5565134686741911229
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_212
      rect:
        serializedVersion: 2
        x: 44
        y: 15
        width: 26
        height: 45
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3adbd6e4619fd4d80800000000000000
      internalID: -8264675866954580573
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_213
      rect:
        serializedVersion: 2
        x: 189
        y: 15
        width: 25
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1d1aeae143201a370800000000000000
      internalID: 8331943208488444369
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_214
      rect:
        serializedVersion: 2
        x: 328
        y: 15
        width: 27
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e1546222587833d0800000000000000
      internalID: -3226696838851309082
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_215
      rect:
        serializedVersion: 2
        x: 472
        y: 15
        width: 28
        height: 48
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f6059ba03d73bc780800000000000000
      internalID: -8661768078759473041
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_216
      rect:
        serializedVersion: 2
        x: 616
        y: 15
        width: 30
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7e2acf7722b0ed890800000000000000
      internalID: -7431490092444900633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_217
      rect:
        serializedVersion: 2
        x: 760
        y: 15
        width: 34
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 41f02b69db95f9420800000000000000
      internalID: 2638926577474342676
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_218
      rect:
        serializedVersion: 2
        x: 904
        y: 15
        width: 34
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 45aab2c7aeab74780800000000000000
      internalID: -8698778638974473644
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_219
      rect:
        serializedVersion: 2
        x: 1048
        y: 15
        width: 36
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f94ffbd7ea0ed0390800000000000000
      internalID: -7850371535398243169
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_220
      rect:
        serializedVersion: 2
        x: 1192
        y: 15
        width: 56
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6b75d6d90d3847f30800000000000000
      internalID: 4572424453705521078
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_221
      rect:
        serializedVersion: 2
        x: 1336
        y: 15
        width: 56
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fb0f55e66f834440800000000000000
      internalID: 4918932890116361209
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_222
      rect:
        serializedVersion: 2
        x: 1480
        y: 15
        width: 56
        height: 49
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 32bd93ff9125edf10800000000000000
      internalID: 2296363131615173411
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_223
      rect:
        serializedVersion: 2
        x: 330
        y: 415
        width: 85
        height: 42
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 127b13f4f8d120145ade3d7f3b6ddee5
      internalID: 230575904
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_224
      rect:
        serializedVersion: 2
        x: 478
        y: 415
        width: 84
        height: 49
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4ac9aa81e413b1b4fb89bbe86aaf04b8
      internalID: 1738356783
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_225
      rect:
        serializedVersion: 2
        x: 624
        y: 415
        width: 84
        height: 50
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 47ff03c3ad4daae40a39ce29f15cc00e
      internalID: 1035137489
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_226
      rect:
        serializedVersion: 2
        x: 763
        y: 415
        width: 92
        height: 51
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 75cd5032d7c20a141bac454a6ef47ee6
      internalID: -1525394683
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_227
      rect:
        serializedVersion: 2
        x: 910
        y: 414
        width: 90
        height: 54
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2db2a4af9acc4154a9ff94c75844d3eb
      internalID: -271195876
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_228
      rect:
        serializedVersion: 2
        x: 1056
        y: 415
        width: 82
        height: 49
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 13c80782e43e8634e90fc42e512acb8b
      internalID: -2123706144
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_229
      rect:
        serializedVersion: 2
        x: 1195
        y: 415
        width: 88
        height: 50
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b25a434788f5344095239722e97f927
      internalID: -491987276
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_230
      rect:
        serializedVersion: 2
        x: 1342
        y: 415
        width: 88
        height: 51
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: db76e9d126489d643919047c8b42c140
      internalID: 1170345860
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_231
      rect:
        serializedVersion: 2
        x: 1488
        y: 415
        width: 88
        height: 53
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6ce98dc1393d63745a17c3a97017fae2
      internalID: 1291704363
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_232
      rect:
        serializedVersion: 2
        x: 1627
        y: 415
        width: 95
        height: 56
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3f58fbb642cb02248930389ad4cd6809
      internalID: 370979022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_233
      rect:
        serializedVersion: 2
        x: 1774
        y: 415
        width: 89
        height: 54
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e4631cbe88dea984cadaf53d16027ad5
      internalID: 1996037155
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_234
      rect:
        serializedVersion: 2
        x: 1920
        y: 415
        width: 86
        height: 56
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2f5939bcae5515e48a330f2a23ac39bc
      internalID: -1926191398
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Fire_WarriorFireSword-Sheet_235
      rect:
        serializedVersion: 2
        x: 2064
        y: 415
        width: 87
        height: 58
      alignment: 9
      pivot: {x: 0.1, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d28dd1d592241b94badfaa1aa29a2305
      internalID: 1523677413
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 73f57abb11db43f478de5bc013485da0
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Fire_WarriorFireSword-Sheet_0: -4904060931984408531
      Fire_WarriorFireSword-Sheet_1: 1097052241410629379
      Fire_WarriorFireSword-Sheet_10: -3423818697110367269
      Fire_WarriorFireSword-Sheet_100: -6760068534779731385
      Fire_WarriorFireSword-Sheet_101: -7682172948956111454
      Fire_WarriorFireSword-Sheet_102: -6310427782916763419
      Fire_WarriorFireSword-Sheet_103: 2396546481631340439
      Fire_WarriorFireSword-Sheet_104: 6672414060514436456
      Fire_WarriorFireSword-Sheet_105: -434646505345235327
      Fire_WarriorFireSword-Sheet_106: -8195862610593739896
      Fire_WarriorFireSword-Sheet_107: -3490179084566062886
      Fire_WarriorFireSword-Sheet_108: 7713251741885278747
      Fire_WarriorFireSword-Sheet_109: -7513526876293808937
      Fire_WarriorFireSword-Sheet_11: 1499285857246109761
      Fire_WarriorFireSword-Sheet_110: 806843710518643812
      Fire_WarriorFireSword-Sheet_111: -7396659506173683684
      Fire_WarriorFireSword-Sheet_112: -6956207221950393168
      Fire_WarriorFireSword-Sheet_113: -7909260988485863567
      Fire_WarriorFireSword-Sheet_114: 8891283137428432786
      Fire_WarriorFireSword-Sheet_115: 4201049432926994542
      Fire_WarriorFireSword-Sheet_116: -796418219213435149
      Fire_WarriorFireSword-Sheet_117: -4210928881695765634
      Fire_WarriorFireSword-Sheet_118: 1833599849901531101
      Fire_WarriorFireSword-Sheet_119: -250219711286975934
      Fire_WarriorFireSword-Sheet_12: 4073459748314283189
      Fire_WarriorFireSword-Sheet_120: 1562025673480118446
      Fire_WarriorFireSword-Sheet_121: -1312453236855542032
      Fire_WarriorFireSword-Sheet_122: -334369991925986408
      Fire_WarriorFireSword-Sheet_123: 482174252726052216
      Fire_WarriorFireSword-Sheet_124: -4638082007826782232
      Fire_WarriorFireSword-Sheet_125: 7910129002761369642
      Fire_WarriorFireSword-Sheet_126: -6282448016134533810
      Fire_WarriorFireSword-Sheet_127: -9086590093139962550
      Fire_WarriorFireSword-Sheet_128: -6327673350574820090
      Fire_WarriorFireSword-Sheet_129: -6034297959458783320
      Fire_WarriorFireSword-Sheet_13: -5269489878570228012
      Fire_WarriorFireSword-Sheet_130: -8542993173292700771
      Fire_WarriorFireSword-Sheet_131: 7237842143926533053
      Fire_WarriorFireSword-Sheet_132: -4431532954613786054
      Fire_WarriorFireSword-Sheet_133: -7552586321364348415
      Fire_WarriorFireSword-Sheet_134: -8570516755365591834
      Fire_WarriorFireSword-Sheet_135: -1085450987987908638
      Fire_WarriorFireSword-Sheet_136: -6326149930729528518
      Fire_WarriorFireSword-Sheet_137: -1919049914377541930
      Fire_WarriorFireSword-Sheet_138: -2217728056312426633
      Fire_WarriorFireSword-Sheet_139: 481190306849913867
      Fire_WarriorFireSword-Sheet_14: 5645773020238216161
      Fire_WarriorFireSword-Sheet_140: 6700627901348864705
      Fire_WarriorFireSword-Sheet_141: -3120347716891507223
      Fire_WarriorFireSword-Sheet_142: -2240330742683856504
      Fire_WarriorFireSword-Sheet_143: -15421244341633582
      Fire_WarriorFireSword-Sheet_144: 3710434576336177671
      Fire_WarriorFireSword-Sheet_145: -7049903259789278345
      Fire_WarriorFireSword-Sheet_146: -2838188013735187232
      Fire_WarriorFireSword-Sheet_147: 8017982809996213737
      Fire_WarriorFireSword-Sheet_148: 5600834148731941969
      Fire_WarriorFireSword-Sheet_149: -8365655806770982912
      Fire_WarriorFireSword-Sheet_15: -3418867535436416985
      Fire_WarriorFireSword-Sheet_150: -5124544916768604406
      Fire_WarriorFireSword-Sheet_151: -8277973031262658600
      Fire_WarriorFireSword-Sheet_152: 2450666221532319487
      Fire_WarriorFireSword-Sheet_153: 6707651686037732082
      Fire_WarriorFireSword-Sheet_154: 3302800255662225271
      Fire_WarriorFireSword-Sheet_155: 5305250507638347907
      Fire_WarriorFireSword-Sheet_156: 3533098906547083107
      Fire_WarriorFireSword-Sheet_157: -4287640764644614076
      Fire_WarriorFireSword-Sheet_158: 3605139221255763329
      Fire_WarriorFireSword-Sheet_159: 1914420635668657017
      Fire_WarriorFireSword-Sheet_16: 8404332561688078241
      Fire_WarriorFireSword-Sheet_160: -3531584464517380644
      Fire_WarriorFireSword-Sheet_161: 8467350303287042402
      Fire_WarriorFireSword-Sheet_162: 1696089850774444396
      Fire_WarriorFireSword-Sheet_163: 3832382276811933971
      Fire_WarriorFireSword-Sheet_164: -1056675874322564195
      Fire_WarriorFireSword-Sheet_165: 571511110284384812
      Fire_WarriorFireSword-Sheet_166: 6652864337927174953
      Fire_WarriorFireSword-Sheet_167: -1063380094898194773
      Fire_WarriorFireSword-Sheet_168: -8929090309205710962
      Fire_WarriorFireSword-Sheet_169: -7180285185111351380
      Fire_WarriorFireSword-Sheet_17: 2498922528779838491
      Fire_WarriorFireSword-Sheet_170: 2767013031267930678
      Fire_WarriorFireSword-Sheet_171: 2062375705440954767
      Fire_WarriorFireSword-Sheet_172: -5669052834954133162
      Fire_WarriorFireSword-Sheet_173: 4914235150016274009
      Fire_WarriorFireSword-Sheet_174: 8404390561027662818
      Fire_WarriorFireSword-Sheet_175: 1453363613652910233
      Fire_WarriorFireSword-Sheet_176: 4387847362150122973
      Fire_WarriorFireSword-Sheet_177: 6619206962356791163
      Fire_WarriorFireSword-Sheet_178: -448745084731457457
      Fire_WarriorFireSword-Sheet_179: 6326814564542513702
      Fire_WarriorFireSword-Sheet_18: 1444096994125233174
      Fire_WarriorFireSword-Sheet_180: 5535630000901821805
      Fire_WarriorFireSword-Sheet_181: -5950547938803945913
      Fire_WarriorFireSword-Sheet_182: 7426957573161127525
      Fire_WarriorFireSword-Sheet_183: -4242399084580865172
      Fire_WarriorFireSword-Sheet_184: 3877803434187271345
      Fire_WarriorFireSword-Sheet_185: -4476828498768373102
      Fire_WarriorFireSword-Sheet_186: -1549288399330245871
      Fire_WarriorFireSword-Sheet_187: 6224029749115100555
      Fire_WarriorFireSword-Sheet_188: 7631694480760461026
      Fire_WarriorFireSword-Sheet_189: -4239924424983915669
      Fire_WarriorFireSword-Sheet_19: -5220089080725606393
      Fire_WarriorFireSword-Sheet_190: -8532394458901187822
      Fire_WarriorFireSword-Sheet_191: -5251822398070460609
      Fire_WarriorFireSword-Sheet_192: 6819725018074726575
      Fire_WarriorFireSword-Sheet_193: 2967513039118778851
      Fire_WarriorFireSword-Sheet_194: -36055183865352305
      Fire_WarriorFireSword-Sheet_195: 3887639064677120839
      Fire_WarriorFireSword-Sheet_196: 1422161029800051340
      Fire_WarriorFireSword-Sheet_197: -8793585661595168688
      Fire_WarriorFireSword-Sheet_198: -6907045548920973917
      Fire_WarriorFireSword-Sheet_199: -5686015432731584440
      Fire_WarriorFireSword-Sheet_2: -1042619608624661564
      Fire_WarriorFireSword-Sheet_20: -7031807366073298382
      Fire_WarriorFireSword-Sheet_200: -8019118080690552349
      Fire_WarriorFireSword-Sheet_201: -3413993972802973554
      Fire_WarriorFireSword-Sheet_202: -8302997360241483957
      Fire_WarriorFireSword-Sheet_203: 5500793478268128905
      Fire_WarriorFireSword-Sheet_204: -3172261104233738891
      Fire_WarriorFireSword-Sheet_205: 3345040293889130578
      Fire_WarriorFireSword-Sheet_206: 7964830710912211300
      Fire_WarriorFireSword-Sheet_207: -4297443232201785437
      Fire_WarriorFireSword-Sheet_208: -709613186616307332
      Fire_WarriorFireSword-Sheet_209: -4334072023361232203
      Fire_WarriorFireSword-Sheet_21: 5410005170011970823
      Fire_WarriorFireSword-Sheet_210: -3989400793110436131
      Fire_WarriorFireSword-Sheet_211: 5565134686741911229
      Fire_WarriorFireSword-Sheet_212: -8264675866954580573
      Fire_WarriorFireSword-Sheet_213: 8331943208488444369
      Fire_WarriorFireSword-Sheet_214: -3226696838851309082
      Fire_WarriorFireSword-Sheet_215: -8661768078759473041
      Fire_WarriorFireSword-Sheet_216: -7431490092444900633
      Fire_WarriorFireSword-Sheet_217: 2638926577474342676
      Fire_WarriorFireSword-Sheet_218: -8698778638974473644
      Fire_WarriorFireSword-Sheet_219: -7850371535398243169
      Fire_WarriorFireSword-Sheet_22: -1871467571364522050
      Fire_WarriorFireSword-Sheet_220: 4572424453705521078
      Fire_WarriorFireSword-Sheet_221: 4918932890116361209
      Fire_WarriorFireSword-Sheet_222: 2296363131615173411
      Fire_WarriorFireSword-Sheet_223: 230575904
      Fire_WarriorFireSword-Sheet_224: 1738356783
      Fire_WarriorFireSword-Sheet_225: 1035137489
      Fire_WarriorFireSword-Sheet_226: -1525394683
      Fire_WarriorFireSword-Sheet_227: -271195876
      Fire_WarriorFireSword-Sheet_228: -2123706144
      Fire_WarriorFireSword-Sheet_229: -491987276
      Fire_WarriorFireSword-Sheet_23: 7734862016100379598
      Fire_WarriorFireSword-Sheet_230: 1170345860
      Fire_WarriorFireSword-Sheet_231: 1291704363
      Fire_WarriorFireSword-Sheet_232: 370979022
      Fire_WarriorFireSword-Sheet_233: 1996037155
      Fire_WarriorFireSword-Sheet_234: -1926191398
      Fire_WarriorFireSword-Sheet_235: 1523677413
      Fire_WarriorFireSword-Sheet_24: 334592909229406553
      Fire_WarriorFireSword-Sheet_25: -1818894400158008479
      Fire_WarriorFireSword-Sheet_26: 4501609856398717390
      Fire_WarriorFireSword-Sheet_27: -7432831540960049113
      Fire_WarriorFireSword-Sheet_28: -4980048669866161948
      Fire_WarriorFireSword-Sheet_29: 8173339526930411951
      Fire_WarriorFireSword-Sheet_3: 5181327836484697304
      Fire_WarriorFireSword-Sheet_30: 3350760646591228236
      Fire_WarriorFireSword-Sheet_31: 16307728598430072
      Fire_WarriorFireSword-Sheet_32: -840994939869901896
      Fire_WarriorFireSword-Sheet_33: -3771119007739376577
      Fire_WarriorFireSword-Sheet_34: 8756418113305168632
      Fire_WarriorFireSword-Sheet_35: -3464657296522377036
      Fire_WarriorFireSword-Sheet_36: 4232190747824605073
      Fire_WarriorFireSword-Sheet_37: -6764551840999554552
      Fire_WarriorFireSword-Sheet_38: -1784975513250127364
      Fire_WarriorFireSword-Sheet_39: -3675091216472680683
      Fire_WarriorFireSword-Sheet_4: 9072091307349490749
      Fire_WarriorFireSword-Sheet_40: -2489430374634202124
      Fire_WarriorFireSword-Sheet_41: -2762440677337370644
      Fire_WarriorFireSword-Sheet_42: -3325531515707334911
      Fire_WarriorFireSword-Sheet_43: -3674167966180859333
      Fire_WarriorFireSword-Sheet_44: -6204609799786472866
      Fire_WarriorFireSword-Sheet_45: 4209470473251784477
      Fire_WarriorFireSword-Sheet_46: 3688992650357657688
      Fire_WarriorFireSword-Sheet_47: -7815741919274686834
      Fire_WarriorFireSword-Sheet_48: -3830370071889123185
      Fire_WarriorFireSword-Sheet_49: 3501082362127154679
      Fire_WarriorFireSword-Sheet_5: -2306392010017305716
      Fire_WarriorFireSword-Sheet_50: -1070368551428574527
      Fire_WarriorFireSword-Sheet_51: -7683281129174531635
      Fire_WarriorFireSword-Sheet_52: -7497913099252786439
      Fire_WarriorFireSword-Sheet_53: -8049278785306582317
      Fire_WarriorFireSword-Sheet_54: -6887270137552344815
      Fire_WarriorFireSword-Sheet_55: 3730438522977345134
      Fire_WarriorFireSword-Sheet_56: -5369804697258680598
      Fire_WarriorFireSword-Sheet_57: 8502501480965070884
      Fire_WarriorFireSword-Sheet_58: -4863277473842490365
      Fire_WarriorFireSword-Sheet_59: -9049664380470677528
      Fire_WarriorFireSword-Sheet_6: -1894708832912923415
      Fire_WarriorFireSword-Sheet_60: -622436962368991780
      Fire_WarriorFireSword-Sheet_61: -3408476400592916472
      Fire_WarriorFireSword-Sheet_62: 2935965203409687798
      Fire_WarriorFireSword-Sheet_63: -4964342189221567242
      Fire_WarriorFireSword-Sheet_64: -7633963068577370476
      Fire_WarriorFireSword-Sheet_65: -4765989794712932744
      Fire_WarriorFireSword-Sheet_66: 8488006700303112307
      Fire_WarriorFireSword-Sheet_67: -1059295366987352956
      Fire_WarriorFireSword-Sheet_68: 1623578526369706666
      Fire_WarriorFireSword-Sheet_69: 6519921053705386639
      Fire_WarriorFireSword-Sheet_7: 7366699977686473458
      Fire_WarriorFireSword-Sheet_70: -6729663148708633812
      Fire_WarriorFireSword-Sheet_71: -7018855618056735107
      Fire_WarriorFireSword-Sheet_72: -7202091154204182158
      Fire_WarriorFireSword-Sheet_73: -3191746942449540678
      Fire_WarriorFireSword-Sheet_74: 5950039674075746694
      Fire_WarriorFireSword-Sheet_75: -8444722477689299561
      Fire_WarriorFireSword-Sheet_76: -8914290602440123675
      Fire_WarriorFireSword-Sheet_77: -7423302892295114259
      Fire_WarriorFireSword-Sheet_78: 3775811148037459427
      Fire_WarriorFireSword-Sheet_79: 7221403751928692256
      Fire_WarriorFireSword-Sheet_8: -7764981491358197694
      Fire_WarriorFireSword-Sheet_80: 6819586172183786837
      Fire_WarriorFireSword-Sheet_81: 2020535866090774657
      Fire_WarriorFireSword-Sheet_82: -3650762844592107245
      Fire_WarriorFireSword-Sheet_83: 1949736526466859164
      Fire_WarriorFireSword-Sheet_84: -2280020493951924489
      Fire_WarriorFireSword-Sheet_85: 4282731640604092970
      Fire_WarriorFireSword-Sheet_86: 243487079525577496
      Fire_WarriorFireSword-Sheet_87: -1675214217001596102
      Fire_WarriorFireSword-Sheet_88: -8047594970978909828
      Fire_WarriorFireSword-Sheet_89: -5939653874309101471
      Fire_WarriorFireSword-Sheet_9: 810512240211234347
      Fire_WarriorFireSword-Sheet_90: -5986811485329310736
      Fire_WarriorFireSword-Sheet_91: -63207839836775335
      Fire_WarriorFireSword-Sheet_92: 5090865770312087798
      Fire_WarriorFireSword-Sheet_93: -4619119848115420422
      Fire_WarriorFireSword-Sheet_94: -7087886686813852041
      Fire_WarriorFireSword-Sheet_95: -6990132740499694735
      Fire_WarriorFireSword-Sheet_96: -6867214773945115138
      Fire_WarriorFireSword-Sheet_97: 8421337564470047216
      Fire_WarriorFireSword-Sheet_98: 6230507712342741987
      Fire_WarriorFireSword-Sheet_99: -1899007000852105579
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
