%!PS-Adobe-3.1 EPSF-3.0
%ADO_DSC_Encoding: Windows Cyrillic
%%Title: Wraith_02_Left Hand.eps
%%Creator: Adobe Illustrator(R) 23.0
%%For: ABCDetective
%%CreationDate: 7/31/2020
%%BoundingBox: 0 0 64 64
%%HiResBoundingBox: 0 0 64 64
%%CropBox: 0 0 64 64
%%LanguageLevel: 3
%%DocumentData: Clean7Bit
%ADOBeginClientInjection: DocumentHeader "AI11EPS"
%%AI8_CreatorVersion: 23.0.1
%AI9_PrintingDataBegin
%ADO_BuildNumber: Adobe Illustrator(R) 23.0.1 x540 R agm 4.7767 ct 5.4352
%ADO_ContainsXMP: MainFirst

%ADOEndClientInjection: DocumentHeader "AI11EPS"
%%Pages: 1
%%DocumentNeededResources: 
%%DocumentSuppliedResources: procset Adobe_AGM_Image 1.0 0
%%+ procset Adobe_CoolType_Utility_T42 1.0 0
%%+ procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%+ procset Adobe_CoolType_Core 2.31 0
%%+ procset Adobe_AGM_Core 2.0 0
%%+ procset Adobe_AGM_Utils 1.0 0
%%DocumentFonts: 
%%DocumentNeededFonts: 
%%DocumentNeededFeatures: 
%%DocumentSuppliedFeatures: 
%%DocumentProcessColors:  Cyan Magenta Yellow Black
%%DocumentCustomColors: 
%%CMYKCustomColor: 
%%RGBCustomColor: 
%%EndComments
                                                                                                                                                                                                               
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
                                                                                                                                                                                                                                                         
%%BeginDefaults
%%ViewingOrientation: 1 0 0 1
%%EndDefaults
%%BeginProlog
%%BeginResource: procset Adobe_AGM_Utils 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{currentpacking	true setpacking}if
userdict/Adobe_AGM_Utils 75 dict dup begin put
/bdf
{bind def}bind def
/nd{null def}bdf
/xdf
{exch def}bdf
/ldf 
{load def}bdf
/ddf
{put}bdf	
/xddf
{3 -1 roll put}bdf	
/xpt
{exch put}bdf
/ndf
{
	exch dup where{
		pop pop pop
	}{
		xdf
	}ifelse
}def
/cdndf
{
	exch dup currentdict exch known{
		pop pop
	}{
		exch def
	}ifelse
}def
/gx
{get exec}bdf
/ps_level
	/languagelevel where{
		pop systemdict/languagelevel gx
	}{
		1
	}ifelse
def
/level2 
	ps_level 2 ge
def
/level3 
	ps_level 3 ge
def
/ps_version
	{version cvr}stopped{-1}if
def
/set_gvm
{currentglobal exch setglobal}bdf
/reset_gvm
{setglobal}bdf
/makereadonlyarray
{
	/packedarray where{pop packedarray
	}{
		array astore readonly}ifelse
}bdf
/map_reserved_ink_name
{
	dup type/stringtype eq{
		dup/Red eq{
			pop(_Red_)
		}{
			dup/Green eq{
				pop(_Green_)
			}{
				dup/Blue eq{
					pop(_Blue_)
				}{
					dup()cvn eq{
						pop(Process)
					}if
				}ifelse
			}ifelse
		}ifelse
	}if
}bdf
/AGMUTIL_GSTATE 22 dict def
/get_gstate
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_clr_spc currentcolorspace def
	/AGMUTIL_GSTATE_clr_indx 0 def
	/AGMUTIL_GSTATE_clr_comps 12 array def
	mark currentcolor counttomark
		{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 3 -1 roll put
		/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 add def}repeat pop
	/AGMUTIL_GSTATE_fnt rootfont def
	/AGMUTIL_GSTATE_lw currentlinewidth def
	/AGMUTIL_GSTATE_lc currentlinecap def
	/AGMUTIL_GSTATE_lj currentlinejoin def
	/AGMUTIL_GSTATE_ml currentmiterlimit def
	currentdash/AGMUTIL_GSTATE_do xdf/AGMUTIL_GSTATE_da xdf
	/AGMUTIL_GSTATE_sa currentstrokeadjust def
	/AGMUTIL_GSTATE_clr_rnd currentcolorrendering def
	/AGMUTIL_GSTATE_op currentoverprint def
	/AGMUTIL_GSTATE_bg currentblackgeneration cvlit def
	/AGMUTIL_GSTATE_ucr currentundercolorremoval cvlit def
	currentcolortransfer cvlit/AGMUTIL_GSTATE_gy_xfer xdf cvlit/AGMUTIL_GSTATE_b_xfer xdf
		cvlit/AGMUTIL_GSTATE_g_xfer xdf cvlit/AGMUTIL_GSTATE_r_xfer xdf
	/AGMUTIL_GSTATE_ht currenthalftone def
	/AGMUTIL_GSTATE_flt currentflat def
	end
}def
/set_gstate
{
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_clr_spc setcolorspace
	AGMUTIL_GSTATE_clr_indx{AGMUTIL_GSTATE_clr_comps AGMUTIL_GSTATE_clr_indx 1 sub get
	/AGMUTIL_GSTATE_clr_indx AGMUTIL_GSTATE_clr_indx 1 sub def}repeat setcolor
	AGMUTIL_GSTATE_fnt setfont
	AGMUTIL_GSTATE_lw setlinewidth
	AGMUTIL_GSTATE_lc setlinecap
	AGMUTIL_GSTATE_lj setlinejoin
	AGMUTIL_GSTATE_ml setmiterlimit
	AGMUTIL_GSTATE_da AGMUTIL_GSTATE_do setdash
	AGMUTIL_GSTATE_sa setstrokeadjust
	AGMUTIL_GSTATE_clr_rnd setcolorrendering
	AGMUTIL_GSTATE_op setoverprint
	AGMUTIL_GSTATE_bg cvx setblackgeneration
	AGMUTIL_GSTATE_ucr cvx setundercolorremoval
	AGMUTIL_GSTATE_r_xfer cvx AGMUTIL_GSTATE_g_xfer cvx AGMUTIL_GSTATE_b_xfer cvx
		AGMUTIL_GSTATE_gy_xfer cvx setcolortransfer
	AGMUTIL_GSTATE_ht/HalftoneType get dup 9 eq exch 100 eq or
		{
		currenthalftone/HalftoneType get AGMUTIL_GSTATE_ht/HalftoneType get ne
			{
			 mark AGMUTIL_GSTATE_ht{sethalftone}stopped cleartomark
			}if
		}{
		AGMUTIL_GSTATE_ht sethalftone
		}ifelse
	AGMUTIL_GSTATE_flt setflat
	end
}def
/get_gstate_and_matrix
{
	AGMUTIL_GSTATE begin
	/AGMUTIL_GSTATE_ctm matrix currentmatrix def
	end
	get_gstate
}def
/set_gstate_and_matrix
{
	set_gstate
	AGMUTIL_GSTATE begin
	AGMUTIL_GSTATE_ctm setmatrix
	end
}def
/AGMUTIL_str256 256 string def
/AGMUTIL_src256 256 string def
/AGMUTIL_dst64 64 string def
/AGMUTIL_srcLen nd
/AGMUTIL_ndx nd
/AGMUTIL_cpd nd
/capture_cpd{
	//Adobe_AGM_Utils/AGMUTIL_cpd currentpagedevice ddf
}def
/thold_halftone
{
	level3
		{sethalftone currenthalftone}
		{
			dup/HalftoneType get 3 eq
			{
				sethalftone currenthalftone
			}{
				begin
				Width Height mul{
					Thresholds read{pop}if
				}repeat
				end
				currenthalftone
			}ifelse
		}ifelse
}def 
/rdcmntline
{
	currentfile AGMUTIL_str256 readline pop
	(%)anchorsearch{pop}if
}bdf
/filter_cmyk
{	
	dup type/filetype ne{
		exch()/SubFileDecode filter
	}{
		exch pop
	}
	ifelse
	[
	exch
	{
		AGMUTIL_src256 readstring pop
		dup length/AGMUTIL_srcLen exch def
		/AGMUTIL_ndx 0 def
		AGMCORE_plate_ndx 4 AGMUTIL_srcLen 1 sub{
			1 index exch get
			AGMUTIL_dst64 AGMUTIL_ndx 3 -1 roll put
			/AGMUTIL_ndx AGMUTIL_ndx 1 add def
		}for
		pop
		AGMUTIL_dst64 0 AGMUTIL_ndx getinterval
	}
	bind
	/exec cvx
	]cvx
}bdf
/filter_indexed_devn
{
	cvi Names length mul names_index add Lookup exch get
}bdf
/filter_devn
{	
	4 dict begin
	/srcStr xdf
	/dstStr xdf
	dup type/filetype ne{
		0()/SubFileDecode filter
	}if
	[
	exch
		[
			/devicen_colorspace_dict/AGMCORE_gget cvx/begin cvx
			currentdict/srcStr get/readstring cvx/pop cvx
			/dup cvx/length cvx 0/gt cvx[
				Adobe_AGM_Utils/AGMUTIL_ndx 0/ddf cvx
				names_index Names length currentdict/srcStr get length 1 sub{
					1/index cvx/exch cvx/get cvx
					currentdict/dstStr get/AGMUTIL_ndx/load cvx 3 -1/roll cvx/put cvx
					Adobe_AGM_Utils/AGMUTIL_ndx/AGMUTIL_ndx/load cvx 1/add cvx/ddf cvx
				}for
				currentdict/dstStr get 0/AGMUTIL_ndx/load cvx/getinterval cvx
			]cvx/if cvx
			/end cvx
		]cvx
		bind
		/exec cvx
	]cvx
	end
}bdf
/AGMUTIL_imagefile nd
/read_image_file
{
	AGMUTIL_imagefile 0 setfileposition
	10 dict begin
	/imageDict xdf
	/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
	/imbufIdx 0 def
	/origDataSource imageDict/DataSource get def
	/origMultipleDataSources imageDict/MultipleDataSources get def
	/origDecode imageDict/Decode get def
	/dstDataStr imageDict/Width get colorSpaceElemCnt mul string def
	imageDict/MultipleDataSources known{MultipleDataSources}{false}ifelse
	{
		/imbufCnt imageDict/DataSource get length def
		/imbufs imbufCnt array def
		0 1 imbufCnt 1 sub{
			/imbufIdx xdf
			imbufs imbufIdx imbufLen string put
			imageDict/DataSource get imbufIdx[AGMUTIL_imagefile imbufs imbufIdx get/readstring cvx/pop cvx]cvx put
		}for
		DeviceN_PS2{
			imageDict begin
		 	/DataSource[DataSource/devn_sep_datasource cvx]cvx def
			/MultipleDataSources false def
			/Decode[0 1]def
			end
		}if
	}{
		/imbuf imbufLen string def
		Indexed_DeviceN level3 not and DeviceN_NoneName or{
			/srcDataStrs[imageDict begin
				currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
				{
					Width Decode length 2 div mul cvi string
				}repeat
				end]def		
			imageDict begin
		 	/DataSource[AGMUTIL_imagefile Decode BitsPerComponent false 1/filter_indexed_devn load dstDataStr srcDataStrs devn_alt_datasource/exec cvx]cvx def
			/Decode[0 1]def
			end
		}{
			imageDict/DataSource[1 string dup 0 AGMUTIL_imagefile Decode length 2 idiv string/readstring cvx/pop cvx names_index/get cvx/put cvx]cvx put
			imageDict/Decode[0 1]put
		}ifelse
	}ifelse
	imageDict exch
	load exec
	imageDict/DataSource origDataSource put
	imageDict/MultipleDataSources origMultipleDataSources put
	imageDict/Decode origDecode put	
	end
}bdf
/write_image_file
{
	begin
	{(AGMUTIL_imagefile)(w+)file}stopped{
		false
	}{
		Adobe_AGM_Utils/AGMUTIL_imagefile xddf 
		2 dict begin
		/imbufLen Width BitsPerComponent mul 7 add 8 idiv def
		MultipleDataSources{DataSource 0 get}{DataSource}ifelse type/filetype eq{
			/imbuf imbufLen string def
		}if
		1 1 Height MultipleDataSources not{Decode length 2 idiv mul}if{
			pop
			MultipleDataSources{
			 	0 1 DataSource length 1 sub{
					DataSource type dup
					/arraytype eq{
						pop DataSource exch gx
					}{
						/filetype eq{
							DataSource exch get imbuf readstring pop
						}{
							DataSource exch get
						}ifelse
					}ifelse
					AGMUTIL_imagefile exch writestring
				}for
			}{
				DataSource type dup
				/arraytype eq{
					pop DataSource exec
				}{
					/filetype eq{
						DataSource imbuf readstring pop
					}{
						DataSource
					}ifelse
				}ifelse
				AGMUTIL_imagefile exch writestring
			}ifelse
		}for
		end
		true
	}ifelse
	end
}bdf
/close_image_file
{
	AGMUTIL_imagefile closefile(AGMUTIL_imagefile)deletefile
}def
statusdict/product known userdict/AGMP_current_show known not and{
	/pstr statusdict/product get def
	pstr(HP LaserJet 2200)eq 	
	pstr(HP LaserJet 4000 Series)eq or
	pstr(HP LaserJet 4050 Series )eq or
	pstr(HP LaserJet 8000 Series)eq or
	pstr(HP LaserJet 8100 Series)eq or
	pstr(HP LaserJet 8150 Series)eq or
	pstr(HP LaserJet 5000 Series)eq or
	pstr(HP LaserJet 5100 Series)eq or
	pstr(HP Color LaserJet 4500)eq or
	pstr(HP Color LaserJet 4600)eq or
	pstr(HP LaserJet 5Si)eq or
	pstr(HP LaserJet 1200 Series)eq or
	pstr(HP LaserJet 1300 Series)eq or
	pstr(HP LaserJet 4100 Series)eq or 
	{
 		userdict/AGMP_current_show/show load put
		userdict/show{
		 currentcolorspace 0 get
		 /Pattern eq
		 {false charpath f}
		 {AGMP_current_show}ifelse
		}put
	}if
	currentdict/pstr undef
}if
/consumeimagedata
{
	begin
	AGMIMG_init_common
	currentdict/MultipleDataSources known not
		{/MultipleDataSources false def}if
	MultipleDataSources
		{
		DataSource 0 get type
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width cvi string def
			1 1 Height cvi
				{
				pop
				0 1 DataSource length 1 sub
					{
					DataSource exch get
					flushbuffer readstring pop pop
					}for
				}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or DataSource 0 get xcheck and
			{
			Width Height mul cvi
				{
				0 1 DataSource length 1 sub
					{dup DataSource exch gx length exch 0 ne{pop}if}for
				dup 0 eq
					{pop exit}if
				sub dup 0 le
					{exit}if
				}loop
			pop
			}if		
		}
		{
		/DataSource load type 
		dup/filetype eq
			{
			1 dict begin
			/flushbuffer Width Decode length 2 idiv mul cvi string def
			1 1 Height{pop DataSource flushbuffer readstring pop pop}for
			end
			}if
		dup/arraytype eq exch/packedarraytype eq or/DataSource load xcheck and
			{
				Height Width BitsPerComponent mul 8 BitsPerComponent sub add 8 idiv Decode length 2 idiv mul mul
					{
					DataSource length dup 0 eq
						{pop exit}if
					sub dup 0 le
						{exit}if
					}loop
				pop
			}if
		}ifelse
	end
}bdf
/addprocs
{
	 2{/exec load}repeat
	 3 1 roll
	 [5 1 roll]bind cvx
}def
/modify_halftone_xfer
{
	currenthalftone dup length dict copy begin
	 currentdict 2 index known{
	 	1 index load dup length dict copy begin
		currentdict/TransferFunction known{
			/TransferFunction load
		}{
			currenttransfer
		}ifelse
		 addprocs/TransferFunction xdf 
		 currentdict end def
		currentdict end sethalftone
	}{
		currentdict/TransferFunction known{
			/TransferFunction load 
		}{
			currenttransfer
		}ifelse
		addprocs/TransferFunction xdf
		currentdict end sethalftone		
		pop
	}ifelse
}def
/clonearray
{
	dup xcheck exch
	dup length array exch
	Adobe_AGM_Core/AGMCORE_tmp -1 ddf 
	{
	Adobe_AGM_Core/AGMCORE_tmp 2 copy get 1 add ddf 
	dup type/dicttype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get
			exch
			clonedict
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	dup type/arraytype eq
		{
			Adobe_AGM_Core/AGMCORE_tmp get exch
			clonearray
			Adobe_AGM_Core/AGMCORE_tmp 4 -1 roll ddf 
		}if
	exch dup
	Adobe_AGM_Core/AGMCORE_tmp get 4 -1 roll put
	}forall
	exch{cvx}if
}bdf
/clonedict
{
	dup length dict
	begin
	{
		dup type/dicttype eq
			{clonedict}if
		dup type/arraytype eq
			{clonearray}if
		def
	}forall
	currentdict
	end
}bdf
/DeviceN_PS2
{
	/currentcolorspace AGMCORE_gget 0 get/DeviceN eq level3 not and
}bdf
/Indexed_DeviceN
{
	/indexed_colorspace_dict AGMCORE_gget dup null ne{
		dup/CSDBase known{
			/CSDBase get/CSD get_res/Names known 
		}{
			pop false
		}ifelse
	}{
		pop false
	}ifelse
}bdf
/DeviceN_NoneName
{	
	/Names where{
		pop
		false Names
		{
			(None)eq or
		}forall
	}{
		false
	}ifelse
}bdf
/DeviceN_PS2_inRip_seps
{
	/AGMCORE_in_rip_sep where
	{
		pop dup type dup/arraytype eq exch/packedarraytype eq or
		{
			dup 0 get/DeviceN eq level3 not and AGMCORE_in_rip_sep and
			{
				/currentcolorspace exch AGMCORE_gput
				false
			}{
				true
			}ifelse
		}{
			true
		}ifelse
	}{
		true
	}ifelse
}bdf
/base_colorspace_type
{
	dup type/arraytype eq{0 get}if
}bdf
/currentdistillerparams where{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
{
	/pdfmark_5{cleartomark}bind def
}{
	/pdfmark_5{pdfmark}bind def
}ifelse
/ReadBypdfmark_5
{
	currentfile exch 0 exch/SubFileDecode filter
	/currentdistillerparams where 
	{pop currentdistillerparams/CoreDistVersion get 5000 lt}{true}ifelse
	{flushfile cleartomark}
	{/PUT pdfmark}ifelse 	
}bdf
/ReadBypdfmark_5_string
{
	2 dict begin
	/makerString exch def string/tmpString exch def
	{
		currentfile tmpString readline not{pop exit}if
		makerString anchorsearch
		{
			pop pop cleartomark exit
		}{
			3 copy/PUT pdfmark_5 pop 2 copy(\n)/PUT pdfmark_5
		}ifelse
	}loop
	end
}bdf
/xpdfm
{
	{
		dup 0 get/Label eq
		{
			aload length[exch 1 add 1 roll/PAGELABEL
		}{
			aload pop
			[{ThisPage}<<5 -2 roll>>/PUT
		}ifelse
		pdfmark_5
	}forall
}bdf
/lmt{
	dup 2 index le{exch}if pop dup 2 index ge{exch}if pop
}bdf
/int{
	dup 2 index sub 3 index 5 index sub div 6 -2 roll sub mul exch pop add exch pop
}bdf
/ds{
	Adobe_AGM_Utils begin
}bdf
/dt{
	currentdict Adobe_AGM_Utils eq{
		end
	}if
}bdf
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_AGM_Core 2.0 0
%%Version: 2.0 0
%%Copyright: Copyright(C)1997-2007 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Core 209 dict dup begin put
/Adobe_AGM_Core_Id/Adobe_AGM_Core_2.0_0 def
/AGMCORE_str256 256 string def
/AGMCORE_save nd
/AGMCORE_graphicsave nd
/AGMCORE_c 0 def
/AGMCORE_m 0 def
/AGMCORE_y 0 def
/AGMCORE_k 0 def
/AGMCORE_cmykbuf 4 array def
/AGMCORE_screen[currentscreen]cvx def
/AGMCORE_tmp 0 def
/AGMCORE_&setgray nd
/AGMCORE_&setcolor nd
/AGMCORE_&setcolorspace nd
/AGMCORE_&setcmykcolor nd
/AGMCORE_cyan_plate nd
/AGMCORE_magenta_plate nd
/AGMCORE_yellow_plate nd
/AGMCORE_black_plate nd
/AGMCORE_plate_ndx nd
/AGMCORE_get_ink_data nd
/AGMCORE_is_cmyk_sep nd
/AGMCORE_host_sep nd
/AGMCORE_avoid_L2_sep_space nd
/AGMCORE_distilling nd
/AGMCORE_composite_job nd
/AGMCORE_producing_seps nd
/AGMCORE_ps_level -1 def
/AGMCORE_ps_version -1 def
/AGMCORE_environ_ok nd
/AGMCORE_CSD_cache 0 dict def
/AGMCORE_currentoverprint false def
/AGMCORE_deltaX nd
/AGMCORE_deltaY nd
/AGMCORE_name nd
/AGMCORE_sep_special nd
/AGMCORE_err_strings 4 dict def
/AGMCORE_cur_err nd
/AGMCORE_current_spot_alias false def
/AGMCORE_inverting false def
/AGMCORE_feature_dictCount nd
/AGMCORE_feature_opCount nd
/AGMCORE_feature_ctm nd
/AGMCORE_ConvertToProcess false def
/AGMCORE_Default_CTM matrix def
/AGMCORE_Default_PageSize nd
/AGMCORE_Default_flatness nd
/AGMCORE_currentbg nd
/AGMCORE_currentucr nd
/AGMCORE_pattern_paint_type 0 def
/knockout_unitsq nd
currentglobal true setglobal
[/CSA/Gradient/Procedure]
{
	/Generic/Category findresource dup length dict copy/Category defineresource pop
}forall
setglobal
/AGMCORE_key_known
{
	where{
		/Adobe_AGM_Core_Id known
	}{
		false
	}ifelse
}ndf
/flushinput
{
	save
	2 dict begin
	/CompareBuffer 3 -1 roll def
	/readbuffer 256 string def
	mark
	{
	currentfile readbuffer{readline}stopped
		{cleartomark mark}
		{
		not
			{pop exit}
		if
		CompareBuffer eq
			{exit}
		if
		}ifelse
	}loop
	cleartomark
	end
	restore
}bdf
/getspotfunction
{
	AGMCORE_screen exch pop exch pop
	dup type/dicttype eq{
		dup/HalftoneType get 1 eq{
			/SpotFunction get
		}{
			dup/HalftoneType get 2 eq{
				/GraySpotFunction get
			}{
				pop
				{
					abs exch abs 2 copy add 1 gt{
						1 sub dup mul exch 1 sub dup mul add 1 sub
					}{
						dup mul exch dup mul add 1 exch sub
					}ifelse
				}bind
			}ifelse
		}ifelse
	}if
}def
/np
{newpath}bdf
/clp_npth
{clip np}def
/eoclp_npth
{eoclip np}def
/npth_clp
{np clip}def
/graphic_setup
{
	/AGMCORE_graphicsave save store
	concat
	0 setgray
	0 setlinecap
	0 setlinejoin
	1 setlinewidth
	[]0 setdash
	10 setmiterlimit
	np
	false setoverprint
	false setstrokeadjust
	//Adobe_AGM_Core/spot_alias gx
	/Adobe_AGM_Image where{
		pop
		Adobe_AGM_Image/spot_alias 2 copy known{
			gx
		}{
			pop pop
		}ifelse
	}if
	/sep_colorspace_dict null AGMCORE_gput
	100 dict begin
	/dictstackcount countdictstack def
	/showpage{}def
	mark
}def
/graphic_cleanup
{
	cleartomark
	dictstackcount 1 countdictstack 1 sub{end}for
	end
	AGMCORE_graphicsave restore
}def
/compose_error_msg
{
	grestoreall initgraphics	
	/Helvetica findfont 10 scalefont setfont
	/AGMCORE_deltaY 100 def
	/AGMCORE_deltaX 310 def
	clippath pathbbox np pop pop 36 add exch 36 add exch moveto
	0 AGMCORE_deltaY rlineto AGMCORE_deltaX 0 rlineto
	0 AGMCORE_deltaY neg rlineto AGMCORE_deltaX neg 0 rlineto closepath
	0 AGMCORE_&setgray
	gsave 1 AGMCORE_&setgray fill grestore 
	1 setlinewidth gsave stroke grestore
	currentpoint AGMCORE_deltaY 15 sub add exch 8 add exch moveto
	/AGMCORE_deltaY 12 def
	/AGMCORE_tmp 0 def
	AGMCORE_err_strings exch get
		{
		dup 32 eq
			{
			pop
			AGMCORE_str256 0 AGMCORE_tmp getinterval
			stringwidth pop currentpoint pop add AGMCORE_deltaX 28 add gt
				{
				currentpoint AGMCORE_deltaY sub exch pop
				clippath pathbbox pop pop pop 44 add exch moveto
				}if
			AGMCORE_str256 0 AGMCORE_tmp getinterval show( )show
			0 1 AGMCORE_str256 length 1 sub
				{
				AGMCORE_str256 exch 0 put
				}for
			/AGMCORE_tmp 0 def
			}{
				AGMCORE_str256 exch AGMCORE_tmp xpt
				/AGMCORE_tmp AGMCORE_tmp 1 add def
			}ifelse
		}forall
}bdf
/AGMCORE_CMYKDeviceNColorspaces[
	[/Separation/None/DeviceCMYK{0 0 0}]
	[/Separation(Black)/DeviceCMYK{0 0 0 4 -1 roll}bind]
	[/Separation(Yellow)/DeviceCMYK{0 0 3 -1 roll 0}bind]
	[/DeviceN[(Yellow)(Black)]/DeviceCMYK{0 0 4 2 roll}bind]
	[/Separation(Magenta)/DeviceCMYK{0 exch 0 0}bind]
	[/DeviceN[(Magenta)(Black)]/DeviceCMYK{0 3 1 roll 0 exch}bind]
	[/DeviceN[(Magenta)(Yellow)]/DeviceCMYK{0 3 1 roll 0}bind]
	[/DeviceN[(Magenta)(Yellow)(Black)]/DeviceCMYK{0 4 1 roll}bind]
	[/Separation(Cyan)/DeviceCMYK{0 0 0}]
	[/DeviceN[(Cyan)(Black)]/DeviceCMYK{0 0 3 -1 roll}bind]
	[/DeviceN[(Cyan)(Yellow)]/DeviceCMYK{0 exch 0}bind]
	[/DeviceN[(Cyan)(Yellow)(Black)]/DeviceCMYK{0 3 1 roll}bind]
	[/DeviceN[(Cyan)(Magenta)]/DeviceCMYK{0 0}]
	[/DeviceN[(Cyan)(Magenta)(Black)]/DeviceCMYK{0 exch}bind]
	[/DeviceN[(Cyan)(Magenta)(Yellow)]/DeviceCMYK{0}]
	[/DeviceCMYK]
]def
/ds{
	Adobe_AGM_Core begin
	/currentdistillerparams where
		{
		pop currentdistillerparams/CoreDistVersion get 5000 lt
			{<</DetectBlends false>>setdistillerparams}if
		}if	
	/AGMCORE_ps_version xdf
	/AGMCORE_ps_level xdf
	errordict/AGM_handleerror known not{
		errordict/AGM_handleerror errordict/handleerror get put
		errordict/handleerror{
			Adobe_AGM_Core begin
			$error/newerror get AGMCORE_cur_err null ne and{
				$error/newerror false put
				AGMCORE_cur_err compose_error_msg
			}if
			$error/newerror true put
			end
			errordict/AGM_handleerror get exec
			}bind put
		}if
	/AGMCORE_environ_ok 
		ps_level AGMCORE_ps_level ge
		ps_version AGMCORE_ps_version ge and 
		AGMCORE_ps_level -1 eq or
	def
	AGMCORE_environ_ok not
		{/AGMCORE_cur_err/AGMCORE_bad_environ def}if
	/AGMCORE_&setgray systemdict/setgray get def
	level2{
		/AGMCORE_&setcolor systemdict/setcolor get def
		/AGMCORE_&setcolorspace systemdict/setcolorspace get def
	}if
	/AGMCORE_currentbg currentblackgeneration def
	/AGMCORE_currentucr currentundercolorremoval def
	/AGMCORE_Default_flatness currentflat def
	/AGMCORE_distilling
		/product where{
			pop systemdict/setdistillerparams known product(Adobe PostScript Parser)ne and
		}{
			false
		}ifelse
	def
	/AGMCORE_GSTATE AGMCORE_key_known not{
		/AGMCORE_GSTATE 21 dict def
		/AGMCORE_tmpmatrix matrix def
		/AGMCORE_gstack 64 array def
		/AGMCORE_gstackptr 0 def
		/AGMCORE_gstacksaveptr 0 def
		/AGMCORE_gstackframekeys 14 def
		/AGMCORE_&gsave/gsave ldf
		/AGMCORE_&grestore/grestore ldf
		/AGMCORE_&grestoreall/grestoreall ldf
		/AGMCORE_&save/save ldf
		/AGMCORE_&setoverprint/setoverprint ldf
		/AGMCORE_gdictcopy{
			begin
			{def}forall
			end
		}def
		/AGMCORE_gput{
			AGMCORE_gstack AGMCORE_gstackptr get
			3 1 roll
			put
		}def
		/AGMCORE_gget{
			AGMCORE_gstack AGMCORE_gstackptr get
			exch
			get
		}def
		/gsave{
			AGMCORE_&gsave
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/grestore{
			AGMCORE_&grestore
			AGMCORE_gstackptr 1 sub
			dup AGMCORE_gstacksaveptr lt{1 add}if
			dup AGMCORE_gstack exch get dup/AGMCORE_currentoverprint known
				{/AGMCORE_currentoverprint get setoverprint}{pop}ifelse
			/AGMCORE_gstackptr exch store
		}def
		/grestoreall{
			AGMCORE_&grestoreall
			/AGMCORE_gstackptr AGMCORE_gstacksaveptr store 
		}def
		/save{
			AGMCORE_&save
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gstackptr 1 add
			dup 64 ge{limitcheck}if
			/AGMCORE_gstackptr exch store
			/AGMCORE_gstacksaveptr AGMCORE_gstackptr store
			AGMCORE_gstack AGMCORE_gstackptr get
			AGMCORE_gdictcopy
		}def
		/setoverprint{
			dup/AGMCORE_currentoverprint exch AGMCORE_gput AGMCORE_&setoverprint
		}def	
		0 1 AGMCORE_gstack length 1 sub{
				AGMCORE_gstack exch AGMCORE_gstackframekeys dict put
		}for
	}if
	level3/AGMCORE_&sysshfill AGMCORE_key_known not and
	{
		/AGMCORE_&sysshfill systemdict/shfill get def
		/AGMCORE_&sysmakepattern systemdict/makepattern get def
		/AGMCORE_&usrmakepattern/makepattern load def
	}if
	/currentcmykcolor[0 0 0 0]AGMCORE_gput
	/currentstrokeadjust false AGMCORE_gput
	/currentcolorspace[/DeviceGray]AGMCORE_gput
	/sep_tint 0 AGMCORE_gput
	/devicen_tints[0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0]AGMCORE_gput
	/sep_colorspace_dict null AGMCORE_gput
	/devicen_colorspace_dict null AGMCORE_gput
	/indexed_colorspace_dict null AGMCORE_gput
	/currentcolor_intent()AGMCORE_gput
	/customcolor_tint 1 AGMCORE_gput
	/absolute_colorimetric_crd null AGMCORE_gput
	/relative_colorimetric_crd null AGMCORE_gput
	/saturation_crd null AGMCORE_gput
	/perceptual_crd null AGMCORE_gput
	currentcolortransfer cvlit/AGMCore_gray_xfer xdf cvlit/AGMCore_b_xfer xdf
		 cvlit/AGMCore_g_xfer xdf cvlit/AGMCore_r_xfer xdf
	<<
	/MaxPatternItem currentsystemparams/MaxPatternCache get
	>>
	setuserparams
	end
}def
/ps
{
	/setcmykcolor where{
		pop
		Adobe_AGM_Core/AGMCORE_&setcmykcolor/setcmykcolor load put
	}if
	Adobe_AGM_Core begin
	/setcmykcolor
	{
		4 copy AGMCORE_cmykbuf astore/currentcmykcolor exch AGMCORE_gput
		1 sub 4 1 roll
		3{
			3 index add neg dup 0 lt{
				pop 0
			}if
			3 1 roll
		}repeat
		setrgbcolor pop
	}ndf
	/currentcmykcolor
	{
		/currentcmykcolor AGMCORE_gget aload pop
	}ndf
	/setoverprint
	{pop}ndf
	/currentoverprint
	{false}ndf
	/AGMCORE_cyan_plate 1 0 0 0 test_cmyk_color_plate def
	/AGMCORE_magenta_plate 0 1 0 0 test_cmyk_color_plate def
	/AGMCORE_yellow_plate 0 0 1 0 test_cmyk_color_plate def
	/AGMCORE_black_plate 0 0 0 1 test_cmyk_color_plate def
	/AGMCORE_plate_ndx 
		AGMCORE_cyan_plate{
			0
		}{
			AGMCORE_magenta_plate{
				1
			}{
				AGMCORE_yellow_plate{
					2
				}{
					AGMCORE_black_plate{
						3
					}{
						4
					}ifelse
				}ifelse
			}ifelse
		}ifelse
		def
	/AGMCORE_have_reported_unsupported_color_space false def
	/AGMCORE_report_unsupported_color_space
	{
		AGMCORE_have_reported_unsupported_color_space false eq
		{
			(Warning: Job contains content that cannot be separated with on-host methods. This content appears on the black plate, and knocks out all other plates.)==
			Adobe_AGM_Core/AGMCORE_have_reported_unsupported_color_space true ddf
		}if
	}def
	/AGMCORE_composite_job
		AGMCORE_cyan_plate AGMCORE_magenta_plate and AGMCORE_yellow_plate and AGMCORE_black_plate and def
	/AGMCORE_in_rip_sep
		/AGMCORE_in_rip_sep where{
			pop AGMCORE_in_rip_sep
		}{
			AGMCORE_distilling 
			{
				false
			}{
				userdict/Adobe_AGM_OnHost_Seps known{
					false
				}{
					level2{
						currentpagedevice/Separations 2 copy known{
							get
						}{
							pop pop false
						}ifelse
					}{
						false
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	def
	/AGMCORE_producing_seps AGMCORE_composite_job not AGMCORE_in_rip_sep or def
	/AGMCORE_host_sep AGMCORE_producing_seps AGMCORE_in_rip_sep not and def
	/AGM_preserve_spots 
		/AGM_preserve_spots where{
			pop AGM_preserve_spots
		}{
			AGMCORE_distilling AGMCORE_producing_seps or
		}ifelse
	def
	/AGM_is_distiller_preserving_spotimages
	{
		currentdistillerparams/PreserveOverprintSettings known
		{
			currentdistillerparams/PreserveOverprintSettings get
				{
					currentdistillerparams/ColorConversionStrategy known
					{
						currentdistillerparams/ColorConversionStrategy get
						/sRGB ne
					}{
						true
					}ifelse
				}{
					false
				}ifelse
		}{
			false
		}ifelse
	}def
	/convert_spot_to_process where{pop}{
		/convert_spot_to_process
		{
			//Adobe_AGM_Core begin
			dup map_alias{
				/Name get exch pop
			}if
			dup dup(None)eq exch(All)eq or
				{
				pop false
				}{
				AGMCORE_host_sep
				{
					gsave
					1 0 0 0 setcmykcolor currentgray 1 exch sub
					0 1 0 0 setcmykcolor currentgray 1 exch sub
					0 0 1 0 setcmykcolor currentgray 1 exch sub
					0 0 0 1 setcmykcolor currentgray 1 exch sub
					add add add 0 eq
					{
						pop false
					}{
						false setoverprint
						current_spot_alias false set_spot_alias
						1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
						set_spot_alias
						currentgray 1 ne
					}ifelse
					grestore
				}{
					AGMCORE_distilling
					{
						pop AGM_is_distiller_preserving_spotimages not
					}{
						//Adobe_AGM_Core/AGMCORE_name xddf
						false
						//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 0 eq
						AGMUTIL_cpd/OverrideSeparations known and
						{
							AGMUTIL_cpd/OverrideSeparations get
							{
								/HqnSpots/ProcSet resourcestatus
								{
									pop pop pop true
								}if
							}if
						}if					
						{
							AGMCORE_name/HqnSpots/ProcSet findresource/TestSpot gx not
						}{
							gsave
							[/Separation AGMCORE_name/DeviceGray{}]AGMCORE_&setcolorspace
							false
							AGMUTIL_cpd/SeparationColorNames 2 copy known
							{
								get
								{AGMCORE_name eq or}forall
								not
							}{
								pop pop pop true
							}ifelse
							grestore
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			end
		}def
	}ifelse
	/convert_to_process where{pop}{
		/convert_to_process
		{
			dup length 0 eq
				{
				pop false
				}{
				AGMCORE_host_sep
				{
				dup true exch
					{
					dup(Cyan)eq exch
					dup(Magenta)eq 3 -1 roll or exch
					dup(Yellow)eq 3 -1 roll or exch
					dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process and}ifelse
					}
				forall
					{
					true exch
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						(Black)eq or and
						}forall
						not
					}{pop false}ifelse
				}{
				false exch
					{
					/PhotoshopDuotoneList where{pop false}{true}ifelse
						{
						dup(Cyan)eq exch
						dup(Magenta)eq 3 -1 roll or exch
						dup(Yellow)eq 3 -1 roll or exch
						dup(Black)eq 3 -1 roll or
						{pop}
						{convert_spot_to_process or}ifelse
						}
						{
						convert_spot_to_process or
						}
					ifelse
					}
				forall
				}ifelse
			}ifelse
		}def
	}ifelse	
	/AGMCORE_avoid_L2_sep_space 
		version cvr 2012 lt 
		level2 and 
		AGMCORE_producing_seps not and
	def
	/AGMCORE_is_cmyk_sep
		AGMCORE_cyan_plate AGMCORE_magenta_plate or AGMCORE_yellow_plate or AGMCORE_black_plate or
	def
	/AGM_avoid_0_cmyk where{
		pop AGM_avoid_0_cmyk
	}{
		AGM_preserve_spots 
		userdict/Adobe_AGM_OnHost_Seps known 
		userdict/Adobe_AGM_InRip_Seps known or
		not and
	}ifelse
	{
		/setcmykcolor[
			{
				4 copy add add add 0 eq currentoverprint and{
					pop 0.0005
				}if
			}/exec cvx
			/AGMCORE_&setcmykcolor load dup type/operatortype ne{
				/exec cvx
			}if
		]cvx def
	}if
	/AGMCORE_IsSeparationAProcessColor
		{
		dup(Cyan)eq exch dup(Magenta)eq exch dup(Yellow)eq exch(Black)eq or or or
		}def
	AGMCORE_host_sep{
		/setcolortransfer
		{
			AGMCORE_cyan_plate{
				pop pop pop
			}{
			 	AGMCORE_magenta_plate{
			 		4 3 roll pop pop pop
			 	}{
			 		AGMCORE_yellow_plate{
			 			4 2 roll pop pop pop
			 		}{
			 			4 1 roll pop pop pop
			 		}ifelse
			 	}ifelse
			}ifelse
			settransfer 
		}	
		def
		/AGMCORE_get_ink_data
			AGMCORE_cyan_plate{
				{pop pop pop}
			}{
			 	AGMCORE_magenta_plate{
			 		{4 3 roll pop pop pop}
			 	}{
			 		AGMCORE_yellow_plate{
			 			{4 2 roll pop pop pop}
			 		}{
			 			{4 1 roll pop pop pop}
			 		}ifelse
			 	}ifelse
			}ifelse
		def
		/AGMCORE_RemoveProcessColorNames
			{
			1 dict begin
			/filtername
				{
				dup/Cyan eq 1 index(Cyan)eq or
					{pop(_cyan_)}if
				dup/Magenta eq 1 index(Magenta)eq or
					{pop(_magenta_)}if
				dup/Yellow eq 1 index(Yellow)eq or
					{pop(_yellow_)}if
				dup/Black eq 1 index(Black)eq or
					{pop(_black_)}if
				}def
			dup type/arraytype eq
				{[exch{filtername}forall]}
				{filtername}ifelse
			end
			}def
		level3{
			/AGMCORE_IsCurrentColor
				{
				dup AGMCORE_IsSeparationAProcessColor
					{
					AGMCORE_plate_ndx 0 eq
						{dup(Cyan)eq exch/Cyan eq or}if
					AGMCORE_plate_ndx 1 eq
						{dup(Magenta)eq exch/Magenta eq or}if
					AGMCORE_plate_ndx 2 eq
						{dup(Yellow)eq exch/Yellow eq or}if
					AGMCORE_plate_ndx 3 eq
						{dup(Black)eq exch/Black eq or}if
					AGMCORE_plate_ndx 4 eq
						{pop false}if
					}{
					gsave
					false setoverprint
					current_spot_alias false set_spot_alias
					1 1 1 1 6 -1 roll findcmykcustomcolor 1 setcustomcolor
					set_spot_alias
					currentgray 1 ne
					grestore
					}ifelse
				}def
			/AGMCORE_filter_functiondatasource
				{	
				5 dict begin
				/data_in xdf
				data_in type/stringtype eq
					{
					/ncomp xdf
					/comp xdf
					/string_out data_in length ncomp idiv string def
					0 ncomp data_in length 1 sub
						{
						string_out exch dup ncomp idiv exch data_in exch ncomp getinterval comp get 255 exch sub put
						}for
					string_out
					}{
					string/string_in xdf
					/string_out 1 string def
					/component xdf
					[
					data_in string_in/readstring cvx
						[component/get cvx 255/exch cvx/sub cvx string_out/exch cvx 0/exch cvx/put cvx string_out]cvx
						[/pop cvx()]cvx/ifelse cvx
					]cvx/ReusableStreamDecode filter
				}ifelse
				end
				}def
			/AGMCORE_separateShadingFunction
				{
				2 dict begin
				/paint? xdf
				/channel xdf
				dup type/dicttype eq
					{
					begin
					FunctionType 0 eq
						{
						/DataSource channel Range length 2 idiv DataSource AGMCORE_filter_functiondatasource def
						currentdict/Decode known
							{/Decode Decode channel 2 mul 2 getinterval def}if
						paint? not
							{/Decode[1 1]def}if
						}if
					FunctionType 2 eq
						{
						paint?
							{
							/C0[C0 channel get 1 exch sub]def
							/C1[C1 channel get 1 exch sub]def
							}{
							/C0[1]def
							/C1[1]def
							}ifelse			
						}if
					FunctionType 3 eq
						{
						/Functions[Functions{channel paint? AGMCORE_separateShadingFunction}forall]def			
						}if
					currentdict/Range known
						{/Range[0 1]def}if
					currentdict
					end}{
					channel get 0 paint? AGMCORE_separateShadingFunction
					}ifelse
				end
				}def
			/AGMCORE_separateShading
				{
				3 -1 roll begin
				currentdict/Function known
					{
					currentdict/Background known
						{[1 index{Background 3 index get 1 exch sub}{1}ifelse]/Background xdf}if
					Function 3 1 roll AGMCORE_separateShadingFunction/Function xdf
					/ColorSpace[/DeviceGray]def
					}{
					ColorSpace dup type/arraytype eq{0 get}if/DeviceCMYK eq
						{
						/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
						}{
						ColorSpace dup 1 get AGMCORE_RemoveProcessColorNames 1 exch put
						}ifelse
					ColorSpace 0 get/Separation eq
						{
							{
								[1/exch cvx/sub cvx]cvx
							}{
								[/pop cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll put
							pop
						}{
							{
								[exch ColorSpace 1 get length 1 sub exch sub/index cvx 1/exch cvx/sub cvx ColorSpace 1 get length 1 add 1/roll cvx ColorSpace 1 get length{/pop cvx}repeat]cvx
							}{
								pop[ColorSpace 1 get length{/pop cvx}repeat cvx 1]cvx
							}ifelse
							ColorSpace 3 3 -1 roll bind put
						}ifelse
					ColorSpace 2/DeviceGray put																		
					}ifelse
				end
				}def
			/AGMCORE_separateShadingDict
				{
				dup/ColorSpace get
				dup type/arraytype ne
					{[exch]}if
				dup 0 get/DeviceCMYK eq
					{
					exch begin 
					currentdict
					AGMCORE_cyan_plate
						{0 true}if
					AGMCORE_magenta_plate
						{1 true}if
					AGMCORE_yellow_plate
						{2 true}if
					AGMCORE_black_plate
						{3 true}if
					AGMCORE_plate_ndx 4 eq
						{0 false}if		
					dup not currentoverprint and
						{/AGMCORE_ignoreshade true def}if
					AGMCORE_separateShading
					currentdict
					end exch
					}if
				dup 0 get/Separation eq
					{
					exch begin
					ColorSpace 1 get dup/None ne exch/All ne and
						{
						ColorSpace 1 get AGMCORE_IsCurrentColor AGMCORE_plate_ndx 4 lt and ColorSpace 1 get AGMCORE_IsSeparationAProcessColor not and
							{
							ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
								{
								/ColorSpace
									[
									/Separation
									ColorSpace 1 get
									/DeviceGray
										[
										ColorSpace 3 get/exec cvx
										4 AGMCORE_plate_ndx sub -1/roll cvx
										4 1/roll cvx
										3[/pop cvx]cvx/repeat cvx
										1/exch cvx/sub cvx
										]cvx									
									]def
								}{
								AGMCORE_report_unsupported_color_space
								AGMCORE_black_plate not
									{
									currentdict 0 false AGMCORE_separateShading
									}if
								}ifelse
							}{
							currentdict ColorSpace 1 get AGMCORE_IsCurrentColor
							0 exch 
							dup not currentoverprint and
								{/AGMCORE_ignoreshade true def}if
							AGMCORE_separateShading
							}ifelse	
						}if			
					currentdict
					end exch
					}if
				dup 0 get/DeviceN eq
					{
					exch begin
					ColorSpace 1 get convert_to_process
						{
						ColorSpace 2 get dup type/arraytype eq{0 get}if/DeviceCMYK eq 
							{
							/ColorSpace
								[
								/DeviceN
								ColorSpace 1 get
								/DeviceGray
									[
									ColorSpace 3 get/exec cvx
									4 AGMCORE_plate_ndx sub -1/roll cvx
									4 1/roll cvx
									3[/pop cvx]cvx/repeat cvx
									1/exch cvx/sub cvx
									]cvx									
								]def
							}{
							AGMCORE_report_unsupported_color_space
							AGMCORE_black_plate not
								{
								currentdict 0 false AGMCORE_separateShading
								/ColorSpace[/DeviceGray]def
								}if
							}ifelse
						}{
						currentdict
						false -1 ColorSpace 1 get
							{
							AGMCORE_IsCurrentColor
								{
								1 add
								exch pop true exch exit
								}if
							1 add
							}forall
						exch 
						dup not currentoverprint and
							{/AGMCORE_ignoreshade true def}if
						AGMCORE_separateShading
						}ifelse
					currentdict
					end exch
					}if
				dup 0 get dup/DeviceCMYK eq exch dup/Separation eq exch/DeviceN eq or or not
					{
					exch begin
					ColorSpace dup type/arraytype eq
						{0 get}if
					/DeviceGray ne
						{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate not
							{
							ColorSpace 0 get/CIEBasedA eq
								{
								/ColorSpace[/Separation/_ciebaseda_/DeviceGray{}]def
								}if
							ColorSpace 0 get dup/CIEBasedABC eq exch dup/CIEBasedDEF eq exch/DeviceRGB eq or or
								{
								/ColorSpace[/DeviceN[/_red_/_green_/_blue_]/DeviceRGB{}]def
								}if
							ColorSpace 0 get/CIEBasedDEFG eq
								{
								/ColorSpace[/DeviceN[/_cyan_/_magenta_/_yellow_/_black_]/DeviceCMYK{}]def
								}if
							currentdict 0 false AGMCORE_separateShading
							}if
						}if
					currentdict
					end exch
					}if
				pop
				dup/AGMCORE_ignoreshade known
					{
					begin
					/ColorSpace[/Separation(None)/DeviceGray{}]def
					currentdict end
					}if
				}def
			/shfill
				{
				AGMCORE_separateShadingDict 
				dup/AGMCORE_ignoreshade known
					{pop}
					{AGMCORE_&sysshfill}ifelse
				}def
			/makepattern
				{
				exch
				dup/PatternType get 2 eq
					{
					clonedict
					begin
					/Shading Shading AGMCORE_separateShadingDict def
					Shading/AGMCORE_ignoreshade known
					currentdict end exch
					{pop<</PatternType 1/PaintProc{pop}/BBox[0 0 1 1]/XStep 1/YStep 1/PaintType 1/TilingType 3>>}if
					exch AGMCORE_&sysmakepattern
					}{
					exch AGMCORE_&usrmakepattern
					}ifelse
				}def
		}if
	}if
	AGMCORE_in_rip_sep{
		/setcustomcolor
		{
			exch aload pop
			dup 7 1 roll inRip_spot_has_ink not	{
				4{4 index mul 4 1 roll}
				repeat
				/DeviceCMYK setcolorspace
				6 -2 roll pop pop
			}{
				//Adobe_AGM_Core begin
					/AGMCORE_k xdf/AGMCORE_y xdf/AGMCORE_m xdf/AGMCORE_c xdf
				end
				[/Separation 4 -1 roll/DeviceCMYK
				{dup AGMCORE_c mul exch dup AGMCORE_m mul exch dup AGMCORE_y mul exch AGMCORE_k mul}
				]
				setcolorspace
			}ifelse
			setcolor
		}ndf
		/setseparationgray
		{
			[/Separation(All)/DeviceGray{}]setcolorspace_opt
			1 exch sub setcolor
		}ndf
	}{
		/setseparationgray
		{
			AGMCORE_&setgray
		}ndf
	}ifelse
	/findcmykcustomcolor
	{
		5 makereadonlyarray
	}ndf
	/setcustomcolor
	{
		exch aload pop pop
		4{4 index mul 4 1 roll}repeat
		setcmykcolor pop
	}ndf
	/has_color
		/colorimage where{
			AGMCORE_producing_seps{
				pop true
			}{
				systemdict eq
			}ifelse
		}{
			false
		}ifelse
	def
	/map_index
	{
		1 index mul exch getinterval{255 div}forall
	}bdf
	/map_indexed_devn
	{
		Lookup Names length 3 -1 roll cvi map_index
	}bdf
	/n_color_components
	{
		base_colorspace_type
		dup/DeviceGray eq{
			pop 1
		}{
			/DeviceCMYK eq{
				4
			}{
				3
			}ifelse
		}ifelse
	}bdf
	level2{
		/mo/moveto ldf
		/li/lineto ldf
		/cv/curveto ldf
		/knockout_unitsq
		{
			1 setgray
			0 0 1 1 rectfill
		}def
		level2/setcolorspace AGMCORE_key_known not and{
			/AGMCORE_&&&setcolorspace/setcolorspace ldf
			/AGMCORE_ReplaceMappedColor
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					/AGMCORE_SpotAliasAry2 where{
						begin
						dup 0 get dup/Separation eq
						{
							pop
							dup length array copy
							dup dup 1 get
							current_spot_alias
							{
								dup map_alias
								{
									false set_spot_alias
									dup 1 exch setsepcolorspace
									true set_spot_alias
									begin
									/sep_colorspace_dict currentdict AGMCORE_gput
									pop pop	pop
									[
										/Separation Name 
										CSA map_csa
										MappedCSA 
										/sep_colorspace_proc load
									]
									dup Name
									end
								}if
							}if
							map_reserved_ink_name 1 xpt
						}{
							/DeviceN eq 
							{
								dup length array copy
								dup dup 1 get[
									exch{
										current_spot_alias{
											dup map_alias{
												/Name get exch pop
											}if
										}if
										map_reserved_ink_name
									}forall 
								]1 xpt
							}if
						}ifelse
						end
					}if
				}if
			}def
			/setcolorspace
			{
				dup type dup/arraytype eq exch/packedarraytype eq or
				{
					dup 0 get/Indexed eq
					{
						AGMCORE_distilling
						{
							/PhotoshopDuotoneList where
							{
								pop false
							}{
								true
							}ifelse
						}{
							true
						}ifelse
						{
							aload pop 3 -1 roll
							AGMCORE_ReplaceMappedColor
							3 1 roll 4 array astore
						}if
					}{
						AGMCORE_ReplaceMappedColor
					}ifelse
				}if
				DeviceN_PS2_inRip_seps{AGMCORE_&&&setcolorspace}if
			}def
		}if	
	}{
		/adj
		{
			currentstrokeadjust{
				transform
				0.25 sub round 0.25 add exch
				0.25 sub round 0.25 add exch
				itransform
			}if
		}def
		/mo{
			adj moveto
		}def
		/li{
			adj lineto
		}def
		/cv{
			6 2 roll adj
			6 2 roll adj
			6 2 roll adj curveto
		}def
		/knockout_unitsq
		{
			1 setgray
			8 8 1[8 0 0 8 0 0]{<ffffffffffffffff>}image
		}def
		/currentstrokeadjust{
			/currentstrokeadjust AGMCORE_gget
		}def
		/setstrokeadjust{
			/currentstrokeadjust exch AGMCORE_gput
		}def
		/setcolorspace
		{
			/currentcolorspace exch AGMCORE_gput
		}def
		/currentcolorspace
		{
			/currentcolorspace AGMCORE_gget
		}def
		/setcolor_devicecolor
		{
			base_colorspace_type
			dup/DeviceGray eq{
				pop setgray
			}{
				/DeviceCMYK eq{
					setcmykcolor
				}{
					setrgbcolor
				}ifelse
			}ifelse
		}def
		/setcolor
		{
			currentcolorspace 0 get
			dup/DeviceGray ne{
				dup/DeviceCMYK ne{
					dup/DeviceRGB ne{
						dup/Separation eq{
							pop
							currentcolorspace 3 gx
							currentcolorspace 2 get
						}{
							dup/Indexed eq{
								pop
								currentcolorspace 3 get dup type/stringtype eq{
									currentcolorspace 1 get n_color_components
									3 -1 roll map_index
								}{
									exec
								}ifelse
								currentcolorspace 1 get
							}{
								/AGMCORE_cur_err/AGMCORE_invalid_color_space def
								AGMCORE_invalid_color_space
							}ifelse
						}ifelse
					}if
				}if
			}if
			setcolor_devicecolor
		}def
	}ifelse
	/sop/setoverprint ldf
	/lw/setlinewidth ldf
	/lc/setlinecap ldf
	/lj/setlinejoin ldf
	/ml/setmiterlimit ldf
	/dsh/setdash ldf
	/sadj/setstrokeadjust ldf
	/gry/setgray ldf
	/rgb/setrgbcolor ldf
	/cmyk[
		/currentcolorspace[/DeviceCMYK]/AGMCORE_gput cvx
		/setcmykcolor load dup type/operatortype ne{/exec cvx}if
	]cvx bdf
	level3 AGMCORE_host_sep not and{
		/nzopmsc{
			6 dict begin
			/kk exch def
			/yy exch def
			/mm exch def
			/cc exch def
			/sum 0 def
			cc 0 ne{/sum sum 2#1000 or def cc}if
			mm 0 ne{/sum sum 2#0100 or def mm}if
			yy 0 ne{/sum sum 2#0010 or def yy}if
			kk 0 ne{/sum sum 2#0001 or def kk}if
			AGMCORE_CMYKDeviceNColorspaces sum get setcolorspace
			sum 0 eq{0}if
			end
			setcolor
		}bdf
	}{
		/nzopmsc/cmyk ldf
	}ifelse
	/sep/setsepcolor ldf
	/devn/setdevicencolor ldf
	/idx/setindexedcolor ldf
	/colr/setcolor ldf
	/csacrd/set_csa_crd ldf
	/sepcs/setsepcolorspace ldf
	/devncs/setdevicencolorspace ldf
	/idxcs/setindexedcolorspace ldf
	/cp/closepath ldf
	/clp/clp_npth ldf
	/eclp/eoclp_npth ldf
	/f/fill ldf
	/ef/eofill ldf
	/@/stroke ldf
	/nclp/npth_clp ldf
	/gset/graphic_setup ldf
	/gcln/graphic_cleanup ldf
	/ct/concat ldf
	/cf/currentfile ldf
	/fl/filter ldf
	/rs/readstring ldf
	/AGMCORE_def_ht currenthalftone def
	/clonedict Adobe_AGM_Utils begin/clonedict load end def
	/clonearray Adobe_AGM_Utils begin/clonearray load end def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
	/getrampcolor
	{
		/indx exch def
		0 1 NumComp 1 sub
		{
			dup
			Samples exch get
			dup type/stringtype eq{indx get}if
			exch
			Scaling exch get aload pop
			3 1 roll
			mul add
		}for
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse
	}bdf
	/sssetbackground{
		aload pop 
		ColorSpaceFamily/Separation eq 
		{sep}
		{
			ColorSpaceFamily/DeviceN eq
			{devn}{setcolor}ifelse
		}ifelse	
	}bdf
	/RadialShade
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/r2 xdf
		/c2y xdf
		/c2x xdf
		/r1 xdf
		/c1y xdf
		/c1x xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		c1x c2x eq
		{
			c1y c2y lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope c2y c1y sub c2x c1x sub div def
			/theta slope 1 atan def
			c2x c1x lt c2y c1y ge and{/theta theta 180 sub def}if
			c2x c1x lt c2y c1y lt and{/theta theta 180 add def}if
		}ifelse
		gsave
		clippath
		c1x c1y translate
		theta rotate
		-90 rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax xdf
		/xMax xdf
		/yMin xdf
		/xMin xdf
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			/max{2 copy gt{pop}{exch pop}ifelse}bdf
			/min{2 copy lt{pop}{exch pop}ifelse}bdf
			rampdict begin
			40 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			c1x c1y translate
			theta rotate
			-90 rotate
			/c2y c1x c2x sub dup mul c1y c2y sub dup mul add sqrt def
			/c1y 0 def
			/c1x 0 def
			/c2x 0 def
			ext0
			{
				0 getrampcolor
				c2y r2 add r1 sub 0.0001 lt
				{
					c1x c1y r1 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2y r1 add r2 le
					{
						c1x c1y r1 0 360 arc
						fill
					}
					{
						c2x c2y r2 0 360 arc fill
						r1 r2 eq
						{
							/p1x r1 neg def
							/p1y c1y def
							/p2x r1 def
							/p2y c1y def
							p1x p1y moveto p2x p2y lineto p2x yMin lineto p1x yMin lineto
							fill
						}{
							/AA r2 r1 sub c2y div def
							AA -1 eq
							{/theta 89.99 def}
							{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
							ifelse
							/SS1 90 theta add dup sin exch cos div def
							/p1x r1 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
							/p1y p1x SS1 div neg def
							/SS2 90 theta sub dup sin exch cos div def
							/p2x r1 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
							/p2y p2x SS2 div neg def
							r1 r2 gt
							{
								/L1maxX p1x yMin p1y sub SS1 div add def
								/L2maxX p2x yMin p2y sub SS2 div add def
							}{
								/L1maxX 0 def
								/L2maxX 0 def
							}ifelse
							p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
							L1maxX L1maxX p1x sub SS1 mul p1y add lineto
							fill
						}ifelse
					}ifelse
				}ifelse
			}if
		c1x c2x sub dup mul
		c1y c2y sub dup mul
		add 0.5 exp
		0 dtransform
		dup mul exch dup mul add 0.5 exp 72 div
		0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
		1 index 1 index lt{exch}if pop
		/hires xdf
		hires mul
		/numpix xdf
		/numsteps NumSamples def
		/rampIndxInc 1 def
		/subsampling false def
		numpix 0 ne
		{
			NumSamples numpix div 0.5 gt
			{
				/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
				/rampIndxInc NumSamples 1 sub numsteps div def
				/subsampling true def
			}if
		}if
		/xInc c2x c1x sub numsteps div def
		/yInc c2y c1y sub numsteps div def
		/rInc r2 r1 sub numsteps div def
		/cx c1x def
		/cy c1y def
		/radius r1 def
		np
		xInc 0 eq yInc 0 eq rInc 0 eq and and
		{
			0 getrampcolor
			cx cy radius 0 360 arc
			stroke
			NumSamples 1 sub getrampcolor
			cx cy radius 72 hires div add 0 360 arc
			0 setlinewidth
			stroke
		}{
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				cx cy radius 0 360 arc
				/cx cx xInc add def
				/cy cy yInc add def
				/radius radius rInc add def
				cx cy radius 360 0 arcn
				eofill
				rampIndxInc add
			}repeat
			pop
		}ifelse
		ext1
		{
			c2y r2 add r1 lt
			{
				c2x c2y r2 0 360 arc
				fill
			}{
				c2y r1 add r2 sub 0.0001 le
				{
					c2x c2y r2 360 0 arcn
					pathbbox
					/aymax exch def
					/axmax exch def
					/aymin exch def
					/axmin exch def
					/bxMin xMin axmin min def
					/byMin yMin aymin min def
					/bxMax xMax axmax max def
					/byMax yMax aymax max def
					bxMin byMin moveto
					bxMax byMin lineto
					bxMax byMax lineto
					bxMin byMax lineto
					bxMin byMin lineto
					eofill
				}{
					c2x c2y r2 0 360 arc fill
					r1 r2 eq
					{
						/p1x r2 neg def
						/p1y c2y def
						/p2x r2 def
						/p2y c2y def
						p1x p1y moveto p2x p2y lineto p2x yMax lineto p1x yMax lineto
						fill
					}{
						/AA r2 r1 sub c2y div def
						AA -1 eq
						{/theta 89.99 def}
						{/theta AA 1 AA dup mul sub sqrt div 1 atan def}
						ifelse
						/SS1 90 theta add dup sin exch cos div def
						/p1x r2 SS1 SS1 mul SS1 SS1 mul 1 add div sqrt mul neg def
						/p1y c2y p1x SS1 div sub def
						/SS2 90 theta sub dup sin exch cos div def
						/p2x r2 SS2 SS2 mul SS2 SS2 mul 1 add div sqrt mul def
						/p2y c2y p2x SS2 div sub def
						r1 r2 lt
						{
							/L1maxX p1x yMax p1y sub SS1 div add def
							/L2maxX p2x yMax p2y sub SS2 div add def
						}{
							/L1maxX 0 def
							/L2maxX 0 def
						}ifelse
						p1x p1y moveto p2x p2y lineto L2maxX L2maxX p2x sub SS2 mul p2y add lineto
						L1maxX L1maxX p1x sub SS1 mul p1y add lineto
						fill
					}ifelse
				}ifelse
			}ifelse
		}if
		grestore
		grestore
		end
		end
		end
		}ifelse
	}bdf
	/GenStrips
	{
		40 dict begin
		/ColorSpaceFamily xdf
		/background xdf
		/ext1 xdf
		/ext0 xdf
		/BBox xdf
		/y2 xdf
		/x2 xdf
		/y1 xdf
		/x1 xdf
		/rampdict xdf
		/setinkoverprint where{pop/setinkoverprint{pop}def}if
		gsave
		BBox length 0 gt
		{
			np
			BBox 0 get BBox 1 get moveto
			BBox 2 get BBox 0 get sub 0 rlineto
			0 BBox 3 get BBox 1 get sub rlineto
			BBox 2 get BBox 0 get sub neg 0 rlineto
			closepath
			clip
			np
		}if
		x1 x2 eq
		{
			y1 y2 lt{/theta 90 def}{/theta 270 def}ifelse
		}{
			/slope y2 y1 sub x2 x1 sub div def
			/theta slope 1 atan def
			x2 x1 lt y2 y1 ge and{/theta theta 180 sub def}if
			x2 x1 lt y2 y1 lt and{/theta theta 180 add def}if
		}
		ifelse
		gsave
		clippath
		x1 y1 translate
		theta rotate
		{pathbbox}stopped
		{0 0 0 0}if
		/yMax exch def
		/xMax exch def
		/yMin exch def
		/xMin exch def
		grestore
		xMax xMin eq yMax yMin eq or
		{
			grestore
			end
		}{
			rampdict begin
			20 dict begin
			background length 0 gt{background sssetbackground gsave clippath fill grestore}if
			gsave
			x1 y1 translate
			theta rotate
			/xStart 0 def
			/xEnd x2 x1 sub dup mul y2 y1 sub dup mul add 0.5 exp def
			/ySpan yMax yMin sub def
			/numsteps NumSamples def
			/rampIndxInc 1 def
			/subsampling false def
			xStart 0 transform
			xEnd 0 transform
			3 -1 roll
			sub dup mul
			3 1 roll
			sub dup mul
			add 0.5 exp 72 div
			0 72 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			72 0 matrix defaultmatrix dtransform dup mul exch dup mul add sqrt
			1 index 1 index lt{exch}if pop
			mul
			/numpix xdf
			numpix 0 ne
			{
				NumSamples numpix div 0.5 gt
				{
					/numsteps numpix 2 div round cvi dup 1 le{pop 2}if def
					/rampIndxInc NumSamples 1 sub numsteps div def
					/subsampling true def
				}if
			}if
			ext0
			{
				0 getrampcolor
				xMin xStart lt
				{
					xMin yMin xMin neg ySpan rectfill
				}if
			}if
			/xInc xEnd xStart sub numsteps div def
			/x xStart def
			0
			numsteps
			{
				dup
				subsampling{round cvi}if
				getrampcolor
				x yMin xInc ySpan rectfill
				/x x xInc add def
				rampIndxInc add
			}repeat
			pop
			ext1{
				xMax xEnd gt
				{
					xEnd yMin xMax xEnd sub ySpan rectfill
				}if
			}if
			grestore
			grestore
			end
			end
			end
		}ifelse
	}bdf
}def
/pt
{
	end
}def
/dt{
}def
/pgsv{
	//Adobe_AGM_Core/AGMCORE_save save put
}def
/pgrs{
	//Adobe_AGM_Core/AGMCORE_save get restore
}def
systemdict/findcolorrendering known{
	/findcolorrendering systemdict/findcolorrendering get def
}if
systemdict/setcolorrendering known{
	/setcolorrendering systemdict/setcolorrendering get def
}if
/test_cmyk_color_plate
{
	gsave
	setcmykcolor currentgray 1 ne
	grestore
}def
/inRip_spot_has_ink
{
	dup//Adobe_AGM_Core/AGMCORE_name xddf
	convert_spot_to_process not
}def
/map255_to_range
{
	1 index sub
	3 -1 roll 255 div mul add
}def
/set_csa_crd
{
	/sep_colorspace_dict null AGMCORE_gput
	begin
		CSA get_csa_by_name setcolorspace_opt
		set_crd
	end
}
def
/map_csa
{
	currentdict/MappedCSA known{MappedCSA null ne}{false}ifelse
	{pop}{get_csa_by_name/MappedCSA xdf}ifelse
}def
/setsepcolor
{
	/sep_colorspace_dict AGMCORE_gget begin
		dup/sep_tint exch AGMCORE_gput
		TintProc
	end
}def
/setdevicencolor
{
	/devicen_colorspace_dict AGMCORE_gget begin
		Names length copy
		Names length 1 sub -1 0
		{
			/devicen_tints AGMCORE_gget 3 1 roll xpt
		}for
		TintProc
	end
}def
/sep_colorspace_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	currentdict/Components known{
		Components aload pop 
		TintMethod/Lab eq{
			2{AGMCORE_tmp mul NComponents 1 roll}repeat
			LMax sub AGMCORE_tmp mul LMax add NComponents 1 roll
		}{
			TintMethod/Subtractive eq{
				NComponents{
					AGMCORE_tmp mul NComponents 1 roll
				}repeat
			}{
				NComponents{
					1 sub AGMCORE_tmp mul 1 add NComponents 1 roll
				}repeat
			}ifelse
		}ifelse
	}{
		ColorLookup AGMCORE_tmp ColorLookup length 1 sub mul round cvi get
		aload pop
	}ifelse
	end
}def
/sep_colorspace_gray_proc
{
	/AGMCORE_tmp exch store
	/sep_colorspace_dict AGMCORE_gget begin
	GrayLookup AGMCORE_tmp GrayLookup length 1 sub mul round cvi get
	end
}def
/sep_proc_name
{
	dup 0 get 
	dup/DeviceRGB eq exch/DeviceCMYK eq or level2 not and has_color not and{
		pop[/DeviceGray]
		/sep_colorspace_gray_proc
	}{
		/sep_colorspace_proc
	}ifelse
}def
/setsepcolorspace
{
	current_spot_alias{
		dup begin
			Name map_alias{
				exch pop
			}if
		end
	}if
	dup/sep_colorspace_dict exch AGMCORE_gput
	begin
	CSA map_csa
	/AGMCORE_sep_special Name dup()eq exch(All)eq or store
	AGMCORE_avoid_L2_sep_space{
		[/Indexed MappedCSA sep_proc_name 255 exch 
			{255 div}/exec cvx 3 -1 roll[4 1 roll load/exec cvx]cvx 
		]setcolorspace_opt
		/TintProc{
			255 mul round cvi setcolor
		}bdf
	}{
		MappedCSA 0 get/DeviceCMYK eq 
		currentdict/Components known and 
		AGMCORE_sep_special not and{
			/TintProc[
				Components aload pop Name findcmykcustomcolor 
				/exch cvx/setcustomcolor cvx
			]cvx bdf
		}{
 			AGMCORE_host_sep Name(All)eq and{
 				/TintProc{
					1 exch sub setseparationgray 
				}bdf
 			}{
				AGMCORE_in_rip_sep MappedCSA 0 get/DeviceCMYK eq and 
				AGMCORE_host_sep or
				Name()eq and{
					/TintProc[
						MappedCSA sep_proc_name exch 0 get/DeviceCMYK eq{
							cvx/setcmykcolor cvx
						}{
							cvx/setgray cvx
						}ifelse
					]cvx bdf
				}{
					AGMCORE_producing_seps MappedCSA 0 get dup/DeviceCMYK eq exch/DeviceGray eq or and AGMCORE_sep_special not and{
	 					/TintProc[
							/dup cvx
							MappedCSA sep_proc_name cvx exch
							0 get/DeviceGray eq{
								1/exch cvx/sub cvx 0 0 0 4 -1/roll cvx
							}if
							/Name cvx/findcmykcustomcolor cvx/exch cvx
							AGMCORE_host_sep{
								AGMCORE_is_cmyk_sep
								/Name cvx 
								/AGMCORE_IsSeparationAProcessColor load/exec cvx
								/not cvx/and cvx 
							}{
								Name inRip_spot_has_ink not
							}ifelse
							[
		 						/pop cvx 1
							]cvx/if cvx
							/setcustomcolor cvx
						]cvx bdf
 					}{
						/TintProc{setcolor}bdf
						[/Separation Name MappedCSA sep_proc_name load]setcolorspace_opt
					}ifelse
				}ifelse
			}ifelse
		}ifelse
	}ifelse
	set_crd
	setsepcolor
	end
}def
/additive_blend
{
 	3 dict begin
 	/numarrays xdf
 	/numcolors xdf
 	0 1 numcolors 1 sub
 		{
 		/c1 xdf
 		1
 		0 1 numarrays 1 sub
 			{
			1 exch add/index cvx
 			c1/get cvx/mul cvx
 			}for
 		numarrays 1 add 1/roll cvx 
 		}for
 	numarrays[/pop cvx]cvx/repeat cvx
 	end
}def
/subtractive_blend
{
	3 dict begin
	/numarrays xdf
	/numcolors xdf
	0 1 numcolors 1 sub
		{
		/c1 xdf
		1 1
		0 1 numarrays 1 sub
			{
			1 3 3 -1 roll add/index cvx 
			c1/get cvx/sub cvx/mul cvx
			}for
		/sub cvx
		numarrays 1 add 1/roll cvx
		}for
	numarrays[/pop cvx]cvx/repeat cvx
	end
}def
/exec_tint_transform
{
	/TintProc[
		/TintTransform cvx/setcolor cvx
	]cvx bdf
	MappedCSA setcolorspace_opt
}bdf
/devn_makecustomcolor
{
	2 dict begin
	/names_index xdf
	/Names xdf
	1 1 1 1 Names names_index get findcmykcustomcolor
	/devicen_tints AGMCORE_gget names_index get setcustomcolor
	Names length{pop}repeat
	end
}bdf
/setdevicencolorspace
{
	dup/AliasedColorants known{false}{true}ifelse 
	current_spot_alias and{
		7 dict begin
		/names_index 0 def
		dup/names_len exch/Names get length def
		/new_names names_len array def
		/new_LookupTables names_len array def
		/alias_cnt 0 def
		dup/Names get
		{
			dup map_alias{
				exch pop
				dup/ColorLookup known{
					dup begin
					new_LookupTables names_index ColorLookup put
					end
				}{
					dup/Components known{
						dup begin
						new_LookupTables names_index Components put
						end
					}{
						dup begin
						new_LookupTables names_index[null null null null]put
						end
					}ifelse
				}ifelse
				new_names names_index 3 -1 roll/Name get put
				/alias_cnt alias_cnt 1 add def 
			}{
				/name xdf				
				new_names names_index name put
				dup/LookupTables known{
					dup begin
					new_LookupTables names_index LookupTables names_index get put
					end
				}{
					dup begin
					new_LookupTables names_index[null null null null]put
					end
				}ifelse
			}ifelse
			/names_index names_index 1 add def 
		}forall
		alias_cnt 0 gt{
			/AliasedColorants true def
			/lut_entry_len new_LookupTables 0 get dup length 256 ge{0 get length}{length}ifelse def
			0 1 names_len 1 sub{
				/names_index xdf
				new_LookupTables names_index get dup length 256 ge{0 get length}{length}ifelse lut_entry_len ne{
					/AliasedColorants false def
					exit
				}{
					new_LookupTables names_index get 0 get null eq{
						dup/Names get names_index get/name xdf
						name(Cyan)eq name(Magenta)eq name(Yellow)eq name(Black)eq
						or or or not{
							/AliasedColorants false def
							exit
						}if
					}if
				}ifelse
			}for
			lut_entry_len 1 eq{
				/AliasedColorants false def
			}if
			AliasedColorants{
				dup begin
				/Names new_names def
				/LookupTables new_LookupTables def
				/AliasedColorants true def
				/NComponents lut_entry_len def
				/TintMethod NComponents 4 eq{/Subtractive}{/Additive}ifelse def
				/MappedCSA TintMethod/Additive eq{/DeviceRGB}{/DeviceCMYK}ifelse def
				currentdict/TTTablesIdx known not{
					/TTTablesIdx -1 def
				}if
				end
			}if
		}if
		end
	}if
	dup/devicen_colorspace_dict exch AGMCORE_gput
	begin
	currentdict/AliasedColorants known{
		AliasedColorants
	}{
		false
	}ifelse
	dup not{
		CSA map_csa
	}if
	/TintTransform load type/nulltype eq or{
		/TintTransform[
			0 1 Names length 1 sub
				{
				/TTTablesIdx TTTablesIdx 1 add def
				dup LookupTables exch get dup 0 get null eq
					{
					1 index
					Names exch get
					dup(Cyan)eq
						{
						pop exch
						LookupTables length exch sub
						/index cvx
						0 0 0
						}
						{
						dup(Magenta)eq
							{
							pop exch
							LookupTables length exch sub
							/index cvx
							0/exch cvx 0 0
							}{
							(Yellow)eq
								{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 3 -1/roll cvx 0
								}{
								exch
								LookupTables length exch sub
								/index cvx
								0 0 0 4 -1/roll cvx
								}ifelse
							}ifelse
						}ifelse
					5 -1/roll cvx/astore cvx
					}{
					dup length 1 sub
					LookupTables length 4 -1 roll sub 1 add
					/index cvx/mul cvx/round cvx/cvi cvx/get cvx
					}ifelse
					Names length TTTablesIdx add 1 add 1/roll cvx
				}for
			Names length[/pop cvx]cvx/repeat cvx
			NComponents Names length
 			TintMethod/Subtractive eq
 				{
 				subtractive_blend
 				}{
 				additive_blend
 				}ifelse
		]cvx bdf
	}if
	AGMCORE_host_sep{
		Names convert_to_process{
			exec_tint_transform
		}
		{	
			currentdict/AliasedColorants known{
				AliasedColorants not
			}{
				false
			}ifelse
			5 dict begin
			/AvoidAliasedColorants xdf
			/painted? false def
			/names_index 0 def
			/names_len Names length def
			AvoidAliasedColorants{
				/currentspotalias current_spot_alias def
				false set_spot_alias
			}if
			Names{
				AGMCORE_is_cmyk_sep{
					dup(Cyan)eq AGMCORE_cyan_plate and exch
					dup(Magenta)eq AGMCORE_magenta_plate and exch
					dup(Yellow)eq AGMCORE_yellow_plate and exch
					(Black)eq AGMCORE_black_plate and or or or{
						/devicen_colorspace_dict AGMCORE_gget/TintProc[
							Names names_index/devn_makecustomcolor cvx
						]cvx ddf
						/painted? true def
					}if
					painted?{exit}if
				}{
					0 0 0 0 5 -1 roll findcmykcustomcolor 1 setcustomcolor currentgray 0 eq{
					/devicen_colorspace_dict AGMCORE_gget/TintProc[
						Names names_index/devn_makecustomcolor cvx
					]cvx ddf
					/painted? true def
					exit
					}if
				}ifelse
				/names_index names_index 1 add def
			}forall
			AvoidAliasedColorants{
				currentspotalias set_spot_alias
			}if
			painted?{
				/devicen_colorspace_dict AGMCORE_gget/names_index names_index put
			}{
				/devicen_colorspace_dict AGMCORE_gget/TintProc[
					names_len[/pop cvx]cvx/repeat cvx 1/setseparationgray cvx
 					0 0 0 0/setcmykcolor cvx
				]cvx ddf
			}ifelse
			end
		}ifelse
	}
	{
		AGMCORE_in_rip_sep{
			Names convert_to_process not
		}{
			level3
		}ifelse
		{
			[/DeviceN Names MappedCSA/TintTransform load]setcolorspace_opt
			/TintProc level3 not AGMCORE_in_rip_sep and{
				[
					Names/length cvx[/pop cvx]cvx/repeat cvx
				]cvx bdf
			}{
				{setcolor}bdf
			}ifelse
		}{
			exec_tint_transform
		}ifelse
	}ifelse
	set_crd
	/AliasedColorants false def
	end
}def
/setindexedcolorspace
{
	dup/indexed_colorspace_dict exch AGMCORE_gput
	begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				currentdict devncs
			}{
				1 currentdict sepcs
			}ifelse
			AGMCORE_host_sep{
				4 dict begin
				/compCnt/Names where{pop Names length}{1}ifelse def
				/NewLookup HiVal 1 add string def
				0 1 HiVal{
					/tableIndex xdf
					Lookup dup type/stringtype eq{
						compCnt tableIndex map_index
					}{
						exec
					}ifelse
					/Names where{
						pop setdevicencolor
					}{
						setsepcolor
					}ifelse
					currentgray
					tableIndex exch
					255 mul cvi 
					NewLookup 3 1 roll put
				}for
				[/Indexed currentcolorspace HiVal NewLookup]setcolorspace_opt
				end
			}{
				level3
				{
					currentdict/Names known{
						[/Indexed[/DeviceN Names MappedCSA/TintTransform load]HiVal Lookup]setcolorspace_opt
					}{
						[/Indexed[/Separation Name MappedCSA sep_proc_name load]HiVal Lookup]setcolorspace_opt
					}ifelse
				}{
				[/Indexed MappedCSA HiVal
					[
					currentdict/Names known{
						Lookup dup type/stringtype eq
							{/exch cvx CSDBase/CSD get_res/Names get length dup/mul cvx exch/getinterval cvx{255 div}/forall cvx}
							{/exec cvx}ifelse
							/TintTransform load/exec cvx
					}{
						Lookup dup type/stringtype eq
							{/exch cvx/get cvx 255/div cvx}
							{/exec cvx}ifelse
							CSDBase/CSD get_res/MappedCSA get sep_proc_name exch pop/load cvx/exec cvx
					}ifelse
					]cvx
				]setcolorspace_opt
				}ifelse
			}ifelse
			end
			set_crd
		}
		{
			CSA map_csa
			AGMCORE_host_sep level2 not and{
				0 0 0 0 setcmykcolor
			}{
				[/Indexed MappedCSA 
				level2 not has_color not and{
					dup 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or{
						pop[/DeviceGray]
					}if
					HiVal GrayLookup
				}{
					HiVal 
					currentdict/RangeArray known{
						{
							/indexed_colorspace_dict AGMCORE_gget begin
							Lookup exch 
							dup HiVal gt{
								pop HiVal
							}if
							NComponents mul NComponents getinterval{}forall
							NComponents 1 sub -1 0{
								RangeArray exch 2 mul 2 getinterval aload pop map255_to_range
								NComponents 1 roll
							}for
							end
						}bind
					}{
						Lookup
					}ifelse
				}ifelse
				]setcolorspace_opt
				set_crd
			}ifelse
		}ifelse
	end
}def
/setindexedcolor
{
	AGMCORE_host_sep{
		/indexed_colorspace_dict AGMCORE_gget
		begin
		currentdict/CSDBase known{
			CSDBase/CSD get_res begin
			currentdict/Names known{
				map_indexed_devn
				devn
			}
			{
				Lookup 1 3 -1 roll map_index
				sep
			}ifelse
			end
		}{
			Lookup MappedCSA/DeviceCMYK eq{4}{1}ifelse 3 -1 roll
			map_index
			MappedCSA/DeviceCMYK eq{setcmykcolor}{setgray}ifelse
		}ifelse
		end
	}{
		level3 not AGMCORE_in_rip_sep and/indexed_colorspace_dict AGMCORE_gget/CSDBase known and{
			/indexed_colorspace_dict AGMCORE_gget/CSDBase get/CSD get_res begin
			map_indexed_devn
			devn
			end
		}
		{
			setcolor
		}ifelse
	}ifelse
}def
/ignoreimagedata
{
	currentoverprint not{
		gsave
		dup clonedict begin
		1 setgray
		/Decode[0 1]def
		/DataSource<FF>def
		/MultipleDataSources false def
		/BitsPerComponent 8 def
		currentdict end
		systemdict/image gx
		grestore
		}if
	consumeimagedata
}def
/add_res
{
	dup/CSD eq{
		pop 
		//Adobe_AGM_Core begin
		/AGMCORE_CSD_cache load 3 1 roll put
		end
	}{
		defineresource pop
	}ifelse
}def
/del_res
{
	{
		aload pop exch
		dup/CSD eq{
			pop 
			{//Adobe_AGM_Core/AGMCORE_CSD_cache get exch undef}forall
		}{
			exch
			{1 index undefineresource}forall
			pop
		}ifelse
	}forall
}def
/get_res
{
	dup/CSD eq{
		pop
		dup type dup/nametype eq exch/stringtype eq or{
			AGMCORE_CSD_cache exch get
		}if
	}{
		findresource
	}ifelse
}def
/get_csa_by_name
{
	dup type dup/nametype eq exch/stringtype eq or{
		/CSA get_res
	}if
}def
/paintproc_buf_init
{
	/count get 0 0 put
}def
/paintproc_buf_next
{
	dup/count get dup 0 get
	dup 3 1 roll
	1 add 0 xpt
	get				
}def
/cachepaintproc_compress
{
	5 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	/string_size 16000 def
	/readbuffer string_size string def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	/LZWFilter 
	{
		exch
		dup length 0 eq{
			pop
		}{
			ppdict dup length 1 sub 3 -1 roll put
		}ifelse
		{string_size}{0}ifelse string
	}/LZWEncode filter def
	{		
		ReadFilter readbuffer readstring
		exch LZWFilter exch writestring
		not{exit}if
	}loop
	LZWFilter closefile
	ppdict				
	end
}def
/cachepaintproc
{
	2 dict begin
	currentfile exch 0 exch/SubFileDecode filter/ReadFilter exch def
	/ppdict 20 dict def
	currentglobal true setglobal 
	ppdict 1 array dup 0 1 put/count xpt
	setglobal
	{
		ReadFilter 16000 string readstring exch
		ppdict dup length 1 sub 3 -1 roll put
		not{exit}if
	}loop
	ppdict dup dup length 1 sub()put					
	end	
}def
/make_pattern
{
	exch clonedict exch
	dup matrix currentmatrix matrix concatmatrix 0 0 3 2 roll itransform
	exch 3 index/XStep get 1 index exch 2 copy div cvi mul sub sub
	exch 3 index/YStep get 1 index exch 2 copy div cvi mul sub sub
	matrix translate exch matrix concatmatrix
			 1 index begin
		BBox 0 get XStep div cvi XStep mul/xshift exch neg def
		BBox 1 get YStep div cvi YStep mul/yshift exch neg def
		BBox 0 get xshift add
		BBox 1 get yshift add
		BBox 2 get xshift add
		BBox 3 get yshift add
		4 array astore
		/BBox exch def
		[xshift yshift/translate load null/exec load]dup
		3/PaintProc load put cvx/PaintProc exch def
		end
	gsave 0 setgray
	makepattern
	grestore
}def
/set_pattern
{
	dup/PatternType get 1 eq{
		dup/PaintType get 1 eq{
			currentoverprint sop[/DeviceGray]setcolorspace 0 setgray
		}if
	}if
	setpattern
}def
/setcolorspace_opt
{
	dup currentcolorspace eq{pop}{setcolorspace}ifelse
}def
/updatecolorrendering
{
	currentcolorrendering/RenderingIntent known{
		currentcolorrendering/RenderingIntent get
	}
	{
		Intent/AbsoluteColorimetric eq 
		{
			/absolute_colorimetric_crd AGMCORE_gget dup null eq
		}
		{
			Intent/RelativeColorimetric eq
			{
				/relative_colorimetric_crd AGMCORE_gget dup null eq
			}
			{
				Intent/Saturation eq
				{
					/saturation_crd AGMCORE_gget dup null eq
				}
				{
					/perceptual_crd AGMCORE_gget dup null eq
				}ifelse
			}ifelse
		}ifelse
		{
			pop null	
		}
		{
			/RenderingIntent known{null}{Intent}ifelse
		}ifelse
	}ifelse
	Intent ne{
		Intent/ColorRendering{findresource}stopped
		{
			pop pop systemdict/findcolorrendering known
			{
 				Intent findcolorrendering
 				{
 					/ColorRendering findresource true exch
 				}
 				{
 					/ColorRendering findresource
					product(Xerox Phaser 5400)ne
					exch
 				}ifelse
				dup Intent/AbsoluteColorimetric eq 
				{
					/absolute_colorimetric_crd exch AGMCORE_gput
				}
				{
					Intent/RelativeColorimetric eq
					{
						/relative_colorimetric_crd exch AGMCORE_gput
					}
					{
						Intent/Saturation eq
						{
							/saturation_crd exch AGMCORE_gput
						}
						{
							Intent/Perceptual eq
							{
								/perceptual_crd exch AGMCORE_gput
							}
							{
								pop
							}ifelse
						}ifelse
					}ifelse
				}ifelse
				1 index{exch}{pop}ifelse
			}
			{false}ifelse
		}
		{true}ifelse
		{
			dup begin
			currentdict/TransformPQR known{
				currentdict/TransformPQR get aload pop
				3{{}eq 3 1 roll}repeat or or
			}
			{true}ifelse
			currentdict/MatrixPQR known{
				currentdict/MatrixPQR get aload pop
				1.0 eq 9 1 roll 0.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 1.0 eq 9 1 roll 0.0 eq 9 1 roll
				0.0 eq 9 1 roll 0.0 eq 9 1 roll 1.0 eq
				and and and and and and and and
			}
			{true}ifelse
			end
			or
			{
				clonedict begin
				/TransformPQR[
					{4 -1 roll 3 get dup 3 1 roll sub 5 -1 roll 3 get 3 -1 roll sub div
					3 -1 roll 3 get 3 -1 roll 3 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 4 get dup 3 1 roll sub 5 -1 roll 4 get 3 -1 roll sub div
					3 -1 roll 4 get 3 -1 roll 4 get dup 4 1 roll sub mul add}bind
					{4 -1 roll 5 get dup 3 1 roll sub 5 -1 roll 5 get 3 -1 roll sub div
					3 -1 roll 5 get 3 -1 roll 5 get dup 4 1 roll sub mul add}bind
				]def
				/MatrixPQR[0.8951 -0.7502 0.0389 0.2664 1.7135 -0.0685 -0.1614 0.0367 1.0296]def
				/RangePQR[-0.3227950745 2.3229645538 -1.5003771057 3.5003465881 -0.1369979095 2.136967392]def
				currentdict end
			}if
			setcolorrendering_opt
		}if		
	}if
}def
/set_crd
{
	AGMCORE_host_sep not level2 and{
		currentdict/ColorRendering known{
			ColorRendering/ColorRendering{findresource}stopped not{setcolorrendering_opt}if
		}{
			currentdict/Intent known{
				updatecolorrendering
			}if
		}ifelse
		currentcolorspace dup type/arraytype eq
			{0 get}if
		/DeviceRGB eq
			{
			currentdict/UCR known
				{/UCR}{/AGMCORE_currentucr}ifelse
			load setundercolorremoval
			currentdict/BG known 
				{/BG}{/AGMCORE_currentbg}ifelse
			load setblackgeneration
			}if
	}if
}def
/set_ucrbg
{
	dup null eq {pop /AGMCORE_currentbg load}{/Procedure get_res}ifelse
	dup currentblackgeneration eq {pop}{setblackgeneration}ifelse
	dup null eq {pop /AGMCORE_currentucr load}{/Procedure get_res}ifelse
	dup currentundercolorremoval eq {pop}{setundercolorremoval}ifelse
}def
/setcolorrendering_opt
{
	dup currentcolorrendering eq{
		pop
	}{
		product(HP Color LaserJet 2605)anchorsearch{
			pop pop pop
		}{
			pop
			clonedict
			begin
				/Intent Intent def
				currentdict
			end
			setcolorrendering
		}ifelse
	}ifelse
}def
/cpaint_gcomp
{
	convert_to_process//Adobe_AGM_Core/AGMCORE_ConvertToProcess xddf
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get not
	{
		(%end_cpaint_gcomp)flushinput
	}if
}def
/cpaint_gsep
{
	//Adobe_AGM_Core/AGMCORE_ConvertToProcess get
	{	
		(%end_cpaint_gsep)flushinput
	}if
}def
/cpaint_gend
{np}def
/T1_path
{
	currentfile token pop currentfile token pop mo
	{
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 exch rlineto 
		currentfile token pop dup type/stringtype eq
			{pop exit}if 
		0 rlineto
	}loop
}def
/T1_gsave
	level3
	{/clipsave}
	{/gsave}ifelse
	load def
/T1_grestore
	level3
	{/cliprestore}
	{/grestore}ifelse 
	load def
/set_spot_alias_ary
{
	dup inherit_aliases
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry xddf
}def
/set_spot_normalization_ary
{
	dup inherit_aliases
	dup length
	/AGMCORE_SpotAliasAry where{pop AGMCORE_SpotAliasAry length add}if
	array
	//Adobe_AGM_Core/AGMCORE_SpotAliasAry2 xddf
	/AGMCORE_SpotAliasAry where{
		pop
		AGMCORE_SpotAliasAry2 0 AGMCORE_SpotAliasAry putinterval
		AGMCORE_SpotAliasAry length
	}{0}ifelse
	AGMCORE_SpotAliasAry2 3 1 roll exch putinterval
	true set_spot_alias
}def
/inherit_aliases
{
	{dup/Name get map_alias{/CSD put}{pop}ifelse}forall
}def
/set_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias 3 -1 roll put
	}{
		pop
	}ifelse
}def
/current_spot_alias
{
	/AGMCORE_SpotAliasAry2 where{
		/AGMCORE_current_spot_alias get
	}{
		false
	}ifelse
}def
/map_alias
{
	/AGMCORE_SpotAliasAry2 where{
		begin
			/AGMCORE_name xdf
			false	
			AGMCORE_SpotAliasAry2{
				dup/Name get AGMCORE_name eq{
					/CSD get/CSD get_res
					exch pop true
					exit
				}{
					pop
				}ifelse
			}forall
		end
	}{
		pop false
	}ifelse
}bdf
/spot_alias
{
	true set_spot_alias
	/AGMCORE_&setcustomcolor AGMCORE_key_known not{
		//Adobe_AGM_Core/AGMCORE_&setcustomcolor/setcustomcolor load put
	}if
	/customcolor_tint 1 AGMCORE_gput
	//Adobe_AGM_Core begin
	/setcustomcolor
	{
		//Adobe_AGM_Core begin
		dup/customcolor_tint exch AGMCORE_gput
		1 index aload pop pop 1 eq exch 1 eq and exch 1 eq and exch 1 eq and not
		current_spot_alias and{1 index 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/sep_colorspace_dict AGMCORE_gget null ne
			{/sep_colorspace_dict AGMCORE_gget/ForeignContent known not}{false}ifelse
			3 1 roll 2 index{
				exch pop/sep_tint AGMCORE_gget exch
			}if
			mark 3 1 roll
			setsepcolorspace
			counttomark 0 ne{
				setsepcolor
			}if
			pop
			not{/sep_tint 1.0 AGMCORE_gput/sep_colorspace_dict AGMCORE_gget/ForeignContent true put}if
			pop
			true set_spot_alias
		}{
			AGMCORE_&setcustomcolor
		}ifelse
		end
	}bdf
	end
}def
/begin_feature
{
	Adobe_AGM_Core/AGMCORE_feature_dictCount countdictstack put
	count Adobe_AGM_Core/AGMCORE_feature_opCount 3 -1 roll put
	{Adobe_AGM_Core/AGMCORE_feature_ctm matrix currentmatrix put}if
}def
/end_feature
{
	2 dict begin
	/spd/setpagedevice load def
	/setpagedevice{get_gstate spd set_gstate}def
	stopped{$error/newerror false put}if
	end
	count Adobe_AGM_Core/AGMCORE_feature_opCount get sub dup 0 gt{{pop}repeat}{pop}ifelse
	countdictstack Adobe_AGM_Core/AGMCORE_feature_dictCount get sub dup 0 gt{{end}repeat}{pop}ifelse
	{Adobe_AGM_Core/AGMCORE_feature_ctm get setmatrix}if
}def
/set_negative
{
	//Adobe_AGM_Core begin
	/AGMCORE_inverting exch def
	level2{
		currentpagedevice/NegativePrint known AGMCORE_distilling not and{
			currentpagedevice/NegativePrint get//Adobe_AGM_Core/AGMCORE_inverting get ne{
				true begin_feature true{
						<</NegativePrint//Adobe_AGM_Core/AGMCORE_inverting get>>setpagedevice
				}end_feature
			}if
			/AGMCORE_inverting false def
		}if
	}if
	AGMCORE_inverting{
		[{1 exch sub}/exec load dup currenttransfer exch]cvx bind settransfer
 		AGMCORE_distilling{
 			erasepage
 		}{
 			gsave np clippath 1/setseparationgray where{pop setseparationgray}{setgray}ifelse
 			/AGMIRS_&fill where{pop AGMIRS_&fill}{fill}ifelse grestore
 		}ifelse
	}if
	end
}def
/lw_save_restore_override{
	/md where{
		pop
		md begin
		initializepage
		/initializepage{}def
		/pmSVsetup{}def
		/endp{}def
		/pse{}def
		/psb{}def
		/orig_showpage where
			{pop}
			{/orig_showpage/showpage load def}
		ifelse
		/showpage{orig_showpage gR}def
		end
	}if
}def
/pscript_showpage_override{
	/NTPSOct95 where
	{
		begin
		showpage
		save
		/showpage/restore load def
		/restore{exch pop}def
		end
	}if
}def
/driver_media_override
{
	/md where{
		pop
		md/initializepage known{
			md/initializepage{}put
		}if
		md/rC known{
			md/rC{4{pop}repeat}put
		}if
	}if
	/mysetup where{
		/mysetup[1 0 0 1 0 0]put
	}if
	Adobe_AGM_Core/AGMCORE_Default_CTM matrix currentmatrix put
	level2
		{Adobe_AGM_Core/AGMCORE_Default_PageSize currentpagedevice/PageSize get put}if
}def
/capture_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup Pscript_Win_Data/mysetup get put
		}if
	}if
}def
/restore_mysetup
{
	/Pscript_Win_Data where{
		pop
		Pscript_Win_Data/mysetup known{
			Adobe_AGM_Core/save_mysetup known{
				Pscript_Win_Data/mysetup Adobe_AGM_Core/save_mysetup get put
				Adobe_AGM_Core/save_mysetup undef
			}if
		}if
	}if
}def
/driver_check_media_override
{
 	/PrepsDict where
 		{pop}
		{
		Adobe_AGM_Core/AGMCORE_Default_CTM get matrix currentmatrix ne
		Adobe_AGM_Core/AGMCORE_Default_PageSize get type/arraytype eq
			{
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 0 get currentpagedevice/PageSize get 0 get eq and
			Adobe_AGM_Core/AGMCORE_Default_PageSize get 1 get currentpagedevice/PageSize get 1 get eq and
			}if
			{
			Adobe_AGM_Core/AGMCORE_Default_CTM get setmatrix
			}if
		}ifelse
}def
AGMCORE_err_strings begin
	/AGMCORE_bad_environ(Environment not satisfactory for this job. Ensure that the PPD is correct or that the PostScript level requested is supported by this printer. )def
	/AGMCORE_color_space_onhost_seps(This job contains colors that will not separate with on-host methods. )def
	/AGMCORE_invalid_color_space(This job contains an invalid color space. )def
end
/set_def_ht
{AGMCORE_def_ht sethalftone}def
/set_def_flat
{AGMCORE_Default_flatness setflat}def
end
systemdict/setpacking known
{setpacking}if
%%EndResource
%%BeginResource: procset Adobe_CoolType_Core 2.31 0
%%Copyright: Copyright 1997-2006 Adobe Systems Incorporated. All Rights Reserved.
%%Version: 2.31 0
10 dict begin
/Adobe_CoolType_Passthru currentdict def
/Adobe_CoolType_Core_Defined userdict/Adobe_CoolType_Core known def
Adobe_CoolType_Core_Defined
	{/Adobe_CoolType_Core userdict/Adobe_CoolType_Core get def}
if
userdict/Adobe_CoolType_Core 70 dict dup begin put
/Adobe_CoolType_Version 2.31 def
/Level2?
	systemdict/languagelevel known dup
		{pop systemdict/languagelevel get 2 ge}
	if def
Level2? not
	{
	/currentglobal false def
	/setglobal/pop load def
	/gcheck{pop false}bind def
	/currentpacking false def
	/setpacking/pop load def
	/SharedFontDirectory 0 dict def
	}
if
currentpacking
true setpacking
currentglobal false setglobal
userdict/Adobe_CoolType_Data 2 copy known not
	{2 copy 10 dict put}
if
get
	 begin
	/@opStackCountByLevel 32 dict def
	/@opStackLevel 0 def
	/@dictStackCountByLevel 32 dict def
	/@dictStackLevel 0 def
	 end
setglobal
currentglobal true setglobal
userdict/Adobe_CoolType_GVMFonts known not
	{userdict/Adobe_CoolType_GVMFonts 10 dict put}
if
setglobal
currentglobal false setglobal
userdict/Adobe_CoolType_LVMFonts known not
	{userdict/Adobe_CoolType_LVMFonts 10 dict put}
if
setglobal
/ct_VMDictPut
	{
	dup gcheck{Adobe_CoolType_GVMFonts}{Adobe_CoolType_LVMFonts}ifelse
	3 1 roll put
	}bind def
/ct_VMDictUndef
	{
	dup Adobe_CoolType_GVMFonts exch known
		{Adobe_CoolType_GVMFonts exch undef}
		{
			dup Adobe_CoolType_LVMFonts exch known
			{Adobe_CoolType_LVMFonts exch undef}
			{pop}
			ifelse
		}ifelse
	}bind def
/ct_str1 1 string def
/ct_xshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_yshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			_ct_x _ct_y moveto
			0 exch
			rmoveto
		}
		ifelse
		/_ct_i _ct_i 1 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/ct_xyshow
{
	/_ct_na exch def
	/_ct_i 0 def
	currentpoint
	/_ct_y exch def
	/_ct_x exch def
	{
		pop pop
		ct_str1 exch 0 exch put
		ct_str1 show
		{_ct_na _ct_i get}stopped 
		{pop pop}
		{
			{_ct_na _ct_i 1 add get}stopped 
			{pop pop pop}
			{
				_ct_x _ct_y moveto
				rmoveto
			}
			ifelse
		}
		ifelse
		/_ct_i _ct_i 2 add def
		currentpoint
		/_ct_y exch def
		/_ct_x exch def
	}
	exch
	@cshow
}bind def
/xsh{{@xshow}stopped{Adobe_CoolType_Data begin ct_xshow end}if}bind def
/ysh{{@yshow}stopped{Adobe_CoolType_Data begin ct_yshow end}if}bind def
/xysh{{@xyshow}stopped{Adobe_CoolType_Data begin ct_xyshow end}if}bind def
currentglobal true setglobal
/ct_T3Defs
{
/BuildChar
{
	1 index/Encoding get exch get
	1 index/BuildGlyph get exec
}bind def
/BuildGlyph
{
	exch begin
	GlyphProcs exch get exec
	end
}bind def
}bind def
setglobal
/@_SaveStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@vmState currentglobal def false setglobal
		@opStackCountByLevel
		@opStackLevel
		2 copy known not
			{
			2 copy
			3 dict dup/args
			7 index
			5 add array put
			put get
			}
			{
			get dup/args get dup length 3 index lt
				{
				dup length 5 add array exch
				1 index exch 0 exch putinterval
				1 index exch/args exch put
				}
				{pop}
			ifelse
			}
		ifelse
			begin
			count 1 sub
			1 index lt
				{pop count}
			if
			dup/argCount exch def
			dup 0 gt
				{
				args exch 0 exch getinterval 
			astore pop
				}
				{pop}
			ifelse
			count
			/restCount exch def
			end
		/@opStackLevel @opStackLevel 1 add def
		countdictstack 1 sub
		@dictStackCountByLevel exch @dictStackLevel exch put
		/@dictStackLevel @dictStackLevel 1 add def
		@vmState setglobal
		end
	}bind def
/@_RestoreStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		@opStackCountByLevel @opStackLevel get
			begin
			count restCount sub dup 0 gt
				{{pop}repeat}
				{pop}
			ifelse
			args 0 argCount getinterval{}forall
			end
		/@dictStackLevel @dictStackLevel 1 sub def
		@dictStackCountByLevel @dictStackLevel get
		end
	countdictstack exch sub dup 0 gt
		{{end}repeat}
		{pop}
	ifelse
	}bind def
/@_PopStackLevels
	{
	Adobe_CoolType_Data
		begin
		/@opStackLevel @opStackLevel 1 sub def
		/@dictStackLevel @dictStackLevel 1 sub def
		end
	}bind def
/@Raise
	{
	exch cvx exch errordict exch get exec
	stop
	}bind def
/@ReRaise
	{
	cvx $error/errorname get errordict exch get exec
	stop
	}bind def
/@Stopped
	{
	0 @#Stopped
	}bind def
/@#Stopped
	{
	@_SaveStackLevels
	stopped
		{@_RestoreStackLevels true}
		{@_PopStackLevels false}
	ifelse
	}bind def
/@Arg
	{
	Adobe_CoolType_Data
		begin
		@opStackCountByLevel @opStackLevel 1 sub get
		begin
		args exch
		argCount 1 sub exch sub get
		end
		end
	}bind def
currentglobal true setglobal
/CTHasResourceForAllBug
	Level2?
		{
		1 dict dup
				/@shouldNotDisappearDictValue true def
				Adobe_CoolType_Data exch/@shouldNotDisappearDict exch put
				begin
				count @_SaveStackLevels
					{(*){pop stop}128 string/Category resourceforall}
				stopped pop
				@_RestoreStackLevels
				currentdict Adobe_CoolType_Data/@shouldNotDisappearDict get dup 3 1 roll ne dup 3 1 roll
					{
						 /@shouldNotDisappearDictValue known
								{
										 {
												end
												currentdict 1 index eq
													{pop exit}
												if
										 }
									 loop
								}
						 if
					}
					{
						 pop
						 end
					}
				ifelse
		}
		{false}
	ifelse
	def
true setglobal
/CTHasResourceStatusBug
	Level2?
		{
		mark
			{/steveamerige/Category resourcestatus}
		stopped
			{cleartomark true}
			{cleartomark currentglobal not}
		ifelse
		}
		{false}
	ifelse
	def
setglobal
/CTResourceStatus
		{
		mark 3 1 roll
		/Category findresource
			begin
			({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
				{cleartomark false}
				{{3 2 roll pop true}{cleartomark false}ifelse}
			ifelse
			end
		}bind def
/CTWorkAroundBugs
	{
	Level2?
		{
		/cid_PreLoad/ProcSet resourcestatus
			{
			pop pop
			currentglobal
			mark
				{
				(*)
					{
					dup/CMap CTHasResourceStatusBug
						{CTResourceStatus}
						{resourcestatus}
					ifelse
						{
						pop dup 0 eq exch 1 eq or
							{
							dup/CMap findresource gcheck setglobal
							/CMap undefineresource
							}
							{
							pop CTHasResourceForAllBug
								{exit}
								{stop}
							ifelse
							}
						ifelse
						}
						{pop}
					ifelse
					}
				128 string/CMap resourceforall
				}
			stopped
				{cleartomark}
			stopped pop
			setglobal
			}
		if
		}
	if
	}bind def
/ds
	{
	Adobe_CoolType_Core
		begin
		CTWorkAroundBugs
		/mo/moveto load def
		/nf/newencodedfont load def
		/msf{makefont setfont}bind def
		/uf{dup undefinefont ct_VMDictUndef}bind def
		/ur/undefineresource load def
		/chp/charpath load def
		/awsh/awidthshow load def
		/wsh/widthshow load def
		/ash/ashow load def
		/@xshow/xshow load def
		/@yshow/yshow load def
		/@xyshow/xyshow load def
		/@cshow/cshow load def
		/sh/show load def
		/rp/repeat load def
		/.n/.notdef def
		end
		currentglobal false setglobal
	 userdict/Adobe_CoolType_Data 2 copy known not
		 {2 copy 10 dict put}
		if
		get
		begin
		/AddWidths? false def
		/CC 0 def
		/charcode 2 string def
		/@opStackCountByLevel 32 dict def
		/@opStackLevel 0 def
		/@dictStackCountByLevel 32 dict def
		/@dictStackLevel 0 def
		/InVMFontsByCMap 10 dict def
		/InVMDeepCopiedFonts 10 dict def
		end
		setglobal
	}bind def
/dt
	{
	currentdict Adobe_CoolType_Core eq
		{end}
	if
	}bind def
/ps
	{
	Adobe_CoolType_Core begin
	Adobe_CoolType_GVMFonts begin
	Adobe_CoolType_LVMFonts begin
	SharedFontDirectory begin
	}bind def
/pt
	{
	end
	end
	end
	end
	}bind def
/unload
	{
	systemdict/languagelevel known
		{
		systemdict/languagelevel get 2 ge
			{
			userdict/Adobe_CoolType_Core 2 copy known
				{undef}
				{pop pop}
			ifelse
			}
		if
		}
	if
	}bind def
/ndf
	{
	1 index where
		{pop pop pop}
		{dup xcheck{bind}if def}
	ifelse
	}def
/findfont systemdict
	begin
	userdict
		begin
		/globaldict where{/globaldict get begin}if
			dup where pop exch get
		/globaldict where{pop end}if
		end
	end
Adobe_CoolType_Core_Defined
	{/systemfindfont exch def}
	{
	/findfont 1 index def
	/systemfindfont exch def
	}
ifelse
/undefinefont
	{pop}ndf
/copyfont
	{
	currentglobal 3 1 roll
	1 index gcheck setglobal
	dup null eq{0}{dup length}ifelse
	2 index length add 1 add dict
		begin
		exch
			{
			1 index/FID eq
				{pop pop}
				{def}
			ifelse
			}
		forall
		dup null eq
			{pop}
			{{def}forall}
		ifelse
		currentdict
		end
	exch setglobal
	}bind def
/copyarray
	{
	currentglobal exch
	dup gcheck setglobal
	dup length array copy
	exch setglobal
	}bind def
/newencodedfont
	{
	currentglobal
		{
		SharedFontDirectory 3 index known
			{SharedFontDirectory 3 index get/FontReferenced known}
			{false}
		ifelse
		}
		{
		FontDirectory 3 index known
			{FontDirectory 3 index get/FontReferenced known}
			{
			SharedFontDirectory 3 index known
				{SharedFontDirectory 3 index get/FontReferenced known}
				{false}
			ifelse
			}
		ifelse
		}
	ifelse
	dup
		{
		3 index findfont/FontReferenced get
		2 index dup type/nametype eq
			{findfont}
		if ne
			{pop false}
		if
		}
	if
	dup
		{
		1 index dup type/nametype eq
			{findfont}
		 if
		dup/CharStrings known
			{
			/CharStrings get length
			4 index findfont/CharStrings get length
			ne
				{
				pop false
				}
			if 
			}
			{pop}
			ifelse
		}
	if
		{
		pop
		1 index findfont
		/Encoding get exch
		0 1 255
			{2 copy get 3 index 3 1 roll put}
		for
		pop pop pop
		}
		{
		currentglobal
	 4 1 roll
		dup type/nametype eq
		 {findfont}
	 if
	 dup gcheck setglobal
		dup dup maxlength 2 add dict
			begin
			exch
				{
				1 index/FID ne
				2 index/Encoding ne and
					{def}
					{pop pop}
				ifelse
				}
			forall
			/FontReferenced exch def
			/Encoding exch dup length array copy def
			/FontName 1 index dup type/stringtype eq{cvn}if def dup
			currentdict
			end
		definefont ct_VMDictPut
		setglobal
		}
	ifelse
	}bind def
/SetSubstituteStrategy
	{
	$SubstituteFont
		begin
		dup type/dicttype ne
			{0 dict}
		if
		currentdict/$Strategies known
			{
			exch $Strategies exch 
			2 copy known
				{
				get
				2 copy maxlength exch maxlength add dict
					begin
					{def}forall
					{def}forall
					currentdict
					dup/$Init known
						{dup/$Init get exec}
					if
					end
				/$Strategy exch def
				}
				{pop pop pop}
			ifelse
			}
			{pop pop}
		ifelse
		end
	}bind def
/scff
	{
	$SubstituteFont
		begin
		dup type/stringtype eq
			{dup length exch}
			{null}
		ifelse
		/$sname exch def
		/$slen exch def
		/$inVMIndex
			$sname null eq
				{
				1 index $str cvs
				dup length $slen sub $slen getinterval cvn
				}
				{$sname}
			ifelse def
		end
		{findfont}
	@Stopped
		{
		dup length 8 add string exch
		1 index 0(BadFont:)putinterval
		1 index exch 8 exch dup length string cvs putinterval cvn
			{findfont}
		@Stopped
			{pop/Courier findfont}
		if
		}
	if
	$SubstituteFont
		begin
		/$sname null def
		/$slen 0 def
		/$inVMIndex null def
		end
	}bind def
/isWidthsOnlyFont
	{
	dup/WidthsOnly known
		{pop pop true}
		{
		dup/FDepVector known
			{/FDepVector get{isWidthsOnlyFont dup{exit}if}forall}
			{
			dup/FDArray known
				{/FDArray get{isWidthsOnlyFont dup{exit}if}forall}
				{pop}
			ifelse
			}
		ifelse
		}
	ifelse
	}bind def
/ct_StyleDicts 4 dict dup begin
		 /Adobe-Japan1 4 dict dup begin
					 Level2?
								{
								/Serif
								/HeiseiMin-W3-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMin-W3}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMin-W3/CIDFont resourcestatus
								{pop pop/HeiseiMin-W3}
								{/Ryumin-Light}
								ifelse
							}
							{/Ryumin-Light}
							ifelse
								}
								ifelse
								def
								/SansSerif
								/HeiseiKakuGo-W5-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiKakuGo-W5/CIDFont resourcestatus
								{pop pop/HeiseiKakuGo-W5}
								{/GothicBBB-Medium}
								ifelse
							}
							{/GothicBBB-Medium}
							ifelse
								}
								ifelse
								def
								/HeiseiMaruGo-W4-83pv-RKSJ-H/Font resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
							/CIDFont/Category resourcestatus
							{
								pop pop
								/HeiseiMaruGo-W4/CIDFont resourcestatus
								{pop pop/HeiseiMaruGo-W4}
								{
									/Jun101-Light-RKSJ-H/Font resourcestatus
									{pop pop/Jun101-Light}
									{SansSerif}
									ifelse
								}
								ifelse
							}
							{
								/Jun101-Light-RKSJ-H/Font resourcestatus
								{pop pop/Jun101-Light}
								{SansSerif}
								ifelse
							}
							ifelse
								}
								ifelse
								/RoundSansSerif exch def
								/Default Serif def
								}
								{
								/Serif/Ryumin-Light def
								/SansSerif/GothicBBB-Medium def
								{
								(fonts/Jun101-Light-83pv-RKSJ-H)status
								}stopped
								{pop}{
										 {pop pop pop pop/Jun101-Light}
										 {SansSerif}
										 ifelse
										 /RoundSansSerif exch def
								}ifelse
								/Default Serif def
								}
					 ifelse
		 end
		 def
		 /Adobe-Korea1 4 dict dup begin
					/Serif/HYSMyeongJo-Medium def
					/SansSerif/HYGoThic-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-GB1 4 dict dup begin
					/Serif/STSong-Light def
					/SansSerif/STHeiti-Regular def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
		 /Adobe-CNS1 4 dict dup begin
					/Serif/MKai-Medium def
					/SansSerif/MHei-Medium def
					/RoundSansSerif SansSerif def
					/Default Serif def
		 end
		 def
end
def
Level2?{currentglobal true setglobal}if
/ct_BoldRomanWidthProc 
	{
	stringwidth 1 index 0 ne{exch .03 add exch}if setcharwidth
	0 0
	}bind def
/ct_Type0WidthProc 
	{
	 dup stringwidth 0 0 moveto 
	 2 index true charpath pathbbox
	 0 -1 
	 7 index 2 div .88 
	 setcachedevice2
	 pop
	0 0
	}bind def
/ct_Type0WMode1WidthProc 
	{
	 dup stringwidth 
	 pop 2 div neg -0.88
	2 copy
	moveto 
	0 -1
	 5 -1 roll true charpath pathbbox
	 setcachedevice
	}bind def
/cHexEncoding
[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
/ct_BoldBaseFont 
	 11 dict begin
		/FontType 3 def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/Encoding cHexEncoding def 
		/_setwidthProc/ct_BoldRomanWidthProc load def
		/_bcstr1 1 string def
		/BuildChar
		{
			exch begin
				_basefont setfont
				_bcstr1 dup 0 4 -1 roll put
				dup 
				_setwidthProc
				3 copy 
				moveto				
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
systemdict/composefont known
{
/ct_DefineIdentity-H
{
	/Identity-H/CMap resourcestatus
	{
		pop pop
	}
	{
		/CIDInit/ProcSet findresource begin
		 12 dict begin
		 begincmap
		 /CIDSystemInfo 3 dict dup begin
			 /Registry(Adobe)def
			 /Ordering(Identity)def
			 /Supplement 0 def
		 end def
		 /CMapName/Identity-H def
		 /CMapVersion 1.000 def
		 /CMapType 1 def
		 1 begincodespacerange
		 <0000><FFFF>
		 endcodespacerange
		 1 begincidrange
		 <0000><FFFF>0
		 endcidrange
		 endcmap
		 CMapName currentdict/CMap defineresource pop
		 end
		 end
	 }
	 ifelse
}
def
/ct_BoldBaseCIDFont 
	 11 dict begin
		/CIDFontType 1 def
		/CIDFontName/ct_BoldBaseCIDFont def
		/FontMatrix[1 0 0 1 0 0]def
		/FontBBox[0 0 1 1]def
		/_setwidthProc/ct_Type0WidthProc load def
		/_bcstr2 2 string def
		/BuildGlyph
		{
			exch begin		 
				_basefont setfont
				_bcstr2 1 2 index 256 mod put
				_bcstr2 0 3 -1 roll 256 idiv put
				_bcstr2 dup _setwidthProc		 
				3 copy 
				moveto
				show
				_basefonto setfont
				moveto
				show
			end
		}bind def
		 currentdict
	 end 
def
}if
Level2?{setglobal}if
/ct_CopyFont{
	{
		1 index/FID ne 2 index/UniqueID ne and
		{def}{pop pop}ifelse
	}forall
}bind def
/ct_Type0CopyFont 
{
	exch
	dup length dict
	begin
	ct_CopyFont
	[
	exch
	FDepVector 
	{
		 dup/FontType get 0 eq
		{	
		1 index ct_Type0CopyFont 
		/_ctType0 exch definefont
		}
		{
		/_ctBaseFont exch
		2 index exec
		}
		 ifelse 
		 exch
	}
	forall 
	pop
	]				
	/FDepVector exch def
	currentdict
	end
}bind def
/ct_MakeBoldFont
{
	 dup/ct_SyntheticBold known
	{
		dup length 3 add dict begin 
		ct_CopyFont 
		/ct_StrokeWidth .03 0 FontMatrix idtransform pop def 
		/ct_SyntheticBold true def
		currentdict 
		end 
		definefont
	}
	{
		dup dup length 3 add dict
		begin
			ct_CopyFont
			/PaintType 2 def
			/StrokeWidth .03 0 FontMatrix idtransform pop def
			/dummybold currentdict
		end
		definefont
		dup/FontType get dup 9 ge exch 11 le and 
		{
			ct_BoldBaseCIDFont
			dup length 3 add dict copy begin
			dup/CIDSystemInfo get/CIDSystemInfo exch def
			ct_DefineIdentity-H
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefont exch def
			/_Type0Identity/Identity-H 3 -1 roll[exch]composefont
			/_basefonto exch def
			currentdict
			end
			/CIDFont defineresource
		}
		{
			ct_BoldBaseFont
			dup length 3 add dict copy begin
			/_basefont exch def
			/_basefonto exch def
			currentdict
			end
			definefont
		}
		ifelse
	}
	ifelse
}bind def
/ct_MakeBold{
	1 index 
	1 index
	findfont
	currentglobal 5 1 roll
	dup gcheck setglobal
		dup
		 /FontType get 0 eq
			{
				dup/WMode known{dup/WMode get 1 eq}{false}ifelse
				version length 4 ge
				and
					{version 0 4 getinterval cvi 2015 ge}
					{true}
				ifelse 
					{/ct_Type0WidthProc}
					{/ct_Type0WMode1WidthProc}
				ifelse
				ct_BoldBaseFont/_setwidthProc 3 -1 roll load put
						{ct_MakeBoldFont}ct_Type0CopyFont definefont
			}
			{
				dup/_fauxfont known not 1 index/SubstMaster known not and
				{
					 ct_BoldBaseFont/_setwidthProc /ct_BoldRomanWidthProc load put
					 ct_MakeBoldFont 
				}
				{
				2 index 2 index eq
					{exch pop	}
					{
						dup length dict begin
						ct_CopyFont
						currentdict
						end
						definefont 
					}
				ifelse
				}
			ifelse
			}
		 ifelse
		 pop pop pop
		 setglobal
}bind def
/?str1 256 string def
/?set
	{
	$SubstituteFont
		begin
		/$substituteFound false def
		/$fontname 1 index def
		/$doSmartSub false def
		end
	dup
	 findfont
	$SubstituteFont
		begin
		$substituteFound
			{false}
			{
			dup/FontName known
				{
				dup/FontName get $fontname eq
				1 index/DistillerFauxFont known not and
				/currentdistillerparams where
					{pop false 2 index isWidthsOnlyFont not and}
				if
				}
				{false}
			ifelse
			}
		ifelse
		exch pop
		/$doSmartSub true def
		end
		{
		5 1 roll pop pop pop pop
		findfont
		}
		{
		1 index
		findfont
		dup/FontType get 3 eq
		{
			6 1 roll pop pop pop pop pop false
		}
		{pop true}
		ifelse
		{
		$SubstituteFont
		begin
		pop pop
		/$styleArray 1 index def
		/$regOrdering 2 index def
		pop pop
		0 1 $styleArray length 1 sub
		{
			$styleArray exch get
			ct_StyleDicts $regOrdering
			2 copy known
			{
				get
				exch 2 copy known not
				{pop/Default}
				if
				get
				dup type/nametype eq
				{
				?str1 cvs length dup 1 add exch
				?str1 exch(-)putinterval
				exch dup length exch ?str1 exch 3 index exch putinterval
				add ?str1 exch 0 exch getinterval cvn
				}
				{
				pop pop/Unknown
				}
				ifelse
			}
			{
				pop pop pop pop/Unknown
			}
			ifelse
		}
		for
		end
		findfont 
		}if
		}
	ifelse
	currentglobal false setglobal 3 1 roll
	null copyfont definefont pop
	setglobal
	}bind def
setpacking
userdict/$SubstituteFont 25 dict put
1 dict
	begin
	/SubstituteFont
		dup $error exch 2 copy known
			{get}
			{pop pop{pop/Courier}bind}
		ifelse def
	/currentdistillerparams where dup
		{
		pop pop
		currentdistillerparams/CannotEmbedFontPolicy 2 copy known
			{get/Error eq}
			{pop pop false}
		ifelse
		}
	if not
		{
		countdictstack array dictstack 0 get
			begin
			userdict
				begin
				$SubstituteFont
					begin
					/$str 128 string def
					/$fontpat 128 string def
					/$slen 0 def
					/$sname null def
					/$match false def
					/$fontname null def
					/$substituteFound false def
					/$inVMIndex null def
					/$doSmartSub true def
					/$depth 0 def
					/$fontname null def
					/$italicangle 26.5 def
					/$dstack null def
					/$Strategies 10 dict dup
						begin
						/$Type3Underprint
							{
							currentglobal exch false setglobal
							11 dict
								begin
								/UseFont exch
									$WMode 0 ne
										{
										dup length dict copy
										dup/WMode $WMode put
										/UseFont exch definefont
										}
									if def
								/FontName $fontname dup type/stringtype eq{cvn}if def
								/FontType 3 def
								/FontMatrix[.001 0 0 .001 0 0]def
								/Encoding 256 array dup 0 1 255{/.notdef put dup}for pop def
								/FontBBox[0 0 0 0]def
								/CCInfo 7 dict dup
									begin
									/cc null def
									/x 0 def
									/y 0 def
									end def
								/BuildChar
									{
									exch
										begin
										CCInfo
											begin
											1 string dup 0 3 index put exch pop
											/cc exch def
											UseFont 1000 scalefont setfont
											cc stringwidth/y exch def/x exch def
											x y setcharwidth
											$SubstituteFont/$Strategy get/$Underprint get exec
											0 0 moveto cc show
											x y moveto
											end
										end
									}bind def
								currentdict
								end
							exch setglobal
							}bind def
						/$GetaTint
							2 dict dup
								begin
								/$BuildFont
									{
									dup/WMode known
										{dup/WMode get}
										{0}
									ifelse
									/$WMode exch def
									$fontname exch
									dup/FontName known
										{
										dup/FontName get
										dup type/stringtype eq{cvn}if
										}
										{/unnamedfont}
									ifelse
									exch
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
									1 index/FontName get known
										{
										pop
										Adobe_CoolType_Data/InVMDeepCopiedFonts get
										1 index get
										null copyfont
										}
										{$deepcopyfont}
									ifelse
									exch 1 index exch/FontBasedOn exch put
									dup/FontName $fontname dup type/stringtype eq{cvn}if put
									definefont
									Adobe_CoolType_Data/InVMDeepCopiedFonts get
										begin
										dup/FontBasedOn get 1 index def
										end
									}bind def
								/$Underprint
									{
									gsave
									x abs y abs gt
										{/y 1000 def}
										{/x -1000 def 500 120 translate}
									ifelse
									Level2?
										{
										[/Separation(All)/DeviceCMYK{0 0 0 1 pop}]
										setcolorspace
										}
										{0 setgray}
									ifelse
									10 setlinewidth
									x .8 mul
									[7 3]
										{
										y mul 8 div 120 sub x 10 div exch moveto
										0 y 4 div neg rlineto
										dup 0 rlineto
										0 y 4 div rlineto
										closepath
										gsave
										Level2?
											{.2 setcolor}
											{.8 setgray}
										ifelse
										fill grestore
										stroke
										}
									forall
									pop
									grestore
									}bind def
								end def
						/$Oblique
							1 dict dup
								begin
								/$BuildFont
									{
									currentglobal exch dup gcheck setglobal
									null copyfont
										begin
										/FontBasedOn
										currentdict/FontName known
											{
											FontName
											dup type/stringtype eq{cvn}if
											}
											{/unnamedfont}
										ifelse
										def
										/FontName $fontname dup type/stringtype eq{cvn}if def
										/currentdistillerparams where
											{pop}
											{
											/FontInfo currentdict/FontInfo known
												{FontInfo null copyfont}
												{2 dict}
											ifelse
											dup
												begin
												/ItalicAngle $italicangle def
												/FontMatrix FontMatrix
												[1 0 ItalicAngle dup sin exch cos div 1 0 0]
												matrix concatmatrix readonly
												end
											4 2 roll def
											def
											}
										ifelse
										FontName currentdict
										end
									definefont
									exch setglobal
									}bind def
								end def
						/$None
							1 dict dup
								begin
								/$BuildFont{}bind def
								end def
						end def
					/$Oblique SetSubstituteStrategy
					/$findfontByEnum
						{
						dup type/stringtype eq{cvn}if
						dup/$fontname exch def
						$sname null eq
							{$str cvs dup length $slen sub $slen getinterval}
							{pop $sname}
						ifelse
						$fontpat dup 0(fonts/*)putinterval exch 7 exch putinterval
						/$match false def
						$SubstituteFont/$dstack countdictstack array dictstack put
						mark
							{
							$fontpat 0 $slen 7 add getinterval
								{/$match exch def exit}
							$str filenameforall
							}
						stopped
							{
							cleardictstack
							currentdict
							true
							$SubstituteFont/$dstack get
								{
								exch
									{
									1 index eq
										{pop false}
										{true}
									ifelse
									}
									{begin false}
								ifelse
								}
							forall
							pop
							}
						if
						cleartomark
						/$slen 0 def
						$match false ne
							{$match(fonts/)anchorsearch pop pop cvn}
							{/Courier}
						ifelse
						}bind def
					/$ROS 1 dict dup
						begin
						/Adobe 4 dict dup
							begin
							/Japan1 [/Ryumin-Light/HeiseiMin-W3
										 /GothicBBB-Medium/HeiseiKakuGo-W5
										 /HeiseiMaruGo-W4/Jun101-Light]def
							/Korea1 [/HYSMyeongJo-Medium/HYGoThic-Medium]def
							/GB1	 [/STSong-Light/STHeiti-Regular]def
							/CNS1	[/MKai-Medium/MHei-Medium]def
							end def
						end def
					/$cmapname null def
					/$deepcopyfont
						{
						dup/FontType get 0 eq
							{
							1 dict dup/FontName/copied put copyfont
								begin
								/FDepVector FDepVector copyarray
								0 1 2 index length 1 sub
									{
									2 copy get $deepcopyfont
									dup/FontName/copied put
									/copied exch definefont
									3 copy put pop pop
									}
								for
								def
								currentdict
								end
							}
							{$Strategies/$Type3Underprint get exec}
						ifelse
						}bind def
					/$buildfontname
						{
						dup/CIDFont findresource/CIDSystemInfo get
							begin
							Registry length Ordering length Supplement 8 string cvs
							3 copy length 2 add add add string
							dup 5 1 roll dup 0 Registry putinterval
							dup 4 index(-)putinterval
							dup 4 index 1 add Ordering putinterval
							4 2 roll add 1 add 2 copy(-)putinterval
							end
						1 add 2 copy 0 exch getinterval $cmapname $fontpat cvs exch
						anchorsearch
							{pop pop 3 2 roll putinterval cvn/$cmapname exch def}
							{pop pop pop pop pop}
						ifelse
						length
						$str 1 index(-)putinterval 1 add
						$str 1 index $cmapname $fontpat cvs putinterval
						$cmapname length add
						$str exch 0 exch getinterval cvn
						}bind def
					/$findfontByROS
						{
						/$fontname exch def
						$ROS Registry 2 copy known
							{
							get Ordering 2 copy known
								{get}
								{pop pop[]}
							ifelse
							}
							{pop pop[]}
						ifelse
						false exch
							{
							dup/CIDFont resourcestatus
								{
								pop pop
								save
								1 index/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get}
									{false}
								ifelse
								exch pop
								exch restore
									{pop}
									{exch pop true exit}
								ifelse
								}
								{pop}
							ifelse
							}
						forall
							{$str cvs $buildfontname}
							{
							false(*)
								{
								save exch
								dup/CIDFont findresource
								dup/WidthsOnly known
									{dup/WidthsOnly get not}
									{true}
								ifelse
								exch/CIDSystemInfo get
								dup/Registry get Registry eq
								exch/Ordering get Ordering eq and and
									{exch restore exch pop true exit}
									{pop restore}
								ifelse
								}
							$str/CIDFont resourceforall
								{$buildfontname}
								{$fontname $findfontByEnum}
							ifelse
							}
						ifelse
						}bind def
					end
				end
				currentdict/$error known currentdict/languagelevel known and dup
					{pop $error/SubstituteFont known}
				if
				dup
					{$error}
					{Adobe_CoolType_Core}
				ifelse
				begin
					{
					/SubstituteFont
					/CMap/Category resourcestatus
						{
						pop pop
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{
								$sname null eq
									{dup $str cvs dup length $slen sub $slen getinterval cvn}
									{$sname}
								ifelse
								Adobe_CoolType_Data/InVMFontsByCMap get
								1 index 2 copy known
									{
									get
									false exch
										{
										pop
										currentglobal
											{
											GlobalFontDirectory 1 index known
												{exch pop true exit}
												{pop}
											ifelse
											}
											{
											FontDirectory 1 index known
												{exch pop true exit}
												{
												GlobalFontDirectory 1 index known
													{exch pop true exit}
													{pop}
												ifelse
												}
											ifelse
											}
										ifelse
										}
									forall
									}
									{pop pop false}
								ifelse
									{
									exch pop exch pop
									}
									{
									dup/CMap resourcestatus
										{
										pop pop
										dup/$cmapname exch def
										/CMap findresource/CIDSystemInfo get{def}forall
										$findfontByROS
										}
										{
										128 string cvs
										dup(-)search
											{
											3 1 roll search
												{
												3 1 roll pop
													{dup cvi}
												stopped
													{pop pop pop pop pop $findfontByEnum}
													{
													4 2 roll pop pop
													exch length
													exch
													2 index length
													2 index
													sub
													exch 1 sub -1 0
														{
														$str cvs dup length
														4 index
														0
														4 index
														4 3 roll add
														getinterval
														exch 1 index exch 3 index exch
														putinterval
														dup/CMap resourcestatus
															{
															pop pop
															4 1 roll pop pop pop
															dup/$cmapname exch def
															/CMap findresource/CIDSystemInfo get{def}forall
															$findfontByROS
															true exit
															}
															{pop}
														ifelse
														}
													for
													dup type/booleantype eq
														{pop}
														{pop pop pop $findfontByEnum}
													ifelse
													}
												ifelse
												}
												{pop pop pop $findfontByEnum}
											ifelse
											}
											{pop pop $findfontByEnum}
										ifelse
										}
									ifelse
									}
								ifelse
								}
								{//SubstituteFont exec}
							ifelse
							/$slen 0 def
							end
						}
						}
						{
						{
						$SubstituteFont
							begin
							/$substituteFound true def
							dup length $slen gt
							$sname null ne or
							$slen 0 gt and
								{$findfontByEnum}
								{//SubstituteFont exec}
							ifelse
							end
						}
						}
					ifelse
					bind readonly def
					Adobe_CoolType_Core/scfindfont/systemfindfont load put
					}
					{
					/scfindfont
						{
						$SubstituteFont
							begin
							dup systemfindfont
							dup/FontName known
								{dup/FontName get dup 3 index ne}
								{/noname true}
							ifelse
							dup
								{
								/$origfontnamefound 2 index def
								/$origfontname 4 index def/$substituteFound true def
								}
							if
							exch pop
								{
								$slen 0 gt
								$sname null ne
								3 index length $slen gt or and
									{
									pop dup $findfontByEnum findfont
									dup maxlength 1 add dict
										begin
											{1 index/FID eq{pop pop}{def}ifelse}
										forall
										currentdict
										end
									definefont
									dup/FontName known{dup/FontName get}{null}ifelse
									$origfontnamefound ne
										{
										$origfontname $str cvs print
										( substitution revised, using )print
										dup/FontName known
											{dup/FontName get}{(unspecified font)}
										ifelse
										$str cvs print(.\n)print
										}
									if
									}
									{exch pop}
								ifelse
								}
								{exch pop}
							ifelse
							end
						}bind def
					}
				ifelse
				end
			end
		Adobe_CoolType_Core_Defined not
			{
			Adobe_CoolType_Core/findfont
				{
				$SubstituteFont
					begin
					$depth 0 eq
						{
						/$fontname 1 index dup type/stringtype ne{$str cvs}if def
						/$substituteFound false def
						}
					if
					/$depth $depth 1 add def
					end
				scfindfont
				$SubstituteFont
					begin
					/$depth $depth 1 sub def
					$substituteFound $depth 0 eq and
						{
						$inVMIndex null ne
							{dup $inVMIndex $AddInVMFont}
						if
						$doSmartSub
							{
							currentdict/$Strategy known
								{$Strategy/$BuildFont get exec}
							if
							}
						if
						}
					if
					end
				}bind put
			}
		if
		}
	if
	end
/$AddInVMFont
	{
	exch/FontName 2 copy known
		{
		get
		1 dict dup begin exch 1 index gcheck def end exch
		Adobe_CoolType_Data/InVMFontsByCMap get exch
		$DictAdd
		}
		{pop pop pop}
	ifelse
	}bind def
/$DictAdd
	{
	2 copy known not
		{2 copy 4 index length dict put}
	if
	Level2? not
		{
		2 copy get dup maxlength exch length 4 index length add lt
		2 copy get dup length 4 index length add exch maxlength 1 index lt
			{
			2 mul dict
				begin
				2 copy get{forall}def
				2 copy currentdict put
				end
			}
			{pop}
		ifelse
		}
	if
	get
		begin
			{def}
		forall
		end
	}bind def
end
end
%%EndResource
currentglobal true setglobal
%%BeginResource: procset Adobe_CoolType_Utility_MAKEOCF 1.23 0
%%Copyright: Copyright 1987-2006 Adobe Systems Incorporated.
%%Version: 1.23 0
systemdict/languagelevel known dup
	{currentglobal false setglobal}
	{false}
ifelse
exch
userdict/Adobe_CoolType_Utility 2 copy known
	{2 copy get dup maxlength 27 add dict copy}
	{27 dict}
ifelse put
Adobe_CoolType_Utility
	begin
	/@eexecStartData
		 <BAB431EA07F209EB8C4348311481D9D3F76E3D15246555577D87BC510ED54E
		 118C39697FA9F6DB58128E60EB8A12FA24D7CDD2FA94D221FA9EC8DA3E5E6A1C
		 4ACECC8C2D39C54E7C946031DD156C3A6B4A09AD29E1867A>def
	/@recognizeCIDFont null def
	/ct_Level2? exch def
	/ct_Clone? 1183615869 internaldict dup
			/CCRun known not
			exch/eCCRun known not
			ct_Level2? and or def
ct_Level2?
	{globaldict begin currentglobal true setglobal}
if
	/ct_AddStdCIDMap
		ct_Level2?
			{{
				mark
				Adobe_CoolType_Utility/@recognizeCIDFont currentdict put
					{
					((Hex)57 StartData
					 0615 1e27 2c39 1c60 d8a8 cc31 fe2b f6e0
					 7aa3 e541 e21c 60d8 a8c9 c3d0 6d9e 1c60
					 d8a8 c9c2 02d7 9a1c 60d8 a849 1c60 d8a8
					 cc36 74f4 1144 b13b 77)0()/SubFileDecode filter cvx exec
					}
				stopped
					{
					 cleartomark
					 Adobe_CoolType_Utility/@recognizeCIDFont get
					 countdictstack dup array dictstack
					 exch 1 sub -1 0
						 {
						 2 copy get 3 index eq
								{1 index length exch sub 1 sub{end}repeat exit}
								{pop}
						 ifelse
						 }
					 for
					 pop pop
					 Adobe_CoolType_Utility/@eexecStartData get eexec
					}
					{cleartomark}
				ifelse
			}}
			{{
				Adobe_CoolType_Utility/@eexecStartData get eexec
			}}
		ifelse bind def
userdict/cid_extensions known
dup{cid_extensions/cid_UpdateDB known and}if
	{
	 cid_extensions
	 begin
	/cid_GetCIDSystemInfo
		{
		 1 index type/stringtype eq
			{exch cvn exch}
		 if
		 cid_extensions
			 begin
			 dup load 2 index known
				{
				 2 copy
				 cid_GetStatusInfo
				 dup null ne
					{
					 1 index load
					 3 index get
					 dup null eq
						 {pop pop cid_UpdateDB}
						 {
						 exch
						 1 index/Created get eq
							 {exch pop exch pop}
							 {pop cid_UpdateDB}
						 ifelse
						 }
					 ifelse
					}
					{pop cid_UpdateDB}
				 ifelse
				}
				{cid_UpdateDB}
			 ifelse
			 end
		}bind def
	 end
	}
if
ct_Level2?
	{end setglobal}
if
	/ct_UseNativeCapability? systemdict/composefont known def
	/ct_MakeOCF 35 dict def
	/ct_Vars 25 dict def
	/ct_GlyphDirProcs 6 dict def
	/ct_BuildCharDict 15 dict dup
		begin
		/charcode 2 string def
		/dst_string 1500 string def
		/nullstring()def
		/usewidths? true def
		end def
	ct_Level2?{setglobal}{pop}ifelse
	ct_GlyphDirProcs
		begin
		/GetGlyphDirectory
			{
			systemdict/languagelevel known
				{pop/CIDFont findresource/GlyphDirectory get}
				{
				1 index/CIDFont findresource/GlyphDirectory
				get dup type/dicttype eq
					{
					dup dup maxlength exch length sub 2 index lt
						{
						dup length 2 index add dict copy 2 index
						/CIDFont findresource/GlyphDirectory 2 index put
						}
					if
					}
				if
				exch pop exch pop
				}
			ifelse
			+
			}def
		/+
			{
			systemdict/languagelevel known
				{
				currentglobal false setglobal
				3 dict begin
					/vm exch def
				}
				{1 dict begin}
			ifelse
			/$ exch def
			systemdict/languagelevel known
				{
				vm setglobal
				/gvm currentglobal def
				$ gcheck setglobal
				}
			if
			?{$ begin}if
			}def
		/?{$ type/dicttype eq}def
		/|{
			userdict/Adobe_CoolType_Data known
				{
			Adobe_CoolType_Data/AddWidths? known
				{
				 currentdict Adobe_CoolType_Data
					begin
					 begin
						AddWidths?
								{
								Adobe_CoolType_Data/CC 3 index put
								?{def}{$ 3 1 roll put}ifelse
								CC charcode exch 1 index 0 2 index 256 idiv put
								1 index exch 1 exch 256 mod put
								stringwidth 2 array astore
								currentfont/Widths get exch CC exch put
								}
								{?{def}{$ 3 1 roll put}ifelse}
							ifelse
					end
				end
				}
				{?{def}{$ 3 1 roll put}ifelse}	ifelse
				}
				{?{def}{$ 3 1 roll put}ifelse}
			ifelse
			}def
		/!
			{
			?{end}if
			systemdict/languagelevel known
				{gvm setglobal}
			if
			end
			}def
		/:{string currentfile exch readstring pop}executeonly def
		end
	ct_MakeOCF
		begin
		/ct_cHexEncoding
		[/c00/c01/c02/c03/c04/c05/c06/c07/c08/c09/c0A/c0B/c0C/c0D/c0E/c0F/c10/c11/c12
		/c13/c14/c15/c16/c17/c18/c19/c1A/c1B/c1C/c1D/c1E/c1F/c20/c21/c22/c23/c24/c25
		/c26/c27/c28/c29/c2A/c2B/c2C/c2D/c2E/c2F/c30/c31/c32/c33/c34/c35/c36/c37/c38
		/c39/c3A/c3B/c3C/c3D/c3E/c3F/c40/c41/c42/c43/c44/c45/c46/c47/c48/c49/c4A/c4B
		/c4C/c4D/c4E/c4F/c50/c51/c52/c53/c54/c55/c56/c57/c58/c59/c5A/c5B/c5C/c5D/c5E
		/c5F/c60/c61/c62/c63/c64/c65/c66/c67/c68/c69/c6A/c6B/c6C/c6D/c6E/c6F/c70/c71
		/c72/c73/c74/c75/c76/c77/c78/c79/c7A/c7B/c7C/c7D/c7E/c7F/c80/c81/c82/c83/c84
		/c85/c86/c87/c88/c89/c8A/c8B/c8C/c8D/c8E/c8F/c90/c91/c92/c93/c94/c95/c96/c97
		/c98/c99/c9A/c9B/c9C/c9D/c9E/c9F/cA0/cA1/cA2/cA3/cA4/cA5/cA6/cA7/cA8/cA9/cAA
		/cAB/cAC/cAD/cAE/cAF/cB0/cB1/cB2/cB3/cB4/cB5/cB6/cB7/cB8/cB9/cBA/cBB/cBC/cBD
		/cBE/cBF/cC0/cC1/cC2/cC3/cC4/cC5/cC6/cC7/cC8/cC9/cCA/cCB/cCC/cCD/cCE/cCF/cD0
		/cD1/cD2/cD3/cD4/cD5/cD6/cD7/cD8/cD9/cDA/cDB/cDC/cDD/cDE/cDF/cE0/cE1/cE2/cE3
		/cE4/cE5/cE6/cE7/cE8/cE9/cEA/cEB/cEC/cED/cEE/cEF/cF0/cF1/cF2/cF3/cF4/cF5/cF6
		/cF7/cF8/cF9/cFA/cFB/cFC/cFD/cFE/cFF]def
		/ct_CID_STR_SIZE 8000 def
		/ct_mkocfStr100 100 string def
		/ct_defaultFontMtx[.001 0 0 .001 0 0]def
		/ct_1000Mtx[1000 0 0 1000 0 0]def
		/ct_raise{exch cvx exch errordict exch get exec stop}bind def
		/ct_reraise
			{cvx $error/errorname get(Error: )print dup(						 )cvs print
					errordict exch get exec stop
			}bind def
		/ct_cvnsi
			{
			1 index add 1 sub 1 exch 0 4 1 roll
				{
				2 index exch get
				exch 8 bitshift
				add
				}
			for
			exch pop
			}bind def
		/ct_GetInterval
			{
			Adobe_CoolType_Utility/ct_BuildCharDict get
				begin
				/dst_index 0 def
				dup dst_string length gt
					{dup string/dst_string exch def}
				if
				1 index ct_CID_STR_SIZE idiv
				/arrayIndex exch def
				2 index arrayIndex get
				2 index
				arrayIndex ct_CID_STR_SIZE mul
				sub
					{
					dup 3 index add 2 index length le
						{
						2 index getinterval
						dst_string dst_index 2 index putinterval
						length dst_index add/dst_index exch def
						exit
						}
						{
						1 index length 1 index sub
						dup 4 1 roll
						getinterval
						dst_string dst_index 2 index putinterval
						pop dup dst_index add/dst_index exch def
						sub
						/arrayIndex arrayIndex 1 add def
						2 index dup length arrayIndex gt
							 {arrayIndex get}
							 {
							 pop
							 exit
							 }
						ifelse
						0
						}
					ifelse
					}
				loop
				pop pop pop
				dst_string 0 dst_index getinterval
				end
			}bind def
		ct_Level2?
			{
			/ct_resourcestatus
			currentglobal mark true setglobal
				{/unknowninstancename/Category resourcestatus}
			stopped
				{cleartomark setglobal true}
				{cleartomark currentglobal not exch setglobal}
			ifelse
				{
					{
					mark 3 1 roll/Category findresource
						begin
						ct_Vars/vm currentglobal put
						({ResourceStatus}stopped)0()/SubFileDecode filter cvx exec
							{cleartomark false}
							{{3 2 roll pop true}{cleartomark false}ifelse}
						ifelse
						ct_Vars/vm get setglobal
						end
					}
				}
				{{resourcestatus}}
			ifelse bind def
			/CIDFont/Category ct_resourcestatus
				{pop pop}
				{
				currentglobal true setglobal
				/Generic/Category findresource
				dup length dict copy
				dup/InstanceType/dicttype put
				/CIDFont exch/Category defineresource pop
				setglobal
				}
			ifelse
			ct_UseNativeCapability?
				{
				/CIDInit/ProcSet findresource begin
				12 dict begin
				begincmap
				/CIDSystemInfo 3 dict dup begin
				 /Registry(Adobe)def
				 /Ordering(Identity)def
				 /Supplement 0 def
				end def
				/CMapName/Identity-H def
				/CMapVersion 1.000 def
				/CMapType 1 def
				1 begincodespacerange
				<0000><FFFF>
				endcodespacerange
				1 begincidrange
				<0000><FFFF>0
				endcidrange
				endcmap
				CMapName currentdict/CMap defineresource pop
				end
				end
				}
			if
			}
			{
			/ct_Category 2 dict begin
			/CIDFont 10 dict def
			/ProcSet	2 dict def
			currentdict
			end
			def
			/defineresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					dup dup maxlength exch length eq
						{
						dup length 10 add dict copy
						ct_Category 2 index 2 index put
						}
					if
					3 index 3 index put
					pop exch pop
					}
					{pop pop/defineresource/undefined ct_raise}
				ifelse
				}bind def
			/findresource
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index 2 copy known
						{get 3 1 roll pop pop}
						{pop pop/findresource/undefinedresource ct_raise}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/resourcestatus
				{
				ct_Category 1 index 2 copy known
					{
					get
					2 index known
					exch pop exch pop
						{
						0 -1 true
						}
						{
						false
						}
					ifelse
					}
					{pop pop/findresource/undefined ct_raise}
				ifelse
				}bind def
			/ct_resourcestatus/resourcestatus load def
			}
		ifelse
		/ct_CIDInit 2 dict
			begin
			/ct_cidfont_stream_init
				{
					{
					dup(Binary)eq
						{
						pop
						null
						currentfile
						ct_Level2?
							{
								{cid_BYTE_COUNT()/SubFileDecode filter}
							stopped
								{pop pop pop}
							if
							}
						if
						/readstring load
						exit
						}
					if
					dup(Hex)eq
						{
						pop
						currentfile
						ct_Level2?
							{
								{null exch/ASCIIHexDecode filter/readstring}
							stopped
								{pop exch pop(>)exch/readhexstring}
							if
							}
							{(>)exch/readhexstring}
						ifelse
						load
						exit
						}
					if
					/StartData/typecheck ct_raise
					}
				loop
				cid_BYTE_COUNT ct_CID_STR_SIZE le
					{
					2 copy cid_BYTE_COUNT string exch exec
					pop
					1 array dup
					3 -1 roll
					0 exch put
					}
					{
					cid_BYTE_COUNT ct_CID_STR_SIZE div ceiling cvi
					dup array exch 2 sub 0 exch 1 exch
						{
						2 copy
						5 index
						ct_CID_STR_SIZE
						string
						6 index exec
						pop
						put
						pop
						}
					for
					2 index
					cid_BYTE_COUNT ct_CID_STR_SIZE mod string
					3 index exec
					pop
					1 index exch
					1 index length 1 sub
					exch put
					}
				ifelse
				cid_CIDFONT exch/GlyphData exch put
				2 index null eq
					{
					pop pop pop
					}
					{
					pop/readstring load
					1 string exch
						{
						3 copy exec
						pop
						dup length 0 eq
							{
							pop pop pop pop pop
							true exit
							}
						if
						4 index
						eq
							{
							pop pop pop pop
							false exit
							}
						if
						}
					loop
					pop
					}
				ifelse
				}bind def
			/StartData
				{
				mark
					{
					currentdict
					dup/FDArray get 0 get/FontMatrix get
					0 get 0.001 eq
						{
						dup/CDevProc known not
							{
							/CDevProc 1183615869 internaldict/stdCDevProc 2 copy known
								{get}
								{
								pop pop
								{pop pop pop pop pop 0 -1000 7 index 2 div 880}
								}
							ifelse
							def
							}
						if
						}
						{
						/CDevProc
							{
							 pop pop pop pop pop
							 0
							 1 cid_temp/cid_CIDFONT get
							/FDArray get 0 get
							/FontMatrix get 0 get div
							 7 index 2 div
							 1 index 0.88 mul
							}def
						}
					ifelse
					/cid_temp 15 dict def
					cid_temp
						begin
						/cid_CIDFONT exch def
						3 copy pop
						dup/cid_BYTE_COUNT exch def 0 gt
							{
							ct_cidfont_stream_init
							FDArray
								{
								/Private get
								dup/SubrMapOffset known
									{
									begin
									/Subrs SubrCount array def
									Subrs
									SubrMapOffset
									SubrCount
									SDBytes
									ct_Level2?
										{
										currentdict dup/SubrMapOffset undef
										dup/SubrCount undef
										/SDBytes undef
										}
									if
									end
									/cid_SD_BYTES exch def
									/cid_SUBR_COUNT exch def
									/cid_SUBR_MAP_OFFSET exch def
									/cid_SUBRS exch def
									cid_SUBR_COUNT 0 gt
										{
										GlyphData cid_SUBR_MAP_OFFSET cid_SD_BYTES ct_GetInterval
										0 cid_SD_BYTES ct_cvnsi
										0 1 cid_SUBR_COUNT 1 sub
											{
											exch 1 index
											1 add
											cid_SD_BYTES mul cid_SUBR_MAP_OFFSET add
											GlyphData exch cid_SD_BYTES ct_GetInterval
											0 cid_SD_BYTES ct_cvnsi
											cid_SUBRS 4 2 roll
											GlyphData exch
											4 index
											1 index
											sub
											ct_GetInterval
											dup length string copy put
											}
										for
										pop
										}
									if
									}
									{pop}
								ifelse
								}
							forall
							}
						if
						cleartomark pop pop
						end
					CIDFontName currentdict/CIDFont defineresource pop
					end end
					}
				stopped
					{cleartomark/StartData ct_reraise}
				if
				}bind def
			currentdict
			end def
		/ct_saveCIDInit
			{
			/CIDInit/ProcSet ct_resourcestatus
				{true}
				{/CIDInitC/ProcSet ct_resourcestatus}
			ifelse
				{
				pop pop
				/CIDInit/ProcSet findresource
				ct_UseNativeCapability?
					{pop null}
					{/CIDInit ct_CIDInit/ProcSet defineresource pop}
				ifelse
				}
				{/CIDInit ct_CIDInit/ProcSet defineresource pop null}
			ifelse
			ct_Vars exch/ct_oldCIDInit exch put
			}bind def
		/ct_restoreCIDInit
			{
			ct_Vars/ct_oldCIDInit get dup null ne
				{/CIDInit exch/ProcSet defineresource pop}
				{pop}
			ifelse
			}bind def
		/ct_BuildCharSetUp
			{
			1 index
				begin
				CIDFont
					begin
					Adobe_CoolType_Utility/ct_BuildCharDict get
						begin
						/ct_dfCharCode exch def
						/ct_dfDict exch def
						CIDFirstByte ct_dfCharCode add
						dup CIDCount ge
							{pop 0}
						if
						/cid exch def
							{
							GlyphDirectory cid 2 copy known
								{get}
								{pop pop nullstring}
							ifelse
							dup length FDBytes sub 0 gt
								{
								dup
								FDBytes 0 ne
									{0 FDBytes ct_cvnsi}
									{pop 0}
								ifelse
								/fdIndex exch def
								dup length FDBytes sub FDBytes exch getinterval
								/charstring exch def
								exit
								}
								{
								pop
								cid 0 eq
									{/charstring nullstring def exit}
								if
								/cid 0 def
								}
							ifelse
							}
						loop
			}def
		/ct_SetCacheDevice
			{
			0 0 moveto
			dup stringwidth
			3 -1 roll
			true charpath
			pathbbox
			0 -1000
			7 index 2 div 880
			setcachedevice2
			0 0 moveto
			}def
		/ct_CloneSetCacheProc
			{
			1 eq
				{
				stringwidth
				pop -2 div -880
				0 -1000 setcharwidth
				moveto
				}
				{
				usewidths?
					{
					currentfont/Widths get cid
					2 copy known
						{get exch pop aload pop}
						{pop pop stringwidth}
					ifelse
					}
					{stringwidth}
				ifelse
				setcharwidth
				0 0 moveto
				}
			ifelse
			}def
		/ct_Type3ShowCharString
			{
			ct_FDDict fdIndex 2 copy known
				{get}
				{
				currentglobal 3 1 roll
				1 index gcheck setglobal
				ct_Type1FontTemplate dup maxlength dict copy
					begin
					FDArray fdIndex get
					dup/FontMatrix 2 copy known
						{get}
						{pop pop ct_defaultFontMtx}
					ifelse
					/FontMatrix exch dup length array copy def
					/Private get
					/Private exch def
					/Widths rootfont/Widths get def
					/CharStrings 1 dict dup/.notdef
						<d841272cf18f54fc13>dup length string copy put def
					currentdict
					end
				/ct_Type1Font exch definefont
				dup 5 1 roll put
				setglobal
				}
			ifelse
			dup/CharStrings get 1 index/Encoding get
			ct_dfCharCode get charstring put
			rootfont/WMode 2 copy known
				{get}
				{pop pop 0}
			ifelse
			exch
			1000 scalefont setfont
			ct_str1 0 ct_dfCharCode put
			ct_str1 exch ct_dfSetCacheProc
			ct_SyntheticBold
				{
				currentpoint
				ct_str1 show
				newpath
				moveto
				ct_str1 true charpath
				ct_StrokeWidth setlinewidth
				stroke
				}
				{ct_str1 show}
			ifelse
			}def
		/ct_Type4ShowCharString
			{
			ct_dfDict ct_dfCharCode charstring
			FDArray fdIndex get
			dup/FontMatrix get dup ct_defaultFontMtx ct_matrixeq not
				{ct_1000Mtx matrix concatmatrix concat}
				{pop}
			ifelse
			/Private get
			Adobe_CoolType_Utility/ct_Level2? get not
				{
				ct_dfDict/Private
				3 -1 roll
					{put}
				1183615869 internaldict/superexec get exec
				}
			if
			1183615869 internaldict
			Adobe_CoolType_Utility/ct_Level2? get
				{1 index}
				{3 index/Private get mark 6 1 roll}
			ifelse
			dup/RunInt known
				{/RunInt get}
				{pop/CCRun}
			ifelse
			get exec
			Adobe_CoolType_Utility/ct_Level2? get not
				{cleartomark}
			if
			}bind def
		/ct_BuildCharIncremental
			{
				{
				Adobe_CoolType_Utility/ct_MakeOCF get begin
				ct_BuildCharSetUp
				ct_ShowCharString
				}
			stopped
				{stop}
			if
			end
			end
			end
			end
			}bind def
		/BaseFontNameStr(BF00)def
		/ct_Type1FontTemplate 14 dict
			begin
			/FontType 1 def
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/PaintType 0 def
			currentdict
			end def
		/BaseFontTemplate 11 dict
			begin
			/FontMatrix [0.001 0 0 0.001 0 0]def
			/FontBBox [-250 -250 1250 1250]def
			/Encoding ct_cHexEncoding def
			/BuildChar/ct_BuildCharIncremental load def
			ct_Clone?
				{
				/FontType 3 def
				/ct_ShowCharString/ct_Type3ShowCharString load def
				/ct_dfSetCacheProc/ct_CloneSetCacheProc load def
				/ct_SyntheticBold false def
				/ct_StrokeWidth 1 def
				}
				{
				/FontType 4 def
				/Private 1 dict dup/lenIV 4 put def
				/CharStrings 1 dict dup/.notdef<d841272cf18f54fc13>put def
				/PaintType 0 def
				/ct_ShowCharString/ct_Type4ShowCharString load def
				}
			ifelse
			/ct_str1 1 string def
			currentdict
			end def
		/BaseFontDictSize BaseFontTemplate length 5 add def
		/ct_matrixeq
			{
			true 0 1 5
				{
				dup 4 index exch get exch 3 index exch get eq and
				dup not
					{exit}
				if
				}
			for
			exch pop exch pop
			}bind def
		/ct_makeocf
			{
			15 dict
				begin
				exch/WMode exch def
				exch/FontName exch def
				/FontType 0 def
				/FMapType 2 def
			dup/FontMatrix known
				{dup/FontMatrix get/FontMatrix exch def}
				{/FontMatrix matrix def}
			ifelse
				/bfCount 1 index/CIDCount get 256 idiv 1 add
					dup 256 gt{pop 256}if def
				/Encoding
					256 array 0 1 bfCount 1 sub{2 copy dup put pop}for
					bfCount 1 255{2 copy bfCount put pop}for
					def
				/FDepVector bfCount dup 256 lt{1 add}if array def
				BaseFontTemplate BaseFontDictSize dict copy
					begin
					/CIDFont exch def
					CIDFont/FontBBox known
						{CIDFont/FontBBox get/FontBBox exch def}
					if
					CIDFont/CDevProc known
						{CIDFont/CDevProc get/CDevProc exch def}
					if
					currentdict
					end
				BaseFontNameStr 3(0)putinterval
				0 1 bfCount dup 256 eq{1 sub}if
					{
					FDepVector exch
					2 index BaseFontDictSize dict copy
						begin
						dup/CIDFirstByte exch 256 mul def
						FontType 3 eq
							{/ct_FDDict 2 dict def}
						if
						currentdict
						end
					1 index 16
					BaseFontNameStr 2 2 getinterval cvrs pop
					BaseFontNameStr exch definefont
					put
					}
				for
				ct_Clone?
					{/Widths 1 index/CIDFont get/GlyphDirectory get length dict def}
				if
				FontName
				currentdict
				end
			definefont
			ct_Clone?
				{
				gsave
				dup 1000 scalefont setfont
				ct_BuildCharDict
					begin
					/usewidths? false def
					currentfont/Widths get
						begin
						exch/CIDFont get/GlyphDirectory get
							{
							pop
							dup charcode exch 1 index 0 2 index 256 idiv put
							1 index exch 1 exch 256 mod put
							stringwidth 2 array astore def
							}
						forall
						end
					/usewidths? true def
					end
				grestore
				}
				{exch pop}
			ifelse
			}bind def
		currentglobal true setglobal
		/ct_ComposeFont
			{
			ct_UseNativeCapability?
				{				
				2 index/CMap ct_resourcestatus
					{pop pop exch pop}
					{
					/CIDInit/ProcSet findresource
						begin
						12 dict
							begin
							begincmap
							/CMapName 3 index def
							/CMapVersion 1.000 def
							/CMapType 1 def
							exch/WMode exch def
							/CIDSystemInfo 3 dict dup
								begin
								/Registry(Adobe)def
								/Ordering
								CMapName ct_mkocfStr100 cvs
								(Adobe-)search
									{
									pop pop
									(-)search
										{
										dup length string copy
										exch pop exch pop
										}
										{pop(Identity)}
									ifelse
									}
									{pop (Identity)}
								ifelse
								def
								/Supplement 0 def
								end def
							1 begincodespacerange
							<0000><FFFF>
							endcodespacerange
							1 begincidrange
							<0000><FFFF>0
							endcidrange
							endcmap
							CMapName currentdict/CMap defineresource pop
							end
						end
					}
				ifelse
				composefont
				}
				{
				3 2 roll pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
			setglobal
		/ct_MakeIdentity
			{
			ct_UseNativeCapability?
				{
				1 index/CMap ct_resourcestatus
					{pop pop}
					{
					/CIDInit/ProcSet findresource begin
					12 dict begin
					begincmap
					/CMapName 2 index def
					/CMapVersion 1.000 def
					/CMapType 1 def
					/CIDSystemInfo 3 dict dup
						begin
						/Registry(Adobe)def
						/Ordering
						CMapName ct_mkocfStr100 cvs
						(Adobe-)search
							{
							pop pop
							(-)search
								{dup length string copy exch pop exch pop}
								{pop(Identity)}
							ifelse
							}
							{pop(Identity)}
						ifelse
						def
						/Supplement 0 def
						end def
					1 begincodespacerange
					<0000><FFFF>
					endcodespacerange
					1 begincidrange
					<0000><FFFF>0
					endcidrange
					endcmap
					CMapName currentdict/CMap defineresource pop
					end
					end
					}
				ifelse
				composefont
				}
				{
				exch pop
				0 get/CIDFont findresource
				ct_makeocf
				}
			ifelse
			}bind def
		currentdict readonly pop
		end
	end
%%EndResource
setglobal
%%BeginResource: procset Adobe_CoolType_Utility_T42 1.0 0
%%Copyright: Copyright 1987-2004 Adobe Systems Incorporated.
%%Version: 1.0 0
userdict/ct_T42Dict 15 dict put
ct_T42Dict begin
/Is2015?
{
 version
 cvi
 2015
 ge
}bind def
/AllocGlyphStorage
{
 Is2015?
 {	
	pop
 }
 {
	{string}forall
 }ifelse
}bind def
/Type42DictBegin
{
25 dict begin
 /FontName exch def
 /CharStrings 256 dict 
begin
	 /.notdef 0 def
	 currentdict 
end def
 /Encoding exch def
 /PaintType 0 def
 /FontType 42 def
 /FontMatrix[1 0 0 1 0 0]def
 4 array astore cvx/FontBBox exch def
 /sfnts
}bind def
/Type42DictEnd 
{
 currentdict dup/FontName get exch definefont end
ct_T42Dict exch
dup/FontName get exch put
}bind def
/RD{string currentfile exch readstring pop}executeonly def
/PrepFor2015
{
Is2015?
{		 
	/GlyphDirectory 
	 16
	 dict def
	 sfnts 0 get
	 dup
	 2 index
	(glyx)
	 putinterval
	 2 index 
	(locx)
	 putinterval
	 pop
	 pop
}
{
	 pop
	 pop
}ifelse			
}bind def
/AddT42Char
{
Is2015?
{
	/GlyphDirectory get 
	begin
	def
	end
	pop
	pop
}
{
	/sfnts get
	4 index
	get
	3 index
 2 index
	putinterval
	pop
	pop
	pop
	pop
}ifelse
}bind def
/T0AddT42Mtx2
{
/CIDFont findresource/Metrics2 get begin def end
}bind def
end
%%EndResource
currentglobal true setglobal
%%BeginFile: MMFauxFont.prc
%%Copyright: Copyright 1987-2001 Adobe Systems Incorporated. 
%%All Rights Reserved.
userdict /ct_EuroDict 10 dict put
ct_EuroDict begin
/ct_CopyFont 
{
    { 1 index /FID ne {def} {pop pop} ifelse} forall
} def
/ct_GetGlyphOutline
{
   gsave
   initmatrix newpath
   exch findfont dup 
   length 1 add dict 
   begin 
		ct_CopyFont 
		/Encoding Encoding dup length array copy 
		dup
		4 -1 roll
		0 exch put   
		def
		currentdict
   end
   /ct_EuroFont exch definefont
   1000 scalefont setfont
   0 0 moveto
   [
       <00> stringwidth 
       <00> false charpath
       pathbbox
       [
       {/m cvx} {/l cvx} {/c cvx} {/cp cvx} pathforall
   grestore
   counttomark 8 add
}
def
/ct_MakeGlyphProc
{
   ] cvx
   /ct_PSBuildGlyph cvx
   ] cvx
} def
/ct_PSBuildGlyph 
{ 
 	gsave 
	8 -1 roll pop 
	7 1 roll 
        6 -2 roll ct_FontMatrix transform 6 2 roll
        4 -2 roll ct_FontMatrix transform 4 2 roll
        ct_FontMatrix transform 
	currentdict /PaintType 2 copy known {get 2 eq}{pop pop false} ifelse  
	dup  9 1 roll 
	{  
		currentdict /StrokeWidth 2 copy known  
		{   
			get 2 div   
			0 ct_FontMatrix dtransform pop
			5 1 roll  
			4 -1 roll 4 index sub   
			4 1 roll   
			3 -1 roll 4 index sub  
			3 1 roll   
			exch 4 index add exch  
			4 index add  
			5 -1 roll pop  
		}  
		{	 
			pop pop 
		}  
		ifelse  
	}       
    if  
	setcachedevice  
        ct_FontMatrix concat
        ct_PSPathOps begin 
		exec 
	end 
	{  
		currentdict /StrokeWidth 2 copy known  
			{ get }  
			{ pop pop 0 }  
  	    ifelse  
		setlinewidth stroke  
	}  
	{   
	    fill  
	}  
	ifelse  
    grestore
} def 
/ct_PSPathOps 4 dict dup begin 
	/m {moveto} def 
	/l {lineto} def 
	/c {curveto} def 
	/cp {closepath} def 
end 
def 
/ct_matrix1000 [1000 0 0 1000 0 0] def
/ct_AddGlyphProc  
{
   2 index findfont dup length 4 add dict 
   begin 
	ct_CopyFont 
	/CharStrings CharStrings dup length 1 add dict copy
      begin
         3 1 roll def  
         currentdict 
      end 
      def
      /ct_FontMatrix ct_matrix1000 FontMatrix matrix concatmatrix def
      /ct_PSBuildGlyph /ct_PSBuildGlyph load def
      /ct_PSPathOps /ct_PSPathOps load def
      currentdict
   end
   definefont pop
}
def
systemdict /languagelevel known
{
	/ct_AddGlyphToPrinterFont {
		2 copy
		ct_GetGlyphOutline 3 add -1 roll restore 
		ct_MakeGlyphProc 
		ct_AddGlyphProc
	} def
}
{
	/ct_AddGlyphToPrinterFont {
	    pop pop restore
		Adobe_CTFauxDict /$$$FONTNAME get
		/Euro
		Adobe_CTFauxDict /$$$SUBSTITUTEBASE get
		ct_EuroDict exch get
		ct_AddGlyphProc
	} def
} ifelse
/AdobeSansMM 
{ 
556 0 24 -19 541 703 
	{ 
	541 628 m 
	510 669 442 703 354 703 c 
	201 703 117 607 101 444 c 
	50 444 l 
	25 372 l 
	97 372 l 
	97 301 l 
	49 301 l 
	24 229 l 
	103 229 l 
	124 67 209 -19 350 -19 c 
	435 -19 501 25 509 32 c 
	509 131 l 
	492 105 417 60 343 60 c 
	267 60 204 127 197 229 c 
	406 229 l 
	430 301 l 
	191 301 l 
	191 372 l 
	455 372 l 
	479 444 l 
	194 444 l 
	201 531 245 624 348 624 c 
	433 624 484 583 509 534 c 
	cp 
	556 0 m 
	}
ct_PSBuildGlyph
} def
/AdobeSerifMM 
{ 
500 0 10 -12 484 692 
	{ 
	347 298 m 
	171 298 l 
	170 310 170 322 170 335 c 
	170 362 l 
	362 362 l 
	374 403 l 
	172 403 l 
	184 580 244 642 308 642 c 
	380 642 434 574 457 457 c 
	481 462 l 
	474 691 l 
	449 691 l 
	433 670 429 657 410 657 c 
	394 657 360 692 299 692 c 
	204 692 94 604 73 403 c 
	22 403 l 
	10 362 l 
	70 362 l 
	69 352 69 341 69 330 c 
	69 319 69 308 70 298 c 
	22 298 l 
	10 257 l 
	73 257 l 
	97 57 216 -12 295 -12 c 
	364 -12 427 25 484 123 c 
	458 142 l 
	425 101 384 37 316 37 c 
	256 37 189 84 173 257 c 
	335 257 l 
	cp 
	500 0 m 
	} 
ct_PSBuildGlyph 
} def 
end		
%%EndFile
setglobal
Adobe_CoolType_Core begin /$Oblique SetSubstituteStrategy end
%%BeginResource: procset Adobe_AGM_Image 1.0 0
%%Version: 1.0 0
%%Copyright: Copyright(C)2000-2006 Adobe Systems, Inc. All Rights Reserved.
systemdict/setpacking known
{
	currentpacking
	true setpacking
}if
userdict/Adobe_AGM_Image 71 dict dup begin put
/Adobe_AGM_Image_Id/Adobe_AGM_Image_1.0_0 def
/nd{
	null def
}bind def
/AGMIMG_&image nd
/AGMIMG_&colorimage nd
/AGMIMG_&imagemask nd
/AGMIMG_mbuf()def
/AGMIMG_ybuf()def
/AGMIMG_kbuf()def
/AGMIMG_c 0 def
/AGMIMG_m 0 def
/AGMIMG_y 0 def
/AGMIMG_k 0 def
/AGMIMG_tmp nd
/AGMIMG_imagestring0 nd
/AGMIMG_imagestring1 nd
/AGMIMG_imagestring2 nd
/AGMIMG_imagestring3 nd
/AGMIMG_imagestring4 nd
/AGMIMG_imagestring5 nd
/AGMIMG_cnt nd
/AGMIMG_fsave nd
/AGMIMG_colorAry nd
/AGMIMG_override nd
/AGMIMG_name nd
/AGMIMG_maskSource nd
/AGMIMG_flushfilters nd
/invert_image_samples nd
/knockout_image_samples	nd
/img nd
/sepimg nd
/devnimg nd
/idximg nd
/ds
{
	Adobe_AGM_Core begin
	Adobe_AGM_Image begin
	/AGMIMG_&image systemdict/image get def
	/AGMIMG_&imagemask systemdict/imagemask get def
	/colorimage where{
		pop
		/AGMIMG_&colorimage/colorimage ldf
	}if
	end
	end
}def
/ps
{
	Adobe_AGM_Image begin
	/AGMIMG_ccimage_exists{/customcolorimage where 
		{
			pop
			/Adobe_AGM_OnHost_Seps where
			{
			pop false
			}{
			/Adobe_AGM_InRip_Seps where
				{
				pop false
				}{
					true
				}ifelse
			}ifelse
			}{
			false
		}ifelse 
	}bdf
	level2{
		/invert_image_samples
		{
			Adobe_AGM_Image/AGMIMG_tmp Decode length ddf
			/Decode[Decode 1 get Decode 0 get]def
		}def
		/knockout_image_samples
		{
			Operator/imagemask ne{
				/Decode[1 1]def
			}if
		}def
	}{	
		/invert_image_samples
		{
			{1 exch sub}currenttransfer addprocs settransfer
		}def
		/knockout_image_samples
		{
			{pop 1}currenttransfer addprocs settransfer
		}def
	}ifelse
	/img/imageormask ldf
	/sepimg/sep_imageormask ldf
	/devnimg/devn_imageormask ldf
	/idximg/indexed_imageormask ldf
	/_ctype 7 def
	currentdict{
		dup xcheck 1 index type dup/arraytype eq exch/packedarraytype eq or and{
			bind
		}if
		def
	}forall
}def
/pt
{
	end
}def
/dt
{
}def
/AGMIMG_flushfilters
{
	dup type/arraytype ne
		{1 array astore}if
	dup 0 get currentfile ne
		{dup 0 get flushfile}if
		{
		dup type/filetype eq
			{
			dup status 1 index currentfile ne and
				{closefile}
				{pop}
			ifelse
			}{pop}ifelse
		}forall
}def
/AGMIMG_init_common
{
	currentdict/T known{/ImageType/T ldf currentdict/T undef}if
	currentdict/W known{/Width/W ldf currentdict/W undef}if
	currentdict/H known{/Height/H ldf currentdict/H undef}if
	currentdict/M known{/ImageMatrix/M ldf currentdict/M undef}if
	currentdict/BC known{/BitsPerComponent/BC ldf currentdict/BC undef}if
	currentdict/D known{/Decode/D ldf currentdict/D undef}if
	currentdict/DS known{/DataSource/DS ldf currentdict/DS undef}if
	currentdict/O known{
		/Operator/O load 1 eq{
			/imagemask
		}{
			/O load 2 eq{
				/image 
			}{
				/colorimage
			}ifelse
		}ifelse
		def
		currentdict/O undef
	}if
	currentdict/HSCI known{/HostSepColorImage/HSCI ldf currentdict/HSCI undef}if
	currentdict/MD known{/MultipleDataSources/MD ldf currentdict/MD undef}if
	currentdict/I known{/Interpolate/I ldf currentdict/I undef}if
	currentdict/SI known{/SkipImageProc/SI ldf currentdict/SI undef}if
	/DataSource load xcheck not{
		DataSource type/arraytype eq{
			DataSource 0 get type/filetype eq{
				/_Filters DataSource def
				currentdict/MultipleDataSources known not{
					/DataSource DataSource dup length 1 sub get def 
				}if
			}if
		}if
		currentdict/MultipleDataSources known not{
			/MultipleDataSources DataSource type/arraytype eq{
				DataSource length 1 gt
			}
			{false}ifelse def
		}if
	}if
	/NComponents Decode length 2 div def
	currentdict/SkipImageProc known not{/SkipImageProc{false}def}if
}bdf
/imageormask_sys
{
	begin
		AGMIMG_init_common
		save mark
		level2{
			currentdict
			Operator/imagemask eq{
				AGMIMG_&imagemask
			}{
				use_mask{
					process_mask AGMIMG_&image
				}{
					AGMIMG_&image
				}ifelse
			}ifelse
		}{
			Width Height
			Operator/imagemask eq{
				Decode 0 get 1 eq Decode 1 get 0 eq	and
				ImageMatrix/DataSource load
				AGMIMG_&imagemask
			}{
				BitsPerComponent ImageMatrix/DataSource load
				AGMIMG_&image
			}ifelse
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
		cleartomark restore
	end
}def
/overprint_plate
{
	currentoverprint{
		0 get dup type/nametype eq{
			dup/DeviceGray eq{
				pop AGMCORE_black_plate not
			}{
				/DeviceCMYK eq{
					AGMCORE_is_cmyk_sep not
				}if
			}ifelse
		}{
			false exch
			{
				 AGMOHS_sepink eq or
			}forall
			not
		}ifelse
	}{
		pop false
	}ifelse
}def
/process_mask
{
	level3{
		dup begin
		/ImageType 1 def
		end
		4 dict begin
			/DataDict exch def
			/ImageType 3 def
			/InterleaveType 3 def
			/MaskDict 9 dict begin
				/ImageType 1 def
				/Width DataDict dup/MaskWidth known{/MaskWidth}{/Width}ifelse get def
				/Height DataDict dup/MaskHeight known{/MaskHeight}{/Height}ifelse get def
				/ImageMatrix[Width 0 0 Height neg 0 Height]def
				/NComponents 1 def
				/BitsPerComponent 1 def
				/Decode DataDict dup/MaskD known{/MaskD}{[1 0]}ifelse get def
				/DataSource Adobe_AGM_Core/AGMIMG_maskSource get def
			currentdict end def
		currentdict end
	}if
}def
/use_mask
{
	dup/Mask known	{dup/Mask get}{false}ifelse
}def
/imageormask
{
	begin
		AGMIMG_init_common
		SkipImageProc{
			currentdict consumeimagedata
		}
		{
			save mark
			level2 AGMCORE_host_sep not and{
				currentdict
				Operator/imagemask eq DeviceN_PS2 not and{
					imagemask
				}{
					AGMCORE_in_rip_sep currentoverprint and currentcolorspace 0 get/DeviceGray eq and{
						[/Separation/Black/DeviceGray{}]setcolorspace
						/Decode[Decode 1 get Decode 0 get]def
					}if
					use_mask{
						process_mask image
					}{
						DeviceN_NoneName DeviceN_PS2 Indexed_DeviceN level3 not and or or AGMCORE_in_rip_sep and 
						{
							Names convert_to_process not{
								2 dict begin
								/imageDict xdf
								/names_index 0 def
								gsave
								imageDict write_image_file{
									Names{
										dup(None)ne{
											[/Separation 3 -1 roll/DeviceGray{1 exch sub}]setcolorspace
											Operator imageDict read_image_file
											names_index 0 eq{true setoverprint}if
											/names_index names_index 1 add def
										}{
											pop
										}ifelse
									}forall
									close_image_file
								}if
								grestore
								end
							}{
								Operator/imagemask eq{
									imagemask
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/imagemask eq{
								imagemask
							}{
								image
							}ifelse
						}ifelse
					}ifelse
				}ifelse
			}{
				Width Height
				Operator/imagemask eq{
					Decode 0 get 1 eq Decode 1 get 0 eq	and
					ImageMatrix/DataSource load
					/Adobe_AGM_OnHost_Seps where{
						pop imagemask
					}{
						currentgray 1 ne{
							currentdict imageormask_sys
						}{
							currentoverprint not{
								1 AGMCORE_&setgray
								currentdict imageormask_sys
							}{
								currentdict ignoreimagedata
							}ifelse				 		
						}ifelse
					}ifelse
				}{
					BitsPerComponent ImageMatrix 
					MultipleDataSources{
						0 1 NComponents 1 sub{
							DataSource exch get
						}for
					}{
						/DataSource load
					}ifelse
					Operator/colorimage eq{
						AGMCORE_host_sep{
							MultipleDataSources level2 or NComponents 4 eq and{
								AGMCORE_is_cmyk_sep{
									MultipleDataSources{
										/DataSource DataSource 0 get xcheck
											{
											[
											DataSource 0 get/exec cvx
											DataSource 1 get/exec cvx
											DataSource 2 get/exec cvx
											DataSource 3 get/exec cvx
											/AGMCORE_get_ink_data cvx
											]cvx
											}{
											DataSource aload pop AGMCORE_get_ink_data
											}ifelse def
									}{
										/DataSource 
										Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
										/DataSource load
										filter_cmyk 0()/SubFileDecode filter def
									}ifelse
									/Decode[Decode 0 get Decode 1 get]def
									/MultipleDataSources false def
									/NComponents 1 def
									/Operator/image def
									invert_image_samples
						 			1 AGMCORE_&setgray
									currentdict imageormask_sys
								}{
									currentoverprint not Operator/imagemask eq and{
 			 							1 AGMCORE_&setgray
 			 							currentdict imageormask_sys
 			 						}{
 			 							currentdict ignoreimagedata
 			 						}ifelse
								}ifelse
							}{	
								MultipleDataSources NComponents AGMIMG_&colorimage						
							}ifelse
						}{
							true NComponents colorimage
						}ifelse
					}{
						Operator/image eq{
							AGMCORE_host_sep{
								/DoImage true def
								currentdict/HostSepColorImage known{HostSepColorImage not}{false}ifelse
								{
									AGMCORE_black_plate not Operator/imagemask ne and{
										/DoImage false def
										currentdict ignoreimagedata
					 				}if
								}if
						 		1 AGMCORE_&setgray
								DoImage
									{currentdict imageormask_sys}if
							}{
								use_mask{
									process_mask image
								}{
									image
								}ifelse
							}ifelse
						}{
							Operator/knockout eq{
								pop pop pop pop pop
								currentcolorspace overprint_plate not{
									knockout_unitsq
								}if
							}if
						}ifelse
					}ifelse
				}ifelse
			}ifelse
			cleartomark restore
		}ifelse
		currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/sep_imageormask
{
 	/sep_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_avoid_L2_sep_space{
			/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
		}if
 		AGMIMG_ccimage_exists 
		MappedCSA 0 get/DeviceCMYK eq and
		currentdict/Components known and 
		Name()ne and 
		Name(All)ne and 
		Operator/image eq and
		AGMCORE_producing_seps not and
		level2 not and
		{
			Width Height BitsPerComponent ImageMatrix 
			[
			/DataSource load/exec cvx
			{
				0 1 2 index length 1 sub{
					1 index exch
					2 copy get 255 xor put
				}for
			}/exec cvx
			]cvx bind
			MappedCSA 0 get/DeviceCMYK eq{
				Components aload pop
			}{
				0 0 0 Components aload pop 1 exch sub
			}ifelse
			Name findcmykcustomcolor
			customcolorimage
		}{
			AGMCORE_producing_seps not{
				level2{
 					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne AGMCORE_avoid_L2_sep_space not and currentcolorspace 0 get/Separation ne and{
						[/Separation Name MappedCSA sep_proc_name exch dup 0 get 15 string cvs(/Device)anchorsearch{pop pop 0 get}{pop}ifelse exch load]setcolorspace_opt
						/sep_tint AGMCORE_gget setcolor
					}if
					currentdict imageormask
				}{
					currentdict
					Operator/imagemask eq{
						imageormask
					}{
						sep_imageormask_lev1
					}ifelse
				}ifelse
 			}{
				AGMCORE_host_sep{
					Operator/knockout eq{
						currentdict/ImageMatrix get concat
						knockout_unitsq
					}{
						currentgray 1 ne{
 							AGMCORE_is_cmyk_sep Name(All)ne and{
 								level2{
 									Name AGMCORE_IsSeparationAProcessColor 
 									{
 										Operator/imagemask eq{
 											//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
 												/sep_tint AGMCORE_gget 1 exch sub AGMCORE_&setcolor
 											}if
 										}{
											invert_image_samples
 										}ifelse
	 								}{
	 									//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
	 										[/Separation Name[/DeviceGray]
	 										{
	 											sep_colorspace_proc AGMCORE_get_ink_data
												1 exch sub
	 										}bind
											]AGMCORE_&setcolorspace
											/sep_tint AGMCORE_gget AGMCORE_&setcolor
										}if
 									}ifelse
 									currentdict imageormask_sys
	 							}{
	 								currentdict
									Operator/imagemask eq{
										imageormask_sys
									}{
										sep_image_lev1_sep
									}ifelse
	 							}ifelse
 							}{
 								Operator/imagemask ne{
									invert_image_samples
 								}if
		 						currentdict imageormask_sys
 							}ifelse
 						}{
 							currentoverprint not Name(All)eq or Operator/imagemask eq and{
								currentdict imageormask_sys 
								}{
								currentoverprint not
									{
 									gsave 
 									knockout_unitsq
 									grestore
									}if
								currentdict consumeimagedata 
		 					}ifelse
 						}ifelse
		 			}ifelse
 				}{
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{
						currentcolorspace 0 get/Separation ne{
							[/Separation Name MappedCSA sep_proc_name exch 0 get exch load]setcolorspace_opt
							/sep_tint AGMCORE_gget setcolor
						}if
					}if
					currentoverprint 
					MappedCSA 0 get/DeviceCMYK eq and 
					Name AGMCORE_IsSeparationAProcessColor not and
					//Adobe_AGM_Core/AGMCORE_pattern_paint_type get 2 ne{Name inRip_spot_has_ink not and}{false}ifelse 
					Name(All)ne and{
						imageormask_l2_overprint
					}{
						currentdict imageormask
 					}ifelse
				}ifelse
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
}def
/colorSpaceElemCnt
{
	mark currentcolor counttomark dup 2 add 1 roll cleartomark
}bdf
/devn_sep_datasource
{
	1 dict begin
	/dataSource xdf
	[
		0 1 dataSource length 1 sub{
			dup currentdict/dataSource get/exch cvx/get cvx/exec cvx
			/exch cvx names_index/ne cvx[/pop cvx]cvx/if cvx
		}for
	]cvx bind
	end
}bdf		
/devn_alt_datasource
{
	11 dict begin
	/convProc xdf
	/origcolorSpaceElemCnt xdf
	/origMultipleDataSources xdf
	/origBitsPerComponent xdf
	/origDecode xdf
	/origDataSource xdf
	/dsCnt origMultipleDataSources{origDataSource length}{1}ifelse def
	/DataSource origMultipleDataSources
		{
			[
			BitsPerComponent 8 idiv origDecode length 2 idiv mul string
			0 1 origDecode length 2 idiv 1 sub
				{
				dup 7 mul 1 add index exch dup BitsPerComponent 8 idiv mul exch
				origDataSource exch get 0()/SubFileDecode filter
				BitsPerComponent 8 idiv string/readstring cvx/pop cvx/putinterval cvx
				}for 
			]bind cvx
		}{origDataSource}ifelse 0()/SubFileDecode filter def		
	[
		origcolorSpaceElemCnt string
		0 2 origDecode length 2 sub
			{
			dup origDecode exch get dup 3 -1 roll 1 add origDecode exch get exch sub 2 BitsPerComponent exp 1 sub div
			1 BitsPerComponent 8 idiv{DataSource/read cvx/not cvx{0}/if cvx/mul cvx}repeat/mul cvx/add cvx
			}for
		/convProc load/exec cvx
		origcolorSpaceElemCnt 1 sub -1 0
			{
			/dup cvx 2/add cvx/index cvx
			3 1/roll cvx/exch cvx 255/mul cvx/cvi cvx/put cvx
			}for
	]bind cvx 0()/SubFileDecode filter
	end
}bdf
/devn_imageormask
{
 	/devicen_colorspace_dict AGMCORE_gget begin
	CSA map_csa
	2 dict begin
	dup
	/srcDataStrs[3 -1 roll begin
		AGMIMG_init_common
		currentdict/MultipleDataSources known{MultipleDataSources{DataSource length}{1}ifelse}{1}ifelse
		{
			Width Decode length 2 div mul cvi
			{
				dup 65535 gt{1 add 2 div cvi}{exit}ifelse
			}loop
			string
		}repeat
		end]def
	/dstDataStr srcDataStrs 0 get length string def
	begin
	AGMIMG_init_common
	SkipImageProc{
		currentdict consumeimagedata
	}{
		save mark 
		AGMCORE_producing_seps not{
			level3 not{
				Operator/imagemask ne{
					/DataSource[[
						DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
						colorSpaceElemCnt/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
						devn_alt_datasource 1/string cvx/readstring cvx/pop cvx]cvx colorSpaceElemCnt 1 sub{dup}repeat]def				
					/MultipleDataSources true def
					/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				}if
			}if
			currentdict imageormask
 		}{
			AGMCORE_host_sep{
				Names convert_to_process{
					CSA get_csa_by_name 0 get/DeviceCMYK eq{
						/DataSource
							Width BitsPerComponent mul 7 add 8 idiv Height mul 4 mul 
							DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
							4/devicen_colorspace_dict AGMCORE_gget/TintTransform get 
							devn_alt_datasource
						filter_cmyk 0()/SubFileDecode filter def
						/MultipleDataSources false def
						/Decode[1 0]def
						/DeviceGray setcolorspace
			 			currentdict imageormask_sys
 					}{
						AGMCORE_report_unsupported_color_space
						AGMCORE_black_plate{
							/DataSource
								DataSource Decode BitsPerComponent currentdict/MultipleDataSources known{MultipleDataSources}{false}ifelse
								CSA get_csa_by_name 0 get/DeviceRGB eq{3}{1}ifelse/devicen_colorspace_dict AGMCORE_gget/TintTransform get
								devn_alt_datasource
							/MultipleDataSources false def
							/Decode colorSpaceElemCnt[exch{0 1}repeat]def
				 			currentdict imageormask_sys
				 		}{
	 						gsave 
	 						knockout_unitsq
	 						grestore
							currentdict consumeimagedata 
						}ifelse
 					}ifelse
				}
				{	
					/devicen_colorspace_dict AGMCORE_gget/names_index known{
	 					Operator/imagemask ne{
	 						MultipleDataSources{
		 						/DataSource[DataSource devn_sep_datasource/exec cvx]cvx def
								/MultipleDataSources false def
	 						}{
								/DataSource/DataSource load dstDataStr srcDataStrs 0 get filter_devn def
	 						}ifelse
							invert_image_samples
	 					}if
			 			currentdict imageormask_sys
	 				}{
	 					currentoverprint not Operator/imagemask eq and{
							currentdict imageormask_sys 
							}{
							currentoverprint not
								{
	 							gsave 
	 							knockout_unitsq
	 							grestore
								}if
							currentdict consumeimagedata 
			 			}ifelse
	 				}ifelse
	 			}ifelse
 			}{
				currentdict imageormask
			}ifelse
		}ifelse
		cleartomark restore
	}ifelse
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
	end
	end
}def
/imageormask_l2_overprint
{
	currentdict
	currentcmykcolor add add add 0 eq{
		currentdict consumeimagedata
	}{
		level3{			
			currentcmykcolor 
			/AGMIMG_k xdf 
			/AGMIMG_y xdf 
			/AGMIMG_m xdf 
			/AGMIMG_c xdf
			Operator/imagemask eq{
				[/DeviceN[
				AGMIMG_c 0 ne{/Cyan}if
				AGMIMG_m 0 ne{/Magenta}if
				AGMIMG_y 0 ne{/Yellow}if
				AGMIMG_k 0 ne{/Black}if
				]/DeviceCMYK{}]setcolorspace
				AGMIMG_c 0 ne{AGMIMG_c}if
				AGMIMG_m 0 ne{AGMIMG_m}if
				AGMIMG_y 0 ne{AGMIMG_y}if
				AGMIMG_k 0 ne{AGMIMG_k}if
				setcolor			
			}{	
				/Decode[Decode 0 get 255 mul Decode 1 get 255 mul]def
				[/Indexed 				
					[
						/DeviceN[
							AGMIMG_c 0 ne{/Cyan}if
							AGMIMG_m 0 ne{/Magenta}if
							AGMIMG_y 0 ne{/Yellow}if
							AGMIMG_k 0 ne{/Black}if
						]
						/DeviceCMYK{
							AGMIMG_k 0 eq{0}if
							AGMIMG_y 0 eq{0 exch}if
							AGMIMG_m 0 eq{0 3 1 roll}if
							AGMIMG_c 0 eq{0 4 1 roll}if						
						}
					]
					255
					{
						255 div 
						mark exch
						dup	dup dup
						AGMIMG_k 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 1 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_y 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 2 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_m 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec 4 3 roll pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						AGMIMG_c 0 ne{
							/sep_tint AGMCORE_gget mul MappedCSA sep_proc_name exch pop load exec pop pop pop		
							counttomark 1 roll
						}{
							pop
						}ifelse
						counttomark 1 add -1 roll pop
					}
				]setcolorspace
			}ifelse
			imageormask_sys
		}{
	write_image_file{
		currentcmykcolor
		0 ne{
			[/Separation/Black/DeviceGray{}]setcolorspace
			gsave
			/Black
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 1 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Yellow/DeviceGray{}]setcolorspace
			gsave
			/Yellow
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 2 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Magenta/DeviceGray{}]setcolorspace
			gsave
			/Magenta
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{4 3 roll pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
		0 ne{
			[/Separation/Cyan/DeviceGray{}]setcolorspace
			gsave
			/Cyan 
			[{1 exch sub/sep_tint AGMCORE_gget mul}/exec cvx MappedCSA sep_proc_name cvx exch pop{pop pop pop 1 exch sub}/exec cvx]
			cvx modify_halftone_xfer
			Operator currentdict read_image_file
			grestore
		}if
				close_image_file
			}{
				imageormask
			}ifelse
		}ifelse
	}ifelse
}def
/indexed_imageormask
{
	begin
		AGMIMG_init_common
		save mark 
 		currentdict
 		AGMCORE_host_sep{
			Operator/knockout eq{
				/indexed_colorspace_dict AGMCORE_gget dup/CSA known{
					/CSA get get_csa_by_name
				}{
					/Names get
				}ifelse
				overprint_plate not{
					knockout_unitsq
				}if
			}{
				Indexed_DeviceN{
					/devicen_colorspace_dict AGMCORE_gget dup/names_index known exch/Names get convert_to_process or{
			 			indexed_image_lev2_sep
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}{
		 			AGMCORE_is_cmyk_sep{
						Operator/imagemask eq{
							imageormask_sys
						}{
							level2{
								indexed_image_lev2_sep
							}{
								indexed_image_lev1_sep
							}ifelse
						}ifelse
					}{
						currentoverprint not{
							knockout_unitsq
			 			}if
			 			currentdict consumeimagedata
					}ifelse
				}ifelse
			}ifelse
 		}{
			level2{
				Indexed_DeviceN{
					/indexed_colorspace_dict AGMCORE_gget begin
				}{
					/indexed_colorspace_dict AGMCORE_gget dup null ne
					{
						begin
						currentdict/CSDBase known{CSDBase/CSD get_res/MappedCSA get}{CSA}ifelse
						get_csa_by_name 0 get/DeviceCMYK eq ps_level 3 ge and ps_version 3015.007 lt and
						AGMCORE_in_rip_sep and{
							[/Indexed[/DeviceN[/Cyan/Magenta/Yellow/Black]/DeviceCMYK{}]HiVal Lookup]
							setcolorspace
						}if
						end
					}
					{pop}ifelse
				}ifelse
				imageormask
				Indexed_DeviceN{
					end
				}if
			}{
				Operator/imagemask eq{
					imageormask
				}{
					indexed_imageormask_lev1
				}ifelse
			}ifelse
 		}ifelse
		cleartomark restore
	currentdict/_Filters known{_Filters AGMIMG_flushfilters}if
	end
}def
/indexed_image_lev2_sep
{
	/indexed_colorspace_dict AGMCORE_gget begin
	begin
		Indexed_DeviceN not{
			currentcolorspace 
			dup 1/DeviceGray put
			dup 3
			currentcolorspace 2 get 1 add string
			0 1 2 3 AGMCORE_get_ink_data 4 currentcolorspace 3 get length 1 sub
			{
			dup 4 idiv exch currentcolorspace 3 get exch get 255 exch sub 2 index 3 1 roll put
			}for 
			put	setcolorspace
		}if
		currentdict 
		Operator/imagemask eq{
			AGMIMG_&imagemask
		}{
			use_mask{
				process_mask AGMIMG_&image
			}{
				AGMIMG_&image
			}ifelse
		}ifelse
	end end
}def
 /OPIimage
 {
 	dup type/dicttype ne{
 		10 dict begin
 			/DataSource xdf
 			/ImageMatrix xdf
 			/BitsPerComponent xdf
 			/Height xdf
 			/Width xdf
 			/ImageType 1 def
 			/Decode[0 1 def]
 			currentdict
 		end
 	}if
 	dup begin
 		/NComponents 1 cdndf
 		/MultipleDataSources false cdndf
 		/SkipImageProc{false}cdndf
 		/Decode[
 				0 
 				currentcolorspace 0 get/Indexed eq{
 					2 BitsPerComponent exp 1 sub
 				}{
 					1
 				}ifelse
 		]cdndf
 		/Operator/image cdndf
 	end
 	/sep_colorspace_dict AGMCORE_gget null eq{
 		imageormask
 	}{
 		gsave
 		dup begin invert_image_samples end
 		sep_imageormask
 		grestore
 	}ifelse
 }def
/cachemask_level2
{
	3 dict begin
	/LZWEncode filter/WriteFilter xdf
	/readBuffer 256 string def
	/ReadFilter
		currentfile
		0(%EndMask)/SubFileDecode filter
		/ASCII85Decode filter
		/RunLengthDecode filter
	def
	{
		ReadFilter readBuffer readstring exch
		WriteFilter exch writestring
		not{exit}if
	}loop
	WriteFilter closefile
	end
}def
/spot_alias
{
	/mapto_sep_imageormask 
	{
		dup type/dicttype ne{
			12 dict begin
				/ImageType 1 def
				/DataSource xdf
				/ImageMatrix xdf
				/BitsPerComponent xdf
				/Height xdf
				/Width xdf
				/MultipleDataSources false def
		}{
			begin
		}ifelse
				/Decode[/customcolor_tint AGMCORE_gget 0]def
				/Operator/image def
				/SkipImageProc{false}def
				currentdict 
			end
		sep_imageormask
	}bdf
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_colorAry xddf
		/customcolor_tint AGMCORE_gget
		<<
			/Name AGMIMG_colorAry 4 get
			/CSA[/DeviceCMYK]
			/TintMethod/Subtractive
			/TintProc null
			/MappedCSA null
			/NComponents 4 
			/Components[AGMIMG_colorAry aload pop pop]
		>>
		setsepcolorspace
		mapto_sep_imageormask
	}ndf
	Adobe_AGM_Image/AGMIMG_&customcolorimage/customcolorimage load put
	/customcolorimage
	{
		Adobe_AGM_Image/AGMIMG_override false put
		current_spot_alias{dup 4 get map_alias}{false}ifelse
		{
			false set_spot_alias
			/customcolor_tint AGMCORE_gget exch setsepcolorspace
			pop
			mapto_sep_imageormask
			true set_spot_alias
		}{
			//Adobe_AGM_Image/AGMIMG_&customcolorimage get exec
		}ifelse			
	}bdf
}def
/snap_to_device
{
	6 dict begin
	matrix currentmatrix
	dup 0 get 0 eq 1 index 3 get 0 eq and
	1 index 1 get 0 eq 2 index 2 get 0 eq and or exch pop
	{
		1 1 dtransform 0 gt exch 0 gt/AGMIMG_xSign? exch def/AGMIMG_ySign? exch def
		0 0 transform
		AGMIMG_ySign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		AGMIMG_xSign?{floor 0.1 sub}{ceiling 0.1 add}ifelse exch
		itransform/AGMIMG_llY exch def/AGMIMG_llX exch def
		1 1 transform
		AGMIMG_ySign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		AGMIMG_xSign?{ceiling 0.1 add}{floor 0.1 sub}ifelse exch
		itransform/AGMIMG_urY exch def/AGMIMG_urX exch def			
		[AGMIMG_urX AGMIMG_llX sub 0 0 AGMIMG_urY AGMIMG_llY sub AGMIMG_llX AGMIMG_llY]concat
	}{
	}ifelse
	end
}def
level2 not{
	/colorbuf
	{
		0 1 2 index length 1 sub{
			dup 2 index exch get 
			255 exch sub 
			2 index 
			3 1 roll 
			put
		}for
	}def
	/tint_image_to_color
	{
		begin
			Width Height BitsPerComponent ImageMatrix 
			/DataSource load
		end
		Adobe_AGM_Image begin
			/AGMIMG_mbuf 0 string def
			/AGMIMG_ybuf 0 string def
			/AGMIMG_kbuf 0 string def
			{
				colorbuf dup length AGMIMG_mbuf length ne
					{
					dup length dup dup
					/AGMIMG_mbuf exch string def
					/AGMIMG_ybuf exch string def
					/AGMIMG_kbuf exch string def
					}if
				dup AGMIMG_mbuf copy AGMIMG_ybuf copy AGMIMG_kbuf copy pop
			}
			addprocs
			{AGMIMG_mbuf}{AGMIMG_ybuf}{AGMIMG_kbuf}true 4 colorimage	
		end
	}def			
	/sep_imageormask_lev1
	{
		begin
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{
					255 mul round cvi GrayLookup exch get
				}currenttransfer addprocs settransfer
				currentdict imageormask
			}{
				/sep_colorspace_dict AGMCORE_gget/Components known{
					MappedCSA 0 get/DeviceCMYK eq{
						Components aload pop
					}{
						0 0 0 Components aload pop 1 exch sub
					}ifelse
					Adobe_AGM_Image/AGMIMG_k xddf 
					Adobe_AGM_Image/AGMIMG_y xddf 
					Adobe_AGM_Image/AGMIMG_m xddf 
					Adobe_AGM_Image/AGMIMG_c xddf 
					AGMIMG_y 0.0 eq AGMIMG_m 0.0 eq and AGMIMG_c 0.0 eq and{
						{AGMIMG_k mul 1 exch sub}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						currentcolortransfer
						{AGMIMG_k mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_y mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_m mul 1 exch sub}exch addprocs 4 1 roll
						{AGMIMG_c mul 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer
						currentdict tint_image_to_color
					}ifelse
				}{
					MappedCSA 0 get/DeviceGray eq{
						{255 mul round cvi ColorLookup exch get 0 get}currenttransfer addprocs settransfer
						currentdict imageormask
					}{
						MappedCSA 0 get/DeviceCMYK eq{
							currentcolortransfer
							{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}{
							currentcolortransfer
							{pop 1}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 2 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 1 get}exch addprocs 4 1 roll
							{255 mul round cvi ColorLookup exch get 0 get}exch addprocs 4 1 roll
							setcolortransfer 
							currentdict tint_image_to_color
						}ifelse
					}ifelse
				}ifelse
			}ifelse
		end
	}def
	/sep_image_lev1_sep
	{
		begin
			/sep_colorspace_dict AGMCORE_gget/Components known{
				Components aload pop
				Adobe_AGM_Image/AGMIMG_k xddf 
				Adobe_AGM_Image/AGMIMG_y xddf 
				Adobe_AGM_Image/AGMIMG_m xddf 
				Adobe_AGM_Image/AGMIMG_c xddf 
				{AGMIMG_c mul 1 exch sub}
				{AGMIMG_m mul 1 exch sub}
				{AGMIMG_y mul 1 exch sub}
				{AGMIMG_k mul 1 exch sub}
			}{
				{255 mul round cvi ColorLookup exch get 0 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 1 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 2 get 1 exch sub}
				{255 mul round cvi ColorLookup exch get 3 get 1 exch sub}
			}ifelse
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end
	}def
	/indexed_imageormask_lev1
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			currentdict
			MappedCSA 0 get dup/DeviceRGB eq exch/DeviceCMYK eq or has_color not and{
				{HiVal mul round cvi GrayLookup exch get HiVal div}currenttransfer addprocs settransfer
				imageormask
			}{
				MappedCSA 0 get/DeviceGray eq{
					{HiVal mul round cvi Lookup exch get HiVal div}currenttransfer addprocs settransfer
					imageormask
				}{
					MappedCSA 0 get/DeviceCMYK eq{
						currentcolortransfer
						{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}{
						currentcolortransfer
						{pop 1}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 2 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 1 add Lookup exch get HiVal div}exch addprocs 4 1 roll
						{3 mul HiVal mul round cvi 		Lookup exch get HiVal div}exch addprocs 4 1 roll
						setcolortransfer 
						tint_image_to_color
					}ifelse
				}ifelse
			}ifelse
		end end
	}def
	/indexed_image_lev1_sep
	{
		/indexed_colorspace_dict AGMCORE_gget begin
		begin
			{4 mul HiVal mul round cvi		 Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 1 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 2 add Lookup exch get HiVal div 1 exch sub}
			{4 mul HiVal mul round cvi 3 add Lookup exch get HiVal div 1 exch sub}
			AGMCORE_get_ink_data currenttransfer addprocs settransfer
			currentdict imageormask_sys
		end end
	}def
}if
end
systemdict/setpacking known
{setpacking}if
%%EndResource
currentdict Adobe_AGM_Utils eq {end} if
%%EndProlog
%%BeginSetup
Adobe_AGM_Utils begin
3 3010 Adobe_AGM_Core/ds gx
Adobe_CoolType_Core/ds get exec
Adobe_AGM_Image/ds gx
currentdict Adobe_AGM_Utils eq {end} if
%%EndSetup
%%Page: 9 1
%%EndPageComments
%%BeginPageSetup
%ADOBeginClientInjection: PageSetup Start "AI11EPS"
%AI12_RMC_Transparency: Balance=75 RasterRes=300 GradRes=150 Text=0 Stroke=1 Clip=1 OP=0

%ADOEndClientInjection: PageSetup Start "AI11EPS"
Adobe_AGM_Utils begin
Adobe_AGM_Core/ps gx
Adobe_AGM_Utils/capture_cpd gx
Adobe_CoolType_Core/ps get exec
Adobe_AGM_Image/ps gx
%ADOBeginClientInjection: PageSetup End "AI11EPS"
/currentdistillerparams where
{pop currentdistillerparams /CoreDistVersion get 5000 lt} {true} ifelse
{ userdict /AI11_PDFMark5 /cleartomark load put
userdict /AI11_ReadMetadata_PDFMark5 {flushfile cleartomark } bind put}
{ userdict /AI11_PDFMark5 /pdfmark load put
userdict /AI11_ReadMetadata_PDFMark5 {/PUT pdfmark} bind put } ifelse
[/NamespacePush AI11_PDFMark5
[/_objdef {ai_metadata_stream_123} /type /stream /OBJ AI11_PDFMark5
[{ai_metadata_stream_123}
currentfile 0 (%  &&end XMP packet marker&&)
/SubFileDecode filter AI11_ReadMetadata_PDFMark5
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c145 79.163499, 2018/08/13-16:40:22        ">
   <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
      <rdf:Description rdf:about=""
            xmlns:dc="http://purl.org/dc/elements/1.1/"
            xmlns:xmp="http://ns.adobe.com/xap/1.0/"
            xmlns:xmpGImg="http://ns.adobe.com/xap/1.0/g/img/"
            xmlns:xmpMM="http://ns.adobe.com/xap/1.0/mm/"
            xmlns:stRef="http://ns.adobe.com/xap/1.0/sType/ResourceRef#"
            xmlns:stEvt="http://ns.adobe.com/xap/1.0/sType/ResourceEvent#"
            xmlns:illustrator="http://ns.adobe.com/illustrator/1.0/"
            xmlns:xmpTPg="http://ns.adobe.com/xap/1.0/t/pg/"
            xmlns:stDim="http://ns.adobe.com/xap/1.0/sType/Dimensions#"
            xmlns:xmpG="http://ns.adobe.com/xap/1.0/g/"
            xmlns:pdf="http://ns.adobe.com/pdf/1.3/">
         <dc:format>application/postscript</dc:format>
         <dc:title>
            <rdf:Alt>
               <rdf:li xml:lang="x-default">Print</rdf:li>
            </rdf:Alt>
         </dc:title>
         <xmp:MetadataDate>2020-07-31T13:55:05+02:00</xmp:MetadataDate>
         <xmp:ModifyDate>2020-07-31T13:55:05+02:00</xmp:ModifyDate>
         <xmp:CreateDate>2020-07-31T13:55:05+02:00</xmp:CreateDate>
         <xmp:CreatorTool>Adobe Illustrator CC 23.0 (Windows)</xmp:CreatorTool>
         <xmp:Thumbnails>
            <rdf:Alt>
               <rdf:li rdf:parseType="Resource">
                  <xmpGImg:width>236</xmpGImg:width>
                  <xmpGImg:height>256</xmpGImg:height>
                  <xmpGImg:format>JPEG</xmpGImg:format>
                  <xmpGImg:image>/9j/4AAQSkZJRgABAgEASABIAAD/7QAsUGhvdG9zaG9wIDMuMAA4QklNA+0AAAAAABAASAAAAAEA&#xA;AQBIAAAAAQAB/+4ADkFkb2JlAGTAAAAAAf/bAIQABgQEBAUEBgUFBgkGBQYJCwgGBggLDAoKCwoK&#xA;DBAMDAwMDAwQDA4PEA8ODBMTFBQTExwbGxscHx8fHx8fHx8fHwEHBwcNDA0YEBAYGhURFRofHx8f&#xA;Hx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgBAADsAwER&#xA;AAIRAQMRAf/EAaIAAAAHAQEBAQEAAAAAAAAAAAQFAwIGAQAHCAkKCwEAAgIDAQEBAQEAAAAAAAAA&#xA;AQACAwQFBgcICQoLEAACAQMDAgQCBgcDBAIGAnMBAgMRBAAFIRIxQVEGE2EicYEUMpGhBxWxQiPB&#xA;UtHhMxZi8CRygvElQzRTkqKyY3PCNUQnk6OzNhdUZHTD0uIIJoMJChgZhJRFRqS0VtNVKBry4/PE&#xA;1OT0ZXWFlaW1xdXl9WZ2hpamtsbW5vY3R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo+Ck5SVlpeYmZ&#xA;qbnJ2en5KjpKWmp6ipqqusra6voRAAICAQIDBQUEBQYECAMDbQEAAhEDBCESMUEFURNhIgZxgZEy&#xA;obHwFMHR4SNCFVJicvEzJDRDghaSUyWiY7LCB3PSNeJEgxdUkwgJChgZJjZFGidkdFU38qOzwygp&#xA;0+PzhJSktMTU5PRldYWVpbXF1eX1RlZmdoaWprbG1ub2R1dnd4eXp7fH1+f3OEhYaHiImKi4yNjo&#xA;+DlJWWl5iZmpucnZ6fkqOkpaanqKmqq6ytrq+v/aAAwDAQACEQMRAD8A9U4q7FXYq7FXYq7FXgv/&#xA;ADk/+b2q+V7W08r+X7hrTVdSiNxe3sZKyw23IoixMPstIyt8XUAbdahS+R7iW4mlae4d5JZTyaWQ&#xA;lmYnuWO5wKjrPzJ5isiDZ6reWxG4MM8sfan7LDtirINP/OT81LAj6v5q1JqdBPO9wPum9QdsVZZp&#xA;P/OVH5u2JH1i7tNTA7Xdqi/jbfVzirPND/5zMeqpr3lsFf257GehHyilU1/5GYVp6h5X/wCcjPyn&#xA;8wMkS6t+i7l+lvqafVqfOWrQf8lMUPSYLiC4hSe3kWaGQco5Y2DKwPcMKgjFV+KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8Nf85L6i95+cmtoTWOzW2t4q9gttG7D/AIN2&#xA;wJYukSCFYyAVUAUO42GY1uVS2PRrC5cqycCRUMhpT6On4YeMhHhgoK98sXcQL27eug/Z6P8A0OTj&#xA;lB5sJYSOSCtb1rZ/TuIVmjBo0UqjkPkSKjJmN8mAlXNGPL5ZnanpTW5P7YpQfRV/1ZCphmTAqk3l&#xA;eUoJLSZZUYBlDfCSD0odxiMvepw9yL8ted/Pvke7Emjajc6dVuT29edvIf8AKiblE/zpXLBIHk1G&#xA;JHN9Hfll/wA5XaLq8kWmedIU0i+eipqcVfqbn/iwMS0NfGpXxK5JD36OSOWNZYmDxuAyOpBVlIqC&#xA;COoOKF2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV8A/nPci7/NzzO4NaajJD&#xA;Wtd4iIv+NMBZBB5iuUj7GHipkPVunyyJLOIRWBLHLnyvcS3UsgnXhIWcEg8qk1of65cMopoOEkpF&#xA;JA0FwYrhSpRqOB1p7ZcDY2aCKO7OrMQC1iEBrCEHpn2ptmIebmxqtl8sUcqFJFDoeqsKjACki2Pa&#xA;r5a4gzWNSBu0B3P+xP8ADL4Ze9x54u56T+Qv58X/AJPv4PL3mCdpvKs78FZ/ieydj9tCd/Sr9tO3&#xA;2hvUNc0PsqOSOWNZYmDxuAyOpBVlIqCCOoOFC7FXYq7FXYq+cvz7/wCcjNR0TVZvKvkyZI721PDV&#xA;NW4rKY5O8EKuGTkvR2INDsKEVxS8UT88vzhVhIvmW9NPi34Mv3FSKZHiC0nWk/8AOUH5wWDqZtTg&#xA;1KNf91XdrDQ/NoVhf/hsKvdPyc/5yRsfO+qpoGs2SaVrUqk2kkTlre4ZRydFDDlG1BUKS1fGuxKH&#xA;tWKsd88ef/K3knSRqfmG7+rwyN6dvEil5pXpXjGg3O3U9B3OKvFtU/5zM0CKRhpflq6u4x9hrm4j&#xA;tid+4Rbmn34ppkH5e/8AOUvlXzVrttol/p02i3l64itJXlSeBpW2WNnAiZSzfCvw0J8MUPa8Vdir&#xA;sVdirsVdiqF1XVLDStNudT1CZbexs42muJnNFVEFScVfnVqOpS635pvNVkFJNQvJryQHehlkaVv1&#xA;5CR2ZxG6d2sHqyb/AGF3b+mY5LlAJnkGbsVdirDvMN5HcX7KsfEwkxs/dip/hmTjFBxcsrKFt9S1&#xA;CBQkMzqq/ZTqBU+BqMkYgsBMjkzWzkmktYnmXjKyguvTfMWXNzInbdWwJSLzNpCNbnUoBR4yFu0H&#xA;Qqxosn30VvmPfL8Uujj5YVu+l/8AnFL8xn1zyxP5V1CYyajoQDWZc1Z7FjRR4n0X+H/VKjL3He74&#xA;q7FXYqk3nXXH0HyfretR09XTrG4uYgRUGSKJmQfSwGKvz00yGXVNXLXLtNJKzTXDsSzOxNWLE9as&#xA;d8xdXl4IEt2KNyet6DorFFpEeNB2NM4rVanfm5RKf3HlHTbuEi6tIpq9eaAn7yK5r4do5IH0yI+L&#xA;UaLxfXLa68oedBJpzmKWwniu7CSpqpUiRN+vwsKZ3/Zuq8fBGZ59fg48hRfoPpGoR6lpVlqMQpHe&#xA;wRXCDrRZUDj9ebBg+Pv+csfMdxqP5nfogufq2h2sUSR70EtwguJH+bK6A/6owJSHy3+WdheWkUt1&#xA;6kkrqGcBuIBIrQUpnKa3t2cJERoByBjAG7GPO3l0+WtdSG2ZkjdFnt2qeSkEj7XWoZa5uOydcdTi&#xA;4jzBpqnGi+zv8f3f/Kiv8acv9yP6E+s86bfXPR41pTp6+bRreh4q7FXYq7FUv8weYNH8vaPc6xrF&#xA;ylpp9onOaZ/uAAG7Mx2VRuTir4n/ADh/O7X/AMwr82kHOx8twv8A6Jpqn4pCpPGW4p9p/Bfsr233&#xA;ITTDtL05oPjkFZn2CjsD/HKZytvhCmSQRCKML36k++UFyAFTFUPJexJsPjPt0+/DSDJR/SDV2QU+&#xA;eHhRxIeaHTrkyNLAqzSKV9WldyKV+fvkgSGJAKRaDMYdWiB2D1jYfMbfjl2QXFoxmpMzzFct2Kqk&#xA;HoM5iuBW2mBiuAOvpyDixHuAaj3wxNFEhYpLvy88zX35cfmZaX05Ijsrg2upoKkPayHhKQB9r4f3&#xA;ie4GZgLgkPv1HV1DoQysAVYGoIPQg4UN4q7FUh8/aLPrfkfX9ItxW5v9PuYLceMrxMI/+Gpir4E8&#xA;oXyafr8f1j92r1hkLbcSSOtenxLTNf2lhOTCQOY3cjTzqT6O8q3cQVN88y1+MuZIM/S+s3tOMqo4&#xA;p+0AR+Oc8cUhLZpIfI35k6lB5j/MK8/Q8Ykhkljs7FIgP3jLSP4adecleOey9g6WeDSQjP6uZ+P7&#xA;HFmbL730LTf0Xomn6YG5ixtobbkO/oxhK7/6ublrfGf/ADlLpU9l+b+oXUgIj1O2tbmEnoVSFbY0&#xA;/wBlAcCXo/5a61oVxotnc+jCxeJeYKg0YCjA1r0IzyftrS5Y5ZRs83Oqxbyb8+9e03VPOiQ6eqLF&#xA;YW6wy+mAF9ZmZ2G3gGUfPOw9k9JPFpbnznK/hy/W42Xm+kP8Mah/0LJ+heDfXv8AD/q+jT4ufpfW&#xA;PTp/N+z886lpeuYq7FXYqtkkjijaWVgkaAs7sQFVQKkknoBir4i/Pv8AOG68++YDp+nSMvlfTZCt&#xA;hEKj6xKKqblx71IQdl9ycCQwbTtOW3USSCsx/wCF9splK3IhCk2sU5TVPRRX6cqLZFMGZVUsxoB1&#xA;ORZpbdXnIEk8Il3NfD3yYDAySWXXFDkRx8lH7RNK/RTLRjaTlVINZtnNJAYj49R9+A4ykZA1qs9w&#xA;Iozbk+m1eTpv8txjADqsyeiTfvEkrusgNe4YHLmlP9G1tohImozEKtPTLglq9xsCTlM8fc348lc0&#xA;9tb21ukLW8gkA602I+YO+UmJHNuEgeSvgSlX5jW4efS9UA/3ttFjmPcy2rGFif8AYBDmTjNhxMoo&#xA;vtD8lNfOvflX5bv2blKLRbaZu5e0Jt2J9yYq5a1M2xV2KuxV8sfn/wD84861+mrrzX5Os2vrS+Zp&#xA;tS0uAcpop2NXkhQbujk1Krup7cegS8Tt/NvnfQH+pi6ntJIhT0LiNSygduMysRmvz9lafKblAfaP&#xA;ubBlkOqIvvzP8+6jbSWUuqP6FwODRRRxRkg7cQyIH3+eUYewtJjkJCHqHeSfvNKcsi9p/wCccPyI&#xA;1RNWt/Onmm1e0htD6mj6dOpWV5f2biRG3VU6oDuWo3QDlt2t9RYUPLfz8/KA/mD5ehl03gnmPS+T&#xA;WDOeKyxvT1IHboOVAVJ6HwBOKvk5fy2/N+xlks4vLmuRUYq4gtbkxE9CecamNh7g0yqeGEjcgD8G&#xA;QkQ9E/KP/nGvzbqfmG11PzjYNpuhWzieW2uCvr3TKaiL0wSyKT9svTbYeIsARb6/oKUpt0phQ7FX&#xA;Yq7FXhP/ADlZ+Y76H5Yh8qafNw1HXQWvCpoyWKmjDbp6z/D/AKoYYq+VNGswT9ZcbDaMe/c5Vkl0&#xA;bsceqb5S3IuwZV9Qk0oAa+2+AsoqV1deoSa8Y133/WcICCWN399JdSCKIEx1oqjqx7bfqy+Macec&#xA;7Zr5d/L60Fr62soZJ5ACtuGZRGPcqRVvwyueXuUQ71PV/wAtYyDJpU5Vuv1eY1B/1XHT6fvxjl71&#xA;MGHSx6ppNyYZ42gkG7RuNiPEdiPcZbQkxBIQ08xlnaWnEsa08DhAoIJspxoHlXU9cdpg3pW4J53M&#xA;lTybwUftHxyMpiKRElC3VrqegaoY5Rwlj3BH2JEPceKnHaQUExLKrC9ivLZZ4+h2Ze6sOozGlGi5&#xA;kZWLd5ri+seT4pKVfT776RHdRmv0c4B9+W4S05g+gv8AnEDWjdeQNR0p2q+m6gzIPCK4jVl/4dXz&#xA;IcYvd8VdirsVdiqnNbW84AniSUDcB1DU+/FVsVjZQvzht4436ckRVP3gYqrYq7FXYq7FXYq7FXYq&#xA;7FXYq+Avzk81S+bfzO1m/R+dstwbKw8BBbn0kI9noX+nASyAQEUaxxrGv2VFB9GYxLkgUhIdTSS8&#xA;NuF23CvXqR7ZIw2tiJ2aRtT08euQZpRq99Um2jOw/vD4nwy6EerTkl0Zb5D8qiJE1e9SsritpEw+&#xA;yv8Avw17ntkMk+gRCPV6FZ6bcXW6ALGOrt0+jKCabQERPoVzGvKNhLTqo2P0YBJaSLVNIsdRgNtf&#xA;QCRRWlRRlPip6g5MSI5MSGLwflnpqXYklupJbYGvoUCk79C4PTxoBlhzFjwMvhhigiSGFBHEg4oi&#xA;igAHYAZUzS/zB5SbzFp0iQqBd2ymS3kP81P7uv8Al0p+OSjPhKDG3l+hXr2V/wChLVEkPpyK23Fw&#xA;aAkHpvscvyRsMcUqLNHg+t6JrFl1aS0aaMDrztWE+3+wRhlWM7t2UbMz/wCcPNdFr511fRnbimp2&#xA;ImQV6y2knwin+pM5zJcQvrnCh2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KpJ541h9F8ma7qyNxlsN&#xA;PubiI/5ccLMlP9kBir88tIj53oJ/YBb+H8crmdmzGN0xn1SGG49EqTSgduwrlYhYbTMA06DTEiu2&#xA;uA1RuVWnSvviZ2KUQo2qahdfV7csPttsnz8fowRjZTOVBryZ5f8A0vqfqTrys7Yh569HY/ZT6e/t&#xA;lmSVBoiLevWNobm4WFdl6sR2UZiktwDKo40jRUQcVUUAGVs12KoW8062uhVxxk7SL1/twgqQlEug&#xA;3ik+mVkXsa0P3HJcTGl0Gg3DMPWYIvcDc/0x4lpOre3it4hHEtFH3k+JyDJ4d+bWgrpvmY3cS8bf&#xA;Ul9cU6CUGko+k0b6czMMrDRkFFMPKWoxySWNzP8AFGWEV0D3Vv3clfmpOVkVJyIniigPIOsP5D/N&#xA;nTbu5fhFpeoG2vZD0+ruTBM/v+6csMyg4hD7/BBFR0wsXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXY&#xA;qwH8+7hoPyg8zuoqTarH1ptJKiH/AIlir4a0MfvpT/kj9eVZG7Fzdd6bcyXzFRWOQ159gD1xjMUs&#xA;oG0dbX6zXMkASgjrRq9aGhyBjQtmJWaSzUZnubz04wW4n041G5LE02+ZyyAoNUzZeseXNHTSdJht&#xA;AB6tOdww7yN9r7ugzHnKy2AUGaaJa+lbeqw+OXcf6o6ZTIswmWBLsVdirsVdirsVYD+c2nLceWIr&#xA;wD95ZTqS3gko4MPpbjl2A7teQbPN/KU1YJ4T+wwcf7IU/wCNctzBlgOya/mjpfKTTPMUQ/darbqt&#xA;yR2uYAEf/ggB9xyzGbDVkFF9a/8AOP3nKXzV+WGmXNy/qX2n8tNvHJqS9sBwZierNCyM3ucsano2&#xA;KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KsG/PG0+tflJ5oi48uNk8tK0/uWEtdvDhXFXwpoZ/fyL4p&#xA;X7j/AG5Vk5N2LmnWUtyEmigtY57lFpIwNT7n+3Jgk7MCALK/yFpgvdeSWQVitFMxr/MNk/E1+jJ5&#xA;DQaoDd63aW5uLhIh+0dz4AbnMUltDLFUKoVRRQKAewytm3irsVdirsVdirsVY3+YsSy+StVVugiV&#xA;/pR1YfqyzF9QYz5PFPKbH63OvYx1+4j+uZObkxwc2aeZWE/5XuspFbTUkMHj8aGo/wCGY5HEnM9c&#xA;/wCcM5pj5d8yQEH0Uu4HRuxZ4mDAfQi5kOOX0Tih2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVLvMmkr&#xA;rHl3VNIbZdRs57Qn2niaP/jbFX50adzg1ERyAo9WjdTsQfA/SMhMbNkDun2Y7kIHWCRZEDuwB/Xl&#xA;mPmwycmR/lfGgtr+T9svGp+QBI/XgzNcGfWly1tcJMoqV6jxB2OUENgZBb6tZTAfH6bfyvt+PTIE&#xA;MrRgIIqDUHvgS3irsVdirsVdirEvzTvktfJV6paj3JjgjHiWcMw/4BWy3CLkwyHZ5J5Ks57m8lWF&#xA;C8r8Io1HUsx6fhl+XojD1LLPzV9LRvL+leWkcPcySNf3pH83Exp9G7Af6uSxxpjllb3z/nEzy9Jp&#xA;v5ZvqMylX1m9lnjJFCYYgIF/4eNzlrS9qxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kvgf8AO7y3&#xA;J5Z/NXXLVU4QTXJv7Tw9O6/fAL7KzFPowEJBSg3UCxpI7hVcAqSfHMei5XEFt1Ct1asisDyFUbqK&#xA;jcYg0USFhJ9L1jUdLnM1lMYmbZ16qwH8ynY5eYg83HBpmemfmZEQE1K2KHvNBuPpRjUfecpOHuZi&#xA;bKLDzFol/QWt5G7npGTwf/gWocrMSGYIT/S75racK7H0X2Ydh75WQyBZJkGTeKuxV2KpR5j80aR5&#xA;esjc6hLQtX0YF3kkI7Kv8TtkoQMuSJSAeG+avN+sear9RIClsrH6rZR7qte5P7TU6n9WZkICIaDI&#xA;yLL/AMuorLQGl1PUXEcVhDJcTt1q7UjRF8WPKg98rvik3VwxY7p9jr35m/mFDZ260u9VnCrWpS3t&#xA;03LH/JijFT4/M5eA45L740PRrHRNGsdHsE9Oy0+CO2t178IlCgnxJpUnuckxR2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KuxV83f85h+TGmsdI84W8dWtSdP1Bh19OQmSBj7K/Mf7IYpD5n0vTdU1rUrP&#xA;StOhe7vrp1gtLdKVZnOw3oB13J2A67YKTa++sdb0m/n0i8hmtL6GT0p7NwVkV/Dj13r264CAoJZx&#xA;+Svk29vvzZ0rSdX0WS4gheU6rY3kDBY4jC45TJIvw7kceXelN8KHuvnH/nEXyZqbvceW76fQp23+&#xA;rODd23ToodllWp6n1G9hhQ8O/MP/AJx98/eR9MfVr4W1/pMbKs13ZOzenzPFTIkixuKnaoBHvgSo&#xA;eQ/NIuIV0q9k/wBJj/3mkc/bX+Sp/aXt7fLMfJDqG2Enqei33qxehIf3kY+E+K/2ZjyDaCmmRS7F&#xA;WNed/Oll5b05jyWTUplItLbrv05uOyD8emWY8fEWMpU8D1TVtQ1W8e8v52nuJOrt2Hgo6ADwGZoi&#xA;Byccm0RpGrx2Db26sG+1IPt09q7ZGcLZwnwqlxeaprt9DYWcUkrTyKltZRAu0khPFfhH2mPLbGEK&#xA;ROfE+yPyD/JqPyDojX2pqknmjUkH1xxRhbxVqtujfi5HVvEKDljW9XxV2KuxVDXeqabZyQRXl3Db&#xA;S3LcLZJpERpH/lQMRyPsMVROKuxV2KuxV2KuxVDXOp6bazwQXV3DBPctwtopZFR5G8EViCx+WKon&#xA;FXYq7FXYqlPmvy1pvmfy5qGgakCbLUYTDIVpyU9VdagjkjAMvuMVeaflB/zjrp3kDXrjXbrUv0xq&#xA;HBobBvQEKwI+zvQvKTIy/DsRQEjeuKvUp9A0K41ODVZ9OtpdTtqi3vnhjaeOop8EhHIfQcVR+Kux&#xA;VQv7Cz1Cynsb2Fbizuo2iuIJBVXRxRlI8CDir41/Of8A5x91zyZezax5filv/K7MZFeOrz2ffjNT&#xA;4ig/Zk/4Kh6hLCtD/MnV9PMYukF2I/syk8ZfpahDfSMqliBZiZZmv53aL6QL6fc+rTcAx8a/PlWn&#xA;0ZT+XPe2eKEk1n86tWuEaPSrRLIHb1pD60nzAoqD6QcnHTjqxOUqX5f/AJS+fPzK1T61GskenO/+&#xA;ma5d8vTAB+IJXeVwOir9JUb5kAU1EveG/wCcPfI7ahBL+lr9bFIkW4tV9PnJKoAZxKVPAORUrxPX&#xA;YjCi0x87/wDOK3kbW7Wwi0B/8Ny2QKSSRRm5E6Eg/vRJIjFxvRuXfeu1FWUflj+R/kzyAPrNjG19&#xA;rLKVfVbqhkCsKFYlHwxqfbc9ycVehYq7FXYq7FXzj/zkj+S/nzzT5li8yaAg1S1S0S3fT/UVJojG&#xA;zEmNZCqsrcq7HlWu2KXi8XmP88fJI+rtda5pEMOywXKziBQu/wAKTBo6bdhQjAqv/wBDFfnN6Xpf&#xA;4lk41rX6vacv+D9Hl+OKvQPyF8+/nPr35i2Qvru+1LQphJ+kzcIfqyRiNmVg3EKjcyvHj16dMVfV&#xA;+FDzH/nIbVPP2m+QfrHkwTC6Nyi381ope4jtSrVaMAFh8fEMw3A+/FXy0fzR/PKcGzGtawXPw8EE&#xA;gl+Hr8SqHrtvvgSi9A/JX85fOmppd3VjeQGQj1dW1l5Iio6hv31Zn9uCnFX259Qn/Q36P+tP6/1b&#xA;0PrtPj58OHq0r9qvxdcKEZirsVdirsVdirsVdirsVdiriARQ9MVee+a/yD/KzzLK9xeaMlpeSGrX&#xA;VgxtnJPUlU/dsT4shOKsPP8Azh/+WJk5/pDWAta+mJ7bjTw3tuVPpxW2UeXP+cdfyl0KVJo9FXUL&#xA;hOkuoO1yP+RTfuf+ExV6RFFFFGsUSLHEgCoigKqgbAADYDFV2KuxV2KuxV2KuxV2KuxV2KuxV2Ku&#xA;xV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2Kux&#xA;V2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV&#xA;2KuxV2KuxV2KuxV2KsB/Nj85PL35cWVs19DJfajfcvqmnwlVYqn2pJHb7CCtK0JJ6DrRV57bf85j&#xA;+S2sDJdaHqMV/UgW0RhkiI7H1WeM/wDCYppD/wDQ5flqv/KOXlOx9aL+mK0yTQf+crfyr1JuF695&#xA;o7773cHNCfZrdpv+GAxQwaT/AJzGvf00ZY/LSt5cWX0+ZlYXRQnZuVPSD8fi4fRy74LTT6Q0PWdO&#xA;1vR7PV9NlE9hfxJPbyjaqOKio7EdCOxwoRuKtMyqpZiAoFSTsABir5o/Mf8A5y2uLTVptO8k2dvc&#xA;21uxjk1W8DyLKymhMEaMnwbbMxNfAYpTH8ov+co5/MOvW3l/zbZ29pc3ziGx1G05JEZm2SOWORno&#xA;XOwZW67U74q+h8UOxVLPM3mTR/LWhXmuaxOLfT7JOc0nUneiqo7szEKo7nFXy3r/APzmF5zm1J20&#xA;HS7G001W/cx3ayTzOo7yMkkaivgo28TgTT2L8kvz1sfzEin0+8tl0/zDZp6stujFopoqhTJDy+Ic&#xA;WIDKa0qNz2KHquKuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVKvM/mry/5X0mXVtdvY7Gxi6ySHdm&#xA;pUJGgqzueyqK4q+LfNnnpfzI/NyHVzbGKwHGDT7OdgxEVujOnOnw1eSrlR4036mrMaiWyA3YNPGd&#xA;Q1u+e7/dTPNI7xrRfiLmoFfDG+GIpIFndEfoazpT4vnX+zI+IWfhhCXmlRQxmRZqAdFfv7Aj+mSj&#xA;O2MsdJvoUip5K15boj6u7Q/VVPUzhtyv0ca5Cf1xpEfpL60/5xevEuPye0uMTCVrWe6idAwYxkzv&#xA;IEYD7OzhqHscyGp6xiry/wD5yQ82SeXfyr1H0HMd3qzJptuwNCBOC0v/ACRRx9OKvl78n/J9jrF7&#xA;dajqUC3FpZcUihkFY3lepPIHZgqjofHNH2zrZYoiMDRl9zueydJHJIykLASP8xNNttF863kOnD6v&#xA;EjRzwonw+mzornjTpRunhmZ2ZmlkwRlLm4naGKMMxEeT9AdEu5bzRrC8l/vbm2imkp/M6Bj+JzYO&#xA;CjMVfMX/ADmP5sk9bQ/KcLkR8W1K9QHZiSYYK/LjIfpGKQw/8tfy80Sfyp9d1W0S4udUVuJkFTFD&#xA;Uqnp1+yxpy5DfOV7T7RyDNwwNCH2nzek7O0EDi4pizL7Axz8hLu50785fL4gehe5ktZPBo5I3jYE&#xA;fTX551ETYt5yQo0+8MkxdirsVdirsVdirsVdirsVdirsVdirsVeF/mf/AM5Q6R5d1C90Dy3YPqmv&#xA;Wcz2000wKWscyHi6gKfUlZWBUgcR/lYCU0+bvPuq+dtf9LzB5tv3nubl2js7VzT00G7cIlokSjYU&#xA;G5PXK45ATQZGNBjdgl9CV1C0YrLbuGjZeoK71GSkRyKQDzDIb648seYqXby/ofWnI+sAitvI3eQH&#xA;9k+NT9/XKQJQ25xZEiXkVx8l6xx5W+s2M8X7LJck7f8AA4PGj1B+SeE96hb6HoMBNxr2srJ6f/Hp&#xA;aVlkf25n7P0jJGcjtEIodShda1WTWeEFharZ6VZKRb26+w+07ftOf8/HDCPDzNkoPq5cnrP/ADiP&#xA;5vn0/wA8XXlqSQ/U9bt2eKKuwubVTIGFfGL1K08B4Zc1vr7Ch8vf85ma0zXvlvRFNFjjnvZV7EyM&#xA;sUZ+j03+/AkJf+UmnLZeSLR6UkvHkuJP9k3Bf+ERc4vtjLxag/0dnreysfDgHnu8q/NqUP581ED/&#xA;AHWsCk+/oof450PY4rTx+P3l0fahvPL4fc++dDtzbaLp9sa1gtoYzUUPwRhdx9GbR1qNxV8Kf85B&#xA;atNrn5x62qVYW00Wn26eHoIsbAfOXkfpyMjQssoi9nsmn2cVhp9tZx7RWsSQoenwxqFH6s88yTM5&#xA;GR5kvdY4CMQB0DxP8iY/rn5zeXONVrdyTdK7JFJJT/hc9DiKFPCyNl955Ji7FXYq4kAVPTFXnHmP&#xA;/nIX8ptBuXtLjWlu7qM8ZIrGN7kA96yIPSqO451xVDaL/wA5KflDqlytt+mGsZHPFDewyQxmvjJQ&#xA;xqP9Zhir02CeC4hSeCRZYZVDxSoQysrCoZWGxBGKr8VdirsVdirsVdir5r/PX8i/Mo8zSeePI0DX&#xA;M1yTJqenRU9UTEUeWJT/AHiyj7aDetTuD8MZRBFFINPEPP8A5P8AzC0S10zUPN1o9nHqHrLYQOyV&#xA;URFC9Y0J9OvqCnLc4IwEeSTIlLNJKmxjp2rX51OVT5t+PkqTaZbXFWMfxDdmXY/TgEyEmAKF1PQ4&#xA;bWwW6id2NV5K1KAH5Ad8nDJZphPHQtV1XQbW28u6fqluJS1yQJmcgpUqfs0UdGUjqcEMhMiCwMdr&#xA;RNzLa2tpDOKLFMgZI167itAMiASW8kAWnv8Azj0Cfzl8tcQf76bp4fVpa/hmQ4r7xwofP3/OV35a&#xA;arrmm2PmvSYWuZtIjeDUbeMFn+rMeayqo6iNuXKnY16A4pfPvlr81fMWg6YNOijgureOv1f1wxaM&#xA;HfjVWWq18c1Wq7JxZp8RsHrTstP2nkxR4RRHmu8meWvMf5k+foYUga4e6uEl1S4VaRQ24I5sx6KA&#xA;i0UHqduubDDijjiIx5BwcuUzkZS5l+gOWtTsVfGX/OS/5b6v5d88XPmi2id9F1qUXCXSAkQ3R/vI&#xA;3YfZLMOaeNaDocBFpBYpJ+c/muTSjZMlv6zR+m17xb1CCKcqcuHL6Ke2acdiYRPi3rudqe18phw7&#xA;X3s4/wCcUPI2sXvnhPNj2zx6PpUUyxXbghJLiZDD6cZP2uKOxanTv1zcOqfYGFDsVdir5I/5yR/O&#xA;/UNV1e78meX7hoNFsXMGp3ETFWup12kj5D/dSH4SP2jXqKYEsR8pfkPreq2aXur3I0qKUBorcx+p&#xA;OVPd1qgSvuSfEDAZJpG+Yv8AnHvU7W1e40TUBfugJ+pzIIpGA7I4ZlJ9jxwcS01+R35zat5C19NF&#xA;1mWRvLFxL6N7bS8ibOQmhmjU7rxb+8XuO1aZJD7YR1dQ6EMrAFWBqCD0IOFDB/ze/NLTvy78sfpK&#xA;aMXOpXTGHS7KtBJKBUs5G4jQbtT2HfFXyDqP5vfnH5ivp7+PXdTX0vjkh015beCJOorHb8V4inVq&#xA;/PAl6v8AkT/zkfrNxrVr5X86XH1uK+dYdO1ZwBKkzGiRzkUDq52D9QetQaqq+oMKHYq7FXzl/wA5&#xA;mzwDRPLEBp68lzcunSvBI0D+/V1xSHzno+1lUnbkT9GUZObfj5I/TdXsWuTbgn1HNFanwmnauQlA&#xA;1bOOQXSh5g1i3ML2MIEjNQSN+ytDWg8TtkscDzY5cg5Jjb3huPyxntXT4baekUnYN6iyEfdIcgRW&#xA;W2APoYczzzlF3covFFG9FUVzJ5Nd2+tv+cU9I/Lu48rnWNMsgPNlozW2r3E7+rKnOpRohsqRyKNq&#xA;KDsVJalckh73ih2KsR1X8o/yz1a8e9v/AC1YS3UpJlmEQRnY9Wbhx5H3OKsg0bQdE0SzFlo1hb6d&#xA;aA19C1iSFOXdiEAqT3OKo7FXYqpXVra3dvJbXUKXFvKpSWGVQ6Op6hlYEEfPFWIL+S35ULc/WR5V&#xA;071K8uJhUx1/4xn4Ke3HFWYwW8FvCkFvGsMEShI4o1CoqjYBVFAAMVX4q7FUj8861JofkvXdYi2m&#xA;0+wubiH/AIyRxMyf8NTFXxB+Tegw6757gN2PWiske+kR/i5sjKqVr1/eSKciWQfXeneULqdBJdP9&#xA;XU7hKVenv2GV2ypGTeSouH7i5YP2DqCD91MbWnyz/wA5H+T20PzTZ35iER1SJ/V4/ZeWAqGkH+sr&#xA;r92TiWJfTX5Ba7PrX5SeXbq4cvcQwvaSMak/6LK0CVJ6koinJsXzj/zlh5gn1D80DpZc/V9FtIYU&#xA;jrsJJ1Fw7fNlkQH5DAl65+XH5c3+h+TNPhgtOMksK3F0xKq7yyKGYkE12rxHsMrJZAPnj85fLcXl&#xA;7zxL9UQ28N4i3kcajj6chZldVApx+NCfauTCC+uP8d3P/KjP8Zc/9P8A0H9a57f71+hT/k9kmL0H&#xA;FXYq+Mf+cqvOEWt/mMNKtpA9r5fgFqxBqPrMh9Seny+BD7qcCXklzcmO2js0NAorKf8AKO9PoyAG&#xA;9sydqRN95Z1qw0q11aeEpaXX92wPxL3XmP2eQ3XIQ1EJSMRzDOWCUYiR5FNfLfkWfU9B1PXLl2t7&#xA;KytppLYAfFNLEhagr+wCKE5i6nXDHkjjG5kRfkC5Gn0ZnCUzsADXmx+yh+sLLG8rLFDG84QbgsoH&#xA;atBXxzOkacOItGeU7+ysPMFnc3qc7VWKyV7B1Kcv9jyrkcsSYkBYGi9I/L7zSn5c/mza3cDkeXNX&#xA;YW94m/FYZmArv/viSjDvx274MM+KO/NOSNF9r5c1pV5l80+X/LGlSatr19HYWEZAMshNWY9FRVBZ&#xA;2P8AKoJxV41e/wDOYfkKK79K10nUrm2BIa4IhjrToUQyEkH/ACqYrT0P8vfzk8iefOUWi3bR6hGv&#xA;OTTLtRFche7BQWVwO5RjTvirN8VdirC/zD/N/wAkeQokGt3bPfyrzh0y2AluXXpy4kqqKabF2APb&#xA;FXl9t/zmT5Ta7CXGgX8VoaVmSSGSQf8APMlB/wAPimnsvkzz35W856V+kvL18l3ApCzR7rLE5/Zl&#xA;jajKdtux7VGKE/xV2Ksf/MLSJtZ8ieYdKgBNxeaddQwKOpkaJuA+lqYq+O/+cbdb0/SvzTso9QKp&#xA;HqMMllE77KszlXirXuzxhB7kZCXJkH2tlbN2KvlH/nLjzLaX3mrSNCgYPJo9vJJdEfsyXhQhD7iO&#xA;JW/2WWQDGT3f/nHrRp9J/KDy9DOpWa4ikvCD/LczPLH98bqcmwfMv/OUFhNa/nJq08gIS+htLiGo&#xA;pVFtkgNP9lC2BL668o6ta6v5W0jU7Vg0F5ZwTJQ1pyjBKn3U7H3yktj5W/5yu1CxuPzJgtbahlsr&#xA;CJLwjtK7vIAff02Q/TlkeTAvbf0Be/8AQrf6N4n6z/h76xwp8X919Z4Ur1ptk2L2HFWGfm3+Ytl5&#xA;C8m3WryFW1CQGDSrZjvJcuPh2/lT7Tew8SMVfAs1zcXd1Pf3cjTTyu008rmrPLIxYliepZqk5Esg&#xA;mPlezsZ9R+tanIqafaUlnZ+jt+ylO/Ijp4DIZLqhzZ46uzyek2/5jeV7iG4ErkRwryMcqD94K0oi&#xA;78jWmxzVz0U72djDWRrdhPmH8x9b1JZLS0IsdLZGi+rRhatGw4kO1O4PRaDMrDoIRNneTj5dbOQo&#xA;bRY3aSCNLk13aEqo8eTqD+GZhHJxInmzj8tfytv/ADv5b82XWnxPNqOjQW0lhEtB6ssjsXjFepMU&#xA;b0H81MkxZ/8AlX/zj55y8x61Zan55t5dO0LTOAjsZxwnuPSIpH6fVENPjdt27eIEYgckmVvrbJMX&#xA;xN/zkz52vPMX5lXWlI5Om6CfqVrCDsZtjcSEfzGT4PkowJDCbfypbeiPrEjmYjfgQAD9IOUHKejk&#xA;jCOqASXVvLGt21/p1y9veWriazu4zxYFT/mCDsR7ZbCVhpnDhL71/K/zoPOnkXSvMRjEM93GVuol&#xA;+ys8LmKXj1+EuhK+xGTa0/1XUbfTNLvNSuK/V7KCS4mp14RIXb8FxV+eGs6vqvnHzXeatqEpa91K&#xA;ZppmJJCL+yi1/ZRAFUeAGRlKhbOMbNI2TyrYmLjHI6ygbOSCCfcUygZS5BwhF/lh551P8v8Az1aa&#xA;isjJaCVYNWtwfhltmYBwR3Kj409xl4NuMRWz9AFZWUMpBUioI3BByTFvFXYq+Hv+cjPJFj5O/MmQ&#xA;6XKEttVjGqQ26fC1u8kjq6in7PqRlkp06dsCX0p+W/m+7T8ntL8z+brnjJFZyXF7duKs0McjiKQg&#xA;bs7xKp8STlRG7MPMPOP/ADlzYmxkg8o6VOLxwVW+1AIqx9uSwxtJzPhyYe4PTJCCOJ5r+Un5Y+YP&#xA;zQ84NqGqGaTRkn9fXNVkJrKxIZoUc/alkr2+yN/AGbF9xwwxQQxwwoI4olCRxqKBVUUAA8AMKHiH&#xA;/OT/AOVN95p0W28x6Jbtcaxo6tHcW0YLST2jHlRFFSzRNVgo6gt3oMVeFflr+f3m7yLpn6Higg1L&#xA;SVcvFbXPNXhLGrrE6nZWNTQg77+OQMbZAsX/ADF16217zrf69bgelqRhuzCx5em8kKNJCSKV9N6p&#xA;XatK7YQpfoD6lv8AoT1fQX6v9W5fVtuPD06+n06U26ZJiu1nWNM0XSrrVdUuFtdPs4zLczv0VV+W&#xA;5PYAbk7Yq+Evzd/M/U/zG81m9KvFpdvWHSLEn+7iJ3dwCR6knVz8h0GAlIDC7sJHxt0IbhvIw7ue&#xA;v3ZGPeyltsi9C8t+YNeknh0iylvPqkT3NyYxVIoo1LM7sfhUUXv16DfJMVHRNE1XXNVttJ0m2e71&#xA;C7cR29vGKszH8AANyTsBucVfWmg/847aF5Y/K/XobyKPUfNd/plyst8V5iGQwsyR2wboFcCrbM3s&#xA;KAFD49wJfcn/ADjh5Em8p/lxbPeR+nqetP8ApC6UijIkigQxnv8ADGAxB6FjhQ9SxV2Kvz2/MWKa&#xA;0/NTzGLzd01q7kkYjjyDXLPyoOgZTXIy5Mo80PruuyRSG1tW4sv95KOoPguU48fUt+TJWwZB5J/I&#xA;n8zPOYhvLawNpptxRhql+3pRlT+0qmssgPYqpHvl9OOS+zvy78lWnkrydp3ly2lNwtkjercMKGSW&#xA;RzJI9N6AuxoOwwoR3mzRn1vyrrOixuI31OxubNJD0U3ELRhj8uWKvzwuLfWPLur3FndwPaajas0N&#xA;xBMpDKQdwQcjKNs4yI5K9v5ov0lBnCyRk/EAKGntTKziDYMx6oXWp4rnUpJYTyRwlCO54jJQFBhk&#xA;Nl+jGgQzwaFpsE4KzxWsKSq3UOsahgfpyxrR+KuxV85/85b/AJeanqdtp/nDTYGuBpsLWuqJGCzJ&#xA;ByMkctBvwRmfme1QelcUvC5fzf8AOkv5fL5EaeL9DLRfU4H6wYVbmsBkrTgG/wAmvatNsjSbesfk&#xA;D/zjxpeuaO3mTztZTGCd1Ok6ezvCJIgCWllC8XKuSOG42FdwRhQ+n9M0vTdLsYdP021is7G3XjDb&#xA;QIscaDrsqgDChE4q7FXhP/OSH5LN5i0mPzB5W0yI67Zu76hBbRqs15C4FW+EfvJYyuw6kE03oMUv&#xA;Afy4/Jnzj5s8z21hPpd1ZaXHMv6UvriF4UiiU1kUFwtZCNlUb19t8CvvL0ovS9LgvpceHp0HHjSl&#xA;KdKUwofGX/OSP5sax5k803vlaINaaFod1JAYKkNcXELFGmk9gQfTHhv32CXkOnWGo6hdpaadbTXd&#xA;3L8Mdvbo0kjeyogLHFXuX5c/84oeZtWeK+84SnRdNNGNjGVe9dfA/ajhqP5qt4rir6i8r+TvLXlb&#xA;SF0jQrCKzsQPjRRVpDShaV2qzsfFjhQhPLf5b+RfLN/cahoOi22n3t0Css8SnlxJ5FUqTwUn9lKD&#xA;FWRsqspVgCpFCDuCDirwnyt/zid5a0bziNavNSfUtJtpTNY6TLCFowNUE8nNvUVPDiOXfbYqbe74&#xA;odirsVfMX/OT35Mapc6nJ558vWzXUcyIutWkKlpVdBwW4VFHxKUAD06U5dCSFLzX8kfyn1vzb50s&#xA;Jb3TpR5esZVudTnnRlidIzyEALAczIw4kDtU4FfcyqqqFUAKBQAbAAYUN4q7FUBqegaFqpU6pp1r&#xA;flPsfWYY5uPy5q1OuKvEP+ciPyIvPMcVjrXk3T4BqFmjQXunwiOAzRV5I6fYQshqCCdwdulMUsF/&#xA;Jj/nG/zVceZ7bV/OFg2m6Pp0izi0mKmW5lQ8kTgpYrGGALluo2HWoCvrjCh2KuxVxAIoemKscX8t&#xA;/wAvl1EakvlrTBfg8xcC0gD861514/ar+11xVkeKuxV2KuxV2KuxV2KsW1r8rfy71zVG1XVvL9ne&#xA;ahJx9W4kj+J+Gy86UDbbb9sVTfSdC8uaFCYNI0600yJvtR2kMcINPERha5VPNCHMshElGi6hrTlQ&#xA;++VDW4iatPhlVBBFQajMkSB3DCnYVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdi&#xA;rsVdirsVdirsVdiqHu7n0lAX7bbAfPNfr9Z4UaH1Ftxw4nnXnz8zX0O+Oi6Na/pDWlVXumY8YoA4&#xA;qoZv5iN6eFPHNTlycA3O56/qDkwgDueSWaF528wyyKda1Wxg9QA/VhBcPQn9lnEaqp9xt75izlE7&#xA;GfzH4pkTEcg9F069mWKOUvHNby7pNC3ONgfBst0+fLp5DiNwPVhKMZ8uadKwZQw6HOphISFhwyKb&#xA;ySHYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FXYq7FWM+bNV/RsD3zgm&#xA;K0/fzAdfTi+OSn+wU5ynaMjLVRHcfuc7DH0vPYdOtpPOWr3E9JRNJLdxMCD6iEBkKnv+7K09sZbZ&#xA;iT0jt+lMRZA+COtDp+qade/6EsP1VPU+E1BBr1NBvtkdPqBkjIGOwcvWaTwa3u0P+XOty2/mufy4&#xA;0hexvLd54UJ+xLEw+z/roxr/AKo98lp4CpQ/gvb4i3DkNrHR6zp7FrcV7ZueypmWEW42YepE5smp&#xA;2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxV2KuxVJ9b023vEltrkVtru&#xA;OSCan8sqFG/Bs53tDDw5xPodvm5WKXpeFQ3es+UdSTQtcJElkaabduDwlhU/CyOKGlDuAfh6ZHJA&#xA;yHmHIMRIWE+uPN13q0Y0ywhjVro0aG1HKSUnr0C0HiT2zHOPJP00IxPOurCQkfqKJ/Ljy/cL5q1D&#xA;zBMQ0MMbWOmkdJXJX15kp1jVlKo37XUZLNmGMVHn0ZyFgB7BZxenAqnr1ze6DD4eIBwcsrkrZmtb&#xA;sVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdiqyWJZUKt0ynPgjljw&#xA;llGRBSvUdKgu4DbX1pDqFrWvpXCLItR7MGH4ZpcmnzYuQ4w5EZxPWkuj8u2kMckGnafb6bFMOM31&#xA;SJIWZT+yWQLtmHP8zPaMeFsBiNybTTStEtrKJURFREFFRRQDM7QdkcB4p7lqy572CaZvnGdirsVd&#xA;irsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdirsVdir//Z</xmpGImg:image>
               </rdf:li>
            </rdf:Alt>
         </xmp:Thumbnails>
         <xmpMM:InstanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:InstanceID>
         <xmpMM:DocumentID>xmp.did:b1c4be4d-f119-2241-9620-a193861f7968</xmpMM:DocumentID>
         <xmpMM:OriginalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</xmpMM:OriginalDocumentID>
         <xmpMM:RenditionClass>proof:pdf</xmpMM:RenditionClass>
         <xmpMM:DerivedFrom rdf:parseType="Resource">
            <stRef:instanceID>uuid:4ebd1503-6f23-441b-8106-aec34809a486</stRef:instanceID>
            <stRef:documentID>xmp.did:0C860B45B6B6E6118615C125FBEA94CC</stRef:documentID>
            <stRef:originalDocumentID>uuid:5D20892493BFDB11914A8590D31508C8</stRef:originalDocumentID>
            <stRef:renditionClass>proof:pdf</stRef:renditionClass>
         </xmpMM:DerivedFrom>
         <xmpMM:History>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:513255365482E611A619EB9F7173BB09</stEvt:instanceID>
                  <stEvt:when>2016-09-24T19:41:28+07:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CS5.1</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
               <rdf:li rdf:parseType="Resource">
                  <stEvt:action>saved</stEvt:action>
                  <stEvt:instanceID>xmp.iid:b1c4be4d-f119-2241-9620-a193861f7968</stEvt:instanceID>
                  <stEvt:when>2020-07-31T13:55:05+02:00</stEvt:when>
                  <stEvt:softwareAgent>Adobe Illustrator CC 23.0 (Windows)</stEvt:softwareAgent>
                  <stEvt:changed>/</stEvt:changed>
               </rdf:li>
            </rdf:Seq>
         </xmpMM:History>
         <illustrator:StartupProfile>Print</illustrator:StartupProfile>
         <xmpTPg:HasVisibleOverprint>False</xmpTPg:HasVisibleOverprint>
         <xmpTPg:HasVisibleTransparency>True</xmpTPg:HasVisibleTransparency>
         <xmpTPg:NPages>1</xmpTPg:NPages>
         <xmpTPg:MaxPageSize rdf:parseType="Resource">
            <stDim:w>260.000000</stDim:w>
            <stDim:h>260.000000</stDim:h>
            <stDim:unit>Pixels</stDim:unit>
         </xmpTPg:MaxPageSize>
         <xmpTPg:PlateNames>
            <rdf:Seq>
               <rdf:li>Cyan</rdf:li>
               <rdf:li>Magenta</rdf:li>
               <rdf:li>Yellow</rdf:li>
               <rdf:li>Black</rdf:li>
            </rdf:Seq>
         </xmpTPg:PlateNames>
         <xmpTPg:SwatchGroups>
            <rdf:Seq>
               <rdf:li rdf:parseType="Resource">
                  <xmpG:groupName>Группа образцов по умолчанию</xmpG:groupName>
                  <xmpG:groupType>0</xmpG:groupType>
                  <xmpG:Colorants>
                     <rdf:Seq>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=71 M=67 Y=67 K=80</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>23</xmpG:red>
                           <xmpG:green>22</xmpG:green>
                           <xmpG:blue>20</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>White</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>255</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>Black</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>35</xmpG:red>
                           <xmpG:green>31</xmpG:green>
                           <xmpG:blue>32</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=0 G=0 B=0</xmpG:swatchName>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:red>0</xmpG:red>
                           <xmpG:green>0</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>C=1 M=3 Y=7 K=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>250</xmpG:red>
                           <xmpG:green>242</xmpG:green>
                           <xmpG:blue>232</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=234 G=255 B=0</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>233</xmpG:red>
                           <xmpG:green>255</xmpG:green>
                           <xmpG:blue>0</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=166 G=149 B=121</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>165</xmpG:red>
                           <xmpG:green>149</xmpG:green>
                           <xmpG:blue>121</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=94 G=68 B=52</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>93</xmpG:red>
                           <xmpG:green>68</xmpG:green>
                           <xmpG:blue>52</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=150 G=130 B=90</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>149</xmpG:red>
                           <xmpG:green>130</xmpG:green>
                           <xmpG:blue>89</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=237 G=142 B=47</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>237</xmpG:red>
                           <xmpG:green>142</xmpG:green>
                           <xmpG:blue>47</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=157 G=161 B=22</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>156</xmpG:red>
                           <xmpG:green>161</xmpG:green>
                           <xmpG:blue>22</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=255 G=252 B=204</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>255</xmpG:red>
                           <xmpG:green>251</xmpG:green>
                           <xmpG:blue>204</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=102 G=58 B=14</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>102</xmpG:red>
                           <xmpG:green>58</xmpG:green>
                           <xmpG:blue>14</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=254 G=234 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>253</xmpG:red>
                           <xmpG:green>233</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                        <rdf:li rdf:parseType="Resource">
                           <xmpG:swatchName>R=87 G=93 B=102</xmpG:swatchName>
                           <xmpG:type>PROCESS</xmpG:type>
                           <xmpG:tint>100.000000</xmpG:tint>
                           <xmpG:mode>RGB</xmpG:mode>
                           <xmpG:red>86</xmpG:red>
                           <xmpG:green>93</xmpG:green>
                           <xmpG:blue>102</xmpG:blue>
                        </rdf:li>
                     </rdf:Seq>
                  </xmpG:Colorants>
               </rdf:li>
            </rdf:Seq>
         </xmpTPg:SwatchGroups>
         <pdf:Producer>Adobe PDF library 9.90</pdf:Producer>
      </rdf:Description>
   </rdf:RDF>
</x:xmpmeta>
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                                                                                                    
                           
<?xpacket end="w"?>
%  &&end XMP packet marker&&
[{ai_metadata_stream_123}
<</Type /Metadata /Subtype /XML>>
/PUT AI11_PDFMark5
[/Document
1 dict begin /Metadata {ai_metadata_stream_123} def
currentdict end /BDC AI11_PDFMark5

%ADOEndClientInjection: PageSetup End "AI11EPS"
%%EndPageSetup
1 -1 scale 0 -64 translate
pgsv
[1 0 0 1 0 0 ]ct
gsave
np
gsave
0 0 mo
0 64 li
64 64 li
64 0 li
cp
clp
35.999 44.9668 mo
35.0898 44 33.0898 42 32.2393 41.2695 cv
31.4961 41.0215 30.7051 40.9004 29.8818 40.8555 cv
25.7646 40.6309 20.8477 42.3066 17.1123 39.4854 cv
16.5498 38.7051 16.1172 37.8486 15.8828 36.9697 cv
14.7559 32.7383 16.9268 25.5732 19.4746 22.4609 cv
21.2383 20.3096 24.6221 18.1436 28.6943 17.4814 cv
29.5098 17.3486 30.3516 17.2764 31.2139 17.2744 cv
37.3945 17.6221 42.4512 21.0293 45.3223 25.7988 cv
45.8008 26.5938 46.2178 27.4268 46.5713 28.2881 cv
49.0898 35 49.0898 44 43.0898 50 cv
39.0898 53 36.0898 49 36 44.9678 cv
35.999 44.9668 li
cp
false sop
/0 
[/DeviceCMYK] /CSA add_res
.425849 .581689 .653254 .557549 cmyk
f
29.0449 35.834 mo
29.4111 35.834 29.7813 35.8428 30.1543 35.8633 cv
31.5049 35.9365 32.7051 36.1533 33.8223 36.5264 cv
34.4365 36.7314 35.0059 37.0547 35.4971 37.4766 cv
36.5947 38.4189 38.7783 40.624 39.6416 41.541 cv
40.4541 42.4053 40.9316 43.5293 40.9922 44.71 cv
43.9688 40.2441 43.4551 34.2686 41.917 30.1172 cv
41.665 29.5117 41.3691 28.9268 41.0381 28.3779 cv
38.8691 24.7744 35.2471 22.5566 31.0811 22.2754 cv
30.5527 22.2842 30.0215 22.3311 29.498 22.416 cv
26.6904 22.873 24.3838 24.3594 23.3418 25.6309 cv
21.6895 27.6484 20.0508 33.1904 20.7148 35.6826 cv
20.7676 35.8457 li
21.8447 36.2578 23.4355 36.1777 25.7041 36.0029 cv
26.7881 35.9189 27.8994 35.834 29.0449 35.834 cv
cp
41.4395 46.4199 mo
41.4492 46.4199 li
41.4395 46.4199 li
cp
40.3594 56.0498 mo
39.1338 56.0498 37.9209 55.7744 36.7725 55.2158 cv
33.9189 53.8281 31.8652 50.8096 31.2158 47.2178 cv
30.7549 46.7529 30.2646 46.2695 29.8447 45.8643 cv
29.6094 45.8477 li
28.7246 45.7998 27.6289 45.8848 26.4717 45.9736 cv
22.9961 46.2383 18.2451 46.6064 14.0996 43.4756 cv
13.7002 43.1738 13.3486 42.8145 13.0566 42.4092 cv
12.1113 41.0977 11.4365 39.7012 11.0518 38.2578 cv
9.46484 32.2988 12.1777 23.4805 15.6055 19.2939 cv
18.4199 15.8613 23.0117 13.3398 27.8916 12.5459 cv
28.9834 12.3682 30.0977 12.2773 31.2021 12.2744 cv
31.3096 12.2734 31.3975 12.2773 31.4951 12.2822 cv
38.9658 12.7021 45.7363 16.791 49.6064 23.2197 cv
50.2109 24.2246 50.7461 25.291 51.1973 26.3896 cv
51.2529 26.5313 li
54.2041 34.3975 54.5391 45.6221 46.625 53.5352 cv
46.458 53.7031 46.2793 53.8584 46.0898 54 cv
44.2793 55.3584 42.3018 56.0498 40.3594 56.0498 cv
cp
.757687 .679133 .626856 .856168 cmyk
f
%ADOBeginClientInjection: EndPageContent "AI11EPS"
userdict /annotatepage 2 copy known {get exec}{pop pop} ifelse

%ADOEndClientInjection: EndPageContent "AI11EPS"
grestore
grestore
pgrs
%%PageTrailer
%ADOBeginClientInjection: PageTrailer Start "AI11EPS"
[/EMC AI11_PDFMark5
[/NamespacePop AI11_PDFMark5

%ADOEndClientInjection: PageTrailer Start "AI11EPS"
[
[/CSA [/0 ]]
] del_res
Adobe_AGM_Image/pt gx
Adobe_CoolType_Core/pt get exec
Adobe_AGM_Core/pt gx
currentdict Adobe_AGM_Utils eq {end} if
%%Trailer
Adobe_AGM_Image/dt get exec
Adobe_CoolType_Core/dt get exec
Adobe_AGM_Core/dt get exec
%%EOF
%AI9_PrintingDataEnd

userdict /AI9_read_buffer 256 string put
userdict begin
/ai9_skip_data
{
	mark
	{
		currentfile AI9_read_buffer { readline } stopped
		{
		}
		{
			not
			{
				exit
			} if
			(%AI9_PrivateDataEnd) eq
			{
				exit
			} if
		} ifelse
	} loop
	cleartomark
} def
end
userdict /ai9_skip_data get exec
%AI9_PrivateDataBegin
%!PS-Adobe-3.0 EPSF-3.0
%%Creator: Adobe Illustrator(R) 10.0
%%AI8_CreatorVersion: 23.0.1
%%For: (ABCDetective) ()
%%Title: (Wraith_02_Left Hand.eps)
%%CreationDate: 7/31/2020 1:55 PM
%%Canvassize: 16383
%AI9_DataStream
%Gb"-6H&l4REA.)O!IfgD!sD.9ON;h^Sd)-qrU_/=5dEKbCdXk@.@E(1JX9FVc)72d1XAtTbsf'PZ]a.GN]NFQ:G.d7r$(]Yh`JRL
%md2s:hZ!NLmf37ENUPig8Smc@j8s6dHMluRhtpcaIe!!`q_(%GHh#p]QZ#+dr;F5D0>IJZo^;CapYYu>k4Ij"Df:*qr:f^&o:+XM
%I/WotT5K[mT>1.[ps.j=q7!i:?GF.$p\rG/%l`UL,@^R&IJHt%_RH0>Hr'D2gE402dP"sVp])ms](,@[SUU.!s"iOGaA6*_hA:R'
%>q[)PGaDq-2t#i*V]Y`(TDU+or8pNBk5YA#r;c0Y^Yk!<CM=O?PhLPEpa25kYnm-%9>dr6r,/M'jauB-j6Ntd:%;!J*L@GPl(p@p
%p#ObgcSsb]`K.Y>2h1_"ghr-:nnEJ/pY6G4&U^E%!3(6g'1-K&^R?0jeOKl%mGkW(VNq<i),%.lpa'!>EL":<nOpj=QU2NJ$oo$7
%1</5l5%023k)TD)6`g4A;Xh;t@q1/#0>l_c;`ME'?5@"%bOEo?;eN]*)r2YBQQfJcC5N%abrHkLi3-&E;LksJ3kcQE:RN\P.:DI6
%n#co+?`*_:?'!X/e$l!4E!c/)koo?2R2Uj)J;2^q-2HV9BNtoch^%)AN!o=gF<i`I@I)jhjaC%J/`?fn#'0k'PT0c7Gg81a8+>&h
%D(IZjH$lrF?Tn*SD5@seZf4&o(I+oK_<nu:&&3AbqcD[(GlR]ZX*8L^M8"91ET-$KbEm^'c6'h'J2IVoqs`Rhq;q(:J+rdAYEPGq
%'tjM7K,_Q#ci/HZDs:KQTf7P:m\G([bm7LEGl0KQgV1T"i?mD,^<b,TI`?a(5hm&t"616^DX4KcMS;lGUfIHoh&kK9J,aHEmW05X
%jR6Ehq8;O]+'oR!$6_RWn7aNEhu&79%'5.8Hr<(KqTY4-'!04pi!K0k%e,6Y3geOB5JDI>V]XdnYF!SMH21ckeD^@]?bZC:J/=IR
%s+L0*mp<bVs/WW45!X2"Ra)$.PBYosHju,j^3ohS3O,Wa([/>5nTW=:s8M*b@']6-Iu!(V_jff\Z_)^@k1T>j^I1i^jX*4h_ooN=
%p[7LLRD)(6Af*QN@>2O^L@n_f@/]#&i]Z3[AR3A2CW0B`etKKQ*5S6LHi;Xp=207Xp4lFkJtC<!#j_HDpTM:KS@=6B`kQcC[HXUT
%O0f^prkjMXDtO-Yq!I;n'+]:_J+?7FpZ:d$Vg!&u&AH+JO0B8R!agHUme?;:pk(b_._be44:DgNqpTcYKi`2til@+/b+H*!rquG5
%nDW9=J+qb@If/n`miF^iro'>js8;HMfrMS8Dtq3hqXb"OhuCa\h*8n!q59O.N5`\1J01F3paJJOn3?D5X8p!+j)86f+.Lp3kHFmS
%K7!%abC=m7iPiQZf"JVO:V-G\^O5S1h0YO7hq\.rTDoZ*TR2Ii5+[8n?:`7YhZTS7J=k`g#H>iNIXEm/h\kW\)[^`Br>sBHQn.WJ
%<8bC\VuH^XmfPA!NYiN/@s!:VKC,[VZn6WPO<b>;rtk(uFMi6,hZ!',lZ[ioqfB.UBu&&%5C`S.rA:IFIfKEK?\Zk.EB=hA#4MmD
%SMG((UAk7<`eM/(nEU=e)&U.2@$G!rEO-eU]jU^#6+;1>iEnkJHhmB)Da2!\I-!"ZYMfF`YK%,t##4)=4euMakHA7-qYg<SJ,#@3
%05cg=^\d_3DZ9([]@UD_O^T.S*N;"M[Nl5T+3*UpatVitfo8!gl07@(;KK+N%q7_PIe1nS?GH@1c!WPW3Pjo.dTFU[>!oBA+.K<_
%\&8De-4\tE2s<=@5O^UaR8C#:YJ@kC6H]jOde!2T?G1U^ns=k*p;>(/XYnm8hS9$B>Fni%E6pok%'JgP\Bu%8$k-tHgn9]HQ2a13
%@c.$fb=K(1)J!:]DCJT\oeEU&pD5Zmedp_^7SN->riY#lgiW=BFL``(mIJ1>'3&8kJKGM>QWsGjCo/c4S'*ao5c2)t=l+GK0E:Ya
%"Li$IrqW-k#m^<NYMOj@Eq@KC?@@(?6CM8)8(([M\q2q[RPl&:BPOop*Yn]qZloAajQfrtIe)Ri,LChqcLUYc^46.,.hL$V#Yh9@
%R8fQh'j++_>D(oCs201s$N1MF7L$7!J#q"MRd^P7b)X5m(P*4cd:VlZlQ_\eps%hhs7#g2s+JG^<e?V,8lDsZ![U3h'e4>UN=46l
%ru\0ZTcE%uI\3R/jgUN`PZoR.4#nc38q=c;L=bHU.75O\pihFa#/;+A:?=`*LNJ2iJoZTdJT@5tZbmk5+a`tjn.=RK,C5_F5:Pp!
%5cO8%i-0h%W>sjZ#f#om+TEO_S48&Jcg^tfb(Y:^o;/sE$U5I+^%r+#QRhip>PoEI."hH>pn#-nDc-IGq0d&IpoYG1HVp`$kNUIk
%lAO2?CVR,+T%igcdp-m"q"5H*dCY2M1@.D1?%^S]&^&4$go\3]%MXC'dm!TeZ`h)/3V]@65rnhsi2FFrTd>iJ-4$H&(-7[I3/1AA
%KQFX4\$C=SXHPuOTgLiebf>!h5IWJY,L#=91HX&jXu)&Vm[e.T?>.'!R]0.rB;$3rl/t;fn'#j/7L80LJE<.+^09bt%T&2I<6jU6
%gfA[4)W4YaIS+HRH9KhX4kEp%)L*Z4[(-qHTXEVdNutbs418)"[+k#[LIY+D(eS_[pHq1<F2&^M_&i_0M$i?`3'oVIDKW,;Y$Vtp
%-Rso+,Er]"E8.$".e#^4\7Dj;@>O+TB.BeebhgOjapH5<I]sfb/3i#BkKDt-Hg@B@?pG[[X\<gcPmXsu9C(.4gO5qHfm2sIok2#<
%9@S4>oG>f'ITue$q9$T^hFb!bV82Gmm1-TPB),F6ngQ*pi`=.(c;6.5[J1JmF(O?;Kt(36r5a*@?tXu*'%#ncN.3SN6I(:tlPqP(
%&21YD19Pb]h&86gE%4Ai0lef+<<q5fJPQT=9"&e9]SZCrnt]eY$2G17@c0cnH;NZn&\([B5mI/462Ao[,9]k"=4auL/"K>Bd]?Cs
%71*sL8I>bDQU$8:#Y#TAp3]MefaYYj</sq.XdqP=#,P4Ka,k-QKae`.!Ysqqa,b2TR*cN#_5d!IKQ<^e%.j\Ho`PeQ![^Vc":?d6
%nLuf0"eB&ZOkCj=Hi/[<Jed2O=`&oq%/L7#G(bl%JHWraLkC&-8Us`p=;H&p5/N.91qnKk5CR\S"qQ)#2[U77:je/9bldHW&"MGV
%qnoSUa2[6_4+7WGXi&_@>7>i>T\r9U3]o:d&hn0RH/_(s=.QRHE#\QZN?L<*?pmk26Qn2BQJ3B6>o^4-FpC,@DCE2M,RLfO_JW@e
%Q6[)!"79Z[f_#CQ!P/e'i'=n\_i%1@lp2F:4NooEe@EHHOk2NAfGNJ'1@7LYQj,%ECI!ll0B%i`V3C:1h<#g*4.U:Pds-oCm*_:*
%ZO5heQT8&V2Xt%(GpIIp@^Ci&Nm8Q71HJlbY.tC.H(o@JkJrr/3\SLCkVO)*c%Rk_GRcuu1^B$trf+pia'M5A`Z3%r,id-ZSS>0N
%S/mUZRE#"-,>07TUPg@dA7V6il:FIPd>/QG0*ukp5\ZIY;j=\N,agTSfJCUWRH/XXnj28jc:;U68=DghN?JB7"*G1MR3R^O9R=h>
%'DEW!?\'-!I25#PaIWDi`d:@):rut8`dDDJpQiBA:h/=GG#.@H'rV`=_Rmt\c>m))W,_h6Hq1^u$dFrYo=a$&himoLo_,$f]>?0l
%Ifsl^3kQ0?GJCiOapgml0$=+jj0s9"qnWs6Bd/W@kkYF%aJh8n/8(p)c-?%5W]'&LBr0SbgPNR5N?r<Jq261_d[.A&cu[Io&Xo9r
%RO/hK)-LCLPHoEVAJ%*=0e71l`%st)[jZ,WIO#+52Q@HISTHTQW.@?2,2NI,D!$d.L(?%a^l26Fa"85mbg=2g%2@&`IDgQ*9bb(#
%Ga<'em'MV$Z3]b2<*j%UKp9LX0PhHE4>\gU0QmFSIr6'#$_o,(W"NE%?ZNAr,\<snrj)lMAbs*9bU(Hq8]6;$Dl>Bf/,4=Qha+[f
%(+7+?7mBs\lf%5$Ka\_R::(TR8EB!?-$9hi)f8@F;e;HXXboQ0,)UIXQ$U*he9?3.GnD4/LuOEgk]B&%0CA]h"JO2CICBeYO7L2B
%,WZN-b9W%,&ksc#:A4IFLnaZR+gdXC>"P*;,(@CnAl6EuLe_Dk,p?3?92tOLLcA=5-pY$3^g\RGO<d"2W/q-Qf!G^(`fmoOCZ8bI
%ZU8Fu`g;BG*&/06]*JesQRF=/'*Bm10V$5.!U@X=/)T*'Fc4boD(&Z%Omn*E0h%#O4"+-4Q)mQr\i'.19l#3ZG[0]P'N4&XW/?kA
%mF`&HAft=&Zj62+:9p%@QmU?CH'D;<cY`s/1]9KiI]N"%^Vr'<D-V##2Xa284c24'7?D((Q3CB^M['huMhnH#`)WC;7h\if'K7b3
%L"$JZ1-DmF3*"@j"]H./<@Ak3W<f:MEHDJ]92Td[Lp"_DPH9%5gTY;]*?s^\!iYWN<I@m_k4t#(3/A3cC/>QJ1\3`kE(Om0JI:`Y
%]G?<Uh3seN@eA?`fMPVFKCBW;?<)4[o,ls]_V(]_;F/lrB/r\f@&uA\7CbI9Jor?&,-+jPRLW>M6=1,8Z4u#gfKW3RAm38F_=Af)
%gVhl@7VV9P18D,`4'WB8"YlMXATB_O0s[g[@I0R^0C/Hb`r`jgK";_.=64j1GLkHL^b,e2.bHq#Xp%t%\46G(k=/TSl;B%YrF=Z@
%,D"G4=LDct_VYjP0(%,=p?G3GFcp&`U?4)HdJJKan0WDl54K(E0VM_"StPkX<Gdhk>=%d&pfEPM6VflWDA8rd2Scd)gmB$B2taLl
%gB)4^KDu&8gAGODg5Kpa1s<:N.t/"/!DSCq8U77^CGkN4Q#3Ll>u^WO)MdU#X+b_.hU2Mnch]`M?Hjd[@?_coC`^Osl\1ikm-$IV
%50i78:*5ea\WE.C1iP+_4mhu09WBgtZq'I.MfYgN9-q\%,lop3*^Cc+JjaQ%LQRm,1qtft,6nk($@^D6epME*]XHdF7rti)`S#>P
%050K5^'?4+'DFlS7l%fFa<6YfYa`V2>,<YI;oe0#ISMGb5`KD3Yai\8HFr0lK-SBm/&mE-cL+a^^^1YlE0+Qbf?D5nQ#eGT4;\;d
%Lh(S6U(1=KG+L'oVfC>I@f9*\Idm%@-Q,]s2<G$!fSHjVM*!MW9=%ADYhD.us,XP0:A-odRnXg1]XhW2NO=s\rNKKLbK=Pgdq.?W
%>;</VSnY<W>\5KUf*SU>8?5*Z8>F__Cjd;#eNmt[=qS=]!j%$Z)K[86X]b=/<dD^/=3[.G4\[c#\N82$\M=X=E?@W!^)17nRgXE+
%k@6D*."LL6Bt4".Y?'I?d6>fAR(>PCjH_rkk&O,k[Q6`WkH8@hm\5i8-C9N%c'EM;.["<i8*u^$W`sMn7WHtI]+u[S,<BX46P%*p
%15Kr[,jYUnD(ieDS+6YR\YIYcJJtS/P9EqcEgk->jK$+"9[Xj7"aXGLMq=r9i%<#O13&L'S>;=1Qq)%n5ES\dEnDq"%;<^l5Vq=W
%9!Q4.Z=oD(-:aEDAKhkqN'*X#8't:3,$eQ#%bRZ#:AR68/Shn5CI$VVc3gsJFI=3UP5`ca6S<365WqF"*c)FF1gemUP=<f8m,SE=
%!aYc+UKkm;6[Cc)EDY&Z\\tg'dqhADU31o*a=qQW>n#oID+BM7QUMp'BJ]T$<_[,-iQm?m*6gOtJ-.@\,V6TY3n)14E&\o\R;jRG
%r[ffY%pLGd.2!%gJ]6#%cU8Gb&[]j,knY,+#:>OG#R`%i*Al:[BW\bZliKj-huM,(W2L_%cb4io&I8M9blq)HbIaVNOu;+b#ha?;
%>]jE?b+[s.QlQHJ\WSO)Vm59=r5d,Z-bAYkY]hC&-q3jA7^em3-*hoCT&dak(7_M@C]a`U;6<*"pGIs(3^i1@2PI6QkWo"-&fd-0
%7TtFrL2FC2+(G4:*67cB7(161r5A:HF1`l0?Ba.B_.\qRS`QpVC-5;RZms*rR3=P<;489!Z!&SWZB_\W6n)QSG,`P.dDc6kd+?t+
%]r)%pkcfX]bi"*kH6C!*W%e^9n=?e9K>A%d_%)3r=$['FEtrk;&*TZN'Qa=;='Wjk64O4X+Dq/aB<_i&EuF\*^470,PrS2^%;)P)
%1T3TG\=6oVK"Xfc#3p:o9HXF[4H%H[XF=pY;p1fET?6d3CE])DDccuUDJ35^le/jNeh_Q#/lR<>YKgkb-%+4dL)TNKgP^?*?Hh`s
%ba*NKqrU#If74dSRaoSA]`cd1N/!`^")2R@Ds$Z!ft<`pNunfA`WVo:cJpX;'c+'A_\6PbD?D[r+Z%mK/$kQH9S"tB\N=D@;!YL4
%ga-'3$^[54eYgZN9k(f4fa>%I74bi>MtN[dH3Kd?5UHmr!B\PY@2#Po]T.3#_m2Oeb,\!G;lN\E;]/^(ThpiP8@'L2&Q=`/Rqtse
%e3D](iVB4\:]4ZH/-C[/#.uE0\#*jd.0OGU_u[cE3MV,0%ZTCK<IAI9+DX6JPY\Gj=o5ucf\7cm<\S4<P%u]Gk*UcmHk>-s%knZJ
%6dGV9MiZgLE0eek_gl>no+H46R@ENq3TfLlT``k%J3EKDV-1sL/dWtgR7^@XUQC1'Q@qMh2Fl>Q1Kbe0A<0"16if:XD&*.hIU^he
%Mm_S12NUOS&[[apjj>5E?.e-9-U3kZQU:AUSBNKBHueM;RsfA&FOl$ek$f,G.X2XHRTe?G)g9q2]Ukt?G\(r)],f(X-h2$n9c*9H
%VbO7C7=(:nP$VaL^kI1d+LSLF;aNJs!dHqf9p,TDE,g!)?fI,MEN4_V'U"FhgO%$<'5bA`9$qGoU7uh\'?SqkF&d#A60td<La.m1
%:RS;l719AlCf=2AGdcuL>HcLmPGJ2aYRRuVZW>EZEf9D>0AJs:0RL-jWl>C<XGj*I@"ks#W"GrXm8TAo,_[QO"5B'"]cFJV_nA=9
%/SOHb;9htos0HtMRs=t.H2`pjnP4m)@jgogLJI!0V>UpJ`a["!h#YhG[iHjbAefb>*a?(G(r+I(&:V\-;pBfM5JnT-Cf`aBMEFF[
%7AG"GhRb%V-!k(pHh+J>"t)Ho%C$j:WY2<GA[S0hVs@m_V@0R9R,o\%P<(8G7:!$N37\)3Jr?^]ZZm7G>%!Y?>6Bkp=rhBD8u2:/
%ILNu[o.8%h^.XnJbfMU.A`hICD`$XfA=;gVjuQ_1PBgc[4C??Rf#Xcb523''*hp(BBHJRG(S'tY.,s=6&m;tG(^LJCB9MNj'!fB[
%GSU$9Rm(Plp>m'=/el0CTcX\-_(_/m1jh+t>GZ-7NQaEc)bi$).Ufq0Xmpdnh.MnWc[la,Bm\DU'sY$n,^3Y_:nq(ugiU]F8E"3F
%`D6jJmRWYRUY+U/L-(KF%LU9b/A:_A'Xs=`6t2YH[C,(Mn'DOM1MB=!Lf\&"DNa4<9K1g:)5?<l!c\XE\q?9^-uDiHWg$L<B,&k:
%(<m#*;:6I2D@=t(6Tp0"/V[NpKYg'l"!p0EfIPN7=N2"l$\`_5`do7NAYb)'a;Ej!.`J@q2bS*;OZAe5ZA*ocKbFaoq;Y=_\&P]m
%nB#.PE(Fpt_el6[/3>u_mXr8[F?^!OF>BH=`P9r>kLTT8pLlcRc\l(Sg5QD@XS_Kq_i5(`-tVX7T7--AjnIL3H=)Gc56R;O"^h;o
%CsY9:nLPJlBGXJ"M(GC-%k\/pZO0<g6^$*:U)pfe#h2N\9@\Pa$(!c\-VccPZi?h_mtAS+&*,`2R8;,U,Rao[hr6Z<U^'r+3crd`
%O2P)FUdnl6U(biZqbE=M:+Ge4:0d-6*Ye4>0V:0s)G*TIj@^r?O.1*)k3,=pnn6t\(YSos8Lh=SMB7nm/a@7'ZF_sa]QE_Y*5qQg
%]+_nthkD-M((`#ffFtrSo5%4^h%*/@4gBmrVO>-j0SOn7nBNiWZkp"&C?2&o)H)/%[0'2W7Or$f'Iq\qfdaJ<fPSMoQXsYM,Wk#]
%SoV:tI8P*P:EG4F@8*$E"@]8W5Cim`e_`B7$>m&*0%2;-[Yh)eU8+k8>XsH_<'NaYYi@(ECgKVu44]O?+jC6`Qr*?>NjV8*.BQWK
%QU#+g94qI%cMWd0RV;.B[\EV#;6lee=&]b"jDn8J\cR_bRrkHACTK-m?f]+pf2iZQG[@L-R$6MKkgO:9f@sDKB/T>8`6o+=!)L-(
%`'4j31*#h0.V2KBWf1mjG\VT_mjNsH(A#e2J69;MjX'NZ%:<EX?f#eabI\1A(MHP.d>JVk4)eJ*FJuW7FreQuE&LCVU0&;Hi`@PZ
%[#8DLijheda#[%unWq%3pQ2I4VJD$#>pEOb@:"s04*%Wk)<Wm>@YNt`G`+H=_U<+RXf<q6Y=f;UY]Ut`ZGgbh(1T[OSb$;'k1dF_
%^kmM?JegtF&/Me0Fh%n>P.(U0gnC$0IP!+.Rdu_pZ^C?LTY?Z_(#Imk]bcG)ZPlr#n'VO^]ZgG7OBE.m6d1d`1>L@oY4fSJm%f^X
%n8/%9_/#`-D(/=-h-VdbD7YR3PHX0$eW([OHj61F*GuQ,SbJ8slm2$?87\_!F'?),GCS<.4Z!:\RtJ(Rc_C'4[r7nRH25_0mtGk9
%8XB+LX,KN3jR37==U6R!+;!T>j3&(`UDX_%TP#.\#@#Ine73HXBV@S#"\'ITI*3YH+H2atLs&%Ic!@[kb6kLJ@"lDEO>11J*XH$o
%DFG,B3e`Zh>;6[;&-XHjc&9MK;+s;6j45unTE''8\_3+LP=`8MYm/,a&7_s/R:0)<XrL"q6(nDb&JdP[M&Aer-uMUi?m25b[bhBn
%`L0aZ,^OeL[5$Qr$J$b#RjhK`UY.>!be,\M?,jK3]pm8#oKUi=n&=oS)e"KEDU+\)SA;K.:7QQ>m_2Ba#'1B70KIgH`3D7"e@Tua
%%H2mrW]pOPQ_lsnB<@oLZ080!.s+PsXWikb8O)56f"qK%2A)1sE2F['6f%*j*JV6@\TcTF7kY5`X(&*!o5,lu5dDDdGff*j1RTRe
%84Cpk!u/`%O+K%?BnRgBlO#cWLEbSQ-mCbA8%9Bs<_q5okVgC+'M&m)=WLhA&eo@G3!hHQ!uK[Hd%,G-'d&E=Sr?i$X/"F"K:1.%
%]&Z>Z')YeG\XL"-5u$$(i5F;,)nChcdI7mh]L<#W&/!U2KffL_>2DqV3!h['SL?k"<Tc\IKcNjLgdR6Vi0-0?1)Z%f^@>^b13+s7
%1/@>NFdSucUeq_o`:%(R<Pp'm/$=a2of>K]E)HbU;/[TW>cY$6i[VKOba"#L7r`+G",:)7k]/t5YS/DF8Y!T];"8']A-_+VK_d0Y
%OVk_krf6(+:m5:]:s`UQ$TA$U"D5uT0UqX]_H?!q%<(!`1"V&E>U*T(,a/qh-FlLX84Y'.IO.Y.QO1c]r+M3/jHDqd#IjqCFs!%_
%A-3WMJHa-2[K$]&JT20MSAQI>.-)?Z3JBmsToi9(,d_t++#kp<UTn+MMUWD6TQ#L?TrJ/Fcn`@JGekelf?i#45A\jU`uk#]$+<'N
%q9K_qJ+dk%<mUpuY\Mol7PM[6Z$?LG:iO9"@=T$t(?1)_2VB"TlrZX;)iFW'V;`$IIMnk;KkC]&Ro;>k6)Z$7AJ13^>uDfEi>1i8
%;EBbAQ,?jN&CjpKj:OYWL:]:Z7a6HBncYOR8AZhn+^#pS28f2ClCh>kTg>#FO@h>c3Lfcm^c+r7B&oH*HtqjI5"hb:2>4H72o2YA
%;M_B)1dIB`T4\8084mX>Y#DF%W\U.MC_Jr-=gbIpeuMGcdRcmX3:f-GH6ju6;FfD=_`n(3*uU3P;3sW'eY9o9o*Hnq%e`dU,f"6@
%A:8ts;CH>-dnkB]2a2]EK9tnE^l]T)(A8!?+)KER%&-i3R':YV(O5SXs6'V.4`3-u[9U'J9Ajd%@KCc>e6<,(0K<Jo&9dFl4KW!S
%-Nj4(CdjMDBEJRPgehtX.ip\0]d^)%q)JUTh"bE@a6Yd6:^K3pYJuWOmF<.HT)58DYG]BoB#+""9CVY+k+j=eRcXE=&q604d$)9*
%DC*m[=\gFo3g4s0_9`GA8WRI7<tCg.mA:;;>+3H.:Z=LbbO!Vhn_VM[EA'Dg8ce7>`QXL`A:`][/O4;-Vjpf,,TMD8DM&bWjnA[/
%93.Zi_Hqd3GNk`+rd&=LB"KG)NBtu0!0,s_%Hd4aXFE3N3@l'2B-7Ma_CWX9GCb$oZ\ej@;,Y$I-uk-9C)NHnQ30!@BP<qVVScR%
%-YKF#<<T\C:cR$FP9rke2&rI1<P"Kk(6Xo4_1skD0SD&TKS,qf:'+#F_L=N<D<hDf[=ICPn_8h4AJs6,B!k%bm;lu_Oc6+$VS-:W
%r.5iRYN+d]$4$C$jY'Wo?UY7dI<dS4YroP9W>0W)rom5W-//.UTDH<1NN42PFg1+::u6X\kpkn7$7RQbP#l5.Ku]`N!m_RJ[gpd>
%/,'D='VtR[.%pmYVQe[5H(D,T8h9X)bCA[nVqHMR:jV<S"Yq#Abpt.dIQ#!P6HqB@F37:,^87WhF3*J`Y.5@KGs1Y!Ji1Z*39ND2
%_S;FgHW/#B:;EHO]qIp)$h>@THZKJM51k2boc<IRI:%b[mhbLr>'Su'PVni@9`E6h=T3V]S7_Ydc4$ZdCuHhI!lHB8"\Job_&&QJ
%SRuWR\OeUJ6pV-=AfArFp3<+#G+8a&mhjoAV6=BmHrBk^CJP323YiV&\,#Le""`UHbDW3>Pa%2k-[ebS3%&fARq.P;FH2[6nTH&J
%kkp/+,.Z`)n>PHQ.",#0)1/'s2UXnJ;*G'ZZm+j&X$3OgJS4p/XDnRERf7U2\&<BRmKit1K.@DgP`!R#abUrN*Q2f_&$(J"P(toA
%%i+_*l9dWbil6Aa"0T'*6.+!pG$b3FXLusJ>']/jjKlrd\HqY+fIY]b2XH4qN^$n#+>no,[Ut%g8s?+f19lnFO%DK-IQE1:rC:M+
%2g@r&`]imE,U(.5=6pu0K;XscXi$cQVpcZA]tKm6!0J]KW;^d_j<n[D`o4,Cdi0`^,r\WJn1<a[*.+8gVNjhR`*9DDo&2<1V(nQ:
%gK=6p8U:rO\_cA,Up/%,Y\iF64/qslp>5m`Ygfl2Pcq7*(5*5^O8]E,)HBn)GZm9,Yqdt8>bANp)e1%"an"/>-!R!2=P6@&kEi2H
%i/7PKRFGlSR=-n9OB9`'M/aYZR$i]WQ4Af6?b/kr7;QRB93Xp,[83s<B"*Xl@Y?KkAE"_NM'Pn(NR'\"C`X:I:q"Gme."5I+6=,o
%^"94[3TAmkEV:3,4C4B7?EpG>H7q#;7u?QiluENlj4nYW:oLW=$E:XRb,o9F"6LM&-!i%<-]mdH`ieR>0\=W,Jo"bC=gR"M.3=D]
%N:kJ&`C3pu4Z3KWfDsoZSro'ae!=J=/WZ?-n3E*uk>c+[bpumJ%dk`!fL65(LsLW(AM65;,'5(GDAf;"=7J>P,+B9U7;jP]Ob/GP
%lilDlctce`lN6Ka0H\HH]*.s]SL/f`h!&brc73Q$PRh?bG(V<I7B=62Qe+pdE"]2Ge.i.&Z]gM,aI3sr'M>kNTlpU<Zk/do@ad;1
%CRfu2M+\?pQ9N-p\Ia_&AZskLptB9Sg'U.?TAK&;R,B7Z$rLYn3CC`q)kQ[<'Q1@R_j@=jMhj565-:M9XRh@,2H-1#`m9(i0ACiG
%06ZHi.s:r=F`8!Gs5BF5P'TYl[59C;b*7]1)brA,qA#r+?U5;9Gkp_Y.FBn.OU=FMS0V)8r<ABtcGa`6SA\h`KoqgJ'L7OkD5;5-
%TVJ<X:DUgG_2MeOFA9:IP]pfhqm1uGdQ:*O[1pPkO_d"2]9R,B!*s.PJJu^s<(M(n(rQ8&"BsLrk#AK&/CYRi[6/,1NP4$iZ:6U;
%]9C8$ST/l@OG$;p\c%`emQ8b`C75AoqD5-6X>0aI0B@PL(Y<>p=1!X*GF^lGelK&bZJ.VX8M*gi5(4dPnr'?.@V`"c.5`IReT44Q
%kaZ3&4&0cnk]iV7,!/PEE\9kgLU+$<:WZBVT9O5k[>RRFl/</)8frndP/WBQi8lNg,"FJ[,c9NCa"Oe[XV@UF+'</I_C!CVDC98M
%BhGqeNTXkMf<X>:!c^+OaeeVp$FE?p-iPt_P]A'Mm-90=1"YrKWt$q@U%jn8<_E20@DP5d<-u_KWc/DA.Q*Pq:%gA]YQQO&TbJ;9
%-q.jt<pP3Y^$i&b4-R'[fh60&5>I(4pK3R;)l_g9M'hEq0i+p()KEa^G#%fJ,jCd>c#oV@TT2!n+NM3i=6)&u^<(AEb='$BdSi.7
%@88U=%]d1fcgq!@ob\rGcgOpa-I8"EIjCJ!H)-nY3%e!`hD?\H=k;Yi<7(8nH!6?P;BdT3^E&0f(2;XFH=U/'nk,]L*M]CZLX$MP
%?"bg]A/u0<e[uNu-$W(ilN^""0^ngM0t"*H)ibXn<b#@8aCHeVg((9bhVr/Ied6e.XNQaId.ZhFMHm=(AX^L']"EAYC-:8,]BY#L
%k$NF'/sB-bA@$Csb*dG`a_OYo=6R^M4ToIo6)dAfCum6skh@O4h]i"E[!1Z1Za=j<op(QuSi!i<1\ed+pR2db-RIYuVXe#&rU4W%
%qc`$0^)@fml.KOekEtmM83U3dTTe;0Aj`jN#bi#!A5]!n`n=5n/^af:lGP,[OJWFI$-#H_#!n6XS)ckA<6i";6>153>T5)H$1dkq
%A3rBBKSBh-rdr1OY+u]a9-Vl/;?i*p5gm&:j.,u3Zp77.YM3@3o\)F@`oaDJdrub*bGNc(IW1i?Cd3d/?7N3K;DG+is%n:DN[)'&
%@Vg/ua%([e=%-F;EEL9a?X0@o)\8.9oQDu7C4tV&ae,C)PGlJ]6(+D6.HYkMollNcPXZYQ*qoT?PGTmIU)3!WIn.?,?OTm%%6qU%
%qR9#m7m?a,347nuJ,@@f*g!JIps@qcHV"4(B`QI@M9:e'!7r&mAsj(3D%`)(<XAd[$U,asA<DmgaZGeN@kT^4EJg/OLtc[`)g6JC
%*[fV%!0e[P[I/7jqgGO#f(-2f5B8>_>JF"pe^aLB<hp^IoT7"DR_N8anY!nIS1)k+E"^BO#O<T(i-C3V$$R[:KJe3/X*Q$4e2=@*
%aibF$#G<A':2aI`YeXY+03@b8Fth[eBA3Q\bG9c#(!e<6\#]:-_SM@PE4pS0*2[^%NTX%Lc`qNV[5=JZ`cp#$En,'Ym%(+<@7BnG
%ot'#'f5('E8qjg$G='XAX7XOAV#u#bPgo;beTq1Pr2b$$XiP(<4;CY'lOhnOR%6_j[,O4PaEp\Wk$5q8@8D:[.5grI2`b95!l"3Y
%i:22+oL5%Ch;OdKP6:&BebC50.WbBjV_X%HWQ]S'4n&;FIEl/NGRLM)7lj@I'D0KTcQkN3"?]0DU!K]`/pJSPUq0+GH$)aq^Lm%b
%atA%':f_/u8MWYRLu\X(G#a2WN.B$@r3D%%Y8c^S[[ET9inIH6:.7-_.85;V;Z_hV`n;ebo3.)WA8\7e`0p:*-Is:+grm[9Q^Y1)
%JLBq&U7ESJ'0EHo@RRhoLoK,8L74'XF.MU+(H\s";k;.#9Jt5R3-SG:JY'AE1P%<Fc?#[Q8PS?C9t-$`Z/5EU!('</cS\TCXTX4N
%Ct7X(Wlt#\f4GBqg0RH4fS]TLB?'FgnHAUtdqmD_hqOQ>/$m1"&mlO2+sVH!<"F>N>&=Gh$+=A4-2;q[C?r/t>rX2ag_im,9c-Yd
%'=ak61s3dn$J[MkR'&lhTs1(6XjL8BMZ:bi1p.HP9l:"QgEH9]VbnJp]$ChG95X`>]1PY9=8lnK".:8/Kn=6PiKN#[<9Nm!R^c'$
%k3GrD:L_NgC#$^%>FpSLfop9m$2,he&$aeGc95IX[6!at43?<=L<K.Np6*`ZAPU&4,t]S6Q[+-<,1&HfQ$n`M9_jW`4MCC&V]*H4
%@QI1&[0)@'!o.cQ7LDb+Mj0Un=GRJt6HiYY>YU!!+9@piD0H3Ac(T>'Q+2fK6bEefea0BT#GuA@&a@U%D',C]ND>GH,C/%aVf'E-
%nlM!`/^GbkE:PnYWH^r0[VI8(q>14D^Q.^5Z2N1WrJ6,bP%s]KNGNBP:<=:lfn*np4&R\[9#="P,(7><U-!J!oO94O?,r767WSE:
%-aWT*4N48$;L:'rUakhD2I#<$q[ZP)j-NbH,9e])I\(CV4j0($S!D9]A"-J[gP+(d&5tl:R.DloETq185/r0<-EKstVPf^MEM.r#
%.8.u^7dVF3eFo-!LCZs@RR!R%U0g<Nj;`of_,Q%uFV0afm=K-d'Hn"j$W[Fp."P[R^CcP?-D_5@%@A,.45XTV8Jn&9c#FpH)ngM%
%.@9Vl+hULg.<hW@EGtRNE:JjAEL!Y1I1L>?SF*0UPVp96U\kUbgLhLF.nA?;W-<C^[iV0COjt@MXiK"-/@D]<@qEg(pb6VtA")(R
%%kC9d/nfo6Y`#tp[a^J`N7"DNW.>7Q<7'=hKB]Y?RUo1aOXf;Kedd-C/.`th%h_8f!i\#@MQoEAEYRh*Vaii2X;`Kg0u1WP,DmHY
%G`dQ\X4)'aMRaJW`s.q6D+$g$b5#?1LjQPe*`cTL/o8e'NtL!]`.sBX/\f2%1Tm,VOr4La-hA]1f:NMQPM6m3j7D?,T)I49s6YiM
%8WR!'K;XT/X",3;hA4F!5;g1(Vk:*B_g4IMZ2+>B.TNS)qYqP>Z-:a=]C9Nqmk43jbTefGdV%nBE\p6goK(Kh0DS@#G#&QI"a$.i
%HtfS^<>0+P)C8D.S9'E<pP@LaEGISPbq*Ra?7jH.R#7JIF:oRb@dk*!YGcn=gmpuqe]ct#&]0:"+)?gT=a.'S3.`\/<7Qpu?)"4i
%NX8scT,?\^&?PXs"qH+,5Q99#Is`[T?U(KZn9UV*^<t0a*;+jk95Y(pA7HUo1k19mq9UZ8RNFr(L+%^H_*n^t_#rb9c$ZG"V)?#0
%&<hPWPFe69Z61u]piA=eF>Lji)Z/%BVtm:AY^7-:a+_dMQCh?%<3'/`9S4J>7E\i9H)oIpW9-E()/:Ma0#g79+DP4u1)!$7B?SOJ
%AjpbtBV%]<eYm*"@;8boEji>*YSU%0)b1CYDQ+4*=j^qBQfCh!7p8-+JN_P>TlC@,Q8q-N:1dk2d\bH,Sn].AjZ2I;Ni\i4Uuhr_
%ah\@4IV*hG.)9Ngr0e;9cUmLU.6eh'4%Fk0WV!/`8M6p)GNT6N!STi!j4Lf`H7rI;<i7(=\YH&ldgNOMq/EfS520WagUbIrIl4,U
%e4>A0/]2\.r?Y7<g3"qKmjRdfUGBW":\j=lH:g^_B/@9K!sL]cKS,.j2Nfs:-R1O]8N<eR?(VAN1I93[!:*=Ik!9t/9+uJI,<#'Z
%F_Qk'B@!H%-9BbXd36cUB?l6-#qI%g\8jIgN=L7]1UlS@-J+UWToD.S3F7mgf9Alt5-@a(6P$\U'Vp07cB1/DeA2uaCLu8_/Z*W2
%7-.cEIal,7NJhV@\$rgc%*B@!Z:;B@YqV&ug!?b1c*&QV2HOI@U*XX32R10uff$%rfX)4b#Kj+YPun@)ocT[qU%0[9RN<TV5$VDO
%?8lmcCsL+l,'UH%S^Q[fgp9dr^fn0<>ILM:cC4lM"k7d"Pb$=&4,V<AdCm6EHejpiEoa;a)npbHNa-5">ir3-6<Pi'lo!$ikp/d\
%1@(6<hAUYdgQ_5JS4"a51j&9/9Y*cHM%MI(a39RB=`k5Dr).=aJRl\gnmeqm(2<!f69@4\neA6:B.k#\X&62/WQ:6TE.?`(ZddX]
%VREq6%Khkf%X*l!kXQ$JB\&/B1HLXuXKM1=E=S?o`4?Lq677Zi7P/<+3K=jg50#)eZ"=E\9s#H6\e;XNf<Q4j44`Vd6=%7rTGQ:%
%5arSn;oO3=]oV\l9qoZ<E\bTn)<FOq/P#XUOMU9V$D(+&K,bIt4>;WZ.8O3P8H`<Xfk;D&ZAhO<??U;MS!]GJX_pV:Sem^,CH0or
%#M0pKFhR\>VdA)Off,mA,SAM58=3fBe^*A>N9d?eGHtOnpM;L"egGX!GEUHFhdb5QbATLZU<=sXo>3,+1ooiG*$uQ0X^p0h[OhHl
%A]Co2mU%]>.Pa8:oa6ZHmP?UGMtc7%/,7s::A)ti$N]9[R\jZ7Ku@1hO]6D'`>iT3R/j`0AKe>SVQX0NKu,2P3iUK;[FSMUD`tAf
%._r,n5h`h5W->(YP=0:2QSh@p-3,[hE!D2Ei:DnsS7m;51QBp0`1EVO]'*#:1e*uuT4o:%G8?&YkS,%V,*]k<8OX%oT9>p\QuOm=
%RSqc#U9)X&&L]B#EMBZQFf&k4OZF84poS-e2.*2NPF'gi]0(MP9ps?=MjW+kagMm+Jku]1I_AjNKNgj\C@'*5:2uac;<2L0_7K,^
%CNRbhINM6e?cL.SGeh_c!nuNO-gT%D\;Z[ndB_AfqMBb<C)k@nG%5-,:0]sVnuhR:pWg>a$R+"Z::a]$E@DbReip<LL)BT>*Aop4
%q5&l6F."n$AB>aXZK>t+j!>IDb%<@Z[;%bXbEoW^P]b^@a.g3_Am$2+k$`lpM_&"hr)f.oH^,2$+S"3f=LY/YNp3j9Q?8SsX+?5/
%g@CM6=<%/'r;);Kq22*GMPp9-Vtl4Jq22*GMPp9-Vtl4Jq22*GMPp9-Vtl4Jq22*G@]ut[R]gt)YBZM^YOO-7jt!%c/"Yl54O%RF
%:NInX#&6i\JLgn%Z\PP`j(c$DROl15fs\>rXsq5"`n2]^7WC>DZk5s#BhN2X)jhR5h=,6^K"Fn@9pC3PL;./F$FF?+H996eF`D#,
%'Kf-P8[CN:<--c;RDEoI-m<]pP="r$:sEj,Ocr''geU)jZNt_E%iqrcLL;K1/)RT'D3#XYM[]>G%hQ$/pD:10YuQ'o6&*D^AV"Oo
%%C,c_!G$83qr;5aK85\!ioN`Q_[>TD*f+564=r=(chZtkj3*`0K[knt[^OcD!aW':fCk5S@.k/eG<X$pn3@a#Zi`n6q`/0XBiU1_
%ZHgp8pa8Dbgjf%:[BajKPY&i<Q3SV>apO7g:;Q^LcDU]M`]IKOX1"b9ei$8al>!"//T%,t8iKs@eW)SiU5QeM%%"j]nVuPKjs2o6
%Yo:oc`gdj2HW`p^cA\8UA#mdJU=l,4j2V1]a<5Y>Fu\R(QG;3of.2?!re9nKZ].;2BiDJ2a\9m%8h7\M9lAY[oF@jtmhY&5C>&QM
%Z;[ep9_Xdt_XuZ8l4\Z`m7/DuY+b^o+n53sc>I36DofKV0oO5m't5&spM3NN2670oqF7%!CR9jm]pa+$NuaJC?&6<S7IN2VL.=/r
%m5`!E-O4Qc&Dm;;C0[;b]^[2jW5CbTii4O@ThE>`2?2,HMK(,/)?'PT=I#pF!V4t3Qe>iP`_Tno_ZKATDqnXNZWKGeP\hR:cFr>S
%\)lXcNF-K!>2tsQnIoo]pEGM]O56Z;]bU"^&m`/e(U"CiL+t%BI6ZubDJjYe)c(]g73%4+s7)lWfaXDjn&m9NMr_W7M-N?=S5Qok
%.Ga0ObZ4$0dF*DK5J9@Fb0-=DgT9jLn@EtgIlHOLd*^A7)%PsG*:D#MY)$6(!DiK@O$(<gpB1`E"@(jM#KHG.1E<9.n2pI(c!)t$
%-&Q?HHA9=(_VRh!PK\7F)%QQ,[k+ZEA6"C'n&u9V5/6(Udi2>#Y'fTh.p''lhk).m6-/\?'q'LSRnh&s_[\_U&"gR)W:+-D@5SI`
%\uq33=S75GT+UBFAfXnce@7uNcfk&,b0C@tqh/Ec+PEI(qXQ_(p*HkiM/HZEguiaNel_YmidH`4T@*+C'`<W=V_]C7lb"ZN<F\+!
%([0BYH@CdIT1drkBeFRHbS#!3/ELWGgHnIlQUq'jV*CUmVA$6&0#r_`W,Wh,n<mj-2THVZ/b#B6?A#;+;Ja5kd1$fE&/RltLIF1-
%MfO=hRT[eqFRXF[_tncMSYudlEHtH,>2Ws-i1G_fD@$>H?uj-T"S)FH`()g?b$e<H$9-HNR%e0Q`&<3V)+S,Sc5BuT\1)d.X]Np!
%OT^"rb8ih/+t@).->#XnQd97//)?-EfIO75MVW<V4M^f,B6-gZnc>l9?+/>ML!TYoOsMdZgL$a=UrJshCn<O,HH&ln5S(taHqa"&
%+MkU[n6d+-[(tCq&W_q9anrMfjNV>g,7/jl@Ur,`s1?IIH1@4eM*ahWY2`YCZ%L'Ik]@bkE?,MfoQ<nCr4,htBg&\*;QBNRhG\)e
%>qGc@2cp`=FQqg$J73F$,u*4dM&#O95J9ikr?=Uml=Z/OFC/QXUAKl]nbFt2"=j2J&]*4Lrd[fspE[HWb&;e+md8?X^O#e9e_AtG
%GqpbcSj.Mukbip'cPh:VI/i$3s75dE+$R>bs7YuXn,N%AT5N_n)ukQ2mcK&Ki7EI)r9-4ij5l)t?iM#DqVUf1?\.pKRrn7cRt(2\
%GOl8kj4D%jr9-_i_51LWoWQtaJ5ODPjr';/I[[hU=6]@RB8a@ZYIAc:TAc/:49,aNYLEQ(#/&(IGJpYb4h+0AW<VX,O69hlHBV:B
%q=Sf:S*oDdO0rO.=odHO)r(;>>Z7".eR3RW6'j'm9iVVMmfp2oV^N&"pZ`3"6HP]XYL98)BA6V1]jQHdT!90tBe1k"%0o$[/Kdl-
%TB$nrl?RQLrgd9f"S4X\c)l>?g0m[`%sEpborN'pd5SNE0TEBqhV(1_R<O"9CNQOgF6c-LFEi(gB;B?*4?m\.Y@Fc`V4LD:&,PG>
%r@(^4[^U-M@nbXFFHnKOCi2E"E9k:7%eKOha*4#s>+bBZnWgiQaniA$?O"2STC/Ei\5V+im1-^[ofkYc\+;Y/WX%`>\NB'.c;_7F
%>bd@/p`4B2m_;S\3T]LPgj]@ml?hF<2piH/Rd.FQ+8,P,5AJT2kppf?p,'hL[oEn6Z:Ug_C\1Jbgc@[%5,IXFB@mmp[ML=fZ:Ug_
%C\5kM"5@iFCi:>dAlrNs>7^T$(8II:oZaK?nF]nl[gl2^CO(9m6!rJXe8pV`\M^V%(3_eZG0O373df;@[i"dCUMk#ZE_c:EF)):j
%([U,8'f5's>E!m2dFYl$m:cS)CjYLE[-,$fn;4kaoV8!noZaN(;ZpoSDdAs/R:^g36!rJXe8pV`l#:%]WPn1+pST1F\fL*&?Zte7
%k8JbglbE8Gj%$is+VP"-V24pid5-.Nd1c>*b8^750Z+u1k;E+7in:b@Q:,Tiqm%EF9.Cg'pG=2C7ee-3GhS&_/+a=.^m9?s/.?Z+
%H:UH4QXs]T"'4oCCOg]t;c.hfFhHAE1I]"u$u"jiL`kR8:jBU(I4e&"*[ER)iJ-Irn>(<H`"[G?7*qaf@O1fJ+]Qdi/J[\"D<T?]
%9n\I)4l+E"?L1$jM55pJO1+;.(Q0jYT$h9/q$s&ZWiu_njVm1i-$+.c[NQpBQ,-P6[*eE/:]ncr=e?=Q3j>h#6$J$VkGJ_W,mnb7
%8e$#/*qscY0Q3`kBQ7_6WP3F<3P;/0OmMURr"NG>XP-)L+,;>b7s%<,\jgR9Ga`PoTDsVI5McQaj?A9k!9ICN^OM+`HD^QF"7t71
%Up`P;,L#]0b)\eV(GQZ=On@p5mB;N2_<ai*Dbra4"Be5t*c,>Z>U-qt=t.J^i5Ud76/XELlh$%J,plM?FfNmfXWW($bpVmqi'3h%
%fY+CiO%1;`*?.13*;%0"$0k)L`Wj#t/i)8[U,/^W7ZWb)[c!nl?2'J<`TJ?q!P1I)T:aNIr>dI%Ut:t<4X7##HJmKBTEe!")!DQK
%-8L'9h[q&;,K*EA'!Z^0Xhhu6I0Bs`69b?A<sM4MB[*Du!'S2kdLpAs9MW,4r<u"B6+\A.TOZo!m'<S8P>VGRd(?7%_8AN%ZdkO;
%k'BQhH`!"A-CTbCh8ha5'ndt;k#1J2KT6l.BEroLOp'30+=L[JRgS\VMYi^Kqn]l7['$&;kf>OCYUL9SJ>a"e$hdk<-&eQ21Gc3A
%A'onQKnoeeN-S+QC(;qkS7fF:UIPC>^)GUUW"Gj.O)[8j%II*,pcT-#0=&mYa>o?h\To<[^urj:J=`=]:gsX#98j0E7YTLQ_1e86
%?C<&.>bUmCCHBG7[jrcJntJDc@T$\-c>+Al5@I*nlc?0DO,U4NR>fO=faM'G2&,lBi2M_>_MXgh/\932r.?624;lda07j#J_;`u5
%_gK:c.eKAMl^Fj#gmWmeB@d_MAZ$bFFCNHZ0=[-#0kK&TJ=&K@FWeP=,BG'FJfFTbN]l/MeLEY=93h`C7'IdqYj>ig@)1a'%BqX@
%1;a"U]?T@ff:>p4;Kt!A/@([])%uCO%XUL0&(!+j7^0/5"4:T[&Oa:I6kEX;?(t@K28egDI@^"j/edi-bpj,p*n"O(ZAHQ%M\GBt
%;6NcJJYVL4SCh78NhLS+qPVYBlp[VV-q)ERKh,/8UhX,T:5hs>1Ic6u)kEFiQ!<GdQD[!*bC^98W^\B''-L=n8S7X!LfoQ<2hR=Z
%1HBP3B=5Pcn9SG1m)EQR;bNL+FOUVAFWh?CObl6jquL7>Yn"/Bf[rs1UD$")_*+5="aBg+Kao;A+*S,>LaH/B*ZEl3BXS:MAE\C4
%5pXA]\e(nJI?"'_8Yq./=EqZuMfF]-MchQ5N&tY!&b2id2J-QbqE)u:J"tJ[ef"n"aQ&nSk$NFl2oLf.?fa!a8g$"p(H69e'[W>%
%AP1R0<c&OYCI(upoEnN,biFL`QT-^:1JH$6T:%k&UgbNbXMjL2W[\9!*A'fU7tV-^C!mU.oUp#7%3Ym$qD)e.70PW=cI)\FFmL`\
%1k5S/7$sl=9O<EukW"M$j1`To[lBn<M(])3V=;J+Wh+E+X_G;u]hfG?GTX*Ck*/e<P!A1"1*56fYsm5]9UY*TX$CuBVMp./80S22
%XfDlrVa`#EAEsTd,30n12D5jof&2+HFIbZ+c3oam.;K%0ho#j(NBIqj/>>QMlTlDUf=#09C](?DUoMSAS8L?j0P+]'Wq)ft,-!+u
%31+`CWSt:E>#BI[2aN_q8utT`^K`NI2PTA:KIPC>4<^J>0r>9D.gQaW'?mIaEceFWl@q]=n\R./(*iWuf`+WuU!=<MP8[7A,N5AT
%T^mci*da4PLWq=)m.S=gpFVb_YTq<`_0=F;S?)s9Z1og&U.5eMZJ:d4BbOnXj9H8$A$'GN7,9I"7BK/kcl#t3eLj08]QH&_9d]JR
%C.P//!Sr;2-aub*M\=QfO!7K0hPC\#FleHVZg=5bZP*Ag1Y^pW`k(5;Nph[V.9jRAr\5FPUt6_>Lc4i`.WlOD`JX`5RHbO&Q`,Tl
%CmCnZb3*dkaK)0%@KAN%(h5Vh*g;N(rC"dtoqc@3]K=!:3,cY7d,A[@?VF.ca&H(H<(f+b-Goa2mPS1P<WdV4kUBbriF_hHs#dEk
%P%fi.%OACV)l7%@3BqaX8b!*]jOC^];&OWD[SipGdld*o"?a2VYM^=h=4O?A=1)5"0T!1<W<)4!I_gr3bSfW`gnd5,%q[#fE[Ke3
%nj7*t<F'gQ\+.FkrDPgPkkaH)'rM6-6NQ.CCY8:*C[4$Cdo_cK,R$U'\q'[f\>`$HIDYCqAlGfckP9JdBfRN61P7!n&D3WV`2Hu)
%D&P[I:%W1tZW;W7LWkD1luCD1<L$[*P@\3XWVAe!*..0K-W_B'9=:%=b/L!V4LK;;4JQR%n;d/.2p-9N0o\U1-L+QK/DDn'`Or?A
%*"8/l?$Z`31@(8GO#Jm*'HnWsd)AmP>gSj%a?[0A$@bQu;j&%aEJ[!b6!9eIKZP&,@$k#Okqk\MTCt3%1LQL0,5%#tpdXKOD,rL@
%flBF]V)Ai9MphL,r+Zh-!I=EMYYkEKM<SOhi#9Z\6U]io<J]=l_$6nK[7@?mWY>s[P<k#*"Z@hW/%3P3&VTMH`+fWtjaBGY8[f@@
%1NK`FJVb]/7b]M<[s,832uPN`A>l4J'&?s"I&t4P(0a-l&)NF+Ok!bK3q3Y0[N]G&pIN#,,&!OIA>!fUKq;ILA!PBe@:[W@<O?ST
%F3dN*;:]J1Y%$-J%8YC+<PB<XP--ZeS1m@4BZ2#Mi(/uO!gD]E;U2!k)XGnE\*4.PqbblJ[6^YOeqTq,<,gAA*:(auXOWp(D@deo
%&2=qiUu"]&$uBroK8X%E'.n<ggoL0TLp?'a`27=0k[Z(([T/GL;:5&',`A<Pb_IC*4#-u?60[6b>j+TX?Gf1:TKSu7E%&qLC],s*
%Du/Yie9/!A7BCEFbcYDUD4KC_Z/H7j,duj<'"lmliQ@0^^<k1"]nhT+e0W[],:NF_,^kl50FpR:[/H0O`>*(hDiO+o$W69M;0jIC
%^r?InEsp7?9l:/n\_la;n=Qg91=$1<E[DW^a!Ks`doH4\\Xd6lksua/[od9'4FJ*UT[$RD0bB9%Ls3_&,Yth,C\:u5`Pl=E3&I=e
%'f]9oTt9<qL4EI=oNL:S$jp&j?OC*Jg'6/*Fm+4T<%2e6aOY)/+#ok_kRU9tNS=$M4GeksMQtsfT1<s?QL5mU*"tV:,[t:"p%Nbe
%M;N-W6D99nKf]bRruu&b[BU""VLXYMm``9?n5r\6Xg:YdCbb,Z/Yp0,(TksYW8d#_ANZq`LX<KL/cmlIrV=T706fBYjOjcmP5-'q
%$7RiK?Yi<E?&,[oU/#c7arq+)KmcbY@;JW1d=leoEk+0X>f7PD;nHT)]K[\0(UHbtI)&g.\MuB=KAL%V*@2on7HVNr-^gKjr,ae$
%A'`=grlqgQEpbbY;+NUqbTgXM#bBO"cUlc<BE;YmFHF&!?=X4O2]8`lCL/?l$[OOEfIZlGiVK5&-"__T)F(Q@dTi1-K(<q[<iB2E
%m1;"q@NjRrL"=U;.oJ_N$O1SKaH%*^+LrSgVuB(i"\iS^Y4N6#V!mLh>8!kEdL*$2\/<qh)VO6aOXuM[]U39ag0#5=_A:A``776g
%f<DroN>8!jc4G>9]BJUY!.%FU3s.YoMD-ap-MkTf)ROe?2VDh['TFtnYlhk,VC);5m_%7FJnlRuR;3!96O&OGX\O[gTLGu[ZUTh<
%.1tQ:6l>[%,,$@<+RrLl?V5.-j$&QAU1MopbZ4l;L1guR4[CSJg]rOeI3oS*(KI>-[NZ_6'Utm/?o.LVjQn#gida3Kf"b\"_2bcY
%Uter#(S:pNZ.r/^UGbg>78tnP3LA-ne@?Y^Nt`60HYuYXf7(Y\gZ?Qtbr(4KWhMs0!^AB$[Qh#ZGr7"MJ]G$knj;`?iYP'+JR0<r
%cU6fsnHOmB<")(Bj(=&]'coLCY3DdZWL57C'EO$3WT0Jf`*2(k=5SOQDG]PtHEgHh=H6l8.NcPX?)lfuZL$.A6W#[TZ5SlNXfI?g
%DN"XAJsbI?8fkjc10K0!1G?baUfnm!+,<C@a!qA.$SDIu(cJ8l;=0u@9S)_1M.OO?b&S^gX/Nj3GD]Y=q+*o;^b,ra]]A*;l.5!t
%'=\.4i0<G<iZE$HML%!$fgWiAonkU71^,0N2QL<]U3pHK$S6)(\1!:,/D]kqT]JMOQ]2hq&4--I7!Q*l$<UG#\WE9pgQ8L?>,9qW
%h"erB_mVb)MZjF]WQGSkU,c130CQ:rJbAcbmBjh?>suB%\hD:7g7ioa>4hA"^@eJ[SKJ9iW4OD9g9kf/0hKUrA186qSLspuH&5?7
%F:jX"Em-@[<[$;]W\+=>pYBU\[bg(OjC#;/7pCXKIi'A[bF=o?=&k_+;9uQ5$(k`YNZ>?6k]+0A#HnpWjB#M8WeP.OJ5)V;Pb71,
%YhKgQo?b=i>FQ/G-*\iFAShq,@7qtZ7#?@>Nc%WY3#]VF7*$gV1/XV$U`4I<I42TEM[3J_7tE5<&!c)o8/-^fEkfbA*jK)`<a45>
%LLDr<RC.@d*(*Krb/h^[YkpIA!g0B@EH%jr'ij*q`L#O.>HiaJ]?q-DiL-pa-uC@]Bt"IpV>Ht^"[`NQh:T<0//gW?\G6uH:W1([
%H5$lHka=K:II-gO[B&__>Ep53%CcF&SQH]?H0);\GU;>jRBh.Ze=2!R=btNZ.sA3V?``rm27$/fXX;`K.k&;FO2EfHEcEnAKQ$/1
%+t]9gRp2)I4uWS<"V)<2SR+1E`3qenLYd7nU=,6`FDo+JCY^U4@pCZt3#,m[+p^TfmCK.*0h/,!HSK?bN-V.0(c@E;'F/9JXbk;]
%.SiD2GVB#jeY:85eJ=Gc9Y_;1)qt".;pK1],Isq4P!tD]->j4.*a,sb0tLsqS^?XHni!6:RVp9MX0oLF5';3[UBm;ZZWH!YXG&_`
%,b=>ZAT<5]guGKj'hdit-mY1T>)7Z-or[@-Eu5[eQYqQf:Lm'Q*HAu)LY#9g'ln,H.80^%k#;?Ri^HR(b0muJC])M/AYK/Sc7`9F
%3=jfSMT::\FLj*;3+ZdB=gq<H$XF1(W(BpA3irkO-#Pa`$NWYY8k6@KbcYCU.@ri%=qTBnFK&^r@61L=@9XBG[WfX:pt12$(6BH9
%dmZU?);M"?DfD7F)bbd)2H*Hk$\!3/cV6M\PLL[@=3[X&FZcjl4[s2clLCWuoDKcdr;-FJ[1L%dBt(%+[l4[(&2P=NO)c.CY8?73
%:#m\rW.ePs6aKsP66f&%LblnlE]ZaB<&DD31.KP(";*S$?'0C=C(b80=Z6s0'e3e[#]Ap6SdpE+CR[]\Qe!.e%>^>_<"pn/mVEDA
%!NV+SA^Zsu'e;V6RS'D%DK2Q\DT.f.;m,KC<$J!!%861k/:aI/4>_V)WMS2_NH)a(MZYhA-5`)0OZ+*X_R3pH-IYX'P[,aCP9I7H
%NtM5(Jr9-Y3l,PUql5(mE#tPtL+e`"'d7kE7!VYW&_&2B$j:FR;n;U)1.FjV2o4tYb5+6g0!7cuTa?SgLD;BFKul]@bL8)PSjX)b
%(,09>ZF[aBH"Y`cGn:sJMm7+CLjQ&6T%\$mU%p;fRI*"X',02T_enE(5^4qd7bZMO>ZXH$iWc\C'=>,%^'X>(Q$FIgGe:F'U(UlJ
%ef(9^G-3Q-JA"BHnG5j6P8Hk@Ql@=50"roHBVU<X#*\8p(BY(NCa1PC+tGl0Z>a_>F>f+'WtcR0)Q\kV0OcbeI!.!u&WFa+TmARS
%OO<!odP7@1mn^;"8>fJMiQ4'B'3U7I2Ml*\i0IY,E4h/e=RbHdKF@(\$B3ALNcEjLL@3#WV4%LpTdR+<)%`U5+iM^X7XU)f0THdu
%T7@_Jjl,Qo(8?+/M()OG,^]Z;3:kea,!O_R<K_3VA_-qa_("V+['Ctn_H0-DigDT5!jGI%FAdaE."Sl2ne34ZH>mX,Y)[':2?_a)
%`5CQ*#3eK1mkIB8RLhlXAdE9k8JYp'q2_SAGIP[Tl+J:LEM]Jja3o]K(4&nh[o[u!kJ)mTB_1>B-VBr7(U@05<*,ZM4%jJFj]TJ)
%S5!^,L=YV+1e8X-_)P"e*GLZM4GR^X7,'<'-+hfr@L]WQR`-.M-X3h6[rIEclQ=\]gl2#fQ$:'im-04\nB%CAC/`AK9THKNS$_7G
%QPrCP)a$9_r%[GBR,*gm76s76/7SJKN'Xp2":6!'6P2VO<#7roJXJr_6Qd@,bIUq8:n7PpZ6Z0:M:UdP5*`.Jo<gR>+qjg(%kPf8
%^rhmYK0'$0U$]8t$8/u\)ZnhS%.'sH_k$_+H?!GR3YS3fAKt490q&@nJ=s6OC/,Y/LFM;:?1D$6X""DkYFBg]<E`&3*b.!=V7VRQ
%L-#Xi!n"Xt8\oa`;CcU%P:n#R&lDd;"T)@m;saV$].9Z#aF+e6$^p&a"ZHR-D45rATYe'pAUhrcSf$)kRX4Z1G_knn<bp-Hi.k0S
%/^3E-RPB_N8>-Gc9MK:NLflNjbqY`p%W*7-L/$?hK$2NQa>Omrri,P['X=p4+:OM[@.9ZfH+F9VN^6W\#*4%m<)R[EZRNR<,EUTV
%`'<O3(*?N;:fj>Y13'e$X:838&t2(Dn\0l(42NpMP&!,$[>g$o2-LH9''Gr+E\`9)."oFlet':(3aDGncn^<+Zr1sL-h[VlDH732
%b<Th6p.YWIE,-?$73#miUg%4I<*!Xg_N\=S\hU/6*)W$>iT\UAkLegu6uU#KD-B?T,jq=0R&;79'<qf#;fL)d$]DRL%1A+6?r47-
%S/']D$5qO\Dd,qSIQp^Y</s'mEd&/_(f45%_4cP]9^0,r-"u9E.orr0K32nk-XstFMMs7oJLD\T.rp_WR\]r$Y'N37H&8Kc*GoI5
%CCPuQKfA&QXUVD30M4,h<"Ig1a\0QK/R#b\9GaKD5>un"MH$GJ@!(Bq_NARRoE"bsZ&^CIRtMSHK#S?+Z\j9hAf,Cl?4tBXO4GFa
%?dFt^m1ug*W/c;EoZe\+c`Zuu#npjKQA?snh$^eqU.,ef0S?_Wn8olB4@LTlPaiGt8,@nd@k=?COZCDEKe;mr:a8b(BCHN<`rKU-
%fJJlOaAjQ=6(<E=cR:g!Sgdr]h*guu##.Eu\/sF,m.rQd"WIc;WT^b:OsKP:XB?5+=\:PFHT!=^YitsXkp<>qN+>_Zf5>SqLeSPo
%73ggU-mDP#X?8A%EA1)7#8PVpHd$oa!W&p;0Z>iE$XRO-?dXI-ZNA*Eb_*&RR3-hY%*Ct,`_dD3^o]`Zd0_6Q!IF6E0S<-)4*Hfi
%d@Qiu7*d?T"pCP:SZooB15d9:SaI.ch-976?rnld,ab*Q>=/%.V-@U5cLdc5<#"]"Bpm,eUkZ\a]etj<a*0Jt0?",gI9lU!<g;i:
%ZdR7$3>mln-9%Z#Q._Hallub?1/Vj;ZkHNA@IRj%5?lSMVlC;Sf%%.$PG=Z3C`BPt;p=0PC_0k\EfJ3T>IINch,3C*QCq2gWrVTj
%>2Q([?6';G+D2l1@H^[aD]C$[ERmFXUuXVjZpa,IQ?:+l4k"hR:>45lmn*1]G1uMK3Nl:9jM[BJ9LXNn_mUj`?alg=p?\3l#=Acu
%kR@5OG@@hkYSVG6ZV=Y@Pa8LfVb`PkBh;dtX$(f;FS:T`e^?][3O.m4&P-MGeW,K4AQd7Q@"?D*a_d_nZ7d+Zf&6"k<t5i!e8pV0
%JV&O2'f`RrUPR2RaKACZ]\WFe7,2%7.$5EOKC'%@gg80mMk\0>3`5T7@g.\5(.Qn$GKH^oi&i!J]W/Le(K[hUB+-a\-bP%2=a+>h
%>4q+ceA%D!6Jp!#aj0EM\Z8,@YNTXBd2Wf8]b72MaG-2$':Ou>V3Mt;qA%[7^8uV,fi0qo_'+WF&T!jD6#;'l;5(ARZXHOC?rU[(
%0b8nS0u4^H*s`ms6h6?K$V%$J,?BdmXiJMV^lWaS5M0'H`)_4o6mVS?:0N:u@F%?jg;jbc@psZs?.(%e<`W6)9Jk9BSq_C4jSC:R
%ZXi^-ZFA49)BO`pJ?e;\o:tF`&`aegN9MGVAO[RH\r(p*WFeFD5D3TWTY:.rqHQg-mW4p?)Ip"2:*C9=\$.to:'jN1We=%W;)#<W
%KD$m*Js#;PQ?<_u(Dn!=NK,-uegGdhK^M8K@BhKnb(meU5W&s0NlblE@*Xsi;!?(a;J8G1MiRIhfP[5WSt_>:VW*3(aF:u_;ZNuS
%VHbCZ-pEMk(Q>r:?".U4Fa?:mige<@7fpG[afgph_#P(h)97I:-PNg=3lL6_4'%Y$Y1#+#*3H20f:cor7+><BjI8ao$hWXom^gR4
%;d*Z&eK%MnkW:@QH70bdDY-o&d*4u2fi]ldBTRVI1D-mH0Y*Z!Ya\;eAnn'=IC?%&grH[,Pc^]ZEe6T<W4bs*bfERPoH7j;!_*q]
%6Z)+\G8c*a'/5@fGK5"2?(@(emq67hiqd./g"V?SQ9_dYFbqb<WS)<1U=l>>.P+Bkm4=e0=rL[GHC7Y?6k67f.uXUc#&ft5U(NPT
%?tdA"/j583(ZL?aj.a]Q7'DTED1#ZcD[=#)0croLWR2"da0n6)<C5@D(a6(;N9ib^KKhs_<V1"4KFH(@K^3&=fG8o"9IEZCq:5"C
%+/[Mg0fgVBV4$Zb$m.?-#pF`=cs.8!a"oEG.0de#BEHqhJn;Gpd&.V/p$h5oQ[50"*_<pUgA`G3%lTLC4:2-^g(I"a.5SZ`>f"mq
%;"D:XnJb*7ad"E?/<K\50c[XIaZ-#]]e^V?SQIhs$ss]2eLGJWJYVn>+b9`(#kp3>#oCmH_j0j2`cN=\PkX'?-bA@-#f:Hs:emb.
%Y]C""Pk(%nJ0Mug:KSNYHmg*6=?(u;iGP8C=uag<.WD8IM\U(Q'L>&'-k$_SFqj(MR]<JGE0E60iB0R@EQPN2Pb?XV7aCpK+H8q#
%(8a"m7L@,V9b:qZ:&kd,5QFlCc1i70OLU&mr^QOB!QM15[6m9>b/aSr3-cI(75QDqcbp?G@+_&)d)g?jOP$oK_&"rD0m\i-US,iN
%0fU.ZKagRY@VmMUfg*D@8,$l+m53H"m[EqgE,Gf=V363hdTd)7bA<Ct0MZC?\sT%a(bCR(Pt<'6RUE:3ih\935FZ<92o1Ii5;K6`
%:0,>[iWN(OS3Y"I*fHYb\%T3n7Q3rDR?R+Xj=$,m5lCt;.1H.5]e0bID.9o-YL)4=`',*rAC^RC3<KQqa-`./*&H(FNR;>R*uSVS
%.O5>2,mU#McD!:.i[Nh$#nQ+lP.f%L&e,jM:J3>b"]9N34Tjg)A<A=0#E]R?e1?Sl6[/\o%9Pa6\I[m-jWgG,=Y`"!A\*c#?1V`4
%4<',<k+9Ft4Z"Ik&>]@5PX-]_E,$4b3FAS'aPT5?%D3\oma'f#K:b0Gn:FID(Q<:CW=<!`T4DCDL/>,rM#/br!Rb/<L"DKL1I)jb
%_h9CU4,*4QC^?b,;VK)863-9!_qF7b`"&!A=]g.;''84i:^;7,kX<[`@=iUgS1DXbA&e^aNEbVTmikV,5]r::'/FX:1JCmue4;I]
%XJ0u;$tlmc"fWtgUm17]^1q^K72Z)H92n85lT#k\1WK8J?tU>)a4-PoX_Bhbb$7M=V534P1WC89cnn,!CY"*[IOqQYFG6hS`_H(d
%B2cY6^*LhAgEi>HV]7mhA4e!XRDf1b2"`l6RNRbA\#-^u2;3<9\j@Fp;YEk*=X_b=,#;Y+\<55Ud]cp]Uu4!IVo9TNiAi6>_QSTd
%%\/4U4)4rd#2#-`:(]Md65A`#\sQER-<H%=dRRN3B((qnMTR(c,=/ACdj(WtF1%MlN1iZJV]sO89Q%[Mao&afHnLfX4qdNk/o&P>
%C5Epo!=?iXo0U\,dKX_S8<-&Joj4:46)GNsHk_f6S\n;RQ)*CuJoWFuc*/Kt:o5(7.60f^**%^iOZ58"!WCCQ+u"G-VtnKVpe4$,
%V^Y1H,uU\3GGAnR>[J2eUZ%-P:.qoA]`B]Pn@?E>/J1ceFe7@7]]I5!m.b^`/p!G59;Tg`-H`$ld)p?W?*dbdUqqOLF&:65W=hYQ
%/F!u!ggj.9hh:#t$;,:T?kOcD\"T;mV+u+e*EE9)'FFMF"FJNE7Rl&"K0"o)`,1h]^Go]UJ3GBEZ.2j\J%JQF<X:W&/5oR#0`ht*
%Q&7j5<AMXgI$Q&!NUdJWk-=\:^"?4=qk(hUSCp#am_7D/Ql-s"l`HPP)I7^#M!_eeG))U9ipcCL(p0Cm/9ZE`iZu8+4ui_h_d(m"
%_CM&4)[s9=PR*XfWL3]4/,BJaolX"]<(G;(k++ec8@=AdT=fZkhp-cs4U]u)]bI!h"?N[YY!2k^/b,N-Rd6k0bJhp6G=/i<>Y,l"
%J?0'l0Ot7X9R@+=5"e79ramM"q$cI,-J1"0/G14NJ'&",\-'j5N%qrp!!:9sW\7)%=@6aOB5Np]N9WOF,u_c[MUP@Bj)EWrY7gHM
%;SHl:3,1(4,Xr()^B)jQOua%.=R[Bh,TOi?Q\;)1*nRDiO<gqtpV,]YE9#=QLP_J9P;(2H93u-:p/UWTRKN[P4+i3e3$]8V,Oh+&
%*s9f:Nl"+I\c<h`,R7_tM`BHF]!DQS0SM_#mrDB$-IL(V*<_/\bU&#3(/A(7Rggcl97=+%K,hp(PQ5A#bfQRmNpS(s#_.nVRP*>Q
%Go7E8gEL=&/Mi-.[Y#&sTqj4X8^nP?7*;PH2>C<ZfKC`)PB$X]9t)E!?N$?@]d%*\=Sb0``98+RV0OYsGnNe-5]tg>T.J)'5%N7b
%VOE["IJ4n'*K_&e*Q<#$,T@I"X#Q4qqE6\b+1`qb#%EW9#u%W@K:hB$3"cmXQ)KMuc%@>\W-@!DZ@l+NEO+l;[)Z&,RUk_r@M!*R
%1`j2*!klRJC+D0`7PT0lM(Y_/bhjm:,A).]*4;1^QUl,,cQ6(hBA&`Yn?cX[Ia/(P=#e<GPr,ZGDISm92n6m2hmPC\HK3\CPDsU>
%<HHd+g!WGo1.Pb*7<K#:[Wj^`!BHZ/@Gt+KRSR#Q3#WG^)KFFIMm_o*j'%kL0$_>'/6=HU&e\-73:rk>?Kpht7q7E&/m:019F6\*
%LG^Emj!<\el-!:Ll6!Jni8*\LK>+ue[1pa7kf[1Kl6!M'\m;CT4m:'n1eiILQ+jp(VZ(4,MU(!8*PS1"Dp`\d^.P#8]iY6D[:`.u
%k15/Kh1AdICNn3n\),V!DG:IHj`$SG8tr5VLqDUV9jR?'f;uM@km.c"8L6(n3Bk,)P^-C-#9=E2k`N?Cd@.rEWe!iDP7Jm8/N)Mj
%$A`@Z9o\VJJ'_3g1f,0Sf@)2f4#TnTOb@>)-Qa:)@6'':=]P$.cCS^GY<CAFGsa>?r2AcsIFRl#F]Fin5AtEk`p6?!fBH8\VQ#3^
%\M[toZ1A&FH=RJ%F,OgUWFp;<6/UI$dR8"kNK1c@[^Wb9Q[1R!*q[^Z4i_;TDXBI4FE<B+FK/tplTsF`g9EuM]JV@XO[38kEK*0I
%g"^o#J%*-\S)\N*oMEGY_3"$^aAaJ=&m>D`ksuaD[$X*(24q&,EARmu[s@,nG!Jld'GO1M2s<mE2sRqC+8*^Wd8\Nbp=>(;qO:\"
%H=7oU5k8k*,AP(Zl[_8\kh15Qm(D(7)VZ,X%>RQ*;78:EF9n#>?4d7e]PpCh62>1'G@#=>`=]C(a:\Vk%#<E,P;t<9Qfr8FA8tm1
%UYtN;PNG__L>=b[V@7O1;1c9QOV2K)(D_-&>b\2!/"b#(7#IEXY,uF6]d0!-L*E#:_C1Z:=`#+^Q/[\LS]6SMSWR-Z[dm.^:YXDl
%#E,GSGUA!Q#^sB9l!Ud-jhQ)e[@YXF/"<X-f!4m'dI#Lq.V3o>jAI_YCc,kkeB#dFE1PR>8"JL!iDWCPd76hfN@u8VA;s[CQ-cY5
%?CgkZ#U1G1g]_-*7O>4RP;,-)\G`dO%+."n*-^0p137@%0t;t'+o8Omo2U;uPPpkX6&DP\-(p6_YqL3D@qj[e$@UCEEQ:cu,9HSA
%D,l0i7#,h<$alda77W^5C1!j":A2uF@94I,0AnJ;43ifdh,P9R3$/oS_%'"X(cFsf>M1:P=:0s9i41nsPE#q0b'%!`;co:.?A-]_
%%^Yk$UpC5ll_S$'Q7*+&=W0&dq:%uF':N0`:33G(d1ZNlk3O?`#^aL;;KSJm[&(i4dJN^.a5ZK[PZm]:6_5THG,!(:3RdU6G5b'F
%hE%t%U0kT%A_Ip3R[gr"aZsGJ8@"@4r^Ga7e5]K[2\nhh'G3]h<\3baDVXI;A/L0!=Mot6C^r;ZXBL=-Qud$$>V2+S"J<@_Hn:9;
%#+D9Ik\uSH\X(_[S1U!0\jVDl%#Q]8Kac:;M(\H5pkSIE'%dM1*F:9lKsQarcDtNeV*,$4Zc=*6@B):@N.U9q8\k_l3N6_V!3jIU
%"E_IP6%)ZrNh"RGj:jgI9nESur'E#?MV$(@iGe.*-TGDV2MomAEJ"XoJqO4QhUaKqr5d#W22s!B\84'&<u$jP4RtG>0sG#UNdgQQ
%[[Ol@2$b8PIqHua)\skB0Klo-W\m'r1*D,1VY\Ut%-#6,^LAI$h/&p:Z9HA[m881$%o^Q>W.9^,9csk&Es0MmK@;ob2sH1u46p!<
%5#JID2`,$Q-1^h'RN.PoI$5%!J"WSc[hpJ=ji&rg<O6_pI=s7jDZ6-m/jRIaQF5r5)!om&dCh4<F@2siC8`2WqeY4+_J>l9-#+eb
%jpZ#K6!LYO2(#ml?EdS>5hi+*<'DZ8H((P/-Kej'eNCAiF\BTq':'JO\nRcIG2M.r*b!_c0XA1AJ[1+0?j)p:/COrgF%[`TEp3(5
%8j;TeKH%N9CkQ)Po6mFK;N9=N93el<a!QA1&SHlj9lrUp-V50F[bV\[O)t@.UB>V!n</E,lJ2lOd%=$,Tn?2*,>^bFBM*54E%RO@
%"sp*^Ynq`U^sH0qVm:M>WqY23q[HJ4i[q]Em)^3CbNV#m;ZsA14?685fmll:Ak.5O]M,[,LUr;.BiA7>mOP5j)I6>i>U3H8iDG[O
%bnBh6a`U!>3b-(qkelC_Sm?V2H[H7q$)+HM=8$5B4`?ido3l+T_o<R6^dJuO6fgmTBUh\&BfhmL$\RWN-$(:gVJ4uo_-?M&8P<Cm
%JLA&()N&$#-:/]jq1ADkRQ9sC9UIh0eJ_dTNt0_PMa$C*)1QQV9U:?Q@;s5r`g!rEo4DWU'$*Waoe\DA"@.p&_#f3S5_r9qG!O2D
%T=]*Mj;NG`/]WpXSRj^%Y8tPKIe^(*<%gfY&1Jh?3//?Bo6?Yg8ScD!-Bi,+ou14L=%Fn-:RR_:gU<)R>@+J_e;]nc$&p9c%nj`h
%m]OS`cupi:=?bgO-TPc>3,&hS554$uNn,'>nk+pF3QBKF(!s2se]pr&05f0N6&eg<$mCQ)J(iI9LDFdq3&a;>D'SrZA%f!!MIB/P
%F)(OO4fc.AD2b!XKE-PNdQi2UO>L[XGk(9H'!drp).79:A-4H#8MOS],SQl&@##OA"#Ser0`okZ5qmY><QY?+Od*BX'pE>\8&#3c
%%4eJNi`!9b2V#*YF6k*&`l,dF%Ed22%Hs_ZG!Of<Z.5U'-Qj%%RfrO"3$enRp=,P.0>O+1"LI)G3(;q*`5s)CX)$/Jnb$Dt@E2a<
%UP+I1n15d-:&!AZ;9&si/_4itb+T6s=kBsPP6aEZUCNf&C5>/F*RNFQ$)0`"Q1Mm#*l.8,jD;(ZKO?0(l=kI&_(fcA/Ts1?*jOB8
%J,VbrD&g5\\/cpmVB./2<6P=_f`fe`K!98BKHPY3cR,B^#[+ajJNP(2hMNY`TH505YsI?>SH#.:8[5?1&4n[gb'#G3]69VC6:i38
%atOif=%=X/pL+_>deXN"U?TV!XiXm=6Da#_*899`];GkniWQ.^&='"'K;#aW9`7@e7#DnZrR_lFjg=3K-r)#3'h(q'+D\l*?5W^A
%'>fr0,&`^;3E$(+fG(]5$=)RFNEL6R'M$&^3#INcU+_qn\DoHXZt(l4XJZVQ;9>Ys#&3FM-o.]NWHN:T)YRL-c)$O\AO2q-1Z.^@
%SQ[ce)*,=SHB5+(1JP$P)Y@UrqP`QqIC#].N75sS-aj(pDE[4,>JT7A)fIL\]/Goh2SQ.2d$l>4cY,l.'n*[A5]6JVr/*6:IIH[c
%p=mdFDiX;$qt=BGrqsZhQg`f=p%\+PYMLnrhuD6]\'NsEr8o@*i3BmS+@>AFJ'ue;gl9'g<.=dYr9JjM]71"<T5N*%:N$0jTA7[M
%n>0Ll!h,B^DgRpkkeGJii[:'I*$(oMOe8bsa`G1qB6+g"`)4A*dCc*f0,2J^0q"dV2]sIl:nZ`MG*mFU(aoIa/*[S2$Ig<m'Aq`j
%rUk5H:I,++-l[-kb'jn>Idrr>TBLt-Ag1A3M:`.b845#=OTVknEW2ak"O+s+2&#nTM7>G4`Aq_AE/iYr0U=tJ)utWS3htGtDhm8$
%+;ZC>WT6\,lA.ud7Tp8^D<75"Gal5VgoQ>B;JmLs8fLNLbWXApF(^";=$lf&@N<K_+3,CcoNPaLheFM,$J2@#@%rjn^t5J:rS`pi
%NT"6m8XTOE/#Y1]DY@b:+?JU0'-]%j,1>.,;JkoD-&FB)SH?%W7A1CWT.VHm+$VU$@F:>@U\nY##Y9efW(":cZLKZol&`]6M`88S
%9hZ;o;+LX$[DIhgJqrJ4\@j\dYgC^\hD&pSpJL7%U>shAF,&JJ5XaP4fAS@15R^AI3ikJKGG*T!F%2d,N^4gt5`7K5>A@%f+W4),
%#':R$19!QW='a??P.;!q'M8P9)LS\rC4^O@/h)I?9!,5<`?^i_Z1oRT6!<9M#p-?U]HDgMMST!\DV#`Sgj&@s!M1T`Z7#(MlO-SD
%0Wf%OLa3h<OE6/DhR+R7HcJN0DD2[SX:U)>+L+^"&G1_o7juMJ$R,I[`j2O7`):`32*@^6*Z9q`9Z0UI<jQR(4#,MH`)3j9\'0]/
%\2/7si#,am$8Ihr=&ML&m<P[q;H<8n'dqU1_X"nW4thOgB>-*HZK[RTRXjm<+dR/p,2L"YWceq4"%pi0SpU[N-O#MaGba?>9F0n\
%O@Vcd$IK,J/MpEZLhLBOTVL/q]?l1`e2+cQ\\@"d#>DKoE)ol2;90F`/_:?W1IB?:mb&Dn7"QTR`Aq\^BL<tQb*&im01N480D:P`
%KW_6..K%C8*tf/]`h9q@p&_dd\$:n5@>U.ZHL"#a'(ZM'c8hhRNgX25+;=[;a8m/='*IMEg^'LTgHi.(bE=<1gK/Ll17nG>lS%9.
%?`;X\I)/Ku=c#QciVE8mPL_uQ%&RNt$mD?Gau/[Yh)8]c/P1+V%g`+_P*Dln*&ERTij<dQPYR)2k^)eUF!^IELt0OpZWm%2C#u/Y
%ZojP&p6=ot>eSB:Q\9(W.@>rsGG;tXl^0TkC*Yj:5^G?PYtT6cL:TbE1NCq2K@]D:1*H1\pM5Vgg3Yf+CeS"GG]D#iNT@soCRgE@
%1(c;9*YY5gM\'CL8PL"fWc[7;7*JXap,ipF9c.@Q\0I^#JL?Wi9&q*uU=1iGc]h0J"%6c92\A@>O@k>(0TjQr6o9iOdp&,rH#lFn
%AWA%)N<pW+U:!&0_YJ)s$,,?OGh+\d@ABq4Wh`ZBJf;nP_uW:lSDs"JL;M@V8eA_>g4mn)TFIbT-U\r+3'CN?-'9HmJFmF9m7U>9
%#Vg?/O\Z$Cl$uTZ5jnEoD(]`2lC!Z@P!d#e;F$f?NR?-Jc1G[L&\[N'/G!m.?AXp`gpZ@oHrF,-,BUi\HdCK:mZ`:U?:#$E-sB,B
%ij.R3BpH]^<cW>q0m`T:`$q`B&M)$V3N?c'c^aFY"u>YS;#n^c1K9OJ&JX3<f<Vs7j@E[u**((i^*AWVmb+Hs[_Uar*a"lQFs#O6
%U+=s7eK"lF.\*c2p]/uIN>Lif.E40X=9]s^M:2YQ#2"7Y+3:T2X@UVu+aTK_LKP-;%fqEWP^JH*8]tA&Q?g*,!rJor[*Z?]3qS)]
%G.97cN'tEcq7o\$2%`g[KR'S_'&XFccI4Lc8jY[@S8._#"0^lm3#YP`$SA@5Mqq/d&@.NQp,F\I]U;>iaINqmN0pZrGYOiT7Q5?,
%csk3eHq\9n8qD^?3mRE8(KrI]1fJA:MO[U96sUj&[0n6X2HoO;W""QS'E=.;KS=ic=(&;98kLBRC.m5h@L[96.@J'JM<LA8jMoTH
%6=f=Fc__+ug*0Fnk\8AMAhUKqRKS3YLFT(EmN3W"&PE\Jn&qYT$p;F;Bd?)cXsW$M9cfX/B]JEk`%)r.nOs1YecE5fK.`PtCpWFI
%F*]?/;8s?K;/rr1U"F"KUp>+-R/0POOc2ck3\A'`F.\g,'=5a\!du49s)"Y<cuo!9Ei3g$Kq9KV[ZjPsBpht;?=Yf)n>N^grIPGb
%l:'?:Q>'".N_GCkjnm2i\.EpE``gE77L#XJ5Y#rP1#$01?r&+IGR+gGcVk9M1aTCf+>Km27M\4a"ButH^H:c4l`tJkgEjrT7)W2u
%0LfX"QtrZJiJJetWFj!nd<<>26P,C,+\Z^h!qH2Q4RY1/!sOPqSR@RR/2G%rS3b1]G,_1i>!VtLmZ%_EU[M.T,Z87,%ma8p7NE\]
%=KD[WA)Xs%,.1q;TDtOs0C+Va\C,a`i;^S/"5O'c3qfEC,ggeQ"+#O[ma5Dr&1A=;@[i:1`NlTEolJIMSVZ?)FfVWbB$r]2n^+0X
%j2QE)XP`8;3MW$\p`tYDR5L4?S<2`1I)WWtYVaa5P!!E"'O=$m+Jd*]7?=j(bpdE[&h5<5<jm(i3)fB1AJ[fH*./oOOnT.67k[IQ
%N(3CXjkZsVcdq62C/Pp<?C$B\;2V>T;6%254fI-X"[u,1<,Yl5p?W-kAdH1</#MW8b70GZY;g=oHp&Cc&-]@=Ng+5jR``%coa+2.
%.&Y"@`\E^Q7kK-;@pa"#T]9id6u2JZ6.sY,gTo4/N1A\**b62Yd?AVgdo/Y$(4pjPPH:^K^$K1<@^3-O<M$&BJd@Gi.$8(EP8+CE
%6HH9u>\V%m6ac-UMXV@!q.i#JQ%2!mZ#TXaep'K?Hu]k+=K.$q2WFKkk#]N&9+1nW3tK8<Ic?Wk2j9%<Mt'o8<P$=C>Wp9aZF!E&
%-mjF-(n-FF"t8tW\u6Hg1]o._X?AlMVtTuFGQG$K@K\9:UF5r0[;QnY#e$_!8c9,Gh%&P7X\jJ=9OJk"*YD&4T&%ieI>AAN#,8/L
%JN,9Rc6DcaqIkPZ7#q3\iZn^DnXW>!@bdcs??4H5WOVSO4,?t/I+\[a-`h)TbB_4iWE-,H0;C\%C!<aGPpbW"HVBmBEmM(k/gac1
%)2Wp)&/g+%@`Ua.gCBd3\(SbTXA0c"Ur6_<+&n4F"Y,1ml8/kS@Srg@ABJXP,Gl?_P&0=u@dk$aMCJm#bn8DnK>)$Q7M:]QM8%:(
%>\`<$mWd2QKLJg9Fl_daK+;$.L4E>nVp^@6B.-*OgFVt3W)@Y%<fGVTc75_lnK&G6TNEHs&5S55Tp*q[9,1K;(4R9`6\I_Ip3^Qn
%!^tZ1IPMVcaYOb'FCNh\a'7,HYG)mZ))"JC_&+KMPh3o-Jf#t`(j\@<cs`oFH;H-<Ae1@u&-ao4jfm:6e>VZ',;\+4kfX!XIMKK"
%>Un(!ibMSi7!Y%s3s1Nc[`_oFeSm7?kGSHlAE69k0n#0R*0lF`'6B'GPqni<"1^0XNu5<*f-l6qZH:e#XO;@X`'^#pXK"mg4aRUR
%*\)ua1rIh_qK)?edokcE@TL3.32'H':'d#;4b_9L1$,rfaM^rP##5'ud;&t1VFaVHi&RK0e<LXc<l*9AB<7g5G2&BO<-s=`/!OK,
%)k!Z-Z[=^!BfuOCeV^.(Bnt!om-8/EcZKtD`;p(RR0XG6jCVJ4Z;i(_f7mkt-n$FJ&T>><WYqJZZ#4p=8I4OPg<t"hC(+7)8m0XD
%4\ar3KOcaV%U)8d;H^m@UFJnX4t8ns`1eUo/1H$TT>*-C5,\_Q7%Pr><Ji/<a'*eE0AE^<#r8(HZRQ)pM0muj).*i:&j`+X`#5;>
%*JSWiBNdj<TKJE;4cVmgUB=SMC,8b;9g2UAX[@eT_f_n;BtStLBBo+!HaW$uPK[Hd\^n>EK$"m_SWtQ-#"N@K-I^5(?CO-q)/9\e
%V?HLWpVU3;j;UF(1O&H0lR%=cgR2K%-]=T(%?r!aZgEgQDk-XQFD1Rp/$V,D9s%]+VRYXf9VOM7#SIWJ(KljQ1tJ#DM^NfEaTU*=
%@oRf5ZAEADePr+tqsG/?%[,XU9U*p=T]e#6V9OIqk7\5u,6)Qp'7p@?7#`K.\q;eei<nRQ-f5D]nY%rfL87ZcL@lj7Mepeg0X6Cg
%nL'C;@+)CQE&"0uK/"NP2J0EIO$Z')B7P9$4-=Y&LB5+O0Ug$FRpWlG]I#("(8@l:6>OOti\`QQgH.+sZ-.gt-R.P\U)(.^Wb;3B
%-(>=[Ge!7h[F*M:<^p,Wcpd[S]%h-LMB@CM;N<RIeY?%I0$7+"KL"U%Ys8m.j3pD*Tkdm6XQ5C-kd!5jq))`HGqJi^SD`QZ+?m[:
%@Z;<N.=B>^4rD)J4,>AC&r:mOFQ)[VcaF5/_^ELU>U?Df=49]9Cu9qD,X<:U;:KP/L6A1ba!\o>1+'h>L8b-jDst*]XfJ6+P0g\N
%J)EpE@Z`/Yc.B-^fWa>IB@KTd;\eHgGRWW,"\AaB+;kE*%+4/o-+Z]AKG`j0DE1pnPpc\Y?)V.?i8^DG9rJ27Y3O!u_f84iEg)1N
%U]IC>F;9q$C:ll,g-;F0We^7EcR712W.CQOVhre04rX+@ndD<_O6I$ET+KOqRKZ@%2M-MEV6R]-6k^ZjB!2`"0OfVnhd"(;\6eS\
%k_M`*\e;];+=`9q@La(A`-M^+#+@N::7q?VZ%9c#+W=KqGU#QL%WnTlL\1Eg%I=^5R:oH>`5LTQ(eU+H(a\KY#`plqaSCilNPJSP
%MW-Lk]T2j&2R4r7On^[TNj-PR:,!kE)Pn)"G]\NYS@ks02cQNkL!e_CVsi"E`\t+M7I;1ug0TCAK4l5JM^1Aq;nFp:Q@(Lul8?95
%-e<oafP8spc_V+QJNbuQa"S%m60C-g-s(N[.n2;DoHFlH7+?0=2OTdnL<%[sd[3EKF:/J0)Le9LTPc>]L4GD@o=D)aSmd[Oia?/g
%l?H1t.[d5/JV`A(3C+1t_54;n<I02Y,C>1e*fT&g\eZNZ1m?n>Y(a3DBjRA)"K?Qm+H+<s\pC0p(PM2PNbD<qkVC!UBhPN#OIYd7
%#6ai;E'jr)XibQM'B]j7ckk8RqBT$@c<&H::-&N5/"n9tNbDF1SAd[&4lhXm,UjihitBNU`@Q#oYDe#4K;@UR7Z3R-&H5chJ2UTt
%Gnr@Km6TjU1^Zaq!dCOuB&QGWVZ[-S7T[DY):27s/0GXg)V5+seff:''PiYY4eVAJWZdV&*Oof>&]@nLjPHkU5Of8jOB-16]dPUN
%P\Ulq6tj@AOAU8@au;9=bIK<s#'ekYP>r1acl=t;?G7CYI%93`8YrL,#J?R^!oi>WQu>Jf:*"KdZ!k,:anq:>W4aR#Fg`^4Yc`Fj
%+Q=)IMs$a@cnZ(-_&#$U%!`<77>L&jkoGZ@+pupF@14B3^q)4>.$1YN5ONfG_ikr(;\r$aHI1@E%D'6g8S*PbNEh:E4=4t>4N*[8
%<D6pcjQjK#FBB]<`k*r;e)Zsf8sI7$,+96ugVWM:[iNDtf8Ur4dHPoh)3m&?lToG$K!&eW9^MG'86c<@>_pLicp_t@71\YgjrtlA
%S=\C]`j!!u#R["'d1*\>^"aScM0PI:BE%,h;e(`I$t?hq4Fj-oBGs7$kd#fLRcrS%_o+#p`/r6=7P*RacoBM\\"'[hNOD#Q#7nO,
%#"!H>Z5U(FH+SN[IVNs^<<Yhk'd?fLKID9S-E>`GN2!r1oPDsJ)ETqZdaVjNiU0-7SB_^pNX@i!X;Z]>R!u(.St`q=c@T61EKR^X
%_'d@<l6EG8<1Y)H`d-:J9&hmR=?N@IL'T(l_r6jq.Bs%>,A\ko7h7f!Uo:O!DN<D0Bb2@mB&1I?7OohrQ>=i-$rlFZA<1Vb-q'<s
%a[D!.bbU?:7Dj3VI;fH5Q6,*P3A&K"7.3F#YSKDW2VW1#R'"aS:.1spd$kRm:Ko61BgY9cI@7%j.PDFY$%<BCeqpf%L`VkEm-P4'
%K)EnrTB^:;<5m"4Xs.Efk/_\_Pb@[_R<u8=eAe.E06@]eOJQn/U6doG(I[n\8"7)CbubM,IUCK#NRAa/-H%WAp<VHuA)kgQ_QpOt
%WN"Oc.-_NX<OVfK8sc_jHD(=?kj.$-a2?K>08'7KS!C/(NXhXQ-76q^H-EFjoA%Ud>b.ViFFp_Cd3piKd(]oI%1`rta`PfL*0bHg
%H<7Sje6$:V$<NXgm"I*7%Tit`U_G#Rj3UHS-O,M0"0M\(gf[Xc8)]&5hoFiTU(<E\7!)]p,'kN0BI"0%X!e7AiR/u5?0JSL7+]XQ
%<*[fl7,E>SL(P/pn_?$FbHT?dC^Lhf:_@\-TP5hah@ULcBrdDd&hG*Nc1M_!\)iAIW*7E6*uR:cb&Eb8*?L3m(f"q6>Lb%ViS*K9
%[od-NM27CTNZ_IK_V@]ta:M[roU,9^W5uhD<SY/6iC_<:EY.9p_0<T_0QLt<$s&e:Z^=NLe-i;5PfH'#-=[gJMdt>7Q6m(%Gs=NG
%8[E2P$mDXYV422Xd1B'Meb:)cMQK1QqJaD5,`*,\\Y'qD2Xr7nTc11/dk`\[I[)Al8l'+/>D^i"?VEje^6Y:p*YA&,KYcp9eF;F9
%StDChQscccLlBVBhD;%hR47uQ0X3PJl\C4m7XEE'G<9IW6[0g=TLsmJXO`W.dk(@5Rr=l]riP5:X@S1pOAIXJ"2lcW4Vqb6XX6"E
%qPH3I\Nqli!fn`t]eVu4Z'":6%RbDa]=cX]e^`[27u0U>M29eg)Em-cq.+4KT:=;%o@bd>VY4_E=n6!X>RQ::QlAPe>oSBkpCK'5
%WJ<,&%PaiH&^jaEf7:M-:_Lh;NUqdM:N`EX3/0%[;t8U/DOuYIdC.EReN:o"EZHnd&WGd%@`NOFieW#f\&/96]]hTi>_%Wf5=!PI
%=i6&u7XTYC)BA'BK@*%l#ZJfh0PBY@KH]4+9=TK`<MQHFNYKJOCs6r?+>QKuBOoWqM24g)&F;!.dZ5G?@s,9_+ea[]oF;$m]cj]*
%aV/8N0,=&dNAaon*T8""C-<]u13e`Ni2W%S;sXEkfX_l17u1D/:g+>f7S%^Jr;a'c8@4E#P!VHFDCPF`(;up`G_GlXNI8Pl`MR2'
%1jXrc[!NesY'[CNN-]I"NqCCsn`"*ricG@S5XD)P3+n2X@o6cm480utYD]#@:TDD\;OD=Q!'*!ND%qbk0f4fYl!h2ddss^"5"3ep
%Gn)_,MRdlR!U2:(OV)/6XdQ?IVFWTJ?j&(TV[5)^QpI=$cB>iWjJV(')@\,)UIG:5OTXMV[oZIM7"h5>.bU]H>8"F^p9#E#$A>fP
%g&U(LGo'0'b:QI"-<4jmGGO%a$a93bj,63H<^d$`2UWLK1,-l)3(FK>,FVdF_G!o]mDYj"6p2`i5.5/680iDDMC!=7,1t#Q1rq6q
%_>Hq:P/(S)rJ%b\&[^%i@s8oC:P`,#_2SsONug:66EVGOZ-7&J02@qT0YNIN!pbL1Zm-j\=D_jqAn(%94BS[8B[<C9Z?'T$lP[b!
%N<<9b+k?<eic/W;H<K$n<uWUc#0NQeX1n.<)>(>0/i@@LcNtcB6;1<:-%e9u=C$mkTpDj[eB'#1XU"X4qaa\%@N.s-rFB)dLr)IS
%g-N&e0INPJVaf9W4f@CW(1Ob?740LK6A0R/9g2.CO$lXRgC#a5<,mW12noo4(*pH,@[6.uRR/`2'R`_#Ds6HZ\ia8M6^;>9LllJP
%lZb:)/1k[)NE=uRLm+2%A^]!QS@uk;-1#L0jX'YKJ]Po:,t):ON1(@!iXKpOcufKE10;,U?A/[ZT9Jn)m=9/ik']<4c<luWP8(Z/
%/Q(+qMAOaR-+jT.8JMQn/<i`S,j,Zl]Z)i@n4LV,`m?n9bUN;2o>+%(!HT0Qe7mc^[:U$`WHfaJGk;P(erYQenU'KD,YGDbJ^pqZ
%,^0PSTj%M*'-<0DrYalV%2O:Qr"7@CZOWE.s#J7E3DMhlO=Sjl+3?-P+q!=ho>hbFd8mb;a+32(@j@,HUi2ELGqfojFW8GiS:LZ\
%mLUs1j_EiL&2?rhFs9,*NklQ\E^DTPKd$O\6nPUQApGY$pc3#XU69<>Mh[QQF(&L&a48H9`%Wia3VHqg&l,YSFCH!$6Y%'ULo<VX
%[MftkAg(5d7&4_Jj4OJ7^c.OWiEP.9j@U50(EHW>$oA@Te8E)a-q*cRaL;-La7NUKa9#d4=(lF"K-lmp]9TAAOt?8GMtW5hn;io!
%C!]OOrsND;#Z:s00%JCIA"oY\C:i(bU5l%(-\9al!HpA4p/APTKW.jRJ>H([MaT!PA,R+;+db*c]p^i9$5OnNK<qa)Osc9jOtr]`
%_U&?E1P/&M2"Hs=,"W%pfMtf;"E?G[R?6daVB^V)_"h#.kH!N4N'UC;Y4n0=9/j[pj(o;/d[GuCl;@'19GY+C7?n-o&)&b-dndf;
%<)k+*.n+t[Q9[n[X!!K<a&P:ZQKkq9ee%)AY/jPg]ld3nR&iXk$E;#C9$AkSB9e>"7g5Z&Q6XMT8l]O25UZ)G;o93RLj/R[404.9
%"<:8N[0L=e)%0"<D/6Sd9,Jk-AXBtl2&RBV!8L-s/gf>98RrO8+ds.nLB:5Ae5FY"r)as:OS)SQa$G>Vo_u)E@6I]"<Ao\nd*HDs
%/tFF;3=T/m,%'!k<@;Eq6%/DdkQ@`Y8pWJ#63cl3oFO6=(7Ds016.S'h'\E,P=h60bSjMeB/u9kbFqZMG7+3!%k4%l6idI!3M^+.
%c68-j@d]3)d^<`(TG7b_idtsi0?6T*"3I3kQAEB:Q6$d.d<Hrm&Cs0*3#9JH.2&pE3fn!8Yn6R27W[4CXI^+L3&=[q\/>mI#!R5l
%Ur/ue$.GP6km3l\MC^Gl:@>pNRF:YO1?T(=8"sd2*O!(G^<%P#SA(jeRn:,3@7Cl:,jRStX>qK0+Voc@F!Tb*M!Ebjg%']Jm[_gO
%fr4&OaAlnrbqp^ndN&Bs'^q*+:o(OQFK>dnYo*'_(99d8WpO)#=076[&-5o8e2oV+ioPTJ=n*n"j=!bffDK\"ne]Rd1F-U>6]V8$
%C2fXe)j;1ul#>Q9Z9IRSqL`Q'1IO.@nZuI7l9SqZMqDX%=]5N,A%#q"Qm\P$[anOk!0<l<0!UC)6YT)KDKX1GIaqh,c06r-!2Anf
%L)7)96'7'4i[BVM4Q$Xc832S>+DscW6d.gD]l#s6@.^^aRfZGB7['-q`GeHT\KE^NC/"78ra%u,5?<^XSJl!:R8$3cD)gIU<,J[B
%%3hfl_@*Z,ZA((80/\DoLB4^o"=[Ro$f`KW;$R"unDi[pj*>eD37?.GP#d:QRtalgfA1nJ3e#E6,c@;JK;N1!i1[92GY<#\kT].6
%*^g&N9<nDZ.7KE8Gs"tYfIeU@]VTm=kg0Eii('CdpkOg!J0l&U`'ZqX[`G2C,1soC>T2d['>ab4a-oSYM'^+@,@]"Kphi2M-ub[&
%'m#Lg0sJLH82BhKY#1aRTL2c=MX^V.$P60Sfe\#3Ci:*M.\:OM/CsaElAR;b)N)YH&0k8H:?.RNS#"Pa?FGY27UA[N*EI6W)J-`6
%[2rR<fISeg-s,1`K-?sW!$u#\juNKKEgCAEF%Wfm9I6i6.R_?Mg!U?t0N"(a`F_]C*_mdqZUE.O&]&:/(lY!c#B>91Y2f8jBX_us
%4ZrYR'CBY,47trY`"<6g(\j[8,+g,uop@mC+lPCNEsFL,Nt\dFCRe@;*G`j$5AYX'e7*S93_TQdJ+DArCaE.b3)36l0q+Rr.7c5]
%_JhDJWkmgO2EF]`s#gXI.Wf25UQhY?4*58TZN-!*SB':&7VW'Y<imILR1MBq,['\rFb'8B3"iWW]i3%5#CT2lRm]NI.H&$BoK6=t
%#a'*PkZ35mUh$n`ne*JB/X<]6i)DRh4@KAKUZustN(6`$O!W+Lr*Bac`]ds`HLXiE0V]QB.2kTGMH8#t&n`GsM^&J_&fN_eY>'Qm
%J8Gd;rZI3Q2'!Y?7ZZ[MY91/gTUoqS0\$YWYbp,CM4q6XXJRGDCJ2V'%mUWi;Sk8)gHq>+d5<(fFnjK1kp>k[b/O"qA!81p%WAX^
%<i1F;fJL-%,r6VV]!%!qklL&X[P@m-?/3h-cRCW>a[[nL#CITkjGjGeGiD.)etb!?e.s/KhJQ+%'://CVgFU1OSTZ5BQD&cCl^q;
%UHof94gHhYbU+;X`-<keQidPPV)JkKR78!i"f_[f#dQrERD=-)YeW#;B1!201CbT:gH*o9k[m#Vj&JZ1O3D9m$X@&p#SY^p%MT5?
%%=Y?$*d7A21`!D(X9e9-7:XSsVm!qMlHBC>#ng_b"DNJWntq`a87$&1_*]0.Ri4fX@3Q\W2Fqte.**bs[=gB^g"GcT8FZ^JL6pNp
%fO++MjC\6;=[djpZ_GBa]O]h8?Pn0kRme\q9ShL52M)<"=<ri3X^GQIo]1k,4"7nOh+aj%)\jM5Ufu'If?,PZkf!<nHF.!'Dl>1C
%!K6*E3>LfW8A;9`I7up`'7EPS/d`+#"Lr2E'($V8>@DE%Kd]b]\f)?YQkoAhH.jCNGDN2kr5$%iP[gmWO[6?flTngn[$7Ss;3nr=
%+$i4)Ka7hW3!\D`p-+LDdd2sHBLbhUD91MYb*'[/Y2Be/7MK7&)M]C,"l['A`%_Q61pmPrGbm9!j<Ns+Au6\\5+,NN-VlfpVFH^$
%ZiJ]Z*V91Y))<lsCLOXU'&=B*S/Cn/MB4=M.[`k,?/@IDom#C$3!HueS3J=Xeqd'X5_+[I)ifMpR>@-(S4Cm7agtkb4osH.=K6-5
%6o6'')4hp3j?/YZf0%Us$>!g:LXO+t:WC70@mPYFdT?JrOmidqZ9W%R6OOgCoR-XRKtX-Z5VHf`$OQ7dl_D!o&$qdsi5[*(JDlZ2
%c9-X9'(DFDM@laRp&NLnOIKZ7NoR\SEu.O08!'Z4fa)LcEplHaeJE!l97FJ.>uCl_W$+'8/'LW7\/d]#f!5',l)@`8DZ:'/'p:i4
%$Sb9S#Qm)JMrgd_PVb#f=AHpM#J:fa"W3P](:qe5#UNI^/VM^J@oB6uL`I#g'R&C>N^Of4i=9Pr1u+OZQn^4r^L02=ZUu+$LI;!Z
%A4E4YWtCZD3&F%'VV5EoioeOkfntD^aT;s=`n`pjdBFM?7DP89%5/cfCK>&>c/hIG]ff=1phsFo&NNX`.%!14]ke;ieN4dsVEmUb
%Y/H=i.]*anKXZ'O_)/Y_2j3YbZ3mfmg5I@r)_.X6fIJDqpn)F,N$QmJ\P=ptU2T8gET3A6e\rDW"R)l&o1Y<.dHX8Cb>[[EoWBUX
%IQE1#U5h\WnYkra:%&F#q';Uq5VJgr*Qj*@>=nX7>m##^$e58260SrRgn_m@)NY'/lA;)d\su3qV)48`3fHc>5n[/O2Zqd*rD)C'
%).H(=IQG%N!$$q-j&R/p7rH2jYZ3W$8RrFe(NDC/7=oLi8b_&*8nSr=V+\F<bUSkEREs6ll&103.K1.2e<WSu=D`Frc*[hHkoL(<
%P>bLs:67=Am%sl+M56$o-9;XF<\#%h%aEZ^05U`@+&0g])?ub*(149JgEiePAMdXf=:g+LF<k=X1(6jHJg9Ju7gLJ?]2BP7o[KCG
%C8_H=.F-ddWW8TiBu1KL8en-PZuc!cW$\nB^+d<b<:7@`UZ/'^+Z=!c;8j[C<<>EgUc!3/d6!p.BYr$pC3`<2M!Wi[F-6c,9`cN(
%9\C('C+sparB=i['^.\GG`_V1/?qgd6/>s&7SVPYop67;P(RpS-0CIj/"oId3OcYFo?IrRA.0]Nds&SF-#j\4EH%@!OS)!E[nK^W
%2h+[9WbLdV`3)1ZP;?]F&uio3_.S+>n!okbD@n9<,*#<raX0F*(f&Z;c!p,e7UO6cnu-Ol64@5P&U'O$j4Ft"<-P"^&JeA8)Q:M5
%3cBj,#t:J>$oKQX>BYo&%+SI_T"j4p8Qr?!%Rc(k1b-BT?.%4H=ejTEZcJhB[t<`;?u^f=E]NP+2*pCS6?U_V#Ra?m2i0Q)4q/Z)
%HOOc$\UmbkpOEe]*G*Hh@GeX%Et/(?1]rIXb2Akn9HA3s%A%?CK#Pj?(pN>D^S&&?M%4o@Ekf1FaaW(IfVdCaD<Hp]9nJ6Z^%B_)
%=[F0T1atUk8AUM*5YCM7AH:DCZ&&NE%VghE,!8SY6U')@]Ll!G\8>9/c1mBC:a?Ls3l%m9Z5?<F<Ohf=P_`+@LAQ-DJ)Gplg]f=H
%k?0!VBj@YT5=USM-`fYo,HItQ*nXINn/!/k]GZ.]E.A"6hUo"9_s$W/l3Bu$`,.FiPVPK32S>d#m]](C[t3MUA<YU[7urKETI7/e
%O)XNGR3L.W!+h`^Y8Y#)-@m$n6?UqoA0$W%#e$QXoBur2Z+T7[4>$>>XbWQf:dIO@jlN/J?2,;VhEs[h.A]b"1Xh8i<FiLP#,eQB
%;50%rRLF>:^TU[-.8,,J2Jt4B0CO?d,)d_&4g?nqnWYtPYk+f&2!qILbR<0eH\c/@[%7D[bek\MMiQ5;m.<+q63XY0**W'gH\?"H
%'^M8t$>)k`*j96Bd"YK#U2C]9#(,8_W&]*j7uGd@6YT?UlorBt-sSYT-eOPJ(`]b0GV+HT2??`RIIE+0ei`e@)j^Q8b3Q+BcWLq>
%c)Bi-o3urq'D*'>+tS@=0G0B4#TePb\?u#=D*7qgP'HuuO;O>jRqq@Yl'&1=JH7mMd@Ut$7)Wo=DM(E'*a2C&f%$sGb]&_6<N9a*
%-`K"N6O19=g-M;qrqKLFU!__/(g&5gjqR1tV?E':&=4aFc["+/UiHcDZ[lkQ9u>C*ITq]gD.Gn0+XRB41ejDTc&/#;Mjb3A72VGC
%`mOCbq7sN*<hL\V%DIsT5=%FD=S#W&kKQ\c#8uWM[FuPQ(>OqMN/FiUi--3m5os<b3JUJ/2A\.mLmNr)ha5.7U#uiZQr`J129&kX
%UB+#"?trE$7XIT[1]9MIBfcm0P,)WkS76D7"JDgZa?-VtWH'NV>mG1U(&^je-9*O-Od.4s,_J+oaL5TH1Al1P2'Sc,5-pP#BO*b%
%>aGqEYff>h1Dmu5oEN]N(2ol8;%e/)K5))4@_-<eW\0lp%M4Vu0uemu!=*c^,MKDSHbe/jEZKt=\Ou3e$O.-iELG>\35L6Oaq8K=
%SNT,NRlCS"H-(#]EG)r7W_Q)UirKr`W_#@6Z/N3jnJ$3n&'u>Q!BjChAs^+]S$V+-C:e$K$"m"=9K<CP\4jHb5.8aLBN05ZK8&ia
%"cF:ZSI5ObZ:>;QMdmB)[U+fZn2b!,^as%[ZtRY3d)!#4YX+0fO<I=lMRNQo#$TDAR=I37o8VZuh@L+$f$maA6!M<?DcH9.YW&7a
%HdtL&#@1C]=_0_//9]hLY=Fqu+m)F[_Kf]doSfQbEK:lqRRM)C!UFIB8DiH-W'Jpn(/ZZ^-.#.#J=-[:'/"KZ!HnuUOU&5^F6Glj
%-4G/u*R^0^_KX0@YJb5;9np(F'##+kYt60-SaOiK:2+35Og^jJVl>2(lNq[q[nL7h'V\XKUC!5Cp$B\i$I0pM'-gIm/M'Za,1(o:
%]%QO^LG,5T+bYZu^bhWiB-s*)`M`=K*;BhE\r:P-T1TQM6DrcO_4ftImP?$j%->[#9V2k/9eA_e-nkrrX\V'@"%0J/P3t"oodICP
%%<!N/@DHR%'lthd>p_TV+Y3nMLf8g-<sj3k%U.Uf*.Y1_.ZK%h@?Q?fgT&eBc^Ztg*E3a@86-L`LbZ`nW>2`6;oUu1R&'E5/Mp!=
%S[:6!5D5YF#'ds;KJ>17Tk6S!i_EpD*>@ZpA"Qr5Jq5Z)QhA^!'JX$NN]'"I5Y<2o`PIPn9&,&l)l7?^k"h%hCr6NI++AO1a+VJ/
%eC"XPJ-JdV[6hfdE'Q=h>6iG=5I'Yl'j+T,18:XdV`]Sr&\Z(b)@bT6<PtM+E+eYpbm<hq5i;_A11m7t3b09E;QG[(8%&k^BPApQ
%`),S3WB"3`bAFVt^.!ucVag-mB3*3KK-sd>-P=041TKII0g&c6%]-tTO_/LeG2o^+&WY2F$l,c,&^FkbOg?D6G90,)jNSU/I1_SI
%alLE18)N0[i%YU=$coZ'do]iZb9l];.Hl$/P.2]q^'Kub7t%%7&hAr^GS+R,1$]6&h87.A!s-q[ou+(fX-lg"-BXVHq#7)01_R=3
%R9Vm9(rf8Z`Zub&<AZ[&H\dm#8A2r09]jMr#M8jg$L=oHW[(2EKpK>I`3mq7@"GHh&n&$_E5)aG`</"HG@-7?I7!-bCt$[T>6c_b
%_@('*(mQ:Ymj%OB&fb(A+g[U4cnH':Y3;0'R:C"bZGR\2dVbBG%O!%V-&WdA(9F"/S[Af/8>L/>8`)W6*C5TP,gen:jDXa%)]b)3
%Q"hVN+f2u_QrOrK(4Ft6&CB\dF,c\0.%=Z>bF=&q_`$.,4We9/D+fSE!.>8e[<D.hW2Id!(m4FVQe%<5*=0A!6Qm;lc'`-QP]'Z-
%,2>4R%Sine7O8b&-"?KX^a2F&)gYV'-KA*CilD\B!\i5@7jXX\;i>5ABr%[#:e]G8J>lmhMBBk,Pe72X>GbVr/Z4l_/YU=J`lkSo
%?rV]WEXW#.)WTE";57BK=gWqu""p>#WG8_dfKAsCLctss,XjIoN>!AB7cQ,]#3oSg;q*#=F+YKqc&Y12R,C_6\s7:N+FE2nI+McC
%G6T[0NROH9_c9i\eo=Cf$&EKYL"7n#?-XB\`9%tF+b!t5o6!I>%=P(9T@LZJ`\prE#MXaJ2DcD')"=KY#0,3cp<[hMbAf8aO3'(`
%CJZnU@qm>YaKo$WAuf0DEf?%h'!11%1WFKl=KZ@V+[4CqPbZ3.(U6SB#p>T<`Jp`t<A;l6fgq*<7]Is0oMa,3,q$"WG0i<+U"EXt
%MR/DoZ!,Ca:]g^sC"Q;8?D[Loi%5&!K,Ot"_%bPgeQ^aE1@_PG:2#4f1/&:F?l5&5Cuk[+1BNPAmJB<DGiM<eF?]ULIqf5p2m,uN
%\Mge&<$kQIZ.DIl9IA8r%A&q=0s1g_DFFmOB4b?Q:05XP.](asHdTYc]r@//U$JRE;"QEd"W%q[4YWF@f6fmqhHmj4C1hZu]Lm7M
%!?u9_5Qs1S&Q?_[4Mq[b.Zi(]NO4:+8A2#-HXE?>kR*sk>j8QDdXq^Q&09K]<0O=$Jp8/(nXU<0/0N66bgPlD=VWsb2F+#<OS1Mh
%3RX/UV<!CO`_"afS.Z"<XFsnn.TJ3KM:O94ZYaSprh<P2HF$&[J1:N(KD;)bP\pCrmKTC0pMAP>jEoV0K'+_aDh^Gg;-oHGXj6Wl
%%WG<YY(3;Z$+uQFW^Ig".AA3NVJ40jebuKoR=.[ds/BI866BD=4aS?H,SCi1Y]=3pl7S$+!MUeE]*Pp"Ia3gd?kS.1!24<dQVp,7
%JpYh[(T3ob_-0S;7H'*U4gO?m'<-`oN]htb\XJDP=$muSU/X3_r`N+hVe]C5R50GOXoQ98(?o#RW>LHAngGi%f?":o*E;+R.^06l
%ErdTo5`Z9n0&RR#[\`\Zc'D8PT#W]-$k/hT'YATpeOSi-_H0-L0ei_$^ri9'b"eS?[2pHG<Jake+qkN\S"]/rJ.hnbi4,G++:Ujq
%bP@VCpd&%//)HrGMG2X(.i'hpke<\sN]0?G2X">jME)FU'94IC&3NM?ZW%#-<6ltSaE6a6C[VOK!!Fg`^_?Ag`mjW\n)A,kWBnsL
%&oO@T8m6BQ1$SHLTRtL6^07=<(YfZM3ae4SP]j$J1UUefa3JR=$:(-BXCWRK%.lHFbNK[S!;,trH82'rFE*A4g,ktsKhg2spfT/`
%d":+Z4mMAfW7:arUU2AeYa[4u\@u!?o("L!X/r7t*i63"W5/]IO(5#%Qjufu=H//aGi&ci9;<e(&eW:r_lno6Ws43&RdjWc'%+;K
%6+f*B<:_f[=mGNg4hq)+FVlZ#oEm"_mY6N3X=?j[,"GUg07mC#o<K)2;JLYD^J>'O36<0*?=i?(1-NFO,Yb5+!KC*<@&0H1Z:-HX
%`M!=\>9kC&@lAY'+%+':*cIGR34'8bFX$[4I[,5+I%g`sTCF4=@`>))),r?P0o'.X:fnBGN$@>3'RV'T`I$s>S)1FO4O7utqJJ/A
%RAP=Nm4AS&<,-uUU)*X$"'hk^Up7YPU9o`8QRl_pHGK=cW0)r4&\YS9YmmSgcE!P$eR`IT;I9qT-S8auTBZFHA[l7Z$Fp)K:QJ$_
%q2,afc.H33QqT[q7TYAfJ843J8^9#/.?<Fj$9^i5m1EUDFh\9Vi7,25(mtD[/8'pogEGOE%^oic5WP=FU^KhN=GR[\#Xh+K$j0Fo
%k@U*e=PsTnQtQZ1#;qmg_cq)b-Pp&;cuc3)N.RL^Ul\EUcDd!,SpGA@(?!n]"YV$"2bJ>-Sre\F`F0><13TTEB6Cs-nslW322*0M
%"QMAbCl>+-J,rmA0g[D+a0b%Eab$Gn2kht'>.?]MM@neE7OrX[#Uq%a`/:C[nAdp'bSGHI_c;6S"NWtKX&l#;!@6H&c;PWr:,?4D
%"E.98LqZPs6\+S1:s@=GUr5b9l4GdQ<+892^he"Ogk5!UQ('Uo?4NabD?W4GGt/DA<ZktFfsCgePF]AF/]NB^bg*#UH8.J.N%*0.
%Sm<>B,l4aZ.sc505p6[bU+#$:<9=ZO3`_W]-bcHFe%6AA;0)<%QTG8DM2*5LgKa3pPhm>3JjD2a/;k"d2O=H*Gs)&#_I8o[!lZX^
%+Uc5YfN46V6AQ/J%Lhtl8%/nR+5n;bI7qIWfZXbd!D^7sVgJoMlg'];MV8J*e]r>rWm!p7(*?l1)Q2lb'2'NJTJ#CUpI>nE)E\d)
%2g#\nh)+.1OH-(>+4c)PepJ`*b^2o'dl_tYLb?NFK^K+uW]?KQA?Gh9g5`4i3^RBO;oXq#qaguY`U]]/>@%Up<q.sV)d9PqT_[-B
%B5/DYc;Qbe[oKsa>N@;;YnN#&K148umffJRRLVkUCH>d=f<lGtQ9\Lp0"Q[aIW?U`@KeQMMFk1X`=9,<?RBj-f(R;H;pc()6Ls:q
%1J3")+;>J$<E/S0-#V04;fG)Vbp)kf4G%(D=VFVgbJp3:%sbH12oMK?N)cOM%7(%P>sfUjdOYP<:_=87EU1,J%9!?R8WrUZA'dad
%lCj%)82Wm_-ZpT$h5T+$S>NB7rg^6#B#LU?CD%-p\2e`^LI86o3DKfb9N(L*WkjZq6\:\GS/8_?^"(fjdd9k$5VeMG#f<Y);]`8(
%94P\4gXZ41>p])Q_1=0^CQLM(]^H/8K[d=#ah7sLONc"rN`o]o8V_Q%ip6.$i8g"(3"h_3<suf\,bgaspamK5("$RWW2AepXnkVr
%;Oc`IMuieh/GbRq%!V&NemSGg_.6gNYG)7Z!G9<GZ8i<o;'?A#K5D:I\d`otCY7?U*e(5iL_@F#9S$1bSfFY=i^o5A<5WTqha+W$
%<!@OjFHK$LG:Sjs_\2&[kSe`/Xkl!lDWpt^/DQWpJQ)[tpOObAXTCM<m^)"FEbGiJ_NFh9$#?CuU_2)8$:MO?jh3("-X%*jJ^5R8
%VIe.WCIZ7/q@k'Q8.QCj-H6CY2CpNs%r;;N#KMj^\WDlURj(2Lm7IIM3AQYWjD5BG>bA1Y<cO(_p(0nbfo4mJYcD?YWL"H:8.&N3
%KHm.\4n?^Ja)6,32!3R*6][*Vd<+5OJ=&ee%Rlq[9+PU6$#>[G"s0>QW)NGh$Dri7PS*PH6JJ"adNboL`emf))=gKCOl06fd,ZNH
%#nolb^p7/iGD.',pLlrf=.g5fAC[+E>`Vmkd5W;E5V*4?%h0BcZkoNA=osK=*RUI=@XH?lEhnhq;it=P'oP>5$8kN_E*/nahnnCp
%.Co_e:]SOK#Diqm%Rt*.GO!"qKu1;gM+(UG0q+qi&jD1YUK"-4JegTo->Rl[^01]hJmL=tK\s(!-+4R@.i$$4XP$ul06G@X+VE-b
%TV"9C2t9a";VR4?S$4/?66o&MU8mL.+/O3F7k0SuPe=t4=,MecMuq'4o7b.04@mFX$"Yb-^12&0PVXf1Y!p<`+<VbpLDrP?/=@1D
%0IjEoi?;[YJhY#KeL;#1d\\p\ePITo4]<alj:'=DfG-A%[7>)eh#KHS<r4'ZOut,35p,4tF'/aPgDNG<f<(:C_8rHW#\nms-))00
%S5fTej3mM>/X6lY>16$C*/-_cJ_I\CbE-?*9?O+h8L>C@!%#o_SpO4gkN4>DOMNM=''JhmW=2l5W1P;9IDjJPE]ZXAP+l,8Vq=Tg
%$k_a#5">iLMT4,$JPHg!?D*90.@D_"V;bF.TB7nla)[L@[Srr&.p/j.Kp4mlTY1?%fk%5R[^g,V5W,qG[3=S&o9RiNkS"O3[H:jo
%_<HL#6'Vo!$qR7:,)_m0YIY.aMI8EE6:478/<U'+@eKl.V@],HL:QImP+qZs&3W?a>goV\*H;!M*lYQ0(5Gq:K3I__3R.HC9""hp
%6d_ojZd-SS*SPEM7F5>4ML0.6*tn`5qJ!H>+'K9bnq=9A\ZFhRA#K]PR]$udmYK;c`UPC<%Q(_\<aDu0Q`m*;;]=o?*06&FC]N8$
%N_gub^&aFR'imqL`%Z"_R17n1,$p3"WGpkE=Nc\b7n^XDd-G(EI`QC=abu,VTs\_M!jAQaU'?1<OoNLW:p`nfP%b8#TprQs#LVit
%:Qk![4Zk,k59ND%VHgR*;u59/-HYb/Zs"*#'a#.B9BaI]WA@W_$<gTX#Yd!D8-b%gIR#AK_Cnh^<^MKk%B%_qKTp4kg#/R7A_*16
%A@ETo6"5t%[?*'SRk%Z@gPPs&+Ko6ETl%6mBFdDQ!_bNYf@c1=P(l/npb<VPoh^U8B`8JR2)d!\LeIi*$<_D@XTJafB%EeK/Vqgu
%PAgh9?7;hF-;P@$91^=8<ZP9\4$g_m-KQtgA6S]J>@lkKWV!tEl%%se<b$?h`\>CJA2bThVM,MtA1_BjYmQ377R<%E#JJ-DAufFm
%q`5ioj!8"WNSfN5^hh*&_+I+l\WYChK7]/gEAh56c1m$aeG'@PoRo@c^T]9ZS:[p=P<"UPW"GC_FD2JSaX?"`Z=RJr%Onj5Lm&:!
%6&tb3!uHh[j-6GW_036s(1FZ[?,MblW6]>n#M:9\&9rR:jC8rG-_qCOU5.b@E8NiTd]P=9NLJ+sgSt?0RAq3?!>9;S8@T_R2kW`B
%>hfNe?oI\KR$\UA,e=^=/#\h=XB)&_Qp20_%J5h1A0!$J<p'qEdKMT3!A.&#O+N)2"g$j7;eA@9%)EJW]bT5>:!&](dj8qS7\D/4
%<+JX/F;e1H\S0Kb+MbjI8kNq%qT[AV3:K99PKPec%!]^*;2e]!g@bd("apSk,=lNCDWqF?ig!39rWEoo,"`G!XrVF7.Z?T_F;TaE
%=R@up8uP?u<pmHAJ>TTF4R*(6n[Q0>d8?r`Pg*^>BUi+-j@=?Q:JfP'-f?AXI^ZVu2L;g#d9>RsbpHKO<rl#tYXTYN15\tA6c>;m
%D+_+/"aKFT,ET`%/^d>0W'`;:<jbsdM5+8/S3iFH,_Tq]NCT7X`37%.D-"7r:MLCH[&EK=J>rB&PX/>s$$RMj]emW(Dhd=4!Q78I
%5(b'F)3llV_9u-pM[OSn\s&:M>Rn#6aEn)e-;fWN'brB9%C@f+d1l;cbuJQc7]]"K01r.POcA"p+q%,R.ue$V:7be^;k+WO64->/
%dN#Hao>$=PNVS`P+[p'XHA_O?!+k(j1W34[8m,mT%9G*B`*)nA?AO=qVD;j:U[4kD3#A+l#KmY9Z],jD@7*J&B3AchI[>9DHK\nt
%lX&P`89`MM9uS_5_Eo'Q%Y=RU^S`$+S=$@B!9_S2CO7Ulm@-M.[$E,F9Z,ur(3c)u%AmD==E-59?I=1XR.4):AmDi@fX4W3R=ANb
%T_S)>.c0Fqj#7J.UK*FVrbu]e7RDg`!Udek'=JAD$kbY/,.dq4#=%5u,WYChNp#4V+mR'G%i%OV;_O4o*fOqEgO[+YF^^?;m[]c>
%'ou*L60kH^%^=0;WdDt86Q'.(fhs8.*CH(FUeul9&JHn/$#eKgI#cPUoGD7O$5k3Z'ZR!mkoi;P7=Lu>>"O[MCDTh7c_@+9_<s/G
%?(-^>;\T$B.p`"!q&+K6+Y7>ZWUtr![kS'!>f!KDN1gVnL@q[BIHl4f<c3kB&F^'l:4I:+2f:bTmFBhM`%ugYoR`D0QK?7A4`$=:
%$rI@-_EQ6/>gZ7A?LeZXnX.PZV55os6!dF<eP__('I)8.4GJgM.m-?hC`ZTm6Sf65!.t%e-:j+R6+!C@1HW>lb%k18#&/m$\8LBd
%;Qt_+"tMY;9u8.+&`0S/$bZ#DPJ\)j`bO&6"s%E$?!*s:Q(;p_qYukY#22<Y?6CjH;\W#%7IE2c:As/C_7iX)perHn=qO:d]BXqU
%;k`SU*K&TTjI3Ob/.PskG%=1/E)$R>UQKYdE-O6;OJ\*OI@B+$%^37i.*KY6QtjqeL43<l%/&+mogjBbK6TuNYT=@JaT8'4TYe/m
%U0!fhg^0;I=4a-5-4`UDNdP]!]HJoA$[6hN,mOD_c&f'<,-Ut%d\QXQDrtRP(IOC8R\+,@3'e+7"J9SB-^<:%+;-1K,WYK/5NO0.
%cQ)nP0>eNBa%c!C0BP%#X_K5WHd`!W68GR0s4p/[07fUP)]mD:#\F-\X"Y4Z/M3Kn<X7pk'Ccjsac58t#ZG,f\j+)%Akb'#O!!%h
%<+HWH\[XdBVZBjm[lU9PcQf63Hu<@>b'5MoYlZD4?2/Jg9pZh])B7,h7aZ?s@!16;lenmE&bg*C=Gm58d6bo+[AO$a.4a,Moq8jg
%fV;BlUD8)E#0]D-`a(.?Ff^4blH=,W)9P.Ir$YIa_)%s-Gs(0q:<$,BkNK`B**97La%N,A"Pj&El9GgC'?2>Z61ah3$hZggpd6tK
%SN>@AT&9U*DGN`h;Vs==!RPh.S.r$#>M<!*C;cj-:FE&7knS#NOOK-)IU.>4H;hHcbE'"s;SqosE(Z&Ec/h1o4<FU'gSgo)&lKc8
%5S+"=;2N.>9oRWSTE>_2UETcO7>D@N'@>bR1Cm]n?9;*BkbsjlK#,QlYu.1!V9,H0Jc05lYgg"[;_.uMon.57!,iXBPu(PZe2:J]
%=ag1pK7e0%TaV.VS:F)l!"T\e3>pR0I'[JfMO/s,4X8l:%#5sf@6b?XA<r2+KE8G;!YN<q:D/(D1a&*F@-NUp!1AAAjuQ1`/E`U4
%h%;/"&j9paNJNf1M?.G4nn99:Tl[;l2FbnIpAg6]Q[-J0,*H]j!9M3=g`J,BP,;hgf8/mmQBI4s?Cd)K*MM,\mP!$6JtB@W+Thd8
%6i\[e2h0,ICdsC"4]V!p!$XL^4.V36]HX)\<_%:=VAHahL.,6Aedr^5ODN<<p(<$!`ZhaVMP3lRh8(_HThITL2IhV1j)8'C^XZFs
%goq&3QeF0^B]4Bq!62$-:!JL#EVc,iEYFhIL(LD9--NhAQB810kI*DlH]RZ)3[j-;6foZ]A[aY3AFL6h[#T[G;8Hj2:E*.@]Y;N6
%_LSFmb\ZA#6pFDkH\all'rFD1GhsU5&h'5:@)ooB_89/7]T#<Ipodh[(q!/4)`U-"$N<ra5>DqKHo!?c8709bE0f&52Zb;;R3TC5
%kno(U&N%2f#Hk'/oPO<q\e!"6Q96^tLZ76HjH0jHJER1.A!USGaJjX.-hH#U_V,#\V+HE[apfMDJiuJIP>9'+2Y%0"C^HdrBnUo_
%du7ESH'Y_H&75`e7q;&@\o*gUVH;gd)(\Je!#"stOB<_?.S.L<Fc[eFGbXaS,2+f&gCa:JK>>Ke?+Dbn!_&<4*Y")Dm#)t:N_(@j
%ctu%L%=GM-?>`-\$H!j\U+6CPR](*)PEs54Rcah0##=T:Q`\>E0So%Q`[Gg<ltWqm(o!M:i.*I\J==P+`rmZfglWdB-$6V4WKp9^
%AL1q"OVlsinPk;HGUQcCLU6bEK&8&>>'$uoA&u]3,\f*\Y;=2T$m(.Z::9XLR8nj(7chX9%@oe.2p2>s4]=&sQ4./ucY,-[1mm>s
%&s`u8X-nGCdKN@T+?LLpcHX4<W9)C]BJ1RTaDt,EFq^`D;r`-([SsSKjrN!n)?6gA#4dR^Y#^gdRCY]ZEJc<u,`G*I>)K1OQ19[%
%'c=ArQl^tnhRFDUnY:dG(&aejQ=md<O_F$ri0[\AQU9oJJM)7NR\:'%-=k(6NQP?i2[_O<$99UD-XI67I:OrjW\$D#=:S%5l8p/$
%7[OOij<?t@Ng:?aWj>A02Zf.,9FN^BkC9OfNA5+SDfuY;i.3qO055D+9C<8HZd6]J]FKA@$k,Z?YXBN);n90a$uM_!@LR3-PprNY
%))bb>(u+E!POk([k(_;dD9bd"]&hOb_+C%!4K;+r7"lJ2"k))L1[_N3E+I8@LYRKF;['O0AQ)%]DH!2Db>a@(dSP:LUg>062F6ep
%*dl"9B+3@%^m\7q6jXGbH>SJ-f`6>en3teV1clm=ZXWW*iNWi.Io>jqd.ARGL=+PPU95@@G-NMkIAgVM:9_S@BFbl^9DB&D:g!4o
%GL15%KPOW5Xpnpd7s_E88XtE5.;AeA2<_r:J9"9=aqjYI_8f@*5+`>XPBa,ofn$=U[;/Zd9[#K_Bp?'%nC*8M0h2Tk-JuXI1_K8g
%;il`[0:WhA?F8kAb30[nI;UZmZ0Y5>.%oZ4#Ts*WVI8[9Ktt99N]A[DkU]\jJePLt2\coZ-eDU?C/iRE!rXO,&_?5Uhr'e=1qqqf
%TireZ)Ma,'/5NMhHo9*!/&/G7J6;nPC@$QJJK,R1(Vj/b+U.$eXSG-@k;Z_1E#"1mj)?M2i#udiPVi'Kc^=Dk&ZQn<NU"nN-#[Mg
%%C*\nFe=j(7LoN?]e(.:\aX4a5teZT?Mk>bK@VNIcQTYR=^nE:\e,T;KLf/&+8+P;7pUpc\BZ=<a<f=rcYe>?+:ke71/;1G%DF!%
%&Q)18A;r7H%YD]_4$\KU0u@(m)=C2jB)=E4)PV!b8t'!*22X0a$k01'N#?r1a'm;XC;a`$F7au>TAaab$=jEl6*(gO3]#OYn,[L0
%F[]ePNAe.P37=$-G3j"CT=9,l/u,p+WA/9oG3#KQ\^Ze:\E$M@GWNWK,>:*h77mL4.&XqiM@@pAknutm;:2V48`4q^m`'>mK"2SH
%g`TiHGT1NX#(\j#:c+P2-1_gRKe59Q!u)%'lfRr2lRKFS%qnC_PNEI!k-EMdo2;).C]&q[J=,IJ*lh!+:T/3uSAh-\H'PLejcD!J
%@DBRRO&QLn@:1peF!uts6P3Zn*O^trX=%>gbubT#VHP[DqJpHFN1/d_c=`k[JjtE:qL[Ucf8#iU@]^DOl:4.1M3n^^38-fL4>Ma_
%q&fW#gp2U;d1J(N^V'gl?tX<\_Fb93R[,:3PRoQlZ".NO*RU(M-gE9-O:PD9/o%E@j"Zp4SiSTcM?GRO?+-+Q#,B1'U<6kLSI>XQ
%dNKT*#^/<pVfKr`#lj+@VpeHVb[-rt&4',*JBQna&n3`99,ZA4KC*YplNql&6=)@P=\7`i-GrKl9)4!Qfs?5PPU)?oM2d+e5uY61
%-o:pJ/sQ1C@JnLo+qQ2;Cg-J2j9_8(<[0e1LO$@lV9!Z-UMUgOR%eO5>AO:<7Bp,8I@,^k[Q&]4J(pN/Q=N8B>'rgQ\UdS)I01C'
%Ff=GaU;_>LMJJA6E5Zr'.p!Q<5[GhJEC2=+0EdT2):-S/BA4T>0oa=T$7.7IM=l7(L.3cB8GI('-7:4:J(1aj710;e>9na`)K]%H
%&&f\qTW3o),N;a%g]@C6<SM5X,,k+?$tr#`?sLNcOPeX,.'r]efM8>QB5o:TFEmLhfI<QY3IhVX;V]EFFc63;&Z*IW`g2$0g]$oL
%FV_hni5l$BJlR%iU0=-N1U_<$.)!"*@F1G`^MXZY'$YHM'H"\R0T5#PDT9O-+G'(QCpl%[d6^9gO^]A.mth4OdqALpTAU&,qG2K5
%5OaNmkn!WYf-q$#,c>GR1tDtind<`[BFiq'=b:4=51.](K"sM$&7*<_)KpNE/k>8$#6\.aZT&d3$)r*($@:(h\86#^bJH1!CX?k4
%WF_/r1XVJ"7<Bk0"\%t`f9a`SVpG!"VOK-/-OQsO[2Dsr!<['5KT)QYCnn9K6/$\D'MUP2:\TqZikd1a4u%es11V+b=;V.:>T:@B
%6UH'o2C$ill@1bF*_hJFhZf5H*O&_Gf*X+@,R8h>K-@<6*Km]e7cu<1!r2AJr=Hkjj3.Lpb\t@,7;35iI*_;\##Ihe8g;,0k_cX8
%28=(Z%-iQIW%<r:#pJ0%B38u^BoI2MG]m^=8KB0GC9X3@%6>7!"oo;lWlD7jW]Dru9uMWKdKnUYHgIjL\gXdcpiFe6;fXe#N54$O
%m"B!;i.1p?-CGqjH]h?5X#0NXJ`iC3%Ip<Qe)WR"L6W2L@R."Pj"I@Q4[Rh1U/bYr;oR#`4j8d@;d1XC(p<b:3!.(;PV>Uk&V,MP
%9Q7*o6UOCJ<@_1qlJkHpq*"$@)K[+H#A!RKSOg^tT?;'geBeU[Aeh^j>/&a]fgU!?+XS;&SY"Y17(Z=!<fIdEa@P`0Q8Et+#)2-M
%$'LAH3M#j[#44*OI0)"ifO(\*eU[0U_uL/pl9Y_.2gg57kG6qfl0[WLU"mbGh`hsnq6W**,KucSPI<E/3[3]SK-(s3;hbNd3>EP2
%DfJn/\6:;KMJg)e^tB/$?a;\Bfat5VO%BdqAdFDt`la6M%_8]UjCpZ7>?7^T:,i]XNYrt)W(=-"eIcAdSf#@)eqF(NOInBS\Z.&Y
%Z:\/!M83)tN,k6bJCCEC3M!TJ$T6C),J1L'N@R7B.96ZjWVC1l+ti@-j=!(]aV[X%NYE*c!$Vb?8Ij_p&OKqDeUYZajfD7YC75]n
%K<d=pk:eRHK>)4$[)sT?SM;^'!#:#\"@e_D2S:-^BrH#0&0PJ/RU)<i,5TF7]n)jad5PAu9s;eC<WE`KjkuC@MWq;.!sWX;&`[Q;
%L;mE*3ib"%aWZV>ldNdT6SX;sS$0t>VeGN-,r:ho$^@]R\RP,[-#jF)C!4+O_ndJ7*;(>_rZ.G>D]gnHaDGKV"@!stS,iilq&i-m
%99=a&.SM^S\@k;L/TaHL21"UsS-Df`k$tQ[g&06]h`I6\1*0\<FT%n/0$el5HV:H`Wc<bj[/*YGj.kK#'YHj#>_AVVbgs^3a2lkY
%GdN,HRn?DS(e%cas(!;CS>btRs67W;N57BIb#9mp!&G!q3iYhUUZj=,;+I#5jS=;"+5;9;kQt-PI"!(^,m\:(q@':_<4lf/V&D<#
%lJ)bV:HX'&qf22.G^@pj$T:"u#\8Ss&^_OY>$!D;.QPeri*B)oFTScfS*<uO_$YuJNA6gK::!5pndp3!T"Di94Y^K:9urc9b14l`
%3*)\K*8CR>%RQaZJckLnL^k02X49=B\tmfPLe&+%AuPX-Rh75UNOT#alpJ&cW)4Q/VkBf.75SX!G63af9?IeX'u?%`PT"4'860@^
%8NCr;C;N&(!`EZeLmgEJ6qafq^^`m;0dJ%*SL"`e'V]n#Gnhn+Np&d')Bt($p^f#C,V#0<dF:=Eiah[+HEm%U!q\r=<(4)rk<o^^
%gkGu&lTh"R/9tH0jU+A`An&_b?otpli([fait`;\$&o,%UAdiRd/:DUQGj4F%\D$1G]L426%,;3J1NM`=(YlaXH$>'$0F5U&W'U=
%R):9s4/P;O$EXPe*o][4iiK1COFm9-KfE0<$f#n+:uYVYim]TN.U@V7E0+ke`DlJ$-E:l"jpP7-j!MhPC@!W6NI4q%[-=\i#]:h5
%;ZXmZ]-m[ApCC0%maUG5;31iGE/<066-Pd59gl*#&uM*B4u=drXGE9:oVG79%jD5%Ym8]E9GXQLb)?`\7_f8*!/Q(Jl5MF[6&ZI=
%nlA;<kXEg*6jCW9gQMH&XH'_#\Z2$%jT.(NUXaEYL3OBlA>+\c&bQ@/WdqGrVDm#p;^<dV+Mbjs"\4rIXY?A;La<L)OVs=&>p65K
%R:`<KjlfF`LhpkS%O_P_G(*.`+ek\gUk+U?]eQlNA`XJ/]U28:38GZ4bn-f&FPb+'VPmFh.F?I:70b2^VWa+]c/nL_*^0Cd:tRhM
%RI!jXlQUlP6r`A-)K?\iV,IFSN9JrL9IDOR.06`u6Hj'9e=(`+g4RoZ2&d9;Qa'1a%u'3rU<&peb'tlp!P)+&ZF)_V46E1`Wmsh_
%IY<;lJ5K%&#?IVNW:FBV&?&o0RSWX0pEV7:NB9DlX.\s(<N*DtdXm='O'g%Fj.k7V26?=0=5[T>O?=^f4$`rb;]<QSKW#aU29]ES
%m]h3CmXZA7+L!?In)Z%!)![%"T^P:eN=6"Po!m_b,$ndf@3Q3TX6"Jo*\r_(UrEgQXaD[",o-Z@#nqnMomdt)6%Dd,N@mL7.@<Ie
%"Q[iU771*bJ`Y2dInpJuaGBqbh=U!.4sgAg5$0(9#^.PI"m\9$"MAA&!kF]8/)3IoL8fgP^sC%G4.4*i>`G9!'_u9lmdafhe9T+U
%$3[8Cs7D_.B(:0)Q#`%Maj7a7#m-Kl+soX('WN]<)Dcg`-K,gJK`Ql?=<K4]&C=;j:CLXFP1:]5RE^bsWCJ)W]UJ$f$4OZ^VpG!F
%duRMSA^\p:S8k8^#h)Mckn`9Q+"41*&f+3[B$u/Zj#n:RYQV!MG?^X2d*]a`1!J8+ko!)LMFu)H@dqjFmpuVk'"uR&%kd4:;1<!&
%#\$-nSMnK]_7,$@AlQ^^7U[i;Y?JJ"_Jr@>WTcgXgqdQ$Iq+bamVC%$YVWLUj!aO>9i-l'!f,coc5+OQ;'@Gm]cNoGY4<LgAL$@B
%oigsSM$RNOC9e;t&sP=Wn;,q($eBGVlV,`7_^'s;M56"L&8sC@4n"6n9u5(lWU\ITV?3pU^`ANCdN]5e6$QnG+C0\kf++8%ldMra
%`?pTeH(?6jLPU"'mae6[.tMh"hU:df1o.,s9n>C6p'$B5-!6!??LX@rKbUsQ(=Bs0H)u`WdDo:sf$M<TA0E9j2Nh;X\7[CAJ]Odj
%QRjgHRNWn%Ms,T[QK`WWj;2r,dV7/=UQd$*lcI=08"Y$)e4VYO9(\("ll>2-C@mC(o2%u6-nrKmM+@?_K:^+RZ`+FKnh3QlFrZSE
%>VEKNEP`Q\mdrl!e;/\?m-mMR3)FiJMJ*UK%VcglE<!iVNUO0/N2+D/:tMB7!ktHmN+aI$7q5Ru'%;<8^_H[C[hp0gDC/Bs7'Qa3
%PR<jgMhdWa'Tf1(2(Wt;f2Ug&8Ga.L(8n5lS+WPUNELU"m[";N=mO\4__.3$=:F1(9s>))0TRUYR6U57/,KIPjlY]PJ4-60AaYp$
%n4ID;66\m_Gpg;iag(/6!j$Z=CE6]5N\lr.=X2nIb]$8kICAM+;DPcSaYOs`o@HYY<b>':,!*YeB;NeCmRcV=c6h,EiPs<&Js8M!
%i5mgA#A\3eaf7NoTFN]]60X0!8RG/9^*fK2\_0*Qa`!i!+aI2t&m[k+oNGEADb-$jjNJLOZT!1faP8UP:4:prpi*]WOD,KiU75V$
%Z4uq?,&G,(MpY6_5.7/S?B#:``#7[Y%GaVa-q*'Y6Pok?D2$KHW=f\_%6>s\'Y&/IMd=LW(.2@QbCdh(QPga$kZus>hI&95<dUh<
%DT2g]YEC8L.]+_6<^CRfmQkoXMgD1TVDuZ9Ar"e!cWTfP)BdLd=js=C6g5iX2qGfa@R@CKU5$0j<-``G(9*Uc#;`628Kc`N4Td84
%!V.4_6&0*rGT^d!9Y*5Q%!PC/ptR?(f/g\mWoWO1Y3XK7L7A5<.#_*abgY65o<C)V,gs.>4<Bo;Zt,67!FaNI'%sBEQ<92`j_KK;
%PJlB;pANAE`o+/q6.u9PdLjuL3qDftfMH#mL5VG<4H^t._GA%ZV?1D47sbSh6qL,_mH$V^RNs2F"X@oFC`9[@JP]RS5P-s8:[lGS
%Atl._N3&b5:,gb%"3/q9jH@Uo?qilnB-PcpJ->c11mD<C)\k>Z9[*:%^(HE\0V4)6*hFd!A(O1/X0+p%nPc4fnn8eSH3"*F.pS\B
%"?lAW3SY[D3-iK9D`o.o%?FuV<?+Z+c75c0S4(miCmo1#5X'j5FL'0-SkY(6I%oZkc%<T"MY:EL9752R8A-9r.8>E#3O6"2Yp)%1
%bPeBEJoD&@[aNZkolpW&oADP([_ZMa2$h!4ihs[h'==66iH,.>Q]A4H)-S2)kj'\=i3u##4,j`9)l*h?N^=sP7T.ZubH]:RhgbKd
%Ie6G9I.YthHLK`Uqu#(5lX/M]ro3A%gM_RkG(5!]GGEY@0)fs<ZUt1pJ%rNS]=rN<`8m\II[&VPs3r';:'iX^IK.cg]V@^hOlQIh
%o`P/rs1/(#T*t&`UNH-tU]9q8h9kQM(LR,LR:pZ8M,e']9*9u``>:tA88XMU5M_tcS@;*3nEED,cd=]J\Y3I1DY^Kt2%+8H;B=@&
%?=".7C+=mK8:L<;n6l['WW]"cE2oMXFQa5j'7G!m.82j`>"F%<32;2VC3aW`7Z*B!2<`)g(5ehhmbpJXSWc<c(?(.VKKMu1.RTq_
%;d6+uh%HP=-_2j-k/9fp&>LLQ*nTg.9e9<?(-D<%LJGfs@Sf*I/r@(X)oBp)Lbr-&^+;Z(I)n_$Bg.k).0%@=4Z-\,$aGVHSV`L,
%D[V]e&kHhLU?+.M@nnF]3nHsS*iRQ9($:-?Sn^5>EKOKhISXl.;4!E`OE@^s,.\<lTUO^_SlVo)%n3%FqYPtAO!)oJedrKT<F[1a
%&Y,)Q6PNuVs2d9I=?&35QP,3IbgXS:.8_Q_1N?iKr2hAMcdi3=E\Lc?;'pHaQC,aII:R*9TA3?9RC%`!1,76N9FX-]%Oi(%oRaMn
%k^h'3ot[.>VYem43KEK0L]@EbihGg_+U1O`ZlYOH<k96B@k6>$FHYB&HfgmcL!$68R_4tA35MR1ipn49,t;0q-IpIs8G7MuTd2h"
%`T]J07jh.n1dt3(k+;6.l648T7OpVUhJOf8c`Z6r(7oQDUpu(!(<I4A;:Cuu*"B7$.5*;H%pmHn9U*\n9T2pSObo?VFr`qPc=BfK
%UAaXDE+t#X*`A8-7BUsN_-,0R["fhALY!^3/>$Ek`?lNLRPki.U."6#UZaH"Th9Wu*+2gG\D'o+?mSH&S;^lZOX<$.&KfFBeEb(q
%_;$8[/b)LiZ*u.;"ppo1,"IWa?l]Tn5r1ZsLEM[RqRIHi/a4NC0NK1##I4f^PVmq.`1j`1*=BC?<L?5g(hFWgQZ?Lq3#iZpks68D
%e?GE+6m]/-bt\tB'_la4lGl<$Z,u"4m^39iq@,55enT:8?7I-R?#hTcQnsQ#[Z5nfj5\+do>FNGF324Z.SReLgYgKuGgF<%&I>h3
%Na>#K04ukHj-q`S/ie)q7.a2&qQ,\j.5+-S;NY+<XZ,W5`'uq9Y.::FWb;e4kYeKq'7/.7LM/=EQUiB"Od&P-6R^SYU3k*m@H:!!
%j+%[q):=`sdpk%P#aEM>[BYBG/jt:-A@\J6JR4)]*FJQhUj0'9&ET5#%WOT_',f4fM#)X`f<GKA@cSrd*lrcWP[5<DqFjR<L+8Y$
%&=YfC7.`3o*ce>gkc)(pNIqbq"(t.;d?FGtM-L8T*':uK<Be'`?5)$N_To/QO*NQ+HO?.PYRaQ+F;NsT^G\t,KY(gb:uP2QT#'>2
%6:>1G##7sCk9)oLJW8.[e(B@VPdF*bDbUU_RL$p\lW_:"Uh#,2V99qo*sNCh0U05qc>Fq`1Z:3P25;m.(4!_aro.l\Y<>u..QL<$
%mSg'C0Hr\A;8iqEQ'<L[a^<hdF&d&m]X(:<EMAIL>)Jug39ZY4Nfcm`<sA.heBp$e6HZ]MIPHpebG7seW/SJ.M@[QY#T
%A!\h^CT.IjL#!*[2bOjHh24nIT$#Ih0#/\.ekNu>+sjOBX%9=ep5n?!1;-M-Qk6M47CHORS2'C+9_7jaGFNFfHIeR&4o+'((^N;B
%r$?nbXSX"8L%-hW$@1V%7-qn\Pm<(Jh>q3DbfQpkQ3u_c@bHZ-d*&3"\Jkp=G[pt4($e/lV1c"8+;8V![_i!bcC;Y+W1j4)$&4E.
%%&[a,6AW3J==TpsYqF4mHtX*'7Lq)m'rm-M_8O&9<"Fdj5Y[I[E$D%nA#*pFO<nBY>h1jGW&PErkh.FERiE=1h-e?Wj=4/]W/&<<
%RE&<a`(aO!Q<(YV)u!60I_pr#<9'h?k;Z"aLi0dg!=3O_>F_2r0@9kWjjI9^84B%;3nhf)7d5!TUlnjlOI#(=3(Ks,6punU%OUDK
%WWQbcPK6>O?>ZutbNp_$bVGMrO8VBR2?p>Da!,Q#:n.[U/<E_g)/<.\6^Kq]<BMRU@i6,o9CO9I5c`.=\I)XI/9N;*#mCWR>lMm7
%:!'_S?%@#l=]=^]93).EK<($SkIe#u7a%!NPZCHf#m4TRT1p6;c_#:T?;YA;XWRRZ@9n8TOqY;Cs2E8m*<U?t2BkOn1pX\,^+A?_
%$1_Db76&A%X&P+@*f+H`W+&)O2?mc&]TQO3=.d;UQ2qHAeMA)964ao]'t-k^#'0WVOjLXK;0,FgB,O"U5i-po(hhdLYm>I%l,I_9
%pT`?W<(uJN&aUj(W:6:sL+grX8/M\]=kFuG*/^O-eZ\V,kSQkQi,cbHk2^fgoRMh96S)%D$*@,Scu"*$<cU3,HD2O*Ot'YA%W(*S
%6?G->36MfBLu1b+FHkTl[!h+KlQI$$&kQUB8\@(36d(YV])7h(P]Dn]A&4&<g9X>V'\]^6YY%&OH[!"s3n'p/WP&0aH6IHMD]WP#
%a:MR[]@k.i82bkbqP`qLbID6KQ#DeY8;>8_L&>25)DZ_DYpO:QPS&0rWP1q?9qqk_7Faus@TeBP-&>(J"W$?lF'9tZm96<_2Fdpp
%C2J26p"<[88Tn='0%-d:@Khr@N^7)C7-k!!Z&H;TB_OsiFb5&cN.sH]e6#S!q;/D]OGFJQ%e=e9ZiH.q3O2BCXB>nX=-fD6.n(I\
%Q6M2Q9K!(n)_JJ1/0+3#@phu/5.uP1ZRcTEd7$fEMSA:8:tB3@9Aq.;Q\ZnHnSKRb24Lk:DCL+=,WV\P*Qf5?qc*AV-F*VLe#6?D
%Z.-$0'btTIkGr4BZM!6`&7,[F"6ZUpa>f]2/?CF']Gq'3N8j09DF(@i"?9OQ,NiOHXG\hV^Ta!I$#88$9DYYpBeLnU4R#7q9u=#)
%=[jiEPf;k'$p65Kaini88`#)2R_e=t=jr<G@Su&R4sucb8SeE>B<JQ8-ig<\E:TrJ!'%Gt:&1/NaI-j2$^$&/4S5%qcIN._OJN:/
%Vsl,:?]q[oP!]7[UsJNjkHl3,4B3(um`e'O!kkK^:4bA;"i$'d!3Vd3#EShW-g6,l4+GPTR>]qD(nqF4')&kkja?X1Gd6D&i0@Nb
%j='Z%l84Koo"+T[HD#h#%#eX6cLK=pP$)<2Wg+lq>D5VlhhVI-NRM)PAThVSNP\gI;Mh&C(LiWl<G,03P4'[X2=O&.8#6kY&r7$W
%UFpI/UOa$%(.pk?nqQ1l$8FDe9W3ua;V:]\Ph+<*OuNr0n$Q(]"#jNr)\]mlDVR!;+`_=g*<n\\9UAqZ/7eCu)r\H_O\#&9F2:7f
%YA4ZK^G6ff"UZ>d7dP0G8^JVr,-u/.\+W)H=NF,']'iBH*>>.,E(dAd\_*:+i=,Y"]>b2Q>`)YYr"F*^&2RoLio=[Gjn1<U\Z/s"
%.f-EFVeQ`YUCs<XC9DeGKVfrjB'DTUB,"8_*P/?b;Ct-&6B$(B.=7p,V(XqcWmoA]>Mct=]I@s-Ta3k1otaTOp)`f[PF*QAb$8!X
%Rm87lM]XR'a=V"_-)F4M.g4b14rG8+@?XF[;A2qtRg[u6#k5?nUF%af-T?5.#a*)7Wrk<#/>@`t1H1A/W'nh72dto9>["_OM'')G
%`%*&_fgMk-6-bei5R%mf!b`?@[@^-2:)BWR1`7nM/o4G.(di6n9G,iof$<EMAIL>"%P+bggQ"=%`9I;Jd'/HgrGj*6`9+D]'
%+?AsohcG!eoJ!LrGU7-K=lguY/3**P/V[4H/]e+WaWAO0+Q0>;-5Y#$IC-70ibJ!'YhPDpe4SM(67:c'9^Rum.\Cc+L6LI:^4:sd
%nZ9M\"="QtUYa4-=]?]a-rMRA@(!'Dd<B#^JS*7!&O@C&AeDA1*$,lM$Z&6/%PeWbG;/iTi2bikqZLRR%:uKWdNrp$.tVT<_Qnta
%<#<=b1M%ol]*l</.HX`k2IZgU)RC\W<_@Wrfg'Y2*n71@POWB5@ERP#$+@euL;]Z0^XlLAe>=Uq_$nC+=7(Qsi5l22_"&/hb4515
%;8qK<%p%)X&`VK#7F*6\C;)qRiEMhSLEad)l!S10Ogus(?W\I0Yr,H7NpP43"(u2OC^7sT<a)dq0bM,RZ6/_A6j$%l\edFfd@Eqo
%60rWOFF*IMq@:c>&F7#LHf(Y82R=X3h>e0mHrDY1HBMuh$qN4E.iJF:b3W<J?7]$U1'Rb6B;-1GQVPhFUlmnbQ?9,N!$`O\faACe
%ikdNG@C=!*VL"Y7O_74'X/Q#N&-r5FLKh*IFZA;Wpa3t]QnFcS9]TqDE\ZO3j&D;H3bU>jS_ST:l83oWPj"i"k)a0u-LjtFd@!4f
%4>==Hb*Z.Q@$dT]/K*FZfhK6CL)n^KAPQAMX%G]h-qu$\3=PU40mF$==\LZ5dj!!cP@XG7+`a]+-@3'fZEE?*B%u[XE^e_Tr==)M
%0a]B\[9^>$O&Dr]Lu(H#a=UQi`\Y<El`6A;CJ1Zp]-Fh:+VJW)j=1ZLFCj`t,K2>j4X25[Ld-u5<]#/jl?<OJA0o!$AgA'&_GY/,
%q6glG7hoN!P9^_=IAIq@_YPJThQshmOE`567.YA):4Xl;nf3sbqPmS>c;'?hYRSK%ZGUB'(,7"J%tgI\aPGY=Ljs[o=QUSH9@&>;
%AYOYPgY84Thd,*o?f(_FpfQc#VDS=r:Ohrin+beQPCNN$5Q:%OQWHg]5Q/^=f\]4!])8b8IWPA?gXd8>pFe/Fmk8TtcL-/V^d!6j
%oM9tM^:IJHnTYr0h;pTamP!Gro?kmNL7dAZSNUe1rpAgtlar&<EMAIL>#mXP$"K)bA5]Q6=7kG*P&Ep<5Y:Rd@CEd%+N
%>CD"sqYo5^RSe/B[os!/m=)duPPOq3nk\0"YF#b;DJM:K-M,h(oD%Wh:Z7'D;tsdJ;u$:B?[mOth7%$(q[ZKao(@mN:nql%k4Y;E
%]K!\+iqD3QlXc#^gcfq0VE)=uhY$=rQhPE"9Q$Wucdtm&f6H<R]=W"<?!Z6U?[R*@jB!F.(@->u'=taQo)A4Zr&G6N:$4+;qr?eg
%`L>'.BY?8=Z\XhfT%Gu7D_HQ^GOb^i/_djkiB1egXi\sfr:&e80&F=S>4\J^iO5Ts5!51$GLsSk^H_V=WWP4>?HI#T)n:CT^d$$g
%0E1_iVD4)-rQDs@S[lE/nCHBDGB%hMIs\Y6@n+pa*ORP=hqai1r7g&\GMc_uYB[UMS,^LOIC&70i:?$@/%.I!Yk@m6EdRc@mG#h]
%Fk>^gNIlkL%hIT*IW=o?kd,mk[r[`nW,fL-Kmch[dQs/qa&(mSgmp%fiRMjb0Adh8\\1i%<`iK7]%eq%E[uCu6'j[sEX,n<^WQon
%]%fNS/sfn-0"5RNH_\b)LDQW.Xdd8slZ6E<r8lr'bAY^5hgYT846ZB8hiX7gQ/!2F:9G-_hql-I`Pl1:cY,GAPao;[[c[8-e[t!N
%WkVCEfVTAPDeJOq^:cY@q;V5'mC(f_ijc[._\$6oOX7/u_j[LfobQG^Fk:-d\_$5<2fCA;hfBb1poaXWkI-_dgq+<)b'+Zbn#jt(
%IehUaDJq.FeniRjT576W?9c2ggRG\HB-0"GnWRh7Dah%RgNRj[cH\)W4?pG]A'j%J^9)ZdO]k+\VWYEi[s.lKrO:*'e/JJ9&*r@+
%m1)8WDjgmdK&=7gm_8T>)#CCTdm]=&rm-AOj1eVG`ID`%*R:]tZ0P#q?agp=ebX.*(r*i!]X^74B,(4<V]3XBqXC%*HeqDhkJat7
%<\ra+Io8JYAFhN>J_f1NIFEI*cHEJ.*+6alnE8tn#P2P(UE.p%rZ'P6+`;3.C\H_tH#CjBn+JrHiDY6_,+CX*]=e1;qVls`6[>kA
%`g>g=s+m"jkMO:[bPU1h>s<%B3BW?!W<@^0V>"KfgGV"_2]$[f\tG>D95/&;7=1I"nOZ5jM":AHj=#M6np,+2>EA?s.qZ-Rp:s[4
%ZMQpO-b$Qd?eeplr7;:*a<>K^-0/q<="@1&n'56H^0@ofBXk/rIslTb[`4ZCLO/)U#rPpALV:gR=L0)6EZF-fP;dFK^AIWtlLn"(
%*iU/Z6OmpKkLnK\?sYK8X_^^@b^k'<IC+bG&.cB&cYD(8pBlDnrnQroA8-Fk$bjYYYcG3@f.Y,e]78_DqcATkH^`djbo00'?^UJC
%&F7(W[sr$LBO;gP4U3D1I"bGojP.#1Ru==#0<QqQfBJHZJ$Q2Op4jBSK9)Y^j.C.'Ia>u8c*,UMe;N_V>eC=sBGG17rSX&]<ZJ0R
%GsU%UAm\l6?X1>Dmcs0:iqhZsr\nKImH'PlNpr[97t:Ap5<lf!r9#!'#7YnR&FMT']09H1>N$j`$h`qW'fNJuiGh(;N8ca@g^3(I
%roSW!D7B>qWkI'XX1fp%Gq\\O<p8$0F6cn$k,#u^]NH,2o]G`0C0ed'eb1onm-=JS+.a-Y2G`Q8g"jdq.P8+e-+@>Ehb]l@2>0IQ
%\#lW7?/AhVXb\K7o]Yo(go^qoSsjP4;E)'D.rN\dq>%YgGOLAKQmXaMhU]()MIZC,%60NXg=A)]]Wq&\iT%]7nUGeY2VW.cP'7_D
%e#o(qldqKtQ[`-q2/Be2'=^A)\m;'Wh<qWapu_#<HHj(Ec*jWTSD@FVY@:m(s&:4f!PiNlm1>,['+3Ih98Z,?1RWYG?gMbXb,T=2
%oo!gG7+o%?PS[R]nGFSdU9&o]gq-[Q2*8/u:X:1"jb\CG`1m1Ck-mZ7fTTF%(Ej'dibZ4KMfSf[@St]MAm\l6(E&.jrrHS+<ZT%_
%kADq&(?PH?`M0H%beESVI?-g"[26H(eR*&Bb5"\f79MF7IC]#8VO(m:@qo'3j[X9LD=6M7KBK@on2fpSrde#7cUN<1;/C:V0+,J`
%o3["G$c[kqH4(LC?!E7F(8Cu[cdi-?4p`q:<Z15DU!+MOg;i]jEo:^`QJfR1%XdZZfCjg"rUOj"*5o7O-*CP#VB38$JZJ`ZF1$Y)
%p7d5J"'7R'[=;km2LC+khL^6(f9Tsd')\jaftDD+*j3o2?__qW7Ose_E/b@ZB]I<IGEc/s,Kch^YUUc8C:c'3Cgdgg^\mE:eaK]e
%Ie`b0c3E(HKFYDD<\2\\c0L9HQT*7BWn`D`qX3A0rYMUh)lC'ugAedCle?'7$4aR&#@*]>&L?F!6DjSJF:Pf;gW-/\CX1`W!7Q<d
%]JuS44?9gOUXJo^s&j;24tls'NgB_>g%+n.s*qKT_pPC(ES6ofnhGqR]-\H0XI=Tf#=Whun[Y\-NUKRQs17Im,DP`,8NhB.5*qPt
%CY-,M8%Xe)5KWcqqRbLkM]Vl0GL,fY^QG.*P7a<mG'h=7<Z(`5r1#u`h@+/+26h5A*1dN?g6b7_U;^NZ*1iY_`Z4non&$&Up#dpt
%WjB=\qerYkman]"]m:\`NX>0r/%Ji9frI(&'7hgNLduJ-*>'C([sJbsFNF)9pj`%as8+$.nf@YC]?5:-rniA.pYc)M>GlM?960P%
%rk7K7nGhpSs-KQPnc/phfh+j,i:Gc%Moh"6Ih"["K?s;MoDN>'@I;J=E[?tD7dFn3Z6(^%Y&;#SnC4Tns7G^WdbsnjJ2^QRmbn\b
%-J,A]l7Uu,H2%$ljS!1_I;[W?L.B#2RaeQnQ+sqXZ\V7i:ZZ3[*Tqj_rmL'lGsRM>l5@;-%'bG=lsLtu=NCsfZ6q!l;CuE4Uic&T
%;>+BEHZEk*2j9*4o%BZ'/hV$*>GT2qc^'@]nkZ`erT3VDlJhfL]8P:5h;4^Y4R:T_f[%"B3nap^k1P5i?GF+%Y*S(e0gs3%S\h[A
%3Ytl@s&s,$S(_c59_408en@j97rC1BjVOY)S1:l%cXX^Z4oas0I!OB5IehUa(9q;5p#5F:[O)Wg\-El<8!m@!s($J;k]:M*Mm/0]
%bXXa35/KJ8C@9-F1"Cp8j^<<n(g[],QR=,&0YAA60*fD<-[U$YbT_cX[DK0Hj^<>dAI\9>GkhW_bP&I*bDGiD)$A<RW]][V"mNu,
%9mi0t]CQC+9?X-e$%p-n@L*4C$2X9P_'[aW@Fr=Bec7So3nVB>I5laOD"UNu,ip\&4CW:(hoDe@qqn#O53p*I4H;MId!#A-4<QBG
%`oD;/@bTe]EBOE\q02?R2;u5o)\33J0+3?q,PLe\^2uVIg=A"Wr7>;*q<cl$g[>YACAq'qMI'=arX*JD6WX33L]i'scG$#LjQdPG
%9"E?XNn0XZ4NBH@k4$u.^E,J_]l)`?H$aO,\)Hc7oh(O_Dg22)4NDq>*T4]=].[)#fX78qWPu.R)VU+ud!TjuqsXpjrpo`^:c*$u
%XnU,]23[k0@U0h82;Hc)NdY4gSMd_&(.G3FP?9'ZDGd8OG)QOUo"g?kLRLIgfJ3(jVqHDY7XG/ZN&>Z9fJ.PMe(qJpJ$<E&lkm@C
%1C$"]_2bcJFpugGma"qQ<ZUh=Q_9tAs2^legSkR#GBSFbG4jVEb;HQWo",E-aL^8,])S,kQhT[h=UR/&:29ODbH"WG`uD.i2*(DG
%"ZXgeB5]GPFa*BUhHep,Y>K<][,9Hic<$CAp')+S%0kD.]Be?+\*kuNNo/W:QZqYDn9b3SZ:Flo1iu"Gq8YndGp2*.CIF!]2U@Rc
%gH%6RHaTTDe,AYPJc9i5-/LS)\(>D%,]:%gnG"h0O,ni$qG`3A5JFc,k2"R.U\E4cpu9Xr<f,A=DFsVVTI8JNeUCbdf>1:p7PfKb
%!FMlSZSqnlCF6CJiEg;sYku7Cj,*d3\a9<J6fE_Z'^e12Q5qOH5Mmb[]R.2dGWL^8a;DZ9P3Qtj`G\[=4m6puV9?!Q3<sD=41F=E
%boO1%K/Rr#k-h24XcOD2g>#?JjpmMRqkh]A^l:=PF1\*jXk9S)3d4E`%dMh8SNEck+/o;&Q__p/R`sWeF3"7`.!4c%Z26$,s15ml
%rMR962%SP0T6gn@ge005YX4.0!&A@tf3+M,^F*UNp)[;pJ<+ukQ.SH/M(MKjjY3k[#9[M<`osHl>a5B6s8(!jg%h7)3tg!/GA$**
%pHSTA.dQ9Pq]Dul=K/^a[a*^O3mr@rO&jP=?`qe[khtji/UDEfc*\qb,sJ)N:cmi/9*]AQcLU%-n[AD!QaaOqe!K61Ws"!5f%W6!
%hg`6f=8tl#Jfhag.F+-(pk*G.DoJknZ-AKe)m7&l7^ma>Mf$^V7D-k!GB[uNc7d`ib0B&R"nll'`p,=^Hh/kl0228[JPLStEVIcX
%lgjo..USjVgt'K2'o4sg=3LVIC$qSHDJMlB"Dn1'k>R45^UW@Vk&d.]p?/&g+Nss`onhR%Bl:n88%$`Orq3nXRu::0_;h0SYi(/M
%IY?eRpa6<_ocVue",_7D<JgKLJR.^a;`tCTHQa(DA>1/o+C?T_fP@2E`L63[Z<mHh^H%o?:3lSXBAX%cm'j6H`P_H5kBu:\Al,7>
%*Nq>AGRcoL@h*LV[CAFcf4JM&I^f&[%BoX"(0T2@h<"UEf,(Ip)!oc\q9f=gf,LZ4s*_Pk]`7r`jg0,u_rJ8E$uOT<a#+CNY!7q^
%p0lPpa#3a5GT:q!!UoruplV$7iGIBM[rEKAaG45W].3H#)IBp<qsd6JZ'QitAX21;+;4&X,10$fR/J/Qbs$jlId3A?gO)We*14eh
%3W79&Ii7@cqo=2hffM%GLE!\n:Y59mdbP74MS48Arb_K(g\4)mmBpPMFaNkPpMJK!Z\b"pn@_eWO&)A[\'7"ApD=dg/&^#0#K\rS
%#q-[`cH<MZl29u<PRkCNgV:cO'/(uHmNVkB(O:5u^e$kq"Idg^58h?.Mf=pI_;=PV%J"9AdI*hn4`Ai-Cr1V47=-[a^SH(0cIu?<
%0(+12H+-6>a0+fK3*K`O^-DMWlJb%Zg)i7WDelh-&3mn^DO]GB=7:%Ps0.i+jAe?`7u(T_5KE-Jp@F[I"#J]0;te12K0rb[+WQ%t
%hOWK8IC#f6_@E15(MEU>3&,:5V9Y.I!SIjuLWjMFP4ZT_n^QuGaP5lZ<sK2t^O'8t+dS`I`:=S$i$4.&rTs,_Hh[1dDdHX&,c5,s
%IJqpTS)5m40LX0jd_E<ee)ejP00fEWDneY(r/Y4aE;e&K'fisF^]3Jh\tdT$q@e)Feb;<,ah,gtl]-Eas5dS[<*Jb6+"ar982j^c
%+X\IZf:,G*8+OB[UT_u,2[C8sl3I/ms)\#%58W&J8*T4r8,qcP\tdm#B>=;\:OdCiH`jQPDt0d7jBmVrgX.nBfuL>D@t.L)NCQBQ
%Y<pEdM"Ob_SV`%)juj=*.p#Y/3p.R,dR,V96ccJ/1@Bisj/\Ch[<5,Hf@l?DCou3^])9*4bkIkGl(G^>5#V](RIIK`7*Dc+X@t.J
%EiT*%5G[\p?#,;\@Hf@_a18sb0fSC-]umK91j7'$Lu)Bc<F+-X\7PLEc,Wo"[X.Q^=blu+'i,kIjfd7>%Pd`*;l_&V-K\AQ&1oNj
%a4,2H3PB1QdfN&>Wg52Qe#R%G(=Mb*XDP=fkuEb_lS:)7=.!6YDL9o0q<kk2:'b0^O*5O]^mjHLID%>$qb?e[Z`jDnfIu">QAJD(
%iJT?Q5<jXArJ0/)@Hd[Cf5qJ9?4PC6n"!Zp]kg1(WHpa_Ziin3G)lT9F:=ScQP2rMIIOdh5++7MASaLU)1#'$5NmAs[OpPiU[LD4
%1h2K4B^o)?=_Ht4T:iPB;d$i6bHu(Wb<1.*LS"0L-(h/hjg-hcg/fptG9L!J\OAHWVH:J'+S96(rTI3U93M6qV@f`f'J4`RcXAhr
%M/GX0s.?uKgY",<Xon,%kT0M3!;iK/Gs9#F-8B!IdfTm(G\g_)<3\"emNMlm8W;MTEZH@u=&jaX9Y&hc6a?]e(qotIp<H$9**0.#
%0@[SBbLoAJfqI":ASuFQe5OnAbalMY>$kOg\+[V(p<+CZlch$V`U-iLiqeo2?@(5pZ)_^N3f=KlRF-3?#/YhLEo$9_QsLgdaE,$*
%cYZf64_01$!+A%%`c#fS&PbF&[N6abQ%X9WRFDCG2njTZ0>7C:GPp,L8#^#Ie2+/\60[aSZN6b@;a$aSnW/ESh(=)_A=;5IoBSGt
%[aGp5=L#jTL>u-4.("QYl::TF;CssL#r?OTRe=meGN`[$pRBO:T5X<[&&]8[cW_daXV:N]N.5hmhH\,TXSR4aT(m0i9_FfV^V]2U
%qD>V5^NssIJ(Rd$O+6]'p/h:jhgG*hs-'VE+%5Leqro*-U")-s8W>lQ^+aKJpX(DC&neXFRp+r1I/E3DKH`G^3*SSmRqVV13@B5`
%o$;!G*_#Tngn-`NU,j&iK:dp_NoJ`2oA$L1V3q/Oi44>Mgu/(qh"T<Qbp?pcI+G,bo:91@-lcU'64MJffs.s(NeP1a=o0ZjSRht0
%GV)9bceEiDhqG@ENj,oI61<hYngKe7-s@^S[_4>;:Ag!qVt^M2s8EV-fUEuV+V+,,Zgd[9I;*`JqN`.'8<^2_q,f:el%Dg(UXd)]
%kCiYU@a-a?qon*_%QV\G:7g/sc`.Hn4D0)'Ned]S<EJ<.`9Nk"9<66t.Q)3cf%FI^GF<=2dI$<_O8X/W*9:b!c)Qc0cD`9t"/gTC
%NZX!ZUe49M\Kt#S[dgSa2g146EMddI/']!6/\U\Bai'Suk=-D+pbSR#F:jgee^s(nYFu=:Q<c"L4>\W$bg.2sm-(jV9icTK"U]P7
%FaE,N8lK.g:2/7Rh26$h2n/#fp[jres/i'ekH4gZ=dhajk"Gc!KoWu5CY.^1Hr!Jl5+>oFs-].[Fe.m*#87OV.)K$&@QAJ"!f51I
%iFg?nIQbP^]aE`Z`e8<>HO>f%:qHsMBbnJ#S?qJqc8L[VU)QC,a!(0_d,L<$fCSLYqrVs]&sd7%qpTH&`_=dE3f_0?gH7Y$7`;P/
%gtR4$S\pS&QQ:%jGHnC8mTUeUlQGojfd_]Fm9JecC<eM"W\c@k,7koOY0+D-YB54dmr7?on2)54O`S!Tgu4e3Q2M<m@1(2&VBNeK
%W]"gRN)*,7l*?oP0ke>MI-!)"4HK0Z@c'46kI.SbiJqko*LC7=:Dl\n\1o#RHr+.q/Gd?2^pWIE>!81\V`X6B%o%?T'@YQZ'O$^"
%IF]Phcf3V1a'mKgT(S.9pgcbl^rGP%m^bEiY]C9"VHZ$fpU9<j#6O3>AHoi9WH'+4G1!!e,s5+u;eW\9I#lkK5qUF%;B"A^o4PJQ
%UrAqIFJ[=@U`0j&U#mMT-ieq``-#Ana0$gprmDTYqO;6Y9a1+=id>Gdhd5n-D325Me!p7&MjsA:Ef\:6+4BE:iH)bOhp=aErs[X<
%htB0X%X3BIfl=95L$-$6Z^eYC(OmM%fqdB;Q-UX<#A)eJ9<iZ`kM;J[D(CAqb`q^]j5m3]jg+Mu8,)W.H-F\hbP[=1;I%pud#EQV
%l/f/Ym>2g&Df'ji*A=Bp1DLr(,<RX/A=]<7\f29#7Wj/2g%,NTV0h*#\ufX\5!'EBFC=/OjX,GZa?P@UF05K)rU?Z,":&m7,i`^A
%k#4j0LDu[3p%tnD2t)_Zgb>@-7t9sH#lMiim>mTgn+7c4(VS(jcfXnMKWrCmbjU7lcac$I51^c4q)[tQ2\m3=KR1p,2Z7T]Jn\"O
%"QuY0IL2V)N__`$^+]!`3N]en8$d0rF`65c1sb9/NdUg#b&/nb'mmaP?7`.0;+rVA<O6pA`DX8A71UK"4*_?XnR]t\es.nF[4e4b
%0u0"U];4<V]"$$p#Iej=QJl*:i"8$fZbRdrdqS09YWDS`1pRhWBMaCF!ZtWW#U8@"M>:1\33W=dTa*NUj=h;jX,r4$2\Q8G,K0p[
%;=OZW6$#[BO2/$EGtNQ-,Ff]bqVd]_Cn/=e4Mr6'+I9;Lfp+qPCOZN)4T:032-re,e%fm?&LdI"6iU:+,A2enaiFT:Ja?liD?jP%
%03//Vn9'.g?OXGYZlVFi7M+dFEW-e-"++YI0QFI:O0GH<5M#e!f7*1F)'dAAcj"D&]7FV23j$hkYFd%(%UY_Ihc7aOS=;<P[CRq0
%A[T#K6WG8e<uq)cF,-ni8[7F@>(q^qX+#?u2[%1tqY7YA!0,j^bJhbo?3r^QYlZ&7\pH.L+AAhbCg=d'S@ksg[R?eVB"-G+J_fp`
%kB"5kpItDX?A83@G(!:"^U_D"F<KF$3F=m[D]QqY&+B7_oYc.[9-^q@i%q=6i&\505!'=*('3aIeG+e1^T$e]\/`5)PI.r4%492Q
%3Cl#dP8"Anm<dd99B4:_jc->):Lo3Mm<tk.$r6A5<9Ca11%VA$Ql=L90ccfB@>*-kFlK<sq83_cRa:,e@?R8ENI_&608bckggYa?
%L%J/jg!Im6f)MfErSmZ'C']+9nc$`Ypj\lJ97Q!TNs>S:5ObZN?eJ5I3G:@Sp4bBtVVChK+7]-Ka"D*Kn#)"YBE*3<IEBBRb8YiX
%Dg]aKr.4T@fh'IU3EtHEHe_E[S^)GOl+d6hq:t9h1YZO3'enh(6?d1j<J/mlihU7O+9[daKoE&0+Zu_[3=^#p/@4K_ijgD5C`S6e
%r]YoA:!mNY3jMB/4Ec\c0?5<ia$'DhG^6^fJiVGKi=V$aD-plA#"UrRN$!4hV.IqP\+DgR?-H,[YacF73\PL0f%OVm4C3\j#Ru%P
%p&*n0)cAdO+7P3[jLK;R$^^MFFj._Gq93qeqYSUrm-jroDHthoZOG`8og.F+8;BUg9#@b.DBK7i@KQT[TH"8sj!sX02NU`DK>)Bt
%eiLse$f<^ISp_n^h975f&Tl8>ilTU`KL#gAKd#:[@0,Q:4U63q_oBS"e]HD>8BB<3B-X%VlHXPol`2Vpge<n/I=.T(/u4Gi>h8'g
%3pat;8"O?<1e6ZYk[D"@X`VR?]`T!Uk[sHg\,0`&T'uH%Q%%B1UUu529`;nL<T;G119Ngp#Y-k-%H0e/Yn?+Q5IZ5NcUL/LEQ<[o
%S7>kQCg?'p@Nul?RagWX#Wmc(#'nK?2M1LXLrLkWaGk.\c/4$In2#V;$"#U-Mu7CEOWsOfF4g4?)rWp7ZGR[,H*QNW\.@^B(jF:)
%A02qo3-UF3YTp/]#OnL:n?O)gTCLSYrNQ1WLI2rpPMoLBF:^3r+$s9Hn!/o$Xs$J!r2'r(:E5dkW@kG0m@&cKN"Uad*>lP\4ud<0
%&]Hm9ju]V'G4MTFjVK%);f8R??NS(c1%o)WZk,RgkSk(!o*EBM'enmnS"l%c!sn["R>?<5%fS#R]q9f*LQM8TXN3,fmNFY^_3ta9
%Y70mPUCsGXq$BHZe%_8_K\KaMe06?"*tO.,okY)Q3Gp)eG6NQ&GY61YaP6Iq!K-g7gqLH1D_BY2C\por3IJR,n$2,bf[3CacG@e`
%(1\m)>njtIb8?]?bT=rPqr0g%qn<_BS1>)n`/BON"P%aHTfo/39XXRW+ftl0XKFFqn(bU!7c->QD]\jAPm@8K[)`Q(X1m&+g%Q&K
%eP7urHk+^-<h+5\&cNrH)QV54LO:"AN1cDgN*^@.5$5=$S/XSZ2BWu6>Modtgjb,iTHM6F2b.rGK'3jO)Q,rf`t0>=?iSjWpl8k?
%e<`8ohELbq=87.iIW2(]p^QC1ZBn>mcA.m;SBVrIn15@uU;l86;ubgf8m0jIL+m?b!^7Ls%mRQkE__8HCrc94:=>+a,82;o[cf"_
%<5JgNkV4LoNeS3%AaAE)54/sYlE?!(HiF!;cZtKHi)@8GMM!jG&:XX<-l2co%`.63?RdACl&AjJ^r_hEmOo'06tMp;g*5$r1bpt>
%;No=VPt8P11-4YS(IQ,)rp)"LEBNVj64,:(rG.*qSRDn[I"1s$pAK:j6[\MDkO3ln2q1t-^RtHF.T$-;J,D0=7=P'_hu<<c^\lWk
%^]#5Z=l5bp$Y9C!Q8!0cD3$a!0-%,ZqqL`*Yi<ugO8o/2reHO+0kC5C)0q%1IGj6KJ+bZNHi*+.q?sIF0E:$=NBb[2q"aCYk-N>%
%^JKaW=,_$^UZ,],NELdXL9WQ>B&MmeP$lTW6`K_WfX(>X`r46`m]AX>Ym8&3DskIuouG-di4`/6l?o!E\d<"e+pN1+lNc9"q4alQ
%k0l7/k5;Wikma9V>`@RQ4o,cjc'O_=\Q7tmVgna$?iBOK^\c!R^\*.Bs66`$rP`t&s0pocqad1sS]$omQ<1DDo_oEj_NZE&N[YnY
%bZ?H?`k*=:_Xd;L/4l5LM**`_g#Cn:@Jh5+UD?NQ3j"2[[T*3&Lhb-J0/IZp?@FZcc'Rlg3Nnr&pXPDX--4@j?Prf5po(P#=?kMP
%Sh'f1lhn'a$=7aRJhQ]<k0'-;D9=T)?.SV)Q*#=8rqZ=R_FFXk>*/A?3_D*a&'rG#:L5AS^;nEpo[,S_p<A`!rT4L*rs7KbI'I)-
%j_0g?rCM7#%H%9DV#^U<?Teq[03I-'ZH'Q*H06D]R6DSga4P\Y77J\K6VU`j=7=D/)W8PmI4UiB?A_44-.K:JcYC+1Yr6p0lAfHP
%I/G)FUZqu&#![DrQ.AH=5/%Hb4FA#kc#9/6Ho3#?(p_KC@mH$%o7c^aKT@%1kMdfN0d73gr,?"t"d=2!F8YXQb6-OrQ\R*\ZHK0q
%Z-/68s5j^Ls/A3id![quYR&NWJA]9?qco9^(^@L^l+sL4'L7ipn4cAWeG1^IlVbD6=e!Ft4dGp7#d$%J^on(1O3:ofl]O2]s1U/4
%5K%T62`TDa*Si%C+L3*W]CZfBaF<"Yn"/M!n_JP>lcu`!M#3sdYu\q1)uiG'-sB&RqI2MC@Vp^`Blnl=s5a.Lnr:F5^AP$KV0pk!
%Xa4cTb4"PDqG19[.df3;N64o#pCK_afugmklLLoMp1/I)@Ps+@jm23ViI?Ml6>[);JuJ.hMGcgUbild].^1AJbGTX[jL%A#ZI[,O
%&XRX[hYV]u+)11P4_-(FaQ);d(+OY*/q*3?o<I;&PkhodEq^WEjWj<]kWcr84(j0(D-\rr'@JWZbM*W*Kr-IKa"j@,E=`,"[U`\0
%FcWrE]uS-@rq#Z0N4l^Gc"hZ69de!O.XH6Mb/eupY>->u?bPW%T,i)-#X"/__d)ZUqdf=@BA]uHi4KAj\0W])o.#]lo3LQq4GSRN
%'07,t7Ct->oP%D.C\u?mLAt2%;kd8eY8if7@n$EbZWkRLhN/h7Yk<gUEjtRBj`MM4:KJ<D5Z^u;d\k^g^#Dk9#>d%2e9>#bT9&u"
%'2X.f73tQXmkV%K>`]q3n`o/fVp;C?q)P)GfEWOU*"dlShgAl15P[gsr$=\\No^_pnm4b6"u!<D2>k,b(`tp@bpUg^,?r0GXoHN<
%5L)m+W'HhgN!LTT[)cZZT_@NQUKA#.k@]:Q=\E/h?pGj:NrhHdNW16)<%_h0j1<Vr]D;D0bKZAFG<95;U0R56r^D+6&_lY5VOrqL
%,CC3*_)&_TSaRHTW:G'urKY_'IQ8Um%B.V5cR-h+@5p:\^=m4prs2uM?se6ep:ZtDRbk6Q6#T>H#<k&rq0X-RR.bG,s!)n(!+8t>
%m][Yi.);'&F2ZeE5$iFMm+;.4ZbpsI<UCCppjDj$rAPnNf/n".g0rr"PA)G#8(DLu!ZQ/IF2[+b#%Q&E2H[o"4s0`!rC-OGa>Ph)
%J+9B(r)0J^fC1?a?EE6X([(Tj?FY'*IK!=HSe"8Qj/p6hC&d==U&-[1;-99RZ;VXOR;h*W(QMqI?iJC7',n8Y?(X:;1B?Rm:%A/-
%hZ%TUs6P*PJ"<:bnS`=,:O<Z1%/5.c*rL%FierBU2o@fYR,uO,"n]E?qFX9N.:>HF5L"MTo.MY-!K7a@s3p/s69T.7GoX0h`si!S
%mhu+9jus^`+'?c0,^B&P-fKudg_\<!gLp?L?1FcAm/?f_'.Au)HS(/Vjo.#IO)K]jbQ*+p1&T/bB6Ar2h4Ee>2?,%Zs4R,Ts#NHL
%_uBgsq*TBs?VHu]&UsGChdD-;OR$9Qici07p:%g9hgZ)1ja[=-?iAE+NW7a_mI:\4;h+K-hu;njr9h$fm/PtZ8bhkb0B<"\s6jlT
%qn&2-!!tYtcWB4%2dEJJ_16R',n"UKH/j[ZBd*K:UB1*rJ+p#<hu<<G?[VUK?iM^lqu?Tfmm$g.5;!QGIsh![1WWXIrO^tdk.dNa
%reGb+s7VLA$a:nTZcKHSocLn[fpD9jB_+V+c+2!3Y#?1Ho5&>9Qqs&F&PPZu4<[K6"eVMZ3PUY7$]HFJW]A4f#-a:&AtV-mM]DLh
%NI]1+F<;h6FFl0Hc5Zot.54$r1U[O%27rKh<lF5+*,ZsEK+O-RD=H=8'/P@3TSoe97LqDEM%jk%[h4@8A9mCf#o.PbpIrC%#!<[/
%C\mp-<(t==.o<D;5^`'D8h?9FJ/q1j(*UFD6S<MmFDEFHf5S`4Z`H['I:0@69pTjk#t.qarlR'+gM_'/0nJg_kVWW$)o/d[Jo..i
%<V\Wdpt7,^6(B+4@/rGlJt8j=OEl@8c_7URoeiJ;"sAYWZWMb>WNOD`.#b;c]^q#LpAs=b%GI)Z#YuR*rpOG.$Z$*eG;V\hI12#b
%daGRe0Qgu?Nt5/.2q)H,Zn8g8eK]0SF-'41nRZ-^13m*j(,_!Pd&G,plNV\,/d:`7'-O<]=23>Ca%*3;s7&eOR:ig3%$u,Zfl;RI
%n3d_'@c1B9!T]^A^/RXPKL;8.QuR)Q=I.Ne=U:to,OR&ZJui`Z,g=sba=1SN6H1Rcs*i(Ke$tC1o)TgV""b(m8U`_e&!7Qrs+`^5
%I5V`>X8E)Uid'^PiU?.[CP49s/5FCj6Hf6dMcKp+EaW!chU#g_oiqc^eGr*7S-YLPG)o^Dh#dbHBi6Sni6YCTAf/c]Rs^#+k6SQ$
%SfZk':u`Ti-kW1_)MIr"Cm4o8)]BKS1*W%t+H#8UjSo!8Q'<(-KY&U[!cc89[h+;WKSW?\6:MYO6MjqOeV*07.X.P!T_GV`D<F:"
%'fKHqr7j0%]l:Dl/Oa3P<<ZV0A^Ao=\RqR8nK:b[SD/$2iVZdnBE2K:>4`#3K5Id/g8Kr[hI4:8@`;B^".82,ioJ@oXBI!ZR5XZi
%?rah!e/.L%VI]<LJ#qhu)*_3IODpI8XCZ%X/*fhj6M<0":>9!iq/UdsZd?a7Irlq:bJckJ^/g/a',qm&m-N\[KuI8\&CkEHQ2,Kd
%7%QG&$$[rBc=Uc+f"J^U7+QU5F,+;>RPi01+WlnFm3?f]4,$6uXGb9OLR%@W#\sp-*.IN,K&F^3K(kj0ni0L6a0:X>(DQFb^5jRD
%IJTL9njS`D6c321@X8@e9dt0M[25V&?ru1NIP6aSR_s0"%.7VSkC/iV7p]D'nZ_^.N:W:ob@JD/NFu5!F-f]@mM$EA^L>CPD76Vp
%,\34XQV@OF:tgcJ'5\BY#H0G3n=ICT_Q'^M;H`ZignT)n*9h'X!p_>mHTC37r0S6q6b-ElAH5D9@+'CZL[.K*3hV<.(rOd2J\ILq
%RuU,+B<%8\PAZ5`YJf%:PYfWDT4JL*E'5F)m(Wd]>EjUk55b;63-B#LA!t=$etbslN"00K.LlO&F4m#Qo*UsHJHWLFed*rLR:,+!
%4pkfR7cOs78shc;q;WI=Of.Z.j7b1pB7,an+!b%&QVs_=#?f[.5\KHuQd=>6>k0[9Xm:69n)jkf>iL1RcQL7MFii2BTKD*A]%\iu
%el*;4DcVuhEee3IWH2](%grV[B\4Id=YE2[\L?QF1re&#Uni_6fNUEq'D=><)k;sI6kGEGI5!tM8im0sS<`aZXR2377ini)\OEnT
%*WULppi;u^#[u"QWHj`Ep0i$Fd28@SK>+6%"fVPXm<22IfNl'"cMAS4@p2uGXXA?"WBDK59jYqpl'E$6L"9f[\gSK#78i9S?oV?A
%W5U+qm<4`'1qO1*;4)gYp]eD4W%U1`o3uZm8(^'0&qY[\9)TaT<+8fCrcD8s3*n^0[0_rW]eI/9./t.Ca$/Sb$al#DB)F.ND[*QT
%iDHX99maR3,'$(2&hAZ_U(]gB>>ah(^L8Q5h%Ki-m3/1m,["Hl$j!i:?XJPt<VT\Zmpu$?cDZJ7%u8AO1f')F4gu=SCu/Iinp51,
%%r-X/DT*#D!-fa)H(62Xf4pK/2+o'5&;7pfV\-]k=150gpYnflYc?Nl:1^d&$>W@V40tPY:BTC;XG(Rq;uM`:c!%-q?Vr=DP&J0>
%_#Za4P[o5Ke)^D.E\1.@Z/g@7>Z9]DU5-m3d,SNk,(9$_A<u)imqsu<<"Cr>V!B14<^Bg?>1<3a!GNNFk9Q$+s'EM<$=qeFhuD?b
%=*`10UG2`E*>mdVht<&/rq`7?`rGR=Ii@T;&r>=3;d0.pNfgMu^1b]%7UE`H(2Q;Ql-Y%(4J*#5JmAiD&A,D0W=Wo=Ss-NNjo"M%
%2'g\Xd`06tlcI;*TF1,pY^OWm(gt#<Y@05TJ+k@Z4LhE!)DDKR[7\%MF33W+Eic=MFt%uUb=T??n#3K0X/p=SA$H5'H\T+*b'SMW
%*lZqE5fYC:M")L(3gi"d"_NV*:=kFa%b,OT]_X"-V)I[J]MLu"9TOAgH6labXJ+'bF6onZ%FLMG9^$BZb;/J:Z[q)]a8,.^bjs&4
%N@,/9g2+&*!3hWlFPd[f!-YYP^`<F>5/F.c1@5i(?Fg59Pd3WSDh&j^'D[S@Ge_,9ROrt^pHG$EbWG*-oEW[nj?1TV%B:#-."52'
%NM9j["J$r%mC[NqNN@,q`_<=onh_+Nd>f3'1X[fFeNqoF,9KYp5>h\"0'*gY>F:U8kbs+?6l.CTdRM(c[Z\%_+Q-e/UV63u6%6OY
%`B+dQs,nd].!1=1UkRY'[PoN=>C739>NTgX<^3J4OK@]pSRat8a27A(!bfLj\N4P\^fiG[PslXE/Jgt##9aOS:A;ZoN6P&J+&cOb
%7grN5hKN6(DZ!<RQn,Za@hhhYc6PG_q&DFJ)2p^VN8i\tWp@Y59^n<fhJ"b&3E5X2LM1j*3%Q,%lAeFoV\U*,">1-!$+2i9!R+9_
%d.#QKHgokSWd:%P]Q`.uNjbGoA,he*)eFuglc9,$N\_Ds57>p0NcR7<%`"$oh0ff%"V-YN3Q-"liRjY?KS!2NM\5<e?mf45FNBjq
%eAd[ca.JRq_ar_.IPERYb]B$_$fho2)q.'t)L;^DV4$97mURl@jH3pTJ\5.I3RpL/*77"Nr=gV7A8+oMnk`7r:[g(2oV*(F/P$?U
%RdWR<'$hd-]trPH\G4NjX9$0qRYABthM4tX)InT]03d!ZgMp^3Rq-_aM<T[f'"X;I,OF&[kM8`]nT#WuKLa?)$Ob3/#^<<AGm?*L
%8Y3-Bc8>8@]R$&e=NbjrE-@X-']_+dcfM\t4i*r/P#0eGX:KgQ:2lqW#eB2_Z,64TamqN&fua"Y+h>b`/gD7=Csk&MGM,LqJmdp;
%/$.UTcrd,/4+.X1J>uceDe_bl\c&R_4lSZ0C&=\&&UN#R79>!ZgHi%mlL:=D(\:lQ=m%ZOFAI9Bb$togHWC5BRnPSIeo%%P9a9^b
%YE]SdThlW6_pkY&E7oVA18H4ue`=1`'Th`%Lo>)nP8&G_;20(E_Q%IH3?;6jiW.e.4@2tC;`C5Hn>W>-Nl#)3g9f59&!hP!Porjk
%QD1;Sk[2LqfuuYU7Ol\oi1B-kJrbs86*X?`10\?gqMNnl#]2+$c6Tsm<W4Q)o:8q1p3aJe$C%d[h1-qH;Xnucn!arj/qo(ng/gW,
%O^a`l%*[(se;`f?F5=56SmZ:\Hu1<Pr/POL0jV@*qi'`)2acl\r%m#<+YCE=JgJjTftE0&(&,(gNXu*t0U.:,^)d#5]Qj9l.kY+X
%po,:BfT"T_>N?]Ah7=d`fTC@=)R\]PZL.O!Q$9oe_3*aHYpG/^.>)&V?_f%8ab!:'.@[ZRq7/eXS1qnAp9_qm4#+AGg"K?7$TA4e
%.`CerrJ3TVl%?r><)=*0H\l5@B!i*_m;g&&do;li/c/ubL%6PQm'rRkRNO_!9snn98pCs@L@F.m81EI;=r"mYr@)DSTsIs#O!qNW
%p13-gN/1-DQdL(dMi^Oq!+]CGP+>%d>Y963V=[<uZk`#NlCe1hQ5$ml!r87ZZUe3OnN,CB5$!djoCMJ"CigfmU[);8\-0q1L!r`!
%IU8>4p[d_NFfmI\96W).kB:k/M%.EL>h<%.8Z^2K7e-j3([pDQQV5l5;Bh(8TX)4^<W5\toV_+T<tNk1/[E@'CU!M3gG/+*VT\K)
%i,E!7[PQeor8:J!jObZ2]+FC'9O0#&er`V=WcKRL6$-R<aiVsdFd!+>Sg8Y^Crd0'_i7b5oF0M!Q6Q>WY9B^.4'7BRkArJU3@E;-
%&?3DlK3NU-O8b18EaIa?3ERM6`OO)tXpUTN7/4Ap'Yp]Z6/V2DkFlo;gfRYVT\iPP/KBHL(Qm3b\qT*25La!WWcIR&,JWAkP2len
%3pB8$oah(Op]T[-<g#Yg'Jl`i)Nq16R"NNmniU9P'8hVe6Ukk&T7s,@pF6C:YE>Y._;o+1G;&]OHqe?"YO9%@P0u&bMI;\ohah-*
%dL,D3oq/<0olru/7+s`.3\X;Larb%r,$@j%o#NL)Gjd9J$5?@Fh2PNcb;V(rCc&_o$g+%Up6b"/0`'IN!L@,K9AWA\Yh#9I-G?f?
%3+3HFp9ZacNf3.l9P:t'q>T"c2[Na1Zo6TZCu-/5nq!HK=X1f+\^AL%X?q:=:mpFHn<]\#SdLjVATjD#BD?#\(TP9;KRQg$i[fSO
%cF7[GA0,0?F?DCnDXHe+hs2+fiL09@AtUW2C*FJa]Y%aURco>JK]@F\1,Eh3ZP>9Y+o!SB<!?jiEl`6X#K(gH1cGQ/Z'C`d!/*k9
%C;fAiF_Fh23a;amF4Ff6eQI5!R,%UA&GIM7S;6G%?tX&f6L\$_^u<>q\,gS7AnAW1*9f5m'!lEb/T%b]%oteK&Plj13.q0dj;m/,
%BudFkQ3(2lSa"uC-F)VdK<i&Fj;c`$akXl7?.u^3<@YF6PhlM[+A)L@j6F:M$347eS0Tni67+m"9MF/8S5-I2P]mR8oFAZ+@+r$R
%GDTC?,R`#nh=aoilkFAi;tKhtZgl>E96QK4fH1#p8S\Y+]&E(Ur?]>#*@dfo+o(;Wc`7HP:n'4BdOFoVL=A?$S]2#c8LYhFXXrd5
%FTG#Vdhm!7pVPR&@mS<ARtO:kC^I#ff1>,OfUlkJ&n!Cr8cJj!/##]CST:f&#^21]%&cLqk31cN)Q.&ZSA[D4Hg:'E*ukqMPdQ-e
%86oI:"`dlX$n#S"_#8oqRR4@olAD4]1\-Ra1jU,/=,C!$;=u2i;t_RmABn<<>2u"Q7fqTmWJq`AE@FoEM,K=5:_.'mIC[Z?N?gS,
%=X#`f(ke8mHi,r3o<YqAoiC6Hh5^_)N$9aUJ7nCA1'Roj6M8kaK?g,I1$[R,C?@\/I"P)%Ao)kgJ^l9L3#6''/!lU)8>h#?\DeRb
%MAcI;hS#e!_-q;Eh]joD=_Q5]g^1&_fAnt=_O^fl[R\b,Jt`$$OGrnR_pjDPDPu4X_NC0X3BT?!GO=^o+TitA".5<pPqco#+.OTa
%e.`J"l)NmC4UKC`Z\c;VnIQ!Ol3>M1a9e7_8qbu?]C,`$]_.@."nQ$@ch3\P03J;fpfD2*kDZs&cot37Y9\0k5s>R>J:'7N`J?'i
%CV_!Mhbpp6U/tE<H\G_e+h,ei<&A;s"XTYt\4>aj&_l"*#kFhUd)C3*g(2&F?)Q7&>1r@*o'>0@jNA(Z9P.^^D8L87Q5=3\"lb&G
%XnVZJr_7B)8"JVf3?FQ_>JLB5,]@W$@kq;cLJf;7CN3THh\bU/c4s'QFYkDR-3]2P,(/1qUtC@5.XJquTD2h*_`H)cd]dueRaZnV
%Y\&/ZSe&i>P>U\<iG3^'7`9B!ZT!0?OH;=%Q.Z_B\-UtD7u=_A2#r.5RTf4jV7h9/F+g!&)tEofbos0A$gM%\WdU_hN0Ds!h"4UM
%pW'sUMfSn<n6?p<f+<&m--R**>6B6,4XkpXA]N3Ef'"cuU1qT9Osq0hLl09\0-`Llg`*r@gg%,OOfS1>(iK=DS"Q\SYU.dtKf*o`
%NG$A(Gsd-UQ9gT*Og<AC$J#eM;pF`L-_L.UcT\RqN-ejHZ,&&=<$8TJ09IgldVht702rAB`\NmA)aM:n&X6_0e$9Q+9o3Jq_M*9/
%B5;/>!8jH'>W3$b[GShhG<c@XKCW,A.mTBPNCL,PT\CZg;&+)'ckcgOlfj"RjpE:4R!n?gHqPbW&J.*VH]#PHfO4ct5F.heKYYW(
%/L=e*7*@8.D\1_S_Fc5?nmKG`nmEk/Q%ElgX(0nL/?UQ5Md"$H:(JTQqMLt+b-SsjBuTKF48Uj>#*#!a+c=c3*#"RR8REWq]N&X,
%QO=-S3OV]@6)7'1XKuO]a$T;e(iuuBi8]cu%=Q^H):fqJAS$\\gjbs`HCs"<SM,3/M*&65F2e*Y(j^p8Hng+IQqA^d.W<1N>T(@_
%KgaB*h2*"GJ>$/WgGu7f[m3C67Ga7))Z4MH+?K4W&e@t?qNcX;f>\,0?ulQnZ3ep;^h1/!ZB37_6B1:SGBLdtaRC/pkI8/f^CcS%
%:7sK=f;L>tHEP5&*X*C:0i*M(]?8+L&W9aPV\+=b?3%Pr#0aYQ&9Q$P_4fA43o,<,_Wii@"S'>t(L_WN`aBLlMsEd2U6Fr)GW>l1
%@A[5<"]Bj?&Z7q>kHfO!2Pk$5&(_:0[,X"*\$hcX!ke(*jJb=cqp-Ps>u<9!G:[8Z^aXi%V=eI_kJ@"X%,se]Qk0U7C`>Qd;776p
%+VD<l0$X7Rno1?Uj625FC8;VE^g5)ul<WmI!F8#T1IB(e!(1RTRL,TH"hZ>DW?YBMbsYLEIaC%lRZ1"H"dpD8gp[\j`q\>^MVV&D
%l&lT!]5ThiMH5Zf';?s=((O;;JSlKIeuL>;!QfY_/aJ#a!Cg<m-niSU<:Z3HQiITK*.('V/mp)`Ph+\3nn1-$&_/_LOh!T-$GY<#
%6i_Gf?8*dp(XNljYYkZ2i)W.3iBA$k^b3TJFj,TA^apZ4X>tfY<,T9'O;UJ02"hSKjP9sU?0^tVo*?s)GY'Wo=Gk_R:6RZLT.A-j
%e1@t@#@[#=GU2iM]Y=R8m>'`Vl?8FU-A'3IDhb`Nl2gdQN$V!"Lu+#9R*L51(?3'I&WPKX&o&-a:rHQJ<PP"r[i"P_C.\:[6XoqK
%jqC-5nK9eJ[$bAM&2[JU*K>T"VE-hM<t9c8G:DuU>[(:1pdhRr"./^u9aC9DI94ip#l,J[@[5H65+@.J@UZgp"Kf<"9PiFJQ$$4"
%fWjhACd4WWKSJ1h7qSY38:TU2`JfO83;PC;^nike,'c!`<>/-q^4gS*!hTH='[?fZb:TgJ@eW:i=9WkRb(ki0j9C#]'KDb=1a7`*
%XaR3][g+r*QBmV680<ZKK$@CA`.o3bbSJl\R5:Wd,L8C0^f>mtA7^T#?S(BO&;%WjSeIc8WCjc7p]NuZPkW/e_VUsIr/u(;R=,=&
%N`/;Qg+]Nd6m]q(SUr.1qd+Qac,64?<Q'(BRN8X3"KY)`d+*?p^cd6397(ZbZnC^lg`q>:kDCo3JYgnMV<.iOl+Yboe_c3O.port
%PI%pA/^+^(5hI,RDf0&lef)pqRfQApV:Y+,bi%gLp4i+!T6PcmMXuYIN>2QqfQf[='aR6!,:R\P@VP_!YE<5-\I[8SLGAqH[off1
%OuhZY$""p`I>TP2"cMXU&%"WFiF`:9J]r;o)T+$MK8['Bh#)ul4>uuW2@;6e%=H.<QeL\pMH0l=Y/F4EZa/E$gKh4@1+S9'>q#2:
%,mr(j^8aWIW2K/DIfN/lgW?F<Zj<"Yb-<S>7)C=$L$FZC";nDo-/9cc$RonDp+ij8\=G*tqA<YO%iOIFl(=_/YngN]g1%pDilh$d
%rRm5u\j:b/l6&HP\d3l<_qV9ZD1[jR^QhZPUaL:SB>Aljf/=3b8;\$PT[tAO:/HOi.nYqLkhYpibU;%q*9qH?K]_kg/i@hX[eI0\
%83mhG[k&t#\V.&\%]cUX6^'74,pFjHIb>,r6,9I@A/@eI&Y5/FYspU/q]2o]m8Fn8e9[4$Lhmk.Lc#C]R7MM30Z-@/qOrq"jbu6q
%b-RilUn2Eu9OS^(c@W8K,-<VZ&UkLs:`YcP2>3$J!U3YHnIG(c'.dGn9Una(2Ug2/(i1=1/@,>-STMa`8/o`c`lLgA"pVj\_Glc:
%.FmWi%,5Ni$R%IjGpDi_MhcG*&0oeM.SZmI.KMkGho@86_<")Y@*!mKQ`h8;]:-%t0U`SH6d3XsFB<!N^6`IUAQ=>4EEs.r#j_0a
%"?O5WGt]s\!$q!E&.<?8:HhkBp)'g+A5UC/YD)XH.0-W,2]>`0MHirc8CbRco`Z?!koo?S<]OJOd8i9!LUP?)]Ln01MR!dILp_rK
%/*FR!S',F&HL,aSTnGbOM2A8>LHr2#An(_H+PA(;82uXk1X#?5N]QJP+&HYk>>Or3KS5Z>Oi)2Jqnl?B&kf8g#q&jo%a+"T>H#eG
%(;U'/&L2u.*cNi#6R=M#U=P(O!KW<GXJ_Y#1LnIr7'd'fJuDf/d5n+uKQt-tcNIgQQ.Au@`<L<]oP<:W>*.k>$4qkK@j<*\S"]o:
%b?M:iU`CB,gG]e94tjebVmjB7!-R)YjuL4.,],^F[qBaS=M"ia>k$="bpL-,f<.U!!%Mn-/67[m;ZTCmpQrb*VNp?o@+!K&em&_i
%"%"K:\oZi_2^CUmm)QQ\A%LI&WZNT?O0^F4hK1's/ks$*DV.DbTf#njC^`3Tj,9W-Ehpc(qj\cmOS<h.g5JMF`Dt&nkD4ZiVNtGM
%,<@&JJ.\4I'%cl[.r13CAIA*^MS-WWm7e=^5WF;'GTtk,G0Wk1c5L<]@/(2NWCsg71/JT=ndSi;6^ku7p99i'?.FZ?BGnVqaKJ/4
%'MC)48.i+1?006h>++p;U8nR].\!%N9kcp,NSooAFEWdpZ5%Fl/Q8MA299GsVk\aDB&tF:"0>-j#<&h*/7C%X$]-G`0`GMo5sf>#
%iGG(E'@nai_,*3"N<k33C&N$kdTFFO;*keE+L[]L+d9"M.W@G/U5H,li(Muu3%0pkXSQ:LLfWG[IO_fqJ>ZtMhT`E/1KMZm%"(Qo
%3hne'AF?)u!$fsAD,CK</ucAO0V,?-#YZM+^jpj3pY3G%TV#hjPI8!uYiFK%S__`Es(O#c@,N'F/H]LdZG,_X]&6lm-A`J^,FmHs
%VbaQJK^%WILU2]W888onU*;b5_IWs*lPaNS$UDP4C>8K;?n5b2pRn-i=]1W38h.n0"LEdA2QMPpEf1LT:<$j4NXk2Y8Ota;#tspJ
%r?NHT;igG+H7a6Zl?m/SKhGQJO*Yf$k\Ih(o`:c:VCeJ9=@<G-1Ji0n%45o/NV$%)/`7cZXd\!3`N<9NT9knIK1t34_pC)=d"e8(
%``7q1C"W)m-_i5lKJW\/5K*&_LH8:!R%8DU0WS\$$oQI?0FDk/g=\J;<C^]WY.7$i&:\';>7Y;Te=FCDp%nc$`r1c3Qfen\BudaA
%'hgoAEGE$=JME&_<K;ra1=o=$Lg7g1.prsO/.#EM*Q>au)?<V`11FW#k]I79Xi)uZ.uA'bX<u8!9GZa:PC)1&6id3cWO*^)?-Pt\
%W<1XcUTt\L%Qt,o'^$G2.OE-.4Hj&H=It-1cKur0^^Tk6/ju5g;<ojI@LMGN_Ju(QHXR"hHU_h=CL(;teX`$t/\IKRDS'HL0#]R\
%?WYJcNRarZ'p1i;*@m"Qg"_U5@@.n.-u'6%Y])8!'^:1Idc#=QG\@@sVXqNcksaGXjho)!S#fsUj+k8Zj+.>oB34dQ[XmKl@CB?I
%h2/?P3:j\d^9=kQmMi0Mk\b#t,5aU4;-u'[P'M4!HiQ)JdNZ+hEYTtaM!T`CGWY3t8.'u[po,XRO$&Qqje-QU[uJi*?2YZ:^4C(C
%+Kb<\1!+c@[jmI$!DmJFb]r(i0k")pYgCT)SXOql)cK:<CqqXg39)-<"t]?TY*'/h/;b>4.5DYL0EcCS!H<H(KL.KRU)ubr/(cb7
%/?AsqKGU'f`t]3$1R/U^dG5e*]7:G*&lDb<$;;;W;5+9"5W+Iql_>IiL/;C$!)[;1'p`[JT"nOl-!AUW,=`5K[Bb_J1G?a6%CV@\
%BeS^X>D479OTi?riHKr2JoASGa[]e?nWln%<DAHf63#>3-3mgF-k^$W2mI62#K/).CQD_pC$#D@FL<5&Z\k-uZr5^-U=2g.;Nn3j
%1QN/a*5=*(K=%J9"LR.%TI!>hF'l$(oa"]f/Xg<0Y_Vq-WHh!VApaaA4Lu?rS,[&2$dT?hB3fiphoSd?%6LW7!#[8LMnS!hnf0?V
%dH'/p"Z@Ga-]'lC]=_-]AJ8c*DQ%^P@s&D'#LEZ.h9'H'BU?#dJ4h`[e0*A2)6RYI@N\'FDPd,/f_iRuD8Xc3,BRq%8$I+Z&pp<[
%+Hp]D=[Pb/X<t6)S;nOrOOq2YmSN\L%9taj!m`pm!4=URI]S]6-:GL!EAX$5>XCc*RHY*Z/LEG\nCZT4-"$ksI7/@rF-(hsQOU$P
%!1\X+O#.G`8ik`@;sl-m%B.*Hioet7(,>qJoj033J;i8Yn)G?TOLSEq]RjNci#2ljWnJUq2]N)ZM!=@66MGE8ka'\qKC$M:hQC&l
%E'0E>,Cfm*QucC;R,4`'P9gF/:;C$MoZn?V&!^=39FHQ]huZ.+"@/G.2X?;uI"^ulA#!n);Ns-h[Yd56GCS9iaujN/'%^^A'3KRE
%;t:RGM>7VXS7V.b>ee3O2'9+s2%X6>Y;o/0%A6&aH`ZcNd9`PGDfQ8+HJ.!<L_,OYlkW#;8QU.;>OJOCP_"Ln?t$(b%HI=b2auC(
%-F=&'K>L7RdVT!48`W+CS#b_FM:\&2as)b1Ea2mOg=Cq(KGi9/H\t'T>1_:f:rUsueS3eLo4MXL'P6JKE;pqAQE><Ll_#+T&1nsi
%"hpa("OdN.mi.E4?XjPpHF2p@!%J/\gPKRu8E7S_i><2W#l-0p,2ptmC9_G8qZ[cnQ.IMDD--*7'm2+/!JQ'B-\nmEVchg+<Db/4
%+a\^l%(+J#5FE;p2p$0>@*8s,S;Ck:Gs9omB_8i+f4S:0*E5=4Q#UdL906h7iD(?d&k>WR$2i''\*"kED<Dn$"%!pV5pTN/FBg'+
%`ZMjlk1u9jNJ;PaCK?1//3GpUXr^]+8dDWWV`<OhNCu#Udq*6D+fSK\&=/,'_h'0g==0l'#XWs=nd*B82B#+fJt-d)Na-H.IoN]3
%gXOfG"=4HWiNbApk2\"rZUdD0p%$/iRkUQ3_mgka3EbTrA9kf^Tk]ID4S*o^\0XdckNRObUUj57oeN0#Z8&3ETD1GlXVuaY&%'d]
%K,m;0e,:hU[)b<=V@jiR$I-USg<giJZj1WJi6s3`"DiBs>SjU3b""^2T?9ZmXX0\h@f8UPSI'&]*m;STMM]eT4Z@VqahWOk4pE(q
%Gt""`c:GPK2j./Q$VZ=BT)_@)I3W1i^QU/t?.H=(a[Nn+9sc<BdaX%\K9UM@*!%X(Pch7M%C\m_7\aX+E!<57,hDbtB]<pq=gW*I
%RmQ(d^8mAHX_CUD5O#pq`h4MVEL)PP,8rh25e[bL%cc/qHHYKT%@8$nflXHoRDiHciBpJS@64a%bM\CIGtf/TPci"(JDToL^Jbgn
%.Bn=87A\]GEA<sc7+A+:4+#dK*n.H)+Bs_n$3!;f_b8j3")Kl@^G$H=%N=D-iijL]gI+LZFtbdh&rM2B6;h"#O%@hKN<A60oLm7>
%S/u":f)@eoots5g0"Ln0s*+M%BA`^JpV6UUJ,B1gAm,/;s63V"r8$9/j_oY?hgY7S[t")<LMoETc*tIdp6U:)Mr4KhcbKA%s7lP2
%K.MJ@;#ZLmj**`fc0p-&%/B!)IesMfqs2#'\b#KkJ7O$Qq7!!rFntecnla-(s+C1<*u(gPp]!\Zpb+\rT[3Ythu:O"naCr*rpP=-
%g]-dgKN%n^OoN'0q2MmBJ?HbCTmdf4ieIe4PC:H1#S]M[U?:'5-,:Q(I=tceJ4!S&9d*EdU-S+':kc_beS62^j+:((2"nXEId7a\
%8V_&,2b8=,,N91G1"8YmEt#PW--;qbdS]Zic;:oA@7`sH/D5=!9tJ=Z;;QI\U6Hc'/4=";*V.H9RpKHGXY.@\4@K*1/0GPe"$<@?
%+`'fj&W!_jKG=MFG:l.K:>;,8@@HEeg&%Ph7H;cp?n?8MDCWRtD1EkS\)XVYJ4QMB%+l'+'*4IF"<$]!ZAm%<l"ZD9LoH=Fd!/(n
%.$1lq)\hi&<Hs5'E^.a3.deNP'O@<^mM\DP.&o@n.qHIE2\N,eo*jaqL$TXc/G#UMSHPj!<!7G5N1;11Qr8Z#`7%Vb,>c]Y&n8YP
%1#uOAHe?iBSD5B'/A_<0`sl&a;nj6%i02_4__N`9M*;.Up+:;^Ug\Ne+C#_,%QdG"4kr:F,JFJp)cJII3CS9ugs2@&M++n1n9hWX
%7'(;34;dVp]5=I@BpWq\C'!#9U6Y))_jL6e>7)eX]E;eY;CV7&Xl,?%$)>&UNrYFANaUIo"?S,'R%>6V$X\>[W`+?6hT%"jT]7#J
%)OhD(MI.K=:G.G6(b*<%lZ/F*au#IG9H8;#+/aNb,C(`gW>!tEADqX":3cEm@0f1BLB/,0B%=G$2`9ul:AP=h""C=*aCkn;&URKe
%2JgfJ`#X^^+:O\`P-CtN:T>s;\tj+3fT86hKE26d$&rL9.('F.]JfXXB"(GM,LAT%CcN?B![d8u'/FS3i'6<`(sc5b)-hq5QaBeX
%NTrI@WO*3>9?*V[:]=+n?9r2>H<J+%JVTH?ZJX/p3Jr--+s]jFC>(!I'qOds(,=Psb$C5T)?J40.RG37hNY<M!Ug+p5e[=#,u8`O
%%d%0gA[AC0(WoRBK<><AB78cd+XbmY).8M0h(iR5VF-5=8Qt%#K]IZu^'"<B(WGndGn[d'!8i`o/nndSfC.,-\^]q(+AA3`[^=Me
%0$Zj(i=rfm"$0[G#B&JC%'\Ck6A5Z?SWT7jpfV,[Kcjs,>#[Ic*!?e.@2*c1RnnXQ[1(!uU8!@GAm,^]m$!%NM1P<]LRh.m$$'A%
%TIlJ!LKuZ-fHi>dQd>&aDuRCsP4<%MX/l6+0`QWkoD)FH&9-ju3E(#+I0b+egN%o8+]Q0@>gQ&NP3kdKL#qkeD`/6S-B@M//%#&\
%/Cu`laa4P>T-r^9mb`4+h\<\Pct73YG]l.q_gWa31b^d)A/E/lV-u&Xl;B/@`M^o/<GksEf8/.tjI=dVN?!(*]W8A\!43G-]r3>Y
%gea]1Be7gKStdOYeQ&OOPN=-d:GmRY2DPF!oA&/gW81u3<O>k"[la7TT<#nCMU=dN(pk8s_/4B*DHmHIr/rI@M/#$scIq'KfG!Ms
%/HQ)0.o7LIRaEr:miaL\Nipq"lIDRZdW9\"aD+Q/F@SJLkQH1s/O1g5ZN]]9aCNqir!HiE9.UJckMFU'7IbXg9DXM]6EMQa9b,QU
%+:On.$Xh?B/bh.7ZQf0:N#=Y$pPM4Q>-&]NXRlj@]a+)Eb*TCsBXGkrFueTKna_JtF"$nnr^LsbaYuId1=]j+.qfY.gS5/pm&I&A
%&^XF,$jXj4MY7#\B2`W430FF%mpMt1(H_]\9M#RHNeoTKbn3VUMFos=.Jm&1T3j(O$-cU=!5:RT%>h(t/-*!<\m*Lb,lZI'Quht_
%UYI\:l_,fIS#`"h!p(oMRfqa.TK<L71m%q9QYB)kWI'g.%MdtXosjoF>7u47Xq!94LmDaN^2o@'%380j9+-sh.j4*G5#&#9q/#'_
%Rk8kSJS<aY@0H$3=c:9Oq[4L7jKbeYLN%%^dI>!_Y\N=92)$Y'PXm=rfN*]c-'VU6@8Con@:#uD<HhHi24lC?KVUP-EggmP.c%`$
%GMa"1_NUetqK4ebE=>7L%sm98]E>e'oO,$^KGQX"EA"IN/=87SK(r&*k<.7s!'_j5dP#h=@VpZc5es%O(a5n,70p"?`JRYmUi\J,
%ZZ%74iI3W!n2dqT5%MZr0EDI*c4=l`>MEPp\'^eDBHkjh2,Y5%b,$@Xgg77MUBgTK*//#YR&gg_p9V`XELS*C?VqCbKWIf.DJK&<
%,(O#V$cc;MXZ!0DngCJhFO\?kY&UNYXnP(g+(nMb/*qgC,0eW/8-CF20!dUDI.p/]6G`7DYQPKEg!LD?Y[/2>54G[lrAr\P%eJ#V
%]c6:4ju.XIMLM4aK'Fthm+@248(0"?K8\hg`2*A77^]pN\WR60VbgG9T\BK[56VY9YWbqV'uS8_d]L=jOi;50k]^<;Z\l'[h,AFW
%2N0F+mNRE._HZ=U`Y=AgCkpE7(id]CW.>bc`]hu3R9SbVjfli*`sR("?db!pgWC#UR)M!p>A@j6GP45c?V;bs>dK8CJUL"cY"cB.
%,IKeg9=aK/rbs`1_^$X8%MP>E93>J%8h5J%drNJ1a#6G,Ff_GE?a;ggl\CO!2'WNpQ'Qpa.E,kV@U<u')fGK#%2$Hh_h"WaZBWjm
%![cp/hLnm?L.dX%W?LM<2ZT!;ijKC^*CYZfKGmKPF#T.mn0*/<7P)-L83GEf0!\Ma2)6:c82ipFj)U(:@0GjcB':o^DRe/27(kbm
%fpd]57o$BW(DT=?pV)oK"(EMpO)N`-^VRE\1TI@AR6C[q%:`Yg0RtQG4:GmX\OIG-1V0m?aHuX^f&9oKpRZ,F5bSQ@+l#5Pba6-[
%5"4s=Rs5pI/]/YFAR0f"h&qr*F:Y_gjR.-oY.:6KKVPkbpk-]h1V49pgp(0j&DR\YP;QYu5$:r_TeZ'Cq2ume:KG"Ti(?Z6)*pe;
%qG0GCi>oFUqD,m9hseF1\:!F'79.3mZ?3-XLUr[ud%"q.aH@cb!oY_VMuFP):6CI6E\Y_hNdQY\T!g]],"G-%Pq$2c-.rkgRB9AF
%!P1C6TGXsV<YC1'F`o47fRQ5Mjs!j*f+;8q!d8u)A7NaNeY1)Hl@R:'["4g[oua[*So'W&bn$+<`OVf[73R!ul\Ml7PldE=*\\.9
%M/iS#L=eI_no8cVMlp3($"5dZP.#kOTgtKN3Qa*W?%[A<9kZL)_/'Td9<#'*bSN9sR%<XF`'fCH0G*/0)[fU+Ch;efDi*I_WHdmO
%%G>D[=o9at!6:J:9i`5"(ATpJ.Uarfh'P6NMX!Y&cVk<dZY`rjX>Ojs%^S=SV=KZj9ms3c6GOO\373Z#;i$/b34d#WjLQMsS*V!<
%>GVHT-[d\CKpg:(csE`(X$hSN/46t\CccC2FK%4$)6-:'/D>FV&d4V=5?>=#*&CaK1S`Uo!D%a<=J5@V,MKGsIc''e^Z9R"4%'T=
%@_*)6KmPW"F-DRZ/>ahu:J!es05/.Gb0Yh)@/_=,H/::m&e5Q1`>jNtL?2IS/5O>>Es^PaTTHX=c]O\fXJmHPq,RhW6I-k9/T,>m
%&uME>+t&No^Md!Sq9rM"O\6Eb!Tds;B`*Bq7&:/?YM;u'6S<ZlaY`;qP(<.gpFr6!c@O?D(]]ld2Fgk6WmE;pG>:/9P4(^iA0BrK
%fLn6^6r$%EMCi:q=%Gr&H&>Kf(iRjqIO1E/BTPY>KJa3loTf4RDMDE`VHB?:',&Q,"^OR%]?lT!]qOVcL&h0u=^,KkVU:f!:07+A
%\"XKZEqqGtKiQcLEJc2cq7=J7,H?&;C*hDpR`ep$^L?6V@`[a#qe^5W7arOI@-f5(T-;<L*af?(K2G:h3=Peif;/c(E^O!\d'Qrh
%c.VcLgRMF-)Zqle4O`<e8r#oA-oa?U\/?YD7D:]LeLW4cb#leGmo!p@9,20V&0:!Bc&)UjgV#O"Jr3]tE<,tuea4O^1%&aWGHSoR
%^\%(,C?Q(uZ)M:ULS`H0YHbTpP]]^E@h"cI?A2>1!FA4-(Y>S@5^6fPKE6BqqOJ)'0%MYYTH`X_!g6?AaIGi-$kn^G2=(*^et-0W
%i/%4ei:(Bo3AskLJfW(#IH5#(^5sBoCDJ6F3Y(]q#A&O^</j'&_C>7@S`HP,Z*1HNc/9)e1q9\jq5.,WEo\/DN&[E/_\a2NdTshY
%is*5'/?14[FK#c';CK2kod@D'o/,scrD@Q?f=_S4.8Hak8gpZJ&5g[E@Kj8Y7pU]5LE3(WDt$,P&qc%M0(<W,l+(D^agMIS*S!'k
%*\nsjZ$5Rb!uA=Hni[X:J\Jg3fujO9Ef[QIXKG>Y\lFt0BUNgRbZl^&\:dHEi;]af#+@>C,rB$=.PZm<lJA`,ADr<[S-4n(N7:kF
%oONjtR`*)9e!-?9^a0NU:m2gNcb`#C/LuA^8!m"]cN^O#_j,3&&>&hum<rNbBe<DHJe.<VCh&?R<Q!rQ4B^b.&[I3:D5BSX;+N[O
%K^;\2QF6bAp0XW-9XY.?N[UnsB:0r1T'E0eKWPZW(;Q5E],c"N!rh2Dk9q=#3VE#$DCV/bX!;=2L0]R.O)<DE+u.!;V?I@%ol:h?
%W@],ggI)"0e7.g'Z9gpAEXCX]^?F<`%3d/,2\Y4bAT#4UUudsg;R#o#Q%iLdO@(6A.()RDg>sI;42NGZ[h(@,>[K%/&>B66+)8p"
%bYRe(QGf=dPfC3EPk*$c<oK2;q'Jm@E\Nf8[dG2:9=pNl3/]\3D=t)l7[hc9%S=iK9C&<[=CXt0-G-;LL0`=OFRobek_TSGMi-Gf
%aj=GIlF>s/F2MEb?o_aD@llZ"Kd(i[Fu"+m\%D-)-1Fl03bF;5p+`9^GeM1rR@I`SG'1#LO!PHJb<?q8!d)D7M0,n#>Qm'4ZsdC7
%2/kqM[R4XG?K%"RBR->sdPNE]j<ZZ-_BOD[Q,S`&^EU)gWqD%V6$W\s^sGj"RRl4`eJ0#V=<9^3\]^l0M=8MOU-;l8@]!!ijO6!n
%U/Cl_'=j>ufQr-q+T8'qG'GNDi<lWd_BgM=mbS?%((fW$c5%k&Sn$rOm!uHi6P-Q>W:l4aeq!NMQnOkCWRF`S]cZlm]iI"ibaoSq
%:>p2L^2ZP8OK_,t/VT8=TkrLjj7?;bB!`)iO[eP=_B.gY5\c-;"XY.C^!g*[FIB,Q9.Ya"3PrBQfn_Q^FH*9AcYk#DA[s'=P@?e4
%=W472.u#GXE&Z6"KAG+MoO@#8-EX&PGo%JUXL[`Z*rD\8W&oDA/1t2u:_B&[,V)f#I"T"^-AA4X"q,Sr)%,d^L_:"_Sh?d9BPXh9
%ZNUFF6UbMrgItf%q[UdLA/L/8V^4?oc6R)R"Vt<Y0#TfnSmQabE&hTb?D=VN:.a&Tk\g.XPR_1]YWZX6a_'&gP'^X_$asOo]tQ0Y
%Q8UScOpFu3"4VChJqm&3C$f4PkN0i1^"7-c6;8=7C.Dl$INpn5P#;](0_&9aG*!8kAoZMC68^XQ$FJ%pi@ZIhm&&,<,$*T3Q^?kq
%Jj^NohO_J`HPh,4q)kde\WUVBgLheO<5OGo>kL[iPpkFBA`$e8Po]A43k,V-KHcc&9"&_bMP0duKEB?26mB\c+dIs*f0'[S<PrG.
%3P4%nFfLEaS"'/T+iPSY*)<YZ8(5#BpN+G]J!A`NE?[r2V.3tV/LgU?ptF=9#XmAV[SPT,\8ApF]$qp1`o9^"7`6MrJoQ-qnj9+f
%_$G_Cjd6l#WR@:%c<=]FjSu]UE^d,+0EXd^,,YK!-V"X+G%:^:o9hlFL>;2Ygto<;6<(IXC7im43qB7/ldm-APm)B`>S+"U'm(Mk
%2Fp[G/8u.A'WVnlaBO*YrQM2]XqR/O&IECAV?HUB&N1ioYSl%T64+Fkh?(`2()f4,)*mBm;2s>fPg(.GH0.sUCN+jMUiuf_Cu$Gn
%fJi1G[DLSO]O^=.FYk.qPgjhOlRR]iJr^B(7n78kS^V=+,sRkb?%l[j&1/-8&.?5bS5a8_=pr4rp=`l6A=,)Uq*pq4]jkot,=2RR
%A0C\no"Kuu8,+^OjQ<MZ$oAUIEJNW`qY>QOZGFNp0STRUGIhU1&Kj<9:)p>[0)?p!`BT;DqjM^Tg6%IiDCj-"d7U^dEtHE00p=`Y
%[)rC?Alli]6^p(j)=;hV(s>U6]8E9,,9Y3rK$;oGItPLenWWY*Kf"uW"#Y4BjcF(9)a421NrX[94PN(FIPV,#`Wb;ebAU+>_X/W'
%"gimp:r&H1U?GD+bkRLp0^U719n[F)qC=jBeWUKi4"eTOi3$m)A$[,]<ehn&.9Lrn:6#<qp`er<A.d3D<R\9bUqHumEY%^9f68Ar
%)&+N\,JY',OVHbT`6?_*)F@[mmj#Q4n)'$5K9*#p('o(23^.V*Io9VM%li(!f6\GW93nY`jbWD4FKDuV-9Zt+pe!$5ZtHj_bE,FG
%TP:-9Q7Xp?/Tor<Y+Rt4Mn*B8!D*DhTi)P?1fA`R1Wi)u=TJk&UdCteMSp+VC*AKY.,=DcKf5:H!-,kRH@5nK&;b*8RALVSA8_MX
%Kh8#.VB5jWYu%n5._r1L:aqnfDYHh+0b!.>4bW#OIPGr;S1M#paQf+FMN4Q)/DumhVF&@_U-jaaWWOL4Wh:lT+a&bI`2)KC.1j)k
%hNg/Pa$HX"md]Vup'!^Aj'?Lr:48#/<EMAIL>+aX%ZTe.#i*A;WD,DD^a\P&.Xu2L"V_schMR1'Bf*7*XjJLn!%&QC9j(eU
%P7(G4iU[Ee-)D'7"-[0+V.(Z-\fe_<4[?=m>te=B""hK2Xs2RlDucMRAia7XNMm-#UfQ!G+lUSeR6;1*h1><S1^e'fGa#4:FVGr/
%C"/h&7UoWp$Y^-8+P\c(P+fJTKuFX)[(/>d7Z1Ueifb.VZ6;IeB>+Io+J`68Ldr;-Qt3[q[g@j/`1cg.?)tfAP<$=aMOW8I>MKej
%nlun+A)fd<92ZD)O0T$&P3'R`o]QN]CT->Q!qD9GCb&P7O=ldb6JIKA2fc"i%DYWH"337JGRdH`Z@_J'A=oCHr6bldo!:0+o[QU*
%L^mbVU]cCID"eVQ]$\$15+c5%2]L;DBXg>L4uTAZ,pnb^8Ep1u=\%[V#c.c57Vr'+Ksce.#](-I!pW+,FSEN8326R<ISM"e/?V5q
%&3=bRk,@Didq(8Rc?1p%I)KuVOQ(/\@.Esa9$mbUV_ImuO_\L2'$f*0'/OkZQ'Mq"2O^P+3@7%LX0<?\!U2M92$&?Y:-qsIh^YT8
%p)RERn\UF1[pO(g.N[-BL\+5g3%;@_K_^omPAu<TZO1`jo4IO$9*!?r\Jg3eG5tUi7nM*!#i.W[oouq\/ECW[6TSZlc$j`qlBD`U
%,r(HWW.([1+`*3e4#DS]Y[:@"^0;K[dW.BhM-Sc:Vi?O8-@B<M1)'u+O*;.64'P=dFMCSK*=k["Ebi3F/7T68";*(LI[50S@FF8:
%kHoM0f^*Fm=*uiVW&sgs9?MrOV2+62m22M\!9(iE[8?D-oOp5STK'\"]X%B>"%K1%$ib>]OcK)kiWCnpdZ8G=lV0L9oNZG'PaWhF
%C:lc9GN8QsmX!UlQ^qKlE56nnbu2D9cdD'@;-KM7KtW;CDg)<7W0SS<fnm0F#ec[`?4&!Kj-#?0D@aSpPfQ"0d;bq";9R$4'S5eH
%fhL%k#B'+_/uAPP+URAAj"1!"%95i)c8tFlX5?PZYJ&HBZX)o_@rG&uC"!/XZP67SpF#o$UETou,H>i/26X0^.Ad*G?1j5[/sj?<
%\@BL2,*>$'5C4nu9=YKR72+<>]9N7ApWKCV*';?5P"D-[d'K3AH?Xm<:.6m'5QC>Z0$55aQD$V/BK,+n/(@445T4iq6n-I4:9q^8
%0.%H()kkUG\T&\u#i"_MbcZ%hTt@-O50%78G+a/G2o0%s;mBk#fCcN\<)qCJn9<k2N8SXOBkOlEr++-bb=99M:btt5C$ZF6J>P5;
%a>geO)><'W;1EYF.Bo''(.qK*EAWmjH],lG5c#D5^=a]`eQen6e1lLta$B-I27jsR'[3+Td@[q!4Hq78hk366W/,At)%!E\[..@e
%#.g;!NgB]%623@G4UgR-qKJgmV+Y6&hp<<?mpq9Jjj/2+46GA$cf$jZ&jAW4X9)kQ8A.4(E7fHAhi&+hC`YOP:Jh@?H=\P>JdT;b
%:,ZSnN-Q:E7u55F(OcQ;T\Qrg%*l%4?$?`]OoEO7N7r^q4/<PFc^WWT(7HqakLM*uX"bm:KAF;=9o?MoQB]K=/,WEJ`-;-%<ML@&
%.shuQ;c=PHr!QgYr),If`fi^]5S%a6&)1%J(XC6QMr/!1/&@No4(-7-]q5=Gk*Z@<,Pc1TBOTCQY\mX](kD>B*?`Vdq?WCDCJZ?d
%3@cb5C?clR<K61fKrT`Le/c<m#(u9YRDW1PA[plS+ia9.AKRqa0Md4PZt(U24hZM,@PDVZf%u"J(VfAf`n&&]W"pHC-6V,7c2&YG
%3iLjH4p`V7R?`X/GcXAa*2ufPV=R3^)J.R,Ih\q\"9$pIs$csP#AO3*h_@=oMZG-pc_3Q%?T->.C*UD(qIJ'"ou]ai^BXoYn<KK:
%)fb@B`t;G58#$24L(V49]QH\j"`QB^#:+JuL2$2&Mec;"*L,N_IM8r87dW,IOklCRA2S1bFZ,SD7+G\nc3%L)BQ(ffa>]cf+p$7b
%:NqQ7&WK(_GaO8%7HlqH;$>SlhRu6,aC@,j#S[BVB\b\^#%'r]+B=+tY8;pMe8TOSlVkM(kMB4&#hXu`P[s\ARMJRN4FVE?JWM>U
%lT$FZ`ELmL_u4^<-QZ;CVB"^_(G<a-H^n/W5-*hTL81cO&J,FB^^qP`YBQWBNrT!85V.KOM]G^7$aYG868cE-:u-+6,['7+n5:(o
%0!$^,:F1=d1-?^Z_*'l#,Yqt-f>T9-jl=G3jU"EQ[eP.AhtD).b8BX:dOY4sX?sM&=mQga>o79UM=O]r$uICKLXG;,>)9!hdIgdJ
%WPP.Ze\.J(j!:CjCeYc$hkePlW4.erJ!8(!ZXP1?@YlR8+'!]/ngF#lBqp]8I[2Cre`be=mC(il!9ZD$1EB*G36D_)mh`^e'X:&F
%U:4G,Y^3?D;Q;6*d;M0+TZ)^E]oNc\],^*)>QbUA@GJ-il62HC;4)9XL%99Gq*(<\%]-#O_<79R-^EtC$_Vf-CBMS4)Di[<9kJ$1
%MdY@@D[4F<(*VOb5]I91Sm%Xm0Ig!S,tBPIcf-^BgcIeQfiSV2(05rG3I<:<dHAiW)??OIYu/(ZN^d&gAA)dl0_"5&eS:\TQfRLR
%Nc*q,h40c'DU,j&3MJH$5*Q6:!KC/r[d[:uI3942$lM.XY2'+\UOY1\=_?GkNVe\t.t4NWRPud+"nr\_!(M1.Lod"g<tYQ>CmN/S
%S!\kRKCk<NpHelMAFS&s$IZQ/W\q9(l0C%\5Vk..;EpBn-^\8l!g/#i,(&a*-:VmM:36:c?0ZP8Es$hsoE6djF]XIe<Ai9!'T+%R
%(R)h4iTUraM>[kj*q3Uaf?iXI@@n.%o]L"lW$c>WMPRH_eq"Sbng0&?-LUHk&gg<BqkA9R05C-9W$U$(cOq.5?Z&*W\T98X1Pi-Q
%Pt\Y0$"+4]1pFe;i%?pTDKM16lkj/VO8!=qMOtf63V)ZI3'__Z,,]3e-X'o!$BPp$V]nGe`.<M<M:,aSLH;q?2K=ka!Zt]&>bl8,
%DC,K.[Z3@>9s#h44&/)uEkpPlPdf;OU6$(7:_T7[#M?'4;<E)GVLe^j""/e?fl`Nb.DZfc%eQBK&o?<E!DOhkUq&Em6;pfa$&&O`
%MYR&>OO%WP=-@V+eTa)P'oa6O$Bo%Dfq&f*LfAEB0L;)pZ\ksJC(Q18FtV)@T.'7P/g0XZE]&u\6`irgO\Y$P6VKYs`.>@NUaXBP
%M,KZ[HP2WpK9rC>+g*bV_5`]Sd3t128`k1?dU6B:d`WW6Qm=*gg^s&tjiZ*^"e@HmICM-,cm[^n/VIr1XV",%qj$DmeT(XW2>,;=
%JPnpg-^s=_d)LR,%;upGBQKZ>&UXt_KMlI#/TjKNA`:&/coomd2P;f?!"Y(!#VXd[m&mK0-(7M8[cVmQTM_$pfF8QoZ@f-&kN3cg
%nEd1&9u-d:!65^N&rGnKOk4G$LjJ.A(:u^js"ZHTgE<[@9PK``$3)X=`GGLU?=sdLF\HC#4h!38C=kpTIJ1U(++NafVI8091L@pm
%]Q:^`Dnq%Ckl,&YUPkutG6J<dLI.UDEh%*nn8lk!2NS\`q0VO]$/?-J\Uf/7^=%*lF+Y@GI.W7-L_4_*Pgs7a(s/V%]>#pu9Z9#2
%[t]7TQDbcV8ITY]Ta+)"(1+eG7P2dH5^D*1=(lP#]la"cFr]A!C6:\_[M%C9L+4aaa33a$#MPYp7gj7*Br,ojE*W3<*&H.p,@-[-
%8S^R3GU-;.#e*gc.m)V4iE7R#a.sW[/umCTH]hR+'&/0>/FGeNg?:6[@5T("Z<?<YXd=Es=ZoqlHj)7-5_Z1sO@W@$a:HRM>.QHI
%HI"Y5C*H1D0seS%\anK*H5&qa/7(oRA)%PjX=ot!1js4<XpS@<,i=@r9,+=JKgBrN(]VDo+6JZ\W6i?i4%csiLsAO-0M/OdogZ,#
%j]mlu;F_,7itXo52Suk,1TLtu#Sk%]BI?^rd6T"JY*O+$:XW5Qdqmm,E\+P.IZ]MOKlq@s<CQuj,m-4GFAg>8]TZ,])mChFgETH9
%1U+0k/I/0/h_EO"UpCAhDG(qQL"1$IccV,A)'rg[.l>`Yg8Q#>mnb)OlXQ+p&jB=gL*d$MR8f=1h\)sWA$XM1h46.eVdtQ\C7dV8
%asqrn;W%OqM!\dtZYo6l*S:P\e_R%s+IiRN\4,ef4FK'lAIe`n3Er>%U$9O'babg%BUWKeM`l$6I9f2BOf6dB%ORP)iWpTX:Y9ES
%Kq\sk5O_=TSI;]Jc-2KT(4O:8N(S+<>;]"N+KE9[&PuT4*7cifT"+A>4)33(AaYS$Str:4rPZ@6L__oVMt>cO;b[9>W^@^k:!E"1
%8/!@[M`MG?b2o/7rB5!fiP%t0+O)h)=fY!,bbpmdY1nFH=iM'j1?<>UJ?u8!mrU,1BIdY6a%PX9XZY.;,CFOcs3/o&ouG0p5UlVP
%8/hFPVt5mPl?4pNGl[nk+$qKFQE4/q,^[SaYR!bom++HK9d5R=e;^l:hQ$r9fbhq*//t9N_Sk01gkis<R<ajFASURBI]-r5#.SCo
%Y8`^<nt2\NKkf>>&=X^`WXU+a`OU<'ao*A*;EY%HKH]q&%c6Rp=EjDY"%Bma%^RbKFA70"F!HC,^*8Y<(\n?IfoFEXdqrh+6E916
%d-k%;DlX(O"kP*^.Yo7^34ha/iK_iApmA.*3C4[3FU&?s;79O3^[?N_f-ngO#$EbhDEWS1S>04H&sift*j[r2X<=r^-\gV22TaFb
%IoMh!B3U@A!@ItXe9urqQB?@fRIIUpFE>$.bUmOo27KEp/5FDl'mkmmH^fkEk-_rsj^-0aX_TsC,MbnfWP<B*Ips[`:FL+Ec&.s]
%emi_Z(V/mJf&H.?>lYm-G.'\88p-h&].M5t<Le\JJ^#3WR4#:4nh=Ts-LN*<]#_)nM*W1Pm0!t@1PEeI4!\gOP38=!!"bG=E5E'&
%EuSRgEC4D[S6ki=+cK:E<X[@]Embl)'.ud+iZb?f9D,i=q2$'`KA[h4XY9lc)1@T/Mt`e(@pSp84^Q)d#,5GT(oki_8@cgT]*%F`
%d)q2e?Yh=JM:`\o7l;##HHXQ'c<""Le(1ZkJBE"m7sOZJl)?nZ>ANu1#,#oFRHNQ0osQuZ4`"_iXPU?E2a*XMan9G%r-HmnEK;D*
%>.G&[$Z&WJmbj2-D$M#O.UE4Rek*B4T&fM:RB?R<@ae>Q1qs@->"$U]f30qP&LGQk;Yt.#nL%*`$AE5*8K_(V@.Hmi8^@r?dR%$H
%%tH`SoO^-3-R(0rmYd/VI4Z^+A?,.=Y75][7KEanf"&RB1bT&6T-/ThneMcdKZi6:Q(0CM06;hB[OR?"%=39!^^kHcfheVr<Y,a`
%!j@0pU_)/;*J+0=,mU+:]9''.PIR7.nc\j$SfiP]T31;f"[)8hQhYAnnS^(%$BY4"3GLEK5Uhltqo"gWb"e5\a/2Hlq+7k@Hf,\<
%19k!D9jss2j4Cq48E8]o+`)?&`ZS6a3?jd+ZSK,<&<!rDYl$k*gt(Rp1[)_sHB2e@STFH$AbJcVSeKj/EPa=1f)R05'Ahg.Ar7'*
%6Z@*eR4gMX]c9tD9A\&:6Ll:>9ul/h0#UCUO&T\-0a?=..AX]OP=7RHaNAaW#A0\AlDt]/pMFXQ6XcK".))R&W+s+j8'!P/c>p6>
%Ujf##*$P/\EW3n[!aNN.rV`+$g9.n!qM%0\]1m6gdi;C&Fs6hMq``&7>Z:<12]c$UDbWW1eR"+j!D54#&:F-V20^&bOONV'@/(L7
%*jU*eb+S(j_MI$FC88A^liGBlV`R/S7Ib7s@(V?1)N_'PMWb-Nn%8o3r-!FDCHCa=,\U$pKb\o5Qq4"+:i.C_(-0kQZoeGf,msGA
%[Zu($AC9:'[j6VQ\9_JU!YAG`l6g_$I`KB!oS1P,Br9l&E<p-,?>Z[5X2;:4#ZSs$0Dj>3Vh!,m\=.o)m9AuaWCEe%j1ulQM(";<
%RpmTnKdoq#T;(uY5/NQ%m//D=X&aFu^sn=RVWUgAY=rttC[?ZKFU+Heb#uIc,J&d$Wed?U2R$lJaa*.(!ORVTL.(O"04AMkJ(f2\
%?\(3hrc%;\)u?ifJjA.N]ImGR5m2-R(n5P2bbrVM?TJ`F6UD3$#P%kmW`J81(k0q-p)`;J$iJo9GbAMB8*FXOH`^u=EJm8MI[5sa
%UoJ6$PK.3;s,e_kI12de/EH5n7U_10\#^ndS(3\9M+R(4(\GdE4Kf*DFD!6t[FQ<s+@!EXIePZpT-AGZ.Q_r0+Qc0LFWK^lHb&V0
%k!qCnXRUYX@%qsKK,0#\JaXEo=!ih'/c4@G45UW>T]d!LN4t+k1_4Xr.0l9f/h%eBI9ZVPaPFBGr$lI6MF'NcS9sGA&@IlYJno[_
%XR%!df!a5m.255='0-:071P&S_E@5d5uY0ji'A6G"8QVb)UmZI&d_W2oR&0##btd(T2!>EgEn(65fK4==>9rYArgWQ3;Q:`FgM#Z
%d=f4?:qnWo&l_;:%pNUuW.(c(3!;\d`@SU7k'16bk#;u*r=MTrkqTV7GTV""1+s,AZk8VS]^#*g0%gtQ77(^liBfPm3@+&OW[&Aa
%:_bPA]/s_AW/:>llb4A)2_gL`7ElAN[`Q\IgQ#;\5r7(_7Ors`,\a&[/g2KsRVB]lQZoaIN4)\%drA&Md<8]."Mk,m-2YAQ#OF2X
%@%errgWQoUK9:TKZSue5c6H.t?Ku4$>%[CQNSr&VlCm21S\!C].tpYtEmG81<e`88;bA4Lji(&_"24l?rR0`k]8_B(2XA0cit?^;
%Q:-l`TnNbcJi;7T)fK6'KI7riYqAYRX\!)\K`Nt'JY4&_E0;[tn=XS1-NZkY67u+QU/f,=@253s)k-8OZ[.+)@I3Yf.Dror"o^XH
%o8PgO8Qk^*!7Mg32sUs/ju9'CH\$u$b30+-JnC.P,D%/,#L;nMXKHWS#*fm.MH5@%M='?EkEVcU?Ac`Y"DM5;\M:5L_$F)R82pi6
%9X8BJUJDa'0ecXj<#_OG#6tTMWS#9.'H>q#hL2Y\X,KKe=5KC9r=[&ZA2]aiNeXP`F:eJB4T2#/hnRfsZbq"Dr_fc!Uo\I?b13/I
%!bH/Y.Z8_>oEJ#po'[EV(#4uES([_#fjIAW1Y>j,ZatGQ`9ubJ"7S[\!D.Z4GF)"mJ8ZPIkDm?95g5tW%J=a,L(FCA.GA3eQjuFu
%KR8@b*AqJqC=f@P(p@0q.Lc2BL+F$tJm%VlbbJGJbcaj@]o-.8X&ag0:cu1&JTi%VPK-Qt(VDD!#@?EW)]*O8l'[g]':ccp:G=c]
%`$5cUCq26(PRXSqb?.'aJ;lT:D]5u7>O)6D]gtBk]3Ft+6>C7BEHGLXn$n16^B1_rc/lFOTgC!CG5JThB20j6hE9H=cLl(l=I+-o
%A._4%RD&6ba.[FI%"UMZo)X\6$][P4p0,9lVtTaY!s^]m+:Z&32-`I$&6gJ?\a(G;P]jV-aZ$TP:YcfgF=2->XXSe@LQJ!kD?YRo
%>C*@%\@Q'*$f-\d]?@P3$KsKNpkkfK8&$iHO_&IT*dDaY*^C1lp)Tj;G[[KalrOPRL^gn"*mrdaFb)?VkO$M"ZUgWA=EIjN@P8oe
%2L%0g2=UFLRnNO5m_XWUcSTW)]@p]u"XXMYQX=Qtc3D.aN"L!")Y0"W\r#S^CgS`6JjYl&$_m2AMUd@fjJdY8$5LY.?6g)O;kgHt
%5tal0DC.t$D$.97^8,8u]WNu@a>ME^Mm7_GjD@NMH+3b/%ZWlg`!4(sBUZ4Ic#jlr&gM3JEpJ.tJk9AoGCS<ElII$VU0M+;@K'qj
%i_MY*Z:##=Ehcpf#k$V#bU[A=8^OC>@Tus>7HVdg$(lXoK`g:Fn0Lhn_f']QY-CQ#%RK'kg<EH&2N?jH[cA:t'aa?D6K($M,R*_o
%l)MfJkHtpM/2pDS*A"7d&SR1NGRrOW>R#>lkmrVK5!O`@%(5[Tc)>*-J0"TLopcb[M^l0$AY7n78"C7,F_dD?rga4[.NNn+fF]c@
%EF+"IPR&hd*$^:1QGU*!53jhGU:dQUSVb]1U;j[`7YJd/#;#$78"Dg62X\Iaa5u,A(Z%"$)[c*_Y2VD9^"$9o=*P57`SP`C;BDsd
%d.'"159/0Q8GB_ZQoR@@ioJf,;(.%.6*`;a,[f@?EIbG<clA9jc<L2S0SEU[J>T6D>SODsbt',,-UktE;5d/@cZZpD[<*Q'#X7I#
%RAT'u!!SG,LB/E(@2.',9KHqlj;);c[MiO?#g&>I=;oA_>4/dF:sL*>Qoe%52D0-uE0lh:52197S87J1EDq*%@5,XB`1[J$n8K"r
%K?2)B+h>]D4Gl(4HqE*`mt/sRFDJ><MjB`mnZ]R'!?8OqlK.Z:%u4j8@.N,hDk:Qa+ULYrn"mjJhglU[ak`@)>_lpATs0i-0o+^l
%'6(r9i.a'X-MmLf@F:UK/J,7Y0Oq6;$?\QHacndH:?o'R4mNi?)B`cRcoS%cV=u$>#GrIP.)ieVnOMEJZVaSi8A1`%II5_7722*D
%]Blc"l=6`qlb(^q?n+drT^PAJEI@,sJb(>$Uj4J5Y#]r'c&?Z[lBQMk;:F<9[$s@%-G.HL=#QR8dU#O%\-qI3ZG"*#YJu[I6@!YL
%1s"iZ6_Y"+35*=p;W5Mr!G;a>(K(RiM%p40Fda@sMB84VT:*D8kTcEd^Y!G;\1LZ0On>B\k[DR,I5pU7.P?GE'>=,N#.MsUXrWSe
%:SGo1fWj>\jIe4Y,l3.WN1@b?OEY\\U"V5Z9a;[=g%U4fg"PLZ\E1@L^$m/eYG$?4Yean:'!;r8G"k\m.D/guob/NuKNrq#^c]E8
%:D0Ot/l80Zn@N!aN`$YHLD8?fBc2T4B;5nN6*f@+Y^-RR3:@hG208Zg[I90U#]eXeLc:+pO(NV@LmJ:mYh9G*!9aFpYaLL!T+k6t
%laC^_m\?lb>TJhkZunBr\dH0VNii0t:`U5pgp1OnYKF7!S3`Fik.Dq#FqVZ;[5d760IV4Y58U\!a#S'F]t'DWick_)Fk&A2V"hH[
%CfU_W0R:;oftgQk@UEjUjXmc?-)DD2;S?0]=2fllh-!`3G,TNZU"U@qNn[JQB!c7=&Gm+S$-d/C=".t;(QKF-/KDd<n',8I3HXt=
%=0<!mAu8Sie$T#4kGT1C$r.-COhQkg?KH?!>O\%XGR<S@L%uTO)'XOM6WRQ<))8rhS/6_f[7'8uH''H\Gul(TL'gmWN5BOX[G[Ql
%0.*r9GCll/R+XHs7V&6s(nn=k7r-Ko&bSD;.H36s%HMS`O5P%m]ND2]bKFEC^ea+5JV&:Fh'OVi/kJ<f=!K`+AV5\4&A#R$B,gWD
%INegW'GG6=U)<gt7+fU4Jn>ZbTkQ3a-h5u.n`)(23#VGN#Wo`lGZ'Vg,NjkOP4(ZQgkUS1b#r0tnc0,`_c(S3-VtV;i#A?`Hok-k
%'YHqIok/eIX8qc&<F*/.pJp^C=CtEnTZZC2QD6q!JRX`T@4*^pmS[@[EjUG&;o0Tmj=bSP8IA%FrVhg"#$]1']J&6)aRt!7,PW&*
%E5Fo=-RKB-UK+eCmODgjRS"PjW5o;(/,=@r6eBGK4Ls-nn`Dtmlp2l5%I=Y^\QGF\n*CJWb0j2U:S-B<B<`TuK4cFA^B:jTo/qrK
%i=H=m,Q)59rmX^andW$rP3>V)KNp*E+EnD$)\n5,,,pKiASglG@IKOVL<YK_i+&WPl`OE%cR%+s>:9]QRH12.,H*@3=cC"fI@)qt
%R\Ah+mKc6:FtA+I&H0Pk[l%B6Vd'RUA8$;Q)!''/h-#uOOS9#*p5$qtHF;N\:.kI]W/:mVY8"iqqs'[I0j,%Je[_[qh#-rTZpWKb
%[Xn1d,TKLp?%S9pkRUA4c!MH'M.rh,o=M/+UR[Ph8567oio.FW3EM&]+H`!+ragdgg9456e'/f:^7@>8nr#(n-mkDZb<'Mt\<s-e
%^GB^):/7-rPE]70'Y`?AqtDIe,2)sQap@BkESSblKbgjji\>AdGuM8KEHsM%qJ`_O64u`b49c?P'lrK*.4gdMm_1^!V,@leIOY'`
%&?6*p!F(A05+?i9b-b=GWbUKi*+?A[Qt?To]V=p+i]GCoP.+(TFr#nXK/_WC:QP/kEgb5DER<ep]82pG;F?"j*.84b<U!D65X$#0
%kHpV=*"TB9NjF.F\](qWHG*b.F3L\319$$HI;B8Hpug+krMa^8[_*e)',#Bm?[W3jQ\q1d2*qIY=PQ]!7aqTe$+F)'%]/#g?c[j3
%Nsdu>,tbMfU,$Q7&'`j][hOnml:#t#;4;FWNbMf,e"td@IOQNBDHMCpp4qX8F;jRe:LWnq)QlWk=$^3-O$&R#Knu!4Oht#no1@f@
%''Z;C[79#TM[Mh&:S&33N@?&mcKt+\ZOHBQAY:pSo=I,"FgP/"C7,nu+VQd:Kt;LO$rEY9affJUMo&>4XEi*`]`C(/:OHrA@T%jh
%BN0N(]Cc_EA7;NkSo7W(mQgiek6TprQFa'^pMDHQ;G.lMS`E#S&$sJdjUsub!GH/Hm/gro\YT;m/1uh*Ur$^G`T,WEAC=c/9Ce9o
%dI-@ogAN]S7DB5@lD,l\mNNZ$5pd;7l%p2-]K^U5"=P?n5/r#\?XYOI0P$</,9a'^"(nJ(qeNbdfTjI\oH%'E=/,?Mj%MDgG4`<,
%r/TuE;-27nHRCaMfT'TqVW^Z$c`oa_4_BOmn$>`?./Ng"2NNH/T*eRqqtW/IaWI$b"9WW,0M=W`33eeSUre<%eZ<!e#!WI2@H%i$
%icXdk!0KkY(ARUb]ii6A5T,]02-@+&Dt(p?&u6;)<u2_V`?h_[#YR(f#iJh(Du2AdJt-\>M&bA>jXU>6Fg7pi.)50r-jeLQl(L\Z
%ljCfGg&<Vbib3tWX'I<2Rr6K>iqW0P4[s+OcVF&i&_u>O_L7.0$=t5TGfa1ZEAAjAMb\IKPB_hu5dO,-HL]naYWiLP@rgkWZ,Ah9
%pp%5Fq,iWuaajJ%^Q,`rfgDK?$%;HN5-man\TA)MbtAXoZQ$tK-:WUT^+%p!p[;f0`#cV2/!-Z1H#2^?ZD$10j?N<9MZOT8/d&@[
%e@:%0Cs2?aKN*CHRJ?.to2,_U,cPW^<C3<N1.V`eXj<FQXW^XQ.tgT]8n^][k'Y2i_:1K0hf;S]P9-,$GhX6lL1^TnV6(:Y49XWF
%\r`F'!TeEh-012^OQI"*s$(jKZEm,c>_g$c/ak$U4qH"4Wg/*@#hn(q"`LCE&uoCCAFoa7B/#7IWfe#\1moJ1"fj<TO7;YTL1BD.
%U;P!L@II)\&I-M>G6NWWO@P*KS*^W[D\A_Ze%Vq3,oYkn>pNArN,%(>?CZhi@5KnU>M)+D&>90?>S6cHgPN@Oh)I50=]X*4cJFg1
%RK\3:&O2i\)n+'-nsqFZ&GZX68!^Ur5Sr(l4[taH'23rAi$+fJ\@)PQ@/W%i@<YWt.4FBOO/6;[UM-Jh3Z*NupbsKX9ea2dWnq;#
%7B.:G<0O5<..RS\]9SG!1[Xe&l:rA/[%1a=;k(fOnSLi9d*[G?nQOb;G4B.l.%Ocb2%(eJ1Gm)OpQL9Vi8$ilU$be"l*$L0nf5Z6
%?%fnJl[]Ci4/S-nj$@ZuRgM*#L\\cq8L0;^'Y^8<R]`(s247ebfo',mgh\R(l;u^QfqL4!G&(R^6U(SH]"buZ50[a%:]7ImYPI@L
%I$CmcrpsKucgMiW7_0Zg!J(cU,_KtP@$ef.4&U?cS_1$@_-"Z+.]ouoR%FM9Yq*=(6\HZMkhAGd,ggUM1Q?t]@<A"aQotOpTatP\
%_>pZ1MWM2OeH06T&SXJAIb8KOHr;3-,K.Ln&*3uI:Q#f+Y:8+cUqR5s2'4/J19+b\rG;cCLhKe(@k2%S3[N1K:^L-.'n/-q:b_$e
%4?)Y;`"smJ8=ePp%:dRO)#;EYfDM)pGh3u0(!eR%S3s&(Pi&Wun,5^Ah9:QP8"C'ce&e[R_Zi0<0^]R&XmU&3>u\j31BCO7E1#[I
%kcCiBc:CI2PJ*P)FKlkm-N:s<cn=G.F2c?E4<-;=qQ>`q(^4YUKTTVO"@9G0a5TeDN[KL9a,jggkqPRK&"`feS/D\#O,A$Dk+%Fe
%_eI^ONW's_\fPn"X=u-)$l[.p)e<<a(SQ"c<6:Z&6B!8+rBB-7M?E[k(=!Z!':`Z!^\6nucg,6UrOY,eO?6E>POo;.g]jFE,UlhK
%+ojMh]J]`!D7+I:Z,asW#Uta)IY8]M3b&JL^Il&0,*>h8V3lnp\@Tsmc4;\i>H7^jmnXcpWJ!L5hKIPW9nXNZ?Xj(-/'-#4.3l[)
%1n#]cW7T\A11Gp+1`#"HXaUH&(*P91`%K4d,l8B+I=07i4:(TGSeCB.At4LY-HWPq@>,fg+.khXd'Xee(DD)@%th-Gr3i__/Xtd/
%5GB0l4oUg/c8hmXRboZmmsT8I=Mg.F@Ogp2Zr96kG2m[PD!LA*65n!IGY>nsGLC;ho6@fcSRii[j\!EmV]*lIgI7*$(eGI7,IkRd
%=VOh)&WX[E,`?%4]A:54=*]g&fo.;*'[p`.8"]/%25'AH02!PID`er'/n@B:TJ[6TZ;-i*:;kGU(rJg4OYTD4s%o+G+-$1nSG'*B
%$tb91Q8B0dHD:8[e>R'!MV^YQY=@_Ia,I2knIZ7HEg,C0ipV9BY#V6$$p=XT1]BE$Or5Xc?#<mF0fp'ub:M!mNM8jlRohG.!]uI1
%9.qrJ.`:+TrMn/9&G)JA*WCJa?SC*uUjoVK`J(8kXYs#nc,Tk1b^&%G,kF>Y2UbH9B>`KF\clq[VgrdKPDKcZ'Hn"%`kPT0;?P^!
%L-c=h)-`%L#SW35)L-q0KEggEYD,o)nk2_XO&4=C`V3s#rl5ja(:'uKEgE[:5E0QUUQJqW2Nj;H@Q<qcI>RHWcd:@fLG(ZQMSR](
%q3p4=IZC'n.U,3'RtgI5eIejZTD7\?c#JQElM:?EoEVer>0X7mo*<:`D,[B#alsN\]3Gj%j5pE$"gU#I?:q#PQgnR2<frGZ2<i$\
%ItqS2RDop?I-pe@THkQFlFZhfEIIc^*Ua;(PHY#%f)*_pmcTtA<\lcBf`Lbdg@$o<8_\!CCQ;,&1s6erU4J#\=-e`mjR=V,S$NJl
%;$t4N`@qLkfO``9>PH/DQ#Wd-*r4P:)KXT?!+p(&KdGI_p)'1I$\gjfkd6,b+KO8pE>;N]KpW1=2+)iHGDsaFhG7iu=!6nYY^j"6
%jr[CBTXF!#Up1+pRV#@5/8BfjXj<BjlW9QqXhk@Y0aI3m>)#G*rGFZYn1g32L@O&:j)Z-Gce4/!-JhA7B_WT_KCneZ[Ab@dOPm[X
%7!>4F]6jPQB^`g[&HKg1SodSsj;7O[<QMsoEWRd4!XAHAL/D?L[2CL&>s7Y5hUg+Wi6'Sa&,Yor*S#H6A$L8tm?\lDB'%eh1Mb&Q
%J'!^7NFG*J[5#0i^TjiRpN&Zc-9mU0JIG[B-:m.>Zji8Q'Y_BHF=SUN6ionk&pCoRPS-K:^=K;jSZl1*5gqq0%n[A-4-_NeG"]]t
%Y$1i_oc)s\:1Fl=!.Ut\=R'YsqMqdqfQ2QTVP[,OE%bbfmJZ3_KYu:FD2LDD?)S=>F,TQu<2C#Cg64#j!i!WGOU>\-%Co_IZ;F.<
%Hj"=T%GR))URT;K)$k08a[\=BGk6^b`b3LJk?lmGJ#Dgi9g.RP=U*co1X-sT-7PpU#LMPX@9<:=p*Uc8/f#VMg"G7t1nb0q#\.*3
%UBb-=)rtpN.>=_9#F#7_P_k]q@ig2fp.g*3quZ)<]^b#p0pq1@&h`7UX>92:M\^cAJC8nP$`99n"-"MXSKmY^)TPF&g'Adgp(`'N
%!Wk-cS]6i4P'W5+irf/lRYJ>N](JKmi=;1QcHIb"+`:RC(sQMAc+d9<T2T'3$m7HIP%aL#V''>aWmVV%T:?j-X6XuO@iuTh=$g8d
%>RKZ6$:-1\G"P>5OS783V.l'C&4YbU^9P\lqd%['duJmu%Dp:=4\Agt_):Og_g$gaU\T,t:4J*VW[m#3>:U4R[7nDM:0J!C2C2)E
%l9%;<5GtZ0fS<O/M0Kb2E68ULR!r)IJR<I$8eTKWbUZSC.g4/Il(L-'GRVUYR)B)nr,^3XBRU>p%Ps<sNTt>Wn7+NC6`%6uLo9$*
%/9hM$.EutJ&\D&n3Ttadn_"1jph:gGBZBHOYpI+do?$V"bK':gn#dCt(%#eVXsI#)^Nh6grb<I$k:7LQm$9Rj8ko5Rjs#iA,2!#8
%X`atJ9>Ol3'd11%;Of(>I#Cr]Bi5\Z"8ss3H-VhAP>Pdij@%>[?r7A/"3Zt89fG:Q8eco05)B(Z+'T/dP^S;"/,Z;bKX#NDb1Ko8
%@qjEK`JIoNB/C#<QP`BJ#d0Ijm?='CCbXcTWt573K"8@?Cj%l%l;Si,(2/efJ>p85daLrF*pk>SoV\\*M&KK1lsZE8aSGKF<Y".k
%`q:qDG7V[uH(@pj-oMAQNh=5g.[lMZh`k`>90lP7dM+:uZ;Ipp`u%'c+mdW%K2#WYYQ_MiX?i3Xj4u^\$2#RB;+gYK`-`bXJ[T>S
%_:CQ`)9-#qs!0kWf$%q[>09rVeEO,PLt+2%b\%rY?)!lT;:sXN)%5J`k7'1*J@3>sLfp)Ga,o,@:kRe[cYrt;FHmWldh\^4b]6I%
%64)r81\<Q]=&WR0EhGLC/2B""X&PrNA!/caVL?c&G&#m:5%4YpcuCdB#G'a"NI1*f"'b0#3Su0Y\8c5alG1?DiRKJhrE(7Z#/#f)
%2&9^WA8.&Dp/e<eHf'%Z"k9O"'9=CXW#?t:!\%*"-cHE1@]F0Z,->BJT$B9EZ?)'nolSBbfuV&EIM-\)1,<d]-.!X9R'TM-,SZQ4
%"OI9">!sRCe=#ljP7gG&m)6-/M][-6bJ;aU-Urb/7pUA.<'3mh,hd$)i&6ja?o4:O1fMk*@s(Z`r+h7!-"/"TQ@J5[12Ki-s-5;b
%\mu>mee,-SUYZ!W=o=[5Qo;Xs3`rF>Ik]uEotYpb6]uUaYD:&bdStjDmEA-eZYM4#$>h3e\'9$>jWX!rI`SW5K$NeEO/8K\*c#SM
%!<BL%9$^IEI!!S?2+kE6`fhLMR?LhJIH&9KmpT<5\7N=%"r9\B^?[oK9*nZ<=,\9]Wc.6[msU7L>jt:M]Z\j3#!b+Tr&h,D)/6jA
%eplmqKB(5A-,?6aRR>klFl3qH?Snrs.i40Gs8>R`HQUW`,UZ+aV.bUZ9MgYj#7@8-?>DnncnAk#R=_7:1NlYXmDKfW^o`HI*D*sa
%f-Rhk^9C]#MA$u^amnXs<)H\8c!$Kf$uS2id5AL6^s;2[g/YU>87@7!:bCX@6UJ^p(6CEc7F5Y+JnGnXa=Ug5.-*0YM#O_n)PqHp
%.KZ"9.^iIsR@BQ8D2LgCe78<(;uAmt>qg..-!d>R1dGto2E.\<8#XhW\-X;=Uph&L`=^HZ_GG$VQpuD%fX5F`h-Mo*EP/R,D\p8Y
%[6=VXdR\&&!oL+%Ql?([k66!(b'/nUlA6ba,>>78CC06s&!7_YRAgKbJG)1&6_fCCY.be;go(S\]auberHj.=@';XQ>l"^+4?*G4
%&.a28Vm+KX>Q9HRe+822H"uWrXPb3ff?a)I"<*I57PUuK8SQt`4E8\J;3DTOpiEcJ)[U')K*gM1[^qk635[%?G+#m,"FOtPH/!Z;
%037TL>)fg$O&Nq)L$K\V.e=a`U:kE>PWg%eWnH4Z65C!q_7nW8SjZO<;a;'L1WB0cUDY:B8W]_^og""8;:X)3KVl8-OIT]ZBs'+d
%W-&K>5q7H2S0(%f:%oBD^e;<Ia!UmE]nHhl7PICI(2JceUV$2W'N'W"l$:[s63V;dLndA[,W/H@L1F73IbXfd96ZkNlLj$`T&A]\
%A1ndlf!:oEV_io(Oqb_[S;2A'_6Z@&ri%\N!ZTpp.CL\1gh+(jq%^[SD1;jET`mD@?s+]U4#O2Od;Cb9M+eP/TVXl*Qq3ri\<&2j
%3Z(l-E_'qM%rbIsp41un7K@SU\.3[gqeNi'CD]@d:?/:@6De$I>n=jRN(lPm89fq\nf&D/5N*6&:\k5&c(+eLH4^+--g=idfFHap
%&d;e`P6<d+#f03J9%LT'[L\>Z%;H`Vb@"R&_l+0?diFD!?UX\2Aepq:T\:*O)^8l"5$jmMG5!qBmVEp&jb_uW"G]`H3Yj!/Darn4
%V(U06H1f4nB!/T$=/U#$Z<D?/>Ko8o-m8$pCc)Dci93'Rc'i++deP,+P0?dGS_$RV,#4Lp9%^X6/Fj.eniKq$;)k49+\$s`9f9*T
%'6rMEUW0S"Q6jpHDJNQs6oWA<LZR:rY%CSuJ!"i5.,-dod&*^1OA"B:"KH_cVqSS5b3_BlFHr"97"qAh!fmiONdI+1XB[@jSVYP)
%3<LT*Y:+X!G6itNrR;eer]0;kI'2$6I)4r.OJ2B$*@SLRnK]=`@)H^hJ<EhGrAgBYZBWi,1>`W?Ohs:pXN;(AZ*8.h^![cs'M=EU
%O#r!oWs<K$'b3k@dU89hRcu?=(R3.Lbm_t'23O>LF!188K<LOM"_aI1r`54p$#NC2<\Ttn#(L;U7ho?3/L-Jb1r2.8VuBd'2.b]+
%,i/\L22r6u666N[D'-d$%"mUhV`=DBB9[-_[1$^33%F725"L&4=&8+ek<[F!O&aYo#)c5k;=`"CR[s/TE`7P(8M@hWbVE5)Bc_S#
%(u`/IUClL2\S&pJ2h)uV=.`@6V)T2I-eW:a1?uO'K:t[W5ssA#5!?]OHlTR1VlR%o(W6E(+C)B3Epno4MFQu1S.]T-6"e<rp4LPV
%ms-%dD[:4;/&r`YT/Bq1>$\\#f?=4'\(JIo@mo2'K42R8(gLC%SRZW]0;Y]7ON=^KB$B%,<NHe&O6,bZKqiG"e='`89HK]Sn1.^L
%$T32&`)4Dm3MMpmQ]M^m`(_O1VY0)k66h3X.3o=B:hoL?W.oI\_HK#6D%EiF(W;#'0XR4[)+GodahD8,",s"E&;TXp?Xnb^F4noY
%g&A6JrM[f:.d(`Rf0>#u2uYqr8I*Z1GO*+oT9Jn_2CTn3Pt1rpq?`tA-3^,G`n(9%&T;fK*Ku^a;mceK.2"FSXcX&3(XZRR+jspk
%FlLc.&RS8Zk.=5667cVW2$29V'Sh:/O&#cT)59g'Y['qm5$UTJ_3C\NrqBH^Oli^#%q0hqF,O^6V@Om_28MJH=1J-j#btP\UF@(7
%B;f>r-OA:$!$W7(-3fn,W$gND]+jSBOF-iu0lgZ85\7Akfg3"DKViY7;pT3E>ptcpBms]W+rWJB-qXdYqBOK9>J4VfU;dFPm;P-b
%m3d#o!6>NG?@#o:_&&%8e"b"cH,<u<$b;7]7%jh'ToKjeWf+CS<0+I%Xhn;pNl(>85&`ck?T"-JHaF8I!'&VB%%d(<72)H-EU"(N
%hH<Y,=0;8'Z.#:6pPSKrF]b3+NC#XU?m%FC:QGOPqoU-D,W!$c`cV.9J,iicesm^"e`Z.M_<7as$6Zdlc-YmO&aTH;JIV7!Zsn'[
%&Z@&ogV#=noZ3%FNN_jZdus6pADfZ5SK4SR:M*@rFJ&#/oZ&uH;I^Q3.H5R94[*=:.mDtGmmdecj!!B3=9WjGAnaflL[-?sm<jX(
%1=/dK_>&u3&T5BO38*/c<FeG,Op-dldRRFXLc;R6><&,>#D4L&F3e53\^:Z[In=)-o86obe`3>fGG?)R\0C?PL\U())(HXb1Z7<Y
%p8+R;iVhu\m,'oT^3rmAn"(oGZXo/W)[:E\CP/$+$:h)IoMQ[tlJ6&ucW;YdbpVAnhi>>HKsopL(q&aC.*)]K/5O<['fIAok`_Cp
%;NeX*Ac8ZV%?U&hf9I_rZ+'6"`>ApC3ON3<0-E9,Aa$g8bVMMBQTB?>\o^kJcm;mYFXA4VH'fhsKf1J4G<]^@FuCJcSj\rVI6BIr
%ZObg@.[fb9![C'N49>]&[/Ws.juacK/D:p<ef]`Jo\eW>C=AOHNU-/)CUuqE*9Bp`;gD)Ya,!K6?-Nb5'Si+4O-Gb]ZVk@EB"CEe
%N3D_u&(We7XlW>W[,.(HYUg7J\MXGkpAJu*GNBOs.P.-W=QI:OrPY=JGq22b$!_(X0%lu?nqNuY=W8&+Ls=Xns2i>[#[-:ZcfIU4
%.gA<".RM7ZZc3Fa4Gd(s.1XI<J^I=tR'0=TQ3YZu'Nq]4192MZ9#KHVlDGZcKNiL8T7RsT4kUsf86pE0k#i],rfW\`J^r9*&qGHo
%2l")+#Pu@Qp]0./Q3.1TH6o:^0T*nC\tll#G7Nl.ot4L\c+f<^Y;udN-WOeAq3L\e)^=@s$jcO7+75Vg*OieT=K4[!o?Og>U.uY=
%a)U+r#6bb,>H'PDfTb/#NJr'(%:fB3NY&C5;K,B\Xk(R`Q$?Oh.*G0ZSmET>'\h)XlaAi`@&\QqX6jAk)_bslbKmS#$UH)_fNp:l
%artG5B@nJN_Tu%r^24i_r+soiqg<..*I_HP9<Q?`Bebk)5kZ(^HhoV_a`tp=-8&9E0WM"#"Za%=@_1NeCY&r)lD*O1h0Nm1F,=Bs
%b^5nFHu>u]nY*sm.Z+5ak)DhO$199LRbmEXkUrM%m'3g6D!\XAOj892NST-6qL+<-Pk=1bcJBrOo"/+7Qg`9H)::VgL5!snl!8hA
%\""EY3GYM]&ZW\fFY2.kF'n?Ih23s-*F12a;DFo<=?'c.OBTa#fKK3.LlOP7._m==K(4`t60l(KJ=Cer>e;Un""n-7/q?3u=hl+,
%\)u+%40FT?N;U\\o)!pYF_#BX20D1_*8?%OU@-gaWD/c/F@25Gi](("N$hfR?l&`cI=+H,-Llq[_IaJ=PMi'Z5/)o2VFWKrB,h8i
%7hX*!FQ1kp%l\#8?7I#Me`6jj't$?C31.m`r.MAP1p1WUA7g:3"RC1np5f?%N1HbHRn%$P(0q[ofmrn46Y!%s7#<>0BN34ed75`^
%oX:jh-t<M$*a_5hesh&R0g\sNfOgmrgcG,0Lf7Jt&E>jW.u-EMZqkA"?5O;bb$&k>EW'XrpuMc,Ss61lNTX:JRo?Za&Ld2/H-Fp&
%5F)TJ&0T+[/p`?dfMNRKV/N]-^0dV=fSf2^p;dd%]L#_nn[U-D3=78aFZPU.UqT$!Q<oLoH(g2ALrDJk1t)'rmdaiX;sH1u?a@Y5
%5PEB;,FqK0>_nKN<6O#>=@G:i["$6C/"#L@J=UiBr"s0/90H4F.0NmC&UO24dfUTpZB)<Ohf$7_Br'_g,<M:$qYD1?S/UN0^p$u`
%$X2c/4Y-NbDD*3iTLV.KlmY^t=4Kl^"7*N;:dfhU'6mZ4!K"%,Vh$#I1<`a?($+t=N+J$u;f8u@)8Lj`gDDB<^(J$Jcg;E0SP<A4
%9SRaG(.7Of*uBXK26rjDh%"`mPWRA\!oAMQUrZRNo#3YB+]7qaSg]Q%O<_T'ScVn?JP#S?CKsPdaK`@+JXBiY]tHKWFAROGhVK"C
%Ik!/.BVr4ubAuUt,7OI`4=?DU#MMld_:Ze64`RSS%7jusDilV#Je>LL[IO;!`tejrVhu2nCS)G%S!9jr2f,*DJ#rQ4%s:$9E$=f0
%Q\8>""HgYOL/+a6^c;9l_r$$8KJG^V07/o=`<sAr6:KCrS+\/2Y^ChGXZ7Xk9@*snI`(GC=>d1G7ec3Mm7'o)N@e?9@2HG09rnBq
%iZ&;%L"B31#%q0>"?`+rO?,VNg$rbhM+lE.M$^L4!'YYQp+pR5'5o@2?;!.?%5bW-A[L02S+p#a8W0`_6i#CI[-#+.P&?$4b15RG
%6n=k6h$*Or_+;6+5S#/;')RN^CDH'PWd2\4W`,TZU(p\(:?j<.[nnN4NS[$Vl-/A^3CK6k$#Tp#23&$%O[04tj>>jUeQX0fAM%n<
%/X;G.0$ue`p,)-oflpS>l:D+k[IZ?\@t8_2Aj0fVdAaKNY3DC)K_AmunJ*dUW7_Su^/0sb_:Vs!N.[,2Usd?4l"o-=42i.fn,!C*
%34/_]#<mZLWZ/8.n$j?g_Chf+=N/5$`ZM4sD?HXgJ11(/H?lZ>T3T!5B#kS5p2qQ#2,-m+PS`hC37k%;s,7N+g(]4<p<PHA1f4lc
%Eg`iF/gL9]eQue7erFM6p6mN/I$Ac%$QrfWX\RA;@kF5S<T5f`(6u0Cr,)g+-$2.TKQW/rm`aTK-97?IS'5(c*ibbjO42qA[UYTD
%\/[E^kb71/97Mr@OcRl5+[We+))-f$'$9.%ND8MS?li,%l8Wo2TTFB^YLPUt#R)!`'1N;<n\q6I'Qu@Jn?;Wo?Hpt5Cp\Egl*/W]
%Jp%W"]9Pt".QdI>C<St,JLt[E:bEtiVSAY*OgFg;[;*b0GL.(c=Y0OMO=[U1407`Y\So,u'q$MHf6+^@G`q;4X/<oBme%hdBN7:c
%CuXs$;A=a/8A_Ye$L:a;']K3.4D;\6kXRa%n6TQVV@LfK!e-m&(iS8dEJPj$9P4JO&b_tKio`hF$t/^;E<2S:e*/>%!/?T-J0NsV
%YaQ]e/M3GlL\&/>'XFF8NdrR/-@)O7O2gK-Y<@2l0R^#6h"!<lkj4Snfi7&%<NqVP!0BpGX1pg:rn/Zl5W5^n!0K?YBV5@G6gAf%
%e&[h-AFi[2>a?!Y?U!]Z$`$<WJa2Bi`<YWrGl8<&dm?I1nG6?q+5npH,fq&&`OR/^SYP_+9L$Qg^ZPq<=FLC)T#]t$[nd,5O:Ok(
%&&@!n1)+]@[0,u<(]RYE;Bqj&7nj)p,q,mI*G4'Ln!107XugbUgs"[g+B#"fI"^Mij5VTcFFS1n0TmPEVb=m_la"9\?2"%t>,&XS
%FA%X%dqMlbq.AS]dO'*KkJA#'OGrIC@Q-lU_-jaY!BcBh%-hrf9K`ts>-:\BOhgM`,T+J@;DLTG8+&&Q9^1E)i>Q&u>of%(9QG.*
%"2PHp;fG,Afj"SgaOJ_9e;9b44XN1@8cg\RBo12;gt33e/9MgT9d<QZq%@;<rLC3*Ncr^N0F8_m8JVlE[:d6]cL<%uo'=:31*[7C
%1D<-aDn#1C;%SF/,?%6?5-*aJ]@,Iu9:T#]K?(X"4JLtp(72%!$n1P_,0Wb(pO_naK^H5sa9)^uDi6LUp81LZWRf^XjI$^4W25#:
%"&a`[+E:]dnMXr8GARb8bl!4HJNK([)gZ<nLI;Lb0d81go.JA2;;;k:*Z&rkMAe"5^:W*FGpZN;4`Jo5.T,"kemAJ&;Vh>F/O:!%
%,KsQ\.@->#.I?D?'P;Rd0r?39G_hdj$%MBKd`%CbLtStOik"djN%2M1X,X^u@9!9c2pUQ8pg!i#m#AV!GK&Hh",6]dq%X8^JXK(#
%rClgh(0QXuL@_.+i3<=K_;0sDJs6lhj#,0GisLnC%<MO(`Cl'%a*a[!q\X)^*B(0I%#eT,Q>\fPMjR-%\Hm1Do=L&pk)AKG"KN%d
%-ij!4+7d@RkZ#M$.+O#IoU([>h!0kG:ZSKK'bZFTiZQa0*[CRoAY`^%5Is<De)K_uPOn4I_4M,TJ,:'9#poMI=K'XJi*n>/A'*:d
%7X(]//"]['G(V&meFhXGo03-kZ]78KT,ffMa4>#OgR^tZ6/H)k_su5"eA3lVj4U(/SFuJN`RtIUQ@(g0%a4:$&LBqY])b[`N`$qq
%<PpC5CA"Mf8)A2RpQ,]9+/70]*RU-7>X1dq=LOrAotS<K,Wp9=oqTDaJn2T!%`0Ga+euSr=QP)GQ^?EhVRs0J<8eUQ#.^Ej_@ouI
%LdQ1.cgZ=SZ0`tk"O6dId@==hl/`N(.X)jhMqVNV0u?51*s#ScR2GH\#R!>tF7_TgVU=o*(hZIH!qFC1@OHJ,GLVcK!2s!6?Z$8L
%L%m]!haDZKl-pe"'cW3m9KN&L::56/+sl'"^,luc7#XmoRRsHF8fTc2!Bl8q!_J9f_72g0R'E#J"g[PaFA1Js8*GM("d/f7QUHV%
%lCQqm@<8hGB[NQ?r"b>6o&,8G&g4AJ#Xq/&@WPW1@2V#oU11O]"'kN?]$8JuL>9@@%;T1:KI8CrmaW>)E^:_930@+(%"`JU1mZ-o
%KuS<rKb+jOMS8A;/O_C%fQh?'ldCpddhXMGquP]u]>P\,6C""Q?<6T14N]\tR&pU$R@7n'LNa]B&-+,XF?%Z`M^l$(TTA')kj^l`
%X=rkMcja\!]D62a`cks4j7QEMNFRq8osL--lXUCn!702&-0bXm@`O]@XM^uo`/ZZP8W40A[X2(iG;t$R"=W="8n&$Rg2+\?q%uAc
%%-2NY!CCZljboj)N^b)USD5mba#cg1%<GkS%UCTH7\$k-JDCH6bhG8r[(C>ej;"MK[/1?B,`G7^<JdB10*ea=s8+;**ST'1/#1fl
%^eb:02&W;amGM21p?u]3)N/ba[!i@o5YIQ$)m_%-3mLicfk!0NZ[9j8ll&=MKHdh+fCWF`5T$.)J)?;%JqVIM&N0<-!qe6bEdj#C
%Apco`>KLFd7a+`Cp=M!kn+/NN<kFI7M[i#XEEAO%4>EllB/m&]gX4a4-e'VdT.h.W!`88+B6K-SP2sfsql#IN`erV^s/PM/re?^@
%JE/FdR+B,lmX/St`Hr,'15j]BG/*(nlFrtMWY#`_"%\@]:kuq(cs,>2Hj13rj=+2iGA(SFXWu2`U^Zj-HsCPlrfVkl*h^"fN^r,9
%3jFfdCd&ar&NKDU[Lc@'gri8[g`qjR=lh.ZR$u8VHcDnuIE;@@\so\WpRpF'BQgEl,@i$+Kp&M.K:7D8(HQ-do@@&KM-Wr\CX2*7
%p%?+;BW%0]/%l,P31$!NVK)F;ce@\XpbTk4_^C7U/rHfMabaOYS':NC]th.F@@MQE?o`ChH/=ed#\L:!'kf2Z*\Z.8DRr)X"][@.
%Yp4hKD9(=HEY-n_CE[7L[A=DiNnh3-IAe/qkMTbo5O;muQ)t_`)"g9A)\7)5#/^m?GW]t-I2#S@OYqPE/lSp4cZKVL4":`5F5N;q
%g^S\pJnR?fAt<>6@Sg;tNa0qG^`2\*aXnn98YODG&3ki2r$X=Wpm(`0P)G9ZH;/l)&60mU5nuU@lQgj#fOGWE88I%<V%*Ar2*WEa
%\,rK.`P!!T!5lnH"-'1P"Tl*/8VH\CM(_$ri\HY@%8'B$LDG(G@/s3,/L.9=)9)5qLW>`o&Wii)8gqPi8N,,5[QM+KeRh&k.7c>5
%Zh"V,!>qldO[@:a\>,"ZL:#]C$scdK7jO'3NBQgKOF:+u2[nGo_M@YlLaQaLfN+M:L^,Ff[l"/P;^F27H;oJB?n<d.j&qc]F-oRO
%1XDGMC/=I`6uL/7QAGr;g03WpUT/;PO#[qm-n<+^-nL%.a6C1c9YWChdZkBdf*L?7Gb>Us[J4O^>(\eKa8+[u_>]MQ2:9`kq,DoT
%jT83#Dh'YO&:OEn5+"u]VRGY.d!1lm#VGhgBO^AWB0eJ7mW]H,.O0OX8;+j'mDt^f(kFLX:(LFOE5qD;hhIV5QrL+ZT4Orta(pb1
%'9XqGI-'W:?/]90PT/mCmfoid0Z$V%3r_\4;-,2JHBes*krU!o,E`Q_\=QWRCU7(9Fu/H,(:O_+V5$A=+HbjBgu?>NSZWu6r)R#K
%T7AC\9$@R6%c8EBk0I`G%\iGl#_KuiRZoOX7s&rT>,Fo99R)l7inlQ?=g#8Oj[q#@_?@Eh7G>-1=Lu8E8\+e@K+G71&^iFZ-Gk"-
%'4.@crYS]^^knr0G5r%G+U(V^Z<YO3l&h(ZL$er#K2EAQgrB:0Ta1UY.`M7*=(pVE%pVRN8I7fp:2`9LAJ0^2V'HUZ3L&C4lKt#p
%=15!T6lr&5dsm\-a,qffbLW6,6>&]i(9;*7k\SuH),?lL`=s%`blHf;0[5Y_[3NM;fH^Do(O9A>IUMD2D;G\J3`i(mSuta:?`34?
%oCo3#_#mf_Nf=_"_`Xh4.+jdP$AtMBIKeG0klLWm23Pu$/hF@P&<#ni2/;b6oH#5B$KVVpnm"O\(D\3X.VR1.=Kg>aU,WbfV8QhH
%jrE9QkWbXoAIm<]1%f-tBkH:9DPe*KEG6^!91rtr^_E9c6/.5g.Kc,LW/gGRRbdJ;]$a/(Su<?RfRJ73TJbH%kVZG3$(DbNj3,sa
%s'2a0'22mZ^JeZrYS>c(YnW@jJT`d,YSUg;koUSao,*PB'D2WQ&g-.3E8hI#GL9u'l1$KG_554b`pBIZbb)aQUR4TY+WZ2<]f6!s
%h1Kl`0.i3A&MhR@j2`64q*`tAoph4n>4U?`p,#Bl)Z`HOi(^hZd_VC%p[Ok#SJceQS_`9]/>cE\4=`m'd#5*9PKf/l-=I&fbFTUi
%eJ"!VQoVsjW7Y-<jiZKO?lBfFYd"\QDDB8"bXLQ8.3na):W>=.JI(T-r3:Pe@8mDs7O_\?dOXJS/PCuP\70&$dXHp8HCOos7oJ8%
%&kna0N!ULWVaC<9(Shb`c%P0NXLoEOC$7Nq8W0C.kKVNi3CRs5>5W2^dbd+@Wp';IT<$K@#!'M7SFGMJlZ/6'&3BOf%Zj]2Hhm%E
%3(3<rGlM-WKU&6X#QL8VE^\9F]KHQBlS=A6J-V0%%QIP&ArmWC`J?r000MQ.2[]>uTrW+"?pj[^P$U+&M2^ANI2r.JiG'r*]Tc]V
%OlB1uM!p=%S"OG;4Oj]M?sgXEd)<N4)/%rpQc)%u%l)6/Ue\73kG`mpnhi4&Ic.3]FIuFR`7&*l1`fcfc*ci@Q#ij;,`[c%g3jp"
%\9Nb3+HKpSVUf&%]#-JkP*3RWOMKIjb'I/9ek;_#\l"fPE`T]uWPIe336bO@V.?K;IbK%mX*TL^C+/;@`AgULn?CHcH[9R:PI8+0
%s0I-?\sokt;e0DLWEbo5><:>`'A$Ct0WlCsmLCfd`<ScoH&gV)$Fjk^:gg/--LbY"iJDD/)^/A@<7/U?=TmP#!YaZHPeEfuihNV+
%'.L`PPT*$SH$-]Rnd5+@V1teaq=&DW6BC#V#&g[7&Q]Z^gjsZr]]\;WSh;nT0'R#Ts,%pGd3?^\5:k75U7/GY!-W?9aFEPu+hrOe
%&3QBi$\Ebn@0,8VBp%SkNQ;7%ic=G;?_eJMKNtk0+G^TQo.^_JgCk)3V,]o<:H3eCY`QZiq,]_#4r7OT"@iUF0F2,7eDV/1Rq$9O
%Zg!n\1h28Y+DDhT7HQ5elY5fl3F>\YD#_#FR)OAAH]?55s"V_N.D&K(2sC,qm-0NMn)C%aB/#K8m@$&NYG0A3>Zp@OA!kO'W0'U?
%1t'=f[B?HV('L,/3Okh4fjh*`hd-oPM(8(;^;5X_7nmh%&!425RH<Q\Qn:RE\*H@_@SNKF&P[;%0r;>MXKN8<*amK2>k\C<_<>5"
%+_cdB58'LC3)3WnVTkOpWCU2C.3Q&iV@L4mdKj7ReGqr[-u?maS&S?qM4u#TW2Nog+-+E4n6(9;SA"M:d?Z8Jg&VnL2l!lN/X_u\
%IY)Z.i#\IH<uE`t4gHZ&(F3_Uq<sC7-Fu"SC.Wd-a!</fY1kUhMP?1.nLK<T,54Ys-GKWEJD)<qA&Eq-`iU#u#q9_)A-h&gN_%,6
%oi/P=#OE9miislM*n^?h%d;%&1_j,.!+\TRMBh7_.:=FTDKEgCX,[BCdi^b7Gog?"(i@p+l7]sB="1*nIs=rH0$_Z@`q<X?-s5rL
%!uXc040''H6mb?\N&Q'J&T,:-";LjhE19?6R",WK](V$F'N>JX@^lO1bBg4!#GjDl":XO"Kd-GXIeQjAAKd_8jqVBm;O29ulm=n[
%Q&u@bTVE%gk&[;niT*TU+i]EE0X!2r4ND%1*jf=j5RJZ"+.O\^YT>$ZP/`$.j'nW04k)aobBjah[Z/+Z_Vt0d,nn''&sUnV"^[5>
%?l3+p`\hf0CQ64(@X&/a`>p(n#%4',@sS_:7LQf.H(L7F>)miNQ"9rl=Z8j<##HEKeeh7C_Fh#f/I=I#cW,TUDQ]>^p9Ds1mYRMJ
%VW&J!dp$;/'h1U[o%s8%s5l^5JW4BA3I<Ghj4Z;1I=6i8/(qaG8Ak)ATge+\rFFj_K:.%lkU'IGX?'fhZTL^76H34S0HaD5ilj*<
%n1Q^fTL%:pNtEXoNhtKqPCq8u6oYTE%Y5pP#9P!Ud,.QsQL/Ri>eTYJh1UPr8"!oCDYCb;<aq=$0Es=CBUolSlOqjRBqph6L,g_#
%BC<kr_T@O\X1Uml)?uSE(+R`+`"$1]3du_tk;1FB?'F@"ecpl9o*j&9h`Eq?eMMdjp1RU\0tA<KqjAb]N>W6sH-b9B$4Z0\EYBK;
%<*:s?JUi8jl<=b_C!&u/'mrXE%"!N$l]jd3Su_alWGe"5Gru-iMRJK<ZSlB>naNBYWMs(u866RV8etoJCTY"*K4Rhk-L4d_f0O0i
%6uCg#1Is8+a\D2M8*jCW%\p!9L)bg]':,Cpq&#F@fng)&8k?HpW_rZhRg$L14GWiE4Fc2B\EGioquo8ELf\,*/lEm:TNVhTX?88!
%bKa#ms!C9Eot^cf_D;T$XZH>LA=M<#@,0P"6n4l3`"Ua]k#`X?U#D\eWo?r6bm6%VP=h6087;+^mf3V(&!2>V.0jPQ)0%Fu\L+>9
%^.h*6@;gH!-=O`g)u8MAaS/##j4Ik[TlZc.U+?(?,G8>HaZPtPJB9\G6(qQ<h!R-m1d9fH5+V@]VWm1!.cR<B,)qq+.l)f#Z:b,p
%W8l"&Rg9M,C'QGa)'dp[rCb#VEWi'k1l7tGSqCK\mI=j_[n]FPC@gdIl)W,tdSO4R,E\[j;FUT#PCsqRlJ3u'Wl&2`H`!,B&`4X<
%(lc.+M-]FBGbPZoR(To2\h()ZG95Gn?t^S6>uQ;sXB)UOC49u@an+GWjm3Ko6@<3&oM:!GjZRZ\-l'ssY,l\A01Bc#"9&OpA@jes
%3n<K?Arn0pg8eTjHVn1X(MB_<$\GX1Vee/ln.?kVHR'YtpBGDa3q)EQi.t7lC0EGKmZ$<h%[3<+Dc?c6B!F0$B6Qhe-6/5`(""i'
%FPat:Qe2i"DOgK>o[4anRg:Tbim3e?(>Na&l0EKo?=+qf9XLKp8+g<d;-r6fV!`H.R[1!Dm#5$PEF@hL0F9OBA\LgZ-r_`UV6+/g
%-#QHb0kl'@C+-[Q4$V_^3(YLFQEmJ7MW,F``1cQZ+'T6+a&61)CS&NI1]>So`b&p:Q3"\]DKJ/s,A&]BT-ZV_0j1gak'hY#!Y_4=
%k!t.9R#ld6f,<g(4b!744"s-KEIGlrs4E?Nf]4:;:q7gf)W%q#\ElDA;/CL$d.`eM/5mtFj`>7X+0QQ+ll7q;A`XrIkPGZ56W^"&
%#ol$J)Y*%PN8X$Q3mF`_B"eEU87Ggjger(G^5=aDM"HZ8D`Yj@mX+[a&?%JQ8'_"<S/8N6Ubt<U*duN[[i4$A&o7Tk=?Ku*m0hs0
%l'lJS\q%'#hSU_n]=LpCNo9GgVoYk`66`(%Fls(CGZJ]qC<ghj6O@Q9ed\^pb3XAg]H/W&_PG3HE8ie=VP4LRnjjDg&nrEIXKY)m
%9Wp(&qa47M*ZG_dBHmLuQZH8iQtF&<)Vb%tOK-B494PPf<A2-!N9OXKZQ(cQ9,EN+1OWVhSf$W'FRsUtj.D+A#o4[/@uhHIPQfri
%U>Q(;EEG\&_Cpkqm@Z89hZ@"T>#".P$#.N^fq"mb8L#?9+&`Lp1<bQt>j=>Ae_2pE738Sobu`*60#S\4B:LD@<-ePlQCPqWr]7_1
%`nQjK8)X9QZ`hV$7DJBpKVogWj(PX2`enq\WA;(;g'd<[;1osI4%Ht#X&]cN!:<EMAIL>)+aj(7K;'OXh3#fOEJ-@?5sZ^
%H80IgWKt@iI&4NbKdZ&=43\`_S)Os4DtmNb(/XQ<^KnE+T>NeF@\HXVo*K0G)j5#P`'H;"OI;+'5ck3cri@5L9c`]X+lscHkgg4W
%B1OGV[7R\IoKbe5<,$^WeJ;"Z+ch<a!^0F;88>0T"-Y6Eji:Iib-;`@n219?)rq@e(F"nQo;[Zt8CDsP[td=&jsn5h!tUAJmSp]L
%2O_JZ%#JAm3[(tZ6m%)IeY5d"9n#;V`Jar;[@I;()'*a(pq^EeL=*7(,M:r+(a0IV7s-!n[t8gS8>XSHFS9Z(0r[g[rW?<kmlbU8
%eiBJA-jeIVj:e,>e%eJ?cPJ@f4t!3"k5$f$B1mfO+tbm(/mQkb6sjVV*[>@-N?cC7A[45T0u]tpX%mPl5l=!VAWlNZ0qud)7Ge"`
%Z#PE"Ih+3YUfupW;X+a8.Ze'bY2GTuaP/E#;QtRBo/!T`jOumWBEc#$Uj+SfIn2dnU6q%+_H<c$FkOe!HI[%;LkHFkct5=+LJj5#
%5/o#Q7uAadkku\@:^"d'@l>[TMb:DTBZ";MW96X(?ncX6,s*g>WUXts+-XC)>O6=-9Iir><0(MJ*WZ#JU3+bjR4Q2E[@#gRGUmL"
%mS"Q$2/oL(!TFm;M.H!q8Y$RgJg'n'^13?B\$8sn`i%mN8_<n,bdbo"Uthg\Y4A5^r+N_J+IlGr8T.8u0Oef]l!8Hjj:0ce5n77g
%kST7.njVDS@g<>4)Z;HJF(AjGV#5,\edlnEVL9@<$K20Nam9ud8\+9.dNdW`k[sO0U2Ws%:nW[loeO%ZB+<0"H%!R&acF-rj0.DA
%Sb1\A;D5BOOKbm[%.pKYFA6/a2>'BI@FF`N&(u-p=Eb,:n2eY/clQYPj8oYc44%Fd$"GCjS"ngR_WmQYbWq"qD/)?'9'h%9T*a6r
%\8[e@YBX6r69f3/c.[l=FpO!ZDY)RoLr"6g.YRa4Q]hgBVO=SiO-]2TjA)4M02Q2NRSl5s1#\/ZT6NbW+26t7iu@5ph/4L!)CK!T
%.?\\\b'/9rbbt;HRtr@)PHZKmU,mG#bX"m-Z]CH%LXd]f+>aNtW@ZiPKN,3tqC:YrCq6".X!4J^TrDm[JS(PRF$kXZ/FDQ>+rq!+
%rc>N/Ia0IG3AL0_E0<(,ihrU_MrH':nljAK.@/g6@IZhr1,*%D?")5ePU/^JRT*$:Tf:`E:aXag_1X]m9&LVl:COo?oam#4d=o\_
%!nFT'm-2fi.T\(a`C$F>W(0$-`?e5HiL++EkbD.ki<5\nA/&f3N9pD*;369>GQNC=o6!P,qp'YRr<VXZ]As'-1tC`UYp_]!XEZ%c
%#-pqDs/`D5&EF]5YfQ%_mhqF^aYKl:Np!ObKQ+<!eWDN:R$,@Z'B(HR_EK.bbg#Or`uhW2l%)Y#O.Z%=pr>"X_^KGo`k#W4lpnkh
%XM[,A,Xdp(4rH'tHoY2j6\pYm/('d_TY(#XZH^HtXp.In0uUiY$G)7TYuiFJWb!JbYX:VDi?]nD,qM>W]9coFo'%FeZ<$[Y'joc(
%F%T!rK#t]=<t`eXD)m9,6I[i4cISY/G?,63-9O*bI9Z$4QW;tD6iQG$I@u_a>>f[i4`\T"?b?N\fA>:Wk;>/eFDr7QO-*XY8)G7A
%mo%</jl:M7D9$re/S\jdh3*QTP84HQT/m]R+F1NSBZM"S6]:PP9qV*Cch.mH481`^Dp[#_V)*D_F[7U$c74loq?MYh&7h(rZ@_-g
%d%f\/55ZRK[Tsi13Yj]94+>::19YBb`lT9-V`e-(^k$I\ka+"IZKAu<c\lU4$NUlQ7=Pf*6+n+s:Ne#a4l>8/k_.[WpO[6^P?ppa
%_qS92TgMfoGF`l4TcK[@4n/i3&ZZ)87-=gPrsIN6E:J,_!$\1RB`Z(I4@47d('LugET8&meoOHZccD]6_Ab>EjJhZ#$l<6l$Rie6
%&gbuP-9[gdVPRlO.=Tdj>VOXI`ruduamWOhh*jkhh&<bl8@.6WNAloRX9tRNfZeg,:+?/^;=A8YZF?.>oBrSDQ@p]:0s^'q:YX]9
%aQ/!]$VcD`>pD.^hFNkIM2"UEipiMoQEU\*]Y&`EApXh#O?6;A+W@Mm][JWG2$.tkL)E&.9b@HB/d_:R1RpVu[/s'?9b(!#(kY5:
%?T.P^]f"OR&8h&KZ>-D3.7qsG(jnn0c`gn`n/UUm8X(8@pCg8Q!m^p_84l&%"`0rbZ%O'U.UqJG$UL>qlM-H+)ki!qCo<mP-u._'
%5d(97M@A:;#^[E?cK1q,jjmjL[KLe&9TF"g=/`1\PfJrMQLL1Al67K"@ShG9X_.LbMBgprA2oX`R.$B/ceBZGAkmt'R.Tj=KP_IQ
%#Wmc1`IM"rmd_TKpVN(?+3WHKGNBUD!0%-Sfd6E^lP&&\f6UK9.kuq=5sG\4I&Tt8'04#SE9+(-'Wc0%69.]h"8*>Y&gBki$_V]G
%>)kjWb98rup+k@,,m%n;Ct*<1>fchlN@]i?_QZrhe40,0_;d^nr)GrAa6u..K6!T/HmG%")'79D-9#htR++*gH`X<0FeO>\Vp$8d
%Gg@nk:gNsqL1\hoTuSKXERYRO:4`:;SjtT8_BWl>)BguDf$\J&E[5h#NLYAMM@3n6j@XOi4nbum-!!GiG[=oMjYnF',^."^,^@k.
%mk:`u"g#09>a'h)0S1T)N2P-c/Yu%)97JC3)c(,LIT6&pX1>?3![+Y<[V`ib&Yq0i!lE.YN3HZ1M_[Mm1VH%'r%MDRD\SoQ@$)rP
%D+mB4"OJ1X:`c7:$@#7h;?"))n>g)c=P5+_h#lG=JEb_AG_#5PO-(`Hf-SsXeS[E(^Pn'5RpTL'MppNOcCiArT:AZMB4`%@7K8mJ
%6U%QCDl-f!/c;J^G'/Eon?L_\I4<@RF`Q=n7kVp<PN"krX&J+[`;?n+\cLAt>CYRN!91_=-<eYp]ZI)*j8([1j?[tBI6U])OuON<
%Meit*#SU$[M.qh8!\HIWeD3`0oEWnl%b/38Ke0>/rHkmZ1PpCX-'>sH"2=@F^O/;3"-Hl?3#?<M/L,$0.qB3)=6EIKfn3usr;3-,
%,.M4J&++WTpqm2r^P*nYU2$i@rIWsHW!3@mSS_DP/^gEKm?qi_HJU=GXTZmcM2\"_3>;enc&4'u9#`PQ=!EMHf@^Xk-G-'I^F,Pu
%c2QpLrcJ+-F?>jTgqbBbSU+fbGG\T3C3khE<R177*!SXj8D/ag?/="r!d#l-7tQSV<bK'd&8j),**ga,XKY;<_lP5=^4\TQiOf't
%4"`7Fo,2s2K,\d!hXLsc=Hj!OF0ST`-![gSngYFCI+hA$?#k<S;/je(O`E6^efPh]Dp4Q"#Uhi1'<au\3)EIOqV`eHl]+K>X<ogj
%,Y'1d((9L(md?(4=%e>m[4V9sSF*\l1Y,:4$,M_$FHNf+2]QUA#I*RgbFo$6$B&FsVBSu=+Fp]b0ok.:KcP0:F,eZ9FsM[Z?LIHs
%/JtUG:,7(e,c_q#/biGMW2,[jSV3poM*Og(F<XSsi4qR7N477;NtG?eD`CbH,l6/SPeCla*4ApC\r=LQ?T::EV2mtn'qY1h[@esO
%LrqYs_iJe+N?.(h#J9G6dqA8-T9m6L(MK6LX@:8`>uZ=B#e[\&X@kt>q8h8FND)G,NIM9sB,?ZF??9AW[(m?%I"k2]8mDiqiXiiD
%m.Q(l)>;+'f8l8S^H(XdIU0EAe&],_f;R1t=\DP,XJ</h0]aP?>*WV>@H8<q2;Wmc4=;?+Y)[:TQcR>hfhm3":@AF[$:UD\^')ip
%Z75=%De>["%\slTDm(65A270&`DB0g_PrNbb=6_cE&Z2)_\/`mmZ6gLi#U4"V%HZrd(dV(]n]ZL_],cL[H\!d=f$n091Bg[,&^pd
%!qqI$=4h5,EGQD21*\D_G$KlO1@8DjWH4i#W36WMp(q&d98l@LN%Roq<pFu#7=:dD!M=b5%38.:F.30@9n`D?\p/B/YX'(_\NSkf
%7D"B=(!BL#l8%\#9C8mlC5M6:0mJK%qNu_O?!i$W4ER!dKH>Bih:Vc?Akj*(YYe877ZJF`LFp>m5U3L#T.,d@R+$1%EDh4<0sjgN
%%N1R.0OD;8,<8WNGMYGWC?Y'u.1TG\HJ[Q'0/;Lq6]6j=UA\g!aUd:#L[0lX\0L;q6sWG3lR$KQm_Wq#JU1-r1Af5H5DVO+M(->6
%U%2fQS4u;@a&HSKD[VQQnrb7^G0/cAg"$$U:i"i0FDBe8V?bYQFant<LWCZ:r_Q40>qpYE.&lRo$!oso$jaAKhN>,e*#[>kZgF'V
%/6o_tJ!eUP_/l_R[9E>u*aOfs-$<an=(gr?-&XiXNat8^V;JF;pl0Iod!Oct'&ZR<J;$?64tjQG@]a38N%_bDRFA!\Wc!b8'q>,C
%JfO[*FO0HTA7Bc5(f`>_Mb%Cg2bJ;c,@M)'Gm/sCU]?k;qeCBYDCu,8m_.FE\l"N%*a(@<@UCN,c1h!1OacR=<4uWC*G/VW/Nk:E
%=3D]]BiO`IDHjQ\<`R5$2%Ug=Z(_P6Rbi:+!f?Mn=BEnrqtpVg?L!o!<>ct4+mZsikm``g3aH<uVFm9U3>2']Q!\[;$BAL']Y:+M
%W3GN;<@%cp,7^N$D@36+!<j8PXoM5>eo0[V7+>4unQpSHogiY1C/uH(ImgXC(oQ;"9ILH^WfGB]`%]^V%!mRF8UN'6L1MYN0<$XB
%i@iP7es=3`ncCkfY#YWk/6t\VJ1R5VaN.`qPuUG\B-*5?\%n]q4DrEjNo#I.C;H'eeL;rQZdm!rjPWbKl]\p#jG9/@ff$h][QTn/
%"-u7gghL+;V`FN=DZ3=d5\%1k\iM%6_X!(8Z7eAu!8J$ZMT6u?$181$Gf;@^Qs!Ob_TUL9I`Rt2maU"Hdl8&<A#ZM3fCc^pp12aq
%m.IKVNX)Kp6_Wi09(s$R,OH'G4bR,t@C68<+N;n?nQ$soRn)=u?7"fCq8_capf>Q'?S@7;Ie\j$p;]DsoY^_#J,\b`s)3D^bJ4)+
%?iTT0s69>,J,DN\pq-Rps8D9`or#S$++O1.s8CP6rS`8*iTEH'r;ZfUDu]=Arl8CFl*#R6l&U>gpOE&F%s!0pMuWc8J,0t@nY[K/
%q"K:0-ia),*WGacrTp0V?%;X-s7qGbr5h`llH:h3s5g`i])D2,T^1_^rs/Lhs.J?/rQ"rWpuT8Xn/HlS0E0-Z4WFFoDnc5$+92''
%rX@K0s89t<^-;*Hdb9V,O];W!b6Cup7g1`P-e>DrM(QcXD05q"Fa03J\e6#4dMt-UGF5X#&tJ;OKi,B<ePSdjB(&++G\?C`$/+_g
%n&VF]#He:6Al!.UaNiTR'p!F\"Nrl"a#.Na,c/s@&VMZMR#1N<);T&tA%p_H,&s\S@!!1!#jKfLChNs?:I;UI38R@N,g%p1"ql(N
%,NEc%')o\3==5#.&u/F;_')JZ09]oTA2B)d.74hf0IB!?nl8_XQ\<i98/iQ/c%tX!01\sc$bXu([OH1Koms(UH-beBYm$:E`@HGE
%:i7spMS,gOJK@Fp!LQtqk]HHbZ9DKZb)kElQj<nL2^@FY"C3Ho'!X/28]:sS6B=If!jja,+p\or9@B)"ll^q3;!38n9glZ'2aoJh
%r&@U]83s;3&#>2%b(RPQrq3_>b9_O-6p'Ld>=*>3D:]3NQXV>aK34&Ikd8f_pjLf6=bM%1W_X'<1[L3)c;%X:0LiioTja#;,QL-B
%Hls?ZfR@/8il\4E_nU=G)GIcC'X1B@\k=S"nqLmDK8nW:&VB]-quGb<bj$]l0%GO]@iZq'@8m(.@hQ9;&`D2U^Fq`.cO@2k3!ige
%Cl_H;7!s]ZkZU(n+UEJQhEAIs=H"o0[s""YCWokaEY^!5O,T=\o=2^93EQfQgS=k=pnPBcY5+m?S["]6D':%D2(ISgXO2)ge3R2)
%mKc4c9h5gHI>6pDG2(<VKl5VP6bsl[i5oe`!k#Y!B9(Xk@&*-#hA%E.g$qsmUSTH8!IVD1=:037H%[a?\iBtpQDQd;mCHa7,'JKm
%K;HS,Kg=1$9p)pQ;[b+N'(EU]P*U!F.70Nl&]lZp80U,bZ9MQ=&/pD@T9(%JHjtL/P"YoM:T"747t)T)k6Wg$1Xm4qC+<r:1u/H.
%UI(L=Ju1S!qtQ8c3(TM`EE;=riJ+W&3#,LCc@tS?"K2C!Va0*-MFPPt>\HW`K0@.#`aQh;9'p<"gTa-(^*;aljJ%#[E,"U7PqFsI
%i2pl*i"1R_Gp*GYX3IS<f'V*YHGbET>:dEjD[b5:gDR=m/Rg'[T25)Xf[WLAiNJL-n;"D9)4QN(Lku7-_uMYPcU1]dGcuI(!6*`,
%9t,NED`j;7B@4fsF#g7us+bRk^X)`RL5ucr&o&Lb_#]7:q:O1[rdcn"Es[,EqP84I>"IAQSmgst`Jt#-d=Zi]G/7Bc'7&ANs&_]+
%)OE_K\:aJ]s0s.d.Y&\"9ljZDJ(`t^*gon,mf<#n.J6Y,FS(`=FI<T-9%Ia'lFiY.cK`CckC21Ja:A/PF?L5bC/IhQAX:F?fktnR
%,dQZYhKXW7<5GggfGu+ACDm)VL#3#5R2,/%+^EOF4.kFIZ_H;i.-2'/hH@3!?CbhDL9uq>__]AY:3C[>5tDl02Rk<LRU.BlGbot?
%d27(+YW,L?8=5goOK+Ugi/!1$mF3!TI6&DUET?niB.-nX#'>A>6k0Hlm-O^]lqu+eX9+M@mPDmTh#c$7+Sdo8%B(5d'%;.,MR4J?
%f<34J[o6?4,)Q^2<f#%"R7iks86scV%t%4iGhIl*a`)g!mj:8P/&:s)1#uG3*nOu2XK;4_/oO;)'$;3n><A4ZW51WdN>=BA2s6"N
%.TkcUYf4JW@5929$6bP\h&s.2HUGt:Kg7f<'X4+'f2YZ`Ub9jud0$V'f8@"^X%n"l.de$ml%EFEgRQp,DZLk<ImT<lO$-.kAXa8u
%XoI.'pYC9Mq0BqFJb6LL/HI]#R;5;FoU'[>^+Pqr2Re0Oo]Io>*soE>[g4U_M$)F?i'7t#/CUCZjdNJ>eW/l@VQYpMW7Zc<,!F%H
%E+6C:b7A@Zj6$Kll)&42*5UV9>i%WgRJeRPMqJaJL8:kLU573(iFgHcQ_+J>a_6qerdhDn./!&.Lb]5!k"hUHVdm5B3+\3";*\3[
%]<Oe:Hjm?Wh80>CGG/MDdnlD2S;N58XL4O`JLTn)TltE*A+OItYrSTU_9/9VSF^tDW;lZNhP,ib%q)+^W^TV=./n5Dqg'I2K1?N9
%^>;ZU"P6rQ=VQ!t0_%\U.;%mU7fP\b,b%8#s-$,/U`L7mbU(L:dH_9,(M)C6a]G&V3tBhe6bNK3k:0)-jt%/R`)+]f0s;19-bu-R
%r?<<gTn`k,(Q*IAM_KR4Oou"aYL1V1K2I;?YV1DtF:q-?*lHhMaqWYgjm1H&f!b!.(q+iNUL&>Z[i4A)S7L<jqf89scYs$t1H1ha
%B*9Y@aLI1^_U5.\#6b-kW=W^3oaFDFT*HOo8OV=;AfSCHZfC;[Z63F$au.C7>`+\-eFG`!]f&k/IQ:X%#rA9?,A<3i0=[!$brq29
%73a'!Q8(BW.pf(s*QB7a*khQKhR(X-P3sO+`cp?<SbFriMtQ*J9dk^_>,eLiT]='M\TQ:Ee9\S,DU]4X,Is0/hI;2E@k`Ds8X"`&
%.N-KNWDBc(iVN5D%`Vb3][+5;1NNl_a'(_HAFbZhr4<r1-ff(^[PkOW6g:Z8Df&qM*=!)ebtF1Y-!`PtbO]s)G@Z@i;[fJb^$0Oq
%U=4r/^'X'E.^:L$!d9U'+W]ge,AEL67'b#RQQpFUMV#uomq<lq_G=lb[<0jZMj0Kb.4dUZ:,j-MN,G]#O=*YLWrta\fas#`FE61h
%>>3^fi;=%.[S4ZGUq9g]CLK!?hDaRjMNql*;;Dc0YUcia@@8qJn'bThn:Ja10.399C)erT(s4$I,<GHCOU7/YD*^n+k6P0Bb5m:i
%Uqgj?3\2E`R#H^H-n]cmTa/mbm?=j4KN5#Oi%ZiS+=;uBXT9^:2$TG+l28Kmd!,[GJ:oJB(A.rlI:U:Xp,.FPf,nD(4+9kKDi'h*
%qSP7eY^a@a_7r];!gulRqoMs'='Q%_WR2=928@oQKSKn)!Q>:>.aEe/Y"'8/F(t'fUs<JB>GW,ifr09...OgP4k^8A0%-Ko(fb2.
%_^XESh:clpo*e$$Ff8r=A)*o'78S^#^b1:p\RjDYnQ_19#K,S'0OYe$0j#]Y1C!-4@UT8/fb.HgR+#91/em0+Gql&;gu.gbY[Su3
%8<$kZ/7<V#lEY@!ka^D$]te6H!7S4/64-(rS=DD+IDY$KEACo1.iP_?%)5FNYf_a\R.q2;X:DX]ON_^g4FjJrCP:@;[-lXK&e^hj
%$6]5K!E@HLqcj3hC2c!,9.e3<fF\2cg:IglfHLN1QBhOUmMq>O8kFDf1HqjQ4r)*q29]R#<^ZslLCJH7"?tMd%aU*<8(].FA\WhC
%6=JYB8a2+sI2(jLYlgFM1!$TbhEtpC'BY/P(r=u.0UDD5N7&q25a;!'g]qG%%AWdfg@=@Ic)$[%>qp0f(ZHDqpO.diI]mO9<KF@a
%-ZRb's40;0N@=,Q):WL1Gin%-G2N=7aTTJ]g=FMd%5\o[#0moFYlb:ULdrCg57Zcc!*?^d^+^()KW/Hb3RLf3_M0n:-QF%BqNa*e
%/7VPe>)J=&e_;%j<"2B8OWl;X?UOr%@pN,^CR28XYm%.gb&CSo0GCJi,7-u/N34;.pQ4`oG_B.Q)tkKVmfq=?IN)0)0MW.igj@:9
%74Z*(,P^&Qi%M?>.gBCgpD>?s^1(&jEYupCjq3oKZ=1e(IU'HQYZX9o2@>jd!&PC=NE6[YMKSt&J!(iN\L\Afo'FL3[Ih<qS]W*0
%I/>52ETkLf$a"mu2#m>abFaR"!.SA\"e;-T:c!-O&rG8LBH+[a]ZokhhG-FB%EBc9-:Cb(<:4P_>@(Kp=*.lPYTLO]>jd9WPniW_
%,Sn!Kl6Qi`TssQ0f@4fm4V8E=;kfYS_JWt,E4Dk"VFs-R[aDV<_3$D3-]D?*U"J]`K@_#m&5S&Ym1_;chX8KAca_$m!JBN3a9iKh
%mA05KHUo/2&g)_[SS4)'?(>`d"\E.2+eW44dU8UC/,FBR"F<K8p%\&YYVu3u^OH$$M[*H*QdL)pAkQVChjoHVonu9>c`UF<5Cas1
%<$e0n[fj@N-(f0YZSKlPR;r8Cd?jP]**WElI0ZfdKtMSqK8BSKNF6TP04\uN+Wrfn^[MT#4IcUf``3D9H^[p3"tJO'XWFXd&8i)J
%"]\@W[qtj@Pj=a7fO/"5cHIt45)Q(UNPOnk%j%.=Wj3Z'gf.*2&m*@8HT!c,-Z]1GY1Sn,,mN"OTqXP0[m&Y^i<D"QOsNIA29[/a
%<=f$[I35mgZ-Gl"c#Q2'(U.2Z#[8I]R6.-hq/"rkY'p);Y<UN3YR$snh$tE<Z*"=.[iS<I;VT;&#hgK,>=jQBDgW8iZp>%$fm&\.
%1Cca6okJL<jI!(&KbaUgNT?t3O^qWR;O`4n?;Ipn_d87LD?=(W(pP*G?PH8!nVY>%3d^ET]_8AX"SSkR[<;*-CBQj(U"`l^lf6n&
%b3T)Aan+QpBuQbn#eD#lO_mDQ84EYl&D9t%dPBP,VOWp;Ke.oC/[K@<N+tYn1<XnS#,6G.JhP<L=hGpr$*>"eJ20fZF4aPk:Jq?Q
%XBD#.f:a&Si&@f4+TaOX"$H5rU>%mZq(lpj#,ng6]n'/4q1)4>EW*TCAob$k0Mkp6_uM-J\ib`%lZ%n%3%Ml9m;X_t!#P$0-%#bc
%YQB5Q?M6IBE4ZY@\Y?MQXsjkc$M<=tVd%'g.'CgUl3dIgdL;MJhJ49/:,bS_*Z"f)!Vs:J.#cGb.0lLJWC^A"AV^O_&7+J%(5NE2
%ka0Nd4*Ob:lKTSp<p&d)StZ#9`@O=i;Pl'tO(lSY\=%k?.bNa?(spCGp(b=@MuA65b$5(Q)/HQ1"`UHXSW&6_hU_Vn=7<2i^PEt)
%Uq;[Lb&7@)0'ZJE+Tb>Td/<n(%@D(hd@Kecj:OPl-8[!ZBD=c>IfY("K\5S$0m`!X./`K9g-rnAjBT5'J<rmh$<*S`,8QY>g9::V
%q!IifWhLl>a3s]l$M1WK$*[Q>D/;XJ&\7gdh=)am5ia1\*A:XR@86n4*,OE@:4o`c^`j`i"B33NR.b@:7qXc-;6GA<4bh2,2hqjF
%7o85G&^((!+_Hdn<`1TE=UJ/K[a.+VVS(Ls8W$=BEr7P$O'j$o+C2PDGK,LcI9I\5;uDZ0q\2AM>t?Y[.`qOaVfI.QTK)Z7/!(cE
%*$\iqbG@mg/m1m(c\#r*YQdWjcEr6@?A:W0BOt;f`4;B+E2Hj+3=<&(*bD$JUIogR)9g#rp<gb&],c>8*G0dj')7*+UG^S4BVZ<&
%M`j]oTgNq>\WF'iL6$;C`(>`jA<e(Q)%:AdX,WG-'a]@J>B%sL")=;.Kmt1DGlIGAauq(qeU6?6,&uS<B^uM*BY%l)!o+Unfc[u]
%PaAS!H(sQ'M%RW*/%p%S0o*[LjFK'%]?6ucch2!?QN<k(OC8jANia\@k(24iY2r127R;[%80`ZUYFJ]e55\OblQ;L%pHqj]\3pd!
%jHua)&U=GD/S.;WS9p,1@[E/f"\[_c9/)Mj6^u"_V&d?mm\eS-jN-U`mS-+l4bM7<i8,+'U:6eu1ACF>TDN&>#J5>J.(Td<_mib,
%VBk>DHa)%[E]pkJ^4#c<dn:Jab16HDO]MTf:YQtRBGhnio-he[?rWXt&YU)gb:Zp,S?73-gFn\Bn*-q8!5]'&gt<q(I?d81%7^$!
%h'DdD23`B\H^a-pXmtF_^iJa54%pYmJm9\n#S^!oDA"g=l+inmc8HhfoW"&S_l_%Ja&;ucZfTr#2&8nqX^VW!g>:P28h/fZA8W_Y
%6f#=9=0UKM>j?uE!W?/K2BrR$Ai'7ufsAS9q3o"j-sTa7F"N#T\*kf\"Q@p7?Yn(nd-Z1E$Db=LpfWoi-:9d8J\_$MDMT$sZ4(6*
%F;m)'_5GtiBV\B/^V_*Q%CjZ)&d(g6>ZdlOT]+SQ`ESl/76/L@V4'O)K@g2UeTmHFgGTmC>)C^fHG\[2=>b<@:t\mol!jjVKU?u_
%fXg<8#jBnf2-;9D^pR\X^Mr?'(-qE%H8Q>Ds+P0o3qXqV_Q%^&8_:cbIXJ'Wmpt00^X"AjU="kGmBMd\5.u,!nm6YU-q)i%X'nRB
%7n+4^:fpEr5mAB>#mQm"V!_ZU<Wln5V[[&KJf''VC*+ZO$LTUf0S56HJ"g03_(c6@>u=!BXP1=D4::!hbfRq9ApPH0&-ct^8]EPF
%nB*(&_)H6B_G=NR5lZ_T%\A6-1k=YQecQ5!lcb#>nqP2DXOHf1,QV%F#qD(GY?M*ZOn^/^-U86tC8>/F,J*UaE8F:V>ucbZY=5qD
%f*G-2E+EpfN&g`,`IeUR\q;A`YpZ=!qYpA!"h3Ah\GiZke;_NG(.8prOnr@:_5ooCFrg-1#O*8gG;>J&_?6OpI/>Z[A<6CdTH;OI
%md;.JW.Md$[c]Rg77Ie4aU*"k+oF:\i-YA^js+Q07Hm&+5"Vnf2]^cs+TY+&I/u,$_>mt:#mNUHb31\o?<."\RK!gc'L,^X)N`a@
%>jF9Y:Dee:A;r.HDK8H[-XKY[<f&sP8[N?h;Fr5]2RBpW]F`?t?52Nm05DS#OOON^?q;Z7#TILj,ga=NoRPL0Y5oXmC#2(p(/$;Q
%;blSATr2g?o)TI8Rt>)SQiaZj!30%sEB97-=5fQpWq^/FDXrWU8.Re)]q)DS2\Q-1`J9lNA^uB.bOW.V_+-,fE(*K?$.--of!Ke$
%QcUF?(uk[rV8QgGk:.Wt-p]c9_?n5'i1btlZN/Pb@7d*5h=ZD5NsQ<uY&KO$5&.`FB`ot_A3\N&YkT%p\lKAQSe:1s)ONsZ"[`.T
%Wo75`@OZ5T@g3KrJlW?A-ui$(i/0KbqL:qNl'E<G<l:[o6Q7K0V04g3MfL$PVr$E)^pNBFO;]@\=cZ"7=pd'n2Ep!6g3]nYPuK%Y
%+l>^.X/>E,,-2DLUALHeWO'CsX0=OIR?nKG:ik,&&rJS]hG][2`\[\H-)K!4/MV[tD;dTfN.pj.ZFCp5V6P<g"/(JFK;I+e\qu^S
%;Me.l6d_7](F0YRF+jV/FenZKg04D652'u#Q7NEp:</]k\_@.<QL5HE&K1XDR6+f@<Ef]D;0Y2'fQ)\_LH.g=8s)rWX?)#+^`a[#
%dT08Z1mqdTK4ig5c^T3?d"G6uE9r`5*&BK1&4AO%iY>;2/KmQn"iu=SC)D%%+\CT,$leRJh+OB6KbuIp`F3-TF9oG!&/9;!]4tXj
%c"'0_JYXS/))=`[r>VJ$V202WSIqAH,m:2Xei.XQFqIZPMl"YTVu?,&P=t9Q%jDS/-LmNka>tkP%f*<07d8Td1nQ?<)aBYBQpZ,u
%/V4q?)SMkN'_Y6^-RtDk6Pa:,g?T$u7MmSu^gY>Nm1CZa_"7o\G3*Ya/[hYAI+$*%7_Zg]!;Np+-@"QTEZ/'-k1l=gP<>Vro2nqJ
%$&UOecbmEQ&1?E1)#QN\;TjXAB?\[N%NTqD'sIH#;bIaHG._O_8>m=t8e%"I)<-XV)EoU>2A7B?M.,N^./JU#^s+QZgf3iiM8J3q
%JSaUU]IlHL;4qV_IgP.7;ZIO7n$[79GG?lM-<o]0@)ji[oK3<NnBn@C&lf'9O)=6D&f_!0)gb+m8E;*.!qS4CP=]N;f8ss.6/CN4
%M"]#e6nE$T.(cnNG%W[GgaX@tF&@o1'P?,B[*Mb@L8;0_Sr^d=8Kf"N]%#8JLirPGD!=bd"nX[]0C1Ui@Qdh,:+.dc1_)Z>fiKR3
%1Y3qJ<QkK5RB"2g92_\2'HODJfRcD/h5kVUndl`OFQ9QQN[mnX2MV)h#@^Mb]a"n;L0bs.5rZr'iiSr;Xg7a?go<ZI`f72$XNRXd
%ZR\t`ojl^8GN^=tfi!:^$e1@jRrs\3J=t+lWIZTI^VP<L@W0-<#O@BS-m=79c6WE9Si.9VXWMbE']H"e\oe*"G9[]-Q3Mc@Dg/L*
%G#4_&qA-)\!<9d"@LgUU;.Ia*+@X#3RKL??2U:V*/3007Wo:/[5/+3H^ep.1$3LbXN?8F`Vs)/;6)N>3Ja@MR8u-kDen&N'-j3F0
%Ps%KX:;7?t-lhp:8j(b&oo>d`hp)(XG&bVq+39VK?o\k#0&>fTIbQ^PX]j.B&fO>/$cZiP"IfsN%nS>Gm%;1]]>]Tu5'HFNh]a_&
%LpK@__=LOgR!6C!*+%un-:Y2OrALan?MqSH?c'W!-#'e96P:D:/R-9r`?#Ye0fm,6_K'94[%k$pR&U)'2DcHDR,Qd%gRrX.gCk"X
%-Bq3/j&"*k,Lf`KXsR]`rAdnUn:AnUT]XmJe8u^.lXo6qPGU>B1?k'%X>,_#0K]@O2&mtEUua,%-QNMeT@0mN'''(9K(P^n78V*,
%,8o74kCp!^fe:AB?H&1.4b$gj>BS"(o@820^gZ?A)9<EPnH6;;P!;qH4%(ppjVq?U+9-b3M#[:LN;dB^7KkNn^PL$gFPd$A.\hQr
%nj!+,k-)mfmQ^@cl^.W\iUlo+cl2qV$gG9,8Dcs_ihkE<e3T9,Amo75TiP:F-\oWjP(dSFU9=YUUX_QE9hS@@%>B<D!11_S?PX^%
%ma3%?&jdM;H?p.PbCbE?<Npo#&I_P)o"1APY0q)O6:s'q9#M]lclOnRUq]Eb;76H/@1C$>SK[8D;<)Ud656K3@`o-b7I]4tf9O'*
%@BNLP8%7k?eRSr).O3M.aXXJ!TYgTp1U'pjE6deM"S(c;2F_I[_AF;i6cl_pGUCpk*O)8kB.V`6>D<5BJ@LeSBGd/Ab/fk,)sq9t
%a9%kDICr8P3W-V9rOT)]k6M3b\aS1Q%)Zar)J>%2P6_HKK?)%FOK4=.@U0JOOi]e9L-?'7Y3m+a1\CDL?)^u:gV(W95@99$fD9SY
%(#a4si/R]Z6\Tu:HZ]_uo2QHQNeJZYPs'FF]R9g;fi>V;7(uko6a"2Eccq7L3J6b=H87lf;N")C()pX43dn'.UF^h9eYb<*a$sV0
%W]F.80jo%Y-W;)M+Z_'-E\cI11B)"He-"*sNBOaiWWujl_La?hZ6/+KQjsLc#ZRQ3RWC?q0^]i(Lj&mPY?:t)$8Q"cMH@#ib;e-G
%%.:PDPVV&ULNY^q[,2V]PBt;pB]YX.RaL"6AH-sNf=qb7[?2_-Ta)Gd&rkT[+QHBATccX2k_@l$3`\RH6VMrbDp,Q4mIi?g8b`0P
%]agk9Ki(J3jR,i^os$YmFC\sol<T9g`es&";q6WHj?7cP`11h)=K>]XY3qH=85ht3N3Y?!HPFmFr%Z9%i6^T)JIXQoa_PfV>&W2S
%NcLSV#lkLd/8g3Fo,WDIm>nrnh*9_?\P55?'&>c>ANh[(RgSB#Io4,i$Z://&1AkRlpf%0C\pP"ag@?+JqC0tcEKG<W)4:YlH4Y)
%rBh^Q-!UX!ON0F5l][itY`DrX6Vok"5CP\hS/#=!6%.#+qVkr,B?3D'o0G4%L6&b<])8q",5do)JDU4a?=lM)Q>Q$M!"Sbm6\(;>
%J5%U\fn\*'#];O6b*6MBk:CCMZ#ik?<Y>Yab*oFc8:hH2_O'-TL"+8do1uYSpr636_)GsP0&[W-%`N`*Kg/Yl)GYj^>FJ$i-u5+8
%9OEBIY!gspo#*5Nk\!\8?4,oTAB@_Pcm-=6dRpu:^#"<RDl$+LM.K`?6d)4e8iN\i\%CQhpt?Mb-,_hGnk2meFFr4o/C^=6V"U#m
%'5@d[IY!Ne9?.1gP,?4QM!L1?[^QQX=GMES"U8b*rqeYaZdjZ'DCPU^fgh3[6OciQ;:qLBpaPRCQ!bTA*k5;bRY4%;JY^]^0Ea--
%)C(%>TC;-(74V5ZTbgsN'+dp:EI7`!EppBTT9=A_Ck?'ojt-PX0.4"%Mcbjajj:"*=n^tS=qnJUoS;0c6N:k`?0h;>G"0[+9+mbh
%HgUUc=6\aE+QRc>_,uGF$K0bO.BT:"+dOX4$Ii9Iob*i8/J)Q@!hJZe+L49iHbC&)ed.pY<;.&j\#.)_i,SL"d5s6I_RS)<.2(MQ
%+S"$O.7keSB>l4(U68s4MR3q7OO+lL6m'hti%Q2nJ>kl75]$9k0Kpk=UcX['mQ2IJp#0n0#^'/'nbIm=pLi^U5^?Xu:F*X7hZ@uc
%Q%;47KiRG^i%C^,@;.ZXiRt"6/i=qNl3sB^N%5$R3_>)DY9;B/.NOFeU6uQ^"A)tncVZ'Z('A*"[>`k@Au]q&7PQBcSi>!Y#WqD>
%=d@BD]es^>(fdn,XAK29RJ&g[fM<n9NMOl2R0(X"qIY88SW[SN;4ts_^TA(B&=4eOY37KXGFnKZ1sp*82B-R=&P__!@TX%OS(s2o
%6s_db`H"j8#1o6aY%r@'Yo^l"h;X%*b,r/+=G@S4CR[6c)?RP\W=Uu3lOc7Em"%1R&uIP':(p5)7`H%sX&BQgSK^G<-tIl8A$Hda
%G.[HCq;=^$ahZ]Alo]-8[>:$GMW0nVbrOoV,sk]5rh)]kpq`gMei:RGI/E>H*I*l;#FT<7LaVQ*]ZTB(e3KGNg\5]X7i5J.XW8g;
%R*@(18!"'LAMd>5"9msBc<MgRnB?kGqAX);G6RC2`pJ@UR.-rkcEhDTg!.\?m_gWmWXA7_&K`1uI*l;""-32XCIM`]/n!1#0hju'
%6/j'\..'51"L/+N`iUma.\TccHE(r]<6dY.PjjF]P40W7&c#(->60*O(L]oS<HR,Wn64E)+^EDlF,-elk!?2K>)Z8<8aiZr&;(IB
%8BZ'V4,n`K,kYE`;En]k/(g>!%U^1P;\;f(;Djt'5oaU/iM3[ClNN&&?l9N*@=*X"$+cT>l:C3>P(nksCaH[,`Y4H@OnNs5CcfJY
%Brj_p6FPU8SO:u/(]Y;G(>`OG^q::nG<INqW8nqAITqt@VS)*+C7CL%Ok#G`Af"7Q?q^EX>oAj46quYQH@DD*pkVNPQ>Ta'42pXu
%[S"[tmL*3`kcLc$-tV9pP\\fS?U=3QmT$Pjfn2UuJfN&$:du\R@"uIO-u&g8J.e?gaAsUp3p\7`,%)F#.e:X4C!Rea_uBSnVm-'"
%.@#n?ZLtIsEFbq`(n3H6QdXL\kB4):SZS?6_!n7R9,BL,r:%t%rpQphf)IiY%Y4'J,?0W9bN\rE%>CmGi:phuMsuKBRr$RTE'le"
%Fd&h;b".mY:E%W/T_X*elo^T$<1lB"&5G#G+cKCZck2)XndhJ)5;Q+0*Kmm^-;d*dIZDJg@C-LsS>(#7ameJP-Dnr'BU]FRh&VS5
%(=MeN'tDr*$#2g<r49_6!Y;Asinep7AhpC.p8Fsefk?*DC;&f@G"73n_n:^F6/5?_`<Pf?;^.!l8o)cu=Lq[a'N8la@9^]?OpQC:
%dOn?9?ot`Nq3[u$bfY57[3a3[rFT!X/2YWaaYh`$%nH&k48djl^H[+\TMA>7F;IK-bSoi$7`u8ikta#m$rQf>]>e@JS'uLi_W15G
%Z"2tO#+Yea$o@%s9$:_ST,'6@^_$=D45QBrrjpQ,HEJ,9+:nuh[hflI8i0Fe,/%klTlDq^<M&NB%(oikQF=g+Dn-!eLY(d.if)rY
%lMgNH0o/6<BC%C3QD:'rjPB8+)pRcmA&QsKL@.[$H'e7J7qjD)^'sIo=YrN#5#H81M=&$oW:`LLq_iL/mDl-Fk_`4k.\"M)V">8u
%`%TH)mlYU>aI^Gb"LtlppH1GTg`?N2-k::!RlakCL:3.`X],h.J.8;D?[fMn/U5Aq1FhA"L_-st_=UhU)p"s1dYjs?P"*e*lE\lQ
%KFo)jpotK19iuZ?EngqV;=bP2bmqhM@l20)oMX5LeK-sE%^j&SM,Y^-a-:1"%PrDgT#\g/Dd1Dm?2&V8<]l[kUX.S(*Uqg4+(j\L
%0PT7I%HeKmnhAchK5n'[?hPH3:tgs6?9:uQb(RPNm@eDmN`m="I\e-eWmD<I/cY<J>btQ<qU*8NF]2eA?*;uVpa/ER[?-qfR1i%Z
%a'[j=T&\EQBDJl"elD*gd!/VZ2.(THs"/QR?fTtb9Y4;>2*3]/G\3^K"#)ndD2\H_g:"[98L@dN^hcXFaW`ob:-s!0j'si`eRq8B
%e"7)Iq=[4^WE3k'$l&X1AK<EP2PaRC2kj<R55X^93%WI+[Wi3oFC'nor6+F(p(X-3.\h@dnpp=,)>"<EMAIL>]mAd-#qdfP
%7GM`]oCVVpAe2TF.c(^neDKHsLJG7`.JUNe8q%]r$Kf)u<JuG=QQp>aA6"f/)tl]a(YItSp`s<IhdJfS6JJ.7\]qnMjF3(#/-:%:
%4>Pp:C@"E(`Nn:PgC@^(]h@&CQI/stV^qb[CM,[NaWsJgm2uB-QNu^JT&fusNECP,)8']DdDt$n#?d&6O0A:(DhqCA-#W4;-e#""
%ibs3_Aj\u2L1MsEJZ9]EAZ=;cTqoS']`JM%*>Rjsg^]2<%Y7:\<fntEkM9QjP4!bVQaE/O,G)aMKglsS2#$-mguKXZ$=_M,G@5eN
%M^#:6nT#4@[&E4?#87(k[lP5MXM:ebGM#:m@s&-::&dWpo47FQoa)P/<5CqYK_02gO036=:`l3,bV(bK1g2"q$.uF9J9A#W+=6[e
%Cni@j%p<N?"=3B$Yt!.Tp/WJ\<WQ</IL"tQ33;5p@4\iA5MuR*[LR8++!CIf""gm4>_a0KO27CVg_9L3:9_W%Dai=u>XiJLL7d>0
%o'jC!_[_(-RpI?MF4.@Ek,Kl`%[ZpG],N3^0'=/#QUop"Vj9%Q=hi#N=s@J-,_g]&%iCGMP@oKKdcf^sk8%hq'M4-UZ)IbQ!eVk]
%H'/=G``_.11/W\*[mNe\RV;3n`3m%Kf[s+iquc1W9*nDf$rckYGRl4uX5#ffhUQ8AfW$FKfHL4M<s]soS't&dq7Q53,^&&8/FnI4
%S0Qq%P?`I".Lt&R)6e6=OrS(\c)PG`d11KI,BC5Q):X3qKAXb6lWXCB1!,9'()?6,9"m6@4P!n,WX,'Z1`\8-\M_5;1=GQpWhm-5
%KHe@'$`uu*jqN[rHVK37GrUt0\opY3QmRBQMD.U!ZkU$b@5KKM(#?)!Le.RM+V/u=%ss/KC-u(H!sIrXg@bF=VN)fWXn(r-+/pHq
%\`(-;.'+eBcX.-sb4ppSE?2]_<DQgd5dOt%'9BQ>lTn"grqR!!&?tVkC"KMFQh]W1(nqfnohgd)D*>e$ko"\6hR/eCSSDI)7Z(6`
%X)*=<pI-^#KkHE+Z^-97;4E"7&k;**VH?-RQ,m&elAer@YZ%EJQP<&+GPd-WLWsP'T-b^W)V`h:R\ZcT1PBi'ZgjTi9_)54UOH^-
%K=:!"5sVjlPgB9E3<0R]/J@7E@:WsibDCn21+J\7U<R(^`hQDk&Bg;);4a<nN,VEF\oe.V!'+WE]70ffmH1#SiF2ue80>O,Hp2^"
%Rnom43R]_I%^WZ"$'<_eNtL/9$D[:9FER!WF"::](kG9HVpi>gTF&=bm_Dp-R?Q]E$iW-<#I1P<a"$Ju\rM<BL.rM&S7APs:4)I<
%Z3\e&%N]p\!ZGu?l>KG7s/>UAo<"%-s""`Zqj>]J^ip)b1%d2.."^o>n:DnkLYW;<^Zsb`Y*[06qHEH4+B:7)D6X`t4bV1+$(<`'
%eB*hP"g%eE)=o#5I<k84Qe6&`epBdo.)o0'ggZX6da.b6H;]>"%k4^rr(=gE`U?\qrB_\\.j-%I?R$sIeIQW?@J2HI#3XP<+!$Gc
%^CNE:&8<5@h36RSqT#Hs69KM)D7(]Tk93R1T7W&Y?`:,<r`p(_/%#_HWQ59$*Lak;&SAlWq!Q648OY*H_3?)-LVoX"Q]_+2YON$k
%^rJV`]o*,E5!tO=$-u![_Wh0CESpkL\V5OX&4F%n<B7r?_IXV0p.HI6aB)clfQ>#tkdL^aVaqb79P(pgec8G48nJ-PM7.5-DU(N@
%OF8K1"q;?^V3@3>OCjObiW>WmFD(IiK3+EPfe2QRPo]1nM9bmnX;`kBdQIX9GD`qKoap'b-G(V(k:-NgEP]92"][H8co`rt]&Z9m
%oV'U9P%tJTTJ1qihl1)P^^Y4A&G2HZ\G2H%NXY+F7I2'=*G!3bYTgrm(C.Q)H.n#<(uU4O0lRj9N$h,E;?5gC=<`a&eJ!IrX$)Em
%=nq[;o:<ohL!BG6fbi$&cXH!SG$HT"Da0CS<8sbB@)\0(L^Ps6=p3e?hfhR9`aMj1!`qi3/i):$-9[N7jp79@!30ItBRCA-S"u06
%j;W?tXiQ>5X$=Tcd"P@ceHJ5C.":W&Y/HVq;?5-@I*!-qB-7gV!,k8A!07[T[`G1qm5kI'a`LgPf"'6iZhD?%3c<"OUhn70V%enO
%cU;Gko$jMLS!fE.W1UrPl\sn`UPdBOe0E.qNVcfjFu\3R&Z&%#qB"o'Y`fu27^>b:-ikV3OV*[j%2P?O5ojp?">u5H!:XNOi%#>]
%h75mJ`g]B.YZ#RJp)>6X&OA)A%lV]MJI'T*3=ehHNl]n7i-O:FX!#IE&=$&#GWLi;MFG,`U5Pk5A_^U(9DET\'1k?MqDrCbU0OL5
%8]e%D<hDfs$9<iM#:[3<fRp$E>j=XB@9!QEEPM>.I%&5,12(QkO`Wo]S\U%jcZk1WO*0j+OK?F0OId@-6!c.fIY;b(mP]`dgEfSq
%F#Eo'p>32KNoS1rgs9K-s%md?bo1_A!(KAro8[P0#Z.]dUdQFUNFmrUZ=]jXp9;pRCG[;6+830^qDmsUO]_i!I"28TPXMeB<*!PB
%_o!S:-"a$A!Z,IOh@$_fX!HAMOsR1A]MZEpSSEQd+A.H-CsdMXObLUcB!IOK,DS,d0-o]Q<e?3);CHt(\=*#Y'gIt6SCi_n4_*@E
%695.]p#-$I4GLMUP0IR/es;b0j:,9;(WdSrd>I3e"qXt<HQ]^p=a*0=A7r'%N2^@U(S?\D9Z7k%M]DO^K^Gp.@D@=-[1g,31QtFE
%'i&XCf79J'H!ZR]L86=FDF9SR\9-_s7:K1Q"ce9l8#B^k(C.<DUiIn]rAY@YW<FEd@jOmQE.E09IN2<fP?C[e/!,@poC$4rCPdO2
%<^*obh`[ZCe,4'e]JfEbkmf]^Z7_7!"]t]7C)0%]C=L5tCbU<W*Wh&l\J:dZB1,t&,Xub1:?FF>-r!o(/%X*jM!FNcRXe?;`"A#(
%::CcLN/4JV=Ns2:b)CE-`cX*q0jI:RiCjE9ArDROL+(E^MMo9K6cLE$`^/RIb_@B?Kt5i$AEK>EBt$b\BoK0o!%7@P:,?M[;'%"A
%b]2nc<FX9;!u!H'GJ$C'@tL[H7\$+kWL,6f(u2@#F:KJ?@g+Y)=r=)Q66@>rLpqJ"l6WdX3RVQHR+-B\6pa4\ikQ]R21Rko@uZ,;
%d9AHFM3d)L$!VA3Rpi;6KF6'=&V9=3lBAFh5IC@jK_<8Hfq?7n!"sG@$^F@j5\;Hu>1VAS'HE9pi6^V$Z,hTXD9*Y3205I-9r!6_
%ql/T3&h#N:P[N^7BN?c6EJEX0f^uo+U+:=REW,"bfZ([Dfr$Uh86#J\CcI51Iq!Y3DJn6BH&DE:?Xk=Uk3jTP6F-n?R./W!_;\F6
%pJ&YT(H'%7\.L3Ci,YIN?ZMnoSH0WC"D!l4+jV$m,#e.9UuiG#Y88%QcE.67pOT6]Qjkk]\:BmfD.2Q<?tK2JeMb5mhO8"qbWbcc
%aisS^Y]^@gTuo=j,%$o2q=,():d\KA/>60nl')KLS0kXGVkTMZk%fQt1!h^n1t3HBFq($.Vh<GI,<Rb>';TW":G13%D9sSTAum*p
%m*Xmg9c9h531`WY[t$j)RhR(bd]<EMAIL>\GXU@K_C$_p>A:^?#4j\ZO0&#$7hb0M^>>Soa-e'd@'g@Rrj3t@Z@=HmmV
%CbR'uVa]p@b$sVN@1!-ni:gPEFrD"\BU8aS*Ep6iUl.j*<#9D]F0]&T%d)=KmA1#G>8C'2qZS5mAQAlio6rJ%Yo'')IsC2g`rULe
%K#Eh5&iqRen/d9i)k5XH;5P.qABcfc&s38f.OgcBEq[r^`Mup:lOIekTkNK!P/I"*(dQ[hm\6Bqg:p;D"ON\3R\EDe=cV>C-r&C4
%.'6tPBc7K_6dUV)Bkm7hVGiU2\+:E[hm_D<8CW7&+%n'pd4.@8SFgs';E2>pQ+P4>TCnYT5kOg755Ecb3JU0PO&)H5ocDRZGuA.1
%L4uZo;G"6g`%c`*X97,nR7F:"]gOQ,Ij*?;bp+?l$8DmtSs;0#=g&@jiY0ZTj[fO,YCb'V[=k,(!;L"L/8CfC.SH3M_K>\h/3Bck
%k%1e%7jY"5N:D:/"U&3*I<G)2&PTA7YZ^FhT/\qn9F5#^bY3KIP<DV4'>U>6Aje4FDP0I3LPIe'1bf^>mr:od8aQ+Y:h&`)+YsXd
%m5lZC8M40Jo'n3n_7#MQ82%eqF`8Z8!%uh^\q(H02Be?aiHU[&m3c=VH9h]*8kA4I%Qaf='*L8C6<!\e5UcI8->t1uf9K!<-43N<
%Uq.0>;pl9i_/!H0GI>9=-2>@/q7b/9FX/Lj;`Yn<<^s`uR$hET;F*%HW8K@b&VN%B?raWfZ*1[;`RaB']?>NZMMAU&2p)J([LL&D
%&rWj!h09(X.fh$]7'AYSGni^eVlX&qD2#uDEhuZnF]f@V`K;"9hXA[[hY3K#A.=:e_n]m_4,m(55m+XX,dE+DV/W^(Gr%:B\7c0^
%WEZ@EW$Xm(U/can">NJ)[n9ML"&>aV&nT1!fnk31AfQHR0e;H:mDq[)XkJtE,CM\*htEhlMt%+?-HQ<AhY9kT7pm3R25U/nVuU4`
%n"<R1"a)5AfF&g#Qag41V7TgR00h;f8FC1!J76`J.<uS:d%BX?,5J<',XB21hFKmaE[fG+em2Z]fcieq"h,[WG--Xkj<L`8E"3Kl
%mUl;@`BRl_l,5bn`)%9rK8Lh)&pd$,Gq2EI[t':7lNJ]1]];d_Y.$!E%)+InJbBP"X"eC"momEO$6Gs*$UufRoo=5Q\o7HXZ7NA^
%+W<DmCndD4R>OquCL2#uDp\EZ?BOtiH3ogGN)I:BYo<NO$Zr.Y/':=S"?38qQSbT;!p`)lXBpU9(N=<E(3p%LA7>1T_(6;]"p$.7
%T.Xk7CJ2##eE#k/<B1O?ANUU\>8;,;3&ac5JFQJ*`=/E]ZGsu'5tse)h5$>'=_a::m,?C&KsMH+Ma]*6g1<"6gB-C[qPL-H,*%d9
%^r^3hOGno'#*iKrDeT9a>)1dK6*`EXEjM"P[$;8&MqQKoGg#9Zpg5Z[joJ>lkX;!Z?I;]V>4uD.9>J8m`!=NJjq4@g'SO2[NdG?5
%c"RRl,A8jG>We/aX#EV,csPQ`Q,).:UXW'j'p<WqRr%Qu122Mo'(^'["g!>j-%oYPJ/(O>5<s7]$>As9^oQ!E95923_aC^:C]#P2
%'O!;_7-mA'Ylfedpqtt#LcH55"Hn\-@R="6oKH:TGW;Ei2n#;V(.$c@<i(-N*B>FJm1LrP7FJUZElDuGPA*pF;[>jMRpZu^N$Oc#
%SZ6/(\UZ66k@<d.'ATM7.;YSbDuHDE7a*6JN[,%o8]Nt37)\7>0uBV=,qHJeC8D`K3*9l$&iXVRB>GCiqauffAKZTcj9d/h/#4aD
%8CABlQGM/sc?[VX+@F7JXkk<'9=dDc"X\Lp(mHVD1$X:W=VVdDg&t(f0<N?CfE<?=41F)l;4$;7Xf\-%#@NH1[)TtlI?["6bc5ua
%/&MtXae%SKPbp$4$Z7sV$;psMG,<Is-871fE-OLYnZ/p[#e29o!oegR_bt+g(79_?Qm0&'!m0s,Wj@7jei8`s"VNMIqc>G%UfWpE
%asJ?,$r)R`,XIE!`WB92>@p%8Up'u,Cl3e]M]hsB#\mi]7<j8jmZta&,h)RuDta;BM^<L!i*"St9jlLOMtOD2Ec&)&2<7s"j9Cb"
%_Xq&@m1OK_(ECq1_"CM2aM_2:Oh#_U,V1Y8F1IL>+e`)hQXu>'#fma9G<rp"#>8)/IT#@B!4)2M_-H790A/TA3J&Ckf]lF&DMF)-
%IUtL6c_Y[apML!`Vif\]g"tMO=\SaaN:9GYo#D'>o/OO!R^H6Q;b36&)]e,W*np*/.KTd73YFa29+4d[`L^V*j(nY$8,2%f/sI%%
%>CRh'FWY]?AAU,8Z[h\]?4#L3A.^(a2H\n!9b\^e4(P8T!R]mHK'S)1;<kJA8s3Ye./dO-+'ntXB=L>J93@bhpC#leOM@D-H#u@`
%Z+CsrX(&75(t4EQ1G_:'4A2!9,^UH[!>&4;kQPV.*9'8b5X#6)Nu3!^/]%Xm@n&Xe0ISQ3nK6=.CD08#&tl5)MS\FMnj7Ca@#$V7
%79%\ooIRmP_/u'G(H![AMXAHqi6"q?e:5m0)G0/sk._fL/'8([-42NfecjTGm!t5"%PT]9DkC#s1VdaqUo!gEVt5Pd]@NUU*NMXM
%\t,I3q18^Z%UCs1Pt)61:CKa.>MrgtVW?^4W;@7Y!Zo`[k2it/TIY9^d/RjSVXPYdJjDj+&2AD4F6hmJ4^j!$9]8J7,P(FL_g=+1
%,gtl_(;7EhZ%M4?!)kZbDrY8"!!13#YQLpVEsY-\QTR%.DX&1ca3ipf]kB@Sd\@QsZ@-IH[UKCg,6j[RMY1IW-SS9<d//r\$1Qg7
%0rLj4gC-d6b*8$ihX5h0iMKW%fQVqdL:r7H_KIQEjg.$ZBA-$&-]mZ9PE&SCWShs9:+5hJAAW@57CZ/bW:%8U\2SCL'%W"tS/i_3
%aMs5@(=b_U&L5>PJR"(l1VOZuQ7M*aZ^hO&(A<+n"UAZ,'Q>3;Lk)15c*I,WE%_kT+Ea"n=+J*e+EIr`gLq`25tBHE"6Npn=tkdT
%5q:"]ZGa_b-`pL+.>;E*U93([b@3FKLH-(&jiSfk@XR&+)<Vcn3'frE26J8R&YuR"C8+AVU5+RjOo-QOZCV0b[/.Wh_/'5[lL`Bs
%-Su<)^e-oCJZC=7m'OM/k7TTOePRP1>jQ8C)>U9#JRI+l84p28Q%&K9:9#2bQkrKu4Ie7`<<sc_<.@s8T</c',bkPW4RpiCi1TF-
%_07Rl"$6ZUS7k$TCY%A#]Tl+;"R/Bu%%#+?mMkeC86W@L8A-:u*i1>'R9^QW*4]cs0%+Gr*93M&f/+HKL4VQ,^9t(bYhDY!\IKOt
%LLFPkKN2Qik[%$ck'/Mljf:aXoq;^]gpfa9.n?j4Q67/PE+Y(BOSNa_,XYZjKKh5/&jWsE4gYh=OqAmj%L#L9cOpYK6%#gMEY\p6
%JGE:I:C8bT\WAS_AdD5n]9+0K+[,n<Y"*H_9+[,Z/&TIk"^GaG-)!L"&lFRm=F9:*&(''.K?d%W98:RVT(:Da=ot'[fXi^Z817"b
%F75WQf5^27T[+t[`X&GJF8up0M(Xj[1-F4s!UbiY/NXH]dYWI7#5BdBYlT\B5oLpB=c,\r!<tCrnAreCJC(S(Un<Ws$-^6dm4p*[
%Ts-!XV*4rHUX:,?W8Ph=p\,n5i_CKqfYksl)a@e/S,PN_DGA!RNDM>!Re3/[:P)_0J&EdrG*)cJAo3a3+9c8`O1&`kAhRO`9lVEi
%+GcZtJ=.[[!9"M8+9G&Z#r"k(3Gde@8?`Yafsb:hNs+(bZHRGV5akN5HKA*>@PS'qM%@?GbE;Dugr9IbGP?"9Ba^I$fBC6VmZ7]=
%$6)IeJI0K2cMKlB2\NXl2A)t@HH"V7k[9/N=Np>,4D9/^KRPdQOG.Q&cuR>V"O!H^BZn;p.m^d`37S%*/[=2+8_1>k#/g2`+S*Up
%8@kB%"&muoFacdhO<GY9-<-IEa98[[Y=t,o1kNXl)SNTh_8!q+@XY[-dS;.#;J@.'^:/JK=[aWuMebGU=!PA%R1?@7I@sS+LeT_U
%;3+5!]'Q5GIXF`<q!'D,<iCU60YED^.UA?.\&-B,+K);kb1e-RB^6EtV`'Zc$'<XB#+@b&jOIC8j>tbL1eb2>VQA%`1g[>P*oeHX
%AXYk(p`541Ofq*GA"?O<?N(.T"i1=S"]u4Ah`/(XTH>N+N=k)X%F`NE!9&e>:r:"<$.u6\QB&]E9libXei]A9coIL;UREJ<]t8N;
%)8[tPminj$GbGj@#0g]].3(<%rYOsA,h;;b++$4;>G<nY)K<-:2h%0);6cAO;uf(id-JiFk0c)'!qfuh]3OGQO@qP*!REZLII*Mg
%1i4Y=.a:NdAfFF1#MAn>brs;Vn0N1hp4p!!]qQ0\^TQGaNPZDV#m;a\=JjX/dq]:EYVd\Kc1L;)8Pn5.,oS9aY=$Xh%NJY5/QjMm
%KSm>hW6kmlcX@!-q5G1G;#t"FW?ED%ep>V5JrZo_Ea?j([0o)LL9!OId&6tq,UXlD<Y0dEnt;UX,dUUS&UG7YN-+1rkq)q#PTVp!
%LXqT&-]e[:-E70eW\mN>dhiG0Q;G.oV%=r'7fd<3[0iJ#8O*_/_-"9^4[-Hnd>(_AHI%jOH,>.$`GT>+gHN@gGShh^"0uN$0jI*3
%1G'TfdHA>^-MnuI5ojEgH%(RU<VNqe]TL/]*,pZW\EH\kroOQR77#^=H3mCoPB7r?![;i&Z(b;-MJ7#?!-LVM;n.;BZjoX>V9OnJ
%GSY$A]Kf^.5oJs/.tL[H`-pt_>61F=DNGQei!;FWiQu:eQe6qK,,0P6ZDI5UV<X<h@BdHH01L8])&*7t;5kA)do<P'b34@T1q`mb
%N$FYn]orFcUL%W-2iO0"0GDBUZ^6bQDTJP!Yn@UM75.gEE5^,&'^Ot:h%Y4jI?%l^qe-lLfq+cP:?rY_rJ`GIOp%n5<E/1PpP>WC
%,%2at'U*6/(b,692&P-QN1j@V=67mB0#1q!msr;W8+<C74Ham1B`X2'5V.GEPF3o;o3Qs&W$"(!/<Rr<[tKjb(8tW7P!k_$cE.N]
%[21eJV=Y'jB`cb"Dpedpe&9R:B6/oE%qTSk_[!#Bq.qf&1(8^irR""I+raG%g,4u=%1G:(8D'hJ1Csnco3R$1"=W\9J":;SC@"sQ
%c@WP'0G#H%<I=i/a1B_Xo;*8SFsY%hk]]cg!>hU1&18f9chI(h!8+m+\DWHF^j*!b@_r]&/7enR63>An00M"rZPNU]S[bOE!)51]
%)hh=_EFjApW`m8!jbYY4n73i,Iu%Jc[Z[]mTEV6d`P(@*b$Ya`dYtUCoG-^`k2HHYHd@&kN-80.eSY_MoWRVe>`9;f)D(Nj&a9j5
%RLKCZ%l.mK.FY6K:m)qB?r'q`B\=E9JN9>N@5AeXo'%`jGMKNdR+.VV)cV'uHc$_m.!b*j<sOSH(ZXLj[(?]J9LT%E5,@]NY)/R#
%q71.0<0n5Q>k@fl>e9C<;"VR2M<("pOX3=M%35rmX;3</1IY42%(>FI'^#6'`aP%/$eS^goU4uc/l^GfFc^G&]a^+gR0]]32H_8i
%OTMQu&f:\J!&EL>5aOA,->YL$<\qG("CVO;N0CJV,Yncqqor4PkmReNpV9FtC<-9!"K!s)mUaZ`4KKjM!9$*cmqS[T.g?I)!_u`'
%Qkd_"*AB8`MnQE8'C#`T2@d8d<?@Fm!D0%@(<jIC-X@-=7s\DAn8('AeGt_-O]*o8*Hq&HD-qS*8'$?:^,"@W='cB&o+a54D3h>Z
%3S',&PQBYZZQp0"hWV$E=&s<47S>@^#?o'G@#33T(ScW+Mkj3FSXMm$7Qk,1R,sk]PWJl7b1KiDZFW@3]?iV#a]>MM@Pt_Wp;QL!
%=!jkVP@;c27GRgW/@]sVZ(@F"YL59Xa/J;Ye"W/-Fi"lP[XA9)r=CZMk896'F6eOs**Vk19%mTE&*oHoC8S1f:Ra?N#nGdu3nlC9
%-jLCPC$Jl>U,%rbWK3G8]]+Nqj+_uR\Hh92mNg,$EE7[(8efAX*BTl]p):Z*]\L0lB5nVi'Q^Z"W#6e-A\s")!$XCY-gG730U"WB
%m>`B<egt\A?-:`ZSdmi>qu/:5^L<S?!H-K,>JA7ngi'4EUB6<!Ac@H7`O9\b1*\Ktkt\@KO1]%Sdj)eK7`%:Ual"JWEb-5JOHhFn
%[r5oTp!1'WEG!54Ef-R>h3U$90W?MPg&)em'3r&Hot5!qS4K0XM.$f*oGb)Q531-.k#h%7@Sf`]N^=p2+@H$ZD<)`O\53>W`'&Kt
%lctY[,a1a\]52237$g`;%*<c@>c]Or))@^<7U"5$&loAt$_ekL:GWQpe(f.<n(R""$FZLsHe4[G3r#AN/#>(\;o6G;!TFK4f&U+G
%8ZYCt&prJQW<6WuEh[,M5g)]!AB1CBKiTID5@m^"=r8+SG<j>XZL#Fk=\Ng)<T'2ZZu_,J8oarP>t].D4b1jF;h%H;(%S"4TrutR
%I)6+Y8$jO`Q^p]T65(6E-b/<c';lYN]aO&g3<G;99&aUDb^fsXPP'g,^8XV5dAsj/?6e7V3q,HDctGQGef<(.j&`;o!P//*C0D$N
%Zt87*S9ku%<tfQ#A6g'Z(o\Y"WL%:Hl(I2Q&A?4mf-#-CTFb[<+!R&OP%oM^LZ@WnO]9+B8DTjSB-82P@W0g1ZDEtTj+cPI'!YMK
%H-Vt*?RD6&,3D:GdR9u_pms;l^68(kk-'Mub#B[L(+1sp8qYToB-6a9B(>qXE_]80GeC10,I%&I67kLp?UFqOQ]#WQOUP9jW"gWs
%[mYTfNs=@>N`qSJ+Ui"iSgtGN,atQ?7PNSiGMPbY;X.[L"]\Wbl2qP<*68`&Y)07_K,5t03G'rnWpHVcI-g*r6pL-6n-(BN2a]_@
%K<X$D`farJ]-6a>XBTV/['0r.U06WtDe?i)4*:$Ga\^pumi%$u>IT*=Xs1I(APu8+#!iV61\XX)5+k=JfH#0h:REU%TG+X'&E]o4
%ac3/&(aT`he*)Y?V;=krk#S@7&hkUQ#X[AUb#<iO*=N<5:$5PD?CF'ZSL?.OFt>PtGgtb0oIH_h[jc;_Q2nA.73[Qf`gq4/4Xq[o
%$9QU*d"L]ooZr?!=#N/!5fktp>3X7^&qs#V6NM8C8lbHu,u1iJ[!We=*s)UTam=\..jBj-nR1Q-CpO(4[PH+q/,M,31g=?eTjU3*
%U/'Up\W$IfJco!30c1MN-O;Nh]$@pMjHj<30%ol:6L.Yq>TlO]BpQbu/LjKXqQ-Xg\Q;@=:[gsh2&nk,kWn],i`'Ji7:6V'),aiu
%KN<[Z.RU6E"8B0QKp:b`[F7TRQ-@b_;kg0_#OBOX(7)B_mrgBJXLJNfN/A1hE;Fc3E$6LjPll4nmmUmC[lY1K:uB5H8k=.aEd.n[
%a?nQHmY]()$udJIUZebc*KJMc5,q<n<#upsEUOo>ZV^lc_WaI%l4talNl>75@c5/(2UNs@p,iWiXN_r)*G@;:<sF>'(GrLo8B`8,
%pV$/fP9SMuBt2?2F.NVb3?tFjFsM]u*%GJ-&s*Si!ofs!]3s)-jqnC\]ngn:3b/9M0?+VEP3`L2e8X\tiA@%"<#BG;"DPO_d*>Rf
%dQ>:t/9k>DLr+V&-)Fp4;*E+!F0atK,\6i]iZa]h,.$\TZ]N@(dOZuaHi7nb=?K;T*6T<eAL'poGN&h^D3?Zl=B[=cX=4FB"H<jX
%Ve%n8XBu!$00r?/ZMf?3LE=N&RJg!!Yq/SBECkDdjlqqaC=\RA,%(./jHEfX;F0psHKM6GL=I`4X!_>-1HVnOGZ(V@UtXok7tN<q
%d8S%6@:9Z$&j-Kd'WcI4J:eM$$:Wmpo0%\3p'MG\j[<R?HcU445?)H!k4=6W$)GKh]\I64=F/+6C/`CV.HWV`TZ!#?n]EcpQU>@R
%:e;bI5V@m/Hbr;c8mB`-,cIS8/g8Ig02!?YXqij3Z`'l^)>Qd<eA0lBR?_hq#QiRgG4I%,HYK#,Wj;am`en2Kj4VGR!]YddQUo7g
%%0/;*9r*s&fj@#9-V-^j9jX5s3]8Df%*d4&F178W-.'Zl/G8Y.3BCN^%hUFC5LKkdpqF-[gtULRqh,'G.)k$d.=I@!P>!qY.On8T
%+k7+)e14Y"+[p]0k&q;KQZZcc;,'gb@SH[)P;)2l<9TGijGTi>([3<eWm'P"fS.pXM3`(QoW5:<UK,Er/tmVX;bA4j%!g>7<<r,)
%>ro[1^X%#l25tp3YS`"),A%i/r4Y&Nb>%=+/O96V/J0XsD:F+4?L\1f`^n['I1T^=N@0t!!OOk@\<qJ(Ybj.O1&RZ^f7n3bl80/G
%nA+nOBGh8[q'N3<U5:[gV:[R;6;;X4'WmOO\Hl<96:^'$k-$':+"qZs2MnIa:&48BAW$+F$aUMJi*(P^7sWg`=FsXtULegj`qSpQ
%FTV[V;p['-*g-U<FdSpRE4&G4JmCqlX<9=hWbA(0OHA>i>P&!H;BWNiKR-Fp5Rr8%=t,B[X3fr=<cBuT6:7;t]d.8X#[p;6hD+=3
%K\S@:IId+.g9W+@,GQOj!^3?PLD:o(l:F70P`>QH&e<8R<RdS,YD5gX@&TM(6KnoG_7ho[5oNQ9/K"VF[T8T[8\-KJKJ9"!$p*Z"
%7#iq!*j_+s+*%!I9t_46>EVagF>iK*L+/n<22R)iZ8[f'2pa@G-3c5\GpZWoAaaFZ:t44DNkco#[>a`TW5<j--SB4I;NQHcE$/kG
%+s<4>cFVO(;Ys?fQM.0j9e(ld<\4u0(<%_D3I&g=)u<hLM\g\&\m4R8,i,I!ftu<B_MPB=q\O&`C$ZQWAtn`S;E](7o%\md0Aj)&
%NNR#0@E`5XS)fVn059StB.$Wt43c.!a?5(uF3W<N.q[rg&OiuF&',s8[!'^fpPLBq>\V:/hZjO!(LL6s2Q`WNVAA6fdsNpfWi`G;
%$R9Cg!do3KM][r=dA*<53-mpO<\.98otZj[]3$#j5)a%>K#`R<6EGS"&+)1`O&&(_-sB00'^&MIVf>c>V__8QaP5q9P8Xr>4]HLj
%LMG06EdZM_n&6O1jliA'oXVJMJs\^T3?=MsP'l>bKk+hMim_Fhb92-IXD9>Rl%#mGj#V1o*$7l9,0;7TOsmbK:t]9!$R0%Q%eD\'
%C(I-0!?!d4M_Sc]Eu[^R,C@&>PP5%]35dfNq$AD^i4+R+b\p21,*[.aobYf3&jUI`WJNK*but//>/RsWm2:g-lQi2ujC6%=8%'V*
%p0,nsHYY8^LDX:Sco!k>C'(ZcDukUdS^n/jH,ba"dV*L><6=l;lZnI)iEeHn/86c2A\\J%Rq7VK<F3IEX9E&>D_uqLcXmU>2)uL(
%<mjB)"Nd3%E(N+.8]1\J8dI=gh/!OYQKQAX78K%f,hMR!j.+q$61E`I1;!G[BOMAY5U!?N$Kq\MOd&0NL7"V4o3A$L$";_=5OBh=
%F"5jHOPniJ:3;EU6%XYn]?s"](rFrZ&j6uCJq_F$dc$*+X"c/;EXFktdBU378KV-F/:D[U]!=N,`CIe%Usu[lR-]bpNpd>iI5)J<
%\YU0u-j<?bh^..gbJ#lOh1fdYZl,$kZ]ao\/R]8I4G<0Wlk-T>8rd72#NR8Q19l.jX?qIhW0,k+,Qno_d6P8n/4b#WP?TfH<3QFM
%?.Ik()QPpRW^b@o#DVPn,>$3]mK@U$eJFADX90ks31+mdk=O?Kk-iu"(8]$H*aY&\U;=4CMs1a<?+kHH3A[_%pE&`QM$bJa1b%eK
%N(e_i0Hu3Y<KO_SWYm:JfoECiM4;;bT%d@r9l5Z)F77;5E?`=Y4";V)mG.)c3RYi\SW3#`ED5RA?o+i/Nq0n8,*h4e2d`Zif`$t"
%6m[AY'BS2R+%Dg4/$#-<8`-[5o?Kt^0;_Xj80k!n9N:MTZP#Ws$A4rlp8(.)"XIoX(aeDYG^LeZY&,]B32`QFE_ga(Y?3@CEEjp#
%k03$fB'YH#e<RArTSa?job'%O5F8;g`=tP0ZDpHAUaKoS@0(RLpu;fd7C*aWNj@6P_&]@C1mV,B9H(]3@GD6`>>u3omTtP"MLn^%
%BmCB6_rOkWPuN\q,LqP(#spkP!_"O$oD04j,p2Os-&r$Lrp^1*H8f;'":,[TQ:bI>Bg[]!P`WMq.#5/%!>?VL5.XW#U_anpZ01rc
%c6UZ_jh+mAn07!6KB'<EMAIL>*D*(e,/2FoXoup&-Oo+["o3Ym3l*R)"\%Z2a)&ta%RmnVH!?dc#<,L-8'MViPI^DBD")=H/GLgh
%o0k*!&<p6i$;Co0h-#\;3K6`7/?-FQes)iN]#N`=)!EE9ot9seSa6O(.k(7[1lfK%<:6VpM^pj\'l=hu'o-WF3:B+O[7jb2`Xf-"
%gp&h1c+C+;<FoJ^V^=?o.$_u=VpmO/;^[WB<C]aqeS(Ec)KCLTaG,BC<WS=A8tGR,OJO@0X-35#NoLnO]sZY&ZB[f2PJj<&HRR&7
%V4jf!@r$3`,E^\;;3%Ac)VuVk)2`jY<7n7QPfPNDoYS+id7cm46mG'*`5"r69$,@:]I@5fK$IQ`&P,+M`Eu)UB##$J@gjXoS-MuO
%0ES_O9I]UanN`)CF@Ihtf8Wac3#LW"1[JkD[=ZFehD\#$*1?5pe#SBa#;Ie1h&q@ANlD908XOnX99-[',ZpJ$1E(.ZAWA)DQ7V1C
%+aAW28oX;C]eWhUA&L<"%2tC@%\ZW!_O"5]&\O@Lp6I8X9^nj7Yd/sQ!+aZ_bWPW'!"B_g2q0U3:C8B>:8=f@gVm")Un-oUUZL0?
%ahj[#lX7eeDZ+"P=1Rl2!ul/Z5un@)_)Z6(-FG^(T=[O=%'e\J5ZWJ0_dM(IW<l1o$!P?>\[BBDjt4eA-I*qqm<=IqcmrVn9K=m$
%Ra[L70)Ih*R4:Y&BhngU'A>7COrW\]9pM^([)t/s/K7i.N!3et^=X*W\#?<t!lf:N*ADjF)hSt7)%Ba9E%$O$W')1'DcU4q`A,e^
%4kBa(<btOYJpK9h!S"0Tml+6"944HgI7j3TS=BcIRR>u:&H*d?#k7c@!Z\a2Q(*e'/*qmcI2<H.At,YV''I6D:7\U4/]gPU/"`)j
%eL;"b,"WMc9e3hVcqMg'L;]ki@cCTKq,_%02g_636Ed/4qc-'X(_'DURL2Og*?BVqV2BOKS:0\t+JBG5/Q]R?22K.[W:kBBGk+.D
%FjJmSldo0>KO7t;CGaT`^M'!C6+@4!0:1(;bWr/i:rt0,UVoef?VuGjFIZZXD)47,'N]5T*76Yuos+SfKOlJO01geY]GquFOQ'?G
%==+#FeCF!P,!Kj;PhEXWk__+PX+89Y-D9_N>RHI>)<t8ca;I<,dRus5!b<)V$jIoP]Rl]\K&.m_X"Qh<s$M=d-3+a`:''\Zi?sci
%I8L8//Kf#?!G'%r_),YY1tHds<l'($^r;HHY>T+/k%*8QGm<8O7^/<lX410+=]MfMU06FF5eGP'@+tq.U7QNK7`kpYXZ`0(?`,XR
%R:$o(!S,i:d5Dq6?-\$J\rL/B=;.q5CB9jB!KZLM..aKA#t?HQ)K#&^Y8&Ur]*p^k%][ZI^aWTPK3kFb*Qd=*^sn?DrG$I<"D0X1
%HIB`_i]*chHmm_!l`hj2&t/bO\!=I:W6_Y\n0LJ%=)-[MWEV_H[in3lQDV=igajH5%e)Rd`fML9$OpX^R9Eo^h;-&)T[QS"MC)fo
%C6lP6n:dC%@PnIs5Ffsf?)(n)Ch0``$+=S'T<9l*6j\71h+_1`e/[J#?6^Kb'7F3;T@uZm:?%k%W6d=>[Rc:n59>Y9WXG-S7bAVN
%L*iMl/M5fM?`ms6WWVWJ*O=4m-23;oKi4Q<=K<4<.3k(C+9pr!rJ%*j&j+J##o5*mo(Q$=K:b[F5*3FocDt;D<AIOGPY2\Q6`TdU
%(11b;.EK_`U*YU#LZ]Ar)LUj:/A,pc4!gqj9N6sK&7]a@h4\'.;6hc=?>9bt_Q:qT<*b3oNoRp.#\%&B^)C?1Y25r]PnG($_c1CJ
%l%P/.h6QkY!n6d`+:#1R)TGK:-XPNi/>1m<$LX9b"/mDp+kDc`Tl1AcS<-5Al=Yt7hIIp&!$)/"1-L^*C3mOEbguae1pdmuM`lq<
%5d7%M+B='X!/`e0(LS<Y;h%eF`NX!U&%uV.ShVJX"arc,0r-1%VVt[S8KDC,g^YuqdiAqT9a&::M7KBjT?J<K]qN15qSGY_9@EN[
%SS400)^eVn-FCSW5B%R"9J<4gGY;'gDGaQWXh19aY>G`E%)>DT=bF7l1]qEoS@Uf49JUj$#/P"&5m$=,+%S@,\.A?QqiZ_a=lc0Z
%/=g?QH)9W+`>Lh=17lX,r.r/pB"iK9drNq,<&A!HKCY<iS.a/#@a'&=D:/8II>6)X8ni<%7UA8@>SdcgdF`gi0+46t(V2N7Oc0I;
%0.^/Bf@!=5:iBb9D/kfEgQc-Ib4lIAj4^R21VNNUT0BjabJi"%(Unn(359h4jce#.Y't\Fp=\!8(DgFn\b"k)7q[$i(Ur"Dk3(&D
%+((DU:f7Orl)B^srcn8*mM;)cjNFI`$K[>@r97R[hV?u4c[aAWS\N9VK%GXA&8gC-J^nBr.&7O8l1Erfg+Rr<?Z!r%O01+]0-6[V
%?*0[ko@qI/s4WsLn#Ck@J*)4[e*G'@!<XCEotNEKS[F8$(I.n]41lpCpq&cN*<'=25Q:0OHm$%R8);a/^6DFnBDq/2<p\.fGkgdt
%+91R8O+64$^\aB7207C^MmD+Nbp-Qe5aYM[dn-"n2X=sQ`H%t(?cOHTHpPlEIWFh7]_>p6655Y>M_1qF`'$S\-,laL(VT[b`LCe[
%ULU:M#FO\\^f`\JbpNgod`"_0\OP36'309j[1bnV"8HDR'ofc(nU`'0&JZ.lOS*"ce99i"TkU93,J+NuGLuG$ii;:S[5%AGqDg<F
%]U\B5%re6Ik>jST$%5ZkMr[42S=e?%SqI?aj0WeAr!]J0fGOf#jurnI.uR3A_iF_/@RFe8E9JHAnEaV3p2M+!h[.un!\6]?QYBJr
%4KJXqfcn,k$'I#@Pfm$k3$AqDjD0s3^j&n:_cI8g@ZqPBK1/%Jcuq#Z%taS%pn#FT<R'>(om.fLOgoX;RKKRp8?Ncfq^Ksd!?+@q
%dB502"!u=HMIQ*f\qMiccA?Gc*a4Ut8Uj?Q2<Bst/P7Wu_0c]%V85PPKF.WeU7FfodhPgeDn(qjf)64,=.YT7Kl2I'3uZF.JDkIQ
%$GCUlBna!s8ik/Dr@=(deua0c4rM0\'bjXbYqk.>0Z2:\TUkaf)A#$H)bc'2)k@6[=>V]-1ADKSX?j$pmbnMI0pBB%N$j1h:.7=M
%6E60"XpAKP9ko=iQJ>'Me_7V%,A7-<&M/!IX'(lmBbLmSA/21hZ6_8o/nU)^!i'r.og.B/9JIfg\VK,Hbq6"f0iinJ\CP58.V9m,
%BU,\+#:_R?.tSF"N@cnA`:(mu#qf4iP\'IIEl<eb4#m(CPd"E1E5E-Q=c!!r,*Rj26n'sMaHhD,la<XO,l^nV`fnOI;*`=Y[gL_d
%p9=b=)*lVZeeS\%d"?<ZJnM+qbSGjQ*E/uB"9_7RSUp:0iQ0I_V_0_YS3\jm^qFn?iuZ3q2*K*VJ\7Kbcqpo4(c1/k@O@D&1f?YQ
%EA?n]<2aa^Lm6r`>]gF<8G\#]<FqsWU]2)bEE!7PQE2\q]Ib2qC@Xu^8n/tqcD&$UqBF8F_N26h!jecP(P"dq!]!eGS_/%7a&EVH
%5E`.d.kIZXO!i72-^\!lFB'o)!Uca,oGaG6RLj][qMSt1!J?e7]ofm@/c%)178SI<DsL_7hOM/&,B6ZEY-t-8ISOb=$)*@`h5,_C
%4%UMlB[@E2/S8!LK7g]O020QQ1j27"4VGGl0b?<["cQu2'eD<:4-Xgt9p\U)`K('m`$V"Ad?>E&;3j&0%bLTC*[NT"Xd_qV\rZ-[
%?Hq08.P2SM8bL$()U0jDP:\*`\FQ?70PCQ7nslHB+8=tDm=Y8C64:RY1f8m*4o-r.a4cIr9.3tR2G`2+4$WLVr_-$"b`_KRdj%J4
%@$=`?KDMeZY=#!X<N)b=<ni:[.-WbS>BdSWhZn-jP#R>@7WK`XCL-7$[%MM@DoR6P4/G]='+b(a!Xb8d;EK%;Y9+Yh<kQ;m:jR=k
%]M0IXa^bi\.a!mf+SD[h(!$COcq8gq5OI3i:*&P'$L?dujY#_`ijBsF69!/HW5+X60i<T;#tkqp<pY-+1*.=MJ3FeVfd';%>.de-
%TR77faYgt<P)PftK"]lD<L1A*j@gcC))nWU`W6=9+"l^lBc9H][\>?*SN\DPiguT<@V[GPAtocshT?8*%GB<+Oh=i2QQTH^#T)g;
%f.$srOQkkYm3a\&;k?s68AqpgP>6mB;[H4<*&0I3\8ju\>r]9NhF/RK+qlq/O9e(nU,?.u563bQ<G>A"+ih$$0+-[1S,leY1+K*.
%R'mek_-PTa"XDQ#W[/c$T5O1LB*j`/&9f$Qa6AZhZ:UX,&pg6t6Fh1\&K#alCTO(Q[bWU-i0A#fP>c6+BN-LmPf-5qL-1S,5]GD+
%+-eJ5W>r9saW*:kj_!O'M9dBPc0.krgT^=W4.`@P5"lI*f(ieq;f3X)aOFnP3LWG``aS*?YH4(@'K+7H%+g]*`%@bq7-ZUdYr^&T
%:WF*Q@+;-->F?ND:?&/GEtT&N6F#37.](*].o"EM#sDjOH&$.V(+`YO^*4>!94XaCH7CaL@.:rt^jbgB?"n/)A"=^qP(r7<`="h1
%\Gdq&,0-87h0XR5[\51`Wm+MgZIGRcLT'6t>>7eSPn,Nols`gRO(27IL0Wo*'!!9_N'r\=*`ag`P$:SF#.eS@GL>t/+S+-j!9nJ-
%3D&!a=k5l4*)/Nu!N$9N1l4GSMfS$f\R,M+DP?^)SoYI^::.g*!].E57EJ^:<GrVp-/G)&YAcH3-"`I-AW)4/5V<G6"X2j4KiB"R
%N>&O2l=<_63!N`ECpQ^)1_Z8jg=FP$L1#?D5c8QE+UQD.T\Hsmh`;-Sk%lBJ=6P8PMo@&h26!W'!cIDBN<553]@##X7<;$L[^5(g
%N(g\1aH6#e0LpG.hhO\0+tEXn6,HR/+N-g&aRtK;GqW-m@YHT7"\o0>`4%5W,YdqC$Zj7ob0>T^E2k,(jN5sc676`Xqnib1c0YN]
%"`A1u,QoJ-n0n&ojth?Mn7X3'+pm-XZF<9'!epRFefm;>]b+,Pq3Wnkfs#0m]`e!_,oX?<H8Wt,6Id"UWWB&@1_HL5`EVVGD1j>4
%EQ46CRE><FOOUteOa^//E2EZ3YjF!lbfbpCXkW&o&lF7NbBhZ>1RAR^->A):8Vl\JlmaP')IYoDO$_-?R=sFt\dI%a.T)Qg"841Y
%9PPE!=i%b?kmSrN)-Wt+kK4IQ*?$3B9sH!K&Rpfr*t!5o9A`?)9FRY\+=iNE??>^o9/hmQF49/o`mc7',`ar5:5qOWW,QEKAtp?"
%BRi'1f4G*cY=[7id/YU*6(4ZLZrS*o%;N8u`QlsR)e:RH;];&Rd7[k6;,OW5m`+q'geTr0DPl#YUpIU295aJT6*1A+Zf/>f`XiN5
%&.]c$eH`^@?<20&MkMn:K#(>Ib!!O_B2c(D-"`k&/84e9IniE5Hq%SNX/qI/8=5E14eNrI%A*2/iY'd+(K"AqoS.X.:74g(UFE9h
%c*"IB+'@k1<P!m?B3+:mh$gcFVkMG#"#iAm8g4^#4dR$/!&+AHmF&ce;)\mE^Abc#l^4usWLIuU1EMoc(5kS``I+1rS/c_&*%>lm
%H>h,fNL.TiWe-+BmG#KVHmf\"e(17.5j<=4TkT&qiRh3LH:j:@=Am0SY@On)aY<2so_NN+R;W6bmk"Zj%k^D5%"]ln!tf"j#"tn+
%B#B^3+_r_fF1;B>7JCVp8juK&0MKWbVoa@hF]A_gl?=>^Eba!CY:rWapJ@WL`POtjKrDK=!*F?MEt;%>M6?StfG$\:R?LOFrLXo;
%H$GhRY!)u.1$tj#B.0$Wp2FM$,5#HY7:J-^I[QL;a^Y5gi"ol^:jISV>H-bHeFM>J6Hj?5=[ZdW9eOfV6qIuo<nFE"88+ER9:L]S
%oqp"GSAFAT*E]%MT+,%O4LqBu'*NO,.^gBA,%5OHA;HHM-TZj*?PECOBO2"FGRWDR%usDCp4::,j;<Y%:-"qP;grV9e@>tWMXN1a
%WSaP+"&G]Z`/tU*#1KCA"'>b9"9F-\YC_"7#oH7+cC\mXB9Bte4?@VM*W.mC0SX8qTjJ!+VD8X;ZqV9u(Y<B$C1K&,Q'8U.flBKT
%OK,T0ZS>>O8]f0eXbnM7/aYV6b^Cb!)j_hASdS-n7bG\5Xj\(9\77Ff5"N9.<5NtE!"YRSk'YOdTQbLg7VJH_3)8ILW+ID<2a+/3
%QFks>b-B?U4a3T_QqGAP_Pr>m1+UXe?__-Y3#&=H/f,k?L,9r&W)gmlTV5e":!!de$7FA!jdu0FP#O![C]]EdJ2iVBF"LeaI!/Y2
%)@WmD,=2rcA.;"!VTbAi.`>27@tUIs=SarBTM/S&P5/U#7<8IG.835ukY,t6Q5h8D2b.f9`:C=2_V&"MC6P^Z')JWpSihUWQ!$DQ
%H_WVD&7eI+XRku?Mf.7qo'=:@l6Rq$eqmtcG$ae+!CZd@^:VgGCRoC:,M#b<>De^:,XGsC7pU,kCB:,E[hi%Q5]KY1;@p*X[!sW3
%X.guc(>L/)oQl@GZ8TU&;A2h6kQuB]"tr;T;C>&$)NpLr()7]U#HDc'M#!cB97jb5/jmgBl3RQ.F)GS4cgHqbLBSGIWK-.r-/#Iu
%X^a.b2m,cU*<^+7EU4LBpCl#Dd%(UN<"2"6#UG-/nn62k[&?`,Ni^4Ld7e8+kp$iJ;bi7ic+]&&&`EsBWmGN/7iMQ``@"FS<)'89
%I7'la/.P9Y<@=kR'r'BsX,ttQW4<Y0T]CGH+:/E"haq6e+\OKn"M&CRe&^n@0WMT1$^.#@OH#mb%:'^o/"T^9+b`i:i-K-\2C,7(
%d%JIEqibdSjVh'M?%m^C&ePdujHpq=TiVcq1:Qkrn5FIdW0Roep,o018*Cj_3MW`+0G>A,BYNBgYG;/'I/mJt"aG.,$5A4qn[JqD
%[b4>9+mVnjXqgKWk-V,XER7$>9b!jB+UNt3?[%B/28*B'7:^pl"a1e+^%sm'>%Rc7d*Y=Y")S1rLqKM[.P"0D%eMOEjMri[e,ItB
%I$DiFa;rQESij]3If9'2m.7Q](Pa@sO"[r%4Kmi2oY(&Q-,cp?K?nN1/-c:eXF$!>/?Xp!qfZ6@pu]]Op#2+hb9r&UcYgLBG51^#
%Fq=':oZ)<.-@^:/WI0*nAU[>=OI.8VUE`^2!^i4Ala0\/V@!'k,Ftg0*e0WM^,D$%EMa?29i/s58&[d4T-e(c?k/(%BI2R3O*ShG
%o>_*8s6oreWm$)$NTNc2q<;XA>'Ju,n`9CB>1@Uc_R(*cUC\40-Ag"fgk"(2L\uZ8a7,h7h"RR%8>Y*WA+)DMIQY1>1t<o\K@+]N
%<F6Au)HceTlR-ctmDZI>UGg3_eSA[`3;%__\c"A:-@!ql8>L`uSk1OATrPVr30COR=I3UKAXuq%?0i#uhMrtmT;K1FOW<*V5u8am
%$E6FY1O+?&\oGdein?f$NLo"?2YN0kpZn$t:K),]>FHf=&KNfH;"@*h(,*3`7_)('YVp4FVG;2W]]7N=CHhSOf4M_"eHJ+p,ceEP
%&VWg!a\>C9lPJ,!O4ogXf;@]@2V[tmGk>^MbpoDY=rs@lKtFes6t?T[">-U7KY=TJA^i&^\MbbZ[Fr8YLSIXDD:W"IiSEg*96<_!
%aS/$HjUel_?F%<2W(h;RD/u"ZZ[I=D8"Z[4q";M^n89M3'GFSR(9>?p>-q.bV9ZJ>DQ^tP]h:Nb1hsku5cA]D[[?_=P?d9S#P".;
%R)s3-ru?K3L?66FJ0m[@DFZlrNDEjoJb."NVi^f!5Q:@(_#K<%iUH"PI5f]&7\AT,l>Z;t0rHq>(3l]i'eM3GEV6O=0cO%;3b'QB
%Qkq7r<AWhGNX0FY\JGec.a4".e7(8Mrd3d:gHUX!DAHt1Z3X;K[2K4Y@KeF$C7nA^Nk\#;?bl_PZJQf=M*O^PG\gPG.!YVb,Da3f
%1WG5Y<m]HtJ.B#uk9`==?b!)U'Z"1R;M,"o=TAGK.;1@X(P;<md>+8R">FMj9]-c#<5R&#9O`\,/^?h=<Z*aGa/K31[t$!R5r/Sd
%B"BJpX*-F<XeQ$So[2j,Nus"[!3/s!o]7taUJNH6&iAdMVA<.4%hH&r'!-:!_8./@heTg%rNjT-_KYNn/Da1ACL_A7m01k4fIVFd
%6,jA-Dq18)J6oYF]nCt"e6eQ\q$j!//<q\=&#sQS@n*f:[GW25HQ-kJ"=4MDG!`H8!UM,ZLq@rb1&?Zun#Nd?_a8RPBpNNQG]YhY
%.RJ)VYGG$KKLmehfO`*&5^jJ*cLl;j-YS*H:<1;t#,XFUKa`,2i66"hfae]?oV3?59sl'[Fq5HpcgG4Saqja_f_W#4*G!;8`jG0-
%UE`('jP!/^[Dg(=0ad,hW8;8#a$?HIH4?SXV(Ads#5GZJ.&65M6dubHfP$uF,q,;6UE>tbKP?c`Gd8'".<K-O71VX;jEXTX41!it
%fUBUJcpLsa7^-mM>KOZ8V*-3ek2Bi<l&,1oqE5mBK=<,XQjhNr"S8,kQl&o8"_"4j,q+fP0cmFG7<drlS*s@5ICj/k)e^qEiM<W*
%TWPUR3k*(cm<]MNBiQd*pT1-hR,Br9i.J'8WLFY<GnP@@QbGhR?EAIZ*=E2:?&R]-Q4l]MDD;j;;b0#Zaq3f+q_)_qQI(#L7A2@%
%i7#8%9Z#-LPE2nI6MkrVH;,$@k?5tbkqWZCG/G^(9Ar4Jl8Un4JWHS<JtYHFX:["S,o9s&o56&t1@a!U2$=$q9Yj?:/0!NS;_;k'
%Hemm9Yq(L!-%+NX>;K#uX]q@]VO>ENOM1t"X/!&3Zm.-0Bb)5'kT[i0V3$5Q2FVB@)r`jCgbKEu!A@^g@hNk!R*D!G6_;-!"Y6FX
%ihL:6BF#DK=Jk%6B&4O8-_$-34&Rl]GJTQ<FTA=<*V]&W3,!1DjIn,45@u9Z1J7r?aB8Pj=02O6":-W7M0m]"!9Qc/9D*_cXO,>j
%"Ar6&"ii`B'0>9*>j>/uFSjt/Mr\!AR'3jKW1L?f7+"rVghC%ul4LPnRL.<KI[[`PXDR=J_+%K?BOb[E!:OeJD-_(7S?<_M**pH&
%qe4ib+QY(e3j8Bo@P>Z61uM+;%7b1;@@7*4F$II(9f$$<`\CJFNZIM"gsSHF8C6&c$Qm^rp(H<Z]72g8UI'Sk9kpn+2$gH-eWu6(
%Cfe&gdDXd$@0lDF[_lsS_)ctna.=6i.=>:hD?/3,VaDKF'uSV-gW2NlbTT.*rNs9j,KLW/S'+(;q^hGU\D5;P:[3P,[GfYEG-d:C
%`_pZ>S/LehX4E)UpZpC+Md\)G/B.-;`d."H'1q93K'b^,"Dk%r\DO\T[dBRcB,dBg=1moIl.$'Khbf(j523U%WpPDtG0oV5hQC%c
%"JWpmLPbp%B8/)C\T/$)Q,;ZK)AsHjPKPJ1_#B_mZmTdP`%aO+fsq-]F*<2jRI&J_FE;\Rbo!iQl+ME2D8ZKOVYcANi0B1108,;W
%\J7HLiNr<S6<(WRb<2At'h8=g"?Y;C-;8]"*k#OEmr9*>hV$"m+3Xe/C4S2DP1Nd:`NY.i4H>%/DjI2)QpoRYQ<%T7R"WuV-[G9*
%9%)V">-CZP8OkXX*0;S*/kAprmC7`VOqZ3_T`B_sL4Gcb>3"9^Y\+sDR9Up;jqHB:WZ='H$ML;G"k'gd\8"h)2QJJTE>546>X1'Q
%Tbi+&ZP)CH&)+mOO6%!4cR['j`qa`um'T>`o*d\7[_o.0B%,`WoUDsj_K.B'QeM>l_dDM=I(Z<cgC0s(MPe$*G-<Id*S^u6*PORd
%O`G@+b7&2!%ntqM*B="PNc@eOTt"uU&C:IOh2h3=!gPld[Li@f[k7c%?`g!)9)0=Z*:"8UC)oC8"jF]QI`h(%@$jnCRND`*]>HVk
%XM,+4ejBh:<X,-;G8fu-3TL]8JMTgF[d\u*>QFYp!Uh#1niX#cTr@AE#6FT8ZmY^d21_67S++hGUp&[8##mo:L(<'PgJOi8\!mh_
%MAb>o']kNWc'sn2k/NM_CS+STj/,L0J_*kEFYO?^<+fjX[_(\$(\kEl*4%>9HYik&04P)+>GF572To0qF]c!\;lD@L>PE2JDHNMo
%ElIUmn^QpiKQ^9j<5g_d_IM=rHQu#9eO%CD0Jhl",^lZ=]OcEthFu-&\EUC-+'mYpS]5-g?or>&\p]O&An3t>e@Bc^0B]Lkc:a<T
%A'5R_isC+i@J=F./1@oh`H$cMI&"YeY-FRaFE(Iu)8V4\ZG9&.b$B:USj`>j@`f6N1kjL3VJJK!:VK<_l!%3S[HShj*?0usn4>j7
%iB/Nm)2c,hE(?!f^T$?AfoqVnfTZ0(GW2qUZP?U,L,08d2K0Z)TYTDKm!<gOFcL'r?ELJh<lbMZ6g1g;$nDT>DI(@<JWmcl`a]nj
%Gf?!*#Ebe_&j/#hNQlbSgl07Efs+)l2"(5Sf]HM1E&/DZbJbgd&95c8KUA#_S\Mf<+[FnC`nn3H"<=lhRNFUN%:j6E%qci9DDV!d
%2F_I0S$C<Y"WcN=1D[3/ICmbV>Fp`k2`>1')62]#);@"dT\5:;E`-DcibV\q'K6/urKc47n8q$:k@cVI9BZdGgP;+tCkA&-3!f8e
%G3!Ht%MiS^j"RX=+B)Qr^d(m;"'d`riu.;tj)A?AjjL9N_9u$.9fPA;$RnkBF3SG>/;8h/AdAlG%h3Jk6e_NB:JM4J_]2G68:Y+n
%e1Oq2DD]om$*De,k5qJp!@OX^(FZ+sY&=>edh4>C,9<NV!2`mEjb603ZPa_L5C1`MFiJ+Q%Sgdhf;",#<C!g+W#Du7m:8$HI>0;j
%B'ecY9lN+D9fD#Lml"\%88*USRpbpgLN08^c*>s>Y)B&cf<*9!gp^OcN>$9:)1*!3!-dIDTe&P4%OVHK/eJM;ZR?W,`FLc:$mqA1
%bR>JMQ,(2?.,d+jH^LmB.+U505s6jor?-L8`Hp0q`^.^?"me_=)4Z]+"-;8OCb4(<l=Mj_'P_csBN$-b[[j7%==iGNTG`@E]kM7>
%#$]%>@BWYC!67)u90\[hX0Ga@%A)RUYKPRV?2=_O-SPt0Z,e:B;?iW`>MCLtA(X+,[S9J5./:#`.KKp'V+]")4<V.rXMKRm,!`VS
%\e%T?:lV./MFGt"%^H3Da\7*oL0>2"=*$PTU`]=XoLk'AUm5?<qXb?3UcuBH$L#O.$2kfZ-l"\FWb51@l"?7Lmk9TiE-6&c[o:<]
%`3gHr:@+E[M-8uKhS<HJC+Kc84ejHCQW^GMSm9tuS3A8?=-/Oe3'3;R7,pSBLJR;RFKuRZhHiIEiY!s.@r+i&%;s0[JB`88dRRF(
%)[.54Lhb`@^t!OX+Y'q6[9eC+2p9LXl,Ha`Rl<VgZmhb1$dtPO-=H;^Xa(R:<lBcA;b=gPi,7Eli7>V&iE`i/S2P4^Jbj,0Ygo*:
%INV>X9gA$WrIV,H9*Y+HD'UI`:7X;K`Q!n>c;??i4QL.1"D2PerbVj7Gc'e_FH`Df,biu-l&<e^E*nCp6;.T,5=\HDB&M_dB"^#a
%S=H^@WE;kPL,pka*P,W;eX,^1Cg"34V.)$G9q75P>UR*W.FjHi=m;lGnKEdL0f3Tofr3?2YVDP*SOIs:)@[5;539D5Es$=Bk.mmO
%9J<L'Ph3"L8*_BU3c2Krb#f`qg!Nr;^N;==rFkSYUsdG&igc=pY]5DZX:?0g-gjE-M8cjKBQ5MKChr&NE0lCF$TdaA9o2F7)f$Bt
%_96ZTo\&\7Y1LU^]9On(*`?_?VN*Up\F&5uGX&FA\;&"!-^0(Vhjoc]N(,upmf`+Mafod=#fYK:3ItBbhS=oHf_6&'Z>hAajn?b3
%4BaZ58R5'@m3G1m0=7E7fdpB6TQ0..`QWCm6H!6N<!9O7(IR5D"=;<]]p5T_*5.]R9'A<uNN:T)\=7Sa0P>"><T(ZP'T.2G1/;Oh
%&Cj@.OCTtML$i)V=6ck,)c?m-#kP,-gO<=uI%<=1Dca'mUpCFlql&Dnf3O1;-$Gau%5`?CR#T`b<^C[[GF)9o<-rC!#6tP)@7Mtg
%;?#or8qZ#0JCBLC8M7p+C`cpl7tiKnj5Y:"Vkt;:pNErIG5*aEbpE(d(lNV?2_0?oa7_d-4+M`$=c,DPh/\bQ6L_6E7<74M0^iNj
%qJP5e;$[gt3euu&U%d::*//bI?Z60&=L#i443,pdGagt1F.hddk;/kOp#gG,$uC8#FhBdHqYN)m(-d0O4]OED2[?&I\dCVrc%R>0
%!flkp+lS<0A#<kd4o2i6'(+`667Ss3[]e.B)lVN2QqE?=nZtRtC!kSmmWpb+SL`.(]>7^N#m;1ODbU7anX1uc@%#j^VQK:2W##Z_
%)`X'?4<9JpNb[*tk$m?[V[PQ(mp$-&[Ah_'lu:jEW`)0H=G(q,".UY'5`2`o'e[=cK7V=T1!HcL_-V^`HH?'&eX"3SCs^c/NJ&=N
%Q/>X[MFhYg[\f2-XS9WZaO6&Wf5It2\.egWR6aX,0(5-Ed)=ZYmPn1k\_,])<FL3e_*h0=4%p$qbLdafCp_)#mrLLNIqCe.V%--]
%?6[5\3p-)LN9PFjH,1oqGKVq,<qC##L1s(MKhO";]FKn:7pAX5A4YL2\lMW6abq6-p'bj9B>UtaOW)L-/Me6Cl#(h_DQ7H(Dp,Vj
%&%<d%L\#]b9%&JnPGdoQYrb.$Z'?BjeD/8<doqg(Y.>]a;E9p\jqCOh0C?]a_f6+4FGatOl,_]=G!6.PGaV@u^pF,4a*o]dY?==#
%l86#5-A!O_hh`UgNG_1JT[@,Y6Ib-U90e&%qFBOC0S8=f[L2c(mh3_re@<#YlCOQ"pV@.^Wt4f,kW2bX=O#=:OYqA>nXs^_SDG2:
%>:[-2:0P*WpT:-#S$7m<D;]"[gluRO_4)pid<m@aH@:pMaBE1[lDu3$DEcXLHVTtNSo.AB3@]\MY.N;F`O`1@&sQ*"&SI?BI8>F%
%"5Lj-%(q@*N)HR`NIJlb@m$":IbmP!Qi/l'D@GpeD8\8(]7"4ATsO0+ZfQ`%5-FD!fm\lY)eCupmqnfZ"B9?FV2a"_.9[L6GXMVu
%AQtJC&4@m"c&R2c?@-pIl>O*I"k>1Eh't7d`qB`@'Dclbn<u"P^V*=s[_9R/,Pj,iCZSg0"EH.raLKtP'p?H%DND^qgKhisYgK'+
%N`HfQFk[*B;j.gUS'eGWg\oJZe$.0YgspoGN,Y!!$#j(Ya-4Z.%o]>PZ5X4kn91s/lL]NLDXl/t;,,O?^9)IL:?8@)_9T0^1ICfs
%W7rY<e&N+c[L*[$".>4P$N8a`2OaUVb0d"%/XrVuVhaZee5S6XFq0D/-Z>Ru-CqTWUVhaAcJQ@0X0i@9G12RM>]1[cMo.k:c-.b!
%H>SS$"/qQ$A$N#L1iAfJg*":d[GS!!lN5aO@W"5>4/WiDjcEuVL/[kjQe`_<ZgXB&ml4/M3Q]Zn5e[jc:5K4G1Ncl]`K>PhQhBg,
%)D09O,XWr5:MJr5fmV#HPpGHq<pOn?^1DN:oe!chY&E!aE.,pt[-Lq>a0'@6ST'!5E:<Y3HeA#VmTn_O>'nl1lqY()ATu#a/\BK7
%DqQ/C^1DNTqNf);2g^O`lW]tU;S"ekFsgPPZ9.IMQJDl!\*T,\hm2ZoHe5an::S#LGJa#)]@3p$gP<*E_m/N3<qbmnlW\N[>i2hK
%iVJkrC"Ki\::S#LGJa#)]@3p$fq!)1bO1&LRt5q<\*_)uqNfqDf=1*A]=qj#_Y#Lm'<Z8QST]ESmF_(`[:@gte5!WiV09`K/[Z7p
%DeW"76?t2k'<Z8QST'!M>J_(qI7rs\lW\*O>i/F@mJ<.&$.lZ9::S$7G1-%8[1f]]WL\!V9C9+Pg\,&uI?S-&[]`H;ldS\@^86"i
%oe!R]f=07)]=t+b_Y#Lg'<Z7fcP*%UDlK:HI?PXXAHW[ooe!T#Y&C;1E.(CHK^2iWVd81X45p%%l3Z(O<):-?D')fVK@EZ+^?tcj
%]=t+b_Y#Lg'<Z8QST]G)mb%1a]+_>cfq!;7bO1&L>BVB!h[J;P^<I14f=1*A\@uNug@[&0'<Z9,ST'!5J`2jZ)<EMAIL><jm8
%GfO!UmHue&-ScEtZn_^N[5*?)d`TY\d"1#;HE<ADn<?sCn0>%D-MUmtA)LZDA,'@\ZRnSQ1>CE[<p$>uW]umi1))<=P;hDkXj$"P
%9rUWdRGID[[5-3E%^!rugb%,g4/nK"q!&.)o\4(k+J54OarKH<9*[sj)$,f*1'4&qap<HlQ-&HI<^sl'=rG/;Zng=*d)omC=t9]#
%GH[:J!o@^($p6c>[Zam_A"3eg>^"`AA9U?jK.<M?b%,]_.taGGCLq\F[_\$31@o1$Xit\H\lNDakH:)<B11@o(qA,=lmA!3APtUl
%PrX.q[5,dIC#u9?.[t;@XBM$e#&61qG=!gdmC!<`V3SlIm'SSc]"M.aY(qA+b]ZmCQ5saPNi&[gA[ZZ!-m"E<@b6EGXisb&TbB_W
%O0p`VhHh]gc-.ba8h2]?nQb@a<b=F2S*D&1AZ"]GZngBm\`oWJ=rFq*SA7p7AIRF/AK2?\@=OH@K5_#1OfegiKcTt6LdtCtr"0^4
%4gT;YbtMY8eU^M9jrW9*9!=L&m9<WW[5g6MrfOe>cA5qlWQ4=$c\NdX-l0G^>>qj63,*#WmG<Hhb:M,G0jRD\Zs*SF"\/T8inZf"
%Sis?%m7a_lLfJ4]]o9fs'>h8h-ETVdc>:9f4#>nON<5h`9CDu2QO#KbBDO2_E7<)<kUe_eSP&*RRsT.l9fpf[9F9A?fHcFY[dfka
%:!SW?m7bjm,2b/<->V57S<<=Nl0)8E&rc`Hc4g`QP:!E&Znl.OaQ^j85_U/ngmq%gZYSAFB7e*M8=.@\OD8nSd$->;)2C4[PB%ue
%)2$kTFK^?]3aLXK=N1^N[01X@h*4RO35/-`Y!l-%WRmr8=d2(6]q[WI`L&odF%nSL^p;A4b]qaa/4RR_Jp&)>1fRaY$;J+"L$<c2
%GMS4(MA%gpb@2VXZ0XS'1i)j`E$.q1Yb?-dj$LOQ2^==b=_lcW1I6;%=g,g,6D:TiJ<P"#f]>pJkP>\(`/ju:\."S]"IYP?XPuj<
%-b*r)`0VZ^Mn?hpQ:\1l33B0GNZM4K"Y&$01ZG*)qG9j\1-.kH:sOlX6I>ujb6_u_cHLN7c:FWe%]5^%Q=V&r0^C!Yk-?I(2\YWF
%PCD5S.GZ3`>+4_pR=i[ZbQLZ'#B8l5RD_h(O>lJ[CBM.S-7E5gjLjcXQC]#L^cn.gFgKhXA'@"$\mAu'Q0,4[,jh>NYca<qR%k7$
%f?;CrXHsfLm<TYn'np4:H5XuCV<<Z()Ss(<32'#!Z^<^'TW),WD/%SE8:&`gG7TUM<n30hNt[8lN]^(m[%LAUMY"IP-0]c]9,sH#
%7T;@ubP7ZH]Bie"e^5_pS]eChZ)KNr0Neq>.p_u8U35GGA_L"T(cQQ;Ua]rGqL,$53.p8:"`DnOO#neemVO,82iY>@>:#+jla@t6
%MHr%WFK=HcS<uhoE"`C/iIc,-Vf<Dnk&.2sj.:6J=$(et8s1b9G!o&hWu@!3pQV_tk4=mY[?/$K*cY_F^;Fl.e`Fd[!GAiXHd_"d
%(Ztm7>gI3:(J[RHeHWs[Eco7-)M3uFPcNlXMAsQ8B%R9sTj5jV(GJ1F9MXFUVODV5B5_l@cOWh13-I&Q?IPK#OE!YFk^,7+]r!Yh
%0Q(PP223i'#V-.iopVKAmPV-@l<%Z%BoNCUA`kr<qk5]h9tP-2*L\7"o>\)bbUHcDdG/HEoX@TEj<94>ZuMNEo]hAShUTthqP7OX
%(_Y5^G?*+K`XP?d1:A^cm>95u)4^%Hek,JN`J/jlQ<`RR_p#bS"M:YMC9`d<l;^hh`BEm/mF(7-hirdF*YDt\E"X4TfGHr=of(*q
%[Ti-u=.*ZjdqDkVqj^.Zg_%&aS-,BqZ<B>uF'^(.Ntt8uhTG5#b%$!]Ff.-Sh;Sn<WKuGDh-`6_k;'oe*/D_EVQJY@.m\4iDkr]e
%1EXg,1-NFTbOgI3R912No6qRE4nWK0PruIjG4XZJm?_D!FsV6<1J<$#Ni)!qlj9-(I=H^!l.k.S`K'B.C"doP&!pT9Af'>HH,3C5
%:<2B3UcLS3:Z0eSGi6JRg*a?ob6+U#UOupRk"Qs8:=G5kEG'P`b`5&;Z<=fN7ecPUarW5Q,0[OQ,a^#sbkc`(@`I'QX`1,g\r@ED
%Ho:fX3>1kBI&:\KDBctZ-OIKNatV'f!F=?K)HBJVI?JHKF.-`bRaV<bRs@@]3aiungqY<'gaI<NcAa;I(9gM]c!6!hfh.[2#rEsq
%DBJ,?]\Q'*Xa+,B6=X8uf*Ri3GhQ?aI'%,;&-6"mT8%K+J6Nn*_Rhof8t?)<hkduFOQL$hhM+Fr'I-i0ch3K<b>"Xl.GK8]K5^-u
%C7iPsC870i[O-1-T&OlNJnTN32P$\N"#9ooS'GgQZ(o"X<fj"jS?*hH\?UWZGjU^t8tNW-/4?cs*o/B7ojN%R61Wn"hVa>'oB=a]
%qJC$8BO+uT_h0E)-8PoVWTT6GVLiXYVTmhqZ0c_+@;eD4F?@PHDX8JZ5*gLhG"%i.+giCE)`pT-rALJ3/1mV5__`TGS<*9K)N-Nq
%9e)7>E,&/A3S>.U;m#)K62h]SVI#lE_E;YH.OWcmmG!t:5AWhs0LO6jf-YIc`=kEn8>W<(aOOV`\9JMdS_u[Oc\^&VC%`t"k&tK3
%^G.M9BF8JsiXnA]*M_tM=H>L&hb=?>Gu"9`HK3L;=3W[6SsC=7lE^q;*B]TJNPqd3G1cYe)C%A$6";MiQi);1DE;AkTn_>c)<WpQ
%G)tINgDf0X#i[0^2qoGV33M9ZM/4KE&JC3YX([&K1jtM=qVp'_(M@LS@HYM*"U(X-jm*b?YEaD340%.QF/7A]In:T_-Z<#k_l*Ad
%"AZiL7%t3Bj5S&f8[-IQ9dr,h1uI%MM7*O,pU1C`.MY!.CERB?@]+QWE7VPd]a[sh4qYH'B#RN=l9;'O*"jn>S;'h5l964';O;Lt
%2@X"`e`J9+Au>Vm6LJWolAj6hNbpjj^nW,ER8]GR$I20%;]"n``fcaDGGD<o'*aK2i?e#-l==]#8N?1/Sue/$S/qlhCSaU%+J?h[
%?IR^2giB`"Q<:TNm-M2521*A%Y##5'C"L7jN+CZdc=A2iHJ%.)Mh]NH]X`XY,g3fHS5%/=]>rS5h@sF?4jWj^>8VX0OgA&PkDPk4
%[u'R#")e(U#!P&4d2n)bn*Xh>VB$)km&LoC=0`ji^p*"hW2gbMi6[[5CYa1!N4f^HVM5I'n&;p%S]*E-j7LJY_V;BW>pVr>R-Un8
%c6sOVp=[.4;(t@-op>hOV&d.SjC</Xrt@T;;k3.e_[k3bla(Bm9Nb!A9c1-VoEd;k5C>VcSsIfF5<UlW@:+bP.P+d:G.L62<fYs?
%X=sH+F"1QkbAi9F=!YPQRf,!p*Y7;jjhpW<[*5,RYtViPVbu(0T\\+>k*:BP23fP!SXOL?(q-NBj-%7&#"*V&>.Uu:ejVOR\\jeX
%#cR'3;^KWpc9@))('BT:1@;I3']/$E5e>A_3:?%YFjXSa3681P4Hhff+]DY9oK%P?PMLA/:3K]5ka7Ri+ku/YW--R40UZ73lY&Jt
%SV8leU]>B]D0`uGcdQo6L>)%a[b*&#89bBDc09#$*MIB?#ATnE6lah#_OM(*gZ1jam[MX?2<VSE-8mH$j(dmRAAA6e>?nUoV;+F'
%>d0E?$F"UDB>rAAZi%;"g]=]X"f]HVYkI-8lIUkA]sQhe>nRqJG\VT_?T#PR_1"EBq%+&YMX6;7=mL&uM&Dr:A[T8EOT6HenCE5@
%b.=s/g3d1:I%6fM0uP,#Z+9_q0gU[Mo4+WBFjX![0'"7^\uamigXLpacZS1k]=P6!\Lp:YF`E34^IK=/[3J+>=aSRYG,6amQMWg:
%4sb2)8=aW2@S'M7/FlT-%n4G\Id<QZr0j7ac9M8lplit+ebpugccO\-`j`&4ppJZL+mru@r3C$-ihnu/.(s^5L:Ed0%S>:KYPH;b
%hJ)b+rCn6ed$I2,"S)>*g&8KUh4UC+iLJ;FXkRaSTAB46D`/R&malpUL@8ZD"hd5%0Jd%QSc>gaAO!LQrRH(\0qGCp:H%NU3smub
%7\T(u?4/3<"LUSg;kI'S9uic/[o,0IjSESYk@?m:b5?%&edf&_2QPtIbH00uF\^5#k-qGTppJXVkG0.%UESCKd*bQO'&;nO=j#mY
%S!gL0Y4Fc]T/UPQTBD=nD#<P.Cq=&CB>XT4S45F_l\7IH,HKMm[FP.9hlV?p);k7-oli.6^>WrelM"hqhR92@dq0<f]+n,9ip&(.
%!f=sqBQ4'6l/$%pGGj\M$>8L#P('Ro=%'\c5#mN%Rs4,;h<?s`Kj%7uc0a5TZFe,RJSb%*P?Z(6T3L&5k4b\aaiC=@HFr$W?(.6m
%kMH,9fp,N[?)NL35iqCt.D;tM3^<YEf4]KQmn30JY5?LU[NB,:f:R&d6-f@G0RLR"/9n1E:6(k%NL][\.G8Yme<"o+"nYsB]mH@J
%ds4,AQ+&(0F.IfGk8?@J@^B7q29l*17cUHoDG-GQ[?2)FNBQ\qHXiHb'4eT,npK2W[[C^URG2AKF^:Ft_=-P;:XZeL\/]YI1=*8:
%'Uss1&3kAEh9T0'odFNlf5$O0J!b4_J&]/+3VC/'m/9tjm&BtE3HBR-EnfFD2a@.oJ,9,Un(lj6IsE[841kI^<EMAIL>
%H1.)Kq<725mc*s:Vq5RR?![C+q;pXpB0STXZTON`n,DnSDY)@_rD1.THD+.2j!T?QQ_udOGOmVm:l-7EWT_#oG:Ch,G<`f$]DD$`
%me63)o'E6Lp?:f4r8Q-"g"#3W++J`dq=n;M^=4q<7K2aGs3M8Er:ocnIp5.=5BFU>l1O(,?U";PjpFA:cbKM1c1OT9Qe-Fs&bk]B
%ra9Tep&*'qro$pSnQk7#+'h$:s8U]CrR]$ah2hQoSbF2_I!0LSE;TRdkD'SoY0[)lEI7KRmGj,Z++Mh-#CcR>m-<m,O1/at5JK>4
%7mC.siS?j\4MA@Vq":9)f"[7frUJj<PVN1\^%;OW*b?3R]/mS$ar`20_8eL\Y9.\TAJlkE;-rmQj(GbO5/$egf)L5KrqZ1DbHQcg
%kNb81p%:@W52Z;3iTFiT?QAJS*hD1pI^W!jH^\N9rlrclDJme'%Zg$1T%N'a't4^[%/f2bMt7lj'n9Db^l=<Lg<f^XQ.G3/SOhXW
%;L[42q,HBcZL5=uY]YL=IWKpcp>k6'@<qTTs%U).]mIOik-<SAH?o+s8[YEmg"2#jY.ltb2_$1tq77=<Y5A"T][6p(bo(<JY:ffj
%5<S^bG<hOAIbj<+b7Tb!3?n_&>;uH`M]#6ZeioS9,4`MA7Z)]baaMohoTC,Om?[2Oo&!G4MuDK2[Cpq?2h"p0CJCH#qc[=OSsNB+
%mnUHG)ufU.h`SJThX'd`HT<)^p:5ZI0/*2d?R;`@Xj?a>QT`-Q\3l(AWeB!3=rY4\\(=,o?X@$>pOmbjp%mgsfD$V3iB+fe\pFA:
%o(q_@ipcCQ='%>1?;J[eJ$n^K,&FsihRRH&rTSMJhE6'54o2M.X*%o=!UIjIH%"TqKR_5+6(du9dm&2$GBWu)=8_F$fsAlnq,KB*
%O.Te9^$,8l0Kd#FdFmpCMr+7Z<SbF]j[5"jA,?!_o7M9_*kQoFq/l"(Np-?/L%XmrT.W<T\jZ;WOr""/rB%1^cAm.0`,]!FVtJqr
%rN*b:j_0+Y"7$bKrO^#@<M*OQ?/Z4n[Cc;.mA%;JRkHb"o?CY`l^PMIj\U99SuMb&h(B3+Y.n/<e)?kl0:tD@3r>'^+c!i0o6"es
%I.<!"Ls8`0Wl*\akOme@%e"@B8<HWM"D\n,J%XbU@LN/.m.M(D&:Bi2]D!u1rkrL@(VH'T%mI_;_I<>`c1-#H?Tc$3If/Qaf@q*M
%a1/j.`XC2+lo/2N#PF@%3B%qnhBe"gP#2-#51JLHB2ZO2h0M:Q_E^*/8pllulQ:TMT"Ic.kell'PY_<lrTh3.:)uP/+!1FA5Mb;g
%O4LL!Q:plHn&I2&CNog%@@cNK9XT14mC-A4%C_oiC-0/s5CPoMfWVbqs2tF!lEFQCqfVPhC+\s+f%s%Ik.<$M%"rj5T'/PFShliW
%S9f(;o&@g,]Ht.P?J:XI^$K&^c^IWbLR*Ih<<Rr1pD-03aE\PiY\o=s)lAsHoZ>U0/bA?$=F[5QmB?QZLM%EI,5Qb;]Y3l/q<hg@
%C61S6YC!)3R8N]UClP`-('WK:j-@khr`s'P?2lO/HeVpa5q+*iGFgbHQOdr[eb*;$G>HVCpEZ'RH!=9-Is?,A4<!eOlLN'K=B[p/
%;/be5(&h6(Pcf==#Ce^Q^4R(h[TM4%^P<Kc45*JA<ILqA9*&O+rVAsHk[Xq6!jGFn#G9*fGfR0Ll[!o*H*Ij-aK3Y"VO,*$[!;(1
%q=T@FRni<MZ2$[kp2?O.r:Ec$f=ue4phr5Kaq++')pA]EV&k#5HB'QEa4!67..k3\F!SG>++:'Hl5MP5-V)hhfQ:!Jo-WrF\n)W.
%"1)5-&0g,=lQRK'\.u1!k#Nm&g^H7*'%a*+1#(TYA&"Miq4=d-YAk_e^\R,$0$^(^Ab[R/37`G)+=d.d34okAmrr]QcKkA<D17Ii
%pC6SIhb'70RMYG%C&MgOFhFMtEdN!.ljl%a:QFm`rMu8kKu8#]e[l^eCRKFQ]:78"F-s/SYC=U\%DV"fqY.*3s5`<<(VfqI7sE:f
%nEnKVg"jX95@.-<[4U,S>[-stk/l^lfs0[UE;f^V_jdQjlE29+F"Q\1q:V%Gio9ZZ4]q&0S*tfLG(+XVHX*g_oSWHM.t2]:S6qd;
%7pGQ``?0"OZuV(*q<@#67lQc8Vd5:3Z)M_]4ML^No(lhI.<d]4C)MEm\(&4,$/7ElOGATM\(;G*cfE>Hmp<n<IX5jKhL3%#`NfQS
%R@Dt.(Oru?WNo,tWMl-BXH,t@(DYD-q`n:jI<kZD;:i5Lr+4@>LNDd:F6a'FEOX1TqY]^Mc(BKI'&Vp'p'.Qf2VQfuj\>1I8(mdK
%2bsrBahR(+(<3'@[7!MV_.C':Ve>2"mIgYJn_*:G)\MSGX6[eQ@.BB*Df5Td7K.CKHaf:(St94cABX\jB33Dt2[,g<]B.43h<hs'
%*S\%Bn7Uoc\$*:`s#8`'5Il18m('X^4dG1-YXSSr7=Q!4SWI07MY@#^U5'Mfq<!eS/O]*>Mu#FpGjA!`[$G!G_`%+I*kh=Cm-4B2
%pJ8%]s8JeZ]+Yt<Fo#FJgZmh:LX30l0"ID-Y7H*U63D2SRthq`nDkp+E"POUX7,hA1]-'"[hjO?s7ToGqu.g7ThEt7*hV8Z4YfaI
%]+6Nrf_6FMr8fSI&tdmuGjR`oj8,DJOoPCBdpN+IiT3k$GN@d[bAY/*H_6qb]mor%p]&#.8,<mqrp?l7jThiKXJb&M&9W5=.=(ct
%3+:]cP(6f9_n%pfS?MLU+-t]O^Z=6s,F;[4@EEq=e(pq@q+o0`hj8KNQ*Pd@;I'78rk2'&qub.bed'bjZ<R>\s/Z_W`uiGk3?4?u
%;>5FCq#.Ep:TW'lhUn)3^Q4ZV^&+mso_P$LaqprT=Ri_?G5p`Q>C(DejQY4)/\Ou#naX*%%DU7-m:8/#42*p_pWpI<2s\rPAO"?'
%*gCf"8pUA*p7oM'#b0B@mulT!`Zr"RVq7!-d#P;i&c(YWlJh7"Jo1DKZgT0XHiM'Gro'Vn.Wn@L?VCA^O3Yijf9G5+T7)I#]Pmqq
%X'AqkNWjoWT?H2aLHd6enDR0o5Q>$gmpCQBS\FY&3D<4rX7&1i[!2N:o?4b(;6=c7a'%$RHM-a']k[#pV]YrW*Iba%oAD"0[eF&u
%SoX9Lm396P(E-?L);];^jX+9c;NQ]RH!dL%9(tmRQaQY;qJFi#^TeUN[;j"mgU3Q7A&3r.>%*8'`MC`h2J)9jZ*K[W6H3#N$'Meo
%=FQedMfHgr:b_/a]r1P7lp\dPE-#1*'[GL+s2;)]qVW`:%r^=6s3BI<6`TX!:DK:BV#2OS2*RkRJ;Zic!8$nVrWdpDG[un[2JL/l
%jW==FL!9ua?hF&g-J[0=QlA.lZU33[>GkNGNDs[qnAj6XLPM(<ihM3!#21^ce8<s6]<D&f=F<PuVLYaSlP`pFW5'7rFj.bikb$;W
%;RdULD_Xujr>*snA(_=Mk,[@699aNk8EVUh$slOd8r?]<`:=8BHX.%bQYYJ*2@eiN[=.P.&9a&fHo:&r&@so]9FD4UiB"NW""P:o
%G,e4mWTDh;gD9.ilbc[BfBKlK36[)BZP,ce,WM-Lb`F$m_b/Js,&sEUlb@oJP9f2V[kHKb1Rc[DGs_eY8`F6U.[<,L=A3m(eQ!o6
%7CBCDeeRgB9&2@=qjkDJ7j"uH-<C;6:si@<Mj?dJ\5t<1$dlLYpfOcsn@C)t^]*<hA_V.uWfJ3[o:hia?S@f9K6>ZC#,glYrYVN?
%(YJ;>4ge&*/FM'(`s3ljq[MR6^#PHTR#n'A0A1-5Itg5aTANs=\Zg&G99C&[Onqb5#Ai+Hk8%%sn]d9(lbb+90+K(qh1"n\nEDEH
%KC%UH+92+lbX14ZP$71E]RP#%r5lqNh>=8.%L*IH8H,k+;[)CIPuY9*Gl5Cs+7=T)G4jX(45TqlLEHFdXpq:Y]\X1uo&T(^IGiu8
%h;.J[B@$$-J&o7jh;4afF5KbJhpGZ*kKZ<,]R"sPENN#>!0;O[s*$C]:>Yf]YFkdm:M7/_ragsVR@jH,e?er0faAi8k?&rh3S9Pq
%r:e]@f/M:tYL6'F2s4OQ4FX,EZ:D[QM>PO:pt=d%j0cc'<>rEnR7A';^Hu^BTqV)<hrU[/p7:2MhC5YZ'ph6UoL^Xo<CZh'4qYO.
%VXVA#o]k1_4Sr_aG\Q@:o4N8+:YpWhn$Pp<s8D:XPQZ$DY3YT-BBo5rDUo0.C%_<!>0>1jnE2`<I_Q%Jq(iUos),i*C!6JJDV%%e
%?eF^0?Tsd#oiDuSII`BM^#L:sP[+10k'_\uq8^fgBoD5:^(Ce_?eMa"]\6hC6eZT7%`c'q2X/CdC=8Ge8<^Vnl\4a%m,i1#0BA/i
%leBVUY5:i949!VH,=u:$I!"uXpAEp$W'%f#h=C$rQMAk2INILpDJl):]kGUBo]I;KY:a/XoQFijO1u8W^n(7He%iT%gHT$"XYQ1*
%<EMAIL>!arnW3[P1^X32iBOM$5FtEIeAIb+M:m3/Ngh,?,Nd$JmuWmfG8I_iBBQIs1795693R.e\#3kSM<D'c]PW/f7^UTlALmc\
%^OfN,`UWG%d^nb6&&6snh#?5k3PW?*GkukBh:nM"NMt:Me%<dNQ[a]^^`-:YocF-&eDnZih&1XB^3+#@\$!sTI&t>^='Cm<e*N#Q
%LuA6*gRZo(J,]*0_:eK'T:c)Pipb@?e)sMDU$aie/)gEgl?9q5S6]DagLF%)Spb$+oH)?Mo^),.0g6LGrUe+03W/.`5J=]oRe,Ah
%&+;Zr]/b7dp2&P[c&qFdI\0PB"rVkOSDE%MFsDHekHZ*Kr';O?Y@$t]cUN,V+DD!cph\Rsr07eR"2G@ts)rW:<m\ajb9tW>#&i)F
%_r[$VKO!D[q07>cY=H2b#1Z3@E;Ak_9$FH1M+=$)XM+,f0<T6=^USQ<\!DKl#N*;#26Y`#UWHlr4&`Wc9cZI0]m+^?qko@i-Pq9C
%48ea@jk\U&QWKMt-7el(e>8LJPPX7koq0O$4W</Kc@:4dnbD\NPJbEWCX5+Ef;QE>:9=f\TY%hG?:P6C\3!A"*C"H9d[Uh$SpfJY
%-&-DccTJ>M:O`9?mciHa?h'nNT`6d6dD,\R(]X'48+2;tkWar;ke%qLGC+Q/c#8(,raDU7fNOP)\Tp4TQ+(+L/6\eDlQ-0'o;eA,
%cE9@FGl%@'fr4HuI#iaMDhj*LQVS-ag^%m^7f7KTb#?MS+jW<!o]S`5cW:5GGMbO]o'kT(r!SqDf11/t+8X6gMo.3?aNjPDq.G2e
%\U&XC['T;=?XB@34^YhuG9;9EPA5+X(.o'$-p1t1L]KcE\=%1^Du7t.HCaU.R&^:48F]tM]/tD=JXAq:RihL9bNm2-It%+A&,t_f
%nh9U#]DMSJZhBEZ)90*25C@SH-Y]dZ@?3`mY%<G]lB+HAbq=IITB1"bZZ<F\^<3X2D0B:P=7Kr7I?e0MDq;/"P:sZ8=3hB*`YdL_
%HC)*q^$<f9ZXlA!?;eD%XF^2,U76N"$X_8E0E9FtcDB"-rM&1d]CP:%CjA<ZIcV)W'W*\ZeUA@LBiX3@!5>[1:/:c^T0HV2+;2fk
%ab,Mtf3'fb)#5_Scj;Jq,cA*3?[VG'#Q57faiEZ9dO0Nn,JIp$V-S3obGNG:T0.FGG(+H.lk_VH@=<3FGBS+L2qe0748akH:Hu?L
%T4L+YeZ\f5PG`V35.nD:HM6W(qsDAih:fhL-i8DjMbi402_f4K@^Z:4QZ\[eI;=oBs4r*)PNk38kkhO:s'\UH3!Zb*A/I'q+Qj/#
%pqln#V^399@@5a2e:"lC<D=@AV9Z2\=gh?l'gH3h`-_0+q=Gg@G\d6-C9RX\jEK"&iWVW@.IJ7t,lr8<02ObdO@q2Kn*^pFgBme$
%FLA%C4m_W:E(^Tn%SlPU@iabBo4L%2`qk[a;#JN3SB1ETLY3'bBg!K00%Gm>7kn*B$h$url_U)nBUS-+J.,uJ9B=WM%.JM8k7!nN
%l^DPL$<DYkMRFiSe.;<tFo4MHZCJ$e0*eEb:,s1QMpJ/*DI1EK`,W=MaU6f+AZKeE6+uf%n@c8%o[XVN;h]b+P`@nk(qo!"ru.Fj
%O;#t"/HXAf#_1t:SWVL>8TjqqLJ)Xd";j9@]7P0t$nI/u&iIUIH37=DoXO1=Wt%Z"J\Nk[icdut;4k=k_C_s[I3Y0'HRO"0QTF(e
%fkaGag#cY:h%jjL4ip4@SP,mLZ,;9Uqd1T?8[]G6)g^hdUP_k)B7O`P_$4c=q%"1''B%hmG.C./2;[eUTJK"g&=q#tH2>T2fH626
%6Ml@#SE^s&j6VkV0!C5iaS"td18+.On8c>AWW"nBhtcH90gF9ooS]5g>.7fC#.1+V,(bat_W+&drZtmP4G;.R.YuIfDCr]uWI?W4
%QUIA?JU^W/iG8@NZr=(P>^utJ\o(uf].tB=oX$O.OcLs/-"ot.U[kN)?'\sNRu9;AbgtG;2Z)/f4R5]\&$-Q8hR[INd4:\5DlG-u
%iM:dV<Ku>A&6j?6]Ntt3+A\C#aWAb/`d:jNpMM@ZKr:!+s(4CeW"7oA3Ueus/$pK!%fXDlpVS*I,;=4%fA,L"=.T&V,rNKkflO+b
%BS&IjY@!R*KDX!G1YLTd`dED>(VUusZhO+^.a2tF^`lVi4+I)+><p+Pd'g\l-@3VJmZ%P;Tml^,)q;HQVctpS^#<W`\k<7A=5X^g
%U1YGhi%;mLfm:=,]bUKX<4l&[n*Th`*u9J'mt#\o<;78]\;^A<`?TbBlgWSpKAFn,6I)mGp&+-bJC>T/[<kLc)XB(]?6]1k^>9;V
%ht.HCD>9`"ma(>a568X<3ru&H/A@>=E&:%X;SN8$$'"5IB!b_%#m&sLK?Y<_D<u3`IrD0i*t6`t+$pKsG/3Q;W7dZYGo6B$6ee/%
%ccF#&!\d"79rhnt!D;JHWD*#@V$=Ba%\Fk4E#R.4+?FOGqNd2C*J5'f&]O2]B;cO@afduXS*f_?KLVcFjm8Bij"Ub-h`L8o]&\9K
%-u.W=P>Udjmo2OEi*/RQ.AJU7q=(6\h\Ti/PF2>HqlMu'&,&CpHD2T`n5n'o-Sca/IGWr'kVp$#rt@"Dq!b2N"d0fJmuL[8oDKWA
%oY%e`k&^]alIOftP'ZT/bM?<HgWeP0`@^:g1VA52WX".>VjI=*j.)=Q5(;VkH0!fJ:oWtu3CjZ@?6;F!rMMpe_$l[bh0="Uk]RK7
%A+k[=i^WW+#QQsErja"GW.>e<86G/3HNLjt4.gW!=.d?Se.(sN<4i5bf;6:3\>Q'HVD2W-q"a4h+Clh:d=*uVs.iZuM32[te(#ZJ
%3<Su_IA];Tk*=+Q7GZ_mB89K*qfGMJW0:TL,QMonB\YN^O)(]WE^=(ej_rK&#=ZW'[n-$Jf?\,["?\D)r=CfXVf_3KgZ<^ERdc,?
%n:ppZ)0cCn)t"h?5H].TmI'4:J[@3,Bc/X-GtuO;S&9?THXck5d)#"^M^J]7ON9@mk;=Vkbu=/F1e)mA5'pt&UhQkd$H8n@j^7Y(
%/r?gs/c@C@k-VU(T/V9_!P?L(^>Vm)l+WG^?#KYg?W9P+X1S;mXU/[75X+jdd4JK"\2<j-6^Q+k:^"46bdK4YXIi3F#]9n/lu#$c
%cS'bXMW3f1-2RN7&63oc%jsS/T([EmTmu)!e7\eXe<s#q%>CWbd/#U;Fb/W,U<NRVb%XkQEj`u8[tOAjN;RiYr?@;OX,&gjH,6bp
%*bp$lT'd@kH_F@bco)6d$h&h\lZJ1[-0ZKr;nMu4gPPmO]!lNM_9_]lo['bIm2`'JU:^Z`O!a^98IWT'(.@E1JnS!j+o2GG2X5TE
%7IOS.r!g1'EcbJsh3Y=ImY1pWfDG<m;Wc7&$HsL9mV=q8Bu-$f%h8@%+rjo(AM-JhT"[[<3n$M\K(DG*.<"T%<:nuVr;AZO3u%sJ
%[uM8H,G8m#r;ScA65EBs&fpXl'=4SkjoenATTHl=q)k1/YK!\.SSM#o$#jICEU'mX^OH8i*_l-Z3dsdk=]`k<^(3bDl"`eaU8)qB
%lXP54CXXc+G&/a#Br?O;lAh]JmSEC6S_V,'0C!c;MfuKT9Y=V*@-*'"U=!DYXS)Z^J_/2VV,#(>EnZ><9qBQ8ii'1Hr?E9P!#5).
%&KJf%f49Zgo1pb/hAq@A1OZDBh2/Lt7PSogJ^GnKD6Q'lJ]):6$@)Eq'-A0PJTVIAP,CeZgkir_ebCs;](tDlV)njQ'Zp`n0"N9N
%Z!^_Y\'>Ce0D\I=^8PY_nN-$($^Ik2S^?$tF*^YV)!6V$Pklo)GXdlHX',+@gp7VgW]CYZlDO]=oNE-3A>4ATWt"ZAMIc?nle<Y<
%lKq0H;lo%m<j,TKAOkQdn'h*s=jd"lC&1;bfKqaYKM+-,Tg5<1p4M1;HTg\C)hMPQEk]25\k6uXKq^dp6910KqVLHi6=G!q>Q(m1
%R(Fp"I7"*p`M#H.GtnIDda6`d8'b3Y:B[2[HMrFO]P/R9Gms19@-SM3[+A]6d'<p6pD#X1g%-]]+$@,`r4(lWD18hJ%&UY]C8>\F
%]iI?jnL0CnIU^j-2-hb!>G/f;A%f"U&UH@`4VE#m\(KeV;g,VK5rHE*&HD6fgBfGWTDs>VqSVJOB=#BH)@;KH\C82O]7k^JIdXVD
%k0\_bGi^*.2@.OHZ_t5$GEUBtG+<;YqAY'4^b4IS,kj7\kEG899][@1l,lWZ_/@`j*HJjnNm8.*S7'S-'8lgXJhC3^Dq&jkR(mTS
%%2s.T6NXjij^/kqj#oQOA;aF`XYp#4nQ\T\Vg`]^QY.&fFnae-=R_:27o;olM)=s:TW+Xul;FfKHJ#Bm12loPIj:@'G(0Cc`J;Z6
%:ZHZ\fLIFHX:Jkfa62MHh.ZOfBSa;?R6@[1<'8_.2eSaWaL1sP0;DKZbPPR-&#hmsoF<+YV/"hU/UoS(h"Ze&eDEF[]hMZKU]52$
%s&C/^kRKCkV>cfI8(Zc[n`Gr9lbr:_3pdU*s4*8*c[sBeM&`Xejj:?>]/[qs%8A634IIpq/eb20>EG"jgOaR+<k#@#2t'cF5M0TA
%`96'm]&W/Ro6p\52TO+d/.^;;3Sg^dlp%7VEO6[G#.J=8mIj?KFK?ZsS`a:hce\e7_Ze5]1XHD2rOk-kL9VHJdE!rNRee@H,*O-S
%2J[-[oB]2!/J[H.^.9gnb-PM`Y%c-NG/o/qLspXs9X1[],koGdRCGdd`8C8J+p$;=+5(:@0;+0sF2[JLPrV5[WDY>]?q6L7//T9R
%*fkGaLFY<2#D@\l0`-Naj?]3CU?mZi-*K5;]pu5FVTo`k4=1cne;2D:ecqdE`aktHE&<b$NJ2]CZRJscT)/1`ol.WG,d9hH,/1a'
%j]#LIA&=NONWBTT=/$''72`QQ6?F)J^A5drkohr"5,]ugF=kXcb'c\[@;A4N#`.?Xg6;47Xt3pPHC-%RDK0QW.rOBN-/u$c49?F8
%DK?<I"0Q0m:"bOk+Ia7,1]$D%3[6@p/J/pB*MIf!3;!dd]`l!93TK/T<(0NDhO^s@RC^+84#;gJc!dZ"kA24$>fH8f(pHO.R4/5O
%G^mCAOk0^$ar/G#]u$ceS-adk5A'!4+AFj453b8k5frBY+?@8!&fM0=oZ^k%*[mH!#AnA1.(U+M/!YnaS2h3T#E4YP9B+<"m$72j
%D[ZrW:"pPa%mPJahq5g+L=TsE.E"<,81gH]K.j?hW'\mcSTeiR=s)ltWb'C)hHn!\+IjN8KSDuUg3=;?!bfZh+jK!gc;5^X>m2/:
%X9p%j`OkDKoK/TJ$[s=kThsN5/)a&r)lO>WpkSIm2V.#Y@Ml9[j\BIh.[iA]d(5<(fn*c<2.CPE7mrUP(,sGF@@$rOk6&8pbalo/
%S,J6=H<*t/n(10_PgTl[e`l!7(JTOp>br<K!j^1!2aXG$8,TJ.5+\UH)Xis![MP:X`2.+53[sHEUPp_n+VlMcgTrt/7XmeU&fMY7
%`&j3i*bF4aM9'QnXg,Tf,E0#UEjHbs)K`KZd>cYiQ0'siHI8?QV(*8j"_dhZi6dieQ;j*mE'F&\,\QC-+hk^Jpo=XP>g47KpX6_%
%VG?b2mD&#oe5f!mmE7;LDaee"9;@0bZF.`MI/,#D-Naii[+i.4;).9!;kcnF/<3t5mUR_^ErgJDJQt,!;:9NZVaUVdX((1+Q'DaE
%BKd"IB1.p*p4nLWE:o59klX'oUJ!f'\.$Y)El!fMWbMt!&-6HpN_r)60$GQU<=5C,_iSA8@rkH7/_g?/>_hCkd$H#UT7+D8M*J]2
%N+93mN5qX@rE5r``cUL_Q[e)&XMCaWG%>-/B/&q>3FSI8+f6u5f:6n-&"VZOY&lIWG8@P3e,dS?'JEPZRn9D^:%o9?Z1J66!_tN-
%4A03(GAU[NKSB06J`;_gI'ECOS1n>@=n^?!D>CR$+BA]KEd>3<ki#.I'SJe'/!QqAO.:X%X9";5U7VEHOfDe<`,]3bl=pZG=Hp(,
%D-p!Dj[>hQ-`37C#f,a<\/*]2\U-(4"L,D)H$%j_0WcZ\EE@8o"@8=li8SZ%B.1hWpo[kA7Fn`sX>E>h&I>24A@!_/+qWr(pb(]@
%i`N8NeU5:T?"QV<db![$;>hsC/\jj@IBQI@]pI-=)H_,9`)m;/N3:%YW1O>'S@_n"mA@FGn)?S0HBKf])UobE5G/[X\FcTnm9Wa4
%9b']c?DP(Tr1kV1`t*%T"CK9u7J*J\UU4N117",t>:iIXCb^UsLIS8bJ-K]1=&H:G%sbled9m#UZ7<C]8\O)g'd@>H&4VIF$fTL[
%:_erJHoHRA!"Au8UTbe'aam-c+op:q&Xj&9UkI.BSPO#&Lji:m,D7fRNZ\aaMj2M=N*m2Rhk5=@1b9U6CZ-b5,o8W$;I3?m!/$4B
%?LT2hnI#Op-gl!B('$2+,[X#/Uorbn$shA<0q^b@Dt*mL14^DD^1@dg3f!1([Y+q(6XEm8"$i>=HYn0GKEhBO=ZTb1*p?#&3b7<1
%7dZ58`+*2bP6hH2k5f$.?3(M0[tm='\?<EDVKLk[JYfF.?`Z)+ENKm5fPYg!KJ]F'j_V7&a@@imEY5Wi!f:\9LG@+fP?$;KdD!n4
%1n$8[?M@9ME&)*,QH5`E<qs?6^>lte3"2=:+`\&S@EepS&ft3+g"A\nOc2utGX0tIp!=tCLb3'R@L9*Ha[P\^](<1,0K0e)(CN9.
%6jStYEFZ1WAGD^XX(J?N1f_WIMk-maI(#]?4ZIn-S,.A[U<q"FT1*7l'363@(EW=jX74G6'X23R^?ulVlnW5C]St36P.[+(j%1"a
%i9^/L7['nGll&?e(.iSQ!uA`+G[1An/=?TscCsNZprO_"WLIj6A`C.REMA5OgHmGp4Qf,5DaEL%A<?\c!-F\_=d69eCrU3B#0DN3
%f:7@\b;Q)cO^)5IA$OuFe:_q>S@W5CK)['^OBFj/H_lrE9]l$+'cPL;WEX5]\FXi!9Gu#>s34i6V_+5uR;[J]HqG_VQu37(_*=l`
%AYJ?\I@"V,Tg5W,h=.GF-)kR-K(?:2,@:@%gOM!_j^OHkd!dhi&+MariX:grhtKj!he)XuB(Nc/o)DRAO4!m/P5J75O-rosF?ceG
%(QaZ[/FflN1STbRNDa+!d.i?r79A"A.Z(Q\4m0m5'`0S2,q=&Es1`;EpEb7dD&n:=RU/smVNQr0<\P57+$0HoLQg;Q"6_!H;-!*?
%1tlH'Bmd*%rWh8hTgW(CZ5bW5SY'[9.;BW*1G:RdGZFn1kU`Tsoh/9e-$FAGRsrB(K70CJhn:-+:e]N"97K:e5%nRWfl_'Q'>kMm
%bpqSZ<#BM;>`@dP;:=lM/>TL0p5<H6gYY=#F48dVe8"Br`Va9!hi08?ob]]T@VO'iNpdMZ\'\0>4[r#c<SJ077qm662kOqinn6eA
%)eYWNFcgQpWac1c>-PRNpTX=TSDee_lSMF<M"n=F#4R3nEOIMB=plh$Q/llo77M5d7r!^1kmY'qhTqk^8<9DX[@i4+oj2mS5@R4E
%AHe;&86G$0IU/!5T2BB$i"l32e0hT`UG,NnF2f=Jg+A(7&RQWlZrpRhQ]V<.-k(-aBW2i"`:n]QWma?"[8P&"Vqj>WKd?'ZD.73j
%q$%%$Em*!8XVUAJ-Ve<erg#TJJ+A^!n9-_s>;eK/CRq-s%%2s%`=PJ^-!"e]IV%3\#3TQUdUu(87n+`66fZ7q)s`5FbBCYmADO?^
%P8gFT8nR8M?JHAL%=R9Y3b"0ua.P1.3["]_,UJoF9Z][Y_H[V.(-O":(O*Km*MNh1U#!/HEV5pir[a[1b!NH`n)I.J5JG?EmVO?*
%NmpdYK5-"SD"UHk-5`qA9%@2QUl)M(U;qVglBiYbK;7%K7AWcY?:2MVSVqFn`PjoePXrG6m-hoGIZ^Wbo]dbnErV0NU#g=Ffo1.N
%m/^M;oqSKbK">\N3Nm:q_5bL:rFE(I3TGaT4c!-+HI/GU,8l!MU*M;H=(DB23P#p,Fr^m6nL:Wn)6h[_<QXT#0)mo]T1"&9DaUZW
%'8J-G-?`YrUJ<Lj^_YV6V'BLnb6A"s`qrc:"1dSXq-HTmE\L<G?W!!TOg1(o<iN%P2qjj%*aVPoWYSMZd4nhYTLbK_a-o"<T(3F5
%H5?%ac54&@&#kLH,h=\ea]`K'\\BWn31*q7W5MS'ZgPg[:o)D%c<]R:X@(@8*ViEG17@cGk!0VHqD%jLk#>.lNg0\3+$d?s8,!3U
%>8i[!9/#6mR7J>rjVA(&NH;mVg^n$Whq,u%Zs[rn9B^AQF"fu,'8ZCr+57_:3B`7Uc+nc1:6I9Q"^aA2nOL("bY^0SR*-Q@[q\>K
%c+U[>K4G'_.CLH%T(qq*7AbCVNqlV,(<'F`([np_9:0h,Sp7kK.AA<NOY%0tn3h0WDr<!7I$fCV`a3.tlLH>o,A^gqJ8Ig]2+"WX
%YZp'l)37G_"2QdUJ=G1@ahjd4iiR<>0?q\M!$"b+m4bFi\XeD.&E=%=D^aO&p$a.<#]1:^B7;T71$F,NkbWR"!IST<M$O?lOd)bh
%2F5t3T/RnFTFmDJotSJEg1l+D5#ja5>0-66/4ek/bYQ?kS8C[c2e.BO(dF/Vo?gP]`'*,Q`6B%/>5i_/:`%7e=sQ*\!0_$B[lC?:
%jc7isDmRWM/pc.adgm#d>+q<abtj/:=*QF1'!h0YMuE$2kB=pbKjEO^-I$N8UJjH4bF$EDB/6-rC"^-K%c8eIli<K9V%VPe>jh][
%1i4/?8iAJ3Uk2=,^9nokA=L][cVRQC*32R]J`BC6HnYh'dH,bc33-#C/'E`M/k?X1QM5bf!DT;(l`g,,qr'k*lGA)eNQ^1i&UccD
%,D"dI$8)l*H'?'[QKDPf17DddW:1\`Q1OdeOs\Jc$1V:m0%<c*\\&5lA1m9<j6(:2252Q*oH5P#Ya`i=a;@JVD_h$S6qhJm'!\[5
%^Gt=828\d,8\NIf&2eO%7Y.(#o:>RA&K1eX<Zam:'C4?S:UA21`KF*<EZB+XE@6Q9i(dUuL!6AsC%TWLTX8>"`d9OfRp9A`8Nb#k
%!1&7B0uc!>3p$-/";1h/V!sMuhR`%eB>:a$ML>FjYuagFPRJJh\rZ:Ad#tDlD,m!WD,[2$n<1Ec>ABP4'Ld@l>(`'7FEF]leT4`G
%(ZY7--(TK@Th8goMumjK>]P)T&/;$3PW2NR&T[:2.OTE4b8')?M6MiX,kuO]lJKqJ[5$T3fWWha`[`e=#(!]^Gt_ME.J]3*(>Y5J
%.Y/ehrc"HI<&@7Ciq/*^)jY9B#8r=:r4lo0P8JUDLoBAJnU+ViQ5/&9b%ONl^X;j;dJ1#B7m&YmOt$1&$_5L\:7u!%/HB@ZTU$&_
%%!==k1+5W,/j[5mC8_QFpE=GBfgQR5a6q,4R8:@s24*sLcRlk?YOU$d]#Zs-W-#MC-,RGtYcTm8`2ZfQ?0N2pDjP]_816Ji`aQ2h
%q^)ek9i*Dh7!WG5K)/n^q_5S2_J'a]Q_3$.3DnUA1olU\*Lt1,l)<Ed4>.C#cCeFH[c212fVf=Oj>+!XZFR9<9^R.k6]JaXC,u!f
%lp&j=&bTd28lJ=&Q:rQcMKuAAFOLLOF]="okZ3d\3UV%=AKf!O[rQc][02tVDhIcl$qggOA$[KNZZ0PECgAG7%o582bal*$r'5]2
%P,e7[6!1O@e_g)N&h&7HQL;FTp@^EIbVr3'6;hp+pW&K@S;N'\LJ[@Xf-k_EPmPJM!j6$S`eV8JnOk.V8n!N`G:CQ]FD4TfYLS7r
%,8%bbi%5U-8Ll,eL7:GKCMLI798UCAE)?5T4\%6<FAp`NgW/&P)4(iski^-PV@*"MmN3+ueo?T@R99!rM&+#J6(\+NU^H0BPscf_
%*dSAQ49gUPmKSKIAs:jFaX(TINJD/m2SD`$`aM?G]B!_^\m44J8!)]A=W]]4]24:4K^)!RdNUHm!U$N'`AZ4')^OZc"-UYU\X1FR
%lW1KY#g4`5%9+(_bWC?F+WEjfLPE1[jNY'i\@Bq1U;sU<3m24$80%3NkWgb2;a$?Y]d$t4=7q$a&0JQG55i17B!-C=EsRZOAeo="
%9l!Sn\DJA.UGX")<qr2cP=.9pBRW$f_LCumiWJ&_7It^"9N\2ccMN<62=atfpIu`LpU`DAZ44g%D$%&PCB@@Sn)1sd]eDFsa9Jt:
%J@\3p*lnrcpO.tXofTAr7Aqk\i**/rRb#1jK.4d:OWhr+b?.-gaCC]G?ft6^r,?Lpr*L2n4Bfk2`Wo!9>"lKV9pa*1=/\>O#hZ+"
%Y%g]>\K=_F;0#k0%]/IpEZ`faF-jNL'1KtB>;Yo,-42XK@SH%me->BKrTQYKbkp73IGe_%J/PcF9q&oV[()5A-RanY",<[Wa"-U7
%[Ve!(Tk1ikH`K-*j,E/l&icQ5"hmsl`s3bYbPa<Aeg/f*T-e:#fE#D0()6pgFdir(GkuNoq@7=Rdsih)GE;GN$9j+%qK:<BSV$(u
%e=4.DBG]dT[fs7Sp%]P>^\D?ph/:NkHEl6I4G1oKr5R%H2=:$^nUaM&^Nr7O+tqj:^L9b7:9V*aPJ4[Y-a9M8,F&E.'%KJ=Fo09J
%rIBqnFnkG0iSX!u,R>TCP_tlBLNj>`(r]]M8cD-(]F.1Zj1I+epg4>:f,tA-RjppUKucQK-1];F@c@8F9j!Rhm@QUm#f>Fu#f:"-
%&;2WfMde:T0DrBY6Qb]n,J-jBJd^$;rN)adRO)4+9CTtE-DQ]#;PhGi70$SghYOOqL"2'1`k-kF#6"2ST2A)MLXFG"$=*NHps@=f
%r[EaOAd]D5!ua9EMoGK/Y3\IH'<S8\XWH.!59'sO:emGqY7+[H?;^GVKUf%YrPYLPOCu0!E8d\`h*c(dqa*M_kbgN!%=QpV71]Ag
%>a1iRGPJ)YN<HWYh!+q@P>Ee)#@beu$;lC3,Qc+cp+X%]K8Fp17Ci9&L[Z:+cld72/)E5\51.Y$8H+ZD)bUX`'2?kV"&@N?9PGft
%jCuU*kp*f_Wr2#jpu*jBo9n9bYI`UUkW%O]HDdg*o;%Sbd?t>kra8/>("UBn$6>$8[8D/"ZifP0%7o'q%W4\jTfiLZ>-i'Z?d)9\
%"s6`?T`]MejU8m+2O\<Y&f-ntX+s;:h`o8(Go3tXj'Esg.C+4G?Yu\R=p3lOc'opqD=ZNH_DcR*^><L"SOHh=71YgVa>snA/W29L
%H6u/'C=39WcOS>$fp!"V<fTKS!eN5"9>'hSkrAJ0a1KE'$"rJ+'/V/?1HoZBdq!]"@pmB1@\s_:(al&&e^'^*4'?"b8_%_Ba[<j<
%'jsLM*TcY"&^$cM\MNN:8tS):_+?<@1smU^mU*q5WJ)>2:s&e+g]jVnM#Yj^4\j/LcAhPDp9f"_h.2a`4s.@9XJXB0fU2[M7Q]]Y
%N8DC.0FUm$Jl!@:)"0(H&E1h6;/4GlAp:TjK`1T2MfAG:#<$a*])\RNYH<M-_+_0IaZk0j]lZU+3>'Xj2_oEj65V7*+(97?YCqaU
%p'J&*?>Ko`d.9$0l6DLG/\M!,m.&5QE+Jng_HAW^$!4fG-euuASFH1,G/?>/"UT3L/.pB<(_M_s?m:tJN@-\Y+@GWNA"+%.cAiG\
%H%qCnCl7`4Lc436%$6p6TVWeCThqUOK^6G\&Qr%-,3j4t7-rCT+l\fYM+A>q,"0SdD%c*Mejs?-8"%HkbT5\#m'IEp$_Jb<I<e:]
%dWq.WO`*+OI[h@5[uDn5Husrg`?Ugi21]G-Q;+)Xj]k3g?EOjW\#kK(%mr?d]'15kKnW24/)U4?Ypjp\`7L[P73#QSb!;bL7tYe#
%bo832UW$W1^=<Y;=I:qTpZAW0bkM#>L<?8XcbReR)2%^![2JnVFjgo-p&]uimet<?S!GRZ6p&R1d]N/iODC^#L;GXj)Tp7Og/'l\
%X+kg2#&;:RK:u3e3emV@:lkQhg+]SD!%[*<A_7d;1dZ8fep*s`Z83@MCj9^%ch\*G])qXb`Oq*ucGDQ#ii9IH#.jf=0iFTm(mW]H
%_3h6a$"L-C#TAWG&o4SoK5T_]d;ChMUohJc/,"/+dA4''cKX[8cktBrf[RF?a:jc2&0@=BY1U\RCaZ,-d\!)"323i:8^5PI.?rX$
%VO0\Q9drPTS+=i/B.COe5uY\]9:D=+HI:76)*^jUI!07.FSA;Sp5np6$3(ShpR!ONY;rr\BSDuP[C8'c#MEf$]NRYa-onGtPLIt0
%SBhE,k)/)RR,8@.2fj>VpbEBC1Yt7_FGo;KGd+Ib_]ETu/qt[1a0OE&=M`q)pkmS!,r1[$JB!c5lWHm[D6^NK/Ztc.UQk+LHK:$m
%9Ldg8AZ[dOQD<3kYZg`0\(*Y4^00-8b@C43r(Ur`]4%i+6=jg^2FCng@qOJ!gW+$b3p,=jel=?YYoXYmZpi#I%0['-B.UaLG_u1J
%JogHiM^SF@[QMK!>BO5VfUC(A^c'DQF1,YEjDBZcemPsV7g$@^jt,5?>V9Ot\+^qjnk7ZjMk7o7rg&Bum+i,5[Eh0>KZ=#!'KLqO
%q/SMYQ;=9.D^f%RE/Ip\;N,>N-s8Gka89JuG4_-7VIM>rI6%k-cDT-/T<t,q\EQ!O.7<!lFNLfZeYOgfNVqPVfe`!>c?Wag3"La!
%HHtl_Fc906SOgN,!q+=2/&"g'>_U#Nk^%.^):tP)`*KGCU>\#3WscDk*-/UoT3HK^Y?q`_FueEhlWfWnGFOT)Hf6DLh8dVBl#B0W
%o,cc:?[<pF/OJL>enkjl4VoetHJ)KEZcoM;?:0GRGhqJ%h^]1rS2YUb[Clb$Jan3=12S\3,No"T[ellF-3m)=AmCF+Y*U7eLSBp4
%:Qu(:01"0t#/l`dD*p`O7D7Eg<3Ti(;X#6jVeR0%VYX8.6`j;+3c4kbaYY$F3pG!<^8/*?p,i68n0I8WW$9Sq3MVkE/!W!0Zh?!b
%j17+F-q'bhQV8!'SstP;Thur[`c1Aik%')F_89g@SHef4@61WfGLN/>1T!=u>W<4`e.<$n1);Ze2/g=V%GVV[qf4\"hVj9bh-\tP
%T87eDI'm5DnGT2#aaZnN(;\C/GsS_H'53+n[^0m=4[^rViGRZep-U[ij9lDOO@jB/Cb3_d+!LZb36OeRP-GRl-$ke-8l860/!],C
%JCGU-_"5NqbNG8+Q[HN#JUm5):AWg;e#dOh%4Z&1c]h^Qr+mpPn10B=n#n4CS:B24=T+!rfa*Zt,8Z0Sk-I<m3_0L%@acCn)Llj]
%mdPQS(ig&P_:ROc&F@n@g#+d+0d0UR]*ee-IOk&ZFehBqO$#1Kcuuk*K\c(K+rkpb[DIW7Xr-RQ>Y97Mm7@n.+mG!-!b73hCh=kH
%>V3B>d;COHVY<*7j2:VJgmBq<o%_rg6ERth8#=XR(!UX<?*kVH)Pc'C!%l:44Kf5A-I]M%XE$:2@cMpCY*e(+6U`E9mZgJ^j&'!>
%WN9pJq+BFX$-hI,d:&*fRY2ZQ3EGSTCLb(Spq9TTnN'k'')f[UT-SgX@$5Shm:=rMMU3VQk!;3,jg=JSUm-k8Pi?SCHCMoAWKgsR
%8\;"U:g1<^kNU^`+I8Tla^)o8UgGpOOjTSr<2ZkBZp"h.D2$;a2Yb\&B?R'A"Mo/&aP"MfHS3sEd5jt(@[.tEC&N+E[o5[X+.>W.
%q.ab9kN>],n`gf8doVruJ%dg#j6Q"\cr:@DI)95eg*Bu!1`TeTYP>t<Uo-@c0cb-QMk*64:a0*'@nB@1%&JPd1BX)ofZDmk2K+^/
%LRG\LY)D.UH1b@a<4jGKXci=Bqiu?@^9&$&VR%aQ_guRMB.Hq/LN1)k(t=PQk\&IJqkunT[9_=tW,q>K<R4H.q>/_VT/Z,a$JI\o
%O=0'lj$PXB)S9u;rl1j>o='>#k'rjOnq2.!f-J!ZX]kb<ogt&`6uP!_n4du_O/gC4q,kW'\PHtQGC4"j8c*S]mY@8)i.,L\bd3@B
%ZbHsWLmA9_2/)emIJm3K<6;O+eYUW)Hb?Uh+?mg4RGb#d4LJA*PI==TBCIg%G3s,BfT!#47CW5@)"^MdI^9iNe/Q)^8?pT)VMh:b
%^K`>`lbe%R^N9%j"7c&7?Dn]?p=/85P>1e+]PB8EQ3QDf\>k&47r3q@7`F:UFL7#&b@;ml2&I,JDo_BW*+:0&c(l_@MPW+3lo2kI
%.BWQtU3BV0/f2.UN,@;IZVo'B3Es8\:guKKMhCZ7dWDYSgIXH9O=\T6U9;SS@R&cDQ@X9P>eV;ZdE:se$EYM'b%fJ(':H8Y-<\r?
%/(Il8]OZ!A&j1qD-H.f"ds`^LV9+=/>HDqXgPqE<!1GRG)reRMl(2<R0V6ZEib?qe*J]a3gWa6mHA1;F.p7q!$03p;5(3$aqOplS
%Qcmk*#WM3%b39_"<PM+d4rSUsS58i?Y702>.\OKQEa%bHf>I27'L4S!:S8"I;IBd5'-0eZC]$2F^]1$7eGgfl7t1^,KqcO&RcE<2
%LK\ct^]1SsG.lR;g-I),jjprW#<!VDMN&+ao=^[Ps2-.%+'#OU#<LBf7l<CkX:E_P;P3@(#t!66f0/G="c,Roq>2i977Md`rr")!
%D@1HD3%(p)7dp;iUjD5^j'V>9i&`Fd6_<`q4/**2-V4hAF!<-JO<./mGS+6N*(+cuC>f:*hdUhmVZjT'H2*.J84K?<(L&mJYqO3A
%0>9i,*/GT>osR,<X%WT's.Wj<\=,e>^L&D[-3*Ph,%$(S_p.c0J'kX]OX(d`%;7j:)LnTlP+j5]eeae;cG+W!`Hl2eim9=gBThSG
%)!\q^=G^)u8K),4diO'N"k:D8,a(PikX3r9X#>eS8&#J*-RF!?Se]dVSfn+X7=.F'UbG+G6OM2,PWTXKj!MPeE]h?6MB3<Am]3$K
%9b\m],_1o?:gX7mdMRiE*[LjI-$bQeEN#.T.s1J2l7:7EU7%9/SA<K:$KY\"(,<i"*gS0"Q"C52J-fah+AbCT]S?_u:_W5i=9aMT
%6(JW(!=PZY'SZF9'KB!"e5h;="Y;0YUH#Z<(3n=F0Mm^QC1=TbP)#0%XY5aL84#,6LZKr>KHq'1du<3n^cjFgJg?5`.l<,/M/[EA
%>S7-P)%ghD#`o/bBV*+9S:F,iBFbPM_gMQRAcS@nUAKY'K/jkU#T`/_3StS`CZ>.+i^u<NR])X6^>XVt_PB$fq_&3F(snSaMobl@
%S"r<`E'a*:&kF]'*rH6q5d4W'kX#uF<6te;_*GG2&BlOEKZeT3-Q<cAMoX0QdK%9<65O01/!Ui7,jJQIr692b!oO<:/$LR:;%RD9
%D5&ISPrC[^7#;`l,Ys=4eu?`31_pfS6fKs\@j;[Qc'0?N;?:8sc4@%5D-Fe'6M&h>"V_9Ys0+SiWeX/"0UP:&2(sM^h%Ns8]O4!2
%;Z.(_d;!$mj\lgCW%J)T/'o+>O":P+91^7;bQ\+OYCn57>_:Dn8clo2/cf92nWZKcFsdVL^c'^iT&bG:E&plpj+IE-E,&aC"6UtE
%[?Uf#HpLKb)n['j+D>`(M<&o`M+(0pqm,/KfO@_=Oc[hM&0]tu)Ckq'Ap;`mEg.=fBMMBH7"X*mTa*maNB@@T-OA@;JO#@YE;8?_
%%XTR:94G7>!7@:!OC/nNI1#KP"Ai2Sa9*;SI%j"PRMd/5'doR5/;Xe9[PFlIdjjmtM&$4G"HYV(i?g7dJJr2>;W:KS%#Y-(8`4L_
%Z4_BH/(1H!(_ltR[6^jD__b1>T[29lF.dmp/Q$%E]##'[##s'%TEZqlG$0V>a@K,@6q0e.1GnOBGQ==4@gjSSWcm(%NC,3s$uU[D
%WATORMbk(mF>Z6:+A^c/W$b`$K+h63;%pu@P\PXY"">4tGgufu\>RS%d)`5Y"Y>gq$:YC_nfjF=Y/+A[P85M@1CV3*'aBA:l7*.r
%LPluZ<&B5$%ssqAfuH^#AL1O3RX5=0_h1c=MX&5-Mdl_l2?[c$WJrH2qkXn:];PpMjN&a1"&kpRp=.#:bfO`^nhcqk[/`"n5[ksh
%H'.+UPf5qiU)C@qK8?;rjj+D?7`%tQ%b38&8Er$)EbOTh-<:a-[>>WC<Vm=J3HYr;cXTg?6tRhQ,,<EX1_MOq*(t#1"/ek:+CpdJ
%WKY)<%$nND:g3YPeB&2h,WshtS,q:4'Z?[O78#(CQDO(-`H%J\82+n2rIXqnm&_67+bF;`N/,HDEZd$rbbt[rNDcZB8.S,D7&n^#
%1k@AQ-gq[J5ZU!A%H'N'((%9MN`RGhRZc/I)T)D$i^5mTBW[pt2'UC^;?0"7U!s3;c#uo96>:EUCgSAh[8B?+&"*]V?W!X%Rjj4g
%O>2QkX$Vhhc=.p\jOEuIiZO.809RQbY<jL(j.[lO;M-7e'kn+/[%^>NiDr'Ti$K4T!];,K_G.\YT<+4i0XH<(1i\Xm0Lc2N%&[5F
%%_t%W<*^T7@!7W,ljBJcGlXFDeJO48;<JYF^8$"PU\=Fh23\Y>mQ:@D0E/q&IsGi-rDtVO<=;V[iEF(brhe(Z7N^ir6D_"V'ua\o
%7G\FOid1&I66Ghh-e[r!S)*:>5Mc5V[@g:X%T&K,&qBrS5G7T;:B~>
%AI9_PrivateDataEnd
