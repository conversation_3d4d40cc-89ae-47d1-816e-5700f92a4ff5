fileFormatVersion: 2
guid: 098b087922f590e43afa49f02d161714
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 1777488443767830289
    second: Enemy3-AttackSmashStart_0
  - first:
      213: -7833720409454760587
    second: Enemy3-AttackSmashStart_1
  - first:
      213: -6161637431825811661
    second: Enemy3-AttackSmashStart_2
  - first:
      213: 3404643750862097984
    second: Enemy3-AttackSmashStart_3
  - first:
      213: 3942464870373950438
    second: Enemy3-AttackSmashStart_4
  - first:
      213: 1959199941604669006
    second: Enemy3-AttackSmashStart_5
  - first:
      213: 7385760228005355208
    second: Enemy3-AttackSmashStart_6
  - first:
      213: -6713818773749960494
    second: Enemy3-AttackSmashStart_7
  - first:
      213: -2524830039376511021
    second: Enemy3-AttackSmashStart_8
  - first:
      213: -2023563897902934879
    second: Enemy3-AttackSmashStart_9
  - first:
      213: 5447888471450109741
    second: Enemy3-AttackSmashStart_10
  - first:
      213: ************6377984
    second: Enemy3-AttackSmashStart_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_0
      rect:
        serializedVersion: 2
        x: 10
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 117b4237258eaa810800000000000000
      internalID: 1777488443767830289
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_1
      rect:
        serializedVersion: 2
        x: 74
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57da8bcdac8094390800000000000000
      internalID: -7833720409454760587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_2
      rect:
        serializedVersion: 2
        x: 138
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 337b549c6677d7aa0800000000000000
      internalID: -6161637431825811661
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_3
      rect:
        serializedVersion: 2
        x: 202
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04e83ece379bf3f20800000000000000
      internalID: 3404643750862097984
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_4
      rect:
        serializedVersion: 2
        x: 266
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6e3881a59e276b630800000000000000
      internalID: 3942464870373950438
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_5
      rect:
        serializedVersion: 2
        x: 330
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e46ff4377f9703b10800000000000000
      internalID: 1959199941604669006
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_6
      rect:
        serializedVersion: 2
        x: 394
        y: 5
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8ce9772cbcd7f7660800000000000000
      internalID: 7385760228005355208
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_7
      rect:
        serializedVersion: 2
        x: 458
        y: 8
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d0f8488569b3d2a0800000000000000
      internalID: -6713818773749960494
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_8
      rect:
        serializedVersion: 2
        x: 522
        y: 13
        width: 43
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3df3ab0d47006fcd0800000000000000
      internalID: -2524830039376511021
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_9
      rect:
        serializedVersion: 2
        x: 586
        y: 17
        width: 43
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1a4d03d2b5bdae3e0800000000000000
      internalID: -2023563897902934879
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_10
      rect:
        serializedVersion: 2
        x: 650
        y: 21
        width: 43
        height: 43
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d27cabc53b9ca9b40800000000000000
      internalID: 5447888471450109741
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-AttackSmashStart_11
      rect:
        serializedVersion: 2
        x: 714
        y: 22
        width: 43
        height: 42
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 08c21511068936a70800000000000000
      internalID: ************6377984
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Enemy3-AttackSmashStart_0: 1777488443767830289
      Enemy3-AttackSmashStart_1: -7833720409454760587
      Enemy3-AttackSmashStart_10: 5447888471450109741
      Enemy3-AttackSmashStart_11: ************6377984
      Enemy3-AttackSmashStart_2: -6161637431825811661
      Enemy3-AttackSmashStart_3: 3404643750862097984
      Enemy3-AttackSmashStart_4: 3942464870373950438
      Enemy3-AttackSmashStart_5: 1959199941604669006
      Enemy3-AttackSmashStart_6: 7385760228005355208
      Enemy3-AttackSmashStart_7: -6713818773749960494
      Enemy3-AttackSmashStart_8: -2524830039376511021
      Enemy3-AttackSmashStart_9: -2023563897902934879
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
