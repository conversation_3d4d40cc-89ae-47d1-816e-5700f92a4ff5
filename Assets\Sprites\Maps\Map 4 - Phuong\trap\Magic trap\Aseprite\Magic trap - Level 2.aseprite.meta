fileFormatVersion: 2
guid: 142ae5ee06d1285489156825546afa00
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 96
      height: 64
    spriteID: 3d64c338a44024d4b8eb92da879a3b94
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 4
      width: 96
      height: 64
    spriteID: a3e313b54f8cf4b489dda21c04c62791
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 4}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 4
      width: 96
      height: 64
    spriteID: 9c3e37bcbf02f8b40bfe11a33e9dc795
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 4}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 4
      width: 96
      height: 64
    spriteID: d6f7fc278ada93e44ba9d9d5e0154de2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 4}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 76
      width: 96
      height: 64
    spriteID: 6cb620c3c1e79d045ac42240847b5f3d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 76}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 76
      width: 96
      height: 64
    spriteID: 2bafde2116453e94799ef99c1daf1911
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 76}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 76
      width: 96
      height: 64
    spriteID: 87fa4fc7b79d3e74d825f1d2262bb6eb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 76}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 76
      width: 96
      height: 64
    spriteID: 5a5ce4c1e4f62024bab1b5abd3ba0f38
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 76}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 148
      width: 96
      height: 64
    spriteID: 60cd4daf9e515ee4e9af13aa0eb6e8d6
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 148}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 148
      width: 96
      height: 64
    spriteID: 0805dbabbf7d0e644b7aa89338561246
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 148}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 148
      width: 96
      height: 64
    spriteID: 576b576b04e1e214ea9472065b17ecfc
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 148}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 148
      width: 96
      height: 64
    spriteID: b3b731a547e8de74090f5ca3dcdb799b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 148}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 220
      width: 96
      height: 64
    spriteID: d3760cf19ad48394f8ef900b73f79ab9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 220}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 220
      width: 96
      height: 64
    spriteID: 1a03ec19c24c6d34fb7702361bd92865
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 220}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 220
      width: 96
      height: 64
    spriteID: 5ac7c1e45ae295c4f914fe5b2e6f9279
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 220}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 220
      width: 96
      height: 64
    spriteID: 19cc8d2d46014b0469948075d620f97f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 220}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 292
      width: 96
      height: 64
    spriteID: 0294e20349bc8064ba2674e38e650d5e
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 292}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 292
      width: 96
      height: 64
    spriteID: 2747c55d76bd2c747b455c866b012f6c
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 292}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 292
      width: 96
      height: 64
    spriteID: b613ec8806bc6bd4eb9a3c98ecd74499
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 292}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 292
      width: 96
      height: 64
    spriteID: 73e228080cb346d4cb8cdc062c0a711d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 292}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 364
      width: 96
      height: 64
    spriteID: 58f89d6c41fe14d4998d537cc7df2b85
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 364}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 364
      width: 96
      height: 64
    spriteID: 1fd4bfe1f7ce36b44ba8530ccbb9fced
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 364}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 212
      y: 364
      width: 96
      height: 64
    spriteID: e0df0475676ef774da7b3e2cafae7b48
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 212, y: 364}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 316
      y: 364
      width: 96
      height: 64
    spriteID: eae916785b8dbcd45a756a7b6743e941
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 316, y: 364}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 436
      width: 96
      height: 64
    spriteID: 02c59375b685be947bb686cd78da1220
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 436}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 108
      y: 436
      width: 96
      height: 64
    spriteID: 2edac915cd566d944958d0c5155eddab
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 108, y: 436}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 191339159
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Magic trap - Level 2
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 3d64c338a44024d4b8eb92da879a3b94
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: a3e313b54f8cf4b489dda21c04c62791
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 9c3e37bcbf02f8b40bfe11a33e9dc795
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: d6f7fc278ada93e44ba9d9d5e0154de2
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 6cb620c3c1e79d045ac42240847b5f3d
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 2bafde2116453e94799ef99c1daf1911
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 87fa4fc7b79d3e74d825f1d2262bb6eb
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5a5ce4c1e4f62024bab1b5abd3ba0f38
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 60cd4daf9e515ee4e9af13aa0eb6e8d6
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 0805dbabbf7d0e644b7aa89338561246
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 576b576b04e1e214ea9472065b17ecfc
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: b3b731a547e8de74090f5ca3dcdb799b
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: d3760cf19ad48394f8ef900b73f79ab9
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 1a03ec19c24c6d34fb7702361bd92865
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 5ac7c1e45ae295c4f914fe5b2e6f9279
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 19cc8d2d46014b0469948075d620f97f
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 0294e20349bc8064ba2674e38e650d5e
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 2747c55d76bd2c747b455c866b012f6c
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: b613ec8806bc6bd4eb9a3c98ecd74499
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 73e228080cb346d4cb8cdc062c0a711d
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 58f89d6c41fe14d4998d537cc7df2b85
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 1fd4bfe1f7ce36b44ba8530ccbb9fced
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: e0df0475676ef774da7b3e2cafae7b48
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: eae916785b8dbcd45a756a7b6743e941
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 02c59375b685be947bb686cd78da1220
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 96
        height: 64
      spriteId: 2edac915cd566d944958d0c5155eddab
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings: []
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 96, y: 64}
  previousTextureSize: {x: 512, y: 512}
