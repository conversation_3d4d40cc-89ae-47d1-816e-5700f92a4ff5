fileFormatVersion: 2
guid: e854e6c13c361f8499ad6e35a92c4fa1
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4331948892352828423
    second: Enemy3-Idle_0
  - first:
      213: -1740225227256936238
    second: Enemy3-Idle_1
  - first:
      213: 4053655113787753197
    second: Enemy3-Idle_2
  - first:
      213: -8530596565656002719
    second: Enemy3-Idle_3
  - first:
      213: 1944211335605986252
    second: Enemy3-Idle_4
  - first:
      213: -6984796632099248290
    second: Enemy3-Idle_5
  - first:
      213: -6567577415892154145
    second: Enemy3-Idle_6
  - first:
      213: -827917689925968828
    second: Enemy3-Idle_7
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 32
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Enemy3-Idle_0
      rect:
        serializedVersion: 2
        x: 10
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fba92e1f63d1e3c0800000000000000
      internalID: -4331948892352828423
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_1
      rect:
        serializedVersion: 2
        x: 74
        y: 5
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d84d2c306a79d7e0800000000000000
      internalID: -1740225227256936238
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_2
      rect:
        serializedVersion: 2
        x: 138
        y: 4
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de2c34b49d9714830800000000000000
      internalID: 4053655113787753197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_3
      rect:
        serializedVersion: 2
        x: 202
        y: 3
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16f18edde9b3d9980800000000000000
      internalID: -8530596565656002719
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_4
      rect:
        serializedVersion: 2
        x: 266
        y: 4
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc3251ba8e93bfa10800000000000000
      internalID: 1944211335605986252
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_5
      rect:
        serializedVersion: 2
        x: 330
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e57216b5874011f90800000000000000
      internalID: -6984796632099248290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_6
      rect:
        serializedVersion: 2
        x: 394
        y: 7
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fd8ced203274bd4a0800000000000000
      internalID: -6567577415892154145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3-Idle_7
      rect:
        serializedVersion: 2
        x: 458
        y: 8
        width: 43
        height: 54
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44c53bc5f35a284f0800000000000000
      internalID: -827917689925968828
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Enemy3-Idle_0: -4331948892352828423
      Enemy3-Idle_1: -1740225227256936238
      Enemy3-Idle_2: 4053655113787753197
      Enemy3-Idle_3: -8530596565656002719
      Enemy3-Idle_4: 1944211335605986252
      Enemy3-Idle_5: -6984796632099248290
      Enemy3-Idle_6: -6567577415892154145
      Enemy3-Idle_7: -827917689925968828
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
