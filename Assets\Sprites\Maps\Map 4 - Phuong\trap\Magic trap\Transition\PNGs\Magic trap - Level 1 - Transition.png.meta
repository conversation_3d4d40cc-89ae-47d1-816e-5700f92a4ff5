fileFormatVersion: 2
guid: 460c3c26b92b24e4c964215495d083af
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 2440028935699923930
    second: Magic trap - Level 1 - Transition_0
  - first:
      213: 1333571439720091973
    second: Magic trap - Level 1 - Transition_1
  - first:
      213: -9179607637497870653
    second: Magic trap - Level 1 - Transition_2
  - first:
      213: -8559699626365139675
    second: Magic trap - Level 1 - Transition_3
  - first:
      213: -4374327030464864415
    second: Magic trap - Level 1 - Transition_4
  - first:
      213: -1156409802689012469
    second: Magic trap - Level 1 - Transition_5
  - first:
      213: 436841869258927927
    second: Magic trap - Level 1 - Transition_6
  - first:
      213: 4780388473822520027
    second: Magic trap - Level 1 - Transition_7
  - first:
      213: 8581410163064964952
    second: Magic trap - Level 1 - Transition_8
  - first:
      213: 1957929599075838227
    second: Magic trap - Level 1 - Transition_9
  - first:
      213: 8008282881141363845
    second: Magic trap - Level 1 - Transition_10
  - first:
      213: -4979563506804990471
    second: Magic trap - Level 1 - Transition_11
  - first:
      213: 8754540260993177436
    second: Magic trap - Level 1 - Transition_12
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_0
      rect:
        serializedVersion: 2
        x: 65
        y: 0
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ad38112a369bcd120800000000000000
      internalID: 2440028935699923930
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_1
      rect:
        serializedVersion: 2
        x: 97
        y: 0
        width: 30
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 5415f1ba82cc18210800000000000000
      internalID: 1333571439720091973
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_2
      rect:
        serializedVersion: 2
        x: 129
        y: 0
        width: 30
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3c60a754d7b7b9080800000000000000
      internalID: -9179607637497870653
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_3
      rect:
        serializedVersion: 2
        x: 161
        y: 5
        width: 30
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 52546728a86d53980800000000000000
      internalID: -8559699626365139675
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_4
      rect:
        serializedVersion: 2
        x: 193
        y: 6
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 16f90c1fdb44b43c0800000000000000
      internalID: -4374327030464864415
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_5
      rect:
        serializedVersion: 2
        x: 225
        y: 6
        width: 30
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b0d11ff496b93ffe0800000000000000
      internalID: -1156409802689012469
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_6
      rect:
        serializedVersion: 2
        x: 265
        y: 16
        width: 22
        height: 6
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73f22389d59ff0600800000000000000
      internalID: 436841869258927927
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_7
      rect:
        serializedVersion: 2
        x: 33
        y: 0
        width: 30
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bd220964df9575240800000000000000
      internalID: 4780388473822520027
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_8
      rect:
        serializedVersion: 2
        x: 255
        y: 0
        width: 193
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8570a69b31b471770800000000000000
      internalID: 8581410163064964952
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_9
      rect:
        serializedVersion: 2
        x: 315
        y: 2
        width: 10
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3196328c896fb2b10800000000000000
      internalID: 1957929599075838227
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_10
      rect:
        serializedVersion: 2
        x: 347
        y: 5
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 584d547c9d2232f60800000000000000
      internalID: 8008282881141363845
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_11
      rect:
        serializedVersion: 2
        x: 379
        y: 5
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9fdd3689f5905eab0800000000000000
      internalID: -4979563506804990471
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Magic trap - Level 1 - Transition_12
      rect:
        serializedVersion: 2
        x: 411
        y: 4
        width: 10
        height: 8
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c579a5cdbff5e7970800000000000000
      internalID: 8754540260993177436
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Magic trap - Level 1 - Transition_0: 2440028935699923930
      Magic trap - Level 1 - Transition_1: 1333571439720091973
      Magic trap - Level 1 - Transition_10: 8008282881141363845
      Magic trap - Level 1 - Transition_11: -4979563506804990471
      Magic trap - Level 1 - Transition_12: 8754540260993177436
      Magic trap - Level 1 - Transition_2: -9179607637497870653
      Magic trap - Level 1 - Transition_3: -8559699626365139675
      Magic trap - Level 1 - Transition_4: -4374327030464864415
      Magic trap - Level 1 - Transition_5: -1156409802689012469
      Magic trap - Level 1 - Transition_6: 436841869258927927
      Magic trap - Level 1 - Transition_7: 4780388473822520027
      Magic trap - Level 1 - Transition_8: 8581410163064964952
      Magic trap - Level 1 - Transition_9: 1957929599075838227
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
