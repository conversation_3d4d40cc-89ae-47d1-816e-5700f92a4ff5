fileFormatVersion: 2
guid: 41f7b05164728e4489bed4eb656cb001
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 100
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 0
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 32
      height: 32
    spriteID: 0ecc8147b3126464ea108a6697149d87
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 44
      width: 32
      height: 32
    spriteID: 914674878d0e67f40ac82cb24d5d8d49
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 44}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 84
      width: 32
      height: 32
    spriteID: b360dad70e46dd94487d5b2715e07cd8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 84}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 124
      width: 32
      height: 32
    spriteID: d56c129ec5dac8f478198924ce3e683d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 124}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 164
      width: 32
      height: 32
    spriteID: 6e71246c48a62f341904e044ea9d519d
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 164}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 204
      width: 32
      height: 32
    spriteID: 3f419e878b371f54b9403cec5c876121
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 204}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 4
      width: 32
      height: 32
    spriteID: 9b5a92d4ae8efd74bb0baecb09ea3c57
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 4}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 44
      width: 32
      height: 32
    spriteID: 8591166c62fd7cb429f0542a52bf18f8
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 44}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 84
      width: 32
      height: 32
    spriteID: 15a643801eaaa7a468e18d845a17dcd3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 84}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 124
      width: 32
      height: 32
    spriteID: 303aa1e76860a634aaea9614db51c6d7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 124}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 164
      width: 32
      height: 32
    spriteID: 6415df4459f590d4abf28e36e8df118b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 164}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 44
      y: 204
      width: 32
      height: 32
    spriteID: f5f8382a167ea3c4b9a588e68b9a1cc4
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 44, y: 204}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 4
      width: 32
      height: 32
    spriteID: 889064175c950cc44ac7fc02c68e96b7
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 4}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.5, y: 0}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 84
      y: 44
      width: 32
      height: 32
    spriteID: 7d310c5009780d549a08d24a8ef25d7f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 84, y: 44}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 2265710644
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Magic trap - Level 1 - Transition
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 0ecc8147b3126464ea108a6697149d87
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 914674878d0e67f40ac82cb24d5d8d49
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: b360dad70e46dd94487d5b2715e07cd8
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: d56c129ec5dac8f478198924ce3e683d
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 6e71246c48a62f341904e044ea9d519d
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 3f419e878b371f54b9403cec5c876121
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 9b5a92d4ae8efd74bb0baecb09ea3c57
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 8591166c62fd7cb429f0542a52bf18f8
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 15a643801eaaa7a468e18d845a17dcd3
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 303aa1e76860a634aaea9614db51c6d7
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 6415df4459f590d4abf28e36e8df118b
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: f5f8382a167ea3c4b9a588e68b9a1cc4
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 889064175c950cc44ac7fc02c68e96b7
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 0
        y: 0
        width: 32
        height: 32
      spriteId: 7d310c5009780d549a08d24a8ef25d7f
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings: []
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 32, y: 32}
  previousTextureSize: {x: 128, y: 256}
