<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:editor="UnityEditor.UIElements" xmlns:engine="UnityEngine.UIElements" xmlns="UnityEditor.Accessibility" elementFormDefault="qualified" targetNamespace="UnityEngine.UIElements" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:simpleType name="VisualElement_picking-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Position" />
      <xs:enumeration value="Ignore" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="VisualElement_usage-hints_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="DynamicTransform" />
      <xs:enumeration value="GroupTransform" />
      <xs:enumeration value="MaskContainer" />
      <xs:enumeration value="DynamicColor" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="VisualElement_language-direction_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Inherit" />
      <xs:enumeration value="LTR" />
      <xs:enumeration value="RTL" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="VisualElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="xs:anyType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VisualElement" type="engine:VisualElementType" />
  <xs:complexType name="TextElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="-1" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enable-rich-text" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="parse-escape-sequences" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="selectable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="double-click-selects-word" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="triple-click-selects-line" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="display-tooltip-when-elided" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TextElement" substitutionGroup="engine:VisualElement" type="engine:TextElementType" />
  <xs:complexType name="PopupWindowType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="-1" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enable-rich-text" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="parse-escape-sequences" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="selectable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="double-click-selects-word" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="triple-click-selects-line" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="display-tooltip-when-elided" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="PopupWindow" substitutionGroup="engine:VisualElement" type="engine:PopupWindowType" />
  <xs:complexType name="Vector2IntFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0, 0)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Vector2IntField" substitutionGroup="engine:VisualElement" type="engine:Vector2IntFieldType" />
  <xs:complexType name="MinMaxSliderType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0.00, 10.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="-3.402823E+38" name="low-limit" type="xs:float" use="optional" />
        <xs:attribute default="3.402823E+38" name="high-limit" type="xs:float" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MinMaxSlider" substitutionGroup="engine:VisualElement" type="engine:MinMaxSliderType" />
  <xs:complexType name="ToggleButtonGroupType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0000000000000000000000000000000000000000000000000000000000000000" name="value" type="xs:string" use="optional" />
        <xs:attribute default="false" name="is-multiple-selection" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="allow-empty-selection" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ToggleButtonGroup" substitutionGroup="engine:VisualElement" type="engine:ToggleButtonGroupType" />
  <xs:simpleType name="SortColumnDescription_direction_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Ascending" />
      <xs:enumeration value="Descending" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SortColumnDescriptionType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="column-name" type="xs:string" use="optional" />
        <xs:attribute default="-1" name="column-index" type="xs:int" use="optional" />
        <xs:attribute default="Ascending" name="direction" type="engine:SortColumnDescription_direction_Type" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SortColumnDescription" substitutionGroup="engine:VisualElement" type="engine:SortColumnDescriptionType" />
  <xs:simpleType name="MultiColumnTreeView_virtualization-method_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="FixedHeight" />
      <xs:enumeration value="DynamicHeight" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnTreeView_selection-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Single" />
      <xs:enumeration value="Multiple" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnTreeView_show-alternating-row-backgrounds_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="ContentOnly" />
      <xs:enumeration value="All" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnTreeView_sorting-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Default" />
      <xs:enumeration value="Custom" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="MultiColumnTreeViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="22" name="fixed-item-height" type="xs:float" use="optional" />
        <xs:attribute default="FixedHeight" name="virtualization-method" type="engine:MultiColumnTreeView_virtualization-method_Type" use="optional" />
        <xs:attribute default="false" name="show-border" type="xs:boolean" use="optional" />
        <xs:attribute default="Single" name="selection-type" type="engine:MultiColumnTreeView_selection-type_Type" use="optional" />
        <xs:attribute default="None" name="show-alternating-row-backgrounds" type="engine:MultiColumnTreeView_show-alternating-row-backgrounds_Type" use="optional" />
        <xs:attribute default="false" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="horizontal-scrolling" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="auto-expand" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="sorting-enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="None" name="sorting-mode" type="engine:MultiColumnTreeView_sorting-mode_Type" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MultiColumnTreeView" substitutionGroup="engine:VisualElement" type="engine:MultiColumnTreeViewType" />
  <xs:complexType name="TabViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="false" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TabView" substitutionGroup="engine:VisualElement" type="engine:TabViewType" />
  <xs:complexType name="TemplateContainerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="template" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TemplateContainer" substitutionGroup="engine:VisualElement" type="engine:TemplateContainerType" />
  <xs:simpleType name="IntegerField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="IntegerField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="IntegerFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:int" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:IntegerField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:IntegerField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="IntegerField" substitutionGroup="engine:VisualElement" type="engine:IntegerFieldType" />
  <xs:simpleType name="Scroller_direction_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Horizontal" />
      <xs:enumeration value="Vertical" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ScrollerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="0" name="low-value" type="xs:float" use="optional" />
        <xs:attribute default="0" name="high-value" type="xs:float" use="optional" />
        <xs:attribute default="Vertical" name="direction" type="engine:Scroller_direction_Type" use="optional" />
        <xs:attribute default="0" name="value" type="xs:float" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Scroller" substitutionGroup="engine:VisualElement" type="engine:ScrollerType" />
  <xs:complexType name="RadioButtonType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="false" name="value" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="toggle-on-label-click" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RadioButton" substitutionGroup="engine:VisualElement" type="engine:RadioButtonType" />
  <xs:complexType name="RepeatButtonType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="-1" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enable-rich-text" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="parse-escape-sequences" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="selectable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="double-click-selects-word" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="triple-click-selects-line" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="display-tooltip-when-elided" type="xs:boolean" use="optional" />
        <xs:attribute name="delay" type="xs:long" use="optional" />
        <xs:attribute name="interval" type="xs:long" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RepeatButton" substitutionGroup="engine:VisualElement" type="engine:RepeatButtonType" />
  <xs:complexType name="ButtonType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enable-rich-text" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="parse-escape-sequences" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="selectable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="double-click-selects-word" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="triple-click-selects-line" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="display-tooltip-when-elided" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="icon-image" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Button" substitutionGroup="engine:VisualElement" type="engine:ButtonType" />
  <xs:complexType name="Vector4FieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0.00, 0.00, 0.00, 0.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Vector4Field" substitutionGroup="engine:VisualElement" type="engine:Vector4FieldType" />
  <xs:simpleType name="Slider_direction_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Horizontal" />
      <xs:enumeration value="Vertical" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SliderType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:float" use="optional" />
        <xs:attribute default="false" name="fill" type="xs:boolean" use="optional" />
        <xs:attribute default="0" name="low-value" type="xs:float" use="optional" />
        <xs:attribute default="10" name="high-value" type="xs:float" use="optional" />
        <xs:attribute default="0" name="page-size" type="xs:float" use="optional" />
        <xs:attribute default="false" name="show-input-field" type="xs:boolean" use="optional" />
        <xs:attribute default="Horizontal" name="direction" type="engine:Slider_direction_Type" use="optional" />
        <xs:attribute default="false" name="inverted" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Slider" substitutionGroup="engine:VisualElement" type="engine:SliderType" />
  <xs:complexType name="TabType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="" name="icon-image" type="xs:string" use="optional" />
        <xs:attribute default="false" name="closeable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Tab" substitutionGroup="engine:VisualElement" type="engine:TabType" />
  <xs:simpleType name="MultiColumnListView_virtualization-method_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="FixedHeight" />
      <xs:enumeration value="DynamicHeight" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnListView_selection-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Single" />
      <xs:enumeration value="Multiple" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnListView_show-alternating-row-backgrounds_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="ContentOnly" />
      <xs:enumeration value="All" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnListView_reorder-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Simple" />
      <xs:enumeration value="Animated" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnListView_binding-source-selection-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Manual" />
      <xs:enumeration value="AutoAssign" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MultiColumnListView_sorting-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Default" />
      <xs:enumeration value="Custom" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="MultiColumnListViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="22" name="fixed-item-height" type="xs:float" use="optional" />
        <xs:attribute default="FixedHeight" name="virtualization-method" type="engine:MultiColumnListView_virtualization-method_Type" use="optional" />
        <xs:attribute default="false" name="show-border" type="xs:boolean" use="optional" />
        <xs:attribute default="Single" name="selection-type" type="engine:MultiColumnListView_selection-type_Type" use="optional" />
        <xs:attribute default="None" name="show-alternating-row-backgrounds" type="engine:MultiColumnListView_show-alternating-row-backgrounds_Type" use="optional" />
        <xs:attribute default="false" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="horizontal-scrolling" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="show-foldout-header" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="header-title" type="xs:string" use="optional" />
        <xs:attribute default="false" name="show-add-remove-footer" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="allow-add" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="allow-remove" type="xs:boolean" use="optional" />
        <xs:attribute default="Simple" name="reorder-mode" type="engine:MultiColumnListView_reorder-mode_Type" use="optional" />
        <xs:attribute default="true" name="show-bound-collection-size" type="xs:boolean" use="optional" />
        <xs:attribute default="Manual" name="binding-source-selection-mode" type="engine:MultiColumnListView_binding-source-selection-mode_Type" use="optional" />
        <xs:attribute default="false" name="sorting-enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="None" name="sorting-mode" type="engine:MultiColumnListView_sorting-mode_Type" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="MultiColumnListView" substitutionGroup="engine:VisualElement" type="engine:MultiColumnListViewType" />
  <xs:complexType name="BoxType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Box" substitutionGroup="engine:VisualElement" type="engine:BoxType" />
  <xs:simpleType name="TwoPaneSplitView_orientation_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Horizontal" />
      <xs:enumeration value="Vertical" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="TwoPaneSplitViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="0" name="fixed-pane-index" type="xs:int" use="optional" />
        <xs:attribute default="100" name="fixed-pane-initial-dimension" type="xs:float" use="optional" />
        <xs:attribute default="Horizontal" name="orientation" type="engine:TwoPaneSplitView_orientation_Type" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TwoPaneSplitView" substitutionGroup="engine:VisualElement" type="engine:TwoPaneSplitViewType" />
  <xs:simpleType name="LongField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="LongField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="LongFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:long" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:LongField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:LongField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="LongField" substitutionGroup="engine:VisualElement" type="engine:LongFieldType" />
  <xs:simpleType name="FloatField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="FloatField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="FloatFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:float" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:FloatField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:FloatField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="FloatField" substitutionGroup="engine:VisualElement" type="engine:FloatFieldType" />
  <xs:complexType name="ImageType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Image" substitutionGroup="engine:VisualElement" type="engine:ImageType" />
  <xs:simpleType name="DoubleField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="DoubleField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="DoubleFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:double" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:DoubleField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:DoubleField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DoubleField" substitutionGroup="engine:VisualElement" type="engine:DoubleFieldType" />
  <xs:complexType name="SortColumnDescriptionsType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SortColumnDescriptions" substitutionGroup="engine:VisualElement" type="engine:SortColumnDescriptionsType" />
  <xs:simpleType name="TextField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TextField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="TextFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="" name="value" type="xs:string" use="optional" />
        <xs:attribute default="-1" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="*" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:TextField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:TextField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="multiline" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TextField" substitutionGroup="engine:VisualElement" type="engine:TextFieldType" />
  <xs:simpleType name="UnsignedLongField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="UnsignedLongField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="UnsignedLongFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:string" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:UnsignedLongField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:UnsignedLongField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UnsignedLongField" substitutionGroup="engine:VisualElement" type="engine:UnsignedLongFieldType" />
  <xs:complexType name="EnumFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="type" type="xs:string" use="optional" />
        <xs:attribute default="false" name="include-obsolete-values" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EnumField" substitutionGroup="engine:VisualElement" type="engine:EnumFieldType" />
  <xs:complexType name="BoundsIntFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="Position: (0, 0, 0), Size: (0, 0, 0)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BoundsIntField" substitutionGroup="engine:VisualElement" type="engine:BoundsIntFieldType" />
  <xs:complexType name="RectIntFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(x:0, y:0, width:0, height:0)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RectIntField" substitutionGroup="engine:VisualElement" type="engine:RectIntFieldType" />
  <xs:complexType name="ToggleType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="false" name="value" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="toggle-on-label-click" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Toggle" substitutionGroup="engine:VisualElement" type="engine:ToggleType" />
  <xs:simpleType name="DataBinding_update-trigger_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="WhenDirty" />
      <xs:enumeration value="OnSourceChanged" />
      <xs:enumeration value="EveryUpdate" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="DataBinding_binding-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="TwoWay" />
      <xs:enumeration value="ToSource" />
      <xs:enumeration value="ToTarget" />
      <xs:enumeration value="ToTargetOnce" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="DataBindingType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="property" type="xs:string" use="optional" />
        <xs:attribute default="OnSourceChanged" name="update-trigger" type="engine:DataBinding_update-trigger_Type" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="TwoWay" name="binding-mode" type="engine:DataBinding_binding-mode_Type" use="optional" />
        <xs:attribute default="" name="source-to-ui-converters" type="xs:string" use="optional" />
        <xs:attribute default="" name="ui-to-source-converters" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DataBinding" substitutionGroup="engine:VisualElement" type="engine:DataBindingType" />
  <xs:simpleType name="ListView_virtualization-method_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="FixedHeight" />
      <xs:enumeration value="DynamicHeight" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ListView_selection-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Single" />
      <xs:enumeration value="Multiple" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ListView_show-alternating-row-backgrounds_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="ContentOnly" />
      <xs:enumeration value="All" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ListView_reorder-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Simple" />
      <xs:enumeration value="Animated" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ListView_binding-source-selection-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Manual" />
      <xs:enumeration value="AutoAssign" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ListViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="22" name="fixed-item-height" type="xs:float" use="optional" />
        <xs:attribute default="FixedHeight" name="virtualization-method" type="engine:ListView_virtualization-method_Type" use="optional" />
        <xs:attribute default="false" name="show-border" type="xs:boolean" use="optional" />
        <xs:attribute default="Single" name="selection-type" type="engine:ListView_selection-type_Type" use="optional" />
        <xs:attribute default="None" name="show-alternating-row-backgrounds" type="engine:ListView_show-alternating-row-backgrounds_Type" use="optional" />
        <xs:attribute default="false" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="horizontal-scrolling" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="show-foldout-header" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="header-title" type="xs:string" use="optional" />
        <xs:attribute default="false" name="show-add-remove-footer" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="allow-add" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="allow-remove" type="xs:boolean" use="optional" />
        <xs:attribute default="Simple" name="reorder-mode" type="engine:ListView_reorder-mode_Type" use="optional" />
        <xs:attribute default="true" name="show-bound-collection-size" type="xs:boolean" use="optional" />
        <xs:attribute default="Manual" name="binding-source-selection-mode" type="engine:ListView_binding-source-selection-mode_Type" use="optional" />
        <xs:attribute default="" name="item-template" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ListView" substitutionGroup="engine:VisualElement" type="engine:ListViewType" />
  <xs:simpleType name="SliderInt_direction_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Horizontal" />
      <xs:enumeration value="Vertical" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="SliderIntType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Ignore" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:int" use="optional" />
        <xs:attribute default="false" name="fill" type="xs:boolean" use="optional" />
        <xs:attribute default="0" name="low-value" type="xs:int" use="optional" />
        <xs:attribute default="10" name="high-value" type="xs:int" use="optional" />
        <xs:attribute default="0" name="page-size" type="xs:float" use="optional" />
        <xs:attribute default="false" name="show-input-field" type="xs:boolean" use="optional" />
        <xs:attribute default="Horizontal" name="direction" type="engine:SliderInt_direction_Type" use="optional" />
        <xs:attribute default="false" name="inverted" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SliderInt" substitutionGroup="engine:VisualElement" type="engine:SliderIntType" />
  <xs:complexType name="ButtonStripFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:int" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ButtonStripField" substitutionGroup="engine:VisualElement" type="engine:ButtonStripFieldType" />
  <xs:complexType name="LabelType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="-1" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enable-rich-text" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="parse-escape-sequences" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="selectable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="double-click-selects-word" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="triple-click-selects-line" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="display-tooltip-when-elided" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Label" substitutionGroup="engine:VisualElement" type="engine:LabelType" />
  <xs:complexType name="BoundsFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="Center: (0.00, 0.00, 0.00), Extents: (0.00, 0.00, 0.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BoundsField" substitutionGroup="engine:VisualElement" type="engine:BoundsFieldType" />
  <xs:complexType name="BindableElementType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="BindableElement" substitutionGroup="engine:VisualElement" type="engine:BindableElementType" />
  <xs:complexType name="DropdownFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:int" use="optional" />
        <xs:attribute default="-1" name="index" type="xs:int" use="optional" />
        <xs:attribute default="System.Collections.Generic.List`1[System.String]" name="choices" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="DropdownField" substitutionGroup="engine:VisualElement" type="engine:DropdownFieldType" />
  <xs:complexType name="Vector3IntFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0, 0, 0)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Vector3IntField" substitutionGroup="engine:VisualElement" type="engine:Vector3IntFieldType" />
  <xs:complexType name="RadioButtonGroupType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="-1" name="value" type="xs:int" use="optional" />
        <xs:attribute default="System.Collections.Generic.List`1[System.String]" name="choices" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RadioButtonGroup" substitutionGroup="engine:VisualElement" type="engine:RadioButtonGroupType" />
  <xs:complexType name="Vector3FieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0.00, 0.00, 0.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Vector3Field" substitutionGroup="engine:VisualElement" type="engine:Vector3FieldType" />
  <xs:simpleType name="Hash128Field_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="Hash128Field_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="Hash128FieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="00000000000000000000000000000000" name="value" type="xs:string" use="optional" />
        <xs:attribute default="-1" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:Hash128Field_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:Hash128Field_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Hash128Field" substitutionGroup="engine:VisualElement" type="engine:Hash128FieldType" />
  <xs:complexType name="GroupBoxType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GroupBox" substitutionGroup="engine:VisualElement" type="engine:GroupBoxType" />
  <xs:simpleType name="UnsignedIntegerField_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="UnsignedIntegerField_keyboard-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="ASCIICapable" />
      <xs:enumeration value="NumbersAndPunctuation" />
      <xs:enumeration value="URL" />
      <xs:enumeration value="NumberPad" />
      <xs:enumeration value="PhonePad" />
      <xs:enumeration value="NamePhonePad" />
      <xs:enumeration value="EmailAddress" />
      <xs:enumeration value="NintendoNetworkAccount" />
      <xs:enumeration value="Social" />
      <xs:enumeration value="Search" />
      <xs:enumeration value="DecimalPad" />
      <xs:enumeration value="OneTimeCode" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="UnsignedIntegerFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="0" name="value" type="xs:string" use="optional" />
        <xs:attribute default="1000" name="max-length" type="xs:int" use="optional" />
        <xs:attribute default="false" name="password" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="mask-character" type="xs:string" use="optional" />
        <xs:attribute default="" name="placeholder-text" type="xs:string" use="optional" />
        <xs:attribute default="false" name="hide-placeholder-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="readonly" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="is-delayed" type="xs:boolean" use="optional" />
        <xs:attribute default="Hidden" name="vertical-scroller-visibility" type="engine:UnsignedIntegerField_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="true" name="select-all-on-mouse-up" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-all-on-focus" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-word-by-double-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="select-line-by-triple-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="emoji-fallback-support" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="hide-mobile-input" type="xs:boolean" use="optional" />
        <xs:attribute default="Default" name="keyboard-type" type="engine:UnsignedIntegerField_keyboard-type_Type" use="optional" />
        <xs:attribute default="false" name="auto-correction" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UnsignedIntegerField" substitutionGroup="engine:VisualElement" type="engine:UnsignedIntegerFieldType" />
  <xs:complexType name="RectFieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(x:0.00, y:0.00, width:0.00, height:0.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="RectField" substitutionGroup="engine:VisualElement" type="engine:RectFieldType" />
  <xs:complexType name="ColumnType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="title" type="xs:string" use="optional" />
        <xs:attribute default="true" name="visible" type="xs:boolean" use="optional" />
        <xs:attribute default="0" name="width" type="xs:string" use="optional" />
        <xs:attribute default="35px" name="min-width" type="xs:string" use="optional" />
        <xs:attribute default="8388608px" name="max-width" type="xs:string" use="optional" />
        <xs:attribute default="false" name="stretchable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="sortable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="optional" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="resizable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="header-template" type="xs:string" use="optional" />
        <xs:attribute default="" name="cell-template" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Column" substitutionGroup="engine:VisualElement" type="engine:ColumnType" />
  <xs:complexType name="IMGUIContainerType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="IMGUIContainer" substitutionGroup="engine:VisualElement" type="engine:IMGUIContainerType" />
  <xs:simpleType name="TreeView_virtualization-method_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="FixedHeight" />
      <xs:enumeration value="DynamicHeight" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TreeView_selection-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Single" />
      <xs:enumeration value="Multiple" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="TreeView_show-alternating-row-backgrounds_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="ContentOnly" />
      <xs:enumeration value="All" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="TreeViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="22" name="fixed-item-height" type="xs:float" use="optional" />
        <xs:attribute default="FixedHeight" name="virtualization-method" type="engine:TreeView_virtualization-method_Type" use="optional" />
        <xs:attribute default="false" name="show-border" type="xs:boolean" use="optional" />
        <xs:attribute default="Single" name="selection-type" type="engine:TreeView_selection-type_Type" use="optional" />
        <xs:attribute default="None" name="show-alternating-row-backgrounds" type="engine:TreeView_show-alternating-row-backgrounds_Type" use="optional" />
        <xs:attribute default="false" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="horizontal-scrolling" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="auto-expand" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="item-template" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="TreeView" substitutionGroup="engine:VisualElement" type="engine:TreeViewType" />
  <xs:simpleType name="HelpBox_message-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="None" />
      <xs:enumeration value="Info" />
      <xs:enumeration value="Warning" />
      <xs:enumeration value="Error" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="HelpBoxType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="None" name="message-type" type="engine:HelpBox_message-type_Type" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="HelpBox" substitutionGroup="engine:VisualElement" type="engine:HelpBoxType" />
  <xs:complexType name="Vector2FieldType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="label" type="xs:string" use="optional" />
        <xs:attribute default="(0.00, 0.00)" name="value" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Vector2Field" substitutionGroup="engine:VisualElement" type="engine:Vector2FieldType" />
  <xs:complexType name="FoldoutType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="true" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="text" type="xs:string" use="optional" />
        <xs:attribute default="true" name="toggle-on-label-click" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="value" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Foldout" substitutionGroup="engine:VisualElement" type="engine:FoldoutType" />
  <xs:simpleType name="Columns_stretch-mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Grow" />
      <xs:enumeration value="GrowAndFill" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ColumnsType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="primary-column-name" type="xs:string" use="optional" />
        <xs:attribute default="GrowAndFill" name="stretch-mode" type="engine:Columns_stretch-mode_Type" use="optional" />
        <xs:attribute default="true" name="reorderable" type="xs:boolean" use="optional" />
        <xs:attribute default="true" name="resizable" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="resize-preview" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Columns" substitutionGroup="engine:VisualElement" type="engine:ColumnsType" />
  <xs:simpleType name="ScrollView_mode_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Vertical" />
      <xs:enumeration value="Horizontal" />
      <xs:enumeration value="VerticalAndHorizontal" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ScrollView_nested-interaction-kind_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Default" />
      <xs:enumeration value="StopScrolling" />
      <xs:enumeration value="ForwardScrolling" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ScrollView_horizontal-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ScrollView_vertical-scroller-visibility_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Auto" />
      <xs:enumeration value="AlwaysVisible" />
      <xs:enumeration value="Hidden" />
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="ScrollView_touch-scroll-type_Type">
    <xs:restriction base="xs:string">
      <xs:enumeration value="Unrestricted" />
      <xs:enumeration value="Elastic" />
      <xs:enumeration value="Clamped" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="ScrollViewType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="Vertical" name="mode" type="engine:ScrollView_mode_Type" use="optional" />
        <xs:attribute default="Default" name="nested-interaction-kind" type="engine:ScrollView_nested-interaction-kind_Type" use="optional" />
        <xs:attribute default="false" name="show-horizontal-scroller" type="xs:boolean" use="optional" />
        <xs:attribute default="false" name="show-vertical-scroller" type="xs:boolean" use="optional" />
        <xs:attribute default="Auto" name="horizontal-scroller-visibility" type="engine:ScrollView_horizontal-scroller-visibility_Type" use="optional" />
        <xs:attribute default="Auto" name="vertical-scroller-visibility" type="engine:ScrollView_vertical-scroller-visibility_Type" use="optional" />
        <xs:attribute default="-1" name="horizontal-page-size" type="xs:float" use="optional" />
        <xs:attribute default="-1" name="vertical-page-size" type="xs:float" use="optional" />
        <xs:attribute default="18" name="mouse-wheel-scroll-size" type="xs:float" use="optional" />
        <xs:attribute default="Clamped" name="touch-scroll-type" type="engine:ScrollView_touch-scroll-type_Type" use="optional" />
        <xs:attribute default="0.135" name="scroll-deceleration-rate" type="xs:float" use="optional" />
        <xs:attribute default="0.1" name="elasticity" type="xs:float" use="optional" />
        <xs:attribute default="16" name="elastic-animation-interval-ms" type="xs:long" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ScrollView" substitutionGroup="engine:VisualElement" type="engine:ScrollViewType" />
  <xs:complexType name="ProgressBarType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="Inherit" name="language-direction" type="engine:VisualElement_language-direction_Type" use="optional" />
        <xs:attribute default="" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute default="0" name="low-value" type="xs:float" use="optional" />
        <xs:attribute default="100" name="high-value" type="xs:float" use="optional" />
        <xs:attribute default="0" name="value" type="xs:float" use="optional" />
        <xs:attribute default="" name="title" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="ProgressBar" substitutionGroup="engine:VisualElement" type="engine:ProgressBarType" />
  <xs:complexType name="UXMLType">
    <xs:complexContent mixed="false">
      <xs:restriction base="xs:anyType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
          <xs:element ref="engine:VisualElement" />
        </xs:sequence>
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="UXML" type="engine:UXMLType" />
  <xs:complexType name="TemplateType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute name="name" type="xs:string" use="required" />
        <xs:attribute default="" name="path" type="xs:string" use="optional" />
        <xs:attribute default="" name="src" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Template" substitutionGroup="engine:VisualElement" type="engine:TemplateType" />
  <xs:complexType name="StyleType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="" name="path" type="xs:string" use="optional" />
        <xs:attribute default="" name="src" type="xs:string" use="optional" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Style" substitutionGroup="engine:VisualElement" type="engine:StyleType" />
  <xs:complexType name="AttributeOverridesType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute name="element-name" type="xs:string" use="required" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="AttributeOverrides" substitutionGroup="engine:VisualElement" type="engine:AttributeOverridesType" />
  <xs:complexType name="InstanceType">
    <xs:complexContent mixed="false">
      <xs:restriction base="engine:VisualElementType">
        <xs:attribute default="" name="name" type="xs:string" use="optional" />
        <xs:attribute default="true" name="enabled" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="view-data-key" type="xs:string" use="optional" />
        <xs:attribute default="Position" name="picking-mode" type="engine:VisualElement_picking-mode_Type" use="optional" />
        <xs:attribute default="" name="tooltip" type="xs:string" use="optional" />
        <xs:attribute default="None" name="usage-hints" type="engine:VisualElement_usage-hints_Type" use="optional" />
        <xs:attribute default="0" name="tabindex" type="xs:int" use="optional" />
        <xs:attribute default="false" name="focusable" type="xs:boolean" use="optional" />
        <xs:attribute default="" name="class" type="xs:string" use="optional" />
        <xs:attribute default="" name="content-container" type="xs:string" use="optional" />
        <xs:attribute default="" name="style" type="xs:string" use="optional" />
        <xs:attribute default="null" name="data-source" type="xs:string" use="optional" />
        <xs:attribute default="" name="data-source-path" type="xs:string" use="optional" />
        <xs:attribute default="null" name="data-source-type" type="xs:string" use="optional" />
        <xs:attribute default="" name="binding-path" type="xs:string" use="optional" />
        <xs:attribute name="template" type="xs:string" use="required" />
        <xs:anyAttribute processContents="lax" />
      </xs:restriction>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Instance" substitutionGroup="engine:VisualElement" type="engine:InstanceType" />
</xs:schema>