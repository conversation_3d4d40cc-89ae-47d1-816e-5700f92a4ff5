fileFormatVersion: 2
guid: 7e693f2b75bedcf46877ecc506b799b5
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -2833775182555582667
    second: skill1_0
  - first:
      213: -5708941596360452247
    second: skill1_1
  - first:
      213: -6852985128836715184
    second: skill1_2
  - first:
      213: -706068992433645699
    second: skill1_3
  - first:
      213: 6341960913417918139
    second: skill1_4
  - first:
      213: 3594819719720684759
    second: skill1_5
  - first:
      213: -5259044502740863221
    second: skill1_6
  - first:
      213: 391909082404558028
    second: skill1_7
  - first:
      213: 6694025853591573197
    second: skill1_8
  - first:
      213: 2365104174983624309
    second: skill1_9
  - first:
      213: -6857078158124440818
    second: skill1_10
  - first:
      213: -2682086547265108738
    second: skill1_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: skill1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 53fe4764c786ca8d0800000000000000
      internalID: -2833775182555582667
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_1
      rect:
        serializedVersion: 2
        x: 100
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 967db798dd3c5c0b0800000000000000
      internalID: -5708941596360452247
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_2
      rect:
        serializedVersion: 2
        x: 200
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 05d27a0855e45e0a0800000000000000
      internalID: -6852985128836715184
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_3
      rect:
        serializedVersion: 2
        x: 300
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d7713170ef98336f0800000000000000
      internalID: -706068992433645699
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_4
      rect:
        serializedVersion: 2
        x: 400
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bb6dfaf79db230850800000000000000
      internalID: 6341960913417918139
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_5
      rect:
        serializedVersion: 2
        x: 500
        y: 100
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7dc45892d7d53e130800000000000000
      internalID: 3594819719720684759
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_6
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b07f82c63ee1407b0800000000000000
      internalID: -5259044502740863221
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_7
      rect:
        serializedVersion: 2
        x: 100
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc419f8eb37507500800000000000000
      internalID: 391909082404558028
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_8
      rect:
        serializedVersion: 2
        x: 200
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: dc25b902c05f5ec50800000000000000
      internalID: 6694025853591573197
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_9
      rect:
        serializedVersion: 2
        x: 300
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 57631be57b982d020800000000000000
      internalID: 2365104174983624309
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_10
      rect:
        serializedVersion: 2
        x: 400
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e0327ebdeb3c6d0a0800000000000000
      internalID: -6857078158124440818
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: skill1_11
      rect:
        serializedVersion: 2
        x: 500
        y: 0
        width: 100
        height: 100
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ef4c86dde7057cad0800000000000000
      internalID: -2682086547265108738
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: cffe2e799ad15d64bac40dc100588f33
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":100.0,"y":100.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      skill1_0: -2833775182555582667
      skill1_1: -5708941596360452247
      skill1_10: -6857078158124440818
      skill1_11: -2682086547265108738
      skill1_2: -6852985128836715184
      skill1_3: -706068992433645699
      skill1_4: 6341960913417918139
      skill1_5: 3594819719720684759
      skill1_6: -5259044502740863221
      skill1_7: 391909082404558028
      skill1_8: 6694025853591573197
      skill1_9: 2365104174983624309
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
