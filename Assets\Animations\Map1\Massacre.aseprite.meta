fileFormatVersion: 2
guid: ce43ac8dfdbcec1459c4ca8df6ab2d2c
ScriptedImporter:
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:AnimationClip
      assembly: UnityEngine.AnimationModule
      name: atk
    second: {fileID: 7400000, guid: 8403ef1be138ad4478c9299dee9e4849, type: 2}
  - first:
      type: UnityEngine:AnimationClip
      assembly: UnityEngine.AnimationModule
      name: death
    second: {fileID: 7400000, guid: 2f3f20b5debd9a54b9b3020fcfc6f440, type: 2}
  - first:
      type: UnityEngine:AnimationClip
      assembly: UnityEngine.AnimationModule
      name: hit
    second: {fileID: 7400000, guid: 2ebaa603e7b014b4c9acbba16c044d8d, type: 2}
  - first:
      type: UnityEngine:AnimationClip
      assembly: UnityEngine.AnimationModule
      name: idle
    second: {fileID: 7400000, guid: 9e1562b70666be2478e4969647fea07a, type: 2}
  - first:
      type: UnityEngine:AnimationClip
      assembly: UnityEngine.AnimationModule
      name: pet - FUTURE
    second: {fileID: 7400000, guid: c1441a5db22efe643ad653549916130c, type: 2}
  serializedVersion: 2
  userData: 
  assetBundleName: 
  assetBundleVariant: 
  script: {fileID: 11500000, guid: 62a9f0aa5b59740cfbadc7e5f9823bb0, type: 3}
  importerVersion: 2
  textureImporterSettings:
    alphaSource: 1
    mipMapMode: 0
    enableMipMap: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
    convertToNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
    swizzle: 50462976
    isReadable: 0
    streamingMipmaps: 0
    streamingMipmapsPriority: 0
    vTOnly: 0
    ignoreMipmapLimit: 0
    nPOTScale: 1
    sRGBTexture: 1
    spriteMode: 2
    spriteExtrude: 1
    spriteMeshType: 1
    alignment: 0
    spritePivot: {x: 0.5, y: 0.5}
    spritePixelsToUnits: 64
    spriteBorder: {x: 0, y: 0, z: 0, w: 0}
    spriteGenerateFallbackPhysicsShape: 0
    generateCubemap: 6
    cubemapConvolution: 0
    seamlessCubemap: 0
    alphaIsTransparency: 1
    spriteTessellationDetail: -1
    textureType: 8
    textureShape: 1
    singleChannelComponent: 0
    flipbookRows: 0
    flipbookColumns: 0
    ignorePngGamma: 0
    cookieMode: 0
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
    normalMap: 0
    textureFormat: 0
    maxTextureSize: 0
    lightmap: 0
    compressionQuality: 0
    linearTexture: 0
    grayScaleToAlpha: 0
    rGBM: 0
    cubemapConvolutionSteps: 0
    cubemapConvolutionExponent: 0
    maxTextureSizeSet: 0
    compressionQualitySet: 0
    textureFormatSet: 0
    applyGammaDecoding: 0
  previousAsepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  asepriteImporterSettings:
    fileImportMode: 1
    importHiddenLayers: 0
    layerImportMode: 1
    defaultPivotSpace: 0
    defaultPivotAlignment: 7
    customPivotPosition: {x: 0.5, y: 0.5}
    mosaicPadding: 4
    spritePadding: 0
    generateModelPrefab: 1
    generateAnimationClips: 1
    addSortingGroup: 1
    addShadowCasters: 0
    generateIndividualEvents: 1
    generateSpriteAtlas: 1
  importFileNodeState: 1
  platformSettingsDirtyTick: 638844162802355159
  textureAssetName: 
  singleSpriteImportData:
  - name: 
    originalName: 
    pivot: {x: 0, y: 0}
    alignment: 0
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 0
      y: 0
      width: 0
      height: 0
    spriteID: 
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 0, y: 0}
  animatedSpriteImportData:
  - name: Frame_1
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 83
      width: 61
      height: 25
    spriteID: 33132f91729650545951bba6e47b3881
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 83}
  - name: Frame_2
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 116
      width: 61
      height: 25
    spriteID: 8aa27cab32d1cd947b3894b3c954d37f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 116}
  - name: Frame_3
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 347
      width: 61
      height: 24
    spriteID: f1440b47467cc834aa47025be51977af
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 347}
  - name: Frame_4
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 379
      width: 61
      height: 24
    spriteID: ba9cc2031be71c64aa6a5884d231b919
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 379}
  - name: Frame_5
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 411
      width: 61
      height: 24
    spriteID: 59e4bc3ff1c17b1418b929a30d82bf36
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 411}
  - name: Frame_6
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 443
      width: 61
      height: 24
    spriteID: 88e4860f6d75f5542a148e6880d994a2
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 443}
  - name: Frame_7
    originalName: 
    pivot: {x: 0.45614034, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 305
      width: 57
      height: 23
    spriteID: afa3e4550462df442b131933020f1932
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 305}
  - name: Frame_8
    originalName: 
    pivot: {x: 0.46551725, y: -0.06451613}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 197
      width: 58
      height: 31
    spriteID: bf9ab3e394bba094183acf64bbe0b012
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 197}
  - name: Frame_10
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 475
      width: 55
      height: 26
    spriteID: e51b2e0abe50ec442a6935e92f98c4fe
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 475}
  - name: Frame_11
    originalName: 
    pivot: {x: 0.47272727, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 197
      y: 475
      width: 55
      height: 23
    spriteID: 83f642f9195c04b47a6b94840b4e5f92
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 197, y: 475}
  - name: Frame_12
    originalName: 
    pivot: {x: 0.52, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 336
      width: 50
      height: 22
    spriteID: 678bc6195eec0234bac16fde4c5487c9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 336}
  - name: Frame_13
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 203
      y: 347
      width: 48
      height: 21
    spriteID: d1b35a2acdadf474793fd3c557f03d2a
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 203, y: 347}
  - name: Frame_14
    originalName: 
    pivot: {x: 0.52, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 366
      width: 50
      height: 22
    spriteID: f27564aba440dc6438a20ac74114aeb9
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 366}
  - name: Frame_15
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 236
      width: 60
      height: 27
    spriteID: c6258fe2183f90146a9c6633db0016f0
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 236}
  - name: Frame_16
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 271
      width: 60
      height: 26
    spriteID: df4692f1657d82b4bb5d4be409b4712b
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 271}
  - name: Frame_17
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 156
      width: 60
      height: 33
    spriteID: 7d077024cc6b6e545a9ff263080a63bf
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 156}
  - name: Frame_18
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 110
      width: 60
      height: 38
    spriteID: 389b6b77af495674dadf86ab744ee1ec
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 110}
  - name: Frame_19
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 4
      width: 60
      height: 44
    spriteID: c624cf609f100a142bcc39783d059b63
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 4}
  - name: Frame_20
    originalName: 
    pivot: {x: 0.5357143, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 66
      y: 56
      width: 56
      height: 46
    spriteID: 454a7aef5429fa145a2e2ed95b386d23
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 66, y: 56}
  - name: Frame_21
    originalName: 
    pivot: {x: 0.5555556, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 4
      y: 4
      width: 54
      height: 51
    spriteID: 1cca96709e1b39e42ae979b42e056427
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 4, y: 4}
  - name: Frame_22
    originalName: 
    pivot: {x: 0.625, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 203
      y: 379
      width: 48
      height: 17
    spriteID: a04775f2a21a5244893c0c042cf04a86
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 203, y: 379}
  - name: Frame_23
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 149
      width: 61
      height: 25
    spriteID: 6f900da9a5d99b043becd5e36267fafa
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 149}
  - name: Frame_24
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 182
      width: 61
      height: 25
    spriteID: 1cac6c6d694529a41b6a6d780f068b8f
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 182}
  - name: Frame_25
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 215
      width: 61
      height: 25
    spriteID: c6a8d012947b93446827512885cb8285
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 215}
  - name: Frame_26
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 248
      width: 61
      height: 25
    spriteID: c91605b1af96558419caf73d094d9559
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 248}
  - name: Frame_27
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 281
      width: 61
      height: 25
    spriteID: 708f92efd0756c141a5e22dc4ee2febb
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 281}
  - name: Frame_28
    originalName: 
    pivot: {x: 0.49180326, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 314
      width: 61
      height: 25
    spriteID: fefffbf44908bc147bcb9d2f13373663
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 314}
  - name: Frame_9
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 4
      width: 62
      height: 35
    spriteID: 9b9adcfcb2f602f4e835f1ff2aa3a9e3
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 4}
  - name: Frame_0
    originalName: 
    pivot: {x: 0.5, y: 0.7}
    alignment: 9
    border: {x: 0, y: 0, z: 0, w: 0}
    customData: 
    rect:
      serializedVersion: 2
      x: 134
      y: 47
      width: 61
      height: 28
    spriteID: 668eb4f0355d6d04890ddf8e32d0ae94
    spriteBone: []
    spriteOutline: []
    vertices: []
    spritePhysicsOutline: []
    indices: 
    edges: []
    tessellationDetail: 0
    uvTransform: {x: 134, y: 47}
  spriteSheetImportData: []
  tileSetImportData: []
  asepriteLayers:
  - layerIndex: 0
    uuid:
      value0: 625146845
      value1: 0
      value2: 0
      value3: 0
    guid: 0
    name: Massacre
    layerFlags: 0
    layerType: 0
    blendMode: 0
    cells:
    - name: Frame_1
      frameIndex: 1
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: 33132f91729650545951bba6e47b3881
    - name: Frame_2
      frameIndex: 2
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: 8aa27cab32d1cd947b3894b3c954d37f
    - name: Frame_3
      frameIndex: 3
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 24
      spriteId: f1440b47467cc834aa47025be51977af
    - name: Frame_4
      frameIndex: 4
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 24
      spriteId: ba9cc2031be71c64aa6a5884d231b919
    - name: Frame_5
      frameIndex: 5
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 24
      spriteId: 59e4bc3ff1c17b1418b929a30d82bf36
    - name: Frame_6
      frameIndex: 6
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 24
      spriteId: 88e4860f6d75f5542a148e6880d994a2
    - name: Frame_7
      frameIndex: 7
      additiveSortOrder: 0
      cellRect:
        x: 6
        y: 0
        width: 57
        height: 23
      spriteId: afa3e4550462df442b131933020f1932
    - name: Frame_8
      frameIndex: 8
      additiveSortOrder: 0
      cellRect:
        x: 5
        y: 2
        width: 58
        height: 31
      spriteId: bf9ab3e394bba094183acf64bbe0b012
    - name: Frame_10
      frameIndex: 10
      additiveSortOrder: 0
      cellRect:
        x: 4
        y: 5
        width: 55
        height: 26
      spriteId: e51b2e0abe50ec442a6935e92f98c4fe
    - name: Frame_11
      frameIndex: 11
      additiveSortOrder: 0
      cellRect:
        x: 6
        y: 0
        width: 55
        height: 23
      spriteId: 83f642f9195c04b47a6b94840b4e5f92
    - name: Frame_12
      frameIndex: 12
      additiveSortOrder: 0
      cellRect:
        x: 6
        y: 0
        width: 50
        height: 22
      spriteId: 678bc6195eec0234bac16fde4c5487c9
    - name: Frame_13
      frameIndex: 13
      additiveSortOrder: 0
      cellRect:
        x: 8
        y: 0
        width: 48
        height: 21
      spriteId: d1b35a2acdadf474793fd3c557f03d2a
    - name: Frame_14
      frameIndex: 14
      additiveSortOrder: 0
      cellRect:
        x: 6
        y: 0
        width: 50
        height: 22
      spriteId: f27564aba440dc6438a20ac74114aeb9
    - name: Frame_15
      frameIndex: 15
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 60
        height: 27
      spriteId: c6258fe2183f90146a9c6633db0016f0
    - name: Frame_16
      frameIndex: 16
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 60
        height: 26
      spriteId: df4692f1657d82b4bb5d4be409b4712b
    - name: Frame_17
      frameIndex: 17
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 60
        height: 33
      spriteId: 7d077024cc6b6e545a9ff263080a63bf
    - name: Frame_18
      frameIndex: 18
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 60
        height: 38
      spriteId: 389b6b77af495674dadf86ab744ee1ec
    - name: Frame_19
      frameIndex: 19
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 60
        height: 44
      spriteId: c624cf609f100a142bcc39783d059b63
    - name: Frame_20
      frameIndex: 20
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 56
        height: 46
      spriteId: 454a7aef5429fa145a2e2ed95b386d23
    - name: Frame_21
      frameIndex: 21
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: -2
        width: 54
        height: 51
      spriteId: 1cca96709e1b39e42ae979b42e056427
    - name: Frame_22
      frameIndex: 22
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: -2
        width: 48
        height: 17
      spriteId: a04775f2a21a5244893c0c042cf04a86
    - name: Frame_23
      frameIndex: 23
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: 6f900da9a5d99b043becd5e36267fafa
    - name: Frame_24
      frameIndex: 24
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: 1cac6c6d694529a41b6a6d780f068b8f
    - name: Frame_25
      frameIndex: 25
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: c6a8d012947b93446827512885cb8285
    - name: Frame_26
      frameIndex: 26
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: c91605b1af96558419caf73d094d9559
    - name: Frame_27
      frameIndex: 27
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: 708f92efd0756c141a5e22dc4ee2febb
    - name: Frame_28
      frameIndex: 28
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 25
      spriteId: fefffbf44908bc147bcb9d2f13373663
    - name: Frame_9
      frameIndex: 9
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 6
        width: 62
        height: 35
      spriteId: 9b9adcfcb2f602f4e835f1ff2aa3a9e3
    - name: Frame_0
      frameIndex: 0
      additiveSortOrder: 0
      cellRect:
        x: 2
        y: 0
        width: 61
        height: 28
      spriteId: 668eb4f0355d6d04890ddf8e32d0ae94
    linkedCells: []
    tileCells: []
    tileSetIndex: 0
    parentIndex: -1
  tileSets: []
  platformSettings:
  - name: DefaultTexturePlatform
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: Standalone
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  - name: WebGL
    overridden: 0
    ignorePlatformSupport: 0
    maxTextureSize: 16384
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    forceMaximumCompressionQuality_BC6H_BC7: 0
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    androidETC2FallbackOverride: 0
  generatePhysicsShape: 0
  secondarySpriteTextures: []
  spritePackingTag: 
  canvasSize: {x: 64, y: 64}
  previousTextureSize: {x: 256, y: 512}
