fileFormatVersion: 2
guid: 63d5596941e01884ebb25a1afced0518
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8340873856085754300
    second: env_rock_0
  - first:
      213: 479919144530993615
    second: env_rock_1
  - first:
      213: -6357495892027737555
    second: env_rock_2
  - first:
      213: -359003401882473920
    second: env_rock_3
  - first:
      213: 8239160857659893436
    second: env_rock_4
  - first:
      213: 4158498375409969417
    second: env_rock_5
  - first:
      213: -6675707502533136265
    second: env_rock_6
  - first:
      213: -7659465075636163285
    second: env_rock_7
  - first:
      213: -7112252247093738425
    second: env_rock_8
  - first:
      213: 2224516406013919252
    second: env_rock_9
  - first:
      213: -3060084300064211181
    second: env_rock_10
  - first:
      213: 5726487468224604094
    second: env_rock_11
  - first:
      213: 7096511611821811947
    second: env_rock_12
  - first:
      213: 7507645219391891788
    second: env_rock_13
  - first:
      213: -1480235927506507313
    second: env_rock_14
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: env_rock_0
      rect:
        serializedVersion: 2
        x: 10
        y: 335
        width: 44
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 44e7aa37b634f3c80800000000000000
      internalID: -8340873856085754300
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_1
      rect:
        serializedVersion: 2
        x: 63
        y: 287
        width: 53
        height: 193
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc1eda25be309a600800000000000000
      internalID: 479919144530993615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_2
      rect:
        serializedVersion: 2
        x: 143
        y: 415
        width: 34
        height: 65
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2a7eae6b23a5c7a0800000000000000
      internalID: -6357495892027737555
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_3
      rect:
        serializedVersion: 2
        x: 188
        y: 287
        width: 104
        height: 193
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 04a63adde40940bf0800000000000000
      internalID: -359003401882473920
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_4
      rect:
        serializedVersion: 2
        x: 319
        y: 415
        width: 34
        height: 65
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cb6ee18a521675270800000000000000
      internalID: 8239160857659893436
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_5
      rect:
        serializedVersion: 2
        x: 364
        y: 287
        width: 53
        height: 193
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 909a9516d34f5b930800000000000000
      internalID: 4158498375409969417
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_6
      rect:
        serializedVersion: 2
        x: 426
        y: 351
        width: 44
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7703a17556f1b53a0800000000000000
      internalID: -6675707502533136265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_7
      rect:
        serializedVersion: 2
        x: 111
        y: 362
        width: 98
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b2d2235681d14b590800000000000000
      internalID: -7659465075636163285
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_8
      rect:
        serializedVersion: 2
        x: 287
        y: 362
        width: 98
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 744010b73443c4d90800000000000000
      internalID: -7112252247093738425
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_9
      rect:
        serializedVersion: 2
        x: 143
        y: 287
        width: 34
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4100d71a2e11fde10800000000000000
      internalID: 2224516406013919252
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_10
      rect:
        serializedVersion: 2
        x: 255
        y: 0
        width: 225
        height: 337
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 31f3ce7ca856885d0800000000000000
      internalID: -3060084300064211181
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_11
      rect:
        serializedVersion: 2
        x: 319
        y: 287
        width: 34
        height: 66
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eb7e04a1302987f40800000000000000
      internalID: 5726487468224604094
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_12
      rect:
        serializedVersion: 2
        x: 0
        y: 143
        width: 135
        height: 139
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: be8e21746bfdb7260800000000000000
      internalID: 7096511611821811947
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_13
      rect:
        serializedVersion: 2
        x: 143
        y: 159
        width: 130
        height: 111
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c496381cc83803860800000000000000
      internalID: 7507645219391891788
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: env_rock_14
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 225
        height: 138
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fc9243d3445257be0800000000000000
      internalID: -1480235927506507313
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      env_rock_0: -8340873856085754300
      env_rock_1: 479919144530993615
      env_rock_10: -3060084300064211181
      env_rock_11: 5726487468224604094
      env_rock_12: 7096511611821811947
      env_rock_13: 7507645219391891788
      env_rock_14: -1480235927506507313
      env_rock_2: -6357495892027737555
      env_rock_3: -359003401882473920
      env_rock_4: 8239160857659893436
      env_rock_5: 4158498375409969417
      env_rock_6: -6675707502533136265
      env_rock_7: -7659465075636163285
      env_rock_8: -7112252247093738425
      env_rock_9: 2224516406013919252
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
