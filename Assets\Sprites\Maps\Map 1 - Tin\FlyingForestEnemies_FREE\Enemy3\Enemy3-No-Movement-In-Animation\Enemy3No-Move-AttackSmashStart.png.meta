fileFormatVersion: 2
guid: 97268601b32257840bd0c87f9dac9530
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 8800674282940891994
    second: Enemy3No-Move-AttackSmashStart_0
  - first:
      213: 7059174939733703851
    second: Enemy3No-Move-AttackSmashStart_1
  - first:
      213: 8808176892804712893
    second: Enemy3No-Move-AttackSmashStart_2
  - first:
      213: 7532257173051118999
    second: Enemy3No-Move-AttackSmashStart_3
  - first:
      213: 1356726567464808967
    second: Enemy3No-Move-AttackSmashStart_4
  - first:
      213: 5600212147655430360
    second: Enemy3No-Move-AttackSmashStart_5
  - first:
      213: 8837354227439260892
    second: Enemy3No-Move-AttackSmashStart_6
  - first:
      213: -7328728441424297576
    second: Enemy3No-Move-AttackSmashStart_7
  - first:
      213: 5170524242060383293
    second: Enemy3No-Move-AttackSmashStart_8
  - first:
      213: 7647064870438675075
    second: Enemy3No-Move-AttackSmashStart_9
  - first:
      213: -249057006195236874
    second: Enemy3No-Move-AttackSmashStart_10
  - first:
      213: -7754473731503768982
    second: Enemy3No-Move-AttackSmashStart_11
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_0
      rect:
        serializedVersion: 2
        x: 10
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a538b92e1a6422a70800000000000000
      internalID: 8800674282940891994
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_1
      rect:
        serializedVersion: 2
        x: 74
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bacba22e43a37f160800000000000000
      internalID: 7059174939733703851
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_2
      rect:
        serializedVersion: 2
        x: 138
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db9cd6e573eec3a70800000000000000
      internalID: 8808176892804712893
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_3
      rect:
        serializedVersion: 2
        x: 202
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7959994cdf3f78860800000000000000
      internalID: 7532257173051118999
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_4
      rect:
        serializedVersion: 2
        x: 266
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7023317ff9f04d210800000000000000
      internalID: 1356726567464808967
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_5
      rect:
        serializedVersion: 2
        x: 330
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8d054dfe643f7bd40800000000000000
      internalID: 5600212147655430360
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_6
      rect:
        serializedVersion: 2
        x: 394
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd450d8f8d694aa70800000000000000
      internalID: 8837354227439260892
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_7
      rect:
        serializedVersion: 2
        x: 458
        y: 6
        width: 43
        height: 55
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8914a91df402b4a90800000000000000
      internalID: -7328728441424297576
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_8
      rect:
        serializedVersion: 2
        x: 522
        y: 11
        width: 43
        height: 50
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d341e8e476461c740800000000000000
      internalID: 5170524242060383293
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_9
      rect:
        serializedVersion: 2
        x: 586
        y: 14
        width: 43
        height: 47
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 382add8dbf4df1a60800000000000000
      internalID: 7647064870438675075
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_10
      rect:
        serializedVersion: 2
        x: 650
        y: 16
        width: 43
        height: 44
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6ff19e0f8fb2b8cf0800000000000000
      internalID: -249057006195236874
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Enemy3No-Move-AttackSmashStart_11
      rect:
        serializedVersion: 2
        x: 714
        y: 17
        width: 43
        height: 43
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a6abe95e933926490800000000000000
      internalID: -7754473731503768982
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Enemy3No-Move-AttackSmashStart_0: 8800674282940891994
      Enemy3No-Move-AttackSmashStart_1: 7059174939733703851
      Enemy3No-Move-AttackSmashStart_10: -249057006195236874
      Enemy3No-Move-AttackSmashStart_11: -7754473731503768982
      Enemy3No-Move-AttackSmashStart_2: 8808176892804712893
      Enemy3No-Move-AttackSmashStart_3: 7532257173051118999
      Enemy3No-Move-AttackSmashStart_4: 1356726567464808967
      Enemy3No-Move-AttackSmashStart_5: 5600212147655430360
      Enemy3No-Move-AttackSmashStart_6: 8837354227439260892
      Enemy3No-Move-AttackSmashStart_7: -7328728441424297576
      Enemy3No-Move-AttackSmashStart_8: 5170524242060383293
      Enemy3No-Move-AttackSmashStart_9: 7647064870438675075
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
