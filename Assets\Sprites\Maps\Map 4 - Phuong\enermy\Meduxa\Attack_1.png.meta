fileFormatVersion: 2
guid: 28dc6b70d72d00a4db7adb4b8bbd23aa
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -8915327726919978405
    second: Attack_1_0
  - first:
      213: 4416717768442454902
    second: Attack_1_1
  - first:
      213: -8053095777402275025
    second: Attack_1_2
  - first:
      213: 4688295478533005478
    second: Attack_1_3
  - first:
      213: 3156989228384878200
    second: Attack_1_4
  - first:
      213: -4176445809780997858
    second: Attack_1_5
  - first:
      213: 9111255474114485651
    second: Attack_1_6
  - first:
      213: -6197699949570948299
    second: Attack_1_7
  - first:
      213: 5610570195965040114
    second: Attack_1_8
  - first:
      213: -4186573142144889428
    second: Attack_1_9
  - first:
      213: -6449648367012705641
    second: Attack_1_10
  - first:
      213: -9080296470528153068
    second: Attack_1_11
  - first:
      213: 549843493828548186
    second: Attack_1_12
  - first:
      213: -1949197173246352631
    second: Attack_1_13
  - first:
      213: 8661367325605615837
    second: Attack_1_14
  - first:
      213: 5279893943038032714
    second: Attack_1_15
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 16
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Attack_1_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dd8b665de6a871a42a8120b8e1bad5fc
      internalID: -1598670933
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_1
      rect:
        serializedVersion: 2
        x: 128
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b1f0e44a8c7a63446804f3b5833d89d9
      internalID: 1333435633
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_2
      rect:
        serializedVersion: 2
        x: 256
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 40747e2ca267a7849a5c25d2f8afd23b
      internalID: -1166242196
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_3
      rect:
        serializedVersion: 2
        x: 384
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ee03e27576e1b34a9ea0c456d550c72
      internalID: -484171120
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_4
      rect:
        serializedVersion: 2
        x: 512
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b07528bac6218b34790f3b44ba4b5d94
      internalID: -2018451589
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_5
      rect:
        serializedVersion: 2
        x: 640
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e666fcddd92c0bb47b348d60c3449f7e
      internalID: -1970079388
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_6
      rect:
        serializedVersion: 2
        x: 768
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6b82ce26a53082f4c838aff35021e1a3
      internalID: 1302014141
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_7
      rect:
        serializedVersion: 2
        x: 896
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ead3c8e2efebf3445896255b07b8e5a3
      internalID: -106862887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_8
      rect:
        serializedVersion: 2
        x: 1024
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 694f22a92b944db47afcea467a20fd10
      internalID: 143012088
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_9
      rect:
        serializedVersion: 2
        x: 1152
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 64d047b510c532a4ab51604101d55edc
      internalID: -1324725323
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_10
      rect:
        serializedVersion: 2
        x: 1280
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e278081d0f5920449bb2841e3aba49c8
      internalID: 657892296
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_11
      rect:
        serializedVersion: 2
        x: 1408
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: aed7fe0a31ef6014f8299ceb5f2f0513
      internalID: 377375615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_12
      rect:
        serializedVersion: 2
        x: 1536
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a27a77860b91518489153c1bff51cfaf
      internalID: -179080011
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_13
      rect:
        serializedVersion: 2
        x: 1664
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bec2843fd7e9dbb47b96a21de30702c9
      internalID: -899223545
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_14
      rect:
        serializedVersion: 2
        x: 1792
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f54b8f6ce55d3c142ad468b5bb907976
      internalID: -2075805900
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Attack_1_15
      rect:
        serializedVersion: 2
        x: 1920
        y: 0
        width: 128
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b97bf22584045ca41ac4cbfe15896d99
      internalID: -2017395245
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: ab4c6c8412f083447b108fabf2e9e4ca
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries:
      - key: SpriteEditor.SliceSettings
        value: '{"sliceOnImport":false,"gridCellCount":{"x":1.0,"y":1.0},"gridSpriteSize":{"x":128.0,"y":128.0},"gridSpriteOffset":{"x":0.0,"y":0.0},"gridSpritePadding":{"x":0.0,"y":0.0},"pivot":{"x":0.0,"y":0.0},"autoSlicingMethod":0,"spriteAlignment":0,"slicingType":1,"keepEmptyRects":false,"isAlternate":false}'
    nameFileIdTable:
      Attack_1_0: -1598670933
      Attack_1_1: 1333435633
      Attack_1_10: 657892296
      Attack_1_11: 377375615
      Attack_1_12: -179080011
      Attack_1_13: -899223545
      Attack_1_14: -2075805900
      Attack_1_15: -2017395245
      Attack_1_2: -1166242196
      Attack_1_3: -484171120
      Attack_1_4: -2018451589
      Attack_1_5: -1970079388
      Attack_1_6: 1302014141
      Attack_1_7: -106862887
      Attack_1_8: 143012088
      Attack_1_9: -1324725323
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
