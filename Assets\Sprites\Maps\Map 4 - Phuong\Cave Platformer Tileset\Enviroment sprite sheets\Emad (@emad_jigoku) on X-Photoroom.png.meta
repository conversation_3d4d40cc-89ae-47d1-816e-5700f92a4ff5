fileFormatVersion: 2
guid: 36bb5c467cbaab44ab04c7e35c50a9ed
TextureImporter:
  internalIDToNameTable:
  - first:
      213: 5172812051142438160
    second: Emad (@emad_jigoku) on X-Photoroom_0
  - first:
      213: 2209050866206881807
    second: Emad (@emad_jigoku) on X-Photoroom_1
  - first:
      213: 3727730894760390628
    second: Emad (@emad_jigoku) on X-Photoroom_2
  - first:
      213: 4562324336913268887
    second: Emad (@emad_jigoku) on X-Photoroom_3
  - first:
      213: -1249700974293785749
    second: Emad (@emad_jigoku) on X-Photoroom_4
  - first:
      213: 6174666395855084765
    second: Emad (@emad_jigoku) on X-Photoroom_5
  - first:
      213: 6719926440126806636
    second: Emad (@emad_jigoku) on X-Photoroom_6
  - first:
      213: 2796315375775201929
    second: Emad (@emad_jigoku) on X-Photoroom_7
  - first:
      213: 5171278457325605565
    second: Emad (@emad_jigoku) on X-Photoroom_8
  - first:
      213: -8909160581561897282
    second: Emad (@emad_jigoku) on X-Photoroom_9
  - first:
      213: -726196176990147410
    second: Emad (@emad_jigoku) on X-Photoroom_10
  - first:
      213: -7247453565802549923
    second: Emad (@emad_jigoku) on X-Photoroom_11
  - first:
      213: 1385333250426099258
    second: Emad (@emad_jigoku) on X-Photoroom_12
  - first:
      213: -3210699021426884445
    second: Emad (@emad_jigoku) on X-Photoroom_13
  - first:
      213: 6970563270505405453
    second: Emad (@emad_jigoku) on X-Photoroom_14
  - first:
      213: 4708266248068872371
    second: Emad (@emad_jigoku) on X-Photoroom_15
  - first:
      213: 7289071866919933256
    second: Emad (@emad_jigoku) on X-Photoroom_16
  - first:
      213: -3864332554440452656
    second: Emad (@emad_jigoku) on X-Photoroom_17
  - first:
      213: 6496817734650743667
    second: Emad (@emad_jigoku) on X-Photoroom_18
  - first:
      213: 7940780474692570611
    second: Emad (@emad_jigoku) on X-Photoroom_19
  - first:
      213: 4403326434378273819
    second: Emad (@emad_jigoku) on X-Photoroom_20
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_0
      rect:
        serializedVersion: 2
        x: 441
        y: 325
        width: 29
        height: 10
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 01d643e572589c740800000000000000
      internalID: 5172812051142438160
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_1
      rect:
        serializedVersion: 2
        x: 445
        y: 302
        width: 22
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f08b70b0f0028ae10800000000000000
      internalID: 2209050866206881807
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_2
      rect:
        serializedVersion: 2
        x: 448
        y: 245
        width: 38
        height: 112
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4ef21789f7f8bb330800000000000000
      internalID: 3727730894760390628
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_3
      rect:
        serializedVersion: 2
        x: 517
        y: 111
        width: 202
        height: 214
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 790c5dbefc1a05f30800000000000000
      internalID: 4562324336913268887
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_4
      rect:
        serializedVersion: 2
        x: 24
        y: 111
        width: 185
        height: 208
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6362c7c59b28aee0800000000000000
      internalID: -1249700974293785749
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_5
      rect:
        serializedVersion: 2
        x: 247
        y: 111
        width: 77
        height: 189
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ddcdb7b4f52d0b550800000000000000
      internalID: 6174666395855084765
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_6
      rect:
        serializedVersion: 2
        x: 441
        y: 296
        width: 29
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: c6a738c5e79f14d50800000000000000
      internalID: 6719926440126806636
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_7
      rect:
        serializedVersion: 2
        x: 78
        y: 25
        width: 99
        height: 87
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 98a3b0dc4028ec620800000000000000
      internalID: 2796315375775201929
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_8
      rect:
        serializedVersion: 2
        x: 231
        y: 25
        width: 99
        height: 87
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: db2d58bbb5214c740800000000000000
      internalID: 5171278457325605565
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_9
      rect:
        serializedVersion: 2
        x: 384
        y: 38
        width: 99
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ebe271167ad4c5480800000000000000
      internalID: -8909160581561897282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_10
      rect:
        serializedVersion: 2
        x: 393
        y: 112
        width: 81
        height: 159
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: eac6141bc680ce5f0800000000000000
      internalID: -726196176990147410
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_11
      rect:
        serializedVersion: 2
        x: 537
        y: 38
        width: 99
        height: 74
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d5d633c616fdb6b90800000000000000
      internalID: -7247453565802549923
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_12
      rect:
        serializedVersion: 2
        x: 571
        y: 111
        width: 8
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a365f2f5041b93310800000000000000
      internalID: 1385333250426099258
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_13
      rect:
        serializedVersion: 2
        x: 578
        y: 111
        width: 10
        height: 7
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3a00823404e4173d0800000000000000
      internalID: -3210699021426884445
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_14
      rect:
        serializedVersion: 2
        x: 587
        y: 111
        width: 36
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d0c1cd6ec5a6cb060800000000000000
      internalID: 6970563270505405453
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_15
      rect:
        serializedVersion: 2
        x: 58
        y: 9
        width: 139
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3b83b18053f175140800000000000000
      internalID: 4708266248068872371
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_16
      rect:
        serializedVersion: 2
        x: 211
        y: 9
        width: 139
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8455e0a6e3cf72560800000000000000
      internalID: 7289071866919933256
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_17
      rect:
        serializedVersion: 2
        x: 365
        y: 9
        width: 137
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 0d5599e04022f5ac0800000000000000
      internalID: -3864332554440452656
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_18
      rect:
        serializedVersion: 2
        x: 384
        y: 25
        width: 99
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 37bc05cde45592a50800000000000000
      internalID: 6496817734650743667
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_19
      rect:
        serializedVersion: 2
        x: 517
        y: 9
        width: 138
        height: 17
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 3fd886905c1533e60800000000000000
      internalID: 7940780474692570611
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: Emad (@emad_jigoku) on X-Photoroom_20
      rect:
        serializedVersion: 2
        x: 537
        y: 25
        width: 99
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b14543fef02cb1d30800000000000000
      internalID: 4403326434378273819
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      Emad (@emad_jigoku) on X-Photoroom_0: 5172812051142438160
      Emad (@emad_jigoku) on X-Photoroom_1: 2209050866206881807
      Emad (@emad_jigoku) on X-Photoroom_10: -726196176990147410
      Emad (@emad_jigoku) on X-Photoroom_11: -7247453565802549923
      Emad (@emad_jigoku) on X-Photoroom_12: 1385333250426099258
      Emad (@emad_jigoku) on X-Photoroom_13: -3210699021426884445
      Emad (@emad_jigoku) on X-Photoroom_14: 6970563270505405453
      Emad (@emad_jigoku) on X-Photoroom_15: 4708266248068872371
      Emad (@emad_jigoku) on X-Photoroom_16: 7289071866919933256
      Emad (@emad_jigoku) on X-Photoroom_17: -3864332554440452656
      Emad (@emad_jigoku) on X-Photoroom_18: 6496817734650743667
      Emad (@emad_jigoku) on X-Photoroom_19: 7940780474692570611
      Emad (@emad_jigoku) on X-Photoroom_2: 3727730894760390628
      Emad (@emad_jigoku) on X-Photoroom_20: 4403326434378273819
      Emad (@emad_jigoku) on X-Photoroom_3: 4562324336913268887
      Emad (@emad_jigoku) on X-Photoroom_4: -1249700974293785749
      Emad (@emad_jigoku) on X-Photoroom_5: 6174666395855084765
      Emad (@emad_jigoku) on X-Photoroom_6: 6719926440126806636
      Emad (@emad_jigoku) on X-Photoroom_7: 2796315375775201929
      Emad (@emad_jigoku) on X-Photoroom_8: 5171278457325605565
      Emad (@emad_jigoku) on X-Photoroom_9: -8909160581561897282
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
