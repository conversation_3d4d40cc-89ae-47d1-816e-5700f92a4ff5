fileFormatVersion: 2
guid: c53edaf14aede6a47a5851bda41a2169
TextureImporter:
  internalIDToNameTable:
  - first:
      213: -4219134444641109766
    second: top_far_bgrnd_0
  - first:
      213: 205935511540882859
    second: top_far_bgrnd_1
  - first:
      213: -9060194621009612625
    second: top_far_bgrnd_2
  - first:
      213: 1059145417291778130
    second: top_far_bgrnd_3
  - first:
      213: -6465862322680240322
    second: top_far_bgrnd_4
  - first:
      213: -3432996983011041322
    second: top_far_bgrnd_5
  - first:
      213: -2854476001591673416
    second: top_far_bgrnd_6
  - first:
      213: -4207758879949944457
    second: top_far_bgrnd_7
  - first:
      213: -8275856428696930068
    second: top_far_bgrnd_8
  - first:
      213: 8087404319364921757
    second: top_far_bgrnd_9
  - first:
      213: 2570461829416463968
    second: top_far_bgrnd_10
  - first:
      213: 4524187652852611590
    second: top_far_bgrnd_11
  - first:
      213: -8822852501580362321
    second: top_far_bgrnd_12
  - first:
      213: 4367753676916548321
    second: top_far_bgrnd_13
  - first:
      213: -3852186176301154771
    second: top_far_bgrnd_14
  - first:
      213: -6032273635458271609
    second: top_far_bgrnd_15
  - first:
      213: 124490051336152523
    second: top_far_bgrnd_16
  - first:
      213: -2058900458461974204
    second: top_far_bgrnd_17
  - first:
      213: -4671023715348445486
    second: top_far_bgrnd_18
  - first:
      213: -2628438497468609832
    second: top_far_bgrnd_19
  - first:
      213: -1892903526148099767
    second: top_far_bgrnd_20
  - first:
      213: 9174627449619117516
    second: top_far_bgrnd_21
  - first:
      213: 2357925768236427912
    second: top_far_bgrnd_22
  - first:
      213: -4670354921151043425
    second: top_far_bgrnd_23
  - first:
      213: -7568491288407996964
    second: top_far_bgrnd_24
  - first:
      213: 2025738066051852536
    second: top_far_bgrnd_25
  - first:
      213: -8613252962887750089
    second: top_far_bgrnd_26
  - first:
      213: 157614497707831767
    second: top_far_bgrnd_27
  - first:
      213: -126115063345916359
    second: top_far_bgrnd_28
  - first:
      213: 7256347733922951290
    second: top_far_bgrnd_29
  - first:
      213: -1876482865312234535
    second: top_far_bgrnd_30
  - first:
      213: 4240822733461071654
    second: top_far_bgrnd_31
  - first:
      213: -5225641987685477781
    second: top_far_bgrnd_32
  - first:
      213: -4771154427451717399
    second: top_far_bgrnd_33
  - first:
      213: 4734113638332272022
    second: top_far_bgrnd_34
  - first:
      213: -2510190975328784320
    second: top_far_bgrnd_35
  - first:
      213: 3993381386711799281
    second: top_far_bgrnd_36
  - first:
      213: 5664498465758658282
    second: top_far_bgrnd_37
  - first:
      213: 7200675447716852973
    second: top_far_bgrnd_38
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: top_far_bgrnd_0
      rect:
        serializedVersion: 2
        x: 0
        y: 751
        width: 593
        height: 98
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: af83d87939f9275c0800000000000000
      internalID: -4219134444641109766
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_1
      rect:
        serializedVersion: 2
        x: 19
        y: 849
        width: 602
        height: 173
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bad5804b141abd200800000000000000
      internalID: 205935511540882859
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_2
      rect:
        serializedVersion: 2
        x: 0
        y: 719
        width: 577
        height: 41
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fa031d2cef8b34280800000000000000
      internalID: -9060194621009612625
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_3
      rect:
        serializedVersion: 2
        x: 0
        y: 575
        width: 640
        height: 109
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 258c82b3527d2be00800000000000000
      internalID: 1059145417291778130
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_4
      rect:
        serializedVersion: 2
        x: 31
        y: 639
        width: 322
        height: 73
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: e37ef02a974a446a0800000000000000
      internalID: -6465862322680240322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_5
      rect:
        serializedVersion: 2
        x: 0
        y: 511
        width: 625
        height: 85
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6df93a8ce6b8b50d0800000000000000
      internalID: -3432996983011041322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_6
      rect:
        serializedVersion: 2
        x: 0
        y: 356
        width: 625
        height: 160
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8bd3041433dd268d0800000000000000
      internalID: -2854476001591673416
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_7
      rect:
        serializedVersion: 2
        x: 15
        y: 429
        width: 30
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7798614a7990b95c0800000000000000
      internalID: -4207758879949944457
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_8
      rect:
        serializedVersion: 2
        x: 22
        y: 415
        width: 82
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: ce8f26bec60462d80800000000000000
      internalID: -8275856428696930068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_9
      rect:
        serializedVersion: 2
        x: 161
        y: 415
        width: 30
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d99b4ba216b3c3070800000000000000
      internalID: 8087404319364921757
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_10
      rect:
        serializedVersion: 2
        x: 271
        y: 399
        width: 9
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 06ea11b3a6d1ca320800000000000000
      internalID: 2570461829416463968
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_11
      rect:
        serializedVersion: 2
        x: 312
        y: 409
        width: 10
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 60afc7933b429ce30800000000000000
      internalID: 4524187652852611590
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_12
      rect:
        serializedVersion: 2
        x: 312
        y: 406
        width: 7
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: fadc2a1356eee8580800000000000000
      internalID: -8822852501580362321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_13
      rect:
        serializedVersion: 2
        x: 321
        y: 406
        width: 9
        height: 31
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1e2640be3d06d9c30800000000000000
      internalID: 4367753676916548321
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_14
      rect:
        serializedVersion: 2
        x: 56
        y: 384
        width: 82
        height: 29
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: d2e36e815194a8ac0800000000000000
      internalID: -3852186176301154771
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_15
      rect:
        serializedVersion: 2
        x: 115
        y: 398
        width: 30
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7863e82c61f094ca0800000000000000
      internalID: -6032273635458271609
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_16
      rect:
        serializedVersion: 2
        x: 255
        y: 399
        width: 7
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: bc526f7ca074ab100800000000000000
      internalID: 124490051336152523
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_17
      rect:
        serializedVersion: 2
        x: 279
        y: 402
        width: 10
        height: 25
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 4413c7901f05d63e0800000000000000
      internalID: -2058900458461974204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_18
      rect:
        serializedVersion: 2
        x: 282
        y: 399
        width: 7
        height: 23
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 2d60b35dda03d2fb0800000000000000
      internalID: -4671023715348445486
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_19
      rect:
        serializedVersion: 2
        x: 339
        y: 406
        width: 7
        height: 18
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8da0c64dc19e58bd0800000000000000
      internalID: -2628438497468609832
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_20
      rect:
        serializedVersion: 2
        x: 15
        y: 362
        width: 34
        height: 11
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 941bbd1a54e0bb5e0800000000000000
      internalID: -1892903526148099767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_21
      rect:
        serializedVersion: 2
        x: 63
        y: 362
        width: 34
        height: 21
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cc578116e03d25f70800000000000000
      internalID: 9174627449619117516
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_22
      rect:
        serializedVersion: 2
        x: 179
        y: 367
        width: 6
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 88ae87e4ef809b020800000000000000
      internalID: 2357925768236427912
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_23
      rect:
        serializedVersion: 2
        x: 303
        y: 356
        width: 6
        height: 32
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: f986bcd91f09f2fb0800000000000000
      internalID: -4670354921151043425
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_24
      rect:
        serializedVersion: 2
        x: 337
        y: 367
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: cd58dc4d54157f690800000000000000
      internalID: -7568491288407996964
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_25
      rect:
        serializedVersion: 2
        x: 352
        y: 367
        width: 17
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 8f8e10bc90edc1c10800000000000000
      internalID: 2025738066051852536
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_26
      rect:
        serializedVersion: 2
        x: 370
        y: 367
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 73eae72b014977880800000000000000
      internalID: -8613252962887750089
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_27
      rect:
        serializedVersion: 2
        x: 385
        y: 367
        width: 15
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 7d1b353fc85ff2200800000000000000
      internalID: 157614497707831767
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_28
      rect:
        serializedVersion: 2
        x: 409
        y: 367
        width: 15
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 936d070a403ff3ef0800000000000000
      internalID: -126115063345916359
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_29
      rect:
        serializedVersion: 2
        x: 443
        y: 367
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: a70303771d9b3b460800000000000000
      internalID: 7256347733922951290
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_30
      rect:
        serializedVersion: 2
        x: 471
        y: 367
        width: 17
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9d305f9a6c465f5e0800000000000000
      internalID: -1876482865312234535
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_31
      rect:
        serializedVersion: 2
        x: 505
        y: 367
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 6272957aecd6ada30800000000000000
      internalID: 4240822733461071654
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_32
      rect:
        serializedVersion: 2
        x: 545
        y: 362
        width: 15
        height: 14
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: b6214a08c4aca77b0800000000000000
      internalID: -5225641987685477781
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_33
      rect:
        serializedVersion: 2
        x: 563
        y: 362
        width: 12
        height: 13
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 9e8453fa35479cdb0800000000000000
      internalID: -4771154427451717399
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_34
      rect:
        serializedVersion: 2
        x: 576
        y: 362
        width: 17
        height: 22
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 69d802b8543f2b140800000000000000
      internalID: 4734113638332272022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_35
      rect:
        serializedVersion: 2
        x: 593
        y: 362
        width: 15
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 040f2258b920a2dd0800000000000000
      internalID: -2510190975328784320
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_36
      rect:
        serializedVersion: 2
        x: 14
        y: 214
        width: 579
        height: 128
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: 1f51d9f96375b6730800000000000000
      internalID: 3993381386711799281
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_37
      rect:
        serializedVersion: 2
        x: 0
        y: 36
        width: 625
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: aee3a42c7575c9e40800000000000000
      internalID: 5664498465758658282
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: top_far_bgrnd_38
      rect:
        serializedVersion: 2
        x: 15
        y: 100
        width: 625
        height: 80
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: -1
      bones: []
      spriteID: de43a0c3b20fde360800000000000000
      internalID: 7200675447716852973
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: 
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      top_far_bgrnd_0: -4219134444641109766
      top_far_bgrnd_1: 205935511540882859
      top_far_bgrnd_10: 2570461829416463968
      top_far_bgrnd_11: 4524187652852611590
      top_far_bgrnd_12: -8822852501580362321
      top_far_bgrnd_13: 4367753676916548321
      top_far_bgrnd_14: -3852186176301154771
      top_far_bgrnd_15: -6032273635458271609
      top_far_bgrnd_16: 124490051336152523
      top_far_bgrnd_17: -2058900458461974204
      top_far_bgrnd_18: -4671023715348445486
      top_far_bgrnd_19: -2628438497468609832
      top_far_bgrnd_2: -9060194621009612625
      top_far_bgrnd_20: -1892903526148099767
      top_far_bgrnd_21: 9174627449619117516
      top_far_bgrnd_22: 2357925768236427912
      top_far_bgrnd_23: -4670354921151043425
      top_far_bgrnd_24: -7568491288407996964
      top_far_bgrnd_25: 2025738066051852536
      top_far_bgrnd_26: -8613252962887750089
      top_far_bgrnd_27: 157614497707831767
      top_far_bgrnd_28: -126115063345916359
      top_far_bgrnd_29: 7256347733922951290
      top_far_bgrnd_3: 1059145417291778130
      top_far_bgrnd_30: -1876482865312234535
      top_far_bgrnd_31: 4240822733461071654
      top_far_bgrnd_32: -5225641987685477781
      top_far_bgrnd_33: -4771154427451717399
      top_far_bgrnd_34: 4734113638332272022
      top_far_bgrnd_35: -2510190975328784320
      top_far_bgrnd_36: 3993381386711799281
      top_far_bgrnd_37: 5664498465758658282
      top_far_bgrnd_38: 7200675447716852973
      top_far_bgrnd_4: -6465862322680240322
      top_far_bgrnd_5: -3432996983011041322
      top_far_bgrnd_6: -2854476001591673416
      top_far_bgrnd_7: -4207758879949944457
      top_far_bgrnd_8: -8275856428696930068
      top_far_bgrnd_9: 8087404319364921757
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
